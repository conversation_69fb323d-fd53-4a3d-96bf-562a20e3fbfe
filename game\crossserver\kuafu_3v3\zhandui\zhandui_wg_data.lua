---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by 123.
--- DateTime: 2019/10/19 18:12
---

local zhandui_zhanling_text_type = {
	[27568] = {color = COLOR3B.WHITE, outline = "#285f27"},
	[27569] = {color = COLOR3B.WHITE, outline = "#12346b"},
	[27570] = {color = COLOR3B.WHITE, outline = "#092654"},
	[27571] = {color = COLOR3B.WHITE, outline = "#38126a"},
	[27572] = {color = COLOR3B.WHITE, outline = "#61260d"},
	[27573] = {color = COLOR3B.WHITE, outline = "#9d1a1a"},
	[27574] = {color = COLOR3B.WHITE, outline = "#ad5824"},
	[27575] = {color = COLOR3B.WHITE, outline = "#b5391f"},
	[27576] = {color = COLOR3B.WHITE, outline = "#b5391f"},
}

ZhanDuiWGData = ZhanDuiWGData or BaseClass()
function ZhanDuiWGData:__init()
    if nil ~= ZhanDuiWGData.Instance then
		ErrorLog("[ZhanDuiWGData]:Attempt to create singleton twice!")
	end
	ZhanDuiWGData.Instance = self

	--唯一日志Id
	self.log_guid = 0
	--日志列表
	self.log_list = {}
	--日志消息格子高度
	self.log_record_height = {}
	--是否有战队
	self.is_in_zhandui = 0
	--是否是战队队长
	self.is_zhandui_captain = 0
	--当前战队信息
	self:InitZhanDuiData()
	--战队申请列表
	self.zhandui_apply_list = {}
	--战队被邀请列表
	self.zhandui_be_invite_list = {}
	--战队战令新解锁列表与已解锁战令列表
	self.zhanling_new_list = {}
	self.zhanling_unlock_list = {}
end

function ZhanDuiWGData:InitZhanDuiData()
	self.zhandui_info = {
		member_list = {},
		score = 0,
		name = "",
		member_count = 0,
		captain_name = "",
		captain_uuid = 0,
		capability = 0,
	}
end

function ZhanDuiWGData:__delete()
	ZhanDuiWGData.Instance = nil
	self:ClearLog()
	self:ClearZhanDuiData()
	self:ClearApply()
	self:ClearBeInvite()
	self.zhandui_item_count = nil
	self.zhandui_list = nil
end

--获取日志Id
function ZhanDuiWGData:GetLogId()
	self.log_guid = self.log_guid + 1
	return self.log_guid
end

--创建一条日志
function ZhanDuiWGData:CreateLogItem()
	local log_item = {}
	log_item.log_id = self:GetLogId()
	log_item.content = ""
	log_item.timestamp = 0
	return log_item
end

function ZhanDuiWGData:CreateZhanduiAutoApplyIdList()
	local list = {}
	for i = 1, 10 do
		list[i] = MsgAdapter.InitUUID()
	end
	return list
end

--设置个人信息
function ZhanDuiWGData:SetRoleInfo(protocol)
	self.role_info = {}
	self.role_info.zhandui_id = protocol.zhandui_id 										-- 战队ID
	self.role_info.zhandui_socre = protocol.zhandui_socre									-- 战队积分
	self.role_info.active_leave_zhandui_time = protocol.active_leave_zhandui_time			-- 主动离开战队的时间
	self.role_info.today_match_times = protocol.today_match_times							-- 今日参与比赛次数
	self.role_info.match_times_reward_fetch_flag = protocol.match_times_reward_fetch_flag	-- 阶段奖励领取标记
	self.role_info.season_continue_win_times = protocol.season_continue_win_times			-- 赛季连胜次数
	self.role_info.season_win_times = protocol.season_win_times								-- 赛季胜利次数
	self.role_info.season_match_times = protocol.season_match_times							-- 赛季比赛次数
	self.role_info.season_rank_reward_time = protocol.season_rank_reward_time 				-- 赛季奖励发放时间
	self.role_info.season_start_time = protocol.season_start_time 							-- 赛季开启时间
end

function ZhanDuiWGData:GetRoleInfo()
	return self.role_info
end
function ZhanDuiWGData:GetFetchFlagList()
	if not self.role_info then
		return nil
	end
	local tab = bit:d2b(self.role_info.match_times_reward_fetch_flag)
	return tab
end



--设置当前战队信息
function ZhanDuiWGData:SetZhanDuiInfo(protocol, member_list)
	self.zhandui_info.zhandui_id = protocol.zhandui_id												--战队id
	self.zhandui_info.name = protocol.name															--战队名字
	self.zhandui_info.score = protocol.score														--战队积分
	self.zhandui_info.member_count = protocol.member_count											--战队人数
	self.zhandui_info.captain_name = protocol.captain_name											--队长名字
	self.zhandui_info.captain_uuid = protocol.captain_uuid											--队长uuid
	self.zhandui_info.capability = protocol.capability												--战队战力
	self.zhandui_info.notice = protocol.notice														--战队公告
	self.zhandui_info.season_win_times = protocol.season_win_times									--赛季总胜利场次
	self.zhandui_info.season_match_times = protocol.season_match_times								--赛季总参与场次
	self.zhandui_info.last_match_time = protocol.last_match_time									--上一次PK时间
	self.zhandui_info.zhandui_lingpai_id = protocol.zhandui_lingpai_id								--当前选用的战队令牌ID
	self.zhandui_info.season_max_rank = protocol.season_max_rank 									--当前赛季最高历史排名
	self.zhandui_info.actived_zhandui_lingpai_id_flag = protocol.actived_zhandui_lingpai_id_flag	--已经激活的战队令牌ID标记
	self.zhandui_info.zhandui_lingpai_name = protocol.zhandui_lingpai_name


	RoleWGData.Instance:SetAttr("zhandui3v3_id", self.zhandui_info.zhandui_id)
	RoleWGData.Instance:SetAttr("zhandui3v3_name", self.zhandui_info.name)
	RoleWGData.Instance:SetAttr("zhandui3v3_lingpai_id", self.zhandui_info.zhandui_lingpai_id)
	RoleWGData.Instance:SetAttr("zhandui3v3_lingpai_name", self.zhandui_info.zhandui_lingpai_name)

	--local zhandui_info = {}
	--zhandui_info.zhandui3v3_id = self.zhandui_info.zhandui_id
	--zhandui_info.zhandui3v3_name = self.zhandui_info.name
	--zhandui_info.zhandui3v3_lingpai_id = self.zhandui_info.zhandui_lingpai_id
	--zhandui_info.zhandui3v3_lingpai_name = self.zhandui_info.zhandui_lingpai_name
	--RoleWGData.Instance:SetAttr("zhandui_info", zhandui_info)

	self.zhandui_info.member_list = {}
	local my_id = RoleWGData.Instance:InCrossGetOriginUid()
	--local my_uuid = RoleWGData.Instance:GetUUid()
	self.is_zhandui_captain = 0         --队长标记
	--for i = 1, self.zhandui_info.member_count do
	--	if member_list[i].captain_uuid == my_uuid then
	--		self.is_zhandui_captain = 1
	--	end
	--end

    for i, v in ipairs(member_list) do
        if v.uuid > 0 then
			if v.uid == my_id then
				self.is_in_zhandui = 1

			end
			if v.uid == protocol.captain_uid then
				v.is_zhandui_captain = 1
				if v.uid == my_id then
					self.is_zhandui_captain = 1
				end
			else
				v.is_zhandui_captain = 0
			end
            table.insert(self.zhandui_info.member_list, v)
        end
    end
	table.sort(self.zhandui_info.member_list, SortTools.KeyUpperSorters("is_zhandui_captain"))
	RemindManager.Instance:Fire(RemindName.JoinZhanDui)
end

--获取当前战队信息
function ZhanDuiWGData:GetZhanDuiInfo()
	return self.zhandui_info
end

--设置战队列表
function ZhanDuiWGData:SetZhanDuiInfoList(protocol)
	self.zhandui_item_count = protocol.zhandui_item_count
	self.zhandui_list = protocol.zhandui_item_list
end

--设置已申请战队列表
function ZhanDuiWGData:SetAppliedZhanDuiInfoList(protocol)
	self.applied_zhandui_id_list = protocol.zhandui_id_list
	self.applied_zhandui_id_map = {}
	for i, v in ipairs(self.applied_zhandui_id_list) do
		self.applied_zhandui_id_map[v.temp_low + v.temp_high] = true
	end
end

function ZhanDuiWGData:GetIsAppliedZhanDui(zhandui_id)
	if not self.applied_zhandui_id_map then
		return false
	end
	return self.applied_zhandui_id_map[zhandui_id.temp_low + zhandui_id.temp_high] or false
end

--获取所有战队列表
function ZhanDuiWGData:GetZhanDuiInfoList()
	return self.zhandui_list
end

--获取人数小于3的战队列表
function ZhanDuiWGData:GetZhanDuiNotFullInfoList(sort_condition)
	local list = {}
	if not self.zhandui_list then return list end
	for i, v in ipairs(self.zhandui_list) do
		if v.member_count < COMMON_CONSTS.ZHAN_DUI_MAX_MEMBER_COUNT then
			table.insert(list, v)
		end
	end
	if sort_condition == ZHANDUI_LIST_SORT_CONDITION.UP_CAPABILITY then --战力从高到低
		table.sort(list, SortTools.KeyUpperSorters("capability"))
	elseif sort_condition == ZHANDUI_LIST_SORT_CONDITION.UP_SOCRE then --积分从高到低
		table.sort(list, SortTools.KeyUpperSorters("score"))
	end
	return list
end

--清理战队数据
function ZhanDuiWGData:ClearZhanDuiData()
	self:InitZhanDuiData()
	self.is_in_zhandui = 0
	self.is_zhandui_captain = 0
	ChatWGData.Instance:RemoveGetChannel(CHANNEL_TYPE.ZHANDUI3V3)
    RemindManager.Instance:Fire(RemindName.JoinZhanDui)
end




---------------------------战队日志---------------------------------start
--设置日志列表
function ZhanDuiWGData:SetAllLogList(protocol)
	self:ClearLog()
	local log_list = protocol.log_list
	table.sort(log_list, SortTools.KeyLowerSorter("timestamp"))
	for i, v in ipairs(log_list) do
		if v.timestamp > 0 then
			self:AddLog(v)
		end
	end
end

--添加日志
function ZhanDuiWGData:AddLog(protocol)
	local log_item = self:CreateLogItem()
	log_item.content = protocol.content
	log_item.timestamp = protocol.timestamp
	table.insert(self.log_list, 1, log_item)
	if #self.log_list > COMMON_CONSTS.ZHAN_DUI_MAX_LOG_COUNT then
		table.remove(self.log_list, #self.log_list)
		--self:RecordLogHeight(log_id, height)
	end
end

--获取日志列表
function ZhanDuiWGData:GetLogList()
  	return self.log_list
end

--获取日志列表
function ZhanDuiWGData:GetLogById(log_id)
	return self.log_list
end

--清空日志
function ZhanDuiWGData:ClearLog()
	self.log_guid = 0
	self.log_list = {}
	self.log_record_height = {}
end

--记录日志格子高度，避免重复计算
function ZhanDuiWGData:RecordLogHeight(log_id, height)
	self.log_record_height[log_id] = height
end

--获取日志格子高度
function ZhanDuiWGData:GetRecordLogHeight(log_id)
	return self.log_record_height[log_id]
end

---------------------------战队日志---------------------------------end




---------------------------战队申请列表-----------------------------start
--设置申请列表
function ZhanDuiWGData:SetApplyInfoList(protocol)
	self:ClearApply()
	for i, v in ipairs(protocol.apply_item_list) do
		self:AddApplyInfo(v)
	end
end

--获取申请列表
function ZhanDuiWGData:GetApplyList()
	return self.zhandui_apply_list
end

--获取申请数量
function ZhanDuiWGData:GetApplyCount()
	return #self.zhandui_apply_list
end


--添加申请消息
function ZhanDuiWGData:AddApplyInfo(protocol)
	local apply_item = {}
	apply_item.uid = protocol.uid
	apply_item.uuid = protocol.uuid
	apply_item.name = protocol.name
	apply_item.apply_time = protocol.apply_time
	apply_item.prof = protocol.prof
	apply_item.vip_level = protocol.vip_level
	apply_item.photoframe = protocol.photoframe
	apply_item.capability = protocol.capability
	apply_item.avatar_key_big = protocol.avatar_key_big
	apply_item.avatar_key_small = protocol.avatar_key_small
	apply_item.level = protocol.level
	apply_item.sex = protocol.sex
	apply_item.shield_vip_flag = protocol.shield_vip_flag
	AvatarManager.Instance:SetAvatarKey(apply_item.uid, apply_item.avatar_key_big, apply_item.avatar_key_small)
	table.insert(self.zhandui_apply_list, apply_item)
	if #self.zhandui_apply_list > COMMON_CONSTS.ZHAN_DUI_MAX_APPLY_COUNT then
		table.remove(self.zhandui_apply_list, 1)
	end
end

--移除申请消息
function ZhanDuiWGData:RemoveApply(uid)
	local remove_success = false
	for i, v in ipairs(self.zhandui_apply_list) do
		if v.uid == uid then
			table.remove(self.zhandui_apply_list, i)
			remove_success = true
			break
		end
	end
	return remove_success
end

--清空申请消息
function ZhanDuiWGData:ClearApply()
	self.zhandui_apply_list = {}
end
---------------------------战队申请列表-----------------------------end





---------------------------战队被邀请列表-----------------------------start
--设置被邀请列表
function ZhanDuiWGData:SetBeInviteInfoList(protocol)
	self:ClearBeInvite()
	for i, v in ipairs(protocol.be_invite_list) do
		self:AddBeInviteInfo(v)
	end
end

--获取被邀请列表
function ZhanDuiWGData:GetBeInviteList()
	return self.zhandui_be_invite_list
end

--获取被邀请数量
function ZhanDuiWGData:GetBeInviteCount()
	return #self.zhandui_be_invite_list
end


--添加被邀请消息
function ZhanDuiWGData:AddBeInviteInfo(protocol)
	local be_invite_item = protocol
	--存在该战队的邀请消息，就移除
	for i, v in ipairs(self.zhandui_be_invite_list) do
		if v.zhandui_id == be_invite_item.zhandui_id then
			table.remove(self.zhandui_be_invite_list, i)
			break
		end
	end
	table.insert(self.zhandui_be_invite_list, be_invite_item)
	if #self.zhandui_be_invite_list > COMMON_CONSTS.ZHAN_DUI_MAX_APPLY_COUNT then
		table.remove(self.zhandui_be_invite_list, 1)
	end
end

--移除被邀请消息
function ZhanDuiWGData:RemoveBeInvite(uuid)
	local remove_success = false
	for i, v in ipairs(self.zhandui_be_invite_list) do
		if v.uuid == uuid then
			table.remove(self.zhandui_be_invite_list, i)
			remove_success = true
			break
		end
	end
	return remove_success
end

--清空被邀请消息
function ZhanDuiWGData:ClearBeInvite()
	self.zhandui_be_invite_list = {}
end
---------------------------战队申请列表-----------------------------end

--添加新战令标记
function ZhanDuiWGData:AddNewZhanLingFlag(zhanling_id)
	self:RemoveNewZhanLingFlag(zhanling_id)
	table.insert(self.zhanling_new_list, zhanling_id)
end

--移除新战令标记
function ZhanDuiWGData:RemoveNewZhanLingFlag(zhanling_id)
	local is_success = false
	for i,v in ipairs(self.zhanling_new_list) do
		if v == zhanling_id then
			table.remove(self.zhanling_new_list, i)
			is_success = true
			break
		end
	end
	return is_success
end

--获取新战令标记 true:新的
function ZhanDuiWGData:GetNewZhanLingFlag(zhanling_id)
	for i,v in ipairs(self.zhanling_new_list) do
		if v == zhanling_id then
			return true
		end
	end
	return false
end

--添加已获取战令标记
function ZhanDuiWGData:AddUnlockZhanLingFlag(zhanling_id)
	self.zhanling_unlock_list = bit:d2b(zhanling_id)
end

--战令是否激活
function ZhanDuiWGData:GetIsParticipationBySeq(seq)
	return self.zhanling_unlock_list[32 - seq] == 1 and true or false
end

---------------------------其他系统可调用---------------------------start
--是否有战队
function ZhanDuiWGData:GetIsInZhanDui()
	return self.is_in_zhandui == 1
end

--是否战队队长
function ZhanDuiWGData:GetIsZhanDuiCaptain()
	return self.is_zhandui_captain == 1
end

--获取战队成员列表
function ZhanDuiWGData:GetZhanDuiMemberList()
	return self.zhandui_info.member_list
end

--获取战队除自己外的成员列表
function ZhanDuiWGData:GetZhanDuiOtherMemberList()
	local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
	local ret_list = {}
	for i, v in ipairs(self.zhandui_info.member_list) do
		if v.uid > 0 and my_uid ~= v.uid then
			table.insert(ret_list, v)
		end
	end
	local function get_state (v)
		local scene_id = Scene.Instance:GetSceneId()
		local state = 0
		if not IsEmptyTable(v) then
			if v.scene_id == 0 then
				state = 0
			elseif v.scene_id == scene_id then
				state = 2
			elseif v.scene_id ~= scene_id then
				state = 1
			end
		end
		return state
	end
	table.sort( ret_list,function (a,b)
		local order_a = get_state(a)
		local order_b = get_state(b)
		return order_a > order_b
	end )
	return ret_list
end

--获取战队成员个数
function ZhanDuiWGData:GetZhanDuiMemberCount()
	return self.zhandui_info.member_count
end

--获取目标是否是自己战队成员 role_id：目标role_id
function ZhanDuiWGData:GetTargetIsMyZhanDui(role_id)
	for i, v in ipairs(self.zhandui_info.member_list) do
		if v.uid > 0 and role_id == v.uid then
			return true
		end
	end
	return false
end

--获取战令图标路径统一接口
function ZhanDuiWGData:GetZhanLingResPath(zhanling_icon)
	return ResPath.GetNoPackPNG(zhanling_icon)
end
--获取战队图标
function ZhanDuiWGData:GetZhanDuiResPath(zhanling_icon)
	return ResPath.GetF2Field1v1(zhanling_icon)
end

--获取战队旗帜图标
function ZhanDuiWGData:GetZhanDuiHuiZhangResPath(zhanling_icon)
	return ResPath.GetNoPackPNG(zhanling_icon)
end

-- 缓存邀请人员的cd列表
function ZhanDuiWGData:AddCacheCDList(role_id, time)
    if not self.cache_invite_cd_list then
        self.cache_invite_cd_list = {}
    end
    if CountDownManager.Instance:HasCountDown("new_zhandui_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("new_zhandui_invite_cd_time")
    end
    CountDownManager.Instance:AddCountDown("new_zhandui_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
    BindTool.Bind(self.CheckInviteTime, self), 40 + TimeWGCtrl.Instance:GetServerTime())
    time = time or 30
    self.cache_invite_cd_list[role_id] = time
    ZhanDuiWGCtrl.Instance:FlushTextInvite()
end

function ZhanDuiWGData:GetCacheCDByRoleid(role_id)
    if not self.cache_invite_cd_list then
        return 0
    end
    return self.cache_invite_cd_list[role_id] or 0
end

function ZhanDuiWGData:CheckInviteTime()
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("new_zhandui_invite_cd_time")
    else
        CountDownManager.Instance:AddCountDown("new_zhandui_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
        BindTool.Bind(self.CheckInviteTime,self), 40 + TimeWGCtrl.Instance:GetServerTime())
    end
end

-- 缓存邀请人员的cd列表
function ZhanDuiWGData:UpdateInviteTime(elapse_time, total_time)
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("new_zhandui_invite_cd_time")
        return
    end

    for k, v in pairs(self.cache_invite_cd_list) do
        self.cache_invite_cd_list[k] = v - 1
        if v <= 0 then
            self.cache_invite_cd_list[k] = nil
            table.remove(self.cache_invite_cd_list, k)
        end
        ZhanDuiWGCtrl.Instance:FlushTextInvite()
    end
end

function ZhanDuiWGData:GetZhanLingTextOutLine(zhanling_id)
	return zhandui_zhanling_text_type[zhanling_id]
end

---------------------------其他系统可调用---------------------------end