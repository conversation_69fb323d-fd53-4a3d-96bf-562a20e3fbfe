TeamBootyBayFBView = TeamBootyBayFBView or BaseClass(SafeBaseView)

function TeamBootyBayFBView:__init()
	self:AddViewResource(0, "uis/view/bootybay_ui_prefab", "layout_bootybay_fb_follow_view")
	self.view_layer = UiLayer.MainUILow
	self.active_close = false
end

function TeamBootyBayFBView:__delete()
end

function TeamBootyBayFBView:LoadCallBack()
	self.obj = self.node_list["panel"].gameObject
	self.obj:SetActive(true)

	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
	local reward_list = other_cfg.zuduireward
	if self.boody_item_list == nil then
		self.boody_item_list = AsyncListView.New(BoodyCellRender,self.node_list["reward_list"])
	end

	self.data = {}
	for k,v in pairs(reward_list) do
		local cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		local data = {}
		data.item_id = v.item_id
		data.num = v.num or 1
		data.is_bind = v.is_bind or 0
		data.sort_index1 = cfg.color
		data.sort_index2 = 1000 - k
		table.insert(self.data,data)
	end
	table.sort(self.data,SortTools.KeyUpperSorters("sort_index1","sort_index2"))
	self.node_list.guaji_btn.button:AddClickListener(BindTool.Bind(self.OnClickGuaJiBtn,self))
end

-- 切换标签调用
function TeamBootyBayFBView:ShowIndexCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil,function ()
		MainuiWGCtrl.Instance:GetTaskMaskRootNode(function ()
			local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
			if self.obj then
				self.obj.transform:SetParent(parent.gameObject.transform, false)
			end
		end)
	end)
	
	self:Flush()
	self.boody_item_list:SetDataList(self.data)
end

function TeamBootyBayFBView:ReleaseCallBack()
	if self.obj then
		ResMgr:Destroy(self.obj)
		self.obj = nil
	end

	if self.reward_list then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end

	if self.boody_item_list then
		self.boody_item_list:DeleteMe()
		self.boody_item_list = nil
	end
end

function TeamBootyBayFBView:OnFlush()
	local kill_boss_count = BootyBayWGData.Instance:GetTeamKillBossNum()
	local cur_monster_count = BootyBayWGData.Instance:GetTeamCurMonsterCount()
	self.node_list.task_desc2:SetActive(true)
	self.node_list.task_desc.text.text = string.format(Language.BootyBay.TeamFBTaskDesc,cur_monster_count)
	self.node_list.task_desc2.text.text = string.format(Language.BootyBay.TeamFBTaskDesc2,kill_boss_count )
end

-- 关闭前调用
function TeamBootyBayFBView:CloseCallBack()
	if self.obj then
		self.obj:SetActive(false)
	end
end

function TeamBootyBayFBView:OnClickGuaJiBtn()
	local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
	if not guaji_state then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)  --挂机
	end
end

BoodyCellRender = BoodyCellRender or BaseClass(BaseRender)
function BoodyCellRender:__init()
	
end

function BoodyCellRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function BoodyCellRender:OnFlush()
	if nil == self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell)
	end

	self.item_cell:SetData(self.data)
end

