-- 国家版图-活动
CountryMapActView = CountryMapActView or BaseClass(SafeBaseView)
function CountryMapActView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:SetMaskBg()

	local bundle_name = "uis/view/country_map_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, bundle_name, "layout_country_map_panel_down")
	self:AddViewResource(0, common_bundle_name, "layout_a2_common_top_panel")
	self:AddViewResource(TabIndex.country_map_secret_area, "uis/view/country_map_ui/secret_area_prefab",
		"layout_secret_area")                                                                                               -- 秘境
	self:AddViewResource(TabIndex.country_map_yanglongsi, "uis/view/country_map_ui/yanglonsi_ui_prefab",
		"layout_yanglongsi")                                                                                                -- 养龙寺
	self:AddViewResource(0, bundle_name, "VerticalTabbar2")

	self.default_index = TabIndex.country_map_secret_area

	self.remind_tab = {
		{ RemindName.CountryMapActSecret },
		{ RemindName.CrossYangLongSi },
	}

	self.hide_rule_btn_index = {
		[TabIndex.country_map_secret_area] = true,
	}

	self.raw_bg = {
		[TabIndex.country_map_secret_area] = "a2_xtmj_bj",
		[TabIndex.country_map_yanglongsi] = "a2_smhy_bg",
	}
end

function CountryMapActView:__delete()
end

function CountryMapActView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:ReleaseSecret()
	self:ReleaseYangLongSi()
end

function CountryMapActView:OpenCallBack()
end

function CountryMapActView:CloseCallBack()
end

function CountryMapActView:LoadCallBack()
	self:CreateToggleList()
end

function CountryMapActView:LoadIndexCallBack(index)
	if index == TabIndex.country_map_secret_area then
		self:LoadIndexCallBackSecret()
	elseif index == TabIndex.country_map_yanglongsi then
		self:LoadIndexCallBackYangLongSi()
	end
end

function CountryMapActView:ShowIndexCallBack(index)
	if index == TabIndex.country_map_secret_area then
		self.node_list.title_view_name.text.text = Language.CountryMap.HunTingViewTitle
		self:ShowIndexCallBackSecret()
	elseif index == TabIndex.country_map_yanglongsi then
		self.node_list.title_view_name.text.text = Language.CountryMap.YangLongViewTitle
	end

	local bg_res = self.raw_bg[index]
	--self.node_list["raw_xunbao_bg"].raw_image:LoadSpriteAsync(ResPath.GetF2RawImagesPNG(bg_res))
	self.node_list["raw_xunbao_bg"].raw_image:LoadSpriteAsync(ResPath.GetRawImagesPNG(bg_res))
end

function CountryMapActView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.country_map_secret_area then
				self:OnFlushSecret(param_t, index)
			elseif index == TabIndex.country_map_yanglongsi then
				self:OnFlushYangLongSi(param_t, index)
			end
		end
	end
end

function CountryMapActView:CreateToggleList()
	if nil == self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar2")
		self.tabbar:SetVerTabbarIconStr("country_map_act_icon")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell2")
		self.tabbar:Init(Language.CountryMap.ActTabGrop, nil, "uis/view/country_map_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.CountryMapActView, self.tabbar)
end

function CountryMapActView:CreatMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_gongyu = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)
	end
end

function CountryMapActView:OnClickTip()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.CountryMap.MapNewsTipsTitle)
	rule_tip:SetContent(Language.CountryMap.MapNewsTipsContent, nil, nil, nil, true)
end
