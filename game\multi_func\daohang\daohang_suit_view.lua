--[[28530 28490 28450 28410 28330 28290 28570 28370]]

local FASION_TYPE = {
    FASHION = 1,   -- 时装 武器那些
    ZUOQI = 2,     -- 坐骑 宠物 鲲
}

local DAOHANG_SUIT_EFFECT = {
	[GameEnum.ITEM_COLOR_GREEN] = "UI_tzjmzqq_lv_lhl",
	[GameEnum.ITEM_COLOR_BLUE] = "UI_tzjmzqq_lan_lhl",
	[GameEnum.ITEM_COLOR_PURPLE] = "UI_tzjmzqq_zi_lhl",
	[GameEnum.ITEM_COLOR_ORANGE] = "UI_tzjmzqq_cheng_lhl",
	[GameEnum.ITEM_COLOR_RED] = "UI_tzjmzqq_hong_lhl",
	[GameEnum.ITEM_COLOR_PINK] = "UI_tzjmzqq_fen_lhl",
	[GameEnum.ITEM_COLOR_GLOD] = "UI_tzjmzqq_jin_lhl",
	[GameEnum.ITEM_COLOR_COLOR_FUL] = "UI_tzjmzqq_caise_lhl",
}

function MultiFunctionView:ShowDaoHangSuitCallBack()
    self:RightPanleShowTween(self.node_list.daohang_suit_right_tween_root, self.node_list.danghang_suit_mid)
end

function MultiFunctionView:LoadDaoHangSuitCallBack()
    if not self.daohang_suit_item_list then
        self.daohang_suit_item_list = {}

        for i = 0, 7 do
            self.daohang_suit_item_list[i] = DaoHangSuitItemRender.New(self.node_list["daohang_suit_item" .. i])
        end
    end

    if not self.suit_right_attr_list then
        self.suit_right_attr_list = AsyncListView.New(SuitRightAttrListRender, self.node_list.suit_right_attr_list)
    end

    if not self.daohang_suit_right_item_list then
        self.daohang_suit_right_item_list = {}

        for i = 0, 7 do
            self.daohang_suit_right_item_list[i] = ItemCell.New(self.node_list["daohang_suit_right_item" .. i])
        end
    end

    self.slot_item_list_cache = {}

    XUI.AddClickEventListener(self.node_list["daohang_holyseal"], BindTool.Bind(self.OnClickDaoHangHolySeal, self))
    XUI.AddClickEventListener(self.node_list["btn_daohang_suit_attr"], BindTool.Bind(self.OnClickDaoHangSuitAttr, self))
    XUI.AddClickEventListener(self.node_list["btn_daohang_suit_decompose"], BindTool.Bind(self.OnClickDaoHangSuitDecompose, self))
    XUI.AddClickEventListener(self.node_list["btn_daohang_suit_onekeywear"], BindTool.Bind(self.OnClickDaoHangSuitOnekey, self))
    XUI.AddClickEventListener(self.node_list["btn_daohang_suit_compose"], BindTool.Bind(self.OnClickDaoHangSuitCompose, self))
    XUI.AddClickEventListener(self.node_list["btn_see_suit"], BindTool.Bind(self.OnClickDaoHangSeeSuit, self))
end

function MultiFunctionView:ReleaseDaoHangSuitCallBack()
    if self.daohang_suit_item_list then
        for k, v in pairs(self.daohang_suit_item_list) do
            v:DeleteMe()
        end

        self.daohang_suit_item_list = nil
    end

    if self.daohang_suit_right_item_list then
        for k, v in pairs(self.daohang_suit_right_item_list) do
            v:DeleteMe()
        end

        self.daohang_suit_right_item_list = nil
    end

    if self.suit_right_attr_list then
        self.suit_right_attr_list:DeleteMe()
        self.suit_right_attr_list = nil
    end

    self.slot_item_list_cache = nil
end

function MultiFunctionView:OnFlushDaoHangSuit()
    local attr_list, cap =  MultiFunctionWGData.Instance:GetDaoHangSuitShowCfgAttrAndCap()
    self.node_list.suit_cap_value.text.text = cap or 0
    if not IsEmptyTable(attr_list) then
        self.suit_right_attr_list:SetDataList(attr_list)
    end

    -- local complete_equip = true
    -- local min_color = GameEnum.ITEM_COLOR_COLOR_FUL
    local equip_datalist = MultiFunctionWGData.Instance:GetDaoHangSlotItemData()

    if not IsEmptyTable(equip_datalist) then
        for i = 0, 7 do
            self.daohang_suit_item_list[i]:SetData(equip_datalist[i])

            -- local item_id = equip_datalist[i].item_id
            -- if item_id > 0 then
            --     local _, item_color = ItemWGData.Instance:GetItemColor(item_id)
            --     min_color = min_color > item_color and item_color or min_color
            -- else
            --     complete_equip = false
            -- end
        end
    end

    self:CheckShowEquipQualityChangeEffect(equip_datalist)

    local suit_apparel_data_list = MultiFunctionWGData.Instance:GetDaoHangSuitShowApparelItemCfg()
    if not IsEmptyTable(suit_apparel_data_list) then
        for i = 0, 7 do
            local data = suit_apparel_data_list[i + 1]
            if not IsEmptyTable(data) then
                local item_id = data.item_id
                self.daohang_suit_right_item_list[i]:SetData({item_id = item_id})
                self.daohang_suit_right_item_list[i]:MakeGray(not data.act)
            end
        end
    end

    -- if complete_equip then
    --     local effct_cfg = DAOHANG_SUIT_EFFECT[min_color]
    --     local bundle, asset = ResPath.GetEffectUi(effct_cfg)
    --     self.node_list.daohang_suit_effect:ChangeAsset(bundle, asset)
    -- end

    -- self.node_list.daohang_suit_effect:CustomSetActive(complete_equip)
    -- self.node_list.daohang_suit_normal_effect:CustomSetActive(not complete_equip)

    -- local suit_apparel_data_list = MultiFunctionWGData.Instance:GetDaoHangSuitApparelItemCfg()
    -- if suit_apparel_data_list then
    --     for i = 0, 6 do
    --         if not IsEmptyTable(suit_apparel_data_list[i + 1]) then
    --             local item_id = suit_apparel_data_list[i + 1].item_id
    --             self.daohang_suit_right_item_list[i]:SetData({item_id = item_id})
    --             local fashion_type = suit_apparel_data_list[i + 1].fashion_type
    --             local act = false
                
    --             if fashion_type == FASION_TYPE.FASHION then
    --                 act = NewAppearanceWGData.Instance:GetFashionIsActByItemId(item_id)
    --             else
    --                 act = NewAppearanceWGData.Instance:GetQiChongIsActByItemId(item_id)
    --             end

    --             self.daohang_suit_right_item_list[i]:MakeGray(not act)
    --         end
    --     end
    -- end

    -- --策划要求展示一个特殊物品
    -- local other_cfg = MultiFunctionWGData.Instance:GetDaoHangOtherCfg()
    -- if other_cfg and other_cfg.rmb_fashion then
    --     local target_item_id = other_cfg.rmb_fashion
    --     self.daohang_suit_right_special_item:SetData({item_id = target_item_id})

    --     local special_act = false
    --     local fashion_type = other_cfg.fashion_type

    --     if fashion_type == FASION_TYPE.FASHION then
    --         special_act = NewAppearanceWGData.Instance:GetFashionIsActByItemId(target_item_id)
    --     else
    --         special_act = NewAppearanceWGData.Instance:GetQiChongIsActByItemId(target_item_id)
    --     end

    --     self.daohang_suit_right_special_item:MakeGray(not special_act)
    -- end

    local one_key_wear_flag = MultiFunctionWGData.Instance:GetDaoHangOneKeyWearDataList()
    local _, is_get, can_get = MultiFunctionWGData.Instance:GetDaoHangEquipSuitShowItemData()
    local remind = MultiFunctionWGData.Instance:GetDaoHangCompRemind()
    self.node_list.btn_daohang_suit_onekeywear_remind:CustomSetActive(one_key_wear_flag > 0)
    local holyseal_remind_flag = MultiFunctionWGData.Instance:GetDaoHangHolySealRemind()
    self.node_list.daohang_holyseal_remind:CustomSetActive(holyseal_remind_flag)
    self.node_list.btn_daohang_suit_attr_remind:CustomSetActive(not is_get and can_get)
    self.node_list.btn_daohang_suit_compose_remind:CustomSetActive(remind)

    local cur_pixiu_level = MultiFunctionWGData.Instance:GetPiXiuLv()
    local is_get_pixiu_pri = cur_pixiu_level > 0
    local is_max_level = cur_pixiu_level >= MultiFunctionWGData.Instance:GetDaoHangRmbBuyCfgNum()
    self.node_list.daohang_suit_unlock_pri:CustomSetActive(not is_get_pixiu_pri)
    self.node_list.daohang_suit_up_pri:CustomSetActive(is_get_pixiu_pri and not is_max_level)
end

function MultiFunctionView:OnClickDaoHangHolySeal()
    MultiFunctionWGCtrl.Instance:OpenDaoHangHolySealView()
end

function MultiFunctionView:OnClickDaoHangSuitAttr()
    MultiFunctionWGCtrl.Instance:OpenDaoHangSuitAttrView()
end

function MultiFunctionView:OnClickDaoHangSuitDecompose()
    MultiFunctionWGCtrl.Instance:OpenDaoHangDeComposeView()
end

function MultiFunctionView:OnClickDaoHangSuitOnekey()
    local one_key_wear_flag = MultiFunctionWGData.Instance:GetDaoHangOneKeyWearDataList()

    if one_key_wear_flag > 0 then
        MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.WEAR_ALL)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.DaoHangOneKeyWearErrorTips)
    end
end

function MultiFunctionView:OnClickDaoHangSuitCompose()
    MultiFunctionWGCtrl.Instance:OpenDaoHangComposeView()
end

function MultiFunctionView:OnClickDaoHangSeeSuit()
    MultiFunctionWGCtrl.Instance:OpenDaoHangSuitFashionPreviewView()
end

function MultiFunctionView:CheckShowEquipQualityChangeEffect(equip_datalist)
    -- local show_effect = false

    if IsEmptyTable(self.slot_item_list_cache) then
        -- show_effect = false
        self.slot_item_list_cache = equip_datalist
        return
    end

    local old_wear_num = 0
    local new_wear_num = 0
    local old_min_color = GameEnum.ITEM_COLOR_COLOR_FUL
    local new_min_color = GameEnum.ITEM_COLOR_COLOR_FUL

    for i = 0, 7 do
        local old_data = self.slot_item_list_cache[i]
        local new_data = equip_datalist[i]

        if old_data.item_id > 0 then
            old_wear_num = old_wear_num + 1

            local _, item_color = ItemWGData.Instance:GetItemColor(old_data.item_id)
            old_min_color = old_min_color > item_color and item_color or old_min_color
        end

        if new_data.item_id > 0 then
            new_wear_num = new_wear_num + 1

            local _, item_color = ItemWGData.Instance:GetItemColor(new_data.item_id)
            new_min_color = new_min_color > item_color and item_color or new_min_color
        end
    end

    -- if old_wear_num < 8 and new_wear_num >= 8 then
    --     show_effect = true
    -- end

    -- if new_wear_num >= 8 and old_min_color < new_min_color then
    --     show_effect = true
    -- end

    -- if show_effect then
    --     self:DaoHangEquipQualityChangeEffect()
    -- end

    self.slot_item_list_cache = equip_datalist
end

-- function MultiFunctionView:DaoHangEquipQualityChangeEffect()
--     local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tzjmbaozha_lhl)
--     EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.daohang_suit_quality_effect.transform)
-- end

--------------------------------------------DaoHangSuitItemRender------------------------------------------------
DaoHangSuitItemRender = DaoHangSuitItemRender or BaseClass(BaseRender)

function DaoHangSuitItemRender:__init()
    self.load_sprite = false
end

function DaoHangSuitItemRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
        self.item:SetItemTipFrom(ItemTip.FROM_DAOHANG_EQUIP)
    end

    XUI.AddClickEventListener(self.node_list["icon"], BindTool.Bind(self.OnClickIcon, self))
end

function DaoHangSuitItemRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end

    self.load_sprite = nil
end

function DaoHangSuitItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local has_equip = self.data.item_id > 0
    if has_equip then
        self.item:SetData(self.data)
    else
        if not self.load_sprite then
            local equip_cfg = MultiFunctionWGData.Instance:GetDaoHangEquipTypeCfgBySlot(self.data.slot)

            if not IsEmptyTable(equip_cfg) then
                local bundle, asset = ResPath.GetMultiFunctionImg("a2_fsrm_eq_icon_" .. equip_cfg.image)
                self.node_list.icon.image:LoadSprite(bundle, asset, function ()
                    self.node_list.icon.image:SetNativeSize()
                    self.load_sprite = true
                end)
            end
        end
    end

    self.node_list.bg:CustomSetActive(not has_equip)
    self.node_list.item:CustomSetActive(has_equip)
end

function DaoHangSuitItemRender:OnClickIcon()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = MultiFunctionWGData.Instance:GetDaoHangEquipTypeCfgBySlot(self.data.slot)

    if cfg and cfg.preview_id then
        TipWGCtrl.Instance:OpenItem({item_id = cfg.preview_id})
    end

    -- SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.DaoHangNoWearTips)
end

------------------------------------------SuitRightAttrListRender------------------------------------------
SuitRightAttrListRender = SuitRightAttrListRender or BaseClass(BaseRender)

function SuitRightAttrListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name or ""
    self.node_list.attr_value.text.text = self.data.value_str or ""
end