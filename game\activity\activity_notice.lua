--------------------------------------------------
-- 活动刷新提醒
--------------------------------------------------
ActivityForenoticeView = ActivityForenoticeView or BaseClass(SafeBaseView)

function ActivityForenoticeView:__init()
	self.is_modal = true
	self.view_layer = UiLayer.Pop
	self.view_name = "ActivityForenoticeView"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third2_panel")
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_forenotice")
	self.show_list = {}
end

function ActivityForenoticeView:__delete()
end

function ActivityForenoticeView:ReleaseCallBack()

end

function ActivityForenoticeView:CloseCallBack()
	self.show_list = {}
end

function ActivityForenoticeView:LoadCallBack()
	self.node_list["btn_go_kill"].button:AddClickListener(BindTool.Bind1(self.ClickHandler, self))
end

function ActivityForenoticeView:ShowIndexCallBack()
	self:FlushActivity()
end

function ActivityForenoticeView:FlushActivity()
	if #self.show_list > 0 then
		self.act_cfg = table.remove(self.show_list, 1)
		if self.act_cfg then
			self.node_list["boss_name_txt"].text.text = self.act_cfg.desc
			self.node_list["lbl_title"].text.text = self.act_cfg.title

			-- 图标
			if self.act_cfg.act_icon then
				self.node_list["icon_bg"]:SetActive(true)
				local bundle, asset = ResPath.GetF2MainUIImage(self.act_cfg.act_icon)
				self.node_list["icon"].image:LoadSprite(bundle, asset, function()
					self.node_list["icon"].image:SetNativeSize()
				end)
			else
				self.node_list["icon_bg"]:SetActive(false)
			end
		end
	end
end

function ActivityForenoticeView:SetData(data)
	if not data then
		return
	end

	for i,v in ipairs(self.show_list) do
		if v.act_type == data.act_type then
			return
		end
	end

	table.insert(self.show_list, data)
end

function ActivityForenoticeView:RemoveAct(act_type)
	for i,v in ipairs(self.show_list) do
		if v.act_type == act_type then
			table.remove(self.show_list, i)
			break
		end
	end

	if #self.show_list == 0 then
		self:Close()
	end
end

function ActivityForenoticeView:ClickHandler()
	if not self.act_cfg then
		return
	end

	ActivityWGCtrl.Instance:AutoQuickJoinActByType(self.act_cfg.act_type)
	if #self.show_list > 0 then
		self:FlushActivity()
	else
		self:Close()
	end
end
