FightSoulSlot = FightSoulSlot or BaseClass()
function FightSoulSlot:__init()
    self.solt_index = -1
    self:DefaultData()
end

function FightSoulSlot:DefaultData()
    self.fs_item_id = 0
    self.fs_type = -1
    self.fs_name = ""
    self.fs_icon = 0
    self.is_out_fight = false
    self.fs_star = 0
    self.fs_level = 0
    self.fs_grade = 0
    self.fs_color = 0
    self.index_in_bag = -1
    self.is_wear = false
    self.model_res = 0
    self.item_data = {}
end

function FightSoulSlot:__delete()

end

-- 槽位
function FightSoulSlot:SetSlotIndex(index)
    self.solt_index = index
end

function FightSoulSlot:GetSlotIndex()
    return self.solt_index
end

function FightSoulSlot:SetData(data)
    self:DefaultData()
    if IsEmptyTable(data) then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    local type_cfg = FightSoulWGData.Instance:GetFightSoulTypeCfg(data.fight_soul_type)
    self.index_in_bag = data.index
    self.fs_item_id = data.item_id
    self.fs_type = data.fight_soul_type
    self.fs_name = item_cfg and item_cfg.name or self.fs_name
    self.fs_icon = self.fs_type
    self.fs_star = data.star
    self.fs_level = data.level
    self.fs_grade = data.grade
    self.fs_color = data.color
    self.is_wear = true
    self.model_res = type_cfg and type_cfg.model_res or self.model_res
    self.item_data = data
end

function FightSoulSlot:GetItemData()
    return self.item_data
end

-- 出战标记
function FightSoulSlot:SetOutFightFlag(bool)
    self.is_out_fight = bool
end

function FightSoulSlot:GetOutFightFlag()
    return self.is_out_fight
end

function FightSoulSlot:GetItemId()
    return self.fs_item_id
end

-- 战魂类型
function FightSoulSlot:GetType()
	return self.fs_type
end

-- 战魂名称
function FightSoulSlot:GetName()
	return self.fs_name
end

-- 战魂图标
function FightSoulSlot:GetIconId()
	return self.fs_icon
end

-- 战魂星级
function FightSoulSlot:GetStar()
	return self.fs_star
end

-- 战魂等级
function FightSoulSlot:GetLevel()
	return self.fs_level
end

-- 战魂重数
function FightSoulSlot:GetGrade()
	return self.fs_grade
end

function FightSoulSlot:GetColor()
	return self.fs_color
end

-- 战魂上阵
function FightSoulSlot:GetIsWear()
	return self.is_wear
end

-- 在背包中的位置
function FightSoulSlot:GetInBagIndex()
	return self.index_in_bag
end

-- 战魂上锁
function FightSoulSlot:IsLock()
    return FightSoulWGData.Instance:GetSlotIsLock(self:GetSlotIndex())
end

function FightSoulSlot:GetModelRes()
    local part_show_list = FightSoulWGData.Instance:GetSlotWearEquipState(self:GetSlotIndex())
    return self.fs_type, part_show_list
end

-- 是否满级
function FightSoulSlot:GetIsMaxLevel()
    local cfg_max_level = FightSoulWGData.Instance:GetMaxUpLevel()
    return self.fs_level >= cfg_max_level
end

-- 是否满重
function FightSoulSlot:GetIsMaxGrade()
    local cfg_max_grade = FightSoulWGData.Instance:GetMaxGrade()
    return self.fs_grade >= cfg_max_grade
end

-- 是否到当前重最大等级
function FightSoulSlot:GetIsCurGradeMaxLevel()
    local max_level = FightSoulWGData.Instance:GetCurGradeMaxUpLevel(self.fs_grade)
    return self.fs_level >= max_level
end

-- 是否是突破限制
function FightSoulSlot:GetIsBreakLimit()
    local cfg = FightSoulWGData.Instance:GetBreakCfgByGrade(self.fs_grade)
    if cfg then
        return self.fs_level < cfg.up_max_level
    end
    return true
end

function FightSoulSlot:GetSkillIdAndLevel()
    local fs_item_cfg = FightSoulWGData.Instance:GetFightSoulItemCfg(self.fs_item_id)
    if fs_item_cfg then
        return fs_item_cfg.active_skill, fs_item_cfg.skill_level
    end

    return 0, 0
end

function FightSoulSlot:GetOutFightRemind()
    return FightSoulWGData.Instance:GetSingleOutFightRemind(self:GetSlotIndex())
end

-- 获得当前技能名
function FightSoulSlot:GetSkillName()
    local skill_id, skill_level = self:GetSkillIdAndLevel()
    local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(skill_id, skill_level)
    if skill_cfg then
        return skill_cfg.skill_name
    end

    return ""
end

-- 获得魂骨装备列表
function FightSoulSlot:GetBoneEquipList()
    return FightSoulWGData.Instance:GetBonePartListBySlot(self:GetSlotIndex())
end

-- 获得魂骨装备属性列表
function FightSoulSlot:GetBoneEquipAttr()
    return FightSoulWGData.Instance:GetBoneSlotBaseAttrList(self:GetSlotIndex())
end

-- 获得魂骨套装列表
function FightSoulSlot:GetBoneSuitList()
    return FightSoulWGData.Instance:GetBoneShowSuitInfoBySlot(self:GetSlotIndex())
end

-- 是否穿戴魂装
function FightSoulSlot:GetHaveWearEquip()
    return FightSoulWGData.Instance:GetSlotHaveWearEquip(self:GetSlotIndex())
end
