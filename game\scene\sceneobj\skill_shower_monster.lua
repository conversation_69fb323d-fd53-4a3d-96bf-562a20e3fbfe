SkillShowerMonster = SkillShowerMonster or BaseClass(SkillShower)

function SkillShowerMonster:__init(vo)
	self.special_res_id = vo.special_res_id or 2040001
	self:InitInfo()
	self:UpdateAppearance()
	self.is_hit_flying = false
	self.origin_high = self:GetRoot().transform.localPosition.y
	self.hit_fly_high = self.origin_high + 5
	self.hit_back_pos = Vector3(0, 0, -5)
end

function SkillShowerMonster:__delete()
	Runner.Instance:RemoveRunObj(self)

	GlobalTimerQuest:CancelQuest(self.hit_fly_delay_timer)
	self.hit_fly_delay_timer = nil

	GlobalTimerQuest:CancelQuest(self.reset_pos_delay_timer)
	self.reset_pos_delay_timer = nil

	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end
	
	if self.sequence2 then
		self.sequence2:Kill()
		self.sequence2 = nil
	end
end

function SkillShowerMonster:InitInfo()
	if self.special_res_id ~= 0 then
		self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Monster", self.special_res_id))
	end
end

function SkillShowerMonster:UpdateAppearance()
	self:InitAppearance()
end

function SkillShowerMonster:InitAppearance()
	if self.special_res_id ~= 0 then
	    local bundle, asset = ResPath.GetMonsterModel(self.special_res_id)
	    self:ChangeModel(SceneObjPart.Main, bundle, asset)
	end
end

function SkillShowerMonster:OnModelLoaded(part, obj, obj_class)
	Role.OnModelLoaded(self, part, obj, obj_class)
	-- if part == SceneObjPart.Main then
	-- 	self.origin_high = self:GetRoot().transform.localPosition.y
	-- 	self.hit_fly_high = self.origin_high + 5
	-- end
	if part == SceneObjPart.Main then
		if self.load_callback ~= nil then
			self.load_callback()
		end
	end
end

function SkillShowerMonster:EnterStateStand()
	SkillShower.EnterStateStand(self)
	self:ResetPos()
end

function SkillShowerMonster:EnterStateHurt()
    local anim_name = SceneObjAnimator.Hurt
    self.is_move_over_pos = false
    self.action_time_record = nil
    self.has_action_hit = false
    self.cur_action_time = 0
	self.action_time_record = self:GetActionTimeRecord(anim_name)
    self:CrossAction(SceneObjPart.Main, anim_name, false, nil, true)
end

function SkillShowerMonster:UpdateStateHurt(elapse_time)
    self.cur_action_time = self.cur_action_time + elapse_time
    if self.action_time_record ~= nil then
        if self.has_action_hit == false then
            -- 触发受击
            if self.cur_action_time >= self.action_time_record.time then
                self.has_action_hit = true
                self:DoStand()
            end
        end
    end
end

function SkillShowerMonster:GetActionTimeRecord(anim_name)
	local atr = MonsterActionConfig[self.res_id]
    if atr ~= nil then
        return atr[anim_name]
    end

    return MonsterActionConfig[0][anim_name]
end

function SkillShowerMonster:DoHurt()
    if self:IsDeleted() then
        return
    end
    self:ChangeState(SceneObjState.Hurt)
end

function SkillShowerMonster:DoHitBack()
	self.hit_back_tween = self:GetRoot().transform:DOLocalMoveZ(-5, 0.2)
	self.hit_back_tween:SetEase(DG.Tweening.Ease.OutQuint)
end

function SkillShowerMonster:DoHitFly()
	if self.is_hit_flying then
		return
	end
	self.is_hit_flying = true

	GlobalTimerQuest:CancelQuest(self.hit_fly_delay_timer)
	self.hit_fly_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		if self.sequence then
			self.sequence:Kill()
			self.sequence = nil
		end

		local sequence = DG.Tweening.DOTween.Sequence()
		local target = self:GetRoot().transform.localPosition
		local jump_high = 3
		local jump_power = 1.3
		local jump_time = 0.3
		local tween = self:GetRoot().transform:DOLocalJump(Vector3(0, self.hit_fly_high, 0), jump_power, 1, jump_time):SetEase(DG.Tweening.Ease.OutQuint)
		sequence:Append(tween)
		sequence:OnComplete(function()
			local jump_down_power = 1.2
			local jump_down_time = 0.2
			if self.sequence2 then
				self.sequence2:Kill()
				self.sequence2 = nil
			end

			local sequence2 = DG.Tweening.DOTween.Sequence()
			local tween2 = self:GetRoot().transform:DOLocalJump(Vector3(0, 0, 0), jump_down_power, 1, jump_down_time)
			sequence2:Append(tween2)
			sequence2:OnComplete(function()
				self.is_hit_flying = false
			end)

			self.sequence2 = sequence2
		end)

		self.sequence = sequence
	end, 0.1)
end

function SkillShowerMonster:ResetPos()
	self:GetRoot().transform.localPosition = Vector3(0, 0, 0)
end

function SkillShowerMonster:EquipDataChangeListen()
end

function SkillShowerMonster:IsWeaponOwnAnim()
	return false
end