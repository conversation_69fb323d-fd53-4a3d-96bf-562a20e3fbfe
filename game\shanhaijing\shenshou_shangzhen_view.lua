ShenShouShangZhenView = ShenShouShangZhenView or BaseClass(SafeBaseView)

function ShenShouShangZhenView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_sssz_show")
    self.view_layer = UiLayer.Pop
end

function ShenShouShangZhenView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ShenShou.Name
    self:InitListView()
end

function ShenShouShangZhenView:OnFlush()
    local data = ShenShouWGData.Instance:GetShenShouActive()
    self.sssz_list:SetDataList(data)
end

function ShenShouShangZhenView:__delete()

end

function ShenShouShangZhenView:ReleaseCallBack()
    if self.sssz_list then
        self.sssz_list:DeleteMe()
        self.sssz_list = nil
    end
end

function ShenShouShangZhenView:CloseCallBack()

end

function ShenShouShangZhenView:CloseView()
    self:Close()
end

function ShenShouShangZhenView:InitListView()
    self.sssz_list = AsyncListView.New(ShenShouShangZhenViewRender, self.node_list.ph_sz_grid)
end



ShenShouShangZhenViewRender = ShenShouShangZhenViewRender or BaseClass(BaseRender)
function ShenShouShangZhenViewRender:__init()

end

function ShenShouShangZhenViewRender:LoadCallBack()
   self.shenshou_icon = self.node_list["ph_item_icon"]
   self.shenshou_name = self.node_list["lbl_item_name"]
   self.shenshou_iconbg = self.node_list["ph_item_iconbg"]
   self.node_list["ItemRender2"].button:AddClickListener(BindTool.Bind(self.ShenShouZhuZhan, self))
end

function ShenShouShangZhenViewRender:__delete()

end

function ShenShouShangZhenViewRender:OnFlush()
    self.shenshou_name.text.text = ToColorStr(self.data.name,ITEM_COLOR[self.data.series + 1])
    self.shenshou_iconbg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_wpk_" .. self.data.series+1))

    local bundle, asset = ResPath.GetF2SHJImgPath(self.data.icon_head_circle)
    self.shenshou_icon.image:LoadSprite(bundle, "icon_" .. asset, function()
        self.shenshou_icon.image:SetNativeSize()
    end)

    local shou_cfg = ShenShouWGData.Instance:GetShenShouCfg(self.data.shou_id)
    local shenshou_base_struct = AttributeMgr.GetAttributteByClass(shou_cfg)
    local eq_struct = ShenShouWGData.Instance:GetOneShenShouAttr(self.data.shou_id)
    local capability = 0
    capability = AttributeMgr.GetCapability(eq_struct)
    capability = capability + AttributeMgr.GetCapability(shenshou_base_struct)
    self.node_list.cap_value.text.text = capability
end

function ShenShouShangZhenViewRender:ShenShouZhuZhan()
    if ShenShouWGData.Instance:IsFullZhuZhan() then
        if ShenShouWGData.Instance:IsShenShouZhuZhan(self.data.shou_id) then
            ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ZHUZHAN, self.data.shou_id)
        else
            local list = ShenShouWGData.Instance:GetShenShouZhuZhanList()
            local min_shou_id = self.data.shou_id
            for k,v in pairs(list) do
                if v < min_shou_id then
                    min_shou_id = v
                end
            end
            if min_shou_id ~= self.data.shou_id then
                ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ZHUZHAN, min_shou_id)
                ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ZHUZHAN, self.data.shou_id)
                ShenShouWGCtrl.Instance:PlanShenShouZhuZhanAnim()
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.ZhuZhan_Tip)
            end
        end
    else
        ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ZHUZHAN, self.data.shou_id)
        ShenShouWGCtrl.Instance:PlanShenShouZhuZhanAnim()
    end
     ShanHaiJingWGCtrl.Instance:CloseShenShouSZ()
end