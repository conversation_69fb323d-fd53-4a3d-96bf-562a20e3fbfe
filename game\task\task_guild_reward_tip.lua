TaskGuildRewardTip = TaskGuildRewardTip or BaseClass(SafeBaseView)

local GongXianId = 27646
function TaskGuildRewardTip:__init()
	
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/task_prefab", "task_guild_reward_tip")
	self:SetMaskBg(true)
	self.time = 5
	self.reward_num = 0
end

function TaskGuildRewardTip:__delete()
end

function TaskGuildRewardTip:LoadCallBack()
	self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(570, 392)

	XUI.AddClickEventListener(self.node_list["btn_ok"], BindTool.Bind(self.OnClick, self))
	self.item_cell = ItemCell.New(self.node_list["go_item"])
end

function TaskGuildRewardTip:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TaskGuildRewardTip:OpenCallBack()

end

function TaskGuildRewardTip:OnFlush(param_list)
	-- body
	if not param_list or not param_list.all then return end
	self.reward_num = param_list.all[1]
	local task_data = TaskWGData.Instance:GetGuildInfo()
    if not task_data or IsEmptyTable(task_data) then return end
	local commit_times = task_data.complete_task_count
	-- local task_status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
	-- if task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS or task_status == GameEnum.TASK_STATUS_COMMIT then
 --        commit_times = commit_times + 1
 --    end
    local temp_count = "[" .. commit_times .. "/" .. TaskWGData.Instance:GetTaskGuildWeekMaxCount() .. "]"
    self.node_list["text_num"].text.text = Language.Task.GuildNumDes .. temp_count

	local reward_cfg = TaskWGData.Instance:GetTaskRewardCfg(GameEnum.TASK_TYPE_MENG)
	if not reward_cfg then return end
	-- self.item_cell:SetData(reward_cfg.reward_item[0])
	self.item_cell:SetData({item_id = GongXianId, num = reward_cfg.finish_gongxian * self.reward_num})	

	local item_cfg = ItemWGData.Instance:GetItemConfig(GongXianId)
	if not item_cfg then return end
	self.node_list["text_item_name"].text.text = (reward_cfg.finish_gongxian * self.reward_num) .. item_cfg.name
	
	self.time = 5
	self.time_quest = GlobalTimerQuest:AddTimesTimer(BindTool.Bind1(self.UpdateTime, self), 1, self.time)
end

function TaskGuildRewardTip:UpdateTime()
	-- body
	self.time = self.time - 1
	self.node_list["btn_text"].text.text = Language.Common.BtnOK .. "(" .. self.time .. ")"
	
	if self.time <= 0 then
		self:ClearTime()
		self:OnClick()
	end
end

function TaskGuildRewardTip:ClearTime()
	-- body
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function TaskGuildRewardTip:CloseCallBack()
	self:ClearTime()	
	self.reward_num = 0
end

function TaskGuildRewardTip:OnClick()
	-- print_error("==================", GameEnum.TASK_TYPE_MENG, self.reward_num)
	TaskWGCtrl.Instance:SendReward(GameEnum.TASK_TYPE_MENG, self.reward_num)
	self:Close()
end