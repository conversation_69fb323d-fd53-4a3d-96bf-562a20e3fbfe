MergeLeiChongRechargeWGData = MergeLeiChongRechargeWGData or BaseClass()
MergeLeiChongRechargeWGData.ConfigPath= "config/auto_new/merge_activity_limit_total_recharge_auto"
function MergeLeiChongRechargeWGData:__init()
	if MergeLeiChongRechargeWGData.Instance then
		ErrorLog("[MergeLeiChongRechargeWGData] Attemp to create a singleton twice !")
	end
	MergeLeiChongRechargeWGData.Instance = self

	self:InitLeiChongRechargeData()
	MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LIMIT_RECHARGE, {[1] = MERGE_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowLCRechargeRedPoint, self))
	RemindManager.Instance:Register(RemindName.MergeTotalRecharge, BindTool.Bind(self.IsShowLCRechargeRedPoint, self))
	
	self.cur_model_index = -1
end

function MergeLeiChongRechargeWGData:__delete()
	MergeLeiChongRechargeWGData.Instance = nil 
	RemindManager.Instance:UnRegister(RemindName.MergeTotalRecharge)
end

function MergeLeiChongRechargeWGData:SetLeiChongRechargeData(protocol)
	if self.LeiChongData == nil then
		self.LeiChongData = {}
	end
	self.LeiChongData.cur_xianyu = protocol.cur_xianyu
	self.LeiChongData.activity_start_combineserver_day = protocol.activity_start_combineserver_day
	self.LeiChongData.dangwei_info = protocol.dangwei_info
	self.LeiChongData.grade = protocol.grade

end

function MergeLeiChongRechargeWGData:InitLeiChongRechargeData()
	self.merge_leichong_recharge_cfg = ConfigManager.Instance:GetAutoConfig("combineserve_activity_limit_total_recharge_auto")
	self.leichong_reward_cfg = self.merge_leichong_recharge_cfg.reward
	self.leichong_param = self.merge_leichong_recharge_cfg.config_param
	self.leichong_param_cfg = ListToMap(self.leichong_param, "grade")
	self.model_info = ListToMap(self.merge_leichong_recharge_cfg.interface, "grade")
	self.leichong_model_cfg = ListToMap(self.merge_leichong_recharge_cfg.model, "grade", "id")
end

--获取累充奖励排行数据
function MergeLeiChongRechargeWGData:GetLeiChongRewardList()
	local list = {}
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		local data = {}
		data.cfg = v
		data.ID = v.ID
		data.receive_state = self:GetLeiChongReceiveState(v.ID)
		if data.receive_state == TianShenRoadRewardState.KLQ then
			data.sort = 0
		elseif data.receive_state == TianShenRoadRewardState.BKL then
			data.sort = 1
		else
			data.sort = 2
		end
		table.insert(list, data)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort", "ID"))
	return list
end

--获取领取状态
function MergeLeiChongRechargeWGData:GetLeiChongReceiveState(index)
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil and self.LeiChongData.dangwei_info[index] ~= nil then
		return self.LeiChongData.dangwei_info[index]
	else
		return 0
	end
end

--当前充值仙玉
function MergeLeiChongRechargeWGData:GetOwnXianYu()
	if self.LeiChongData ~= nil then
		return self.LeiChongData.cur_xianyu
	end
	return 0
end

function MergeLeiChongRechargeWGData:GetLeiChongRewardCfg()
	local grade = self:GetGradeByServerDay()
	local reward_cfg = {}
	for i,v in ipairs(self.leichong_reward_cfg) do
		if grade == v.grade then
			table.insert(reward_cfg, v)
		end
	end
	return reward_cfg
end


--获取当前档需要充值的仙玉
function MergeLeiChongRechargeWGData:GetNeedRechargeXianYU()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		if cur_xianyu < v.stage_value then
			local pre_stage_value = 0
			if i > 1 then
				pre_stage_value = cfg[i-1].stage_value
			end
			return v.stage_value - cur_xianyu, v.stage_value, pre_stage_value
		end
	end
	return 0, cfg[#cfg].stage_value, cfg[#cfg].stage_value
end

--是否已达成
function MergeLeiChongRechargeWGData:IsRechargeTargetFinish()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	return cur_xianyu >= cfg[#cfg].stage_value
end

--根据服务器开服天数获得活动配置grade
function MergeLeiChongRechargeWGData:GetGradeByServerDay()
	if self.LeiChongData and self.LeiChongData.grade then
		return self.LeiChongData.grade
	end

	return 0
end

--活动结束时间
function MergeLeiChongRechargeWGData:GetActEndTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LIMIT_RECHARGE)
	if activity_info and activity_info.end_time then
		return activity_info.end_time
	end
	return -1
end

function MergeLeiChongRechargeWGData:IsShowLCRechargeRedPoint()
	local state = MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LIMIT_RECHARGE)
	if not state then
		return 0
	end

	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil then
		for k,v in ipairs(self.LeiChongData.dangwei_info) do
			if v == TianShenRoadRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end

function MergeLeiChongRechargeWGData:GetActCanOpen()
	if nil == self.LeiChongData or nil == self.LeiChongData.grade then
        return false
    end

	local grade = self.LeiChongData.grade
	local cfg = self.leichong_param_cfg[grade]

	if nil == cfg then
		return false
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level >= cfg.open_role_level then
		return true
	end

	return false
end

--获取模型信息
function MergeLeiChongRechargeWGData:GetModelInfo()
	local grade = self:GetGradeByServerDay()
	return self.model_info[grade]
end

function MergeLeiChongRechargeWGData:GetLeiChongProgress(leichong_value, cfg_list)  --进度条
	local cur_progress = 0
	local cfg = cfg_list
	if next(cfg) == nil or leichong_value == nil then
		return cur_progress
	end
	local progress_list = {0.1, 0.4, 0.6, 1}			--对应的进度条值

	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list	
		local cur_need = tonumber(cfg[seq]) or 0
		local next_need = tonumber(cfg[seq + 1]) or tonumber(cfg[#cfg])
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		
		if leichong_value > cur_need and leichong_value <= next_need then
			cur_progress = (leichong_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif leichong_value > tonumber(cfg[#cfg]) then
			cur_progress = progress_list[length]
			break
		end
	end
	return cur_progress
end

function MergeLeiChongRechargeWGData:GetLeiChongModelCfg()
	local old_model_index = self.cur_model_index
	local cur_model_cfg = {}
	local grade = self:GetGradeByServerDay()
	local cur_index = 0

	if self.LeiChongData == nil then
		return {}
	end

	for k, v in pairs(self.LeiChongData.dangwei_info) do
		if v ~= 2 then
			cur_index = k
			break
		end
	end

	local model_cfg = self.leichong_model_cfg[grade] or {}
	local list = SortTableKey(model_cfg)
	for k, v in pairs(list) do
		if cur_index <= v.id then
			self.cur_model_index = grade .. v.id
			cur_model_cfg = v
			break
		end
	end

	if IsEmptyTable(cur_model_cfg) or cur_index == 0 then
		cur_model_cfg = list[#list]
		self.cur_model_index = grade .. list[#list].id
	end

	if old_model_index == self.cur_model_index then
		return {}
	end

	return cur_model_cfg
end

function MergeLeiChongRechargeWGData:ResetLeiChongModelIndex()
	self.cur_model_index = -1
end