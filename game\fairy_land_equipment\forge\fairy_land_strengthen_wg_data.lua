
--仙界装备--强化--Data

local this = FairyLandEquipmentWGData

function this:InitStrengthenData()
	--配置初始化
	local main_cfg = ConfigManager.Instance:GetAutoConfig("xianjie_equip_auto")
    self.fle_strength_cfg = ListToMap(main_cfg.gb_equip_strength, "slot", "part", "level")
    self.fle_strength_total_cfg = ListToMapList(main_cfg.gb_equip_all_strength, "slot")

    --红点
	RemindManager.Instance:Register(RemindName.FLEF_Strengthen, BindTool.Bind(self.IsShowStrengthenRedPoint, self))
	self:RegisterStrengthenRemindInBag(RemindName.FLEF_Strengthen)
end

function this:DeleteStrengthenData()
	this.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.FLEF_Strengthen)
end

function this:RegisterStrengthenRemindInBag(remind_name)
	self.strengthen_remind_item_id_map = {}
    local cfg = self:GetFLEStrengthenCfgBySlotPartLv(0, 0, 1)
	if cfg then
		local temp_stuff_id
		for i = 1, XIANJIE_EQUIP_STRENGTHEN_MAX_STUFF do
			temp_stuff_id = cfg["stuff_id_" .. i]
	    	if temp_stuff_id and temp_stuff_id > 0 then
	    		self.strengthen_remind_item_id_map[temp_stuff_id] = true
	    	end
	    end
	end

    local item_id_list = {}
    for k,v in pairs(self.strengthen_remind_item_id_map) do
        table.insert(item_id_list, k)
    end
    if not IsEmptyTable(item_id_list) then
	    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
	end
end

function this:GetIsStrengthenRemindItem(check_id)
	if self.strengthen_remind_item_id_map and self.strengthen_remind_item_id_map[check_id] then
		return true
	end
	return false
end

--仙界装备 强化总红点
function this:IsShowStrengthenRedPoint()
    local cur_active_num = self:GetCurMaxGodBody()--当前神体已激活转数
	for slot = 0, cur_active_num do
		if self:GetStrengthenRedPointBySlot(slot) then
        	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FLEF_STRENGTHEN, 1, function ()
				ViewManager.Instance:Open(GuideModuleName.FairyLandEquipmentView,
										TabIndex.fl_eq_forge_strengthen, nil, {to_ui_name = slot})
				return true
			end)
            return 1
        end
	end

    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FLEF_STRENGTHEN, 0)
	return 0
end

--仙界装备 强化--每个神体的红点
function this:GetStrengthenRedPointBySlot(slot)
	if self:GetStrengthenMasterRedPointBySlot(slot) then
		return true
	end

	if self:GetStrengthenPartRedPointBySlot(slot) then
		return true
	end
	return false
end

--仙界装备 强化--每个神体的红点
function this:GetStrengthenPartRedPointBySlot(slot)
	for part = 0, GOD_BODY_ENUM.MAX_HOLY_EQUIP_PART -1 do
		if self:GetStrengthenRedPointBySlotPart(slot, part) then
			return true
		end
	end
	return false
end

--仙界装备 强化--每个部位的红点
function this:GetStrengthenRedPointBySlotPart(slot, part)
	-- if FairyLandEquipmentWGData.Instance:GetIsSpecialType(part) then--特殊部位不能强化--策划说去掉
	-- 	return false
	-- end

	if not FairyLandEquipmentWGData.Instance:GetHolyEquipPartIsWear(slot, part) then
		return false
	end

	local level = FairyLandEquipmentWGData.Instance:GetPartStrengthenLevel(slot, part)
    if FairyLandEquipmentWGData.Instance:GetFLEStrengthenIsMaxLevel(slot, part, level) then
        return false
    end

    local level_cfg = FairyLandEquipmentWGData.Instance:GetFLEStrengthenCfgBySlotPartLv(slot, part, level + 1)
    local need_stuff, enough_stuff = 0, 0
    if level_cfg then
        local has_num, consume_item, consume_num
        for i = 1, XIANJIE_EQUIP_STRENGTHEN_MAX_STUFF do
            consume_item = level_cfg["stuff_id_" .. i]
            consume_num = level_cfg["stuff_num_" .. i]
            if consume_item and consume_num and consume_item > 0 and consume_num > 0 then
                need_stuff = need_stuff + 1
                has_num = ItemWGData.Instance:GetItemNumInBagById(consume_item)
                if has_num >= consume_num then
                    enough_stuff = enough_stuff + 1
                end
            end
        end
    end
	return need_stuff > 0 and need_stuff == enough_stuff
end

--仙界装备 总强化--每个神体的红点
function this:GetStrengthenMasterRedPointBySlot(slot)
	local cur_total_level = self:GetFLEStrengthenAllLevel(slot)
	local next_attr_cfg = self:GetFLEStrengthenTotalCfg(slot, true)
	return next_attr_cfg and cur_total_level >= next_attr_cfg.level
end

function this:GetFLEStrengthenCfgBySlotPartLv(slot, part, level)
	if self.fle_strength_cfg and self.fle_strength_cfg[slot] and self.fle_strength_cfg[slot][part] then
		return self.fle_strength_cfg[slot][part][level]
	end
end

--获取全身装备配置
function this:GetFLEStrengthenTotalCfg(slot, is_next)
	local curr_cfg = nil
	local next_cfg = nil
	local cur_slot_cfg_list = self.fle_strength_total_cfg[slot]
	local cur_total_act_level = self:GetFLEStrengthenActiveTotalLv(slot)
	if not IsEmptyTable(cur_slot_cfg_list) then
		for k, v in pairs(cur_slot_cfg_list) do
			if v.level <= cur_total_act_level then
				curr_cfg = v
				next_cfg = cur_slot_cfg_list[k + 1]
			end
		end

		if is_next then
			if nil == curr_cfg then
				return cur_slot_cfg_list[1]
			else
				return next_cfg
			end
		end
	end

	return curr_cfg
end

--获取全身装备配置
function this:GetFLEStrengthenTotalBaseCfg(slot)
	return self.fle_strength_total_cfg[slot] and self.fle_strength_total_cfg[slot][1]
end

--根据 转数 获取已穿戴的装备列表
function this:GetFLEStrengthenShowListBySlot(slot)
	local part_info_lsit = FairyLandEquipmentWGData.Instance:GetHolyEquipWearList(slot)
    local show_list, part_max_lv_num = {}, 0
    if IsEmptyTable(part_info_lsit) then
        return show_list, part_max_lv_num
    end

    local data, is_part_max
    --重构数据
    for part, part_info in pairs(part_info_lsit) do
    		data = {}
	        data.slot = slot
	        data.part = part
	        is_part_max = self:GetFLEStrengthenIsMaxLevel(slot, part, data.part_level)
	        if is_part_max then
	        	part_max_lv_num = part_max_lv_num + 1
	        end
	        data.part_info = part_info
	        table.insert(show_list, data)
    end

    if not IsEmptyTable(show_list) then
    	table.sort(show_list, SortTools.KeyLowerSorter("max_lv_flag", "part"))
    end

    return part_info_lsit, part_max_lv_num
end

--根据 转数 获取装备总强化等级(当前所在的激活总等级)
function this:GetFLEStrengthenActiveTotalLv(slot)
	return FairyLandEquipmentWGData.Instance:GetTotalStrengthenLevel(slot) or 0
end

--根据 转数 获取当前穿戴装备强化等级总和
function this:GetFLEStrengthenAllLevel(slot)
	local all_level = 0
	local all_level_list = FairyLandEquipmentWGData.Instance:GetPartStrengthenLevelList(slot)
	for part, lv in pairs(all_level_list) do
		all_level = all_level + lv
	end
	return all_level
end

function this:GetFLEStrengthenJumpIndex(slot)
	local list = self:GetHolyEquipWearList(slot)
	if list == nil then
		return 0
	end

	local first_had_data_index
	for i = 0, #list do
		local data = list[i]
		if not first_had_data_index and data.item_id > 0 then
			first_had_data_index = i
		end

		if self:GetStrengthenRedPointBySlotPart(data.slot, data.part) then
			return i
		end
	end

	return first_had_data_index or 0
end

--根据 转数 部位 获取装备信息
function this:GetFLEStrengthenInfoBySoltPart(slot, part)
	local one_part_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)
	if not IsEmptyTable(one_part_info) then
		local t_data = {}
        t_data.slot = slot
        t_data.part = part
        t_data.part_level = FairyLandEquipmentWGData.Instance:GetPartStrengthenLevel(slot, part)
        t_data.part_info = one_part_info
		return t_data
	end

	return nil
end

function this:GetFLEStrengthenIsMaxLevel(slot, part, level)
	level = level or 1
	return IsEmptyTable(self:GetFLEStrengthenCfgBySlotPartLv(slot, part, level + 1))
end

function this:GetFLEStrengthenAttr(slot, part, level)
    local cur_cfg = self:GetFLEStrengthenCfgBySlotPartLv(slot, part, level)
    local next_cfg = self:GetFLEStrengthenCfgBySlotPartLv(slot, part, level + 1)
    local attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_cfg, next_cfg)
    return attr_list
end

--FairyLandEquipmentWGData.Instance:TestPrintQHRed()
function this:TestPrintQHRed()
	local max_slot = FairyLandEquipmentWGData.Instance:GetGodBodyMaxNum()
	for slot = 0, max_slot - 1 do
		for part = 0,XIANJIE_EQUIP_TYPE.XIANYIN do
			if self:GetStrengthenRedPointBySlotPart(slot, part) then
				local str = string.format("第%s转神体第%s个部位可强化, 强化红点状态:1", slot, part)
				print_error(str)
				SysMsgWGCtrl.Instance:ErrorRemind(str)
				return
			end
		end
	end

	local str = string.format("没有可强化的部位, 强化红点状态:0")
	print_error(str)
	SysMsgWGCtrl.Instance:ErrorRemind(str)
end
