 ------------------------------------------------------------
--宠物相关主View
------------------------------------------------------------
SHJSkillTipView = SHJSkillTipView or BaseClass(SafeBaseView)

function SHJSkillTipView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/shj_ui_prefab", "layout_skill_tip")
end

function SHJSkillTipView:ReleaseCallBack()
	self.data = nil
end

function SHJSkillTipView:SetData(data)
	if not data then return end
	self.data = data
	self:Open()
end

function SHJSkillTipView:LoadCallBack()
end


function SHJSkillTipView:ShowIndexCallBack(index)
	self:Flush()
end

function SHJSkillTipView:OnFlush(param_t, index)
	if not self.data then return end
	local data = self.data.skill_cfg
	self.node_list.condition_info.text.text = data.skill_des
	self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(data.res_id))
	self.node_list.skill_name.text.text = data.skill_name

	self.node_list.layout_4:SetActive(not self.data.has_active)
	self.node_list.active_text.text.text = string.format(Language.ShanHaiJing.Active_Text,data.active_level)
end
