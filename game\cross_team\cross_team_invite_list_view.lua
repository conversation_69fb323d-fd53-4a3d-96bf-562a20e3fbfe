CrossTeamInviteListView = CrossTeamInviteListView or BaseClass(SafeBaseView)

function CrossTeamInviteListView:__init()
	self.view_layer = UiLayer.Pop
	
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(1078, 608)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_team_req")
    self.view_name = "CrossTeamInviteListView"
end

function CrossTeamInviteListView:LoadCallBack()
	--self:SetSecondView(nil, self.node_list["size"])
	self:CreateList()
	XUI.AddClickEventListener(self.node_list.btn_auto_refuse, BindTool.Bind(self.OnClickAutoRefuse, self))
    XUI.AddClickEventListener(self.node_list.btn_today_refuse_check, BindTool.Bind(self.OnClickChangeTodaySelectState, self))

    --打开界面默认不选择拒绝（拒绝后今日不再接受该玩家入队邀请）
    self:SetSelectState(false)
end

function CrossTeamInviteListView:SetSelectState(is_select)
    self.default_select_state = is_select
    self.node_list["btn_today_refuse_select"]:SetActive(is_select)
end

function CrossTeamInviteListView:ReleaseCallBack()
	if nil ~= self.list then
		self.list:DeleteMe()
		self.list = nil
	end
	if self.role_head_cell ~= nil then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

function CrossTeamInviteListView:CloseCallBack()
	 if 0 == #CrossTeamWGData.Instance:GetInviteList()then
	 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, 0)
	 end
	--self:RefuseAllReq()
end

function CrossTeamInviteListView:ShowIndexCallBack(index)
	self:Flush()
	self.node_list["btn_today_refuse_select"]:SetActive(false)
end

function CrossTeamInviteListView:CreateList()
	self.list = AsyncListView.New(CrossTeamInviteReqRender, self.node_list["ph_req_list"])
end

function CrossTeamInviteListView:OpenCallBack()
	-- self.root_node:setPositionX(HandleRenderUnit:GetWidth() / 2)
	-- self.root_node:setPositionY(HandleRenderUnit:GetHeight() / 2)
end

function CrossTeamInviteListView:CreateRoleHeadCell(uuid, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
	if self.role_head_cell == nil then
		self.role_head_cell = RoleHeadCell.New(false)
	end
	CrossTeamWGCtrl.Instance:QueryCrossTeamInfo(uuid, function(protocol)
		if self:IsLoaded() and self:IsOpen() then
			local main_role = Scene.Instance:GetMainRole()
			local main_role_vo = main_role.vo
			local role_info = {
				role_id = uuid.temp_low,
				role_name = role_name,
				prof = prof,
				sex = sex,
				is_online = is_online,
				team_index = protocol.team_index,
				team_type = TEAM_INVITE_TYPE.CHAT,
				plat_type = main_role_vo.plat_type,
				plat_name = main_role_vo.plat_name,
				server_id = server_id,
			}
			self.role_head_cell:SetRoleInfo(role_info)
			self.role_head_cell:OpenMenu(node)
		end
	end)
end

--一键拒绝
function CrossTeamInviteListView:OnClickAutoRefuse()
    --SocietyWGCtrl.Instance:RefuseInviteTeam(self.data.req_role_id)
    local invite_list = CrossTeamWGData.Instance:GetInviteList()
    for i, v in pairs(invite_list) do
        CrossTeamWGCtrl.Instance:RefuseInviteTeam(v.inviter_uuid)
		if CrossTeamWGCtrl.Instance:GetTodayCheckActive() then
			CrossTeamWGCtrl.Instance:SendNoLongerOperateReq(CROSS_TEAM_LOGIN_NO_LONGER_TYPE.CROSS_TEAM_LOGIN_NO_LONGER_TYPE_INVITE_ME_TEAM, v.inviter_uuid)
		end
    end
end

function CrossTeamInviteListView:OnClickChangeTodaySelectState()
	local is_select = self.node_list["btn_today_refuse_select"].gameObject.activeSelf
	self.node_list["btn_today_refuse_select"]:SetActive(not is_select)
    --if self.default_select_state == nil then
    --    self.default_select_state = false
    --end
    --self:SetSelectState(not self.default_select_state)
end

function CrossTeamInviteListView:GetTodayCheckActive()
	if not self.node_list["btn_today_refuse_select"] then
		return false
	end
	return self.node_list["btn_today_refuse_select"].gameObject.activeSelf
end

function CrossTeamInviteListView:OnFlush()
	if self.list and nil ~= CrossTeamWGData.Instance:GetInviteList() then
		self.list:SetDataList(CrossTeamWGData.Instance:GetInviteList())
		self.node_list["ph_req_list"].scroller:ReloadData(0)
	end
end

function CrossTeamInviteListView:OnRefuseAllReq()
	self:RefuseAllReq()
	self:Close()
end

function CrossTeamInviteListView:RefuseAllReq()
	local allInviteReq = CrossTeamWGData.Instance:GetInviteList()
	if nil == allInviteReq or #allInviteReq <= 0 then
		return
	end
	for k, v in pairs(allInviteReq) do
		CrossTeamWGCtrl.Instance:SendInviteUserRet(v.inviter_uuid, 1)
	end
	CrossTeamWGData.Instance:TeamInviteReqClear()
	self:Flush()
end

function CrossTeamInviteListView:OnClose()
	self:Close()
end

function CrossTeamInviteListView:DeleteReq(uuid)
	if nil == uuid then
		return
	end
	CrossTeamWGData.Instance:RemoveTeamInviteReq(uuid)
	self:Flush()

	if CrossTeamWGData.Instance:GetInviteListSize() <= 0 then
		self:Close()
	end
end



----------------------------------------------------------------------------------------------------
-- 组队邀请回复面板
----------------------------------------------------------------------------------------------------
CrossTeamInviteReqRender = CrossTeamInviteReqRender or BaseClass(SocietyBaseRender)
function CrossTeamInviteReqRender:__init()
end

function CrossTeamInviteReqRender:__delete()
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end
end

function CrossTeamInviteReqRender:LoadCallBack()
	self.node_list["btn_refuse"].button:AddClickListener(function()
		CrossTeamWGCtrl.Instance:RefuseInviteTeam(self.data.inviter_uuid)
        if CrossTeamWGCtrl.Instance:GetTodayCheckActive() then
            CrossTeamWGCtrl.Instance:SendNoLongerOperateReq(CROSS_TEAM_LOGIN_NO_LONGER_TYPE.CROSS_TEAM_LOGIN_NO_LONGER_TYPE_INVITE_ME_TEAM, self.data.inviter_uuid)
		end
	end)

	self.node_list["btn_agree"].button:AddClickListener(function()
        local uuid = self.data.inviter_uuid
		CrossTeamWGCtrl.Instance:SendInviteUserRet(uuid, 1)
		GlobalTimerQuest:AddDelayTimer(function ()
			CrossTeamWGCtrl.Instance:DeleteReq(uuid)
		end, 0)
	end)
	self.node_list["lbl_name"].button:AddClickListener(function()
		local uuid = self.data.inviter_uuid
		BrowseWGCtrl.Instance:BrowRoelInfo(uuid.temp_low, function(param_protocol)
            CrossTeamWGCtrl.Instance:CreateRoleHeadCell(uuid, self.data.req_role_name, param_protocol.prof, param_protocol.sex,
            param_protocol.is_online, self.node_list.lbl_name, param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
		end, nil, true)
	end)
end

function CrossTeamInviteReqRender:OnFlush()
	if self.role_avatar then
		local role_info = {
			role_id = self.data.uuid.temp_low,
			role_name = self.data.req_role_name,
			prof = self.data.req_role_prof,
			sex = self.data.req_role_sex,
			is_online = true,
		}
		if IS_ON_CROSSSERVER then
			local main_role = Scene.Instance:GetMainRole()
	        local main_role_vo = main_role.vo
			role_info.plat_name = main_role_vo.plat_name
			role_info.server_id = main_role_vo.merge_server_id
			self.role_avatar:SetRoleInfo(role_info)
		else
			self.role_avatar:SetRoleInfo(role_info)
		end
	end

	local level_str = string.format(Language.NewTeam.InvaterInfo, self.data.req_role_name, self.data.inviter_level)
	EmojiTextUtil.ParseRichText(self.node_list["lbl_name"].emoji_text, level_str, 20, COLOR3B.DEFAULT)

    local team_type_name = CrossTeamWGData.Instance:GetSceneName()
	local str = string.format(Language.NewTeam.BeApplyViewOtherInfoStr, team_type_name, self.data.team_min_level, self.data.team_max_level)
	EmojiTextUtil.ParseRichText(self.node_list["other_info_richtext"].emoji_text, str, 20, COLOR3B.DEFAULT)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["hor_layout"].rect)

	local relation_flag = bit:d2b_two(self.data.inviter_relation_flag or 0)
	local index = -1
	for k,v in pairs(relation_flag) do
		if v == 1 then
			index = k
			break
		end
	end
	local relation_str = Language.NewTeam.TeamMateRelation[index + 2]
	if index + 2 == 1 then
		relation_str = ToColorStr(relation_str, COLOR3B.DEFAULT)
	end
	self.node_list["lbl_relation"].text.text = relation_str

	--local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.req_role_level)
	--if is_vis then
	--	self.node_list["label_level"].text.text = "         "..role_level
	--else
	--	self.node_list["label_level"].text.text = "Lv."..role_level--RoleWGData.GetLevelString(self.data.level)
	--end
	--self.node_list.img_fire:SetActive(is_vis)
	--self.node_list["label_prof"].text.text = tostring(Language.Common.ProfName[self.data.req_role_prof])
end

function CrossTeamInviteReqRender:OnSelectChange(is_select)
	--if true == is_select then
	--	self.node_list.img_choose:SetActive(true)	--高亮
	--else
	--	self.node_list.img_choose:SetActive(false)	--非高亮
	--end
end