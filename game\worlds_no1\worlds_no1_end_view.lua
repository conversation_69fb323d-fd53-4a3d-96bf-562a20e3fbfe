-- 天下第一结算面板
WorldsNO1EndView = WorldsNO1EndView or BaseClass(SafeBaseView)
local AUTO_CLOSE_SECONDS = 10 
function WorldsNO1EndView:__init()
	self:SetMaskBg(false)
	self.active_close = false
	self.view_layer = UiLayer.PopTop
	self:AddViewResource(0, "uis/view/worlds_no1_ui_prefab", "layout_worlds_no1_end")
end

function WorldsNO1EndView:__delete()
end

function WorldsNO1EndView:ReleaseCallBack()
	self.end_info = nil
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end

	CountDownManager.Instance:RemoveCountDown("WorldsNO1EndView")

	self.open_time_stamp = nil
end

function WorldsNO1EndView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_comfire"], BindTool.Bind(self.Close, self))
	self.rank_list = AsyncListView.New(WorldsNO1EndRankItem, self.node_list["rank_list"])
	self.reward_item_list = AsyncListView.New(ItemCell, self.node_list["reward_item_list"])
	self.open_time_stamp = TimeWGCtrl.Instance:GetServerTime()
	self:UpdateAutoCloseCountdown()
	CountDownManager.Instance:RemoveCountDown("WorldsNO1EndView")
	CountDownManager.Instance:AddCountDown("WorldsNO1EndView", BindTool.Bind1(self.UpdateAutoCloseCountdown, self), BindTool.Bind1(self.Close, self), nil, AUTO_CLOSE_SECONDS, 0.5)
end

function WorldsNO1EndView:CloseCallBack()
	-- Field1v1WGCtrl.Instance:LeaveZhanChangScene()
end

function WorldsNO1EndView:SetData(end_info)
	self.end_info = end_info
end

function WorldsNO1EndView:OnFlush()
	-- 我的积分
	self.node_list["my_score"].text.text = string.format(Language.WorldsNO1.MyScore, self.end_info.my_score)
	-- 存活积分
	if self.end_info.finial_survival_score > 0 then
		self.node_list["survive_score"].text.text = string.format(Language.WorldsNO1.FinalSurvival, self.end_info.finial_survival_score)
	else
		self.node_list["survive_score"].text.text = ""
	end 
	-- 排名积分（淘汰赛有）
	if self.end_info.my_rank_score > 0 then
		self.node_list["my_rank_score"].text.text = string.format(Language.WorldsNO1.MyRankScore, self.end_info.my_rank_score)
	else
		self.node_list["my_rank_score"].text.text = ""
	end

	-- 我的排名
	self.node_list["my_rank"].text.text = string.format(Language.WorldsNO1.MyRank, self.end_info.my_rank)
	-- 排名列表
	self.rank_list:SetDataList(self.end_info.role_rank_info_list)
	-- 奖励列表
	if self.end_info.my_rank > 0 and not self.end_info.is_observation then
		local reward_cfg = WorldsNO1WGData.Instance:GetSingleMatchRewardCfgByRank(self.end_info.my_rank).reward_item
		local reward_list = {}
		for i = 0, #reward_cfg do
			table.insert(reward_list, reward_cfg[i])
		end
		self.reward_item_list:SetDataList(reward_list)
	end

	self.node_list["my_score_panel"]:SetActive(not self.end_info.is_observation)
	self.node_list["reward_item_list"]:SetActive(not self.end_info.is_observation)
	self.node_list["reward_panel"]:SetActive(not self.end_info.is_observation)
end

function WorldsNO1EndView:UpdateAutoCloseCountdown()
	if self.node_list["auto_close_countdown"] then
		local second = AUTO_CLOSE_SECONDS - (TimeWGCtrl.Instance:GetServerTime() - self.open_time_stamp)
		self.node_list["auto_close_countdown"].text.text = string.format(Language.WorldsNO1.AutoCloseCountdown, math.max(0, math.floor(second)) )
	end
end
-----------------------WorldsNO1EndRankItem------------
WorldsNO1EndRankItem = WorldsNO1EndRankItem or BaseClass(BaseRender)

function WorldsNO1EndRankItem:__init()

end


function WorldsNO1EndRankItem:__delete()
	self.data = nil
end

function WorldsNO1EndRankItem:OnFlush()
	if self.data == nil then return end
	local color = "#EDC7B0FF"
	if self.data.is_main_role then
		color = COLOR3B.D_GREEN
	end
	
	-- 排名
	self.node_list["rank_txt"]:SetActive(self.data.rank > 3)
	self.node_list["rank_img"]:SetActive(self.data.rank <= 3)
	if self.data.rank <= 3 then
		local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.data.rank)
		self.node_list["rank_img"].image:LoadSpriteAsync(bundle, asset, function ()
			self.node_list["rank_img"].image:SetNativeSize()
		end)
	else
		self.node_list["rank_txt"].text.text = ToColorStr(self.data.rank, color)
	end

	-- 角色名
	self.node_list["name"].text.text = ToColorStr(self.data.role_name, color)

	-- 积分
	self.node_list["score"].text.text = ToColorStr(self.data.score, color)

	-- 区服名
	local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id)		--内网无法请求PHP，先设置默认名字
	self.node_list["server_name"].text.text = ToColorStr(temp_name, color)
	self.node_list["bg_1"]:SetActive(self.index % 2 == 1)
	self.node_list["bg_2"]:SetActive(self.index % 2 == 0)
end