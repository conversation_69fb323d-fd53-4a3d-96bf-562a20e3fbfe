
FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)
-- 多人经验本(炼狱洞窟)
function FuBenPanelView:InitTeamExp()
	-- self.is_exp_des_expand = false

	self.node_list["btn_exp_pingtai"].button:AddClickListener(BindTool.Bind(self.OnClickExpPingTai, self))
	self.node_list["btn_exp_baoming_enter"].button:AddClickListener(BindTool.Bind(self.OnClickExpBaoMingEnter, self))
	self.node_list["layout_combine_mark"].button:AddClickListener(BindTool.Bind(self.OnClickCombine, self))
	self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClickBuyCount, self))
	self.node_list["btn_buy_count_tickert"].button:AddClickListener(BindTool.Bind(self.OnClickBuyTicket, self))
	self.node_list["cishu_item"].button:AddClickListener(BindTool.Bind(self.OnClickCiShuItem, self))

	local data_list = WuJinJiTanWGData.Instance:GetLingHunGuangChangCfg()
	local reward_list = data_list.other[1].reward_item

	local list = {}
	for i = 0, 10 do
		if reward_list[i] then
			table.insert(list, reward_list[i])
		else
			break
		end
	end

	self.exp_reward_list = AsyncListView.New(ItemCell, self.node_list.fb_reward_list)
	self.exp_reward_list:SetDataList(list, 3)

	self.alert_window1 = Alert.New()
	self.alert_window2 = Alert.New()

	if nil == self.tongxingzheng_data_event then
		self.tongxingzheng_data_event = BindTool.Bind1(self.TongXingZhengChangeCallback, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.tongxingzheng_data_event)
	end

	self.is_enter_fuben = false

	self.is_linghunguangchang = WuJinJiTanWGData.Instance:IsLingHunGuangChang()

	local item_cfg = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.EXP_COUNT_ITEM)
	if nil ~= item_cfg then
		local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
		self.node_list["cishu_item"]:SetActive(true)
		self.node_list["cishu_item"].image:LoadSpriteAsync(bundle, asset)
	else
		self.node_list["cishu_item"]:SetActive(false)
	end

	local other_cfg = WuJinJiTanWGData.Instance:GetWuJinJiTanOtherCfg()
	if other_cfg.fb_des then
		self.node_list["desc_team_exp"].text.text = other_cfg.fb_des
	end

	self.node_list.desc_team_exp_ads.text.text = Language.FuBenPanel.TeamExpFBAds

	if not self.exp_item_list then
		self.exp_item_list = AsyncListView.New(ItemCell, self.node_list.exp_item_list)
	end

	for i = 1, 9 do
		if Language.FuBenPanel.TeamExpShuoming[i] then
			self.node_list["txt_fb_info_sign"..i]:SetActive(true)
			self.node_list["jyb_fb_info"..i].text.text = Language.FuBenPanel.TeamExpShuoming[i]
		else
			self.node_list["txt_fb_info_sign"..i]:SetActive(false)
		end
	end
end

function FuBenPanelView:DeleteTeamExp()
	if self.exp_reward_list then
		self.exp_reward_list:DeleteMe()
		self.exp_reward_list = nil
	end

	if self.alert_window2 then
		self.alert_window2:DeleteMe()
		self.alert_window2 = nil
	end

	if self.tongxingzheng_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.tongxingzheng_data_event)
		self.tongxingzheng_data_event = nil
	end

	if self.alert_window1 then
		self.alert_window1:DeleteMe()
		self.alert_window1 = nil
	end

	if self.exp_cancel_match_alert then
		self.exp_cancel_match_alert:DeleteMe()
		self.exp_cancel_match_alert = nil
	end
	
	if self.exp_item_list then
		self.exp_item_list:DeleteMe()
		self.exp_item_list = nil
	end

	self.teamexp_btn_effect = nil
	self:StopTeamExpBuffCountDown()
end

--如果是匹配中，就是走取消匹配的逻辑，按钮文本显示 匹配中...
--如果没在匹配，就打开组队平台
function FuBenPanelView:OnClickExpPingTai()
	if NewTeamWGData.Instance:GetIsInRoomScene() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.InFbStateTip)
		return
	end
	local is_match = NewTeamWGData.Instance:GetIsMatching()
	local operate = is_match and 1 or 0
	local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
	if is_match and (now_team_type == GoalTeamType.Exp_DuJieXianZhou or now_team_type == GoalTeamType.Exp_FuMoZhanChuan) then
		if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
			if not self.exp_cancel_match_alert then
				self.exp_cancel_match_alert = Alert.New()
			end
			self.exp_cancel_match_alert:SetLableString(Language.FuBenPanel.AlertCancelMatch)
			self.exp_cancel_match_alert:SetOkFunc(function()
				if not is_linghunguangchang_fb then
					NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, GoalTeamType.Exp_DuJieXianZhou, 0)
				else
					NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, GoalTeamType.Exp_FuMoZhanChuan, 0)
				end
			end)
			self.exp_cancel_match_alert:Open()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.OnlyLeaderCanDo)
		end
	else
		if not is_linghunguangchang_fb then
			NewTeamWGData.Instance:SetTeamTypeAndMode(GoalTeamType.Exp_DuJieXianZhou, 0)
		else
			NewTeamWGData.Instance:SetTeamTypeAndMode(GoalTeamType.Exp_FuMoZhanChuan, 0)
		end
		ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
	end
end

--报名进入
function FuBenPanelView:OnClickExpBaoMingEnter(is_guide)
	if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end

	local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
	local team_type = is_linghunguangchang_fb and GoalTeamType.Exp_FuMoZhanChuan or GoalTeamType.Exp_DuJieXianZhou
	local fb_mode =  0
	--local info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
    local remain_count = total_count - cur_enter_count
 	if remain_count == 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
        self:OnClickBuyCount()
        return
    end

	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.LINGHUNGUANGCHANG)
end


function FuBenPanelView:TongXingZhengChangeCallback()
	self:OnFlushTeamExpView()
end

function FuBenPanelView:SendFBUseCombineExp()
    local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
	local vas = FuBenWGData.Instance:GetCombineStatus(is_linghunguangchang_fb) == 1-- self.node_list.layout_combine_hook:GetActive()
	if not vas then return end
	FuBenWGCtrl.Instance:SendFBUseCombine(1,is_linghunguangchang_fb)
end

function FuBenPanelView:OnClickCombine()
	local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
	local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local need_level = combine_cfg[is_linghunguangchang_fb+1].level_limit
	if need_level > role_level then
		local level_des = RoleWGData.GetLevelString(need_level)
		local str = string.format(Language.FuBenPanel.CombineLimitTips,level_des)
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		return
	end
	if self.enter_dj_times < 2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
		return
	end
	local vas = FuBenWGData.Instance:GetCombineStatus(is_linghunguangchang_fb) == 1--self.node_list.layout_combine_hook:GetActive()
	local is_combine = vas == true and 0 or 1
	if vas then
		FuBenWGCtrl.Instance:SendFBUseCombine(is_combine, is_linghunguangchang_fb)
	else
		local callback_func = function()
			--self.node_list.layout_combine_hook:SetActive(true)
		end
		FuBenWGCtrl.Instance:ShowCombinePanel(is_linghunguangchang_fb,callback_func)
	end
end

function FuBenPanelView:FlushFirstTime(value)
	-- body
	self.first_time_flag = value
end

function FuBenPanelView:OnClickAloneEnerFirst()
	if 1 ~= self.first_time_flag then
		local other_cfg = WuJinJiTanWGData.GetOtherCfg()
		local pass_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.pass_item_id)
		local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
		local cost_text = Language.Common.GoldText
		if pass_num <= 0 then 
			if bind_gold_num >= other_cfg.buy_pass_item_gold then
				cost_text = Language.Common.BindGoldText
			end 

			self.alert_window2:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient, cost_text, other_cfg.buy_pass_item_gold))
			self.alert_window2:SetOkFunc(function ()
				if RoleWGData.Instance:GetIsEnoughBindGold(other_cfg.buy_pass_item_gold) then
					ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, 1, 0, 1, other_cfg.pass_item_seq)
				else
					ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, 1, 0, 0, other_cfg.pass_item_seq)
				end
			end)
			self.alert_window2:Open()
			return
		end
	end
	self:GuideCreateTeam(TeamDataConst.GoalType.TeamExpFb, 0)
	NewTeamWGCtrl.Instance:GuildFirstEnterFb(TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_SET_FIRST_TIME,function ()
		local fuben_type = WuJinJiTanWGData.GetFuBenType()
		FuBenWGCtrl.Instance:SendEnterFB(fuben_type, FUBEN_ENTER_TYPE.ALONE, 0)-- 副本类型, 是否组队, 无意义
		FuBenPanelWGData.Instance:SetEnterType(FUBEN_ENTER_TYPE.ALONE)
	end)
end

function FuBenPanelView:GuideCreateTeam(team_type, fb_mode)
	local is_in_team = SocietyWGData.Instance:GetIsInTeam()
    if is_in_team == 1 then return end
   	NewTeamWGData.Instance:SetNowGoalInfoByTypeAndMode(team_type,fb_mode)
    local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
    NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
    local content = ""
    local main_role = Scene.Instance:GetMainRole()
end

function FuBenPanelView:OnClickAloneEner()
	self:SendFBUseCombineExp()
	local fuben_type = WuJinJiTanWGData.GetFuBenType()

	if self.enter_dj_times > 0 then
		local other_cfg = WuJinJiTanWGData.GetOtherCfg()
		local pass_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.pass_item_id)
		--local flag_data = FuBenWGData.Instance:SetCombineMark()
		--local is_combine_flag = bit:d2b(flag_data)

		local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
		local hook_type = FuBenWGData.Instance:GetCombineStatus(is_linghunguangchang_fb)
		local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
		local cost_text = Language.Common.GoldText

		if hook_type == 1 then
			local need_buy_count = FuBenWGData.Instance:GetEnterFbCombineCount(FB_COMBINE_TYPE.WUJINJITAN)-- self.enter_dj_times - pass_num

			if pass_num < need_buy_count  then
				local NUM = need_buy_count - pass_num
				if NUM <= 1 then
					if bind_gold_num >= other_cfg.buy_pass_item_gold then
						cost_text = Language.Common.BindGoldText
					end 
					self.alert_window2:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient, cost_text, other_cfg.buy_pass_item_gold))
				else
					if bind_gold_num >= other_cfg.buy_pass_item_gold * NUM then
						cost_text = Language.Common.BindGoldText
					end 
					self.alert_window2:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient1, cost_text, other_cfg.buy_pass_item_gold,NUM))
				end
				self.alert_window2:SetOkFunc(function ()
					if RoleWGData.Instance:GetIsEnoughBindGold(other_cfg.buy_pass_item_gold * NUM) then
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, NUM, 0, 1, other_cfg.pass_item_seq)
					else
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, NUM, 0, 0, other_cfg.pass_item_seq)
					end
					FuBenWGCtrl.Instance:SendEnterFB(fuben_type, FUBEN_ENTER_TYPE.ALONE, 0)
					FuBenPanelWGData.Instance:SetEnterType(FUBEN_ENTER_TYPE.ALONE)
				end)
				self.alert_window2:Open()
				return
			else
				FuBenWGCtrl.Instance:SendEnterFB(fuben_type, FUBEN_ENTER_TYPE.ALONE, 0)
				FuBenPanelWGData.Instance:SetEnterType(FUBEN_ENTER_TYPE.ALONE)
			end
		else
			if pass_num <= 0 then
				if bind_gold_num >= other_cfg.buy_pass_item_gold then
					cost_text = Language.Common.BindGoldText
				end 
				self.alert_window2:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient, cost_text, other_cfg.buy_pass_item_gold))
				self.alert_window2:SetOkFunc(function ()
					if RoleWGData.Instance:GetIsEnoughBindGold(other_cfg.buy_pass_item_gold) then
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, 1, 0, 1, other_cfg.pass_item_seq)
					else
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, 1, 0, 0, other_cfg.pass_item_seq)
					end
					FuBenWGCtrl.Instance:SendEnterFB(fuben_type, FUBEN_ENTER_TYPE.ALONE, 0)
					FuBenPanelWGData.Instance:SetEnterType(FUBEN_ENTER_TYPE.ALONE)
				end)
				self.alert_window2:Open()
				return
			end
			self.alert_window2:SetLableString(Language.FuBenPanel.AloneEnter)
			self.alert_window2:SetOkFunc(function ()
			self.is_enter_fuben = true
			FuBenWGCtrl.Instance:SendEnterFB(fuben_type, FUBEN_ENTER_TYPE.ALONE, 0)
			FuBenPanelWGData.Instance:SetEnterType(FUBEN_ENTER_TYPE.ALONE)
		end)
		self.alert_window2:Open()

		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
		return

	end
end

function FuBenPanelView:OnClickMatchExpFb()
	if 1 == self.first_time_flag then
		self:OnClickAloneEnerFirst()
		return
	end
	local is_in_team =SocietyWGData.Instance:GetIsInTeam()
    if is_in_team == 1 then
        local  is_leader = SocietyWGData.Instance:GetIsTeamLeader()
        if is_leader ~= 1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotTeamLeader)
            return
        end
    end
    self:SendFBUseCombineExp()

    local is_can_entr = FuBenPanelWGCtrl.Instance:IsCheckCanEnterFb()
    if not is_can_entr then
    	SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CanNotEnterFb)
    	return
    end
	local fuben_type = WuJinJiTanWGData.GetFuBenType()
	local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
	local cost_text = Language.Common.GoldText

	if self.enter_dj_times > 0 or 1 == self.first_time_flag then
		if 1 == SocietyWGData.Instance:GetIsInTeam() then
			if 1 == #SocietyWGData.Instance:GetTeamMemberList() then
				self:OnClickAloneEner()
			else
				local other_cfg = WuJinJiTanWGData.GetOtherCfg()
				local pass_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.pass_item_id)
				--local flag_data = FuBenWGData.Instance:SetCombineMark()
				--local is_combine_flag = bit:d2b(flag_data)
				local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
				local hook_type = FuBenWGData.Instance:GetCombineStatus(is_linghunguangchang_fb)
				if hook_type == 1 then
					local need_buy_count = self.enter_dj_times - pass_num
					if pass_num < self.enter_dj_times  then
						if need_buy_count <= 1 then
							if bind_gold_num >= other_cfg.buy_pass_item_gold then
								cost_text = Language.Common.BindGoldText
							end 
							self.alert_window2:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient, cost_text, other_cfg.buy_pass_item_gold))
						else
							if bind_gold_num >= other_cfg.buy_pass_item_gold * need_buy_count then
								cost_text = Language.Common.BindGoldText
							end 
							self.alert_window2:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient1, cost_text, other_cfg.buy_pass_item_gold,need_buy_count))
						end
						self.alert_window2:SetOkFunc(function ()
							if RoleWGData.Instance:GetIsEnoughBindGold(other_cfg.buy_pass_item_gold * need_buy_count) then
								ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, need_buy_count, 0, 1, other_cfg.pass_item_seq)
							else
								ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, need_buy_count, 0, 0, other_cfg.pass_item_seq)
							end
							self:EnterFubenLiXian(fuben_type,FUBEN_ENTER_TYPE.TEAM,0)

						end)
						self.alert_window2:Open()
						return
					else
						self:EnterFubenLiXian(fuben_type,FUBEN_ENTER_TYPE.TEAM,0)
					end
				else
					self:EnterFubenLiXian(fuben_type,FUBEN_ENTER_TYPE.TEAM,0)
				end
			end
		else
			NewTeamWGCtrl.Instance:Open({team_type = TeamDataConst.GoalType.TeamExpFb, fb_mode = 0, is_match = false})
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
	end
end

function FuBenPanelView:EnterFubenLiXian(fuben_type,is_team,layer)
	local menmber_info = SocietyWGData.Instance:GetTeamMemberList()
	local is_have_lixian = false
	for k,v in pairs(menmber_info) do
		if v.is_online ~= 1 then
			is_have_lixian = true
			break
		end
	end
	if is_have_lixian then
		self.alert_window1:SetLableString(Language.FuBenPanel.LiXianMember)
		self.alert_window1:SetOkFunc(function ()
			for k,v in pairs(menmber_info) do
				if v.is_online ~= 1 then
					SocietyWGCtrl.Instance:SendKickOutOfTeam(v.orgin_role_id)
				end
			end
			FuBenWGCtrl.Instance:SendEnterFB(fuben_type,is_team,layer)   -- 副本类型, 是否组队, 无意义
			FuBenPanelWGData.Instance:SetEnterType(is_team)
		end)
		self.alert_window1:Open()
	else
		FuBenWGCtrl.Instance:SendEnterFB(fuben_type,is_team,layer)   -- 副本类型, 是否组队, 无意义
		FuBenPanelWGData.Instance:SetEnterType(is_team)
	end
end

function FuBenPanelView:OnClickAlertWindow1OkCallBack()
	self.is_enter_fuben = true
	local fuben_type = self.is_linghunguangchang and FUBEN_TYPE.LINGHUNGUANGCHANG or FUBEN_TYPE.FBCT_WUJINJITAN
	FuBenWGCtrl.Instance:SendEnterFB(fuben_type, FUBEN_ENTER_TYPE.TEAM, 0)   -- 0是单人, 1是组队
	--self:SendEncourgeTimes()
end

function FuBenPanelView:OnClickTeamEner()
	local fuben_type = self.is_linghunguangchang and FUBEN_TYPE.LINGHUNGUANGCHANG or FUBEN_TYPE.FBCT_WUJINJITAN
	local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
	local cost_text = Language.Common.GoldText
	if self.enter_dj_times > 0 then
		local other_cfg = WuJinJiTanWGData.GetOtherCfg()
		local pass_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.pass_item_id)
		if 0 == pass_num then
			if bind_gold_num >= other_cfg.buy_pass_item_gold then
				cost_text = Language.Common.BindGoldText
			end 
			self.alert_window2:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient, cost_text, other_cfg.buy_pass_item_gold))
			self.alert_window2:SetOkFunc(function ()
				ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, 1, 0, 0, other_cfg.pass_item_seq)
			end)
			self.alert_window2:Open()
			return
		end

		if 0 == SocietyWGData.Instance:GetIsInTeam() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NoInTeamTip)
			NewTeamWGCtrl.Instance:Open({team_type = TeamDataConst.GoalType.TeamExpFb, fb_mode = 0, is_match = false})
		else
			if 1 == #SocietyWGData.Instance:GetTeamMemberList() then
				self.alert_window2:SetLableString(Language.NewTeam.EnterTip)
				self.alert_window2:SetOkFunc(BindTool.Bind1(self.OnClickAlertWindow1OkCallBack, self))
				self.alert_window2:Open()
			else
				self:OnClickAlertWindow1OkCallBack()
			end
		end
	else
		local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
		local param = VipPower.Instance:GetParam(VipPowerId.exp_fb_buy_times)
		if param - yet_buy_time > 0 then
			--FuBenPanelWGCtrl.Instance:OpenTeamExpBuyView(FUBEN_TYPE.FBCT_WUJINJITAN)
			FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_WUJINJITAN)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
		end
	end
end

function FuBenPanelView:OnClickCiShuItem()
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = COMMON_CONSTS.EXP_COUNT_ITEM})
end

function FuBenPanelView:OnClickBuyTicket()
	local num1 = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.EXP_COUNT_ITEM)
	local num2 = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.EXP_COUNT_ITEM_VIP)
	local total_num = num1 + num2
	local user_ticket_time = WuJinJiTanWGData.Instance:GetTeamExpUseTicketTimes()
	local riyue_other_cfg = WuJinJiTanWGData.Instance:GetWuJinJiTanOtherCfg()
	local max_use_time = riyue_other_cfg.max_use_ticket_times
	local can_add_time = math.min(max_use_time - user_ticket_time, total_num)

	if max_use_time - user_ticket_time <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Tip.TipMaxAddNumStr)
		return
	elseif total_num <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.ExpCountItemDeficiency)
		return
	end

	local item_data = ItemWGData.Instance:GetItem(COMMON_CONSTS.EXP_COUNT_ITEM)
	local data = {
		item_data = { item_id = COMMON_CONSTS.EXP_COUNT_ITEM },
		has_num = total_num, price = 1,
		max_num = can_add_time,
		add_error = total_num >= max_use_time and Language.Tip.TipMaxAddNumStr or Language.Common.MaxValue,
		ok_func = function(consume_num)
			BagWGCtrl.Instance:SendUseItem(item_data.index, GameMath.Round(consume_num), 0)
		end
	}
	TipWGCtrl.Instance:ShowTipCommonUseView(data)
	-- OfflineRestWGCtrl.Instance:OpenUserOfflineView(COMMON_CONSTS.EXP_COUNT_ITEM, can_add_time)
end

function FuBenPanelView:SendEncourgeTimes()
	local other_cfg = WuJinJiTanWGData.GetOtherCfg()
	local per_gold_consume = other_cfg.gold_guwu_cost
	local per_coin_consume = other_cfg.coin_guwu_cost
	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	local all_gold = main_role_vo.gold + main_role_vo.bind_gold
	local all_coin = main_role_vo.coin
	local can_buy_gold_count = math.floor(all_gold/per_gold_consume)
	local can_buy_coin_count = math.floor(all_coin / per_coin_consume)
	can_buy_gold_count = math.floor(all_gold/per_gold_consume) <= 5 and math.floor(all_gold/per_gold_consume) or 5
	can_buy_coin_count = math.floor(all_coin / per_coin_consume) <= 5 and math.floor(all_coin / per_coin_consume) or 5
end

function FuBenPanelView:OnClickBuyCount()
	if self.is_linghunguangchang then
		FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.LINGHUNGUANGCHANG)
	else
		FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.LINGHUNGUANGCHANG)
	end
end

function FuBenPanelView:OnFlushTeamExpView()
	self:FlushTeamExpUtemList()
	self:FlushTeamExpBuffTime()

	FuBenWGCtrl.Instance:SendWuJinJiTanReq(TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_FIRST_TIME)
	self.is_linghunguangchang = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
	--self.node_list.ph_show_item:SetActive(not self.is_linghunguangchang )
	--self.node_list.linghun_reward_list:SetActive(self.is_linghunguangchang )
	--local flag_data = FuBenWGData.Instance:SetCombineMark()
	local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
    local is_combine_flag = FuBenWGData.Instance:GetCombineStatus(is_linghunguangchang_fb)
    self.node_list.layout_combine_hook:SetActive(is_combine_flag == 1)
	local is_linghunguangchang = WuJinJiTanWGData.Instance:IsLingHunGuangChang() --是否有改变
	if self.is_linghunguangchang ~= is_linghunguangchang then
		self.is_linghunguangchang = is_linghunguangchang
	end
	if self.is_enter_fuben == false then
	FuBenPanelWGData.Instance:SetIsSelect(false)
	end
	self.is_enter_fuben = false

	--local is_linghunguangchang_fb =  WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
	local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local pre_show_level = combine_cfg[is_linghunguangchang_fb+1].pre_show_level
	self.node_list["layout_combine_mark_root"]:SetActive(role_level >= pre_show_level)

	local other_cfg = WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
	local yet_enter_time = WuJinJiTanWGData.Instance:GetTeamExpFbEnterTimes()
	local yet_buy_time = WuJinJiTanWGData.Instance:GetTeamFbBuyTimes()
	local user_ticket_time = WuJinJiTanWGData.Instance:GetTeamExpUseTicketTimes()

	self.enter_dj_times = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
	self.node_list["text_exp_fb_count"].text.color = Str2C3b(self.enter_dj_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.L_RED)
	self.node_list["text_exp_fb_count"].text.text = self.enter_dj_times  .. "/" .. other_cfg.everyday_times + yet_buy_time + user_ticket_time

	local riyue_other_cfg = WuJinJiTanWGData.Instance:GetWuJinJiTanOtherCfg()
	local max_use_time = riyue_other_cfg.max_use_ticket_times
	local can_add_time = max_use_time - user_ticket_time > 0
	-- self.node_list.exp_add_time_root:CustomSetActive(can_add_time)
	self.node_list["exp_today_add_time"].text.color = Str2C3b(can_add_time and COLOR3B.DEFAULT_NUM or COLOR3B.L_RED)
	self.node_list["exp_today_add_time"].text.text = max_use_time - user_ticket_time .. "/" .. max_use_time

	--按钮文本
	local is_match = NewTeamWGData.Instance:GetIsMatching()
	local team_type, fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
	self.node_list.btn_exp_pingtai_text.text.text = (is_match and (team_type == GoalTeamType.Exp_FuMoZhanChuan or team_type == GoalTeamType.Exp_DuJieXianZhou)) and Language.NewTeam.IsMatching or Language.NewTeam.AutoMatch
	self.node_list.exp_pingtai_effect:SetActive(is_match and (team_type == GoalTeamType.Exp_FuMoZhanChuan or team_type == GoalTeamType.Exp_DuJieXianZhou))

	local need_tickets = FuBenWGData.Instance:GetEnterFbCombineCount(FB_COMBINE_TYPE.WUJINJITAN)
	need_tickets = need_tickets > 0 and need_tickets or 1

	self.node_list["img_exp_double"]:SetActive(self:IsDoubleExp())
	self:FlushJingYanEffect()

	local vip_level = GameVoManager.Instance:GetMainRoleVo().vip_level
	local num1 = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.EXP_COUNT_ITEM)
	local num2 = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.EXP_COUNT_ITEM_VIP)
	local total_num = num1 + num2
	if vip_level >= 4 then
		self.node_list.exp_use_effect:SetActive(total_num > 0)
	else
		self.node_list.exp_use_effect:SetActive(num1 > 0)
	end

	self.node_list["lbl_tickets"].text.text = ToColorStr(total_num, COLOR3B.DEFAULT_NUM)

	local hook_type = FuBenWGData.Instance:GetCombineStatus(is_linghunguangchang_fb)
 	self.node_list.layout_combine_hook:SetActive(hook_type == 1)
end

function FuBenPanelView:FlushJingYanEffect()
	if not self:IsOpen() or not self:IsLoadedIndex(TabIndex.fubenpanel_exp) then
        return
    end
	self.is_linghunguangchang = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
	local now_count = VipPower.Instance:GetParam(VipPowerId.exp_fb_buy_times)
	local yet_buy_time = WuJinJiTanWGData.Instance:GetTeamFbBuyTimes()

	local have_count = now_count - yet_buy_time
	local enter_times = WuJinJiTanWGData.Instance:GetTeamExpFbEnterTimes()
	local total_times = self:GetExpTotalCount()
	local remain_times = total_times - enter_times
	--self.node_list.exp_effect:SetActive(have_count > 0 and remain_times == 0 and FuBenPanelWGData.Instance:CheckVipCondition())
end

function FuBenPanelView:CheckTeamExpCount()
	local other_cfg = WuJinJiTanWGData.GetOtherCfg()
	local yet_enter_time = WuJinJiTanWGData.GetFBEnterTimes()
	local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
	local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
	local enter_dj_times = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
	return enter_dj_times > 0
end

function FuBenPanelView:GetExpTotalCount()
	local other_cfg = WuJinJiTanWGData.GetOtherCfg()
	local yet_enter_time = WuJinJiTanWGData.GetFBEnterTimes()
	local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
	local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
	local total_times = other_cfg.everyday_times + yet_buy_time + user_ticket_time
	return total_times
end

function FuBenPanelView:IsDoubleExp()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE) then
		local act_status = ServerActivityWGData.Instance:GetVersionActivityNowStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE)
		if act_status and act_status.status == ACTIVITY_STATUS.OPEN then
			return true
		end
	end
	if DOUBLE_EXP_FLAG_BY_GOBALXUNBAO then
		return true
	end
	return false
end

function FuBenPanelView:GetFuBenType()
	return WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FUBEN_TYPE.LINGHUNGUANGCHANG or FUBEN_TYPE.FBCT_WUJINJITAN
end

function FuBenPanelView:FlushTeamExpUtemList()
	local other_cfg = WuJinJiTanWGData.Instance:GetWuJinJiTanOtherCfg()
	local str = other_cfg.exp_medicicn_seq
	local list = Split(str, "|")
	local data_list = {}
	for i, v in ipairs(list) do
		local item_id = tonumber(v)
		local has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
		table.insert(data_list, {item_id = item_id, num = has_num})
	end

	self.exp_item_list:SetDataList(data_list)
end

function FuBenPanelView:FlushTeamExpBuffTime()
	if not self.node_list then	
		return
	end

	-- 刷新经验BUFF
	local exp_budd_list = FightWGData.Instance:GetExpBuff()

	if not IsEmptyTable(exp_budd_list) then
		local cd_time = exp_budd_list.param_list[1] or 0
		local cd_total_time = math.max(cd_time / 1000 - (Status.NowTime - exp_budd_list.recv_time), 0)

		if cd_total_time > 0 then
			if CountDownManager.Instance:HasCountDown("team_exp_buff_time") then
				CountDownManager.Instance:RemoveCountDown("team_exp_buff_time")
			end

			local add_exp = exp_budd_list.param_list[3] or 0
			local target_add_per = add_exp / 10000 + 1

			if self.node_list["desc_exp_item_info"] ~= nil then
				self.node_list["desc_exp_item_info"].text.text = string.format(Language.FuBenPanel.TeamExpBuffDesc, target_add_per, TimeUtil.FormatSecondDHM3(cd_total_time))
			end
			CountDownManager.Instance:AddCountDown("team_exp_buff_time", 
			function (elapse_time, total_time)
				local on_time = math.ceil(total_time - elapse_time)
				on_time = TimeUtil.FormatSecondDHM3(on_time)

				if self.node_list["desc_exp_item_info"] ~= nil then
					self.node_list["desc_exp_item_info"].text.text = string.format(Language.FuBenPanel.TeamExpBuffDesc, target_add_per, on_time)
				end
			end, 
			function ()
				self:StopTeamExpBuffCountDown()
			end, nil, cd_total_time, 1)
		else
			self:StopTeamExpBuffCountDown()	
		end
	else
		self:StopTeamExpBuffCountDown()
	end
end

function FuBenPanelView:StopTeamExpBuffCountDown()
	if self.node_list["desc_exp_item_info"] ~= nil then
		self.node_list["desc_exp_item_info"].text.text = Language.FuBenPanel.TeamExpNoBuffDesc
	end

	if CountDownManager.Instance:HasCountDown("team_exp_buff_time") then
		CountDownManager.Instance:RemoveCountDown("team_exp_buff_time")
	end
end

-- 改成只用无尽祭坛的次数
function FuBenPanelView:GetFBEnterTimes()
	return WuJinJiTanWGData.Instance:GetTeamExpFbEnterTimes()
end

function FuBenPanelView:GetFBBuyTimes()
	return WuJinJiTanWGData.Instance:GetTeamFbBuyTimes()
end

function FuBenPanelView:GetFBAddTimes()
	return WuJinJiTanWGData.Instance:GetTeamExpUseTicketTimes()
end

---------------------ExpRewardCell--------------------------------
ExpRewardCell = ExpRewardCell or BaseClass(BaseRender)
function ExpRewardCell:__init()
	self.base_cell = ItemCell.New(self.node_list["pos"])
    self.base_cell:SetIsShowTips(true)
end

function ExpRewardCell:__delete()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end

function ExpRewardCell:OnFlush()
	self.base_cell:SetData(self.data)
    self.node_list.three_flag:SetActive(false)
    self.node_list.new_flag:SetActive(false)
end
