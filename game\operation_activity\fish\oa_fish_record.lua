OAFishRecord = OAFishRecord or BaseClass(SafeBaseView)

function OAFishRecord:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830,480)})
    self:AddViewResource(0, "uis/view/operation_fish_prefab", "layout_ts_luckyfish_record")
	self:SetMaskBg(true, true)
end

function OAFishRecord:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
    self.data_list = nil
end

function OAFishRecord:ShowIndexCallBack()
    OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.RECORD)
    OAFishWGData.Instance:UpdateRecordCount()
end

function OAFishRecord:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.OAFish.RewardListTitle
    self.record_list = AsyncListView.New(OAFishRecordRender, self.node_list["record_list"])
    --self.record_list:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize,self))
end

local LINE_SPACING = 20
function OAFishRecord:ChangeCellSize(data_index)
    local data = self.data_list and self.data_list[data_index + 1] 
    if not data then return 0 end

    local cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not cfg then
        print_error('物品配置读取不到   item_id:',data.item_id)
        return 0
    end
    local str = string.format(Language.OAFish.Record, data.role_name, ITEM_COLOR[cfg.color], cfg.name, data.num)

    self.node_list["TestText"].text.text = str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)

    local hight = math.ceil(self.node_list["TestText"].rect.rect.height) + LINE_SPACING
    return hight or 0
end

function OAFishRecord:OnFlush()
    local data_list = OAFishWGData.Instance:GetRecordInfo()

    ---[[ 大奖排前面
    local grade_cfg = OAFishWGData.Instance:GetCurActGradeCfg()
    local reward_pool_id = grade_cfg and grade_cfg.reward
    local reward_cfg_list = OAFishWGData.Instance:GetRewardCfgList(reward_pool_id)
    if reward_cfg_list then
        local reward_type_list = {}
        for k,v in pairs(reward_cfg_list) do
            reward_type_list[v.reward_item.item_id] = v.reward_type
        end
  
        local temp_list = {}
        local index = 0
        for i=1,#data_list do
            index = reward_type_list[data_list[i].item_id] or 0
            temp_list[index * 200 + i] = data_list[i]
        end
        data_list = SortTableKey(temp_list)
    end
    --]]

    self.data_list = data_list
    self.record_list:SetDataList(data_list)
    self.node_list["no_flag"]:SetActive(IsEmptyTable(data_list))
end

function OAFishRecord:CloseCalBack()
    OAFishWGData.Instance:UpdateRecordCount()
end
-------------------------------------------------------------------------------------
OAFishRecordRender = OAFishRecordRender or BaseClass(BaseRender)

function OAFishRecordRender:OnFlush()
    if not self.data then
        return
    end

    -- self.node_list['time'].text.text = TimeUtil.FormatYMDHMS(self.data.draw_time)
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if not cfg then
        print_error('物品配置读取不到   item_id:',self.data.item_id)
        return
    end
    local str = string.format(Language.OAFish.Record, self.data.role_name, ITEM_COLOR[cfg.color], cfg.name, self.data.num)
    self.node_list["info"].text.text = str
    --self.node_list.bg:SetActive(self:GetIndex() % 2 == 0)
end