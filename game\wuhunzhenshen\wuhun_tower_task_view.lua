WuHunTowerTaskView = WuHunTowerTaskView or BaseClass(SafeBaseView)

function WuHunTowerTaskView:__init()
	self:AddViewResource(0, "uis/view/wuhunzhenshen_prefab", "layout_wuhun_tower_task")
	self:AddViewResource(0, "uis/view/wuhunzhenshen_prefab", "layout_wuhun_tower_follow")
	self.view_cache_time = 0
    self.active_close = false
end

function WuHunTowerTaskView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	self.is_out_fb = nil

	if self.reward_one_item_list then
        self.reward_one_item_list:DeleteMe()
        self.reward_one_item_list = nil
    end

    if self.reward_ten_item_list then
    	self.reward_ten_item_list:DeleteMe()
        self.reward_ten_item_list = nil
    end

    if self.show_top_event then
    	GlobalEventSystem:UnBind(self.show_top_event)
    	self.show_top_event = nil
    end

	if self.main_menu_icon_change then
    	GlobalEventSystem:UnBind(self.main_menu_icon_change)
    	self.main_menu_icon_change = nil
    end

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    self.show_model_id = nil
end

function WuHunTowerTaskView:LoadCallBack()
    if not self.reward_one_item_list then
	    self.reward_one_item_list = AsyncListView.New(ItemCell, self.node_list.reward_one_item_list)
    end

    if not self.reward_ten_item_list then
	    self.reward_ten_item_list = AsyncListView.New(ItemCell, self.node_list.reward_ten_item_list)
    end

    ----[[模型展示
    if self.show_model == nil then
        self.show_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        
        self.model_display:SetRenderTexUI3DModel(display_data)
        -- self.show_model:SetUI3DModel(self.node_list["display"].transform, nil, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.show_model)
    end

    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
end

function WuHunTowerTaskView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["layout_wuhun_tower"] then
		self.obj = self.node_list["layout_wuhun_tower"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0,0,0)
		self.obj.transform.localScale = Vector3.one
	end

	if self.is_out_fb then
        self.obj:SetActive(false)
    end
    self.is_out_fb = nil

    self.show_top_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK,
    BindTool.Bind(self.ShowTopEvent, self))

    self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, 
    BindTool.Bind1(self.ShowTopEvent, self))

    self:ShowTopEvent(false)
end

function WuHunTowerTaskView:ShowTopEvent(ison)
	self.node_list.wuhun_tower_panel.transform:DOAnchorPosY(ison and 380 or -38, 0.8)
end

function WuHunTowerTaskView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function WuHunTowerTaskView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function WuHunTowerTaskView:OnFlush()
	local tower_scene_info = WuHunTowerWGData.Instance:GetTowerSceneInfo()

	if not tower_scene_info then
		return
	end

	local one_data, ten_data, is_max = WuHunTowerWGData.Instance:GetCurrLevelRewardCfg(tower_scene_info.tower_type, tower_scene_info.tower_seq)

	if self.reward_one_item_list and one_data then
        local real_reward_item = {}

        for _, reward_data in pairs(one_data.pass_reward_item) do
            if reward_data and reward_data.item_id then
                table.insert(real_reward_item, reward_data)
            end
        end

		self.reward_one_item_list:SetDataList(real_reward_item)
	end

    if self.reward_ten_item_list and ten_data then
        local real_reward_item = {}

        for _, reward_data in pairs(ten_data.pass_reward_item) do
            if reward_data and reward_data.item_id then
                table.insert(real_reward_item, reward_data)
            end
        end

		self.reward_ten_item_list:SetDataList(real_reward_item)
        self.node_list.tongguan_jinagli.text.text = string.format(Language.FuBen.TongGuanJiangLi, ten_data.seq, tower_scene_info.tower_seq, ten_data.seq)
	end

    -- 刷新怪物名称
    local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto")
	local boss_id = one_data and one_data.boss_id

    if monster_cfg and monster_cfg.monster_list and monster_cfg.monster_list[boss_id] then
        local boss_config = monster_cfg.monster_list[boss_id]
		self.node_list.lbl_mubiao.text.text = string.format(Language.FuBen.XiuZhenLuTaskTitle, tower_scene_info.tower_seq, ToColorStr(boss_config.name, COLOR3B.D_PURPLE))
    end

	-- 刷新推荐战力
	local zhanli_tuijian = one_data and one_data.need_cap or 0
	local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
	local is_green = role_zhanli >= zhanli_tuijian	
	self.node_list.tuijian_zhanli.text.text = string.format(Language.FuBen.TuiJianZhanLi, ToColorStr(zhanli_tuijian, is_green and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED))

    local is_show_wuhun = one_data and one_data.need_wuhun ~= nil and one_data.need_wuhun > 0
    self.node_list.wuhun_tower_panel:CustomSetActive(is_show_wuhun)

    if is_show_wuhun and self.show_model and one_data then
        local active = WuHunWGData.Instance:GetWuHunActiveCfg(one_data.need_wuhun)
		local curr_wuhun_appeid = active and active.appe_image_id or 10114

        if self.show_model_id ~= curr_wuhun_appeid then
            local bundle, asset = ResPath.GetWuHunModel(curr_wuhun_appeid)
            self.show_model:SetMainAsset(bundle, asset, function ()
                self.show_model:PlayRoleAction(SceneObjAnimator.Rest)
            end)

            self.show_model_id = curr_wuhun_appeid
        end
    end
end
