HeFuMainBtn = HeFuMainBtn or BaseClass(BaseRender)

function HeFuMainBtn:LoadCallBack()
end

function HeFuMainBtn:__delete()
    if CountDownManager.Instance:HasCountDown("HeFuMainBtn_key") then
        CountDownManager.Instance:RemoveCountDown("HeFuMainBtn_key")
    end
end

function HeFuMainBtn:OnFlush()
    --倒计时
    if CountDownManager.Instance:HasCountDown("HeFuMainBtn_key") then
        CountDownManager.Instance:RemoveCountDown("HeFuMainBtn_key")
    end

    local time = HeFuWGData.Instance:GetHeFuInfo().merge_server_time
    local remain_time = time - TimeWGCtrl.Instance:GetServerTime()

    if remain_time > 0 then
        self:UpdateTime(0, remain_time)
        CountDownManager.Instance:AddCountDown("HeFuMainBtn_key",
                BindTool.Bind(self.UpdateTime, self),
                BindTool.Bind(self.CompleteTime, self), nil, remain_time, 1)
        self.node_list.Image_bg:SetActive(true)
    else
        self.node_list.Image_bg:SetActive(false)
    end

    local is_can_active = false
    local has_fetch = HeFuWGData.Instance:HasFetchReward()
    local can_fetch = not has_fetch and HeFuWGData.Instance:GetIsCombineServer()
    local level_condition = HeFuWGData.Instance:IsLevelConditionOK()
    local not_open_hefu = HeFuWGData.Instance:GetIsNotOpenHeFu()
    if not level_condition then
        is_can_active = false
    elseif not_open_hefu then
        is_can_active = false
    elseif has_fetch then
        is_can_active = false
    elseif not has_fetch then
        is_can_active = true
    elseif can_fetch then
        is_can_active = true
    end

    self:SetActive(is_can_active)
end

function HeFuMainBtn:ClacTimeShow(time)
    if time > 0 then
        local time_tab = TimeUtil.Format2TableDHM2(time)
        if time_tab.day >= 1 then
            return string.format(Language.HeFu.EndTimeDay, time_tab.day)
        elseif time_tab.day < 1 and time_tab.hour >= 1 then
            return string.format(Language.HeFu.EndTimeHour, time_tab.hour)
		elseif time_tab.hour < 1 then
            return string.format("%02d:%02d", time_tab.min, time_tab.sec)
		end
    else
        return string.format("%02d:%02d:%02d", 0, 0, 0)
    end
end

function HeFuMainBtn:UpdateTime(elapse_time, total_time)
    local temp_seconds = GameMath.Round(total_time - elapse_time)
    if self.node_list and self.node_list.time then
        local calc_time = self:ClacTimeShow(temp_seconds)
        self.node_list.time.text.text = calc_time
    end
end

function HeFuMainBtn:CompleteTime(elapse_time, total_time)
    if self.node_list and self.node_list.end_time then
        self.node_list.time.text.text = ""
    end

    self:Flush()
end
