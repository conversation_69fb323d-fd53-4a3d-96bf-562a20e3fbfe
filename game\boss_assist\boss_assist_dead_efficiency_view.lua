--Boss的死亡效率.
BossDeadEfficiencyView = BossDeadEfficiencyView or BaseClass(SafeBaseView)

local TWEEN_INFO = {
	FromScale = Vector3(1.3, 1.3, 1.3),
	ToScale = Vector3(1, 1, 1),
	FromAlpha = 0.6,
	ToAlpha = 1,
	ScaleTweenTime = 0.3,
	AlphaTweenTime = 0.2,
	ScaleTweenType = DG.Tweening.Ease.OutExpo,
	AlphaTweenType = DG.Tweening.Ease.OutExpo
}

local EFFECT_NAME = {
	[1] = "UI_piaozi_C",
	[2] = "UI_piaozi_B",
	[3] = "UI_piaozi_A",
	[4] = "UI_piaozi_S",
	[5] = "UI_piaozi_SS",
	[6] = "UI_piaozi_SSS",
}

function BossDeadEfficiencyView:__init()
	self.view_layer = UiLayer.MainUILow
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self.view_name = "BossDeadEfficiencyView"
	self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_boss_dead_efficiency_view")
end

function BossDeadEfficiencyView:LoadCallBack()
	self.obj_delete_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DELETE, BindTool.Bind(self.OnObjDeleteHead, self))
	self.obj_dead_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnObjDeleteHead, self))

	self.target_obj_id = -1
	self.effect_obj = nil

	XUI.AddClickEventListener(self.node_list.attack_efficiency_btn, BindTool.Bind(self.OnClickRuleBtn, self))
end

function BossDeadEfficiencyView:ReleaseCallBack()
	if self.obj_delete_event then
		GlobalEventSystem:UnBind(self.obj_delete_event)
		self.obj_delete_event = nil
	end

	if self.obj_dead_event then
		GlobalEventSystem:UnBind(self.obj_dead_event)
		self.obj_dead_event = nil
	end

	self.fill_tween = nil
end

function BossDeadEfficiencyView:OnFlush()
	local target_obj = SceneObj.select_obj
	if nil == target_obj or target_obj:IsDeleted() or target_obj:GetType() == SceneObjType.MainRole then
		return
	end

	self.target_obj_id = target_obj:GetObjId()
	local boss_hp_record_info = BossAssistWGData.Instance:GetBossHpRecordInfo(self.target_obj_id)
	if IsEmptyTable(boss_hp_record_info) then
		return
	end

	local bg_res = "a3_efficiency_bg_" .. boss_hp_record_info.record_index
	local img_res = "a3_efficiency_" .. boss_hp_record_info.record_index
	local bg_bundle, bg_asset = ResPath.GetMainUIIcon(bg_res)
	self.node_list.attack_efficiency_bg.image:LoadSprite(bg_bundle, bg_asset, function()
		self.node_list.attack_efficiency_bg.image:SetNativeSize()
	end)

	-- local img_bundle, img_asset = ResPath.GetMainUIIcon(img_res)
	-- self.node_list.attack_efficiency_img.image:LoadSprite(img_bundle, img_asset, function()
	-- 	self.node_list.attack_efficiency_img.image:SetNativeSize()
	-- end)

	local effect_bundle, effect_asset = ResPath.GetEffectUi(EFFECT_NAME[boss_hp_record_info.record_index])
	if effect_bundle and effect_asset then
		self.node_list.attack_efficiency_effect:ChangeAsset(effect_bundle, effect_asset, nil,
			function(new_obj)
				self.effect_obj = new_obj
			end)
	end

	self:DoBossDeadEfficiencyTween(boss_hp_record_info.record_pro, boss_hp_record_info.record_index)
end

function BossDeadEfficiencyView:OnObjDeleteHead(obj)
	if obj:GetObjId() == self.target_obj_id then
		BossAssistWGData.Instance:RemoveBossHpRecordInfo(self.target_obj_id)
		self:Close()
	end
end

function BossDeadEfficiencyView:DoBossDeadEfficiencyTween(record_pro, record_index)
	local play_time = 0.2

	UITween.CleanScaleAlaphaShow(self.view_name)
	UITween.DoScaleAlaphaShow(self.view_name, self.node_list.attack_efficiency_tween_root, TWEEN_INFO)
	-- self:KillFillAmountTweener()
	-- self.node_list.attack_efficiency_img.image.fillAmount = record_pro
	-- self.fill_tween = self.node_list.attack_efficiency_img.image:To(0, play_time)

	if self.effect_obj then
		local anim = self.effect_obj:GetComponent(typeof(UnityEngine.Animator))
		anim:Play(EFFECT_NAME[record_index], -1, record_pro)
		anim.speed = play_time;
	end
end

function BossDeadEfficiencyView:KillFillAmountTweener()
	if self.fill_tween then
		self.fill_tween:Kill()
		self.fill_tween = nil
	end
end

function BossDeadEfficiencyView:OnClickRuleBtn()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.BossDeadEfficiency.TipsTitle)
	rule_tip:SetContent(Language.BossDeadEfficiency.TipsContent, nil, nil, nil, true)
end