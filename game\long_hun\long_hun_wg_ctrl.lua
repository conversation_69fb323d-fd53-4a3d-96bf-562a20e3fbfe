require("game/long_hun/long_hun_wg_data")
require("game/long_hun/long_hun_view")
require("game/long_hun/long_hun_fen_jie_view")
require("game/long_hun/long_hun_opera_view")
require("game/long_hun/long_hun_up_grade")
require("game/long_hun/long_hun_fenjie_result")
require("game/long_hun/long_hun_show_way")


LongHunWGCtrl = LongHunWGCtrl or BaseClass(BaseWGCtrl)
function LongHunWGCtrl:__init()
	if LongHunWGCtrl.Instance ~= nil then
		ErrorLog("[LongHunWGCtrl] Attemp to create a singleton twice !")
	end
	LongHunWGCtrl.Instance = self

	self.view = LongHunView.New(GuideModuleName.LongHunView)
	self.data = LongHunWGData.New()
	self.fenjie_view = LongHunFenJieView.New()
	self.long_hun_result = LongHunResultTips.New()
	self.longhun_opera_view = LongHunOperaView.New()
	self.longhun_upgrade_view = LongHunUpGradeView.New()
	self.longhun_showway_view = LongHunShowWayView.New()
	self:RegisterAllProtocols()
	self.need_flush_and_hold = false

end

function LongHunWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.longhun_upgrade_view then
		self.longhun_upgrade_view:DeleteMe()
		self.longhun_upgrade_view = nil
	end

	if self.fenjie_view then
		self.fenjie_view:DeleteMe()
		self.fenjie_view = nil
	end

	if self.longhun_showway_view then
		self.longhun_showway_view:DeleteMe()
		self.longhun_showway_view = nil
	end
	LongHunWGCtrl.Instance = nil
end


function LongHunWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSLifesoulOperator)
	self:RegisterProtocol(CSLifesoulDeCompose)
	self:RegisterProtocol(CSLifesoulCompose)
	self:RegisterProtocol(SCLifesoulBagInfo,"OnSCLifesoulBagInfo")
	self:RegisterProtocol(SCLifesoulGridInfo,"OnSCLifesoulGridInfo")
	self:RegisterProtocol(SCLifesoulBaseInfo,"OnSCLifesoulBaseInfo")
	self:RegisterProtocol(SCLifesoulItemAddNotice,"OnSCLifesoulItemAddNotice")
end


function LongHunWGCtrl:OnSCLifesoulBagInfo(protocol)
	self.data:OnSCLifesoulBagInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
	self.data:InitFenJieMark()
	GlobalEventSystem:Fire(OtherEventType.LONG_HUN_BAG_CHANGE,"")

	RemindManager.Instance:Fire(RemindName.LongHun_XiangQian)
	RemindManager.Instance:Fire(RemindName.Compose_LongHun) 
end
function LongHunWGCtrl:OnSCLifesoulGridInfo(protocol)
	self.data:OnSCLifesoulGridInfo(protocol)
	-- body
	GlobalEventSystem:Fire(OtherEventType.LONG_HUN_BAG_CHANGE,"")
	if self.view:IsOpen() then
		self.view:Flush()
	end

	if self.fenjie_view:IsOpen() then
		
		self.fenjie_view:Flush()
	end

	self.data:InitFenJieMark()
	RemindManager.Instance:Fire(RemindName.LongHun_XiangQian)
	RemindManager.Instance:Fire(RemindName.Compose_LongHun) 
end
function LongHunWGCtrl:OnSCLifesoulBaseInfo(protocol)
	self.data:OnSCLifesoulBaseInfo(protocol)
	-- body
	GlobalEventSystem:Fire(OtherEventType.LONG_HUN_BAG_CHANGE,"")
	if self.view:IsOpen() then
		self.view:Flush()
		GlobalEventSystem:Fire(OtherEventType.LONG_HUN_EQUIP_CHANGE,"")
	end
	if self.longhun_upgrade_view:IsOpen() then --and self.need_flush_and_hold then
		self.longhun_upgrade_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.LongHun_XiangQian)
	RemindManager.Instance:Fire(RemindName.Compose_LongHun) 
end

function LongHunWGCtrl:OpenLongHunFenJie()
	if not self.fenjie_view then 
		self.fenjie_view = LongHunFenJieView.New()
	end
	if self.fenjie_view:IsOpen() then
		self.fenjie_view:Flush()
	else
		self.fenjie_view:Open()
		self.fenjie_view:Flush()
	end
end

function LongHunWGCtrl:SendMingWenCompose(item_id,count, index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLifesoulCompose)
	protocol.item_id = item_id or 0
	protocol.count = count or 0
	protocol.index_list = index_list or {}
	protocol:EncodeAndSend()
	
end

function LongHunWGCtrl:OpenLongHunOpera(data,from_view,click_call_back,need_flush_and_hold)
	TipWGCtrl.Instance:OpenItem(data,from_view,nil,nil,click_call_back)
	-- if self.longhun_opera_view then
	-- 	self.need_flush_and_hold = need_flush_and_hold
	-- 	self.longhun_opera_view:SetData(data,from_view,click_call_back,need_flush_and_hold)
	-- 	self.longhun_opera_view:Open()
	-- 	self.longhun_opera_view:Flush()
	-- end
end

-- LIFESOUL_OPERATOR_TYPE = 
-- {
-- 	LIFESOUL_OPERATOR_TYPE_BASE_INFO = 0,	-- 基本信息
-- 	LIFESOUL_OPERATOR_TYPE_UP_LEVEL = 1,	-- 升级 p1(slot) 特殊槽不能升级
-- 	LIFESOUL_OPERATOR_TYPE_BAG_INFO = 2,	-- 背包信息
-- 	LIFESOUL_OPERATOR_TYPE_PUT_SLOT = 3,	-- 装备 p1(slot) p2(背包位置) p3(is_speical)
-- 	LIFESOUL_OPERATOR_TYPE_DOWN_SLOT = 4,	-- 卸下 p1(slot)
-- 	LIFESOUL_OPERATOR_TYPE_LOCK_EVEN = 5,	-- 锁 - 解锁
-- }

function LongHunWGCtrl:SendLongHunOperaReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLifesoulOperator)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()

end

function LongHunWGCtrl:SendLongHunDeCompose(count, list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLifesoulDeCompose)
	protocol.count = count or 0
	protocol.index_list = list or {}
	protocol:EncodeAndSend()
end

function LongHunWGCtrl:OpenUpGradeView(slot_index)
	if self.longhun_upgrade_view then
		self.longhun_upgrade_view:SetData(slot_index)
		self.longhun_upgrade_view:Open()
		self.longhun_upgrade_view:Flush()
	end
end

function LongHunWGCtrl:OpenFenJieResultTips(data)
	if self.long_hun_result then
		self.long_hun_result:SetData(data)
		self.long_hun_result:Open()
		self.long_hun_result:Flush()
	end
end

function LongHunWGCtrl:OpenLongHunShowWay()
	if self.longhun_showway_view then
		self.longhun_showway_view:Open()
		self.longhun_showway_view:Flush()
	end
end

function LongHunWGCtrl:OnSCLifesoulItemAddNotice(protocol)
	local item_cfg = ItemWGData.Instance:GetItemConfig(protocol.item_id)
	local str = string.format(Language.Bag.GetItemTxt, ToColorStr(item_cfg.name, GET_TIP_ITEM_COLOR[item_cfg.color]), protocol.num)
	TipWGCtrl.Instance:ShowSystemMsg(str)
end

function LongHunWGCtrl:PlayFenJieAni()
	if self.view:IsOpen() then
		self.view:PlayFenJieAni()
	end
end