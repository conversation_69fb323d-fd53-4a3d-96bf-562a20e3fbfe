local TARGET_COOK_NAME_COLOR_TYPE = {
	[2] = { vertex1 = "#6bdeff", vertex2 = "#d7f1ff" },
	[3] = { vertex1 = "#a879e7", vertex2 = "#f9f5ff" },
	[4] = { vertex1 = "#ed6127", vertex2 = "#fff1eb" },
	[5] = { vertex1 = "#ff0000", vertex2 = "#ffeeee" },
	[6] = { vertex1 = "#ff8898", vertex2 = "#ffffff" },
}

FestivalChuShenCookResultPanel = FestivalChuShenCookResultPanel or BaseClass(SafeBaseView)

function FestivalChuShenCookResultPanel:__init()
	self.view_name = "FestivalChuShenCookResultPanel"
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/festival_activity_ui/chushen_ui_prefab", "layout_cook_result_view")
end

function FestivalChuShenCookResultPanel:LoadCallBack()
	local cook_result_title_bg_bundel, cook_result_title_bg_asset = ResPath.GetFestivalRawImages("xltb_tbcgd")
	self.node_list.cook_result_title_bg.raw_image:LoadSprite(cook_result_title_bg_bundel, cook_result_title_bg_asset,
		function()
			self.node_list.cook_result_title_bg.raw_image:SetNativeSize()
		end)

	local cook_result_tbcg_bundel, cook_result_tbcg_asset = ResPath.GetFestivalRawImages("xltb_tbcg")
	self.node_list.cook_result_tbcg.raw_image:LoadSprite(cook_result_tbcg_bundel, cook_result_tbcg_asset, function()
		self.node_list.cook_result_tbcg.raw_image:SetNativeSize()
	end)

	local cook_result_tbd_bundel, cook_result_tbd_asset = ResPath.GetFestivalRawImages("xltbtbd")
	self.node_list.cook_result_tbd.raw_image:LoadSprite(cook_result_tbd_bundel, cook_result_tbd_asset, function()
		self.node_list.cook_result_tbd.raw_image:SetNativeSize()
	end)

	local item_cell_name_bg_bundel, item_cell_name_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_xltb_name_bg")
	self.node_list.item_cell_name_bg.image:LoadSprite(item_cell_name_bg_bundel, item_cell_name_bg_asset, function()
		self.node_list.item_cell_name_bg.image:SetNativeSize()
	end)

	local reward_bg_bundel, reward_bg_asset = ResPath.GetFestivalRawImages("jianglidi")
	self.node_list.reward_bg_2.raw_image:LoadSprite(reward_bg_bundel, reward_bg_asset, function()
		self.node_list.reward_bg_2.raw_image:SetNativeSize()
	end)

	self.node_list.reward_bg_3.raw_image:LoadSprite(reward_bg_bundel, reward_bg_asset, function()
		self.node_list.reward_bg_3.raw_image:SetNativeSize()
	end)

	self.node_list.reward_bg_2_1.raw_image:LoadSprite(reward_bg_bundel, reward_bg_asset, function()
		self.node_list.reward_bg_2_1.raw_image:SetNativeSize()
	end)
	if not self.target_item_cell then
		self.target_item_cell = ItemCell.New(self.node_list.item_cell_pos)
	end
end

function FestivalChuShenCookResultPanel:LoadIndexCallBack()
	-- self.grop_list_1 = {}
	-- self.grop_list_2 = {}
	-- self.grop_list_0 = {}
	-- for i=1,7 do
	-- 	local cell = ItemCell.New(self.node_list["goods_reward_"..i])
	-- 	self.grop_list_1[i] = cell

	-- 	local cell1 = ItemCell.New(self.node_list["goods_reward_1_"..i])
	-- 	self.grop_list_2[i] = cell1

	-- 	local cell_2 = ItemCell.New(self.node_list["goods_reward_2_"..i])
	-- 	self.grop_list_0[i] = cell_2
	-- end

	self.all_item_list1 = AsyncListView.New(ItemCell, self.node_list["all_item_list1"])
	self.all_item_list2 = AsyncListView.New(ItemCell, self.node_list["all_item_list2"])
	self.item_list = AsyncListView.New(ItemCell, self.node_list["item_list"])

end

function FestivalChuShenCookResultPanel:LoadSkeleAni()
	local interface_cfg = FestivalChuShenWGData.Instance:GetChuShenOtherCfg()
	if nil == interface_cfg then
		return
	end
end

function FestivalChuShenCookResultPanel:ReleaseCallBack()
	self.cook_type = nil
	self.menu_id = nil

	self.ske_ani_big0 = nil
	self.ske_ani_big1 = nil
	self.ske_ani_big2 = nil
	self.ske_ani_big3 = nil

	if self.all_item_list1 then
		self.all_item_list1:DeleteMe()
		self.all_item_list1 = nil
	end

	if self.all_item_list2 then
		self.all_item_list2:DeleteMe()
		self.all_item_list2 = nil
	end

	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	if self.target_item_cell then
		self.target_item_cell:DeleteMe()
		self.target_item_cell = nil
	end
end

function FestivalChuShenCookResultPanel:OnFlush()
	if nil == self.cook_type then
		return
	end
	local menu_name = 0
	local oa_index = FestivalChuShenWGData.Instance:GetChuShenActInfoData()
	local reward_cfg = FestivalChuShenWGData.Instance:GetChuShenParamCfg()
	local reward_base_cfg, reward_cfg1 = FestivalChuShenWGData.Instance:GetMenuListCfg()
	local data_list = {}
	local unlock_reward_list = {}
	local is_special_menu = false
	local special_id = 0
	local quality_color = 2
	for k, v in pairs(reward_base_cfg) do
		if v.is_special == 1 then
			special_id = v.item_id
			break
		end
	end

	if self.menu_id > 0 then
		for k, v in pairs(reward_cfg1) do
			if v.id == self.menu_id then
				data_list = v.reward_item
				menu_name = v.menu_name_image
				local temp_data = v.consume_item
				for i = 0, 2 do
					if temp_data[i] and temp_data[i].item_id == special_id then
						is_special_menu = true
						break
					end
				end

				--local b, a =  ResPath.GetFestivalChuShenUiImages("yuan_spe_color"..v.menu_quality)
				--local b, a =  ResPath.GetFestivalActImages("a1_jrhd_xltb"..v.menu_quality)
				quality_color = v.menu_quality
				-- self.node_list["item_cell_image_bg_1"].image:LoadSprite(b, a)
				-- self.node_list["item_cell_image_bg_1_1"].image:LoadSprite(b, a)
				-- local parti_b, parti_a = FestivalChuShenWGData.Instance:GetPartiAsesst(v.menu_quality)
				-- self.node_list["item_cell_image_parti_1"]:ChangeAsset(parti_b, parti_a)
				-- self.node_list["item_cell_image_parti_1_1"]:ChangeAsset(parti_b, parti_a)
				-- self.node_list["item_cell_image_parti_1"]:SetActive(v.menu_quality > SHOW_EFFECT_LEVEL)
				-- self.node_list["item_cell_image_parti_1_1"]:SetActive(v.menu_quality > SHOW_EFFECT_LEVEL)

				local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.menu_id)
				local target_item_cell_data = { item_id = self.menu_id }
				self.node_list.item_cell_name_text.text.text = item_cfg.name
				self.target_item_cell:SetData(target_item_cell_data)
				--local center_image_b, center_image_a =  ResPath.GetItem(item_cfg.icon_id)
				-- self.node_list["item_cell_image_1"].image:LoadSprite(center_image_b, center_image_a, function ()
				-- 		XUI.ImageSetNativeSize(self.node_list["item_cell_image_1"])
				-- 	end)

				-- self.node_list["item_cell_image_1_1"].image:LoadSprite(center_image_b, center_image_a, function ()
				-- 		XUI.ImageSetNativeSize(self.node_list["item_cell_image_1_1"])
				-- 	end)
				unlock_reward_list = v.unlock_item
				break
			end
		end

	else
		for k, v in pairs(reward_cfg) do
			if oa_index == v.oa_index then
				data_list = self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_4 and v.fail_reward_item_2 or
					v.fail_reward_item
				unlock_reward_list = v.unlock_all_item
				break
			end
		end

		local other_cfg = FestivalChuShenWGData.Instance:GetChuShenOtherCfg()
		local default_item_id = self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_4 and
			other_cfg.mid_cuisine_icon or other_cfg.dark_cuisine_icon
		-- menu_name = self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_4 and other_cfg.menu_name_image or 0
		menu_name = 0
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(default_item_id)
		quality_color = item_cfg.color
		self.node_list.item_cell_name_text.text.text = item_cfg.name
		local target_item_cell_data = { item_id = default_item_id } --,num = cfg.num or 1,is_bind = cfg.is_bind or 1}
		self.target_item_cell:SetData(target_item_cell_data)
		--local center_image_b, center_image_a =  ResPath.GetItem(item_cfg.icon_id)
		-- self.node_list["item_cell_image_1"].image:LoadSprite(center_image_b, center_image_a, function ()
		-- 		XUI.ImageSetNativeSize(self.node_list["item_cell_image_1"])
		-- 	end)

		-- self.node_list["item_cell_image_1_1"].image:LoadSprite(center_image_b, center_image_a, function ()
		-- 		XUI.ImageSetNativeSize(self.node_list["item_cell_image_1_1"])
		-- 	end)
		-- local b, a =  ResPath.GetFestivalChuShenUiImages("yuan_spe_color"..item_cfg.color)
		-- local b, a =  ResPath.GetNoPackPNG("a1_tbcg_dadi"..item_cfg.color)

		-- self.node_list["item_cell_image_bg_1"].image:LoadSprite(b, a)
		-- self.node_list["item_cell_image_bg_1_1"].image:LoadSprite(b, a)
		-- local parti_b, parti_a = FestivalChuShenWGData.Instance:GetPartiAsesst(item_cfg.color)
		-- self.node_list["item_cell_image_parti_1"]:ChangeAsset(parti_b, parti_a)
		-- self.node_list["item_cell_image_parti_1_1"]:ChangeAsset(parti_b, parti_a)
		-- self.node_list["item_cell_image_parti_1"]:SetActive(item_cfg.color > SHOW_EFFECT_LEVEL)
		-- self.node_list["item_cell_image_parti_1_1"]:SetActive(item_cfg.color > SHOW_EFFECT_LEVEL)
	end

	-- local bundle, asset = ResPath.GetFestivalChuShenUiImages("menu_image_"..menu_name)
	-- local unlock_b, unlock_a = ResPath.GetFestivalChuShenUiImages("unlock_success_image_"..quality_color)
	-- local success_b, success_a = ResPath.GetFestivalChuShenUiImages("cooking_success_image_"..quality_color)
	-- self.node_list.desc_1.text.text = ""
	-- self.node_list.desc_1_1.text.text = ""

	-- self.node_list["desc_name_1"].image:LoadSprite(bundle, asset, function ()
	-- 	XUI.ImageSetNativeSize(self.node_list["desc_name_1"])
	-- end)
	-- self.node_list["desc_name_1_1"].image:LoadSprite(bundle, asset, function ()
	-- 	XUI.ImageSetNativeSize(self.node_list["desc_name_1_1"])
	-- end)

	-- self.node_list["desc_text_1"].image:LoadSprite(unlock_b, unlock_a, function ()
	-- 	XUI.ImageSetNativeSize(self.node_list["desc_text_1"])
	-- end)
	-- self.node_list["desc_text_1_1"].image:LoadSprite(success_b, success_a, function ()
	-- 	XUI.ImageSetNativeSize(self.node_list["desc_text_1_1"])
	-- end)

	if self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_1 -- 失败
		or self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_2
		or self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_4 then -- 成功但没解锁新菜谱

		self.node_list.all_group:SetActive(false)
		-- self.node_list.desc_1:SetActive(false)
		-- self.node_list.desc_1_1:SetActive(true)
		self.node_list.desc_2:SetActive(false)
		self.node_list.desc_3:SetActive(false)
		--self.node_list.big_bg_type_1:SetActive(false)
		--self.node_list.big_bg_type_2:SetActive(false)
		--self.node_list.item_cell_image_bg_1:SetActive(false)
		-- self.node_list.flower_group_1:SetActive(false)

		-- self.node_list.flower_group_1_1:SetActive(true)
		--self.node_list.item_cell_image_bg_1_1:SetActive(true)
		-- self.node_list.group_1_1:SetActive(true)
		self.node_list.item_list:SetActive(true)
		self.node_list.desc_2_1:SetActive(true)
		--self.node_list.big_bg_type_1_1:SetActive(is_special_menu)
		--self.node_list.big_bg_type_2_1:SetActive(not is_special_menu)

		-- if self.grop_list_0 then
		-- 	--self.grop_list_0:SetDataList(self.result_reward_list)
		-- 	for k,v in pairs(self.grop_list_0) do
		-- 		self.node_list["goods_reward_2_"..k]:SetActive(nil ~= self.result_reward_list[k - 1])
		-- 		if self.result_reward_list[k - 1] then
		-- 			v:SetData(self.result_reward_list[k - 1])
		-- 		end
		-- 	end
		-- end
		self.item_list:SetDataList(self.result_reward_list)

		self.node_list.desc_2_1.text.text = Language.FestivalChuShen.ChuShenGetRewARD

	elseif self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_3 then -- 成功并解锁新菜谱

		self.node_list.all_group:SetActive(true)
		-- self.node_list.desc_1:SetActive(true)
		-- self.node_list.desc_1_1:SetActive(false)
		self.node_list.desc_2:SetActive(true)
		self.node_list.desc_3:SetActive(true)
		--self.node_list.big_bg_type_1:SetActive(is_special_menu)
		--self.node_list.big_bg_type_2:SetActive(not is_special_menu)
		--self.node_list.item_cell_image_bg_1:SetActive(true)
		-- self.node_list.flower_group_1:SetActive(true)

		-- self.node_list.flower_group_1_1:SetActive(false)
		--self.node_list.item_cell_image_bg_1_1:SetActive(false)
		-- self.node_list.group_1_1:SetActive(false)
		self.node_list.item_list:SetActive(false)
		self.node_list.desc_2_1:SetActive(false)
		--self.node_list.big_bg_type_1_1:SetActive(false)
		--self.node_list.big_bg_type_2_1:SetActive(false)
		-- if self.grop_list_1 then
		-- 	--self.grop_list_1:SetDataList(self.unlock_reware_list)
		-- 	for k,v in pairs(self.grop_list_1) do
		-- 		self.node_list["goods_reward_"..k]:SetActive(nil ~= self.unlock_reware_list[k - 1])
		-- 		if self.unlock_reware_list[k - 1] then
		-- 			v:SetData(self.unlock_reware_list[k - 1])
		-- 		end
		-- 	end
		-- end
		self.all_item_list1:SetDataList(self.unlock_reware_list)

		-- self.node_list.group_2:SetActive(true)
		self.node_list.all_item_list2:SetActive(true)
		self.node_list.desc_3:SetActive(true)
		--  if self.grop_list_2 then
		--  	--self.grop_list_2:SetDataList(self.result_reward_list)
		-- 	for k,v in pairs(self.grop_list_2) do
		-- 		self.node_list["goods_reward_1_"..k]:SetActive(nil ~= self.result_reward_list[k - 1])
		-- 		if self.result_reward_list[k - 1] then
		-- 			v:SetData(self.result_reward_list[k - 1])
		-- 		end
		-- 	end
		-- end
		self.all_item_list2:SetDataList(self.result_reward_list)
		self.node_list.desc_2.text.text = Language.FestivalChuShen.ChuShenFirstUnlock
		self.node_list.desc_3.text.text = Language.FestivalChuShen.ChuShenGetRewARD
	end

	local gradient = self.node_list.item_cell_name_text.text:GetOrAddComponent(typeof(UIGradient))
	gradient.Color1 = StrToColor(TARGET_COOK_NAME_COLOR_TYPE[quality_color] and
		TARGET_COOK_NAME_COLOR_TYPE[quality_color].vertex1 or TARGET_COOK_NAME_COLOR_TYPE[2].vertex1)
	gradient.Color2 = StrToColor(TARGET_COOK_NAME_COLOR_TYPE[quality_color] and
		TARGET_COOK_NAME_COLOR_TYPE[quality_color].vertex2 or TARGET_COOK_NAME_COLOR_TYPE[2].vertex2)

	local interface_cfg = FestivalChuShenWGData.Instance:GetChuShenOtherCfg()
	if nil == interface_cfg then
		return
	end

	-- if is_special_menu then
	-- 	if self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_3 then   --高级+解锁
	-- 		--self.node_list.bg_di_bg_1.raw_image:LoadSprite(ResPath.GetFestivalRawImages("chushen_result_bg"..quality_color))
	-- 		self.node_list.bg_di_bg_1.image:LoadSprite(ResPath.GetNoPackPNG("a1_tbcg_dadi"..quality_color))
	-- 	else
	-- 		--self.node_list.bg_di_bg_1_1.raw_image:LoadSprite(ResPath.GetFestivalRawImages("chushen_result_bg"..quality_color))
	-- 		 self.node_list.bg_di_bg_1_1.image:LoadSprite(ResPath.GetNoPackPNG("a1_tbcg_dadi"..quality_color))														--高级
	-- 	end
	-- else
	-- 	if self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_3 then   --中级+解锁
	-- 		--self.node_list.bg_di_bg_2.raw_image:LoadSprite(ResPath.GetFestivalRawImages("chushen_result_bg"..quality_color))
	-- 		 self.node_list.bg_di_bg_2.image:LoadSprite(ResPath.GetNoPackPNG("a1_tbcg_dadi"..quality_color))
	-- 	else
	-- 		--self.node_list.bg_di_bg_2_1.raw_image:LoadSprite(ResPath.GetFestivalRawImages("chushen_result_bg"..quality_color))
	-- 		 self.node_list.bg_di_bg_2_1.image:LoadSprite(ResPath.GetNoPackPNG("a1_tbcg_dadi"..quality_color))
	-- 												--中级
	-- 	end
	-- end

	self.node_list.cook_result_bg.raw_image:LoadSprite(ResPath.GetFestivalRawImages("xltb_resultbg"))

	self:LoadSkeleAni()
end

function FestivalChuShenCookResultPanel:CloseCallBack()
	if self.cook_type == FA_TIANCAICHUSHEN_COOK_TYPE.FA_TIANCAICHUSHEN_COOK_TYPE_3 then -- 成功并解锁新菜谱
		local _, cfg = FestivalChuShenWGData.Instance:GetMenuListCfg()
		local all_num = #cfg or 1
		local has_num = FestivalChuShenWGData.Instance:GetAllUnlockNum()
		if all_num == has_num then
			FestivalChuShenWGCtrl.Instance:OpenRewardPanel()
		end
	end
end

function FestivalChuShenCookResultPanel:SetDataOpen(protocol)
	if not protocol.cook_type or not protocol.id then
		return
	end

	self.cook_type = protocol.cook_type
	self.menu_id = protocol.id
	self.result_reward_list = protocol.reware_list
	self.unlock_reware_list = protocol.unlock_reware_list
	self:Open()
end

---------------------------------------------------------------------------
FestivalChuShenRewardShowPanel = FestivalChuShenRewardShowPanel or BaseClass(SafeBaseView)

function FestivalChuShenRewardShowPanel:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
		{ vector2 = Vector2(2, 57), sizeDelta = Vector2(826, 468) })
	self:AddViewResource(0, "uis/view/festival_activity_ui/chushen_ui_prefab", "layout_unlock_result_view")
end

function FestivalChuShenRewardShowPanel:LoadIndexCallBack()
	self.node_list.title_view_name.text.text = Language.FestivalChuShen.ChuShenRankReward
	-- self.grop_list_1 = {}
	-- for i=1,3 do
	-- 	local cell = ItemCell.New(self.node_list["goods_reward_"..i])
	-- 	self.grop_list_1[i] = cell
	-- end
	self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
	XUI.AddClickEventListener(self.node_list["get_btn"], BindTool.Bind(self.GetReward, self))
	self.node_list.desc_1.text.text = Language.FestivalChuShen.ChuShenUnLockDesc
end

function FestivalChuShenRewardShowPanel:ReleaseCallBack()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function FestivalChuShenRewardShowPanel:OnFlush()
	local _, cfg = FestivalChuShenWGData.Instance:ChunShenOpenLimit()
	local data_list = cfg and cfg.unlock_all_item
	data_list = SortDataByItemColor(data_list)
	-- 	if self.grop_list_1 then
	-- 	for k,v in pairs(self.grop_list_1) do
	-- 		self.node_list["goods_reward_"..k]:SetActive(nil ~= data_list[k])
	-- 		if data_list[k] then
	-- 			v:SetData(data_list[k])
	-- 		end
	-- 	end
	-- end
	self.item_list:SetDataList(data_list)

	local _, all_num = FestivalChuShenWGData.Instance:GetMenuListCfg()
	local has_num = FestivalChuShenWGData.Instance:GetAllUnlockNum()
	self.node_list.desc_2.text.text = string.format(Language.FestivalChuShen.ChuShenProcess, has_num, #all_num)
	local _, unlock_all = FestivalChuShenWGData.Instance:GetChuShenActInfoData()

	if unlock_all == 0 or unlock_all == 2 then
		XUI.SetGraphicGrey(self.node_list["get_btn"], true)
	else
		XUI.SetGraphicGrey(self.node_list["get_btn"], false)
	end

end

function FestivalChuShenRewardShowPanel:GetReward()
	local _, unlock_all = FestivalChuShenWGData.Instance:GetChuShenActInfoData()
	if unlock_all == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FestivalChuShen.ChuShenNotAllUnlock)
		return
	elseif unlock_all == 2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FestivalChuShen.ChuShenGetReward1)
		return
	end
	FestivalChuShenWGCtrl.Instance:CSFATianCaiChuShenOpera(FA_TIANCAICHUSHEN_OPERA_TYPE.FA_TIANCAICHUSHEN_OPERA_TYPE_6)
	FestivalChuShenWGData.Instance:ChuShenUnlockRemindMark()
	self:Close()
end
