-- 已屏蔽
TianShenJiBanTip = TianShenJiBanTip or BaseClass(SafeBaseView)

function TianShenJiBanTip:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true,false,BindTool.Bind1(self.ViewAnimation,self))
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_ts_jb_tip")
end

function TianShenJiBanTip:__delete()
end

function TianShenJiBanTip:ReleaseCallBack()
	if self.auto_close_timer then
        GlobalTimerQuest:CancelQuest(self.auto_close_timer)
        self.auto_close_timer = nil
    end
    self.tween_anim = nil
end

function TianShenJiBanTip:LoadCallBack()
	if self.auto_close_timer then
        GlobalTimerQuest:CancelQuest(self.auto_close_timer)
        self.auto_close_timer = nil
    end
    self.auto_close_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.ViewAnimation,self), 3)--5秒自动关闭
    self.auto_tail_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.ShowTailEffect,self), 2.8)--5秒自动关闭
end
function TianShenJiBanTip:ShowTailEffect()
	self.node_list["tail_effect"]:SetActive(true)
	--self.node_list["tail_effect"].canvas_group.alpha = 0
end

function TianShenJiBanTip:ViewAnimation()
	if not self:IsLoaded() then
		return
	end
	--self.node_list.root.rect.anchoredPosition = Vector2(0,0)
	self.node_list["light_effect"]:SetActive(false)
	local MoveTime = 0.5
	self.node_list.root.rect:DOAnchorPos(Vector2(440, 0),MoveTime)
	--self.node_list.tail_effect.rect:DOAnchorPos(Vector2(440, 0),MoveTime)
	self.tween_anim = self.node_list["root"].canvas_group:DoAlpha(1, 0.5, 0.5)
    self.tween_anim:OnComplete(function()
        self:Close()
    end)
end

function TianShenJiBanTip:ShowIndexCallBack()
	self.node_list["tail_effect"]:SetActive(false)
	self.node_list["light_effect"]:SetActive(false)
end

function TianShenJiBanTip:OnFlush(prarm_t)
	local jiban_info = TianShenWGData.Instance:GetTianShenJiBanAct()
	if not jiban_info then
		return
	end
	for i=1, 2 do
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(jiban_info["index"..i])
		if tianshen_cfg then
			local bundle, asset = ResPath.GetItem(tianshen_cfg.head_id)
			self.node_list["item_icon_"..i].image:LoadSprite(bundle, asset, function ()
			self.node_list["item_icon_"..i].image:SetNativeSize()
			end)
			self.node_list["name_"..i].text.text = tianshen_cfg.bianshen_name
		end
	end
	self.node_list["light_effect"]:SetActive(true)
end