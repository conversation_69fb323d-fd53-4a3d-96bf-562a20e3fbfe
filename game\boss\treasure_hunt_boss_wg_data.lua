TreasureBossWGData = TreasureBossWGData or BaseClass()

function TreasureBossWGData:__init()
	if TreasureBossWGData.Instance then
		error("[TreasureBossWGData] Attempt to create singleton twice!")
		return
	end
	TreasureBossWGData.Instance = self

	self.treasurehunt_boss_cfg = ConfigManager.Instance:GetAutoConfig("chestshop_boss_auto")
    self.treasurehunt_type_cfg = ListToMapList(self.treasurehunt_boss_cfg.boss_cfg,"chestshop_type")
    self.treasurehunt_scene_cfg = ListToMap(self.treasurehunt_boss_cfg.boss_cfg,"scene_id")
    self.treasurehunt_boss_id_cfg = ListToMap(self.treasurehunt_boss_cfg.boss_cfg,"boss_id")
    self.treasurehunt_boss_order_cfg = ListToMapList(self.treasurehunt_boss_cfg.join_reward,"boss_id") --self.treasurehunt_boss_cfg.join_reward
    --
    self.treasurehunt_boss_refresh_cfg = self.treasurehunt_boss_cfg.refresh_time[1]


    self.other_treasure_cfg = self.treasurehunt_boss_cfg.other[1]

    self.treasurehunt_kill_times = 0
    self.treasurehunt_join_times = 0
    self.all_treasure_boss_info = {}
	self.cache_xb_boss_refresh_time = nil
    self.drop_temp_list = {}
    self.join_drop_temp_list = {}
    self.boss_open_time = {}
    self.is_have_show = false
    self:InitBossOpenTime()
end
function TreasureBossWGData:__delete()
    self:CanCleOpenQuest()
    self:CanCleStayQuest()
    self:CanCleReFlushQuest()
	TreasureBossWGData.Instance = nil
end

function TreasureBossWGData:InitBossOpenTime()
    if (not self.other_treasure_cfg) or (not self.other_treasure_cfg.enter_time) then
        return
    end

    local time_count_tab = Split(self.other_treasure_cfg.enter_time, "|")
    for index, time_str in ipairs(time_count_tab) do
        local time_data = {}
        local time_tab = Split(time_str, ",")
        local time_num_s = tonumber(time_tab[1]) or 0
        time_data.start_hour = math.floor(time_num_s / 100)
        time_data.start_sec = time_num_s % 100

        local time_num_e = tonumber(time_tab[2]) or 0
        time_data.end_hour = math.floor(time_num_e / 100)
        time_data.end_sec = time_num_e % 100
  
        self.boss_open_time[index] = time_data
    end
end

 --    local t = {}
 --    local KILL_HISTORY_MAX_COUNT = 10
	-- t.boss_id = MsgAdapter.ReadUShort()				
 --    t.status = MsgAdapter.ReadShort()			-- 0不存在 1存在
 --    t.next_refresh_time = MsgAdapter.ReadUInt() 	
 --    t.top_hurt_role_name = MsgAdapter.ReadStrN(32) 
 --    t.kill_count = MsgAdapter.ReadInt()
 --    t.kill_history = {}
 --    for i = 1, KILL_HISTORY_MAX_COUNT do
 --        local info = ProtocolStruct.ReadShenyuanBossKillHistory()
 --        if info.killer_role_name ~= nil and info.kill_timestamp ~= 0 then
 --            t.kill_history[# t.kill_history + 1] = info
 --        end
 --    end
	-- return t

function TreasureBossWGData:OnSCChestshopBossResetInfo(protocol)
    if protocol.count > 0 then
        for k,v in pairs(protocol.index_list) do
            if self.all_treasure_boss_info[v] then
                self.all_treasure_boss_info[v].status = 0
                self.all_treasure_boss_info[v].next_refresh_time = 0
                self.all_treasure_boss_info[v].top_hurt_role_name = ""
            end
        end
    end
    TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
end


function TreasureBossWGData:OnSCSendTreasureHuntAllBossInfo(protocol)
	for k, v in pairs(protocol.all_shenyuan_boss_info) do
        self.all_treasure_boss_info[v.boss_id] = v
    end
    TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
end

function TreasureBossWGData:GetTreasureBossInfo()
    return self.all_treasure_boss_info or {}
end

function TreasureBossWGData:GetTreasureBossInfoByBossID(boss_id)
    return self.all_treasure_boss_info and self.all_treasure_boss_info[boss_id] or {}
end

function TreasureBossWGData:OnSCSendTreasureHuntSingleBossInfo(protocol)
	self.all_treasure_boss_info[protocol.boss_info.boss_id] = protocol.boss_info
    TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
end

-- function TreasureBossWGData:OnSCTreasureHuntBossHurtInfo(protocol)
-- 	self.treasure_hurt_info = protocol.boss_hurt_info
-- end

-- function TreasureBossWGData:OnSCTreasureHuntBossPersonHurtInfo(protocol)
-- 	self.treasure_person_hurt_info.hurt = protocol.hurt
--     self.treasure_person_hurt_info.rank_count = protocol.rank_count
-- end

function TreasureBossWGData:GetHuntBossDataListByType(treasure_type)
    if self.treasurehunt_type_cfg then
        return self.treasurehunt_type_cfg[treasure_type] or {}
    end
    return {}
end

function TreasureBossWGData:GetHuntBossInfoByScene(scene_id)
	return self.treasurehunt_scene_cfg and self.treasurehunt_scene_cfg[scene_id] or {}
end

function TreasureBossWGData:GetMaxKillTimes()
    return self.other_treasure_cfg.kill_reward_times
end

function TreasureBossWGData:GetMaxJoinTimes()
    return self.other_treasure_cfg.join_reward_times
end

-- 检测是否能进入副本
function TreasureBossWGData:CanEnterFightTreasureBoss()
    if not self.boss_open_time then
        return false, ""
    end

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local day_start_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)
    local reason = ""
    local time_str = ""
    for _, time_data in ipairs(self.boss_open_time) do
        local hour_s = time_data.start_hour
        local min_s = time_data.start_sec
        local hour_e = time_data.end_hour
        local min_e = time_data.end_sec

        if server_time >= (day_start_time + hour_s * 60 * 60 + min_s * 60) then
            if server_time < (day_start_time + hour_e * 60 * 60 + min_e * 60) then
                return true
            end
        end

        time_str = time_str == "" and time_str or time_str .. ","
        local hour_s_str = hour_s < 10 and "0" .. hour_s or hour_s
        local min_s_str = min_s == 0 and "00" or min_s < 10 and "0" .. min_s or min_s
        local hour_e_str = hour_e < 10 and "0" .. hour_e or hour_e
        local min_e_str = min_e == 0 and "00" or min_e < 10 and "0" .. min_e or min_e
        time_str = time_str .. (hour_s_str .. ":" ..min_s_str .. "~".. hour_e_str .. ":" ..min_e_str)
    end

    reason = string.format(Language.TreasureHunt.DescEnterFBTimeLimit, time_str)

    return false, reason
end

-- 获取副本副本开启最优解
function TreasureBossWGData:GetBestFightTimeTreasureSrc(get_num)
    if not self.boss_open_time then
        return ""
    end

    if not get_num then
        return ""
    end

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local day_start_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)

    local index = 0
    local time_open_table = {}

    for _, time_data in ipairs(self.boss_open_time) do
        local hour_s = time_data.start_hour
        local min_s = time_data.start_sec
        local hour_e = time_data.end_hour
        local min_e = time_data.end_sec

        -- 小于结束时间
        if server_time < (day_start_time + hour_e * 60 * 60 + min_e * 60) then
            if server_time >= (day_start_time + hour_s * 60 * 60 + min_s * 60) then -- 正在开启
                table.insert(time_open_table, time_data)
                index = index + 1
            elseif server_time < (day_start_time + hour_e * 60 * 60 + min_e * 60) then  -- 开启预告
                table.insert(time_open_table, time_data)
                index = index + 1
            end

            if index == get_num then
                break
            end
        end
    end

    local desc_t = {}
    local get_time_desc = function (prefix, time_data, index)
        local desc = ""
        if time_data then
            local hour_s = time_data.start_hour
            local min_s = time_data.start_sec
            local hour_e = time_data.end_hour
            local min_e = time_data.end_sec
    
            local hour_s_str = hour_s < 10 and "0" .. hour_s or hour_s
            local min_s_str = min_s == 0 and "00" or min_s < 10 and "0" .. min_s or min_s
            local hour_e_str = hour_e < 10 and "0" .. hour_e or hour_e
            local min_e_str = min_e == 0 and "00" or min_e < 10 and "0" .. min_e or min_e
            desc = hour_s_str .. ":" ..min_s_str .. "~".. hour_e_str .. ":" ..min_e_str
            desc = ToColorStr(desc, COLOR3B.DEFAULT_NUM)
            desc = string.format(prefix, desc)
        else
            if index == 1 then
                desc = Language.TreasureHunt.DescOpenFBTimeLimitNoTime
            end
        end

        return desc
    end

    for i = 1, 2 do
        local prefix = i == 1 and Language.TreasureHunt.DescOpenFBTimeLimit or Language.TreasureHunt.DescOpenNextFBTimeLimit
        table.insert(desc_t, get_time_desc(prefix, time_open_table[i], i))
    end

    return desc_t
end

function TreasureBossWGData:GetHaveKillTimes()
    return self.treasurehunt_kill_times
end

function TreasureBossWGData:GetHaveJoinTimes()
    return self.treasurehunt_join_times
end

function TreasureBossWGData:SetHaveKillTimes(value)
    self.treasurehunt_kill_times = value
    self:CheckShowTreasureBossTired()
end

function TreasureBossWGData:SetHaveJoinTimes(value)
    self.treasurehunt_join_times = value
    self:CheckShowTreasureBossTired()
end

function TreasureBossWGData:IsTreasureBoss(boss_id)
    if self.treasurehunt_boss_id_cfg and not IsEmptyTable(self.treasurehunt_boss_id_cfg[boss_id]) then
        return true
    end
    return false
end

function TreasureBossWGData:NeedShowTreasureBossFlushTip(boss_id)
    if self:GetMaxKillTimes() <= self:GetHaveKillTimes() and self:GetMaxJoinTimes() <= self:GetHaveJoinTimes() and boss_id ~= 0 then
        return false,boss_id
    end

    local best_boss_id = TreasureHuntWGData.Instance:GetBestOpenTreasureBoss()

    if best_boss_id == 0 then
        return false,boss_id
    end

    if boss_id == 0 then
        return true,best_boss_id
    end
    return best_boss_id == boss_id,boss_id
end

function TreasureBossWGData:GetTreasureBossShowItemList(boss_id)
    if self.treasurehunt_boss_id_cfg and self.treasurehunt_boss_id_cfg[boss_id] then
        if self.drop_temp_list[boss_id] then
            return self.drop_temp_list[boss_id]
        end
        self.drop_temp_list[boss_id] = {}
        local temp_list = Split(self.treasurehunt_boss_id_cfg[boss_id].drop_item_list,",")
        local drop_list = {}
        for k,v in pairs(temp_list) do
            drop_list[k] = {}
            local item_data = Split(v,":")
            drop_list[k].item_id = item_data[1] and tonumber(item_data[1]) or 0
            drop_list[k].num = item_data[2] and tonumber(item_data[2]) or 1
            drop_list[k].is_bind = item_data[3] and tonumber(item_data[3]) or 0
            drop_list[k].mutiple = self.treasurehunt_boss_id_cfg[boss_id].mutiple
            drop_list[k].show_num = self.treasurehunt_boss_id_cfg[boss_id].show_num
        end
        self.drop_temp_list[boss_id] = drop_list
        return self.drop_temp_list[boss_id]
    end
    return {}
end

function TreasureBossWGData:GetTreasureBossJoinItemList(boss_id)
    if self.treasurehunt_boss_id_cfg and self.treasurehunt_boss_id_cfg[boss_id] then
        if self.join_drop_temp_list[boss_id] then
            return self.join_drop_temp_list[boss_id]
        end
        self.join_drop_temp_list[boss_id] = {}
        local temp_list = Split(self.treasurehunt_boss_id_cfg[boss_id].join_drop_item_list,",")
        local drop_list = {}
        for k,v in pairs(temp_list) do
            drop_list[k] = {}
            local item_data = Split(v,":")
            drop_list[k].item_id = item_data[1] and tonumber(item_data[1]) or 0
            drop_list[k].num = item_data[2] and tonumber(item_data[2]) or 1
            drop_list[k].is_bind = item_data[3] and tonumber(item_data[3]) or 0
        end
        self.join_drop_temp_list[boss_id] = drop_list
        return self.join_drop_temp_list[boss_id]
    end
    return {}
end

function TreasureBossWGData:GetBossDropShowData(boss_id)
    local data = {}
    data.drop_item_list = self:GetTreasureBossShowItemList(boss_id)
    data.join_drop_item_list = {}--self:GetTreasureBossJoinItemList(boss_id)
    data.reward_tips_des = self.treasurehunt_boss_id_cfg[boss_id].reward_tips_des
    return data
end

function TreasureBossWGData:GetTreasureDataByBossId(boss_id)
    if self.treasurehunt_boss_id_cfg and self.treasurehunt_boss_id_cfg[boss_id] then
        return self.treasurehunt_boss_id_cfg[boss_id]
    end
    return {}
end

function TreasureBossWGData:GetAllTreasureBossList()
    return self.treasurehunt_boss_id_cfg or {}
end

function TreasureBossWGData:CheckShowTreasureBossTired()
    if Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
        local main_role = Scene.Instance:GetMainRole()
        if self:GetMaxKillTimes() <= self:GetHaveKillTimes() and self:GetMaxJoinTimes() <= self:GetHaveJoinTimes() then
            main_role:SetAttr("tired_value_icon", 1)
        else
            main_role:SetAttr("tired_value_icon", 0)
        end
    end
end

--世界等级对应阶数
function TreasureBossWGData:GetTreasureBossClass(boss_id)
    local world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
    if self.treasurehunt_boss_order_cfg[boss_id] then
        for k,v in pairs(self.treasurehunt_boss_order_cfg[boss_id]) do
            if v.min_level <= world_level and v.max_level >= world_level then
                return v.order
            end
        end
    end
    
    return 1
end

function TreasureBossWGData:GetNextRefreshTime()
    local flush_data = self:GetXunBaoBossRefreshTimeData()
    if not flush_data or IsEmptyTable(flush_data) or not flush_data.time_data then
        return
    end

    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    local next_time = 0
    local min_time = 0
    local str = ""
    local min_str = ""
    for k,v in pairs(flush_data.time_data) do
        if v.definite_time > cur_time then
            if next_time == 0 or v.definite_time < next_time then
                next_time = v.definite_time
                str = v.time_desc
            end
        end
        if min_time == 0 or v.definite_time < min_time then
            min_time = v.definite_time
            min_str = v.time_desc
        end
    end

    if next_time == 0 then
        return min_str
    end
    return str
end

function TreasureBossWGData:GetXunBaoBossRefreshTimeData()
    local time_desc, time_data = TimeUtil.ParsingTimeStr(self.treasurehunt_boss_refresh_cfg.refresh_time, nil, ",")
    self.cache_xb_boss_refresh_time = {time_desc = time_desc, time_data = time_data}

    return self.cache_xb_boss_refresh_time
end

function TreasureBossWGData:CanCleStayQuest()
    if self.delay_stay_remind_icon then
        GlobalTimerQuest:CancelQuest(self.delay_stay_remind_icon)
        self.delay_stay_remind_icon = nil
    end
end

function TreasureBossWGData:CanCleOpenQuest()
    if self.delay_cancle_remind_icon then
        GlobalTimerQuest:CancelQuest(self.delay_cancle_remind_icon)
        self.delay_cancle_remind_icon = nil
    end
end

function TreasureBossWGData:CanCleReFlushQuest()
    if self.delay_reflush_remind_icon then
        GlobalTimerQuest:CancelQuest(self.delay_reflush_remind_icon)
        self.delay_reflush_remind_icon = nil
    end
end

function TreasureBossWGData:NeedShowTreasureBossIcon()
    if self:GetMaxKillTimes() <= self:GetHaveKillTimes() and self:GetMaxJoinTimes() <= self:GetHaveJoinTimes() then
        self:CanCleStayQuest()
        self:CanCleOpenQuest()

        TreasureHuntWGCtrl.Instance:CloseBossReflushIcon()
        return
    end
    local flush_data = self:GetXunBaoBossRefreshTimeData()
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    local last_flush_time = 0
    local next_flush_time = 0

    for k,v in pairs(flush_data.time_data) do
        if v.definite_time <= cur_time then
            if v.definite_time > last_flush_time then
                last_flush_time = v.definite_time
            end
        end
        if v.definite_time > cur_time then
            if v.definite_time < next_flush_time or next_flush_time == 0 then
                next_flush_time = v.definite_time
            end
        end
    end
    self:CanCleReFlushQuest()
    self:CanCleStayQuest()
    local stay_left_time = next_flush_time - cur_time - 180
    if next_flush_time == 0 then
        --self:CanCleOpenQuest()
        TreasureHuntWGCtrl.Instance:CloseBossReflushIcon()
    elseif cur_time + 180 >= next_flush_time - 1 then
        if TreasureHuntWGData.Instance:HaveTreasureBossCountEnough() then
            TreasureHuntWGCtrl.Instance:StayBossReflushIcon(next_flush_time)

            self.delay_reflush_remind_icon = GlobalTimerQuest:AddDelayTimer(function ()
                self:NeedShowTreasureBossIcon()
                self:CanCleReFlushQuest()
            end,next_flush_time - cur_time)
        end
        return
    else
        self.delay_stay_remind_icon = GlobalTimerQuest:AddDelayTimer(function ()
            self:NeedShowTreasureBossIcon()
            self:CanCleStayQuest()
        end,stay_left_time)
    end

    local refresh_remind_time = 3600 --60分钟内
    local have_can_kill = TreasureHuntWGData.Instance:HaveTreasureBossCanKill()
    --self:CanCleOpenQuest()

    if have_can_kill and (last_flush_time + refresh_remind_time > cur_time) then
        local left_time = last_flush_time + refresh_remind_time - cur_time + 2
        TreasureHuntWGCtrl.Instance:ShowBossReflushIcon(last_flush_time,last_flush_time + refresh_remind_time)
        self.delay_cancle_remind_icon = GlobalTimerQuest:AddDelayTimer(function ()
            TreasureHuntWGCtrl.Instance:CloseBossReflushIcon()
        end, left_time)
    else
        self:CanCleOpenQuest()
        TreasureHuntWGCtrl.Instance:CloseBossReflushIcon()
    end
end

function TreasureBossWGData:GetIsHaveShow()
    return self.is_have_show
end

function TreasureBossWGData:SetIsHaveShow(bool)
    self.is_have_show = bool
end