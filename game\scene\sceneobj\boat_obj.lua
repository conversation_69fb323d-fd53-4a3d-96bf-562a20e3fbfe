BoatObj = BoatObj or BaseClass(SceneObj)

local SUANGXIU_BOAT_WATER_POS_Y = 211.9        	--双修船在水上的y位置
local ANMO_BOAT_WATER_POS_Y = 211.9
-- 温泉皮艇
function BoatObj:__init(boat_vo)
	self.obj_type = SceneObjType.BoatObj
	self:SetObjId(boat_vo.obj_id)
	self.vo = boat_vo

	--self.girl_girl_res_id = 2096

	self.mount_point_r = nil
	self.mount_point_l = nil

	self.is_has_vip4 = false

	self.model_offset = 0

	local is_cross = Scene.Instance:GetSceneType() == SceneType.KF_HotSpring
	local cfg = ConfigManager.Instance:GetAutoConfig(is_cross and "cross_hotspring_auto" or "hotspring_auto").other[1]
	self.higher_boat_vip_level = cfg and cfg.higher_boat_vip_level or 6

	self.res_id = cfg.sxzj_id2

	self.boy_boy_res_id = cfg.sxzj_id2  --任意一个 VIP6 使用这个
	self.boy_girl_res_id = cfg.sxzj_id1 --都小于VIP6
	self.anmo_boat_res_id = cfg.cbzj_id --按摩船Id
end

function BoatObj:__delete()
	local boy_obj = Scene.Instance:GetObjectByObjId(self.vo.boy_obj_id)
	local girl_obj = Scene.Instance:GetObjectByObjId(self.vo.girl_obj_id)

	local role_obj_id = GameVoManager.Instance:GetMainRoleVo().obj_id
	if role_obj_id == self.vo.boy_obj_id or role_obj_id == self.vo.girl_obj_id then
		local obj = Scene.Instance:GetObjectByObjId(role_obj_id)
		if obj and obj.draw_obj then
			if not IsNil(MainCameraFollow) then
				MainCameraFollow.Target = obj.draw_obj:GetLookAtPoint()
			end
		end
	end

	if nil ~= boy_obj and nil ~= boy_obj:GetRoot() and not IsNil(boy_obj:GetRoot().gameObject) then
		local boy_main_part_obj = boy_obj.draw_obj:_TryGetPartObj(SceneObjPart.Main)
		if nil ~= boy_main_part_obj then
			boy_main_part_obj.transform:SetParent(boy_obj.draw_obj.root.transform)
			self:ResetTransform(boy_main_part_obj.transform, 1, true)
		end

		local boy_main_part = boy_obj.draw_obj:GetPart(SceneObjPart.Main)
		if boy_obj:IsWaterWay() then
			boy_main_part:CrossFade(SceneObjAnimator.Idle_YouYong)
		else
			boy_main_part:CrossFade(SceneObjAnimator.Idle)
		end
	end

	if nil ~= girl_obj and nil ~= girl_obj:GetRoot() and not IsNil(girl_obj:GetRoot().gameObject) then
		local girl_main_part_obj = girl_obj.draw_obj:_TryGetPartObj(SceneObjPart.Main)
		if nil ~= girl_main_part_obj then
			girl_main_part_obj.transform:SetParent(girl_obj.draw_obj.root.transform)
			self:ResetTransform(girl_main_part_obj.transform, 0, true)
		end

		local girl_main_part = girl_obj.draw_obj:GetPart(SceneObjPart.Main)
		if girl_obj:IsWaterWay() then
			girl_main_part:CrossFade(SceneObjAnimator.Idle_YouYong)
		else
			girl_main_part:CrossFade(SceneObjAnimator.Idle)
		end
	end

	self.mount_point_r = nil
	self.mount_point_l = nil

	self.is_has_vip4 = nil

	if self.main_part_loaded_event then
		GlobalEventSystem:UnBind(self.main_part_loaded_event)
		self.main_part_loaded_event = nil
	end

	self.model_offset = 0

	GlobalTimerQuest:CancelQuest(self.delay_timer1)
	GlobalTimerQuest:CancelQuest(self.delay_timer2)
	GlobalTimerQuest:CancelQuest(self.delay_timer3)
	GlobalTimerQuest:CancelQuest(self.delay_timer4)
	self.delay_timer1 = nil
	self.delay_timer2 = nil
	self.delay_timer3 = nil
	self.delay_timer4 = nil
end

function BoatObj:InitShow()
	Character.InitShow(self)
	
	if self.draw_obj then
		self.draw_obj:SetWaterHeight(0)
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic then
			local flag = scene_logic:IsCanCheckWaterArea() and true or false
			self.draw_obj:SetCheckWater(flag)
		end
	end
end

function BoatObj:InitAppearance()
	self.name = self.vo.name
	local boy_obj = Scene.Instance:GetObjectByObjId(self.vo.boy_obj_id)
	local girl_obj = Scene.Instance:GetObjectByObjId(self.vo.girl_obj_id)
	--if boy_obj and girl_obj then
	--	local boy_vo_sex = boy_obj:GetVo().sex
	--	local girl_vo_sex = girl_obj:GetVo().sex
	--	if boy_vo_sex == girl_vo_sex and boy_obj:GetVo().sex == GameEnum.MALE then
	--		res_id = self.boy_boy_res_id
	--	elseif boy_vo_sex == girl_vo_sex and boy_obj:GetVo().sex ~= 1 then
	--		res_id = self.girl_girl_res_id
	--	elseif boy_vo_sex ~= girl_vo_sex then
	--		res_id = self.boy_girl_res_id
	--	end
	--end

	self.model_offset = 0
	if self.vo.action_type and self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO then
		self.res_id = self.anmo_boat_res_id
		self.model_offset = 1.5
	else
		--双修根据Vip显示船
		if boy_obj and girl_obj then
			local vip_level1 = boy_obj:GetVo().vip_level
			local vip_level2 = girl_obj:GetVo().vip_level
			if vip_level1 >= self.higher_boat_vip_level or vip_level2 >= self.higher_boat_vip_level then
				self.res_id = self.boy_boy_res_id
				self.is_has_vip4 = true
				self.model_offset = VIP_BOAT_WATER_POS_Y
			elseif vip_level1 < self.higher_boat_vip_level and vip_level2 < self.higher_boat_vip_level then
				self.model_offset = VIP_BOAT_NOT_WATER_POS_Y
				self.res_id = self.boy_girl_res_id
				self.is_has_vip4 = false
			end
		end
	end

	self:ChangeModel(SceneObjPart.Main, ResPath.GetMountModel(self.res_id))
end

function BoatObj:OnModelLoaded(part, obj)
	SceneObj.OnModelLoaded(self, part, obj)
	if part == SceneObjPart.Main then
		--由于不同的船节点位置不规范，只能根据对应船的节点来查找
		self.effect_point = obj.transform:Find("cg_point").transform:Find("effect").gameObject
		if self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO then
			self.anmo_point = obj.transform:Find("anmo_point")
		else
			self.cg_point = obj.transform:Find("cg_point")
		end
		
		if self.effect_point == nil then
			return
		end
		self.effect_point:SetActive(false)
		local obj1 = Scene.Instance:GetObjectByObjId(self.vo.boy_obj_id)
		local obj2 = Scene.Instance:GetObjectByObjId(self.vo.girl_obj_id)
		local role_obj_id = GameVoManager.Instance:GetMainRoleVo().obj_id
	    GuajiWGCtrl.Instance:CancelSelect()
		local boy_obj, girl_obj
        local is_same_sex = false
		if self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO then
			boy_obj = obj1
			girl_obj = obj2
		else
			if obj1 and obj1.vo then
				if obj1.vo.sex == GameEnum.FEMALE then
					girl_obj = obj1
					boy_obj = obj2
				else
					boy_obj = obj1
					girl_obj = obj2
				end
                --如果都是男的， 背靠背
                if obj1.vo.sex == obj2.vo.sex then
                    is_same_sex = true
                end
			end
		end

		boy_obj:SetIsInBoat(true)
		boy_obj:QuitStateMove()
		girl_obj:SetIsInBoat(true)
		girl_obj:QuitStateMove()
		if nil ~= boy_obj and nil ~= boy_obj:GetRoot() and not IsNil(boy_obj:GetRoot().gameObject) then
			local obj = boy_obj.draw_obj:_TryGetPartObj(SceneObjPart.Main)
			if nil ~= obj then
				local boy_main_part = boy_obj.draw_obj:GetPart(SceneObjPart.Main)
				if boy_main_part then
					if self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO then
						boy_main_part:CrossFade(SceneObjAnimator.massage)
                        obj.gameObject.transform:SetParent(self.anmo_point)   --按摩在同一边
                        obj.gameObject.transform:SetLocalPosition(0.25, 0.17, -1.21)
						obj.gameObject.transform.localRotation = Quaternion.Euler(0, -178, 0)
					else
						GlobalTimerQuest:CancelQuest(self.delay_timer3)
            			self.delay_timer3 = GlobalTimerQuest:AddDelayTimer(
							function ()
								self.effect_point:SetActive(not is_same_sex)
								--背对背
								if is_same_sex then
									boy_main_part:CrossFade(SceneObjAnimator.Pass)
									obj.gameObject.transform:SetParent(self.cg_point)
			                        obj.gameObject.transform:SetLocalPosition(0, 0.17, 0.5)
									obj.gameObject.transform.localRotation = Quaternion.Euler(0, -178, 0)
								else
									 --面对面
									boy_main_part:CrossFade(SceneObjAnimator.SHUANG_XIU)
									obj.gameObject.transform:SetParent(self.cg_point)
			                        obj.gameObject.transform:SetLocalPosition(0, 0.1, 1.93)
									obj.gameObject.transform.localRotation = Quaternion.Euler(0, -178, 0)
								end
								
							end, 0.1)
						--self:ResetTransform(obj.gameObject.transform, 1)
					end
				end
			else
                if not self.main_part_loaded_event then
					self.main_part_loaded_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_MAIN_PART_LOADED, BindTool.Bind(self.OnMainPartLoaded,self))
				end
			end
		end

		if nil ~= girl_obj and nil ~= girl_obj:GetRoot() and not IsNil(girl_obj:GetRoot().gameObject) then
			local obj = girl_obj.draw_obj:_TryGetPartObj(SceneObjPart.Main)
			if nil ~= obj then
				local girl_main_part = girl_obj.draw_obj:GetPart(SceneObjPart.Main)
				if nil ~= girl_main_part then
					GlobalTimerQuest:CancelQuest(self.delay_timer4)
            		self.delay_timer4 = GlobalTimerQuest:AddDelayTimer(
						function ()
							if nil ~= self.vo then
								if self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO then
									girl_main_part:CrossFade(SceneObjAnimator.by_massage)
								end
							end
					end, 0.1)
				end

				if self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO then
					obj.gameObject.transform:SetParent(self.anmo_point)
					obj.gameObject.transform:SetLocalPosition(-1, 0.15, 0)
					obj.gameObject.transform.localRotation = Quaternion.Euler(0, 0, 0)
					obj.gameObject.transform:SetLocalScale(1, 1, 1)
				else
					if is_same_sex then
						girl_main_part:CrossFade(SceneObjAnimator.Pass)
						obj.gameObject.transform:SetParent(self.cg_point)
                        obj.gameObject.transform:SetLocalPosition(0, 0.17, 1.33)
						obj.gameObject.transform.localRotation = Quaternion.Euler(0, 0, 0)
					else
						 --面对面
						self.effect_point:SetActive(not is_same_sex)
						girl_main_part:CrossFade(SceneObjAnimator.SHUANG_XIU)
						obj.gameObject.transform:SetParent(self.cg_point)
                        obj.gameObject.transform:SetLocalPosition(0, 0, 0.28)
						obj.gameObject.transform.localRotation = Quaternion.Euler(0, 0, 0)
					end
					--self:ResetTransform(obj.gameObject.transform, 0)
				end
			else
				if not self.main_part_loaded_event then
					self.main_part_loaded_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_MAIN_PART_LOADED, BindTool.Bind(self.OnMainPartLoaded,self))
				end
			end
		end

		local pos = self.draw_obj:GetRoot().gameObject.transform.position
		local pos_y = self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO and ANMO_BOAT_WATER_POS_Y or SUANGXIU_BOAT_WATER_POS_Y
		self.draw_obj:GetRoot():SetLocalPosition(pos.x, pos_y ,pos.z)
	end
end

--param: part_obj, scene_obj
function BoatObj:SetBoyPartObjAnimation(obj, boy_obj)
    if nil ~= obj then
        local boy_main_part = boy_obj.draw_obj:GetPart(SceneObjPart.Main)
        if boy_main_part then
            if self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO then
                boy_main_part:CrossFade(SceneObjAnimator.massage)
                obj.gameObject.transform:SetParent(self.anmo_point)
                obj.gameObject.transform:SetLocalPosition(1, 0, 0)
				obj.gameObject.transform.localRotation = Quaternion.Euler(0, -90, 0)
            else
            	GlobalTimerQuest:CancelQuest(self.delay_timer1)
                self.delay_timer1 = GlobalTimerQuest:AddDelayTimer(
                    function ()
                    	obj.gameObject.transform:SetParent(self.cg_point)
                        -- --如果是男男
                        -- if is_two_man then
                        --     boy_main_part:CrossFade(self.is_has_vip4 and SceneObjAnimator.Pass or SceneObjAnimator.SHUANG_XIU2)
                        -- else
                        --     boy_main_part:CrossFade(self.is_has_vip4 and SceneObjAnimator.Pass or SceneObjAnimator.SHUANG_XIU)
                        -- end
                    end,0.1)
                self:ResetTransform(obj.gameObject.transform, 1)
            end
        end
        local pos = self.draw_obj:GetRoot().gameObject.transform.position
        local pos_y = self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO and ANMO_BOAT_WATER_POS_Y or SUANGXIU_BOAT_WATER_POS_Y
        self.draw_obj:GetRoot():SetLocalPosition(pos.x, pos_y ,pos.z)
    end
end

function BoatObj:SetGirlPartObjAnimation(obj, girl_obj)
    if nil ~= obj then
        
        local girl_main_part = girl_obj.draw_obj:GetPart(SceneObjPart.Main)
        if nil ~= girl_main_part then
            GlobalTimerQuest:CancelQuest(self.delay_timer2)
            self.delay_timer2 = GlobalTimerQuest:AddDelayTimer(
                function ()
                    if self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO then
                        girl_main_part:CrossFade(SceneObjAnimator.by_massage)
                        obj.gameObject.transform:SetParent(self.anmo_point)
                    else
                    	obj.gameObject.transform:SetParent(self.cg_point)
                        girl_main_part:CrossFade(self.is_has_vip4 and SceneObjAnimator.Pass or SceneObjAnimator.SHUANG_XIU)
                    end
            end,0.1)
        else
            --print_error("空的>>>girl_main_part>>>>")
        end
        self:ResetTransform(obj.gameObject.transform, 0)
        local pos = self.draw_obj:GetRoot().gameObject.transform.position
        local pos_y = self.vo.action_type == HOTSPRING_BOAT_TYPE.ANMO and ANMO_BOAT_WATER_POS_Y or SUANGXIU_BOAT_WATER_POS_Y
        self.draw_obj:GetRoot():SetLocalPosition(pos.x, pos_y ,pos.z)
    end
end

function BoatObj:OnMainPartLoaded(part, obj, scene_obj)
	if not obj or not scene_obj then
		return
	end
	local obj_id = scene_obj.vo.obj_id
	if part == SceneObjPart.Main then
		if self.vo and obj_id == self.vo.boy_obj_id then
			local boy_obj = Scene.Instance:GetObjectByObjId(self.vo.boy_obj_id)
            self:SetBoyPartObjAnimation(obj, boy_obj)
		elseif self.vo and obj_id == self.vo.girl_obj_id  then
            local girl_obj = Scene.Instance:GetObjectByObjId(self.vo.girl_obj_id)
            self:SetGirlPartObjAnimation(obj, girl_obj)
		end
	else

	end
end

function BoatObj:ResetTransform(transform, site, is_reset)
	if self.is_has_vip4 and not is_reset then
		if site == 0 then
			transform:SetLocalPosition(0, 0.55, -0.375)
			transform.localRotation = Quaternion.Euler(-90, 90, 90)
		else
			transform:SetLocalPosition(0, -0.315, -0.43)
			transform.localRotation = Quaternion.Euler(90, 90, 90)
		end
	else
		transform:SetLocalPosition(0,0,0)
		transform.localRotation = Quaternion.Euler(0, 0, 0)
	end
	transform:SetLocalScale(1, 1, 1)
end

function BoatObj:GetBoatAttachPoint(obj_id)
	if obj_id == self.vo.boy_obj_id then
		return self.mount_point_r
	else
		return self.mount_point_l
	end
end

function BoatObj:IsCharacter()
	return false
end

function BoatObj:IsBoat()
	return true
end

function BoatObj:DeleteDrawObj()
	SceneObj.DeleteDrawObj(self)
	local obj1 = (self.vo and self.vo.boy_obj_id) and Scene.Instance:GetObjectByObjId(self.vo.boy_obj_id) or nil
	local obj2 = (self.vo and self.vo.girl_obj_id) and Scene.Instance:GetObjectByObjId(self.vo.girl_obj_id) or nil
	if obj1 ~= nil then
		obj1:SetIsInBoat(false)
	end
	if obj2 ~= nil then
		obj2:SetIsInBoat(false)
	end
end