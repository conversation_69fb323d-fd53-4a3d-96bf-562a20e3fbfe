-- 暗器详情-六个小技能
-- 暗器详情-背包列表
HWDetailSkillRender = HWDetailSkillRender or BaseClass(BaseRender)

function HWDetailSkillRender:__init(node)
    self.node = node
    XUI.AddClickEventListener(self.node_list.btn_icon, BindTool.Bind(self.ShowTip, self))
end

function HWDetailSkillRender:ShowTip()
    HiddenWeaponWGCtrl.Instance:ShowEquipSkillView(self.data)
end

function HWDetailSkillRender:OnNodeClick()
    SysMsgWGCtrl.Instance:ErrorRemind(self.node.gameObject.name)
end

function HWDetailSkillRender:LoadCallBack(a)
    --print_log(a)
end

function HWDetailSkillRender:__delete()
    self.node = nil
end

function HWDetailSkillRender:SetData(data)
    self.data = data
    self:OnFlush()
end

function HWDetailSkillRender:SetMaskState(flag)
   self.node_list.mask:SetActive(flag)
   self.node_list.btn_icon:SetActive(not flag)
   self.node_list.txt_level:SetActive(not flag)
end

function HWDetailSkillRender:OnFlush()
    if not self.data then
        self:SetMaskState(true)
        return
    end
     self:SetMaskState(false)
    local cfg = self.data.cfg
    local is_active = self.data.is_active
    self.node_list["btn_icon"].image:LoadSprite(ResPath.GetSkillIconById(cfg.skill_icon))
    self.node_list["btn_icon"].image:SetNativeSize()

    self.node_list["txt_level"].text.text = "LV." .. cfg.skill_level
    self.node_list["txt_level"]:SetActive(is_active)
     XUI.SetGraphicGrey(self.node_list["btn_icon"], not is_active)
end

function HWDetailSkillRender:OnSelectChange(is_select)
    -- if nil == is_select then return end
    -- self.node_list.bg_res_bar_selected:SetActive(is_select)
end

------------------- 专属技能
HWDetailSpecialSkillRender = HWDetailSpecialSkillRender or BaseClass(BaseRender)

function HWDetailSpecialSkillRender:__init(node)
    XUI.AddClickEventListener(self.node_list.btn_icon, BindTool.Bind(self.ShowTip, self))
end

function HWDetailSpecialSkillRender:OnFlush()
    local awaken_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(self.data.big_type)
    local def_cfg = awaken_cfg and awaken_cfg[1]
    if not def_cfg then
        return
    end

    self.node_list.btn_icon.image:LoadSprite(ResPath.GetSkillIconById(def_cfg.skill_icon))
    self.node_list.btn_icon.image:SetNativeSize()
end

function HWDetailSpecialSkillRender:ShowTip()
    HiddenWeaponWGCtrl.Instance:ShowDetailSkillView(self.data)
end
