CustomizedSuitStarUpView = CustomizedSuitStarUpView or BaseClass(SafeBaseView)

function CustomizedSuitStarUpView:__init()
	self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/customized_suit_ui_prefab", "layout_customized_suit_star_up")
	self:SetMaskBgAlpha(250/255)
end

function CustomizedSuitStarUpView:__delete()
end

function CustomizedSuitStarUpView:OpenCallBack()
end

function CustomizedSuitStarUpView:CloseCallBack()
end

function CustomizedSuitStarUpView:LoadCallBack()
	if self.left_suit == nil then
		self.left_suit = StarUpSuitCell.New(self.node_list.left_suit)
	end

	if self.right_suit == nil then
		self.right_suit = StarUpSuitCell.New(self.node_list.right_suit)
	end

	if self.cur_skill_left_attr_list == nil then
        self.cur_skill_left_attr_list = {}
        for i = 1, 9 do
            local skill_obj = self.node_list.left_layout_attr:FindObj(string.format("attr_%d", i))
            if skill_obj then
                local cell = CustomizedSkillAttrRender.New(skill_obj)
                cell:SetIndex(i)
                self.cur_skill_left_attr_list[i] = cell
            end
        end
    end

	if self.cur_skill_right_attr_list == nil then
        self.cur_skill_right_attr_list = {}
        for i = 1, 9 do
            local skill_obj = self.node_list.right_layout_attr:FindObj(string.format("attr_%d", i))
            if skill_obj then
                local cell = CustomizedSkillAttrRender.New(skill_obj)
                cell:SetIndex(i)
                self.cur_skill_right_attr_list[i] = cell
            end
        end
    end

	if self.cur_skill_left_item == nil then
		self.cur_skill_left_item = CustomizedSkillShowRender.New(self.node_list.left_skill)
	end

	if self.cur_skill_right_item == nil then
		self.cur_skill_right_item = CustomizedSkillShowRender.New(self.node_list.right_skill)
	end

	if self.suit_skill == nil then
		self.suit_skill = CustomizedSkillShowRender.New(self.node_list.suit_skill)
	end

	--加载模型时装
	if nil == self.user_model then
		self.user_model = CommonUserModelRender.New(self.node_list["display"])
		self.user_model:AddUiRoleModel(self)
	end

	XUI.AddClickEventListener(self.node_list["one_key_btn"], BindTool.Bind(self.OperateSkillBtn, self))
end

function CustomizedSuitStarUpView:ReleaseCallBack()
	self.suit = nil							---当前的套装id

	if self.left_suit then
		self.left_suit:DeleteMe()
		self.left_suit = nil
	end

	if self.right_suit then
		self.right_suit:DeleteMe()
		self.right_suit = nil
	end

	if self.cur_skill_left_attr_list and #self.cur_skill_left_attr_list > 0 then
		for _, skill_cell in ipairs(self.cur_skill_left_attr_list) do
			skill_cell:DeleteMe()
			skill_cell = nil
		end

		self.cur_skill_left_attr_list = nil
	end

	if self.cur_skill_right_attr_list and #self.cur_skill_right_attr_list > 0 then
		for _, skill_cell in ipairs(self.cur_skill_right_attr_list) do
			skill_cell:DeleteMe()
			skill_cell = nil
		end

		self.cur_skill_right_attr_list = nil
	end
	
	if self.cur_skill_left_item then
		self.cur_skill_left_item:DeleteMe()
		self.cur_skill_left_item = nil
	end

	if self.cur_skill_right_item then
		self.cur_skill_right_item:DeleteMe()
		self.cur_skill_right_item = nil
	end

	if self.suit_skill then
		self.suit_skill:DeleteMe()
		self.suit_skill = nil
	end

	if self.user_model then
		self.user_model:DeleteMe()
		self.user_model = nil
	end
end

function CustomizedSuitStarUpView:SetSuit(suit)
	self.suit = suit
end

function CustomizedSuitStarUpView:OnFlush(param_t, index)
	if self.suit == nil then
		return
	end

	local cur_data = CustomizedSuitWGData.Instance:GetThemeSkillBySuit(self.suit)
	if not cur_data then
		return
	end


	self:RefreshSuitStar(cur_data)
	self:RefreshGuradeRoot(cur_data)
	self:FlushSuitModel(cur_data)
	self.node_list.one_key_btn_red:CustomSetActive(CustomizedSuitWGData.Instance:GetOneThemeStarUpRed(self.suit))
end

-- 刷新套装展示星级
function CustomizedSuitStarUpView:RefreshSuitStar(cur_data)
	local left_suit_data = {}
	left_suit_data.suit = self.suit
	left_suit_data.star = cur_data.star

	if self.left_suit then
		self.left_suit:SetData(left_suit_data)
	end

	local aim_star, is_full = self:GetNextStar(cur_data)
	--self.node_list.right_suit:CustomSetActive(not is_full)

	if self.right_suit then
		local right_suit_data = {}
		if is_full then
			right_suit_data = left_suit_data
		else
			right_suit_data.suit = self.suit
			right_suit_data.star = aim_star
		end

		self.right_suit:SetData(right_suit_data)
	end
end

function CustomizedSuitStarUpView:GetNextStar(cur_data)
	--刷新一下文字
	local is_full = false
	local aim_star = cur_data.star + 1 
	if aim_star > 10 then
		aim_star = 10
		is_full = true
	end

	return aim_star, is_full
end

---刷新升级相关界面
function CustomizedSuitStarUpView:RefreshGuradeRoot(cur_data)
	local aim_star, is_full = self:GetNextStar(cur_data)
	local min_level, cur, max = CustomizedSuitWGData.Instance:GetCustomizedSuitMinLevBySuit(self.suit, aim_star)
	local color = cur >= max and COLOR3B.D_GREEN or COLOR3B.D_RED
	local color_str = ToColorStr(string.format("%d/%d", cur, max), color)
	self.node_list.star_up_tips.text.text = string.format(Language.Customized.SkillAttrTips, cur_data.suit_name, max, aim_star, color_str)
	self.node_list.one_key_btn_text.text.text = is_full and Language.HolyDarkWeapon.MaxStar or Language.Customized.SkillAttrAtive

	self.node_list.full_star:CustomSetActive(is_full)
	self.node_list.one_key_btn:CustomSetActive(not is_full)

	---刷新属性面板
	--self.node_list.right:SetActive(not is_full)
	local cur_star_attr_data, next_star_attr_data = CustomizedSuitWGData.Instance:GetCustomizedSuitSatrAttr(self.suit, cur_data.star)

	if self.cur_skill_left_attr_list and #self.cur_skill_left_attr_list > 0 then
		for index, attr_cell in ipairs(self.cur_skill_left_attr_list) do
			attr_cell:SetVisible(index <= #cur_star_attr_data)---表不会为空

			if attr_cell and cur_star_attr_data and cur_star_attr_data[index] then
				attr_cell:SetData(cur_star_attr_data[index])
			end
		end
	end

	if is_full then
		next_star_attr_data = cur_star_attr_data
	end

	if next_star_attr_data ~= nil then
		if self.cur_skill_right_attr_list and #self.cur_skill_right_attr_list > 0 then
			for index, attr_cell in ipairs(self.cur_skill_right_attr_list) do
				attr_cell:SetVisible(index <= #next_star_attr_data)

				if attr_cell and next_star_attr_data[index] then
					attr_cell:SetData(next_star_attr_data[index])
				end
			end
		end
	end

	---刷新技能面板
	--先刷新当前的技能
	--self.node_list.skill_tips:CustomSetActive(not is_full)
	--self.node_list.skill_layout_show:CustomSetActive(not is_full)
	local cur_skill_data = CustomizedSuitWGData.Instance:GetSelectSkillBySkillId(cur_data.skill_id, cur_data.skill_level)
	local theme_star_data = CustomizedSuitWGData.Instance:GetThemeStarCfgBySuit(self.suit, aim_star)

	if cur_skill_data and self.cur_skill_left_item then
		self.cur_skill_left_item:SetData(cur_skill_data)
		self.suit_skill:SetData(cur_skill_data)
	end

	local skill_level = cur_data.is_upgrade and cur_data.skill_level + 1 or cur_data.skill_level
	local next_skill_data = CustomizedSuitWGData.Instance:GetSelectSkillBySkillId(cur_data.skill_id, skill_level)
	self.node_list.right_skill:CustomSetActive(next_skill_data ~= nil)
	self.node_list.suit_skill:CustomSetActive(next_skill_data ~= nil and cur_data.upgrade_red)

	if is_full then
		next_skill_data = cur_skill_data
	end

	if next_skill_data and self.cur_skill_right_item then
		self.cur_skill_right_item:SetData(next_skill_data)
	end

	if theme_star_data then
		self.node_list.suit_skill_desc.text.text = theme_star_data.skill_change
	end
end

-- 刷新模型
function CustomizedSuitStarUpView:FlushSuitModel(cur_data)
	if not self.user_model then
		return
	end

	local show_list = WardrobeWGData.Instance:GetActivationPartList(self.suit)
	if IsEmptyTable(show_list) then
		return
	end

	local user_model_data = {}
	user_model_data.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	local fashion_cfg

	-- 获取当前可炫彩的套装
	local start_cfg = WardrobeWGData.Instance:GetXuanCaiBySuitLevel(self.suit, cur_data.star)
	local start_level = start_cfg and start_cfg.level or 0

	if start_cfg then	-- 设置标签图片
		-- local bundle, asset = ResPath.GetRawImagesPNG(start_cfg.title_tex)
		-- self.node_list.suit_title_raw.raw_image:LoadSprite(bundle, asset, function ()
		-- 	self.node_list.suit_title_raw.raw_image:SetNativeSize()
		-- end)
	end

	for k, data in pairs(show_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then		-- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then																		-- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				local resouce = fashion_cfg.resouce

				if start_level ~= nil and start_level ~= 0 then
					local _, new_resouce = NewAppearanceColorfulWGData.Instance:GetAppearanceColorfulCfgByOld(
						WARDROBE_PART_TYPE.FASHION, 
						data.param1, data.param2, 
						start_level, 
						fashion_cfg.resouce
					)
		
					resouce = new_resouce
				end

				user_model_data.body_res_id = ResPath.GetFashionModelId(prof, resouce)
			end
		elseif data.type == WARDROBE_PART_TYPE.MOUNT then	
			fashion_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, data.param1)
			if fashion_cfg then
				user_model_data.mount_res_id = fashion_cfg.appe_image_id

				if start_level ~= nil and start_level ~= 0 then
					local _, new_resouce = NewAppearanceColorfulWGData.Instance:GetAppearanceColorfulCfgByOld(
						WARDROBE_PART_TYPE.MOUNT, 
						data.param1, 0, 
						start_level, 
						user_model_data.mount_res_id
					)
		
					user_model_data.mount_res_id = new_resouce
				end

				user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then											-- 化鲲
			fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
			if fashion_cfg then
				user_model_data.mount_res_id = fashion_cfg.active_id

				if start_level ~= nil and start_level ~= 0 then
					local _, new_resouce = NewAppearanceColorfulWGData.Instance:GetAppearanceColorfulCfgByOld(
						WARDROBE_PART_TYPE.HUA_KUN, 
						data.param1, 0, 
						start_level, 
						user_model_data.mount_res_id
					)
		
					user_model_data.mount_res_id = new_resouce
				end

				user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		end
	end

	for k, v in pairs(show_list) do
		self:ShowModelByData(v, user_model_data, start_level)
	end
	user_model_data = self:ChangeModelShowScale(user_model_data)
	self.user_model:SetData(user_model_data)

end


function CustomizedSuitStarUpView:ChangeModelShowScale(user_model_data)

	-- 跟定制属性用同一套显示方式 因为界面一样
	local data = WardrobeWGData.Instance:GetThemeCfgBySuit(self.suit)
	if IsEmptyTable(data) then
		return user_model_data
	end

	local pos_str = data.tzsx_pos
    local rotate_str = data.tzsx_rot
    if pos_str and pos_str ~= "" then
        local pos = Split(pos_str, "|")
		user_model_data.model_adjust_root_local_position  = u3dpool.vec3(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
        -- RectTransform.SetAnchoredPosition3DXYZ(self.node_list.display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
    end

    if rotate_str and rotate_str ~= "" then
        local rot = Split(rotate_str, "|")
		user_model_data.role_rotation = u3dpool.vec3(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
    end

    local scale = data.main_scale2
    if scale and scale ~= "" then
    	user_model_data.model_adjust_root_local_scale = scale
    end
	user_model_data.model_rt_type = ModelRTSCaleType.M
	return user_model_data
end

function CustomizedSuitStarUpView:ShowModelByData(data, user_model_data, start_level)
	if IsEmptyTable(data) then
		return
	end

	if data.type == WARDROBE_PART_TYPE.FASHION then				-- 时装大类
		WardrobeWGData.Instance:AssembleRoleModelDataByTypeIndex(data.param1, data.param2, user_model_data, start_level)
	end
end


---点击
function CustomizedSuitStarUpView:OperateSkillBtn()
	local act, mum, max_num = CustomizedSuitWGData.Instance:IsCustomizedSkillAct(self.suit)
	if mum < max_num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Customized.NotAtive)
		return
	end

	local cur_data = CustomizedSuitWGData.Instance:GetThemeSkillBySuit(self.suit)
	if (not cur_data) or (cur_data.skill_id == 0) then
		return
	end

	if cur_data.star_red then		---可以升级属性
		CustomizedSuitWGCtrl.Instance:SendWardrobeTypeStarUp(self.suit, 0, 0)

		if cur_data.upgrade_red then
			CustomizedSuitWGCtrl.Instance:SendWardrobeTypeUpgrade(self.suit, 0, 0)
		end
		return
	end
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Customized.SkillAttrNotAtive)
end

-------------------------------------套装标签item---------------------------
StarUpSuitCell = StarUpSuitCell or BaseClass(BaseRender)

function StarUpSuitCell:OnFlush()
    if self.data == nil then
        return
    end

    local bundle, asset = ResPath.GetCustomizedImg("a3_ystz_tx_" .. self.data.suit)
    --self.node_list.cell_bg.image:LoadSprite(bundle, asset)
    local star_res_list = GetSuitStarImgResByStar(self.data.star)
    for i = 1, 5 do
        self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
		self.node_list["star" .. i].image:SetNativeSize()
    end
end
-------------------------------------技能展示item---------------------------
CustomizedSkillShowRender = CustomizedSkillShowRender or BaseClass(BaseRender)
function CustomizedSkillShowRender:OnFlush()
	if self.data then
		if self.node_list.skill_name then
			self.node_list.skill_name.text.text = string.format(Language.Customized.NameStr, self.data.skill_name)
		end

		self.node_list.skill_level.text.text = string.format(Language.Customized.LevelStr, self.data.skill_level)

		if self.data.skill_icon then
			self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))
			self.node_list.skill_icon.image:SetNativeSize()
		end
	end
end

-------------------------------------属性展示item---------------------------
CustomizedSkillAttrRender = CustomizedSkillAttrRender or BaseClass(BaseRender)
function CustomizedSkillAttrRender:OnFlush()
	if self.data then
		local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
		local per_desc = is_per and "%" or ""
		local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
		self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, false, false)
		self.node_list.attr_value.text.text = string.format("%s%s", value_str, per_desc)
	end
end