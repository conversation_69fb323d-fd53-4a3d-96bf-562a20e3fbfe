XianQiTeDianZhenXuanView = XianQiTeDianZhenXuanView or BaseClass(SafeBaseView)

local Model_Move_Loop_Pos = {
	[1] = { start_x = -235, start_y = 0, end_x = -235, end_y = 30, loop_time = 2 },
	[2] = { start_x = 0, start_y = 30, end_x = 0, end_y = 0, loop_time = 2 },
	[3] = { start_x = 238, start_y = 0, end_x = 238, end_y = 30, loop_time = 2 },
}

function XianQiTeDianZhenXuanView:__init(view_name)
	self.view_layer = UiLayer.Pop

	self:AddViewResource(0, "uis/view/sixiang_call_prefab", "xqzl_zhenxuan_view")
	self:SetMaskBg(true, true)
end

function XianQiTeDianZhenXuanView:LoadCallBack()
	self:InitParam()
	self:InitListener()
	self:InitModel()
	self:InitRewardItem()
end

function XianQiTeDianZhenXuanView:ReleaseCallBack()
	if self.m_item_cell then
		self.m_item_cell:DeleteMe()
		self.m_item_cell = nil
	end

	if self.model_go_list then
		for k, v in pairs(self.model_go_list) do
			if v.model_pos then
				UITween.KillMoveLoop(v.model_pos.gameObject)
			end

			if v.model then
				v.model:DeleteMe()
			end
			v = nil
		end
		self.model_go_list = nil
	end
	CountDownManager.Instance:RemoveCountDown("xianqi_tedian_zhenxuan")
end

function XianQiTeDianZhenXuanView:ShowIndexCallBack(index)
	self:DoPanelAnim()
end

function XianQiTeDianZhenXuanView:DoPanelAnim()
	local scale_time = 0.6 --底板缩放展开时间
	local alpha_time = 0.8 --右侧面板上的信息淡入时间

	UITween.CleanAllTween(GuideModuleName.XianQiZhenXuan)
	UITween.FakeHideShow(self.node_list["tween_root"])
	-- self.node_list["model_root"]:SetActive(false)

	RectTransform.SetSizeDeltaXY(self.node_list["root_bg"].rect, 300, 468)
	self.node_list["root_bg"].rect:DOSizeDelta(Vector2(888, 468), scale_time)
	local tween_cb = function()
		-- self.node_list["model_root"]:SetActive(true)
	end
	UITween.AlphaShow(GuideModuleName.XianQiZhenXuan, self.node_list["tween_root"], 0, 1, alpha_time, nil, tween_cb)
end

function XianQiTeDianZhenXuanView:OnFlush(param_t)
	self:RefreshView()
end

function XianQiTeDianZhenXuanView:InitParam()

end

function XianQiTeDianZhenXuanView:InitModel()
	self.model_go_list = {}
	local t_info
	for i = 1, 3 do
		self.model_go_list[i] = {}
		self.model_go_list[i].model_pos = self.node_list["model_pos_" .. i]
		self.model_go_list[i].model_name_bg = self.node_list["model_name_bg_" .. i]
		self.model_go_list[i].model_name = self.node_list["model_name_" .. i]
		self.model_go_list[i].model = RoleModel.New()

		local display_data = {
			parent_node = self.node_list["model_pos_" .. i],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = false,
		}
		
		self.model_go_list[i].model:SetRenderTexUI3DModel(display_data)
		-- self.model_go_list[i].model:SetUI3DModel(self.node_list["model_pos_" .. i].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
		t_info = Model_Move_Loop_Pos[i]
		if t_info then
			UITween.MoveLoop(self.node_list["model_pos_" .. i].gameObject, u3dpool.vec2(t_info.start_x, t_info.start_y),
				u3dpool.vec2(t_info.end_x, t_info.end_y), t_info.loop_time)
		end
	end
end

function XianQiTeDianZhenXuanView:InitRewardItem()
	self.m_item_cell = ItemCell.New(self.node_list["item_cell_root"])
end

function XianQiTeDianZhenXuanView:InitListener()
	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind1(self.OnClickBuyBtn, self))
	XUI.AddClickEventListener(self.node_list.tip_btn, BindTool.Bind1(self.OnClickTipBtn, self))
end

function XianQiTeDianZhenXuanView:OnClickTipBtn()
	RuleTip.Instance:SetContent(Language.SiXiangCall.XQZLZhenXuanRuleContent, Language.SiXiangCall.XQZLZhenXuanRuleTitle)
end

function XianQiTeDianZhenXuanView:OnClickBuyBtn()
	if self.cur_act_open_state then
		XianQiTeDianWGCtrl.Instance:BuyXianQiTeDian(self.cur_act_id)
	else
		self:Close()
	end
end

function XianQiTeDianZhenXuanView:RefreshView()
	self.cur_act_id = XianQiTeDianWGData.Instance:GetCurZhenXuan()
	self.cur_act_open_state = XianQiTeDianWGData.Instance:CheckSubActIsOpen(self.cur_act_id)
	self:FlushActTime()
	self:FlushDiscount()
	self:FlushBuyBtnState()
	self:FlushRewardItem()
	self:FlushShowModel()
end

-- 刷新折扣
function XianQiTeDianZhenXuanView:FlushRewardItem()
	local spe_sale_cfg = XianQiTeDianWGData.Instance:GetLingJiaSaleCfg(2)
	if spe_sale_cfg and spe_sale_cfg.special_sale_item then
		self.m_item_cell:SetData(spe_sale_cfg.special_sale_item)
	end
end

function XianQiTeDianZhenXuanView:FlushShowModel()
	--加载模型
	local xianqi_zhenxian_sale_id = 2
	local model_cfg = XianQiTeDianWGData.Instance:GetLingJiaBtnShowCfg(xianqi_zhenxian_sale_id)
	local other_cfg = XianQiTeDianWGData.Instance:GetLingJiaSaleOtherCfg()
	if IsEmptyTable(model_cfg) or IsEmptyTable(other_cfg) then
		return
	end
	local model_id_tab = Split(model_cfg.model or "", "#")
	local m_bundle, m_asset
	if not IsEmptyTable(model_id_tab) then
		for i, v in ipairs(self.model_go_list) do
			if model_id_tab[i] then
				m_bundle, m_asset = ResPath.GetShenJiModel(model_id_tab[i])
				if m_bundle and m_asset then
					v.model:SetMainAsset(m_bundle, m_asset)
				end
			end
		end
	end
	self.model_go_list[1].model_name.text.text = other_cfg.left_name or ""
	self.model_go_list[2].model_name.text.text = other_cfg.middle_name or ""
	self.model_go_list[3].model_name.text.text = other_cfg.right_name or ""
end

-- 刷新折扣
function XianQiTeDianZhenXuanView:FlushDiscount()

end

-- 刷新购买按钮
function XianQiTeDianZhenXuanView:FlushBuyBtnState()
	local sale_info = XianQiTeDianWGData.Instance:GetSubActSaleInfo(self.cur_act_id, 1)
	local cfg_list = XianQiTeDianWGData.Instance:GetActSaleCfg(self.cur_act_id)
	local btn_text = Language.SiXiangCall.HasFetch
	local is_gray = false
	if self.cur_act_open_state and sale_info and not IsEmptyTable(cfg_list) then
		if sale_info.status == YuanShenSaleSubActSaleStatus.NotBuy then
			--未购买
			btn_text = string.format(Language.SiXiangCall.BuyText, cfg_list[1].special_sale_price)
		elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch then
			--已购买未领取（rmb商品会用到）
			btn_text = Language.SiXiangCall.CurCanFetch
		elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyAndFetched then
			--已购买已领取
			btn_text = Language.SiXiangCall.HasFetch
			is_gray = true
		end
	end

	XUI.SetGraphicGrey(self.node_list.buy_btn, is_gray)
	self.node_list.buy_btn_label.text.text = btn_text
end

---[[ 活动结束倒计时
function XianQiTeDianZhenXuanView:FlushActTime()
	CountDownManager.Instance:RemoveCountDown("xianqi_tedian_zhenxuan")
	local sever_time = TimeWGCtrl.Instance:GetServerTime()
	local end_time = XianQiTeDianWGData.Instance:GetTeDianEndTimeStamp()
	if end_time <= sever_time then
		self.node_list.time_label.text.text = ""
		return
	end

	self:UpdateCountDown(sever_time, end_time)

	CountDownManager.Instance:AddCountDown(
		"xianqi_tedian_zhenxuan",
		BindTool.Bind(self.UpdateCountDown, self),
		BindTool.Bind(self.FlushActTime, self),
		end_time,
		nil,
		1
	)
end

function XianQiTeDianZhenXuanView:UpdateCountDown(elapse_time, total_time)
	local time_str = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
	self.node_list.time_label.text.text = string.format(Language.SiXiangCall.CloseTimeDesc, time_str)
end

--]]
