--动态运营活动 限时累充
function OperationActivityView:InitLeiChongView()
	self.leichong_reward_list = nil
	self.jundu_list = nil
end

function OperationActivityView:ShowLeiChongCallBack()
    self.need_leichong_tween = true
end

function OperationActivityView:LoadIndexCallBackLeiChongView()
	if not self.leichong_reward_list then
		self.leichong_reward_list = AsyncListView.New(DTYYLeiChongRender, self.node_list["total_reward_list"])
	end

	if not self.total_model_display then
        self.total_model_display = OperationActRender.New(self.node_list["total_display_model"])
        self.total_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
    --刷新累充边角界面
    --self:PlayGiftTween()
	self:DTYYLeiChongLoadImage()
	self:DTYYFlushLeiChongView()
end

function OperationActivityView:DTYYReleaseLeiChongView()
	OperationLeiChongRechargeWGData.Instance:ResetLeiChongModelIndex()

	if self.leichong_reward_list then
		self.leichong_reward_list:DeleteMe()
		self.leichong_reward_list = nil
	end

	if self.total_model_display then
        self.total_model_display:DeleteMe()
        self.total_model_display = nil
	end

	self.jundu_list = nil
end

function OperationActivityView:DTYYLeiChongLoadImage()

end

function OperationActivityView:DTYYFlushLeiChongView()
	self:DTYYFlushLeiChongList()
	self:OnFlushLeiChongTipsAndRule()
	self:DTYYLeiChongFlushModel()
end

function OperationActivityView:OpenIndexCallBackLeiChong()
	OperationLeiChongRechargeWGCtrl.Instance:SendLeiChongRechargeReq(YUNYINGHUODONG_XIANSHILEICHONG_TYPE.TYPE_INFO)
	if not self:IsLoadedIndex(TabIndex.operation_act_total_recharge) then
   		return
   	end
end

-- 模型
function OperationActivityView:DTYYLeiChongFlushModel()
	local model_cfg = OperationLeiChongRechargeWGData.Instance:GetLeiChongModelCfg()
	if IsEmptyTable(model_cfg) then
		return
	end

	local display_data = {}
	display_data.not_show_active = true
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = model_cfg["model_bundle_name"]
    display_data.asset_name = model_cfg["model_asset_name"]
    local model_show_type = tonumber(model_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

	if model_cfg.model_rot and model_cfg.model_rot ~= "" then
		local rot_list = string.split(model_cfg.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_cfg.model_scale and model_cfg.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_cfg.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L
    self.total_model_display:SetData(display_data)

    local pos_x, pos_y = 0, 0
	if model_cfg.model_pos and model_cfg.model_pos ~= "" then
		local pos_list = string.split(model_cfg.model_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end
	RectTransform.SetAnchoredPositionXY(self.node_list.total_display_model.rect, pos_x, pos_y)
end

-- 累充列表
function OperationActivityView:DTYYFlushLeiChongList()
	local list = OperationLeiChongRechargeWGData.Instance:GetLeiChongRewardList()
	self.leichong_reward_list:SetDataList(list)
end

function OperationActivityView:OnFlushLeiChongTipsAndRule()
	local view_cfg = OperationLeiChongRechargeWGData.Instance:GetShouChongViewCfgByInterface()
	if view_cfg == nil then
		return
	end
	local title = OperationActivityWGData.Instance:GetActivityNameByActivityNum(ACTIVITY_TYPE.OPERA_ACT_TOTAL_RECHARGE)
	self:SetRuleInfo(view_cfg.rule_2, title)
	self:SetOutsideRuleTips(view_cfg.rule_1)
end

----------------------------- DTYYLeiChongRender ----------------------
DTYYLeiChongRender = DTYYLeiChongRender or BaseClass(BaseRender)
function DTYYLeiChongRender:LoadCallBack()
	self.item_list = {}
	self.btn_lingqu = self.node_list["btn_lingqu"]
	self.btn_recharge = self.node_list["btn_recharge"]
	self.btn_lingqu.button:AddClickListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
	self.btn_recharge.button:AddClickListener(BindTool.Bind1(self.OnClickRechargeHnadler, self))
	self.item_cell = {}
end

function DTYYLeiChongRender:ReleaseCallBack()
	if self.item_list then
		for k,v in pairs(self.item_list) do
			v:DeleteMe()
		end
		self.item_list = nil
	end

	self.btn_lingqu = nil
	self.btn_recharge = nil

	if self.item_cell then
        for i,v in ipairs(self.item_cell) do
            v:DeleteMe()
        end
        self.item_cell = nil
    end
end

function DTYYLeiChongRender:OnFlush()
	if not self.data then
		return
	end 

	local view_cfg = OperationLeiChongRechargeWGData.Instance:GetShouChongViewCfgByInterface()
	if view_cfg == nil then
		return
	end

	local cur_xianyu = OperationLeiChongRechargeWGData.Instance:GetOwnXianYu()
	local color =  cur_xianyu >= self.data.cfg.stage_value and COLOR3B.C2 or COLOR3B.C3
	local str = string.format("%d/%d", cur_xianyu, self.data.cfg.stage_value)
	self.node_list["recharge_value"].text.text = ToColorStr(str, color)
	self.node_list["btn_recharge"]:SetActive(cur_xianyu < self.data.cfg.stage_value and self.data.receive_state == 0)
	self.node_list["btn_lingqu"]:SetActive(cur_xianyu >= self.data.cfg.stage_value and self.data.receive_state == 1)
	self.node_list["btn_yilingqu"]:SetActive(self.data.receive_state == 2)

    if self.data.cfg.reward_item then
        local data_list = {}
        for k, v in pairs(self.data.cfg.reward_item) do
            if v.item_id ~= 0 then
                data_list[#data_list + 1] = v
            end
        end
        data_list = OperationActivityWGData.Instance:SortDataByItemColor(data_list)

		for i = 1, #data_list do
	        if not self.item_cell[i] then
	            self.item_cell[i] = ItemCell.New(self.node_list["cell_group"])
	        end

	        self.item_cell[i]:SetData(data_list[i])
			self.item_cell[i]:SetRedPointEff(cur_xianyu >= self.data.cfg.stage_value and self.data.receive_state == 1)
			self.item_cell[i]:SetLingQuVisible(self.data.receive_state == 2)
	    end
	end
end

function DTYYLeiChongRender:SortDataList(data_list)
	if data_list and not IsEmptyTable(data_list) then
		for k,v in pairs(data_list) do
			if v.item_id and v.item_id > 0 then
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
                v.color = item_cfg and item_cfg.color or 1
			else
                v.color = 0
            end
		end
		table.sort(data_list, SortTools.KeyUpperSorters("color"))
    end
    return data_list
end

function DTYYLeiChongRender:OnClickRewardHnadler(sender)
	OperationLeiChongRechargeWGCtrl.Instance:SendLeiChongRechargeReq(YUNYINGHUODONG_XIANSHILEICHONG_TYPE.TYPE_DRAW,self.data.cfg.ID)
end

function DTYYLeiChongRender:OnClickRechargeHnadler(sender)
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end

