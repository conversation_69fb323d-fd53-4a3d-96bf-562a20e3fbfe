﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class YYCelestialObjectWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(YYCelestialObject), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>un<PERSON>("GetGradientColor", GetGradientColor);
		<PERSON><PERSON>unction("GetGradientLookup", GetGradientLookup);
		L<PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("ShaftMultiplier", get_ShaftMultiplier, set_ShaftMultiplier);
		<PERSON><PERSON>("HorizonMultiplier", get_HorizonMultiplier, set_HorizonMultiplier);
		<PERSON>.<PERSON>ar("IsSun", get_IsSun, set_IsSun);
		<PERSON><PERSON>("OrbitType", get_OrbitType, set_OrbitType);
		<PERSON><PERSON>("RotateYDegrees", get_RotateYDegrees, set_RotateYDegrees);
		<PERSON><PERSON>("TintColor", get_TintColor, set_TintColor);
		<PERSON><PERSON>("TintIntensity", get_TintIntensity, set_TintIntensity);
		L.RegVar("Scale", get_Scale, set_Scale);
		L.RegVar("LightPower", get_LightPower, set_LightPower);
		L.RegVar("LightMultiplier", get_LightMultiplier, set_LightMultiplier);
		L.RegVar("Renderer", get_Renderer, null);
		L.RegVar("MeshFilter", get_MeshFilter, null);
		L.RegVar("Light", get_Light, null);
		L.RegVar("Collider", get_Collider, null);
		L.RegVar("OrbitTypeIsPerspective", get_OrbitTypeIsPerspective, null);
		L.RegVar("ViewportPosition", get_ViewportPosition, null);
		L.RegVar("IsActive", get_IsActive, null);
		L.RegVar("LightIsActive", get_LightIsActive, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGradientColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			YYCelestialObject obj = (YYCelestialObject)ToLua.CheckObject<YYCelestialObject>(L, 1);
			UnityEngine.Gradient arg0 = (UnityEngine.Gradient)ToLua.CheckObject<UnityEngine.Gradient>(L, 2);
			UnityEngine.Color o = obj.GetGradientColor(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGradientLookup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			YYCelestialObject obj = (YYCelestialObject)ToLua.CheckObject<YYCelestialObject>(L, 1);
			float o = obj.GetGradientLookup();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ShaftMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float ret = obj.ShaftMultiplier;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ShaftMultiplier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_HorizonMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			UnityEngine.Vector3 ret = obj.HorizonMultiplier;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HorizonMultiplier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsSun(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			bool ret = obj.IsSun;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsSun on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OrbitType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			WeatherOrbitType ret = obj.OrbitType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OrbitType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RotateYDegrees(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float ret = obj.RotateYDegrees;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotateYDegrees on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TintColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			UnityEngine.Color ret = obj.TintColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TintColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TintIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float ret = obj.TintIntensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TintIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Scale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float ret = obj.Scale;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Scale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LightPower(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float ret = obj.LightPower;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LightPower on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LightMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float ret = obj.LightMultiplier;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LightMultiplier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Renderer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			UnityEngine.Renderer ret = obj.Renderer;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Renderer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MeshFilter(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			UnityEngine.MeshFilter ret = obj.MeshFilter;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MeshFilter on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Light(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			UnityEngine.Light ret = obj.Light;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Light on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Collider(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			UnityEngine.Collider ret = obj.Collider;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Collider on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OrbitTypeIsPerspective(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			bool ret = obj.OrbitTypeIsPerspective;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OrbitTypeIsPerspective on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ViewportPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			UnityEngine.Vector3 ret = obj.ViewportPosition;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ViewportPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsActive(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			bool ret = obj.IsActive;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsActive on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LightIsActive(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			bool ret = obj.LightIsActive;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LightIsActive on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ShaftMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.ShaftMultiplier = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ShaftMultiplier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_HorizonMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.HorizonMultiplier = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HorizonMultiplier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsSun(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsSun = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsSun on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OrbitType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			WeatherOrbitType arg0 = (WeatherOrbitType)ToLua.CheckObject(L, 2, typeof(WeatherOrbitType));
			obj.OrbitType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OrbitType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RotateYDegrees(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.RotateYDegrees = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotateYDegrees on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_TintColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.TintColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TintColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_TintIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.TintIntensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TintIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Scale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Scale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Scale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_LightPower(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.LightPower = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LightPower on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_LightMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYCelestialObject obj = (YYCelestialObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.LightMultiplier = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LightMultiplier on a nil value");
		}
	}
}

