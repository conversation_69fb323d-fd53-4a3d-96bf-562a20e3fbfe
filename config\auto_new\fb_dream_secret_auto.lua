-- F-副本-幻梦秘境.xls
local item_table={
[1]={item_id=27763,num=5,is_bind=1},
[2]={item_id=27762,num=5,is_bind=1},
[3]={item_id=28666,num=5,is_bind=1},
[4]={item_id=28665,num=20,is_bind=1},
[5]={item_id=27763,num=10,is_bind=1},
[6]={item_id=27762,num=10,is_bind=1},
[7]={item_id=28666,num=10,is_bind=1},
[8]={item_id=28665,num=30,is_bind=1},
[9]={item_id=27763,num=15,is_bind=1},
[10]={item_id=27762,num=15,is_bind=1},
[11]={item_id=28666,num=15,is_bind=1},
[12]={item_id=28665,num=40,is_bind=1},
[13]={item_id=27763,num=20,is_bind=1},
[14]={item_id=27762,num=20,is_bind=1},
[15]={item_id=28666,num=20,is_bind=1},
[16]={item_id=28665,num=50,is_bind=1},
[17]={item_id=27763,num=25,is_bind=1},
[18]={item_id=27762,num=25,is_bind=1},
[19]={item_id=28666,num=25,is_bind=1},
[20]={item_id=28665,num=60,is_bind=1},
[21]={item_id=27763,num=30,is_bind=1},
[22]={item_id=27762,num=30,is_bind=1},
[23]={item_id=28666,num=30,is_bind=1},
[24]={item_id=28665,num=70,is_bind=1},
[25]={item_id=27763,num=35,is_bind=1},
[26]={item_id=27762,num=35,is_bind=1},
[27]={item_id=28666,num=35,is_bind=1},
[28]={item_id=28665,num=80,is_bind=1},
[29]={item_id=27763,num=40,is_bind=1},
[30]={item_id=27762,num=40,is_bind=1},
[31]={item_id=28666,num=40,is_bind=1},
[32]={item_id=28665,num=90,is_bind=1},
[33]={item_id=27763,num=50,is_bind=1},
[34]={item_id=27762,num=50,is_bind=1},
[35]={item_id=28666,num=50,is_bind=1},
[36]={item_id=28665,num=100,is_bind=1},
[37]={item_id=28670,num=2,is_bind=1},
[38]={item_id=27764,num=3,is_bind=1},
[39]={item_id=28670,num=1,is_bind=1},
[40]={item_id=27764,num=2,is_bind=1},
[41]={item_id=28666,num=8,is_bind=1},
[42]={item_id=28665,num=18,is_bind=1},
[43]={item_id=27764,num=1,is_bind=1},
[44]={item_id=27763,num=4,is_bind=1},
[45]={item_id=27762,num=4,is_bind=1},
[46]={item_id=28666,num=6,is_bind=1},
[47]={item_id=28665,num=16,is_bind=1},
[48]={item_id=28665,num=14,is_bind=1},
[49]={item_id=27763,num=3,is_bind=1},
[50]={item_id=27762,num=3,is_bind=1},
[51]={item_id=28665,num=12,is_bind=1},
[52]={item_id=28666,num=4,is_bind=1},
[53]={item_id=28665,num=10,is_bind=1},
[54]={item_id=28665,num=8,is_bind=1},
[55]={item_id=27763,num=2,is_bind=1},
[56]={item_id=27762,num=2,is_bind=1},
[57]={item_id=28666,num=3,is_bind=1},
[58]={item_id=28665,num=6,is_bind=1},
[59]={item_id=28665,num=5,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
grade={
{}
},

grade_meta_table_map={
},
level={
{}
},

level_meta_table_map={
},
wave={
{monster_icon=1,},
{wave=2,pass_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},monster_icon=2,},
{wave=3,pass_reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8]},},
{wave=4,pass_reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12]},},
{wave=5,pass_reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[16]},monster_icon=2,is_show_boss=1,},
{grade=2,pass_reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[19],[3]=item_table[20]},},
{wave=2,pass_reward_item={[0]=item_table[21],[1]=item_table[22],[2]=item_table[23],[3]=item_table[24]},},
{wave=3,pass_reward_item={[0]=item_table[25],[1]=item_table[26],[2]=item_table[27],[3]=item_table[28]},},
{wave=4,pass_reward_item={[0]=item_table[29],[1]=item_table[30],[2]=item_table[31],[3]=item_table[32]},},
{grade=2,wave=5,pass_reward_item={[0]=item_table[33],[1]=item_table[34],[2]=item_table[35],[3]=item_table[36]},is_show_boss=1,}
},

wave_meta_table_map={
[4]=1,	-- depth:1
[7]=6,	-- depth:1
[8]=6,	-- depth:1
[9]=6,	-- depth:1
},
monster={
{},
{wave=2,monster_id=6801,},
{wave=3,monster_id=6802,},
{wave=4,monster_id=6803,},
{wave=5,monster_id=6804,is_not_die=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

monster_meta_table_map={
[7]=2,	-- depth:1
[8]=3,	-- depth:1
[9]=4,	-- depth:1
[10]=5,	-- depth:1
},
card={
{icon=1,},
{seq=1,param1=791,desc="7：攻击加成1200",color=2,},
{seq=2,param1=792,name="伤害加成",desc="7：伤害加成500",},
{index=1,param1=793,name="伤害减免",desc="7：伤害减伤1200",color=2,},
{index=1,seq=1,},
{index=1,param1=791,desc="7：攻击加成1200",color=2,},
{index=2,param1=792,name="伤害加成",desc="7：伤害加成500",},
{index=2,seq=1,},
{index=2,seq=2,},
{index=3,param1=791,icon=3,desc="7：攻击加成1200",color=2,},
{index=3,seq=1,icon=3,},
{index=3,seq=2,icon=3,},
{index=4,type=3,param1=100,icon=4,name="技能能量",desc="加100技能能量",},
{seq=1,},
{seq=2,}
},

card_meta_table_map={
[2]=1,	-- depth:1
[3]=1,	-- depth:1
[6]=9,	-- depth:1
[8]=4,	-- depth:1
[11]=7,	-- depth:1
[12]=4,	-- depth:1
[14]=13,	-- depth:1
[15]=14,	-- depth:2
},
rank_reward={
{reward_item={[0]=item_table[37],[1]=item_table[38],[2]=item_table[1],[3]=item_table[2],[4]=item_table[7],[5]=item_table[4]},},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[39],[1]=item_table[40],[2]=item_table[1],[3]=item_table[2],[4]=item_table[41],[5]=item_table[42]},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[39],[1]=item_table[43],[2]=item_table[44],[3]=item_table[45],[4]=item_table[46],[5]=item_table[47]},},
{min_rank=4,max_rank=7,reward_item={[0]=item_table[43],[1]=item_table[44],[2]=item_table[45],[3]=item_table[3],[4]=item_table[48]},},
{min_rank=8,max_rank=10,reward_item={[0]=item_table[43],[1]=item_table[49],[2]=item_table[50],[3]=item_table[3],[4]=item_table[51]},},
{min_rank=11,max_rank=15,reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[52],[3]=item_table[53]},},
{min_rank=16,max_rank=20,reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[52],[3]=item_table[54]},},
{min_rank=21,max_rank=30,reward_item={[0]=item_table[55],[1]=item_table[56],[2]=item_table[57],[3]=item_table[58]},},
{min_rank=31,max_rank=50,},
{min_rank=51,max_rank=100,}
},

rank_reward_meta_table_map={
},
other_default_table={open_level=300,scene_id=9800,max_buy_time_count=3,add_time=10,add_price=100,beast_time=3,},

grade_default_table={min_open_day=1,max_open_day=999999,grade=1,consume_lingyu=100,rank_condition=100000,},

level_default_table={grade=1,need_level=300,need_cap=5000000,card_pool=0,skill_id=504,need_skill_power=100,boss_name="风邪",boss_img="a3_hhmj_rw1",},

wave_default_table={grade=1,wave=1,wave_time=200,pass_reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[52],[3]=item_table[53]},monster_icon=3,is_show_boss=0,},

monster_default_table={grade=1,wave=1,seq=0,monster_id=6800,monster_num=1,kill_skill_power=100,is_boss=1,is_not_die=0,},

card_default_table={index=0,seq=0,type=1,param1=790,param2=0,param3=0,param4=0,icon=2,name="攻击加成",desc="7：攻击加成500",color=1,},

rank_reward_default_table={grade=1,min_rank=1,max_rank=1,reward_item={[0]=item_table[55],[1]=item_table[56],[2]=item_table[57],[3]=item_table[59]},}

}

