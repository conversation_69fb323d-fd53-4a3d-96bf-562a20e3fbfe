SunLibraryView = SunLibraryView or BaseClass(SafeBaseView)

function SunLibraryView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(812,574)})
    self:AddViewResource(0, "uis/view/sun_rainbow_ui_prefab", "layout_sun_library")
end

function SunLibraryView:__delete()

end

function SunLibraryView:ReleaseCallBack()
	if self.library_grid_list then
        self.library_grid_list:DeleteMe()
        self.library_grid_list = nil
    end
end

function SunLibraryView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.TianShenLingHe.LibraryTitle
	local bundle = "uis/view/sun_rainbow_ui_prefab"
	local asset = "sun_library_cell"
	if self.library_grid_list == nil then
		self.library_grid_list = AsyncBaseGrid.New()
		self.library_grid_list:CreateCells({col = 7,
								change_cells_num = 1,
								list_view = self.node_list["posy_library_list"],
								assetBundle = bundle,
								assetName = asset,
								itemRender = SunLibraryItemRender})
		self.library_grid_list:SetStartZeroIndex(false)
	end
end

function SunLibraryView:OnFlush(param_t)
	local list_data = SunRainbowWgData.Instance:ShowNeedDisplay()
	self.library_grid_list:SetDataList(list_data)
end




------------------SunLibraryItemRender------------------
SunLibraryItemRender = SunLibraryItemRender or BaseClass(BaseRender)

function SunLibraryItemRender:LoadCallBack()
	self.sun_show_cell = BaseLingHeCell.New(nil, self.node_list.item_pos)
end

function SunLibraryItemRender:__delete()
	if self.sun_show_cell then
		self.sun_show_cell:DeleteMe()
        self.sun_show_cell = nil
    end
end

function SunLibraryItemRender:OnFlush()
	if not self.data then
		return
	end
    local reward_item = self.data.reward_item
	self.sun_show_cell:SetData({item_id = reward_item.item_id})
end