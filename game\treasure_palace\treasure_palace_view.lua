local TREASURE_PALACE_BG = {
    [TabIndex.treasure_palace_enter_reward] = "a2_zbd_beijin2",
    [TabIndex.treasure_palace_rebate] = "a2_zbd_beijin2",
    [TabIndex.treasure_palace_everyday_recharge] = "a2_zbd_bg",
    [TabIndex.treasure_palace_lifetime_recharge] = "a2_zbd_beijin",
    [TabIndex.treasure_palace_gift] = "a2_zbd_beijin3",
}

TreasurePalaceView = TreasurePalaceView or BaseClass(SafeBaseView)

function TreasurePalaceView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
    self.default_index = TabIndex.treasure_palace_rebate
    self:SetMaskBg()

    local treasure_palace_path = "uis/view/treasure_palace_ui_prefab"
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_panel")
    self:AddViewResource(TabIndex.treasure_palace_rebate, treasure_palace_path, "layout_zbd_rebate_view")
    self:AddViewResource(TabIndex.treasure_palace_enter_reward, treasure_palace_path, "layout_zbd_enter_game_reward_view")
    self:AddViewResource(TabIndex.treasure_palace_everyday_recharge, treasure_palace_path, "layout_everyday_recharge")
    self:AddViewResource(TabIndex.treasure_palace_lifetime_recharge, treasure_palace_path, "layout_lifetime_recharge")
    self:AddViewResource(TabIndex.treasure_palace_gift, treasure_palace_path, "layout_zbd_gift_view")
    self:AddViewResource(0, treasure_palace_path, "VerticalTabbar")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")

    self.remind_tab = {
        {RemindName.Treasurepalace_Rebate},
		{RemindName.Treasurepalace_Enter_Reward},
        {RemindName.Treasurepalace_Lifetime_Recharge},
        {RemindName.Treasurepalace_Everyday_Recharge},
        {RemindName.Treasurepalace_Gift},
    }
end

-- function TreasurePalaceView:CalcShowIndex()
-- 	return TreasurePalaceData.Instance:GetOneRemindTabIndex()
-- end

function TreasurePalaceView:OpenCallBack()
    TreasurePalaceCtrl.Instance:SendZhenBaoDianClientReq(ZHENBAODIAN_OPERA_TYPE.INFO)
end

function TreasurePalaceView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TreasurePalace.ViewName

    if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.TreasurePalace.TabGrop, nil, "uis/view/treasure_palace_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
    end

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }

        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.node_list.btn_rule_tips:SetActive(true)
	XUI.AddClickEventListener(self.node_list.btn_rule_tips, BindTool.Bind(self.OnClickTipsBtn, self))
end

function TreasurePalaceView:LoadIndexCallBack(index)
    if index == TabIndex.treasure_palace_lifetime_recharge then
        self:LifeTimeRechargeInit()
    elseif index == TabIndex.treasure_palace_everyday_recharge then
        self:EveryDayRechargeInit()
    elseif index == TabIndex.treasure_palace_enter_reward then
        self:ZBKEnterRewardLoadIndexCallBack()
    elseif index == TabIndex.treasure_palace_gift then
        self:ZBKGiftLoadIndexCallBack()
    elseif index == TabIndex.treasure_palace_rebate then
        self:ZBKRebateLoadIndexCallBack()
    end
end

function TreasurePalaceView:ChangeBg(index)
    if TREASURE_PALACE_BG[index] then
        local bg_bundle, bg_asset = ResPath.GetF2RawImagesPNG(TREASURE_PALACE_BG[index])
        self.node_list.RawImage_tongyong.raw_image:LoadSprite(bg_bundle, bg_asset)
    end
end

function TreasurePalaceView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    self:LifeTimeRechargeReleaseCallBack()
    self:EveryDayRechargeReleaseCallBack()
    self:ZBKEnterRewardReleaseCallBack()
    self:ZBKRebateReleaseCallBack()
    self:ZBKGiftReleaseCallBack()
end

function TreasurePalaceView:ShowIndexCallBack(index)
    self:ChangeBg(index)

    if index == TabIndex.treasure_palace_lifetime_recharge then
        self:LifeTimeRechargeShowIndexCallBack()
    elseif index == TabIndex.treasure_palace_enter_reward then
        self:ZBKEnterRewardShowIndexCallBack()
    elseif index == TabIndex.treasure_palace_gift then
        self:ZBKGiftShowIndexCallBack()
    elseif index == TabIndex.treasure_palace_rebate then
        self:ZBKRebateShowIndexCallBack()
    end
end

function TreasurePalaceView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if k == "all" then
            if index == TabIndex.treasure_palace_lifetime_recharge then
                self:LifeTimeRechargeFlush()
            elseif index == TabIndex.treasure_palace_everyday_recharge then
                self:EveryDayRechargeFlush()
            elseif index == TabIndex.treasure_palace_enter_reward then
                self:ZBKEnterRewardOnFlush()
            elseif index == TabIndex.treasure_palace_gift then
                self:ZBKGiftOnFlush()
            elseif index == TabIndex.treasure_palace_rebate then
                self:ZBKRebateOnFlush()
            end
        else
            if index == TabIndex.treasure_palace_lifetime_recharge then
                self.lifetime_need_jump = true
                self:FlushLifeTimeToggleList()
                self:LifeTimeToggleListJumpToRemind()
            elseif index == TabIndex.treasure_palace_everyday_recharge then
                self:EveryDayRechargeFlush()
            elseif index == TabIndex.treasure_palace_enter_reward then
                self:ZBKEnterRewardOnFlush()
            elseif index == TabIndex.treasure_palace_gift then
                self:ZBKGiftOnFlush()
            elseif index == TabIndex.treasure_palace_rebate then
                self:ZBKRebateOnFlush()
            end
        end
    end
end

function TreasurePalaceView:OnClickTipsBtn()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.TreasurePalace.TipsTitle)
	rule_tip:SetContent(Language.TreasurePalace.TipsContent, nil, nil, nil, true)
end