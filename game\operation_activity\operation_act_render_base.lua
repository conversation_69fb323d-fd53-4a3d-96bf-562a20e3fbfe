OperationActRenderBase = OperationActRenderBase or BaseClass()
function OperationActRenderBase:__init(node_list)
    self.node_list = node_list
end

function OperationActRenderBase:__delete()
    self.node_list = nil
	self.data = nil
end

function OperationActRenderBase:SetData(data)
	self.data = data
end

function OperationActRenderBase:LoadSprite(bundle_name, asset_name, callback, cbdata)
	LoadSprite(self, bundle_name, asset_name, callback, cbdata)
end

function OperationActRenderBase:LoadSpriteAsync(bundle_name, asset_name, callback, cbdata)
	LoadSpriteAsync(self, bundle_name, asset_name, callback, cbdata)
end

function OperationActRenderBase:LoadRawImage(arg0, arg1, arg2)
	LoadRawImage(self, arg0, arg1, arg2)
end

function OperationActRenderBase:Show()
end

function OperationActRenderBase:Reset()
end