local TOGGLE_MAX_COMPOSE = 16
local LIST_CELL_COUNT = 28		--list最大格子数量
local ITEM_MAX_HEIGHT = 54

function NewComposeView:InitXQSComposeView()
    self.cur_xqs_product_data = nil
    self.set_xqs_left_data = true

    if self.xqs_cell_list == nil then
        self.xqs_cell_list = {}
        for i = 1, 4 do
            self.xqs_cell_list[i] = ItemCell.New(self.node_list["xqs_cell_pos_" .. i])
            self.xqs_cell_list[i]:SetIsShowTips(false)
            self.xqs_cell_list[i]:SetCellBgEnabled(false)
            self.xqs_cell_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSelectXQSItem, self))
        end
    end

    -- 连线特效  暂时不用
    -- if self.xqs_line_effect == nil then
    --     local effect_root = self.node_list.effect_root_xqs_compose
    --     local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_HC2xian)
    --     local async_loader = AllocAsyncLoader(self, "compose_xqs_effect")
    --     async_loader:SetParent(effect_root.transform)
    --     async_loader:SetIsUseObjPool(true)
    --     async_loader:Load(bundle_name, asset_name,
    --     function (obj)
    --         local temp_list = {}
    --         for i = 1, 6 do
    --             local line = obj.transform:Find("line_" .. i)
    --             temp_list[i] = line and line.gameObject
    --         end
    --         self.xqs_line_effect = temp_list
    --         self:FlushXQSLineEffect()
    --     end)
    -- end

    if self.xqs_product_cell == nil then
        self.xqs_product_cell = ItemCell.New(self.node_list["xqs_product_grid"])
        self.xqs_product_cell:SetIsShowTips(true)
    end

    if nil == self.xqs_accordion_list then
        self.xqs_cell_group = {}
        self.xqs_accordion_list = {}
        self.xqs_left_big_btn_list = {}
        self.load_xqsc_cell_complete = false
        local content_node = self.node_list.xqs_content
        for i = 1, TOGGLE_MAX_COMPOSE do
            self.xqs_accordion_list[i] = content_node:FindObj("List" .. i)

            local big_btn_cell = XQSCBigBtnItem.New(content_node:FindObj("SelectBtn" .. i))
            big_btn_cell:SetIndex(i)
            big_btn_cell:SetClickCallBack(BindTool.Bind(self.OnClickXQSLeftBigBtn, self))
            self.xqs_left_big_btn_list[i] = big_btn_cell

            self:LoadXQSLeftCell(i)
        end
    end

    XUI.AddClickEventListener(self.node_list.btn_xqs_hecheng, BindTool.Bind1(self.OnClickXQSCompose, self))
end

function NewComposeView:ReleaseXQSComposeView()
    self.xqs_line_effect = nil
    self.xqs_accordion_list = nil
    self.load_xqsc_cell_complete = nil
    self.set_xqs_left_data = nil
    self.cur_xqs_product_data = nil
    self.xqs_acc_data_list = nil

    if self.xqs_left_big_btn_list ~= nil then
        for k,v in ipairs(self.xqs_left_big_btn_list) do
            v:DeleteMe()
        end
        self.xqs_left_big_btn_list = nil
    end

	if self.xqs_cell_list ~= nil then
		for k,v in ipairs(self.xqs_cell_list) do
			v:DeleteMe()
		end
		self.xqs_cell_list = nil
	end

    if nil ~= self.xqs_product_cell then
        self.xqs_product_cell:DeleteMe()
        self.xqs_product_cell = nil
    end

    if self.xqs_cell_group then
        for k,v in pairs(self.xqs_cell_group) do
            for k1,v1 in pairs(v) do
                v1:DeleteMe()
            end
            self.xqs_cell_group[k] = nil
        end
        self.xqs_cell_group = nil
    end

end

function NewComposeView:OnClickXQSLeftBigBtn(cell)
    -- print_error("【-----点击 大----】：")
	if cell == nil then
		return
	end

    local index = cell:GetIndex()
	if self.xqs_cell_group ~= nil and self.xqs_cell_group[index] ~= nil then
		self.xqs_cell_group[index][1]:SetToggleState(true)
	end
end

function NewComposeView:LoadXQSLeftCell(index)
    local res_async_loader = AllocResAsyncLoader(self, "xqs_hecheng_item" .. index)
	res_async_loader:Load("uis/view/equipment_suit_ui_prefab", "equipment_hecheng_item", nil, function(new_obj)
		local item_vo = {}
		for i = 1, LIST_CELL_COUNT do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.xqs_accordion_list[index].transform, false)
			obj:GetComponent("Toggle").group = self.xqs_accordion_list[index].toggle_group

			local item_render = XQSCIToggleChildRender.New(obj)
            item_render:AddClickEventListener(BindTool.Bind(self.ClickChildCellCallBack, self), true)
			item_vo[i] = item_render

			if index == TOGGLE_MAX_COMPOSE and i == LIST_CELL_COUNT then
				self.load_xqsc_cell_complete = true
			end
		end

        -- 加载完自动选择
		self.xqs_cell_group[index] = item_vo
		if self.load_xqsc_cell_complete then
			self:ShowFlushXQSCompose(self:GetTranferParentIndex(), self:GetTranferChildIndex())
		end
	end)
end

function NewComposeView:ShowFlushXQSCompose(tranfer_parent_index, child_index)
    if not self.load_xqsc_cell_complete then
		return
	end

    -- 左侧列表数据
    local tab_index = self:GetShowIndex()
    local big_type = math.floor(tab_index / 10)
    local menu_list = ComposeWGData.Instance:GetMenu(tab_index % 10)
	self.xqs_acc_data_list = menu_list and menu_list.sub_menu
    if IsEmptyTable(self.xqs_acc_data_list) then
    	return
    end

    -- set 列表 data
    if self.set_xqs_left_data then
    	for i = 1, TOGGLE_MAX_COMPOSE do
            self.xqs_left_big_btn_list[i]:SetAccordionElementState(false)
            local data
            self.xqs_left_big_btn_list[i]:SetData(self.xqs_acc_data_list[i])
            self.xqs_accordion_list[i]:SetActive(false)

    		if self.xqs_cell_group[i] ~= nil then
                local child_data = {}
                if self.xqs_acc_data_list[i] ~= nil then
                    child_data = ComposeWGData.Instance:GetXQSComposeChildList(big_type, self.xqs_acc_data_list[i].menu_type, self.xqs_acc_data_list[i].id)
                end

    			for k,v in pairs(self.xqs_cell_group[i]) do
    				v:SetData(child_data[k])
    			end
    		end
    	end

        self.set_xqs_left_data = false
    end

    -- flush remind
    self:FlushXQSCRemind()

    -- select btn
	self:RealXQSSelectBtn(tranfer_parent_index, child_index)
end

function NewComposeView:FlushXQSCRemind()
    if IsEmptyTable(self.xqs_acc_data_list) then
        return
    end

    for i = 1, TOGGLE_MAX_COMPOSE do
        if self.xqs_acc_data_list[i] ~= nil then
            self.xqs_left_big_btn_list[i]:FlushRemind()
            if self.xqs_cell_group[i] ~= nil then
                for k,v in pairs(self.xqs_cell_group[i]) do
                    v:FlushRemind()
                end
            end
        end
    end
end

function NewComposeView:RealXQSSelectBtn(tmp_parent_index, tranfer_child_index)
    if IsEmptyTable(self.xqs_acc_data_list) or self.xqs_acc_data_list[tmp_parent_index] == nil then
    	return
    end

	self.xqs_left_big_btn_list[tmp_parent_index]:SetAccordionElementState(true)

	GlobalTimerQuest:AddDelayTimer(function ()
        local cell = self.xqs_cell_group and self.xqs_cell_group[tmp_parent_index] and self.xqs_cell_group[tmp_parent_index][tranfer_child_index]
		if cell then
			local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.canvas.worldCamera, cell.view.transform.position)
			local flag = UnityEngine.RectTransformUtility.RectangleContainsScreenPoint(self.node_list.xqs_toggle_scroll.rect, screen_pos_tbl, self.canvas.worldCamera)
			if not flag then
                self.node_list.xqs_toggle_scroll.scroll_rect.verticalNormalizedPosition = 0
            end

            cell:SetToggleState(true)
		end

		self:SetTranferChildIndex(nil)
	end, 0.1)
end

function NewComposeView:ClickChildCellCallBack(item)
    -- print_error("【----点击 子 回调-----】：")
    if item == nil then
        return
    end

    local data = item:GetData()
    if data == nil then
        return
    end

    self.cur_xqs_product_data = data
	local item_config = ItemWGData.Instance:GetItemConfig(self.cur_xqs_product_data.product_id)
	self.node_list["cur_name"].text.text = ToColorStr(item_config.name, ITEM_COLOR[item_config.color])
    self:FlushXQSProductInfo()
    self:CleanStuffInfo()
end

function NewComposeView:FlushXQSProductInfo()
    if self.cur_xqs_product_data == nil then
        return
    end

    local product_id = self.cur_xqs_product_data.product_id
    local num = self.cur_xqs_product_data.product_num
    if self.xqs_product_cell then
        self.xqs_product_cell:SetData({item_id = product_id, num = num, is_bind = 0})
    end

    self.node_list.xqs_need_stuff_desc.text.text = self.cur_xqs_product_data.stuff_desc
end

function NewComposeView:CleanStuffInfo()
    if self.cur_xqs_product_data == nil then
        return
    end

    local stuff_num = self.cur_xqs_product_data.stuff_num
    for k,v in ipairs(self.xqs_cell_list) do
        v:ClearData()
        if k <= stuff_num then
            v:SetItemIcon(ResPath.GetCommonImages("a3_ty_jia"))
            v:SetButtonComp(true)
        else
            v:SetItemIcon(ResPath.GetCommonImages("a3_ty_suo"))
            v:SetButtonComp(false)
        end
--[[ 
        local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_0")
        v:SetCellBg(bundle, asset) ]]
    end

    self:FlushXQSLineEffect()
    self:FlushXQSComposeBtn()
end

function NewComposeView:FlushXQSLineEffect()
	if not IsEmptyTable(self.xqs_line_effect) then
		local mark_list = {false, false, false, false}
        local stuff_num = self.cur_xqs_product_data and self.cur_xqs_product_data.stuff_num or 0

		for i = 1, 4 do
			mark_list[i] = i <= stuff_num
			if self.xqs_line_effect[i] then
				self.xqs_line_effect[i]:SetActive(mark_list[i])
			end
		end

		if self.xqs_line_effect[5] then
			self.xqs_line_effect[5]:SetActive(mark_list[2] or mark_list[4])
		end

		if self.xqs_line_effect[6] then
			self.xqs_line_effect[6]:SetActive(mark_list[1] or mark_list[3])
		end
	end
end

-- 已选择材料装备
function NewComposeView:GetXQSAlreadySelectList()
    local select_list = {}
    if self.xqs_cell_list == nil then
        return select_list
    end

    for k,v in ipairs(self.xqs_cell_list) do
        local data = v:GetData()
        if data and data.item_id > 0 then
            select_list[data.index] = true
        end
    end

    return select_list
end

-- 已选择材料装备数
function NewComposeView:GetXQSAlreadySelectNum()
    local num = 0
    if self.xqs_cell_list == nil then
        return num
    end

    for k,v in ipairs(self.xqs_cell_list) do
        local data = v:GetData()
        if data and data.item_id > 0 then
            num = num + 1
        end
    end

    return num
end

function NewComposeView:OnClickSelectXQSItem(item)
    if self.cur_xqs_product_data == nil then
        return
    end

    -- print_error("【---点击 材料 格子------】：")
    local data = item:GetData()
    if IsEmptyTable(data) then
        -- 弹出列表
        local stuff_list = ComposeWGData.Instance:GetXQSCStuffInBagList(self.cur_xqs_product_data, self:GetXQSAlreadySelectList())
        if IsEmptyTable(stuff_list) then
            TipWGCtrl.Instance:OpenEquipGetWayView(nil, self.cur_xqs_product_data.get_way_id)
            return
        else
            ComposeWGCtrl.Instance:OpenComposeStuffListView(stuff_list, item, BindTool.Bind(self.XQSCStuffSelectCallBack, self))
        end
    else
        item:ClearData()
		item:SetItemIcon(ResPath.GetCommonImages("a3_ty_jia"))

		item:SetButtonComp(true)
		--[[ local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_0")
		item:SetCellBg(bundle, asset) ]]

        self:FlushXQSComposeBtn()
    end
end

function NewComposeView:XQSCStuffSelectCallBack(select_data, select_grid)
    if IsEmptyTable(select_data) or select_grid == nil then
    	return
    end

    select_grid:SetData(select_data)
    self:FlushXQSComposeBtn()
end

-- 刷新合成按钮
function NewComposeView:FlushXQSComposeBtn()
    if self.cur_xqs_product_data == nil then
        return
    end

    local stuff_num = self.cur_xqs_product_data.stuff_num
    local select_num = self:GetXQSAlreadySelectNum()
    XUI.SetButtonEnabled(self.node_list.btn_xqs_hecheng, select_num >= stuff_num)
end

-- 合成
function NewComposeView:OnClickXQSCompose()
    if self.cur_xqs_product_data == nil then
        return
    end

    local stuff_list = {}
    local select_list = self:GetXQSAlreadySelectList()
    for k,v in pairs(select_list) do
        local data = {}
        data.index = k
        data.num = 1
        table.insert(stuff_list, data)
    end

    if #stuff_list < self.cur_xqs_product_data.stuff_num then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Compose.NotEnoughStuff)
        return
    end

    ComposeWGCtrl.Instance:SendComposXQSeReq(self.cur_xqs_product_data.producd_seq, self.cur_xqs_product_data.product_id, stuff_list)
end


----------------------------------------------------------------------------
-- XQSCBigBtnItem
----------------------------------------------------------------------------
XQSCBigBtnItem = XQSCBigBtnItem or BaseClass(BaseRender)
function XQSCBigBtnItem:__init()
    self.view.accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClick, self))
end

function XQSCBigBtnItem:__delete()
end

function XQSCBigBtnItem:SetAccordionElementState(is_on)
    self.view.accordion_element.isOn = is_on
end

function XQSCBigBtnItem:OnFlush()
	if self.data == nil then
        self.view:SetActive(false)
		return
	end

    self.view:SetActive(true)
    self.node_list.text_btn.text.text = self.data.name
    self.node_list.text_btn_hl.text.text = self.data.name
end

function XQSCBigBtnItem:FlushRemind()
    if self.data == nil then
		return
	end

	local flag = ComposeWGData.Instance:GetXQSCBigBtnRemind(self.data.id)
	self.node_list["remind"]:SetActive(flag)
end


----------------------------------------------------------------------------
-- XQSCIToggleChildRender
----------------------------------------------------------------------------
XQSCIToggleChildRender = XQSCIToggleChildRender or BaseClass(BaseRender)
function XQSCIToggleChildRender:__init()

end

function XQSCIToggleChildRender:__delete()
	self.text_name = nil
	self.parent_view = nil
end

function XQSCIToggleChildRender:SetToggleState(is_on)
    self.view.toggle.isOn = is_on
end

function XQSCIToggleChildRender:OnFlush()
	if self.data == nil then
        self.view:SetActive(false)
		return
	end

    self.view:SetActive(true)
	local item_config = ItemWGData.Instance:GetItemConfig(self.data.product_id)
	if item_config then
		self.node_list["text_name"].text.text = item_config.name
		self.node_list["text_name_hl"].text.text = item_config.name
	end
end

function XQSCIToggleChildRender:FlushRemind()
    if self.data == nil then
		return
	end

	local flag = ComposeWGData.Instance:GetXQSCSingleRemind(self.data.product_id)
	self.node_list["remind"]:SetActive(flag)
end
