local ModelFocus = typeof(ModelFocus)

function RoleModel:DeleteExpand()
	self:ModelFocusStatus(true)
	self.model_cocus = nil
	self.project_cache_data = nil
end

function RoleModel:ChangePartDyeColor(dye_color_table)
	local main_part = self:GetDrawPart(SceneObjPart.Main)
	if main_part ~= nil then
		local skin = main_part:GetChangeSkinComponent()

		if IsNil(skin) then
			return
		end

		skin:BatchSetPartDyeColor(cjson.encode({dye_list = dye_color_table}))
	end
end

function RoleModel:ResetPartDyeTableColor(dye_color_table)
	local main_part = self:GetDrawPart(SceneObjPart.Main)
	if main_part ~= nil then
		local skin = main_part:GetChangeSkinComponent()

		if IsNil(skin) then
			return
		end

		skin:BatchReSetPartDyeColor(cjson.encode({dye_list = dye_color_table}))
	end
end

function RoleModel:ResetPartDyeColor()
	local main_part = self:GetDrawPart(SceneObjPart.Main)
	if main_part ~= nil then
		local skin = main_part:GetChangeSkinComponent()

		if IsNil(skin) then
			return
		end

		skin:ResetPartDyeColor()
	end
end

function RoleModel:ResetPartHairDyeColor()
	local main_part = self:GetDrawPart(SceneObjPart.Main)
	if main_part ~= nil then
		local skin = main_part:GetChangeSkinComponent()

		if IsNil(skin) then
			return
		end

		skin:ResetPartHairDyeColor()
	end
end

function RoleModel:GetModelFocusCom()
	local display_obj = self.draw_obj.root.gameObject
	if IsNil(display_obj) then
		return nil
	end

	if self.model_cocus then
		return self.model_cocus
	end

	self.model_cocus = display_obj:GetComponent(ModelFocus)
	if not IsNil(self.model_cocus) then
		self:RemoveModelFocus()
	end

	self.model_cocus = display_obj:AddComponent(ModelFocus)
	return self.model_cocus
end

-- 设置模型聚焦
function RoleModel:SetModelFocus(to_pos, from_pos, to_scale, from_scale, all_move_time, node_trans)
	local com = self:GetModelFocusCom()
	local adjustment_node_trans = node_trans

	if adjustment_node_trans == nil then
		if self.ui_scene_camera ~= nil and (not IsNil(self.ui_scene_camera)) then
			adjustment_node_trans = self.ui_scene_camera
		end
	end

	if adjustment_node_trans == nil then
		adjustment_node_trans = self.display_adjustment_node_trans or self.node_list.DisplayAdjustmentRoot.transform
	end
	
	if (not IsNil(com)) and adjustment_node_trans ~= nil then
		com:SetModelFocusParameter(adjustment_node_trans, to_pos, from_pos, to_scale, from_scale, all_move_time)
		com:ModelFocusStatus(false)
	end
end

-- 设置模型聚焦状态
-- is_not_pinch 是否不能聚焦
function RoleModel:ModelFocusStatus(is_not_pinch)
	if not IsNil(self.model_cocus) then
		self.model_cocus:ModelFocusStatus(is_not_pinch)
	end
end

-- 设置聚焦移动
function RoleModel:ModelFocusMove(is_forward, node_trans)
	local com = self:GetModelFocusCom()
	local adjustment_node_trans = node_trans

	if adjustment_node_trans == nil then
		if self.ui_scene_camera ~= nil and (not IsNil(self.ui_scene_camera)) then
			adjustment_node_trans = self.ui_scene_camera
		end
	end

	if adjustment_node_trans == nil then
		adjustment_node_trans = self.display_adjustment_node_trans or self.node_list.DisplayAdjustmentRoot.transform
	end

	if (not IsNil(com)) and adjustment_node_trans ~= nil then
		com:ModelFocusPlayForward(is_forward)
	end
end

-- 设置移除聚焦代码
function RoleModel:RemoveModelFocus()
	if not IsNil(self.model_cocus) then
		self.model_cocus:RemoveModelFocus()
	end
end

---[[ 
----==========================================================
----==========================================================
----==========================================================
----==========================================================
-- 改变主部位，子部位的模型 （要求设置了主部需要的数据）
function RoleModel:ChangeDrawModel(sub_type, res_id, callback)
	if sub_type == nil or res_id == nil then
		return
    end

	local part = self:GetDrawPart(SceneObjPart.Main)
	if part == nil then return end

	local function func()
		if callback ~= nil then
			callback()
		end
	end

	part:ChangeDrawModel(sub_type, res_id, func)
end

---改变单个头部type数据 - BlendShape
--[[
        HeadCustomizationType.EyeSize
        HeadCustomizationType.EyePosition
        HeadCustomizationType.EyeAngle
        HeadCustomizationType.EyeClose
        HeadCustomizationType.EyebrowAngle
        HeadCustomizationType.MouthSize
        HeadCustomizationType.MouthPosition
        HeadCustomizationType.MouthAngle
        HeadCustomizationType.NoseSize
        HeadCustomizationType.NoseAngle
        HeadCustomizationType.ChinLength
        HeadCustomizationType.CheekSize
]]
function RoleModel:ChangeSkinHeadRendererBlendShape(type, val, part)
	part = part or SceneObjPart.Main
	local draw_part = self:GetDrawPart(part)
	if draw_part == nil then return end
	draw_part:UpdateChangeExtraModelData(type, val)
	draw_part:ChangeSkinHeadRendererBlendShape(type, val)
end

---改变单个头部type数据 - 材质数值
--[[
        HeadCustomizationType.IrisSize_Left
		HeadCustomizationType.IrisSize_Right
        HeadCustomizationType.PupilSize_Left
		HeadCustomizationType.PupilSize_Right
		HeadCustomizationType.FaceDecalTexST
        HeadCustomizationType.FaceDecalFade
		HeadCustomizationType.FaceDecalMirror
]]
function RoleModel:ChangeSkinHeadMatVal(type, val, part)
	part = part or SceneObjPart.Main
	local draw_part = self:GetDrawPart(part)
	if draw_part == nil then return end
	draw_part:UpdateChangeExtraModelData(type, val)
	draw_part:ChangeSkinHeadMatVal(type, val)
end

---改变单个头部type数据 - 材质贴图
--[[
        HeadCustomizationType.PupilType_Left
		HeadCustomizationType.PupilType_Right
        HeadCustomizationType.FaceDecalTex
]]
function RoleModel:ChangeSkinHeadMatTex(type, text_type, part, sex)
	part = part or SceneObjPart.Main
	local draw_part = self:GetDrawPart(part)
	if draw_part == nil then return end
	local tex_bundle = nil
	local tex_asset = nil
	if type == HeadCustomizationType.PupilType_Left then
		tex_bundle, tex_asset = ResPath.GetPupilTypeImg(text_type)
	elseif type == HeadCustomizationType.PupilType_Right then
		tex_bundle, tex_asset = ResPath.GetPupilTypeImg(text_type)
	elseif type == HeadCustomizationType.FaceDecalTex then
		tex_bundle, tex_asset = ResPath.GetFaceDecalImg(text_type, sex)
	end

	if tex_bundle and tex_asset then
		draw_part:UpdateChangeExtraModelData(type, text_type)
		draw_part:ChangeSkinHeadMatTex(type, tex_bundle, tex_asset)
	end
end

---改变单个头部type数据 - 材质颜色rgba table
--[[
        HeadCustomizationType.EyeShadowColor
        HeadCustomizationType.PupilColor
        HeadCustomizationType.MouthColor
        HeadCustomizationType.HairColor
]]
function RoleModel:ChangeSkinHeadMatColorByRGBATable(type, rgba_table, part)
	part = part or SceneObjPart.Main
	local draw_part = self:GetDrawPart(part)
	if draw_part == nil then return end

	draw_part:UpdateChangeExtraModelData(type, rgba_table)
	draw_part:ChangeSkinHeadMatColorByRGBATable(type, rgba_table)
end

---改变单个头部type数据 - 材质颜色rgba
function RoleModel:ChangeSkinHeadMatColorByRGBA(type, r, g, b, a, part)
	part = part or SceneObjPart.Main
	local draw_part = self:GetDrawPart(part)
	if draw_part == nil then return end

	draw_part:ChangeSkinHeadMatColorByRGBA(type, r, g, b, a)
end

---改变单个头部type数据 - 材质颜色convert hex
function RoleModel:ChangeSkinHeadMatColorByHex(type, hex, part)
	part = part or SceneObjPart.Main
	local draw_part = self:GetDrawPart(part)
	if draw_part == nil then return end

	draw_part:ChangeSkinHeadMatColorByHex(type, hex)
end

-- 改变材质依据方案类型
function RoleModel:ChangeRoleMaterialsByProjectId(skin_type, project_id, callback, role_res_id, extra_role_model_data)
	local draw_part = self:GetDrawPart(SceneObjPart.Main)
	if draw_part == nil then return end
    local is_find_fashion, body_res, face_res, hair_res = self:GetModelPartRes(role_res_id, extra_role_model_data)

	if not is_find_fashion then
		return
	end

	if not self.project_cache_data then
		self.project_cache_data = {}
	end

	if skin_type == ROLE_SKIN_TYPE.BODY then
		if project_id ~= 0 then
			draw_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.BODY, body_res, project_id, callback)
			self.project_cache_data.body_id = project_id
		else
			if self.project_cache_data.body_id and self.project_cache_data.body_id ~= 0 then		-- 有发生改变才会重置
				self.project_cache_data.body_id = 0
				draw_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.BODY)
			end
			
			if callback then
				callback()
			end
		end
    elseif skin_type == ROLE_SKIN_TYPE.FACE then
		if project_id ~= 0 then
			draw_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.FACE, face_res, project_id, callback)
			self.project_cache_data.face_id = project_id
		else
			if self.project_cache_data.face_id and self.project_cache_data.face_id ~= 0 then		-- 有发生改变才会重置
				self.project_cache_data.face_id = 0
				draw_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.FACE)
			end

			if callback then
				callback()
			end
		end
    elseif skin_type == ROLE_SKIN_TYPE.HAIR then
		if project_id ~= 0 then
			draw_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.HAIR, hair_res, project_id, callback)
			self.project_cache_data.hair_id = project_id
		else
			if self.project_cache_data.hair_id and self.project_cache_data.hair_id ~= 0 then		-- 有发生改变才会重置
				self.project_cache_data.hair_id = 0
				draw_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.HAIR)
			end
	
			if callback then
				callback()
			end
		end
    end
end

-- 获取角色部位资源
function RoleModel:GetModelPartRes(var_role_res_id, extra_role_model_data)
	local sex = extra_role_model_data and extra_role_model_data.sex
	local prof = extra_role_model_data and extra_role_model_data.prof
	local d_body_res, d_face_res, d_hair_res
	local mr_def_body_res, mr_def_face_res, mr_def_hair_res = RoleWGData.Instance:GetMainRoleDefSkinPartRes()
	d_body_res = d_body_res or mr_def_body_res
	d_face_res = d_face_res or mr_def_face_res
	d_hair_res = d_hair_res or mr_def_hair_res

	local final_role_res_id = self.role_res_id 
	if not final_role_res_id then
		final_role_res_id = var_role_res_id or 0
	end

	if final_role_res_id == 0 then
		return false, d_body_res, d_face_res, d_hair_res
	end

	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	sex = sex or main_vo.sex
	prof = prof or main_vo.prof
	prof = prof and prof % 10

	local role_res_id_number = tonumber(self.role_res_id) or 0
	local resouce = role_res_id_number % 1000
	if resouce ~= 0 then
		local body_res, face_res, hair_res = RoleWGData.GetShowRoleSkinPartRes(sex, prof, resouce, d_body_res, d_face_res, d_hair_res)
		return true, body_res, face_res, hair_res
	else
		print_error("当前换色dye是0", debug.traceback())
		return false, d_body_res, d_face_res, d_hair_res
	end
end
----==========================================================
----==========================================================
----==========================================================
----==========================================================
---]]

function RoleModel:UpdateChangeExtraModelData(type, val, part)
	part = part or SceneObjPart.Main
	local draw_part = self:GetDrawPart(part)
	if draw_part == nil then return end

	draw_part:UpdateChangeExtraModelData(type, val)
end

