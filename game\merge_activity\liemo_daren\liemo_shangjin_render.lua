--猎魔赏金格子
LieMoShangJinRender = LieMoShangJinRender or BaseClass(BaseRender)

function LieMoShangJinRender:LoadCallBack()
    self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
    self.item_list:SetIsDelayFlush(false)
    XUI.AddClickEventListener(self.node_list.btn_fetch, BindTool.Bind(self.OnClickFetch, self))
    XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind(self.OnClickGoTo, self))
end

function LieMoShangJinRender:__delete()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function LieMoShangJinRender:OnFlush()
    if not self.data then
        return
    end

    self.item_list:SetDataList(self.data.item_list)
    --描述
    local info = LieMoDaRenWGData.Instance:GetLieMoInfo()
    local cur_score = info.score
    local score_str = ToColorStr(string.format("%s", self.data.score), cur_score >= self.data.score and COLOR3B.C2 or COLOR3B.C3)
    local cur_score_str = ToColorStr(cur_score, COLOR3B.C3)
    self.node_list.desc.text.text = string.format(Language.LieMoDaRen.ShangJinItemDesc, score_str, cur_score_str)

    --按钮
    local has_fetch = LieMoDaRenWGData.Instance:GetIsFetchReward(self.data.seq)
    local can_fetch = LieMoDaRenWGData.Instance:CanFetch(self.data.seq)
    self.node_list.btn_yiling:SetActive(has_fetch)
    self.node_list.btn_fetch:SetActive(not has_fetch and can_fetch)
    self.node_list.btn_fetch_red_point:SetActive(not has_fetch and can_fetch)
    self.node_list.btn_goto:SetActive(not has_fetch and not can_fetch)
end

function LieMoShangJinRender:OnClickFetch()
    LieMoDaRenWGCtrl.Instance:SendFetchReward(self.data.seq)
end

function LieMoShangJinRender:OnClickGoTo()
    ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_world)
end


-- --猎魔赏金物品格子
-- LieMoItemCell = LieMoItemCell or BaseClass(BaseRender)
-- function LieMoItemCell:LoadCallBack()
--     self.item = ItemCell.New(self.node_list.item_pos)
-- end

-- function LieMoItemCell:__delete()
--     if self.item then
--         self.item:DeleteMe()
--         self.item = nil
--     end
-- end

-- function LieMoItemCell:SetData(data)
--     self.data = data
--     self.item:SetData(self.data)
-- end

-- function LieMoItemCell:OnFlush()
    
-- end