GLOBAL_CONFIG = {
	client_time = 0,					-- 更新数据时的客户端时间

	-- 参数列表
	param_list = {
		private_policy_url =  "",			-- 隐私政策url
		user_protocol_url = "",				-- 用户协议url
		-- 静态资源列表
		resources = {
			circle = {						-- 游戏圈
				download = "",				-- 下载链接
				hyperlink = "",				-- 超链接
			},

			share = {						-- 分享
				download = "",
				hyperlink = "",
			},

			pay = {							-- 充值
				download = "",
				hyperlink = "",
			},
		},

        client_ip = "string",               -- 客户端外网IP
        cdn_url = "string",              	-- 资源更新地址
        cdn_url2 = "string",             	-- 资源更新地址(备用)
        chat_report_url = "string",         -- 聊天上报地址
        chongzhi_data = "string",           --  PHP定义充值档位
        get_user_money_url = "string",      -- 用户充值数据
        app_filing_url = "string",          -- app备案号url
        gm_report_url = "string",           -- 联系GM上报地址
        download_other_game_url = "string", -- 下载有礼URL
        vip_service_image = "string",       -- 专属客服图片链接
        vip_service_url = "string",         -- 专属客服网址跳转
        bind_phone_url = "string",          -- php请求绑定手机验证(废弃)
        get_server_name_url = "string",     -- 获取服务器名链接
        ip_check_url = "string",            -- 根据ip请求城市信息

        emulator_limit_login = "0/1",           -- 模拟器限制登录（非白名单玩家不可登录）
        shield_chat_voice = "0/1",              -- 屏蔽聊天语音(1屏蔽)
        guild_chat_need_vip_limit = "0/1",      -- 宗门聊天是否需要vip等级限制(与 all_chat_vip_level 配套)（1开启）
        guild_chat_need_level_limit = "0/1",    -- 宗门聊天是否需要人物等级限制(与 all_chat_role_level 配套)（1开启）
        is_open_download_other_game = "0/1",    -- 是否开启下载有礼（1开启）
        shield_fake_guild = "0/1",              -- 屏蔽假仙盟(1屏蔽)
        shield_see_guild_member = "0/1",        -- 屏蔽查看仙盟成员(1屏蔽)
        login_fanli_switch = "0/1",             -- 登录返利开关（1开启）
        is_hide_area_server_id_name = "0/1",    -- 是否隐藏区服大区内的服务器名称上的区服id（只影响大区类型的服务器，1开启隐藏）
        is_show_reconnet_btn = "0/1",           -- 是否显示重连游戏按钮(0隐藏)
        market_can_change_price = "0/1",        -- 市场物品出售价格修改限制(1限制)
        limit_custom_avatar = "0/1",            -- 是否限制自定义头像(1开启限制)
        shield_high_charge = "0/1",             -- 屏蔽高额充值（1屏蔽）（充值金额 > 200）
        is_open_vip_service = "0/1",            -- 是否开启专属客服（1开启）
		is_audit_android = "0/1",           	-- 非审核模式，需要变更充值档位

        is_enforce_cfg = "number",              -- 是否强制使用安卓配置（0没有返回，1安卓，2苹果）
        pay_money_type = "number",              -- 支付货币类型
        area_type = "number",                   -- 地区类型
        recharge_bili = "number",               -- 充值比例
        recommend_type = "number",              -- 服务器列表推荐类型（当值为1，推荐标签，推荐服列表flag为1的）
        all_chat_vip_level = "number",          -- 聊天系统vip等级限制
        all_chat_role_level = "number",         -- 聊天系统人物等级限制
        market_can_sell_vip_level = "number",   -- 市场上架物品vip等级限制
        vip_market_recharge_limit_open = "number",  -- 累充金额限制聊天、市场购买上架（value = 充值金额）
        vip_service_number = "number",          -- 专属客服账号

		res_encrypt_key = "string",         	-- AB资源加密key
        res_base64_value = "string",        	-- AB资源路径base64混淆值
        res_encrypt_type = "number",            -- AB资源解密方式，1:不需传参key，2:需传参key

        username_white_list = {},          -- GM包不检测白名单
		-- 版权信息
		copyright = {
			publisher = "",				-- 出版单位
			operating_unit = "",		-- 运营单位
			approval_number = "",		-- 审批文号
			copyright_holder = "",		-- 著作权人
			copyright_number = "",		-- 审批文号
			registration_number = "",	-- 登记号
			app_record_number = "",		-- APP备案号
		},

		switch_list = {
			update_package = false,				-- 开关 - 安装包更新
			update_assets = false,				-- 开关 - 资源更新
			audit_version = false,				-- 开关 - IOS审核版本
			gamewp = false,						-- 开关 - Web支付
			show_abide_protocol = false,		-- 开关 - 显示用户阅读遵守 《游戏服务协议》/《隐私保护》
			open_chongzhi = true,				-- 开关 - 开启充值
			open_gvoice = false,				-- 开关 - 是否开启收费语音(带文字翻译)
        },
	},

	-- 这部分都需要额外对返回的code处理
	api_urls = {
		client = {
			login = "",					-- 登录验证URL
			upload = "",				-- 上传地址（头像、语音）
			user_info = "",				-- 用户信息接口
			notice = "",				-- 公告列表接口
			use_card = "",				-- 礼包码使用接口
			refresh = "",				-- token刷新接口
		},

		pay = {
			order = "",					-- 下单接口
		},
		report = {
			buried_event = "",			-- 埋点事件日志上报接口
		},
	},

	chat_reward_word_list = {			-- 聊天发送口令领奖励列表
		{
			word = "发财",				-- 聊天口令
			seq = 0,					-- 奖励索引
		},

		{
			word = "大吉",
			seq = 1,
		},
	},

	server_info = {						-- 服务器列表
		last_server = 0,				-- 上一次登录的服务器ID
		server_time = 0,				-- 服务器当前时间 (用于对时)
		server_offset = 0,				-- 服偏移值
		server_list = {
			{
				id = 1,					-- 服务器ID
				name = "",				-- 服务器名字
				ip = "",				-- 登录服务器IP
				port = 0,				-- 登录服务器端口
				open_time = 0,			-- 服务器开服时间
				ahead_time = 0,			-- 提前开放登录时间(秒)
				pause_time = 0,			-- 维护结束时间
				flag = 0,				-- 服务器标记 (1: 火爆 2: 新服 3: 即将开服 4: 维护)
				is_show = 1,			-- 是否显示
				register_status = 1,	-- 开启注册（0关闭）
			}
		},

		server_area = {					-- 分大区列表
			{
				name = "大区名字",		 -- 大区名字
				start_server_id = 1,	-- 大区服务器区间
				end_server_id = 10,
			},
		},
	},

	-- 版本信息
	version_info = {
		php_package_info = {			-- 安装包信息
			version = 0,				-- 安装包版本
			name = "",					-- 安装包文件名
			desc = "",					-- 安装包描述
			web_url = "",				-- 安装包下载页面（优先判断，若web_url有链接，则不执行读取url更新整包操作）
			url = "",					-- 安装包下载地址
			size = 0,					-- 安装包大小
			md5 = "", 					-- 安装包MD5
			msg = "", 					-- msg
			clearCache = false,			-- 清理缓存
		},

		php_assets_info = {				-- 游戏资源信息
			version = "",				-- 资源版本号
		},

		update_data = ""				-- 更新初始化代码
	},

	local_package_info = {				-- 本地 安装包信息 (从安装文件获取, 无法修改)
		version = 0,					-- 安装包版本
		config = {
			agent_id = "",				-- 平台(渠道)ID
			init_urls = {},				-- PHP配置获取地址
		}
	},

	local_assets_info = {				-- 本地 游戏资源信息
		version = 0,					-- 资源版本号
		lua_version = 0,
	},
}

local init_urls = nil
local init_urls_text = ChannelAgent.GetInitUrl()
DEFAULT_AGENT_ID = "pev"
CHANNEL_AGENT_ID = ChannelAgent.GetChannelID()
local agent_id = CHANNEL_AGENT_ID

if init_urls_text == nil or init_urls_text == "" then
	init_urls = {"http://192.168.0.133/a3/cn/v1/client/init.php"} -- ?is_debug=1
else
	init_urls = string.split(init_urls_text, ',')
end

-- if UnityEngine.Debug.isDebugBuild then
-- 	init_urls = {"http://192.168.0.133/a3/unity/query.php"}
-- end

DefaultGlobalUrl = "http://gateway.g1.winunet.com"
GlobalUrl = "http://cngt.a1.winunet.com"
-- 请求签名时，内容加密(注意关键字 按照键名进行升序排序)
-- 注意修改这个需要同时修改GameRoot的值
GlobalUrlSignKey = "fba0de406ea352eb8ff760c0587611d3"
-- 白包请求支付回调时的key
WhitePkgPayKey = "9234a93e9d565aee21322605ca9a1280"
-- 初始化时内容返回解密
GlobalPHPInfoDecodeKey = "dd0091cb686e627ece36dfb513cfb2c9"

if IS_LOCLA_WINDOWS_DEBUG_EXE or RuntimeGUIMgr.Instance:IsAndroidGM() == true then
	local win_query_url = UnityEngine.PlayerPrefs.GetString("a3_fanli_local_windows_debug_exe_query_url")
	local win_agent_id = UnityEngine.PlayerPrefs.GetString("a3_fanli_local_windows_debug_exe_agent_id")
	if "" ~= win_query_url and "" ~= win_agent_id then
		init_urls = {win_query_url}
		agent_id = win_agent_id
	end
end

if init_urls_text ~= nil and init_urls_text ~= "" then
	local agent_t = string.split(init_urls_text, ',')
	if agent_t ~= nil and #agent_t > 0 then
		for k,v in pairs(agent_t) do
			if v ~= nil and v ~= "" then
				init_urls[#init_urls + 1] = string.gsub(v, "//[^/]+/?", "//gateway.g1.winunet.com/")
			end
		end
	end
end

GLOBAL_CONFIG.local_package_info.version = UnityEngine.Application.version

-- 【 外网测试 】
--[[

--============================
	-- 1、不需要越过sdk的登录（白包）（如：dev），使用后台的【渠道账号】，不使用伪登录
	-- 2、需要越过sdk的登录（如：a01），使用后台的【玩家账号】，使用伪登录
	-- 注意：若要使用伪登录，需要联系运营开启伪登录功能（别问为什么这么麻烦，洁晓设计如此）
--============================

-- [测试环境]
-- USE_INNER_LOGIN = true			-- 是否使用伪登录
-- USE_INNER_LOGIN_URL = "https://testgt-a3.winunet.com/v1/client/loginJy"			-- 伪装登录url
-- init_urls = {"https://testgt-a3.winunet.com/v1/client/init"}
-- CHANNEL_AGENT_ID = "dev"

-- [正常生产环境]
-- USE_INNER_LOGIN = true			-- 是否使用伪登录
USE_INNER_LOGIN_URL = "https://cngt-a3.jingyougate.com/v1/client/loginJy"			-- 伪装登录url
init_urls = {"https://cngt-a3.jingyougate.com/v1/client/init"}
CHANNEL_AGENT_ID = "dev"

agent_id = CHANNEL_AGENT_ID
GLOBAL_CONFIG.local_package_info.version = "9.9.9"
--]]

GLOBAL_CONFIG.local_package_info.config.init_urls = init_urls
GLOBAL_CONFIG.local_package_info.config.agent_id = agent_id
GLOBAL_CONFIG.local_assets_info.version = ResMgr:GetHashCode()
GLOBAL_CONFIG.local_assets_info.lua_version = ResMgr:GetLuaHashCode()