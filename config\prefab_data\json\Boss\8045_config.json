{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false, "hurtEffectFreeDelay": 0.0, "QualityCtrlList": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "8045_acttack", "effectAsset": {"BundleName": "effects/prefab/model/boss/8045/8045_acttack_prefab", "AssetName": "8045_acttack", "AssetGUID": "9ad1799afb7bd084692f7fcb946edad8", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "8045_skill", "effectAsset": {"BundleName": "effects/prefab/model/boss/8045/8045_skill_prefab", "AssetName": "8045_skill", "AssetGUID": "6438635576ec81646827fa8245828b80", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": true}], "halts": [], "sounds": [], "cameraShakes": [], "cameraFOVs": [], "sceneFades": [], "footsteps": []}, "actorBlinker": {"blinkFadeIn": 0.0, "blinkFadeHold": 0.0, "blinkFadeOut": 0.0}, "TimeLineList": []}