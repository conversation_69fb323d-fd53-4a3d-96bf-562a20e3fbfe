local ReconnectHandle = {}

function ReconnectHandle.CheckCanReConnect(call_back)
    local url = InitWGCtrl.Instance:GetQueryUrl()
	print_log("SendRequest", url)
	HttpClient:Request(url, function(url, is_succ, data)
		ReconnectHandle:OnRequestCallback(url, is_succ, data, call_back)
	end)
end

function ReconnectHandle:OnRequestCallback(url, is_succ, data, call_back)
    if not is_succ then
        if call_back then
            call_back(false)
        end

		return
    end   
    
    for i = 1, 1 do
        if data == "login block" then
            if call_back then
                call_back(false)
            end

			return
		end

        local init_info = cjson.decode(data)
		local php_data = init_info ~= nil and init_info.data or nil
		if type(init_info) == "table" and type(php_data) ~= "table" and Base64Decode ~= nil then
			local b64 = Base64Decode(init_info.data, GlobalPHPInfoDecodeKey)
			php_data = cjson.decode(b64)
		end

        if init_info == nil or php_data == nil then
            if call_back then
                call_back(false)
            end

			return
		end

        if nil == php_data.param_list then break end
		GLOBAL_CONFIG.param_list = php_data.param_list
		GLOBAL_CONFIG.param_list.shield_chat_voice = 1 -- 强制屏蔽语音聊天
		GLOBAL_CONFIG.api_urls = php_data.api_urls
		GLOBAL_CONFIG.chat_reward_word_list = php_data.chat_reward_word_list

		if nil ~= GLOBAL_CONFIG.param_list.recharge_bili then
			RECHARGE_BILI = GLOBAL_CONFIG.param_list.recharge_bili
		end

		GlobalUrl = php_data.param_list.global_url or DefaultGlobalUrl

		if GLOBAL_CONFIG.param_list.res_encrypt_type == 2 then
			EncryptMgr.SetEncryptKey(GLOBAL_CONFIG.param_list.res_encrypt_key)
			EncryptMgr.SetBase64EncryptKey(GLOBAL_CONFIG.param_list.res_base64_value)
			EncryptMgr.SaveCacheEncryptKeyFile()
		end

		if GLOBAL_CONFIG.param_list.switch_list.open_gvoice then
			IS_FEES_VOICE = true
		end
		
		if nil == php_data.server_info then break end
		GLOBAL_CONFIG.server_info = php_data.server_info
		GLOBAL_CONFIG.client_time = self.client_time

		if nil == php_data.version_info then break end
		local version_info = php_data.version_info
		GLOBAL_CONFIG.version_info = {}

		if nil == version_info.package_info then break end
		GLOBAL_CONFIG.version_info.php_package_info = version_info.package_info

		if nil ~= version_info.assets_info then
			GLOBAL_CONFIG.version_info.php_assets_info = version_info.assets_info
			ResMgr:SetAssetVersion(version_info.assets_info.version)
			ResMgr:SetAssetLuaVersion(version_info.assets_info.version)
		end

        if call_back then
            call_back(true)
        end
    end
end

return ReconnectHandle