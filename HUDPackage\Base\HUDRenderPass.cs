using HUDProgramme;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class HUDRenderPass : MonoPass
{
    public HUDRenderPass()
    {
    }

    public override void MonoDisable()
    {

    }

    public override void MonoEnable()
    {
    }

    protected override void PassExecute(ScriptableRenderContext context, ref RenderingData renderingData)
    {
        CommandBuffer HUDTitleCmdBuffer = HUDTitleInfo.HUDTitleRender.Instance.GetCmdBuffer();
        CommandBuffer HUDNumberCmdBuffer = HUDNumberRender.Instance.GetCmdBuffer();

        if (HUDTitleCmdBuffer != null && HUDTitleCmdBuffer.sizeInBytes > 0)
        {
            context.ExecuteCommandBuffer(HUDTitleCmdBuffer);
        }
        if (HUDNumberCmdBuffer != null && HUDNumberCmdBuffer.sizeInBytes > 0)
        {
            context.ExecuteCommandBuffer(HUDNumberCmdBuffer);
        }
    }
}