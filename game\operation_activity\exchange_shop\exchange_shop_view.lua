local this = OperationActivityView

function this:LoadIndexCallBackExchange()
	self.is_load_exchange = true
	self:InitListExchange()

	self.data = ExchangeShopWGData.Instance:GetData()

	local word_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_txt_6")
	local bundle1, asset1 = ResPath.GetRawImagesPNG(word_name)
	self.node_list["exchange_word"].raw_image:LoadSprite(bundle1, asset1, function()
        self.node_list["exchange_word"].raw_image:SetNativeSize()
    end)

	self:NewExchangeShopModel()

	-- self.flush_exchange = BindTool.Bind(self.FlushExchange, self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_callback) --遗留bug  从取消注册来看应该是需要绑定self.flush_exchange
end

function this:ReleaseExchangeCallBack()
	self.is_load_exchange = nil

	if self.exchange_shop_act_render then
		self.exchange_shop_act_render:DeleteMe()
		self.exchange_shop_act_render = nil
	end

	self:DeleteListExchange()

	-- ItemWGData.Instance:UnNotifyDataChangeCallBack(self.flush_exchange)
	-- self.flush_exchange = nil
end

function this:FlushExchange()
	if not self.is_load_exchange then
		return
	end

	self.exchange_list.scroller:RefreshActiveCellViews()
end

function this:OnSelectExchangeShop()
	local data = ExchangeShopWGData.Instance:GetData()
	self:SetRuleInfo(data.rule_desc, data.btn_rule_title)
	self:SetOutsideRuleTips(data.out_side_rule or "")
end

function this:DeleteListExchange()
	self.exchange_list = nil
	if self.exchange_cell_list then
		for k,v in pairs(self.exchange_cell_list) do
			v:DeleteMe()
		end
		self.exchange_cell_list = nil
	end
end

function this:InitListExchange()
	self.exchange_cell_list = {}
	self.exchange_list = self.node_list.exchange_list
	local list_delegate = self.exchange_list.list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCellExchange, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshCellExchange, self)
end
 
function this:GetNumberOfCellExchange()
	return math.ceil(ExchangeShopWGData.Instance:GetShopListNum() / 3)
end

function this:RefreshCellExchange(cell, cell_index)
	cell_index = cell_index + 1
	local item_cell = self.exchange_cell_list[cell]
	local data_list = self:GetDataListExchange()
	if not item_cell then
		item_cell = CommonItemGroup.New(cell.gameObject)
		item_cell:Init(ExchangeShopItem, "exchange_item", 3)
		self.exchange_cell_list[cell] = item_cell
	end
	item_cell:SetIndex(cell_index)
	if data_list then
		item_cell:SetData(data_list)
		item_cell:SetClickCallBack(function (data)
			self:OnClickItemExchange(data)
		end)
	end
end

function this:GetDataListExchange()
	return ExchangeShopWGData.Instance:GetExchangeShopList()
end

function this:OnClickItemExchange(data)
	-- self:FlushExchange()
end

function this:NewExchangeShopModel()
	local data = {}
	if self.data.show_model then
		data.render_type = 0
		data.bundle_name = self.data.show_model_bundle
		data.asset_name = self.data.model_id
	elseif self.data.show_item_id == "" then
		data.bundle_name = self.data.show_picture_bundle
		data.asset_name = self.data.show_picture
		data.render_type = 2
	else
		data.item_id = self.data.show_item_id
		data.render_type = 0
	end
	data.model_adjust_root_local_position = self.data.position
	data.model_adjust_root_local_rotation = self.data.rotation
	data.model_adjust_root_local_scale = self.data.scale
	data.model_rt_type = ModelRTSCaleType.M

	self.exchange_shop_act_render = OperationActRender.New(self.node_list.exchange_model)
	self.exchange_shop_act_render:SetData(data)
end