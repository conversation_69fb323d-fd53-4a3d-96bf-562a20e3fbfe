GuildPopInfoView = GuildPopInfoView or BaseClass(SafeBaseView)

function GuildPopInfoView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
						{vector2 = Vector2(0, -27), sizeDelta = Vector2(814, 522)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_info_view")
end

function GuildPopInfoView:ReleaseCallBack()
    if self.member_list1 ~= nil then
    	self.member_list1:DeleteMe()
		self.member_list1 = nil
	end

	if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end

	self.member_datasource = nil
	self.guild_data = nil
	self.IsGetInfo = nil
end

function GuildPopInfoView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Guild.GuildInfoName
	--首次进入会偶尔出现界面未加载完，洗衣过来赋值的问题
	if not self.member_list1 then
		self.member_list1 = AsyncListView.New(GuildMemberItemRender, self.node_list["info_member_list"])
		self.member_list1:SetSelectCallBack(BindTool.Bind1(self.OnClickLookInfo, self))
		self:FirstDeal()
	end

	if not self.role_avatar then
		self.role_avatar = RoleHeadCell.New(false)
	end
end

function GuildPopInfoView:OnClickLookInfo(item, cell_index, is_default, is_click)
	if item == nil or not is_click then
		return
	end

	local data = item:GetData()
    if data == nil then
		return
	end

	if data.uid == 0 then
		return
	end

	if data.uid == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then --
		self.role_avatar:SetRobotInfo(data.role_name, data.prof, data.sex)
		self.role_avatar:OpenMenu()
		return
	end

	if data.uid == RoleWGData.Instance:InCrossGetOriginUid() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.NotOperateSelf)
		return
	end

	local role_info = {
		role_id = data.uid,
		role_name = data.role_name,
		prof = data.prof,
		sex = data.sex,
		is_online = true,
	}
	self.role_avatar:SetRoleInfo(role_info)
	self.role_avatar:OpenMenu()
end

function GuildPopInfoView:ShowIndexCallBack()
	if nil == self.guild_data then
		return
	end

	self.node_list.lbl_guild_name.text.text = self.guild_data.guild_name
    self.node_list.lbl_guild_member_num.text.text = self.guild_data.cur_member_num .. "/" .. self.guild_data.max_member_num
    self.node_list.lbl_guild_lv.text.text = self.guild_data.guild_level
    -- self.node_list.lbl_guild_zijin.text.text = self.guild_data.guild_money--self.guild_data.guild_level
    self.node_list.lbl_guild_leader.text.text = self.guild_data.mengzhu_name
    local cap_str = CommonDataManager.ConverExpByThousand(self.guild_data.guild_all_capability)
    self.node_list.lbl_guild_shili.text.text = cap_str
end

function GuildPopInfoView:GetGuildList(fake_guild_seq)
	if not self.IsGetInfo then
		return
	end

	self.IsGetInfo = false
	local member_num = 0
	local member_list = {}
	if fake_guild_seq then
		local m_list = GuildWGData.Instance:GetFakeGuildMemberShowList(fake_guild_seq)
		member_num = #m_list
		member_list = m_list
	else
		local m_list = GuildDataConst.GUILD_MEMBER_LIST
		member_num = m_list.count
		member_list = m_list.list
	end
	
	local guild_index = 1
	self.member_datasource = {}
	for i = 1, member_num do
		local item = member_list[i]
		if item.post == 4 then --团长
			guild_index = 6
		elseif item.post ==3 then--副帮主
			guild_index = 5
		elseif item.post ==2 then--长老
			guild_index = 4
		elseif item.post ==6 then--护法
			guild_index = 3
		elseif item.post ==5 then--精英
			guild_index = 2
		elseif item.post ==1 then--成员
			guild_index = 1
		end

		local datasource = {
			uid = item.uid,
			role_name = item.role_name,
			level = item.level,
			sex = item.sex,
			prof = item.prof,
			post = item.post,
			vip_type = item.vip_type,
			vip_level = item.vip_level,
			is_online = item.is_online,
			join_time = item.join_time,
			last_login_time = item.last_login_time,
			gongxian = item.gongxian,
			total_gongxian = item.total_gongxian,
			capability = item.capability,
			guild_zw = guild_index}
		table.insert(self.member_datasource, datasource)
	end

	if nil ~= self.member_list1 and not IsEmptyTable(self.member_datasource) then
		table.sort( self.member_datasource, SortTools.KeyUpperSorters("guild_zw","is_online","capability"))
		self.member_list1:SetDataList(self.member_datasource)
	end
end

function GuildPopInfoView:FirstDeal()
	if nil ~= self.member_list1 and self.member_datasource and not IsEmptyTable(self.member_datasource) then
		table.sort( self.member_datasource, SortTools.KeyUpperSorters("guild_zw","is_online","capability"))
		self.member_list1:SetDataList(self.member_datasource)
	end
end

-- 重写打开方法
function GuildPopInfoView:SetData(guild_data)
	if nil == guild_data then
		return
    end

    self.IsGetInfo = true
	self.guild_data = guild_data
	if self.guild_data.guild_id > 0 then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, self.guild_data.guild_id)--请求成员列表信息
	elseif guild_data.guild_seq then
		self:GetGuildList(guild_data.guild_seq)
	end
end

function GuildPopInfoView:CloseCallBack()
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)--请求成员列表信息
end

function GuildPopInfoView:GetMemberDatasource()
	return self.member_datasource
end

--------------------GuildMemberItemRender---------------------------------
GuildMemberItemRender = GuildMemberItemRender or BaseClass(BaseRender)

function GuildMemberItemRender:OnFlush()
	if nil == self.data then
		return
	end

	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	-- local asset_name = self.index % 2 == 0 and "a2_zudui_lbdi" or "a2_ty_xxd_5"
	local post_list = GuildDataConst.GUILD_POST_AUTHORITY_LIST
	local is_online = self.data.is_online == ONLINE_TYPE.ONLINE_TYPE_ONLINE

	-- self.node_list["img9_bg"].image:LoadSprite(ResPath.GetCommonImages(asset_name))
	self.node_list.lbl_level2:SetActive(not is_vis)
	self.node_list.img_lv_icon:SetActive(is_vis)
	self.node_list.lbl_name.text.text = self.data.role_name --显示名字
	self.node_list.lbl_state.text.text = is_online and ToColorStr(Language.Common.OnLine, COLOR3B.D_GREEN) or ToColorStr(Language.Common.OutLine, COLOR3B.D_RED) --是否在线
	self.node_list.lbl_level2.text.text = role_level .. Language.Rank.Ji --普通等级
	self.node_list.lbl_level.text.text = role_level .. Language.Rank.Ji --巅峰等级
	self.node_list.lbl_post.text.text = (post_list[self.data.post] or {}).post or "" --宗门职位
	self.node_list.lbl_cap.text.text = CommonDataManager.ConverExpByThousand(self.data.capability) --战斗力
end