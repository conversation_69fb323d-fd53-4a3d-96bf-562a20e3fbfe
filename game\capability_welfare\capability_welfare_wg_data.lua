CapabilityWelfareWGData = CapabilityWelfareWGData or BaseClass()

function CapabilityWelfareWGData:__init()
	if CapabilityWelfareWGData.Instance ~= nil then
		error("[CapabilityWelfareWGData] attempt to create singleton twice!")
		return
	end
	CapabilityWelfareWGData.Instance = self
	self:InitConfig()

	self.fetch_reward_flag = {}
	-- 红点注册
	RemindManager.Instance:Register(RemindName.CapabilityWelfare, BindTool.Bind(self.GetRemind,self))
end

function CapabilityWelfareWGData:__delete()
	CapabilityWelfareWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.CapabilityWelfare)
end

-- 初始化配置
function CapabilityWelfareWGData:InitConfig()
	local cfg = ConfigManager.Instance:GetAutoConfig("capability_weal_auto")

	if cfg then
		self.capability_weal_cfg = cfg
		self.task_cfg = ListToMap(cfg.task, "seq")
		self.open_day_task_cfg = ListToMapList(cfg.task, "open_day")
		self.daily_task_cfg = ListToMap(cfg.task, "open_day", "seq")
        self.other_cfg = cfg.other[1]
		self.model_cfg = cfg.model_show[1]
	end
end

function CapabilityWelfareWGData:GetOtherCfg()
	return self.other_cfg
end

function CapabilityWelfareWGData:GetOpenLevel()
	return self.other_cfg.open_level
end

-- 获取所有任务信息
function CapabilityWelfareWGData:GetWelfareTaskAllInfo()
	return self.all_task_info or {}
end

-------------------服务器相关数据-------------------
function CapabilityWelfareWGData:SetCapabilityWelfareAllInfo(protocol)
	self.end_timestamp = protocol.end_timestamp
	self.day = protocol.day
    self.is_buy_tequan = protocol.is_buy_tequan
	self.total_capability = protocol.total_capability
	self.fetch_reward_flag = protocol.fetch_reward_flag
	self.final_reward_fetch_flag = protocol.final_reward_fetch_flag

	self.all_task_info = {}
	local is_act_tequan = protocol.is_buy_tequan == 1
	local tequan_day = is_act_tequan and protocol.day + 1 or protocol.day
    --self.real_open_day = tequan_day > #self.open_day_task_cfg and #self.open_day_task_cfg or tequan_day -- 已开启天数
	self.real_open_day = protocol.day
	--local flag = false
	for open_day, cfg_task_list in ipairs(self.open_day_task_cfg) do
		local day_info = {}
		day_info.open_day = open_day
		day_info.is_open = self.real_open_day >= open_day
		-- day_info.is_show_open_tip = false

		-- if not flag and not is_act_tequan and not day_info.is_open then
		-- 	day_info.is_show_open_tip = true
		-- 	flag = true
		-- end
		-- 任务列表
		local task_list = {}
		local final_task
		for i, task_cfg in ipairs(cfg_task_list) do
			local task_info = {}
			task_info.cfg = task_cfg
			task_info.seq = task_cfg.seq
			local fetch_flag = protocol.fetch_reward_flag[task_cfg.seq]
			task_info.fetch_flag = fetch_flag
			task_info.can_fetch = fetch_flag == 0 and protocol.total_capability >= task_cfg.need_capability
			if i == #cfg_task_list then
				final_task = task_info
				final_task.is_final_task = true
			else
				table.insert(task_list, task_info)
			end
		end
		table.sort(task_list, SortTools.KeyLowerSorter("fetch_flag", "seq"))
		day_info.task_list = task_list
		day_info.final_task = final_task

		table.insert(self.all_task_info, day_info)
	end
	table.sort(self.all_task_info, SortTools.KeyLowerSorter("open_day"))
end

-- 获取所有任务信息
function CapabilityWelfareWGData:GetAllTaskInfo()
	return self.all_task_info
end

-- 获取任务是否已领取奖励
function CapabilityWelfareWGData:GetTaskIsFetchBySeq(seq)
	return self.fetch_reward_flag and self.fetch_reward_flag[seq] == 1 or false
end

-- 获取已领取的最大索引
function CapabilityWelfareWGData:GetMaxGotSeq()
	local max_seq = 0
	for seq, flag in pairs(self.fetch_reward_flag) do
		if flag == 1 and seq > max_seq then
			max_seq = seq
		end
	end
	return max_seq
end

-- 读取任务
function CapabilityWelfareWGData:GetTaskBySeq(seq)
	return self.task_cfg and self.task_cfg[seq] or {}
end

-- 获取当前功能已打开天数
function CapabilityWelfareWGData:GetCurrentOpenDay()
	return self.real_open_day or 0
end

-- 获取结束时间戳
function CapabilityWelfareWGData:GetCloseTimeStamp()
	return self.end_timestamp
end

-- 获取总战力
function CapabilityWelfareWGData:GetTotalCapability()
	return self.total_capability or 0
end

function CapabilityWelfareWGData:GetModelCfg()
	return self.model_cfg
end

-- 获取某天是否有可领取奖励
function CapabilityWelfareWGData:GetDayIsRed(day)
	if not self.all_task_info or not self.all_task_info[day] then
		return false
	end
	if day > self:GetCurrentOpenDay() then
		return false
	end
	local day_info = self.all_task_info[day]
	if day_info.final_task.can_fetch then
		return true
	end
	for i, v in ipairs(day_info.task_list) do
		if v.can_fetch then
			return true
		end
	end
	return false
end

-- 获取最终奖励
function CapabilityWelfareWGData:GetFinalReward()
	return self.other_cfg.sp_reward_item
end

-- 获取最终奖励是否可领取、领取状态
function CapabilityWelfareWGData:GetFetchFinalRewardState()
	if not self.fetch_reward_flag or not self.final_reward_fetch_flag then
		return false, 0
	end
	for k, v in pairs(self.task_cfg) do
		if self.fetch_reward_flag[k] == 0 then
			return false, self.final_reward_fetch_flag
		end
	end
	return self.final_reward_fetch_flag == 0, self.final_reward_fetch_flag
end

-- 获取跳转界面(提前开启跳转)
function CapabilityWelfareWGData:GetJumpView()
    return self.other_cfg.open_view
end

-- 获取跳转界面（界面跳转）
function CapabilityWelfareWGData:GetJumpViewNew()
	return self.other_cfg.function_view
end

-- 获取进度
function CapabilityWelfareWGData:GetTaskProgress()
	if not self.capability_weal_cfg or not self.fetch_reward_flag then
		return 0, 0
	end
	local complete_num = 0
	local total_num = 0
	for i, v in ipairs(self.capability_weal_cfg.task) do
		if self.fetch_reward_flag[v.seq] == 1 then
			complete_num = complete_num + 1
		end
		total_num = total_num + 1
	end
	return complete_num, total_num
end

-- 获取跳转天数
function CapabilityWelfareWGData:GetJumpDay()
	if not self.capability_weal_cfg or not self.fetch_reward_flag then
		return 1
	end
	local cur_open_day = self:GetCurrentOpenDay()
	for i, v in ipairs(self.capability_weal_cfg.task) do
		if self.fetch_reward_flag[v.seq] == 0 then -- 首个未领奖励的任务
			if v.open_day > cur_open_day then
				return cur_open_day
			end
			return v.open_day
		end
	end
	local max_day = #self.open_day_task_cfg
	return max_day
end

-- 红点方法
function CapabilityWelfareWGData:GetRemind()
	for k, v in pairs(self.open_day_task_cfg) do
		if self:GetDayIsRed(k) then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.CapabilityWelfare, 1, function ()
				ViewManager.Instance:Open(GuideModuleName.CapabilityWelfare)
			end)
			return 1
		end
	end
	--[[ 屏蔽最终奖励
	local can_fetch_final = self:GetFetchFinalRewardState()
	if can_fetch_final then
		return 1
	end
	]]--

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.CapabilityWelfare, 0)
	return 0
end

--任务是否全部领取
function CapabilityWelfareWGData:GetIsAllGetTask()
	local is_all_get = true

	for k, v in pairs(self.task_cfg) do
		if self.fetch_reward_flag[k] ~= 1 then
			is_all_get = false
			break
		end
	end

	return is_all_get
end