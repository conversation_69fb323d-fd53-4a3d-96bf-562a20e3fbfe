-- 通天降临-川流不息

local BG_EFFECT = {
	"UI_TTJL_clbx_bg",
	"UI_thjl_bgeff"
}

function WorldTreasureView:FlowingReleaseCallBack()
	if self.flowing_buy_item_grid then
		self.flowing_buy_item_grid:DeleteMe()
		self.flowing_buy_item_grid = nil
	end
	self.flowing_is_loaded = nil
	self.flowing_need_jump = nil
end

function WorldTreasureView:FlowingLoadCallBack()
	if not self.flowing_buy_item_grid then
		self.flowing_buy_item_grid = AsyncBaseGrid.New()
		self.flowing_buy_item_grid:SetStartZeroIndex(false)
		self.flowing_buy_item_grid:CreateCells({
			col = 3,
			assetName = "flowing_buy_item",
			assetBundle = "uis/view/world_treasure_ui_prefab",
			list_view = self.node_list["flowing_buy_list_view"],
			itemRender = WorldTreasureFlowingBuyItem,
			change_cells_num = 1,
		})
	end
	self.flowing_is_loaded = true
	--self:DoFlowingCellsAnim()

	self:ChangeFlowingStyle()
end

function WorldTreasureView:FlowingShowIndexCallBack()
	-- if self.flowing_is_loaded then
	-- 	self:DoFlowingCellsAnim()
	-- end
	self.flowing_need_jump = true
end

function WorldTreasureView:FlowingOnFlush(param_t)
	local data_list = WorldTreasureWGData.Instance:GetFlowingDataList()
	self.flowing_buy_item_grid:SetDataList(data_list)

	local jump_index
	for i, v in ipairs(data_list) do  --第一个未领取的
		if not v.reward_flag then
			jump_index = i
			break
		end
	end
	if jump_index then
		local per
		if #data_list <= 6 or jump_index <= 6 then
			per = 0
		else
			local length = #data_list
			local column = math.ceil((length - 6) / 3)
			per = math.ceil((jump_index - 6) / 3) / column
		end
		self.jump_per = per
		self.flowing_buy_item_grid:JumptToPrecent(per)
	end

	if self.flowing_need_jump then
		self:DoFlowingCellsAnim()
		self.flowing_need_jump = false
	end
end

function WorldTreasureView:DoFlowingCellsAnim()
	-- local tween_info = UITween_CONSTS.WorldTreasureView.FlowingItemRender
	-- self.node_list["flowing_buy_list_view"]:SetActive(false)
	local list = self.flowing_buy_item_grid:GetAllCell()
	local sort_list = self:GetFlowingSortListView(list)
	local count = 0
	for k,v in ipairs(sort_list) do
		if 0 ~= v.index then
			count = count + 1
		end
		v.item:PalyItemAnim(count)
	end
    -- ReDelayCall(self, function()
	-- 	self.node_list["flowing_buy_list_view"]:SetActive(true)
    --     local list = self.flowing_buy_item_grid:GetAllCell()
    --     local sort_list = self:GetFlowingSortListView(list)
    --     local count = 0
    --     for k,v in ipairs(sort_list) do
    --         if 0 ~= v.index then
    --             count = count + 1
    --         end
    --         v.item:PalyItemAnim(count)
    --     end
    -- end, tween_info.DelayDoTime, "flowing_cell_tween")
end

--获得排序后的list view(界面list view动画用)
function WorldTreasureView:GetFlowingSortListView(list_tb)
	local sort_list = {}
	for i, v in pairs(list_tb) do
		local data = {}
		data.index = (v:GetData() or {}).seq or 0
		data.item = v
		sort_list[#sort_list + 1] = data
	end
	table.sort(sort_list, SortTools.KeyLowerSorter("index"))
	return sort_list
end

function WorldTreasureView:ChangeFlowingStyle()
	local grade = WorldTreasureWGData.Instance:GetTreasureCurGrade() or 1
	-- 背景特效
	local bundle, asset = ResPath.GetUIEffect(BG_EFFECT[grade])
	self.node_list.flowing_bg_effect:ChangeAsset(bundle, asset)

	local bundle, asset = WorldTreasureWGData.Instance:FormatGradeRawImage("a3_ttjl_bt1_")
	self.node_list.flowing_title_img.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.flowing_title_img.raw_image:SetNativeSize()
	end)
end

----------------------------------
-- 川流不息Item
----------------------------------
WorldTreasureFlowingBuyItem = WorldTreasureFlowingBuyItem or BaseClass(BaseRender)

function WorldTreasureFlowingBuyItem:LoadCallBack()
    self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    self.reward_list:SetStartZeroIndex(true)
    XUI.AddClickEventListener(self.node_list["btn_free"], BindTool.Bind1(self.OnClickBtnBuy, self))
	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind1(self.OnClickBtnBuy, self))
	
	self:ChangeStyle()
end

function WorldTreasureFlowingBuyItem:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function WorldTreasureFlowingBuyItem:OnFlush()
	if IsEmptyTable(self.data) then
		self.node_list["content"]:SetActive(false)
		return
	end
	self.node_list["content"]:SetActive(true)

	self.node_list["btn_free"]:SetActive(not self.data.reward_flag and self.data.type == 0)
	self.node_list["btn_buy"]:SetActive(not self.data.reward_flag and self.data.type == 1)
	self.node_list["reward_flag"]:SetActive(self.data.reward_flag)
	self.node_list["bg"]:SetActive(not self.data.reward_flag)
	self.node_list["bg_gray"]:SetActive(self.data.reward_flag)
	self.node_list["img_locked"]:SetActive(not self.data.is_unlock)

	local btn_txt = Language.WorldTreasure.Free
	if self.data.type == 1 then
		btn_txt = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
	end
	self.node_list["txt_buy_text"].text.text = btn_txt

	local show_remind = not self.data.reward_flag and self.data.is_unlock and self.data.type == 0
	self.node_list["remind"]:SetActive(show_remind)

	self.reward_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetRedPointEff(show_remind)
        end
    end)
	self.reward_list:SetDataList(self.data.reward_item)

	--进度条
	self.node_list["progress_right"]:SetActive(false)
	self.node_list["progress_left"]:SetActive(false)
	self.node_list["progress_down"]:SetActive(false)
	local show_progress_str
	if self.data.is_last then
		show_progress_str = false
	elseif (self.data.seq + 1) % 3 == 0 then
		show_progress_str = "progress_down"
	elseif (((self.data.seq + 1) % 6) / 3) < 1 then
		show_progress_str = "progress_right"
	else
		show_progress_str = "progress_left"
	end

	if show_progress_str then
		self.node_list[show_progress_str]:SetActive(true)
		self.node_list[show_progress_str .. "_hl"]:SetActive(self.data.reward_flag)
	end
end

function WorldTreasureFlowingBuyItem:OnClickBtnBuy()
	if IsEmptyTable(self.data) then
		return
	end

	if not self.data.is_unlock then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.Locked2)
		return
	end

	if self.data.type == 0 then
		WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_FLOWING_REWARD, self.data.seq)
	elseif self.data.type == 1 then
		RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
	end
end

function WorldTreasureFlowingBuyItem:PalyItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.WorldTreasureView.FlowingItemRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.AlphaShow(GuideModuleName.WorldTreasureView, self.node_list["tween_root"], tween_info.FromAlpha, tween_info.ToAlpha, tween_info.AlphaTweenTime, tween_info.AlphaShowType)
        end
    end, tween_info.NextDoDelay * wait_index, "flowing_task_item_" .. wait_index)
end

function WorldTreasureFlowingBuyItem:ResetAlpha()
    UITween.FakeHideShow(self.node_list["tween_root"])
end

function WorldTreasureFlowingBuyItem:ChangeStyle()
	local bundle, asset = WorldTreasureWGData.Instance:FormatGradeRawImage("a3_ttjl_mc_")
	self.node_list.bg.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.bg.raw_image:SetNativeSize()
	end)
	
	bundle, asset = WorldTreasureWGData.Instance:FormatGradeRawImage("a3_ttjl_mc1_")
	self.node_list.bg_gray.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.bg_gray.raw_image:SetNativeSize()
	end)
	
	bundle, asset = WorldTreasureWGData.Instance:FormatGradeImage("a3_ttjl_mf_")
	self.node_list.btn_free.image:LoadSprite(bundle, asset, function()
        self.node_list.btn_free.image:SetNativeSize()
    end)

	bundle, asset = WorldTreasureWGData.Instance:FormatGradeImage("a3_ttjl_gm_")
	self.node_list.btn_buy.image:LoadSprite(bundle, asset, function()
        self.node_list.btn_buy.image:SetNativeSize()
    end)
end
