TianShenShenShiComposeTips = TianShenShenShiComposeTips or BaseClass(SafeBaseView)

function TianShenShenShiComposeTips:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 486)})
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_shenshi_compose_tip")
end

function TianShenShenShiComposeTips:__delete()
end

function TianShenShenShiComposeTips:ReleaseCallBack()
	if self.target_cell then
		self.target_cell:DeleteMe()
		self.target_cell = nil
	end

	if self.cost_list then
		self.cost_list:DeleteMe()
		self.cost_list = nil
	end

	if self.small_cost_list then
		for k,v in pairs(self.small_cost_list) do
			v:DeleteMe()
		end
		self.small_cost_list = nil
	end
end

function TianShenShenShiComposeTips:LoadCallBack()
	self.need_num = 0
	self:SetViewName(Language.TianShen.ShenShiHeCheng)
	self.target_cell = ItemCell.New(self.node_list.target_cell)
	XUI.AddClickEventListener(self.node_list.btn_compose, BindTool.Bind1(self.OnClickCompose, self))
	self.cost_list = AsyncListView.New(ShenShiComposeCell, self.node_list["cost_cell_list"])
	self.small_cost_list = {}
	for i =1,4 do
		self.small_cost_list[i] = ShenShiComposeCell.New(self.node_list["tianshen_compose_cell"..i])
	end
end

function TianShenShenShiComposeTips:ShowIndexCallBack(index)

end

function TianShenShenShiComposeTips:OnFlush()
	if self.compose_data and self.compose_data.com_way_table then
		if #self.compose_data.com_way_table <= 3 then
			self.node_list["mask2"]:SetActive(true)
			self.node_list["mask1"]:SetActive(false)
			for i =1,4 do
				if self.compose_data.com_way_table[i] then
					self.small_cost_list[i]:SetData(self.compose_data.com_way_table[i])
					self.node_list["tianshen_compose_cell"..i]:SetActive(true)
				else
					self.node_list["tianshen_compose_cell"..i]:SetActive(false)
				end
			end
		else
			self.node_list["mask1"]:SetActive(true)
			self.node_list["mask2"]:SetActive(false)
			self.cost_list:SetDataList(self.compose_data.com_way_table)
		end
		self.target_cell:SetData({item_id = self.compose_data.target_item})
		local attr_cfg = TianShenWGData.Instance:GetTianShenAttributeTab(self.compose_data.target_item)
		local attr_num = 0
		for k,v in pairs(attr_cfg) do
			if v > 0 then
				if Language.Common.TipsAttrNameList[k] then
					attr_num = attr_num + 1
					self.node_list["attr_name"..attr_num].text.text = Language.Common.TipsAttrNameList[k]
					self.node_list["attr"..attr_num].text.text = ToColorStr(v, COLOR3B.DEFAULT_NUM)
				end
			end
		end

		for i=1,4 do
			self.node_list["attr"..i]:SetActive(i <= attr_num)
		end
		
		self.node_list["attrcell2"]:SetActive(attr_num > 2)

	end

end

function TianShenShenShiComposeTips:OnClickCompose()
	if self.compose_data and self.compose_data.compose_func then
		self.compose_data.compose_func()
	end
	self:Close()
end

function TianShenShenShiComposeTips:SetComposeData(data)
	self.compose_data = data
end



ShenShiComposeCell = ShenShiComposeCell or BaseClass(BaseRender)

function ShenShiComposeCell:__init()
	self.item_cell = ItemCell.New(self.node_list.cell_pos)
end

function ShenShiComposeCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ShenShiComposeCell:OnFlush()
	self.item_cell:SetData(self.data)
	local str = self.data.have_num.."/"..self.data.cost_num
	self.node_list.cost_text.text.text = str
end