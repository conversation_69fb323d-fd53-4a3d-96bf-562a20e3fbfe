﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Sockets;
using UnityEngine;

namespace Nirvana
{
	/// <summary>
	/// EnhancedNetClient 网络客户端封装。
	/// 使用 System.Net.Sockets.Socket 提供 TCP 连接、收发消息与断开处理，
	/// 并通过回调事件向上层派发网络状态与消息数据。
	/// </summary>
	public sealed class EnhancedNetClient
	{
		/// <summary>
		/// 断开连接事件。当连接被动或主动关闭时触发。
		/// </summary>
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EnhancedNetClient.DisconnectDelegate DisconnectEvent;

        /// <summary>
        /// 接收消息事件。收到一条完整消息时触发（长度前缀 + 负载）。
        /// </summary>
        [field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event EnhancedNetClient.ReceiveDelegate ReceiveEvent;

		/// <summary>
		/// 接收超时（毫秒）。
		/// </summary>
		public int ReceiveTimeout
		{
			get
			{
				return this.socket.ReceiveTimeout;
			}
			set
			{
				this.socket.ReceiveTimeout = value;
			}
		}

		/// <summary>
		/// 发送超时（毫秒）。
		/// </summary>
		public int SendTimeout
		{
			get
			{
				return this.socket.SendTimeout;
			}
			set
			{
				this.socket.SendTimeout = value;
			}
		}

		/// <summary>
		/// 构造函数。初始化读取缓冲区大小，默认读取窗口为 2x ReadLength。
		/// </summary>
		public EnhancedNetClient()
		{
			this.readBuffer = new EnhancedNetBuffer(2 * this.ReadLength);
		}

		/// <summary>
		/// 设置单次 BeginReceive 期望读取的长度，并重建内部读缓冲区容量（2x）。
		/// </summary>
		/// <param name="length">期望读取的长度（字节）。</param>
		public void SetReadLength(int length)
		{
			this.ReadLength = length;
			this.readBuffer = new EnhancedNetBuffer(2 * this.ReadLength);
		}

		/// <summary>
		/// 清除所有事件的订阅者，避免泄漏。
		/// </summary>
		public void Clear()
		{
			Delegate[] invocationList = this.DisconnectEvent.GetInvocationList();
			for (int i = 0; i < invocationList.Length; i++)
			{
				this.DisconnectEvent -= invocationList[i] as EnhancedNetClient.DisconnectDelegate;
			}
			Delegate[] invocationList2 = this.ReceiveEvent.GetInvocationList();
			for (int j = 0; j < invocationList2.Length; j++)
			{
				this.ReceiveEvent -= invocationList2[j] as EnhancedNetClient.ReceiveDelegate;
			}
		}

		/// <summary>
		/// 异步连接到指定主机与端口。
		/// 先解析 DNS，再创建 Socket 并 BeginConnect，完成后回调 completeDelegate。
		/// </summary>
		/// <param name="host">域名或 IP。</param>
		/// <param name="port">端口。</param>
		/// <param name="completeDelegate">连接完成回调，参数为是否成功。</param>
		public void Connect(string host, int port, EnhancedNetClient.ConnectDelegate completeDelegate = null)
		{
			bool flag = this.socket != null;
			if (flag)
			{
				completeDelegate(false);
			}
			else
			{
				Dns.BeginGetHostAddresses(host, delegate(IAsyncResult result)
				{
					IPAddress[] array = Dns.EndGetHostAddresses(result);
					bool flag2 = array.Length == 0;
					if (flag2)
					{
                        UnityEngine.Debug.LogError("[EnhancedNetClient]Connect failed: can not resolve DNS.");
						completeDelegate(false);
					}
					else
					{
						try
						{
							IPAddress ipaddress = array[0];
							// 进入连接流程，重置关闭标记
							this.isClosing = false;
							// 按解析到的地址族创建 TCP 套接字
							this.socket = new Socket(ipaddress.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
							this.socket.BeginConnect(ipaddress, port, new AsyncCallback(this.ConnectCallback), completeDelegate);
						}
						catch (Exception ex)
						{
                            UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Connect failed: {0}", new object[] { ex.Message });
							completeDelegate(false);
						}
					}
				}, null);
			}
		}

		/// <summary>
		/// 主动断开连接。
		/// 若发送队列中仍有未发完的数据，尝试一次性发送后再关闭。
		/// </summary>
		public void Disconnect()
		{
			try
			{
				this.isClosing = true;
				bool connected = this.socket.Connected;
				if (connected)
				{
					bool flag = this.writeQueue.Count > 0;
					if (flag)
					{
						List<ArraySegment<byte>> list = new List<ArraySegment<byte>>();
						foreach (EnhancedWriteMessage writeMessage in this.writeQueue)
						{
							ArraySegment<byte> arraySegment = new ArraySegment<byte>(writeMessage.Buffer, 0, writeMessage.BufferLength);
							list.Add(arraySegment);
						}
						this.writeQueue.Clear();
						// 合并发送剩余数据，避免数据丢失
						this.socket.Send(list, SocketFlags.None);
					}

					this.socket.Shutdown(SocketShutdown.Both);
					this.socket.Close();
					bool flag2 = this.DisconnectEvent != null;
					if (flag2)
					{
						this.DisconnectEvent(EnhancedNetClient.DisconnectReason.Manual, null);
					}
				}
			}
			catch (SocketException se)
			{
				if (se.SocketErrorCode == SocketError.NetworkDown || se.SocketErrorCode == SocketError.NetworkUnreachable)
				{
					UnityEngine.Debug.LogWarning("[EnhancedNetClient]Socket disconnect due to network down; will notify for reconnect.");
					this.DisconnectEvent?.Invoke(EnhancedNetClient.DisconnectReason.Manual, se.Message);
				}
				else
				{
					UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Socket disconnect: {0}", new object[] { se.Message });
					this.DisconnectEvent?.Invoke(EnhancedNetClient.DisconnectReason.Manual, se.Message);
				}
			}
			catch (Exception ex)
			{
				UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Socket disconnect: {0}", new object[] { ex.Message });
				this.DisconnectEvent?.Invoke(EnhancedNetClient.DisconnectReason.Manual, ex.Message);
			}
		}

		/// <summary>
		/// 设置 TCP keep-alive 相关参数。
		/// </summary>
		/// <param name="onOff">是否开启（1 开启，0 关闭）。</param>
		/// <param name="keepAliveTime">首次探测前空闲时长（毫秒）。</param>
		/// <param name="keepAliveInterval">探测间隔（毫秒）。</param>
		public void SetKeepAlive(uint onOff, uint keepAliveTime, uint keepAliveInterval)
		{
			byte[] array = new byte[12];
			BitConverter.GetBytes(onOff).CopyTo(array, 0);
			BitConverter.GetBytes(keepAliveTime).CopyTo(array, 4);
			BitConverter.GetBytes(keepAliveInterval).CopyTo(array, 8);
			this.socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.KeepAlive, array);
		}

		/// <summary>
		/// 发送一条消息（已按协议打包好的字节）。
		/// 消息进入发送队列，按顺序异步发送，完成后回调 sendDelegate。
		/// </summary>
		/// <param name="data">消息数据（须包含长度字段的协议外部已封装或这里直接发送原始体）。</param>
		/// <param name="sendDelegate">发送完成回调。</param>
		public void SendMsg(byte[] data, EnhancedNetClient.SendDelegate sendDelegate = null)
		{
			bool flag = this.isClosing || this.socket == null || !this.socket.Connected;
			if (flag)
			{
				if (this.socket != null)
				{
					this.socket.Close();
				}
				bool flag2 = this.DisconnectEvent != null;
				if (flag2)
				{
					this.DisconnectEvent(EnhancedNetClient.DisconnectReason.NotConnected, "Socket not connected");
				}
			}
			else
			{
				EnhancedWriteMessage writeMessage = EnhancedWriteMessage.Alloc();
				writeMessage.SetData(data);
				writeMessage.ByteSended = 0;
				writeMessage.Context = sendDelegate;
				this.writeQueue.Enqueue(writeMessage);
				// 若当前未在发送，则立刻触发一次 Flush 开始发送
				bool flag3 = !this.writing;
				if (flag3)
				{
					this.Flush();
				}
			}
		}

		/// <summary>
		/// 开始一次异步接收。
		/// 读取窗口大小为 ReadLength，收到数据后进入 ReceiveCallback。
		/// </summary>
		public void StartReceive()
		{
			try
			{
				int num = this.readBuffer.Prepare(this.ReadLength);
				this.socket.BeginReceive(this.readBuffer.Buffer, num, this.ReadLength, SocketFlags.None, new AsyncCallback(this.ReceiveCallback), this);
			}
			catch (SocketException se)
			{
				if (se.SocketErrorCode == SocketError.NetworkDown || se.SocketErrorCode == SocketError.NetworkUnreachable)
				{
					UnityEngine.Debug.LogWarning("[EnhancedNetClient]Socket receive start: network down/unreachable; will notify for reconnect.");
				}
				else
				{
					UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Socket receive: {0}", new object[] { se.Message });
				}
				this.socket.Close();
				bool flag = this.DisconnectEvent != null;
				if (flag)
				{
					this.DisconnectEvent(EnhancedNetClient.DisconnectReason.ReceiveStartError, se.Message);
				}
			}
			catch (Exception ex)
			{
				UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Socket receive: {0}", new object[] { ex.Message });
				this.socket.Close();
				bool flag = this.DisconnectEvent != null;
				if (flag)
				{
					this.DisconnectEvent(EnhancedNetClient.DisconnectReason.ReceiveStartError, ex.Message);
				}
			}
		}

		/// <summary>
		/// Connect 完成回调。
		/// 结束连接过程并在主线程（Scheduler.PostTask）派发连接结果。
		/// </summary>
		private void ConnectCallback(IAsyncResult ar)
		{
			EnhancedNetClient.ConnectDelegate connectDelegate = (EnhancedNetClient.ConnectDelegate)ar.AsyncState;
			try
			{
				this.socket.EndConnect(ar);
				bool flag = connectDelegate != null;
				if (flag)
				{
					bool isCompleted = ar.IsCompleted;
					Scheduler.PostTask(delegate
					{
						connectDelegate(isCompleted);
					});
				}
			}
			catch (ObjectDisposedException)
			{
			}
			catch (Exception ex)
			{
                UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Socket receive: {0}", new object[] { ex.Message });
				bool flag2 = connectDelegate != null;
				if (flag2)
				{
					Scheduler.PostTask(delegate
					{
						connectDelegate(false);
					});
				}
			}
		}

		/// <summary>
		/// 尝试从发送队列中取出一条消息异步发送。
		/// 若队列为空，重置 writing 状态以允许后续触发。
		/// </summary>
		private void Flush()
		{
			if (this.isClosing || this.socket == null || !this.socket.Connected)
			{
				this.writeQueue.Clear();
				this.writing = false;
				return;
			}
			bool flag = this.writeQueue.Count > 0;
			if (flag)
			{
				EnhancedWriteMessage writeMessage = this.writeQueue.Dequeue();
				try
				{
					this.writing = true;
					this.socket.BeginSend(writeMessage.Buffer, writeMessage.ByteSended, writeMessage.BufferLength - writeMessage.ByteSended, SocketFlags.None, new AsyncCallback(this.SendCallback), writeMessage);
				}
				catch (SocketException se)
				{
					if (se.SocketErrorCode == SocketError.NetworkDown || se.SocketErrorCode == SocketError.NetworkUnreachable || se.SocketErrorCode == SocketError.NotConnected)
					{
						UnityEngine.Debug.LogWarning("[EnhancedNetClient]Socket send: network down/unreachable; will notify for reconnect.");
					}
					else
					{
						UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Socket send error: {0}", new object[] { se.Message });
					}
					this.writing = false;
					if (this.socket != null)
					{
						this.socket.Close();
					}
					bool flag2 = this.DisconnectEvent != null;
					if (flag2)
					{
						this.DisconnectEvent(EnhancedNetClient.DisconnectReason.SendError, se.Message);
					}
				}
				catch (ObjectDisposedException)
				{
					UnityEngine.Debug.LogWarning("[EnhancedNetClient]Socket send: disposed during close; will notify for reconnect.");
					this.writing = false;
					Scheduler.PostTask(delegate
					{
						if (this.socket != null)
						{
							this.socket.Close();
						}
						if (this.DisconnectEvent != null)
						{
							this.DisconnectEvent(EnhancedNetClient.DisconnectReason.SendError, "Socket disposed");
						}
					});
				}
				catch (Exception ex)
				{
                    UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Socket send error: {0}", new object[] { ex.Message });
					this.writing = false;
					if (this.socket != null)
					{
						this.socket.Close();
					}
					bool flag2 = this.DisconnectEvent != null;
					if (flag2)
					{
						this.DisconnectEvent(EnhancedNetClient.DisconnectReason.SendError, ex.Message);
					}
				}
			}
			else
			{
				this.writing = false;
			}
		}

		/// <summary>
		/// 异步发送完成回调。若未完全发送则继续发送；
		/// 完成后释放消息并回调上层，然后尝试继续发送队列中的下一条。
		/// </summary>
		private void SendCallback(IAsyncResult ar)
		{
			try
			{
				EnhancedWriteMessage message = (EnhancedWriteMessage)ar.AsyncState;
				int num = this.socket.EndSend(ar);
				message.ByteSended += num;
				bool flag = message.ByteSended < message.BufferLength;
				if (flag)
				{
					this.socket.BeginSend(message.Buffer, message.ByteSended, message.BufferLength - message.ByteSended, SocketFlags.None, new AsyncCallback(this.SendCallback), message);
				}
				else
				{
					EnhancedNetClient.SendDelegate sendDelegate = (EnhancedNetClient.SendDelegate)message.Context;
					Scheduler.PostTask(delegate
					{
						try
						{
							EnhancedWriteMessage.Free(message);
							bool flag2 = sendDelegate != null;
							if (flag2)
							{
								sendDelegate();
							}
						}
						finally
						{
							this.Flush();
						}
					});
				}
			}
			catch (ObjectDisposedException)
			{
				UnityEngine.Debug.LogWarning("[EnhancedNetClient]SendCallback: socket disposed; will notify for reconnect.");
				Scheduler.PostTask(delegate
				{
					if (this.socket != null)
					{
						this.socket.Close();
					}
					bool flag3 = this.DisconnectEvent != null;
					if (flag3)
					{
						this.DisconnectEvent(EnhancedNetClient.DisconnectReason.SendError, "Socket disposed");
					}
				});
			}
			catch (SocketException se)
			{
				if (se.SocketErrorCode == SocketError.NetworkDown || se.SocketErrorCode == SocketError.NetworkUnreachable || se.SocketErrorCode == SocketError.NotConnected)
				{
					UnityEngine.Debug.LogWarning("[EnhancedNetClient]SendCallback: network down/unreachable/not connected; will notify for reconnect.");
				}
				else
				{
					UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]SendCallback, Socket send: {0}", new object[] { se.Message });
				}
				Scheduler.PostTask(delegate
				{
					if (this.socket != null)
					{
						this.socket.Close();
					}
					bool flag3 = this.DisconnectEvent != null;
					if (flag3)
					{
						this.DisconnectEvent(EnhancedNetClient.DisconnectReason.SendError, se.Message);
					}
				});
			}
			catch (Exception ex)
			{
                UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]SendCallback, Socket send: {0}", new object[] { ex.Message });
				Scheduler.PostTask(delegate
				{
					if (this.socket != null)
					{
						this.socket.Close();
					}
					bool flag3 = this.DisconnectEvent != null;
					if (flag3)
					{
						this.DisconnectEvent(EnhancedNetClient.DisconnectReason.SendError, ex.Message);
					}
				});
			}
		}

		/// <summary>
		/// 异步接收回调。若收到长度为 0 认为对端关闭；
		/// 否则将数据投递到主线程处理并继续下一次接收。
		/// </summary>
		private void ReceiveCallback(IAsyncResult ar)
		{
			try
			{
				int length = this.socket.EndReceive(ar);
				bool flag = length > 0;
				if (flag)
				{
					Scheduler.PostTask(delegate
					{
						this.ProcessReadBuffer(length);
						bool connected = this.socket.Connected;
						if (connected)
						{
							this.StartReceive();
						}
					});
				}
				else
				{
					Scheduler.PostTask(delegate
					{
						this.socket.Close();
						bool flag2 = this.DisconnectEvent != null;
						if (flag2)
						{
							this.DisconnectEvent(EnhancedNetClient.DisconnectReason.RemoteClosed, null);
						}
					});
				}
			}
			catch (ObjectDisposedException)
			{
			}
			catch (SocketException se)
			{
				if (se.SocketErrorCode == SocketError.NetworkDown || se.SocketErrorCode == SocketError.NetworkUnreachable)
				{
					UnityEngine.Debug.LogWarning("[EnhancedNetClient]Socket receive: network down/unreachable; will notify for reconnect.");
				}
				else
				{
					UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Socket receive: {0}", new object[] { se.Message });
				}
				Scheduler.PostTask(delegate
				{
					this.socket.Close();
					bool flag3 = this.DisconnectEvent != null;
					if (flag3)
					{
						this.DisconnectEvent(EnhancedNetClient.DisconnectReason.ReceiveError, se.Message);
					}
				});
			}
			catch (Exception ex)
			{
				UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]Socket receive: {0}", new object[] { ex.Message });
				Scheduler.PostTask(delegate
				{
					this.socket.Close();
					bool flag3 = this.DisconnectEvent != null;
					if (flag3)
					{
						this.DisconnectEvent(EnhancedNetClient.DisconnectReason.ReceiveError, ex.Message);
					}
				});
			}
		}

		/// <summary>
		/// 处理累计到读缓冲区中的数据，按 [4字节长度 | 负载] 协议拆包，
		/// 每解析出一条完整消息即派发 ReceiveEvent。
		/// </summary>
		private void ProcessReadBuffer(int length)
		{
			bool flag = !this.readBuffer.Submit(length);
			if (flag)
			{
                UnityEngine.Debug.LogError("[EnhancedNetClient]ProcessReadBuffer, EnhancedNetClient receive buffer submit out of range.");
			}
			// 至少要有 4 字节长度头
			while (this.readBuffer.Payload >= 4)
			{
				uint num = BitConverter.ToUInt32(this.readBuffer.Buffer, this.readBuffer.DataStart);
				// 不足以组成一条完整消息，等待更多数据
				bool flag2 = (long)this.readBuffer.Payload < (long)((ulong)(4U + num));
				if (flag2)
				{
					break;
				}
				bool flag3 = (long)this.messageBuffer.Length < (long)((ulong)num);
				if (flag3)
				{
					this.messageBuffer = new byte[num];
				}
				Array.Copy(this.readBuffer.Buffer, (long)(this.readBuffer.DataStart + 4), this.messageBuffer, 0L, (long)((ulong)num));
				this.readBuffer.Consume((int)(num + 4U));
				bool flag4 = this.ReceiveEvent != null;
				if (flag4)
				{
					try
					{
						this.ReceiveEvent(this.messageBuffer, num);
					}
					catch (Exception ex)
					{
                        UnityEngine.Debug.LogErrorFormat("[EnhancedNetClient]ProcessReadBuffer, Exception, {0}", new object[] { ex.ToString() });
					}
				}
			}
		}

		/// <summary>
		/// 监听断线事件
		/// </summary>
		public delegate void DisconnectWithReasonDelegate(int reason, string detail);

		public EnhancedNetClient.DisconnectDelegate ListenDisconnectWithReason(DisconnectWithReasonDelegate handler)
		{
			EnhancedNetClient.DisconnectDelegate adapter = (reason, detail) => handler((int)reason, detail);
			this.DisconnectEvent += adapter;
			return adapter;
		}

		/// <summary>
		/// 取消监听断线事件。
		/// </summary>
		public void UnlistenDisconnect(EnhancedNetClient.DisconnectDelegate handle)
		{
			this.DisconnectEvent -= handle;
		}

		private Socket socket; // TCP 套接字实例

		private int ReadLength = 32768; // 单次异步读取期望长度

		private EnhancedNetBuffer readBuffer = null; // 循环读缓冲区，用于累计数据与拆包

		private byte[] messageBuffer = new byte[16384]; // 临时消息缓冲，根据需要扩容

		private Queue<EnhancedWriteMessage> writeQueue = new Queue<EnhancedWriteMessage>(); // 发送队列（保证顺序）

		private bool writing = false; // 是否正处于发送中（避免并发 BeginSend）

		private bool isClosing = false; // 是否正在关闭

		/// <summary>
		/// 连接结果回调。
		/// </summary>
		public delegate void ConnectDelegate(bool isCompleted);

		/// <summary>
		/// 断开连接回调，包含断开原因与可选详情。
		/// </summary>
		public delegate void DisconnectDelegate(EnhancedNetClient.DisconnectReason reason, string detail);

		/// <summary>
		/// 单条消息发送完成回调。
		/// </summary>
		public delegate void SendDelegate();

		/// <summary>
		/// 收到一条完整消息时的回调。
		/// </summary>
		public delegate void ReceiveDelegate(byte[] message, uint length);

		/// <summary>
		/// 断开连接原因。
		/// </summary>
		public enum DisconnectReason
		{
			/// <summary>
			/// 本地主动断开（调用 Disconnect ）。
			/// </summary>
			Manual,
			/// <summary>
			/// 远端主动关闭（接收长度为 0）。
			/// </summary>
			RemoteClosed,
			/// <summary>
			/// 套接字未连接（发送前检测到未连接）。
			/// </summary>
			NotConnected,
			/// <summary>
			/// 发送过程中发生异常（BeginSend/EndSend/Flush）。
			/// </summary>
			SendError,
			/// <summary>
			/// 接收过程中发生异常（ReceiveCallback）。
			/// </summary>
			ReceiveError,
			/// <summary>
			/// 开始接收时发生异常（Prepare/BeginReceive）。
			/// </summary>
			ReceiveStartError
		}
	}
}
