require("game/xinfutemai/xinfutemai_wg_data")
require("game/xinfutemai/xinfutemai_view")

XinFuTeMaiWGCtrl = XinFuTeMaiWGCtrl or BaseClass(BaseWGCtrl)

function XinFuTeMaiWGCtrl:__init()
    if XinFuTeMaiWGCtrl.Instance then
        error("[XinFuTeMaiWGCtrl]:Attempt to create singleton twice!")
    end
    XinFuTeMaiWGCtrl.Instance = self

    self.data = XinFuTeMaiWGData.New()
    self.view = XinFuTeMaiView.New(GuideModuleName.XinFuTeMai)
    self:RegisterAllProtocals()
end

function XinFuTeMaiWGCtrl:__delete()
    if nil ~= self.view then
        self.view:DeleteMe()
        self.view = nil
    end
    if nil ~= self.data then
        self.data:DeleteMe()
        self.data = nil
    end
    XinFuTeMaiWGCtrl.Instance = nil
end

function XinFuTeMaiWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCNewShopInfo, "OnSCNewShopInfo") --新服特卖奖励信息(激活标记，领奖标记)
    self:RegisterProtocol(CSNewShopFetchRewardReq)          --请求领奖
end

function XinFuTeMaiWGCtrl:OnSCNewShopInfo(protocol)
    --print_error("新服特卖 protocol >>>>>>" , protocol)
    self.data:SetNewShopInfo(protocol)
    if protocol.is_open_window == 1 then
        if protocol.end_time_stamp > 0 then
            local activity_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.XinFuTeMai)
            --主界面添加活动按钮
            MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.XinFuTeMai, protocol.end_time_stamp, activity_cfg)
            if Scene.Instance:GetSceneType() == SceneType.Common then
                local callback = function()
                    --自动打开界面
                    ViewManager.Instance:Open(GuideModuleName.XinFuTeMai, 0)
                end
                if MainuiWGCtrl.Instance:IsLoadMainUiView() then
                    callback()
                else
                    MainuiWGCtrl.Instance:AddInitCallBack(nil,callback)
                end
            end
        end
    else
        if protocol.reward_fetch_flag == 1 and protocol.reward_active_flag == 1 then
            if self.view:IsOpen() then
                self.view:Close()
            end
            MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.XinFuTeMai)
            return
        end
        if protocol.end_time_stamp > 0 then
            local activity_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.XinFuTeMai)
            --主界面添加活动按钮
            MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.XinFuTeMai, protocol.end_time_stamp, activity_cfg)
        end
    end

    RemindManager.Instance:Fire(RemindName.XinFuTeMai)

    if protocol.end_time_stamp <= 0 then
        MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.XinFuTeMai)
    else
        if self.view:IsOpen() then
            self.view:Flush()
        end
    end
end

function XinFuTeMaiWGCtrl:CheckViewClose()
    if self.view:IsOpen() then
        self.view:Close()
    end
end

function XinFuTeMaiWGCtrl:NewShopFetchRewardReq()
    local protocol = ProtocolPool.Instance:GetProtocol(CSNewShopFetchRewardReq)
    local server_time  = TimeWGCtrl.Instance:GetServerTime()
    protocol.now_time_stamp = math.floor(server_time)
    protocol:EncodeAndSend()
end

