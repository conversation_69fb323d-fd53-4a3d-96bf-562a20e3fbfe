ZFTQRewardPreviewView = ZFTQRewardPreviewView or BaseClass(SafeBaseView)
function ZFTQRewardPreviewView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_reward_preview")
end

function ZFTQRewardPreviewView:ReleaseCallBack()
	if self.reward_list then
		for key, value in pairs(self.reward_list) do
			value:DeleteMe()
			value = nil
		end
		self.reward_list = nil
	end
end

function ZFTQRewardPreviewView:LoadCallBack()
	if not self.reward_list then
		self.reward_list = {}

		local bcak_cfg = RechargeWGData.Instance:GetZFAllBackCfg()
		if IsEmptyTable(bcak_cfg) then
			return
		end

		for i, v in pairs(bcak_cfg) do
			self.reward_list[i] = AsyncBaseGrid.New()
			self.reward_list[i]:CreateCells({
				col = 3,
				change_cells_num = 1,
				list_view = self.node_list["reward_grid_" .. i]
			})
			self.reward_list[i]:SetStartZeroIndex(false)

			self.node_list["type_text_" .. i].text.text = v.quality
		end
	end
end

function ZFTQRewardPreviewView:OnFlush()
	local bcak_cfg = RechargeWGData.Instance:GetZFAllBackCfg()
	if IsEmptyTable(bcak_cfg) then
		return
	end

	for i, v in pairs(bcak_cfg) do
		self.reward_list[i]:SetDataList(v.reward_item)
	end
end
