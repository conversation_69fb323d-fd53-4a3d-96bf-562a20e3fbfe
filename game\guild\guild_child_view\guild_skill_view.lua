
local SKILL_PAGE_COUNT = 3
-- 仙盟技能
function GuildView:InitGuildSkillView()
	self.skill_list_data = {}
	self.cur_stage_index = -1
	self.guild_skill_cell = ItemCell.New(self.node_list["ph_cell_1"])  --右侧宗门贡献消耗 格子
	self.guild_skill_cell:SetNeedItemGetWay(true)

	self.node_list["btn_uplevel"].button:AddClickListener(BindTool.Bind1(self.OnClickSkillUpLevel, self)) --升级
	self.node_list["btn_guild_skill_states"].button:AddClickListener(BindTool.Bind1(self.OnClickSkilRule, self)) --问号帮助
	self.show_effect = true
	self.is_load = false
	-------------------------------修改
	self.name_list = {}
	self.remind_list = {}
	self.name_hl_list = {}
	for i = 1, SKILL_PAGE_COUNT do
    	self.name_list[i] = self.node_list["skill_toggle_" .. i]:FindObj("item_bg/name")
		self.name_hl_list[i] = self.node_list["skill_toggle_" .. i]:FindObj("img_hl/name_hl")
    	self.remind_list[i] = self.node_list["skill_toggle_" .. i]:FindObj("remind")
    end

    XUI.AddClickEventListener(self.node_list.right_fanye, BindTool.Bind(self.JumpToSkillIndex,self, 1))
   	XUI.AddClickEventListener(self.node_list.left_fanye, BindTool.Bind(self.JumpToSkillIndex, self, -1))
   	self.node_list["ph_skill_list_view"].list_page_scroll:SetPageCount(self:GetSkillNumberOfCells())
    local list_delegate = self.node_list["ph_skill_list_view"].list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetSkillNumberOfCells, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.SkillRefreshCell, self)

    self.page_event = BindTool.Bind(self.OnJumpSkillPageCallBack, self)
	self.page_scroll = self.node_list["ph_skill_list_view"].list_page_scroll
	self.page_scroll.JumpToPageEvent = self.page_scroll.JumpToPageEvent + self.page_event
	self.select_skill_item = nil
	self.guild_skill_data = nil
	self:JumpPage(0)

	self.arrow_tweener = self.node_list.skill_attr_arrow.rect:DOAnchorPosY(-12, 0.8)
    self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
	self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	
	if not self.role_data_change then
		self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
		RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"longhun"})
	end
end

function GuildView:DeleteGuildSkillView()
	if self.guild_skill_cell then
		self.guild_skill_cell:DeleteMe()
		self.guild_skill_cell = nil
	end
	self.cur_level = nil
	self.cur_skill_id = nil

	-----------------------------修改
	self.select_skill_item = nil

	if self.page_scroll then
		self.page_scroll.JumpToPageEvent = self.page_scroll.JumpToPageEvent - self.page_event
		self.page_scroll = nil
    end

	self.page_event = nil
    self.cur_page_index = nil

    if self.skill_group then
	    for k,v in pairs(self.skill_group) do
			v:DeleteMe()
		end

		self.skill_group = nil
	end

	if self.arrow_tweener then
        self.arrow_tweener:Kill()
        self.arrow_tweener = nil
	end
	
	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

function GuildView:OnJumpSkillPageCallBack()
	local page = self:GetNowPage()
	self:FanYeActive(page)
	if self.cur_page_index ~= nil or page ~= self.cur_page_index then
        self.cur_page_index = page
        for k, v in pairs(self.skill_group) do
			if v.index == page then
				-- v:ActiveFirstItem()
				v:JumpToRedItem()
			end
		end
    end
end

function GuildView:JumpToSkillIndex(num)
	local cur_page = self:GetNowPage()
	local max_page = SKILL_PAGE_COUNT - 1
	local now_page = cur_page + num
	if now_page >= 0 and now_page <= max_page then
		self:JumpPage(now_page)
	else
		self:JumpPage(0)
	end
end

function GuildView:GetSkillNumberOfCells()
	return SKILL_PAGE_COUNT
end

function GuildView:SkillRefreshCell(cell, cell_index)
	if not self.skill_group then
        self.skill_group = {}
    end

    local skill_cell = self.skill_group[cell]
	if skill_cell == nil then
        skill_cell = GuildSkillGroupItem.New(cell.gameObject) 
		self.skill_group[cell] = skill_cell
	end

    local data = {}
	data.data_index = cell_index + 1
	local skill_data = GuildWGData.Instance:GetCurStageSkillCfg(cell_index + 1) --获取阶段技能信息
	data.skill_data = skill_data
	skill_cell:SetIndex(cell_index)
	skill_cell:SetParent(self)
	skill_cell:SetData(data)
end

function GuildView:JumpPage(page)
	if not self.node_list["ph_skill_list_view"]
    or not self.node_list["ph_skill_list_view"].scroller
    or not self.node_list["ph_skill_list_view"].scroller.isActiveAndEnabled then
		return
	end

    if self.node_list["ph_skill_list_view"].list_page_scroll then
    	self.cur_page_index = page
    	self:FanYeActive(page)
	    self.node_list["ph_skill_list_view"].list_page_scroll:JumpToPage(page)
    end
end

function GuildView:GetNowPage()
    if self.node_list["ph_skill_list_view"] and self.node_list["ph_skill_list_view"].list_page_scroll then
	    return self.node_list["ph_skill_list_view"].list_page_scroll:GetNowPage()
    end

    return 0
end

function GuildView:FanYeActive(page)
	-- local show_bundle, show_name = ResPath.GetF2RawImagesPNG("a2_xm_xiulian" .. page + 1)
	-- self.node_list["skill_bg"].raw_image:LoadSpriteAsync(show_bundle, show_name, function()
	-- 	self.node_list["skill_bg"].raw_image:SetNativeSize()
 	-- end)

	-- local show_bundle, show_name = ResPath.GetGuildSystemImage("a3_xm_tu" .. page + 1)
	-- self.node_list["skill_icon_bg"].image:LoadSpriteAsync(show_bundle, show_name, function()
	-- 	self.node_list["skill_icon_bg"].image:SetNativeSize()
 	-- end)

	self.node_list.left_fanye:SetActive(page >= 1)
	self.node_list.right_fanye:SetActive(page < SKILL_PAGE_COUNT - 1)
end

--点击item 刷新右侧选中item
function GuildView:OnGuildSkillItemHandler(item)
	if item == nil then
		return
	end

	self.guild_skill_data = item.data
	self.cur_select_skill_index = item.index
	self:FlushGuildSkillInfo(item.data)
end

--升级按钮回调
function GuildView:OnClickSkillUpLevel()
	if self.guild_skill_data == nil then
		return
	end

	local skill_level = GuildWGData.Instance:GetCurSkillLevel(self.guild_skill_data.skill_id)
	local skill_level_cfg = GuildWGData.Instance:GetCurSkillLevelCfg(self.guild_skill_data.skill_id, skill_level + 1)
	local item_id = 0
	local contribution = RoleWGData.Instance.role_vo.longhun
	if skill_level_cfg then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(skill_level_cfg.consume_item_id)
		if skill_level_cfg.contribution > contribution then
			item_id = GuildWGData.Instance:GetBnagGongItemId()
		elseif skill_level_cfg.consume_num > item_num then
			item_id = skill_level_cfg.consume_item_id
		end
		if item_id > 0 then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
			return
		end
	end
	self.show_effect = true
	AddDelayCall(self,function ()
		self.show_effect = false
		-- TipWGCtrl.Instance:ForceHideEffect()
	end,0.5)
	GuildWGCtrl.Instance:SendCSGuildSkillUpgradeReq(self.guild_skill_data.skill_id)
end

--问号帮助
function GuildView:OnClickSkilRule()
	RuleTip.Instance:SetContent(Language.GuildSkill.GuildSkillStatesTips, Language.GuildSkill.GuildSkillTips)
end


--刷新技能列表
function GuildView:FlushGuildSkillList(data_list)
	if data_list == nil then
		return
	end

	if self.skill_group then
		for k, v in pairs(self.skill_group) do
			v:Flush()  --执行刷新逻辑

			if self.cur_page_index == v.index then
				v:JumpToRedItem()
			end
		end
	end
end

function GuildView:FlushGuildSkillToggleInfo()
	for i = 1, SKILL_PAGE_COUNT do
		self.name_list[i].text.text = Language.Guild.ToggleName[i]
		self.name_hl_list[i].text.text = Language.Guild.ToggleName[i]
		self.remind_list[i]:SetActive(false)
		local skill_data = GuildWGData.Instance:GetCurStageSkillCfg(i) --获取阶段技能信息
		for k,v in pairs(skill_data) do
			local is_can_uplevel = GuildWGData.Instance:CurSkillIsCanUpLevel(v.skill_id)
			if is_can_uplevel then
				self.remind_list[i]:SetActive(true)
				break
			end	
		end
	end
end

--刷新技能信息
function GuildView:FlushGuildSkillInfo(data)
	if data == nil then
		return
	end

	-- 战力
	local power = GuildWGData.Instance:GetGuildSkillAllPower()
	--self.node_list["lbl_xm_power"].text.text = power
	--单个技能信息
	local skill_id = data.skill_id
	local is_show, is_open = GuildWGData.Instance:GetCurSkillOpen(skill_id)
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildSkill.SkillShowHint)
		return
	end
	local skill_level =  GuildWGData.Instance:GetCurSkillLevel(skill_id)
	self.node_list["skill_name"].text.text = data.skill_name
	self.node_list["skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(data.skill_icon))
	self.node_list["skill_level"].text.text = skill_level .. Language.Tip.Ji
	--技能属性信息
	self:UpdateSkillAttrInfo(skill_id, skill_level, data)
end

--太长了不重写了
function GuildView:UpdateSkillAttrInfo(skill_id, skill_level, cell_data)
	if skill_id == nil or skill_level == nil then
		return
	end

	local skill_level_cur_cfg = GuildWGData.Instance:GetCurSkillLevelCfg(skill_id, skill_level)
	local skill_level_next_cfg = GuildWGData.Instance:GetCurSkillLevelCfg(skill_id, skill_level + 1)
	self.node_list["skill_attr_arrow"]:SetActive(nil ~= skill_level_next_cfg)
	self.node_list["skill_attr_next_value"]:SetActive(nil ~= skill_level_next_cfg)
	self.node_list["skill_attr_name"].text.text = cell_data.skill_name
	if skill_level == 0 and skill_level_next_cfg then
		local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrId(skill_level_next_cfg.attr_type)
		local value_data = not is_per and skill_level_next_cfg.attr_value or (skill_level_next_cfg.attr_value / 100) .. "%"
		local value_str = value_data
		self.node_list["skill_attr_cur_value"].text.text = "0" 
		self.node_list["skill_attr_next_value"].text.text = value_str
		self:SetCellItem(skill_level_next_cfg,skill_level)
		self:SetSkillUpContion(skill_level_next_cfg,cell_data)	
	else
		if skill_level_next_cfg then
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrId(skill_level_next_cfg.attr_type)
			local cur_value = not is_per and skill_level_cur_cfg.attr_value or (skill_level_cur_cfg.attr_value / 100) .. "%"
			local cur_str = cur_value
			local next_value = not is_per and skill_level_next_cfg.attr_value or (skill_level_next_cfg.attr_value / 100) .. "%"
			local next_str = not is_per and (next_value - cur_value) or ((skill_level_next_cfg.attr_value - skill_level_cur_cfg.attr_value) / 100) .. "%"
			self.node_list["skill_attr_cur_value"].text.text = cur_str 
			self.node_list["skill_attr_next_value"].text.text = next_str
			self:SetCellItem(skill_level_next_cfg,skill_level)
			self:SetSkillUpContion(skill_level_next_cfg,cell_data)
		elseif skill_level_cur_cfg then
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrId(skill_level_cur_cfg.attr_type)
			local cur_value = not is_per and skill_level_cur_cfg.attr_value or (skill_level_cur_cfg.attr_value / 100) .. "%"
			local cur_str = cur_value
			self.node_list["skill_attr_cur_value"].text.text = cur_str 
			self.node_list["skill_attr_next_value"].text.text = ""
			self:SetCellItem(skill_level_cur_cfg,skill_level)
			self:SetSkillUpContion(skill_level_cur_cfg,cell_data)
		end
	end
	self:SetUpLevelEffect(cell_data.skill_id,skill_level) --升级成功特效
end

function GuildView:SetUpLevelEffect(skill_id,skill_level)
	if self.show_effect then
		if self.cur_skill_id and self.cur_skill_id == skill_id and self.cur_level ~= nil and self.cur_level < skill_level then
			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["effect"]})
		end
	end
	self.cur_level = skill_level
	self.cur_skill_id = skill_id
end

--设置格子数据
function GuildView:SetCellItem(data,skill_level)
	local skill_cfg = GuildWGData.Instance:GetCurOneSkillCfg(data.skill_id)
	if skill_cfg and skill_cfg.skill_max_level == skill_level then
		self.node_list["cell_parent"]:SetActive(false)
		return
	else
		self.node_list["cell_parent"]:SetActive(true)
	end
	self.node_list["ph_cell_1"]:SetActive(data.contribution > 0)
	-- self.node_list["ph_cell_2"]:SetActive(data.consume_item_id > 0)
	if data.contribution > 0 then
		local item_id = GuildWGData.Instance:GetBnagGongItemId()
		self.guild_skill_cell:SetData({item_id = item_id})
		-- self.guild_skill_cell:SetQualityIconVisible(false)
		-- self.guild_skill_cell:SetCellBgEnabled(false)
		-- self.guild_skill_cell:SetDefaultEff(false)

		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		self.node_list.ph_cell_name_text.text.text = item_cfg and item_cfg.name or ""

		local contribution = RoleWGData.Instance.role_vo.longhun
		local color = contribution >= data.contribution and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		self.node_list.ph_cell_comuse_text.text.text = ToColorStr(CommonDataManager.ConverExp(contribution) .. "/" .. CommonDataManager.ConverExp(data.contribution) ,color)
	end
end

function GuildView:SetSkillUpContion(data, cell_data)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local guild_level = GuildDataConst.GUILDVO.guild_level
	local _,is_open = GuildWGData.Instance:GetCurSkillOpen(cell_data.skill_id)
	local is_button = true
	-- self.node_list["need_1"]:SetActive(data.role_level_limit > 0)
	self.node_list["need_1"]:SetActive(false) --人物等级
	self.node_list["need_3"]:SetActive(false) --宗门等级
	self.node_list["need_2"]:SetActive(false) --前置技能
	local color = role_level >=  data.role_level_limit and COLOR3B.GREEN or COLOR3B.RED
	if role_level < data.role_level_limit or  guild_level < data.guild_level_limit or not is_open then
		is_button = false
	end
	self.node_list["need_role_level"].text.text = ToColorStr(role_level.."/"..data.role_level_limit,color)
	if data.pre_skill_id > 0 then
		local pre_skill_level = GuildWGData.Instance:GetCurSkillLevel(data.pre_skill_id)
		local color1 = pre_skill_level >=  data.pre_skill_level and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		if is_button and data.pre_skill_level > pre_skill_level then
			is_button = false
		end
		local pre_skill_cfg = GuildWGData.Instance:GetCurOneSkillCfg(data.pre_skill_id)
		local color_str = ToColorStr(pre_skill_level, color1)
		local str = string.format(Language.GuildSkill.GuildLimit, pre_skill_cfg.skill_name, color_str, data.pre_skill_level)
		self.node_list["need_skill"].text.text = str
		self.node_list["need_2"]:SetActive(pre_skill_level < data.pre_skill_level)
	else
		self.node_list["need_2"]:SetActive(false)
	end

	local color2 = guild_level >=  data.guild_level_limit and COLOR3B.GREEN or COLOR3B.RED
	self.node_list["need_guild_level"].text.text = ToColorStr(guild_level.."/"..data.guild_level_limit,color2)
	local skill_level = GuildWGData.Instance:GetCurSkillLevel(cell_data.skill_id)
	if is_button and skill_level >= cell_data.skill_max_level then
		is_button = false
		self.node_list["img_ymj"]:SetActive(true)
		self.node_list["btn_uplevel"]:SetActive(false)
	else
		self.node_list["img_ymj"]:SetActive(false)
		self.node_list["btn_uplevel"]:SetActive(true)
	end
	XUI.SetButtonEnabled(self.node_list["btn_uplevel"], is_button)--设置按钮不可用,置灰
end

--协议刷新
function GuildView:OnFlushGuildSkillView()
	local skill_info = GuildWGData.Instance:GetSkillStageInfo() --获取全部修炼技能数据
	self:FlushGuildSkillInfo(self.guild_skill_data)
	self:FlushGuildSkillList(skill_info)
	self:FlushGuildSkillToggleInfo()
end

function GuildView:SetSkillItemSelect(item)
	if nil ~= self.select_skill_item then
		self.select_skill_item:CancelSelect()
	end
	self.select_skill_item = item
	self.select_skill_item:Select()
end

function GuildView:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "longhun" then
        self:OnFlushGuildSkillView()
	end
end

----------------------------------------------------------------------------------------------------------------
GuildSkillGroupItem = GuildSkillGroupItem or BaseClass(BaseRender)
function GuildSkillGroupItem:__init()
	--修改
	self.skill_item_list = {}

	self.is_load = false
	self.item_list = {}
	self.select_skill_list_index = -1
end

--修改
function GuildSkillGroupItem:__delete()
	if self.skill_item_list then
		for k,v in pairs(self.skill_item_list) do
			v:DeleteMe()
		end
	end
    self.skill_item_list = nil

	self.item_list = nil
    self.parent = nil

    if nil ~= self.skill_list then
		for i,v in ipairs(self.skill_list) do
			v:DeleteMe()
		end
		self.skill_list = nil
    end
end

function GuildSkillGroupItem:LoadCallBack()
    if not self.skill_list then
        self.skill_list = {}
        for i = 1, 6 do
            self.skill_list[i] = GuildSkillItemCell.New(self.node_list["ph_guild_skill_cell_" .. i])
            self.skill_list[i]:SetIndex(i)
        end
    end
end

function GuildSkillGroupItem:OnFlush()
	---------------------------修改
	if nil == self.data or nil == self.data.skill_data or IsEmptyTable(self.data.skill_data) then
		return
	end
	local guild_level = GuildDataConst.GUILDVO.guild_level --宗门等级
	local skill_table = GuildWGData.Instance:GetSkillStageInfo() --技能阶段

	local show_bundle, show_name = ResPath.GetGuildSystemImage("a3_xm_tu" .. self.data.data_index)
	self.node_list["skill_icon_bg"].image:LoadSpriteAsync(show_bundle, show_name, function()
		self.node_list["skill_icon_bg"].image:SetNativeSize()
 	end)

	if skill_table and skill_table[self.data.data_index] then
		self.node_list["title"].text.text = skill_table[self.data.data_index].skill_stage_name

		if guild_level < skill_table[self.data.data_index].open_guild_level then
			self.node_list["lock_bg"]:SetActive(true)
			self.node_list["lock_name"].text.text = string.format(Language.GuildSkill.SkillState4, skill_table[self.data.data_index].open_guild_level)  --仙盟几级开启
		else
			self.node_list["lock_bg"]:SetActive(false)
		end
	end

	if not self.is_load then
		for i = 1, 6 do
			self.skill_list[i]:SetParent(self.parent)
			self.skill_list[i]:SetPageParent(self)
		end
		self.is_load = true
	end

	for i = 1, #self.skill_list do
		if self.data.skill_data[i] then
            self.skill_list[i]:SetActive(true)
            self.skill_list[i]:SetData(self.data.skill_data[i])
        else
            self.skill_list[i]:SetActive(false)
        end
	end
end

function GuildSkillGroupItem:SetParent(parent)
	self.parent = parent
end

function GuildSkillGroupItem:ActiveFirstItem()
	if self.skill_list and self.skill_list[1] then
		self.skill_list[1]:OnClickSkill()
	end
end

function GuildSkillGroupItem:SetSelectSkillListIndex(index)
	self.select_skill_list_index = index
end

function GuildSkillGroupItem:JumpToRedItem()
	if self.select_skill_list_index and self.select_skill_list_index > 0 then
		local skill_id = (((self.data or {}).skill_data or {})[self.select_skill_list_index] or {}).skill_id or 0
		
		if skill_id > 0 then
			local is_can_uplevel = GuildWGData.Instance:CurSkillIsCanUpLevel(skill_id)

			if is_can_uplevel then
				self.skill_list[self.select_skill_list_index]:OnClickSkill()
				return
			end
		end
	end

	if not IsEmptyTable(self.skill_list) then
		for k, v in pairs(self.skill_list) do
			local skill_id = (((self.data or {}).skill_data or {})[k] or {}).skill_id or 0

			if skill_id > 0 then
				local is_can_uplevel = GuildWGData.Instance:CurSkillIsCanUpLevel(skill_id)

				if is_can_uplevel then
					v:OnClickSkill()
					return
				end
			end
		end
	end

	local default_select_index = self.select_skill_list_index and self.select_skill_list_index > 0 and self.select_skill_list_index or 1
	self.skill_list[default_select_index]:OnClickSkill()
end

--------------------------------------------------------------------------------------
GuildSkillItemCell = GuildSkillItemCell or BaseClass(BaseRender)

function GuildSkillItemCell:__init()
	-- body
end

function GuildSkillItemCell:__delete()
    self.parent = nil
	self.page_parent = nil
end

function GuildSkillItemCell:OnFlush()
	if not self.data then
		return
	end
	local is_show,is_open = GuildWGData.Instance:GetCurSkillOpen(self.data.skill_id)
	local old_open_state, old_skill_level = GuildWGData.Instance:GetEnterViewJiHuoSkillState(self.data.skill_id)
	local skill_bundle, skill_asset = ResPath.GetSkillIconById(self.data.skill_icon)
	self.node_list["ph_xm_skill_icon"].image:LoadSprite(skill_bundle, skill_asset, function()
		self.node_list["ph_xm_skill_icon"].image:SetNativeSize()
	end)
	self.node_list["lbl_xm_skill_name"].text.text = self.data.skill_name --技能名字
	local skill_level = GuildWGData.Instance:GetCurSkillLevel(self.data.skill_id)
	self.node_list["lbl_xm_skill_level"].text.text = skill_level  --技能等级

	local skill_level_cur_cfg = GuildWGData.Instance:GetCurSkillLevelCfg(self.data.skill_id, skill_level)  --当前等级的数据
	local skill_level_next_cfg = GuildWGData.Instance:GetCurSkillLevelCfg(self.data.skill_id, skill_level + 1) --下级等级的数据
	if skill_level == 0 then
		skill_level_cur_cfg = GuildWGData.Instance:GetCurSkillLevelCfg(self.data.skill_id, skill_level + 1)
	end
	local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrId(skill_level_cur_cfg.attr_type)
	local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(skill_level_cur_cfg.attr_type)
	local attr_val = not is_per and skill_level_cur_cfg.attr_value or (skill_level_cur_cfg.attr_value/100).."%"
	self.node_list["lbl_xm_skill_effect"].text.text = string.format(Language.GuildSkill.GuildSkillDesc, attr_name, skill_level == 0 and 0 or attr_val)
	self.node_list["ph_limit"]:SetActive(false)
	-- self.node_list["lbl_xm_skill_level"]:SetActive(is_open)
	self.node_list["img_lock"]:SetActive(not is_open)
	if is_open then --是否解锁
		self:UpdateLimitInfo(skill_level)
	end

	local is_can_uplevel = GuildWGData.Instance:CurSkillIsCanUpLevel(self.data.skill_id)
	self.node_list["img_remind"]:SetActive(is_can_uplevel)
	self.node_list["ph_guild_skill_cell"].button:AddClickListener(BindTool.Bind1(self.OnClickSkill, self))
end

function GuildSkillItemCell:UpdateLimitInfo(skill_level)
	self.node_list["lbl_xm_role_limit"]:SetActive(false)
	self.node_list["lbl_xm_pre_skill_id"]:SetActive(false)
	self.node_list["lbl_xm_level_limit"]:SetActive(false)
	local skill_level_cur_cfg = GuildWGData.Instance:GetCurSkillLevelCfg(self.data.skill_id, skill_level + 1)

	if skill_level_cur_cfg == nil then
		self.node_list["ph_limit"]:CustomSetActive(false)
		return
	end
	--升级条件
	local role_level = RoleWGData.Instance:GetRoleLevel()
	--角色等级
	local role_level_limit = skill_level_cur_cfg.role_level_limit

	self.node_list["lbl_xm_role_limit"]:CustomSetActive(role_level_limit ~= 0 and role_level < role_level_limit)
	self.node_list["lbl_xm_role_limit"].text.text = string.format(Language.GuildSkill.GuildRoleLevelLimit, role_level_limit)
	--前置等级
	local pre_skill_level = GuildWGData.Instance:GetCurSkillLevel(skill_level_cur_cfg.pre_skill_id)
	local pre_skill_level_limit = skill_level_cur_cfg.pre_skill_level
	self.node_list["lbl_xm_pre_skill_id"]:CustomSetActive(pre_skill_level < pre_skill_level_limit and pre_skill_level_limit ~= 0)
	if pre_skill_level_limit > 0 then
		local pre_skill_cfg = GuildWGData.Instance:GetCurOneSkillCfg(skill_level_cur_cfg.pre_skill_id)
		self.node_list["lbl_xm_pre_skill_id"].text.text = string.format(Language.GuildSkill.GuildPreSkillLimit, pre_skill_cfg.skill_name, pre_skill_level_limit)
	end
	--仙盟等级
	local guild_level = GuildDataConst.GUILDVO.guild_level
	local guild_level_limit = skill_level_cur_cfg.guild_level_limit
	self.node_list["lbl_xm_level_limit"]:CustomSetActive(guild_level < guild_level_limit and guild_level_limit ~= 0)
	self.node_list["lbl_xm_level_limit"].text.text = string.format(Language.GuildSkill.GuildLevelLimit, guild_level_limit)
end

function GuildSkillItemCell:FlushSelect(index)
	self.node_list["ph_xm_skill_hl"]:SetActive(index == self.index)
end

function GuildSkillItemCell:OnClickSkill()
	if self.parent then
		self.parent:OnGuildSkillItemHandler(self)
		self.parent:SetSkillItemSelect(self)
	end

	if self.page_parent then
		self.page_parent:SetSelectSkillListIndex(self.index)
	end
end

function GuildSkillItemCell:Select()
	self.node_list["ph_xm_skill_hl"]:SetActive(true)
end

function GuildSkillItemCell:CancelSelect()
	self.node_list["ph_xm_skill_hl"]:SetActive(false)
end

function GuildSkillItemCell:SetParent(parent)
	self.parent = parent
end

function GuildSkillItemCell:SetPageParent(parent)
	self.page_parent = parent
end
