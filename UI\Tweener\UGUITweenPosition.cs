﻿using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("UGUI/Tween/UGUI Tween Position")]
[RequireComponent(typeof(RectTransform))]
public class UGUITweenPosition : UGUITweener 
{
	public Vector3 from;
	public Vector3 to;

	[Header("表示的是否为世界坐标移动")]
	public bool worldSpace = false;

	[<PERSON>er("表示是否为相对位置移动")]
	public bool anchor = false;

	bool mCached = false;
	RectTransform mTrans;
	void Cache ()
	{
		mCached = true;
		mTrans = GetComponent<RectTransform>();
	}

	public Vector3 value
	{
		get
		{
			if (!mCached) Cache();
			if(worldSpace && anchor)
				return mTrans.anchoredPosition3D;
			else if (worldSpace)
				return mTrans.transform.position;
			else if (anchor)
				return mTrans.anchoredPosition;
			else
				return mTrans.transform.localPosition;
		}
		set
		{
			if (!mCached) Cache();
			if(worldSpace && anchor)
				mTrans.anchoredPosition3D = value;
			else if (worldSpace)
				mTrans.transform.position = value;
			else if (anchor)
				mTrans.anchoredPosition = value;
			else
				mTrans.transform.localPosition = value;
		}
	}

	protected override void OnUpdate (float factor, bool isFinished) { value = from * (1f - factor) + to * factor; }

	static public UGUITweenPosition Begin (GameObject go, float duration, Vector3 pos, bool worldSpace = false, bool anchor = false)
	{
		UGUITweenPosition comp = UGUITweener.Begin<UGUITweenPosition>(go, duration);
		comp.worldSpace = worldSpace;
		comp.anchor = anchor;
		comp.from = comp.value;
		comp.to = pos;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	[ContextMenu("Set 'From' to current value")]
	public override void SetStartToCurrentValue () { from = value; }

	[ContextMenu("Set 'To' to current value")]
	public override void SetEndToCurrentValue () { to = value; }

	[ContextMenu("Assume value of 'From'")]
	void SetCurrentValueToStart () { value = from; }

	[ContextMenu("Assume value of 'To'")]
	void SetCurrentValueToEnd () { value = to; }
}
