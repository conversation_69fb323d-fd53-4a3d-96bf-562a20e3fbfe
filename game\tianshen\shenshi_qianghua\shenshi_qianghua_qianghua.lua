-- 天神神饰-强化-强化
function ShenShiQiangHuaView:QiangHuaReleaseCallBack()
	self.equip_list_view = nil
	if self.equip_cell_list then
		for k,v in pairs(self.equip_cell_list) do
			v:DeleteMe()
		end
	end
	self.equip_cell_list = {}

	if self.qianghua_item then
		self.qianghua_item:DeleteMe()
		self.qianghua_item = nil
	end

	if self.jingjie_item1 then
		self.jingjie_item1:DeleteMe()
		self.jingjie_item1 = nil
	end

	if self.jingjie_item2 then
		self.jingjie_item2:DeleteMe()
		self.jingjie_item2 = nil
	end

	if self.jingjie_item3 then
		self.jingjie_item3:DeleteMe()
		self.jingjie_item3 = nil
	end

	if self.jingjie_item4 then
		self.jingjie_item4:DeleteMe()
		self.jingjie_item4 = nil
	end

	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	self.item_callback = nil
	self.start_upgrade = nil
	self.qianghua_curr_select_item = nil
	self.equip_list = nil
	self.is_one_key = false
	self.shenshi_index = nil
end

function ShenShiQiangHuaView:QiangHuaCloseCallBack()
	if self.qianghua_curr_select_item then
		self.qianghua_curr_select_item:SetSelect(false)
	end
	self.qianghua_curr_select_item = nil
	self:QiangHuaRemoveTimeQuest(true)
end

function ShenShiQiangHuaView:QiangHuaShowIndexCallBack()
	self:FlushRedDot()
end

function ShenShiQiangHuaView:QiangHuaLoadCallBack()
	self.item_callback = BindTool.Bind(self.OnItemCallBack, self)
	self.start_upgrade = BindTool.Bind(self.StartUpgrade, self)
	self.equip_cell_list = {}
	self.qianghua_item = nil
	self.qianghua_curr_select_item = nil 																	-- 选中的装备
	self.equip_list = nil
	self.is_one_key = false
	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)

	self.shenshi_index = 0

	self.qianghua_item = ItemCell.New(self.node_list["item_pos"])

	self.jingjie_item1 = ItemCell.New(self.node_list["go_jingjie_pos1"])
	self.jingjie_item2 = ItemCell.New(self.node_list["go_jingjie_pos2"])
	self.jingjie_item3 = ItemCell.New(self.node_list["go_jingjie_pos3"])
	self.jingjie_item3:SetNeedItemGetWay(true)
	self.jingjie_item4 = ItemCell.New(self.node_list["go_jingjie_pos4"])
	self.jingjie_item4:SetNeedItemGetWay(true)

	self.equip_list_view = self.node_list["ph_btn_listview"]
	local list_delegate = self.equip_list_view.list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshCell, self)

	XUI.AddClickEventListener(self.node_list["btn_upgrade"], BindTool.Bind(self.OnClickUpgrade, self))
	XUI.AddClickEventListener(self.node_list["btn_quickly_upgrade"], BindTool.Bind(self.OnClickQuickly, self))
	XUI.AddClickEventListener(self.node_list["btn_shengjie"], BindTool.Bind(self.OnClickShengJie, self))
end

function ShenShiQiangHuaView:OnClickShengJie()
	if not self.qianghua_curr_select_item or not self.qianghua_curr_select_item:GetData() then return end
	local data = self.qianghua_curr_select_item:GetData()
	local upgrade_cfg = TianShenWGData.Instance.tianshen_cfg_auto.equip_upgrade[data.grade_level]
	local other = TianShenWGData.Instance.tianshen_cfg_auto.other[1]
	if not upgrade_cfg then return end
	local data = self.qianghua_curr_select_item:GetData()
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other.upgrade_item_id)
	if item_num < upgrade_cfg.cost then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other.upgrade_item_id})
		return
	end

	item_num = ItemWGData.Instance:GetItemNumInBagById(other.upgrade_item_id2)
	if item_num < upgrade_cfg.cost2 then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other.upgrade_item_id2})
		return
	end

	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type9, self.select_tainshen_cfg.index, TianShenWGData.Equip_Pos[item_cfg.sub_type])
end

function ShenShiQiangHuaView:OnClickUpgrade()
	self:StartUpgrade(true)
end

function ShenShiQiangHuaView:OnClickQuickly()
	self.is_one_key = not self.is_one_key
	self:QiangHuaRemoveTimeQuest()

	if self.is_one_key then
		self:StartUpgrade()
	end
end

function ShenShiQiangHuaView:QiangHuaRemoveTimeQuest(is_clear)
	if is_clear then
		self.is_one_key = false
	end
	if not self.node_list["one_key_upstar_title"] then
		return
	end

	if self.is_one_key then
		self.node_list["one_key_upstar_title"].text.text = Language.TianShen.OneKeyBtnText[2]
	else
		self.node_list["one_key_upstar_title"].text.text = Language.TianShen.OneKeyBtnText[9]
	end
end

function ShenShiQiangHuaView:StartUpgrade(is_upgrade)
	if not self.qianghua_curr_select_item or not self.select_tainshen_cfg then
		self:QiangHuaRemoveTimeQuest(true)
		return
	end
	if not self.is_one_key and not is_upgrade then return end

	local data = self.qianghua_curr_select_item:GetData()

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then return end
	if item_cfg.color < GameEnum.ITEM_COLOR_PURPLE then
		self:QiangHuaRemoveTimeQuest(true)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.QianHuaTips2)
		return
	end

	local tianshen_info = TianShenWGData.Instance:TianShenInfo()
	local equip_strange_cfg = TianShenWGData.Instance.shenshi_equip_strange_cfg[data.stren_level]
	if tianshen_info.jinghua < equip_strange_cfg.cost then
		if self.is_one_key then
			self:QiangHuaRemoveTimeQuest(true)
		end
		local other = TianShenWGData.Instance.tianshen_cfg_auto.other[1]
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other.essence_item_id})
		return
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then
		self:QiangHuaRemoveTimeQuest(true)
		return
	end

	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type8, self.select_tainshen_cfg.index, TianShenWGData.Equip_Pos[item_cfg.sub_type])
end

function ShenShiQiangHuaView:FlushUpgradeSlider()
	if not self.select_tainshen_cfg then return end
	if not self.qianghua_curr_select_item or not self.qianghua_curr_select_item:GetData() then return end

	local cur_data = self.qianghua_curr_select_item:GetData()
	local old_data = TianShenWGData.Instance:GetOldTianShenEquip(self.select_tainshen_cfg.index, cur_data.bag_index)
	local cur_exp = cur_data.stren_exp
	local cur_level = cur_data.stren_level
	local old_exp = old_data.stren_exp
	local old_level = old_data.stren_level
	local level_add = cur_level - old_level

	local equip_strange_cfg = TianShenWGData.Instance.shenshi_equip_strange_cfg[cur_data.stren_level]
	local next_strange_cfg = TianShenWGData.Instance.shenshi_equip_strange_cfg[cur_data.stren_level + 1]

	local add_exp_num = 0
	for i=1,level_add do
		add_exp_num = add_exp_num + TianShenWGData.Instance.shenshi_equip_strange_cfg[old_level + i - 1].all_need
	end
	add_exp_num = add_exp_num - old_exp + cur_exp

	local need_exp = equip_strange_cfg.all_need

	self:QiangHuaPlayAni(need_exp,next_strange_cfg == nil,level_add,old_level,cur_exp,add_exp_num)

	--刷新进度条
	local is_max_level = next_strange_cfg == nil

	if not is_max_level then
		self.node_list["lbl_upstar_progress_num"].text.text = cur_data.stren_exp .. "/" .. need_exp
	else
		self.node_list["lbl_upstar_progress_num"].text.text = "-- / --"
		self.node_list["strength_num"].text.text = "-- / --"
	end

end

-- 播放动画
function ShenShiQiangHuaView:QiangHuaPlayAni(total_exp,is_max_level,level_add,slider_level,exp,add_exp_num)
	if self.is_play_ani then return end
	self.is_play_ani = true
	local add_value = 1
	if 0 ~= total_exp then
		add_value = level_add + exp/total_exp
	end

	if not self.first_tween and add_exp_num and add_exp_num > 0 then
		TipWGCtrl.Instance:ShowNumberMsg(string.format(Language.Bag.GetRoleItemTxt2, add_exp_num), nil, Vector2(560,62))
	end
	self.first_tween = false

	if is_max_level then
		local tween_max = self.node_list["Slider"].slider:DOValue(1,1/2)
		tween_max:SetId('slider_tween')
		tween_max:OnComplete(function ()
			self.is_play_ani = false
			self.is_one_key = false
			self:StartUpgrade()
		end)
		return
	end

	self.fun = function (add_value)
		if add_value <= 0 then
			self.is_play_ani = false
			self:StartUpgrade()
			return
		end
		local tween
		if add_value > 1 then
			local value = 1 - self.node_list["Slider"].slider.value
			tween = self.node_list["Slider"].slider:DOValue(1,value/2)
		else
			local value = add_value - self.node_list["Slider"].slider.value
			tween = self.node_list["Slider"].slider:DOValue(add_value,value/2)
		end
		tween:SetId('slider_tween')
		add_value = add_value - 1
		tween:OnComplete(function ()
			if add_value >= 0 then
				self.node_list["Slider"].slider.value = 0
				slider_level = slider_level + 1
				local effect_name = UIEffectName.s_qianghua
				TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["effect_container"]})
            end
			if add_value < 1 then
			 	MainuiWGCtrl.Instance:DelayShowCachePower(0) --延时调用
			end
			self.fun(add_value)
		end)
	end
	self.fun(add_value)
end

--物品变化
function ShenShiQiangHuaView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	self:FlushConsume()
end

function ShenShiQiangHuaView:FlushConsume()
	-- body
	if not self.qianghua_curr_select_item or not self.qianghua_curr_select_item:GetData() then return end
	local data = self.qianghua_curr_select_item:GetData()

	local upgrade_cfg = TianShenWGData.Instance.tianshen_cfg_auto.equip_upgrade[data.grade_level]
	local other = TianShenWGData.Instance.tianshen_cfg_auto.other[1]
	if not upgrade_cfg then return end

	if upgrade_cfg.need_stren_level <= data.stren_level then
		self.jingjie_item3:SetRightBottomTextVisible(true)
		local item_num = ItemWGData.Instance:GetItemNumInBagById(other.upgrade_item_id)
		local str = item_num .. " / " .. upgrade_cfg.cost
		if item_num < upgrade_cfg.cost then
			str = ToColorStr(str, COLOR3B.RED)
		else
			str = ToColorStr(str, COLOR3B.GREEN)
		end
		self.node_list["jinjie_stuff_num_1"].text.text = str

		self.node_list["go_jingjie_pos4"]:SetActive(0 < upgrade_cfg.cost2)
		if 0 < upgrade_cfg.cost2 then
			self.jingjie_item4:SetRightBottomTextVisible(true)
			item_num = ItemWGData.Instance:GetItemNumInBagById(other.upgrade_item_id2)
			str = item_num .. " / " .. upgrade_cfg.cost2
			if item_num < upgrade_cfg.cost2 then
				str = ToColorStr(str, COLOR3B.RED)
			else
				str = ToColorStr(str, COLOR3B.GREEN)
			end
			self.node_list["jinjie_stuff_num_2"].text.text = str
		end
	end
end

function ShenShiQiangHuaView:GetNumberOfCells()
	return self.equip_list and #self.equip_list or 0
end

function ShenShiQiangHuaView:RefreshCell(cell, cell_index)
	local item_cell = self.equip_cell_list[cell]

	if item_cell == nil then
		item_cell = TianShenQiangHuaItemRender.New(cell.gameObject, self)
		item_cell:ClickCallBack(self.item_callback)
		self.equip_cell_list[cell] = item_cell
	end

	item_cell:SetIndex(cell_index)

	if self.equip_list then
		item_cell:SetData(self.equip_list[cell_index + 1])
		if self.shenshi_index == cell_index and self.qianghua_curr_select_item == nil then
			self:OnItemCallBack(item_cell)
		end
	end
end

function ShenShiQiangHuaView:FlushRedDot()
	-- body
	for k,v in pairs(self.equip_cell_list) do
		v:Flush()
	end
end

function ShenShiQiangHuaView:OnItemCallBack(item, refresh)
	if not item then
		return
	end

	if self.qianghua_curr_select_item == item and not refresh then
		return
	end

	if self.qianghua_curr_select_item then
		self.qianghua_curr_select_item:SetSelect(false)
	end

	self.qianghua_curr_select_item = item
	self.qianghua_curr_select_item:SetSelect(true)

	local data = item:GetData()
	if not refresh then
		self:QiangHuaRemoveTimeQuest(true)
	end

	local upgrade_cfg = TianShenWGData.Instance.tianshen_cfg_auto.equip_upgrade[data.grade_level]
	if not upgrade_cfg then return end

	if 0 == upgrade_cfg.need_stren_level or upgrade_cfg.need_stren_level > data.stren_level then
		self.node_list["go_qianghua"]:SetActive(true)
		self.node_list["go_jingjie"]:SetActive(false)
		self:FlushQiangHua(data, refresh)
	else
		self.node_list["go_qianghua"]:SetActive(false)
		self.node_list["go_jingjie"]:SetActive(true)
		self:FlushJingJie(data)
		self:QiangHuaRemoveTimeQuest(true)
	end

end

function ShenShiQiangHuaView:FlushQiangHua(data, refresh)
	-- body
	if not data then return end
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then return end
	local tianshen_info = TianShenWGData.Instance:TianShenInfo()

	self.qianghua_item:SetData(data)--({item_id = data.item_id})
	self.qianghua_item:SetCellBgEnabled(false)
	self.qianghua_item:SetQualityIconVisible(false)
    self.qianghua_item:SetDefaultEff(false)
    self.qianghua_item:SetRightTopImageTextActive(false)
    self.qianghua_item:SetLeftTopImg(0)
   	self.qianghua_item:SetRightBottomTextVisible(false)
   	self.qianghua_item:SetBindIconVisible(false)
	local item_name = ItemWGData.Instance:GetItemName(data.item_id)
	self.node_list["qianghua_item_name_1"].text.text = item_name 
	self.node_list["qianghua_item_name_2"].text.text = item_name 

	local upgrade_cfg = TianShenWGData.Instance.tianshen_cfg_auto.equip_upgrade[data.grade_level]
	local next_upgrade_cfg = TianShenWGData.Instance.tianshen_cfg_auto.equip_upgrade[data.grade_level + 1]
	if not upgrade_cfg then return end

	local str = nil
	if not next_upgrade_cfg then
		str = Language.TianShen.QianHuaTips12
	else
		str = string.format(Language.TianShen.QianHuaTips3, upgrade_cfg.need_stren_level)
	end
	self.node_list["text_qianghua"].text.text = string.format(str)

	local equip_strange_cfg = TianShenWGData.Instance.shenshi_equip_strange_cfg[data.stren_level]
	local next_equip_strange_cfg = TianShenWGData.Instance.shenshi_equip_strange_cfg[data.stren_level + 1]
	local strange_attr = TianShenWGData.Instance:GetEquipStrangeAttr(item_cfg.sub_type, data.stren_level)
	local next_strange_attr = TianShenWGData.Instance:GetEquipStrangeAttr(item_cfg.sub_type, data.stren_level + 1)
	if not equip_strange_cfg then return end

	self.node_list["cur_strength_level"].text.text = data.stren_level
	if next_equip_strange_cfg then
		local strength_num = tianshen_info.jinghua .. "/" .. equip_strange_cfg.cost
		if tianshen_info.jinghua >= equip_strange_cfg.cost then
			self.node_list["strength_num"].text.text = ToColorStr(strength_num, COLOR3B.GREEN)
		else
			self.node_list["strength_num"].text.text = ToColorStr(strength_num, COLOR3B.RED)
		end
		self.node_list["next_strength_level"].text.text = data.stren_level + 1
		self.node_list["btn_upgrade"]:SetActive(true)
		self.node_list["btn_quickly_upgrade"]:SetActive(true)
		self.node_list["btn_maxlevel"]:SetActive(false)
	else
		self.node_list["strength_num"].text.text = "-- / --"
		self.node_list["next_strength_level"].text.text = "MAX"
		self.node_list["btn_upgrade"]:SetActive(false)
		self.node_list["btn_quickly_upgrade"]:SetActive(false)
		self.node_list["btn_maxlevel"]:SetActive(true)
		XUI.SetButtonEnabled(self.node_list["btn_maxlevel"], false)
	end

	local attr_str = nil
	local attr_cfg = strange_attr or next_strange_attr or {}
	local attr_name = {"gongji", "pojia", "shengming_max", "fangyu", "yuansu_hj", "yuansu_sh"}
	for i=1,2 do
		attr_str = nil
		for j=1,#attr_name do
			if attr_cfg[attr_name[j]] and 0 < attr_cfg[attr_name[j]] then
				attr_str = attr_name[j]
				table.remove(attr_name, j)
				break
			end
		end
		if attr_str then
			self.node_list["lbl_attr_name_" .. i].text.text = Language.TianShen.AttrName[attr_str] .. (strange_attr and strange_attr[attr_str] or 0)
			if next_strange_attr then
				self.node_list["lbl_attr_value_" .. i].text.text = next_strange_attr[attr_str]
			else
				self.node_list["lbl_attr_value_" .. i].text.text = "MAX"
			end
		end
	end

	if not refresh then
		if next_strange_attr then
			self.node_list["lbl_upstar_progress_num"].text.text = data.stren_exp .. "/" .. equip_strange_cfg.all_need
		else
			self.node_list["lbl_upstar_progress_num"].text.text = "-- / --"
		end
		self.node_list["Slider"].slider.value = data.stren_exp / equip_strange_cfg.all_need
	else
		self:FlushUpgradeSlider()
	end
end

function ShenShiQiangHuaView:FlushJingJie(data)
	-- body
	if not data then return end
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then return end
	local upgrade_cfg = TianShenWGData.Instance.tianshen_cfg_auto.equip_upgrade[data.grade_level]
	local next_upgrade_cfg = TianShenWGData.Instance.tianshen_cfg_auto.equip_upgrade[data.grade_level + 1]
	local upgrade_attr = TianShenWGData.Instance:GetEquipUpgradeAttr(item_cfg.sub_type, item_cfg.color, data.star_level, data.grade_level)
	local next_upgrade_attr = TianShenWGData.Instance:GetEquipUpgradeAttr(item_cfg.sub_type, item_cfg.color, data.star_level, data.grade_level + 1)
	local other = TianShenWGData.Instance.tianshen_cfg_auto.other[1]

	if not upgrade_cfg then return end
	self.jingjie_item1:SetData(data)

	local color = ITEM_COLOR[item_cfg.color]
	self.node_list["text_jingjie_name1"].text.text = ToColorStr(item_cfg.name,color)
	self.node_list["text_jingjie_name2"].text.text = ToColorStr(item_cfg.name,color)

	local next_data = __TableCopy(data)
	if next_upgrade_cfg then
		next_data.grade_level = next_data.grade_level + 1
	end
	self.jingjie_item2:SetData(next_data)

	self.node_list["text_pinfen1"].text.text = TianShenWGData.Instance:SetEquipPingFen(data)
	self.node_list["text_pinfen2"].text.text = TianShenWGData.Instance:SetEquipPingFen(next_data)

	XUI.SetButtonEnabled(self.node_list["btn_shengjie"], nil ~= next_upgrade_cfg)

	self.jingjie_item3:SetData({item_id = other.upgrade_item_id})
	self.jingjie_item4:SetData({item_id = other.upgrade_item_id2})
	self:FlushConsume()

	local str = nil
	local attr_str = nil
	local attr_cfg = next_upgrade_attr or upgrade_attr or {}
	local attr_name = {"gongji", "pojia", "shengming_max", "fangyu", "yuansu_hj", "yuansu_sh"}
	for i=1,2 do
		attr_str = nil
		for j=1,#attr_name do
			if attr_cfg[attr_name[j]] and 0 < attr_cfg[attr_name[j]] then
				attr_str = attr_name[j]
				table.remove(attr_name, j)
				break
			end
		end

		if attr_str then
			str = nil
			if 1 == data.grade_level then
				local base_attri_butte = TianShenWGData.Instance:GetEquipBasicsAttr(item_cfg.id, data.star_level)
				local attr_value = base_attri_butte and base_attri_butte[attr_str] or 0
				-- 字段名字不同，转换一下
				if "shengming_max" == attr_str then
					attr_value = base_attri_butte and base_attri_butte["maxhp"] or 0
				end

				str = Language.Common.TipsAttrNameList[attr_str] .. " +" .. attr_value
			else
				str = Language.Common.TipsAttrNameList[attr_str] .. " +" .. (upgrade_attr and upgrade_attr[attr_str] or 0)
			end
			self.node_list["text_left_attr" .. i].text.text = str
			if next_upgrade_attr then
				self.node_list["text_right_attr" .. i].text.text = Language.Common.TipsAttrNameList[attr_str] .. " <color=#006a25>" .. (data.grade_level + 1) .. "阶+" .. next_upgrade_attr[attr_str] .. "</color>"
			else
				self.node_list["text_right_attr" .. i].text.text = Language.Common.TipsAttrNameList[attr_str] .. " <color=#006a25>MAX</color>"
			end
		end
	end

	local curr_jiping = TianShenWGData.Instance:GetEquipUpgradeAcuraAttr(item_cfg.sub_type, item_cfg.color, data.star_level, data.grade_level)
	local next_jiping = TianShenWGData.Instance:GetEquipUpgradeAcuraAttr(item_cfg.sub_type, item_cfg.color, data.star_level, data.grade_level + 1)
	local index = 3
	local attr_type = 0
	str = nil
	for i=1,#data.xianpin_list do
		local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(data.xianpin_list[i].attr_id)
		if 0 < data.xianpin_list[i].attr_value and self.node_list["text_left_attr" .. index] then
			attr_str = nil
			attr_type = 0
			for k,v in pairs(next_jiping) do
				attr_type = EquipmentWGData.Instance:GetAttrIdByAttrStr(k)
				if attr_type == data.xianpin_list[i].attr_id then
					attr_str = k
					break
				end
			end

			if 1 == data.grade_level then
				if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_str) then
					str = attr_name .. "+" .. (data.xianpin_list[i].attr_value / 100) .. "%"
				else
					str = attr_name .. "+" .. data.xianpin_list[i].attr_value
				end
			else
				if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_str) then
					str = attr_name .. "+" .. (curr_jiping[attr_str] / 100) .. "%"
				else
					str = attr_name .. "+" .. curr_jiping[attr_str]
				end
			end
			str = TianShenWGData.Instance:ChangeColor(str, data.xianpin_list[i].is_star)
			self.node_list["text_left_attr" .. index]:SetActive(true)
			self.node_list["text_right_attr" .. index]:SetActive(true)
			self.node_list["text_left_attr" .. index].text.text = str
			if next_jiping then
				str = TianShenWGData.Instance:ChangeColor(attr_name, data.xianpin_list[i].is_star)
				str = str .. " <color=#006a25>" .. (data.grade_level + 1) .. Language.Common.Jie .. "+"
				if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_str) then
					str = str .. (next_jiping[attr_str] / 100) .. "%</color>"
				else
					str = str .. next_jiping[attr_str] .. "</color>"
				end
				self.node_list["text_right_attr" .. index].text.text = str
			else
				str = TianShenWGData.Instance:ChangeColor(attr_name, data.xianpin_list[i].is_star)
				self.node_list["text_right_attr" .. index].text.text = str .. "<color=#006a25> MAX</color>"
			end
			index = index + 1
		end
	end

	for i=index,5 do
		self.node_list["text_left_attr" .. i]:SetActive(false)
		self.node_list["text_right_attr" .. i]:SetActive(false)
	end
end

function ShenShiQiangHuaView:QiangHuaOnFlush(param_list)
	for k,v in pairs(param_list) do
		if k == "all" then
			if param_list.all.select_data then
				self.select_tainshen_cfg = param_list.all.select_data
			end
			self.equip_list = TianShenWGData.Instance:GetTianShenEquip(self.select_tainshen_cfg.index)
			self.shenshi_index = v.shenshi_index or 0
			self.equip_list_view.scroller:ReloadData(1)
		elseif k == "single" then
			self:QiangHuaOnFlushView()
			self:FlushRedDot()
		end
	end
end

function ShenShiQiangHuaView:QiangHuaOnFlushView()
	if not self.select_tainshen_cfg or not self.qianghua_curr_select_item then return end

	self.equip_list = TianShenWGData.Instance:GetTianShenEquip(self.select_tainshen_cfg.index)
	local equip_data = TianShenWGData.Instance:GetTianShenEquip(self.select_tainshen_cfg.index, self.qianghua_curr_select_item:GetData().bag_index)
	self.qianghua_curr_select_item:SetData(equip_data)

	self:OnItemCallBack(self.qianghua_curr_select_item, true)
end


----------------------------------------------------------------------------------------------

TianShenQiangHuaItemRender = TianShenQiangHuaItemRender or BaseClass(BaseRender)

function TianShenQiangHuaItemRender:__init()
	self.item_call_back = BindTool.Bind(self.OnClickItem, self)
	self.base_cell = ItemCell.New(self.node_list["item_pos"])
	self.base_cell:AddClickEventListener(self.item_call_back, true)
	self.base_cell:SetItemTipFrom(ItemTip.FROM_TIANSHEN_QIANGHUA)
	self.base_cell:SetIsShowTips(false)
	self.call_back = nil
	XUI.AddClickEventListener(self.node_list["item_root"], self.item_call_back)
end

function TianShenQiangHuaItemRender:__delete()
	self.call_back = nil
	self.item_call_back = nil
	self.base_cell:DeleteMe()
	self.base_cell = nil
end

function TianShenQiangHuaItemRender:ClickCallBack(call_back)
	-- body
	self.call_back = call_back
end

function TianShenQiangHuaItemRender:OnClickItem()
	-- body
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if not item_cfg then return end

	if item_cfg.color < GameEnum.ITEM_COLOR_PURPLE then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.QianHuaTips2)
		return
	end

	if nil ~= self.call_back then
		self.call_back(self)
	end
end

function TianShenQiangHuaItemRender:SetSelect(is_select)
	-- body
	self.is_select = is_select
	self.node_list["img_highLight"]:SetActive(is_select)
end

function TianShenQiangHuaItemRender:OnFlush()
	if self.data == nil then return end
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if not item_cfg then return end

	self.base_cell:SetData(self.data)
	self.node_list["text_name"].text.text = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color])

	if item_cfg.color < GameEnum.ITEM_COLOR_PURPLE then
		self.node_list["text_level"].text.text = Language.TianShen.QianHuaTips2
		self.node_list["text_can_up"]:SetActive(false)
		self.node_list["img_max_level"]:SetActive(false)
	else
		local upgrade_cfg = TianShenWGData.Instance.tianshen_cfg_auto.equip_upgrade[self.data.grade_level]
		if not upgrade_cfg then return end

		self.node_list["text_level"].text.text = self.data.stren_level .. Language.Common.Ji
		local can_up = 0 ~= upgrade_cfg.need_stren_level and upgrade_cfg.need_stren_level <= self.data.stren_level
		self.node_list["text_can_up"]:SetActive(can_up)

		local next_equip_strange_cfg = TianShenWGData.Instance.shenshi_equip_strange_cfg[self.data.stren_level + 1]
		self.node_list["img_max_level"]:SetActive(not next_equip_strange_cfg)
	end
	self:ChecnRedPoint()
end

function TianShenQiangHuaItemRender:CheckSelect()
	local view = ViewManager.Instance:GetView(GuideModuleName.ShenShiQiangHuaView)
	if view then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if not item_cfg then
			return
		end

		if item_cfg.color >= GameEnum.ITEM_COLOR_PURPLE then
			self:OnClickItem()
		end
	end
end

function TianShenQiangHuaItemRender:ChecnRedPoint()
	local view = ViewManager.Instance:GetView(GuideModuleName.TianShenView)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if view and item_cfg then
		local select_data = view:GetCurSelectListData()
		local upgrade_cfg = TianShenWGData.Instance.tianshen_cfg_auto.equip_upgrade[self.data.grade_level]
		if upgrade_cfg and select_data then
			local flag = 0
			if 0 == upgrade_cfg.need_stren_level or upgrade_cfg.need_stren_level > self.data.stren_level then
				flag = TianShenWGData.Instance:CheckEquipStrengthen(select_data.index, TianShenWGData.Equip_Pos[item_cfg.sub_type])
			else
				flag = TianShenWGData.Instance:CheckEquipUpgrade(select_data.index, TianShenWGData.Equip_Pos[item_cfg.sub_type])
			end
			self.node_list["red_point"]:SetActive(1 == flag)
		end
	end
end
