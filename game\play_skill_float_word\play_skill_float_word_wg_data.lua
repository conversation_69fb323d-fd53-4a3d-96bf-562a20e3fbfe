PlaySkillFloatWordWGCData = PlaySkillFloatWordWGCData or BaseClass()

function PlaySkillFloatWordWGCData:__init()
	if PlaySkillFloatWordWGCData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[PlaySkillFloatWordWGCData] attempt to create singleton twice!")
		return
	end

	PlaySkillFloatWordWGCData.Instance = self

	self:InitCfg()
end

function PlaySkillFloatWordWGCData:__delete()
	PlaySkillFloatWordWGCData.Instance = nil
end

function PlaySkillFloatWordWGCData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("skill_screen_effect_cfg_auto")
	self.skill_screen_effect_cfg = cfg.piaozi
	self.nor_piaozi_cfg = cfg.nor_piaozi
end

function PlaySkillFloatWordWGCData:GetFloatSkillCfg(skill_id)
    return self.skill_screen_effect_cfg[skill_id]
end

function PlaySkillFloatWordWGCData:GetFloatWordCfgByType(float_word_type)
    return self.nor_piaozi_cfg[float_word_type]
end