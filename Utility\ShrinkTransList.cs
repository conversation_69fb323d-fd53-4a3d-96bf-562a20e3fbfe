﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public sealed class ShrinkTransList
{
    [SerializeField]
    private GameObject gameObj;

    [SerializeField]
    private GameObject move;

    [SerializeField]
    private GameObject redPoint;

    public GameObject GetGameObj()
    {
        return gameObj;
    }

    public GameObject GetMoveObj()
    {
        return move;
    }

    public GameObject GetRedPointObj()
    {
        return redPoint;
    }
}
