using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;



namespace Game
{
    [ExecuteInEditMode]
    [RequireComponent(typeof(GameObjectAttach))]
    public class GameObjectOffsetSizeData : MonoBehaviour
    {
        public enum OffetType
        {
            Vertical,
            Horizontal,
            Auto,
        }


        [Header("Target SizeData")]
        public RectTransform Target;

        [Header("effect of SizeData template")]
        public Vector2 effect_template;

        public OffetType cur_offet_Type = OffetType.Vertical;

        //private bool is_offseted = false;

        private GameObjectAttach object_attach;
        public GameObjectAttach p_object_attach
        {
            get
            {
                if (object_attach == null)
                {
                    object_attach = this.GetComponent<GameObjectAttach>();
                }
                return object_attach;
            }
        }


        private float size_data_x;
        private float size_data_y;
        private void Update()
        {
            if (Target == null)
            {
                return;
            }

            if (Target.sizeDelta.x == size_data_x && Target.sizeDelta.y == size_data_y)
            {
                return;
            }

            this.UpdateOffsetObj();
        }

        private void UpdateOffsetObj()
        {
            float proportion_x = Target.sizeDelta.x / effect_template.x;
            float proportion_y = Target.sizeDelta.y / effect_template.y;

            float real_x = cur_offet_Type == OffetType.Horizontal || cur_offet_Type == OffetType.Auto ? proportion_x : 1;
            float real_y = cur_offet_Type == OffetType.Vertical || cur_offet_Type == OffetType.Auto ? proportion_y : 1;

            this.transform.localScale = new Vector3(real_x, real_y, 1);
            size_data_x = Target.sizeDelta.x;
            size_data_y = Target.sizeDelta.y;
        }
    }
}


