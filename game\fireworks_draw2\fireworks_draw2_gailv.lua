--灵核概率展示面板
FireWorksDrawSecondProbabilityView = FireWorksDrawSecondProbabilityView or BaseClass(SafeBaseView)
function FireWorksDrawSecondProbabilityView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(650, 400)})
    self:AddViewResource(0, "uis/view/rebate_gift_ui_prefab", "rebate_fire_works_probability")
    self:SetMaskBg(true, true)
end

function FireWorksDrawSecondProbabilityView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function FireWorksDrawSecondProbabilityView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FireWorksDrawSecond.ProbabilityTitle
     if not self.probability_list then
        self.probability_list = AsyncListView.New(FireWorksDrawSecondProItemRender, self.node_list.ph_pro_list) 
    end
end

function FireWorksDrawSecondProbabilityView:OnFlush()
    local info = FireWorksDrawSecondWGData.Instance:GetFireWorksProbabilityInfo()
    self.probability_list:SetDataList(info)
end

----------------------------------------------------------------------------------
FireWorksDrawSecondProItemRender = FireWorksDrawSecondProItemRender or BaseClass(BaseRender)
function FireWorksDrawSecondProItemRender:__delete()
    
end

function FireWorksDrawSecondProItemRender:LoadCallBack()
    
end

function FireWorksDrawSecondProItemRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
