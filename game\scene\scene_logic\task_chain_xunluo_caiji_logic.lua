TaskChainXunLuoCaiJiLogic = TaskChainXunLuoCaiJiLogic or BaseClass(CommonFbLogic)
function TaskChainXunLuoCaiJiLogic:__init()
	self.obj_die_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_DEAD, BindTool.Bind(self.MainRoleDie, self))
	self.stop_gather_event = GlobalEventSystem:Bind(ObjectEventType.STOP_GATHER, BindTool.Bind(self.OnStopGather, self))

	self.need_set_guaji = false
	self.need_commit = true
	self.show_tip_time = nil
end

function TaskChainXunLuoCaiJiLogic:__delete()
	if self.obj_die_event then
		GlobalEventSystem:UnBind(self.obj_die_event)
		self.obj_die_event = nil
	end

	if self.stop_gather_event then
		GlobalEventSystem:UnBind(self.stop_gather_event)
		self.stop_gather_event = nil
	end

	if self.fuhuo_delay then
		GlobalTimerQuest:CancelQuest(self.fuhuo_delay)
		self.fuhuo_delay = nil
	end

	self.need_set_guaji = false
	self.show_tip_time = nil
	self:ResetCommitTimer()
end

function TaskChainXunLuoCaiJiLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	--不让挂机，空气墙未生成会走出空气墙范围
	GuajiWGCtrl.Instance:StopGuaji(false, true)
	
	ViewManager.Instance:CloseAll()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetSkillShowState(false)
		MainuiWGCtrl.Instance:SetButtonModeClick(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		ViewManager.Instance:Open(GuideModuleName.OperationTaskChainFbInfoView)
	end)

	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainScheduleView)
	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainSkillView)

	self.need_set_guaji = false
end

function TaskChainXunLuoCaiJiLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	if self.fuhuo_delay then
		GlobalTimerQuest:CancelQuest(self.fuhuo_delay)
		self.fuhuo_delay = nil
	end

	OperationTaskChainWGCtrl.Instance:ClearCaiJiTaskStates()

	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetSkillShowState(true)
	MainuiWGCtrl.Instance:SetButtonModeClick(true)
	UiInstanceMgr.Instance:ColseFBStartDown()
	OperationTaskChainWGCtrl.Instance:ResetInfoView()
	OperationTaskChainWGData.Instance:ResetXunLuoCaiJiInfo()
	
	FuBenPanelCountDown.Instance:CloseViewHandler()

	self.need_set_guaji = false
	self.show_tip_time = nil
	self:ResetCommitTimer()

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		main_role:SetUnderShowInfo(nil)
	end
end

function TaskChainXunLuoCaiJiLogic:IsEnemy(target_obj, main_role, ignore_table, test)
	return false
end

function TaskChainXunLuoCaiJiLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function TaskChainXunLuoCaiJiLogic:GetGuajiCharacter()
	local is_need_stop = true

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and not main_role:GetIsGatherState() and main_role:IsStand() and not self:GetIsSendGather() then
		local num = OperationTaskChainWGData.Instance:GetOldCanSubmission() or 0
		local npc_id, gather_radius = OperationTaskChainWGData.Instance:GetXunLuoCaiJiOperaInfo()

		if num > 0 and npc_id ~= nil and self.need_commit then
			GuajiWGCtrl.Instance:MoveToNpc(npc_id, nil, Scene.Instance:GetSceneId())
		elseif num <= 0 then
			local obj = Scene.Instance:SelectRandGatherByDis(nil, gather_radius)
			if obj ~= nil then
				MoveCache.SetEndType(MoveEndType.Gather)
				MoveCache.target_obj = obj
				GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), obj.vo.pos_x, obj.vo.pos_y, COMMON_CONSTS.GATHER_TRIIGER_RANGE)
			end
		end	
	end

	return nil, nil, is_need_stop
end

-- 怪物是否是敌人
function TaskChainXunLuoCaiJiLogic:IsMonsterEnemy(target_obj, main_role)
	return false
end

function TaskChainXunLuoCaiJiLogic:GetGuajiPos()
end

function TaskChainXunLuoCaiJiLogic:CanGetMoveObj()
	return false
end

-- function TaskChainXunLuoCaiJiLogic:ObjCreate(obj)
-- 	local npc_id = OperationTaskChainWGData.Instance:GetXunLuoCaiJiOperaInfo()
-- 	if obj ~= nil and npc_id ~= nil and not obj:IsDeleted() and obj:IsNpc() and obj:GetNpcId() == npc_id then
-- 		obj:CheckShowUnderBar()
-- 	end
-- end

function TaskChainXunLuoCaiJiLogic:GetMonsterName(monster)
	local color_name = ""
	if monster.vo then
		color_name = ToColorStr(monster.vo.name or "", COLOR3B.RED)
	end
	return color_name
end

function TaskChainXunLuoCaiJiLogic:MainRoleDie()
	if self.fuhuo_delay then
		GlobalTimerQuest:CancelQuest(self.fuhuo_delay)
		self.fuhuo_delay = nil
	end

	if self.show_tip_time == nil or self.show_tip_time <= Status.NowTime then
		self.show_tip_time = Status.NowTime + 1
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.TaskChain.IsSee)
	end

	self.fuhuo_delay = GlobalTimerQuest:AddDelayTimer(function() 
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 1, -1)
		end, 1)
end

function TaskChainXunLuoCaiJiLogic:OnStopGather(reason)
    if reason ~= nil then
        if reason == STOP_GATHER_REASON.ALREADY_GATHER then
            local cfg = OperationTaskChainWGData.Instance:GetXunLuoCaiJiDungeonInfo()
            local name = ""
            if cfg ~= nil then
                name = cfg.item_name
            end

            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.OpertionAcitvity.TaskChain.AlreadyGather, name))
        end
    end
end

function TaskChainXunLuoCaiJiLogic:OnSelectObj(target_obj, select_type)
	self.need_set_guaji = false

	if target_obj == nil or target_obj:IsDeleted() then
		return
	end

	if target_obj:GetType() == SceneObjType.Npc and GuajiCache.guaji_type == GuajiType.Auto and not SceneWGData.Instance:TargetSelectIsScene(select_type) then
		self.need_set_guaji = true
	end
end

function TaskChainXunLuoCaiJiLogic:GetIsNeedGuaJi()
	return self.need_set_guaji
end

function TaskChainXunLuoCaiJiLogic:SetIsNeedCheckCommit(value)
	self.need_commit = value

	if not value then
		if self.commit_delay_timer then
			GlobalTimerQuest:CancelQuest(self.commit_delay_timer)
			self.commit_delay_timer = nil
		end

		self.commit_delay_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.OnCommitDealy,self), 2)
	else
		if self.commit_delay_timer then
			GlobalTimerQuest:CancelQuest(self.commit_delay_timer)
			self.commit_delay_timer = nil
		end
	end
end

function TaskChainXunLuoCaiJiLogic:OnCommitDealy()
	self:ResetCommitTimer()
end

function TaskChainXunLuoCaiJiLogic:ResetCommitTimer()
	if self.commit_delay_timer then
		GlobalTimerQuest:CancelQuest(self.commit_delay_timer)
		self.commit_delay_timer = nil
	end

	self.need_commit = true
end

function TaskChainXunLuoCaiJiLogic:CheckObjIsIgnoreShowMainSelect(target_obj, select_type)
	if SceneWGData:TargetSelectIsScene(select_type) then
		if target_obj ~= nil and not target_obj:IsDeleted() then
			return not target_obj:IsRole()
		else
			return false
		end
	else
		return false
	end
end