﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class RuntimeAssetHelperWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("RuntimeAssetHelper");
		<PERSON><PERSON>RegFunction("InsureDirectory", InsureDirectory);
		<PERSON><PERSON>Function("WriteWebRequestData", WriteWebRequestData);
		<PERSON><PERSON>unction("TryWriteWebRequestData", TryWriteWebRequestData);
		<PERSON><PERSON>RegFunction("TryWriteWebRequestDataByStream", TryWriteWebRequestDataByStream);
		<PERSON><PERSON>RegFunction("IntToString", IntToString);
		<PERSON><PERSON>RegFunction("DeleteDirectory", DeleteDirectory);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InsureDirectory(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			RuntimeAssetHelper.InsureDirectory(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WriteWebRequestData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			UnityEngine.Networking.UnityWebRequest arg1 = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 2);
			RuntimeAssetHelper.WriteWebRequestData(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TryWriteWebRequestData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			UnityEngine.Networking.UnityWebRequest arg1 = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 2);
			bool o = RuntimeAssetHelper.TryWriteWebRequestData(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TryWriteWebRequestDataByStream(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				string arg0 = ToLua.CheckString(L, 1);
				UnityEngine.Networking.UnityWebRequest arg1 = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 2);
				System.Action<bool> arg2 = (System.Action<bool>)ToLua.CheckDelegate<System.Action<bool>>(L, 3);
				RuntimeAssetHelper.WriteHandle o = RuntimeAssetHelper.TryWriteWebRequestDataByStream(arg0, arg1, arg2);
				ToLua.PushValue(L, o);
				return 1;
			}
			else if (count == 4)
			{
				string arg0 = ToLua.CheckString(L, 1);
				UnityEngine.Networking.UnityWebRequest arg1 = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 2);
				System.Action<bool> arg2 = (System.Action<bool>)ToLua.CheckDelegate<System.Action<bool>>(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				RuntimeAssetHelper.WriteHandle o = RuntimeAssetHelper.TryWriteWebRequestDataByStream(arg0, arg1, arg2, arg3);
				ToLua.PushValue(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: RuntimeAssetHelper.TryWriteWebRequestDataByStream");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			string o = RuntimeAssetHelper.IntToString(arg0, arg1);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DeleteDirectory(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			RuntimeAssetHelper.DeleteDirectory(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

