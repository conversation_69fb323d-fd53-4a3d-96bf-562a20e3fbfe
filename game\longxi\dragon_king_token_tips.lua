-- 龙神令牌按钮
DragonKingTokenTip = DragonKingTokenTip or BaseClass(BaseRender)

function DragonKingTokenTip:DoLoad(parent)
	self:LoadAsset("uis/view/long_xi_ui_prefab", "layout_king_token_tip", parent.transform)
end

function DragonKingTokenTip:__delete()
	if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

end

function DragonKingTokenTip:LoadCallBack()
	self:InitListener()
	self:InitModel()
end

function DragonKingTokenTip:InitListener()
	XUI.AddClickEventListener(self.node_list.click, BindTool.Bind(self.OnClickGetBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.OnClickClose, self))
end

function DragonKingTokenTip:InitModel()
	if self.model_display == nil then
        self.model_display = OperationActRender.New(self.node_list["display_model"])
    end

    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
    if base_data and base_data.show_id > 0 then
        local data = {}
        data.item_id = base_data.show_id
        data.render_type = 0
        data.position = Vector3(0, 0, 0)
        data.rotation = Vector3(0, 0, 0)
        data.scale = Vector3(0.8, 0.8, 0.8)
        self.model_display:SetData(data)
    end
end


function DragonKingTokenTip:OnClickGetBtn()
	--ViewManager.Instance:Open(GuideModuleName.DragonKingTokenView)
    ViewManager.Instance:Open(GuideModuleName.LongXiView, TabIndex.dragon_king_token)
end

function DragonKingTokenTip:OnClickClose()
    LongXiWGData.Instance:SetIsShowTip(false)
	MainuiWGCtrl.Instance:FlushView(0, "king_token_tip")
end