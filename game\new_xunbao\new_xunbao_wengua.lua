NewXunbaoView.MAXPOOLNUM = 5    --池子数量
NewXunbaoView.SPE_POOL_ID = 3   --可直接召唤的pool_id
NewXunbaoView.IMG_ANIM = 9      --9个可移动动画img
NewXunbaoView.BAG_MAX_NUM = 20  --界面最大背包数量

function NewXunbaoView:InitWenGua()
    self.node_list["btn_wengua_record"].button:AddClickListener(BindTool.Bind(self.OnClickWenGuaRecord, self))
    self.node_list["btn_bagua"].button:AddClickListener(BindTool.Bind(self.OnClickOpenBaGua, self))
    self.node_list["btn_wengua_previous"].button:AddClickListener(BindTool.Bind(self.OnClickGuaPrevious, self))
    self.node_list["btn_wengua_tip"].button:AddClickListener(BindTool.Bind(self.OnClickOpenWenGuaTip, self))
    self.node_list["btn_gua_once"].button:AddClickListener(BindTool.Bind(self.OnClickGuaOnce, self))
    self.node_list["btn_gua_all"].button:AddClickListener(BindTool.Bind(self.OnClickGuaALl, self))
    self.node_list["btn_gua_fenjie"].button:AddClickListener(BindTool.Bind(self.OnClickGuaFenJie, self))
    self.node_list["btn_gua_get"].button:AddClickListener(BindTool.Bind(self.OnClickGuaGet, self))
    self.node_list["btn_gua_call"].button:AddClickListener(BindTool.Bind(self.OnClickGuaCall, self))
    self.node_list["btn_stop_wengua"].button:AddClickListener(BindTool.Bind(self.StopWenGuaIng, self))

    local other_cfg = NewXunbaoWGData.Instance:GetWenGuaOther()
    local bundle, asset = ResPath.GetItem(other_cfg.quick_open_pool_consume_item_id)
    self.node_list["img_call_cost"].image:LoadSprite(bundle, asset ,function()
        self.node_list["img_call_cost"].image:SetNativeSize()
    end)

    self:CreateGuaBagList()
    self:ShowGuaCostImg()
    self:ShowQuickOpenPoolInfo()
    self:LoadEffctNode()
    self.node_list["txt_wengua_tip"].text.text = Language.WenGua.WenGuaTip

    self.role_data_change = BindTool.Bind(self.OnRoleValueChange, self)
    RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"vip_level"})
    self.take_anim = GlobalEventSystem:Bind(OtherEventType.WenGuaAnim, BindTool.Bind(self.PlayGuaTakeAnim, self))

    if nil == self.item_data_change_callback then
        self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
    end
end

function NewXunbaoView:DeleteWenGua()
    if self.gua_bag_list then
        self.gua_bag_list:DeleteMe()
        self.gua_bag_list = nil
    end

    if self.gua_alert then
        self.gua_alert:DeleteMe()
        self.gua_alert = nil
    end

    if self.gua_call_alert then
        self.gua_call_alert:DeleteMe()
        self.gua_call_alert = nil
    end

    if self.wengua_ing_delay then
		GlobalTimerQuest:CancelQuest(self.wengua_ing_delay)
		self.wengua_ing_delay = nil
	end

    if self.take_anim then
        GlobalEventSystem:UnBind(self.take_anim)
        self.take_anim = nil
    end

    if self.role_data_change then
        RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
    end

    for i = 1, NewXunbaoView.IMG_ANIM do
        self["gua_take_anim_" .. i] = nil
    end

    self.wengua_ing = nil
    self.effct_trail_list = nil

    if self.item_data_change_callback then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
        self.item_data_change_callback = nil
    end

    self:CancelTween()
end

function NewXunbaoView:ShowIndexWenGua()
    NewXunbaoWGCtrl.Instance:SendWenGuaOp(WENGUA_OPERA_TYPE.QUERY_INFO)
end

--物品变化
function NewXunbaoView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local other_cfg = NewXunbaoWGData.Instance:GetWenGuaOther()
    if other_cfg and other_cfg.draw_consume_item_id == change_item_id then
        self:FlushGuaItemCost()
    end
end

--vip等级变化改变直接召唤的显示
function NewXunbaoView:OnRoleValueChange(attr_name)
    if attr_name == "vip_level" then
        self:ShowQuickOpenPoolInfo()
    end
end

--刷新整个问卦界面
function NewXunbaoView:OnFlushWenGua()
    self:OnFlushUpperShow()
    self:FlushGuaBag()
end

--刷新问卦界面除背包以外的信息
function NewXunbaoView:OnFlushUpperShow()
    self:FlushGuaRawRank()
    self:FlushGuaItemCost()
    self:ShowQuickOpenPoolInfo()
    self:FlushGuaRed()
end

--根据can_do_anim判断是否动画，动画要稍微延迟刷新人物
function NewXunbaoView:OnFlushWenGuaShow(key, value)
    if "gua_result" == key then
        if value.can_do_anim then
            self:OnFlushGuaResultAnim(value)
        else
            self:OnFlushWenGua()
        end
    end
end

--抽奖的拖尾和爆点动画
function NewXunbaoView:OnFlushGuaResultAnim(param_t)
    local effect
    for i = 1, 4 do
        if self.effct_trail_list[i].using == false then
            effect = self.effct_trail_list[i]
        end
    end

    if effect == nil then
        return
    end

    effect.using = true
    local old_pool_id = param_t.old_pool_id
    local now_effect_pos = self.node_list["raw_rank_" .. old_pool_id + 1].rect.anchoredPosition
    effect.eff_get.rect.anchoredPosition = Vector2(now_effect_pos.x, 0)
    local ori_pos = self.node_list["first_pos"].rect.anchoredPosition
    local num = NewXunbaoWGData.Instance:GetCurTimeIdLen()
    local end_pos = self.node_list["end_pos"].rect.anchoredPosition
    local max_len = 9--一行最多显示9个格子
    if num < max_len then
        local dif = (end_pos.x - ori_pos.x) / (max_len - 1)
        end_pos = Vector2(dif * (num-1) + ori_pos.x, ori_pos.y)
    end

    effect.eff_tuowei:SetActive(true)
    effect.eff_bao:SetActive(false)
    effect.eff_get:SetActive(true)
    effect.eff_bao.rect.anchoredPosition = end_pos--爆点特效要在拖尾特效消失前播放

    self:CancelTween()
    local tween = DG.Tweening.DOTween.Sequence()
    --UnityEngine.Time.timeScale = 0.2
    local tween1 = effect.eff_get.rect:DOAnchorPos(end_pos, 0.3)
    tween1:SetEase(DG.Tweening.Ease.Linear)

    tween:Append(tween1)

    tween:AppendInterval(0.1)
    tween:AppendCallback(function()
        self:OnFlushUpperShow()
        effect.eff_bao:SetActive(true)
    end)

    tween:AppendInterval(0.1)
    tween:AppendCallback(function()
        self:FlushGuaBag()
    end)

    tween:AppendInterval(0.4)
    tween:AppendCallback(function()
        effect.eff_tuowei:SetActive(false)
         --UnityEngine.Time.timeScale = 1
    end)

    tween:AppendInterval(0.6)
    tween:OnComplete(function()
        effect.eff_bao:SetActive(false)
        effect.eff_get:SetActive(false)
        effect.using = false
    end)

    self.enter_play_tween = tween
end

function NewXunbaoView:CancelTween()
    if self.enter_play_tween then
        self.enter_play_tween:Kill()
        self.enter_play_tween = nil
    end
end

function NewXunbaoView:FlushGuaRed()
    local is_show_btn_draw_red = NewXunbaoWGData.Instance:IsShowGuaBtnRed()
    local cur_pool_id = NewXunbaoWGData.Instance:GetGuaCurPoolId()
    self.node_list["btn_gua_once_red"]:SetActive(is_show_btn_draw_red)
    self.node_list["btn_gua_all_red"]:SetActive((is_show_btn_draw_red and cur_pool_id) and (cur_pool_id < NewXunbaoView.SPE_POOL_ID) or false)
end

function NewXunbaoView:CreateGuaBagList()
    if not self.gua_bag_list then
        self.gua_bag_list = AsyncListView.New(WenGuaRender, self.node_list["gua_list"])
    end
end

function NewXunbaoView:LoadEffctNode()
    self.effct_trail_list = {}
    for i = 1, 4 do
        self.effct_trail_list[i] = {}
        self.effct_trail_list[i].using = false
        self.effct_trail_list[i].eff_get = self.node_list["effect_get_" .. i]
        self.effct_trail_list[i].eff_tuowei = self.node_list["effect_tuowei_" .. i]
        self.effct_trail_list[i].eff_bao = self.node_list["effect_bao_" .. i]
        self.effct_trail_list[i].eff_get:SetActive(false)
    end
end

function NewXunbaoView:ShowGuaCostImg()
    local other_cfg = NewXunbaoWGData.Instance:GetWenGuaOther()
    local bundle, asset = ResPath.GetItem(other_cfg.draw_consume_item_id)
    self.node_list["img_gua_cost_icon"].image:LoadSprite(bundle, asset ,function()
        self.node_list["img_gua_cost_icon"].image:SetNativeSize()
    end)


    --self:LoadGuaImgNative(self.node_list["img_pre_icon_1"], other_cfg.show_1)
    --self:LoadGuaImgNative(self.node_list["img_pre_icon_2"], other_cfg.show_2)
end

function NewXunbaoView:ShowQuickOpenPoolInfo()
    local other_cfg = NewXunbaoWGData.Instance:GetWenGuaOther()
    local vip_level = GameVoManager.Instance:GetMainRoleVo().vip_level
    if vip_level < other_cfg.quick_open_pool_need_vip_level then
        self.node_list["img_call_cost"]:SetActive(false)
        self.node_list["txt_call_cost"].text.text = ""
        self.node_list["txt_call_cost_no"].text.text = string.format(Language.WenGua.VipNoInfo, other_cfg.quick_open_pool_need_vip_level)
    else
        local num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.quick_open_pool_consume_item_id)
        local color = num < other_cfg.quick_open_pool_consume_item_num and COLOR3B.D_RED or COLOR3B.D_GREEN
        self.node_list["txt_call_cost"].text.text = ToColorStr(num , color).. "/" .. other_cfg.quick_open_pool_consume_item_num
        self.node_list["txt_call_cost_no"].text.text = ""
        self.node_list["img_call_cost"]:SetActive(true)
    end
end

--打开八卦界面
function NewXunbaoView:OnClickOpenBaGua()
    ViewManager.Instance:Open(GuideModuleName.FuWen, TabIndex.fuwen_xiangqian)
end

--打开问卦记录
function NewXunbaoView:OnClickWenGuaRecord()
    ViewManager.Instance:Open(GuideModuleName.WenGuaRecord)
end

--打开问卦预览
function NewXunbaoView:OnClickGuaPrevious()
    ViewManager.Instance:Open(GuideModuleName.WenGuaPre)
end

--打开问卦tip
function NewXunbaoView:OnClickOpenWenGuaTip()
	RuleTip.Instance:SetContent(Language.WenGua.TipCotent, Language.WenGua.TipTitle)
end

--检测能否问卦
function NewXunbaoView:CheckCanWenGua()
    local other_cfg = NewXunbaoWGData.Instance:GetWenGuaOther()
    local num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.draw_consume_item_id)
    if num < other_cfg.draw_consume_item_num then
        --SysMsgWGCtrl.Instance:ErrorRemind(Language.WenGua.NoEnoughTip)
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other_cfg.draw_consume_item_id})
        self:StopWenGuaIng()
        return false
    end
    local has_num = NewXunbaoWGData.Instance:GetCurTimeIdLen()
    if has_num and has_num >= NewXunbaoView.BAG_MAX_NUM then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WenGua.NoEnoughTip)
        self:StopWenGuaIng()
        return false
    end

    if self.wengua_ing then
        local cur_pool_id = NewXunbaoWGData.Instance:GetGuaCurPoolId()
        if cur_pool_id and cur_pool_id >= NewXunbaoView.SPE_POOL_ID then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.WenGua.SpeStopTip)
            self:StopWenGuaIng()
            return false
        end
    end

    return true
end

--问卦一次
function NewXunbaoView:OnClickGuaOnce()
    if self:CheckCanWenGua() then
        if not self.wengua_ing then
            self.node_list["btn_gua_once"].button.interactable = false
            GlobalTimerQuest:AddDelayTimer(function()
                self.node_list["btn_gua_once"].button.interactable = true
            end, 0.4)
        end
        NewXunbaoWGCtrl.Instance:SendWenGuaOp(WENGUA_OPERA_TYPE.DRAW, self.wengua_ing and 1 or 0)
        return true
    end
end

--一键问卦
function NewXunbaoView:OnClickGuaALl()
    self.wengua_ing = not self.wengua_ing
    if self.wengua_ing then
        self.node_list["btn_stop_wengua"]:SetActive(true)
        NewXunbaoWGCtrl.Instance:SendWenGuaOp(WENGUA_OPERA_TYPE.ONEC_KEY_DRAW)
        self:StartWenGuaIng()
    else
        self:StopWenGuaIng()
    end
end

function NewXunbaoView:StartWenGuaIng()
    local status = self:OnClickGuaOnce()
    if status then
        self.node_list["txt_gua_all"].text.text = Language.WenGua.StopWenGua
        if self.wengua_ing then
            self.wengua_ing_delay = GlobalTimerQuest:AddDelayTimer(function()
                self:StartWenGuaIng()
            end, 0.5)
        end
    end
end

function NewXunbaoView:StopWenGuaIng()
    if self.wengua_ing_delay then
		GlobalTimerQuest:CancelQuest(self.wengua_ing_delay)
		self.wengua_ing_delay = nil
	end
    self.node_list["btn_stop_wengua"]:SetActive(false)
    self.wengua_ing = false
    if self.node_list["txt_gua_all"] then
        self.node_list["txt_gua_all"].text.text = Language.WenGua.YiJianWenGua
    end
end

--一键分解
function NewXunbaoView:OnClickGuaFenJie()
    local item_list = NewXunbaoWGData.Instance:GetGuaIdList()
    if IsEmptyTable(item_list) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WenGua.NoFenJieTip)
        return
    end

    self:GetGuaAlert()
    local color_red, color_orange, color_purple = NewXunbaoWGData.Instance:CheckGuaColorNum()
    local str_all = ""
    if color_red ~= 0 or color_orange ~= 0 or color_purple ~= 0 then
        str_all = Language.WenGua.FenJieTipStart
        local str1, str2, str3
        if color_red ~= 0 then
            str1 = string.format(Language.WenGua.FenJieTip1, color_red)
        end
        if color_orange ~= 0 then
            str2 = string.format(Language.WenGua.FenJieTip2, color_orange)
        end
        if color_purple ~= 0 then
            str3 = string.format(Language.WenGua.FenJieTip3, color_purple)
        end
        str1 = (str1 or "") .. (str2 or "") .. (str3 or "") .. Language.WenGua.FenJieTipEnd
        str_all = str_all .. str1
        self.gua_alert:SetLableString(str_all)
        self.gua_alert:Open()
    else
        self:SendGuaFenJie()
    end
end

function NewXunbaoView:GetGuaAlert()
    if not self.gua_alert then
        self.gua_alert = Alert.New()
        self.gua_alert:SetOkFunc(BindTool.Bind(self.SendGuaFenJie, self))
    end
end

function NewXunbaoView:SendGuaFenJie()
    --if self:CheckGuaBagIsEmpty() then
    --    return
    --end
    NewXunbaoWGCtrl.Instance:SendWenGuaOp(WENGUA_OPERA_TYPE.BREAK, -1)
end

function NewXunbaoView:CheckGuaBagIsEmpty()
    local num = NewXunbaoWGData.Instance:GetGuaBagNum()
    if num < 1 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WenGua.NoTakeTip)
        return true
    end
    if NewXunbaoWGCtrl.Instance:CheckBaGuaBag(num) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WenGua.NoBag)
        return true
    end
end

--一键提取
function NewXunbaoView:OnClickGuaGet()
    if self:CheckGuaBagIsEmpty() then
        return
    end
    local list = self.gua_bag_list:GetAllItems()
    for i, v in pairs(list) do
        v:FireTakeAnim()
    end
    NewXunbaoWGCtrl.Instance:SendWenGuaOp(WENGUA_OPERA_TYPE.TAKE, -1)
end

--问卦召唤
function NewXunbaoView:OnClickGuaCall()
    local other_cfg = NewXunbaoWGData.Instance:GetWenGuaOther()
    local num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.quick_open_pool_consume_item_id)
    if num < other_cfg.quick_open_pool_consume_item_num then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other_cfg.quick_open_pool_consume_item_id})
        return
    end
    local vip_level = GameVoManager.Instance:GetMainRoleVo().vip_level
    if vip_level < other_cfg.quick_open_pool_need_vip_level then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WenGua.VipNo)
        return
    end
    self:GetGuaCallAlert()
    --self.gua_call_alert:Open()
end

--屏蔽问卦直接召唤二次确认窗
function NewXunbaoView:GetGuaCallAlert()
    --if not self.gua_call_alert then
    --    local other_cfg = NewXunbaoWGData.Instance:GetWenGuaOther()
    --    local cfg = ItemWGData.Instance:GetItemConfig(other_cfg.quick_open_pool_consume_item_id)
    --    local name = ToColorStr(cfg.name, ITEM_COLOR[cfg.color])
    --    local num = other_cfg.quick_open_pool_consume_item_num
    --    local str = string.format(Language.WenGua.CallTip, num, name)
    --
    --    self.gua_call_alert = Alert.New()
    --    self.gua_call_alert:SetLableString(str)
    --    self.gua_call_alert:SetOkFunc(function()
            NewXunbaoWGCtrl.Instance:SendWenGuaOp(WENGUA_OPERA_TYPE.BUY_OPEN_POOL, NewXunbaoView.SPE_POOL_ID)
        --end)
    --end
end

--刷新人物
function NewXunbaoView:FlushGuaRawRank()
    local cur_pool_id = NewXunbaoWGData.Instance:GetGuaCurPoolId()
    if not cur_pool_id then
        return
    end
    for i = 1, NewXunbaoView.MAXPOOLNUM do
        local is_cur_pool = cur_pool_id == i-1
        self.node_list["raw_rank_" .. i]:SetActive(not is_cur_pool)
        self.node_list["anim_rank_" .. i]:SetActive(is_cur_pool)
        self.node_list["role_eff_" .. i]:SetActive(is_cur_pool)

        local str
        if is_cur_pool then
            str = nil
        elseif cur_pool_id < i - 1 then
            str = "wengua_arrow_2"
        else
            str = "wengua_arrow_1"
        end

        if i ~= 5 then--4个可召唤
            self.node_list["txt_pool_call_" .. i].text.text = i == cur_pool_id+1 and Language.WenGua.CanCall or ""
        end

        for j = 1, 4 do
            local index = (i-1)*4+j
            if index <= 16 then
                if str == nil then
                    self.node_list["img_arrow_" .. index]:SetActive(false)
                else
                    self.node_list["img_arrow_" .. index].image:LoadSprite(ResPath.GetXunBaoImg(str))
                    self.node_list["img_arrow_" .. index]:SetActive(true)
                end
            end
        end
    end

    self.node_list["img_cur_pool"].transform:SetParent(self.node_list["img_pool_" .. cur_pool_id + 1].transform, false)

    if cur_pool_id + 1 == NewXunbaoView.MAXPOOLNUM then
        self.node_list["effect_arrow"]:SetActive(false)
    else
        self.node_list["effect_arrow"]:SetActive(true)
        self.node_list["effect_arrow"].transform:SetParent(self.node_list["txt_pool_call_" .. cur_pool_id + 1].transform, false)
    end

    if cur_pool_id < NewXunbaoView.SPE_POOL_ID then
        self.node_list["btn_gua_call"]:SetActive(true)
        XUI.SetGraphicGrey(self.node_list["btn_gua_all"], false)
    else
        self.node_list["btn_gua_call"]:SetActive(false)
        XUI.SetGraphicGrey(self.node_list["btn_gua_all"], true)
    end
end

function NewXunbaoView:FlushGuaItemCost()
    local other_cfg = NewXunbaoWGData.Instance:GetWenGuaOther()
    local num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.draw_consume_item_id)
    local color = num < other_cfg.draw_consume_item_num and COLOR3B.D_RED or COLOR3B.D_GREEN
    self.node_list["txt_gua_cost"].text.text = ToColorStr(num , color).. "/" .. other_cfg.draw_consume_item_num
end

function NewXunbaoView:FlushGuaBag()
    NewXunbaoWGCtrl.Instance:DoWenGuaList()
    local item_list = NewXunbaoWGData.Instance:GetGuaIdList()
    local old_data = self.gua_bag_list:GetDataList()
    local fresh_type = #item_list < #old_data and 3 or 1
    self.gua_bag_list:SetDataList(item_list, fresh_type)
    local is_empty = IsEmptyTable(item_list)
    XUI.SetGraphicGrey(self.node_list["btn_gua_fenjie"], is_empty)
    XUI.SetGraphicGrey(self.node_list["btn_gua_get"], is_empty)
end

function NewXunbaoView:PlayGuaTakeAnim(v3p, item_id)
    for i = 1, NewXunbaoView.IMG_ANIM do
        local anim = "gua_take_anim_" .. i
        if not self[anim] then
            local img = "img_anim_" .. i
            self:ResetAnimPos(v3p, self.node_list[img])
            self:LoadGuaImg(self.node_list[img], item_id)
            self[anim] = DG.Tweening.DOTween.Sequence()
            --local tween1 = self.node_list[img].rect:DOAnchorPos(self.node_list["btn_bagua"].rect.anchoredPosition, 1)
            local tween1 = self.node_list[img].rect:DOMove(self.node_list["btn_bagua"].rect.position, 1)
            tween1:SetEase(DG.Tweening.Ease.InOutCubic)
            local tween2 = self.node_list[img].rect:DOScale(Vector3(0,0,0), 1)
            tween2:SetEase(DG.Tweening.Ease.InExpo)
            self[anim]:Join(tween1)
            self[anim]:Join(tween2)
            self[anim]:OnComplete(function()
                self[anim]:Kill()
                self[anim] = nil
                self.node_list[img]:SetActive(false)
            end)

            break
        end
    end
end

function NewXunbaoView:ResetAnimPos(v3p, parent_node)
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, v3p)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.node_list["raw_xunbao_bg"].rect, screen_pos_tbl, UICamera, Vector2(0, 0))
    parent_node.rect.anchoredPosition = local_position_tbl
    parent_node.rect.localScale = Vector3(1,1,1)
end

function NewXunbaoView:LoadGuaImg(root, item_id)
    local bundle, asset = ResPath.GetItem(item_id)
    root.image:LoadSprite(bundle, asset, function()
        root:SetActive(true)
    end)
end

function NewXunbaoView:LoadGuaImgNative(root, item_id)
    local bundle, asset = ResPath.GetItem(item_id)
    root.image:LoadSprite(bundle, asset, function()
        root.image:SetNativeSize()
    end)
end

--------------------------------------------------------------------------------
WenGuaRender = WenGuaRender or BaseClass(BaseRender)
function WenGuaRender:LoadCallBack()
    self.node_list["icon"].button:AddClickListener(BindTool.Bind(self.OnClickIcon, self))
    self.node_list["take"].button:AddClickListener(BindTool.Bind(self.OnClickTake, self))
end

function WenGuaRender:OnFlush()
    local item_id
    local cfg
    if self.data.is_pre then
        item_id = self.data.item_id
        self.node_list["high"]:SetActive(false)
        cfg = ItemWGData.Instance:GetItemConfig(item_id)
        self.node_list["icon"].image.raycastTarget = true
        self.node_list["root_cell"].image.enabled = false
        self.node_list["take"]:SetActive(false)
    else
        local item = NewXunbaoWGData.Instance:GetGuaItemByRewardId(self.data.id)
        item_id = item.reward_item.item_id
        cfg = ItemWGData.Instance:GetItemConfig(item_id)
        self.node_list["high"]:SetActive(cfg.color >= GameEnum.ITEM_COLOR_PURPLE)
        self.node_list["effect"]:SetActive(cfg.color > GameEnum.ITEM_COLOR_PURPLE)
        self.node_list["root_cell"].image.enabled = true
        self.node_list["icon"].image.raycastTarget = false
        self.node_list["take"]:SetActive(true)
    end
    self.data.item_id = item_id
    self.node_list["icon"].image:LoadSprite(ResPath.GetItem(item_id))
    self.node_list["name"].text.text = ToColorStr(cfg.name, ITEM_COLOR[cfg.color])
end

function WenGuaRender:OnClickIcon()
    if self.data.is_pre then
        TipWGCtrl.Instance:OpenItem({item_id = self.data.item_id})
    end
end

function WenGuaRender:OnClickTake()
    if not self.data.is_pre then
        if NewXunbaoWGCtrl.Instance:CheckBaGuaBag() then
			 SysMsgWGCtrl.Instance:ErrorRemind(Language.WenGua.NoBag)
			 return
		 end
        self:FireTakeAnim()
        NewXunbaoWGCtrl.Instance:SendWenGuaOp(WENGUA_OPERA_TYPE.TAKE, self.data.index - 1)
    end
end

function WenGuaRender:FireTakeAnim()
    print_error("self.data.item_id",self.data.item_id)
    GlobalEventSystem:Fire(OtherEventType.WenGuaAnim, self.node_list["icon"].transform.position, self.data.item_id)
end