
--boss关注
BossFocusView = BossFocusView or BaseClass(SafeBaseView)

local Max_Bg_High = 458  --2
local Max_List_High = 450  --234
local Max_Panel_High = 606
local Min_Panel_High = 392
local Min_Bg_High = 238
local Min_List_High = 230

function BossFocusView:__init()
    self.view_name = "BossFocusView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(948, 590)})
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_focus")
	self:SetMaskBg(true)
end

function BossFocusView:OpenCallBack()

end

function BossFocusView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Boss.Remind
	self.ph_boss_list = AsyncListView.New(BossFocusRender, self.node_list["ph_boss_list"])
end

function BossFocusView:ReleaseCallBack()
	if nil ~= self.ph_boss_list then
		self.ph_boss_list:DeleteMe()
		self.ph_boss_list = nil
    end
    self.data = nil
end

function BossFocusView:CloseCallBack()
    self.data = nil
end

--tabindex, is_world_server cur_layer
function BossFocusView:SetDataAndOpen(data)
    self.data = data

    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function BossFocusView:OnFlush()
    if not self.data then
        return
    end

    local index = self.data.tabindex
    local cur_layer = self.data.cur_layer
    local list_data

    if self.data.is_world_server then
        if index == TabIndex.worserv_boss_mh then --蛮荒神兽
            list_data = BossWGData.Instance:GetCrossLayerBossBylayer(cur_layer)
        elseif index == TabIndex.xianjie_boss then --仙界boss
            list_data = XianJieBossWGData.Instance:GetBossListlayerByLayer(cur_layer)
        elseif index == TabIndex.world_new_shenyuan_boss then
            list_data = BossWGData.Instance:GetShenYuanBossList()
        end
    elseif index == BossViewIndex.VipBoss then
		if cur_layer == nil then
			return
		end
        list_data = BossWGData.Instance:GetVipBossListByIndex(cur_layer) 
    else
        if index == BossViewIndex.DabaoBoss then
			if cur_layer == nil then
				return
            end
            list_data = BossWGData.Instance:GetDabaoBossListByIndex(cur_layer)
        elseif BossViewIndex.WorldBoss == index then
            if cur_layer == nil then
				return
            end
            list_data = BossWGData.Instance:GetWorldBossListByLayer(cur_layer)
        elseif BossViewIndex.PersonalBoss == index then
            list_data = BossWGData.Instance:GetPersonalBossList()
        end
    end
    list_data = list_data or {}
    self.ph_boss_list:SetDataList(list_data)
    -- local count = #list_data
    -- if count <= 4 then
    --     self.node_list.bg.rect.sizeDelta = Vector2(464, Min_Bg_High)
    --     self.node_list.ph_boss_list.rect.sizeDelta = Vector2(464, Min_List_High)
    --     self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(632, Min_Panel_High)
    -- else
    --     self.node_list.bg.rect.sizeDelta = Vector2(464, Max_Bg_High)
    --     self.node_list.ph_boss_list.rect.sizeDelta = Vector2(464, Max_List_High)
    --     self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(632, Max_Panel_High)
    -- end
end

-------------------------------------------------------------------------------------------
BossFocusRender = BossFocusRender or BaseClass(BaseRender)
function BossFocusRender:__delete()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end
    
	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
end

function BossFocusRender:LoadCallBack()
    self.node_list["focus_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBossFocus, self))
end

function BossFocusRender:OnFlush()
    local monster_info = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
    local level
    if self.data.boss_level then
        level = self.data.boss_level
    else
        level = monster_info.level
    end
    
    self.node_list.level.text.text = string.format("%s %s", level, Language.Common.Ji)
    if self.data.slot_limit and self.data.slot_page_limit then
        local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(self.data.slot_limit)
        local slor_str = slot_cfg.short_name
        local page_str2 = XianJieBossWGData.Instance:GetPageStr(self.data.slot_page_limit, true)
        self.node_list.jieshu_txt.text.text = string.format(Language.XianJieBoss.SlotSNoPoint, slor_str, page_str2)
    else
        self.node_list.jieshu_txt.text.text = string.format(Language.Boss.FocusJieShu, NumberToChinaNumber(monster_info.boss_jieshu))
    end
    
    self.node_list.boss_name.text.text = monster_info.name
    local data = WorldServerWGData.Instance:GetCurFocusViewData()
    if not data.is_world_server then
        local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
        if boss_server_info and boss_server_info.is_concern then
            self:RefreshConcernTag(boss_server_info.is_concern > 0)
        else
            self:RefreshConcernTag(false)
        end
    else
        local tabindex = data.tabindex
        if tabindex == TabIndex.worserv_boss_mh then
            local is_concern = BossWGData.Instance:GetCrossBossIsConcern(data.cur_layer, self.data.boss_index)
            self:RefreshConcernTag(is_concern ~= nil and is_concern > 0)
        elseif tabindex == TabIndex.xianjie_boss then
            local boss_info = XianJieBossWGData.Instance:GetBossCfgById(self.data.boss_id)
            if boss_info and boss_info.is_concern then
                self:RefreshConcernTag(boss_info.is_concern > 0)
            else
                self:RefreshConcernTag(false)
            end
        elseif tabindex == TabIndex.world_new_shenyuan_boss then
            local is_concern = BossWGData.Instance:GetShenYuanBossIsConcern(self.data.seq)
            self:RefreshConcernTag(is_concern)
        end
    end
end

function BossFocusRender:RefreshConcernTag(vis)
	self.node_list["gou"]:SetActive(vis)
end

--检查角色关注的boss中有没有等级过高无产出的
function BossFocusRender:CheckRoleConcern(list_data)
    local role_level = RoleWGData.Instance:GetRoleLevel()
    if not IsEmptyTable(list_data) then
        if list_data.max_delta_level and list_data.boss_level then
            if list_data.max_delta_level and role_level - list_data.boss_level >= list_data.max_delta_level then
                return true
            end
        end
    end
end

function BossFocusRender:OnClickBossFocus()
    if self:CheckRoleConcern(self.data) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.CannotFocus)
        return
    end
    local data = WorldServerWGData.Instance:GetCurFocusViewData()
    local tabindex = data.tabindex
    local cur_layer = data.cur_layer
    local img_hook = self.node_list["gou"]
    local vis = img_hook:GetActive()
    if data.is_world_server then
        if tabindex == TabIndex.worserv_boss_mh then
            local concern_opera = not vis and BossView.KfReqType.CONCERN or BossView.KfReqType.UNCONCERN
            BossWGCtrl.Instance:SendCrossBossReq(concern_opera, cur_layer,self.data.boss_id)
            BossWGData.Instance:SetCrossBossConcern(cur_layer,self.data.boss_id, not vis, tabindex)
        elseif tabindex == TabIndex.world_new_shenyuan_boss then
            local opa_type = vis and WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE.SHENYUAN_BOSS_REQ_TYPE_UN_CONCERN or WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE.SHENYUAN_BOSS_REQ_TYPE_CONCERN
            BossWGCtrl.Instance:SendShenYuanOpera(opa_type, self.data.seq)
        else
            BossWGCtrl.Instance:SendXianJieBossReq(CROSS_FAIRYLAND_BOSS_OPERATE_TYPE.CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_BOSS_CONCERN, 
            cur_layer, self.data.boss_id, not vis and 1 or 0)
        end
        img_hook:SetActive(not vis)
        ViewManager.Instance:FlushView(GuideModuleName.WorldServer, tabindex, "focus_cell")
    else
        if tabindex == BossViewIndex.WorldBoss then
            BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.CONCERN, self.data.boss_id, not vis and 1 or 0)
        elseif tabindex == BossViewIndex.VipBoss then
            BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.OTHER_CONCERN, cur_layer - 1, self.data.boss_id, not vis and 1 or 0)
        elseif tabindex == BossViewIndex.DabaoBoss then
            BossWGCtrl.Instance:SendDongKuBossReq(BossView.ReqType.OTHER_CONCERN, cur_layer, self.data.boss_id, not vis and 1 or 0)
        end
        BossWGData.Instance:SetBossIsConcernByBossId(self.data.boss_id, vis and 0 or 1)
        img_hook:SetActive(not vis)
        ViewManager.Instance:FlushView(GuideModuleName.Boss, tabindex, "focus_cell")
    end
end
