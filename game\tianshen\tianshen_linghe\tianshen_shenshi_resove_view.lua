-------------------------------------
-- 诸神-文物分解
-------------------------------------
TianShenShenShiResoveView = TianShenShenShiResoveView or BaseClass(SafeBaseView)
function TianShenShenShiResoveView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal
    self.view_cache_time = 0

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(-14, 8),sizeDelta = Vector2(942, 650)})
	self:AddViewResource(0, "uis/view/tianshen_linghe_ui_prefab", "layout_shenshi_resolve_view")
end

function TianShenShenShiResoveView:LoadCallBack()
    self.is_on = false
    self.is_rs_first_load = true
    self.rs_color_select_list = {}

    if not self.rs_bag_grid then
        self.rs_bag_grid = LingHeResloveGrid.New()
        self.rs_bag_grid:SetStartZeroIndex(false)
        self.rs_bag_grid:SetIsMultiSelect(true)
        self.rs_bag_grid:CreateCells({
            col = 5,
            cell_count = 40,
            list_view = self.node_list["rs_bag_grid"],
            itemRender = ResoveSSEquipCell,
            change_cells_num = 2,
            complement_col_item = true
        })
        self.rs_bag_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectCB, self))
    end

    if not self.rs_get_list_view then
        self.rs_get_list_view = AsyncListView.New(SSBagResloveGetItem, self.node_list["rs_get_list_view"])
    end

    for i = 1, 4 do
        XUI.AddClickEventListener(self.node_list["quality_toggle_" .. i], BindTool.Bind(self.OnRSSelectColor, self, i))
        self.node_list["text_quality_" .. i].text.text = Language.TianShenLingHe.QualityNameList[i]
    end
    XUI.AddClickEventListener(self.node_list["rs_btn_resolve"], BindTool.Bind(self.OnClickReslove, self))
    XUI.AddClickEventListener(self.node_list["btn_select_color"], BindTool.Bind(self.OnClickSelectColorBtn, self))

    self.node_list["quality_toggle_1"].toggle.isOn = true
    self.node_list["title_view_name"].text.text = Language.TianShenLingHe.ResoveViewName
end

function TianShenShenShiResoveView:ReleaseCallBack()
    self.rs_color_select_list = {}
    self.is_rs_first_load = nil

    if self.rs_bag_grid then
        self.rs_bag_grid:DeleteMe()
        self.rs_bag_grid = nil
    end

    if self.rs_get_list_view then
        self.rs_get_list_view:DeleteMe()
        self.rs_get_list_view = nil
    end
end

function TianShenShenShiResoveView:OnFlush()
    local reslove_list = TianShenLingHeWGData.Instance:GetResloveBagList(self.rs_color_select_list)
    -- self.node_list["rs_no_item"]:SetActive(#reslove_list == 0)
    self.rs_bag_grid:SetDataList(reslove_list)
    self.rs_bag_grid:SetColorSelcet(self.rs_color_select_list)
    self:FlushGetItemView()
end

function TianShenShenShiResoveView:OnBagSelectCB(cell)
    self:FlushGetItemView()
end

function TianShenShenShiResoveView:OnClickSelectColorBtn()
    self.is_on = not self.is_on
    self:SetTogglesShow(self.is_on)
end

function TianShenShenShiResoveView:SetTogglesShow(is_show)
    self.node_list.color_list_part:SetActive(is_show)
    self.node_list.color_arrow_down:SetActive(is_show)
    self.node_list.color_arrow_up:SetActive(not is_show)
end

function TianShenShenShiResoveView:OnRSSelectColor(index, is_on)
    for key, value in pairs(self.rs_color_select_list) do
        self.rs_color_select_list[key] = false
    end
    self.rs_color_select_list[index] = true
    self.node_list.cur_color_text.text.text = Language.TianShenLingHe.QualityNameList[index]
    if self.is_rs_first_load then
        self.is_rs_first_load = nil
        return
    end

    if self.rs_bag_grid then
        -- 选中颜色的要排前面
        local reslove_list = TianShenLingHeWGData.Instance:GetResloveBagList(self.rs_color_select_list)
        self.rs_bag_grid:SetDataList(reslove_list)
        self.rs_bag_grid:SetColorSelcet(self.rs_color_select_list)
    end
    self:OnClickSelectColorBtn()
    self:FlushGetItemView()
end

function TianShenShenShiResoveView:FlushGetItemView()
    self.rs_select_list = self.rs_bag_grid:GetAllSelectCell()
    local get_item_list = {}
    local tslh_data = TianShenLingHeWGData.Instance

    local get_id, get_num
    for k,v in pairs(self.rs_select_list) do
        local cfg = tslh_data:GetLingGHeCfgByItemId(v.item_id)
        get_id = cfg and cfg.get_id or 0
        get_num = cfg and cfg.get_num or 0
        if get_id > 0 and get_num > 0 then
            get_item_list[get_id] = get_item_list[get_id] or 0
            get_item_list[get_id] = get_item_list[get_id] + get_num
        end
    end

    local show_list = {}
    for k,v in pairs(get_item_list) do
        table.insert(show_list, {item_id = k, get_num = v})
    end

    if not IsEmptyTable(show_list) then
        SortTools.SortAsc(show_list, "item_id")
    end

    self.node_list["rs_no_get_tips"]:SetActive(#show_list == 0)
    self.rs_get_list_view:SetDataList(show_list)
end

-- 分解
function TianShenShenShiResoveView:OnClickReslove()
    if IsEmptyTable(self.rs_select_list) then return end
    TianShenLingHeWGCtrl.Instance:SendResloveReq(self.rs_select_list)
end

---------------------------------------------------------
-- 神饰分解列表 LingHeResloveGrid
---------------------------------------------------------
LingHeResloveGrid = LingHeResloveGrid or BaseClass(AsyncBaseGrid)
function LingHeResloveGrid:IsSelectMultiNumLimit(cell_index)
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= TianShenLingHeWGData.MAX_RESLOVE then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenLingHe.ResloveLimit)
            return true
        end
    end

    return false
end

function LingHeResloveGrid:SetColorSelcet(select_color_list)
    if IsEmptyTable(select_color_list) then
        return
    end

    self.select_tab[1] = {}
    self.cur_multi_select_num = 0
    local data = self.cell_data_list
    for i = 1, self.has_data_max_index do
        if data[i] and select_color_list[data[i].color] then
            if self.cur_multi_select_num < TianShenLingHeWGData.MAX_RESLOVE then
                self.cur_multi_select_num = self.cur_multi_select_num + 1
                self.select_tab[1][i] = true
            else
                break
            end
        end
    end

    self:__DoRefreshSelectState()
end

-------------------------------------------
--神饰分解格子ResoveSSEquipCell
-------------------------------------------
ResoveSSEquipCell = ResoveSSEquipCell or BaseClass(ItemCell)
function ResoveSSEquipCell:__init()
	self:SetIsShowTips(false)
	self:UseNewSelectEffect(true)
end

function ResoveSSEquipCell:SetSelect(is_select)
	self:SetSelectEffect(is_select)
end

-------------------------------------------
-- 神饰获得物品 SSBagResloveGetItem
-------------------------------------------
SSBagResloveGetItem = SSBagResloveGetItem or BaseClass(BaseRender)
function SSBagResloveGetItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.can_get_item)
end

function SSBagResloveGetItem:__delete()
    if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function SSBagResloveGetItem:OnFlush()
    if self.data == nil then
        return
    end

    self.item_cell:SetData({item_id = self.data.item_id})
    self.node_list["can_get_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)
    self.node_list["can_get_num"].text.text = self.data.get_num
end