function KfActivityView:LoadOGADailyRechargeCallBack()
    if not self.recharge_task_list then
        self.recharge_task_list = AsyncListView.New(OGARechargeTaskItemRender, self.node_list["recharge_task_list"])
        self.recharge_task_list:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list["recharge_get_btn"],BindTool.Bind(self.OnClickRechargeRewardBtn, self))
end

function KfActivityView:CloseOGADailyRechargeCallBack()
end

function KfActivityView:ReleaseOGADailyRechargeCallBack()
    if self.recharge_task_list then
        self.recharge_task_list:DeleteMe()
        self.recharge_task_list = nil
    end
end

function KfActivityView:FlushOGADailyRechargeCallBack()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local all_task_info = ServerActivityWGData.Instance:GetOgaRechargeTaskByDay(open_day)
    self.recharge_task_list:SetDataList(all_task_info)

    local is_can_get = ServerActivityWGData.Instance:GetOARechargeRewardRemind()
    self.node_list.recharge_get_btn_red:SetActive(is_can_get)

    local have_recharge_num = ServerActivityWGData.Instance:GetOGARechargeNum()
    self.node_list.recharge_price_text.text.text = have_recharge_num
end

function KfActivityView:OnClickRechargeRewardBtn()
    local is_can_get = ServerActivityWGData.Instance:GetOARechargeRewardRemind()

    if is_can_get then
        ServerActivityWGCtrl.Instance:SendOGAExtendReqOperate(OGA_EXTEND_OPERATE_TYPE_ENUM.FETCH_CUMULATE_REWARD)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.NotGetRewardTips)
    end
end

------------------------------------------- OGARechargeTaskItemRender -------------------------------------------
OGARechargeTaskItemRender = OGARechargeTaskItemRender or BaseClass(BaseRender)

function OGARechargeTaskItemRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
        self.reward_list:SetStartZeroIndex(true)
    end

    self.is_had_get = false
end

function OGARechargeTaskItemRender:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function OGARechargeTaskItemRender:OnFlush()
    self.is_had_get = false

	if not self.data then return end

    local cfg_data = self.data.cfg_data

    local reward = cfg_data.reward or {}
    

    local flag_status = ServerActivityWGData.Instance:GetOGARechargeFetchInfoBySeq(cfg_data.seq)

    local task_str = cfg_data.task_desc
    local target_num = cfg_data.num or 0

    self.is_had_get = flag_status == 1

    local have_recharge_num = ServerActivityWGData.Instance:GetOGARechargeNum()
    local is_can_get = have_recharge_num >= target_num and flag_status == 0
    self.reward_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            --item_cell:SetLingQuVisible()
            item_cell:SetRedPointEff(is_can_get)
        end
    end)
    self.reward_list:SetDataList(reward)

    self.node_list["task_desc_2"].text.text = task_str
    self.node_list["task_desc"].text.text = target_num

    self.node_list["had_get_flag"]:SetActive(self.is_had_get)
end
