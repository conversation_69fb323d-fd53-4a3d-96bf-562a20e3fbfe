OperationTaskChainRewardView = OperationTaskChainRewardView or BaseClass(SafeBaseView)

function OperationTaskChainRewardView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	local assetbundle = "uis/view/common_panel_prefab"
	self:AddViewResource(0, assetbundle, "layout_commmon_second_panel", {sizeDelta = Vector2(685,597), vector2 = Vector2(12,22)})
	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain_reward")
end

function OperationTaskChainRewardView:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function OperationTaskChainRewardView:LoadCallBack()
	self.const_item_id = 0
	self.node_list["title_view_name"].text.text = Language.OpertionAcitvity.TaskChain.RewardViewName
	XUI.AddClickEventListener(self.node_list.img_reward, BindTool.Bind(self.OnClickConstImg, self))
end

function OperationTaskChainRewardView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			-- local is_preview = v ~= nil and (v.is_preview ~= nil and v.is_preview == true) or false
			self:FlushView(false)
		end
	end
end

function OperationTaskChainRewardView:FlushView(is_preview)
	if nil == self.reward_list_view then
		self.reward_list_view = AsyncListView.New(OperationTaskChainRewardRender, self.node_list["ph_item_list"])
	end

	if self.reward_list_view then
		local data_list = OperationTaskChainWGData.Instance:GetShowMingWangRewardList(is_preview)
		if data_list ~= nil then
			self.reward_list_view:SetDataList(data_list)
		end
		
		self.node_list["star_num"].text.text = OperationTaskChainWGData.Instance:GetMingWangValue()
	end

	local cfg =  OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if cfg ~= nil then
		local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item_id)
		if item_cfg ~= nil then
			local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
			self.node_list.img_reward.image:LoadSprite(bundle, asset)
			self.const_item_id = cfg.item_id
		end		
	end
end

function OperationTaskChainRewardView:OnClickConstImg()
	if self.const_item_id > 0 then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.const_item_id})
	end
end

----------------------------------------------------------------------------------------------------------

OperationTaskChainRewardRender = OperationTaskChainRewardRender or BaseClass(BaseRender)

function OperationTaskChainRewardRender:ReleaseCallBack()
	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function OperationTaskChainRewardRender:LoadCallBack()
	self.const_item_id = 0
	self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind(self.OnClickLingQu, self))
	XUI.AddClickEventListener(self.node_list.img_reward, BindTool.Bind(self.OnClickConstImg, self))
end

function OperationTaskChainRewardRender:OnFlush()
	local data = self:GetData()
	if not data or not data.cfg then
		return 
	end

	local reward_list = SortTableKey(data.cfg.reward_item)
	self.reward_item_list:SetDataList(reward_list)

	self.node_list["lbl_lscs"].text.text = data.cfg.mingwang
	self.node_list["btn_lingqu"]:SetActive(data.is_can and not data.is_preview)
	self.node_list["image_ylq"]:SetActive(data.reward_flag == 1 and not data.is_preview)
	self.node_list["btn_weilingqu"]:SetActive(not data.is_can and data.reward_flag == 0 and not data.is_preview)

	local cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if cfg ~= nil then
		local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item_id)
		if item_cfg ~= nil then
			local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
			self.node_list.img_reward.image:LoadSprite(bundle, asset)
			self.const_item_id = cfg.item_id
		end
	end
end

function OperationTaskChainRewardRender:OnClickLingQu()
	if not self.data then
		return
	end

	OperationTaskChainWGCtrl.Instance:SendTaskChainMWRewardFetch(self.data.index)
	local reward_data = {}
	for k,v in pairs(self.data.cfg.reward_item) do
		table.insert(reward_data, v)
	end
	
	TipWGCtrl.Instance:ShowGetReward(nil, reward_data, false, false, nil)
end

function OperationTaskChainRewardRender:OnClickConstImg()
	if self.const_item_id > 0 then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.const_item_id})
	end
end