HuanHuaFetterView = HuanHuaFetterView or BaseClass(SafeBaseView)

function HuanHuaFetterView:__init()
    self:SetMaskBg()
    self.default_index = TabIndex.huanhua_fetter_waiguan
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    local common_bundle_name = "uis/view/common_panel_prefab"
    self:AddViewResource(0, common_bundle_name, "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/huanhua_fetter_ui_prefab", "layout_huanhua_fetter")
    self:AddViewResource(0, common_bundle_name, "VerticalTabbar")
    self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")

    self.remind_tab = {
        { RemindName.HuanhuaFetterWaiguan },
        { RemindName.HuanhuaFetterLingchong },
        { RemindName.HuanhuaFetterMount },
        { RemindName.HuanhuaFetterKun },
    }

    self.tab_index_list = {
        [TabIndex.huanhua_fetter_waiguan] = true,
        [TabIndex.huanhua_fetter_lingchong] = true,
        [TabIndex.huanhua_fetter_mount] = true,
        [TabIndex.huanhua_fetter_kun] = true,
    }

    self.tab_sub = {}
end

function HuanHuaFetterView:__delete()

end

function HuanHuaFetterView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    self:InfoReleaseCallBack()
end

function HuanHuaFetterView:OpenCallBack()
end

function HuanHuaFetterView:CloseCallBack()

end

function HuanHuaFetterView:LoadCallBack()
    if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.CheckVerCellCanOpen, self))
        self.tabbar:Init(Language.HuanHuaFetter.TabGrop, nil, nil, nil, self.remind_tab)
        self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
        self.first_open = true
    end

    self.node_list.title_view_name.text.text = Language.Advanced.QiXingHuaXing
    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true,
            show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    local bundle_bg, asset_bg = ResPath.GetRawImagesJPG("a3_sz_bg_2")
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle_bg, asset_bg)
end

function HuanHuaFetterView:CheckVerCellCanOpen()
    if self.tabbar then
        for index, v in pairs(self.tab_index_list) do
            local can_open = HuanHuaFetterWGData.Instance:GetTabbarVerOrToggleCanOpen(index)
            self.tabbar:SetVerToggleVisble(index, can_open)
            self.tab_index_list[index] = can_open
        end
    end
end

function HuanHuaFetterView:LoadIndexCallBack(index)
    if self.tab_index_list[index] then
        self:InitInfoView()
    end
end

function HuanHuaFetterView:ShowIndexCallBack(index)
    if self.tab_index_list[index] then
        self:InfoShowIndexCallBack()
    end
end

function HuanHuaFetterView:OnFlush(param_t, index)
    --print_error("----OnFlush------", param_t , index)
    for k, v in pairs(param_t) do
        if self.tab_index_list[index] then
            if k == "all" then
                self.flush_wait_flag = true
                self:FlushInfoView()
                self:SelectToggle(v.force_big_type, v.force_suit_type)
            elseif k == "protocol_change" then
                self.flush_wait_flag = true
                self:FlushInfoView()
                self:SelectToggle(nil, nil, true)
            elseif k == "flush_open_toggle" then
                self:CheckVerCellCanOpen()
                self:FlushToggleAllData()
            end
        end
    end
end

function HuanHuaFetterView:CalcShowIndex()
    local index = SafeBaseView.CalcShowIndex(self)
    if self.tab_index_list[index] then
        return index
    end

    for i = TabIndex.huanhua_fetter_waiguan, TabIndex.huanhua_fetter_kun, 10 do
        if self.tab_index_list[i] then
            return i
        end
    end
end
