HolyHeavenlyDomainCampDetailsView = HolyHeavenlyDomainCampDetailsView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainCampDetailsView:__init()
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_camp_details")
end

function HolyHeavenlyDomainCampDetailsView:SetData(data)
    self.select_data = data

    if self.select_data ~= nil then
		if not self:IsOpen() then
			self:Open()
		else
			self:Flush()
		end
	end
end

function HolyHeavenlyDomainCampDetailsView:LoadCallBack()
    if not self.camp_details_role_list_list then
        self.camp_details_role_list_list = AsyncListView.New(HHDCampDetailsRoleListItemRender, self.node_list["camp_details_role_list"])
    end
end

function HolyHeavenlyDomainCampDetailsView:ReleaseCallBack()
    if self.camp_details_role_list_list then
        self.camp_details_role_list_list:DeleteMe()
        self.camp_details_role_list_list = nil
    end

    self.select_data = nil
end

function HolyHeavenlyDomainCampDetailsView:OnFlush()
    if nil == self.select_data then
        return
    end

    -- local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByServerId(self.select_data)
    local camp_cfg = self.select_data
	local city_owner_bundle, city_owner_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_zhanyyy16")

    if not IsEmptyTable(camp_cfg) then
        city_owner_bundle, city_owner_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_zhanyyy" .. camp_cfg.camp_icon)
        self.node_list.desc_country_name.text.text = camp_cfg.camp_name

        local rank_list =  HolyHeavenlyDomainWGData.Instance:GetCampRankPlayerListBySeq(camp_cfg.seq)
        local has_data = not IsEmptyTable(rank_list)
        self.node_list.no_data:CustomSetActive(not has_data)
        self.camp_details_role_list_list:SetDataList(rank_list)
    end

    self.node_list.img_camp.image:LoadSprite(city_owner_bundle, city_owner_asset, function ()
        self.node_list.img_camp.image:SetNativeSize()
    end)
end

---------------------------------HHDCampDetailsRoleListItemRender--------------------------------
HHDCampDetailsRoleListItemRender = HHDCampDetailsRoleListItemRender or BaseClass(BaseRender)

function HHDCampDetailsRoleListItemRender:LoadCallBack()
    if not self.head then
        self.head = BaseHeadCell.New(self.node_list.head_pos)
    end
end

function HHDCampDetailsRoleListItemRender:__delete()
    if self.head then
        self.head:DeleteMe()
        self.head = nil
    end
end

function HHDCampDetailsRoleListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local rank = self.data.rank + 1
    local is_top_three = rank <= 3
    self.node_list.dec_rank.text.text = rank
    self.node_list.dec_rank:CustomSetActive(not is_top_three)
    self.node_list.img_rank:CustomSetActive(is_top_three)
    --self.node_list.desc_name.text.text = string.format(Language.HolyHeavenlyDomain.CampDetailsName, self.data.name) 

    if is_top_three then
        local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. rank)
        self.node_list.img_rank.image:LoadSprite(bundle, asset, function ()
            self.node_list.img_rank.image:SetNativeSize() 
        end)

        self.node_list.img_bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_"..rank))
    end

	local uuid = RoleWGData.Instance:GetUUid()
    local role_info = {}

    local flush_info = function (role_info)
        -- local appearance = role_info and role_info.appearance
	    -- local data = {fashion_photoframe = role_info.fashion_photoframe}
        local data = {}
	    data.role_id = role_info.role_id
	    data.prof = role_info.prof
	    data.sex = role_info.sex
	    -- self.head:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	    self.head:SetData(data)

        self.node_list.desc_name.text.text = string.format(Language.HolyHeavenlyDomain.CampDetailsName, role_info.role_name) 
        self.node_list.desc_cap.text.text = ToColorStr(string.format(Language.HolyHeavenlyDomain.CampDetailsCapName, role_info.capability or 0), COLOR3B.GREEN) 
    end

    if uuid == self.data.uuid then
        role_info = RoleWGData.Instance:GetRoleVo()
        flush_info(role_info)
    else
        local role_id = self.data.uuid.temp_low
        local plat_type = self.data.uuid.temp_high
        BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
            flush_info(protocol)
        end, plat_type, true)
    end
end