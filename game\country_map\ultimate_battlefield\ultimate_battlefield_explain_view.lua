UltimateBattlefieldExplainView = UltimateBattlefieldExplainView or BaseClass(SafeBaseView)

function UltimateBattlefieldExplainView:__init()
	self:SetMaskBg()
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 5), sizeDelta = Vector2(960, 646)})
    self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_ultimate_explain_view")
end

function UltimateBattlefieldExplainView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FlagGrabbingBattlefield.ExplainTitle

    for i = 1, 4 do
        local index = i - 1
        self.node_list[string.format("fgb_explain_text%d", index)].text.text = Language.UltimateBattlefield.UltimateExplainTipsContent[i]
        local raw_str = string.format("a2_zjzc_explain_0%d", i) 
        local bundle, asset = ResPath.GetRawImagesPNG(raw_str)
        self.node_list[string.format("fgb_explain_img%d", index)].raw_image:LoadSprite(bundle, asset)
    end
end

function UltimateBattlefieldExplainView:ReleaseCallBack()
end

function UltimateBattlefieldExplainView:OnFlush(param_t, key)

end