XuYuanFreshPoolReward = XuYuanFreshPoolReward or BaseClass(SafeBaseView)

local ani_ten_flag_t = {}
local ANI_SPEED = 0.15
local MAX_COUNT = 50
local MAX_LINE_COUNT = 5

function XuYuanFreshPoolReward:__init()
    self:SetMaskBg(true,true)
    self.view_layer = UiLayer.Pop
    self.view_name = "XuYuanFreshPoolReward"
    self:AddViewResource(0, "uis/view/xuyuan_freshpool_ui_prefab", "xuyuan_freshpool_reward")
end

function XuYuanFreshPoolReward:__delete()

end

function XuYuanFreshPoolReward:ReleaseCallBack()
    if self.tween_time_quest then
        GlobalTimerQuest:CancelQuest(self.tween_time_quest)
        self.tween_time_quest = nil
    end
    if self.cell_list then
        for i,v in ipairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = {}
    end

    self.click_delay = nil
end

function XuYuanFreshPoolReward:LoadCallBack(index, loaded_times)
    self.cell_list = {}

    XUI.AddClickEventListener(self.node_list.list_mask,BindTool.Bind(self.ImmPlayToBigReward,self))
    XUI.AddClickEventListener(self.node_list.btn_cancel,BindTool.Bind(self.Close,self))
    XUI.AddClickEventListener(self.node_list.fish_btn,BindTool.Bind(self.DrawAgain,self))
    XUI.AddClickEventListener(self.node_list["bx_skip_anim_btn"], BindTool.Bind(self.OnClickBoxSkipAnimBtn, self))
    self.node_list["bx_skip_gou_img"]:SetActive(XuYuanFreshPoolWGData.Instance:GetBoxIsSkipAnim())
end

--初始化格子
local MAX_DELTA = 636
local SINGLE_DELTA = 105
local LINE_COUNT = 10
local ONE_LINE_DELTA = 350
function XuYuanFreshPoolReward:FlushCell(id_list)
    if not id_list then
        return
    end

    local length = math.ceil(#id_list / 10)
    for i = 1, MAX_LINE_COUNT do
        self.node_list['list_' .. i]:SetActive(length >= i)
    end

    if length == 1 then
        self.node_list.bg.rect.sizeDelta = Vector2(1380, ONE_LINE_DELTA)
    else
        self.node_list.bg.rect.sizeDelta = Vector2(1380, MAX_DELTA - SINGLE_DELTA * (5 - length))
    end

    for i = 1, MAX_COUNT do
        if id_list[i] then
            if not self.cell_list[i] then
                self.cell_list[i] = XuYuanFreshPoolRewardSingleCell.New(self.node_list['single_cell_' .. i])
                self.cell_list[i]:SetAnimIndex(i)
            end
            self.cell_list[i]:SetAlpha(false)
            self.cell_list[i]:SetVisible(true)
            -- 刷新格子
            self.cell_list[i]:SetData(id_list[i])
        else
            if self.cell_list[i] then
                self.cell_list[i]:SetAlpha(false)
                self.cell_list[i]:SetVisible(false)
            end
        end
    end


    for i = (length - 1) * LINE_COUNT + 1,length * LINE_COUNT do
        self.node_list['single_cell_' .. i]:SetActive(id_list[i] ~= nil)
    end
end

function XuYuanFreshPoolReward:CloseCallBack()
    if self.cell_list then --关闭的时候直接释放，因为跳过动画后显示会奇怪
        for i,v in ipairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = {}
    end
end

function XuYuanFreshPoolReward:SetData(id_list, stuff_id, stuff_num, btn_text, ok_func, discount_text)
    self.id_list = id_list
    self.stuff_id = stuff_id
    self.stuff_num = stuff_num
    self.btn_text = btn_text
    self.ok_func = ok_func
    self.discount_text = discount_text
end

local Sort_Type = {
    [GameEnum.ITEM_BIGTYPE_EQUIPMENT] = 10,
    [GameEnum.ITEM_BIGTYPE_EXPENSE] = 9,
    [GameEnum.ITEM_BIGTYPE_GIF] = 8,
    [GameEnum.ITEM_BIGTYPE_OTHER] = 7,
    [GameEnum.ITEM_BIGTYPE_VIRTUAL] = 6,
}
function XuYuanFreshPoolReward:SortItem(item_list)
    local item_cfg,item_type
    for i,v in ipairs(item_list) do
        item_cfg,item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
        v.color = item_cfg.color
        v.item_type = Sort_Type[item_type]
    end
    SortTools.SortDesc(item_list,"color","item_type")
    return item_list
end

--刷新数据
function XuYuanFreshPoolReward:OnFlush(param)
    for i, v in pairs(param) do
        if i == "all" then
            self:FlushView()
        end
    end
end

function XuYuanFreshPoolReward:FlushView()
    self.node_list.list_mask:SetActive(true)
    local gift_info = self.id_list

    if self.tween_time_quest then
        GlobalTimerQuest:CancelQuest(self.tween_time_quest)
        self.tween_time_quest = nil
    end

    self:FlushCell(gift_info)
    self:ChangeBlock(false)

    self.tween_index = 1
    self.tween_time_quest = GlobalTimerQuest:AddTimesTimer(function()
        if self.tween_index > MAX_COUNT then
            GlobalTimerQuest:CancelQuest(self.tween_time_quest)
            self.tween_time_quest = nil
            self:ChangeBlock(true)
            return
        end
        if self.cell_list[self.tween_index - 1] and not self.cell_list[self.tween_index - 1]:GetCanPlayNext() then
            return
        end

        if self.cell_list[self.tween_index] then
            self.cell_list[self.tween_index]:DoAnim()
            self.cell_list[self.tween_index]:SetAlpha(true)
        end
        self.tween_index = self.tween_index + 1

        if self.tween_index >= #gift_info then
            self.node_list.list_mask:SetActive(false)
            return
        end
    end, ANI_SPEED, #gift_info * 3)

    if XuYuanFreshPoolWGData.Instance:GetBoxIsSkipAnim() then     --跳过动画
        self:ImmPlayToBigReward()
    end

    -- 刷新按钮
    self:FlushBtn()

    self.node_list["bx_skip_gou_img"]:SetActive(XuYuanFreshPoolWGData.Instance:GetBoxIsSkipAnim())
end

-- 快速播放到下一个大奖
function XuYuanFreshPoolReward:ImmPlayToBigReward()
    for i = self.tween_index, #self.cell_list do
        if self.id_list[i] then
            self.cell_list[i]:ImmShowItem()
        end
        self.tween_index = #self.id_list
        self.node_list.list_mask:SetActive(false)
    end
end

function XuYuanFreshPoolReward:ChangeBlock(enable)
    --for i = 1, 50 do
    --    self.node_list['single_cell_' .. i].canvas_group.blocksRaycasts = enable
    --end
end

function XuYuanFreshPoolReward:DrawAgain()
    if self.click_delay then
        return
    end
    self.click_delay = true
    GlobalTimerQuest:AddDelayTimer(function()
        self.click_delay = nil
    end, 0.3)

    self.ok_func()
    self:Close()
end

function XuYuanFreshPoolReward:FlushBtn()
    local has_num = ItemWGData.Instance:GetItemNumInBagById(self.stuff_id)
    self.node_list["fish_btn_txt"].text.text = self.btn_text
    local color = has_num >= self.stuff_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list["fish_btn_cost"].text.text = ToColorStr(string.format(Language.Activity.ActivityProcess, has_num, self.stuff_num), color)

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["fish_btn_cost"].rect)

    local cfg = ItemWGData.Instance:GetItemConfig(self.stuff_id)
    local bundle, asset = ResPath.GetItem(cfg.icon_id)
    self.node_list["fish_cost_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list["fish_cost_icon"].image:SetNativeSize()
    end)
    self.node_list["fish_cost_icon"].button:AddClickListener(BindTool.Bind(self.OnClickFishCostBtn, self, self.stuff_id))

    local show_discount = self.discount_text ~= ""
    self.node_list.fish_btn_discount:SetActive(show_discount)
    if show_discount then
        self.node_list["fish_txt_discount"].text.text = string.format(Language.Recharge.DiscountText, self.discount_text)
    end

end

function XuYuanFreshPoolReward:OnClickFishCostBtn(item_id)
    if not item_id then
        return
    end
    TipWGCtrl.Instance:OpenItem({item_id = item_id, num = 1, is_bind = 1},ItemTip.FROM_NORMAL)
end

function XuYuanFreshPoolReward:OnClickBoxSkipAnimBtn()
	local last_state = XuYuanFreshPoolWGData.Instance:GetBoxIsSkipAnim()
	self.node_list["bx_skip_gou_img"]:SetActive(not last_state)
	XuYuanFreshPoolWGData.Instance:SetBoxSkipAnimFalg(not last_state)
end

----------------------------------------------------------------------------------------------
XuYuanFreshPoolRewardSingleCell = XuYuanFreshPoolRewardSingleCell or BaseClass(BaseRender)
function XuYuanFreshPoolRewardSingleCell:LoadCallBack()
    self.graphic_raycast = self.node_list.cell:GetComponent(typeof(UnityEngine.UI.GraphicRaycaster))
    self.node_list.cell.image.enabled = false
end

function XuYuanFreshPoolRewardSingleCell:ReleaseCallBack()
    if self.base_cell then
        self.base_cell:DeleteMe()
        self.base_cell = nil
    end
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    self.graphic_raycast = nil
end

function XuYuanFreshPoolRewardSingleCell:SetAlpha(value)
    if self.graphic_raycast then
        self.graphic_raycast.enabled = value
    end
    self.view.canvas_group.alpha = value and 1 or 0
end

local scale1 = Vector3(1.4, 1.4, 1.4) --大小1
local scale2 = Vector3(0.9, 0.9, 0.9) --大小1
local effect_name = {

}
local start_effect_name = {
    [5] = "ui_jinjichenggong",
    [6] = "ui_jinjichenggong",
}

function XuYuanFreshPoolRewardSingleCell:GetCanPlayNext()
    return self.can_play_next or false
end


XuYuanFreshPoolRewardSingleCell.Time = 1
function XuYuanFreshPoolRewardSingleCell:DoAnim()
    self.can_play_next = false

    local cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
    if not cfg then
        return
    end
    local eff_level = cfg.color

    self.view.transform.localScale = scale1
    local scale_tween_2 = self.view.rect:DOScale(scale2,0.3)
    scale_tween_2:SetEase(DG.Tweening.Ease.InQuad)

    self.sequence = DG.Tweening.DOTween.Sequence()
        -- 不同类型的奖励，播放不同的特效
    if self.data.reward_type ~= XuYuanFreshPoolWGData.REWARD_TYPE.LOW then
        --self.sequence:AppendInterval(XuYuanFreshPoolRewardSingleCell.Time)
        local bundle = string.format("effects2/prefab/ui/%s_prefab", string.lower('UI_jinli_saoguang'))
        local asset = 'UI_jinli_saoguang'
        self.node_list.effect_attach.rect:SetAsLastSibling()
        self.node_list.effect_attach:SetActive(true)
        --self.node_list.effect_attach:ChangeAsset(bundle,asset)
    end

    self.sequence:AppendCallback(function ()
        self.can_play_next = true
        self.node_list.effect_attach.rect:SetAsFirstSibling()
        self:PlayStartEffect()
    end)
    self.sequence:Append(scale_tween_2)
    self.sequence:AppendCallback(function ()
        ani_ten_flag_t[#ani_ten_flag_t + 1] = true
        if not effect_name[eff_level] then
            self.node_list.effect_attach:SetActive(false)
            return
        end

        local bundle, asset = ResPath.GetEffectUi(effect_name[eff_level])

        self.node_list.effect_attach:SetActive(true)
        self.node_list.effect_attach:ChangeAsset(bundle,asset)
    end)

end

function XuYuanFreshPoolRewardSingleCell:ImmShowItem()
    self.can_play_next = true
    self:SetAlpha(true)
    self:FlushEffect()
end

function XuYuanFreshPoolRewardSingleCell:PlayStartEffect()
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
    local eff_level = cfg.color

    if not start_effect_name[eff_level] then
        self.node_list.effect_attach:SetActive(false)
        return
    end

    local bundle, asset = ResPath.GetEffectUi(start_effect_name[eff_level])
     self.node_list.effect_attach:SetActive(true)
     self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

function XuYuanFreshPoolRewardSingleCell:FlushEffect()
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
    local eff_level = cfg.color

    if not effect_name[eff_level] then
        self.node_list.effect_attach:SetActive(false)
        return
    end

    local bundle, asset = ResPath.GetEffectUi(effect_name[eff_level])
    if self.node_list.effect_attach and self.node_list.effect_attach.game_obj_attach then
        self.node_list.effect_attach.game_obj_attach.BundleName = nil
    self.node_list.effect_attach.game_obj_attach.AssetName = nil
    end
    self.node_list.effect_attach:SetActive(true)
    self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

function XuYuanFreshPoolRewardSingleCell:OnFlush()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    if not self.data then
        return
    end
    self:FlushBaseCell()
    self.node_list.cell.image.enabled = self.data.is_zhenxi
end

function XuYuanFreshPoolRewardSingleCell:FlushBaseCell()
    if not self.base_cell then
        self.base_cell = ItemCell.New(self.node_list["cell"])
        self.base_cell:SetItemTipFrom(ItemTip.FROM_GET_REWARD)
    end
    self.base_cell:SetData(self.data.reward_item)
end

function XuYuanFreshPoolRewardSingleCell:SetAnimIndex(index)
    self.anim_index = index
end