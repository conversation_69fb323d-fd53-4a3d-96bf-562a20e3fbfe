local check_update = {
	ctrl_state = CTRL_STATE.START,
}

local SysFile = System.IO.File
local SysDirectory = System.IO.Directory
local SysSearchOption = System.IO.SearchOption

function check_update:Start()
	local update_pkg = GLOBAL_CONFIG.param_list.switch_list.update_package
	if update_pkg then
		local old_pkg_ver_nums = string.split(GLOBAL_CONFIG.local_package_info.version, ".")
		local new_pkg_ver_nums = (GLOBAL_CONFIG.version_info.php_package_info.version ~= nil and GLOBAL_CONFIG.version_info.php_package_info.version ~= "")
									and string.split(GLOBAL_CONFIG.version_info.php_package_info.version, ".")
									or {"1", "0", "0"}

		if tonumber(new_pkg_ver_nums[1]) > tonumber(old_pkg_ver_nums[1])
			or tonumber(new_pkg_ver_nums[2]) > tonumber(old_pkg_ver_nums[2])
			or tonumber(new_pkg_ver_nums[3]) > tonumber(old_pkg_ver_nums[3]) then
			-- ReportManager:Step(Report.STEP_UPGRADE)
			-- 删除缓存里面的Lua列表，防止LuaBundleLoader加载到旧的文件
			local lua_manifest_path = ResUtil.GetCachePath("LuaAssetBundle/LuaAssetBundle.lua")
			if SysFile.Exists(lua_manifest_path) then
				os.remove(lua_manifest_path)
			end

			-- 删除客户端资源缓存
			local base_cache_path = string.format("%s/%s", UnityEngine.Application.persistentDataPath, ResUtil.GetFileEncryptPath("BundleCache"))
			if SysDirectory.Exists(base_cache_path) then
				local file_list = SysDirectory.GetFiles(base_cache_path, "*", SysSearchOption.AllDirectories)
				for i = 0, file_list.Length - 1 do
					if SysFile.Exists(file_list[i]) then
						os.remove(file_list[i])
					end
				end
			end

			local package_info = GLOBAL_CONFIG.version_info.php_package_info
			if nil ~= package_info.web_url and "" ~= package_info.web_url then
				InitWGCtrl:ShowMessageBox("更新提示", "有新版本安装包，请前往更新", "前往下载", function ()
					UnityEngine.Application.OpenURL(package_info.web_url)
				end)
			else
				InitWGCtrl:ShowMessageBox("更新提示", "请使用最新版本的游戏包", "退出游戏", function ()
					UnityEngine.Application.Quit()
				end)
			end
		else
			PushCtrl(require("init/init_download"))
			self.ctrl_state = CTRL_STATE.STOP
		end
	else
		PushCtrl(require("init/init_download"))
		self.ctrl_state = CTRL_STATE.STOP
	end
end

function check_update:Update()
	if self.ctrl_state == CTRL_STATE.UPDATE then
		-- print_log("check_update: Update State")
	elseif self.ctrl_state == CTRL_STATE.START then
		self.ctrl_state = CTRL_STATE.UPDATE
		self:Start()
	elseif self.ctrl_state == CTRL_STATE.STOP then
		self.ctrl_state = CTRL_STATE.NONE
		PopCtrl(self)
	end
end

function check_update:Stop()
end

return check_update
