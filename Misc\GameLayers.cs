﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using UnityE<PERSON>ine;

/// <summary>
/// The game layers.
/// </summary>
public static class GameLayers
{
    private static int? walkable;
    private static int? terrain;
    private static int? clickable;
    private static int? mainRole;
    private static int? role;
    private static int? areaAtmosphere;
    private static int? water;
    private static int? bigBuilding;
    private static int? smallBuilding;
    private static int? ui3D;
    private static int? ui3DTerrain;
    private static int? ui3DEffect;
    private static int? uiScene;
    private static int? lodMask;
    private static int? notImportantCulling;
    private static int? notImportantCulling2;
    private static int? importantCulling2;
    private static int? importantCulling3;
    private static int? receiveShadow;
    private static int? castShadow;
    private static int? uiEffect;
    private static int? waterSurface;
    private static int? invisible;
    private static int? ui;
    private static int? uiCg;
    private static int? disableCastShadow;

    /// <summary>
    /// Gets the default layer.
    /// </summary>
    public static int Default
    {
        get { return 0; }
    }

    /// <summary>
    /// Gets the walkable layer.
    /// </summary>
    public static int Walkable
    {
        get
        {
            if (!walkable.HasValue)
            {
                walkable = LayerMask.NameToLayer("Walkable");
            }

            return walkable.Value;
        }
    }

    public static int Terrain
    {
        get
        {
            if (!terrain.HasValue)
            {
                terrain = LayerMask.NameToLayer("Terrain");
            }

            return terrain.Value;
        }
    }

    public static int DisableCastShadow
    {
        get
        {
            if (!disableCastShadow.HasValue)
            {
                disableCastShadow = LayerMask.NameToLayer("DisableCastShadow");
            }

            return disableCastShadow.Value;
        }
    }

    /// <summary>
    /// Gets the clickable layer.
    /// </summary>
    public static int Clickable
    {
        get
        {
            if (!clickable.HasValue)
            {
                clickable = LayerMask.NameToLayer("Clickable");
            }

            return clickable.Value;
        }
    }

    /// <summary>
    /// Gets the area atmosphere layer.
    /// </summary>
    public static int AreaAtmosphere
    {
        get
        {
            if (!areaAtmosphere.HasValue)
            {
                areaAtmosphere = LayerMask.NameToLayer("AreaAtmosphere");
            }

            return areaAtmosphere.Value;
        }
    }

    public static int Water
    {
        get
        {
            if (!water.HasValue)
            {
                water = LayerMask.NameToLayer("Water");
            }

            return water.Value;
        }
    }

    public static int WaterSurface
    {
        get
        {
            if (!waterSurface.HasValue)
            {
                waterSurface = LayerMask.NameToLayer("WaterSurface");
            }

            return waterSurface.Value;
        }
    }

    public static int BigBuilding
    {
        get
        {
            if (!bigBuilding.HasValue)
            {
                bigBuilding = LayerMask.NameToLayer("BigBuilding");
            }

            return bigBuilding.Value;
        }
    }

    public static int SmallBuilding
    {
        get
        {
            if (!smallBuilding.HasValue)
            {
                smallBuilding = LayerMask.NameToLayer("SmallBuilding");
            }

            return smallBuilding.Value;
        }
    }

    public static int UI3D
    {
        get
        {
            if (!ui3D.HasValue)
            {
                ui3D = LayerMask.NameToLayer("UI3D");
            }

            return ui3D.Value;
        }
    }

    public static int UI3DEffect
    {
        get
        {
            if (!ui3DEffect.HasValue)
            {
                ui3DEffect = LayerMask.NameToLayer("UI3DEffect");
            }

            return ui3DEffect.Value;
        }
    }

    public static int UIEffect
    {
        get
        {
            if (!uiEffect.HasValue)
            {
                uiEffect = LayerMask.NameToLayer("UIEffect");
            }

            return uiEffect.Value;
        }
    }

    public static int UI3DTerrain
    {
        get
        {
            if (!ui3DTerrain.HasValue)
            {
                ui3DTerrain = LayerMask.NameToLayer("UI3DTerrain");
            }

            return ui3DTerrain.Value;
        }
    }

    public static int UIScene
    {
        get
        {
            if (!uiScene.HasValue)
            {
                uiScene = LayerMask.NameToLayer("UIScene");
            }

            return uiScene.Value;
        }
    }

    public static int LODMask
    {
        get
        {
            if (!lodMask.HasValue)
            {
                lodMask = LayerMask.NameToLayer("LODMask");
            }

            return lodMask.Value;
        }
    }

    public static int NotImportantCulling
    {
        get
        {
            if (!notImportantCulling.HasValue)
            {
                notImportantCulling = LayerMask.NameToLayer("NotImportantCulling");
            }

            return notImportantCulling.Value;
        }
    }

    public static int NotImportantCulling2
    {
        get
        {
            if (!notImportantCulling2.HasValue)
            {
                notImportantCulling2 = LayerMask.NameToLayer("NotImportantCulling2");
            }

            return notImportantCulling2.Value;
        }
    }

    public static int ImportantCulling2
    {
        get
        {
            if (!importantCulling2.HasValue)
            {
                importantCulling2 = LayerMask.NameToLayer("ImportantCulling2");
            }

            return importantCulling2.Value;
        }
    }

    public static int ImportantCulling3
    {
        get
        {
            if (!importantCulling3.HasValue)
            {
                importantCulling3 = LayerMask.NameToLayer("ImportantCulling3");
            }

            return importantCulling3.Value;
        }
    }

    public static int ReceiveShadow
    {
        get
        {
            if (!receiveShadow.HasValue)
            {
                receiveShadow = LayerMask.NameToLayer("ReceiveShadow");
            }

            return receiveShadow.Value;
        }
    }

    public static int CastShadow
    {
        get
        {
            if (!castShadow.HasValue)
            {
                castShadow = LayerMask.NameToLayer("CastShadow");
            }

            return castShadow.Value;
        }
    }

    public static int Invisible
    {
        get
        {
            if (!invisible.HasValue)
            {
                invisible = LayerMask.NameToLayer("Invisible");
            }

            return invisible.Value;
        }
    }

    public static int UI
    {
        get
        {
            if (!ui.HasValue)
            {
                ui = LayerMask.NameToLayer("UI");
            }

            return ui.Value;
        }
    }

    public static int UICG
    {
        get
        {
            if (!uiCg.HasValue)
            {
                uiCg = LayerMask.NameToLayer("UICG");
            }

            return uiCg.Value;
        }
    }
}
