function BossView:DeleteWorldBossView()
    if self.worldboss_flush_timer then
        GlobalTimerQuest:CancelQuest(self.worldboss_flush_timer)
        self.worldboss_flush_timer = nil
    end

    if self.world_firstkill_tween then
        self.world_firstkill_tween:Kill()
        self.world_firstkill_tween = nil
    end

    if self.vip_firstkill_tween then
        self.vip_firstkill_tween:Kill()
        self.vip_firstkill_tween = nil
    end
end

function BossView:InitWorldBossView()
    self.world_boss_last_flush_times_time = 0
    if self.node_list.btn_worldboss_add then
        XUI.AddClickEventListener(self.node_list.btn_worldboss_add, BindTool.Bind1(self.OnClickWorldBossAdd, self))
    end
    if self.node_list["person_kill_reward_btn"] then
        self.node_list["person_kill_reward_btn"].button:AddClickListener(BindTool.Bind1(self.PersonKillRewardClick, self)) --个人首杀奖励
    end
    if self.node_list["gobal_kill_btn"] then
        self.node_list["gobal_kill_btn"].button:AddClickListener(BindTool.Bind1(self.WorldKillRewardClick, self)) --世界首杀奖励
    end

    if self.node_list["btn_world_firstkill"] then
        self.node_list["btn_world_firstkill"].button:AddClickListener(BindTool.Bind1(self.BossFirstKillClick, self)) --Boss首杀弹窗
    end
end

function BossView:OnClickWorldBossAdd()
    -- local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(SceneType.WorldBoss)
    -- if cfg ~= nil then
    --     BossWGCtrl.Instance:OpenQuickUseView(cfg)
    -- end
    BossWGCtrl.Instance:OpenWorldBossBuyView(VIP_LEVEL_AUTH_TYPE.WORLDBOSS_ENTER_TIMES)
end

function BossView:FlushBossTimes()
    local cur_layer = self:GetCurLayer()
    local boss_enter_cfg = BossWGData.Instance:GetWorldBossEnterCfgByLayer(cur_layer)
    if boss_enter_cfg.is_free == 1 then
        self.node_list["text_world_fatigue2"].text.text = Language.Boss.WorldBossTimesValue ..
        ToColorStr(Language.Boss.WorldBossTimesStr, COLOR3B.DEFAULT_NUM)
    else
        self:FlushWorldBossView()
    end
end

function BossView:FlushWorldBossView()
    self.node_list.world_boss_des.text.text = Language.Boss.WorldBossNorDes
    local cur_times, max_times = BossWGData.Instance:GetWorldBossTimes()
    local color = cur_times <= 0 and COLOR3B.D_RED or COLOR3B.DEFAULT_NUM
    self.node_list["text_world_fatigue2"].text.text = Language.Boss.WorldBossTimesValue ..
        ToColorStr(string.format("%s/%s", cur_times, max_times), color)

    self.node_list.btn_worldboss_add:SetActive(true)
    --策划改需求，不让自动回复次数
    --if world_times_info.world_boss_flush_can_kill_times < wb_other_cfg.day_extra_times_recover_times and world_times_info.world_boss_last_flush_times_time > 0 then
    --self.world_boss_last_flush_times_time = world_times_info.world_boss_last_flush_times_time + wb_other_cfg.world_boss_extra_times_gap
    --self:UpdateWorldBossFlushTime()
    -- if nil == self.worldboss_flush_timer then
    -- 	self.worldboss_flush_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateWorldBossFlushTime, self), 1)
    -- end
    --else
    if self.worldboss_flush_timer then
        GlobalTimerQuest:CancelQuest(self.worldboss_flush_timer)
        self.worldboss_flush_timer = nil
    end
    self.node_list.text_world_num_detial:SetActive(false)
    --end

    local first_kill_red = BossWGData.Instance:GetBossFirstKillRed()
    if self.node_list["world_first_remind"] then
        self.node_list["world_first_remind"]:SetActive(first_kill_red > 0)
    end

    local show_red_point = first_kill_red > 0
    if show_red_point then
        if not self.world_firstkill_tween then
            self.world_firstkill_tween = DG.Tweening.DOTween.Sequence()
            UITween.ShakeAnimi(self.node_list.btn_world_firstkill_icon.transform, self.world_firstkill_tween, 2)
        end
    else
        if self.world_firstkill_tween then
            self.world_firstkill_tween:Kill()
            self.world_firstkill_tween = nil
        end

        self.node_list.btn_world_firstkill_icon.transform.localRotation = Quaternion.Euler(0, 0, 0)
    end
end

function BossView:OnFlushWorldBossView()
    if self.show_index == BossViewIndex.WorldBoss then
        self:FlushWorldBossView()
    elseif self.show_index == BossViewIndex.VipBoss then
        local first_kill_red = BossWGData.Instance:GetBossFirstKillRed()
        if self.node_list["vip_firstkill_remind"] then
            self.node_list["vip_firstkill_remind"]:SetActive(first_kill_red > 0)
        end

        local show_red_point = first_kill_red > 0
        if show_red_point then
            if not self.vip_firstkill_tween then
                self.vip_firstkill_tween = DG.Tweening.DOTween.Sequence()
                UITween.ShakeAnimi(self.node_list.btn_vip_firstkill_icon.transform, self.vip_firstkill_tween, 2)
            end
        else
            if self.vip_firstkill_tween then
                self.vip_firstkill_tween:Kill()
                self.vip_firstkill_tween = nil
            end

            self.node_list.btn_vip_firstkill_icon.transform.localRotation = Quaternion.Euler(0, 0, 0)
        end
    else
        if self.worldboss_flush_timer then
            GlobalTimerQuest:CancelQuest(self.worldboss_flush_timer)
            self.worldboss_flush_timer = nil
        end
    end
end

-- 玩法介绍
function BossView:UpdateWorldBossFlushTime()
    local time = self.world_boss_last_flush_times_time - TimeWGCtrl.Instance:GetServerTime()
    self.node_list.text_world_num_detial:SetActive(time > 0)
    if time > 0 then
        self.node_list.text_world_num_detial.text.text = string.format(Language.BossAssist.WorldBossTimesText,
            TimeUtil.FormatSecond(time, 2))
    else
        if self.worldboss_flush_timer then
            GlobalTimerQuest:CancelQuest(self.worldboss_flush_timer)
            self.worldboss_flush_timer = nil
        end
    end
end
