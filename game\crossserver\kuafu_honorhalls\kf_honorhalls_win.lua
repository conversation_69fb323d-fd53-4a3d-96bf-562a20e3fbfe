KuafuHonorhallWin = KuafuHonorhallWin or BaseClass(SafeBaseView)

function KuafuHonorhallWin:__init(view_name)
	self.view_style = ViewStyle.Half
	self.view_name = "KuafuHonorhallWin"
	self.is_need_depth = true										-- 是否需要开启景深
	self:SetMaskBg(true, true)
	-- self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/kuafu_honorhalls_ui_prefab", "layout_honorhall_win")
end

function KuafuHonorhallWin:LoadCallBack()
	self:InitParam()
	-- self:InitPanel()
	-- self:InitListener()
end

function KuafuHonorhallWin:ReleaseCallBack()
	if self.reward_item_list then
		for k,v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end
end

function KuafuHonorhallWin:OnFlush(param_t)
	self:RefreshView()
end

function KuafuHonorhallWin:InitParam()
	self.reward_item_list = {}
end

-- function KuafuHonorhallWin:InitListener()
-- 	self.node_list.sure_btn.button:AddClickListener(BindTool.Bind(self.Close, self))
-- end

function KuafuHonorhallWin:RefreshView()
	self:RefreshJieSuanInfo()
	self:RefreshRewardList()
end

function KuafuHonorhallWin:RefreshJieSuanInfo()
	self.node_list.victory:CustomSetActive(true)

	local person_rank_info = KuafuHonorhallWGData.Instance:GetPassInfo()
	if not person_rank_info then
		return
	end

	local rank_index = person_rank_info.rank_pos or 0
	if rank_index > 0 then
		self.node_list.rank_label.text.text = string.format(Language.Honorhalls.MyRank2, ToColorStr(rank_index, COLOR3B.GREEN))
	else
		self.node_list.rank_label.text.text = Language.Honorhalls.NoRank
	end

	if person_rank_info.finish_time > 0 then
		local time_str = TimeUtil.FormatSecond2MS(person_rank_info.finish_time)
		self.node_list.time_label.text.text = time_str
	else
		self.node_list.time_label.text.text = Language.Honorhalls.NoComplete
	end
end

function KuafuHonorhallWin:RefreshRewardList()
	local reward_data, title_info = KuafuHonorhallWGData.Instance:GetAllRewardItem()
	if not reward_data then
		return
	end

	local item_list = self.reward_item_list
	if #item_list < #reward_data then
		local parent = self.node_list.item_cell_list
		for i = #item_list + 1, #reward_data do
			item_list[i] = ItemCell.New(parent)
		end
		self.reward_item_list = item_list
	end

	for i=1,#item_list do
		if reward_data[i] then
			item_list[i]:SetData(reward_data[i])
			item_list[i]:SetActive(true)
		else
			item_list[i]:SetActive(false)
		end
	end

	self.node_list.title_panel:SetActive(title_info ~= nil)
	if title_info ~= nil then
		local bundle, asset = ResPath.GetTitleModel(title_info.title_id)
		self.node_list.title_img:ChangeAsset(bundle, asset)	
	end
end
