GuildBattleRankedWGData = GuildBattleRankedWGData or BaseClass()

GuildBattleRankedWGData.ChuangWenType = {
	CaiJi = 1, 		--1 - 采集成功 p0(side) p1(gather采集物id)
	DiaoLuo = 2,	--2 - 灵石掉落 p0(side) p1(gather采集物id)
	ErrorGive = 3,	--3 - 上交灵石给敌方 p0(side) p1(gather采集物id)
	GUILD_BATTLE_NOTIFY_4 = 4,	-- 4 - 3分钟倒计时
	GUILD_BATTLE_NOTIFY_5 = 5,	-- 5 - 灵石刷新
	GUILD_BATTLE_NOTIFY_6 = 6,	-- 6 - 目标总值 p0(side) p1(value)
	GUILD_BOSS_WAIT_TO_FLUSH = 7, --boss即将刷新
	GUILD_BOSS_IS_FLUSH = 8,      --boss已刷新
}

GuildBattleRankedWGData.GUILD_BATTlE_CAMP = {
	BLUETEAM = 0,
	REDTEAM = 1,
}

GuildBattleRankedWGData.GUILD_BOSS_STATE = {
	WAIT_TO_ACTIVE = 0, --等待刷新
	ACTIVE = 1,			--存活
	DIE_BY_ROLE = 2,    --被玩家击杀
	DIE_BY_SYSTEM = 3,  --超时被系统干掉
}

function GuildBattleRankedWGData:__init()
	if GuildBattleRankedWGData.Instance then
		ErrorLog("[GuildBattleRankedWGData] attempt to create singleton twice!")
		return
	end
	GuildBattleRankedWGData.Instance = self
	self.guild_info_list = {}
	self.role_info_list = {}

	self.end_data = {}

	self.guild_battle_auto = ConfigManager.Instance:GetAutoConfig("guild_battle_auto")

	self.show_reward_cfg = self.guild_battle_auto.show_reward

	self.battle_rule_cfg = ListToMapList(self.guild_battle_auto.battle_rule,"rule_perent_index")

	self.continue_kill_cfg = ListToMapList(self.guild_battle_auto.continue_kill_cfg,"type")

	self.lingshi_cfg = self.guild_battle_auto.lingshi

	local guild_battle_auto = self.guild_battle_auto

	self.fenpei_reward_info = {}
	self.fenpei_item_info = {}
	self.person_reward_cfg_list = nil


	self.husong_role_list = {}
	self.lingshi_list = {}
	self.lingshi_list_num = {}
	self.battle_rank_list = {}

	self.guild_batter_boss_info = {}
end

function GuildBattleRankedWGData:__delete()
	self.guild_info_list = nil
	self.role_info_list = nil
	self.guild_batter_boss_info = nil
	GuildBattleRankedWGData.Instance = nil
end

function GuildBattleRankedWGData:SetGuildBattleSceneUserInfo(protocol)
	self.role_info_list = protocol.role_info_list
end


function GuildBattleRankedWGData:SetGuildBattleSceneGlobalInfo(protocol)
	self.guild_info_list = protocol.guild_info_list
	self.guild_info_list.guild_battle_stage = protocol.guild_battle_stage
	self.end_data.delay_kickout_timestamp = protocol.delay_kickout_timestamp
	self.end_data.winner_side = protocol.winner_side
end

function GuildBattleRankedWGData:GetGuildBattleAutoCfg()
	return self.guild_battle_auto
end

function GuildBattleRankedWGData:GetGuildBattleSceneUserInfo()
	return self.role_info_list
end

function GuildBattleRankedWGData:GetGuildBattleSceneGlobalInfo()
	return self.guild_info_list
end


function GuildBattleRankedWGData:GetGuildBattleEndData()
	return self.end_data
end


function GuildBattleRankedWGData:GetGuildBattleSceneReward(stage)
	local guild_battle_auto = self.guild_battle_auto.score_reward
	local is_all_done = true

	if stage ~= #guild_battle_auto - 1 then
		stage = stage + 1
		is_all_done = false
	end

	for k,v in ipairs(guild_battle_auto)do
		if stage == v.seq then
			return v, is_all_done
		end
	end
end

function GuildBattleRankedWGData:GetMaxScore()
	local guild_battle_auto = self.guild_battle_auto.score_reward
	return guild_battle_auto[#guild_battle_auto].need_score
end

function GuildBattleRankedWGData:GetGuildBattleSceneRewardList()
	local guild_battle_auto = self.guild_battle_auto.score_reward
	local reward_fetch_stage = self:GetGuildBattleSceneUserInfo().reward_fetch_stage
	local item_list = {}
	for k,v in ipairs(guild_battle_auto)do
		if v.seq <= reward_fetch_stage then
			for k,v in pairs(v.reward)do
				if item_list[v.item_id] == nil then
					item_list[v.item_id] = 0
				end
				item_list[v.item_id] = item_list[v.item_id] + v.num
			end
		end
	end
	local item_list_2 = {}
	for k,v in pairs(item_list) do
		item_list_2[#item_list_2 + 1] = {item_id = k, num = v}
	end
	return item_list_2
end

function GuildBattleRankedWGData:GetRoleSpecialAppearance()
	local main_role = Scene.Instance:GetMainRole()
	if main_role and main_role.vo.special_appearance == SPECIAL_APPEARANCE_TYPE.XMZ and
		Scene.Instance:GetSceneType() == SceneType.XianMengzhan then
		return true
	end
	return false
end

function GuildBattleRankedWGData:GetEnterSceneRoleMovePos()
	local user_info = self:GetGuildBattleSceneUserInfo()
	if user_info.side == nil then return end
	local auto_position = self.guild_battle_auto.other[1]["auto_position_" .. user_info.side + 1]
	if auto_position == nil then return end
	local move_pos_data = Split(auto_position, "|")

	local logic_pos = {}
	logic_pos.x = move_pos_data[1] + math.random(- move_pos_data[3],  move_pos_data[3])
	logic_pos.y = move_pos_data[2] + math.random(- move_pos_data[3],  move_pos_data[3])

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:DoMoveOperate(logic_pos)
	end
end

--获取定级赛活动结束的时间戳
function GuildBattleRankedWGData:GetDingJiCloseTime()
	local other_cfg = GuildInviteWGData.Instance:GetOtherCfg()
	local time_tab = Split(other_cfg.open_time,"|")
	local hour  = tonumber(time_tab[1])
	local min  = tonumber(time_tab[2])
	local duration_time = other_cfg.duration_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local all_time = TimeUtil.GetEarlyTime(server_time) + hour * 3600 +  min * 60 + duration_time
	return all_time
end

function GuildBattleRankedWGData:GetCfgOther()
	return self.guild_battle_auto.other[1]
end
function GuildBattleRankedWGData:GetActivityLimitLevel()
	return self.guild_battle_auto.other[1].foreshow_level ,self.guild_battle_auto.other[1].forward_day
end
function GuildBattleRankedWGData:SetPreviousRewardFlag(protocol)
	self.previous_reward_flag = protocol.reward_flag
end
function GuildBattleRankedWGData:GetPreviousRewardFlag(protocol)
	return self.previous_reward_flag or 0
end
function GuildBattleRankedWGData:SetSceneBuffInfo( protocol )

	--local side = 0
	self.buff_info_list = protocol.data_list
	-- if self.guild_info_list then
	-- 	side = self.guild_info_list.side
	-- else
	-- 	return
	-- end
	-- local data = {}
	-- if side == 1 then
	-- 	for k,v in pairs(protocol.data_list) do
	-- 		data[#protocol.data_list + 1- k] = v
	-- 	end
	-- end
	-- self.buff_info_list = data
end

function GuildBattleRankedWGData:GetSceneBuffInfo()
	local data = {}
	--local side = 0
	if self.guild_info_list and #self.buff_info_list > 0 and self.guild_info_list.side == 1 then
		for i= #self.buff_info_list,1,-1 do
			table.insert(data,self.buff_info_list[i])
		end
		return data
	end

	return self.buff_info_list or {}
end
function GuildBattleRankedWGData:SetSceneTitleInfo( protocol )
	self.title_info_list = protocol.data_list
end
function GuildBattleRankedWGData:GetSceneTitleInfo( protocol )
	return self.title_info_list or {}
end
--获取帮贡
function GuildBattleRankedWGData:GetGuildBG(ranknum)
	local data ,is_win = GuildWGData.Instance:GetGuildEndDataInfo()
	local rank_reward = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").rank_reward
	for k,v in pairs(rank_reward) do
		if ranknum >= v.min_rank and ranknum <= v.max_rank then
			if is_win == 1 then
				return v.win_banggong
			elseif is_win == 0 then
				return v.failure_banggong
			end
		end
	end
	return 0
end

--获取奖励物品
function GuildBattleRankedWGData:GetGuildReward(ranknum,is_win,zone)
	local rank_reward = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").rank_reward
	local reward_data = {}
	local open_day =  TimeWGCtrl.Instance:GetCurOpenServerDay()
	local num = 1
	local day = nil

	for k,v in pairs(rank_reward) do
		if (nil == day or v.opengame_day == day) and open_day  <= v.opengame_day and ranknum >= v.min_rank
			and ranknum <= v.max_rank and zone == v.zone then
			day = v.opengame_day
			if is_win == 1 then
				reward_data = TableCopy(v.show_reward_item) or {}
				num = v.win_gift_num
			else
				reward_data = TableCopy(v.show_failure_reward_item) or {}
				num = v.fail_gift_num
			end
		end
	end
	return reward_data
end

function GuildBattleRankedWGData:GetBaseRewardCfg(zone)
	if not self.person_reward_cfg_list then
		local offse = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").rank_reward
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local cfg_list = {}
		local function add_tab(tab, key, val)
			tab[key] = tab[key] or {}
			tab[key][#tab[key] + 1] = val
		end
		for i,v in ipairs(offse) do
			add_tab(cfg_list, v.zone, v)
		end
		self.person_reward_cfg_list = cfg_list
	end
	return self.person_reward_cfg_list[zone]
end

function GuildBattleRankedWGData:GetMaxBaseRewardCfg(zone)
	local cfg = self:GetBaseRewardCfg(zone)
	local max_cfg = cfg[#cfg]
	return max_cfg
end

function GuildBattleRankedWGData:GetGuildBattleOpenTime()
	local cfgs = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").other[1]
	local round_1 = cfgs.first_round
	local round_2 = cfgs.second_round
	local table_1 = Split(round_1,"-")
	local table_2 = Split(round_2,"-")

	local table_1_1 = Split(table_1[1],":")  --开始时间 hour min
	local table_1_2 = Split(table_1[2],":")	--结束时间 hour min
	local table_2_1 = Split(table_2[1],":")--开始时间 hour min
	local table_2_2 = Split(table_2[2],":")--结束时间 hour min
	local during_time = (tonumber(table_2_1[2]) - tonumber(table_1_2[2])) * 60  --休息时间

	local all_second = (tonumber(table_2_2[2]) - tonumber(table_1_1[2])) * 60 -- + cfgs.activity_prepare_time
	local round_time_1 = (tonumber(table_1_2[2]) - tonumber(table_1_1[2])) * 60
	local round_time_2 = (tonumber(table_2_2[2]) - tonumber(table_2_1[2])) * 60

	return tonumber(table_1_1[1]), tonumber(table_1_1[2]), during_time, tonumber(table_2_1[1]), tonumber(table_2_1[2]), all_second, round_time_1,round_time_2, tonumber(table_2_2[1]), tonumber(table_2_2[2]), tonumber(table_1_2[1]), tonumber(table_1_2[2])
end

function GuildBattleRankedWGData:GetShowOpenTime()
	local cfgs = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").other[1]
	local round_1 = cfgs.first_round
	local round_2 = cfgs.second_round
	local table_1 = Split(round_1,"-")
	local table_2 = Split(round_2,"-")
	return table_1[1],table_1[2],table_2[1],table_2[2]
end

--获取距离开始时间的前10分钟的时间戳
function GuildBattleRankedWGData:GetShowPaperTimetamp()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").other[1]
	local show_papertime = other_cfg.show_papertime
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_table = Split(show_papertime,"|")  --显示对阵时间 hour min
	local early_time = TimeUtil.GetEarlyTime(server_time)
	local time = early_time + tonumber(time_table[1]) * 3600 + tonumber(time_table[2]) * 60
	return time
end

--获取开始时间的时间戳
function GuildBattleRankedWGData:GetGuildWarOpenTime()
	local time_star_hour, time_star1, during_time, _, time_star2 = self:GetGuildBattleOpenTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local early_time = TimeUtil.GetEarlyTime(server_time)
	local time = early_time + time_star_hour * 3600 + time_star1 * 60
	return time
end

--获取活动结束的时间戳
function GuildBattleRankedWGData:GetGuildWarOpenEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local early_time = TimeUtil.GetEarlyTime(server_time)
	local cfgs = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").other[1]
	local round_2 = cfgs.second_round
	local table_2 = Split(round_2,"-")
	local table_2_2 = Split(table_2[2],":")--结束时间 hour min
	local time = early_time + tonumber(table_2_2[1]) * 3600 + tonumber(table_2_2[2]) * 60
	return time
end


function GuildBattleRankedWGData:GetIsWarFirstOpenTime()
	local cross_open_cfg = BiZuoWGData.Instance:GetCrossActivityOpenTimeCfg(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local limit_day = cross_open_cfg and cross_open_cfg.certain_open_openserver_day
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local activity_info =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local is_first_open = false
	if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
		if (limit_day - open_day) >= 0 then
			is_first_open = true
		end
	end

	return is_first_open
end

--获取是否是中场休息
function GuildBattleRankedWGData:GetPrepareRestTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_table = os.date("*t", server_time)
	local guild_battle_limit_level,limit_day = GuildBattleRankedWGData.Instance:GetActivityLimitLevel()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local time_star_hour, time_star1, during_time, _, time_star2 = self:GetGuildBattleOpenTime()
	if ((time_table.wday == 1 and open_day >= limit_day) or open_day == limit_day) and time_star_hour == time_table.hour and time_star1 * 60 + during_time <= time_table.min * 60 and time_table.min < time_star2 then
		return true
	end
	return false
end

--获取去变身坐标点
function GuildBattleRankedWGData:GetGuildChangePos(side)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").monster_cfg
	for k,v in pairs(monster_cfg) do
		if v.side == side then
			return v.npc_pos_x, v.npc_pos_y
		end
	end
	return 0,0
end


--获取NPCid
function GuildBattleRankedWGData:GetGuildNpcId(side)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").monster_cfg
	for k,v in pairs(monster_cfg) do
		if v.side == side then
			return v.npc_id
		end

	end
	return 0
end

--获取NPCid
function GuildBattleRankedWGData:GetGuildNpcIsCanChanegBody(npc_id)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").monster_cfg
	local side = self:GetGuildBattleSceneUserInfo().side
	for k,v in pairs(monster_cfg) do
		if v.npc_id == npc_id then
			if v.side == side then
				return true
			end
		end

	end
	return false
end

function GuildBattleRankedWGData:GetNpcCfgByNpcId(npc_id)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").monster_cfg
	for k,v in pairs(monster_cfg) do
		if v.npc_id == npc_id then
			return v
		end
	end
end

function GuildBattleRankedWGData:GetGuildGuaJiPos(side)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").monster_cfg
	for k,v in pairs(monster_cfg) do
		if v.side == side then
			return v.guaji_pos_x, v.guaji_pos_y
		end

	end
	return 0,0
end

function GuildBattleRankedWGData:GetMonsterCfgBySide(side)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").monster_cfg
	for k,v in pairs(monster_cfg) do
		if v.side == side then
			return v
		end
	end
end

-- 仙盟战第二轮结算仙盟排行
function GuildBattleRankedWGData:SetGuildRankPaiMingInfo(protocol)
	self.end_rank_info = {}
	self.end_rank_info.is_win_second_round = protocol.is_win
	self.end_rank_info.guild_rank = protocol.guild_rank
	self.end_rank_info.rank_reward_list = protocol.rank_info_list
end

function GuildBattleRankedWGData:GetGuildRankPaiMingInfo()
	return self.end_rank_info
end

function GuildBattleRankedWGData:ClearGuildRankPaiMingInfo()
	if self.end_rank_info then
		self.end_rank_info = nil
	end
end

-- 仙盟战连胜结算信息
function GuildBattleRankedWGData:TestGuildBattleEndKeepWinInfo()
	local data = {}
	data.is_shut_down = 0
	data.keep_win_times = 4
	data.guild_name = "测试"
	self:SetGuildBattleEndKeepWinInfo(data)
end

function GuildBattleRankedWGData:SetGuildBattleEndKeepWinInfo(protocol)
	self.end_keep_info = {}
	self.end_keep_info.is_shut_down = protocol.is_shut_down
	self.end_keep_info.keep_win_times = protocol.keep_win_times
	self.end_keep_info.guild_name = protocol.guild_name
end
function GuildBattleRankedWGData:GetGuildBattleEndKeepWinInfo()
	return self.end_keep_info
end
function GuildBattleRankedWGData:ResertKeepWinInfo()
	self.end_keep_info = nil
end



--仙盟连胜奖励配置num
function GuildBattleRankedWGData:GetGuildRoleOffsetCfg(is_shut_down,num)
	local offse = {}--self.guild_battle_auto.keep_win_reward_new
	local open_day =  TimeWGCtrl.Instance:GetCurOpenServerDay()
	local rand_t = {}
	local day = nil
	if is_shut_down == 0 then
		offse = self.guild_battle_auto.keep_win_reward_new

	else
		offse = self.guild_battle_auto.shut_down_keep_win_reward

	end
	num = offse[1].win_count >= num and offse[1].win_count or num
	num = offse[#offse].win_count <= num and offse[#offse].win_count or num
	--print_error("++++++++++++",open_day,is_shut_down,num)
	-- if num >= offse[#offse].win_count then
	-- 	return offse[#offse]
	-- end
	for k,v in pairs(offse) do
		if nil ~= v.opengame_day then
			if v and (nil == day or v.opengame_day == day) and open_day  <= v.opengame_day and v.win_count == num then
				day = v.opengame_day
				--print_error(k,day)
				return v
				--
				-- table.insert(rand_t, v)
			end
		end
	end
	return nil
end

function GuildBattleRankedWGData:GetGuildRoleOffsetCfgPanel(num)
	local offse = self.guild_battle_auto.shut_down_keep_win_reward
	local open_day =  TimeWGCtrl.Instance:GetCurOpenServerDay()
	local rand_t = {}
	local day = nil
	num = offse[1].win_count >= num and offse[1].win_count or num
	num = offse[#offse].win_count <= num and offse[#offse].win_count or num
	for k,v in pairs(offse) do
		if nil ~= v.opengame_day then
			if v and (nil == day or v.opengame_day == day) and open_day  <= v.opengame_day then
				day = v.opengame_day
				if v.win_count == num then
					return v
				elseif v.win_count > num then
					return offse[k - 1]
				end
			end
		end
	end
end

function GuildBattleRankedWGData:GetGuildWarReward()
	local cfg_list = {}
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for k,v in pairs(self.show_reward_cfg) do
		if open_day >= v.opengame_day_min and open_day < v.opengame_day_max then
			cfg_list = v
			return cfg_list
		end
	end

	return cfg_list
end

function GuildBattleRankedWGData:GetBattleRuleCfg()
	return self.battle_rule_cfg
end

function GuildBattleRankedWGData:GetBattleRuleByIndexCfg(index)
	return self.battle_rule_cfg[index]
end

--获取分配奖励   分配奖励包括 {仙盟排名奖励 连胜奖励  终结奖励}
function GuildBattleRankedWGData:GetFenPeiReward()
	local sort_list = {}
	local reward_list = {}
	local rank = 1
	local rank_reward_cfg = GuildWGData.Instance:GetGuildItemRewardByRankNum(rank)
	local rank_reward = rank_reward_cfg and rank_reward_cfg.show_reward_item or {}
	self:SortFenPeiRewawrd(rank_reward,sort_list)
	local info =  GuildBattleRankedWGData.Instance:GetGuildBattleEndKeepWinInfo()
	if info then
		local reward_info = GuildBattleRankedWGData.Instance:GetGuildRoleOffsetCfg(info.is_shut_down, info.keep_win_times)
		local offset_reward = {}
		if info.is_shut_down == 0 then
			offset_reward = reward_info and reward_info.win_reward_item or {}
		else
			offset_reward = reward_info and reward_info.defeat_reward_item or {}
		end
		self:SortFenPeiRewawrd(offset_reward,sort_list)
	end
	for k,v in pairs(sort_list) do
		table.insert(reward_list,v)
	end
	table.sort(reward_list,function (a,b)
		if a and b and a.item_id and b.item_id and a.item_id ~= b.item_id then
			local a_cfg = ItemWGData.Instance:GetItemConfig(a.item_id)
			local b_cfg = ItemWGData.Instance:GetItemConfig(b.item_id)
			if a_cfg and a_cfg.color and b_cfg and b_cfg.color then
				return a_cfg.color > b_cfg.color
			else
				return false
			end
		end
	end )

	return reward_list
end

function GuildBattleRankedWGData:SortFenPeiRewawrd(reward_cfg,reward_list)
	if not IsEmptyTable(reward_cfg) then
		for i = 0, #reward_cfg do
			local cfg = reward_cfg[i]
			if cfg then
				if reward_list[cfg.item_id] == nil then
					reward_list[cfg.item_id] = {item_id = cfg.item_id, num = cfg.num or 1, is_bind = cfg.is_bind or 1}
				else
					reward_list[cfg.item_id].num = reward_list[cfg.item_id].num + (cfg.num or 1)
				end
			end
		end
	end
end


------仙盟争霸奖励分配------
function GuildBattleRankedWGData:TestSendProtocol()

	local data1 = {}
	data1.reward_info = {}
	for i=1,10 do
		local data = {}
		data.apply_info = {}
		data.item_id = 5128
		data.item_num = 11
		-- local role_num = MsgAdapter.ReadInt()
		for i=1,20 do
			local role_info = {}
			role_info.role_id = 100000
			role_info.role_name = "盟友"..i
			table.insert(data.apply_info,role_info)
		end
		data1.reward_info[data.item_id] = data
	end
	self:SetSCGuildBattleRewardAlloc(data1)

	local data2 = {}
	data2.get_num = 1
	local count = 10
	data2.item_info = {}
	for i=1,count do
		local data = {}
		data.item_id = 5128
		data.num = 11
		data.is_bind = 1
		data.is_apply = i % 2 == 1 and 1 or 0
		-- data2.item_info[data.item_id] = data
		table.insert(data2.item_info,data)
	end
	self:SetSCGuildBattleRewardInfo(data2)
end

function GuildBattleRankedWGData:SetSCGuildBattleRewardAlloc(protocol)
	self.fenpei_reward_info = protocol.reward_info
end

function GuildBattleRankedWGData:SetSCGuildBattleRewardInfo(protocol)
	self.fenpei_item_info = protocol.item_info
	self.fenpei_get_num = protocol.get_num
	table.sort(self.fenpei_item_info,function (a,b)
		if a and b and a.item_id and b.item_id and a.item_id ~= b.item_id then
			local a_cfg = ItemWGData.Instance:GetItemConfig(a.item_id)
			local b_cfg = ItemWGData.Instance:GetItemConfig(b.item_id)
			if a_cfg and a_cfg.color and b_cfg and b_cfg.color then
				return a_cfg.color > b_cfg.color
			else
				return false
			end
		end
	end )
end

function GuildBattleRankedWGData:GetFenPeiRewardInfo()
	return self.fenpei_reward_info
end

function GuildBattleRankedWGData:GetFenPeiItemInfo()
	return self.fenpei_item_info
end

function GuildBattleRankedWGData:GetFenPeiGetNum()
	return self.fenpei_get_num or 0
end

function GuildBattleRankedWGData:GetApplyRoleList(item_id)
	local info = self.fenpei_reward_info[item_id]
	return info and info.apply_info or {}
end

function GuildBattleRankedWGData:GetFenPeiFirstEffect()
	return self.fenpei_first_effect
end

function GuildBattleRankedWGData:SetFenPeiFirstEffect(flag)
	self.fenpei_first_effect = flag
end

function GuildBattleRankedWGData:GetFenPeiRemind()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local guild_post = role_vo.guild_post
	local is_mengzhu = guild_post == GUILD_POST.TUANGZHANG or guild_post == GUILD_POST.JiaMengZhu
	if is_mengzhu then
		local fenpei_reward_info = self:GetFenPeiRewardInfo()
		if IsEmptyTable(fenpei_reward_info) then
			return 0
		else
			local fenpei_item_info = self:GetFenPeiItemInfo()
			if IsEmptyTable(fenpei_item_info) then
				return 0
			end
			for k,v in pairs(fenpei_reward_info) do
				if v.apply_info and #v.apply_info > 0 then
					return 1
				end
			end
		end
	else
		local fenpei_item_info = self:GetFenPeiItemInfo()
		if IsEmptyTable(fenpei_item_info) then
			return 0
		else
			local fenpei_get_num = self:GetFenPeiGetNum()
			if fenpei_get_num >= GUILD_BATTLE_FENPEI_REWARD_LIMIT_NUM then
				return 0
			else
				for k,v in pairs(fenpei_item_info) do
					if v.is_apply == 0 then
						return 1
					end
				end
			end
		end 
	end
	return 0
end

function GuildBattleRankedWGData:GetContinueKillCfg(type,kill_num)
	local cfg = self.continue_kill_cfg[type]
	if cfg then
		for k,v in pairs(cfg) do
			if v.kill_count > kill_num then
				return cfg[k-1]
			end
			if k >= #cfg then
				return v
			end
		end
	end
	
end

function GuildBattleRankedWGData:GetGuildRankRewardId(id)
	local cfg_list = self.guild_battle_auto.guild_rank_reward_id
	for k,v in pairs(cfg_list) do
		if v.reward_id == id then
			return v
		end
	end
end

local map_width = 884
local map_height = 664
-- logic坐标转ui坐标
function GuildBattleRankedWGData:LogicToUI(logic_x, logic_y)
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		return 0, 0
	end

	if logic_x == nil or logic_y == nil then
		return 0, 0
	end

	local wx, wy = GameMapHelper.LogicToWorld(logic_x, logic_y)
	local uipos = mini_camera:TransformWorldToUV(Vector3(wx, 0, wy))
	local ui_x, ui_y = map_width * uipos.x, map_height * uipos.y
	return ui_x, ui_y
end


-- ui坐标转logic坐标
function GuildBattleRankedWGData:UIToLogic(ui_x, ui_y)
	local mini_camera = Scene.Instance:GetSceneMiniMapCamera()
	if IsNil(mini_camera) then
		return 0, 0
	end

	local uipos_x = ui_x / map_width
	local uipos_y =  ui_y / map_height
	local world_pos = mini_camera:TransformUVToWorld(Vector2(uipos_x, uipos_y))
	local logic_x, logic_y = GameMapHelper.WorldToLogic(world_pos.x, world_pos.z)
	return logic_x, logic_y
end

function GuildBattleRankedWGData:SetSCCrossGuildBattleHusongRoleInfo(protocol)
	self.husong_role_list = protocol.husong_role_list
end

function GuildBattleRankedWGData:SetSCCrossGuildBattleLingshiInfo(protocol)
	self.lingshi_list = protocol.lingshi_list
	self.lingshi_list_num = protocol.lingshi_list_num
	self.fresh_timestamp = protocol.fresh_timestamp
end


function GuildBattleRankedWGData:SetGuildBattleEndRankInfo(protocol)
	self.battle_rank_list = protocol.rank_list
	self.is_win = protocol.is_win
	self.cur_zone = protocol.zone
	table.sort(self.battle_rank_list,function (a,b)
		if a and b and a.rank and b.rank then
			return a.rank < b.rank
		end
	end )
end

function GuildBattleRankedWGData:GetHuSongRoleList()
	return self.husong_role_list or {} 
end

function GuildBattleRankedWGData:GetLingShiList()
	return self.lingshi_list or {}
end

function GuildBattleRankedWGData:GetHuSongRoleListByIndex(index)
	return self.husong_role_list and self.husong_role_list[index]
end

function GuildBattleRankedWGData:GetMyHuSongRoleInfo()
	local uuid = RoleWGData.Instance:GetUUid()
	for k,v in pairs(self.husong_role_list) do
		if uuid == v.uuid then
			return v
		end
	end
end

function GuildBattleRankedWGData:GetLingShiListByIndex(index)
	return self.lingshi_list and self.lingshi_list[index]
end

function GuildBattleRankedWGData:GetLingShiListNumById(gather_id)
	return self.lingshi_list_num and self.lingshi_list_num[gather_id] or 0
end


function GuildBattleRankedWGData:GetLingShiListNum()
	return self.lingshi_list_num
end

function GuildBattleRankedWGData:GetLingShiCfg()
	return self.lingshi_cfg
end

function GuildBattleRankedWGData:GetLingShiCfgById(gather_id)
	return self.lingshi_cfg[gather_id]
end

function GuildBattleRankedWGData:GetBattleRankList()
	return self.battle_rank_list
end

function GuildBattleRankedWGData:GetMaxBattleRankList()
	return self.battle_rank_list and self.battle_rank_list[1]
end

function GuildBattleRankedWGData:GetLingShiFreshTimestamp()
	return self.fresh_timestamp or 0
end

-- 获取当前战车的主动技能
function GuildBattleRankedWGData:GetXMZCarSkill()
	local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
    local skill_str_list = Split(other_cfg.skill_list, "|", true)
	return skill_str_list, {1,1,1,1,1,1,1,1,1,1}
end

-- 是否是战车技能
function GuildBattleRankedWGData:IsXMZCarSkill(skill_id)
	local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
    local skill_str_list = Split(other_cfg.skill_list, "|")
    for i,v in ipairs(skill_str_list) do
    	if tonumber(v) == skill_id then
    		return true
    	end
    end
    return false
end

function GuildBattleRankedWGData:GetHSXSIdByCfg(id)
	for k,v in pairs(self.lingshi_cfg) do
		if v.hs_xs_id == id then
			return v
		end
	end
end

function GuildBattleRankedWGData:SetFirstEnterScene(flag)
	self.first_enter_scene = flag
end

function GuildBattleRankedWGData:GetFirstEnterScene()
	return self.first_enter_scene
end

function GuildBattleRankedWGData:SetCurActStatus(status)
	self.cur_act_status = status
end

function GuildBattleRankedWGData:GetCurActStatus()
	return self.cur_act_status
end

function GuildBattleRankedWGData:GetMonsterBatterArrayFlag()
    return self.guild_battle_auto and self.guild_battle_auto.boss_cfg
end

function GuildBattleRankedWGData:GetMonsterInfoByBossId(boss_id)
	local boss_info = ListToMapList(self.guild_battle_auto.boss_cfg,"boss_id")
	return boss_info and boss_info[boss_id] or {}
end

function GuildBattleRankedWGData:IsTheSameBatterArrayMonster(monster_id)
	local batterarray_flag = self:GetGuildBossSide(monster_id)
	return batterarray_flag == self.role_info_list.side
end

function GuildBattleRankedWGData:GetMonsterGuaJiPos(index)
	local boo_cfg = self:GetMonsterBatterArrayFlag()
	local boss_id =  boo_cfg[index].boss_id
	for k, v in pairs(boo_cfg) do
		if v.boss_id == boss_id then
			return v.boss_pos_x, v.boss_pos_y
		end
	end
	return nil
end

function GuildBattleRankedWGData:GetGuildBossSide(monster_id)
	local boo_cfg = self:GetMonsterBatterArrayFlag()
	for k, v in pairs(boo_cfg) do
		if v.boss_id == monster_id then
			return v.zone
		end
	end
end

function GuildBattleRankedWGData:SetGuildBossInfo(protocol)
	self.guild_batter_boss_info = protocol and protocol.guild_boss_cfg and protocol.guild_boss_cfg or {}
end

function GuildBattleRankedWGData:GetGuildBossInfo()
	return self.guild_batter_boss_info
end

function GuildBattleRankedWGData:UpdateGuildBossInfo(boss_state_info)
	local has_update = false
	local boss_side = -1
	for k, v in pairs(self.guild_batter_boss_info) do
		if v.boss_state_type ~= boss_state_info[k].boss_state then
			if v.boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.WAIT_TO_ACTIVE and boss_state_info[k].boss_state == GuildBattleRankedWGData.GUILD_BOSS_STATE.ACTIVE then
				boss_side = k -1	
			end
			v.boss_state_type = boss_state_info[k].boss_state
			has_update = true
		end
	end
	return has_update, boss_side
end

function GuildBattleRankedWGData:GetLingShiPosCfg(gather_id)
	local lingshi_pos_cfg = self.guild_battle_auto.lingshi_pos
	local pos_x,pos_y = lingshi_pos_cfg[1].pos_x,lingshi_pos_cfg[1].pos_y
	for k, v in pairs(lingshi_pos_cfg) do
		if v.lingshi_gather == gather_id then
			pos_x,pos_y = v.pos_x, v.pos_y
			break
		end
	end
	return pos_x,pos_y
end