CopperSaoDangView = CopperSaoDangView or BaseClass(SafeBaseView)

function CopperSaoDangView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_copper_saodang")
	self.saodang_index = -1
	self.item_data_change_callback = BindTool.Bind1(self.ShowIndexCallBack, self)
end

function CopperSaoDangView:__delete()
end

function CopperSaoDangView:ReleaseCallBack()
	if self.saodang_cell then
		self.saodang_cell:DeleteMe()
		self.saodang_cell = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function CopperSaoDangView:LoadCallBack()
	local ph = self.node_list["ph_cell"]
	self.saodang_cell = ItemCell.New(ph)

	self.node_list["btn_ok"].button:AddClickListener(BindTool.Bind(self.OnClink<PERSON>k<PERSON><PERSON><PERSON>, self))
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClinkCancelHandler, self))

	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function CopperSaoDangView:ShowIndexCallBack()
	self:Flush()
end

function CopperSaoDangView:OnFlush()
	local star_num = FuBenPanelWGData.Instance:SetStarDisplay(self.saodang_index)
	local str = ""
	if star_num and star_num > 0 then
		str = string.format(Language.FuBenPanel.CopperSaoDangTips, star_num, star_num)	
	end
	self.node_list["rich_saodang_des"].text.text = str

	local other_cfg = FuBenPanelWGData.Instance:GetTongBiBenOtherCfg()
	if self.saodang_cell then
		self.saodang_cell:SetData({item_id = other_cfg.sweep_item_id})
	end
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_item_id)

	self.node_list["lbl_item_num"].text.text = item_num .. "/" .. other_cfg.sweep_consum_item_num

end

function CopperSaoDangView:SetCopperIndex(index)
	self.saodang_index = index

	--print_error("index", index)
	self:Open()
end

function CopperSaoDangView:OnClinkOkHandler()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyCountCfg()
	local copper_info = FuBenPanelWGData.Instance:GetTongBiInfo()
	local time = vip_buy_cfg["param_" .. role_vip] - copper_info.day_buy_times
	local other_cfg = FuBenPanelWGData.Instance:GetTongBiBenOtherCfg()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_item_id)
	local copper_level_cfg = FuBenPanelWGData.Instance:GetCopperTitleCfg()
	local role_level = RoleWGData.Instance.role_vo.level
	if item_num < other_cfg.sweep_consum_item_num then
		GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, other_cfg.sweep_item_id, other_cfg.sweep_consum_item_num, other_cfg.seq)
		return
	end

	if not FuBenPanelWGCtrl.Instance:CheckCopperCount() and time > 0  then
		FuBenPanelWGCtrl.Instance:OpenCopperBuy(true)
	else
		FuBenPanelWGData.Instance:SetSaoDangMark(true)
		FuBenPanelWGCtrl.Instance:SendTongBiFbOperate(COIN_FB_OPERA_TYPE.COIN_FB_OPERA_TYPE_SWEEP_FB, self.saodang_index - 1)
	end
end

function CopperSaoDangView:OnClinkCancelHandler()
	self:Close()
end
