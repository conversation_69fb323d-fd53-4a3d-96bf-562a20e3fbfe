-- 解锁战令界面
BossZhanLingUnlockView = BossZhanLingUnlockView or BaseClass(SafeBaseView)

function BossZhanLingUnlockView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_zhanling_unlock_view")
end

function BossZhanLingUnlockView:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncBaseGrid.New()
        self.reward_list:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list.reward_list})
        self.reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_buy_zhanling, BindTool.Bind(self.OnClickBugZhanLingBtn, self))
end

function BossZhanLingUnlockView:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function BossZhanLingUnlockView:OnFlush()
    local reward_cfg = BossZhanLingWGData.Instance:GetCurGradeSeniorOrderCfg()
    if IsEmptyTable(reward_cfg) then
        return
    end

    self.reward_list:SetDataList(reward_cfg.order_preview)
    local get_higer_order = BossZhanLingWGData.Instance:GetHigerOrderRewardFlag()
    self.node_list.flag_is_get:CustomSetActive(get_higer_order)
    self.node_list.btn_buy_zhanling:CustomSetActive(not get_higer_order)

    if not get_higer_order then
        local price = RoleWGData.GetPayMoneyStr(reward_cfg.price, reward_cfg.rmb_type, reward_cfg.rmb_seq)
        self.node_list.buy_text.text.text = string.format(Language.BossZhanLing.ZhanLingBuyLevelBtn, price)
    end

    if reward_cfg.return_lingyu and reward_cfg.return_lingyu > 0 and reward_cfg.return_score and reward_cfg.return_score > 0 then
        self.node_list.desc_lingyu.text.text = string.format(Language.BossZhanLing.ZhanLingBackLingYu, reward_cfg.return_score, reward_cfg.return_lingyu)
    else
        self.node_list.desc_lingyu.text.text = ""
    end
end

function BossZhanLingUnlockView:OnClickBugZhanLingBtn()
    local reward_cfg = BossZhanLingWGData.Instance:GetCurGradeSeniorOrderCfg()

    if IsEmptyTable(reward_cfg) then
        return
    end

    RechargeWGCtrl.Instance:Recharge(reward_cfg.price, reward_cfg.rmb_type, reward_cfg.rmb_seq)
end