-------------------
--化鲲分解背包
-------------------

local BREAK_BAG_NUM = 40

HuaKunBagView = HuaKunBagView or BaseClass(SafeBaseView)

function HuaKunBagView:__init()
	
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(842, 540)})
	self:AddViewResource(0, "uis/view/mount_lingchong_ui_prefab", "layout_kun_resolve")
	self.view_name = "HuaKunBagView"
	self.analy_num = 0
	self.select_list = {}
end

function HuaKunBagView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.MountLingChong.HuaKunBagTipTitle
    self.node_list.layout_commmon_second_root.rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list.layout_commmon_second_root.rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	if not self.huakun_bag_list then
		self.huakun_bag_list = AsyncBaseGrid.New()
		self.huakun_bag_list:CreateCells({col = 8, cell_count = BREAK_BAG_NUM, list_view = self.node_list["ph_bag_grid"], itemRender = HuaKunBagItem})
		self.huakun_bag_list:SetSelectCallBack(BindTool.Bind(self.SelectCellCallBack, self))
		self.huakun_bag_list:SetStartZeroIndex(false)
		self.huakun_bag_list:SetIsMultiSelect(true)
	end

	XUI.AddClickEventListener(self.node_list.btn_kun_resolve, BindTool.Bind1(self.OnAnalyBtnClick, self))
	-- 物品变化事件
   	self.item_data_event = BindTool.Bind(self.BagDataChange,self)
   	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
   	local material_cfg = NewAppearanceWGData.Instance:GetKunMaterial()
   	local item_cfg = ItemWGData.Instance:GetItemConfig(material_cfg[1].param1)
   	self.node_list.huakun_getitem.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
end

function HuaKunBagView:ReleaseCallBack()
	if self.huakun_bag_list then
		self.huakun_bag_list:DeleteMe()
		self.huakun_bag_list = nil
	end
	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
 		self.item_data_event = nil
    end
end

-- 一键分解
function HuaKunBagView:OnAnalyBtnClick()
	local list = {}
	for k, v in pairs(self.select_list) do
		table.insert(list, v.index)
	end
	if #list <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.MountLingChong.HuaKunAnalyTips)
	else
		NewAppearanceWGCtrl.Instance:SendKunBreakDown(#list, list)
	end
end

function HuaKunBagView:BagDataChange(item_id, index, reason, put_reason, old_num, new_num)
	self.select_list = {}
	self:Flush()
end

--  刷新仓库
function HuaKunBagView:OnFlush()
	local list_data = NewAppearanceWGData.Instance:GetKunResloveBagList()
	self.huakun_bag_list:SetDataList(list_data)
	-- self.huakun_bag_list:CancleAllSelectCell()

	self.huakun_bag_list:SetAllSelectCell(#list_data)--默认全选
	for _,v in pairs(list_data) do
		self.select_list[v.index] = v
	end

	self:FlushAnalyGetNum()
end

function HuaKunBagView:FlushAnalyGetNum()
	self.analy_num = 0
	for k,v in pairs(self.select_list) do
		local cfg = NewAppearanceWGData.Instance:GetKunStuffCfgByItemId(v.item_id)
		self.analy_num = self.analy_num + cfg.param2 * v.num
	end
	self.node_list.resolve_num.text.text = self.analy_num
end

function HuaKunBagView:UpdateOneCell()
	if self.huakun_bag_list then
		self.huakun_bag_list:CancleAllSelectCell()
	end
end

function HuaKunBagView:SelectCellCallBack(cell)
	if cell == nil or next(cell:GetData()) == nil then
		return
	end

	if not cell:IsSelect() then
		if self.select_list[cell.data.index] then
			self.select_list[cell.data.index] = nil
		end
	else
		self.select_list[cell.data.index] = cell.data		
	end

	self:FlushAnalyGetNum()
end
------------------------------
HuaKunBagItem = HuaKunBagItem or BaseClass(ItemCell)

function HuaKunBagItem:__init()
	self.is_showtip = false
	self:UseNewSelectEffect(true)
end

function HuaKunBagItem:OnSelectChange(is_select)
	self:SetSelectEffect(is_select)
end