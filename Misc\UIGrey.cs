﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UIGrey : MonoBehaviour {

    private bool isGrey = false;

    public bool GetIsGrey()
    {
        return isGrey;
    }

    public void SetIsGrey(bool isGrey, Material greyMaterial)
    {
        if (this.isGrey == isGrey) return;
        if (isGrey && null == greyMaterial) return;

        this.isGrey = isGrey;

        var graphics = ListPool<UnityEngine.UI.Graphic>.Get();
        this.GetComponentsInChildren<UnityEngine.UI.Graphic>(true, graphics);
        for (int i = 0; i < graphics.Count; i++)
        {
            int layer = graphics[i].gameObject.layer;
            if (graphics[i] is SrpUIEffect
                || graphics[i].tag == "UIEffect" // 考虑旧特效
                || layer == GameLayers.UIEffect || layer == GameLayers.UI3DEffect)
            {
                continue;
            }

            graphics[i].material = isGrey ? greyMaterial : null;
        }

        ListPool<UnityEngine.UI.Graphic>.Release(graphics);
    }
}
