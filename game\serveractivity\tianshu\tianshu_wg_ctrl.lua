
require("game/serveractivity/tianshu/tianshu_wg_data")
require("game/serveractivity/tianshu/tianshu_view")
require("game/serveractivity/tianshu/tianshu_boss_tip")
require("game/serveractivity/tianshu/tianshu_get_skill_view")

TianShuWGCtrl = TianShuWGCtrl or BaseClass(BaseWGCtrl)

function TianShuWGCtrl:__init()
	if nil ~= TianShuWGCtrl.Instance then
		print("[TianShuWGCtrl] attempt to create singleton twice!")
		return
	end
	TianShuWGCtrl.Instance = self
	self.data = TianShuWGData.New()
	self.view = TianShuView.New(GuideModuleName.TianShuView)
	self.boss_tip = TianShuBossTip.New()
	self.tianshu_get_skill_view = TianShuGetSkillView.New()
	self:RegisterAllProtocols()
	self.equip_data_change_fun = BindTool.Bind1(self.OnEquipDataChange, self)
	EquipWGData.Instance:NotifyDataChangeCallBack(self.equip_data_change_fun)

	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	self.task_change_call_back = GlobalEventSystem:Bind(OtherEventType.TASK_CHANGE, BindTool.Bind1(self.OnOneTaskDataChange, self))
end

function TianShuWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.boss_tip then
		self.boss_tip:DeleteMe()
		self.boss_tip = nil
	end

	if nil ~= self.tianshu_get_skill_view then
		self.tianshu_get_skill_view:DeleteMe()
		self.tianshu_get_skill_view = nil
	end

	if self.equip_data_change_fun then
		EquipWGData.Instance:UnNotifyDataChangeCallBack(self.equip_data_change_fun)
		self.equip_data_change_fun = nil
	end

	if self.task_change_call_back then
		GlobalEventSystem:UnBind(self.task_change_call_back)
		self.task_change_call_back = nil
	end
	if self.old_value ~= nil then
		self.old_value = nil
	end
	TianShuWGCtrl.Instance = nil
end

function TianShuWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCTianshuXunzhuInfo,"OnTianshuXunzhuInfo")
	self:RegisterProtocol(SCRoleKillBossIDList,"OnRoleKillBossIDList")

	self:RegisterProtocol(SCChengZhangTianShuInfo,"OnChengZhangTianShuInfo")
	self:RegisterProtocol(CSChengZhangTianShuFetchReward)
end

-- 打开主窗口
function TianShuWGCtrl:Open()
	self.view:Open()
end

function TianShuWGCtrl:OnTianshuXunzhuInfo(protocol)
	self.data:SetTianshuXunzhuInfo(protocol)
	self:UpdateTianshuIcon()
	self.view:Flush()
end

function TianShuWGCtrl:OnRoleKillBossIDList(protocol)
	self.data:SetRoleKillBossIDList(protocol)
end

function TianShuWGCtrl:SetBossTipData(data)
	self.boss_tip:SetData(data)
end

function TianShuWGCtrl:CloseView()
	self.view:Close()
end

function TianShuWGCtrl:OnEquipDataChange(item_id,index,reason)
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TIANSHU_XUNZHU_REQ_INFO)
end

function TianShuWGCtrl:GetTianshuRemind()
	return self.data:GetAllRemind() + self.data:GetChengZhangTisnShuRemindByIndex()
end

-- function TianShuWGCtrl:MainuiOpenCreate()
-- 	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TIANSHU_XUNZHU_REQ_INFO)
-- end

function TianShuWGCtrl:UpdateTianshuIcon()
	--region 目前天书没走开服入口 先不开启 by tansen
	-- local is_can_close = WelfareWGData.Instance:GetSevenLoginDayIsClose()
	-- if TimeWGCtrl.Instance:GetCurOpenServerDay() == 8 or (not self.data:IsGetAllReward() and not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPEN_SERVER) and TaskWGData.Instance:GetTaskIsCompleted(self.data:GetTianshuPerTaskId()))  then
	-- 	local cur_timestamp = TimeWGCtrl.Instance:GetServerTime()
	-- 	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER, ACTIVITY_STATUS.OPEN, cur_timestamp + COMMON_CONSTS.MAX_LOOPS)
	-- elseif self.data:IsGetAllReward() and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPEN_SERVER) and TimeWGCtrl.Instance:GetCurOpenServerDay() > 8 and not ActivityWGCtrl.Instance:IsOpenServerOpen() and is_can_close then
	-- 	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER, ACTIVITY_STATUS.CLOSE)
	-- end
	--endregion
end

function TianShuWGCtrl:OnOneTaskDataChange(task_event_type, task_id)
	if task_event_type == TASK_EVENT_TYPE.ACCEPTED or task_event_type == TASK_EVENT_TYPE.COMPLETED then
		if FunOpen.Instance:GetFunIsOpened(FunName.ActOpenServerScroll) then
			self:UpdateTianshuIcon()
			GlobalEventSystem:UnBind(self.task_change_call_back)
			self.task_change_call_back = nil
		end
	end
end

function TianShuWGCtrl:OnChengZhangTianShuInfo(protocol)
	self.data:SetChengZhangTianShuInfo(protocol)
	self.view:Flush()
end

function TianShuWGCtrl:SendChengZhangTianShuFetchReward(index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChengZhangTianShuFetchReward)
	protocol.index = index
	protocol:EncodeAndSend()
end
--上古卷轴按钮红点是否展示
function TianShuWGCtrl:IsTianShuShowRedRemind()
	if not TaskWGData.Instance:GetTaskIsCompleted(TianShuWGData.Instance:GetTianshuPerTaskId()) then
		return false
	end
	local all_fetch_flag_t = TianShuWGData.Instance:GetAllFetchFlag()
	local index = 1
	if nil ~= all_fetch_flag_t then
		for i=1,#all_fetch_flag_t do
			if not all_fetch_flag_t[i] then
				index = i
				break
			end
		end
	end
	for i=1,5 do
		if i == GameEnum.TIANSHU_CHENGZHANG_TYPE then
			local final_reward_item, fetch_flag, can_fetch_flag = TianShuWGData.Instance:GetChengZhangFinalRewardByIndex()
			if not fetch_flag and can_fetch_flag then
				return true
			else
				local data_list = TianShuWGData.Instance:GetChengZhangTisnShuDataListByIndex()
				if not data_list then return false end
				for k=1,#data_list do --判断天书是否有可领取的
					if 1==data_list[k].can_fetch_flag and 0 == data_list[k].fetch_flag then
						return true
					end
				end
			end
		else
			local final_reward_item, fetch_flag, can_fetch_flag = TianShuWGData.Instance:GetFinalRewardByIndex(i)
			if not fetch_flag and can_fetch_flag and i <= index then
				return true
			else
				local data_list = TianShuWGData.Instance:GetTisnShuDataListByIndex(i)
				if not data_list then return false end
				for j=1,#data_list do
					if 1 == data_list[j].can_fetch_flag and  0 == data_list[j].fetch_flag and i <= index then
						return true
					end
				end
			end
		end
	end
	--不需要展示上古卷轴按钮
	-- if TianShuWGData.Instance:IsGetAllReward() and not TaskWGData.Instance:GetTaskIsCompleted(TianShuWGData.Instance:GetTianshuPerTaskId() then
	-- 	return false
	-- end
	return false
end


--符文精华改变提示
function TianShuWGCtrl:GetCurMingWWenJingHua(protocol)
	if self.old_value == nil or self.old_value ~= protocol.rune_jinghua then
		if self.view:IsOpen() then
			if self.old_value < protocol.rune_jinghua then
				local str = string.format(Language.TianShuXunZhu.GetMingWenHint,protocol.rune_jinghua - self.old_value)
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end
		end
		self.old_value = protocol.rune_jinghua
	end
end

function TianShuWGCtrl:ShowGetNewSkillView(skill_id)
	if self.tianshu_get_skill_view then
		self.tianshu_get_skill_view:ShowView(skill_id)
	end
end



