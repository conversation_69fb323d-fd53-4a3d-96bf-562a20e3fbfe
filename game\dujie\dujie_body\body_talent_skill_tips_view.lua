BodyTalentSkillTipsView = BodyTalentSkillTipsView or BaseClass(SafeBaseView)

function BodyTalentSkillTipsView:__init()
	self:SetMaskBg(true,true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/dujie_body_ui_prefab", "layout_talent_skill_tips_view")
end

function BodyTalentSkillTipsView:LoadCallBack()
	-- if not self.talent_skill_list then
	-- 	self.talent_skill_list = AsyncListView.New(BodySkillItemRender, self.node_list.talent_attr_list)
	-- end

    -- 初始化11个技能
	if not self.next_desc_list then
        self.next_desc_list = {}

        for i = 1, 10 do
            local cell_obj = self.node_list.next_desc:FindObj(string.format("next_title_root_%d", i))
            if cell_obj then
                local cell = TalentSkillNextDescRender.New(cell_obj)
                cell:SetIndex(i)
                self.next_desc_list[i] = cell
            end
        end
	end
end

function BodyTalentSkillTipsView:ReleaseCallBack()
    if self.next_desc_list and #self.next_desc_list > 0 then
        for i, next_desc_cell in ipairs(self.next_desc_list) do
            next_desc_cell:DeleteMe()
            next_desc_cell = nil
        end

        self.next_desc_list = nil
    end 

end

function BodyTalentSkillTipsView:CloseCallBack()
    self.data  = nil
end


function BodyTalentSkillTipsView:OnFlush()
    if not self.data then
        return 
    end

    if self.data.is_special then
        local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_"..self.data.body_seq)
        self.node_list.ph_ml_skill_item.image:LoadSprite(bundle, asset)

        self.node_list.skill_name.tmp.text = ""
        
        self.node_list.scroll_view:SetActive(true)
        self.node_list.body_group:SetActive(true)
        self.node_list.cur_desc:SetActive(false)

        -- 品质 灵根名字+完美度+品质
        local body_info = DujieWGData.Instance:GetBodyInfo(self.data.body_seq)
        local body_cfg = DujieWGData.Instance:GetBodyCfgBySeq(self.data.body_seq)
        local body_score_cfg, next_body_score_cfg = DujieWGData.Instance:GetBodyScoreCfg(self.data.body_seq, body_info.score)

        self.node_list.text_body_quality.tmp.text = string.format(Language.Dujie.BodyQualityStr, body_cfg.body_name, body_score_cfg.level_txt)
        -- 进度
        if next_body_score_cfg then
            self.node_list.img_scorce_progress.image.fillAmount = body_info.score/next_body_score_cfg.need_score
        else
            self.node_list.img_scorce_progress.image.fillAmount = 1
        end
        -- 评分
        if next_body_score_cfg then
            self.node_list.text_body_score.tmp.text = body_info.score .. "/" .. next_body_score_cfg.need_score
        else
            self.node_list.text_body_score.tmp.text = body_info.score .. "/" .. body_score_cfg.need_score
        end

        local score_cfg = DujieWGData.Instance:GetBodyScoreCfgByBody(self.data.body_seq)
        local skill_desc_list_height = 0
        -- 下一级新表现
        for i, next_desc_cell in ipairs(self.next_desc_list) do
            if score_cfg[i] ~= nil then
                next_desc_cell:SetVisible(true)
                next_desc_cell:SetData(score_cfg[i])

                -- 加上Spacing
                skill_desc_list_height = skill_desc_list_height + 10
                skill_desc_list_height = skill_desc_list_height + next_desc_cell:GetHeight()
            else
                next_desc_cell:SetVisible(false)
            end
        end
        -- 加上Top
        if skill_desc_list_height > 0 then
            skill_desc_list_height = skill_desc_list_height + 4
        end

        self:ChangePanelHeight(skill_desc_list_height)
    else
        if self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
            local skill_cfg = DujieWGData.Instance:GetTalentSkillCfg(self.data.info.skill_seq)
            local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_2_"..skill_cfg.skill_icon)
            self.node_list.ph_ml_skill_item.image:LoadSprite(bundle, asset)
        elseif self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.EFFECT then
            local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_1_"..self.data.info.element + 1)
            self.node_list.ph_ml_skill_item.image:LoadSprite(bundle, asset)
        end


        local desc, skill_name = DujieWGData.Instance:GetTalentSkillDesc(self.data.info.skill_seq, self.data.body_seq)
        self.node_list.skill_dsc.tmp.text = desc
        self.node_list.skill_name.tmp.text = skill_name
        
        self.node_list.cur_desc:SetActive(true)
        self.node_list.scroll_view:SetActive(false)
        self.node_list.body_group:SetActive(false)

    end

end

function BodyTalentSkillTipsView:SetData(data)
    self.data = data
end

-- 设置高度
function BodyTalentSkillTipsView:ChangePanelHeight(skill_desc_list_height)
	local scroll_view = self.node_list["scroll_view"]
	local scroll_content = self.node_list["layout_skill_tip"]
	-- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(cur_desc.rect)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(scroll_content.rect)

	local max_center_height = 424
	local scroll_content_height = scroll_content.rect.sizeDelta.y
	local cur_desc_height = 0

	local scroll_view_height = 0
    -- print_error("scroll_content_height:",scroll_content_height)
	if scroll_content_height > max_center_height then
		scroll_view.layout_element.preferredHeight = max_center_height
		scroll_view.scroll_rect.vertical = true
		scroll_view_height = max_center_height
	else
		scroll_view.layout_element.preferredHeight = scroll_content_height
		scroll_view.scroll_rect.vertical = false
		scroll_view_height = scroll_content_height
	end

	if skill_desc_list_height then
		local percent = 0
		local scroll_max = scroll_content_height - scroll_view_height
		if skill_desc_list_height > scroll_max then
			percent = 1
		else
			percent = skill_desc_list_height / scroll_max 
		end
		scroll_view.scroll_rect.verticalNormalizedPosition = percent
	else
		scroll_view.scroll_rect.verticalNormalizedPosition = 0
	end
	-- scroll_view.scroll_rect.verticalNormalizedPosition = 1
end


----------------------------------------------------技能描述-------------------------------------------------
--技能描述对象
TalentSkillNextDescRender = TalentSkillNextDescRender or BaseClass(BaseRender)
function TalentSkillNextDescRender:OnFlush()
	if not self.data then
		return
	end
	
    local body_info = DujieWGData.Instance:GetBodyInfo(self.data.body_seq)

    if body_info.score >= self.data.need_score then
        self.node_list.common_flag_active:SetActive(true)
        self.node_list.common_flag_not_active:SetActive(false)
    else
        self.node_list.common_flag_active:SetActive(false)
        self.node_list.common_flag_not_active:SetActive(true)
    end

    local desc, skill_name = DujieWGData.Instance:GetTalentSkillDesc(self.data.skill_seq, self.data.body_seq)
	self.node_list.title.tmp.text = self.data.level_txt
	self.node_list.desc.tmp.text = desc
end

function TalentSkillNextDescRender:GetHeight()
	local height = 30
	height = self.node_list.desc.tmp.preferredHeight + 30
	return height
end









