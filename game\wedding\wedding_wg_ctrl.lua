require("game/wedding/wedding_wg_data")
-- require("game/wedding/wedding_invite")
require("game/wedding/wedding_send_to_couple")
require("game/wedding/wedding_send_gift")
require("game/wedding/wedding_send_gift_progress")
require("game/wedding/wedding_demand")
require("game/wedding/marry_danmu_view")

WeddingWGCtrl = WeddingWGCtrl or BaseClass(BaseWGCtrl)

function WeddingWGCtrl:__init()
	if WeddingWGCtrl.Instance ~= nil then
		ErrorLog("[WeddingWGCtrl] Attemp to create a singleton twice !")
	end
	WeddingWGCtrl.Instance = self
	self.data = WeddingWGData.New()
	self.send_to_couple_view = WeddingSendCoupleView.New()
	self.send_gift_view = WeddingSendGiftView.New()
	self.send_gift_progress_view = WeddingSendGiftProgressView.New()
	self.wedding_demand_view = WeddingDeMandView.New(GuideModuleName.Marry_DeMandView) 		--索要请帖界面
	self.marry_danmu_view = MarryDanMuView.New(GuideModuleName.MarryDanMu) --弹幕界面
	self:RegisterAllProtocols()

	self.invite_tip_icon_list = {}
	self.invalid_key_list = {}
end

function WeddingWGCtrl:__delete()
	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.send_to_couple_view ~= nil then
		self.send_to_couple_view:DeleteMe()
		self.send_to_couple_view = nil
	end

	if self.send_gift_view ~= nil then
		self.send_gift_view:DeleteMe()
		self.send_gift_view = nil
	end

	if self.wedding_demand_view ~= nil then
		self.wedding_demand_view:DeleteMe()
		self.wedding_demand_view = nil
	end

	if self.send_gift_progress_view ~= nil then
		self.send_gift_progress_view:DeleteMe()
		self.send_gift_progress_view = nil
	end

	if self.marry_danmu_view ~= nil then
		self.marry_danmu_view:DeleteMe()
		self.marry_danmu_view = nil
	end


	WeddingWGCtrl.Instance = nil
end

function WeddingWGCtrl:RegisterAllProtocols()
	-- 注册接收到的协议
	self:RegisterProtocol(SCHunyanInfo, "OnHunyanInfo")
	self:RegisterProtocol(SCMarryHunyanOpera, "OnMarryHunyanOpera")
	self:RegisterProtocol(SCHunyanGuestInfo, "OnHunyanGuestInfo")
	self:RegisterProtocol(SCWebSpecialParamChange, "OnWebSpecialParamChange")
	self:RegisterProtocol(SCWeddingBlessingRecordInfo, "OnWeddingBlessingRecordInfo")
	self:RegisterProtocol(SCWeddingBarrageInfo, "OnSCWeddingBarrageInfo")--弹幕信息

	-- 注册发送的协议
	self:RegisterProtocol(CSMarryHunyanOpera)
	self:RegisterProtocol(CSJoinHunyan)
	self:RegisterProtocol(CSMarryHunyanBless)
	self:RegisterProtocol(CSWeddingUseYanhua)

end

function WeddingWGCtrl:WeddingInviteViewOpen()
	-- self.wedding_invite_view:Open()
end

function WeddingWGCtrl:WeddingSendCoupleViewOpen()
	self.send_to_couple_view:Open()
end

function WeddingWGCtrl:WeddingSendGiftViewOpen()
	self.send_gift_view:Open()
end

function WeddingWGCtrl:WeddingSendGiftViewFlush()
    if self.send_gift_view:IsOpen() then
        self.send_gift_view:Flush()
    end
end

function WeddingWGCtrl:WeddingSendGiftProgressViewOpen()
	self.send_gift_progress_view:Open()
end


function WeddingWGCtrl:WeddingDeMandViewOpen(is_need_time)
	local info = MarryWGData.Instance:GetCurWeddingInfo()
	if not info or IsEmptyTable(info) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Calendar.HunyanClose)
		return
	end
	local callback = function ()
		if is_need_time then
			ViewManager.Instance:Open(GuideModuleName.Marry_DeMandView, nil, "is_need_time", {is_need_time = true})
		else
			ViewManager.Instance:Open(GuideModuleName.Marry_DeMandView)
		end
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,callback)
end

function WeddingWGCtrl:FlushWeddingDeMandView()
	if self.wedding_demand_view:IsOpen() then
		self.wedding_demand_view:Flush()
	end
end

function WeddingWGCtrl:WeddingSetQuickBuyId(item_id, wedding_id, seq)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg then
    	ShopTip.Instance:SetData(item_cfg, 2, GameEnum.SHOP, nil, seq, nil, 999)
    end
end

function WeddingWGCtrl:WeddingDeMandViewClose()
	if self.wedding_demand_view:IsOpen() then
		self.wedding_demand_view:Close()
	end
end


function WeddingWGCtrl:FlushWeddindSendGiftProgress()
	if self.send_gift_progress_view:IsOpen() then
		self.send_gift_progress_view:Flush()
	end
end

function WeddingWGCtrl:WeddingAllViewClose()
	if self.send_to_couple_view:IsOpen() then
		self.send_to_couple_view:Close()
	end
	if self.send_gift_view:IsOpen() then
		self.send_gift_view:Close()
	end
	if self.send_gift_progress_view:IsOpen() then
		self.send_gift_progress_view:Close()
	end
end

-- 婚宴副本请求
function WeddingWGCtrl:SendCSMarryHunyanOpera(opera_type, opera_param1, opera_param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMarryHunyanOpera)
	protocol.opera_type = opera_type or 0
	protocol.opera_param1 = opera_param1 or 0
	protocol.opera_param2 = opera_param2 or 0
	protocol:EncodeAndSend()
end

-- 婚宴贺礼请求
function WeddingWGCtrl:SendMarryHunyanBless(merry_uid, zhufu_type, contenttxt_len, contenttxt)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMarryHunyanBless)
	protocol.merry_uid = merry_uid or 0
	protocol.zhufu_type = zhufu_type or 0
	protocol.contenttxt_len = contenttxt_len or 0
	protocol.contenttxt = contenttxt or 0
	protocol:EncodeAndSend()
end

-- 婚宴副本请求
function WeddingWGCtrl:SendJoinHunyan(fb_key)
	if not FuBenWGCtrl.CanEnterFuben() then
		return
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSJoinHunyan)
	protocol.fb_key = fb_key
	protocol:EncodeAndSend()
end

-- 婚宴副本信息
function WeddingWGCtrl:OnHunyanInfo(protocol)
	self.data.marry_scence_id = protocol.info.fb_key
	--------------------------------------------------------------------

	if HUNYAN_NOTIFY_REASON.HUNYAN_NOTIFY_REASON_INVITE_FRIEND == protocol.info.notify_reason or
		HUNYAN_NOTIFY_REASON.HUNYAN_NOTIFY_REASON_INVITE_GUILD == protocol.info.notify_reason then
		self.invalid_key_list[protocol.info.fb_key] = nil --resume
	end

	if (protocol.info.hunyan_state == WeddingWGData.READY or protocol.info.hunyan_state == WeddingWGData.STATE) and Scene.Instance:GetSceneType() ~= SceneType.HunYanFb then
	else
		self:RemoveInviteTip(protocol.info.fb_key)
	end

	if self.send_gift_view:IsOpen() then
		self.send_gift_view:Flush()
	end
	if self.send_gift_progress_view:IsOpen() then
		self.send_gift_progress_view:Flush()
	end
	FuBenWGCtrl.Instance:UpdataTaskFollow()
end

-- 烟花次数协议
function WeddingWGCtrl:OnHunyanGuestInfo(protocol)
	WeddingWGData.Instance:SetHunyanGuestInfo(protocol)
	if self.send_gift_view:IsOpen() then
		self.send_gift_view:Flush()
	end
	if self.send_gift_progress_view:IsOpen() then
		self.send_gift_progress_view:Flush()
	end
end

-- 婚宴特效播放信息
function WeddingWGCtrl:OnMarryHunyanOpera(protocol)
	WeddingWGData.Instance:SetHunyanOpera(protocol)
	if protocol.opera_type == HUNYAN_TYPE.HUNYAN_OPERA_TYPE_HUAYU then
		-- effect_cfg = Effect_Red_Flower
		-- ParticleEffectSys.Instance:StopEffect(Effect_Red_Flower.name) --先停止，防止屏幕过多
		-- ParticleEffectSys.Instance:StopEffect(Effect_Blue_Flower.name)
		-- ParticleEffectSys.Instance:PlayEffect(effect_cfg, effect_cfg.name, nil, effect_cfg.name)
	end
	if protocol.opera_type == 1 then
		-- 	--播放烟花特效 TODO 2019-01-08 21:04
		local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
		local bundle, asset = nil, nil
		local duration = 6
		if protocol.opera_param == 22100 then
		    --放烟火特效
			bundle, asset = ResPath.GetEnvironmentCommonEffect(Ui_Effect.effect_yanhua_zise)
		elseif protocol.opera_param == 22101 then
			bundle, asset = ResPath.GetEnvironmentCommonEffect(Ui_Effect.effect_yanhua_da)
		end
		if obj then
			local position = obj:GetRoot().transform.position
			EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(position.x, position.y + 2, position.z))
		end
	end
end

function WeddingWGCtrl:RemoveInvalidHunyanInfo()
	for k,v in pairs(self.invalid_key_list) do
		if true == v then
			WeddingWGData.Instance:RemoveHunyanInfoByKey(k)
			self.invalid_key_list[k] = nil
		end
	end
end

----------------------------------------------------
-- invite tip
----------------------------------------------------

function WeddingWGCtrl:AddAllInviteTip()
	for k,v in pairs(WeddingWGData.Instance:GetHunyanList()) do
		if self.invalid_key_list[k] == nil then
			self:AddInviteTip(k)
		end
	end
end

function WeddingWGCtrl:AddInviteTip(hunyan_key)
	if self.invite_tip_icon_list[hunyan_key] then return end

	local icon = MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.WEDDING, 10,
					BindTool.Bind2(self.ClickInviteTipHandler, self, hunyan_key))

	if nil ~= icon then
		self.invite_tip_icon_list[hunyan_key] = icon
	end
end

function WeddingWGCtrl:RemoveInviteTip(hunyan_key)

	local icon = self.invite_tip_icon_list[hunyan_key]
	if nil == icon then return end

	MainuiWGCtrl.Instance:RemoveTipIconByIconObj(icon)
	self.invite_tip_icon_list[hunyan_key] = nil
end

function WeddingWGCtrl:ClickInviteTipHandler(hunyan_key, tip_icon)
	if not WeddingWGData.Instance:GetIsOwnHunyan(hunyan_key) then
		self:RemoveInviteTip(hunyan_key)
		self.invalid_key_list[hunyan_key] = true
	end
end

function WeddingWGCtrl:RemoveAllInviteTip()
	for k,v in pairs(self.invite_tip_icon_list) do
		self:RemoveInviteTip(k)
	end
	self.invite_tip_icon_list = {}
end

----------------------------------------------------
-- 在婚宴上生成一次性烟花特效
----------------------------------------------------
function WeddingWGCtrl:OnShow(is_show)
	if is_show then
		self.send_gift_view:Close()
		self.send_gift_progress_view:Close()
	else
		self.send_gift_view:Open()
		self.send_gift_progress_view:Open()
	end
end

----------------------------------------------------
-- 结婚者进入场景
----------------------------------------------------
function WeddingWGCtrl:OnWebSpecialParamChange(protocol)
	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if obj ~= nil and obj:IsRole() then
	 	obj:SetAttr("special_param", protocol.param)
	 	obj:UpdateNameBoard()
	 	Scene.Instance:SceneSpecialHandler(obj)
	end
	WeddingWGData.Instance:SetSpecialParamChange(protocol)
end

-- 祝福历史
function WeddingWGCtrl:OnWeddingBlessingRecordInfo(protocol)
	self.data:SetBlessingRecordInfo(protocol)
	if self.send_to_couple_view:IsOpen() then
		self.send_to_couple_view:Flush()
	end
end

function WeddingWGCtrl:FlushSendToCouple()
	if self.send_to_couple_view:IsOpen() then
		self.send_to_couple_view:Flush()
	end
end

function WeddingWGCtrl:DelDanMuRender(index)
	if self.marry_danmu_view:IsOpen() then
		self.marry_danmu_view:DelDanMuRender(index)
	end
end

--婚宴弹幕信息
function WeddingWGCtrl:OnSCWeddingBarrageInfo(protocol)
	self.data:SetMarryDanMuInfo(protocol)
	if self.marry_danmu_view:IsOpen() then
		self.marry_danmu_view:Flush()
	end
end

--婚宴鲜花祝福
function WeddingWGCtrl:SendCSWeddingUseYanhua(item_id, num, barrage)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWeddingUseYanhua)
	protocol.item_id = item_id or 0
	protocol.num = num or 0
	protocol.barrage = barrage or "0"
	protocol:EncodeAndSend()
end

--设置婚宴祝福的list高度
function WeddingWGCtrl:SetListHight(index, hight)
	if self.send_to_couple_view then
		self.send_to_couple_view:SetListHight(index, hight)
	end
end


