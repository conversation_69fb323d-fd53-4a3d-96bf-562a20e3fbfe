require("game/cat_explore/cat_explore_wg_data")
require("game/cat_explore/cat_explore_view")

CatExploreWGCtrl = CatExploreWGCtrl or BaseClass(BaseWGCtrl)

function CatExploreWGCtrl:__init()
	if CatExploreWGCtrl.Instance then
		ErrorLog("[CatExploreWGCtrl] attempt to create singleton twice!")
		return
	end

	CatExploreWGCtrl.Instance = self
	self.data = CatExploreWGData.New()
    self.view = CatExploreView.New(GuideModuleName.CatExploreView)
  
    self:RegisterAllProtocols()

    self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
end

function CatExploreWGCtrl:__delete()
	CatExploreWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function CatExploreWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local other_cfg = CatExploreWGData.Instance:GetOtherCfg()
    if change_item_id == other_cfg.cost_item_id then
        ViewManager.Instance:FlushView(GuideModuleName.CatExploreView, nil, "flush_cost")
        RemindManager.Instance:Fire(RemindName.CatExplore)
    end
end

function CatExploreWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOACatVentureInfo,"OnSCOACatVentureInfo")
	self:RegisterProtocol(SCOACatVentureTaskUpdate,"OnSCOACatVentureTaskUpdate")
end

function CatExploreWGCtrl:ReqCatExploreInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function CatExploreWGCtrl:OnSCOACatVentureInfo(protocol)
	--print_error("========猫猫========",protocol)
	local flag = self.data:GetInfoFlag()
	local old_step = self.data:GetCurStep()
	if flag and old_step < protocol.step then
		ViewManager.Instance:FlushView(GuideModuleName.CatExploreView, nil, "flush_cat_step")
	else
		ViewManager.Instance:FlushView(GuideModuleName.CatExploreView)
	end

	local old_reward_state = self.data:GetAllRewardState()
	local reward_list = {}
	local reward_cfg = self.data:GetAllStepRewardCfg()
	if not IsEmptyTable(old_reward_state) then
		for k, v in pairs(protocol.step_reward_flag) do
			if old_reward_state[k] ~= v and old_reward_state[k] == 0 then
				table.insert(reward_list, reward_cfg[k].item)
			end
		end
	end

	if not IsEmptyTable(reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end

	self.data:SetAllCatExploreInfo(protocol)
	self.data:SetInfoFlag()
	RemindManager.Instance:Fire(RemindName.CatExplore)
end

function CatExploreWGCtrl:OnSCOACatVentureTaskUpdate(protocol)
	--print_error("========任务========",protocol)
	self.data:SetSingleTaskInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.CatExploreView, nil, "flush_task")
	RemindManager.Instance:Fire(RemindName.CatExplore)
end
