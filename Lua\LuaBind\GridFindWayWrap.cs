﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class GridFindWayWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(GridFindWay), typeof(System.Object));
		<PERSON><PERSON>RegFunction("LoadData", LoadData);
		<PERSON><PERSON>RegFunction("FindWay", FindWay);
		<PERSON><PERSON>Function("GenerateInflexPoints", GenerateInflexPoints);
		<PERSON><PERSON>unction("GetPathLength", GetPathLength);
		L.RegFunction("GetPathPoint", GetPathPoint);
		L.RegFunction("IsWaterWay", IsWaterWay);
		<PERSON><PERSON>RegFunction("IsBlock", IsBlock);
		<PERSON><PERSON>unction("IsInSafeArea", IsInSafeArea);
		L<PERSON>RegFunction("IsTunnelArea", IsTunnelArea);
		<PERSON><PERSON>RegFunction("IsBorder", IsBorder);
		<PERSON><PERSON>Function("<PERSON>High<PERSON><PERSON>", IsHighArea);
		<PERSON><PERSON>un<PERSON>("IsNotInGrid", IsNotInGrid);
		<PERSON><PERSON>unction("GetTargetXY", GetTargetXY);
		<PERSON><PERSON>unction("GetLineEndXY", GetLineEndXY);
		L.RegFunction("GetLineEndXY2", GetLineEndXY2);
		L.RegFunction("IsWayLine", IsWayLine);
		L.RegFunction("SetBlock", SetBlock);
		L.RegFunction("RevertBlock", RevertBlock);
		L.RegFunction("IsBlockFindWay", IsBlockFindWay);
		L.RegFunction("IsWaterRipple", IsWaterRipple);
		L.RegFunction("FindNearestValidPoint", FindNearestValidPoint);
		L.RegFunction("FindOptimalPointNearTarget", FindOptimalPointNearTarget);
		L.RegFunction("New", _CreateGridFindWay);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("Width", get_Width, null);
		L.RegVar("Height", get_Height, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateGridFindWay(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
				GridFindWay obj = new GridFindWay(arg0, arg1);
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: GridFindWay.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LoadData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			string arg2 = ToLua.CheckString(L, 4);
			obj.LoadData(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FindWay(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 5)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				bool o = obj.FindWay(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 6)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				bool arg4 = LuaDLL.luaL_checkboolean(L, 6);
				bool o = obj.FindWay(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 7)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				bool arg4 = LuaDLL.luaL_checkboolean(L, 6);
				bool arg5 = LuaDLL.luaL_checkboolean(L, 7);
				bool o = obj.FindWay(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: GridFindWay.FindWay");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GenerateInflexPoints(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.GenerateInflexPoints(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPathLength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int o = obj.GetPathLength();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPathPoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1;
			int arg2;
			bool o = obj.GetPathPoint(arg0, out arg1, out arg2);
			LuaDLL.lua_pushboolean(L, o);
			LuaDLL.lua_pushinteger(L, arg1);
			LuaDLL.lua_pushinteger(L, arg2);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsWaterWay(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			bool o = obj.IsWaterWay(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsBlock(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				bool o = obj.IsBlock(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				bool o = obj.IsBlock(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 5)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				bool o = obj.IsBlock(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: GridFindWay.IsBlock");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsInSafeArea(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			bool o = obj.IsInSafeArea(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsTunnelArea(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			bool o = obj.IsTunnelArea(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsBorder(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			bool o = obj.IsBorder(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsHighArea(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			bool o = obj.IsHighArea(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsNotInGrid(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			bool o = obj.IsNotInGrid(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTargetXY(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 9);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
			int arg4 = (int)LuaDLL.luaL_checknumber(L, 6);
			int arg5;
			int arg6;
			int arg7;
			obj.GetTargetXY(arg0, arg1, arg2, arg3, arg4, out arg5, out arg6, out arg7);
			LuaDLL.lua_pushinteger(L, arg5);
			LuaDLL.lua_pushinteger(L, arg6);
			LuaDLL.lua_pushinteger(L, arg7);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLineEndXY(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 7)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				int arg4;
				int arg5;
				obj.GetLineEndXY(arg0, arg1, arg2, arg3, out arg4, out arg5);
				LuaDLL.lua_pushinteger(L, arg4);
				LuaDLL.lua_pushinteger(L, arg5);
				return 2;
			}
			else if (count == 8)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				int arg4;
				int arg5;
				bool arg6 = LuaDLL.luaL_checkboolean(L, 8);
				obj.GetLineEndXY(arg0, arg1, arg2, arg3, out arg4, out arg5, arg6);
				LuaDLL.lua_pushinteger(L, arg4);
				LuaDLL.lua_pushinteger(L, arg5);
				return 2;
			}
			else if (count == 9)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				int arg4;
				int arg5;
				bool arg6 = LuaDLL.luaL_checkboolean(L, 8);
				bool arg7 = LuaDLL.luaL_checkboolean(L, 9);
				obj.GetLineEndXY(arg0, arg1, arg2, arg3, out arg4, out arg5, arg6, arg7);
				LuaDLL.lua_pushinteger(L, arg4);
				LuaDLL.lua_pushinteger(L, arg5);
				return 2;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: GridFindWay.GetLineEndXY");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLineEndXY2(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 7)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				int arg4;
				int arg5;
				obj.GetLineEndXY2(arg0, arg1, arg2, arg3, out arg4, out arg5);
				LuaDLL.lua_pushinteger(L, arg4);
				LuaDLL.lua_pushinteger(L, arg5);
				return 2;
			}
			else if (count == 8)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				int arg4;
				int arg5;
				bool arg6 = LuaDLL.luaL_checkboolean(L, 8);
				obj.GetLineEndXY2(arg0, arg1, arg2, arg3, out arg4, out arg5, arg6);
				LuaDLL.lua_pushinteger(L, arg4);
				LuaDLL.lua_pushinteger(L, arg5);
				return 2;
			}
			else if (count == 9)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				int arg4;
				int arg5;
				bool arg6 = LuaDLL.luaL_checkboolean(L, 8);
				bool arg7 = LuaDLL.luaL_checkboolean(L, 9);
				obj.GetLineEndXY2(arg0, arg1, arg2, arg3, out arg4, out arg5, arg6, arg7);
				LuaDLL.lua_pushinteger(L, arg4);
				LuaDLL.lua_pushinteger(L, arg5);
				return 2;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: GridFindWay.GetLineEndXY2");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsWayLine(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 5)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				bool o = obj.IsWayLine(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 6)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				bool arg4 = LuaDLL.luaL_checkboolean(L, 6);
				bool o = obj.IsWayLine(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 7)
			{
				GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
				bool arg4 = LuaDLL.luaL_checkboolean(L, 6);
				bool arg5 = LuaDLL.luaL_checkboolean(L, 7);
				bool o = obj.IsWayLine(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: GridFindWay.IsWayLine");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetBlock(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			obj.SetBlock(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RevertBlock(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			obj.RevertBlock(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsBlockFindWay(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			bool o = obj.IsBlockFindWay(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsWaterRipple(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			bool o = obj.IsWaterRipple(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FindNearestValidPoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 6);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			int arg3;
			int arg4;
			obj.FindNearestValidPoint(arg0, arg1, arg2, out arg3, out arg4);
			LuaDLL.lua_pushinteger(L, arg3);
			LuaDLL.lua_pushinteger(L, arg4);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FindOptimalPointNearTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 8);
			GridFindWay obj = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			int arg3 = (int)LuaDLL.luaL_checknumber(L, 5);
			int arg4 = (int)LuaDLL.luaL_checknumber(L, 6);
			int arg5;
			int arg6;
			obj.FindOptimalPointNearTarget(arg0, arg1, arg2, arg3, arg4, out arg5, out arg6);
			LuaDLL.lua_pushinteger(L, arg5);
			LuaDLL.lua_pushinteger(L, arg6);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Width(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			GridFindWay obj = (GridFindWay)o;
			int ret = obj.Width;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Width on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Height(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			GridFindWay obj = (GridFindWay)o;
			int ret = obj.Height;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Height on a nil value");
		}
	}
}

