require("game/xianqi_tedian/xianqi_tedian_wg_data")
require("game/xianqi_tedian/xianqi_tedian_probability_up_view")
require("game/xianqi_tedian/xianqi_tedian_shilian_view")
require("game/xianqi_tedian/xianqi_tedian_zhenxuan_view")
require("game/xianqi_tedian/xianqi_tedian_tehui_view")

XianQiTeDianWGCtrl = XianQiTeDianWGCtrl or BaseClass(BaseWGCtrl)

function XianQiTeDianWGCtrl:__init()
    if XianQiTeDianWGCtrl.Instance then
        error("[XianQiTeDianWGCtrl]:Attempt to create singleton twice!")
        return
    end
    XianQiTeDianWGCtrl.Instance = self

    self.data = XianQiTeDianWGData.New()
    self.proup_view = XianQiTeDianProUpView.New(GuideModuleName.XianQiProUp)
    self.shilian_view = XianQiTeDianShiLianView.New(GuideModuleName.XianQiShiLian)
    self.zhenxuan_view = XianQiTeDianZhenXuanView.New(GuideModuleName.XianQiZhenXuan)
    self.tehui_view = XianQiTeDianTeHuiView.New(GuideModuleName.XianQiTeHui)
    
    self:RegisterAllProtocals()
end

function XianQiTeDianWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.proup_view:DeleteMe()
    self.proup_view = nil

    self.shilian_view:DeleteMe()
    self.shilian_view = nil

    self.zhenxuan_view:DeleteMe()
    self.zhenxuan_view = nil

    self.tehui_view:DeleteMe()
    self.tehui_view = nil

    XianQiTeDianWGCtrl.Instance = nil
end

function XianQiTeDianWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCShenJiBaiLianTeDianInfo, "OnSCShenJiBaiLianTeDianInfo")
end

function XianQiTeDianWGCtrl:OnSCShenJiBaiLianTeDianInfo(protocol)
    self.data:SetXianQiTeDianInfo(protocol)
    self.proup_view:Flush()
    self.shilian_view:Flush()
    self.zhenxuan_view:Flush()
    self.tehui_view:Flush()

    --刷新仙器真炼面板,红点
    SiXiangCallWGCtrl.Instance:FlushXQZLView()
    RemindManager.Instance:Fire(RemindName.SiXiangCall_XQZL)
    
    --MainuiWGCtrl.Instance:FlushView(0, "BaiLianZhaoHuanMainBtnFlush")
    MainuiWGCtrl.Instance:FlushView(0, "ShenJiTianCiMainBtnFlush")
end

-- 仙器十连、仙器甄选、仙器特惠 购买
function XianQiTeDianWGCtrl:BuyXianQiTeDian(act_id, product_id)
    product_id = product_id or 1

    local cfg_list = XianQiTeDianWGData.Instance:GetActSaleCfg(act_id)
    local sale_info = XianQiTeDianWGData.Instance:GetSubActSaleInfo(act_id, product_id)
    local cfg = cfg_list and cfg_list[product_id]
    if not cfg or not sale_info then
        return
    end

    if sale_info.status == YuanShenSaleSubActSaleStatus.NotBuy then
        
    -- elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch then
    --     SiXiangCallWGCtrl.Instance:SendMachineOp(MACHINE_OP_TYPE.TE_DIAN,
    --         XIANQI_TEDIAN_OP_TYPE.GET_TEDIAN_RMB_SHOPITEM,
    --         act_id, cfg.product_id)
    --     return
    elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyAndFetched and sale_info.buy_num >= cfg.limit_buy_times then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.SiXiangCall.HasFetch)
        return
    end

    local buyfunc = function ()
        if cfg.price_type == SiXiangBuyType.RMB then
            local flag = cfg.product_id * 100 + act_id
            RechargeWGCtrl.Instance:Recharge(cfg.special_sale_price, GET_GOLD_REASON.GET_GOLD_REASON_SHEN_JI_BAI_LIAN, flag)
        elseif cfg.price_type == SiXiangBuyType.XY then
            SiXiangCallWGCtrl.Instance:SendMachineOp(MACHINE_OP_TYPE.TE_DIAN,
                XIANQI_TEDIAN_OP_TYPE.REQ_BUY_TEDIAN_SHOPITEM,
                act_id, product_id)
        end
    end
    if cfg.name and cfg.name ~= "" then
        local tip_str = ""
        if cfg.price_type == SiXiangBuyType.XY then
            tip_str = string.format(Language.Common.CommonAlertFormat3, cfg.special_sale_price, cfg.name)
        elseif cfg.price_type == SiXiangBuyType.RMB then
            local flag = cfg.product_id * 100 + act_id
            local price = RoleWGData.GetPayMoneyStr(cfg.special_sale_price, GET_GOLD_REASON.GET_GOLD_REASON_SHEN_JI_BAI_LIAN, flag)
            tip_str = string.format(Language.SiXiangCall.ShenJiTianCiShopBuyTips, price, cfg.name)
        end
        TipWGCtrl.Instance:OpenAlertTips(tip_str, buyfunc)
    else
        buyfunc()
    end
end