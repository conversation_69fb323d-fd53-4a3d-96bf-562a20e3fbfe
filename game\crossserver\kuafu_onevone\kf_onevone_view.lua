
function Field1v1View:OpenPvPRankView(mark)
	ViewManager.Instance:Open(GuideModuleName.PvPRank)
	ViewManager.Instance:FlushView(GuideModuleName.PvPRank, nil, nil, {mark})
end

function Field1v1View:OpenPvPAimView(mark)
	ViewManager.Instance:Open(GuideModuleName.PvPSeasonReward)
	ViewManager.Instance:FlushView(GuideModuleName.PvPSeasonReward, nil, nil, {mark})
end

function Field1v1View:OpenPvPGongXunView(mark)
	ViewManager.Instance:Open(GuideModuleName.PvPGongXunReward)
	ViewManager.Instance:FlushView(GuideModuleName.PvPGongXunReward, nil, nil, {["choose"] = mark})
end

function Field1v1View:OpenRankScoreView( mark )

end

function Field1v1View:OnOnClickWZRingCallBack()
	KuafuOnevoneWGCtrl.Instance:OpenWZRingView()
end

function Field1v1View:Update1v1BtnEnable()
	local state = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)
	XUI.SetGraphicGrey(self.node_list.btn_matching, not state)

end

function Field1v1View:OnClickTerraceCallBack(index)
	local kf1v1_info = KuafuOnevoneWGData.Instance:Get1V1Info()
	local i = index
	local cfg = KuafuOnevoneWGData.Instance:GetRewardCfg()
	local data = cfg[index]
	if KuafuOnevoneWGData.Instance:GetJionTimesRewardIsGet(i) == 0 then
		local count = data.jion_times
		if kf1v1_info.cross_day_join_1v1_count >= count then
			KuafuOnevoneWGCtrl.Instance:SendCSCross1v1FetchRewardReq(CROSS_1V1_FETCH_REWARD_TYPE.CROSS_1V1_FETCH_REWARD_TYPE_JOIN_TIMES, index-1)
			return
		end
	end
	TipWGCtrl.Instance:OpenItem({item_id = data.reward_display}, ItemTip.FROM_NORMAL, nil)
end

function Field1v1View:OpenKFonevone()
	if not self.is_load then return end
	self:OnFlushKuafuOneVOne()

	KuafuOnevoneWGCtrl.Instance:SendCross1v1WeekRecordQuery()
	self:UpdataActState()
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change_callback)
end

function Field1v1View:UpdataActState()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
	if nil ~= activity_info then
		if ACTIVITY_STATUS.CLOSE == activity_info.status then
			local macth_info = KuafuOnevoneWGData.Instance:Get1V1MacthInfo()
			macth_info.result = 0
			macth_info.match_end_left_time = 0
			self:Flush()
		end
	end
end

function Field1v1View:MatchingHandler()
	local scene_type = Scene.Instance:GetSceneType()
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)
	if is_open and scene_type ~= SceneType.Kf_OneVOne_Prepare then
		Field1v1WGCtrl.Instance:EnterFieldPrepareScene(ACTIVITY_TYPE.KF_ONEVONE)
		return
	end
	if self.is_wait_match then
		ViewManager.Instance:Close(GuideModuleName.KfOneVOneMatch)
		KuafuOnevoneWGCtrl.Instance:SendCross1v1MatchQuery(CROSS_1V1_MATCH_REQ_TYPE.CROSS_1V1_MATCH_REQ_CANCEL)
		return
	end

	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)  then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
		return
	end

	local kf1v1_info = KuafuOnevoneWGData.Instance:Get1V1Info()
	if IsEmptyTable(kf1v1_info) then
		return
	end

	Field1v1WGData.Instance:SetCurMatchFlag(KFPVP_TYPE.ONE)
	KuafuOnevoneWGCtrl.Instance:SendCrossMatch1V1Req()
end

function Field1v1View:OpenTips()
	RuleTip.Instance:SetContent(Language.Kuafu1V1.Kf1v1AfterTips, Language.Kuafu1V1.Kf1v1Tips, nil, nil, true)
end

function Field1v1View:OpenConfirmAlert()
	self.confirm_buy_alert:Open()
end

function Field1v1View:ClickToBuyTimes()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE) then
		KuafuOnevoneWGCtrl.Instance:SendCSCross1v1BuyTimeReq()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.GUILDJIUHUINOOPEN)
	end
end

function Field1v1View:SetMatchBtnFalse()
	if self.node_list.btn_matching then
		XUI.SetButtonEnabled(self.node_list.btn_matching,false)
	end
end

function Field1v1View:ClickRankReward()

end
