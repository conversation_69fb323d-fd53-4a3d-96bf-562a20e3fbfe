
Beauty = Beauty or BaseClass(FollowObj)

-- 美人
function Beauty:__init(vo)
	self.obj_type = SceneObjType.BeautyObj
	self.followui_class = RoleFollow
	self.draw_obj:SetObjType(self.obj_type)
	self:SetObjId(vo.obj_id)

	self.follow_offset = -1
	self.sqrt_stop_distance = 3 * 3-----6 * 6
	self.sqrt_slow_down_distance = 5 * 5---8 * 8
	self:SetMaxForce(5) --80
	self:SetMaxSpeed(26)

    self.record_time = self.now_time or 0
    self.record_talk_time = 0
    self.is_talk = false
    self.can_bubble = true

	self:InitAppearance()

end

function Beauty:__delete()
	if self.bobble_timer_quest then
        GlobalTimerQuest:CancelQuest(self.bobble_timer_quest)
        self.bobble_timer_quest = nil
    end

	--销毁前做一下隐藏防止其他地方用的时候 展示特效
	if self.draw_obj and self.draw_obj:GetPart(SceneObjPart.Main) and self.draw_obj:GetPart(SceneObjPart.Main).obj then
		local ui_effect_obj = self.draw_obj:GetPart(SceneObjPart.Main).obj.gameObject.transform:FindHard("UIEffectObject")
		if ui_effect_obj and not IsNil(ui_effect_obj) then
			ui_effect_obj.gameObject:SetActive(false)
		end
	end

	self.obj_type = nil
end

function Beauty:InitAppearance()
	local scale = YunbiaoWGData.Instance:GetBeautyScale(self.vo.husong_color)
	self.draw_obj:GetRoot().transform:SetLocalScale(scale, scale, scale)
	local follow_ui = self:GetFollowUi()
	if follow_ui then
		follow_ui:SetHpVisiable(false)
		--follow_ui:SetNameVis(false)
	end

	self.follow_ui:SetNameContainerVisible(false)
	local name = YunbiaoWGData.Instance:GetRewardCfgByLv(self.vo.husong_color)
	local bundle,asset = ResPath.GetCommonImages("a3_hs_mzd" .. (self.vo.husong_color - 1))
	self.follow_ui:SetHuSongImg(bundle, asset)
	self.follow_ui:SetHuSongName(name.task_name)
	self.follow_ui:SetNameTextOffY(90)
	
	self:SetLogicPos(self.vo.pos_x, self.vo.pos_y)
	self:UpdateModelResId()

	self.exist_time = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].exist_time
    self.interval = 6
    self.record_time = self.record_time + self.interval
end

function Beauty:UpdateModelResId()
	--local res_id = YunbiaoWGData.Instance:GetBeautyResid(self.vo.husong_color)
	if 0 ~= self.vo.husong_taskid then
		--local asset, bundle = ResPath.GetPetModel(res_id)
		local bundle, asset = YunbiaoWGData.Instance:GetBeautyModelPath(self.vo.husong_color)
		self:ChangeModel(SceneObjPart.Main, bundle, asset, function()
			local mian_obj = self.draw_obj:GetPart(SceneObjPart.Main).obj
			if mian_obj then
				local ui_effect_obj = mian_obj.gameObject.transform:FindHard("UIEffectObject")
				if ui_effect_obj and not IsNil(ui_effect_obj) then
					ui_effect_obj.gameObject:SetActive(true)
				else
					print_log("护送模型显示特效, 找不到 UIEffectObject" , bundle, asset)
				end
			end
		end)
	end
end

function Beauty:SetAttr(key, value)
	Character.SetAttr(self, key, value)
	if key == "husong_color" then
		self:UpdateModelResId()
	end
end

function Beauty:Update(now_time, elapse_time)
	FollowObj.Update(self, now_time, elapse_time)

	if not self.can_bubble or not self:GetVisiable() then
        return
    end
	if nil ~= self.follow_ui and self:OwnerIsMainRole() then
        if now_time > self.record_time and self.is_talk == false then
            self.is_talk = true
            self:UpdataBubble()
            self.follow_ui:ForceSetVisible(true)
            self.follow_ui:ShowBubble()
            self.bobble_timer_quest = GlobalTimerQuest:AddDelayTimer(function()
                self.follow_ui:CancelForceSetVisible()
                self.follow_ui:HideBubble()
                self.is_talk = false
                self.record_time = now_time + self.interval
            end,self.exist_time)
        end
    end
end

function Beauty:GetOwerRoleId()
	return self.vo.owner_role_id
end

function Beauty:MoveEnd()
	if nil == self.distance then
		return false
	end
	return self.distance <= 9
end

function Beauty:IsBeautyVisible()
	return true
end

function Beauty:IsBeauty()
	-- body
	return true
end

function Beauty:IsMyBeauty()
	local obj = self.parent_scene:GetObjectByObjId(self.vo.owner_obj_id)
	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	if nil ~= obj and obj:IsRole() and role_id == self.vo.owner_role_id then
		return true
	end
	return false
end
function Beauty:SetIsSkill(position)
	if self.draw_obj == nil then
		return
	end
	local deliverer_position = self.draw_obj:GetRootPosition()
	if position then
		local direction = position - deliverer_position
		direction.y = 0
		if direction.x == 0 and direction.z == 0 then return end
		self.draw_obj:GetRoot().transform.localRotation = Quaternion.LookRotation(direction)
		-- local pos = Quaternion.LookRotation(direction)
		-- self.draw_obj:GetRoot().transform:DOLocalRotate(Vector3(pos.x, pos.y, pos.z), 0.2)
	end
end

function Beauty:EnterStateAttack()
	local anim_name = SceneObjAnimator.Atk1
	Character.EnterStateAttack(self, anim_name)
end

function Beauty:EnterStateMove()
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	part:SetInteger("status", ActionStatus.Run)
end

function Beauty:EnterStateStand()
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	math.randomseed(os.time())
	local value = math.random(1, 4)
	if value ~= 1 then
		part:SetInteger("status", 22)
	else
		part:SetInteger("status", ActionStatus.Idle)
	end
end

function Beauty:GetCurFollowTargetRealPos()
	local target = self.owner_obj

	if target then
		local real_pos = target.real_pos
	
		local deliverer_draw_obj = target:GetDrawObj()
		if not deliverer_draw_obj then
			return real_pos
		end
	
		local role_transform = deliverer_draw_obj:GetTransfrom()
	
		if not role_transform then
			return real_pos
		end
	
		self.radius = 4
		local pos = nil
		local left_back =  role_transform.forward * 4 + role_transform.right * 2
		pos = role_transform.position + left_back       ---左
	
		if not pos then
			return real_pos
		end
	
		local beast_real_pos = u3d.vec2(0, 0)
		beast_real_pos.x = pos.x
		beast_real_pos.y = pos.z
	
		return beast_real_pos
	end
end

function Beauty:UpdataBubble()
    if nil ~= self.follow_ui then
        local text = self:GetRandBubbletext()
        self.follow_ui:ChangeBubble(text)
    end
end

function Beauty:GetRandBubbletext()
    local bubble_cfg = NewAppearanceWGData.Instance:GetLingChongBubbleList(Scene.Instance:GetSceneType())
    if #bubble_cfg > 0 then
        math.randomseed(os.time())
        local bubble_text_index = math.random(1, #bubble_cfg)
        return bubble_cfg[bubble_text_index].pet_talk
    else
        return ""
    end
end

function Beauty:OwnerIsMainRole()
    return self.vo.owner_is_mainrole or false
end
