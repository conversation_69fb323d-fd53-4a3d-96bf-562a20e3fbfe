KFOneVOneHeadView = KFOneVOneHeadView or BaseClass(SafeBaseView)

function KFOneVOneHeadView:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "kf_layout_field_head_panel")
	self.view_layer = UiLayer.MainUILow
	self.out_time = 0
	self.prepare_time = 0

	self.out_time_key = "field_head_out_time"


	self.is_set_data = false

	self.start_fight = false  		--是否开启战斗的标记

end

function KFOneVOneHeadView:__delete()
end

function KFOneVOneHeadView:ReleaseCallBack()
	Runner.Instance:RemoveRunObj(self)
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)

	if self.role_head then
		for k, v in pairs(self.role_head) do
			v:DeleteMe()
			v = nil
		end
		self.role_head = nil
	end

end

function KFOneVOneHeadView:LoadCallBack()
	Runner.Instance:AddRunObj(self, 8)

	if not self.role_head then
		self.role_head = {}
		for i = 1, 2 do
			self.role_head[i] = BaseHeadCell.New(self.node_list["role_head_" .. i])
		end
	end
end


function KFOneVOneHeadView:Update(now_time, elapse_time)
	local main_role_vo = RoleWGData.Instance:GetRoleVo()
	if main_role_vo then
		self:RoleSliderChange(1,main_role_vo.hp,main_role_vo.max_hp)
	end
	local oppo_scene_obj = KFOneVOneWGData.Instance:GetOpponentSceneInfo()
	if oppo_scene_obj == nil then
		local role_list = Scene.Instance:GetRoleList()
		for k,v in pairs(role_list) do
			local obj_id = RoleWGData.Instance.role_vo.obj_id
			if v:GetType() == SceneObjType.Role and v:GetVo().obj_id ~= obj_id then
				oppo_scene_obj = v
			end
		end
	end
	if oppo_scene_obj then
		self:RoleSliderChange(2,oppo_scene_obj.vo.hp,oppo_scene_obj.vo.max_hp)
	end
end

function KFOneVOneHeadView:RoleSliderChange(index,cur_hp,max_hp)
	self.node_list["role_slider_" .. index ].slider.value = cur_hp / max_hp
end

function KFOneVOneHeadView:FlushView()
	self:SetMyInfoMsg()
	self:SetEnemyInfoMsg()
end

function KFOneVOneHeadView:SetMyInfoMsg()
	local main_role_vo = RoleWGData.Instance:GetRoleVo()
	local cap = main_role_vo.capability
	local is_vis, limt_level = RoleWGData.Instance:GetDianFengLevel(main_role_vo.level)
	self.node_list["feixianlevel_imag"]:SetActive(is_vis)
	self.node_list["label_lv_feixian_1" ].text.text = limt_level
	self.node_list["label_lv_1"]:SetActive(not is_vis)
	self.node_list["label_lv_1" ].text.text = "LV."..limt_level
	self.node_list["label_name_1"].text.text = main_role_vo.name
	self.node_list["ph_cap_1"].text.text = cap

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	self:SetHeadIcon(1, role_id, main_role_vo.prof, main_role_vo.sex)
end

function KFOneVOneHeadView:SetHeadIcon(head_index, uid, prof, sex)
	local data = {}
	data.role_id = uid
	data.prof = prof
	data.sex = sex
	self.role_head[head_index]:SetData(data)
	self.role_head[head_index]:SetImgBg(true)
end

function KFOneVOneHeadView:SetEnemyInfoMsg()
	local oppo_info = KFOneVOneWGData.Instance:GetMyEnemyInfo()

	if not IsEmptyTable(oppo_info) then
		local cap = oppo_info.capability
		local is_vis, limt_level = RoleWGData.Instance:GetDianFengLevel(oppo_info.level)
		self.node_list["feixianlevel_imag_2"]:SetActive(is_vis)
		self.node_list["label_lv_2"]:SetActive(not is_vis)
		self.node_list["label_lv_feixian_2"].text.text = limt_level
		self.node_list["label_lv_2"].text.text = "Lv."..limt_level
		local name = ""
		if oppo_info.name and oppo_info.name ~= "" then
			local name_list = Split(oppo_info.name, "_")
			local has_s = string.find(name_list[2],"s")
            if has_s then
                name = string.format(Language.BiZuo.ServerName_2,name_list[2],name_list[1])
            else
                name = string.format(Language.BiZuo.ServerName_1,name_list[2],name_list[1])
            end
		end			
		self.node_list["label_name_2"].text.text = name
		self.node_list["ph_cap_2"].text.text = cap
		local uuid = oppo_info.uuid
		local key = uuid.temp_low --.. uuid.temp_high
		if uuid.temp_high == 0 then
			key = 0
		end

		self:SetHeadIcon(2, key, oppo_info.prof, oppo_info.sex)
	end
end

function KFOneVOneHeadView:CloseCallBack()
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)
end

function KFOneVOneHeadView:ShowIndexCallBack()
	self:Flush()
	self.node_list.activity_tips:SetActive(false)
end

function KFOneVOneHeadView:OnFlush()
	self:FlushView()
end

function KFOneVOneHeadView:StartFight(time)
	self:SetOutTime(time)
end

-- 结束时间
function KFOneVOneHeadView:SetOutTime(time)
	self.out_time = time
	self:OutTimerFunc()
	local timer_func = BindTool.Bind1(self.OutTimerFunc, self)
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)
	if self.out_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
		CountDownManager.Instance:AddCountDown(self.out_time_key, timer_func, nil, self.out_time, nil, 1)
	end
end

function KFOneVOneHeadView:OutTimerFunc()
	local time = math.floor(self.out_time - TimeWGCtrl.Instance:GetServerTime())
	if  time >= 0 and self.node_list.ph_timenum1 then
		self.node_list.ph_timenum1.text.text = time
		self.node_list.activity_tips:SetActive(true)
	end
end

