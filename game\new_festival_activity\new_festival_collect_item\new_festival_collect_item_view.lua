local collect_item_timer_key = "collect_item_timer"
local CollectItemColor = {
    EnoughColor = "#ff5859",
    NotEnoughColor = "#9df5a7",
}

function NewFestivalActivityView:LoadIndexCallBackCollectItem()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.NEW_JRHD_JFSC, OA_COLLECT_ITEM_OP_TYPE.INFO)

    self.jrsc_select_reward_type = 0

    if not self.jrsc_show_model then
        self.jrsc_show_model = OperationActRender.New(self.node_list.jrsc_model_pos)
        self.jrsc_show_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    if not self.jrsc_reward_list then
        self.jrsc_reward_list = NFACollectItemList.New(NFACollectItemRender, self.node_list.jrsc_reward_list)
        self.jrsc_reward_list:SetCreateCellCallBack(BindTool.Bind(self.CollectItemCreatCellCallBack, self))
    end

    XUI.AddClickEventListener(self.node_list.self_reward_btn,
        BindTool.Bind(self.OnClickCollectItemSwitchBtn, self, NewFestivalCollectItemWGData.REWARD_TYPE.Person))
    XUI.AddClickEventListener(self.node_list.jieyi_reward_btn,
        BindTool.Bind(self.OnClickCollectItemSwitchBtn, self, NewFestivalCollectItemWGData.REWARD_TYPE.JieYi))

    self:InitCollectItemImageAndText()
    self:CreateCollectItemCountDown()
end

function NewFestivalActivityView:ReleaseCollectItem()
    if CountDownManager.Instance:HasCountDown(collect_item_timer_key) then
        CountDownManager.Instance:RemoveCountDown(collect_item_timer_key)
    end

    if self.jrsc_show_model then
        self.jrsc_show_model:DeleteMe()
        self.jrsc_show_model = nil
    end

    if self.jrsc_reward_list then
        self.jrsc_reward_list:DeleteMe()
        self.jrsc_reward_list = nil
    end
end

function NewFestivalActivityView:OnFlushCollectItem()
    local self_reward_remind = NewFestivalCollectItemWGData.Instance:GetRewardRemindByType(NewFestivalCollectItemWGData
        .REWARD_TYPE.Person)
    local jieyi_reward_remind = NewFestivalCollectItemWGData.Instance:GetRewardRemindByType(NewFestivalCollectItemWGData
        .REWARD_TYPE.JieYi)
    self.node_list.self_reward_remind:CustomSetActive(self_reward_remind)
    self.node_list.jieyi_reward_remind:CustomSetActive(jieyi_reward_remind)

    if self.jrsc_select_reward_type == 0 then
         self.node_list.self_reward_btn.toggle.isOn = true
        return
    end

    self.node_list.jrsc_illustrate_text.text.text = Language.NewFestivalActivity.JrscIllustrate
    self:FlushCollectItemRewardList()
end

function NewFestivalActivityView:FlushCollectItemRewardList()
    local reward_list = NewFestivalCollectItemWGData.Instance:GetRewardListByType(self.jrsc_select_reward_type)

    if IsEmptyTable(reward_list) then
        return
    end

    self.jrsc_reward_list:SetCellRewardType(self.jrsc_select_reward_type)
    self.jrsc_reward_list:SetDataList(reward_list)
end

function NewFestivalActivityView:InitCollectItemImageAndText()
    local model_data = NewFestivalCollectItemWGData.Instance:GetModelData()
    if not IsEmptyTable(model_data) then
        -- local transform_info = model_data.transform_info
        -- RectTransform.SetAnchoredPositionXY(self.node_list.jrsc_model_pos.rect, transform_info.pos_x,
        --     transform_info.pos_y)
        -- self.node_list.jrsc_model_pos.rect.rotation = Quaternion.Euler(transform_info.rot_x, transform_info.rot_y,
        --     transform_info.rot_z)
        -- Transform.SetLocalScaleXYZ(self.node_list.jrsc_model_pos.transform, transform_info.scale, transform_info.scale,
        --     transform_info.scale)

        self.jrsc_show_model:SetData(model_data.display_data)
    end

    local title_bundle, title_asset = ResPath.GetNewFestivalRawImages("jrsc_title")
    self.node_list.jrsc_title.raw_image:LoadSprite(title_bundle, title_asset, function()
        self.node_list.jrsc_title.raw_image:SetNativeSize()
    end)

    local timer_bg_bundle, timer_bg_asset = ResPath.GetNewFestivalRawImages("jrsc_time_bg")
    self.node_list.jrsc_timer_bg.raw_image:LoadSprite(timer_bg_bundle, timer_bg_asset, function()
        self.node_list.jrsc_timer_bg.raw_image:SetNativeSize()
    end)

    local bg_bundle, bg_asset = ResPath.GetNewFestivalRawImages("jrsc_bg")
    self.node_list.jrsc_bg.raw_image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list.jrsc_bg.raw_image:SetNativeSize()
    end)

    local client_show_cfg = NewFestivalActivityWGData.Instance:GetCollectItemOtherCfg()
    CollectItemColor.EnoughColor = client_show_cfg.enough_color
    CollectItemColor.NotEnoughColor = client_show_cfg.not_enough_color

    local btn_bundle, btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xcqy_btn1")
    local btn_hl_bundle, btn_hl_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xcqy_btn1_hl")
    for i = 1, 2 do
        self.node_list["jrsc_switch_btn_txt" .. i].text.color = Str2C3b(client_show_cfg.btn_color1)
        self.node_list["jrsc_switch_btn_hl_txt" .. i].text.color = Str2C3b(client_show_cfg.btn_hl_color1)
        local btn_img = self.node_list["jrsc_switch_btn_nor_img" .. i].image
        btn_img:LoadSprite(btn_bundle, btn_asset, function()
            btn_img:SetNativeSize()
        end)

        local btn_hl_img = self.node_list["jrsc_switch_btn_hl_img" .. i].image
        btn_hl_img:LoadSprite(btn_hl_bundle, btn_hl_asset, function()
            btn_hl_img:SetNativeSize()
        end)
    end

    self.node_list.jrsc_time.text.color = Str2C3b(client_show_cfg.timer_color)
    self.timer_time_part_color = client_show_cfg.time_part_color
end

function NewFestivalActivityView:OnClickCollectItemSwitchBtn(reward_type)
    self.jrsc_select_reward_type = reward_type
    self:FlushCollectItemRewardList()
end

function NewFestivalActivityView:CreateCollectItemCountDown()
    if CountDownManager.Instance:HasCountDown(collect_item_timer_key) then
        return
    end

    local time, total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.NEW_JRHD_JFSC)
    if time > 0 then
        self:CollectItemUpdateCountDown(total_time - time, total_time)
        CountDownManager.Instance:AddCountDown(collect_item_timer_key,
            BindTool.Bind1(self.CollectItemUpdateCountDown, self), BindTool.Bind1(self.CollectItemCompleteCallBack, self),
            nil, time, 1)
    else
        self:CollectItemCompleteCallBack()
    end
end

function NewFestivalActivityView:CollectItemUpdateCountDown(elapse_time, total_time)
    if self.node_list and self.node_list.jrsc_time then
        -- self.node_list.jrsc_time.text.text = string.format(Language.CollectItem.ActEndTime, self.timer_time_part_color,
        -- TimeUtil.FormatSecondDHM8(total_time - elapse_time))
        self.node_list.jrsc_time.text.text = string.format(Language.NewFestivalActivity.ActTime, self.timer_time_part_color, TimeUtil.FormatSecondDHM8(total_time - elapse_time))
    end
end

function NewFestivalActivityView:CollectItemCompleteCallBack()
    if self.node_list and self.node_list.jrsc_time then
        self.node_list.jrsc_time.text.text = Language.Common.ActivityIsEnd
    end
end

function NewFestivalActivityView:CollectItemCreatCellCallBack(cell)
    cell:SetShowRewardType(self.jrsc_select_reward_type)
end

----------------NFACollectItemList节日收集奖励列表
NFACollectItemList = NFACollectItemList or BaseClass(AsyncListView)
function NFACollectItemList:SetCellRewardType(type)
    for k, v in pairs(self.cell_list) do
        v:SetShowRewardType(type)
    end
end

----------------NFACollectItemRender节日收集奖励render
NFACollectItemRender = NFACollectItemRender or BaseClass(BaseRender)
local BtnName = {
    [1] = "a3_jrhd_jrsc_btn1",
    [2] = "a3_jrhd_jrsc_btn3",
}

function NFACollectItemRender:LoadCallBack()
    self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    self.reward_list:SetStartZeroIndex(true)

    XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBtn, self))
    self:SetImageAndText()
end

function NFACollectItemRender:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function NFACollectItemRender:OnFlush()
    if not self.data or not self.show_reward_type then
        return
    end

    local item_num = NewFestivalCollectItemWGData.Instance:GetItemCountByType(self.show_reward_type)
    local color = item_num >= self.data.need_num and CollectItemColor.EnoughColor or CollectItemColor.NotEnoughColor
    self.node_list.num.text.text = string.format(Language.CollectItem.NeedItemNum, color, item_num, self.data.need_num)

    local get_flag = NewFestivalCollectItemWGData.Instance:GetRewardFlagByTypeAndSeq(self.show_reward_type, self.data
        .seq)
    self.node_list.btn_txt.text.text = get_flag == 0 and Language.CollectItem.GetBtnStr or Language.Common.YiLingQu
    local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.NEW_JRHD_JFSC)
    self.node_list.btn_remind:CustomSetActive(get_flag == 0 and item_num >= self.data.need_num and act_is_open)

    self.reward_list:SetDataList(self.data.reward_item)

    local btn_name_index = get_flag == 1 and 2 or 1
    local btn_img_bundle, btn_img_asset = ResPath.GetNewFestivalActImages(BtnName[btn_name_index])
    self.node_list.get_btn_img.image:LoadSprite(btn_img_bundle, btn_img_asset, function()
        self.node_list.get_btn_img.image:SetNativeSize()
    end)
end

function NFACollectItemRender:OnClickGetBtn()
    if not self.data then
        return
    end

    local is_sworn = SwornWGData.Instance:IsSwornNow()
    if not is_sworn and self.show_reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.JieYi then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CollectItem.NotSworn)
        return
    end

    local item_num = NewFestivalCollectItemWGData.Instance:GetItemCountByType(self.show_reward_type)
    if item_num < self.data.need_num then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CollectItem.ItemNotEnough)
        return
    end

    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.NEW_JRHD_JFSC, self:GetOperaType(), self.data.seq)
end

function NFACollectItemRender:SetShowRewardType(type)
    self.show_reward_type = type
end

function NFACollectItemRender:SetImageAndText()
    local client_show_cfg = NewFestivalActivityWGData.Instance:GetCollectItemOtherCfg()

    local icon_bundle, icon_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jrsc_icon")
    self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)

    local bg_bundle, bg_asset = ResPath.GetNewFestivalRawImages("jrsc_di")
    self.node_list.bg.raw_image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list.bg.raw_image:SetNativeSize()
    end)

    local top_bundle, top_asset = ResPath.GetNewFestivalRawImages("jrsc_top_bg")
    self.node_list.top.raw_image:LoadSprite(top_bundle, top_asset, function()
        self.node_list.top.raw_image:SetNativeSize()
    end)

    self.node_list.btn_txt.text.color = Str2C3b(client_show_cfg.btn_color2)
end

function NFACollectItemRender:GetOperaType()
    if not self.show_reward_type then
        return 0
    end

    if self.show_reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.Person then
        return OA_COLLECT_ITEM_OP_TYPE.PERSONAL_REWARD
    elseif self.show_reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.JieYi then
        return OA_COLLECT_ITEM_OP_TYPE.JIEYI_REWARD
    end

    return 0
end
