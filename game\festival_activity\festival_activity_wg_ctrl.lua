require("game/festival_activity/festival_act_btn")
require("game/festival_activity/festival_tab_button")
require("game/festival_activity/festival_activity_wg_data")
require("game/festival_activity/festival_activity_view")
require("game/festival_activity/festival_login_youli/festival_login_youli_view")
require("game/festival_activity/festival_miaosha_view/festival_miaosha_view")
require("game/festival_activity/festival_leichong/festival_leichong_recharge_view")
require("game/festival_activity/festival_duobei/festival_duobei_view")
require("game/festival_activity/festival_mowang_youli/festival_mowang_view")
require("game/festival_activity/festival_special_rank/festival_special_rank_view")
require("game/festival_activity/festival_exchange_shop/festival_exchange_shop_view")
require("game/festival_activity/festival_chushen/festival_chushen_view")
require("game/festival_activity/festival_turn_table/festival_turn_table_view")

FestivalActivityWGCtrl = FestivalActivityWGCtrl or BaseClass(BaseWGCtrl)
function FestivalActivityWGCtrl:__init()
	FestivalActivityWGCtrl.Instance = self

	self.festival_data = FestivalActivityWGData.New()
	self.festival_view = FestivalActivityView.New(GuideModuleName.FestivalActivityView)

	--festival 总的监听
	self.item_change_callback = BindTool.Bind(self.OnNotifyDataChangeCallBack,self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)
end

function FestivalActivityWGCtrl:__delete()
	FestivalActivityWGCtrl.Instance = nil

	self.festival_data:DeleteMe()
    self.festival_data = nil

	self.festival_view:DeleteMe()
	self.festival_view = nil

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
	self.item_change_callback = nil
end

function FestivalActivityWGCtrl:OnNotifyDataChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	FestivalActivityWGData.Instance:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	FestivalChuShenWGData.Instance:ChuShenItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	FestivalExchangeShopWGData.Instance:ItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
end

function FestivalActivityWGCtrl:Open(tab_index, param_t)
	if nil == tab_index then --这一步只是为了防止出错而已为了兼容报错的情况下可走
		tab_index = self:GetFirstOpenActivity()
	end

	local activity_type = self.festival_data:GetCurSelectActivityType(tab_index)
	local state = self.festival_data:GetActivityState(activity_type)

	if not state then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeActivity.ActivityNoOpenHint)
		return
	end

	self:SetViewTabbarMoveFlag()
	self.festival_view:Open(tab_index)
end

function FestivalActivityWGCtrl:SetViewTabbarMoveFlag()
	self.festival_view:SetTabbarMoveFlag()
end

-- 获取排序后第一个开启的活动
function FestivalActivityWGCtrl:GetFirstOpenActivity()
	local first_tab_index = self.festival_data:GetOneOpenTabIndex()
	return first_tab_index
end

function FestivalActivityWGCtrl:GetActivityIsOpen(tab_index)
	if not tab_index or not TabIndex[tab_index] then
		return false
	end

	local activity_type = self.festival_data:GetCurSelectActivityType(TabIndex[tab_index])
	local state = self.festival_data:GetActivityState(activity_type)

	if state then
		return true
	end

	return false
end

function FestivalActivityWGCtrl:ChangeSelectIndex()
	--活动关闭的时候对页签进行一次刷新并且重新做选中
	if self.festival_view:IsOpen() then
		--优先判断是否还有已开启的活动
		if self.festival_data:GetActivityIsClose() then
			self.festival_view:Close()
			return
		end

		self.festival_view:SetTabSate()
		--如果当前选择的活动是开启状态那么返回
		local index = self.festival_view:GetShowIndex() or 0
		local activity_type = self.festival_data:GetCurSelectActivityType(index)
		local is_open = self.festival_data:GetActivityState(activity_type)
		if is_open then
			return
		end
		self:Open() --有意传空让他做选择
	end
end

function FestivalActivityWGCtrl:FlushView(...)
	-- 防止为空
	if self.festival_view then
		self.festival_view:Flush(...)
	end
end

function FestivalActivityWGCtrl:GetViewIsOpen()
	if self.festival_view then
		return self.festival_view:IsOpen()
	end

	return false
end

function FestivalActivityWGCtrl:GetViewInShowIndex(tab_index)
	if self.festival_view and self.festival_view:IsOpen() and self.festival_view:GetShowIndex() == tab_index then
		return self.festival_view
	end
end

function FestivalActivityWGCtrl:OnClickJuLingBackBtn()
	if self.festival_view and self.festival_view:IsOpen() and self.festival_view:IsLoaded() then
		self.festival_view:OnClickJuLingBackBtn()
	end
end