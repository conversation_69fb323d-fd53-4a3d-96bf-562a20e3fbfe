MergeActivityView = MergeActivityView or BaseClass(SafeBaseView)


--需要倒计时
local NeedShowCountDown = {
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE] = true,    --秒杀
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LIMIT_RECHARGE] = true, --累充
}

function MergeActivityView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true, false)


	local assetbundle = "uis/view/merge_activity_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")

	self:AddViewResource(TabIndex.merge_activity_2248, "uis/view/merge_activity_ui/login_youli_ui_prefab",
		"layout_login_reward")
	self:AddViewResource(TabIndex.merge_activity_2109, "uis/view/merge_activity_ui/day_first_chongzhi_prefab",
		"layout_merge_dayfirst_recharge")
	self:AddViewResource(TabIndex.merge_activity_2112, "uis/view/merge_activity_ui/zhaocai_miaomiao_prefab",
		"layout_zhaocai_miaomiao_view")
	self:AddViewResource(TabIndex.merge_activity_2118, "uis/view/merge_activity_ui/hmwd_ui_prefab", "layout_merge_hmwd")
	self:AddViewResource(TabIndex.merge_activity_2110, "uis/view/merge_activity_ui/leichong_ui_prefab",
		"layout_operation_total_recharge")
	self:AddViewResource(TabIndex.merge_activity_2111, "uis/view/merge_activity_ui/merge_duobei_prefab",
		"layout_merge_duobei")
	self:AddViewResource(TabIndex.merge_activity_2128, "uis/view/merge_activity_ui/hefu_miaosha_ui_prefab",
		"hefu_miaosha_view")
	self:AddViewResource(TabIndex.merge_activity_2127, "uis/view/merge_activity_ui/hefu_juling_ui_prefab", "juling_view")
	self:AddViewResource(TabIndex.merge_activity_2119, "uis/view/merge_activity_ui/liemo_daren_prefab",
		"layout_liemo_daren")
	self:AddViewResource(TabIndex.merge_activity_2129, "uis/view/merge_activity_ui/sp_rank_prefab", "layout_special_rank")
	self:AddViewResource(TabIndex.merge_activity_2246, "uis/view/merge_activity_ui/fireworks_ui_prefab",
		"layout_merge_fireworks")
	self:AddViewResource(TabIndex.merge_activity_2249, "uis/view/merge_activity_ui/merge_exchage_prefab",
		"layout_exchange_shop")


	self:AddViewResource(0, assetbundle, "merge_view_time")
	--self:AddViewResource(TabIndex.merge_activity_2127, "uis/view/merge_activity_ui/hefu_juling_ui_prefab", "juling_friend_obj")
	self:AddViewResource(0, assetbundle, "rule_panel")
	--self:AddViewResource(0, assetbundle, "VerticalTabbar")
	self:AddViewResource(0, assetbundle, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	--self:AddViewResource(0, assetbundle, "layout_merge_activity_panel2")
	self:InitLieMoDaRen()
	self:InitSPRank()
	self.tab_sub = {}
	self.default_index = 0

	self.item_data_change_event = BindTool.Bind1(self.ItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_event)
end

-- 计算要显示的index（可重写）
function MergeActivityView:CalcShowIndex()
	return MergeActivityWGData.Instance:GetOneOpenTabIndex()
end

function MergeActivityView:__delete()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_event)
	self.item_data_change_event = nil
end

function MergeActivityView:ItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.show_index == TabIndex.merge_activity_2246 then
		self:OnFwsItemChange(change_item_id)
	elseif self.show_index == TabIndex.merge_activity_2249 then
		self:FlushExchange()
	end
end

function MergeActivityView:ReleaseCallBack()
	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if CountDownManager.Instance:HasCountDown("merge_activity_common_count_down") then
		CountDownManager.Instance:RemoveCountDown("merge_activity_common_count_down")
	end

	GlobalEventSystem:UnBind(self.new_flag_change)
	self.tabbar_loading = nil

	self.rule_content = nil
	self.rule_title = nil
	self.activity_cfg = nil
	self:DelHongMengView()
	self:ReleaseLoginRewardView()
	self:DTYYReleaseShouChongView()
	self:DelFireworksView()
	self:DTYYReleaseLeiChongView()
	self:ReleaseDBView()
	self:DeleteHeFuJuLing()
	self:DeleteHeFuMiaoSha()
	self:ReleaseCallBackLieMoDaRen()
	self:ReleaseCallBackSPRank()
	self:DeleteZhaoCaiMiao()
	self:ReleaseExchangeCallBack()

	-- 注销功能引导
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.MergeActivityView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function MergeActivityView:OpenCallBack()
	self:SetTabSate()
end

function MergeActivityView:OpenIndexCallBack(index)
	if index == TabIndex.merge_activity_2109 then
		self:OpenIndexCallBackShouChongView()
	elseif index == TabIndex.merge_activity_2118 then
		self:OpenMergeHMWD()
	elseif index == TabIndex.merge_activity_2112 then
		self:LoadIndexCallBackZhaoCaiMiao()
	elseif index == TabIndex.merge_activity_2110 then
		self:OpenIndexCallBackLeiChong()
	elseif index == TabIndex.merge_activity_2111 then
		self:OpenCallBackDuoBei()
	elseif index == TabIndex.merge_activity_2119 then
		self:OpenIndexCallBackLieMoDaRen()
	end
end

function MergeActivityView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.MergeActivityView
	self.activity_cfg = ConfigManager.Instance:GetAutoConfig("merge_activity_config_auto").merge_activity_dec

	self.tabbar_loading = false
	if not self.tabbar then
		local tab_index_name, remind_tab = MergeActivityWGData.Instance:GetOperationViewInfo()
		local common_path = "uis/view/common_panel_prefab"
		self.remind_tab = remind_tab
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabbarInfo, self))
		--self.tabbar:Init(tab_index_name, nil, common_path, nil, remind_tab, VerMergeItemRender)
		self.tabbar:Init(tab_index_name, nil, common_path, nil, remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	XUI.AddClickEventListener(self.node_list.common_rule_btn, BindTool.Bind(self.OnClickCommonRuleBtn, self))
	self.new_flag_change = GlobalEventSystem:Bind(ACTIVITY_BTN_EVENT.MERGE_ACT_BTN_CHANGE,
		BindTool.Bind(self.NewFalgChange, self))

	-- 注册引导
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.MergeActivityView, self.get_guide_ui_event)
end

function MergeActivityView:LoadIndexCallBack(index)
	if index == TabIndex.merge_activity_2248 then  --登录有礼
		self:LoadIndexCallBackLoginRewarView()
	elseif index == TabIndex.merge_activity_2118 then --鸿蒙悟道
		self:InitHongMengView()
	elseif index == TabIndex.merge_activity_2246 then --占星
		self:InitFireworksView()
	elseif index == TabIndex.merge_activity_2109 then --每日首充
		self:LoadIndexCallBackDayShouChongView()
	elseif index == TabIndex.merge_activity_2110 then --超值累充
		self:LoadIndexCallBackLeiChongView()
	elseif index == TabIndex.merge_activity_2111 then --多倍有礼
		self:LoadIndexCallBackDuoBei()
	elseif index == TabIndex.merge_activity_2128 then --限时秒杀
		self:LoadIndexCallBackHeFuMiaoSha()
	elseif index == TabIndex.merge_activity_2127 then --聚灵助阵
		self:LoadIndexCallBackHeFuJuLing()
	elseif index == TabIndex.merge_activity_2119 then --猎魔达人
		self:LoadCallBackLieMoDaRen()
	elseif index == TabIndex.merge_activity_2129 then --排行榜
		self:LoadCallBackSPRank()
	elseif index == TabIndex.merge_activity_2249 then
		self:LoadIndexCallBackExchange()
	end
end

function MergeActivityView:ShowIndexCallBack(index)
	local rawimg_bg_name = "a3_hfhd_bj" --背景底图
	if self.is_clicked_miaosha then
		self.is_clicked_miaosha = false
		self:ClearMiaoShaoNewFlag()
	end

	if index ~= 0 then
		self:ClearRuleInfo()
		-- local pos = index ~= TabIndex.merge_activity_2246 and Vector2.zero or Vector2(-18, 640)
		-- self:SetRemainTimePos(pos)
		-- local pos1 = index ~= TabIndex.merge_activity_2246 and Vector2.zero or Vector2(150, 0)
		-- self:SetRuleRootPos(pos1)
		self:SetRuleInfoActive(true)
		self:SetActRemainTime(index)
		self:SetRightTimeTitleText(Language.MergeActivity.ActivityTime)
	end
	if index == TabIndex.merge_activity_2248 then
		rawimg_bg_name = "a3_hfhd_dlyl_bj"
		self:SetLoginYouLiViewInfo()
	elseif index == TabIndex.merge_activity_2112 then
		self:ShowIndexCallZhaoCaiMiao()
	elseif index == TabIndex.merge_activity_2109 then --每日首充
		rawimg_bg_name = "a3_hfhd_bj"
		self:FlushRulerTipAndTipsInfo()
	elseif index == TabIndex.merge_activity_2246 then --烟花
		rawimg_bg_name = "a3_ylxc_bj"
		self:ShowIndexFireworks()
	elseif index == TabIndex.merge_activity_2110 then
		rawimg_bg_name = "a3_hfhd_xsms_bj"
		self:OnFlushLeiChongTipsAndRule()
	elseif index == TabIndex.merge_activity_2111 then --多倍有礼
		self:SetDuoBeiTips()
	elseif index == TabIndex.merge_activity_2127 then --聚灵助阵
		rawimg_bg_name = "a3_ldzd_bj"
		self:ShowIndexCallBackHeFuJuLing()
	elseif index == TabIndex.merge_activity_2128 then --限时秒杀
		rawimg_bg_name = "a3_xsms_bj"
		self:ShowIndexCallHeFuMiaoSha()
	elseif index == TabIndex.merge_activity_2129 then --排行榜
		rawimg_bg_name = "a3_hfhd_lmjc_bj"
		self:ShowIndexCallBackSPRank()
	elseif index == TabIndex.merge_activity_2119 then --猎魔达人
		rawimg_bg_name = "a3_hfhd_lmjc_bj"
		self:ShowIndexCallBackLieMoDaRen()
	elseif index == TabIndex.merge_activity_2249 then
		rawimg_bg_name = "a3_hfhd_xsms_bj"
		self:OnSelectExchangeShop()
	end

	local act_type = 0
	if index == TabIndex.merge_activity_2128 then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE
	elseif index == TabIndex.merge_activity_2129 then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_RANK
	elseif index == TabIndex.merge_activity_2118 then
		rawimg_bg_name = "a3_xyss_bg_1"
		act_type = ACTIVITY_TYPE.MERGE_ACT_HONGMENG_WUDAO
	elseif index == TabIndex.merge_activity_2119 then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_BOSS_HUNTER
	elseif index == TabIndex.merge_activity_2248 then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LOGIN_GIFT
	elseif index == TabIndex.merge_activity_2109 then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DAY_FIRST_RECHARGE
	elseif index == TabIndex.merge_activity_2110 then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LIMIT_RECHARGE
	elseif index == TabIndex.merge_activity_2111 then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DUOBEI
	elseif index == TabIndex.merge_activity_2249 then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_CONVERT
	elseif index == TabIndex.merge_activity_2127 then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN
	elseif index == TabIndex.merge_activity_2246 then
		act_type = ACTIVITY_TYPE.MERGE_ACT_FIREWORKS
	end

	if act_type > 0 then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.MergeActivityView, act_type)
	end

	-- local index_show = index == TabIndex.merge_activity_2129 or index == TabIndex.merge_activity_2249
	-- self.node_list["title_2"]:SetActive(not index_show)
	self:ShowIndexHMWD(index)

	local bundle, asset = ResPath.GetRawImagesPNG(rawimg_bg_name)
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function MergeActivityView:ClearMiaoShaoNewFlag()
	local is_enough_level = HeFuMiaoshaWGData.Instance:GetAvtivityIsOpen()
	local state = MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE)

	if not is_enough_level or not state then
		return
	end
end

function MergeActivityView:CloseCallBack()
	self:ClearMiaoShaoNewFlag()
	HeFuJuLingWGCtrl.Instance:CloseFriendView()
end

function MergeActivityView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.merge_activity_2248 then --登录有礼
				self:OperationFlushLoginView()
			elseif index == TabIndex.merge_activity_2118 then --鸿蒙悟道
				self:FlushHongMengView(v)
			elseif index == TabIndex.merge_activity_2246 then --占星
				self:OnFlushFireworks(v)
			elseif index == TabIndex.merge_activity_2109 then
				self:DTYYOnFlushShouChongView()
			elseif index == TabIndex.merge_activity_2112 then
				self:FlushZhaoCaiMiao()
			elseif index == TabIndex.merge_activity_2110 then
				self:DTYYFlushLeiChongView()
			elseif index == TabIndex.merge_activity_2111 then
				self:FlushDBView()
			elseif index == TabIndex.merge_activity_2128 then --限时秒杀
				self:FlushHeFuMiaoSha()
			elseif index == TabIndex.merge_activity_2127 then --聚灵助阵
				self:FlushHeFuJuLing()
			elseif index == TabIndex.merge_activity_2119 then
				self:OnFlushLieMoDaRen(param_t)
			elseif index == TabIndex.merge_activity_2129 then
				self:OnFlushSPRank(param_t)
			elseif index == TabIndex.merge_activity_2249 then
				self:FlushExchange()
			end
		elseif "RefreshLogin" == k then
			self:OperationFlushLoginView()
		elseif "ZhaoCaiMiaoMiaoPlayNum" == k then
			self:FlushZhaoCaiMiao()
		elseif "ZhaoCaiMiaoMiaoPlayXianyu" == k then
			self:ShowIndexCallZhaoCaiMiao()
		elseif "ZhaoCaiMiaoMiaoPlayTween" == k then
			self:PlayMiaoMiaoTween()
		elseif "RefreshLoginReward" == k then
			self:ShowLoginReward()
		elseif "RefreshDuoBei" == k then
			self:FlushDBView()
		end
	end
end

--tabbar是否加载完毕
function MergeActivityView:SetTabbarInfo()
	self.tabbar_loading = true
	self:SetTabSate()
	self:NewFalgChange()
end

--
--按钮的显示和隐藏
function MergeActivityView:SetTabSate()
	if nil == self.tabbar or not self.tabbar_loading then
		return
	end
	local open_num = 0
	local cur_select_index = 0
	-- 这里边的条件包含服务器的开启状态和你注册的开启条件，其他特殊处理你酌情来
	for k, v in ipairs(self.activity_cfg) do
		local tab_index = v.rank_id * 10

		local is_open = MergeActivityWGData.Instance:GetActivityState(v.activity_type)
		is_open = is_open and v.btn_type < 100 -- 当屏蔽用不在这个界面显示
		self.tabbar:SetToggleVisible(tab_index, is_open or false)
		if is_open then
			open_num = open_num + 1
		end
		if self.jump_index ~= nil and self.jump_index == tab_index then
			cur_select_index = open_num
		end
	end
	if cur_select_index > 7 then
		--65cell hight 1.13 cell space
		local y = (cur_select_index - 7) * (65 + 1.13)
		self:JumpTabbarReloadData(y)
	else
		self:JumpTabbarReloadData(0)
	end
end

--设置活动剩余时间 index: tab_index , end_time:活动结束时间戳
function MergeActivityView:SetActRemainTime(index, end_time)
	local act_end_time = 0
	if end_time then
		act_end_time = end_time
	else
		local activity_type = MergeActivityWGData.Instance:GetCurSelectActivityType(index)
		act_end_time = MergeActivityWGData.Instance:GetActivityInValidTime(activity_type)
	end

	if CountDownManager.Instance:HasCountDown("merge_activity_common_count_down") then
		CountDownManager.Instance:RemoveCountDown("merge_activity_common_count_down")
	end

	--合服排行结算后拦截
	if index == TabIndex.merge_activity_2129 then
		if MergeSpecialRankWGData.Instance:GetIsEnd() then
			if self.node_list and self.node_list.common_remain_time_text then
				self.node_list.common_remain_time_text.text.text = Language.MergeSPRank.YiJieShu
			end
			return
		end
	end

	if act_end_time ~= 0 then
		self.node_list.remain_time_root:SetActive(true)
		local time = act_end_time - TimeWGCtrl.Instance:GetServerTime()
		if time > 0 then
			CountDownManager.Instance:AddCountDown("merge_activity_common_count_down",
				BindTool.Bind(self.CommonUpdateTime, self),
				BindTool.Bind(self.CommonCompleteTime, self), nil, time, 1)
		end
	end
end

function MergeActivityView:CommonUpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	local str = string.format(Language.MergeActivity.ActEndTime, TimeUtil.FormatSecondDHM6(temp_seconds))
	if self.node_list and self.node_list.common_remain_time_text then
		self.node_list.common_remain_time_text.text.text = str
	end
end

function MergeActivityView:CommonCompleteTime(elapse_time, total_time)
	if self.node_list and self.node_list.common_remain_time_text and self.node_list.remain_time_root then
		self.node_list.remain_time_root:SetActive(false)
		self.node_list.common_remain_time_text.text.text = ""
	end
end

--设置活动剩余时间位置
function MergeActivityView:SetRemainTimePos(vector2_pos)
	self.node_list.remain_time_root.rect.anchoredPosition = vector2_pos or Vector2.zero
end

function MergeActivityView:SetRuleInfoActive(enabled)
	if self.node_list and self.node_list.rule_panel_root then
		self.node_list.rule_panel_root:SetActive(enabled)
	end
end

function MergeActivityView:SetRuleRootPos(vector2_pos)
	if self.node_list and self.node_list.rule_panel_root then
		self.node_list.rule_panel_root.rect.anchoredPosition = vector2_pos or Vector2.zero
	end
end

--设置外边玩法提示
function MergeActivityView:SetOutsideRuleTips(content)
	--self.node_list.outside_rule_content.text.text = content
end

function MergeActivityView:SetRuleInfo(rule_content, rule_title)
	self.rule_content = rule_content
	self.rule_title = rule_title
end

function MergeActivityView:ClearRuleInfo()
	self.rule_content = nil
	self.rule_title = nil
end

function MergeActivityView:OnClickCommonRuleBtn()
	if not self.rule_content or not self.rule_title then
		print_error("rule_content 或者 rule_title 是nil,  赶紧查合服活动调了 SetRuleInfo 的地方")
		return
	elseif self.rule_content == "" or self.rule_title == "" then
		print_error("rule_content 或者 rule_title 赋了空， 赶紧查合服活动调了 SetRuleInfo 的地方")
		return
	end

	RuleTip.Instance:SetContent(self.rule_content, self.rule_title)
end

function MergeActivityView:NewFalgChange()

end

function MergeActivityView:SetRightTimeTitleText(dec)
	self.node_list["tip_label"].text.text = dec
end

function MergeActivityView:TabbarReloadData(is_jump, index)
	self.jump_states = is_jump
	self.jump_index = index
end

function MergeActivityView:JumpTabbarReloadData(y)
	if self.jump_states and self.node_list and self.node_list["VerticalTabbarContent"] then
		self.jump_states = false
		self.jump_index = nil
		local x = self.node_list["VerticalTabbarContent"].rect.anchoredPosition.x
		self.node_list["VerticalTabbarContent"].rect.anchoredPosition = Vector2(x, y)
	end
end

function MergeActivityView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "btn_sel_reward_2" then
		return self.node_list[ui_name], BindTool.Bind(self.OnClickZiXuanBtn, self)
	end
end

VerMergeItemRender = VerMergeItemRender or BaseClass(VerItemRender)

function VerItemRender:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("merge_activity_count_down_idx" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("merge_activity_count_down_idx" .. self.index)
	end
end

function VerMergeItemRender:OnFlush()
	VerItemRender.OnFlush(self)
	if CountDownManager.Instance:HasCountDown("merge_activity_count_down_idx" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("merge_activity_count_down_idx" .. self.index)
	end

	local activity_type = MergeActivityWGData.Instance:GetCurSelectActivityType(self.index * 10)
	if NeedShowCountDown[activity_type] then
		--self.node_list.Text.rect.anchoredPosition = Vector2(15, 9)
		--self.node_list.TextHL.rect.anchoredPosition = Vector2(15, 9)
		local activity_type = MergeActivityWGData.Instance:GetCurSelectActivityType(self.index * 10)
		local act_end_time = MergeActivityWGData.Instance:GetActivityInValidTime(activity_type)
		if act_end_time ~= 0 then
			local time = act_end_time - TimeWGCtrl.Instance:GetServerTime()
			if time > 0 then
				CountDownManager.Instance:AddCountDown("merge_activity_count_down_idx" .. self.index,
					BindTool.Bind(self.CommonUpdateTime, self),
					BindTool.Bind(self.CommonCompleteTime, self), nil, time, 1)
			end
		end
	else
		--self.node_list.Text.rect.anchoredPosition = Vector2(15, -3)
		--self.node_list.TextHL.rect.anchoredPosition = Vector2(15, -3)
		self.node_list.time_txt.text.text = ""
	end
end

function VerMergeItemRender:CommonUpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	local str = TimeUtil.FormatDToHAndMS(temp_seconds)
	if self.node_list and self.node_list.time_txt then
		self.node_list.hl_time_txt.text.text = str
		self.node_list.time_txt.text.text = str
	end
end

function VerMergeItemRender:CommonCompleteTime(elapse_time, total_time)
	if self.node_list and self.node_list.time_txt then
		self.node_list.time_txt.text.text = "00:00:00"
		self.node_list.hl_time_txt.text.text = "00:00:00"
	end
end
