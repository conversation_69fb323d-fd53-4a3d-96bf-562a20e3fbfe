MainuiWGData = MainuiWGData or BaseClass()

MainuiWGData.IsSetCameraZoom = true
MainuiWGData.UserOperation = false

ActRemindList = {
	[ACTIVITY_TYPE.OPEN_SERVER]                     = RemindName.OpenServer,			-- 开服活动
	[ACTIVITY_TYPE.KF_ACTIVITY]						= RemindName.KFActivity,
	[ACTIVITY_TYPE.RAND_ACTIVITY_FIREWORKS]         = RemindName.Fireworks,				-- 烟花庆典
	[ACTIVITY_TYPE.HUNYAN]                          = RemindName.HUNYAN, 				-- 婚宴
	[ACTIVITY_TYPE.RAND_ACTIVITY_DAILYCONSUME]      = RemindName.DailyConsume, 			-- 每日累消
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY]  = RemindName.crazy_money_tree, 		-- 疯狂摇钱树
	[ACTIVITY_TYPE.ACTIVITY_DAILY_LOVE]             = RemindName.EveryDayOneLove,		-- 每日一爱
	[ACTIVITY_TYPE.RAND_WEEKEND_BOSS]               = RemindName.WeekendBoss, 			-- 周末Boss
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_HIDEWELFARE]  = RemindName.WelfareHideGift, 		-- 隐藏福利
	[ACTIVITY_TYPE.XinFuTeMai]                      = RemindName.XinFuTeMai, 			-- 新服特卖
	[ACTIVITY_TYPE.DayDayFanLi]                     = RemindName.DayDayFanLi, 			-- 天天返利
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ACTIVE_PLATE] = RemindName.ActivePlate, 			-- 活跃转盘
	[ACTIVITY_TYPE.FIRST_CHONGZHI]                  = RemindName.FirstCharge, 			-- 首充
	[ACTIVITY_TYPE.MEIRI_LEICHONG]                  = RemindName.DailyRecharge, 		-- 每日累充
	[ACTIVITY_TYPE.XIUZHEN_ROAD]                  	= RemindName.XiuZhenRoad, 			-- 修真之路
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY]  = RemindName.KFCityLove, 			-- 完美情人_全城热恋
	[ACTIVITY_TYPE.GOD_XUNBAO]  					= RemindName.GodGetReward, 			-- 天神夺宝
	[ACTIVITY_TYPE.GOD_TIANSHENROAD]				= RemindName.TianShenRoad,			-- 天神之路
	[ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN]				= RemindName.QuanMinBeiZhan,		-- 全民备战
	[ACTIVITY_TYPE.MARRY_PAIQI]						= RemindName.Marry_PaiQi,			-- 婚宴排期
	[ACTIVITY_TYPE.ZEROBUY]							= RemindName.ZeroBuy,				-- 零元购
	[ACTIVITY_TYPE.YIBWNWANLI]						= RemindName.YiBenWanLi,			-- 一本万利
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN]= RemindName.XianLing_GuZhen,		-- F2仙灵古阵
	[ACTIVITY_TYPE.BOSS_XUAN_SHANG]                 = RemindName.BossXuanShang,         -- F2boss悬赏
	[ACTIVITY_TYPE.QUN_XIONG_ZHU_LU]                = RemindName.QunXiongZhuLu,         -- F2群雄逐鹿
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG]= RemindName.ZhouYi_YunCheng,		-- F2周一运程
	[ACTIVITY_TYPE.OPEN_SERVER_ASSIST]				= RemindName.OpenServerAssist,		-- 开服助力
	[ACTIVITY_TYPE.FengShenBang]					= RemindName.FengShenBang,			-- F2封神榜
	[ACTIVITY_TYPE.SHITUXIULI]						= RemindName.ShiTuXiuLiMain,		-- F2师徒修历
	[ACTIVITY_TYPE.LIMIT_TIME_GIFT]					= RemindName.LimitTimeGift,			-- F2限时礼包
	[ACTIVITY_TYPE.OPERATIONACTIVITY]				= RemindName.OperationActivity,		-- 动态运营活动
	[ACTIVITY_TYPE.OPERATIONACTIVITY_TWO]			= RemindName.OperationActivity_Two,	-- 动态运营活动第二个按钮
    [ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN]			= RemindName.OperationTaskChain,	-- 运营活动_任务链
    [ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT]= RemindName.FortuneCatRemind,	    -- 运营活动_招财猫
    [ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY]		= RemindName.MustBuyRemind,	    	-- 运营活动_超值必买
    [ACTIVITY_TYPE.HEFU_ACTIVITY]		            = RemindName.Merge_Activity,	    -- 合服活动
    [ACTIVITY_TYPE.REBATE_ACTIVITY]		            = RemindName.Rebate_Activity,	    -- 合服活动
    [ACTIVITY_TYPE.FESTIVA_ACTIVITY]		        = RemindName.Festival_Activity,	    -- 节日活动
    [ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG]		    = RemindName.XianQiJieFeng,	    -- 仙器解封
    [ACTIVITY_TYPE.ACT_PIERRE_DIRECT_PURCHASE]		= RemindName.PierreDirectPurchase,	-- 臻品直购
    [ACTIVITY_TYPE.SHENBINGAWAKEN_ACTIVITY]		    = RemindName.TianShenAwaken,	    -- 神兵觉醒
    [ACTIVITY_TYPE.LIMITEDTIMEOFFER]		        = RemindName.RemindLimitedTimeOffer,-- 限时特惠
    [ACTIVITY_TYPE.MONTH_CARD_PROVOLEGE]		    = RemindName.MonthCardProvolege,	-- 月卡特权
    [ACTIVITY_TYPE.FESTIVAL_ACT_YANHUA_SHENGDIAN_2] = RemindName.NiuDan,				-- 幸运砸蛋
    [ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE] = RemindName.CatExplore,			-- 猫猫探险
    [ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE2] = RemindName.FlopDraw,			-- 翻牌好礼
    [ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW] = RemindName.RebateFireWorks,	-- 烟花抽奖
    [ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT] = RemindName.RebateExtinctGift,  -- 绝版赠礼
	[ACTIVITY_TYPE.PREMIUM_GIFT] 					   = RemindName.PremiumGift,        -- 超值赠礼
    [ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS] = RemindName.RebateDiscount,  -- 充值立减
	[ACTIVITY_TYPE.GOD_OF_WEALTH]                  = RemindName.Wealth_god, 		    -- 喜迎财神
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW] = RemindName.DIYDraw_One,			-- DIY1抽奖
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW] = RemindName.DIYDraw_Two,			-- DIY2抽奖
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GLORY_CRYSTAL] = RemindName.GloryCrystal,		-- 荣耀水晶
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3] = RemindName.XuYuanFreshPool,	-- 许愿仙池
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WEEK_CARD] = RemindName.HammerPlan,            -- 打策划
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DAILY_RECHARGE] = RemindName.OneDayRecharge,	-- 每日累充2
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW2] = RemindName.FireWorksDrawSecond,	-- 烟花抽奖2
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1] = RemindName.SpecialDrawTrunTable,	--特殊抽奖1 幸运转盘
	[ACTIVITY_TYPE.PRIVILEGEBUY] = RemindName.RemindPrivilegeBuy,						-- 特权直购
	[ACTIVITY_TYPE.OPERA_ACT_DRAGONSECRET] = RemindName.RemindDragonSecret,				--升龙密藏
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHAIN_DRAW] = RemindName.ChainDraw,				-- 一线牵
	[ACTIVITY_TYPE.ACTIVITY_TYPE_OA_EXCHANGE_SHOP] = RemindName.RemindCollectionWord,	--天师集字 兑换商店
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIKE] = RemindName.TianShiGodownHill,			-- 天师下山
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN] = RemindName.SupremeFieldsAct,			-- 领域直购
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WEIWODUZUN] = RemindName.WeiWoDunZun,			    --唯我独尊
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HOLY_BEAST_CALL] = RemindName.HolyBeastCall,    -- 圣兽召唤
	[ACTIVITY_TYPE.ZHI_ZUN_VIEW] = RemindName.Vip_Week,                                  -- 至尊特权
	[ACTIVITY_TYPE.GOLD_STONE_VIEW] = RemindName.GoldStoneRemind,                        -- 金石之言
	[ACTIVITY_TYPE.PRIVILEGED_GUIDANCE_VIEW] = RemindName.PrivilegedGuidance,            -- 特权引导
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK] = RemindName.RoleCharmRank,          -- 魅力榜
	[ACTIVITY_TYPE.ACTIVITY_FOLD_ONE] = RemindName.ACTIVITY_FOLD_ONE,                    -- 活动折叠自定义活动号--1
	[ACTIVITY_TYPE.ACTIVITY_FOLD_TWO] = RemindName.ACTIVITY_FOLD_TWO,                    -- 活动折叠自定义活动号--2
	[ACTIVITY_TYPE.ACTIVITY_FOLD_THREE] = RemindName.ACTIVITY_FOLD_THREE,                -- 活动折叠自定义活动号--3
	[ACTIVITY_TYPE.ACTIVITY_FOLD_FOUR] = RemindName.ACTIVITY_FOLD_FOUR,                  -- 活动折叠自定义活动号--4
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHESSBOARD] = RemindName.RemindChessPieces,	 -- 棋盘寻宝
	[ACTIVITY_TYPE.RAND_ACTIVIYY_TYPE_OA_ODYSSEYRMB] = RemindName.OdysseyPurchaseRemind, -- 永恒直购
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_JIANG_SHAN_RU_HUA] = RemindName.JiangShanRuHua,								-- 江山如画
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_A_LIFELONG_LOVE_TASK] = RemindName.LifeTimeOfLove,	 -- 一生所爱
	[ACTIVITY_TYPE.TREASUREPALACE] = RemindName.TreasurePalace,								-- 臻宝殿
	[ACTIVITY_TYPE.RAND_ACTIVITY_PANACEA_FURNACE] = RemindName.PanaceaFurnace,				-- 灵丹宝炉
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN] = RemindName.RemindVientiane,		-- 万象天引
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_MoRan_XuanYuan] = RemindName.MoRanXuanYuan,		-- 墨染轩辕
	[ACTIVITY_TYPE.RAND_ACTIVITY_ReikiSeed] = RemindName.ReikiSeed,							-- 灵气之种
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUANRI_CHANGHONG] = RemindName.SunRainbow,				-- 贯日长虹
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK] = RemindName.HelpRankRed,               -- 冲榜助力
	[ACTIVITY_TYPE.CROSS_ACTIVITY_LuckyGiftBag] = RemindName.LuckyGiftBagDailyReward,      		-- 幸运大礼包
	[ACTIVITY_TYPE.CROSS_ACTIVITY_LUCKYGIFTBAGLOCAL] = RemindName.LuckyGiftBagLocalDailyReward, -- 幸运大礼包(本服)
	[ACTIVITY_TYPE.LIMIT_TIME_GIFT_PURCHASE] = RemindName.LimitTimeGiftPurchase, 				-- 神秘直购礼包
	[ACTIVITY_TYPE.KF_ATTRIBUTE_STONE_RANK] = RemindName.KFAttributeStoneRank, 				-- 跨服属性宝石榜
	[ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK] = RemindName.LoverPkView,                   -- 仙侣PK
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_BLIND_BOX_CARD] = RemindName.MysteryBox,     -- 盲盒卡包
	[ACTIVITY_TYPE.YanYuGe] = RemindName.YanYuGe,                               -- 藏金商铺
	[ACTIVITY_TYPE.OPEN_SERVER_INVEST] = RemindName.OpenServerInvestView,                   -- 开服投资
	[ACTIVITY_TYPE.LORD_EVERY_DAY_SHOP] = RemindName.LoadEveryDayShop,					-- 领主商店 魔王仙藏
	[ACTIVITY_TYPE.WORLD_TREASURE] = RemindName.WorldTreasure, 									-- 天财地宝
	[ACTIVITY_TYPE.NEW_FESTIVA_ACTIVITY]			= RemindName.NewFestivalActivity,	    -- 新节日活动
	[ACTIVITY_TYPE.LIFE_INDULGENCE_ACTIVITY]		= RemindName.LifeIndulgence,			-- 终身特惠
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DREAM_SECRET] = RemindName.PhantomDreamland,		-- 幻梦秘境
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_RECHARGE_RANK] = RemindName.LocalRechargeRank,		-- 运营活动 - 充值榜
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONSUME_RANK] = RemindName.LocalConsumeRank,			-- 运营活动 - 消费榜
	[ACTIVITY_TYPE.CAPABILITY_WELFARE] = RemindName.CapabilityWelfare,						-- 战力福利
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT] = RemindName.TotalRechargeGift,	-- 累充豪礼
	[ACTIVITY_TYPE.HUNDRED_EQUIP] = RemindName.HundredEquipView,								-- 百倍爆装(假活动)
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DRAGON_TRIAL] = RemindName.DragonTrial,				-- 五气朝元
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SWORD_FROSTBITE] = RemindName.OneSwordFrostbiteView,	-- 一剑霜寒
}


function MainuiWGData:__init()
	if MainuiWGData.Instance then
		ErrorLog("[MainuiWGData]:Attempt to create singleton twice!")
	end
	MainuiWGData.Instance = self

	self.target_obj = nil
	self.mainui_icon_list = {}
	self.target_objid = COMMON_CONSTS.INVALID_OBJID

	local bosssuppress_cfg = ConfigManager.Instance:GetAutoConfig("bosssuppress_auto")
	self.boss_level_suppress = ListToMapList(bosssuppress_cfg.level_suppress,"scene_id","monster_id")
	self.boss_cap_suppress = ListToMapList(bosssuppress_cfg.capability_suppress,"scene_id","monster_id")

	self.main_task_list = {}
	self.bianqiang_list_cache = {}
	self.main_act_btn_is_click = {} -- 主界面活动按钮是否点击过了
	self.main_act_limit_day_list = {}

	self.main_top_shrink_arrow_is_click = false  -- 右上角收缩按钮的箭头按钮是否已经点击过了
	self.mainui_act_btn_num = 0

	self.mainui_func_box_cache = {}
end

function MainuiWGData:__delete()
	MainuiWGData.Instance =nil
	self.target_obj = nil
	self.target_objid = nil
    self.main_task_list = {}
    self.boss_refresh_item_list = nil
    self.boss_invoke_cfg = nil
    self.boss_rebirth_cfg = nil
end


function MainuiWGData:ChangeMainUiChatIconList(model_name, flush_name, state)
	local model_list = self.mainui_icon_list[model_name]
	if model_list then
		if not state then
			model_list[flush_name] = nil
		else
			model_list[flush_name] = 1
		end
	else
		if state then
			self.mainui_icon_list[model_name] = {}
			self.mainui_icon_list[model_name][flush_name] = 1
		end
	end
end

function MainuiWGData:GetMainUiIconList()
	return self.mainui_icon_list
end

function MainuiWGData:SetMianUiBottomBtnVisible(is_visible)
	self.is_mianui_bottom_btn = is_visible
end

function MainuiWGData:GetMianUiBottomBtnVisible()
	return self.is_mianui_bottom_btn
end

function MainuiWGData:SetTargetObj(obj)
	self.target_obj = obj
	if obj then
		self.target_objid = obj:GetObjId()
	else
		self.target_objid = COMMON_CONSTS.INVALID_OBJID
	end
end

function MainuiWGData:GetTargetObj()
	if self.target_objid ~= nil then
		if self.target_objid >= 0 and Scene.Instance:GetObjectByObjId(self.target_objid) == self.target_obj then
			return self.target_obj, self.target_objid
		end
	end

	self.target_obj = nil
	self.target_objid = COMMON_CONSTS.INVALID_OBJID
	return nil, nil
end

function MainuiWGData:GetMonstIDList(min_lv, max_lv)
	if not self.monst_level_cfg then
		self.monst_level_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").monst_level
	end

	-- -- body
	-- local monst_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").monst_level
	local list = {}
	for _, v in pairs(self.monst_level_cfg) do
		if v.min_level >= min_lv and v.max_level <= max_lv then
			table.insert(list, v.monst_id)
		end
	end
	return list
end

--随机获取一个怪物ID
function MainuiWGData:GetRandomMonstID(min_lv, max_lv)
	if 0 == TaskWGData.Instance.shang_rand_monst_id then
		local list = self:GetMonstIDList(min_lv, max_lv)
		if #list > 0 then
			TaskWGData.Instance.shang_rand_monst_id = list[math.random(1, #list)] or 0
		end

		return TaskWGData.Instance.shang_rand_monst_id or 0
	end

	return TaskWGData.Instance.shang_rand_monst_id or 0
end

--引导副本场景
function MainuiWGData.IsGuideFbScene(scene_type)
	scene_type = scene_type or Scene.Instance:GetSceneType()
	return scene_type == SceneType.FBCT_NEWPLAYERFB
end

function MainuiWGData:GetMainUIButtonName(open_param)
	local list = Split(open_param, "#")
	local fun_name = ""
	if #list == 2 then
		fun_name = list[2]
	else
		fun_name = open_param
	end

	local button_name = MainuiWGCtrl.Instance:GetAllButtonName()
	local btn_name, btn_pos
	-- 根据功能开启的标签参数获取按钮名字
	for k,v in pairs(button_name) do
		for k1,v1 in pairs(v) do
			if v1[1] == fun_name then
				btn_name = k1
				btn_pos = k
			end
		end
	end
	return btn_name, btn_pos
end

function MainuiWGData:GetMainUIButtonNameAndPos(fun_cfg)
	local btn_name, btn_pos
	if IsEmptyTable(fun_cfg) then
		return btn_name, btn_pos
	end

	local zp_btn = fun_cfg.zjm_zp_btn_name ~= ""
	local fp_btn = fun_cfg.zjm_fp_btn_name ~= ""
	local bottom_isopen = SettingWGCtrl.Instance:GetMainToggleFlag()

	if zp_btn then
		if fp_btn and bottom_isopen then
			btn_name = fun_cfg.zjm_fp_btn_name
			btn_pos = FunOpenIconFlyPos.Bottom
		else
			btn_name = fun_cfg.zjm_zp_btn_name
			btn_pos = FunOpenIconFlyPos.Top
		end
	elseif fp_btn then
		btn_name = fun_cfg.zjm_fp_btn_name
		btn_pos = FunOpenIconFlyPos.Bottom
	end

	return btn_name, btn_pos
end

function MainuiWGData:CreateCacheTable()
	self.power_cache_table = {time = Status.NowTime + 20} -- 防止不恢复
end
function MainuiWGData:GetCacheTable()
	return self.power_cache_table
end

function MainuiWGData:CacheTableAddData(data)
	-- table.insert(self.power_cache_table, data)
	if not self.power_cache_table then return end
	self.power_cache_table[1] = data
end
function MainuiWGData:SetEmpty()
	self.power_cache_table = {}
end
function MainuiWGData:CacheTableClearData()
	self.power_cache_table = nil
end

function MainuiWGData:SaveTaskDesc( desc, task_id, task_status )
	local match = string.match(desc, "%(.-%)")
	if match then
		desc = string.gsub(desc, "%(.-%)", "<color=#7CFFB7FF>" .. match .. "</color>")
	end
	self.main_task_list[task_id] = {desc = desc, task_id = task_id, task_status = task_status}
end

function MainuiWGData:GetTaskDesc()
	return self.main_task_list[TaskWGData.Instance:CurrTaskId()]
end

function MainuiWGData:CheckMainUIActIsLimit( act_cfg )
	if nil == act_cfg then
		return false
	end

	local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	--不在开服天数内
	if cur_openserver_day < act_cfg.act_serveropen or cur_openserver_day > act_cfg.act_serverover_day then
		return true
	end

	if type(act_cfg.recharge_val) == "number" then
		local real_recharge_val = RechargeWGData.Instance:GetRealChongZhiRmb()
		if real_recharge_val < act_cfg.recharge_val then
			return true
		end
	end

	--是否需要等级上限
	local is_need_limit = false
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local week_day = TimeUtil.FormatSecond3MYHM1(server_time)
	local limit_day_list = self.main_act_limit_day_list[act_cfg.act_type]
	if not limit_day_list then
		if act_cfg.limit_day and "" ~= act_cfg.limit_day then
			limit_day_list = {}
			local day_list = Split(act_cfg.limit_day,":")
			for k,v in pairs(day_list) do
				limit_day_list[k] = tonumber(v)
			end
		else
			limit_day_list = {}
		end
		self.main_act_limit_day_list[act_cfg.act_type] = limit_day_list
	end

	for k,v in pairs(limit_day_list) do
		if v == week_day then
			is_need_limit = true
			break
		end
	end

	local level = GameVoManager.Instance:GetMainRoleVo().level
	--根据周几来判断是否有等级上限(迷？)
	if is_need_limit then
		if level < act_cfg.level then --不满足最小参与等级
			return true
		elseif level >= act_cfg.level and level < act_cfg.level_max then --等级都满足，但天数不足(控制跨服活动显隐)
			if self:CheckCross(act_cfg, level) then
				return false
			else
				return true
			end
		elseif level >= act_cfg.level_max then --大于参与等级(控制本服活动显隐)
			--等级超过加入跨服等级，但不满足跨服天数，还是可以显示本服的图标
			if self:CheckOpenServerDay(act_cfg, level) then
				return false
			else
				return true
			end
		end
	else
		if level < act_cfg.level then	--等级不足
			return true
		end
	end
	return false
end

function MainuiWGData:CheckCross(act_cfg, level)
	--策划需求，跨服温泉不再有开服天数限制
	-- if act_cfg.act_type == ACTIVITY_TYPE.KF_HOTSPRING then --跨服温泉
	-- 	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	-- 	local openday_limit = HotSpringWGData.Instance:GetAddCrossDay()
	-- 	local add_cross_level = HotSpringWGData.Instance:GetAddCrossLevel()
	--  	if level >= add_cross_level and open_day >= openday_limit then --符合跨服
	--  		return true
	--  	else
	--  		return false
	--  	end
	-- end
	return true --不是以上活动,默认显示
end

function MainuiWGData:CheckOpenServerDay(act_cfg, level)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if act_cfg.act_type == ACTIVITY_TYPE.HOTSPRING then --本服温泉
		local openday_limit = HotSpringWGData.Instance:GetAddCrossDay()
		if open_day < openday_limit  then --不符合跨服天数
			return true
		else --符合天数的，根据等级判断是否隐藏本服活动
		 	local add_cross_level = HotSpringWGData.Instance:GetAddCrossLevel()
		 	if level >= add_cross_level then
		 		return false
		 	end
		end
	end
	return false
end

function MainuiWGData:GetBossRebirthCfg()
	if not self.boss_rebirth_cfg then
		self.boss_rebirth_cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto").boss_rebirth[1]
	end
	return self.boss_rebirth_cfg
end

function MainuiWGData:GetBossInvokeCfg()
	if not self.boss_invoke_cfg then
		self.boss_invoke_cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto").boss_invoke[1]
	end
	return self.boss_invoke_cfg
end

function MainuiWGData:IsBossRefreshInvokeItem(item_id)
    if not self.boss_refresh_item_list then
        self.boss_refresh_item_list = {}
        local cfg = self:GetBossInvokeCfg()
        self.boss_refresh_item_list[cfg.boss_invoke_card_1] = true
        self.boss_refresh_item_list[cfg.boss_invoke_card_2] = true
        local cfg1 = self:GetBossRebirthCfg()
        self.boss_refresh_item_list[cfg1.rebirth_item] = true
        self.boss_refresh_item_list[cfg1.rebirth_item_2] = true
        -- self.boss_refresh_item_list[cfg1.rebirth_item_3] = true
        self.boss_refresh_item_list[cfg1.rebirth_all_item_1] = true
    end
	return self.boss_refresh_item_list[item_id]
end

function MainuiWGData:GetBossRefreshItemId(idx)
    local scene_type = Scene.Instance:GetSceneType()
    local cfg = self:GetBossRebirthCfg()

	if idx == 1 then --boss单个刷新
        if scene_type == SceneType.XianJie_Boss or scene_type == SceneType.KF_BOSS then --蛮荒
            return tonumber(cfg.rebirth_item_2)
        elseif scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
            return tonumber(cfg.rebirth_item)
        end
    elseif idx == 2 then--boss层刷新
        if scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
            return tonumber(cfg.rebirth_all_item_1)
        end
    elseif idx == 3 then  --boss召唤
        local invoke_cfg = self:GetBossInvokeCfg()
        if scene_type == SceneType.KF_BOSS then --蛮荒
            return tonumber(invoke_cfg.boss_invoke_card_2)
        elseif scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
            return tonumber(invoke_cfg.boss_invoke_card_1)
        end
		--策划zp需求，屏蔽这个道具.
    -- elseif idx == 4 then  --通用刷新
    --     if scene_type == SceneType.XianJie_Boss or scene_type == SceneType.KF_BOSS or scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then --蛮荒
    --         return tonumber(cfg.rebirth_item_3)
    --     end
    end
end

function MainuiWGData:GetBossRefreshItemIdStuff()
	local scene_type = Scene.Instance:GetSceneType()
    local cfg = self:GetBossRebirthCfg()
	local item_id
	local num = 0

	local check_item_enough = function (item_id)
		local num = ItemWGData.Instance:GetItemNumInBagById(item_id)

		if num > 0 then
			return item_id, num
		end
	end

	if scene_type == SceneType.KF_BOSS then
		local stuff_id, num = check_item_enough(tonumber(cfg.rebirth_item_2))

		if stuff_id then
			return stuff_id, num
		end
	elseif scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
		local stuff_id, num = check_item_enough(tonumber(cfg.rebirth_item))

		if stuff_id then
			return stuff_id, num
		end
	end

	--[[ zp要屏蔽.
	-- 通用
	local stuff_id2, num = check_item_enough(tonumber(cfg.rebirth_item_3))

	if stuff_id2 then
		return stuff_id2, num
	end
	]]

	return item_id, num
end

function MainuiWGData:BossRefreshItemIdCanUpdate()
	local scene_type = Scene.Instance:GetSceneType()
	return scene_type == SceneType.XianJie_Boss or scene_type == SceneType.KF_BOSS or scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS
end

--返回三个子按钮的状态和大按钮的状态
function MainuiWGData:UpdateQuickItemByScene(scene_type)
    scene_type = scene_type or Scene.Instance:GetSceneType()

	local cfg = ConfigManager.Instance:GetAutoConfig("call_boss_auto")
	local call_boss_base_cfg = cfg.other[1]
	local level_limit = call_boss_base_cfg and call_boss_base_cfg.show_level or 1
	local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_show_btn = role_level >= level_limit

    local state_tb = {}
    for i = 1, 4 do
        local item_id = self:GetBossRefreshItemId(i)
        if item_id then
            state_tb[i] = true
        else
            state_tb[i] = false
        end
    end
    return state_tb, is_show_btn
end

--是否是boss刷新卡，激活卡，是否可显示使用按钮
function MainuiWGData:IsBossRefreshOrInvokeCard(item_id)
    for i = 1, 4 do
        local itemid = self:GetBossRefreshItemId(i)
		local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
		local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
		if is_in_team and is_leader then
			if itemid and itemid == item_id then
				local num = NewTeamWGData.Instance:GetSelectUseShareItemInfo(item_id)
				return num > 0
			end
		else
			if itemid and itemid == item_id and ItemWGData.Instance:GetItemNumInBagById(itemid) > 0 then
				return true
			end
		end
    end

    return false
end

--获取操作类型
function MainuiWGData:GetRefreshOrInvokeCardtype(item_id)
    for i = 1, 4 do
        local itemid = self:GetBossRefreshItemId(i)
        if itemid and itemid == item_id then
            return i
        end
    end

    return 0
end

function MainuiWGData:GetBossDecTiredCfgBySceneType(scene_type)
	scene_type = scene_type or Scene.Instance:GetSceneType()
	if not self.boss_tire_cfg then
		self.boss_tire_cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto").boss_tired
	end

	for i, v in pairs(self.boss_tire_cfg) do
		if v.scene_type == scene_type then
			return v
		end
	end

	return nil
end

function MainuiWGData:IsDecTiredItem(item_id)
	if not self.boss_tire_cfg then
		self.boss_tire_cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto").boss_tired
	end

	for i, v in pairs(self.boss_tire_cfg) do
		if v.consume_item == item_id then
			return v.dec_tired_type
		end
	end

	return nil
end


function MainuiWGData:GetBossDecTiredCfgByDecType(dec_type)
	if not self.boss_tire_cfg then
		self.boss_tire_cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto").boss_tired
	end

	for i, v in pairs(self.boss_tire_cfg) do
		if v.dec_tired_type == dec_type then
			return v
		end
	end

	return nil
end

--检测相同类型的提醒，如果有，重新设置值
function MainuiWGData:CheckTheSameTipType(check_list, tip_type, repetition_num, callback, effect_id, param)
	for k, v in pairs(check_list) do
		if v.tip_type == tip_type then
			v.repetition_num = repetition_num
			v.callback = callback
			v.effect_id = effect_id
			v.callback_param = param
			return true
		end
	end
	return false
end

function MainuiWGData:SetBianQiangListCache(tip_type, repetition_num, callback, effect_id, param, strong_tip_type)
	local data = {tip_type = tip_type,
		callback = callback,
		repetition_num = repetition_num,
		effect_id = effect_id,
		callback_param = param,
		strong_tip_type = strong_tip_type,
	}

	self.bianqiang_list_cache[data.tip_type] = data
	self:UpdateBianQiangListCache()
end

function MainuiWGData:GetBianQiangList()
	return self.bianqiang_list_cache or {}
end

--变强按钮列表
function MainuiWGData:GetBianQiangListCache()
	return self.bianqiang_filter_list_cache or {}
end

function MainuiWGData:GetBianQiangGetRewardListCache()
	return self.bianqiang_get_reward_list_cache or {}
end

function MainuiWGData:UpdateBianQiangListCache()
	local list = self.bianqiang_list_cache or {}
	self.bianqiang_filter_list_cache = {}
	self.bianqiang_get_reward_list_cache = {}

	for i, v in pairs(list) do
		if v.repetition_num > 0 then
			if v.strong_tip_type == MAINUI_STRONG_TIP_TYPE.GET_REWARD then
				table.insert(self.bianqiang_get_reward_list_cache, v)
				table.sort(self.bianqiang_get_reward_list_cache, SortTools.KeyLowerSorter("tip_type"))
			elseif v.strong_tip_type == MAINUI_STRONG_TIP_TYPE.UP_POWER then
				table.insert(self.bianqiang_filter_list_cache, v)
				table.sort(self.bianqiang_filter_list_cache, SortTools.KeyLowerSorter("tip_type"))
			end
		end
	end
end

-- 设置首充变强信息
function MainuiWGData:SetShouChongBianqiang(enable, callback)
	self.shouchong_enable = enable
	self.shouchong_bianqiang_callback = callback
end

function MainuiWGData:GetShouChongBianqiangData()
	local data = {}
	data.tip_type = MAINUI_TIP_TYPE.SHOU_CHONG_TE_HUI
	data.callback = self.shouchong_bianqiang_callback
	return self.shouchong_enable, data
end

function MainuiWGData:SetCurShowBuffType(client_buff_type)
	self.cur_show_buff_type = client_buff_type
end

function MainuiWGData:GetCurShowBuffType()
	return self.cur_show_buff_type
end

function MainuiWGData:GetBossRePressCfg()
	return self.boss_level_suppress, self.boss_cap_suppress
end

--boss压制
function MainuiWGData:IsBossRePress(boss_id, boss_level)
	local cur_scene_id = Scene.Instance:GetSceneId()
	local role_level = RoleWGData.Instance:GetRoleVo().level
	local role_cap = RoleWGData.Instance:GetMainRoleCap()
	local boss_level_suppress , boss_cap_suppress = self:GetBossRePressCfg()

	local press_type = 0
	local level_cfg = {}
	local cap_cfg = {}
	if boss_level_suppress[cur_scene_id] then
		level_cfg = boss_level_suppress[cur_scene_id][boss_id] or {}
	end

	if boss_cap_suppress[cur_scene_id] then
		cap_cfg = boss_cap_suppress[cur_scene_id][boss_id] or {}
	end

	if level_cfg and next(level_cfg) then
		for k,v in pairs(level_cfg) do
			if boss_level - role_level >= v.suppress_lv then
				press_type = press_type + BossRePressType.level
				break
			end
		end

	end

	for i,v in pairs(cap_cfg) do
		if v.recommend_cap - role_cap >= v.suppress_cap then
			press_type = press_type + BossRePressType.cap
			break
		end
	end

	return press_type
end


--添加boss 压制buff
function MainuiWGData:AddBossPressBuff(effect_list, repress_type)
	if repress_type == BossRePressType.all then
		local effect = FightWGData.CreateEffectInfo()
		effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_BOSS_LEVEL_YZ
		table.insert(effect_list,1,{type = 1, info = effect})

		local effect1 = FightWGData.CreateEffectInfo()
		effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_BOSS_CAP_YZ
		table.insert(effect_list,1,{type = 1, info = effect1})
	elseif repress_type == BossRePressType.level then
		local effect = FightWGData.CreateEffectInfo()
		effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_BOSS_LEVEL_YZ
		table.insert(effect_list,1,{type = 1, info = effect})
	elseif repress_type == BossRePressType.cap then
		local effect = FightWGData.CreateEffectInfo()
		effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_BOSS_CAP_YZ
		table.insert(effect_list,1,{type = 1, info = effect})
	end
end

--离线挂机Tips显示顺序
NEED_OPEN_TIPS_TYPE = {
	OFF_LINEREST_TYPE = 1,
	--PERAL_INVAILD_TYPE = 2,
	GURAD_INVAILD_TYPE = 2,
}

function MainuiWGData:InitNeedOpenViewType()
	self.insert_open_list = {}
	for k,v in pairs(NEED_OPEN_TIPS_TYPE) do
		local data = {}
		data.can_open = false
		data.fun = nil
		data.first_flag = false
		data.has_data = false
		data.last_open_flag =false
		self.insert_open_list[v] = data
	end
end

function MainuiWGData:GetNeedOpenView(form_type)
	if self.insert_open_list == nil then
		self:InitNeedOpenViewType()
	end
	return self.insert_open_list[form_type]
end

function MainuiWGData:InsertNeedOpenView(form_type,can_open,fun)
	if self.insert_open_list == nil then
		self:InitNeedOpenViewType()
	end

	if self.insert_open_list[form_type] == nil then
		return
	end

	self.insert_open_list[form_type].can_open = can_open
	self.insert_open_list[form_type].fun = fun
	self.insert_open_list[form_type].has_data = true

	if self.insert_open_list[form_type].first_flag and fun and can_open then
		fun()
		return
	end

	local count = 0
	for k,v in ipairs(self.insert_open_list) do
		if v.has_data then
			count = count + 1
		end
	end
	if count >= #self.insert_open_list then
		local last_open_flag = true
		for k,v in ipairs(self.insert_open_list) do
			v.last_open_flag = last_open_flag
			last_open_flag = (not v.can_open) or (v.first_flag)
		end

		for k,v in ipairs(self.insert_open_list) do
			if v and v.can_open and v.first_flag == false and v.last_open_flag then
				if v.fun then
					v.fun()
				end
				return
			end
		end
	end
end

function MainuiWGData:OpenNextView(cur_form_type)
	local cur_data = self.insert_open_list[cur_form_type]
	if cur_data then
		cur_data.first_flag = true
	end
	local form_type = cur_form_type + 1
	if self.insert_open_list[form_type] == nil then
		return
	end
	local open_data = self.insert_open_list[form_type]
	if open_data.can_open and open_data.first_flag == false then
		open_data.last_open_flag = true
		if open_data.fun then
			open_data.fun()
		end
	end
end

function MainuiWGData:ClearNeedOpenList()
	self.insert_open_list = nil
end

function MainuiWGData:SetActivityBtnIsClick(activity_type)
	if activity_type == nil then
		return
	end

	self.main_act_btn_is_click[activity_type] = true
end

-- 活动按钮是否已经点击过了
function MainuiWGData:GetActivityBtnIsClick(activity_type)
	return self.main_act_btn_is_click[activity_type]
end

-- 右上角收缩按钮的箭头按钮是否已经点击过了
function MainuiWGData:GetTopShrinkIsClick()
	return self.main_top_shrink_arrow_is_click
end

function MainuiWGData:SetTopShrinkIsClick()
	self.main_top_shrink_arrow_is_click = true
end

-- 当前场景是否需要切换出任务面板（Boss场景/修真路 点击左边收起按钮之后需要切换出任务面板），是否是修真路（other和任务切换）
function MainuiWGData:NeedShowBossShrinkBtn()
    local scene_type = Scene.Instance:GetSceneType()
    local result = false
	if scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.Fb_Welkin then
		result = true
    end
    return result, scene_type == SceneType.Fb_Welkin
end

-- 转换成主界面右上角按钮的时间格式
function MainuiWGData:ConvertMainTopBtnTime(time)
	local time_str = ""
	if time > 0 then
		local hour = math.floor(time / 3600)
		local minute = math.floor(time / 60)
		local second = math.floor(time % 60)
		if hour == 0 then
			time_str = string.format("%02d:%02d", minute, second)
		elseif hour >= 24 then
			time_str = string.format(Language.Common.ShowTime2, math.floor(hour / 24))
		else
			time_str = string.format(Language.Common.ShowTime3, hour)
		end
	end
	return time_str
end

function MainuiWGData:GetMainUiActBtnKeyNum()
	self.mainui_act_btn_num = self.mainui_act_btn_num + 1
	return self.mainui_act_btn_num
end

function MainuiWGData:GetBianQiangBottomOtherCfg()
	if not self.bianqiang_btn_other_cfg then
		self.bianqiang_btn_other_cfg = ConfigManager.Instance:GetAutoConfig("bianqiang_adv_auto").other[1]
	end

	return self.bianqiang_btn_other_cfg
end

function MainuiWGData:GetBianQiangBottomCfg()
	if not self.bianqiang_btn_info then
		self.bianqiang_btn_info = ConfigManager.Instance:GetAutoConfig("bianqiang_adv_auto").bianqiang_btn_info
	end

	return self.bianqiang_btn_info
end

-- 获取显示优先级最高的变强底部按钮配置
function MainuiWGData:GetTopPriorityBianQiangBottomBtnInfo()
	local cfg_list = self:GetBianQiangBottomCfg()
	local open_btn_list = {}
	local top_priority_btn_cfg = nil
	for k,v in pairs(cfg_list) do
		if self:GetBianQiangBottomIsOpen(v.id) then
			if top_priority_btn_cfg then
				if v.priority > top_priority_btn_cfg.priority then
					top_priority_btn_cfg = v
				end
			else
				top_priority_btn_cfg = v
			end
		end
	end
	return top_priority_btn_cfg
end

-- 主界面变强底部按钮是否开启
function MainuiWGData:GetBianQiangBottomIsOpen(id)
	local cfg_list = self:GetBianQiangBottomCfg()
	local cfg = cfg_list[id]
	if cfg == nil then
		return false
	end

	-- 首充
	if id == BIANQIANG_BOTTOM_ID.FIRST_RECHARGE then
		if ServerActivityWGData.Instance:AtLeastOneGradeRecharge() then
			return false
		end
	end

	-- 连续充值
	if id == BIANQIANG_BOTTOM_ID.EVERYDAY_RECHARGE_LIANCHONG then
		local all_complete = true
		-- 遍历所有档位
		for k, lxcz_cur_btn_type in pairs(EVERYDAY_LIANXU_RECHARGE_DAY_TYPE) do
		    local cur_recharge_day = EveryDayLianXuRechargeWGData.Instance:GetCurDayByRechargeType(lxcz_cur_btn_type)
		    local cur_cfg = EveryDayLianXuRechargeWGData.Instance:GetRewardCfgByRechargeDat(lxcz_cur_btn_type, cur_recharge_day)
    		local reward_state = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish
			if cur_cfg.reward_index then
		        reward_state = EveryDayLianXuRechargeWGData.Instance:GetRewardState(lxcz_cur_btn_type, cur_cfg.reward_index + 1, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal)
		    end
		    -- 档位是否未完成
		    if reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish then
		    	all_complete = false
		    	break
		    end
		end
		if all_complete then
			return false
		end
	end

	-- 零元购V6
	if id == BIANQIANG_BOTTOM_ID.ZERO_BUY_VIP then
		local info_list = RechargeWGData.Instance:GetVipZeroBuyInfo()
		if info_list and info_list.buy_time > 0 then
			return false
		end
	end

	-- 月卡
	if id == BIANQIANG_BOTTOM_ID.MONTH_CARD then
		-- 如果已投资月卡，则返回false
		if not RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.MonthCard) then
			return false
		end
	end

	-- 18元直购
	if id == BIANQIANG_BOTTOM_ID.RECHARGE_ZHIGOU then
		local money = EveryDayRechargeRebateWGData.Instance:GetNeedRechargeCount()
		local already_recharge = EveryDayRechargeRebateWGData.Instance:GetAlreadyRecharge()
		money = money / RECHARGE_BILI
		already_recharge = already_recharge / RECHARGE_BILI
		if already_recharge >= money then
			return false
		end
	end
	
	-- 仙器特典-十连
	-- if id == BIANQIANG_BOTTOM_ID.XIANQI_TEDIAN then
	-- 	if not XianQiTeDianWGData.Instance:GetIsOpenTeDian() then--boss层刷新
	-- 		return false
	-- 	end
	-- 	local cur_act_id = XianQiTeDianWGData.Instance:GetCurShiLian()
	-- 	local sale_info = XianQiTeDianWGData.Instance:GetSubActSaleInfo(cur_act_id, 1)
	-- 	if not sale_info then
	-- 		return false
	-- 	end

	-- 	-- 如果不是未购买
	--     if sale_info and sale_info.status ~= YuanShenSaleSubActSaleStatus.NotBuy then
	--     	return false
	--     end
	-- end

	-- 四象升级特权
	if id == BIANQIANG_BOTTOM_ID.SIXIANG_EXP_POOL then
		if FightSoulWGData.Instance:GetExpPoolLevel() > 0 then
			return false
		end
	end

	-- 功能开启检查
	if cfg.module_name and cfg.module_name ~= "" then
		if not FunOpen.Instance:GetFunIsOpened(cfg.module_name) then
			return false
		end

		local lingzhi_type = nil
		if cfg.id == BIANQIANG_BOTTOM_ID.LINGZHI_WING then
			lingzhi_type = LINGZHI_SKILL_TYPE.WING
		elseif cfg.id == BIANQIANG_BOTTOM_ID.LINGZHI_FABAO then
			lingzhi_type = LINGZHI_SKILL_TYPE.FABAO
		elseif cfg.id == BIANQIANG_BOTTOM_ID.LINGZHI_JIANZHEN then
			lingzhi_type = LINGZHI_SKILL_TYPE.JIANZHEN
		elseif cfg.id == BIANQIANG_BOTTOM_ID.LINGZHI_SHENBING then
			lingzhi_type = LINGZHI_SKILL_TYPE.SHENBING
		end

		if lingzhi_type then
			local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
			if server_info == nil or IsEmptyTable(server_info) then
				return false
			end
			local is_open = server_info.is_open == 1
			if is_open then
				return false
			end
		end

	end

	-- 活动状态检查
	if cfg.act_type and cfg.act_type ~= "" and cfg.act_type > 0 then
		if not ActivityWGData.Instance:GetActivityIsOpen(cfg.act_type) then
			return false
		end
	end

	return true
end

function MainuiWGData:GetFPAdvanceNoticeList()
	if not self.fp_show_pic_cfg then
		self.fp_show_pic_cfg = ConfigManager.Instance:GetAutoConfig("face_book_auto").fp_show_pic
	end

	-- local cfg = ConfigManager.Instance:GetAutoConfig("face_book_auto").fp_show_pic
	local show_list = {}
	local role_data = RoleWGData.Instance
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = role_data:GetAttr("level") or 0
	local vip_level = role_data:GetAttr("vip_level") or 0
	local acc_total_gold = RechargeWGData.Instance:GetHistoryRecharge()

	local day_meet, level_meet, vip_meet, gold_meet
	for k, v in ipairs(self.fp_show_pic_cfg) do
		day_meet = server_day >= v.open_time and server_day <= v.close_time
		level_meet = role_level >= v.min_level and role_level <= v.max_level
		--vip_meet = vip_level >= v.open_vip and vip_level <= v.close_vip
		gold_meet = acc_total_gold >= v.gold_total

		if day_meet and level_meet and gold_meet then
			local is_close = self:GetAdvanceNoticeCellState(TASK_PANEL_ADVANCE_NOTICE_STATE.CLOSE_STATE, v.close_condition)
			if not is_close then
				local is_open = self:GetAdvanceNoticeCellState(TASK_PANEL_ADVANCE_NOTICE_STATE.OPEN_STATE, v.open_condition)
				if is_open then
					table.insert(show_list, v)
				end
			end
		end
	end

	table.sort(show_list, SortTools.KeyLowerSorter("type"))

	return show_list
end

function MainuiWGData:GetAdvanceNoticeCellState(index, condition)
	local state = true
	local state2 = true
	local condition_list = string.split(condition, "|")

	for k1, v1 in pairs(condition_list) do
		v1 = tonumber(v1)
		if v1 == 0 then
			state = index == TASK_PANEL_ADVANCE_NOTICE_STATE.OPEN_STATE and true or false
		elseif v1 == TASK_PANEL_ADVANCE_NOTICE_CONDITION.ONE_YUAN_SALE then
			state2 = true
		elseif v1 == TASK_PANEL_ADVANCE_NOTICE_CONDITION.REBATE_PRIVILEGE then
			state2 = RechargeWGData.Instance:IsBuyInvestCard()
		elseif v1 == TASK_PANEL_ADVANCE_NOTICE_CONDITION.FUND then
			local ser_data = RechargeWGData.Instance:GetTouZiPlanSerData(0)
			local cur_grade = ser_data and ser_data.cur_grade or 0
			state2 = cur_grade >= 1
		elseif v1 == TASK_PANEL_ADVANCE_NOTICE_CONDITION.GODDESS_BLESSING1 then
			local buy_seq = RechargeVolumeWGData.Instance:GetRmbBuySeq()
			state2 = buy_seq >= 1
		elseif v1 == TASK_PANEL_ADVANCE_NOTICE_CONDITION.GODDESS_BLESSING2 then
			local buy_seq = RechargeVolumeWGData.Instance:GetRmbBuySeq()
			state2 = buy_seq >= 2
		elseif v1 == TASK_PANEL_ADVANCE_NOTICE_CONDITION.BILLIONSUBSIDY then
			local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
			state2 = member_level > 1
		elseif v1 == TASK_PANEL_ADVANCE_NOTICE_CONDITION.YANYU_PAVILION then
			local is_buy = YanYuGeWGData.Instance:IsGetOneYYTQ()
			state2 = is_buy
		elseif v1 == TASK_PANEL_ADVANCE_NOTICE_CONDITION.SUPREME_PRIVILEGE then
			local is_buy = YanYuGeWGData.Instance:IsAllZanZhuTeQuanUnLock()
			state2 = is_buy
		end

		state = state and state2
	end

	return state
end

function MainuiWGData:GetShowPicCfg()
	if not self.fp_show_pic_cfg then
		self.fp_show_pic_cfg = ConfigManager.Instance:GetAutoConfig("face_book_auto").fp_show_pic
	end

	return self.fp_show_pic_cfg
end

function MainuiWGData:GetAdvanceNoticePopTimeCfg()
	if not self.ad_pop_time_cfg then
		self.ad_pop_time_cfg = ConfigManager.Instance:GetAutoConfig("face_book_auto").advance_notice_pop_time
	end

	return self.ad_pop_time_cfg
end

function MainuiWGData:GetAdvanceNoticePopFinalTime()
	local cfg = self:GetAdvanceNoticePopTimeCfg()
	local final_time = cfg[#cfg].pop_time
	return final_time
end

function MainuiWGData:GetFirstRechargeTipState()
	if not self.roleexp_auto_other_open_state then
		self.roleexp_auto_other_open_state = ConfigManager.Instance:GetAutoConfig("roleexp_auto").other[1].open
	end

	-- local open_state = ConfigManager.Instance:GetAutoConfig("roleexp_auto").other[1].open
	return self.roleexp_auto_other_open_state == FIRSTRECHAEGE_AND_LEVELRECHARGE_SWITCH_TYPE.SHOW_LEVEL_RECHARGE
end

function MainuiWGData:GetFirstRechargeTimeTipState()
	local open_state = ConfigManager.Instance:GetAutoConfig("roleexp_auto").other[1].time_open
	return open_state == FIRSTRECHAEGE_AND_LEVELRECHARGE_SWITCH_TYPE.SHOW_LEVEL_RECHARGE
end

function MainuiWGData:SetRouteInfo(protocol)
	self.rare_attr_added_time = protocol.rare_attr_added_time
end

function MainuiWGData:GetRouteInfo(protocol)
	return self.rare_attr_added_time or 0
end

function MainuiWGData:CheckIsHaveBreakShieldBuff(target_id)
	local eff_list = FightWGData.Instance:GetOtherRoleEffectList(target_id)
	for k,v in pairs(eff_list) do
		if v.buff_type == BUFF_TYPE.EBT_BREAK_SHIELD then
			return true
		end
	end

	return false
end

function MainuiWGData:AddFuncBoxCache(act_type, data)
	if not self.mainui_func_box_cache then
		self.mainui_func_box_cache = {}
	end

	if self.mainui_func_box_cache[act_type] then
		self.mainui_func_box_cache[act_type] = nil
	end

	self.mainui_func_box_cache[act_type] = data
end

function MainuiWGData:RemoveFuncBoxCache(act_type)
	if self.mainui_func_box_cache and self.mainui_func_box_cache[act_type] then
		self.mainui_func_box_cache[act_type] = nil
	end
end

function MainuiWGData:GetFuncBoxDataCache()
	local data_list = {}
	
	if IsEmptyTable(self.mainui_func_box_cache) then
		return data_list
	end

	for k, v in pairs(self.mainui_func_box_cache) do
		if not IsEmptyTable(v.act_cfg) then
			if self:IsCanShowFuncBoxAct(v.act_type) then
				local act_info = ActivityWGData.Instance:GetActivityStatuByType(v.act_type)
				local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(v.act_type)
				
				if not IsEmptyTable(act_info) and not IsEmptyTable(act_cfg) then
					local show_limit = self:CheckMainUIActIsLimit(v.act_cfg)
	
					if not show_limit then
						table.insert(data_list, v)
					end
				end
			end
		end
	end

	return data_list
end

-- 功能盒子中一些特殊活动，隐藏接口
function MainuiWGData:IsCanShowFuncBoxAct(act_type)
	-- if act_type == ACTIVITY_TYPE.GUILD_FB then
	-- 	return not GuildWGData.Instance:IsFinishGuildFb()
	-- end

	return true
end