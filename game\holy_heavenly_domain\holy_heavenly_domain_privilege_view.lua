HolyHeavenlyDomainPrivilegeView = HolyHeavenlyDomainPrivilegeView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainPrivilegeView:__init()
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_privilege")
end

function HolyHeavenlyDomainPrivilegeView:LoadCallBack()
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model_root)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuy, self))
	XUI.AddClickEventListener(self.node_list.btn_item1, BindTool.Bind(self.OnClickItem, self))
	XUI.AddClickEventListener(self.node_list.btn_item2, BindTool.Bind(self.OnClickItem, self))
	XUI.AddClickEventListener(self.node_list.btn_item3, BindTool.Bind(self.OnClickItem, self))
end

function HolyHeavenlyDomainPrivilegeView:ShowIndexCallBack()
    -- self:FlushTime()
end

function HolyHeavenlyDomainPrivilegeView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("holy_heavenly_domain_privilege") then
        CountDownManager.Instance:RemoveCountDown("holy_heavenly_domain_privilege")
    end

    if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end
end

function HolyHeavenlyDomainPrivilegeView:OnFlush()
	local privilege_time = HolyHeavenlyDomainWGData.Instance:GetPrivilegeBuyInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
    local is_active_privilege = privilege_time > server_time
	local privilege_cfg = HolyHeavenlyDomainWGData.Instance:GetOtherCfg()
    self.node_list.buy_flag:CustomSetActive(is_active_privilege)
    self.node_list.btn_buy:CustomSetActive(not is_active_privilege)

    if not is_active_privilege then
        self.node_list.desc_btn_buy.text.text =  RoleWGData.GetPayMoneyStr(privilege_cfg.rmb_buy_price, privilege_cfg.rmb_buy_type)
    end

    self:FlushModel(privilege_cfg)
	self:FlushTime()
end

function HolyHeavenlyDomainPrivilegeView:FlushTime()
	self.node_list.count_down_tip:CustomSetActive(false)
    self.node_list.desc_count_down_time.text.text = ""
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local time = HolyHeavenlyDomainWGData.Instance:GetPrivilegeBuyInfo()
    
    if time > server_time then
		self.node_list.count_down_tip:CustomSetActive(true)
        if CountDownManager.Instance:HasCountDown("holy_heavenly_domain_privilege") then
            CountDownManager.Instance:RemoveCountDown("holy_heavenly_domain_privilege")
        end

		self.node_list.desc_count_down_time.text.text = string.format(Language.HolyHeavenlyDomain.SeasonEndTimeDesc, ToColorStr(TimeUtil.FormatSecondDHM8(time - server_time), COLOR3B.GREEN)) 
        CountDownManager.Instance:AddCountDown("holy_heavenly_domain_privilege",
		    function (now_time, total_time)
			    if self.node_list.desc_count_down_time then
				    local time = math.ceil(total_time - now_time)
				    self.node_list.desc_count_down_time.text.text = string.format(Language.HolyHeavenlyDomain.SeasonEndTimeDesc, ToColorStr(TimeUtil.FormatSecondDHM8(time), COLOR3B.GREEN))
			    end
		    end,
		    function ()
			    if self.node_list.desc_count_down_time then
				    self.node_list.desc_count_down_time.text.text = ""
					self.node_list.count_down_tip:CustomSetActive(false)
			    end
		    end,
        time, nil, 1)
    end
end

function HolyHeavenlyDomainPrivilegeView:OnClickBuy()
	local privilege_time = HolyHeavenlyDomainWGData.Instance:GetPrivilegeBuyInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
    local buy_flag = privilege_time > server_time

	if not buy_flag then
		local privilege_cfg = HolyHeavenlyDomainWGData.Instance:GetOtherCfg()
		RechargeWGCtrl.Instance:Recharge(privilege_cfg.rmb_buy_price, privilege_cfg.rmb_buy_type)
	end
end

function HolyHeavenlyDomainPrivilegeView:FlushModel(model_cfg)
	if IsEmptyTable(model_cfg) then
		return
	end

    local display_data = {}
	local model_show_type = tonumber(model_cfg["model_show_type"]) or 1

	if model_cfg["model_show_itemid"] ~= 0 and model_cfg["model_show_itemid"] ~= "" then
		local split_list = string.split(model_cfg["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = model_cfg["model_bundle_name"]
	display_data.asset_name = model_cfg["model_asset_name"]
	display_data.render_type = model_show_type - 1
	display_data.model_click_func = function ()
		TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
	end

	self.model_display:SetData(display_data)

	local pos_x, pos_y = 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.model_root.rect, pos_x, pos_y)

	if model_cfg["display_scale"] then
		local scale = model_cfg["display_scale"]
		Transform.SetLocalScaleXYZ(self.node_list.model_root.transform, scale, scale, scale)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rotation_tab = string.split(model_cfg.rotation,"|")
		self.node_list.model_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

function HolyHeavenlyDomainPrivilegeView:OnClickItem()
	RuleTip.Instance:SetContent(Language.HolyHeavenlyDomain.PrivilegeTipContent, Language.HolyHeavenlyDomain.PrivilegeTipTitle)
end