HUNYAN_TYPE = {
	HUNYAN_OPERA_TYPE_INVALID = 0,
	HUNYAN_OPERA_TYPE_INVITE = 1,						-- 婚宴邀请
	HUNYAN_OPERA_TYPE_HONGBAO = 2,						-- 婚宴发红包
	HUNYAN_OPERA_TYPE_HUAYU = 3,						-- 浪漫花雨
	HUNYAN_OPERA_TYPE_YANHUA = 4,						-- 婚宴燃放烟花
	HUNYAN_OPERA_TYPE_MAX = 5,
}

WeddingWGData = WeddingWGData or BaseClass()

WeddingWGData.READY = 1			-- 婚宴状态 准备
WeddingWGData.STATE = 2			-- 婚宴状态 开始
WeddingWGData.END = 3

function WeddingWGData:__init()
	if WeddingWGData.Instance ~= nil then
		ErrorLog("[WeddingWGData] Attemp to create a singleton twice !")
	end
	WeddingWGData.Instance = self

	self.hunyan_info_list = {}
	self.hunyan_opera = {}
	self.hunyan_guest_info = { 
		yanhua_count = 0
	}
	self.hunyan_param = {}

	self.marry_scence_id = 0

	self.blessing_info = {}
	self.qingyuan_cfg_auto = ConfigManager.Instance:GetAutoConfig("qingyuanconfig_auto")
	self.profess_cfg = ListToMap(self.qingyuan_cfg_auto.wedding_rewrad_exp, "role_level")
end

function WeddingWGData:__delete()
	WeddingWGData.Instance = nil
end

function WeddingWGData:GetHunyanCfg()
	return self.qingyuan_cfg_auto.hunyan_cfg[1]
end

function WeddingWGData:GetHunyanList()
	return self.hunyan_info_list
end

function WeddingWGData:GetScenceId()
	return self.marry_scence_id
end

function WeddingWGData:GetHunyanInfo(key)
	return self.hunyan_info_list[key]
end

function WeddingWGData:GetCurInHunyanInfo()
	return self.hunyan_info_list[GameVoManager.Instance:GetMainRoleVo().scene_key]
end

function WeddingWGData:GetOwnHunyanInfo()
	for _,huanyan_info in pairs(self.hunyan_info_list) do
		for k,v in pairs(huanyan_info.marryuser_list) do
			if v.marry_uid == GameVoManager.Instance:GetMainRoleVo().role_id then
				return huanyan_info
			end
		end
	end
	return nil
end

function WeddingWGData:GetIsOwnHunyan(key)
	local own_info = self:GetOwnHunyanInfo()
	if nil ~= own_info and key == own_info.fb_key then
		return true
	end 
	return false
end

-- 获取发了多少次红包数量
function WeddingWGData:GetFreeFaHongBaoNum()
	local hunyan_info = self:GetCurInHunyanInfo()
	if nil == hunyan_info then return end

	local hunyancfg = self.qingyuan_cfg_auto.hunyan_cfg[1]
	local ifno_list = hunyan_info.marryuser_list
	local my_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local hongbao_num = 0
	if hunyancfg ~= nil and hunyancfg.free_fahongbao_num ~= nil and ifno_list ~= nil then
		for k,v in pairs(ifno_list) do
			if v.marry_uid == my_id then
				hongbao_num = hunyancfg.free_fahongbao_num - v.hongbao_count
				if hongbao_num > 0 then
					return hongbao_num
				end
			end
		end
	end
	return 0
end

-- 获取放了多少次烟花数量
function WeddingWGData:GetFreeYanHuaNum()
	local hunyancfg = self.qingyuan_cfg_auto.hunyan_cfg[1]
	local guest_info = self.hunyan_guest_info.yanhua_count
	local yanhua_num = 0
	if hunyancfg ~= nil and hunyancfg.free_yanhua_num ~= nil and guest_info ~= nil then
		yanhua_num = hunyancfg.free_yanhua_num - guest_info
		if yanhua_num > 0 then
			return yanhua_num
		end
	end
	return 0
end

-- 贺礼赠送需要的元宝费用
function WeddingWGData:GetHeLiGoldNum(num)
	local hunyancfg = self.qingyuan_cfg_auto.hunyan_cfg[1]
	if hunyancfg ~= nil and hunyancfg["gift_price_"..num] ~= nil then
		return hunyancfg["gift_price_"..num]
	end
	return 0
end

-- 获取最大可得到的经验值
function WeddingWGData:GetCanRewardExpByLevel(level)
	if self.profess_cfg ~= nil and self.profess_cfg[level] then
		return self.profess_cfg[level].award_exp_max
	end
	return 0
end

-- 获取购买红包或烟花的费用
function WeddingWGData:GetBuyGold(buy)
	local hunyancfg = self.qingyuan_cfg_auto.hunyan_cfg[1]
	if hunyancfg ~= nil and hunyancfg.buy_hongbao_gold ~= nil and buy == 0 then
		return hunyancfg.buy_hongbao_gold
	end
	if hunyancfg ~= nil and hunyancfg.buy_yanhua_gold ~= nil and buy == 1 then
		return hunyancfg.buy_yanhua_gold
	end
	return 0
end

function WeddingWGData:GetSpecialParamChange()
	return self.hunyan_param
end

function WeddingWGData:SetSpecialParamChange(info)
	local vo = {}
	vo.obj_id = info.obj_id
	vo.param = info.param

	local role = Scene.Instance:GetRoleByObjId(info.obj_id)
	if nil ~= role then
		role:GetVo().special_param = info.param
		role:UpdateNameBoard()
		Scene.Instance:SceneSpecialHandler(role)
	else
		table.insert(self.hunyan_param, vo)
	end
end

function WeddingWGData:SetHunyanInfo(info)
	if nil == info or nil == info.fb_key then return end

	if info.hunyan_state == WeddingWGData.READY or info.hunyan_state == WeddingWGData.STATE then
		self.hunyan_info_list[info.fb_key] = info
	else
		self.hunyan_info_list[info.fb_key] = nil
	end

	local role = Scene.Instance.main_role
	if nil ~= role then
		local is_married = self:GetIsInMarried(role)
		role:GetVo().special_param = is_married == true and 1 or 0
		role:UpdateNameBoard()
		Scene.Instance:SceneSpecialHandler(role)
	end
end

function WeddingWGData:RemoveHunyanInfoByKey(key)
	self.hunyan_info_list[key] = nil
end

function WeddingWGData:GetHunyanGuestInfo()
	return self.hunyan_guest_info
end

function WeddingWGData:SetHunyanGuestInfo(info)
	self.hunyan_guest_info.yanhua_count = info.yanhua_count
end

function WeddingWGData:SetHunyanOpera(info)
	self.hunyan_opera.obj_id = info.obj_id
	self.hunyan_opera.opera_type = info.opera_type
	self.hunyan_opera.opera_param = info.opera_param
	self.hunyan_opera.reserve = info.reserve
end

-- 用来处理开启婚宴的数据
function WeddingWGData:OpenHunyanHandler()
	-- 进入婚宴副本
	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_HUNYAN, 0, 0)
end


function WeddingWGData:GetIsInMarried(role)
	local info = self:GetCurInHunyanInfo()
	if info == nil then return end
	local info_list = info.marryuser_list
	local role_id = role:GetVo().role_id
	local lover_id = role:GetVo().lover_uid
	if info_list == nil or role_id == nil or lover_id == nil then return end
	local is_organizers = false
	for k,v in pairs(info_list) do
		if v.marry_uid == role_id or v.marry_uid == lover_id then
			is_organizers = true
		else
			is_organizers = false
		end
	end
	return is_organizers
end

function WeddingWGData:GetWeddingInfodata()
	local info = self:GetCurInHunyanInfo() or {}
	local info_list = info.marryuser_list
	if info_list == nil then return {} end
	local man_name, woman_name = "",""
	for _,v in pairs(info_list) do
		if v.sex == 1 then
			man_name = v.marry_name
		else
			woman_name = v.marry_name
		end
	end
	man_name = man_name
	woman_name = woman_name
	local he = Language.Wedding.FbTitle2
	local xhzx = HtmlTool.GetHtml(Language.Wedding.FbTitle4, COLOR3B.RED, 24)
	local state_str = ""
	if info.hunyan_state == WeddingWGData.READY then
		state_str = Language.Wedding.FbInfoReady
	end
	if info.hunyan_state == WeddingWGData.STATE then
		state_str = Language.Wedding.FbInfoState
	end
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = man_name, alg = RichHAlignment.HA_CENTER}, 
			{str = he, alg = RichHAlignment.HA_CENTER}, 
			{str = woman_name, alg = RichHAlignment.HA_CENTER}, 
			{str = xhzx, alg = RichHAlignment.HA_CENTER}, 
			{str = "    " .. string.format(Language.Wedding.FbTitle1, state_str)},
			{str = "    " .. Language.Wedding.FbTitle1, timer = info.next_state_timestmp},
		},
	}
	return fb_task_data
end

function WeddingWGData:SetBlessingRecordInfo(protocol)
	self.blessing_info = protocol.applicant_list
end

function WeddingWGData:GetBlessingRecordInfo()
	table.sort(self.blessing_info, SortTools.KeyUpperSorter("timestamp"))
	return self.blessing_info
end

function WeddingWGData:GetBlessingListCfg(param)
	local blessing_cfg = self.qingyuan_cfg_auto.wedding_blessing
	for k,v in pairs(blessing_cfg) do
		if v.param == param then
			return v
		end
	end
end

function WeddingWGData:GetBlessingDesc(seq)
	local blessing_cfg = self.qingyuan_cfg_auto.wedding_blessing
	for k,v in pairs(blessing_cfg) do
		if v.seq == seq and v.blessing_type == 0 then
			return v
		end
	end
end

function WeddingWGData:GetBlessingCfgById(id)
	local blessing_cfg = self.qingyuan_cfg_auto.wedding_blessing
	for k,v in pairs(blessing_cfg) do
		if v.param == id then
			return v
		end
	end
end

--弹幕信息
function WeddingWGData:SetMarryDanMuInfo(protocol)
	if nil == self.danmu_list then
		self.danmu_list = {}
	end
	local data = {}
	data.sender_name = protocol.sender_name
	data.item_id = protocol.item_id
	data.barrage = protocol.barrage
	self.danmu_list[#self.danmu_list + 1] = data
end

function WeddingWGData:GetMarryDanMuInfo()
	return self.danmu_list or {}
end
--清除数据（true——全清除，false 一个一个清理）
function WeddingWGData:DelMarryDanMuInfo(enable)
	if self.danmu_list then
		if enable then
			self.danmu_list = {}
			return
		end
		if #self.danmu_list > 0 then
			table.remove(self.danmu_list,1)
		end
	end
end

--结婚拜堂开始时间
function WeddingWGData:SetMarryBaiTangStartTime(time)
	self.baitang_start_time = time or 0
end

function WeddingWGData:GetMarryBaiTangStartTime()
	return self.baitang_start_time or 0
end