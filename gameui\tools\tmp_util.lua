TMPUtil = TMPUtil or BaseClass()

-- 根据文本内容计算文本大小
function TMPUtil.GetTMPBounds(tmp_text, content,width,is_ceil)
    local preferred_values = tmp_text:GetPreferredValues(content, width, 0)
	local width = preferred_values.x > width and width or preferred_values.x
	if is_ceil then
		return Vector2(width, math.ceil(preferred_values.y))
	else
		return Vector2(width, preferred_values.y)
	end
end

-- 更新TMP材质
function TMPUtil.UpdateMat(text, mat_name)
	if text then
		local function OnLoadCompleteMat(material)
			if text and material then
				text.fontSharedMaterial = material
			end
		end
		ResPoolMgr:GetMaterial("uis/fonts_bundle", mat_name, OnLoadCompleteMat, false)
	else
		print_error("TMP控件为空，请检查代码")
	end

end

-- 注册TMP Link点击回调
function  TMPUtil.AddClickEventListener(text, callback, index)
	local tmp_link_opener = text:GetComponent(typeof(TMPTextLinkOpener))
	tmp_link_opener:AddClickListener(callback, index)
end

--移除零宽空格
function TMPUtil.RemoveZeroWidthSpace(str)
	return string.gsub(str, "(\226\128\139)", "")
end