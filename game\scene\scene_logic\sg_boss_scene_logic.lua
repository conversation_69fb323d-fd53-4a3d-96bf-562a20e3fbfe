SgBossSceneLogic = SgBossSceneLogic or BaseClass(CommonFbLogic)
SgBossSceneLogic.SGBOXES = {198, 197}
function SgBossSceneLogic:__init()

end

function SgBossSceneLogic:__delete()

end

-- 进入场景
function SgBossSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	MainuiWGCtrl.Instance:ResetLightBoss()
	BossWGCtrl.Instance:Close()
	BossWGCtrl.Instance:EnterSceneCallback()
	BaseFbLogic.SetLeaveFbTip(true)
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)

	-- FuhuoWGCtrl.Instance:SetFuhuoCallback(nil,function ()
	-- 	local value_1,value_2
	-- 	value_1,value_2,GuajiCache.monster_id = BossWGData.Instance:GetCurSelectBossID()
 --        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Monster)
	-- end)
	self:GoToPos()
end

function SgBossSceneLogic:OnObjCreate(obj)
	if obj and not SceneObj.select_obj and self:IsEnemy(obj) then
		-- GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
	end
end

-- 获取挂机打怪的位置
function SgBossSceneLogic:GetGuiJiMonsterPos()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = nil
    local target_y = nil
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end

function SgBossSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	BossWGCtrl.Instance:OutSceneCallback(true)
	-- UiInstanceMgr.Instance:CloseRewardAction()
	BossWGData.Instance:SetDabaoBossAngryValue({angry_value = 0})
	BossWGCtrl.Instance:CloseDriveView()
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
	FuhuoWGCtrl.Instance:SetFuhuoCallback(nil,nil)

	if BossWGData.Instance:GetIsEnterInScene() then
		BossWGCtrl.Instance:OpenBossViewByScene(old_scene_type, new_scene_type)
	end

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
	end
end

function SgBossSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	if self:RolePickUpFallItem() then
		return true
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
		local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
		target_obj = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
		if target_obj ~= nil and self:IsRoleEnemy(target_obj, main_role) then
			MainuiWGCtrl.Instance:SetTargetObj(target_obj)
		end
	end

	target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local _, _, boss_id = BossWGData.Instance:GetCurSelectBossID()
		target_obj = Scene.Instance:SelectMinDisMonster(boss_id)
		if target_obj ~= nil then
			MainuiWGCtrl.Instance:SetTargetObj(target_obj)
		end
	end

	-- target_obj = MainuiWGData.Instance:GetTargetObj()
	-- if target_obj == nil or (target_obj ~= nil and target_obj:GetType() == SceneObjType.GatherObj) then
	-- 	for k, v in pairs(SgBossSceneLogic.SGBOXES) do
	-- 		local gather_obj = Scene.Instance:SelectMinDisGather(v)
	-- 		if nil ~= gather_obj then
	-- 			MainuiWGCtrl.Instance:SetTargetObj(gather_obj)
	-- 			self:MoveToObj(gather_obj)
	-- 			break
	-- 		end
	-- 	end
	-- end

	local mosnter_type = nil
	if target_obj ~= nil and target_obj:GetType() == SceneObjType.Monster then
		mosnter_type = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[target_obj:GetVo().monster_id].type
	end

	-- target_obj = MainuiWGData.Instance:GetTargetObj()
	-- if (target_obj ~= nil and target_obj:GetType() ~= SceneObjType.GatherObj) or target_obj == nil then
		BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
	-- 	return
	-- end
end

-- 是否是挂机打怪的敌人
function SgBossSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function SgBossSceneLogic:GetGuajiCharacter()
	local target_obj
	local is_need_stop = false

	target_obj, is_need_stop = self:GetNormalRole()
	if target_obj ~= nil then
		return target_obj, nil, is_need_stop
	end
	if target_obj == nil then
		return self:GetMonster(), nil, is_need_stop
	end
end

function SgBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local info = self:GetGuaJiInfo()
	local is_stop = info ~= nil

	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if info ~= nil then
				if not v:IsDeleted() then
					local pos_x, pos_y = v:GetLogicPos()
					local dis = GameMath.GetDistance(info.x, info.y, pos_x, pos_y, false)
					if dis <= info.aoi_range * info.aoi_range then
						GuajiCache.target_obj = v
						return v, is_stop
					end
				end
			else
				GuajiCache.target_obj = v
				return v, is_stop
			end
		end
	end

	return nil, is_stop
end

function SgBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()

		local info = self:GetGuaJiInfo()
		if info ~= nil then
			distance_limit = info.aoi_range * info.aoi_range
			x = info.x
			y = info.y
		end

		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function SgBossSceneLogic:GoToPos()
  	local cur_bossType, cur_bossLayer, cur_bossId = BossWGData.Instance:GetCurSelectBossID()
  	local data = BossWGData.Instance:GetSGAllBossByBossId(cur_bossId)

  	if data and data.type == BossWGData.MonsterType.Boss then
  		local sence_id = Scene.Instance:GetSceneId()
  		if not data.x_pos or not data.y_pos then return end
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)

		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = cur_bossId
		MoveCache.param1 = cur_bossId
		local range = BossWGData.Instance:GetMonsterRangeByid(cur_bossId)
		GuajiWGCtrl.Instance:MoveToPos(sence_id, data.x_pos, data.y_pos, range)
  	end
end

function SgBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	monsters_list = BossWGData.Instance:GetSGMonsterList(monsters_list)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function SgBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

function SgBossSceneLogic:GetFbSceneMonsterCfg()
	return BossWGData.Instance:GetSGMonsterList()
end

function SgBossSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	self:CheckGuaJiPosMove()
end