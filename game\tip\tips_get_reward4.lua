TipsGetValueRewardView = TipsGetValueRewardView or BaseClass(SafeBaseView)

local col_num = 10 							 		--每行多少个
local ANI_SPEED = 0.1
local reword_count = 0 				--该次寻宝奖励物品个数
local ani_flag_t = {}
local ani_count = 1
local scroll_verticalNormalizedPosition = 1
local row_num = 1								--显示3行
local lerp = 0.015--1 / (50 - (col_num * row_num)*0)* 0.5		--每次减少多少


local Sort_Type = {
	[GameEnum.ITEM_BIGTYPE_EQUIPMENT] = 10,
	[GameEnum.ITEM_BIGTYPE_EXPENSE] = 9,
	[GameEnum.ITEM_BIGTYPE_GIF] = 8,
	[GameEnum.ITEM_BIGTYPE_OTHER] = 7,
	[GameEnum.ITEM_BIGTYPE_VIRTUAL] = 6,
}


function TipsGetValueRewardView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop
    self.view_name = "TipsGetValueRewardView"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_result_panel")
    self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_value_reward_result")
end

function TipsGetValueRewardView:ReleaseCallBack()
	if nil ~= self.zhanshi_grid then
		self.zhanshi_grid:DeleteMe()
		self.zhanshi_grid = nil
	end
	if nil ~= self.zhanshi_ten_grid then
		self.zhanshi_ten_grid:DeleteMe()
		self.zhanshi_ten_grid = nil
	end

	if self.best_item_list then
		self.best_item_list:DeleteMe()
		self.best_item_list = nil
	end

	if self.rect_five_tween then
		self.rect_five_tween:Kill() 
	end

	self:CancelTimeQuest()
	self:CancelMoveScrollTimeQuest()
	self.is_skip_anim =	true
	self:ReleaseData()
end

function TipsGetValueRewardView:ReleaseData()
	self.id_list = nil
	self.again_func = nil
	self.other_info = nil
	self.no_need_sort = nil
	self.sure_func = nil
	self.best_list = nil
end

function TipsGetValueRewardView:LoadCallBack()
	self.is_skip_anim = true

	self:InitXunBaoZhanshi()
	self:RegisterEvent()
end

--初始化格子
function TipsGetValueRewardView:InitXunBaoZhanshi()
	local asset_bundle = "uis/view/rolebag_ui_prefab"
    local asset_name = "value_zhanshi_item_cell"

	self.zhanshi_grid = CommonValueRewardGrid.New()
	local t = {
		col = col_num, 
		change_cells_num = 1, 
		assetBundle = asset_bundle,
        assetName = asset_name,
		list_view = self.node_list["ph_zhanshii_cell"],
    	itemRender = CommonValueRewardCell
	}
    self.zhanshi_grid:CreateCells(t)
    self.zhanshi_grid:SetStartZeroIndex(false)

	self.zhanshi_ten_grid = CommonValueRewardGrid.New()
	local ten_t = {
		col = 5, 
		change_cells_num = 1, 
		assetBundle = asset_bundle,
        assetName = asset_name,
		list_view = self.node_list["ph_zhanshii_ten_cell"],
    	itemRender = TenCommonValueRewardCell
	}
    self.zhanshi_ten_grid:CreateCells(ten_t)
    self.zhanshi_ten_grid:SetStartZeroIndex(false)

	if not self.best_item_list then
        self.best_item_list = AsyncListView.New(BestValueItemCellRender, self.node_list.best_item_list)
    end
end

function TipsGetValueRewardView:CloseCallBack()
	GlobalEventSystem:Fire(OtherEventType.Common_Reward_Close3)
end

-- 注册按钮事件
function TipsGetValueRewardView:RegisterEvent()
	XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind1(self.OnSure, self))						--确定关闭界面
	XUI.AddClickEventListener(self.node_list.btn_one_more, BindTool.Bind1(self.OnQuchuAgain, self))				--再来一次
	XUI.AddClickEventListener(self.node_list.skip_anim_toggle, BindTool.Bind(self.OnClinkSkipAnim, self))
end

function TipsGetValueRewardView:OnClinkSkipAnim(is_on)
	self.is_skip_anim = not is_on
end


function TipsGetValueRewardView:SetData(id_list, again_func, other_info, no_need_sort, sure_func)
	self.id_list = id_list
	self.again_func = again_func
	self.other_info = other_info
	self.no_need_sort = no_need_sort
	self.sure_func = sure_func
	self.best_list = nil
end

--刷新数据
function TipsGetValueRewardView:OnFlush()
	self.node_list.skip_anim_toggle.toggle.isOn = not self.is_skip_anim
	self.node_list.btn_one_more:CustomSetActive(self.again_func ~= nil and self.other_info ~= nil)
	self.node_list.skip_anim_toggle:CustomSetActive(self.again_func ~= nil)
	self.node_list.img_zbitem:CustomSetActive(false)

	if self.other_info then
		self.node_list.btn_sure_text.text.text = self.other_info.sure_text or Language.Common.Confirm
		self.node_list.btn_again_txt.text.text = self.other_info.again_text or Language.TreasureHunt.BtnText[0]
		self.node_list.spend_root:CustomSetActive(self.other_info.stuff_id ~= nil and self.other_info.times ~= nil and self.other_info.spend ~= nil)

		if self.other_info.stuff_id then
			local item_cfg = ItemWGData.Instance:GetItemConfig(self.other_info.stuff_id)
			local has_num = ItemWGData.Instance:GetItemNumInBagById(self.other_info.stuff_id) --拥有的材料数量
			self.node_list.xianyu_icon:CustomSetActive(has_num < self.other_info.times)
			self.node_list.one_more_cosume:CustomSetActive(has_num < self.other_info.times)
			self.node_list.zbitem_key:CustomSetActive(has_num > 0)

			self.node_list.zbitem_key_num.text.text = has_num .. "/" .. self.other_info.times
			local zb_item_bundle, zb_item_asset = ResPath.GetItem(item_cfg.icon_id)
			self.node_list.img_zbitem.image:LoadSprite(zb_item_bundle, zb_item_asset, function ()
				self.node_list.img_zbitem.image:SetNativeSize()
			end)
			self.node_list.one_more_cosume.text.text = (self.other_info.times - has_num) * self.other_info.spend
			self.node_list.img_zbitem:CustomSetActive(true)

			local cost_id
			local cost_type = self.other_info.cost_type or COST_TYPE.LINGYU
			if cost_type == COST_TYPE.LINGYU then
				cost_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
			elseif cost_type == COST_TYPE.YANYUGE_SCORE then
				cost_id = COMMON_CONSTS.VIRTUAL_ITEM_CANG_JIN_SCORE
			end

			local cost_item_bundle, cost_item_asset = ResPath.GetItem(cost_id)
			self.node_list.xianyu_icon.image:LoadSprite(cost_item_bundle, cost_item_asset)
		end

		self.node_list.total_price_desc1.text.text = self.other_info.total_price_desc1 or Language.Tip.TotalPriceDesc1
		self.node_list.total_price_desc2.text.text = self.other_info.total_price_desc2 or Language.Tip.TotalPriceDesc2
	end

	local show_count = #self.id_list
	self:ChangeState(show_count <= 10)
	self:AdjustPosition()
	self:ResetItemData()
	-- self:ShowBestReward()
end

function TipsGetValueRewardView:AdjustPosition()
	local rect = self.node_list["ph_zhanshii_cell"].rect -- 50抽
    local rect_ten = self.node_list["ph_zhanshii_ten_cell"].rect   --一抽 10抽
    local baodi_rect = self.node_list.best_container_root.rect
	local show_count = #self.id_list

	local best_data = (self.other_info or {}).best_data
	local best_list = {}

	if not IsEmptyTable(best_data) then
		if best_data.item_id then
			table.insert(best_list, {item_id = best_data.item_id, best_text = best_data.best_text or Language.Tip.BestDataItemTitle})
		end
	
		if best_data.item_ids then
			for _, temp_item_id in ipairs(best_data.item_ids) do
				table.insert(best_list, {item_id = temp_item_id, best_text = best_data.best_text or Language.Tip.BestDataItemTitle})
			end
		end
	end
	
	local has_best_data = not IsEmptyTable(best_list)

	if has_best_data then
		self.best_list = best_list
	end
	
	self.node_list["best_container_root"]:CustomSetActive(has_best_data)
	self.best_item_list:SetDataList(best_list)
	self.node_list.best_des:CustomSetActive(has_best_data)
	self:ResetTotalValueRootScale()

	-- 有无奖励
	if show_count > 0 then
		-- 有大奖
		if has_best_data then
			baodi_rect.anchoredPosition = Vector2(0, 226)
			self.node_list.best_des.text.text = best_data and best_data.best_des or Language.Tip.GetBestDataItemDesc

			if show_count <= 10 then
				rect_ten.anchoredPosition = Vector2(0, -18)
			else
	            rect.anchoredPosition = Vector2(0, -9)
            	-- rect.sizeDelta = Vector2(880, 244)
				self:DoDataListRectTween(rect, 108, 880, 244)
			end
		else
			-- 无大奖
			if show_count <= 1 then
				rect_ten.anchoredPosition = Vector2(0, 15)
			elseif show_count <= 10 then
	            rect_ten.anchoredPosition = Vector2(0, 74)
			else
	            rect.anchoredPosition = Vector2(0, 67)
            	-- rect.sizeDelta = Vector2(880, 396)
				self:DoDataListRectTween(rect, 108, 880, 396)
			end
		end
	else
		-- 单个大奖
		if has_best_data then
			baodi_rect.anchoredPosition = Vector2(0, 80)
		end
	end
end

function TipsGetValueRewardView:DoDataListRectTween(rect, cell_hight, width, height)
    if self.is_skip_anim then
        rect.sizeDelta = Vector2(width, cell_hight)

        if self.rect_five_tween then
            self.rect_five_tween:Kill() 
        end

        self.rect_five_tween = rect:DOSizeDelta(Vector2(width, height), 2):SetDelay(1.2):SetEase(DG.Tweening.Ease.Linear)
    else
        if self.rect_five_tween then
            self.rect_five_tween:Kill() 
        end

        rect.sizeDelta = Vector2(width, height)
    end
end

function TipsGetValueRewardView:ChangeState(is_ten)
	self.node_list["ph_zhanshii_cell"]:SetActive(not is_ten)
	self.node_list["ph_zhanshii_ten_cell"]:SetActive(is_ten)
end

function TipsGetValueRewardView:SortItem(item_list)
	if self.no_need_sort then
		return item_list
	end

	local item_cfg,item_type
	for i,v in ipairs(item_list) do
		item_cfg, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		v.color = item_cfg and item_cfg.color or 0
		v.item_type = Sort_Type[item_type] or 1
		v.is_bind = item_cfg and item_cfg.isbind or 0
	end
	
	SortTools.SortDesc(item_list, "color", "item_type")
	return item_list
end

function TipsGetValueRewardView:CancelTimeQuest()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function TipsGetValueRewardView:CancelMoveScrollTimeQuest()
	if self.move_scroll_quest then
		GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
		self.move_scroll_quest = nil
	end
end

function TipsGetValueRewardView:ResetItemData()
	if not self.id_list then
		return
	end

	local list_info = self.id_list
	list_info = self:SortItem(list_info)

    reword_count = #list_info
	local grid_target = #self.id_list <= 10 and self.zhanshi_ten_grid or self.zhanshi_grid
	if grid_target ~= nil then
		grid_target:SetDataList(list_info)
		self:CancelTimeQuest()
		ani_flag_t = {}
		ani_count = 1
		-- ANI_SPEED = 0.1 * ((col_num * row_num) / #self.id_list)

		if self.is_skip_anim then
			self.time_quest = GlobalTimerQuest:AddTimesTimer(function()
				self:DoCellAnimTen(true)
				end, ANI_SPEED, reword_count )
		else
			for i = 1, reword_count do
				self:DoCellAnimTen(false)
			end
		end

		if reword_count <= 0 or not self.is_skip_anim then
			self:DoTotalValueRootTween(false)
		end

		if #self.id_list > 10 then
			self:CancelMoveScrollTimeQuest()
			grid_target:JumptToPrecent(1)
			lerp = 1 / (#self.id_list - (col_num * row_num)) * 0.35		--每次减少多少
			if reword_count > 30 and self.is_skip_anim then
				self.move_scroll_quest = GlobalTimerQuest:AddTimesTimer(function()
					self:MoveScroll()
					if scroll_verticalNormalizedPosition <= 0 then
						scroll_verticalNormalizedPosition = 0
						self:CancelMoveScrollTimeQuest()
					end
				end, 0.03, 999999)
			end
		end
	end
end

function TipsGetValueRewardView:DoCellAnimTen(do_tween)
	if not self.id_list then
		return
	end
	local grid_target = #self.id_list <= 10 and self.zhanshi_ten_grid or self.zhanshi_grid
    local cell = grid_target:GetCell(ani_count)
    ani_flag_t[ani_count] = true
	ani_count = ani_count + 1

	if cell ~= nil and cell:GetData() ~= nil then
		if do_tween then
			cell.view.transform.localScale = Vector3(2.5, 2.5, 2.5)
			cell.view.transform:DOScale(Vector3(1, 1, 1), 0.2)
		else
			cell.view.transform.localScale = Vector3(1, 1, 1)
		end
		--特效
		-- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
		-- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0), nil, nil)
		cell:SetActive(true)
    end

	if do_tween and ani_count == reword_count + 1 then
		self:DoTotalValueRootTween(true)
	end
end

function TipsGetValueRewardView:MoveScroll()
	if ani_count > (col_num * row_num) then --and self.move_tween_complete then --
		scroll_verticalNormalizedPosition = scroll_verticalNormalizedPosition - lerp
	else
		scroll_verticalNormalizedPosition = 1
	end

	if self.node_list.ph_zhanshii_cell then
		self.node_list.ph_zhanshii_cell.scroll_rect.verticalNormalizedPosition = scroll_verticalNormalizedPosition
	end
end


-- function TipsGetValueRewardView:ShowBestReward()
-- 	self.node_list["best_container_root"]:SetActive(false)
-- 	if not self.other_info then
-- 		return
-- 	end

-- 	if not self.other_info.best_data then
-- 		return
-- 	end

-- 	self.node_list["best_container_root"]:SetActive(true)
-- 	local best_data = self.other_info.best_data

-- 	local best_list = {}
-- 	if best_data.item_id then
-- 		table.insert(best_list, {item_id = best_data.item_id})
-- 	end

-- 	if best_data.item_ids then
-- 		for _, temp_item_id in ipairs(best_data.item_ids) do
-- 			table.insert(best_list, {item_id = temp_item_id})
-- 		end
-- 	end

-- 	self.best_item_list:SetDataList(best_list)
-- 	self.node_list.best_des:CustomSetActive(#best_list <= 1)

-- 	if best_data.best_des then
-- 		self.node_list.best_des.text.text = best_data.best_des
-- 	end

-- 	-- if best_data.best_text then
-- 	-- 	self.node_list.best_text.text.text = best_data.best_text
-- 	-- end
-- end


function TipsGetValueRewardView:OnSure()
	if self.sure_func then
		self.sure_func()
	end
	
	self:Close()
end

function TipsGetValueRewardView:OnQuchuAgain()
	if self.again_func then
		self.again_func()
	end

	self:Close()
end

function TipsGetValueRewardView:ResetTotalValueRootScale()
    self.node_list.total_price_root.rect.localScale = Vector3(0, 0, 0)
end

function TipsGetValueRewardView:DoTotalValueRootTween(is_tween)
    local function set_total_value(is_tween)
        local total_value = 0
        if not IsEmptyTable(self.best_list) then
            for k, v in pairs(self.best_list) do
                total_value = total_value + ItemWGData.Instance:GetItemValueByItemId(v.item_id)
            end
        end

        if not IsEmptyTable(self.id_list) then
            for k, v in pairs(self.id_list) do
                total_value = total_value + ItemWGData.Instance:GetItemValueByItemId(v.item_id)
            end
        end

        if is_tween then
            UITween.DONumberTo(self.node_list.desc_total_price.text, 0, tonumber(total_value), 0.5, 
            function (num)
                self.node_list.desc_total_price.text.text = math.floor(num)
            end, function ()
                self.node_list.desc_total_price.text.text = total_value
            end)
        else
            self.node_list.desc_total_price.text.text = total_value
        end
    end

    if is_tween then
        self.node_list.total_price_root.rect.localScale = Vector3(2.5, 2.5, 2.5)
        self.node_list.desc_total_price.text.text = ""
        self.node_list.total_price_root.rect:DOScale(Vector3(1, 1, 1), 0.15):OnComplete(function()
            set_total_value(true)
        end)
    else
        self.node_list.total_price_root.rect.localScale = Vector3(1, 1, 1)
        set_total_value(false)
    end
end

-------------------------------------------------CommonValueRewardGrid---------------------------------------------------
CommonValueRewardGrid = CommonValueRewardGrid or BaseClass(AsyncBaseGrid)

-- 获得指定的格子
function CommonValueRewardGrid:GetCell(index)
    for k, v in pairs(self.cell_list) do
        local row = math.floor((index - 1) / self.columns)
        if row == v:GetRows() and v:GetActive()  then
            for k1, v1 in pairs(v:GetAllCell()) do
                if v1:GetIndex() == index then
                    return v1
                end
            end
        end
    end
	return nil
end

-------------------------------------------------CommonValueRewardCell---------------------------------------------------
CommonValueRewardCell = CommonValueRewardCell or BaseClass(BaseRender)

function CommonValueRewardCell:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end
end

function CommonValueRewardCell:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function CommonValueRewardCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self:SetVisible(ani_flag_t[self.index] == true)
    self.item:SetData(self.data)
    self.node_list.desc_value_str.text.text = ItemWGData.Instance:GetItemValueByItemId(self.data.item_id)
end

-- function CommonValueRewardCell:OnFlush()
--     self:SetActive(ani_flag_t[self.index] == true)
--     self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)

-- 	for k,v in pairs(BaseCell_Ui_Circle_Effect) do
-- 		self:SetEffectEnable(false, v)
-- 	end
-- 	ItemCell.OnFlush(self)
-- end

-- function CommonValueRewardCell:SetActive(value)
-- 	ItemCell.SetVisible(self, value and (self.index == nil or ani_flag_t[self.index]))
-- end

-------------------------------------------------TenCommonValueRewardCell---------------------------------------------------
TenCommonValueRewardCell = TenCommonValueRewardCell or BaseClass(BaseRender)

function TenCommonValueRewardCell:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end
end

function TenCommonValueRewardCell:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function TenCommonValueRewardCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self:SetVisible(ani_flag_t[self.index] == true)
    self.item:SetData(self.data)
    self.node_list.desc_value_str.text.text = ItemWGData.Instance:GetItemValueByItemId(self.data.item_id)
end

-- function TenCommonValueRewardCell:OnFlush()
-- 	self:SetActive(ani_flag_t[self.index] == true)
--     self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)

-- 	for k,v in pairs(BaseCell_Ui_Circle_Effect) do
-- 		self:SetEffectEnable(false, v)
-- 	end
-- 	ItemCell.OnFlush(self)
-- end

-- function TenCommonValueRewardCell:SetActive(value)
-- 	ItemCell.SetActive(self, value and (self.index == nil or ani_flag_t[self.index]))
-- end

----------------------------------------------BestValueItemCellRender----------------------------------------------
BestValueItemCellRender = BestValueItemCellRender or BaseClass(BaseRender)

function BestValueItemCellRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end
end

function BestValueItemCellRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function BestValueItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item:SetData(self.data)
    self.node_list.desc_value.text.text = ItemWGData.Instance:GetItemValueByItemId(self.data.item_id)
	self.node_list.title.text.text = self.data.best_text
end