LifeTimeOfLoveView = LifeTimeOfLoveView or BaseClass(SafeBaseView)

function LifeTimeOfLoveView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_panel")
	self:AddViewResource(0, "uis/view/lifetime_of_love_ui_prefab", "layout_lifetime_of_love")
end

function LifeTimeOfLoveView:LoadCallBack()
    self.cur_show_type = 0
    self.node_list.view_name_txt.text.text = Language.LifeTimeOfLove.ViewName

    local bundle, asset = ResPath.GetF2RawImagesPNG("a2_dhxy_bg")
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset)

    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["model_display"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    if not self.toggle_list then
        self.toggle_list = AsyncListView.New(LifeTimeOfLoveToggle, self.node_list.toggle_list)
        self.toggle_list:SetSelectCallBack(BindTool.Bind(self.OnSelectToggle, self))
        self.toggle_list:SetStartZeroIndex(true)
    end

    if not self.task_list then
        self.task_list = AsyncListView.New(LifeTimeOfLoveTaskRendr, self.node_list.task_list)
    end

    XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBigRewardBtn, self))

    local act_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_A_LIFELONG_LOVE_TASK)
    if act_time > 0 then
        self:CleanTimer()
        self.act_timer = CountDown.Instance:AddCountDown(act_time, 1, function(elapse_time, total_time)
            local time = math.floor(total_time - elapse_time)
            self:UpdateTimer(time)
        end,
        function()
            self:UpdateTimer(0)
        end)
    end
end

function LifeTimeOfLoveView:ReleaseCallBack()
    self:CleanTimer()

    if self.toggle_list then
        self.toggle_list:DeleteMe()
        self.toggle_list = nil
    end

    if self.task_list then
        self.task_list:DeleteMe()
        self.task_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    self.cur_show_type = nil
end

function LifeTimeOfLoveView:OpenCallBack()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_A_LIFELONG_LOVE_TASK, 1)
end

function LifeTimeOfLoveView:OnFlush(parma)
    for k, v in pairs(parma) do
        if k == "update_task_info" then
            self:FlushAllListInfoPart()
            self:FlushRemindPart()
        else
            self:FlushAllListInfoPart()
            self:FlushLoadResPart()
            self:FlushRemindPart()
        end
    end

    local need_change, change_type = self:GetIsChangeShowType()
    if need_change then
        self.toggle_list:JumpToIndex(change_type)
    end
end

function LifeTimeOfLoveView:FlushAllListInfoPart()
    local toggle_data_list = LifeTimeOfLoveWGData.Instance:GetTaskShowCfg()
    self.toggle_list:SetDataList(toggle_data_list)

    self:FlushTaskListInfo()
end

function LifeTimeOfLoveView:FlushTaskListInfo()
    local task_data_list = LifeTimeOfLoveWGData.Instance:GetTaskListByType(self.cur_show_type)
    self.task_list:SetDataList(task_data_list)
end

function LifeTimeOfLoveView:FlushLoadResPart()
    local task_show_cfg = LifeTimeOfLoveWGData.Instance:GetTaskShowCfg()
    if task_show_cfg then
        local info = task_show_cfg[self.cur_show_type]
        local item_cfg = ItemWGData.Instance:GetItemConfig(info.show_id)
        if item_cfg and info then
            --刷新信息
            local bundle, asset = ResPath.GetF2RawImagesPNG("a2_dhxy_zg_ysz" .. info.title_index)
            self.node_list.task_title.raw_image:LoadSprite(bundle, asset, function()
                self.node_list.task_title.raw_image:SetNativeSize()
            end)
            
            self.node_list.title_txt.text.text = info.des_title
            self.node_list.des_txt.text.text = info.des
            self.node_list.name_txt.text.text = item_cfg.name

            -- 刷新模型
            local data = {}
            data.render_type = OARenderType.RoleModel
            data.item_id = item_cfg.id
            data.model_click_func = BindTool.Bind(self.OnDisplayClick, self)
            self.model_display:SetData(data)
        end
    end
end

function LifeTimeOfLoveView:FlushRemindPart()
    local can_get = LifeTimeOfLoveWGData.Instance:GetTaskBigRewardCanGetByType(self.cur_show_type)
    self.node_list.get_btn:SetActive(can_get)

    local is_get = LifeTimeOfLoveWGData.Instance:GetBigRewardFlag(self.cur_show_type)
    self.node_list.get_flag:SetActive(is_get)
end

function LifeTimeOfLoveView:OnDisplayClick()
    local task_show_cfg = LifeTimeOfLoveWGData.Instance:GetTaskShowCfg()
    if not task_show_cfg or not task_show_cfg[self.cur_show_type] then
        return
    end

    local item_id = task_show_cfg[self.cur_show_type] and task_show_cfg[self.cur_show_type].show_id or 0
    TipWGCtrl.Instance:OpenItem({item_id = item_id})
end

function LifeTimeOfLoveView:OnSelectToggle(cell)
    if not cell then
        return
    end

    local index = cell:GetIndex()
    if self.cur_show_type == index then
        return
    end

    self.cur_show_type = index
    self:FlushTaskListInfo()
    self:FlushLoadResPart()
    self:FlushRemindPart()
end

function LifeTimeOfLoveView:OnClickGetBigRewardBtn()
    local can_get = LifeTimeOfLoveWGData.Instance:GetTaskBigRewardCanGetByType(self.cur_show_type)
    if can_get then
        LifeTimeOfLoveWGCtrl.Instance:SendGetRewardReq(A_LIFELONG_LOVE_OPERATE_TYPE.BIG_REWARD, self.cur_show_type)
    end
end

function LifeTimeOfLoveView:CleanTimer()
    if self.act_timer and CountDown.Instance:HasCountDown(self.act_timer) then
        CountDown.Instance:RemoveCountDown(self.act_timer)
        self.act_timer = nil
    end
end

function LifeTimeOfLoveView:UpdateTimer(time)
    if self.node_list and self.node_list.limited_time then
        local time_str = TimeUtil.FormatSecondDHM8(time)
        self.node_list.limited_time.text.text = string.format(Language.LifeTimeOfLove.LimitedTime, time_str)
    end
end

function LifeTimeOfLoveView:GetIsChangeShowType()
    local toggle_data_list = LifeTimeOfLoveWGData.Instance:GetTaskShowCfg()
    for k, v in pairs(toggle_data_list) do
        if LifeTimeOfLoveWGData.Instance:GetTypeRemind(v.task_type) then
            if self.cur_show_type == k then
                return false, 0
            elseif self.cur_show_type ~= k then
                return true, k
            end
        end
    end

    return false, 0
end

------------ 一生所爱切页toggle ------------
LifeTimeOfLoveToggle = LifeTimeOfLoveToggle or BaseClass(BaseRender)

function LifeTimeOfLoveToggle:OnFlush()
    if not self.data then
        return
    end

    self.node_list.nor_txt.text.text = self.data.name
    self.node_list.hl_txt.text.text = self.data.name

    local task_reward_remind = LifeTimeOfLoveWGData.Instance:GetTypeRemind(self.data.task_type)
    self.node_list.remind:SetActive(task_reward_remind)
end

function LifeTimeOfLoveToggle:OnSelectChange(is_select)
    self.node_list.nor_img:SetActive(not is_select)
    self.node_list.hl_img:SetActive(is_select)
end

------------ 一生所爱任务render ------------
LifeTimeOfLoveTaskRendr = LifeTimeOfLoveTaskRendr or BaseClass(BaseRender)

function LifeTimeOfLoveTaskRendr:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)
    self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    self.reward_list:SetStartZeroIndex(true)
    XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.OnClickGetOrGotoBtn, self))
end

function LifeTimeOfLoveTaskRendr:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function LifeTimeOfLoveTaskRendr:OnFlush()
    if not self.data then
        return
    end

    local cur_count = self.data.get_limit - LifeTimeOfLoveWGData.Instance:GetTaskRewardCountBySeq(self.data.seq)
    local color = cur_count == 0 and COLOR3B.PINK or COLOR3B.DEFAULT_NUM
    self.node_list.limit_txt.text.text = string.format(Language.LifeTimeOfLove.LimitCount, color, cur_count, self.data.get_limit)
    self.node_list.get_flag:SetActive(cur_count == 0)
    self.node_list.btn:SetActive(cur_count ~= 0)

    local can_get, get_num = LifeTimeOfLoveWGData.Instance:GetTaskCanGetBySeq(self.data.seq)
    self.node_list.btn_text.text.text = can_get and Language.LifeTimeOfLove.Get or Language.LifeTimeOfLove.GoTo
    self.node_list.remind:SetActive(can_get)
    self.node_list.remind_num.text.text = get_num

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.show_id)
    if item_cfg then
        local fashion_is_active = NewAppearanceWGData.Instance:GetFashionIsActByItemId(item_cfg.id) or NewAppearanceWGData.Instance:GetQiChongIsActByItemId(item_cfg.id)
        local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        local task_str = fashion_is_active and Language.LifeTimeOfLove.UpStarTaskDes or Language.LifeTimeOfLove.ActiveTaskDes
        self.node_list.task_des.text.text = string.format(task_str, item_name)
    end

    local cell_data = {item_id = self.data.show_id}
    self.item_cell:SetData(cell_data)
    self.reward_list:SetDataList(self.data.reward_item)
end

function LifeTimeOfLoveTaskRendr:OnClickGetOrGotoBtn()
    if not self.data then
        return
    end

    local can_get = LifeTimeOfLoveWGData.Instance:GetTaskCanGetBySeq(self.data.seq)
    if can_get then
        LifeTimeOfLoveWGCtrl.Instance:SendGetRewardReq(A_LIFELONG_LOVE_OPERATE_TYPE.TASK_REWARD, self.data.seq)
    else
        local act_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVIYY_TYPE_OA_ODYSSEYRMB)
        if not act_open then
            TipWGCtrl.Instance:ShowSystemMsg(Language.LifeTimeOfLove.NoOpenBuyActTips)
            return
        end

        ViewManager.Instance:Open(GuideModuleName.OdysseyPurchaseView)
    end
end