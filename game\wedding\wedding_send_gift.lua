WeddingSendGiftView = WeddingSendGiftView or BaseClass(SafeBaseView)

function WeddingSendGiftView:__init()
	self.active_close = false
	
	self.view_layer = UiLayer.FloatText
	self:LoadConfig()
end

function WeddingSendGiftView:__delete()
	
end

function WeddingSendGiftView:ReleaseCallBack()
	if self.gift_render then
		self.gift_render:SetInstanceParent(self.root_node_transform)
		self.gift_render:DeleteMe()
		self.gift_render = nil
	end
end

function WeddingSendGiftView:LoadConfig()
	-- self.texture_path_list[1] = 'res/xui/marry.png'
    self.view_name = "WeddingSendGiftView"
    self:AddViewResource(0, "uis/view/wedding_ui_prefab", "layout_send_gift")
end

function WeddingSendGiftView:LoadCallBack()
	local callback = function ()
		local parent = MainuiWGCtrl.Instance:GetSkillContent()
		self.node_list.send_gift_view:SetActive(true)
		if parent then
			self.gift_render = BaseRender.New(self.node_list.send_gift_view)
			self.gift_render:SetInstanceParent(parent)
		else
			print_error("can not find the parent!!")
		end
		self:Flush()
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,callback)
	XUI.AddClickEventListener(self.node_list["btn_wish"], BindTool.Bind2(self.OnBtnWishHandler, self)) 		--祝福
	self.node_list["btn_gath_jiuzhuo"].button:AddClickListener(BindTool.Bind(self.OnClickGatnJiuZhuo, self))
    self.node_list["btn_gath_tangguo"].button:AddClickListener(BindTool.Bind(self.OnClickGatnTangGuo, self))
    XUI.AddClickEventListener(self.node_list["btn_guests_msg"], BindTool.Bind2(self.OnBtnGuestsHandler, self)) --管理宾客
    self.node_list["img_flag"]:SetActive(false)
end

function WeddingSendGiftView:ShowIndexCallBack()
	self:Flush()
end

function WeddingSendGiftView:CloseCallBack()
	if self.node_list and self.node_list.send_gift_view then
		self.node_list.send_gift_view:SetActive(false)
	end
end

function WeddingSendGiftView:OnFlush()
    local marry_cfg = MarryWGData.Instance:GetCurWeddingInfo()
	local my_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local weeding_info = MarryWGData.Instance:GetWeddingRoleInfo()
	local seq = MarryWGData.Instance:GetInviteGuestsByWeddingSequence().wedding_yuyue_seq
    local applincant_data_num = MarryWGData.Instance:GetWeddingApplicantRed(seq)
	if not IsEmptyTable(marry_cfg) then
		if my_id == marry_cfg.role_id or my_id == marry_cfg.lover_role_id then
			self.node_list["btn_guests_msg"]:SetActive(true)
			self.node_list["img_flag"]:SetActive(applincant_data_num > 0)
		else
			-- XUI.SetButtonEnabled(self.node_list["btn_wedding"], false)
			self.node_list["btn_guests_msg"]:SetActive(false)
        end
    end
end

function WeddingSendGiftView:OnBtnGuestsHandler()
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_GET_YUYUE_INFO)    -- 获取预约列表信息
	MarryWGCtrl.Instance:OpenInviteView()
end

function WeddingSendGiftView:OnBtnWishHandler()
	WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_GET_BLESS_RECORD_INFO)
	WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_GET_WEDDING_INFO)
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.HUNYAN_GET_APPLICANT_INFO)
	WeddingWGCtrl.Instance:WeddingSendCoupleViewOpen()
end

function WeddingSendGiftView:OnClickGatnJiuZhuo()
	local weeding_info = MarryWGData.Instance:GetWeddingRoleInfo() or {}
    local red_id, red_max, jiuxi_id, jiuxi_max, today_jiuxi_num = MarryWGData.Instance:GetWeedingSceneCfg()

    local right_num = today_jiuxi_num - weeding_info.today_food_count + weeding_info.has_gather_num
    local this_can_get_max_num = right_num >= jiuxi_max and jiuxi_max or right_num

    local can_gather_jiuxi = weeding_info.has_gather_num < this_can_get_max_num --是否可以继续采集酒席
    --今日是否达到上限
	if weeding_info.today_food_count ~= nil and weeding_info.today_food_count > 0 and weeding_info.today_food_count >= today_jiuxi_num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.JiuZhuoGatnHint2)
		return
    end
    --当场是否达到上限
	if not can_gather_jiuxi then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.JiuZhuoGatnHint)
		return
	end

	local cur_select_target = SceneObj.select_obj
	if nil ~= cur_select_target and cur_select_target.vo.gather_id ~= jiuxi_id then
		cur_select_target = nil
	end

	cur_select_target = self:FindJiuXiObj(jiuxi_id)
	if cur_select_target == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.JiuZhuoGatnHint1)
		return
	end

	local target_obj = {}
	target_obj.x = 0
	target_obj.y = 0
	target_obj.scene = SceneType.HunYanFb
	if nil ~= cur_select_target then
		target_obj.obj = cur_select_target
		target_obj.obj_id = cur_select_target:GetObjId()
		target_obj.x, target_obj.y= cur_select_target:GetLogicPos()
		target_obj.id = cur_select_target.id
	end

	local scene_id = Scene.Instance:GetSceneId()
	MarryWGData.Instance:RemberGatnId(jiuxi_id)
	MoveCache.SetEndType(MoveEndType.Gather)
	MoveCache.param1 = jiuxi_id
	MoveCache.target_obj = cur_select_target
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	GuajiWGCtrl.Instance:MoveToPos(scene_id, target_obj.x, target_obj.y,3)
end


function WeddingSendGiftView:FindJiuXiObj(gather_id)
	local target_obj = nil
	local target_distance = 50
    target_distance = target_distance * target_distance
    local main_role = Scene.Instance:GetMainRole()
    local main_role_x, main_role_y = main_role:GetLogicPos()
	local target_x, target_y, distance = 0, 0, 0
	local obj_list = Scene.Instance:GetGatherList()
	if obj_list and not IsEmptyTable(obj_list) then
		for k, v in pairs(obj_list) do
			if v:GetGatherId() == gather_id and not v:IsDeleted() and not MarryWGData.Instance:GetIsHasGather(v:GetObjId()) then
	            target_x, target_y = v:GetLogicPos()
	            distance = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
	            if distance < target_distance then
	                if v:IsInBlock() then
	                    target_obj = target_obj or v
	                else
	                    target_obj = v
	                    target_distance = distance
	                end
	            end
	        end
		end
	end
	return target_obj
end

function WeddingSendGiftView:OnClickGatnTangGuo()
    local weeding_info = MarryWGData.Instance:GetWeddingRoleInfo() or {}
    if not IsEmptyTable(weeding_info) then
        local red_id, red_max, jiuxi_id, jiuxi_max, today_jiuxi_num = MarryWGData.Instance:GetWeedingSceneCfg()
        local can_gather_red_bag = weeding_info.has_gather_red_bag < red_max 		--是否可以继续采集喜酒/糖果
        if not can_gather_red_bag  then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.TangGuoGatnHint)
            return
        end
    
        local num = 0
        local cur_select_target = SceneObj.select_obj
        if nil ~= cur_select_target and cur_select_target.vo.gather_id ~= red_id then
            cur_select_target = nil
        end

        cur_select_target = Scene.Instance:SelectMinDisGather(red_id)	--选择最近的

        local target_obj = {}
        target_obj.x = 0
        target_obj.y = 0
        target_obj.scene = SceneType.HunYanFb
        if nil ~= cur_select_target then
            target_obj.obj = cur_select_target
            target_obj.obj_id = cur_select_target:GetObjId()
            target_obj.x, target_obj.y= cur_select_target:GetLogicPos()
            target_obj.id = cur_select_target.id
        end

        if cur_select_target == nil  then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.TangGuoGatnHint1)
            return
        end

        local scene_id = Scene.Instance:GetSceneId()
        MarryWGData.Instance:RemberGatnId(red_id)
        MoveCache.SetEndType(MoveEndType.GatherById)
        MoveCache.param1 = red_id
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        GuajiWGCtrl.Instance:MoveToPos(scene_id, target_obj.x, target_obj.y,3)
    end
end