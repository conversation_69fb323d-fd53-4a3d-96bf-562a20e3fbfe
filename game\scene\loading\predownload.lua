-- 预下载
PreDownload = PreDownload or BaseClass()

function PreDownload:__init()
	if PreDownload.Instance then
		print_error("[PreDownload] Attempt to create singleton twice!")
		return
	end
	PreDownload.Instance = self
	self.download_list = nil
	self.download_list_count = 0
	self.bundle_index = 0
	self.is_strong_update = false
	self.is_open_perdownload = true --是否开启预下载
	self.net_work_state = NETWORK_STATE.WIFI

	self.net_work_state_change = GlobalEventSystem:Bind(
		NetWorkStateEvent.NET_WORK_STATE_CHANGE,
		BindTool.Bind1(self.NetWorkStateChange, self))

	-- 在windows调下下关掉预下载
	if IS_LOCLA_WINDOWS_DEBUG_EXE then
		self.is_open_perdownload = false
	end
end

function PreDownload:__delete()
	PreDownload.Instance = nil
	GlobalEventSystem:UnBind(self.net_work_state_change)
end

function PreDownload:NetWorkStateChange(state, msg)
	self.net_work_state = state
end

function PreDownload:IsEmpty()
	if not self.is_open_perdownload then
		return true
	end

	return nil == self.download_list or self.bundle_index >= self.download_list_count
		or self.is_strong_update or self.net_work_state ~= NETWORK_STATE.WIFI
end

-- 获取偷偷下载的资源列表
-- 因为底层做了限制，只有在完全空闲的时候才会触发偷偷下载，所以在下载分包的时候，是不会进行偷偷下载的
function PreDownload:GetNextBundles(count)
	if nil == self.download_list then
		self:CreateDownloadList()
	end

	local bundles = nil

	local index = 0
	for i = self.bundle_index + 1, #self.download_list do
		index = index + 1
		if index > 1000 then
			break
		end

		self.bundle_index = i
		local bundle = self.download_list[i]
		if nil == bundle then
			break
		end

		local bundle_name = bundle.bundle
		local bundle_hash = ResMgr:GetBundleHash(bundle_name)
		if nil ~= bundle_hash then
			if not ResUtil.IsFileExist(bundle_name, bundle_hash) then
				bundles = bundles or {}
				table.insert(bundles, bundle_name)
				if #bundles >= count then
					break
				end
			end
		end
	end

	self:CheckFinish()

	return bundles
end

function PreDownload:CreateDownloadList()
	self.download_list = {}
	self.bundle_index = 0

	local predownload_config = require("config/config_predownload")
	local list = {}
	for k,v in ipairs(predownload_config) do
		table.insert(list, v)
	end

	-- 处理成相关联的文件
	local dic = {}
	for _, v in ipairs(list) do
		local uncached_bundles = ResMgr:GetBundlesWithoutCached(v.bundle)
		if uncached_bundles ~= nil then
			for v2 in pairs(uncached_bundles) do
				if not dic[v2] then
					dic[v2] = true
					table.insert(self.download_list, {bundle = v2, level = v.level})
				end
			end
		end
	end

	self.download_list_count = #self.download_list

	-- 当下载列表不为空时创建缓冲器
	if self.download_list_count > 0 then
		AssetBundleMgr:CreateBuffers()
	end
end

function PreDownload:CheckFinish()
	if self.bundle_index >= self.download_list_count and nil ~= self.download_list and #self.download_list > 0 then
		self.download_list = {}
		-- 当所有偷偷下载任务完成后，销毁缓冲器，释放内存
		AssetBundleMgr:DestoryBuffers()
	end
end

function PreDownload:StartStrongUpdate(main_role_level, scene_id, call_back)
	self.is_strong_update = true
	local bundles, total_file_size = self:GetStrongUpdateBundles(main_role_level, scene_id)
	local total_downloaded_size = 0
	local retry_count = 0

	local update_callback = function (downloaded_size, download_speed)
		local str = string.format(Language.Common.Updating, (total_downloaded_size + downloaded_size) / 1024 / 1024,
			total_file_size / 1024 / 1024, download_speed / 1024 / 1024)

		if total_file_size == 0 then
			call_back(false, 0, str)
		else
			call_back(false, (total_downloaded_size + downloaded_size) / total_file_size, str)
		end
		
	end

	local complete_callback = nil
	complete_callback = function (error_bundles, downloaded_size)
		total_downloaded_size = total_downloaded_size + downloaded_size
		-- 最多重试3次
		if #error_bundles <= 0 or retry_count > 3 then
			-- 下载成功, 还原网络下载地址
			if retry_count > 0 then
				ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url)
			end

			self.is_strong_update = false
			call_back(true)
			return
		end

		retry_count = retry_count + 1
		-- 切换下载地址
		if GLOBAL_CONFIG.param_list.cdn_url2 ~= nil and "" ~= GLOBAL_CONFIG.param_list.cdn_url2 then
			if retry_count % 2 == 1 then
				ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url2)
			else
				ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url)
			end
		end
		self:StartDownLoad(error_bundles, 5, update_callback, complete_callback)
	end

	self:StartDownLoad(bundles, 5, update_callback, complete_callback)
end

function PreDownload:StartDownLoad(bundles, group_count, _update_call_back, _complete_callback)
	local bundle_group_list = {}
	for i = 1, group_count do
		bundle_group_list[i] = {downloaded_size = 0, bundles = {}}
	end

	local complete_group_count = 0
	local cur_group_index = 1
	local error_bundles = {}

	if #bundles <= 0 then
		_complete_callback(error_bundles, 0)
		return
	end

	for i = 1, #bundles do
		local bundle = bundles[i]
		table.insert(bundle_group_list[cur_group_index].bundles, bundle)
		cur_group_index = cur_group_index + 1
		if cur_group_index > group_count then
			cur_group_index = 1
		end
	end

	local last_calulate_speed_time = Status.NowTime
	local last_total_downloaded_size = 0
	local download_speed = 0

	local update_callback = function (group_index, downloaded_size)
		bundle_group_list[group_index].downloaded_size = downloaded_size

		local total_downloaded_size = 0
		for i = 1, group_count do
			total_downloaded_size = total_downloaded_size + bundle_group_list[i].downloaded_size
		end

		local elapse_time = Status.NowTime - last_calulate_speed_time
		if elapse_time >= 1 then
			download_speed = (total_downloaded_size - last_total_downloaded_size) / elapse_time
			last_total_downloaded_size = total_downloaded_size
			last_calulate_speed_time = Status.NowTime
		end

		_update_call_back(total_downloaded_size, download_speed)
	end

	local complete_callback = function (group_index, downloaded_size)
		bundle_group_list[group_index].downloaded_size = downloaded_size
		complete_group_count = complete_group_count + 1

		if complete_group_count >= group_count then
			local total_downloaded_size = 0
			for i = 1, group_count do
				total_downloaded_size = total_downloaded_size + bundle_group_list[i].downloaded_size
			end
			_complete_callback(error_bundles, total_downloaded_size)
		end
	end

	for i = 1, group_count do
		self:DoDownLoad(i, bundle_group_list[i].bundles, 1, 0, error_bundles, update_callback, complete_callback)
	end
end

function PreDownload:DoDownLoad(group_index, update_bundles, index, total_downloaded_size, error_bundles, update_callback, complete_callback)
	if index > #update_bundles then
		complete_callback(group_index, total_downloaded_size)
		return
	end

	local bundle = update_bundles[index]
	local file_size = ResMgr:GetBundleSize(bundle) or 0

	ResMgr:UpdateBundle(bundle,
		function(progress, download_speed)
			local downloaded_size = total_downloaded_size + file_size * progress
			update_callback(group_index, downloaded_size)
		end,
		function(error_msg)
			if error_msg ~= nil and error_msg ~= "" then
				table.insert(error_bundles, bundle)
			else
				total_downloaded_size = total_downloaded_size + file_size
			end
			self:DoDownLoad(group_index, update_bundles, index + 1, total_downloaded_size, error_bundles, update_callback, complete_callback)
		end)
end

function PreDownload:GetStrongUpdateBundles(main_role_level, scene_id)
	local dic = {}
	local download_list = {}
	local total_size = 0

	if main_role_level > 0 then
		local predownload_config = require("config/config_predownload")
		local list = {}
		for k,v in ipairs(predownload_config) do
			if v.level > main_role_level then
				break
			end

			if string.find(v.bundle, "uis/view/") or string.find(v.bundle, "scenes/map/") then
				table.insert(list, v)
			end
		end

		for _, v in ipairs(list) do
			local uncached_bundles = ResMgr:GetBundlesWithoutCached(v.bundle)
			if uncached_bundles ~= nil then
				for v2 in pairs(uncached_bundles) do
					if not dic[v2] then
						dic[v2] = true
						local file_size = ResMgr:GetBundleSize(v2) or 0
						total_size = total_size + file_size
						table.insert(download_list, v2)
					end
				end
			end

			-- 一次最多下载300M
			if total_size > 300 * 1024 * 1024 then
				break
			end
		end

		UnRequire("config/config_predownload")
	end

	-- 下载当前场景
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	if nil ~= scene_cfg then
		local name_list = {scene_cfg.bundle_name}
		for _, v in ipairs(name_list) do
			local uncached_bundles = ResMgr:GetBundlesWithoutCached(v)
			if uncached_bundles ~= nil then
				for v2 in pairs(uncached_bundles) do
					if not dic[v2] then
						dic[v2] = true
						local file_size = ResMgr:GetBundleSize(v2) or 0
						total_size = total_size + file_size
						table.insert(download_list, v2)
					end
				end
			end
		end
	end

	-- 场景需要预加载的CG
	local cg_list = {}
	ScenePreload.GetLoadCgList(cg_list, scene_id)
	-- 跳跃点CG
	if nil ~= scene_cfg then
		local scene_jumppoints = scene_cfg.jumppoints or {}
		ScenePreload.GetLoadJumpCgList(cg_list, scene_jumppoints)
	end

	for k,v in pairs(cg_list) do
		local bundle_name = v.bundle_name
		local uncached_bundles = ResMgr:GetBundlesWithoutCached(bundle_name)
		if uncached_bundles ~= nil then
			for v2 in pairs(uncached_bundles) do
				if not dic[v2] then
					dic[v2] = true
					local file_size = ResMgr:GetBundleSize(v2) or 0
					total_size = total_size + file_size
					table.insert(download_list, v2)
				end
			end
		end
	end

	return download_list, total_size
end