EveryDayLianXuRechargeWGData = EveryDayLianXuRechargeWGData or BaseClass()

--档位类型
EVERYDAY_LIANXU_RECHARGE_DAY_TYPE = {
    DAY_TYPE_1 = 1, --第一档6元
    DAY_TYPE_2 = 2, --第一档30元
}

--奖励类型
EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE = {
    Normal = 0, --普通奖励
    Stage = 1, --阶段奖励
}

--奖励领取状态类型
EVERYDAY_LIANXU_RECHARGE_REWARD_STATE = {
    NotFinish = 0, --未达成
    CanGet = 1, --可领取
    HasGet = 2, --已领取
}

function EveryDayLianXuRechargeWGData:__init()
    if nil ~= EveryDayLianXuRechargeWGData.Instance then
        ErrorLog("[EveryDayLianXuRechargeWGData]:Attempt to create singleton twice!")
    end
    EveryDayLianXuRechargeWGData.Instance = self
    self:InitConfig()

    self.lianxu_recharge_info = {}
    self.show_slider_cfg = {}

    RemindManager.Instance:Register(RemindName.DailyRecharge_LianChong, BindTool.Bind(self.IsShowRebateLianXuRechargeRed, self))
end

function EveryDayLianXuRechargeWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.DailyRecharge_LianChong)
    self.show_slider_cfg = nil
    EveryDayLianXuRechargeWGData.Instance = nil
end

function EveryDayLianXuRechargeWGData:InitConfig()
    local lianxuchongzhicfg = ConfigManager.Instance:GetAutoConfig("lianxuchongzhicfg_auto")
    self.other_cfg = lianxuchongzhicfg.other[1]
    self.reward_cfg_group_day = ListToMapList(lianxuchongzhicfg.reward_cfg, "group", "day_type")
    
    self.suit_param = ListToMap(lianxuchongzhicfg.suit_param, "group_index", "day_type")
    self.suit_show = ListToMapList(lianxuchongzhicfg.suit_show, "seq")

    self.reward_cfg_by_recharge_day = ListToMap(lianxuchongzhicfg.reward_cfg, "day_type", "need_chongzhi_day")
end

function EveryDayLianXuRechargeWGData:GetShowModelParam(group_index, day_type)
    if self.suit_param and self.suit_param[group_index] then
        return self.suit_param[group_index][day_type]
    end
end

function EveryDayLianXuRechargeWGData:GetShowModelList(seq)
    if not IsEmptyTable(self.suit_show) then
        return self.suit_show[seq]
    end
end

--保存协议下发信息
--print_error(EveryDayLianXuRechargeWGData.Instance.lianxu_recharge_info)
function EveryDayLianXuRechargeWGData:SaveData(protocol)
    -- print_error("FFFF===== 保存协议下发信息", protocol.gold_day, protocol.lianxu_recharge_info)
    self.today_recharge_num = protocol.gold_day
    self.lianxu_recharge_info = protocol.lianxu_recharge_info
end

function EveryDayLianXuRechargeWGData:GetTodayRechargeNum()
    return self.today_recharge_num or 0
end

function EveryDayLianXuRechargeWGData:GetCurGroupByRechargeType(btn_type)
    local group_index = 1--当前显示配置组数
    if self.lianxu_recharge_info and self.lianxu_recharge_info[btn_type] then
        group_index = self.lianxu_recharge_info[btn_type].group_index
    end
    return group_index
end

function EveryDayLianXuRechargeWGData:GetTotalDayByRechargeType(btn_type)
    local total_recharge_day = 0--此类型累计充值天数
    if self.lianxu_recharge_info and self.lianxu_recharge_info[btn_type] then
        total_recharge_day = self.lianxu_recharge_info[btn_type].chongzhi_conut
    end
    return total_recharge_day
end

--获取该类型当天是否充值过
function EveryDayLianXuRechargeWGData:GetTodayHasRecharge(btn_type)
    local has_recharge = false
    if self.lianxu_recharge_info and self.lianxu_recharge_info[btn_type] then
        has_recharge = self.lianxu_recharge_info[btn_type].chongzhi_flag == 1
    end
    return has_recharge
end

--获取当前类型下,当前展示的天数
function EveryDayLianXuRechargeWGData:GetCurDayByRechargeType(btn_type)
    local total_recharge_day = self:GetTotalDayByRechargeType(btn_type)
    local has_recharge = self:GetTodayHasRecharge(btn_type)
    local cur_show_day = has_recharge and total_recharge_day or total_recharge_day + 1
    return cur_show_day
end

--普通奖励领取状态
--[[
btn_type: 1:充值6元, 2:充值30元
day:对应天数
reward_type: 0:普通奖励, 1:阶段奖励
--]]
function EveryDayLianXuRechargeWGData:GetRewardState(btn_type, day, reward_type)
    local state = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish
    if self.lianxu_recharge_info and self.lianxu_recharge_info[btn_type] then
        local state_info = self.lianxu_recharge_info[btn_type].reward_state_info
        if not IsEmptyTable(state_info) and not IsEmptyTable(state_info[day]) then
            local reward_is_get, reward_can_get
            if reward_type == EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal then
                reward_is_get = state_info[day].reward_is_get % 2 ~= 0
                reward_can_get = state_info[day].reward_can_get % 2 ~= 0
            elseif reward_type == EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Stage then
                reward_is_get = bit:_rshift(state_info[day].reward_is_get or 0, reward_type) > 0
                reward_can_get = bit:_rshift(state_info[day].reward_can_get or 0, reward_type) > 0
            end

            if reward_is_get then
                state = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet
            elseif reward_can_get then
                state = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet
            end
        end
    end
    return state
end

function EveryDayLianXuRechargeWGData:IsShowRebateLianXuRechargeRed()
    local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.everyday_recharge_lianchong)
	if not is_open then
		return 0
	end

    local red_type_1 = self:IsShowLianXuRechargeRedByBtnType(EVERYDAY_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_1)
    if red_type_1 == 1 then
        return 1
    end

    local red_type_2 = self:IsShowLianXuRechargeRedByBtnType(EVERYDAY_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_2)
    if red_type_2 == 1 then
        return 1
    end

    return 0
end

function EveryDayLianXuRechargeWGData:IsShowLianXuRechargeRedByBtnType(btn_type)
    local normal_reward_state, stage_reward_state
    for day = 1, 30 do

        normal_reward_state = self:GetRewardState(btn_type, day, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal)
        if normal_reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet then
            -- print_error(string.format("FFFFF==== 类型%s 第%s天 普通奖励未领", btn_type, day))
            return 1
        end

        stage_reward_state = self:GetRewardState(btn_type, day, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Stage)
        if stage_reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet then
            -- print_error(string.format("FFFFF==== 类型%s 第%s天 进度奖励未领", btn_type, day))
            return 1
        end
    end
    
    return 0
end

--普通奖励,首个可领的
function EveryDayLianXuRechargeWGData:TryGetFirstNormalRewardDay(btn_type)
    local first_day = -1
    for day = 1, 30 do
        if self:GetRewardState(btn_type, day, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal) == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet then
            first_day = day
            break
        end
    end
    if first_day <= 0 then
        local cur_total_day = self:GetTotalDayByRechargeType(btn_type)
        local group_index = self:GetCurGroupByRechargeType(btn_type)
        local cfg = self:GetRewardCfg(group_index, btn_type)
        for k, v in pairs(cfg) do
            if v.need_chongzhi_day == cur_total_day then
                first_day = k + 1
            end
        end
    end

    if first_day <= 0 then
        first_day = 1
    end
    return first_day
end

--阶段奖励,首个可领的
function EveryDayLianXuRechargeWGData:TryGetFirstStageRewardDay(btn_type)
    local first_day = 1
    local group_index = self:GetCurGroupByRechargeType(btn_type)
    local slider_cfg = self:GetSliderRewardCfg(group_index, btn_type)
    if IsEmptyTable(slider_cfg) then
        return 0
    end
    for i, v in ipairs(slider_cfg) do
        if self:GetRewardState(btn_type, v.reward_index + 1, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Stage) ~= EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet then
            first_day = i
            break
        end
    end
    
    return first_day
end

function EveryDayLianXuRechargeWGData:SetCacheBtnType(btn_type)
    self.lxcz_cur_btn_type = btn_type
end

function EveryDayLianXuRechargeWGData:GetCacheBtnType()
    return self.lxcz_cur_btn_type or 1
end

function EveryDayLianXuRechargeWGData:TransitionRewardDay(day)
    local reward_day = -1
    if day then
        if day % 30 == 0 then
            reward_day = 30
        else
            reward_day = day % 30
        end
    end
    return reward_day
end

function EveryDayLianXuRechargeWGData:GetOtherCfg(key)
    if self.other_cfg then
        if key then
            return self.other_cfg[key]
        else
            return self.other_cfg
        end
    end
end

function EveryDayLianXuRechargeWGData:GetRewardCfg(group, day_type)
    if self.reward_cfg_group_day and self.reward_cfg_group_day[group] then
        return self.reward_cfg_group_day[group][day_type]
    end
end

function EveryDayLianXuRechargeWGData:GetSliderRewardCfg(group, day_type)
    if not self.show_slider_cfg then
       self.show_slider_cfg = {}
    end

    if not self.show_slider_cfg[group] then
        self.show_slider_cfg[group] = {}
    end

    if not self.show_slider_cfg[group][day_type] then
        self.show_slider_cfg[group][day_type] = {}
    end

    local all_cfg = self:GetRewardCfg(group, day_type)
    if all_cfg and IsEmptyTable(self.show_slider_cfg[group][day_type]) then
        for i, v in ipairs(all_cfg) do
            if v and not IsEmptyTable(v.rare_reward_item) then
                table.insert(self.show_slider_cfg[group][day_type], v)
            end
        end
    end
    return self.show_slider_cfg[group][day_type]
end

function EveryDayLianXuRechargeWGData:GetSliderRewardCfgByIndex(group, day_type, index)
    local cfg = {}
    if not IsEmptyTable(self.show_slider_cfg[group][day_type]) then
        cfg = self.show_slider_cfg[group][day_type][index]
    end
    return cfg
end

--检测最后一档的奖励是否全部领取完
function EveryDayLianXuRechargeWGData:CheckEndGroupAllRewardIsGet()
    if not self:CheckCurIsEndGroup() then--不是最后一组
        return false
    end
    local is_all_get_1 = self:IsAllGetRewardByBtnType(EVERYDAY_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_1)
    local is_all_get_2 = self:IsAllGetRewardByBtnType(EVERYDAY_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_2)
    if is_all_get_1 and is_all_get_2 then
        return true
    end
    return false
end

--检测是否为最后一档
function EveryDayLianXuRechargeWGData:CheckCurIsEndGroup()
    local is_end = true
    local btn_type = EVERYDAY_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_1--两个档位的活动组数是同步的
    local group_index = self:GetCurGroupByRechargeType(btn_type)
    if self.reward_cfg_group_day and self.reward_cfg_group_day[group_index + 1] then--下一档配置能取到
        is_end = false
    end
    return is_end
end

--根据档位类型检测当下奖励是否领完
function EveryDayLianXuRechargeWGData:IsAllGetRewardByBtnType(btn_type)
    local normal_reward_state, stage_reward_state
    local group_index = self:GetCurGroupByRechargeType(btn_type)
    local slider_cfg = self:GetSliderRewardCfg(group_index, btn_type)
    local nor_cfg = self:GetRewardCfg(group_index, btn_type)
    if IsEmptyTable(slider_cfg) or IsEmptyTable(nor_cfg) then
        return true
    end

    for i, v in ipairs(nor_cfg) do
        normal_reward_state = self:GetRewardState(btn_type, v.reward_index + 1, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal)
        if normal_reward_state ~= EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet then
            return false
        end
    end

    for i, v in ipairs(slider_cfg) do
        stage_reward_state = self:GetRewardState(btn_type, v.reward_index + 1, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Stage)
        if stage_reward_state ~= EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet then
            return false
        end
    end
    
    return true
end

function EveryDayLianXuRechargeWGData:GetRewardCfgByRechargeDat(btn_type, recharge_day)
    if self.reward_cfg_by_recharge_day then
        return self.reward_cfg_by_recharge_day[btn_type] and self.reward_cfg_by_recharge_day[btn_type][recharge_day]
    end
end