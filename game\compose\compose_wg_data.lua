ComposeWGData = ComposeWGData or BaseClass()

function ComposeWGData:__init()
	if ComposeWGData.Instance then
		ErrorLog("[ComposeWGData]:Attempt to create singleton twice!")
	end
	-- 过滤(等级按版本开放)

	ComposeWGData.Instance = self

	self.is_show_enough = false
	self.menu_list = {}
	self.special_menu_list = {}
	self.xqs_child_compose_list = {}

	self.max_compose_num = 99
	self.is_come_from_task = false
	self.is_come_from_item_tip = false
	self.is_form_other_view = false
	self.eq_compose_remind_list = {}
	local compose_auto = ConfigManager.Instance:GetAutoConfig("compose_auto").compose_list
 	self.compose_list_cfg = compose_auto
 	self.compose_list_cfg_2 = ListToMap(compose_auto, "stuff_id_1")
	self.compose_list_cfg_3 = ListToMapList(compose_auto, "big_type", "type", "sub_type")
	self.compose_list_cfg_4 = ListToMap(compose_auto, "product_id")

	self.xianqi_stuff_compose_cfg = ConfigManager.Instance:GetAutoConfig("compose_auto").xianqi_stuff_compose
	self:InitSubTypeMinOpenLevel()

	-- 总
	-- RemindManager.Instance:Register(RemindName.Compose, BindTool.Bind(self.GetComposeTotalRemind, self))

	-- 装备
	RemindManager.Instance:Register(RemindName.Compose_Male_Equip, BindTool.Bind(self.GetComposeMaleEquipRemind, self))			-- 合成-男性装备
	RemindManager.Instance:Register(RemindName.Compose_Female_Equip, BindTool.Bind(self.GetComposeFemaleEquipRemind, self))		-- 合成-女性装备
	RemindManager.Instance:Register(RemindName.Compose_Jewelry_Equip, BindTool.Bind(self.GetComposeJewelryEquipRemind, self))	-- 合成-饰品
	RemindManager.Instance:Register(RemindName.Compose_ShenShou_Equip, BindTool.Bind(self.GetComposeShenShouEquipRemind, self))	-- 合成-神兽
	
	self:RegisterRemindInBagEquip()

	-- 仙器
	RemindManager.Instance:Register(RemindName.Compose_XianQi_ShengJie, BindTool.Bind(self.GetXianQiShengJieRemind, self))		-- 合成仙器升阶红点
	RemindManager.Instance:Register(RemindName.Compose_XianQi_ShengXing, BindTool.Bind(self.GetXianQiShenXingRemind, self))		-- 合成仙器升星红点
	self:RegisterRemindInBagXianQi(RemindName.Compose_XianQi_ShengJie, TabIndex.other_compose_shengjie)
	self:RegisterRemindInBagXianQi(RemindName.Compose_XianQi_ShengXing, TabIndex.other_compose_shengxing)

	-- 道具
	local big_type = math.floor(TabIndex.other_compose_daoju / 10)
	local small_type = TabIndex.other_compose_daoju % 10
	RemindManager.Instance:Register(RemindName.Compose_DaoJu, BindTool.Bind(self.GetComposeRemindByType, self, small_type, big_type, "other_compose_daoju"))		-- 合成道具红点
	self:RegisterRemindInBag(RemindName.Compose_DaoJu, small_type, big_type)

	-- big_type = math.floor(TabIndex.other_compose_stone / 10)
	-- small_type = TabIndex.other_compose_stone % 10
	-- RemindManager.Instance:Register(RemindName.Compose_BaoShi, BindTool.Bind(self.GetComposeRemindByType, self, small_type, big_type, "other_compose_stone"))		-- 合成宝石红点
	-- self:RegisterRemindInBag(RemindName.Compose_BaoShi, small_type, big_type)

	big_type = math.floor(TabIndex.other_compose_taozhuang / 10)
	small_type = TabIndex.other_compose_taozhuang % 10
	RemindManager.Instance:Register(RemindName.Compose_TaoZhuang, BindTool.Bind(self.GetComposeRemindByType, self, small_type, big_type, "other_compose_taozhuang"))	-- 合成套装石红点
	self:RegisterRemindInBag(RemindName.Compose_TaoZhuang, small_type, big_type)

	-- 特殊
	big_type = math.floor(TabIndex.other_compose_xiaogui / 10)
	small_type = TabIndex.other_compose_xiaogui % 10
 	RemindManager.Instance:Register(RemindName.Compose_XiaoGui, BindTool.Bind(self.GetComposeXiaoGuiRemind, self))				-- 合成小鬼红点
	self:RegisterRemindInBag(RemindName.Compose_XiaoGui, small_type, big_type)

	big_type = math.floor(TabIndex.other_compose_tianshen / 10)
	small_type = TabIndex.other_compose_tianshen % 10
 	RemindManager.Instance:Register(RemindName.Compose_TianShen, BindTool.Bind(self.GetComposeTianShenRemind, self))			-- 合成-天神
	self:RegisterRemindInBag(RemindName.Compose_TianShen, small_type, big_type)

	big_type = math.floor(TabIndex.other_compose_shenbing / 10)
	small_type = TabIndex.other_compose_shenbing % 10
	RemindManager.Instance:Register(RemindName.Compose_ShenBing, BindTool.Bind(self.GetComposeRemindByType, self, small_type, big_type, "other_compose_shenbing"))		-- 合成-神兵
	self:RegisterRemindInBag(RemindName.Compose_ShenBing, small_type, big_type)


	big_type = math.floor(TabIndex.other_compose_beast / 10)
	small_type = TabIndex.other_compose_beast % 10
	RemindManager.Instance:Register(RemindName.Compose_Beast, BindTool.Bind(self.GetComposeRemindByType, self, small_type, big_type, "other_compose_beast"))		-- 合成-神兵
	self:RegisterRemindInBag(RemindName.Compose_Beast, small_type, big_type)
	-- big_type = math.floor(TabIndex.other_compose_shitian / 10)
	-- small_type = TabIndex.other_compose_shitian % 10
	-- RemindManager.Instance:Register(RemindName.Compose_ShiTian, BindTool.Bind(self.GetComposeRemindByType, self, small_type, big_type, "other_compose_shitian"))		-- 合成-神兵
	-- self:RegisterRemindInBag(RemindName.Compose_ShiTian, small_type, big_type)
	-- 仙器材料
	-- big_type = math.floor(TabIndex.other_xianqi_stuff / 10)
	-- small_type = TabIndex.other_xianqi_stuff % 10
	RemindManager.Instance:Register(RemindName.Compose_XianQi_Stuff, BindTool.Bind(self.GetXQSComposeRemind, self))		-- 合成-仙器材料
	self:RegisterXQSCRemindInBag(RemindName.Compose_XianQi_Stuff)

	big_type = math.floor(TabIndex.other_compose_back / 10)
	small_type = TabIndex.other_compose_back % 10
	RemindManager.Instance:Register(RemindName.Compose_Background, BindTool.Bind(self.GetComposeRemindByType, self, small_type, big_type, "other_compose_back"))		-- 合成-神兵
	self:RegisterRemindInBag(RemindName.Compose_Background, small_type, big_type)
end

function ComposeWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.Compose)
	RemindManager.Instance:UnRegister(RemindName.Compose_Male_Equip)
	RemindManager.Instance:UnRegister(RemindName.Compose_Female_Equip)
	RemindManager.Instance:UnRegister(RemindName.Compose_Jewelry_Equip)
	RemindManager.Instance:UnRegister(RemindName.Compose_ShenShou_Equip)
	RemindManager.Instance:UnRegister(RemindName.Compose_DaoJu)
	-- RemindManager.Instance:UnRegister(RemindName.Compose_BaoShi)
	RemindManager.Instance:UnRegister(RemindName.Compose_TaoZhuang)
	RemindManager.Instance:UnRegister(RemindName.Compose_XianQi_Stuff)
	RemindManager.Instance:UnRegister(RemindName.Compose_XianQi_ShengJie)
	RemindManager.Instance:UnRegister(RemindName.Compose_XianQi_ShengXing)

	RemindManager.Instance:UnRegister(RemindName.Compose_XiaoGui)
	RemindManager.Instance:UnRegister(RemindName.Compose_TianShen)
	RemindManager.Instance:UnRegister(RemindName.Compose_ShenBing)
	RemindManager.Instance:UnRegister(RemindName.Compose_Beast)
	--RemindManager.Instance:UnRegister(RemindName.Compose_ShiTian)
	RemindManager.Instance:UnRegister(RemindName.Compose_Background)

	ComposeWGData.Instance = nil
	self.is_form_other_view = nil
end

function ComposeWGData:InitSubTypeMinOpenLevel()
	self.compose_sub_type_limit = {}
	local need_level = RoleWGData.GetRoleMaxLevel()
	for k,v in ipairs(self.compose_list_cfg) do
		self.compose_sub_type_limit[v.big_type] = self.compose_sub_type_limit[v.big_type] or {}
		self.compose_sub_type_limit[v.big_type][v.type] = self.compose_sub_type_limit[v.big_type][v.type] or {}
		need_level = self.compose_sub_type_limit[v.big_type][v.type][v.sub_type]
		if need_level then
			if v.level < need_level then
				self.compose_sub_type_limit[v.big_type][v.type][v.sub_type] = v.level
			end
		else
			self.compose_sub_type_limit[v.big_type][v.type][v.sub_type] = v.level
		end
	end
end

function ComposeWGData:GetSubTypeMinOpenLevel(big_type, type, sub_type)
	local empty_table = {}
	return ((self.compose_sub_type_limit[big_type] or empty_table)[type] or empty_table)[sub_type] or 0
end

function ComposeWGData:RegisterRemindInBagEquip()
	local cfg = ConfigManager.Instance:GetAutoConfig("equipforge_auto")
	local equip_compose_cfg = cfg.equip_compose
	if IsEmptyTable(equip_compose_cfg) then
		return
	end

	local map = {}
	local stuff_id, stuff_num
	for k,v in ipairs(equip_compose_cfg) do
		for i = 1, 2 do
			local id_str = string.format("stuff%s_id", i)
			local num_str = string.format("stuff%s_num", i)
			stuff_id = v[id_str] or 0
			stuff_num = v[num_str] or 0
			if stuff_id > 0 and stuff_num > 0 then
				map[stuff_id] = true
			end
		end
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	if not IsEmptyTable(item_id_list) then
		BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.Compose_Male_Equip, item_id_list, nil)
		BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.Compose_Female_Equip, item_id_list, nil)
		BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.Compose_Jewelry_Equip, item_id_list, nil)
	end

	local shenshou_compose_cfg = ShenShouWGData.Instance:GetShenShouComposeCfg()
	local shenshou_id_list = {}
	local shenshou_id_map = {}

	for k, v in pairs(shenshou_compose_cfg) do
		if v.is_need_item == 1 and v.item_id > 0 and v.item_num > 0 and nil == shenshou_id_map[v.item_id] then
			shenshou_id_map[v.item_id] = v.item_id
			table.insert(shenshou_id_list, v.item_id)
		end
	end

	if not IsEmptyTable(shenshou_id_list) then
		BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.Compose_ShenShou_Equip, shenshou_id_list, nil)
	end
end

-- 注册背包红点通知
function ComposeWGData:RegisterRemindInBag(remind_name, small_type, big_type)
	local map = {}
	for k,v in pairs(self.compose_list_cfg) do
		if v.type == small_type and v.big_type == big_type then
			for i = 1, 4 do
				local item_id = v["stuff_id_" .. i]
				if item_id and item_id > 0 then
					map[item_id] = true
				end
			end
		end
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	if not IsEmptyTable(item_id_list) then
		BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
	end
end

-- 注册XianQi背包红点通知
function ComposeWGData:RegisterRemindInBagXianQi(remind_name, index)
	local item_id_list = EquipmentWGData.Instance:GetXianQiComposeFocusItemList(index)
	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function ComposeWGData:GetMaxToggleCount()
	local max = 0
	if IsEmptyTable(self.menu_list) then
		self:FormatMenu()
	end
	for k,v in pairs(self.menu_list) do
		if v.sub_menu and #v.sub_menu > max then
			max = #v.sub_menu
		end
	end
	return max
end

-- 设置显示足够材料的配方标识
function ComposeWGData:SetIsShowEnough(is_show)
	if nil == is_show then
		return
	end

	self.is_show_enough = is_show
end

-- 获取显示足够材料的配方标识
function ComposeWGData:GetIsShowEnough()
	return self.is_show_enough
end

-- 获取菜单列表
function ComposeWGData:GetMenuList()
	if IsEmptyTable(self.menu_list) then
		self:FormatMenu()
	end
	return self.menu_list
end

-- 获取可合成产品的最大数量
function ComposeWGData:ComposeMaxNum(seq_id)
	local num = 0
	seq_id = seq_id or 0
	if seq_id <= 0 then
		return num
	end

	local can_compose_data
	for k, v in pairs(self.compose_list_cfg) do
		if v.producd_seq == seq_id then
			can_compose_data = v
			break
		end
	end

	if can_compose_data == nil then
		return 0
	end

	num = -1
	for i= 1, 4 do
		local cost = can_compose_data["stuff_count_" .. i]
		local item_id = can_compose_data["stuff_id_" .. i]
		if cost > 0 then
			local count = 0
			local is_xiaogui = ItemWGData.GetIsXiaogGui(item_id)
			if is_xiaogui then
				count = ItemWGData.Instance:GetItemNumInBagById(item_id)
				local has_xiaogui = EquipWGData.Instance:GetmpGuardInfoByid(item_id)
				count = has_xiaogui and count + 1 or count
			else
				count = ItemWGData.Instance:GetItemNumInBagById(item_id)
			end
			num = num ~= -1 and math.min(num, count / cost) or count / cost
		end
	end

	num = num <= 0 and 0 or num
	return math.floor(num)
end

-- 根据合成ID获取合成材料ID
function ComposeWGData:GetMaterialCfgByID(producd_seq)
	local temp_material_info = {}
	if producd_seq and producd_seq > 0 then
		for k, v in pairs(self.compose_list_cfg) do
			if v and v.producd_seq == producd_seq then
				if v.stuff_id_1 > 0 then
					temp_material_info[1] = {item_id = v.stuff_id_1, num = v.stuff_count_1}
				end

				if v.stuff_id_2 > 0 then
					temp_material_info[2] = {item_id = v.stuff_id_2, num = v.stuff_count_2}
				end

				if v.stuff_id_3 > 0 then
					temp_material_info[3] = {item_id = v.stuff_id_3, num = v.stuff_count_3}
				end

				if v.stuff_id_4 > 0 then
					temp_material_info[4] = {item_id = v.stuff_id_4, num = v.stuff_count_4}
				end

				break
			end
		end
	end

	return temp_material_info
end

function ComposeWGData:GetLackOfGoodsID(producd_seq)
	local stuff_list = self:GetMaterialCfgByID(producd_seq)
	for k,v in pairs(stuff_list) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if num <= 0 and ItemWGData.GetIsXiaogGui(v.item_id) then --显示小鬼(10101)不能合成
			local has_xiaogui = EquipWGData.Instance:GetmpGuardInfoByid(v.item_id) --身上装备着的小鬼
			num = has_xiaogui and num + 1 or num
		end

		if v.num > num then
			return v.item_id
		end
	end
end

function ComposeWGData:GetConfigByStuffId(item_id)
	for k, config in pairs(self.compose_list_cfg) do
		if config.product_id == item_id then
			return config
		end

		local index = 1 -- 保护循环不要超过最大限制s
		while config["stuff_id_" .. index] ~= nil and index < 8 do
			if config["stuff_id_" .. index] == item_id then
				return config
			end
			index = index + 1
		end
	end

	return nil
end

-- 格式化菜单数据
function ComposeWGData:FormatMenu()
	local compose_menu = ConfigManager.Instance:GetAutoConfig("compose_auto").compose_menu
	local role_level = RoleWGData.Instance:GetRoleVo().level
	self.menu_list = {}
	local need_level = 0
	for i = 1, #compose_menu do
		local item = compose_menu[i]
		if item then
			local menu = self.menu_list[item.type]
			if nil == menu then
				self.menu_list[item.type] = {}
				self.menu_list[item.type].id = item.type
				self.menu_list[item.type].name = item.type_name
				self.menu_list[item.type].big_type = item.big_type
				self.menu_list[item.type].sub_menu = {}
			end

			need_level = self:GetSubTypeMinOpenLevel(item.big_type, item.type, item.sub_type)
			-- 一级类型的开启 还是通过功能开启控制
			if role_level >= need_level then
				local temp = {
					id = item.sub_type,
					menu_type = item.type,
					name = item.sub_name,
					big_type = item.big_type,
				}
				table.insert(self.menu_list[item.type].sub_menu, temp)
			end
		end
	end
end

--格式化语言文字
function ComposeWGData:FormatLanguage(key, str)
	if nil == key or nil == str then
		return ""
	end
	local lanugae = Language.Compose[key]
	if nil == lanugae then
		return ""
	end
	return string.format(lanugae, str)
end

function ComposeWGData:IsShowComposeRedPoint()
	return 0
end

function ComposeWGData:SetEQComposeRemindList(view_index, bool)
	self.eq_compose_remind_list[view_index] = bool
end

function ComposeWGData:GetEQComposeHaveRemind()
	for k,v in pairs(self.eq_compose_remind_list) do
		if v then
			return true
		end
	end

	return false
end

function ComposeWGData:ComposeToBeStrengthen(view_index, remind, open_param, to_ui_param)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_COMPOSE, remind, function ()
		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, view_index,
										{to_ui_name = 0, open_param = open_param, to_ui_param = to_ui_param})
		return true
	end)
end

function ComposeWGData:GetComposeMaleEquipRemind()
	local view_index = TabIndex.other_compose_eq_hecheng_one
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("other_compose_eq_hecheng_one")
	if not is_open then
		self:SetEQComposeRemindList(view_index, false)
		return 0
	end

	local role_sex = RoleWGData.Instance:GetRoleSex()
	if role_sex ~= GameEnum.MALE then
		self:SetEQComposeRemindList(view_index, false)
		return 0
	end

	local remind = 0
	local title_index = EquipmentWGData.COMPOSE_EQUIP_TYPE.MALE

	local wear_remind, type, star_level, order = EquipmentWGData.Instance:GetWearEquipComposeRemind(title_index)
	if wear_remind then
		local open_param, to_ui_param = EquipmentWGData.Instance:GetEquipComposeJumpParam(title_index, type, star_level, order)
		self:ComposeToBeStrengthen(view_index, 1, open_param, to_ui_param)
		self:SetEQComposeRemindList(view_index, true)
		remind = 1
	else
		self:SetEQComposeRemindList(view_index, false)
	end

	local no_wear_remind = not self:GetEQComposeHaveRemind()
	if no_wear_remind then
		self:ComposeToBeStrengthen(view_index, 0)
	end

	if remind == 0 then
		remind = EquipmentWGData.Instance:GetComposeEquipRemindByBigType(title_index)
	end

	return remind
end

function ComposeWGData:GetComposeFemaleEquipRemind()
	local view_index = TabIndex.other_compose_eq_hecheng_two
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("other_compose_eq_hecheng_two")
	if not is_open then
		self:SetEQComposeRemindList(view_index, false)
		return 0
	end

	local role_sex = RoleWGData.Instance:GetRoleSex()
	if role_sex ~= GameEnum.FEMALE then
		self:SetEQComposeRemindList(view_index, false)
		return 0
	end

	local remind = 0
	local title_index = EquipmentWGData.COMPOSE_EQUIP_TYPE.FEMALE

	local wear_remind, type, star_level, order = EquipmentWGData.Instance:GetWearEquipComposeRemind(title_index)
	if wear_remind then
		local open_param, to_ui_param = EquipmentWGData.Instance:GetEquipComposeJumpParam(title_index, type, star_level, order)
		self:ComposeToBeStrengthen(view_index, 1, open_param, to_ui_param)
		self:SetEQComposeRemindList(view_index, true)
		remind = 1
	else
		self:SetEQComposeRemindList(view_index, false)
	end

	local no_wear_remind = not self:GetEQComposeHaveRemind()
	if no_wear_remind then
		self:ComposeToBeStrengthen(view_index, 0)
	end

	if remind == 0 then
		remind = EquipmentWGData.Instance:GetComposeEquipRemindByBigType(title_index)
	end

	return remind
end

function ComposeWGData:GetComposeJewelryEquipRemind()
	local view_index = TabIndex.other_compose_eq_hecheng_three
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("other_compose_eq_hecheng_three")
	if not is_open then
		self:SetEQComposeRemindList(view_index, false)
		return 0
	end

	local remind = 0
	local title_index = EquipmentWGData.COMPOSE_EQUIP_TYPE.JEWELRY

	local wear_remind, type, star_level, order = EquipmentWGData.Instance:GetWearEquipComposeRemind(title_index)
	if wear_remind then
		local open_param, to_ui_param = EquipmentWGData.Instance:GetEquipComposeJumpParam(title_index, type, star_level, order)
		self:ComposeToBeStrengthen(view_index, 1, open_param, to_ui_param)
		self:SetEQComposeRemindList(view_index, true)
		remind = 1
	else
		self:SetEQComposeRemindList(view_index, false)
	end

	local no_wear_remind = not self:GetEQComposeHaveRemind()
	if no_wear_remind then
		self:ComposeToBeStrengthen(view_index, 0)
	end

	if remind == 0 then
		remind = EquipmentWGData.Instance:GetComposeEquipRemindByBigType(title_index)
	end

	return remind
end

function ComposeWGData:GetComposeShenShouEquipRemind()
	local view_index = TabIndex.other_compose_eq_hecheng_shenshou
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("other_compose_eq_hecheng_shenshou")
	if not is_open then
		self:SetEQComposeRemindList(view_index, false)
		return 0
	end
	local all_cfg = EquipmentWGData.Instance:GetShenShouAllComposeCfg()
	local can_hecheng_item = {}
	local is_batter_list = {}
	local temp_data = {}
	for k,v in pairs(all_cfg) do
		local item_cost_enough = true

		if v.is_need_item == 1 and v.item_id > 0 and v.item_num > 0 then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)

			if has_num < v.item_num then
				item_cost_enough = false
			end
		end

		if item_cost_enough then
			can_hecheng_item = ShenShouWGData.Instance:FilterShenShouEq(v.need_qualit, v.need_start_num)
			local need_red = false
			if #can_hecheng_item >= 3 then
				need_red = true
				for t,q in pairs(can_hecheng_item) do
					if is_batter_list[q.index] and is_batter_list[q.index] == 1 then
						need_red = false
						break
					end
					if ShenShouWGData.Instance:GetShenShouEquipHaveUpFlag(q) then
						is_batter_list[q.index] = 1
						need_red = false
						break
					else
						is_batter_list[q.index] = 0
					end
				end
	
				if need_red then
					temp_data.star_count = v.give_start_num
					temp_data.item_id = v.v_item_id
					if ShenShouWGData.Instance:GetComposeProductUpFlag(temp_data) then
						return 1
					end
				end
			end
		end
	end

	return 0
end


function ComposeWGData:GetComposeXiaoGuiRemind()
	local num = 0
	local list = {}

	if not FunOpen.Instance:GetFunIsOpened(FunName.Compose) then
		return num, list
	end

	local compose_list_config = self.compose_list_cfg
	local type = TabIndex.other_compose_xiaogui % 10
	local big_type = math.floor(TabIndex.other_compose_xiaogui / 10)
	local role_level = RoleWGData.Instance:GetRoleVo().level

	for k,v in ipairs(compose_list_config) do
		if v.is_need_remind == 1 and v.type == type and v.big_type == big_type and role_level >= v.level then
			local need_num = 0
			local cur_num = 0
			local coin = RoleWGData.Instance.role_info.coin or 0
			for i=1,4 do
				local cost = v["stuff_count_" .. i]
				if cost > 0 then
					need_num = need_num + 1
					local has_xiaogui = 0
					local is_xiaogui = ItemWGData.GetIsXiaogGui(v.product_id)
					if is_xiaogui then
						has_xiaogui = EquipWGData.Instance:GetmpGuardInfoByid(v["stuff_id_"..i]) and 1 or 0
					end
					local count = ItemWGData.Instance:GetItemNumInBagById(v["stuff_id_" .. i]) + has_xiaogui
					if count >= cost then
						cur_num = cur_num + 1
					end
				end
			end

			if cur_num >= need_num and coin >= v.coin then
				list = {big_type = v.big_type, type = v.type, sub_type = v.sub_type, index = v.child_type}
				if num + 1 >= 1 then
					 MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHOUHU_COMPOSE, 1, function ()
    			        FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, TabIndex.other_compose_xiaogui)
    			    end)
				end

				return num + 1, list
			end
		end
	end

	if num >= 1 then
		 MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHOUHU_COMPOSE, 1, function ()
            FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, TabIndex.other_compose_xiaogui)
        end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHOUHU_COMPOSE, 0)
	end
	return num, list
end

function ComposeWGData:GetComposeTianShenRemind()
	local num = 0
	local list = {}

	if not FunOpen.Instance:GetFunIsOpened(FunName.Compose) then
		return num, list
	end

	local compose_list_config = self.compose_list_cfg
	local type = TabIndex.other_compose_tianshen % 10
	local big_type = math.floor(TabIndex.other_compose_tianshen / 10)
	local role_level = RoleWGData.Instance:GetRoleVo().level
	for k,v in ipairs(compose_list_config) do
		--is_need_remind: 是否需要红点 type:上标签 big_type：右标签
		if v.is_need_remind == 1 and v.type == type and v.big_type == big_type and role_level >= v.level then
			local need_num = 0
			local cur_num = 0
			local coin = RoleWGData.Instance.role_info.coin or 0
			for i=1,4 do
				local cost = v["stuff_count_" .. i]
				if cost > 0 then
					need_num = need_num + 1
					local count = ItemWGData.Instance:GetItemNumInBagById(v["stuff_id_" .. i])
					if count >= cost then
						cur_num = cur_num + 1
					end
				end
			end
			if cur_num >= need_num and coin >= v.coin then
				if v.sub_type == 1 then
					if self:NotActiveTianShen(v) then
						list = {big_type = v.big_type, type = v.type, sub_type = v.sub_type, index = v.child_type}
						return num + 1, list
					end
				elseif v.sub_type == 2 then
					if self:CanRemindShenQiCompose(v) then
						list = {big_type = v.big_type, type = v.type, sub_type = v.sub_type, index = v.child_type}
						return num + 1, list
					end
				else
					list = {big_type = v.big_type, type = v.type, sub_type = v.sub_type, index = v.child_type}
					return num + 1, list
				end
			end
		end
	end
	return num, list
end

function ComposeWGData:NotActiveTianShen(data)
	-- local magic_cfg = TianShenWGData.Instance:GetAppeImage(data.product_id)
	-- if magic_cfg then
	-- 	local active = TianShenWGData.Instance:IsActivation(magic_cfg.index)
	-- 	if not active then
	-- 		return true
	-- 	end
	-- end
	-- return false

	-- 还能继续升星
	return true
end

function ComposeWGData:NotActiveShenQi(data)
	local shenqi_cfg = TianShenWGData.Instance:GetShenQiByStuffId(data.product_id)
	if shenqi_cfg then
		local active = TianShenWGData.Instance:IsShenQiActivation(shenqi_cfg.index)
		if not active then
			return true
		end
	else
		local cfg = TianShenWGData.Instance:GetWaiGuanCfgByStuff(data.product_id)
		if cfg then
			local waiguan_list_cfg = TianShenWGData.Instance:GetWaiGuanInfo(cfg.waiguan_id)
			if not waiguan_list_cfg then
				return true
			end
		end
	end

	return false
end

-- 神器配方是特殊材料合成的，已激活过不提醒合成
function ComposeWGData:CanRemindShenQiCompose(data)
	if IsEmptyTable(data) then
		return false
	end

	local no_special_stuff = true
	local stuff_id, stuff_num
	for i = 1, 4 do
		stuff_id = data["stuff_id_" .. i] or 0
		stuff_num = data["stuff_count_" .. i] or 0
		if stuff_id == 46048 and stuff_num > 0 then
			no_special_stuff = false
			break
		end
	end

	if no_special_stuff then
		return true
	end

	if self:NotActiveShenQi(data) then
		return true
	end

	return false
end

function ComposeWGData:GetComposeRemindByType(type, big_type, fun_key)
	local num = 0
	local list = {}
	if not big_type or not type then
		return num, list
	end

	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(fun_key)
	if not is_open then
		return num, list
	end

	local index = 0
	local compose_list_config = self.compose_list_cfg
	local coin = RoleWGData.Instance.role_info.coin or 0
	local role_level = RoleWGData.Instance:GetRoleVo().level

	for k,v in ipairs(compose_list_config) do
		--is_need_remind: 是否需要红点 type:上标签 big_type：右标签
		if v.is_need_remind == 1 and v.type == type and v.big_type == big_type and role_level >= v.level then
			index = index + 1
			local need_num = 0
			local cur_num = 0

			for i = 1, 4 do
				local cost = v["stuff_count_" .. i]
				local stuff = v["stuff_id_" .. i]
				if cost > 0 and stuff > 0 then
					need_num = need_num + 1
					--境界系统的特殊判断
					if not self:CheckJingJieCurCostStuff(big_type, type, stuff) then

						local count = ItemWGData.Instance:GetItemNumInBagById(stuff)
						if count >= cost then
							cur_num = cur_num + 1
						end

					end
				end
			end
			
			if cur_num >= need_num and coin >= v.coin and self:CheckBeastIsNotActive(big_type, type, v) then
				list = {big_type = v.big_type, type = v.type, sub_type = v.sub_type, index = v.child_type}
				return num + 1, list
			end
		end
	end
	return num, list
end

--检测一下是否为境界当前消耗物品
--是:屏蔽当前红点
function ComposeWGData:CheckJingJieCurCostStuff(big_type, small_type, stuff_id)
	local check_small_type = TabIndex.other_compose_daoju % 10
	local check_big_type = math.floor(TabIndex.other_compose_daoju / 10)
	--不是道具类型不用判断
	if small_type ~= check_small_type or big_type ~= check_big_type then
		return false
	end

	local stuff_use_level = JINGJIE_MAT_LEVEL[stuff_id or 0]
	--不是境界消耗物品不用判断
	if not stuff_use_level then
		return false
	end
	local check_cur_stuff = JingJieWGData.Instance:GetCurJingJieStuff()
	local cur_use_level = JINGJIE_MAT_LEVEL[check_cur_stuff]
	--print_error("FFF=== check_cur_stuff == stuff_id", check_cur_stuff == stuff_id, check_cur_stuff, stuff_id)
	return stuff_use_level >= cur_use_level
end

function ComposeWGData:GetComposeTotalRemind()
	local remind, big_type, small_type
	-- 男装
	if self:GetComposeMaleEquipRemind() == 1 then
		return 1
	end

	-- 女装
	if self:GetComposeFemaleEquipRemind() == 1 then
		return 1
	end

	-- 饰品
	if self:GetComposeJewelryEquipRemind() == 1 then
		return 1
	end

	--天神
	if self:GetComposeTianShenRemind() == 1 then
		return 1
	end

	--小鬼
	if self:GetComposeXiaoGuiRemind() == 1 then
		return 1
	end

	-- 神兵
	big_type = math.floor(TabIndex.other_compose_shenbing / 10)
	small_type = TabIndex.other_compose_shenbing % 10
	remind = self:GetComposeRemindByType(small_type, big_type, "other_compose_shenbing")
	if remind == 1 then
		return 1
	end

	-- 驭兽
	big_type = math.floor(TabIndex.other_compose_beast / 10)
	small_type = TabIndex.other_compose_beast % 10
	remind = self:GetComposeRemindByType(small_type, big_type, "other_compose_beast")
	if remind == 1 then
		return 1
	end

	--套装石
	big_type = math.floor(TabIndex.other_compose_taozhuang / 10)
	small_type = TabIndex.other_compose_taozhuang % 10
	remind = self:GetComposeRemindByType(small_type, big_type, "other_compose_taozhuang")
	if remind == 1 then
		return 1
	end

	-- --弑天套装
	-- big_type = math.floor(TabIndex.other_compose_shitian / 10)
	-- small_type = TabIndex.other_compose_shitian % 10
	-- remind = self:GetComposeRemindByType(small_type, big_type, "other_compose_shitian")
	-- if remind == 1 then
	-- 	return 1
	-- end

	--道具
	big_type = math.floor(TabIndex.other_compose_daoju / 10)
	small_type = TabIndex.other_compose_daoju % 10
	remind = self:GetComposeRemindByType(small_type, big_type, "other_compose_daoju")
	if remind == 1 then
		return 1
	end

	--奇境
	big_type = math.floor(TabIndex.other_compose_back / 10)
	small_type = TabIndex.other_compose_back % 10
	remind = self:GetComposeRemindByType(small_type, big_type, "other_compose_back")
	if remind == 1 then
		return 1
	end

	-- 仙器进阶
	if self:GetXianQiShengJieRemind() == 1 then
		return 1
	end

	-- 仙器升星
	if self:GetXianQiShenXingRemind() == 1 then
		return 1
	end

	-- 仙器材料
	if self:GetXQSComposeRemind() == 1 then
		return 1
	end

	return 0
end

--获取一个有红点的标签
function ComposeWGData:GetAIsRmindTab(index, no_limit)
	local remind, list
	local big_type, small_type
	--仙器升阶
	if no_limit or index == TabIndex.other_compose_shengjie then
		remind, list = self:GetXianQiShengJieRemind()
		if remind == 1 then
			return list
		end
	end

	--仙器升星
	if no_limit or index == TabIndex.other_compose_shengxing then
		remind, list = self:GetXianQiShenXingRemind()
		if remind == 1 then
			return list
		end
	end

	--天神
	if no_limit or index == TabIndex.other_compose_tianshen then
		remind, list = self:GetComposeTianShenRemind()
		if remind == 1 then
			return list
		end
	end

	--小鬼
	if no_limit or index == TabIndex.other_compose_xiaogui then
		remind, list = self:GetComposeXiaoGuiRemind()
		if remind == 1 then
			return list
		end
	end

	-- 神兵
	if no_limit or index == TabIndex.other_compose_shenbing then
		big_type = math.floor(TabIndex.other_compose_shenbing / 10)
		small_type = TabIndex.other_compose_shenbing % 10
		remind, list = self:GetComposeRemindByType(small_type, big_type, "other_compose_shenbing")
		if remind == 1 then
			return list
		end
	end

	-- 驭兽
	if no_limit or index == TabIndex.other_compose_beast then
		big_type = math.floor(TabIndex.other_compose_beast / 10)
		small_type = TabIndex.other_compose_beast % 10
		remind, list = self:GetComposeRemindByType(small_type, big_type, "other_compose_beast")
		if remind == 1 then
			return list
		end
	end

	--套装石
	if no_limit or index == TabIndex.other_compose_taozhuang then
		big_type = math.floor(TabIndex.other_compose_taozhuang / 10)
		small_type = TabIndex.other_compose_taozhuang % 10
		remind, list = self:GetComposeRemindByType(small_type, big_type, "other_compose_taozhuang")
		if remind == 1 then
			return list
		end
	end

	-- --弑天套装
	-- if no_limit or index == TabIndex.other_compose_shitian then
	-- 	big_type = math.floor(TabIndex.other_compose_shitian / 10)
	-- 	small_type = TabIndex.other_compose_shitian % 10
	-- 	remind, list = self:GetComposeRemindByType(small_type, big_type, "other_compose_shitian")
	-- 	if remind == 1 then
	-- 		return list
	-- 	end
	-- end


	--道具
	if no_limit or index == TabIndex.other_compose_daoju then
		big_type = math.floor(TabIndex.other_compose_daoju / 10)
		small_type = TabIndex.other_compose_daoju % 10
		remind, list = self:GetComposeRemindByType(small_type, big_type, "other_compose_daoju")
		if remind == 1 then
			return list
		end
	end

	--奇境
	if no_limit or index == TabIndex.other_compose_back then
		big_type = math.floor(TabIndex.other_compose_back / 10)
		small_type = TabIndex.other_compose_back % 10
		remind, list = self:GetComposeRemindByType(small_type, big_type, "other_compose_back")
		if remind == 1 then
			return list
		end
	end

	-- --宝石
	-- if no_limit or index == TabIndex.other_compose_stone then
	-- 	big_type = math.floor(TabIndex.other_compose_stone / 10)
	-- 	small_type = TabIndex.other_compose_stone % 10
	-- 	remind, list = self:GetComposeRemindByType(small_type, big_type, "other_compose_stone")
	-- 	if remind == 1 then
	-- 		return list
	-- 	end
	-- end

	--仙器材料
	if no_limit or index == TabIndex.other_xianqi_stuff then
		big_type = math.floor(TabIndex.other_xianqi_stuff / 10)
		small_type = TabIndex.other_xianqi_stuff % 10
		remind, list = self:GetXQSComposeRemind()
		if remind == 1 then
			return list
		end
	end

	return list
end

function ComposeWGData:GetRealJumpIndexByType(list)
	if IsEmptyTable(list) or IsEmptyTable(self.compose_list_cfg_3) then
		return 1
	end

	local big_type = list.big_type
	local type = list.type
	local sub_type = list.sub_type
	local child_type = list.index

	local cfg = self.compose_list_cfg_3[big_type] and self.compose_list_cfg_3[big_type][type]
				and self.compose_list_cfg_3[big_type][type][sub_type]

	if IsEmptyTable(cfg) then
		return 1
	end

	for k, v in ipairs(cfg) do
		if v.child_type == child_type then
			return v.child_type
		end
	end

	return 1
end

function ComposeWGData:SetComeFromTask(boo)
	self.is_come_from_task = boo
end

function ComposeWGData:GetComeFromTask()
	return self.is_come_from_task
end

function ComposeWGData:SetPropTabData(data)
	self.big_tab_data_list = data
end

function ComposeWGData:GetChildDataListByIndex(cur_tab_index,item_parent_index)
	if self.big_tab_data_list[cur_tab_index] == nil then
		return nil
	end
	if self.big_tab_data_list[cur_tab_index][item_parent_index] == nil then
		return nil
	end
	return self.big_tab_data_list[cur_tab_index][item_parent_index].child
end

function ComposeWGData:SetIsZhuangBeiHechnegTask(bo)
	self.is_form_task_equip = bo
end

function ComposeWGData:GetIsZhuangBeiHechnegTask()
	return self.is_form_task_equip
end

function ComposeWGData:SetComeFromItemTip(bo)
	self.is_come_from_item_tip = bo
end
function ComposeWGData:GetComeFromItemTip()
	return self.is_come_from_item_tip
end

function ComposeWGData:GetComposeCfgByItemId(item_id)
	local cfg  = self.compose_list_cfg
	for k,v in pairs(cfg) do
		if v.product_id == item_id then
			return v
		end
	end
end

function ComposeWGData:GetXiaoGuiComposeCfg()
	local xiaogui_seq_list = EquipmentWGData.Instance:GetGuardComposeSeqList()
	local xiaogui_compose_show_cfg = EquipmentWGData.Instance:GetGuardComposeShowCfg()
	local cfg  = self.compose_list_cfg
	local compose_list = {}
	for k1, producd_seq in pairs(xiaogui_seq_list) do
		for k,v in pairs(cfg) do
			if v.producd_seq == producd_seq then
				for xiaogui_type, xiaogui_list in pairs(xiaogui_compose_show_cfg) do
					for k, xiaogui_info in pairs(xiaogui_list) do
						if not compose_list[xiaogui_info.scene_guard_type] then
							compose_list[xiaogui_info.scene_guard_type] = {}
						end
						if v.product_id == xiaogui_info.stuff_id_1 then
							table.insert(compose_list[xiaogui_info.scene_guard_type], v)
						end
					end
				end
			end
		end
	end

	return compose_list
end

function ComposeWGData:GetComposeCfgByStuffId1(stuff_id_1)
	return self.compose_list_cfg_2[stuff_id_1]
end

function ComposeWGData:GetComposeCfgByProductId(product_id)
	return self.compose_list_cfg_4[product_id]
end

function ComposeWGData:GetComposeCfgByStuffId2(item_id)
	local cfg  = self.compose_list_cfg
	if IsEmptyTable(cfg) or item_id == nil then
		return nil
	end

	local stuff_id, stuff_num
	for k,v in ipairs(cfg) do
		for i = 1, 4 do
			stuff_id = v["stuff_id_" .. i] or 0
			stuff_num = v["stuff_count_" .. i] or 0
			if stuff_id == item_id and stuff_id > 0 and stuff_num > 0 then
				return v
			end
		end
	end
	return nil
end

-- 帮玩家默认选中现在应该合成的(根据穿戴)
function ComposeWGData:GetXiaoGuiComposeJumpTabIdx()
	local defult_remind_guard = EquipmentWGData.Instance:GetDefultGuardComposeCfg()
	local guard_can_up = {true, true}
	local sub_type, child_type = 1, 1
	local guard_info = EquipWGData.Instance:GetmpGuardInfo()
	for i = 1, 2 do
		for k = 1, 2 do
			local wear_data = guard_info.item_wrapper[k]
			local wear_itemid = wear_data and wear_data.item_id or 0
			local imp_cfg = EquipmentWGData.Instance:GetGuardCfgByItemID(wear_itemid)
			if imp_cfg and imp_cfg.impguard_type == i then
				local can_compose_next_cfg = self:GetComposeCfgByStuffId1(wear_itemid)
				if can_compose_next_cfg then
					return can_compose_next_cfg.sub_type, can_compose_next_cfg.child_type

				elseif wear_itemid > 0 and not can_compose_next_cfg then
					guard_can_up[k] = false
				end
			end
		end

		if guard_can_up[i] then
			local remind_compose_cfg = self:GetComposeCfgByStuffId1(defult_remind_guard[i])
			if remind_compose_cfg then
				sub_type = remind_compose_cfg.sub_type
				child_type = remind_compose_cfg.child_type
				guard_can_up[i + 1] = false
			end
		end
	end

	return sub_type, child_type
end

function ComposeWGData:GetIsCanComposeByDataHasFeixian(data)
		local num = 0
	local total_num = 0

	for i = 1, 4 do
		if data["stuff_id_" .. i] ~= 0 then
			total_num = total_num + 1
			local item_count = ItemWGData.Instance:GetItemNumInBagById(data["stuff_id_" .. i])
			if item_count >= data["stuff_count_" .. i] then
				num = num + 1
			end
		end
	end

	if num ~= 0 and num >= total_num then
		return true
	else
		return false
	end
end

function ComposeWGData:GetIsCanComposeByData(data)
	local num = 0
	local total_num = 0
	if data.is_need_remind == 0 then
		return false
	end

	if self:IsTianShen(data) then
		if not self:NotActiveTianShen(data) then
			return false
		end
	elseif self:IsShenQi(data) then
		if not self:CanRemindShenQiCompose(data) then
			return false
		end
	end

	for i = 1, 4 do
		if data["stuff_id_" .. i] ~= 0  then --升仙令不显示红点and data.product_id ~= 26195

			if self:CheckJingJieCurCostStuff(data.big_type, data.type, data["stuff_id_" .. i]) then
				return false
			end

			total_num = total_num + 1
			local item_count = 0
			local is_xiaogui = ItemWGData.GetIsXiaogGui(data.product_id)
			local has_xiaogui = 0
			if is_xiaogui then
				has_xiaogui = EquipWGData.Instance:GetmpGuardInfoByid(data["stuff_id_"..i]) and 1 or 0
			end
			item_count = ItemWGData.Instance:GetItemNumInBagById(data["stuff_id_" .. i]) + has_xiaogui
			if item_count >= data["stuff_count_" .. i] then
				num = num + 1
			end
		end
	end

	local coin = RoleWGData.Instance.role_info.coin or 0
	if num ~= 0 and num >= total_num and coin >= data.coin and self:CheckBeastIsNotActive(data.big_type, data.type, data) then
		return true
	else
		return false
	end
end

function ComposeWGData:IsTianShen(data)
	return data.type == 1 and data.big_type == 3 and data.sub_type == 1
end

function ComposeWGData:IsShenQi(data)
	return data.type == 1 and data.big_type == 3 and data.sub_type == 2
end

--获取侧页签菜单
function ComposeWGData:GetMenu(menu_type)
	if IsEmptyTable(self.menu_list) then
		self:FormatMenu()
	end
	if menu_type == nil then return nil end
	return self.menu_list[menu_type]
end

function ComposeWGData:GetSpecialMenu(menu_type)
	if self.special_menu_list == nil then return nil end
	if menu_type == nil then return nil end
	return self.special_menu_list[menu_type]
end

-- function ComposeWGData:( ... )
-- 	-- body
-- end
-- 检测幻兽是否已激活
local BEAST_BIG_TYPE = 3
local BEAST_MENU_TYPE = 5
local BEAST_COLOR_SP = 6
function ComposeWGData:CheckBeastIsNotActive(big_type, menu_type, cfg)
	if big_type == BEAST_BIG_TYPE and menu_type == BEAST_MENU_TYPE then
		local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(cfg.product_id)
		if not beast_cfg then
			return false
		end

		if beast_cfg.beast_color < BEAST_COLOR_SP or (not ControlBeastsWGData.Instance:CheckBeastTypeIsHaveInBag(beast_cfg.beast_type)) then
			return true
		end

		return false
	end

	return true
end

--将完整得数据排序 并返回
function ComposeWGData:SortChildData(big_type, menu_type, sub_type)
	local cfg  = self.compose_list_cfg
	local tmp_list = {}
	local role_level = RoleWGData.Instance:GetRoleVo().level
	for k,v in pairs(cfg) do
		if v.big_type == big_type and v.type == menu_type and v.sub_type == sub_type and role_level >= v.level then
			-- 检测幻兽展示
			--if self:CheckBeastIsNotActive(big_type, menu_type, v) then
				table.insert(tmp_list, v)
			--end
		end
	end

	table.sort(tmp_list, function(a, b) return a.producd_seq < b.producd_seq end)
	return tmp_list
end

--将只显示足够材料的数据排序 并返回
function ComposeWGData:SortEnoughChildData(big_type, menu_type, sub_type)
	local cfg  = self.compose_list_cfg
	local tmp_list = {}
	local first_parent_value = nil
	local role_level = RoleWGData.Instance:GetRoleVo().level
	for k,v in pairs(cfg) do
		if v.big_type == big_type and v.type == menu_type and v.sub_type == sub_type and role_level >= v.level then
			if self:GetIsCanComposeByDataHasFeixian(v) then
				if first_parent_value == nil then
					first_parent_value = v.sub_type
				end

				-- 检测幻兽展示
				--if self:CheckBeastIsNotActive(big_type, menu_type, v) then
					table.insert(tmp_list, v)
				--end
			end
		end
	end

	table.sort(tmp_list, function(a, b) return a.producd_seq < b.producd_seq end)
	return tmp_list, first_parent_value
end

function ComposeWGData:GetChildData(big_type, menu_type, sub_type)
	if self.is_show_enough then
		return self:SortEnoughChildData(big_type, menu_type,sub_type)
	else
		return self:SortChildData(big_type, menu_type,sub_type)
	end
end

function ComposeWGData:GetParentRemind(big_type, menu_type, parent_index)
	local cfg  = self.compose_list_cfg
	local role_level = RoleWGData.Instance:GetRoleVo().level
	--if menu_type == 2 or menu_type == 3 or menu_type == 6 then return false end					--屏蔽寶石、套裝、守护合成紅點
	--if menu_type == 4 and parent_index == 2 then return false end 				--屏蔽龍紋紅點
	for k,v in pairs(cfg) do
		if v.is_need_remind == 1 and v.big_type == big_type and v.type == menu_type and v.sub_type == parent_index and role_level >= v.level then
			if self:GetIsCanComposeByData(v) and self:CheckBeastIsNotActive(big_type, menu_type, v) then
				return true
			end
		end
	end
	return false
end

function ComposeWGData:SetGoToMenu(menu_type, parent_index)
	self.will_go_menu = menu_type
	self.will_go_parent_index = parent_index
end

function ComposeWGData:GetGoToMenu()
	return self.will_go_menu, self.will_go_parent_index
end

function ComposeWGData:IsHaveBindItem(data)
	for i=1,4 do
		if data["stuff_id_" .. i] then
			local list = ItemWGData.Instance:GetItemListIndex(data["stuff_id_" .. i])
			if not next(list) then
				list = ItemWGData.Instance:GetItemListIndexByStuffBag(data["stuff_id_" .. i])
			end
			for k,v in pairs(list) do
				local stuff_cfg = ItemWGData.Instance:GetGridData(v)
				if stuff_cfg.is_bind == 1 then
					return true
				end
			end
		end
	end
	return false
end

function ComposeWGData:IsHaveBindItemByRingOrBracelet(data)
	for i=1,2 do
		if data["stuff"..i.."_id"] and data["stuff"..i.."_id"] > 0 then
			local list = ItemWGData.Instance:GetItemListIndex(data["stuff"..i.."_id"])
			if not next(list) then
				list = ItemWGData.Instance:GetItemListIndexByStuffBag(data["stuff"..i.."_id"])
			end
			for k,v in pairs(list) do
				local stuff_cfg = ItemWGData.Instance:GetGridData(v)
				if stuff_cfg.is_bind == 1 then
					return true
				end
			end
		end
	end
	return false
end

function ComposeWGData:IsHaveBindItemByEquip(data)
	if data and data.item_id and data.item_id > 0 then
		local list = ItemWGData.Instance:GetItemListIndex(data.item_id)
		if not next(list) then
			list = ItemWGData.Instance:GetItemListIndexByStuffBag(data.item_id)
		end
		for k,v in pairs(list) do
			local stuff_cfg = ItemWGData.Instance:GetGridData(v)
			if stuff_cfg.is_bind == 1 then
				return true
			end
		end
	end
	return false
end

function ComposeWGData:GetTemporaryActivityType()
	local cfg  = ConfigManager.Instance:GetAutoConfig("compose_auto").name_type
	return cfg[1].type
end

function ComposeWGData:GetXianQiShengJieRemind()
	local datalist = EquipmentWGData.Instance:GetXQComposeDataListByType(TabIndex.other_compose_shengjie)
	local is_red = 0
	local list = {}
    if IsEmptyTable(datalist) then
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.New_Compose, 0)
		return 0, list
	end

	for k,v in pairs(datalist) do
		if v.show_remind then
            list = {big_type = 2, type = 1, sub_type = k, index = 0}
			is_red = 1
			break
		end
    end
    if is_red == 1 then
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.New_Compose, 1, function ()
            FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, TabIndex.other_compose_shengjie)
            return true
        end)
    else
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.New_Compose, 0)
    end
	return is_red, list
end

function ComposeWGData:GetXianQiShenXingRemind()
	local is_red = 0
	local list = {}
	local is_open , _ = FunOpen.Instance:GetFunIsOpenedByTabName("other_compose_shengxing")
	if not is_open then
		return 0, list
	end

	local datalist = EquipmentWGData.Instance:GetXQComposeDataListByType(TabIndex.other_compose_shengxing)
	if IsEmptyTable(datalist) then
		return 0, list
	end

	for k,v in pairs(datalist) do
		if v.show_remind then
			list = {big_type = 2, type = 2, sub_type = k, index = 0}
			is_red = 1
			break
		end
	end

	return is_red, list
end


-------------------------------------------------特殊合成------------------------------------------------------

function ComposeWGData:CreateSpecialComposeMenu()
	local compose_menu = ConfigManager.Instance:GetAutoConfig("compose_auto").special_compose_menu
	self.special_menu_list = {}
	local role_level = RoleWGData.Instance:GetRoleVo().level
	local need_level = 0
	for i = 1, #compose_menu do
		local item = compose_menu[i]
		if item then
			local menu = self.special_menu_list[item.type]
			if nil == menu then
				self.special_menu_list[item.type] = {}
				self.special_menu_list[item.type].id = item.type
				self.special_menu_list[item.type].big_type = item.big_type
				self.special_menu_list[item.type].name = item.type_name
				self.special_menu_list[item.type].sub_menu = {}
			end

			need_level = self:GetSubTypeMinOpenLevel(item.big_type, item.type, item.sub_type)
			if role_level >= need_level then
				local temp = {
					id = item.sub_type,
					menu_type = item.type,
					name = item.sub_name,
					big_type = item.big_type,
				}
				table.insert(self.special_menu_list[item.type].sub_menu, temp)
			end
		end
	end
end

function ComposeWGData:GetSpecialComposeMenu()
	if IsEmptyTable(self.special_menu_list) then
		self:CreateSpecialComposeMenu()
	end
	return self.special_menu_list
end

------------------------------------------------装备合成----------------------------------------------------------------

function ComposeWGData:GetIsEnoughStuff(data)
	if data == nil then
		return false
	end

	if data.zhizun == 1 then
		--至尊装备
		local demand_data = EquipmentWGData.Instance:GetZhiZunEquipCfg(data.item_id)
		if demand_data ~= nil then
			local equip_list = EquipmentWGData.Instance:GetHechengZhiZunEquipmentItemList(demand_data)
			if equip_list ~= nil then
				if #equip_list > 0 then
					return #equip_list >= 3
				else
					return false
				end
			end
		end
	elseif data.level then
		local index = ComposeWGCtrl.Instance:GetShowIndex()

		if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			local demand_data = EquipmentWGData.Instance:GetSSEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
			if demand_data ~= nil then
				--local equip_list = EquipmentWGData.Instance:GetSSHechengEquipmentItemList(demand_data)
				local equip_list = ShenShouWGData.Instance:FilterShenShouEq(demand_data.need_qualit, demand_data.need_start_num)
				if equip_list ~= nil then
					if #equip_list>0 then
						return #equip_list >= 3
					else
						return false
					end
				end
			end
		end

	else
		--普通装备
		local can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(data.item_id, data.compose_equip_best_attr_num)
		return can_use_num > 0
	end
end

--获取宝石可合成的
function ComposeWGData:GetCanComposeBaoShi(sub_type)
	local compose_list_config = self.compose_list_cfg
	local num = 0
	local list = {}
	local index = 0

	local function getComposeList()
		for k,v in ipairs(compose_list_config) do
			if v.type == 4 and v.big_type == 4  then
				index = index + 1
				local temp_num = 0
				local stuff_type_num = 0
				local coin = RoleWGData.Instance.role_info.coin or 0
				for i=1,4 do
					if v["stuff_id_" .. i] > 0 then
						stuff_type_num = stuff_type_num + 1
						local count = ItemWGData.Instance:GetItemNumInBagById(v["stuff_id_" .. i])
						if count >= v["stuff_count_" .. i] then
							temp_num = temp_num + 1
						end
					end
				end
				if temp_num == stuff_type_num then
					list = {big_type = v.big_type, type = v.type, sub_type = v.sub_type, index = v.child_type}
					return num + 1, list
				end
			end
		end
	end

	if sub_type then
		num, list = self:GetComposeBaoShiBySubType(sub_type)
		if next(list) == nil then
			getComposeList()
		end
	else
		getComposeList()
	end

	return num, list
end

function ComposeWGData:GetComposeBaoShiBySubType(sub_type)
	local compose_list_config = self.compose_list_cfg
	local num = 0
	local list = {}
	local index = 0
	local type = TabIndex.other_compose_stone % 10
	local big_type = math.floor(TabIndex.other_compose_stone / 10)
	for k,v in ipairs(compose_list_config) do
		if v.type == type and v.big_type == big_type and sub_type == v.sub_type then
			index = index + 1
			local temp_num = 0
			local stuff_type_num = 0
			local coin = RoleWGData.Instance.role_info.coin or 0
			for i=1,4 do
				if v["stuff_id_" .. i] > 0 then
					stuff_type_num = stuff_type_num + 1
					local count = ItemWGData.Instance:GetItemNumInBagById(v["stuff_id_" .. i])
					if count >= v["stuff_count_" .. i] then
						temp_num = temp_num + 1
					end
				end
			end
			if temp_num == stuff_type_num then
				list = {big_type = v.big_type, type = v.type, sub_type = v.sub_type, index = v.child_type}
				return num + 1, list
			end
		end
	end
	return num, list
end

function ComposeWGData.XQSComposeChildData()
	return {producd_seq = 0, product_id = 0,
		product_num = 0, big_type = 0,
		type = 0, sub_type = 0, get_way_id = 0,
		child_type = 0, star_limit = 0,
		stuff_list = {}, stuff_num = 0,
		stuffid_list = {}, stuff_desc = ""}
end

function ComposeWGData:GetXQSComposeChildList(big_type, menu_type, sub_type)
	local empty_table = {}
	local list = ((self.xqs_child_compose_list[big_type] or empty_table)[menu_type] or empty_table)[sub_type]
	if list == nil then
		local child_list = {}
		for k,v in ipairs(self.xianqi_stuff_compose_cfg) do
			if big_type == v.big_type and menu_type == v.type and sub_type == v.sub_type then
				local data = ComposeWGData.XQSComposeChildData()
				for i,j in pairs(v) do
					if data[i] then
						data[i] = j
					end
				end

				local stuff_list = Split(v.stuff_id_list, "|")
				for i,j in ipairs(stuff_list) do
					local stuff_id = tonumber(j)
					data.stuff_list[i] = stuff_id
					data.stuffid_list[stuff_id] = true
				end

				table.insert(child_list, data)
			end
		end

		table.sort(child_list, function(a, b) return a.producd_seq < b.producd_seq end)
		self.xqs_child_compose_list[big_type] = self.xqs_child_compose_list[big_type] or empty_table
		self.xqs_child_compose_list[big_type][menu_type] = self.xqs_child_compose_list[big_type][menu_type] or empty_table
		self.xqs_child_compose_list[big_type][menu_type][sub_type] = child_list
		return child_list
	else
		return list
	end
end

function ComposeWGData:GetXQSComposeChildCfgById(product_id)
	if self.xqs_child_compose_id_list == nil then
		self.xqs_child_compose_id_list = {}
		for k,v in ipairs(self.xianqi_stuff_compose_cfg) do
			local data = ComposeWGData.XQSComposeChildData()
			for i,j in pairs(v) do
				if data[i] then
					data[i] = j
				end
			end

			local stuff_list = Split(v.stuff_id_list, "|")
			for i,j in ipairs(stuff_list) do
				local stuff_id = tonumber(j)
				data.stuff_list[i] = stuff_id
				data.stuffid_list[stuff_id] = true
			end

			self.xqs_child_compose_id_list[v.product_id] = data
		end
	end

	return self.xqs_child_compose_id_list[product_id]
end

-- 获取背包中的合成仙器材料的装备
function ComposeWGData:GetXQSCStuffInBagList(data, already_select_index_list)
	local stuff_list = {}
	if IsEmptyTable(data) then
		return stuff_list
	end

	local child_cfg = self:GetXQSComposeChildCfgById(data.product_id)
	if IsEmptyTable(child_cfg) then
		return stuff_list
	end

	local need_stuff_list = child_cfg.stuffid_list

	already_select_index_list = already_select_index_list or {}
	local bag_item_data = ItemWGData.Instance:GetBagItemDataList()
	for k,v in pairs(bag_item_data) do
		if need_stuff_list[v.item_id]
		and (v.param and v.param.star_level and v.param.star_level == child_cfg.star_limit)
		and not already_select_index_list[v.index] then
			table.insert(stuff_list, v)
		end
	end

	return stuff_list
end

-- 仙器材料 - 大按钮红点
function ComposeWGData:GetXQSCBigBtnRemind(sub_type)
	local big_type = math.floor(TabIndex.other_xianqi_stuff / 10)
	local small_type = TabIndex.other_xianqi_stuff % 10
	local child_list = self:GetXQSComposeChildList(big_type, small_type, sub_type)
	if IsEmptyTable(child_list) then
		return false
	end

	for k,v in ipairs(child_list) do
		if self:GetXQSCSingleRemind(v.product_id) then
			return true
		end
	end

	return false
end

-- 仙器材料 - 小toggle红点
function ComposeWGData:GetXQSCSingleRemind(product_id)
	local child_cfg = self:GetXQSComposeChildCfgById(product_id)
	if IsEmptyTable(child_cfg) then
		return false
	end

	if child_cfg.sub_type == 1 then
		local wear_xianjie_data = EquipWGData.Instance:GetGridData(GameEnum.EQUIP_INDEX_XIANJIE)
		local is_wear_xianjie = wear_xianjie_data ~= nil and wear_xianjie_data.item_id > 0

		local wear_xianzhuo_data = EquipWGData.Instance:GetGridData(GameEnum.EQUIP_INDEX_XIANZHUO)
		local is_wear_xianzhuo = wear_xianzhuo_data ~= nil and wear_xianzhuo_data.item_id > 0
		if not is_wear_xianjie or not is_wear_xianzhuo then
			return false
		end
	end

	local need_num = child_cfg.stuff_num
	local have_num = 0
	local bag_item_data = ItemWGData.Instance:GetBagItemDataList()
	local need_stuff_list = child_cfg.stuffid_list

	for k,v in pairs(bag_item_data) do
		if need_stuff_list[v.item_id]
		and (v.param and v.param.star_level and v.param.star_level == child_cfg.star_limit) then
			have_num = have_num + 1
			if have_num >= need_num then
				return true
			end
		end
	end

	return false
end

-- 仙器材料 - 总红点
function ComposeWGData:GetXQSComposeRemind()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("other_xianqi_stuff")
	if not is_open then
		return 0, list
	end

	for k,v in ipairs(self.xianqi_stuff_compose_cfg) do
		local remind = self:GetXQSCSingleRemind(v.product_id)
		if remind then
			list = {big_type = v.big_type, type = v.type, sub_type = v.sub_type, index = v.child_type}
			return 1, list
		end
	end

	return 0, list
end

function ComposeWGData:RegisterXQSCRemindInBag(remind_name)
	self.xqsc_stuff_list = {}
	for k,v in ipairs(self.xianqi_stuff_compose_cfg) do
		local data = self:GetXQSComposeChildCfgById(v.product_id)
		if data then
			for i,j in pairs(data.stuffid_list) do
				self.xqsc_stuff_list[i] = self.xqsc_stuff_list[i] or {}
				if self.xqsc_stuff_list[i][v.star_limit] == nil then
					self.xqsc_stuff_list[i][v.star_limit] = v
				end
			end
		end
	end

	local item_id_list = {}
	for k,v in pairs(self.xqsc_stuff_list) do
		table.insert(item_id_list, k)
	end

	if not IsEmptyTable(item_id_list) then
		BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
	end
end

function ComposeWGData:GetIsXQSCStuff(data)
	if IsEmptyTable(data) then
		return false
	end

	local item_id = data.item_id
	local star = data.param and data.param.star_level or 0
	local product_data = (self.xqsc_stuff_list[item_id] or {})[star]
	return product_data ~= nil, product_data
end

--神兽合成列表按钮
function ComposeWGData:GetComposeShenShouBigTypeRemind(data)
	for i =1, 2 do
		if data.child and data.child[i] then
			local need_red = self:GetComposeShenShouSmallTypeRemind(data.child[i])
			if need_red then
				return need_red
			end
		end
	end
	return false
end
--神兽合成列表下拉按钮
function ComposeWGData:GetComposeShenShouSmallTypeRemind(data)
	local can_hecheng_item = {}
	local is_batter_list = {}
	local temp_data = {}
	for i = 1, 5 do
		if data["cao"..i] then
			local compose_data = EquipmentWGData.Instance:GetComposeByVItemIdAndStar(data["cao"..i], data.compose_equip_best_attr_num)
			if compose_data then
				if compose_data.is_need_item == 1 and compose_data.item_id > 0 and compose_data.item_num > 0 then
					local has_num = ItemWGData.Instance:GetItemNumInBagById(compose_data.item_id)
		
					if has_num < compose_data.item_num then
						break
					end
				end
			end

			can_hecheng_item = ShenShouWGData.Instance:FilterShenShouEq(data.need_qualit, data.need_start_num)
			local need_red = false
			if #can_hecheng_item >= 3 then
				need_red = true
				for t,q in pairs(can_hecheng_item) do
					if is_batter_list[q.index] and is_batter_list[q.index] == 1 then
						need_red = false
						break
					end
					if ShenShouWGData.Instance:GetShenShouEquipHaveUpFlag(q) then
						is_batter_list[q.index] = 1
						need_red = false
						break
					else
						is_batter_list[q.index] = 0
					end
				end
				if need_red then
					temp_data.star_count = data.compose_equip_best_attr_num
					temp_data.item_id = data["cao"..i]
					if ShenShouWGData.Instance:GetComposeProductUpFlag(temp_data) then
						return true
					end
				end
			end
		end
	end
	return false
end

--神兽单一合成公式红点
function ComposeWGData:GetComposeShenShouComposeWayRemind(data)
	local compose_data = EquipmentWGData.Instance:GetComposeByVItemIdAndStar(data.item_id, data.compose_equip_best_attr_num)
	if compose_data then
		if compose_data.is_need_item == 1 and compose_data.item_id > 0 and compose_data.item_num > 0 then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(compose_data.item_id)

			if has_num < compose_data.item_num then
				return false
			end
		end
	end

	local can_hecheng_item = {}
	local is_batter_list = {}
	local temp_data = {}
	if data and data.item_id then
		can_hecheng_item = ShenShouWGData.Instance:FilterShenShouEq(data.need_qualit, data.need_start_num)
		local need_red = false
		if #can_hecheng_item >= 3 then
			need_red = true
			for t,q in pairs(can_hecheng_item) do
				if is_batter_list[q.index] and is_batter_list[q.index] == 1 then
					need_red = false
					break
				end
				if ShenShouWGData.Instance:GetShenShouEquipHaveUpFlag(q) then
					is_batter_list[q.index] = 1
					need_red = false
					break
				else
					is_batter_list[q.index] = 0
				end
			end

			if need_red then
				temp_data.star_count = data.compose_equip_best_attr_num
				temp_data.item_id = data.item_id
				if ShenShouWGData.Instance:GetComposeProductUpFlag(temp_data) then
					return true
				end
			end
		end
	end
	
	return false
end