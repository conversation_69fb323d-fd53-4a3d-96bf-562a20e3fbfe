MainUIEfficientyBtn = MainUIEfficientyBtn or BaseClass(BaseRender)
--主界面效率按钮 与经验本冲突

function MainUIEfficientyBtn:__init()
	self.node_list["btn_efficiency"].button:AddClickListener(BindTool.Bind(self.OnClickOpenEfficiencyView, self))
	self.node_list["btn_efficiency"]:SetActive(false)
    self.effect_change = GlobalEventSystem:Bind(ObjectEventType.FIGHT_EFFECT_CHANGE, BindTool.Bind(self.OnEffectChange, self))
    self:FlushEffecticiency()
end

function MainUIEfficientyBtn:FlushEffecticiency()
    if self.node_list and self.node_list.btn_efficiency then
        local fb_cfg = FuBenWGData.GetFbSceneConfig(Scene.Instance:GetSceneType())
        local is_show_efficient = fb_cfg and fb_cfg.is_show_efficient == 1
        self.node_list["btn_efficiency"]:SetActive(is_show_efficient)
    end
end

function MainUIEfficientyBtn:OnEffectChange()
	self:Flush()
end

function MainUIEfficientyBtn:OnClickOpenEfficiencyView()
	FuBenWGCtrl.Instance:OpenTeamExpMdeicineView()
end

function MainUIEfficientyBtn:__delete()
    if self.effect_change then
		GlobalEventSystem:UnBind(self.effect_change)
		self.effect_change = nil
	end

	self.trigger_times = nil
	self:RemoveDelayTimer()
end

function MainUIEfficientyBtn:OnFlush()
    local add_exp = 0
	local exp_buff = FightWGData.Instance:GetExpBuff()
	if exp_buff then
		add_exp = exp_buff.param_list[3] / 100
	end
    if add_exp > 0 then
		self.node_list.jingYan_bg.gameObject:SetActive(true)
		self.trigger_times = 0
		self:SetShakeStatus(false)
		self.node_list["xiaolv_text"].text.text = string.format(Language.ExpAddition.VipAdd, add_exp)
		self.node_list.can_up_img:SetActive(false)
	else
		self.node_list.jingYan_bg.gameObject:SetActive(false)
		self.node_list.can_up_img:SetActive(true)
		self.trigger_times = 0
		self:SetShakeStatus(true)
    end
end

-- 修改动画状态机状态
function MainUIEfficientyBtn:SetShakeStatus(shake_status)
	if not self.node_list then
		return
	end

	local is_active = self.node_list["btn_efficiency_icon"]:GetActive()
	
	if (not is_active) then
		if self.trigger_times > 0 then
			return
		end

		-- 尝试延迟调用
		self.trigger_times = self.trigger_times + 1
		self:RemoveDelayTimer()
		self.show_effect_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self:RemoveDelayTimer()
			self:SetShakeStatus(shake_status)
		end, 0.1)
	else
		self.node_list["btn_efficiency_icon"].animator:SetBool("shake", shake_status)	
	end
end

--移除回调
function MainUIEfficientyBtn:RemoveDelayTimer()
    if self.show_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_delay_timer)
        self.show_delay_timer = nil
    end
end