SubPanelView = SubPanelView or BaseClass()

local _tinsert = table.insert

-- 子面板
function SubPanelView:__init(instance)
	self.view = nil
	self.global_event_map = {}
	self.flush_param_t = nil								-- 界面刷新参数
	self.has_load = nil
	self.active = true

	if instance ~= nil then
		self:SetInstance(instance)
	end
end

function SubPanelView:__delete()
	if not IsNil(self.event_table) then
		self.event_table:ClearAllEvents()
		self.event_table = nil
	end

	for k, _ in pairs(self.global_event_map) do
		GlobalEventSystem:UnBind(k)
	end
	self.global_event_map = {}

	self.node_list = nil

	self.name_table = nil
	self.variable_table = nil
	self.view = nil
	self.has_load = nil
end

function SubPanelView:GetView()
	-- body
	return self.view
end

function SubPanelView:LoadAsset(bundle_name, asset_name, parent, callback)
	self.async_loader = AllocSyncLoader(self, "base_asset_loader")
	self.async_loader:SetParent(parent)
	self.async_loader:Load(bundle_name, asset_name, function(obj)
		if IsNil(obj) then
			return
		end
		-- 这里不用处理BaseRender被delete了的情况
		-- 因为被delete后，不会调用这个回调函数了
		self:SetInstance(obj)
		if nil ~= callback then
			callback(obj)
		end
	end)
end

function SubPanelView:SetInstance(instance)
	-- UI根节点, 支持instance是GameObject或者U3DObject
	if type(instance) == "userdata" then
		self.view = U3DObject(instance)
	elseif (type(instance) == "table" and not instance.gameObject) or type(instance) == "boolean" then
		return
	else
		self.view = instance
	end

	self.name_table = instance:GetComponent(typeof(UINameTable))			-- 名字绑定

	self.node_list = U3DNodeList(self.name_table, self)
	self:LoadCallBack(instance)
	if not self.active then
		self:SetActive(self.active)
	end
	self.has_load = true
	self:FlushHelper()
end

function SubPanelView:SetInstanceParent(instance_parent)
	self.view.transform:SetParent(instance_parent.transform, false)
end

function SubPanelView:SetLocalPosition(x,y,z)
	self.view.transform:SetLocalPosition(x,y,z)
end

-- 外部通知刷新，调用此接口
function SubPanelView:Flush(key, value_t)
	key = key or "all"
	value_t = value_t or {"all"}

	self.flush_param_t = self.flush_param_t or {}
	for k, v in pairs(value_t) do
		self.flush_param_t[key] = self.flush_param_t[key] or {}
		self.flush_param_t[key][k] = v
	end

	if nil == self.delay_flush_timer and self.view ~= nil then
		self.delay_flush_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.FlushHelper, self), 0)
	end
end

function SubPanelView:FlushHelper()
	self:CancelDelayFlushTimer()

	if self.view == nil then
		return
	end
	if not self.has_load then
		return
	end

	if nil ~= self.flush_param_t then
		local param_list = self.flush_param_t
		self.flush_param_t = nil
		self:OnFlush(param_list)
		if self.need_change then
			self.need_change = false
			self:OnSelectChange(self.is_select)
		end
	end
end

function SubPanelView:CancelDelayFlushTimer()
	if self.delay_flush_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_flush_timer)
		self.delay_flush_timer = nil
	end
end

-- 查找组件
-- name_path 对象名，支持name/name/name的形式
function SubPanelView:FindObj(name_path)
	local node

	if self.name_table ~= nil then
		local game_obj = self.name_table:Find(name_path)
		if game_obj ~= nil then
			node = U3DObject(game_obj)
			return node
		end
	end

	local transform = self.view.transform:FindHard(name_path)
	if transform ~= nil then
		node = U3DObject(transform.gameObject, transform)
		return node
	end

	print_error("SubPanelView: can not find: " .. name_path)
	return nil
end

function SubPanelView:LoadSprite(bundle_name, asset_name, callback, cbdata)
	LoadSprite(self, bundle_name, asset_name, callback, cbdata)
end

function SubPanelView:LoadSpriteAsync(bundle_name, asset_name, callback, cbdata)
	LoadSpriteAsync(self, bundle_name, asset_name, callback, cbdata)
end

function SubPanelView:LoadRawImage(arg0, arg1, arg2)
	LoadRawImage(self, arg0, arg1, arg2)
end

function SubPanelView:SetActive(active)
	if self.view and not IsNil(self.view.gameObject) then
		self.view.gameObject:SetActive(active)
	end
	self.active = active
end

function SubPanelView:SetParentActive(active)
	if self.view and not IsNil(self.view.gameObject) then
		self.view.transform.parent.gameObject:SetActive(active)
	end
end

function SubPanelView:IsNil()
	if nil == self.view or nil == self.view.gameObject then
		return true
	end

	return IsNil(self.view.gameObject)
	-- return IsNil(self.view and self.view.gameObject)
end

-- 是否打开过
function SubPanelView:IsOpen()
	return self.view and true or false
end

function SubPanelView:BindGlobalEvent(event_id, event_func)
	local handle = GlobalEventSystem:Bind(event_id, event_func)
	self.global_event_map[handle] = event_id
	return handle
end

function SubPanelView:UnBindGlobalEvent(handle)
	GlobalEventSystem:UnBind(handle)
	self.global_event_map[handle] = nil
end

----------------------------------------------------
-- 可重写继承的接口 begin
----------------------------------------------------
function SubPanelView:LoadCallBack(instance)
	-- override
end

-- 刷新(用Flush刷新OnFlush的方法必须是有用LoadCallBack加载完成的时候使用,否则有可能引起报错)
function SubPanelView:OnFlush(param_list)

end


----------------------------------------------------
-- 可重写继承的接口 end
----------------------------------------------------
