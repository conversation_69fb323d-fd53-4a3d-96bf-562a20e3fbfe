﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;

[RequireComponent(typeof(TMP_Text))]
public class TMPTextLinkOpener : MonoBehaviour, IPointerClickHandler
{
    private TMP_Text m_Text;
    private Action<string> m_Callback;
    private Dictionary<int, Action<float, float>> callBackDir = new Dictionary<int, Action<float, float>>();

    /// <remarks>
    /// Returns a non-destroyed instance or a null reference.
    /// </remarks>
    [NonSerialized] private Canvas m_Canvas;
    private Canvas Canvas
    {
        get
        {
            if (m_Canvas == null)
            {
                var list = ListPool<Canvas>.Get();
                gameObject.GetComponentsInParent(false, list);
                if (list.Count > 0)
                {
                    m_Canvas = list[list.Count - 1];
                }
                else
                {
                    m_Canvas = null;
                }

                ListPool<Canvas>.Release(list);
            }

            return m_Canvas;
        }
    }

    private void Awake()
    {
        m_Text = GetComponent<TMP_Text>();
    }

    //public void AddClickListener(Action<string> cb)
    //{
    //    m_Callback = cb;
    //}

    public void AddClickListener(Action<float, float> action, int index)
    {
        callBackDir[index] = action;
    }

    public void ClearSubscription()
    {
        m_Callback = null;
    }

    public void OnPointerClick(PointerEventData event_data)
    {
        Camera camera = null;
        if (Canvas.renderMode != RenderMode.ScreenSpaceOverlay)
        {
            camera = Canvas.worldCamera;
        }

        var link_idx = TMP_TextUtilities.FindIntersectingLink(m_Text, event_data.position, camera);
        if (link_idx != -1)
        {
            var link = m_Text.textInfo.linkInfo[link_idx];
            var linkId = link.GetLinkID();
            //Debug.Log("linkId:"+ linkId);
            //m_Callback.Invoke(link.GetLinkID());
            if(int.TryParse(linkId,out int index))
            {
                Vector2 pressPosition = event_data.pressPosition;
                ButtonClick(index, pressPosition);
            }
            
        }
    }

    private void ButtonClick(int index, Vector2 pressPosition)
    {
        Action<float, float> action;
        if (callBackDir.TryGetValue(index, out action))
        {
            if (null != action)
            {
                try
                {
                    action(pressPosition.x, pressPosition.y);
                }
                catch (Exception exp)
                {
                    Debug.LogError(exp.Message);
                }
            }
        }
    }

    public void Clear()
    {
        callBackDir.Clear();
    }
}
