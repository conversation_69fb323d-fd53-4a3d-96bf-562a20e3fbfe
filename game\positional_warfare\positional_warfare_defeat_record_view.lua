-- 击败记录界面
PositionalWarfareDefeatRecordView = PositionalWarfareDefeatRecordView or BaseClass(SafeBaseView)

function PositionalWarfareDefeatRecordView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 594)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_defeat_record_view")
end

function PositionalWarfareDefeatRecordView:SetDataAndOpen(data)
	self.show_data = data

	if self.show_data ~= nil then
        PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.MONSTER_RECORD, self.show_data.land_seq, self.show_data.seq)

		if not self:IsOpen() then
			self:Open()
		else
			self:Flush()
		end
	end
end

function PositionalWarfareDefeatRecordView:LoadCallBack()
    if not self.defeat_data_list then
        self.defeat_data_list = AsyncListView.New(PWDefeatRecordItemCellRender, self.node_list.defeat_data_list)
        self.defeat_data_list:SetStartZeroIndex(false)
    end

    self.node_list.title_view_name.text.text = Language.PositionalWarfare.DefeatRecordView
end

function PositionalWarfareDefeatRecordView:ReleaseCallBack()
    if self.defeat_data_list then
        self.defeat_data_list:DeleteMe()
        self.defeat_data_list = nil
    end
end

function PositionalWarfareDefeatRecordView:OnFlush()
    local defeat_data_list = PositionalWarfareWGData.Instance:GetMOnsterDefeatRecordDataList(self.show_data.land_seq, self.show_data.seq)
    -- defeat_data_list = {[1] = {kill_camp = 1,time = TimeWGCtrl.Instance:GetServerTime(), land_seq = 1, monster_seq = 1}}
    self.defeat_data_list:SetDataList(defeat_data_list)
    self.node_list.no_data:CustomSetActive(IsEmptyTable(defeat_data_list))
end

----------------------------------PWDefeatRecordItemCellRender-------------------------------------
PWDefeatRecordItemCellRender = PWDefeatRecordItemCellRender or BaseClass(BaseRender)

function PWDefeatRecordItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local camp_cfg =  PositionalWarfareWGData.Instance:GetCurCampCfg(self.data.kill_camp)
    local time = TimeUtil.FormatSecond2MYHM1(self.data.time)
    local monster_cfg = PositionalWarfareWGData.Instance:GetCurMonsterCfg(self.data.land_seq, self.data.monster_seq)
    local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(monster_cfg.monster_id)
    self.node_list.desc_content.text.text = string.format(Language.PositionalWarfare.DefeatRecordDesc, time, camp_cfg.camp_name, boss_cfg.name)
end