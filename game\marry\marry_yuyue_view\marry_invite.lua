MarryInviteView = MarryInviteView or BaseClass(SafeBaseView)

local FRIEND = 1 		-- 好友列表
local FACTION = 2 		-- 帮派列表
local APPLICANT = 3 	-- 申请列表
local INVITED = 4       -- 已申请表

function MarryInviteView:__init()
	self.view_layer = UiLayer.Pop
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_invite_guests")
	--self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_bg3")

	self.applicant_select = 0 --申请者专用
end

function MarryInviteView:__delete()

end

function MarryInviteView:ReleaseCallBack()
	-- if self.friends_list_view then
	-- 	self.friends_list_view:DeleteMe()
	-- 	self.friends_list_view = nil
	-- end
	-- if self.guild_list_view then
	-- 	self.guild_list_view:DeleteMe()
	-- 	self.guild_list_view = nil
	-- end
	-- if self.guests_list_view then
	-- 	self.guests_list_view:DeleteMe()
	-- 	self.guests_list_view = nil
	-- end
	-- if self.applicant_list_view then
	-- 	self.applicant_list_view:DeleteMe()
	-- 	self.applicant_list_view = nil
	-- end
	if self.add_guests_count then
  		self.add_guests_count:DeleteMe()
   		self.add_guests_count = nil
    end

	if self.invite_friends_list_view then
		self.invite_friends_list_view:DeleteMe()
		self.invite_friends_list_view = nil
	end

	if self.already_invite_friends_list_view then
		self.already_invite_friends_list_view:DeleteMe()
		self.already_invite_friends_list_view = nil
	end
end

function MarryInviteView:LoadCallBack()
	self.select_type = 1
	-- self:CreateGuestsList()
	-- self:CreateLeftList()
	self:CreateInviteFriendList()
	self:CreateAlreadyInviteFriendList()
	-- self:CreateInviteFriendList()
	-- self:CreateInviteGuildList()
	-- self:CreateApplicantList()
	
	self.node_list["img_remind"]:SetActive(false)
    XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind(self.Close, self))
	XUI.AddClickEventListener(self.node_list["tag_friend"], BindTool.Bind2(self.OnBtnInviteGuestsState, self, 1))
	XUI.AddClickEventListener(self.node_list["tag_guild"], BindTool.Bind2(self.OnBtnInviteGuestsState, self, 2))
	XUI.AddClickEventListener(self.node_list["tag_applicant"], BindTool.Bind2(self.OnBtnInviteGuestsState, self, 3))
	--XUI.AddClickEventListener(self.node_list["tag_invited"], BindTool.Bind2(self.OnBtnInviteGuestsState, self, 4))

	self.node_list["btn_add_guests"].button:AddClickListener(BindTool.Bind(self.OnClickAddGuestsHandler, self))
	--self.node_list["btn_sure"].button:AddClickListener(BindTool.Bind(self.OnClickSureYaoQing, self))
end

function MarryInviteView:ShowIndexCallBack(index)
	SocietyWGCtrl.Instance:SendFriendInfoReq()
	GuildWGCtrl.Instance:GetAllGuildData()
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_GET_YUYUE_INFO)
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.HUNYAN_GET_APPLICANT_INFO, 0, 0)
	--婚宴描述
	local str = MarryWGData.Instance:GetShowYuYueTime(self.seq)
	local free_invite_num = MarryWGData.Instance:GetCanFreeInviteNum()
	self.node_list["invite_des"].text.text = string.format(Language.Marry.MarryInviteDes,str,free_invite_num)
end

--重写关闭按钮的方法
function MarryInviteView:OnClickCloseWindow()
	local want_yaoqing_binke = MarryWGData.Instance:GetYaoQingBinKe()
	local data_num = 0

	if want_yaoqing_binke and not IsEmptyTable(want_yaoqing_binke) then
		data_num = 1
	end

	if data_num == 0 then
		MarryWGData.Instance:SetYaoQingBinKe()
		self:Close()
		return
	end

	local id_list = {}
	for k,v in pairs(want_yaoqing_binke) do
		local role_id = v.user_id or v.uid
		id_list[data_num] = role_id
		data_num = data_num + 1
	end

	local hunyan_num = MarryWGData.Instance:GetHunYanNum(self.seq)
	local guests_data = MarryWGData.Instance:GetInviteGuestsByWeddingSequence(hunyan_num)
	local has_num = 0 
	if guests_data then
		has_num = guests_data.can_num - guests_data.has_num 	--剩余邀请人数
	end

	if has_num <= 0 or #id_list == 0 then
		MarryWGData.Instance:SetYaoQingBinKe()
		self:Close()
		return
	end

	MarryWGCtrl.Instance:SendYaoQingBingKeReq(self.seq, #id_list, id_list)
	MarryWGData.Instance:SetYaoQingBinKe()
	self:Close()
end

function MarryInviteView:CreateInviteFriendList()
	--[[self.invite_friends_list_view = AsyncBaseGrid.New()
	local bundle = "uis/view/marry_ui_prefab"
	local asset = "invite_friend_item"
	self.invite_friends_list_view:CreateCells({col = 1, change_cells_num = 1, list_view = self.node_list["ph_invite_list"],
	assetBundle = bundle, assetName = asset, itemRender = InviteFriendItemRender})
	--self.tiqin_friend_list:SetSelectCallBack(BindTool.Bind1(self.OnClickFriendRenderCalllBack, self))
	self.invite_friends_list_view:SetStartZeroIndex(false)
	]]
	self.invite_friends_list_view = AsyncListView.New(InviteFriendItemRender, self.node_list.ph_invite_list)
	self.invite_friends_list_view:SetStartZeroIndex(false)
end

function MarryInviteView:CreateAlreadyInviteFriendList()
	--self.already_invite_friends_list_view = AsyncBaseGrid.New()
	self.already_invite_friends_list_view = AsyncListView.New(InviteBinKeItemRender, self.node_list.ph_invite_list_right)
	self.already_invite_friends_list_view:SetStartZeroIndex(false)
end

function MarryInviteView:OnFlush()
	-- local guests_data = MarryWGData.Instance:GetInviteGuests() 
	local hunyan_num = MarryWGData.Instance:GetHunYanNum(self.seq)
	local guests_data = MarryWGData.Instance:GetInviteGuestsByWeddingSequence(hunyan_num)
    if not IsEmptyTable(guests_data) then
        local data_list = {}
		data_list = self:YaoQingListInfo(guests_data.data)
		local applincant_data_num = MarryWGData.Instance:GetWeddingApplicantRed(self.seq)
		self.node_list["img_remind"]:SetActive(applincant_data_num > 0)
		local applincant_data = MarryWGData.Instance:GetWeddingApplicantInfo(self.seq)
        if self.select_type == FRIEND then
			self:FlushFriendListData(guests_data.data)
        elseif self.select_type == FACTION then
			self:FlushGuildListData(guests_data.data)
		elseif self.select_type == APPLICANT then
			local copy_applincant_data = {}
			if applincant_data and not IsEmptyTable(applincant_data) then
				for k,v in pairs(applincant_data) do
					local is_enemy = MarryWGData.Instance:IsEnemy(v)
					v.is_enemy = is_enemy
					v.list_type = APPLICANT
					table.insert(copy_applincant_data,v)
				end

				if not IsEmptyTable(copy_applincant_data) then
					table.sort(copy_applincant_data, SortTools.KeyMarrySorters("intimacy","is_enemy", "capability"))
                end	
                self:FlushApplicantListData(copy_applincant_data)
            else
                self:FlushApplicantListData({})
			end
		end
		--刷新已邀请的宾客
		self:FlushAlreadyInviteListData(data_list)
		--显示邀请人数和总人数
		if self.node_list.ph_num then
			local has_num = guests_data.can_num - guests_data.has_num 	--剩余邀请人数
			local can_num = guests_data.can_num							--总共可以邀请的人数
			self.node_list.ph_num.text.text = string.format(Language.Marry.AlreadyInvitenum,has_num,can_num)
		end

		if self.invite_friends_list_view then
			self.node_list["tag_guild_hl"]:SetActive(self.select_type == FACTION)
			self.node_list["tag_friend_hl"]:SetActive(self.select_type == FRIEND)
			self.node_list["tag_qpplicant_hl"]:SetActive(self.select_type == APPLICANT)
			--self.node_list["tag_invited_hl"]:SetActive(self.select_type == INVITED)
			--self.node_list["btn_sure"]:SetActive(self.select_type ~= INVITED)
		end
	end
	--local want_yaoqing_binke = MarryWGData.Instance:GetYaoQingBinKe() or {}
	--XUI.SetButtonEnabled(self.node_list["btn_sure"], not IsEmptyTable(want_yaoqing_binke))
end

--设置宾客名单
function MarryInviteView:YaoQingListInfo(data)
	local data_list = {}
	if data and not IsEmptyTable(data) then
		for k,v in pairs(data) do
			v.is_from = 0 --来自协议数据
			local is_enemy = MarryWGData.Instance:IsEnemy(v)
			v.is_enemy = is_enemy
			table.insert(data_list,v)
		end
    end
 --    local want_yaoqing_binke = MarryWGData.Instance:GetYaoQingBinKe()
 --    if want_yaoqing_binke and not IsEmptyTable(want_yaoqing_binke) then
 --        --移除已经邀请了的
 --        for k2,v2 in pairs(data_list) do
 --            if want_yaoqing_binke[v2.user_id] then
 --                MarryWGData.Instance:RemoveYaoQing(want_yaoqing_binke[v2.user_id])           
 --            end
 --        end

	-- 	for k,v in pairs(want_yaoqing_binke) do
	-- 		v.is_from = 1 --来自自己添加
	-- 		local is_enemy = MarryWGData.Instance:IsEnemy(v)
	-- 		v.is_enemy = is_enemy
	-- 		local id = v.user_id or v.uid
	-- 		local intimacy = MarryWGData.Instance:GetAndRoleIntimacy(id)
	-- 		v.intimacy = intimacy
	-- 		table.insert(data_list,v)
	-- 	end
	-- end
	if data_list and not IsEmptyTable(data_list) then
		table.sort(data_list, SortTools.KeyMarryTwoSorters("is_from","intimacy","is_enemy", "capability"))
	end
	return data_list
end

--刷新已邀请者列表
function MarryInviteView:FlushAlreadyInviteListData(data)
	if self.already_invite_friends_list_view then
		self.already_invite_friends_list_view:SetDataList(data)
		self.node_list["friends_hint_right"].text.text = ""
		self.node_list.invite_nodata_right:SetActive(#data == 0)
		if #data == 0 then
			self.node_list["friends_hint_right"].text.text = Language.Marry.NoCanYaoQingHint[INVITED]
		end
	end

	-- if self.friends_list_view then
	-- 	--self.friends_list_view:SetDataList(data,0)
	-- end
end

--刷新邀请好友或仙盟或申请者列表
function MarryInviteView:FlushApplicantListData(data)
	if self.invite_friends_list_view then
		for key, value in pairs(data) do
			value["tab_select_type"] = self.select_type
		end
		self.invite_friends_list_view:SetDataList(data)
		self.node_list["friends_hint"].text.text = ""
		self.node_list.invite_nodata:SetActive(#data == 0)
		if #data == 0 then
			if self.select_type == FACTION then
				local guild_id = RoleWGData.Instance.role_vo.guild_id
				local hint_index = guild_id > 0 and 1 or 0
				self.node_list["friends_hint"].text.text = Language.Marry.NoCanYaoQingHint[self.select_type][hint_index]
			else
				self.node_list["friends_hint"].text.text = Language.Marry.NoCanYaoQingHint[self.select_type]
			end
		end
	end

	-- if self.friends_list_view then
	-- 	--self.friends_list_view:SetDataList(data,0)
	-- end
end

--刷新好友列表
function MarryInviteView:FlushFriendListData(data)
	local friend_list = SocietyWGData.Instance:GetFriendList()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid
	local friend_data = {}
	for k,v in pairs(friend_list) do
		if v.is_online == 1 and lover_id ~= v.user_id then
			v.list_type = FRIEND
			--仇人标记 0是1不是
			local is_enemy = MarryWGData.Instance:IsEnemy(v)
			v.is_enemy = is_enemy
			table.insert(friend_data, v)
		end
    end
	--不在已邀请名单中
	if data and not IsEmptyTable(data) then
		for k = #friend_data, 1, -1 do
			for k2,v2 in pairs(data) do
				if friend_data[k] then
					if friend_data[k].user_id == v2.user_id then
						table.remove(friend_data, k)
					end
				end
			end
		end
	end

	--不在准备邀请名单之中
	local friend_data_list = {}
    -- local want_yaoqing_binke = MarryWGData.Instance:GetYaoQingBinKe()
	-- if want_yaoqing_binke and not IsEmptyTable(want_yaoqing_binke) then
	-- 	for k,v in pairs(friend_data) do
	-- 		if nil == want_yaoqing_binke[v.user_id] then
	-- 			table.insert(friend_data_list, v)
	-- 		end
	-- 	end
	-- else
		friend_data_list = friend_data
	-- end
    for k, v in pairs(friend_data_list) do
        if v.user_id == lover_id or v.user_id == RoleWGData.Instance:InCrossGetOriginUid() then
			table.remove(friend_data_list, k)
		end
    end
	if not IsEmptyTable(friend_data_list) then
		table.sort(friend_data_list, SortTools.KeyMarrySorters("intimacy","is_enemy", "capability"))
	end

    if self.invite_friends_list_view then
		-- self.friends_list_view:SetData(friend_data,0)
		self:FlushApplicantListData(friend_data_list)
	end
end

--刷新帮派列表
function MarryInviteView:FlushGuildListData(data)
	local guild_list = GuildDataConst.GUILD_MEMBER_LIST.list
	local lover_id = RoleWGData.Instance.role_vo.lover_uid
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local guild_data = {}
	for k,v in pairs(guild_list) do
		if v.uid ~= role_id and v.is_online == 1 and v.uid ~= lover_id then
			v.list_type = FACTION
			local is_enemy = MarryWGData.Instance:IsEnemy(v)
			v.is_enemy = is_enemy
			local intimacy = MarryWGData.Instance:GetAndRoleIntimacy(v.uid)
			v.intimacy = intimacy
			table.insert(guild_data, v)
		end
	end

	if data and next(data) then
		for k = #guild_data, 1, -1 do
			for k2,v2 in pairs(data) do
				if guild_data[k] then
					if guild_data[k].uid == v2.user_id then
						table.remove(guild_data, k)
					end
				end
			end
		end
	end

		--不在准备邀请名单之中
	local guild_data_list = {}
	-- local want_yaoqing_binke = MarryWGData.Instance:GetYaoQingBinKe()

	-- if want_yaoqing_binke and not IsEmptyTable(want_yaoqing_binke) then
	-- 	for k,v in pairs(guild_data) do
	-- 		if nil == want_yaoqing_binke[v.uid] then
	-- 			table.insert(guild_data_list, v)
	-- 		end
	-- 	end
	-- else
		guild_data_list = guild_data
    --end
    
    for k, v in pairs(guild_data_list) do
        if v.uid == role_id or v.uid == lover_id then
			table.remove(guild_data, k)
		end
    end
	if not IsEmptyTable(guild_data_list) then
		table.sort(guild_data_list, SortTools.KeyMarrySorters("intimacy","is_enemy", "capability"))
	end
	
	if self.invite_friends_list_view then
        -- self.friends_list_view:SetData(guild_data,0)    
		self:FlushApplicantListData(guild_data_list)
	end
end

function MarryInviteView:OnBtnInviteGuestsState(index)
	self.select_type = index
	if self.select_type == FRIEND then
		SocietyWGCtrl.Instance:SendFriendInfoReq()
	elseif self.select_type == FACTION then
		GuildWGCtrl.Instance:GetAllGuildData()
	end
	MarryWGData.Instance:SetYaoQingBinKe()
	self:Flush()
end

function MarryInviteView:OnClickAddGuestsHandler()
	if nil == self.add_guests_count then
		self.add_guests_count = Alert.New(nil, nil, nil, nil, true)
		self.add_guests_count:SetCheckBoxDefaultSelect(false)
	end

	local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
	local str = string.format(Language.Marry.AddCountTips, other_cfg.wedding_buy_guest_price)
	self.add_guests_count:SetLableString(str)
	self.add_guests_count:SetOkFunc(BindTool.Bind2(self.OnClickGuestsCountOk, self))
	self.add_guests_count:Open()
end

function MarryInviteView:OnClickGuestsCountOk()
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_BUY_GUEST_NUM,self.seq)
end

--[[function MarryInviteView:OnClickSureYaoQing()
    local want_yaoqing_binke = MarryWGData.Instance:GetYaoQingBinKe()
    if IsEmptyTable(want_yaoqing_binke) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.NoGuestCanInvite)
        return
    end
	local data_num = 1
	local id_list = {}
	if want_yaoqing_binke and not IsEmptyTable(want_yaoqing_binke) then
		for k,v in pairs(want_yaoqing_binke) do
			local role_id = v.user_id or v.uid
			id_list[data_num] = role_id
			data_num = data_num + 1
		end
	end
		
	local hunyan_num = MarryWGData.Instance:GetHunYanNum(self.seq)
	local guests_data = MarryWGData.Instance:GetInviteGuestsByWeddingSequence(hunyan_num)
	local has_num = 0 
	if guests_data then
		has_num = guests_data.can_num - guests_data.has_num 	--剩余邀请人数
	end
	if has_num <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YaoQingNoSucess)
		return
	-- else
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YaoQingSucess)
	end
	MarryWGCtrl.Instance:SendYaoQingBingKeReq(self.seq, #id_list, id_list)
	MarryWGData.Instance:SetYaoQingBinKe()
end
]]

function MarryInviteView:SetMarryDataInfo(seq)
    self.seq = seq or MarryWGData.Instance:GetInviteGuestsByWeddingSequence().wedding_yuyue_seq
    MarryWGData.Instance:SetCurInviteSeq(self.seq)
	self:Open()
end

--------------------------------------------------邀请好友列表----------------------------------------------------------------
InviteFriendItemRender = InviteFriendItemRender or BaseClass(BaseRender)

function InviteFriendItemRender:__init()
end

function InviteFriendItemRender:__delete()
	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

function InviteFriendItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["true_btn"], BindTool.Bind(self.OnInvitebtn, self))
	XUI.AddClickEventListener(self.node_list["false_btn"], BindTool.Bind(self.OnDisAgreedApplaybtn, self))
	XUI.AddClickEventListener(self.node_list["invite_btn"], BindTool.Bind(self.OnInvitebtn, self))
end

function InviteFriendItemRender:SetData(data)
	self.data = data
	self.tab_select_type = data and data.tab_select_type
	if self.has_load or self.is_use_objpool then
		self:OnFlush()
	else
		self:Flush()
	end
end

function InviteFriendItemRender:OnFlush()
	if not self.data then
		return
	end
	local user_id = self.data.user_id or self.data.uid
	local flush_fun = function (protocol)
		if not self.node_list then
			return
		end

		local roledata = {is_online = protocol.is_online ,guild_id = protocol.guild_id}
		if roledata.is_online == 1 then
			self.node_list["isonline"].text.text = Language.Marry.OnLine
		else
			self.node_list["isonline"].text.text = Language.Marry.NoOnLine
		end
	end
	BrowseWGCtrl.Instance:BrowRoelInfo(user_id, flush_fun, RoleWGData.Instance.role_vo.plat_type)
	
	local is_applicant = self.tab_select_type == APPLICANT
	self.node_list["isonline_right"]:SetActive(false)
	self.node_list["invite_btn"]:SetActive(not is_applicant)
	self.node_list["select_invite"]:SetActive(is_applicant)
	
	--local in_inviteList = MarryWGData.Instance:IsInYaoQingBinKeList(self.data)
	--self.node_list["select_gou"]:SetActive(in_inviteList)
	self.node_list["role_name"].text.text = self.data.name or self.data.gamename or self.data.role_name
	self:FlushHeadIcon()

end

function InviteFriendItemRender:FlushHeadIcon()
	local role_info_call_back = function(protocol)
		if IsEmptyTable(protocol) or not self.node_list then
			return
		end

		local role_id = protocol.role_id --self.data.user_id or self.data.uid   --报错 data a nil value

		if not self.role_head_cell and self.node_list.role_icon then
			self.role_head_cell = BaseHeadCell.New(self.node_list.role_icon)
		end

		if self.role_head_cell then
			self.role_head_cell:SetData({role_id = role_id, prof = protocol.prof, sex = protocol.sex})
			local bundle = "uis/view/marry_ui/images_atlas"
        	local asset = "a3_qy_tq_yd"
        	self.role_head_cell:ChangeBg(bundle, asset, true)
			self.role_head_cell:SetHeadCellScale(0.7)
		end
	end

	local role_id = self.data.user_id or self.data.uid
	BrowseWGCtrl.Instance:BrowRoelInfo(role_id, role_info_call_back)
	-- local data = SocietyWGData.Instance:GetAfterSortFriendList(self.data.user_id)
	-- self.role_head_cell:SetData({role_id = self.data.user_id, prof = data.prof, sex = data.sex})
	-- self.role_head_cell:SetHeadCellScale(0.6)
end

function InviteFriendItemRender:OnInvitebtn()
	local data_num = 1
	local id_list = {}
	id_list[data_num] = self.data.user_id or self.data.uid
	local seq = MarryWGData.Instance:GetCurInviteSeq()
	local hunyan_num = MarryWGData.Instance:GetHunYanNum(seq)
	local guests_data = MarryWGData.Instance:GetInviteGuestsByWeddingSequence(hunyan_num)
	local has_num = 0
	if guests_data then
		has_num = guests_data.can_num - guests_data.has_num 	--剩余邀请人数
	end
	if has_num <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YaoQingNoSucess)
		return
	else
		local cur_yaoqing_listindex_value = self.tab_select_type
		if cur_yaoqing_listindex_value == FRIEND or cur_yaoqing_listindex_value == FACTION then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YaoQingSucess)
		elseif cur_yaoqing_listindex_value == APPLICANT then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.AgreeSucess)
		end
		
	end
	MarryWGCtrl.Instance:SendYaoQingBingKeReq(seq, data_num, id_list)
	MarryWGData.Instance:SetYaoQingBinKe()
end

function InviteFriendItemRender:OnDisAgreedApplaybtn()
	local seq = MarryWGData.Instance:GetCurInviteSeq()
	local user_id = self.data.user_id or self.data.uid
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_REFUSAL_APPLY, seq, user_id)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.DenySucess)
end

--------------------------------------------------已邀请的宾客列表----------------------------------------------------------------
InviteBinKeItemRender = InviteBinKeItemRender or BaseClass(BaseRender)

function InviteBinKeItemRender:__init()
end

function InviteBinKeItemRender:__delete()
	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

function InviteBinKeItemRender:OnFlush()
	if not self.data then
		return
	end
	
	--local user_id = self:Get_User_id()
	local flush_fun = function (protocol)
		if not self.data then
			return
		end

        local roledata = {is_online = protocol.is_online ,guild_id = protocol.guild_id}
		if roledata.is_online == 1 then
			self.node_list["isonline_right"].text.text = Language.Marry.OnLine
		else
			self.node_list["isonline_right"].text.text = Language.Marry.NoOnLine
		end
		if SocietyWGData.Instance:CheckIsFriend(self.data.user_id) then
			self.node_list["isonline"].text.text = Language.Marry.HaoYou
		elseif roledata.guild_id > 0 and roledata.guild_id == RoleWGData.Instance.role_vo.guild_id then
			self.node_list["isonline"].text.text = Language.Marry.MengYou
		else
			self.node_list["isonline"].text.text = Language.Marry.MoShengRen
		end

    end
    BrowseWGCtrl.Instance:BrowRoelInfo(self.data.user_id, flush_fun, RoleWGData.Instance.role_vo.plat_type)
	
	self.node_list["invite_btn"]:SetActive(false)
	self.node_list["isonline_right"]:SetActive(true)
	self.node_list["select_invite"]:SetActive(false)
	--local in_inviteList = MarryWGData.Instance:IsInYaoQingBinKeList(self.data)
	--self.node_list["select_gou"]:SetActive(in_inviteList)
	self.node_list["role_name"].text.text = self.data.name or self.data.gamename or self.data.role_name
	self:FlushHeadIcon()
end

function InviteBinKeItemRender:FlushHeadIcon()
	local role_info_call_back = function(protocol)
		if IsEmptyTable(protocol) or not self.node_list then
			return
		end

		local role_id = protocol.role_id --self.data.user_id or self.data.uid   --报错 data a nil value

		if not self.role_head_cell and self.node_list.role_icon then
			self.role_head_cell = BaseHeadCell.New(self.node_list.role_icon)
		end

		if self.role_head_cell then
			self.role_head_cell:SetData({role_id = role_id, prof = protocol.prof, sex = protocol.sex})
			local bundle = "uis/view/marry_ui/images_atlas"
        	local asset = "a3_qy_tq_yd"
        	self.role_head_cell:ChangeBg(bundle, asset, true)
			self.role_head_cell:SetHeadCellScale(0.7)
		end
	end

	local role_id = self.data.user_id or self.data.uid
	BrowseWGCtrl.Instance:BrowRoelInfo(role_id, role_info_call_back)
	-- local data = SocietyWGData.Instance:GetAfterSortFriendList(self.data.user_id)
	-- self.role_head_cell:SetData({role_id = self.data.user_id, prof = data.prof, sex = data.sex})
	-- self.role_head_cell:SetHeadCellScale(0.6)
end