SkillBreakPurchaseWGData = SkillBreakPurchaseWGData or BaseClass()

function SkillBreakPurchaseWGData:__init()
	if SkillBreakPurchaseWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[SkillBreakPurchaseWGData] attempt to create singleton twice!")
		return
	end
	SkillBreakPurchaseWGData.Instance = self

	self.skill_purchase_info = {}

	local skill_purchase_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_skill_break_rmb_buy_auto")
	self.shop_cfg = ListToMap(skill_purchase_cfg.shop, "grade")
	self.model_show_cfg = ListToMap(skill_purchase_cfg.model_show,"sex","prof")
end

function SkillBreakPurchaseWGData:__delete()
	self.skill_purchase_info = nil

    SkillBreakPurchaseWGData.Instance = nil
end

function SkillBreakPurchaseWGData:SetPurchaseInfo(protocol)
	self.skill_purchase_info = protocol
end

function SkillBreakPurchaseWGData:GetPurchaseInfo()
	return self.skill_purchase_info
end

-- 折扣剩余时间
function SkillBreakPurchaseWGData:GetDiscountEndTime()
	return self.skill_purchase_info and self.skill_purchase_info.discount_end_time or 0
end

-- 获取当前档次配置
function SkillBreakPurchaseWGData:GetCurrentShopConfig()
	local grade = self.skill_purchase_info and self.skill_purchase_info.grade or 1
	return self.shop_cfg[grade]
end

-- 获取模型显示配置
function SkillBreakPurchaseWGData:GetModelShowConfig(sex, prof)
	return self.model_show_cfg[sex][prof]
end