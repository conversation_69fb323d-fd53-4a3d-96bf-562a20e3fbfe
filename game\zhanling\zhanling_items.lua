ZhanLingRewardRender = ZhanLingRewardRender or BaseClass(BaseRender)
function ZhanLingRewardRender:__init()
    self.view:SetActive(true)
    self.normal_item = ZhanLingRewardItem.New(self.node_list.normal_item)

    self.high_item_list = {}
    for i = 1, 2 do
        self.high_item_list[i] = ZhanLingRewardItem.New(self.node_list.high_item_list:FindObj("high_item_" .. i))
        self.high_item_list[i]:SetIsHighItem(true)
    end
end

function ZhanLingRewardRender:LoadCallBack()
    if self.node_list["btn_high_get"] then
        self.node_list["btn_high_get"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward,self))
    end
end

function ZhanLingRewardRender:__delete()
    if self.normal_item then
        self.normal_item:DeleteMe()
        self.normal_item = nil
    end

    if self.high_item_list then
        for k, v in pairs(self.high_item_list) do
            v:DeleteMe()
        end
        self.high_item_list = nil
    end
end

function ZhanLingRewardRender:SetIsGuDingReward(bool)
    self.is_guding_reward = bool
end

function ZhanLingRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local cur_max_level = ZhanLingWGData.Instance:GetCurRewardMaxLevel()
    local level_star = self.data.level
    local show_level_str = self.data.level
    if self.data.level > cur_max_level then
        level_star = cur_max_level
        show_level_str = cur_max_level .. "+"
    end

    local nor_can_get = self.data.nor_reward_state == REWARD_STATE_TYPE.CAN_FETCH
    local high_can_get = self.data.high_reward_state == REWARD_STATE_TYPE.CAN_FETCH
    self.node_list.title.text.text = show_level_str  --战令等级
    if self.node_list["btn_high_get"] then
        self.node_list["btn_high_get"]:SetActive(nor_can_get or high_can_get)
    end

    local zhanling_info = ZhanLingWGData.Instance:GetZhanLingInfo()

    if self.node_list.progress_up then
        self.node_list["progress_up"]:SetActive(zhanling_info.level >= level_star)
    end
    if self.node_list.progress_down then
        self.node_list["progress_down"]:SetActive(zhanling_info.level > level_star)
    end

    -- if self.node_list.red_line then
    --     self.node_list.red_line:SetActive(nor_can_get)
    -- end
    -- if self.node_list.lv_bg then
    --     self.node_list.lv_bg:SetActive(nor_can_get) --灰色圆背景
    -- end
    
    if self.node_list.high_lock then
        self.node_list.high_lock:SetActive(not zhanling_info.act_high) --(self.data.high_reward_state == REWARD_STATE_TYPE.FINISH) --灰色背景
    end

    -- if self.node_list.normal_lock then
    --     self.node_list.normal_lock:SetActive(self.data.nor_reward_state == REWARD_STATE_TYPE.FINISH) --灰色背景
    -- end

    if self.node_list.normal_item_red_point then
        self.node_list.normal_item_red_point:SetActive(nor_can_get) 
    end

    if self.node_list.high_red_point1 then
        self.node_list.high_red_point1:SetActive(high_can_get) 
    end

    if self.node_list.high_red_point2 then
        self.node_list.high_red_point2:SetActive(high_can_get) 
    end

    if self.node_list.lv_graybg then
        self.node_list.lv_graybg:SetActive(not nor_can_get) --灰色圆背景
    end
    -- if self.node_list.gray_line then
    --     self.node_list.gray_line:SetActive(not nor_can_get) --灰线
    -- end
    
    --self.node_list.btn_high_get:SetActive(high_can_get)

    -- if self.node_list.n_bg_light then
    --     self.node_list.n_bg_light:SetActive(true)-- self.data.nor_reward_state ~= REWARD_STATE_TYPE.UNDONE)
    -- end

    -- if self.node_list.n_bg_dark then
    --     self.node_list.n_bg_dark:SetActive(false)-- self.data.nor_reward_state == REWARD_STATE_TYPE.UNDONE)
    -- end

    -- if self.node_list.h_bg_light then
    --     self.node_list.h_bg_light:SetActive(true)-- self.data.high_reward_state ~= REWARD_STATE_TYPE.UNDONE)
    -- end

    -- if self.node_list.h_bg_dark then
    --     self.node_list.h_bg_dark:SetActive(false)-- self.data.high_reward_state == REWARD_STATE_TYPE.UNDONE)
    -- end
    if self.node_list.special_canget_nor_part and self.node_list.special_canget_high_part then
        local is_act = ZhanLingWGData.Instance:GetIsActHighZhanLing()
        local overflow_nor_num, overflow_high_num = ZhanLingWGData.Instance:GetAfterMaxLevelRewardNum()
        overflow_high_num = is_act and overflow_high_num or 0
        local show_rest_num = math.max(overflow_nor_num, overflow_high_num)
        self.node_list.canget_nor_text.text.text = string.format(Language.ZhanLing.RestRewardStr, show_rest_num)
        self.node_list.canget_high_text.text.text = string.format(Language.ZhanLing.RestRewardStr, show_rest_num)

        local s_nor_show, s_high_show = false, false
        if nor_can_get and not high_can_get then
            s_nor_show = true
        elseif nor_can_get and high_can_get then
            s_nor_show = true
        elseif not nor_can_get and high_can_get then
            s_high_show = true
        end

        self.node_list.special_canget_nor_part:SetActive(s_nor_show and self.data.level > cur_max_level)
        self.node_list.special_canget_high_part:SetActive(s_high_show and self.data.level > cur_max_level)
        self.node_list.btn_special_get:SetActive(s_high_show and self.data.level > cur_max_level)
        self.node_list.btn_special_high_get:SetActive(s_high_show and self.data.level > cur_max_level)
    end

    self.normal_item:SetData({reward_data = self.data.nor_reward_list[0], reward_state = self.data.nor_reward_state})
    for k, v in ipairs(self.high_item_list) do
        v:SetData({reward_data = self.data.high_reward_list[k - 1], reward_state = self.data.high_reward_state})
    end
    local is_show = (self.normal_item.data.reward_state == REWARD_STATE_TYPE.FINISH or not nor_can_get)
    --self.node_list.btn_normal_get:SetActive(is_show)
    --self.node_list.red_line:SetActive(is_show)
    --self.node_list.lock:SetActive(not is_show) --灰色背景
end

function ZhanLingRewardRender:OnClickGetReward()
    if IsEmptyTable(self.data) then
        return
    end

    ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.FetchLevelReward, self.data.level)
end

--------------------------------------------------------------------------------
ZhanLingRewardItem = ZhanLingRewardItem or BaseClass(BaseRender)
function ZhanLingRewardItem:__init()
    self.reward_item = ItemCell.New(self.node_list.item_node)
end

function ZhanLingRewardItem:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end

    self.is_high_item = nil
end

function ZhanLingRewardItem:SetIsHighItem(bool)
    self.is_high_item = bool
end

function ZhanLingRewardItem:OnFlush()
    if IsEmptyTable(self.data.reward_data) then
        self.view:SetActive(false)
    else
        self.view:SetActive(true)

        local act_high = ZhanLingWGData.Instance:GetIsActHighZhanLing()
        self.reward_item:SetData(self.data.reward_data)
        self.reward_item:SetRedPointEff(self.data.reward_state == REWARD_STATE_TYPE.CAN_FETCH)
        -- self.node_list.item_lock:SetActive(self.data.reward_state == REWARD_STATE_TYPE.UNDONE)
        self.node_list.item_lock:SetActive(self.is_high_item and not act_high)
        self.node_list.item_isget:SetActive(self.data.reward_state == REWARD_STATE_TYPE.FINISH)
        -- local item_id = self.data.reward_data.item_id
        -- local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
        -- if item_cfg then
        --     self.reward_item:SetEffectRootEnable(item_cfg.color > GameEnum.ITEM_COLOR_ORANGE)
        -- else
        --     self.reward_item:SetEffectRootEnable(false)
        -- end
    end
end

--------------------------------------------------------------------------------
ZhanLingBuyLevelRender = ZhanLingBuyLevelRender or BaseClass(BaseRender)
function ZhanLingBuyLevelRender:__init()
    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuyLevelBtn, self))
    XUI.AddClickEventListener(self.node_list["cell_node_btn"], BindTool.Bind(self.OnClickBuyLevelBtn, self))
end

function ZhanLingBuyLevelRender:__delete()

end

function ZhanLingBuyLevelRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.add_level_str.text.text = string.format(Language.ZhanLing.LevelStr2, self.data.add_level)
    self.node_list.old_price_text.text.text = self.data.old_price
    self.node_list.now_price_text.text.text = self.data.now_price

    self.node_list.tag_zhekou:SetActive(self.data.cur_discount < 1)
    self.node_list.zhekou_text.text.text = string.format(Language.ZhanLing.Discount, NumberToChinaNumber(self.data.cur_discount * 10))
    
    self.node_list.tuijian:SetActive(self.data.is_tuijian)
    local desc_1 = string.format(Language.ZhanLing.BuyLevelDesc1, self.data.add_to_level)
    local desc_2 = string.format(Language.ZhanLing.BuyLevelDesc2, self.data.can_get_reward_num)
    local desc_3 = ""

    local btn_enable = true
    if self.data.limit_count > 0 then
        local rest_num = self.data.limit_count - self.data.has_buy_count
        btn_enable = rest_num > 0
        local color_str = btn_enable and COLOR3B.L_GREEN or "#000011"
        desc_3 = string.format(Language.ZhanLing.BuyLevelDesc3, ToColorStr(rest_num, color_str), self.data.limit_count)
    end
    self.node_list.old_price:SetActive(self.data.cur_discount < 1 and btn_enable) --原价显示
    self.node_list.buy_desc.text.text = desc_1 -- .. desc_2
    self.node_list.buy_desc_2.text.text = desc_2 .. "\n" .. desc_3
    XUI.SetButtonEnabled(self.node_list["btn_buy"], btn_enable)
    if not btn_enable then
        self.node_list.zhekou_text.text.text = Language.Shop.OtherDesc2
    end
end

function ZhanLingBuyLevelRender:OnClickBuyLevelBtn()
    if IsEmptyTable(self.data) then
        return
    end

    ZhanLingWGCtrl.Instance:SetBuyLevelAlertData(self.data.level_id, self.data.now_price)
end

--------------------------------------------------------------------------------
ZhanLingShopRender = ZhanLingShopRender or BaseClass(BaseRender)
function ZhanLingShopRender:LoadCallBack()
    self.view:SetActive(true)
    self.shop_item = ItemCell.New(self.node_list.item_node)
    XUI.AddClickEventListener(self.node_list["btn_exchange"], BindTool.Bind(self.OnClickExchangeBtn, self))
    XUI.AddClickEventListener(self.node_list["icon"], BindTool.Bind(self.OnClickExchangeItem, self))
end

function ZhanLingShopRender:__delete()
    if self.shop_item then
        self.shop_item:DeleteMe()
        self.shop_item = nil
    end
end

function ZhanLingShopRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.shop_item:SetData({item_id = self.data.item_id, is_bind = self.data.is_bind})
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    self.node_list.name.text.text = item_cfg and item_cfg.name or ""

    local exchange_item_cfg = ZhanLingWGData.Instance:GetExchangeItemCfg()
    self.node_list.icon:SetActive(false)
    if exchange_item_cfg then
        local bundle, asset = ResPath.GetItem(exchange_item_cfg.icon_id)
        self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function()
            self.node_list.icon:SetActive(true)
        end)
    end

    local is_limit_type = self.data.limit_type ~= ZhanLingShowType.NoLimit
    self.node_list.limit_str:SetActive(is_limit_type)
    local is_limit = false
    if is_limit_type then
        local has_exchange_num = ZhanLingWGData.Instance:GetLimitExchangeItemInfo(self.data.limit_type, self.data.item_seq)
        is_limit = has_exchange_num >= self.data.limit_count
        --限制文本
        local str = self.data.limit_type == ZhanLingShowType.DailyLimit and Language.ZhanLing.ExchangeLimitDesc1 or Language.ZhanLing.ExchangeLimitDesc2
        local color_str = is_limit and has_exchange_num or ToColorStr(has_exchange_num, COLOR3B.L_GREEN) 
        self.node_list.limit_str.text.text = string.format(str, color_str, self.data.limit_count)
    end

    --按钮文本
    local had_num = ZhanLingWGData.Instance:GetExchangeItemNum()
    local is_enough = had_num >= self.data.item_price
    local num_str_color = is_enough and ToColorStr(had_num, COLOR3B.GREEN) or had_num
    self.node_list.need_str.text.text = num_str_color .. "/" .. self.data.item_price
    XUI.SetButtonEnabled(self.node_list.btn_exchange, not is_limit)

    self.node_list.tag_enough:SetActive(is_enough and not is_limit) --可兑换tag
    self.node_list.cant_buy:SetActive(is_limit) --已售罄tag
end

function ZhanLingShopRender:OnClickExchangeBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local had_num = ZhanLingWGData.Instance:GetExchangeItemNum()
    if had_num < self.data.item_price then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanLing.NotEnoughExchange)
        return
    end

    local empty_num = ItemWGData.Instance:GetEmptyNum()
    local stuff_empty_num = ItemWGData.Instance:GetStuffBagEmptyNum()
    if empty_num == 0 or stuff_empty_num == 0 then
        RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
        return
    end

    ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.RechangeGoods, self.data.item_seq, 1)
end

function ZhanLingShopRender:OnClickExchangeItem()
    local other_cfg = ZhanLingWGData.Instance:GetZhanLingOtherCfg()
	local exchange_item_id = other_cfg and other_cfg.reward_item_id or 0
    TipWGCtrl.Instance:OpenItem({item_id = exchange_item_id})
end
--------------------------------------------------------------------------------
ZhanLingTaskRender = ZhanLingTaskRender or BaseClass(BaseRender)
function ZhanLingTaskRender:__init()
    self.view:SetActive(true)
    self.exp_item = ItemCell.New(self.node_list.item_node)
    self.exp_item:SetNeedItemGetWay(true)
    self.exp_item:SetShowCualityBg(false)
    XUI.AddClickEventListener(self.node_list["btn_jump_view"], BindTool.Bind(self.OnClickBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_flush_task"], BindTool.Bind(self.OnClickFlushTask, self))

    local x_scale = self.node_list["bg"].rect.rect.width / 1124
    self.node_list.refresh_effect_node.transform.localScale = u3dpool.vec3(0.97 * x_scale, 0.97, 0.97)
end

function ZhanLingTaskRender:__delete()
    if self.exp_item then
        self.exp_item:DeleteMe()
        self.exp_item = nil
    end

    self:ResetEffectTimer()
end

function ZhanLingTaskRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    self.node_list.task_desc.text.text = self.data.task_des

    local can_fetch = self.data.task_state == REWARD_STATE_TYPE.CAN_FETCH
    local show_jump = self.data.task_state == REWARD_STATE_TYPE.UNDONE and self.data.jump_view ~= ""
    self.node_list.btn_text.text.text = can_fetch and Language.ZhanLing.TaskBtnText2 or Language.ZhanLing.TaskBtnText1
    self.node_list.btn_jump_view:SetActive(show_jump or can_fetch)
    self.node_list.is_finish:SetActive(self.data.task_state == REWARD_STATE_TYPE.FINISH)
    self.node_list.cant_jump:SetActive(self.data.task_state == REWARD_STATE_TYPE.UNDONE and self.data.jump_view == "")
    self.node_list.btn_jump_remind:SetActive(can_fetch)
    self.node_list.btn_flush_task:SetActive(self.data.task_state == REWARD_STATE_TYPE.UNDONE)
    
    local other_cfg = ZhanLingWGData.Instance:GetZhanLingOtherCfg()
	local cost_value = other_cfg and other_cfg.flash_cost or 0
    self.node_list["cost_value"].text.text = cost_value --刷新需要消耗的仙玉

    -- local bundle, asset = ResPath.GetZhanLingImg("a2_task" .. self.data.difficult)
    -- self.node_list.nandu_img.image:LoadSpriteAsync(bundle, asset)
    
    local desc = Language.ZhanLing.TaskType[self.data.difficult]
    self.node_list.type_desc.text.text = desc
    local other_cfg = ZhanLingWGData.Instance:GetZhanLingOtherCfg()
    local exchange_item_id = other_cfg and other_cfg.exp_item or 0
    self.exp_item:SetData({item_id = exchange_item_id})
    self.exp_item:SetRightBottomColorText(self.data.task_exp)
    self.exp_item:SetRightBottomTextVisible(true)
    local bundle, asset = ResPath.GetCommon("a3_ty_wpk_" .. self.data.difficult + 1)
    self.exp_item:SetCellBg(bundle, asset)
    self.exp_item:SetRedPointEff(can_fetch)

    local last_refresh_index = ZhanLingWGData.Instance:GetRefreshTaskID()
    self:ResetEffectTimer()
    if self.index == last_refresh_index then
        self:ShowRefreshEffect()
        ZhanLingWGData.Instance:SetRefreshTaskID(-999)
    end
end

function ZhanLingTaskRender:ShowRefreshEffect()
    self.node_list.refresh_effect_node:SetActive(true)
    local callback = function()
        self.node_list.refresh_effect_node:SetActive(false)
    end
    self.effect_timer = GlobalTimerQuest:AddTimesTimer(callback, 1, 1)
end

function ZhanLingTaskRender:ResetEffectTimer()
    self.node_list.refresh_effect_node:SetActive(false)
    if self.effect_timer then
        GlobalTimerQuest:CancelQuest(self.effect_timer)
        self.effect_timer = nil
    end
end

function ZhanLingTaskRender:OnClickBtn()
    if IsEmptyTable(self.data) then
        return
    end

    if self.data.task_state == REWARD_STATE_TYPE.CAN_FETCH then
        --ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.FetchTaskExp, self.data.task_id, self.data.task_class)
        ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.OneKeyFetchTaskExp)
        ZhanLingWGData.Instance:SetZhanLingItemPos(self.node_list.item_node)
    else
        self:OnClickJumpView()
    end
end

function ZhanLingTaskRender:OnClickJumpView()
    if IsEmptyTable(self.data) then
        return
    end

    if self.data.task_type == ZhanLingTaskType.BATTLE_LINGPAI_TASK_TYPE_GUILD_SPEAK then
        local str = XiuXianShiLianWGData.Instance:GetRandomContent()
        ViewManager.Instance:Open(GuideModuleName.ChatView, 60, nil ,{open_param = str})

    elseif self.data.task_type == ZhanLingTaskType.BATTLE_LINGPAI_TASK_TYPE_WORLD_SPEAK then
        local str = XiuXianShiLianWGData.Instance:GetRandomContent()
        ViewManager.Instance:Open(GuideModuleName.ChatView, 70, nil ,{open_param = str})

    elseif self.data.task_type == ZhanLingTaskType.BATTLE_LINGPAI_TASK_TYPE_DOUBLE_HUSONG_X then
        if NewTeamWGData.Instance:GetIsMatching() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
            return
        end

        if YunbiaoWGData.Instance:GetIsHuShong() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
            return
        end

        TaskGuide.Instance:CanAutoAllTask(false)
        ViewManager.Instance:Close(GuideModuleName.OpenServerAssistView)
        ActIvityHallWGCtrl.Instance:DoHuSong()
    else
        FunOpen.Instance:OpenViewNameByCfg(self.data.jump_view)
    end
end

function ZhanLingTaskRender:OnClickFlushTask()
    if IsEmptyTable(self.data) then
        return
    end
    ZhanLingWGCtrl.Instance:SetFlushTaskAlertData(self.data.task_id, self.data.task_class, self:GetIndex())
end
