GuildShouHuJieSuanView = GuildShouHuJieSuanView or BaseClass(SafeBaseView)

function GuildShouHuJieSuanView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/guild_shouhu_ui_prefab", "layout_guild_shouhu_jiesuan")
	self.active_close = false
end

function GuildShouHuJieSuanView:ReleaseCallBack()
	if self.geren_reward_list ~= nil then
		self.geren_reward_list:DeleteMe()
		self.geren_reward_list = nil
	end

	if CountDownManager.Instance:HasCountDown("guild_fb_shouhu_close_timer") then
		CountDownManager.Instance:RemoveCountDown("guild_fb_shouhu_close_timer")
	end
end

function GuildShouHuJieSuanView:LoadCallBack()
	self.geren_reward_list = AsyncListView.New(ItemCell, self.node_list.geren_reward_list)
	self.geren_reward_list:SetStartZeroIndex(true)

	--先隐藏需要播放动画的节点
	self.node_list["geren_title_loss"]:SetActive(false)
	self.node_list["geren_title_win"]:SetActive(false)
	self:Flush()
end

function GuildShouHuJieSuanView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("guild_fb_shouhu_close_timer") then
		CountDownManager.Instance:RemoveCountDown("guild_fb_shouhu_close_timer")
	end

	GuildWGData.Instance:SetIsInPaiMaiHang(false)
	if self.data.is_boss then
		GuildBossWGCtrl.Instance:ShowShaiZiTips()
	end
end

function GuildShouHuJieSuanView:SetData(data)
	self.data = data
end

function GuildShouHuJieSuanView:OnFlush()
	if not self.data then
		return
	end

	local data = self.data
	if data.is_boss then
		self.node_list.shouhu_info:SetActive(false)
		self.node_list.boss_info:SetActive(true)
		local star_num = data.star_num
		local asset_name = {[1] = "a3_ty_xx_zc",[2] = "a3_ty_xx_zh",} --001实心

		for i = 1, 3 do
			if i <= star_num then
				self.node_list["star_"..i].image:LoadSprite(ResPath.GetCommonImages(asset_name[1]))
			else
				self.node_list["star_"..i].image:LoadSprite(ResPath.GetCommonImages(asset_name[2]))
			end
		end

		local part_time = data.part_time
		self.node_list.xianmeng_pass_time.text.text = TimeUtil.FormatSecond(part_time)--通关时间
		self.node_list.boss_geren_damage.text.text = data.hurt --个人伤害
		self.node_list.boss_geren_rank.text.text = data.rank --个人排名

		local star_res = GetSpecialStarImgResByStar5(star_num)
		local bundle, asset = ResPath.GetCommonImages(star_res)
		self.node_list.img_boss_pingjia.image:LoadSprite(bundle, asset, function()
			self.node_list.img_boss_pingjia.image:SetNativeSize()
		end)
	elseif data.is_shouhu then
		self.node_list.shouhu_info:SetActive(true)
		self.node_list.boss_info:SetActive(false)
		local main_rank_data, person_rank = GuildWGData.Instance:GetMainRoleRankData()
		--个人伤害
		self.node_list.geren_damage.text.text = main_rank_data and CommonDataManager.ConverExp(BigNumFormat(main_rank_data.hurt_val))  or 0
		--个人排名
		self.node_list.geren_rank.text.text = person_rank and string.format(Language.Guild.SHJieSuanRankStr, person_rank) or Language.Guild.SHNotRank
		--帮派通关时间
		self.node_list.guild_pass_time.text.text = data.is_success and TimeUtil.MSTime(data.pass_time) or string.format(Language.Guild.SHWaveStr, data.curr_wave)
		--帮派排名
		local part_time = GuildWGData.Instance:GetGuildFBData().pass_time
		self.node_list.guild_rank.text.text = TimeUtil.FormatSecond(part_time)--通关时间
	end

	self:SetGuildRank(data)

	if not data.is_success then
		--失败逻辑
		self.node_list.guild_pass_time.text.text = string.format(Language.Guild.SHWaveStr, GuildWGData.Instance:GetNowMonsterLun())
	end

	self.node_list.victory:SetActive(data.is_success)
	self.node_list.win_complete_title:SetActive(data.is_success)
	self.node_list.lose:SetActive(not data.is_success)
	self.node_list.lose_complete_title:SetActive(not data.is_success)
	local node_geren = data.is_success and self.node_list["geren_title_win"] or self.node_list["geren_title_loss"]
	node_geren:SetActive(true)

	if CountDownManager.Instance:HasCountDown("guild_fb_shouhu_close_timer") then
		CountDownManager.Instance:RemoveCountDown("guild_fb_shouhu_close_timer")
	end

	self:UpdateCountDownTime(1, 20)
	CountDownManager.Instance:AddCountDown("guild_fb_shouhu_close_timer", BindTool.Bind1(self.UpdateCountDownTime, self), BindTool.Bind1(self.CompleteCountDownTime, self), nil, 20, 1)
end

function GuildShouHuJieSuanView:UpdateCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		if self.node_list.lbl_end_time then
			self.node_list.lbl_end_time.text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, math.floor(total_time - elapse_time))
		end
	end
end
function GuildShouHuJieSuanView:CompleteCountDownTime()
	self:Close()
end

function GuildShouHuJieSuanView:SetGuildRank(data)
	local guild_data = GuildWGData.Instance
	local geren_data_list = {}
	local is_success = data.is_success
		if data.is_shouhu then
			geren_data_list = is_success and guild_data:GetGuildSHWinList() or guild_data:GetGuildSHLoseList()
		elseif data.is_boss then
			geren_data_list = GuildBossWGData.Instance:GetGuildBossRewardList()
		end

	if not IsEmptyTable(geren_data_list) then
		self.geren_reward_list:SetDataList(geren_data_list, 3)
	end
end