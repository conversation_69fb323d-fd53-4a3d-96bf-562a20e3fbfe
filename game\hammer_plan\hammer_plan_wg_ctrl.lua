require("game/hammer_plan/hammer_plan_view")
require("game/hammer_plan/hammer_plan_wg_data")

HammerPlanWGCtrl = HammerPlanWGCtrl or BaseClass(BaseWGCtrl)

function HammerPlanWGCtrl:__init()
	if nil ~= HammerPlanWGCtrl.Instance then
		print_error("[HammerPlanWGCtrl] attempt to create singleton twice!")
		return
	end

	HammerPlanWGCtrl.Instance = self
	self.view = HammerPlanView.New(GuideModuleName.HammerPlanView)
	self.data = HammerPlanWGData.New()

	self.need_check_hammer_plan_recharge = false
	self.need_check_daily_reward = false
	self:RegisterProtocol(SCWeekCardActivityInfo, "OnSCWeekCardActivityInfo")
	self.act_change = BindTool.Bind(self.ActChange, self)
    ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
end

function HammerPlanWGCtrl:__delete()
	HammerPlanWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.act_change then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
		self.act_change = nil
	end
end

function HammerPlanWGCtrl:OnSCWeekCardActivityInfo(protocol)
	local old_hammer_active_state = self.data:GetHammerPlanState()
	local old_daily_reward_flag = self.data:IsGetDailyReward()
	self.data:SetWeekCardActivityInfo(protocol)
	RemindManager.Instance:Fire(RemindName.HammerPlan)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	local state = self.data:GetHammerPlanState()
	local activity_state = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WEEK_CARD)
	self:SetTipsState(activity_state and state)

	if self.need_check_hammer_plan_recharge then
		local new_hammer_active_state = self.data:GetHammerPlanState()

		if old_hammer_active_state and not new_hammer_active_state then
			if self.view:IsOpen() then
				self.view:HammerPlanAnimation()
			end
		end

		self.need_check_hammer_plan_recharge = false
	end

	if self.need_check_daily_reward then
		local new_daily_reward_flag = self.data:IsGetDailyReward()

		if not old_daily_reward_flag and new_daily_reward_flag then
			local reward_item = self.data:GetDailyRewardDataList()
			
			if not IsEmptyTable(reward_item) then
				TipWGCtrl.Instance:ShowGetReward(nil, reward_item, nil, nil, nil, true)
			end
		end

		self.need_check_daily_reward = false
	end
end

function HammerPlanWGCtrl:ActChange(act_type, status)
    if act_type ~= ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WEEK_CARD then
        return
    end

	local state = self.data:GetHammerPlanState()
	self:SetTipsState(status == ACTIVITY_STATUS.OPEN and state)
end

function HammerPlanWGCtrl:SetTipsState(state)
	if state then
		local level_limit = ActivityWGData.Instance:GetActivityIsOpenByLevel(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WEEK_CARD)
		
		if level_limit then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.HAMMER_PLAN, 1,function ()
				FunOpen.Instance:OpenViewByName(GuideModuleName.HammerPlanView)
				return true
			end)	
		end
	else
		MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.HAMMER_PLAN)
	end
end

function HammerPlanWGCtrl:SetCheckRecharge(Status)
	self.need_check_hammer_plan_recharge = Status
end

function HammerPlanWGCtrl:SetCheckDailyReward(Status)
	self.need_check_daily_reward = Status
end