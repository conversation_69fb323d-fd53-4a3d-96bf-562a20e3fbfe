LingHeRecordView = LingHeRecordView or BaseClass(SafeBaseView)

function LingHeRecordView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(850, 500)})
    self:AddViewResource(0, "uis/view/tianshen_linghe_ui_prefab", "layout_linghe_record")
end

function LingHeRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function LingHeRecordView:LoadCallBack()
	 self.node_list.title_view_name.text.text = Language.TianShenLingHe.HuntRecordTitle
	 self.record_list = AsyncListView.New(LingHeRecordItem, self.node_list["record_list"])
end

function LingHeRecordView:OnFlush(param_t)
    
end

function LingHeRecordView:FlushRecordList()

end

----LingHeRecordItem---
LingHeRecordItem = LingHeRecordItem or BaseClass(BaseRender)
function LingHeRecordItem:__init()

end

function LingHeRecordItem:__delete()

end

function LingHeRecordItem:OnFlush()
	if not self.data then
		return
    end

    local index = self:GetIndex()
   	local mark = (index % 2) == 1
end