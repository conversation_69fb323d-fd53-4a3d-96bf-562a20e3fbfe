MustBuyView = MustBuyView or BaseClass(SafeBaseView)

function MustBuyView:__init( )
	self.view_style = ViewStyle.Window
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/must_buy_ui_prefab", "layout_must_buy")
end

function MustBuyView:ReleaseCallBack()
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	self.list_container = nil
end

function MustBuyView:OpenCallBack()
	MustBuyWGCtrl.Instance:SendOffSale()
	MustBuyWGData.Instance:SetHasOpenMustBuyFlag(true)
	RemindManager.Instance:Fire(RemindName.MustBuyRemind)
	if PlayerPrefsUtil.GetString("FirstOpenMustBuyView") ~= "FirstOpenMustBuyView" then
        PlayerPrefsUtil.SetString("FirstOpenMustBuyView", "FirstOpenMustBuyView")
        TalkCache.StopCurIndexAudio()
        AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Sound23, nil, true))
    end
end

function MustBuyView:CloseCallBack()
	self:RestCellTween()
	self.need_do_celltween = true
	MustBuyWGCtrl.Instance:SendHadCheck()
	MustBuyWGCtrl.Instance:SendOffSale()
end

function MustBuyView:LoadCallBack()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["layout_money_bar"].transform)
	end

	if not self.item_list then
		self.item_list = AsyncListView.New(MustBuyItemRender, self.node_list["item_list"])
		self.item_list:SetEndScrolledCallBack(BindTool.Bind(self.EndScrolledCallBack, self))
	end

	self.node_list["btn_tips"].button:AddClickListener(BindTool.Bind(self.OnClickBtnTip, self))

	self.need_do_celltween = true
end

function MustBuyView:ShowIndexCallBack()
    if self.node_list.chaozhibimai_img then
		UITween.DoUpDownCrashTween(self.node_list.chaozhibimai_img)
	end

	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.MustBuy, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY)
end

function MustBuyView:EndScrolledCallBack(scroll_obj, start_idx, end_idx)
	if self.need_do_celltween then
		self.need_do_celltween = false
        self:DoListAni()
    end
end

function MustBuyView:OnFlush()
	self:FlushItemList()
end

function MustBuyView:FlushItemList()
	local item_data = MustBuyWGData.Instance:GetItemList()
	self.item_list:SetDataList(item_data)
end

function MustBuyView:OnClickBtnTip()
	RuleTip.Instance:SetContent(Language.MustBuy.ExRuleContent, Language.MustBuy.ExRuleTitle)
end

function MustBuyView:SetScrollInteract()
    self.node_list.draw_mask_img:SetActive(false)
end

function MustBuyView:DoListAni()
    UITween.CleanMoveAlphaShow(GuideModuleName.MustBuy)
	
    local cell_list = self.item_list:GetSortCellList()
    if IsEmptyTable(cell_list) then
    	return
    end

    self.node_list.draw_mask_img:SetActive(true)

    local last_num = #cell_list
    for i=1,#cell_list do
    	cell_list[i]:PlayAnim(i, last_num)
    end
end

function MustBuyView:RestCellTween()
	UITween.CleanAllTween(GuideModuleName.MustBuy)
	if not self.item_list then 
		return 
	end
	local cell_list = self.item_list and self.item_list:GetSortCellList()
    if IsEmptyTable(cell_list) then
    	return
    end
    for i=1,#cell_list do
    	cell_list[i]:RestTween()
    end
end

----------------------------------------------------------------------------------------

MustBuyItemRender = MustBuyItemRender or BaseClass(BaseRender)

function MustBuyItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function MustBuyItemRender:LoadCallBack()
	self.node_list["btn_buy"].button:AddClickListener(BindTool.Bind(self.OnClickBtnBuy, self))
end

function MustBuyItemRender:OnFlush()
	if not self.data then
		return
	end
	local info = MustBuyWGData.Instance:GetItemCfgBySeq(self.data.seq)
	local cfg = ItemWGData.Instance:GetItemConfig(info.buy_item.item_id)

	self.node_list["discount_text"].text.text = info.discount .. Language.MustBuy.Discount
	-- self.node_list["ori_price"].text.text = string.format(Language.MustBuy.OriPrice, info.origin_cost)
	self.node_list["ori_price"].text.text = info.origin_cost
	self.node_list["now_price"].text.text = info.buy_cost
	if self.data.is_new ~= 0 and self.data.buy_times == 0 then
		self.node_list["img_new"]:SetActive(true)
	else
		self.node_list["img_new"]:SetActive(false)
	end

	if cfg and info.item_name then
		self.node_list["name"].text.text = info.item_name --ToColorStr(info.item_name, ITEM_COLOR[cfg.color])
	end

	self:FlushCell(info)
	self:FlushIconImage(info)
	self:ShowCountDown()
	self:ShowButtonStatus(info)
end

function MustBuyItemRender:OnClickBtnBuy()
	local info = MustBuyWGData.Instance:GetItemCfgBySeq(self.data.seq)
	if info.money_type == 1 then
		local gold = RoleWGData.Instance:GetRoleVo().gold
		if gold < info.buy_cost then
			VipWGCtrl.Instance:OpenTipNoGold()
			return
		end
	elseif info.money_type == 2 then
		local role_info = RoleWGData.Instance:GetRoleInfo()
		if not role_info then
			return
		end
		if role_info.gold + role_info.bind_gold < info.buy_cost then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = COMMON_CONSTS.VIRTUAL_ITEM_BINDGOL})--铜币
			return
		end
		if role_info.bind_gold < info.buy_cost then
			MustBuyWGCtrl.Instance:OpenAlert(info.buy_cost - role_info.bind_gold, function()
				MustBuyWGCtrl.Instance:SendBuyItem(self.data.seq)
			end)
			return
		end
	elseif info.money_type == 3 then
		if not RoleWGData.GetIsEnoughAllCoin(info.buy_cost) then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = 27517})--铜币
			return
		end
	end
	MustBuyWGCtrl.Instance:SendBuyItem(self.data.seq)
end

function MustBuyItemRender:ShowButtonStatus(info)
	if info == nil then
		info = MustBuyWGData.Instance:GetItemCfgBySeq(self.data.seq)
	end

	local btn_state = true
	local btn_text_state = true
	if self.data.off_time == 0 or self.data.off_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
		if self.data.buy_times >= info.buy_limit then
			btn_state = false
			self.node_list["btn_text"].text.text = Language.MustBuy.SellOut
		else
			btn_text_state = false
			XUI.SetButtonEnabled(self.node_list["btn_buy"], true)
			self.node_list["btn_text"].text.text = string.format(Language.MustBuy.Buy, info.buy_limit - self.data.buy_times, info.buy_limit)
		end
	else
		XUI.SetButtonEnabled(self.node_list["btn_buy"], false)
		self.node_list["btn_text"].text.text = Language.MustBuy.OffLine
	end

	self.node_list["btn_buy"]:SetActive(btn_state)
	self.node_list["sell_out"]:SetActive(not btn_state)

	self.node_list["btn_text"]:SetActive(btn_text_state)
	self.node_list["new"]:SetActive(not btn_text_state)
end

function MustBuyItemRender:ShowCountDown()
	self:ShowButtonStatus()
end

function MustBuyItemRender:UpdateCellTime()
	local time = self.data.off_time - TimeWGCtrl.Instance:GetServerTime()
	if time <= 0 then
		self:ShowCountDown()
	end
end

function MustBuyItemRender:FlushIconImage(info)
	local icon_name
	local show_red = false
	if info.money_type == 1 then
		icon_name = "a3_huobi_xianyu"
	elseif info.money_type == 2 then
		icon_name = "a3_huobi_bangyu"
		local bind_gold = RoleWGData.Instance:GetAttr("bind_gold")
		show_red = bind_gold >= info.buy_cost
	else
		icon_name = "a3_huobi_tongqian"
	end

	self.node_list["btn_buy_red"]:SetActive(show_red)
	local bundle, asset = ResPath.GetCommonIcon(icon_name)
	self.node_list["icon_gold"].image:LoadSprite(bundle, asset, function()
		self.node_list["icon_gold"].image:SetNativeSize()
	end)
	self.node_list["old_icon_gold"].image:LoadSprite(bundle, asset, function()
		self.node_list["old_icon_gold"].image:SetNativeSize()
	end)
end

function MustBuyItemRender:FlushCell(info)
	if not self.cell then
		self.cell = ItemCell.New(self.node_list["cell"])
		self.cell:SetItemTipFrom(ItemTip.FROM_GET_REWARD)
	end
	self.cell:SetData(info.buy_item)
end

function MustBuyItemRender:PlayAnim(item_index, total_count)
	local tween_info = UITween_CONSTS.MustBuySys
    local move_delay_time = tween_info.NumNextDoDelay * item_index
    local alpha_delay_time = move_delay_time + 0.2

    UITween.FakeHideShow(self.node_list["tween_root"])
	UITween.FakeHideShow(self.node_list["canvas_root"])
	
	self.move_delay_key = "MustBuyRender" .. item_index
	ReDelayCall(self, function()
		if self.node_list and self.node_list["tween_root"] then
            UITween.MoveAlphaShow(GuideModuleName.MustBuy, self.node_list["tween_root"], tween_info.Item_Tween, function()
                if item_index == total_count then
                    MustBuyWGCtrl.Instance:SetScrollInteract()
                end
            end)
        end   
	end, move_delay_time, self.move_delay_key)

	self.alpha_delay_key = "MustBuyRenderAlpha" .. item_index
	ReDelayCall(self, function()
		UITween.FakeToShow(self.node_list.canvas_root)
    end, alpha_delay_time, self.alpha_delay_key)
end

function MustBuyItemRender:RestTween()
	UITween.FakeToShow(self.node_list.tween_root)
	UITween.FakeToShow(self.node_list.canvas_root)
	CancleDelayCall(self, self.move_delay_key)
	CancleDelayCall(self, self.alpha_delay_key)
	self.node_list["tween_root"].transform.anchoredPosition = u3dpool.vec2(0, 0)
end