KF3v3View = KF3v3View or BaseClass(SafeBaseView)
-- 3v3信息界面
local SlderValue = {
    [0] = 0,
    [1] = 0.15,
    [2] = 0.36,
    [3] = 0.57,
    [4] = 0.78,
    [5] = 1,
}

function KF3v3View:Init3V3()
    --XUI.AddClickEventListener(self.node_list.btn_join_zhandui, BindTool.Bind(self.OnClickJoinZhanDui3V3, self))
    XUI.AddClickEventListener(self.node_list.btn_pvp_tips, BindTool.Bind(self.OnClickTips3V3, self))
    --XUI.AddClickEventListener(self.node_list.btn_season_reward, BindTool.Bind(self.OnClickSeasonReward, self)) 赛季奖励
    XUI.AddClickEventListener(self.node_list.btn_duanwei_reward, BindTool.Bind(self.OnClickDuan<PERSON><PERSON>ew<PERSON>, self))
    XUI.AddClickEventListener(self.node_list.btn_zhandui_rank, BindTool.Bind(self.OnClickZhanDuiRank, self))
    XUI.AddClickEventListener(self.node_list.btn_goto_war, BindTool.Bind(self.OnClickGoToWar, self))
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickClose, self))
    XUI.AddClickEventListener(self.node_list.btn_to_zhandui, BindTool.Bind(self.OnClickJoinZhanDui3V3, self))

    self.reward_item_list = {}
    for i = 1, 5 do
        self.reward_item_list[i] = KF3V3FetchRewardItemRender.New(self.node_list["reward_item" .. i])
    end
    self.member_item_list = {}
    for i = 1, 3 do
        self.member_item_list[i] = KF3v3MemberItemRender.New(self.node_list["Member" .. i])
    end

    self.zhan_dui_info_change_event_3v3 = GlobalEventSystem:Bind(OtherEventType.ZhanDui_Info_Change,
        BindTool.Bind(self.OnZhanDuiInfoChange3V3, self))                                                                                              --战队信息改变
    self.role_info_change_3v3 = GlobalEventSystem:Bind(OtherEventType.KF3V3ZhanDuiRoleInfoChnage,
        BindTool.Bind(self.OnRoleInfoChange3V3, self))                                                                                                 --人物信息改变

    -- 红点绑定
    self.join_zhandui_remind = BindTool.Bind(self.OnJoinZhanduiRemindChange, self)
    RemindManager.Instance:Bind(self.join_zhandui_remind, RemindName.JoinZhanDui)
end

function KF3v3View:Delete3V3()
    if self.zhan_dui_info_change_event_3v3 then
        GlobalEventSystem:UnBind(self.zhan_dui_info_change_event_3v3)
        self.zhan_dui_info_change_event_3v3 = nil
    end
    if self.role_info_change_3v3 then
        GlobalEventSystem:UnBind(self.role_info_change_3v3)
        self.role_info_change_3v3 = nil
    end
    self.last_req_member_info_time = nil

    RemindManager.Instance:UnBind(self.join_zhandui_remind)

    if self.reward_item_list ~= nil then
        for i, v in ipairs(self.reward_item_list) do
            v:DeleteMe()
        end
        self.reward_item_list = nil
    end
    if self.member_item_list ~= nil then
        for i, v in ipairs(self.member_item_list) do
            v:DeleteMe()
        end
        self.member_item_list = nil
    end
end

function KF3v3View:ShowIndex3V3()
    self:OnFlush3V3()
end

function KF3v3View:OnFlush3V3()
    --加入战队按钮
    self:FlushBtn3V3()
    --处理左侧有无战队显隐情况
    self:FlushLeftInfo3V3()
    --段位
    self:FlushDuanWei3V3()
    --刷新领取奖励
    self:FlushReward3v3()
end

function KF3v3View:FlushBtn3V3()
    local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
    --self.node_list.btn_join_zhandui:SetActive(false)
    self.node_list.btn_goto_war:SetActive(is_in_zhandui)
    self.node_list["join_zhandui_remind"]:SetActive(RemindManager.Instance:GetRemind(RemindName.JoinZhanDui) > 0)
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_PVP)
    XUI.SetGraphicGrey(self.node_list.btn_goto_war, activity_info and activity_info.status ~= ACTIVITY_STATUS.OPEN)
    self.node_list.btn_to_zhandui:SetActive(not is_in_zhandui)
    local scene_type = Scene.Instance:GetSceneType()
    self.node_list.go_to_war_red_point:SetActive(activity_info and activity_info.status == ACTIVITY_STATUS.OPEN and
    (scene_type ~= SceneType.Kf_PvP_Prepare and scene_type ~= SceneType.Kf_PVP))
end

function KF3v3View:FlushLeftInfo3V3()
    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
    local zhandui_member_List = ZhanDuiWGData.Instance:GetZhanDuiMemberList()
    --成员父节点
    --self.node_list.pvp_member_comtainer:SetActive(is_in_zhandui)
    --无字
    -- if is_in_zhandui then
    --     self.node_list.shuoming:SetActive(false)
    --     -- if ZhanDuiWGData.Instance:GetZhanDuiMemberCount() == 1 then
    --     --     self.node_list.shuoming:SetActive(true)
    --     --     self.node_list.shuoming.text.text = Language.KuafuPVP.WuDuiYuan
    --     -- else
    --     --     self.node_list.shuoming:SetActive(false)
    --     -- end
    -- else
    --     self.node_list.shuoming:SetActive(true)
    --     self.node_list.shuoming.text.text = Language.KuafuPVP.KF3v3ShuoMing
    -- end


    --战令
    -- if is_in_zhandui then
    --     self.node_list.zhanling_icon_3v3:SetActive(true)
    --     self.node_list.zhanling_text_3v3.text.text = zhandui_info.zhandui_lingpai_name
    --     local cfg = KF3V3WGData.Instance:GetZhanLingCfg(zhandui_info.zhandui_lingpai_id)
    --     ZhanDuiWGCtrl.ChangeZhanLingTextColor(self.node_list.zhanling_text_3v3.text, cfg.color_type)
    --     self.node_list.zhanling_icon_3v3.image:LoadSprite(ZhanDuiWGData.Instance:GetZhanLingResPath(cfg.icon))
    -- -- else
    -- --      self.node_list.zhanling_icon_3v3:SetActive(false)
    -- end

    local myzhandui_name = string.format(Language.KuafuPVP.CurMyZhanDuiName, zhandui_info.name)
    --战队名
    self.node_list.zhandui_name_3v3.text.text = is_in_zhandui and myzhandui_name or Language.KuafuPVP.CurNotZhanDuiName
    --战队赛季胜场/总场
    if is_in_zhandui then
        local str = string.format(Language.KuafuPVP.WinCountTotalCount, zhandui_info.season_win_times,
            zhandui_info.season_match_times)
        self.node_list.zhandui_changci_right.text.text = str
    else
        self.node_list.zhandui_changci_right.text.text = ""
    end

    --个人赛季胜场/总场
    self.node_list.record:SetActive(is_in_zhandui)
    if role_info then
        local str = string.format(Language.KuafuPVP.WinCountTotalCount, role_info.season_win_times,
            role_info.season_match_times)
        self.node_list.person_changci_right.text.text = is_in_zhandui and str or Language.KuafuPVP.Wu
        --赛季连胜次数
        self.node_list.liansheng_right.text.text = string.format(Language.KuafuPVP.Chang,
            role_info.season_continue_win_times)
    else
        self.node_list.person_changci_right.text.text = ""
        self.node_list.liansheng_right.text.text = ""
    end
    --其他成员名字
    local other_member_list = ZhanDuiWGData.Instance:GetZhanDuiOtherMemberList()
    -- for i = 1, 2 do
    --     self.node_list["member_name_" .. i]:SetActive(other_member_list[i] ~= nil)
    --     if other_member_list[i] ~= nil then
    --         self.node_list["member_name_" .. i].text.text = other_member_list[i].name
    --     end
    -- end
    local my_info = RoleWGData.Instance:GetRoleInfo()
    --self.node_list.my_name.text.text = my_info.name
    --local is_vis, level = RoleWGData.Instance:GetDianFengLevel(my_info.level)
    --self.node_list.dianfen_img:SetActive(is_vis)
    -- if is_vis then
    --     self.node_list.level.text.text = level
    -- else
    --     self.node_list.level.text.text = "Lv."..level
    -- end
    --self.node_list.cap_value.text.text = my_info.capability

    --刷新成员
    -- for i=1,3 do
    --    self.node_list["Member"..i]:SetActive(is_in_zhandui)
    -- end

    for i = 1, 3 do
        if zhandui_member_List[i] then
            self.member_item_list[i]:SetData(zhandui_member_List[i])
        else
            self.member_item_list[i]:SetData({})
        end
    end

    if role_info then
        local start_time_table = os.date("*t", role_info.season_start_time)
        local end_time_table = os.date("*t", role_info.season_rank_reward_time)
        self.node_list.time.text.text = string.format(Language.Kuafu1V1.System3V3SeasonWillStart, start_time_table.month,
            start_time_table.day, end_time_table.month, end_time_table.day)
    else
        self.node_list.time.text.text = ""
    end
    --防止频繁切换页签请求数据
    if not self.last_req_member_info_time or self.last_req_member_info_time < Status.NowTime then
        for i = 1, 2 do
            if other_member_list[i] ~= nil then
                local idx = i
                BrowseWGCtrl.Instance:AllReqRoleInfo(other_member_list[idx].uid, nil, function(protocol)
                    -- if self.node_list["member_state_" .. idx] then
                    --     self.node_list["member_state_" .. idx].text.text = protocol.is_online == 0 and Language.KuafuPVP.OffLine or Language.KuafuPVP.OnLine
                    -- end
                end)
            end
        end
    end
    self.last_req_member_info_time = Status.NowTime + 2

    local zhou_str, start_time, end_time = BiZuoWGData.Instance:GetActOpenTimeStr(ACTIVITY_TYPE.KF_PVP)
    local time_str = start_time .. "-" .. end_time
    local open_time_str = string.format(Language.Kuafu1V1.OpenTimeZhouStr, zhou_str, time_str)
    self.node_list["3v3_open_tips"].text.text = open_time_str
end

--刷新领取奖励
function KF3v3View:FlushReward3v3()
    local reward_list = KF3V3WGData.Instance:GetMatchTimesRewardList()
    for i = 1, 5 do
        if reward_list[i] then
            self.reward_item_list[i]:SetData(reward_list[i])
        else
            self.reward_item_list[i]:SetData({})
        end
    end

    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    if role_info then
        local max_count = KF3V3WGData.Instance:GetCanGetMatchTimesRewardMaxCount()
        self.node_list.prog_upstar_progress.slider.value = SlderValue[max_count] or 0
    else
        self.node_list.prog_upstar_progress.slider.value = 0
    end
end

function KF3v3View:FlushDuanWei3V3()
    local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
    if is_in_zhandui then
        local grade_cfg, next_score, is_max = KF3V3WGData.Instance:GetDuanWeiCfgAll(zhandui_info.score)
        self.node_list.img_duanwei_pvp.text.text = grade_cfg.tier_name
        --ChangeToQualityText(self.node_list.img_duanwei_pvp.text, RankGradeEnum[grade_cfg.rank_id])
        --ZhanDuiWGCtrl.SetZhanDuiDuanWeiText(self.node_list.img_duanwei_pvp, grade_cfg)
        --self.node_list.duanwei_bg.image:LoadSprite(ResPath.GetNoPackPNG("a1_kf3v3_dw_" .. grade_cfg.grade))
        if not is_max then
            --self.node_list.duanwei_socre.text.text = (zhandui_info.score - grade_cfg.score) .. "/" .. next_score
            --self.node_list.progress.slider.value = (zhandui_info.score - grade_cfg.score) / next_score
            self.node_list.duanwei_socre.text.text = zhandui_info.score .. "/" .. next_score
            self.node_list.progress.slider.value = zhandui_info.score / next_score
        else
            self.node_list.duanwei_socre.text.text = zhandui_info.score .. "/" .. Language.KuafuPVP.Max
            self.node_list.progress.slider.value = 1
        end

        -- for i = 1, 5 do
        --     self.node_list["star"..i]:SetActive(i <= grade_cfg.star)
        -- end
        local star_res_list = GetStarImgResByStar(grade_cfg.star)
        for i = 1, 5 do
            self.node_list["star" .. i]:SetActive(true)
            self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
        end

        -- self.node_list.img_duanwei_pvp.image:LoadSprite(ResPath.GetNoPackPNG("a2_duanweiname"..grade_cfg.grade))
        -- self.node_list.img_duanwei_pvp.image:SetNativeSize()

        self.node_list.duanwei_icon_3v3.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz" .. grade_cfg.grade))
        self.node_list.duanwei_icon_3v3.image:SetNativeSize()
    end

    self.node_list.duanwei_container:SetActive(is_in_zhandui)
    self.node_list.duanwei_name_display:SetActive(is_in_zhandui)
    self.node_list.not_zhandui_duanwei:SetActive(not is_in_zhandui)

    -- 可获得积分剩余场次
    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    local cfg = KF3V3WGData.Instance:GetPKRewardCfg()
    local times = role_info.today_match_times
    local max_times = cfg.score_reward_time_limit
    local surplus_times = math.max(0, max_times - times)
    local format_str = surplus_times == 0 and Language.KuafuPVP.TodayAddScoreTips2 or
    Language.KuafuPVP.TodayAddScoreTips1
    --self.node_list["sorce_num"].text.text = string.format(format_str, surplus_times, max_times)
end

function KF3v3View:OnClickJoinZhanDui3V3()
    -- self.close_param = {}
    -- self.close_param.is_goto_war = true
    -- self:Close()
    -- KF3V3WGCtrl.Instance:GoToNpcJoin()
    ZhanDuiWGCtrl.Instance:OpenJoinView()
end

function KF3v3View:OnClickTips3V3()
    KF3V3WGCtrl.Instance:OpenTabRuleTip(10)
end

function KF3v3View:OnClickDuanweiReward()
    ZhanDuiWGCtrl.Instance:OpenSeasonReward()
end

function KF3v3View:OnClickZhanDuiRank()
    ZhanDuiWGCtrl.Instance:OpenZhanDuiRankView()
end

function KF3v3View:OnClickGoToWar()
    if KF3V3WGData.Instance:GetIsMatching() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.MatchingCanNotDoThis2)
        return
    end

    --已经在准备场景直接返回
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.Kf_PvP_Prepare then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.MatchingCanNotDoThis2)
        return
    end
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_PVP)
    if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
        if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.StandbyTimeTips)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
        end
        return
    end


    -- 活动要队伍里是同战队的才能进入，否则就需要退队(进去后还可以组队)
    local change_index , member_list = NewTeamWGData.Instance:GetTeamMemberList()
    for k,v in ipairs(change_index) do
        if v and member_list[v] then
            local is_my_zhandui = ZhanDuiWGData.Instance:GetTargetIsMyZhanDui(member_list[v].role_id)
            if not is_my_zhandui then
                if SocietyWGData.Instance:GetIsTeamLeader() then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.LeaderEnterPrepareSceneTip2)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.MemberEnterPrepareSceneTip2)
                end
                
                return
            end
        end
    end

    if activity_info.status == ACTIVITY_STATUS.OPEN then
        self.close_param = {}
        self.close_param.is_goto_war = true
        self:Close()
        KF3V3WGCtrl.Instance:EnterPrepareScene()
    end
end

function KF3v3View:OnClickClose()
    self:Close()
end

function KF3v3View:OnZhanDuiInfoChange3V3(notify_reason)
    self:FlushLeftInfo3V3()
    self:FlushDuanWei3V3()
    self:FlushBtn3V3()
end

function KF3v3View:OnRoleInfoChange3V3()
    self:FlushReward3v3()
end

function KF3v3View:OnJoinZhanduiRemindChange(remind_name, num)
    self.node_list["join_zhandui_remind"]:SetActive(num > 0)
end

---奖励item
KF3V3FetchRewardItemRender = KF3V3FetchRewardItemRender or BaseClass(BaseRender)
function KF3V3FetchRewardItemRender:__init()
    XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClickFetch, self))
end

function KF3V3FetchRewardItemRender:OnFlush()
    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    self.node_list.count.text.text = string.format(Language.KuafuPVP.ChangJiangLi, self.data.match_times)
    local flag_list = ZhanDuiWGData.Instance:GetFetchFlagList()
    if flag_list == nil or role_info == nil then return end
    local has_fetch = flag_list[32 - self.data.seq] == 1
    local is_show_red_point = role_info.today_match_times >= self.data.match_times and not has_fetch
    self.node_list.effect:SetActive(is_show_red_point)
    self.node_list.red_point:SetActive(is_show_red_point)
    self.node_list.yilingqu:SetActive(has_fetch)
end

function KF3V3FetchRewardItemRender:OnClickFetch()
    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    local flag_list = ZhanDuiWGData.Instance:GetFetchFlagList()
    if flag_list == nil or role_info == nil then return end
    local has_fetch = flag_list[32 - self.data.seq] == 1
    local is_show_red_point = role_info.today_match_times >= self.data.match_times and not has_fetch
    if role_info.today_match_times < self.data.match_times then
        -- KF3V3WGCtrl.Instance:OpenFetchView()
        if not IsEmptyTable(self.data) then
            TipWGCtrl.Instance:OpenItem({ item_id = self.data.reward_gift }, ItemTip.FROM_NORMAL, nil)
        end
        return
    end
    if not is_show_red_point then
        -- KF3V3WGCtrl.Instance:OpenFetchView()
        if not IsEmptyTable(self.data) then
            TipWGCtrl.Instance:OpenItem({ item_id = self.data.reward_gift }, ItemTip.FROM_NORMAL, nil)
        end
        return
    end

    ZhanDuiWGCtrl.Instance:SendZhanDuiFetchJieDuanReward(self.data.seq)
end

---成员item KF3v3MemberItemRender
KF3v3MemberItemRender = KF3v3MemberItemRender or BaseClass(BaseRender)
function KF3v3MemberItemRender:__init()
    --XUI.AddClickEventListener(self.node_list.jiahao_btn, BindTool.Bind(self.OnClickZhanDuiInvite, self))
    XUI.AddClickEventListener(self.node_list.jiahao_btn2, BindTool.Bind(self.OnClickZhanDuiInvite, self))
end

function KF3v3MemberItemRender:__delete()
    if self.role_model then
        self.role_model:DeleteMe()
        self.role_model = nil
    end
end

function KF3v3MemberItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.node_list.wu_menber:SetActive(true)
        self.node_list.menber_data:SetActive(false)
        local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
        -- local desc = is_in_zhandui and Language.KuafuPVP.InvitePlayer or Language.KuafuPVP.ViewName_CreateZhanDui
        -- self.node_list["Text"].text.text = desc
    else
        self.node_list.wu_menber:SetActive(false)
        self.node_list.menber_data:SetActive(true)
        local my_info = RoleWGData.Instance:GetRoleInfo()
        self.node_list["zhuangtai"]:SetActive(self.data.uid ~= my_info.role_id)

        local flush_fun = function(protocol)
            if not self.node_list then
                return
            end
            --设置状态
            self.node_list["zhuangtai"].text.text = protocol.is_online == 1 and Language.KuafuPVP.OnLine or
            Language.KuafuPVP.OffLine
            --设置模型
            if nil == self.role_model then
                self.role_model = RoleModel.New()
                local display_data = {
                    parent_node = self.node_list["display"],
                    camera_type = MODEL_CAMERA_TYPE.BASE,
                    -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
                    rt_scale_type = ModelRTSCaleType.M,
                    can_drag = true,
                }
        
                self.role_model:SetRenderTexUI3DModel(display_data)

                -- self.role_model:SetUI3DModel(self.node_list.display.transform,
                --     self.node_list.display.event_trigger_listener,
                --     1, false, MODEL_CAMERA_TYPE.BASE)
            end

            if self.role_model then
                local ignore_table = { ignore_wing = true, ignore_jianzhen = true, ignore_halo = true,
                    ignore_shouhuan = true, ignore_tail = true, ignore_waist = true }
                self.role_model:SetModelResInfo(protocol, ignore_table)
            end
            --设置等级
            local is_vis, level = RoleWGData.Instance:GetDianFengLevel(protocol.level)
            self.node_list.dianfen_img:SetActive(is_vis)
            if is_vis then
                self.node_list.level.text.text = level
            else
                self.node_list.level.text.text = "Lv." .. level
            end
            local score = self.data.cross3v3_score or 0
            self.node_list["score"].text.text = string.format(Language.KuafuPVP.JiFenStr, score)
            --设置名字和vip
            -- self.node_list.vip_level:SetActive(self.data.vip_level >= 1)
            -- self.node_list.vip_level.image:LoadSprite(ResPath.GetVipIcon("vip"..self.data.vip_level))
            -- self.node_list.vip_level.image:SetNativeSize()
            self.node_list.name.text.text = protocol.role_name
            --self.node_list.cap_value.text.text = protocol.capability
        end
        BrowseWGCtrl.Instance:BrowRoelInfo(self.data.uid, flush_fun)
    end
end

function KF3v3MemberItemRender:OnClickZhanDuiInvite() --根据是否加入战队做不同处理
    local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
    if is_in_zhandui then
        --KF3V3WGCtrl.Instance:Open3V3View(TabIndex.kf_pvp_zhandui_info)
        ZhanDuiWGCtrl.Instance:OpenInviteView()
    else
        ZhanDuiWGCtrl.Instance:OpenCreateView()
    end
end
