
--boss击杀历史记录
BossKillRecordView = BossKillRecordView or BaseClass(SafeBaseView)

function BossKillRecordView:__init()
    self.view_name = "BossKillRecordView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third3_panel")
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_kill_record")
	self:SetMaskBg(true)
end

function BossKillRecordView:OpenCallBack()

end

function BossKillRecordView:LoadCallBack()
    
	self.node_list["layout_commmon_third_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
	self.node_list["layout_commmon_third_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	self.kill_record_list = AsyncListView.New(RecordRender, self.node_list["ph_kill_record_list"])
end

function BossKillRecordView:ReleaseCallBack()
	if nil ~= self.kill_record_list then
		self.kill_record_list:DeleteMe()
		self.kill_record_list = nil
    end
    self.boss_data = nil
end

function BossKillRecordView:CloseCallBack()
    self.boss_data = nil
end

function BossKillRecordView:SetData(data, is_from_first_kill)
    self.boss_data = data
    self.is_from_first_kill = is_from_first_kill
end

function BossKillRecordView:OnFlush()
    self.node_list.title_view_name.text.text = self.is_from_first_kill and Language.Boss.FirstKillRank or Language.Boss.KillHistory
    local record_list = {}
    if IsEmptyTable(self.boss_data) then
        record_list = BossWGData.Instance:GetBossKillRecordData()
    else
        record_list = self.boss_data.kill_history
    end
    
	if #record_list == 0 then
		--self.node_list["img_no_record"]:SetActive(true)
	else
		--self.node_list["img_no_record"]:SetActive(false)
    end
    -- local need_sort = false
    -- if not IsEmptyTable(record_list) then
    --     for k, v in pairs(record_list) do
    --         if v.is_first_kill then
    --             need_sort = true
    --             break
    --         end
    --     end
    -- end
    -- if need_sort then
    --     table.sort(record_list, SortTools.KeyUpperSorters("is_first_kill", "kill_timestamp"))
    -- end
	if record_list ~= nil and not IsEmptyTable(record_list) then
		self.kill_record_list:SetDataList(record_list)
	end
end

-------------------------------------------------------------------------------------------
RecordRender = RecordRender or BaseClass(BaseRender)
function RecordRender:__delete()

end

function RecordRender:LoadCallBack()

end

function RecordRender:OnFlush()
	if self.data ~= nil then
		local time = self.data.kill_timestamp 
		if time ~= 0 then
			time = os.date("%m-%d  %X", time)
			self.node_list["time"].text.text = time
		else
			self.node_list["time"].text.text = ""
		end
		local role_name = self.data.killer_role_name 
        self.node_list["item_name"].text.text = role_name
        self.node_list.first_kill_icon:SetActive(self.data.is_first_kill and self.data.is_first_kill == 1)
	end
end

