--上古遗迹，打宝boss
BossNormalHurtView = BossNormalHurtView or BaseClass(SafeBaseView)

function BossNormalHurtView:__init()
	self.default_index = 1
	self.active_close = false
	self.view_layer = UiLayer.MainUILow

	self.view_name = "BossNormalHurtView"
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
    self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_hurt_info")
	self.view_cache_time = 0
	self.open_tween = nil
	self.close_tween = nil
	self.next_call_time = 0
    self.convene_cd = 60.5
    self.dec_convene_cd = 0.5
end

function BossNormalHurtView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_swithc_list, BindTool.Bind1(self.OnClicSwithList, self))
    XUI.AddClickEventListener(self.node_list.xiezhu_reward_btn, BindTool.Bind1(self.OpenXuezhuReward, self))
    XUI.AddClickEventListener(self.node_list.btn_convene,BindTool.Bind1(self.OnClickBossConvene,self))

    self:InitList()
    -- self:Flush()

    self.node_list["convenel_count_down"].text.text = Language.Boss.ConvenelCountDown2
    self.node_list["convene_btn_mask"].image.fillAmount = 1
end

function BossNormalHurtView:InitList()
    self.rank_list = StrengthenAsyncListView.New(BossHurtRender, self.node_list.TaskList1)
    self.rank_list:SetCreateCellCallBack(BindTool.Bind(self.OnCreateCell, self))
end

-- function BossNormalHurtView:Init()
-- 	local mainui_ctrl = MainuiWGCtrl.Instance

-- 	local parent = mainui_ctrl:GetTaskOtherContent()
-- 	self.node_list.task_root_view.transform:SetParent(parent.transform)
--  self.node_list.task_root_view.transform.anchoredPosition = Vector3(0, 0, 0)
--  self.node_list.task_root_view.transform.localScale = Vector3(1, 1, 1)

-- 	-- mainui_ctrl:SetTaskPanel(false,151,-136.1, true)
-- 	-- mainui_ctrl.view:SetTaskCallBack(function (ison)
-- 	-- 	if self.node_list.task_root_view then
-- 	-- 		self.node_list.task_root_view:SetActive(ison)
-- 	-- 	end
-- 	-- end)
--     mainui_ctrl:SetLeftPanelShowContent()
-- end

function BossNormalHurtView:ShowIndexCallBack()
    -- local init_callback = function ()
    --     self:Init()
    -- end
    self:SetGuajiPosInfo()
    -- local mainuictrl = MainuiWGCtrl.Instance
    -- mainuictrl:AddInitCallBack(nil,init_callback)
	-- self:Flush()

    ViewManager.Instance:AddMainUIFuPingChangeList(self)
    ViewManager.Instance:AddMainUIRightTopChangeList(self)
    MainuiWGCtrl.Instance:SetShrinkButtonIsOn(false)
end

function BossNormalHurtView:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
    self.next_call_time = 0

    if self.next_call_timer then
		GlobalTimerQuest:CancelQuest(self.next_call_timer)
		self.next_call_timer = nil
	end

    if self.convene_cd_delay then
        GlobalTimerQuest:CancelQuest(self.convene_cd_delay)
        self.convene_cd_delay = nil
    end
end

function BossNormalHurtView:OnCreateCell(cell)
    cell:SetParentScrollRect(self.node_list.TaskList1.scroll_rect)
end

function BossNormalHurtView:CloseCallBack()
    ViewManager.Instance:RemoveMainUIFuPingChangeList(self)
    ViewManager.Instance:RemoveMainUIRightTopChangeList(self)
    -- MainuiWGCtrl.Instance:RevertLeftPanelStatus()
    -- if self.node_list.task_root_view then
    --     self.node_list.task_root_view.transform:SetParent(self.root_node_transform, false)
    -- end
end

function BossNormalHurtView:FlushConveneCD()
    local last_time = BossAssistWGData.Instance:GetConveneTime()
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    if last_time + 60 > cur_time and not self.convene_cd_delay then
        self.convene_cd = last_time + 60 - cur_time + 0.5
        self.convene_cd_delay = GlobalTimerQuest:AddRunQuest(function ()
            self:CacularConveneCD()
        end, self.dec_convene_cd)
        self:CacularConveneCD()
    end
end

function BossNormalHurtView:OnClickBossConvene()
    if self.convene_cd_delay then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.BossConveneCD)
        return
    end

    if not GuildWGData.Instance:HasGuild() then
        local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("guild")
        if is_open then
            local ok_func = function ()
                FunOpen.Instance:OpenViewByName(GuideModuleName.Guild)--, TabIndex.guild_guildlist)
            end
            local ok_str = Language.Boss.BossConveneNoGuild
            local text_dec = Language.Boss.BossConveneGuild
            TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func, nil, nil, nil, nil, nil, ok_str)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
        end
        return
    end
    
    local all_hurt_info = BossAssistWGData.Instance:GetHurtInfo()
    local cur_boss_id = all_hurt_info.target_boss_id
    local boss_key = all_hurt_info.monster_key
    local boss_cfg = BossWGData.Instance:GetBossAllInfoByBossId(cur_boss_id)
    local monster_info = BossWGData.Instance:GetMonsterInfo(cur_boss_id)

    local scene_id = Scene.Instance:GetSceneId()
    local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0

    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil then
        return
    end
    
    local x, y = main_role:GetLogicPos()

    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    
    local pos_msg = string.format(Language.Boss.BossConvenePos, scene_id,x, y)
    local name_str = string.format(Language.Boss.BossConveneName, boss_cfg.boss_name)
    local message = string.format(Language.Boss.BossConveneText, pos_msg, monster_info.boss_jieshu, name_str)
    message = message .. string.format(Language.Boss.BossConveneLink, scene_id, x, y, main_role_vo.cur_plat_name, cur_boss_id, boss_key,scene_key)
    ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, message, CHAT_CONTENT_TYPE.TEXT ,nil, nil, true)
    self.convene_cd = 60.5
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    BossAssistWGData.Instance:SaveConveneTime(cur_time)
    self.convene_cd_delay = GlobalTimerQuest:AddRunQuest(function ()
        self:CacularConveneCD()
    end, self.dec_convene_cd)
    self:CacularConveneCD()
end

function BossNormalHurtView:CacularConveneCD()
    if not self.node_list or not self.node_list["convenel_count_down"] then
        return
    end

    self.convene_cd = self.convene_cd - self.dec_convene_cd
    if self.convene_cd < 0 then
        self.convene_cd = 0
    end

    if self.convene_cd > 0 then
        self.node_list["convenel_count_down"].text.text = string.format(Language.Boss.ConvenelCountDown1, math.ceil(self.convene_cd))
    else
        self.node_list["convenel_count_down"].text.text = Language.Boss.ConvenelCountDown2
    end

    local perscent = 1 - self.convene_cd / 60
    if math.abs(perscent - self.node_list["convene_btn_mask"].image.fillAmount) > 0.2 then
        self.node_list["convene_btn_mask"].image.fillAmount = perscent
    else
        self.node_list["convene_btn_mask"].image:DOFillAmount(perscent, 1):SetEase(DG.Tweening.Ease.Linear)
    end

    if self.convene_cd == 0 then
        self.node_list["convenel_count_down"].text.text = Language.Boss.ConvenelCountDown2
        GlobalTimerQuest:CancelQuest(self.convene_cd_delay)
        self.convene_cd_delay = nil
    end
end


function BossNormalHurtView:OnClicSwithList()
	GlobalEventSystem:Fire(OtherEventType.SHOW_BOSS_CHANGE, false)
end

function BossNormalHurtView:SetGuajiPosInfo() --如果是召唤的boss这儿会有坑，策划说不管
    local all_hurt_info = BossAssistWGData.Instance:GetHurtInfo()
    local cur_boss_id = all_hurt_info.target_boss_id
    local boss_cfg = BossWGData.Instance:GetBossAllInfoByBossId(cur_boss_id)

    if boss_cfg then
        local range = BossWGData.Instance:GetMonsterRangeByid(cur_boss_id)
        BossWGCtrl.Instance:SetGuaJiPosInfo(boss_cfg.scene_id, boss_cfg.x_pos or boss_cfg.flush_pos_x, boss_cfg.y_pos or boss_cfg.flush_pos_y, range, cur_boss_id)
    end
end

function BossNormalHurtView:OpenXuezhuReward()
    local is_empty = BossXiezhuWGData.Instance:GetIsEmptyInvokeList()
    if not is_empty then
        ViewManager.Instance:Open(GuideModuleName.BossXiezhuListView)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.EmptyXiezhuMsg)    
    end
end

function BossNormalHurtView:OnFlush()
    self:FlushConveneCD()

	local all_hurt_info = BossAssistWGData.Instance:GetHurtInfo()
    local damage_list = all_hurt_info.hurt_info or {}
    local boss_id = all_hurt_info.target_boss_id
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.VIP_BOSS or scene_type == SceneType.WorldBoss or scene_type == SceneType.DABAO_BOSS or scene_type == SceneType.XianJie_Boss then
        if boss_id ~= 0 then
            local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_id)
            --魔王巢穴和世界boss
            if boss_cfg and boss_cfg.drop_type then
                if boss_cfg.drop_type == BOSS_DROP_OWN_TYPE.BOSS_DROP_OWN_TYPE_HURT_RANK then
                    if boss_cfg.drop_param == 1 then
                        if scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS then
                            local is_show_person, can_get = BossWGData.Instance:GetIsPersonFirstKilledByBossId(boss_id)
                            if not (is_show_person and not can_get) then
                                self.node_list.des_text_info.text.text = string.format(Language.Boss.DemageGetReward33, boss_cfg.drop_param)
                            else
                                self.node_list.des_text_info.text.text = string.format(Language.Boss.DemageGetReward3, boss_cfg.drop_param)
                            end
                        else
                            self.node_list.des_text_info.text.text = Language.Boss.DemageFirstGetAward
                        end
                    else
                        if scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS then
                            local is_show_person, can_get = BossWGData.Instance:GetIsPersonFirstKilledByBossId(boss_id)
                            if is_show_person and not can_get then
                                self.node_list.des_text_info.text.text = string.format(Language.Boss.DemageGetReward2, boss_cfg.drop_param)
                            else
                                self.node_list.des_text_info.text.text = string.format(Language.Boss.DemageGetReward22, boss_cfg.drop_param)
                            end
                        else
                            self.node_list.des_text_info.text.text = string.format(Language.Boss.DemageGetReward, boss_cfg.drop_param)
                        end
                    end
                elseif boss_cfg.drop_type == BOSS_DROP_OWN_TYPE.BOSS_DROP_OWN_TYPE_MAX_HURT_TEAM then
                    self.node_list.des_text_info.text.text = Language.Boss.DemageFirstGetAward
                elseif boss_cfg.drop_type == BOSS_DROP_OWN_TYPE.BOSS_DROP_OWN_TYPE_MAX_HURT_ROLE then
                    self.node_list.des_text_info.text.text = Language.Boss.DemagePersonFirstGetAward
                end
            else
                print_error("取不到boss配置或者boss配置有误， boss_id ==",boss_id)
            end
        end
    else
        if scene_type == SceneType.CROSS_AIR_WAR then
            self.node_list.des_text_info.text.text = Language.Boss.DemageFirstBetterGetAward
        elseif CrossTreasureWGData.Instance:IsServerTreasureBeastStatusBoss() then
            self.node_list.des_text_info.text.text = Language.Boss.DemageCatchbeastGetAward 
        else
            self.node_list.des_text_info.text.text = Language.Boss.DemageFirstGetAward
        end
    end

    if scene_type == SceneType.XianJie_Boss then
        local text = self.node_list.des_text_info.text.text
        local num = XianJieBossWGData.Instance:GetReliveDiscountPercent()
        local percent = num / 100 .."%"
        local hurt_str = string.format(Language.XianJieBoss.DeathDes, percent)
        self.node_list.des_text_info.text.text = text .. "\n" .. hurt_str
    end

    if scene_type == SceneType.WORLD_TREASURE_JIANLIN then
        self.node_list.des_text_info.text.text = Language.WorldTreasure.KillStr
    end

    if self.rank_list ~= nil then
        self.rank_list:SetDataList(damage_list)
    else
        self:InitList()
        self.rank_list:SetDataList(damage_list)
    end

	local main_role_hurt = all_hurt_info.main_role_hurt or 0

    --当我是协助者时  对boss的伤害
    local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
    if state == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER then
        main_role_hurt = BossXiezhuWGData.Instance:GetMyXiezhuHurt()
    end

	self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(main_role_hurt)

	-- self.node_list.per_bg.slider.value = main_role_hurt / (BossAssistWGData.Instance:GetNormalHurtInfoMaxValue() or 1)

	-- self.node_list["btn_swithc_list"]:SetActive(#damage_list > 0)
    local scene_type = Scene.Instance:GetSceneType()
    self.node_list["btn_convene"]:SetActive(false)
    if scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS then
        local role_level = RoleWGData.Instance:GetRoleLevel()
        local convene_level = BossWGData.Instance:GetConveneOpenLevel()
        self.node_list["btn_convene"]:SetActive(#damage_list > 0 and role_level >= convene_level)
    end

    self:FlushXieZhuInfo()
    -- self.node_list["tips_info"]:SetActive(true)
    -- self.node_list["xiezhu_info"]:SetActive(false)
    if self.node_list and self.node_list.TaskList1 and self.node_list.TaskList1.scroll_rect and self.node_list.TaskList1.scroll_rect.content then
        local rect_transform = self.node_list.TaskList1.scroll_rect.content:GetComponent(typeof(UnityEngine.RectTransform))
        local hight = rect_transform.rect.height
        local is_enabled = self.node_list.TaskList1.rect.rect.height <= hight
        self.node_list.TaskList1.scroll_rect.enabled = is_enabled
    end
end

function BossNormalHurtView:FlushXieZhuInfo()
    local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
    local xiezhu_count = BossXiezhuWGData.Instance:GetAssistCount()
    local xiezhu_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BOSS_ASSIST)
    local can_xiezhu = xiezhu_num < xiezhu_count and state == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER
    self.node_list["xiezhu_info"]:SetActive(can_xiezhu)
    self.node_list["tips_info"]:SetActive(not can_xiezhu)
    local xiezhu_num_text = string.format(Language.BossXiezhu.XiezhuNumText, xiezhu_count - xiezhu_num, xiezhu_count)
    self.node_list["xiuzhu_num_text"].text.text = xiezhu_num_text
end

--------------------------------------------------------------------------------
BossHurtRender = BossHurtRender or BaseClass(BaseRender)

function BossHurtRender:InitListener()
    self.on_begin_drag = BindTool.Bind1(self.OnBeginDrag, self)
    self.on_drag = BindTool.Bind1(self.OnDrag, self)
    self.on_end_drag = BindTool.Bind1(self.OnEndDrag, self)
    if self.node_list.button.event_trigger_listener ~= nil then
        self.node_list.button.event_trigger_listener:AddBeginDragListener(self.on_begin_drag)
        self.node_list.button.event_trigger_listener:AddDragListener(self.on_drag)		--按下
        self.node_list.button.event_trigger_listener:AddEndDragListener(self.on_end_drag)
    end
end

function BossHurtRender:SetParentScrollRect(parent_scroll_rect)
    self.parent_scroll_rect = parent_scroll_rect
end

function BossHurtRender:OnBeginDrag(event_data)
    if not self.parent_scroll_rect then
        return
    end
    self.parent_scroll_rect:OnBeginDrag(event_data)
end

function BossHurtRender:OnDrag(event_data)
    if not self.parent_scroll_rect then
        return
    end
    self.parent_scroll_rect:OnDrag(event_data)
end

function BossHurtRender:OnEndDrag(event_data)
    if not self.parent_scroll_rect then
        return
    end
    self.parent_scroll_rect:OnEndDrag(event_data)
end


function BossHurtRender:__delete()
    self.parent_scroll_rect = nil
end

function BossHurtRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.attk, BindTool.Bind(self.OnClickAttk, self))
    XUI.AddClickEventListener(self.node_list.button, BindTool.Bind(self.OpenCustomMenu, self))
    self:InitListener()
end

--是否是未组队或者是单人组队
function BossHurtRender:GetIsSingle()
    if self.data.is_in_team == 0 then
        return true
    end
    if self.data.is_in_team == 1 then
        local count = 0
        for k,v in ipairs(self.data.team_uuid_least) do
            if v.uuid and v.uuid.temp_high > 0 then
                count = count + 1
            end
        end
        return count == 1
    end
    return false
end

function BossHurtRender:OnClickAttk()
    if nil == self.data then return end
    local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() then
		return
    end
    if self.data.is_online == 0 and self:GetIsSingle() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NotOnLine)
        return
    end

    local my_vo = main_role:GetVo()
	local my_attack_mode = my_vo.attack_mode
    if my_attack_mode == ATTACK_MODE.PEACE then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.ModeCantAttack)
        return
    end
    
    local scene_id = Scene.Instance:GetSceneId()
    local cur_obj = nil
    local is_had_target = false
    local is_enemy, reason
    if self.data.is_in_team == 0 then
        local obj = Scene.Instance:GetRoleByUUID(self.data.uuid)
        if obj then
            is_had_target = true
            is_enemy, reason = Scene.Instance:IsEnemy(obj)
            if is_enemy then
                cur_obj = obj
            end
        end
    else
        for k,v in ipairs(self.data.team_uuid_least) do
            local obj = Scene.Instance:GetRoleByUUID(v.uuid)
            if obj then
                is_had_target = true
                is_enemy, reason = Scene.Instance:IsEnemy(obj)
                if is_enemy then
                    cur_obj = obj
                    break
                end
            end
        end
    end

    if cur_obj then
        GuajiWGCtrl.Instance:ClearTemporary()
        if GuajiCache.guaji_type == GuajiType.None then
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        end

        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic ~= nil then
            scene_logic:ClearGuaJiInfo()
        end
        
        GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, cur_obj, SceneTargetSelectType.SCENE)
        if main_role:IsInSafeArea() or cur_obj:IsInSafeArea() then
            if cur_obj:IsInSafeArea() then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.TargetIsSafe)
            end
            GuajiWGCtrl.Instance:MoveToPos(scene_id, cur_obj.pos_x, cur_obj.pos_y, 1)
        end
    else
        if is_had_target then
            SysMsgWGCtrl.Instance:ErrorRemind(reason)
        else --没找到目标，不在视野范围内
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NotInHorizon)
        end
    end
end

function BossHurtRender:OnFlush()
    if not self.data then
        return
    end
    local name = self.data.name --self.data.is_in_team == 0 and self.data.name or string.format(Language.Boss.BossTeam, self.data.name)
   
    if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
        name = Split(name, "_s")[1]
    end
    local name_str = self.data.is_mine and ToColorStr(name, COLOR3B.D_GLOD) or name
    self.node_list.name.text.text = name_str
    
    local is_in_team = self.data.is_in_team == 1
    self.node_list.team_flag:SetActive(is_in_team)

	local damage = CommonDataManager.ConverNumber(self.data.hurt)
	self.node_list.damage.text.text = damage
    self.node_list.per_bg.slider.value = self.data.hurt / BossAssistWGData.Instance:GetNormalHurtInfoMaxValue()
	
    local show_perfect = self.index <= 2 and Scene.Instance:GetSceneType()== SceneType.VIP_BOSS
    self.node_list.perfect_mark:SetActive(show_perfect)
    if show_perfect then
        local bundle, asset = ResPath.GetMainUIIcon("a3_zjm_damage_rank" .. self.index)
        self.node_list.damage_rank_img.image:LoadSprite(bundle, asset, function()
            self.node_list.damage_rank_img.image:SetNativeSize()
        end)
    end

    local bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_4")

	if self.index < 4 then
        bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_" .. self.index)
		self.node_list["icon"]:SetActive(true)
		self.node_list["icon"].image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
		self.node_list.num.text.text = ""
	else
		self.node_list["icon"]:SetActive(false)
		self.node_list.num.text.text = self.index
    end

    self.node_list.per_bg.image:LoadSprite(bg_bundle, bg_asset)

    self:FlushBtnState()

    self:FlushXianJieState()
end

function BossHurtRender:FlushXianJieState()
end

function BossHurtRender:FlushBtnState()
    local is_show = true
    local cur_obj = nil
    local is_enemy, reason
    if self.data.is_in_team == 0 then
        local obj = Scene.Instance:GetRoleByUUID(self.data.uuid)
        if obj then
            cur_obj = obj
        end
    else
        for k,v in ipairs(self.data.team_uuid_least) do
            local obj = Scene.Instance:GetRoleByUUID(v.uuid)
            if obj then
                is_enemy, reason = Scene.Instance:IsEnemy(obj)
                if is_enemy then
                    cur_obj = obj
                    break
                end
            end
        end
    end
    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil then
        return
    end
    
    local attack_mode = main_role:GetVo().attack_mode
    if cur_obj then
        local lover_id = main_role:GetVo().lover_uid or 0
        local is_lover = lover_id ~= 0 and lover_id == cur_obj:GetVo().role_id
        if is_lover and (attack_mode == ATTACK_MODE.GUILD or attack_mode == ATTACK_MODE.PEACE)  then
           is_show = false
        end
        local is_team = SocietyWGData.Instance:IsTeamMember(cur_obj:GetOriginId())  
        if is_team then
            is_show = false
        end
    end

    local is_xiezhu = BossXiezhuWGData.Instance:IsGotoXiezhu()
    local xiezhu_role_id = BossXiezhuWGData.Instance:GetGoXiezhuRoleId()
    if is_xiezhu then
        if self.data.is_in_team == 0 then
            if xiezhu_role_id == self.data.uuid.temp_low then
                is_show = false
            end
        else
            for k,v in ipairs(self.data.team_uuid_least) do
                if xiezhu_role_id == v.uuid.temp_low then
                    is_show = false
                    break
                end
            end
        end
    end
    --世界boss不显示attk
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.CROSS_AIR_WAR then
        is_show = false
    end
    local is_mine = self.data.is_mine
	self.node_list["attk"]:SetActive(is_show and not is_mine)
end

function BossHurtRender:OnSelectChange(is_select)
	self.node_list["high"]:SetActive(is_select)
end

function BossHurtRender:ReleaseCallBack()
end

function BossHurtRender:OpenCustomMenu()
    if self.data == nil then
        return
    end

    local main_role_id = RoleWGData.Instance:InCrossGetOriginUid()
    local role_id = self.data.uuid.temp_low
    if role_id == main_role_id then
        return
    end

    local my_server_id = RoleWGData.Instance:GetOriginServerId()
    local my_plat_type = RoleWGData.Instance:GetPlatType()
    local is_cross = my_server_id ~= self.data.server_id or my_plat_type ~= self.data.plat_type
    local member_count
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        member_count = CrossTeamWGData.Instance:GetTeamMemberCount()
    else
        local member_list = SocietyWGData.Instance:GetTeamMemberList()
        member_count = #member_list 
    end
    local str_tip, oprate
    local items = {
        Language.Menu.ShowInfo,
        -- Language.Menu.GiveFlower,
        --Language.Menu.Profess, 
    }
    if not is_cross then
        if SocietyWGData.Instance:GetIsMyFriend(role_id) then
            table.insert(items, Language.Menu.PrivateChat)
        else
            table.insert(items, Language.Menu.AddFriend)
        end
    end

    local is_enemy = false
    if self.data.is_in_team == 0 then
        local obj = Scene.Instance:GetRoleByUUID(self.data.uuid)
        if obj then
            is_enemy = Scene.Instance:IsEnemy(obj)
        end
    else
        for k,v in ipairs(self.data.team_uuid_least) do
            local obj = Scene.Instance:GetRoleByUUID(v.uuid)
            if obj then
                is_enemy = Scene.Instance:IsEnemy(obj)
                if is_enemy then
                    break
                end
            end
        end
    end

    if is_enemy then
        table.insert(items, Language.Menu.AttackOherRole)
    end

    if not is_cross then
        if self.data.is_in_team == 0 and member_count >= 0 then
            table.insert(items, Language.Menu.InviteTeam)
        elseif self.data.is_in_team > 0 and member_count <= 0 then
            table.insert(items, Language.Menu.ApplyTeam)
        elseif self.data.is_in_team > 0 and member_count > 0 then
            local is_same_team = SocietyWGData.Instance:GetIsSameTeam(self.data.team_index)
            if not is_same_team then
                table.insert(items, Language.Menu.MergeTeam)
            end
        end
    elseif Scene.Instance:GetIsOpenCrossViewByScene() then
        if self.data.is_in_team == 0 and member_count >= 0 then
            table.insert(items, Language.Menu.InviteTeam)
        elseif self.data.is_in_team > 0 and member_count <= 0 then
            table.insert(items, Language.Menu.ApplyTeam)
        elseif self.data.is_in_team > 0 and member_count > 0 then
            local is_same_team = SocietyWGData.Instance:GetIsSameTeam(self.data.team_index)
            if not is_same_team then
                table.insert(items, Language.Menu.MergeTeam)
            end
        end
    end

    if items == nil then
        return
    end
    local team_index = self.data.team_index
    BrowseWGCtrl.Instance:ShowOtherRoleInfo(role_id, self:GetPos(self.node_list["button"]), is_cross, self.data.uuid.temp_high, items, 
    BindTool.Bind(self.MenuClickCallBack, self, is_cross, team_index, items))
end

function BossHurtRender:GetPos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
    if nil == main_view or not main_view:IsOpen() then
        return
    end
    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
    local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
    local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x - 282
    y = local_position_tbl.y - 214
    return Vector2(x, y)
end

--index 在items 里面的索引 param 操作的目标  item_data = items
function BossHurtRender:MenuClickCallBack(is_cross, team_index, items, index, sender, param, item_data)
    if self.data == nil then
        return
    end
    
    local menu_text = items[index]
    local role_id = param.role_id
    if menu_text == Language.Menu.ShowInfo then --查看资料
        local role_id = param.role_id
        BrowseWGCtrl.Instance:OpenWithUid(role_id,nil,nil,param.plat_type,1)
    elseif menu_text == Language.Menu.GiveFlower then
        if not is_cross then
            FlowerWGCtrl.Instance:OpenSendFlowerView(param.role_id, param.role_name, param.sex, param.prof)
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
        end
        
    -- elseif menu_text == Language.Menu.Profess then
    --     if not is_cross then
    --         local is_friend = SocietyWGData.Instance:CheckIsFriend(param.role_id)
    --         if is_friend then
    --             -- ProfessWallWGData.Instance:SetDefaultInfo(param.role_id,nil)
    --             -- ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
    --         else
    --             local data = {}
    --             data.role_id = param.role_id
    --             data.role_name = param.role_name
    --             data.sex = param.sex
    --             data.prof = param.prof
    --             SocietyWGCtrl.Instance:OpenAddTipsPanel(data)
    --         end
    --     else
    --         TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
    --     end
    elseif menu_text == Language.Menu.AttackOherRole then
        self:OnClickAttk()
    elseif menu_text == Language.Menu.AddFriend then
        if not is_cross then
            local role_id = param.role_id
            if SocietyWGData.Instance:GetIsMyFriend(role_id) then
                SocietyWGCtrl.Instance:Flush("find_role_id",{role_id})
            else
                SocietyWGCtrl.Instance:IAddFriend(param.role_id)
            end
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
        end
    elseif menu_text == Language.Menu.InviteTeam then --组队邀请
        if Scene.Instance:GetIsOpenCrossViewByScene() then --跨服组队
            BrowseWGCtrl.Instance:AllReqRoleInfo(role_id,self.data.plat_type, function (protocol)
                local uuid = MsgAdapter.ReadUUIDByValue(protocol.role_id, self.data.plat_type)
                if protocol.is_online == 1 or protocol.is_online == 3 then
                    CrossTeamWGCtrl.Instance:ITeamInvite(uuid)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
                end
            end)
            return
        end

        if 0 == SocietyWGData.Instance:GetIsInTeam() then
            local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
            NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
        end
        NewTeamWGCtrl.Instance:SendInviteUser(role_id, 0, 1)
    elseif menu_text == Language.Menu.ApplyTeam and team_index >= 0  then
        if SocietyWGData.Instance:GetIsInTeam() == 1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.AlreadyInTeam)
            return
        end
        if Scene.Instance:GetIsOpenCrossViewByScene() then
            CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_JOIN, team_index)
        else
            SocietyWGCtrl.Instance:SendReqJoinTeam(team_index)
        end
    elseif menu_text == Language.Menu.MergeTeam and team_index >= 0  then
        if Scene.Instance:GetIsOpenCrossViewByScene() then
            local my_uuid = RoleWGData.Instance:GetUUid()
            local uuid = MsgAdapter.ReadUUIDByValue(role_id, my_uuid.temp_high)
            CrossTeamWGCtrl.Instance:SendTeamMergeReq(uuid)
        else
            NewTeamWGCtrl.Instance:SendTeamMergeReq(role_id)
        end
    end
end