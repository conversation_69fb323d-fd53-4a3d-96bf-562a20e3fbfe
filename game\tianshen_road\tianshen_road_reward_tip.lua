TianShenRoadRewardTips = TianShenRoadRewardTips or BaseClass(SafeBaseView)

function TianShenRoadRewardTips:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self.view_name = "TianShenRoadUi"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 2), sizeDelta = Vector2(730,464)})
	self:AddViewResource(0, "uis/view/tianshenroad_ui_prefab", "layout_sqbx_reward")
end

function TianShenRoadRewardTips:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function TianShenRoadRewardTips:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.XiuZhenRoad.ViewName
	self.reward_list_view = AsyncListView.New(TianShenRoadReWardItemRender, self.node_list["ph_item_list"])
	self:FlushView()
end

function TianShenRoadRewardTips:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView()
		end
	end
end

function TianShenRoadRewardTips:FlushView()
	local cfg = TianshenRoadWGData.Instance:GetSQRewardCfg()
	if not cfg then
		return
	end
	
	local data_list = {}
	for k,v in pairs(cfg) do
		local item = {}
		item.rewards = v.reward_item
		item.ID = v.ID
		item.score = v.jigsaw_num
		item.state = TianshenRoadWGData.Instance:GetBXState(v.ID)

		if item.state == TianShenRoadRewardState.KLQ then
			item.sort = 0
		elseif item.state == TianShenRoadRewardState.BKL then
			item.sort = 1
		else
			item.sort = 2
		end

		data_list[#data_list + 1] = item
	end
	table.sort(data_list, SortTools.KeyLowerSorter("sort","ID"))

	self.reward_list_view:SetDataList(data_list)
	self.node_list["star_num"].text.text = TianshenRoadWGData.Instance:GetDangweiScore() .. "奖励点数"
end

-----------------------------------------------------------------------------------------------

TianShenRoadReWardItemRender = TianShenRoadReWardItemRender or BaseClass(BaseRender)

function TianShenRoadReWardItemRender:DeleteMe()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function TianShenRoadReWardItemRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
end

function TianShenRoadReWardItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return 
	end

	self.reward_list:SetDataList(SortTableKey(data.rewards))

	self.node_list["lbl_lscs"].text.text = data.score .. "奖励点数"
	self.node_list["btn_lingqu"]:SetActive(data.state == TianShenRoadRewardState.KLQ)
	self.node_list["image_ylq"]:SetActive(data.state == TianShenRoadRewardState.YLQ)
	self.node_list["btn_weilingqu"]:SetActive(data.state == TianShenRoadRewardState.BKL)
end

function TianShenRoadReWardItemRender:OnClickLingQu()
	TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_WANT_SHENQI,WOYAOSHENQI_OP_TYPE.DANGWEI_REWARD, self.data.ID)
end