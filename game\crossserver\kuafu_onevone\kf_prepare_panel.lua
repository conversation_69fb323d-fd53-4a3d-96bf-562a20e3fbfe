KfOneVSOnePrepareInfoPanel = KfOneVSOnePrepareInfoPanel or BaseClass(SafeBaseView)

function KfOneVSOnePrepareInfoPanel:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "kf_onevone_prepare_Panel")
	self.view_layer = UiLayer.MainUILow
	self.active_close = false
    self.order = 1
    self.is_safe_area_adapter = true
end

function KfOneVSOnePrepareInfoPanel:LoadCallBack()
	self.is_real_flush = true
	self.on_play_matching = GlobalEventSystem:Bind(OtherEventType.KF1V1SceneInfoMatch, BindTool.Bind(self.MatchingHandlerOneVOne, self))
	XUI.AddClickEventListener(self.node_list.pipei_anniu, BindTool.Bind1(self.MatchingHandlerOneVOne, self))
	XUI.AddClickEventListener(self.node_list.add_count_btn, BindTool.Bind1(self.AddCount, self))
	XUI.AddClickEventListener(self.node_list.person_button, BindTool.Bind(self.ChangePanelName, self,1))
	XUI.AddClickEventListener(self.node_list.rank_button, BindTool.Bind(self.ChangePanelName, self,2))
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_CROSS_1V1_SELF_INFO)
	self.rank_list = AsyncListView.New(OpenInfoMenuItem,self.node_list.TaskList)
	self:ChangePanelName(1)

	self.close_loading_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.AutoMatch, self))
end

function KfOneVSOnePrepareInfoPanel:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if self.on_play_matching then
		GlobalEventSystem:UnBind(self.on_play_matching)
		self.on_play_matching = nil
	end

	if self.confirm_buy_alert then
		self.confirm_buy_alert:DeleteMe()
		self.confirm_buy_alert = nil
	end
	self.panel_index = nil
	self.is_real_flush = nil

	if self.close_loading_event then
		GlobalEventSystem:UnBind(self.close_loading_event)
		self.close_loading_event = nil
	end
end

function KfOneVSOnePrepareInfoPanel:AddCount()
	if nil == self.confirm_buy_alert then
		self.confirm_buy_alert = Alert.New()
	end

	self.confirm_buy_alert:SetCheckBoxText(Language.Common.DontTip)
	self.confirm_buy_alert:SetLableString(string.format(Language.Kuafu1V1.BuyChallengeTimes, KuafuOnevoneWGData.GetBuyTimeCost()))
	self.confirm_buy_alert:SetOkFunc(BindTool.Bind(self.ClickToBuyTimes, self))
	self.confirm_buy_alert:SetShowCheckBox(true)
	self.confirm_buy_alert:Open()
end

function KfOneVSOnePrepareInfoPanel:ClickToBuyTimes()
	KuafuOnevoneWGCtrl.Instance:SendCSCross1v1BuyTimeReq()
end

function KfOneVSOnePrepareInfoPanel:ChangePanelName(panel_index)
	self.panel_index = panel_index
	self.node_list.person_select:SetActive(panel_index == 1)
	self.node_list.person_panel:SetActive(panel_index == 1)
	self.node_list.rank_select:SetActive(panel_index == 2)
	self.node_list.rank_panel:SetActive(panel_index == 2)
	self:Flush()
end

function KfOneVSOnePrepareInfoPanel:OnFlush()
	local kf1v1_info = KuafuOnevoneWGData.Instance:Get1V1Info()
	if nil == self.panel_index or IsEmptyTable(kf1v1_info) then
		return
	end

	if self.is_real_flush then
		MainuiWGCtrl.Instance:ShowMainuiMenu(false)
		self.is_real_flush = false
	end

	local surplus_times = 0
 	surplus_times = KuafuOnevoneWGData.GetMaxJionTimes() + kf1v1_info.today_buy_times - kf1v1_info.today_reward_count
 	surplus_times = surplus_times > 0 and surplus_times or 0
 	--self.node_list.Frame_panel_1:SetActive(self.panel_index == 1 )
	if self.panel_index == 1 then
		local ji_fen = KuafuOnevoneWGData.Instance:Get1V1InfoJiFen()
		self.node_list.pipeishenglv_text.text.text =string.format(Language.Kuafu1V1.Winrate, KuafuOnevoneWGData.Instance:GetWinRate())
		self.node_list.pipei_changci_show.text.text = kf1v1_info.cross_lvl_total_join_times
		self.node_list.liansheng_text.text.text = kf1v1_info.cross_1v1_dur_win_times
		self.node_list.my_rank_jifen.text.text = ji_fen
		local rewared_id = KuafuOnevoneWGData.Instance:GetRewardBaseCell(ji_fen)
		local seq = math.floor(rewared_id.seq / 5) + 1
		self.node_list.my_rank.text.text = rewared_id.name

		self.node_list.shengyu_chgangci_text.text.text = surplus_times
		local max_buy_count = KuafuOnevoneWGData.Instance:GetKFOneVOneOtherCfg()
		local max_buy_count1 = max_buy_count.max_buy_times
		self.node_list.add_count_effect:SetActive(max_buy_count1 > kf1v1_info.today_buy_times)
	else
		local data = Field1v1WGData.Instance:GetOneVOnePrepareInfo()
		self.rank_list:SetDataList(data)
		--local orgin_uid = RoleWGData.Instance:InCrossGetOriginUid()
		local main_role_vo = Scene.Instance:GetMainRole().vo
  	 	local orgin_uid = main_role_vo.uuid
		for k,v in pairs(data) do
			--print_error(orgin_uid , v.uuid,orgin_uid==v.uuid)
			if orgin_uid == v.uuid then
				--self.node_list.rank_name.text.text = v.name
				self.node_list.rank_my_rank.text.text = v.rank
				local rewared_id = KuafuOnevoneWGData.Instance:GetRewardBaseCell(v.score)
				self.node_list.rank_duanwei.text.text = rewared_id.name
				break
			end
		end
	end

	local macth_info = KuafuOnevoneWGData.Instance:Get1V1MacthInfo()
	if macth_info.result == 0 then
		self.node_list.pipei_anniu_Text.text.text = string.format(Language.Kuafu1V1.EnterSoon1,1)
	else
		self.node_list.pipei_anniu_Text.text.text = Language.Kuafu1V1.OnMatchTxt
	end

	self:ActUpdateCallBack()
end

function KfOneVSOnePrepareInfoPanel:FlushCountDown()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)

	self.node_list.text_activity_tip.text.text = ""
	self.node_list.activity_tips:SetActive(true)
	local time = 0
	if activity_info.status == ACTIVITY_STATUS.STANDY then
		time = activity_info.next_time
		self.node_list.text_activity_tip.text.text = Language.Activity.ZhunBeiZhong
	else
		time = activity_info.next_time
		self.node_list.text_activity_tip.text.text = Language.ZhuXie.ActTips2
	end
end

function KfOneVSOnePrepareInfoPanel:ActUpdateCallBack()
	if not self:IsOpen() then
		return
	end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
	if not activity_info then
		return
	end

	self.node_list.text_activity_tip.text.text = ""
	self.node_list.activity_tips:SetActive(true)
	local time = 0
	if activity_info.status == ACTIVITY_STATUS.STANDY then
		time = activity_info.next_time
		self.node_list.text_activity_tip.text.text = Language.Activity.ZhunBeiZhong
	else
		time = activity_info.next_time
		self.node_list.text_activity_tip.text.text = Language.ZhuXie.ActTips2
	end
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	self.node_list.activity_count_time.text.text = TimeUtil.FormatSecond(time - now_time, 2)
	AddDelayCall(self, BindTool.Bind(self.ActUpdateCallBack, self), 1)
end

function KfOneVSOnePrepareInfoPanel:AutoMatch()
	local kf1v1_info = KuafuOnevoneWGData.Instance:Get1V1Info()
	if nil == self.panel_index or IsEmptyTable(kf1v1_info) then
		return
	end

	local surplus_times = 0
 	surplus_times = KuafuOnevoneWGData.GetMaxJionTimes() + kf1v1_info.today_buy_times - kf1v1_info.today_reward_count
 	surplus_times = surplus_times > 0 and surplus_times or 0
	local is_auto_match = KuafuOnevoneWGData.Instance:GetIsAutoMatch()
	if is_auto_match then
		if surplus_times > 0 then
			GlobalTimerQuest:AddDelayTimer(function ()
				local macth_info = KuafuOnevoneWGData.Instance:Get1V1MacthInfo()
				local is_wait = macth_info.result == 0
				if is_wait and self:IsOpen() then
					self:MatchingHandlerOneVOne()
				end
			end, 5)
		end
		KuafuOnevoneWGData.Instance:SetIsAutoMatch(false)
	end
end

function KfOneVSOnePrepareInfoPanel:CloseCallBack()
	KuafuOnevoneWGData.Instance:SetIsAutoMatch(false)
end

function KfOneVSOnePrepareInfoPanel:SetMatchBtnFalse()
	if self.node_list.pipei_anniu then
		XUI.SetButtonEnabled(self.node_list.pipei_anniu,false)
	end
end

function KfOneVSOnePrepareInfoPanel:MatchingHandlerOneVOne()
	local macth_info = KuafuOnevoneWGData.Instance:Get1V1MacthInfo()
	local is_pipei_ing = macth_info.result == 1
	if is_pipei_ing then
		ViewManager.Instance:Close(GuideModuleName.KfOneVOneMatch)
		KuafuOnevoneWGCtrl.Instance:SendCross1v1MatchQuery(CROSS_1V1_MATCH_REQ_TYPE.CROSS_1V1_MATCH_REQ_CANCEL)
		return
	end

	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)  then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
		return
	end

	local kf1v1_info = KuafuOnevoneWGData.Instance:Get1V1Info()
	if IsEmptyTable(kf1v1_info) then
		return
	end

	Field1v1WGData.Instance:SetCurMatchFlag(KFPVP_TYPE.ONE)
	KuafuOnevoneWGCtrl.Instance:SendCrossMatch1V1Req()
end

OpenInfoMenuItem = OpenInfoMenuItem or BaseClass(BaseRender)
function OpenInfoMenuItem:__init()

end
function OpenInfoMenuItem:__delete()

end
function OpenInfoMenuItem:OnFlush()
	if nil == self.data then return end
	self.node_list.name_1.text.text = self.data.name
	self.node_list.rank.text.text = self.data.rank
	self.node_list.rank_image:SetActive(self.index <= 3)
	if self.index <= 3 then
		self.node_list.rank_image.image:LoadSprite(ResPath.GetF2CommonIcon("icon_kf_paiming"..self.index))
	end

	local rewared_id = KuafuOnevoneWGData.Instance:GetRewardBaseCell(self.data.score)
	self.node_list.duanwei.text.text = rewared_id.name
end
--------------------------------------------------------------------------------------------------
--废弃的
KfPVPPrepareInfoPanel = KfPVPPrepareInfoPanel or BaseClass(SafeBaseView)

function KfPVPPrepareInfoPanel:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "kf_pvp_prepare_Panel")
end

function KfPVPPrepareInfoPanel:__delete()

end

function KfPVPPrepareInfoPanel:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if self.node_list.task_root_view then
        local obj = self.node_list.task_root_view.gameObject
        ResMgr:Destroy(obj)
    end

    if self.alert_window then
    	self.alert_window:DeleteMe()
    	self.alert_window = nil
    end

end
function KfPVPPrepareInfoPanel:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.pipei_anniu, BindTool.Bind1(self.MatchingHandlerPvP, self))
	local mainui_ctrl = MainuiWGCtrl.Instance

	local parent = mainui_ctrl:GetTaskOtherContent()
	self.node_list.task_root_view.transform:SetParent(parent.transform)
	self.node_list.task_root_view.transform.anchoredPosition = Vector3(0,0,0)

	mainui_ctrl:SetTaskPanel(false,151,-136.1)
	-- mainui_ctrl.view:SetTaskCallBack(function (ison)
	-- 	self.node_list.task_root_view:SetActive(ison)
	-- end)
	-- mainui_ctrl:ChangeTaskBtnName(Language.Common.Rank)
	self.rank_list = AsyncListView.New(OpenInfoMenuItem, self.node_list.TaskList1)
	self:Flush()
end

function KfPVPPrepareInfoPanel:OnFlush()
	local data = Field1v1WGData.Instance:GetPvpPrepareInfo()
	self.rank_list:SetDataList(data)
	self:UpdateBtnLbl()
end

function KfPVPPrepareInfoPanel:MatchingHandlerPvP()
	local match_info = KuafuPVPWGData.Instance:GetMatchStateInfo()
	if  match_info.matching_state >= 0 and match_info.matching_state ~= 3 then
		if SocietyWGData.Instance:GetIsInTeam() == 1 and SocietyWGData.Instance:GetIsTeamLeader() ~= 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.CancelMatchError)
			return
		end
		KuafuPVPWGCtrl.Instance:SendCrossMultiuerChallengeCancelMatching()
		return
	end

	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_PVP)  then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
		return
	end

	local mate_list = KuafuPVPWGData.Instance:GetMatesInfo()
	if nil == self.alert_window then
		self.alert_window = Alert.New()
	end

	if #mate_list > 1 then
		if SocietyWGData.Instance:GetTeamMemberCount() > 3 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.MaxTeammateTips)
			return
		end
		local team_mate = ""
		for k,v in pairs(mate_list) do
			local role_id = RoleWGData.Instance.role_vo.role_id
			if v.uid ~= role_id then
				team_mate = team_mate .. "    " .. v.user_name .. " " .. v.level .. Language.Common.Ji .. "\n"
			end
		end
		self.alert_window:SetLableString(string.format(Language.KuafuPVP.TeamMatch, team_mate))
	else
		self.alert_window:SetLableString(Language.KuafuPVP.SelfMatch)
	end

	self.alert_window:SetOkFunc(BindTool.Bind1(self.SendMatchgingReq, self))
	self.alert_window:Open()
	-- self:OnFlushPVPTeamEquipView()
end

function KfPVPPrepareInfoPanel:SendMatchgingReq()
	KuafuPVPWGCtrl.Instance:SendCrossMultiuserChallengeMatchgingReq()
end

function KfPVPPrepareInfoPanel:SetMatchBtnFalsePVP()
	if self.node_list.pipei_anniu then
		XUI.SetButtonEnabled(self.node_list.pipei_anniu,false)
	end
end

function KfPVPPrepareInfoPanel:UpdateBtnLbl()
    local match_info = KuafuPVPWGData.Instance:GetMatchStateInfo()
	if match_info.matching_state < 0 or match_info.matching_state == 3 then
		self.node_list.pipei_anniu_Text.text.text = Language.KuafuPVP.MatchBtnTxt[1]
		ViewManager.Instance:Close(GuideModuleName.KfOneVOneMatch)
	else
		self.node_list.pipei_anniu_Text.text.text = Language.KuafuPVP.MatchBtnTxt[2]
	end
end
