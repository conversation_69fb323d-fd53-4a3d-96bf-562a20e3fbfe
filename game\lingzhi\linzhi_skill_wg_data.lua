LingZhiSkillWGData = LingZhiSkillWGData or BaseClass()

local PART_TYPE = {
	[LINGZHI_SKILL_TYPE.WING] = SHIZHUANG_TYPE.WING,
	[LINGZHI_SKILL_TYPE.FABAO] = SHIZHUANG_TYPE.FABAO,
	[LINGZHI_SKILL_TYPE.JIANZHEN] = SHIZHUANG_TYPE.JIANZHEN,
	[LINGZHI_SKILL_TYPE.SHENBING] = SHIZHUANG_TYPE.SHENBING,
}

LingZhiSkillWGData.FunName = {
	[LINGZHI_SKILL_TYPE.WING] = FunName.WingLinZhiSkillView,
	[LINGZHI_SKILL_TYPE.FABAO] = FunName.FaBaoLinZhiSkillView,
	[LINGZHI_SKILL_TYPE.JIANZHEN] = FunName.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hiSkillView,
	[LINGZHI_SKILL_TYPE.SHENBING] = FunName.ShenBingLinZhiSkillView,
}

function Ling<PERSON>hiSkillWGData:__init()
	if <PERSON><PERSON><PERSON><PERSON>killWGData.Instance ~= nil then
		error("[LingZhiSkillWGData] attempt to create singleton twice!")
		return
	end

	LingZhiSkillWGData.Instance = self

	local lingzhi_skill_cfg = ConfigManager.Instance:GetAutoConfig("qihun_system_auto")
	self.lingzhi_skill_cfg = lingzhi_skill_cfg
	-- 修炼升阶配置
	self.xiulian_cfg = ListToMap(lingzhi_skill_cfg.xiulian, "lingzhi_type", "grade")
	-- 修炼成就配置
	self.xiulian_ach_cfg = ListToMap(lingzhi_skill_cfg.xiulian_chengjiu, "lingzhi_type", "index")
	-- 外观成就配置
	self.waiguan_ach_cfg = ListToMap(lingzhi_skill_cfg.waiguan_chengjiu, "lingzhi_type", "index")
	-- 其他
	self.other_cfg = lingzhi_skill_cfg.other[1]
	-- 价格
	self.price_cfg = ListToMap(lingzhi_skill_cfg.price, "qihun_type")

	-- 符文升级
	self.skill_fuwen_cfg = ListToMap(lingzhi_skill_cfg.skill_fuwen, "lingzhi_type", "skill_index", "skill_level")
	-- 符文激活
	self.skill_fuwen_active_cfg = ListToMap(lingzhi_skill_cfg.skill_fuwen_active, "lingzhi_type", "skill_index")
	-- 符文技能列表
	self.skill_list_cfg = ListToMapList(lingzhi_skill_cfg.skill_fuwen_active, "lingzhi_type")
	-- 初始技能配置
	self.base_skill_param = ListToMap(lingzhi_skill_cfg.base_skill_param, "lingzhi_type")

	self.server_info = {}

	local remindmanager_instance = RemindManager.Instance
	remindmanager_instance:Register(RemindName.LingZhi_Wing_SJ, BindTool.Bind(self.GetSJRemind, self, LINGZHI_SKILL_TYPE.WING))
	remindmanager_instance:Register(RemindName.LingZhi_Wing_XL, BindTool.Bind(self.GetXLRemind, self, LINGZHI_SKILL_TYPE.WING))
	remindmanager_instance:Register(RemindName.LingZhi_FaBao_SJ, BindTool.Bind(self.GetSJRemind, self, LINGZHI_SKILL_TYPE.FABAO))
	remindmanager_instance:Register(RemindName.LingZhi_FaBao_XL, BindTool.Bind(self.GetXLRemind, self, LINGZHI_SKILL_TYPE.FABAO))
	remindmanager_instance:Register(RemindName.LingZhi_JianZhen_SJ, BindTool.Bind(self.GetSJRemind, self, LINGZHI_SKILL_TYPE.JIANZHEN))
	remindmanager_instance:Register(RemindName.LingZhi_JianZhen_XL, BindTool.Bind(self.GetXLRemind, self, LINGZHI_SKILL_TYPE.JIANZHEN))
	remindmanager_instance:Register(RemindName.LingZhi_ShenBing_SJ, BindTool.Bind(self.GetSJRemind, self, LINGZHI_SKILL_TYPE.SHENBING))
	remindmanager_instance:Register(RemindName.LingZhi_ShenBing_XL, BindTool.Bind(self.GetXLRemind, self, LINGZHI_SKILL_TYPE.SHENBING))
	remindmanager_instance:Register(RemindName.LingZhi_Wing_Act, BindTool.Bind(self.GetActRemind, self, LINGZHI_SKILL_TYPE.WING))
	remindmanager_instance:Register(RemindName.LingZhi_FaBao_Act, BindTool.Bind(self.GetActRemind, self, LINGZHI_SKILL_TYPE.FABAO))
	remindmanager_instance:Register(RemindName.LingZhi_JianZhen_Act, BindTool.Bind(self.GetActRemind, self, LINGZHI_SKILL_TYPE.JIANZHEN))
	remindmanager_instance:Register(RemindName.LingZhi_ShenBing_Act, BindTool.Bind(self.GetActRemind, self, LINGZHI_SKILL_TYPE.SHENBING))
	self:RegisterRemindInBag(RemindName.LingZhi_Wing_XL, RemindName.LingZhi_FaBao_XL, RemindName.LingZhi_JianZhen_XL, RemindName.LingZhi_ShenBing_XL)

	self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_Wing_SJ)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_Wing_XL)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_FaBao_SJ)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_FaBao_XL)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_JianZhen_SJ)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_JianZhen_XL)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_ShenBing_SJ)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_ShenBing_XL)

	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_JianZhen_Act)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_FaBao_Act)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_Wing_Act)
	RemindManager.Instance:Bind(self.remind_change, RemindName.LingZhi_ShenBing_Act)
end

function LingZhiSkillWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.LingZhi_Wing_SJ)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_Wing_XL)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_FaBao_SJ)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_FaBao_XL)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_JianZhen_SJ)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_JianZhen_XL)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_ShenBing_SJ)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_ShenBing_XL)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_JianZhen_Act)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_FaBao_Act)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_Wing_Act)
	RemindManager.Instance:UnRegister(RemindName.LingZhi_ShenBing_Act)

	RemindManager.Instance:UnBind(self.remind_change)
	LingZhiSkillWGData.Instance = nil
end

-- 变强
function LingZhiSkillWGData:RemindChangeCallBack(remind_name, num)
	-- 羁绊
	if remind_name == RemindName.LingZhi_Wing_SJ then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.WING_SHENJI, num, function ()
			ViewManager.Instance:Open(GuideModuleName.WingLinZhiSkillView, TabIndex.lingzhi_ql_skill)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_Wing_XL then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.WING_XIULIAN, num, function ()
			ViewManager.Instance:Open(GuideModuleName.WingLinZhiSkillView, TabIndex.lingzhi_ql_up)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_FaBao_SJ then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FABAO_SHENJI, num, function ()
			ViewManager.Instance:Open(GuideModuleName.FaBaoLinZhiSkillView, TabIndex.lingzhi_ql_skill)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_FaBao_XL then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FABAO_XIULIAN, num, function ()
			ViewManager.Instance:Open(GuideModuleName.FaBaoLinZhiSkillView, TabIndex.lingzhi_ql_up)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_JianZhen_SJ then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.JIANZHEN_SHENJI, num, function ()
			ViewManager.Instance:Open(GuideModuleName.JianZhenLinZhiSkillView, TabIndex.lingzhi_ql_skill)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_JianZhen_XL then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.JIANZHEN_XIULIAN, num, function ()
			ViewManager.Instance:Open(GuideModuleName.JianZhenLinZhiSkillView, TabIndex.lingzhi_ql_up)
			return true
		end)
	elseif remind_name == RemindName.LingZhi_Wing_Act then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.WING_QL_ACT, num, function ()
			ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_wing)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_FaBao_Act then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FABAO_QL_ACT, num, function ()
			ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_fabao)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_JianZhen_Act then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.JIANZHEN_QL_ACT, num, function ()
			ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_jianzhen)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_ShenBing_Act then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHENBING_QL_ACT, num, function ()
			ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_shenbing)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_ShenBing_SJ then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHENBING_SHENJI, num, function ()
			ViewManager.Instance:Open(GuideModuleName.ShenBingLinZhiSkillView, TabIndex.lingzhi_ql_skill)
			return true
		end)

	elseif remind_name == RemindName.LingZhi_ShenBing_XL then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHENBING_XIULIAN, num, function ()
			ViewManager.Instance:Open(GuideModuleName.ShenBingLinZhiSkillView, TabIndex.lingzhi_ql_up)
			return true
		end)

	end
end

function LingZhiSkillWGData:RegisterRemindInBag(...)
    local map = {}

	for _, v in ipairs(self.lingzhi_skill_cfg.xiulian) do
		if v.stuff_id > 0 then
			map[v.stuff_id] = true
		end
	end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    local remind_name_list = {...}
	for _, remind_name in pairs(remind_name_list) do
		BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
	end
end

-- 外观成就配置
function LingZhiSkillWGData:GetWaiGuanAchCfg(lingzhi_type, index)
	return self.waiguan_ach_cfg[lingzhi_type] and self.waiguan_ach_cfg[lingzhi_type][index]
end

-- 修炼成就配置
function LingZhiSkillWGData:GetXiuLianAchCfg(lingzhi_type, grade)
	return self.xiulian_ach_cfg[lingzhi_type] and self.xiulian_ach_cfg[lingzhi_type][grade]
end

-- 修炼升阶配置
function LingZhiSkillWGData:GetXiuLianCfg(lingzhi_type, grade)
	return self.xiulian_cfg[lingzhi_type] and self.xiulian_cfg[lingzhi_type][grade]
end

-- 获取最大修炼等级
function LingZhiSkillWGData:GetXlGradeMax(lingzhi_type)
	return self.xiulian_cfg[lingzhi_type] and #self.xiulian_cfg[lingzhi_type]
end

-- 获取最大符文等级
function LingZhiSkillWGData:GetFuWenCfg(lingzhi_type, skill_index, skill_level)
	return self.skill_fuwen_cfg[lingzhi_type] and self.skill_fuwen_cfg[lingzhi_type][skill_index] and self.skill_fuwen_cfg[lingzhi_type][skill_index][skill_level]
end

-- 获取最大符文等级
function LingZhiSkillWGData:GetFuWenLevelMax(lingzhi_type, skill_index)
	return self.skill_fuwen_cfg[lingzhi_type] and self.skill_fuwen_cfg[lingzhi_type][skill_index] and #self.skill_fuwen_cfg[lingzhi_type][skill_index]
end

-- 价格配置
function LingZhiSkillWGData:GetPriceCfg(lingzhi_type)
	return self.price_cfg[lingzhi_type]
end

-- 基础技能配置
function LingZhiSkillWGData:GetBaseSkillCfg(lingzhi_type)
	return self.base_skill_param[lingzhi_type]
end

-- 符文技能激活配置
function LingZhiSkillWGData:GetFwSkillActCfg(lingzhi_type, skill_index)
	return self.skill_fuwen_active_cfg[lingzhi_type] and self.skill_fuwen_active_cfg[lingzhi_type][skill_index]
end

-- 符文技能升级配置
function LingZhiSkillWGData:GetFwSkillUpLevelCfg(lingzhi_type, skill_index, skill_level)
	return self.skill_fuwen_cfg[lingzhi_type] and self.skill_fuwen_cfg[lingzhi_type][skill_index] and self.skill_fuwen_cfg[lingzhi_type][skill_index][skill_level]
end

function LingZhiSkillWGData:GetOtherCfg()
	return self.other_cfg
end

function LingZhiSkillWGData:SetAllServerInfo(protocol)
	self.server_info = protocol.qihun_list
end

function LingZhiSkillWGData:SetSingleServerInfo(protocol)
		
	local old_data = self.server_info[protocol.qihun_info.qihun_type]
	if old_data then
		local lingzhi_type = protocol.qihun_info.qihun_type
		-- local old_num = old_data.history_lingzhi
		-- local new_num = protocol.qihun_info.history_lingzhi
		local old_num = old_data.lingzhi
		local new_num = protocol.qihun_info.lingzhi

		if new_num > old_num then
			local linzhi_id = LINGZHI_SKILL_XUNI_ITEM[lingzhi_type]
			local cfg = ItemWGData.Instance:GetItemConfig(linzhi_id)
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, ToColorStr(cfg.name, GET_TIP_ITEM_COLOR[cfg.color]), new_num - old_num))
		end
		local old_act_list_flag = old_data.fuwen_act_list_flag
		local new_act_list_flag = protocol.qihun_info.fuwen_act_list_flag
		if old_act_list_flag ~= new_act_list_flag then
			local lerp_flag = new_act_list_flag - old_act_list_flag
			local lerp_flag_list = bit:d2b_two(lerp_flag)

			for i=0,32 do
				if lerp_flag_list[i] == 1 then
					local skill_index = i
					local fuwen_cfg = self:GetFuWenCfg(lingzhi_type,skill_index,1)
					if fuwen_cfg then
						local skill_data = {}
						skill_data.name = fuwen_cfg.skill_name
						skill_data.desc = ""
						skill_data.icon = "a1_lz_skill_icon_high_" .. fuwen_cfg.skill_icon_id
						skill_data.res_fun = ResPath.GetLingZhiImg
						skill_data.img_get_newskill = Language.Skill.ActSkillTitle2
						local role_level = RoleWGData.Instance:GetRoleLevel()
						local skill_level = protocol.qihun_info.fuwen_level_list[skill_index] or 0
						local cur_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, skill_index, skill_level)
						local nex_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, skill_index, skill_level + 1)
						local need_num = cur_cfg and cur_cfg.cost 
						if new_num > need_num and nex_cfg and cur_cfg and role_level >= cur_cfg.role_level then
							skill_data.ok_fun_callback = BindTool.Bind(self.ShowGetNewSkillOkBtnFun,self,lingzhi_type,skill_index)
							skill_data.ok_btn_text = Language.LingZhi.NewSkillGetOkBtnText
						end
						AppearanceWGCtrl.Instance:AddNewAppeInfoListFromSkill(skill_data)
						break
					end
				end
			end
		end
	end

	self.server_info[protocol.qihun_info.qihun_type] = protocol.qihun_info
end

function LingZhiSkillWGData:ShowGetNewSkillOkBtnFun(lingzhi_type,skill_index)
	LingZhiWGCtrl.Instance:OpenLingZhiView(lingzhi_type,TabIndex.lingzhi_ql_skill,nil,{select_skill = skill_index + 1})
end

function LingZhiSkillWGData:GetSingleServerInfo(qihun_type)
	return self.server_info[qihun_type] 
end

-- 获取技能列表
function LingZhiSkillWGData:GetSkillList(lingzhi_type)
	local data_list = {}
	if self.skill_list_cfg[lingzhi_type] then
		for i,v in ipairs(self.skill_list_cfg[lingzhi_type]) do
			data_list[v.skill_index + 1] = {
				lingzhi_type = v.lingzhi_type,
				skill_index = v.skill_index,
				index = i,
				cfg = v,
			}
		end
	end

	return data_list
end

-- 获取成就列表
function LingZhiSkillWGData:GetAchShowList(lingzhi_type, view_type)
	local data_list = {}
	local cfg_list

	if not lingzhi_type or not view_type then
		return data_list
	end

	-- 灵器修炼
	if view_type == 1 then
		cfg_list = self.xiulian_ach_cfg[lingzhi_type]
	-- 法宝收集
	else
		cfg_list = self.waiguan_ach_cfg[lingzhi_type]
	end

	cfg_list = cfg_list or {}
	for i= 0,#cfg_list do
		local sort_index = cfg_list[i].index
		local cfg = cfg_list[i]
		if view_type == 2 then
			local waiguan_item_cfg = ItemWGData.Instance:GetItemConfig(cfg.waiguan_id)
			sort_index = waiguan_item_cfg and waiguan_item_cfg.color or cfg_list[i].index
		end
		table.insert(data_list, {
			cfg = cfg_list[i],
			lingzhi_type = lingzhi_type,
			view_type = view_type,
			sort_index = 1000 - sort_index,
			is_can_receive = self:GetIsCanReceiveAch(cfg_list[i], view_type)
		})
	end

	SortTools.SortDesc(data_list,"is_can_receive", "sort_index")

	return data_list
end

-- 是否可领取  2 可领取  1不可领取
function LingZhiSkillWGData:GetIsCanReceiveAch(data, view_type)
	if not data or not view_type then
		return 0
	end

	local lingzhi_type = data.lingzhi_type
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return 0
	end

	local flag_list
	if view_type == 1 then
		flag_list = server_info.grade_chengjiu_flag
	else
		flag_list = server_info.appe_chengjiu_flag
	end

	local is_act = flag_list[data.index] == 1
	if is_act then
		return 0
	end

	if view_type == 1 then 
		local need_grade = data.grade
		local has_grade = server_info.xiulian_grade

		if has_grade >= need_grade then
			return 2
		end
	else
		local sz_is_act = self:GetWGHasAct(data, lingzhi_type)
		if sz_is_act then
			return 2
		end
	end

	return 1
end

function LingZhiSkillWGData:GetWGHasAct(data, lingzhi_type)
	if not data or not lingzhi_type then
		return false
	end

	-- if lingzhi_type == LINGZHI_SKILL_TYPE.SHENBING then
	-- 	return CloakData.Instance:GetCloakHasActByItemId(data.waiguan_id)
	-- else
		local need_act_waiguan_id = data.waiguan_id

		local f_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(need_act_waiguan_id)
		if f_cfg then
			local sz_is_act = NewAppearanceWGData.Instance:GetFashionIsAct(PART_TYPE[lingzhi_type], f_cfg.index)
			return sz_is_act
		end
	-- end

	return false
end

-- 获取修炼属性列表
function LingZhiSkillWGData:GetXLAttrList(data)
	local data_list = {}

	if not data then
		return data_list, 0
	end

	-- 当前等级
	local lingzhi_type = data.qihun_type or 0
	local grade = data.xiulian_grade or 1
	-- 最大等级
	local max_level = self:GetXlGradeMax(lingzhi_type)
	-- 当前属性
	local cur_cfg = self:GetXiuLianCfg(lingzhi_type, grade)
	local cur_level_cfg = AttributeMgr.GetAttributteByClass(cur_cfg)
	-- 下一级属性
	local nex_cfg = self:GetXiuLianCfg(lingzhi_type, grade + 1)
	local nex_level_cfg = AttributeMgr.GetAttributteByClass(nex_cfg)
	-- 已有属性
	local has_show_list = {}
	local sort_list = AttributeMgr.SortAttribute()
	local attr_name = Language.Common.AttrNameList2
	local show_list = cur_level_cfg
	local is_per
	local cur_cap = AttributeMgr.GetCapability(cur_level_cfg)

	for k,v in ipairs(sort_list) do
		if show_list[v] ~= 0 then
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
			local value = is_per and ((show_list[v]/100) .. "%") or math.floor(show_list[v])
			local name = attr_name[v] .. " " .. ToColorStr(value,"#A64E35")
			local has_nex = false
			local add_value = 0

			if nex_level_cfg and nex_level_cfg[v] ~= 0 then
				add_value = nex_level_cfg[v] - show_list[v]
				has_nex = add_value > 0

			 	add_value = is_per and ((add_value/100) .. "%") or math.floor(add_value)
				add_value = ToColorStr(add_value,"#006a25")
			end

			has_show_list[v] = #data_list + 1
			local attr_data = {name = name, has_nex = has_nex, add_value = add_value}
			table.insert(data_list, attr_data)
		end
	end

	if cur_cfg.zongshuxing_per and cur_cfg.zongshuxing_per > 0 then
		local value = (cur_cfg.zongshuxing_per/100) .. "%"
		local name = Language.LingZhi.NameList3[lingzhi_type] .. " " .. ToColorStr(value,"#A64E35")
		local has_nex = false
		local add_value = 0
		if nex_cfg and nex_cfg['zongshuxing_per'] ~= 0 then
			add_value = nex_cfg['zongshuxing_per'] - cur_cfg['zongshuxing_per']
			has_nex = add_value > 0

		 	add_value = (add_value/100) .. "%"
			add_value = ToColorStr(add_value,"#006a25")
		end
		has_show_list['zongshuxing_per'] = #data_list + 1
		local attr_data = {name = name, has_nex = has_nex, add_value = add_value}
		table.insert(data_list, attr_data)
	end

	-- 未激活属性
	for i= grade + 1,max_level do
		local level_cfg = self:GetXiuLianCfg(lingzhi_type, i)
		local attr_list = AttributeMgr.GetAttributteByClass(level_cfg)
		for k,v in ipairs(sort_list) do
			if attr_list[v] ~= 0 and not has_show_list[v] then
				is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
				local value = is_per and ((attr_list[v]/100) .. "%") or math.floor(attr_list[v])
				local name = ToColorStr(attr_name[v] .. " " .. value, COLOR3B.GRAY)
				local limit = ToColorStr(string.format(Language.LingZhi.Tip3, level_cfg.grade_name, level_cfg.grade_show), COLOR3B.RED)

				has_show_list[v] = #data_list + 1
				local attr_data = {name = name,limit = limit}
				table.insert(data_list, attr_data)
			end

			if level_cfg['zongshuxing_per'] ~= 0 and not has_show_list['zongshuxing_per'] then
				local value = (level_cfg.zongshuxing_per/100) .. "%"
				local has_nex = false
				local add_value = 0
				local name = ToColorStr(Language.LingZhi.NameList3[lingzhi_type] .. " " .. value, COLOR3B.GRAY)
				local limit = ToColorStr(string.format(Language.LingZhi.Tip3, level_cfg.grade_name, level_cfg.grade_show) , COLOR3B.RED)

				has_show_list['zongshuxing_per'] = #data_list + 1
				local attr_data = {name = name,limit = limit}
				table.insert(data_list, attr_data)
			end
		end
	end	

	if has_show_list['zongshuxing_per'] then
		local index = has_show_list['zongshuxing_per']
		local item = table.remove(data_list, index)
		table.insert(data_list, item)
	end

	return data_list, cur_cap
end

-- 获取技能描述
function LingZhiSkillWGData:GetSJSkillDes(cfg, dec_name,is_dark)
	if not cfg then
		return ''
	end
	local effect_dec = (dec_name and cfg[dec_name]) or cfg.effect_dec or ''
	local per_str = dec_name and "%%" or "%%%%"

	for str in GFind(effect_dec, "{.-}") do
		local name = string.sub(str, 2, -2)
		local is_per = name and string.sub(name,1,1) == "#"
		name = is_per and string.sub(name,2,-1) or name
		local value = cfg[name]
		value = is_per and ((math.abs(value)/100) .. per_str) or math.abs(value) 
		local color = is_dark and COLOR3B.D_GREEN or COLOR3B.GREEN
		value = ToColorStr(value, color)

		effect_dec = string.gsub(effect_dec, str, value)
	end

	return effect_dec
end

function LingZhiSkillWGData:GetSJBaseSkillDes(cfg,is_dark)
	if not cfg then
		return ''
	end

	local lingzhi_type = cfg.lingzhi_type or 0
	local effect_dec = cfg.effect_dec or ''
	local skill_list = self:GetSkillList(lingzhi_type)
	local server_info =	self:GetSingleServerInfo(lingzhi_type)
	for str in GFind(effect_dec, "{.-}") do
		local str_list = string.sub(str, 2, -2)
		local list = string.split(str_list, ",")
		local need_child_dec = #list > 1
		if need_child_dec then
			local child = skill_list[tonumber(list[1]+ 1)]
			local skill_index = child and child.skill_index or 0
			local is_act = server_info.fuwen_act_list[skill_index] == 1

			local child_dec = ""
			if is_act and child then
				local skill_level = server_info.fuwen_level_list[skill_index]
				local child_cfg = self:GetFuWenCfg(lingzhi_type, skill_index, skill_level)
				child_dec = self:GetSJSkillDes(child_cfg)
			end
			effect_dec = string.gsub(effect_dec, str, child_dec)
		else
			local name = list[#list]
			local is_per = name and string.sub(name,1,1) == "#"
			name = is_per and string.sub(name,2,-1) or name
			-- 主技能数值
			local value = cfg[name]
			for i,v in ipairs(skill_list) do
				local child = v
				local skill_index = child and child.skill_index or 0
				local is_act = server_info.fuwen_act_list[skill_index] == 1
				if is_act and child then
					local skill_level = server_info.fuwen_level_list[skill_index]
					local child_cfg = self:GetFuWenCfg(lingzhi_type, skill_index, skill_level)
					value = value + (child_cfg and child_cfg[name] or 0)
				end
			end

			value = is_per and ((value/100) .. "%%") or value
			local color = is_dark and COLOR3B.D_GREEN or COLOR3B.GREEN
			value = ToColorStr(value, color)
			effect_dec = string.gsub(effect_dec, str, value)
		end
	end

	return effect_dec
end

function LingZhiSkillWGData:GetSJBaseSkillColor(server_info)
	local lingzhi_type = server_info.qihun_type
	local base_skill_cfg = LingZhiSkillWGData.Instance:GetBaseSkillCfg(lingzhi_type)
	local color = base_skill_cfg and base_skill_cfg.baseskill_color or 3
	local show_list = LingZhiSkillWGData.Instance:GetSkillList(lingzhi_type)
	for i,v in ipairs(show_list) do
		if server_info.fuwen_level_list[v.skill_index] > 0 then
			local cfg = self:GetFuWenCfg(lingzhi_type, v.skill_index, server_info.fuwen_level_list[v.skill_index])
			if cfg then
				color = math.max(cfg.skill_color, color) 
			end
		end
	end

	return color
end

-- 获取成就激活点数
function LingZhiSkillWGData:GetAchLZNum(lingzhi_type, ach_type)
	local get_linzhi_num = 0
	local total_num = 0
	local server_info = self:GetSingleServerInfo(lingzhi_type)
	if not lingzhi_type or not ach_type or not server_info then
		return 0, 1
	end

	local cfg_list = {}
	local flag_list = {}
	if ach_type == 1 then
		cfg_list = self.xiulian_ach_cfg[lingzhi_type]
		flag_list = server_info.grade_chengjiu_flag
	else
		cfg_list = self.waiguan_ach_cfg[lingzhi_type]
		flag_list = server_info.appe_chengjiu_flag
	end

	for k,v in pairs(cfg_list) do
		if flag_list[k] == 1 then
			get_linzhi_num = get_linzhi_num + v.add_lingzhi
		end

		total_num = total_num + v.add_lingzhi
	end

	return get_linzhi_num, total_num
end

function LingZhiSkillWGData:CheckJump()
	for i=0,2 do
		local server_info = self.server_info[i]
		if server_info and server_info.is_open == 1 then
			if i == LINGZHI_SKILL_TYPE.WING then
				ViewManager.Instance:Open(GuideModuleName.WingLinZhiSkillView)
			elseif i == LINGZHI_SKILL_TYPE.FABAO then
				ViewManager.Instance:Open(GuideModuleName.FaBaoLinZhiSkillView)
			elseif i == LINGZHI_SKILL_TYPE.JIANZHEN then
				ViewManager.Instance:Open(GuideModuleName.JianZhenLinZhiSkillView)
			end
			
			return
		end
	end

	SysMsgWGCtrl.Instance:ErrorRemind(Language.LingZhi.ActiveTxt7)
end

---------------------------------------------------------------------
--功能开启第一次显示红点 终身只显示一次 策划说可以只做本地缓存 不需要服务端
function LingZhiSkillWGData:SetLoginRemind(lingzhi_type,flag)
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local key = "lingzhi_funopen_red" .. uuid_str .. lingzhi_type
	local value = flag and 1 or 0
	PlayerPrefsUtil.SetInt(key, value)
	RemindManager.Instance:Fire(RemindName.LingZhi_Wing_Act)
	RemindManager.Instance:Fire(RemindName.LingZhi_FaBao_Act)
	RemindManager.Instance:Fire(RemindName.LingZhi_JianZhen_Act)
	RemindManager.Instance:Fire(RemindName.LingZhi_ShenBing_Act)
end

function LingZhiSkillWGData:GetActRemind(lingzhi_type)
	if IS_AUDIT_VERSION then
		return 0
	end

	if not lingzhi_type then
		return 0
	end

	local server_info = self:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return 0
	end

	-- 领取激活卡
	if server_info.is_buy == 1 and server_info.is_open ~= 1 then
		return 1
	end

	--[[
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local key = "lingzhi_funopen_red" .. uuid_str .. lingzhi_type
	local funopen_red = PlayerPrefsUtil.GetInt(key)
	if tonumber(funopen_red) == 1 and server_info.is_buy ~= 1 then
		return 1
	end
	]]
	return 0
end

-- 升级红点
function LingZhiSkillWGData:GetSJRemind(lingzhi_type)
	if IS_AUDIT_VERSION then 
		return 0
	end

	if not lingzhi_type then
		return 0
	end

	local server_info = self:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return 0
	end

	if server_info.is_buy ~= 1 then
		return 0
	else
		local fun_name = LingZhiSkillWGData.FunName[lingzhi_type]
		local fun_is_open = FunOpen.Instance:GetFunIsOpened(fun_name)
		if not fun_is_open then
			return 0
		end
	end

	local is_open = server_info.is_open == 1
	if not is_open then
		return 0
	end

	local skill_list = self:GetSkillList(lingzhi_type)
	for i,v in ipairs(skill_list) do
		local skill_level = server_info.fuwen_level_list[v.skill_index]
		local skill_index = v.skill_index
		local is_act = server_info.fuwen_act_list[skill_index] ~= 1

		-- 激活
		if is_act then
			local act_cfg = self:GetFwSkillActCfg(lingzhi_type, skill_index)
			local need_orange_apperance_count = act_cfg.need_orange_apperance_count
			local need_red_apperance_count = act_cfg.need_red_apperance_count
			local need_purple_apperance_count = act_cfg.need_purple_apperance_count
			local need_pink_apperance_count = act_cfg.need_pink_apperance_count
			-- local upquality_orange_count = act_cfg.upquality_orange_count
			-- local upquality_red_count = act_cfg.upquality_red_count
			-- local upquality_pink_count = act_cfg.upquality_pink_count
			-- local upquality_colorful_count = act_cfg.upquality_colorful_count
			local need_qihun_grade = act_cfg.need_qihun_grade
			local need_golden_apperance_count = act_cfg.need_golden_apperance_count
			local need_colour_apperance_count = act_cfg.need_colour_apperance_count
			local cur_xl_grade = server_info.xiulian_grade
			local remind = true
			if need_orange_apperance_count > 0 then
				local has_num = self:GetApperaHasActNum(lingzhi_type, 4)
				remind = remind and has_num >= need_orange_apperance_count
			end
			if need_red_apperance_count > 0 then
				local has_num = self:GetApperaHasActNum(lingzhi_type, 5)
				remind = remind and has_num >= need_red_apperance_count
			end
			
			if need_purple_apperance_count > 0 then
				local has_num = self:GetApperaHasActNum(lingzhi_type, 3)
				remind = remind and has_num >= need_purple_apperance_count
			end

			if need_pink_apperance_count > 0 then
				local has_num = self:GetApperaHasActNum(lingzhi_type, 6)
				remind = remind and has_num >= need_pink_apperance_count
			end

			if need_golden_apperance_count > 0 then
				local has_num = self:GetApperaHasActNum(lingzhi_type, 7)
				remind = remind and has_num >= need_golden_apperance_count
			end

			if need_colour_apperance_count > 0 then
				local has_num = self:GetApperaHasActNum(lingzhi_type, 8)
				remind = remind and has_num >= need_colour_apperance_count
			end 

			if need_qihun_grade > 0 then
				remind = remind and cur_xl_grade >= need_qihun_grade
			end

			-- if lingzhi_type == LINGZHI_SKILL_TYPE.SHENBING then
			-- 	if upquality_orange_count > 0 then
			-- 		local has_num = self:GetApperaHasActNumByUpColor(lingzhi_type, 4)
			-- 		remind = remind and has_num >= upquality_orange_count
			-- 	end

			-- 	if upquality_red_count > 0 then
			-- 		local has_num = self:GetApperaHasActNumByUpColor(lingzhi_type, 5)
			-- 		remind = remind and has_num >= upquality_red_count
			-- 	end
			-- 	if upquality_pink_count > 0 then
			-- 		local has_num = self:GetApperaHasActNumByUpColor(lingzhi_type, 6)
			-- 		remind = remind and has_num >= upquality_pink_count
			-- 	end

			-- 	if upquality_colorful_count > 0 then
			-- 		local has_num = self:GetApperaHasActNumByUpColor(lingzhi_type, 7)
			-- 		remind = remind and has_num >= upquality_colorful_count
			-- 	end
			-- end

			if remind then
				return 1
			end
		-- 升级
		else
			local cur_cfg = self:GetFwSkillUpLevelCfg(lingzhi_type, skill_index, skill_level)
			local nex_cfg = self:GetFwSkillUpLevelCfg(lingzhi_type, skill_index, skill_level + 1)
			local has_num = server_info.lingzhi
			local need_num = cur_cfg and cur_cfg.cost 
			local role_level = RoleWGData.Instance:GetRoleLevel()
	
			if cur_cfg and has_num >= need_num and nex_cfg and role_level >= cur_cfg.role_level then
				return 1
			end
		end
	end

	return 0
end

-- 获取幻装激活信息
function LingZhiSkillWGData:GetApperaHasActNum(part_type, color)
	local num = 0
	if not part_type or not color then
		return num
	end
	
	local data_list = NewAppearanceWGData.Instance:GetShowListByTabIndex(part_type)
	for i,v in ipairs(data_list) do
		local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(part_type, v.index)
		if is_act then
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.active_stuff_id)
			local fashion_color = item_cfg and item_cfg.color or 0
			if fashion_color == color then
				num = num + 1
			end
		end
	end

	return num
end

function LingZhiSkillWGData:GetApperaHasActNumByUpColor(lingzhi_type, color)
	return 0
end

-- 修炼红点
function LingZhiSkillWGData:GetXLRemind(lingzhi_type)
	if IS_AUDIT_VERSION then 
		return 0
	end
	
	if not lingzhi_type then
		return 0
	end

	local server_info = self:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return 0
	end

	if server_info.is_buy ~= 1 then
		return 0
	else
		local fun_name = LingZhiSkillWGData.FunName[lingzhi_type]
		local fun_is_open = FunOpen.Instance:GetFunIsOpened(fun_name)
		if not fun_is_open then
			return 0
		end
	end

	local is_open = server_info.is_open == 1
	if not is_open then
		return 0
	end

	if self:GetCJRemind(lingzhi_type) then
		return 1
	end

	local grade = server_info.xiulian_grade or 1
	local cfg = self:GetXiuLianCfg(lingzhi_type, grade)
	if not cfg then
		return 0
	end

	local is_max_level = self:GetXiuLianCfg(lingzhi_type, grade + 1) == nil
	if is_max_level then
		return 0
	end

	local stuff_id = cfg.stuff_id or 0
	local has_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
	local need_num = cfg.stuff_count or 0
	local remind = has_num >= need_num and 1 or 0

	return remind
end

-- 成就红点
function LingZhiSkillWGData:GetCJRemind(lingzhi_type)
	for i=1,2 do
		if self:GetCJSingleRemind(lingzhi_type, i) then
			return true
		end
	end

	return false
end

function LingZhiSkillWGData:GetCJSingleRemind(lingzhi_type, view_type)
	if not lingzhi_type then
		return false
	end

	local server_info = self:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return false
	end

	if server_info.is_open ~= 1 or server_info.is_buy ~= 1 then
		return false
	end

	local flag_list
	if view_type == 1 then
		flag_list = server_info.grade_chengjiu_flag
	else
		flag_list = server_info.appe_chengjiu_flag
	end

	local ach_list = self:GetAchShowList(lingzhi_type, view_type)

	for i,v in ipairs(ach_list) do
		local index = v.cfg.index
		local is_act = flag_list[index] == 1

		if not is_act then
			if view_type == 1 then
				local need_grade = v.cfg.grade
				local has_grade = server_info.xiulian_grade

				if has_grade >= need_grade then
					return true
				end
			else
				local sz_is_act = self:GetWGHasAct(v.cfg, lingzhi_type)
				if sz_is_act then
					return true
				end
			end
		end
	end

	return false
end