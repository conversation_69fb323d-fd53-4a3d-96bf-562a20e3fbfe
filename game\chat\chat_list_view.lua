-----------------------------------------------------
-- 聊天列表
----------------------------------------------------
ChatListView = ChatListView or BaseClass()

ChatListView.IGNORE_OFFSET_Y = 200 -- 滚动多少距离后不自动调到最低


function ChatListView:__init()
	self.list_view = nil
	self.items = {}
	self.data_list = {}
	self.width = 0
	self.is_pressed = false
	self.cell_list = {}
	self.first_time_load = true
	self.channel_type = -1

	self.load_length = 0
	self.load_count_frame = 5 --每帧加载5个
	self.max_show_len = 6
end

function ChatListView:__delete()
	for i, v in ipairs(self.items) do
		v:DeleteMe()
	end
	self.items = {}

	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = {}
	end
	self.new_msg_refresh_callback = nil
	self.end_scroll_handle = nil
end

function ChatListView:GetView()
	return self.list_view
end

function ChatListView:SetNewMsgRefreshCallBack(callback)
	self.new_msg_refresh_callback = callback
end

function ChatListView:Create(item_render, list_view)
	if nil ~= self.list_view then
		return
	end

	self.item_render = item_render
	self.list_view = list_view

	self.list_view.scroller.scrollerEndScrolled = BindTool.Bind(self.ScrollerEndScrolled, self)
end

function ChatListView:SetScrollerEndHandle(end_scroll_handle)
	self.end_scroll_handle = end_scroll_handle
end

function ChatListView:ScrollerEndScrolled()
	if self.list_view.scroll_rect.verticalNormalizedPosition <= 0 and self.end_scroll_handle then
		self.end_scroll_handle()
	end
end

-- 获得数据源
function ChatListView:GetDataList()
	return 10
end

function ChatListView:FlushCompreView()
	self.list_view.scroller:ReloadData(1)
end

-- 设置数据源
function ChatListView:SetDataList(data_list, refresh, curr_send_channel)
	if nil == self.list_view and IsNil(self.list_view.scroller) then
		return 
	end
	
	self.data_list = data_list
	if self.first_time_load then
		local list_delegate = self.list_view.list_simple_delegate
		list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self)
		list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshListViewCells, self)
		list_delegate.CellSizeDel = BindTool.Bind(self.GetCellSizeDel, self)
		self.first_time_load = false
		self.load_length = #self.data_list
		self.list_view.scroller:ReloadData(1)
	else
		local last_data_list_count = self.load_length
		if self.load_time_quest == nil then
			self.load_length = #self.data_list
		end

		local new_data_count = self.load_length - last_data_list_count
		local has_mainrole_msg = false
		local role_id = RoleWGData.Instance:GetUUid().temp_low
		local merge_server_id = RoleWGData.Instance:GetMergeServerId()
		local merge_plat_type = RoleWGData.Instance:GetMergePlatType()

		if new_data_count > 0 then
			local start_index = self.load_length - new_data_count + 1
			for i = start_index, self.load_length do
				local data = self.data_list[i]
				if data.is_from_private and data.is_from_private == 1
					and data.from_uid and role_id == data.from_uid then
					has_mainrole_msg = true
					break
				elseif data.merge_server_id and merge_server_id == data.merge_server_id
					and data.origin_plat_type and merge_plat_type == data.origin_plat_type
					and data.from_uid and role_id == data.from_uid then
					has_mainrole_msg = true
					break
				end
			end
		end

		local cell_height = self.load_length == 0 and 1 or 1 / #self.data_list
		local is_msg_list_in_buttom = self.list_view.scroll_rect.verticalNormalizedPosition <= cell_height
		if has_mainrole_msg or self.load_length == 0 or IsEmptyTable(self.data_list) or is_msg_list_in_buttom then
			self.list_view.scroller:ReloadData(1)
		else
			if new_data_count > 0 then
				if self.new_msg_refresh_callback and self.list_view.scroll_rect.verticalNormalizedPosition ~= 0 then
					self.new_msg_refresh_callback()
				end
			end

			if curr_send_channel and curr_send_channel ~= self.channel_type then
				self.list_view.scroller:ReloadData(1)
			else
				self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
			end
		end
	end

	--[[
	if refresh ~= nil then
		if refresh == 0 then
			self.list_view.scroller:ReloadData(0) --跳到最顶部
		elseif refresh == 1 then
			self.list_view.scroller:ReloadData(1) --跳到最底部
		elseif refresh == 2 then
			self.list_view.scroller:RefreshActiveCellViews()
		elseif refresh == 3 then
			self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
		end
	end
	--]]
end

function ChatListView:GetScrollRectVerNorPos()
	return self.list_view.scroll_rect.verticalNormalizedPosition
end

function ChatListView:GetCellSizeDel(data_index)
	if self.load_time_quest ~= nil then
		data_index = #self.data_list - self.max_show_len + data_index + 1
	else
		data_index = data_index + 1
	end

	local msg_info = self.data_list[data_index]
	if not msg_info then
		return 0 end
	local height = ChatWGData.Instance:GetChannelItemHeight(self.channel_type, msg_info.msg_id)
	if height > 0 then
		self.data_list[data_index].chat_cell_height = height
		return height
	end

	-- local scroller_delegate = self.list_view.list_simple_delegate
	-- local chat_measuring = ChatWGCtrl.Instance:GetChatMeasuring(scroller_delegate)
	-- --if chat_measuring ~= nil then
	-- chat_measuring:SetData(msg_info)
	-- height = chat_measuring:GetContentHeight() --计算自身的高度
	-- ChatWGData.Instance:SetChannelItemHeight(self.channel_type, msg_info.msg_id, height)
	ChatWGCtrl.Instance:TryCalcH(msg_info)
	height = ChatWGData.Instance:GetChannelItemHeight(self.channel_type, msg_info.msg_id) or 0
	self.data_list[data_index].chat_cell_height = height
	return height --- 20
	--end
	--return nil
end

--获得格子数
function ChatListView:GetListViewNumbers()
	return self.load_length--#self.data_list
end

--刷新格子
function ChatListView:RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]
	if self.load_time_quest ~= nil then
		cell_index = #self.data_list - self.max_show_len + cell_index + 1
	else
		cell_index = cell_index + 1
	end

	if not item_cell then
		item_cell = self.item_render.New(cell.gameObject)
		item_cell:SetIsCalchighCell(false)
		-- item_cell:SetToggleGroup(self.list_view.toggle_group)
		-- self.cell_list[cell] = item_cell
		self.cell_list[cell] = item_cell
		-- cell.gameObject:GetComponent(typeof(UnityEngine.UI.Button)):AddClickListener(
		-- 	BindTool.Bind(self.ListEventCallback, self, item_cell))
	end
	local item_data = self.data_list[cell_index]
	item_cell:SetIndex(cell_index)
	item_cell:SetCurChannelType(self.channel_type)
	item_cell:SetData(item_data)
end

function ChatListView:GetAllItemList()
	local show_item_list = {}
	for k, v in pairs(self.cell_list) do
		if v.view.gameObject.activeInHierarchy then
			table.insert(show_item_list, v)
		end
	end
	return show_item_list
end

function ChatListView:GetAllShowItemIndexList()
	local show_item_index_list = {}
	for k, v in pairs(self.cell_list) do
		if v.view.gameObject.activeInHierarchy then
			table.insert(show_item_index_list, v.index)
		end
	end
	return show_item_index_list
end

function ChatListView:RemoveAt(index)
	if index <= 0 then
		return
	end

	local item = self:GetItemAt(index)
	if nil == item then
		return
	end

	-- self.list_view:removeItemByIndex(index - 1)
	item:DeleteMe()

	table.remove(self.items, index)
end

function ChatListView:GetItemAt(index)
	return self.items[index]
end

function ChatListView:GetAllItems()
	return self.items
end

function ChatListView:RemoveAllItem()
	for k, v in pairs(self.items) do
		v:DeleteMe()
	end
	self.items = {}
	self.list_view:RemoveAllItem()
end

function ChatListView:GetCount()
	return #self.items
end

-- 移动第一条到最后
function ChatListView:MoveFrontToLast(move_count)
	if move_count <= 0 then
		return
	end

	local item_count = #self.items
	if item_count <= 1 or move_count >= item_count then
		return
	end

	for i = 1, move_count do
		self.list_view:moveFrontToLast()

		local item = table.remove(self.items, 1)
		table.insert(self.items, item)
	end
end

function ChatListView:SetChannelType(channel_type)
	self.channel_type = channel_type
end