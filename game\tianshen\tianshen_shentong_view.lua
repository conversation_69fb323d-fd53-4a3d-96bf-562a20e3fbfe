local OperateType = {
	ACTIVE = 1, -- 激活
	UPLEVEL = 2, -- 升级
}

local ST_COLOR = {
	COLOR3B.PURPLE,
	COLOR3B.ORANGE,
	COLOR3B.RED,
	COLOR3B.PINK,
}
TianShenShenTongView = TianShenShenTongView or BaseClass(SafeBaseView)
function TianShenShenTongView:__init()
	self.view_name = GuideModuleName.TianShenShenTongView
	self.view_layer = UiLayer.Pop	-- 副本会掉落面板物品，需要面板与结算面板同层级
    self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/tianshen_prefab", "tianshen_shentong_view")
end

function TianShenShenTongView:__delete()

end

function TianShenShenTongView:ReleaseCallBack()
	if self.st_up_item then
		self.st_up_item:DeleteMe()
		self.st_up_item = nil
	end

	if self.item_group then
		for k,v in pairs(self.item_group) do
			v:DeleteMe()
		end
		self.item_group = nil
	end

	if self.skill_all_attrlist and #self.skill_all_attrlist > 0 then
		for _, skill_cell in ipairs(self.skill_all_attrlist) do
			skill_cell:DeleteMe()
			skill_cell = nil
		end

		self.skill_all_attrlist = nil
	end

	self.first_select_data = nil
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
	self.item_data_change_callback = nil
end

function TianShenShenTongView:LoadCallBack(index, loaded_times)
	self.first_load = true
	self.st_up_item = ItemCell.New(self.node_list.st_item_pos)
	 self.st_up_item:SetNeedItemGetWay(true)
	-- 点击按钮
	XUI.AddClickEventListener(self.node_list.st_btn,BindTool.Bind(self.ClickSTBtn,self))

	self.item_data_change_callback = BindTool.Bind(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
    local st_type_list =  {}
    for i=4,1,-1 do
		st_type_list = TianShenWGData.Instance:GetSTcfgByType(i)
		for i1,v in ipairs(st_type_list) do
			if TianShenWGData.Instance:GetSTRemindWithoutInvateTip(v.skill_id) then
				self:SaveJumpToNextSkill(v.skill_id)
				break
			end
		end
    end

	-- 基础属性
	if self.skill_all_attrlist == nil then
		self.skill_all_attrlist = {}
		for i = 1, 8 do
			local attr_obj = self.node_list.layout_skill_all_attr:FindObj(string.format("attr_%d", i))
			if attr_obj then
				local cell = CommonAddAttrRender.New(attr_obj)
				cell:SetIndex(i)
				cell:SetAttrNameNeedSpace(true)
				self.skill_all_attrlist[i] = cell
			end
		end
	end
end

function TianShenShenTongView:SaveJumpToNextSkill(skill_id)
	self.jump_next_skill_id = skill_id
end

function TianShenShenTongView:CloseCallBack()
	TipWGCtrl.Instance:ForceHideEffect()
end

function TianShenShenTongView:ShowIndexCallback(index, loaded_times)
end

function TianShenShenTongView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if not old_num or not new_num then
		return
	end
	if old_num >= new_num then return end
	self:FlushShiYong()
	self:FlushShenTongRight()
end

-- 神通刷新
function TianShenShenTongView:OnFlush(param_t, index)
	local skill_id = 0
	if param_t and param_t.all then
		if param_t.all.operate_param then
			self.first_select_data = param_t.all.operate_param
		end

		if param_t.all.to_ui_param then
			skill_id = tonumber(param_t.all.to_ui_param)
		end
	end

	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushShiYong()
			self:FlushShenTongRight()
		elseif k == "part" then
			if v['st_flush_left_view'] then
				self:FlushShiYong()
			end
			if v['st_flush_right_view'] then
				self:FlushShenTongRight()
			end
		elseif k == "upgrade_result" then
			if v["active"] then
				TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["st_effect_pos"]})
			elseif v["level"] then
				TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["st_effect_pos"]})
			end
		end
	end
	if  0 == skill_id and self.jump_next_skill_id ~= -998 then
		skill_id = self.jump_next_skill_id
		self.jump_next_skill_id = -998
	end

	if 0 ~= skill_id then
		self.first_select_data = TianShenWGData.Instance:GetSTServerInfoBySkillId(skill_id)
		if self.first_select_data then
			self:OnSelectSTItemRender(self.first_select_data)
		end
	end
end

function TianShenShenTongView:FlushShiYong()
	if nil == self.item_group then
		self.item_group = {}
		local node_num = self.node_list.layout_group.transform.childCount
		for i=1, node_num do
			local cell = STItemRender.New(self.node_list.layout_group:FindObj("layout_tf_di_"..i))
			cell:SetIndex(i)
			cell:SetParent(self)
			self.item_group[i] = cell
		end
	end

	local st_server_info = TianShenWGData.Instance:GetSTServerInfo()
	local skill_cfg = TianShenWGData.Instance:GetTianShenSTDataList()
	for k,v in pairs(self.item_group) do
		local skill_id = skill_cfg[k].skill_id
		v:SetData({skill_id = skill_id})
	end

	for k,v in pairs(self.item_group) do
		local skill_id = v:GetSkillID()
		if self.first_select_data then
			if self.first_select_data.skill_id == skill_id then
				self:OnSelectSTItemRender(st_server_info[skill_id])
				self.first_load = false
				self.first_select_data = nil
			end
		else
			if self.first_load then
				self:OnSelectSTItemRender(st_server_info[skill_id])
				-- 传入空展示总属性
				-- self:OnSelectSTItemRender(nil)
				self.first_load = false
			end
		end
	end
end

-- 刷新右边
function TianShenShenTongView:FlushShenTongRight()
	self.node_list.select_skill_all_attr_root:CustomSetActive(self.st_select_data == nil)
	self.node_list.select_skill_root:CustomSetActive(self.st_select_data ~= nil)

	if not self.st_select_data then
		self:FlushShenTongRightAllAttr()
		return
	end

	local tianshen_language = Language.TianShen
	local data = TianShenWGData.Instance:GetSTServerInfoBySkillId(self.st_select_data.skill_id)
	local skill_cfg = TianShenWGData.Instance:GetSTcfgById(data.skill_id)
	-- 技能信息Top
	self.node_list.st_skill_name.text.text = ToColorStr(skill_cfg.name, ITEM_COLOR[skill_cfg.color])
	self.node_list.st_skill_level.text.text = ToColorStr(string.format(Language.LongZhu.LongZhuTotalLevelDesc2, data.level) , ITEM_COLOR[skill_cfg.color])
	-- 是否激活
	local is_active = data.level > 0
	-- 是否满级
	local max_flag = data.level >= skill_cfg.max_level
	self.node_list.st_layout_3:SetActive(not max_flag)  --满级隐藏升级道具图标
	self.node_list["img_max_level"]:SetActive(max_flag)

	local cur_skill_cfg = nil
	if is_active then
		cur_skill_cfg = TianShenWGData.Instance:GetSTLevelcfg(data.skill_id, data.level) --当前等级配置
	else
		cur_skill_cfg = TianShenWGData.Instance:GetSTLevelcfg(data.skill_id, 1)  --一级配置
	end

	if not cur_skill_cfg then
		return
	end

	local next_upstar_cfg = TianShenWGData.Instance:GetSTLevelcfg(data.skill_id, data.level + 1)  --下一级配置
	local now_attri_list = AttributeMgr.GetAttributteByClass(cur_skill_cfg)

	self.node_list.desc_arrow:SetActive(false)
	self.node_list.next_shux_txt:SetActive(false)
	self.node_list.dq_shux:SetActive(false)
	if cur_skill_cfg.param_b > 0 then
		self.node_list.desc_and_shux_txt.text.text = cur_skill_cfg.desc
		self.node_list.dq_shux:SetActive(true)
		self.node_list.dq_shux.text.text = (cur_skill_cfg.param_b /10000 * 100).."%"
		if is_active and not max_flag then
			self.node_list.desc_arrow:SetActive(true)
			self.node_list.next_shux_txt:SetActive(true)
			self.node_list.next_shux_txt.text.text = ((next_upstar_cfg.param_b - cur_skill_cfg.param_b) /10000 * 100).."%"
		end
	elseif cur_skill_cfg.attr_per > 0 then
		self.node_list.desc_and_shux_txt.text.text = cur_skill_cfg.desc
		self.node_list.dq_shux:SetActive(true)
		self.node_list.dq_shux.text.text = (cur_skill_cfg.attr_per / 10000 * 100).."%"
		if is_active and not max_flag then
			self.node_list.desc_arrow:SetActive(true)
			self.node_list.next_shux_txt:SetActive(true)
			self.node_list.next_shux_txt.text.text = ((next_upstar_cfg.attr_per - cur_skill_cfg.attr_per) / 10000 * 100).."%"
		end
	else
		self.node_list.desc_and_shux_txt.text.text = cur_skill_cfg.desc
	end

	local next_attri_list
	local up_attri_list = AttributePool.AllocAttribute()

	local is_max_level = next_upstar_cfg == nil
	if not is_max_level then
		next_attri_list = AttributeMgr.GetAttributteByClass(next_upstar_cfg)
		up_attri_list = AttributeMgr.LerpAttributeAttr(now_attri_list, next_attri_list)
	end
	AttributeMgr.FlushAttr(self.node_list, now_attri_list, up_attri_list)

	self.st_can_active = false
	self.st_can_uplevel = false
	-- 技能信息Bottom
	if not is_active then
		self.node_list.st_title_3.text.text = tianshen_language.UpgradeText[1]
		self.node_list.st_btn_text.text.text = tianshen_language.OneKeyBtnText[7]
		XUI.SetButtonEnabled(self.node_list.st_btn, true)
		self.node_list.st_btn:SetActive(true)

		-- 激活条件
		local active_seq = skill_cfg.active_seq
		local need_active_image = active_seq ~= -1
		local active_stuff_id = skill_cfg.active_item_id
		local active_stuff_cfg = ItemWGData.Instance:GetItemConfig(active_stuff_id)
		local active_stuff_num = skill_cfg.active_item_num
		local active_has_num = ItemWGData.Instance:GetItemNumInBagById(active_stuff_id)
		self.active_item_id = active_stuff_id

		-- 需要激活形象
		if need_active_image then
			local had_active_image = TianShenWGData.Instance:IsActivation(active_seq)
			-- 已激活形象
			if had_active_image then
				self.st_can_active = active_has_num >= active_stuff_num
			else
				local image_cfg = TianShenWGData.Instance:GetTianShenCfg(active_seq)
				self.node_list.st_btn:SetActive(false)
			end
		else
			self.st_can_active = active_has_num >= active_stuff_num
		end
		-- 激活物品
		local color = active_has_num < active_stuff_num and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH

		self.st_up_item:SetFlushCallBack(function ()
			self.st_up_item:SetRightBottomColorText(ToColorStr(active_has_num .. '/' .. active_stuff_num ,color))
			self.st_up_item:SetRightBottomTextVisible(true)
		end)
		self.st_up_item:SetData({item_id = active_stuff_id})

	elseif max_flag then
		self.node_list.st_btn_text.text.text = tianshen_language.OneKeyBtnText[3]
		self.node_list.st_btn:SetActive(true)
		XUI.SetButtonEnabled(self.node_list.st_btn, false)

		self.st_up_item:SetFlushCallBack(function ()
			self.st_up_item:SetRightBottomColorText('--/--')
			self.st_up_item:SetRightBottomTextVisible(true)
		end)
		self.st_up_item:SetData({item_id = cur_skill_cfg.uplevel_item_id})
	else
		self.node_list.st_btn_text.text.text = tianshen_language.OneKeyBtnText[8]
		self.node_list.st_title_3.text.text = tianshen_language.UpgradeText[3]
		local role_level = RoleWGData.Instance:GetAttr("level")
		local need_level = cur_skill_cfg.uplevel_condition
		local uplevel_flag = role_level >= need_level
		local uplevel_item_cfg = ItemWGData.Instance:GetItemConfig(cur_skill_cfg.uplevel_item_id)
		local uplevel_has_num = ItemWGData.Instance:GetItemNumInBagById(cur_skill_cfg.uplevel_item_id)
		self.uplevel_item_id = cur_skill_cfg.uplevel_item_id

		-- 是否达到升级条件
		self.node_list.st_btn:SetActive(uplevel_flag)
		XUI.SetButtonEnabled(self.node_list.st_btn, true)
		local color = uplevel_has_num < cur_skill_cfg.uplevel_item_num and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
		self.st_can_uplevel = uplevel_has_num >= cur_skill_cfg.uplevel_item_num

		self.st_up_item:SetFlushCallBack(function ()
			self.st_up_item:SetRightBottomColorText(ToColorStr(uplevel_has_num .. '/' .. cur_skill_cfg.uplevel_item_num ,color))
			self.st_up_item:SetRightBottomTextVisible(true)
		end)
		self.st_up_item:SetData({item_id = cur_skill_cfg.uplevel_item_id})
	end
	local have_red = TianShenWGData.Instance:GetSTRemindWithoutInvateTip(data.skill_id)
	self.node_list.st_btn_remind.image.enabled = have_red
	self.node_list["st_btn"]:SetActive(not max_flag)

	if not self.old_select_skill or self.old_select_skill ~= self.st_select_data.skill_id then
		self.old_have_red = have_red
		self.old_select_skill = self.st_select_data.skill_id
	else
		if self.old_have_red and not have_red then
			self.old_have_red = have_red
			local st_type_list =  {}
    		for i=4,1,-1 do
				st_type_list = TianShenWGData.Instance:GetSTcfgByType(i)
				for i1,v in ipairs(st_type_list) do
					if TianShenWGData.Instance:GetSTRemindWithoutInvateTip(v.skill_id) then
						self:SaveJumpToNextSkill(v.skill_id)
						self:Flush()
						break
					end
				end
    		end
    	else
    		self.old_have_red = have_red
		end
	end
end

-- 设置总属性
function TianShenShenTongView:FlushShenTongRightAllAttr()
	local skill_cfg = TianShenWGData.Instance:GetTianShenSTDataList()

	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end
		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end

	for _, skill_data in pairs(skill_cfg) do
		if skill_data and skill_data.skill_id then
			local data = TianShenWGData.Instance:GetSTServerInfoBySkillId(skill_data.skill_id)
			local cur_skill_cfg = TianShenWGData.Instance:GetSTLevelcfg(skill_data.skill_id, data and data.level or 0) --当前等级配置
			local attr_list = AttributeMgr.GetAttributteByClass(cur_skill_cfg)

			for k, v in pairs(attr_list) do
				if v > 0 then
					add_tab(k, v)
				end
			end
		end
	end

	local new_attr_list = EquipWGData.GetSortAttrListHaveAddByCfg(attribute)
	self.node_list.layout_not_have_all_attr:CustomSetActive(#new_attr_list <= 0)

	for i, skill_cell in ipairs(self.skill_all_attrlist) do
		if new_attr_list[i] then
			skill_cell:SetVisible(true)
			skill_cell:SetData(new_attr_list[i])
		else
			skill_cell:SetVisible(false)
		end
	end
end

function TianShenShenTongView:OnSelectSTItemRender(data)
	if data ~= nil then
		-- 切换高亮
		for k,v in pairs(self.item_group) do
			local skill_id = v:GetSkillID()
			v:ChangeHigh(skill_id == data.skill_id)
		end
	end

	self.st_select_data = data
	self:FlushShenTongRight()
end

-- 点击操作
function TianShenShenTongView:ClickSTBtn()
	if not self.st_select_data then return end
	local data = TianShenWGData.Instance:GetSTServerInfoBySkillId(self.st_select_data.skill_id)
	-- 能激活
	if self.st_can_active and data.level == 0 then

		--EffectManager.Instance:PlayCommonSuccessEffect(self.node_list["st_effect_pos"], nil, nil, nil, nil, Ui_Effect.UI_jihuochenggong) --通用成功特效
		TianShenWGCtrl.Instance:SendShenTongOperaReq(OperateType.ACTIVE, data.skill_id)
		return
	-- 能升级
	elseif self.st_can_uplevel then
		--EffectManager.Instance:PlayCommonSuccessEffect(self.node_list["st_effect_pos"], nil, nil, nil, nil, Ui_Effect.UI_SJcg) --通用成功特效
		
		local num = TianShenWGData.Instance:GetUplevelNum(data)
		TianShenWGCtrl.Instance:SendShenTongOperaReq(OperateType.UPLEVEL, data.skill_id, num)
		return
	end

	if not self.st_can_active and data.level == 0 then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.active_item_id})
		return
	end

	if not self.st_can_uplevel then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.uplevel_item_id})
		return
	end
end


STItemRender = STItemRender or BaseClass(BaseRender)

function STItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.skill_icon, BindTool.Bind(self.OnClickST,self))
	-- self:ChangeHigh(self.high_flag == true)
end

function STItemRender:__delete()
	self.parent = nil
end

function STItemRender:SetParent(parent)
	self.parent = parent
end

function STItemRender:OnClickST()
	-- 刷新面板
	self.parent:OnSelectSTItemRender(self.data)
end

function STItemRender:ChangeHigh(enable)
	if not self.node_list then
		self.high_flag = enable
		return
	end
	
	self.node_list.high:SetActive(enable)
end

function STItemRender:GetSkillID()
	return self.data.skill_id
end

function STItemRender:OnFlush()
	local skill_cfg = TianShenWGData.Instance:GetTianShenSTDataList()
	local item_cfg = ItemWGData.Instance:GetItemConfig(skill_cfg[self.index].active_item_id)
	self.node_list.skill_icon.image:LoadSprite(ResPath.GetF2TianShenIcon(string.format("a3_shenge_%d", self.data.skill_id)))
	self.node_list.active_skill_icon.image:LoadSprite(ResPath.GetF2TianShenIcon(string.format("a3_shenge_%d_hl", self.data.skill_id)))
	self.node_list.remind:CustomSetActive(TianShenWGData.Instance:GetSTRemindWithoutInvateTip(skill_cfg[self.index].skill_id))
	
	local skill_data = TianShenWGData.Instance:GetSTServerInfoBySkillId(self.data.skill_id)
	self.node_list.active_skill_icon:SetActive(skill_data and skill_data.level > 0)
	self.node_list.lv_bg:SetActive(skill_data and skill_data.level > 0)
	XUI.SetGraphicGrey(self.node_list["skill_icon"], skill_cfg[self.index].level == 0)
	self.node_list.lv_txt.text.text = skill_data and skill_data.level or 0
end
