OperationTaskChainSkillView = OperationTaskChainSkillView or BaseClass(SafeBaseView)

local SKILL_POS = {
	[1] = {
		[1] = {x = 31, y = 2.5},
	},
	[2] = {
		[1] = {x = -89, y = -111.5},
		[2] = {x = 56.3, y = 28}
	},
	[3] = {
		[1] = {x = -152.7, y = -111.5},
		[2] = {x = -66.7, y = 2.5},
		[3] = {x = 56.3, y = 28},
	},
}

function OperationTaskChainSkillView:__init()
	self.view_layer = UiLayer.MainUILow
	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain_skill")
end

function OperationTaskChainSkillView:ReleaseCallBack()
    if self.task_chain_skill_list ~= nil then
    	for k,v in pairs(self.task_chain_skill_list) do
    		if v then
    			v:DeleteMe()
    		end
    	end

    	self.task_chain_skill_list = nil
    end

   	if self.skill_render then
		self.skill_render:SetInstanceParent(self.root_node_transform)
		self.skill_render:DeleteMe()
		self.skill_render = nil
	end
end

function OperationTaskChainSkillView:LoadCallBack()
	-- self.node_list.btn_follow.button:AddClickListener(BindTool.Bind1(self.OnClickFollow, self))
	local callback = function ()
			local parent = MainuiWGCtrl.Instance:GetSkillContent()
			self.node_list.root_skill:SetActive(true)
			if parent then
				self.skill_render = BaseRender.New(self.node_list.root_skill)
				self.skill_render:SetInstanceParent(parent)
			else
				print_error("can not find the parent!!")
			end

			self:Flush()
	end

	MainuiWGCtrl.Instance:AddInitCallBack(nil,callback)
end

function OperationTaskChainSkillView:OpenCallBack()
end

function OperationTaskChainSkillView:CloseCallBack()
	if self.skill_render then
		self.skill_render:SetInstanceParent(self.root_node_transform)
		self.skill_render:DeleteMe()
		self.skill_render = nil
	end
end

function OperationTaskChainSkillView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushSkill()
		end
	end
end

function OperationTaskChainSkillView:FlushSkill()
	local is_need_load = false
	if self.task_chain_skill_list == nil then
		is_need_load = true
	end

	local show_list = OperationTaskChainWGData.Instance:GetShowSkillList()
	if show_list == nil or #show_list == 0 then
		return
	end

	local show_num = #show_list
	if self.task_chain_skill_list ~= nil and show_num > #self.task_chain_skill_list then
		is_need_load = true
	end

	local pos_list = SKILL_POS[show_num]

	if is_need_load then
		local asset,bundle = ResPath.GetOperationTaskChainPrefabF2("render_task_chain_skill")
		local res_async_loader = AllocResAsyncLoader(self, "render_task_chain_skill")
		res_async_loader:Load(asset, bundle, nil,
			function(new_obj)
				if nil == new_obj then return end

				local cur_num = 1
				if self.task_chain_skill_list ~= nil then
					cur_num = show_num - #self.task_chain_skill_list
				else
					self.task_chain_skill_list = {}
				end

				for i = cur_num, show_num do
					local obj = ResMgr:Instantiate(new_obj)
					local obj_transform = obj.transform
					obj_transform:SetParent(self.node_list.root_obj.transform, false)
					local item_render = TaskChainSkillRender.New(obj)
					table.insert(self.task_chain_skill_list, item_render)
				end

				for k,v in pairs(self.task_chain_skill_list) do
					v:SetData(show_list[k])
					if pos_list ~= nil and pos_list[k] ~= nil then
						v:SetLocalPosition(pos_list[k].x, pos_list[k].y)
					end
				end
			end)
	else
		if self.task_chain_skill_list == nil then
			return
		end

		for i = 1, show_num do
			if self.task_chain_skill_list[i] ~= nil then
				self.task_chain_skill_list[i]:SetActive(true)
				self.task_chain_skill_list[i]:SetData(show_list[i])
				if pos_list ~= nil and pos_list[i] ~= nil then
					self.task_chain_skill_list[i]:SetLocalPosition(pos_list[i].x, pos_list[i].y)
				end
			end
		end

		if show_num < #self.task_chain_skill_list then
			for i = show_num, #self.task_chain_skill_list do
				if self.task_chain_skill_list[i] ~= nil then
					self.task_chain_skill_list[i]:SetActive(false)
				end
			end
		end
	end
end

-----------------------------------TaskChainSkillRender------------------------------------------------
TaskChainSkillRender = TaskChainSkillRender or BaseClass(BaseRender)

function TaskChainSkillRender:__init()
	self:AddClickEventListener(BindTool.Bind(self.UseSkill, self))
end

function TaskChainSkillRender:__delete()
end

function TaskChainSkillRender:OnFlush()
	if self.data == nil then
		return
	end

	self.node_list.str_name.text.text = self.data.name
	if self.data.img ~= nil then
		self.node_list.img_skill.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.data.img))
		self.node_list.img_skill.image:SetNativeSize()
	end
end

function TaskChainSkillRender:UseSkill()
	if self.data == nil then
		return
	end

	local info = OperationTaskChainWGData:GetInfoBySceneType(Scene.Instance:GetSceneType())
	if info ~= nil and info.task_status == OPERATION_TASK_CHAIN_ACT_STATUS.STANDY then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.TaskChain.IsStandy)
		return
	end

	if self.data.skill_type == OPERATION_TASK_CHAIN_SKILL_TYPE.GATHER then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		local obj = Scene.Instance:SelectRandGatherByDis(nil, self.data.param_2)
		if obj ~= nil then
			MoveCache.SetEndType(MoveEndType.Gather)
			MoveCache.target_obj = obj
			GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), obj.vo.pos_x, obj.vo.pos_y, COMMON_CONSTS.GATHER_TRIIGER_RANGE)
		else
            local cfg = OperationTaskChainWGData.Instance:GetXunLuoCaiJiDungeonInfo()
            local name = ""
            if cfg ~= nil then
                name = cfg.item_name
            end	
            		
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.OpertionAcitvity.TaskChain.NoCanGather, name))
		end

	elseif self.data.skill_type == OPERATION_TASK_CHAIN_SKILL_TYPE.SUBMISSION then
		GuajiWGCtrl.Instance:MoveToNpc(self.data.npc_id, nil, Scene.Instance:GetSceneId())
	elseif self.data.skill_type == OPERATION_TASK_CHAIN_SKILL_TYPE.CHECK_GATHER then
		local main_role = Scene.Instance:GetMainRole()
		if main_role ~= nil then
			-- local m_x, m_y = main_role:GetLogicPos()
			-- local obj = Scene.Instance:GetGatherByGatherIdAndPosInfo(self.data.param, m_x, m_y)
			local obj = nil
			if self.data.param ~= nil then
				obj = Scene.Instance:SelectNearGatherById(self.data.param_3, self.data.param)
			else
				obj = Scene.Instance:SelectNearGather(self.data.param_3)
			end

			if obj == nil or obj:IsDeleted() then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.OpertionAcitvity.TaskChain.IsNoCanGather, self.data.param_2))
			else
				GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
				MoveCache.SetEndType(MoveEndType.Gather)
				MoveCache.target_obj = obj
				GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), obj.vo.pos_x, obj.vo.pos_y, COMMON_CONSTS.GATHER_TRIIGER_RANGE)				
			end
		end
	end
end