---------------------------------------------
-- 双修-好感度
---------------------------------------------
AffectionLevelView = AffectionLevelView or BaseClass(SafeBaseView)

local ARTIFACT_RENDER_HEIGHT_EXPAND = 142
local ARTIFACT_RENDER_HEIGHT_SHRINK = 94

function AffectionLevelView:__init()
	self.view_style = ViewStyle.Window
	self:SetMaskBg(true, true)
	self.is_safe_area_adapter = true
	
	self:AddViewResource(0, "uis/view/artifact_ui_prefab", "layout_artifact_affection_reward")
end

function AffectionLevelView:__delete()

end

function AffectionLevelView:LoadCallBack()
	if not self.affection_level_list then
		self.affection_level_list = AsyncListView.New(AffectionLevelItem, self.node_list["level_reward_list"])
	end

	XUI.AddClickEventListener(self.node_list["btn_go_uplevel"], BindTool.Bind1(self.OnClickGoToUpLevelBtn, self))
end

function AffectionLevelView:ReleaseCallBack()
	if self.affection_level_list then
		self.affection_level_list:DeleteMe()
		self.affection_level_list = nil
	end

	self.data = nil
end

function AffectionLevelView:SetDataAndOpen(data)
	self.data = data
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function AffectionLevelView:OnFlush(param_t, index)
	if not self.data then
		return
	end
	local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.seq)
	if not artifact_data then
		return
	end

	local data_list = ArtifactWGData.Instance:GetAffectionLevelCfgList(self.data.seq)
	self.affection_level_list:SetDataList(data_list)

	local affection_level_cfg = ArtifactWGData.Instance:GetAffectionLevelCfg(self.data.seq, artifact_data.favor_level)
	self.node_list["txt_level_name"].text.text = string.format(Language.Artifact.AffectionName, affection_level_cfg.favor_level_name)

	local affection_next_level_cfg = ArtifactWGData.Instance:GetAffectionLevelCfg(self.data.seq, artifact_data.favor_level + 1)
	local is_max_affection_level = affection_next_level_cfg == nil
	local value_str = "MAX"
	local slider_value = 1
	if not is_max_affection_level then
		value_str = artifact_data.favor_value .. "/" ..affection_next_level_cfg.need_favor
		slider_value = (artifact_data.favor_value - affection_level_cfg.need_favor) / (affection_next_level_cfg.need_favor - affection_level_cfg.need_favor)
	end
	self.node_list["txt_affection_value"].text.text = value_str
	self.node_list["affection_slider"].slider.value = slider_value
end

function AffectionLevelView:OnClickGoToUpLevelBtn()
	self:Close()
	ViewManager.Instance:Open(GuideModuleName.ArtifactView, TabIndex.artifact_affection, "show_send_gift", {to_ui_param = self.data.seq})
end

---------------------------------------
-- 好感等级Item
---------------------------------------
AffectionLevelItem = AffectionLevelItem or BaseClass(BaseRender)

function AffectionLevelItem:__init()

end

function AffectionLevelItem:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
		self.reward_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list["btn_receive"], BindTool.Bind1(self.OnClickReceiveBtn, self))
end

function AffectionLevelItem:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function AffectionLevelItem:OnFlush()
	if not self.data then
		return
	end

	local attr_name = EquipmentWGData.Instance:GetAttrName(self.data.attr_id)
	self.node_list["attr_name"].text.text = attr_name
	self.node_list["attr_add_value"].text.text = string.format(Language.Artifact.AddAttr, self.data.attr_value)
	self.node_list["txt_level"].text.text = string.format(Language.Artifact.Level, self.data.favor_level)
	self.node_list["txt_level_name"].text.text = self.data.favor_level_name

	local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.seq)
	if not artifact_data then
		return
	end

	local reward_flag = ArtifactWGData.Instance:GetAffectionLevelRewardFlag(self.data.seq, self.data.favor_level)
	self.reward_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetLingQuVisible(reward_flag == 1)
        end
    end)
	self.reward_list:SetDataList(self.data.reward_item)
	self.node_list["img_mask"]:SetActive(self.data.favor_level > artifact_data.favor_level)
	local can_receive = reward_flag == 0 and self.data.favor_level <= artifact_data.favor_level
	self.node_list["remind"]:SetActive(can_receive)
	self.node_list["btn_receive"]:SetActive(can_receive)
end

function AffectionLevelItem:OnClickReceiveBtn()
	ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.FETCH_FAVOR_REWARD, self.data.seq, self.data.favor_level)
end