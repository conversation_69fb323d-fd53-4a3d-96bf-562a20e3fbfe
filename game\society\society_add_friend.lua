AddFriendView = AddFriendView or BaseClass(SafeBaseView)

function AddFriendView:__init()
	self.data = {}
	-- self:SetModal(true)
	self.view_layer = UiLayer.PopTop
	self:SetMaskBg(true)
	local assetbundle = "uis/view/society_ui_prefab"
	self:AddViewResource(0, assetbundle, "layout_addfriend")
end

function AddFriendView:__delete()
	self.input_add_friend = nil
end

function AddFriendView:CloseCallBack()
	self.add_username = nil
	self.input_add_friend = nil
end
function AddFriendView:OpenCallBack()
	if self.node_list["inp_addfriend_name"] then
		self.input_add_friend = self.node_list["inp_addfriend_name"]:GetComponent(typeof(TMPro.TMP_InputField))
		if nil ~= self.add_username then
			self.input_add_friend.text = self.add_username
		end
	end
end
function AddFriendView:LoadCallBack()
	
	self.input_add_friend = self.node_list["inp_addfriend_name"]:GetComponent(typeof(TMPro.TMP_InputField))
	XUI.AddClickEventListener(self.node_list["btn_addfd_ok"], BindTool.Bind1(self.OnAddFriend, self))
	XUI.AddClickEventListener(self.node_list["btn_close_window_12"], BindTool.Bind1(self.CloseThieChuangKou, self))
	if nil ~= self.add_username then
		self.input_add_friend.text = self.add_username
	end
end
function AddFriendView:CloseThieChuangKou()
	self:Close()
end
-- 添加好友
function AddFriendView:OnAddFriend()
	local game_name = self.input_add_friend.text
	self.input_add_friend.text = ""
	local msg_ctrl = SysMsgWGCtrl.Instance
	--print_error(game_name)
	if nil ~= game_name and "" ~= game_name then
		if SocietyWGData.Instance:FindFriendByName(game_name) then
			if nil ~= Language.Society["AlreadyYouFriend"] then
				msg_ctrl:ErrorRemind(Language.Society["AlreadyYouFriend"])
			end
			return
		end

		SocietyWGCtrl.Instance:GetUserInfoByName(2151,game_name,function (flag, user_info)
			--找不到角色的时候
			if 0 == flag or nil == user_info then
				if nil ~= Language.Society["UserNotExist"] then
					msg_ctrl:ErrorRemind(Language.Society["UserNotExist"])
				end
				return
			end

			--添加的人为自己的时候
			if user_info.role_id == GameVoManager.Instance:GetMainRoleVo().role_id then
				if nil ~= Language.Society["NotAddSelf"] then
					msg_ctrl:ErrorRemind(Language.Society["NotAddSelf"])
				end
				return
			end
	--print_error(game_name,user_info)
				
			SocietyWGCtrl.Instance:IAddFriend(user_info.role_id)
			-- self:SetUiVisible("layout_addfriend",false)
		end)
	end

end

function AddFriendView:SetFriendName(name)
	self.add_username = name
end