-- B-本服刺探.xls
local item_table={
[1]={item_id=29476,num=7,is_bind=1},
[2]={item_id=26148,num=2,is_bind=1},
[3]={item_id=26149,num=2,is_bind=1},
[4]={item_id=29476,num=8,is_bind=1},
[5]={item_id=26148,num=3,is_bind=1},
[6]={item_id=29476,num=9,is_bind=1},
[7]={item_id=26149,num=3,is_bind=1},
[8]={item_id=29477,num=1,is_bind=1},
[9]={item_id=57837,num=3,is_bind=1},
[10]={item_id=27741,num=40,is_bind=1},
[11]={item_id=29476,num=10,is_bind=1},
[12]={item_id=56317,num=3,is_bind=1},
[13]={item_id=26148,num=5,is_bind=1},
[14]={item_id=26149,num=5,is_bind=1},
[15]={item_id=27741,num=30,is_bind=1},
[16]={item_id=27741,num=20,is_bind=1},
[17]={item_id=57837,num=2,is_bind=1},
[18]={item_id=27741,num=10,is_bind=1},
[19]={item_id=29476,num=6,is_bind=1},
[20]={item_id=29476,num=3,is_bind=1},
[21]={item_id=26421,num=1,is_bind=1},
[22]={item_id=26425,num=1,is_bind=1},
[23]={item_id=29476,num=2,is_bind=1},
[24]={item_id=56316,num=4,is_bind=1},
[25]={item_id=56317,num=1,is_bind=1},
[26]={item_id=56316,num=6,is_bind=1},
[27]={item_id=56316,num=8,is_bind=1},
[28]={item_id=57837,num=1,is_bind=1},
[29]={item_id=56317,num=2,is_bind=1},
[30]={item_id=56316,num=10,is_bind=1},
[31]={item_id=56316,num=12,is_bind=1},
[32]={item_id=26148,num=4,is_bind=1},
[33]={item_id=26149,num=4,is_bind=1},
[34]={item_id=29476,num=5,is_bind=1},
[35]={item_id=27741,num=5,is_bind=1},
[36]={item_id=56316,num=2,is_bind=1},
}

return {
enter_other_gs={
{}
},

enter_other_gs_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
open_time={
{}
},

open_time_meta_table_map={
},
npc_info={
{show_map_name="铜弥",information_type="1|2",information_pro="6|4",gather_time=5000,npc_talk="铜弥龙穴，可获得<color=#3be0f9>低品龙蛋</color>、<color=#b759fa>普通龙蛋</color>（采集并带回高级、史诗龙蛋可以在破坏阶段召唤帮手啊）",},
{npc_id=51002,show_map_name="翡翠",npc_name="蓝紫橙龙蛋点",pos_x=177,pos_y=266,information_type="1|2|3",information_pro="400|267|333",npc_talk="翡翠龙穴，可获得<color=#3be0f9>低品龙蛋</color>、<color=#b759fa>普通龙蛋</color>、<color=#ff6000>高级龙蛋</color>（采集并带回高级、史诗龙蛋可以在破坏阶段召唤帮手啊）",},
{npc_id=51003,npc_name="紫橙红龙蛋点1",pos_x=136,pos_y=318,npc_talk="金骸龙穴，可获得<color=#b759fa>普通龙蛋</color>、<color=#ff6000>高级龙蛋</color>、<color=#ec0228>史诗龙蛋</color>（采集并带回高级、史诗龙蛋可以在破坏阶段召唤帮手啊）",},
{npc_id=51004,npc_name="紫橙红龙蛋点2",pos_x=134,pos_y=259,gather_time=15000,information_num=2,},
{npc_id=51005,npc_name="紫橙红龙蛋点3",pos_x=125,pos_y=150,gather_time=15000,information_num=3,}
},

npc_info_meta_table_map={
},
information_reward={
{},
{information_type=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},information_name="普通龙蛋",num=2,attacker_add_score=450,image_id=5,},
{information_type=3,reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[3]},information_name="高级龙蛋",num=4,attacker_add_score=600,image_id=6,},
{information_type=4,information_num=3,reward_item={[0]=item_table[6],[1]=item_table[5],[2]=item_table[7],[3]=item_table[8]},information_name="史诗龙蛋",num=8,attacker_add_score=1000,image_id=9,}
},

information_reward_meta_table_map={
},
information_color={
{},
{index=2,npc_id=51002,information_type=3,},
{index=3,npc_id=51003,information_type=4,}
},

information_color_meta_table_map={
},
score_rank_reward={
{reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=2,rank_min=2,rank_max=2,reward_item={[0]=item_table[9],[1]=item_table[15],[2]=item_table[4],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=3,rank_min=3,rank_max=3,reward_item={[0]=item_table[9],[1]=item_table[16],[2]=item_table[1],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=4,rank_min=4,rank_max=4,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[19],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=5,rank_min=5,rank_max=5,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[20],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=6,rank_min=6,rank_max=6,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[20],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=7,rank_min=7,rank_max=7,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[20],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=8,rank_min=8,rank_max=8,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[20],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=9,rank_min=9,rank_max=9,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[20],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=10,rank_min=10,rank_max=10,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[20],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},
{rank_index=11,rank_min=11,rank_max=11,},
{rank_index=12,rank_min=12,rank_max=12,},
{rank_index=13,rank_min=13,rank_max=13,},
{rank_index=14,rank_min=14,rank_max=14,},
{rank_index=15,rank_min=15,rank_max=15,},
{rank_index=16,rank_min=16,rank_max=16,},
{rank_index=17,rank_min=17,rank_max=17,},
{rank_index=18,rank_min=18,rank_max=18,},
{rank_index=19,rank_min=19,rank_max=19,},
{rank_index=20,rank_min=20,rank_max=20,}
},

score_rank_reward_meta_table_map={
},
spy_boss={
{}
},

spy_boss_meta_table_map={
},
spy_boss_attack={
{},
{need_number=10,attack_boss_info="224,337",},
{need_number=20,attack_boss_info="230,330",}
},

spy_boss_attack_meta_table_map={
},
attack_task_reward={
{},
{index=2,need_score=1000,reward_item={[0]=item_table[21],[1]=item_table[22],[2]=item_table[23],[3]=item_table[24]},},
{index=3,need_score=1500,reward_item={[0]=item_table[21],[1]=item_table[22],[2]=item_table[23],[3]=item_table[25],[4]=item_table[26]},},
{index=4,need_score=2000,reward_item={[0]=item_table[21],[1]=item_table[22],[2]=item_table[23],[3]=item_table[25],[4]=item_table[27]},},
{index=5,need_score=3000,reward_item={[0]=item_table[28],[1]=item_table[21],[2]=item_table[22],[3]=item_table[23],[4]=item_table[29],[5]=item_table[30]},},
{index=6,need_score=5000,reward_item={[0]=item_table[28],[1]=item_table[21],[2]=item_table[22],[3]=item_table[23],[4]=item_table[12],[5]=item_table[31]},}
},

attack_task_reward_meta_table_map={
},
shanghai_reward={
{},
{rank_min=2,rank_max=2,},
{rank_min=3,rank_max=3,},
{rank_min=4,rank_max=4,},
{rank_min=5,rank_max=5,reward_item={[0]=item_table[17],[1]=item_table[4],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14],[5]=item_table[8]},},
{rank_min=6,rank_max=6,},
{rank_min=7,rank_max=7,},
{rank_min=8,rank_max=8,reward_item={[0]=item_table[17],[1]=item_table[1],[2]=item_table[29],[3]=item_table[32],[4]=item_table[33],[5]=item_table[8]},},
{rank_min=9,rank_max=9,},
{rank_min=10,rank_max=10,}
},

shanghai_reward_meta_table_map={
[6]=5,	-- depth:1
[7]=5,	-- depth:1
[9]=8,	-- depth:1
[10]=8,	-- depth:1
},
robot_point={
{}
},

robot_point_meta_table_map={
},
enter_other_gs_default_table={enter_level=9999,enter_scene_id=5100,task_scene_id=1003,enter_pos_x=35,enter_pos_y=236,relive_pos="35,236",protect_buff_id=1005,back_scene_id=1003,back_pos_x=35,back_pos_y=236,cross_bgm=1102,ui_level=200,},

other_default_table={spy_task_id=51000,task_count=3,add_task_npc=10375,portal_pos_x=35,portal_pos_y=236,call_cd_time=60,reward_item={[0]=item_table[34]},npc_talk="前往敌国主城刺探龙蛋，将龙蛋带回我这里，<color=#ec0228>品质越高的龙蛋奖励越好</color>，刺探时间为每日的<color=#95d12b>10:00~24:00</color>",},

open_time_default_table={is_open=0,start_time=1600,end_time=1630,server_count=2,spy_boss_start_time=1612,spy_boss_end_time=1630,},

npc_info_default_table={npc_id=51001,show_map_name="金骸",npc_name="蓝紫龙蛋点",pos_x=163,pos_y=221,information_type="2|3|4",information_pro="25|25|50",gather_time=10000,information_num=1,scene_id=5100,npc_talk="前往第一个金骸龙穴获得<color=#ec0228>史诗龙蛋</color>碎片，再来找我吧！",},

information_reward_default_table={information_type=1,information_num=1,reward_item={[0]=item_table[19],[1]=item_table[2],[2]=item_table[3]},information_name="低品龙蛋",num=1,attacker_add_score=300,image_id=7,},

information_color_default_table={index=1,npc_id=51001,information_type=2,},

score_rank_reward_default_table={rank_index=1,rank_min=1,rank_max=1,reward_item={[0]=item_table[28],[1]=item_table[35],[2]=item_table[20],[3]=item_table[25],[4]=item_table[13],[5]=item_table[14],[6]=item_table[8]},},

spy_boss_default_table={boss_id=5505,boss_pos="230,337",attack_skill_id="500|501",relive_buff_id=220,gather_buff_id=221,boss_hp_check_time=3,boss_hp_box="30:44317:10:12823|40:44317:10:12823|50:44317:10:12823|60:44317:10:12823|70:44317:10:12823|80:44317:10:12823|90:44317:10:12823",box_reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[3]},boss_hp_box_pos="230,328",boss_hp_score="30:10000|50:30000|70:50000|90:80000",box_time=3,box_duration_time=5,get_box_max_count=10,attack_boss_add_score=2,skill_use_item="500,26580,1,1|501,26581,1,1|502,26582,1,1|503,26583,1,1",boss_scene_id=5100,},

spy_boss_attack_default_table={need_number=5,attack_boss_id=5506,attack_boss_info="230,341",},

attack_task_reward_default_table={index=1,need_score=600,reward_item={[0]=item_table[21],[1]=item_table[22],[2]=item_table[23],[3]=item_table[36]},},

shanghai_reward_default_table={rank_min=1,rank_max=1,reward_item={[0]=item_table[9],[1]=item_table[11],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14],[5]=item_table[8]},},

robot_point_default_table={}

}

