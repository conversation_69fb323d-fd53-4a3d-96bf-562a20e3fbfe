ReviveWGData = ReviveWGData or BaseClass()

ReviveDataTime = {
	RevivieTime = 30
}

ReviveDataItemId = {
	ItemId = 26900
}

ReviveDataMoney = {
	Money = 20
}

function ReviveWGData:__init()
	if ReviveWGData.Instance ~= nil then
		print_error("[ReviveWGData] Attemp to create a singleton twice !")
		return
	end
	self.killer_name = ""
	ReviveWGData.Instance = self
	self:GetTodayFreeReviveNum()

	self.revive_type = -1
end

function ReviveWGData:__delete()
	self.revive_info = nil
	ReviveWGData.Instance = nil
end

function ReviveWGData:SetRoleReviveInfo(portocol)
	self.revive_info = portocol
end

function ReviveWGData:GetRoleReviveInfo()
	return self.revive_info
end

function ReviveWGData:SetKillerName(name)
	self.killer_name = name
end

function ReviveWGData:GetKillerName()
	return self.killer_name
end

function ReviveWGData:GetGong<PERSON>eng()
	self.gongneng_sort = {
		[1]={	-- 锻造
			img_name = 'Forge',
			view_name = "Forge",
		},
		[2]={	-- 形象
			img_name = 'Iconic',
			view_name = "Advance",
		},
		-- [3]={	-- 女神
		-- 	img_name = 'Goddress',
		-- 	view_name = "Goddess",
		-- },
		[3]={	-- 功勋
			img_name = 'Treasure',
			view_name = "BaoJu",
		},
		[4]={	-- 精灵
			img_name = 'Spirit',
			view_name = "SpiritView",
		},
	}
	return self.gongneng_sort
end

function  ReviveWGData:SetReviveFreeTime(portocol)
	self.UsedTime = portocol.param1
end

function  ReviveWGData:GetTodayFreeReviveNum()
	self.today_free_revive_num=ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1].today_free_relive_num
end

--记录当前点击的复活类型
function ReviveWGData:SetLastReviveType(revive_type)
	self.revive_type = revive_type
end

function ReviveWGData:GetLastReviveType()
	return self.revive_type
end

function ReviveWGData:SetBeHitData(deliveler)
	self.be_hit_deliverler = deliveler
end

function ReviveWGData:GetBeHitData()
	return self.be_hit_deliverler
end