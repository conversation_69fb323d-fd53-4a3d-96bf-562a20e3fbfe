-- K-跨服属性宝石积分榜.xls
local item_table={
[1]={item_id=37687,num=1,is_bind=1},
[2]={item_id=26464,num=1,is_bind=1},
[3]={item_id=26463,num=1,is_bind=1},
[4]={item_id=57612,num=1,is_bind=1},
[5]={item_id=57632,num=1,is_bind=1},
[6]={item_id=26944,num=2,is_bind=1},
[7]={item_id=26462,num=1,is_bind=1},
[8]={item_id=57610,num=1,is_bind=1},
[9]={item_id=57630,num=1,is_bind=1},
[10]={item_id=26944,num=1,is_bind=1},
[11]={item_id=26461,num=1,is_bind=1},
[12]={item_id=57609,num=1,is_bind=1},
[13]={item_id=57629,num=1,is_bind=1},
[14]={item_id=48120,num=1,is_bind=1},
[15]={item_id=26459,num=1,is_bind=1},
[16]={item_id=57608,num=1,is_bind=1},
[17]={item_id=57628,num=1,is_bind=1},
[18]={item_id=26455,num=1,is_bind=1},
[19]={item_id=57607,num=1,is_bind=1},
[20]={item_id=57627,num=1,is_bind=1},
[21]={item_id=48117,num=1,is_bind=1},
[22]={item_id=37147,num=1,is_bind=1},
[23]={item_id=28482,num=1,is_bind=1},
[24]={item_id=28502,num=1,is_bind=1},
[25]={item_id=48442,num=2,is_bind=1},
[26]={item_id=37974,num=1,is_bind=1},
[27]={item_id=37870,num=1,is_bind=1},
[28]={item_id=28481,num=1,is_bind=1},
[29]={item_id=28501,num=1,is_bind=1},
[30]={item_id=48442,num=1,is_bind=1},
[31]={item_id=28480,num=1,is_bind=1},
[32]={item_id=28500,num=1,is_bind=1},
[33]={item_id=48120,num=2,is_bind=1},
[34]={item_id=28479,num=1,is_bind=1},
[35]={item_id=28499,num=1,is_bind=1},
[36]={item_id=28478,num=1,is_bind=1},
[37]={item_id=28498,num=1,is_bind=1},
[38]={item_id=28477,num=1,is_bind=1},
[39]={item_id=28497,num=1,is_bind=1},
[40]={item_id=38072,num=1,is_bind=1},
[41]={item_id=38175,num=1,is_bind=1},
[42]={item_id=37498,num=1,is_bind=1},
[43]={item_id=37691,num=1,is_bind=1},
[44]={item_id=37148,num=1,is_bind=1},
[45]={item_id=37979,num=1,is_bind=1},
[46]={item_id=37873,num=1,is_bind=1},
[47]={item_id=38076,num=1,is_bind=1},
[48]={item_id=38179,num=1,is_bind=1},
[49]={item_id=37499,num=1,is_bind=1},
[50]={item_id=37692,num=1,is_bind=1},
[51]={item_id=37149,num=1,is_bind=1},
[52]={item_id=37980,num=1,is_bind=1},
[53]={item_id=37874,num=1,is_bind=1},
[54]={item_id=38077,num=1,is_bind=1},
[55]={item_id=38180,num=1,is_bind=1},
[56]={item_id=37500,num=1,is_bind=1},
[57]={item_id=48557,num=1,is_bind=1},
[58]={item_id=48552,num=1,is_bind=1},
[59]={item_id=48553,num=1,is_bind=1},
[60]={item_id=48556,num=1,is_bind=1},
[61]={item_id=48558,num=1,is_bind=1},
[62]={item_id=48554,num=1,is_bind=1},
[63]={item_id=48555,num=1,is_bind=1},
[64]={item_id=57814,num=1,is_bind=1},
[65]={item_id=57812,num=1,is_bind=1},
[66]={item_id=57601,num=1,is_bind=1},
[67]={item_id=57621,num=1,is_bind=1},
[68]={item_id=22587,num=1,is_bind=1},
[69]={item_id=57813,num=1,is_bind=1},
[70]={item_id=57815,num=1,is_bind=1},
[71]={item_id=22622,num=1,is_bind=1},
[72]={item_id=57602,num=1,is_bind=1},
[73]={item_id=57622,num=1,is_bind=1},
[74]={item_id=22010,num=1,is_bind=1},
[75]={item_id=57816,num=1,is_bind=1},
[76]={item_id=28446,num=1,is_bind=1},
[77]={item_id=28455,num=1,is_bind=1},
[78]={item_id=28470,num=1,is_bind=1},
[79]={item_id=28490,num=1,is_bind=1},
[80]={item_id=22009,num=1,is_bind=1},
[81]={item_id=28447,num=2,is_bind=1},
[82]={item_id=28456,num=1,is_bind=1},
[83]={item_id=28448,num=1,is_bind=1},
[84]={item_id=28471,num=1,is_bind=1},
[85]={item_id=28491,num=1,is_bind=1},
[86]={item_id=28449,num=1,is_bind=1},
[87]={item_id=28450,num=2,is_bind=1},
[88]={item_id=28458,num=1,is_bind=1},
[89]={item_id=28451,num=1,is_bind=1},
[90]={item_id=28459,num=1,is_bind=1},
[91]={item_id=28452,num=1,is_bind=1},
[92]={item_id=28472,num=1,is_bind=1},
[93]={item_id=28492,num=1,is_bind=1},
[94]={item_id=28453,num=2,is_bind=1},
[95]={item_id=28454,num=1,is_bind=1},
[96]={item_id=28457,num=1,is_bind=1},
[97]={item_id=57801,num=1,is_bind=1},
[98]={item_id=57800,num=1,is_bind=1},
[99]={item_id=57600,num=1,is_bind=1},
[100]={item_id=57620,num=1,is_bind=1},
[101]={item_id=57802,num=1,is_bind=1},
[102]={item_id=57803,num=1,is_bind=1},
[103]={item_id=57804,num=1,is_bind=1},
[104]={item_id=57807,num=1,is_bind=1},
[105]={item_id=57806,num=1,is_bind=1},
[106]={item_id=57808,num=1,is_bind=1},
[107]={item_id=57809,num=1,is_bind=1},
[108]={item_id=57810,num=1,is_bind=1},
[109]={item_id=57819,num=1,is_bind=1},
[110]={item_id=57818,num=1,is_bind=1},
[111]={item_id=57820,num=1,is_bind=1},
[112]={item_id=57821,num=1,is_bind=1},
[113]={item_id=57822,num=1,is_bind=1},
[114]={item_id=57825,num=1,is_bind=1},
[115]={item_id=57824,num=1,is_bind=1},
[116]={item_id=57826,num=1,is_bind=1},
[117]={item_id=57827,num=1,is_bind=1},
[118]={item_id=57828,num=1,is_bind=1},
[119]={item_id=57831,num=1,is_bind=1},
[120]={item_id=57830,num=1,is_bind=1},
[121]={item_id=57832,num=1,is_bind=1},
[122]={item_id=57833,num=1,is_bind=1},
[123]={item_id=57834,num=1,is_bind=1},
[124]={item_id=57611,num=1,is_bind=1},
[125]={item_id=57631,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
reward={
{reach_value=9350,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{min_rank=2,max_rank=2,reach_value=4950,},
{min_rank=3,max_rank=3,reach_value=2750,reward_item={[0]=item_table[3],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10]},},
{min_rank=4,max_rank=5,reach_value=1430,reward_item={[0]=item_table[3],[1]=item_table[11],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{min_rank=6,max_rank=10,reach_value=990,reward_item={[0]=item_table[7],[1]=item_table[15],[2]=item_table[16],[3]=item_table[17],[4]=item_table[14]},},
{min_rank=11,max_rank=20,reach_value=770,reward_item={[0]=item_table[11],[1]=item_table[18],[2]=item_table[19],[3]=item_table[20],[4]=item_table[21]},},
{week_day=1,reward_item={[0]=item_table[22],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=2,reward_item={[0]=item_table[26],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{week_day=2,},
{week_day=2,},
{week_day=2,reach_value=1300,},
{week_day=2,reach_value=900,},
{week_day=2,reach_value=700,},
{week_day=3,reward_item={[0]=item_table[27],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{week_day=3,reward_item={[0]=item_table[2],[1]=item_table[7],[2]=item_table[28],[3]=item_table[29],[4]=item_table[30]},},
{week_day=3,reward_item={[0]=item_table[3],[1]=item_table[7],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},},
{week_day=3,reach_value=1300,reward_item={[0]=item_table[3],[1]=item_table[11],[2]=item_table[34],[3]=item_table[35],[4]=item_table[14]},},
{week_day=3,reach_value=900,reward_item={[0]=item_table[7],[1]=item_table[15],[2]=item_table[36],[3]=item_table[37],[4]=item_table[14]},},
{week_day=3,reach_value=700,reward_item={[0]=item_table[11],[1]=item_table[18],[2]=item_table[38],[3]=item_table[39],[4]=item_table[21]},},
{week_day=4,reward_item={[0]=item_table[40],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{week_day=4,},
{week_day=4,reach_value=2500,},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=5,reward_item={[0]=item_table[41],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=6,reward_item={[0]=item_table[42],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{week_day=6,reach_value=4500,},
{week_day=6,},
{week_day=6,},
{week_day=6,},
{week_day=6,},
{grade=1,reward_item={[0]=item_table[43],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,reward_item={[0]=item_table[44],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{grade=1,reward_item={[0]=item_table[45],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{grade=1,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{grade=1,reward_item={[0]=item_table[46],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{week_day=3,},
{week_day=3,},
{week_day=3,},
{grade=1,},
{grade=1,},
{grade=1,reward_item={[0]=item_table[47],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{week_day=4,},
{grade=1,},
{grade=1,},
{grade=1,},
{week_day=4,},
{grade=1,reward_item={[0]=item_table[48],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{grade=1,},
{grade=1,},
{grade=1,},
{week_day=5,},
{week_day=5,},
{grade=1,reward_item={[0]=item_table[49],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{week_day=6,},
{week_day=6,},
{week_day=6,},
{grade=1,},
{grade=1,},
{grade=2,reward_item={[0]=item_table[50],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,reward_item={[0]=item_table[51],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{grade=2,reward_item={[0]=item_table[52],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{grade=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{grade=2,reward_item={[0]=item_table[53],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{week_day=3,},
{grade=2,},
{week_day=3,},
{grade=2,},
{grade=2,},
{grade=2,reward_item={[0]=item_table[54],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,reward_item={[0]=item_table[55],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,reward_item={[0]=item_table[56],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,reward_item={[0]=item_table[57],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,reward_item={[0]=item_table[58],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{grade=3,},
{grade=3,},
{grade=3,},
{week_day=1,},
{grade=3,},
{grade=3,reward_item={[0]=item_table[59],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{grade=3,reward_item={[0]=item_table[60],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{week_day=3,},
{week_day=3,},
{week_day=3,},
{week_day=3,},
{week_day=3,},
{grade=3,reward_item={[0]=item_table[61],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{grade=3,},
{week_day=4,},
{week_day=4,},
{grade=3,},
{grade=3,},
{grade=3,reward_item={[0]=item_table[62],[1]=item_table[2],[2]=item_table[3],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{grade=3,},
{week_day=5,},
{grade=3,reward_item={[0]=item_table[63],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{week_day=6,},
{grade=3,},
{grade=3,},
{week_day=6,},
{grade=3,}
},

reward_meta_table_map={
[163]=37,	-- depth:1
[103]=19,	-- depth:1
[73]=31,	-- depth:1
[151]=25,	-- depth:1
[67]=25,	-- depth:1
[127]=1,	-- depth:1
[43]=1,	-- depth:1
[109]=25,	-- depth:1
[157]=31,	-- depth:1
[61]=19,	-- depth:1
[49]=7,	-- depth:1
[121]=37,	-- depth:1
[115]=31,	-- depth:1
[133]=7,	-- depth:1
[79]=37,	-- depth:1
[145]=19,	-- depth:1
[55]=13,	-- depth:1
[97]=13,	-- depth:1
[139]=13,	-- depth:1
[85]=1,	-- depth:1
[91]=7,	-- depth:1
[44]=2,	-- depth:1
[38]=2,	-- depth:1
[14]=38,	-- depth:2
[86]=44,	-- depth:2
[26]=14,	-- depth:3
[128]=86,	-- depth:3
[110]=26,	-- depth:4
[56]=14,	-- depth:3
[68]=56,	-- depth:4
[89]=5,	-- depth:1
[80]=68,	-- depth:5
[88]=4,	-- depth:1
[152]=68,	-- depth:5
[129]=3,	-- depth:1
[130]=88,	-- depth:2
[87]=129,	-- depth:2
[131]=89,	-- depth:2
[132]=6,	-- depth:1
[140]=152,	-- depth:6
[90]=132,	-- depth:2
[98]=140,	-- depth:7
[47]=131,	-- depth:3
[164]=140,	-- depth:7
[27]=3,	-- depth:1
[24]=6,	-- depth:1
[23]=5,	-- depth:1
[22]=4,	-- depth:1
[21]=27,	-- depth:2
[20]=38,	-- depth:2
[18]=6,	-- depth:1
[17]=5,	-- depth:1
[16]=4,	-- depth:1
[15]=27,	-- depth:2
[12]=24,	-- depth:2
[11]=23,	-- depth:2
[10]=22,	-- depth:2
[9]=21,	-- depth:3
[8]=20,	-- depth:3
[28]=16,	-- depth:2
[122]=164,	-- depth:8
[29]=17,	-- depth:2
[32]=8,	-- depth:4
[48]=90,	-- depth:3
[46]=130,	-- depth:3
[45]=87,	-- depth:3
[30]=18,	-- depth:2
[41]=29,	-- depth:3
[40]=28,	-- depth:3
[42]=30,	-- depth:3
[39]=15,	-- depth:3
[36]=12,	-- depth:3
[35]=11,	-- depth:3
[34]=10,	-- depth:3
[33]=9,	-- depth:4
[126]=42,	-- depth:4
[135]=9,	-- depth:4
[125]=41,	-- depth:4
[124]=40,	-- depth:4
[123]=39,	-- depth:4
[134]=8,	-- depth:4
[136]=10,	-- depth:3
[159]=135,	-- depth:5
[138]=12,	-- depth:3
[166]=124,	-- depth:5
[165]=123,	-- depth:5
[162]=138,	-- depth:4
[161]=35,	-- depth:4
[160]=136,	-- depth:4
[120]=162,	-- depth:5
[158]=134,	-- depth:5
[156]=30,	-- depth:3
[155]=29,	-- depth:3
[137]=161,	-- depth:5
[154]=166,	-- depth:6
[150]=162,	-- depth:5
[149]=137,	-- depth:6
[148]=160,	-- depth:5
[147]=159,	-- depth:6
[146]=158,	-- depth:6
[144]=156,	-- depth:4
[143]=155,	-- depth:4
[142]=154,	-- depth:7
[141]=165,	-- depth:6
[153]=141,	-- depth:7
[119]=161,	-- depth:5
[84]=126,	-- depth:5
[117]=159,	-- depth:6
[76]=160,	-- depth:5
[75]=117,	-- depth:7
[74]=158,	-- depth:6
[72]=84,	-- depth:6
[71]=155,	-- depth:4
[70]=154,	-- depth:7
[69]=153,	-- depth:8
[66]=150,	-- depth:6
[65]=149,	-- depth:7
[64]=76,	-- depth:6
[63]=75,	-- depth:8
[62]=74,	-- depth:7
[60]=72,	-- depth:7
[59]=71,	-- depth:5
[58]=70,	-- depth:8
[57]=69,	-- depth:9
[54]=66,	-- depth:7
[53]=65,	-- depth:8
[52]=64,	-- depth:7
[51]=63,	-- depth:9
[50]=62,	-- depth:8
[77]=53,	-- depth:9
[118]=76,	-- depth:6
[78]=54,	-- depth:8
[82]=58,	-- depth:9
[116]=74,	-- depth:7
[114]=72,	-- depth:7
[113]=71,	-- depth:5
[112]=70,	-- depth:8
[111]=69,	-- depth:9
[108]=66,	-- depth:7
[107]=65,	-- depth:8
[106]=118,	-- depth:7
[105]=63,	-- depth:9
[104]=116,	-- depth:8
[102]=114,	-- depth:8
[101]=113,	-- depth:6
[100]=112,	-- depth:9
[99]=111,	-- depth:10
[96]=108,	-- depth:8
[95]=107,	-- depth:9
[94]=106,	-- depth:8
[93]=105,	-- depth:10
[92]=104,	-- depth:9
[167]=143,	-- depth:5
[83]=167,	-- depth:6
[81]=57,	-- depth:10
[168]=84,	-- depth:6
},
score={
{button_name1="贯日长虹",open_panel1="SunRainbowView",act_type1=2330,},
{week_day=1,item_score="28446,1|28447,1|28448,40|28449,1|28450,1|28451,40|28452,1|28453,1|28454,40|28455,1|28456,1|28457,40|28458,1|28459,1|28460,40",button_name1="幸运仙石",open_panel1="GloryCrystalView",act_type1=2291,jump_parma="DragonTempleView#dragon_temp_hatch",model_name="桃红凤翎鲲",model_show_itemid=37147,},
{week_day=2,item_score="57800,1|57801,3|57802,9|57803,18|57804,65|57805,132",jump_parma="NewAppearanceWGView#new_appearance_upgrade_wing",model_name="桃之幻梦翼",model_show_itemid=37974,},
{week_day=3,item_score="57806,1|57807,3|57808,9|57809,18|57810,65|57811,132",jump_parma="NewAppearanceWGView#new_appearance_upgrade_fabao",model_name="桃舞风华衣",model_show_itemid=37870,},
{week_day=4,item_score="57818,1|57819,3|57820,9|57821,18|57822,65|57823,132",jump_parma="NewAppearanceWGView#new_appearance_upgrade_jianzhen",model_name="桃之恋仙",model_show_itemid=38072,},
{week_day=5,item_score="57824,1|57825,3|57826,9|57827,18|57828,65|57829,132",jump_parma="NewAppearanceWGView#new_appearance_upgrade_lingchong",model_name="桃叶飞舞",model_show_itemid=38175,},
{week_day=6,item_score="57830,1|57831,3|57832,9|57833,18|57834,65|57835,132",button_name1="寻仙福池",open_panel1="XuYuanFreshPoolView",act_type1=2292,jump_parma="NewAppearanceWGView#new_appearance_upgrade_mount",model_name="桃之恋曲",model_show_itemid=37498,}
},

score_meta_table_map={
},
person_score_reward={
{},
{seq=1,score=500,},
{seq=2,score=800,reward_item={[0]=item_table[64],[1]=item_table[65],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68]},},
{seq=3,score=1100,reward_item={[0]=item_table[64],[1]=item_table[69],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68]},},
{seq=4,score=1400,},
{seq=5,score=1700,reward_item={[0]=item_table[70],[1]=item_table[65],[2]=item_table[66],[3]=item_table[67],[4]=item_table[71]},},
{seq=6,score=2000,reward_item={[0]=item_table[70],[1]=item_table[69],[2]=item_table[72],[3]=item_table[73],[4]=item_table[71]},},
{seq=7,score=2300,reward_item={[0]=item_table[70],[1]=item_table[64],[2]=item_table[72],[3]=item_table[73],[4]=item_table[74]},},
{seq=8,score=2600,reward_item={[0]=item_table[75],[1]=item_table[70],[2]=item_table[72],[3]=item_table[73],[4]=item_table[74]},},
{week_day=1,reward_item={[0]=item_table[76],[1]=item_table[77],[2]=item_table[78],[3]=item_table[79],[4]=item_table[80]},},
{week_day=1,reward_item={[0]=item_table[81],[1]=item_table[82],[2]=item_table[78],[3]=item_table[79],[4]=item_table[80]},},
{week_day=1,reward_item={[0]=item_table[83],[1]=item_table[77],[2]=item_table[84],[3]=item_table[85],[4]=item_table[68]},},
{week_day=1,reward_item={[0]=item_table[86],[1]=item_table[82],[2]=item_table[84],[3]=item_table[85],[4]=item_table[68]},},
{week_day=1,reward_item={[0]=item_table[87],[1]=item_table[88],[2]=item_table[84],[3]=item_table[85],[4]=item_table[68]},},
{week_day=1,reward_item={[0]=item_table[89],[1]=item_table[90],[2]=item_table[84],[3]=item_table[85],[4]=item_table[71]},},
{week_day=1,reward_item={[0]=item_table[91],[1]=item_table[88],[2]=item_table[92],[3]=item_table[93],[4]=item_table[71]},},
{week_day=1,reward_item={[0]=item_table[94],[1]=item_table[90],[2]=item_table[92],[3]=item_table[93],[4]=item_table[74]},},
{week_day=1,reward_item={[0]=item_table[95],[1]=item_table[96],[2]=item_table[92],[3]=item_table[93],[4]=item_table[74]},},
{week_day=2,reward_item={[0]=item_table[97],[1]=item_table[98],[2]=item_table[99],[3]=item_table[100],[4]=item_table[80]},},
{week_day=2,reward_item={[0]=item_table[97],[1]=item_table[98],[2]=item_table[99],[3]=item_table[100],[4]=item_table[80]},},
{week_day=2,reward_item={[0]=item_table[101],[1]=item_table[98],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68]},},
{week_day=2,reward_item={[0]=item_table[101],[1]=item_table[97],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68]},},
{seq=4,score=1400,},
{week_day=2,reward_item={[0]=item_table[102],[1]=item_table[98],[2]=item_table[66],[3]=item_table[67],[4]=item_table[71]},},
{week_day=2,reward_item={[0]=item_table[102],[1]=item_table[97],[2]=item_table[72],[3]=item_table[73],[4]=item_table[71]},},
{week_day=2,reward_item={[0]=item_table[102],[1]=item_table[101],[2]=item_table[72],[3]=item_table[73],[4]=item_table[74]},},
{week_day=2,reward_item={[0]=item_table[103],[1]=item_table[102],[2]=item_table[72],[3]=item_table[73],[4]=item_table[74]},},
{week_day=3,reward_item={[0]=item_table[104],[1]=item_table[105],[2]=item_table[78],[3]=item_table[79],[4]=item_table[80]},},
{week_day=3,reward_item={[0]=item_table[104],[1]=item_table[105],[2]=item_table[78],[3]=item_table[79],[4]=item_table[80]},},
{week_day=3,reward_item={[0]=item_table[106],[1]=item_table[105],[2]=item_table[84],[3]=item_table[85],[4]=item_table[68]},},
{week_day=3,reward_item={[0]=item_table[106],[1]=item_table[104],[2]=item_table[84],[3]=item_table[85],[4]=item_table[68]},},
{week_day=3,reward_item={[0]=item_table[106],[1]=item_table[104],[2]=item_table[84],[3]=item_table[85],[4]=item_table[68]},},
{week_day=3,reward_item={[0]=item_table[107],[1]=item_table[105],[2]=item_table[84],[3]=item_table[85],[4]=item_table[71]},},
{week_day=3,reward_item={[0]=item_table[107],[1]=item_table[104],[2]=item_table[92],[3]=item_table[93],[4]=item_table[71]},},
{week_day=3,reward_item={[0]=item_table[107],[1]=item_table[106],[2]=item_table[92],[3]=item_table[93],[4]=item_table[74]},},
{week_day=3,reward_item={[0]=item_table[108],[1]=item_table[107],[2]=item_table[92],[3]=item_table[93],[4]=item_table[74]},},
{week_day=4,reward_item={[0]=item_table[109],[1]=item_table[110],[2]=item_table[99],[3]=item_table[100],[4]=item_table[80]},},
{week_day=4,reward_item={[0]=item_table[109],[1]=item_table[110],[2]=item_table[99],[3]=item_table[100],[4]=item_table[80]},},
{week_day=4,reward_item={[0]=item_table[111],[1]=item_table[110],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68]},},
{week_day=4,reward_item={[0]=item_table[111],[1]=item_table[109],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68]},},
{seq=4,score=1400,},
{week_day=4,reward_item={[0]=item_table[112],[1]=item_table[110],[2]=item_table[66],[3]=item_table[67],[4]=item_table[71]},},
{week_day=4,reward_item={[0]=item_table[112],[1]=item_table[109],[2]=item_table[72],[3]=item_table[73],[4]=item_table[71]},},
{week_day=4,reward_item={[0]=item_table[112],[1]=item_table[111],[2]=item_table[72],[3]=item_table[73],[4]=item_table[74]},},
{week_day=4,reward_item={[0]=item_table[113],[1]=item_table[112],[2]=item_table[72],[3]=item_table[73],[4]=item_table[74]},},
{week_day=5,reward_item={[0]=item_table[114],[1]=item_table[115],[2]=item_table[78],[3]=item_table[79],[4]=item_table[80]},},
{week_day=5,reward_item={[0]=item_table[114],[1]=item_table[115],[2]=item_table[78],[3]=item_table[79],[4]=item_table[80]},},
{week_day=5,reward_item={[0]=item_table[116],[1]=item_table[115],[2]=item_table[84],[3]=item_table[85],[4]=item_table[68]},},
{week_day=5,reward_item={[0]=item_table[116],[1]=item_table[114],[2]=item_table[84],[3]=item_table[85],[4]=item_table[68]},},
{seq=4,score=1400,},
{week_day=5,reward_item={[0]=item_table[117],[1]=item_table[115],[2]=item_table[84],[3]=item_table[85],[4]=item_table[71]},},
{week_day=5,reward_item={[0]=item_table[117],[1]=item_table[114],[2]=item_table[92],[3]=item_table[93],[4]=item_table[71]},},
{week_day=5,reward_item={[0]=item_table[117],[1]=item_table[116],[2]=item_table[92],[3]=item_table[93],[4]=item_table[74]},},
{week_day=5,reward_item={[0]=item_table[118],[1]=item_table[117],[2]=item_table[92],[3]=item_table[93],[4]=item_table[74]},},
{week_day=6,reward_item={[0]=item_table[119],[1]=item_table[120],[2]=item_table[99],[3]=item_table[100],[4]=item_table[80]},},
{week_day=6,reward_item={[0]=item_table[119],[1]=item_table[120],[2]=item_table[99],[3]=item_table[100],[4]=item_table[80]},},
{week_day=6,reward_item={[0]=item_table[121],[1]=item_table[120],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68]},},
{week_day=6,reward_item={[0]=item_table[121],[1]=item_table[119],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68]},},
{week_day=6,reward_item={[0]=item_table[121],[1]=item_table[119],[2]=item_table[66],[3]=item_table[67],[4]=item_table[68]},},
{week_day=6,reward_item={[0]=item_table[122],[1]=item_table[120],[2]=item_table[66],[3]=item_table[67],[4]=item_table[71]},},
{week_day=6,reward_item={[0]=item_table[122],[1]=item_table[119],[2]=item_table[72],[3]=item_table[73],[4]=item_table[71]},},
{week_day=6,reward_item={[0]=item_table[122],[1]=item_table[121],[2]=item_table[72],[3]=item_table[73],[4]=item_table[74]},},
{week_day=6,reward_item={[0]=item_table[123],[1]=item_table[122],[2]=item_table[72],[3]=item_table[73],[4]=item_table[74]},}
},

person_score_reward_meta_table_map={
[5]=4,	-- depth:1
[59]=5,	-- depth:2
[39]=3,	-- depth:1
[40]=4,	-- depth:1
[41]=40,	-- depth:2
[42]=6,	-- depth:1
[43]=7,	-- depth:1
[44]=8,	-- depth:1
[45]=9,	-- depth:1
[47]=2,	-- depth:1
[48]=3,	-- depth:1
[49]=4,	-- depth:1
[50]=49,	-- depth:2
[52]=7,	-- depth:1
[53]=8,	-- depth:1
[38]=47,	-- depth:2
[54]=9,	-- depth:1
[61]=7,	-- depth:1
[56]=47,	-- depth:2
[57]=3,	-- depth:1
[58]=4,	-- depth:1
[60]=6,	-- depth:1
[51]=6,	-- depth:1
[32]=5,	-- depth:2
[35]=8,	-- depth:1
[11]=47,	-- depth:2
[12]=3,	-- depth:1
[13]=4,	-- depth:1
[14]=5,	-- depth:2
[15]=6,	-- depth:1
[16]=7,	-- depth:1
[17]=8,	-- depth:1
[18]=9,	-- depth:1
[20]=47,	-- depth:2
[21]=3,	-- depth:1
[36]=9,	-- depth:1
[22]=4,	-- depth:1
[24]=6,	-- depth:1
[25]=7,	-- depth:1
[26]=8,	-- depth:1
[27]=9,	-- depth:1
[29]=47,	-- depth:2
[30]=3,	-- depth:1
[31]=4,	-- depth:1
[62]=8,	-- depth:1
[33]=6,	-- depth:1
[34]=7,	-- depth:1
[23]=22,	-- depth:2
[63]=9,	-- depth:1
},
grade={
{},
{grade=1,min_server_day=20,max_server_day=27,},
{grade=2,min_server_day=28,max_server_day=33,},
{grade=3,min_server_day=34,max_server_day=999,}
},

grade_meta_table_map={
},
other_default_table={minimum_capability=1,minimum_open_day=999,},

reward_default_table={grade=0,week_day=0,min_rank=1,max_rank=1,reach_value=8500,reward_item={[0]=item_table[2],[1]=item_table[7],[2]=item_table[124],[3]=item_table[125],[4]=item_table[10]},},

score_default_table={week_day=0,item_score="57812,1|57813,3|57814,9|57815,18|57816,65|57817,132",button_name1="藏宝时光",button_name2="",button_name3="",open_panel1="DIYDrawOneView",open_panel2="",open_panel3="",act_type1=2284,act_type2="",act_type3="",jump_parma="NewAppearanceWGView#new_appearance_upgrade_shenbing",model_show_type=1,model_bundle_name="",model_asset_name="",model_name="桃瑞春风武",model_show_itemid=37687,display_pos="-185|0",display_scale=1,display_rotation="0|0|0",},

person_score_reward_default_table={week_day=0,seq=0,score=200,reward_item={[0]=item_table[69],[1]=item_table[65],[2]=item_table[99],[3]=item_table[100],[4]=item_table[80]},},

grade_default_table={grade=0,min_server_day=1,max_server_day=19,}

}

