-- 更新公告

function WelfareView:InitUpdateAfficheView()
	--self:ShowAfficheContent()
	XUI.AddClickEventListener(self.node_list.btn_update_receive, BindTool.Bind1(self.OnClickUpdateReceive, self))
end

function WelfareView:DeleteUpdateAffiche()
	if self.reward_affiche_list then
		for k,v in pairs(self.reward_affiche_list) do
			v:DeleteMe()
		end
	end
	self.reward_affiche_list = nil
end

function WelfareView:FlushUpdateAffiche()
	-- local can_get = WelfareWGData.Instance:CanFetchReward()
	-- XUI.SetButtonEnabled(self.node_list.btn_update_receive, can_get)
	-- self.node_list["red_image"]:SetActive(can_get)
	-- local btn_txt = can_get and Language.Common.LingQu or Language.Common.YiLingQu
	-- self.node_list["lbl_btn_text"].tmp.text = btn_txt

	self:ShowAfficheContent()
	XUI.SetButtonEnabled(self.node_list.btn_update_receive, WelfareWGData.Instance:CanFetchReward())
	if WelfareWGData.Instance:CanFetchReward() then
		self.node_list["lbl_btn_text"].tmp.text = Language.Common.LingQu
		self.node_list["red_image"]:SetActive(true)
		self.node_list.img_receive:SetActive(false)
		self.node_list.btn_update_receive:SetActive(true)
	else
		self.node_list["red_image"]:SetActive(false)
		self.node_list.img_receive:SetActive(true)
		self.node_list.btn_update_receive:SetActive(false)
		self.node_list["lbl_btn_text"].tmp.text = Language.Common.YiLingQu
	end

end


-- 显示公告内容
function WelfareView:ShowAfficheContent()
	--local cfg = WelfareWGData.Instance:GetUpdateAfficheCfgByLevel()
	--self.node_list["lbl_gonggao"].emoji_tmp.text = cfg and cfg.explain or ''

	-- if not self.reward_affiche_list then
	-- 	self.reward_affiche_list = {}
		
	-- 	for i=0,#cfg.reward_item do
	-- 		local cell = ItemCell.New(self.node_list["ph_update_item"])
	-- 		cell:SetData(cfg.reward_item[i])
	-- 		self.reward_affiche_list[i] = cell
	-- 	end
	-- end


	local cfg = WelfareWGData.Instance:GetNotifyInfo()
	local affiche_cfg = WelfareWGData.Instance:GetUpdateAfficheCfgByLevel()
	local content = ''
	local reward_list = {}

	if cfg and not IsEmptyTable(cfg) and cfg.content ~= "" and not IsEmptyTable(cfg.reward_item) then
		content = cfg.content
		reward_list = cfg.reward_item
	elseif affiche_cfg then
		content = affiche_cfg.explain
		reward_list = affiche_cfg.reward_item
	end

	self.node_list["lbl_gonggao"].tmp.text = content

	if not self.reward_affiche_list then
		self.reward_affiche_list = {}
	end

	--reward_list k从零开始 导致获取奖励数量不对
	local target_reward_list = {}
	local index = 1
	for k, v in pairs(reward_list) do
		target_reward_list[index] = v
		index = index + 1
	end

	for i=1, #target_reward_list do
		local cell = self.reward_affiche_list[i]
		if not cell then
			cell = ItemCell.New(self.node_list["ph_update_item"])
			self.reward_affiche_list[i] = cell
		else
			cell:SetVisible(true)
		end
		local data = target_reward_list[i]
		cell:SetData({item_id = tonumber(data.item_id), num = tonumber(data.num), is_bind = tonumber(data.is_bind)})
	end

	for i=#target_reward_list + 1, #self.reward_affiche_list do
		self.reward_affiche_list[i]:SetVisible(false)
	end

end

-- 点击领取
function WelfareView:OnClickUpdateReceive()
	local empty_num = ItemWGData.Instance:GetEmptyNum()
	if empty_num <= 5 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotBagRoom)
		return
	end

	-- WelfareWGCtrl.Instance:SendUpdateNoticeFetchReward()
	WelfareWGCtrl.Instance:GetNotifyReward()
	AudioService.Instance:PlayRewardAudio()
end
