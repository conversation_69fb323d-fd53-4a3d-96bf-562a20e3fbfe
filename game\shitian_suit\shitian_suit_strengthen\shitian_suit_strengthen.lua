MAX_STTZ_ATTR_NUM = 3
function ShiTianSuitStrengthenView:StrengthenLoadCallBack()
    if not self.strengthen_stuff_item then
        self.strengthen_stuff_item = ShiTianStuffItem.New(self.node_list.strengthen_stuff_pos)
    end

    if not self.strengthen_attr_list then
        self.strengthen_attr_list = {}
        for i = 1, MAX_STTZ_ATTR_NUM do
            self.strengthen_attr_list[i] = CommonAddAttrRender.New(self.node_list["strengthen_attr_item" .. i])
        end
    end

    self.auto_strengthen = false
    XUI.AddClickEventListener(self.node_list.strengthen_level_btn, BindTool.Bind(self.OnClickResonanceBtn, self))
    XUI.AddClickEventListener(self.node_list.strengthen_btn, BindTool.Bind(self.OnClickStrengthenBtn, self, true))
    XUI.AddClickEventListener(self.node_list.auto_strengthen_btn, BindTool.Bind(self.OnClickAutoStrengthenBtn, self))
    XUI.AddClickEventListener(self.node_list.strengthen_equip, BindTool.Bind(self.ShowShiTianSuitEpTips, self))
end

function ShiTianSuitStrengthenView:StrengthenReleaseCallBack()
    if self.strengthen_stuff_item then
        self.strengthen_stuff_item:DeleteMe()
        self.strengthen_stuff_item = nil
    end

    if self.strengthen_attr_list then
        for k, v in pairs(self.strengthen_attr_list) do
            v:DeleteMe()
        end

        self.strengthen_attr_list = nil
    end

    if self.delay_effect_close then
		GlobalTimerQuest:CancelQuest(self.delay_effect_close)
		self.delay_effect_close = nil
	end
end

function ShiTianSuitStrengthenView:StrengthenOnFlush()
    if not self.cur_select_ep_data then
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part

    local title_bundle, title_asset = ResPath.GetF2RawImagesPNG("a2_lhtz_epname_" .. suit_seq .. "_" .. part)
    self.node_list.strengthen_title_img.raw_image:LoadSprite(title_bundle, title_asset, function()
        self.node_list.strengthen_title_img.raw_image:SetNativeSize()
    end)

    local ep_bundle, ep_asset = ResPath.GetF2RawImagesPNG("a2_lhtz_epimg_" .. suit_seq .. "_" .. part)
    self.node_list.strengthen_equip.raw_image:LoadSprite(ep_bundle, ep_asset, function()
        self.node_list.strengthen_equip.raw_image:SetNativeSize()
    end)

    self:FlushStrengthenPanel()
end

-- 激活共鸣刷新
function ShiTianSuitStrengthenView:FlushTotalStrengthenPanel()
    if not self.cur_select_ep_data then
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local total_level = ShiTianSuitStrengthenWGData.Instance:GetStrengthenTotalLevelBySeq(suit_seq)
	local total_level_is_max = ShiTianSuitStrengthenWGData.Instance:GetCurTotalCanActiveOrIsMax(suit_seq, SHOW_TYPE.Strengthen, true)
    local cur_total_cfg, next_total_cfg = ShiTianSuitStrengthenWGData.Instance:GetCurAndNextTotalLevelCfg(suit_seq, SHOW_TYPE.Strengthen)
    local next_get_level = next_total_cfg.total_level or cur_total_cfg.total_level or 0
    local slider_value = total_level / next_get_level

    self.node_list.strengthen_pro_text.text.text = string.format(Language.ShiTianSuit.StrengthenTotalLevel, total_level, total_level, next_get_level)
    self.node_list.strengthen_slider.slider.value = slider_value > 1 and 1 or slider_value
    self.node_list.strengthen_level_remind:SetActive(not total_level_is_max and total_level >= next_get_level)
end

-- 强化刷新
function ShiTianSuitStrengthenView:FlushStrengthenPanel(is_level_up)
    if not self.cur_select_ep_data then
        return
    end
    
    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part
    if is_level_up then
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_qianghua,
							is_success = true, pos = Vector2(0, 0), parent_node = self.node_list.strengthen_effect})
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
        self:PlayFireEffect()


        local jump_ep_index, is_remind_jump = ShiTianSuitStrengthenWGData.Instance:GetOneRemindEpIndex(suit_seq, part, SHOW_TYPE.Strengthen)
        if jump_ep_index ~= part and is_remind_jump then
            self.shitian_ep_list:JumpToIndex(jump_ep_index)
            return
        end
    end

    local cur_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetStrengthenLevelCfg(suit_seq, part)
    if IsEmptyTable(cur_level_cfg) then
        return
    end

    self:FlushTotalStrengthenPanel()
    self:FlushStrengthenStuff(true)

    local can_strength = ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitEpCanStrengthen(suit_seq, part)
    local cur_level = ShiTianSuitStrengthenWGData.Instance:GetEpStrengthenCurLevel(suit_seq, part)
	local next_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetStrengthenLevelCfg(suit_seq, part, cur_level + 1)
    local cur_ep_is_max = ShiTianSuitStrengthenWGData.Instance:GetEpStrengthenCurLevelIsMax(suit_seq, part)

    self.node_list.strengthen_equip_name.text.text = ItemWGData.Instance:GetItemName(self.cur_select_ep_data.icon_id)
    self.node_list.strengthen_equip_level.text.text = string.format(Language.ShiTianSuit.StrengthenSelectEpLevel, cur_level)
    self.node_list.strengthen_btn_group:SetActive(not cur_ep_is_max)
    self.node_list.strengthen_max_flag:SetActive(cur_ep_is_max)

    local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 2)
    for k, v in pairs(self.strengthen_attr_list) do
        v:SetData(attr_list[k])
    end

    if self.auto_strengthen and not cur_ep_is_max and can_strength then
        local timer_callback = function()
            if self.auto_strengthen then
                self:OnClickStrengthenBtn()
            end
        end

        self.auto_strengthen_timer = GlobalTimerQuest:AddTimesTimer(timer_callback, 0, 0.5)
    else
        self:CancelAutoStrengthen()
    end

    self:FlushAutoStrengthenBtnTxt()
end

function ShiTianSuitStrengthenView:FlushStrengthenStuff(need_flush_coin)
    if not self.cur_select_ep_data then
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part
    local cur_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetStrengthenLevelCfg(suit_seq, part)
    if IsEmptyTable(cur_level_cfg) then
        return
    end

    -- 材料部分
    local cur_level = ShiTianSuitStrengthenWGData.Instance:GetEpStrengthenCurLevel(suit_seq, part)
    local cur_ep_is_max = ShiTianSuitStrengthenWGData.Instance:GetEpStrengthenCurLevelIsMax(suit_seq, part)
    local can_strength = ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitEpCanStrengthen(suit_seq, part)
	local next_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetStrengthenLevelCfg(suit_seq, part, cur_level + 1)
    local next_cost_item_num = next_level_cfg.cost_item_num or 0
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)

    self.node_list.strengthen_btn_remind:SetActive(not cur_ep_is_max and can_strength and is_act)
    self.node_list.auto_strengthen_btn_remind:SetActive(not cur_ep_is_max and can_strength and is_act)
    self.strengthen_stuff_item:SetData({item_id = next_level_cfg.cost_item_id or cur_level_cfg.cost_item_id,
                                            is_special = false, need_num = next_cost_item_num, is_max = cur_ep_is_max})

    if not need_flush_coin then
        return
    end

    local coin = RoleWGData.Instance.role_info.coin or 0
    local coin_str = CommonDataManager.ConverMoneyByThousand(coin)
    local next_lingyu_num = next_level_cfg.need_num_tongqian or 0
    local color = coin >= next_lingyu_num and COLOR3B.GREEN or COLOR3B.PINK
    local need_coin_str = CommonDataManager.ConverMoneyByThousand(next_lingyu_num or 0)

    self.node_list.strengthen_cost_txt.text.text = string.format(Language.ShiTianSuit.StrengthenCost, color, coin_str, need_coin_str)
end

function ShiTianSuitStrengthenView:CleanStrengthenTimer()
	if self.auto_strengthen_timer then
        GlobalTimerQuest:CancelQuest(self.auto_strengthen_timer)
        self.auto_strengthen_timer = nil
    end
end

function ShiTianSuitStrengthenView:OnClickStrengthenBtn(is_click)
    if not self.cur_select_ep_data then
        self.auto_strengthen = false
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)
    if not is_act then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.NoActiveShiTianEp)
        self.auto_strengthen = false
        return
    end

    if is_click and self.auto_strengthen then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.AutoMode)
        return
    end

    local is_max = ShiTianSuitStrengthenWGData.Instance:GetEpStrengthenCurLevelIsMax(suit_seq, part)
    if is_max then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.MaxMsg[SHOW_TYPE.Strengthen])
        self.auto_strengthen = false
        return
    end

    local cur_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetStrengthenLevelCfg(suit_seq, part)
    if IsEmptyTable(cur_level_cfg) then
        self.auto_strengthen = false
        return
    end

    local cur_level = ShiTianSuitStrengthenWGData.Instance:GetEpStrengthenCurLevel(suit_seq, part)
    local next_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetStrengthenLevelCfg(suit_seq, part, cur_level + 1)
    local coin = RoleWGData.Instance.role_info.coin or 0
    local target_lingyu = next_level_cfg.need_num_tongqian or cur_level_cfg.need_num_tongqian
    local target_item_num = next_level_cfg.cost_item_num or cur_level_cfg.cost_item_num
    local can_strength = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id) >= target_item_num and coin >= target_lingyu
    if not can_strength then
        self.auto_strengthen = false
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.NotCanUp[SHOW_TYPE.Strengthen])
        return
    end

    ShiTianSuitStrengthenWGCtrl.Instance:SendCSShiTianStrengthenReq(SHITIANSTONE_OPERA_TYPE.EQUIP_LEVEL_UP, suit_seq, part)
end

function ShiTianSuitStrengthenView:OnClickAutoStrengthenBtn()
    self.auto_strengthen = not self.auto_strengthen
    if self.auto_strengthen then
        self:OnClickStrengthenBtn()
    end

    self:FlushAutoStrengthenBtnTxt()
end

function ShiTianSuitStrengthenView:FlushAutoStrengthenBtnTxt()
    local btn_str = self.auto_strengthen and Language.ShiTianSuit.CancelAuto or Language.ShiTianSuit.AutoStrengthen
    self.node_list.auto_strengthen_text.text.text = btn_str
end

function ShiTianSuitStrengthenView:CancelAutoStrengthen()
    if self.auto_strengthen then
        self.auto_strengthen = false
        self:CleanStrengthenTimer()
    end
end

function ShiTianSuitStrengthenView:PlayFireEffect()
    if self.delay_effect_close then
        return
    end

    self.node_list.strengthen_fire_effect:SetActive(true)
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Advanced))
    self.delay_effect_close = GlobalTimerQuest:AddDelayTimer(function()
        self.node_list.strengthen_fire_effect:SetActive(false)
        GlobalTimerQuest:CancelQuest(self.delay_effect_close)
        self.delay_effect_close = nil
    end, 1)
end

function ShiTianSuitStrengthenView:ShowShiTianSuitEpTips()
    if not self.cur_select_ep_data then
        return
    end

    TipWGCtrl.Instance:OpenItem({item_id = self.cur_select_ep_data.icon_id})
end

---------------------养成材料item------------------
ShiTianStuffItem = ShiTianStuffItem or BaseClass(BaseRender)

function ShiTianStuffItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)
    XUI.AddClickEventListener(self.node_list.special_part, BindTool.Bind(self.OnClickSpecialItem, self))
end

function ShiTianStuffItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function ShiTianStuffItem:OnFlush()
    if not self.data then
        return
    end

    local need_show = self.data.need_num > 0
    self.view:SetActive(need_show)
    if need_show then
        self.item_cell:SetData({item_id = self.data.item_id})
        self.node_list.special_part:SetActive(self.data.is_special)
        local num_str
        if self.data.is_max then
            num_str = Language.ShiTianSuit.MaxLevelStuffNum
        else
            local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
            local color = item_num >= self.data.need_num and COLOR3B.DEFAULT_NUM or COLOR3B.PINK
            num_str = string.format(Language.ShiTianSuit.StuffNum, color, item_num, self.data.need_num)
        end

        self.node_list.need_num.text.text = num_str
    else
        self:CompelCloseUseLuckItem()
    end
end

function ShiTianStuffItem:OnClickSpecialItem(change_value)
    if not self.data then
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
    local can_use = item_num >= self.data.need_num

    if self.data.is_max and change_value == nil then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.MaxMsg[SHOW_TYPE.InfuseSoul])
        return
    end

    if not can_use and change_value == nil then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.LuckItemNotUse)
        return
    end

    local no_use_item = self.node_list.hook:GetActive()
    if no_use_item == change_value then
        return
    end

    self.node_list.hook:SetActive(not no_use_item or change_value)
    if self.click_callback then
        self.click_callback(not no_use_item or change_value)
    end
end

function ShiTianStuffItem:CompelCloseUseLuckItem()
    self.node_list.hook:SetActive(false)
    if self.click_callback then
        self.click_callback(false)
    end
end