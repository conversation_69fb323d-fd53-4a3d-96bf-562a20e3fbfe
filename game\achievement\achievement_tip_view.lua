
--成就弹窗 和 掉落极品展示（DropEquipShowTips）互斥

AchievementTipiew = AchievementTipiew or BaseClass(SafeBaseView)

function AchievementTipiew:__init()
	-- self:SetMaskBg()
	self.view_name = "AchievementTipiew"
	self.view_layer = UiLayer.PopWhite
	self.open_tween = nil
	self.close_tween = nil
	self:AddViewResource(0, "uis/view/achievement_ui_prefab", "layout_achievement_tip")
end

function AchievementTipiew:__delete()

end

function AchievementTipiew:CleanAnimatorTime()
	if self.animator_timer then
		GlobalTimerQuest:CancelQuest(self.animator_timer)
		self.animator_timer = nil
	end
end

function AchievementTipiew:ReleaseCallBack()
	self.tip_data = nil

	self:CleanAnimatorTime()
end

function AchievementTipiew:CloseCallBack()
	if self.close_open_func then
		self.need_stop = false
		self.close_open_func()
		self.close_open_func = nil
	end

	self:CleanAnimatorTime()
end

function AchievementTipiew:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.button, BindTool.Bind(self.OnClickJumpBtn, self))
	self.node_list.button:SetActive(false)
end

function AchievementTipiew:OnClickJumpBtn()
	if IsEmptyTable(self.tip_data) then
		return
	end

	AppearanceWGCtrl.Instance:CloseGetNewView()--2020.7.2 策划需求:点击跳转成就界面,关闭恭喜获得界面
	AchievementWGCtrl.Instance:TipViewToOpenView(self.tip_data.type_sort, self.tip_data.client_sort)
end

local tween_info = {
	FromScale = Vector3(0, 1, 0),
	ToScale = Vector3(1, 1, 1),
	ScaleTweenTime = 0.5,
	AlphaTweenTime = 0.5,
	ScaleTweenType = DG.Tweening.Ease.Linear,
	AlphaTweenType = DG.Tweening.Ease.Linear,
}

function AchievementTipiew:OnFlush()
	if IsEmptyTable(self.tip_data) then
		self:Close()
		return
	end

	self.node_list.button:SetActive(true)
	UITween.CleanScaleAlaphaShow(self.view_name)
	UITween.DoScaleAlaphaShow(self.view_name, self.node_list.parent, tween_info)

	self.node_list.task_name.text.text = self.tip_data[1].sub_type_str
	self.node_list.des.text.text = self.tip_data[1].client_show
	self.node_list.value.text.text = self.tip_data[1].chengjiu

	self:CleanAnimatorTime()
	self.animator_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.ShowAnimator, self), tween_info.ScaleTweenTime + 2)--延时处理
end

function AchievementTipiew:ShowNextAchieventHint()
	if self.need_stop then
		AchievementWGCtrl.Instance:SaveNextNeedShowData(self.tip_data)
		self.tip_data = {}
	end

	self:Flush()
end

function AchievementTipiew:ShowAnimator()
	local parent_tween = self.node_list["parent"].canvas_group:DoAlpha(1, 0, 0.5)
	parent_tween:SetEase(DG.Tweening.Ease.OutCubic)
	parent_tween:OnComplete(function()
		if self.node_list.button then
        	self.node_list.button:SetActive(false)
		end

		table.remove(self.tip_data, 1)
		self:ShowNextAchieventHint()
    end)
end

function AchievementTipiew:SetDataList(data)
	if self.tip_data == nil then
		self.tip_data = {}
	end

	table.insert(self.tip_data, data)
end

function AchievementTipiew:StopNextShow(close_open_func)
	self.need_stop = true
	self.close_open_func = close_open_func
end
