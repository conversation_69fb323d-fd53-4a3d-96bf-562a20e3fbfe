ZCTipsSystemManager = ZCTipsSystemManager or BaseClass()

function ZCTipsSystemManager:__init()
	if ZCTipsSystemManager.Instance ~= nil then
		error("[ZCTipsSystemManager] attempt to create singleton twice!")
		return
	end
	ZCTipsSystemManager.Instance = self
	self.system_tips = ZCTipsSystemView.New()
	self.next_time = 0.0
	self.list = {}
	self.tips_list = {}
	self.index = 1
	Runner.Instance:AddRunObj(self, 3)
end

function ZCTipsSystemManager:__delete()
	self.list = {}
	for k,v in pairs(self.tips_list) do
		v:DeleteMe()
	end
	self.tips_list= {}
	self.next_time = nil
	if self.system_tips ~= nil then
		self.system_tips:DeleteMe()
		self.system_tips = nil
	end
	ZCTipsSystemManager.Instance = nil
	Runner.Instance:RemoveRunObj(self)
end

local tip_t = {}
local add_time = 0
local normal_time = 0.6
local time_int = normal_time
local normal_speed = 1.5
local tips_speed = normal_speed
local show_count = 0
local max_speed = 18
local min_time = 0.05
function ZCTipsSystemManager:ShowSystemTips(msg, speed)
	if #tip_t > 20 then --最多飘20条
		table.remove(tip_t, 1)
	end
	table.insert(tip_t, {msg, speed})
	show_count = 0
	for k,v in pairs(self.tips_list) do
		if v:SystemTipsVis() then
			show_count = show_count + 1
		end
	end
	show_count = show_count + #tip_t
	time_int = show_count > 3 and normal_time / (show_count - 3 + 1) or normal_time
	time_int = math.max(time_int, min_time)
end

function ZCTipsSystemManager:RealShowSystemTips(msg, speed)
	speed = speed or 1
	if type(speed) ~= "number" then
		speed = 1
	end

	local tips_cell = self.tips_list[self.index]
	if tips_cell then
		tips_cell:Close()
		tips_cell:Show(msg, speed, 0)
	else
		tips_cell = ZCTipsSystemView.New()
		self.tips_list[self.index] = tips_cell
		tips_cell:Show(msg, speed, 0)
	end
	for k,v in pairs(self.tips_list) do
		if k ~= self.index then
			v:AddIndex()
		end
	end

	self.index = self.index + 1
	if self.index > 3 then
		self.index = 1
	end
end

function ZCTipsSystemManager:ShowSystemTips2(convertion,msg, speed)
	speed = speed or 1
	if type(speed) ~= "number" then
		speed = 1
	end

	local tips_cell = self.tips_list[self.index]
	if tips_cell then
		tips_cell:Close()
		tips_cell:Show2(convertion,msg, speed)
	else
		tips_cell = ZCTipsSystemView.New()
		self.tips_list[self.index] = tips_cell
		tips_cell:Show2(convertion,msg, speed)
	end

	for i = 1, #self.tips_list do
		if i ~= self.index and self.tips_list[i] then
			local cur_speed = self.tips_list[i]:GetAnimSpeed()
			self.tips_list[i]:ChangeSpeed(cur_speed * 3)
		end
	end

	self.index = self.index + 1
	if self.index > 5 then
		self.index = 1
	end
end

function ZCTipsSystemManager:Update(now_time, elapse_time)
	if tip_t[1] and (now_time - add_time > time_int) then
		add_time = now_time
		show_count = 0
		for k,v in pairs(self.tips_list) do
			if v:SystemTipsVis() then
				show_count = show_count + 1
			end
		end
		show_count = show_count + #tip_t
		tips_speed = show_count > 3 and normal_speed * (show_count - 3 + 1) or normal_speed
		tips_speed = math.min(tips_speed, max_speed)
		time_int = show_count > 3 and normal_time / (show_count - 3 + 1) or normal_time
		time_int = math.max(time_int, min_time)
		local msg = table.remove(tip_t, 1)
		self:RealShowSystemTips(msg[1], tips_speed)
		for k,v in pairs(self.tips_list) do
			v:ChangeSpeed(tips_speed)
		end
	end
end

-- function ZCTipsSystemManager:Update()
-- 	if self.next_time > 0.0 then
-- 		self.next_time = self.next_time - 0.05
-- 	else
-- 		self.next_time = 0.0
-- 	end
-- 	if #self.list > 2 then
-- 		table.remove(self.list, 1)
-- 	end
-- 	if #self.list > 0 and self.next_time <= 0.0 and PlayerData.Instance:GetRoleVo().level >= 50 then
-- 		self.system_tips = ZCTipsSystemView.New()
-- 		self.system_tips:Show(self.list[1].msg, self.list[1].speed)
-- 		self.next_time = 4.0 / (self.list[1].speed or 1)
-- 		table.remove(self.list, 1)
-- 	end
-- end