require("game/activity_limit_buy/activity_limit_buy_wg_data")
require("game/activity_limit_buy/activity_limit_buy_view")

ActivityLimitBuyWGCtrl = ActivityLimitBuyWGCtrl or BaseClass(BaseWGCtrl)

function ActivityLimitBuyWGCtrl:__init()
	if ActivityLimitBuyWGCtrl.Instance then
		ErrorLog("[ActivityLimitBuyWGCtrl] attempt to create singleton twice!")
		return
	end

	ActivityLimitBuyWGCtrl.Instance = self
	self.data = ActivityLimitBuyWGData.New()
    self.view = ActivityLimitBuyView.New(GuideModuleName.ActivityLimitBuyView)
  
    self:RegisterAllProtocols()
    self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function ActivityLimitBuyWGCtrl:__delete()
	ActivityLimitBuyWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil
end

function ActivityLimitBuyWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOALimitRmbBuyInfo,"OnSCOALimitRmbBuyInfo")
end


function ActivityLimitBuyWGCtrl:OnSCOALimitRmbBuyInfo(protocol)
	--print_error("===========数据============", protocol)
	self.data:SetAllBuyInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ActivityLimitBuyView)
end

function ActivityLimitBuyWGCtrl:ReqActivityLimitBuyInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

--跨天，请求一下秘境信息
function ActivityLimitBuyWGCtrl:OnPassDay()
	GlobalTimerQuest:AddDelayTimer(function()
		self:ReqActivityLimitBuyInfo(OA_LIMIT_RMB_BUY_OPERATE_TYPE.INFO)
	end, 5)
end