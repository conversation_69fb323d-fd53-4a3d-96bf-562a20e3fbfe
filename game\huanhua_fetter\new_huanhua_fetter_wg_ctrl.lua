require("game/huanhua_fetter/new_huanhua_fetter_wg_data")
require("game/huanhua_fetter/new_huanhua_fetter_view")
require("game/huanhua_fetter/huanhua_fetter_suit_view")

NewHuanHuaFetterWGCtrl = NewHuanHuaFetterWGCtrl or BaseClass(BaseWGCtrl)

function NewHuanHuaFetterWGCtrl:__init()
	if nil ~= NewHuanHuaFetterWGCtrl.Instance then
		ErrorLog("[NewHuanHuaFetterWGCtrl] attempt to create singleton twice!")
		return
	end
	NewHuanHuaFetterWGCtrl.Instance = self

    self.view = NewHuanHuaFetterView.New(GuideModuleName.NewHuanHuaFetterView)
    self.data = NewHuanHuaFetterWGData.New()
	self.attr_active = HuanHuaFetterSuitView.New()

    self:RegisterProtocol(CSHuanhuaFetterOperate)
	self:RegisterProtocol(SCHuanhuaFetterItemInfo, "OnSCHuanhuaFetterItemInfo")
	self:RegisterProtocol(SCHuanhuaFetterItemUpdate, "OnSCHuanhuaFetterItemUpdate")
	self:RegisterProtocol(SCHuanhuaFetterRewardItemInfo, "OnSCHuanhuaFetterRewardItemInfo")
    self:RegisterProtocol(SCHuanhuaFetterRewardItemUpdate, "OnSCHuanhuaFetterRewardItemUpdate")
end

function NewHuanHuaFetterWGCtrl:__delete()
    NewHuanHuaFetterWGCtrl.Instance = nil
    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

	self.attr_active:DeleteMe()
    self.attr_active = nil
end

function NewHuanHuaFetterWGCtrl:SendHuanHuaFetterRequest(operate_type, param1, param2)
	--print_error("---请求----", operate_type, param1, param2)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSHuanhuaFetterOperate)
 	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol:EncodeAndSend()
end

function NewHuanHuaFetterWGCtrl:OnSCHuanhuaFetterItemInfo(protocol)
	--print_error("---全部套装信息----", protocol)
	self.data:SetAllSuitStateInfo(protocol)
	RemindManager.Instance:Fire(RemindName.NewHuanHuaFetterView)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if self.attr_active and self.attr_active:IsOpen() then
		self.attr_active:Flush()
	end
end

function NewHuanHuaFetterWGCtrl:OnSCHuanhuaFetterItemUpdate(protocol)
	--print_error("---单个信息更新----", protocol)
	self.data:SetSingleSuitStateInfo(protocol)
	RemindManager.Instance:Fire(RemindName.NewHuanHuaFetterView)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if self.attr_active and self.attr_active:IsOpen() then
		self.attr_active:Flush()
	end
end

function NewHuanHuaFetterWGCtrl:OnPartActiveResult(result, suit, part)
	--print_error("---激活返回----", result, suit, part)
	if result == 1 then
		-- if self.view:IsOpen() then
		-- 	self.view:DoActiveEffect()
		-- end
	end
end

function NewHuanHuaFetterWGCtrl:OnSCHuanhuaFetterRewardItemInfo(protocol)
    self.data:SetSCHuanhuaFetterRewardItemInfo(protocol)
	RemindManager.Instance:Fire(RemindName.NewHuanHuaFetterView)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if self.attr_active and self.attr_active:IsOpen() then
		self.attr_active:Flush()
	end
end

function NewHuanHuaFetterWGCtrl:OnSCHuanhuaFetterRewardItemUpdate(protocol)
    self.data:SCHuanhuaFetterRewardItemUpdate(protocol)
	RemindManager.Instance:Fire(RemindName.NewHuanHuaFetterView)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	if self.attr_active and self.attr_active:IsOpen() then
		self.attr_active:Flush()
	end
end

function NewHuanHuaFetterWGCtrl:OpenAttrActiveView(data)
    if self.attr_active then
		self.attr_active:SetDataInfo(data)
        if self.attr_active:IsOpen() then
            self.attr_active:Flush()
        else
            self.attr_active:Open()
        end
    end
end