function OperationActivityView:InitMoWuCallBack()
	
	self.view_info_cfg = MoWuJiangLinWGData.Instance:GetShouChongViewCfgByInterface()

	self.jl_sl_reward_list = AsyncListView.New(ItemCell, self.node_list["jl_sl_reward_list"])
	self.jl_yj_reward_list = AsyncListView.New(ItemCell, self.node_list["jl_yi_reward_list"])

	if nil == self.mw_jianglin_display_model then
		self.mw_jianglin_display_model = OperationActRender.New(self.node_list.jl_display)
		self.mw_jianglin_display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	XUI.AddClickEventListener(self.node_list.jl_btn_shilian, BindTool.Bind(self.OnBtnShi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,self))
	self:SetViewShowInfo()
end

function OperationActivityView:ReleaseMoWuCallBack()
	self.view_info_cfg = nil

	if self.jl_sl_reward_list then
		self.jl_sl_reward_list:DeleteMe()
		self.jl_sl_reward_list = nil
	end

	if self.jl_yj_reward_list then
		self.jl_yj_reward_list:DeleteMe()
		self.jl_yj_reward_list = nil
	end

	if self.mw_jianglin_display_model then
		self.mw_jianglin_display_model:DeleteMe()
		self.mw_jianglin_display_model = nil
	end

	if self.mowan_tween then
		self.mowan_tween:Kill()
		self.mowan_tween = nil
	end


	if CountDownManager.Instance:HasCountDown("MW_common_count_douwn") then
        CountDownManager.Instance:RemoveCountDown("MW_common_count_douwn")
    end
end

function OperationActivityView:ShowIndexCallBackJiangLinView()
	MoWuJiangLinWGCtrl.Instance:RankActTSJLReq(RA_MOWU_JIANGLIN_OPERATE_TYPE.RA_MOWU_JIANGLIN_OPERATE_TYPE_BOSS_DEAD_INFO)
end

function OperationActivityView:OnFlushMoWuView()
	self:InitJiangLinRewardList()
	self:FlushMWJiangLinModel()
	self:InitJiangLinFlushDesc()

	local is_open = MoWuJiangLinWGData.Instance:IsInMoWuJiangLinActivity()
	self.node_list.jl_btn_redpoint:SetActive(is_open)

	XUI.SetGraphicGrey(self.node_list.jl_btn_shilian, not is_open)
	--倒计时多少秒后降临
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN)
	if activity_data.status == ACTIVITY_STATUS.STANDY then
		self:MWSetActMainBtnTime(activity_data.next_time)
	else
		self.node_list["jl_btn_name"].text.text = self.view_info_cfg and self.view_info_cfg.btn_name_1 or ""
	end
	
end

function OperationActivityView:MWSetActMainBtnTime(next_time)
	local time = next_time - TimeWGCtrl.Instance:GetServerTime()
	if time > 0 then 
		if CountDownManager.Instance:HasCountDown("MW_common_count_douwn") then
	        CountDownManager.Instance:RemoveCountDown("MW_common_count_douwn")
	    end

	    CountDownManager.Instance:AddCountDown("MW_common_count_douwn",
	            BindTool.Bind(self.MWCommonUpdateTime, self),
	            BindTool.Bind(self.MWCommonCompleteTime, self), nil, time, 1)
	end

end

function OperationActivityView:MWCommonUpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
    local str = string.format(Language.OpertionAcitvity.MWOpenCount, TimeUtil.MSTime(temp_seconds))
    if self.node_list and self.node_list["jl_btn_name"] then
        self.node_list["jl_btn_name"].text.text = str
    end
end

function OperationActivityView:MWCommonCompleteTime(elapse_time, total_time)
 	if self.node_list and self.node_list["jl_btn_name"] then
        self.node_list["jl_btn_name"].text.text = self.view_info_cfg.btn_name_1
    end
	self:InitJiangLinFlushDesc()
end

function OperationActivityView:InitJiangLinRewardList()
	local reward_cfg = MoWuJiangLinWGData.Instance:GetJiangLinReward()
	if not reward_cfg then
		return
	end

	if reward_cfg.reward_show then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(reward_cfg.reward_show)
		self.jl_sl_reward_list:SetDataList(data_list)
	end

	if reward_cfg.last_kill_reward_show then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(reward_cfg.last_kill_reward_show)
		self.jl_yj_reward_list:SetDataList(data_list)
	end
end

function OperationActivityView:OnJLBtnTipClickHnadler()
	local cfg = ActivityWGData.Instance:GetCanLookActivityInfo(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN)
	local name = cfg and cfg.active_name or ""
	local desc = self.view_info_cfg and self.view_info_cfg.rule_2 or ""
	local rule_1 = self.view_info_cfg and self.view_info_cfg.rule_1 or ""
	self.node_list.jl_tip_label.text.text = self.view_info_cfg.rule_2
	self:SetOutsideRuleTips(rule_1)
	self:SetRuleInfo(desc, name)

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN)
    if act_info ~= nil then
    	local count_time = act_info.end_time - TimeWGCtrl.Instance:GetServerTime()
    	if count_time > 0 then
    		local show_time = MoWuJiangLinWGData.Instance:GetMowuJiangLinViewShowTime()
    		if show_time ~= 0 then
    			self:SetActRemainTime(TabIndex.operation_act_task_chain, show_time)
    		end
    	end
    end
end

function OperationActivityView:OnBtnShiLianClickHnadler()
	local is_open = MoWuJiangLinWGData.Instance:IsInMoWuJiangLinActivity()
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OperationActivity.NoWuFlushHint)
		return
	end

	MoWuJiangLinWGCtrl.Instance:GotoShiLian()
	self:Close()
end

--设置界面的所有显示信息包括底图(只需要赋值一次的)
function OperationActivityView:SetViewShowInfo()

	-- local title_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a2_zx_ggdt")
	-- local bundle3, asset3 = ResPath.GetRawImagesPNG(title_bg_name)
	-- self.node_list["title_bg"].raw_image:LoadSprite(bundle3, asset3, function()
    --     self.node_list["title_bg"].raw_image:SetNativeSize()
    -- end)


end

function OperationActivityView:FlushMWJiangLinModel()
	local boss_id, param_cfg = MoWuJiangLinWGData.Instance:GetBossId()
	if boss_id and param_cfg then
		if nil == self.mw_jianglin_display_model then
			self.mw_jianglin_display_model = OperationActRender.New(self.node_list.jl_display)
			self.mw_jianglin_display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
		end
	end

	local display_data = {}
	display_data.render_type = param_cfg.render_type
	if param_cfg.render_int_param1 and param_cfg.render_int_param1 > 0 then


		display_data.item_id = param_cfg.render_int_param1
	elseif param_cfg.model_bundle_name and param_cfg.model_bundle_name ~= ""
			and param_cfg.model_asset_name and param_cfg.model_asset_name ~= "" then
		display_data.bundle_name = param_cfg.model_bundle_name
		display_data.asset_name = param_cfg.model_asset_name
		
		display_data.model_rt_type = ModelRTSCaleType.L
		-- 模型位置
		if param_cfg.model_pos and param_cfg.model_pos ~= "" then
			local pos_list = string.split(param_cfg.model_pos, "|")
			local pos_x = tonumber(pos_list[1]) or 0
			local pos_y = tonumber(pos_list[2]) or 0
			local pos_z = tonumber(pos_list[3]) or 0
	
			display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
		end
		-- 模型旋转角度
		if param_cfg.model_rot and param_cfg.model_rot ~= "" then
            local rot_list = string.split(param_cfg.model_rot, "|")
            local rot_x = tonumber(rot_list[1]) or 0
            local rot_y = tonumber(rot_list[2]) or 0
            local rot_z = tonumber(rot_list[3]) or 0

            display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
        end
		-- 模型尺寸
		if param_cfg.model_scale then
			display_data.model_adjust_root_local_scale = param_cfg.model_scale
		end
	end

	if param_cfg.whole_display_pos and param_cfg.whole_display_pos ~= "" then
		local pos = Split(param_cfg.whole_display_pos, "|")
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list.jl_display.rect, tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	self.mw_jianglin_display_model:SetData(display_data)
end

function OperationActivityView:InitJiangLinFlushDesc()
	local is_dead = MoWuJiangLinWGData.Instance:GetShiLianBossState()
	if is_dead then
		self.node_list.jl_title_text.text.text = Language.OperationActivity.ShiLianTime4
		return 
	end
	
	local is_in_time, time_data = MoWuJiangLinWGData.Instance:IsInShiLianActivity()
	if is_in_time  then
		self.node_list.jl_title_text.text.text = Language.OperationActivity.ShiLianTime3
	else
		if time_data then
			local time_str = TimeUtil.FormatHM(time_data.start_timestemp)
			self.node_list.jl_title_text.text.text = string.format(Language.OperationActivity.JianLinTip, time_str)
		else
			self.node_list.jl_title_text.text.text = ""
		end
	end

	-- local cfg = WorldTreasureWGData.Instance:GetJiangLinFlushTimeCfg()
	-- local time_pramstr = {}
	-- local index = 1
	-- if cfg ~= nil then
	-- 	for k,v in pairs(cfg) do
	-- 		time_pramstr[index] = string.format("%s:%s",string.sub(v.refresh_time,1,2) ,string.sub(v.refresh_time,-2))
	-- 		index = index + 1
	-- 	end
	-- 	self.node_list.jl_title_text.text.text = string.format(Language.WorldTreasure.JianLinTip, time_pramstr[1])
	-- end
end