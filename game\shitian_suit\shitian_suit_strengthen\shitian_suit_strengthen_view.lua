ShiTianSuitStrengthenView = ShiTianSuitStrengthenView or BaseClass(SafeBaseView)

function ShiTianSuitStrengthenView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.default_index = TabIndex.shitian_suit_strengthen
	self:SetMaskBg()

    local shitian_suit_strengthe_path = "uis/view/shitian_suit_ui_prefab"
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_panel")
    self:AddViewResource(TabIndex.shitian_suit_strengthen, shitian_suit_strengthe_path, "layout_shitiansuit_strengthen")
    self:AddViewResource(TabIndex.shitian_suit_gemstone, shitian_suit_strengthe_path, "layout_shitiansuit_gemstone")
    self:AddViewResource(TabIndex.shitian_suit_infuse_soul, shitian_suit_strengthe_path, "layout_shitiansuit_infuse_soul")
    self:AddViewResource(0, shitian_suit_strengthe_path, "layout_shitiansuit_common_panel")
    self:AddViewResource(0, shitian_suit_strengthe_path, "VerticalTabbar")
end

function ShiTianSuitStrengthenView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ShiTianSuit.ViewName
    self.cur_show_suit_seq = -1
    self.jump_tab_index = nil
    self.jump_ep_index = nil

    -- 灵皇套装列表
    if not self.shitian_suit_list then
        local res_async_loader = AllocResAsyncLoader(self, "shitiansuit_list_render")
        res_async_loader:Load("uis/view/shitian_suit_ui_prefab", "suit_type_cell", nil,
        function(new_obj)
            if IsNil(new_obj) then
                return
            end

            local show_info = ShiTianSuitWGData.Instance:GetSuitTypeCfg()
            local item_parent = self.node_list.shitian_suit_list.transform
            local item_list = {}
            for i = 0, #show_info do
                local obj = ResMgr:Instantiate(new_obj)
                obj.transform:SetParent(item_parent, false)
                item_list[i] = ShiTianSuitRender.New(obj)
                item_list[i]:SetToggleGroup(self.node_list.shitian_suit_list.toggle_group)
                item_list[i]:SetIndex(i)
                item_list[i]:SetData(show_info[i])
                item_list[i]:SetClickCallBack(BindTool.Bind(self.OnSelectSuitToggle, self))
            end

            self.shitian_suit_list = item_list
        end)
    end

    if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:Init(Language.ShiTianSuit.StrengthenTabGrop, nil, "uis/view/shitian_suit_ui_prefab", nil, nil, VerShiTianSuitStrengthenRender)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
    end

    -- 装备列表
    if not self.shitian_ep_list then
        self.shitian_ep_list = ShiTianEquipListView.New(ShiTianEquipRender, self.node_list.shitian_ep_list)
        self.shitian_ep_list:SetStartZeroIndex(true)
        self.shitian_ep_list:SetSelectCallBack(BindTool.Bind(self.OnSelectSuitEquip, self))
        self.shitian_ep_list:SetCreateCellCallBack(BindTool.Bind(self.CreateCellSetShowType, self))
    end
end

function ShiTianSuitStrengthenView:OpenCallBack()
    self.first_enter = true
end

function ShiTianSuitStrengthenView:CalcShowIndex()
	return ShiTianSuitStrengthenWGData.Instance:GetOneRemindTabIndex(self.cur_show_suit_seq)
end

function ShiTianSuitStrengthenView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    if self.shitian_ep_list then
        self.shitian_ep_list:DeleteMe()
        self.shitian_ep_list = nil
    end

    if self.shitian_suit_list then
        for k, v in pairs(self.shitian_suit_list) do
            v:DeleteMe()
        end

        self.shitian_suit_list = nil
    end

    self.cur_select_ep_data = nil
    self:StrengthenReleaseCallBack()
    self:GemstoneReleaseCallBack()
    self:InfuseSoulReleaseCallBack()
end

function ShiTianSuitStrengthenView:CloseCallBack()
    self.jump_tab_index = nil
    self.jump_ep_index = nil
    self:CancelAutoStrengthen()
    self:CancelAutoInfuseSoul()
end

function ShiTianSuitStrengthenView:LoadIndexCallBack(index)
    if index == TabIndex.shitian_suit_strengthen then
        self:StrengthenLoadCallBack()
    elseif index == TabIndex.shitian_suit_gemstone then
        self:GemstoneLoadCallBack()
    elseif index == TabIndex.shitian_suit_infuse_soul then
        self:InfuseSoulLoadCallBack()
    end
end

function ShiTianSuitStrengthenView:ShowIndexCallBack(index)
    if self.first_enter then
        self:FlushSuitEpList()
        self.first_enter = false
    end

    local img_index = math.ceil(index / 10)
    img_index = img_index == 0 and 1 or img_index
    local big_bg_name = "a2_lhtz_bigbg_" .. img_index
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(big_bg_name))

    self.shitian_suit_list[self.cur_show_suit_seq or 0]:SetToggleIsOn(true)
    self:CancelAutoStrengthen()
    self:CancelAutoInfuseSoul()
end

function ShiTianSuitStrengthenView:OnFlush(parma, index)
    for k, v in pairs(parma) do
        if v.select_type then --跳转进来的第一次刷新
            self.cur_show_suit_seq = v.select_type
            self.jump_tab_index = ShiTianSuitStrengthenWGData.Instance:GetOneRemindTabIndex(self.cur_show_suit_seq)
            self.jump_ep_index = ShiTianSuitStrengthenWGData.Instance:GetOneRemindEpIndex(self.cur_show_suit_seq, 0, self.jump_tab_index)
        end

        if not self.jump_ep_index and self.jump_tab_index then
            local jump_tab_index = self.jump_tab_index
            self.jump_tab_index = nil
            if jump_tab_index ~= self.show_index then
                self:ChangeToIndex(jump_tab_index)
                return
            end
        end

        self.shitian_ep_list:FlushAllCellInfo(index)

        if k == "all" then
            if index == TabIndex.shitian_suit_strengthen then
                self:StrengthenOnFlush()
            elseif index == TabIndex.shitian_suit_gemstone then
                self:GemstoneOnFlush()
            elseif index == TabIndex.shitian_suit_infuse_soul then
                self:InfuseSoulOnFlush()
            end
        elseif k == "update_strengthen" then
            self:FlushStrengthenPanel(true)
        elseif k == "update_stone" then
            self:FlushStonePanel(true)
        elseif k == "update_infuse_soul" then
            self:FlushInfuseSoulPanel(true)
        elseif k == "update_total_level" then
            if index == TabIndex.shitian_suit_strengthen then
                self:FlushTotalStrengthenPanel()
            elseif index == TabIndex.shitian_suit_gemstone then
                self:FlushStoneTotalLevelPanel()
            elseif index == TabIndex.shitian_suit_infuse_soul then
                self:FlushInfuseSoulTotalLevelPanel()
            end
        elseif k == "item_add" then
            if index == TabIndex.shitian_suit_strengthen then
                self:FlushStrengthenStuff()
            elseif index == TabIndex.shitian_suit_gemstone then
                self:FlushStonePanel()
            elseif index == TabIndex.shitian_suit_infuse_soul then
                self:FlushInfuseSoulStuff()
            end
        elseif k == "coin_add" and index == TabIndex.shitian_suit_strengthen then
            self:FlushStrengthenStuff(true)
        end
    end

    self:FlushTabbarRemind()
    self:FlushSuitToggleRemind()
end


function ShiTianSuitStrengthenView:CreateCellSetShowType(cell)
    cell:SetSuitType(self.show_index)
end

function ShiTianSuitStrengthenView:OnSelectSuitToggle(cell)
    local cell_data = cell:GetData()
    if not cell_data or self.cur_show_suit_seq == cell_data.suit_seq then
        return
    end

    self.cur_show_suit_seq = cell_data.suit_seq
    local jump_index = self.cur_select_ep_data and self.cur_select_ep_data.part or 0
    self:FlushSuitEpList(jump_index)
end

function ShiTianSuitStrengthenView:OnSelectSuitEquip(cell)
    local cell_data = cell:GetData()
    if not cell_data then
        return
    end

    self.cur_select_ep_data = cell_data
    self:CancelAutoStrengthen()
    self:CancelAutoInfuseSoul()
    self:Flush()

end

function ShiTianSuitStrengthenView:FlushSuitToggleRemind()
    for k, v in pairs(self.shitian_suit_list) do
        v:FlushRemind()
    end
end

function ShiTianSuitStrengthenView:FlushSuitEpList(jump_index)
    local ep_data_list = ShiTianSuitWGData.Instance:GetShiTianHoleList(self.cur_show_suit_seq)
    self.shitian_ep_list:SetDataList(ep_data_list)

    if self.jump_ep_index then
        self.shitian_ep_list:JumpToIndex(self.jump_ep_index)
        self.jump_ep_index = nil
    elseif jump_index then
        self.shitian_ep_list:JumpToIndex(jump_index)
    end
end

function ShiTianSuitStrengthenView:FlushTabbarRemind()
	local ver_cell_list = self.tabbar:GetVerCellList()
    for index = TabIndex.shitian_suit_strengthen, TabIndex.shitian_suit_infuse_soul, 10 do
        local ver_cell = ver_cell_list[index / 10]
        if ver_cell then
            ver_cell:ShowRemind(ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitEpRemind(self.cur_show_suit_seq, index))
        end
    end
end

function ShiTianSuitStrengthenView:OnClickResonanceBtn()
    if not self.cur_select_ep_data then
        return
    end

    ShiTianSuitStrengthenWGCtrl.Instance:OpenResonanceView({show_type = self.show_index, show_suit_seq = self.cur_show_suit_seq})
end

-------------- VerShiTianSuitStrengthenRender功能页签 --------------
VerShiTianSuitStrengthenRender = VerShiTianSuitStrengthenRender or BaseClass(VerItemRender)

local BTN_IMAGE_TYPE = {
    Left = 1,
    Mid = 2,
    Right = 3,
}

function VerShiTianSuitStrengthenRender:OnFlush()
    VerItemRender.OnFlush(self)
    if self.index then
        local btn_img_type = self.index == 1 and BTN_IMAGE_TYPE.Left or BTN_IMAGE_TYPE.Mid
        btn_img_type = self.index == 3 and BTN_IMAGE_TYPE.Right or btn_img_type

        local nor_bundle, nor_asset = ResPath.GetShiTianSuitImg("a2_lhtz_tab_" .. btn_img_type)
        self.node_list["normal"].image:LoadSprite(nor_bundle, nor_asset, function ()
            self.node_list["normal"].image:SetNativeSize()
         end)

        local hl_bundle, hl_asset = ResPath.GetShiTianSuitImg("a2_lhtz_tab_hl_" .. btn_img_type)
        self.node_list["HLImage"].image:LoadSprite(hl_bundle, hl_asset, function ()
            self.node_list["HLImage"].image:SetNativeSize()
        end)

        self.node_list.normal_point:SetActive(self.index ~= BTN_IMAGE_TYPE.Right)
        self.node_list.hl_point_left:SetActive(self.index ~= BTN_IMAGE_TYPE.Left)
        self.node_list.hl_point_right:SetActive(self.index ~= BTN_IMAGE_TYPE.Right)
    end
end

-------------- 套装切换render --------------
ShiTianSuitRender = ShiTianSuitRender or BaseClass(STSuitTypeRender)

function ShiTianSuitRender:FlushRemind()
	local remind = ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitRemind(self.index)
	self.node_list.remind:SetActive(remind)
end

-------------- ShiTianEquipListView装备列表 --------------
ShiTianEquipListView = ShiTianEquipListView or BaseClass(AsyncListView)

function ShiTianEquipListView:FlushAllCellInfo(type)
    for k, v in pairs(self.cell_list) do
        v:SetSuitType(type)
	end
end

-------------- 单件装备render --------------
ShiTianEquipRender = ShiTianEquipRender or BaseClass(BaseRender)

function ShiTianEquipRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.item_pos)
    self.item_cell:SetUseButton(false)
    self.item_cell:IsCanDJ(false)
end

function ShiTianEquipRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function ShiTianEquipRender:OnFlush()
    if not self.data then
        return
    end

    self.item_cell:SetData({item_id = self.data.icon_id})
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(self.data.suit_seq, self.data.part)
    self.item_cell:SetGraphicGreyCualityBg(not is_act)

    self:FlushInfo()
end

function ShiTianEquipRender:SetSuitType(type)
    self.show_type = type
    self:FlushInfo()
end

function ShiTianEquipRender:FlushInfo()
    self.node_list.gemstone_list:SetActive(false)
    self.node_list.level_txt:SetActive(false)

    if not self.show_type or not self.data then
        return
    end

    local remind = false
    local suit_seq = self.data.suit_seq
    local part = self.data.part
    if self.show_type == SHOW_TYPE.Gemstone then
        local compose_flag = false
        self.node_list.gemstone_list:SetActive(true)
        local leng = self.node_list.gemstone_list.transform.childCount
        local stone_type = ShiTianSuitStrengthenWGData.Instance:GetEquipGemTypeBySlot(part).shitianstone_type or 0
        local bundle, asset = ResPath.GetShiTianSuitIcon("a2_lhtz_stone_s_" .. stone_type)
        local stone_data = ShiTianSuitStrengthenWGData.Instance:GetStoneDataList(suit_seq, part)
        for i = 1, leng do
            local stone_id = stone_data[i - 1] or 0
            local is_act = stone_id > 0
            local can_compose = ShiTianSuitStrengthenWGData.Instance:ComputBaoShiUpgradePrice(stone_id) <= 0
            if not compose_flag and can_compose then
                compose_flag = true
            end
            local stone_img = self.node_list["gemstone_" .. i]
            stone_img.image:LoadSprite(bundle, asset)
            XUI.SetGraphicGrey(stone_img, not is_act)

            local stone_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(stone_id)
            local stone_level = stone_cfg and stone_cfg.level or -1
            -- self.node_list["garde" .. i].text.text = CommonDataManager.GetDaXie(stone_level)
        end

        remind = ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitEpStoneRemind(suit_seq, part) or compose_flag
    else
        self.node_list.level_txt:SetActive(true)
        local level_str
        if self.show_type == SHOW_TYPE.Strengthen then
            remind = ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitEpCanStrengthen(suit_seq, part)
            local strengthen_level = ShiTianSuitStrengthenWGData.Instance:GetEpStrengthenCurLevel(suit_seq, part)
            level_str = string.format(Language.ShiTianSuit.StrengthenLevel, strengthen_level)
        elseif self.show_type == SHOW_TYPE.InfuseSoul then
            local level, quality = ShiTianSuitStrengthenWGData.Instance:GetEpInfuseSoulCurLevelAndQuality(suit_seq, part)
            quality = quality > 8 and 8 or quality
            local fumo_des = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulLevelCfg(suit_seq, part).fumo_des or ""
            remind = ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitEpCanInfuseSoul(suit_seq, part)
            level_str = ToColorStr(fumo_des, ITEM_COLOR[quality])
        end

        self.node_list.level_txt.text.text = level_str
    end

    self.node_list.remind:SetActive(remind)
end

function ShiTianEquipRender:OnSelectChange(is_select)
	self.node_list.select_hl:SetActive(is_select)
end