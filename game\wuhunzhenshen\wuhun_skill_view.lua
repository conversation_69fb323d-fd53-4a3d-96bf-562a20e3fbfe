WuHunSkillView = WuHunSkillView or BaseClass(SafeBaseView)

WH_VIEW_BTN_TYPE = {
	XIANG_QIAN = 1,
	JINNEG = 2,
}

function WuHunSkillView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
    self.view_name = "WuHunSkillView"
	local bundle_name = "uis/view/wuhunzhenshen_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(948, 594)})
    self:AddViewResource(0, bundle_name, "layout_tianshen_wuhun_xiangqian")

	self.curr_btn_index = nil
	self.wuhun_skill_view_list = nil
	self.wuhun_skill_list = nil
	self.curr_select_skill = nil
	self.curr_select_skill_index = nil
end

function WuHunSkillView:__delete()
end

function WuHunSkillView:OpenCallBack()
end

function WuHunSkillView:CloseCallBack()
	self.curr_btn_index = nil
end

function WuHunSkillView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.WuHunZhenShen.WuHunXiangQian

	if self.wuhun_skill_list == nil then
        self.wuhun_skill_list = {}
        for i = 1, 4 do
            local skill_obj = self.node_list["wuhun_skill_list"]:FindObj(string.format("btn_skill%d", i))
            if skill_obj then
                local cell = TianShenSelectWuHunSkillRender.New(skill_obj)
                cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.OnSelectTianShenWuHunSkillCallBack, self, cell))
                self.wuhun_skill_list[i] = cell
            end
        end
    end

	if not self.wuhun_skill_view_list then
		self.wuhun_skill_view_list = AsyncListView.New(WuHunSkillItemRender, self.node_list["wuhun_skill_list_view"])
		self.wuhun_skill_view_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectWuHunSkillHandler, self))
	end

	XUI.AddClickEventListener(self.node_list.skill_btn_xiangqian, BindTool.Bind2(self.WuHunOnXiangQianClick, self))
	XUI.AddClickEventListener(self.node_list.skill_btn_jinneng, BindTool.Bind2(self.WuHunOnJiNengClick, self))
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.WuHunSkillView, self.get_guide_ui_event)
end

function WuHunSkillView:ReleaseCallBack()
	self.curr_btn_index = nil
	self.curr_select_skill = nil
	self.curr_select_skill_index = nil

	if self.wuhun_skill_list and #self.wuhun_skill_list > 0 then
		for _, wuhun_skill_cell in ipairs(self.wuhun_skill_list) do
			wuhun_skill_cell:DeleteMe()
			wuhun_skill_cell = nil
		end

		self.wuhun_skill_list = nil
	end

	if self.wuhun_skill_view_list then
		self.wuhun_skill_view_list:DeleteMe()
		self.wuhun_skill_view_list = nil
	end

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.WuHunSkillView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function WuHunSkillView:WuHunRefreshRootStatus()
	self.node_list.wuhun_skill_list_view:CustomSetActive(self.curr_btn_index == WH_VIEW_BTN_TYPE.XIANG_QIAN)
	self.node_list.wuhun_skill_desc_root:CustomSetActive(self.curr_btn_index == WH_VIEW_BTN_TYPE.JINNEG)
	self.node_list.skill_btn_xiangqian_Image_nor:CustomSetActive(self.curr_btn_index ~= WH_VIEW_BTN_TYPE.XIANG_QIAN)
	self.node_list.skill_btn_xiangqian_Image_hl:CustomSetActive(self.curr_btn_index == WH_VIEW_BTN_TYPE.XIANG_QIAN)
	self.node_list.skill_btn_jinneng_Image_nor:CustomSetActive(self.curr_btn_index ~= WH_VIEW_BTN_TYPE.JINNEG)
	self.node_list.skill_btn_jinneng_Image_hl:CustomSetActive(self.curr_btn_index == WH_VIEW_BTN_TYPE.JINNEG)
end


function WuHunSkillView:OnFlush(param_t, index)
	if self.curr_btn_index == nil then
		self.curr_btn_index = WH_VIEW_BTN_TYPE.XIANG_QIAN
	end

	local skill_data = WuHunWGData.Instance:GetWuHunSkillData()

	if self.wuhun_skill_list and skill_data then
		for i, skill_cell in ipairs(self.wuhun_skill_list) do
			skill_cell:SetData(skill_data[i])

			if self.curr_select_skill_index == nil and skill_data[i].is_can_add_skill then
				self.curr_select_skill_index = i
			end
		end

		if self.curr_select_skill_index == nil then
			self.curr_select_skill_index = 1
		end

		self.curr_select_skill = skill_data[self.curr_select_skill_index]
		local curr_skill_list = WuHunWGData.Instance:GetCurrCanUseSkillList(skill_data)
		self.wuhun_skill_view_list:SetDataList(curr_skill_list)
		self:OnSelectTianShenWuHunSkillChange()
	end

	--self:RefreshRoleSkillMessage()
	self:RefreshSkillDescRoot()
	self:WuHunRefreshRootStatus()
end

function WuHunSkillView:RefreshRoleSkillMessage()
	local role_sex, role_prof = RoleWGData.Instance:GetRoleSexProf()
	local bg_res, icon_res, name_res
	if role_prof == GameEnum.ROLE_PROF_4 then
		-- bg_res = string.format("a2_jn_di%s_%s", role_prof, role_sex)
		icon_res = string.format("a2_jn_icon%s_%s", role_prof, role_sex)
		name_res = string.format("a2_jn_prof%s_%s", role_prof, role_sex)
	else
		-- bg_res = string.format("a2_jn_di%s", role_prof)
		icon_res = string.format("a2_jn_icon%s_%s", role_prof, role_sex)
		name_res = string.format("a2_jn_prof%s_%s", role_prof, role_sex)
	end

	-- local bundle, asset = ResPath.GetRawImagesPNG(bg_res)
	-- self.node_list.zd_skill_bg.raw_image:LoadSprite(bundle, asset)

	local bundle, asset = ResPath.GetRawImagesPNG(icon_res)
	self.node_list.zd_skill_prof.raw_image:LoadSprite(bundle, asset)
	
	bundle, asset = ResPath.GetRoleUIImage(name_res)
	self.node_list.zd_skill_prof_icon.image:LoadSprite(bundle, asset)
end

function WuHunSkillView:RefreshSkillDescRoot()
	if not self.curr_select_skill then
		return
	end

	local curr_data = self.curr_select_skill
	local skill_level = WuHunWGData.Instance:GetWuHunSkillLevel(curr_data.wuhun_id, curr_data.level, curr_data.promotion_state)
	---刷新技能
	local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(curr_data.wuhun_id)

	if not active_cfg then
		return
	end

	local wuhun_skill_client_cfg = WuHunWGData.Instance:GetWuHunClientSkillCfg(active_cfg.skill_id, skill_level)

	if not wuhun_skill_client_cfg then
		return
	end

	self.node_list.skill_desc.text.text = wuhun_skill_client_cfg.description
	local wuhun_skill_skill_cfg = WuHunWGData.Instance:GetWuHunSkillCfg(active_cfg.skill_id, skill_level)

	if not wuhun_skill_skill_cfg then
		return
	end

	self.node_list.skill_name.text.text = wuhun_skill_skill_cfg.skill_name
	self.node_list.wuhun_name.text.text = active_cfg.wh_name

	local cur_wuhun_li = WuHunWGData.Instance:GetPurgatoryWuHunLi(curr_data.wuhun_id, curr_data.order)

	if not curr_data.lock then
		cur_wuhun_li = cur_wuhun_li + WuHunWGData.Instance:GetOtherWuHunLi(curr_data.wuhun_id, curr_data.level, nil, curr_data.promotion_state, curr_data.break_times)
	end
	
	local wuhun_li_cfg = WuHunWGData.Instance:GetWuHunLiCfg(curr_data.wh_type)
	self.node_list.wuhun_li_value.text.text = cur_wuhun_li

	if wuhun_li_cfg then
		self.node_list.wuhun_li_name.text.text = wuhun_li_cfg.wh_name
		self.node_list.wuhun_li_icon.image:LoadSprite(ResPath.GetWuHunZhenShenImage(wuhun_li_cfg.wh_bead))
	end
end

function WuHunSkillView:OnSelectWuHunSkillHandler(item, cell_index, is_default, is_click)
	if nil == item or nil == item.data then
		return
	end
	
	if is_click and self.curr_select_skill_index then
		-- 选择镶嵌或是替换
		WuHunWGCtrl:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_EQUIP_SKILL, item.data.wuhun_id, self.curr_select_skill_index + 3)	--去掉普攻三个槽位
	end
end

function WuHunSkillView:OnSelectTianShenWuHunSkillCallBack(item)
	if nil == item or nil == item.data then
		return
	end

    local skill_data = item.data
    if skill_data then
		self.curr_select_skill_index = item.index

		self:OnSelectTianShenWuHunSkillChange()
		self:Flush()
	end
end

function WuHunSkillView:OnSelectTianShenWuHunSkillChange()
	if self.wuhun_skill_list then
		for i, skill_cell in ipairs(self.wuhun_skill_list) do
			skill_cell:ChangeSelect(self.curr_select_skill_index)
		end
	end
end

function WuHunSkillView:WuHunOnXiangQianClick()
	if self.curr_btn_index ~= WH_VIEW_BTN_TYPE.XIANG_QIAN then
		self.curr_btn_index = WH_VIEW_BTN_TYPE.XIANG_QIAN
	end

	self:WuHunRefreshRootStatus()
end

function WuHunSkillView:WuHunOnJiNengClick()
	if not self.curr_select_skill then
		return
	end

	if not self.curr_select_skill.is_have_skill then	--没有技能不能切换技能
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunSelectSkillError)
		return
	end

	if self.curr_btn_index ~= WH_VIEW_BTN_TYPE.JINNEG then
		self.curr_btn_index = WH_VIEW_BTN_TYPE.JINNEG
	end

	self:WuHunRefreshRootStatus()
end

function WuHunSkillView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	if ui_name == "wuhun_skill_guide" then
		local fun = function()
			local skill_data = WuHunWGData.Instance:GetWuHunSkillData()
			local curr_skill_list = WuHunWGData.Instance:GetCurrCanUseSkillList(skill_data)
			local data = curr_skill_list and curr_skill_list[1]

			if data then
				-- 选择镶嵌或是替换
				WuHunWGCtrl:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_EQUIP_SKILL, data.wuhun_id, self.curr_select_skill_index + 3)	--去掉普攻三个槽位
			end
		end

		return self.node_list[ui_name], fun
	end

	return self.node_list[ui_name]
end

-------------------------------------------------------------------------
-- 镶嵌的技能itemrender--------------------------------------------------
-------------------------------------------------------------------------
WuHunSkillItemRender = WuHunSkillItemRender or BaseClass(BaseRender)

function WuHunSkillItemRender:OnFlush()
	if nil == self.data then
		return
	end

	self.node_list.insert_ed:CustomSetActive(self.data.used)

	---刷新技能
	local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(self.data.wuhun_id)
	local skill_level = WuHunWGData.Instance:GetWuHunSkillLevel(self.data.wuhun_id, self.data.level, self.data.promotion_state)

	if not active_cfg then
		self.node_list.skill_icon:CustomSetActive(false)
		return
	end

	local wuhun_skill_client_cfg = WuHunWGData.Instance:GetWuHunClientSkillCfg(active_cfg.skill_id, skill_level)

	if not wuhun_skill_client_cfg then
		self.node_list.skill_icon:CustomSetActive(false)
		return
	end

	self.node_list.skill_icon:CustomSetActive(true)
	self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(wuhun_skill_client_cfg.icon_resource))


	local wuhun_skill_skill_cfg = WuHunWGData.Instance:GetWuHunSkillCfg(active_cfg.skill_id, skill_level)

	if not wuhun_skill_skill_cfg then
		return
	end

	self.node_list.lbl_name.text.text = wuhun_skill_skill_cfg.skill_name
	self.node_list.simple_desc.text.text = active_cfg.skill_word
end

--===================================================================
TianShenSelectWuHunSkillRender = TianShenSelectWuHunSkillRender or BaseClass(BaseRender)
function TianShenSelectWuHunSkillRender:OnFlush()
	if self.data then
		self.node_list["skill_icon"]:SetActive(self.data.is_have_skill)
		--self.node_list["name_root"]:SetActive(self.data.is_have_skill)
		self.node_list["add_skill"]:SetActive(not self.data.is_have_skill)
		if self.data.is_have_skill then
			-- 设置技能图标
			local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(self.data.wuhun_id)
		
			if not active_cfg then
				self.node_list.skill_icon:CustomSetActive(false)
				return
			end
		
			local skill_level = WuHunWGData.Instance:GetWuHunSkillLevel(self.data.wuhun_id, self.data.level, self.data.promotion_state)
			local wuhun_skill_client_cfg = WuHunWGData.Instance:GetWuHunClientSkillCfg(active_cfg.skill_id, skill_level)
		
			if not wuhun_skill_client_cfg then
				self.node_list.skill_icon:CustomSetActive(false)
				return
			end
		
			self.node_list.skill_icon:CustomSetActive(true)
			self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(wuhun_skill_client_cfg.icon_resource))
		
			local wuhun_skill_skill_cfg = WuHunWGData.Instance:GetWuHunSkillCfg(active_cfg.skill_id, skill_level)
		
			if not wuhun_skill_skill_cfg then
				return
			end
		
			self.node_list.skill_dec.text.text = wuhun_skill_skill_cfg.skill_name
		end

		if self.data.role_skill_data then
			self.node_list.role_skill_root:SetActive(true)
			local bundle, asset = ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data.role_skill_data.skill_id))
			self.node_list.role_skil_icon.image:LoadSprite(bundle, asset)
		else
			self.node_list.role_skill_root:SetActive(false)
		end
		
		self.node_list["btn_skill_red"]:SetActive(self.data.is_can_add_skill)
	end
end

function TianShenSelectWuHunSkillRender:ChangeSelect(cur_select_index)
	self.node_list["highlight"]:SetActive(self.index and self.index == cur_select_index)
end
