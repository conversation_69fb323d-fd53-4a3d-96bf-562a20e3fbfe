﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_PrimitiveTypeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>num(typeof(UnityEngine.PrimitiveType));
		<PERSON><PERSON>("Sphere", get_Sphere, null);
		<PERSON><PERSON>("Capsule", get_Capsule, null);
		<PERSON><PERSON>("Cylinder", get_Cylinder, null);
		<PERSON><PERSON>("Cube", get_Cube, null);
		<PERSON><PERSON>("Plane", get_Plane, null);
		<PERSON><PERSON>("Quad", get_Quad, null);
		<PERSON><PERSON>unction("IntToEnum", IntToEnum);
		<PERSON>.EndEnum();
		TypeTraits<UnityEngine.PrimitiveType>.Check = CheckType;
		StackTraits<UnityEngine.PrimitiveType>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.PrimitiveType arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.PrimitiveType), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Sphere(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.PrimitiveType.Sphere);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Capsule(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.PrimitiveType.Capsule);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Cylinder(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.PrimitiveType.Cylinder);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Cube(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.PrimitiveType.Cube);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Plane(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.PrimitiveType.Plane);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Quad(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.PrimitiveType.Quad);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.PrimitiveType o = (UnityEngine.PrimitiveType)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

