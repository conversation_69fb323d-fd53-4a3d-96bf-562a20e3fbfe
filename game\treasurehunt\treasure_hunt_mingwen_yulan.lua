TreasureHuntMingWenYuLanView = TreasureHuntMingWenYuLanView or BaseClass(SafeBaseView)

function TreasureHuntMingWenYuLanView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_fuwen_reward_show_view")
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_fuwen_xunbao_yulan")
end

function TreasureHuntMingWenYuLanView:LoadCallBack()
    -- if not self.mingwen_grid_list then
    --     self.mingwen_grid_list = AsyncBaseGrid.New()
    --     self.mingwen_grid_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["ph_item_grid"], itemRender = MingWenItemRender})
    --     self.mingwen_grid_list:SetStartZeroIndex(false)
    -- end

    if not self.mingwen_grid_list then
        self.mingwen_grid_list = AsyncListView.New(MingWenYuLanItemRender, self.node_list.ph_item_grid)
    end
end

function TreasureHuntMingWenYuLanView:ReleaseCallBack()
    if self.mingwen_grid_list then
        self.mingwen_grid_list:DeleteMe()
        self.mingwen_grid_list = nil
    end

    -- if self.move_scroll then
    --     self.move_scroll:Kill()
    --     self.move_scroll = nil
    -- end
end

function TreasureHuntMingWenYuLanView:ShowIndexCallBack()
    -- if self.move_scroll then
    --     self.move_scroll:Kill()
    --     self.move_scroll = nil
    --     self.node_list["ph_item_grid"].scroll_rect.verticalNormalizedPosition = 0
    -- end

    -- local info = TreasureHuntWGData.Instance:GetMingwenItems()
    -- local cell_count = #info
    -- local time = 0.06 * cell_count
    -- if self.move_scroll == nil then
    --     self.move_scroll = UITween.DoScrollRectVerticalPosition(self.node_list["ph_item_grid"], 1 ,0 ,time)
    -- end
end

function TreasureHuntMingWenYuLanView:OnFlush()
    local info = TreasureHuntWGData.Instance:GetMingwenItems()
    self.node_list.no_data_tip:CustomSetActive(IsEmptyTable(info))
    self.mingwen_grid_list:SetDataList(info)

    local pass_lv =  FuBenPanelWGData.Instance:GetPassLevel() + 1
    self.node_list.pass_level_desc.text.text = string.format(Language.TreasureHunt.MingWenPassLevelDesc, math.ceil(pass_lv / 10))
end

MingWenYuLanItemRender = MingWenYuLanItemRender or BaseClass(BaseRender)
function MingWenYuLanItemRender:__init()

end

function MingWenYuLanItemRender:LoadCallBack()
    if not self.mingwen_item_list then
        self.mingwen_item_list = AsyncListView.New(MingWenItemRender, self.node_list.mingwen_item_list)
    end
end

function MingWenYuLanItemRender:ReleaseCallBack()
    if self.mingwen_item_list then
        self.mingwen_item_list:DeleteMe()
        self.mingwen_item_list = nil
    end
end

function MingWenYuLanItemRender:OnFlush()
    if not self.data then return end

    self.mingwen_item_list:SetDataList(self.data.item_list)
    local pass_level = self.data.pass_level + 1
    self.node_list.condition_value.text.text = math.ceil(pass_level / 10)
end