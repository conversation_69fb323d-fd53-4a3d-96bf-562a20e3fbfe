ExpWestSceneLogic = ExpWestSceneLogic or BaseClass(CommonFbLogic)

function ExpWestSceneLogic:__init()
end

function ExpWestSceneLogic:__delete()
end

function ExpWestSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- WuHunWGCtrl.Instance:OpenWuHunTowerTaskView()

	TaskWGCtrl.Instance:AddFlyUpList(function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	end)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		if Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_EXP_WEST_FB then
			MainuiWGCtrl.Instance:SetTaskContents(false)
			MainuiWGCtrl.Instance:SetOtherContents(true)
			MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
			MainuiWGCtrl.Instance:SetTeamBtnState(false)
			ExperienceFbWgCtrl.Instance:OpenExperienceFbSceneView()

			UiInstanceMgr.Instance:DoFBStartDown(TimeWGCtrl.Instance:GetServerTime() + 5, function()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)
		end
	end)

	local view = ExperienceFbWgCtrl.Instance:GetExperienceFbSceneView()
	if view then
		ViewManager.Instance:AddMainUIFuPingChangeList(view)
	end
end

function ExpWestSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	
	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	UiInstanceMgr.Instance:ColseFBStartDown()
	ExperienceFbWgCtrl.Instance:CloseExperienceFbSceneView()

	local view = ExperienceFbWgCtrl.Instance:GetExperienceFbSceneView()
	if view then
		ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	end
end