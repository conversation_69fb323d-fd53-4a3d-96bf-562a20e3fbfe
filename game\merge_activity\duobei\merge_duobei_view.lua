function MergeActivityView:OpenCallBackDuoBei()
	MergeDuoBeiWGCtrl.Instance:SendDayDuoBeiReq()
end

function MergeActivityView:LoadIndexCallBackDuoBei()
	-- self:DBTimeCountDown()
end

function MergeActivityView:ReleaseDBView()
	if self.db_reward_list then
		self.db_reward_list:DeleteMe()
		self.db_reward_list = nil
	end

	if self.duoibei_count_down and CountDownManager.Instance:HasCountDown(self.duoibei_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.duoibei_count_down)
	end
end

function MergeActivityView:FlushDBView()
	self:FlushDBReward()
end

function MergeActivityView:SetDuoBeiTips()
	self:SetRuleInfo(Language.MergeDuoBeiRecharge.TipsActivityHintShow, Language.MergeDuoBeiRecharge.TipsActivityHint)
	self:SetOutsideRuleTips(Language.MergeDuoBeiRecharge.DuoBeiRechargeState)
	self.node_list.duobei_desc.text.text = Language.MergeDuoBeiRecharge.DuoBeiRechargeState
end

function MergeActivityView:FlushDBReward()
    local cfg = MergeActDuoBeiWGData.Instance:GetDuoBeiInfo()
	if cfg ~= nil then
		if not self.db_reward_list then
            self.db_reward_list = AsyncListView.New(MergeDuoBeiItemRender, self.node_list.db_list)
        end
        self.db_reward_list:SetDataList(cfg)
	end
end

--有效时间倒计时
-- function MergeActivityView:DBTimeCountDown()
-- 	self.duoibei_count_down = "duoibei_count_down"
-- 	local invalid_time = MergeActDuoBeiWGData.Instance:GetActivityInValidTime()
-- 	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
-- 		self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
-- 		CountDownManager.Instance:AddCountDown(self.duoibei_count_down, BindTool.Bind1(self.UpdateDBCountDown, self), BindTool.Bind1(self.OnDBComplete, self), invalid_time, nil, 1)
-- 	end
-- end

-- function MergeActivityView:UpdateDBCountDown(elapse_time, total_time)
-- 	local valid_time = total_time - elapse_time
-- 	if valid_time > 0 then
-- 		self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
-- 	end
-- end

-- function MergeActivityView:OnDBComplete()
-- 	self.node_list.db_time_label.text.text = ""
-- end

-------------------------------------

MergeDuoBeiItemRender = MergeDuoBeiItemRender or BaseClass(BaseRender)
function MergeDuoBeiItemRender:__init()
	--TODO
	self.db_render_info_cfg = {}
	
end

function MergeDuoBeiItemRender:LoadCallBack()
    self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnClickDuoBei, self))
end

function MergeDuoBeiItemRender:__delete()
	if self.db_reward_item_list then
		self.db_reward_item_list:DeleteMe()
		self.db_reward_item_list = nil
	end
end

function MergeDuoBeiItemRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function MergeDuoBeiItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.name.text.text = self.data.cfg.wanfa_name
	local list = {}
    local show_len = 3
    if not IsEmptyTable(self.data.cfg.reward_item) then
        for i=0,#self.data.cfg.reward_item do
            if i == show_len then
                break
            end
            table.insert(list, self.data.cfg.reward_item[i])
        end
    else
        list = TianshenRoadWGData.Instance:GetDuoBeiRewardListByTaskType(self.data.cfg.task_type)
    end
    if not self.db_reward_item_list then
        self.db_reward_item_list = AsyncListView.New(QuanMinCommonItemRender, self.node_list.reward_list)
    end

 --    self.db_reward_item_list:SetDataList(list)

	-- self.node_list.reward_list:SetActive(true)
end

function MergeDuoBeiItemRender:OnBtnClickDuoBei()
	if self.data and self.data.cfg then
		local param = string.split(self.data.cfg.panel,"#")
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end
