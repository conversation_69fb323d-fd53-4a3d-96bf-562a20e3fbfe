KfPVPPrepareView = KfPVPPrepareView or BaseClass(SafeBaseView)

function KfPVPPrepareView:__init()
	self:LoadConfig()
	self.view_layer = UiLayer.Pop
end

function KfPVPPrepareView:__delete()

end

function KfPVPPrepareView:LoadConfig()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf3v3_ready")
end

function KfPVPPrepareView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("kf3v3_prepare_timer") then
		CountDownManager.Instance:RemoveCountDown("kf3v3_prepare_timer")
	end
end

function KfPVPPrepareView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_game_rule, BindTool.Bind1(self.OpenRule, self))
	local prepare_info = KuafuPVPWGData.Instance:GetPrepareInfo()
	local mul_time = prepare_info.next_state_time - TimeWGCtrl.Instance:GetServerTime()
	self:Flush()
	--mul_time = mul_time - 2
	if mul_time  > 0 then
		self:UpdateOpenCountDownTime(1, mul_time)
		CountDownManager.Instance:AddCountDown("kf3v3_prepare_timer", BindTool.Bind1(self.UpdateOpenCountDownTime, self),
			BindTool.Bind1(self.CompletePVPTime, self), nil, mul_time, 1)
	else
		self.node_list.fuhuo_time.text.text = 0
	end
end

function KfPVPPrepareView:OpenCallBack()

end

function KfPVPPrepareView:CompletePVPTime()
	self.node_list.fuhuo_time.text.text = 0
	self:Close()
end

function KfPVPPrepareView:UpdateOpenCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		self.node_list.fuhuo_time.text.text = math.floor(total_time - elapse_time)
	end
end

function KfPVPPrepareView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("kf3v3_prepare_timer") then
		CountDownManager.Instance:RemoveCountDown("kf3v3_prepare_timer")
	end
end

function KfPVPPrepareView:OnFlush(param_t)
	local role_info = KuafuPVPWGData.Instance:GetRoleInfo()
	local macth_info = KuafuPVPWGData.Instance:GetPrepareInfo()
	for i,v in ipairs(macth_info.user_info_list) do
		self.node_list["name_" .. i].text.text = string.format(Language.KuafuPVP.Role_Name,v.server_id,v.name) --v.name
		local reward_id = KuafuPVPWGData.Instance:GetRewardBaseCell(v.origin_score)
		self.node_list["level_name_" .. i].text.text = string.format(Language.KuafuPVP.Role_DuanWei,reward_id.name)
		self.node_list["power_"..i].text.text = v.capability
		local temp_param = nil
		if i > 3 then 													--蓝方
			temp_param = self.node_list["ph_blue"..(i-3)]
		else
			temp_param = self.node_list["ph_red"..i]
		end
		local bundle,asset = ResPath.GetRoleHeadIconPVP(v.prof)
		temp_param.image:LoadSprite(bundle,asset)
	end

end

function KfPVPPrepareView:OpenRule()
	RuleTip.Instance:SetContent(Language.KuafuPVP.KfPvPAfterTips, Language.KuafuPVP.KfPvPTips)
end