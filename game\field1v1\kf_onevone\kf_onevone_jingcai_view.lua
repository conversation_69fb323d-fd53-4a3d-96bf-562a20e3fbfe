KFOneVOneJingCaiView = KFOneVOneJingCaiView or BaseClass(SafeBaseView)

function KFOneVOneJingCaiView:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf_onevone_jingcai")
	self:SetMaskBg(true)
end

function KFOneVOneJingCaiView:LoadCallBack()
	local jingcai_groups_obj = self.node_list["jingcai_groups"].transform
	self.jingcai_group_list = {}
	-- for i=1,2 do
	-- 	local group_obj = jingcai_groups_obj:Find(str)
	--     local obj = ResMgr:Instantiate(self.node_list["group_item_prefab"].gameObject)
	-- 	local obj_transform = obj.transform
	-- 	obj_transform:SetParent(jingcai_groups_obj.transform, false)
	-- 	self.jingcai_group_list[i] = KFOneVOneJingCaiGroup.New(obj)
	-- end

	self.reward_item_list = {}
	for i=1,2 do
		self.reward_item_list[i] = ItemCell.New(self.node_list["reward_items"])
	end

	-- self.last_group_item = KFOneVOneJingCaiGroupItem.New(self.node_list["last_group"])

	local group_toggles_obj = self.node_list["group_toggles"].transform
	self.group_toggle_list = {}
	for i=1,4 do
		local toggle_obj = U3DObject(group_toggles_obj:Find("group_toggle"..i).gameObject, group_toggles_obj:Find("group_toggle"..i), self)
		self.group_toggle_list[i] = toggle_obj
		XUI.AddClickEventListener(toggle_obj,BindTool.Bind(self.ClickGroupToggle,self,i))
	end

	self.group_item_list = {}
	local group_item_list_obj = self.node_list["group_item_list"].transform
	for i=1,2 do
		local obj = U3DObject(group_item_list_obj:Find("group_item"..i).gameObject, group_item_list_obj:Find("group_item"..i), self)
		self.group_item_list[i] = KFOneVOneJCGroupItem.New(obj)
	end

	self.last_group_item = KFOneVOneJCGroupItem.New(self.node_list["last_group_item"])

	self.group_toggle_list[1].toggle.isOn = true
	self.cur_group_toggle_index = 1
	self.jingcai_group_data = {}

end

function KFOneVOneJingCaiView:ReleaseCallBack()
	if not IsEmptyTable(self.jingcai_group_list) then
		for k,v in pairs(self.jingcai_group_list) do
			v:DeleteMe()
		end
		self.jingcai_group_list = {}
	end
	
	if not IsEmptyTable(self.reward_item_list) then
		for k,v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = {}
	end
	
	if not IsEmptyTable(self.group_item_list) then
		for k,v in pairs(self.group_item_list) do
			v:DeleteMe()
		end
		self.group_item_list = {}
	end

	if self.last_group_item then
		self.last_group_item:DeleteMe()
		self.last_group_item = nil
	end

	if CountDownManager.Instance:HasCountDown("onevone_jingcai_time") then
		CountDownManager.Instance:RemoveCountDown("onevone_jingcai_time")
	end

	self.jingcai_group_data = {}
end

function KFOneVOneJingCaiView:OpenCallBack()
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_MATCH_INFO)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_PERSON_INFO)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_KNOCKOUT_MATCH_INFO)
end

function KFOneVOneJingCaiView:OnFlush(param)
	local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()

	self.jingcai_group_data = {}
	local num = #self.jingcai_group_data
	for i=1,4 do
		self.group_toggle_list[i]:SetActive(i <= num)
	end

	if knockout_state == KFOneVOneWGData.KnockoutState.Index2To1 then
		self.node_list["group_item_list"]:SetActive(false)
		self.node_list["group_toggles"]:SetActive(false)
		self.node_list["last_group_item"]:SetActive(true)
		if self.jingcai_group_data[1] and self.jingcai_group_data[1][1] then
			self.last_group_item:SetData(self.jingcai_group_data[1][1])
		end
	else
		self.node_list["group_item_list"]:SetActive(true)
		self.node_list["group_toggles"]:SetActive(true)
		self.node_list["last_group_item"]:SetActive(false)

		if self.jingcai_group_data[self.cur_group_toggle_index] then
			local group_data = self.jingcai_group_data[self.cur_group_toggle_index]
			for i=1,2 do
				if group_data[i] then
					self.group_item_list[i]:SetData(group_data[i])
					self.group_item_list[i]:SetVisible(true)
				else
					self.group_item_list[i]:SetVisible(false)
				end
			end
		end
	end

	local next_time = KFOneVOneWGData.Instance:GetNextFightStartTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if CountDownManager.Instance:HasCountDown("onevone_jingcai_time") then
		CountDownManager.Instance:RemoveCountDown("onevone_jingcai_time")
	end

	if next_time > 0 and match_state == KFOneVOneWGData.MatchState.TaoTai then
		if next_time > server_time then
			local time = next_time - server_time
			self:JingCaiRefreshTime(server_time, next_time)
			CountDownManager.Instance:AddCountDown("onevone_jingcai_time", BindTool.Bind1(self.JingCaiRefreshTime, self), BindTool.Bind1(self.JingCaiTimeComplete, self), nil, time,1)
		else
			self:JingCaiTimeComplete()
		end
	end

	local guess_cfg = KFOneVOneWGData.Instance:GetGuessRewardCfgByRound(knockout_state)
	if guess_cfg then
		local reward_item = guess_cfg.reward_item
		for i=1,2 do
			if reward_item[i-1] then
				self.reward_item_list[i]:SetVisible(true)
				self.reward_item_list[i]:SetData(reward_item[i-1])
			else
				self.reward_item_list[i]:SetVisible(false)
			end
		end
	end
	
end

function KFOneVOneJingCaiView:JingCaiRefreshTime(now_time, total_time)
	local lerp_time = math.floor(total_time - now_time)
	local time_str = TimeUtil.MSTime(lerp_time)
	local knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local grade_str = Language.Kuafu1V1.TaoTaiGrade[knockout_state]
	self.node_list["jingcai_desc"].text.text = string.format(Language.Kuafu1V1.JingCaiDesc,grade_str,time_str)
end

function KFOneVOneJingCaiView:JingCaiTimeComplete()
	local knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local grade_str = Language.Kuafu1V1.TaoTaiGrade[knockout_state]
	self.node_list["jingcai_desc"].text.text = string.format(Language.Kuafu1V1.JingCaiOverDesc,grade_str)
end

function KFOneVOneJingCaiView:ClickGroupToggle(index,isOn)
	self.cur_group_toggle_index = index
	if IsEmptyTable(self.jingcai_group_data) or not isOn then return end
	local knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	if knockout_state == KFOneVOneWGData.KnockoutState.Index2To1 then
		self.node_list["group_item_list"]:SetActive(false)
		self.node_list["group_toggles"]:SetActive(false)
		self.node_list["last_group_item"]:SetActive(true)
		if self.jingcai_group_data[1] and self.jingcai_group_data[1][1] then
			self.last_group_item:SetData(self.jingcai_group_data[1][1])
		end
	else
		self.node_list["group_item_list"]:SetActive(true)
		self.node_list["group_toggles"]:SetActive(true)
		self.node_list["last_group_item"]:SetActive(false)

		if self.jingcai_group_data[self.cur_group_toggle_index] then
			local group_data = self.jingcai_group_data[self.cur_group_toggle_index]
			for i=1,2 do
				if group_data[i] then
					self.group_item_list[i]:SetData(group_data[i])
					self.group_item_list[i]:SetVisible(true)
				else
					self.group_item_list[i]:SetVisible(false)
				end
			end
		end
	end
end

-----------------------------------------------------------------------
KFOneVOneJingCaiGroup = KFOneVOneJingCaiGroup or BaseClass(BaseRender)

function KFOneVOneJingCaiGroup:__init()
	self.group_jingca_list = {}
	for i=1,4 do
		self.group_jingca_list[i] = KFOneVOneJingCaiGroupItem.New(self.node_list["group_jingcai"..i])
	end
end

function KFOneVOneJingCaiGroup:__delete()
	for k,v in pairs(self.group_jingca_list) do
		v:DeleteMe()
	end
	self.group_jingca_list = {}
end

function KFOneVOneJingCaiGroup:OnFlush()
	if not self.data then return end
	for i=1,4 do
		self.node_list["group_jingcai"..i]:SetActive(not IsEmptyTable(self.data[i]))
		if not IsEmptyTable(self.data[i]) then
			self.group_jingca_list[i]:SetData(self.data[i])
		end
	end
end


-----------------------------------------------------------------------
KFOneVOneJingCaiGroupItem = KFOneVOneJingCaiGroupItem or BaseClass(BaseRender)

function KFOneVOneJingCaiGroupItem:__init()
	XUI.AddClickEventListener(self.node_list["role_item1"], BindTool.Bind(self.ClickJingCaiRoleBtn,self,1))
	XUI.AddClickEventListener(self.node_list["role_item2"], BindTool.Bind(self.ClickJingCaiRoleBtn,self,2))
end

function KFOneVOneJingCaiGroupItem:__delete()

end

function KFOneVOneJingCaiGroupItem:OnFlush()
	if not self.data then return end
	local next_time = KFOneVOneWGData.Instance:GetNextFightStartTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	for i=1,2 do
		local info = self.data[i]
		local name = info and info.name ~= "" and info.name or "虚位以待" 
		self.node_list["name"..i].text.text = name
		self.node_list["hl_name"..i].text.text = name
		local cur_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
		local info_index = info and info.index or -1
		local is_win_index = KFOneVOneWGData.Instance:GetKnockoutItemWinInfo(cur_state,info_index)
		local is_hl = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info_index)
		self.node_list["hl"..i]:SetActive(is_hl)
		self.node_list["vd_img"..i]:SetActive(is_win_index)
	end
end

function KFOneVOneJingCaiGroupItem:ClickJingCaiRoleBtn(index)
	if not self.data then return end
	local select_info = self.data[index]
	local info1 = self.data[1]
	local info2 = self.data[2]
	if IsEmptyTable(select_info) or IsEmptyTable(info1) or IsEmptyTable(info2) or info1.name == "" or info2.name == "" then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiLunKong)
		return
	end

	local is_guess1 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info1.index)
	local is_guess2 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info2.index)
	if is_guess1 or is_guess2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.HasJingCai)
		return 
	end

	local ok_fun = function ()
		--竞猜本轮胜利者 param1是下标索引标记
		local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_KNOCKOUT_GUESS_WINNER
		Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type,select_info.index)
	end
	local knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local guess_cfg = KFOneVOneWGData.Instance:GetGuessRewardCfgByRound(knockout_state)
	if guess_cfg then
		local guess_need_gold = guess_cfg.guess_need_gold or 0
		local str = string.format(Language.Kuafu1V1.SelectJingCaiRole,guess_need_gold)
		TipWGCtrl.Instance:OpenAlertTips(str, ok_fun)
	end
end




---------------------------------------------------------------------------------
KFOneVOneJCGroupItem = KFOneVOneJCGroupItem or BaseClass(BaseRender)

function KFOneVOneJCGroupItem:__init()
	self.head_list = {}
	for i=1,2 do
		local obj = self.node_list["head"..i].transform
		local item = {}
		item.nor_kuang = U3DObject(obj:Find("nor_kuang").gameObject, obj:Find("nor_kuang"), self)
		item.hl = U3DObject(obj:Find("hl").gameObject, obj:Find("hl"), self)
		local head_cell_obj = U3DObject(obj:Find("head_cell").gameObject, obj:Find("head_cell"), self)
		item.head_cell = BaseHeadCell.New(head_cell_obj)
		item.vd_img = U3DObject(obj:Find("vd_img").gameObject, obj:Find("vd_img"), self)
		item.name = U3DObject(obj:Find("name").gameObject, obj:Find("name"), self)
		item.not_head = U3DObject(obj:Find("not_head").gameObject, obj:Find("not_head"), self)
		local btn = U3DObject(obj:Find("btn").gameObject, obj:Find("btn"), self)
		self.head_list[i] = item
		XUI.AddClickEventListener(btn, BindTool.Bind(self.ClickJingCaiRoleBtn,self,i))
	end
	XUI.AddClickEventListener(self.node_list["jingcai_btn"], BindTool.Bind(self.ClickJingCaiBtn,self))
	self.cur_select_index = 0
end

function KFOneVOneJCGroupItem:__delete()
	for k,v in pairs(self.head_list) do
		if v.head_cell then
			v.head_cell:DeleteMe()
			v.head_cell = nil
		end
	end
	self.head_list = {}
end

function KFOneVOneJCGroupItem:OnFlush()
	if not self.data then return end
	local next_time = KFOneVOneWGData.Instance:GetNextFightStartTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local can_show_vd = false
	local cur_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	for i=1,2 do
		local item = self.head_list[i]
		local info = self.data[i]
		item.hl:SetActive(false)
		if not IsEmptyTable(info) and info.name ~= "" then
			local name = info and info.name ~= "" and info.name or Language.Kuafu1V1.XuWeiYiDai
			item.name.text.text = name
			local uuid = info.uuid
			local key = uuid.temp_low --.. uuid.temp_high
			AvatarManager.Instance:SetAvatarKey(key, info.avatar_key_big, info.avatar_key_small)
			item.head_cell:SetData({role_id = key, sex = info.sex, prof = info.prof})

			local info_index = info and info.index or -1
			local is_win_index = KFOneVOneWGData.Instance:GetKnockoutItemWinInfo(cur_state,info_index)
			local is_hl = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info_index)
			if is_hl then
				self.cur_select_index = i
			end
			if is_win_index then
				can_show_vd = true
			end
			item.hl:SetActive(is_hl)
			item.not_head:SetActive(false)
			item.head_cell:SetVisible(true)
		else
			item.not_head:SetActive(true)
			item.head_cell:SetVisible(false)
			item.name.text.text = Language.Kuafu1V1.XuWeiYiDai
		end
	end
	for i=1,2 do
		local item = self.head_list[i]
		local info = self.data[i]
		if not IsEmptyTable(info) then
			local info_index = info and info.index or -1
			local is_win = KFOneVOneWGData.Instance:GetKnockoutItemWinInfo(cur_state,info_index)
			local win_img_ab = is_win and "xmhdks_sheng" or "xmhdks_bai"
			item.vd_img.image:LoadSprite(ResPath.GetF2Field1v1(win_img_ab))
		else
			item.vd_img.image:LoadSprite(ResPath.GetF2Field1v1("xmhdks_bai"))
		end
		item.vd_img:SetActive(can_show_vd)
	end

	local info1 = self.data[1]
	local info2 = self.data[2]
	local is_guess1 = false
	local is_guess2 = false
	if not IsEmptyTable(info1) then
		is_guess1 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info1.index or 0)
	end
	if not IsEmptyTable(info2) then
		is_guess2 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info2.index or 0)
	end
	self.node_list["jingcai_btn"]:SetActive(not is_guess1 and not is_guess2)
	self.node_list["has_jingcai"]:SetActive(is_guess1 or is_guess2)
end

function KFOneVOneJCGroupItem:ClickJingCaiBtn()
	if not self.data then return end
	local select_info = self.data[self.cur_select_index]
	local info1 = self.data[1]
	local info2 = self.data[2]
	if IsEmptyTable(info1) or IsEmptyTable(info2) or info1.name == "" or info2.name == "" then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiLunKong)
		return
	end

	if IsEmptyTable(select_info) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiNotSelect)
		return
	end

	local is_guess1 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info1.index)
	local is_guess2 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info2.index)
	if is_guess1 or is_guess2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.HasJingCai)
		return 
	end

	local ok_fun = function ()
		--竞猜本轮胜利者 param1是下标索引标记
		local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_KNOCKOUT_GUESS_WINNER
		Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type,select_info.index)
	end
	local knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local guess_cfg = KFOneVOneWGData.Instance:GetGuessRewardCfgByRound(knockout_state)
	if guess_cfg then
		local guess_need_gold = guess_cfg.guess_need_gold or 0
		local str = string.format(Language.Kuafu1V1.SelectJingCaiRole,guess_need_gold)
		TipWGCtrl.Instance:OpenAlertTips(str, ok_fun)
	end
end

function KFOneVOneJCGroupItem:ClickJingCaiRoleBtn(index)
	local info1 = self.data[1]
	local info2 = self.data[2]
	if IsEmptyTable(info1) or IsEmptyTable(info2) or info1.name == "" or info2.name == "" then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiLunKong)
		return
	end

	local other_index = index % 2 == 1 and index + 1 or index - 1
	local item = self.head_list[index]
	local other_item = self.head_list[other_index]
	item.hl:SetActive(true)
	item.nor_kuang:SetActive(false)
	other_item.hl:SetActive(false)
	other_item.nor_kuang:SetActive(true)
	self.cur_select_index = index
end