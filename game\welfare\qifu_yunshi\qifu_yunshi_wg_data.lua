------------运势------------------
QifuYunShiWGData = QifuYunShiWGData or BaseClass()

--这几个类型对应 Y-运势 运势加成类型  作用是显示在各个系统界面上的百分比加成
QifuYunShiWGData.ADDITION_TYPE = {
	COMPOSE_TYPE = 1,			--装备合成概率
	BAOSHI_TYPE = 6,			--宝石洗练概率
	SHENGPING_TYPE = 7,			--装备升品概率
	EXP_BUFF_TYPE = 8,			--经验加成buff
}

function QifuYunShiWGData:__init()
	if QifuYunShiWGData.Instance then
		ErrorLog("[QifuYunShiWGData]:Attempt to create singleton twice!")
	end
	QifuYunShiWGData.Instance = self

	RemindManager.Instance:Register(RemindName.QiFuYunShi, BindTool.Bind(self.IsShowQiFuYunShiRedPoint, self))--祈福

	local fortune_cfg = ConfigManager.Instance:GetAutoConfig("fortune_cfg_auto")
	self.other_cfg = fortune_cfg.other[1]
	self.fortune_type_cfg = ListToMap(fortune_cfg.fortune_type, "fortune_type")
	self.fortune_addition_cfg = ListToMap(fortune_cfg.fortune_addition, "addition_id")
	self.fortune_addition_type_cfg = ListToMapList(fortune_cfg.fortune_addition, "addition_type")
	self.fortune_cfg = ListToMapList(fortune_cfg.fortune,"fortune_type")
	self.fortune_refresh_cfg = ListToMap(fortune_cfg.fortune_refresh,"times")

	self.fortune_info = {
		fortune_type = 0,
		refresh_times = 0,
		addition_id_list = {},
		has_info = false,
	}

end

function QifuYunShiWGData:__delete()
	QifuYunShiWGData.Instance = nil
	self.fortune_info = {
		fortune_type = 0,
		refresh_times = 0,
		addition_id_list = {},
		has_info = false,
	}
end

function QifuYunShiWGData:IsShowQiFuYunShiRedPoint()
	if self.fortune_info.fortune_type == 0 then
		return 1
	end	
	return 0
end

function QifuYunShiWGData:SetSCFortuneInfo(protocol)
	self.fortune_info.fortune_type = protocol.fortune_type
	self.fortune_info.refresh_times = protocol.refresh_times
	self.fortune_info.addition_id_list = protocol.addition_id_list
	self.fortune_info.has_info = true
end

function QifuYunShiWGData:GetFortuneInfo()
	return self.fortune_info
end

function QifuYunShiWGData:GetOtherCfg()
	return self.other_cfg
end

function QifuYunShiWGData:GetFortuneTypeCfg(fortune_type)
	return self.fortune_type_cfg[fortune_type]
end

function QifuYunShiWGData:GetFortuneAdditionCfg(addition_id)
	return self.fortune_addition_cfg[addition_id]
end

function QifuYunShiWGData:GetFortuneAdditionTypeCfg(add_type)
	return self.fortune_addition_type_cfg[add_type]
end

function QifuYunShiWGData:GetFortuneLevelCfg(fortune_type)
	local level = RoleWGData.Instance:GetRoleLevel()
	local cfg_list = self.fortune_cfg[fortune_type]
	if not IsEmptyTable(cfg_list) then
		for k,v in pairs(cfg_list) do
			if level >= v.min_level and level < v.max_level then
				return v
			end
		end
	end
end

function QifuYunShiWGData:GetFortuneRefreshCfg(times)
	return self.fortune_refresh_cfg[times]
end

function QifuYunShiWGData:GetMaxRefreshTimer()
	local max_cfg = self.fortune_refresh_cfg[#self.fortune_refresh_cfg]
	return max_cfg.times or 40
end

--获取今天的运势加成
function QifuYunShiWGData:GetCurMyFortuneAdditionCfg()
	local addition_id_list = self.fortune_info.addition_id_list
	local addition_list = {}
	-- local fortune_type = self.fortune_info.fortune_type
	-- local fortune_cfg = self:GetFortuneTypeCfg(fortune_type)
	-- if fortune_cfg then
	-- 	local data = {}
	-- 	data.title = fortune_cfg.title
	-- 	data.des = fortune_cfg.des
	-- 	table.insert(addition_list,data)
	-- end

	if not IsEmptyTable(addition_id_list) then
		for k,v in pairs(addition_id_list) do
			local add_cfg = self:GetFortuneAdditionCfg(tonumber(v))
			if not IsEmptyTable(add_cfg) then
				local data = {}
				data.title = add_cfg.title
				data.des = add_cfg.des
				data.type = add_cfg.type
				table.insert(addition_list,data)
			end
		end
	end
	return addition_list
end

--判断今天是否已抽取运势
function QifuYunShiWGData:GetToDayHasFortune()
	return self.fortune_info.fortune_type > 0
end


function QifuYunShiWGData:SetSendChouQianFlag(flag)
	self.send_chouqian_falg = flag
end

function QifuYunShiWGData:GetSendChouQianFlag()
	return self.send_chouqian_falg
end

--第一次抽签的标记
function QifuYunShiWGData:SetFirstSendChouQianFlag(flag)
	if flag then
		MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.QIFU_YUNSHI_TIP)
	end
	self.first_send_chouqian_falg = flag
end

function QifuYunShiWGData:GetFirstSendChouQianFlag()
	return self.first_send_chouqian_falg
end

function QifuYunShiWGData:SetIgnoireAnimFlag(flag)
	self.ignoire_anim_flag = flag
end

function QifuYunShiWGData:GetIgnoireAnimFlag()
	return self.ignoire_anim_flag
end

function QifuYunShiWGData:GetHasYunShiAdditionIdCfg(add_type)
	local info = self.fortune_info
	local cfg_list = self:GetFortuneAdditionTypeCfg(add_type)
	local has_id = 0
	local addition_id_list = info.addition_id_list
	for k,v in pairs(cfg_list) do
		local id = v.addition_id
		if addition_id_list[id] then
			has_id = id
			break
		end
	end
	if addition_id_list[has_id] then
		local cfg = QifuYunShiWGData.Instance:GetFortuneAdditionCfg(has_id)
		return cfg
	end
end
