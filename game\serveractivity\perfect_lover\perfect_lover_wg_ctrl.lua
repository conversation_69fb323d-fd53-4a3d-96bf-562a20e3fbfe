require("game/serveractivity/perfect_lover/perfect_lover_wg_data")
require("game/serveractivity/perfect_lover/perfect_lover_view")

PerfectLoverWGCtrl = PerfectLoverWGCtrl or BaseClass(BaseWGCtrl)

function PerfectLoverWGCtrl:__init()
	if PerfectLoverWGCtrl.Instance ~= nil then
		print("[PerfectLoverWGCtrl]error:create a singleton twice")
	end
	PerfectLoverWGCtrl.Instance = self

	self.view = PerfectLoverView.New()
	self.data = PerfectLoverWGData.New()

	self:RegisterAllProtocols()
end

function PerfectLoverWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
	end

	if nil ~= self.show_view then
		self.show_view:DeleteMe()
	end

	if nil ~= self.data then
		self.data:DeleteMe()
	end

	PerfectLoverWGCtrl.Instance = nil
end

function PerfectLoverWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRAPerfectLoverInfo, "OnSCRAPerfectLoverInfo")

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
end

function PerfectLoverWGCtrl:OnSCRAPerfectLoverInfo(protocol)
	self.data:SetPerfectLoverInfo(protocol)
	self.view:Flush()
end

function PerfectLoverWGCtrl:Open()
	self.view:Open()
end

function PerfectLoverWGCtrl:MainuiOpenCreate()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_PERFECTLOVER) then
		return
	end
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_PERFECTLOVER,
		opera_type = 0,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end