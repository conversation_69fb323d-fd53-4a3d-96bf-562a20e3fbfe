------------------------------------------------------------
--被动技能View
------------------------------------------------------------
local BTN_ICON_TYPE = {
	[1] = "a3_jn_icon_yq_zz_2",
	[2] = "a3_jn_icon_yq_zz_2",
	[3] = "a3_jn_icon_yq_zz_2",
	[4] = "a3_jn_icon_yq_xx_2",
	[5] = "a3_jn_icon_yq_zb_2",
}

function SkillView:InitPassiveView()
	self.skill_item_group = {}
	self.btn_list = {}
	self.skill_index = 0
	self.index = 0
	self.select_passive_skill_item = nil

	for i = 1, GameEnum.PASSIVE_SKILL.SKILL_NUM do
		self.skill_item_group[i] = SkillPassiveItem.New(self.node_list.skill_group:FindObj("skill_item_" .. i))
	end

	for i = 1, GameEnum.PASSIVE_SKILL.BUTTON_NUM do
		XUI.AddClickEventListener(self.node_list.passive_tabbar:FindObj("passive_btns/btn" .. i), BindTool.Bind(self.OnClickSkillButton,self,i))
	end

	self:OnClickSkillButton(4)
end

function SkillView:DeleteIntroView()
	if self.skill_item_group then
		for k, v in pairs(self.skill_item_group) do
			v:DeleteMe()
		end
		self.skill_item_group = nil
	end

	self.select_passive_skill_item = nil
end

-- 刷新右边区域
function SkillView:FlushSkillDescView(data)
	local desc = data.desc
	local icon = data.icon
	if data.is_spc == 1 then
		local dec, icon1,dark_des = EquipTargetWGData.Instance:GetSkillDes(data.index)
		desc = dark_des
		icon = icon1
	end

	self.node_list["skill_name"].text.text = data.name
	self.node_list["skill_desc"].text.text = desc --技能属性描述
	self.node_list["skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(icon))
	self.node_list["skill_open_need_tip"].text.text = not data.is_open and ToColorStr(data.des_2, COLOR3B.C4)
														or ToColorStr(Language.Skill.ActiveEnable, COLOR3B.C8)
end

-- 刷新左边区域
function SkillView:FlushLeftSkillView()
	local bundle, asset = ResPath.GetRoleUIImage(BTN_ICON_TYPE[self.index])
	self.node_list.beidong_normal_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.beidong_normal_skill_icon.image:SetNativeSize()
    end)

	local skill_data = SkillWGData.Instance:GetPassiveSkillByType(self.skill_index, SkillWGData.Instance:GetSkillListByType(2))
	local skill_open_data = SkillWGData.Instance:GetPassiveSkillOpen(self.index)
	local index = self.index == GameEnum.PASSIVE_SKILL.AFTER_INDEX and GameEnum.PASSIVE_SKILL.BEFORE_INDEX or self.index
	local start = 1
	local flag = true
	for k, v in pairs(skill_data) do
		for i = start, GameEnum.PASSIVE_SKILL.SKILL_NUM do
			if (skill_open_data["open_skill" .. i]) == 1 then
				self.skill_item_group[i]:SetData(v)
				self.skill_item_group[i]:SetParent(self)
				self.skill_item_group[i]:SetActive(true)
				start = i + 1
				if (flag) then
					self.skill_item_group[i]:OnClickSkill()
					flag = false
				end
				break
			else
				self.skill_item_group[i]:SetActive(false)
			end
		end
	end

	for i = start, GameEnum.PASSIVE_SKILL.SKILL_NUM do
		self.skill_item_group[i]:SetActive(false)
	end

	-- local bg_bundle, bg_asset = ResPath.GetF2RawImagesPNG("a3_jn_icon_" .. index)
    -- self.node_list.ps_bg2.raw_image:LoadSprite(bg_bundle, bg_asset)
end

function SkillView:SetPassiveSkillItemSelect(item)
	if nil ~= self.select_passive_skill_item then
		self.select_passive_skill_item:CancelSelect()
	end

	self.select_passive_skill_item = item
	self.select_passive_skill_item:Select()
end

function SkillView:OnClickSkillButton(index)
	self.index = index
	self.skill_index = GameEnum.PASSIVE_SKILL.PASSIVE_SKILL_INDEX[index]
	self:FlushLeftSkillView()
end

--------------------------------------------------------------------------------------------------------------
SkillPassiveItem = SkillPassiveItem or BaseClass(BaseRender)
function SkillPassiveItem:__init()

end

function SkillPassiveItem:__delete()
    self.parent = nil
end

function SkillPassiveItem:OnFlush()
	if self.data == nil then return end

	local icon = self.data.icon
	if self.data.is_spc == 1 then
		local _, icon1 = EquipTargetWGData.Instance:GetSkillDes(self.data.index)
		icon = icon1
	end

	local bundle, asset = ResPath.GetSkillIconById(icon)
	self.node_list["icon"].image:LoadSprite(bundle, asset)
	self.node_list["jinjie_item"].button:AddClickListener(BindTool.Bind1(self.OnClickSkill, self))
	self.node_list.lv_bg:CustomSetActive(self.data.is_open)
	self.node_list.name_txt.text.text = self.data.name

	if not self.data.is_open then
		self.node_list["skill_lock"]:SetActive(true)
	else
		self.node_list["skill_lock"]:SetActive(false)
	end
end

function SkillPassiveItem:OnClickSkill()
	if self.parent then
		self.parent:FlushSkillDescView(self.data)
		self.parent:SetPassiveSkillItemSelect(self)
	end
end

function SkillPassiveItem:SetParent(parent)
	self.parent = parent
end

function SkillPassiveItem:Select()
	self.node_list["highlight"]:SetActive(true)
end

function SkillPassiveItem:CancelSelect()
	self.node_list["highlight"]:SetActive(false)
end