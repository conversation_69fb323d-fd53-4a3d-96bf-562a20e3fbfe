-- K-跨服猎鲲地带.xls
local item_table={
[1]={item_id=28000,num=1,is_bind=1},
[2]={item_id=28001,num=1,is_bind=1},
[3]={item_id=26340,num=5,is_bind=1},
[4]={item_id=26340,num=2,is_bind=1},
[5]={item_id=22637,num=1,is_bind=1},
[6]={item_id=22638,num=1,is_bind=1},
[7]={item_id=22639,num=1,is_bind=1},
[8]={item_id=22640,num=1,is_bind=1},
[9]={item_id=22641,num=1,is_bind=1},
[10]={item_id=22642,num=1,is_bind=1},
[11]={item_id=22643,num=1,is_bind=1},
[12]={item_id=22644,num=1,is_bind=1},
[13]={item_id=22645,num=1,is_bind=1},
[14]={item_id=22646,num=1,is_bind=1},
[15]={item_id=22647,num=1,is_bind=1},
[16]={item_id=22648,num=1,is_bind=1},
[17]={item_id=22649,num=1,is_bind=1},
[18]={item_id=22650,num=1,is_bind=1},
[19]={item_id=22651,num=1,is_bind=1},
[20]={item_id=22652,num=1,is_bind=1},
[21]={item_id=22653,num=1,is_bind=1},
[22]={item_id=22654,num=1,is_bind=1},
[23]={item_id=22655,num=1,is_bind=1},
[24]={item_id=22656,num=1,is_bind=1},
[25]={item_id=22657,num=1,is_bind=1},
[26]={item_id=22658,num=1,is_bind=1},
[27]={item_id=22659,num=1,is_bind=1},
[28]={item_id=22660,num=1,is_bind=1},
[29]={item_id=22734,num=5,is_bind=1},
[30]={item_id=32971,num=1,is_bind=1},
[31]={item_id=32970,num=1,is_bind=1},
[32]={item_id=23575,num=1,is_bind=0},
[33]={item_id=23595,num=1,is_bind=0},
[34]={item_id=26340,num=1,is_bind=1},
[35]={item_id=28216,num=1,is_bind=0},
[36]={item_id=26345,num=1,is_bind=0},
[37]={item_id=22636,num=1,is_bind=1},
[38]={item_id=22734,num=4,is_bind=1},
}
return {
other={
{}},

activity_open_time={
{},
{},
{},
{}},

zone={
{boss_id_1=11748,boss_id_2=11749,boss_id_3=11750,boss_id_4=11751,},
{zone=1,scene_id=6171,boss_id_0=11734,},
{zone=2,scene_id=6172,boss_id_0=11735,},
{zone=3,scene_id=6173,boss_id_0=11736,},
{zone=4,scene_id=6174,boss_id_0=11737,}},

reward_pos={
{boss_id=11738,gather_id=211,},
{boss_id=11748,gather_id=213,},
{boss_id=11749,gather_id=214,},
{boss_id=11750,gather_id=215,},
{boss_id=11751,gather_id=216,},
{zone=1,boss_id=11734,},
{zone=1,gather_id=217,},
{zone=1,boss_id=11745,gather_id=218,},
{zone=1,boss_id=11746,gather_id=219,},
{zone=1,boss_id=11747,gather_id=220,},
{zone=2,boss_id=11735,},
{zone=2,gather_id=217,},
{zone=2,boss_id=11745,gather_id=218,},
{zone=2,boss_id=11746,gather_id=219,},
{zone=2,boss_id=11747,gather_id=220,},
{zone=3,boss_id=11736,},
{zone=3,gather_id=217,},
{zone=3,boss_id=11745,gather_id=218,},
{zone=3,boss_id=11746,gather_id=219,},
{zone=3,boss_id=11747,gather_id=220,},
{zone=4,boss_id=11737,},
{zone=4,gather_id=217,},
{zone=4,boss_id=11745,gather_id=218,},
{zone=4,boss_id=11746,gather_id=219,},
{zone=4,boss_id=11747,gather_id=220,}},

reward={
{gather_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},},
{gather_id=212,gather_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[4]},},
{gather_id=213,},
{gather_id=214,},
{gather_id=215,},
{gather_id=216,},
{gather_id=217,},
{gather_id=218,},
{gather_id=219,},
{gather_id=220,}},

cross_boss_information={
{boss_id=11738,view_resouce=213,big_icon=1099,name="王者·巨鲲",},
{zone=1,boss_id=11734,scene_id=6171,view_resouce=214,big_icon=1102,name="异兽尸鲲",},
{zone=2,boss_id=11735,scene_id=6172,view_resouce=215,big_icon=1100,name="异兽骨鲲",},
{zone=3,boss_id=11736,scene_id=6173,view_resouce=212,big_icon=1098,name="异兽灵鲲",},
{zone=4,boss_id=11737,scene_id=6174,view_resouce=211,big_icon=1101,name="异兽玄鲲",},
{index=2,boss_id=11748,x_pos=72,y_pos=174,view_resouce=214,big_icon=1102,name="巨兽尸鲲",},
{index=3,boss_id=11749,x_pos=45,y_pos=94,view_resouce=215,big_icon=1100,name="巨兽骨鲲",},
{index=4,boss_id=11750,x_pos=114,y_pos=45,view_resouce=212,big_icon=1098,name="巨兽灵鲲",},
{index=5,boss_id=11751,x_pos=182,y_pos=95,view_resouce=211,big_icon=1101,name="巨兽玄鲲",},
{zone=1,index=2,scene_id=6171,x_pos=72,y_pos=174,scale=0.6,},
{zone=1,index=3,boss_id=11745,scene_id=6171,x_pos=45,y_pos=94,view_resouce=1014,big_icon=1031,scale=1.1,name="鲲·剑客",},
{zone=1,index=4,boss_id=11746,scene_id=6171,x_pos=114,y_pos=45,view_resouce=1028,big_icon=1061,scale=0.5,name="鲲·狂狮",},
{zone=1,index=5,boss_id=11747,scene_id=6171,x_pos=182,y_pos=95,view_resouce=1024,big_icon=1009,scale=0.7,name="鲲·魔女",},
{zone=2,index=2,scene_id=6172,x_pos=72,y_pos=174,scale=0.6,},
{zone=2,index=3,boss_id=11745,scene_id=6172,x_pos=45,y_pos=94,view_resouce=1014,big_icon=1031,scale=1.1,name="鲲·剑客",},
{zone=2,index=4,boss_id=11746,scene_id=6172,x_pos=114,y_pos=45,view_resouce=1028,big_icon=1061,scale=0.5,name="鲲·狂狮",},
{zone=2,index=5,boss_id=11747,scene_id=6172,x_pos=182,y_pos=95,view_resouce=1024,big_icon=1009,scale=0.7,name="鲲·魔女",},
{zone=3,index=2,scene_id=6173,x_pos=72,y_pos=174,scale=0.6,},
{zone=3,index=3,boss_id=11745,scene_id=6173,x_pos=45,y_pos=94,view_resouce=1014,big_icon=1031,scale=1.1,name="鲲·剑客",},
{zone=3,index=4,boss_id=11746,scene_id=6173,x_pos=114,y_pos=45,view_resouce=1028,big_icon=1061,scale=0.5,name="鲲·狂狮",},
{zone=3,index=5,boss_id=11747,scene_id=6173,x_pos=182,y_pos=95,view_resouce=1024,big_icon=1009,scale=0.7,name="鲲·魔女",},
{zone=4,index=2,scene_id=6174,x_pos=72,y_pos=174,scale=0.6,},
{zone=4,index=3,boss_id=11745,scene_id=6174,x_pos=45,y_pos=94,view_resouce=1014,big_icon=1031,scale=1.1,name="鲲·剑客",},
{zone=4,index=4,boss_id=11746,scene_id=6174,x_pos=114,y_pos=45,view_resouce=1028,big_icon=1061,scale=0.5,name="鲲·狂狮",},
{zone=4,index=5,boss_id=11747,scene_id=6174,x_pos=182,y_pos=95,view_resouce=1024,big_icon=1009,scale=0.7,name="鲲·魔女",}},

smallmonster_info={
{boss_id=11724,name="鲲·恶灵",},
{index=2,boss_id=11725,x_pos=76,y_pos=64,name="鲲·树妖",},
{index=3,boss_id=11726,x_pos=53,y_pos=135,name="鲲·魔狼",},
{index=4,boss_id=11727,x_pos=113,y_pos=180,name="鲲·刀锋",},
{index=5,boss_id=11728,x_pos=174,y_pos=136,name="鲲·天兵",},
{zone=1,scene_id=6171,},
{zone=1,index=2,boss_id=11730,scene_id=6171,x_pos=76,y_pos=64,name="鲲·狼王",},
{zone=1,index=3,boss_id=11731,scene_id=6171,x_pos=53,y_pos=135,name="鲲·白妖",},
{zone=1,index=4,boss_id=11732,scene_id=6171,x_pos=113,y_pos=180,name="鲲·灵妖",},
{zone=1,index=5,boss_id=11733,scene_id=6171,x_pos=174,y_pos=136,name="鲲·牛头",},
{zone=2,scene_id=6172,},
{zone=2,index=2,boss_id=11730,scene_id=6172,x_pos=76,y_pos=64,name="鲲·狼王",},
{zone=2,index=3,boss_id=11731,scene_id=6172,x_pos=53,y_pos=135,name="鲲·白妖",},
{zone=2,index=4,boss_id=11732,scene_id=6172,x_pos=113,y_pos=180,name="鲲·灵妖",},
{zone=2,index=5,boss_id=11733,scene_id=6172,x_pos=174,y_pos=136,name="鲲·牛头",},
{zone=3,scene_id=6173,},
{zone=3,index=2,boss_id=11730,scene_id=6173,x_pos=76,y_pos=64,name="鲲·狼王",},
{zone=3,index=3,boss_id=11731,scene_id=6173,x_pos=53,y_pos=135,name="鲲·白妖",},
{zone=3,index=4,boss_id=11732,scene_id=6173,x_pos=113,y_pos=180,name="鲲·灵妖",},
{zone=3,index=5,boss_id=11733,scene_id=6173,x_pos=174,y_pos=136,name="鲲·牛头",},
{zone=4,scene_id=6174,},
{zone=4,index=2,boss_id=11730,scene_id=6174,x_pos=76,y_pos=64,name="鲲·狼王",},
{zone=4,index=3,boss_id=11731,scene_id=6174,x_pos=53,y_pos=135,name="鲲·白妖",},
{zone=4,index=4,boss_id=11732,scene_id=6174,x_pos=113,y_pos=180,name="鲲·灵妖",},
{zone=4,index=5,boss_id=11733,scene_id=6174,x_pos=174,y_pos=136,name="鲲·牛头",}},

hurt_rank_reward={
{boss_id=11738,},
{reward_item={[0]=item_table[5]},boss_id=11748,},
{reward_item={[0]=item_table[6]},boss_id=11749,},
{reward_item={[0]=item_table[7]},boss_id=11750,},
{reward_item={[0]=item_table[8]},boss_id=11751,},
{reward_item={[0]=item_table[9]},zone=1,boss_id=11734,},
{reward_item={[0]=item_table[10]},zone=1,},
{reward_item={[0]=item_table[11]},zone=1,boss_id=11745,},
{reward_item={[0]=item_table[12]},zone=1,boss_id=11746,},
{reward_item={[0]=item_table[13]},zone=1,boss_id=11747,},
{reward_item={[0]=item_table[14]},zone=2,boss_id=11735,},
{reward_item={[0]=item_table[15]},zone=2,},
{reward_item={[0]=item_table[16]},zone=2,boss_id=11745,},
{reward_item={[0]=item_table[17]},zone=2,boss_id=11746,},
{reward_item={[0]=item_table[18]},zone=2,boss_id=11747,},
{reward_item={[0]=item_table[19]},zone=3,boss_id=11736,},
{reward_item={[0]=item_table[20]},zone=3,},
{reward_item={[0]=item_table[21]},zone=3,boss_id=11745,},
{reward_item={[0]=item_table[22]},zone=3,boss_id=11746,},
{reward_item={[0]=item_table[23]},zone=3,boss_id=11747,},
{reward_item={[0]=item_table[24]},zone=4,boss_id=11737,},
{reward_item={[0]=item_table[25]},zone=4,},
{reward_item={[0]=item_table[26]},zone=4,boss_id=11745,},
{reward_item={[0]=item_table[27]},zone=4,boss_id=11746,},
{reward_item={[0]=item_table[28]},zone=4,boss_id=11747,}},

monster_attribute={
{},
{level=2,attribute=200,},
{level=3,attribute=300,},
{level=4,attribute=400,},
{level=5,attribute=500,},
{level=6,attribute=600,},
{level=7,attribute=700,},
{level=8,attribute=800,},
{level=9,attribute=900,},
{level=10,attribute=1000,},
{level=11,attribute=1100,},
{level=12,attribute=1200,},
{level=13,attribute=1300,},
{level=14,attribute=1400,},
{level=15,attribute=1500,},
{level=16,attribute=1600,},
{level=17,attribute=1700,},
{level=18,attribute=1800,},
{level=19,attribute=1900,},
{level=20,attribute=2000,},
{level=21,attribute=2100,},
{level=22,attribute=2200,},
{level=23,attribute=2300,},
{level=24,attribute=2400,},
{level=25,attribute=2500,},
{level=26,attribute=2600,},
{level=27,attribute=2700,},
{level=28,attribute=2800,},
{level=29,attribute=2900,},
{level=30,attribute=3000,},
{level=31,attribute=3100,},
{level=32,attribute=3200,},
{level=33,attribute=3300,},
{level=34,attribute=3400,},
{level=35,attribute=3500,},
{level=36,attribute=3600,},
{level=37,attribute=3700,},
{level=38,attribute=3800,},
{level=39,attribute=3900,},
{level=40,attribute=4000,},
{level=41,attribute=4100,},
{level=42,attribute=4200,},
{level=43,attribute=4300,},
{level=44,attribute=4400,},
{level=45,attribute=4500,},
{level=46,attribute=4600,},
{level=47,attribute=4700,},
{level=48,attribute=4800,},
{level=49,attribute=4900,},
{level=50,attribute=5000,},
{level=51,attribute=5100,},
{level=52,attribute=5200,},
{level=53,attribute=5300,},
{level=54,attribute=5400,},
{level=55,attribute=5500,},
{level=56,attribute=5600,},
{level=57,attribute=5700,},
{level=58,attribute=5800,},
{level=59,attribute=5900,},
{level=60,attribute=6000,},
{level=61,attribute=6100,},
{level=62,attribute=6200,},
{level=63,attribute=6300,},
{level=64,attribute=6400,},
{level=65,attribute=6500,},
{level=66,attribute=6600,},
{level=67,attribute=6700,},
{level=68,attribute=6800,},
{level=69,attribute=6900,},
{level=70,attribute=7000,},
{level=71,attribute=7100,},
{level=72,attribute=7200,},
{level=73,attribute=7300,},
{level=74,attribute=7400,},
{level=75,attribute=7500,},
{level=76,attribute=7600,},
{level=77,attribute=7700,},
{level=78,attribute=7800,},
{level=79,attribute=7900,},
{level=80,attribute=8000,},
{level=81,attribute=8100,},
{level=82,attribute=8200,},
{level=83,attribute=8300,},
{level=84,attribute=8400,},
{level=85,attribute=8500,},
{level=86,attribute=8600,},
{level=87,attribute=8700,},
{level=88,attribute=8800,},
{level=89,attribute=8900,},
{level=90,attribute=9000,},
{level=91,attribute=9100,},
{level=92,attribute=9200,},
{level=93,attribute=9300,},
{level=94,attribute=9400,},
{level=95,attribute=9500,},
{level=96,attribute=9600,},
{level=97,attribute=9700,},
{level=98,attribute=9800,},
{level=99,attribute=9900,},
{level=100,attribute=10000,},
{level=101,attribute=10100,},
{level=102,attribute=10200,},
{level=103,attribute=10300,},
{level=104,attribute=10400,},
{level=105,attribute=10500,},
{level=106,attribute=10600,},
{level=107,attribute=10700,},
{level=108,attribute=10800,},
{level=109,attribute=10900,},
{level=110,attribute=11000,},
{level=111,attribute=11100,},
{level=112,attribute=11200,},
{level=113,attribute=11300,},
{level=114,attribute=11400,},
{level=115,attribute=11500,},
{level=116,attribute=11600,},
{level=117,attribute=11700,},
{level=118,attribute=11800,},
{level=119,attribute=11900,},
{level=120,attribute=12000,},
{level=121,attribute=12100,},
{level=122,attribute=12200,},
{level=123,attribute=12300,},
{level=124,attribute=12400,},
{level=125,attribute=12500,},
{level=126,attribute=12600,},
{level=127,attribute=12700,},
{level=128,attribute=12800,},
{level=129,attribute=12900,},
{level=130,attribute=13000,},
{level=131,attribute=13100,},
{level=132,attribute=13200,},
{level=133,attribute=13300,},
{level=134,attribute=13400,},
{level=135,attribute=13500,},
{level=136,attribute=13600,},
{level=137,attribute=13700,},
{level=138,attribute=13800,},
{level=139,attribute=13900,},
{level=140,attribute=14000,},
{level=141,attribute=14100,},
{level=142,attribute=14200,},
{level=143,attribute=14300,},
{level=144,attribute=14400,},
{level=145,attribute=14500,},
{level=146,attribute=14600,},
{level=147,attribute=14700,},
{level=148,attribute=14800,},
{level=149,attribute=14900,},
{level=150,attribute=15000,},
{level=151,attribute=15100,},
{level=152,attribute=15200,},
{level=153,attribute=15300,},
{level=154,attribute=15400,},
{level=155,attribute=15500,},
{level=156,attribute=15600,},
{level=157,attribute=15700,},
{level=158,attribute=15800,},
{level=159,attribute=15900,},
{level=160,attribute=16000,},
{level=161,attribute=16100,},
{level=162,attribute=16200,},
{level=163,attribute=16300,},
{level=164,attribute=16400,},
{level=165,attribute=16500,},
{level=166,attribute=16600,},
{level=167,attribute=16700,},
{level=168,attribute=16800,},
{level=169,attribute=16900,},
{level=170,attribute=17000,},
{level=171,attribute=17100,},
{level=172,attribute=17200,},
{level=173,attribute=17300,},
{level=174,attribute=17400,},
{level=175,attribute=17500,},
{level=176,attribute=17600,},
{level=177,attribute=17700,},
{level=178,attribute=17800,},
{level=179,attribute=17900,},
{level=180,attribute=18000,},
{level=181,attribute=18100,},
{level=182,attribute=18200,},
{level=183,attribute=18300,},
{level=184,attribute=18400,},
{level=185,attribute=18500,},
{level=186,attribute=18600,},
{level=187,attribute=18700,},
{level=188,attribute=18800,},
{level=189,attribute=18900,},
{level=190,attribute=19000,},
{level=191,attribute=19100,},
{level=192,attribute=19200,},
{level=193,attribute=19300,},
{level=194,attribute=19400,},
{level=195,attribute=19500,},
{level=196,attribute=19600,},
{level=197,attribute=19700,},
{level=198,attribute=19800,},
{level=199,attribute=19900,},
{level=200,attribute=20000,},
{level=201,attribute=20100,},
{level=202,attribute=20200,},
{level=203,attribute=20300,},
{level=204,attribute=20400,},
{level=205,attribute=20500,},
{level=206,attribute=20600,},
{level=207,attribute=20700,},
{level=208,attribute=20800,},
{level=209,attribute=20900,},
{level=210,attribute=21000,},
{level=211,attribute=21100,},
{level=212,attribute=21200,},
{level=213,attribute=21300,},
{level=214,attribute=21400,},
{level=215,attribute=21500,},
{level=216,attribute=21600,},
{level=217,attribute=21700,},
{level=218,attribute=21800,},
{level=219,attribute=21900,},
{level=220,attribute=22000,},
{level=221,attribute=22100,},
{level=222,attribute=22200,},
{level=223,attribute=22300,},
{level=224,attribute=22400,},
{level=225,attribute=22500,},
{level=226,attribute=22600,},
{level=227,attribute=22700,},
{level=228,attribute=22800,},
{level=229,attribute=22900,},
{level=230,attribute=23000,},
{level=231,attribute=23100,},
{level=232,attribute=23200,},
{level=233,attribute=23300,},
{level=234,attribute=23400,},
{level=235,attribute=23500,},
{level=236,attribute=23600,},
{level=237,attribute=23700,},
{level=238,attribute=23800,},
{level=239,attribute=23900,},
{level=240,attribute=24000,},
{level=241,attribute=24100,},
{level=242,attribute=24200,},
{level=243,attribute=24300,},
{level=244,attribute=24400,},
{level=245,attribute=24500,},
{level=246,attribute=24600,},
{level=247,attribute=24700,},
{level=248,attribute=24800,},
{level=249,attribute=24900,},
{level=250,attribute=25000,},
{level=251,attribute=25100,},
{level=252,attribute=25200,},
{level=253,attribute=25300,},
{level=254,attribute=25400,},
{level=255,attribute=25500,},
{level=256,attribute=25600,},
{level=257,attribute=25700,},
{level=258,attribute=25800,},
{level=259,attribute=25900,},
{level=260,attribute=26000,},
{level=261,attribute=26100,},
{level=262,attribute=26200,},
{level=263,attribute=26300,},
{level=264,attribute=26400,},
{level=265,attribute=26500,},
{level=266,attribute=26600,},
{level=267,attribute=26700,},
{level=268,attribute=26800,},
{level=269,attribute=26900,},
{level=270,attribute=27000,},
{level=271,attribute=27100,},
{level=272,attribute=27200,},
{level=273,attribute=27300,},
{level=274,attribute=27400,},
{level=275,attribute=27500,},
{level=276,attribute=27600,},
{level=277,attribute=27700,},
{level=278,attribute=27800,},
{level=279,attribute=27900,},
{level=280,attribute=28000,},
{level=281,attribute=28100,},
{level=282,attribute=28200,},
{level=283,attribute=28300,},
{level=284,attribute=28400,},
{level=285,attribute=28500,},
{level=286,attribute=28600,},
{level=287,attribute=28700,},
{level=288,attribute=28800,},
{level=289,attribute=28900,},
{level=290,attribute=29000,},
{level=291,attribute=29100,},
{level=292,attribute=29200,},
{level=293,attribute=29300,},
{level=294,attribute=29400,},
{level=295,attribute=29500,},
{level=296,attribute=29600,},
{level=297,attribute=29700,},
{level=298,attribute=29800,},
{level=299,attribute=29900,},
{level=300,attribute=30000,},
{level=301,attribute=30100,},
{level=302,attribute=30200,},
{level=303,attribute=30300,},
{level=304,attribute=30400,},
{level=305,attribute=30500,},
{level=306,attribute=30600,},
{level=307,attribute=30700,},
{level=308,attribute=30800,},
{level=309,attribute=30900,},
{level=310,attribute=31000,},
{level=311,attribute=31100,},
{level=312,attribute=31200,},
{level=313,attribute=31300,},
{level=314,attribute=31400,},
{level=315,attribute=31500,},
{level=316,attribute=31600,},
{level=317,attribute=31700,},
{level=318,attribute=31800,},
{level=319,attribute=31900,},
{level=320,attribute=32000,},
{level=321,attribute=32100,},
{level=322,attribute=32200,},
{level=323,attribute=32300,},
{level=324,attribute=32400,},
{level=325,attribute=32500,},
{level=326,attribute=32600,},
{level=327,attribute=32700,},
{level=328,attribute=32800,},
{level=329,attribute=32900,},
{level=330,attribute=33000,},
{level=331,attribute=33100,},
{level=332,attribute=33200,},
{level=333,attribute=33300,},
{level=334,attribute=33400,},
{level=335,attribute=33500,},
{level=336,attribute=33600,},
{level=337,attribute=33700,},
{level=338,attribute=33800,},
{level=339,attribute=33900,},
{level=340,attribute=34000,},
{level=341,attribute=34100,},
{level=342,attribute=34200,},
{level=343,attribute=34300,},
{level=344,attribute=34400,},
{level=345,attribute=34500,},
{level=346,attribute=34600,},
{level=347,attribute=34700,},
{level=348,attribute=34800,},
{level=349,attribute=34900,},
{level=350,attribute=35000,},
{level=351,attribute=35100,},
{level=352,attribute=35200,},
{level=353,attribute=35300,},
{level=354,attribute=35400,},
{level=355,attribute=35500,},
{level=356,attribute=35600,},
{level=357,attribute=35700,},
{level=358,attribute=35800,},
{level=359,attribute=35900,},
{level=360,attribute=36000,},
{level=361,attribute=36100,},
{level=362,attribute=36200,},
{level=363,attribute=36300,},
{level=364,attribute=36400,},
{level=365,attribute=36500,},
{level=366,attribute=36600,},
{level=367,attribute=36700,},
{level=368,attribute=36800,},
{level=369,attribute=36900,},
{level=370,attribute=37000,},
{level=371,attribute=37100,},
{level=372,attribute=37200,},
{level=373,attribute=37300,},
{level=374,attribute=37400,},
{level=375,attribute=37500,},
{level=376,attribute=37600,},
{level=377,attribute=37700,},
{level=378,attribute=37800,},
{level=379,attribute=37900,},
{level=380,attribute=38000,},
{level=381,attribute=38100,},
{level=382,attribute=38200,},
{level=383,attribute=38300,},
{level=384,attribute=38400,},
{level=385,attribute=38500,},
{level=386,attribute=38600,},
{level=387,attribute=38700,},
{level=388,attribute=38800,},
{level=389,attribute=38900,},
{level=390,attribute=39000,},
{level=391,attribute=39100,},
{level=392,attribute=39200,},
{level=393,attribute=39300,},
{level=394,attribute=39400,},
{level=395,attribute=39500,},
{level=396,attribute=39600,},
{level=397,attribute=39700,},
{level=398,attribute=39800,},
{level=399,attribute=39900,},
{level=400,attribute=40000,},
{level=401,attribute=40100,},
{level=402,attribute=40200,},
{level=403,attribute=40300,},
{level=404,attribute=40400,},
{level=405,attribute=40500,},
{level=406,attribute=40600,},
{level=407,attribute=40700,},
{level=408,attribute=40800,},
{level=409,attribute=40900,},
{level=410,attribute=41000,},
{level=411,attribute=41100,},
{level=412,attribute=41200,},
{level=413,attribute=41300,},
{level=414,attribute=41400,},
{level=415,attribute=41500,},
{level=416,attribute=41600,},
{level=417,attribute=41700,},
{level=418,attribute=41800,},
{level=419,attribute=41900,},
{level=420,attribute=42000,},
{level=421,attribute=42100,},
{level=422,attribute=42200,},
{level=423,attribute=42300,},
{level=424,attribute=42400,},
{level=425,attribute=42500,},
{level=426,attribute=42600,},
{level=427,attribute=42700,},
{level=428,attribute=42800,},
{level=429,attribute=42900,},
{level=430,attribute=43000,},
{level=431,attribute=43100,},
{level=432,attribute=43200,},
{level=433,attribute=43300,},
{level=434,attribute=43400,},
{level=435,attribute=43500,},
{level=436,attribute=43600,},
{level=437,attribute=43700,},
{level=438,attribute=43800,},
{level=439,attribute=43900,},
{level=440,attribute=44000,},
{level=441,attribute=44100,},
{level=442,attribute=44200,},
{level=443,attribute=44300,},
{level=444,attribute=44400,},
{level=445,attribute=44500,},
{level=446,attribute=44600,},
{level=447,attribute=44700,},
{level=448,attribute=44800,},
{level=449,attribute=44900,},
{level=450,attribute=45000,},
{level=451,attribute=45100,},
{level=452,attribute=45200,},
{level=453,attribute=45300,},
{level=454,attribute=45400,},
{level=455,attribute=45500,},
{level=456,attribute=45600,},
{level=457,attribute=45700,},
{level=458,attribute=45800,},
{level=459,attribute=45900,},
{level=460,attribute=46000,},
{level=461,attribute=46100,},
{level=462,attribute=46200,},
{level=463,attribute=46300,},
{level=464,attribute=46400,},
{level=465,attribute=46500,},
{level=466,attribute=46600,},
{level=467,attribute=46700,},
{level=468,attribute=46800,},
{level=469,attribute=46900,},
{level=470,attribute=47000,}},

hurt_rank_score={
{},
{min_rank=1,max_rank=1,add_score=400,},
{min_rank=2,max_rank=2,add_score=300,},
{min_rank=3,max_rank=9999,add_score=200,}},

score_reward={
{},
{seq=1,score=1000,},
{seq=2,score=1500,},
{seq=3,score=2000,},
{seq=4,score=3000,reward_item={[0]=item_table[29]},}},

other_default_table={is_open=1,open_level=540,enter_time_limit_s=240,refresh_zone_boss_s=240,relive_need_gold=20,gather_time_s=15,gather_times=1,kill_minor_zone_title_item=item_table[30],kill_main_zone_title_item=item_table[31],portal_id=221,portal_time_s=0,effective_range=20,reward_item={[0]=item_table[32],[1]=item_table[33]},dec="巨鲲苏醒时间:每周一、三、五晚上20:30-20:50\n\n活动形式:跨服仙盟活动\n\n等级限制:飞升170级开启\n\n活动描述:本活动为跨服仙盟战场，消灭区域中的巨鲲可获得生肖碎片等珍贵物品",total_time=1200,add_score_interval=10,add_score_once=20,kill_add_score=20,assist_add_score=10,kill_monster_score=5,},

activity_open_time_default_table={},

zone_default_table={zone=0,scene_id=6170,boss_id_0=11738,boss_id_1=11744,boss_id_2=11745,boss_id_3=11746,boss_id_4=11747,boss_pos_0="155,173",boss_pos_1="72,174",boss_pos_2="45,94",boss_pos_3="114,45",boss_pos_4="182,95",},

reward_pos_default_table={zone=0,boss_id=11744,gather_id=212,},

reward_default_table={gather_id=211,gather_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[34]},suipian_item=item_table[35],suipian_drop_rate=0,reward_item=item_table[36],reward_drop_rate=0,},

cross_boss_information_default_table={zone=0,index=1,boss_id=11744,scene_id=6170,x_pos=155,y_pos=173,drop_item_list="23595|23623",treasure_item_list="27620|26345",view_resouce=1022,adjust_x=0,adjust_height=-60,big_icon=1016,scale=0.35,name="鲲·玄月",},

smallmonster_info_default_table={zone=0,index=1,boss_id=11729,scene_id=6170,x_pos=151,y_pos=65,name="鲲·兽王",},

hurt_rank_reward_default_table={reward_item={[0]=item_table[37]},zone=0,boss_id=11744,},

monster_attribute_default_table={level=1,attribute=100,},

hurt_rank_score_default_table={min_rank=0,max_rank=0,add_score=500,},

score_reward_default_table={seq=0,score=500,reward_item={[0]=item_table[38]},}

}

