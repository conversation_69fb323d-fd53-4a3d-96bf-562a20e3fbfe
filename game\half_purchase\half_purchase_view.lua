HalfPurchaseView = HalfPurchaseView or BaseClass(SafeBaseView)

function HalfPurchaseView:__init()
	self:SetMaskBg(false, true)
	local bundle = "uis/view/half_purchase_ui_prefab"
	self:AddViewResource(0, bundle, "layout_half_purchase")
end

function HalfPurchaseView:__delete()

end

function HalfPurchaseView:ReleaseCallBack()
	if self.barrage_area then
		self.barrage_area:DeleteMe()
		self.barrage_area = nil
	end
end

function HalfPurchaseView:LoadCallBack()
	if not self.barrage_area then
		self.barrage_area = RegionalBarrage.New(self.node_list.barrage_area)
		self.barrage_area:SetGetDanMuInfoFunc(BindTool.Bind(function ()
			return HalfPurchaseWGData.Instance:GetBarrageMessage()
		end, self))
	end

    XUI.AddClickEventListener(self.node_list.rule_btn, BindTool.Bind(self.OnClickRuleBtn, self))
    XUI.AddClickEventListener(self.node_list.recharge_btn, BindTool.Bind(self.OnClickRechargeBtn, self))
end

function HalfPurchaseView:OnFlush()
	if self.barrage_area then
		self.barrage_area:StartShowDanMu()
	end
end

function HalfPurchaseView:OnClickRuleBtn()
	local view_rule = ViewRuleWGData.Instance:GetViewRuleCfg(self.view_name, 0)

	if view_rule then
		local rule_tip = RuleTip.Instance
		local rule_title = view_rule.rule_title
		local rule_content = view_rule.rule_content

		rule_tip:SetTitle(rule_title)
		rule_tip:SetContent(rule_content, nil, nil, nil, true)
	end
end

function HalfPurchaseView:OnClickRechargeBtn()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
	self:Close()
end