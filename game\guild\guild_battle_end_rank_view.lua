
GuildBattleEndRankView = GuildBattleEndRankView or BaseClass(SafeBaseView)

function GuildBattleEndRankView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
end

function GuildBattleEndRankView:LoadConfig()
	-- self:AddViewResource(0, "uis/view/guild_battle_ranked_ui_prefab", "layout_guild_battle_finish")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/guild_battle_new_ui_prefab", "layout_guild_battle_new_finish")
end

function GuildBattleEndRankView:__delete()
end

function GuildBattleEndRankView:LoadCallBack()
	-- for i = 1, 5 do
	-- 	self.node_list["lbl_" .. i].text.text = Language.GuildBattleRanked.End_Rank_Name[i]
	-- end
	-- self.node_list.other_title_lbl:SetActive(true)
	self.node_list.lbl_btn_quit.text.text = Language.GuildBattleRanked.End_Rank_Btn_Name
	XUI.AddClickEventListener(self.node_list.btn_quit_scene, BindTool.Bind1(self.Close, self))
	if not self.guidle_end_rank_list then
		self.guidle_end_rank_list = AsyncListView.New(GuildBattleEndRankRender,self.node_list.ph_result_list)
	end
	self.node_list.victory:SetActive(self.is_win == 1 )
	self.node_list.lose:SetActive(self.is_win == 0 )
	self.node_list.title_bg_loss:SetActive(self.is_win == 0 )
	self.node_list.ph_result_bg_loss:SetActive(self.is_win == 0 )
	self.node_list.title_bg_win:SetActive(self.is_win == 1 )
	self.node_list.ph_result_bg_win:SetActive(self.is_win == 1 )
	self.node_list.text_win:SetActive(self.is_win == 1 )
	self.node_list.text_loss:SetActive(self.is_win == 0 )

	self:CreateItemCell()
end

function GuildBattleEndRankView:ReleaseCallBack()
	if self.guidle_end_rank_list then
		self.guidle_end_rank_list:DeleteMe()
		self.guidle_end_rank_list = nil
	end
	if self.show_my_item then
		self.show_my_item:DeleteMe()
		self.show_my_item = nil
	end
	if CountDownManager.Instance:HasCountDown("guild_battle_end_rank") then
		CountDownManager.Instance:RemoveCountDown("guild_battle_end_rank")
	end
	self.data_list = nil
	self.is_win = nil
	if self.self_reward_list  then
		self.self_reward_list:DeleteMe()
		self.self_reward_list = nil
	end
end

function GuildBattleEndRankView:CreateItemCell()
	-- local cell = {}
	-- for i=1,5 do
	-- 	cell[i] = ItemCell.New(self.node_list["ph_cell_"..i])
	-- end
	if nil == self.data_list then
		return
	end

	local ranknum = 0
	local role_id = RoleWGData.Instance:GetUUid()
	local mine_info
	for k,v in pairs(self.data_list) do
		if v.user_id == role_id then
			ranknum = v.rank
			mine_info = v
			break
		end
	end
	if mine_info then
		self.node_list.label_rank.text.text = mine_info.rank
		--self.node_list.panel_rank_round_2.text.text = mine_info.rank
		local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
		local name = mine_info.user_name
		local name_list = Split(mine_info.user_name, "_")
		if not is_cross_server_stage and not IsEmptyTable(name_list) then
			name = name_list[1]
		end
		
		self.node_list.label_name.text.text = name
		-- self.node_list.label_kill.text.text = mine_info.kill_role_num
		-- self.node_list.label_assist.text.text = mine_info.be_kill_times
		self.node_list.label_jifen.text.text = mine_info.total_score
		-- self.node_list.label_gongxun.text.text = mine_info.destroy_tower_num
		self.node_list.label_rank:SetActive(mine_info.rank > 3)
		--self.node_list.panel_rank_round_2:SetActive(mine_info.rank > 3)
		self.node_list.guild_battle_img_mvp:SetActive(mine_info.rank <= 3)
		-- self.node_list.rank_image_round_2:SetActive(mine_info.rank <= 3)
		-- --前三名
		if mine_info.rank <= 3 then
			local bundel,asset = ResPath.GetCommonIcon("a3_tb_jp" .. mine_info.rank)
			self.node_list.guild_battle_img_mvp.image:LoadSprite(bundel,asset,function ()
				self.node_list.guild_battle_img_mvp.image:SetNativeSize()
		end)
		--self.node_list.rank_image_round_2.image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_" .. mine_info.rank))
		end
	end
	local zone = GuildWGData.Instance:GetGuildCurZone() or 0
	local data_list = GuildBattleRankedWGData.Instance:GetGuildReward(ranknum,self.is_win,zone)

	local reward_data = {}
	reward_data = SortDataByItemColor(data_list)
	-- local info = GuildBattleRankedWGData.Instance:GetGuildRankPaiMingInfo()
	self.node_list.ph_result_item_group:SetActive(true)
	--self.node_list.guild_group:SetActive(false)

	-- local fight_state = GuildWGData.Instance:GetCurGuildWarState()
	-- if info and fight_state >= GuildWGData.GuildWarState.TwoState then
	-- 	ranknum = info.guild_rank - 1
	-- 	reward_data = info.rank_reward_list
	-- 	self.node_list.ph_result_item_group:SetActive(false)
	-- 	self.node_list.guild_group:SetActive(true)
	-- end

	--self.node_list.panel_rank:SetActive(ranknum + 1 > 3)
	-- self.node_list.rank_image:SetActive(ranknum + 1 <= 3)
	-- if (ranknum + 1) <= 3 then
	-- 	self.node_list.rank_image.image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_" .. (ranknum + 1)))
	-- 	--self.node_list.mvp_text.text.text = ""
	-- end
	--self.node_list.panel_rank.text.text = ranknum + 1
	if nil == self.self_reward_list then
		 self.self_reward_list = AsyncListView.New(RewardPreviousCell,self.node_list.reward_list_1)
	end
	 self.self_reward_list:SetDataList(reward_data)
end

function GuildBattleEndRankView:ShowIndexCallBack(index)
	self:Flush()
end

function GuildBattleEndRankView:OnFlush(param_t, index)
	local info = GuildBattleRankedWGData.Instance:GetGuildRankPaiMingInfo()
	local data_list = {}
	local ranknum = 0
	local fight_state = GuildWGData.Instance:GetCurGuildWarState()
	if info and fight_state >= GuildWGData.GuildWarState.TwoState then
		ranknum = info.guild_rank - 1
		data_list = info.rank_reward_list
		self.node_list.ph_result_item_group:SetActive(false)
		--self.node_list.guild_group:SetActive(true)
		--self.node_list.panel_rank:SetActive(ranknum + 1 > 3)
		-- self.node_list.rank_image:SetActive(ranknum + 1 <= 3)
		-- if (ranknum + 1) <= 3 then
		-- 	self.node_list.rank_image.image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_" .. (ranknum + 1)))

		-- end

		if nil == self.self_reward_list then
			 self.self_reward_list = AsyncListView.New(RewardPreviousCell,self.node_list.reward_list_1)
		end
		 self.self_reward_list:SetDataList(data_list)
	end

	if self.data_list ~= nil then
		self.guidle_end_rank_list:SetDataList(self.data_list)
	end
	
	local guild_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneGlobalInfo()
	if not IsEmptyTable(guild_info_list) then
		self.node_list["guild_name1"].text.text = guild_info_list[0].guild_name
		self.node_list["guild_name2"].text.text = guild_info_list[1].guild_name
		self.node_list["xianling_value1"].text.text = string.format(Language.GuildBattleRanked.EndXianLingVlaue,ToColorStr(guild_info_list[0].total_score,COLOR3B.D_GREEN))
		self.node_list["xianling_value2"].text.text = string.format(Language.GuildBattleRanked.EndXianLingVlaue,ToColorStr(guild_info_list[1].total_score,COLOR3B.D_GREEN))
	end
end

function GuildBattleEndRankView:SetDataList(data_list, is_win)
	self.data_list = data_list
	for k,v in pairs(self.data_list) do
		v.is_win = is_win
	end
	self.is_win = is_win
end

function GuildBattleEndRankView:CloseCallBack()
	--FuBenWGCtrl.Instance:SendLeaveFB()
	local info = GuildBattleRankedWGData.Instance:GetGuildBattleEndKeepWinInfo()
	if info then
		local reward_info = GuildBattleRankedWGData.Instance:GetGuildRoleOffsetCfg(info.is_shut_down, info.keep_win_times)
		local fight_state = GuildWGData.Instance:GetCurGuildWarState()
		if reward_info and fight_state == GuildWGData.GuildWarState.TwoState then
			GuildWGCtrl.Instance:OpenLisnShengPasnel()
		else
			FuBenWGCtrl.Instance:SendLeaveFB()
		end
	else
		FuBenWGCtrl.Instance:SendLeaveFB()

	end
	--清除第二轮排名信息
	GuildBattleRankedWGData.Instance:ClearGuildRankPaiMingInfo()
end

-- <GuildBattleEndRankRender> ------------------------------------------------
GuildBattleEndRankRender = GuildBattleEndRankRender or BaseClass(BaseRender)
function GuildBattleEndRankRender:__init()

end

function GuildBattleEndRankRender:__delete()
	if self.self_reward_list  then
		self.self_reward_list:DeleteMe()
		self.self_reward_list = nil
	end
end

function GuildBattleEndRankRender:OnFlush()
	local data = self.data
	if data == nil then return end
	if nil == self.self_reward_list then
		 self.self_reward_list = AsyncListView.New(RewardPreviousCell,self.node_list.reward_list)
	end
	local zone = GuildWGData.Instance:GetGuildCurZone() or 0
	local data_list = GuildBattleRankedWGData.Instance:GetGuildReward(data.rank,self.data.is_win,zone)
	local reward_data = {}
	-- for i=0,#data_list do
	-- 	table.insert(reward_data,data_list[i])
	-- end
	reward_data = SortDataByItemColor(data_list)
	self.self_reward_list:SetDataList(reward_data)
	self.node_list.label_rank.text.text = (data.rank)
	local role_id = RoleWGData.Instance:GetUUid()
	local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
	local name = data.user_name
	local name_list = Split(data.user_name, "_")
	if not is_cross_server_stage and not IsEmptyTable(name_list) then
		name = name_list[1]
	end

	if self.data.user_id == role_id then
		self.node_list.label_name.text.text = string.format(Language.Guild.GuildMySelfNameColor, name)
	else
		self.node_list.label_name.text.text = (name)
	end
	-- self.node_list.label_kill.text.text = (data.kill_role_num)
	-- self.node_list.label_assist.text.text = (data.be_kill_times)
	self.node_list.label_jifen.text.text = (data.total_score)
	-- self.node_list.label_gongxun.text.text =(data.destroy_tower_num)
	self.node_list.label_rank:SetActive(data.rank > 3)
	self.node_list.img_mvp:SetActive(data.rank <= 3)

	--前三名
	if data.rank <= 3 then
		local bundel,asset = ResPath.GetCommonIcon("a3_tb_jp" .. data.rank)
		self.node_list.img_mvp.image:LoadSprite(bundel,asset,function ()
			self.node_list.img_mvp.image:SetNativeSize()
		end)
	end
end

function GuildBattleEndRankRender:CreateSelectEffect()

end


GuildBattleEndRankRewardView = GuildBattleEndRankRewardView or BaseClass(SafeBaseView)
function GuildBattleEndRankRewardView:__init()
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
end

function GuildBattleEndRankRewardView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third2_panel")
	self:AddViewResource(0, "uis/view/guild_battle_ranked_ui_prefab", "layout_guild_battle_liansheng")
end

function GuildBattleEndRankRewardView:ReleaseCallBack()
	if nil ~= self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GuildBattleEndRankRewardView:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["cell_pos"])
	local indo =  GuildBattleRankedWGData.Instance:GetGuildBattleEndKeepWinInfo()
	local reward_info = GuildBattleRankedWGData.Instance:GetGuildRoleOffsetCfg(indo.is_shut_down,indo.keep_win_times)
	--print_error(reward_info,indo.is_shut_down,indo.keep_win_times)
	local str
	if indo.is_shut_down == 1 then
		str = string.format(Language.Guild.ContinuePanel_1,indo.guild_name,indo.keep_win_times)   --"终结了【"..info.guild_name.."】的"..indo.keep_win_times.."连胜"
--		print_error(reward_info)
		if reward_info then
			self.item_cell:SetData(reward_info.defeat_reward_item[0])
		end
	else
		--str = "连胜次数："..indo.keep_win_times.."次"
		str = string.format(Language.Guild.ContinuePanel_2,indo.keep_win_times)
		--print_error(reward_info)
		if reward_info then
			self.item_cell:SetData(reward_info.win_reward_item[0])
		end
	end

	self.node_list.lbl_title.text.text = indo.is_shut_down == 1 and Language.Guild.ZhongJieReward or Language.Guild.LianShengReward
	self.node_list.desc.text.text = str
	GlobalTimerQuest:AddDelayTimer(function()
		self:Close()
	end,10)
end

function GuildBattleEndRankRewardView:ShowIndexCallBack()
	XUI.AddClickEventListener(self.node_list.btn_quit_scene,BindTool.Bind1(self.OpenPanel,self))
end

function GuildBattleEndRankRewardView:OpenPanel()
	-- ViewManager.Instance:Open(GuideModuleName.GuildWarFeiPeiView)
	ZhuZaiShenDianWGCtrl.Instance:OpenZhuZaiRewardView(5)
	self:Close()
end

function GuildBattleEndRankRewardView:CloseCallBack()
	GuildBattleRankedWGData.Instance:ResertKeepWinInfo()
	FuBenWGCtrl.Instance:SendLeaveFB()
end
