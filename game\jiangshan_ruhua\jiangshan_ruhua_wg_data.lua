JiangShanRuHuaWGData = JiangShanRuHuaWGData or BaseClass()
JiangShanRuHuaWGData.REWARD_TYPE = {
    BIG = 1,
    NORMAL = 2,
    LOW = 3
}

function JiangShanRuHuaWGData:__init()
    if JiangShanRuHuaWGData.Instance then
        ErrorLog("[JiangShanRuHuaWGData] Attemp to create a singleton twice !")
    end
    JiangShanRuHuaWGData.Instance = self

    self.fws_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_yanhua_shengdian4_auto")
    self.param_cfg = ListToMap(self.fws_cfg.config_param, "grade")
    self.grade_cfg = ListToMap(self.fws_cfg.grade, "grade")
    self.consume_cfg = ListToMap(self.fws_cfg.consume, "consume")
    self.reward_cfg = ListToMap(self.fws_cfg.reward, "reward", "reward_id")
    self.rebate_cfg = ListToMap(self.fws_cfg.rebate, "rebate", "index")
    self.role_sp_guarantee_cfg = ListToMapList(self.fws_cfg.role_sp_guarantee, "reward")
    self.model_grade_show = ListToMap(self.fws_cfg.model_show, "grade")
    self.gailv_cfg = ListToMapList(self.fws_cfg.item_random_desc, "grade")--self.fws_cfg.item_random_desc --概率展示
    RemindManager.Instance:Register(RemindName.JiangShanRuHua, BindTool.Bind(self.ShowRed, self))

    self:RemindInBag(RemindName.JiangShanRuHua)
    self.selected_draw_times = 1
    self.grade = 0
    self.skip_spine_cache = false
end

function JiangShanRuHuaWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.JiangShanRuHua)
    JiangShanRuHuaWGData.Instance = nil
end

function JiangShanRuHuaWGData:ShowRed()
    if not self:GetActIsOpen() then
        return 0
    end

    --背包有道具
    if self:GetEnoughItem() then
        return 1
    end

    --返利
    if self:GetFanliRed() then
        return 1
    end

    return 0
end

function JiangShanRuHuaWGData:GetEnoughItem()
    local item_list = self:GetItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

function JiangShanRuHuaWGData:GetFanliRed()
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1
    local draw_time = self:GetCurDrawTimes()
    for i, v in ipairs(self.rebate_cfg[rebate]) do
        if draw_time >= v.lotto_num then
            if not self:GetFanliHasGet(v.index) then
                return true
            end
        else
            return false
        end
    end
end

function JiangShanRuHuaWGData:GetActIsOpen()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_JIANG_SHAN_RU_HUA)
    if not is_open then
        return false
    end

    local cur_cfg = self:GetCurCfg()
    if not cur_cfg then
        return false
    end

    return RoleWGData.Instance:GetRoleLevel() >= cur_cfg.open_level
end

function JiangShanRuHuaWGData:GetCurCfg()
    return self.param_cfg[self.grade or 0]
end

function JiangShanRuHuaWGData:GetGrade()
    return self.grade or 0
end

function JiangShanRuHuaWGData:GetGradeCfg()
    return self.grade_cfg[self.grade or 0]
end

function JiangShanRuHuaWGData:GetConsumeItem()
    local cur_grade_cfg = self:GetGradeCfg()
    return cur_grade_cfg.consume_item
end

function JiangShanRuHuaWGData:GetConsumeCfg()
    local cfg = self:GetGradeCfg()
    return self.consume_cfg[cfg.consume]
end

function JiangShanRuHuaWGData:GetRewardById(reward_pool_id, reward_id)
    return self.reward_cfg[reward_pool_id or 1][reward_id]
end

function JiangShanRuHuaWGData:GetItemDataChangeList()
    if not self.item_data_change_list then
        self.item_data_change_list = {}
        local cfg = self:GetConsumeCfg()
        table.insert(self.item_data_change_list, cfg.cost_item_id)
    end
    return self.item_data_change_list
end

function JiangShanRuHuaWGData:CacheOrGetDrawIndex(btn_index)
    if btn_index then
        self.draw_btn_index = btn_index
    end

    return self.draw_btn_index
end

--获取选中次数
function JiangShanRuHuaWGData:GetDrawTimes()
    return self.selected_draw_times
end

--设置选中次数
function JiangShanRuHuaWGData:SetDrawTimes(draw_times)
    self.selected_draw_times = draw_times
end

--协议信息
function JiangShanRuHuaWGData:SetInfo(protocol)
    self.grade = protocol.grade
    self.cur_draw_times = protocol.person_draw_count
    self.fetch_flag = protocol.leiji_reward_fetch_flag
    self.is_skip_comic = protocol.is_skip_comic         --跳过动画？
    self.sp_guarantee_x = protocol.sp_guarantee_x       --特殊保底次数？ 弃用
    self.sp_guarantee_n = protocol.sp_guarantee_n       --特殊保底轮数？
    self.sp_enter_num = protocol.sp_enter_num           --进入保底库次数？

    self.gather_small_count = protocol.gather_small_count
    self.gather_big_count = protocol.gather_big_count
end

function JiangShanRuHuaWGData:GetCurDrawTimes()
    return self.cur_draw_times or 0
end

function JiangShanRuHuaWGData:RemindInBag(remind_name)
    local check_list = self:GetItemDataChangeList()
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, check_list, nil)
end

function JiangShanRuHuaWGData:GetFanliHasGet(index)
    return bit:_and(self.fetch_flag or 0, bit:_lshift(1, index - 1)) ~= 0
end

function JiangShanRuHuaWGData:GetFanliList(len)
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1

    local list = {}
    local max = #self.rebate_cfg[rebate]
    local len = len or 5
    local t = {}
    for i = 0, len-1 do
        t = {
            data = self.rebate_cfg[rebate][max - i],
            has_get = self:GetFanliHasGet(max - i) and 1 or 0
        }
        table.insert(list, 1, t)
    end

    local flag
    for i = max - len, 1, -1 do
        flag = self:GetFanliHasGet(i)
        if not flag then
            t = {
                data = self.rebate_cfg[rebate][i],
                has_get = 0
            }
            table.insert(list, 1, t)
            table.remove(list, len + 1)
        end
    end

    return list
end

function JiangShanRuHuaWGData:GetFanliList1()
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1
    local rebate_cfg = self.rebate_cfg[rebate]
    local draw_time = self:GetCurDrawTimes()
    local list = {}
    for i = 1, #rebate_cfg do
        local temp = {}
        if draw_time >= rebate_cfg[i].lotto_num then
            if not self:GetFanliHasGet(rebate_cfg[i].index) then
                temp.index = rebate_cfg[i].index
            else
                temp.index = 999 + rebate_cfg[i].index
            end
        else
            temp.index = 99 + rebate_cfg[i].index
        end

        temp.data = rebate_cfg[i]
        temp.has_get = self:GetFanliHasGet(rebate_cfg[i].index) and 1 or 0
        table.insert(list, temp)
    end
    
    --table.sort(list, SortTools.KeyLowerSorter("index"))
    return list
end

function JiangShanRuHuaWGData:SortDataList(data_list)
    local list = {}
    if data_list and not IsEmptyTable(data_list) then    
        for k,v in pairs(data_list) do
            local temp = {}
            temp.reward_item = v.reward_item
            if temp.reward_item and temp.reward_item.item_id and temp.reward_item.item_id > 0 then
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(temp.reward_item.item_id)       
                temp.color = item_cfg and item_cfg.color or 1
            else
                temp.color = 0
            end
            list[#list+1] = temp
        end
        table.sort(list, SortTools.KeyUpperSorters("color"))
    end
    return list
end

--已经抽到的奖励
function JiangShanRuHuaWGData:SaveDrawInfo(protocol)
    self.had_draw_list = protocol.reward_info
end

--绑定奖池和非绑奖池的展示奖励
function JiangShanRuHuaWGData:GetShowCellList()
    local show_list = {}
    local grade_cfg = self:GetGradeCfg()

    local reward_pool = grade_cfg and grade_cfg.reward_unbind or 1
    local cfg = self.reward_cfg[reward_pool]  --做展示用的奖励配置
    for i, v in ipairs(cfg) do
        table.insert(show_list, v)
    end

    return show_list
end

--日志协议
function JiangShanRuHuaWGData:SetRecord(record_list)
    self.record_list = record_list
    self.new_record_time = self.record_list[1] and self.record_list[1].draw_time or 0
end

function JiangShanRuHuaWGData:GetRecordInfo()
    local list = {}
    if not IsEmptyTable(self.record_list) then
        for k, v in pairs(self.record_list) do
            local item_data = {}
            item_data.item_id = v.item_id
            item_data.num = v.num
            v.item_data = item_data
            v.consume_time = v.draw_time
            list[k] = v
        end
    end
    
    if not IsEmptyTable(list) then
        table.sort(list, SortTools.KeyUpperSorter("draw_time"))
    end
    --获取日志
    return list
end


--获取最新日志的时间
function JiangShanRuHuaWGData:GetRecordTime()
    return self.new_record_time or 1
end

--获取奖励对应的配置
function JiangShanRuHuaWGData:CalDrawRewardList(protocol)
    local data_list = {}
    if not protocol or not protocol.count or protocol.count <= 0 then
        return data_list
    end

    local zhenxi_item = nil
    for i,v in ipairs(protocol.reward_list) do
        local cfg = self:GetRewardById(v.reward_pool_id, v.reward_id)
        if cfg then
            local temp = {}
            temp.is_zhenxi = v.is_zhenxi
            temp.reward = cfg.reward
            temp.reward_item = cfg.reward_item
            temp.reward_type = cfg.reward_type
            temp.rewrad_rare_show = cfg.rewrad_rare_show
            temp.draw_corner_type = cfg.reward_big_show
            if temp.is_zhenxi and protocol.count == 50 then
                zhenxi_item = temp
            else
                table.insert(data_list, temp)
            end
        else
            print_error("错误数据 请检查奖励配置 reward_pool_id,reward_id: ", v.reward_pool_id, v.reward_id)
        end
    end

    local temp_data_list
    if zhenxi_item then
        local zhenxi_index
        temp_data_list = {}
        if protocol.count == 50 then
            zhenxi_index = math.random(22, 28)
        end
        for k, v in ipairs(data_list) do
            if k < zhenxi_index then
                temp_data_list[k] = v
            else
                if k == zhenxi_index then
                    temp_data_list[zhenxi_index] = zhenxi_item
                    temp_data_list[k+1] = v
                else
                    temp_data_list[k+1] = v
                end
            end
        end
    end

    return temp_data_list or data_list
end

function JiangShanRuHuaWGData:GetProbabilityInfo()
    return self.gailv_cfg[self.grade or 0] or {}
end

function JiangShanRuHuaWGData:GetModelShowInfo()
    return self.model_grade_show[self.grade or 0] or {}
end

function JiangShanRuHuaWGData:SetSkipSpineStatus(is_skip)
    is_skip = is_skip or false
    self.skip_spine_cache = is_skip
end

function JiangShanRuHuaWGData:GetSkipSpineStatus()
    return self.skip_spine_cache or false
end