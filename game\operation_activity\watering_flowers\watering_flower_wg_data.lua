WateringFlowersWGData = WateringFlowersWGData or BaseClass()
WateringFlowersWGData.ConfigPath = "config/auto_new/operation_activity_water_flower_auto"

FLOWER_STATUS ={
	FLOWER_STATUS_NOTHING = 0,			-- 什么都没有
	FLOWER_STATUS_GROWING = 1,			-- 成长期
	FLOWER_STATUS_RIPE = 2,				-- 已成熟
	FLOWER_STATUS_FETCH = 3,			-- 已领取
}

function WateringFlowersWGData:__init()
	if WateringFlowersWGData.Instance then
		ErrorLog("[WateringFlowersWGData] Attemp to create a singleton twice !")
	end

	WateringFlowersWGData.Instance = self

	self:FlushWateringFlowersCfg()
	self:CreateWaterFlowerInfoList()
	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS, {[1] = OPERATION_EVENT_TYPE.LEVEL},
										BindTool.Bind(self.GetOAFlowerIsOpen, self), BindTool.Bind(self.GetWateringFlowerRemind, self))

	RemindManager.Instance:Register(RemindName.OperationWateringFlowers, BindTool.Bind(self.GetWateringFlowerRemind, self))

	self.garden_flag = 0
	self.next_invite_flag_list = {}
end

function WateringFlowersWGData:FlushWateringFlowersCfg()
	self.watering_flower_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_water_flower_auto")
end

function WateringFlowersWGData:__delete()
	WateringFlowersWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.OperationWateringFlowers)
end

function WateringFlowersWGData:CreateWaterFlowerInfoList()
	--个人信息（自己）初始化
	self.self_water_flower_info = {}
	self.self_water_flower_info.flower_list = {}
	self.self_water_flower_info.activity_start_time = 0
	self.self_water_flower_info.today_total_help_other_times = 0
	self.self_water_flower_info.today_total_help_lover_timse = 0
	self.self_water_flower_info.uid = 0
	self.self_water_flower_info.name = ""

	--个人信息（其他人）初始化
	self.other_water_flower_info = {}
	self.other_water_flower_info.flower_list = {}
	self.other_water_flower_info.activity_start_time = 0
	self.other_water_flower_info.today_total_help_other_times = 0
	self.other_water_flower_info.uid = 0
	self.other_water_flower_info.name = ""

	--我帮浇过的人信息初始化
	self.help_friend_info = {}
	self.help_friend_info.count = 0
	self.help_friend_info.help_param_list = {}

	--一些人的帮浇次数信息初始化
	self.help_me_info = {}
	self.help_me_info.count = 0
	self.help_me_info.help_me_times_list = {}

	--被某人邀请浇水信息初始化
	self.invite_help_other_ack = {}
	self.invite_help_other_ack.uid = 0
	self.invite_help_other_ack.prof = 0
	self.invite_help_other_ack.sex = 0
	self.invite_help_other_ack.name = ""

	--其他人得花盆信息列表初始化
	self.other_role_info_list = {}
	self.other_role_info_list.count = 0
	self.other_role_info_list.other_role_info = {}

	self.water_log_list = {}
	self.water_log_list.count = 0
	self.water_log_list.record_list = {}

	self.friend_list = {}

	self.member_list = {}

	self.self_total_help_times = 0

	self.temp_flower_cfg_list = {}
end

-- 9011 个人信息（自己的）下发
function WateringFlowersWGData:SetWaterFlowerInfo(protocol)
	self.self_water_flower_info.flower_list = protocol.flower_list
	self.self_water_flower_info.activity_start_time = protocol.activity_start_time
	self.self_water_flower_info.today_total_help_other_times = protocol.today_total_help_other_times
	self.self_water_flower_info.today_total_help_lover_timse = protocol.today_total_help_lover_timse
	self.self_water_flower_info.uid = protocol.uid
	self.self_water_flower_info.name = protocol.name
end

-- 9012 我帮浇过的人
function WateringFlowersWGData:SetWaterFlowerHelpFriendInfo(protocol)
	self.help_friend_info.count = protocol.count
	self.help_friend_info.help_param_list = protocol.help_param_list
end

-- 9014 下发一些人的帮浇次数
function WateringFlowersWGData:SetWaterFlowerHelpMeInfo(protocol)
	self.help_me_info.count = protocol.count
	self.help_me_info.help_me_times_list = protocol.help_me_times_list
end

-- 9015 通知被某人邀请浇水
function WateringFlowersWGData:SetWaterFlowerInviteHelpOtherAck(protocol)
	if self.other_role_info_list.other_role_info[protocol.uid] then
		self.other_role_info_list.other_role_info[protocol.uid].invite_me = protocol.type

		self:UpdateWateringFlowersFriendList()
		self:UpdateWateringFlowersMemberList()
	end
end

-- 9016 下发浇水日志
function WateringFlowersWGData:SetWaterFlowerWaterLog(protocol)
	self.water_log_list.count = protocol.count
	self.water_log_list.record_list = protocol.record_list
end

-- 9017 其他玩家(好友和盟友)盆栽信息
function WateringFlowersWGData:SetWaterFlowerOtherRoleFlower(protocol)
	self.other_role_info_list.count = protocol.count
	for i=1,protocol.count do
		self.other_role_info_list.other_role_info[protocol.other_role_info[i].uid] = {}
		self.other_role_info_list.other_role_info[protocol.other_role_info[i].uid] = protocol.other_role_info[i]
	end


	self:UpdateWateringFlowersFriendList()
	self:UpdateWateringFlowersMemberList()
end

-- 9018 个人信息（其他人）下发
function WateringFlowersWGData:SetOtherWaterFlowerInfo(protocol)
	self.other_water_flower_info.flower_list = protocol.flower_list
	self.other_water_flower_info.activity_start_time = protocol.activity_start_time
	self.other_water_flower_info.today_total_help_other_times = protocol.today_total_help_other_times
	self.other_water_flower_info.uid = protocol.uid
	self.other_water_flower_info.name = protocol.name
end

function WateringFlowersWGData:GetWaterFlowerInfo()
	return self.self_water_flower_info
end

function WateringFlowersWGData:GetWaterFlowerHelpFriendInfo()
	return self.help_friend_info
end

function WateringFlowersWGData:GetWaterFlowerHelpMeInfo()
	return self.help_me_info
end

function WateringFlowersWGData:GetWaterFlowerInviteHelpOtherAck()
	return self.invite_help_other_ack
end

function WateringFlowersWGData:GetWaterFlowerWaterLog()
	return self.water_log_list
end

function WateringFlowersWGData:GetOtherWaterFlowerInfo()
	return self.other_water_flower_info
end

function WateringFlowersWGData:GetCurGardenUid()
	if self.garden_flag == 0 then
		return self.self_water_flower_info.uid
	else
		return self.other_water_flower_info.uid
	end
end

--根据uid获取其他人的花盆信息
function WateringFlowersWGData:GetOtherRoleFlowerInfoByUid(uid)
	if self.other_role_info_list.other_role_info[uid] then
		return self.other_role_info_list.other_role_info[uid]
	end
end

--根据花盆类型获取帮我浇过水的玩家信息列表
function WateringFlowersWGData:GetHlepMeDataList(pot_type)
	local help_info = self:GetWaterFlowerHelpMeInfo()

	local data_list = {}

	if help_info.count == 0 then
		return data_list
	end

	local count = 0

	for i=1,help_info.count do
		local fetch_times = help_info.help_me_times_list[i].fetch_reward_times[pot_type]
		local help_times = help_info.help_me_times_list[i].help_me_times[pot_type]
		local times = help_times - fetch_times

		if times > 0 then
			for j=1,times do
				if count <= 4 then			--最多展示4条
					count = count + 1
					data_list[count] = {}
					data_list[count].uid = help_info.help_me_times_list[i].uid
					data_list[count].name = help_info.help_me_times_list[i].name
					
				else
					break
				end
			end
		end

		if count >= 4 then
			break
		end
	end
	return data_list
end

--获取今天剩余浇水次数（自己浇别人）
function WateringFlowersWGData:GetTodayRemainWaterTimes()

	local total_water_times = 0
	local cur_water_times = 0

	for i=1,2 do
		local pot_cfg = self:GetFlowerCfgByType(i - 1)

		if pot_cfg then
			total_water_times = total_water_times + pot_cfg.self_times

		end

		if self.self_water_flower_info.flower_list[i] then
			cur_water_times = cur_water_times + self.self_water_flower_info.flower_list[i].today_self_water_times
		end
	end

	return total_water_times - cur_water_times
end

--获取今天剩余浇水次数（自己浇别人）
function WateringFlowersWGData:GetTodayRemainWaterTimes2(cur_panel_index)
	local total_water_times = 0
	local cur_water_times = 0
	local pot_type = cur_panel_index - 1
	local pot_cfg = self:GetFlowerCfgByType(pot_type)

	if pot_cfg then
		total_water_times = pot_cfg.self_times
	end

	if self.self_water_flower_info.flower_list[cur_panel_index] then
		cur_water_times = self.self_water_flower_info.flower_list[cur_panel_index].today_self_water_times
	end

	return total_water_times - cur_water_times
end

--根据类型获取今天被别人浇水的剩余次数（别人浇自己）
function WateringFlowersWGData:GetTodayRemindBeWaterdTimesByType(type)
	local remain_times = 0
	local max_times = 0

	local pot_cfg = self:GetFlowerCfgByType(type - 1)
	if self.self_water_flower_info.flower_list[type] and pot_cfg then
		remain_times = pot_cfg.other_times - self.self_water_flower_info.flower_list[type].today_other_water_times
		max_times = pot_cfg.other_times
	end

	return remain_times, max_times
end

--根据类型获取好友今天被主角浇水的剩余次数(自己浇别人)
function WateringFlowersWGData:GetOtherTodayRemindWaterTimesByType(type)
	local remain_times = 0
	local max_times = 0

	local pot_cfg = self:GetFlowerCfgByType(type - 1)
	if self.other_water_flower_info.flower_list[type] and pot_cfg then
		remain_times = pot_cfg.other_times - self.other_water_flower_info.flower_list[type].today_other_water_times
		max_times = pot_cfg.other_times
	end

	-- 剩余次数，最大次数，已浇次数
	return remain_times, max_times
end

-- A代指本人，B指好友1，C指好友2
--浇水按钮显示-- A 剩余协助他人浇水次数/剩余协助他人浇水总次数
function WateringFlowersWGData:GetOtherTodayRemindWaterTimesByTypeTwo(type)
	local remain_times = 0
	local max_times = 0
 
	local pot_cfg = self:GetFlowerCfgByType(type - 1)
	if self.self_water_flower_info and pot_cfg then
		--已浇水次数
		local watered_times = type == 1 and self.self_water_flower_info.today_total_help_other_times or self.self_water_flower_info.today_total_help_lover_timse
		remain_times = pot_cfg.help_other_times - watered_times
		max_times = pot_cfg.help_other_times
	end
	return remain_times, max_times
end

--获取自己今天剩余协助次数
function WateringFlowersWGData:GetTodayRemainHelpOtherTimesByType(type)
	local  other_cfg = self:GetOtherCfg()
	local times = 0

	if not other_cfg then
		return times
	end

	local max_times = type == 1 and other_cfg.help_other_times or other_cfg.help_lover_times
	local other_max_times = type == 1 and other_cfg.help_lover_times or other_cfg.help_other_times

	return max_times - (self.self_water_flower_info.today_total_help_other_times - other_max_times), max_times
end

-- 自己今日剩余被浇水次数
function WateringFlowersWGData:GetTodayRemainWaterTimesByType(type)
	local pot_cfg = self:GetFlowerCfgByType(type - 1)
	local remain_times, max_times = 0, 0

	if not pot_cfg then
		return remain_times, max_times
	end

	if not self.self_water_flower_info.flower_list[type] then
		return remain_times, max_times
	end

	max_times = pot_cfg.self_times
	remain_times = pot_cfg.self_times - self.self_water_flower_info.flower_list[type].today_self_water_times

	return remain_times, max_times
	
end

--获取自己今天剩余协助次数
function WateringFlowersWGData:GetTodayRemainHelpOtherTimes()
	local  other_cfg = self:GetOtherCfg()
	local times = 0

	if not other_cfg then
		return times
	end

	local max_times = other_cfg.help_other_times + other_cfg.help_lover_times

	return max_times - self.self_water_flower_info.today_total_help_other_times - self.self_water_flower_info.today_total_help_lover_timse, max_times
end

--获取自己今天剩余协助次数
function WateringFlowersWGData:GetTodayRemainHelpOtherTimes2(cur_select_toggle)
	local  other_cfg = self:GetOtherCfg()
	local times = 0

	if not other_cfg then
		return times
	end

	local max_times = 0
	local count = 0
	
	if cur_select_toggle == 1 then
		max_times = other_cfg.help_other_times
		count = max_times - self.self_water_flower_info.today_total_help_other_times
	elseif cur_select_toggle == 2 then
		max_times = other_cfg.help_lover_times
		count = max_times - self.self_water_flower_info.today_total_help_lover_timse
	end

	return count
end

--更新好友列表
function WateringFlowersWGData:UpdateWateringFlowersFriendList()
	self.friend_list = {}

	local friend_list = SocietyWGData.Instance:GetFriendList2()
	
	if not friend_list or IsEmptyTable(friend_list) then
		return
	end

	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0

	for k,v in pairs(friend_list) do
		self.friend_list[k] = {}
		self.friend_list[k].is_lover = 0
		self.friend_list[k].is_online = v.is_online
		self.friend_list[k].uid = v.user_id
		self.friend_list[k].name = v.gamename
		self.friend_list[k].sex = v.sex
		self.friend_list[k].prof = v.prof
		self.friend_list[k].intimacy = v.intimacy
		self.friend_list[k].level = v.level
		self.friend_list[k].is_be_invited = 0
		self.friend_list[k].today_total_help_other_times = 0
		self.friend_list[k].common_pot_info = {}
		self.friend_list[k].lover_pot_info = {}
		self.friend_list[k].help_me_times = 0
		self.friend_list[k].common_help_me_times = 0
		self.friend_list[k].lover_help_me_times = 0
		self.friend_list[k].offline_time = v.last_logout_timestamp
		self.friend_list[k].list_type = 1

		if lover_id == v.user_id then
			self.friend_list[k].is_lover = 1
		end

		local other_role_info = self:GetOtherRoleFlowerInfoByUid(v.user_id)

		if other_role_info then
			self.friend_list[k].is_be_invited = other_role_info.invite_me 										--是否被邀请
			self.friend_list[k].help_me_times = other_role_info.help_me_times 									--帮特定某人(自己)浇水次数
			self.friend_list[k].today_total_help_other_times = other_role_info.today_total_help_other_times		--今天协助别人总次数
			self.friend_list[k].common_pot_info = other_role_info.flower_list[1]								--普通花盆信息
			self.friend_list[k].lover_pot_info = other_role_info.flower_list[2]								--情侣花盆信息
			self.friend_list[k].common_help_me_times = other_role_info.help_me_times_list[1]					--帮特定某人(自己)普通盆栽浇水次数
			self.friend_list[k].lover_help_me_times = other_role_info.help_me_times_list[2]						--帮特定某人(自己)情侣盆栽浇水次数
		end
	end

	table.sort(self.friend_list, SortTools.KeyUpperSorters("is_lover", "is_online", "is_be_invited", "intimacy", "level"))
end

function WateringFlowersWGData:GetWateringFlowersFriendList()
	return self.friend_list
end

--更新仙盟成员列表
function WateringFlowersWGData:UpdateWateringFlowersMemberList()
	self.member_list = {}

	local member_list = GuildDataConst.GUILD_MEMBER_LIST

	if not member_list or not member_list.list or IsEmptyTable(member_list.list) then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local index = 0

	for k,v in pairs(member_list.list) do
		if v.uid ~= role_id then
			index = index + 1
			self.member_list[index] = {}
			self.member_list[index].is_lover = 0
			self.member_list[index].is_online = v.is_online
			self.member_list[index].uid = v.uid
			self.member_list[index].name = v.role_name
			self.member_list[index].sex = v.sex
			self.member_list[index].prof = v.prof
			self.member_list[index].level = v.level
			self.member_list[index].is_be_invited = 0
			self.member_list[index].today_total_help_other_times = 0
			self.member_list[index].help_me_times = 0
			self.member_list[index].common_help_me_times = 0
			self.member_list[index].lover_help_me_times = 0
			self.member_list[index].common_pot_info = {}
			self.member_list[index].lover_pot_info = {}
			self.member_list[index].offline_time = v.last_login_time
			self.member_list[index].list_type = 2

			if lover_id == v.uid then
				self.member_list[index].is_lover = 1
			end

			local other_role_info = self:GetOtherRoleFlowerInfoByUid(v.uid)

			if other_role_info then
				self.member_list[index].is_be_invited = other_role_info.invite_me 										--是否被邀请
				self.member_list[index].today_total_help_other_times = other_role_info.today_total_help_other_times		--今天协助别人总次数
				self.member_list[index].common_pot_info = other_role_info.flower_list[1]								--普通花盆信息
				self.member_list[index].lover_pot_info = other_role_info.flower_list[2]									--情侣花盆信息
				self.member_list[index].common_help_me_times = other_role_info.help_me_times_list[1]					--帮特定某人(自己)普通盆栽浇水次数
				self.member_list[index].lover_help_me_times = other_role_info.help_me_times_list[2]						--帮特定某人(自己)情侣盆栽浇水次数
			end
		end
	end

	table.sort(self.member_list, SortTools.KeyUpperSorters("is_lover", "is_online", "is_be_invited", "level"))
end

function WateringFlowersWGData:GetWateringFlowersMemberList()
	return self.member_list
end

-- 获取是否可也被邀请
function WateringFlowersWGData:GetIsCanInvite(data)
	local is_can_invite = false

	if not data then
		return is_can_invite
	end

	local other_cfg = self:GetOtherCfg()

	if not other_cfg then
		return is_can_invite
	end

	local common_pot_cfg = self:GetFlowerCfgByType(0)

	if not common_pot_cfg then
		return is_can_invite
	end

	local lover_pot_cfg = self:GetFlowerCfgByType(1)

	if not lover_pot_cfg then
		return is_can_invite
	end

	local cur_times = 0
	local total_times = 0

	local cur_help_me_times = 0
	local total_help_me_times = 0

	if data.is_lover == 1 then

		cur_times = (data.lover_pot_info.today_other_water_times or 0) + (data.lover_pot_info.today_other_water_times or 0)
		total_times = other_cfg.help_other_times + other_cfg.help_lover_times

		cur_help_me_times = (data.common_help_me_times or 0) + (data.lover_help_me_times or 0)
		total_help_me_times = common_pot_cfg.help_other_times + lover_pot_cfg.help_other_times
	else
		cur_times = data.lover_pot_info.today_other_water_times or 0
		total_times = other_cfg.help_other_times

		cur_help_me_times = data.common_help_me_times or 0
		total_help_me_times = common_pot_cfg.help_other_times
	end
	-- 被别人浇水次数达到上限
	if cur_times >= total_times then
		return is_can_invite
	end

	-- 帮特定某人(自己)盆栽浇水次数达到上限
	if cur_help_me_times >= total_help_me_times then
		return is_can_invite
	end

	-- 自己盆栽不处于成长期
	local count = 0
	--是否有剩余被浇水次数
	local has_be_water_times = 0

	for i=1,2 do
		local be_water_remain_times, be_water_max_times = self:GetTodayRemindBeWaterdTimesByType(i)
		-- 普通盆栽判断次数
		if be_water_remain_times <= 0 and i == 1 then
			has_be_water_times = has_be_water_times + 1
		else
			-- 情侣盆栽 是情侣判断次数否则不能邀请
			if i == 2 and data.is_lover == 1 and be_water_remain_times <= 0 then
				has_be_water_times = has_be_water_times + 1
			elseif i == 2 and data.is_lover == 0 then
				has_be_water_times = has_be_water_times + 1
			end
		end

		-- 自己盆栽不处于成长期
		if self.self_water_flower_info.flower_list and self.self_water_flower_info.flower_list[i] then
			if self.self_water_flower_info.flower_list[i].status ~= FLOWER_STATUS.FLOWER_STATUS_GROWING then
				count = count + 1
			end
		end
	end

	if count >= 2 or has_be_water_times >= 2 then
		return is_can_invite
	end

	is_can_invite = true

	return is_can_invite
end

function WateringFlowersWGData:GetIsCanHelpWatering(data)
	local log_type = 0
	local is_can_watering = false

	if not data then
		return is_can_watering, log_type
	end

	local other_cfg = self:GetOtherCfg()

	if not other_cfg then
		return is_can_watering, log_type
	end

	local common_pot_cfg = self:GetFlowerCfgByType(0)

	if not common_pot_cfg then
		return is_can_watering, log_type
	end

	local lover_pot_cfg = self:GetFlowerCfgByType(1)

	if not lover_pot_cfg then
		return is_can_watering, log_type
	end

	--自己剩余协助次数
	local remind_help_times = WateringFlowersWGData.Instance:GetTodayRemainHelpOtherTimes()

	if remind_help_times <= 0 then
		log_type = 1
		return is_can_watering, log_type
	end

	local cur_times = 0
	local total_times = 0

	if data.is_lover == 1 then
		cur_times = (data.lover_pot_info.today_other_water_times or 0) + (data.lover_pot_info.today_other_water_times or 0)
		total_times = common_pot_cfg.other_times + lover_pot_cfg.other_times
	else
		cur_times = data.lover_pot_info.today_other_water_times or 0
		total_times = common_pot_cfg.other_times
	end

	-- 被别人浇水次数达到上限
	if cur_times >= total_times then
		log_type = 2
		return is_can_watering, log_type
	end

	--盆栽不处于成长期
	if data.is_lover == 1 then
		local count = 0
		if data.common_pot_info.status ~= FLOWER_STATUS.FLOWER_STATUS_GROWING then
			count = count + 1
		end

		if data.lover_pot_info.status ~= FLOWER_STATUS.FLOWER_STATUS_GROWING then
			count = count + 1
		end

		if count >= 2 then
			log_type = 3
			return is_can_watering, log_type
		end
	else
		if data.common_pot_info.status ~= FLOWER_STATUS.FLOWER_STATUS_GROWING then
			log_type = 3
			return is_can_watering, log_type
		end
	end

	is_can_watering = true
	return is_can_watering, log_type
end

--根据类型获取是否被邀请
--type:1(好友列表) 2(盟友列表)
function WateringFlowersWGData:GetIsBeInveiteByType(type)
	local is_be_invited = false
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	if self.self_water_flower_info.uid ~= role_id then
		return is_be_invited
	end

	local help_times = self:GetTodayRemainHelpOtherTimes()

	if help_times <= 0 then
		return is_be_invited
	end

	local data_list

	if type == 1 then
		data_list = self:GetWateringFlowersFriendList()
	else
		data_list = self:GetWateringFlowersMemberList()
	end
	for k, v in pairs(data_list) do
		local is_can_invite = self:GetIsCanHelpWatering(v)
		if is_can_invite and v.is_be_invited == 1 then
			is_be_invited = true
			return is_be_invited
		end
	end

	return is_be_invited
end

function WateringFlowersWGData:GetWateringFlowerRemind()
	local is_remind = 0

	local status = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS)

	if not status then
		return is_remind
	end

	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS) then
		return is_remind
	end

	local act_cfg = self:GetOtherCfg()
	if act_cfg == nil then
		return is_remind
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()

	if role_level < act_cfg.open_role_level then
		return is_remind
	end

	if IsEmptyTable(self.self_water_flower_info.flower_list) then
		return is_remind
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	for i = 1, 2 do
		--无种植或可领取给红点
		if role_id > 0 then
			if self.self_water_flower_info.flower_list[i].status == FLOWER_STATUS.FLOWER_STATUS_NOTHING then
				if i == 2 then -- 情侣花盆
					local lover_id = RoleWGData.Instance:GetAttr("lover_uid") or 0
					is_remind = lover_id > 0 and 1 or 0
				else
					is_remind = 1
				end
				return is_remind
			end
		else
			if self.self_water_flower_info.flower_list[1].status == FLOWER_STATUS.FLOWER_STATUS_NOTHING then
				is_remind = 1
				return is_remind
			end
		end
		if self.self_water_flower_info.flower_list[i].status == FLOWER_STATUS.FLOWER_STATUS_RIPE then
			is_remind = 1
			return is_remind
		else
			local help_me_list = self:GetHlepMeDataList(i)
			if not IsEmptyTable(help_me_list) then
				is_remind = 1
				return is_remind
			end
		end
		for key = 1, 2 do
			if self:GetIsBeInveiteByType(key) then
				is_remind = 1
				return is_remind
			end
		end
	end
	return is_remind
end

--设置一个花园标记，区分现在是在别人的花园还是自己的花园
--flag == 1 and 别人的花园 or 自己的花园
function WateringFlowersWGData:SetGardenFlag(garden_flag)
	self.garden_flag = garden_flag
end

function WateringFlowersWGData:GetGardenFlag()
	return self.garden_flag
end

--根据花盆类型获取花盆配置
function WateringFlowersWGData:GetFlowerCfgByType(pot_type)
	local openserver_day_cfg = self:GetOpenServerDayCfg()

	if not openserver_day_cfg then
		return
	end

	local grade = openserver_day_cfg.grade

	for i,v in ipairs(self.watering_flower_cfg.flower_cfg) do
		if grade == v.grade and pot_type == v.seq then
			return v
		end
	end
end

--获取活动开启当天的配置
function WateringFlowersWGData:GetOpenServerDayCfg()
	local cfg = self.watering_flower_cfg.openserver_day_cfg

	local activity_open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS)
	local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
    -- print_error("FFF=====Watering open_time", week, open_time)
	for k,v in pairs(cfg) do
		if activity_open_day >= v.start_server_day and activity_open_day < v.end_server_day and week == v.week_index then
			return v
		end
	end
end

--获取其他配置
function WateringFlowersWGData:GetOtherCfg()
	local cfg = self.watering_flower_cfg.other
	local openserver_day_cfg = self:GetOpenServerDayCfg()
	if not openserver_day_cfg then
		return
	end

	for k,v in pairs(cfg) do
		if openserver_day_cfg.grade == v.grade then
			return v
		end
	end
end

function WateringFlowersWGData:GetInterfaceCfg()
	local openserver_day_cfg = self:GetOpenServerDayCfg()

	if not openserver_day_cfg then
		return
	end

	for k,v in pairs(self.watering_flower_cfg.interface) do
		if openserver_day_cfg.interface == v.interface then
			return v
		end
	end
end

-- 获取活动界面文本 如果是饮酒就把品茶转换成饮酒
function WateringFlowersWGData:GetInterfaceDesc(key,is_drink)
	local cfg = self:GetInterfaceCfg()
	if cfg and cfg[key] then
		local desc = cfg[key]
		if is_drink then
			desc = string.gsub(desc, Language.OpertionAcitvity.WateringFlowers.FlowerActionNames[1], Language.OpertionAcitvity.WateringFlowers.FlowerActionNames[2], 3)
			desc = string.gsub(desc, Language.OpertionAcitvity.WateringFlowers.FlowerActionNames2[1], Language.OpertionAcitvity.WateringFlowers.FlowerActionNames2[2], 3)
		end
		return desc
	end
	return ""
end

--根据类型和剩余时间获取花的模型
function WateringFlowersWGData:GetFlowerModelCfgByTypeAndRemainTime(flower_type, remain_time)
	local flower_model_cfg = self.watering_flower_cfg.flower_model

	for i,v in ipairs(flower_model_cfg) do
		if v.flower_type == flower_type - 1 and remain_time <= v.remain_time and remain_time > v.remain_time_1 then
			return v
		end
	end
end

function WateringFlowersWGData:GetOAFlowerIsOpen()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS) then
		return false
	end

	local act_cfg = self:GetOtherCfg()
	if act_cfg == nil then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local need_level = act_cfg.open_role_level
	return role_level >= need_level
end




-- 缓存邀请人员的cd列表
function WateringFlowersWGData:AddCacheCDList(role_id, time)
    if not self.cache_invite_cd_list then
        self.cache_invite_cd_list = {}
    end
    if CountDownManager.Instance:HasCountDown("water_flower_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("water_flower_invite_cd_time")
    end
    CountDownManager.Instance:AddCountDown("water_flower_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
    BindTool.Bind(self.CheckInviteTime, self), 15 + TimeWGCtrl.Instance:GetServerTime())
    local cfg_time = self:GetOtherCfg() and self:GetOtherCfg().cd or 10
    time = time or cfg_time
    self.cache_invite_cd_list[role_id] = time
    OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_watering_flowers, "flush_invite_time")
end

function WateringFlowersWGData:GetCacheCDByRoleid(role_id)
    if not self.cache_invite_cd_list then
        return 0
    end
    return self.cache_invite_cd_list[role_id] or 0
end

function WateringFlowersWGData:CheckInviteTime()
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("water_flower_invite_cd_time")
    else
        CountDownManager.Instance:AddCountDown("water_flower_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self),
        BindTool.Bind(self.CheckInviteTime,self), 15 + TimeWGCtrl.Instance:GetServerTime())
    end
end

-- 缓存邀请人员的cd列表
function WateringFlowersWGData:UpdateInviteTime(elapse_time, total_time)
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("water_flower_invite_cd_time")
        return
    end

    for k, v in pairs(self.cache_invite_cd_list) do
        self.cache_invite_cd_list[k] = v - 1
        if v <= 0 then
            self.cache_invite_cd_list[k] = nil
            table.remove(self.cache_invite_cd_list, k)
        end
        OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_watering_flowers, "flush_invite_time")
    end
end