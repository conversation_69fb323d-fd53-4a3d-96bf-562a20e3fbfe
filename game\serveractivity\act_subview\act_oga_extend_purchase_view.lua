
function KfActivityView:LoadOGAPurchaseCallBack()
    if not self.dp_item_list_list then
        self.dp_item_list_list = AsyncListView.New(OGARmbBuyItemRender, self.node_list["dp_item_list"])
        self.dp_item_list_list:SetStartZeroIndex(false)
        -- self.dp_item_list_list:SetSelectCallBack(BindTool.Bind(self.OnselectChangeXXXclick,self)) 
    end
    
    self.node_list["dp_remain_time"].text.text = ""
    
    
    XUI.AddClickEventListener(self.node_list["dp_one_key_btn"],BindTool.Bind(self.OnClickOGAOneKeyPurchase, self))
    self.is_can_oga_one_key_buy = true
end

function KfActivityView:CloseOGAPurchaseCallBack()
    self.oga_one_key_data = nil
    self.is_can_oga_one_key_buy = true
end

function KfActivityView:ReleaseOGAPurchaseCallBack()
    if CountDownManager.Instance:HasCountDown("OGA_Purchase_CD") then
		CountDownManager.Instance:RemoveCountDown("OGA_Purchase_CD")
	end

    if self.dp_item_list_list then
        self.dp_item_list_list:DeleteMe()
        self.dp_item_list_list = nil
    end
end

function KfActivityView:FlushOGAPurchaseCallBack()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local all_rmb_buy_cfg = ServerActivityWGData.Instance:GetOgaRmbCfgByDay(open_day)
    local cd_time = -1
    local tb_normal, tb_sp = {}, nil
    self.is_can_oga_one_key_buy = true
    if not IsEmptyTable(all_rmb_buy_cfg) then
        for key, value in pairs(all_rmb_buy_cfg) do
            if value.rmb_type == OGA_EXTEND_RMB_TYPE_ENUM.ONE_KEY then
                tb_sp = value
            else
                table.insert(tb_normal, {
                    cfg_data = value,
                    rmb_seq = value.rmb_seq,
                    sort_val = 0
                })
            end
        end

        local close_day, had_buy_time = -1, 0
        if not IsEmptyTable(tb_normal) then
            local buy_limit = false
            local had_buy_time = 0
            for key, value in pairs(tb_normal) do
                buy_limit = value.cfg_data.buy_limit or 1
                had_buy_time = ServerActivityWGData.Instance:GetOGARmbBuyInfoBySeq(value.cfg_data.seq)
                value.sort_val = had_buy_time >= buy_limit and 1 or 0
            end

            table.sort(tb_normal, SortTools.KeyLowerSorters("sort_val", "rmb_seq"))
            for key, value in pairs(tb_normal) do
                if value.cfg_data.close_open_day > close_day then
                    close_day = value.cfg_data.close_open_day
                end

                if self.is_can_oga_one_key_buy then
                    had_buy_time = ServerActivityWGData.Instance:GetOGARmbBuyInfoBySeq(value.cfg_data.seq) or 0
                    self.is_can_oga_one_key_buy = had_buy_time <= 0
                end
            end
        end

        self.oga_one_key_data = tb_sp

        local diff_sec = ((close_day - open_day >= 0) and (close_day - open_day) + 1 or 0 ) * 86400
        local start_stamp = TimeWGCtrl.Instance:GetTodayBeginningTimestamp()
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        start_stamp = start_stamp + diff_sec
        cd_time = start_stamp - server_time

        self.node_list["dp_one_key_btn"]:SetActive(self.is_can_oga_one_key_buy)
    end
    self.dp_item_list_list:SetDataList(tb_normal)

    self:FlushOGAPurchaseCDPart(cd_time)
end

-- 活动倒计时cd
function KfActivityView:FlushOGAPurchaseCDPart(cd_time)
    local time = cd_time
    if CountDownManager.Instance:HasCountDown("OGA_Purchase_CD") then
		CountDownManager.Instance:RemoveCountDown("OGA_Purchase_CD")
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown("OGA_Purchase_CD",
			BindTool.Bind(self.UpdateOGAPurchaseCountDown, self),
			BindTool.Bind(self.OnOGAPurchaseCDComplete, self),
			nil, time, 1)
	else
		self:OnOGAPurchaseCDComplete()
	end
end

function KfActivityView:OnOGAPurchaseCDComplete()
    self.node_list.dp_remain_time.text.text = ""
end

function KfActivityView:UpdateOGAPurchaseCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list.dp_remain_time.text.text = time_str
end

function KfActivityView:OnClickOGAOneKeyPurchase()
    if self.oga_one_key_data then
        RechargeWGCtrl.Instance:Recharge(self.oga_one_key_data.price, self.oga_one_key_data.rmb_type, self.oga_one_key_data.rmb_seq)
    end
end

------------------------------------------- OGARmbBuyItemRender -------------------------------------------
OGARmbBuyItemRender = OGARmbBuyItemRender or BaseClass(BaseRender)

function OGARmbBuyItemRender:LoadCallBack()
    if not self.reward_gird then
        self.reward_gird = AsyncBaseGrid.New()
        local tb_grid_info = {}
        tb_grid_info.col = 2
        tb_grid_info.change_cells_num = 1
        tb_grid_info.itemRender = ItemCell
        tb_grid_info.list_view = self.node_list["reward_gird_list"]
        self.reward_gird:CreateCells(tb_grid_info)
        self.reward_gird:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list["buy_btn"],BindTool.Bind(self.OnClickBuyItem, self))
end

function OGARmbBuyItemRender:ReleaseCallBack()
    if self.reward_gird then
        self.reward_gird:DeleteMe()
        self.reward_gird = nil
    end
end

function OGARmbBuyItemRender:OnFlush()
    self.is_can_buy = false
	if not self.data then return end

    local cfg_data = self.data.cfg_data

    local reward = cfg_data.reward or {}
    self.reward_gird:SetDataList(reward)

    self.node_list["item_title"].text.text = cfg_data.gift_name or ''

	local price_str = RoleWGData.GetPayMoneyStr(cfg_data.price, cfg_data.rmb_type, cfg_data.rmb_seq)
    self.node_list["cost_txt"].text.text = price_str
    
    local buy_limit = cfg_data.buy_limit or 1
    local had_buy_time = ServerActivityWGData.Instance:GetOGARmbBuyInfoBySeq(cfg_data.seq)
    had_buy_time = had_buy_time or 0

    self.is_can_buy = buy_limit - had_buy_time > 0
    local color = self.is_can_buy and "#0F9C1C" or COLOR3B.RED
    local show_buy_time = ToColorStr(buy_limit - had_buy_time, color)
    local show_str = string.format(Language.OpenServer.OGABuyLimit, show_buy_time, buy_limit)
    self.node_list["buy_time_txt"].text.text = show_str

    local ori_show_str = ''
    if cfg_data.origin_price then
        ori_show_str = string.format(Language.OpenServer.OGABuyPrice, cfg_data.origin_price)
    end
    self.node_list["origin_price"].text.text = ori_show_str

    self.node_list["buy_btn"]:SetActive(self.is_can_buy)
    self.node_list["buy_out_flag"]:SetActive(not self.is_can_buy)
end

function OGARmbBuyItemRender:OnClickBuyItem()
    local cfg_data = self.data.cfg_data
    if self.is_can_buy and cfg_data then
        RechargeWGCtrl.Instance:Recharge(cfg_data.price, cfg_data.rmb_type, cfg_data.rmb_seq)
    end
end