﻿namespace Nirvana
{
    using System;
    using UnityEngine;

    // 单个弹道的属性和行为，包括弹道的速度、移动曲线、命中特效等，并在更新时处理弹道的移动和命中逻辑。
    [Serializable]
    public sealed class ProjectileObject
    {
        [SerializeField]
        private EffectControl effectPrefab;

        [SerializeField]
        private float delay = 0.0f;                         // 弹道的延迟时间

        [SerializeField]
        private float speedBase = 5.0f;                     // 弹道的基础速度

        [SerializeField]
        private AnimationCurve speedCurve = 
            AnimationCurve.Linear(0.0f, 1.0f, 1.0f, 1.0f);  // 弹道的速度曲线

        [SerializeField]
        private float speedMultiplier = 5.0f;               // 弹道的速度乘数

        [SerializeField]
        private AnimationCurve moveXCurve = 
            AnimationCurve.Linear(0.0f, 1.0f, 1.0f, 1.0f);  // 弹道在X轴上的移动曲线

        [SerializeField]
        private float moveXMultiplier = 1.0f;               // 弹道在X轴上的移动乘数

        [SerializeField]
        private AnimationCurve moveYCurve = 
            AnimationCurve.Linear(0.0f, 1.0f, 1.0f, 1.0f);  // 弹道在Y轴上的移动曲线

        [SerializeField]
        private float moveYMultiplier = 1.0f;               // 弹道在Y轴上的移动乘数

        [SerializeField]
        private Vector3 targetOffset = Vector3.zero;        // 弹道的目标偏移量

        [SerializeField]
        private EffectControl hitEffect;                    // 弹道命中目标时的特效

        private Vector3 sourceScale;                        // 弹道的源缩放比例
        private float delayTime;                            // 弹道的延迟时间
        private int layer;                                  // 弹道的层级
        private EffectControl effect;                       // 弹道的特效实例
        private Vector3 startPosition;                      // 弹道的起始位置
        private Vector3 normalPosition;                     // 弹道的当前位置
        private bool playing;                               // 弹道是否正在播放
        private bool started;                               // 弹道是否已启动

        public bool Playing
        {
            get { return this.playing; }
        }

        public void Play(Vector3 sourceScale, Vector3 position, int layer)
        {
            this.sourceScale = sourceScale;
            this.layer = layer;
            this.normalPosition = position;
            this.startPosition = position;
            this.delayTime = this.delay;
            this.playing = true;
            this.started = false;
        }

        public void Update(Vector3 targetPosition)
        {
            if (!this.playing)
            {
                return;
            }

            if (!this.started)
            {
                this.delayTime -= Time.deltaTime;
                if (this.delayTime <= 0.0f)
                {
                    var effectInstance = GameObjectPool.Instance.Spawn(
                        this.effectPrefab, null);
                    effectInstance.FinishEvent += () => 
                        GameObjectPool.Instance.Free(effectInstance.gameObject);
                    effectInstance.Reset();
                    effectInstance.Play();

                    this.effect = effectInstance;
                    this.started = true;
                }
                else
                {
                    return;
                }
            }

            // 更新弹道的位置
            targetPosition += this.targetOffset;
            var offset = targetPosition - this.normalPosition;
            var total = targetPosition - this.startPosition;
            var radio = 1.0f - offset.magnitude / total.magnitude;

            var direction = offset.normalized;
            var speed = this.speedBase + 
                this.speedMultiplier * this.speedCurve.Evaluate(radio);
            var velocity = direction * speed;

            var movement = velocity * Time.deltaTime;
            if (offset.sqrMagnitude > movement.sqrMagnitude)
            {
                this.normalPosition += movement;
                var movementPosition = this.normalPosition;
                var movementUp = Vector3.up;
                var movementRight = Vector3.Cross(direction, movementUp);
                // 根据曲线和乘数调整弹道在X轴和Y轴上的位置
                if (!Mathf.Approximately(this.moveXMultiplier, 0.0f))
                {
                    var moveX = 
                        this.moveXMultiplier * this.moveXCurve.Evaluate(radio);
                    movementPosition += movementRight * moveX;
                }

                if (!Mathf.Approximately(this.moveYMultiplier, 0.0f))
                {
                    var moveY = 
                        this.moveYMultiplier * this.moveYCurve.Evaluate(radio);
                    movementPosition += movementUp * moveY;
                }

                // 更新特效的位置和朝向
                this.effect.transform.position = movementPosition;
                this.effect.transform.LookAt(targetPosition);
            }
            else
            {
                // 弹道到达目标位置，停止播放并触发命中特效
                this.effect.transform.position = targetPosition;
                this.effect.Stop();
                if (this.hitEffect != null)
                {
                    var effect = GameObjectPool.Instance.Spawn(hitEffect, null);
                    effect.transform.SetPositionAndRotation(
                        this.effect.transform.position, 
                        this.effect.transform.rotation);

                    effect.transform.localScale = this.sourceScale;
                    effect.gameObject.SetLayerRecursively(this.layer);

                    effect.FinishEvent += 
                        () => GameObjectPool.Instance.Free(effect.gameObject);
                    effect.Reset();
                    effect.Play();
                }
                this.playing = false;
            }
        }
    }
}
