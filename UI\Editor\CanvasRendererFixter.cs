﻿using Nirvana;
using System;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

class CanvasRendererFixter
{
	[MenuItem("自定义工具/修复工具/UI/修复UI CanvasRenderer丢失")]
	static void Fixed()
	{
		string[] guids = AssetDatabase.FindAssets("t:prefab", new string[] { "Assets/Game/UIs/" });
		int count = guids.Length;
		int index = 0;
		string path = "";
		try
		{
			foreach (string guid in guids)
			{
				path = AssetDatabase.GUIDToAssetPath(guid);
				GameObject obj = AssetDatabase.LoadAssetAtPath<GameObject>(path);

				bool isNeedReplace = false;
				UIBlock[] uIBlocks = obj.GetComponentsInChildren<UIBlock>(true);
				foreach (UIBlock uiblock in uIBlocks)
				{
					CanvasRenderer canvasRenderer = uiblock.GetComponent<CanvasRenderer>();
					if (canvasRenderer == null)
					{
						isNeedReplace = true;
						uiblock.GetOrAddComponent<CanvasRenderer>();
					}
				}

				if (isNeedReplace)
				{
					PrefabUtility.ResetToPrefabState(obj.gameObject);
					PrefabUtility.SetPropertyModifications(obj.gameObject, new PropertyModification[] { });
					Debug.LogErrorFormat("丢失 CanvasRenderer, path: {0}", path);
				}

				index++;
				EditorUtility.DisplayProgressBar("", string.Format("正在处理:{0}/{1}", index, count), (float)index / (float)count);
			}
		}

		catch (Exception ex)
		{
			EditorUtility.ClearProgressBar();
			Debug.LogErrorFormat("path:{0}, Exception:{1}", path, ex.ToString());
		}

		EditorUtility.ClearProgressBar();
		AssetDatabase.SaveAssets();
		AssetDatabase.Refresh();
	}

	[MenuItem("自定义工具/修复工具/UI/移除所有预制体多余的UI CanvasRenderer")]
	static void RemvoeAllUselessCanvasRenderer()
	{
		string[] guids = AssetDatabase.FindAssets("t:prefab", new string[] { "Assets/Game/UIs/" });
		int count = guids.Length;
		int index = 0;
		string path = "";
		try
		{
			foreach (string guid in guids)
			{
				path = AssetDatabase.GUIDToAssetPath(guid);
				GameObject obj = AssetDatabase.LoadAssetAtPath<GameObject>(path);

				RemvoeCanvasRendere(obj, path);

				index++;
				EditorUtility.DisplayProgressBar("", string.Format("正在处理:{0}/{1}", index, count), (float)index / (float)count);
			}
		}
		catch (Exception ex)
		{
			EditorUtility.ClearProgressBar();
			Debug.LogErrorFormat("path:{0}, Exception:{1}", path, ex.ToString());
		}
		EditorUtility.ClearProgressBar();
	}

	public static void RemvoeUselessCanvasRenderer(bool is_check_prefab = false)
	{
		GameObject[] objs = Selection.gameObjects;
		int count = objs.Length;
		int index = 0;
		string path = "";
		GameObject gameObjec;
		try
		{
			foreach (var obj in objs)
			{
				gameObjec = obj;

				if (is_check_prefab)
				{
					gameObjec = PrefabUtility.GetCorrespondingObjectFromSource(obj);
				}

				path = AssetDatabase.GetAssetPath(gameObjec);

				RemvoeCanvasRendere(gameObjec, path);

				index++;
				EditorUtility.DisplayProgressBar("", string.Format("正在处理:{0}/{1}", index, count), (float)index / (float)count);
			}
		}
		catch (Exception ex)
		{
			EditorUtility.ClearProgressBar();
			Debug.LogErrorFormat("Exception:{0}", ex.ToString());
		}
		EditorUtility.ClearProgressBar();
	}

	[MenuItem("GameObject/技术专用/UI/(可多选)移除选择的预制体多余的UI CanvasRenderer")]
	static void RemvoeUselessCanvasRendererGameObjectBtn()
	{
		RemvoeUselessCanvasRenderer(true);
	}

	[MenuItem("Assets/技术专用/UI/(可多选)移除选择的预制体多余的UI CanvasRenderer")]
	static void RemvoeUselessCanvasRendererAssetsBtn()
	{
		RemvoeUselessCanvasRenderer();
	}

	static void RemvoeCanvasRendere(GameObject obj, string path)
	{
		bool isNeedReplace = false;

		try
		{
			//判断是否为预制体.
			if (null != obj && PrefabUtility.IsPartOfAnyPrefab(obj))
			{
				GameObject go = GameObject.Instantiate(obj);

				CanvasRenderer[] canvasRenderers = go.GetComponentsInChildren<CanvasRenderer>(true);
				for (int i = 0; i < canvasRenderers.Length; ++i)
				{
					var canvasRender = canvasRenderers[i];
					//没有渲染组件的都移除掉.
					if (null == canvasRender.GetComponent<Graphic>())
					{
						isNeedReplace = true;

						Debug.Log($"{go.name} ====>>> {canvasRender.name}结点上的CanvasRender组件没有关联Graphic，已移除");

						GameObject.DestroyImmediate(canvasRender, true);
					}
				}

				if (isNeedReplace)
				{
					PrefabUtility.SaveAsPrefabAsset(go, path);
				}

				GameObject.DestroyImmediate(go);
			}
		}
		catch (Exception ex)
		{
			Debug.LogErrorFormat("Exception:{0}", ex.ToString());
		}
	}
}