
require("game/daycounter/daycounter_wg_data")

DayCounterWGCtrl = DayCounterWGCtrl or BaseClass(BaseWGCtrl)

function DayCounterWGCtrl:__init()
	if DayCounterWGCtrl.Instance ~= nil then
		<PERSON><PERSON>r<PERSON><PERSON>("[DayCounterWGCtrl] attempt to create singleton twice!")
		return
	end
	DayCounterWGCtrl.Instance = self
	self.data = DayCounterWGData.New()
	self:RegisterAllProtocols()
end

function DayCounterWGCtrl:__delete()
	DayCounterWGCtrl.Instance = nil

	self.data:DeleteMe()
	self.data = nil
end

function DayCounterWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCDayCounterInfo, 'OnDayCounterInfo')
	self:RegisterProtocol(SCDayCounterItemInfo, 'OnDayCounterItemInfo')
	self:RegisterProtocol(SCCommonDayCounterInfo, 'OnDayCounterInfo2')
	self:RegisterProtocol(SCCommonDayCounterItemInfo, 'OnDayCounterItemInfo2')
end

function DayCounterWGCtrl:OnDayCounterInfo2(protocol)
	for k,v in pairs(protocol.day_counter_list) do 
		if k == DAY_COUNT.COMMON_DAYCOUNT_ID_TEAM_WUJINJITAN_OPEN_TIMES +1 then
			self.data:SetDayCount(DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_OPEN_TIMES, v)
			WuJinJiTanWGData.Instance:SetTeamExpFbEnterTimes(v)
			RemindManager.Instance:Fire(RemindName.LianYuFuBen)
		elseif k == DAY_COUNT.COMMON_DAYCOUNT_ID_TEAM_WUJINJITAN_USE_TICKET_TIMES +1 then
			self.data:SetDayCount(DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_USE_TICKET_TIMES, v)
			WuJinJiTanWGData.Instance:SetTeamExpUseTicketTimes(v)
		end

	end	
	BiZuoWGCtrl.Instance:Flush()
	GlobalEventSystem:Fire(OtherEventType.DAY_COUNT_CHANGE, -1)
end

-- 全部次数改变
function DayCounterWGCtrl:OnDayCounterInfo(protocol)
	for k, v in pairs(protocol.daycount_list) do
		if DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_OPEN_TIMES ~= k and DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_USE_TICKET_TIMES ~= k then --这两条移动去新协议
			self.data:SetDayCount(k, v)
		end
		
		if (DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT) == k then
			YunbiaoWGCtrl.Instance:OnLingQuCiShuChangeHandler(v)
		elseif (DAY_COUNT.DAYCOUNT_ID_HUSONG_TASK_VIP_BUY_COUNT) == k then
			YunbiaoWGCtrl.Instance:OnGouMaiCiShuChangeHandler(v)
		elseif (DAY_COUNT.DAYCOUNT_ID_HUSONG_REFRESH_COLOR_FREE_TIMES) == k then
			YunbiaoWGCtrl.Instance:OnChangeRefreshFreeTimeHandler(v)
		elseif (DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_BUY_TIMES) == k then
            WuJinJiTanWGData.Instance:SetTeamFbBuyTimes(v)
            FuBenPanelWGCtrl.Instance:FlushBuyView()
			if FuBenPanelWGCtrl.Instance.team_exp_buy:IsOpen() then
				FuBenPanelWGCtrl.Instance.team_exp_buy:Flush(0, "buy_count")
				FuBenWGCtrl.Instance:FlushCombinePanel()
			end
		elseif (DAY_COUNT.DAYCOUNT_ID_GCZ_DAILY_REWARD_TIMES) == k then
			GuildWGCtrl.Instance:GuildViewFlush()
		elseif (DAY_COUNT.DAYCOUNT_ID_XIANMENGZHAN_RANK_REWARD_TIMES) == k then
			GuildWGCtrl.Instance:SetXianMengZhanRewardCounter(v)
		elseif (DAY_COUNT.DAYCOUNT_ID_FB_COIN) == k
				or (DAY_COUNT.DAYCOUNT_ID_FB_XIANNV) == k
				or (DAY_COUNT.DAYCOUNT_ID_FB_QIBING) == k
				or (DAY_COUNT.DAYCOUNT_ID_FB_WING) == k
				or (DAY_COUNT.DAYCOUNT_ID_FB_XIULIAN) == k then
		elseif (DAY_COUNT.DAYCOUNT_ID_GUILD_ZHUFU_TIMES) == k then
			GuildWGData.Instance:SetGuildZhuFuNum(v)
		elseif DAY_COUNT.DAYCOUNT_ID_FRIENDBLESS_TIMES == k then
			-- 好友祝福次数
			SocietyWGData.Instance:SetFriendblessTimes(v)
		elseif DAY_COUNT.DAYCOUNT_ID_FETCH_FRIENDBLESS_TIMES == k then
			-- 好友祝福领取奖励次数
			SocietyWGData.Instance:SetFetchFriendblessTimes(v)
		elseif DAY_COUNT.DAYCOUNT_ID_WUSHUANGFB_FETCH_REWARD == k then
			DailyWGData.Instance:SetWushuangRewardInfo(v)
		elseif (DAY_COUNT.DAYCOUNT_ID_GUILD_DAY_REWARD) == k then
			GuildWGData.Instance:SetDaycountIsDayReward(v)
		elseif (DAY_COUNT.DAYCOUNT_ID_GUILD_LINGDI_REWARD) == k then
			GuildWGData.Instance:SetDaycountLingDiReward(v)
		elseif DAY_COUNT.DAYCOUNTER_ITEM_INFO_SC == k then
			ZhuZaiShenDianWGData.Instance:SetReceiveFlag(v)
			RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)
		elseif DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_ENTER_TIMES == k then
			RemindManager.Instance:Fire(RemindName.LiuDaoLunHui)
		elseif DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES == k then
			RemindManager.Instance:Fire(RemindName.LiuDaoLunHui)
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_EQUIPMENT_FB_HELP_TIMES == k then
			TeamEquipFbWGData.Instance:SetXieZhuTimes(v)
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_YUANGU_XIANDIAN_VIP_BUY_TIME == k then
			TeamEquipFbWGData.Instance:SetHTEFbVipBuyTimes(v)
            RemindManager.Instance:Fire(RemindName.YuanGuXianDian)
            FuBenPanelWGCtrl.Instance:FlushBuyView()
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_KILLGODTOWER_GET_REWARD_COUNT  == k then
			FuBenPanelWGData.Instance:SetZhuShenTaFbDayCount(v)
			RemindManager.Instance:Fire(RemindName.ZhuShenTa)
		elseif DAY_COUNT.DAYCOUNT_ID_SHENYING_FB_ENTER_TIMES == k then
			BossWGData.Instance:SetShenYinFbDayCount(v)
		elseif DAY_COUNT.DAYCOUNT_ID_WORLD_BOSS_TIRE == k then
			BossWGData.Instance:SetWorldBossTiredByDayCount(v)
			BossWGCtrl.Instance:FulshWorldBossTired()
			BossWGCtrl.Instance:SetRoleTiredIcon(k)
		elseif DAY_COUNT.DAYCOUNT_ID_MJ_BOSS_TIRE == k then
			BossWGData.Instance:SetMIJiBossTiredByDaycount(v)
			BossWGCtrl.Instance:FulshScrectBossTired()
            BossWGCtrl.Instance:SetRoleTiredIcon(k)
        elseif DAY_COUNT.DAYCOUNT_ID_TREASURE_CRYSTAL_NUM == k then --跨服BOSS珍稀水晶采集数
            BossWGData.Instance:SetTreasureGatherTimes(v)
            ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_mh)
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_YUANGU_XIANDIAN_GET_REWARD_COUNT == k then
			TeamEquipFbWGData.Instance:SetHTEFbTimes(v)
			RemindManager.Instance:Fire(RemindName.YuanGuXianDian)
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_equip_high, "yuangu_enter_times", {yuangu_enter_times = true})
		elseif DAY_COUNT.DAYCOUNT_ID_TIANSHEN_FB_ENTER_TIMES == k then 	-- 天神副本进入次数
            FuBenPanelWGData.Instance:SetTianShenFbDayCount(k, v)
            FuBenPanelWGCtrl.Instance:FlushBuyView()
			RemindManager.Instance:Fire(RemindName.TianShenFb)
		elseif DAY_COUNT.DAYCOUNT_ID_TIANSHEN_FB_VIP_BUY_TIMES == k then 	-- 天神副本VIP购买次数
            FuBenPanelWGData.Instance:SetTianShenFbDayCount(k, v)
            FuBenPanelWGCtrl.Instance:FlushBuyView()
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_tianshen, "tianshen_buy_times", {tianshen_buy_times = true})
			RemindManager.Instance:Fire(RemindName.TianShenFb)
        elseif DAY_COUNT.DAYCOUNT_ID_TIANSHEN_FB_ITEM_ADD_TIMES == k then 	-- 天神副本消耗物品增加次数
            FuBenPanelWGCtrl.Instance:FlushBuyView()
			FuBenPanelWGData.Instance:SetTianShenFbDayCount(k, v)
			RemindManager.Instance:Fire(RemindName.TianShenFb)
		elseif DAY_COUNT.DAYCOUNT_WABAO_TASK_COUNT == k then 		--挖宝任务剩余次数
			BootyBayWGData.Instance:SetDailyWaBaoRemainCount(v)
			RemindManager.Instance:Fire(RemindName.Bootybay)
			ViewManager.Instance:FlushView(GuideModuleName.BootyBaySceneFollowView)
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel,TabIndex.fubenpanel_bootybay)
		elseif DAY_COUNT.DAYCOUNT_ID_BAGUAMIZHEN_FB_ENTER_TIMES == k then 	-- 八卦迷阵副本进入次数
			FuBenPanelWGData.Instance:SetBaGuaMiZhenFbDayCount(k, v)
			RemindManager.Instance:Fire(RemindName.BaGuaMiZhenFb)
		elseif DAY_COUNT.DAYCOUNT_ID_BAGUAMIZHEN_FB_VIP_BUY_TIMES == k then 	-- 八卦迷阵VIP购买次数
            FuBenPanelWGData.Instance:SetBaGuaMiZhenFbDayCount(k, v)
            FuBenPanelWGCtrl.Instance:FlushBuyView()
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_tianshen, "baguamizhen_buy_times", {baguamizhen_buy_times = true})
			RemindManager.Instance:Fire(RemindName.BaGuaMiZhenFb)
	 	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_HELP_REWARD_TIMES == k then -- 无尽祭坛获得助战奖励次数
			WuJinJiTanWGData.Instance:SetZhuZhanTimes(v)
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
		elseif DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_ENTER_TIMES == k then 		-- 蛮荒古殿副本进入次数
			ManHuangGuDianWGData.Instance:SetManHuangEnterTimes(v)
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_manhuanggudian)
			RemindManager.Instance:Fire(RemindName.ManHungGuDian)
		elseif DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_VIP_BUY_TIMES == k then		-- 蛮荒古殿副本VIP购买次数
            ManHuangGuDianWGData.Instance:SetManHuangBuyTimes(v)
            FuBenPanelWGCtrl.Instance:FlushBuyView()
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_manhuanggudian)
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
			RemindManager.Instance:Fire(RemindName.ManHungGuDian)
		elseif DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_HELP_TIMES == k then			-- 蛮荒古殿副本协助次数
			ManHuangGuDianWGData.Instance:SetManHuangXieZhuTimes(v)
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_manhuanggudian)
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
			ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_FB_HELP_TIMES == k then	 -- 组队副本共用协助次数
			NewTeamWGData.Instance:SetHasXieZhuTimes(v)
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_FB_HELP_TIMES_MAX == k then
            NewTeamWGData.Instance:SetHasXieZhuTimesMax(v)
        elseif DAY_COUNT.SHENYUAN_BOSS_KILL_REWARD == k then
            BossWGData.Instance:SetShenYuanKillTimes(v)
            ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.world_new_shenyuan_boss)
        elseif DAY_COUNT.SHENYUAN_BOSS_JION_REWARD == k then
            BossWGData.Instance:SetShenYuanJoinTimes(v)
            BossWGCtrl.Instance:FlushShenYuanHurtView()
            BossWGCtrl.Instance:FlushBossVipTimesView()
            ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.world_new_shenyuan_boss)
        elseif DAY_COUNT.WORLD_BOSS_HAS_BUY_TIMES == k then
            BossWGData.Instance:SetWorldBossBuyTimes(v)
            BossWGCtrl.Instance:FlushWorldBossBuyView()
            ViewManager.Instance:FlushView(GuideModuleName.Boss, TabIndex.boss_world)
        elseif DAY_COUNT.DAYCOUNT_ID_DAILY_TREASURE_HELP_REFRESH_TIMES == k then
            GuildBaoXiangWGData.Instance:SetDailyTreaureHelpRefreshTimes(v)
        elseif DAY_COUNT.DAYCOUNT_ID_CROSS_DRAGON_VEIN_SHOP_REFRESH_TIMES == k then
        	CrossLongMaiWGCtrl.Instance:SetLongMaiFlushTimes(v)
        elseif DAY_COUNT.SHENYUAN_BOSS_BUY_JION_TIMES == k then
            BossWGData.Instance:SetShenYuanBuyJoinTimes(v)
            BossWGCtrl.Instance:FlushShenYuanHurtView()
            BossWGCtrl.Instance:FlushBossVipTimesView()
            ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.world_new_shenyuan_boss)
        elseif DAY_COUNT.DAYCOUNT_ID_BOSS_ASSIST == k then
            ViewManager.Instance:FlushView(GuideModuleName.BossXiezhuListView)
            GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
            BossWGCtrl.Instance:FlushKFBossGatherInfoView()
        elseif DAY_COUNT.DAYCOUNT_ID_CROSS_BOSS_HAS_BUY_TIMES == k then
            BossWGData.Instance:SetCrossBossBuyTime(v)
            BossWGCtrl.Instance:FlushWorldBossBuyView()
            ViewManager.Instance:FlushView(GuideModuleName.WorldServer,TabIndex.worserv_boss_mh)
            BossWGCtrl.Instance:SetRoleTiredIcon(0, TabIndex.worserv_boss_mh)
            GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
        elseif DAY_COUNT.DAYCOUNT_ID_CROSS_CHESTSHOP_BOSS_KILL_REWARD == k then
        	TreasureBossWGData.Instance:SetHaveKillTimes(v)
        	TreasureHuntWGCtrl.Instance:FlushTreasureBossList()
        	WorldServerWGCtrl.Instance:FlushTreasureHurtBossInfoView()
        	TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
        elseif DAY_COUNT.DAYCOUNT_ID_CROSS_CHESTSHOP_BOSS_JOIN_REWARD == k then
        	TreasureBossWGData.Instance:SetHaveJoinTimes(v)
        	TreasureHuntWGCtrl.Instance:FlushTreasureBossList()
        	WorldServerWGCtrl.Instance:FlushTreasureHurtBossInfoView()
            TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
        elseif DAY_COUNT.DAYCOUNT_ID_CROSS_FAIRYLAND_BOSS_ENTER_TIMES == k then
            BossWGCtrl.Instance:SetXianJieEnterTimes(v)
		elseif DAY_COUNT.DAYCOUNT_ID_CROSS_LONGMAI_GATHER_TIMES == k then
			CrossLongMaiWGCtrl.Instance:OnGatherTimesChange(v)
		elseif DAY_COUNT.DAYCOUNT_ID_KILL_VIP_BOSS_COUNT == k then
			BossAssistWGCtrl.Instance:VipBossKillTimesChange(v)
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_TYPE_COMMON_BOSS1_ENTER_TIMES == k then  -- 幻兽副本
			FuBenTeamCommonBossWGData.Instance:SetFuBenEnterTimes(GoalTeamType.FbControlBeastsType, v)
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_control_beasts)
			RemindManager.Instance:Fire(RemindName.FbControlBeasts)
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_TYPE_COMMON_BOSS2_ENTER_TIMES == k then
			FuBenTeamCommonBossWGData.Instance:SetFuBenEnterTimes(GoalTeamType.FbBeautyType, v)
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_beauty)
			RemindManager.Instance:Fire(RemindName.FbBeauty)
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_TYPE_COMMON_BOSS3_ENTER_TIMES == k then
			FuBenTeamCommonBossWGData.Instance:SetFuBenEnterTimes(GoalTeamType.FbWuHunType, v)
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_wuhun)
			RemindManager.Instance:Fire(RemindName.FbWuHun)
		elseif DAY_COUNT.DAYCOUNT_ID_TEAM_TYPE_COMMON_TOWER1_ENTER_TIMES == k then
			FuBenTeamCommonTowerWGData.Instance:SetFuBenEnterTimes(GoalTeamType.FbRuneTowerType, v)
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_rune_tower)
			RemindManager.Instance:Fire(RemindName.FbRuneTower)
		end
	end

	BiZuoWGCtrl.Instance:Flush()
	GlobalEventSystem:Fire(OtherEventType.DAY_COUNT_CHANGE, -1)
end

function DayCounterWGCtrl:OnDayCounterItemInfo2(protocol)
	if protocol.day_counter_id == DAY_COUNT.COMMON_DAYCOUNT_ID_TEAM_WUJINJITAN_OPEN_TIMES then
		self.data:SetDayCount(DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_OPEN_TIMES, protocol.day_counter_value)
		WuJinJiTanWGData.Instance:SetTeamExpFbEnterTimes(protocol.day_counter_value)
		RemindManager.Instance:Fire(RemindName.LianYuFuBen)
		GlobalEventSystem:Fire(OtherEventType.DAY_COUNT_CHANGE, DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_OPEN_TIMES)
	elseif protocol.day_counter_id == DAY_COUNT.COMMON_DAYCOUNT_ID_TEAM_WUJINJITAN_USE_TICKET_TIMES then
		self.data:SetDayCount(DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_USE_TICKET_TIMES, protocol.day_counter_value)
		WuJinJiTanWGData.Instance:SetTeamExpUseTicketTimes(protocol.day_counter_value)
		GlobalEventSystem:Fire(OtherEventType.DAY_COUNT_CHANGE, DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_USE_TICKET_TIMES)
	end
end

-- 单个次数改变
function DayCounterWGCtrl:OnDayCounterItemInfo(protocol)
	-- print_error("---单个次数改变----", protocol)
	local day_counter_id = protocol.day_counter_id
	local day_counter_value = protocol.day_counter_value
	self.data:SetDayCount(day_counter_id, day_counter_value)
	if DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT == day_counter_id then
		YunbiaoWGCtrl.Instance:OnLingQuCiShuChangeHandler(day_counter_value)
		-- 影响到每日必做的护送次数， 重新计算数据
		BiZuoWGData.Instance:SetActivityHallCfg()
	elseif DAY_COUNT.DAYCOUNT_ID_HUSONG_TASK_VIP_BUY_COUNT == day_counter_id then
		YunbiaoWGCtrl.Instance:OnGouMaiCiShuChangeHandler(day_counter_value)
		-- 影响到每日必做的护送次数， 重新计算数据
		BiZuoWGData.Instance:SetActivityHallCfg()
	elseif DAY_COUNT.DAYCOUNT_ID_HUSONG_REFRESH_COLOR_FREE_TIMES == day_counter_id then
		YunbiaoWGCtrl.Instance:OnChangeRefreshFreeTimeHandler(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_BUY_TIMES == day_counter_id then
        WuJinJiTanWGData.Instance:SetTeamFbBuyTimes(day_counter_value)
        FuBenPanelWGCtrl.Instance:FlushBuyView()
		FuBenPanelWGCtrl.Instance:Flush(TabIndex.fubenpanel_exp)
		if FuBenPanelWGCtrl.Instance.team_exp_buy:IsOpen() then
			FuBenPanelWGCtrl.Instance.team_exp_buy:Flush(0, "buy_count")
			FuBenWGCtrl.Instance:FlushCombinePanel()
		end
		RemindManager.Instance:Fire(RemindName.LianYuFuBen)
	elseif (DAY_COUNT.DAYCOUNT_ID_GCZ_DAILY_REWARD_TIMES) == day_counter_id then
		GuildWGCtrl.Instance:GuildViewFlush()
	elseif (DAY_COUNT.DAYCOUNT_ID_XIANMENGZHAN_RANK_REWARD_TIMES) == day_counter_id then
		GuildWGCtrl.Instance:SetXianMengZhanRewardCounter(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_FB_COIN  == day_counter_id
			or DAY_COUNT.DAYCOUNT_ID_FB_XIANNV == day_counter_id
			or DAY_COUNT.DAYCOUNT_ID_FB_QIBING == day_counter_id
			or DAY_COUNT.DAYCOUNT_ID_FB_WING == day_counter_id
			or DAY_COUNT.DAYCOUNT_ID_FB_XIULIAN == day_counter_id then
	elseif (DAY_COUNT.DAYCOUNT_ID_GUILD_ZHUFU_TIMES) == day_counter_id then
		GuildWGData.Instance:SetGuildZhuFuNum(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_FRIENDBLESS_TIMES == day_counter_id then
		-- 好友祝福次数
		SocietyWGData.Instance:SetFriendblessTimes(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_FETCH_FRIENDBLESS_TIMES == day_counter_id then
		-- 好友祝福领取奖励次数
		SocietyWGData.Instance:SetFetchFriendblessTimes(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_WUSHUANGFB_FETCH_REWARD == day_counter_id then
		DailyWGData.Instance:SetWushuangRewardInfo(day_counter_value)
		DailyWGCtrl.Instance.view:Flush(math.floor(TabIndex.daily_wushuang / 1000))
	elseif DAY_COUNT.DAYCOUNT_ID_GUILD_DAY_REWARD == day_counter_id then
		GuildWGData.Instance:SetDaycountIsDayReward(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_GUILD_LINGDI_REWARD == day_counter_id then
		GuildWGData.Instance:SetDaycountLingDiReward(day_counter_value)
	elseif DAY_COUNT.DAYCOUNTER_ITEM_INFO_SC == day_counter_id then
	 	ZhuZaiShenDianWGData.Instance:SetReceiveFlag(day_counter_value)
		RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)
	elseif DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_ENTER_TIMES == day_counter_id then
		RemindManager.Instance:Fire(RemindName.LiuDaoLunHui)
	elseif DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES == day_counter_id then
		RemindManager.Instance:Fire(RemindName.LiuDaoLunHui)
	elseif DAY_COUNT.DAYCOUNT_ID_CROSS_MULTIUSER_CHALLENGE_DAYCOUNT_REWARD == day_counter_id then
		KuafuPVPWGData.Instance:SetKF3v3DayCount(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_EQUIPMENT_FB_HELP_TIMES == day_counter_id then
		TeamEquipFbWGData.Instance:SetXieZhuTimes(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_YUANGU_XIANDIAN_VIP_BUY_TIME == day_counter_id then
		TeamEquipFbWGData.Instance:SetHTEFbVipBuyTimes(day_counter_value)
        FuBenPanelWGCtrl.Instance.pet_buy:Flush()
        FuBenPanelWGCtrl.Instance:FlushBuyView()
		FuBenPanelWGCtrl.Instance:Flush()
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_equip_high, "yuangu_enter_times", {yuangu_enter_times = true})
		RemindManager.Instance:Fire(RemindName.YuanGuXianDian)
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_KILLGODTOWER_GET_REWARD_COUNT == day_counter_id then --诛神塔进入次数
		FuBenPanelWGData.Instance:SetZhuShenTaFbDayCount(day_counter_value)
		RemindManager.Instance:Fire(RemindName.ZhuShenTa)
	elseif DAY_COUNT.DAYCOUNT_ID_SHENYING_FB_ENTER_TIMES  == day_counter_id then
			BossWGData.Instance:SetShenYinFbDayCount(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_YUANGU_XIANDIAN_GET_REWARD_COUNT == day_counter_id then
			TeamEquipFbWGData.Instance:SetHTEFbTimes(day_counter_value)
			RemindManager.Instance:Fire(RemindName.YuanGuXianDian)
	elseif DAY_COUNT.DAYCOUNT_ID_TIANSHEN_FB_ENTER_TIMES == day_counter_id then 	-- 天神副本进入次数
        FuBenPanelWGData.Instance:SetTianShenFbDayCount(day_counter_id, day_counter_value)
		RemindManager.Instance:Fire(RemindName.TianShenFb)
        FuBenPanelWGCtrl.Instance:FlushBuyView()
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_tianshen, "tianshen_enter_times", {tianshen_enter_times = true})
	elseif DAY_COUNT.DAYCOUNT_ID_TIANSHEN_FB_VIP_BUY_TIMES == day_counter_id then 	-- 天神副本VIP购买次数
        FuBenPanelWGData.Instance:SetTianShenFbDayCount(day_counter_id, day_counter_value)
		RemindManager.Instance:Fire(RemindName.TianShenFb)
        FuBenPanelWGCtrl.Instance:FlushBuyView()
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_tianshen, "tianshen_buy_times", {tianshen_buy_times = true})
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanelTianShenBuyView)
    elseif DAY_COUNT.DAYCOUNT_ID_TIANSHEN_FB_ITEM_ADD_TIMES == day_counter_id then 	-- 天神副本消耗物品增加次数
        FuBenPanelWGCtrl.Instance:FlushBuyView()
		FuBenPanelWGData.Instance:SetTianShenFbDayCount(day_counter_id, day_counter_value)
		RemindManager.Instance:Fire(RemindName.TianShenFb)
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_tianshen, "tianshen_buy_times", {tianshen_buy_times = true})
	elseif DAY_COUNT.DAYCOUNT_WABAO_TASK_COUNT == day_counter_id then --挖宝任务剩余次数
		BootyBayWGData.Instance:SetDailyWaBaoRemainCount(day_counter_value)
		ViewManager.Instance:FlushView(GuideModuleName.BootyBaySceneFollowView)
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel,TabIndex.fubenpanel_bootybay)
		RemindManager.Instance:Fire(RemindName.Bootybay)
	elseif DAY_COUNT.DAYCOUNT_ID_GUILD_CHUANGONG_REWARD_EXP_TIMES == day_counter_id then
		GuildAnswerWGCtrl.Instance:FlushLeftPanel()
	elseif DAY_COUNT.DAYCOUNT_ID_BAGUAMIZHEN_FB_ENTER_TIMES == day_counter_id then 	-- 八卦迷阵副本进入次数
		FuBenPanelWGData.Instance:SetBaGuaMiZhenFbDayCount(day_counter_id, day_counter_value)
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_bagua, "baguamizhen_enter_times", {baguamizhen_enter_times = true})
		RemindManager.Instance:Fire(RemindName.BaGuaMiZhenFb)
	elseif DAY_COUNT.DAYCOUNT_ID_BAGUAMIZHEN_FB_VIP_BUY_TIMES == day_counter_id then 	-- 八卦迷阵VIP购买次数
        FuBenPanelWGData.Instance:SetBaGuaMiZhenFbDayCount(day_counter_id, day_counter_value)
        FuBenPanelWGCtrl.Instance:FlushBuyView()
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_bagua, "baguamizhen_buy_times", {baguamizhen_buy_times = true})
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanelBaGuaMiZhenBuyView)
		RemindManager.Instance:Fire(RemindName.BaGuaMiZhenFb)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_WUJINJITAN_HELP_REWARD_TIMES == day_counter_id then -- 无尽祭坛获得助战奖励次数
		WuJinJiTanWGData.Instance:SetZhuZhanTimes(day_counter_value)
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
	elseif DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_ENTER_TIMES == day_counter_id then 		-- 蛮荒古殿副本进入次数
		ManHuangGuDianWGData.Instance:SetManHuangEnterTimes(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_VIP_BUY_TIMES == day_counter_id then		-- 蛮荒古殿副本VIP购买次数
        ManHuangGuDianWGData.Instance:SetManHuangBuyTimes(day_counter_value)
        FuBenPanelWGCtrl.Instance:FlushBuyView()
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
		RemindManager.Instance:Fire(RemindName.ManHungGuDian)
	elseif DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_HELP_TIMES == day_counter_id then			-- 蛮荒古殿副本协助次数
		ManHuangGuDianWGData.Instance:SetManHuangXieZhuTimes(day_counter_value)
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_pingtai, "FlushRewardTimes", {FlushRewardTimes = true})
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView, TabIndex.team_my_team, "FlushRewardTimes", {FlushRewardTimes = true})
		RemindManager.Instance:Fire(RemindName.ManHungGuDian)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_FB_HELP_TIMES == day_counter_id then	 -- 组队副本共用协助次数
		NewTeamWGData.Instance:SetHasXieZhuTimes(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_FB_HELP_TIMES_MAX == day_counter_id then
		NewTeamWGData.Instance:SetHasXieZhuTimesMax(day_counter_value)
        RemindManager.Instance:Fire(RemindName.ManHungGuDian)
    elseif DAY_COUNT.SHENYUAN_BOSS_KILL_REWARD ==  day_counter_id then
        BossWGData.Instance:SetShenYuanKillTimes(day_counter_value)
        ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.world_new_shenyuan_boss)
    elseif DAY_COUNT.SHENYUAN_BOSS_JION_REWARD ==  day_counter_id then
        BossWGData.Instance:SetShenYuanJoinTimes(day_counter_value)
        BossWGCtrl.Instance:FlushShenYuanHurtView()
        BossWGCtrl.Instance:FlushBossVipTimesView()
        ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.world_new_shenyuan_boss)
    elseif DAY_COUNT.WORLD_BOSS_HAS_BUY_TIMES == day_counter_id then
        BossWGData.Instance:SetWorldBossBuyTimes(day_counter_value)
        BossWGCtrl.Instance:FlushWorldBossBuyView()
        ViewManager.Instance:FlushView(GuideModuleName.Boss, TabIndex.boss_world)
    elseif DAY_COUNT.DAYCOUNT_ID_TREASURE_CRYSTAL_NUM == day_counter_id then --跨服BOSS珍稀水晶采集数
        BossWGData.Instance:SetTreasureGatherTimes(day_counter_value)
        ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.worserv_boss_mh)
    elseif DAY_COUNT.DAYCOUNT_ID_CROSS_DRAGON_VEIN_SHOP_REFRESH_TIMES == protocol.day_counter_id then
        CrossLongMaiWGCtrl.Instance:SetLongMaiFlushTimes(day_counter_value)
    elseif DAY_COUNT.SHENYUAN_BOSS_BUY_JION_TIMES == day_counter_id then
        BossWGData.Instance:SetShenYuanBuyJoinTimes(day_counter_value)
        BossWGCtrl.Instance:FlushShenYuanHurtView()
        BossWGCtrl.Instance:FlushBossVipTimesView()
        ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.world_new_shenyuan_boss)
    elseif DAY_COUNT.DAYCOUNT_ID_BOSS_ASSIST == day_counter_id then
        ViewManager.Instance:FlushView(GuideModuleName.BossXiezhuListView)
        GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
        BossWGCtrl.Instance:FlushKFBossGatherInfoView()
    elseif DAY_COUNT.DAYCOUNT_ID_CROSS_BOSS_HAS_BUY_TIMES == day_counter_id then
        BossWGData.Instance:SetCrossBossBuyTime(day_counter_value)
        BossWGCtrl.Instance:FlushWorldBossBuyView()
        ViewManager.Instance:FlushView(GuideModuleName.WorldServer,TabIndex.worserv_boss_mh)
        BossWGCtrl.Instance:SetRoleTiredIcon(0, TabIndex.worserv_boss_mh)
        GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
    elseif DAY_COUNT.DAYCOUNT_ID_CROSS_CHESTSHOP_BOSS_KILL_REWARD == day_counter_id then
    	TreasureBossWGData.Instance:SetHaveKillTimes(day_counter_value)
    	TreasureHuntWGCtrl.Instance:FlushTreasureBossList()
    	WorldServerWGCtrl.Instance:FlushTreasureHurtBossInfoView()
    	TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
    elseif DAY_COUNT.DAYCOUNT_ID_CROSS_CHESTSHOP_BOSS_JOIN_REWARD == day_counter_id then
    	TreasureBossWGData.Instance:SetHaveJoinTimes(day_counter_value)
    	TreasureHuntWGCtrl.Instance:FlushTreasureBossList()
    	WorldServerWGCtrl.Instance:FlushTreasureHurtBossInfoView()
        TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
    elseif DAY_COUNT.DAYCOUNT_ID_CROSS_FAIRYLAND_BOSS_ENTER_TIMES == day_counter_id then
        BossWGCtrl.Instance:SetXianJieEnterTimes(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_CROSS_LONGMAI_GATHER_TIMES == day_counter_id then
		CrossLongMaiWGCtrl.Instance:OnGatherTimesChange(day_counter_value)
	elseif DAY_COUNT.DAYCOUNT_ID_KILL_VIP_BOSS_COUNT == day_counter_id then
		--print_error("boss击杀次数：", day_counter_value)
		BossAssistWGCtrl.Instance:VipBossKillTimesChange(day_counter_value)
		MainuiWGCtrl.Instance:FlushView(0, "xianli_info")
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_TYPE_COMMON_BOSS1_ENTER_TIMES == day_counter_id then
		FuBenTeamCommonBossWGData.Instance:SetFuBenEnterTimes(GoalTeamType.FbControlBeastsType, day_counter_value)
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_control_beasts)
		RemindManager.Instance:Fire(RemindName.FbControlBeasts)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_TYPE_COMMON_BOSS2_ENTER_TIMES == day_counter_id then
		FuBenTeamCommonBossWGData.Instance:SetFuBenEnterTimes(GoalTeamType.FbBeautyType, day_counter_value)
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_beauty)
		RemindManager.Instance:Fire(RemindName.FbBeauty)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_TYPE_COMMON_BOSS3_ENTER_TIMES == day_counter_id then
		FuBenTeamCommonBossWGData.Instance:SetFuBenEnterTimes(GoalTeamType.FbWuHunType, day_counter_value)
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_wuhun)
		RemindManager.Instance:Fire(RemindName.FbWuHun)
	elseif DAY_COUNT.DAYCOUNT_ID_TEAM_TYPE_COMMON_TOWER1_ENTER_TIMES == day_counter_id then
		FuBenTeamCommonTowerWGData.Instance:SetFuBenEnterTimes(GoalTeamType.FbRuneTowerType, day_counter_value)
		ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_rune_tower)
		RemindManager.Instance:Fire(RemindName.FbRuneTower)
	end

	GlobalEventSystem:Fire(OtherEventType.DAY_COUNT_CHANGE, day_counter_id)
end