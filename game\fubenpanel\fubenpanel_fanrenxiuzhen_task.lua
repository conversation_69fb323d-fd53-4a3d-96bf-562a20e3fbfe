FanRenXiuZhenTask = FanRenXiuZhenTask or BaseClass(SafeBaseView)

function FanRenXiuZhenTask:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fanrenxiuzhen_task")
	self.view_cache_time = 0
    self.active_close = false
end

function FanRenXiuZhenTask:ReleaseCallBack()

	if self.fanrenxiuzhen_model_display then
		self.fanrenxiuzhen_model_display:DeleteMe()
		self.fanrenxiuzhen_model_display = nil
	end

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end
	self.is_out_fb = nil
	if self.boss_reward_list then
        self.boss_reward_list:DeleteMe()
        self.boss_reward_list = nil
    end
    -- if self.reward_list_mingwen then
    -- 	self.reward_list_mingwen:DeleteMe()
    --     self.reward_list_mingwen = nil
    -- end
    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.FanRenXiuZhenTask, self.get_guide_ui_event)
    self.get_guide_ui_event = nil
end

function FanRenXiuZhenTask:LoadCallBack()
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.FanRenXiuZhenTask, self.get_guide_ui_event)

	self.boss_reward_list = AsyncListView.New(FanRenXiuZhenRewardCell, self.node_list["reward_list"])
	-- self.reward_list_mingwen = AsyncListView.New(FanRenXiuZhenFirstPassRender, self.node_list["reward_list_mingwen"])
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

    local is_guide = FuBenPanelWGData.Instance:GetXiuZhenRoadGuideFlag()
    local level = FuBenPanelWGData.Instance:GetPassLevel()
    if is_guide and level == 0 then --引导第一层的时候才提示
        FunctionGuide.Instance:TriggerGuideByGuideName(GuideUIName.BtnXiuzhenRoadShowWuxing)
    end

	XUI.AddClickEventListener(self.node_list.btn_fanrenxiuzhen_reward, BindTool.Bind(self.OnClickBtnFanrenxiuzhenRewardPreview, self))

	-- if self.fanrenxiuzhen_model_display == nil then
    --     self.fanrenxiuzhen_model_display = OperationActRender.New(self.node_list["fanrenxiuzhen_model_pos"])
    -- end
end

function FanRenXiuZhenTask:OnClickBtnFanrenxiuzhenRewardPreview()
	local is_red , seq = FuBenPanelWGData.Instance:CheckLevelReward()
	local index = FuBenPanelWGData.Instance:GetFanrenxiuzhenRewardGroupSeq(seq)
	FuBenPanelWGCtrl.Instance:OpenFanRenXiuZhenRewardPreview(index)
end

function FanRenXiuZhenTask:GetGuideUiCallBack(ui_name, ui_param)
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function FanRenXiuZhenTask:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["layout_fanrenxiuzhen"] then
		self.obj = self.node_list["layout_fanrenxiuzhen"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0,0,0)
		self.obj.transform.localScale = Vector3.one
	end

	if self.is_out_fb then
        self.obj:SetActive(false)
    end
    self.is_out_fb = nil
end

function FanRenXiuZhenTask:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function FanRenXiuZhenTask:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function FanRenXiuZhenTask:ShowIndexCallBack()
	self:Flush()
end

function FanRenXiuZhenTask:OnFlush()
	local reward_cfg = FuBenPanelWGData.Instance:GetFanRenXiuZhenRewardItemList(true)
	local cur_chapter = FuBenPanelWGData.Instance:GetChapter()
	local data_list = FuBenPanelWGData.Instance:GetChapterRewardItemList(cur_chapter,true)
	local no_zero_item_data = {}
	for k,v in pairs(data_list) do
		if v.item_id ~= 0 then
			table.insert(no_zero_item_data,v)
		end
	end

	-- if IsEmptyTable(no_zero_item_data) then
	-- 	self.node_list["reward_list_mingwen"]:SetActive(false)
	-- 	self.node_list["tongguan_jinagli"]:SetActive(false)
	-- else
	-- 	self.node_list["tongguan_jinagli"]:SetActive(true)
	-- 	self.node_list["reward_list_mingwen"]:SetActive(true)
	-- end

	-- if self.reward_list_mingwen then
	-- 	self.reward_list_mingwen:SetDataList(no_zero_item_data, 3)
	-- end

	--场景信息is_pass
	local scence_info = FuBenPanelWGData.Instance:GetWelkinInfo()
	if not scence_info then
		return
	end

	if scence_info.is_pass == 0 then --通关时不刷新
		self.boss_reward_list:SetDataList(reward_cfg)
    end

	--当前进入的层数
	local layer = FuBenPanelWGData.Instance:GetWelkinInfo().level
	if layer == nil or layer <= 0 then
		return
	end

	--根据层数确定推荐战力,Boss名字
	local zhanli_tuijian, boss_id = FuBenPanelWGData.Instance:GetNameAndZhanLiByLevel(layer)
	local boss_name = BossWGData.Instance:GetMonsterInfo(boss_id).name
	-- if data_list ~=nil then
	-- 	self.node_list.tongguan_jinagli.text.text = string.format(Language.FuBen.TongGuanJiangLi, data_list[1].level, layer, data_list[1].level)
	-- end
	
	self.node_list.lbl_mubiao.text.text = string.format(Language.FuBen.XiuZhenLuTaskTitle, layer, ToColorStr(boss_name, COLOR3B.D_PURPLE))
	local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
	local is_green = role_zhanli >= zhanli_tuijian	
	self.node_list.tuijian_zhanli.text.text = string.format(Language.FuBen.TuiJianZhanLi, ToColorStr(zhanli_tuijian, is_green and IS_CAN_COLOR.ENOUGH or IS_CAN_COLOR.NOT_ENOUGH))

	-- 后续可以把代码和预制里的一起删除 改了表现形式
	local is_red , seq = FuBenPanelWGData.Instance:CheckLevelReward()
	local cfg = FuBenPanelWGData.Instance:GetFanrenxiuzhenLevelRwawrdDataBySeq(seq)
	local is_show_reward_pre = is_red or not IsEmptyTable(cfg)
	self.node_list.btn_fanrenxiuzhen_reward:SetActive(is_show_reward_pre)
	if is_show_reward_pre then
		self.node_list.text_reward_pre.text.text = string.format(Language.FanRenXiuZhen.RewardLevelStr3, cfg.level)
		self.node_list["fanrenxiuzhen_reward_remind"]:CustomSetActive(is_red)
	end
	-- self.node_list.img_reward_pre:SetActive(is_red or not IsEmptyTable(cfg))

	-- self:XZFlushLevelReawrd()
end

-- 刷新阶段奖励显示
function FanRenXiuZhenTask:XZFlushLevelReawrd()
	local count = FuBenPanelWGData.Instance:GetFanrenxiuzhenLevelRwawrdCount()
	local is_red , seq = FuBenPanelWGData.Instance:CheckLevelReward()

	local cfg = FuBenPanelWGData.Instance:GetFanrenxiuzhenLevelRwawrdDataBySeq(seq)
	self:XZFlushModel(cfg)

	-- self.node_list["fanrenxiuzhen_reward_remind"]:CustomSetActive(is_red)
	-- local is_open = FunOpen.Instance:GetFunIsOpenedByMouduleName(GuideModuleName.FanRenXiuZhenRewardView)
	-- self.node_list["btn_fanrenxiuzhen_reward"]:CustomSetActive(seq ~= count )--and is_open

	-- if seq ~= count then

	-- 	self.node_list.text_reward_pre.text.text = string.format(Language.FanRenXiuZhen.RewardLevelStr2, cfg.level)
	-- end

	
end

function FanRenXiuZhenTask:XZFlushModel(show_model_data)
	local old_data = self.fanrenxiuzhen_model_display:GetData()
    if old_data and old_data.item_id == show_model_data.model_show_itemid then
        return
    end

    local data = {}
    data.item_id = show_model_data.model_show_itemid
    data.render_type = show_model_data.model_show_type - 1
    data.hide_model_block = true
	data.skip_rest_action = true
	data.can_drag = false

    if show_model_data.display_scale ~= nil and show_model_data.display_scale ~= "" then
        data.model_adjust_root_local_scale = show_model_data.display_scale
    end

    if show_model_data.display_rotation ~= nil and show_model_data.display_rotation ~= "" then
        local rota = Split(show_model_data.display_rotation, "|")
        data.role_rotation = Vector3(tonumber(rota[1]), tonumber(rota[2]), tonumber(rota[3]))
    end

    self.fanrenxiuzhen_model_display:SetData(data)
end
---------------------CopperRewardCell--------------------------------
FanRenXiuZhenRewardCell = FanRenXiuZhenRewardCell or BaseClass(BaseRender)
function FanRenXiuZhenRewardCell:__init()
	self.base_cell = ItemCell.New(self.node_list["pos"])
end
function FanRenXiuZhenRewardCell:__delete()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end
function FanRenXiuZhenRewardCell:OnFlush()
	self.base_cell:SetFlushCallBack(function()
			--self.base_cell:SetRightBottomTextVisible(false)
	end)
	self.base_cell:SetData(self.data)
end