﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class System_UriWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(System.Uri), typeof(System.Object));
		<PERSON><PERSON>RegFunction("CheckHostName", CheckHostName);
		<PERSON><PERSON>RegFunction("GetLeftPart", GetLeftPart);
		<PERSON><PERSON>RegFunction("HexEscape", HexEscape);
		<PERSON><PERSON>RegFunction("HexUnescape", HexUnescape);
		L<PERSON>RegFunction("IsHexEncoding", IsHexEncoding);
		<PERSON><PERSON>RegFunction("CheckSchemeName", CheckSchemeName);
		<PERSON><PERSON>RegFunction("IsHexDigit", IsHexDigit);
		<PERSON><PERSON>unction("FromHex", FromHex);
		L.RegFunction("GetHashCode", GetHashCode);
		<PERSON><PERSON>RegFunction("ToString", ToString);
		<PERSON><PERSON>RegFunction("Equals", Equals);
		<PERSON><PERSON>RegFunction("MakeRela<PERSON><PERSON><PERSON>", MakeRelativeUri);
		<PERSON><PERSON>("TryCreate", TryCreate);
		<PERSON><PERSON>unction("GetComponents", GetComponents);
		<PERSON><PERSON>unction("Compare", Compare);
		L.RegFunction("IsWellFormedOriginalString", IsWellFormedOriginalString);
		L.RegFunction("IsWellFormedUriString", IsWellFormedUriString);
		L.RegFunction("UnescapeDataString", UnescapeDataString);
		L.RegFunction("EscapeUriString", EscapeUriString);
		L.RegFunction("EscapeDataString", EscapeDataString);
		L.RegFunction("IsBaseOf", IsBaseOf);
		L.RegFunction("New", _CreateSystem_Uri);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("UriSchemeFile", get_UriSchemeFile, null);
		L.RegVar("UriSchemeFtp", get_UriSchemeFtp, null);
		L.RegVar("UriSchemeGopher", get_UriSchemeGopher, null);
		L.RegVar("UriSchemeHttp", get_UriSchemeHttp, null);
		L.RegVar("UriSchemeHttps", get_UriSchemeHttps, null);
		L.RegVar("UriSchemeMailto", get_UriSchemeMailto, null);
		L.RegVar("UriSchemeNews", get_UriSchemeNews, null);
		L.RegVar("UriSchemeNntp", get_UriSchemeNntp, null);
		L.RegVar("UriSchemeNetTcp", get_UriSchemeNetTcp, null);
		L.RegVar("UriSchemeNetPipe", get_UriSchemeNetPipe, null);
		L.RegVar("SchemeDelimiter", get_SchemeDelimiter, null);
		L.RegVar("AbsolutePath", get_AbsolutePath, null);
		L.RegVar("AbsoluteUri", get_AbsoluteUri, null);
		L.RegVar("LocalPath", get_LocalPath, null);
		L.RegVar("Authority", get_Authority, null);
		L.RegVar("HostNameType", get_HostNameType, null);
		L.RegVar("IsDefaultPort", get_IsDefaultPort, null);
		L.RegVar("IsFile", get_IsFile, null);
		L.RegVar("IsLoopback", get_IsLoopback, null);
		L.RegVar("PathAndQuery", get_PathAndQuery, null);
		L.RegVar("Segments", get_Segments, null);
		L.RegVar("IsUnc", get_IsUnc, null);
		L.RegVar("Host", get_Host, null);
		L.RegVar("Port", get_Port, null);
		L.RegVar("Query", get_Query, null);
		L.RegVar("Fragment", get_Fragment, null);
		L.RegVar("Scheme", get_Scheme, null);
		L.RegVar("OriginalString", get_OriginalString, null);
		L.RegVar("DnsSafeHost", get_DnsSafeHost, null);
		L.RegVar("IdnHost", get_IdnHost, null);
		L.RegVar("IsAbsoluteUri", get_IsAbsoluteUri, null);
		L.RegVar("UserEscaped", get_UserEscaped, null);
		L.RegVar("UserInfo", get_UserInfo, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateSystem_Uri(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				System.Uri obj = new System.Uri(arg0);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string, System.UriKind>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				System.UriKind arg1 = (System.UriKind)ToLua.ToObject(L, 2);
				System.Uri obj = new System.Uri(arg0, arg1);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Uri, string>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				System.Uri obj = new System.Uri(arg0, arg1);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Uri, System.Uri>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				System.Uri arg1 = (System.Uri)ToLua.ToObject(L, 2);
				System.Uri obj = new System.Uri(arg0, arg1);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: System.Uri.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CheckHostName(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			System.UriHostNameType o = System.Uri.CheckHostName(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLeftPart(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			System.Uri obj = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
			System.UriPartial arg0 = (System.UriPartial)ToLua.CheckObject(L, 2, typeof(System.UriPartial));
			string o = obj.GetLeftPart(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HexEscape(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			char arg0 = (char)LuaDLL.luaL_checknumber(L, 1);
			string o = System.Uri.HexEscape(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HexUnescape(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			char o = System.Uri.HexUnescape(arg0, ref arg1);
			LuaDLL.lua_pushnumber(L, o);
			LuaDLL.lua_pushinteger(L, arg1);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsHexEncoding(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			bool o = System.Uri.IsHexEncoding(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CheckSchemeName(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			bool o = System.Uri.CheckSchemeName(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsHexDigit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			char arg0 = (char)LuaDLL.luaL_checknumber(L, 1);
			bool o = System.Uri.IsHexDigit(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FromHex(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			char arg0 = (char)LuaDLL.luaL_checknumber(L, 1);
			int o = System.Uri.FromHex(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetHashCode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Uri obj = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
			int o = obj.GetHashCode();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Uri obj = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
			string o = obj.ToString();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
			System.Uri arg1 = (System.Uri)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Equals(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			System.Uri obj = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
			object arg0 = ToLua.ToVarObject(L, 2);
			bool o = obj != null ? obj.Equals(arg0) : arg0 == null;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MakeRelativeUri(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			System.Uri obj = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
			System.Uri arg0 = (System.Uri)ToLua.CheckObject<System.Uri>(L, 2);
			System.Uri o = obj.MakeRelativeUri(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TryCreate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3 && TypeChecker.CheckTypes<string, System.UriKind, LuaInterface.LuaOut<System.Uri>>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				System.UriKind arg1 = (System.UriKind)ToLua.ToObject(L, 2);
				System.Uri arg2 = null;
				bool o = System.Uri.TryCreate(arg0, arg1, out arg2);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.PushObject(L, arg2);
				return 2;
			}
			else if (count == 3 && TypeChecker.CheckTypes<System.Uri, string, LuaInterface.LuaOut<System.Uri>>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				System.Uri arg2 = null;
				bool o = System.Uri.TryCreate(arg0, arg1, out arg2);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.PushObject(L, arg2);
				return 2;
			}
			else if (count == 3 && TypeChecker.CheckTypes<System.Uri, System.Uri, LuaInterface.LuaOut<System.Uri>>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				System.Uri arg1 = (System.Uri)ToLua.ToObject(L, 2);
				System.Uri arg2 = null;
				bool o = System.Uri.TryCreate(arg0, arg1, out arg2);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.PushObject(L, arg2);
				return 2;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: System.Uri.TryCreate");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetComponents(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			System.Uri obj = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
			System.UriComponents arg0 = (System.UriComponents)ToLua.CheckObject(L, 2, typeof(System.UriComponents));
			System.UriFormat arg1 = (System.UriFormat)ToLua.CheckObject(L, 3, typeof(System.UriFormat));
			string o = obj.GetComponents(arg0, arg1);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Compare(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			System.Uri arg0 = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
			System.Uri arg1 = (System.Uri)ToLua.CheckObject<System.Uri>(L, 2);
			System.UriComponents arg2 = (System.UriComponents)ToLua.CheckObject(L, 3, typeof(System.UriComponents));
			System.UriFormat arg3 = (System.UriFormat)ToLua.CheckObject(L, 4, typeof(System.UriFormat));
			System.StringComparison arg4 = (System.StringComparison)ToLua.CheckObject(L, 5, typeof(System.StringComparison));
			int o = System.Uri.Compare(arg0, arg1, arg2, arg3, arg4);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsWellFormedOriginalString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Uri obj = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
			bool o = obj.IsWellFormedOriginalString();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsWellFormedUriString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			System.UriKind arg1 = (System.UriKind)ToLua.CheckObject(L, 2, typeof(System.UriKind));
			bool o = System.Uri.IsWellFormedUriString(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnescapeDataString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = System.Uri.UnescapeDataString(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EscapeUriString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = System.Uri.EscapeUriString(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EscapeDataString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = System.Uri.EscapeDataString(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsBaseOf(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			System.Uri obj = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
			System.Uri arg0 = (System.Uri)ToLua.CheckObject<System.Uri>(L, 2);
			bool o = obj.IsBaseOf(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeFile(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeFile);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeFtp(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeFtp);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeGopher(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeGopher);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeHttp(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeHttp);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeHttps(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeHttps);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeMailto(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeMailto);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeNews(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeNews);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeNntp(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeNntp);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeNetTcp(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeNetTcp);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UriSchemeNetPipe(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.UriSchemeNetPipe);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SchemeDelimiter(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, System.Uri.SchemeDelimiter);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AbsolutePath(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.AbsolutePath;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AbsolutePath on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AbsoluteUri(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.AbsoluteUri;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AbsoluteUri on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LocalPath(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.LocalPath;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LocalPath on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Authority(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.Authority;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Authority on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_HostNameType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			System.UriHostNameType ret = obj.HostNameType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HostNameType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsDefaultPort(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			bool ret = obj.IsDefaultPort;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsDefaultPort on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsFile(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			bool ret = obj.IsFile;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsFile on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsLoopback(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			bool ret = obj.IsLoopback;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsLoopback on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PathAndQuery(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.PathAndQuery;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PathAndQuery on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Segments(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string[] ret = obj.Segments;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Segments on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsUnc(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			bool ret = obj.IsUnc;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsUnc on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Host(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.Host;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Host on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Port(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			int ret = obj.Port;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Port on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Query(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.Query;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Query on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Fragment(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.Fragment;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Fragment on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Scheme(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.Scheme;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Scheme on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OriginalString(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.OriginalString;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OriginalString on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DnsSafeHost(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.DnsSafeHost;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DnsSafeHost on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IdnHost(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.IdnHost;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IdnHost on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsAbsoluteUri(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			bool ret = obj.IsAbsoluteUri;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsAbsoluteUri on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UserEscaped(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			bool ret = obj.UserEscaped;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index UserEscaped on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UserInfo(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			System.Uri obj = (System.Uri)o;
			string ret = obj.UserInfo;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index UserInfo on a nil value");
		}
	}
}

