GuildBossSceneLogic = GuildBossSceneLogic or BaseClass(CommonFbLogic)

function GuildBossSceneLogic:__init()
	self.open_view = false
end

function GuildBossSceneLogic:__delete()
	self.old_attack_mode = nil
end

function GuildBossSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.GuildBoss.FBName)
		GuildBossWGCtrl.Instance:OpenTaskView()
	end)

	self:SetLeaveFbTip(true)
	GuildWGData.Instance:SetHideGuildBossRemind(true)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_Boss)

	local scene_id = Scene.Instance:GetSceneId()
	local param_t = {scene_id = scene_id}
	Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, 27, 180, nil, param_t)

	local view = GuildBossWGCtrl.Instance:GetTaskView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIRightTopChangeList(view)

	if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end
end

function GuildBossSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function GuildBossSceneLogic:Out()
	CommonFbLogic.Out(self)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	GuildBossWGCtrl.Instance:CloseTaskView()
	GuildBossWGData.Instance:SetCurShowTips(0)
	GuildBossWGCtrl.Instance:CloseTipsView()
	FuBenPanelWGCtrl.Instance:CloseStarAniView()
	TipWGCtrl.Instance:CloseAlertTips()
	GuildBossWGCtrl.Instance:CloseGuildBossReward()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:FlushView(0, "guild_boss_shaizi_state", {false})
	end)

	local view = GuildBossWGCtrl.Instance:GetTaskView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)

	if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end
end

function GuildBossSceneLogic:IsRoleEnemy(target_obj, main_role)
	if target_obj:GetType() == SceneObjType.Role then			-- 同一边
		return false, Language.Fight.Side
	end
	return true
end


-- 是否是挂机打怪的敌人
function GuildBossSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end

function GuildBossSceneLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
end

function GuildBossSceneLogic:CloseLoadingCallBack()
    Scene.Instance:SendFBLoadedScene()
end