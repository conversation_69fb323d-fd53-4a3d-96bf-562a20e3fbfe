﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class PageViewSimpleDelegateWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(PageViewSimpleDelegate), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("NumberOfCellsDel", get_NumberOfCellsDel, set_NumberOfCellsDel);
		<PERSON><PERSON>("CellRefreshDel", get_CellRefreshDel, set_CellRefreshDel);
		<PERSON><PERSON>("CellRecycleDel", get_CellRecycleDel, set_CellRecycleDel);
		<PERSON><PERSON>unction("CellRecycleDelegate", PageViewSimpleDelegate_CellRecycleDelegate);
		<PERSON><PERSON>RegFunction("CellRefreshDelegate", PageViewSimpleDelegate_CellRefreshDelegate);
		<PERSON><PERSON>un<PERSON>("NumberOfCellsDelegate", PageViewSimpleDelegate_NumberOfCellsDelegate);
		<PERSON><PERSON>lass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NumberOfCellsDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PageViewSimpleDelegate obj = (PageViewSimpleDelegate)o;
			PageViewSimpleDelegate.NumberOfCellsDelegate ret = obj.NumberOfCellsDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index NumberOfCellsDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CellRefreshDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PageViewSimpleDelegate obj = (PageViewSimpleDelegate)o;
			PageViewSimpleDelegate.CellRefreshDelegate ret = obj.CellRefreshDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellRefreshDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CellRecycleDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PageViewSimpleDelegate obj = (PageViewSimpleDelegate)o;
			PageViewSimpleDelegate.CellRecycleDelegate ret = obj.CellRecycleDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellRecycleDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_NumberOfCellsDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PageViewSimpleDelegate obj = (PageViewSimpleDelegate)o;
			PageViewSimpleDelegate.NumberOfCellsDelegate arg0 = (PageViewSimpleDelegate.NumberOfCellsDelegate)ToLua.CheckDelegate<PageViewSimpleDelegate.NumberOfCellsDelegate>(L, 2);
			obj.NumberOfCellsDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index NumberOfCellsDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CellRefreshDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PageViewSimpleDelegate obj = (PageViewSimpleDelegate)o;
			PageViewSimpleDelegate.CellRefreshDelegate arg0 = (PageViewSimpleDelegate.CellRefreshDelegate)ToLua.CheckDelegate<PageViewSimpleDelegate.CellRefreshDelegate>(L, 2);
			obj.CellRefreshDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellRefreshDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CellRecycleDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			PageViewSimpleDelegate obj = (PageViewSimpleDelegate)o;
			PageViewSimpleDelegate.CellRecycleDelegate arg0 = (PageViewSimpleDelegate.CellRecycleDelegate)ToLua.CheckDelegate<PageViewSimpleDelegate.CellRecycleDelegate>(L, 2);
			obj.CellRecycleDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellRecycleDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PageViewSimpleDelegate_CellRecycleDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<PageViewSimpleDelegate.CellRecycleDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<PageViewSimpleDelegate.CellRecycleDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PageViewSimpleDelegate_CellRefreshDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<PageViewSimpleDelegate.CellRefreshDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<PageViewSimpleDelegate.CellRefreshDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PageViewSimpleDelegate_NumberOfCellsDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<PageViewSimpleDelegate.NumberOfCellsDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<PageViewSimpleDelegate.NumberOfCellsDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

