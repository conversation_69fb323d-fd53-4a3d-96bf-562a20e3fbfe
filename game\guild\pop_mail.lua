GuildPopMail = GuildPopMail or BaseClass(SafeBaseView)
--群发邮件界面
function GuildPopMail:__init()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_mail")
	self.ui_subject = nil	
	self.ui_content = nil
end

-- function GuildPopMail:__delete()
-- 	if self.pop_alert then
-- 		self.pop_alert:DeleteMe()
-- 		self.pop_alert = nil
-- 	end
-- end

-- function GuildPopMail:LoadCallBack()
-- 	self.pop_alert = Alert.New()
-- 	self.ui_content:setFont(COMMON_CONSTS.FONT, 20)
-- 	self.ui_content:setFontColor(COLOR3B.GLOD)
-- 	self.ui_content:registerScriptEditBoxHandler(BindTool.Bind2(ChatWGData.ExamineEditTextNum, self.ui_content, 120))


-- 	self:RegisterAllEvent()
-- end

-- -- 注册事件
-- function GuildPopMail:RegisterAllEvent()
-- end

-- -- 发送仙盟邮件事件
-- function GuildPopMail:OnSendMailHandler()
-- 	local guildvo = GuildDataConst.GUILDVO
-- 	local contenttxt = self.ui_content:getText()
-- 	if "" == contenttxt then	
-- 		self.pop_alert:Open()	
-- 		self.pop_alert:SetLableString(Language.Guild.NotEmptyContent)

-- 		return
-- 	end
-- 	GuildWGCtrl.Instance:SendGuildMailReq(guildvo.guild_id, subject, 0, contenttxt)
-- end

-- function GuildPopMail:OpenCallBack()

-- end

-- function GuildPopMail:ShowIndexCallBack()
-- 	self.ui_content:setText("")
-- end