FairyLandPlaneDetailView = FairyLandPlaneDetailView or BaseClass(SafeBaseView)
function FairyLandPlaneDetailView:__init()
	self.is_async_load = false
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	local bundle = "uis/view/fairy_land_equipment_ui_prefab"
	self:AddViewResource(0, bundle, "layout_fairy_land_plane_detail_view")

	self.plane_index = -1
end

function FairyLandPlaneDetailView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_open_rule_view, BindTool.Bind(self.OnClickOpenRuleViewBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_check, BindTool.Bind(self.OnClickCheckBtn, self))
end

function FairyLandPlaneDetailView:SetPlaneIndex(index)
    self.plane_index = index
end

function FairyLandPlaneDetailView:ReleaseCallBack()

end

function FairyLandPlaneDetailView:__delete()

end

function FairyLandPlaneDetailView:OpenCallBack()

end

function FairyLandPlaneDetailView:CloseCallBack()

end

function FairyLandPlaneDetailView:OnFlush()
    local book_remind = FairyLandEquipmentWGData.Instance:GeSlotGodBookRemind(self.plane_index)
    self.node_list.remind:SetActive(book_remind)
	local body_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(self.plane_index)
	self.node_list.plane_desc.text.text = body_cfg.plane_desc
	self.node_list.plane_name.text.text = body_cfg.body_name
	self.node_list.jieling_condition.text.text = string.format(Language.FairyLandEquipment.JielingConditionDesc, body_cfg.jieling_name)

	local book_all_cfg = FairyLandEquipmentWGData.Instance:GetGodBookAllCfg()
	local node_num = self.node_list.rule_desc_content.transform.childCount
    for i = 0, node_num - 1 do
        local data = book_all_cfg[i]
        local node = self.node_list.rule_desc_content:FindObj("rule_desc" .. i)
		local progress = FairyLandEquipmentWGData.Instance:GetPageActProgress(self.plane_index, i)
        if data then
            node:SetActive(true)
            node.text.text = string.format(Language.FairyLandEquipment.RuleActProgressDesc, data.title_name, progress)
        else
            node:SetActive(false)
        end
    end
end

function FairyLandPlaneDetailView:OnClickOpenRuleViewBtn()
    local max_show_num = FairyLandEquipmentWGData.Instance:GetGodBodyMaxShowNum()
    if self.plane_index > max_show_num then
        TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.PlaneNotActTip)
    else
        local bundle_name, asset_name = ResPath.GetEffectUi("UI_yuanshen_cs")
        self.node_list.block:SetActive(true)
        EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect"].transform, 1.7, nil, nil, nil, nil, function ()
            self.node_list.block:SetActive(false)
            FairyLandEquipmentWGCtrl.Instance:OpenRuleViewAndSetData(self.plane_index)
            self:Close()
        end)
    end
end

function FairyLandPlaneDetailView:OnClickCheckBtn()
    local body_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(self.plane_index)
    local item_id = body_cfg.show_item_id
    TipWGCtrl.Instance:OpenItem({item_id = item_id})
end