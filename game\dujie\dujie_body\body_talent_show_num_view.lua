BodyTalentShowNumView = BodyTalentShowNumView or BaseClass(SafeBaseView)

function BodyTalentShowNumView:__init()
	self:SetMaskBg(true,true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/dujie_body_ui_prefab", "layout_talent_show_num_view")
end

function BodyTalentShowNumView:LoadCallBack()

end

function BodyTalentShowNumView:ReleaseCallBack()


end

function BodyTalentShowNumView:CloseCallBack()
    if self.data then
        DujieWGCtrl.Instance:FlushBodyViewAutoSelect(self.data)
    end
    DujieWGData.Instance:SetNewTalentNum(0)
    self.data  = nil
end


function BodyTalentShowNumView:OnFlush()
    if not self.data then
        return 
    end
    local num = DujieWGData.Instance:GetNewTalentNum()
    self.node_list.text_count.tmp.text = string.format(Language.Dujie.NewTalentNumStr,num)
end

function BodyTalentShowNumView:SetData(data)
    self.data = data
end







