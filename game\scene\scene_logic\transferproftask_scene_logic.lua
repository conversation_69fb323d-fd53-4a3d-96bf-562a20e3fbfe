TransferProfTaskSceneLogic = TransferProfTaskSceneLogic or BaseClass(BaseFbLogic)

function TransferProfTaskSceneLogic:__init()
	BaseFbLogic.__init(self)
end

function TransferProfTaskSceneLogic:__delete()

end

function TransferProfTaskSceneLogic:Enter(old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()
	BaseFbLogic.Enter(self, old_scene_type, new_scene_type)
	self:SetGuaiJi(GUAI_JI_TYPE.MONSTER)
end

function TransferProfTaskSceneLogic:Out()
	BaseFbLogic.Out(self)
end