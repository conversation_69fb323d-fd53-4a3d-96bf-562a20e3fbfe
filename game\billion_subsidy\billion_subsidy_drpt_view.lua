-----------------------------------
-- 百亿补贴-多人拼团
-----------------------------------
local GRADIENT_COLOR =
{
	Activated = {gradient1 = "#79fa83",gradient2 = "#fdf6d6"},
	NotActivated = {gradient1 = "#FFEB89",gradient2 = "#FDF6D6"}
}

function BillionSubsidyView:DRPTReleaseCallBack()
	if self.drpt_tab_list then
		self.drpt_tab_list:DeleteMe()
		self.drpt_tab_list = nil
	end

	if self.drpt_reward_list then
		self.drpt_reward_list:DeleteMe()
		self.drpt_reward_list = nil
	end

	if self.player_item_list then
		for key, value in pairs(self.player_item_list) do
			value:DeleteMe()
			value = nil
		end
		self.player_item_list = nil
	end

	self:DRPTCleanTimer()

	self.drpt_jump_tab_seq = nil
	self.drpt_select_tab_seq = nil
	self.drpt_select_list_index = nil
end

function BillionSubsidyView:DRPTCloseCallBack()
	self.drpt_jump_tab_seq = nil
	self.drpt_select_tab_seq = -1
	self.drpt_select_list_index = -1
end

function BillionSubsidyView:DRPTLoadIndexCallBack()
	if not self.drpt_tab_list then
		self.drpt_tab_list = AsyncListView.New(BillionSubsidyDRPTTabCell, self.node_list.drpt_tab_list)
		self.drpt_tab_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectDRPTTabItemCallBack, self))
	end

	if not self.drpt_reward_list then
		self.drpt_reward_list = AsyncListView.New(ItemCell, self.node_list.drpt_item_list)
		self.drpt_reward_list:SetStartZeroIndex(true)
	end

	if not self.player_item_list then
		self.player_item_list = {}
		for idx = 1, DRPT_MAX_PLAYER_NUM do
			self.player_item_list[idx] = BillionSubsidyDRPTPlayerItem.New(self.node_list["drpt_player_item_" .. idx])
		end
	end

	for i = 1, 2 do
		XUI.AddClickEventListener(self.node_list["drpt_buy_btn_" .. i], BindTool.Bind(self.OnClickDRPTBuyBtn, self))
	end
	XUI.AddClickEventListener(self.node_list["drpt_get_btn"], BindTool.Bind(self.OnClickDRPTGetRewardBtn, self))
	XUI.AddClickEventListener(self.node_list["drpt_invite_btn"], BindTool.Bind(self.OnClickDRPTInviteBtn, self))

	self.drpt_jump_tab_seq = nil
	self.drpt_select_tab_seq = -1
	self.drpt_select_list_index = -1
end

function BillionSubsidyView:DRPTShowIndexCallBack()

end

--ViewManager.Instance:Open(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_drpt, "jump_tab", {jump_tab = index})外部跳转到对应页签.
function BillionSubsidyView:DRPTOnFlush(param_t, index)
	local to_tab = nil
	for k_1, v_1 in pairs(param_t or {}) do
		if k_1 == "jump_tab" then
			to_tab = v_1.jump_tab
			break
		end
	end

	self:FlushDRPTTabList(to_tab)
	self:FlushDRPTPanel()
	self:FlushDRPTPlayerList()
end

function BillionSubsidyView:FlushDRPTTabList(to_tab)
	local show_list = BillionSubsidyWGData.Instance:GetDRPTData()

	if self.drpt_jump_tab_seq == nil then
		self.drpt_jump_tab_seq = 0

		for index, temp_data in ipairs(show_list) do
			if to_tab then
				if to_tab == temp_data.item_seq then
					self.drpt_jump_tab_seq = index
					break
				end
			else
				--跳红点.
				local is_red = 0
				if is_red then
					self.drpt_jump_tab_seq = index
					break
				end
			end
		end
	end

	local is_have = BillionSubsidyWGData.Instance:GetDRPTIsHaveNotEndData()
	self.node_list.drpt_had_data_root:SetActive(is_have)
	self.node_list.drpt_no_data_root:SetActive(not is_have)
	self.node_list.drpt_tab_list:SetActive(show_list and #show_list > 0)

	if self.drpt_tab_list ~= nil then
		self.drpt_tab_list:SetDataList(show_list)
		if self.drpt_jump_tab_seq then
			if self.drpt_jump_tab_seq == 0 then
				self.drpt_tab_list:JumpToIndex(1, 5)
				self.drpt_jump_tab_seq = 100
			elseif self.drpt_jump_tab_seq ~= 100 then
				self.drpt_tab_list:JumpToIndex(self.drpt_jump_tab_seq, 5)
				self.drpt_jump_tab_seq = 100
			end
		end
	end
end

function BillionSubsidyView:OnSelectDRPTTabItemCallBack(item)
	if nil == item or nil == item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = item:GetIndex()
	if self.drpt_select_list_index == cell_index then
		return
	end

	self.drpt_select_list_index = cell_index
	self.drpt_select_tab_seq = item.data.item_seq

	--刷新界面.
	self:FlushDRPTPanel()
	self:FlushDRPTPlayerList()
	self:DRPTLoginTimeCountDown()
end

function BillionSubsidyView:FlushDRPTPanel()
	local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeq(self.drpt_select_tab_seq)
	if not drpt_cfg then
		return
	end

	local data_list = drpt_cfg.reward
	if not IsEmptyTable(data_list) then
		self.drpt_reward_list:SetDataList(data_list)
	end

	self.node_list.drpt_buy_text.text.text = string.format(Language.BillionSubsidy.DRPTBuyPriceText, drpt_cfg.price)
	self.node_list.drpt_tips_desc.text.text = string.format(Language.BillionSubsidy.DRPTBuyLimitText, drpt_cfg.fetch_reward_participate_num_limit, drpt_cfg.max_participate_num)

	local player_num = BillionSubsidyWGData.Instance:GetDRPTPlayerNumBySeq(self.drpt_select_tab_seq)
	local role_can_get_num = BillionSubsidyWGData.Instance:GetDRPTCanGetRewardNumBySeq(self.drpt_select_tab_seq)
	self.node_list.drpt_get_remind:SetActive(role_can_get_num > 0 and player_num >= drpt_cfg.fetch_reward_participate_num_limit)

	local drpt_get_btn_text = role_can_get_num > 0 and player_num >= drpt_cfg.fetch_reward_participate_num_limit
			and string.format(Language.BillionSubsidy.DRPTGetRewardBtnText, role_can_get_num) or Language.BillionSubsidy.GetRewardBtnText

	self.node_list.drpt_get_btn_text.text.text = drpt_get_btn_text
	
	local role_buy_num = BillionSubsidyWGData.Instance:GetDRPTRoleNumBySeq(self.drpt_select_tab_seq)
	local is_receive = BillionSubsidyWGData.Instance:GetDRPTIsGetRewardBySeq(self.drpt_select_tab_seq)
	self.node_list.drpt_buy_btn_1:SetActive(role_buy_num <= 0)
	self.node_list.drpt_buy_btn_2:SetActive(false)	--屏蔽按钮
	self.node_list.drpt_get_btn:SetActive(role_buy_num > 0)
	self.node_list.drpt_btn_group:SetActive(not is_receive)
	self.node_list.drpt_receive_flag:SetActive(is_receive)
end

function BillionSubsidyView:FlushDRPTPlayerList()
	local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeq(self.drpt_select_tab_seq)
	if not drpt_cfg then
		return
	end

	local participate_info = BillionSubsidyWGData.Instance:GetDRPTPlayerInfoBySeq(self.drpt_select_tab_seq)
	if not participate_info then
		return
	end

	for i = 1, DRPT_MAX_PLAYER_NUM do
		self.node_list["drpt_player_item_" .. i]:SetActive(i <= drpt_cfg.max_participate_num)
		if i <= drpt_cfg.max_participate_num and participate_info[i - 1] then
			self.player_item_list[i]:SetData(participate_info[i - 1])
		end
	end
end

--活动时间倒计时
function BillionSubsidyView:DRPTLoginTimeCountDown()
	local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeq(self.drpt_select_tab_seq)
	if not drpt_cfg then
		return
	end

	local act_state, start_time, rest_time = BillionSubsidyWGData.Instance:GetDRPTItemIsOpenBySeq(self.drpt_select_tab_seq)

	local color_type = GRADIENT_COLOR.NotActivated
	local count_down_text = ""
	if act_state == BillionSubsidyWGData.DRPTActState.NotTime then
		local time_tab = TimeUtil.Format2TableDHMS(start_time)
		local time_hour = time_tab.hour > 0 and time_tab.hour or 0
		count_down_text = string.format(Language.BillionSubsidy.CountDownText, time_hour)
		color_type = GRADIENT_COLOR.NotActivated
	elseif act_state == BillionSubsidyWGData.DRPTActState.TimeEnd then
		count_down_text = Language.BillionSubsidy.CountDownEndText
		color_type = GRADIENT_COLOR.NotActivated
	elseif act_state == BillionSubsidyWGData.DRPTActState.Activated then
		count_down_text = TimeUtil.FormatSecondDHM6(rest_time)
		color_type = GRADIENT_COLOR.Activated
	end

	self.node_list.drpt_count_down_tips.text.text = Language.BillionSubsidy.CountDownTips[act_state]
	self.node_list.drpt_count_down_text.text.text = count_down_text
	-- TODO 需要改为新版处理方式
	--self.node_list.drpt_count_down_text.gradient.Color1 = StrToColor(color_type.gradient1)
	--self.node_list.drpt_count_down_text.gradient.Color2 = StrToColor(color_type.gradient2)

	self.node_list.drpt_count_down_text.text.colorGradient = TMPro.VertexGradient(
		StrToColor(color_type.gradient1),
		StrToColor(color_type.gradient1),
		StrToColor(color_type.gradient2),
		StrToColor(color_type.gradient2)
	)

	self:DRPTCleanTimer()
	if act_state == BillionSubsidyWGData.DRPTActState.Activated and not self.bs_drpt_down then
		self.bs_drpt_down = CountDown.Instance:AddCountDown(rest_time, 1,
			-- 回调方法
			function(elapse_time, total_time)
				local valid_time = total_time - elapse_time
				self.node_list.drpt_count_down_text.text.text = TimeUtil.FormatSecondDHM6(valid_time)
			end,
			-- 倒计时完成回调方法
			function()
				self.node_list.drpt_count_down_tips.text.text = Language.BillionSubsidy.CountDownTips[BillionSubsidyWGData.DRPTActState.TimeEnd]
				self.node_list.drpt_count_down_text.text.text = Language.BillionSubsidy.CountDownEndText
			end
		)
	end
end

function BillionSubsidyView:DRPTCleanTimer()
	if self.bs_drpt_down and CountDown.Instance:HasCountDown(self.bs_drpt_down) then
		CountDown.Instance:RemoveCountDown(self.bs_drpt_down)
		self.bs_drpt_down = nil
	end
end

function BillionSubsidyView:OnClickDRPTBuyBtn()
	local act_state, start_time, rest_time = BillionSubsidyWGData.Instance:GetDRPTItemIsOpenBySeq(self.drpt_select_tab_seq)
	if act_state == BillionSubsidyWGData.DRPTActState.TimeEnd then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.CountDownTips[act_state])
		return
	end

	local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeq(self.drpt_select_tab_seq)
	if not drpt_cfg then
		return
	end

	local player_num = BillionSubsidyWGData.Instance:GetDRPTPlayerNumBySeq(self.drpt_select_tab_seq)

	if player_num >= drpt_cfg.max_participate_num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.DRPTJoinPlayerNumMax)
		return
	end

	TipWGCtrl.Instance:OpenAlertTips(string.format(Language.BillionSubsidy.DRPTAlertFormat, drpt_cfg.price), function ()
		BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.PARTICIPATE_BUY, self.drpt_select_tab_seq)
	end)
end

function BillionSubsidyView:OnClickDRPTGetRewardBtn()
	local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeq(self.drpt_select_tab_seq)
	if not drpt_cfg then
		return
	end

	local player_num = BillionSubsidyWGData.Instance:GetDRPTPlayerNumBySeq(self.drpt_select_tab_seq)
	if player_num < drpt_cfg.fetch_reward_participate_num_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.DRPTNotJoinPlayerNum)
		return
	end

	local role_can_get_num = BillionSubsidyWGData.Instance:GetDRPTCanGetRewardNumBySeq(self.drpt_select_tab_seq)
	if role_can_get_num <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.DRPTNotGetRewardNum)
		return
	end

	BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_PARTICIPATE_REWARD, self.drpt_select_tab_seq)
end

function BillionSubsidyView:OnClickDRPTInviteBtn()
	local act_state, start_time, rest_time = BillionSubsidyWGData.Instance:GetDRPTItemIsOpenBySeq(self.drpt_select_tab_seq)
	if act_state == BillionSubsidyWGData.DRPTActState.TimeEnd then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.CountDownTips[act_state])
		return
	end

	local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeq(self.drpt_select_tab_seq)
	if not drpt_cfg then
		return
	end

	local player_num = BillionSubsidyWGData.Instance:GetDRPTPlayerNumBySeq(self.drpt_select_tab_seq)

	if player_num >= drpt_cfg.max_participate_num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.DRPTJoinPlayerNumMax)
		return
	end

	BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.INVITE_PARTICIPATE_REWARD, self.drpt_select_tab_seq)
end

-------------------------------------BillionSubsidyDRPTTabCell-------------------------------------
BillionSubsidyDRPTTabCell = BillionSubsidyDRPTTabCell or BaseClass(BaseRender)

function BillionSubsidyDRPTTabCell:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.name.text.text = self.data.tab_name
    self.node_list.name_hl.text.text = self.data.tab_name

	local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeq(self.data.item_seq)
	if not drpt_cfg then
		return
	end

	local act_state, start_time, rest_time = BillionSubsidyWGData.Instance:GetDRPTItemIsOpenBySeq(self.data.item_seq)

	local count_down_text = ""
	if act_state == BillionSubsidyWGData.DRPTActState.NotTime then
		local time_tab = TimeUtil.Format2TableDHMS(start_time)
		local time_hour = time_tab.hour > 0 and time_tab.hour or 0
		count_down_text = string.format(Language.BillionSubsidy.CountDownText, time_hour)
	elseif act_state == BillionSubsidyWGData.DRPTActState.TimeEnd then
		count_down_text = Language.BillionSubsidy.CountDownText2
	elseif act_state == BillionSubsidyWGData.DRPTActState.Activated then
		count_down_text = Language.BillionSubsidy.CountDownText3
	end

	self.node_list.desc.text.text = count_down_text
	self.node_list.desc_hl.text.text = count_down_text

	local is_red = BillionSubsidyWGData.Instance:GetDRPTRemindBySeq(self.data.item_seq)
	self.node_list.RedPoint:SetActive(is_red)
end

function BillionSubsidyDRPTTabCell:OnSelectChange(is_select)
    self.node_list.HLImage:SetActive(is_select)
end

---------------------------------------BillionSubsidyDRPTPlayerItem----------------------------------
BillionSubsidyDRPTPlayerItem = BillionSubsidyDRPTPlayerItem or BaseClass(BaseRender)

function BillionSubsidyDRPTPlayerItem:LoadCallBack()
	if not self.role_head then
		self.role_head = BaseHeadCell.New(self.node_list.player_node)
		self.role_head:SetBgActive(false)
	end

	if not self.role_avatar then
		self.role_avatar = RoleHeadCell.New(false)
		self.role_avatar:AddCustomMenu(Language.Menu.ShowInfo, Language.Menu.AddFriend)
	end

	XUI.AddClickEventListener(self.node_list.head_btn, BindTool.Bind(self.OnClickMainRoleHead, self))
end

function BillionSubsidyDRPTPlayerItem:ReleaseCallBack()
	if self.role_head then
		self.role_head:DeleteMe()
		self.role_head = nil
	end

	if self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end
end

function BillionSubsidyDRPTPlayerItem:OnFlush()
	if not self.data then
		return
	end

	self.node_list.name.text.text = self.data.user_name

	self:SetHeadIcon()
end

function BillionSubsidyDRPTPlayerItem:SetHeadIcon()
	self.node_list.bg_1:SetActive(self.data.uid == 0)
	self.node_list.bg_2:SetActive(self.data.uid ~= 0)
	self.role_head:SetActive(self.data.uid ~= 0)
	self.node_list.head_btn:SetActive(self.data.uid ~= 0)

	if self.data.uid ~= 0 then
		local data = {}
		data.role_id = self.data.uid
		data.prof = self.data.prof
		data.sex = self.data.sex
		self.role_head:SetData(data)
		self.role_head:SetImgBg(true)
	end
end

function BillionSubsidyDRPTPlayerItem:OnClickMainRoleHead()
	if not self.data or self.data.uid == 0 then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	--判断是否是自己.
	if self.data.uid == role_id then
		ViewManager.Instance:Open(GuideModuleName.RoleView, TabIndex.role_intro)
	else
		local role_info = {
			role_id =self.data.uid,
			role_name = self.data.name,
			prof = self.data.prof,
			sex = self.data.sex,
			is_online = true,
			-- server_id = self.data.usid.temp_low,
		}
		self.role_avatar:SetRoleInfo(role_info)
		self.role_avatar:OpenMenu(nil, nil, nil, MASK_BG_ALPHA_TYPE.Normal)
	end
end