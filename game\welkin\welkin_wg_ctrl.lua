require("game/welkin/welkin_wg_data")
require("game/welkin/welkin_view")

-- 日常
WelkinWGCtrl = WelkinWGCtrl or BaseClass(BaseWGCtrl)

function WelkinWGCtrl:__init()
	if WelkinWGCtrl.Instance then
		error("[WelkinWGCtrl]:Attempt to create singleton twice!")
	end
	WelkinWGCtrl.Instance = self

	self.data = WelkinData.New()
	self.view = WelkinView.New()

	self:RegisterAllProtocals()
	self:RegisterAllRemind()
end

function WelkinWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	WelkinWGCtrl.Instance = nil
end

function WelkinWGCtrl:Open(index, param_t)
	self.view:Open(index)

end

function WelkinWGCtrl:Close()
	if self.view then
		self.view:Close()
	end
end

function WelkinWGCtrl:RegisterAllProtocals()
	--天仙阁
	--self:RegisterProtocol(CSTianxiangeFbOperate)
	--self:RegisterProtocol(SCTianxiangeInfo, "OnTianxiangeInfo")
	--self:RegisterProtocol(SCTianxiangeSceneInfo, "OnTianxiangeSceneInfo")
end

function WelkinWGCtrl:RegisterAllRemind()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function WelkinWGCtrl:MainuiOpenCreate()
	self:SendPataFbInfo()
end

-------- 天仙阁

function WelkinWGCtrl:SendPataFbInfo()
	self:SendTianxiangeFbOperate(PATA_FB_OPERA_TYPE.TIANXIANGEFB_REQ_INFO)
end
function WelkinWGCtrl:SendPataFbRewrad()
	self:SendTianxiangeFbOperate(PATA_FB_OPERA_TYPE.TIANXIANGEFB_FETCH_DAILY_REWARD)
end
function WelkinWGCtrl:SendTianxiangeFbOperate(operate, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianxiangeFbOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end


function WelkinWGCtrl:OnTianxiangeInfo(protocol)
	-- Log("---------WelkinWGCtrl:OnTianxiangeInfo")
	local pass_level = protocol.level
	local pass_fetch_level = protocol.fetch_level
	self.data:SetPassLevel(pass_level)
	self.data:SetPassFetchLevel(pass_fetch_level)
	RoleWGData.Instance:SetAttr("tianxiange_level", pass_level)
	Scene.Instance:GetMainRole():UpdateNameBoard()
	-- self.view:Flush(DailyViewIndex.Pata)

	-- Remind.Instance:DoRemind(RemindId.daily_fuben)
end

function WelkinWGCtrl:OnTianxiangeSceneInfo(protocol)
	-- Log("---------WelkinWGCtrl:OnTianxiangeSceneInfo")
	if 1 == protocol.is_finish then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = protocol.scene_type
		pass_vo.is_pass = protocol.is_pass
		pass_vo.reward_list = {}
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time_s), COLOR3B.GREEN , 24)
		pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(self.data:GetCurTitleName(protocol.level), COLOR3B.YELLOW , 24)
		pass_vo.tip3 = Language.FuBen.NextLevel
		if 0 == protocol.is_pass then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end
		if 0 ~= pass_vo.is_pass then
			FuBenWGCtrl.Instance:OpenFuBenNextView(pass_vo)
		else
			pass_vo.tip4 = HtmlTool.GetHtml(self.data:GetCurTitleName(protocol.level), COLOR3B.YELLOW , 24)
			pass_vo.tip5 = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[protocol.level + 30400].name
			pass_vo.level = protocol.level
			FuBenWGCtrl.Instance:OpenFuBenLoseView()
		end
	end
end
