HappyConsumeWGData = HappyConsumeWGData or BaseClass()

function HappyConsumeWGData:__init()
	if HappyConsumeWGData.Instance then
		error("[HappyConsumeWGData] Attempt to create singleton twice!")
		return
	end

	HappyConsumeWGData.Instance = self
	
	self.happy_consume_count = 0
	self.happy_consume_exchange_flag = {}
end

function HappyConsumeWGData:__delete()
	HappyConsumeWGData.Instance = nil
end

function HappyConsumeWGData:SetTotalJiFen(happy_consume_count,activity_open_day)
	self.happy_consume_count = happy_consume_count
	self.activity_open_day = activity_open_day
end

function HappyConsumeWGData:GetTotalJiFen()
	return self.happy_consume_count
end

function HappyConsumeWGData:GetActivityOpenDay()
	return self.activity_open_day or 0
end

function HappyConsumeWGData:SetComsumeExchangeFlag(happy_consume_exchange_flag)
	self.happy_consume_exchange_flag = happy_consume_exchange_flag
end

function HappyConsumeWGData:GetComsumeExchangeFlag()
	return self.happy_consume_exchange_flag
end

function HappyConsumeWGData:GetConsumeData()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_HAPPY_CUNSUME)
	local openday_time = TimeWGCtrl.Instance:GetServerRealStartTime()
	local day  = self:GetActivityOpenDay()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = rand_config.rand_happy_consume
	local cousume_cfg = ServerActivityWGData.Instance:GetRandHappyActivity(rand_t, day)
	local data = table.remove(cousume_cfg, 1)
	cousume_cfg[0] = data
	return cousume_cfg
end

function HappyConsumeWGData:GetConsumeConfg(seq)
	local consume_cfg = self:GetConsumeData()
	for k,v in pairs(consume_cfg) do
		if seq == v.seq then
			return v
		end
	end
	return nil
end

function HappyConsumeWGData:GetHappyConsumeCNum()
	local num = 0
	local happy_consume_exchange_flag = self:GetComsumeExchangeFlag()
	local happy_consume_count = HappyConsumeWGData.Instance:GetTotalJiFen()
	local consume_cfg = self:GetConsumeData()
	if next(happy_consume_exchange_flag) then
		for i=0, GameEnum.RAND_ACTIVITY_HAPPY_CONSUME_LEVEL_MAX_COUNT - 1 do
			local cur_exchange_times = happy_consume_exchange_flag[i]
			local consume_cfg = HappyConsumeWGData.Instance:GetConsumeConfg(i)
			if consume_cfg then
				local can_exchange_times = consume_cfg.can_exchange_times
				local left_exchange_times = can_exchange_times - cur_exchange_times
				local cur_need_jifen = consume_cfg.consume_point + cur_exchange_times * consume_cfg.add_consume_point_once
				if happy_consume_count >= cur_need_jifen and left_exchange_times > 0 then
					num = num + 1
				end
			end
	    end
	end
	return num
end
