WorldTreasureTSJLFBView = WorldTreasureTSJLFBView or BaseClass(SafeBaseView)

function WorldTreasureTSJLFBView:__init(view_name)
	self.view_name = "WorldTreasureTSJLFBView"
	self.view_layer = UiLayer.MainUILow
	self.view_cache_time = 0
	self:AddViewResource(0, "uis/view/world_treasure_ui_prefab", "layout_tsjl_fb_view")
end

function WorldTreasureTSJLFBView:LoadCallBack()
	self:InitPanel()
	self:RefreshRewardList()
end

function WorldTreasureTSJLFBView:ReleaseCallBack()
	if self.reward_partake_list then
		self.reward_partake_list:DeleteMe()
		self.reward_partake_list = nil
	end

	if self.reward_mabay_list then
		self.reward_mabay_list:DeleteMe()
		self.reward_mabay_list = nil
	end

	if not IsNil(self.obj) then
        ResMgr:Destroy(self.obj)
    end
    self.obj = nil

    CountDownManager.Instance:RemoveCountDown("tsjl_fb_count_down")
end

function WorldTreasureTSJLFBView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:RefreshView()
			self:RefreshRewardList()
		elseif k == "boss_state" then
			print_error("boss状态",param_t)
		end
	end
end

function WorldTreasureTSJLFBView:InitPanel()
	self.reward_partake_list = AsyncListView.New(ItemCell, self.node_list.reward_partake_list)
	self.reward_mabay_list = AsyncListView.New(ItemCell, self.node_list.reward_mabay_list)
	MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.XINGTIANLAIXI, BindTool.Bind(self.InitCallBack, self))
	MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD, BindTool.Bind(self.InitCallBack, self))

	self.node_list.text_next_round.text.text = Language.WorldTreasure.JianLinFbTimeStr
end

function WorldTreasureTSJLFBView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	self.obj = self.node_list["layout_view"].gameObject
	self.obj.transform:SetParent(parent.gameObject.transform, false)

	MainuiWGCtrl.Instance:SetAutoGuaJi(true)
end

function WorldTreasureTSJLFBView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function WorldTreasureTSJLFBView:RefreshRewardList()
	local reward_cfg = WorldTreasureWGData.Instance:GetJiangLinReward()
	if reward_cfg and reward_cfg.reward_show then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(reward_cfg.reward_show)
		self.reward_mabay_list:SetDataList(data_list)
	end

	local boss_drop_cfg = WorldTreasureWGData.Instance:GetBossDropCfg()
	if boss_drop_cfg then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(boss_drop_cfg.random_item)
		self.reward_partake_list:SetDataList(data_list)
	end
end

function WorldTreasureTSJLFBView:RefreshView()
	local is_in,next_refresh_time= WorldTreasureWGData.Instance:IsInShiLianActivity()
	if is_in and next_refresh_time then
		self:FlushFBCountDown(next_refresh_time.end_timestemp or 0)
	else
		self:FlushFBCountDown(0)
	end
end

function WorldTreasureTSJLFBView:FlushFBCountDown(next_time)
	CountDownManager.Instance:RemoveCountDown("tsjl_fb_count_down")
	local ser_time = TimeWGCtrl.Instance:GetServerTime()
	local count_down_time = math.floor(next_time - ser_time)
	if count_down_time > 0 then
		self.node_list.next_round_time.text.text = TimeUtil.FormatSecondDHM4(count_down_time)
		CountDownManager.Instance:AddCountDown("tsjl_fb_count_down", BindTool.Bind(self.UpdateFBCountDown, self), nil, nil, count_down_time, 1)
	else
		self.node_list.next_round_time.text.text = ""
	end
end

function WorldTreasureTSJLFBView:UpdateFBCountDown(elapse_time, total_time)
	self.node_list.next_round_time.text.text = TimeUtil.FormatSecondDHM4(total_time - elapse_time)
end