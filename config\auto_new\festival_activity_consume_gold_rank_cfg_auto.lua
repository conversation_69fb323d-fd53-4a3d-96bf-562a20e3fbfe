-- J-节日活动-仙玉消费.xls
local item_table={
[1]={item_id=48478,num=2,is_bind=1},
[2]={item_id=37438,num=1,is_bind=1},
[3]={item_id=48117,num=1,is_bind=1},
[4]={item_id=26193,num=2,is_bind=1},
[5]={item_id=48118,num=2,is_bind=1},
[6]={item_id=26410,num=45,is_bind=1},
[7]={item_id=48478,num=1,is_bind=1},
[8]={item_id=26193,num=1,is_bind=1},
[9]={item_id=48118,num=1,is_bind=1},
[10]={item_id=29615,num=8,is_bind=1},
[11]={item_id=26410,num=25,is_bind=1},
[12]={item_id=28920,num=1,is_bind=1},
[13]={item_id=26191,num=3,is_bind=1},
[14]={item_id=29615,num=6,is_bind=1},
[15]={item_id=26409,num=25,is_bind=1},
[16]={item_id=48073,num=1,is_bind=1},
[17]={item_id=26191,num=2,is_bind=1},
[18]={item_id=29615,num=5,is_bind=1},
[19]={item_id=26191,num=1,is_bind=1},
[20]={item_id=29615,num=4,is_bind=1},
[21]={item_id=26409,num=15,is_bind=1},
[22]={item_id=37442,num=1,is_bind=1},
[23]={item_id=48120,num=1,is_bind=1},
[24]={item_id=26411,num=45,is_bind=1},
[25]={item_id=37445,num=1,is_bind=1},
[26]={item_id=26410,num=15,is_bind=1},
[27]={item_id=26411,num=25,is_bind=1},
}

return {
config_param={
{},
{start_server_day=21,end_server_day=40,grade=1,},
{start_server_day=41,end_server_day=60,grade=2,},
{start_server_day=61,end_server_day=9999,grade=3,}
},

config_param_meta_table_map={
},
reward={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{rank_hight=2,rank_low=3,reward_item={[0]=item_table[7],[1]=item_table[3],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11]},reach_value=100000,},
{rank_hight=4,rank_low=7,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[9],[3]=item_table[14],[4]=item_table[15]},reach_value=40000,},
{rank_hight=8,rank_low=12,reward_item={[0]=item_table[16],[1]=item_table[17],[2]=item_table[9],[3]=item_table[18],[4]=item_table[15]},reach_value=15000,},
{rank_hight=13,rank_low=25,reward_item={[0]=item_table[16],[1]=item_table[19],[2]=item_table[9],[3]=item_table[20],[4]=item_table[21]},reach_value=5000,},
{grade=1,reward_item={[0]=item_table[1],[1]=item_table[22],[2]=item_table[23],[3]=item_table[4],[4]=item_table[5],[5]=item_table[24]},},
{grade=1,rank_hight=2,rank_low=3,reach_value=100000,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,reward_item={[0]=item_table[1],[1]=item_table[25],[2]=item_table[23],[3]=item_table[4],[4]=item_table[5],[5]=item_table[24]},},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,reward_item={[0]=item_table[16],[1]=item_table[19],[2]=item_table[9],[3]=item_table[20],[4]=item_table[26]},},
{grade=3,},
{grade=3,},
{grade=3,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[9],[3]=item_table[14],[4]=item_table[11]},},
{grade=3,reward_item={[0]=item_table[16],[1]=item_table[17],[2]=item_table[9],[3]=item_table[18],[4]=item_table[11]},},
{grade=3,}
},

reward_meta_table_map={
[16]=6,	-- depth:1
[17]=7,	-- depth:1
[12]=17,	-- depth:2
[18]=3,	-- depth:1
[15]=5,	-- depth:1
[10]=15,	-- depth:2
[13]=18,	-- depth:2
[19]=4,	-- depth:1
[9]=19,	-- depth:2
[8]=13,	-- depth:3
[14]=9,	-- depth:3
[20]=10,	-- depth:3
},
view_cfg={
{},
{grade=1,},
{grade=2,},
{grade=3,}
},

view_cfg_meta_table_map={
},
config_param_default_table={start_server_day=1,end_server_day=20,grade=0,rank_limit=300,rank_show_num=100,},

reward_default_table={grade=0,rank_hight=1,rank_low=1,reward_item={[0]=item_table[7],[1]=item_table[3],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10],[5]=item_table[27]},reach_value=500000,},

view_cfg_default_table={grade=0,model_id=0,render_type=1,render_string_param1="uis/view/festival_activity_ui/sp_rank_prefab",render_string_param2="render_prefab",show_name="",title_name_color="#fef4e3",common_txt_color="#6d2f25",left_txt_color="#fbedda",down_txt_color="#ffdda2",activity_des="1.消费<color=#6FBB6F>灵玉数量</color>达到上榜要求的仙友才能上榜\n2.活动第<color=#6FBB6F>3天23</color>点结算，请广大仙友切莫错失良机",}

}

