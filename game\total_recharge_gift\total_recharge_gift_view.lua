---------------------------------
-- 累充豪礼View
---------------------------------
TotalRechargeGiftView = TotalRechargeGiftView or BaseClass(SafeBaseView)

local SLDER_VALUE = { 0.1, 0.3, 0.5, 0.67, 0.85, 1 }
local RECHARGE_TYPE =
{
	ZhenChong = 1,
	ShiFu = 2
}

function TotalRechargeGiftView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	-- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/total_recharge_gift_ui_prefab", "layout_total_recharge_gift")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

	self:SetTabShowUIScene(0,{type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.TOTAL_RECHARGE_GIFT})
end

function TotalRechargeGiftView:OpenCallBack()
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT, OA_HAOLI_WANZHANG2_OPERATE_TYPE.OA_HAOLI_WANZHANG2_OPERATE_TYPE_INFO)
end

function TotalRechargeGiftView:LoadCallBack()

    self.node_list["title_view_name"].text.text = Language.TotalRechargeGift.ViewName
	self:FlushEndTime()

    if not self.all_reward_list then
        self.all_reward_list = AsyncListView.New(TotalRechargeGiftCell, self.node_list.suit_list)
		self.node_list.suit_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnValueChanged, self))
	end

	-- local other_cfg = TotalRechargeGiftWGData.Instance:GetTipShowShopCfg()
	-- local bundle, asset = nil, nil
	-- if other_cfg.bg_res and other_cfg.bg_res ~= '' then
	-- 	bundle, asset = ResPath.GetRawImagesJPG(other_cfg.bg_res)
	-- end
	-- if bundle and asset then
	-- 	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- 	end)
	-- end

	--加载模型时装
	if nil == self.role_model then
		self.role_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["ph_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	rt_scale_type = ModelRTSCaleType.L,
		-- 	can_drag = false,
		-- }
		-- self.role_model:SetRenderTexUI3DModel(display_data)
		-- self:AddUiRoleModel(self.role_model)

		self.role_model:SetUISceneModel(self.node_list["ph_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_model, 0)
	end

	if self.model_display == nil then
		self.model_display = OperationActRender.New(self.node_list.display_root)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if self.wuhun_model == nil then
		self.wuhun_model = RoleModel.New()
		--[[
		local display_data = {
			parent_node = self.node_list["wuhun_root"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}
		self.wuhun_model:SetRenderTexUI3DModel(display_data)
		]]
		self.wuhun_model:SetUISceneModel(self.node_list["wuhun_root"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.wuhun_model, 0)
	end

	-- if not self.new_model_display then
	-- 	self.new_model_display = OperationActRender.New(self.node_list.ph_display)
	-- 	self.new_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	-- end

	-- 详细奖励
	if not self.message_exchange_reward then
		self.message_exchange_reward = AsyncListView.New(ItemCell, self.node_list.message_exchange_reward)
		self.message_exchange_reward:SetStartZeroIndex(true)
	end

	self.grade_cache = -1
	self.round_cache = -1

	self.load_flag = true
	-- XUI.AddClickEventListener(self.node_list["everyday_gift"], BindTool.Bind(self.OnBtnfreeBtn, self))    --屏蔽每日礼包.
	XUI.AddClickEventListener(self.node_list["btn_skill_show"], BindTool.Bind(self.OnBtnShowSkill, self))
	XUI.AddClickEventListener(self.node_list.close_show_exchange_message, BindTool.Bind1(self.CloseShowMessage, self))
	XUI.AddClickEventListener(self.node_list.go_to_btn, BindTool.Bind(self.OnClickGoToBtn, self))
end

-- 主模型
function TotalRechargeGiftView:ShowFashionMainModel(res_path, res_id, callback)
    if self.old_main_res_id == res_id and self.old_main_res_fun == res_path then
        return
    end

    self.old_fashion_role_res = nil
    self.old_fashion_weapon_res = nil
    self.old_fashion_action_name = 0
    -- self:CancelWeaponTween()
    self.role_model:ClearLoadComplete()
    self.role_model:ClearPartCrossFadeAnimCache()
    self.role_model:RemoveAllModel()
    local bundle, asset = res_path(res_id)
    self.role_model:SetMainAsset(bundle, asset, callback)
    self.old_main_res_id = res_id
    self.old_main_res_fun = res_path
end

function TotalRechargeGiftView:ReleaseCallBack()

	self.old_main_res_id = 0
    self.old_main_res_fun = ""

	if self.all_reward_list then
		self.all_reward_list:DeleteMe()
		self.all_reward_list = nil
	end
	
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.lingchong_model then
		self.lingchong_model:DeleteMe()
		self.lingchong_model = nil
	end

	if self.xianwa_model then
		self.xianwa_model:DeleteMe()
		self.xianwa_model = nil
	end

	if self.mount_model then
		self.mount_model:DeleteMe()
		self.mount_model = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	-- if self.new_model_display then
	-- 	self.new_model_display:DeleteMe()
	-- 	self.new_model_display = nil
	-- end

	if self.message_exchange_reward then
		self.message_exchange_reward:DeleteMe()
		self.message_exchange_reward = nil
	end

	if self.wuhun_model then
        self.wuhun_model:DeleteMe()
        self.wuhun_model = nil
    end

	if CountDownManager.Instance:HasCountDown("total_recharge_gift_end_time") then
		CountDownManager.Instance:RemoveCountDown("total_recharge_gift_end_time")
	end

	if self.show_exchange_message_tween then
		self.show_exchange_message_tween:Kill()
		self.show_exchange_message_tween = nil
	end

	if self.timer_3 then
        GlobalTimerQuest:CancelQuest(self.timer_3)
        self.timer_3 = nil
    end

	self:CleanTimer()

	--self.footprint_eff_t = nil
	--self.is_foot_view = nil
	self.body_res_id = nil
	self.mount_res_id = nil
	self.have_foot_print = nil
	self.load_flag = nil
	self.next_create_footprint_time = nil
	self.grade_cache = nil
	self.round_cache = nil
end

function TotalRechargeGiftView:ShowIndexCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.TotalRechargeGiftView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT)
end

function TotalRechargeGiftView:OnFlush()
	local cur_grade = TotalRechargeGiftWGData.Instance:GetCurGrade()
	local cur_round = TotalRechargeGiftWGData.Instance:GetCurRoundNum()
	if self.grade_cache ~= cur_grade or self.round_cache ~= cur_round then
		-- self:FlushSuitModel()
		self:FlushOperationActModel()
		
		--self:FlushExchangeModel()
	end

	self.grade_cache = cur_grade
	self.round_cache = cur_round
	
	self:FlushSuitModel()
	-- self:FlushOperationActModel()
	-- -- self:FlushWuHunModel()
	-- self:FlushExchangeModel()
	self:FlushSkillBtn()

	local round_cfg = TotalRechargeGiftWGData.Instance:GetCurRoundRewardCfg()
	if IsEmptyTable(round_cfg) then
		return
	end
	
	local tb_round_cfg = {}
	for key, value in pairs (round_cfg) do
		tb_round_cfg[key] = {
			cfg_data = value,
			show_message_call_back = function(item_cell, node, index, is_init)
				self:OnShowMessage(item_cell, node, index, is_init)
			end,	
		}
	end
	self.all_reward_list:SetDataList(tb_round_cfg)
	local jump_index = TotalRechargeGiftWGData.Instance:GetReceiveRemindListJumpIndex()
	self.all_reward_list:JumpToIndex(jump_index)

	local next_index = TotalRechargeGiftWGData.Instance:GetReceiveNextIndex()
	if self.load_flag then
		self.load_flag = nil
		self.timer_3 = GlobalTimerQuest:AddTimesTimer(function ()
			if self:IsOpen() then
				local all_items = self.all_reward_list:GetAllItems()
				for k_1, v_1 in pairs(all_items) do
					v_1:SetShowMessageCallBack(BindTool.Bind1(self.OnShowMessage, self))

					if v_1.index == next_index then
						--策划zbp要求，第一次打开界面自动弹出详情面板，3秒后自动关闭.
						self:OnShowMessage(v_1, v_1.view, v_1.index, false)--不需要动画.

						self:CleanTimer()
						local complete_fun = function()
							self:CloseShowMessage()
						end
						self.timer = GlobalTimerQuest:AddDelayTimer(complete_fun, 3)
					end
				end
			end
		end, 0.5, 1)
	end

	local open_day_cfg = TotalRechargeGiftWGData.Instance:GetTipShowShopCfg()
	local bundle, asset = ResPath.GetF2RawImagesPNG(open_day_cfg.title_res)
	self.node_list.tianyin_title.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.tianyin_title.raw_image:SetNativeSize()
	end)

	self.ui_scene_change_config_index = open_day_cfg.ui_scene_config_index
	Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.TOTAL_RECHARGE_GIFT, open_day_cfg.ui_scene_config_index)

	local cur_round = TotalRechargeGiftWGData.Instance:GetCurRoundNum() + 1
	local total_round = TotalRechargeGiftWGData.Instance:GetTotalRoundNum() + 1
	self.node_list.text_round.text.text = string.format(Language.TotalRechargeGift.RoundNumStr, cur_round, total_round)


	local is_buy_free = TotalRechargeGiftWGData.Instance:GetShopIsBuyFlag()
	self.node_list.gift_is_get:SetActive(is_buy_free)
	self.node_list.gift_remind:SetActive(not is_buy_free)

	-- local data = TotalRechargeGiftWGData.Instance:GetCurRoundModelCfg()
	-- if data then
	-- 	self.node_list.suit_name.text.text = string.format(Language.TotalRechargeGift.SuitName2, data.name)
	-- end
	-- self.node_list.txt_des.text.text = Language.TotalRechargeGift.SuitDes

	local cur_integral = TotalRechargeGiftWGData.Instance:GetCurScore()

	local next_reward_cfg
	for k, v in ipairs(round_cfg) do
		if cur_integral < v.need_lingyu then
			next_reward_cfg = v
			break
		end
	end
	if not next_reward_cfg then
		next_reward_cfg = round_cfg[#round_cfg]
	end

	local need_num = 0
	local cur_integral_show = 0
	if open_day_cfg.recharge_type == RECHARGE_TYPE.ZhenChong then
		cur_integral_show = cur_integral / RECHARGE_BILI
		need_num = next_reward_cfg.need_lingyu / RECHARGE_BILI
	elseif open_day_cfg.recharge_type == RECHARGE_TYPE.ShiFu then
		cur_integral_show = cur_integral / TotalRechargeGiftWGData.SHIFU_DISCOUNT
		need_num = next_reward_cfg.need_lingyu / TotalRechargeGiftWGData.SHIFU_DISCOUNT
	end

	local color = cur_integral_show < need_num and "#F97878" or "#35e28a"
	local cur_integral_text = string.format("%.2f", cur_integral_show)
	cur_integral_text = ToColorStr(cur_integral_show, color)

	local price_str = Language.TotalRechargeGift.RechargeInfo[open_day_cfg.recharge_type]
	self.node_list.txt_recharge_des.text.text = string.format(Language.TotalRechargeGift.RechargeDes, price_str, cur_integral_text, need_num)
end

function TotalRechargeGiftView:CleanTimer()
    if self.timer then
        GlobalTimerQuest:CancelQuest(self.timer)
        self.timer = nil
    end
end

function TotalRechargeGiftView:OnValueChanged(pos)
	local all_count = self.all_reward_list:GetListViewNumbers()
	local width = (1/(all_count - 2))
	local val = self.node_list.suit_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.img_left:SetActive(val ~= 0 and val >= width)
	self.node_list.img_right:SetActive(val <= (1-width))

end

-- 武魂模型
function TotalRechargeGiftView:FlushWuHunModel()
	self.node_list.wuhun_root:CustomSetActive(false)
	local cfg = TotalRechargeGiftWGData.Instance:GetCurShowWuHunCfg()

    if IsEmptyTable(cfg) then
		return
	end

	local wuhun_cfg = WuHunWGData.Instance:GetWuhunResByItemId(cfg.model_show_itemid)
	if IsEmptyTable(wuhun_cfg) then
		return
	end

	self.node_list.wuhun_root:CustomSetActive(true)

	local bundle, asset = ResPath.GetWuHunModel(wuhun_cfg.appe_image_id)
    self.wuhun_model:SetMainAsset(bundle, asset, function ()
		self.wuhun_model:PlayRoleAction(SceneObjAnimator.Rest)
	end)

	if cfg.model_pos and cfg.model_pos ~= "" then
		local pos_list = string.split(cfg.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list.wuhun_root.rect, pos_x, pos_y)
	end

	-- if cfg.model_scale and cfg.model_scale ~= "" then
	-- 	Transform.SetLocalScaleXYZ(self.node_list.wuhun_root.transform, cfg.model_scale, cfg.model_scale, cfg.model_scale)
	-- end

	if cfg.model_scale and cfg.model_scale ~= "" then
		self.wuhun_model:SetRTAdjustmentRootLocalScale(cfg.model_scale, cfg.model_scale, cfg.model_scale)
	end

	if cfg.model_rot and cfg.model_rot ~= "" then
		local rot_x, rot_y, rot_z = 0, 0, 0
		local rot_list = string.split(cfg.model_rot, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
		self.node_list.wuhun_root.rect.rotation = Quaternion.Euler(rot_x, rot_y, rot_z)
	end
end

function TotalRechargeGiftView:FlushEndTime()
	local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT)
	if CountDownManager.Instance:HasCountDown("total_recharge_gift_end_time") then
		CountDownManager.Instance:RemoveCountDown("total_recharge_gift_end_time")
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown("total_recharge_gift_end_time",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	else
		self:OnComplete()
	end
end

function TotalRechargeGiftView:UpdateCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list["activity_time"].text.text = string.format(Language.TotalRechargeGift.ActTime, time_str)
end

function TotalRechargeGiftView:OnComplete()
	self.node_list.activity_time.text.text = ""
	self:Close()
end

function TotalRechargeGiftView:OnBtnfreeBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_buy_free = TotalRechargeGiftWGData.Instance:GetShopIsBuyFlag()
		if is_buy_free then
			TipWGCtrl.Instance:ShowSystemMsg(Language.GoldStoneBuy.AllFreeShopBuy)
			return
		end

		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT, OA_HAOLI_WANZHANG2_OPERATE_TYPE.OA_HAOLI_WANZHANG2_OPERATE_TYPE_EVERYDAY_REWARD)
	end
end

function TotalRechargeGiftView:OnClickGoToBtn()
	local open_day_cfg = TotalRechargeGiftWGData.Instance:GetTipShowShopCfg()
	FunOpen.Instance:OpenViewNameByCfg(open_day_cfg.open_panel)
end

-- 点击关闭详情信息
function TotalRechargeGiftView:CloseShowMessage()
	self:SetShowMessage(false)
end

-- 查看详细信息
function TotalRechargeGiftView:OnShowMessage(item_cell, node, index, is_init)
	if (not item_cell) or (not item_cell.data) then
		return
	end

	self:SetShowMessage(true, node, index, is_init)
	local item_data = item_cell.data
	local cfg_data = item_data.cfg_data

	local open_day_cfg = TotalRechargeGiftWGData.Instance:GetTipShowShopCfg()

	local score = TotalRechargeGiftWGData.Instance:GetCurScore()
	local need_score = cfg_data.need_lingyu or 0
	local need_num = 0
	local cur_integral_show = 0
	if open_day_cfg.recharge_type == RECHARGE_TYPE.ZhenChong then
		cur_integral_show = score / RECHARGE_BILI
		need_num = need_score / RECHARGE_BILI
	elseif open_day_cfg.recharge_type == RECHARGE_TYPE.ShiFu then
		cur_integral_show = score / TotalRechargeGiftWGData.SHIFU_DISCOUNT
		need_num = need_score / TotalRechargeGiftWGData.SHIFU_DISCOUNT
	end
	local str = ToColorStr(string.format("(%d/%d)", cur_integral_show, need_num), COLOR3B.L_GREEN)

	self.node_list.message_exchange_desc.text.text = Language.TotalRechargeGift.DetailRechargeStr .. str
	local reward_list = cfg_data.reward_item
	self.message_exchange_reward:SetDataList(reward_list)
end

-- 展示详细信息
function TotalRechargeGiftView:SetShowMessage(is_show, node, index, is_init)
	self.node_list.close_show_exchange_message:CustomSetActive(is_show)

	if is_show and node and index then
		if self.show_exchange_message_tween then
			self.show_exchange_message_tween:Kill()
			self.show_exchange_message_tween = nil
		end
		local node_pos_x = node.transform.position.x
		local offset_y = 13
		self.node_list.message_exchange_root.transform.position = node.transform.position + Vector3(0, offset_y, 0)

		if is_init then
			self.show_exchange_message_tween = self.show_exchange_message_tween or DG.Tweening.DOTween.Sequence()
			local pos = self.node_list.message_exchange_root.transform.anchoredPosition
			local trans = self.node_list.message_exchange_root
			self.show_exchange_message_tween:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(0, 20, 0), 0.6)) --上30
			self.show_exchange_message_tween:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(0, 0, 0), 0.6))	--下30
			self.show_exchange_message_tween:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(0, 20, 0), 0.6))	--上30
			self.show_exchange_message_tween:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(0, 0, 0), 0.6))	--下30
			self.show_exchange_message_tween:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(0, 20, 0), 0.6))	--上30
			self.show_exchange_message_tween:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(0, 0, 0), 0.6))	--下30
			self.show_exchange_message_tween:SetEase(DG.Tweening.Ease.Linear)
			self.show_exchange_message_tween:OnComplete(function()
				self:CloseShowMessage()
			end)
		end
	end
end

function TotalRechargeGiftView:OnBtnShowSkill()
	-- local cfg = TotalRechargeGiftWGData.Instance:GetCurShowWaistCfg()
	-- if not cfg then
	-- 	return
	-- end
	-- local data = SupremeFieldsWGData.Instance:SkillShowCfgList(cfg.waist_type, 1)
	-- CommonSkillShowCtrl.Instance:SetViewDataAndOpen(data)

	local open_day_cfg = TotalRechargeGiftWGData.Instance:GetTipShowShopCfg()
	if not open_day_cfg then
		return
	end

	if open_day_cfg.skill_type and open_day_cfg.skill_type == WARDROBE_PART_TYPE.MOUNT then
		local mount_image_skill = NewAppearanceWGData.Instance:GetSpecialMountSkillCfg(open_day_cfg.param1, open_day_cfg.param2)
		local show_data = {
			icon = mount_image_skill.skill_icon,
			top_text = mount_image_skill.skill_name,
			body_text = mount_image_skill.skill_describe,
	
			x = 0,
			y = 0,
			set_pos = true,
			--capability = capability,
		}
	
		NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
	end
end

function TotalRechargeGiftView:FlushSkillBtn()
	local open_day_cfg = TotalRechargeGiftWGData.Instance:GetTipShowShopCfg()
	if not open_day_cfg then
		return
	end

	self.node_list.btn_skill_show:SetActive(open_day_cfg.skill_type and open_day_cfg.skill_type == WARDROBE_PART_TYPE.MOUNT)

	if open_day_cfg.skill_type and open_day_cfg.skill_type == WARDROBE_PART_TYPE.MOUNT then
		local mount_image_skill = NewAppearanceWGData.Instance:GetSpecialMountSkillCfg(open_day_cfg.param1, open_day_cfg.param2)
		local bundle, asset = ResPath.GetSkillIconById(mount_image_skill.skill_icon)
		self.node_list.skill_icon.image:LoadSpriteAsync(bundle, asset, function()
			self.node_list.skill_icon.image:SetNativeSize()
		end)
	end
end

function TotalRechargeGiftView:FlushOperationActModel()
	if not self.model_display then
		return
	end

	local cfg = TotalRechargeGiftWGData.Instance:GetCurShowWaistCfg()
    if not cfg then
		return
	end

	local display_data = {}
	display_data.should_ani = false				-- 后续需要上下动就加配置
	display_data.hide_model_block = false
	display_data.render_type = cfg.model_show_type - 1
	display_data.is_show_model = true
	display_data.need_wp_tween = true

	if display_data.render_type == OARenderType.FZ or display_data.render_type == OARenderType.CangMing then
		display_data.waist_type = cfg.waist_type
	end

	display_data.model_click_func = function ()
		if cfg.model_show_itemid == nil or type(cfg.model_show_itemid) == "string" or cfg.model_show_itemid <= 0 then
			return
		end

		TipWGCtrl.Instance:OpenItem({item_id = cfg.model_show_itemid})
	end
	self.model_display:SetData(display_data)

	local pos_x, pos_y, pos_z = 0, 52, 0
	if cfg.display_pos and cfg.display_pos ~= "" then
		local pos_list = string.split(cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
	end

	local rot_x, rot_y, rot_z = 0, 0, 0
	if cfg.display_rotation and cfg.display_rotation ~= "" then
		local rot_list = string.split(cfg.display_rotation, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
	end

	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.display_root.rect, pos_x, pos_y, pos_z)
	self.node_list.display_root.rect.rotation = Quaternion.Euler(rot_x, rot_y, rot_z)

	local scale = cfg.display_scale
	scale = (scale and scale ~= "" and scale > 0) and scale or 1
	Transform.SetLocalScaleXYZ(self.node_list.display_root.transform, scale, scale, scale)
end

-- 模型UI场景版
function TotalRechargeGiftView:FlushExchangeModel()
	local shop_cfg = TotalRechargeGiftWGData.Instance:GetCurShowWuHunCfg()
	if not shop_cfg then
		return
	end
	if shop_cfg.model_show_itemid ~= 0 and shop_cfg.model_show_itemid ~= "" then
		local item_cfg = ItemWGData.Instance:GetItemConfig(shop_cfg.model_show_itemid )
		local res_id = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_cfg.id)
		local item_id
		if item_cfg.param2 then
			item_id = string.split(item_cfg.param2, "|")
		end
		res_id = res_id or item_id[1]
		-- local path = ResPath.GetMountModel
		-- local animation_type = "mount"

		self:ShowFashionMainModel(ResPath.GetMountModel, res_id, function ()
			self.role_model:PlayMountAction()
        end)
		
	end
end

-- 模型RT版
-- function TotalRechargeGiftView:FlushExchangeModel()
-- 	local shop_cfg = TotalRechargeGiftWGData.Instance:GetCurShowWuHunCfg()
-- 	if not shop_cfg then
-- 		return
-- 	end

-- 	local display_data = {}
-- 	if shop_cfg.model_show_itemid ~= 0 and shop_cfg.model_show_itemid ~= "" then
-- 		local split_list = string.split(shop_cfg.model_show_itemid, "|")
-- 		if #split_list > 1 then
-- 			local list = {}
-- 			for k, v in pairs(split_list) do
-- 				list[tonumber(v)] = true
-- 			end
-- 			display_data.model_item_id_list = list
-- 		else
-- 			display_data.item_id = shop_cfg.model_show_itemid
-- 		end
-- 	end

-- 	display_data.model_rt_type = ModelRTSCaleType.L
-- 	display_data.render_type = tonumber(shop_cfg["model_show_type"]) or 0

-- 	if shop_cfg.model_pos and shop_cfg.model_pos ~= "" then
-- 		local pos_list = string.split(shop_cfg.model_pos, "|")
-- 		local pos_x = tonumber(pos_list[1]) or 0
-- 		local pos_y = tonumber(pos_list[2]) or 0
-- 		local pos_z = tonumber(pos_list[3]) or 0

-- 		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
-- 	end

-- 	if shop_cfg.model_rot and shop_cfg.model_rot ~= "" then
-- 		local rot_list = string.split(shop_cfg.model_rot, "|")
-- 		local rot_x = tonumber(rot_list[1]) or 0
-- 		local rot_y = tonumber(rot_list[2]) or 0
-- 		local rot_z = tonumber(rot_list[3]) or 0

-- 		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
-- 	end

-- 	if shop_cfg.model_scale and shop_cfg.model_scale ~= "" then
-- 		display_data.model_adjust_root_local_scale = shop_cfg.model_scale
-- 	end

	

-- 	self.new_model_display:SetData(display_data)
-- end


function TotalRechargeGiftView:FlushSuitModel()
	if not self.role_model then
		return
	end

	self.role_model:RemoveAllModel()

	local data_list = TotalRechargeGiftWGData.Instance:GetActivationPartList()
	if not data_list then
		return
	end

	--清理掉回调
	--self:ClearFootEff()

	self.node_list["lc_root"]:SetActive(false)
	self.node_list["xw_root"]:SetActive(false)
	self.node_list["mount_root"]:SetActive(false)

	--self.is_foot_view = false
	self.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	self.mount_res_id = 0
	self.have_foot_print = false
	self.is_mecha_type = false
	self.mecha_data = nil
	local has_fashion_show = false
	local res_id, fashion_cfg
	for k, data in pairs(data_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				self.body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
				has_fashion_show = true
			end
		elseif data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
			self.have_foot_print = true
		elseif data.type == WARDROBE_PART_TYPE.MECHA then -- 机甲
			self.is_mecha_type = true
			self.mecha_data = data
		end
	end

	local d_body_res, d_hair_res, d_face_res
	local main_role = not has_fashion_show and Scene.Instance:GetMainRole()
	local vo = main_role and main_role:GetVo()
	if not has_fashion_show and vo and vo.appearance then
		if vo.appearance.fashion_body == 0 then
			d_body_res = vo.appearance.default_body_res_id
			d_hair_res = vo.appearance.default_hair_res_id
			d_face_res = vo.appearance.default_face_res_id
		end
	end

	if not self.is_mecha_type then
		local animation_name = self.have_foot_print and SceneObjAnimator.Move or SceneObjAnimator.Idle
		local extra_role_model_data = {
			d_face_res = d_face_res,
			d_hair_res = d_hair_res,
			d_body_res = d_body_res,
			animation_name = animation_name,
		}
		self.role_model:SetRoleResid(self.body_res_id, nil, extra_role_model_data)

		local action_type = MOUNT_RIDING_TYPE[1]
		if self.mount_res_id > 0 then
			self.role_model:SetMountResid(self.mount_res_id)
			local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.mount_res_id)
			if not IsEmptyTable(action_cfg) then
				action_type = MOUNT_RIDING_TYPE[action_cfg.action]
			end
			self.role_model:PlayStartAction(action_type)
		end

		for k, v in pairs(data_list) do
			self:ShowModelByData(v)
		end
	else
		self:ShowMechaModel(self.mecha_data)
	end

	self:ChangeModelShowScale()
end

function TotalRechargeGiftView:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end
	local res_id, fashion_cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			local is_run = false
			res_id = fashion_cfg.resouce
			if data.param1 == SHIZHUANG_TYPE.MASK then -- 脸饰
				self.role_model:SetMaskResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.BELT then -- 腰饰
				self.role_model:SetWaistResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WEIBA then -- 尾巴
				self.role_model:SetTailResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
				self.role_model:SetShouHuanResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
				self.role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.role_model:SetWeaponResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
				self.role_model:SetFootTrailModel(res_id)
				self.role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
				self.role_model:PlayRoleAction(SceneObjAnimator.Move)
				is_run = true
				-- self.is_foot_view = true
				-- self.foot_effect_id = res_id
				-- if not self.use_update then
				-- 	Runner.Instance:AddRunObj(self, 8)
				-- 	self.use_update = true
				-- end
			end
			if not is_run then
				self.role_model:PlayRoleAction(SceneObjAnimator.Walk)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then -- 灵宠
		fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetLingChongModelData(WARDROBE_PART_TYPE.LING_CHONG, fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then -- 仙娃
		fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
		if fashion_cfg then
			self:SetXianWaModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.active_id)
		end
	end
end

function TotalRechargeGiftView:ShowMechaModel(mecha_data)
	local mecha_seq = mecha_data.param1 or 0
	if mecha_seq >= 0 then
		local part_list = {}
		local base_part = MechaWGData.Instance:GetMechaBasePartListByMechaSeq(mecha_seq)
		for k, v in pairs(base_part) do
			local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
			part_list[part_cfg.part] = part_cfg.res_id
		end

		local part_info = {
			gundam_seq = mecha_seq,
			gundam_body_res = part_list[MECHA_PART_TYPE.BODY] or 0,
			gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON] or 0,
			gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND] or 0,
			gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND] or 0,
			gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT] or 0,
			gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
			gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING] or 0,
			gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING] or 0,
		}

		self.role_model:SetGundamModel(part_info)
        self.role_model:PlayStartAction(SceneObjAnimator.UiIdle)
	end
end

function TotalRechargeGiftView:SetLingChongModelData(type, res_id)
	self.node_list["lc_root"]:SetActive(true)
	if nil == self.lingchong_model then
		self.lingchong_model = RoleModel.New()
		--[[
		local display_data = {
			parent_node = self.node_list["lc_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		self.lingchong_model:SetRenderTexUI3DModel(display_data)
		]]
		-- 使用场景模型
		self.lingchong_model:SetUISceneModel(self.node_list["lc_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.lingchong_model, 0)
	else
		if self.lingchong_model then
			self.lingchong_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetPetModel(res_id)
	self.lingchong_model:SetMainAsset(bundle, asset, function()
		self.lingchong_model:PlaySoulAction()
	end)

	self.lingchong_model:FixToOrthographic(self.root_node_transform)
end

function TotalRechargeGiftView:SetXianWaModelData(res_id)
	self.node_list["xw_root"]:SetActive(true)
	if nil == self.xianwa_model then
		self.xianwa_model = RoleModel.New()
		--[[
		local display_data = {
			parent_node = self.node_list["xw_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		self.xianwa_model:SetRenderTexUI3DModel(display_data)
		]]
		-- 使用场景模型
		self.xianwa_model:SetUISceneModel(self.node_list["xw_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.xianwa_model, 0)
	else
		if self.xianwa_model then
			self.xianwa_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetHaiZiModel(res_id)
	self.xianwa_model:SetMainAsset(bundle, asset, function()
		self.xianwa_model:PlaySoulAction()
	end)

	self.xianwa_model:FixToOrthographic(self.root_node_transform)
end

function TotalRechargeGiftView:SetMountModelData(res_id)
	self.node_list["mount_root"]:SetActive(true)
	if nil == self.mount_model then
		self.mount_model = RoleModel.New()
		--[[
		local display_data = {
			parent_node = self.node_list["mount_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		self.mount_model:SetRenderTexUI3DModel(display_data)
		]]
		-- 使用场景模型
		self.mount_model:SetUISceneModel(self.node_list["mount_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.mount_model, 0)
	else
		if self.mount_model then
			self.mount_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetMountModel(res_id)
	self.mount_model:SetMainAsset(bundle, asset, function()
		self.mount_model:PlayMountAction()
	end)

	self.mount_model:FixToOrthographic(self.root_node_transform)
end

--[[
function TotalRechargeGiftView:Update(now_time, elapse_time)
	if not self.is_foot_view then
		return
	end

	if self.next_create_footprint_time == 0 then
		self:CreateFootPrint()
		self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
	end

	if self.next_create_footprint_time == nil then --初生时也是位置改变，不播
		self.next_create_footprint_time = 0
	end

	if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
		self.next_create_footprint_time = 0
	end

	self:UpdateFootprintPos()
end

function TotalRechargeGiftView:CreateFootPrint()
	if nil == self.foot_effect_id then
		return
	end

	if nil == self.footprint_eff_t then
		self.footprint_eff_t = {}
	end

	local pos = self.role_model.draw_obj:GetRoot().transform
	local bundle, asset = ResPath.GetFootUIEffect(self.foot_effect_id)
	EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.position.x, pos.position.y, pos.position.z), nil, pos, nil, function(obj)
		if obj then
			if nil ~= obj then
				if self.role_model then
					obj.transform.localPosition = Vector3.zero
					obj:SetActive(false)
					obj:SetActive(true)
					table.insert(self.footprint_eff_t, { obj = obj, role_model = self.role_model })
					self.role_model:OnAddGameobject(obj)
				else
					ResPoolMgr:Release(obj)
				end
			end
		end
	end)

	if #self.footprint_eff_t > 2 then
		local obj = table.remove(self.footprint_eff_t, 1)
		obj.role_model:OnRemoveGameObject(obj.obj)
		if not IsNil(obj.obj) then
			obj.obj:SetActive(false)
		end
	end
end

function TotalRechargeGiftView:UpdateFootprintPos()
	if nil == self.footprint_eff_t then
		return
	end

	for k, v in pairs(self.footprint_eff_t) do
		if not IsNil(v.obj) then
			local pos = v.obj.transform.localPosition
			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
		end
	end
end

function TotalRechargeGiftView:ClearFootEff()
	if self.footprint_eff_t ~= nil then
		for k, v in pairs(self.footprint_eff_t) do
			if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
				v.role_model:OnRemoveGameObject(v.obj)
				v.obj:SetActive(false)
			end
		end
	end

	self.footprint_eff_t = {}
end
]]

function TotalRechargeGiftView:ChangeModelShowScale()
	local data = TotalRechargeGiftWGData.Instance:GetCurRoundModelCfg()
	if IsEmptyTable(data) then
		return
	end

	local pos_str = data.main_pos
	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		if self.role_model then
			self.role_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end
		--RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ph_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	end

	local rotate_str = data.main_rot
	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		if self.role_model then
			self.role_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			--self.role_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
		end
	end

	local scale = data.main_scale
	if scale and scale ~= "" then
		self.role_model:SetUSAdjustmentNodeLocalScale(scale)
		--RectTransform.SetLocalScale(self.node_list.ph_display.rect, scale)
	end

	--灵宠
	if self.node_list["lc_root"]:GetActive() then
		pos_str = data.pet_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			--RectTransform.SetAnchoredPosition3DXYZ(self.node_list.lc_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
			if self.lingchong_model then
				self.lingchong_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.pet_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.lingchong_model then
				--self.lingchong_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
				self.lingchong_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.lc_scale
		if scale and scale ~= "" then
			self.lingchong_model:SetUSAdjustmentNodeLocalScale(scale)
			--RectTransform.SetLocalScale(self.node_list.lc_display.rect, scale)
		end
	end

	--仙娃
	if self.node_list["xw_root"]:GetActive() then
		pos_str = data.xw_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.xianwa_model then
				self.xianwa_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
			--RectTransform.SetAnchoredPosition3DXYZ(self.node_list.xw_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		rotate_str = data.xw_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.xianwa_model then
				self.xianwa_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
				--self.xianwa_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
		end

		scale = data.xw_scale
		if scale and scale ~= "" then
			self.xianwa_model:SetUSAdjustmentNodeLocalScale(scale)
			--RectTransform.SetLocalScale(self.node_list.xw_display.rect, scale)
		end
	end

	--坐骑
	if self.node_list["mount_display"]:GetActive() then
		pos_str = data.mount_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			self.mount_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			--RectTransform.SetAnchoredPosition3DXYZ(self.node_list.mount_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		rotate_str = data.mount_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.mount_model then
				self.mount_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
				--self.mount_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
		end

		scale = data.mount_scale
		if scale and scale ~= "" then
			self.mount_model:SetUSAdjustmentNodeLocalScale(scale)
			--RectTransform.SetLocalScale(self.node_list.mount_display.rect, scale)
		end
	end
end

--------------------------------------
-- TotalRechargeGiftCell
--------------------------------------
TotalRechargeGiftCell = TotalRechargeGiftCell or BaseClass(BaseRender)

function TotalRechargeGiftCell:__init()
	self.reward_item = ItemCell.New(self.node_list["tianyin_item"])
	self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function TotalRechargeGiftCell:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end

	self.show_message_cb = nil
end

function TotalRechargeGiftCell:OnFlush()
	if not self.data then
		return
	end

	local cfg_data = self.data.cfg_data
	local item_data = cfg_data.reward_item and cfg_data.reward_item[0] or {}

	if self.data.show_message_call_back then
		self:SetShowMessageCallBack(self.data.show_message_call_back)
	end

	-- self.reward_item:SetFlushCallBack(function ()
	-- 	self.reward_item:SetCellBgEnabled(false)
	-- 	self.reward_item:SetShowCualityBg(false)
	-- 	self.reward_item:SetDefaultEff(false)
	-- 	self.reward_item:SetBindIconVisible(false)
	-- end)
	--self.reward_item:SetShowCualityBg(false)
	self.reward_item:SetData(item_data)

	local is_get = TotalRechargeGiftWGData.Instance:GetRewardStateBySeq(cfg_data.seq)
	self.node_list.tianyin_is_get:SetActive(is_get)

	local recharge_value = 0
	local open_day_cfg = TotalRechargeGiftWGData.Instance:GetTipShowShopCfg()
	if open_day_cfg.recharge_type == RECHARGE_TYPE.ZhenChong then
		recharge_value = cfg_data.need_lingyu / RECHARGE_BILI
	elseif open_day_cfg.recharge_type == RECHARGE_TYPE.ShiFu then
		recharge_value = cfg_data.need_lingyu / 2
	end

	local price_str = Language.TotalRechargeGift.RechargeInfo2[open_day_cfg.recharge_type]
	self.node_list.tianyin_integral.text.text = string.format(Language.TotalRechargeGift.RechargeStr, price_str, recharge_value)

	local score = TotalRechargeGiftWGData.Instance:GetCurScore()
	local need_lingyu = cfg_data.need_lingyu
	self.node_list.get_btn:SetActive(true)--need_lingyu <= score)

	if not is_get and need_lingyu <= score then
		self.node_list.is_remind:SetActive(true)
		--self.node_list.get_btn:SetActive(true)
	else
		self.node_list.is_remind:SetActive(false)
		--self.node_list.get_btn:SetActive(false)
	end
end

function TotalRechargeGiftCell:OnClickGetReward()
	if not self.data then
		return
	end
	local cfg_data = self.data.cfg_data
	local is_get = TotalRechargeGiftWGData.Instance:GetRewardStateBySeq(cfg_data.seq)
	local score = TotalRechargeGiftWGData.Instance:GetCurScore()
	local need_lingyu = cfg_data.need_lingyu
	if is_get then
		self.node_list.tianyin_is_get:SetActive(true)
		TipWGCtrl.Instance:ShowSystemMsg(Language.TotalRechargeGift.IsGetReward)
		-- self:LookMessage()
		return
	end

	if score >= need_lingyu then
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT, OA_HAOLI_WANZHANG2_OPERATE_TYPE.OA_HAOLI_WANZHANG2_OPERATE_TYPE_REWARD, cfg_data.seq)
	else
		--TipWGCtrl.Instance:ShowSystemMsg(Language.TotalRechargeGift.ScoreLack)
		self:LookMessage()
	end
end

function TotalRechargeGiftCell:SetShowMessageCallBack(cb)
	self.show_message_cb = cb
end

function TotalRechargeGiftCell:LookMessage()
	if not self.data then
		return
	end
	-- local cfg_data = self.data.cfg_data
	-- if #cfg_data.reward_item == 0 then
	-- 	local item_data = cfg_data.reward_item and cfg_data.reward_item[0] or {}
	-- 	TipWGCtrl.Instance:OpenItem(item_data)
	-- else
		if self.show_message_cb then
			self.show_message_cb(self, self.view, self.index)
		end
	-- end
end