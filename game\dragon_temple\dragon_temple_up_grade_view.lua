DragonTempleUpGradeView = DragonTempleUpGradeView or BaseClass(SafeBaseView)

function DragonTempleUpGradeView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false, true)

	self:AddViewResource(0, "uis/view/dragon_temple_ui_prefab", "layout_longshen_upgrade")
end

function DragonTempleUpGradeView:LoadCallBack()
	if self.cur_attr_list == nil then
        self.cur_attr_list = {}
        local node_num = self.node_list["cur_attr_list"].transform.childCount
        for i = 1, node_num do
            self.cur_attr_list[i] = CommonAddAttrRender.New(self.node_list["cur_attr_list"]:FindObj("attr_" .. i))
			self.cur_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    if self.next_attr_list == nil then
        self.next_attr_list = {}
        local node_num = self.node_list["next_attr_list"].transform.childCount
        for i = 1, node_num do
            self.next_attr_list[i] = CommonAddAttrRender.New(self.node_list["next_attr_list"]:FindObj("attr_" .. i))
			self.next_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end
end

function DragonTempleUpGradeView:ReleaseCallBack()
	if self.cur_attr_list then
	    for k, v in pairs(self.cur_attr_list) do
	        v:DeleteMe()
	    end
	    self.cur_attr_list = nil
    end

    if self.next_attr_list then
	    for k, v in pairs(self.next_attr_list) do
	        v:DeleteMe()
	    end
	    self.next_attr_list = nil
    end	

    if self.buy_longshen_alert ~= nil then
		self.buy_longshen_alert:DeleteMe()
		self.buy_longshen_alert = nil
	end
end

function DragonTempleUpGradeView:OnFlush()
	local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
	local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
	local next_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level + 1)
	if IsEmptyTable(cur_level_cfg) then
		return
	end

	self:FlushLevelAttr(longshen_level, cur_level_cfg, next_level_cfg)

	local other_cfg = DragonTempleWGData.Instance:GetOtherCfg()
    local bundle, asset = ResPath.GetSkillIconById(other_cfg.longshen_skill_icon)
    self.node_list.cur_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.cur_skill_icon.image:SetNativeSize()
    end)

    self.node_list.next_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.next_skill_icon.image:SetNativeSize()
    end)
end

function DragonTempleUpGradeView:FlushLevelAttr(longshen_level, cur_level_cfg, next_level_cfg)
	self.node_list.cur_level.text.text = "LV." .. longshen_level
	self.node_list.cur_level_desc.text.text = cur_level_cfg.show_desc
	local cur_attr_list = DragonTempleWGData.Instance:GetLongShenLevelAttrList(longshen_level)
	for k,v in pairs(self.cur_attr_list) do
        v:SetData(cur_attr_list[k])
    end

    local is_max_level = IsEmptyTable(next_level_cfg)
    self.node_list.next_level_panel:SetActive(not is_max_level)
    if not is_max_level then
    	self.node_list.next_level.text.text = "LV." .. longshen_level + 1
		self.node_list.next_level_desc.text.text = next_level_cfg.show_desc
		local next_attr_list = DragonTempleWGData.Instance:GetLongShenLevelAttrList(longshen_level + 1)
		for k,v in pairs(self.next_attr_list) do
	        v:SetData(next_attr_list[k])
	    end
    end
end