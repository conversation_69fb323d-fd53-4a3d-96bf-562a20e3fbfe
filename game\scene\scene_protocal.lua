
Scene = Scene or BaseClass(BaseWGCtrl)

function Scene:RegisterAllProtocols()
    self:RegisterProtocol(SCEnterScene, "OnEnterScene")
    self:RegisterProtocol(SCVisibleObjEnterRole, "OnVisibleObjEnterRole")
    self:RegisterProtocol(SCVisibleObjLeave, "OnVisibleObjLeave")
    self:RegisterProtocol(SCObjMove, "OnObjMove")
    self:RegisterProtocol(SCVisibleObjEnterFalling, "OnVisibleObjEnterFalling")
    self:RegisterProtocol(SCVisibleObjEnterMonster, "OnVisibleObjEnterMonster")
    -- self:RegisterProtocol(SCVisibleObjEnterPet, "OnSCVisibleObjEnterPet")
    self:RegisterProtocol(SCVisibleObjEnterBattleFieldShenShi, "OnVisibleObjEnterBattleFieldShenShi")
    self:RegisterProtocol(SCRoleVisibleChange, "OnRoleVisibleChange")
    self:RegisterProtocol(SCVisibleObjEnterGather, "OnVisibleObjEnterGather")
    self:RegisterProtocol(SCVisibleObjEnterWorldEventObj, "OnVisibleObjEnterWorldEventObj")
    self:RegisterProtocol(SCVisibleObjEnterRoleShadow, "OnVisibleObjEnterRoleShadow")
    self:RegisterProtocol(SCVisibleObjEnterMarryObj, "OnSCVisibleObjEnterMarryObj")
    self:RegisterProtocol(SCVisibleObjEnterEffect, "OnVisibleObjEnterEffect")
    self:RegisterProtocol(SCResetPost, "OnResetPost")
    self:RegisterProtocol(SCSkillResetPos, "OnSkillResetPos")
    self:RegisterProtocol(SCStartGather, "OnStartGather")
    self:RegisterProtocol(SCStopGather, "OnStopGather")
    self:RegisterProtocol(SCGatherBeGather, "OnGatherBeGather")
    self:RegisterProtocol(SCStartGatherTimer, "OnStartGatherTimer")
    self:RegisterProtocol(SCAllObjMoveInfo, "OnAllObjMoveInfo")
    self:RegisterProtocol(SCObjMoveMode, "OnObjMoveMode")
    self:RegisterProtocol(SCSceneMonsterDie, "OnSceneMonsterDie")
    self:RegisterProtocol(SCRoleSpecialAppearanceChange, "OnRoleSpecialAppearanceChange")
    self:RegisterProtocol(SCGatherChange, "OnGatherChange")
    self:RegisterProtocol(SCRoleAccetpTaskAppearn, "OnRoleAccetpTaskAppearn")
    self:RegisterProtocol(SCMoveSwordDown, "OnSCMoveSwordDown")
    self:RegisterProtocol(SCMoveSwordSprint, "OnSCMoveSwordSprint")

    self:RegisterProtocol(CSTransportReq)
    self:RegisterProtocol(CSObjMove)
    self:RegisterProtocol(CSStartGatherReq)
    self:RegisterProtocol(CSPickItem)
    self:RegisterProtocol(CSGetAllObjMoveInfoReq)
    self:RegisterProtocol(CSWorldEventObjTouch)

    self:RegisterProtocol(SCTeamMemberPosList, "OnTeamMemberPosList") --队员位置下发
    self:RegisterProtocol(CSReqTeamMemberPos) --请求队员位置

    self:RegisterProtocol(SCRolePersonAreaMsgInfo, "OnRolePersonAreaMsgInfo")

    self:RegisterProtocol(SCMultiuserChallengeTeamMemberPosList, "OnMultiuserChallengeTeamMemberPosList") --跨服3v3队员位置下发
    self:RegisterProtocol(CSMultiuserChallengeReqSideMemberPos) --跨服3v3请求队员位置
    self:RegisterProtocol(SCServerDebugMsg, "OnServerDebugMsg")
    self:RegisterProtocol(SCQingyuanCoupleHaloTrigger, "OnQingyuanCoupleHaloTrigger")--夫妻光环

    -- 场景单个对象的运动信息返回
    self:RegisterProtocol(SCOneObjMoveInfo, "OnSCOneObjMoveInfo")
    self:RegisterProtocol(CSGetOneObjMoveInfoReq)
    --玩家杀死怪物通知,只有BOSS被击杀才会通知
    self:RegisterProtocol(SCRoleKillBossNotice, "OnSCRoleKillBossNotice")
    -- 客户端通知加载完场景
    self:RegisterProtocol(CSFBLoadedScene)

    self:RegisterProtocol(CSGetOneRoleMovePosReq)
    self:RegisterProtocol(SCOneRoleMovePos, "OnSCOneRoleMovePos")
    self:RegisterProtocol(SCSceneEffectRemove, "OnSCSceneEffectRemove")

    --------刺探用查询-------------------------------------------------------------------
    self:RegisterProtocol(CSGetOneRoleMovePosReqNew)
    self:RegisterProtocol(SCOneRoleMovePosNew, "OnSCOneRoleMovePosNew")

    self:RegisterProtocol(CSSetSkillAttackPos)
    self:RegisterProtocol(SCSkillAttackPos, "OnSCSkillAttackPos")
    self:RegisterProtocol(SCSceneEffectCrossFireTurn, "OnSCSceneEffectCrossFireTurn")
end

function Scene.ServerSpeedToClient(server_speed)
    return server_speed / 100 * Config.SCENE_TILE_WIDTH
end

local last_enter_scene = 0
local last_enter_server_id = 0
local last_enter_plat_type = 0
function Scene:OnEnterScene(protocol)
    -- print_error("Scene:OnEnterScene,scene_id:", protocol.scene_id, protocol.pos_x, protocol.pos_y)
    local scene_config = ConfigManager.Instance:GetSceneConfig(protocol.scene_id)
    if nil == scene_config then
        print_error("scene_config not find, scene_id:" .. protocol.scene_id)
        return
    end

    IS_ON_CROSSSERVER = UserVo.IsCrossServer(protocol.role_id)
    if protocol.scene_id == COMMON_CONSTS.XIN_SHOU_CUN_SCENE_ID or
            (last_enter_scene == COMMON_CONSTS.XIN_SHOU_CUN_SCENE_ID and
            scene_config.scene_type == SceneType.Common and
            last_enter_server_id == protocol.current_server_id and
            last_enter_plat_type == protocol.current_plat_type) then

        ViewManager.Instance:CloseAll()
        last_enter_scene = protocol.scene_id
        last_enter_server_id = protocol.current_server_id
        last_enter_plat_type = protocol.current_plat_type
        return
    end

    last_enter_scene = protocol.scene_id
    last_enter_server_id = protocol.current_server_id
    last_enter_plat_type = protocol.current_plat_type

    RoleWGData.Instance:SetAttr("scene_id", protocol.scene_id)
    RoleWGData.Instance:SetAttr("scene_key", protocol.scene_key)
    RoleWGData.Instance:SetAttr("obj_id", protocol.obj_id)
    RoleWGData.Instance:SetAttr("pos_x", protocol.pos_x)
    RoleWGData.Instance:SetAttr("pos_y", protocol.pos_y)
    RoleWGData.Instance:SetAttr("max_hp", protocol.max_hp)
    RoleWGData.Instance:SetAttr("hp", protocol.hp)
    RoleWGData.Instance:SetAttr("role_id", protocol.role_id)
    RoleWGData.Instance:SetAttr("origin_uid", protocol.origin_role_id)
    RoleWGData.Instance:SetAttr("origin_server_id", protocol.origin_server_id)
    RoleWGData.Instance:SetAttr("current_server_id", protocol.current_server_id)
    RoleWGData.Instance:SetAttr("current_plat_type", protocol.current_plat_type)
    RoleWGData.Instance:SetAttr("merge_plat_type", protocol.merge_plat_type)
    RoleWGData.Instance:SetAttr("merge_server_id", protocol.merge_server_id)
    local old_main_role = Scene.Instance:GetMainRole()
    if old_main_role then
        old_main_role:SetLogicPos(protocol.pos_x, protocol.pos_y)
    end

    GlobalEventSystem:Fire(SceneEventType.SCENE_LOADING_STATE_ENTER, protocol.scene_id)
    if Scene ~= nil and Scene.Instance ~= nil then
		Scene.Instance:ClearUISceneControllerList()
	end

    local scene_type = Scene.Instance:GetSceneType()
    local main_role = Scene.Instance:GetMainRole()
    if scene_type ~= SceneType.HotSpring and main_role.vo.is_in_hot_spring then
        main_role.vo.is_in_hot_spring = false
    end
end

function Scene:OnVisibleObjEnterRole(protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if scene_obj then
        local old_vo = scene_obj:GetVo()
        if nil ~= old_vo and old_vo.role_id ~= protocol.role_id then
            print_error("[Scene]Big Bug!!! 场景管理错乱 OnVisibleObjEnterRole", protocol.obj_id, old_vo.role_id, old_vo.name, protocol.role_id, protocol.role_name)
        end
        return
    end

    local yzwc_status = self:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN and
            KuafuYeZhanWangChengWGCtrl.Instance:CheckActiIsOpen()

    local role_vo = GameVoManager.Instance:CreateVo(RoleVo)
    role_vo.obj_id = protocol.obj_id

    if yzwc_status then
        role_vo.name = Language.Common.MysteryMen
    else
        role_vo.name = protocol.role_name
    end

    role_vo.pos_x = protocol.pos_x
    role_vo.pos_y = protocol.pos_y
    role_vo.uuid = protocol.uuid
    role_vo.role_id = protocol.role_id
    role_vo.dir = protocol.dir
    role_vo.move_mode_param = protocol.move_mode_param
    role_vo.role_status = protocol.role_status
    role_vo.hp = protocol.hp
    role_vo.max_hp = protocol.max_hp
    role_vo.level = protocol.level
    role_vo.camp = protocol.camp
    role_vo.prof = protocol.prof
    role_vo.sex = protocol.sex
    role_vo.vip_level = protocol.vip_level
    role_vo.rest_partner_obj_id = protocol.rest_partner_obj_id
    role_vo.move_speed = protocol.move_speed
    role_vo.distance = protocol.distance
    role_vo.attack_mode = protocol.attack_mode
    role_vo.name_color = protocol.name_color
    role_vo.move_mode = protocol.move_mode
    role_vo.authority_type = protocol.authority_type
    role_vo.husong_color = protocol.husong_color
    role_vo.husong_taskid = protocol.husong_taskid
    role_vo.guild_post = protocol.guild_post
    role_vo.mount_appeid = protocol.mount_appeid

    role_vo.jianzhen_appe = protocol.jianzhen_appe --剑阵
    role_vo.appearance = protocol.appearance

    role_vo.flyup_use_image = protocol.flyup_use_image-- 坐骑使用的筋斗云资源

    role_vo.multi_mount_res_id = protocol.multi_mount_res_id-- 双人坐骑资源id
    role_vo.multi_mount_is_owner = protocol.multi_mount_is_owner-- 是否当前双人坐骑的主人
    role_vo.multi_mount_other_uid = protocol.multi_mount_other_uid-- 一起骑乘的玩家objID

    role_vo.shenjiang_id = protocol.shenjiang_id-- 使用神将ID
    role_vo.shenjiang_special_img = protocol.shenjiang_special_img-- 神将形象
    role_vo.shenjiang_level = protocol.shenjiang_level-- 神将等级
    role_vo.shenjiang_grade = protocol.shenjiang_grade-- 神将阶数
    role_vo.shenjiang_name = protocol.shenjiang_name-- 神将名字
    role_vo.use_halo_id = protocol.use_halo_id-- 神将光环
    role_vo.use_shenjiang_fly_id = protocol.use_shenjiang_fly_id-- 神将飞升
    role_vo.love_stamp_img_id = protocol.love_stamp_img_id-- 爱情印记图片id

    role_vo.guild_id = protocol.guild_id or 0
    role_vo.guild_name = protocol.guild_name or ""

    local used_title_list = protocol.used_title_list
    -- local use_pet_titleid = TitleWGData.GetAndRemoveLingpoTitleFromList(used_title_list)
    local use_pet_titleid = 0

    for i = 1, 3 do
        if yzwc_status then
            role_vo.used_title_list[i] = 0
        else
            role_vo.used_title_list[i] = used_title_list[i] or 0
        end
    end

    role_vo.used_diy_title_name = protocol.used_diy_title_name
    -- role_vo.buff_mark_low = protocol.buff_mark_low or 0
    -- role_vo.buff_mark_high = protocol.buff_mark_high or 0
    role_vo.buff_flag = protocol.buff_flag
    role_vo.special_param = protocol.special_param or 0
    role_vo.special_param2 = protocol.special_param2 or 0
    role_vo.height = protocol.height or 0
    role_vo.special_appearance = protocol.special_appearance or 0
    role_vo.appearance_param = protocol.appearance_param or 0
    role_vo.appearance_param_extend = protocol.appearance_param_extend or {}

    role_vo.shenbing_flag = protocol.shenbing_flag or 0
    role_vo.lover_name = protocol.lover_name or ""
    role_vo.jilian_type = protocol.jilian_type or 0

    role_vo.wing_appeid = protocol.wing_appeid or 0
    role_vo.fabao_appeid = protocol.fabao_appeid or 0
    role_vo.soulboy_lt_id = protocol.soulboy_lt_id or 0
    role_vo.shenwu_appeid = protocol.shenwu_appeid or 0
    role_vo.soulboy_lg_id = protocol.soulboy_lg_id or 0
    role_vo.soulboy_ls_id = protocol.soulboy_ls_id or 0
    role_vo.lingtong_wing_id = protocol.lingtong_wing_id or 0
    role_vo.jianzhen_appeid = protocol.jianzhen_appeid or 0
    role_vo.lingchong_appeid = protocol.lingchong_appeid or 0


    role_vo.used_pet_jie = protocol.used_pet_jie or 0
    role_vo.xianjie_level = protocol.xianjie_level or 0
    role_vo.jinghua_husong_status = protocol.jinghua_husong_status or 0
    role_vo.use_pet_titleid = use_pet_titleid or 0
    role_vo.tianxiange_level = protocol.tianxiange_level or 0

    role_vo.chengjiu_title_level = protocol.chengjiu_title_level-- 成就称号等级

    role_vo.pet_id = protocol.pet_id-- 使用宠物ID
    role_vo.pet_special_img = protocol.pet_special_img-- 宠物形象
    role_vo.pet_level = protocol.pet_level-- 宠物等级
    role_vo.pet_zizhi_level = protocol.pet_zizhi_level-- 宠物资质等级
    role_vo.use_halo_img = protocol.use_halo_img or 0-- 宠物光环
    role_vo.pet_name = protocol.pet_name-- 宠物名字
    role_vo.use_fly_img = protocol.use_fly_img-- 宠物飞升
    role_vo.use_fazhen_grade = protocol.use_fazhen_grade-- 宠物法阵

    role_vo.use_fabao_id = protocol.use_fabao_id or 0
    role_vo.use_fabao_special_img_id = protocol.use_fabao_special_img_id or 0 --法宝幻化

    role_vo.jingling_id = protocol.jingling_id-- 使用精灵ID
    role_vo.jingling_name = protocol.jingling_name-- 精灵名字
    role_vo.use_jingling_imageid = protocol.jingling_use_imageid-- 精灵形象
    role_vo.use_jingling_special_img = protocol.jingling_phantom_img-- 精灵幻化形象
    role_vo.use_jingling_level = protocol.use_jingling_level-- 精灵等级
    role_vo.use_jingling_halo_img = protocol.jingling_halo_img-- 精灵光环
    role_vo.jingjie_level = protocol.jingjie_level-- 境界等级
    role_vo.fame = protocol.fame                -- 名望
    role_vo.use_baby_id = protocol.use_baby_id-- 宝宝使用id
    role_vo.jump_speed = protocol.jump_speed
    role_vo.jump_aspeed = protocol.jump_aspeed
    role_vo.jump_move_speed = protocol.jump_move_speed
    role_vo.tianming_appid = protocol.tianming_appid
    role_vo.role_title_appeid = protocol.role_title_appeid
    role_vo.own_drop_boss_count = protocol.own_drop_boss_count-- 怪物掉落归属个数
    role_vo.guard_id = protocol.guard_id-- 守护小鬼外观
    role_vo.is_in_xunyou = protocol.is_in_xunyou or 0 --是否在巡游中
    role_vo.origin_uid = protocol.origin_uid
    role_vo.origin_plat_type = protocol.origin_plat_type
    role_vo.plat_type = protocol.plat_type
    role_vo.plat_name = protocol.plat_name
    role_vo.origin_server_id = protocol.origin_server_id
    role_vo.fb_kill_role_times = protocol.fb_kill_role_times or 0 -- 副本内击杀玩家次数
    role_vo.zhandui3v3_id = protocol.zhandui3v3_id
    role_vo.zhandui3v3_name = protocol.zhandui3v3_name
    role_vo.zhandui3v3_lingpai_id = protocol.zhandui3v3_lingpai_id
    role_vo.zhandui3v3_lingpai_name = protocol.zhandui3v3_lingpai_name
    role_vo.cross3v3_side = protocol.cross3v3_side
    role_vo.is_team_leader = protocol.is_team_leader
    role_vo.tianshenshenqi_appeid = protocol.tianshenshenqi_appeid

    role_vo.task_appearn = protocol.task_appearn
    role_vo.task_appearn_param_1 = protocol.task_appearn_param_1
    role_vo.disguise_type = protocol.disguise_type
    role_vo.information_type = protocol.information_type
    role_vo.information_num = protocol.information_num
    role_vo.merge_server_id = protocol.merge_server_id
    role_vo.merge_plat_type = protocol.merge_plat_type

    role_vo.wuhun_id = protocol.wuhun_id
    role_vo.wuhun_lv = protocol.wuhun_lv
    role_vo.beast_id = protocol.beast_id
    role_vo.shaungsheng_tianshen_aura_id = protocol.shaungsheng_tianshen_aura_id
    role_vo.stage_level = protocol.stage_level
    role_vo.god_or_demon_type = protocol.god_or_demon_type
    role_vo.god_or_demon_level = protocol.god_or_demon_level
    role_vo.god_or_demon_grade = protocol.god_or_demon_grade
    role_vo.longzhu_skill_level = protocol.longzhu_skill_level
    role_vo.beast_skin = protocol.beast_skin
    role_vo.role_diy_appearance = protocol.role_diy_appearance

    AvatarManager.Instance:SetAvatarKey(role_vo.origin_uid > 0 and role_vo.origin_uid or protocol.role_id, protocol.avatar_key_big, protocol.avatar_key_small)
    local obj = self:CreateRole(role_vo)
    if nil == obj then
        return
    end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then
        --永夜之巅特殊处理时装显示
        EternalNightWGData.Instance:SetSceneEquipShiZhuang(role_vo.uuid,role_vo.appearance)
        obj:UpdateAppearanceInEternalNight()
    end

    obj:SetAttr("shield_vip_flag", protocol.shield_vip_flag or 0)
    obj:SetAttr("baozhu_act_flag", protocol.baozhu_act_flag or 0)

    if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
        role_vo.area_index = protocol.special_param3
        obj:SetAttr("area_index", protocol.special_param3)
    end

    if protocol.move_speed and protocol.move_speed > 0 then
        obj:SetAttr("move_speed", protocol.move_speed)
    end
    
    if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss and not obj:IsMainRole() then
        local xianjie_boss_equip = {}
        xianjie_boss_equip.slot = protocol.special_param_tb.temp_low
        xianjie_boss_equip.page = protocol.special_param_tb.temp_high
        xianjie_boss_equip.active_tb = bit:d2b_two(protocol.special_param2)
        role_vo.xianjie_boss_equip = xianjie_boss_equip
        obj:SetAttr("xianjie_equip", xianjie_boss_equip)
    end

    if protocol.special_param2 == 1 then
        obj:SetAttr("special_param2", protocol.special_param2)
    end
   if Scene.Instance:GetSceneType() == SceneType.KFZhuXieZhanChang and
        ActivityWGCtrl.Instance:CheckKFZhuXieActiIsOpen() then
        local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)
        if protocol.fb_kill_role_times > 1 then
            local title_info = ActivityWGData.Instance:GetFBMltiKillTitle(protocol.fb_kill_role_times)
            if role then
                local title_res = title_info.title_res
                role:SetAttr("used_title_list", {title_res})
            end
        end
    elseif Scene.Instance:GetSceneType() == SceneType.ZhuXie and
        ActivityWGCtrl.Instance:CheckZhuXieActiIsOpen() then
        local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)
        if protocol.fb_kill_role_times > 1 then
            local title_info = ActivityWGData.Instance:GetFBMltiKillTitle(protocol.fb_kill_role_times)
            if role then

                local title_res = title_info.title_res
                role:SetAttr("used_title_list", {title_res})
            end
        end
    elseif Scene.Instance:GetSceneType() == SceneType.XianMengzhan then
       local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)
       if role then
           GuildBattleRankedWGCtrl.Instance:IsShowTitle(protocol.role_id,role)
       end
    end

    BossWGData.Instance:GetIsOwnBossByUid(obj)
    --巡游
    obj:SetMarryFlag(protocol.is_in_xunyou)

    if role_vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP1 then
        local height = bit:_rshift(role_vo.height, 4)
        local percent = bit:_and(role_vo.height, 15) / 15
        local dir = Vector3(math.cos(role_vo.dir), 0, math.sin(role_vo.dir))
        obj:Jump2(role_vo.move_mode_param, dir, height, percent)
    end
    -- end
    -- 物体进入、离开角色视野抛出一个事件(放在最后)
    GlobalEventSystem:Fire(SceneEventType.ROLE_ENTER_ROLE, protocol.obj_id)
end

function Scene:OnVisibleObjLeave(protocol)
    -- print_error("OnVisibleObjLeave",protocol)
    local scene_obj = self:GetObj(protocol.obj_id)
    if nil == scene_obj then
        -- print_error("[Scene] Big Bug, 场景管理错乱 OnVisibleObjLeave ", protocol.obj_id)
        return
    end
    FightWGData.Instance:ClearOtherRoleEffectList(protocol.obj_id)

        if scene_obj:IsMarryObj() then
            local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN) or {}
            if activity_info.status and activity_info.status == ACTIVITY_STATUS.XUNYOU and MarryWGData.Instance:IsMarryUser() then
                return
            end
        end

        if GuajiCache.target_obj ~= nil and GuajiCache.target_obj == scene_obj then
            GuajiCache.target_obj = nil
        end

        if SceneObj.select_obj ~= nil and SceneObj.select_obj == scene_obj then
            SceneObj.select_obj = nil
        end

        if self.main_role and self.main_role:GetAttackTarget() == scene_obj then
            self.main_role:SetAttackTarget()
        end

        if scene_obj:IsRole() then
            Trycall(function ()
                scene_obj:CheckTrackRoleFollow()
            end)

            self:DeleteRolFollower(scene_obj)
            self:DeleteBoatByRole(protocol.obj_id)
        end

        if scene_obj:IsMonster() or scene_obj:IsPet() then
            -- print_error("OnVisibleObjLeave scene_obj.vo.hp "..scene_obj.vo.hp)
            if scene_obj.vo.hp <= 0 then
                self:DeleteObj(protocol.obj_id, 1.4)
                self.obj_move_info_list[protocol.obj_id] = nil
            else
                -- print_error("scene obj leave not die")
                self:DeleteObj(protocol.obj_id, 0)
            end

            if scene_obj:IsMonster() then
                self:DeleteEffect(protocol.obj_id)
            end
        else
            local is_gather = scene_obj:IsGather()
            -- print_error("OnVisibleObjLeave SceneType ",scene_obj.obj_id)
            self:DeleteObj(protocol.obj_id, 0)

            if is_gather then
                self:RemoveInVaildGatherByWaitList(protocol.obj_id)
            end
        end
        GlobalEventSystem:Fire(SceneEventType.OBJ_LEVEL_ROLE, protocol.obj_id)
end

function Scene:DeleteRolFollower(obj)
    -- if obj and obj:IsRole() then
    -- local pet_obj = obj:GetPetObj()
    -- if nil ~= pet_obj then
    -- self:DeleteClientObj(pet_obj:GetObjId())
    -- end
    -- local shenjiang_obj = obj:GetShenJiangObj()
    -- if nil ~= shenjiang_obj then
    -- self:DeleteClientObj(shenjiang_obj:GetObjId())
    -- end

    -- local truck_obj = obj:GetTruckObj()
    -- if nil ~= truck_obj then
    -- self:DeleteClientObj(truck_obj:GetObjId())
    -- end

    -- local soulboy_obj = obj:GetSoulBoyObj()
    -- if nil ~= soulboy_obj then
    -- self:DeleteClientObj(soulboy_obj:GetObjId())
    -- end

    -- local jingling_obj = obj:GetJingLingObj()
    -- if nil ~= jingling_obj then
    -- self:DeleteClientObj(jingling_obj:GetObjId())
    -- end

    -- local xiaogui_obj = obj:GetXiaoGuiObj()
    -- if nil ~= xiaogui_obj then
    -- self:DeleteClientObj(xiaogui_obj:GetObjId())
    -- end
    -- end
end

function Scene:OnObjMove(protocol)
    local scene_obj = self:GetObj(protocol.obj_id)
    if nil == scene_obj then
        return
    end

    if scene_obj:IsMainRole() and (not scene_obj:IsFear() and not scene_obj:IsRepelMoving()) then
        return
    end

    if scene_obj:IsRole() and scene_obj:IsSwordSprint() then
        return
    end

    -- 轻功状态时
    if scene_obj:IsRole() and (scene_obj:IsQingGong() or scene_obj.vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP1) then
        self:DoQingGongMove(scene_obj, protocol.pos_x, protocol.pos_y, protocol.dir, protocol.distance)
        return
    end

    if scene_obj:IsMarryObj() then
        scene_obj.vo.pos_x = protocol.pos_x
        scene_obj.vo.pos_y = protocol.pos_y
        scene_obj.vo.dir = protocol.dir
        scene_obj.vo.distance = protocol.distance
    end

    if protocol.distance > 0.1 then
        self:DoObjMove(scene_obj, protocol.pos_x, protocol.pos_y, protocol.dir, protocol.distance, protocol.height)
    else
    	if scene_obj:IsCharacter() then
    		scene_obj:ChangeToCommonState()
    	end

        if not scene_obj:IsDeleted() and scene_obj:IsMonster() and not scene_obj:GetIsLimitRotate() then
            scene_obj:SetRotationByDir(protocol.dir)
        end

        scene_obj:SetLogicPos(protocol.pos_x, protocol.pos_y, protocol.height)
    end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.YEZHANWANGCHENGFUBEN and protocol.is_transmit == 1 and scene_obj:IsRole() then
        scene_obj:PlayChuShengEffect()
    end
end

function Scene:DoObjDir(scene_obj, pos_x, pos_y, dir, distance)
    -- body
    if not scene_obj or not scene_obj:IsCharacter() then return end
    local x = math.floor(pos_x + math.cos(dir) + 0.5)
    local y = math.floor(pos_y + math.sin(dir) + 0.5)
    scene_obj:SetDirectionByXY(x, y)
end

function Scene:DoObjMove(scene_obj, pos_x, pos_y, dir, distance, height)
    if nil ~= scene_obj and scene_obj:IsCharacter() then
        local logic_x, logic_y = scene_obj:GetLogicPos()
        if math.abs(logic_x - pos_x) >= 8 or math.abs(logic_y - pos_y) >= 8 then
            scene_obj:SetLogicPos(pos_x, pos_y)
        end
        -- if distance > 0.1 then
        scene_obj:DoMove(math.floor(pos_x + math.cos(dir) * distance + 0.5), math.floor(pos_y + math.sin(dir) * distance + 0.5), nil, nil, height or 0)
        -- else
        -- scene_obj:ChangeToCommonState()
        -- end
    end
end

function Scene:DoQingGongMove(scene_obj, pos_x, pos_y, dir, distance)
    local target_pos_x = math.floor(pos_x + math.cos(dir) * distance)
    local target_pos_y = math.floor(pos_y + math.sin(dir) * distance)
    scene_obj:SaveMoveTarget(Vector2(target_pos_x, target_pos_y))

    pos_x = math.floor(pos_x + math.cos(dir) * 100)
    pos_y = math.floor(pos_y + math.sin(dir) * 100)
    local target_x, target_y = GameMapHelper.LogicToWorld(pos_x, pos_y)
    scene_obj.draw_obj:SetQingGongTarget(Vector3(target_x, 0, target_y))
end


function Scene:OnVisibleObjEnterFalling(protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if scene_obj then
        local old_vo = scene_obj:GetVo() or {}
        if nil ~= old_vo and old_vo.item_id ~= protocol.item_id then
            print_error("[Scene]Big Bug!!! 场景管理错乱 OnVisibleObjEnterFalling", old_vo.item_id, old_vo.monster_id, protocol.item_id, protocol.monster_id)
        end
        return
    end

    --神魔禁地 组队装备本，跨服boss 天帝陵只显示自己的掉落
    local _, boss_info = BossWGData.Instance:GetIsBossVipScene(Scene.Instance:GetSceneId())
    local level = boss_info and boss_info.level or -1
    local scene_type = Scene.Instance:GetSceneType()
    if protocol.owner_role_id ~= RoleWGData.Instance.role_vo.role_id and
        (scene_type == SceneType.TEAM_EQUIP_FB or level == 0 or scene_type == SceneType.HIGH_TEAM_EQUIP_FB
        or scene_type == SceneType.KF_BOSS or scene_type == SceneType.DABAO_BOSS or scene_type == SceneType.BOOTYBAY_FB
        or scene_type == SceneType.TEAM_BOOTYBAY_FB) then
        return
    end

    local fallitem_vo = GameVoManager.Instance:CreateVo(FallItemVo)
    fallitem_vo.obj_id = protocol.obj_id
    fallitem_vo.item_id = protocol.item_id
    fallitem_vo.obj_pos_x = protocol.obj_pos_x -- 掉落怪的位置
    fallitem_vo.obj_pos_y = protocol.obj_pos_y
    fallitem_vo.pos_x = protocol.pos_x
    fallitem_vo.pos_y = protocol.pos_y -- 掉落物的位置
    fallitem_vo.owner_role_id = protocol.owner_role_id
    fallitem_vo.team_index = protocol.team_index
    fallitem_vo.coin = protocol.coin
    fallitem_vo.monster_id = protocol.monster_id
    fallitem_vo.item_num = protocol.item_num
    fallitem_vo.is_client_fall = false
    fallitem_vo.drop_time = protocol.drop_time
    fallitem_vo.create_time = Status.NowTime
    fallitem_vo.lose_owner_time = protocol.lose_owner_time
    fallitem_vo.scene_id = Scene.Instance:GetSceneId()
    fallitem_vo.is_buff_falling = protocol.is_buff_falling
    fallitem_vo.buff_appearance = protocol.buff_appearance
    fallitem_vo.star_num = protocol.star_num
    self:CreateFallItem(fallitem_vo)
end

function Scene:OnVisibleObjEnterMonster(protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if scene_obj then
        local old_vo = scene_obj:GetVo() or {}
        if nil ~= old_vo and old_vo.monster_id ~= protocol.monster_id then
            print_error("[Scene]Big Bug!!! 场景管理错乱 OnVisibleObjEnterMonster", old_vo.monster_id, old_vo.level, protocol.monster_id, protocol.level)
        end
        return
    end

    local monster_vo = GameVoManager.Instance:CreateVo(MonsterVo)
    monster_vo.obj_id = protocol.obj_id
    monster_vo.monster_id = protocol.monster_id
    monster_vo.is_has_owner = protocol.is_has_owner
    monster_vo.pos_x = protocol.pos_x
    monster_vo.pos_y = protocol.pos_y
    monster_vo.hp = protocol.hp
    monster_vo.level = protocol.level
    monster_vo.max_hp = protocol.max_hp
    -- monster_vo.move_speed = Scene.ServerSpeedToClient(protocol.move_speed)
    monster_vo.move_speed = protocol.move_speed

    --print_error("怪物移动速度 >>>> ", protocol.move_speed, monster_vo.monster_id)
    monster_vo.dir = protocol.dir
    monster_vo.distance = protocol.distance
    -- monster_vo.buff_mark_low = protocol.buff_mark_low
    -- monster_vo.buff_mark_high = protocol.buff_mark_high
    monster_vo.buff_flag = protocol.buff_flag
    monster_vo.lose_owner_time = protocol.lose_owner_time
    monster_vo.gamer_name = protocol.gamer_name
    monster_vo.special_param = protocol.special_param
    monster_vo.summoner_obj_id = protocol.summoner_obj_id
    monster_vo.wabao_owner_uuid = protocol.wabao_owner_uuid
    monster_vo.monster_type = protocol.monster_type
    monster_vo.monster_key = protocol.monster_key
    monster_vo.shield_value = protocol.shield_value
    monster_vo.max_shield_value = protocol.max_shield_value
    monster_vo.shield_recover_time = protocol.shield_recover_time
    monster_vo.monster_sub_type = protocol.monster_sub_type

    -- print_error("summoner_obj_id:::", monster_vo.summoner_obj_id, monster_vo.obj_id, monster_vo.monster_id)
    local monster_obj = self:CreateMonster(monster_vo)
    if not monster_obj then
        return
    end

    --print_error(monster_vo)
    if Scene.Instance:GetSceneLogic():CanMonsterDoJump(monster_vo.special_param) then
        if not self:GetMonsterJumpFlag(monster_vo.obj_id) then
            self:SetMonsterJumpFlag(monster_vo.obj_id, MONSTER_JUMP_FLAG.WAIT_JUMP)
        end
    end

    self:DoObjDir(monster_obj, monster_vo.pos_x, monster_vo.pos_y, monster_vo.dir)
    if monster_vo.distance > 0.1 then
        self:DoObjMove(monster_obj, monster_vo.pos_x, monster_vo.pos_y, monster_vo.dir, monster_vo.distance)
    end
    -- print_error("OnVisibleObjEnterMonster end ",protocol.obj_id,monster_vo.monster_id)
    GlobalEventSystem:Fire(ObjectEventType.VISIBLE_OBJ_ENTER_MONSTER, monster_vo, monster_obj)
    --print_error("怪物刷新视野",protocol)

    self:CreateSDX(monster_vo, "Strengthen_buff_01")


    local own_id = monster_obj:GetOwnerObjId()
    if monster_vo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_CALL and own_id ~= nil then
        if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and GuajiCache.target_obj:GetObjId() == own_id then
            GuajiWGCtrl.Instance:OnObjInvalid(GuajiCache.target_obj)
            GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, monster_obj, SceneTargetSelectType.SELECT)
        end
    end
end

function Scene:CreateSDX(monster_vo, res)
    if monster_vo.summoner_obj_id < 0 or 10000 < monster_vo.summoner_obj_id then
        return
    end

    local effect_vo = GameVoManager.Instance:CreateVo(EffectObjVo)
    effect_vo.main_deliverer_obj_id = monster_vo.main_deliverer_obj_id or effect_vo.main_deliverer_obj_id
    effect_vo.deliverer_obj_id = monster_vo.obj_id
    effect_vo.summoner_obj_id = monster_vo.summoner_obj_id
    effect_vo.name = "Line"
    effect_vo.src_pos_x = monster_vo.pos_x
    effect_vo.src_pos_y = monster_vo.pos_y
    effect_vo.res = res
    effect_vo.product_id = PRODUCT_ID_TRIGGER.CLIENT_SHANDIANXIAN_LINE
    return Scene.Instance:CreateEffectObj(effect_vo)
end

function Scene:ClearJumpTime()
    self.can_jump_time = nil
     RoleWGData.DeleteRolePlayerPrefsInt("EnterFBFlag") --主动进入删除key
end

function Scene:GetMonsterJumpFlag(obj_id)
    if self.can_jump_time == nil then
        local flag = RoleWGData.GetRolePlayerPrefsInt("EnterFBFlag")
        local orgin_role_id = RoleWGData.Instance:InCrossGetOriginUid()
        if flag == 1 then
            self.can_jump_time = Status.NowTime + 3
        else
            self.can_jump_time = Status.NowTime
            RoleWGData.SetRolePlayerPrefsInt("EnterFBFlag", 1)
        end
    end

    if self.can_jump_time > Status.NowTime then
        return nil
    end
    if not self.jump_obj_flag_list then
        return nil
    end
    return self.jump_obj_flag_list[obj_id]
end

function Scene:SetMonsterJumpFlag(obj_id, flag)
    if not self.jump_obj_flag_list then
        self.jump_obj_flag_list = {}
    end
    self.jump_obj_flag_list[obj_id] = flag
end

function Scene:ClearJumpFlag()
    self.jump_obj_flag_list = {}
end

--宠物进入视野
function Scene:OnSCVisibleObjEnterPet(protocol)
    local pet_scene_obj = self:GetObjectByObjId(protocol.pet_obj_id)
    if pet_scene_obj then
        -- local old_vo = pet_scene_obj:GetVo() or {}
        -- if nil ~= old_vo and old_vo.pet_id ~= protocol.pet_id then
        --     print_error("[Scene]Big Bug!!! 场景管理错乱 OnSCVisibleObjEnterPet", protocol.pet_obj_id, old_vo.pet_id, old_vo.obj_name, protocol.pet_id, protocol.pet_obj_name)
        -- end
        return
    end

    local pet_vo = GameVoManager.Instance:CreateVo(PetObjVo)
    pet_vo.pet_id = protocol.pet_id
    pet_vo.pet_type = protocol.pet_type
    pet_vo.obj_id = protocol.pet_obj_id
    pet_vo.obj_name = protocol.pet_obj_name
    pet_vo.image_id = protocol.image_id
    pet_vo.dir = protocol.dir
    pet_vo.distance = protocol.distance
    pet_vo.hp = protocol.hp
    pet_vo.max_hp = protocol.max_hp
    pet_vo.move_speed = protocol.speed
    pet_vo.pos_x = protocol.pos_x
    pet_vo.pos_y = protocol.pos_y
    pet_vo.grow = protocol.grow
    pet_vo.owner_obj_id = protocol.owner_objid
    pet_vo.owner_obj_name = protocol.owner_obj_name
    pet_vo.pet_level = protocol.pet_type
    pet_vo.pet_zizhi = protocol.pet_type

    local pet_obj = self:CreatePet(pet_vo)
    if pet_obj then
        if pet_obj:IsOnwerPet() then
            pet_obj:SetIsPerformer(true)
            -- pet_obj:SetIsDeputyPet(false)
        end

        if pet_vo.distance > 0.1 then
            self:DoObjMove(pet_obj, pet_vo.pos_x, pet_vo.pos_y, pet_vo.dir, pet_vo.distance)
        end
    end
end

function Scene:OnVisibleObjEnterBattleFieldShenShi(protocol)

end

local waiguan_appearance = {
    "shizhuang_tail", "shizhuang_bracelet", "shizhuang_wuqi", "shizhuang_body", "wing_appeid",
     "fashion_guanghuan", "shizhuang_photoframe", "jianzhen_appeid", "shizhuang_guanghuan",
     "fashion_photoframe", "fashion_wuqi", "fashion_foot", "fashion_body", "fabao_appeid", "shizhuang_mask", "shizhuang_bubble", "shizhuang_foot",
     "wing_special_imageid", "shenwu_appeid", "linggong_appeid", "shizhuang_belt", "footprint_effect_id",
     "default_face_res_id", "default_hair_res_id", "default_body_res_id", "fazhen_id", "skill_halo_id", "part_color",
}
function Scene:IsRoleApperanceChange(scene_obj, new_appearance)
    local role_vo = scene_obj:GetVo()
    if nil == role_vo then
        return false
    end

    local old_appearance = role_vo.appearance
    for k,v in pairs(waiguan_appearance) do
        if old_appearance[v] ~= nil and new_appearance[v] ~= nil and old_appearance[v] ~= new_appearance[v] then
            return true
        end
    end

    return false
end

function Scene:IsRoleDiyApperanceChange(scene_obj, new_diy_appearance)
    local role_vo = scene_obj:GetVo()
    if nil == role_vo then
        return false
    end

    local old_role_diy_appearance = role_vo.role_diy_appearance

    local is_same_table = IsSameTable(new_diy_appearance, old_role_diy_appearance) 

    return is_same_table
end

function Scene:OnRoleVisibleChange(protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if nil == scene_obj or not scene_obj:IsRole() then
        return
    end

    local scene_type = Scene.Instance:GetSceneType()
    local can_appearance_change = scene_type ~= SceneType.YEZHANWANGCHENGFUBEN and not KuafuYeZhanWangChengWGCtrl.Instance:CheckActiIsOpen()
    local is_appearance_change = self:IsRoleApperanceChange(scene_obj, protocol.appearance)
    if can_appearance_change then
        scene_obj:SetAttr("appearance", protocol.appearance)
    end

    local is_change_diy_appearance = not self:IsRoleDiyApperanceChange(scene_obj, protocol.role_diy_appearance)
    if is_change_diy_appearance then
        scene_obj:SetAttr("role_diy_appearance", protocol.role_diy_appearance)
    end

    local is_main_role = scene_obj:GetType() == SceneObjType.MainRole
    if is_main_role then
        if is_appearance_change then
            GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_APPERANCE_CHANGE)
        end

        GlobalEventSystem:Fire(OtherEventType.CHANGE_HEAD_ICON)
        --收集头像框信息(这个协议只存自己外观形象)
        local role_id = RoleWGData.Instance:GetRoleVo().role_id or 0
        AvatarManager.Instance:SetAvatarFrameKey(role_id, protocol.appearance.fashion_photoframe)
        AvatarManager.Instance:SetAvatarBubbleKey(role_id, protocol.appearance.fashion_bubble)
    end
end

function Scene:OnVisibleObjEnterGather(protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if scene_obj then
        self:DeleteObj(protocol.obj_id)
    end

    local gather_vo = GameVoManager.Instance:CreateVo(GatherVo)
    gather_vo.obj_id = protocol.obj_id
    gather_vo.gather_id = protocol.gather_id
    gather_vo.special_gather_type = protocol.special_gather_type
    gather_vo.pos_x = protocol.pos_x
    gather_vo.pos_y = protocol.pos_y
    gather_vo.param = protocol.param
    gather_vo.param1 = protocol.param1
    gather_vo.param2 = protocol.param2
    gather_vo.param3 = protocol.param3
    gather_vo.param4 = protocol.param4
    gather_vo.param5 = protocol.param5
    gather_vo.max_gather_times = protocol.max_gather_times
    gather_vo.has_gather_times = protocol.has_gather_times
    gather_vo.disappear_time = protocol.disappear_time
    gather_vo.can_gather_time = protocol.can_gather_time
    local gather_obj = self:CreateGatherObj(gather_vo)

    GlobalEventSystem:Fire(ObjectEventType.VISIBLE_OBJ_ENTER_GATHER, gather_vo, gather_obj)
end

function Scene:OnVisibleObjEnterWorldEventObj(protocol)
    if protocol.obj_id then
        return
    end

    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if scene_obj then
        local old_vo = scene_obj:GetVo() or {}
        if nil ~= old_vo and old_vo.world_event_id ~= protocol.world_event_id then
            print_error("[Scene]Big Bug!!! 场景管理错乱 OnVisibleObjEnterWorldEventObj", old_vo.world_event_id, protocol.world_event_id)
        end
        return
    end

    local event_vo = GameVoManager.Instance:CreateVo(EventVo)
    event_vo.obj_id = protocol.obj_id
    event_vo.world_event_id = protocol.world_event_id
    event_vo.pos_x = protocol.pos_x
    event_vo.pos_y = protocol.pos_y
    event_vo.hp = protocol.hp
    event_vo.max_hp = protocol.max_hp
    -- event_vo.move_speed = Scene.ServerSpeedToClient(protocol.move_speed)
    event_vo.move_speed = protocol.move_speed
    event_vo.param_list = protocol.param_list
    event_vo.dir = protocol.dir
    event_vo.distance = protocol.distance

    self:CreateEventObj(event_vo)

    if event_vo.distance > 0.1 then
        self:DoObjMove(event_vo.pos_x, event_vo.pos_y, event_vo.obj_id, event_vo.dir, event_vo.distance)
    end
end

function Scene:OnSCVisibleObjEnterMarryObj(protocol)
    local scene_obj = self:GetObj(protocol.obj_id)
    if scene_obj then
        local old_vo = scene_obj:GetVo() or {}
        if nil ~= old_vo and old_vo.marry_seq ~= protocol.marry_seq then
            print_error("[Scene]Big Bug!!! 场景管理错乱 OnSCVisibleObjEnterMarryObj", old_vo.marry_seq, protocol.marry_seq)
        end
        return
    end

    local marry_vo = GameVoManager.Instance:CreateVo(MarryObjVo)
    marry_vo.obj_id = protocol.obj_id
    marry_vo.marry_seq = protocol.marry_seq
    marry_vo.dir = protocol.dir
    marry_vo.pos_x = protocol.pos_x
    marry_vo.pos_y = protocol.pos_y
    marry_vo.hp = 100
    marry_vo.distance = protocol.distance
    marry_vo.move_speed = protocol.move_speed
    local obj = self:CreateMarryObj(marry_vo)
    if obj == nil then
        return
    end

    if marry_vo.distance > 0.1 then
        self:DoObjMove(obj, marry_vo.pos_x, marry_vo.pos_y, marry_vo.dir, marry_vo.distance)
    end
end

function Scene:OnVisibleObjEnterRoleShadow(protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if scene_obj then
        local old_vo = scene_obj:GetVo() or {}
        if nil ~= old_vo and old_vo.role_id ~= protocol.role_id then
            print_error("[Scene]Big Bug!!! 场景管理错乱 OnVisibleObjEnterRoleShadow", old_vo.role_id, old_vo.name, protocol.role_id, protocol.role_name)
        end
        return
    end
    local role_vo = GameVoManager.Instance:CreateVo(RoleVo)
    local appearance = protocol.appearance
    role_vo.obj_id = protocol.obj_id

    if Scene.Instance:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN then
        local main_role_side_type = KuafuYeZhanWangChengWGData.Instance:GetSelfSideType()
        local this_shadow_is_same_side = main_role_side_type == protocol.special_param
        local color = this_shadow_is_same_side and  COLOR3B.WHITE or COLOR3B.RED
        role_vo.name = ToColorStr(Language.Common.MysteryMen, color)
        local def_wing = NewAppearanceWGData.Instance:GetAdvancedImageCfgByType(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING).appe_image_id
        role_vo.wing_appeid = def_wing
    else
        role_vo.name = protocol.role_name
        role_vo.wing_appeid = appearance.wing_appeid
    end

    --时装外观
    role_vo.fashion_photoframe = appearance.fashion_photoframe
    role_vo.shizhuang_bracelet = appearance.shizhuang_bracelet
    role_vo.fashion_wuqi = appearance.fashion_wuqi
    role_vo.qilinbi = appearance.qilinbi
    role_vo.fashion_foot = appearance.fashion_foot
    role_vo.wuqi_id = appearance.wuqi_id
    role_vo.shizhuang_body = appearance.shizhuang_body
    role_vo.fashion_guanghuan = appearance.fashion_guanghuan
    role_vo.shizhuang_photoframe = appearance.shizhuang_photoframe
    role_vo.fashion_bubble = appearance.fashion_bubble
    role_vo.wing_jinhua_grade = appearance.wing_jinhua_grade
    role_vo.shizhuang_mask = appearance.shizhuang_mask
    role_vo.tail = appearance.tail
    role_vo.body_color = appearance.body_color
    role_vo.waist = appearance.waist
    role_vo.shizhuang_bubble = appearance.shizhuang_bubble
    role_vo.shizhuang_foot = appearance.shizhuang_foot
    role_vo.shouhuan = appearance.shouhuan
    role_vo.wing_special_imageid = appearance.wing_special_imageid
    role_vo.linggong_appeid = appearance.linggong_appeid
    role_vo.shizhuang_belt = appearance.shizhuang_belt
    role_vo.footprint_effect_id = appearance.footprint_effect_id
    role_vo.shizhuang_tail = appearance.shizhuang_tail
    role_vo.fabao_appeid = appearance.fabao_appeid
    role_vo.jianzhen_appeid = appearance.jianzhen_appeid
    role_vo.soulboy_lt_id = appearance.soulboy_lt_id
    role_vo.shenwu_appeid = appearance.shenwu_appeid
    role_vo.soulboy_lg_id = appearance.soulboy_lg_id
    role_vo.soulboy_ls_id = appearance.soulboy_ls_id
    role_vo.lingtong_wing_id = appearance.lingtong_wing_id

    role_vo.pos_x = protocol.pos_x
    role_vo.pos_y = protocol.pos_y

    role_vo.role_id = protocol.role_id
    role_vo.uuid = protocol.uuid
    role_vo.origin_uid = protocol.origin_uid
    role_vo.level = protocol.level
    role_vo.prof = protocol.prof
    role_vo.sex = protocol.sex
    role_vo.camp = protocol.camp
    role_vo.hp = protocol.hp
    role_vo.max_hp = protocol.max_hp
    -- role_vo.move_speed = Scene.ServerSpeedToClient(protocol.move_speed)
    role_vo.move_speed = protocol.move_speed
    role_vo.dir = protocol.dir
    role_vo.distance = protocol.distance
    role_vo.appearance = protocol.appearance
    role_vo.vip_level = protocol.vip_level
    role_vo.pet_id = protocol.pet_id
    role_vo.pet_name = protocol.pet_name
    role_vo.guild_id = protocol.guild_id
    role_vo.guild_name = protocol.guild_name
    role_vo.guild_post = protocol.guild_post
    role_vo.is_shadow = 1 --是影子
    role_vo.attack_mode = 1 -- 全体模式
    role_vo.use_pet_halo_img = protocol.use_pet_halo_img
    role_vo.used_pet_jie = protocol.used_pet_jie
    role_vo.soulboy_lt_id = protocol.soulboy_lt_id
    role_vo.soulboy_lg_id = protocol.soulboy_lg_id
    role_vo.soulboy_ls_id = protocol.soulboy_ls_id
    role_vo.lingtong_wing_id = protocol.lingtong_wing_id
    role_vo.shadow_type = protocol.shadow_type
    role_vo.shadow_param = protocol.shadow_param
    role_vo.capability = protocol.capability
    role_vo.jingjie_level = protocol.jingjie_level
    role_vo.fame = protocol.fame
    role_vo.origin_server_id = protocol.origin_server_id
    role_vo.plat_name = protocol.plat_name
    role_vo.special_param = protocol.special_param
    role_vo.guard_id = protocol.guard_id
    role_vo.move_mode = protocol.move_mode
    role_vo.special_appearance = protocol.special_appearance
    role_vo.appearance_param = protocol.appearance_param
    role_vo.plat_type = protocol.plat_type
    role_vo.merge_plat_type = protocol.merge_plat_type
    role_vo.merge_server_id = protocol.merge_server_id

    local default_diy_data = RoleDiyAppearanceWGData.Instance:GetDefaultPresetDiyCfgBySexAndProf(protocol.sex, protocol.prof)
    if not IsEmptyTable(default_diy_data) then
        role_vo.role_diy_appearance = default_diy_data
    end

    AvatarManager.Instance:SetAvatarKey(protocol.role_id, protocol.avatar_key_big, protocol.avatar_key_small)

    local role_shadow = self:CreateRole(role_vo)
    if role_shadow then
        role_shadow:SetRobot(true)      --机器人标记
        role_shadow:SetAttr("yzwc_ico",protocol.camp)
    end
    
    if role_vo.distance > 0.1 then
        self:DoObjMove(role_shadow, protocol.pos_x, protocol.pos_y, protocol.dir, protocol.distance)
    end

    GlobalEventSystem:Fire(SceneEventType.OBJ_ENTER_SHADOW, protocol.obj_id)
end

function Scene:OnVisibleObjEnterEffect(protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if scene_obj then
        local old_vo = scene_obj:GetVo() or {}
        if nil ~= old_vo and old_vo.product_id ~= protocol.product_id then
            print_error("[Scene]Big Bug!!! 场景管理错乱 OnVisibleObjEnterEffect", old_vo.product_id, protocol.product_id)
        end
        return
    end

    local effect_vo = GameVoManager.Instance:CreateVo(ServerEffectVo)
    effect_vo.obj_id = protocol.obj_id
    effect_vo.src_pos_x = protocol.pos_x
    effect_vo.src_pos_y = protocol.pos_y
    effect_vo.product_method = protocol.product_method
    effect_vo.product_id = protocol.product_id
    effect_vo.birth_time = protocol.birth_time
    effect_vo.disappear_time = protocol.disappear_time
    effect_vo.param1 = protocol.param1
    effect_vo.param2 = protocol.param2
    effect_vo.param3 = protocol.param3
    effect_vo.dir = protocol.dir
    effect_vo.name = "服务器下发特效"
    if protocol.product_method == PRODUCT_METHOD.PRODUCT_METHOD_FIRE_OBJ then
        effect_vo.pos_x = protocol.pos_x
        effect_vo.pos_y = protocol.pos_y
        if protocol.product_method == PRODUCT_METHOD.PRODUCT_METHOD_FIRE_OBJ then
            effect_vo.dir = 0
        end
    end

    -- print_error("---服务器下发特效---", protocol.product_id, protocol.product_method)
    self:CreateEffectObj(effect_vo)
end

function Scene:OnResetPost(protocol)
    --  print_error('OnResetPost',protocol)
    local main_role = self:GetMainRole()
    if main_role then
    	local logic_x, logic_y = main_role:GetLogicPos()
        if main_role:IsMove() and math.abs(logic_x - protocol.pos_x) <= 8 and math.abs(logic_y - protocol.pos_y) <= 8 then
            return
        end

        local is_sit = main_role:GetIsInSit()
        main_role:ResetSkillResetInfo()
        main_role:SetLogicPos(protocol.pos_x, protocol.pos_y)
        main_role:ChangeToCommonState()
		-- 发协议同步服务端状态，之前是直接设置客户端缓存变量
        Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
        main_role:UpdateSitState(nil, is_sit)

        if TaskGuide.Instance:CanAutoAllTask() then
            main_role:ClearJumpCache()
        else
            main_role:ContinuePath()
        end

       if (math.abs(logic_x - protocol.pos_x) >= 8 or math.abs(logic_y - protocol.pos_y) >= 8) and nil ~= MainCamera and not IsNil(MainCamera) then
            main_role:UpdateCameraFollowTarget(true)
		end

        if YunbiaoWGCtrl.Instance:GetIsClickHuSong() then
            GlobalEventSystem:Fire(ObjectEventType.FLY_TO_HUSONG_NPC)
        end
        GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_RESET_POS, protocol.pos_x, protocol.pos_y)
    end

    -- if protocol.reset_type == RESET_POST_TYPE.ASSIST then
    --     local scene_logic = self:GetSceneLogic()

    --     if not scene_logic then
    --         return
    --     end

    --     if scene_logic.AtkTeamLeaderTarget then
    --         scene_logic:AtkTeamLeaderTarget()
    --     end
    -- end

    if TaskGuide.Instance:NoviceCheckTask() then
        if ViewManager.Instance:IsOpen(GuideModuleName.TaskDialog) then
            -- 如果在发协议的时候被重置了坐标，这个时候就不会继续做任务，这里重置下标记
            TaskWGCtrl.Instance:TaskUpdateing(false)
        end
    end
end

-- 技能强行设置坐标 冲锋类技能
function Scene:OnSkillResetPos(protocol)
    -- print_error("技能强行设置坐标 冲锋类技能:::", protocol.reset_pos_type, AtkCache.skill_id, protocol.pos_x, protocol.pos_y)
    local obj = self:GetObjectByObjId(protocol.obj_id)
    if nil == obj or not obj:IsCharacter() then
        return
    end

    local reset_pos_type = protocol.reset_pos_type

    local use_server_dir = true
    if SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_JITUI == reset_pos_type then
        use_server_dir = false
    end

    -- 服务端说要同步方向，不然算背刺伤害有问题
    if use_server_dir then
        obj:SetDirectionByXY(protocol.pos_x, protocol.pos_y)
    end

    if SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_CHONGFENG == reset_pos_type then
        if not obj:IsMainRole() then
            obj:SetLogicPos(protocol.pos_x, protocol.pos_y)
        else
            obj:OnSkillResetPos(protocol.skill_id, reset_pos_type, protocol.pos_x, protocol.pos_y)
        end

    elseif SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_JITUI == reset_pos_type then
        obj:RepelMove(protocol.pos_x, protocol.pos_y)
    elseif SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_SHUNYI == reset_pos_type then
        obj:OnSkillResetPos(protocol.skill_id, reset_pos_type, protocol.pos_x, protocol.pos_y)
    elseif SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_YUEJI == reset_pos_type then
        if not obj:IsMainRole() then
            obj:DoAttack(protocol.skill_id, protocol.pos_x, protocol.pos_y)
            obj:OnSkillResetPos(protocol.skill_id, reset_pos_type, protocol.pos_x, protocol.pos_y)
        end
    elseif SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_YOULI == reset_pos_type then
        local logic_x, logic_y = obj:GetLogicPos()
        obj:SetLogicPos(protocol.pos_x, protocol.pos_y)
        if not obj:IsMainRole() then
            obj:ChangeToCommonState()
            obj:ChangeMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)

            if (math.abs(logic_x - protocol.pos_x) >= 8 or math.abs(logic_y - protocol.pos_y) >= 8) and nil ~= MainCamera and not IsNil(MainCamera) then
                obj:UpdateCameraFollowTarget(true)
            end
        end
    elseif SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_ZHENYI == reset_pos_type then
    elseif SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_SHANSHUO == reset_pos_type then
        -- obj:ResetTwinkleState()
        obj:ChangeToCommonState()
        obj:ChangeMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
        obj:SetLogicPos(protocol.pos_x, protocol.pos_y)
        -- obj:OnSkillResetPos(protocol.skill_id, reset_pos_type, protocol.pos_x, protocol.pos_y)
        if obj:IsMainRole() then
            local cfg = SkillWGData.Instance:GetBeforeSkillCfgByType(FRONT_SKILL_TYPE.TWINKLE)
            if cfg ~= nil then
                if obj:IsRidingNoFightMount() then
                    MountWGCtrl.Instance:SendMountGoonReq(0)
                end
                
                -- print_error("---闪烁回调--", AtkCache.target_obj ~= nil, GuajiCache.target_obj ~= nil)
                local attack_obj = nil
                local attack_pos_x, attack_pos_y = protocol.pos_x, protocol.pos_y

                if GuajiCache.target_obj ~= nil then
                    local target_pos_x, target_pos_y = GuajiCache.target_obj:GetLogicPos()
                    if Scene.Instance:GetObjIsInSkillRange(cfg.skill_id, GuajiCache.target_obj, target_pos_x, target_pos_y) then
                        attack_obj = GuajiCache.target_obj
                        attack_pos_x, attack_pos_y = target_pos_x, target_pos_y
                    end
                end

                FightWGCtrl.Instance:TryUseRoleSkill(cfg.skill_id, attack_obj, attack_pos_x, attack_pos_y, ATTACK_SKILL_TYPE.TWINKLE_BLACK)
            end
        end
    elseif SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_BACKSTAB == reset_pos_type then
        obj:ResetBackstapState()
        obj:ChangeToCommonState()
        obj:ChangeMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
        obj:SetLogicPos(protocol.pos_x, protocol.pos_y)
        if obj:IsMainRole() then
            local cfg = SkillWGData.Instance:GetBeforeSkillCfgByType(FRONT_SKILL_TYPE.BACKSTAB)
            if cfg ~= nil then
                if protocol.param1 ~= nil and protocol.param1 >= 0 then
                    local target_obj = self:GetObj(protocol.param1)
                    if target_obj ~= nil and not target_obj:IsDeleted() then
                        if obj:IsRidingNoFightMount() then
                            MountWGCtrl.Instance:SendMountGoonReq(0)
                        end
                        local t_pos = target_obj:GetLuaPosition()
                        local pos = obj:GetLuaPosition()
                        local t_x, t_y = target_obj:GetLogicPos()
                        local m_dir = u3dpool.v3Normalize({x = t_pos.x - pos.x, y = pos.y, z = t_pos.z - pos.z})
                        local angle = Quaternion.LookRotation(Vector3(m_dir.x, m_dir.y, m_dir.z))
                        -- local b = Quaternion.ToEulerAngles(angle)
                        local delta_pos = u3dpool.v2Sub({x = pos.x, y = pos.y}, {x = t_pos.x, y = t_pos.y})
                        local move_dir = u3dpool.v2Normalize(delta_pos)
                        local dir = math.atan2(move_dir.y, move_dir.x)
                        Scene.SendMoveReq(dir, protocol.pos_x, protocol.pos_y, 0, 0)
                        obj:SetRotation(angle)
                        Scene.Instance:TrySetCameraBehind()
                        FightWGCtrl.Instance:TryUseRoleSkill(cfg.skill_id, target_obj, t_x, t_y, ATTACK_SKILL_TYPE.BACKSTAB_BACK)
                        GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
                    else
                        if obj:IsRidingNoFightMount() then
                            MountWGCtrl.Instance:SendMountGoonReq(0)
                        end
                        FightWGCtrl.Instance:TryUseRoleSkill(cfg.skill_id, nil, protocol.pos_x, protocol.pos_y, ATTACK_SKILL_TYPE.BACKSTAB_BACK)
                    end
                else
                    if obj:IsRidingNoFightMount() then
                        MountWGCtrl.Instance:SendMountGoonReq(0)
                    end
                    FightWGCtrl.Instance:TryUseRoleSkill(cfg.skill_id, nil, protocol.pos_x, protocol.pos_y, ATTACK_SKILL_TYPE.BACKSTAB_BACK)
                end
            end
        end
    elseif SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_BLACK_BACKSTAB == reset_pos_type then
        obj:ResetBackstapState()
        obj:ChangeToCommonState()
        obj:ChangeMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
        obj:SetLogicPos(protocol.pos_x, protocol.pos_y)
        if obj:IsMainRole() then
            local after_cfg = SkillWGData.Instance:GetAfterSkillCfgById(protocol.param2)
            if protocol.param1 ~= nil and protocol.param1 >= 0 then
                local target_obj = self:GetObj(protocol.param1)
                if target_obj ~= nil and not target_obj:IsDeleted() then
                    local t_pos = target_obj:GetLuaPosition()
                    local pos = obj:GetLuaPosition()
                    local m_dir = u3dpool.v3Normalize({x = t_pos.x - pos.x, y = pos.y, z = t_pos.z - pos.z})
                    local angle = Quaternion.LookRotation(Vector3(m_dir.x, m_dir.y, m_dir.z))
                    -- local b = Quaternion.ToEulerAngles(angle)
                    local delta_pos = u3dpool.v2Sub({x = pos.x, y = pos.y}, {x = t_pos.x, y = t_pos.y})
                    local move_dir = u3dpool.v2Normalize(delta_pos)
                    local dir = math.atan2(move_dir.y, move_dir.x)
                    Scene.SendMoveReq(dir, protocol.pos_x, protocol.pos_y, 0, 0)
                    obj:SetRotation(angle)
                    Scene.Instance:TrySetCameraBehind()
                    if obj:IsRidingNoFightMount() then
                        MountWGCtrl.Instance:SendMountGoonReq(0)
                    end

                    if after_cfg ~= nil and after_cfg.param2 ~= nil and after_cfg.param2 ~= 0 and after_cfg.param2 ~= "" then
                        FightWGCtrl.Instance:TryUseRoleSkill(after_cfg.param2, target_obj, protocol.pos_x, protocol.pos_y, ATTACK_SKILL_TYPE.BACKSTAB_BACK)
                    end
                    GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
                else
                    if obj:IsRidingNoFightMount() then
                        MountWGCtrl.Instance:SendMountGoonReq(0)
                    end

                    if after_cfg ~= nil and after_cfg.param2 ~= nil and after_cfg.param2 ~= 0 and after_cfg.param2 ~= "" then
                        FightWGCtrl.Instance:TryUseRoleSkill(after_cfg.param2, nil, protocol.pos_x, protocol.pos_y, ATTACK_SKILL_TYPE.BACKSTAB_BACK)
                    end
                end
            else
                if obj:IsRidingNoFightMount() then
                    MountWGCtrl.Instance:SendMountGoonReq(0)
                end

                if after_cfg ~= nil and after_cfg.param2 ~= nil and after_cfg.param2 ~= 0 and after_cfg.param2 ~= "" then
                    FightWGCtrl.Instance:TryUseRoleSkill(after_cfg.param2, nil, protocol.pos_x, protocol.pos_y, ATTACK_SKILL_TYPE.BACKSTAB_BACK)
                end
            end
        end
    elseif SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_LA == reset_pos_type then
        local after_cfg = SkillWGData.Instance:GetAfterSkillCfgById(protocol.skill_id)
        if after_cfg ~= nil and after_cfg.skill_type == BLACK_SKILL_TYPE.PULL then
            -- if protocol.param1 ~= nil and protocol.param1 >= 0 then
            --     local deliverer = self:GetObj(protocol.param1)
            --     if deliverer ~= nil and not deliverer:IsDeleted() then
            --         local draw_obj = deliverer:GetDrawObj()
            --         if draw_obj ~= nil then
            --             local bundle = "effects2/prefab/mingjiang/10032_prefab"
            --             local asset = "10032_Mingjiang_attack1_dilie"
            --             deliverer:PlaySpecialEffect(bundle, asset, draw_obj:GetTransfrom().position)
            --         end
            --     end
            -- end

            -- local draw_obj = obj:GetDrawObj()
            -- if draw_obj ~= nil then
            --     local bundle = "effects2/prefab/mingjiang/10032_prefab"
            --     local asset = "10032_Mingjiang_attack1_kunbang"
            --     obj:PlaySpecialEffect(bundle, asset, nil, AttachPoint.BuffMiddle, nil, true, 1)
            -- end

            obj:OnSkillResetPos(protocol.skill_id, SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_CLASH, protocol.pos_x, protocol.pos_y)
        else
            obj:OnSkillResetPos(protocol.skill_id, reset_pos_type, protocol.pos_x, protocol.pos_y)
        end
    else
        obj:OnSkillResetPos(protocol.skill_id, reset_pos_type, protocol.pos_x, protocol.pos_y)
    end
end

function Scene:OnStartGather(protocol)
    local obj = Scene.Instance:GetObjectByObjId(protocol.role_obj_id)
    local role_obj = self:GetObj(protocol.gather_role_obj_id)

    if obj ~= nil then
        local gather = self:GetObj(protocol.gather_obj_id)
        if nil == gather or not gather:IsGather() then return end
        local caiji_type = 0
        local gather_config = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[gather:GetGatherId()]
        if gather_config and gather_config.action then
            caiji_type = gather_config.action
        end

        if gather_config.bundle_2 ~= "" then
            self:PlayGatherEffect(obj or role_obj, gather_config.bundle_2, gather_config.asset_2, gather_config.effect_scale, gather_config.audio_gather_2)
        end

        obj:SetIsGatherState(true, caiji_type)
        gather:OnStartGather(obj:IsMainRole())
        if obj:IsMainRole() then
            -- 捉鬼副本特殊采集
            local scene_type = self:GetSceneType()
            if scene_type == SceneType.GHOST_FB_GLOBAL then

            else
                GatherBar.Instance:Open()
                if gather and gather:IsGather() then
                    GatherBar.Instance:SetGatherId(gather:GetGatherId(),gather:GetGatherLeftTimes())
                end
            end

            GlobalEventSystem:Fire(ObjectEventType.START_GATHER, ROLE_GATHER_TYPE.GATHER, protocol.gather_obj_id)

            if gather_config and gather_config.audio_gather ~= nil and gather_config.audio_gather ~= "" then
                local bundle, asset = ResPath.GetNpcTalkVoiceResByResName(gather_config.audio_gather)
                TalkCache.PlayGuideTalkAudio(bundle, asset)
            end
        end

        local obj_root = obj:GetRoot()
        if obj_root then
             local obj_pos = obj_root.transform.position
             local towards
            if gather_config and gather_config.toward == 1 then
                towards = gather:GetRoot().transform.position.back * 10
                towards = Vector3(obj_pos.x + towards.x, obj_pos.y, obj_pos.z + towards.z)
            else
                towards = gather:GetRoot().transform.position
                towards = Vector3(towards.x, obj_pos.y, towards.z)
            end
             obj_root.transform:DOLookAt(towards, 0.5)
        end
    end
end

function Scene:OnStopGather(protocol)
    local obj = Scene.Instance:GetObjectByObjId(protocol.role_obj_id)
    if obj ~= nil then
        obj:SetIsGatherState(false)
        if obj:IsMainRole() then
            -- 捉鬼副本特殊采集
            local scene_type = self:GetSceneType()
            if scene_type == SceneType.GHOST_FB_GLOBAL then

            else
                GatherBar.Instance:Close()
            end
            
            local stop_reason = protocol.gather_obj_id >= 65535 and STOP_GATHER_REASON.ALREADY_GATHER or STOP_GATHER_REASON.NONE
            local gather_obj = Scene.Instance:GetObjectByObjId(protocol.gather_obj_id)
            local gather_id = gather_obj and gather_obj:GetGatherId() or 0
            GlobalEventSystem:Fire(ObjectEventType.STOP_GATHER, stop_reason, gather_id)
        end

        local gather = self:GetObj(protocol.gather_obj_id)
        if gather and gather:IsGather() then
            gather:OnStopGather(obj:IsMainRole())
        end
    end
end

function Scene:OnGatherBeGather(protocol)
    local obj = self:GetObj(protocol.gather_obj_id)
    local role_obj = self:GetObj(protocol.gather_role_obj_id)
    local gather_config = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[protocol.gather_id]
    if obj and obj:GetType() == SceneObjType.GatherObj and gather_config then
        if gather_config.bundle ~= "" then
            self:PlayGatherEffect(obj or role_obj, gather_config.bundle, gather_config.asset, gather_config.effect_scale, gather_config.audio_gather)
        elseif gather_config.effect == 1 and role_obj and role_obj:IsMainRole() then
            obj:CancelSelect()
            local draw_obj = obj:GetDrawObj()
            if draw_obj then
                local part = draw_obj:GetPart(SceneObjPart.Main)
                if part then
                    part:SetTrigger("Fly")
                end
            end
        end

        if role_obj then
            obj:GatherEnd(role_obj:IsMainRole())
        end

        if role_obj and role_obj:IsMainRole() and 1 == gather_config.disappear then
            local obj_key = obj:GetObjKey()
            if gather_config.disappear_delay_time and gather_config.disappear_delay_time ~= "" then
                GlobalTimerQuest:AddDelayTimer(function()
                    self:DeleteObjByTypeAndKey(SceneObjType.GatherObj, obj_key)
                    self:RemoveInVaildGatherByWaitList(obj_key)
                end, gather_config.disappear_delay_time)
            else
                self:DeleteObjByTypeAndKey(SceneObjType.GatherObj, obj_key)
                self:RemoveInVaildGatherByWaitList(obj_key)
            end
        end
    end
    
    if role_obj and role_obj:IsMainRole() then
        GlobalEventSystem:Fire(ObjectEventType.COMPLETE_GATHER, protocol.gather_obj_id)
    end
    TaskWGCtrl.Instance:CheckTaskStoryTextShow(nil,nil,protocol.gather_id)
end

-- 采集物OBJ，特效路径，特效名字
function Scene:PlayGatherEffect(gather_obj, bundle, asset, effect_scale, voice)
    if effect_scale == "" then
        effect_scale = nil
    end
    if self.effect_cd and self.effect_cd - Status.NowTime <= 0 then
        local gather_transform = gather_obj:GetRoot().transform
        if gather_transform then
            EffectManager.Instance:PlayControlEffect(self,
                asset,
                bundle,
                gather_transform.position,
                nil,
                nil,
                effect_scale
            )
        end
        self.effect_cd = Status.NowTime + 1
    end
    if voice and voice ~= "" then
        AudioManager.PlayAndForget("audios/sfxs/other", voice)
    end
end

function Scene:OnStartGatherTimer(protocol)
    GatherBar.Instance:SetGatherTime(protocol.gather_time / 1000)
end

function Scene:SendStopGather()
    -- body
    local protocol = ProtocolPool.Instance:GetProtocol(CSStopGather)
    protocol:EncodeAndSend()

    Scene.Instance:GetMainRole()
end

function Scene:OnAllObjMoveInfo(protocol)
    self:DeleteAllMoveObj()
    self.map_role_info_list = {}
    for k, v in pairs(protocol.obj_move_info_list) do
        local vo = GameVoManager.Instance:CreateVo(MapMoveVo)
        vo.obj_id = v.obj_id
        vo.obj_type = v.obj_type
        vo.type_special_id = v.type_special_id
        -- vo.dir = v.dir
        -- vo.distance = v.distance
        vo.pos_x = v.pos_x
        vo.pos_y = v.pos_y
        -- vo.move_speed = v.move_speed
        -- vo.monster_key = v.monster_key
        -- vo.name = v.name
        -- vo.level = v.level
        -- vo.avatar_key_big = v.avatar_key_big
        -- vo.avatar_key_small = v.avatar_key_small
        vo.role_id = v.role_id
        -- vo.prof = v.prof
        -- vo.sex = v.sex
        local map_move_obj = MapMoveObj.CreateMapMoveObj(vo)
        self.obj_move_info_list[v.obj_id] = map_move_obj
        self.map_role_info_list[v.role_id] = vo
    end

    ViewManager.Instance:FlushView(GuideModuleName.GuildPass, nil, GuildPassView.STATE.SEND)
    ViewManager.Instance:FlushView(GuideModuleName.GuildPass, nil, GuildPassView.STATE.ACCEPT)
    GlobalEventSystem:Fire(OtherEventType.SendAllObjMoveInfoEvent)
end

function Scene:OnObjMoveMode(protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if nil ~= scene_obj and scene_obj:IsRole() then
        -- local pet_obj = scene_obj:GetPetObj()
        -- local shenjiang_obj = scene_obj:GetShenJiangObj()
        -- local jingling_obj = scene_obj:GetJingLingObj()

        -- local is_change_noew = true
        if protocol.move_mode ~= MOVE_MODE.MOVE_MODE_SIT and scene_obj.vo.move_mode == MOVE_MODE.MOVE_MODE_SIT then
            if scene_obj:IsMainRole() then
                MainuiWGCtrl.Instance:FlushSitState()
            end

            if scene_obj:IsRole() then
                scene_obj:TryLeaveSit()
            end
        end

        -- 退出渡劫模式
        if protocol.move_mode ~= MOVE_MODE.MOVE_MODE_DUJIE and scene_obj.vo.move_mode == MOVE_MODE.MOVE_MODE_DUJIE then
            if scene_obj:IsRole() and not scene_obj:IsMainRole() then
                scene_obj:LeaveDujie()
            end
        end

        if scene_obj.vo.move_mode == MOVE_MODE.MOVE_MODE_NORMAL and protocol.move_mode == MOVE_MODE.MOVE_MODE_FLY then
            -- scene_obj:StartFlyingUp()
            -- if nil ~= pet_obj then
            --     pet_obj:StartFlyingUp()
            -- end
            -- if nil ~= shenjiang_obj then
            --     shenjiang_obj:StartFlyingUp()
            -- end
            -- if nil ~= jingling_obj then
            --     jingling_obj:StartFlyingUp()
            -- end
        elseif scene_obj.vo.move_mode == MOVE_MODE.MOVE_MODE_FLY and protocol.move_mode == MOVE_MODE.MOVE_MODE_NORMAL then
            -- scene_obj:StartFlyingDown()
            -- if nil ~= pet_obj then
            --     pet_obj:StartFlyingDown()
            -- end
            -- if nil ~= shenjiang_obj then
            --     shenjiang_obj:StartFlyingDown()
            -- end
            -- if nil ~= jingling_obj then
            --     jingling_obj:StartFlyingDown()
            -- end
            -- is_change_noew = false
        elseif scene_obj.vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP1 and protocol.move_mode == MOVE_MODE.MOVE_MODE_JUMP2 then
            if not scene_obj:IsMainRole() and scene_obj:IsJumping() then
                scene_obj:SetJumpingStep(2)
                scene_obj:StartJumpUp(protocol.move_mode_param)
            end
        end

        if protocol.move_mode == MOVE_MODE.MOVE_MODE_JUMP1 then
            if scene_obj:IsRole() and not scene_obj:IsMainRole() then
                scene_obj:Jump(protocol.move_mode_param)
            end
        elseif scene_obj.vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP1 and protocol.move_mode == MOVE_MODE.MOVE_MODE_NORMAL then
            if scene_obj:IsRole() and not scene_obj:IsMainRole() and not scene_obj:IsQingGong() then
                if scene_obj.move_target then
                    if u3dpool.v2Length(u3dpool.v2Sub(scene_obj.move_target, scene_obj.logic_pos), false) > 1 then
                        scene_obj:DoMove(scene_obj.move_target.x, scene_obj.move_target.y)
                    end
                    scene_obj.move_target = nil
                end
            end
        elseif protocol.move_mode == MOVE_MODE.MOVE_MODE_SWORD then
            if scene_obj:IsRole() and (not scene_obj:IsMainRole()) then
                scene_obj:Jump()
            end
        elseif protocol.move_mode == MOVE_MODE.MOVE_MODE_SIT then
            if scene_obj:IsRole() then
                scene_obj:EnterSit()
            end

            if scene_obj:IsMainRole() then
                MainuiWGCtrl.Instance:FlushSitState()
            end
        elseif protocol.move_mode == MOVE_MODE.MOVE_MODE_DUJIE then
            -- 进度渡劫模式
            if scene_obj:IsRole() and not scene_obj:IsMainRole() then
                scene_obj:EnterDujie()
            end
        end

        scene_obj:ChangeMoveMode(protocol.move_mode)
        scene_obj.vo.move_mode_param = protocol.move_mode_param
    end
end

-- 御剑下落
function Scene:OnSCMoveSwordDown(protocol)
    -- print_error("御剑下落", protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if nil ~= scene_obj and scene_obj:IsRole() then
        scene_obj:MoveSwordDown()
    end
end

-- 御剑冲刺
function Scene:OnSCMoveSwordSprint(protocol)
    -- print_error("御剑冲刺", protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if nil ~= scene_obj and scene_obj:IsRole() then
        scene_obj:MoveSwordSprint(protocol)
    end
end

function Scene:OnSceneMonsterDie(protocol)
    --print_error("OnSceneMonsterDie",protocol)
    local special_param = nil
    local obj = self:GetObj(protocol.obj_id)
    if obj and obj.vo and obj:IsMonster() then
        special_param = obj.vo.special_param
        if Scene.Instance:GetSceneLogic():CanMonsterDoJump(special_param) then
            self:SetMonsterJumpFlag(protocol.obj_id, nil)
        end
    end

    self.obj_move_info_list[protocol.obj_id] = nil
end

function Scene:OnRoleSpecialAppearanceChange(protocol)
    -- print_error("角色形象改变", protocol)
    local scene_obj = self:GetObj(protocol.obj_id)
    if nil ~= scene_obj and scene_obj:IsRole() then
        local is_need_stand = true
        if scene_obj:IsMainRole() then
            local vo = RoleWGData.Instance:GetRoleVo()
            if vo ~= nil and vo.special_appearance ~= nil and vo.special_appearance == 0 and protocol.special_appearance == 0 then
                is_need_stand = false
            end
        end

        if is_need_stand then
            scene_obj:ChangeToCommonState()
        end
    
        scene_obj:SetAttr("appearance_param", protocol.appearance_param)
        scene_obj:SetAttr("appearance_param_extend", protocol.appearance_param_extend)
        scene_obj:SetAttr("special_appearance", protocol.special_appearance)
        
        -- if scene_obj:IsMainRole() then
        --     MainuiWGCtrl.Instance:GetView():UpdateSkillVisible()
        --     ViewManager.Instance:Close(GuideModuleName.MainUiMultiSkillSelectView)
        -- end
    end
end

function Scene:OnGatherChange(protocol)
    local scene_obj = self:GetObjectByObjId(protocol.obj_id)
    if nil ~= scene_obj then
        scene_obj:GetVo().param = protocol.param
        scene_obj:GetVo().param1 = protocol.param1
        scene_obj:GetVo().param2 = protocol.param2
        scene_obj:GetVo().special_gather_type = protocol.special_gather_type

        if GuildWGData.Instance:IsGatherBonfire(protocol.gather_id) then
            scene_obj:GetVo().param = protocol.param
            scene_obj:UpdateNameBoard()
            scene_obj:RefreshAnimation()
        end
        -- if MeridianData.Instance:IsGatherJinghua(protocol.gather_id) then
        --     scene_obj:GetVo().param = protocol.param
        --     scene_obj:GetVo().special_gather_type = protocol.special_gather_type
        --     scene_obj:UpdateNameBoard()
        -- end

        if SPECIAL_GATHER_TYPE.BUILDING == protocol.special_gather_type then
            scene_obj:UpdateNameBoard()
        end
    end
end

function Scene:OnRoleAccetpTaskAppearn(protocol)
   -- print_error("OnRoleAccetpTaskAppearn:::", protocol)
    local scene_obj = self:GetObj(protocol.obj_id)
    if scene_obj and scene_obj:IsRole() then
        local callback = function()
            scene_obj:SetAttr("task_appearn_param_1", protocol.task_appearn_param_1)
            scene_obj:SetAttr("task_appearn", protocol.task_appearn)
        end
        -- if scene_obj:IsMainRole() and protocol.task_appearn == CHANGE_MODE_TASK_TYPE.TALK_IMAGE and protocol.task_appearn_param_1 > 0 then
        --     TipWGCtrl.Instance:OpenBarTipsView(2, 1, function()
        --         callback()
        --     end)
        -- else
            callback()
        -- end

        if scene_obj:IsMainRole() then
            if protocol.task_appearn == CHANGE_MODE_TASK_TYPE.TALK_IMAGE or protocol.task_appearn == CHANGE_MODE_TASK_TYPE.MOUNT_HIGH then
                MainuiWGCtrl.Instance:SetVisibleNormalSkillPanel(0 == protocol.task_appearn_param_1)
            end

            if protocol.task_appearn == CHANGE_MODE_TASK_TYPE.TALK_IMAGE and protocol.task_appearn_param_1 ~= 0 then
                MountWGCtrl.Instance:SendMountGoonReq(0)
            end
        end
    end
end

function Scene.SendTransportReq(transport_index)
    local protocol = ProtocolPool.Instance:GetProtocol(CSTransportReq)
    protocol.transport_index = transport_index
    protocol:EncodeAndSend()
end

function Scene.SendMoveReq(dir, x, y, distance, height)
    local protocol = ProtocolPool.Instance:GetProtocol(CSObjMove)
    protocol.dir = dir
    protocol.pos_x = math.floor(x)
    protocol.pos_y = math.floor(y)
    protocol.distance = distance
    protocol.height = height or 0
    protocol:EncodeAndSend()
end

function Scene.SendStartGatherReq(gather_obj_id, gather_count)
    local protocol = ProtocolPool.Instance:GetProtocol(CSStartGatherReq)
    protocol.gather_obj_id = gather_obj_id
    protocol.gather_count = gather_count
    protocol:EncodeAndSend()
end

function Scene.ScenePickItem(item_objid_list)
    if nil == item_objid_list or #item_objid_list <= 0 then
        return
    end
    PickCache.last_time = Status.NowTime + PickCache.interval
    local protocol = ProtocolPool.Instance:GetProtocol(CSPickItem)
    protocol.item_objid_list = item_objid_list
    protocol:EncodeAndSend()
end

-- 一次性拾取超过多个弹出恭喜获得
function Scene.ChcekShowScenePickItemList(item_id_list)
    if #item_id_list > 1 then
        TipWGCtrl.Instance:ShowGetCommonBossReward(item_id_list, nil, nil, false)
    end
end

function Scene.SendGetAllObjMoveInfoReq()
    local protocol = ProtocolPool.Instance:GetProtocol(CSGetAllObjMoveInfoReq)
    protocol:EncodeAndSend()
end

function Scene.SendWorldEventObjTouch(obj_id)
    local protocol = ProtocolPool.Instance:GetProtocol(CSWorldEventObjTouch)
    protocol.obj_id = obj_id
    protocol:EncodeAndSend()
end

function Scene.SendReqTeamMemberPos(reason)
    local protocol = ProtocolPool.Instance:GetProtocol(CSReqTeamMemberPos)
    protocol.reason_from_client = reason
    protocol:EncodeAndSend()
end

function Scene:OnTeamMemberPosList(protocol)
    if protocol.reason_from_client == EnumReqTeamMemberPosReason.KF3V3Prepare then
        KF3V3WGCtrl.Instance:HandleReqMemberMsg(protocol)
    elseif protocol.reason_from_client == EnumReqTeamMemberPosReason.OtherSceneEnterKF3V3Prepare then
        KF3V3WGCtrl.Instance:HandleOtherSceneEnterKF3V3Prepare(protocol)
        KF3V3WGCtrl.Instance:EnterPrepareScene()
    elseif protocol.reason_from_client == EnumReqTeamMemberPosReason.FollowTeam then
        SocietyWGCtrl.Instance:SendFollowLeader(protocol)
    end
end

function Scene.SendMultiuserChallengeReqSideMemberPos()
    local protocol = ProtocolPool.Instance:GetProtocol(CSMultiuserChallengeReqSideMemberPos)
    protocol:EncodeAndSend()
end

function Scene:OnMultiuserChallengeTeamMemberPosList(protocol)
    local list = {}
    for k, v in pairs(protocol.team_member_list) do
        if v.role_id ~= RoleWGData.Instance.role_vo.role_id then
            list[#list + 1] = v
        end
    end
    MapWGCtrl.Instance:OnFlushTeamMemberPos(list)
end

function Scene:OnServerDebugMsg(protocol)
    print(protocol.content)
end

--夫妻光环--------
function Scene:OnQingyuanCoupleHaloTrigger(protocol)
    if nil ~= protocol.role1_uid and nil ~= protocol.role2_uid and nil ~= protocol.halo_type then
        local scene_obj_1, scene_obj_2
        if protocol.role1_uid == self.main_role.vo.role_id then
            scene_obj_1 = self:GetMainRole()
            scene_obj_2 = self:GetObjectByRoleId(protocol.role2_uid)
        elseif protocol.role2_uid == self.main_role.vo.role_id then
            scene_obj_1 = self:GetMainRole()
            scene_obj_2 = self:GetObjectByRoleId(protocol.role1_uid)
        else
            scene_obj_1 = self:GetObjectByRoleId(protocol.role1_uid)
            scene_obj_2 = self:GetObjectByRoleId(protocol.role2_uid)
        end

        local couple_halo_t
        local key = self:GetTwoObjUniqueKey(protocol.role1_uid, protocol.role2_uid)
        if scene_obj_1 and scene_obj_2 and protocol.halo_type ~= 0 then
            couple_halo_t = self.couple_halo_map[key]
            if nil ~= couple_halo_t and nil ~= couple_halo_t.couple_halo_type then
                couple_halo_t.couple_halo_type:removeFromParent()
                couple_halo_t.couple_halo_type = nil
                couple_halo_t.obj1 = nil
                couple_halo_t.obj2 = nil
                self.couple_halo_map[key] = nil
            end
            self:AddCoupleEffect(key, scene_obj_1, scene_obj_2, protocol.halo_type)
        elseif 0 == protocol.halo_type then
            if 0 == protocol.role1_uid or 0 == protocol.role2_uid then
                couple_halo_t = self:GetCoupleEffectByDivorcedCouple(protocol.role1_uid, protocol.role2_uid)
            else
                couple_halo_t = self.couple_halo_map[key]
            end

            if nil ~= couple_halo_t and nil ~= couple_halo_t.couple_halo_type then
                couple_halo_t.couple_halo_type:removeFromParent()
                couple_halo_t.couple_halo_type = nil
                couple_halo_t.obj1 = nil
                couple_halo_t.obj2 = nil
                self.couple_halo_map[key] = nil
            end
        end
    end
end

function Scene.SendMoveMode(move_mode, move_mode_param)
    local protocol = ProtocolPool.Instance:GetProtocol(CSSetMoveMode)
    protocol.move_mode = move_mode
    protocol.move_mode_param = move_mode_param or 0
    protocol:EncodeAndSend()
end

-- 同步跳跃
function Scene.SendSyncJump(scene_id, pos_x, pos_y, scene_key)
    local protocol = ProtocolPool.Instance:GetProtocol(CSSyncJump)
    protocol.scene_id = scene_id
    protocol.scene_key = scene_key
    protocol.pos_x = pos_x
    protocol.pos_y = pos_y
    protocol.item_id = 0
    protocol:EncodeAndSend()
end

function Scene:SendRoleLandingReq()
    local protocol = ProtocolPool.Instance:GetProtocol(CSRolePersonAreaMsgInfo)
    protocol:EncodeAndSend()
end

function Scene:OnRolePersonAreaMsgInfo(protocol)
    local scene_obj = self:GetObj(protocol.obj_id)
    if nil ~= scene_obj and scene_obj:IsRole() and not scene_obj:IsMainRole() and (scene_obj:IsQingGong() or scene_obj:IsMitsurugi()) then
        scene_obj:Landing()
    end
end

function Scene.SendGetRoleMoveInfo(uuid, follow_type, param)
    if uuid == nil or follow_type == nil then
        return
    end

    local protocol = ProtocolPool.Instance:GetProtocol(CSGetOneObjMoveInfoReq)
    protocol.uuid = uuid
    protocol.follow_type = follow_type
    protocol.client_param = param or CLIENT_MOVE_REQ_PARAM.NORMAL
    protocol:EncodeAndSend()
end

function Scene:OnSCOneObjMoveInfo(protocol)
    local scene_logic = self:GetSceneLogic()
    if scene_logic == nil then
        return
    end
    -- protocol.obj_moveinfo.client_param 如果是2, 说明这是刺探跟随，在挂机中缓存的跟随目标不在视野内发起的位置查询
    -- 但是攻击的时候，很多情况下会缓存攻击的操作，所以会导致在发出查询的请求，并且设置了TrackRoleInfo的值后
    -- 在查询结果还没返回的情况下，执行了缓存的攻击，缓存的攻击触发了移动，导致TrackRoleInfo的值被清了，所以这里只针对普通的跟随做校验
    local tarck_type, track_role_uuid = scene_logic:GetTrackRoleInfo()
    if (tarck_type == nil or track_role_uuid == nil) and protocol.obj_moveinfo.client_param == CLIENT_MOVE_REQ_PARAM.NORMAL then
        scene_logic:ResetTrackRoleInfo()
        return
    end

    local main_role = self:GetMainRole()
    -- 这个情况就是这个玩家不再这个场景里，需要清除缓存的数据
    if COMMON_CONSTS.SERVER_INVAILD_OBJ_ID == protocol.obj_moveinfo.obj_id and protocol.obj_moveinfo.client_req_uuid == track_role_uuid then
        if tarck_type == OBJ_FOLLOW_TYPE.TEAM and main_role ~= nil then
            --SysMsgWGCtrl.Instance:ErrorRemind(Language.FollowState.LeaderLeave)
            main_role:SetIsFollowState(false)
            MainuiWGCtrl.Instance:FlushXunLuStates()
            Scene.SendReqTeamMemberPos(EnumReqTeamMemberPosReason.FollowTeam)
        end
        scene_logic:ResetTrackRoleInfo()
        GuajiWGCtrl.Instance:GetRecoveryFollowCache()
        GuajiWGCtrl.Instance:ResetRealiveFollowCache()
        return
    end

    if protocol.obj_moveinfo.client_param == CLIENT_MOVE_REQ_PARAM.NORMAL and not (track_role_uuid == protocol.obj_moveinfo.uuid) then
        --SysMsgWGCtrl.Instance:ErrorRemind(Language.FollowState.LeaderLeave)
        scene_logic:ResetTrackRoleInfo()
        if main_role ~= nil and not main_role:IsDeleted() and tarck_type == OBJ_FOLLOW_TYPE.TEAM then
            main_role:ResetServerFollowState()
        end
        return
    end

    if (GuajiCache.guaji_type ~= GuajiType.None and protocol.obj_moveinfo.client_param == CLIENT_MOVE_REQ_PARAM.NORMAL)
        or CgManager.Instance:IsCgIng()
        or FunctionGuide.Instance:GetIsGuide() then
        scene_logic:ResetTrackRoleInfo()
        if main_role ~= nil and not main_role:IsDeleted() and tarck_type == OBJ_FOLLOW_TYPE.TEAM then
            main_role:ResetServerFollowState()
        end
        return
    end

    if ViewManager.Instance:IsOpen(GuideModuleName.TaskDialog) then
        scene_logic:ResetTrackRoleInfo()
        if main_role ~= nil and not main_role:IsDeleted() and tarck_type == OBJ_FOLLOW_TYPE.TEAM then
            main_role:ResetServerFollowState()
        end
        return
    end

    if main_role ~= nil and not main_role:IsDeleted() then
        if main_role:IsDead() or main_role:GetIsGatherState() or main_role:IsFollowState() then
            scene_logic:ResetTrackRoleInfo()
            main_role:ResetServerFollowState()
            --GuajiWGCtrl.Instance:ResetRealiveFollowCache()
            return
        else
            local function check()
                if main_role ~= nil and not main_role:IsDeleted() and scene_logic ~= nil then
                    local real_tarck_type, real_track_role_uuid = scene_logic:GetTrackRoleInfo()
                    if (real_tarck_type == nil or real_track_role_uuid == nil) and protocol.obj_moveinfo.client_param == CLIENT_MOVE_REQ_PARAM.NORMAL then
                        scene_logic:ResetTrackRoleInfo()
                        return
                    end

                    if real_track_role_uuid == nil and protocol.obj_moveinfo.client_param ~= CLIENT_MOVE_REQ_PARAM.NORMAL then
                        real_track_role_uuid = protocol.obj_moveinfo.uuid
                    end

                    if not main_role:CheckFollowStateIsVaild(protocol.obj_moveinfo.follow_type) then
                        scene_logic:ResetTrackRoleInfo()
                        GuajiWGCtrl.Instance:ResetRealiveFollowCache()
                        return
                    end

                    main_role:OperaRoleFollow(self:GetSceneId(), real_track_role_uuid, protocol.obj_moveinfo.follow_type, protocol.obj_moveinfo.client_param)
                end
            end

            local m_x, m_y = main_role:GetLogicPos()
            if GameMath.GetDistance(m_x, m_y, protocol.obj_moveinfo.pos_x, protocol.obj_moveinfo.pos_y, false) <= 4 then
                check()
            else
                GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
                GuajiWGCtrl.Instance:SetMoveToPosCallBack(check)
                GuajiWGCtrl.Instance:MoveToPos(self:GetSceneId(), protocol.obj_moveinfo.pos_x, protocol.obj_moveinfo.pos_y, 1, nil, nil, nil, nil, nil, CLIENT_MOVE_REASON.FOLLOW)
            end
        end
    end
end

function Scene:OnSCRoleKillBossNotice(protocol)
    GuajiWGCtrl.Instance:SetBossDeadTime()
end

-- 客户端通知加载完场景
function Scene:SendFBLoadedScene()
    local protocol = ProtocolPool.Instance:GetProtocol(CSFBLoadedScene)
    protocol:EncodeAndSend()
end

-- 请求场景内某个对象的位置信息
function Scene:SendGetOneMovePos(obj_type, param)
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    if main_role_vo == nil then
        return
    end

    local server_id = main_role_vo.server_id
    local scene_id = Scene.Instance:GetSceneId()
    local scene_key = RoleWGData.Instance.role_vo.scene_key or 0

    local protocol = ProtocolPool.Instance:GetProtocol(CSGetOneRoleMovePosReq)
    protocol.obj_type = obj_type
    protocol.param = param
    protocol.server_id = server_id
    protocol.scene_id = scene_id
    protocol.scene_key = scene_key
    protocol:EncodeAndSend()
end

function Scene:OnSCOneRoleMovePos(protocol)
    FightRevengeWGCtrl.Instance:OnRevengeMoveInfo(protocol)
end

function Scene:OnSCSceneEffectRemove(protocol)
    local obj = self:GetObj(protocol.obj_id)
    if obj ~= nil and not obj:IsDeleted() then
        if protocol.effect_type == SCENE_EFFECT_TYPE.SCENE_EFFECT_TYPE_SHIELD then
            local pos = obj:GetLuaPosition()
            EffectManager.Instance:PlayControlEffect(self, "effects2/prefab/mingjiang/10027_prefab", "10027_Mingjiang_attack2_bao", pos)
            AudioManager.PlayAndForget("audios/sfxs/roleskill/tianshen_chuangshinvwa", "MingJiangSkill_15_2_2")
        end
    end
end

-- 请求场景内某个对象的位置信息
function Scene:SendGetOneMovePosNew(obj_type, plat_type, server_id, param)
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    if main_role_vo == nil then
        return
    end

    local scene_id = Scene.Instance:GetSceneId()
    local scene_key = RoleWGData.Instance.role_vo.scene_key or 0

    local protocol = ProtocolPool.Instance:GetProtocol(CSGetOneRoleMovePosReqNew)
    protocol.obj_type = obj_type
    protocol.param = param
    protocol.plat_type = plat_type
    protocol.server_id = server_id
    protocol.scene_id = scene_id
    protocol.scene_key = scene_key
    protocol:EncodeAndSend()
end

function Scene:OnSCOneRoleMovePosNew(protocol)
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.XianJie_Boss then
        BossWGCtrl.Instance:OnReceiveRolePosInfo(protocol)
    end
end


-- 请求修正技能方向
function Scene:SendSetSkillAttackPos(pos_x, pos_y)
    local protocol = ProtocolPool.Instance:GetProtocol(CSSetSkillAttackPos)
    protocol.pos_x = pos_x
    protocol.pos_y = pos_y
    protocol:EncodeAndSend()
end

-- 目标技能方向改变
function Scene:OnSCSkillAttackPos(protocol)
    local obj = self:GetObj(protocol.obj_id)
    if obj == nil or obj:IsDeleted() or not obj:IsRole() or obj:IsMainRole() then
        return
    end

    obj:SetDirectionByXY(protocol.attack_x, protocol.attack_y, nil, true)
end

-- 火焰柱同步位置
function Scene:OnSCSceneEffectCrossFireTurn(protocol)
    local obj = self:GetObj(protocol.obj_id)
    if obj == nil or obj:IsDeleted() or not obj:IsEffect() then
        return
    end

    local angle = COMMON_CONSTS.OBJ_ROTOATE_SPEED
    if protocol.cur_angle ~= 0 and protocol.interval ~= 0 then
        angle = protocol.cur_angle / (protocol.interval * 0.001)
    end

    -- obj:SetDirectionByXY(protocol.pos_x, protocol.pos_y, angle)
    obj:SetAutoRotateInfo(protocol.pos_x, protocol.pos_y, angle, protocol.interval * 0.001)
end
