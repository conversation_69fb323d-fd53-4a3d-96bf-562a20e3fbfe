QuanMinBeiZhanRewardTips = QuanMinBeiZhanRewardTips or BaseClass(SafeBaseView)

function QuanMinBeiZhanRewardTips:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self.view_name = "TianShenRoadUi"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 2), sizeDelta = Vector2(730,464)})
	self:AddViewResource(0, "uis/view/quanmin_beizhan_ui_prefab", "layout_lhbx_reward")
end

function QuanMinBeiZhanRewardTips:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function QuanMinBeiZhanRewardTips:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.XiuZhenRoad.ViewName
	self.reward_list_view = AsyncListView.New(QuanMinBeiZhanReWardItemRender, self.node_list["ph_item_list"])
	self:FlushView()
end

function QuanMinBeiZhanRewardTips:OnFlush(param_t, index)
	self:FlushView()
end

function QuanMinBeiZhanRewardTips:FlushView()
	local cfg = QuanMinBeiZhanWGData.Instance:GetLHRewardCfg()
	--数据处理
	local data_list = {}
	for k,v in pairs(cfg) do
		local item = {}
		item.rewards = {}
		item.ID = v.ID
		item.score = v.jifen
		item.state = QuanMinBeiZhanWGData.Instance:GetBXState(v.ID)
		for k2,v2 in pairs(v.reward_item) do
			table.insert(item.rewards,v2)
		end

		if item.state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.state == ActivityRewardState.BKL then
			item.sort = 1
		else
			item.sort = 2
		end
		table.insert(data_list, item)
	end
	table.sort(data_list, SortTools.KeyLowerSorter("sort","ID"))

	self.reward_list_view:SetDataList(data_list)
	self.node_list["star_num"].text.text = QuanMinBeiZhanWGData.Instance:GetDangweiScore() .. "奖励点数"
end

-----------------------------------------------------------------------------------------------

QuanMinBeiZhanReWardItemRender = QuanMinBeiZhanReWardItemRender or BaseClass(BaseRender)

function QuanMinBeiZhanReWardItemRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function QuanMinBeiZhanReWardItemRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
end

function QuanMinBeiZhanReWardItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return 
	end

	self.reward_list:SetDataList(SortTableKey(data.rewards))

	self.node_list["lbl_lscs"].text.text = self.data.score .. "奖励点数"
	self.node_list["btn_lingqu"]:SetActive(self.data.state == ActivityRewardState.KLQ)
	self.node_list["image_ylq"]:SetActive(self.data.state == ActivityRewardState.YLQ)
	self.node_list["btn_weilingqu"]:SetActive(self.data.state == ActivityRewardState.BKL)
end

function QuanMinBeiZhanReWardItemRender:OnClickLingQu()
	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_WOYAOLONGHUN,WOYAOSHENQI_OP_TYPE.DANGWEI_REWARD,self.data.ID)
end
