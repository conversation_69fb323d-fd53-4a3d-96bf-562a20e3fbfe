OpenServerInvestWGData = OpenServerInvestWGData or BaseClass()
function OpenServerInvestWGData:__init()
	if OpenServerInvestWGData.Instance then
		error("[OpenServerInvestWGData] Attempt to create singleton twice!")
		return
	end
	OpenServerInvestWGData.Instance = self

    self:InitConfig()

    self.rmb_buy_flag = {}
    self.reward_flag = {}
    self.rmb_reward_flag = {}

	RemindManager.Instance:Register(RemindName.OpenServerInvestView, BindTool.Bind(self.GetInvestRemind, self))
end

function OpenServerInvestWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto")
    self.invest_type_cfg = ListToMap(cfg.invest_type, "type")
    self.invest_task_cfg = ListToMap(cfg.invest_task, "type", "seq")
end

function OpenServerInvestWGData:__delete()
    OpenServerInvestWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.OpenServerInvestView)
end

function OpenServerInvestWGData:SetAllInfo(protocol)
	self.rmb_buy_flag = bit:d2b_l2h(protocol.rmb_buy_flag, nil, true)
	self.reward_flag = protocol.reward_flag
	self.rmb_reward_flag = protocol.rmb_reward_flag
	self.value_list = protocol.value_list
end

-- 活动剩余时间戳
function OpenServerInvestWGData:GetInvestTimeByType(type)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg_data = self:GetInvestTypeCfgByType(type)
	if not cfg_data or open_day < cfg_data.open_day or open_day > cfg_data.close_day then
		return 0
	end

	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local format_time = os.date("*t", now_time)
	local end_time = os.time({
		year = format_time.year,
		month = format_time.month,
		day = format_time.day + cfg_data.close_day - open_day + 1,
		hour = 0,
		min = 0,
		sec = 0
	})
	return end_time - now_time
end

-- 判断活动是否开启
function OpenServerInvestWGData:AllActIsOpen()
	for k, v in pairs(self.invest_type_cfg) do
		if self:ActIsOpenByType(k) then
			return true
		end
	end

	return false
end

function OpenServerInvestWGData:GetToggleShowList()
	local toggle_data_list = {}
	for k, v in pairs(self.invest_type_cfg) do
		if self:ActIsOpenByType(k) then
			table.insert(toggle_data_list, v)
		end
	end

	return toggle_data_list
end

-- 根据类型判断活动是否开启
function OpenServerInvestWGData:ActIsOpenByType(type)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg_data = self:GetInvestTypeCfgByType(type)
	local role_level = RoleWGData.Instance:GetRoleLevel()

	local is_all_get = self:IsAllGetInvest(type)
	if not is_all_get then
		return true
	end

	if not cfg_data or role_level < (cfg_data.level_limit or 0) then
		return false
	end

	return open_day >= cfg_data.open_day and open_day <= cfg_data.close_day
end

function OpenServerInvestWGData:GetInvestRewardListByType(type)
	local reward_list = {}
	for k, v in pairs(self.invest_task_cfg[type]) do
		local data = {}
		local value1, is_special_show = self:GetInvestTaskConditionParamByType(type)
		local nor_can_get, high_can_get = self:IsCanGetInvestRewardByTypeAndSeq(type, v.seq)
		local nor_is_get, higer_is_get = self:IsGetInvestRewardByTypeAndSeq(type, v.seq)
		data.item_list = v.item_list
		data.rmb_item_list = v.rmb_item_list
		data.describe_type = v.describe_type
		data.param1 = v.param1
		--data.param2 = v.param2
		data.value1 = value1
		data.is_special_show = is_special_show
		data.nor_can_get = nor_can_get
		data.high_can_get = high_can_get
		data.nor_is_get = nor_is_get
		data.higer_is_get = higer_is_get
		table.insert(reward_list, data)
	end

	return reward_list
end

function OpenServerInvestWGData:GetInvestTaskCfgByType(type)
	return self.invest_task_cfg[type]
end

function OpenServerInvestWGData:GetInvestTypeCfg()
	return self.invest_type_cfg
end

function OpenServerInvestWGData:GetInvestTypeCfgByType(type)
	return self.invest_type_cfg[type]
end

function OpenServerInvestWGData:GetInvestTaskCfgByTypeAndSeq(type, seq)
	return (self.invest_task_cfg[type] or {})[seq]
end

-- 通过类型和索引获得奖励数据
function OpenServerInvestWGData:GetInvestRewardDataByTypeAndSeq(type, seq)
	local task_cfg = self:GetInvestTaskCfgByTypeAndSeq(type, seq)
	local data = {}
	local value1, is_special_show = self:GetInvestTaskConditionParamByType(type)
	local nor_can_get, high_can_get = self:IsCanGetInvestRewardByTypeAndSeq(type, seq)
	local nor_is_get, higer_is_get = self:IsGetInvestRewardByTypeAndSeq(type, seq)
	data.item_list = task_cfg.item_list
	data.rmb_item_list = task_cfg.rmb_item_list
	data.describe_type = task_cfg.describe_type
	data.param1 = task_cfg.param1
	--data.param2 = task_cfg.param2
	data.value1 = value1
	data.is_special_show = is_special_show
	data.nor_can_get = nor_can_get
	data.high_can_get = high_can_get
	data.nor_is_get = nor_is_get
	data.higer_is_get = higer_is_get
	return data
end

function OpenServerInvestWGData:IsMeetMaxLevel(type)
	local max_cfg = self:GetMaxLevelCfg(type)
	local value1 = self:GetInvestTaskConditionParamByType(type)
	return value1 >= max_cfg.param1
end

function OpenServerInvestWGData:GetMaxLevelCfg(type)
	local task_cfg = self.invest_task_cfg[type]
	local max_cfg = task_cfg[#task_cfg]
	return max_cfg or {}
end

-- 是否解锁投资奖励
function OpenServerInvestWGData:GetInvestBuyFlagByType(type)
	return self.rmb_buy_flag[type] == 1
end

function OpenServerInvestWGData:GetJumpRedTab()
	local index = 0
    local type_cfg = self:GetInvestTypeCfg()

	if not IsEmptyTable(type_cfg) then
		for k, v in pairs(type_cfg) do
			if self:ActIsOpenByType(v.type) then
				index = index + 1

				if self:GetActRemindByType(v.type) then
					return index
				end
			end
		end
	end

	return 1
end

function OpenServerInvestWGData:GetInvestRemind()
    local type_cfg = self:GetInvestTypeCfg()

	if not IsEmptyTable(type_cfg) then
		for k, v in pairs(type_cfg) do
			local is_act_remind = self:GetActRemindByType(v.type)
			if is_act_remind then
				OpenServerInvestWGCtrl.Instance:CreatMainUiInvestBtn(MAINUI_TIP_TYPE.OPEN_SERVER_INVEST, 1)
				return 1
			end
		end
	end

	OpenServerInvestWGCtrl.Instance:CreatMainUiInvestBtn(MAINUI_TIP_TYPE.OPEN_SERVER_INVEST, 0)
	return 0
end

function OpenServerInvestWGData:GetActRemindByType(type)
    local task_cfg = self:GetInvestTaskCfgByType(type)

	if not IsEmptyTable(task_cfg) and self:ActIsOpenByType(type) then
		for k, v in pairs(task_cfg) do
			local nor_can_get, high_can_get = self:IsCanGetInvestRewardByTypeAndSeq(type, v.seq)

			if nor_can_get or high_can_get then
				return true
			end
		end
	end

	return false
end

-- 购买过的是否获取了全部奖励
function OpenServerInvestWGData:IsAllGetInvest(type)
	local task_cfg = self:GetInvestTaskCfgByType(type)
	local is_bug_invest = self:GetInvestBuyFlagByType(type)
	if not IsEmptyTable(task_cfg) and is_bug_invest then
		for k, v in pairs(task_cfg) do
			local is_get_nor, is_get_highr = self:IsGetInvestRewardByTypeAndSeq(type, v.seq)
			if not is_get_highr then
				return false
			end
		end
	end

	return true
end

-- 普通奖励 高级奖励能否领取
function OpenServerInvestWGData:IsCanGetInvestRewardByTypeAndSeq(type, seq)
	local can_get_nor, can_get_highr = false, false
	-- 没领取
	local is_get_nor, is_get_highr = self:IsGetInvestRewardByTypeAndSeq(type, seq)

	local cur_task_cfg = self:GetInvestTaskCfgByTypeAndSeq(type, seq)
	local is_bug_invest = self:GetInvestBuyFlagByType(type)
	if IsEmptyTable(cur_task_cfg) then
		return can_get_nor, can_get_highr
	end

	if not is_get_nor or not is_get_highr then
		local is_meet_conditon = false
		local value1 = self:GetInvestTaskConditionParamByType(type)
		is_meet_conditon = self:IsMeetCondition(value1, cur_task_cfg.param1)

		can_get_nor = not is_get_nor and is_meet_conditon
		can_get_highr = not is_get_highr and is_meet_conditon and is_bug_invest
	end

	return can_get_nor, can_get_highr
end

-- 判断是否达到目标条件
function OpenServerInvestWGData:IsMeetCondition(value1, param1)
	return value1 >= param1
end

-- 获取任务的条件参数值
function OpenServerInvestWGData:GetInvestTaskConditionParamByType(type)
	local value1
	local is_special_show = false
	--[[ 改为使用后端数据
	if type == 0 then
		value1 = ControlBeastsWGData.Instance:GetBattleBeastsAllLevel()
	elseif type == 1 then
		value1 = TianShenWGData.Instance:GetTianShenAllLevel()
	elseif type == 2 then
		local info = NewAppearanceWGData.Instance:GetQiChongInfo(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
		if info then
			--local cur_upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, info.star_level)
			--value1 = cur_upstar_cfg.grade_num
			--value2 = info.star_level % 10 == 0 and 10 or info.star_level % 10
			value1 = info.star_level
			is_special_show = true
		end
	elseif type == 3 then
		local info = NewAppearanceWGData.Instance:GetAdvancedInfo(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING)
		if info then
			value1 = info.level
		end
	end
	]]
	if type == 2 then
		is_special_show = true
	end
	value1 = (self.value_list or {})[type] or 0
	return value1, is_special_show
end

-- 普通奖励 高级奖励 领取标记
function OpenServerInvestWGData:IsGetInvestRewardByTypeAndSeq(type, seq)
	return self.reward_flag[type] and self.reward_flag[type] >= seq or false, self.rmb_reward_flag[type] and self.rmb_reward_flag[type] >= seq or false
end

function OpenServerInvestWGData:GetJumpRewardIndex(type)
	return self.reward_flag[type] or 1
end

function OpenServerInvestWGData:GetRewardFlagByType(type)
	return self.reward_flag[type], self.rmb_reward_flag[type]
end