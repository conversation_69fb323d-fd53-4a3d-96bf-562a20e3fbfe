require("game/recharge_return_reward/recharge_return_reward_view")
require("game/recharge_return_reward/recharge_return_reward_wg_data")

RechargeReturnRewardWGCtrl = RechargeReturnRewardWGCtrl or BaseClass(BaseWGCtrl)

function RechargeReturnRewardWGCtrl:__init()
	if RechargeReturnRewardWGCtrl.Instance ~= nil then
		print("[RechargeReturnRewardWGCtrl]error:create a singleton twice")
	end
	RechargeReturnRewardWGCtrl.Instance = self

	self.view = RechargeReturnRewardView.New(GuideModuleName.RechargeReturnRewardView)
	self.data = RechargeReturnRewardWGData.New()

	self:RegisterAllProtocols()
end

function RechargeReturnRewardWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	RechargeReturnRewardWGCtrl.Instance = nil
end

function RechargeReturnRewardWGCtrl:Open(index, param_t)
	-- local mijingtaobao_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_CRAZY_REBATE)
	-- if not mijingtaobao_isopen then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
	-- 	return
	-- end
	self.view:Open(index)
end

function RechargeReturnRewardWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCrazyRebateChongInfo , "OnSCRaCrazyRebateChongInfo")
end

function RechargeReturnRewardWGCtrl:OnSCRaCrazyRebateChongInfo(protocol)
	self.data:SetRechargeNum(protocol.chongzhi_count)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end