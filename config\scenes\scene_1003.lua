return {
    id = 1003,
    name = "烟雨城",
    scene_type = 0,
    bundle_name = "scenes/map/a3_zx_zhucheng_main",
    asset_name = "A3_ZX_ZhuCheng_Main",
    width = 600,
    height = 500,
    origin_x = -260,
    origin_y = -280,
    levellimit = 40,
    is_forbid_pk = 0,
    skip_loading = 0,
    show_weather = 0,
    scene_broadcast = 0,
    scenex = 54,
    sceney = 284,
    npcs = {
		{id=10302, x=307, y=308, rotation_y = 230, is_walking = 0, paths = {}},
		{id=10303, x=73, y=284, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10304, x=159, y=264, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10305, x=216, y=200, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10308, x=195, y=172, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10309, x=309, y=178, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10310, x=184, y=71, rotation_y = 300, is_walking = 0, paths = {}},
		{id=10311, x=350, y=74, rotation_y = 240, is_walking = 0, paths = {}},
		{id=10312, x=402, y=69, rotation_y = 310, is_walking = 0, paths = {}},
		{id=10313, x=405, y=93, rotation_y = 233.7, is_walking = 0, paths = {}},
		{id=10314, x=278, y=324, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10315, x=293, y=141, rotation_y = 50, is_walking = 0, paths = {}},
		{id=10316, x=284, y=269, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10317, x=307, y=141, rotation_y = 310, is_walking = 0, paths = {}},
		{id=10318, x=238, y=394, rotation_y = 40, is_walking = 0, paths = {}},
		{id=10319, x=227, y=396, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10320, x=292, y=449, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10321, x=243, y=278, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10322, x=321, y=276, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10323, x=420, y=284, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10324, x=487, y=284, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10325, x=219, y=347, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10326, x=338, y=428, rotation_y = 200, is_walking = 0, paths = {}},
		{id=10327, x=291, y=414, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10328, x=132, y=80, rotation_y = 240, is_walking = 0, paths = {}},
		{id=10329, x=88, y=105, rotation_y = 130, is_walking = 0, paths = {}},
		{id=10330, x=126, y=130, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10331, x=294, y=196, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10332, x=271, y=179, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10333, x=175, y=178, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10334, x=179, y=184, rotation_y = 120, is_walking = 0, paths = {}},
		{id=10335, x=219, y=211, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10336, x=401, y=97, rotation_y = 210, is_walking = 0, paths = {}},
		{id=10337, x=305, y=140, rotation_y = 340, is_walking = 0, paths = {}},
		{id=10338, x=201, y=419, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10339, x=235, y=416, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10340, x=266, y=360, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10341, x=262, y=255, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10342, x=565, y=287, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10343, x=565, y=282, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10345, x=192, y=278, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10346, x=187, y=399, rotation_y = 79.99999, is_walking = 0, paths = {}},
		{id=10347, x=342, y=404, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10348, x=283, y=300, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10350, x=291, y=418, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10351, x=81, y=421, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10352, x=84, y=408, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10353, x=80, y=409, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10354, x=83, y=419, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10355, x=82, y=405, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10371, x=169, y=203, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10372, x=82, y=57, rotation_y = 90, is_walking = 0, paths = {}},
		{id=10373, x=181, y=199, rotation_y = 160, is_walking = 0, paths = {}},
		{id=10376, x=320, y=192, rotation_y = 212, is_walking = 0, paths = {}},
		{id=10377, x=272, y=198, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10378, x=178, y=300, rotation_y = 163.11, is_walking = 0, paths = {}},
		{id=10379, x=318, y=298, rotation_y = 221.4, is_walking = 0, paths = {}},
		{id=10380, x=264, y=421, rotation_y = 90, is_walking = 0, paths = {}},
		{id=316, x=217, y=218, rotation_y = 270, is_walking = 0, paths = {}},
		{id=312, x=292, y=453, rotation_y = 180, is_walking = 0, paths = {}},
    },
    monsters = {
    },
    doors = {
		{id=1005, type=0, level=0, target_scene_id=1002, target_door_id=1003, offset={0, 0, 0}, rotation={0, 0, 0}, x=32, y=284, door_target_x=102, door_target_y=69},
		{id=1211, type=0, level=0, target_scene_id=1005, target_door_id=1008, offset={0, 0, 0}, rotation={0, 0, 0}, x=169, y=397, door_target_x=16, door_target_y=47},
		{id=1032, type=0, level=0, target_scene_id=1004, target_door_id=1004, offset={0, 0, 0}, rotation={0, 0, 0}, x=577, y=284, door_target_x=92, door_target_y=272},
		{id=5101, type=0, level=0, target_scene_id=5100, target_door_id=5100, offset={0, 0, 0}, rotation={0, 0, 0}, x=277, y=490, door_target_x=74, door_target_y=157},
		{id=10003, type=0, level=0, target_scene_id=1000, target_door_id=10001, offset={0, 0, 0}, rotation={0, 0, 0}, x=285, y=490, door_target_x=113, door_target_y=199},
    },
    gathers = {
		{id=1200, x=153, y=100, disappear_after_gather=0},
		{id=1208, x=266, y=284, disappear_after_gather=0},
		{id=1203, x=488, y=236, disappear_after_gather=0},
		{id=1204, x=50, y=271, disappear_after_gather=0},
		{id=2001, x=255, y=307, disappear_after_gather=0},
		{id=2002, x=272, y=317, disappear_after_gather=0},
		{id=2003, x=249, y=296, disappear_after_gather=0},
		{id=2004, x=252, y=301, disappear_after_gather=0},
		{id=2005, x=288, y=359, disappear_after_gather=0},
		{id=2006, x=264, y=363, disappear_after_gather=0},
		{id=2067, x=114, y=101, disappear_after_gather=0},
		{id=2068, x=124, y=128, disappear_after_gather=0},
		{id=2069, x=426, y=116, disappear_after_gather=0},
		{id=2070, x=228, y=367, disappear_after_gather=0},
		{id=2078, x=209, y=416, disappear_after_gather=0},
		{id=2072, x=235, y=415, disappear_after_gather=0},
		{id=356, x=264, y=444, disappear_after_gather=0},
		{id=357, x=266, y=446, disappear_after_gather=0},
		{id=1800, x=92, y=55, disappear_after_gather=0},
		{id=313, x=179, y=133, disappear_after_gather=0},
		{id=314, x=242, y=181, disappear_after_gather=0},
		{id=315, x=264, y=455, disappear_after_gather=0},
		{id=316, x=194, y=365, disappear_after_gather=0},
    },
    jumppoints = {
    },
    fences = {
    },
    effects = {
    },
    sounds = {
    },
    scene_way_points = {
		[0] = {id=0, target_id={1, },x=45, y=284},
		[1] = {id=1, target_id={0, 2, },x=170, y=285},
		[2] = {id=2, target_id={1, 3, },x=171, y=222},
		[3] = {id=3, target_id={2, 4, },x=207, y=221},
		[4] = {id=4, target_id={3, 5, },x=190, y=161},
		[5] = {id=5, target_id={4, 6, },x=201, y=95},
		[6] = {id=6, target_id={5, 7, },x=203, y=37},
		[7] = {id=7, target_id={6, 8, },x=152, y=18},
		[8] = {id=8, target_id={7, 9, },x=182, y=71},
		[9] = {id=9, target_id={8, 10, },x=160, y=15},
		[10] = {id=10, target_id={9, 11, },x=203, y=46},
		[11] = {id=11, target_id={10, 12, },x=209, y=78},
		[12] = {id=12, target_id={11, 13, },x=187, y=133},
		[13] = {id=13, target_id={12, 14, },x=188, y=188},
		[14] = {id=14, target_id={13, 15, },x=298, y=188},
		[15] = {id=15, target_id={14, 16, },x=299, y=134},
		[16] = {id=16, target_id={15, 17, },x=310, y=107},
		[17] = {id=17, target_id={16, 18, },x=330, y=91},
		[18] = {id=18, target_id={17, 19, },x=348, y=86},
		[19] = {id=19, target_id={18, 20, },x=349, y=71},
		[20] = {id=20, target_id={19, 21, },x=394, y=81},
		[21] = {id=21, target_id={20, 22, },x=348, y=72},
		[22] = {id=22, target_id={21, 23, },x=339, y=90},
		[23] = {id=23, target_id={22, 24, },x=314, y=103},
		[24] = {id=24, target_id={23, 25, },x=303, y=119},
		[25] = {id=25, target_id={24, 26, },x=299, y=134},
		[26] = {id=26, target_id={25, 27, },x=298, y=187},
		[27] = {id=27, target_id={26, 28, },x=281, y=196},
		[28] = {id=28, target_id={27, 29, },x=283, y=262},
		[29] = {id=29, target_id={28, 30, },x=305, y=309},
		[30] = {id=30, target_id={29, 31, },x=282, y=324},
		[31] = {id=31, target_id={30, 32, },x=281, y=406},
		[32] = {id=32, target_id={31, 33, },x=194, y=405},
		[33] = {id=33, target_id={32, 34, },x=193, y=382},
		[34] = {id=34, target_id={33, 35, },x=169, y=381},
		[35] = {id=35, target_id={34, 36, },x=169, y=287},
		[36] = {id=36, target_id={35, 37, },x=260, y=284},
		[37] = {id=37, target_id={36, 38, },x=416, y=284},
		[38] = {id=38, target_id={37, 39, },x=423, y=268},
		[39] = {id=39, target_id={38, 40, },x=425, y=261},
		[40] = {id=40, target_id={39, 41, },x=432, y=254},
		[41] = {id=41, target_id={40, 42, },x=440, y=253},
		[42] = {id=42, target_id={41, 43, },x=493, y=280},
		[43] = {id=43, target_id={42, },x=571, y=284},
	},
    mask = "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",
}