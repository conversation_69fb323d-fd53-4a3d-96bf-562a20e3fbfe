NewFightMountWGData = NewFightMountWGData or BaseClass()

function NewFightMountWGData:__init()
	if NewFightMountWGData.Instance ~= nil then
		ErrorLog("[NewFightMountWGData] attempt to create singleton twice!")
		return
	end

	NewFightMountWGData.Instance = self

    self.skill_level_list = {}
    self.appearance_level_list = {}
    self.mainui_skill_order_list = {}
    self.mount_ultimate_skill_list = {}

    self.use_mount_id = -1
    self.use_dragon_id = -1
    self.use_skill_id = -1

    self.use_skill_end_time = 0
	self.next_use_skill_time = 0
    self.use_skill_start_time = 0

    self:InitCfg()
    self:CacheMountUltimateSkillList()
    RemindManager.Instance:Register(RemindName.NewFightMount, BindTool.Bind(self.ShowNewFightMountRemind, self))
end

function NewFightMountWGData:__delete()
	NewFightMountWGData.Instance = nil

    RemindManager.Instance:UnRegister(RemindName.NewFightMount)
end

function NewFightMountWGData:InitCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("fightmount_cfg_auto")
    self.upgrade_cfg =  ListToMap(cfg.upgrade, "mount_seq", "level")
    self.upgrade_cost_cfg = ListToMap(cfg.upgrade, "cost_item_id")
    self.mount_type_cfg = ListToMap(cfg.mount_type, "mount_seq")
    self.appe_image_cfg = ListToMap(cfg.mount_type, "appe_image_id")
    self.uplevel_cfg = ListToMap(cfg.uplevel, "seq", "level")
    self.uplevel_cost_item_cfg = ListToMap(cfg.uplevel, "cost_item_id")
    self.other_cfg = cfg.other[1]
    self.mount_skill_cfg = cfg.skill
end


--------------------协议数据Start-----------------------
function NewFightMountWGData:SetFightMountAllInfo(protocol)
    self.skill_level_list = protocol.skill_level_list
    self.appearance_level_list = protocol.appearance_level_list
    self.use_mount_id = protocol.use_mount_id
    self.use_dragon_id = protocol.use_dragon_id
    self.use_skill_id =  protocol.use_skill_id
end

function NewFightMountWGData:SetFightMountUseUpdateInfo(protocol)
    self.use_mount_id = protocol.use_mount_id
    self.use_dragon_id = protocol.use_dragon_id
    self.use_skill_id =  protocol.use_skill_id
end

function NewFightMountWGData:SetFightMountUpdateInfo(protocol)
    local data = protocol.change_data
    if  self.skill_level_list[data.mount_id] then
        self.skill_level_list[data.mount_id] = data.skill_level
    end

    if  self.appearance_level_list[data.mount_id] then
        self.appearance_level_list[data.mount_id] = data.appearance_level
    end
end

function NewFightMountWGData:SetFightMountTimeInfo(protocol)
    self.use_skill_end_time = protocol.use_skill_end_time
	self.next_use_skill_time = protocol.next_use_skill_time
    self.use_skill_start_time = protocol.use_skill_start_time
end

--------------------协议数据End---------------------
---战斗坐骑形象突破等级
function NewFightMountWGData:GetAppearanceLevelBySeq(seq)
    return self.appearance_level_list[seq] or 0
end

---战斗坐骑技能等级
function NewFightMountWGData:GetMountSkillLevelBySeq(seq)
    return self.skill_level_list[seq] or 0
end

function NewFightMountWGData:GetUseMountId()
    return self.use_mount_id
end

function NewFightMountWGData:GetUseSkillId()
    return self.use_skill_id
end

function NewFightMountWGData:GetUseSkillTimeInfo()
    return self.use_skill_end_time, self.next_use_skill_time, self.use_skill_start_time
end

function NewFightMountWGData:GetOtherCfg()
    return self.other_cfg
end

--战斗坐骑形象突破配置
function NewFightMountWGData:GetUpgradeLevelCfg(seq, level)
	return (self.upgrade_cfg[seq] or {})[level]
end

--是否是形象等级消耗道具
function NewFightMountWGData:GetIsUpgradeCostCfg(stuff_id)
    return self.upgrade_cost_cfg[stuff_id] ~= nil
end

--等级消耗配置
function NewFightMountWGData:GetUpgradeCostCfg(stuff_id)
    return self.upgrade_cost_cfg[stuff_id]
end

function NewFightMountWGData:IsActiveByItemId(item_id)
    local upgrade_cfg = self:GetUpgradeCostCfg(item_id)
    
    if IsEmptyTable(upgrade_cfg) then
        return false
    end

    local mount_seq = upgrade_cfg.mount_seq or -1
    local level = self:GetAppearanceLevelBySeq(mount_seq)
    return level > 0
end

--是否是战斗坐骑的形象
function NewFightMountWGData:IsFightMountAppImage(appe_image_id)
   return self.appe_image_cfg[appe_image_id] ~= nil
end

--获取类型配置By形象id
function NewFightMountWGData:GetFightMountAppCfgById(appe_image_id)
    return self.appe_image_cfg[appe_image_id]
end

--获取类型 By形象id
function NewFightMountWGData:GetFightMountTypeCfgByAppeId(appe_image_id)
    local cfg = self.appe_image_cfg[appe_image_id]
    return cfg and cfg.mount_seq or -1
end

--获取单个索引形象配置
function NewFightMountWGData:GetMountTypeCfgBySeq(seq)
    return self.mount_type_cfg[seq]
end

--获取全部形象配置
function NewFightMountWGData:GetAllFightMountTypeCfg()
    local show_list = {}
    if not IsEmptyTable(self.mount_type_cfg) then
		for k, v in pairs(self.mount_type_cfg) do
			if v.mount_show == 1 then
				table.insert(show_list, v)
			end
		end
	end

    if not IsEmptyTable(show_list) then
        table.sort(show_list, SortTools.KeyLowerSorters("mount_seq"))
    end

    return show_list
end

function NewFightMountWGData:GetUpSkillLevelCfg(seq, level)
	return (self.uplevel_cfg[seq] or {})[level]
end

--是否是技能消耗物品道具
function NewFightMountWGData:GetIsSkillLevelCostCfg(stuff_id)
	return self.uplevel_cost_item_cfg[stuff_id] ~= nil
end

--通过对应等级的道具数据
function NewFightMountWGData:GetSkillLevelCostCfg(item_id, level)
    local cfgs = self:GetIsSkillLevelCostCfg(item_id)
    if not cfgs then
        return
    end

	for k_1, v_1 in pairs(self.uplevel_cfg) do
        for k_2, v_2 in pairs(v_1) do
            if v_2.cost_item_id == item_id and v_2.level == level then
                return v_2
            end
        end
    end
end

--获取坐骑技能配置by seq
function NewFightMountWGData:GetFightMountSkillCfgByseq(seq)
	return self.mount_skill_cfg[seq]
end

--是否为大招技能（返回值 第一个bool 是否为大招,第二个为大招对应的坐骑索引(-1 表示找不到技能对应索引)）
function NewFightMountWGData:IsFightMountUltimateSkillId(skill_id)
    local mount_seq = self.mount_ultimate_skill_list[skill_id] or -1
    local is_ultimate_skill = mount_seq ~= -1
    return is_ultimate_skill, mount_seq
end

--缓存大招技能列表
function NewFightMountWGData:CacheMountUltimateSkillList()
    for key, cfg in pairs(self.mount_skill_cfg) do
        local skill_list = Split(cfg.skill, "|")
        local ultimate_skill_id = tonumber(skill_list[4])
        if ultimate_skill_id then
            self.mount_ultimate_skill_list[ultimate_skill_id] = cfg.mount_seq
        end
    end
end

--技能升级属性展示
function NewFightMountWGData:GetSkillLevelAttr(seq, level)
    local cur_level_cfg = self:GetUpSkillLevelCfg(seq, level)
    local next_level_cfg = self:GetUpSkillLevelCfg(seq, level + 1)
    local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 5)
    --组装一个系统自己属性（加一个龙威之力)
    local lw_attr = {}
    local lw_attr_value = cur_level_cfg and cur_level_cfg.lw_value or 0
    local lw_next_attr_value = next_level_cfg and next_level_cfg.lw_value or 0
    lw_attr.attr_str = -100
    lw_attr.attr_value = lw_attr_value
    lw_attr.attr_next_value = lw_next_attr_value
    lw_attr.add_value = (lw_next_attr_value - lw_attr_value) > 0 and (lw_next_attr_value - lw_attr_value) or 0
    lw_attr.attr_sort = 1000
    table.insert(attr_list, lw_attr)

    --加一个时间假属性
    local time_attr = {}
    local time_attr_value = cur_level_cfg and cur_level_cfg.continue_time or 0
    local time_next_attr_value = next_level_cfg and next_level_cfg.continue_time or 0
    time_attr.attr_str = -200
    time_attr.attr_value = time_attr_value
    time_attr.attr_next_value = time_next_attr_value
    time_attr.add_value = (time_next_attr_value - time_attr_value) > 0 and (time_next_attr_value - time_attr_value) or 0
    time_attr.attr_sort = 2000
    table.insert(attr_list, time_attr)

    if not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

    return attr_list
end

--技能升级属性
function NewFightMountWGData:GetSkillLevelCap(seq)
	local attr_list = AttributePool.AllocAttribute()
	local capability = 0

    local mount_skill_level = self:GetMountSkillLevelBySeq(seq)
    local cur_level_cfg = self:GetUpSkillLevelCfg(seq, mount_skill_level)
    local cfg_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, nil, "attr_id", "attr_value", 1, 5)
	for k, v in pairs(cfg_attr_list) do
		attr_list[v.attr_str] = attr_list[v.attr_str] + v.attr_value
	end

	capability = AttributeMgr.GetCapability(attr_list)

	return capability
end





------------------------------红点--------------
function NewFightMountWGData:ShowNewFightMountRemind()
    if self:GetNewFightMountUpLevelRemind() then
        return 1
    end

    return 0
end

function NewFightMountWGData:GetNewFightMountUpLevelRemind()
    local all_mount_cfg = self:GetAllFightMountTypeCfg()
    if not IsEmptyTable(all_mount_cfg) then
        for i, v in pairs(all_mount_cfg) do
            if self:GetSinggleMountRemind(v.mount_seq) then
                return true
            end
        end
    end

    return false
end

function NewFightMountWGData:GetSinggleMountRemind(seq)
    if self:GetSinggleUpLevelRemind(seq) then
        return true
    end

    return false
end

function NewFightMountWGData:GetSinggleUpLevelRemind(seq)
    local mount_skill_level = self:GetMountSkillLevelBySeq(seq)
    local cur_mount_type_cfg = self:GetMountTypeCfgBySeq(seq)
    local cur_level_cfg = self:GetUpSkillLevelCfg(seq, mount_skill_level)
    local next_level_cfg = self:GetUpSkillLevelCfg(seq, mount_skill_level + 1)
    if cur_mount_type_cfg and cur_level_cfg and next_level_cfg then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
        local is_enough_num = item_num >= cur_level_cfg.cost_item_num

        local hatch_item_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(seq)
        local hatch_level = hatch_item_info and hatch_item_info.level or 0
        local is_enough_hatch_level = cur_mount_type_cfg.skill_need_level <= hatch_level

        local mount_level = self:GetAppearanceLevelBySeq(seq)
        local is_act_appimage = mount_level > 0
        if is_enough_num and is_enough_hatch_level and is_act_appimage then
            return true
        end
    end

    return false
end



-- 主界面技能 
function NewFightMountWGData:GetMainUISkillOrder(appe_image_id, seq)
    local fm_type = seq or self:GetFightMountTypeCfgByAppeId(appe_image_id)
    if not self.mainui_skill_order_list[fm_type] then
        self.mainui_skill_order_list[fm_type] = {}
        local cfg = self:GetFightMountSkillCfgByseq(fm_type)
        if cfg then
            local order_list = self.mainui_skill_order_list[fm_type]
            order_list[0] = cfg.normal_skill
            local skill_list = Split(cfg.skill, "|")
            for k,v in ipairs(skill_list) do
                table.insert(order_list, tonumber(v))
            end
        end
    end

    return self.mainui_skill_order_list[fm_type], {1,1,1,1,1,1,1,1,1,1}
end

function NewFightMountWGData:GetMainUISkillList(appe_image_id)
    local order_list = self:GetMainUISkillOrder(appe_image_id)
    local skill_list = {}

    for i = 0, #order_list do
        local order_data = order_list[i]
        local data = SkillWGData.Instance:GetFightMountSkillCfg(order_data, 1)
        table.insert(skill_list, data)
    end

    return skill_list
end