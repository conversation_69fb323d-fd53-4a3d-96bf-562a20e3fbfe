local AUTO_FEFINING_TIME = 1
local RAW_SHOW_TYPE = {
    FULL = 1,
    SMALL = 2,
}

function ControlBeastsWGView:LoadFefiningViewCallBack()
    self.opeate_cool_timer = nil
    self.is_feining_auto = false
    self.select_fefining_index = nil
    self.select_fefining_data = nil
    self.show_type = nil        

    -- 星阵孔位
    if self.fefining_cell_list == nil then
        self.fefining_cell_list = {}
        for i = 1, BEAST_DEFINE.BEAST_FIGHTING_COUNT_MAX do
            local attr_obj = self.node_list.refining_item_root:FindObj(string.format("refining_item_0%d", i))
            if attr_obj then
                local cell = BeststsRefiningItemRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetClickCallBack(BindTool.Bind1(self.OnClickBeststsRefining, self))
                self.fefining_cell_list[i] = cell
            end
        end
    end

    -- 星阵属性
    if self.star_circle_attr_list == nil then
        self.star_circle_attr_list = {}
        for i = 1, BEAST_DEFINE.BEAST_CHANGE_STAR_CIRCLE_ATTR_COUNT_MAX do
            local attr_obj = self.node_list.refining_attr_root:FindObj(string.format("refining_attr_item_%d", i))
            if attr_obj then
                local cell = BeststsRefiningAttrItemRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetOperateCallBack(BindTool.Bind1(self.CircleResetClickBack, self))
                self.star_circle_attr_list[i] = cell
            end
        end
    end

    if not self.refining_spend_item then
		self.refining_spend_item = ItemCell.New(self.node_list.refining_spend_item)
	end

    XUI.AddClickEventListener(self.node_list.auto_operate_btn, BindTool.Bind2(self.OperateAutoFeining, self))
    XUI.AddClickEventListener(self.node_list.level_preview_btn, BindTool.Bind2(self.OpenLevelPreviewView, self))
    XUI.AddClickEventListener(self.node_list.click_operate_btn, BindTool.Bind2(self.OperateOnceFeining, self, true))
    XUI.AddClickEventListener(self.node_list.click_open_new_btn, BindTool.Bind2(self.ClickOpenNewBtn, self, true))
    XUI.AddClickEventListener(self.node_list.change_show_full_type_btn, BindTool.Bind2(self.ClickChangeShowType, self, RAW_SHOW_TYPE.FULL))
    XUI.AddClickEventListener(self.node_list.change_show_small_type_btn, BindTool.Bind2(self.ClickChangeShowType, self, RAW_SHOW_TYPE.SMALL))
    self.refining_loaded = true
end

function ControlBeastsWGView:OpenFefiningViewCallBack()

end

function ControlBeastsWGView:CloseFefiningViewCallBack()
    self:CleanOperateCoolTime()
    self.is_feining_auto = false

    self.select_fefining_index = nil
    self.select_fefining_data = nil
    self:StopAutoFeiningOperate()

    -- 这里清空是清空一下特效缓存
    if self.star_circle_attr_list then
        for _, circle_attr_cell in ipairs(self.star_circle_attr_list) do
            circle_attr_cell:ResetOldStarNum()
        end
    end
end

function ControlBeastsWGView:ChangeRefiningTableIndex()
    if self.refining_loaded then
        self:StopAutoFeiningOperate()
    end
end

function ControlBeastsWGView:ShowFefiningViewCallBack()
    -- print_error("展示炼根界面")
end

function ControlBeastsWGView:ReleaseFefiningViewCallBack()
    self:CleanOperateCoolTime()
    self.is_feining_auto = false

    if self.fefining_cell_list and #self.fefining_cell_list > 0 then
		for _, fefining_cell in ipairs(self.fefining_cell_list) do
			fefining_cell:DeleteMe()
			fefining_cell = nil
		end

		self.fefining_cell_list = nil
	end

    if self.star_circle_attr_list and #self.star_circle_attr_list > 0 then
		for _, star_circle__cell in ipairs(self.star_circle_attr_list) do
			star_circle__cell:DeleteMe()
			star_circle__cell = nil
		end

		self.star_circle_attr_list = nil
	end

    if self.refining_spend_item then
		self.refining_spend_item:DeleteMe()
		self.refining_spend_item = nil
	end

    self.refining_loaded = false
end

-- 点击炼根item
function ControlBeastsWGView:OnClickBeststsRefining(fefining_cell, is_force)
    -- print_error("点击炼根", fefining_cell:GetIndex())
    if fefining_cell.data == nil then
		return
	end

    local temp_index = fefining_cell:GetIndex()
    if temp_index == self.select_fefining_index and (not is_force) then
        return
    end

    if not is_force then
        self:StopAutoFeiningOperate()
    end

    if fefining_cell.data.is_unlock ~= 1 then    --未解锁
        -- print_error("未解锁，不让点击")
        local str = ""
        if temp_index > 3 then
            str = string.format(Language.ContralBeasts.HoleTips2, temp_index - 3)
        else
            str = string.format(Language.ContralBeasts.HoleTips1, temp_index)
        end

        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ContralBeasts.AttrWhee21, str))
        return
    end

    if self.select_fefining_index ~= temp_index then
        if self.star_circle_attr_list then
            for _, circle_attr_cell in ipairs(self.star_circle_attr_list) do
                circle_attr_cell:ResetOldStarNum()
            end
        end
    end
    
    self.select_fefining_index = temp_index
    self.select_fefining_data = fefining_cell.data
    self.select_fefining_cell = fefining_cell
    if not self.fefining_cell_list then
        return
    end
    
    for index, fefining_list_cell in ipairs(self.fefining_cell_list) do
        if fefining_list_cell then
            fefining_list_cell:FlushSelectHl(self.select_fefining_index == index)
        end
    end

    self:FlushRefiningStarCircle()
    self:FlushAllPower()
    if self.show_type == RAW_SHOW_TYPE.FULL then
        self:JumpSelectRePos()
    end
end

-- 跳转到当前选中的位置
function ControlBeastsWGView:JumpSelectRePos()
    if not self.select_fefining_cell then
        return
    end

    local pos = self:GetCurrSelectPos(self.select_fefining_cell)
    self.node_list.refining_item_root.rect:DOAnchorPos(Vector2(-pos.x, -pos.y), 0.5)
end

-- 获取到当前选择的相对位置
function ControlBeastsWGView:GetCurrSelectPos(fefining_list_cell)
    local cell_view = fefining_list_cell:GetView()
    if cell_view then
        return cell_view.transform.localPosition
    end
    return nil
end

-- 属性点击重置
function ControlBeastsWGView:CircleResetClickBack(circle_cell)
    if circle_cell.data == nil then
		return
	end

    local attr_list = self.select_fefining_data.attr_list
    local interval_times = self.select_fefining_data.interval_times
    if interval_times == 0 then
        local is_can_reset = false

        for index, attr_data in ipairs(attr_list) do
            if attr_data.star_num ~= 0 then
                is_can_reset = true
                break
            end
        end

        if not is_can_reset then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip20)
            return
        end
    end

    local show_data = {
        refining_index = self.select_fefining_index,
        circle_index = circle_cell.index,
    }

    ControlBeastsWGCtrl.Instance:OpenBeastsRefiningResetView(show_data)
end


-- 取消自动炼根定时器
function ControlBeastsWGView:CleanOperateCoolTime()
    if self.opeate_cool_timer then
        GlobalTimerQuest:CancelQuest(self.opeate_cool_timer)
        self.opeate_cool_timer = nil
    end
end

---------------------------------------------------------------------
function ControlBeastsWGView:FlushFefiningViewCallBack(param_t)
	-- print_error("刷新炼根界面")
    self:SetFefiningList()
    self:FlushRefiningStarCircleInterval()
    self:FlushRawSelectStatus()

    -- 检测是否自动刷新
    self:CheckFefiningAuto()
end

-- 刷新列表
function ControlBeastsWGView:SetFefiningList()
    local fefining_list = ControlBeastsWGData.Instance:GetStarCircleData()
    -- local center_interval_times = 0
    -- local left_interval_times = 0
    -- local right_interval_times = 0


    if self.fefining_cell_list and fefining_list then
        for index, fefining_cell in ipairs(self.fefining_cell_list) do
            if fefining_cell and fefining_list[index] then
                fefining_cell:SetData(fefining_list[index])

                -- if index <= 3 then
                --     center_interval_times = center_interval_times + fefining_list[index].interval_times
                -- else
                --     local last_num = index % 2
                --     if last_num == 0 then
                --         left_interval_times = left_interval_times + fefining_list[index].interval_times
                --     else
                --         right_interval_times = right_interval_times + fefining_list[index].interval_times
                --     end
                -- end
            end
        end
    end

    -- local interval_str = NumberToChinaNumber(center_interval_times)
    -- self.node_list.refining_image_02_text.text.text = string.format(Language.ContralBeasts.AttrWheel2, interval_str)
    -- interval_str = NumberToChinaNumber(left_interval_times)
    -- self.node_list.refining_image_01_text.text.text = string.format(Language.ContralBeasts.AttrWheel2, interval_str)
    -- interval_str = NumberToChinaNumber(right_interval_times)
    -- self.node_list.refining_image_03_text.text.text = string.format(Language.ContralBeasts.AttrWheel2, interval_str)

    if not self.select_fefining_index then
        self.select_fefining_index = 1
    end

    if self.fefining_cell_list and self.fefining_cell_list[self.select_fefining_index] then
        self:OnClickBeststsRefining(self.fefining_cell_list[self.select_fefining_index], true)
    end
end

-- 刷新星轮
function ControlBeastsWGView:FlushRefiningStarCircle()
    if not self.select_fefining_data then
        return
    end

    local attr_list = self.select_fefining_data.attr_list
    if attr_list and self.star_circle_attr_list then
        for index, circle_attr_cell in ipairs(self.star_circle_attr_list) do
            circle_attr_cell:SetIntervalTimes(self.select_fefining_data.interval_times)
            circle_attr_cell:SetData(attr_list[index])
        end
    end

    local interval_str = NumberToChinaNumber(self.select_fefining_data.interval_times)
    interval_str = string.format(Language.ContralBeasts.AttrWheel2, interval_str)
    interval_str = ToColorStr(interval_str, COLOR3B.GREEN) 

    if self.select_fefining_index then
        local cur_hole_id = self.select_fefining_index - 1
        local cfg_data = ControlBeastsWGData.Instance:GetHoleCfgById(cur_hole_id)
        if cfg_data then
            interval_str = string.format("%s（%s）", cfg_data.hole_name, interval_str)
        end
    end

    self.node_list.refining_lun_text.text.text = interval_str

    local star_circle_level_cfg = ControlBeastsWGData.Instance:GetStarCircleLevelCfgById(self.select_fefining_data.interval_times)
    if star_circle_level_cfg and star_circle_level_cfg.item then
        self.refining_spend_item:SetData({item_id = star_circle_level_cfg.item.item_id})
        local item_num = ItemWGData.Instance:GetItemNumInBagById(star_circle_level_cfg.item.item_id)
        local color = item_num >= star_circle_level_cfg.item.num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.refining_spend_item:SetRightBottomTextVisible(true)
        self.refining_spend_item:SetRightBottomColorText(item_num .. '/' .. star_circle_level_cfg.item.num, color)
    end

    local is_full, _ = self:CheckFeiningFullStar()
    local is_interval_full = false

	if is_full then
		local star_circle_level_cfg_next = ControlBeastsWGData.Instance:GetStarCircleLevelCfgById(self.select_fefining_data.interval_times + 1)
		if not star_circle_level_cfg_next then
			is_interval_full = true
		end
	end

    self.node_list.click_open_new_btn:CustomSetActive(is_full and (not is_interval_full))
    self.node_list.interval_full:CustomSetActive(is_interval_full and is_full)
    self.node_list.not_full_star_root:CustomSetActive((not is_full) and (not is_interval_full))

    if is_full then
        self:StopAutoFeiningOperate()
    end
    
    self.node_list.auto_operate_remind:CustomSetActive(self.select_fefining_data.is_can_circle)
    self.node_list.click_operate_remind:CustomSetActive(self.select_fefining_data.is_can_circle)
end

-- 刷新当前的星轮的总战斗力
function ControlBeastsWGView:FlushAllPower()
    if not self.select_fefining_data then
        return
    end

    local power_attr_list = {}

    local attr_list = self.select_fefining_data.attr_list
    local interval_times = self.select_fefining_data.interval_times or 0
    for _, data in ipairs(attr_list) do
        local star_circle_attr_cfg = ControlBeastsWGData.Instance:GetStarCircleAttrCfgById(data.id)
        if star_circle_attr_cfg then
            local attr_data = {}
            attr_data.attr_str = star_circle_attr_cfg.array_attr_id
            attr_data.attr_value = star_circle_attr_cfg.array_attr_base_value * (data.star_num + (interval_times * 10))
            table.insert(power_attr_list, attr_data)
        end
    end

    -- 刷新战斗力
    local cap, attribute = ControlBeastsWGData.Instance:GetCapValueByAttrList(power_attr_list)
    self.node_list.refining_cap_value.text.text = cap
end

-- 刷新总轮回
function ControlBeastsWGView:FlushRefiningStarCircleInterval()
    local totle_interval_times = ControlBeastsWGData.Instance:GetStarCircleTotleIntervalTimes()
    local level_limit = ControlBeastsWGData.Instance:GetStarCircleBeastMaxLevel(totle_interval_times)
    --local interval_str = NumberToChinaNumber(totle_interval_times)
    local interval_str
    interval_str = string.format(Language.ContralBeasts.AttrWheel2, totle_interval_times)
    interval_str = ToColorStr(interval_str, COLOR3B.GREEN)
    interval_str = string.format(Language.ContralBeasts.AttrWheel6, interval_str)
    self.node_list.all_refining_lun.text.text = interval_str
    self.node_list.all_refining_level_limit.text.text = string.format(Language.ContralBeasts.AttrWheel7, ToColorStr(level_limit, COLOR3B.GREEN))
end

-- 检测是否自动炼根
function ControlBeastsWGView:CheckFefiningAuto()
    if self.is_feining_auto then
        -- 增加一个冷却
        self:OperateOnceFeining()
        -- self:CleanOperateCoolTime()
        -- self.opeate_cool_timer = GlobalTimerQuest:AddDelayTimer(function ()
        --     if self.is_feining_auto then
             
        --     end
        -- end, AUTO_FEFINING_TIME)
    end
end

-- 刷新按钮状态
function ControlBeastsWGView:FlushFeiningButtonState()
    local str = self.is_feining_auto and Language.ContralBeasts.AttrWheel5 or Language.ContralBeasts.AttrWheel4
    self.node_list.btn_auto_operate_tex.text.text = str
end

-- 关闭自动
function ControlBeastsWGView:StopAutoFeiningOperate()
    if self.is_feining_auto then
        self.is_feining_auto = false
    end

    self:FlushFeiningButtonState()
end

-- 检测当前星阵是否满星
function ControlBeastsWGView:CheckFeiningFullStar()
    if not self.select_fefining_data then
        return true, -1
    end

    local attr_list = self.select_fefining_data.attr_list
    if not attr_list then
        return true, -1
    end

    local not_full_table = {}
    for index, attr_data in ipairs(attr_list) do
        if attr_data and attr_data.star_num < 10 then
            table.insert(not_full_table, index)
        end
    end

    if #not_full_table == 0 then
        return true, -1
    end

    local attr_index_ran = math.random(1, #not_full_table)
    return false, not_full_table[attr_index_ran]
end

-- 刷新当前的界面选中的查看状态
function ControlBeastsWGView:FlushRawSelectStatus()
    local is_change_full = false
    if self.show_type == nil then
        self.show_type = RAW_SHOW_TYPE.SMALL
        is_change_full = true
    end
    
    self.node_list.refining_mask_raw_image:SetActive(self.show_type == RAW_SHOW_TYPE.FULL)
    self.node_list.change_show_full_type_btn:SetActive(self.show_type ~= RAW_SHOW_TYPE.FULL)
    self.node_list.change_show_small_type_btn:SetActive(self.show_type == RAW_SHOW_TYPE.FULL)
    self.node_list.refining_tween_root.scroll_rect.enabled = self.show_type == RAW_SHOW_TYPE.FULL

    local vec = self.show_type == RAW_SHOW_TYPE.FULL and Vector3.one or Vector3(0.5, 0.5, 0.5)
    if self.show_type == RAW_SHOW_TYPE.FULL then
        self:JumpSelectRePos()
    else
        self.node_list.refining_item_root.transform:DOAnchorPos(Vector3(1, 1, 1), 0.5)
    end
    
    self.node_list.refining_tween_root.transform:DOScale(vec, 0.5):OnComplete(function()
        if is_change_full then
            self:ClickChangeShowType(RAW_SHOW_TYPE.FULL)
        end
    end)
end
---------------------------------------------------------------------
--自动炼根
function ControlBeastsWGView:OperateAutoFeining()
    self.is_feining_auto = not self.is_feining_auto

    if self.is_feining_auto then
        self:OperateOnceFeining()
        self:FlushFeiningButtonState()
    else
        self:StopAutoFeiningOperate()
    end
end

--打开等级预览界面
function ControlBeastsWGView:OpenLevelPreviewView()
    ControlBeastsWGCtrl.Instance:OpenRefiningLevelView()
end

-- 单次炼根
function ControlBeastsWGView:OperateOnceFeining(is_click)
    if is_click and self.is_feining_auto then    -- 手动点击且在自动炼根中则不操作
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip1)
        return
    end

    if not self.select_fefining_data then
        return
    end

    -- 检测是否可以炼根
    local is_can_feining = true
    local star_circle_level_cfg = ControlBeastsWGData.Instance:GetStarCircleLevelCfgById(self.select_fefining_data.interval_times)
    if star_circle_level_cfg and star_circle_level_cfg.item then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(star_circle_level_cfg.item.item_id)
        is_can_feining = item_num >= star_circle_level_cfg.item.num
        
        if not is_can_feining then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = star_circle_level_cfg.item.item_id}) 
        end
    end

    if not is_can_feining then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip2)
        self:StopAutoFeiningOperate()
        return
    end

    local is_full, attr_index = self:CheckFeiningFullStar()
    if is_full then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip7)
        self:StopAutoFeiningOperate()
        return
    end
    
    ---发送协议
    ControlBeastsWGCtrl.Instance:SendOperateTypeStarCircleUpgrade(self.select_fefining_index, attr_index)
end

-- 开启新轮回
function ControlBeastsWGView:ClickOpenNewBtn()
    if not self.select_fefining_data then
        return
    end

    ControlBeastsWGCtrl:SendOperateTypeStarNewCircle(self.select_fefining_index)
end

-- 改变展示类型
function ControlBeastsWGView:ClickChangeShowType(var_show_type)
    if self.show_type == var_show_type then
        return
    end

    self.show_type = var_show_type
    self:FlushRawSelectStatus()
end
