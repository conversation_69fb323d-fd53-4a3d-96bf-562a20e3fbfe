YinianMagicTipsView = YinianMagicTipsView or BaseClass(SafeBaseView)

function YinianMagicTipsView:__init()
	self.view_style = ViewStyle.Half
	self.select_type = 1
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/yinianmagic_prefab", "laout_magic_tips_view")
end

function YinianMagicTipsView:LoadCallBack()
	if not self.item_list then
		self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.item_list:SetStartZeroIndex(true)
	end

	if not self.model_display then
		self.model_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.model_display:SetRenderTexUI3DModel(display_data)
		-- self.model_display:SetUI3DModel(self.node_list.display.transform,
		-- 	self.node_list.EventTriggerListener.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.model_display)
	end

	XUI.AddClickEventListener(self.node_list.confirm_btn, BindTool.Bind(self.OnClickFunc, self))
end

function YinianMagicTipsView:ReleaseCallBack()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end
end

function YinianMagicTipsView:FlushTipsView(select_type)
	self.select_type = select_type
end

function YinianMagicTipsView:OnFlush()
	self.node_list.bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ryxh_beijingt" .. self.select_type))

	self.node_list.btn_close_window.image:LoadSprite("uis/view/yinianmagic/images_atlas", "a2_ryxh_close" ..
		self.select_type)

	local bundle, asset = ResPath.GetRawImagesPNG("a2_ynsm_big_" .. self.select_type .. "_" .. 12)
	self.node_list.title_img.raw_image:LoadSpriteAsync(bundle, asset)

	self:FlushModel()

	local cap = YinianMagicWGData.Instance:GetMaxCapability(self.select_type)
	self.node_list.cap_value.text.text = cap

	local text_dec = string.format(Language.YinianMagicView.SelectStr,
		Language.YinianMagicView.SelectType[self.select_type])
	self.node_list.tips_text.text.text = text_dec

	local type_cfg = YinianMagicWGData.Instance:GetTypeCfgByType(self.select_type)
	if not IsEmptyTable(type_cfg) then
		self.item_list:SetDataList(type_cfg.choose_reward_item)
	end
end

function YinianMagicTipsView:FlushModel()
	if self.model_display then
		self.model_display:RemoveAllModel()
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = { ignore_god_or_demon = true }
		self.model_display:SetModelResInfo(role_vo, special_status_table, function()
			self.model_display:PlayRoleAction()
		end)
		self.model_display:FixToOrthographic(self.root_node_transform)

		local cur_grade = YinianMagicWGData.Instance:GetMaxGrade(self.select_type)
		local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(self.select_type, cur_grade)
		if IsEmptyTable(cur_cfg) then
			return
		end
		local model_type = cur_cfg.modle_type
		local show_model_type_cfg = YinianMagicWGData.Instance:GetModelTypeCfg(self.select_type, model_type)
		local appe_image_id = show_model_type_cfg.appe_image_id
		self.model_display:SetGodOrDemonResid(appe_image_id)
	end
end

function YinianMagicTipsView:OnClickFunc()
	YinianMagicWGCtrl.Instance:SendYinianMagicRequest(YINIAN_MAGIC_OPERATE_TYPE.CHOOSE_TYPE, self.select_type)
	YinianMagicWGCtrl.Instance:CloseSelectView()
	self:Close()
end
