-- L-雷法抽奖.xls
local item_table={
[1]={item_id=38104,num=1,is_bind=1},
[2]={item_id=38116,num=1,is_bind=1},
[3]={item_id=26380,num=1,is_bind=1},
[4]={item_id=26360,num=1,is_bind=1},
[5]={item_id=26350,num=1,is_bind=1},
[6]={item_id=26349,num=1,is_bind=1},
[7]={item_id=26358,num=1,is_bind=1},
[8]={item_id=26359,num=1,is_bind=1},
[9]={item_id=26121,num=1,is_bind=1},
[10]={item_id=26377,num=1,is_bind=1},
[11]={item_id=26376,num=1,is_bind=1},
[12]={item_id=26378,num=1,is_bind=1},
[13]={item_id=26379,num=1,is_bind=1},
[14]={item_id=40212,num=1,is_bind=1},
[15]={item_id=40211,num=1,is_bind=1},
[16]={item_id=40210,num=1,is_bind=1},
[17]={item_id=62000,num=1,is_bind=1},
[18]={item_id=61500,num=1,is_bind=1},
[19]={item_id=61600,num=1,is_bind=1},
[20]={item_id=61700,num=1,is_bind=1},
[21]={item_id=61800,num=1,is_bind=1},
[22]={item_id=61900,num=1,is_bind=1},
[23]={item_id=61501,num=1,is_bind=1},
[24]={item_id=61601,num=1,is_bind=1},
[25]={item_id=61701,num=1,is_bind=1},
[26]={item_id=61801,num=1,is_bind=1},
[27]={item_id=61901,num=1,is_bind=1},
[28]={item_id=61502,num=1,is_bind=1},
[29]={item_id=61602,num=1,is_bind=1},
[30]={item_id=61702,num=1,is_bind=1},
[31]={item_id=61802,num=1,is_bind=1},
[32]={item_id=61902,num=1,is_bind=1},
[33]={item_id=61503,num=1,is_bind=1},
[34]={item_id=61603,num=1,is_bind=1},
[35]={item_id=61703,num=1,is_bind=1},
[36]={item_id=61803,num=1,is_bind=1},
[37]={item_id=61903,num=1,is_bind=1},
[38]={item_id=61504,num=1,is_bind=1},
[39]={item_id=61604,num=1,is_bind=1},
[40]={item_id=61704,num=1,is_bind=1},
[41]={item_id=61804,num=1,is_bind=1},
[42]={item_id=61904,num=1,is_bind=1},
[43]={item_id=26191,num=1,is_bind=1},
[44]={item_id=27857,num=1,is_bind=1},
[45]={item_id=27855,num=1,is_bind=1},
[46]={item_id=26521,num=1,is_bind=1},
[47]={item_id=62004,num=1,is_bind=1},
[48]={item_id=38120,num=1,is_bind=1},
[49]={item_id=26504,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
draw_num={
{},
{seq=1,draw_num=50,consume_num=50,}
},

draw_num_meta_table_map={
},
reward_pool={
{},
{level=2,next_level_draw_num=4500,},
{level=3,next_level_draw_num=6000,},
{level=4,next_level_draw_num=12000,},
{level=5,next_level_draw_num=24000,}
},

reward_pool_meta_table_map={
},
reward={
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=1,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{},
{seq=1,reward_item=item_table[1],},
{seq=2,reward_item=item_table[2],},
{seq=3,reward_item=item_table[3],},
{seq=4,reward_item=item_table[4],},
{seq=5,reward_item=item_table[5],},
{seq=6,reward_item=item_table[6],},
{seq=7,reward_item=item_table[7],},
{seq=8,reward_item=item_table[8],},
{seq=9,reward_item=item_table[9],},
{seq=10,reward_item=item_table[10],},
{seq=11,reward_item=item_table[11],},
{seq=12,reward_item=item_table[12],},
{seq=13,reward_item=item_table[13],},
{seq=14,reward_item=item_table[14],},
{seq=15,reward_item=item_table[15],},
{seq=16,reward_item=item_table[16],},
{seq=17,reward_item=item_table[17],},
{seq=18,reward_item=item_table[18],},
{seq=19,reward_item=item_table[19],},
{seq=20,reward_item=item_table[20],},
{seq=21,reward_item=item_table[21],},
{seq=22,reward_item=item_table[22],},
{seq=23,reward_item=item_table[23],},
{seq=24,reward_item=item_table[24],},
{seq=25,reward_item=item_table[25],},
{seq=26,reward_item=item_table[26],},
{seq=27,reward_item=item_table[27],},
{seq=28,reward_item=item_table[28],},
{seq=29,reward_item=item_table[29],},
{seq=30,reward_item=item_table[30],},
{seq=31,reward_item=item_table[31],},
{seq=32,reward_item=item_table[32],},
{seq=33,reward_item=item_table[33],},
{seq=34,reward_item=item_table[34],},
{seq=35,reward_item=item_table[35],},
{seq=36,reward_item=item_table[36],},
{seq=37,reward_item=item_table[37],},
{seq=38,reward_item=item_table[38],},
{seq=39,reward_item=item_table[39],},
{seq=40,reward_item=item_table[40],},
{seq=41,reward_item=item_table[41],},
{seq=42,reward_item=item_table[42],}
},

reward_meta_table_map={
[48]=147,	-- depth:1
[33]=132,	-- depth:1
[34]=133,	-- depth:1
[35]=134,	-- depth:1
[36]=135,	-- depth:1
[37]=136,	-- depth:1
[38]=137,	-- depth:1
[39]=138,	-- depth:1
[40]=139,	-- depth:1
[41]=140,	-- depth:1
[42]=141,	-- depth:1
[43]=142,	-- depth:1
[122]=160,	-- depth:1
[121]=159,	-- depth:1
[120]=158,	-- depth:1
[119]=157,	-- depth:1
[118]=156,	-- depth:1
[117]=155,	-- depth:1
[116]=154,	-- depth:1
[115]=153,	-- depth:1
[114]=152,	-- depth:1
[113]=151,	-- depth:1
[112]=150,	-- depth:1
[111]=149,	-- depth:1
[110]=148,	-- depth:1
[109]=48,	-- depth:2
[32]=131,	-- depth:1
[31]=130,	-- depth:1
[30]=129,	-- depth:1
[29]=128,	-- depth:1
[2]=124,	-- depth:1
[3]=125,	-- depth:1
[4]=126,	-- depth:1
[5]=127,	-- depth:1
[6]=29,	-- depth:2
[7]=30,	-- depth:2
[8]=31,	-- depth:2
[9]=32,	-- depth:2
[10]=33,	-- depth:2
[11]=34,	-- depth:2
[12]=35,	-- depth:2
[13]=36,	-- depth:2
[108]=146,	-- depth:1
[14]=37,	-- depth:2
[16]=39,	-- depth:2
[17]=40,	-- depth:2
[18]=41,	-- depth:2
[19]=42,	-- depth:2
[20]=43,	-- depth:2
[21]=143,	-- depth:1
[22]=144,	-- depth:1
[23]=145,	-- depth:1
[25]=2,	-- depth:2
[26]=3,	-- depth:2
[27]=4,	-- depth:2
[28]=5,	-- depth:2
[15]=38,	-- depth:2
[107]=23,	-- depth:2
[106]=22,	-- depth:2
[105]=21,	-- depth:2
[74]=107,	-- depth:3
[73]=106,	-- depth:3
[72]=105,	-- depth:3
[71]=20,	-- depth:3
[70]=19,	-- depth:3
[69]=18,	-- depth:3
[68]=17,	-- depth:3
[67]=16,	-- depth:3
[66]=15,	-- depth:3
[65]=14,	-- depth:3
[64]=13,	-- depth:3
[63]=12,	-- depth:3
[75]=108,	-- depth:2
[62]=11,	-- depth:3
[60]=9,	-- depth:3
[59]=8,	-- depth:3
[58]=7,	-- depth:3
[57]=6,	-- depth:3
[56]=28,	-- depth:3
[55]=27,	-- depth:3
[54]=26,	-- depth:3
[53]=25,	-- depth:3
[46]=74,	-- depth:4
[51]=112,	-- depth:2
[50]=111,	-- depth:2
[49]=110,	-- depth:2
[61]=10,	-- depth:3
[47]=75,	-- depth:3
[76]=109,	-- depth:3
[78]=50,	-- depth:3
[104]=71,	-- depth:4
[103]=70,	-- depth:4
[102]=69,	-- depth:4
[101]=68,	-- depth:4
[100]=67,	-- depth:4
[99]=66,	-- depth:4
[98]=65,	-- depth:4
[97]=64,	-- depth:4
[96]=63,	-- depth:4
[95]=62,	-- depth:4
[94]=61,	-- depth:4
[93]=60,	-- depth:4
[77]=49,	-- depth:3
[92]=59,	-- depth:4
[90]=57,	-- depth:4
[89]=56,	-- depth:4
[88]=55,	-- depth:4
[87]=54,	-- depth:4
[86]=53,	-- depth:4
[44]=72,	-- depth:4
[84]=117,	-- depth:2
[45]=73,	-- depth:4
[82]=115,	-- depth:2
[81]=114,	-- depth:2
[80]=113,	-- depth:2
[79]=51,	-- depth:3
[91]=58,	-- depth:4
[83]=116,	-- depth:2
},
exchange={
{price=10000,},
{seq=1,item_id=28151,},
{seq=2,item_id=28152,},
{seq=3,item_id=28153,},
{seq=4,item_id=28154,},
{seq=5,item_id=28155,}
},

exchange_meta_table_map={
},
item_random_desc={
{level=1,random_count=0.01,},
{level=1,random_count=0.22,},
{level=1,random_count=0.07,},
{level=1,},
{level=1,},
{level=1,random_count=3.06,},
{level=1,random_count=19.91,},
{level=1,random_count=1.53,},
{level=1,random_count=1.53,},
{level=1,random_count=9.95,},
{level=1,random_count=3.06,},
{level=1,random_count=19.91,},
{level=1,random_count=1.53,},
{number=13,item_id=26379,},
{level=1,random_count=0.31,},
{level=1,random_count=1.07,},
{level=1,random_count=3.22,},
{level=1,random_count=8.88,},
{number=18,item_id=61500,},
{level=1,random_count=4.9,},
{level=1,random_count=4.9,},
{level=1,random_count=4.9,},
{level=1,random_count=4.9,},
{level=2,random_count=0.012,},
{level=2,random_count=0.264,},
{level=2,random_count=0.084,},
{level=2,},
{level=2,},
{level=2,random_count=3.02,},
{level=2,random_count=19.61,},
{number=7,item_id=26358,},
{number=8,item_id=26359,},
{level=2,random_count=9.8,},
{level=2,random_count=3.02,},
{level=2,random_count=19.61,},
{level=2,random_count=1.51,},
{level=2,random_count=1.51,},
{level=2,random_count=0.3,},
{level=2,random_count=1.06,},
{level=2,random_count=3.17,},
{level=2,random_count=8.75,},
{level=2,random_count=4.83,},
{level=2,random_count=4.83,},
{number=20,item_id=61700,},
{number=21,item_id=61800,},
{number=22,item_id=61900,},
{level=2,random_count=0.3,},
{level=2,random_count=0.3,},
{level=2,random_count=0.3,},
{level=2,random_count=0.3,},
{level=2,random_count=0.3,},
{level=3,random_count=0.018,},
{level=3,random_count=0.396,},
{level=3,random_count=0.126,},
{level=3,},
{level=3,},
{level=3,random_count=2.93,},
{level=3,random_count=19.03,},
{level=3,random_count=1.46,},
{level=3,random_count=1.46,},
{level=3,random_count=9.52,},
{level=3,random_count=2.93,},
{level=3,random_count=19.03,},
{level=3,random_count=1.46,},
{level=3,random_count=1.46,},
{level=3,random_count=0.29,},
{level=3,random_count=1.02,},
{level=3,random_count=3.07,},
{level=3,random_count=8.49,},
{level=3,random_count=4.69,},
{level=3,random_count=4.69,},
{level=3,random_count=4.69,},
{level=3,random_count=4.69,},
{level=3,random_count=4.69,},
{level=3,random_count=0.59,},
{level=3,random_count=0.59,},
{level=3,random_count=0.59,},
{level=3,random_count=0.59,},
{level=3,random_count=0.59,},
{number=28,item_id=61502,},
{level=3,random_count=0.29,},
{level=3,random_count=0.29,},
{number=31,item_id=61802,},
{level=3,random_count=0.29,},
{level=4,random_count=0.024,},
{level=4,random_count=0.528,},
{level=4,random_count=0.168,},
{level=4,},
{level=4,},
{number=5,item_id=26350,},
{number=6,item_id=26349,},
{number=7,item_id=26358,},
{number=8,item_id=26359,},
{level=4,random_count=9.12,},
{level=4,random_count=2.81,},
{level=4,random_count=18.23,},
{level=4,random_count=1.4,},
{level=4,random_count=1.4,},
{level=4,random_count=0.28,},
{level=4,random_count=0.98,},
{level=4,random_count=2.95,},
{level=4,random_count=8.13,},
{number=18,item_id=61500,},
{level=4,random_count=4.49,},
{level=4,random_count=4.49,},
{level=4,random_count=4.49,},
{level=4,random_count=4.49,},
{level=4,random_count=0.84,},
{level=4,random_count=0.84,},
{level=4,random_count=0.84,},
{number=26,item_id=61801,},
{level=4,random_count=0.84,},
{number=28,item_id=61502,},
{level=4,random_count=0.56,},
{level=4,random_count=0.56,},
{level=4,random_count=0.56,},
{level=4,random_count=0.56,},
{number=33,item_id=61503,},
{number=34,item_id=61603,},
{number=35,item_id=61703,},
{number=36,item_id=61803,},
{level=4,random_count=0.28,},
{random_count=0.03,show_type=1,},
{number=1,item_id=38104,random_count=0.66,show_type=1,},
{number=2,item_id=38116,random_count=0.21,show_type=1,},
{number=3,item_id=26380,},
{number=4,item_id=26360,},
{number=5,item_id=26350,},
{number=6,item_id=26349,},
{number=7,item_id=26358,},
{number=8,item_id=26359,},
{number=9,item_id=26121,random_count=8.63,},
{number=10,item_id=26377,random_count=2.66,},
{number=11,item_id=26376,random_count=17.26,},
{number=12,item_id=26378,random_count=1.33,},
{number=13,item_id=26379,},
{number=14,item_id=40212,random_count=0.27,},
{number=15,item_id=40211,random_count=0.93,},
{number=16,item_id=40210,random_count=2.79,},
{number=17,item_id=62000,random_count=7.7,},
{number=18,item_id=61500,},
{number=19,item_id=61600,random_count=4.25,show_type=2,},
{number=20,item_id=61700,},
{number=21,item_id=61800,},
{number=22,item_id=61900,},
{number=23,item_id=61501,random_count=1.06,show_type=2,},
{number=24,item_id=61601,},
{number=25,item_id=61701,},
{number=26,item_id=61801,},
{number=27,item_id=61901,},
{number=28,item_id=61502,},
{number=29,item_id=61602,random_count=0.8,show_type=2,},
{number=30,item_id=61702,},
{number=31,item_id=61802,},
{number=32,item_id=61902,},
{number=33,item_id=61503,},
{number=34,item_id=61603,},
{number=35,item_id=61703,},
{number=36,item_id=61803,},
{number=37,item_id=61903,random_count=0.53,show_type=2,},
{number=38,item_id=61504,random_count=0.27,show_type=2,},
{number=39,item_id=61604,},
{number=40,item_id=61704,},
{number=41,item_id=61804,},
{number=42,item_id=61904,}
},

item_random_desc_meta_table_map={
[1]=123,	-- depth:1
[131]=135,	-- depth:1
[129]=134,	-- depth:1
[28]=127,	-- depth:1
[128]=133,	-- depth:1
[52]=123,	-- depth:1
[55]=126,	-- depth:1
[56]=28,	-- depth:2
[89]=56,	-- depth:3
[88]=55,	-- depth:2
[85]=123,	-- depth:1
[130]=135,	-- depth:1
[27]=88,	-- depth:3
[136]=135,	-- depth:1
[24]=123,	-- depth:1
[4]=27,	-- depth:4
[5]=89,	-- depth:4
[100]=138,	-- depth:1
[99]=137,	-- depth:1
[98]=136,	-- depth:2
[97]=135,	-- depth:1
[96]=134,	-- depth:1
[95]=133,	-- depth:1
[94]=132,	-- depth:1
[93]=97,	-- depth:2
[92]=97,	-- depth:2
[91]=96,	-- depth:2
[90]=95,	-- depth:2
[162]=161,	-- depth:1
[163]=161,	-- depth:1
[101]=139,	-- depth:1
[102]=140,	-- depth:1
[159]=160,	-- depth:1
[144]=142,	-- depth:1
[141]=142,	-- depth:1
[145]=142,	-- depth:1
[147]=146,	-- depth:1
[148]=146,	-- depth:1
[164]=161,	-- depth:1
[149]=146,	-- depth:1
[143]=142,	-- depth:1
[150]=146,	-- depth:1
[153]=152,	-- depth:1
[154]=152,	-- depth:1
[155]=152,	-- depth:1
[156]=160,	-- depth:1
[157]=160,	-- depth:1
[158]=160,	-- depth:1
[151]=152,	-- depth:1
[69]=140,	-- depth:1
[165]=161,	-- depth:1
[57]=128,	-- depth:2
[61]=132,	-- depth:1
[60]=131,	-- depth:2
[59]=130,	-- depth:2
[58]=129,	-- depth:2
[68]=139,	-- depth:1
[41]=140,	-- depth:1
[40]=139,	-- depth:1
[62]=133,	-- depth:1
[39]=138,	-- depth:1
[37]=136,	-- depth:2
[36]=135,	-- depth:1
[29]=128,	-- depth:2
[30]=129,	-- depth:2
[31]=36,	-- depth:2
[32]=36,	-- depth:2
[33]=132,	-- depth:1
[38]=137,	-- depth:1
[34]=133,	-- depth:1
[63]=134,	-- depth:1
[65]=136,	-- depth:2
[6]=128,	-- depth:2
[7]=129,	-- depth:2
[8]=130,	-- depth:2
[9]=131,	-- depth:2
[10]=132,	-- depth:1
[11]=133,	-- depth:1
[64]=135,	-- depth:1
[13]=135,	-- depth:1
[12]=134,	-- depth:1
[15]=137,	-- depth:1
[67]=138,	-- depth:1
[16]=138,	-- depth:1
[66]=137,	-- depth:1
[17]=139,	-- depth:1
[18]=140,	-- depth:1
[14]=13,	-- depth:2
[35]=134,	-- depth:1
[42]=141,	-- depth:2
[43]=142,	-- depth:1
[44]=43,	-- depth:2
[70]=141,	-- depth:2
[25]=124,	-- depth:1
[23]=145,	-- depth:2
[22]=144,	-- depth:2
[21]=143,	-- depth:2
[20]=142,	-- depth:1
[19]=20,	-- depth:2
[45]=43,	-- depth:2
[3]=125,	-- depth:1
[2]=124,	-- depth:1
[26]=125,	-- depth:1
[46]=43,	-- depth:2
[117]=155,	-- depth:2
[122]=160,	-- depth:1
[53]=124,	-- depth:1
[54]=125,	-- depth:1
[87]=125,	-- depth:1
[86]=124,	-- depth:1
[84]=155,	-- depth:2
[82]=153,	-- depth:2
[81]=152,	-- depth:1
[80]=81,	-- depth:2
[79]=150,	-- depth:2
[78]=149,	-- depth:2
[77]=148,	-- depth:2
[76]=147,	-- depth:2
[75]=146,	-- depth:1
[74]=145,	-- depth:2
[73]=144,	-- depth:2
[72]=143,	-- depth:2
[71]=142,	-- depth:1
[51]=150,	-- depth:2
[50]=149,	-- depth:2
[49]=148,	-- depth:2
[48]=147,	-- depth:2
[121]=122,	-- depth:2
[120]=122,	-- depth:2
[119]=122,	-- depth:2
[118]=122,	-- depth:2
[116]=154,	-- depth:2
[115]=153,	-- depth:2
[114]=152,	-- depth:1
[113]=114,	-- depth:2
[47]=146,	-- depth:1
[112]=150,	-- depth:2
[110]=148,	-- depth:2
[109]=147,	-- depth:2
[108]=146,	-- depth:1
[107]=145,	-- depth:2
[106]=144,	-- depth:2
[105]=143,	-- depth:2
[104]=142,	-- depth:1
[103]=104,	-- depth:2
[111]=108,	-- depth:2
[83]=81,	-- depth:2
},
count_reward={
{},
{seq=1,draw_times=200,reward_item={[0]=item_table[43]},},
{seq=2,draw_times=400,reward_item={[0]=item_table[1]},},
{seq=3,draw_times=600,reward_item={[0]=item_table[44]},},
{seq=4,draw_times=1000,reward_item={[0]=item_table[45]},},
{seq=5,draw_times=1500,reward_item={[0]=item_table[46]},},
{seq=6,draw_times=2000,reward_item={[0]=item_table[47]},},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=2,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=3,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=4,},
{level=5,},
{level=5,},
{level=5,},
{level=5,},
{level=5,},
{level=5,},
{level=5,}
},

count_reward_meta_table_map={
[33]=5,	-- depth:1
[32]=4,	-- depth:1
[23]=2,	-- depth:1
[30]=23,	-- depth:2
[28]=7,	-- depth:1
[27]=6,	-- depth:1
[26]=33,	-- depth:2
[25]=32,	-- depth:2
[24]=3,	-- depth:1
[31]=24,	-- depth:2
[18]=25,	-- depth:3
[20]=27,	-- depth:2
[19]=26,	-- depth:3
[34]=20,	-- depth:3
[17]=31,	-- depth:3
[16]=30,	-- depth:3
[14]=28,	-- depth:2
[13]=34,	-- depth:4
[12]=19,	-- depth:4
[11]=18,	-- depth:4
[10]=17,	-- depth:4
[9]=16,	-- depth:4
[21]=14,	-- depth:3
[35]=21,	-- depth:4
},
show_reward={
{},
{level=2,item_id_list="38120|61501|40212|38116|61601",},
{level=3,item_id_list="38120|61502|40212|38116|61602",},
{level=4,item_id_list="38120|61503|40212|38116|61603",},
{level=5,item_id_list="38120|61504|40212|38116|61604",}
},

show_reward_meta_table_map={
},
other_default_table={draw_item_id=64512,money_type=1,draw_item_price=60,add_score=1,add_coin=10000,excahnge_show_item_id=91643,},

draw_num_default_table={seq=0,draw_num=10,consume_num=10,},

reward_pool_default_table={level=1,next_level_draw_num=3000,},

reward_default_table={level=5,seq=0,reward_item=item_table[48],},

exchange_default_table={seq=0,item_id=38120,price=3000,limit_type=0,limit_num=0,open_day=0,limit_level=0,},

item_random_desc_default_table={level=5,number=0,item_id=38120,random_count=0.67,show_type=3,},

count_reward_default_table={level=1,seq=0,draw_times=100,reward_item={[0]=item_table[49]},},

show_reward_default_table={level=1,item_id_list="38120|61500|40212|38116|61600",name="应龙·濯世",}

}

