-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

GlobalEventSystem = GlobalEventSystem or nil
GlobalTimerQuest = GlobalTimerQuest or nil

Play = Play or {
	ctrl_state = CTRL_STATE.START,
	module_list = {},
}

function Play:Start(call_back)
	table.insert(self.module_list, Runner.New())			-- 循环
	GlobalEventSystem = EventSystem.New()					-- 全局事件系统
	Runner.Instance:AddRunObj(GlobalEventSystem, 3)
	table.insert(self.module_list, GlobalEventSystem)

	GlobalTimerQuest = TimerQuest.New()						-- 定时器
	table.insert(self.module_list, GlobalTimerQuest)

	table.insert(self.module_list, CountDown.New())			-- 3D项目时代的倒计时
	table.insert(self.module_list, CountDownManager.New())	-- 2D项目时代的倒计时
	table.insert(self.module_list, GameNet.New())			-- 网络
	table.insert(self.module_list, ConfigManager.New())		-- 配置管理器
	-- table.insert(self.module_list, StepPool.New())			-- 分步池
	table.insert(self.module_list, GameMapHelper.New())		-- 地图
	table.insert(self.module_list, PreloadManager.New())	-- 预加载
	table.insert(self.module_list, EffectManager.New())		-- 特效
	table.insert(self.module_list, RoadFindWay.New())			-- 寻路

	local modules_ctrl = ModulesWGCtrl.New()		-- 游戏模块
	modules_ctrl:Start(function (percent)
		InitWGCtrl:SetPercent(percent * 0.2 + 0.6)
		if percent >= 1 then
			table.insert(self.module_list, modules_ctrl)
			table.insert(self.module_list, TipsSystemManager.New())	-- 系统提示
			table.insert(self.module_list, ZCTipsSystemManager.New())	-- 战场传闻提示
			table.insert(self.module_list, TipsFloatingManager.New())	-- 右下角上漂文字
			-- table.insert(self.module_list, TipsFloatingName.New())		-- 右下角上漂文字(好友名字用)
			-- table.insert(self.module_list, TipsActivityNoticeManager.New())	-- 活动公告
			-- table.insert(self.module_list, RareItemTipManager.New())			-- 珍稀道具提示框

			if self.complete_callback then
				self.complete_callback()
				self.complete_callback = nil
			end
			-- 注册通知事件
			self:RefreshNotification()
			if call_back then
				call_back()
			end
		end
	end)
end

function Play:Update(now_time, elapse_time)
	if self.ctrl_state == CTRL_STATE.UPDATE then
		if Runner then
			Runner.Instance:Update(now_time, elapse_time)
		end
	elseif self.ctrl_state == CTRL_STATE.START then
		self.ctrl_state = CTRL_STATE.NONE
		self:Start(function ()
			self.ctrl_state = CTRL_STATE.UPDATE
		end)
	elseif self.ctrl_state == CTRL_STATE.STOP then
		self.ctrl_state = CTRL_STATE.NONE
		self:Stop()
		PopCtrl(self)
	end
end

function Play:SetComplete(complete_callback)
	self.complete_callback = complete_callback
end

function Play:Stop()
	-- 析构各个模块
	local count = #self.module_list
	for i = count, 1, -1 do
		self.module_list[i]:DeleteMe()
	end
	self.module_list = {}
	self.complete_callback = nil
end

function Play:RefreshNotification()
	LocalNotification.CancelAllNotifications(50)

	-- local now = os.time()
	-- local date_t = Split(os.date("%w-%H-%M-%S", now), "-")
	-- local week_pass = date_t[1] * 24 * 3600 + date_t[2] * 3600 + date_t[3] * 60 + date_t[4]
	-- local week_start = now - week_pass

	-- local cfg = ConfigManager.Instance:GetAutoConfig("notification_config_auto").notification
	-- for k,v in pairs(cfg) do
	-- 	local notify_time = v.weekday * 24 * 3600 + v.hour * 3600 + v.minute * 60 + v.second
	-- 	local next_time = nil
	-- 	if notify_time > week_pass then
	-- 		next_time = week_start + notify_time
	-- 	else
	-- 		next_time = week_start + notify_time + 7 * 24 * 60 * 60
	-- 	end

	-- 	LocalNotification.SendRepeatingNotification(
	-- 		k, next_time * 1000, CalendarUnit.Week, v.title, v.content)
	-- end
end

return Play
