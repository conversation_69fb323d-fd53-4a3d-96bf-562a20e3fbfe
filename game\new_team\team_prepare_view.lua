TeamPrepareView = TeamPrepareView or BaseClass(SafeBaseView)

local TEAM_MEMBER_COUNT = 5

function TeamPrepareView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(1288, 580)})
    self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_team_prepare")
    self.prepare_remain_time = 0

    self.fake_pipei_delay_list = {}
end

function TeamPrepareView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["size"])
    XUI.AddClickEventListener(self.node_list["btn_prepare"], BindTool.Bind(self.OnClickPrepare, self))
    XUI.AddClickEventListener(self.node_list["btn_refuse"], BindTool.Bind(self.OnClickRefuse, self))
    XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind(self.OnClickRefuse, self))
    XUI.AddClickEventListener(self.node_list["btn_exp_add"], BindTool.Bind(self.OnClickExpRule, self))
    XUI.AddClickEventListener(self.node_list["btn_add_times"], BindTool.Bind(self.OnClickAddTimes, self))
    self.day_count_change_event = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.OnDayCountChange, self))
    self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.FuBenPanel, self.get_guide_ui_event)
    self.vip_change_event = GlobalEventSystem:Bind(OtherEventType.VIP_INFO_CHANGE, BindTool.Bind(self.OnVipInfoChange, self))
    self.node_list.vip_tips.text.text = Language.FuBenPanel.ToBeVip6
    XUI.AddClickEventListener(self.node_list["vip_btn_tips"],BindTool.Bind(self.OnClickActiveVip6,self))
    self.node_list["layout_combine_mark"].button:AddClickListener(BindTool.Bind(self.OnClickCombine, self))
end

function TeamPrepareView:ReleaseCallBack()
    if  self.member_info_list ~= nil then
        for i, v in pairs(self.member_info_list) do
            v:DeleteMe()
        end
        self.member_info_list = nil
    end
    if self.team_room_prepare_timer then
        GlobalTimerQuest:CancelQuest(self.team_room_prepare_timer)
        self.team_room_prepare_timer = nil
    end
    if FunctionGuide.Instance then
        FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.FuBenPanel, self.get_guide_ui_event)
        self.get_guide_ui_event = nil
    end
    if self.day_count_change_event then
        GlobalEventSystem:UnBind(self.day_count_change_event)
        self.day_count_change_event = nil
    end
    if self.vip_change_event then
        GlobalEventSystem:UnBind(self.vip_change_event)
        self.vip_change_event = nil
    end

    self:CloseTimer()
end

function TeamPrepareView:CloseTimer(index)
    if index then
        if self.fake_pipei_delay_list[index] then
            GlobalTimerQuest:CancelQuest(self.fake_pipei_delay_list[index])
            self.fake_pipei_delay_list[index] = nil
        end
    else
        for i = 1, TEAM_MEMBER_COUNT do
            if self.fake_pipei_delay_list[i] then
                GlobalTimerQuest:CancelQuest(self.fake_pipei_delay_list[i])
                self.fake_pipei_delay_list[i] = nil
            end
        end
    end
end

function TeamPrepareView:CloseCallBack()
    self:CloseTimer()
    if self.team_room_prepare_timer then
        GlobalTimerQuest:CancelQuest(self.team_room_prepare_timer)
        self.team_room_prepare_timer = nil
    end
    self.fake_pipie_ing = false
    self.fake_pipie_complete = false
    self.fake_pipei_delay_flag = false
    self.prepare_remain_time = 0
end

function TeamPrepareView:ShowIndexCallBack()
   self:Flush()
end

function TeamPrepareView:OnClickExpRule()
    local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.NewTeam.EXP_rule_title)
	rule_tip:SetContent(Language.NewTeam.EXP_desc_rule)
end

function TeamPrepareView:OnDayCountChange(day_count_id)
    self:Flush()
end

function TeamPrepareView:OnClickActiveVip6()
    local tab_index = VipWGData.Instance:GetVIPZeorBuyIsOpenJump()
    ViewManager.Instance:Open(GuideModuleName.Vip, tab_index)
end

function TeamPrepareView:OnClickAddTimes()
    local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if team_type == GoalTeamType.Exp_DuJieXianZhou then 			-- 渡劫仙舟
        --FuBenPanelWGCtrl.Instance:OpenTeamExpBuyView(FUBEN_TYPE.FBCT_WUJINJITAN)
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_WUJINJITAN)
	elseif team_type == GoalTeamType.QingYuanFb then				-- 情缘副本
        NewTeamWGCtrl.Instance:OnClickQingyuanAddTimes()
	elseif team_type == GoalTeamType.YuanGuXianDian then			-- 远古仙殿
		--FuBenPanelWGCtrl.Instance:OpenPetBuy(false, FUBEN_TYPE.HIGH_TEAM_EQUIP)
        --FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.HIGH_TEAM_EQUIP)
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
	--elseif team_type == GoalTeamType.ZhuShenTa then 				-- 诛神塔 --诸神塔不能购买次数
	elseif team_type == GoalTeamType.Exp_FuMoZhanChuan then 		-- 伏魔战船
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.LINGHUNGUANGCHANG)
        --FuBenPanelWGCtrl.Instance:OpenTeamExpBuyView(FUBEN_TYPE.LINGHUNGUANGCHANG)
	elseif team_type == GoalTeamType.ManHuangGuDian then 			-- 蛮荒古殿
		--FuBenPanelWGCtrl.Instance:OpenManHuangGuDianBuy()
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FB_MANHUANG_GUDIAN_FB)
	end
end

--点击准备
function TeamPrepareView:OnClickPrepare()
    -- self.close_func = nil
    --ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
    local is_fake_pipei = self.fake_pipie_ing and not self.fake_pipie_complete
    if is_fake_pipei then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.FakePrepareIngText)
        return
    end
    local is_self_prepare = NewTeamWGData.Instance:GetSelfPrepareState() == 1
    if is_self_prepare then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.HasPrepare)
        return
    end
    SocietyWGCtrl.Instance:SendTeamReadyStateChange(1)
end

function TeamPrepareView:OnVipInfoChange()
    self:Flush()
end

--点击拒绝
function TeamPrepareView:OnClickRefuse()
    --SocietyWGCtrl.Instance:SendTeamReadyStateChange(0)
    NewTeamWGCtrl.Instance:StartOrCancelTeamFB(1)
    self:Close()
end

function TeamPrepareView:OnFLushTimes()
    local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
    local remain_count = total_count - cur_enter_count
    local color = remain_count > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
    self.node_list.remain_times.text.text = string.format(Language.NewTeam.RemainRewardTimes, color, remain_count, total_count)
end

--机器人的匹配流程
function TeamPrepareView:StartFakePipeiDelay(robot_num,team_num)
     if not self.fake_pipei_delay_flag then
        self.fake_pipei_delay_flag = true
    end
    if self.fake_pipie_ing then
        return
    end
    self.fake_pipie_complete = false
    if self.fake_pipei_delay_flag then
        self.fake_pipie_ing = true
        local member_info_list_temp = {}

        for a = 1, TEAM_MEMBER_COUNT do
            if not self.member_info_list[a] then
                self.member_info_list[a] = TeamPrepareMemberInfoRender.New(self.node_list["info"..a])
            end
            if team_num >= a then
                self.member_info_list[a]:SetData({fake_pipei = true})
                self.node_list["info"..a]:SetActive(true)
            else
                self.node_list["info"..a]:SetActive(false)
            end
        end

        for i=1, team_num do
            local member_info_list = NewTeamWGData.Instance:GetRoomMemberList()
            table.insert(member_info_list_temp, member_info_list[i])
            if team_num - i >= robot_num then
                self.member_info_list[i]:SetData(member_info_list_temp[i])
            else
                self:CloseTimer(i)
                self.fake_pipei_delay_list[i] = GlobalTimerQuest:AddDelayTimer(function()
                    if not self.member_info_list[i] then
                        self.member_info_list[i] = TeamPrepareMemberInfoRender.New(self.node_list["info"..i])
                    end
                    if member_info_list_temp[i] then
                        self.member_info_list[i]:SetData(member_info_list_temp[i])
                        self.node_list["info"..i]:SetActive(true)
                    else
                        self.member_info_list[i]:SetData({fake_pipei = true})
                        self.node_list["info"..i]:SetActive(true)
                    end
                end, 0.5)
            end
        end
        self.fake_pipie_complete = true
        self:Flush()
    end
end

function TeamPrepareView:OnFlush(param_t)
    for k,v in pairs(param_t) do
        if k == "prepare_times" then
            self:OnFLushTimes()
            return
        end
    end

    local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    if not self.member_info_list then
        self.member_info_list = {}
    end

    local member_info_list = NewTeamWGData.Instance:GetRoomMemberList()
    --print_error("刷新准备界面》》》》》》成员个数 》》》》 ", #member_info_list, member_info_list)

    --换方式了
    local robot_num = 0
    for k,v in pairs(member_info_list) do
        if v.is_robert == 1 then
            robot_num = robot_num + 1
        end
    end

    local team_num = 5
    if team_type == GoalTeamType.QingYuanFb then
        team_num = 2
    end

    -- --策划说匹配到机器人的时候 需要做一个假的匹配过程 看起来真一点。。。。。。
    -- --表示匹配到的都是机器人 要做假的匹配过程
    -- if robot_num >= 1 and not self.fake_pipie_complete then
    --     self:StartFakePipeiDelay(robot_num,team_num)
    -- else
      
    -- end
    for i = 1, TEAM_MEMBER_COUNT do
        if not self.member_info_list[i] then
            self.member_info_list[i] = TeamPrepareMemberInfoRender.New(self.node_list["info"..i])
        end
        if member_info_list[i] then
            self.member_info_list[i]:SetData(member_info_list[i])
        else
            self.member_info_list[i]:SetData({})
        end
    end
    self.node_list.add_xiayi_per.text.text = string.format(Language.NewTeam.AddXiaYiPer, Language.NewTeam.XiaYiPerCount[#member_info_list - robot_num] or 0)
    --界面名字为当前目标名
    local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    -- self.node_list.vip_remind_container:SetActive(VipWGData.Instance:GetVipLevel() < 6 and team_type == GoalTeamType.Exp_FuMoZhanChuan) --经验本
    local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
    local title_fuben_name = string.format(Language.NewTeam.PrepareTeamTypeName, now_goal_info.team_type_name)
    self.node_list["title_view_name"].text.text = string.format(Language.NewTeam.PrepareViewName, ToColorStr(title_fuben_name, COLOR3B.DEFAULT_NUM))

    local is_prepared = NewTeamWGData.Instance:GetSelfPrepareState()
    local prepare_timestamp = NewTeamWGData.Instance:GetPrepareTimeStamp()
    prepare_timestamp = prepare_timestamp or TimeWGCtrl.Instance:GetServerTime()
    self.prepare_remain_time = math.floor(prepare_timestamp - TimeWGCtrl.Instance:GetServerTime())

    if self.prepare_remain_time > 0 then
        if self.team_room_prepare_timer then
            GlobalTimerQuest:CancelQuest(self.team_room_prepare_timer)
        end
        self.team_room_prepare_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdatePrepreTime, self), 0.5)
        self:UpdatePrepreTime()
    end

    local is_fake_pipei = self.fake_pipie_ing and not self.fake_pipie_complete
    if is_fake_pipei then
        self.node_list["centen_text"].text.text = Language.NewTeam.FakePrepareIngText
    else
        self.node_list["centen_text"].text.text = Language.NewTeam.TruePrepareIngText
    end

    self.node_list["btn_prepare_text"].text.text = Language.NewTeam.PrepareBtnTextMember
    XUI.SetButtonEnabled(self.node_list["btn_prepare"], not (is_prepared or is_fake_pipei))

    local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
    local remain_count = total_count - cur_enter_count
    local xiezhu_times, total_xiezhu_times = NewTeamWGData.Instance:GetXieZhuTimesByTeamType(team_type)
    local color = remain_count > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
    self.node_list.remain_times.text.text = string.format(Language.NewTeam.RemainRewardTimes, color, remain_count, total_count)
    if remain_count <= 0 then
        --self.node_list.exp_add:SetActive(false)
        local is_can_xiezhu = GoalHasXieZhuType[team_type] or false
        self.node_list.xiezhu_times_container:SetActive(is_can_xiezhu)
        if is_can_xiezhu then
            local xiezhu_count = total_xiezhu_times - xiezhu_times -- 获取协助次数
            local color1 = xiezhu_count > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
            self.node_list.xiezhu_times.text.text = string.format(Language.NewTeam.RemainRewardTimes, color1, xiezhu_count, total_xiezhu_times)
            local reward_value = NewTeamWGData.Instance:GetXieZhuRewardValue(team_type)
            local item_config = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.VIRTUAL_ITEM_HORNOR)
            local str = ""
            if xiezhu_count > 0 then
                str = string.format(Language.NewTeam.HasXieZhuWuJiangLi, COMMON_CONSTS.VIRTUAL_ITEM_HORNOR, ITEM_COLOR[item_config.color], reward_value) --声望
            else
                str = Language.NewTeam.WuXieZhuWuJiangLi
            end
            EmojiTextUtil.ParseRichText(self.node_list.tips.emoji_text, str, 20, COLOR3B.RED)
        end
    else
        self.node_list.xiezhu_times_container:SetActive(false)
        --self.node_list.exp_add:SetActive(true)
        -- local exp_add = NewTeamWGData.Instance:GetExpAddIgnoreScene() * 10
        local exp_add = 0
        if not is_fake_pipei then
            exp_add = NewTeamWGData.Instance:GetRoomExpAdd() * 10
        end

        self.node_list.exp_add_value.text.text = string.format(Language.NewTeam.EXP_Add2, exp_add)
        EmojiTextUtil.ParseRichText(self.node_list.tips.emoji_text, Language.NewTeam.EXP_max_desc, 20, COLOR3B.RED)
    end

    local fb_combine_type = self:GetFBCombinType()
    if fb_combine_type then
        local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
        local role_level = GameVoManager.Instance:GetMainRoleVo().level
        local pre_show_level = combine_cfg[fb_combine_type+1].pre_show_level
        self.node_list["layout_combine_mark_root"]:SetActive(role_level >= pre_show_level)
        local hook_type = FuBenWGData.Instance:GetCombineStatus(fb_combine_type)
        self.node_list["layout_combine_hook"]:SetActive(hook_type == 1)
    else
        self.node_list["layout_combine_mark_root"]:SetActive(false)
    end

end

function TeamPrepareView:GetFBCombinType()
    local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    local fb_combine_type = nil
    if team_type == GoalTeamType.Exp_DuJieXianZhou then             -- 渡劫仙舟
        fb_combine_type = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
    elseif team_type == GoalTeamType.YuanGuXianDian then            -- 远古仙殿
        fb_combine_type = FB_COMBINE_TYPE.YUANGU
    elseif team_type == GoalTeamType.Exp_FuMoZhanChuan then         -- 伏魔战船
        fb_combine_type = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
    -- elseif team_type == GoalTeamType.ManHuangGuDian then            -- 蛮荒古殿
    --    fb_combine_type = FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN
    end
    return fb_combine_type
end


-- 倒计时
-- 变更:按钮文本倒计时-->上方进度倒计时
function TeamPrepareView:UpdatePrepreTime()
    local time = math.floor(self.prepare_remain_time)

    local member_info_list = NewTeamWGData.Instance:GetRoomMemberList()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local show_num = 0
    for k,v in pairs(member_info_list) do
        if server_time >= v.show_time then
            show_num = show_num + 1
        end
    end
 
    self.node_list.remain_time_text.text.text = time .. "s"
    self.node_list.progress_fill_img.image.fillAmount = self.prepare_remain_time / 20
    self.prepare_remain_time = self.prepare_remain_time - 0.5
    if self.prepare_remain_time <= 0 then
        self:ComletePrepreTime()
    end
end

function TeamPrepareView:ComletePrepreTime()
    if self.team_room_prepare_timer then
        GlobalTimerQuest:CancelQuest(self.team_room_prepare_timer)
        self.team_room_prepare_timer = nil
    end
    self:Close()
end

function TeamPrepareView:GetGuideUiCallBack(ui_name, ui_param)
    if ui_name == GuideUIName.TeamPrepareBtn then
        if FunctionGuide.Instance:GetCurrentGuideName() == "jiuyoumoku2" then
            local time = TimeWGCtrl.Instance:GetServerTime()
            local guide_cfg = FunctionGuide.Instance:GetGuideCfg()[33]
            if guide_cfg then
                FuBenPanelWGData.Instance:SetFuBenJiangHuGuideFlag(guide_cfg.trigger_param .. "|" .. time .. "|33")
            end
        end
        return self.node_list["btn_prepare"], BindTool.Bind(self.OnClickPrepare, self)
    end
    return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function TeamPrepareView:OnClickCombine()
    local fb_combine_type = self:GetFBCombinType()
    if not fb_combine_type then return end
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = combine_cfg[fb_combine_type+1].level_limit
    if need_level > role_level then
        local level_des = RoleWGData.GetLevelString(need_level)
        local str = string.format(Language.FuBenPanel.CombineLimitTips,level_des)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    local enter_dj_times = 0
    if fb_combine_type == FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or fb_combine_type == FB_COMBINE_TYPE.WUJINJITAN then
        local other_cfg = WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
        local yet_enter_time = WuJinJiTanWGData.GetFBEnterTimes()
        local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
        local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
        enter_dj_times = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
    elseif fb_combine_type == FB_COMBINE_TYPE.YUANGU then
        local hte_fb_times,_ = TeamEquipFbWGData.Instance:GetHTEFbTimes()
        enter_dj_times = hte_fb_times
    end

    if enter_dj_times < 2 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
        return
    end
    local vas = FuBenWGData.Instance:GetCombineStatus(fb_combine_type) == 1--self.node_list.layout_combine_hook:GetActive()
    local is_combine = vas == true and 0 or 1
    if vas then
        FuBenWGCtrl.Instance:SendFBUseCombine(is_combine, fb_combine_type)
    else
        local callback_func = function()
            --self.node_list.layout_combine_hook:SetActive(true)
        end
        FuBenWGCtrl.Instance:ShowCombinePanel(fb_combine_type,callback_func)
    end
end

-------------------------------------------------------------------------------------------
--- TeamPrepareMemberInfoRender
-------------------------------------------------------------------------------------------

TeamPrepareMemberInfoRender = TeamPrepareMemberInfoRender or BaseClass(BaseRender)
function TeamPrepareMemberInfoRender:__init()
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end
function TeamPrepareMemberInfoRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
    if self.timer_quest then
        GlobalTimerQuest:CancelQuest(self.timer_quest)
        self.timer_quest = nil
    end

    if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
    end
end
function TeamPrepareMemberInfoRender:OnFlush()
    if not self.data then return end
    local is_empty_data = IsEmptyTable(self.data)

	--没有队员信息，隐藏相关信息
    local is_fake_pipei = self.data.fake_pipei or false
	self.node_list.no_data_hide:SetActive((not is_empty_data and self.data.role_original_id and self.data.role_original_id > 0) or not is_fake_pipei)
    self.node_list.no_data_active:SetActive(is_empty_data or is_fake_pipei)
    if self.timer_quest then
        GlobalTimerQuest:CancelQuest(self.timer_quest)
        self.timer_quest = nil
    end
    if is_empty_data then return end

	self.node_list.leader_img:SetActive(self.data.is_leader == 1) --队长标记
    --self.node_list.xiezhu_img:SetActive(self.data.is_helper == 1 and self.data.is_leader ~= 1)
    self.node_list.xiezhu_img:SetActive(self.data.is_fenshen == 1) -- 分身tag
    if self.data.role_original_id and self.data.role_original_id > 0 then
        local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
        if self.data.fbroom_ready == 1 or (self.data.is_robert and self.data.is_robert == 1) then
            self.node_list.prepared_img.gameObject:SetActive(true)
            self.node_list.prepare_state_text.gameObject:SetActive(false)
            --self.node_list.prepare_state_text.text.text = Language.NewTeam.StateHasPrepare
        else
            self.node_list.prepared_img.gameObject:SetActive(false)
            self.node_list.prepare_state_text.gameObject:SetActive(true)
            self.node_list.prepare_state_text.text.text = string.format(Language.NewTeam.StateWaitPrepareDot,"")
            self.update_prepare_format = Language.NewTeam.StateWaitPrepareDot
            self.timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdatePrepareStateText,self), 0.5)
        end
        --人物名字
        if GoalCrossType[team_type] then
            -- 角色名字
            self.node_list["role_name"].text.text = self.data.name

            --内网无法请求PHP，先设置默认名字
            local server_id = self.data.server_id
            server_id = server_id > 0 and server_id or RoleWGData.Instance:GetMergeServerId()
            local temp_name = string.format(Language.WorldServer.ServerDefName, server_id)
            local server_name_str = LoginWGData.Instance:GetServerName(server_id)
            self.node_list["server_name"].text.text = string.format(Language.NewTeam.NewServerName, temp_name, server_name_str)
        else
            self.node_list.role_name.text.text = self.data.name
        end

        -- Vip等级
        -- self.node_list.vip_level:SetActive(self.data.vip_level > 0)
        -- if self.data.vip_level > 0 then
        --     local bundle, asset = ResPath.GetVipIcon("vip"..self.data.vip_level)
        --     self.node_list.vip_level.image:LoadSpriteAsync(bundle, asset, function ()
        --         self.node_list.vip_level.image:SetNativeSize()
        --     end)
        -- end

        -- [A3]注释 VIP
        -- self.node_list.vip_level.text.text = self.data.vip_level > 0 and ("V" .. self.data.vip_level) or ""

        --人物等级
        local is_dianfeng, _ = RoleWGData.Instance:GetDianFengLevel(self.data.level)
        local str = string.format(Language.NewTeam.PTLevel, self.data.level)
        EmojiTextUtil.ParseRichText(self.node_list["role_level"].emoji_text, str, 18, COLOR3B.WHITE)
        --self.node_list["role_level"].rect.anchoredPosition = Vector2(210, is_dianfeng and -19 or -19)

        ----头像
        local data = {}
        if self.data.is_robert and self.data.is_robert == 1 then
            data.role_id = 0
        else
            data.role_id = self.data.role_original_id
        end
        data.prof = self.data.prof
        data.sex = self.data.sex
        data.fashion_photoframe = self.data.shizhuang_photoframe
        self.node_list["head_cell"]:SetActive(true)
        self.head_cell:SetImgBg(true)
        self.head_cell:SetData(data)
        self.node_list["head_fake_bg"]:SetActive(false)
    else
        self.text_dot_num = 1
        self.node_list.prepare_state_text.text.text = string.format(Language.NewTeam.StatePrepareDot,"")
        self.update_prepare_format = Language.NewTeam.StatePrepareDot
        self.timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdatePrepareStateText,self), 0.5)
        self.node_list["head_cell"]:SetActive(false)
        
        self.head_cell:SetImgBg(false)
        self.node_list["head_fake_bg"]:SetActive(true)
    end

    local server_time = TimeWGCtrl.Instance:GetServerTime()

    if server_time >= self.data.show_time then
        self.node_list.root:SetActive(true)
        self.node_list.show_info_panel:SetActive(true)
        self.node_list.wait_info_panel:SetActive(false)
    else
        if server_time >= self.data.last_show_time then
            self.node_list.root:SetActive(true)
            self.node_list.show_info_panel:SetActive(false)
            self.node_list.wait_info_panel:SetActive(true)
        else
            self.node_list.root:SetActive(false)
        end

        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end

        self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.ShowInfo, self), 0.5)
    end
end

function TeamPrepareMemberInfoRender:ShowInfo()
    if IsEmptyTable(self.data) then
        return
    end

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    if server_time >= self.data.show_time then
        self.node_list.root:SetActive(true)
        self.node_list.show_info_panel:SetActive(true)
        self.node_list.wait_info_panel:SetActive(false)

        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end
    else
        if server_time >= self.data.last_show_time then
            self.node_list.root:SetActive(true)
            self.node_list.show_info_panel:SetActive(false)
            self.node_list.wait_info_panel:SetActive(true)
        else
            self.node_list.root:SetActive(false)
        end
    end
end

-- 刷新三点
function TeamPrepareMemberInfoRender:UpdatePrepareStateText()
    if self.text_dot_num == nil or self.text_dot_num > 3 then
        self.text_dot_num = 0
    end
    
    local dot_str = ""
    if self.text_dot_num >= 1 then
        for i=1,self.text_dot_num do
            dot_str = dot_str .. "."
        end
    end
    self.text_dot_num = self.text_dot_num + 1
    if self.update_prepare_format then
        self.node_list.prepare_state_text.text.text = string.format(self.update_prepare_format,dot_str)
    end
end