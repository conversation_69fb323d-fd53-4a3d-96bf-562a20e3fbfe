FlagGrabbingBattleFieldRewardView = FlagGrabbingBattleFieldRewardView or BaseClass(SafeBaseView)

function FlagGrabbingBattleFieldRewardView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 5), sizeDelta = Vector2(738, 644)})
    self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_person_reward_view")
end

function FlagGrabbingBattleFieldRewardView:LoadCallBack()
    if not self.fgb_person_reward_list then
		self.fgb_person_reward_list = AsyncListView.New(FGBPersonRewardItemRender, self.node_list.fgb_person_reward_list)
		self.fgb_person_reward_list:SetStartZeroIndex(true)
	end
end

function FlagGrabbingBattleFieldRewardView:ReleaseCallBack()
	if self.fgb_person_reward_list then
		self.fgb_person_reward_list:DeleteMe()
		self.fgb_person_reward_list = nil
	end

	self.is_score_reward = nil
end

function FlagGrabbingBattleFieldRewardView:SetData(is_score_reward)
	self.is_score_reward = is_score_reward
end

function FlagGrabbingBattleFieldRewardView:OnFlush()
	local panel_name = self.is_score_reward and Language.FlagGrabbingBattlefield.FGBScorePanelTitle or Language.FlagGrabbingBattlefield.FGBKillPanelTitle
	self.node_list.title_view_name.text.text = panel_name
	
	local title_cfg = self.is_score_reward and Language.FlagGrabbingBattlefield.FGBScoreRewardPanelTitle or Language.FlagGrabbingBattlefield.FGBKillRewardPanelTitle
	for i = 1, 2 do
		self.node_list["title" .. i].text.text = title_cfg[i] or ""
	end

	local data_list = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBShowRewardList(self.is_score_reward)
	self.fgb_person_reward_list:SetDataList(data_list)
end

-------------------------------------FGBPersonRewardItemRender---------------------------------------
FGBPersonRewardItemRender = FGBPersonRewardItemRender or BaseClass(BaseRender)

function FGBPersonRewardItemRender:LoadCallBack()
	if not self.ph_reward_rank_list then
		self.ph_reward_rank_list = AsyncListView.New(ItemCell, self.node_list.ph_reward_rank_list)
		self.ph_reward_rank_list:SetStartZeroIndex(true)
	end
end

function FGBPersonRewardItemRender:ReleaseCallBack()
	if self.ph_reward_rank_list then
		self.ph_reward_rank_list:DeleteMe()
		self.ph_reward_rank_list = nil
	end
end

function FGBPersonRewardItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	
	local is_score = nil ~= self.data.need_score
	self.node_list.score.text.text = is_score and self.data.need_score or self.data.need_kill
	self.ph_reward_rank_list:SetDataList(self.data.reward_item)
end