local BEASTS_STAR_VIRTUAL_ITEM = {
    [1] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_ONE,
    [2] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWO,
	[3] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_THREE,
	[4] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_FOUR,
	[5] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_FIVE,
	[6] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_SIX,
	[7] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_SEVEN,			--任意7星
	[8] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_EIGHT,			--任意8星
	[9] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_NINE,			--任意9星
	[10] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TEN,			--任意10星
	[11] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_ELEVEN,		--任意11星
	[12] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWELVE,		--任意12星
	[13] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_THIRTEEN,		--任意13星
	[14] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_FOURTEEN,		--任意14星
	[15] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_FIFTEEN,		--任意15星
	[16] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_SIXTEEN,		--任意16星
	[17] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_SEVENTEEN,		--任意17星
	[18] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_EIGHTEEN,		--任意18星
	[19] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_NINETEEN,		--任意19星
	[20] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY,		--任意20星
	[21] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_ONE,	--任意21星
	[22] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_TWO,	--任意23星
	[23] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_THREE,	--任意22星
	[24] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_FOUR,	--任意24星
	[25] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_FIVE,	--任意25星
}


ControlBeastsEvolvePreview = ControlBeastsEvolvePreview or BaseClass(SafeBaseView)
function ControlBeastsEvolvePreview:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
    self.view_name = "ControlBeastsEvolvePreview"
	local bundle_name = "uis/view/control_beasts_ui_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(814, 580)})
    self:AddViewResource(0, bundle_name, "layout_beast_evolve_preview")
	self.beast_id = nil
end

function ControlBeastsEvolvePreview:SetCurShowBeastsId(beast_id)
	self.beast_id = beast_id
end

function ControlBeastsEvolvePreview:LoadCallBack()
	--- 初始化三个位置
    self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName11
	
	if not self.item_list then
		self.item_list = AsyncListView.New(BeastStarUpRender, self.node_list.ph_item_list)
	end
end

function ControlBeastsEvolvePreview:ReleaseCallBack()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

	self.beast_id = nil
end

function ControlBeastsEvolvePreview:OnFlush(param_t)
	if not self.beast_id then
		return
	end
	
	local show_list = ControlBeastsWGData.Instance:GetBeastsTypeList(self.beast_id)
	self.item_list:SetDataList(show_list)
	self.node_list.ph_item_list:SetActive(show_list and #show_list > 0)
	self.node_list.img_no_record:SetActive(show_list == nil or #show_list <= 0)
end

--=================================================================================
BeastStarUpRender = BeastStarUpRender or BaseClass(BaseRender)
function BeastStarUpRender:LoadCallBack()
	self.cell_list = {}
	for i = 1, 4 do
		self.cell_list[i] = ItemCell.New(self.node_list["content"])
	end
    self.target_item = ItemCell.New(self.node_list["target_item"])
    -- self.must_item = ItemCell.New(self.node_list["must_item"])

	XUI.AddClickEventListener(self.node_list.skill_show, BindTool.Bind2(self.SkillTips, self))
end

-- 技能提示
function BeastStarUpRender:SkillTips()
	if not self.data then
		return
	end

	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.beast_id)
	local starup_beast_id = beast_cfg and beast_cfg.starup_beast_id or 0
	if starup_beast_id == 0 then
		return
	end

	local starup_beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(starup_beast_id)
	local star_show_skill = starup_beast_cfg and starup_beast_cfg.star_show_skill or 0

	if star_show_skill > 0 then
		if star_show_skill ~= 3 then
			ControlBeastsWGData.Instance:ShowBeastSkill(self.data.beast_id, star_show_skill == 2)
		else
			--专属技能
			local be_skill_cfg = ControlBeastsWGData.Instance:GetBeastBeSkillCfgBySeq(starup_beast_cfg.be_skill_id)
			if be_skill_cfg ~= nil then
				local show_data = {
					icon = be_skill_cfg.skill_icon,
					top_text = be_skill_cfg.skill_name,
					body_text = be_skill_cfg.skill_des,
					x = 0,
					y = -120,
					set_pos = true,
					hide_next = true,
					skill_level = starup_beast_cfg and starup_beast_cfg.be_skill_level or 0,
					is_active_skill = false,
					passive_str = Language.Common.ZhuanShu
				}
				NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
			end
		end
	end
end

function BeastStarUpRender:__delete()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
    end

    if self.target_item then
        self.target_item:DeleteMe()
        self.target_item = nil
    end

    -- if self.must_item then
    --     self.must_item:DeleteMe()
    --     self.must_item = nil
    -- end
end

function BeastStarUpRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.equal:SetActive(self.index ~= 1)
	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.beast_id)
	local starup_beast_cfg = nil
	local star_show_skill = 0

	if beast_cfg and beast_cfg.starup_beast_id then
        starup_beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_cfg.starup_beast_id)
        if starup_beast_cfg then
            local target = {}
            target.item_id = starup_beast_cfg.beast_id
            self.target_item:SetData(target)
			star_show_skill = starup_beast_cfg.star_show_skill or 0 
			self.node_list.target_item_star_txt.text.text = string.format(Language.Common.StarStr, CommonDataManager.GetDaXie(starup_beast_cfg.beast_star or 0))
        end
    end

	-- local must = {}
    -- must.item_id = self.data.beast_id
    -- must.is_egg = false
    -- must.is_beast = false
    -- self.must_item:SetData(must)

	local same_star_num = beast_cfg.starup_beast_num or 0
	-- self.node_list.plus:CustomSetActive(same_star_num > 0)
	self.node_list.skill_show:CustomSetActive(star_show_skill > 0)

	local starup_beast_ids = ControlBeastsWGData.Instance:GetBeastStarupCfgById(self.data.beast_id)
	for i = 1, 4 do
		self.cell_list[i]:SetActive(starup_beast_ids[i] ~= nil) 

		if starup_beast_ids[i] ~= nil then
			local star_up_data = starup_beast_ids[i]

			if star_up_data.beast_id ~= nil then
				self.cell_list[i]:SetData({item_id = star_up_data.beast_id, is_beast = false, num = star_up_data.num})

				local star_up_data_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(star_up_data.beast_id)
				if star_up_data_cfg ~= nil then
					self.cell_list[i]:SetBeastStarMessage(star_up_data_cfg.beast_star or 1)
				end
			elseif star_up_data.star ~= nil then
				self.cell_list[i]:SetData({item_id = BEASTS_STAR_VIRTUAL_ITEM[star_up_data.star], is_beast = false, num = star_up_data.num})
				self.cell_list[i]:SetBeastStarMessage(star_up_data.star)

				if star_up_data.element then
					self.cell_list[i]:SetBeastElementMessage(star_up_data.element)
				end
			elseif star_up_data.chip_id ~= nil then
				self.cell_list[i]:SetData({item_id = star_up_data.chip_id, is_beast = false, num = star_up_data.num})
			end
		end
	end

	if star_show_skill > 0 and starup_beast_cfg ~= nil then
		if star_show_skill ~= 3 then
			local skill_id = star_show_skill == 1 and starup_beast_cfg.skill_id or starup_beast_cfg.normal_skill_id
			if skill_id ~= 0 then
				--去技能数据类查
				local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
		
				if client_cfg then
					self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
				end
			end
		else
			--专属技能
			local be_skill_cfg = ControlBeastsWGData.Instance:GetBeastBeSkillCfgBySeq(starup_beast_cfg.be_skill_id)
			if be_skill_cfg ~= nil then
				self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(be_skill_cfg.skill_icon))
			end
		end
	end
end
