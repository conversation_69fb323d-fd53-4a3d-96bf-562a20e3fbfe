TianShenShuangShengPreview = TianShenShuangShengPreview or BaseClass(SafeBaseView)

function TianShenShuangShengPreview:__init()
	self:AddViewResource(0, "uis/view/tianshen_shuangsheng_prefab", "layout_tianshen_shuangsheng_preview")
	self:SetMaskBg(true)
end

function TianShenShuangShengPreview:__delete()

end

function TianShenShuangShengPreview:LoadCallBack()
    -- 初始化3出战
    if not self.display_list then
        self.display_list = {}
        
        for i = 1, 4 do
            local display_obj = self.node_list.displayroot:FindObj(string.format("display_%d", i))
            if display_obj then
                local cell = ShuangShengDisplayRender.New(display_obj)
                cell:SetIndex(i)
                self.display_list[i] = cell
            end
        end
    end

	self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.OnClinkClose, self))
end

function TianShenShuangShengPreview:ReleaseCallBack()
	if self.display_list and #self.display_list > 0 then
		for _, display_cell in ipairs(self.display_list) do
			display_cell:DeleteMe()
			display_cell = nil
		end

		self.display_list = nil
	end

    self.show_data = nil
end


function TianShenShuangShengPreview:SetData(show_data)
	self.show_data = show_data
	self:Open()
end

function TianShenShuangShengPreview:OnFlush()
    if not self.show_data or (not self.show_data.list) then
        return
    end

    for i, display_cell in ipairs(self.display_list) do
        if display_cell and self.show_data.list[i] then
            display_cell:SetTianShenIndex(self.show_data.tianshen_index)
            display_cell:SetData(self.show_data.list[i])
        end
    end
end

function TianShenShuangShengPreview:OnClinkClose()
	self:Close()
end

----------------------------------双生神灵模型item-----------------------
ShuangShengDisplayRender = ShuangShengDisplayRender or BaseClass(BaseRender)
function ShuangShengDisplayRender:LoadCallBack()
    if self.show_model == nil then
		self.show_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["ph_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        
        self.show_model:SetRenderTexUI3DModel(display_data)
		-- self.show_model:SetUI3DModel(self.node_list["ph_display"].transform, self.node_list["ph_display"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
	end

    self.model_res_id = nil
    self.tianshen_index = nil
end

function ShuangShengDisplayRender:__delete()
    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    self.model_res_id = nil
    self.tianshen_index = nil
end

function ShuangShengDisplayRender:SetTianShenIndex(tianshen_index)
    self.tianshen_index = tianshen_index
end

function ShuangShengDisplayRender:OnFlush()
    if not self.data then return end
    if not self.tianshen_index then return end

	local star_level = self.data or 0
	local lv_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarLvCfgData(self.tianshen_index, star_level)
	if lv_data then
		local app_image_id_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(lv_data.avatar_id)
        local preview_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarPreviewShowData(lv_data.avatar_id)

        if preview_data then
            if preview_data.pos then
                self.node_list["ph_display"]:SetLocalPosition(preview_data.pos.x, preview_data.pos.y, preview_data.pos.z)
            end

            if preview_data.preview_scale then
                self.node_list["ph_display"].transform.localScale = Vector3(preview_data.preview_scale, preview_data.preview_scale, preview_data.preview_scale)
            end

            if preview_data.rot then
                self.node_list["ph_display"].transform.localRotation = Quaternion.Euler(preview_data.rot.x, preview_data.rot.y, preview_data.rot.z)
            end
        end

		if app_image_id_data then
			self.node_list.name_text.text.text = app_image_id_data.name
            self.node_list.desc_text.text.text = app_image_id_data.description

			if self.model_res_id ~= app_image_id_data.appe_image_id then
                local bundle, asset = ResPath.GetShuangShengModelUI(app_image_id_data.appe_image_id)
                self.show_model:SetMainAsset(bundle, asset)
                self.show_model:PlayRoleAction(SceneObjAnimator.Rest, nil, true)
			end
		end
	end
end

