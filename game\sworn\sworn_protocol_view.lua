SwornProtocolView = SwornProtocolView or BaseClass(SafeBaseView)

function SwornProtocolView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/sworn_ui_prefab", "sworn_protocol")
end

function SwornProtocolView:LoadCallBack()
	self:FlushProtocolDesc()
	self.node_list.btn_sure.event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnPointerUp, self))
	self.node_list.btn_sure.event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnPointerDown, self))
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.OnClickCloseBtn, self))
end

function SwornProtocolView:ShowIndexCallBack()
	self.node_list.fingerprint.image.fillAmount = 0
end

function SwornProtocolView:ReleaseCallBack()
	self:CleanTimer()
end

function SwornProtocolView:OnFlush(param_t, index)
end

function SwornProtocolView:FlushProtocolDesc()
	local sworn_cfg = SwornWGData.Instance:GetSwornOtherCfg()

    if not IsEmptyTable(sworn_cfg) then
        local item_cfg = ItemWGData.Instance:GetItemConfig(sworn_cfg.jieyi_item_id)

        if not IsEmptyTable(item_cfg) then
            local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
            local num = sworn_cfg.jieyi_item_num
			local time = TimeUtil.FormatSecondDHM8(sworn_cfg.jieyi_wait_time)

            self.node_list.protocol_desc.text.text = string.format(Language.Sworn.SwornProtocolDesc, num, name, time, name)
        end
    end

	self.node_list.promot.text.text = string.format(Language.Sworn.SwornProtocolPromot, SwornWGData.Instance:GetSwornKeyTime())
end

function SwornProtocolView:OnPointerUp()
	self:CleanTimer()
end

function SwornProtocolView:OnPointerDown()
	if self:CalculationSwornCost() then
		return
	end

	self:CleanTimer()
	local total_time = SwornWGData.Instance:GetSwornKeyTime()
    local interval = 0.02

	self.timer = CountDown.Instance:AddCountDown(total_time, interval,
		function(elapse_time, total_time)
			self.node_list.fingerprint.image.fillAmount = elapse_time / total_time
		end,
		function()
			self.node_list.fingerprint.image.fillAmount = 1
			self:CreateTeam()
		end
	)
end

function SwornProtocolView:CalculationSwornCost()
	local sworn_cfg = SwornWGData.Instance:GetSwornOtherCfg()
    local cost_item_id = sworn_cfg.jieyi_item_id
    local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

    if has_num < sworn_cfg.jieyi_item_num then
        TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
    end

	return has_num < sworn_cfg.jieyi_item_num
end

function SwornProtocolView:CleanTimer()
    if self.timer and CountDown.Instance:HasCountDown(self.timer) then
        CountDown.Instance:RemoveCountDown(self.timer)
        self.timer = nil
		self.node_list.fingerprint.image.fillAmount = 0
    end
end

function SwornProtocolView:CreateTeam()
	SwornWGCtrl.Instance:CreateOrJoinTeam()
	self:Close()
end

function SwornProtocolView:OnClickCloseBtn()
	SwornWGCtrl.Instance:SetBeInvuteState(false)
	self:Close()
end