YinianMagicView = YinianMagicView or BaseClass(SafeBaseView)

function YinianMagicView:__init()
	self.view_style = ViewStyle.Full
    self:SetMaskBg(false)
    self.is_safe_area_adapter = true

    self:AddViewResource(0, "uis/view/yinianmagic_prefab", "layout_magic_bg")
    self:AddViewResource(0, "uis/view/yinianmagic_prefab", "layout_magic")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function YinianMagicView:__delete()
end

function YinianMagicView:OpenCallBack()
    self.flush_jump = true
end

function YinianMagicView:LoadCallBack()
    if not self.magic_type_list then
		self.magic_type_list = AsyncListView.New(YinianMagicTypeRender, self.node_list["magic_type_list"])
		self.magic_type_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMagic<PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	end

    if not self.exp_reward_list then
		self.exp_reward_list = {}
        local node_num = self.node_list.exp_reward_group.transform.childCount
		for i = 0, node_num - 1 do
			self.exp_reward_list[i] = MagicExpRewardItem.New(self.node_list.exp_reward_group:FindObj("exp_reward_" .. i))
			self.exp_reward_list[i]:SetIndex(i)
		end
	end
    
    if nil == self.task_list then
        self.task_list = AsyncListView.New(YinianMagicTaskCell, self.node_list.task_list)
    end

    XUI.AddClickEventListener(self.node_list.chongzhi_btn, BindTool.Bind1(self.ClickChongZhi, self))
    XUI.AddClickEventListener(self.node_list.daily_buy_btn, BindTool.Bind1(self.OpenDailyBuyView, self))
    XUI.AddClickEventListener(self.node_list.xiulian_btn, BindTool.Bind1(self.OpenPurchaseView, self))
    XUI.AddClickEventListener(self.node_list.dujie_btn, BindTool.Bind1(self.ClickDuJie, self))
    XUI.AddClickEventListener(self.node_list.zhenqi_btn, BindTool.Bind1(self.OpenZhenQiView, self))
    self.node_list["btn_tip"].button:AddClickListener(BindTool.Bind(self.OnClickBtnTip, self))
    self.node_list.title_view_name.text.text = Language.YinianMagicView.YinianMagicTile

    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.YinianMagicView, self.get_guide_ui_event)
    self.select_type_data = nil
    self.flush_jump = true
end

function YinianMagicView:ReleaseCallBack()
    if self.magic_type_list then
		self.magic_type_list:DeleteMe()
		self.magic_type_list = nil
	end

    if self.exp_reward_list then
		for k, v in pairs(self.exp_reward_list) do
			v:DeleteMe()
		end
		self.exp_reward_list = nil
	end

    if self.task_list then
        self.task_list:DeleteMe()
        self.task_list = nil
    end

    if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.YinianMagicView, self.get_guide_ui_event)
        self.get_guide_ui_event = nil
    end

    if self.yoyo_tween then
        self.yoyo_tween:Kill()
        self.yoyo_tween = nil
    end

    self.select_type_data = nil
    self.flush_jump = false
end

function YinianMagicView:ShowIndexCallBack(index)

end

function YinianMagicView:GetGuideUiCallBack(ui_name, ui_param)
    if ui_name == GuideUIName.YiNianMagic then
        return self.node_list["zhenqi_btn"], BindTool.Bind1(self.OpenZhenQiView, self)
    end

    return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function YinianMagicView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
		if k == "all" then
            local show_info = YinianMagicWGData.Instance:GetTitleTypeInfo()
            if not IsEmptyTable(show_info) and self.magic_type_list then
                self.magic_type_list:SetDataList(show_info)
            end

            if v.jump_index then
                self.magic_type_list:JumpToIndex(v.jump_index)
            elseif self.flush_jump then
                local cur_level = YinianMagicWGData.Instance:GetCurLevel()
                self.magic_type_list:JumpToIndex(cur_level)
                self.flush_jump = false
            end

            self:FlushTaskInfo()
            self:FlushRewardInfo()
            self:FluhOtherInfo()
        elseif k == "flush_task" then
            self:FlushTaskInfo()
        end
    end

    local select_type = YinianMagicWGData.Instance:GetCurSelcetType()
    self.node_list["big_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ynsm_js_bg_" .. select_type))
    self.node_list["yuan_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ynsm_js_yuan_" .. select_type))

    self.node_list.mo_skelet:SetActive(select_type == 2)
    self.node_list.s_skelet:SetActive(select_type == 1)
    local effct_str = select_type == 1 and "UI_ryxh_r3a" or "UI_ryxh_y3a"
    local asset, bundle = ResPath.GetA2Effect(effct_str)
    self.node_list.bg_effect:ChangeAsset(asset, bundle)
end

function YinianMagicView:FlushTaskInfo()
    local all_task_data = YinianMagicWGData.Instance:GetAllTaskList()
    self.task_list:SetDataList(all_task_data)
end

function YinianMagicView:FlushRewardInfo()
    if self.select_type_data == nil then
        return
    end

    local reward_info = YinianMagicWGData.Instance:GetLevelReWardInfo(self.select_type_data.type, self.select_type_data.level)
    if not IsEmptyTable(reward_info) then
        for k, v in pairs(self.exp_reward_list) do
            v:SetData(reward_info[k])
        end
    end
end

function YinianMagicView:FluhOtherInfo()
    if self.select_type_data == nil then
        return
    end

    local exp_value = YinianMagicWGData.Instance:GetCurExpValue()
    local cur_level = YinianMagicWGData.Instance:GetCurLevel()
    local reward_info = YinianMagicWGData.Instance:GetLevelReWardInfo(self.select_type_data.type, self.select_type_data.level)
    local slider_value = YinianMagicWGData.Instance:GetCurExpProgress(self.select_type_data.type, self.select_type_data.level, exp_value)
    local max_exp = reward_info[#reward_info].need_exp
    self.node_list.exp_num.text.text = exp_value .. "/" .. max_exp
    self.node_list.exp_progress.slider.value = slider_value

    local other_cfg = YinianMagicWGData.Instance:GetOtherCfg()
    local level = YinianMagicWGData.Instance:GetPurchaseLevel()
    local cur_cfg = YinianMagicWGData.Instance:GetPurchaseCfgbyLevel(level)
    local tc_exp_add = cur_cfg and cur_cfg.exp_add_per or 0
    local tc_bless_add = cur_cfg and cur_cfg.bless_add_per or 0
    local add_exp_num = math.floor(other_cfg.chongzhi_add_exp * (1 + (tc_exp_add / 10000)))
    local add_bless_num = math.floor(other_cfg.chongzhi_add_bless * (1 + (tc_bless_add / 10000)))

    self.node_list.cz_exp_num.text.text = string.format(Language.YinianMagicView.CZExpAdd, add_exp_num, tc_exp_add / 100)
    self.node_list.cz_bless_num.text.text = string.format(Language.YinianMagicView.CzBlessAdd, add_bless_num, tc_exp_add / 100)

    local is_all_get = YinianMagicWGData.Instance:GetCuLevelRewardIsGet()
    local next_reward_info = YinianMagicWGData.Instance:GetLevelReWardInfo(self.select_type_data.type, self.select_type_data.level + 1)
    self.node_list.dujie_btn:SetActive(is_all_get and not IsEmptyTable(next_reward_info) and cur_level == self.select_type_data.level)
    local grade_red = YinianMagicWGData.Instance:GetGradeRemind()
    self.node_list.zhenqi_red:SetActive(grade_red)

    local bundle, asset = ResPath.GetRawImagesPNG("a2_ynsm_big_" .. self.select_type_data.type .. "_" .. self.select_type_data.level)
    self.node_list.title_img.raw_image:LoadSpriteAsync(bundle, asset)

    local chongzhi_exp, chongzhi_bless = YinianMagicWGData.Instance:GetChongZhiGetExpAndBless()
    if chongzhi_exp > 0 or chongzhi_bless > 0 then
        self.node_list.chongzhi_text.text.text = Language.YinianMagicView.ChongZhiStr[2]
        self.node_list.chongzhi_red:SetActive(true)
        self.node_list.chongzhi_value_bg:SetActive(true)
        self.node_list.chongzhi_value.text.text = string.format(Language.YinianMagicView.ChongZhiAddValue, chongzhi_exp, chongzhi_bless)
    else
        self.node_list.chongzhi_text.text.text = Language.YinianMagicView.ChongZhiStr[1]
        self.node_list.chongzhi_red:SetActive(false)
        self.node_list.chongzhi_value_bg:SetActive(false)
        self.node_list.chongzhi_value.text.text = ""
    end
end

function YinianMagicView:OnSelectMagicTypeHandler(item)
	if nil == item or nil == item.data or item.data == self.select_type_data then
		return
    end

    self.select_type_data = item.data
    self:FlushRewardInfo()
    self:FluhOtherInfo()
    self:PalyYoyoTween()
end

function YinianMagicView:ClickChongZhi()
    local chongzhi_exp, chongzhi_bless = YinianMagicWGData.Instance:GetChongZhiGetExpAndBless()
    if chongzhi_exp > 0 or chongzhi_bless > 0 then
        -- self:PlayFlyEffect()
        YinianMagicWGCtrl.Instance:SendYinianMagicRequest(YINIAN_MAGIC_OPERATE_TYPE.FETCH_CHONGZHI_EXP_BLESS)
    else
        ViewManager.Instance:CloseAll()
        RechargeWGCtrl.Instance:Open(RechargeView.TABINDEX.CZ)
    end
end

function YinianMagicView:PlayFlyEffect()
    local effct_str = self.select_type_data and self.select_type_data.type == 1 and "UI_ynsm2" or "UI_ynsm4"
    local bundle_name, asset_name = ResPath.GetEffectUi(effct_str)
    local camber_power = math.random(100, 200)
    local star_obj = self.node_list.effect_star_pos
	local end_obj = self.node_list.effect_end_pos
    TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.YinianMagicView, bundle_name,
    asset_name, star_obj, end_obj, DG.Tweening.Ease.OutCubic,
    1, nil, nil, math.random(3, 6), 200 ,nil, true, nil, nil, camber_power)
end

function YinianMagicView:OpenDailyBuyView()
    if self.select_type_data == nil then
        return
    end

	YinianMagicWGCtrl.Instance:OpenBuyView(self.select_type_data.type)
end

function YinianMagicView:OpenPurchaseView()
    YinianMagicWGCtrl.Instance:OpenPurchaseView()
end

function YinianMagicView:OpenZhenQiView()
    YinianMagicWGCtrl.Instance:OpenZhenQiView()
end

function YinianMagicView:ClickDuJie()
    if self.select_type_data == nil then
        return
    end

    local is_all_get = YinianMagicWGData.Instance:GetCuLevelRewardIsGet()
    local next_reward_info = YinianMagicWGData.Instance:GetLevelReWardInfo(self.select_type_data.type, self.select_type_data.level + 1)
    if is_all_get and not IsEmptyTable(next_reward_info) then
        YinianMagicWGCtrl.Instance:SendYinianMagicRequest(YINIAN_MAGIC_OPERATE_TYPE.UP_LEVE)
        -- local effct_str = self.select_type_data.type == 1 and "UI_ynsm_s3" or "UI_ynsm_m3"
		-- local bundle_name, asset_name = ResPath.GetEffectUi(effct_str)
		-- EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["effect_pos"].transform, 3)
    end
end

function YinianMagicView:OnClickBtnTip()
	RuleTip.Instance:SetContent(Language.YinianMagicView.RuleContent, Language.YinianMagicView.RuleTitle)
end

function YinianMagicView:PalyYoyoTween()
    if self.yoyo_tween == nil then
        local tween_root = self.node_list.title_img.rect
        self.yoyo_tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 20, 1):SetLoops(-1, DG.Tweening.LoopType.Yoyo):SetEase(DG.Tweening.Ease.Linear)
    end
end

-- 使用特效
function YinianMagicView:PlayUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true, pos = Vector2(0, 0),
						parent_node = self.node_list["effect_pos"]})
end

----------------YinianMagicTypeRender------------
YinianMagicTypeRender = YinianMagicTypeRender or BaseClass(BaseRender)

function YinianMagicTypeRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["lock"], BindTool.Bind(self.OnClicNoOpen, self))
end

function YinianMagicTypeRender:__delete()

end

function YinianMagicTypeRender:OnFlush()
	if not self.data then
		return
	end

    self.node_list.name.text.text = self.data.title
    local cur_level = YinianMagicWGData.Instance:GetCurLevel()
    self.node_list.lock:SetActive(cur_level < self.data.level)

    local is_red = YinianMagicWGData.Instance:GetLevelRewardRemind(self.data.level)
    local grade_red = false
    local task_red = false
    if cur_level == self.data.level then
        grade_red = YinianMagicWGData.Instance:GetGradeRemind()
        task_red = YinianMagicWGData.Instance:GetTaskRemind()
    end

    self.node_list.remind:SetActive(is_red or grade_red or task_red)
end

function YinianMagicTypeRender:OnSelectChange(is_select)
	if not self.data then
		return
	end

    self.node_list.select_hl:SetActive(is_select)
	self.node_list.nor_img:SetActive(not is_select)
end

function YinianMagicTypeRender:OnClicNoOpen()
    local cur_level = YinianMagicWGData.Instance:GetCurLevel()

	if self.data.level > cur_level then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YinianMagicView.Lock)
	end
end

-----------MagicExpRewardItem-----------
MagicExpRewardItem = MagicExpRewardItem or BaseClass(BaseRender)

function MagicExpRewardItem:LoadCallBack()
    if self.reward_cell == nil then
        self.reward_cell = ItemCell.New(self.node_list["reward_pos"])
    end

    self.node_list["click_get"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function MagicExpRewardItem:__delete()
	if self.reward_cell then
		self.reward_cell:DeleteMe()
		self.reward_cell = nil
	end
end

function MagicExpRewardItem:OnFlush()
    if not self.data then
		return
	end

    self.reward_cell:SetData(self.data.reward_item[0])
    self.node_list.exp_num.text.text = self.data.need_exp
    local cur_level = YinianMagicWGData.Instance:GetCurLevel()
    local exp_value = YinianMagicWGData.Instance:GetCurExpValue()
    local is_get = false
    if cur_level > self.data.level then
        is_get = true
    elseif cur_level ==  self.data.level then
        is_get = YinianMagicWGData.Instance:GetLevelRewardState(self.data.stage)
    end

    self.node_list.lingqu_flag:SetActive(is_get)
    self.node_list.click_get:SetActive(not is_get and exp_value >= self.data.need_exp and cur_level ==  self.data.level)
    self.node_list.red:SetActive(not is_get and exp_value >= self.data.need_exp and cur_level ==  self.data.level)
end

function MagicExpRewardItem:OnClickGetReward()
    if not self.data then
        return
    end
    local cur_level = YinianMagicWGData.Instance:GetCurLevel()
    if cur_level ~= self.data.level then
        return
    end

    local is_get = YinianMagicWGData.Instance:GetLevelRewardState(self.data.stage)
    local exp_value = YinianMagicWGData.Instance:GetCurExpValue()
    if not is_get and exp_value >= self.data.need_exp then
        YinianMagicWGCtrl.Instance:SendYinianMagicRequest(YINIAN_MAGIC_OPERATE_TYPE.LEVEL_REWARD, self.data.stage)
    end
end

--------------------------------任务格子----------------
YinianMagicTaskCell = YinianMagicTaskCell or BaseClass(BaseRender)

function YinianMagicTaskCell:__init()
    self.node_list["go_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo,self))
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetTaskReward,self))
end

function YinianMagicTaskCell:__delete()
end

function YinianMagicTaskCell:OnFlush()
    if not self.data then
        return
    end
 
    local level = YinianMagicWGData.Instance:GetPurchaseLevel()
    local cur_cfg = YinianMagicWGData.Instance:GetPurchaseCfgbyLevel(level)
    local tc_exp_add = cur_cfg and cur_cfg.exp_add_per or 0
    local tc_bless_add = cur_cfg and cur_cfg.bless_add_per or 0
    local add_exp_num = math.floor(self.data.add_exp * (1 + (tc_exp_add / 10000)))
    local add_bless_num = math.floor(self.data.add_bless * (1 + (tc_bless_add / 10000)))
    self.node_list.exp_add_num.text.text = string.format(Language.YinianMagicView.ExpAdd, add_exp_num, tc_exp_add / 100)
    self.node_list.bless_add_num.text.text = string.format(Language.YinianMagicView.BlessAdd, add_bless_num, tc_bless_add / 100)
    local count_str = self.data.progress_num_flag .. "/" .. self.data.target
    self.node_list.desc.text.text =  string.format(Language.YinianMagicView.TaskDesc, self.data.des, count_str)

    self.node_list.go_btn:SetActive(self.data.status == REWARD_STATE_TYPE.UNDONE)
    self.node_list.get_btn:SetActive(self.data.status == REWARD_STATE_TYPE.CAN_FETCH)
    self.node_list.have_get:SetActive(self.data.status == REWARD_STATE_TYPE.FINISH)
end

function YinianMagicTaskCell:OnClickGo()
    if self.data and self.data.open_panel ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
    end
end

function YinianMagicTaskCell:OnClickGetTaskReward()
    -- YinianMagicWGCtrl.Instance:PlayFlyEffect()
    YinianMagicWGCtrl.Instance:SendYinianMagicRequest(YINIAN_MAGIC_OPERATE_TYPE.TASK_REWARD, self.data.task_id)
end