-- 副本准备面板
--------------
NewTeamPrepareView = NewTeamPrepareView or BaseClass(SafeBaseView)
function NewTeamPrepareView:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_prepare")
	self:SetMaskBg()
	self.flag = false
	self.view_name = "test"
end

function NewTeamPrepareView:__delete()
end

function NewTeamPrepareView:ReleaseCallBack()
	if self.prepare_list then
		self.prepare_list:DeleteMe()
		self.prepare_list = nil
	end
	if self.prepare_bar then
		self.prepare_bar:DeleteMe()
		self.prepare_bar = nil
	end
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.NewTeamPrepareView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
end

function NewTeamPrepareView:OpenCallBack()
end

function NewTeamPrepareView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideModuleName.NewTeamPrepareView and self.node_list.btn_agree then
		return self.node_list.btn_agree, BindTool.Bind1(self.OnClickAgree, self)
	end
	return nil, nil
end

function NewTeamPrepareView:FlushPrepareView()
	if 0 ~= SocietyWGData.Instance:GetTeamFbEnterTimeStamp() then
		if 1 == #SocietyWGData.Instance:GetTeamMemberList() then
			SocietyWGCtrl.Instance:SendTeamReadyStateChange(1)
			return
		end
		if not self:IsOpen() then
			self:Open()
		else
			self:Flush()
		end
	elseif self:IsOpen() and not self.fb_type then
		self:Close()
	end
end

function NewTeamPrepareView:CloseCallBack(is_all)
	if CountDownManager.Instance:HasCountDown("prepare_time") then
		CountDownManager.Instance:RemoveCountDown("prepare_time")
	end
	self.node_list["lbl_time"].text.text = ("")

	if self.fb_type and self.fb_type == TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_SET_FIRST_TIME then
		FuBenWGCtrl.Instance:SendWuJinJiTanReq(TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_SET_FIRST_TIME)
		FuBenWGCtrl.Instance:SendWuJinJiTanReq(TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_FIRST_TIME)
	-- elseif self.fb_type == TEAM_EXP_TYPE.YUGUXIANDIAN_REQ_TYPE_SET_FIRST_TIME then
	-- 	FunctionGuide.Instance:StartNextStep()
	end
end

function NewTeamPrepareView:LoadCallBack()
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.node_list.title_view_name.text.text = ""
	self.prepare_list = AsyncListView.New(ParepareListItem, self.node_list["ph_prepare_list"])


	XUI.AddClickEventListener(self.node_list["btn_refuse"], BindTool.Bind1(self.OnClickRefuse, self))
	XUI.AddClickEventListener(self.node_list["btn_agree"], BindTool.Bind1(self.OnClickAgree, self))
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.NewTeamPrepareView, self.get_guide_ui_event)
end

function NewTeamPrepareView:OnCloseHandler()
	self.flag = true
	self:OnClickRefuse()
	self:Close()
end

function NewTeamPrepareView:UpdatePrepareTime(elapse_time, total_time)
	-- self.prepare_bar:SetPercent(100 * math.ceil(total_time - elapse_time) / 25)
	self.node_list.slider.slider.value = math.ceil(total_time - elapse_time) / 25
	self.node_list["lbl_time"].text.text = (TimeUtil.FormatSecond(math.ceil(total_time - elapse_time),2))
end

function NewTeamPrepareView:CompletePrepareTime(elapse_time, total_time)
	self:OnClickAgree()
	self.node_list["lbl_time"].text.text = (TimeUtil.FormatSecond(0,2))
	-- self.prepare_bar:SetPercent(0)
	self.node_list.slider.value = 0
end

function NewTeamPrepareView:OnClickRefuse()
	if self.fb_type and self.fb_type == TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_SET_FIRST_TIME then
		if self.ok_callback then
		--	self.ok_callback()
			FuBenWGCtrl.Instance:SendWuJinJiTanReq(TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_SET_FIRST_TIME)
			FuBenWGCtrl.Instance:SendWuJinJiTanReq(TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_FIRST_TIME)
			self.ok_callback = nil
			self.fb_type = nil
			self:Close()
			return
		end
	end
	SocietyWGCtrl.Instance:SendTeamReadyStateChange(0)
	if not self.flag then
		self:Close()
	end
end

function NewTeamPrepareView:OnClickAgree()
	if CountDownManager.Instance:HasCountDown("prepare_time") then
		CountDownManager.Instance:RemoveCountDown("prepare_time")
	end
	if self.fb_type then
		if self.fb_type == TEAM_EXP_TYPE.YUGUXIANDIAN_REQ_TYPE_SET_FIRST_TIME then
			--NewTeamWGData.Instance:SetExpFirstFirstEnter(true)
			FuBenWGCtrl.Instance:SendSpecialTempReq(FUBEN_TYPE.HIGH_TEAM_EQUIP,1,self.guid_member_list[2].name,self.guid_member_list[3].name)
		elseif self.fb_type == TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_SET_FIRST_TIME then
			FuBenWGCtrl.Instance:SendSpecialTempReq(FUBEN_TYPE.FBCT_WUJINJITAN,1,self.guid_member_list[2].name,self.guid_member_list[3].name)
		end
		if self.ok_callback then
			self.ok_callback()
			self.ok_callback = nil
			self.fb_type = nil
			self:Close()
			return
		end
	end
	SocietyWGCtrl.Instance:SendTeamReadyStateChange(1)
end

function NewTeamPrepareView:ShowIndexCallBack()
	if CountDownManager.Instance:HasCountDown("prepare_time") then
		CountDownManager.Instance:RemoveCountDown("prepare_time")
	end
	if self.fb_type then
		local time_stamp =  TimeWGCtrl.Instance:GetServerTime() + 25
		CountDownManager.Instance:AddCountDown("prepare_time", BindTool.Bind1(self.UpdatePrepareTime, self), BindTool.Bind1(self.CompletePrepareTime, self), time_stamp, nil, 1)
		self:Flush()
		XUI.SetButtonEnabled(self.node_list["btn_refuse"],false)
		return
	end
	self.flag = false
	local enter_time = SocietyWGData.Instance:GetTeamFbEnterTimeStamp()
	CountDownManager.Instance:AddCountDown("prepare_time", BindTool.Bind1(self.UpdatePrepareTime, self), BindTool.Bind1(self.CompletePrepareTime, self), enter_time, nil, 1)

	local member_list = __TableCopy(SocietyWGData.Instance:GetTeamMemberList())
	member_list[0] = table.remove(member_list, 1)
	--队长
	-- for k,v in pairs(member_list) do
	-- 	if v.role_id == RoleWGData.Instance.role_vo.role_id and 1 == SocietyWGData.Instance:GetIsTeamLeader() then
	-- 		-- self:OnClickAgree()
	-- 		XUI.SetButtonEnabled(self.node_list["btn_agree"],false)
	-- 	end
	-- end

	self:Flush()
end

function NewTeamPrepareView:OnFlush()
	local fb_name = NewTeamWGData.Instance:GetTeamFbNameByToTeamTypeAndMode()
	self.node_list["lbl_fb_name"].text.text = (fb_name)

	if self.fb_type then
		if self.fb_type == TEAM_EXP_TYPE.WUJINJITAN_REQ_TYPE_SET_FIRST_TIME then
			self.node_list["lbl_fb_name"].text.text = Language.FuBenPanel.TabSub2[1]
		elseif self.fb_type == TEAM_EXP_TYPE.YUGUXIANDIAN_REQ_TYPE_SET_FIRST_TIME then
			self.node_list["lbl_fb_name"].text.text = Language.FuBenPanel.YuGuXianDianYi
		end
		self.prepare_list:SetDataList(self.guid_member_list, 0)
		return
	end

	local member_list = __TableCopy(SocietyWGData.Instance:GetTeamMemberList())
	if #member_list <= 2 then
		self.node_list.ph_prepare_list.rect.anchoredPosition = Vector2(111,168.65)
	else
		self.node_list.ph_prepare_list.rect.anchoredPosition = Vector2(8,168.65)
	end
	self.prepare_list:SetDataList(member_list, 0)
end
function NewTeamPrepareView:GuildFirstEnterFbView(fb_type,call_back)
	self.fb_type = fb_type
	self.ok_callback = call_back
	self:InitRanDomName()
	self.guid_member_list = {}
	self.guid_member_list[1] = {}
	self.guid_member_list[1].role_id = RoleWGData.Instance.role_vo.role_id
	self.guid_member_list[1].prof = RoleWGData.Instance.role_vo.prof
	self.guid_member_list[1].name = RoleWGData.Instance.role_vo.name
	self.guid_member_list[1].is_leader = 1
	self.guid_member_list[1].jingjie_level = RoleWGData.Instance.role_vo.jingjie_level

	self.guid_member_list[2] = {}
	self.guid_member_list[2].role_id = -1
	self.guid_member_list[2].sex = GameEnum.FEMALE
	self.guid_member_list[2].name = self:RandomName(GameEnum.FEMALE)
	self.guid_member_list[2].jingjie_level = RoleWGData.Instance.role_vo.jingjie_level

	self.guid_member_list[3] = {}
	self.guid_member_list[3].role_id = -1
	self.guid_member_list[3].sex = GameEnum.MALE
	self.guid_member_list[3].name = self:RandomName(GameEnum.MALE)
	self.guid_member_list[3].jingjie_level = RoleWGData.Instance.role_vo.jingjie_level
	-- if self.fb_type == TEAM_EXP_TYPE.YUGUXIANDIAN_REQ_TYPE_SET_FIRST_TIME then
	-- 	local name1,name2 =  TeamEquipFbWGData.Instance:GetRobertName()
	-- 	self.guid_member_list[2].name = name1
	-- 	self.guid_member_list[3].name = name2
	-- end
	self:Open()
end

function NewTeamPrepareView:RandomName(select_sex)
	local first_list = self.first_list[select_sex]
	local last_list = self.last_list[select_sex]
	if nil == first_list or nil == last_list then
		return
	end

	local first_index = math.random(1, #first_list)
	local last_index = math.random(1, #last_list)
	local name = first_list[first_index] .. last_list[last_index]
	local isill = ChatFilter.Instance:IsIllegal(name, true)
	-- 存在敏感字
	if isill and self.re_count <= 7 then
		self.re_count = self.re_count + 1
		table.remove(first_list, first_index)
		table.remove(last_list, last_index)
		self:RandomName()
	else
		self.re_count = 0
		return name
	end
end

function NewTeamPrepareView:InitRanDomName()
	self.re_count = 0
	self.first_list = {}
	self.last_list = {}

	local name_cfg = ConfigManager.Instance:GetAutoConfig("randname_auto").random_name[1]
	if not name_cfg then
		return
	end

	local the_list_1 = {}
	local the_list_2 = {}
	the_list_1[GameEnum.FEMALE] = name_cfg.common_first
	the_list_2[GameEnum.FEMALE] = name_cfg.female_last
	the_list_1[GameEnum.MALE] = name_cfg.common_first
	the_list_2[GameEnum.MALE] = name_cfg.male_last

	self.first_list = __TableCopy(the_list_1)
	self.last_list = __TableCopy(the_list_2)
end
------------------itemRender-----------------
ParepareListItem = ParepareListItem or BaseClass(BaseRender)

function ParepareListItem:__init()
end

function ParepareListItem:__delete()

end

function ParepareListItem:OnFlush()
	if not self.data then
		return
	end
	-- if 1 == self.data.fbroom_read then
	-- 	local bundle,asset = ResPath.GetRoleHeadIconSociety(self.data.prof)
	-- 	self.node_list["default_head_icon"].image:LoadSprite(bundle,asset)
	-- 	self.node_list["default_head_icon"].image:SetNativeSize()
	-- 	-- AvatarManager.Instance:UpdateAvatarImg(self.node_list["default_head_icon"], self.data.role_id, self.data.prof, false, false)
	-- end

	--已确认
	if 1 == self.data.fbroom_read then
		self.node_list["enter_img_bg"]:SetActive(true)
	else
		self.node_list["enter_img_bg"]:SetActive(false)
	end

	--队长
	if 1 == self.data.is_leader then
		self.node_list["enter_img_bg"]:SetActive(false)
		self.node_list["captain"]:SetActive(true)
	else
		self.node_list["captain"]:SetActive(false)
	end
	if self.data.role_id <= 0 then
		local bundle1, asset1 = ResPath.GetNewHeadIcon(self.data.sex, 0)
		self.node_list.default_head_icon.image:LoadSprite(bundle1, asset1, function()
			self.node_list.default_head_icon.image:SetNativeSize()
			self.node_list.custom_head_icon:SetActive(false)
		end)
	else
		XUI.UpdateRoleHead(self.node_list["default_head_icon"],self.node_list["custom_head_icon"],self.data.role_id,self.data.sex,self.data.prof,nil,true,true)
	end


	self.node_list["lbl_role_name"].text.text = (self.data.name)

	local bundle, asset = ResPath.GetF2JingJie(self.data.jingjie_level)
	self.node_list.img_jingjie1.image:LoadSprite(bundle, asset, function()
		self.node_list.img_jingjie1.image:SetNativeSize()
	end)
end
