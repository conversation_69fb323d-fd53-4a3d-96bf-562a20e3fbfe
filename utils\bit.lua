local libbit = require("bit")
bit = {data32 = {}, data8 = {}}

for i = 1, 32 do
	bit.data32[i] = 2 ^ (32 - i)
end

for i = 1, 8 do
	bit.data8[i] = 2 ^ (8 - i)
end

-- number转table 1->8 高位->低位
function bit:d2b8(arg, tbl)
	local tr = tbl or {}
	for i = 1, 8 do
		if arg >= self.data8[i] then
			tr[i] = 1
			arg = arg - self.data8[i]
		else
			tr[i] = 0
		end
	end
	return  tr
end

-- number转table 8 -> 1 低位 -> 高位
local d2b8_two_data = {}
function bit:d2b8_two(arg, tbl)
	local tr = tbl or {}
	for k, v in ipairs(bit:d2b8(arg, d2b8_two_data)) do
		tr[8 - k] = v
	end

	return tr
end

-- number转table 1->32 高位->低位
function bit:d2b(arg, tbl)
	local tr = tbl or {}
	for i = 1, 32 do
		if arg >= self.data32[i] then
			tr[i] = 1
			arg = arg - self.data32[i]
		else
			tr[i] = 0
		end
	end
	return  tr
end

-- number转table 32 -> 1 低位 -> 高位
local d2b_two_data = {}
function bit:d2b_two(arg, tbl)
	local tr = tbl or {}
	for k, v in ipairs(bit:d2b(arg, d2b_two_data)) do
		tr[32 - k] = v
	end

	return tr
end

-- 【新】number转table  低位 -> 高位 （少一个循环）
function bit:d2b_l2h(arg, tbl, form_zero)
	local form_value = form_zero and 32 or 33
	local tr = tbl or {}
	for i = 1, 32 do
		if arg >= self.data32[i] then
			tr[form_value - i] = 1
			arg = arg - self.data32[i]
		else
			tr[form_value - i] = 0
		end
	end
	return tr
end

-- number转table 32 -> 1 低位 -> 高位
function bit:d2bsort(arg, list)
	local data = list or {}
	local index = #data + 1
	for i = index, index + 31 do
		if arg >= self.data32[i - index + 1] then
			data[2 * index + 31 - i] = 1
			arg = arg - self.data32[i - index + 1]
		else
			data[2 * index + 31 - i] = 0
		end
	end
	return data
end

-- table转number
function bit:b2d(arg)
	local nr = 0
	for i = 1, 32 do
		if arg[i] == 1 then
			nr = nr + 2 ^ (32 - i)
		end
	end
	return nr
end

-- table转number 从后面插数据
function bit:b2d_end(arg)
	local nr = 0
	for i = 0, 31 do
		if arg[i] == 1 then
			nr = nr + 2 ^ i
		end
	end
	return nr
end

-- long long转table 1->64 高位->低位
-- @high 高32位
-- @low 低32位
function bit:ll2b(high, low, tbl)
	local tr = tbl or {}
	tr = bit:d2b(low, tr)
	for i = 1, 32 do
		tr[i + 32] = tr[i]
	end

	tr = bit:d2b(high, tr)
	return tr
end

-- long long转table 0->63 低位->高位
function bit:ll2b_two(low, high, tbl)
	local tr = tbl or {}
	tr = bit:d2b_l2h(high, tr, true)
	for i = 0, 31 do
		tr[i + 32] = tr[i]
	end

	tr = bit:d2b_l2h(low, tr, true)
	return tr
end

-- table转long long
-- @return 高32位，低32位
function bit:b2ll(arg)
	local high_t, low_t = {}, {}

	for i = 1, 32 do
		high_t[i] = arg[i]
		low_t[i] = arg[i + 32]
	end

	return bit:b2d(high_t), bit:b2d(low_t)
end

-- 按位与
function bit:_and(a, b)
	return libbit.band(a, b)
end

-- 按位或
function bit:_or(a, b)
	return libbit.bor(a, b)
end

-- 按位取反
function bit:_not(a)
	return libbit.bnot(a)
end

-- 按位异或
function bit:_xor(a, b)
	return libbit.bxor(a, b)
end

-- 右移
function bit:_rshift(a, n)
	return libbit.rshift(a, n)
end

-- 左移
function bit:_lshift(a, n)
	return libbit.lshift(a, n)
end

-- 多少个1
function bit:d2b1n(arg)
	local count = 0
	for i = 1, 32 do
		if arg >= self.data32[i] then
			count = count + 1
			arg = arg - self.data32[i]
		end
	end
	return count
end