local flush_type =
{
	refresh = 1,
	refresh_all_act_cells = 2,
	update_one_cell = 3,
	update_select_state = 4,
}

local flush_type_list = {}
for k,v in pairs(flush_type) do
	flush_type_list[v] = k
end

QiWenYiWuGrid = QiWenYiWuGrid or BaseClass()
function QiWenYiWuGrid:__init()
	self.item_render = nil							-- 创建的item类型
	self.grid_name = ""								-- 格子名称
	self.data_list = {}								-- 格子数据列表
	self.create_callback = nil						-- 创建完成回调
	self.select_callback = nil						-- 选中某个格子回调

	self.first_time_load = true						-- 是否第一次加载

	self.cell_list = {}
	self.select_tab = {[1] = {}} 					-- 选择项保存表
	self.asset_bundle = nil
	self.asset_name = nil
	self.has_data_max_index = 0						--最大有数据的格子索引
	self.flush_param_t = {}
end

function Qi<PERSON><PERSON>YiWuGrid:__delete()
	for i, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
end

-- 设置数据源
function QiWenYiWuGrid:SetDataList(data)
	self.data_list = data
	self:__Flush(flush_type.refresh, {0, 0})
end

-- 创建网格 {t.asset_bundle, t.asset_name, t.list_view, t.columns}
--- t.itemRender 可为nil，默认为ItemCell
-- asset_bundle 和 asset_name 创建自己的预制物（不传默认创建ItemCell）
function QiWenYiWuGrid:CreateCells(t)
	if t.assetBundle and t.assetName then
		self.asset_bundle = t.assetBundle						-- 路径
		self.asset_name = t.assetName							-- 预制体名字
	end

	self.item_render = t.itemRender or ItemCell		-- render方法
	self.list_view = t.list_view					-- 需生成的东西的父节点
	self.columns = t.columns or 1					-- 多少列

	if self.first_time_load then
		local list_delegate = self.list_view.list_simple_delegate
		list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self)						-- 需要创建多少个group
		list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshListViewCells, self)						-- 刷新格子
		list_delegate.CellSizeDel = BindTool.Bind(self.GetGroupCellHigh, self)								
		self.first_time_load = false
	end

	if nil ~= self.create_callback then
		self.create_callback()
	end
end

-- 选中后回调
function QiWenYiWuGrid:SetSelectCallBack(callback)
	self.select_callback = callback
end

-- 设置创建完成回调函数(在执行CreateCells之前设置)
function QiWenYiWuGrid:SetCreateCallback(create_callback)
	self.create_callback = create_callback
end

-- 设置选中某个格子
function QiWenYiWuGrid:SetSelectCellSeq(seq)
	self.select_tab[1] = {}
	self.select_tab[1][seq] = true
	self:__Flush(flush_type.update_select_state)
end

--获得格子数
function QiWenYiWuGrid:GetListViewNumbers()
	return #self.data_list
end

--[[
	data = quality_type_list
	quality_type = 1、2、3、4、5、6、7、8 ...
	quality_type_list = {
		[quality_type] = {
			{is_remind = false},
			{is_remind = false},
			{is_remind = false},
		}
	}
]]
--刷新格子
function QiWenYiWuGrid:RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]
	if not item_cell then
		item_cell = QiWuItemRender.New(cell.gameObject)
		item_cell:SetSelectTab(self.select_tab)
		item_cell:SetAssetBundle(self.asset_bundle, self.asset_name)
		item_cell:SetItemRender(self.item_render)
		item_cell:SetClickCallBack(BindTool.Bind(self.SelectCellHandler, self))
		self.cell_list[cell] = item_cell
	end

	local new_index = cell_index + 1
	item_cell:SetIndex(new_index)
	item_cell:SetData(self.data_list[new_index])
	self:TrySelectSeq(new_index, item_cell)
end

local group_tilte_high = 30							-- 标题 高度
local child_cell_high = 218							-- cell 高度
local child_cell_spacing = 13						-- cell 之间间距
local group_spaicing = 15							-- group 之间间距
-- 单个group的高度
function QiWenYiWuGrid:GetGroupCellHigh(cell_index)
	local data = self.data_list[cell_index + 1]
	if not data then
		return 0
	end

	local rows = math.ceil(#data / self.columns)
	return group_tilte_high + rows * child_cell_high + child_cell_spacing * (rows - 1)
end

function QiWenYiWuGrid:GetAllGroupCellHigh()
	local group_num = #self.data_list
	local length = 0
	for k,v in pairs(self.data_list) do
		length = length + self:GetGroupCellHigh(k)
	end

	return length + (group_num - 1)
end

function QiWenYiWuGrid:GetChildCellPos(seq)
	local cfg = StrangeCatalogWGData.Instance:GetActPartItem(seq)

	if not cfg then
		return 0
	end

	local group_index = cfg.quality_type
	local data = self.data_list[group_index]
	if not data then
		return 0
	end

	local rows = math.ceil(#data / self.columns)
	local length = 0
	for i = 1, group_index - 1 do
		length = length + self:GetGroupCellHigh(i)
	end

	return length + child_cell_spacing * (rows - 1) + group_index * group_spaicing
end

-- 格子尝试选择
function QiWenYiWuGrid:TrySelectSeq(quality_type, select_cell)
	if nil == self.select_callback or nil == self.jump_to_select_seq then
		return
	end

	local qua_cfg = self.data_list[quality_type]
	local cfg = {}

	for k,v in pairs(qua_cfg) do
		if self.jump_to_select_seq == v.seq then
			cfg = v
			break
		end
	end

	if not cfg or quality_type ~= cfg.quality_type then
		return
	end

	local cell = select_cell:GetCellBySeq(self.jump_to_select_seq)
	if nil ~= cell then
		self:SelectCellHandler(cell)
	end

	self.jump_to_select_seq = nil
end

function QiWenYiWuGrid:JumptToPrecent(percent)
	self:__Flush(flush_type.refresh, {1, percent})
end

--跳转到具体cell
function QiWenYiWuGrid:JumpToSeq(seq)
	local min_length = 540
	local percent = 0
	local cell_pos = self:GetChildCellPos(seq)
	if cell_pos > min_length then
		local total_length = self:GetAllGroupCellHigh()
		percent = cell_pos / total_length
	end

	self:JumptToPrecent(percent)
end

--跳转到某个cell并且选中
function QiWenYiWuGrid:JumpToSeqAndSelect(seq)
	self.jump_to_select_seq = seq
	self:JumpToSeq(seq)
end

-- 选择某个格子回调
function QiWenYiWuGrid:SelectCellHandler(cell)
	local cell_data = cell:GetData()
	if not cell_data then
		return
	end

	local seq = cell_data.seq
	local is_select = false
    -- 能改变已选中效果
	if not self.select_tab[1][seq] then
		self.select_tab[1] = {}
		self.select_tab[1][seq] = true
		is_select = true
		self:__DoRefreshSelectState()
	end

	if nil ~= self.select_callback then
		self.select_callback(cell, is_select)
	end
end

function QiWenYiWuGrid:__DoRefresh(refresh_type, percent)
	if IsNil(self.list_view.scroller) then
		return
	end

	if self.list_view.scroller.isActiveAndEnabled then
		if refresh_type == 0 then
			self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
		elseif refresh_type == 1 then
			self.list_view.scroller:ReloadData(percent)
		elseif refresh_type == 2 then
			self.list_view.scroller:RefreshActiveCellViews()
		end
	end
end

function QiWenYiWuGrid:__DoUpdateOneCell(index, data)
	local cell = self:GetCell(index)
	if cell then
		cell:SetData(data)
	end
end

function QiWenYiWuGrid:__DoRefreshSelectState()
	for k,v in pairs(self.cell_list) do
		v:RefreshSelectState()
	end
end

function QiWenYiWuGrid:__Flush(key, value)
	self.flush_param_t[key] = value
	TryDelayCall(self, function ()
		self:__OnFlush()
	end, 0, "flush")
end

function QiWenYiWuGrid:__OnFlush()
	for i,_ in ipairs(flush_type_list) do
		local v = self.flush_param_t[i]
		if nil ~= v then
			if i == flush_type.refresh then
				self:__DoRefresh(v[1], v[2])
				self.flush_param_t[flush_type.refresh_all_act_cells] = nil
				self.flush_param_t[flush_type.update_one_cell] = nil
				self.flush_param_t[flush_type.update_select_state] = nil
			end

			if i == flush_type.refresh_all_act_cells then
				self:__DoRefresh(2, 0)
			end

			if i == flush_type.update_one_cell then
				self:__DoUpdateOneCell(v[1], v[2])
			end

			if i == flush_type.update_select_state then
				self:__DoRefreshSelectState()
			end
		end
	end

	self.flush_param_t = {}
end
