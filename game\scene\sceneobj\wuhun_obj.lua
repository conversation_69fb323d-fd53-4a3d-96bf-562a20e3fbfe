WuHunObj = WuHunObj or BaseClass(Character)

function WuHunObj:__init(vo, scene_type)
	self.obj_type = scene_type or SceneObjType.WuHunZhenShen
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.PartWuHunZhenShen

    self.owner_obj_id = vo.owner_obj_id
    self.owner_obj = vo.owner_obj
    self.wuhun_id = vo.wuhun_id
	self.wuhun_lv = vo.wuhun_lv

	self.appe_ring_image_id = nil
	self.wuhun_appid = nil

	self.bundle_name = ""
	self.asset_name = ""
	self.vo = vo



    self.effect_handle = nil
	self.is_using_skill = false
	self.cancel_quest_of_action = nil
	self.cancel_quest_of_normal_action = nil
	self.cancel_change_model = nil
	self.cancel_attach_action = nil

	self:InitInfo()
	self:InitAppearance()
end

function WuHunObj:__delete()
	self:RemoveModel(SceneObjPart.SoulFormation)
	if self.owner_obj then
		self.owner_obj:ReleaseWuHunObj()
	end

	self.appe_ring_image_id = nil
	self.wuhun_appid = nil

	self.owner_obj = nil
	self.is_using_skill = false
	self.wuhun_hunzhen_id = nil

	ReuseableHandleManager.Instance:ReleaseShieldHandle(self.effect_handle)
	self.effect_handle = nil
	
	self:RemoveActionDelayTime()
	self:RemoveChangeModelDelayTime()
	self:RemoveAttachMainDelayTime()
	self:RemoveNormalActionDelayTime()
end

function WuHunObj:DeleteDrawObj()
	if not self:IsRealDead() then
		Character.DeleteDrawObj(self)
		return
	end

	if nil ~= self.draw_obj then
		local draw_obj = self.draw_obj
		self.draw_obj = nil
        draw_obj:DeleteMe()
	end
end

function WuHunObj:InitInfo()
	Character.InitInfo(self)
	local wuhun_appid_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(self.wuhun_id)
	self.appe_ring_image_id = wuhun_appid_cfg and wuhun_appid_cfg.appe_ring_image_id or 0
end

-- 重写CheckModleScale方法 避免放大缩小给限制了
function WuHunObj:CheckModleScale()

end

function WuHunObj:ChangeWuHunImageId(wuhun_id, wuhun_lv)
	self.wuhun_id = wuhun_id
	self.wuhun_lv = wuhun_lv
	local wuhun_appid_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(self.wuhun_id)
	self.appe_ring_image_id = wuhun_appid_cfg and wuhun_appid_cfg.appe_ring_image_id or 0
	self:InitAppearance()
end


function WuHunObj:InitAppearance()
	self.load_priority = 5

	if self.obj_scale ~= nil then
		local transform = self.draw_obj:GetRoot().transform
		transform.localScale = Vector3(self.obj_scale, self.obj_scale, self.obj_scale)
	end

	local bundle, asset = ResPath.GetWuHunModel(self.appe_ring_image_id)
	self:InitModel(bundle, asset)
end

function WuHunObj:InitModel(bundle, asset)
	if bundle == nil or asset == nil then
		self:VisibleChanged(false)
		return
	else
		self:VisibleChanged(true)
	end
	self.bundle_name = bundle
	self.asset_name = asset
	self:ChangeModel(SceneObjPart.Main, bundle, asset, BindTool.Bind(self.AttachToRoleMainPart, self))
end

function WuHunObj:AttachToRoleMainPart(attachment_callback)
	if (not self.owner_obj) or (not self.owner_obj.draw_obj) or (not self.draw_obj) then
		return
	end

	if self.bundle_name == nil or self.bundle_name == "" or self.asset_name == nil or self.asset_name == "" then
		return
	end

	local main_part = self.owner_obj.draw_obj:GetPart(SceneObjPart.Main)
	local self_draw_obj = self.draw_obj:GetPart(SceneObjPart.Main).obj

	if (not main_part) or (not main_part.obj) or (not self_draw_obj) or (not self_draw_obj.attach_obj) then
		self:RemoveAttachMainDelayTime()
		self.cancel_attach_action = GlobalTimerQuest:AddDelayTimer(function ()
			self:AttachToRoleMainPart(attachment_callback)
		end, 0.4)
		return
	end

	local attachment = main_part.obj.actor_attachment

	if IsNil(attachment) then
		self:RemoveAttachMainDelayTime()
		self.cancel_attach_action = GlobalTimerQuest:AddDelayTimer(function ()
			self:AttachToRoleMainPart(attachment_callback)
		end, 0.4)
		return
	end

	if attachment then
		self_draw_obj.gameObject:SetActive(true)
		local point = attachment:GetAttachPoint(PartAttachPoint[SceneObjPart.WuHunZhenShen])

		if not (IsNil(point) or IsNil(self_draw_obj.attach_obj)) then
			self_draw_obj.attach_obj:SetAttached(point)
			self_draw_obj.attach_obj:SetTransform(attachment.Prof)

			if attachment_callback then
				attachment_callback()
			end
		end
	else
		self_draw_obj.gameObject:SetActive(false)
	end
end

function WuHunObj:InitEnd()
	Character.InitEnd(self)
end

function WuHunObj:SetDirectionByXY(x, y)
	Character.SetDirectionByXY(self, x, y)
end

function WuHunObj:OnEnterScene()
	Character.OnEnterScene(self)
end

function WuHunObj:CancelSelect()
	Character.CancelSelect(self)
end

function WuHunObj:EnterStateDead()
	Character.EnterStateDead(self)
end

function WuHunObj:IsWuHun()
	return true
end

-- 修正特效大小
function WuHunObj:CorrectEffectTriggerCustomScale(correct_ratio)
    self.actor_trigger:SetTargetEffectTriggerCustomScale(Vector3(0.8 * correct_ratio, 0.8 * correct_ratio, 0.8 * correct_ratio))
end

--是否是自己的武魂
function WuHunObj:IsOnwerWuHun()
	return self.owner_obj_id == GameVoManager.Instance:GetMainRoleVo().obj_id
end

function WuHunObj:GetWuHunId()
	return self.vo.wuhun_id
end

---创建武魂实例，将环转换为武魂
function WuHunObj:BuildWuHunInstance(wuhun_skill_id)
	self:VisibleChanged(true)

	local wuhun_data = WuHunWGData.Instance:GetWuHunSkillActiveCfg(wuhun_skill_id)
	if wuhun_data then
		local res_lv = wuhun_data.appe_image_id
		local cfg = WuHunWGData.Instance:GetWuhunBreachResByIdLv(wuhun_data.wuhun_id, self.wuhun_lv)
		if cfg then
			res_lv = cfg.appe_image_id
		end

		self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("WuHun", res_lv))

		local bundle, asset = ResPath.GetWuHunModel2(wuhun_data.appe_image_id, res_lv)
		self.bundle_name = bundle
		self.asset_name = asset
		self:PlayWuHunFade(0, 0.9, function ()
			self.attack_skill_id = wuhun_skill_id
			self.wuhun_appid = wuhun_data.appe_image_id
			self:ChangeModel(SceneObjPart.Main, bundle, asset, BindTool.Bind(self.EnterStateAttack, self))
		end)
	end
end

-- 创建武魂魂阵
function WuHunObj:BuildWuHunHunZhenInstance()
	if self.owner_obj.vo and self.owner_obj.vo.wuhun_hunzhen_id ~= nil and self.owner_obj.vo.wuhun_hunzhen_id > -1 then
		self.wuhun_hunzhen_id = self.owner_obj.vo.wuhun_hunzhen_id
		local wuhun_data = WuHunWGData.Instance:GetWuHunSkillActiveCfg(self.attack_skill_id)

		if wuhun_data and self.wuhun_hunzhen_id ~= nil and self.wuhun_hunzhen_id > -1 then
			local cfg = WuHunFrontWGData.Instance:GetSoulFormationAppimageCfg(wuhun_data.wuhun_id, self.wuhun_hunzhen_id)
			if cfg then
				local bundle, asset = ResPath.GetWuHunSceneWunZhenModel(wuhun_data.wuhun_id, cfg.scene_app_image_id)
				self:ChangeModel(SceneObjPart.SoulFormation, bundle, asset)
			end
		end
	end
end

---去除武魂实例，将武魂转换为环
function WuHunObj:RemoveWuHunInstance()
	self.wuhun_appid = nil
	self.is_using_skill = false
	self.wuhun_hunzhen_id = nil
	self:RemoveModel(SceneObjPart.SoulFormation)
	self:InitAppearance()
end

function WuHunObj:EnterStateAttack()
	if not self.owner_obj or self.owner_obj:IsDeleted() or self.owner_obj:IsDead() or (not self.wuhun_appid) then
		self:SetIsAtkPlaying(false)
		return
	end

	local anim_name = SceneObjAnimator.Combo1_1 
	local clip_name = SceneObjAnimator.D_Combo1_1 


	local is_skill_normal = false
	local client_skill_data = SkillWGData.GetSkillinfoConfig(self.attack_skill_id)
	local is_wuhun_skill = SkillWGData.Instance:GetIsWuHunSkill(self.attack_skill_id)

	-- if client_skill_data == nil then
	-- 	is_skill_normal = true
	-- 	client_skill_data = SkillWGData.Instance:GetSkillClientConfig(self.attack_skill_id)
	-- end

	if (not is_wuhun_skill) and (not client_skill_data) then
		self:SetIsAtkPlaying(false)
		return
	end

	if self:IsAtkPlaying() then
		return
	end

	if client_skill_data and (not is_wuhun_skill) then	---不是武魂技能是普攻
		local remainder = self.attack_skill_id % 10
		if remainder == 1 then
			anim_name = SceneObjAnimator.Atk1 or anim_name
			clip_name = SceneObjAnimator.D_Attack1 or clip_name
		elseif remainder == 2 then
			anim_name = SceneObjAnimator.Atk2 or anim_name
			clip_name = SceneObjAnimator.D_Attack2 or clip_name
		elseif remainder == 3 then
			anim_name = SceneObjAnimator.Atk3 or anim_name
			clip_name = SceneObjAnimator.D_Attack3 or clip_name
		elseif remainder == 4 then
			anim_name = SceneObjAnimator.Atk4 or anim_name
			clip_name = SceneObjAnimator.D_Attack4 or clip_name
		end
	end

	self:AttachToRoleMainPart(function ()
		-- 正在播放技能时，不播放其他动作
		if anim_name == SceneObjAnimator.Combo1_1 then
			self.is_using_skill = true
		else
			if self.is_using_skill then
				return
			end
		end

		Character.EnterStateAttack(self, anim_name)
		local part = self.draw_obj:GetPart(SceneObjPart.Main)
		if not part or not part.obj then 
			self:SetIsAtkPlaying(false)
			return 
		end
	
		local anim = part:GetObj().animator
		if not anim then 
			self:SetIsAtkPlaying(false)
			return 
		end
	
		local clip = anim:GetAnimationClip(clip_name)
		if not clip then 
			self:SetIsAtkPlaying(false)
			return 
		end
	
		local action_end_time = clip.length or 0.1
		self:RemoveActionDelayTime()
		self.cancel_quest_of_action = GlobalTimerQuest:AddDelayTimer(function ()
			if anim_name == SceneObjAnimator.Combo1_1 then
				self.is_using_skill = false
			end
			
			self:SetIsAtkPlaying(false)
			self:RemoveActionDelayTime()
		end, action_end_time)
	end)
end

-- 攻击
function WuHunObj:QuitStateAttack(attack_skill_id)
    self.is_special_move = false
    self.special_speed = 0
    self:ClearActionData()
    self._moveList = nil
    --结束移动
    local main_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
    if main_obj then
        main_obj.transform.localPosition = Vector3.zero
    end
end

function WuHunObj:OnAttackPlayEnd()
	if not self.owner_obj then
		return
	end

	if self.owner_obj:IsDeleted() or self.owner_obj:IsDead() then
	else
		self:ChangeToCommonState()
	end
end

function WuHunObj:EnterStateStand()
	if not self.is_using_skill then
		Character.EnterStateStand(self)
	end
end

--移除回调
function WuHunObj:RemoveActionDelayTime()
    if self.cancel_quest_of_action then
        GlobalTimerQuest:CancelQuest(self.cancel_quest_of_action)
        self.cancel_quest_of_action = nil
    end
end

--移除回调2
function WuHunObj:RemoveChangeModelDelayTime()
    if self.cancel_change_model then
        GlobalTimerQuest:CancelQuest(self.cancel_change_model)
        self.cancel_change_model = nil
    end
end

--移除回调3
function WuHunObj:RemoveAttachMainDelayTime()
    if self.cancel_attach_action then
        GlobalTimerQuest:CancelQuest(self.cancel_attach_action)
        self.cancel_attach_action = nil
    end
end

--移除回调4
function WuHunObj:RemoveNormalActionDelayTime()
    if self.cancel_quest_of_normal_action then
        GlobalTimerQuest:CancelQuest(self.cancel_quest_of_normal_action)
        self.cancel_quest_of_normal_action = nil
    end
end

function WuHunObj:OnDie()
	Character.OnDie(self)

	local part_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()

    if nil ~= part_obj and nil ~= part_obj.actor_ctrl then
        part_obj.actor_ctrl:StopEffects()
    end
end


function WuHunObj:AttackActionEndHandle()
	self:ChangeToCommonState()
end

function WuHunObj:OnModelLoaded(part, obj)
	if not self.owner_obj then
		return
	end

	if self:IsDeleted() or self.owner_obj:IsDead() then
		return
	end

	Character.OnModelLoaded(self, part, obj)
end


function WuHunObj:SetDeputyAttackTarget(target_obj)
	self.deputy_attack_target = target_obj
end

function WuHunObj:GetDeputyAttackTarget()
	return self.deputy_attack_target
end

function WuHunObj:SetFightCache(data)
	self.fight_cache = data
end

function WuHunObj:GetFightCache()
	return self.fight_cache
end

function WuHunObj:ClearFightCache()
    if nil ~= self.fight_cache then
    	self.fight_cache = nil
    end
end

function WuHunObj:GetOwnerObjID()
	return self.owner_obj_id
end

function WuHunObj:SetOwnerObj(owner_obj)
	self.owner_obj = owner_obj
end

function WuHunObj:GetOwnerObj()
	return self.owner_obj
end

function WuHunObj:PartEffectVisibleChanged(part, visible)
    self.draw_obj:SetPartIsDisableAttachEffect(part, not visible)
end

function WuHunObj:SetEffectQualityLevelOffset(offset)
	if self.effect_handle then
		self.effect_handle:SetQualityLevelOffset(offset)
	end
end

function WuHunObj:UpdateQualityLevel()
	local base_offset = -1
	local owner_obj = self.owner_obj
	if owner_obj and owner_obj.IsMainRole and owner_obj:IsMainRole() then
		base_offset = 1
	end

	local model_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     model_offset = model_offset + QualityWGData.Instance:GetModelQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetQualityLevelOffset(model_offset)

	local effect_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     effect_offset = effect_offset + QualityWGData.Instance:GetModelEffectQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetEffectQualityLevelOffset(effect_offset)
end

function WuHunObj:UpdateEffectVisible()
	local owner_obj = self.owner_obj
	if self.effect_handle and owner_obj and not owner_obj:IsDeleted() and not self.owner_obj:IsDead() and owner_obj.GetRoleEffectVisible then
		local visible = owner_obj:GetRoleEffectVisible()

		if visible then
		    self.effect_handle:CancelForceSetVisible()
		else
		    self.effect_handle:ForceSetVisible(false)
		end
	end
end

function WuHunObj:GetActionTimeRecord(anim_name)
	local wuhun_data = WuHunWGData.Instance:GetWuHunSkillActiveCfg(self.attack_skill_id)

	if wuhun_data then
		local atr = WuhunActionConfig[wuhun_data.appe_image_id]
		if atr ~= nil then
			return atr[anim_name]
		end
	end

	return nil
end

-- 渐变
function WuHunObj:PlayWuHunFade(fade_type, fade_time, call_back)
    local wuhun_main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    if nil ~= wuhun_main_part then
        local mount_obj = wuhun_main_part:GetObj()
        if mount_obj == nil then
            if call_back then
                call_back()
            end
            return
        end
        local actor_render = mount_obj.actor_render
        if actor_render ~= nil then
            actor_render:PlayFadeEffect(fade_type == 1, fade_time, call_back)
        else
            if call_back then
                call_back()
            end
        end
    end
end