require("game/subpackage/subpackage_view")
require("game/subpackage/subpackage_wg_data")

local PackageIndex = 1

SubPackageWGCtrl = SubPackageWGCtrl or BaseClass(BaseWGCtrl)

function SubPackageWGCtrl:__init()
    if SubPackageWGCtrl.Instance then
		ErrorLog("[SubPackageWGCtrl] Attemp to create a singleton twice !")
	end
	SubPackageWGCtrl.Instance = self

    self.view = SubpackageView.New(GuideModuleName.SubPackageView)
    self.data = SubpackageWGData.New()

    self:RegisterAllProtocols()

    self.mainui_opened = false

    self.update_callback = BindTool.Bind1(self.UpdateProgress, self)
	self.complete_callback = BindTool.Bind1(self.CompleteCallBack, self)
    self.updata_delegate = nil
    self.complete_delegate = nil

    if not IS_DEBUG_BUILD then
        NetWorkState.SetNetWorkStateListener(BindTool.Bind1(self.NetWorkStateChange, self))
    end
    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
end

function SubPackageWGCtrl:__delete()
    self.mainui_opened = false

    self.view:DeleteMe()
    self.view = nil

    self.data:DeleteMe()
    self.data = nil

    if not IS_DEBUG_BUILD then
        NetWorkState.SetNetWorkStateListener(nil)
    end

    SubPackageWGCtrl.Instance = nil
end

function SubPackageWGCtrl:MainuiOpenCreate()
    self.mainui_opened = true
    local state, msg = self.data:GetNetWorkState()
    self:NetWorkStateChange(state, msg)
end

function SubPackageWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCSubPackageDownloadInfo, "OnSCSubPackageDownloadInfo")
	self:RegisterProtocol(CSSubPackageDownloadFetchReq)
end

function SubPackageWGCtrl:OnSCSubPackageDownloadInfo(protocol)
    if not GAME_ASSETBUNDLE then
        return
    end

    local tab = bit:d2b_two(protocol.reward_fetched_flag)
    self.data:SetRewardTab(tab)
    self.view:Flush()
    --目前只有一个包的奖励
    if tab[0] == 0 then
        --show btn
        MainuiWGCtrl.Instance:FlushView(0, "subpackage_icon", {true})
    else
        local total_size, downloaded_size, is_downloading = SubPackage.Instance:GetPackageInfo(PackageIndex)
        if total_size ~= 0 then
            local percent = downloaded_size / total_size
            if percent < SubpackageWGData.Instance:GetShowBtnPercent() then
                MainuiWGCtrl.Instance:FlushView(0, "subpackage_icon", {true})
            else
                --hide_btn
                MainuiWGCtrl.Instance:FlushView(0, "subpackage_icon", {false})
            end
        end
    end
    RemindManager.Instance:Fire(RemindName.SubPackage_Download)
end

function SubPackageWGCtrl:SendSubPackageDownloadFetchReq(seq)
    local protocol = ProtocolPool.Instance:GetProtocol(CSSubPackageDownloadFetchReq)
    protocol.seq = seq
    protocol:EncodeAndSend()
end

-- state = 0 无网络 msg等于空
-- state = 1 移动网络 msg等于MOBILE
-- state = 2 WiFi网络 msg等于WIFI
-- state = 3 未知网络 msg未知
function SubPackageWGCtrl:NetWorkStateChange(state, msg)
    self.data:SetNetWorkState(state, msg)

    if not GAME_ASSETBUNDLE then
        return
    end

    if not self.mainui_opened then
        return
    end

    local total_size, downloaded_size, is_downloading = SubPackage.Instance:GetPackageInfo(PackageIndex)
    --当玩家以wifi连接状态进入游戏，自动进行后台分包下载
    if state == NETWORK_STATE.WIFI then
        if not is_downloading and total_size > downloaded_size then
            self:StartDownloadPackage(PackageIndex)
        end
    end
    --下载过程中如果玩家网络状态切换为移动信号，则出现弹窗
    if state == NETWORK_STATE.MOBILE then
        if is_downloading then
            self:PauseDownloadPackage(PackageIndex)
            local str = string.format(Language.SubPackage.MobieNetworkCancelDownload, Language.SubPackage.MobileNetWork, Language.SubPackage.MobileNetWork)
            TipWGCtrl.Instance:OpenAlertTips(str, function()
                self:StartDownloadPackage(PackageIndex)
            end)
        else
            if total_size > downloaded_size then
                --主界面icon弹出气泡
                MainuiWGCtrl.Instance:FlushView(0, "subpackage_qipao_active", {true})
            end
        end
    end

    RemindManager.Instance:Fire(RemindName.SubPackage_Download)
end

function SubPackageWGCtrl:UpdateProgress(total_size, downloaded_size, speed)
    if self.updata_delegate ~= nil then
        self.updata_delegate(total_size, downloaded_size, speed)
    end
end

function SubPackageWGCtrl:CompleteCallBack(is_succ, total_size, downloaded_size)
    if self.complete_delegate ~= nil then
        self.complete_delegate(is_succ, total_size, downloaded_size)
    end

    MainuiWGCtrl.Instance:FlushView(0, "subpackage_qipao_active", {false})
	RemindManager.Instance:Fire(RemindName.SubPackage_Download)
end

--目前只给自己的view注册
function SubPackageWGCtrl:RegisterFun(updata_delegate, complete_delegate)
    self.updata_delegate = updata_delegate
    self.complete_delegate = complete_delegate
end

function SubPackageWGCtrl:UnRegisterFun()
    self.updata_delegate = nil
    self.complete_delegate = nil
end

function SubPackageWGCtrl:StartDownloadPackage(index)
    SubPackage.Instance:StartDownloadPackage(index, self.update_callback, self.complete_callback)
    self.view:Flush()
    MainuiWGCtrl.Instance:FlushView(0, "subpackage_qipao_active", {false})
end

function SubPackageWGCtrl:PauseDownloadPackage(index)
    SubPackage.Instance:PauseDownloadPackage(index)
    self.view:Flush()
    local total_size, downloaded_size, is_downloading = SubPackage.Instance:GetPackageInfo(PackageIndex)
    if total_size > downloaded_size then
        MainuiWGCtrl.Instance:FlushView(0, "subpackage_qipao_active", {true})
    end
end

function SubPackageWGCtrl:GetPackageInfo(index)
    return SubPackage.Instance:GetPackageInfo(index)
end

function SubPackageWGCtrl:GetPackageCount()
    return SubPackage.Instance:GetPackageCount()
end