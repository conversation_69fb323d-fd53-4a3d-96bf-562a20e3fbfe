ActorTriggerCameraShake = ActorTriggerCameraShake or BaseClass(ActorTriggerBase)

function ActorTriggerCameraShake:__init(anima_name)
	self.anima_name = anima_name
	self.transform = nil
	self.enabled = true
	self.target = nil

	self.camera_shake_data = nil
end

function ActorTriggerCameraShake:InitData(anima_name)
	self.anima_name = anima_name
	self.transform = nil
	self.enabled = true
	self.target = nil

	self.camera_shake_data = nil
	self:Reset()
end

function ActorTriggerCameraShake:Reset()
	self.camera_shake_data = nil
	ActorTriggerBase.Reset(self)
end

function ActorTriggerCameraShake:__delete()
	self.camera_shake_data = nil
end

-- 初始化预制体保存的配置数据(单个)
function ActorTriggerCameraShake:Init(camera_shake_data)
	self.camera_shake_data = camera_shake_data
	self.delay = camera_shake_data.delay
end

-- get/set
function ActorTriggerCameraShake:Enalbed(value)
	if value == nil then
		return self.enabled
	end
	self.enabled = value
end

function ActorTriggerCameraShake:OnEventTriggered(source, target, stateInfo)
	local shake = self.camera_shake_data
	if shake and self.enabled and (stateInfo == nil or stateInfo.is_ignore_shake == nil or (stateInfo ~= nil and stateInfo.is_ignore_shake ~= nil and not stateInfo.is_ignore_shake)) then
		CameraShake.Shake(shake.numberOfShakes, Vector3.one, Vector3.zero, shake.distance, shake.speed, shake.decay, 1, true)
	end
end
