require("game/offlinerest/offlinerest_wg_data")
require("game/offlinerest/offlinerest_view")
require("game/offlinerest/offLine_insufficient_view")
require("game/offlinerest/offLine_overstep_view")
require("game/offlinerest/offLine_user_view")
require("game/offlinerest/offline_zhaohui_view")
require("game/offlinerest/tianshen_lilian_view")
require("game/offlinerest/offlinerest_exp_effect_view")

-- 离线经验
OfflineRestWGCtrl = OfflineRestWGCtrl or BaseClass(BaseWGCtrl)

function OfflineRestWGCtrl:__init()
	if OfflineRestWGCtrl.Instance ~= nil then
		ErrorLog("[OfflineRestWGCtrl] Attemp to create a singleton twice !")
	end
	OfflineRestWGCtrl.Instance = self
	self.view = OfflineRestView.New(GuideModuleName.OfflineRestView)
	self.data = OfflineRestWGData.New()
	self.user_offline_view = UserOffLineView.New()
	self.offline_overstep_view = OffLineOverstepView.New()
	self.offline_insufficient_view = OffLineInsufficient.New()
	self.offline_zhaohui_view = OfflineZhaoHuiView.New()
	self.guaji_exp_view = GuaJiExpView.New(GuideModuleName.GuaJiExpView)
	self.tianshen_lilian_view = TianShenLiLianView.New(GuideModuleName.TianShenLiLianView)
	self.offlinerest_exp_effect_view = OfflinerestEffectView.New()

	self:RegisterAllProtocols()
end

function OfflineRestWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.user_offline_view:DeleteMe()
	self.user_offline_view = nil

	self.offline_overstep_view:DeleteMe()
	self.offline_overstep_view = nil

	self.offline_insufficient_view:DeleteMe()
	self.offline_insufficient_view = nil

	self.guaji_exp_view:DeleteMe()
	self.guaji_exp_view = nil

	self.tianshen_lilian_view:DeleteMe()
	self.tianshen_lilian_view = nil

	if self.offline_zhaohui_view then
		self.offline_zhaohui_view:DeleteMe()
		self.offline_zhaohui_view = nil
	end

	if self.flush_quintuple_exp_time then
		GlobalTimerQuest:CancelQuest(self.flush_quintuple_exp_time)
		self.flush_quintuple_exp_time = nil
	end

	self:RemoveGetInfoQuest()

	OfflineRestWGCtrl.Instance = nil
end

function OfflineRestWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOfflineRestInfo, "OnOfflineRestInfo")				--离线挂机信息

	self:RegisterProtocol(SCOnlineMeditationRewardCount, "OnSCOnlineMeditationRewardCount")	  -- 打坐奖励信息
	self:RegisterProtocol(CSHangMeditataionInfo) 								-- 请求打坐具体奖励信息

	self:RegisterProtocol(OfflineHangExpFindInfo, "OnOfflineHangExpFindInfo")	  --离线挂机经验找回--信息返回
end

function OfflineRestWGCtrl:Open(tab_index)
	-- self.view:Open(tab_index)
end


function OfflineRestWGCtrl:RemoveGetInfoQuest()
    if self.get_sit_info_quest ~= nil then
        GlobalTimerQuest:CancelQuest(self.get_sit_info_quest)
        self.get_sit_info_quest = nil
    end
end

function OfflineRestWGCtrl:InitSitRewardInfo()
    self:RemoveGetInfoQuest()

	self.get_sit_info_quest = GlobalTimerQuest:AddDelayTimer(function ()
        self:SendGetSitRewardInfo()
	end, 10)
end

function OfflineRestWGCtrl:OnOfflineRestInfo(protocol)
	--print_error("----离线信息-----", protocol.notify_reson, protocol)
	self.data:SetOfflineRestData(protocol)
	self:InitSitRewardInfo()
	SettingWGCtrl.Instance:FlushGuaji()
	-- if BiZuoWGCtrl.Instance.view:IsOpen() then
	-- 	BiZuoWGCtrl.Instance:Flush()
	-- end

	MainuiWGCtrl.Instance:InsertNeedOpenView(NEED_OPEN_TIPS_TYPE.OFF_LINEREST_TYPE,false,BindTool.Bind(self.NeedOpenView,self))
	if OFFLINE_REST_INFO_NOTIFY_REASON.BUY_TIME == protocol.notify_reson then
		if SettingWGCtrl.Instance.setting_view:IsOpen() then
			SettingWGCtrl.Instance.setting_view:Flush(SettingViewIndex.Guaji)
		end

		if BiZuoWGCtrl.Instance.view:IsOpen() then
			BiZuoWGCtrl.Instance:Flush()
		end

		self:FlushLiLianView("flush_exp")

	elseif OFFLINE_REST_INFO_NOTIFY_REASON.LOGIN == protocol.notify_reson then
		if protocol.add_exp > 0 then
			MainuiWGCtrl.Instance:InsertNeedOpenView(NEED_OPEN_TIPS_TYPE.OFF_LINEREST_TYPE, true, BindTool.Bind(self.NeedOpenView, self))
		end
		-- if protocol.remain_offline_rest_time < 5 * 3600 and RoleWGData.Instance.role_vo.level >= self.data:GetOfflineRestLimitLevel() then
			--！！暂时注释 self.offline_insufficient_view:Open()
		-- end
	elseif OFFLINE_REST_INFO_NOTIFY_REASON.GET_EXP_CHANGE == protocol.notify_reson then
		-- UiInstanceMgr.Instance:FlushGuaJiEfficiency()
		if protocol.get_exp_per_minute > 0 and GuajiCache.guaji_type == GuajiType.Auto and Scene.Instance:GetSceneType() == SceneType.Common then
			self.guaji_exp_view:SetData(protocol.get_exp_per_minute)
		end

	elseif OFFLINE_REST_INFO_NOTIFY_REASON.ONLINE_REWARD == protocol.notify_reson then
		if not self.view:IsOpen() then
			if self.need_open_offline_view then
				self.need_open_offline_view = nil
				self:NeedOpenView(true)
			end
		else
			self.need_open_offline_view = nil
			self.view:Flush()
		end

		self:FlushLiLianView("flush_exp")
		RemindManager.Instance:Fire(RemindName.TianShenLiLian)
	end
end

function OfflineRestWGCtrl:NeedOpenView(value)
	self.view:SetIsOnlineOpen(value)
	self.view:Open()
end

function OfflineRestWGCtrl:OpenUserOfflineView(item_id,default_num,show_num, str)
	local num = nil
	if self.data:IsOfflineItem(item_id) then
		-- num = self.data:NeedUserNum(item_id)
		num = ItemWGData.Instance:GetItemNumInBagById(item_id)
	end
	self.user_offline_view:SetData(item_id,default_num or num,show_num, str)
end

function OfflineRestWGCtrl:OpenOfflineOverstepView(item_id, item_num)
	self.offline_overstep_view:SetData(item_id, item_num)
end

function OfflineRestWGCtrl:IsViewOpen()
	return self.view:IsOpen()
end

function OfflineRestWGCtrl:FlushOfflineView()
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
end


-----------------------打坐----------------------------
function OfflineRestWGCtrl:OnSCOnlineMeditationRewardCount(protocol)
	-- print_error("----打坐信息-----", protocol)
	local old_num = self.data:GetLiLianItemCount()
	local old_exp_xiaolv = self.data:GetLiLianExpXiaoLv()
	local is_flush_lilian = false
	if (old_num == 0 and protocol.item_count > 0) or (old_num > 0 and protocol.item_count == 0) then
		is_flush_lilian = true
	end

	if old_exp_xiaolv ~= protocol.exp_xiaolv then
		is_flush_lilian = true
	end

	self.data:SetSitRewardInfo(protocol)
	if is_flush_lilian then
		RemindManager.Instance:Fire(RemindName.TianShenLiLian)
		self:FlushLiLianView("flush_exp")
	end
end

function OfflineRestWGCtrl:SendGetSitRewardInfo(need_open_offline_view)
	self.need_open_offline_view = need_open_offline_view
    local protocol = ProtocolPool.Instance:GetProtocol(CSHangMeditataionInfo)
    protocol:EncodeAndSend()
end

--离线经验--找回-----------------------start------------------------
function OfflineRestWGCtrl:OnOfflineHangExpFindInfo(protocol)
	self.data:OnOfflineHangExpFindInfo(protocol)
end

function OfflineRestWGCtrl:OpenOfflineZhaoHuiView()
	-- print_error("FFFF====== 打开离线经验找回", self.data:GetOfflineZhaoHuiHasOpened(), self.data:GetOfflineZhaoHuiCanFind(), RechargeWGData.Instance:IsBuyTouZiPlan(), self.data:CheckFindCostEnough())

	-- if self.data:GetOfflineZhaoHuiHasOpened() then--本次登陆已经打开过
	-- 	return
	-- end

	if not self.data:GetOfflineZhaoHuiCanFind() then--无数据找回
		return
	end

	-- --策划:离线挂机打坐的经验找回加个限制：购买了绑玉投资 或者 1.5倍经验药水足够才弹
	-- if not RechargeWGData.Instance:IsBuyTouZiPlan() and not self.data:CheckFindCostEnough() then
	-- 	return
	-- end

	if not self.offline_zhaohui_view:IsOpen() then
		self.offline_zhaohui_view:Open()
	end
end

--离线经验--找回-----------------------end------------------------

function OfflineRestWGCtrl:FlushLiLianView(key)
	if self.tianshen_lilian_view:IsOpen() then
		self.tianshen_lilian_view:Flush(0, key)
	end
end


function OfflineRestWGCtrl:OpenOfflinerestExpEffectView()
	if not self.offlinerest_exp_effect_view:IsOpen() then
		self.offlinerest_exp_effect_view:Open()
	end
end