function NewAppearanceWGView:UGQiChongInitView()
    if self.is_init_ug_qc_view then
        return
    end

    if not self.ug_qc_attr_list then
        self.ug_qc_attr_list = {}
        local parent_node = self.node_list["ug_qc_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = QiChongAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.ug_qc_attr_list[i] = cell
        end
    end

    self.ug_qc_skill_item_list = {}
    for i = 1, 5 do
        self.ug_qc_skill_item_list[i] = QiChongSkillRender.New(self.node_list["ug_qc_skill_" .. i])
        self.ug_qc_skill_item_list[i]:SetIndex(i)
    end

    self.ug_qc_stuff_item_list = {}
    for i = 1, 2 do
        self.ug_qc_stuff_item_list[i] = ItemCell.New(self.node_list["ug_qc_stuff_item" .. i])
    end

    if not self.ug_qc_sxd_list then
        self.ug_qc_sxd_list = {}
        local parent_node = self.node_list["ug_qc_sxd_list"]
        local count = parent_node.transform.childCount
        for i = 1, count do
            local cell = NewAppearanceSXDRender.New(parent_node:FindObj("sxd_" .. i))
            cell:SetIndex(i)
            self.ug_qc_sxd_list[i] = cell
        end
    end

    XUI.AddClickEventListener(self.node_list["ug_qc_btn_use"], BindTool.Bind(self.OnClickUGQCUse, self))
    XUI.AddClickEventListener(self.node_list["ug_qc_btn_upgrade"], BindTool.Bind(self.OnClickUGQCUpStar, self))
    XUI.AddClickEventListener(self.node_list["ug_qc_btn_left"], BindTool.Bind(self.OnClickUGQCGradeShow, self, -1))
    XUI.AddClickEventListener(self.node_list["ug_qc_btn_right"], BindTool.Bind(self.OnClickUGQCGradeShow, self, 1))
    XUI.AddClickEventListener(self.node_list["ug_qc_cangjin_btn"], BindTool.Bind(self.OnClickOpenCangJinView, self))
    XUI.AddClickEventListener(self.node_list["ug_qc_sxd_extend"], BindTool.Bind(self.OnClickUGQCSXDExtend, self))
    XUI.AddClickEventListener(self.node_list["qc_king_vip_btn"], BindTool.Bind(self.OnClickKingVipBtn, self))
    XUI.AddClickEventListener(self.node_list["ug_qc_btn_reset"], BindTool.Bind(self.OnClicQCResetBtn, self))
    self.is_init_ug_qc_view = true
end

function NewAppearanceWGView:UGQiChongReleaseCallBack()
    self:ClearUGQCSliderTween()
    self.is_init_ug_qc_view = nil
    self.ug_qc_select_type = nil
    self.ug_qc_cur_show_grade = nil

    if self.ug_qc_attr_list then
        for k,v in pairs(self.ug_qc_attr_list) do
            v:DeleteMe()
        end
        self.ug_qc_attr_list = nil
    end

    if self.ug_qc_skill_item_list then
        for k,v in pairs(self.ug_qc_skill_item_list) do
            v:DeleteMe()
        end
        self.ug_qc_skill_item_list = nil
    end

    if self.ug_qc_stuff_item_list then
        for k,v in pairs(self.ug_qc_stuff_item_list) do
            v:DeleteMe()
        end
        self.ug_qc_stuff_item_list = nil
    end

    if self.ug_qc_sxd_list then
        for k,v in pairs(self.ug_qc_sxd_list) do
            v:DeleteMe()
        end
        self.ug_qc_sxd_list = nil
    end
end

function NewAppearanceWGView:UGQiChongShowIndexCallBack(index)
    local qc_type = 0
    if self.show_index == TabIndex.new_appearance_upgrade_mount then
        qc_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT
    elseif self.show_index == TabIndex.new_appearance_upgrade_lingchong then
        qc_type = MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
    end

    if self.ug_qc_select_type ~= qc_type then
        self.ug_qc_cur_show_grade = nil
    end

    self.ug_qc_select_type = qc_type

    self:ClearUGQCSliderTween()
    self:StopUGQCAutoUpStar()

    local is_lingchong_type = qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
    self.node_list["ug_qc_btn_use_text"].text.text = is_lingchong_type and Language.NewAppearance.HuanHua or Language.NewAppearance.ChengQi
    self.node_list["ug_qc_btn_reset_text"].text.text = is_lingchong_type and Language.NewAppearance.YiHuanHua or Language.NewAppearance.YiChengQi
end

function NewAppearanceWGView:UGQiChongFlushView()
    local info = NewAppearanceWGData.Instance:GetQiChongInfo(self.ug_qc_select_type)
    if info == nil then
        return
    end

    local star_level = info.star_level
    local upstar_bless_val = info.upstar_bless_val

    local cur_upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(self.ug_qc_select_type, star_level)
    local next_upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(self.ug_qc_select_type, star_level + 1)
    local is_max = next_upstar_cfg == nil

    -- 技能
    local skill_list = NewAppearanceWGData.Instance:GetBaseQiChongSkillList(self.ug_qc_select_type)
    -- local bubble_data
    for k,v in ipairs(skill_list) do
        if self.ug_qc_skill_item_list[k] then
            -- if not v.is_act and not bubble_data then
            --     bubble_data = {index = k, data = v}
            -- end

            self.ug_qc_skill_item_list[k]:SetData(v)
        end
    end

    -- if bubble_data then
    --     self.node_list["ug_qc_tip_qipao"]:CustomSetActive(true)
    --     local pos = RectTransform.GetAnchoredPosition(self.ug_qc_skill_item_list[bubble_data.index].view.transform)
    --     RectTransform.SetAnchoredPositionXY(self.node_list["ug_qc_tip_qipao"].rect, pos.x + 12, 36)
    --     local upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(self.ug_qc_select_type, bubble_data.data.cfg.active_star_level)
    --     if upstar_cfg then
    --         self.node_list["ug_qc_tip_qipao_text"].text.text = string.format(Language.NewAppearance.SkillGradeActTips2, upstar_cfg.grade_num, upstar_cfg.star_num)
    --     end
    -- else
    --     self.node_list["ug_qc_tip_qipao"]:CustomSetActive(false)
    -- end

    -- 属性丹
    local sxd_list = NewAppearanceWGData.Instance:GetQiChongSXDlist(self.ug_qc_select_type)
    for k,v in pairs(self.ug_qc_sxd_list) do
        v:SetData(sxd_list[k])
    end

    local is_can_choose_attr =  YanYuGeWGData.Instance:GetIsActChooseAttrTeQuan()
    local already_choose_attr = YanYuGeWGData.Instance:IsAlreadyChooseAttr()
    local show_cangjin_btn = self.ug_qc_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT and is_can_choose_attr
    self.node_list.ug_qc_cangjin_btn:SetActive(show_cangjin_btn)
    self.node_list.ug_qc_cangjin_remind:SetActive(is_can_choose_attr and (not already_choose_attr))

    local fun_name = NewAppearanceWGData.Instance:GetQiChongFunnameBuType(self.ug_qc_select_type)
    if fun_name then
        local is_open = FunOpen.Instance:GetFunIsOpened(fun_name)

        if is_open then
            -- 属性宝石
            local is_remind = NewAppearanceWGData.Instance:GetQiChongSXDRemind(self.ug_qc_select_type)
            self.node_list["ug_qc_sxd_extend_remind"]:CustomSetActive(is_remind)
        end

        self.node_list["ug_qc_sxd_extend"]:CustomSetActive(is_open)
    end

    -- 进度
    local need_bless_val = cur_upstar_cfg.need_bless
    if is_max then
        if self.is_ug_qc_auto_upstar then
            self:PlayUGQCSliderTween(0, upstar_bless_val, need_bless_val)
        else
            self.node_list["ug_qc_progress"].slider.value = 1
        end

        self.node_list["ug_qc_progress_text"].text.text = "-/-"
    else
        if self.is_ug_qc_auto_upstar then
            local add_star_level = star_level - self.ug_qc_old_star_level
            self:PlayUGQCSliderTween(add_star_level, upstar_bless_val, need_bless_val)
        else
            self.node_list["ug_qc_progress_text"].text.text = ToColorStr(upstar_bless_val, COLOR3B.C8) .. "/" .. need_bless_val
            self.node_list["ug_qc_progress"].slider.value = upstar_bless_val / need_bless_val
            self.node_list["ug_qc_btn_upgrade_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[4]
        end
    end

    -- 消耗
    local stuff_list = NewAppearanceWGData.Instance:GetQiChongBaseUpStarStuff(self.ug_qc_select_type)
    for k,v in pairs(self.ug_qc_stuff_item_list) do
        local data = stuff_list[k]
        if data then
            self.node_list["ug_qc_stuff_item" .. k]:CustomSetActive(true)
            local item_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
			v:SetFlushCallBack(function ()
				v:SetRightBottomColorText(item_num)
				v:SetRightBottomTextVisible(true)
			end)

            v:SetData({item_id = data.item_id})
        else
            self.node_list["ug_qc_stuff_item" .. k]:CustomSetActive(false)
        end
    end

    -- 按钮
    self.node_list["ug_qc_max_star_flag"]:CustomSetActive(is_max)
    self.node_list["ug_qc_btn_upgrade"]:CustomSetActive(not is_max)

    local is_remind = NewAppearanceWGData.Instance:GetBaseQiChongUpStarRemind(self.ug_qc_select_type)
    self.node_list["ug_qc_btn_upgrade_remind"]:CustomSetActive(is_remind)

    local flush_model = false
    if self.ug_qc_cur_show_grade ~= cur_upstar_cfg.grade_num and not self.ug_qc_no_jump_grade then
        self.ug_qc_cur_show_grade = cur_upstar_cfg.grade_num
        flush_model = true
     end

    if not self.is_ug_qc_auto_upstar then
        self:UGQiChongStarLevelChangeFlush(star_level, false)
    end

    self:UGQCFlushDisplay(flush_model)

    self:SetKingVipBtnInfo(2)
end

function NewAppearanceWGView:UGQiChongStarLevelChangeFlush(star_level, need_check_show_effect)
    local next_upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(self.ug_qc_select_type, star_level + 1)
    local is_max = next_upstar_cfg == nil

    -- 属性
    local attr_list, capability = NewAppearanceWGData.Instance:GetBaseQiChongAttrListAndCap(self.ug_qc_select_type, star_level)
    local need_show_up_effect = false

    if need_check_show_effect then
        if nil ~= self.ug_qc_select_type_cache and nil ~= self.ug_qc_select_type_levle_cache then
            if self.ug_qc_select_type_cache == self.ug_qc_select_type and (star_level - self.ug_qc_select_type_levle_cache == 1) then
                need_show_up_effect = true
            end
        end
    end

    for k,v in pairs(self.ug_qc_attr_list) do
        v:SetData(attr_list[k])

        if need_show_up_effect then
            v:PlayAttrValueUpEffect()
        end
    end

    self.ug_qc_select_type_cache = self.ug_qc_select_type
    self.ug_qc_select_type_levle_cache = star_level

    self.node_list["ug_qc_cap_value"].text.text = capability

    -- 星级
    local show_star_num = star_level % 10 == 0 and 10 or star_level % 10
    for i = 1, 10 do
        local res = (show_star_num >= i or is_max) and "a3_ty_xx_zc" or "a3_ty_xx_zh"
        self.node_list["ug_qc_star_" .. i].image:LoadSprite(ResPath.GetCommonImages(res))
    end
end

function NewAppearanceWGView:UGQCFlushDisplay(flush_model)
    local info = NewAppearanceWGData.Instance:GetQiChongInfo(self.ug_qc_select_type)
    if info == nil then
        return
    end

    local cur_upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(self.ug_qc_select_type, info.star_level)
    if not cur_upstar_cfg then
        return
    end

    local cfg_grade = cur_upstar_cfg.grade_num
    local max_grade = NewAppearanceWGData.Instance:GetQiChongBaseMaxGrade(self.ug_qc_select_type)
    local base_cfg = NewAppearanceWGData.Instance:GetQiChongBaseCfgByGrade(self.ug_qc_select_type, self.ug_qc_cur_show_grade)
    if not base_cfg then
        return
    end

    local is_act = self.ug_qc_cur_show_grade <= cfg_grade

    local cfg_appe_id = base_cfg.appe_image_id
    self.node_list["ug_qc_btn_use"]:CustomSetActive(info.used_imageid ~= cfg_appe_id and is_act)
    self.node_list["ug_qc_btn_reset"]:CustomSetActive(info.used_imageid == cfg_appe_id and is_act)

    -- 取巧 幻化不跳转
    if self.ug_qc_no_jump_grade then
        self.ug_qc_no_jump_grade = nil
        return
    end

    -- 名字
    self.node_list["ug_qc_select_name"].text.text = base_cfg.image_name
    self.node_list["ug_qc_grade"].text.text = string.format(Language.NewAppearance.GradeStr, self.ug_qc_cur_show_grade)

    -- self.node_list["ug_qc_btn_left"]:CustomSetActive(self.ug_qc_cur_show_grade > 1)
    -- self.node_list["ug_qc_btn_right"]:CustomSetActive(self.ug_qc_cur_show_grade < cfg_grade + 1 and self.ug_qc_cur_show_grade < max_grade)
    self.node_list["ug_qc_no_act_flag"]:CustomSetActive(self.ug_qc_cur_show_grade > cfg_grade)

    if flush_model then
        self:ShowQiChongModel(self.ug_qc_select_type, cfg_appe_id)
    end
end

-- 使用
function NewAppearanceWGView:OnClickUGQCUse()
    local base_cfg = NewAppearanceWGData.Instance:GetQiChongBaseCfgByGrade(self.ug_qc_select_type, self.ug_qc_cur_show_grade)
    if not base_cfg then
        return
    end

	if self.ug_qc_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, base_cfg.appe_image_id, -1)
	elseif self.ug_qc_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
		NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.USE_IMAGE, base_cfg.appe_image_id)
	end

    self.ug_qc_no_jump_grade = true
    self:PlayUseEffect()
end

-- 方向键点击
function NewAppearanceWGView:OnClickUGQCGradeShow(dir)
    if not self.ug_qc_cur_show_grade then
        return
    end

    local max_grade = NewAppearanceWGData.Instance:GetQiChongBaseMaxGrade(self.ug_qc_select_type)
    local grade = self.ug_qc_cur_show_grade + dir
    grade = math.min(grade, max_grade)
    grade = math.max(1, grade)
    if grade ~= self.ug_qc_cur_show_grade then
        self.ug_qc_cur_show_grade = grade
        self:UGQCFlushDisplay(true)
    end
end

function NewAppearanceWGView:OnClicQCResetBtn()
    SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.ResetErrorTip)
end

-- 一键升星
function NewAppearanceWGView:OnClickUGQCUpStar()
    if not self.is_ug_qc_auto_upstar then
        self:StartUGQCAutoUpStar()
    else
        self:StopUGQCAutoUpStar()
    end
end

-- 停止升星
function NewAppearanceWGView:StopUGQCAutoUpStar()
    if not self.is_init_ug_qc_view then
        return
    end

    self.is_ug_qc_auto_upstar = nil
    self.node_list["ug_qc_btn_upgrade_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[4]
end

-- 自动升星
function NewAppearanceWGView:StartUGQCAutoUpStar(no_tips)
    -- if self.is_ug_qc_auto_upstar then
    --     return
    -- end

    local info = NewAppearanceWGData.Instance:GetQiChongInfo(self.ug_qc_select_type)
    if IsEmptyTable(info) then
        self:StopUGQCAutoUpStar()
        return
    end

    local next_upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(self.ug_qc_select_type, info.star_level + 1)
    if not next_upstar_cfg then
        self:StopUGQCAutoUpStar()
        return
    end

    -- 升阶材料
    local stuff_list = NewAppearanceWGData.Instance:GetQiChongBaseUpStarStuff(self.ug_qc_select_type)
    local stuff_id, had_stuff = 0, false
    for i, v in ipairs(stuff_list) do
        if stuff_id == 0 then
            stuff_id = v.item_id
        end
        
		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if item_num > 0 then
            stuff_id = v.item_id
			had_stuff = true
            break
		end
	end

    if not had_stuff then
        if not no_tips then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_id})
        end

        self:StopUGQCAutoUpStar()
        return
    end

    self.ug_qc_old_star_level = info.star_level
    self.ug_qc_old_grade = info.grade
    self.is_ug_qc_auto_upstar = true
    self.node_list["ug_qc_btn_upgrade_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[3]

    MainuiWGCtrl.Instance:CreateCacheTable()
    if self.ug_qc_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.UPSTAR, stuff_id)
	elseif self.ug_qc_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
		NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.UPSTAR, stuff_id)
	end
end

function NewAppearanceWGView:ClearUGQCSliderTween()
    if self.ug_qc_slider_tween then
        self.ug_qc_slider_tween:Kill()
        self.ug_qc_slider_tween = nil
    end
end

function NewAppearanceWGView:PlayUGQCSliderTween(add_star, bless_val, need_bless_val)
    if add_star == 0 and bless_val == need_bless_val then
        self:ClearUGQCSliderTween()
        local slider = self.node_list["ug_qc_progress"].slider
        local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.1))
        self.ug_qc_slider_tween = slider:DOValue(1, time)
        self.ug_qc_slider_tween:OnComplete(function ()
			MainuiWGCtrl.Instance:DelayShowCachePower(0) --延时调用
            self:PlayUpStarEffect()
		end)

        self:StopUGQCAutoUpStar()
        return
    end

    local info = NewAppearanceWGData.Instance:GetQiChongInfo(self.ug_qc_select_type)
    if not info then
        return
    end

    local real_star_level = info.star_level
    self.ug_qc_slider_tween_func = function (progress)
        self:ClearUGQCSliderTween()

        local before_upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(self.ug_qc_select_type, self.ug_qc_old_star_level)
        if not before_upstar_cfg then
            return
        end
        
        if progress <= 0 then
            if self.is_ug_qc_auto_upstar then
                if self.ug_qc_old_star_level ~= real_star_level then
                    self.ug_qc_old_star_level = real_star_level
                    self:UGQiChongStarLevelChangeFlush(real_star_level, true)
                end

                -- 升阶停止
                -- if self.ug_qc_old_grade ~= before_upstar_cfg.grade_num then
                --     self:StopUGQCAutoUpStar()
                -- else
                    self:StartUGQCAutoUpStar(true)
                -- end
            end

            return
        end

        local is_up_one_star = false
        local slider = self.node_list["ug_qc_progress"].slider
		if progress > 1 then
			local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.1))
			self.ug_qc_slider_tween = slider:DOValue(1, time)
            is_up_one_star = true
            self.node_list["ug_qc_progress_text"].text.text = ToColorStr(before_upstar_cfg.need_bless, COLOR3B.C8) .. "/" .. before_upstar_cfg.need_bless
		else
			local time = tonumber(string.format("%.2f", (progress - slider.value) * 0.1))
			self.ug_qc_slider_tween = slider:DOValue(progress, time)
            self.node_list["ug_qc_progress_text"].text.text = ToColorStr(info.upstar_bless_val, COLOR3B.C8) .. "/" .. before_upstar_cfg.need_bless
		end

        progress = progress - 1
        self.ug_qc_slider_tween:OnComplete(function ()
			if progress >= 0 then
				slider.value = 0
                if is_up_one_star then
                    self.ug_qc_old_star_level = self.ug_qc_old_star_level + 1
                    self:UGQiChongStarLevelChangeFlush(self.ug_qc_old_star_level, true)
                end

                local change_upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(self.ug_qc_select_type, self.ug_qc_old_star_level)
                if change_upstar_cfg then
                    if self.ug_qc_old_grade == change_upstar_cfg.grade_num then
                        self:PlayUpStarEffect()
                    else
                        self:PlayUpGradeEffect()
                    end
                end
			end
			
			if progress < 1 then
				MainuiWGCtrl.Instance:DelayShowCachePower(0)
			end

			self.ug_qc_slider_tween_func(progress)
		end)
    end

    local total_progress = add_star + bless_val / need_bless_val
    self.ug_qc_slider_tween_func(total_progress)
end

function NewAppearanceWGView:OnClickOpenCangJinView()
    ViewManager.Instance:Open(GuideModuleName.CangJinExchangeAttr)
end

function NewAppearanceWGView:OnClickUGQCSXDExtend()
    NewAppearanceWGCtrl.Instance:OpenAppearanceAttrStoreView(-1, self.ug_qc_select_type)
end