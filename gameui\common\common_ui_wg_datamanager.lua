CommonDataManager = CommonDataManager or {}

-- 匹配阶数（文字）
CommonDataManager.DAXIE =  { [0] = "零", "十", "一", "二", "三", "四", "五", "六", "七", "八", "九" }
CommonDataManager.FANTI =  { [0] = "零", "拾", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" }
CommonDataManager.ANCIENT = {"十", "廿", "卅", "卌", "圩", "圆", "进", "枯", "枠"}
CommonDataManager.WEEK =  {"周一", "周二", "周三", "周四", "周五", "周六", "周日"}

local Num_Type = {
	"%.0f",
	"%.2f",
	"%.1f",
	"%.4f",
}

function CommonDataManager.GetDaXie(num, type)
	if nil == num or num < 0 then
		return ""
	end

	if num >= 100 then
		return CommonDataManager.GetDaXieByOverHundred(num, type)
	end

	
	local index1 = num
	local index2 = -1

	if 10 == num then
		index1 = 0
	elseif num > 10 then
		index1 = math.floor(num / 10)
		index1 = (1 == index1) and 0 or index1
		index2 = num % 10
	elseif num == 0 then
		index1 = -1
	end

	local result = ""
	local table = type and CommonDataManager.FANTI or CommonDataManager.DAXIE

	result = table[index1 + 1]
	if num > 20 and index2 ~= 0 then
		result = result .. table[1]
	end
	if index2 > -1 then
		result = result .. table[index2 + 1]
	end

	return result
end

function CommonDataManager.GetAncientNumber(num)
	if nil == num or num < 0 then
		return ""
	end

	if num >= 100 then
		return CommonDataManager.GetDaXieByOverHundred(num)
	end

	local floor_num = math.floor(num / 10)
	local yu_num = num % 10
	local dx_str = CommonDataManager.DAXIE
	local num_str
	if floor_num < 1 then
		if num == 0 then
			num_str = dx_str[0] 
		else
			num_str = dx_str[yu_num + 1]
		end
	elseif yu_num == 0 then
		num_str = dx_str[1]
		if floor_num > 1 then
			num_str = dx_str[floor_num + 1] .. num_str
		end
	else
		num_str = CommonDataManager.ANCIENT[floor_num] .. dx_str[yu_num + 1]
	end

	return num_str
end

function CommonDataManager.GetWeekTextByDay(day)
	if CommonDataManager.WEEK[day] then
		return CommonDataManager.WEEK[day]
	end
end

function CommonDataManager.GetDaXieByOverHundred(num, type)
	if num >= 100 and num < 1000 then
		if tonumber(math.floor((num / 10 % 10)) .. (num % 10)) >= 20 then
			return CommonDataManager.GetDaXie(math.floor(num / 100), type) .. Language.Common.Bai .. CommonDataManager.GetDaXie(tonumber(math.floor((num / 10 % 10)) .. (num % 10)), type)
		elseif tonumber(math.floor((num / 10 % 10)) .. (num % 10)) >= 10 then
			return CommonDataManager.GetDaXie(math.floor(num / 100), type) .. Language.Common.Bai .. CommonDataManager.GetDaXie(1, type) .. CommonDataManager.GetDaXie(tonumber(math.floor((num / 10 % 10)) .. (num % 10)), type)
		elseif tonumber(math.floor((num / 10 % 10)) .. (num % 10)) > 0 then
			return CommonDataManager.GetDaXie(math.floor(num / 100), type) .. Language.Common.Bai .. CommonDataManager.GetDaXie(0, type) .. CommonDataManager.GetDaXie(tonumber(math.floor((num / 10 % 10)) .. (num % 10)), type)
		else
			return CommonDataManager.GetDaXie(math.floor(num / 100), type) .. Language.Common.Bai
		end
	end
end

--转换财富
function CommonDataManager.ConverMoney(value, is_wan, is_zhengshu)
	if nil == is_wan then
		is_wan = true
	end
	if nil == is_zhengshu then
		is_zhengshu = true
	end
	return CommonDataManager.ConverExp(value, is_wan, is_zhengshu)
end

--转换财富以万为单位
function CommonDataManager.ConverMoneyByThousand(value, is_wan, is_zhengshu)
	if value >= 10000 and value < 100000000 then
		local result = math.floor(value / 10000) .. Language.Common.Wan
		return result
	end

	if value >= 100000000 then
		local result = math.floor(value / 100000000) .. Language.Common.Yi
		return result
	end
	return value
end

--转换开服活动战力,保留一位小数
function CommonDataManager.ConverPowerByThousand(value)
	if value >= 10000 and value < 100000000 then
		local result = string.format(Num_Type[3], value / 10000)
		return tonumber(result) .. Language.Common.Wan
	end

	if value >= 100000000 then
		local result = string.format(Num_Type[3], value / 100000000)
		return tonumber(result) .. Language.Common.Yi
	end

	return value
end

--超过100000改为xx万(保留4位小數)
function CommonDataManager.ConverGoldByThousand(value)
	if value >= 100000 and value < 100000000 then
		local result = string.format(Num_Type[4], value / 10000)
		return tonumber(result) .. Language.Common.Wan
	end

	if value >= 100000000 then
		local result = string.format(Num_Type[4], value / 100000000)
		return tonumber(result) .. Language.Common.Yi
	end

	return value
end

--转换xx亿xx万
function CommonDataManager.ConverNumberToThousand2(value)
	if value >= 10000 and value < 100000000 then
		local result = string.format(Num_Type[2], value / 10000)
		return tonumber(result) .. Language.Common.Wan
	end

	if value >= 100000000 then
        local result = math.modf(value / 100000000)
        local result2 = string.format(Num_Type[1], value % 100000000 / 10000)
		return tonumber(result) .. Language.Common.Yi..tonumber(result2)..Language.Common.Wan
	end
	return value
end

--保留两位小数
function CommonDataManager.ConverExpByThousand(value, is_wan, is_zhengshu)
	--string.format(is_zhengshu and Num_Type[1] or Num_Type[2], tonumber(value) / 10000)
	if value >= 10000 and value < 100000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 10000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result .. Language.Common.Wan
	elseif value >= 100000000 and  value < 1000000000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 100000000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result .. Language.Common.Yi
	elseif value >= 1000000000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 1000000000000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result .. Language.Common.Zhao
	end
	return value
end

--保留两位小数
function CommonDataManager.ConverMoneyBar(value, is_wan, is_zhengshu)
	if value >= 10000 and value < 100000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 10000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Wan
	elseif value >= 100000000 and  value < 1000000000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 100000000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Yi
	elseif value >= 1000000000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 1000000000000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Zhao
	end
	return value, ""
end

--保留两位小数 少于100万显示具体数值
function CommonDataManager.ConverMoneyBarNew(value, is_wan, is_zhengshu)
	if value >= 1000000 and value < 100000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 10000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Wan
	elseif value >= 100000000 and  value < 1000000000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 100000000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Yi
	elseif value >= 1000000000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 1000000000000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Zhao
	end
	return value, ""
end

function CommonDataManager.MaxFourNumberValue(number_value)
	local number_table = Split(number_value, "%.")
	local num_leng1 = string.len(number_table[1])
	local num_leng2
	local temp_str = number_table[1]
	if tonumber(number_table[1]) < 10 then
		if number_table[2] then
			temp_str = string.sub(number_table[2], 1, 2)
			temp_str = string.format(Language.Common.NumPinJie, number_table[1], temp_str)
		end
	else
		local float_point = 4 - num_leng1 --4位有效数字
		if number_table[2] and float_point > 0 then
			temp_str = string.sub(number_table[2], 1, float_point)
			temp_str = string.format(Language.Common.NumPinJie, number_table[1], temp_str)
		end
	end
	return tonumber(temp_str)
end

--转换经验  is_wan 是否直接转换万,保留一位小数
function CommonDataManager.ConverNumber(value, is_wan, is_zhengshu)
	if nil == is_wan then
		is_wan = true
	end
	if nil == is_zhengshu then
		is_zhengshu = true
	end
	return CommonDataManager.ConverExpExtend(value, is_wan)
end

--转换经验  is_wan 是否直接转换万
function CommonDataManager.ConverExp(value, is_wan, is_zhengshu)
	if tonumber(value) >= 10000 and tonumber(value) < 100000000 then
		local result = string.format(is_zhengshu and Num_Type[1] or Num_Type[2], tonumber(value) / 10000) .. Language.Common.Wan
		return result
	elseif tonumber(value) >= 100000000 and tonumber(value) < 1000000000000 then
		local result = string.format(is_zhengshu and Num_Type[1] or Num_Type[2], tonumber(value) / 100000000) .. Language.Common.Yi
		return result
	elseif tonumber(value) >= 1000000000000 then
		local result = string.format(is_zhengshu and Num_Type[1] or Num_Type[2], tonumber(value) / 1000000000000).. Language.Common.Zhao
		return result
	end
	return value
end

--转换经验  is_wan 是否直接转换万
function CommonDataManager.ConverExpExtend(value, is_wan)
	if tonumber(value) >= 100000 and tonumber(value) < 100000000 then
		local result = string.format(Num_Type[3], tonumber(value) / 10000)
		result = result * 10 / 10 -- **策划说结尾0不显示出来
		return result .. Language.Common.Wan
	end

	if tonumber(value) >= 100000000 then
		local result = string.format(Num_Type[3], tonumber(value) / 100000000)
		if (tonumber(result) >= 100000) then
			result = CommonDataManager.ConverExpExtend(result,true)
		end
		return result .. Language.Common.Yi
	end
	return value
end

--转换道具数量
function CommonDataManager.ConverNumExtend(value)
	if tonumber(value) >= 10000 and tonumber(value) < 10000000 then
		local result = string.format(Num_Type[1], tonumber(value) / 10000)
		return result .. Language.Common.Wan
	elseif tonumber(value) >= 10000000 and tonumber(value) < 100000000 then
		local result =string.format(Num_Type[1], tonumber(value) / 10000000)
		return result .. Language.Common.QianWan
	elseif tonumber(value) >= 100000000 and tonumber(value) < 100000000000 then
		local result = string.format(Num_Type[1], tonumber(value) / 100000000)
		return result .. Language.Common.Yi
	elseif tonumber(value) >= 100000000000 and tonumber(value) < 1000000000000 then
		local result = string.format(Num_Type[1], tonumber(value) / 100000000000)
		return result .. Language.Common.QianYi
	elseif tonumber(value) >= 1000000000000 then
		local result = math.floor(tonumber(value) / 1000000000000)
		return result .. Language.Common.Zhao
	end

	return value
end

--转换经验  is_wan 是否直接转换万
function CommonDataManager.ConverNumValue(value)
	if tonumber(value) >= 10000 and tonumber(value) < 100000000 then
		return math.floor(tonumber(value) / 10000), Language.Common.Wan
	elseif tonumber(value) >= 100000000 and tonumber(value) < 1000000000000 then
		local result = math.floor(tonumber(value) / 100000000)
		return result, Language.Common.Yi
	elseif tonumber(value) >= 1000000000000 then
		local result = math.floor(tonumber(value) / 1000000000000)
		return result, Language.Common.Zhao
	end
	return value, ""
end

function CommonDataManager.NotConverExtend(value)
	if tonumber(value) >= 10000 and tonumber(value) < 100000000 then
		local result = string.format(Num_Type[3], tonumber(value) / 10000)
		result = result * 10 / 10 -- **策划说结尾0不显示出来
		return result .. Language.Common.Wan
	end

	if tonumber(value) >= 100000000 then
		if tonumber(value) >= 1000000000000 then
			local result = math.floor(tonumber(value) / 1000000000000)
			result = result .. Language.Common.Wan .. Language.Common.Yi
			return result
		else
			local result = string.format(Num_Type[1], tonumber(value) / 100000000)
			if (tonumber(result) >= 100000) then
				result = CommonDataManager.ConverExpExtend(result,true)
			end
			return result .. Language.Common.Yi	
		end
	end
	return value
end

--不保留转换
function CommonDataManager.NotConverExpExtend(value, is_wan)
	if tonumber(value) >= 100000 and tonumber(value) < 100000000 then
		local result = string.format(Num_Type[1], tonumber(value) / 10000) .. Language.Common.Wan
		return result
	end

	if tonumber(value) >= 100000000 then
		local result = string.format(Num_Type[1], tonumber(value) / 100000000)
		if (tonumber(result) >= 100000) then
			result = CommonDataManager.ConverExpExtend(result,true)
		end
		return result .. Language.Common.Yi
	end
	return value
end

function CommonDataManager.GetParamCfg()
	if not CommonDataManager.cap_param then
		CommonDataManager.cap_param = {}
		for k,v in pairs(ConfigManager.Instance:GetAutoConfig("param_cfg_auto").cap_param[1]) do
			CommonDataManager.cap_param[k] = v / 1000000
		end
	end
	return CommonDataManager.cap_param
end

--小数点保留几位数(数值,保留个数)
function CommonDataManager.GetPreciseDecimal(attribute, n)
	for k,v in pairs(attribute) do
		if not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k) then
			attribute[k] = math.floor(v)
		else
			if attribute[k] > 0 then
				n = n or 0;
				n = math.floor(n)
				local fmt = '%.' .. n .. 'f'
				attribute[k] = tonumber(string.format(fmt, v))
			else
				attribute[k] = 0
			end
		end
	end
	return attribute
end

-- 速度换算
function CommonDataManager.CountSpeedForPercent(speed)
	local speed_percent = (speed / COMMON_CONSTS.ROLE_MOVE_SPEED) * 100
	speed_percent = GameMath.Round(speed_percent / 5) * 5

	return speed_percent
end

--=============================新ui控件重写=============================--
function CommonDataManager.ParseTagContent(content, font_size)
	font_size = font_size or 32

	--unity版本的富文本
	content = string.gsub(content,"<font color=","<color=")
	content = string.gsub(content,"</font>","</color>")

	--有名字替换，<player_name>主角</player_name>
	local name = RoleWGData.Instance.role_vo.name--HtmlTool.GetHtml(RoleWGData.Instance.role_vo.name, COLOR3B.YELLOW , font_size)
	content = XmlUtil.RelaceTagContent(content, "player_name", name)

	--有性别替换，<sex0>女娃儿</sex0><sex1>小兄弟</sex1>
	local sex = RoleWGData.Instance.role_vo.sex
	local sex_tag_content = XmlUtil.GetTagContent(content, "sex" .. sex)
	if sex_tag_content ~= nil then
		content = XmlUtil.RelaceTagContent(content, "sex0", sex_tag_content)
		content = XmlUtil.RelaceTagContent(content, "sex1", "")
	end

	--有职业替换，<prof1>职业1</prof1><prof2>职业2</prof2><prof3>职业3</prof3><prof4>职业4</prof4>
	local role_prof = RoleWGData.Instance:GetRoleProf()
	local prof_tag_content = XmlUtil.GetTagContent(content, "prof" .. role_prof)
	if prof_tag_content ~= nil then
		content = XmlUtil.RelaceTagContent(content, "prof1", prof_tag_content)
		content = XmlUtil.RelaceTagContent(content, "prof2", "")
		content = XmlUtil.RelaceTagContent(content, "prof3", "")
		content = XmlUtil.RelaceTagContent(content, "prof4", "")
	end


	local camp = RoleWGData.Instance.role_vo.camp
	local camp_tag_content = XmlUtil.GetTagContent(content, "camp" .. camp)
	if camp_tag_content ~= nil then
		content = XmlUtil.RelaceTagContent(content, "camp1", camp_tag_content)
		content = XmlUtil.RelaceTagContent(content, "camp0", "")
		content = XmlUtil.RelaceTagContent(content, "camp2", "")
		content = XmlUtil.RelaceTagContent(content, "camp3", "")
	end

	return content
end

-- 解析不同平台的游戏名字
function CommonDataManager.ParseGameName(content)
	return string.gsub(content, "{gamename;}", CommonDataManager.GetGameName())
end

function CommonDataManager.GetGameName()
	if nil ~= AgentAdapter.GetGameName then
		return AgentAdapter:GetGameName()
	end
	return Language.Common.GameName[1]
end

function CommonDataManager.GetRandomName(rand_num)
	local name_cfg = ConfigManager.Instance:GetAutoConfig("randname_auto").random_name[1]
	local sex = rand_num % 2

	local name_first_list = {}	-- 前缀
	local name_last_list = {}	-- 后缀
	if sex == GameEnum.FEMALE then
		name_first_list = name_cfg.common_first
		name_last_list = name_cfg.female_last
	else
		name_first_list = name_cfg.common_first
		name_last_list = name_cfg.male_last
	end

	local name_first_index = (rand_num % #name_first_list) + 1
	local name_last_index = (rand_num % #name_last_list) + 1
	local first_name = name_first_list[name_first_index] or ""
	local last_name = name_last_list[name_last_index] or ""
	return first_name .. last_name
end

function CommonDataManager.SetImageActiveData(active_skill_flag,length)
	local tb = {}
	local data_index = 1
	local cacl_t = {}
	for i = 0, length - 1 do
		local active_flag = bit:d2b_two(active_skill_flag[i], cacl_t)
		for k = 1, 8 do
			tb[data_index] = active_flag[k]
			data_index = data_index + 1
		end
	end
	return tb
end

--角色面板数值特殊处理
function CommonDataManager.ConverPlayerPanelAttr(value)
	if value >= 10000000 and value < 100000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 10000)
		local result = string.format(Num_Type[3], value1)
		result = tonumber(result)
		return result, Language.Common.Wan
	elseif value >= 100000000 and  value < 1000000000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 100000000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Yi
	elseif value >= 1000000000000 then
		local value1 = CommonDataManager.MaxFourNumberValue(tonumber(value) / 1000000000000)
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Zhao
	end
	return value, ""
end

--经验本特殊数字 超过亿，保留两位
function CommonDataManager.ConverExpFBNum(value)
	if value >= 1000000000000 then
		local value1 = tonumber(value) / 1000000000000
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Zhao
	elseif value >= 100000000 then
		local value1 = tonumber(value) / 100000000
		local result = string.format(Num_Type[2], value1)
		result = tonumber(result)
		return result, Language.Common.Yi
	end
	return value, ""
end

-- 参数num是数字，如3428439，转换结果"3,428,439"
function CommonDataManager.FormatNumStrWithComma(num)
	local numstr = tostring(num)
	local strlen = string.len(numstr)
	local splitStrArr = {}
	for i = strlen, 1, -3 do
		local beginIndex = (i - 2 >= 1) and (i - 2) or 1
		table.insert(splitStrArr, string.sub(numstr, beginIndex, i))
	end
	
	local cnt = #splitStrArr
	local result = ""
	for i = cnt, 1, -1 do
		if i == cnt then
			result = result .. splitStrArr[i]
		else
			result = result .. "," .. splitStrArr[i]
		end
	end
	return result
end

