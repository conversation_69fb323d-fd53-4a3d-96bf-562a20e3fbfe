require("game/lingzhi/linzhi_skill_wg_data")
require("game/lingzhi/linzhi_ach_view")
require("game/lingzhi/linzhi_skill_view")
require("game/lingzhi/linzhi_skill_sj_view")
require("game/lingzhi/linzhi_skill_xl_view")
require("game/lingzhi/linzhi_skill_tip")
require("game/lingzhi/linzhi_zhigou_tip")

LingZhiWGCtrl = LingZhiWGCtrl or BaseClass(BaseWGCtrl)
function LingZhiWGCtrl:__init()
	if LingZhiWGCtrl.Instance then
		ErrorLog("[LingZhiWGCtrl] Attemp to create a singleton twice !")
	end
	LingZhiWGCtrl.Instance = self
	self.lingzhi_data = LingZhiSkillWGData.New()

	self.lingzhi_wing_view = LinZhiSkillView.New(GuideModuleName.WingLinZhiSkillView)
	self.lingzhi_wing_view:SetViewType(LINGZHI_SKILL_TYPE.WING)
	self.lingzhi_fabao_view = LinZhiSkillView.New(GuideModuleName.FaBaoLinZhiSkillView)
	self.lingzhi_fabao_view:SetViewType(LINGZHI_SKILL_TYPE.FABAO)
	self.lingzhi_jianzhen_view = LinZhiSkillView.New(GuideModuleName.JianZhenLinZhiSkillView)
	self.lingzhi_jianzhen_view:SetViewType(LINGZHI_SKILL_TYPE.JIANZHEN)
	self.lingzhi_shenbing_view = LinZhiSkillView.New(GuideModuleName.ShenBingLinZhiSkillView)
	self.lingzhi_shenbing_view:SetViewType(LINGZHI_SKILL_TYPE.SHENBING)

	self.lingzhi_ach_view = LinZhiAchView.New(GuideModuleName.LinZhiAchView)
	self.lingzhi_skill_tip = LingZhiSkillTips.New(GuideModuleName.LingZhiSkillTips)
	self.lingzhi_zhigou_tip = LingZhiZhiGouTips.New(GuideModuleName.LingZhiZhiGouTips)

	-- 功能开启监听
	-- self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))

	self:RegisterAllProtocols()
end


function LingZhiWGCtrl:__delete()
	LingZhiWGCtrl.Instance = nil

	if self.lingzhi_zhigou_tip ~= nil then
		self.lingzhi_zhigou_tip:DeleteMe()
		self.lingzhi_zhigou_tip = nil
	end

	if self.lingzhi_skill_tip ~= nil then
		self.lingzhi_skill_tip:DeleteMe()
		self.lingzhi_skill_tip = nil
	end

	if self.lingzhi_data ~= nil then
		self.lingzhi_data:DeleteMe()
		self.lingzhi_data = nil
	end

	if self.lingzhi_wing_view ~= nil then
		self.lingzhi_wing_view:DeleteMe()
		self.lingzhi_wing_view = nil
	end

	if self.lingzhi_fabao_view ~= nil then
		self.lingzhi_fabao_view:DeleteMe()
		self.lingzhi_fabao_view = nil
	end

	if self.lingzhi_shenbing_view ~= nil then
		self.lingzhi_shenbing_view:DeleteMe()
		self.lingzhi_shenbing_view = nil
	end

	if self.lingzhi_jianzhen_view ~= nil then
		self.lingzhi_jianzhen_view:DeleteMe()
		self.lingzhi_jianzhen_view = nil
	end

	if self.lingzhi_ach_view ~= nil then
		self.lingzhi_ach_view:DeleteMe()
		self.lingzhi_ach_view = nil
	end

	-- GlobalEventSystem:UnBind(self.open_fun_change)
end

function LingZhiWGCtrl:RegisterAllProtocols()
	-- 灵智
	self:RegisterProtocol(CSQiSystemReq)
	self:RegisterProtocol(SCQiHunSystemAllInfo, "SCLingZhiSkillAllInfo")
	self:RegisterProtocol(SCQiHunSystemSingleTypeInfo, "SCLingZhiSkillSingleTypeInfo")
end


function LingZhiWGCtrl:OpenLingZhiAchView(data)
	self.lingzhi_ach_view:SetData(data)
end

function LingZhiWGCtrl:OpenLingZhiSkillTip(data)
	self.lingzhi_skill_tip:SetData(data)
end

function LingZhiWGCtrl:OpenLingZhiZhiGouTip(data)
	self.lingzhi_zhigou_tip:SetData(data)
end

function LingZhiWGCtrl:CSLingZhiSkillReq(opera_type, param1, param2)
	-- print_error('CSLingZhiSkillReq-----',opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQiSystemReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or -1
	protocol.param2 = param2 or -1
	protocol:EncodeAndSend()
end

function LingZhiWGCtrl:SCLingZhiSkillAllInfo(protocol)
	self.lingzhi_data:SetAllServerInfo(protocol)

	RemindManager.Instance:Fire(RemindName.LingZhi_Wing_SJ)
	RemindManager.Instance:Fire(RemindName.LingZhi_FaBao_SJ)
	RemindManager.Instance:Fire(RemindName.LingZhi_JianZhen_SJ)
	RemindManager.Instance:Fire(RemindName.LingZhi_ShenBing_SJ)
	RemindManager.Instance:Fire(RemindName.LingZhi_Wing_XL)
	RemindManager.Instance:Fire(RemindName.LingZhi_FaBao_XL)
	RemindManager.Instance:Fire(RemindName.LingZhi_JianZhen_XL)
	RemindManager.Instance:Fire(RemindName.LingZhi_ShenBing_XL)
	RemindManager.Instance:Fire(RemindName.LingZhi_Wing_Act)
	RemindManager.Instance:Fire(RemindName.LingZhi_FaBao_Act)
	RemindManager.Instance:Fire(RemindName.LingZhi_JianZhen_Act)
	RemindManager.Instance:Fire(RemindName.LingZhi_ShenBing_Act)
end

function LingZhiWGCtrl:SCLingZhiSkillSingleTypeInfo(protocol)
	self.lingzhi_data:SetSingleServerInfo(protocol)

	local param_name = 'all'
	local linzhi_param = nil
	if protocol.reason == LINGZHI_SKILL_REASON_TYPE.SEND_REASON_FETCH_XIULIAN_CHENGJIU then
		param_name = "xiulian_eff"
	elseif protocol.reason == LINGZHI_SKILL_REASON_TYPE.SEND_REASON_FETCH_WAIGUAN_CHENGJIU then
		param_name = "waiguan_eff"
	elseif protocol.reason == LINGZHI_SKILL_REASON_TYPE.SEND_REASON_FUWEN_UPLEVEL then
		linzhi_param = {}
		linzhi_param.play_sj_effect =  true
	elseif protocol.reason == LINGZHI_SKILL_REASON_TYPE.SEND_REASON_QIHUN_FETCH then
		ViewManager.Instance:Close(GuideModuleName.LingZhiZhiGouTips)
	end

	ViewManager.Instance:FlushView(GuideModuleName.LinZhiAchView,nil,param_name)
	ViewManager.Instance:FlushView(GuideModuleName.RoleView)
	ViewManager.Instance:FlushView(GuideModuleName.LingZhiZhiGouTips)
	self:FlushLingZhiView(linzhi_param)

	RemindManager.Instance:Fire(RemindName.LingZhi_Wing_SJ)
	RemindManager.Instance:Fire(RemindName.LingZhi_FaBao_SJ)
	RemindManager.Instance:Fire(RemindName.LingZhi_JianZhen_SJ)
	RemindManager.Instance:Fire(RemindName.LingZhi_Wing_XL)
	RemindManager.Instance:Fire(RemindName.LingZhi_FaBao_XL)
	RemindManager.Instance:Fire(RemindName.LingZhi_JianZhen_XL)
	RemindManager.Instance:Fire(RemindName.LingZhi_ShenBing_SJ)
	RemindManager.Instance:Fire(RemindName.LingZhi_ShenBing_XL)
	RemindManager.Instance:Fire(RemindName.LingZhi_Wing_Act)
	RemindManager.Instance:Fire(RemindName.LingZhi_FaBao_Act)
	RemindManager.Instance:Fire(RemindName.LingZhi_JianZhen_Act)
	RemindManager.Instance:Fire(RemindName.LingZhi_ShenBing_Act)
end

function LingZhiWGCtrl:FlushLingZhiView(param_t)
	if ViewManager.Instance:IsOpen(GuideModuleName.WingLinZhiSkillView) then
		ViewManager.Instance:FlushView(GuideModuleName.WingLinZhiSkillView, nil, nil, param_t)
	elseif ViewManager.Instance:IsOpen(GuideModuleName.FaBaoLinZhiSkillView) then
		ViewManager.Instance:FlushView(GuideModuleName.FaBaoLinZhiSkillView, nil, nil, param_t)
	elseif ViewManager.Instance:IsOpen(GuideModuleName.ShenBingLinZhiSkillView) then
		ViewManager.Instance:FlushView(GuideModuleName.ShenBingLinZhiSkillView, nil, nil, param_t)
	elseif ViewManager.Instance:IsOpen(GuideModuleName.JianZhenLinZhiSkillView) then
		ViewManager.Instance:FlushView(GuideModuleName.JianZhenLinZhiSkillView, nil, nil, param_t)
	end
end

function LingZhiWGCtrl:OpenLingZhiView(lingzhi_type,tab_index,key,values)
	if lingzhi_type == LINGZHI_SKILL_TYPE.WING then
		ViewManager.Instance:Open(GuideModuleName.WingLinZhiSkillView,tab_index,key,values)
	elseif lingzhi_type == LINGZHI_SKILL_TYPE.FABAO then
		ViewManager.Instance:Open(GuideModuleName.FaBaoLinZhiSkillView,tab_index,key,values)
	elseif lingzhi_type == LINGZHI_SKILL_TYPE.JIANZHEN then
		ViewManager.Instance:Open(GuideModuleName.JianZhenLinZhiSkillView,tab_index,key,values)
	elseif lingzhi_type == LINGZHI_SKILL_TYPE.SHENBING then
		ViewManager.Instance:Open(GuideModuleName.ShenBingLinZhiSkillView,tab_index,key,values)
	end
end

--功能开启，红点监听
function LingZhiWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
	if not check_all and (fun_name == FunName.WingLinZhiSkillView or fun_name == FunName.FaBaoLinZhiSkillView 
		or fun_name == FunName.ShenBingLinZhiSkillView or fun_name == FunName.JianZhenLinZhiSkillView) and is_open then
		local all_fun_checked = FunOpen.Instance:GetAllFunChecked()
		local has_server_time_flag = TimeWGCtrl.Instance:GetHasServerTimeFlag()
		if not all_fun_checked or not has_server_time_flag then return end
		local lingzhi_type = 0
		if fun_name == FunName.WingLinZhiSkillView then
			lingzhi_type = LINGZHI_SKILL_TYPE.WING
		elseif fun_name == FunName.FaBaoLinZhiSkillView then
			lingzhi_type = LINGZHI_SKILL_TYPE.FABAO
		elseif fun_name == FunName.JianZhenLinZhiSkillView then
			lingzhi_type = LINGZHI_SKILL_TYPE.JIANZHEN
		elseif fun_name == FunName.ShenBingLinZhiSkillView then
			lingzhi_type = LINGZHI_SKILL_TYPE.SHENBING
		end
		self.lingzhi_data:SetLoginRemind(lingzhi_type,true)
	end

end