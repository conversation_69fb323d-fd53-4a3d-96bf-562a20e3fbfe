TransFerAttrTipsView = TransFerAttrTipsView or BaseClass(SafeBaseView)
function TransFerAttrTipsView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
    self.view_name = "TransFerAttrTipsView"
    self:AddViewResource(0, "uis/view/tansfer_ui_prefab", "layout_transfer_attr_tip")
end

function TransFerAttrTipsView:LoadCallBack()
	local attr_group_obj = self.node_list["attr_group"].transform
	self.attr_group_list = {}
	for i=1,9 do
		local item = {}
		local str = "ph_attr_cell_"..i.."/"
		item.attr_cell = U3DObject(attr_group_obj:Find("ph_attr_cell_"..i).gameObject, attr_group_obj:Find("ph_attr_cell_"..i), self)
		item.attr_name = U3DObject(attr_group_obj:Find(str.."attr_name").gameObject, attr_group_obj:Find(str.."attr_name"), self)
		item.attr_val = U3DObject(attr_group_obj:Find(str.."attr_val").gameObject, attr_group_obj:Find(str.."attr_val"), self)
		item.arrow = U3DObject(attr_group_obj:Find(str.."arrow").gameObject, attr_group_obj:Find(str.."arrow"), self)
		self.attr_group_list[i] = item
	end
	self.node_list["title"].text.text = Language.TransFer.AttrTitle1
end

function TransFerAttrTipsView:OnFlush()
	local zhuan_num = RoleWGData.Instance:GetZhuanZhiNumber()
	local next_attr_cfg = TransFerWGData.Instance:GetAttrCfgByZhuanNum(self.index, self.stage)
	local sort_attr = AttributeMgr.SortAttribute()
	local num = 1
	for k,v in pairs(sort_attr) do
		if next_attr_cfg[v] and next_attr_cfg[v] > 0 then
			if self.attr_group_list[num] then
				self.attr_group_list[num].attr_cell:SetActive(true)
				self.attr_group_list[num].arrow:SetActive(false)
				self.attr_group_list[num].attr_val:SetActive(true)
				local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
				local attr_val = next_attr_cfg[v]
				if is_per then
					attr_val = (attr_val / 100) .. "%"
				end
				--local val_str = ToColorStr(attr_val, COLOR3B.DEFAULT_NUM)
				self.attr_group_list[num].attr_name.text.text = Language.Common.AttrNameList[v] --..val_str --.. " +" .. attr_cfg[v]
				self.attr_group_list[num].attr_val.text.text = attr_val
				num = num + 1
			end
		end
	end

	for i = num,#self.attr_group_list do
		if self.attr_group_list[i] then
			self.attr_group_list[i].attr_cell:SetActive(false)
		end
	end
end

function TransFerAttrTipsView:SetData(index,stage)
	self.index = index
	self.stage = stage
end
