TianShenHeJiSkillShowView = TianShenHeJiSkillShowView or BaseClass(CommonSkillShowView)

function TianShenHeJiSkillShowView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)
    
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "skill_show_bg_scene")
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_ts_hj_skill_show_view")
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_skill_desc_view")
end

function TianShenHeJiSkillShowView:LoadCallBack()
    self:InitShower()
	self:InitEnemy()
    self:InitSkillInfo()

    self.cur_select_jb_index = -1
    if not self.jiban_show_list then
        self.jiban_show_list = AsyncListView.New(SkillShowTSJBRender, self.node_list["jb_list_view"])
        self.jiban_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectJiBanCallBack, self))
        self.jiban_show_list:SetLimitSelectFunc(BindTool.Bind(self.IsLimitClick, self))
    end

    self.skill_btn = SkillShowSkillRender.New(self.node_list.skill_btn)
    self.skill_btn:SetNeedChangeSkillBtnPos(false)
    self.skill_btn:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self, self.skill_btn))
    self:TrySetCamera()
    self:ChangeViewDisplay(7100)
end

function TianShenHeJiSkillShowView:ReleaseCallBack()
    self:CleanSkillBtnCDTimer()
    self:CleanAllSummonObjCDTimer()
    CommonSkillShowView.ReleaseCallBack(self)

    self.cur_select_jb_index = -1

    if self.jiban_show_list then
        self.jiban_show_list:DeleteMe()
        self.jiban_show_list = nil
    end

    if self.skill_btn then
        self.skill_btn:DeleteMe()
        self.skill_btn = nil
    end
end 

--[[
	data = {
        jiban_seq = 0,
	}
]]
function TianShenHeJiSkillShowView:SetShowDataAndOpen(data)
	self.show_data = data
	self:Open()
end

function TianShenHeJiSkillShowView:ShowIndexCallBack()
    self:ResetCameraFieldOfView()
end

-- 列表选择返回
function TianShenHeJiSkillShowView:OnSelectJiBanCallBack(item)
    if self:IsLimitClick() then
        return
    end

	if nil == item or nil == item.data then
		return
	end

    local data = item.data
    local jiban_seq = data.seq
    if self.cur_select_jb_index == jiban_seq then
        return
    end

    self.cur_select_jb_index = jiban_seq
    -- 刷新技能格子
    self.skill_btn:SetData({skill_id = data.tianshen_heji_skill_id, skill_level = 1})

    local skill_data = SkillWGData.Instance:GetSkillClientConfig(data.tianshen_heji_skill_id, 1)
    local skill_cfg = SkillWGData.Instance:GetTianShenHeJiSkillById(data.tianshen_heji_skill_id, 1)
    if skill_data or skill_cfg then
        self:FlushSkillInfo(data.tianshen_heji_skill_id, 1, skill_cfg.skill_name, skill_data.description)
    end
end


-- 点击技能
function TianShenHeJiSkillShowView:OnClickSkillBtn(item)
    if not self.is_shower_loaded or not self.is_enemy_loaded then
		return
	end

    if self:IsLimitClick() then
		return
	end

    local data = item:GetData()
    if not data then
        return
    end

    self:CleanSkillBtnCDTimer()
    self.show_data.skill_id = data.skill_id
    self.show_data.level = data.skill_level
    self:ChangeViewDisplay(data.skill_id)

    -- 技能按钮CD
    local skill_show_time = 6
    self.skill_play_timestemp = Status.NowTime + skill_show_time
    self:SetAllSkillBtnCD(string.format("%.1f", skill_show_time), skill_show_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(skill_show_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), skill_show_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, skill_show_time)
        end
    )

    self:SimulateSummonObj()
end

-- 当召唤物全创建
function TianShenHeJiSkillShowView:OnAllSummonLoaded()
    -- 释放技能
    self:SimulateSpecialSkillOpera()
    local max_time = 0
    for k,v in pairs(self.summon_obj_list) do
        local obj = v.obj
        if obj:IsTianShenMonsterRes() then
            local boss_cfg = BossWGData.Instance:GetMonsterInfo(obj.vo.monster_id)
            if boss_cfg then
                obj:DoAttackForNoTarget(boss_cfg.skillid, 0)
            end
        end

        if v.alive_time and v.alive_time > max_time then
            max_time = v.alive_time
        end
    end

    self:CleanAllSummonObjCDTimer()
    if max_time > 0 then
        self.all_summon_obj_remove_timer = GlobalTimerQuest:AddDelayTimer(function ()
            self:CleanSummonObj()
        end, max_time)
    end
end

-- 延迟移除召唤物
function TianShenHeJiSkillShowView:CleanAllSummonObjCDTimer()
    if self.all_summon_obj_remove_timer then
        GlobalTimerQuest:CancelQuest(self.all_summon_obj_remove_timer)
        self.all_summon_obj_remove_timer = nil
    end
end

function TianShenHeJiSkillShowView:SetAllSkillBtnCD(time, total_time)
    if self.skill_btn then
        self.skill_btn:SetSkillBtnCD(time, total_time)
    end
end

-- 技能按钮倒计时
function TianShenHeJiSkillShowView:CleanSkillBtnCDTimer()
    if self.skill_btn_cd_timer and CountDown.Instance:HasCountDown(self.skill_btn_cd_timer) then
        CountDown.Instance:RemoveCountDown(self.skill_btn_cd_timer)
        self.skill_btn_cd_timer = nil
    end
end


function TianShenHeJiSkillShowView:IsLimitClick(no_tips)
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp
    if not no_tips and is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end

function TianShenHeJiSkillShowView:OnFlush()
    if not self.show_data then
        return
    end

    local jb_list = TianShenWGData.Instance:GetTianShenHeJiList()
    if IsEmptyTable(jb_list) then
        return
    end

    if self.jiban_show_list then
        self.jiban_show_list:SetDataList(jb_list)
    end

    local show_jiban_seq = self.show_data.jiban_seq
    if self.cur_select_jb_index ~= show_jiban_seq then
        local jump_index = 1
        for k,v in pairs(jb_list) do
            if show_jiban_seq == v.seq then
                jump_index = k
                break
            end
        end

        self.jiban_show_list:JumpToIndex(jump_index)
    end
end















--===================================================================
SkillShowTSJBRender = SkillShowTSJBRender or BaseClass(BaseRender)
function SkillShowTSJBRender:OnFlush()
    local bundle, asset = ResPath.GetNoPackPNG("a2_ts_hj_icon_" .. self.data.seq)
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)
end

function SkillShowTSJBRender:OnSelectChange(is_select)
    self.node_list["select_bg"]:SetActive(is_select)
end