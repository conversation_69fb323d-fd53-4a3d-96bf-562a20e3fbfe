NewAppearanceColorfulWGData = NewAppearanceColorfulWGData or BaseClass()

function NewAppearanceColorfulWGData:__init()
    if NewAppearanceColorfulWGData.Instance then
		error("[NewAppearanceWGData] Attempt to create singleton twice!")
		return
	end

	NewAppearanceColorfulWGData.Instance = self
	self:InitCfg()
end

function NewAppearanceColorfulWGData:__delete()
    NewAppearanceColorfulWGData.Instance = nil
end

-- 初始化配配置
function NewAppearanceColorfulWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("xuancai_extend_cfg_auto")
	if cfg then
		self.shizhuang_extend_cfg = ListToMap(cfg.shizhuang_extend, "part_type", "old_index", "star_level")
		self.shizhuang_extend_cfg2 = ListToMap(cfg.shizhuang_extend, "part_type", "index")
		self.shizhuang_extend_cfg3 = ListToMap(cfg.shizhuang_extend, "resouce", "part_type", "old_index")

		self.mount_extend_cfg = ListToMap(cfg.mount_extend, "old_image_id", "star_level")
		self.mount_extend_cfg1 = ListToMap(cfg.mount_extend, "appe_image_id")
		self.lingchong_extend_cfg = ListToMap(cfg.lingchong_extend, "old_image_id", "star_level")
		self.lingchong_extend_cfg1 = ListToMap(cfg.lingchong_extend, "appe_image_id")
		self.kun_extend_cfg = ListToMap(cfg.kun_extend, "old_kun_id", "star_level")
		self.kun_extend_cfg1 = ListToMap(cfg.kun_extend, "appe_image_id")
	end
end

--------------------------------------时装-------------------------------------
-- 获取当前时装的所有可炫彩列表
function NewAppearanceColorfulWGData:GetShiZhuangExtendList(part_type, old_index)
    local emtry = {}
    return ((self.shizhuang_extend_cfg or emtry)[part_type] or emtry)[old_index] or nil
end

-- 获取当前时装炫彩部件配置(依据之前的id)
function NewAppearanceColorfulWGData:GetShiZhuangExtendCfg(part_type, old_index, star_level)
    local emtry = {}
    return (((self.shizhuang_extend_cfg or emtry)[part_type] or emtry)[old_index] or {})[star_level]
end

-- 获取当前时装炫彩部件配置(依据炫彩id)
function NewAppearanceColorfulWGData:GetShiZhuangExtendCfg2(part_type, new_index)
    local emtry = {}
    return ((self.shizhuang_extend_cfg2 or emtry)[part_type] or emtry)[new_index] or nil
end

-- 获取当前形象对应的炫彩配置(依据形象id)
function NewAppearanceColorfulWGData:GetShiZhuangExtendCfg3(resouce, part_type, old_index)
    local emtry = {}
    return (((self.shizhuang_extend_cfg3 or emtry)[resouce] or emtry)[part_type] or emtry)[old_index]
end

--------------------------------------时装end-------------------------------------
--------------------------------------坐骑-------------------------------------
-- 获取当前坐骑的所有可炫彩列表
function NewAppearanceColorfulWGData:GetMountExtendList(old_image_id)
    local emtry = {}
    return (self.mount_extend_cfg or emtry)[old_image_id] or emtry
end

-- 获取当前坐骑炫彩部件配置(依据之前的id)
function NewAppearanceColorfulWGData:GetMountExtendCfg(old_image_id, star_level)
    local emtry = {}
    return ((self.mount_extend_cfg or emtry)[old_image_id] or emtry)[star_level] or nil
end

-- 获取当前坐骑炫彩部件配置(依据新的appimage_id)
function NewAppearanceColorfulWGData:GetMountExtendCfg2(appe_image_id)
    local emtry = {}
    return (self.mount_extend_cfg1 or emtry)[appe_image_id] or nil
end
--------------------------------------坐骑end-------------------------------------

--------------------------------------灵宠-------------------------------------
-- 获取当前灵宠的所有可炫彩列表
function NewAppearanceColorfulWGData:GetLingChongExtendList(old_image_id)
    local emtry = {}
    return (self.lingchong_extend_cfg or emtry)[old_image_id] or emtry
end

-- 获取当前灵宠炫彩部件配置(依据之前的id)
function NewAppearanceColorfulWGData:GetLingChongExtendCfg(old_image_id, star_level)
    local emtry = {}
    return ((self.lingchong_extend_cfg or emtry)[old_image_id] or emtry)[star_level] or nil
end

-- 获取当前灵宠炫彩部件配置(依据新的appimage_id)
function NewAppearanceColorfulWGData:GetLingChongExtendCfg2(appe_image_id)
    local emtry = {}
    return (self.lingchong_extend_cfg or emtry)[appe_image_id] or nil
end
--------------------------------------灵宠end-------------------------------------

--------------------------------------鲲-------------------------------------
-- 获取当前鲲的所有可炫彩列表
function NewAppearanceColorfulWGData:GetKunExtendList(old_image_id)
    local emtry = {}
    return (self.mount_extend_cfg or emtry)[old_image_id] or emtry
end
-- 获取当前坐骑炫彩部件配置(依据之前的id)
function NewAppearanceColorfulWGData:GeHuaKunExtendCfg(old_image_id, star_level)
    local emtry = {}
    return ((self.mount_extend_cfg or emtry)[old_image_id] or emtry)[star_level] or nil
end

-- 获取当前坐骑炫彩部件配置(依据新的appimage_id)
function NewAppearanceColorfulWGData:GetHuaKunExtendCfg2(appe_image_id)
    local emtry = {}
    return (self.mount_extend_cfg1 or emtry)[appe_image_id] or nil
end
--------------------------------------鲲end-------------------------------------

--- 总入口(返回当前的新id, 和新的炫彩resouce, 不存在或者未达到条件则返回当前默认值)(正推)
--- 获取时装炫彩部件配置信息
---param1 参考枚举 WARDROBE_PART_TYPE
---param2 时装部件类型 参考枚举 SHIZHUANG_TYPE, 时装传入值,其他不需要传值或者0 part_type,如果是时装则是parttype, 如果是别的则是appimage_id
---param3 对应表中的index, image_id, active_id
---param4 当前时装达到炫彩的星级条件
function NewAppearanceColorfulWGData:GetAppearanceColorfulCfgByOld(appearance_type, part_type, old_index, star_level, old_resouce)
	local new_index = old_index
	local new_resouce = old_resouce
	local fashion_cfg = nil

	if appearance_type == WARDROBE_PART_TYPE.FASHION then			-- 时装
		fashion_cfg = self:GetShiZhuangExtendCfg(part_type, old_index, star_level)
		if fashion_cfg then
			new_index = fashion_cfg.index
			new_resouce = fashion_cfg.resouce
		end
	elseif appearance_type == WARDROBE_PART_TYPE.MOUNT then			-- 坐骑
		fashion_cfg = self:GetMountExtendCfg(part_type, star_level)
		if fashion_cfg then
			new_index = 0
			new_resouce = fashion_cfg.appe_image_id
		end
	elseif appearance_type == WARDROBE_PART_TYPE.LING_CHONG then	-- 灵宠
		fashion_cfg = self:GetLingChongExtendCfg(part_type, star_level)
		if fashion_cfg then
			new_index = 0
			new_resouce = fashion_cfg.appe_image_id
		end
	elseif appearance_type == WARDROBE_PART_TYPE.HUA_KUN then		-- 鲲
		fashion_cfg = self:GeHuaKunExtendCfg(part_type, star_level)
		if fashion_cfg then
			new_index = 0
			new_resouce = fashion_cfg.appe_image_id
		end
	elseif appearance_type == WARDROBE_PART_TYPE.XIAN_WA then		-- 崽崽
	end

	return new_index, new_resouce
end

--- 总入口(返回之前套装id, 不存在或者未达到条件则返回当前默认值)(反推当前穿戴的是否是炫彩)
--- 获取时装炫彩部件配置信息
---param1 参考枚举 WARDROBE_PART_TYPE
---param2 时装部件类型 参考枚举 SHIZHUANG_TYPE, 时装传入值,其他不需要传值或者0
---param3 对应炫彩表中的index
function NewAppearanceColorfulWGData:GetAppearanceColorfulCfgByXuanCai(appearance_type, part_type, xuan_cai_index)
	local old_index = xuan_cai_index
	local is_xuancai = false

	if appearance_type == WARDROBE_PART_TYPE.FASHION then			-- 时装
		local fashion_cfg = self:GetShiZhuangExtendCfg2(part_type, xuan_cai_index)
		if fashion_cfg then
			old_index = fashion_cfg.old_index
			is_xuancai = true
		end
	elseif appearance_type == WARDROBE_PART_TYPE.MOUNT then			-- 坐骑
	elseif appearance_type == WARDROBE_PART_TYPE.LING_CHONG then	-- 灵宠
	elseif appearance_type == WARDROBE_PART_TYPE.HUA_KUN then		-- 鲲
	elseif appearance_type == WARDROBE_PART_TYPE.XIAN_WA then		-- 崽崽
	end

	return is_xuancai, old_index
end

--- 组装炫彩列表
---param1 参考枚举 WARDROBE_PART_TYPE
---param2 时装部件类型 参考枚举 SHIZHUANG_TYPE, 时装传入part_type,其他的则是image_id
---param3 对应表中的index, 时装传入值,其他不需要传值或者0
function NewAppearanceColorfulWGData:AssembleColorfulList(appearance_type, part_type, old_index, star_level)
	local list = nil

	if appearance_type == WARDROBE_PART_TYPE.FASHION then			-- 时装
		list = self:AssembleFashionList(part_type, old_index, star_level)
	elseif appearance_type == WARDROBE_PART_TYPE.MOUNT then			-- 坐骑
		list = self:AssembleMountLingChongList(part_type, star_level, MOUNT_LINGCHONG_APPE_TYPE.MOUNT, WARDROBE_PART_TYPE.MOUNT)
	elseif appearance_type == WARDROBE_PART_TYPE.LING_CHONG then	-- 灵宠
		list = self:AssembleMountLingChongList(part_type, star_level, MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, WARDROBE_PART_TYPE.LING_CHONG)
	elseif appearance_type == WARDROBE_PART_TYPE.HUA_KUN then		-- 鲲
		list = self:AssembleHuaKunChongList(part_type, star_level)
	elseif appearance_type == WARDROBE_PART_TYPE.XIAN_WA then		-- 崽崽
	end

	if list then
		table.sort(list, SortTools.KeyLowerSorter("sort_index"))
	end

	return list
end

-- 组装时装列表
function NewAppearanceColorfulWGData:AssembleFashionList(part_type, old_index, star_level)
	local list = {}
	local used_index = NewAppearanceWGData.Instance:GetFashionUseIndex(part_type)
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(part_type, old_index)
	local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(part_type, old_index)

	if fashion_cfg then
		table.insert(list, self:AssembleData(WARDROBE_PART_TYPE.FASHION, fashion_cfg, fashion_cfg.index == used_index, star_level, is_act, 0))
	end

	local new_list = self:GetShiZhuangExtendList(part_type, old_index)
	if new_list then
		for _, cfg_data in pairs(new_list) do
			table.insert(list, self:AssembleData(
					WARDROBE_PART_TYPE.FASHION, 
					cfg_data, 
					cfg_data.index == used_index, 
					star_level, 
					is_act, 
					cfg_data.sort_index
				)
			)
		end
	end

	return list
end

-- 组装坐骑列表
function NewAppearanceColorfulWGData:AssembleMountLingChongList(old_index, star_level, select_type, wardrobe_type)
	local list = {}
	local all_info = NewAppearanceWGData.Instance:GetQiChongInfo(select_type)
	local is_act = NewAppearanceWGData.Instance:GetSpecialQiChongIsAct(select_type, old_index)
	local fashion_cfg = nil

	if wardrobe_type == WARDROBE_PART_TYPE.MOUNT then
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(old_index)
	else
		fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(old_index)
	end

	local used_imageid = all_info and all_info.used_imageid or 0

	if fashion_cfg then
		table.insert(list, self:AssembleData(wardrobe_type, fashion_cfg, fashion_cfg.appe_image_id == used_imageid, star_level, is_act, 0))
	end

	local new_list = nil

	if wardrobe_type == WARDROBE_PART_TYPE.MOUNT then
		new_list = self:GetMountExtendList(old_index)
	else
		new_list = self:GetLingChongExtendList(old_index)
	end

	if new_list then
		for _, cfg_data in pairs(new_list) do
			table.insert(list, self:AssembleData(
				wardrobe_type, 
				cfg_data, 
				cfg_data.appe_image_id == used_imageid, 
				star_level, 
				is_act,
				cfg_data.sort_index)
			)
		end
	end

	return list
end

-- 组装坐骑列表
function NewAppearanceColorfulWGData:AssembleHuaKunChongList(old_index, star_level)
	local list = {}
	local used_id = NewAppearanceWGData.Instance:GetKunUsedId()
	local is_act = NewAppearanceWGData.Instance:GetSpecialQiChongIsAct(MOUNT_LINGCHONG_APPE_TYPE.KUN, old_index)
	local fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(old_index)

	if fashion_cfg then
		table.insert(list, self:AssembleData(WARDROBE_PART_TYPE.HUA_KUN, fashion_cfg, fashion_cfg.id == used_id, star_level, is_act, 0))
	end

	local new_list = self:GetKunExtendList(old_index)
	if new_list then
		for _, cfg_data in pairs(new_list) do
			table.insert(list, self:AssembleData(
					WARDROBE_PART_TYPE.HUA_KUN, 
					cfg_data, 
					cfg_data.appe_image_id == used_id, 
					star_level, 
					is_act,
					cfg_data.sort_index
				)
			)
		end
	end

	return list
end


-- 组装时装列表
function NewAppearanceColorfulWGData:AssembleData(appearance_type, cfg, is_select, star_level, is_act, sort_index)
	local fashion_data = {}
	local cfg_level = cfg and cfg.star_level or 0
	local part_level = star_level or 0

	fashion_data.appearance_type = appearance_type
	fashion_data.cfg_data = cfg
	fashion_data.is_select = is_select
	fashion_data.is_unlock = part_level >= cfg_level and is_act
	fashion_data.sort_index = sort_index or 0
	return fashion_data
end




