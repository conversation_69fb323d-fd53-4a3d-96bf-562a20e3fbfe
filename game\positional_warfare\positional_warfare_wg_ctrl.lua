--[[
    CROSS_LAND_WAR_GM_TYPE = {
        SET_TIRED = 101,              //设置疲劳值        param1:tired
        SET_DEVOTE = 102,				// 设置贡献值		param1:devote
	    SET_DEVOTE_FLAG = 103,		// 设置贡献奖励标记  param1:flag
	    SET_SPECIAL_FLAG = 104,		// 设置特殊标记		 param1:bit			param2:flag
	    SET_SHOP_SCORE = 105,			// 设置商店积分		 param1:shop_score
	    SET_SHOP_TIMES = 106,			// 设置商店购买次数   param1:seq			param2:times
        MATCH = 107,					// 进行匹配			param1:is_reset
        SET_LAND_OWNER = 108,			// 设置领地归属		param1:land_seq		param2:owner_camp
    }

    /jy_gm crosslandwargmoperate:1 0 0 0 0

    23290   跨服阵地战 - 疲劳
    23291   跨服阵地战 - 贡献
    29292   跨服阵地战 - 商店积分

--]]

require("game/positional_warfare/positional_warfare_wg_data")
require("game/positional_warfare/positional_warfare_view")
require("game/positional_warfare/positional_warfare_attributive_reward_view")
require("game/positional_warfare/positional_warfare_buy_zhanling_level_view")
require("game/positional_warfare/positional_warfare_camp_list_view")
require("game/positional_warfare/positional_warfare_defeat_record_view")
require("game/positional_warfare/positional_warfare_gameplay_description_view")
require("game/positional_warfare/positional_warfare_information_view")
require("game/positional_warfare/positional_warfare_mall_view")
require("game/positional_warfare/positional_warfare_pay_unlock_view")
require("game/positional_warfare/positional_warfare_rank_view")
require("game/positional_warfare/positional_warfare_scene_view")
require("game/positional_warfare/positional_warfare_stage_description_view")
require("game/positional_warfare/positional_warfare_treasure_view")
require("game/positional_warfare/positional_warfare_unlock_zhanling_view")
require("game/positional_warfare/positional_warfare_war_situation_view")
require("game/positional_warfare/positional_warfare_zhanling_view")
require("game/positional_warfare/positional_warfare_score_reward_view")
require("game/positional_warfare/positional_warfare_act_tip")
require("game/positional_warfare/positional_warfare_camp_show_view")
require("game/positional_warfare/positional_warfare_explain_view")
require("game/positional_warfare/positional_warfare_nuqi_tip")

PositionalWarfareWGCtrl = PositionalWarfareWGCtrl or BaseClass(BaseWGCtrl)

function PositionalWarfareWGCtrl:__init()
	if PositionalWarfareWGCtrl.Instance then
		print_error("[PositionalWarfareWGCtrl] attempt to create singleton twice!")
		return
	end

	PositionalWarfareWGCtrl.Instance = self

    self.data = PositionalWarfareWGData.New()
    self.pw_act_tip = PositionalWarfareActTip.New()
    self.mall_view = PositionalWarfareMallView.New(GuideModuleName.PositionalWarfareMallView)
    self.rank_view = PositionalWarfareRankView.New()
    self.scene_view = PositionalWarfareSceneView.New(GuideModuleName.PositionalWarfareSceneView)
    self.zhanling_view = PositionalWarfareZhanLingView.New(GuideModuleName.PositionalWarfareZhanLingView)
    self.treasure_view = PositionalWarfareTreasureView.New()
    self.camp_list_view = PositionalWarfareCampListView.New()
    self.pay_unlock_view = PositionalWarfarePayUnlockView.New()
    self.information_view = PositionalWarfareInformationView.New()
    self.score_reward_view = PositionalWarfareScoreRewardView.New()
    self.war_situation_view = PositionalWarfareWarSituationView.New()
    self.defeat_record_view = PositionalWarfareDefeatRecordView.New()
    self.unlock_zhanling_view = PositionalWarfareUnlockZhanLingView.New()
    self.stage_description_view = PositionalWarfareStageDescriptionView.New()
    self.buy_zhanling_level_view = PositionalWarfareBuyZhanLingLevelView.New()
    self.attributive_reward_view = PositionalWarfareAttributiveRewardView.New()
    self.view = PositionalWarfareView.New(GuideModuleName.PositionalWarfareView)
    self.gameplay_description_view = PositionalWarfareGamePlayDescriptionView.New()
    self.day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPass, self))
    self.create_role_event = GlobalEventSystem:Bind(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind(self.ReqProtocol,self))

    self.camp_show_view = PositionalWarfareCampShowView.New()
    self.explain_view = PositionalWarfareExplainView.New()
    self.nuqi_tip = PositionalWarfareNuQiTip.New()

    self:RegisterAllProtocol()

    -- self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	-- RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"guild_id"})

    self.order_reward_flag_cache = {}
    self.order_added_reward_flag_cache = {}
    self.first_enter = true

    self.call_cd_time_cache = -1
    self.call_land_seq_cache = -1
end

function PositionalWarfareWGCtrl:__delete()
	PositionalWarfareWGCtrl.Instance = nil

    self.view:DeleteMe()
    self.view = nil

    self.data:DeleteMe()
    self.data = nil

    self.attributive_reward_view:DeleteMe()
    self.attributive_reward_view = nil

    self.buy_zhanling_level_view:DeleteMe()
    self.buy_zhanling_level_view = nil

    self.camp_list_view:DeleteMe()
    self.camp_list_view = nil

    self.defeat_record_view:DeleteMe()
    self.defeat_record_view = nil

    self.gameplay_description_view:DeleteMe()
    self.gameplay_description_view = nil
    
    self.information_view:DeleteMe()
    self.information_view = nil

    self.mall_view:DeleteMe()
    self.mall_view = nil

    self.pay_unlock_view:DeleteMe()
    self.pay_unlock_view = nil

    self.rank_view:DeleteMe()
    self.rank_view = nil

    self.scene_view:DeleteMe()
    self.scene_view = nil

    self.stage_description_view:DeleteMe()
    self.stage_description_view = nil

    self.treasure_view:DeleteMe()
    self.treasure_view = nil

    self.unlock_zhanling_view:DeleteMe()
    self.unlock_zhanling_view = nil

    self.war_situation_view:DeleteMe()
    self.war_situation_view = nil

    self.zhanling_view:DeleteMe()
    self.zhanling_view = nil

    self.score_reward_view:DeleteMe()
    self.score_reward_view = nil

    self.pw_act_tip:DeleteMe()
    self.pw_act_tip = nil

    self.camp_show_view:DeleteMe()
    self.camp_show_view = nil

    self.explain_view:DeleteMe()
    self.explain_view = nil

    if self.create_role_event then
		GlobalEventSystem:UnBind(self.create_role_event)
		self.create_role_event = nil
	end

    if self.day_pass then
		GlobalEventSystem:UnBind(self.day_pass)
		self.day_pass = nil
	end

    -- if self.role_data_change then
	-- 	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
	-- 	self.role_data_change = nil
	-- end

    self.order_reward_flag_cache = nil
    self.order_added_reward_flag_cache = nil
    self.first_enter = nil

    self.call_cd_time_cache = nil
    self.call_land_seq_cache = nil
end

-------------------------------------------------------PROTOCOL_START----------------------------------------------------------
function PositionalWarfareWGCtrl:RegisterAllProtocol()
    self:RegisterProtocol(CSCrossLandWarOperate)
    self:RegisterProtocol(SCCrossLandWarVieStart, "OnSCCrossLandWarVieStart")
    self:RegisterProtocol(SCCrossLandWarBaseInfo, "OnSCCrossLandWarBaseInfo")
    self:RegisterProtocol(SCCrossLandWarRoomInfo, "OnSCCrossLandWarRoomInfo")
    self:RegisterProtocol(SCCrossLandWarLandInfo, "OnSCCrossLandWarLandInfo")
    self:RegisterProtocol(SCCrossLandWarMonsterRecordInfo, "OnSCCrossLandWarMonsterRecordInfo")
    self:RegisterProtocol(SCCrossLandWarMonsterInfo, "OnSCCrossLandWarMonsterInfo")
    self:RegisterProtocol(SCCrossLandWarMonsterUpdate, "OnSCCrossLandWarMonsterUpdate")
    self:RegisterProtocol(SCCrossLandWarRankInfo, "OnSCCrossLandWarRankInfo")
    self:RegisterProtocol(SCCrossLandWarSituationInfo, "OnSCCrossLandWarSituationInfo")
    self:RegisterProtocol(SCCrossLandWarCampInfo, "OnSCCrossLandWarCampInfo")
    self:RegisterProtocol(SCCrossLandWarCallInfo, "OnSCCrossLandWarCallInfo")
end

function PositionalWarfareWGCtrl:SendCrossLandWarOperate(operate_type, param1, param2, param3, param4)
    -- print_error("发送阵地战请求", operate_type, param1, param2, param3, param4)
    local protocol = ProtocolPool.Instance:GetProtocol(CSCrossLandWarOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol:EncodeAndSend()
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarBaseInfo(protocol)
    -- print_error("----------OnSCCrossLandWarBaseInfo----------", protocol)

    local old_score = self.data:GetShopExchangeScore()
    local old_devote = self.data:GetDevote()
    local reward_data_list = self.data:CheckGetZhanLingRewardDataList(self.order_reward_flag_cache, self.order_added_reward_flag_cache)
    self.order_reward_flag_cache, self.order_added_reward_flag_cache = self.data:SetCrossLandWarBaseInfo(protocol)

    if not IsEmptyTable(reward_data_list) then
        TipWGCtrl.Instance:ShowGetReward(nil, reward_data_list)
    end

    RemindManager.Instance:Fire(RemindName.PositionalWarfare)

    if not self.first_enter then
        if protocol.shop_score > old_score then
            local item_id = PositionalWarfareWGData.Instance:GetOtherAttrValue("mall_item")
            local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    
            local str = string.format(Language.PositionalWarfare.ZhenDiGuBiGetTip, ITEM_COLOR[item_cfg.color], item_cfg.name, protocol.shop_score - old_score)
            TipWGCtrl.Instance:ShowSystemMsg(str)
        end

        if protocol.devote > old_devote then
            local item_id = PositionalWarfareWGData.Instance:GetOtherAttrValue("devote_item")

            local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    
            local str = string.format(Language.PositionalWarfare.ZhenDiGuBiGetTip, ITEM_COLOR[item_cfg.color], item_cfg.name, protocol.devote - old_devote)
            TipWGCtrl.Instance:ShowSystemMsg(str)
        end
    end

    self.first_enter = false

    if self.view and self.view:IsOpen() then
        self.view:Flush()
    end

    if self.score_reward_view and self.score_reward_view:IsOpen() then
        self.score_reward_view:Flush()
    end

    if self.mall_view and self.mall_view:IsOpen() then
        self.mall_view:Flush()
    end

    if self.zhanling_view and self.zhanling_view:IsOpen() then
        self.zhanling_view:Flush()
    end

    if self.buy_zhanling_level_view and self.buy_zhanling_level_view:IsOpen() then
        self.buy_zhanling_level_view:Flush()
    end

    if self.nuqi_tip and self.nuqi_tip:IsOpen() then
        self.nuqi_tip:Flush()
    end
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarRoomInfo(protocol)
    -- print_error("----------OnSCCrossLandWarRoomInfo----------", protocol)
    self.data:SetCrossLandWarRoomInfo(protocol)
    RemindManager.Instance:Fire(RemindName.PositionalWarfare)

    if self.view and self.view:IsOpen() then
        self.view:Flush()
    end

    if self.information_view and self.information_view:IsOpen() then
        self.information_view:Flush()
    end
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarLandInfo(protocol)
    -- print_error("----------OnSCCrossLandWarLandInfo----------", protocol)
    self.data:SetCrossLandWarLandInfo(protocol)

    if self.view and self.view:IsOpen() then
        self.view:Flush()
    end

    if self.information_view and self.information_view:IsOpen() then
        self.information_view:Flush()
    end
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarMonsterRecordInfo(protocol)
    -- print_error("----------OnSCCrossLandWarMonsterRecordInfo----------", protocol)
    self.data:SetLandWarMonsterRecordInfo(protocol)

    if self.defeat_record_view and self.defeat_record_view:IsOpen() then
        self.defeat_record_view:Flush()
    end
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarMonsterInfo(protocol)
    -- print_error("----------OnSCCrossLandWarMonsterInfo----------", protocol)
    self.data:SetCrossLandWarMonsterInfo(protocol)

    if self.scene_view and self.scene_view:IsOpen() then
        self.scene_view:Flush()
    end
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarMonsterUpdate(protocol)
    -- print_error("----------OnSCCrossLandWarMonsterUpdate----------", protocol)
    self.data:SetCrossLandWarMonsterUpdate(protocol)

    if self.scene_view and self.scene_view:IsOpen() then
        self.scene_view:Flush()
    end
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarSituationInfo(protocol)
    -- print_error("----------OnSCCrossLandWarSituationInfo----------", protocol)
    self.data:SetWarSituationInfo(protocol)

    if self.war_situation_view and self.war_situation_view:IsOpen() then
        self.war_situation_view:Flush()
    end
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarRankInfo(protocol)
    -- print_error("----------OnSCCrossLandWarRankInfo----------", protocol)
    self.data:SetCrossLandWarRankInfo(protocol)

    local rank_type = protocol.rank_type

    if rank_type == CROSS_LAND_WAR_RANK_TYPE.LAND_DEVOTE then
        if self.attributive_reward_view and self.attributive_reward_view:IsOpen() then
            self.attributive_reward_view:Flush()
        end
    elseif rank_type == CROSS_LAND_WAR_RANK_TYPE.DEVOTE then
        if self.rank_view and self.rank_view:IsOpen() then
            self.rank_view:Flush()
        end
    elseif rank_type == CROSS_LAND_WAR_RANK_TYPE.KILL then
        if self.rank_view and self.rank_view:IsOpen() then
            self.rank_view:Flush()
        end
    elseif rank_type == CROSS_LAND_WAR_RANK_TYPE.CAMP_DEVOTE then
        -- if self.war_situation_view and self.war_situation_view:IsOpen() then
        --     self.war_situation_view:Flush()
        -- end

        if self.camp_list_view and self.camp_list_view:IsOpen() then
            self.camp_list_view:Flush()
        end
    end
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarCampInfo(protocol)
    -- print_error("----------OnSCCrossLandWarCampInfo----------", protocol)
    self.data:SetLandWarCampInfo(protocol)

    if self.gameplay_description_view and self.gameplay_description_view:IsOpen() then
        self.gameplay_description_view:Flush()
    end

    if self.camp_show_view and self.camp_show_view:IsOpen() then
        self.camp_show_view:Flush()
    end
end

function PositionalWarfareWGCtrl:OnBossHurtInfo(protocol)
    if self.scene_view and self.scene_view:IsOpen() then
        self.scene_view:Flush(0, "rank_info", protocol)
    end
end

function PositionalWarfareWGCtrl:ReqProtocol()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.ROOM_INFO)
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.CAMP_INFO)
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarVieStart(protocol)
    if self.pw_act_tip then
        if not self.pw_act_tip:IsOpen() then
            self.pw_act_tip:Open()
        end
    end
end

function PositionalWarfareWGCtrl:OnSCCrossLandWarCallInfo(protocol)
    -- print_error("召集信息", protocol)s
    self.data:SetCrossLandWarCallInfo(protocol)

    local call_cd = self.data:GetOtherAttrValue("call_cd")
    local server_time = TimeWGCtrl.Instance:GetServerTime()

    if protocol.call_land_seq >= 0 and protocol.call_cd_time > server_time and ((self.call_land_seq_cache ~= protocol.call_land_seq) or (self.call_cd_time_cache ~= protocol.call_cd_time)) then
        local my_camp = self.data:GetMyCamp()
        local camp_cfg = self.data:GetCurCampCfg(my_camp)
        local land_cfg = self.data:GetLandCfg(protocol.call_land_seq)
        local text_dec = string.format(Language.PositionalWarfare.DescZhaoJi, camp_cfg.camp_name, land_cfg.land_name)

        TipWGCtrl.Instance:OpenAlertTips(text_dec, function ()
            CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LAND_WAR, protocol.call_land_seq)
        end, nil, nil, nil, 10, nil, nil, nil, Language.PositionalWarfare.DescZhaoJiTitle)
    end
     
    self.call_cd_time_cache = protocol.call_cd_time
    self.call_land_seq_cache = protocol.call_land_seq
end
-------------------------------------------------------PROTOCOL_END----------------------------------------------------------

-- function PositionalWarfareWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
-- 	if attr_name == "guild_id" then
--         local stage_type = self.data:GetMapId()
--         local my_camp = self.data:GetMyCamp()

--         if stage_type == 1 and my_camp < 0 then
--             GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.ALL_GUILD_BASE_INFO, RoleWGData.Instance.role_vo.guild_id)
--             PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.CAMP_INFO)
--         end
-- 	end
-- end

function PositionalWarfareWGCtrl:OnDayPass()
    RemindManager.Instance:Fire(RemindName.PositionalWarfare)

    if self.view and self.view:IsOpen() then
        self.view:Flush()
    end

    if self.mall_view and self.mall_view:IsOpen() then
        self.mall_view:Flush()
    end
end

-------------------------------------------------------VIEW_START----------------------------------------------------------
function PositionalWarfareWGCtrl:OpenStageDescriptionView()
    if self.stage_description_view then
        self.stage_description_view:Open()
    end
end

function PositionalWarfareWGCtrl:OpenCampListView()
    if self.camp_list_view then
        self.camp_list_view:Open()
    end
end

function PositionalWarfareWGCtrl:OpenWarSituationView()
    if self.war_situation_view then
        self.war_situation_view:Open()
    end
end

function PositionalWarfareWGCtrl:OpenMallView()
    if self.mall_view then
        self.mall_view:Open()
    end
end

function PositionalWarfareWGCtrl:FlushMallView()
    if self.mall_view then
        self.mall_view:Flush()
    end
end

function PositionalWarfareWGCtrl:OpenTreasureView()
    if self.treasure_view then
        self.treasure_view:Open()
    end
end

function PositionalWarfareWGCtrl:FlushTreasureView()
    if self.treasure_view then
        self.treasure_view:Flush()
    end
end

function PositionalWarfareWGCtrl:OpenRankView(rank_type)
    if self.rank_view then
        self.rank_view:SetSelectRankType(rank_type)
        self.rank_view:Open()
    end
end

function PositionalWarfareWGCtrl:OpenScoreRewardView()
    if self.score_reward_view then
        self.score_reward_view:Open()
    end
end

function PositionalWarfareWGCtrl:OpenZhanLingView()
    if self.zhanling_view then
        self.zhanling_view:Open()
    end
end

function PositionalWarfareWGCtrl:OpenUnLockZhanLingView()
    if self.unlock_zhanling_view then
        self.unlock_zhanling_view:Open()
    end
end

function PositionalWarfareWGCtrl:OpenBugZhanLingLevelView()
    if self.buy_zhanling_level_view then
        self.buy_zhanling_level_view:Open()
    end
end

function PositionalWarfareWGCtrl:CloseBugZhanLingLevelView()
    if self.buy_zhanling_level_view and self.buy_zhanling_level_view:IsOpen() then
        self.buy_zhanling_level_view:Close()
    end
end

function PositionalWarfareWGCtrl:OpenGamePlayDescriptionView()
    if self.gameplay_description_view then
        self.gameplay_description_view:Open()
    end
end

function PositionalWarfareWGCtrl:OpenPlayUnlockView()
    if self.pay_unlock_view then
        self.pay_unlock_view:Open()
    end
end

function PositionalWarfareWGCtrl:OpenInformationView(data)
    if self.information_view then
        self.information_view:SetDataAndOpen(data)
    end
end

function PositionalWarfareWGCtrl:SendInformationRequest()
    if self.information_view and self.information_view:IsOpen() then
        self.information_view:AutoSendRequest()
    end
end

function PositionalWarfareWGCtrl:OpenAttributiveRewardView(data)
    if self.attributive_reward_view then
        self.attributive_reward_view:SetDataAndOpen(data)
    end
end

function PositionalWarfareWGCtrl:OpenDefeatRecordView(data)
    if self.defeat_record_view then
        self.defeat_record_view:SetDataAndOpen(data)
    end
end

function PositionalWarfareWGCtrl:OpenFBTaskView()
    if self.scene_view then
		self.scene_view:Open()
	end
end

function PositionalWarfareWGCtrl:CloseFBTaskView()
    if self.scene_view and self.scene_view:IsOpen() then
		self.scene_view:Close()
	end
end

function PositionalWarfareWGCtrl:GetFBTaskView()
    return self.scene_view
end

function PositionalWarfareWGCtrl:ClosePWView()
    if self.view and self.view:IsOpen() then
        self.view:Close()
    end
end

function PositionalWarfareWGCtrl:OpenCampShowView()
    if self.camp_show_view then
		self.camp_show_view:Open()
	end
end

function PositionalWarfareWGCtrl:OpenExplainView()
    if self.explain_view then
		self.explain_view:Open()
	end
end

function PositionalWarfareWGCtrl:OpenNuQiTip()
    if self.nuqi_tip then
		self.nuqi_tip:Open()
	end
end
-------------------------------------------------------VIEW_END----------------------------------------------------------