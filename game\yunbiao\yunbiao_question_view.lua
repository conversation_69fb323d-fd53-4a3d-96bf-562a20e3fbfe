-- 图片对应的 渐变色(1,2) 和描边颜色(3)
local color_table = {
	["a3_zjm_hs_gz"] = {[1]="#6de2ff", [2]="#bff2ff", [3]="#214999"},
	["a3_zjm_hs_jh"] = {[1]="#ffd76d", [2]="#fffbd3", [3]="#994321"},
	["a3_zjm_hs_jj"] = {[1]="#ff9191", [2]="#fff1ef", [3]="#861919"},
	["a3_zjm_hs_kt"] = {[1]="#ffd76d", [2]="#fffbd3", [3]="#994321"},
	["a3_zjm_hs_lk"] = {[1]="#b17dff", [2]="#fcefff", [3]="#5514c1"},
	["a3_zjm_hs_sh"] = {[1]="#ff91f9", [2]="#ffeffd", [3]="#99213d"},
	["a3_zjm_hs_tg"] = {[1]="#97f0a3", [2]="#eaffd3", [3]="#0e5a21"},
}

local EFFECT_TIME = 3

YuBiaoQuestionView = YuBiaoQuestionView or BaseClass(SafeBaseView)

function YuBiaoQuestionView:__init()
	self.view_style = ViewStyle.Window
	-- self:SetMaskBg(false,false)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/yunbiao_ui_prefab", "layout_yunbiao_question")

end

function YuBiaoQuestionView:__delete()

end

function YuBiaoQuestionView:ReleaseCallBack()

end

function YuBiaoQuestionView:LoadCallBack()


	self.node_list.btn_answer_1.button:AddClickListener(BindTool.Bind2(self.OnClinkHandler, self, 1))
	self.node_list.btn_answer_2.button:AddClickListener(BindTool.Bind2(self.OnClinkHandler, self, 2))
	self.node_list.btn_answer_3.button:AddClickListener(BindTool.Bind2(self.OnClinkHandler, self, 3))


end

function YuBiaoQuestionView:ShowIndexCallBack()
    MainuiWGCtrl.Instance:SetShrinkButtonIsOn(false)
	self.timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind2(self.OnClinkHandler, self, 1), YunbiaoWGData.Instance:GetTopicTime())
    self.node_list.root:CustomSetActive(true)

end

function YuBiaoQuestionView:CloseCallBack()
	GlobalTimerQuest:CancelQuest(self.timer)
	MainuiWGCtrl.Instance:SetShrinkButtonIsOn(true)
end

function YuBiaoQuestionView:OnFlush()
	local question_cfg, answer_cfg = YunbiaoWGData.Instance:GetRandomAnswerCfg()
	local color = YunbiaoWGData.Instance:GetCurHusongColor()
	local task_cfg = YunbiaoWGData.Instance:GetTaskCfgByColor(color)
	self.node_list.text_name.text.text = task_cfg.task_name
	self.node_list.text_desc.text.text = question_cfg.issue_text
	self.question_index = question_cfg.issue
	self.answer_cfg = answer_cfg
	for i = 1, 3 do
		if answer_cfg[i] then
			local cfg = answer_cfg[i]
			self.node_list["btn_answer_"..i]:CustomSetActive(true)
			self.node_list["text_answer_"..i].text.text = cfg.options_1

			local bundle, asset = ResPath.GetHuSongIcon(cfg.icon_1)
			self.node_list["btn_answer_"..i].image:LoadSprite(bundle, asset)

			if color_table[cfg.icon_1] then
				self.node_list["text_answer_"..i].gradient.Color1 = StrToColor(color_table[cfg.icon_1][1])
				self.node_list["text_answer_"..i].gradient.Color1 = StrToColor(color_table[cfg.icon_1][2])
				self.node_list["text_answer_"..i].out_line.effectColor = StrToColor(color_table[cfg.icon_1][3])
			else
				print_error("护送配置的图标对不上 是不是换资源了，记得同步:"..cfg.icon_1)
			end

		else
			self.node_list["btn_answer_"..i]:CustomSetActive(false)
		end
	end
end



function YuBiaoQuestionView:OnClinkHandler(index)
    GlobalTimerQuest:CancelQuest(self.timer)
    -- 发送协议
    YunbiaoWGCtrl.Instance:SendYunbiaoAnswer(self.question_index, index)
	if self.answer_cfg[index].bunble and self.answer_cfg[index].bunble~="" and self.answer_cfg[index].asset and self.answer_cfg[index].asset~="" then
		self.timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.Close, self),EFFECT_TIME)
		EffectManager.Instance:PlayAtTransform(self.answer_cfg[index].bunble, self.answer_cfg[index].asset, self.node_list.effect_node.transform,5)
		self.node_list.root:CustomSetActive(false)
	else
		self:Close()
	end

end

