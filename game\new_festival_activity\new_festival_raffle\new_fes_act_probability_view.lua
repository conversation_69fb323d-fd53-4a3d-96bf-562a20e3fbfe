NewFesActProbabilityView = NewFesActProbabilityView or BaseClass(SafeBaseView)

function NewFesActProbabilityView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(866, 516)})
    self:AddViewResource(0, "uis/view/new_festival_activity_ui_prefab", "layout_new_fes_act_probability")
    self:SetMaskBg(true, true)
end

function NewFesActProbabilityView:__delete()

end

function NewFesActProbabilityView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.NewFestivalActivity.ProbabilityTitle
     if not self.probability_list then
        self.probability_list = AsyncListView.New(NewFesActItemRender, self.node_list.ph_pro_list)
    end
end

function NewFesActProbabilityView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function NewFesActProbabilityView:OnFlush()
    local info = NewFestivalRaffleWGData.Instance:GetProbabilityInfo()
    if not info then
        return
    end

    self.probability_list:SetDataList(info)
end

-----------------------SunProItemRender-----------------------
NewFesActItemRender = NewFesActItemRender or BaseClass(BaseRender)
function NewFesActItemRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.bg:SetActive(self.index % 2 == 1)
    self.node_list.index_text.text.text = self.data.number
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
