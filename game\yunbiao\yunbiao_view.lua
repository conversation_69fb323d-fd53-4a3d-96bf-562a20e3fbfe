YunbiaoView = YunbiaoView or BaseClass(SafeBaseView)

local yunbiao_max_color = 5
local DOUBLE_MAX_NUM = 2 -- 双倍奖励目前的数量

function YunbiaoView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/yunbiao_ui_prefab", "layout_ui_yunbiao")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_light_common_top_panel")

	self.itemdata_change_callback = BindTool.Bind1(self.ItemDataChange, self)
end

function YunbiaoView:ReleaseCallBack()

	if self.yunbiao_flush_timer then
		GlobalTimerQuest:CancelQuest(self.yunbiao_flush_timer)
		self.yunbiao_flush_timer = nil
	end
	self.alert:DeleteMe()
	self.alert = nil
	self.alert2:DeleteMe()
	self.alert2 = nil
	self.flush_alert_auto_buy:DeleteMe()
	self.flush_alert_auto_buy = nil
	if self.flush_alert_hint then
		self.flush_alert_hint:DeleteMe()
		self.flush_alert_hint = nil
	end

	if self.gold_cell then
		self.gold_cell:DeleteMe()
		self.gold_cell = nil
	end

	if self.reward_cell then
		self.reward_cell:DeleteMe()
		self.reward_cell = nil
	end

	if self.other_cell then
		self.other_cell:DeleteMe()
		self.other_cell = nil
	end

	if self.model_head_farm_list then
        for i,v in ipairs(self.model_head_farm_list) do
            v:DeleteMe()
        end
        self.model_head_farm_list = {}
    end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.YunbiaoView, self.get_guide_ui_event)
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.itemdata_change_callback)
end

function YunbiaoView:LoadCallBack()
	self.alert = Alert.New(nil, nil, nil, nil, false)
	self.alert2 = Alert.New(nil, nil, nil, nil, false)
	self.alert:SetMarkName("YunBiao_Two")
	self.alert2:SetMarkName("YunBiao_Three")
	self.flush_alert_auto_buy = Alert.New()
	self.flush_alert_auto_buy:SetShowCheckBox(true, "HUSONG_AUTO_BUY")

	self.gold_can_flush_num = 0
	self.open_gold_buy_mark = false

	--铜币、经验、其他
	self.gold_cell = ItemCell.New(self.node_list["gold_cell_item"])
	self.reward_cell = ItemCell.New(self.node_list["reward_cell_item"])
	self.other_cell = ItemCell.New(self.node_list["ph_cell_item"])

	--功能引导注册
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.YunbiaoView, self.get_guide_ui_event)
	local husong_flush_state = PlayerPrefsUtil.GetInt("HUSONG_STATE")
	if nil == husong_flush_state then
		PlayerPrefsUtil.SetInt("HUSONG_STATE", 0)
		self.node_list["check_image"]:SetActive(false)
	else
		self.node_list["check_image"]:SetActive(1 == husong_flush_state)
	end
	self.node_list["time_state"].text.text = Language.YunBiao.HuSongTimeState
	self.node_list["flush_state"].text.text = Language.YunBiao.HuSongFlushState

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
	ItemWGData.Instance:NotifyDataChangeCallBack(self.itemdata_change_callback)

	self.node_list["title_view_name"].text.text = Language.ViewName.HuSong
	local bundle, asset = ResPath.GetRawImagesJPG("a3_hs_bj")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self:RegisterAllEvents()
	self:InitModelHeadFarmList()
end

function YunbiaoView:OpenCallBack()
	YunbiaoWGCtrl.Instance:SendGetHusongTaskInfo()
	self.is_first = true
end

function YunbiaoView:Open()
	-- print_log('#YunbiaoView#', "打开护送面板")
	SafeBaseView.Open(self)
end

function YunbiaoView:Close()
	-- print_log('#YunbiaoView#', "关闭护送面板")
	SafeBaseView.Close(self)
end

function YunbiaoView:CloseCallBack(is_all)
	self.all_flush_num = 0
	self.buy_item_num = 0
	self.consume_item_num = 0
	self.npc_id = nil
	self.old_color_index = -1
	if self.yunbiao_flush_timer then
		GlobalTimerQuest:CancelQuest(self.yunbiao_flush_timer)
		self.yunbiao_flush_timer = nil
	end
	YunbiaoWGCtrl.Instance:DeleteMeAlertHint()
end

function YunbiaoView:RegisterAllEvents()
	--XUI.AddClickEventListener(self.node_list["btn_tip"], BindTool.Bind1(self.OnClickRule, self))

	self.node_list["btn_start_husong"].button:AddClickListener(BindTool.Bind1(self.DealClickHuSong, self))
	self.node_list["check_box"].button:AddClickListener(BindTool.Bind1(self.OnClickYiJianFlush, self))
	self.node_list["btn_flush"].button:AddClickListener(BindTool.Bind1(self.OnclickFlush, self))
	self.node_list["btn_meirenling2"].button:AddClickListener(BindTool.Bind1(self.OnclickOpenItemTip, self))
	self.node_list["btn_preview"].button:AddClickListener(BindTool.Bind1(self.ClickBtnPreview, self))
	self:UpdataText()
end

function YunbiaoView:OnClickMeiRenLing()
	local id = YunbiaoWGData.Instance:GetYubiaoMeiRenLingId()
	TipWGCtrl.Instance:OpenItem({ item_id = id })
end

function YunbiaoView:ItemDataChange()
	self:Flush()
end

function YunbiaoView:OnFlush(param_t)
	self:SetRightDownShow()
	self:SetViewShowInfo()
	--self:SetTitleShowInfo()

	self:CleanTimer()

	local color_index = YunbiaoWGData.Instance:GetCurHusongColor()
	
	self:FlushModelHeadFarmList()

	self:UpdataText()

	self.old_color_index = color_index
	
end

function YunbiaoView:CleanTimer()
    if self.show_model_timer then
        GlobalTimerQuest:CancelQuest(self.show_model_timer)
        self.show_model_timer = nil
    end
end

function YunbiaoView:InitModelHeadFarmList()
	self.model_head_farm_list = {}
	for i = 1 , 4 do
		if not self.model_head_farm_list[i] then
        	self.model_head_farm_list[i] = ModelHeadRander.New(self.node_list["model_head_"..i])
    	end
	end
end

function YunbiaoView:FlushModelHeadFarmList()
	local select_color = YunbiaoWGData.Instance:GetCurHusongColor()
	local cfg_data = nil
	for i = 1 , 4 do
		cfg_data = YunbiaoWGData.Instance:GetRewardCfgByLv(i + 1)
		self.model_head_farm_list[i]:SetIndex(i + 1)
		if cfg_data then
			self.model_head_farm_list[i]:SetData(cfg_data)
		end
	end
end

-- 更新文本_OK
function YunbiaoView:UpdataText()
	local yunbiao_data = YunbiaoWGData.Instance
	local act_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.HUSONG)
	local list = yunbiao_data:GetRewardConfig()
	local color_index = YunbiaoWGData.Instance:GetCurHusongColor() - 1
	local reward_item_info = list[color_index]
	if reward_item_info then
		local exp = reward_item_info[1].num
		local exp_per = RoleWGData.Instance:GetRoleExpCorrection()
		exp = exp * exp_per
		local exp_str = CommonDataManager.ConverExp(exp, false, true)
		self.gold_cell:SetData({ item_id = 90050 })
		self.gold_cell:SetRightBottomTextVisible(true)
		self.gold_cell:SetRightBottomColorText(exp_str)

		local tongbi = CommonDataManager.ConverExpExtend(reward_item_info[2].num, false)
		self.reward_cell:SetData({ item_id = 65536 })
		self.reward_cell:SetRightBottomTextVisible(true)
		self.reward_cell:SetRightBottomColorText(tongbi)
		if reward_item_info[3] then
			self.node_list["ph_cell"]:SetActive(true)
			if reward_item_info[3].item_id > 0 then
				self.other_cell:SetData(reward_item_info[3])
			end
		else
			self.node_list["ph_cell"]:SetActive(false)
		end
	end

	for i = 1, DOUBLE_MAX_NUM do
		if self.node_list["double_img" .. i] then
			self.node_list["double_img" .. i]:SetActive(act_isopen)
		end
	end
end

function YunbiaoView:SetRightDownShow()
	local color = YunbiaoWGData.Instance:GetCurHusongColor()
	local cfg = YunbiaoWGData.Instance:GetRewardCfgByLv(color)
	self.node_list["left_parent"]:SetActive(color < yunbiao_max_color and YunbiaoWGData.Instance:GetSurplusTimes() > 0)
	self.node_list["max_color"]:SetActive(color >= yunbiao_max_color)
	self.node_list["btn_meirenling2"]:SetActive(YunbiaoWGData.Instance:GetFreeNum() <= 0 and cfg and
	cfg.refresh_comsume_num > 0)

	if color < yunbiao_max_color and cfg and cfg.refresh_comsume_id > 0 and cfg.refresh_comsume_num > 0 then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.refresh_comsume_id)
		local comsume_num = cfg.refresh_comsume_num
		local color = item_num < comsume_num and COLOR3B.RED or COLOR3B.DEFAULT_NUM
		local str = ToColorStr(item_num, color) .. "/" .. comsume_num
		self.node_list["flush_item_num"].text.text =  "（" .. str .. "）"
	end

	self.node_list["rich_tips2"].text.text = YunbiaoWGData.Instance:GetConsumeYbStr()
	self.node_list["right_parent"]:SetActive(YunbiaoWGData.Instance:GetSurplusTimes() > 0)
	self.node_list["max_num"]:SetActive(YunbiaoWGData.Instance:GetSurplusTimes() <= 0)
	self.node_list["free_num"]:SetActive(YunbiaoWGData.Instance:GetFreeNum() > 0)
	self.node_list["free_num"].text.text = string.format(Language.YunBiao.FreeFlushNum,
		YunbiaoWGData.Instance:GetFreeNum())
end

function YunbiaoView:SetViewShowInfo()
	local color = YunbiaoWGData.Instance:GetCurHusongColor()
	local btn_icon_name = color < yunbiao_max_color and "bt_btn_l_hui" or "bt_btn_l_huang"
	local btn_name_color = color < yunbiao_max_color and COLOR3B.BLACK or COLOR3B.BLACK

	--self.node_list["husong_text"].text.text = ToColorStr(Language.YunBiao.BtnHusongName, btn_name_color)
	--self.node_list["btn_start_husong"].image:LoadSprite(ResPath.GetF2CommonButtonToggle(btn_icon_name))
end

--[[function YunbiaoView:SetTitleShowInfo()
	local index = YunbiaoWGData.Instance:GetCurHusongColor() - 1
	for i = 1, 4 do
		self.hight_list[i]:SetActive(i == index)
		--self.qipao_list[i]:SetActive(i == index)
	end
end]]



-- 开始护送（不弹提醒）
function YunbiaoView:DealClickHuSongNoTips()
	local color = YunbiaoWGData.Instance:GetCurHusongColor()
	self:SendHusongReq(color)
end

-- 接护送任务，开始护送
function YunbiaoView:DealClickHuSong()
	if NewTeamWGData.Instance:GetIsMatching() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
		return
	end

	if YunbiaoWGData.Instance:GetSurplusTimes() <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.NoHuSongNum)
		return
	end

	self:SetTimeAlert2()
end

function YunbiaoView:SetTimeAlert2(color)
	local act_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.HUSONG)
	if act_isopen then
		self:BastColorJudje()
	else
		self.alert:SetShowCheckBox(true)
		self.alert:SetLableString(Language.YunBiao.HuoDongShiJian)
		self.alert:SetOkFunc(BindTool.Bind(self.BastColorJudje, self, color))
		self.alert:Open()
	end
end

function YunbiaoView:BastColorJudje()
	local color = YunbiaoWGData.Instance:GetCurHusongColor()
	if color >= yunbiao_max_color then
		self:SendHusongReq(color)
	else
		self.alert2:SetShowCheckBox(true)
		self.alert2:SetLableString(Language.YunBiao.HuoDongColor)
		self.alert2:SetOkFunc(BindTool.Bind(self.SendHusongReq, self, color))
		self.alert2:Open()
	end
end

function YunbiaoView:SendHusongReq(color)
	self:Close()
	YunbiaoWGCtrl.Instance:SendRefreshHusongTask()
end

function YunbiaoView:SetNpcId(npc_id)
	self.npc_id = npc_id
end

function YunbiaoView:GetNpcId()
	return self.npc_id
end

-- 任务引导
function YunbiaoView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.CloseBtn then
		return self.node_list.btn_close_window.node, BindTool.Bind1(self.OnCloseHandler, self)
	end

	if ui_name == GuideUIName.HuSongFlush then
		local func = function()
			local husong_flush_state = PlayerPrefsUtil.GetInt("HUSONG_STATE")
			if husong_flush_state == 0 then
				self:OnClickYiJianFlush()
			end
			self:OnclickFlush(true)
		end
		return self.node_list["btn_flush"], func
	end

	if ui_name == GuideUIName.StartHuSong then
		return self.node_list["btn_start_husong"], BindTool.Bind(self.DealClickHuSongNoTips, self)
	end

	return nil, nil
end

--[[
function YunbiaoView:YunbiaoAddCountBtnEffect()
	local yb_data = YunbiaoWGData.Instance
	local visible = false
	if yb_data:GetHusongRemainTimes() <= 0 and
		VipPower.Instance:GetHasPower(VipPowerId.husong_buy_times, false, yb_data:GetGouMaiCishu() + 1, yb_data:GetGouMaiCishu()) then
		visible = true
	end
end
--]]
--点击规则提示按钮回调
--[[function YunbiaoView:OnClickRule()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.YunBiao.YunBiaoTipTitle)
	rule_tip:SetContent(Language.YunBiao.YunBiaoRuleTip)
end]]

--一键刷新
function YunbiaoView:OnClickYiJianFlush()
	local husong_flush_state = PlayerPrefsUtil.GetInt("HUSONG_STATE")
	if 1 == husong_flush_state then
		husong_flush_state = 0
	elseif 0 == husong_flush_state then
		husong_flush_state = 1
	end
	if husong_flush_state == 1 then
		TipWGCtrl.Instance:OpenAlertTips(Language.YunBiao.OneKeyMaxPZTip, function()
			self.node_list["check_image"]:SetActive(1 == husong_flush_state)
			PlayerPrefsUtil.SetInt("HUSONG_STATE", husong_flush_state)
		end)
		return
	end
	self.node_list["check_image"]:SetActive(1 == husong_flush_state)
	PlayerPrefsUtil.SetInt("HUSONG_STATE", husong_flush_state)
end

function YunbiaoView:OnclickOpenItemTip()
	local id = YunbiaoWGData.Instance:GetYubiaoMeiRenLingId()
	TipWGCtrl.Instance:OpenItem({ item_id = id }, nil)
end

function YunbiaoView:OnclickFlush(is_guide)
	if self.yunbiao_flush_timer then
		GlobalTimerQuest:CancelQuest(self.yunbiao_flush_timer)
		self.yunbiao_flush_timer = nil
		self.node_list["flush_text"].text.text = Language.YunBiao.BtnStarFlush
		self:StopFlushSettlement()
		return
	end
	local color = YunbiaoWGData.Instance:GetCurHusongColor()
	if color >= yunbiao_max_color then
		return
	end

	local husong_flush_state = PlayerPrefsUtil.GetInt("HUSONG_STATE")
	local is_onekey = husong_flush_state == 1
	local flush_num = 0
	-- 免费次数
	local free_num = YunbiaoWGData.Instance:GetFreeNum()
	if free_num > 0 then
		if is_onekey then
			flush_num = flush_num + free_num
		else
			self:SendFlushReq(1, nil, nil, is_guide)
			return
		end
	end

	-- 已有护送令数
	local cfg_data = YunbiaoWGData.Instance:GetRewardCfgByLv(color)
	local need_num = (cfg_data and cfg_data.refresh_comsume_num) or 0
	if need_num <= 0 then
		return
	end
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg_data.refresh_comsume_id)
	if item_num >= need_num then
		if is_onekey then
			flush_num = flush_num + math.floor(item_num / need_num)
		else
			self:SendFlushReq(1, true, nil, is_guide)
			return
		end
	end

	-- 绑玉能购买护送令数
	local bind_gold = RoleWGData.Instance.role_info.bind_gold or 0 -- 自身绑玉
	local price_type_1, price_type_2 = YunbiaoWGData.Instance:GetItemPriceType()
	local one_pice_2 = ShopWGData.Instance:GetShopCfgItemIdPrice(cfg_data.refresh_comsume_id, price_type_2)
	local bind_can_buy_num = math.floor(bind_gold / one_pice_2)
	flush_num = flush_num + math.floor((bind_can_buy_num + item_num) / need_num)

	-- 仙玉能购买护送令数
	local gold = RoleWGData.Instance.role_info.gold or 0 -- 自身仙玉
	local one_pice_1 = ShopWGData.Instance:GetShopCfgItemIdPrice(cfg_data.refresh_comsume_id, price_type_1)
	local gold_can_buy_num = math.floor(gold / one_pice_1)
	self.gold_can_flush_num = math.floor((gold_can_buy_num + item_num) / need_num)

	if is_onekey then
		flush_num = math.min(flush_num, 10)
		YunbiaoWGData.Instance:CleanRefreshConsumeInfo()
		self:LoopSendFlushReq(flush_num, true, true, is_guide)
		return
	end

	-- 绑玉购买提示
	local item_cfg = ItemWGData.Instance:GetItemConfig(cfg_data.refresh_comsume_id)
	if not item_cfg then
		return
	end
	local buy_num = need_num - item_num
	local is_not_tip = self.flush_alert_auto_buy:GetIsNeedOpenViewFlag()
	self.flush_alert_auto_buy:SetCheckBoxDefaultSelect(is_not_tip)
	if bind_can_buy_num > 0 then
		local need_bind_gold = one_pice_2 * buy_num
		local tip_str = string.format(Language.YunBiao.HuSongBingGoldBuy, need_bind_gold, buy_num, item_cfg.name)
		self.flush_alert_auto_buy:SetLableString(tip_str)
		self.flush_alert_auto_buy:SetOkFunc(function()
			self:SendFlushReq(1, false, true, is_guide)
		end)
		self.flush_alert_auto_buy:Open()
		return
	end

	-- 仙玉购买提示
	local need_gold = one_pice_1 * buy_num
	local tip_str = string.format(Language.YunBiao.HuSongGoldBuy, need_gold, buy_num, item_cfg.name)
	self.flush_alert_auto_buy:SetLableString(tip_str)
	self.flush_alert_auto_buy:SetOkFunc(function()
		if gold_can_buy_num + item_num < need_num then
			VipWGCtrl.Instance:OpenTipNoGold()
			return
		end
		self:SendFlushReq(1, false, true, is_guide)
	end)
	self.flush_alert_auto_buy:Open()
end

function YunbiaoView:SendFlushReq(flush_num, is_item, is_gold, is_guide)
	local is_one_key_fresh = 0
	local auto_buy = is_gold and 1 or 0
	local is_clear = 1
	YunbiaoWGCtrl.Instance:SendHusongRefreshReq(is_one_key_fresh, auto_buy, is_clear)
end

function YunbiaoView:LoopSendFlushReq(flush_num, is_item, is_gold, is_guide)
	local is_one_key_fresh = 0
	local auto_buy = is_gold and 1 or 0
	local is_clear = 1
	self.open_gold_buy_mark = true
	self.node_list["flush_text"].text.text = Language.YunBiao.BtnStopFlush
	self.yunbiao_flush_timer = GlobalTimerQuest:AddTimesTimer(function()
		local color = YunbiaoWGData.Instance:GetCurHusongColor()
		if flush_num <= 0 or color >= yunbiao_max_color then
			GlobalTimerQuest:CancelQuest(self.yunbiao_flush_timer)
			self.yunbiao_flush_timer = nil
			self.node_list["flush_text"].text.text = Language.YunBiao.BtnStarFlush
			if not is_guide then
				if color >= yunbiao_max_color then
					self:StopFlushSettlement()
				elseif self.open_gold_buy_mark then
					self:OpenXianYuBuyTip()
				elseif self.gold_can_flush_num <= 0 then
					VipWGCtrl.Instance:OpenTipNoGold()
				end
			end
			return
		end
		flush_num = flush_num - 1
		YunbiaoWGCtrl.Instance:SendHusongRefreshReq(is_one_key_fresh, auto_buy, is_clear)
	end, 0.5, flush_num + 1)
end

function YunbiaoView:OpenXianYuBuyTip()
	TipWGCtrl.Instance:OpenAlertTips(Language.YunBiao.BindGoldNo, function()
		if self.gold_can_flush_num > 0 then
			local flush_num = math.min(self.gold_can_flush_num, 10)
			self:LoopSendFlushReq(flush_num, false, true)
			self.gold_can_flush_num = 0
		else
			VipWGCtrl.Instance:OpenTipNoGold()
		end
	end)
	self.open_gold_buy_mark = false
end

function YunbiaoView:StopFlushSettlement()
	local consume_info = YunbiaoWGData.Instance:GetRefreshConsumeInfo()
	if not consume_info then
		return
	end
	local color = YunbiaoWGData.Instance:GetCurHusongColor()
	local color_str = Language.YunBiao.HusongColor[color] or ""
	local cfg_data = YunbiaoWGData.Instance:GetRewardCfgByLv(color)
	local item_cfg = ItemWGData.Instance:GetItemConfig(cfg_data and cfg_data.refresh_comsume_id)
	local tip_str = ""
	local buy_str = ""
	if consume_info.consume_buy_item_num > 0 then
		local gold_str = ""
		local bind_gold_str = ""
		if consume_info.refresh_consume_bind_gold > 0 then
			bind_gold_str = string.format("<color=%s>%d%s</color>，", COLOR3B.DEFAULT_NUM,
				consume_info.refresh_consume_bind_gold, Language.Role.BangYu)
		end
		if consume_info.refresh_consume_gold > 0 then
			gold_str = string.format("<color=%s>%d%s</color>，", COLOR3B.DEFAULT_NUM, consume_info.refresh_consume_gold,
				Language.Role.XianYu)
		end
		buy_str = string.format(Language.YunBiao.FlushBuyItem, bind_gold_str, gold_str, consume_info
		.consume_buy_item_num, item_cfg and item_cfg.name or "")
	end

	if consume_info.refresh_total_count > 0 then
		tip_str = string.format(Language.YunBiao.FlushSettlement, buy_str, consume_info.refresh_total_count, color_str)

		if not self.flush_alert_hint then
			self.flush_alert_hint = Alert.New()
			self.flush_alert_hint:SetCheckBoxDefaultSelect(true)
			self.flush_alert_hint:SetUseOneSign(true)
		end
		self.flush_alert_hint:SetLableString(tip_str)
		self.flush_alert_hint:Open()
	end
	YunbiaoWGData.Instance:CleanRefreshConsumeInfo()
end

function YunbiaoView:ClickBtnPreview()
	local list = YunbiaoWGData.Instance:GetRewardConfig()
	if list then
		local title_reward_item_data = {}
		for index, value in ipairs(list) do
			local data = ListIndexFromZeroToOne(value)
			title_reward_item_data[index] = {}
			local head_name = YunbiaoWGData.Instance:GetBeautyName(index + 1)
			title_reward_item_data[index].title_text = head_name
			title_reward_item_data[index].reward_item_list = data
		end

		local data_list =
		{
			view_type = RewardShowViewType.Title,
			title_reward_item_data = title_reward_item_data
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
	end
end

------------------------ 模型ModelHeadRander ---------------------------------------

ModelHeadRander = ModelHeadRander or BaseClass(BaseRender)
function ModelHeadRander:LoadCallBack()
	self:InitModels()
	self.old_color = -1
end

function ModelHeadRander:ReleaseCallBack()

	if self.show_role_model then
		self.show_role_model:DeleteMe()
		self.show_role_model = nil
	end

	-- if self.alpha_tween then
    --     self.alpha_tween:Kill()
    --     self.alpha_tween = nil
    -- end
end

function ModelHeadRander:OnFlush()
	--local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.npc_head)
	if not self.data then
		return
	end



	local select_color = YunbiaoWGData.Instance:GetCurHusongColor()
	self.node_list.bg:SetActive(self.index ~= select_color)
	self.node_list.title_bg:SetActive(self.index ~= select_color)
	self.node_list.select_image:SetActive(self.index == select_color)

	local head_name = YunbiaoWGData.Instance:GetBeautyName(self.index)
	self.node_list["name_text"].text.text = head_name

	if self.index ~= select_color then
		self.node_list.ph_model:SetActive(false)
		-- if self.old_color ~= select_color and self.index == self.old_color then
		-- 	if self.alpha_tween then
		-- 		self.alpha_tween:Kill()
		-- 		self.alpha_tween = nil
		-- 	end
		-- 	self.alpha_tween = self.node_list.bg.canvas_group:DoAlpha(0, 1, 1)
		-- end
	else
		--渐变隐藏 alpha_tween相关的效果策划又不要了  下次改这里直接把注释删了 2024.12.4
		if self.old_color ~= select_color then
			if self.old_color == -1 then
				self:ShowModels()
				self:PlayAllModelAction()
				-- self.node_list.bg.canvas_group.alpha = 0
			else
				-- if self.alpha_tween then
				-- 	self.alpha_tween:Kill()
				-- 	self.alpha_tween = nil
				-- end
				-- self.alpha_tween = self.node_list.bg.canvas_group:DoAlpha(1, 0, 1)
				-- self.timer = GlobalTimerQuest:AddDelayTimer(function ()
				-- 	self:ShowModels()
				-- 	self:PlayAllModelAction()
				-- 	self.node_list.bg.canvas_group.alpha = 0
				-- 	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_husong_baozha)
				-- 	EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["effect_node"].transform, 3, nil, nil, Vector3(0.5, 0.5, 0.5))
				-- end, 1)

				self:ShowModels()
				self:PlayAllModelAction()
				local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_husong_baozha)
				EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["effect_node"].transform, 3, nil, nil, Vector3(1, 1, 1))
			end
			
		end
	end
	self.old_color = select_color
end

function ModelHeadRander:ShowModels()
	local select_color = YunbiaoWGData.Instance:GetCurHusongColor()
	self.node_list.ph_model:SetActive(self.index == select_color)
	local task_cfg = YunbiaoWGData.Instance:GetTaskCfgByColor(select_color)
	local bundle, asset = YunbiaoWGData.Instance:GetBeautyModelPath(select_color)
	self.show_role_model:SetMainAsset(bundle, asset)
	
	local pos_x , pos_y = 0 , 0
	if task_cfg.whole_display_pos and task_cfg.whole_display_pos ~= "" then
		local pos_list = string.split(task_cfg.whole_display_pos, ",")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		RectTransform.SetAnchoredPositionXY(self.node_list.ph_model.rect, pos_x, pos_y)
	end

	local scale = task_cfg.model_scale
	if scale and scale ~= "" then
		self.show_role_model:SetRTAdjustmentRootLocalScale(scale)
	end

	local rot_x , rot_y, rot_z = 0, 0, 0
	if task_cfg.model_rot and task_cfg.model_rot ~= "" then
		local pos_list = string.split(task_cfg.model_rot, ",")
		rot_x = tonumber(pos_list[1]) or rot_x
		rot_y = tonumber(pos_list[2]) or rot_y
		rot_z = tonumber(pos_list[3]) or rot_z
		self.show_role_model:SetRTAdjustmentRootLocalRotation(rot_x, rot_y, rot_z)
	end

end

function ModelHeadRander:InitModels()
	if not self.show_role_model then
		self.show_role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		self.show_role_model:SetRenderTexUI3DModel(display_data)
	end
end
function ModelHeadRander:PlayAllModelAction()
	local color_index = YunbiaoWGData.Instance:GetCurHusongColor()
	if color_index == 2 then
		self.show_role_model:PlayHSRoleAction()
	elseif color_index == 3 or color_index == 4 then
		self.show_role_model:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Sit)
	else
		self.show_role_model:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)
	end
end