LongZhuWGData = LongZhuWGData or BaseClass()

LONGZHU_TYPE_MAX = 7

LONGZHU_OPER_TYPE = {
	LONGZHU_OPER_TYPE_UP_LEVEL = 1,		-- 升级
	LONGZHU_OPER_TYPE_ALL_INFO = 2,		-- 龙珠所有信息
}

function LongZhuWGData:__init()
	if LongZhuWGData.Instance then
		error("[LongZhuWGData]:Attempt to create singleton twice!")
		return
	end
	LongZhuWGData.Instance = self
	
	self.longzhu_config = ConfigManager.Instance:GetAutoConfig("longzhu_config_auto")
	self:InitParam()
	self:InitStuffList()
	self:RegisterLongZhuRemindInBag()
	RemindManager.Instance:Register(RemindName.LongZhu, BindTool.Bind(self.GetLongZhuRemind, self))				-- 背包标签红点
end

function LongZhuWGData:__delete()
	self:InitParam()
	self.longzhu_config = nil
	LongZhuWGData.Instance = nil
end

function LongZhuWGData:InitParam()
	self.long_zhu_level_list = {}
	self.long_zhu_stuff_map = {}
	self.long_zhu_fuhuo_count = 0
end

-- 收集材料Map
function LongZhuWGData:InitStuffList()
	local longzhu_config = self.longzhu_config
	local cfg_list = longzhu_config and longzhu_config.longzhu
	local stuff_map = {}
	if cfg_list then
		for _,v in pairs(cfg_list) do
			if not stuff_map[v.consume_item] then
				stuff_map[v.consume_item] = v.id
			end
		end
	end
	self.long_zhu_stuff_map = stuff_map
end

function LongZhuWGData:RegisterLongZhuRemindInBag()
	local item_id_list = {}
	local long_zhu_list = self.long_zhu_stuff_map
	for item_id,v in pairs(long_zhu_list) do
		item_id_list[#item_id_list + 1] = item_id
	end
	BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.LongZhu, item_id_list, nil)
end

---[[ 龙珠表格数据
function LongZhuWGData:GetLongZhuCfg(id, level)
	local cfg_list = self.longzhu_config and self.longzhu_config.longzhu
	if cfg_list then
		for i=1,#cfg_list do
			if cfg_list[i].id == id and cfg_list[i].level == level then
				return cfg_list[i]
			end
		end
	end
end

function LongZhuWGData:GetLongZhuCfgByItemId(item_id)
	local cfg_list = self.longzhu_config.longzhu or {}
	for i=1,#cfg_list do
		if cfg_list[i].consume_item == item_id then
			return cfg_list[i]
		end
	end
end

function LongZhuWGData:GetOtherCfg(key)
	if self.longzhu_config and key then
		return self.longzhu_config.other[1][key]
	end
end

function LongZhuWGData:GetIsLongZhuSkill(skill_id)
	local longzhu_skill_id = self:GetOtherCfg("skill_id")
	if longzhu_skill_id then
		return tonumber(longzhu_skill_id) == tonumber(skill_id)
	end
	return false
end

function LongZhuWGData:GetMaxLongZhuSkillLevel()
	if self.longzhu_config and self.longzhu_config.longzhu_skill then
		return #self.longzhu_config.longzhu_skill
	end
	return 0
end

function LongZhuWGData:GetLongZhuSkillCfg(level)
	if self.longzhu_config and self.longzhu_config.longzhu_skill then
		return self.longzhu_config.longzhu_skill[level]
	end
end

function LongZhuWGData:GetLongZhuSpecialAttrCfg(level)
	if self.longzhu_config and self.longzhu_config.special_attr then
		return self.longzhu_config.special_attr[level]
	end
end

function LongZhuWGData:GetLongZhuAttrList(cfg_list, need_cap)
	local attr_str_list = {}
	local attribute = AttributePool.AllocAttribute()
	
	if cfg_list then
		local attr_type = 0
		local attr_value = 0
		local attr_add_pre = self:GetLongZhuAttrAddPre()
		for i=0,2 do
			attr_type = cfg_list["attr_" .. i] or 0
			attr_value = cfg_list["attr_value_" .. i] or 0
			attr_value = attr_value + math.floor(attr_value * attr_add_pre)
			if attr_type > 0 and attr_value > 0 then
				local attr_key = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_type)
				local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_type, true)
				attribute[attr_key] = attr_value
				if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_key) then
					attr_value = string.format("%.2f%%", attr_value / 100)
				elseif AttributeMgr.AttrIsWan(attr_key) then
					attr_value = string.format("%.2f%%", attr_value / 10000)
				end

				attr_str_list[#attr_str_list + 1] = {attr_name = attr_name, attr_value = attr_value}
			end
		end
	end

	local specia_id = self:GetOtherCfg("special_attr_longzhu_id")
	if cfg_list.id == specia_id then
		local specia_attr_cfg = self:GetLongZhuSpecialAttrCfg(cfg_list.level)
		if specia_attr_cfg then
			local attr_value = string.format("%.2f%%", specia_attr_cfg.attr_value / 100)
			local specia_attr = {attr_name = Language.LongZhu.LongZhuAttr, attr_value = attr_value}
			attr_str_list[#attr_str_list + 1] = specia_attr
		end
	end

	if need_cap then
		local cap_value = AttributeMgr.GetCapability(attribute)
		return attr_str_list, cap_value
	end

	return attr_str_list, 0
end

function LongZhuWGData:GetLongZhuAttrList2(cfg)
	local attr_data = EquipWGData.GetSortAttrListByTypeCfg(cfg, "attr_", "attr_value_", 0, 2)

	local specia_id = self:GetOtherCfg("special_attr_longzhu_id")
	if cfg and cfg.id == specia_id then
		local specia_attr_cfg = self:GetLongZhuSpecialAttrCfg(cfg.level)
		if specia_attr_cfg then
			attr_data[#attr_data + 1] = {attr_str = "longzhu_per", attr_value = specia_attr_cfg.attr_value}
		end
	end

	return attr_data
end

function LongZhuWGData:GetLongZhuAttrStr(attr_list)
	local attr_str = ""
	if attr_list then
		local str_list = {}
		local color_str = COLOR3B.DEFAULT_NUM
		for i=1,#attr_list do 
			str_list[i] = string.format("%s <color=%s>+%s</color>", attr_list[i].attr_name, color_str, attr_list[i].attr_value)
		end
		attr_str = table.concat(str_list, "\n")
	end
	return attr_str
end

function LongZhuWGData:GetLongZhuDescCfgList()
	if self.longzhu_config then
		return self.longzhu_config.longzhu_desc
	end
end

-- 龙珠描述数据(当前等级和下一级)
function LongZhuWGData:GetLongZhuSkillDescList()
	local desc_cfg_list = self:GetLongZhuDescCfgList()
	if not desc_cfg_list then
		return
	end

	local now_cfg_list = {}
	local nex_cfg_list = {}
	local longzhu_level = self:GetLongZhuSkillLevel()
	local next_level = longzhu_level + 1

	for i,v in ipairs(desc_cfg_list) do
		if longzhu_level >= v.active_skill_level then
			now_cfg_list[v.skill_type] = v
		else
			if not now_cfg_list[v.skill_type] then
				now_cfg_list[v.skill_type] = v
			end

			if next_level == v.active_skill_level then
				nex_cfg_list[v.skill_type] = v
			end
		end
	end

	return now_cfg_list, nex_cfg_list
end

-- 龙珠描述数据(当前等级拥有的技能词条和下一级时能拥有的技能词条)
function LongZhuWGData:GetLongZhuSkillDescList2()
	local desc_cfg_list = self:GetLongZhuDescCfgList()
	if not desc_cfg_list then
		return
	end

	local now_cfg_list = {}
	local now_lock_cfg_list = {}
	local nex_cfg_list = {}
	local nex_lock_cfg_list = {}
	local longzhu_level = self:GetLongZhuSkillLevel()
	local next_level = longzhu_level + 1

	for i,v in ipairs(desc_cfg_list) do
		if longzhu_level >= v.active_skill_level then
			now_cfg_list[v.skill_type] = v
			nex_cfg_list[v.skill_type] = v
			now_lock_cfg_list[v.skill_type] = nil
			nex_lock_cfg_list[v.skill_type] = nil
		elseif next_level == v.active_skill_level then
			nex_cfg_list[v.skill_type] = v
			nex_lock_cfg_list[v.skill_type] = nil
		end

		if now_cfg_list[v.skill_type] == nil and now_lock_cfg_list[v.skill_type] == nil then
			now_lock_cfg_list[v.skill_type] = v
		end
		if nex_cfg_list[v.skill_type] == nil and nex_lock_cfg_list[v.skill_type] == nil then
			nex_lock_cfg_list[v.skill_type] = v
		end
	end

	return now_cfg_list, nex_cfg_list, now_lock_cfg_list, nex_lock_cfg_list
end

function LongZhuWGData:GetLongZhuSkillEffect(longzhu_skill_level)
	longzhu_skill_level = longzhu_skill_level or 1
	local max_longzhu_level = self:GetMaxLongZhuSkillLevel()
	longzhu_skill_level = math.max(longzhu_skill_level, 1)
	longzhu_skill_level = math.min(longzhu_skill_level, max_longzhu_level)

	local cfg = self:GetLongZhuSkillCfg(longzhu_skill_level)
	return cfg.bundle, cfg.asset
end
--end龙珠表格数据]]

---[[ 龙珠后端信息
function LongZhuWGData:SetLongZhuInfo(protocol)
	self.long_zhu_level_list = protocol.level_list
	self.long_zhu_fuhuo_count = protocol.role_free_relive_times
end

function LongZhuWGData:GetLongZhuLevelList()
	return self.long_zhu_level_list
end

function LongZhuWGData:GetLongZhuLevelById(id)
	return self.long_zhu_level_list[id] or 0
end

function LongZhuWGData:GetFuHuoCount()
	return self.long_zhu_fuhuo_count or 0
end

-- 前7级激活一个龙珠算一级 7级后取所有龙珠中最低等级作为龙珠等级
function LongZhuWGData:GetLongZhuLevel()
	local level_list = self:GetLongZhuLevelList()
	local level = 0
	local activate_num = 0
	
	if not IsEmptyTable(level_list) then
		for i=1,#level_list do
			if level_list[i] > 0 then
				activate_num = activate_num + 1
			end
			if level == 0 or level > level_list[i] then
				level = level_list[i]
			end
		end
	end

	if activate_num >= LONGZHU_TYPE_MAX then
		return level + activate_num - 1
	else
		return activate_num
	end
end

-- 龙珠技能等级
function LongZhuWGData:GetLongZhuSkillLevel()
	local longzhu_level = self:GetLongZhuLevel()
	local max_level = self:GetMaxLongZhuSkillLevel()
	if longzhu_level > max_level then
		return max_level
	end
	return longzhu_level
end
--]]

-- 特殊龙珠增幅其它龙珠的所有属性
function LongZhuWGData:GetLongZhuAttrAddPre()
	local specia_id = self:GetOtherCfg("special_attr_longzhu_id")
	local level = self:GetLongZhuLevelById(specia_id)
	local specia_attr_cfg = self:GetLongZhuSpecialAttrCfg(level)
	if specia_attr_cfg then
		return specia_attr_cfg.attr_value / 10000
	end
	return 0
end

-- 所有龙珠是否都激活了
function LongZhuWGData:AllLongZhuIsActive()
	local is_active = false
	local level_list = self:GetLongZhuLevelList()
	if not IsEmptyTable(level_list) then
		is_active = true
		for i=1,#level_list do
			if level_list[i] <= 0 then
				is_active = false
				break
			end
		end
	end
	return is_active
end

-- 龙珠激活数
function LongZhuWGData:GetLongZhuActiveNum()
	local num = 0
	local level_list = self:GetLongZhuLevelList()
	if not IsEmptyTable(level_list) then
		for i=1,#level_list do
			if level_list[i] > 0 then
				num = num + 1
			end
		end
	end
	return num
end

-- 龙珠全身等级 从全部激活后开始算
function LongZhuWGData:GetLongZhuAvgSkillLevel()
	if self:AllLongZhuIsActive() then
		local longzhu_level = self:GetLongZhuLevel()
		return longzhu_level - LONGZHU_TYPE_MAX + 1
	end
	return 0
end

-- 是否有一个龙珠激活了
function LongZhuWGData:HasLongZhuActive()
	local level_list = self:GetLongZhuLevelList()
	if not IsEmptyTable(level_list) then
		for i=1,#level_list do
			if level_list[i] > 0 then
				return true
			end
		end
	end
	return false
end

-- 是否是龙珠材料 该龙珠是否激活了
function LongZhuWGData:IsLongZhuStuff(item_id)
	if self.long_zhu_stuff_map[item_id] then
		return true
	end
	return false
end

-- 根据item_id获取该龙珠是否激活
function LongZhuWGData:IsActiveByItemId(item_id)
	if self.long_zhu_stuff_map[item_id] then
		local id = self.long_zhu_stuff_map[item_id]
		local level = self:GetLongZhuLevelById(id)
		return level > 0
	end
end

-- 根据item_id获取该龙珠等级
function LongZhuWGData:GetLongZhuLevelByItemId(item_id)
	local level = 0
	if self.long_zhu_stuff_map[item_id] then
		local id = self.long_zhu_stuff_map[item_id]
		level = self:GetLongZhuLevelById(id)
	end
	return level
end

-- 根据item_id获取该道具是否显示红点
function LongZhuWGData:IsRemindByItemId(item_id)
	local id = self.long_zhu_stuff_map[item_id]
	if id then
		local is_open = FunOpen.Instance:GetFunIsOpened(FunName.LongZhuView)
		return is_open and self:IsRemindById(id)
	end
end

-- 根据龙珠id是否显示红点
function LongZhuWGData:IsRemindById(id)
	local level = LongZhuWGData.Instance:GetLongZhuLevelById(id)
	local cfg = LongZhuWGData.Instance:GetLongZhuCfg(id, level + 1)

	if cfg then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(cfg.consume_item)
		return has_num >= cfg.consume_num
	end

	return false
end

function LongZhuWGData:GetLongZhuRemind()
	if self:IsShowLongZhuRedPoint() then
		return 1
	end
	return 0
end

---[[ 龙珠材料在背包的红点显示
function LongZhuWGData:IsShowLongZhuRedPoint()
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.LongZhuView)
	if is_open then
		local item_list = self:GetAllLongZhuItemIdList()
		local has_num = 0
		for item_id,need_num in pairs(item_list) do
			has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
			if has_num >= need_num then
				self:ShowLongZhuStrong(1)
				return true
			end
		end
	end
	self:ShowLongZhuStrong(0)
end

-- 获取所有龙珠当前等级激活or升级材料所需
function LongZhuWGData:GetAllLongZhuItemIdList()
	local item_list = {}
	local level = 0
	local cfg = nil
	for id = 1, LONGZHU_TYPE_MAX do
		level = self:GetLongZhuLevelById(id)
		cfg = self:GetLongZhuCfg(id, level + 1)
		if cfg then
			item_list[cfg.consume_item] = cfg.consume_num
		end
	end
	return item_list
end
--]]

-- 我要变强里添加龙珠升级
function LongZhuWGData:ShowLongZhuStrong(num)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LONG_ZHU, num, function ()
				ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.rolebag_longzhu)
				return true
			end)
end