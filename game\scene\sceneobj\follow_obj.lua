FollowObj = FollowObj or BaseClass(Character)

-- 小鬼自动捡物品速度默认30
local moveto_pcik_speed = 30

FollowObj.Count = 0

function FollowObj:__init(vo)
	FollowObj.Count = FollowObj.Count + 1
    self.update_num = 0
    self.elapse_time = 0
    self.shadow_level = 2

	self.followui_class = FollowObjectFollow
	---------------- 子类可以修改的参数 ----------------------
	-- 主人obj_id
	self.owner_obj_id = vo.owner_obj_id or 0x10000
	-- 是否随机漫步
	self.is_wander = false
	-- 跟随偏移值
	self.follow_offset = 0
	-- 最大速度
	self.max_speed = 13
	-- 最大速度平方
	self.sqrt_max_speed = 13 * 13
	-- 最大牵引力
	self.max_force = 20
	-- 最大牵引力平方
	self.sqrt_max_force = 20 * 20
	-- 质量
	self.mass = 1
	-- 最大距离平方（超出这个距离则瞬移到跟随的物体）
	self.sqrt_max_distance = 50 * 50
	-- 减速距离平方
	self.sqrt_slow_down_distance = 6 * 6
	-- 停止距离平方
	self.sqrt_stop_distance = 2 * 2
	-- 随机漫步范围
	self.wander_distance = 5
	-- 随机漫步范围平方
	self.sqrt_wander_distance = 5 * 5
	-- 随机漫步速度
	self.max_wander_speed = 5
	-- 随机漫步CD
	self.wander_cd = 7
	--默认速度
	self.default_speed = 13
	--目标移动完成后朝向主角
	self.direction_owner = false
	-- 驭兽正方向距离
	self.radius = 4			

	self.speed = 0

	self.max_move_offset = 3

	self.shield_obj_type = ShieldObjType.FollowObj

	self.owner_obj = vo.owner_obj

	self.shield_effect_type = ShieldObjType.FollowObjEffect

	------------------ 子类不可修改的参数 ------------------
	-- 加速度
	self.acceleration = u3d.vec2(0, 0)
	-- 速率
	self.velocity = u3d.vec2(0, 0)
	-- target的坐标
	self.last_target_pos = u3d.vec2(0, 0)
	self.last_dir = u3d.vec2(0, 0)

	self.check_cd = 0.4
	self.timer = 0
	self.is_stop_move = true
	self.last_wander_time = 0
	self.last_wander_pos = u3d.vec2(0, 0)
	self.status = 0 -- 移动状态 0 移动 1 战斗
	self.is_moveto_pcik = false

	self.check_pick_timer = 0

	self:GetUpdateNumber()
	self.is_main_role = self.owner_obj and self.owner_obj.IsMainRole and self.owner_obj:IsMainRole() or false
end

function FollowObj:__delete()
	FollowObj.Count = FollowObj.Count - 1
	self.owner_obj = nil
	self.vo.owner_obj = nil
	self.same_group = {}

	ReuseableHandleManager.Instance:ReleaseShieldHandle(self.effect_handle)
	self.effect_handle = nil
	self.check_pick_timer = 0
end

-- 其他玩家品质等级-1，主角+1
function FollowObj:GetQualityOffsetLevel()
	local offset = -1
	if self.is_main_role then
		offset = 1
	end

    return offset
end

function FollowObj:InitInfo()
    Character.InitInfo(self)

    self.effect_handle = ReuseableHandleManager.Instance:GetShieldHandle(RolePartEffectShieldHandle, self, self.shield_effect_type, SceneObjPart.Main)
    self.effect_handle:CreateShieldHandle()

    local offset = self:GetQualityOffsetLevel()
    self:SetQualityLevelOffset(offset)
    self:SetEffectQualityLevelOffset(offset)
    self:UpdateEffectVisible()
end

function FollowObj:InitAppearance()
	local target_pos = self:GetFollowTargetPos()

	if target_pos then
		self.last_target_pos = target_pos
	end
end

function FollowObj:GetFollowTargetPos()
	local target_pos = self:GetCurFollowTargetRealPos()

	if target_pos then
		return target_pos
	end

	local target = self.owner_obj

	if target then
		return target.real_pos
	end
end

-- 跟随物体重写此方法，设置自定义跟随位置
function FollowObj:GetCurFollowTargetRealPos()
end

function FollowObj:OnEnterScene()
	Character.OnEnterScene(self)
	self:CreateShadow()
end

function FollowObj:SetAttr(key, value)
	Character.SetAttr(self, key, value)
end

function FollowObj:GetUpdateNumber()
	if self.is_main_role then
		self.update_num = 5
	else
    	self.update_num = 10 + math.ceil(FollowObj.Count / 5)
    end
end

function FollowObj:EnterStateStand()
	self.is_move_over_pos = false
	self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle)
end

function FollowObj:Update(now_time, elapse_time)
	if not self.is_moveto_pcik then
		self.check_pick_timer = 0
	end

	self.update_num = self.update_num - 1
    self.elapse_time = self.elapse_time + elapse_time
    if self.update_num > 0 then
        return
    end

    elapse_time = self.elapse_time
    self.elapse_time = 0
    self:GetUpdateNumber()

	Character.Update(self, now_time, elapse_time)
	if not self.is_main_role and not self:GetVisiable() then
		return
	end

	self.timer = elapse_time + self.timer
	if self.is_moveto_pcik then
		if self.check_pick_timer ~= 0 and self.check_pick_timer <= Status.NowTime then
			self.check_pick_timer = 0
			self:CheckPickByQuitMove()
		end
		-- 小鬼自动捡东西默认50速度
		self.speed = moveto_pcik_speed
		return
	elseif self:IsFightState() then
		if self:IsPet() or (self:IsBeast() and self:IsNotMoveUseSkill()) or self:IsTaskCallInfo() then
			self.speed = self:GetOwnerSpeed()
		end

		return
	end

    if self.timer > self.check_cd then
    	-- 目标在轻功时，原地待命
    	local target = self.owner_obj
    	if target and target.IsRole and target:IsRole() then
    		if target:IsQingGong() or target:IsMitsurugi() then
    			return
    		end
    	end

		-- 牵引力
	    local steeringForce = u3dpool.vec2(0, 0)
    	local sqrt_distance = self:GetOwnerDistance(false)
    	-- 超出最大距离，则瞬移过来
	    if sqrt_distance > self.sqrt_max_distance then
	    	local target = self.owner_obj
	    	if nil ~= target and nil ~= target:GetRoot() then
	    		if self:GetRoot() then
	    			local tar_x, tar_y = target:GetLogicPos()
	    			tar_x, tar_y = AStarFindWay:GetAroundVaildXY(tar_x, tar_y, 2, false)
	    			tar_x, tar_y = GameMapHelper.LogicToWorld(tar_x, tar_y)
	    			self:GetRoot().transform.position = Vector3(tar_x, target:GetRoot().transform.position.y, tar_y)
	    			u3dpool.reset(self.velocity)
					target:GeBeastRealPosNumber(tar_x, tar_y, self.battle_index, self.radius)
	    			self:SetRealPos(tar_x, tar_y)
	    			return
	    		end
	    	end
	    end
	    if self.min_move_distance and self.min_move_distance > sqrt_distance and self:IsStand() or self.status == 1 then
	    	if self.status == 1 and self:IsMove() then
	    		self:DoStand()
	    	end
	    	return
	    end
	    -- 计算牵引力
    	steeringForce = u3dpool.v2Add(steeringForce, self:FollowForce())
    	if u3dpool.v2Length(steeringForce, false) > self.sqrt_max_force then
    		steeringForce = u3dpool.v2Mul(u3dpool.v2Normalize(steeringForce), self.max_force)
    	end
    	-- 加速度
	    self.acceleration = u3dpool.v2Mul(steeringForce, 1 / self.mass,  self.acceleration)
	    self.timer = 0
	end


	if self.is_stop_move then
		u3dpool.reset(self.velocity)
		self.speed = 0
		self:StopMove()
	else
		-- 计算速率
		self.velocity = u3dpool.v2Add(self.velocity, u3dpool.v2Mul(self.acceleration, elapse_time), self.velocity)
		if u3dpool.v2Length(self.velocity, false) > self.sqrt_max_speed then
		    self.velocity = u3dpool.v2Mul(u3dpool.v2Normalize(self.velocity), self.max_speed, self.velocity)
		end
		self.speed = u3dpool.v2Length(self.velocity)
		local move_ment = u3dpool.v2Mul(self.velocity, elapse_time)
		self:DoMove(u3dpool.v2Add(self.real_pos, move_ment))
	end
end


function FollowObj:DoMove(target_pos, is_special)
	local target_to_aim = u3dpool.v2Sub(target_pos, self.last_target_pos)
	local logic_x, logic_y = GameMapHelper.WorldToLogic(target_pos.x, target_pos.y)
	-- 如果目标点是Block，则沿着移动方向找一个可以站立的点
	if AStarFindWay:IsBlock(logic_x, logic_y) then
		local distance = u3dpool.v2Length(u3dpool.v2Sub(target_pos, target_pos))
		distance = distance - 0.5
		while(distance > 0) do
			target_pos = u3dpool.v2Add(target_pos, u3dpool.v2Mul(u3dpool.v2Normalize(target_to_aim), distance))
			distance = distance - 0.5
			local logic_x, logic_y = GameMapHelper.WorldToLogic(target_pos.x, target_pos.y)
			if not AStarFindWay:IsBlock(logic_x, logic_y) then
				u3dpool.set(self.last_target_pos, target_pos.x, target_pos.y)
				break
			end
		end
	end

    if is_special then
    	Character.DoMove(self, target_pos.x, target_pos.y)
    else
	    if self.draw_obj then
	    	local h = 0
	    	local target = self.owner_obj
	    	if target ~= nil and not target:IsDeleted() then
	    		local pos = target:GetLuaPosition()
	    		h = pos.y
	    	end

	        self.draw_obj:SetDirectionByXY(target_pos.x, target_pos.y)
	        self.draw_obj:MoveTo(target_pos.x, target_pos.y, self:GetMoveSpeed(), h)

	        self.move_end_pos.x, self.move_end_pos.y = target_pos.x, target_pos.y
	        local delta_pos = u3dpool.v2Sub(self.move_end_pos, self.real_pos)

	        self.move_total_distance = u3dpool.v2Length(delta_pos) * 100
	        self:SetMoveDir(u3dpool.v2Normalize(delta_pos, self.move_dir))
	        self.move_pass_distance = 0.0
	        --如果当前不在移动状态则切换至移动状态
	        if not self:IsMove() then
	            self.state_machine:ChangeState(SceneObjState.Move)
	        end
	    end
    end
end

-- 战斗屏蔽随机移动
function FollowObj:ShieldWadnerForce(target)
	return false
end

-- 跟随力
function FollowObj:FollowForce()
	local desired_velocity = u3dpool.vec2(0, 0)
	local target = self.owner_obj
	if target then
		self.is_stop_move = false
		local target_pos = self:GetFollowTargetPos()
		
		local fixed_target_pos = u3dpool.v2Add(target_pos, u3dpool.v2Mul(u3dpool.vec2(0.5, 0.5), self.follow_offset))
		local to_target = u3dpool.v2Sub(fixed_target_pos, self.real_pos)
		local target_is_moving = false
		local target_movement = u3dpool.v2Sub(target_pos, self.last_target_pos)
		if u3dpool.v2Length(target_movement, false) > 0.01 then
			target_is_moving = true
		end
		if not target_is_moving and self.is_wander and not self:ShieldWadnerForce(target) then
			if self.is_main_role then
				self:SetMaxSpeed(self.default_speed)
				desired_velocity = self:WadnerForce()
			else
				self.is_stop_move = true
			end
		else
			local sqrt_distance = self:GetOwnerDistance(false)
			-- 如果距离大于减速距离则全速靠近
			if sqrt_distance > self.sqrt_slow_down_distance then
				self:UpdateMaxSpeed()
				desired_velocity = u3dpool.v2Mul(u3dpool.v2Normalize(to_target), self.max_speed)
			else
				-- 如果目标在移动，保持与目标相同的速度
				if target_is_moving then
					local elapse_time = self.timer
					if elapse_time == 0 then
						elapse_time = 0.0001
					end
					local target_velocity = u3dpool.v2Mul(target_movement, 1 / elapse_time)
					desired_velocity = u3dpool.v2Mul(u3dpool.v2Normalize(to_target), u3dpool.v2Length(target_velocity))
				else
					-- 减速靠近目标
					self:SetMaxSpeed(self.default_speed)
					desired_velocity = u3dpool.v2Sub(to_target, self.velocity)
			    	-- 如果小于停止距离
			    	if sqrt_distance <= self.sqrt_stop_distance then
						if self.direction_owner then
							self.draw_obj:SetDirectionByXY(target.real_pos.x, target.real_pos.y)
						end
					
						self.is_stop_move = true
					end
				end
			end
		end
		u3dpool.set(self.last_target_pos, target_pos.x, target_pos.y)
	end
    return u3dpool.v2Sub(desired_velocity, self.velocity)
end

-- 随机漫步
function FollowObj:WadnerForce()
	self.is_stop_move = true
	local desired_velocity = u3dpool.vec2(0, 0)
	local target = self.owner_obj
	if target then
		local target_pos = self:GetFollowTargetPos()
		local wander_pos = self.last_wander_pos
		if self.last_wander_time + self.wander_cd < Status.NowTime then
			self.last_wander_time = Status.NowTime
			local random_pos = u3dpool.vec2((math.random() - 0.5) * 2 * self.wander_distance, (math.random() - 0.5) * 2 * self.wander_distance)
			wander_pos = u3dpool.v2Add(target_pos, random_pos)
			u3dpool.set(self.last_wander_pos, wander_pos.x, wander_pos.y)
			self.last_dir.x = 0
			self.last_dir.y = 0
		end

		local target_to_wander = u3dpool.v2Sub(wander_pos, target_pos)
		if u3dpool.v2Length(target_to_wander, false) > self.sqrt_wander_distance then
			wander_pos = u3dpool.v2Add(target_pos, u3dpool.v2Mul(u3dpool.v2Normalize(target_to_wander), self.wander_distance))
			u3dpool.set(self.last_wander_pos, wander_pos.x, wander_pos.y)
		end

		local can_move = true
		local logic_x, logic_y = GameMapHelper.WorldToLogic(wander_pos.x, wander_pos.y)
		-- 如果目标点是Block，则沿着移动方向找一个可以站立的点
		if AStarFindWay:IsBlock(logic_x, logic_y) then
			can_move = false
			local distance = u3dpool.v2Length(u3dpool.v2Sub(wander_pos, target_pos))
			distance = distance - 0.5
			while(distance > 0) do
				wander_pos = u3dpool.v2Add(target_pos, u3dpool.v2Mul(u3dpool.v2Normalize(target_to_wander), distance))
				distance = distance - 0.5
				local logic_x, logic_y = GameMapHelper.WorldToLogic(wander_pos.x, wander_pos.y)
				if not AStarFindWay:IsBlock(logic_x, logic_y) then
					can_move = true
					u3dpool.set(self.last_wander_pos, wander_pos.x, wander_pos.y)
					break
				end
			end
		end

		if can_move then
			local to_wander = u3dpool.v2Sub(wander_pos, self.real_pos)
			local sqrt_distance = u3dpool.v2Length(to_wander, false)
			if sqrt_distance > 0.5 then
				local dir = u3dpool.v2Normalize(to_wander)
				if dir.x * self.last_dir.x >= 0 and dir.y * self.last_dir.y >= 0 then
					desired_velocity = u3dpool.v2Mul(dir, self.max_wander_speed)
					self.last_dir.x = dir.x
					self.last_dir.y = dir.y
					self.is_stop_move = false
				end
			end
		end
	end
	return desired_velocity
end

-- 停止移动
function FollowObj:StopMove()
	if not self:IsAtk() and not self.state_machine:IsInState(SceneObjState.Stand) then
		self.state_machine:ChangeState(SceneObjState.Stand)
	end
end

function FollowObj:GetMoveSpeed()
	return self.speed
end

function FollowObj:GetOwnerID()
	return self.vo.owner_obj_id
end

function FollowObj:GetOwnerObj()
	return self.owner_obj
end

-- 得到与跟随物的距离
function FollowObj:GetOwnerDistance(is_sqrt)
	local distance = 0
	local target = self.owner_obj
	if target then
		local target_pos = self:GetFollowTargetPos()
		local logic = u3d.vec2(0, 0)
		logic.x, logic.y = GameMapHelper.WorldToLogic(target_pos.x, target_pos.y)
		distance = self:GetLogicDistance(logic, is_sqrt)
	end
	return distance
end

function FollowObj:SetMaxSpeed(max_speed)
	self.max_speed = max_speed
	self.sqrt_max_speed = max_speed * max_speed
end

function FollowObj:SetMaxForce(max_force)
	self.max_force = max_force
	self.sqrt_max_force = max_force * max_force
end

function FollowObj:IsFollowObj()
	return true
end

--根据主角速度更新灵宠速度
function FollowObj:UpdateMaxSpeed()
    local target = self.owner_obj
    if nil ~= target and nil ~= target:GetRoot() then
    	self:SetMaxSpeed(target:GetMoveSpeed() + self.max_move_offset)
    end
end

function FollowObj:GetOwnerSpeed()
    local target = self.owner_obj
    if nil ~= target and not target:IsDeleted() then
    	return target:GetMoveSpeed()
    end

    return self.default_speed
end

function FollowObj:PartEffectVisibleChanged(part, visible)
    self.draw_obj:SetPartIsDisableAttachEffect(part, not visible)
end

function FollowObj:SetEffectQualityLevelOffset(offset)
	if self.effect_handle then
		self.effect_handle:SetQualityLevelOffset(offset)
	end
end

function FollowObj:ChangeModel(part, bundle_name, asset_name, callback)
	Character.ChangeModel(self, part, bundle_name, asset_name, callback)

	if part == SceneObjPart.Main then
		-- local offset = self:GetQualityOffsetLevel()
		local base_offset = self:GetQualityOffsetLevel()

		local model_offset = base_offset
		-- if bundle_name and "" ~= bundle_name and asset_name and "" ~= asset_name then
		--     model_offset = model_offset + QualityWGData.Instance:GetModelQualityOffsetConfig(bundle_name, asset_name)
		-- end
		self:SetQualityLevelOffset(model_offset)

		local effect_offset = base_offset
		-- if bundle_name and "" ~= bundle_name and asset_name and "" ~= asset_name then
		--     effect_offset = effect_offset + QualityWGData.Instance:GetModelEffectQualityOffsetConfig(bundle_name, asset_name)
		-- end
		self:SetEffectQualityLevelOffset(effect_offset)
	end
end

function FollowObj:CheckPickByQuitMove(obj_id, obj_type)
end

function FollowObj:UpdateEffectVisible()
	local owner_obj = self:GetOwnerObj()
	if self.effect_handle and owner_obj and not owner_obj:IsDeleted() and owner_obj.GetRoleEffectVisible then
		local visible = owner_obj:GetRoleEffectVisible()
		if visible then
		    self.effect_handle:CancelForceSetVisible()
		else
		    self.effect_handle:ForceSetVisible(false)
		end
	end
end

-- 是否不是使用技能移动中
function FollowObj:IsNotMoveUseSkill()
	return true
end