LoginCreateRoleDIYView = LoginCreateRoleDIYView or BaseClass(SafeBaseView)
local camera_distance_type = {
    Default = 1,            -- 默认
    DIY = 2,                -- 自定义
    Suit = 3,               -- 套装
}

local camera_distance_param = {
    [camera_distance_type.Default] = {
        [0] = {
            position = Vector3(0, 2.02, -5.41),
            rotation = Vector3(0, 0, 0),
            fieldOfView = 20,
            bg_position = Vector3(75.738, -93.09, 38.84),
            bg_scale = Vector3(0.351, 0.351, 0.528),
            role_pos = Vector3(0, 0.1, 0)
        },
        [1] = {
            position = Vector3(0, 2.02, -5.41),
            rotation = Vector3(0, 0, 0),
            fieldOfView = 20,
            bg_position = Vector3(75.738, -93.09, 38.84),
            bg_scale = Vector3(0.351, 0.351, 0.528),
            role_pos = Vector3(0, 0, 0)
        },
    },

    [camera_distance_type.DIY] = {
        [0] = {
            position = Vector3(0.1, 2.3, -3.16),
            rotation = Vector3(0, 0, 0),
            fieldOfView = 20,
            bg_position = Vector3(71.5, -64.1, 0),
            bg_scale = Vector3(0.25, 0.25, 0.25),
            role_pos = Vector3(0, 0, 0)
        },
        [1] = {
            position = Vector3(0.1, 2.3, -3.16),
            rotation = Vector3(0, 0, 0),
            fieldOfView = 20,
            bg_position = Vector3(71.5, -64.1, 0),
            bg_scale = Vector3(0.25, 0.25, 0.25),
            role_pos = Vector3(0, -0.1, 0)
        },
    },

    [camera_distance_type.Suit] = {
        [0] = {
            position = Vector3(0.3, 2, -6.6),
            rotation = Vector3(-9, 0, 0),
            fieldOfView = 60,
            bg_position = Vector3(71.5, -50.4, 0),
            bg_scale = Vector3(0.25, 0.25, 0.25),
            role_pos = Vector3(0, 0, 0)
        },
        [1] = {
            position = Vector3(0.3, 2, -6.6),
            rotation = Vector3(-8, 0, 0),
            fieldOfView = 60,
            bg_position = Vector3(71.5, -50.4, 0),
            bg_scale = Vector3(0.25, 0.25, 0.25),
            role_pos = Vector3(0, 0, 0)
        },
    },
}

local special_param = {
    [0] = {
        [1] = {
            rot_y = 165,
            anim = "cg_idle",
        }
    }
}

local show_type = {
    None = 0,
    DIY = 1,                -- 自定义
    Suit = 2,               -- 套装 
}

function LoginCreateRoleDIYView:__init()
    self.view_name = "LoginCreateRoleDIYView"
    self:ClearViewTween()
	self:AddViewResource(0, "uis/view/login_ui_prefab", "layout_login_create_role_diy")
    self.is_safe_area_adapter = true
end

function LoginCreateRoleDIYView:LoadCallBack()
    self.cur_face_res_id = nil
    self.cur_hair_res_id = nil
    self.cur_suit_seq = nil
    self.record_body_res_id = nil
    self.record_hair_res_id = nil
    self.record_face_res_id = nil

    self.cur_body_res_id = 0
    self.cur_hair_res_id = 0
    self.cur_face_res_id = 0
    self.cur_weapon_res_id = 0
    self.cur_wing_res_id = 0
    self.cur_fabao_res_id = 0
    self.cur_halo_res_id = 0
    self.cur_mount_res_id = 0
    self.cur_pet_res_id = 0

    self.suit_show_list = {}
    self.cur_show_type = show_type.None
    self.cur_camera_distance_type = camera_distance_type.Default

    self.face_list = {}
    local node_num = self.node_list.face_list.transform.childCount
    for i = 1, node_num do
        self.face_list[i] = CreatePartDIYRender.New(self.node_list.face_list:FindObj("item" .. i))
        self.face_list[i]:SetIndex(i)
        self.face_list[i]:SetPartType(ROLE_SKIN_TYPE.FACE)
        self.face_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSelectPartCallBack, self))
    end

    self.hair_list = {}
    node_num = self.node_list.hair_list.transform.childCount
    for i = 1, node_num do
        self.hair_list[i] = CreatePartDIYRender.New(self.node_list.hair_list:FindObj("item" .. i))
        self.hair_list[i]:SetIndex(i)
        self.hair_list[i]:SetPartType(ROLE_SKIN_TYPE.HAIR)
        self.hair_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSelectPartCallBack, self))
    end

    self.input_name = self.node_list["name_input"]
	self.input_name.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnInputNameChange, self))
    self:InitRanDomName()

    XUI.AddClickEventListener(self.node_list["close_btn"], BindTool.Bind(self.ReturnToLoginCreateView, self, true))
    XUI.AddClickEventListener(self.node_list["crap_btn"], BindTool.Bind(self.OnCrapsClick, self))
    XUI.AddClickEventListener(self.node_list["confirm_btn"], BindTool.Bind(self.OnClickCreateConfirm, self))
    XUI.AddClickEventListener(self.node_list["show_suit_btn"], BindTool.Bind(self.OnClickShowSuit, self))




    

    self.node_list["event_trigger_listener"]:SetActive(false)
	self.node_list["event_trigger_listener"].event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDrawObjDrag, self))

    self.draw_obj_parent_transform = UnityEngine.GameObject.Find("RolePos").transform
    local camera_obj = UnityEngine.GameObject.Find("Camera")
    self.scene_camera = camera_obj and camera_obj:GetComponent(typeof(UnityEngine.Camera)) or nil
    self.scene_bg = UnityEngine.GameObject.Find("Background").transform:GetChild(0)
end

function LoginCreateRoleDIYView:ReleaseCallBack()
    self:KillCameraMoveTween()
    self:DestroyDIYRoleDrawObj()
    self:DestroyDIYMountDrawObj()
    self:DestroyDIYPetDrawObj()

    self.input_name = nil
    if self.face_list then
        for k,v in pairs(self.face_list) do
            v:DeleteMe()
        end
        self.face_list = nil
    end

    if self.hair_list then
        for k,v in pairs(self.hair_list) do
            v:DeleteMe()
        end
        self.hair_list = nil
    end

    self.next_click_time = nil
    self.draw_obj_parent_transform = nil
    self.cur_face_res_id = nil
    self.cur_hair_res_id = nil
    self.cur_suit_seq = nil
    self.record_body_res_id = nil
    self.record_hair_res_id = nil
    self.record_face_res_id = nil
    self.cur_body_res_id = 0
    self.cur_hair_res_id = 0
    self.cur_face_res_id = 0
    self.cur_weapon_res_id = 0
    self.cur_wing_res_id = 0
    self.cur_fabao_res_id = 0
    self.cur_halo_res_id = 0
    self.cur_mount_res_id = 0
    self.cur_pet_res_id = 0
    self.suit_show_list = {}
    self.cur_show_type = show_type.None
    self.scene_camera = nil
end

-- return_create  true:手动点击  false：一般为断线重连
function LoginCreateRoleDIYView:ReturnToLoginCreateView(return_create)
    local now_time = Status.NowTime
    if return_create and self.cur_show_type == show_type.Suit then
        self:ToShowRoleDiy()
        self.next_click_time = now_time + 1
        return
    end

    self.cur_face_res_id = nil
    self.cur_hair_res_id = nil
    self.cur_suit_seq = nil
    self.record_body_res_id = nil
    self.record_hair_res_id = nil
    self.record_face_res_id = nil
    self.cur_body_res_id = 0
    self.cur_hair_res_id = 0
    self.cur_face_res_id = 0
    self.cur_weapon_res_id = 0
    self.cur_wing_res_id = 0
    self.cur_fabao_res_id = 0
    self.cur_halo_res_id = 0
    self.cur_mount_res_id = 0
    self.cur_pet_res_id = 0
    self.suit_show_list = {}
    self.cur_show_type = show_type.None

    self:KillCameraMoveTween()
    self:DestroyDIYRoleDrawObj()
    self:DestroyDIYMountDrawObj()
    self:DestroyDIYPetDrawObj()

    self:OnShowTypeChange(195)
    self.cur_camera_distance_type = camera_distance_type.Default
    if not IsNil(self.scene_camera) then
        local sex = self.data and self.data.sex or 0
        local param = (camera_distance_param[self.cur_camera_distance_type] or {})[sex]
		self.scene_camera.transform.localPosition = param.position
        self.scene_camera.transform.rotation = Quaternion.Euler(param.rotation.x, param.rotation.y, param.rotation.z)
        self.scene_camera.fieldOfView = param.fieldOfView

        if not IsNil(self.scene_bg) then
            self.scene_bg.localPosition = param.bg_position
            self.scene_bg.localScale = param.bg_scale
        end
        if not IsNil(self.role_draw_obj_transform) then
            self.role_draw_obj_transform.localPosition = param.role_pos
        end
	end


    self:Close()
    LoginWGCtrl.Instance:ChangeLoginViewShow(true)
    if return_create then
        local login_view = LoginWGCtrl.Instance:GetView()
        if login_view then
            login_view:OnDIYToCreate()
        end
    end
end

-- {prof = 0, sex = 0}
function LoginCreateRoleDIYView:SetDataAndOpen(data)
    if not data then
        return
    end

    self.data = data
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.CreateRoleDIY, data.sex, data.prof)

	self:DestroyDIYRoleDrawObj()
    self:DestroyDIYMountDrawObj()
    self:DestroyDIYPetDrawObj()
    self.diy_do_onece_anim = true
    self:Open()
end

-- 点击DIY回调
function LoginCreateRoleDIYView:OnClickSelectPartCallBack(cell)
    if not cell or not cell.data then
        return
    end

    local data = cell.data
    local type = cell:GetPartType()
    local is_diy_state = self.cur_show_type == show_type.DIY
    self.need_fake_hide = not is_diy_state
    if type == ROLE_SKIN_TYPE.HAIR then
        if is_diy_state and self.record_hair_res_id == data.resouce_id then
            return
        else
            self.record_hair_res_id = data.resouce_id
        end
    elseif type == ROLE_SKIN_TYPE.FACE then
        if is_diy_state and self.record_face_res_id == data.resouce_id then
            return
        else
            self.record_face_res_id = data.resouce_id
        end
    else
        return
    end

    self:ToShowRoleDiy()
end

function LoginCreateRoleDIYView:OnFlush()
    self:RandomName()
    self:ChangeProfDesc()

    local sex = self.data.sex
    local prof = self.data.prof
     -- 【职业改动修改】
    local is_no_face = false
    local is_no_hair = false--(sex == GameEnum.FEMALE and prof == GameEnum.ROLE_PROF_2)
    local face_data = LoginWGData.Instance:GetCreateRoleDIYCfg(ROLE_SKIN_TYPE.FACE, sex, prof)
    local hair_data = LoginWGData.Instance:GetCreateRoleDIYCfg(ROLE_SKIN_TYPE.HAIR, sex, prof)
    if is_no_face or IsEmptyTable(face_data) or self.cur_show_type == show_type.Suit then
        self.node_list["face_part"]:SetActive(false)
    else
        for k,v in pairs(self.face_list) do
            v:SetData(face_data[k])
        end

        self.node_list["face_part"]:SetActive(true)
    end

    if is_no_hair or IsEmptyTable(hair_data) or self.cur_show_type == show_type.Suit then
        self.node_list["hair_part"]:SetActive(false)
    else
        for k,v in pairs(self.hair_list) do
            v:SetData(hair_data[k])
        end

        self.node_list["hair_part"]:SetActive(true)
    end

    -- 套装
    self.suit_show_list = LoginWGData.Instance:GetCreateRoleSuitCfg(sex, prof)
    -- if #self.suit_show_list == 1 then
    --     self.node_list["show_suit_btn"]:SetActive(true)

    --     local suit_icon = string.format("suit_%s_%s", sex, prof)
    --     local bundle, asset = ResPath.GetCreateRoleIcon(suit_icon)
    --     self.node_list["show_suit_btn"].image:LoadSprite(bundle, asset, function ()
    --         self.node_list["show_suit_btn"].image:SetNativeSize()
    --     end)
    -- else
    --     self.node_list["show_suit_btn"]:SetActive(false)
    -- end

    if not self.cur_show_type or self.cur_show_type == show_type.None then
        self:ToShowRoleDiy()
    end
end

function LoginCreateRoleDIYView:ToShowRoleDiy()
    self.node_list["create_role_diy_left"]:SetActive(true)
	self.cur_suit_seq = nil
    self.cur_weapon_res_id = 0
    self:RemoveRoleWing()
    self:RemoveRoleHalo()
    self:RemoveRoleBaoJu()

    local sex = self.data.sex
    local prof = self.data.prof

    if not self.record_body_res_id or not self.record_hair_res_id or not self.record_face_res_id then
        local server_use_face_res, server_use_hair_res, server_use_body_res = RoleWGData.Instance:GetAllDefaultPartID(sex, prof)
        self.record_body_res_id = server_use_body_res
        self.record_hair_res_id = server_use_hair_res
        self.record_face_res_id = server_use_face_res
    end

    self.cur_body_res_id = self.record_body_res_id
    self.cur_hair_res_id = self.record_hair_res_id
    self.cur_face_res_id = self.record_face_res_id

    self.cur_show_type = show_type.DIY
    self:OnShowTypeChange()
    self:ShowRoleDrawObjMain(camera_distance_type.DIY)
end

----[[ 人物模型
function LoginCreateRoleDIYView:GetDIYRoleDrawObj()
	if nil == self.role_draw_obj then
		self.role_draw_obj = DrawObj.New(self, self.draw_obj_parent_transform)
        self.role_draw_obj:SetIsNeedSyncAnim(true)
        self.role_draw_obj:ChangeWaitSyncAnimType(SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE)

		self.role_draw_obj:SetIsInQueueLoad(false)
		self.role_draw_obj:SetIsUseObjPool(true)
        self.role_draw_obj:SetIsCanWeaponPointAttach(false)
	end

	return self.role_draw_obj
end

function LoginCreateRoleDIYView:DestroyDIYRoleDrawObj()
	if self.role_draw_obj ~= nil then
		self.role_draw_obj:DeleteMe()
		self.role_draw_obj = nil
	end

    self.role_draw_obj_transform = nil
end

function LoginCreateRoleDIYView:RemoveRoleWing()
	if not self.role_draw_obj then
		return
	end

	self.cur_wing_res_id = 0
	local part = self.role_draw_obj:GetPart(SceneObjPart.Wing)
	part:RemoveModel()
end

function LoginCreateRoleDIYView:RemoveRoleHalo()
	if not self.role_draw_obj then
		return
	end

	self.cur_halo_res_id = 0
	local part = self.role_draw_obj:GetPart(SceneObjPart.Halo)
	part:RemoveModel()
end

function LoginCreateRoleDIYView:RemoveRoleBaoJu()
	if not self.role_draw_obj then
		return
	end
	
	self.cur_fabao_res_id = 0
	local part = self.role_draw_obj:GetPart(SceneObjPart.BaoJu)
	part:RemoveModel()
end

-- 羽翼
function LoginCreateRoleDIYView:SetRoleWing()
	if not self.role_draw_obj then
		return
	end

    if self.cur_wing_res_id > 0 then
        local wing_part = self.role_draw_obj:GetPart(SceneObjPart.Wing)
        wing_part:SetIsCastShadow(true)
        wing_part:ChangeModel(ResPath.GetWingModel(self.cur_wing_res_id))
    end
end

-- 法宝
function LoginCreateRoleDIYView:SetRoleFaBao()
	if not self.role_draw_obj then
		return
	end

    if self.cur_fabao_res_id > 0 then
        local fabao_part = self.role_draw_obj:GetPart(SceneObjPart.BaoJu)
        fabao_part:SetIsCastShadow(true)
        fabao_part:ChangeModel(ResPath.GetFaBaoModel(self.cur_fabao_res_id))
    end
end

-- 光环
function LoginCreateRoleDIYView:SetRoleHalo()
	if not self.role_draw_obj then
		return
	end

    if self.cur_halo_res_id > 0 then
        local fabao_part = self.role_draw_obj:GetPart(SceneObjPart.Halo)
        fabao_part:SetIsCastShadow(true)
        fabao_part:ChangeModel(ResPath.GetHaloModel(self.cur_halo_res_id))
    end
end

-- 展示人物模型
function LoginCreateRoleDIYView:ShowRoleDrawObjMain(dis_type)
    local sex = self.data.sex
    local prof = self.data.prof

    if not self.draw_obj_parent_transform then
        return
    end

    for k,v in pairs(self.face_list) do
        v:OnSelectChange(self.cur_face_res_id)
    end

    for k,v in pairs(self.hair_list) do
        v:OnSelectChange(self.cur_hair_res_id)
    end

    self.node_list["show_suit_select"]:SetActive(self.cur_suit_seq ~= nil)

    local draw_obj = self:GetDIYRoleDrawObj()
    self.role_draw_obj_transform = draw_obj.root.transform
    -- 主角
    local main_part = draw_obj:GetPart(SceneObjPart.Main)
    -- 改为CastShadow是为了产生倒影
    main_part:SetIsCastShadow(true)
    local bundle, asset = ResPath.GetRoleModel(RoleWGData.GetJobModelId(sex, prof))

    local body_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.BODY, self.cur_body_res_id, sex, prof)
    local hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, self.cur_hair_res_id, sex, prof)
    local face_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.FACE, self.cur_face_res_id, sex, prof)

    local extra_model_data = {
        role_body_res = body_res,
        role_face_res = face_res,
        role_hair_res = hair_res,
    }

    draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset, function()
        self:ChangeCameraDistance(dis_type)
        if self.need_fake_hide then
            self:FakeHideDelayShow()
            self.need_fake_hide = nil
        end

    end, DRAW_MODEL_TYPE.ROLE, extra_model_data)
    self:ShowDrawObjPart()
    self:PlayRoleDrawObjAni()
end

-- 加载人物部位模型
function LoginCreateRoleDIYView:ShowDrawObjPart()
	local draw_obj = self:GetDIYRoleDrawObj()
	if self.data == nil or draw_obj == nil or draw_obj:IsDeleted() then
		return
	end

    local sex = self.data.sex
    local prof = self.data.prof

	-- 武器
	local weapon_res_id = 0
	if self.cur_weapon_res_id > 0 then
		weapon_res_id = RoleWGData.GetFashionWeaponId(sex, prof, self.cur_weapon_res_id)
	else
		weapon_res_id = RoleWGData.GetJobWeaponId(sex, prof)
	end

	local wepapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
	wepapon_part:SetIsCastShadow(true)

	local weapon_bundle, weapon_asset = ResPath.GetWeaponModelRes(weapon_res_id)
    draw_obj:ChangeModel(SceneObjPart.Weapon, weapon_bundle, weapon_asset)

    self:SetRoleWing()
    self:SetRoleFaBao()
    self:SetRoleHalo()
end

-- 人物模型做动画
function LoginCreateRoleDIYView:PlayRoleDrawObjAni()
    local draw_obj = self:GetDIYRoleDrawObj()
    if draw_obj == nil or draw_obj:IsDeleted() then
		return
	end

    if self.cur_show_type == show_type.DIY then
        if self.diy_do_onece_anim then
            local anim = SceneObjAnimator.UiIdle

            if self.data then
                local param = (special_param[self.data.sex] or {})[self.data.prof]
                if param and param.anim ~= nil then
                    anim = param.anim
                end
            end

            draw_obj:CrossAction(SceneObjPart.Main, anim)
            self.diy_do_onece_anim = nil
        end
    else
        self.diy_do_onece_anim = true
        draw_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.Walk)
    end
end

-- 假隐藏，主要为了让idel 过渡到uiIdel不突兀
function LoginCreateRoleDIYView:FakeHideDelayShow()
	local draw_obj = self:GetDIYRoleDrawObj()
	if not draw_obj then
		return
	end

	draw_obj:SetScale(0, 0, 0)
	GlobalTimerQuest:AddDelayTimer(function ()
		if not draw_obj then
			return
		end

		draw_obj:TryResetScale()
	end, 0.25)
end
-- 人物模型 end]]

----[[ 坐骑模型
function LoginCreateRoleDIYView:GetDIYMountDrawObj()
	if nil == self.mount_draw_obj then
		self.mount_draw_obj = DrawObj.New(self, self.draw_obj_parent_transform)
		self.mount_draw_obj:SetIsInQueueLoad(false)
		self.mount_draw_obj:SetIsUseObjPool(true)
        self.mount_draw_obj:SetIsCanWeaponPointAttach(false)
	end

	return self.mount_draw_obj
end

function LoginCreateRoleDIYView:DestroyDIYMountDrawObj()
	if self.mount_draw_obj ~= nil then
		self.mount_draw_obj:DeleteMe()
		self.mount_draw_obj = nil
	end
end

-- 展示坐骑模型
function LoginCreateRoleDIYView:ShowMountDrawObj(mount_action)
    if not self.draw_obj_parent_transform then
        return
    end

    local draw_obj = self:GetDIYMountDrawObj()
    local main_part = draw_obj:GetPart(SceneObjPart.Main)
    main_part:SetIsCastShadow(true)
    local bundle, asset = ResPath.GetMountModel(self.cur_mount_res_id)
    main_part:ChangeModel(bundle, asset, function()
        self:PlayMountDrawObjAni(mount_action)
    end)
end

function LoginCreateRoleDIYView:PlayMountDrawObjAni(mount_action)
    local draw_obj = self:GetDIYMountDrawObj()
    local main_part = draw_obj:GetPart(SceneObjPart.Main)
    main_part:CrossFade(mount_action, nil, nil, true)
end
-- 坐骑模型 end]]


----[[ 宠物模型
function LoginCreateRoleDIYView:GetDIYPetDrawObj()
	if nil == self.pet_draw_obj then
		self.pet_draw_obj = DrawObj.New(self, self.draw_obj_parent_transform)
		self.pet_draw_obj:SetIsInQueueLoad(false)
		self.pet_draw_obj:SetIsUseObjPool(true)
        -- self.pet_draw_obj:SetIsCanWeaponPointAttach(false)
	end

	return self.pet_draw_obj
end

function LoginCreateRoleDIYView:DestroyDIYPetDrawObj()
	if self.pet_draw_obj ~= nil then
		self.pet_draw_obj:DeleteMe()
		self.pet_draw_obj = nil
	end
end

-- 展示宠物模型
function LoginCreateRoleDIYView:ShowPetDrawObj()
    if not self.draw_obj_parent_transform then
        return
    end

    local draw_obj = self:GetDIYPetDrawObj()
    local main_part = draw_obj:GetPart(SceneObjPart.Main)
    main_part:SetIsCastShadow(true)
    local bundle, asset = ResPath.GetPetModel(self.cur_pet_res_id)
    main_part:ChangeModel(bundle, asset, function()
        self:PlayPetDrawObjAni()
    end)
end

function LoginCreateRoleDIYView:PlayPetDrawObjAni()
    local draw_obj = self:GetDIYPetDrawObj()
    local main_part = draw_obj:GetPart(SceneObjPart.Main)
    main_part:CrossFade(SceneObjAnimator.Move, nil, nil, true)
end
-- 宠物模型 end]]


----[[ 摄像机
function LoginCreateRoleDIYView:KillCameraMoveTween()
    if self.camera_move_tween then
        self.camera_move_tween:Kill()
        self.camera_move_tween = nil
    end
end

function LoginCreateRoleDIYView:ChangeCameraDistance(dis_type)
    if self.cur_camera_distance_type == dis_type then
        return
    end

    local sex = self.data and self.data.sex or 0
    local param = (camera_distance_param[dis_type] or {})[sex]
    if not param then
        return
    end

    if IsNil(self.scene_camera) then
		return
	end

    if not IsNil(self.role_draw_obj_transform) then
        self.role_draw_obj_transform.localPosition = param.role_pos
        self.role_draw_obj_transform.localRotation = Quaternion.identity
	end

    if self.node_list and self.node_list["event_trigger_listener"] then
        self.node_list["event_trigger_listener"]:SetActive(false)
    end
    self.cur_camera_distance_type = dis_type

    self:KillCameraMoveTween()
    local duration = 0.5
    self.camera_move_tween = DG.Tweening.DOTween.Sequence()
    self.camera_move_tween:Append(self.scene_camera.transform:DOLocalMove(param.position, duration):SetEase(DG.Tweening.Ease.OutQuad))
    self.camera_move_tween:Join(self.scene_camera.transform:DOLocalRotate(param.rotation, duration, DG.Tweening.RotateMode.Fast))
    self.camera_move_tween:Join(self.scene_camera:DOFieldOfView(param.fieldOfView, duration):SetEase(DG.Tweening.Ease.OutQuad))
    self.camera_move_tween:Join(self.scene_bg:DOLocalMove(param.bg_position, duration):SetEase(DG.Tweening.Ease.OutQuad))
    self.camera_move_tween:Join(self.scene_bg:DOScale(param.bg_scale, duration):SetEase(DG.Tweening.Ease.OutQuad))
    self.camera_move_tween:OnComplete(function ()
		if self.node_list and self.node_list["event_trigger_listener"] then
            self.node_list["event_trigger_listener"]:SetActive(self.cur_show_type == show_type.DIY)
        end
	end)
end
--摄像机 end]]

function LoginCreateRoleDIYView:OnClickShowSuit()
    local data = self.suit_show_list[1]
    if not data then
        return
    end

    if self.cur_suit_seq == data.seq then
        return
    end

    self.node_list["create_role_diy_left"]:SetActive(false)
    self.cur_suit_seq = data.seq

    self.cur_body_res_id = data.body_id
    self.cur_hair_res_id = data.hair_id
    self.cur_face_res_id = data.face_id
    self.cur_weapon_res_id = data.weapon_id

    if data.wing_id == 0 then
        self:RemoveRoleWing()
    else
        self.cur_wing_res_id = data.wing_id
    end

    if data.fabao_id == 0 then
        self:RemoveRoleBaoJu()
    else
        self.cur_fabao_res_id = data.fabao_id
    end

    if data.halo_id == 0 then
        self:RemoveRoleHalo()
    else
        self.cur_halo_res_id = data.halo_id
    end

    self.cur_show_type = show_type.Suit
    self:OnShowTypeChange(data.rolepos_y)
    self:ShowRoleDrawObjMain(camera_distance_type.Suit)

    local mount_action = data.mount_action == 1 and SceneObjAnimator.Move or SceneObjAnimator.Idle
    -- 坐骑
    if data.mount_id == 0 then
        self:DestroyDIYMountDrawObj()
    else
        local is_diff = self.cur_mount_res_id ~= data.mount_id
        self.cur_mount_res_id = data.mount_id
        local draw_obj = self:GetDIYMountDrawObj()
        draw_obj:SetRootActive(true)
        if is_diff then
            self:ShowMountDrawObj(mount_action)
            local pos_str = data.mount_pos
            if pos_str and pos_str ~= "" then
                local pos = Split(pos_str, "|")
                draw_obj.root_transform.localPosition = Vector3(pos[1] or 0, pos[2] or 0, pos[3] or 0)
            end
    
            local rotate_str = data.mount_rot
            if rotate_str and rotate_str ~= "" then
                local rot = Split(rotate_str, "|")
                draw_obj.root_transform.localRotation = Quaternion.Euler(rot[1] or 0, rot[2] or 0, rot[3] or 0)
            end
    
            local scale = data.mount_scale
            if scale and scale ~= "" then
                draw_obj:SetScale(scale, scale, scale)
            end
        else
            self:PlayMountDrawObjAni(mount_action)
        end
    end

    -- 宠物
    if data.pet_id == 0 then
        self:DestroyDIYPetDrawObj()
    else
        local is_diff = self.cur_pet_res_id ~= data.pet_id
        self.cur_pet_res_id = data.pet_id
        local draw_obj = self:GetDIYPetDrawObj()
        draw_obj:SetRootActive(true)
        if is_diff then
            self:ShowPetDrawObj()
            local pos_str = data.pet_pos
            if pos_str and pos_str ~= "" then
                local pos = Split(pos_str, "|")
                draw_obj.root_transform.localPosition = Vector3(pos[1] or 0, pos[2] or 0, pos[3] or 0)
            end
    
            local rotate_str = data.pet_rot
            if rotate_str and rotate_str ~= "" then
                local rot = Split(rotate_str, "|")
                draw_obj.root_transform.localRotation = Quaternion.Euler(rot[1] or 0, rot[2] or 0, rot[3] or 0)
            end
    
            local scale = data.pet_scale
            if scale and scale ~= "" then
                draw_obj:SetScale(scale, scale, scale)
            end
        else
            self:PlayPetDrawObjAni()
        end
    end
end

function LoginCreateRoleDIYView:OnShowTypeChange(rolepos_rot_y)
    local str_seq = 2
    if self.cur_show_type ~= show_type.Suit then
        if nil ~= self.mount_draw_obj then
            self.mount_draw_obj:SetRootActive(false)
        end
    
        if nil ~= self.pet_draw_obj then
            self.pet_draw_obj:SetRootActive(false)
        end

        str_seq = 1
    end

    local sex_rot = 190
    if type(rolepos_rot_y) == "number" then
        sex_rot = rolepos_rot_y
    else
        if self.data then
            local param = (special_param[self.data.sex] or {})[self.data.prof]
            if param and param.rot_y ~= nil then
                sex_rot = param.rot_y
            end
        end
    end
     
    self.draw_obj_parent_transform.localRotation = Quaternion.Euler(0, sex_rot, 0)
    -- self.node_list["confirm_btn_text"].text.text = Language.Login.CreateRoleBtnStr[str_seq]
end





function LoginCreateRoleDIYView:OnDrawObjDrag(data)
	if not IsNil(self.role_draw_obj_transform) then
		local y = -data.delta.x * 0.25
		self.role_draw_obj_transform:Rotate(0, y, 0)
	end
end


-- 职业描述
function LoginCreateRoleDIYView:ChangeProfDesc()
    local sex = self.data.sex
    local prof = self.data.prof

	local img_asset, img_name = ResPath.GetProfRawImagesPNG(sex, prof)
	self.node_list["prof_name"].raw_image:LoadSprite(img_asset, img_name, function()
		self.node_list["prof_name"].raw_image:SetNativeSize()
	end)

    local bundle, asset = RoleWGData.Instance:GetJobProfEffect(sex, prof)
    if bundle and asset then
        self.node_list["prof_name"]:ChangeAsset(bundle, asset)
    end
	-- img_asset, img_name = ResPath.GetRawImagesPNG(string.format("a2_cjjs_%s_%s", prof, sex))
	-- self.node_list["desc_name"].raw_image:LoadSprite(img_asset, img_name, function()
	-- 	self.node_list["desc_name"].raw_image:SetNativeSize()
	-- end)

    self.node_list.prof_title_text.text.text = RoleWGData.Instance:GetJobName1(sex, prof)
	local desc = RoleWGData.Instance:GetJobProfDesc(sex, prof)
    self.node_list.desc_name_1.text.text = desc and desc[1] or ""
	self.node_list.desc_name_2.text.text = desc and desc[2] or ""
end




---------------------------------- 名字 -------------------------------------------------
function LoginCreateRoleDIYView:InitRanDomName()
    self.re_count = 0
	self.first_list = {}
	self.last_list = {}

	local name_cfg = ConfigManager.Instance:GetAutoConfig("randname_auto").random_name[1]
	if not name_cfg then
        return
    end

	local the_list_1 = {}
	local the_list_2 = {}
	the_list_1[GameEnum.FEMALE] = name_cfg.common_first
	the_list_2[GameEnum.FEMALE] = name_cfg.female_last
	the_list_1[GameEnum.MALE] = name_cfg.common_first
	the_list_2[GameEnum.MALE] = name_cfg.male_last

	self.first_list = __TableCopy(the_list_1)
	self.last_list = __TableCopy(the_list_2)
end

function LoginCreateRoleDIYView:OnCrapsClick()
	self:RandomName()
end

function LoginCreateRoleDIYView:RandomName()
    local sex = self.data.sex
	local first_list = self.first_list[sex]
	local last_list = self.last_list[sex]

	if nil == first_list or nil == last_list then
		return
	end

	local first_index = math.random(1, #first_list)
	local last_index = math.random(1, #last_list)
	local name = first_list[first_index] .. last_list[last_index]
	local isill = ChatFilter.Instance:IsIllegal(name, true)
	-- 存在敏感字
	if isill and self.re_count <= 7 then
		self.re_count = self.re_count + 1
		table.remove(first_list, first_index)
		table.remove(last_list, last_index)
		self:RandomName()
	else
		self.input_name.input_field.text = name
		self.re_count = 0
	end
end

-- 创建角色按钮
function LoginCreateRoleDIYView:OnClickCreateConfirm()
    local now_time = Status.NowTime
    if self.cur_show_type == show_type.Suit then
        self:ToShowRoleDiy()
        self.next_click_time = now_time + 1
        return
    end

    if self.next_click_time and now_time < self.next_click_time then
        return
    end

	local role_name = self.input_name.input_field.text
	if role_name == "" then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent)
	end

	if ChatFilter.Instance:IsIllegal(role_name, true) or ChatFilter.IsEmoji(role_name) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.IllegalContent)
		return
	end

	local name_length_ch = CheckCharactersHaveCh(role_name)
	local name_length_ev = CheckCharactersHaveBigEV(role_name)
    --一个汉字长度是3,但是策划要把它当成2来处理
    --一个大写英文长度是1,但是策划要把它当成2来处理
	if string.len(role_name) + name_length_ev - name_length_ch > 12 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent3)
		return
	end

	local dur_time = 0
	if self.enter_create_role_time then
		dur_time = os.time() - self.enter_create_role_time
	end

    local sex = self.data.sex
    local prof = self.data.prof
    local country = 1

    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.clickLoginEnterGame)
	LoginWGCtrl.SendCreateRole(role_name, prof, sex, country, self.cur_body_res_id, self.cur_face_res_id, self.cur_hair_res_id)
end

-- 名字输入
function LoginCreateRoleDIYView:OnInputNameChange()
	local role_name = self.input_name.input_field.text
	if role_name == "" then
		return
	end
	
	if ChatFilter.IsEmoji(role_name) then
		self.input_name.input_field.text = ""
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.IllegalContent)
	end
end













CreatePartDIYRender = CreatePartDIYRender or BaseClass(BaseRender)
function CreatePartDIYRender:LoadCallBack()
	if not self.pos_tween then
		self.pos_tween = self.view:GetComponent(typeof(UGUITweenPosition))
	end
end

function CreatePartDIYRender:__delete()
	self.pos_tween = nil
end

function CreatePartDIYRender:SetPartType(type)
    self.part_type = type
end

function CreatePartDIYRender:GetPartType()
    return self.part_type or 0
end


function CreatePartDIYRender:OnFlush()
	if not self.data then
        self.view:CustomSetActive(false)
		return
	end

    local bundle, asset = ResPath.GetCreateRoleIcon(self.data.icon)
    self.node_list.icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.icon.image:SetNativeSize()
    end)

    self.view:CustomSetActive(true)
end

function CreatePartDIYRender:OnSelectChange(res_id)
    local is_select = false
    if not self.data then
        is_select = false
    else
        is_select = self.data.resouce_id == res_id
	end

    -- local node = self.node_list.root
    -- local pos_y = is_select and 30 or 0
    -- node.rect.anchoredPosition = Vector2(node.rect.anchoredPosition.x, pos_y)
	self.node_list.select:SetActive(is_select)

    if self.pos_tween then
		if is_select then
			self.pos_tween:PlayForward()
		else
			self.pos_tween:PlayReverse()
		end
	end
end