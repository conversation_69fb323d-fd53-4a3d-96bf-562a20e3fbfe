local PAGE_COUNT = 6
ProfessWallView = ProfessWallView or BaseClass(BaseRender)
PROFESS_OPERATOR = {
		[TabIndex.profess_wall_all] = 2,
		[TabIndex.profess_wall_to] = 1,
		[TabIndex.profess_wall_from] = 0,
	}
function ProfessWallView:__init()
	self.data_list = {}
	self.profess_cell_list = {}
	self.tab_index = nil
	local list_delegate = self.node_list["ListView"].list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshCell, self)


end

function ProfessWallView:ReleaseCallBack()
	self.tab_index = nil
	self.data_list = nil

	for k,v in pairs(self.profess_cell_list) do
		v:DeleteMe()
	end
	self.profess_cell_list = {}
	self.need_change_page = false
end

function ProfessWallView:ShowIndexCallBack(tab_index)
	self.tab_index = tab_index
--	print_error(self.tab_index)
	self.need_change_page = true
	-- self:SendReqInfo()
end

function ProfessWallView:SendReqInfo()
	local index = self.tab_index / 10
	if self.tab_index == TabIndex.profess_wall_all then
		ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_GLOBAL_ALL)
	elseif self.tab_index == TabIndex.profess_wall_to then
		ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_PASSIVE)
	elseif self.tab_index == TabIndex.profess_wall_from then
		ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_ACTIVE)
	end
	--local record_count =  ProfessWallWGData.Instance:GetRecordNumber(index) or 0
	--MarryWGCtrl.Instance:SendProfessWallReq(PROFESS_WALL_REQ_TYPE.PROFESS_WALL_REQ_INFO, PROFESS_OPERATOR[self.tab_index], 0)
	--self:Flush()
end

--表白框信息
function ProfessWallView:GetNumberOfCells()
	if self.data_list == nil then
		return 0
    end
	return math.ceil(#self.data_list / PAGE_COUNT)
end

function ProfessWallView:RefreshCell(cell, cell_index)
	local profess_cell = self.profess_cell_list[cell]
	if profess_cell == nil then
		profess_cell = ProfessWallInfoGroupCell.New(cell.gameObject)
		self.profess_cell_list[cell] = profess_cell
	end
	local data_list = {}
	for i = 1, PAGE_COUNT do
		if self.data_list[cell_index * PAGE_COUNT + i] then
			table.insert(data_list, self.data_list[cell_index * PAGE_COUNT + i])
		else
			break
		end
	end
	profess_cell:SetData(data_list)
end


function ProfessWallView:PageChangeEvent()
	if true then
		print_error("TODO")
		return
	end
	self:FlushPageDes()
end

function ProfessWallView:FlushPageDes()
	local page = self.node_list["ListView"].list_page_scroll:GetNowPage() + 1
	self.max_page_num = math.ceil(#self.data_list / PAGE_COUNT)
	local str = ""
	if self.max_page_num ~= 0 then
		str = page.." / "..self.max_page_num
	end
	self.page_num:SetValue(str)
end

--界面刷新
function ProfessWallView:OnFlush()
	self.data_list = {}
	local _,temp_data = {},{}
	if self.tab_index == TabIndex.profess_wall_all then
    	_,temp_data = ActivePerfertQingrenWGData.Instance:GetAllProfessInfo()--ProfessWallWGData.Instance:GetGobalProfessInfoList()
	elseif self.tab_index == TabIndex.profess_wall_to then
    	_,temp_data =  ActivePerfertQingrenWGData.Instance:GetPersonalInfo()--ProfessWallWGData.Instance:GetOtherProfessInfo()
	elseif self.tab_index == TabIndex.profess_wall_from then
    	_,temp_data = ActivePerfertQingrenWGData.Instance:GetPersonalInfo()--ProfessWallWGData.Instance:GetMineProfessInfo()
	end
	if #temp_data > 60 then
		for i=1,60 do
			table.insert(self.data_list,temp_data[i])
		end
	else
		self.data_list = temp_data
    end
    self.node_list.zanwu:SetActive(#self.data_list == 0)

	local page = self:GetNumberOfCells()
	if page > 0 then
		self.node_list["ListView"]:SetActive(true)
		self.node_list["ListView"].list_page_scroll:SetPageCount(self:GetNumberOfCells())
    	self.node_list["ListView"].scroller:RefreshAndReloadActiveCellViews(true)
    	if self.need_change_page then
    		for i = 1, 10 do
    			self.node_list["pagetoggle" .. i].toggle.isOn = i == 1
    		end
		end
		self.need_change_page = false
    else
		self.node_list["ListView"]:SetActive(false)
    end

    self:UpdatePageCount()
    -- self:FlushPageDes()
end

--更新页数
function ProfessWallView:UpdatePageCount()
    local count = self:GetNumberOfCells()
	for i = 1, 10 do
		self.node_list["pagetoggle" .. i]:SetActive( i <= count)
	end
end


--点击方法
function ProfessWallView:PageUp()
	local now_page = self.node_list["ListView"].list_page_scroll:GetNowPage() + 1
	if now_page <= 1 then
		return
	end

	now_page = now_page - 1
	self:ProfessJumpPage(now_page)
end

function ProfessWallView:PageDown()
	local now_page = self.node_list["ListView"].list_page_scroll:GetNowPage() + 1
	if now_page >= self.max_page_num then
		return
	end
	now_page = now_page + 1
	self:ProfessJumpPage(now_page)
end

function ProfessWallView:ProfessJumpPage(now_page)
	if not self.node_list["ListView"].scroller.isActiveAndEnabled then
		return
	end
	self.node_list["ListView"].list_page_scroll:JumpToPage(now_page - 1 )
end
------------------------------ProfessTabItemCell-----------------------------------
ProfessTabItemCell = ProfessTabItemCell or BaseClass(ItemCell)
function ProfessTabItemCell:__init()
	self.tab_name = self:FindVariable("TabName")
	self.is_show_hl = self:FindVariable("IsShowHL")
	self:ListenEvent("ClickTab", BindTool.Bind(self.OnClick, self))
end

function ProfessTabItemCell:__delete()
end

function ProfessTabItemCell:SetIndex(index)
	self.index = index
end

function ProfessTabItemCell:SetData(data)
	if data == nil then
		return
	end
	self.data = data
	self.tab_name:SetValue(Language.PrefessWall.ProfessTypeName[data.tab_type] or "")
	self.is_show_hl:SetValue(false)
end

function ProfessTabItemCell:SetTabHL(index)
	if index == nil then
		return
	end
	self.is_show_hl:SetValue(self.index == index)
end

------------------------------ProfessWallInfoGroupCell------------------------------
ProfessWallInfoGroupCell = ProfessWallInfoGroupCell  or BaseClass(BaseRender)

function ProfessWallInfoGroupCell:__init(instance)
	self.cell_list = {}
	self.data = {}
	for i = 1, PAGE_COUNT do
		self.cell_list[i] = ProfessWallInfoSingelCell.New(self.node_list["point_" .. i])
	end

	self.node_list["point_2"].transform.localScale = Vector3(0.9,0.9,0.9)
	self.node_list["point_2"].rect.localRotation = Quaternion.Euler(0, 0, -12)
	self.node_list["point_4"].rect.localRotation = Quaternion.Euler(0, 0, -12)
	self.node_list["point_5"].transform.localScale = Vector3(0.9,0.9,0.9)
	self.node_list["point_6"].rect.localRotation = Quaternion.Euler(0, 0, -12)
end

function ProfessWallInfoGroupCell:__delete()
	for k,v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
end

function ProfessWallInfoGroupCell:SetData(data)
	self.data = data
	if #self.cell_list < PAGE_COUNT then return end
	for k,v in pairs(self.cell_list) do
		v:SetData(data[k])
		v:SetActive(data[k] ~= nil)
	end
end

------------------------------ProfessWallInfoGroupCell------------------------------
ProfessWallInfoSingelCell = ProfessWallInfoSingelCell  or BaseClass(BaseRender)
function ProfessWallInfoSingelCell:__init(instance)
	if nil == self.root_node then
		local bundle, asset = "uis/view/marry_ui_prefab", "profess_cell"

        local async_loader = AllocAsyncLoader(self, "profess_cell")
		async_loader:SetParent(instance.transform)
		async_loader:Load(bundle, asset, function(obj)
        	if not obj then
        		print_error("can not find the asset!!")
        		return
        	end
			self.name_table = obj:GetComponent(typeof(UINameTable))			-- 名字绑定
			self.node_list = U3DNodeList(self.name_table, self)
			self.obj = obj
			self.load_finish = true
			if self.need_flush then
				self.need_flush = false
				self:SetData(self.data)
			end
			XUI.AddClickEventListener(self.node_list["profess_icon"], BindTool.Bind(self.OnClick, self))
        end)
	end


	-- self.charm_value = self.node_list["CharmValue"]
	-- self.exp_value = self.node_list["ExpValue"]
	-- self.topBg = self.node_list["TopBg"]
	-- self.show_delete_btn = self.node_list["ShowDelete"]
end

function ProfessWallInfoSingelCell:__delete()
	self.load_finish = false
end


function ProfessWallInfoSingelCell:SetData(data)
	self.data = data

	if not self.load_finish then
		self.need_flush = true
		return
	end
	if self.data == nil then
		return
    end

	local be_name = data.role_name_to or data.be_professor
	if nil == data.profess_type or data.profess_type == 0 then
		self.node_list["FromeText"].text.text = data.professor or data.role_id_from --表白者
	    self.node_list["ToText"].text.text = Language.Marry.PW_To .. data.be_professor --被.表白者
	else
		self.node_list["FromeText"].text.text = data.be_professor or data.role_id_from --表白者
	    self.node_list["ToText"].text.text = Language.Marry.PW_To .. data.professor --被.表白者
	end
	self.node_list["ContentText"].text.text = data.content  --表白内容
    


	-- self.charm_value:SetValue(data.self_charm)-- 魅力值
	-- self.exp_value:SetValue(data.exp) 		-- 表白经验

	if data.profess_type then
		-- self.show_delete_btn:SetValue(true)
		-- print_error("删除这条表白按钮", true)
	else
		-- self.show_delete_btn:SetValue(false)
		-- print_error("删除这条表白按钮", false)
	end
	self:UpdateIcon()
	--时间
	local time_list = os.date("%Y-%m-%d", data.profess_time)
	self.node_list["Time"].text.text = time_list
end

function ProfessWallInfoSingelCell:UpdateIcon()
	-- local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.gift_id)
	-- if nil == item_cfg then
	-- 	print_error("error ほ　to find the cfg , item_id = ", self.data.gift_id)
	-- 	return
	-- end
	local bundle, asset = ResPath.GetItem(27643 + self.data.gift_type)
	self.node_list["profess_icon"].image:LoadSprite(bundle, asset)
end

function ProfessWallInfoSingelCell:OnClick()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.gift_id)
	if nil == item_cfg then return end
	TipWGCtrl.Instance:OpenItem({item_id = self.data.gift_id}, ItemTip.FROM_NORMAL, nil)
end