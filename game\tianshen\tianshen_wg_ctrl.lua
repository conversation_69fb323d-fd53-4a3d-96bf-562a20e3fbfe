require("game/tianshen/tianshen_wg_data")
require("game/tianshen/tianshen_view")
require("game/tianshen/tianshen_activation_view")
require("game/tianshen/tianshen_battle_view")
require("game/tianshen/tianshen_select_view")
require("game/tianshen/tianshen_shenshi_view")
require("game/tianshen/tianshen_shentong_view")
require("game/tianshen/tianshen_shenqi_view")
require("game/tianshen/tianshen_fight_alert")
require("game/tianshen/tianshen_close_up_view")
require("game/tianshen/tianshen_shenshi_compose_tip")
require("game/tianshen/tianshen_shenshi_skill_tip")
require("game/tianshen/tianshen_shenshi_upgrade_tip")
require("game/tianshen/tianshen_shenshi_succinct_tip")
require("game/tianshen/tianshen_bagua_view")
require("game/tianshen/tianshen_bagua_sx_view")
require("game/tianshen/tianshen_bagua_wg_data")
require("game/tianshen/tianshen_bagua_equip_tip")
require("game/tianshen/tianshen_bagua_shop_view")
require("game/tianshen/tianshen_bagua_compose_view")
require("game/tianshen/wu_xing_tip")
require("game/tianshen/tianshen_special_table_bar")
-- require("game/tianshen/bagua_shop_setting_view")
-- require("game/tianshen/tianshen_info_view")
-- require("game/tianshen/tianshen_heji_view")

--天神宝匣
require("game/tianshen/tianshen_baoxia/tianshen_baoxia_view")
require("game/tianshen/tianshen_baoxia/tianshen_baoxia_tips_view")
require("game/tianshen/tianshen_baoxia/tianshen_baoxia_box_view")
require("game/tianshen/tianshen_baoxia/tianshen_baoxia_reward_panel")
-- require("game/tianshen/tianshen_baoxia/tianshen_baoxia_levelup_view")

-- require("game/tianshen/tianshen_jiban_tip")
-- require("game/tianshen/tianshen_jiban_view")

--天神组合技能
require("game/tianshen/tianshen_guanghuan_tip_view")
require("game/tianshen/tianshen_zhenfa_view")

--天神变身飘字
require("game/tianshen_bianshen_float/tianshen_bianshen_float_view")

--天神神灵殿
require("game/tianshen/tianshen_shenlingdian/tianshen_shenlingdian_view")
require("game/tianshen/tianshen_shenlingdian/tianshen_shenlingdian_wg_data")
--天神神灵殿选择上阵
require("game/tianshen/tianshen_shenlingdian/tianshen_shenlingdian_select_view")
require("game/tianshen/tianshen_shenlingdian/tianshen_shendian_star_attr_view")

-- 坐骑灵宠
TianShenWGCtrl = TianShenWGCtrl or BaseClass(BaseWGCtrl)

TianShenWGCtrl.Opera_Type = {
	Opera_Type1 = 1, -- 	1: // 升阶 param1:index
	Opera_Type2 = 2, -- 2: // 使用属性丹 param1:丹槽索引 param2:使用数量
	Opera_Type3 = 3, -- 3: // 出战/卸下 param1:形象索引 param2:位置
	-- Opera_Type4 = 4,-- 4: // 分解材料 param1:角色背包索引 param2:1：消耗元宝
	Opera_Type5 = 5, -- 5: // 升星 param1:形象索引
	Opera_Type6 = 6, -- 6: // 激活 param1:形象索引
	Opera_Type7 = 7, -- 7: // 升级丹升级
	Opera_Type8 = 8, -- 8: // 装备强化 param1:形象索引, param2装备部位 0~3
	Opera_Type9 = 9, -- 9: // 装备进阶 param1:形象索引, param2装备部位 0~3
	Opera_Type10 = 10, --10: // param1:形象索引 param2:装备1索引, param3:装备1是否穿戴戴 param4:装备2索引 param5:装备2是否穿戴戴
	Opera_Type11 = 11, -- 11: // 装备拆解 param1:背包物品索引
	-- Opera_Type12 = 12,-- 12: // 装备分解 param1:背包物品索引 param2:1消耗元宝
	Opera_Type13 = 13, -- 13: // 穿戴装备 param1:幻形索引 param2:幻形装备位置0~3 param3:背包位置
	Opera_Type14 = 14, -- 14: // 卸下装备 param1:幻形索引 param2:部位 0~3
	Opera_Type15 = 15, -- 15: // 变身 param1:幻形索引
	Opera_Type16 = 16, -- 16: // 激活套装 param1;幻形索引 param2:套装编码
	Opera_Type17 = 17, -- 17: // 被动技能升级 param1:天神索引(-1表示升级初级面板被动技能) param2:技能索引 0~4
	Opera_Type18 = 18, -- 18: // 主动技能升级 param1:天神索引 param2:技能id
	Opera_Type19 = 19, -- 19: // 激活天神出战位 param1:激活出战位的索引 从零开始
	Opera_Type20 = 20, -- 20: // 出战的天神互换位置 param1：param2 = 天神index1:天神index2
	Opera_Type21 = 21, -- 21: // 养成度信息
	Opera_Type22 = 22, -- 22	 //激活合击技能 param1:索引
	Opera_Type23 = 23, -- 23  //上阵合击技能 param1:索引	param2:seq（参数2先默认传0，主要是为了后面拓展）
	Opera_Type24 = 24, -- 24  //双生神灵 - 激活 p1: index
	Opera_Type25 = 25, -- 25  //双生神灵 - 升级 p1: index
	Opera_Type26 = 26, -- 26  //双生神灵 - 更换幻化 p1->形象索引 p2->星级
	Opera_Type27 = 27, -- 27// 神饰洗练		param1:index	param2:hole param3:is_base
}

TianShenWGCtrl.ShenQi_Opera_Type = {
	ShenQi_Opera_Type0 = 0, -- 0: // 请求天神神器的所有信息
	ShenQi_Opera_Type1 = 1, -- 1: // 激活神器 param1 天神神器索引
	ShenQi_Opera_Type2 = 2, -- 2: // 强化神器 param1 天神神器索引
	ShenQi_Opera_Type3 = 3, -- 3: // 附灵神器 param1 天神神器索引
	ShenQi_Opera_Type4 = 4, -- 4: // 幻化激活/升星 param1 幻化形象id param2  1激活 2升星
	ShenQi_Opera_Type5 = 5, -- 5: // 使用幻化形象 param1 天神神器索引 param2 外观id
	ShenQi_Opera_Type6 = 6, -- 6: // 神器升星 param1 天神神器索引
}

function TianShenWGCtrl:__init()
	if TianShenWGCtrl.Instance ~= nil then
		ErrorLog("[TianShenWGCtrl] attempt to create singleton twice!")
		return
	end
	TianShenWGCtrl.Instance = self

	self.tianshen_data = TianShenWGData.New()
	self.tianshen_view = TianShenView.New(GuideModuleName.TianShenView)
	self.tianshen_select_view = TianShenSelectView.New(GuideModuleName.TianShenSelectView)
	self.tianshen_fight_alert = TianShenFightAlert.New(GuideModuleName.TianShenFightAlert)
	self.tianshen_shentong_view = TianShenShenTongView.New(GuideModuleName.TianShenShenTongView)
	--self.shenshi_qianghua_view = ShenShiQiangHuaView.New(GuideModuleName.ShenShiQiangHuaView)

	self.one_bag_func = BindTool.Bind(self.OneBagToFlushByTime, self)
	self.tianshen_bagua_data = TianShenBaGuaWGData.New()
	self.tianshen_close_up_view = TianShenCloseUpView.New()
	self.shishi_compose_tip = TianShenShenShiComposeTips.New()
	self.shenshi_skill_tip = TianShenShenShiSkillTips.New()
	self.shenshi_upgrade_tip = TianShenShenUpGradeTips.New()
	self.bagua_equip_tip = TianShenBaGuaEquipTip.New()
	self.bagua_shop_view = TianShenBaGuaShopView.New(GuideModuleName.TianShenBaGuaShop)
	self.bagua_compose_tip = TianShenBaGuaComposeTips.New()
	self.bagua_sx_view = TianShenBaGuaSXTips.New()
	-- self.jiban_tip = TianShenJiBanTip.New()
	-- self.jiban_view = TianShenJiBanView.New(GuideModuleName.TianShenJiBanView)
	-- self.bagua_shop_setting_view = BaGuaShopSettingView.New()
	--天神宝匣
	-- self.tianshen_baoxia_view = TianShenBaoXiaView.New()
	self.tianshen_baoxia_view = TianShenBaoXiaView.New(GuideModuleName.TianShenBaoXia)
	self.tianshen_baoxia_tips_view = TianShenBaoXiaTipsView.New()
	self.tianshen_baoxia_reward_panel = TianShenBaoXiaRewardPanel.New()
	-- self.tianshen_baoxia_levelup_view = TianShenBaoXiaLevelUpView.New()

	self.wuxing_tips = WuXingTip.New(GuideModuleName.WuXingTip)

	self.tianshen_guanghuan_tip_view = TianShenGuangHuanTipView.New()
	self.tianshen_zhenfa_view = TianShenZhenFaView.New()

	--天神变身飘字
	self.tianshen_bianshen_float_view = TianShenBianShenFloat.New()
	self.tianshen_shenshi_succinct_tip = TianshenShenshiSuccinctTip.New()

	--天神神灵殿
	-- self.tianshen_shenlingdian_wg_ctrl = TianShenShenLingDianCtrl.New()
	self.ts_shenling_wg_data = TSShenLingDianData.New()
	self.tianshen_shenlingdian_select_view = TSShenLingDianSelectView.New()
	self.tianshen_shendian_star_attr_view = TSShenLingDianStarAttrView.New()
	
	self:RegisterAllProtocols()
	self.is_first_rember = true
	self.money_info = {
		draw_key_count = 0, -- 神饰币
	}
end

function TianShenWGCtrl:__delete()
	self.tianshen_fight_alert:DeleteMe()
	--self.tianshen_shengqi_qianghua_view:DeleteMe()
	--self.shenshi_qianghua_view:DeleteMe()
	-- self.bagua_shop_setting_view:DeleteMe()
	self.tianshen_view:DeleteMe()
	self.tianshen_select_view:DeleteMe()
	self.tianshen_data:DeleteMe()
	self.tianshen_shentong_view:DeleteMe()
	self.tianshen_close_up_view:DeleteMe()
	self.shenshi_skill_tip:DeleteMe()
	self.shishi_compose_tip:DeleteMe()
	self.shenshi_upgrade_tip:DeleteMe()
	self.tianshen_bagua_data:DeleteMe()
	self.bagua_equip_tip:DeleteMe()
	self.bagua_shop_view:DeleteMe()
	self.bagua_compose_tip:DeleteMe()
	self.bagua_sx_view:DeleteMe()
	self.wuxing_tips:DeleteMe()
	self.ts_shenling_wg_data:DeleteMe()
	self.tianshen_shenlingdian_select_view:DeleteMe()
	self.tianshen_shendian_star_attr_view:DeleteMe()
	-- self.jiban_tip:DeleteMe()
	-- self.jiban_view:DeleteMe()

	--天神宝匣
	self.tianshen_baoxia_view:DeleteMe()
	self.tianshen_baoxia_tips_view:DeleteMe()
	self.tianshen_baoxia_reward_panel:DeleteMe()
	-- self.tianshen_baoxia_levelup_view:DeleteMe()

	if self.tianshen_zhenfa_view then
		self.tianshen_zhenfa_view:DeleteMe()
		self.tianshen_zhenfa_view = nil
	end

	if self.tianshen_guanghuan_tip_view then
		self.tianshen_guanghuan_tip_view:DeleteMe()
		self.tianshen_guanghuan_tip_view = nil
	end

	if self.confirm_decompose_alert then
		self.confirm_decompose_alert:DeleteMe()
		self.confirm_decompose_alert = nil
	end

	if self.confirm_buy_alert then
		self.confirm_buy_alert:DeleteMe()
		self.confirm_buy_alert = nil
	end

	if self.tianshen_bianshen_float_view then
		self.tianshen_bianshen_float_view:DeleteMe()
		self.tianshen_bianshen_float_view = nil
	end

	if self.tianshen_shenshi_succinct_tip then
		self.tianshen_shenshi_succinct_tip:DeleteMe()
		self.tianshen_shenshi_succinct_tip = nil
	end

	self.one_bag_func = nil
	self.is_first_rember = nil
	self.money_info = nil
	self:RemoveTimeQuest()
	TianShenWGCtrl.Instance = nil
end

function TianShenWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCTianshenInfo, "OnTianShenInfo")
	self:RegisterProtocol(SCTianshenOneInfo, "OnTianShenOneInfo")
	self:RegisterProtocol(SCTianshenOtherInfo, "OnTianShenOtherInfo")
	self:RegisterProtocol(SCTianshenBagInfo, "OnTianshenBagInfo")
	self:RegisterProtocol(SCTianshenOneBagInfo, "OnTianshenOneBagInfo")
	self:RegisterProtocol(SCTianshenShentongSkillInfo, "OnSCTianshenShentongSkillInfo")
	self:RegisterProtocol(SCTianshenSkillInfo, "OnSCTianshenSkillInfo")
	self:RegisterProtocol(SCALLTianShenShenqiInfo, "OnSCALLTianShenShenqiInfo")
	self:RegisterProtocol(SCUpdataTianShenShenqiInfo, "OnSCUpdataTianShenShenqiInfo")

	self:RegisterProtocol(CSTianshenShenShiReq)

	self:RegisterProtocol(CSTianshenBaguaReq)
	self:RegisterProtocol(CSTianshenBaguaDecompose)
	self:RegisterProtocol(SCTianshenBaguaInfo, "OnSCTianshenBaguaInfo")
	self:RegisterProtocol(SCTianshenBaguaBagInfo, "OnSCTianshenBaguaBagInfo")
	self:RegisterProtocol(SCTianShenBagChangeInfo, "OnSCTianShenBagChangeInfo")
	self:RegisterProtocol(SCTianshenBaguaPartChange, "OnSCTianshenBaguaPartChange")
	self:RegisterProtocol(SCTianshenBaguaOtherInfo, "OnSCTianshenBaguaOtherInfo")

	-- 天神宝匣(改成神格抽奖 协议定的新的)
	self:RegisterProtocol(CSTianShanBoxDrawOper)
	self:RegisterProtocol(SCTianShenBoxInfo, "OnSCTianShenBoxInfo")
	-- 神格抽奖
	self:RegisterProtocol(CSGodhoodDrawOperate)
	self:RegisterProtocol(SCGodhoodDrawItemInfo, "OnSCGodhoodDrawItemInfo")
	self:RegisterProtocol(SCGodhoodDrawDrawResult, "OnSCGodhoodDrawDrawResult")
	self:RegisterProtocol(SCGodhoodDrawRecordInfo, "OnSCGodhoodDrawRecordInfo")
	self:RegisterProtocol(SCGodhoodDrawRecordAdd, "OnSCGodhoodDrawRecordAdd")

	

	self:RegisterProtocol(SCTianShenSkillCall, "OnSCTianShenSkillCall")
	self:RegisterProtocol(SCAllTianShenCompleteRate, "OnSCAllTianShenCompleteRate")
	self:RegisterProtocol(SCTianShenBianShenDongMan, "OnSCTianShenBianShenDongMan")

	self:RegisterProtocol(SCTianShenUnionSkillInfo, "OnSCTianShenUnionSkillInfo")
	self:RegisterProtocol(CSPerformChangeTianShenSkill)
	self:RegisterProtocol(SCMultiWuXingType, "OnSCMultiWuXingType")

	-- 天神殿协议
	self:RegisterProtocol(CSTianshenHallOperate)
	self:RegisterProtocol(CSTianshenHallGoHall)
	self:RegisterProtocol(SCTianshenHallBaseInfo, "OnSCTianshenHallBaseInfo")
	self:RegisterProtocol(SCTianshenHallItemInfo, "OnSCTianshenHallItemInfo")
	self:RegisterProtocol(SCTianshenHallItemUpdate, "OnSCTianshenHallItemUpdate")
end

function TianShenWGCtrl:SendShenShiOpera(opera_type, param1, param2) --F2神饰操作
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianshenShenShiReq)
	protocol.opera = opera_type or 0
	protocol.param0 = param1 or 0
	protocol.param1 = param2 or -1
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:SendShenTongOperaReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianshenShentongOperaReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:SendTianShenOperaReq(opera_type, param1, param2, param3, param4, param5)
	-- print_error("操作", opera_type, param1, param2, param3, param4, param5)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianshenOperaReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol.param5 = param5 or 0
	protocol:EncodeAndSend()
end

-- yuanbao 1 使用元宝翻倍
-- item_list {type  1 材料分解 2 神饰分解, index 角色背包索引 或者 天神背包索引}
function TianShenWGCtrl:SendTianshenFenjie(yuanbao, item_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianshenFenjie)
	protocol.yuanbao = yuanbao
	protocol.item_list = item_list
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:SendTianshenComposeReq(item_id, star, bag_index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianshenComposeReq)
	protocol.item_id = item_id
	protocol.star = star
	protocol.bag_index = bag_index_list
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:SendTianShenShenqiOpReq(req_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenShenqiOpReq)
	protocol.req_type = req_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

-- items {item_id, item_num}
function TianShenWGCtrl:SendTianShenShenQiFenjie(items)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenShenQiFenjieReq)
	protocol.items = items
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:OnTianShenInfo(protocol)
	local old_fight_info = self.tianshen_data:GetFightTisnShen(true)
	local change_dic = {}
	local add_dic = {}
	local add_index = 1
	local cur_fight_dic = {}
	if old_fight_info ~= nil then
		for k, v in pairs(old_fight_info) do
			change_dic[v.index] = v
			-- local jiban_cfg = self.tianshen_data:GetTianShenCfg(v.jiban_index)
			-- if jiban_cfg ~= nil then
			-- 	change_dic[v.jiban_index] = jiban_cfg
			-- end
		end
	end

	-- body
	self.tianshen_data:TianShenInfo(protocol)
	local cur_fight_info = self.tianshen_data:GetFightTisnShen(true)
	if cur_fight_info ~= nil then
		for k, v in pairs(cur_fight_info) do
			if change_dic[v.index] == nil then
				add_dic[add_index] = v
				add_index = add_index + 1
			else
				change_dic[v.index] = nil
			end

			cur_fight_dic[v.index] = true
			cur_fight_dic[v.jiban_index] = true
		end
	end
	-- 预加载新的出战天神
	self:DynamicLoadAsset(add_dic, false, cur_fight_dic)
	-- 释放旧的出战天神
	self:DynamicLoadAsset(change_dic, true, cur_fight_dic)

	--更新委托任务外形数据
	AssignmentWGData.Instance:SetMyAssignFashionData(AssignmentWGData.FASHION_TYPE.TIANSHEN, protocol)

	RemindManager.Instance:Fire(RemindName.TianShen_Huanhua)
	RemindManager.Instance:Fire(RemindName.TianShen_Temple)
	--RemindManager.Instance:Fire(RemindName.TianShen_Battle)
	GlobalEventSystem:Fire(MainUIEventType.BIANSHEN_SKILL_CHANGE)
	--ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_battle)
end

function TianShenWGCtrl:OnTianShenOneInfo(protocol)
	local old_data = self.tianshen_data:GetShenShiEquipInfo(protocol.index)
	-- body
	self.tianshen_data:SetTianShenInfoOne(protocol)
	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end

	--更新委托任务外形数据
	AssignmentWGData.Instance:SetMyAssignFashionData(AssignmentWGData.FASHION_TYPE.TIANSHEN, protocol)

	ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_activation)
	ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_shenshi)
	--ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_battle)
	--ViewManager.Instance:FlushView(GuideModuleName.ShenShiQiangHuaView)
	--ViewManager.Instance:FlushView(GuideModuleName.ShenShiQiangHuaView, TabIndex.shenshi_qianghua, "single")

	RemindManager.Instance:Fire(RemindName.TianShen_ST)
	RemindManager.Instance:Fire(RemindName.TianShen_Huanhua)
	RemindManager.Instance:Fire(RemindName.TianShen_ShenShi)
	--RemindManager.Instance:Fire(RemindName.ShenShiQiangHua)
	RemindManager.Instance:Fire(RemindName.ShenShiTaoZhuang)
	--RemindManager.Instance:Fire(RemindName.TianShen_Battle)
	RemindManager.Instance:Fire(RemindName.TianShen_Temple)
	ViewManager.Instance:FlushView(GuideModuleName.MountLingChongDisplayBox)
	GlobalEventSystem:Fire(MainUIEventType.BIANSHEN_SKILL_CHANGE)

	if old_data == nil or old_data.zhan_index ~= protocol.upgrade_item_info.zhan_index then
		local new_data = self.tianshen_data:GetTianShenCfg(protocol.index)
		if new_data ~= nil then
			local cur_fight_dic = {}
			local cur_fight_info = self.tianshen_data:GetFightTisnShen(true)
			if cur_fight_info ~= nil then
				for k, v in pairs(cur_fight_info) do
					cur_fight_dic[v.index] = true
					cur_fight_dic[v.jiban_index] = true
				end
			end

			self:DynamicLoadAsset({ new_data }, protocol.upgrade_item_info.zhan_index == -1, cur_fight_dic)
		end

		OfflineRestWGCtrl.Instance:FlushLiLianView("flush_chuzhan")
	end
end

function TianShenWGCtrl:OnTianShenOtherInfo(protocol)
	local old_tianshen_info = self.tianshen_data:OldTianShenInfo()
	self.tianshen_data:TianShenOtherInfo(protocol)

	local tianshen_info = self.tianshen_data:TianShenInfo()

	if not IsEmptyTable(old_tianshen_info) and tianshen_info then
		local add_jinghua = tianshen_info.jinghua - old_tianshen_info.jinghua
		if 0 < add_jinghua then
			ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.HongMengJingHua, add_jinghua)
			local other = TianShenWGData.Instance.tianshen_cfg_auto.other[1]
			local item_cfg = ItemWGData.Instance:GetItemConfig(other.essence_item_id)
			if item_cfg then
				local str = string.format(Language.Bag.GetItemTxt,
					ToColorStr(item_cfg.name, GET_TIP_ITEM_COLOR[item_cfg.color]), add_jinghua)
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end
		end
	end

	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end

	--ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_heji)
	--ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_battle)

	ViewManager.Instance:FlushView(GuideModuleName.ShenShiQiangHuaView, TabIndex.shenshi_qianghua, "single")
	OfflineRestWGCtrl.Instance:FlushLiLianView("flush_chuzhan")

	local view = NewAppearanceWGCtrl.Instance:GetDisplayBox()
	if view and view:IsOpen() and view:GetData() and view:GetData().is_ts then
		view:Flush()
	end

	GlobalEventSystem:Fire(MainUIEventType.BIANSHEN_SKILL_CHANGE)

	RemindManager.Instance:Fire(RemindName.TianShen_ST)
	RemindManager.Instance:Fire(RemindName.TianShen_Huanhua)
	RemindManager.Instance:Fire(RemindName.TianShen_ShenShi)
	RemindManager.Instance:Fire(RemindName.ShenShiQiangHua)
	RemindManager.Instance:Fire(RemindName.ShenShiTaoZhuang)
	--RemindManager.Instance:Fire(RemindName.TianShen_Battle)
	--RemindManager.Instance:Fire(RemindName.TianShenHeJi)
end

function TianShenWGCtrl:FlushActiveEffect(protocol)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.TianShenView, TabIndex.tianshen_activation) then
		if protocol.result == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_activation, "succes",
				{ id = protocol.param1 })
		end
	end
end

-- 自动获取的天神展示
function TianShenWGCtrl:FlushTianshenDirectActive(protocol)
	if protocol.result == 1 then
		self.tianshen_data:ShowGetNewAppearance(protocol.param1)
	end
end

function TianShenWGCtrl:FlushShenQiActiveEffect(protocol)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.TianShenView, TabIndex.tianshen_shenQi) then
		if protocol.result == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_shenQi, "sq_succes",
				{ id = protocol.param1 })
		end
	end
end

function TianShenWGCtrl:FlushTaozhuangActiveEffect(protocol)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.ShenShiQiangHuaView, TabIndex.shenshi_taozhuang) then
		if protocol.result == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.ShenShiQiangHuaView, TabIndex.shenshi_taozhuang, "tz_succes",
				{ id = protocol.param1 })
		end
	end
end

function TianShenWGCtrl:FlushSkillActiveEffect(protocol)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.TianShenView, TabIndex.tianshen_activation) then
		if protocol.result == 1 then
			--self.tianshen_view:BeiSkillEffectActive(protocol.param2, protocol.param1)
			ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_activation, "skill_active",
				{ id = protocol.param1, num = protocol.param2 })
		end
	end
end

function TianShenWGCtrl:OnTianshenBagInfo(protocol)
	-- body
	self.tianshen_data:TianshenBagInfo(protocol)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.TianShenView, TianShenView.TabIndex.ShenShi) then
		self.tianshen_view:FlushBagView()
	end

	if ViewManager.Instance:IsOpen(GuideModuleName.ShenShiQiangHuaView) then
		ViewManager.Instance:FlushView(GuideModuleName.ShenShiQiangHuaView, TabIndex.shenshi_jicheng, "FlushBagView")
	end
end

function TianShenWGCtrl:OnTianshenOneBagInfo(protocol)
	self.tianshen_data:SetTianshenOneBagInfo(protocol)
	self.one_bag_info = protocol
	self:OneBagToFlush()

	local item_cfg = ItemWGData.Instance:GetItemConfig(protocol.bag_item.item_id)
	if item_cfg and protocol.reason_type ~= TIANSHEN_REASON_TYPE.EQUIP_TAKE_OFF then
		local str = string.format(Language.Bag.GetItemTxt, ToColorStr(item_cfg.name, GET_TIP_ITEM_COLOR[item_cfg.color]),
			1)
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	end
	GlobalEventSystem:Fire(MainUIEventType.TianShen_Item_Change, protocol)
end

function TianShenWGCtrl:RemoveTimeQuest()
	-- body
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end
end

-- 单个背包数据会返回多条，这里做下延时
function TianShenWGCtrl:OneBagToFlush()
	-- body
	self:RemoveTimeQuest()
	self.timer_quest = GlobalTimerQuest:AddDelayTimer(self.one_bag_func, 0.1)
end

function TianShenWGCtrl:OneBagToFlushByTime()
	-- body
	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end

	local value = nil
	if self.one_bag_info then
		value = { reason_type = self.one_bag_info.reason_type }
	end

	if ViewManager.Instance:IsOpen(GuideModuleName.ShenShiQiangHuaView) then
		ViewManager.Instance:FlushView(GuideModuleName.ShenShiQiangHuaView, TabIndex.shenshi_jicheng, "FlushBagView",
			value)
	end

	RemindManager.Instance:Fire(RemindName.TianShen_ShenShi)
	RemindManager.Instance:Fire(RemindName.ShenShiQiangHua)
	RemindManager.Instance:Fire(RemindName.ShenShiTaoZhuang)
end

function TianShenWGCtrl:OnSCTianshenShentongSkillInfo(protocol)
	self.tianshen_data:SetSTServerInfo(protocol)

	-- 刷新神通界面
	ViewManager.Instance:FlushView(GuideModuleName.TianShenShenTongView)
	RemindManager.Instance:Fire(RemindName.TianShen_ST)
end

function TianShenWGCtrl:OnSCTianshenSkillInfo(protocol)
	self.tianshen_data:TianShenSkill(protocol)
	local view = NewAppearanceWGCtrl.Instance:GetDisplayBox()
	if view and view:IsOpen() and view:GetData() and view:GetData().is_ts_image_main then
		view:Flush()
	end

	ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_activation, "flush_skill")
end

function TianShenWGCtrl:OnSCALLTianShenShenqiInfo(protocol)
	self.tianshen_data:TianShenShenQiInfo(protocol.tianshenshenqi_items)
end

function TianShenWGCtrl:OnSCUpdataTianShenShenqiInfo(protocol)
	self.tianshen_data:SetTianShenShenQiInfoOne(protocol.tianshenshenqi_item)

	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.TianShenView, TabIndex.tianshen_shenQi) then
		ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_shenQi, "Flushlist",
			{ id = protocol.tianshenshenqi_item })
	end

	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end
end

function TianShenWGCtrl:Open(index, value)
	ViewManager.Instance:Open(GuideModuleName.TianShenView, index, "all", value)
end

function TianShenWGCtrl:OpenShenShiQiangHuaView(tianshen_index, tab_index, key, values)
	--self.shenshi_qianghua_view:SetData(tianshen_index)
	--ViewManager.Instance:Open(GuideModuleName.ShenShiQiangHuaView, tab_index, key, values)
end

-- 神通升级结果返回
function TianShenWGCtrl:OnShenTongUpgradeResult(result)
	if result == 1 then
		ViewManager.Instance:FlushView(GuideModuleName.TianShenShenTongView, nil, "upgrade_result", { level = true })
	end
end

-- 神通激活结果返回
function TianShenWGCtrl:OnShenTongActiveResult(result)
	if result == 1 then
		ViewManager.Instance:FlushView(GuideModuleName.TianShenShenTongView, nil, "upgrade_result", { active = true })
	end
end

-- 打开天神特写面板
function TianShenWGCtrl:OpenTianShenCloseUpView(appearance_param, is_force)
	if not SettingWGData.Instance:GetSettingData(SETTING_TYPE.TIANSHEN_CLOSE_UP) then
		if is_force then
			if self.tianshen_close_up_view:IsOpen() then
				self.tianshen_close_up_view:Close()
			end
		end

		self.tianshen_close_up_view:SetData(appearance_param)
		self.tianshen_close_up_view:Open()
	end
end

function TianShenWGCtrl:OpenShenShiComposeTip(data)
	if self.shishi_compose_tip then
		self.shishi_compose_tip:SetComposeData(data)
		if self.shishi_compose_tip:IsOpen() then
			self.shishi_compose_tip:Flush()
		else
			self.shishi_compose_tip:Open()
		end
	end
end

function TianShenWGCtrl:OpenShenShiShiSkillTip(tianshen_index)
	if self.shenshi_skill_tip then
		self.shenshi_skill_tip:SetSkillData(tianshen_index)
		if self.shenshi_skill_tip:IsOpen() then
			self.shenshi_skill_tip:Flush()
		else
			self.shenshi_skill_tip:Open()
		end
	end
end

function TianShenWGCtrl:OpenShenShiUpGradeTip(tianshen_index)
	if self.shenshi_upgrade_tip then
		self.shenshi_upgrade_tip:SetTianShenData(tianshen_index)
		if self.shenshi_upgrade_tip:IsOpen() then
			self.shenshi_upgrade_tip:Flush()
		else
			self.shenshi_upgrade_tip:Open()
		end
	end
end

function TianShenWGCtrl:OnSCTianshenBaguaBagInfo(protocol)
	self.tianshen_bagua_data:OnSCTianshenBaguaBagInfo(protocol)
	self.tianshen_bagua_data:SetInfoChange(true)
	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.TianShen_BaGua)
end

function TianShenWGCtrl:DoLevelFlushBaGua()
	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.TianShen_BaGua)
end

function TianShenWGCtrl:OnSCTianshenBaguaInfo(protocol)
	self.tianshen_bagua_data:OnSCTianshenBaguaInfo(protocol)
	self.tianshen_bagua_data:SetInfoChange(true)
	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end

	if self.bagua_shop_view:IsOpen() then
		self.bagua_shop_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.TianShen_BaGua)
end

function TianShenWGCtrl:OnSCTianShenBagChangeInfo(protocol)
	self.tianshen_bagua_data:OnSCTianShenBagChangeInfo(protocol)
	self.tianshen_bagua_data:SetInfoChange(true)
	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end
	if self.bagua_compose_tip:IsOpen() then
		self.bagua_compose_tip:Flush()
	end
	RemindManager.Instance:Fire(RemindName.TianShen_BaGua)
end

function TianShenWGCtrl:OnSCTianshenBaguaPartChange(protocol)
	self.tianshen_bagua_data:OnSCTianshenBaguaPartChange(protocol)
	self.tianshen_bagua_data:SetInfoChange(true)
	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end

	if self.bagua_compose_tip:IsOpen() then
		self.bagua_compose_tip:Flush()
	end
	RemindManager.Instance:Fire(RemindName.TianShen_BaGua)
end

function TianShenWGCtrl:OnSCTianshenBaguaOtherInfo(protocol)
	self.tianshen_bagua_data:OnSCTianshenBaguaOtherInfo(protocol)
	self.tianshen_bagua_data:SetInfoChange(true)
	if self.bagua_shop_view:IsOpen() then
		self.bagua_shop_view:Flush()
	end

	self.tianshen_view:Flush(TabIndex.tianshen_bagua, "FlushShopRed")

	RemindManager.Instance:Fire(RemindName.TianShen_BaGua)
end

function TianShenWGCtrl:SendBaGuaOpera(opera_type, param1, param2) --F2八卦操作
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianshenBaguaReq)
	protocol.opera_type = opera_type or 0
	protocol.param_0 = param1 or 0
	protocol.param_1 = param2 or -1
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:SendBaGuaDeCompose(count, index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianshenBaguaDecompose)
	protocol.count = count
	protocol.index_list = index_list
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:GetBaGuaClickFunc(data, from_view)
	local click_func = {}
	if from_view == ItemTip.FROM_TIANSHEN_BAGUA_BAG and not IsEmptyTable(data) then
		local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(data.item_id)
		local red_info = TianShenBaGuaWGData.Instance:GetBaGuaItemRedInfo(data.item_id)
		click_func[1] = {}
		click_func[1].btn_text = Language.TianShen.BaGuaTipBtnText[1]
		click_func[1].btn_red = 0
		click_func[1].callback = function()
			if base_cfg.star >= 4 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.CanNotDeCompose)
			else
				local index_list = {}
				index_list[1] = data.index
				local decompose_cfg = TianShenBaGuaWGData.Instance:GetBaGuaDeCompseCfg(data.item_id)
				local str = string.format(Language.TianShen.BaGuaDeComposeTip, decompose_cfg.decompose_num)
				local ok_fun = function()
					self:SendBaGuaDeCompose(1, index_list)
				end
				if not self.confirm_decompose_alert then
					self.confirm_decompose_alert = Alert.New()
				end
				self.confirm_decompose_alert:SetCheckBoxText(Language.MountLingChongEquip.DontTip)
				self.confirm_decompose_alert:SetLableString(str)
				self.confirm_decompose_alert:SetOkFunc(ok_fun)
				self.confirm_decompose_alert:SetShowCheckBox(true, "bagua_compos")
				self.confirm_decompose_alert:Open()
			end
		end
		local compose_data = {}
		local target_item_id, compose_type, compose_way, count_2 = self.tianshen_bagua_data:GetTargetComposeItem(data
		.item_id)
		compose_data.cost_item = data.item_id
		compose_data.target_item_id = target_item_id
		compose_data.compose_type = compose_type
		compose_data.compose_way = compose_way
		compose_data.count_2 = count_2

		click_func[2] = {}
		click_func[2].btn_text = Language.TianShen.BaGuaTipBtnText[2]
		click_func[2].btn_red = 0

		if compose_type ~= 3 and compose_type ~= 4 then
			click_func[2].btn_red = 1
		end

		click_func[2].callback = function()
			if compose_type == 3 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.BaGuaNoComposeTarget)
			else
				self:BaGuaOpenComPose(compose_data)
			end
			-- elseif compose_type == 4 then
			-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.BaGuaComposeNotEnough)
			-- else

			-- 	self:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_UP_STAR,target_item_id,compose_type)
			-- end
		end

		click_func[3] = {}
		click_func[3].btn_text = Language.TianShen.BaGuaTipBtnText[3]

		if red_info.equip_red and red_info.equip_red == 1 then
			click_func[3].btn_red = 1
		end

		click_func[3].callback = function()
			self:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_PUT, data.index)
		end
	elseif from_view == ItemTip.FROM_TIANSHEN_BAGUA_EQUIP and not IsEmptyTable(data) then
		local compose_data = {}
		local target_item_id, compose_type, compose_way, count_2 = self.tianshen_bagua_data:GetTargetComposeItem(data
		.item_id)
		local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(data.item_id)
		local equip_red = TianShenBaGuaWGData.Instance:GetBaGuaWearRed(base_cfg.index, base_cfg.part)
		compose_data.cost_item = data.item_id
		compose_data.target_item_id = target_item_id
		compose_data.compose_type = compose_type
		compose_data.compose_way = compose_way
		compose_data.count_2 = count_2

		click_func[1] = {}
		click_func[1].btn_text = Language.TianShen.BaGuaTipBtnText[2]
		click_func[1].btn_red = 0

		if compose_type ~= 3 and compose_type ~= 4 then
			click_func[1].btn_red = 1
		end

		click_func[1].callback = function()
			if compose_type == 3 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.BaGuaNoComposeTarget)
			else
				self:BaGuaOpenComPose(compose_data)
			end
			-- elseif compose_type == 4 then
			-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.BaGuaComposeNotEnough)
			-- else
			-- 	self:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_UP_STAR,target_item_id,compose_type)
			-- end
		end

		click_func[2] = {}
		click_func[2].btn_text = Language.TianShen.BaGuaTipBtnText[4]
		click_func[2].btn_red = equip_red
		click_func[2].callback = function()
			self:OpenBaGuaEquipTip(data)
		end
	end
	return click_func
end

function TianShenWGCtrl:OpenBaGuaEquipTip(data)
	if self.bagua_equip_tip then
		self.bagua_equip_tip:SetEquipData(data)
		self.bagua_equip_tip:Open()
		self.bagua_equip_tip:Flush()
	else
		self.bagua_equip_tip:SetEquipData(data)
		self.bagua_equip_tip:Flush()
	end
end

function TianShenWGCtrl:CloseBaGuaEquipTis()
	if self.bagua_equip_tip and self.bagua_equip_tip:IsOpen() then
		self.bagua_equip_tip:Close()
	end
end

function TianShenWGCtrl:OpenBaGuaShopView()
	if self.bagua_shop_view then
		self.bagua_shop_view:Open()
	end
end

function TianShenWGCtrl:BaGuaShopFLushReturn(result)
	if result == 1 then
		if self.bagua_shop_view:IsOpen() then
			self.bagua_shop_view:Flush(0, "flush_anim")
		end
	end
end

function TianShenWGCtrl:BaGuaOpenComPose(data)
	if self.bagua_compose_tip then
		self.bagua_compose_tip:SetComposeData(data)
		if not self.bagua_compose_tip:IsOpen() then
			self.bagua_compose_tip:Open()
		end
		self.bagua_compose_tip:Flush()
	end
end

function TianShenWGCtrl:BaGuaOpenShuXing(cur_select_bagua)
	if self.bagua_sx_view then
		self.bagua_sx_view:SetComposeData(cur_select_bagua)
		if not self.bagua_sx_view:IsOpen() then
			self.bagua_sx_view:Open()
		end

		self.bagua_sx_view:Flush()
	end
end

function TianShenWGCtrl:ShowBaGuaInsertAnim(protocol)
	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush(TabIndex.tianshen_bagua, "InsertAnim", protocol)
	end
end

---------天神宝匣  start -----------------------------------------------------------
function TianShenWGCtrl:OnCSTianShanBoxDrawOper(opera_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShanBoxDrawOper)
	protocol.oper_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:OnSCTianShenBoxInfo(protocol)
	if not self.is_first_rember then
		local str_list = {}
		local data = {}
		if protocol.draw_key_count > self.money_info.draw_key_count then
			data = ItemWGData.Instance:GetItemConfig(39144)
			table.insert(str_list,
				string.format(Language.Bag.GetItemTxt, data.name,
					protocol.draw_key_count - self.money_info.draw_key_count))
		end

		for i, v in ipairs(str_list) do
			SysMsgWGCtrl.Instance:ErrorRemind(v)
		end
	end
	self.money_info.draw_key_count = protocol.draw_key_count
	self.is_first_rember = false
	local total_draw_count = protocol.total_draw_count
	local draw_key_count = protocol.draw_key_count
	-- print_error("FFFF=== 天神宝匣 次数, 材料", total_draw_count, draw_key_count)
	local old_level = self.tianshen_data:GetBaoXiaOldLevel()

	local is_can_uplevel = TianShenWGData.Instance:GetGodHoodDrawIsCanUpLevel()
	if is_can_uplevel and protocol.is_can_uplevel == 0 then
		TipWGCtrl.Instance:ShowEffect(
			{
				effect_type = UIEffectName.s_shengpin,
				is_success = true,
				pos = Vector2(0, 0),
			}
		)
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))

		if self.tianshen_baoxia_view:IsOpen() then
			self.tianshen_baoxia_view:TSBoxPlayStoneUpLevelEffect()
		end
	end

	self.tianshen_data:SetTianShenBaoXiaBoxInfo(protocol)
	local new_level = self.tianshen_data:GetGodHoodDrawGradeCfg().grade or 1
	-- print_error("FFF==== old_level, new_level", old_level, new_level)

	local data_list = protocol.data_list
	-- print_error("FFFF=== 天神宝匣 data_list", data_list)
	if not IsEmptyTable(data_list) then
		if protocol.item_count > 10 then
			TianShenWGData.Instance:DelayFlushShenShiRed(0.4)
		else
			TianShenWGData.Instance:DelayFlushShenShiRed(0)
		end
		self:OpenTianShenBaoXiaRewardPanel(data_list)
	else
		--货币改变时刷新
		self:FlushTianShenBaoXiaView()
	end

	-- if old_level and old_level < new_level then
	-- 	self:OpenTianShenBaoXiaUpLevelView()
	-- end

	if self.tianshen_view:IsOpen() then
		self.tianshen_view:DoFLushShenShiBagRedPoint()
	end
end

function TianShenWGCtrl:GetTianShenBoxIsOpen()
	local other_cfg = TianShenWGData.Instance:GetGodHoodDrawOtherCfg()
	local is_open = false
	local open_day = 2
	if other_cfg and other_cfg.open_day then
		local cur_openday = TimeWGCtrl.Instance:GetCurOpenServerDay()
		is_open = cur_openday >= other_cfg.open_day
		open_day = other_cfg.open_day
	end
	return is_open, open_day
end

function TianShenWGCtrl:OpenTianShenBaoXiaView()
	local is_open, open_day = self:GetTianShenBoxIsOpen()
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.SystemOpenDay, open_day))
		return
	end
	ViewManager.Instance:Open(GuideModuleName.TianShenBaoXia)
end

function TianShenWGCtrl:TryToResetTSBoxModel()
	if self.tianshen_baoxia_view:IsOpen() then

	end
end

--刷新宝匣界面的神饰背包
function TianShenWGCtrl:FlushTianShenBaoXiaView()
	if self.tianshen_baoxia_view:IsOpen() then
		self.tianshen_baoxia_view:Flush()
	end
end

function TianShenWGCtrl:OpenTianShenBaoXiaTipPanel(show_type)
	self.tianshen_baoxia_tips_view:SetBoxTipShowType(show_type)
	if not self.tianshen_baoxia_tips_view:IsOpen() then
		self.tianshen_baoxia_tips_view:Open()
	end
end

function TianShenWGCtrl:OpenTianShenBaoXiaRewardPanel(info)
	self.tianshen_baoxia_reward_panel:SetInfo(info)
	if self.tianshen_baoxia_reward_panel:IsOpen() then
		self.tianshen_baoxia_reward_panel:Flush()
		return
	end
	self.tianshen_baoxia_reward_panel:Open()
end

-- function TianShenWGCtrl:OpenTianShenBaoXiaUpLevelView()
-- 	if self.tianshen_baoxia_levelup_view:IsOpen() then
-- 		self.tianshen_baoxia_levelup_view:Flush()
-- 		return
-- 	end
-- 	self.tianshen_baoxia_levelup_view:Open()
-- end

function TianShenWGCtrl:GetTianShenBaoXiaViewIsOpen()
	return self.tianshen_baoxia_view:IsOpen()
end

function TianShenWGCtrl:CloseTianShenBaoXiaView()
	if self.tianshen_baoxia_view:IsOpen() then
		self.tianshen_baoxia_view:Close()
	end
end

function TianShenWGCtrl:PlayNextShenShiRewardTween()
	self.tianshen_baoxia_reward_panel:NextGetShenShiRewardTween()
end

---------天神宝匣  end -----------------------------------------------------------

---------神格抽奖  start -----------------------------------------------------------
function TianShenWGCtrl:OnCSGodhoodDrawOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGodhoodDrawOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:OnSCGodhoodDrawItemInfo(protocol)
	local other_cfg = TianShenWGData.Instance:GetGodHoodDrawOtherCfg()
	local draw_key_count = protocol.item.draw_score
	if not self.is_first_rember then
		local str_list = {}
		local data = {}
		
		if draw_key_count > self.money_info.draw_key_count then
			data = ItemWGData.Instance:GetItemConfig(other_cfg.consume_item)
			table.insert(str_list,
				string.format(Language.Bag.GetItemTxt, data.name,
				draw_key_count - self.money_info.draw_key_count))
		end

		for i, v in ipairs(str_list) do
			SysMsgWGCtrl.Instance:ErrorRemind(v)
		end
	end
	self.money_info.draw_key_count = draw_key_count
	self.is_first_rember = false


	local is_can_uplevel = TianShenWGData.Instance:GetGodHoodDrawIsCanUpLevel()
	if is_can_uplevel and protocol.is_can_uplevel == 0 then
		TipWGCtrl.Instance:ShowEffect(
			{
				effect_type = UIEffectName.s_shengpin,
				is_success = true,
				pos = Vector2(0, 0),
			}
		)
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))

		if self.tianshen_baoxia_view:IsOpen() then
			self.tianshen_baoxia_view:TSBoxPlayStoneUpLevelEffect()
		end
	end

	self.tianshen_data:SetTianShenBaoXiaBoxInfo(protocol)
	TianShenWGData.Instance:SetGodHoodDrawInfo(protocol)

	local data_list = protocol.data_list


	--货币改变时刷新
	self:FlushTianShenBaoXiaView()
	-- if not IsEmptyTable(data_list) then
	-- 	if protocol.item_count > 10 then
	-- 		TianShenWGData.Instance:DelayFlushShenShiRed(0.4)
	-- 	else
	-- 		TianShenWGData.Instance:DelayFlushShenShiRed(0)
	-- 	end
	-- 	self:OpenTianShenBaoXiaRewardPanel(data_list)
	-- else
		
	-- end
	if self.tianshen_view:IsOpen() then
		self.tianshen_view:DoFLushShenShiBagRedPoint()
	end
end

function TianShenWGCtrl:OnSCGodhoodDrawDrawResult(protocol)
	if #protocol.result_item_list > 10 then
		TianShenWGData.Instance:DelayFlushShenShiRed(0.4)
	else
		TianShenWGData.Instance:DelayFlushShenShiRed(0)
	end
	self:OpenTianShenBaoXiaRewardPanel(protocol.result_item_list)
end

function TianShenWGCtrl:OnSCGodhoodDrawRecordInfo(protocol)

end

function TianShenWGCtrl:OnSCGodhoodDrawRecordAdd(protocol)

end
---------神格抽奖  end -----------------------------------------------------------


function TianShenWGCtrl:OnSCTianShenSkillCall(protocol)
	--[[2021/08/13 屏蔽天神动画
	local cfg = self.tianshen_data:GetTianShenCfg(protocol.call_index)
	if cfg ~= nil then
		self:OpenTianShenCloseUpView(cfg.appe_image_id, true)
	end
	]]
end

-- 请求获取天神养成度信息
function TianShenWGCtrl:SendRequestCompleteRate()
	self:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type21)
end

-- 天神养成完成度
function TianShenWGCtrl:OnSCAllTianShenCompleteRate(protocol)
	TianShenWGData.Instance:SetCompleteRateList(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TianShen3v3View)
end

-- 天神变身通知
function TianShenWGCtrl:OnSCTianShenBianShenDongMan(protocol)
	--[[2021/08/13 屏蔽天神动画
	TianShenWGCtrl.Instance:OpenTianShenCloseUpView(protocol.appe_image_id)
	]]
	if protocol.appe_image_id > 0 then
		-- MainuiWGCtrl.Instance:ShowTianShenScreenEffect(true)
		self:OpenTianShenBianshenFloatView(protocol.appe_image_id)
	else
		MainuiWGCtrl.Instance:ShowTianShenScreenEffect(false)
	end
end

-- function TianShenWGCtrl:OpenTianShenJiBanTip()
-- 	local flag = self.tianshen_data:GetTianShenJiBanAct()
-- 	if flag then
-- 		if self.jiban_tip:IsOpen() then
-- 			self.jiban_tip:Flush()
-- 			return
-- 		end
-- 		self.jiban_tip:Open()
-- 	end
-- end

-- function TianShenWGCtrl:OpenTianShenShopSet()
-- 	if self.bagua_shop_setting_view then
-- 		if not self.bagua_shop_setting_view:IsOpen() then
-- 			self.bagua_shop_setting_view:Open()
-- 		end
-- 		self.bagua_shop_setting_view:Flush()
-- 	end

-- end

function TianShenWGCtrl:BaGuaShopSetChange()
	if self.tianshen_view and self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end

	if self.bagua_shop_view and self.bagua_shop_view:IsOpen() then
		self.bagua_shop_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.TianShen_BaGua)
end

function TianShenWGCtrl:GetTianShenShouldPlayTurnAnim()
	if self.bagua_shop_view then
		return self.bagua_shop_view:GetTianShenShouldPlayTurnAnim()
	end

	return 0
end

function TianShenWGCtrl:DoBuyHaveBaGuaConfirm(ok_func, str)
	if not self.confirm_buy_alert then
		self.confirm_buy_alert = Alert.New()
	end
	local str = str
	self.confirm_buy_alert:SetOkFunc(ok_func)
	self.confirm_buy_alert:SetLableString(str)
	self.confirm_buy_alert:SetCheckBoxText(Language.MountLingChongEquip.DontTip)
	self.confirm_buy_alert:SetShowCheckBox(true, "bagua_have_buy")
	self.confirm_buy_alert:SetCheckBoxDefaultSelect(false)
	self.confirm_buy_alert:Open()
end

function TianShenWGCtrl:DynamicLoadAsset(data, is_delete, cur_fight_dic)
	if data ~= nil and next(data) ~= nil then
		for k, v in pairs(data) do
			if is_delete then
				local model_bundle, model_asset = ResPath.GetBianShenModel(v.appe_image_id)
				--[[ 屏蔽天神出场大图
				if cur_fight_dic ~= nil then
					if cur_fight_dic[v.index] == nil then
						local bundle_name, asset_name = ResPath.GetF2RawImagesPNG("show_bg_" .. v.appe_image_id)
						DynamicAssetCache.Instance:UnLoadAsset(LOAD_ASSET_TYPE.TypeUnityTexture, bundle_name, asset_name)

					end					
				else
					local bundle_name, asset_name = ResPath.GetF2RawImagesPNG("show_bg_" .. v.appe_image_id)
					DynamicAssetCache.Instance:UnLoadAsset(LOAD_ASSET_TYPE.TypeUnityTexture, bundle_name, asset_name)				end

				local jiban_cfg = self.tianshen_data:GetTianShenCfg(v.jiban_index)

				if jiban_cfg ~= nil then
					if cur_fight_dic ~= nil then
						if cur_fight_dic[jiban_cfg.index] == nil then
							bundle_name, asset_name = ResPath.GetF2RawImagesPNG("show_bg_" .. jiban_cfg.appe_image_id)
							DynamicAssetCache.Instance:UnLoadAsset(LOAD_ASSET_TYPE.TypeUnityTexture, bundle_name, asset_name)
						end
					else
						bundle_name, asset_name = ResPath.GetF2RawImagesPNG("show_bg_" .. jiban_cfg.appe_image_id)
						DynamicAssetCache.Instance:UnLoadAsset(LOAD_ASSET_TYPE.TypeUnityTexture, bundle_name, asset_name)
					end
				end
				]]
				DynamicAssetCache.Instance:UnLoadAsset(LOAD_ASSET_TYPE.TypeUnityPrefab, model_bundle, model_asset)
			else
				local model_bundle, model_asset = ResPath.GetBianShenModel(v.appe_image_id)
				--[[ 屏蔽天神出场大图
				local bundle_name, asset_name = ResPath.GetF2RawImagesPNG("show_bg_" .. v.appe_image_id)
				DynamicAssetCache.Instance:LoadAsset(LOAD_ASSET_TYPE.TypeUnityTexture, bundle_name, asset_name)
				local jiban_cfg = self.tianshen_data:GetTianShenCfg(v.jiban_index)
				if jiban_cfg ~= nil then
					bundle_name, asset_name = ResPath.GetF2RawImagesPNG("show_bg_" .. jiban_cfg.appe_image_id)
					DynamicAssetCache.Instance:LoadAsset(LOAD_ASSET_TYPE.TypeUnityTexture, bundle_name, asset_name)
				end
				]]
				DynamicAssetCache.Instance:LoadAsset(LOAD_ASSET_TYPE.TypeUnityPrefab, model_bundle, model_asset)
			end
		end
	end
end

--天神组合技能激活信息
function TianShenWGCtrl:OnSCTianShenUnionSkillInfo(protocol)
	self.tianshen_data:OnSCTianShenUnionSkillInfo(protocol)
end

function TianShenWGCtrl:OpenTianShenZhenFaView()
	if not self.tianshen_zhenfa_view:IsOpen() then
		self.tianshen_zhenfa_view:Open()
	else
		self.tianshen_zhenfa_view:Flush()
	end
end

function TianShenWGCtrl:CloseTianShenZhenFaView()
	if self.tianshen_zhenfa_view:IsOpen() then
		self.tianshen_zhenfa_view:Close()
	end
end

function TianShenWGCtrl:GetView()
	return self.view
end

function TianShenWGCtrl:GetTsView()
	return self.tianshen_view
end

function TianShenWGCtrl:OpenTSGuangHuanTipView(data)
	if self.tianshen_guanghuan_tip_view and not self.tianshen_guanghuan_tip_view:IsOpen() then
		self.tianshen_guanghuan_tip_view:SetShowInfo(data)
		self.tianshen_guanghuan_tip_view:Open()
	end
end

function TianShenWGCtrl:SendPerformChangeTianShenSkill(wuxing_type, remove_skill, add_skill)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPerformChangeTianShenSkill)
	protocol.wuxing_type = wuxing_type or 0
	protocol.remove_skill = remove_skill
	protocol.add_skill = add_skill
	protocol:EncodeAndSend()
end

function TianShenWGCtrl:OpenTianShenFightAlertOpenTip(data)
	self.tianshen_fight_alert:SetOpenData(data)
	self.tianshen_fight_alert:Open()
end

function TianShenWGCtrl:OnSCMultiWuXingType(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if obj == nil or obj:IsDeleted() or not obj:IsRole() then
		return
	end

	-- obj:CheckTianShenSpecialEff(protocol.wuxing_type)
	-- obj:CheckTianShenSpecialPartEff(protocol.wuxing_type)
end

---操作结果
function TianShenWGCtrl:OnTianShenHeJiSkillActResult(result, param_1)
	--[[if result == 1 then
		local is_load = self.tianshen_view.show_index == TabIndex.tianshen_heji
		if is_load then
			self.tianshen_view:HeJiSkillEffectActive()
		end
	end]]
end

-- 天神变身飘字
function TianShenWGCtrl:OpenTianShenBianshenFloatView(appe_image_id)
	if nil == appe_image_id then
		return
	end
	self.tianshen_bianshen_float_view:SetDataAndOpen(appe_image_id)
end

function TianShenWGCtrl:CloseTianShenBianShenFloatView()
	if self.tianshen_bianshen_float_view:IsOpen() then
		self.tianshen_bianshen_float_view:Close()
	end
end

function TianShenWGCtrl:OpenTipsShenShiSuccinctView(tianshen_index)
	self.tianshen_shenshi_succinct_tip:SetData(tianshen_index)
end

-----------------------------------------------------------------------------------------
-- 天神殿协议请求
function TianShenWGCtrl:SendCSTianshenHallOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianshenHallOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 请求天神殿基础信息
function TianShenWGCtrl:RequestTianShenHallBaseInfo()
	self:SendCSTianshenHallOperate(TIANSHEN_HALL_OPERATE_TYPE.TIANSHEN_HALL_OPERATE_TYPE_BASE_INFO)
end

-- 请求天神殿神殿信息
function TianShenWGCtrl:RequestTianShenHallItemInfo()
	self:SendCSTianshenHallOperate(TIANSHEN_HALL_OPERATE_TYPE.TIANSHEN_HALL_OPERATE_TYPE_ITEM_INFO)
end

-- 请求天神殿神殿升阶
function TianShenWGCtrl:RequestTianShenHallUpOrder(seq)
	self:SendCSTianshenHallOperate(TIANSHEN_HALL_OPERATE_TYPE.TIANSHEN_HALL_OPERATE_TYPE_UP_ORDER, seq)
end

-- 请求天神殿神殿上阵
function TianShenWGCtrl:RequestTianShenHallGoHall(seq, index, tianshen_index)
	self:SendCSTianshenHallOperate(TIANSHEN_HALL_OPERATE_TYPE.TIANSHEN_HALL_OPERATE_TYPE_GO_HALL, seq, index, tianshen_index)
end

-- 请求天神殿神殿选择
function TianShenWGCtrl:RequestTianShenHallUseSeq(seq)
	self:SendCSTianshenHallOperate(TIANSHEN_HALL_OPERATE_TYPE.TIANSHEN_HALL_OPERATE_TYPE_USE_SEQ, seq)
end

-- 请求激活
function TianShenWGCtrl:RequestTianShenHallActive(seq)
	self:SendCSTianshenHallOperate(TIANSHEN_HALL_OPERATE_TYPE.TIANSHEN_HALL_OPERATE_TYPE_ACTIVE, seq)
end

-- 天神殿一键上阵请求
function TianShenWGCtrl:SendCSTianshenHallQuickOperate(seq, index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianshenHallGoHall)
	protocol.seq = seq or 0
	protocol.index_list = index_list or {}
	protocol:EncodeAndSend()
end

--天神殿基础信息
function TianShenWGCtrl:OnSCTianshenHallBaseInfo(protocol)
	-- print_error("天神殿基础信息", protocol)
	self.ts_shenling_wg_data:SetTSHallBaseInfo(protocol)
	RemindManager.Instance:Fire(RemindName.TianShen_Temple)

	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end
end

--天神殿神殿信息（全部）
function TianShenWGCtrl:OnSCTianshenHallItemInfo(protocol)
	-- print_error("天神殿神殿信息（全部）", protocol)
	self.ts_shenling_wg_data:SetTSHallItemInfo(protocol)
	RemindManager.Instance:Fire(RemindName.TianShen_Temple)

	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end
end

--天神殿神殿信息（单个）
function TianShenWGCtrl:OnSCTianshenHallItemUpdate(protocol)
	-- print_error("天神殿神殿信息（单个）", protocol)
	self.ts_shenling_wg_data:SetTSHallItemUpdate(protocol)
	RemindManager.Instance:Fire(RemindName.TianShen_Temple)

	if self.tianshen_view:IsOpen() then
		self.tianshen_view:Flush()
	end
end

--天神殿神殿上阵
function TianShenWGCtrl:OpenTSShenLingDianSelectView(shendian_seq, tianshen_index, slot_index)
	if not self.tianshen_shenlingdian_select_view:IsOpen() then
		self.tianshen_shenlingdian_select_view:SetData(shendian_seq, tianshen_index, slot_index)
	end
end

--天神殿神殿上阵
function TianShenWGCtrl:CloseTSShenLingDianSelectView()
	if self.tianshen_shenlingdian_select_view:IsOpen() then
		self.tianshen_shenlingdian_select_view:Close()
	end
end

--天神殿神殿星数加成
function TianShenWGCtrl:OpenTSShenDianLingStarAttrView(shendian_seq)
	if not self.tianshen_shendian_star_attr_view:IsOpen() then
		self.tianshen_shendian_star_attr_view:SetData(shendian_seq)
	end
end
-----------------------------------------------------------------------------------------

