KFOneVOneRewardView = KFOneVOneRewardView or BaseClass(SafeBaseView)

KFOneVOneDataList = 16
function KFOneVOneRewardView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(1078, 608)})
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf_onevone_reward")
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
end

function KFOneVOneRewardView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.Kuafu1V1.ChaKanJiangLi

	XUI.AddClickEventListener(self.node_list["reward_btn"], BindTool.Bind(self.ClickRewardBtn,self))

	self.rank_msg_list = AsyncListView.New(KFOneVOneRankMsgItem, self.node_list.rank_msg_list)
	self.my_item_list = AsyncListView.New(KF1V1RewardItemCell, self.node_list.my_item_list)

	local zhou_str,start_time,end_time = BiZuoWGData.Instance:GetActOpenTimeStr(ACTIVITY_TYPE.KF_ONEVONE)
	local time_str = start_time .. "-" .. end_time
	local open_time_str = string.format(Language.Kuafu1V1.OpenTimeZhouDarkStr,zhou_str,time_str)
	self.node_list["act_time"].text.text = open_time_str
end

function KFOneVOneRewardView:ReleaseCallBack()
	if self.rank_msg_list then
		self.rank_msg_list:DeleteMe()
		self.rank_msg_list = nil
	end

	if self.my_item_list then
		self.my_item_list:DeleteMe()
		self.my_item_list = nil
	end
end

function KFOneVOneRewardView:OpenCallBack()
	-- 请求淘汰赛对阵表
	local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_KNOCKOUT_MATCH_INFO
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_SCORE_RANK)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_MATCH_INFO)
end

function KFOneVOneRewardView:OnFlush(param)
	for k, v in pairs(param) do
		if "all" == k then
            self:FlushScoreRankView()
		end
	end

	--local rank_red = KFOneVOneWGData.Instance:IsShowRankRewardRed()
	--self.node_list["person_tabbtn_red"]:SetActive(rank_red == 1)
end

function KFOneVOneRewardView:FlushScoreRankView()
	local onevone_rank_list_data = KFOneVOneWGData.Instance:GetOneVOneRankList()
	local msg_data_list = {}
	for i = 1, KFOneVOneDataList do
		if onevone_rank_list_data[i] then
			table.insert(msg_data_list, onevone_rank_list_data[i])
		else
			msg_data_list[i] = {}
		end
	end
	self.rank_msg_list:SetDataList(msg_data_list)

	local person_info = KFOneVOneWGData.Instance:GetOneVOneMyRankPerson()
	--local onevone_person_info = KFOneVOneWGData.Instance:GetOneVOnePersonInfo()
	--self.node_list["not_my_msg_item"]:SetActive(IsEmptyTable(person_info))
	self.node_list["my_item_list"]:SetActive(not IsEmptyTable(person_info))
	--local cur_server_id = RoleWGData.Instance:GetCurServerId()
	local server_id = RoleWGData.Instance:GetOriginServerId()
	--print_error(cur_server_id)
	self.node_list["my_server_name"].text.text = string.format(Language.Kuafu1V1.Server3, server_id)

	if not IsEmptyTable(person_info) then
		local server_name = LoginWGData.Instance:GetServerName(person_info.server_id)
		local guild_name = string.format(Language.BiZuo.ServerName_1,person_info.server_id,server_name)
		self.node_list["my_guild_name"].text.text = guild_name
		--local name = ""
		-- if person_info.name and person_info.name ~= "" then
		-- 	local name_list = Split(person_info.name, "_")
		-- 	name = string.format(Language.Kuafu1V1.ServerName_1,name_list[2],name_list[1])
		-- end
		self.node_list["my_role_name"].text.text = string.format(Language.Kuafu1V1.RankStr, person_info.rank)
		self.node_list["my_score"].text.text = person_info.score
		local reward_data_seq = KFOneVOneWGData.Instance:GetRankRewardSeq(person_info.rank)
		local is_get = false
		local is_red = false
		if reward_data_seq then
			local reward_data =  KFOneVOneWGData.Instance:GetRankRewardCfgBySeq(reward_data_seq)
			is_get = KFOneVOneWGData.Instance:GetRankRewardFetchFlag(reward_data_seq)
			is_red = KFOneVOneWGData.Instance:GeRankRewardIsGet(reward_data_seq)
			local data_list = {}
			for i = 0,#reward_data.reward_item do
				table.insert(data_list, reward_data.reward_item[i])
			end
			self.my_item_list:SetDataList(data_list)
		end

		self.node_list["reward_remind"]:SetActive(is_red)
		self.node_list["reward_btn"]:SetActive(is_red)
		self.node_list["ylq_img"]:SetActive(is_get)
	else
		self.node_list["my_role_name"].text.text = Language.Kuafu1V1.RankStr1
		self.node_list["my_score"].text.text = Language.Kuafu1V1.XiaHuaXian
		self.node_list.reward_btn:SetActive(false)
		self.node_list.reward_remind:SetActive(false)
		self.node_list.ylq_img:SetActive(false)
	end
end

function KFOneVOneRewardView:ClickRewardBtn()
	local person_info = KFOneVOneWGData.Instance:GetOneVOneMyRankPerson()
	local reward_data_seq = KFOneVOneWGData.Instance:GetRankRewardSeq(person_info.rank)
	if not IsEmptyTable(person_info) then
		local is_red = KFOneVOneWGData.Instance:GeRankRewardIsGet(reward_data_seq)
		if is_red then
			local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_FETCH_RANK_REWARD
			Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type, reward_data_seq)
		end
	end
end

-----------------------------KFOneVOneRankMsgItem------------------------------------------
KFOneVOneRankMsgItem = KFOneVOneRankMsgItem or BaseClass(BaseRender)

function KFOneVOneRankMsgItem:__init()
	self.item_list = {}
end

function KFOneVOneRankMsgItem:__delete()
	for k,v in pairs(self.item_list) do
		v:DeleteMe()
	end
	self.item_list = {}
end

function KFOneVOneRankMsgItem:OnFlush()
	-- if self.node_list["bg"] then
	-- 	self.node_list.bg:SetActive(self.index % 2 == 1)
	-- end

	self.node_list["quality_bg"]:SetActive(self.index <= 3)
	if self.index <= 3 then
		self.node_list["quality_bg"].image:LoadSprite(ResPath.GetCommon("a3_ty_list_" .. self.index))
	end
	local reward_data_seq = KFOneVOneWGData.Instance:GetRankRewardSeq(self.index)
	if reward_data_seq then
		local reward_data =  KFOneVOneWGData.Instance:GetRankRewardCfgBySeq(reward_data_seq)
		local data_list = {}
		for i = 0,#reward_data.reward_item do
			table.insert(data_list, reward_data.reward_item[i])
		end

		local item_list = self.item_list
		if #data_list > #item_list then
			local cell_parent = self.node_list.cell_group
			for i = 1, #data_list do
				item_list[i] = item_list[i] or ItemCell.New(cell_parent)
			end
			self.item_list = item_list
		end

		for i=1, #item_list do
			if data_list[i] then
				item_list[i]:SetData(data_list[i])
				item_list[i]:SetActive(true)
			else
				item_list[i]:SetActive(false)
			end
		end
	end

	local have_data = not IsEmptyTable(self.data)
	if have_data then
		local rank_num = self.data.rank
		self.node_list["rank"]:SetActive(rank_num > 3)
		self.node_list["rank_img"]:SetActive(rank_num <= 3)

		if rank_num <= 3 then
			self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank_num))
			self.node_list["rank_img"].image:SetNativeSize()
			self.node_list.rank_text.text.text = rank_num
		else
			self.node_list["rank"].text.text = rank_num
			self.node_list.rank_text.text.text = ""
		end

		local server_name = LoginWGData.Instance:GetServerName(self.data.server_id)
		local guild_name = string.format(Language.BiZuo.ServerName_1,self.data.server_id,server_name)
		self.node_list["guild_name"].text.text = guild_name
		local name = ""
		local server = ""
		if self.data.name and self.data.name ~= "" then
			local name_list = Split(self.data.name, "_")
			server = string.format(Language.Kuafu1V1.Server2,name_list[2])
			name = name_list[1]
		end
		self.node_list["role_name"].text.text = name
		self.node_list["server"].text.text = server
		self.node_list["score"].text.text = self.data.score
	else
		self.node_list["rank"]:SetActive(self.index > 3)
		self.node_list["rank_img"]:SetActive(self.index <= 3)
		if self.index <= 3 then
			self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.index))
			self.node_list["rank_img"].image:SetNativeSize()
			self.node_list.rank_text.text.text = self.index
		else
			self.node_list["rank"].text.text = self.index
			self.node_list.rank_text.text.text = ""
		end
		self.node_list.role_name.text.text =  Language.Kuafu1V1.XiaHuaXian
		self.node_list["server"].text.text = Language.Kuafu1V1.XiaHuaXian
		self.node_list.guild_name.text.text =  Language.Kuafu1V1.XiaHuaXian
		self.node_list["score"].text.text = Language.Kuafu1V1.XiaHuaXian
	end
end

KF1V1RewardItemCell = KF1V1RewardItemCell or BaseClass(BaseRender)
function KF1V1RewardItemCell:__init()
	self.cell = ItemCell.New(self.node_list.pos)
end

function KF1V1RewardItemCell:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function KF1V1RewardItemCell:OnFlush()
	self.cell:SetData(self.data)
end