PositionalWarfareSceneLogic = PositionalWarfareSceneLogic or BaseClass(CommonFbLogic)

function PositionalWarfareSceneLogic:__init()
	self.update_elaspe_time = 0
end

function PositionalWarfareSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnter, self))

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.PositionalWarfare.PWFBName)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetToPositionalWaefareViewBtnState(true)
		PositionalWarfareWGCtrl.Instance:OpenFBTaskView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	local role_list = Scene.Instance:GetRoleList()
	for i, v in pairs(role_list) do
		if not v:IsMainRole() then
			local follow_ui = v:GetFollowUi()
			follow_ui:SetPWCampIcon(v:GetVo().special_param)
		end
	end

	local main_role = Scene.Instance:GetMainRole()
	-- local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()
	local follow_ui = main_role:GetFollowUi()
	follow_ui:SetPWCampIcon(my_camp)

	local view = PositionalWarfareWGCtrl.Instance:GetFBTaskView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIRightTopChangeList(view)
	self.update_elaspe_time = 0

	MainuiWGCtrl.Instance:SetMianUITargetPos(0, -50)
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.PositionalWarfare.OutFbTips)
end

function PositionalWarfareSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

    PositionalWarfareWGCtrl.Instance:CloseFBTaskView()
    MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	MainuiWGCtrl.Instance:SetToPositionalWaefareViewBtnState(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	if self.role_enter_event then
		GlobalEventSystem:UnBind(self.role_enter_event)
		self.role_enter_event = nil
	end

	local view = PositionalWarfareWGCtrl.Instance:GetFBTaskView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
	MainuiWGCtrl.Instance:SetMianUITargetPos(0, 0)

	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Dungeon.ConfirmLevelFB)
end

function PositionalWarfareSceneLogic:Update(now_time, elapse_time)
	BaseSceneLogic.Update(self, now_time, elapse_time)

	-- local main_role = Scene.Instance:GetMainRole()
	-- if main_role ~= nil and main_role:IsDeleted() then
	-- 	return
	-- end

	-- if now_time > self.update_elaspe_time then
	-- 	if GuajiCache.guaji_type == GuajiType.Auto then
	-- 		-- 拾取
	-- 		local obj_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
	-- 		if not IsEmptyTable(obj_list) then
	-- 			local obj_id_list = {}
	
	-- 			for k, v in pairs(obj_list)do
	-- 				table.insert(obj_id_list, v:GetObjId())
	-- 			end
	
	-- 			if not IsEmptyTable(obj_id_list) then
	-- 				self.update_elaspe_time = now_time + 2
	-- 				GlobalTimerQuest:AddTimesTimer(function ()
	-- 					Scene.ScenePickItem(obj_id_list)
	-- 				end, 1, 1)
	-- 			end
	-- 		end
	-- 	end
	-- end
end

function PositionalWarfareSceneLogic:GetGuajiPos()
	local target_distance = 1000 * 1000
    local target_x = 0
    local target_y = 0
	local get_pos = false
    local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	local boss_data_list = PositionalWarfareWGData.Instance:GetCurSceneBossDataList()
	for k, v in pairs(boss_data_list) do
		local boss_info = PositionalWarfareWGData.Instance:GetCurSceneBossInfo(v.seq)
		
		if boss_info and boss_info.is_die == 0 then
			local split_list = string.split(v.monster_pos, ",")
			local pos_x = split_list[1]
			local pos_y = split_list[2]
        	local not_block = not AStarFindWay:IsBlock(pos_x, pos_y)

			if not_block then
				local distance = GameMath.GetDistance(x, y, pos_x, pos_y, false)
				if distance < target_distance then
					target_x = pos_x
					target_y = pos_y
					target_distance = distance
					get_pos = true
				end
			end
		end
    end

	if get_pos then
		return target_x, target_y
	end
end

-- 是否是挂机打怪的敌人
function PositionalWarfareSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	local tired = PositionalWarfareWGData.Instance:GetTired()
	local max_tired = PositionalWarfareWGData.Instance:GetOtherAttrValue("max_tired")

	if tired >= max_tired then
		return false, Language.PositionalWarfare.OutTired
	end

	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end

	return true
end

function PositionalWarfareSceneLogic:IsRoleEnemy(target_obj, main_role)
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local target_obj_vo = target_obj:GetVo()

	if target_obj_vo.role_id == main_role_vo.role_id then
		return false
   	end

	local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()
	if target_obj_vo.special_param == my_camp then
		return false
	end

	return true
end

function PositionalWarfareSceneLogic:IsMonsterEnemy(target_obj, main_role)
	local tired = PositionalWarfareWGData.Instance:GetTired()
	local max_tired = PositionalWarfareWGData.Instance:GetOtherAttrValue("max_tired")

	if tired >= max_tired then
		return false, Language.PositionalWarfare.OutTired
	end

	-- 策划wjx 要求打boss需要的怒气值不够锕，也要能打
	-- if target_obj:IsBoss() then
	-- 	local vo = target_obj:GetVo()
	-- 	local boss_id = vo.monster_id or -1

	-- 	if boss_id > 0 and vo.special_param > 0 then
		-- local boss_seq =  (vo.special_param % 100000) % 1000
		-- local land_seq =  math.floor((vo.special_param % 100000) / 1000) - 1
		-- local monster_group = math.floor(vo.special_param / 100000) - 1

	-- 		local boss_cfg = PositionalWarfareWGData.Instance:GetCurMonsterCfgData(monster_group, land_seq, boss_seq)

	-- 		if boss_cfg and boss_cfg.monster_id == boss_id then
	-- 			if (max_tired - tired) < boss_cfg.kill_tired then
	-- 				return false, Language.PositionalWarfare.OutTired
	-- 			end
	-- 		end
	-- 	end
	-- end

	return true
end

function PositionalWarfareSceneLogic:OnRoleEnter(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	local follow_ui = role:GetFollowUi()

	if not role:IsMainRole() then
		follow_ui:SetPWCampIcon(role:GetVo().special_param)
	else
		local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()
		follow_ui:SetPWCampIcon(my_camp)
	end
end