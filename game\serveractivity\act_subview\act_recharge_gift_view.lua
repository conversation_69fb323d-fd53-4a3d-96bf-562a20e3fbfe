----------------------------------------------------
-- 充值有礼
----------------------------------------------------
ActRechargeGiftView = ActRechargeGiftView or BaseClass(SafeBaseView)

function ActRechargeGiftView:__init(act_id)
	self:AddViewResource(0, "uis/view/act_subview_ui_prefab", "layout_act_recharge_gift")
	self.act_id = act_id
	self.open_tween = nil
	self.close_tween = nil
end

function ActRechargeGiftView:__delete()

end

function ActRechargeGiftView:ReleaseCallBack()
	for i,v in ipairs(self.item_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end
	self.need_refresh = nil
	self.has_load_callback = nil
	self.item_list = {}
end

function ActRechargeGiftView:LoadCallBack()
	self.item_list = {}
	for i=1,5 do
		self.item_list[i] = ItemCell.New(self.node_list["ph_cell_" .. i])
	end

	XUI.AddClickEventListener(self.node_list.btn_recharge, BindTool.Bind1(self.OnClickRecharge, self))
	XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind1(self.OnClickLingQu, self))
	self.has_load_callback = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

function ActRechargeGiftView:RefreshView(param_list)
	if not self.has_load_callback then
		self.need_refresh = true
		return
	end
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)

	self:RefreshTopDesc()
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(self.act_id)
	local reward_cfg = ServerActivityWGData.Instance:GetActivityItemDataListNew(self.act_id)

	if not info or not next(reward_cfg) then
		return
	end
	local state = info.cur_value_t[1] >=reward_cfg[1].chongzhi_edu
	self.node_list.btn_recharge:SetActive(not state)
	local item_data = reward_cfg[1].reward_item

	if 1 == info.can_reward_flag[32] then
		-- self.node_list.have_get:SetActive(true)			--已领取图标

		self.node_list.btn_lingqu:SetActive(true)
		self.node_list.btn_text.text.text = (Language.ContinuousRecharge.HasFetch) 	--已领取
	else
		-- self.node_list.have_get:SetActive(false)

		self.node_list.btn_lingqu:SetActive(state)
		self.node_list.btn_text.text.text = (Language.ContinuousRecharge.Fetch)		--领取
	end
	XUI.SetButtonEnabled(self.node_list.btn_lingqu, info.cur_value_t[1] >= reward_cfg[1].chongzhi_edu and 1 ~= info.can_reward_flag[32])	--按钮置灰

	if item_data.item_id ~= nil then
		item_data = ServerActivityWGData.Instance:GetShowRewardListByCfg({item_data}, true)
	else
		item_data = ServerActivityWGData.Instance:GetShowRewardListByCfg(item_data, true)
	end

	for i=1,5 do
		if nil == item_data[i] then
			self.item_list[i]:SetActive(false)
		else
			self.item_list[i]:SetData(item_data[i])
			self.item_list[i]:SetActive(true)
		end
	end

	-- local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local chongzhi_edu = reward_cfg[1].chongzhi_edu
	-- self.node_list.lbl_recharge_desc.text.text = (string.format(Language.Activity.RechargeGiftDesc, chongzhi_edu))
	self.node_list.num_2.text.text = chongzhi_edu
	local is_show_remind = ServerActivityWGData.Instance:GetActBenRechargeGiftRemind() == 1
	self.node_list["rechange_gift_remind"]:SetActive(is_show_remind)
end

function ActRechargeGiftView:OnClickRecharge()
 	FunOpen.Instance:OpenViewNameByCfg("recharge")
end

function ActRechargeGiftView:OnClickLingQu()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)
	local param_t ={}
	param_t = {
		rand_activity_type = open_act_cfg.open_type,
		opera_type = RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE.RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_FETCH_REWARD,
		param_1 = 0
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	AudioService.Instance:PlayRewardAudio()
end

function ActRechargeGiftView:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)

	if self.node_list.version_act_time ~= nil and open_act_cfg ~= nil then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
		local star_str = ""
		local end_str = ""
		if act_info then
			star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
			end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time - 1)
		end
		self.node_list.version_act_time.text.text = (star_str .. "----" .. end_str)
	end

	if self.node_list.version_act_des ~= nil and open_act_cfg ~= nil then
		self.node_list.version_act_des.text.text = Language.OpenServer.ActShuoMing .. (open_act_cfg.top_desc)
	end
end

function ActRechargeGiftView:PlayTween()
	self.open_tween = UITween.ShowFadeUp
end

function ActRechargeGiftView:CloseCallBack()
	self.open_tween = nil
end