function NewAppearanceWGView:FashionInitView()
    if self.is_load_fashion then
        return
    end

    self.fashion_part_type = nil
    self.fashion_index = nil
    self.fs_select_grid_type = nil
    self.fs_force_jump_cell_data = nil
    self.fs_select_cell_index = nil

    if self.fs_star_list == nil then
        self.fs_star_list = {}
        for i = 1, 5 do
            self.fs_star_list[i] = self.node_list["fs_star_" .. i]
        end
    end

    if not self.fs_attr_list then
        self.fs_attr_list = {}
        local parent_node = self.node_list["fs_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            self.fs_attr_list[i] = cell
        end
    end

    if self.fs_stuff_item == nil then
        self.fs_stuff_item = ItemCell.New(self.node_list["fs_stuff_item"])
    end

    XUI.AddClickEventListener(self.node_list["fs_btn_upstar"], BindTool.Bind(self.FashionOnClickUpgrade, self))
    XUI.AddClickEventListener(self.node_list["fs_btn_use"], BindTool.Bind(self.FashionOnClickUse, self))
    XUI.AddClickEventListener(self.node_list["fs_btn_reset"], BindTool.Bind(self.FashionOnClickUse, self))
    XUI.AddClickEventListener(self.node_list["fs_btn_reslove"], BindTool.Bind(self.OpenAppearanceResloveView, self))
    XUI.AddClickEventListener(self.node_list["fs_skill"], BindTool.Bind(self.FashionOnClickSkill, self))
    XUI.AddClickEventListener(self.node_list["fs_btn_use_skill"], BindTool.Bind(self.FashionOnClickUseSkill, self))
    XUI.AddClickEventListener(self.node_list["fs_btn_reset_skill"], BindTool.Bind(self.FashionOnClickRestSkill, self))
    XUI.AddClickEventListener(self.node_list["fs_btn_wardrobe"], BindTool.Bind(self.OnClickOpenWardrobe, self))
    XUI.AddClickEventListener(self.node_list["fs_btn_dye_color"], BindTool.Bind(self.OnClickOpenDyeColor, self))


    self.is_load_fashion = true

    if not self.fs_select_item_grid then
        self.fs_select_item_grid = {}
        self.fs_cell_list = {}

        for i = 1, GameEnum.ITEM_COLOR_COLOR_FUL do
			self.fs_select_item_grid[i] = {}
			self.fs_select_item_grid[i].text_name = self.node_list["fs_btn_text" .. i]
            self.fs_select_item_grid[i].text_name_hl = self.node_list["fs_btn_text_hl" .. i]
			self.fs_select_item_grid[i].list = self.node_list["fs_list_".. i]
            self.node_list["fs_select_btn" .. i].accordion_element.isOn = false
            self.node_list["fs_select_btn" .. i]:SetActive(false)
		    self.node_list["fs_list_" .. i]:SetActive(false)
			self.node_list["fs_select_btn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickFSSelectTogHandler, self, i))
		end
    end
end

function NewAppearanceWGView:FashionReleaseCallBack()
    self:FashionClearCountDown()
    self.fs_star_list = nil
    self.is_load_fashion = nil
    self.fashion_part_type = nil
    self.fashion_index = nil

    if self.fs_stuff_item then
        self.fs_stuff_item:DeleteMe()
        self.fs_stuff_item = nil
    end

    if self.fs_attr_list then
        for k,v in pairs(self.fs_attr_list) do
            v:DeleteMe()
        end
        self.fs_attr_list = nil
    end

    if self.fs_select_item_grid then
		self.fs_select_item_grid = nil
	end

    if self.fs_cell_list then
		for k,v in pairs(self.fs_cell_list) do
			for k1,v1 in pairs(v) do
				v1:DeleteMe()
			end
			self.fs_cell_list[k] = nil
		end

		self.fs_cell_list = nil
	end

    self.fs_select_grid_type = nil
    self.fs_force_jump_cell_data = nil
    self:RemoveFSEffectDelayTimer()
end

function NewAppearanceWGView:FashionShowIndexCallBack()
    self.fashion_part_type = nil
    self.fashion_index = nil
    self.fs_force_jump_cell_data = nil
    self.fs_select_cell_index = nil

	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_lv = RoleWGData.Instance:GetRoleLevel()
	local skynumber_show = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("skynumber_show")
	local level_show = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("level_show")
	local is_show_reslove = server_day >= skynumber_show and role_lv >= level_show
	self.node_list.fs_btn_reslove:SetActive(is_show_reslove)
end

function NewAppearanceWGView:FashionOnItemSelectCB(cell)
    local data = cell and cell:GetData()
    if data == nil then
        return
    end

    local data_change = self.fashion_part_type ~= data.part_type or self.fashion_index ~= data.index
    self.fashion_part_type = data.part_type
    self.fashion_index = data.index
    self:FashionFlushSelectView(self.fashion_part_type == data.part_type and self.fashion_index == data.index)

    if data_change then
        local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.fashion_part_type, self.fashion_index)
        if fashion_cfg then
            local res_id = fashion_cfg.resouce
            self:FlushFashionModel(self.fashion_part_type, res_id)
        end
    end

    -- 获取染色配置
    local fashion_dye_cfg = NewAppearanceDyeWGData.Instance:GetConsumeCfgByIndex(self.fashion_index, self.fashion_part_type)
    if not fashion_dye_cfg then
        return
    end

    local server_project_index = NewAppearanceDyeWGData.Instance:GetDyeingIndexInfoBySeq(fashion_dye_cfg.seq)
    local use_project_index = server_project_index + 1
    local project_info = NewAppearanceDyeWGData.Instance:GetDyeingInfoBySeqIndex(fashion_dye_cfg.seq, use_project_index)
    if (not project_info) or (not project_info.part_color) then
        return
    end

    local change_body_dye_color_fun = function()
        local dye_color_table = {}
        for show_part, color_data in ipairs(project_info.part_color) do
            local dye_index_list_data = NewAppearanceDyeWGData.Instance:GetDyeIndexListBySeqPart(fashion_dye_cfg.seq, show_part)
            local index_list = dye_index_list_data and dye_index_list_data.dye_index_list
    
            local color = nil
            if color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0 then
                color = Color.New(color_data.r / 255, color_data.g / 255, color_data.b / 255, color_data.a / 255)
            end
    
            if show_part ~= HAIR_PART then
                if index_list and color then
                    for _, dye_index in ipairs(index_list) do
                        local dye_color_data = {}
                        dye_color_data.dye_index = dye_index
                        dye_color_data.r = color.r
                        dye_color_data.g = color.g
                        dye_color_data.b = color.b
                        dye_color_data.a = color.a
                        table.insert(dye_color_table, dye_color_data)
                    end
                end
            end
        end
    
        if self.show_model and (not IsEmptyTable(dye_color_table))  then
            self.show_model:ChangePartDyeColor(dye_color_table)
        end
    end

    local change_hair_dye_color_fun = function()
        local color_data = project_info.part_color[HAIR_PART]
        if color_data and (color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0) then
            self.show_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, color_data)
        end
    end

    local body_material_id = NewAppearanceDyeWGData.Instance:GetProjectBodyIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
    local hair_material_id = NewAppearanceDyeWGData.Instance:GetProjectHairIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
    local face_material_id = NewAppearanceDyeWGData.Instance:GetProjectFaceIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
    self.show_model:ChangeRoleMaterialsByProjectId(ROLE_SKIN_TYPE.BODY, body_material_id, change_body_dye_color_fun)
    self.show_model:ChangeRoleMaterialsByProjectId(ROLE_SKIN_TYPE.FACE, face_material_id)
    self.show_model:ChangeRoleMaterialsByProjectId(ROLE_SKIN_TYPE.HAIR, hair_material_id, change_hair_dye_color_fun)
end

function NewAppearanceWGView:FashionFlushView(param_t)
    local grid_data = NewAppearanceWGData.Instance:GetSortShowListByTabIndexAndItemColor(self.show_index)

    local load_info = {}
    for i = 1, GameEnum.ITEM_COLOR_COLOR_FUL do
		self.node_list["fs_select_btn" .. i]:SetActive(nil ~= grid_data[i])

        if nil == grid_data[i] then
            self.node_list["fs_list_" .. i]:CustomSetActive(false)
        end

        self.node_list["fs_btn_text" .. i].text.text = Language.NewAppearance.AppearanceColorTypeName[i]
        self.node_list["fs_btn_text_hl" .. i].text.text = Language.NewAppearance.AppearanceColorTypeName[i]

        local remind = false
        if grid_data[i] then
            for k, v in pairs(grid_data[i]) do
                if v.is_remind then
                    remind = true
                    break
                end
            end
        end

        self.node_list["fs_btn_remind" .. i]:CustomSetActive(remind)

        if self.fs_cell_list[i] ~= nil then
            if nil ~= grid_data[i] then
                local start_num = #self.fs_cell_list[i]
                local end_num = #grid_data[i]

                if start_num < end_num then
                    table.insert(load_info, {index = i, start_num = start_num, end_num = end_num, data_list = grid_data[i]})
                end
            end
        else
            if nil ~= grid_data[i] then
                table.insert(load_info, {index = i, start_num = 1, end_num = #grid_data[i], data_list = grid_data[i]})
            end
        end
	end

    local function load_complete_func()
        self:FlushFSGridListCell()
        self:SetFSGridItemSelect(param_t)
    end

    if IsEmptyTable(load_info) then
        load_complete_func()
    else
        self:LoadFSGridListCell(load_info, function()
            load_complete_func()
        end)
    end
end

function NewAppearanceWGView:FashionFlushSelectView(need_check_show_effect)
    local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.fashion_part_type, self.fashion_index)
    if fashion_cfg == nil then
        return
    end

    local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.fashion_part_type, self.fashion_index)
    local level = NewAppearanceWGData.Instance:GetFashionLevel(self.fashion_part_type, self.fashion_index)
    -- local cur_level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(self.fashion_part_type, self.fashion_index, level)
    -- local next_level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(self.fashion_part_type, self.fashion_index, level + 1)
    -- local is_max = next_level_cfg == nil

    local level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(self.fashion_part_type, self.fashion_index)
    local is_max = level >= level_cfg.max_up_level

    local cur_attr_data = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(self.fashion_part_type, self.fashion_index, level)
    local next_attr_data = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(self.fashion_part_type, self.fashion_index, level + 1)

    -- 属性
    self.node_list["fs_select_name"].text.text = fashion_cfg.name
    -- local attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_level_cfg, next_level_cfg)
    local attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_attr_data, next_attr_data)
    local per_attr = NewAppearanceWGData.Instance:GetFashionPerAddAttr(self.fashion_part_type, self.fashion_index, level)
    local need_show_up_effect = false
    
    local insert_key = -1
    for key, value in pairs(attr_list) do
        if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
            value.attr_name = ToColorStr(EquipmentWGData.Instance:GetAttrName(value.attr_str, false, false), COLOR3B.GLOD)
            if insert_key < 0 then
                insert_key = key
            end
        end
    end

    if not IsEmptyTable(per_attr) then
        if per_attr.attr_value > 0 or per_attr.attr_next_value > 0 then
            table.insert(attr_list, insert_key > 0 and insert_key or #attr_list + 1, per_attr)
        end
    end

    if need_check_show_effect then
        if nil ~= self.fashion_part_type_cache and nil ~= self.fashion_index_cache and nil ~= self.fashion_level_cache then
            if (self.fashion_part_type_cache == self.fashion_part_type) and (self.fashion_index_cache == self.fashion_index) and (level - self.fashion_level_cache == 1) then
                need_show_up_effect = true
            end
        end
    end

    for k,v in pairs(self.fs_attr_list) do
        v:SetData(attr_list[k])

        if need_show_up_effect then
            v:PlayAttrValueUpEffect()
        end
    end

    self.fashion_part_type_cache = self.fashion_part_type
    self.fashion_index_cache = self.fashion_index
    self.fashion_level_cache = level

    -- 升级   时装默认等级 0 激活后1 显示上激活后希望是0星   所以配置多加一级，显示1级激活 + 100级升星
    if is_act then
        local star_res_list = GetTwenTyStarImgResByStar(level - 1)

        for k,v in pairs(self.fs_star_list) do
            v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
            
            self.node_list["fs_name_star_" .. k].image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
        end
    end

    -- 限时
    local is_time_limit_time = false
    self:FashionClearCountDown()
    local end_time = NewAppearanceWGData.Instance:GetFashionEndTime(self.fashion_part_type, self.fashion_index)
    end_time = end_time - TimeWGCtrl.Instance:GetServerTime()

    local is_show_limit = false
    -- 限时道具激活的限时
    local is_limit_time_type = fashion_cfg.shizhuang_type == 2
    if is_limit_time_type and end_time > 0 then
        is_show_limit = true
        is_time_limit_time = true
        self.node_list["fs_limited_time_desc"].text.text = string.format(Language.NewAppearance.LimitTimeDesc[1], TimeUtil.FormatSecondDHM8(end_time))
        self.fashion_quest = CountDown.Instance:AddCountDown(end_time, 0.5,
                                BindTool.Bind(self.FashionFlushLimitTime, self),
                                BindTool.Bind(self.Flush, self))

    -- 活动给的限时
    elseif level == 1 and is_max then
        is_show_limit = true
        self.node_list["fs_limited_time_desc"].text.text = Language.NewAppearance.LimitTimeDesc[2]
    end

    self.node_list["fs_stars_list"]:CustomSetActive(level > 0 and not is_show_limit)
    -- self.node_list["fs_name_stars_list"]:CustomSetActive(level > 0 and not is_show_limit)
    self.node_list["fs_limited_time_desc"]:CustomSetActive(is_show_limit)
    self.node_list["fs_no_max_part"]:CustomSetActive(not is_max or not is_act or is_time_limit_time)
    self.node_list["fs_max_level_flag"]:CustomSetActive(is_act and is_max and not is_time_limit_time)
    self.node_list["fs_cost_title"].text.text = Language.NewAppearance.CostTitle[is_limit_time_type and 2 or 1]

    -- 消耗
    local is_remind = false
    -- local stuff_id, stuff_num = 0, 1
    -- if not is_max or not is_act then
    --     stuff_id = next_level_cfg.stuff_id
    --     stuff_num = next_level_cfg.stuff_num
    -- elseif is_limit_time_type then
    --     stuff_id = next_level_cfg and next_level_cfg.stuff_id or cur_level_cfg.stuff_id
    --     stuff_num = next_level_cfg and next_level_cfg.stuff_num or cur_level_cfg.stuff_num
    -- end

    local stuff_id = level_cfg.stuff_id
    local stuff_num = NewAppearanceWGData.Instance:GetFashionUpLevelCostStuffNumCfg(self.fashion_part_type, self.fashion_index, level) or 1

    if stuff_id and stuff_id > 0 then
        self.fs_stuff_item:SetData({item_id = stuff_id})
        local num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
        is_remind = num >= stuff_num
        local str_color = is_remind and COLOR3B.D_GREEN or COLOR3B.RED
        self.node_list["fs_need_num"].text.text = string.format("%s/%s", ToColorStr(num, str_color), stuff_num)
    end

    local btn_str = ""
    if is_act and is_show_limit then
        btn_str = Language.NewAppearance.UpStarBtnDesc[3]
    elseif is_act then
        btn_str = Language.NewAppearance.UpStarBtnDesc[2]
    else
        btn_str = Language.NewAppearance.UpStarBtnDesc[1]
    end
    self.node_list["fs_btn_upstar_text"].text.text = btn_str
    self.node_list["fs_btn_upstar_remind"]:CustomSetActive(is_remind)

    -- 技能
    local skill_cap = 0
    local skill_cfg = NewAppearanceWGData.Instance:GetFashionSkillCfg(self.fashion_part_type, fashion_cfg.index, 1)
    if skill_cfg then
        self.node_list["fs_skill_lock"]:CustomSetActive(not is_act)
        local bundel, asset = ResPath.GetSkillIconById(skill_cfg.skill_icon)
        self.node_list["fs_skill_icon"].image:LoadSpriteAsync(bundel, asset, function ()
            self.node_list["fs_skill_icon"].image:SetNativeSize()
        end)

        self.node_list["fs_skill_desc"].text.text = string.format("<color=#f89f21>【%s】</color>%s", skill_cfg.skill_name, skill_cfg.skill_describe)
        self.node_list["fs_skill_part"]:CustomSetActive(true)

        if is_act then
            skill_cap = NewAppearanceWGData.Instance:GetSingleSkillCap(skill_cfg)
        end

        local skill_used_index = NewAppearanceWGData.Instance:GetFashionKillUseIndex(self.fashion_part_type)
        local skill_is_used = self.fashion_index == skill_used_index
        if skill_cfg.param1 > 0 then
            self.node_list["fs_btn_use_skill"]:CustomSetActive(is_act and not skill_is_used)
            self.node_list["fs_btn_reset_skill"]:CustomSetActive(is_act and skill_is_used)
        else
            self.node_list["fs_btn_use_skill"]:CustomSetActive(false)
            self.node_list["fs_btn_reset_skill"]:CustomSetActive(false)
        end
    else
        self.node_list["fs_skill_part"]:CustomSetActive(false)
        self.node_list["fs_btn_use_skill"]:CustomSetActive(false)
        self.node_list["fs_btn_reset_skill"]:CustomSetActive(false)
    end

    -- 战力
    local attr_cap = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(cur_attr_data))
    local add_per_val = NewAppearanceWGData.Instance:GetCurFashionPartAddPerValue(self.fashion_part_type)
    local extra_cap =  NewAppearanceWGData.Instance:CalcFashionAttrCapWithAddPer(cur_attr_data, add_per_val)
    attr_cap = attr_cap + extra_cap
    self.node_list["fs_cap_value"].text.text = attr_cap + skill_cap

    -- 激活 / 进阶
    local used_index = NewAppearanceWGData.Instance:GetFashionUseIndex(self.fashion_part_type)
    local is_used = self.fashion_index == used_index
    self.node_list["fs_btn_use"]:CustomSetActive(is_act and not is_used)
    self.node_list["fs_btn_reset"]:CustomSetActive(is_act and is_used)

    -- 染色
    self.node_list["fs_btn_dye_color"]:CustomSetActive(fashion_cfg.is_open_dyeing == 1)
end

function NewAppearanceWGView:FashionClearCountDown()
	if CountDown.Instance:HasCountDown(self.fashion_quest) then
        CountDown.Instance:RemoveCountDown(self.fashion_quest)
        self.fashion_quest = nil
    end
end

function NewAppearanceWGView:FashionFlushLimitTime(elapse_time, total_time)
	local time = total_time - elapse_time
	if time > 0 then
		self.node_list["fs_limited_time_desc"].text.text = string.format(Language.NewAppearance.LimitTimeDesc[1], TimeUtil.FormatSecondDHM8(time))
	else
		self.node_list["fs_limited_time_desc"].text.text = ""
	end
end

-- 激活 / 进阶
function NewAppearanceWGView:FashionOnClickUpgrade()
    local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.fashion_part_type, self.fashion_index)
    if fashion_cfg == nil then
        return
    end
    
    local use_item_id = 0
    local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.fashion_part_type, self.fashion_index)

    local level = NewAppearanceWGData.Instance:GetFashionLevel(self.fashion_part_type, self.fashion_index)
    local cur_level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(self.fashion_part_type, self.fashion_index)
    local next_level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(self.fashion_part_type, self.fashion_index)

    local cur_level_stuff_num = NewAppearanceWGData.Instance:GetFashionUpLevelCostStuffNumCfg(self.fashion_part_type, self.fashion_index, level)
    local next_level_stuff_num = NewAppearanceWGData.Instance:GetFashionUpLevelCostStuffNumCfg(self.fashion_part_type, self.fashion_index, level + 1)

    if is_act then
        local need_num = 1
        if fashion_cfg.shizhuang_type == 2 then
            use_item_id = cur_level_cfg.stuff_id
            need_num = cur_level_stuff_num -- cur_level_cfg.stuff_num
        elseif next_level_cfg and (level < cur_level_cfg.max_up_level) then
            use_item_id = next_level_cfg.stuff_id
            need_num = next_level_stuff_num -- next_level_cfg.stuff_num
        end

        local num = ItemWGData.Instance:GetItemNumInBagById(use_item_id)
        if num < need_num then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = use_item_id})
            return
        end
    else
        use_item_id = next_level_cfg.stuff_id
        local need_num = next_level_stuff_num -- next_level_cfg.stuff_num
        local num = ItemWGData.Instance:GetItemNumInBagById(use_item_id)
        if num < need_num then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = use_item_id})
            return
        end

        local zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
        if zhuan < fashion_cfg.zhuanzhi_level then
            return
        end
    end

    local ok_func = function ()
        NewAppearanceWGCtrl.Instance:OnFashionUpLevel(self.fashion_part_type, self.fashion_index, use_item_id)
    end

    if is_act and fashion_cfg.shizhuang_type == 2 then
        self:OpenIncreaseTimeTips(use_item_id, ok_func)
    else
        ok_func()
    end

    if is_act and fashion_cfg.shizhuang_type ~= 2 then
        self:PlayUpStarEffect()
    end
end

-- 重置 / 幻化
function NewAppearanceWGView:FashionOnClickUse()
    local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.fashion_part_type, self.fashion_index)
    if fashion_cfg == nil then
        return
    end

    local res_id = fashion_cfg.resouce
    local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.fashion_part_type, self.fashion_index)
	if not is_act then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.NoActTips)
		return
	end

    local is_to_use = self.node_list["fs_btn_use"]:GetActive()
    if self.fashion_part_type == SHIZHUANG_TYPE.WING then
		self:FashionCheckIsRest(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING, res_id, is_to_use)
	elseif self.fashion_part_type == SHIZHUANG_TYPE.FABAO then
		self:FashionCheckIsRest(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO, res_id, is_to_use)
	elseif self.fashion_part_type == SHIZHUANG_TYPE.SHENBING then
		self:FashionCheckIsRest(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING, res_id, is_to_use)
	elseif self.fashion_part_type == SHIZHUANG_TYPE.JIANZHEN then
		self:FashionCheckIsRest(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN, res_id, is_to_use)
	else
        NewAppearanceWGCtrl.Instance:OnUseFashion(self.fashion_part_type, self.fashion_index, 1)
	end

	if is_to_use then
		self:PlayUseEffect()
	end
end

-- 进阶系 重置 / 幻化
function NewAppearanceWGView:FashionCheckIsRest(ad_type, res_id, is_to_use)
    -- 重置使用默认模型
    local index = self.fashion_index
    if not is_to_use then
        local cur_level = NewAppearanceWGData.Instance:GetAdvancedLevel(ad_type)
        if cur_level > 0 then
            local cfg = NewAppearanceWGData.Instance:GetAdvancedImageCfgByType(ad_type)
            if cfg then
                res_id = cfg.appe_image_id
                index = cfg.index
            end
        end
    end

	NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE, ad_type, res_id, index)
end

function NewAppearanceWGView:OpenAppearanceResloveView()
    NewAppearanceWGCtrl.Instance:OpenAppearanceResloveView()
end

function NewAppearanceWGView:OpenHuanHuaFetterView()
    ViewManager.Instance:Open(GuideModuleName.NewHuanHuaFetterView)
end


function NewAppearanceWGView:FashionOnClickSkill()
    local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.fashion_part_type, self.fashion_index)
    if fashion_cfg == nil then
        return
    end
    
    local skill_cfg = NewAppearanceWGData.Instance:GetFashionSkillCfg(self.fashion_part_type, fashion_cfg.index, 1)
    if not skill_cfg then
        return
    end

    local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.fashion_part_type, self.fashion_index)
    local limit_text
	if not is_act then
        limit_text = Language.NewAppearance.SkillGradeActTips4
	end

    local view = ViewManager.Instance:GetView(GuideModuleName.NewAppearanceWGView)
    local parent_rect = view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.node_list["fs_skill"].transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    local capability = NewAppearanceWGData.Instance:GetSingleSkillCap(skill_cfg)
    local show_data = {
        skill_id = skill_cfg.skill_id,
		icon = skill_cfg.skill_icon,
		top_text = skill_cfg.skill_name,
		body_text = skill_cfg.skill_describe,
		limit_text = limit_text,
        set_pos2 = true,
        hide_next = true,
        is_lock = not is_act,
		x = -370,
		y = -100,
		capability = capability,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function NewAppearanceWGView:FashionOnClickUseSkill()
    NewAppearanceWGCtrl.Instance:SendFashionOperate(FASHION_OPERATE.SKILL_USE, self.fashion_part_type, self.fashion_index)
end

function NewAppearanceWGView:FashionOnClickRestSkill()
    NewAppearanceWGCtrl.Instance:SendFashionOperate(FASHION_OPERATE.SKILL_RESET, self.fashion_part_type, self.fashion_index)
end

function NewAppearanceWGView:FSRemindCallBack(remind_name, num)

end

function NewAppearanceWGView:OnClickOpenWardrobe()
	ViewManager.Instance:Open(GuideModuleName.WardrobeView)
end

function NewAppearanceWGView:OnClickOpenDyeColor()
	NewAppearanceDyeWGCtrl.Instance:OpenAppearanceDyeView(self.fashion_part_type, self.fashion_index)
end

function NewAppearanceWGView:FlushFSGridListCell()
    local grid_data = NewAppearanceWGData.Instance:GetSortShowListByTabIndexAndItemColor(self.show_index)

    for k, v in pairs(grid_data) do
        if self.fs_cell_list ~= nil and self.fs_cell_list[k] ~= nil then
            for i, u in ipairs(self.fs_cell_list[k]) do
                u:SetActive(nil ~= v[i])

                if nil ~= v[i] then
                    u:SetData(v[i])
                end
            end
        end
    end
end

function NewAppearanceWGView:SetFSGridItemSelect(param_t)
    local part_index = -1
    local fashion_id = -1

    if not IsEmptyTable(param_t) then
        part_index = tonumber(param_t.to_ui_name) or -1
        fashion_id = tonumber(param_t.to_ui_param) or -1
    end

    local try_select_tog_index = -1
    local force_select_color_type = -1
    local force_select_cell_index = -1
    local fashion_part_type = -1

    if part_index > 0 and fashion_id > 0 then
        local grid_data = NewAppearanceWGData.Instance:GetSortShowListByTabIndexAndItemColor(self.show_index)

        for k, v in pairs(grid_data) do
            for i, u in pairs(v) do
                if part_index == u.part_type and fashion_id == u.index then
                    force_select_color_type = u.color
                    self.fs_force_jump_cell_data = {part_index == u.part_type, fashion_id == u.index}
                    break
                end
            end
        end
    else
        try_select_tog_index, fashion_part_type = self:GetFSGridListTogSelect()
    end

    local select_tog_index = force_select_color_type > 0 and force_select_color_type or try_select_tog_index
    if self.fs_select_grid_type and select_tog_index ~= self.fs_select_grid_type then
        if self.node_list["fs_select_btn" .. self.fs_select_grid_type] then
            self.node_list["fs_select_btn" .. self.fs_select_grid_type].accordion_element.isOn = false
        end
    elseif self.fs_select_grid_type and select_tog_index == self.fs_select_grid_type and (self.fashion_part_type ~= fashion_part_type) then
        -- 不同页签同tog撑不开问题解决
        if self.node_list["fs_select_btn" .. select_tog_index] then
            self.node_list["fs_select_btn" .. select_tog_index].accordion_element:Refresh()
        end
    end

    if self.node_list["fs_select_btn" .. select_tog_index] and self.node_list["fs_select_btn" .. select_tog_index].accordion_element.isOn then
        self:OnClickFSSelectTogHandler(select_tog_index, true)
    else
        if self.node_list["fs_select_btn" .. select_tog_index] then
            self.node_list["fs_select_btn" .. select_tog_index].accordion_element.isOn = true
        end
    end
end

function NewAppearanceWGView:GetFSGridListTogSelect()
    local grid_data = NewAppearanceWGData.Instance:GetSortShowListByTabIndexAndItemColor(self.show_index)

    local fashion_part_type = -1
    local select_grid_type = -1
    local default_grid_type = -1
    
    for k, v in pairs(grid_data) do
        for i, u in pairs(v) do
            if fashion_part_type < 0 then
                fashion_part_type = u.part_type
            end

            if default_grid_type < 0 or u.color < default_grid_type then
                default_grid_type = u.color
            end

            if u.is_remind and (select_grid_type < 0 or u.color < select_grid_type) then
                select_grid_type = u.color
            end
        end
    end

    select_grid_type = select_grid_type > 0 and select_grid_type or default_grid_type

    if (nil == self.fs_select_grid_type) or ((fashion_part_type > 0) and (fashion_part_type ~= self.fashion_part_type)) then
        return select_grid_type, fashion_part_type
    else
        return self.fs_select_grid_type or select_grid_type, fashion_part_type
    end
end

function NewAppearanceWGView:OnClickFSSelectTogHandler(index, isOn)
	if index == nil or not isOn then
		return
	end

    local jump_cell_index = 1
    if self.fs_select_grid_type == index and self.fs_select_cell_index and self.fs_select_cell_index > 0 then
        jump_cell_index = self.fs_select_cell_index
    end

    self.fs_select_grid_type = index

    local grid_data = NewAppearanceWGData.Instance:GetSortShowListByTabIndexAndItemColor(self.show_index)
    local type_data = grid_data[index]

    -- 是否有固定跳转数据
    if not IsEmptyTable(self.fs_force_jump_cell_data) then
        if not IsEmptyTable(type_data) then
            for k,v in ipairs(type_data) do
                if self.fs_force_jump_cell_data.part_index == v.part_type and self.fs_force_jump_cell_data.fashion_id == v.index then
                    jump_cell_index = k
                    break
                end
            end
        end

        self.fs_force_jump_cell_data = nil
    else
        if not IsEmptyTable(type_data) then
            if not type_data[jump_cell_index] or not type_data[jump_cell_index].is_remind then
                for k,v in ipairs(type_data) do
                    if v.is_remind then
                        jump_cell_index = k
                        break
                    end
                end
            end
        end
    end

    if self.fs_cell_list ~= nil and self.fs_cell_list[index] ~= nil then
        if self.fs_cell_list[index][jump_cell_index].view.toggle.isOn then
            self:OnClickFSGridItem(self.fs_cell_list[index][jump_cell_index], jump_cell_index, true)
        else
            -- 这里延时0.1秒，父节点没激活情况下设置isOn不生效
            self:RemoveFSEffectDelayTimer()
            self.show_fs_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
                self.fs_cell_list[index][jump_cell_index].view.toggle.isOn = true
            end, 0.1)
        end
    end
end

--移除回调
function NewAppearanceWGView:RemoveFSEffectDelayTimer()
    if self.show_fs_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_fs_delay_timer)
        self.show_fs_delay_timer = nil
    end
end

function NewAppearanceWGView:OnClickFSGridItem(item, cell_index, is_on)
    if not is_on or not item or IsEmptyTable(item.data) then
        return
    end

    self.fs_select_cell_index = cell_index
    self:FashionOnItemSelectCB(item)
end

function NewAppearanceWGView:LoadFSGridListCell(load_info, load_complete_func)
    local res_async_loader = AllocResAsyncLoader(self, "appearance_fashion_item")

    res_async_loader:Load("uis/view/new_appearance_ui_prefab", "new_appearance_fs_item", nil,
    function(new_obj)
        for k, v in pairs(load_info) do
            if not self.fs_cell_list[v.index] then
                self.fs_cell_list[v.index] = {}
            end
        
            for i = v.start_num, v.end_num do
                local obj = ResMgr:Instantiate(new_obj)
                local obj_transform = obj.transform
                obj_transform:SetParent(self.fs_select_item_grid[v.index].list.transform, false)
                obj:GetComponent("Toggle").group = self.fs_select_item_grid[v.index].list.toggle_group
                local item_render = NewAppearanceFashionRender.New(obj)
                table.insert(self.fs_cell_list[v.index], item_render)
                obj:SetActive(false)
                obj:GetComponent("Toggle"):AddValueChangedListener(BindTool.Bind(self.OnClickFSGridItem, self, item_render, #self.fs_cell_list[v.index]))
    
                if k == #load_info and i == v.end_num then
                    if load_complete_func then
                        load_complete_func()
                    end
                end
            end
        end
    end)
end