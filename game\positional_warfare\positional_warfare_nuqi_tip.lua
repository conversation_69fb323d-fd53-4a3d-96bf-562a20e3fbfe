PositionalWarfareNuQiTip = PositionalWarfareNuQiTip or BaseClass(SafeBaseView)

function PositionalWarfareNuQiTip:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(14, 5), sizeDelta = Vector2(602, 408)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_nuqi_tip")
end

function PositionalWarfareNuQiTip:LoadCallBack()
    if not self.nuqi_item_list then
        self.nuqi_item_list = AsyncListView.New(PWNuQiItemCellRender, self.node_list.nuqi_item_list)
    end

    self.node_list.desc_tip.text.text = Language.PositionalWarfare.DescNuQiTiptitle
    self.node_list.desc_use_tip.text.text = Language.PositionalWarfare.DescNuQiTipContent
    XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind(self.Close, self))
end

function PositionalWarfareNuQiTip:ReleaseCallBack()
    if self.nuqi_item_list then
        self.nuqi_item_list:DeleteMe()
        self.nuqi_item_list = nil
    end
end

function PositionalWarfareNuQiTip:OnFlush()  
    local drag_item_list = PositionalWarfareWGData.Instance:GetTiredDragItemList()
    local data_list = {}
    
    if not IsEmptyTable(drag_item_list) then
        for k, v in pairs(drag_item_list) do
            local item_num = ItemWGData.Instance:GetItemNumInBagById(v)
            table.insert(data_list, {item_id = v, num = item_num})
        end
    end
    
    self.nuqi_item_list:SetDataList(data_list)
end

-----------------------------------------------------------
PWNuQiItemCellRender = PWNuQiItemCellRender or BaseClass(BaseRender)

function PWNuQiItemCellRender:LoadCallBack()
    if not self.item then
        self.item = PWNuQiItemCell.New(self.node_list.item_pos)
    end
end

function PWNuQiItemCellRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function PWNuQiItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item:SetData(self.data)
    self.node_list.item_name.text.text = ItemWGData.Instance:GetItemName(self.data.item_id)
end

-----------------------PWNuQiItemCell-----------------------
PWNuQiItemCell = PWNuQiItemCell or BaseClass(ItemCell)

function PWNuQiItemCell:OnFlush()
    ItemCell.OnFlush(self)

    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
    self:SetRightBottomColorText(item_num)
    self:SetRightBottomTextVisible(true)
end

function PWNuQiItemCell:OnClick()
    if self.tip_callback ~= nil then
		local is_black = self.tip_callback(self.data)
		if is_black == true then
			return
		end
	end

	if self.data and self.is_showtip then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if nil == item_cfg then return end

		TipWGData.Instance:SetDefShowBuyCount(self.show_buy_num or 1)
		if self.need_item_get_way then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
		else
            local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)

            if item_num > 0 then
                local btn_callback_event = {}
                btn_callback_event[1] = {btn_text = Language.Tip.ButtonLabel[2], callback = function()
                    OfflineRestWGCtrl.Instance:OpenUserOfflineView(self.data.item_id)
                end}

                 TipWGCtrl.Instance:OpenItem(self.data, self.item_tip_from or ItemTip.FROM_NORMAL, nil, nil, btn_callback_event)
            else
                TipWGCtrl.Instance:OpenItem(self.data, self.item_tip_from or ItemTip.FROM_NORMAL, nil, nil, self.item_tips_btn_click_callback)
            end
		end
	end
	BaseRender.OnClick(self)
end