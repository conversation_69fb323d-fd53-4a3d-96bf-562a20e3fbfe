LongXiWGData = LongXiWGData or BaseClass()

function LongXiWGData:__init()
	if LongXiWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[LongXiWGData] attempt to create singleton twice!")
		return
	end
	LongXiWGData.Instance = self

	self.longxi_cfg = ConfigManager.Instance:GetAutoConfig("longxi_cfg_auto")
	self.longxi = ListToMap(self.longxi_cfg.longxi, "seq")
	self.level_cfg = ListToMap(self.longxi_cfg.level, "seq", "level")
	self.other_cfg = self.longxi_cfg.other[1]
	self.task_cfg = self.longxi_cfg.task[1]

	self.auto_up_super_dragon_seal = false
	self.auto_up_dragon_king_token = false
	self.show_tip = true

	self.long_xi_data_list = {}
	self.cost_item_list = {}
	self:RecordLongXiUpDataCost()
	RemindManager.Instance:Register(RemindName.SuperDragonSeal, BindTool.Bind(self.GetSuperDragonSealRemind, self))
	RemindManager.Instance:Register(RemindName.DragonKingToken, BindTool.Bind(self.GetDragonKingTokenRemind, self))
end

function LongXiWGData:__delete()
	LongXiWGData.Instance = nil
	self.show_tip = nil

	RemindManager.Instance:UnRegister(RemindName.SuperDragonSeal)
	RemindManager.Instance:UnRegister(RemindName.DragonKingToken)
end

function LongXiWGData:RecordLongXiUpDataCost()
	local cost_item_list = {}
	for k, v in pairs(self.level_cfg) do
		for i, k in pairs(v) do
			if IsEmptyTable(cost_item_list[k.seq]) then
				cost_item_list[k.seq] = {}
			end

			if nil == cost_item_list[k.seq][k.cost_item_id] then
				cost_item_list[k.seq][k.cost_item_id] = k.cost_item_id
			end
		end
	end
	self.cost_item_list = cost_item_list
end

function LongXiWGData:GetSuperDragonSealRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.SuperDragonSeal) then
		return 0
	end

	-- 策划要求没点进来之前需要展示一次红点  点一次后消失
	if self:GetOpraterFlag(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL) then
		return 1
	end


	if self:CaluclationCost(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL) then
		return 1
	end

	return 0
end

function LongXiWGData:GetDragonKingTokenRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.DragonKingToken) then
		return 0
	end

	-- 策划要求没点进来之前需要展示一次红点  点一次后消失
	if self:GetOpraterFlag(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN) then
		return 1
	end

	if self:CaluclationCost(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN) then
		return 1
	end

	local data = self:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
	local task_active = self:GetLongXiTaskInfo()
	if data.is_active < 1 and task_active then
		return 1
	end
	return 0
end

function LongXiWGData:CaluclationCost(longxi_type)
	if not IsEmptyTable(self.long_xi_data_list[longxi_type]) then
		local data = self.long_xi_data_list[longxi_type]
		local len = not IsEmptyTable(self.level_cfg[longxi_type]) and #self.level_cfg[longxi_type] or 0
		local is_max_level = data.level >= len
		if data.is_active ~= 0 and not is_max_level then
			local cost_cfg = self:GetLongXiLevelCfg(longxi_type, data.level)
			if not IsEmptyTable(cost_cfg) then
				local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.cost_item_id)
				if item_num >= cost_cfg.cost_item_num then
					return true
				end
			end
		end
	end

	return false
end

function LongXiWGData:SetOpraterFlag(longxi_type)
	local uuid = RoleWGData.Instance:GetUUIDStr()
	local token_key = "longxi_activity_type_dragon_king_token" .. uuid
	local seal_key = "longxi_activity_type_super_dragon_seal" .. uuid

	if longxi_type == LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN then
		PlayerPrefsUtil.SetInt(token_key, 1)
	else
		PlayerPrefsUtil.SetInt(seal_key, 1)
	end
end

function LongXiWGData:GetOpraterFlag(longxi_type)
	local uuid = RoleWGData.Instance:GetUUIDStr()
	local token_key = "longxi_activity_type_dragon_king_token" .. uuid
	local seal_key = "longxi_activity_type_super_dragon_seal" .. uuid
	
	if longxi_type == LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN then	
		return PlayerPrefsUtil.GetInt(token_key) == 0 
	else
		return PlayerPrefsUtil.GetInt(seal_key) == 0 
	end
end

function LongXiWGData:GetLongXINoticeFlag()
	local dragon_king_token_flag = self:GetOpraterFlag(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
	local super_dragon_seal_flag = self:GetOpraterFlag(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)

	if not dragon_king_token_flag or not super_dragon_seal_flag then
		return false
	end

	local role_vo = RoleWGData.Instance:GetRoleVo()
	if self.other_cfg.disappear_lv < role_vo.level then
		return false
	end
	
	return true
end

function LongXiWGData:SetLongXiItemInfo(protocol)
	for k, v in pairs(protocol.item_list) do
		self.long_xi_data_list[k] = v
	end
end

function LongXiWGData:UpdateLongXiItem(protocol)
	self.long_xi_data_list[protocol.seq] = protocol.item
end

function LongXiWGData:GetLongXiDataByType(longxi_activiti_type)
	return self.long_xi_data_list[longxi_activiti_type]
end

function LongXiWGData:CheckIsDragonKingTokenItem(change_item_id)
	local cost_item_list = ((self.cost_item_list or {})[LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN] or {})
	return not IsEmptyTable(cost_item_list) and nil ~= cost_item_list[change_item_id]
end

function LongXiWGData:CheckIsSuperDragonSealItem(change_item_id)
	local cost_item_list = ((self.cost_item_list or {})[LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL] or {})
	return not IsEmptyTable(cost_item_list) and nil ~= cost_item_list[change_item_id]
end

function LongXiWGData:GetLongXiBaseInfoByType(longxi_activiti_type)
	return self.longxi[longxi_activiti_type]
end

function LongXiWGData:GetLongXiLevelInfoByType(longxi_activiti_type)
	local level_cfg = (self.long_xi_data_list or {})[longxi_activiti_type]
	if IsEmptyTable(level_cfg) or nil == level_cfg.level then
		return {}
	end

	return self:GetLongXiLevelCfg(longxi_activiti_type, level_cfg.level)
end

function LongXiWGData:SetBossKillCount(protocol)
	self.kill_world_boss_count = protocol.kill_world_boss_count
end

function LongXiWGData:GetBossKillCount()
	return self.kill_world_boss_count or 0
end

function LongXiWGData:GetTaskCfg()
	return self.task_cfg
end

--任务是否完成
function LongXiWGData:GetLongXiTaskInfo()
	local boss_count = self:GetBossKillCount()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.task_cfg.cost_item_id)
	if boss_count >= tonumber(self.task_cfg.need_kill_world_boss_num) and 
		role_level >= tonumber(self.task_cfg.need_level) and 
		item_num >= tonumber(self.task_cfg.cost_item_num) then
		return true
	end

	return false
end

--是否拥有祈福特权
function LongXiWGData:GetIsShowPrivilegeDesc()
	local base_data = self:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
	return base_data.have_qifu_tequan == 1
end

--是否显示主界面提醒
function LongXiWGData:GetIsShowMainViewTips()
	local base_data = self:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
	return base_data.show_mainicon == 1
end

 -- 策划要求 要拿属性五做个假技能显示
function LongXiWGData:GetSkillInfo(longxi_activiti_type)
	local base_info = self.longxi[longxi_activiti_type]
	local data_info = self.long_xi_data_list[longxi_activiti_type]
	local attr = {}
	local attr_id = 5

	--未激活
	if data_info.is_active == 0 then
		attr = base_info
	else
		attr = self:GetLongXiLevelCfg(longxi_activiti_type, data_info.level)
	end

	local attr_list, capability = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(attr, "attr_id", "attr_value")

	-- 2022/03/09 修改 策划嫌弃技能描述过低，要拿固定描述做显示
	-- local cur_skill_desc = ""
	-- if not IsEmptyTable(attr_list[attr_id]) then
	-- 	cur_skill_desc =  attr_list[attr_id].attr_name .. " " .. ToColorStr(attr_list[attr_id].value_str, COLOR3B.D_GREEN) 	
	-- end
	local cur_skill_desc = self.longxi[longxi_activiti_type].skill_des

	local next_skill_desc = ""
	local limit_text = ""
	local real_limit_text = ""
	--local show_next_desc = data_info.is_active == 1 and data_info.level < #self.level_cfg[longxi_activiti_type]

	-- if show_next_desc then
	-- 	local next_attr_list = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(self.level_cfg[longxi_activiti_type][data_info.level + 1], "attr_id", "attr_value","attr_name", "value_str", attr_id, attr_id, true)
	-- 	if not IsEmptyTable(next_attr_list) then
	-- 		next_skill_desc = next_attr_list[1].attr_name .. " " .. ToColorStr(next_attr_list[1].value_str, COLOR3B.D_GREEN) 
	-- 	end
	-- end

	local show_data = {
		x = 0,
		y = 0, 
		icon = base_info.skill_ico, 
		top_text = base_info.skill_name, 
		hide_level = true,
		capability = capability, 
		next_skill_desc = next_skill_desc, 
		body_text = cur_skill_desc, 
		set_pos2 = true, 
		real_limit_text = real_limit_text, 
		skill_level = data_info.is_active == 0 and 1 or data_info.level, 
		limit_text = limit_text
	}

	return show_data
end

function LongXiWGData:GetUpLevelAttrInfo(longxi_activiti_type)
	local skill_attr_id = 5
	local data_info = self.long_xi_data_list[longxi_activiti_type]
	local is_max_level = data_info.level >= #self.level_cfg[longxi_activiti_type]
	local data_list = self:GetLongXiLevelCfg(longxi_activiti_type, data_info.level)
	local attr_list_info = {}

	local attr_list, capability = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(data_list, "attr_id", "attr_value", "attr_name", "value_str", 1, 5, true)
	local skill_info = {}

	if not IsEmptyTable(attr_list[skill_attr_id]) then
		skill_info = {attr_name = attr_list[skill_attr_id].attr_name, value_str = attr_list[skill_attr_id].value_str}
	end

	if is_max_level then
		attr_list_info = attr_list
	else
		local next_level_data = {}
		local next_level_data_attr = self:GetLongXiLevelCfg(longxi_activiti_type, data_info.level + 1)

		if not IsEmptyTable(next_level_data_attr) then
			next_level_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(next_level_data_attr, "attr_id", "attr_value", "attr_name", "value_str", 1, 5, true)
		end

		for k, v in pairs(attr_list) do
		     local data = {}
			 data.attr_name = v.attr_name
			 data.value_str = v.value_str
			 data.add_value = ((next_level_data[k] or {}).value_str or 0) - v.value_str
			 attr_list_info[k] = data
		end
	end

	table.remove(attr_list_info, skill_attr_id)
	return is_max_level, attr_list_info, capability, skill_info
end

function LongXiWGData:GetLongXiCountDownTime()
	local count_down_time = 24 * 60 * 60
	local role_vo = RoleWGData.Instance:GetRoleVo()
	local create_times = TimeWGCtrl.Instance:GetServerTime() - role_vo.create_timestamp
	return count_down_time - create_times
end

function LongXiWGData:GetAutoUpLevelStateByType(longxi_type)
	if longxi_type == LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN then
		return self.auto_up_dragon_king_token
	else
		return self.auto_up_super_dragon_seal
	end
end

function LongXiWGData:SetAutoUpLevelStateByType(longxi_type, state)
	if longxi_type == LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN then	
		self.auto_up_dragon_king_token = state
	else
		self.auto_up_super_dragon_seal = state
	end
end

function LongXiWGData:GetLongXiLevelCfg(longxi_type, level)
	return (self.level_cfg[longxi_type] or {})[level] or {}
end

function LongXiWGData:GetRMBByAddRechargeFlag()
	local other_config = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1]
	local flag = other_config.rmb_buy_add_chongzhi or 0
	return flag ~= 0
end

function LongXiWGData:SetIsShowTip(state)
	self.show_tip = state
end

function LongXiWGData:GetIsShowTip()
	local is_show = false
	local data = self:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
	local is_show_main = self:GetIsShowMainViewTips()
    if is_show_main and data and data.is_active < 1 and self.show_tip then
    	is_show = true
    end

	return is_show
end