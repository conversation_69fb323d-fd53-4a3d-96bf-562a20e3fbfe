SiXiangTeDianWGData = SiXiangTeDianWGData or BaseClass()

MAX_YUAN_SHEN_ZHAO_HUAN_SUBACT_ID = 6   --元神召唤特典子活动最大id
MAX_YUAN_SHEN_ZHAO_HUAN_SUBACT_SALE_PRODUCT_NUM = 64 --元神召唤特典每次开启每个子活动最大售卖商品个数

function SiXiangTeDianWGData:__init()
    if SiXiangTeDianWGData.Instance then
        error("[SiXiangTeDianWGData]:Attempt to create singleton twice!")
        return
    end
    SiXiangTeDianWGData.Instance = self
    self:InitParam()
    self:InitCfgData()
end

function SiXiangTeDianWGData:__delete()
    
    SiXiangTeDianWGData.Instance = nil
end

function SiXiangTeDianWGData:InitParam()
	self.te_dian_status = 0				-- 特典是否开启中 0-未开启 1-开启中
	self.te_dian_open_times = 0			-- 开启的次数 （对特典实际开启次数做了循环步长 取余操作得到的）
	self.te_dian_end_time = 0			-- 本次特典的结束时间戳or下一次特典开启时间戳
	self.te_dian_subact_info = {}		-- 子活动售卖状态
	self.shilian_show_id_list = nil
end

function SiXiangTeDianWGData:InitCfgData()
	local summon_config = ConfigManager.Instance:GetAutoConfig("yuanshengzhaohuan_auto")

	self.other_cfg = summon_config.other and summon_config.other[1]

	self.client_sub_act_map = ListToMap(summon_config.client_sub_act, "cycle", "act_id")

	-- 构造周期子活动数据
	self:MakeDataCycleSubActivity(summon_config.cycle_subactivity)
	-- 构造概率提升展示数据
	self:MakeDataProbabilityUp(summon_config.probability_up_show)
	-- 构造子活动售卖数据
	self.sub_act_sale_map = ListToMapList(summon_config.subactivity_sale, "cycle", "subactivity_id")
end

function SiXiangTeDianWGData:MakeDataProbabilityUp(cfg_list)
	local sixiang_cfg = nil
	local sixiang_type = 1
	local sixiang_pro_up_list = {}
	local hungu_pro_up_list = {}

	for i=1,#cfg_list do
		sixiang_cfg, sixiang_type = SiXiangCallWGData.Instance:GetSiXiangItemCfg(cfg_list[i].item_id)
		if sixiang_type == FIGHT_SOUL_ITEM_TYPE.SOUL then
			sixiang_pro_up_list[#sixiang_pro_up_list + 1] = cfg_list[i]
		elseif sixiang_type == FIGHT_SOUL_ITEM_TYPE.BONE then
			hungu_pro_up_list[#hungu_pro_up_list + 1] = cfg_list[i]
		end
	end

	self.soul_pro_up_map = ListToMapList(sixiang_pro_up_list, "cycle")
	self.bone_pro_up_map = ListToMapList(hungu_pro_up_list, "cycle")
	-- self.pro_up_cycle_map = ListToMapList(cfg_list, "cycle")
end

function SiXiangTeDianWGData:MakeDataCycleSubActivity(cfg_list)
	local map_list = {}
	for i=1,#cfg_list do
		local split_tab = Split(cfg_list[i].start_subactivitys, "|")
		local temp_list = {}
		for j=1,#split_tab do
			temp_list[j] = tonumber(split_tab[j])
		end
		map_list[cfg_list[i].cycle] = temp_list
	end
    self.cycle_subactivity_map = map_list
end

---[[ 后端发的数据
function SiXiangTeDianWGData:SetSiXiangTeDianInfo(protocol)
	self.te_dian_status = protocol.te_dian_status
    self.te_dian_open_times = protocol.open_times
    self.te_dian_end_time = protocol.end_time
    self.te_dian_subact_info = protocol.te_dian_subact_info
end

-- 获取是否开启特典
function SiXiangTeDianWGData:GetIsOpenTeDian()
	return self.te_dian_status == 1
end

-- 获取开启次数
function SiXiangTeDianWGData:GetCycle()
	return self.te_dian_open_times
end

-- 获取本次特典的结束时间戳or下一次特典开启时间戳
function SiXiangTeDianWGData:GetTeDianEndTimeStamp()
    return self.te_dian_end_time
end

-- 获取子活动售卖状态
function SiXiangTeDianWGData:GetSubActSaleInfo(act_id, product_id)
	if self.te_dian_subact_info[act_id] then
		return self.te_dian_subact_info[act_id][product_id]
	end
end
--]]

-- 获取其它配置
function SiXiangTeDianWGData:GetOtherCfg(key)
	if self.other_cfg and key then
		return self.other_cfg[key]
	end
end

-- 获取十连展示物品id列表
function SiXiangTeDianWGData:GetShilianShowIdList()
	if not self.shilian_show_id_list then
		local show_cfg = SiXiangTeDianWGData.Instance:GetOtherCfg("show")
		if not show_cfg then
			return
		end
		local id_list = Split(show_cfg, ",")
		local temp_list = {}
		for i=1,#id_list do
			temp_list[i] = tonumber(id_list[i])
		end
		self.shilian_show_id_list = temp_list
	end
	return self.shilian_show_id_list
end

-- 获取特典概率提升cfg
function SiXiangTeDianWGData:GetProUpCfgList(_type)
	local cycle = self:GetCycle()
	if _type == FIGHT_SOUL_ITEM_TYPE.SOUL then
		return self.soul_pro_up_map[cycle] or {}
	elseif _type == FIGHT_SOUL_ITEM_TYPE.BONE then
		return self.bone_pro_up_map[cycle] or {}
	end
end

-- 获取子活动售卖cfg_list
function SiXiangTeDianWGData:GetActSaleCfg(act_id)
	local cycle = self:GetCycle()
	if self.sub_act_sale_map[cycle] then
		return self.sub_act_sale_map[cycle][act_id]
	end
end

--获取客户端显示活动配置
function SiXiangTeDianWGData:GetClientShowCfg(act_id)
    local cycle = self:GetCycle()
    if self.client_sub_act_map[cycle] then
    	return self.client_sub_act_map[cycle][act_id]
    end
end

-- 获取显示的活动列表(买完领完后就移除)
function SiXiangTeDianWGData:GetShowActIdList()
	local cycle = self:GetCycle()
	local act_list = self.cycle_subactivity_map[cycle]
	local show_list = {}

	if act_list then
		local is_show = false
		for i,act_id in ipairs(act_list) do
			if act_id == YuanShenZhaoHuanSubActId.ProbabilityUp then
				is_show = self:GetIsOpenTeDian()
			elseif act_id == YuanShenZhaoHuanSubActId.ShiLian2 then
				if self:IsAllBuyAndGetReward(YuanShenZhaoHuanSubActId.ShiLian1) then
					is_show = not self:IsAllBuyAndGetReward(act_id)
				else
					is_show = false
				end
			else
				is_show = not self:IsAllBuyAndGetReward(act_id)
			end
			if is_show then
				show_list[#show_list + 1] = act_id
			end
		end
	end

	return show_list
end

-- 该活动的所有商品都买完了且领取了奖励
function SiXiangTeDianWGData:IsAllBuyAndGetReward(act_id)
	local cfg_list = self:GetActSaleCfg(act_id)
	if cfg_list then
		local act_info = nil
		for i=1,#cfg_list do
			act_info = self:GetSubActSaleInfo(act_id, cfg_list[i].product_id)
			if act_info and (act_info.status ~= YuanShenSaleSubActSaleStatus.HasBuyAndFetched or act_info.buy_num < cfg_list[i].limit_buy_times) then
				return false
			end
		end
		return true
	end
end

-- 检测某个特典活动是否开启
function SiXiangTeDianWGData:CheckSubActIsOpen(act_id)
	local cycle = self:GetCycle()
	local act_list = self.cycle_subactivity_map[cycle]
	for i,id in ipairs(act_list) do
		if id == act_id then
			return true
		end
	end
end

-- 获取当前十连
function SiXiangTeDianWGData:GetCurShiLian()
	local act_id = YuanShenZhaoHuanSubActId.ShiLian1
	if self:CheckSubActIsOpen(act_id) and not self:IsAllBuyAndGetReward(act_id) then
		return act_id
	elseif self:CheckSubActIsOpen(YuanShenZhaoHuanSubActId.ShiLian2) then
		return YuanShenZhaoHuanSubActId.ShiLian2
	end
	return act_id
end

-- 获取当前甄选
function SiXiangTeDianWGData:GetCurZhenXuan()
	local act_id = YuanShenZhaoHuanSubActId.ZhenXuan1
	if self:CheckSubActIsOpen(act_id) and not self:IsAllBuyAndGetReward(act_id) then
		return act_id
	elseif self:CheckSubActIsOpen(YuanShenZhaoHuanSubActId.ZhenXuan2) then
		return YuanShenZhaoHuanSubActId.ZhenXuan2
	end
	return act_id
end