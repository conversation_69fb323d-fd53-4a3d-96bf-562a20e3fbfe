SingleRechargeWgData = SingleRechargeWgData or BaseClass()

function SingleRechargeWgData:__init()
    if SingleRechargeWgData.Instance then
        error("[SingleRechargeWgData]:Attempt to create singleton twice!")
        return
    end
    SingleRechargeWgData.Instance = self
    self:InitCfgData()
    self:InitParam()
end

function SingleRechargeWgData:__delete()
    
    SingleRechargeWgData.Instance = nil
    self.danwei_data__list = nil
end

function SingleRechargeWgData:InitCfgData()
    local single_charge_cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").single_charge
    local day_cfg = ListToMap(single_charge_cfg,"opengame_day")
    local danwei_cfg = ListToMap(single_charge_cfg, "opengame_day","seq")

    local data_list = {}
    for k, v in pairs(day_cfg) do
        data_list[v.opengame_day] = {}
        data_list[v.opengame_day].opengame_day = v.opengame_day
        data_list[v.opengame_day].data_list = {}
    end
   
    for k, v in pairs(danwei_cfg) do
        data_list[k].data_list = v
    end

    self.danwei_cfg = data_list
end

function SingleRechargeWgData:InitParam()
end

function SingleRechargeWgData:SetListData()
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_SINGLE_CHARGE)
    if activity_info == nil then
        self.danwei_data__list = nil
        return
    end

    local day_limit_id = 1
    local sign_fetch = 1
    local sign_can_fetch = 1
    local start_day = TimeWGCtrl.Instance:GetOpenServerDayByTimestamp(activity_info.start_time)
    -- 处理self.danwei_cfg 未按照天数排序  1- <7 拿第一天档   7- <14 拿第七天档   14- <21 拿14天档   21-num拿 21天档
    for k, v in pairs(self.danwei_cfg) do
        if start_day >= v.opengame_day and v.opengame_day >= day_limit_id then
            day_limit_id = v.opengame_day
        end
    end

    local data_list = {}
    local target_data_list = self.danwei_cfg[day_limit_id].data_list

    for k ,v in pairs(target_data_list) do
        local item_data = {}
        item_data.seq = v.seq
        item_data.charge_value = v.charge_value
        item_data.reward_item = OperationActivityWGData.Instance:SortDataByItemColor(v.reward_item)
        item_data.can_fetch_flag = self.reward_can_fetch_flag[32 - v.seq]
        item_data.fetch_flag = self.reward_fetch_flag[32 - v.seq]
        if item_data.fetch_flag == 1 then
            table.insert(data_list, sign_fetch, item_data)
        elseif item_data.can_fetch_flag == 1 then
            table.insert(data_list, sign_can_fetch, item_data)
            sign_can_fetch = sign_can_fetch + 1
            sign_fetch = sign_fetch + 1
        else
            table.insert(data_list, sign_fetch, item_data)
            sign_fetch = sign_fetch + 1
        end
    end

    self.danwei_data__list = data_list
end

function SingleRechargeWgData:GetSingleRechargeListData()
    if self.danwei_data__list == nil then
        self:SetListData()
    end

    return self.danwei_data__list or {}
end

function SingleRechargeWgData:SetSingleRechargeListData(protocol)
    self.reward_can_fetch_flag = bit:d2b(protocol.reward_can_fetch_flag)
    self.reward_fetch_flag = bit:d2b(protocol.reward_fetch_flag)
    self:SetListData()
end