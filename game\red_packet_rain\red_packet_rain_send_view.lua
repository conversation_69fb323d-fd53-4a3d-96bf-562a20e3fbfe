-- 跨服红包天降 发红包界面
RedPacketRainSendView = RedPacketRainSendView or BaseClass(SafeBaseView)

function RedPacketRainSendView:__init()
	self.is_safe_area_adapter = true
    self.view_style = ViewStyle.Half
    self:SetMaskBg(false, true)

    self:AddViewResource(0, "uis/view/red_packet_rain_ui_prefab", "layout_red_packet_rain_send")

end

function RedPacketRainSendView:ReleaseCallBack()
	if self.red_packet_item_list then
		for k, v in pairs(self.red_packet_item_list) do
			v:DeleteMe()
		end
		self.red_packet_item_list = nil
	end
end

function RedPacketRainSendView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_send, BindTool.Bind(self.OnClickBtnSend, self))
    XUI.AddClickEventListener(self.node_list.btn_cost, BindTool.Bind(self.OnClickSetCost, self))
    XUI.AddClickEventListener(self.node_list.tips_check, BindTool.Bind(self.OnClickCheck, self))
    
end

function RedPacketRainSendView:OnClickCheck(is_on)
    local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	-- --是否勾选今日不在展示提示框 :1(勾选) 0(未勾选)
	PlayerPrefsUtil.SetInt("red_packet_rain_send" .. role_id .. cur_day, is_on and 1 or 0)
end


function RedPacketRainSendView:ShowIndexCallBack()
    self.node_list.text_cost.text.text = 0
    self.cost_num = 0
end

function RedPacketRainSendView:OnFlush()
    local role_info = RedPacketRainWGData.Instance:GetRoleInfo()
    local add_count = 0

    if role_info and role_info.add_round then
        add_count = role_info.add_round
    end
    self.node_list.text_add_count.text.text = string.format(Language.RedPacketRain.AddCount, add_count, RedPacketRainWGData.Instance:GetOtherCfg("add_max"))

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	-- --是否勾选今日不在展示提示框 :1(勾选) 0(未勾选)
	local value = PlayerPrefsUtil.GetInt("red_packet_rain_send" .. role_id .. cur_day)

    self.node_list.tips_check.toggle.isOn = value == 1
end

function RedPacketRainSendView:OnClickBtnSend()
    -- local bind_gold = RoleWGData.Instance.role_info.bind_gold
	local gold = RoleWGData.Instance.role_info.gold

    local cost_num = self.cost_num or 0

    if  gold >= cost_num then
        RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ADD_ROUND, cost_num)
        RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ROLE_INFO)
        self:Close()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.RedPacketRain.CostTips)
    end
    
end

--点击设置总价
function RedPacketRainSendView:OnClickSetCost()

	local function callback(input_num)
		self.cost_num = input_num
        self.node_list.text_cost.text.text = input_num
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(0)
    -- local bind_gold = RoleWGData.Instance.role_info.bind_gold
	local gold = RoleWGData.Instance.role_info.gold
	num_keypad:SetMaxValue(gold)
	num_keypad:SetMinValue(0)
	num_keypad:SetOkCallBack(callback)
end



