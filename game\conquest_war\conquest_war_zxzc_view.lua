-- 诛仙世界
function ConquestWarView:LoadIndexCallBackZXZCiew()
	if not self.zxzc_reward_list then
		self.zxzc_reward_list = AsyncListView.New(ItemCell, self.node_list.zxzc_reward_list)
		self.zxzc_reward_list:SetStartZeroIndex(true)
	end

	--local reward_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBWinRewardList()
	--self.zxzc_reward_list:SetDataList(reward_cfg)
	XUI.AddClickEventListener(self.node_list.go_zxzc_btn, BindTool.Bind1(self.OnClickGoToZXZC, self))
	XUI.AddClickEventListener(self.node_list.btn_zxzc_reward_show, BindTool.Bind(self.OnClickZXZCRewardShow, self))

	self.zxzc_activity_change_callback = BindTool.Bind(self.OnFGBActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.zxzc_activity_change_callback)
end

function ConquestWarView:ReleaseZXZCView()
	if self.zxzc_reward_list then
		self.zxzc_reward_list:DeleteMe()
		self.zxzc_reward_list = nil
	end

	if self.zxzc_activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.zxzc_activity_change_callback)
		self.zxzc_activity_change_callback = nil
	end
end

function ConquestWarView:ZXZCShowIndexCallBack()

end

function ConquestWarView:OnClickZXZCRewardShow()
	ViewManager.Instance:Open(GuideModuleName.EternalNightRewardView)
end

function ConquestWarView:OnFlushZXZCView(param_t, index)
	local data = ConquestWarWGData.Instance:GetConquestWarActivityInfoById(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF)
	if IsEmptyTable(data) then
		return
	end

	self.zxzc_reward_list:SetDataList(data.activity_item)
	self.node_list["txt_zxzc_title"].text.text = data.activity_title
	self.node_list["txt_zxzc_open_time"].text.text = string.format(Language.FlagGrabbingBattlefield.ActivityTime1, data.time_1, data.time_2)
	self.node_list["txt_zxzc_desc"].text.text = data.activity_illustrate

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF)
	if not activity_info then return end
	self:FlushZXZCBtnState(activity_info.status)
end

function ConquestWarView:OnClickGoToZXZC()
	local info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF)
	if not info then return end
	
	if info.status ~= ACTIVITY_STATUS.OPEN and info.status ~= ACTIVITY_STATUS.STANDY then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.NotOpenAct))
		return
	end

	local open_level = KuafuYeZhanWangChengWGData.Instance:GetLimitLevel()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < open_level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FlagGrabbingBattlefield.LevelLimitJoin, RoleWGData.GetLevelString2(open_level)))
		return
	end

	MainuiWGCtrl.Instance:ClickEnterActivity(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF)
end

function ConquestWarView:OnZXZCActChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF and self:IsOpen() then
		self:FlushZXZCBtnState(status)
	end
end

function ConquestWarView:FlushZXZCBtnState(status)
	if status == ACTIVITY_STATUS.STANDY then
		self.node_list["go_zxzc_btn"]:CustomSetActive(true)
		self.node_list["go_zxzc_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Standy
	elseif status == ACTIVITY_STATUS.OPEN then
		self.node_list["go_zxzc_btn"]:CustomSetActive(true)
		self.node_list["go_zxzc_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Doing
	else
		self.node_list["go_zxzc_btn"]:CustomSetActive(false)
		self.node_list["go_zxzc_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.NotOpenDesc
	end
end
