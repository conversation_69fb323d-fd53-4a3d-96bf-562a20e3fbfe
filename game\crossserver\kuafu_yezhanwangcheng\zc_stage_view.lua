ZCStageView = ZCStageView or BaseClass(SafeBaseView)
ZCStageView.FromView = {
    YZWC = 10,
}

function ZCStageView:__init()
    self:AddViewResource(0, "uis/view/zc_result_ui_prefab", "zc_switch_stage_view")
end

function ZCStageView:__delete()

end

function ZCStageView:ReleaseCallBack()
    self:CancelTween()
end

function ZCStageView:OpenIndexCallBack()
    local cur_turn = KuafuYeZhanWangChengWGData.Instance:GetCurTurn()
    self:ShowTurnChange(cur_turn)
end

function ZCStageView:ShowTurnChange(turn)
    local cur_turn = turn
    if cur_turn == -1 then--默认值-1，切换到1
        cur_turn = 0
    end
    cur_turn = cur_turn + 1--提前1s打开，cur_turn还是上一轮的数据

    if cur_turn > 3 then
        self:Close()
        return
    end
    
    self.node_list["chapter_num"].text.text = Language.YeZhanWangCheng.NumJu[cur_turn]
    self.timer_index = 1
    self.timer_title_index = 1
    local chaper_title = Language.YeZhanWangCheng.NumJuDes[cur_turn]
    local asset_name, bundle_name = ResPath.GetF2ZCResultImg("a3_zzjd_title" .. cur_turn)
    self.node_list["chapter_name"].image:LoadSprite(asset_name,bundle_name,function ()
        self.node_list["chapter_name"].image:SetNativeSize()
    end)

    self:CancelTween()
    local show_tweener = DG.Tweening.DOTween.Sequence()
    show_tweener:Append(self.node_list.chapter_name.canvas_group:DoAlpha(0, 1, 2))
    show_tweener:OnComplete(
        function()
           self:Close()
        end
    )

    self.enter_play_tween = show_tweener
end

function ZCStageView:CancelTween()
    if self.enter_play_tween then
        self.enter_play_tween:Kill()
        self.enter_play_tween = nil
    end
end
