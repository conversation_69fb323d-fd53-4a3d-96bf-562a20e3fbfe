﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ActorAnimatorSyncterWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>ginClass(typeof(ActorAnimatorSyncter), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("SyncAnimationImmediate", SyncAnimationImmediate);
		<PERSON><PERSON>unction("SetDrawPartAnimator", SetDrawPartAnimator);
		<PERSON><PERSON>ction("RemoveDrawPartAnimator", RemoveDrawPartAnimator);
		<PERSON><PERSON>RegFunction("ClearSyncTargetAnimatorData", ClearSyncTargetAnimatorData);
		L.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SyncAnimationImmediate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorAnimatorSyncter obj = (ActorAnimatorSyncter)ToLua.CheckObject<ActorAnimatorSyncter>(L, 1);
			UnityEngine.Animator arg0 = (UnityEngine.Animator)ToLua.CheckObject<UnityEngine.Animator>(L, 2);
			obj.SyncAnimationImmediate(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDrawPartAnimator(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorAnimatorSyncter obj = (ActorAnimatorSyncter)ToLua.CheckObject<ActorAnimatorSyncter>(L, 1);
			UnityEngine.Animator arg0 = (UnityEngine.Animator)ToLua.CheckObject<UnityEngine.Animator>(L, 2);
			obj.SetDrawPartAnimator(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveDrawPartAnimator(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorAnimatorSyncter obj = (ActorAnimatorSyncter)ToLua.CheckObject<ActorAnimatorSyncter>(L, 1);
			UnityEngine.Animator arg0 = (UnityEngine.Animator)ToLua.CheckObject<UnityEngine.Animator>(L, 2);
			obj.RemoveDrawPartAnimator(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearSyncTargetAnimatorData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorAnimatorSyncter obj = (ActorAnimatorSyncter)ToLua.CheckObject<ActorAnimatorSyncter>(L, 1);
			obj.ClearSyncTargetAnimatorData();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

