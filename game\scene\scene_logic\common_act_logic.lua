-------------------------------------------
--基础活动逻辑,所有活动逻辑都继承这个
--<AUTHOR>
--------------------------------------------
CommonActivityLogic = CommonActivityLogic or BaseClass(BaseFbLogic)

function CommonActivityLogic:__init()

end

function CommonActivityLogic:__delete()
	if self.main_role_relive_event then
		GlobalEventSystem:UnBind(self.main_role_relive_event)
	    self.main_role_relive_event = nil
	end
end

function CommonActivityLogic:Enter(old_scene_type, new_scene_type)
	BaseFbLogic.Enter(self, old_scene_type, new_scene_type)
	MainuiWGCtrl.Instance:ShowMainuiMenu(false)
	FuBenWGData.Instance:SetFuBenInitiativeToLeave(new_scene_type, nil)
	self.main_role_relive_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_REALIVE, BindTool.Bind1(self.OnMainRoleRelive, self))
end

--退出
function CommonActivityLogic:Out()
	BaseFbLogic.Out(self)
	MainuiWGCtrl.Instance:ShowMainuiMenu(true) 
	if self.main_role_relive_event then
		GlobalEventSystem:UnBind(self.main_role_relive_event)
   		self.main_role_relive_event = nil
   	end
end

-- 所有活动默认复活就挂机，不需要复活的，可以重写这个方法
function CommonActivityLogic:OnMainRoleRelive()
    --self.curr_jump = nil
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

-- 活动时间
function CommonActivityLogic:OpenActivitySceneCd(act_type, not_show_end)

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if nil ~= activity_info then
		local flag = activity_info.status == ACTIVITY_STATUS.STANDY 
		if flag then
			FuBenPanelWGCtrl.Instance:SetCountDowmType( nil ,GameEnum.FU_BEN_ZHUNBEI )
		end
		
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time,flag,flag and GameEnum.FU_BEN_ZHUNBEI or GameEnum.FU_BEN_OUT_SCENE, not_show_end)
	end

end

function CommonActivityLogic:OnClickHeadHandler(is_show)
	BaseFbLogic.OnClickHeadHandler(self)
end