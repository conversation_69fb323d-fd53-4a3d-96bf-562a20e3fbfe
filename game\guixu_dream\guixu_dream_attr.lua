GuiXuDreamAttrView = GuiXuDreamAttrView or BaseClass(SafeBaseView)
function GuiXuDreamAttrView:__init()
	self:SetMaskBg(true)
	self:ClearViewTween()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self:AddViewResource(0, "uis/view/guixudream_ui_prefab", "layout_guixu_dream_attr")
end

function GuiXuDreamAttrView:LoadCallBack()
	-- if nil == self.role_model then
	-- 	self.role_model = RoleModel.New()
	-- 	local display_data = {
	-- 		parent_node = self.node_list["ph_display"],
	-- 		camera_type = MODEL_CAMERA_TYPE.BASE,
	-- 		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
	-- 		rt_scale_type = ModelRTSCaleType.M,
	-- 		can_drag = true,
	-- 	}
		
	-- 	self.role_model:SetRenderTexUI3DModel(display_data)
	-- 	-- self.role_model:SetUI3DModel(self.node_list["ph_display"].transform,
	-- 	-- 	self.node_list["EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
	-- 	self:AddUiRoleModel(self.role_model)
	-- end

	if not self.suit_part_grid then
		local bundle = "uis/view/guixudream_ui_prefab"
		local asset = "act_part_item"
		self.suit_part_grid = AsyncBaseGrid.New()
		self.suit_part_grid:CreateCells({
			col = 2,
			change_cells_num = 1,
			list_view = self.node_list.act_part_list,
			assetBundle = bundle,
			assetName = asset,
			itemRender = GuiXuDreamActPartRender
		})
		self.suit_part_grid:SetStartZeroIndex(false)
	end

	self.attr_item_list = {}

	XUI.AddClickEventListener(self.node_list.btn_wear_all, BindTool.Bind(self.OnClickWearAll, self)) --激活套装属性button
	XUI.AddClickEventListener(self.node_list.close_btn, BindTool.Bind(self.CloseTween, self))
	XUI.AddClickEventListener(self.node_list.close_mask_btn, BindTool.Bind(self.CloseTween, self))
end

function GuiXuDreamAttrView:ReleaseCallBack()
	-- if self.role_model then
	-- 	self.role_model:DeleteMe()
	-- 	self.role_model = nil
	-- end

	if self.suit_part_grid then
		self.suit_part_grid:DeleteMe()
		self.suit_part_grid = nil
	end

	if self.attr_item_list then
		for k, v in pairs(self.attr_item_list) do
			v.cell:DeleteMe()
		end
		self.attr_item_list = nil
	end
	UITween.CleanMoveAlphaShow(self.view_name)
end

function GuiXuDreamAttrView:CloseTween()
	ViewManager.Instance:FlushView(GuideModuleName.GuiXuDreamView, nil, "show_suit_btn")

	local tween_info = UITween_CONSTS.GuiXuDreamAttr
	UITween.MoveAlphaShow(self.view_name, self.node_list.tween_root, tween_info, BindTool.Bind(self.Close, self))
end

function GuiXuDreamAttrView:SetDataAndOpen(suit_seq)
	self.suit_seq = suit_seq
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function GuiXuDreamAttrView:ShowIndexCallBack()

end

function GuiXuDreamAttrView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if v.suit_seq then
			self.suit_seq = v.suit_seq
		end
	end

	if self.suit_seq == nil then
		return
	end

	self:FlushPanelView()
	-- self:FlushSuitModel()
end

function GuiXuDreamAttrView:FlushPanelView()
	local suit_data = GuiXuDreamWGData.Instance:GetGuiXuDreamSuitInfoBySuit(self.suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end

	self.node_list.name.text.text = suit_data.suit_name
	local act_btn = suit_data.act_part_num >= suit_data.total_part_num
	if suit_data.can_act then
		self.node_list["btn_wear_red"]:SetActive(true)
	else
		self.node_list["btn_wear_red"]:SetActive(false)
	end

	if act_btn then
		self.node_list["btn_wear_text"].text.text = Language.GuiXuDream.WearbtnText[1]
		XUI.SetButtonEnabled(self.node_list.btn_wear_all, true)
	else
		self.node_list["btn_wear_text"].text.text = Language.GuiXuDream.WearbtnText[2]
		if suit_data.can_act then
			XUI.SetButtonEnabled(self.node_list.btn_wear_all, true)
		else
			XUI.SetButtonEnabled(self.node_list.btn_wear_all, false)
		end
	end

	self.suit_part_grid:SetDataList(suit_data.part_list)
	self:FlushAttrList()
end

--数据属性列表
function GuiXuDreamAttrView:FlushAttrList()
	local attr_data = WardrobeWGData.Instance:GetAttrBySuit(self.suit_seq)

	for i, v in ipairs(attr_data) do
		if self.attr_item_list[i] then
			if self.attr_item_list[i].loaded_flag then
				self.attr_item_list[i].cell:SetData(v)
			end
		else
			local async_loader = AllocAsyncLoader(self, "guiXu_dream_attr" .. i)
			self.attr_item_list[i] = {}
			self.attr_item_list[i].loaded_flag = false
			async_loader:SetParent(self.node_list["attr_list"].transform)
			async_loader:Load("uis/view/guixudream_ui_prefab", "guixu_dream_attr_cell", function(obj)
				local cell = GuiXuDreamAttrRender.New(obj)
				cell:SetIndex(i)
				cell:SetData(v)
				self.attr_item_list[i].cell = cell
				self.attr_item_list[i].loaded_flag = true
			end)
		end
	end

	local active_num = #attr_data
	for i, v in ipairs(self.attr_item_list) do
		if v.loaded_flag then
			v.cell:SetActive(i <= active_num)
		end
	end
end

function GuiXuDreamAttrView:FlushSuitModel()
	if self.suit_seq == nil then
		return
	end
	local show_list = WardrobeWGData.Instance:GetActivationPartList(self.suit_seq)

	if IsEmptyTable(show_list) then
		return
	end

	--清理掉回调
	self.role_model:RemoveAllModel()

	self.is_foot_view = false
	self.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	self.mount_res_id = 0
	self.mount_action = ""

	local res_id, fashion_cfg
	for k, data in pairs(show_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				self.body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
			end
		elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
			fashion_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(MOUNT_LINGCHONG_APPE_TYPE.MOUNT,
				data.param1)
			if fashion_cfg then
				self.mount_res_id = fashion_cfg.appe_image_id
				self.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					self.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
			fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
			if fashion_cfg then
				self.mount_res_id = fashion_cfg.active_id
				self.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					self.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		end
	end

	local extra_role_model_data = {
		no_need_do_anim = true,
	}
	if self.mount_res_id > 0 then
		self.role_model:SetRoleResid(self.body_res_id, nil, extra_role_model_data)
	else
		self.role_model:SetRoleResid(self.body_res_id, function()
			if self.mount_res_id == 0 then
				if self.show_role_idel_ani then
					self.role_model:PlayRoleShowAction()
					self.show_role_idel_ani = false
				else
					self.role_model:PlayIdleAni()
				end
			end
		end, extra_role_model_data)
	end

	if self.mount_res_id > 0 then
		self.role_model:SetMountResid(self.mount_res_id)
		self.role_model:PlayStartAction(self.mount_action)
	end

	for k, v in pairs(show_list) do
		self:ShowModelByData(v)
	end
end

function GuiXuDreamAttrView:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end

	local res_id, fashion_cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce

			if data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
				self.role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.role_model:SetWeaponResid(res_id)
			end
		end
	end
end

function GuiXuDreamAttrView:OnClickWearAll()
	if self.suit_seq == nil then
		return
	end

	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end
	local part_list = suit_data.part_list

	if suit_data.total_part_num == suit_data.act_part_num then --所有已激活
		for k, v in pairs(part_list) do
			if v.state == REWARD_STATE_TYPE.FINISH then
				self:SendReqByData(v)
			end
		end
		TipWGCtrl.Instance:ShowSystemMsg(Language.GuiXuDream.WearTips)
	end

	if suit_data.can_act then --判断激活下一套装属性
		local need_num = suit_data.suit_less_need - suit_data.act_part_num
		for k, v in pairs(part_list) do
			if v.state == REWARD_STATE_TYPE.CAN_FETCH then
				WardrobeWGCtrl.Instance:SendWardrobeRequest(WARDROBE_OP_TYPE.WARDROBE_OPERATE_TYPE_ACTIVE_PART,
					self.suit_seq, v.part)
				need_num = need_num - 1
				if need_num == 0 then
					break
				end
			end
		end
	end
end

function GuiXuDreamAttrView:SendReqByData(data)
	if IsEmptyTable(data) then
		return
	end

	local is_wearing = false
	local fashion_cfg, res_id, index_id
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			index_id = fashion_cfg.index
			is_wearing = WardrobeWGData.Instance:CheckIsSameRes(data.type, data.param1, res_id)
			if not is_wearing then
				if data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING, res_id, index_id)
				elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO, res_id, index_id)
				elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN, res_id, index_id)
				elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING, res_id, index_id)
				else
					NewAppearanceWGCtrl.Instance:OnUseFashion(data.param1, data.param2, 1)
				end
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			res_id = fashion_cfg.appe_image_id
			index_id = fashion_cfg.image_id
			is_wearing = WardrobeWGData.Instance:CheckIsSameRes(data.type, nil, res_id)
			if not is_wearing then
				NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, res_id, index_id)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			is_wearing = WardrobeWGData.Instance:CheckIsSameRes(data.type, nil, fashion_cfg.active_id)
			if not is_wearing then
				NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.USE_KUN, fashion_cfg.id)
			end
		end
	end
end

function GuiXuDreamAttrView:DoActiveEffect(suit, part)
	if self.suit_seq == nil then
		return
	end

	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(suit)

	if suit_data and suit_data.suit_less_need == 0 or suit_data.suit_less_need > suit_data.act_part_num then
		self.node_list["effect_root"]:SetActive(true)
		TipWGCtrl.Instance:ShowEffect({
			effect_type = UIEffectName.s_jihuo,
			is_success = true,
			pos = Vector2(0, 0),
			parent_node = self.node_list["effect_root"]
		})
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end
