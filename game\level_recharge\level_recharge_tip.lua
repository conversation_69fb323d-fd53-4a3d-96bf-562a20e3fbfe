-- 等级直购
LevelRechargeTip = LevelRechargeTip or BaseClass(BaseRender)

function LevelRechargeTip:DoLoad(parent)
	self:LoadAsset("uis/view/level_recharge_ui_prefab", "level_recharge_tip", parent.transform)
end

function LevelRechargeTip:__delete()
	if CountDownManager.Instance:HasCountDown("level_recharge_tip_count_down") then
		CountDownManager.Instance:RemoveCountDown("level_recharge_tip_count_down")
	end
end

function LevelRechargeTip:LoadCallBack()

	local level_cfg = LevelRechargeWGData.Instance:GetLevelRechargeCfg()
	-- self.node_list.level.text.text = level_cfg.rmb_buy_level

	XUI.AddClickEventListener(self.node_list.click_img, BindTool.Bind(self.OnClickGetBtn, self))
	-- XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.OnClickClose, self)
	self:FlushCountDown()
end

function LevelRechargeTip:FlushCountDown()
	if CountDownManager.Instance:HasCountDown("level_recharge_tip_count_down") then
		CountDownManager.Instance:RemoveCountDown("level_recharge_tip_count_down")
	end

	local online_time = TimeWGCtrl.Instance:GetOnlineTimes()
	local continue_time = LevelRechargeWGData.Instance:GetLevelRechargeShowTime()
	local count_down_time = continue_time - online_time 

	if count_down_time > 0 then
		self:UpdateCountDown(0, count_down_time)
		CountDownManager.Instance:AddCountDown(
			"level_recharge_tip_count_down",
			BindTool.Bind(self.UpdateCountDown, self),
			function ()
			self:SetVisible(false)
			end,
			nil,
			count_down_time,
			0.02
		)
	else
		self:SetVisible(false)
	end
end

function LevelRechargeTip:UpdateCountDown(elapse_time, total_time)
	local time = total_time - elapse_time
	local min = math.floor((time / 60) % 60)
	local s = math.floor(time % 60)
	local ss,ms = math.modf(time)
	ms = math.floor(ms * 100)
	
	if self.node_list.count_down_lbl then
		self.node_list.count_down_lbl.text.text = string.format("%02d:%02d.%02d", min, s, ms)
	end
end

function LevelRechargeTip:OnClickGetBtn()
	ViewManager.Instance:Open(GuideModuleName.LevelRechargeView)
end

-- function LevelRechargeTip:OnClickClose()
-- 	LevelRechargeWGData.Instance:SetIsShowTip(false)
-- 	self:SetVisible(false)
-- end

