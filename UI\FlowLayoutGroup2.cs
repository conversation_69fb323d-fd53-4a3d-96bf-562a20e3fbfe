﻿//------------------------------------------------------------------------------
// Copyright (c) 2018-2018 Nirvana Technology Co. Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

namespace Nirvana
{
    using System.Collections.Generic;
    using UnityEngine;
    using UnityEngine.UI;

    /// <summary>
    /// The glow layout group.
    /// </summary>
    [AddComponentMenu("Nirvana/Layout/Flow Layout Group")]
    public sealed class FlowLayoutGroup2 : LayoutGroup
    {
        [SerializeField]
        private Vector2 spacing;

        [SerializeField]
        private RowAnchor rowAlignment = RowAnchor.Upper;

        [SerializeField]
        [Tooltip("Used working for ContentSizeFitter with horizontalFit")]
        private float workingRowWidth = 0.0f;

        [SerializeField]
        private float minRowWidth = 0.0f;

        [SerializeField]
        private float[] rowPadding;

        private ContentSizeFitter contentSizeFitter;

        // The layout height.
        private float layoutHeight;

        // The max row width.
        private float maxRowWidth;

        // Holds the rects that will make up the current row being processed
        private IList<RectTransform> rowList = new List<RectTransform>();

        /// <summary>
        /// The anchor for the row.
        /// </summary>
        public enum RowAnchor
        {
            /// <summary>
            /// Anchor each item by upper in one line.
            /// </summary>
            Upper,

            /// <summary>
            /// Anchor each item by middle in one line.
            /// </summary>
            Middle,

            /// <summary>
            /// Anchor each item by lower in one line.
            /// </summary>
            Lower,
        }

        /// <summary>
        /// Gets the spacing.
        /// </summary>
        public Vector2 Spacing
        {
            get { return this.spacing; }
        }

        /// <summary>
        /// Gets the working row width.
        /// </summary>
        public float WorkingRowWidth
        {
            get { return this.workingRowWidth; }
        }

        /// <summary>
        /// Gets the last row width.
        /// </summary>
        public float LastRowWidth { get; set; }

        private bool IsCenterAlign
        {
            get
            {
                return this.childAlignment == TextAnchor.LowerCenter ||
                    this.childAlignment == TextAnchor.MiddleCenter ||
                    this.childAlignment == TextAnchor.UpperCenter;
            }
        }

        private bool IsRightAlign
        {
            get
            {
                return this.childAlignment == TextAnchor.LowerRight ||
                    this.childAlignment == TextAnchor.MiddleRight ||
                    this.childAlignment == TextAnchor.UpperRight;
            }
        }

        private bool IsMiddleAlign
        {
            get
            {
                return this.childAlignment == TextAnchor.MiddleLeft ||
                    this.childAlignment == TextAnchor.MiddleRight ||
                    this.childAlignment == TextAnchor.MiddleCenter;
            }
        }

        private bool IsLowerAlign
        {
            get
            {
                return this.childAlignment == TextAnchor.LowerLeft ||
                    this.childAlignment == TextAnchor.LowerRight ||
                    this.childAlignment == TextAnchor.LowerCenter;
            }
        }

        /// <inheritdoc/>
        public override void CalculateLayoutInputHorizontal()
        {
            base.CalculateLayoutInputHorizontal();

            if (this.contentSizeFitter == null)
            {
                this.contentSizeFitter = this.GetComponent<ContentSizeFitter>();
            }

            bool controlWidth = this.contentSizeFitter != null &&
                this.contentSizeFitter.horizontalFit != ContentSizeFitter.FitMode.Unconstrained;
            if (controlWidth)
            {
                this.CalculateLayoutInputVertical();
            }

            var minWidth = this.GetGreatestMinimumChildWidth() +
                this.padding.left + this.padding.right;
            if (controlWidth)
            {
                minWidth = Mathf.Max(
                    minWidth,
                    this.minRowWidth,
                    this.maxRowWidth);
            }

            this.SetLayoutInputForAxis(minWidth, -1, -1, 0);
        }

        /// <inheritdoc/>
        public override void CalculateLayoutInputVertical()
        {
            this.layoutHeight = this.SetLayout(1, true);
        }

        /// <inheritdoc/>
        public override void SetLayoutHorizontal()
        {
            this.SetLayout(0, false);
        }

        /// <inheritdoc/>
        public override void SetLayoutVertical()
        {
            this.SetLayout(1, false);
        }

        public void UpdateLayoutHorizontal()
        {
            this.SetLayout(0, true);
        }

        public void UpdateLayoutVertical()
        {
            this.SetLayout(1, true);
        }

        /// <summary>
        /// Main layout method
        /// </summary>
        /// <param name="axis">0 for horizontal axis, 1 for vertical</param>
        /// <param name="layoutInput">If true, sets the layout input for the
        /// axis. If false, sets child position for axis</param>
        private float SetLayout(int axis, bool layoutInput)
        {
            return this.SetLayout(axis, layoutInput, this.rectChildren);
        }

        private float SetLayout(
            int axis, bool layoutInput, IList<RectTransform> children)
        {
            var groupHeight = this.rectTransform.rect.height;

            if (this.contentSizeFitter == null)
            {
                this.contentSizeFitter = this.GetComponent<ContentSizeFitter>();
            }

            float workingWidth;
            if (this.contentSizeFitter != null &&
                this.contentSizeFitter.horizontalFit != ContentSizeFitter.FitMode.Unconstrained)
            {
                workingWidth = this.workingRowWidth;
            }
            else
            {
                workingWidth = this.rectTransform.rect.width -
                    this.padding.left - this.padding.right;
            }

            // Accumulates the total height of the rows, including spacing and
            // padding.
            var offsetY = this.IsLowerAlign ?
                (float)this.padding.bottom : (float)this.padding.top;

            this.LastRowWidth = 0f;
            this.maxRowWidth = 0f;
            var currentRowWidth = 0f;
            var currentRowHeight = 0f;
            var rowIndex = 0;
            for (var i = 0; i < children.Count; ++i)
            {
                // LowerAlign works from back to front.
                var index = this.IsLowerAlign ? children.Count - 1 - i : i;
                var child = children[index];

                var childWidth = LayoutUtility.GetPreferredSize(child, 0);
                var childHeight = LayoutUtility.GetPreferredSize(child, 1);

                // Max child width is layout group width - padding.
                childWidth = Mathf.Min(childWidth, workingWidth);

                // If adding this element would exceed the bounds of the row,
                // go to a new line after processing the current row.
                if (currentRowWidth + childWidth > workingWidth)
                {
                    currentRowWidth -= this.spacing.x;

                    // Process current row elements positioning.
                    if (!layoutInput)
                    {
                        var h = this.CalculateRowVerticalOffset(
                            groupHeight, offsetY, currentRowHeight);
                        this.LayoutRow(
                            this.rowList,
                            currentRowWidth,
                            currentRowHeight,
                            workingWidth,
                            this.padding.left,
                            h,
                            axis,
                            rowIndex++);
                    }

                    // Clear existing row.
                    this.rowList.Clear();

                    // Add the current row height to total height accumulator,
                    // and reset to 0 for the next row.
                    offsetY += currentRowHeight;
                    offsetY += this.spacing.y;

                    currentRowHeight = 0;
                    currentRowWidth = 0;
                }

                currentRowWidth += childWidth;
                this.rowList.Add(child);

                // We need the largest element height to determine the starting
                // position of the next line.
                if (childHeight > currentRowHeight)
                {
                    currentRowHeight = childHeight;
                }

                // Don't do this for the last one.
                if (i < children.Count - 1)
                {
                    currentRowWidth += this.spacing.x;
                }

                // Record the max row width.
                if (this.maxRowWidth < currentRowWidth)
                {
                    this.maxRowWidth = currentRowWidth;
                }
            }

            if (!layoutInput)
            {
                var h = this.CalculateRowVerticalOffset(
                    groupHeight, offsetY, currentRowHeight);
                currentRowWidth -= this.spacing.x;

                // Layout the final row.
                this.LayoutRow(
                    this.rowList,
                    currentRowWidth,
                    currentRowHeight,
                    workingWidth - (this.rowList.Count > 1 ? this.spacing.x : 0),
                    this.padding.left,
                    h,
                    axis,
                    rowIndex);
            }

            this.rowList.Clear();

            // Record current row width
            this.LastRowWidth = Mathf.Max(currentRowWidth, 0.0f);

            // Add the last rows height to the height accumulator
            offsetY += currentRowHeight;
            offsetY += this.IsLowerAlign ? this.padding.top : this.padding.bottom;

            if (layoutInput && axis == 1)
            {
                this.SetLayoutInputForAxis(offsetY, offsetY, -1, axis);
            }

            return offsetY;
        }

        private float CalculateRowVerticalOffset(
            float groupHeight, float offsetY, float currentRowHeight)
        {
            float h;

            if (this.IsLowerAlign)
            {
                h = groupHeight - offsetY - currentRowHeight;
            }
            else if (this.IsMiddleAlign)
            {
                h = (groupHeight * 0.5f) - (this.layoutHeight * 0.5f) + offsetY;
            }
            else
            {
                h = offsetY;
            }

            return h;
        }

        private void LayoutRow(
            IList<RectTransform> contents,
            float rowWidth,
            float rowHeight,
            float maxWidth,
            float offsetX,
            float offsetY,
            int axis,
            int rowIndex)
        {
            if (this.rowPadding != null && this.rowPadding.Length > rowIndex)
            {
                offsetX += this.rowPadding[rowIndex];
            }

            var posX = offsetX;
            if (this.IsCenterAlign)
            {
                var rect = this.rectTransform;
                posX += (rect.sizeDelta.x - rowWidth) * 0.5f;
            }
            else if (this.IsRightAlign)
            {
                var rect = this.rectTransform;
                posX += rect.sizeDelta.x - rowWidth;
            }

            for (var i = 0; i < contents.Count; ++i)
            {
                var index = this.IsLowerAlign ? contents.Count - 1 - i : i;

                var rowChild = contents[index];
                var rowChildWidth = LayoutUtility.GetPreferredSize(rowChild, 0);
                var rowChildHeight = LayoutUtility.GetPreferredSize(rowChild, 1);

                rowChildWidth = Mathf.Min(rowChildWidth, maxWidth);

                var posY = offsetY;
                if (this.IsMiddleAlign || this.rowAlignment == RowAnchor.Middle)
                {
                    posY += (rowHeight - rowChildHeight) * 0.5f;
                }
                else if (this.IsLowerAlign || this.rowAlignment == RowAnchor.Lower)
                {
                    posY += rowHeight - rowChildHeight;
                }

                if (axis == 0)
                {
                    this.SetChildAlongAxis(rowChild, 0, posX, rowChildWidth);
                }
                else
                {
                    this.SetChildAlongAxis(rowChild, 1, posY, rowChildHeight);
                }

                // Don't do horizontal spacing for the last one
                if (i < contents.Count - 1)
                {
                    posX += rowChildWidth + this.spacing.x;
                }
            }
        }

        private float GetGreatestMinimumChildWidth()
        {
            var max = 0.0f;
            foreach (var child in this.rectChildren)
            {
                var w = LayoutUtility.GetMinWidth(child);
                max = Mathf.Max(w, max);
            }

            return max;
        }
    }
}
