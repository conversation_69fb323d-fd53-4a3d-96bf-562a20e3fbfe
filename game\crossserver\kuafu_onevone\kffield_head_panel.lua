KfFieldHeadPanel = KfFieldHeadPanel or BaseClass(SafeBaseView)

function KfFieldHeadPanel:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "kf_layout_field_head_panel")
	self.view_layer = UiLayer.MainUILow
	self.can_penetrate = true
	self.user_list = {}
	self.order = 1
	self.out_time = 0
	self.prepare_time = 0

	self.out_time_key = "field_head_out_time"
	self.prepare_time_key = "field_head_prepare_time"
	self.head_img_list = {}
	self.cap_list = {}
	self.role_hp_list = {}

	for i=1,2 do
		self.role_hp_list[i] = {}
		self.role_hp_list[i].main_cur_hp = 0
		self.role_hp_list[i].main_max_hp = 0
	end

	self.is_set_data = false
	self.is_have_parepare_time = false
	self.is_startimage_setfals = false
	self.send_over_pro = nil
end

function KfFieldHeadPanel:__delete()
end

function KfFieldHeadPanel:ReleaseCallBack()
	Runner.Instance:RemoveRunObj(self)
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)
	CountDownManager.Instance:RemoveCountDown(self.prepare_time_key)
	if self.update_end_event then
	GlobalEventSystem:UnBind(self.update_end_event)
	end
	for k,v in pairs(self.cap_list) do
		v:DeleteMe()
	end
	self.cap_list = {}

	if self.tine_num1 then
		self.tine_num1:DeleteMe()
		self.tine_num1 = nil
	end

	if self.tine_num2 then
		self.tine_num2:DeleteMe()
		self.tine_num2 = nil
	end
	self.is_startimage_setfals = nil
	self.head_img_list = {}
	if self.close_timer_quest13 ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer_quest13)
		self.close_timer_quest13 = nil
	end
	self.send_over_pro = nil
end

function KfFieldHeadPanel:LoadCallBack()
	self:HideOverBtn()
	XUI.AddClickEventListener(self.node_list.over_btn, BindTool.Bind1(self.FinishThisFight, self))
	Runner.Instance:AddRunObj(self, 8)
	self.node_list.ph_timenum2:SetActive(false)
	self.update_end_event = GlobalEventSystem:Bind(OtherEventType.BEFORE_OUT_SCENE, BindTool.Bind1(self.SetEndEvent, self))
	self.node_list.rank_icon1:SetActive(true)
	self.node_list.rank_icon2:SetActive(true)
end

-- 准备时间
function KfFieldHeadPanel:SetPrepareTimeKF(time)
	if time > 0 then
		GuajiWGCtrl.Instance:StopGuaji()
		self.prepare_time = time
		if nil == self.node_list.ph_timenum2 then return end

		local timer_func = BindTool.Bind1(self.KFPrepareTimerFunc, self)

		local timer_callback = function()
			if not self:IsOpen() then
				return 
			end
			
			if CountDownManager.Instance:HasCountDown(self.prepare_time_key) then
				CountDownManager.Instance:RemoveCountDown(self.prepare_time_key)
			end 

			CountDownManager.Instance:AddCountDown(self.prepare_time_key, timer_func, BindTool.Bind1(self.PrepareCompare, self), self.prepare_time , nil, 1)
		end

	end
end

function KfFieldHeadPanel:PrepareTimerFunc(elapse_time, total_time)
	local time = total_time - elapse_time
	time = math.floor(time)
	if time > 0 then
		self.node_list.ph_timenum2_1:SetActive(true)
		self.node_list.ph_timenum2.text.text = time
		self:DoTweenScaleContent(self.node_list.ph_timenum2_1)
	else
		self.node_list.ph_timenum2_1:SetActive(false)
		if not self.is_startimage_setfals then
			self.node_list.start_image:SetActive(true)
			self:DoTweenScaleContent(self.node_list.start_image)
		end
		if not self.send_over_pro then
			self.send_over_pro = true
			self.is_startimage_setfals = true
			GlobalTimerQuest:AddDelayTimer(function ()
				self.node_list.start_image:SetActive(false)
				GlobalEventSystem:Fire(OtherEventType.FIELD_FIGHT)
			end, 0.8)
		end
	end
end

function KfFieldHeadPanel:KFPrepareTimerFunc(elapse_time, total_time)
	-- if nil == self.tine_num2 then
	-- 	return
	-- end
	local time = total_time - elapse_time

	
	time = math.floor(time)
	if time >= 0 then
		self.node_list.ph_timenum2.text.text = time
	end
end

function KfFieldHeadPanel:PrepareCompare()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	KuafuOnevoneWGCtrl.SendCross1v1FightReady()
end

function KfFieldHeadPanel:Update(now_time, elapse_time)
	if self.user_list then
		for k,v in pairs(self.user_list) do
			local role = Scene.Instance:GetObjectByObjId(v.obj_id)
			if nil == role then
				for k,v in pairs(Scene.Instance.obj_list) do
					local obj_id = RoleWGData.Instance.role_vo.obj_id
					if v:GetType() == SceneObjType.Role and v:GetVo().obj_id ~= obj_id then
						role = v
					end
				end
			end
			if role and role:GetVo() then
				-- local hp = self.hp_list[k]
				-- local percent = 100 / role:GetMaxHp() * role:GetHp()
				-- if percent ~= hp:getPercent() then
				-- 	hp:setPercent(percent)
				-- end
				if role.vo.hp ~= self.role_hp_list[k].main_cur_hp or role.vo.max_hp  ~= self.role_hp_list[k].main_max_hp then 
				 	self.role_hp_list[k].main_cur_hp = role.vo.hp
				 	self.role_hp_list[k].main_max_hp = role.vo.max_hp
				 	self:RoleSliderChange(k,role.vo.hp,role.vo.max_hp)
				end
			end
		end
	end
end

function KfFieldHeadPanel:OpenCallBack()	
	self:UpdataStatus()
end


function KfFieldHeadPanel:UpdataStatus()
	self.status = KuafuOnevoneWGData.Instance.scene_status
	self.next_time = KuafuOnevoneWGData.Instance.scene_next_time
	if self.next_time < 0 then return end

	self:Flush(0,"prepare_time",{time = self.next_time})
end

function KfFieldHeadPanel:FlushView()
	local main_role = GameVoManager.Instance:GetMainRoleVo()
	local list_role = Scene.Instance:GetRoleList()	
	if self.user_list then
		for k,v in pairs(self.user_list) do

			if not v.level then
				return
			end
			
			local cap = v.capability
			local is_vis, limt_level = RoleWGData.Instance:GetDianFengLevel(v.level)
			if is_vis then
				if k == 1 then
					self.node_list.feixianlevel_imag:SetActive(is_vis)
					self.node_list["label_lv_feixian_1" ].text.text = limt_level
					self.node_list["label_lv_1"]:SetActive(not is_vis)
				else
					self.node_list["label_lv_feixian_2"]:SetActive(is_vis)
					self.node_list["label_lv_2"]:SetActive(not is_vis)
					self.node_list["label_lv_feixian_2"].text.text = limt_level
				end
			else
				if k == 1 then
					self.node_list.feixianlevel_imag:SetActive(is_vis)
					self.node_list["label_lv_1" ]:SetActive(not is_vis)
					self.node_list["label_lv_1" ].text.text = "Lv."..limt_level
				else
					self.node_list["label_lv_feixian_2"]:SetActive(is_vis)
					self.node_list["label_lv_2"]:SetActive(not is_vis)
					self.node_list["label_lv_2"].text.text = "Lv."..limt_level
				end
			end

			self.node_list["label_name_" .. k].text.text = v.name
			self.node_list["ph_cap_"..k].text.text = cap
			if Scene.Instance:GetSceneType() == SceneType.Kf_OneVOne then
				self.node_list["ph_cap_"..k].text.text = v.capability
			end

			local role_id = v.role_id
			if IS_ON_CROSSSERVER and nil ~= v.origin_uid and v.origin_uid > 0 then
				role_id = v.origin_uid
				local is_self = role_id == RoleWGData.Instance:InCrossGetOriginUid()
				local server_id = is_self and RoleWGData.Instance:GetOriginServerId() or v.oppo_sever_id
				if is_self then
					local name = v.name .. "_s" .. server_id
					self.node_list["label_name_" .. k].text.text = name
				end
			end
			XUI.UpdateRoleHead(self.node_list["head_icon_"..k], self.node_list["custom_head_icon_"..k], role_id, 
				v.sex, v.prof, false, true, true)
		end
	end

	local self_score = KuafuOnevoneWGData.Instance:Get1V1InfoJiFen()
	local rank_cfg = KuafuOnevoneWGData.Instance:GetRewardBaseCell(self_score)
	local b, a = ResPath.GetF2Field1v1Rank(rank_cfg.rank_id)
	self.node_list.rank_icon1.image:LoadSprite(b, a)

	local oppo_info = KuafuOnevoneWGData.Instance:GetOppoInfo()
	oppo_info.oppo_score = oppo_info.oppo_score or 0
	local rank_cfg = KuafuOnevoneWGData.Instance:GetRewardBaseCell(oppo_info.oppo_score)
	local b, a = ResPath.GetF2Field1v1Rank(rank_cfg.rank_id)
	self.node_list.rank_icon2.image:LoadSprite(b, a)
end


function KfFieldHeadPanel:SetEndEvent()
	--self.node_list.ph_timenum1:SetActive(false)
end

function KfFieldHeadPanel:FinishThisFight()
	RobertManager.Instance:StopFight()
end

function KfFieldHeadPanel:HideOverBtn()
	self.node_list.over_btn:SetActive(false)
end

function KfFieldHeadPanel:CloseCallBack()
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)
	CountDownManager.Instance:RemoveCountDown(self.prepare_time_key)
end


function KfFieldHeadPanel:ShowIndexCallBack()
	self:Flush()
end

function KfFieldHeadPanel:SetData(user_list)
	self.user_list = user_list
	self:Flush()
	if self.user_list then

		if nil == self.user_list[1] or nil == self.user_list[2] then return end
		local role_sex = self.user_list[1].sex
		if not self.is_set_data then
			self.is_set_data = true
			for k,v in pairs(self.user_list) do
				self.role_hp_list[k].main_cur_hp = v.hp
				self.role_hp_list[k].main_max_hp = v.max_hp
			end
		end
		for k,v in ipairs(self.user_list) do
			if v.hp == 0 then
				self.is_set_data = false
				local other_cfg = Field1v1WGData.Instance:GetChallengeFieldOtherCfg()
				local audio_res = nil
				if k == 1 then --自己死了
					if role_sex == GameEnum.MALE then --男性
						audio_res = other_cfg.men_fail_audio
					else
						audio_res = other_cfg.women_fail_audio
					end
				else
					if role_sex == GameEnum.MALE then
						audio_res = other_cfg.men_win_audio
					else
						audio_res = other_cfg.women_win_audio
					end
				end
				break
			end
		end
	end
end

function KfFieldHeadPanel:OnFlush(param_list, index)
	local main_role = GameVoManager.Instance:GetMainRoleVo()
	local list_role = Scene.Instance:GetRoleList()

	for k, v in pairs(param_list) do
		if k == "out_time" then
			self:SetOutTime(v.time)
		elseif k == "prepare_time" then
			self.node_list.activity_tips:SetActive(false)
			self:SetPrepareTimeKF(v.time)
			self.is_have_parepare_time = true
		elseif k == "star_img" then
			self:SetIsShowStartImg(v.is_show)
		end
	end
	self:FlushView()
end

function KfFieldHeadPanel:GetRoleListCap()
	if self.user_list then
		return self.user_list[1].capability or 1,self.user_list[2].capability or 1
	end
	return 1 ,1
end

function KfFieldHeadPanel:RoleSliderChange(index,cur_hp,max_hp)
	self.node_list["boss_progress_image_"..index].image.fillAmount = cur_hp / max_hp
end

-- 结束时间
function KfFieldHeadPanel:SetOutTime(time)
	self.out_time = time
	self:OutTimerFunc()
	local timer_func = BindTool.Bind1(self.OutTimerFunc, self)
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)
	if self.out_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
		CountDownManager.Instance:AddCountDown(self.out_time_key, timer_func, nil, self.out_time, nil, 1)
	end
end

function KfFieldHeadPanel:OutTimerFunc()
	local time = math.floor(self.out_time - TimeWGCtrl.Instance:GetServerTime())
	if  time >= 0 and self.node_list.ph_timenum1 then
		self.node_list.ph_timenum1.text.text = time
		self.node_list.activity_tips:SetActive(true)
	end
end

-- 准备时间
function KfFieldHeadPanel:SetPrepareTime(time)
	if time > 0 then
		self.prepare_time = time - 1
		local timer_func = BindTool.Bind1(self.PrepareTimerFunc, self)

		local timer_callback = function()
			if not self:IsOpen() then
				return
			end
			if CountDownManager.Instance:HasCountDown(self.prepare_time_key) then
				CountDownManager.Instance:RemoveCountDown(self.prepare_time_key)
			end

			CountDownManager.Instance:AddCountDown(self.prepare_time_key, timer_func,
			 BindTool.Bind1(self.PrepareTimeCompleteCallback, self), self.prepare_time, nil, 1)
		end
		timer_callback()
		self.node_list.ph_timenum1.text.text = ""
		self.node_list.activity_tips:SetActive(false)
	end
end

function KfFieldHeadPanel:PrepareTimeCompleteCallback()
	if not self.send_over_pro then
		self.send_over_pro = true
		GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.SetDotweenAni, self,false), 0.8)
	end
	GlobalEventSystem:Fire(OtherEventType.FIELD_FIGHT)
end

function KfFieldHeadPanel:SetPrepareTextActive(state)
	if nil == self.node_list.ph_timenum2 then
		 self.close_timer_quest13 = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.SetPrepareTextActive, self,state), 0.05)
	else
		self.node_list.ph_timenum2:SetActive(state)
	end

end

function KfFieldHeadPanel:SetDotweenAniPic(bool)
	if bool then
		self:SetDotweenAni(bool)
	else
		self:SetDotweenAni(false)
	end

end

function KfFieldHeadPanel:SetDotweenAni(bool)
	if nil == self.node_list.left_red_ani then return end
	local scene_type = Scene.Instance:GetSceneType()
	local flag = bool or self.prepare_time - TimeWGCtrl.Instance:GetServerTime() > 1
	if scene_type ~= nil and scene_type == SceneType.Kf_OneVOne then
		flag = bool and self.prepare_time - TimeWGCtrl.Instance:GetServerTime() > 1
	end

	if flag then
		self.node_list.left_red_ani:SetActive(true)
		self.node_list.right_blue_ani:SetActive(true)
		local tween1 = self.node_list.left_red_ani.rect:DOAnchorPosX(-201.6, 0.2)
	    tween1:SetEase(DG.Tweening.Ease.Linear)
	    local tween2 = self.node_list.right_blue_ani.rect:DOAnchorPosX(185.7, 0.2)
	    tween2:SetEase(DG.Tweening.Ease.Linear)
    else
    	if self.node_list.start_image then
    		self.node_list.start_image:SetActive(false)
    	end
    	self.is_startimage_setfals = true
	    local tween1 = self.node_list.left_red_ani.rect:DOAnchorPosX(-1600, 0.5)
	    tween1:SetEase(DG.Tweening.Ease.Linear)
	    local tween2 = self.node_list.right_blue_ani.rect:DOAnchorPosX(1600, 0.5)
	    tween2:SetEase(DG.Tweening.Ease.Linear)
	    GlobalTimerQuest:AddDelayTimer(function ()
	    	GlobalEventSystem:Fire(OtherEventType.FIELD_FIGHT)
	    	self.node_list.left_red_ani:SetActive(false)
			self.node_list.right_blue_ani:SetActive(false)
	    end, 0.5)
    end
end

function KfFieldHeadPanel:DoTweenScaleContent(node)
	local scale = Vector3(1,1,1)
	if node ~= nil then
		node.rect.localScale = Vector3(2,2,1)
		node.rect:DOScale(scale,0.3)
	end
end

function KfFieldHeadPanel:SetTineNumEffect(sprite, scale)
end

function KfFieldHeadPanel:SetIsShowStartImg(is_show)
	if not self:IsLoaded() then return end
	if is_show then
		self.node_list.ph_timenum2:SetActive(false)
		self.node_list.ph_timenum1:SetActive(true)
		self.node_list.mask_bg_one:SetActive(false)
		--GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end
