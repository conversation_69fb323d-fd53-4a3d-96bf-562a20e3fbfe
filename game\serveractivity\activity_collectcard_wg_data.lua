ActivityCollectCardWGData = ActivityCollectCardWGData or BaseClass()
local UNIVERSAL_CARD = 5

function ActivityCollectCardWGData:__init()
	if ActivityCollectCardWGData.Instance ~= nil then
		ErrorLog("[ActivityCollectCardWGData] attempt to create singleton twice!")
		return
	end
	ActivityCollectCardWGData.Instance = self
    self:InitCfg()
	self:InitInfo()
end

function ActivityCollectCardWGData:__delete()
    self:DeleteCfg()
	self:DeleteInfo()
	ActivityCollectCardWGData.Instance = nil
end

-- 初始化配置表
function ActivityCollectCardWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_collect_card_auto")
	if cfg then
        self.base_cfg = cfg.other[1]
        self.card_seq_cfg = cfg.card
		self.card_list_cfg = ListToMapList(cfg.card, "type")
        self.mode_cfg = cfg.mode
        self.reward_pool_cfg = cfg.reward_pool
        self.task_cfg = ListToMap(cfg.task, "grade", "task_id")
        self.reward_cfg = ListToMap(cfg.reward, "grade", "seq")
		self.collect_reward_cfg = ListToMap(cfg.collect_reward, "grade", "seq")
    end
end

-- 清理垃圾
function ActivityCollectCardWGData:DeleteCfg()
    self.base_cfg = nil
    self.card_seq_cfg = nil
    self.mode_cfg = nil
    self.reward_pool_cfg = nil
    self.task_cfg = nil
    self.reward_cfg = nil
end

-- 初始化数据
function ActivityCollectCardWGData:InitInfo()
	RemindManager.Instance:Register(RemindName.CollectCardRed, BindTool.Bind(self.GetActCollectCardRemind, self))
end
-- 清理垃圾
function ActivityCollectCardWGData:DeleteInfo()
	self.card_info = nil
	self.task_info = nil
	self.card_self_info = nil
	self.card_friend_info = nil

    RemindManager.Instance:UnRegister(RemindName.CollectCardRed)
end

-- 开服集卡活动红点
function ActivityCollectCardWGData:GetActCollectCardRemind()
	if self:IsHaveDrawTimes() then
		return 1
	end

	if self:IsHaveCollectTypeNumberReward() then
		return 1
	end

	if self:IsHaveCollectTypeReward() then
		return 1
	end

	if self:IsHaveCollectTaskReward() then
		return 1
	end

	if self:GetCurGradeCollectCardRequsetRed() then
		return 1
	end

	return 0
end

-- 当前是否有抽卡次数
function ActivityCollectCardWGData:IsHaveDrawTimes()
	local draw_times = self:GetCollectCardDrawTime()
	return draw_times > 0
end

-- 当前是否可以领取收集种类个数奖励
function ActivityCollectCardWGData:IsHaveCollectTypeNumberReward()
	local now_num = self:GetAllCollectNumber()
	local pro_list = self:GetCurGradeCollectCardProRewarList()
	if not pro_list then
		return false
	end

	for k, card_pro_data in pairs(pro_list) do
		if card_pro_data and card_pro_data.seq then
			local is_can_get = self:GetCollectRewardFlagBySeq(card_pro_data.seq) ~= 1
			local card_pro_num = card_pro_data.card_num or 1
			if is_can_get and now_num >= card_pro_num then
				return true
			end
		end
	end

	return false
end

-- 当前是否可以领取收集种类奖励
function ActivityCollectCardWGData:IsHaveCollectTypeReward()
	local reward_list = self:GetCurGradeCollectCardRewardList()
	if not reward_list then
		return false
	end

	for k, reward_data in pairs(reward_list) do
		if reward_data and reward_data.seq then
			local is_can_get = self:GetRewardFlagBySeq(reward_data.seq) ~= 1
			local type_num = self:GetAllCollectTypeNumber(reward_data.card_type)

			if is_can_get and type_num >= reward_data.card_num then
				return true
			end
		end
	end

	return false
end

-- 当前是否有任务可以领取
function ActivityCollectCardWGData:IsHaveCollectTaskReward()
	local task_list = self:GetCurGradeCollectCardTaskList()
	if not task_list then
		return false
	end

	for i, v in ipairs(task_list) do
		if v.is_can_get and (not  v.is_get_flag) then
			return true
		end
	end

	return false
end
-----------------------------------------------------------
-- 获取基础表
function ActivityCollectCardWGData:GetBaseCfg()
	return self.base_cfg
end

-- 获取卡牌信息
function ActivityCollectCardWGData:GetCardBySeq(seq)
	local empty = {}
	return (self.card_seq_cfg or empty)[seq]
end

-- 获取卡牌列表信息
function ActivityCollectCardWGData:GetCardListByType(card_type)
	local empty = {}
	return (self.card_list_cfg or empty)[card_type]
end

-- 获取抽奖模式
function ActivityCollectCardWGData:GetModeCfgByMode(mode)
	local empty = {}
	return (self.mode_cfg or empty)[mode]
end

-- 获取奖池根据档次
function ActivityCollectCardWGData:GetRewardPoolByGrade(grade)
	local empty = {}
	return (self.reward_pool_cfg or empty)[grade]
end

-- 获取任务列表根据档次
function ActivityCollectCardWGData:GetTaskListByGrade(grade)
	local empty = {}
	return (self.task_cfg or empty)[grade]
end

-- 获取任务信息根据档次加任务id
function ActivityCollectCardWGData:GetTaskCfgByGrade(grade, task_id)
	local empty = {}
	return ((self.task_cfg or empty)[grade] or empty)[task_id]
end

-- 获取奖励列表根据档次
function ActivityCollectCardWGData:GetRewardListByGrade(grade)
	local empty = {}
	return (self.reward_cfg or empty)[grade] or {}
end

-- 获取奖励列表根据档次
function ActivityCollectCardWGData:GetRewardCfgByGradeSeq(grade, seq)
	local empty = {}
	return ((self.reward_cfg or empty)[grade] or empty)[seq]
end

-- 获取奖励列表根据档次
function ActivityCollectCardWGData:GetCollectRewardListByGrade(grade)
	local empty = {}
	return (self.collect_reward_cfg or empty)[grade]
end

-- 获取奖励列表根据档次
function ActivityCollectCardWGData:GetCollectRewardCfgByGradeSeq(grade, seq)
	local empty = {}
	return ((self.collect_reward_cfg or empty)[grade] or empty)[seq]
end

-- 是否为碎片id
function ActivityCollectCardWGData:IsCollectCardChipItemId(item_id)
	local empty = {}
	return (self.base_cfg or empty).ship_id == item_id
end
-----------------------------------------------------------

-----------------------------------------------------------
-- 集卡活动卡牌信息
function ActivityCollectCardWGData:SetCollectCardInfo(protocol)
	if not self.card_info then
		self.card_info = {}
	end

	self.card_info.grade = protocol.grade
	self.card_info.reward_flag = protocol.reward_flag

	if self.card_info.draw_times ~= nil and protocol.draw_times ~= 0 and protocol.draw_times ~= self.card_info.draw_times and protocol.draw_times > self.card_info.draw_times then
		local get_new_time = protocol.draw_times - self.card_info.draw_times

		local item_cfg = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.VIRTUAL_ITEM_COLLECT_CARD)
		if item_cfg then
			-- 新获得提示
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.SysRemind.AddItem, item_name, get_new_time)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end

	self.card_info.draw_times = protocol.draw_times
	self.card_info.has_draw_times = protocol.has_draw_times

	self.card_info.collect_reward_flag = protocol.collect_reward_flag

	self.card_info.collect_card_num = 0
	self.card_info.collect_card_type_num = {}

	for i = 0, #protocol.card_list do
		local seq = i
		local cfg = self:GetCardBySeq(seq)
		local card_type = cfg and cfg.type or 0
		local num = protocol.card_list[i]

		if self.card_info.card_list ~= nil and self.card_info.card_list[i] ~= nil and self.card_info.card_list[i] ~= num and num > self.card_info.card_list[i] then
			local get_new_time = num - self.card_info.card_list[i] 
			local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item_id or 0)
			if item_cfg and cfg.type ~= UNIVERSAL_CARD then
				-- 新获得提示
				local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
				local str = string.format(Language.SysRemind.AddItem, item_name, get_new_time)
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end

			-- -- 最新获得
			-- if self.card_info.card_list[i] == 0 and num ~= 0 then
			-- 	ServerActivityWGCtrl.Instance:OpenCollectCardOperateView(seq)
			-- end
		end

		if num > 0 and card_type ~= UNIVERSAL_CARD then
			self.card_info.collect_card_num = self.card_info.collect_card_num + 1

			if self.card_info.collect_card_type_num[card_type] then
				self.card_info.collect_card_type_num[card_type] = self.card_info.collect_card_type_num[card_type] + 1
			else
				self.card_info.collect_card_type_num[card_type] = 1
			end
		end
	end

	self.card_info.card_list = protocol.card_list
end

-- 集卡活动任务信息
function ActivityCollectCardWGData:SetCollectCardTaskInfo(protocol)
	if not self.task_info then
		self.task_info = {}
	end

	self.task_info.task_process = protocol.task_process
	self.task_info.task_flag = protocol.task_flag
end

-- 集卡活动任务信息更新
function ActivityCollectCardWGData:SetCollectCardTaskUpdateInfo(protocol)
	if not self.task_info then
		self.task_info = {}
	end

	if not self.task_info.task_process then
		self.task_info.task_process = {}
	end

	self.task_info.task_process[protocol.task_id] = protocol.task_process
	self.task_info.task_flag = protocol.task_flag
end

-- 集卡活动索取信息
function ActivityCollectCardWGData:SetCollectCardSelfInfo(protocol)
	if not self.card_self_info then
		self.card_self_info = {}
	end

	self.card_self_info.grade = protocol.grade						--//档次
	self.card_self_info.gain_times = protocol.gain_times			--//当前向别人索要的次数
	self.card_self_info.be_times = protocol.be_times				--//当前被增送次数
	self.card_self_info.times = protocol.times						--//当前主动赠送次数
	self.card_self_info.redemption_num = protocol.redemption_num	--//当前兑换次数
	self.card_self_info.request_list = protocol.request_list
end

-- 集卡活动朋友索要信息
function ActivityCollectCardWGData:SetCollectCardFriendInfo(protocol)
	if not self.card_friend_info then
		self.card_friend_info = {}
	end

	self.card_friend_info.friend_list = protocol.friend_list
end

-- 集卡活动索取信息更新
function ActivityCollectCardWGData:SetCollectCardInfoUpdateInfo(protocol)
	if not self.card_friend_info then
		self.card_friend_info = {}
	end

	if not self.card_friend_info.friend_list then
		self.card_friend_info.friend_list = {}
	end

	for k, v in pairs(self.card_friend_info.friend_list) do
		if v.uid and v.uid == protocol.uid then
			v.be_times = protocol.be_times
			v.gain_flag[protocol.seq] = protocol.flag
		end
	end
end

--集卡活动抽卡记录
function ActivityCollectCardWGData:SetCollectCardRecordInfo(protocol)
	if not self.card_record_list then
		self.card_record_list = {}
	end

	for i, v in ipairs(protocol.record_list) do
		table.insert(self.card_record_list, v)
	end
end

--集卡活动抽卡更新
function ActivityCollectCardWGData:SetCollectCardRecordUpdateInfo(protocol)
	if not self.card_record_list then
		self.card_record_list = {}
	end

	local data = {}
	data.seq = protocol.seq
	data.num = protocol.num
	table.insert(self.card_record_list, 1, data)
end

-- 获取当前的卡牌数据
function ActivityCollectCardWGData:GetCollectCardInfo()
	return self.card_info
end

-- 获取当前的集卡活动索取信息
function ActivityCollectCardWGData:GetCollectCardSelfInfo()
	return self.card_self_info
end

-- 获取当前的领取奖励标记
function ActivityCollectCardWGData:GetRewardFlagBySeq(seq)
	local empty = {}
	return ((self.card_info or empty).reward_flag or empty)[seq] or 0
end

-- 获取当前的领取收集奖励标记
function ActivityCollectCardWGData:GetCollectRewardFlagBySeq(seq)
	local empty = {}
	return ((self.card_info or empty).collect_reward_flag or empty)[seq] or 0
end

-- 获取当前的卡牌数据列表
function ActivityCollectCardWGData:GetCollectCardList()
	local empty = {}
	return (self.card_info or empty).card_list
end

-- 获取当前的卡牌数据
function ActivityCollectCardWGData:GetCollectCardInfoBySeq(seq)
	local empty = {}
	return ((self.card_info or empty).card_list or empty)[seq] or 0
end

-- 获取已抽奖次数和可抽奖次数
function ActivityCollectCardWGData:GetCollectCardDrawTime()
	local draw_times = self.card_info and self.card_info.draw_times or 0
	local has_draw_times = self.card_info and self.card_info.has_draw_times or 0

	return draw_times, has_draw_times
end

-- 获取当前的任务进度
function ActivityCollectCardWGData:GetCollectCardTaskProgress(task_id)
	local empty = {}
	return ((self.task_info or empty).task_process or empty)[task_id]
end

-- 获取当前的任务领取状态
function ActivityCollectCardWGData:GetCollectCardTaskFlag(task_id)
	local empty = {}
	return ((self.task_info or empty).task_flag or empty)[task_id]
end

-- 获取当前收集的个数，不区分种类
function ActivityCollectCardWGData:GetAllCollectNumber()
	local empty = {}
	return (self.card_info or empty).collect_card_num or 0
end

-- 获取当前收集的种类个数
function ActivityCollectCardWGData:GetAllCollectTypeNumber(card_type)
	local empty = {}
	return ((self.card_info or empty).collect_card_type_num or empty)[card_type] or 0
end

-- 获取当前的好友索取的列表
function ActivityCollectCardWGData:GetCollectCardFriendInfo()
	local empty = {}
	return (self.card_friend_info or empty).friend_list
end

-- 获取当前的好友请求的列表
function ActivityCollectCardWGData:GetCollectCardSelfRequestInfo()
	local empty = {}
	return (self.card_self_info or empty).request_list
end

----------------------------------------------------
-- 获取当前档次的奖励信息
function ActivityCollectCardWGData:GetCurGradeCollectCardRewardList()
	local card_info = self:GetCollectCardInfo()
	local grade = card_info and card_info.grade or 1
	return self:GetRewardListByGrade(grade)
end

-- 获取当前档次的收集进度奖励信息
function ActivityCollectCardWGData:GetCurGradeCollectCardProRewarList()
	local card_info = self:GetCollectCardInfo()
	local grade = card_info and card_info.grade or 1
	return self:GetCollectRewardListByGrade(grade)
end

-- 获取任务可领取红点
function ActivityCollectCardWGData:GetCollectCardTaskRed()
	local card_info = self:GetCollectCardInfo()
	local grade = card_info and card_info.grade or 1
	local list = self:GetTaskListByGrade(grade)

	if not list then
		return false
	end

	for k, v in pairs(list) do
		if v and v.task_id then
			local progress = self:GetCollectCardTaskProgress(v.task_id) or 0
			local is_can_get = progress >= v.param1
			local is_get_flag = self:GetCollectCardTaskFlag(v.task_id) == 1

			if (not is_get_flag) and is_can_get then
				return true
			end
		end
	end

	return false
end

-- 获取当前的任务列表，返回当前的任务状态
function ActivityCollectCardWGData:GetCurGradeCollectCardTaskList()
	local task_list = {}
	local card_info = self:GetCollectCardInfo()
	local grade = card_info and card_info.grade or 1
	local list = self:GetTaskListByGrade(grade)

	if not list then
		return task_list
	end

	for k, v in pairs(list) do
		if v and v.task_id then
			local data = {}
			data.cfg_data = v	
			data.progress = self:GetCollectCardTaskProgress(v.task_id) or 0
			data.is_can_get = data.progress >= v.param1
			data.is_get_flag = self:GetCollectCardTaskFlag(v.task_id) == 1
			local can_get_num = data.is_can_get and 1 or 0
			local get_flag_num = data.is_get_flag and -1 or 1
			data.sort_index = 1000 + can_get_num * get_flag_num
			table.insert(task_list, data)
		end
	end

	table.sort(task_list, SortTools.KeyUpperSorter("sort_index"))
	return task_list
end

-- 获取当前的请求赠送的列表
function ActivityCollectCardWGData:GetCurGradeCollectCardRequsetList(seq)
	local list = self:GetCollectCardSelfRequestInfo()
	local final_list = {}
	local index = 0

	if list then
		for k, v in pairs(list) do
			if v and v.seq and v.uid ~= -1 and (seq == nil or seq == v.seq) then
				final_list[index] = v
				index = index + 1
			end
		end
	end

	return final_list
end

-- 有索取列表是否可以赠送
function ActivityCollectCardWGData:GetCurGradeCollectCardRequsetRed()
	local list = self:GetCollectCardSelfRequestInfo()
	if list then
		for k, v in pairs(list) do
			if v and v.seq and v.uid ~= -1 then
				local has_num = self:GetCollectCardInfoBySeq(v.seq)
				if has_num > 1 then
					return true
				end
			end
		end
	end

	return false
end

-- 有索取列表是否存在当前的索取
function ActivityCollectCardWGData:GetCurGradeCollectCardRequsetRedBySeq(seq)
	local list = self:GetCollectCardSelfRequestInfo()
	if list then
		for k, v in pairs(list) do
			if v and v.seq == seq and v.uid ~= -1 then
				return true
			end
		end
	end

	return false
end

-- 获取当前的抽奖记录
function ActivityCollectCardWGData:GetCurGradeCollectCardRecordList()
	local record_table = {}
	if self.card_record_list then
		for i, v in ipairs(self.card_record_list) do
			local cfg = self:GetCardBySeq(v.seq)

			if cfg then
				local data = {}
				data.item_data = {}
				data.item_data.item_id = cfg.item_id
				data.item_data.num = v.num
				table.insert(record_table, data)
			end
		end
	end

	return record_table
end