OdysseyPurchaseData = OdysseyPurchaseData or BaseClass()

function OdysseyPurchaseData:__init()
    if OdysseyPurchaseData.Instance then
		error("[OdysseyPurchaseData] Attempt to create singleton twice!")
		return
	end

    OdysseyPurchaseData.Instance = self
	
	self:InitConfig()
	self.grade = 1
	self.buy_flag = {}
	self:GetModelSeq()
	RemindManager.Instance:Register(RemindName.OdysseyPurchaseRemind, BindTool.Bind(self.GetRemind, self))
end

function OdysseyPurchaseData:__delete()
	RemindManager.Instance:UnRegister(RemindName.OdysseyPurchaseRemind)
	OdysseyPurchaseData.Instance = nil
	self.grade = nil
	self.buy_flag = nil
end

function OdysseyPurchaseData:InitConfig()
	local cfg = ConfigManager.Instance:GetAutoConfig("a_lifelong_love_cfg_auto")
	self.cur_reward_cfg = ListToMap(cfg.rmb_grade, "grade", "reward_type")
	self.cur_model_cfg = ListToMapList(cfg.rmb_model, "grade", "reward_type")
	self.model_cfg = ListToMapList(cfg.rmb_model, "grade")
end

--全部数据
function OdysseyPurchaseData:SetAllOdysseyInfo(protocol)
	self.grade = protocol.grade												-- 档次
	self.every_day_reward_flag = protocol.every_day_reward_flag == 1		-- 每日奖励
	self.buy_flag = protocol.buy_flag									-- 次数
end


function OdysseyPurchaseData:GetShopIsBuyFlag()
	return self.every_day_reward_flag
end

function OdysseyPurchaseData:GetRewardState()
	return self.buy_flag[0] or 0, self.buy_flag[1] or 0
end


function OdysseyPurchaseData:GetGradeRewardCfg()
	return (self.cur_reward_cfg[self.grade] or {})[0], (self.cur_reward_cfg[self.grade] or {})[1]
end

function OdysseyPurchaseData:GetGradeModelCfg()
	return (self.cur_model_cfg[self.grade] or {})[0], (self.cur_model_cfg[self.grade] or {})[1]
end


function OdysseyPurchaseData:GetGradeLimit()
	local left_reward_cfg, right_reward_cfg = self:GetGradeRewardCfg()
	if not IsEmptyTable(left_reward_cfg) or not IsEmptyTable(right_reward_cfg) then
		return left_reward_cfg.buy_limit, right_reward_cfg.buy_limit
	end
end

function OdysseyPurchaseData:GetRemind()
	local is_buy_free = self:GetShopIsBuyFlag()
	if not is_buy_free then
		return 1
	end

    return 0
end

function OdysseyPurchaseData:GetModelSeq()
	local cfg_list = self.model_cfg[self.grade] or {}
	local model_seq = {}
	if not IsEmptyTable(cfg_list) then
		for _,v in pairs(cfg_list) do
			if not model_seq[v.model_show_itemid] then
				model_seq[v.model_show_itemid] = v.model_seq
			end
		end
	end

	self.model_stuff_seq = model_seq
end

-- 用于自选包显示
function OdysseyPurchaseData:IsOdysseySeqStuff(item_id)
	if self.model_stuff_seq[item_id] then
		return true
	end

	return false
end
