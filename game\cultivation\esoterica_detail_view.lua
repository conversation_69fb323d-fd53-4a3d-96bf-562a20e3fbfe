EsotericaDetailView = EsotericaDetailView or BaseClass(SafeBaseView)
function EsotericaDetailView:__init()
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    self:SetMaskBg(false, true)
    local common_bundle = "uis/view/common_panel_prefab"
    local view_bundle = "uis/view/cultivation_ui_prefab"
    self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
    self:AddViewResource(0, view_bundle, "layout_esoterica_detail_view")
    self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")

    self.select_index = -1
end

function EsotericaDetailView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Esoterica.TitleName

    local bundle, asset = ResPath.GetRawImagesPNG("a3_xf_bj")
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)

    self.esoterica_slot_data = nil
	self.esoterica_select_slot = -1 --从零开始
    self:CreateEsotericaList()

    --经验道具id ESOTERICA_DEFINE.EXP_ITEM_ID

    -- 属性列表
    if not self.es_attr_list then
        self.es_attr_list = {}
        local parent_node = self.node_list["es_uplevel_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.es_attr_list[i] = cell
        end
    end

    -- 技能
    if not self.es_skill_list then
        self.es_skill_list = {}
        local parent_node = self.node_list["es_skill_list"]
        local skill_num = parent_node.transform.childCount
        for i = 1, 1 do
            local cell = EsotericaSkillRender.New(parent_node:FindObj("es_skill_" .. i))
            cell:SetIndex(i)
            self.es_skill_list[i] = cell
        end
    end

    -- 等级
    if not self.lv_list then
        self.lv_list = {}
        self.lv_effect = {}

        for i = 1, 10 do
            self.lv_list[i] = self.node_list["lv_img_" .. i]
            self.lv_effect[i] = self.node_list["lv_effect_" .. i]
        end
    end

    -- 部位
    -- if not self.es_part_list then
    --     self.es_part_list = {}
    --     local parent_node = self.node_list["es_stuff_list"]
    --     local skill_num = parent_node.transform.childCount
    --     for i = 0, 0 do
    --         local cell = EsotericaPartRender.New(parent_node:FindObj("stuff_" .. i))
    --         cell:SetIndex(i)
    --         self.es_part_list[i] = cell
    --     end
    -- end

    -- if nil == self.esoterica_stuff_item and self.node_list.esoterica_stuff_item then
    --     self.esoterica_stuff_item = ItemCell.New(self.node_list.esoterica_stuff_item)
    -- end

    self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
	RemindManager.Instance:Bind(self.remind_callback, RemindName.EsotericaReslove)

    XUI.AddClickEventListener(self.node_list.btn_es_uplevel, BindTool.Bind(self.OnClickBtnEsotericaUplevel, self, false))
    XUI.AddClickEventListener(self.node_list.btn_es_one_key_uplevel, BindTool.Bind(self.OnClickBtnEsotericaUplevel, self, true))
    XUI.AddClickEventListener(self.node_list.btn_es_act, BindTool.Bind(self.OnClickBtnEsotericaAct, self))
    XUI.AddClickEventListener(self.node_list.btn_es_resolve, BindTool.Bind(self.OnClickBtnEsotericaReslove, self))
    XUI.AddClickEventListener(self.node_list.btn_es_skill_show, BindTool.Bind(self.OnClickBtnEsotericaSkillShow, self))
    XUI.AddClickEventListener(self.node_list.btn_get_esoterica, BindTool.Bind(self.OnClickBtnGetEsoterica, self))
    XUI.AddClickEventListener(self.node_list.btn_up, BindTool.Bind(self.OnClickBtnUp, self))
    XUI.AddClickEventListener(self.node_list.es_cost_item, BindTool.Bind(self.OnClickESCostItem, self))
    XUI.AddClickEventListener(self.node_list.btn_es_cultivate, BindTool.Bind(self.OnClickBtnEsCultivate, self))
end

function EsotericaDetailView:OnClickBtnEsCultivate()
    --打开升级升星界面
    CultivationWGCtrl.Instance:OpenEsotericaPartView(self.esoterica_select_slot, 0)
end

function EsotericaDetailView:OnClickESCostItem()
    local item_id = CultivationWGData.Instance:GetEsotericaPartMinActItemid(self.esoterica_select_slot, 0)
	TipWGCtrl.Instance:OpenItem({item_id = item_id})
end

function EsotericaDetailView:ReleaseCallBack()
    RemindManager.Instance:UnBind(self.remind_callback)
    self.es_effect_show_slot = nil
    self.esoterica_slot_list_data = nil


    if self.esoterica_list then
		self.esoterica_list:DeleteMe()
		self.esoterica_list = nil
	end

    if self.es_role_model then
		self.es_role_model:DeleteMe()
		self.es_role_model = nil
	end

    if self.es_attr_list then
        for k,v in pairs(self.es_attr_list) do
            v:DeleteMe()
        end
        self.es_attr_list = nil
    end

    if self.es_skill_list then
        for k,v in pairs(self.es_skill_list) do
            v:DeleteMe()
        end
        self.es_skill_list = nil
    end

    -- if self.esoterica_stuff_item then
    --     self.esoterica_stuff_item:DeleteMe()
    --     self.esoterica_stuff_item = nil
    -- end
    -- if self.es_part_list then
    --     for k,v in pairs(self.es_part_list) do
    --         v:DeleteMe()
    --     end
    --     self.es_part_list = nil
    -- end
    self.lv_list = nil
    self.lv_effect = nil
end

function EsotericaDetailView:CloseCallBack()
    self.select_index = -1
end

--设置选中index
function EsotericaDetailView:SetSelectIndex(index)
    self.select_index = index
end

-- 创建左侧列表
function EsotericaDetailView:CreateEsotericaList()
	if not self.esoterica_list then
		self.esoterica_list = AsyncListView.New(EsotericaSlotRender, self.node_list["es_list"])
		self.esoterica_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectEsotericaItemHandler, self))
	end
end

-- 选择仙技列表项回调
function EsotericaDetailView:OnSelectEsotericaItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

	self.esoterica_slot_data = item.data
	self.esoterica_select_slot = item.data.slot
	self.esoterica_select_index = item.data.index

	self:FlushEsotericaDetailView()
end

-- 刷新仙技列表
function EsotericaDetailView:FlushEsotericaList()
	if nil ~= self.esoterica_list then
		self.esoterica_slot_list_data = CultivationWGData.Instance:GetEsotericaShowList()
        self.esoterica_list:SetDataList(self.esoterica_slot_list_data)

        -- 策划要求进来先跳转红点
        local esoterica_index = self:GetDefaultSelectIndex(self.esoterica_slot_list_data)
        self.esoterica_list:JumpToIndex(esoterica_index)
        

        -- local esoterica_index
        -- if self.select_index > 0 then
        --     esoterica_index = self.select_index
        --     self.select_index = -1
        --     ReDelayCall(self, function()
        --         self.esoterica_list:JumpToIndex(esoterica_index or 1)
        --     end, 0.1, "EsotericaDetailView")
        -- -- else
        -- --     esoterica_index = self:CalculateDefaultIndex(self.esoterica_slot_list_data)
        -- end
	end
end

function EsotericaDetailView:GetDefaultSelectIndex(esoterica_slot_list_data)
    local esoterica_index = self.select_index > 0 and self.select_index or 1

    local selct_remind = (esoterica_slot_list_data[esoterica_index] or {}).is_remind
    if selct_remind then
        return esoterica_index
    end

    if not IsEmptyTable(esoterica_slot_list_data) then
        for k, v in pairs(esoterica_slot_list_data) do
            if v.is_remind then
                return k
            end
        end
    end

    return esoterica_index
end

-- 得到第一个有红点的装备在listview里的下标
function EsotericaDetailView:CalculateDefaultIndex(equip_list)
    if nil ~= self.esoterica_select_slot_cache then
        for key, value in pairs(equip_list) do
            if value.slot == self.esoterica_select_slot_cache then
                return key
            end
        end
        return self.esoterica_select_slot_cache + 1
    end
	return 1
end

-------------------------------------防止其他地方要 做个保留
-- -- 滑动弧形列表 
-- ----[[
-- --需要在Release里调用
-- function EsotericaDetailView:DeleteEsotericaCamberedList()
-- 	if self.esoterica_cambered_list then
-- 		self.esoterica_cambered_list:DeleteMe()
-- 		self.esoterica_cambered_list = nil
-- 	end
-- end

-- --需要在load里调用
-- function EsotericaDetailView:InitEsotericaCamberedList()

-- 	self.esoterica_btn_select_index = -1
--     self.esoterica_drag_select_index = -1
-- 	self.esoterica_viewport_drag_count = 4

-- 	local cambered_list_data = {
-- 		item_render = EsotericaSlotRender,
-- 		asset_bundle = "uis/view/cultivation_ui_prefab",
-- 		asset_name = "esoterica_item",

-- 		scroll_list = self.node_list.es_list_part,
-- 		center_x = 220, center_y = 28,
-- 		radius_x = 300, radius_y = 280,
-- 		angle_delta = Mathf.PI / 6.2,
-- 		origin_rotation = Mathf.PI / 3.5,
-- 		is_drag_horizontal = false,
-- 		viewport_count = self.esoterica_viewport_drag_count,
-- 		speed = 1,
-- 		arg_speed = 0.2,
-- 		-- limit_drag_over_angle = 0.5,
--         is_clockwise_list = false,

-- 		click_item_cb = BindTool.Bind(self.OnClickEsotericaSlotBtn, self),
-- 		drag_to_next_cb = BindTool.Bind(self.OnDragEsotericaSlotToNextCallBack, self),
-- 		drag_to_last_cb = BindTool.Bind(self.OnDragEsotericaSlotToLastCallBack, self),
-- 		on_drag_end_cb = BindTool.Bind(self.OnDragEsotericaSlotEndCallBack, self),
-- 	}

--     self.esoterica_slot_list_data = CultivationWGData.Instance:GetEsotericaShowList()
-- 	self.esoterica_cambered_list = CamberedList.New(cambered_list_data)
-- 	self.esoterica_cambered_list:CreateCellList(#self.esoterica_slot_list_data)
-- 	local btn_item_list = self.esoterica_cambered_list:GetRenderList()
-- 	for k, item_cell in ipairs(btn_item_list) do
-- 		local item_data = self.esoterica_slot_list_data[k]
-- 		item_cell:SetData(item_data)
-- 	end
-- end

-- function EsotericaDetailView:OnClickEsotericaSlotBtn(item_cell, force_jump)
-- 	if item_cell == nil then
-- 		return
-- 	end

-- 	local select_index = item_cell:GetIndex()
-- 	local select_data = item_cell:GetData()
-- 	if select_data == nil then
-- 		return
-- 	end

-- 	if (not force_jump and self.esoterica_btn_select_index == select_index) then
-- 		return
-- 	end

--     self.esoterica_slot_data = select_data
--     self.esoterica_select_slot = select_data.slot
-- 	self.esoterica_btn_select_index = select_index
-- 	self:OnDragEsotericaSlotSelectedBtnChange(function ()
-- 		self:FlushEsotericaDetailView()
-- 	end, true)
-- end

-- -- 拖拽到下一个
-- function EsotericaDetailView:OnDragEsotericaSlotToNextCallBack()
-- 	local max_index = #self.esoterica_slot_list_data
-- 	self.esoterica_drag_select_index = self.esoterica_drag_select_index < self.esoterica_viewport_drag_count and self.esoterica_viewport_drag_count or self.esoterica_drag_select_index
-- 	self.esoterica_drag_select_index = self.esoterica_drag_select_index + 1
-- 	self.esoterica_drag_select_index = self.esoterica_drag_select_index > max_index and max_index or self.esoterica_drag_select_index
-- end

-- function EsotericaDetailView:OnDragEsotericaSlotToLastCallBack()
-- 	self.esoterica_drag_select_index = self.esoterica_drag_select_index - 1
-- 	self.esoterica_drag_select_index = self.esoterica_drag_select_index < self.esoterica_viewport_drag_count and self.esoterica_viewport_drag_count or self.esoterica_drag_select_index
-- end

-- function EsotericaDetailView:OnDragEsotericaSlotEndCallBack()
-- 	self:OnDragEsotericaSlotSelectedBtnChange(nil, nil, self.esoterica_drag_select_index)
-- end

-- function EsotericaDetailView:OnDragEsotericaSlotSelectedBtnChange(callback, is_click, drag_index)
-- 	if self.esoterica_cambered_list == nil then
-- 		return
-- 	end

-- 	local to_index = drag_index ~= nil and drag_index or self.esoterica_btn_select_index or 1
-- 	self.esoterica_cambered_list:ScrollToIndex(to_index, callback, is_click)
-- 	if is_click then
-- 		local item_list = self.esoterica_cambered_list:GetRenderList()
-- 		for k, item_cell in ipairs(item_list) do
-- 			item_cell:SetSelectedHL(to_index)
-- 		end
-- 	end
-- end
-- --]]
-- 需要在OnFlush里设置数据SetData
-------------------------------------
function EsotericaDetailView:ShowIndexCallBack()
    --self:FlushEsotericaModel()
    --self:PlayEsotericaModelTween(0)
end

function EsotericaDetailView:StopSequence_Esoterica()
    self:CancelTween()
end

function EsotericaDetailView:CancelTween()
    if self.esoterica_show_sequence then
        self.esoterica_show_sequence:Kill()
        self.esoterica_show_sequence = nil
    end
end

---模型动画
function EsotericaDetailView:PlayEsotericaModelTween(time)
    local cultivation_time = time or 0.5
    -- model
    self:CancelTween()
	self.esoterica_show_sequence = DG.Tweening.DOTween.Sequence()

	local model_transform = self.es_role_model:GetModelPosNode()
	self.esoterica_show_sequence:Join(model_transform:DOLocalMoveX(0.9, 0):SetEase(DG.Tweening.Ease.Linear))

	local effects_transform = self:GetUISceneEffectsTransform()
	self.esoterica_show_sequence:Join(effects_transform:DOLocalMoveX(0.54, 0):SetEase(DG.Tweening.Ease.Linear))

    self.esoterica_show_sequence:Join(model_transform:DOLocalMoveX(0, cultivation_time):SetEase(DG.Tweening.Ease.Linear))
    self.esoterica_show_sequence:Join(effects_transform:DOLocalMoveX(0, cultivation_time):SetEase(DG.Tweening.Ease.Linear))
end

function EsotericaDetailView:GetUISceneEffectsTransform()
	local ui_scene_cfg = Scene.Instance:GetUISceneCfg(UI_SCENE_TYPE.DEFAULT)
	if not IsEmptyTable(ui_scene_cfg) then
		return Scene.Instance:GetUIEffectsTransform(ui_scene_cfg.asset_name)
	end
	return nil
end

function EsotericaDetailView:FlushEsotericaModel()
	if not self:IsLoaded() then
		return
	end

	if nil == self.es_role_model then
		self.es_role_model = RoleModel.New()
		self.es_role_model:SetUISceneModel(nil, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		--self:AddUiRoleModel(self.es_role_model, {TabIndex.esoterica})
        self:AddUiRoleModel(self.es_role_model, 0)

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true, ignore_weapon = true}
		self.es_role_model:SetPositionAndRotation(nil,nil,Vector3(1.6, 1.6, 1.6))
		self.es_role_model:SetModelResInfo(role_vo, special_status_table, nil, SceneObjAnimator.Sit_Idle)
		self.es_role_model:SetWingResid(0)
	end

	if self.es_role_model then
		self.es_role_model:PlaySitAction()
		self.es_role_model:SetRotation({ x = 0, y = 180, z = 0 })
		self.es_role_model:FixToOrthographicOnUIScene()
	end
end


function EsotericaDetailView:OnFlush(param_t)
    local jump_slot
    if param_t then
        jump_slot = (param_t.all or {}).slot
    end

    self:FlushEsotericaList()
    self:FlushEsotericaDetailView()
end

--刷新整个界面
function EsotericaDetailView:FlushEsotericaDetailView()
    local slot_info = CultivationWGData.Instance:GetEsotericaSlotInfo(self.esoterica_select_slot)
    if not slot_info or not self.esoterica_slot_data then
        return
    end

    local slot_cfg = CultivationWGData.Instance:GetEsotericaCfg(self.esoterica_select_slot)
    if not slot_cfg then
        return
    end

    local level = slot_info.level
    local is_active = slot_info.is_active

    local skill_level = CultivationWGData.Instance:GetEsotericaSlotSkillLevel(self.esoterica_select_slot)
    --还没激活技能显示的等级信息就是1级
    local cur_skill_level = skill_level >= 1 and skill_level or 1
    local skill_cfg = SkillWGData.Instance:GetEsotericaSkillById(slot_cfg.skill_id, cur_skill_level)
    self.node_list.es_name_text.text.text = skill_cfg.skill_name

    --新刷新
    local info = CultivationWGData.Instance:GetEsotericaSlotPartInfo(self.esoterica_select_slot, 0)
    local item_id = info.item_id
    local is_inlay = item_id > 0
    if not is_inlay then
        item_id = CultivationWGData.Instance:GetEsotericaPartMinActItemid(self.esoterica_select_slot, 0)
    end

    local cur_part_level_cfg = CultivationWGData.Instance:GetEsotericaPartLevelCfg(self.esoterica_select_slot, 0, info.level)
    local stuff_item_id = is_inlay and cur_part_level_cfg.cost_item_id or item_id
    local had_num = CultivationWGData.Instance:GetEsotericaItemNum(stuff_item_id)
    local stuff_num = is_inlay and cur_part_level_cfg.cost_item_num or 1
    local is_remind = had_num >= stuff_num
    local color = is_remind and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.es_cost_num.text.text = string.format("(<color=%s>%d</color>/%d)", color, had_num, stuff_num)
    local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(stuff_item_id)
    --local bundle, asset = ResPath.GetItem(stuff_item_id)
    self.node_list.es_cost_item.image:LoadSpriteAsync(bundle, asset, function ()
        self.node_list.es_cost_item.image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetRawImagesPNG("a3_xf_rwlh" .. slot_cfg.img_id)
    self.node_list.esoterica_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["esoterica_img"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetRawImagesPNG("a3_xf_wb" .. slot_cfg.skill_img)
    self.node_list.skill_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["skill_img"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetA2Effect(slot_cfg.ui_effect_asset)
    self.node_list.effect:ChangeAsset(bundle, asset)

    local pos_x, pos_y = 0, 0
    if slot_cfg.img_pos and slot_cfg.img_pos ~= "" then
		local pos_list = string.split(slot_cfg.img_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
        RectTransform.SetAnchoredPositionXY(self.node_list.esoterica_img.rect, pos_x, pos_y)
	end

    local scale = slot_cfg.img_scale
    if scale and scale ~= "" then
        Transform.SetLocalScaleXYZ(self.node_list.esoterica_img.transform, scale, scale, scale)
	end

    local last_lv = level % 10
    local last_lv_bai = level / 10

    if last_lv == 0 and last_lv_bai ~= 0 then
        last_lv = 10
    end

    -- for i, v in ipairs(self.lv_list) do
    --     local str = i <= last_lv and "a3_hs_huo1" or "a3_hs_huo2"
    --     bundle, asset = ResPath.GetCultivationImg(str)
    --     v.image:LoadSprite(bundle, asset)

    --     if self.lv_effect[i] then
    --         self.lv_effect[i]:CustomSetActive(i <= last_lv)
    --     end
    -- end

    --local skill_client_cfg = SkillWGData.Instance:GetSkillClientAllConfigBySkillId(slot_cfg.skill_id)
    --local cur_skill_level = CultivationWGData.Instance:GetEsotericaSlotSkillLevel(self.esoterica_select_slot)
    --local list = CultivationWGData.Instance:GetEsotericaSkillLevelList(self.esoterica_select_slot)
    -- for i = 1, 3 do
    --     local data = list[i]
    --     if data then
    --         local color1 = cur_skill_level >= i and COLOR3B.C8 or COLOR3B.C4
    --         self.node_list["skill_action_text" .. i]:SetActive(true)
    --         self.node_list["skill_action_text" .. i].text.text = string.format(Language.Esoterica.SkillAction, color1, data.need_part_level, data.effect_desc)
    --     else
    --         self.node_list["skill_action_text" .. i]:SetActive(false)
    --     end
    -- end

    local uplevel_remind = CultivationWGData.Instance:GetEsotericaSlotUplevelRemind(self.esoterica_select_slot)
    local upstar_remind = CultivationWGData.Instance:GetEsotericaSlotPartRemind(self.esoterica_select_slot, 0)
    self.node_list.btn_es_cultivate_remind:SetActive(uplevel_remind or upstar_remind)

    self.node_list.skill_desc.text.text = slot_cfg.skill_desc

    if slot_cfg.skill_label and slot_cfg.skill_label ~= "" then
        self.node_list.skill_label_panel:SetActive(true)
        self.node_list.esoterica_label_bg:SetActive(true)
		local split_list = string.split(slot_cfg.skill_label, "|")
        for i = 1, 3 do
            if split_list[i] then
                self.node_list["skill_label_" .. i]:SetActive(true)
                self.node_list["skill_label_text_" .. i].text.text =split_list[i]
            else
                self.node_list["skill_label_" .. i]:SetActive(false)
            end
        end

        if split_list[1] then
            self.node_list["esoterica_label"].text.text = split_list[1]
        end
    else
        self.node_list.skill_label_panel:SetActive(false)
        self.node_list.esoterica_label_bg:SetActive(false)
	end
    -- self.node_list.es_name_text.text.text = string.format("%s LV.%d", self.esoterica_slot_data.name, level)

    -- 秘笈展示 换成人物模型展示
    -- if self.es_effect_show_slot ~= self.esoterica_select_slot then
    --     self.es_effect_show_slot = self.esoterica_select_slot
    --     local eff_bundle, eff_asset = ResPath.GetEffectUi(slot_cfg.ui_effect_asset)
    --     self.node_list["es_effect"]:ChangeAsset(eff_bundle, eff_asset)
    -- end

    -- 技能
    self:FlushEsotericaSkillView()

    -- 部位
    --self:FlushEsotericaPartView()

    local next_level_cfg = CultivationWGData.Instance:GetEsotericaLevelCfg(self.esoterica_select_slot, level + 1)
    local is_max = not next_level_cfg

    -- 属性
    local attr_list = CultivationWGData.Instance:GetEsotericaUpLevelShowAttr(self.esoterica_select_slot, level)

    local need_show_effect = false
    if nil ~= self.esoterica_select_slot_cache and nil ~= self.esoterica_select_slot_level_cache then
        if (self.esoterica_select_slot_cache == self.esoterica_select_slot) and (level > self.esoterica_select_slot_level_cache) then
            need_show_effect = true
        end
    end

    self.esoterica_select_slot_cache = self.esoterica_select_slot
    self.esoterica_select_slot_level_cache = level

    for k,v in ipairs(self.es_attr_list) do
        v:SetData(attr_list[k])
        v:SetRealHideNext(is_max)

        if need_show_effect then
            v:PlayAttrValueUpEffect()
        end
    end

    -- 战力
    self.node_list.cap_value.text.text = CultivationWGData.Instance:GetEsotericaSlotCapability(self.esoterica_select_slot)

    -- 状态
    --self.node_list.es_uplevel_part:SetActive(is_active)
    if is_active then
        local cur_level_cfg = CultivationWGData.Instance:GetEsotericaLevelCfg(self.esoterica_select_slot, level)
        if not cur_level_cfg then
            return
        end

        self.node_list.es_uplevel_max_flag:SetActive(is_max)
        self.node_list.es_uplevel_no_max_part:SetActive(not is_max)

        local max_level = CultivationWGData.Instance:GetEsotericaMaxLevel(self.esoterica_select_slot)
        self.node_list.es_text_level:SetActive(true)
        self.node_list.stage_uplevel_need_str:SetActive(false)
        self.node_list.es_text_level.text.text = string.format(Language.Esoterica.LevelDesc1, level, max_level)
        if is_max then
            self.node_list.btn_es_one_key_uplevel_remind:SetActive(false)
        else
            local cur_exp = CultivationWGData.Instance:GetEsotericaEXP()
            local show_color = cur_exp >= cur_level_cfg.need_exp and COLOR3B.GREEN or COLOR3B.RED
            self.node_list.es_text_exp.text.text = string.format("%d/%d", cur_exp, cur_level_cfg.need_exp)


            self.node_list.es_slider_exp.slider.value = cur_exp / cur_level_cfg.need_exp
            local is_remind = false

            local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
            local stage_meet = cur_stage >= cur_level_cfg.jingjie_level_limit

            local stage_cfg = CultivationWGData.Instance:GetXiuWeiStageCfgByState(cur_level_cfg.jingjie_level_limit)
            if stage_cfg then
                show_color = stage_meet and COLOR3B.GREEN or COLOR3B.RED
                self.node_list.stage_uplevel_need_str.text.text = string.format(Language.Esoterica.SlotUpLevelLimit, show_color, stage_cfg.stage_title)
            end

            self.node_list.btn_es_one_key_uplevel:CustomSetActive(stage_meet)
            self.node_list.stage_uplevel_need_str:CustomSetActive(not stage_meet)
            self.node_list.es_text_level:SetActive(stage_meet)

            is_remind = stage_meet and cur_exp >= cur_level_cfg.need_exp
            self.node_list.btn_es_one_key_uplevel_remind:SetActive(is_remind)
        end

        self.node_list.es_act_part:SetActive(true)
        self.node_list.es_unact_part:SetActive(false)
        return
    end

    local act_remind = CultivationWGData.Instance:GetEsotericaSlotActRemind(self.esoterica_select_slot)
    self.node_list.es_act_part:SetActive(false)
    self.node_list.es_unact_part:SetActive(true)
    self.node_list.btn_es_act:SetActive(true)
    self.node_list.btn_es_act_remind:SetActive(act_remind)
    --self.node_list.btn_up:SetActive(not act_remind)
    --self.node_list.btn_get_esoterica:SetActive(not act_remind)
    
    local part_act_num = CultivationWGData.Instance:GetEsotericaSlotActPartNum(self.esoterica_select_slot)
    local show_color = part_act_num >= 1 and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.es_act_condition1.text.text = string.format(Language.Esoterica.ActConditionStr1, show_color, part_act_num, 1)
    
    -- 境界条件
	local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
    local show_num = cur_stage >= slot_cfg.jingjie_level_limit and 1 or 0
    show_color = cur_stage >= slot_cfg.jingjie_level_limit and COLOR3B.GREEN or COLOR3B.RED
    local stage_cfg = CultivationWGData.Instance:GetXiuWeiStageCfgByState(slot_cfg.jingjie_level_limit)
    if not stage_cfg then
        return
    end

    self.node_list.es_act_condition2.text.text = string.format(Language.Esoterica.ActConditionStr2, "#fff8bb", stage_cfg.stage_title, show_color, show_num)
end

--刷新部位
function EsotericaDetailView:FlushEsotericaPartView()
    local part_list = CultivationWGData.Instance:GetEsotericaSlotPartInfoList(self.esoterica_select_slot)
    for k,v in pairs(self.es_part_list) do
        v:SetData(part_list[k])
    end
end

--刷新秘笈技能
function EsotericaDetailView:FlushEsotericaSkillView()
    local list = CultivationWGData.Instance:GetEsotericaSkillLevelList(self.esoterica_select_slot)
    local cur_skill_level = CultivationWGData.Instance:GetEsotericaSlotSkillLevel(self.esoterica_select_slot)
    for k,v in ipairs(self.es_skill_list) do
        v:SetData(list[k], cur_skill_level)
    end
end

-- 升级操作
function EsotericaDetailView:OnClickBtnEsotericaUplevel(is_auto)
    local slot_info = CultivationWGData.Instance:GetEsotericaSlotInfo(self.esoterica_select_slot)
    if not slot_info or not self.esoterica_slot_data then
        return
    end

    if not slot_info.is_active then
        return
    end

    local cur_level_cfg = CultivationWGData.Instance:GetEsotericaLevelCfg(self.esoterica_select_slot, slot_info.level)
    local next_level_cfg = CultivationWGData.Instance:GetEsotericaLevelCfg(self.esoterica_select_slot, slot_info.level + 1)
    if not cur_level_cfg or not next_level_cfg then
        return
    end

    local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
	if cur_stage < cur_level_cfg.jingjie_level_limit then
        local stage_cfg = CultivationWGData.Instance:GetXiuWeiStageCfgByState(cur_level_cfg.jingjie_level_limit)
        if stage_cfg then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Esoterica.SlotUpLevelLimitTips, stage_cfg.stage_title))
        end

		return
	end

    local cur_exp = CultivationWGData.Instance:GetEsotericaEXP()
    if cur_exp < cur_level_cfg.need_exp then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Esoterica.ExpNoEnough)
        return
    end

    CultivationWGCtrl.Instance:OnEsotericaOperate(ESOTERICA_OPERATE_TYPE.UPLEVEL, self.esoterica_select_slot, is_auto and 1 or 0)
end

-- 激活操作
function EsotericaDetailView:OnClickBtnEsotericaAct()
    --CultivationWGCtrl.Instance:OnEsotericaOperate(ESOTERICA_OPERATE_TYPE.ACTIVE, self.esoterica_select_slot)
    local item_id = CultivationWGData.Instance:GetEsotericaPartMinActItemid(self.esoterica_select_slot, 0)
	local had_num = CultivationWGData.Instance:GetEsotericaItemNum(item_id)
	if had_num < 1 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Esoterica.NoEnoughItem)
		return
	end

    local is_had_better, bag_index = CultivationWGData.Instance:GetEsotericaSlotPartHadBetter(self.esoterica_select_slot, 0)
    if is_had_better then
        CultivationWGCtrl.Instance:OnEsotericaOperate(ESOTERICA_OPERATE_TYPE.WEAR, self.esoterica_select_slot, 0, bag_index)
    end
end

function EsotericaDetailView:OnClickBtnEsotericaReslove()
    CultivationWGCtrl.Instance:OpenEsotericaResloveView()
end

function EsotericaDetailView:OnRemindChange(remind_name, num)
    if remind_name == RemindName.EsotericaReslove then
        if self.node_list.btn_es_resolve_remind then
            self.node_list.btn_es_resolve_remind:SetActive(num > 0)
        end
    end
end

function EsotericaDetailView:OnClickBtnEsotericaSkillShow()
    CultivationWGCtrl.Instance:OpenEsotericaSkillView({slot = self.esoterica_select_slot})
end

function EsotericaDetailView:OnClickBtnGetEsoterica()
    local is_xiuxian_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.XiuXianShiLian)
    if is_xiuxian_open then
        local cur_data_index = XiuXianShiLianWGData.Instance:GetMainViewDisplayChapterId()
        ViewManager.Instance:Open(GuideModuleName.XiuXianShiLian, nil, "all", {open_param = cur_data_index})
    else
        local fun_cfg = FunOpen.Instance:GetFunByName(GuideModuleName.XiuXianShiLian)
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.XiuXianShiLian.LockDes, fun_cfg.trigger_param))
    end
end

function EsotericaDetailView:OnClickBtnUp()
    ViewManager.Instance:Open(GuideModuleName.XiuWeiView)
end

--=================================================================
EsotericaSlotRender = EsotericaSlotRender or BaseClass(BaseRender)
function EsotericaSlotRender:__delete()

end

function EsotericaSlotRender:__init()

end


function EsotericaSlotRender:OnFlush()
	if not self.data then
		return
	end

    -- local bundle = "uis/view/cultivation_ui/images_atlas"
    -- local asset = "a3_xj_icon" .. self.data.slot
    -- self.node_list["icon"].image:LoadSprite(bundle, asset, function()
    --     self.node_list["icon"].image:SetNativeSize()
    -- end)

    self.node_list["remind"]:SetActive(self.data.is_remind)
    self.node_list.text_level.text.text = self.data.level
    self.node_list.name.text.text = self.data.name

    local bundle, asset = ResPath.GetCultivationImg("a3_xf_yq" .. self.data.img_id)
    self.node_list.bg.image:LoadSprite(bundle, asset, function ()
        self.node_list["bg"].image:SetNativeSize()
    end)
    -- XUI.SetGraphicGrey(self.node_list.img_bg, is_grey)

    if self.data.skill_label and self.data.skill_label ~= "" then
        self.node_list.label_bg:SetActive(true)
		local split_list = string.split(self.data.skill_label, "|")
        if split_list[1] then
            local label1 = split_list[1]:sub(1, 3)
		    local label2 = split_list[1]:sub(4, 6)
            self.node_list["label"].text.text = string.format(Language.Esoterica.LabelDesc, label2, label1)
        end
    else
        self.node_list.label_bg:SetActive(false)
	end

    self.node_list.lock_flag:SetActive(not self.data.is_active)
end

-- 选择框没有新的
function EsotericaSlotRender:OnSelectChange(is_select)
	-- local is_select = self.index == index
	self.node_list.select:SetActive(is_select)
end








--=================================================================
EsotericaSkillRender = EsotericaSkillRender or BaseClass(BaseRender)
function EsotericaSkillRender:__delete()
end

function EsotericaSkillRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

function EsotericaSkillRender:SetData(data, slot_skill_level)
	self.data = data
    self.slot_skill_level = slot_skill_level
	if self.has_load or self.is_use_objpool then
		self:OnFlush()
	else
		self:Flush()
	end
end

function EsotericaSkillRender:OnFlush()
	if not self.data then
		return
	end

    local slot_cfg = CultivationWGData.Instance:GetEsotericaCfg(self.data.seq)
    if not slot_cfg then
        return
    end

    local skill_id = slot_cfg.skill_id
    local is_act = self.slot_skill_level >= 1
    self.node_list["mask"]:CustomSetActive(not is_act)
    local show_level = is_act and self.slot_skill_level or self.data.level
    local skill_client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, show_level)
    if skill_client_cfg then
        local bundle, asset = ResPath.GetSkillIconById(skill_client_cfg.icon_resource)
        self.node_list["icon"].image:LoadSprite(bundle, asset, function()
            self.node_list["icon"].image:SetNativeSize()
        end)
    end
end

function EsotericaSkillRender:OnClick()
	if not self.data then
		return
	end

    local slot_cfg = CultivationWGData.Instance:GetEsotericaCfg(self.data.seq)
    if not slot_cfg then
        return
    end

    local is_act = self.slot_skill_level >= 1
    local skill_id = slot_cfg.skill_id
    local skill_level = is_act and self.slot_skill_level or self.data.level

    local skill_cfg = SkillWGData.Instance:GetEsotericaSkillById(skill_id, skill_level)
    local skill_client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	if not skill_cfg or not skill_client_cfg then
		return
	end

    
    local limit_text = not is_act and self.data.need_des or nil
	local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(skill_id)
    local skill_des = skill_client_cfg.description
    is_act = self.slot_skill_level >= 1

	local show_data = {
		icon = skill_client_cfg.icon_resource,
		top_text = skill_cfg.skill_name,	                    -- 技能名
		skill_level = skill_level,
		body_text = skill_des or "",							-- 当前等级技能描述
		limit_text = limit_text,
        is_lock = not is_act,
        hide_level = true,
		x = 0,
		y = 0,
		set_pos2 = true,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

--=================================================================
EsotericaPartRender = EsotericaPartRender or BaseClass(BaseRender)
function EsotericaPartRender:__init(instance)
	local bundle = "uis/view/cultivation_ui_prefab"
	local asset = "esoterica_part_item"
	self:LoadAsset(bundle, asset, instance.transform)
end

function EsotericaPartRender:__delete()
    if self.part_item then
        self.part_item:DeleteMe()
        self.part_item = nil
    end
end

function EsotericaPartRender:LoadCallBack()
    if not self.part_item and self.node_list.node then
        self.part_item = ItemCell.New(self.node_list.node)
    end

    if self.node_list.btn_block then
        XUI.AddClickEventListener(self.node_list.btn_block, BindTool.Bind(self.OnClick, self))
    end

    self.is_need_line = true
end

-- 设置点击回调
function EsotericaPartRender:SetClickCallback(click_callback)
end

function EsotericaPartRender:OnClick()
    if not self.data then
		return
	end

    local slot = self.data.slot
    local part = self.data.part
    local is_had_better, bag_index = CultivationWGData.Instance:GetEsotericaSlotPartHadBetter(slot, part)
    if is_had_better then
        CultivationWGCtrl.Instance:OnEsotericaOperate(ESOTERICA_OPERATE_TYPE.WEAR, slot, part, bag_index)
    else
        CultivationWGCtrl.Instance:OpenEsotericaPartView(slot, part)
    end
end

function EsotericaPartRender:SetSelectActive(visible)
    self.node_list.img_select:CustomSetActive(visible)
end

function EsotericaPartRender:OnFlush()
	if not self.data then
		return
	end
    local is_inlay = self.data.item_id > 0
    -- self.node_list.no_data:SetActive(not is_inlay)
    -- self.node_list.node:SetActive(is_inlay)

    local remind = CultivationWGData.Instance:GetEsotericaSlotPartRemind(self.data.slot, self.data.part)
    self.node_list.remind:SetActive(remind)
    self.node_list.no_inlay_remind_effect:SetActive(not is_inlay and remind)

    if is_inlay then
        self.part_item:SetData({item_id = self.data.item_id})
        local cur_part_level_cfg = CultivationWGData.Instance:GetEsotericaPartLevelCfg(self.data.slot, self.data.part, self.data.level)
        if cur_part_level_cfg then
            self.part_item:SetLeftTopImg(cur_part_level_cfg.show_star)
        end
    else
        self.part_item:SetData(nil)
        self.part_item:SetCanSelectChoose(true)
        -- self.node_list.type_str.text.text = Language.Esoterica.PartName[self.data.part]
    end
    self.node_list.text_part.text.text = Language.Esoterica.PartNameFlag[self.data.part]

    -- 两个界面用到，part_view下直接隐藏线条
    if self.is_need_line then
        self:SetGrey(not is_inlay)
        self:SetLinePos()
        self.node_list.img_select:CustomSetActive(false)
    else
        -- self:SetAllLineActive(false)
    end
    self:SetAllLineActive(false)
end

-- 设置是否需要线条 part_view界面不需要，同时需要选中效果
function EsotericaPartRender:SetIsNeedLine(is_need)
    self.is_need_line = is_need
end

-- 线条置灰
function EsotericaPartRender:SetGrey(is_grey)
    XUI.SetGraphicGrey(self.node_list.img_bg, is_grey)
    XUI.SetGraphicGrey(self.node_list.img_line_1, is_grey)
    XUI.SetGraphicGrey(self.node_list.img_line_2, is_grey)
    XUI.SetGraphicGrey(self.node_list.img_line_3, is_grey)

end

-- 设置线条的显示
function EsotericaPartRender:SetLinePos()
    local info=CultivationWGData.Instance:GetEsotericaLineShow(self.data.slot, self.data.part+1)
    if info then
        self.node_list.img_line_1:SetActive(info[1] == "1")
        self.node_list.img_line_2:SetActive(info[1] == "2")
        self.node_list.img_line_3:SetActive(info[1] == "3")
        self.node_list["img_line_"..info[1]].transform.localScale= Vector3(info[2], info[3], 1)
    end
end

-- 设置线条的显示隐藏
function EsotericaPartRender:SetAllLineActive(is_active)
    self.node_list.img_line_1:SetActive(is_active)
    self.node_list.img_line_2:SetActive(is_active)
    self.node_list.img_line_3:SetActive(is_active)
    self.node_list.img_bg:SetActive(is_active)
end
