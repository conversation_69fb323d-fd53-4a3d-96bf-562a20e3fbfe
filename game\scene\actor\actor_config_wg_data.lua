require("game/scene/actor/actor_wg_ctrl")
require("game/scene/actor/actor_trigger")
require("game/scene/actor/actor_trigger_base")
require("game/scene/actor/actor_trigger_effect")
require("game/scene/actor/actor_trigger_sound")
require("game/scene/actor/actor_trigger_camera_shake")
require("game/scene/actor/actor_trigger_radial_blur")

ActorConfigWGData = ActorConfigWGData or BaseClass()

function ActorConfigWGData:__init()
	self.prefab_data = nil
end

function ActorConfigWGData:__delete()
	self.prefab_data = nil
end

function ActorConfigWGData:SetPrefabData(data)
	self.prefab_data = data
end

function ActorConfigWGData:GetPrefabData()
	return self.prefab_data
end


