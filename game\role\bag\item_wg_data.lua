--------------------------------------------------------
--游戏中的背包，仓库，基础物品数据管理
--------------------------------------------------------
ItemWGData = ItemWGData or BaseClass()

ITEM_DINGZHI_TIP_VALUE = 1000 --永世弹窗配置值
ITEM_TIANMING_TIP_VALUE = 2000 --永世弹窗配置值

function ItemWGData:__init()
	if ItemWGData.Instance then
		ErrorLog("[ItemWGData] Attemp to create a singleton twice !")
	end

	ItemWGData.Instance = self

	self.max_knapsack_valid_num = 0 				--最大背包数
	self.hold_knapsack_num = 0						--占用的背包格子数

	self.max_storage_valid_num = 0 					--最大仓库数
	self.hold_storage_num = 0 						--占用的仓库格子数

	self.bag_data_list = {} 						--背包中的数据
	self.storage_data_list = {}						--仓库中的数据

	self.grid_data_list = {}						--拥有的相关物品信息
	self.notify_data_change_callback_list = {}		--物品有更新变化时进行回调
	self.notify_datalist_change_callback_list = {} 	--物品列表有变化时回调，一般是整理时，或初始化物品列表时
	self.notify_data_change_callback_map = {}
	self.colddown_info_list = {}					--物品CD信息
	self.notify_colddown_callback_list = {}			--物品CD改变回调
	self.use_times_list = {}						--物品使用次数

	self.delay_notice_list = {}
	Runner.Instance:AddRunObj(self, 8)
	self.break_color_flag = false					-- 自动分解设置(装备)
	self.treasure_color_flag = false				-- 自动分解设置(宝物)
	self.up_card_flag = false						-- 自动分解设置

	self.item_id_num_t = {}							-- 物品个数, id为key, num为value (不区分绑定非绑)

	self.show_red_gift_item_list = {}				-- 需要消耗仙玉的礼包，要显示红点 一天只显示一次 策划需求
	self.save_gift_drop_list = {}

	self.baoxiang_id = 0
	self.item_id_list = {}
	self.use_dan_list = {}
	self.stuff_data_list = {}
	self.equip_stone_list = {}
	self.equip_lingyu_list = {}
	self.shitian_stone_list = {}
	self.daohang_keling_store_list = {}
	self.hold_stuff_bag_num = 0

	self.dan_limit_cfg = ConfigManager.Instance:GetAutoConfig("role_level_reward_auto").exp_dan_every_limit
	local cfg = ConfigManager.Instance:GetAutoConfig("eternal_night_auto").equip
	self.m_eternal_night_auto = ListToMap(cfg,"equip_id")
	self.drop_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("drop_list_auto").drop_list, "drop_id")
	self.check_show_red_gift_item_event = BindTool.Bind(self.CheckItemGiftShowRedEvent,self)
	local agent_tip_cfg = ConfigManager.Instance:GetAutoConfig("agent_adapt_auto").agent_tip_desc
	self.agent_tip_info = ListToMapList(agent_tip_cfg, "item_id")
	self.special_beam_cfg = ConfigManager.Instance:GetAutoConfig("special_beam_auto").item_beam

	self.societgift_flower_item = { [26120] = 0, [26121] = 0, [26122] = 0, [26123] = 0, }
	self.societgift_xianlv_gift_item = { [27643] = 0, [27644] = 0, [27645] = 0, }
	self:InitGradeTipsTypeData()
end

function ItemWGData:__delete()
	ItemWGData.Instance = nil
	self.notify_data_change_callback_map = {}
	self.notify_data_change_callback_list = {}
	self.notify_datalist_change_callback_list = {}
	self.delay_notice_list = {}
	self.check_show_red_gift_item_event = nil
	self.societgift_flower_item = nil
	self.societgift_xianlv_gift_item = nil
	self.item_value_cfg = nil
	self.item_default_value = nil
	Runner.Instance:RemoveRunObj(self)
end

function ItemWGData:InitGradeTipsTypeData()
	self.grade_tips_type_cfgs = {}
	local expens_cfg = self:GetExpenseCfg()
	for k_1, v_1 in pairs(expens_cfg) do
		if v_1.grade_tips_type and v_1.grade_tips_type ~= "" and v_1.grade_tips_type > 0 then
			self.grade_tips_type_cfgs[v_1.grade_tips_type] = self.grade_tips_type_cfgs[v_1.grade_tips_type] or {}
			table.insert(self.grade_tips_type_cfgs[v_1.grade_tips_type], v_1.id)
		end
	end

	local other_cfg = self:GetOtherCfg()
	for k_1, v_1 in pairs(other_cfg) do
		if v_1.grade_tips_type and v_1.grade_tips_type ~= "" and v_1.grade_tips_type > 0 then
			self.grade_tips_type_cfgs[v_1.grade_tips_type] = self.grade_tips_type_cfgs[v_1.grade_tips_type] or {}
			table.insert(self.grade_tips_type_cfgs[v_1.grade_tips_type], v_1.id)
		end
	end
end

function ItemWGData:GetGradeTipsTypeData(grade_tips_type)
	return self.grade_tips_type_cfgs[grade_tips_type]
end

function ItemWGData:SetDataList(datalist)
	self.bag_data_list = {}
	self.storage_data_list = {}
	self.item_id_num_t = {}
	self.stuff_data_list = {}
	self.hold_knapsack_num = 0
	self.hold_storage_num = 0
	self.hold_stuff_bag_num = 0

	for _, v in pairs(datalist) do
		if v.index < COMMON_CONSTS.MAX_BAG_COUNT then
			self.bag_data_list[v.index] = v
			self.item_id_num_t[v.item_id] = (self.item_id_num_t[v.item_id] or 0) + v.num
			if v.num > 0 and v.item_id > 0 then
				self.hold_knapsack_num = self.hold_knapsack_num + 1
			end
			self:CheckScoietyGiftItem(v.item_id, v.num > 0 and 1 or 0)
		elseif v.index >= (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
			self.stuff_data_list[v.index - (COMMON_CONSTS.MAX_BAG_COUNT * 2)] = v
			self.item_id_num_t[v.item_id] = (self.item_id_num_t[v.item_id] or 0) + v.num
			if v.num > 0 and v.item_id > 0 then
				self.hold_stuff_bag_num = self.hold_stuff_bag_num + 1
			end
		else
			self.storage_data_list[v.index - COMMON_CONSTS.MAX_BAG_COUNT] = v
			if v.num > 0 and v.item_id > 0 then
				self.hold_storage_num = self.hold_storage_num + 1
			end
		end

		--当有的礼包需要消耗仙玉时，显示的红点 一天只显示一次
		local item_cfg = self:GetItemConfig(v.item_id)
		if item_cfg and item_cfg.need_gold and item_cfg.need_gold > 0 and not self:GetItemGiftShowRed(v.item_id) then
			self:SetItemGiftShowRed(v.item_id,true)
		end
	end

	self:UpdateEquipStoneList()
	self:UpdateEquipLingYuList()
	self:UpdateDaoHangEquipStoneList()
	self:UpdateShiTianStoneList()

	for k,v in pairs(self.notify_datalist_change_callback_list) do  --物品有变化，通知观察者，不带消息体
		v()
	end
end

--获得背包里的所有物品，一般只在初始化显示时来取
function ItemWGData:GetBagItemDataList()
	return self.bag_data_list
end

function ItemWGData:GetBagDataList()
	return self.grid_data_list
end

-- 获取当前仓库开启格子数
function ItemWGData:GetCurOpenStorgeNum()
	return self.max_storage_valid_num
end
-- 获取当前仓库未开启格子数
function ItemWGData:GetNotOpenStorgrNum()
	return COMMON_CONSTS.MAX_SRORGE_COUNT - self.max_storage_valid_num
end

--获得仓库里的所有物品，一般只在初始化显示时来取
function ItemWGData:GetStorgeItemDataList()
	return self.storage_data_list
end

function ItemWGData:GetStuffStorgeItemData()
	return self.stuff_data_list
end

function ItemWGData:GetEmptyNum()
	return self.max_knapsack_valid_num - self.hold_knapsack_num
end

-- 获取当前背包未开启格子数
function ItemWGData:GetNotOpenKnapsackNum()
	return COMMON_CONSTS.MAX_BAG_COUNT - self.max_knapsack_valid_num
end

function ItemWGData:GetStuffBagEmptyNum()
	return COMMON_CONSTS.MAX_STUFF_SRORGE_COUNT - self.hold_stuff_bag_num
end

function ItemWGData:GetBagItemNum()
	local bag_data_list = self:GetBagItemDataList()
	self.item_count = 0
	for k, v in pairs(bag_data_list) do
		self.item_count = self.item_count + 1
	end
	return self.item_count
end

--获得背包里的物品数量
function ItemWGData:GetItemNumInBagById(item_id)
	return self.item_id_num_t[item_id] or 0
end

--获得背包里的物品数量
function ItemWGData:GetItemNumInBagByIndex(index, item_id)
	local data = self:GetGridData(index)
	if data then
		if item_id then
			if data.item_id == item_id then
				return data.num
			end
		else
			return data.num
		end
	end
	return 0
end

--改变某个格中的数据
function ItemWGData:ChangeDataInGrid(data)
	if data == nil then
		return
	end

	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	local change_item_id = data.item_id
	local change_item_index = data.index
	local t = self:GetGridData(data.index)
	local need_old_param = {star = 0}

	local put_reason = data.reason_type --self.change_type
	local new_num = 0
	local old_num = 0
	if t ~= nil and data.num == 0 then --delete
		old_num = t.num
		new_num = 0
		change_reason = GameEnum.DATALIST_CHANGE_REASON_REMOVE
		change_item_id = t.item_id
		need_old_param.star = t.param and t.param.star_level or 0
		t = nil
	elseif t == nil then			   --add
		change_reason = GameEnum.DATALIST_CHANGE_REASON_ADD
		t = {}
	end

	if t ~= nil then
		old_num = t.num or 0
		new_num = data.num

		t.index = data.index
		t.item_id = data.item_id
		t.num = data.num
		t.is_bind = data.is_bind
		t.invalid_time = data.invalid_time
		if data.param then
			t.param = data.param
		end
		t.has_param = data.has_param
		t.gold_price = data.gold_price
		t.knapsack_type = data.knapsack_type
	end

	if data.index < COMMON_CONSTS.MAX_BAG_COUNT or data.index >= (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
		self.item_id_num_t[change_item_id] = (self.item_id_num_t[change_item_id] or 0) + (new_num - old_num)
	end

	if GameEnum.DATALIST_CHANGE_REASON_REMOVE == change_reason then
		if data.index < COMMON_CONSTS.MAX_BAG_COUNT then
			self:UpdateComposeEquipList(false, self:GetGridData(data.index), false)
			self.bag_data_list[data.index] = nil
			self.hold_knapsack_num = self.hold_knapsack_num - 1

			--当有的礼包需要消耗仙玉时，显示的红点 一天只显示一次 时间到了会移除礼包格子 红点标记也要移除掉
			local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
			if item_cfg and item_cfg.need_gold and item_cfg.need_gold > 0 and self:GetItemGiftShowRed(change_item_id) then
				self:SetItemGiftShowRed(change_item_id, false)
				self.now_check_show_red = true
				if self.check_show_red_gift_item_event then
					ReDelayCall(self, self.check_show_red_gift_item_event, 3, "check_show_red_gift_item_event")
				end
			end

		elseif data.index >= (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
			self.stuff_data_list[data.index - (COMMON_CONSTS.MAX_BAG_COUNT * 2)] = nil
			self.hold_stuff_bag_num = self.hold_stuff_bag_num - 1
		else
			self.storage_data_list[data.index - COMMON_CONSTS.MAX_BAG_COUNT] = nil
			self.hold_storage_num = self.hold_storage_num - 1
		end

	elseif GameEnum.DATALIST_CHANGE_REASON_ADD == change_reason then
		if data.index < COMMON_CONSTS.MAX_BAG_COUNT then
			self.bag_data_list[data.index] = t
			self.hold_knapsack_num = self.hold_knapsack_num + 1
			self:UpdateComposeEquipList(false, self:GetGridData(data.index), true)

			--当有的礼包需要消耗仙玉时，显示的红点 一天只显示一次
			local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			if item_cfg and item_cfg.need_gold and item_cfg.need_gold > 0 and not self:GetItemGiftShowRed(data.item_id) then
				self:SetItemGiftShowRed(data.item_id, true)
			end
		elseif data.index >= (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
			self.stuff_data_list[data.index - (COMMON_CONSTS.MAX_BAG_COUNT * 2)] = t
			self.hold_stuff_bag_num = self.hold_stuff_bag_num + 1
		else
			self.storage_data_list[data.index - COMMON_CONSTS.MAX_BAG_COUNT] = t
			self.hold_storage_num = self.hold_storage_num + 1
		end

	end

	if data.index >= (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
		if EquipmentWGData.Instance:IsBaoShiItem(change_item_id) then
			self:UpdateEquipStoneList()
		end

		if EquipmentLingYuWGData.Instance:IsLingYuItem(change_item_id) then
			self:UpdateEquipLingYuList()
		end

		if MultiFunctionWGData.Instance:IsDaoHangKeLingStore(change_item_id) then
			self:UpdateDaoHangEquipStoneList()
		end

		if ShiTianSuitStrengthenWGData.Instance:IsShiTianStone(change_item_id) then
			self:UpdateShiTianStoneList()
		end
	end

	if change_reason ~= GameEnum.DATALIST_CHANGE_REASON_REMOVE
		and (put_reason == PUT_REASON_TYPE.PUT_REASON_LUCKYROLL_CS
			or put_reason == PUT_REASON_TYPE.PUT_RANDACT_DAILYONLINELOTTERY
			or put_reason == PUT_REASON_TYPE.PUT_REASON_ONLINE_REWARD
			or put_reason == PUT_REASON_TYPE.PUT_REASON_RA_LUCKY_DRAW_REWARD
			or put_reason == PUT_REASON_TYPE.PUT_REASON_TOUCH_GOLD_REWARD
			or put_reason == PUT_REASON_TYPE.PUT_REASON_RA_TIANSHEN_XUNBAO
            or put_reason == PUT_REASON_TYPE.PUT_REASON_ZHOU_YI_YUN_CHENG
            or put_reason == PUT_REASON_TYPE.PUT_REASON_RA_YANHUA_SHENGDIAN_2_REWARD
            or put_reason == PUT_REASON_TYPE.PUT_REASON_FISH
            or put_reason == PUT_REASON_TYPE.PUT_REASON_TIAN_SHEN_BAO_XIA_REWARD
            or put_reason == PUT_REASON_TYPE.PUT_REASON_FESTIVAL_TURN_TABLE
            or put_reason == PUT_REASON_TYPE.PUT_REASON_CROSS_ACT_GOLD_ZHUANGPAN
			or put_reason == PUT_REASON_TYPE.PUT_REASON_TU_NV_LANG
			or put_reason == PUT_REASON_TYPE.PUT_REASON_Glory_Crystal_TABLE
			or put_reason == PUT_REASON_TYPE.PUT_REASON_YINJI_TURN_TABLE 
			or put_reason == PUT_REASON_TYPE.PUT_REASON_SPECIAL_DRAW
			or put_reason == PUT_REASON_TYPE.PUT_REASON_OGADRAWREWAD
			or ControlBeastsWGCtrl.Instance:GetIsBlockBeastItemAddTips()) then -- 新增幻兽特殊处理

		local notice_t = {
			change_item_id = change_item_id,
			change_item_index = change_item_index,
			change_reason = change_reason,
			put_reason = put_reason,
			old_num = old_num,
			new_num = new_num,
			need_old_param = need_old_param,
			notice_time_stamp = 0,
		}

		if put_reason == PUT_REASON_TYPE.PUT_REASON_TOUCH_GOLD_REWARD then
			notice_t.notice_time_stamp = Status.NowTime + 3
		elseif put_reason == PUT_REASON_TYPE.PUT_REASON_LUCKYROLL then --幸运转盘
			notice_t.notice_time_stamp = Status.NowTime + 4
		elseif put_reason == PUT_REASON_TYPE.PUT_REASON_RA_TIANSHEN_XUNBAO then -- 天神夺宝
			local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
			if PlayerPrefsUtil.GetInt("god_draw" .. role_id) == 1 then
				notice_t.notice_time_stamp = Status.NowTime
			else
				local delay_time = GodGetRewardWGData.Instance:GetAnimDelayTime()
				notice_t.notice_time_stamp = Status.NowTime + delay_time
			end
		elseif put_reason == PUT_REASON_TYPE.PUT_REASON_ZHOU_YI_YUN_CHENG then--周一运程
			local delay = ZhouYiYunChengWGData.Instance:GetJumpAni() and 0 or 5
			notice_t.notice_time_stamp = Status.NowTime + delay
        elseif put_reason == PUT_REASON_TYPE.PUT_REASON_FISH then--运营活动幸运锦鲤，捕鱼
            local is_do_tween = OperationActivityWGCtrl.Instance and OperationActivityWGCtrl.Instance:GetIsDoTween()
			if is_do_tween then --有播动画
				notice_t.notice_time_stamp = Status.NowTime + OAFishWGData.Instance:GetNoticeDelayTime()
			else
				notice_t.notice_time_stamp = Status.NowTime
			end
		elseif put_reason == PUT_REASON_TYPE.PUT_REASON_TU_NV_LANG then--兔女郎宝藏
			if OATurnTableWGData.Instance and OATurnTableWGData.Instance:GetIsTurningTable() then
				notice_t.notice_time_stamp = Status.NowTime + 6
			else
				notice_t.notice_time_stamp = Status.NowTime
            end
        elseif put_reason == PUT_REASON_TYPE.PUT_REASON_TIAN_SHEN_BAO_XIA_REWARD then--天神宝匣抽奖
        	TianShenWGData.Instance:SetTSBoxRewardTipCache(notice_t)
        	return
        elseif put_reason == PUT_REASON_TYPE.PUT_REASON_FESTIVAL_TURN_TABLE then
        	local delay = FestivalTurnTableWGData.Instance:GetJumpAni() and 0 or 5
			notice_t.notice_time_stamp = Status.NowTime + delay
        elseif put_reason == PUT_REASON_TYPE.PUT_REASON_RA_YANHUA_SHENGDIAN_2_REWARD then--节日活动烟花盛典扭蛋机
			notice_t.notice_time_stamp = Status.NowTime + NiuDanWGData.Instance:GetDelayTime()
        elseif put_reason == PUT_REASON_TYPE.PUT_REASON_CROSS_ACT_GOLD_ZHUANGPAN then--仙玉转盘
			notice_t.notice_time_stamp = Status.NowTime + XianyuTrunTableWGData.Instance:GetDelayTime()
		elseif put_reason == PUT_REASON_TYPE.PUT_REASON_YINJI_TURN_TABLE then
			notice_t.notice_time_stamp = Status.NowTime + EveryDayYinJiTurnTableWGData.Instance:GetRewardDelayTime()
		elseif put_reason == PUT_REASON_TYPE.PUT_REASON_Glory_Crystal_TABLE then
			notice_t.notice_time_stamp = Status.NowTime + GloryCrystalWGData.Instance:GetDelayTime()
		elseif put_reason == PUT_REASON_TYPE.PUT_REASON_SPECIAL_DRAW then
			notice_t.notice_time_stamp = Status.NowTime + SpecialActivityWGData.Instance:GetDelayTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
		elseif put_reason == PUT_REASON_TYPE.PUT_REASON_OGADRAWREWAD then
			local is_skip = ServerActivityWGData.Instance:GetIsSkipOGALotteryAnim()
			local delay_time = is_skip and 0 or 3.5
			notice_t.notice_time_stamp = Status.NowTime + delay_time
		elseif ControlBeastsWGCtrl.Instance:GetIsBlockBeastItemAddTips() then
			notice_t.notice_time_stamp = Status.NowTime + 9999
		else
			notice_t.notice_time_stamp = Status.NowTime + 5
		end

		table.insert(self.delay_notice_list, notice_t)
		return
	end
	-- cocos2d项目代码暂时屏蔽
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD and change_item_id ~= 0 then
		GlobalEventSystem:Fire("MainUIViewChat_ItemDataGet",change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	end

	self:CheckScoietyGiftItem(change_item_id, new_num > 0 and 1 or 0)
	self:NoticeOneItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num, need_old_param)
	self:CheckAutoBreakHandler(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	self:CheckAutoTreasureHandler(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
end

function ItemWGData:NoticeOneItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num, need_old_param)
	for k,v in pairs(self.notify_data_change_callback_list) do  --物品有变化，通知观察者，带消息体
		v(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num, need_old_param)
	end

	-- 获得物品提示
	if put_reason ~= PUT_REASON_TYPE.PUT_REASON_INVALID and put_reason ~= PUT_REASON_TYPE.PUT_REASON_NO_NOTICE
	and old_num < new_num then
		local item_data = self:GetGridData(change_item_index)
		BagWGCtrl.Instance:AddItemTips(change_item_id, old_num, new_num, put_reason, change_item_index, item_data)
	-- 	self:NoticeEffectOnGetItem(item_data, old_num, put_reason, is_daily_show)
	end
end

function ItemWGData:GetNotifyCallBackNum()
	local num = 0
	for _, v in pairs(self.notify_data_change_callback_list) do
		num = num + 1
	end

	for _, v in pairs(self.notify_datalist_change_callback_list) do
		num = num + 1
	end

	return num
end

function ItemWGData:NoticeEffectOnGetItem(data, old_num, put_reason)
	if data == nil then
		return
	end

	if data.num > old_num and put_reason ~= PUT_REASON_TYPE.PUT_REASON_INVALID and put_reason ~= PUT_REASON_TYPE.PUT_REASON_NO_NOTICE then
		if GodGetRewardWGCtrl.Instance:ViewIsOpen() then
			return
		end

		local item_cfg = self:GetItemConfig(data.item_id)
		if item_cfg ~= nil then
			if item_cfg.bag_effect == nil or item_cfg.bag_effect ~= 0 then
				BagWGCtrl.Instance:PlayAnimationOnGetItem(data.item_id)
			end
		end
	end
end

function ItemWGData:HandleDelayNoticeNow(put_reason)
	for i = #self.delay_notice_list, 1, -1 do
		local t = table.remove(self.delay_notice_list, i)
		if t ~= nil and (put_reason == nil or t.put_reason == put_reason) then
			self:NoticeOneItemChange(t.change_item_id, t.change_item_index, t.change_reason, t.put_reason, t.old_num, t.new_num, t.need_old_param)
		end
	end
end

function ItemWGData:ChangeParamInGrid(data)
	local t = self:GetGridData(data.index)
	if t ~= nil then
		local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
		local change_item_id = t.item_id
		local change_item_index = data.index
		if data.param then
	   		t.param = data.param
		end
		for k,v in pairs(self.notify_data_change_callback_list) do  --物品有变化，通知观察者，带消息体
			v(change_item_id, change_item_index, change_reason)
		end
	end
end

function ItemWGData:Update(now_time, elapse_time)
	if #self.delay_notice_list > 0 then
		for i = #self.delay_notice_list, 1, -1 do
			if now_time > self.delay_notice_list[i].notice_time_stamp then
				local t = table.remove(self.delay_notice_list, i)
				self:NoticeOneItemChange(t.change_item_id, t.change_item_index, t.change_reason, t.put_reason, t.old_num, t.new_num, t.need_old_param)
			end
		end
	end
end

--绑定数据改变时的回调方法.用于任意物品有更新时进行回调
function ItemWGData:NotifyDataChangeCallBack(callback, notify_datalist)
	if callback == nil then
		return
	end

	self.notify_data_change_callback_map[callback] = true

	if notify_datalist == true then
		for k,v in pairs(self.notify_datalist_change_callback_list) do
			if v == callback then
				return
			end
		end

		table.insert(self.notify_datalist_change_callback_list, callback)
		-- if #self.notify_datalist_change_callback_list >= 30 then
		-- 	print_error(string.format("监听物品List数据的地方多达%d条，请检查！",#self.notify_datalist_change_callback_list))
		-- end

	else
		for k,v in pairs(self.notify_data_change_callback_list) do
			if v == callback then
				return
			end
		end

		table.insert(self.notify_data_change_callback_list, callback)
		-- if #self.notify_data_change_callback_list >= 30 then
		-- 	print_error(string.format("监听物品数据的地方多达%d条，请检查！",#self.notify_data_change_callback_list))
		-- end
	end
end

--移除绑定回调
function ItemWGData:UnNotifyDataChangeCallBack(callback)
	if callback == nil then
		return
	end

	self.notify_data_change_callback_map[callback] = nil

	for i,v in ipairs(self.notify_data_change_callback_list) do
		if v == callback then
			table.remove(self.notify_data_change_callback_list, i)
			break
		end
	end

	for i,v in ipairs(self.notify_datalist_change_callback_list) do
		if v == callback then
			table.remove(self.notify_datalist_change_callback_list, i)
			break
		end
	end
end

--获得物品分数
function ItemWGData:GetItemScore(item_data)
	if item_data == nil then
		return 0
	end

	local pingfen = 0
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	-- if item_data.param and item_cfg.sub_type == GameEnum.EQUIP_TYPE_HUNJIE then                             -- 情缘装备特殊处理
	-- 	local qingyuan_attr = MarryWGData.Instance:GetEquipCfgById(item_data.item_id, item_data.param.star_level)
	-- 	pingfen = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(qingyuan_attr))
	-- else
		local pingfencfg = self:GetItemConfig(item_data.item_id)
		pingfen = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(pingfencfg))
	-- end

	return pingfen
end

--获取装备配置
function ItemWGData:GetEquipmentCfg()
	if self.m_equipment_auto == nil then
		self.m_equipment_auto = ConfigManager.Instance:GetAutoItemConfig("equipment_auto")
	end
	return self.m_equipment_auto
end

function ItemWGData:GetEquipmentCfgByID(id)
	local cfg = ConfigManager.Instance:GetAutoItemConfig("equipment_auto")[id]
	return cfg
end

function ItemWGData:GetExpenseCfg()
	if self.m_expense_auto == nil then
		self.m_expense_auto = ConfigManager.Instance:GetAutoItemConfig("expense_auto")
	end
	return self.m_expense_auto
end

function ItemWGData:GetGiftCfg()
	if self.m_gift_auto == nil then
		self.m_gift_auto = ConfigManager.Instance:GetAutoItemConfig("gift_auto")
	end
	return self.m_gift_auto
end

function ItemWGData:GetOtherCfg()
	if self.m_other_auto == nil then
		self.m_other_auto = ConfigManager.Instance:GetAutoItemConfig("other_auto")
	end
	return self.m_other_auto
end

function ItemWGData:GetVirtualCfg()
	if self.m_virtual_auto == nil then
		self.m_virtual_auto = ConfigManager.Instance:GetAutoItemConfig("virtual_auto")
	end
	return self.m_virtual_auto
end

function ItemWGData:GetChestShopCfg()
	if self.m_chestshop_auto == nil then
		self.m_chestshop_auto = ConfigManager.Instance:GetAutoConfig("chestshop_auto")
	end
	return self.m_chestshop_auto
end

function ItemWGData:GetEternalNightEquipCfg()
	return self.m_eternal_night_auto
end

function ItemWGData:GetAgentTipsDesc(item_id)
	local tip_desc = {}
	local spid = CHANNEL_AGENT_ID
	local tip_info = self.agent_tip_info[item_id]
	if not IsEmptyTable(tip_info) then
		for k, v in pairs(tip_info) do
			if spid == v.a_spid or spid == v.i_spid then
				tip_desc = {title = v.biaoti, desc = v.description}
			end
		end

		return tip_desc
	end

	return nil
end

--获得物品配置
function ItemWGData:GetItemConfig(item_id)
	if item_id == nil then
		return nil, nil
	end
	local item_cfg = nil
	item_cfg = self:GetEquipmentCfg()[item_id]
	if nil ~= item_cfg then return item_cfg, GameEnum.ITEM_BIGTYPE_EQUIPMENT end

	item_cfg = self:GetExpenseCfg()[item_id]
	if nil ~= item_cfg then return item_cfg, GameEnum.ITEM_BIGTYPE_EXPENSE end

	item_cfg = self:GetGiftCfg()[item_id]
	if nil ~= item_cfg then return item_cfg, GameEnum.ITEM_BIGTYPE_GIF end

	item_cfg = self:GetOtherCfg()[item_id]
	if nil ~= item_cfg then return item_cfg, GameEnum.ITEM_BIGTYPE_OTHER end

	item_cfg = self:GetVirtualCfg()[item_id]
	if nil ~= item_cfg then return item_cfg, GameEnum.ITEM_BIGTYPE_VIRTUAL end

	--永夜之巅的虚拟物品
	item_cfg = self:GetEternalNightEquipCfg()[item_id]
	if nil ~= item_cfg then return item_cfg, GameEnum.ITEM_BIGTYPE_VIRTUAL end

	return nil, nil
end

function ItemWGData:GetExpenseItemConfig(item_id)
	return self:GetExpenseCfg()[item_id]
end

--获取礼包配置
function ItemWGData:GetGiftConfig(gift_id)
	local gift_cfg = self:GetGiftCfg()[gift_id]
	local main_prof = GameVoManager.Instance:GetMainRoleVo().prof % 10
	local giftData, item_cfg
	local i = 1
	if gift_cfg then
		giftData = __TableCopy(gift_cfg)
        giftData.item_data = {}
        local drop_list = self:GetConformPlayerDropList()
		for k,v in pairs(giftData.reward_item) do
			item_cfg = self:GetItemConfig(v.item_id)
			if item_cfg then
				if 0 == giftData.is_check_prof or (item_cfg.limit_prof == main_prof or item_cfg.limit_prof == 5) then
					giftData.item_data[i] = __TableCopy(item_cfg)
					giftData.item_data[i].num = v.num
					giftData.item_data[i].isbind = v.is_bind
					giftData.item_data[i].item_id = v.item_id
					giftData.item_data[i].star_level = gift_cfg.equip_star
					giftData.item_data[i].param = {}
					giftData.item_data[i].param.star_level = gift_cfg.equip_star
					i = i + 1
				end
            else
                local drop_id = v.item_id  --item_id有可能是掉落表id，，
                if drop_list and drop_list[drop_id] and drop_list[drop_id][1] then
                    local drop_data = drop_list[drop_id][1]
                    item_cfg = self:GetItemConfig(drop_data.item_id)
                    if item_cfg then
                        giftData.item_data[i] = __TableCopy(item_cfg)
                        giftData.item_data[i].num = v.num
                        giftData.item_data[i].isbind = v.bind
                        giftData.item_data[i].item_id = drop_data.item_id
                        giftData.item_data[i].star_level = drop_data.param
                        giftData.item_data[i].param = {}
                        giftData.item_data[i].param.star_level = drop_data.param
                        i = i + 1
                    else
                        print_error("物品没有配置 >>> ", drop_data.item_id)
                    end
                end
                if IsEmptyTable(item_cfg) then
                    print_error("物品没有配置 >>> ", v.item_id)
                end
			end
		end
	end
	return giftData
end

--获取重置礼包配置
function ItemWGData:GetResetGiftConfig(gift_id)
	local gift_cfg = self:GetGiftCfg()[gift_id]
	local main_prof = GameVoManager.Instance:GetMainRoleVo().prof % 10
	local giftData, item_cfg
	local i = 1
	if gift_cfg then
		giftData = {}
		setmetatable(giftData, {__index = gift_cfg})
		giftData.item_data = {}
        local drop_list = self:GetConformPlayerDropList()
		for j=1,#giftData.reward_item + 1 do
			item_cfg = self:GetItemConfig(giftData.reward_item[j - 1].item_id)
			if item_cfg then
				if 0 == giftData.is_check_prof or (item_cfg.limit_prof == main_prof or item_cfg.limit_prof == 5) then
					giftData.item_data[i] = __TableCopy(item_cfg)
					if ShenShouWGData.Instance:GetIsShenShouEquip(giftData.reward_item[j - 1].item_id) then
						giftData.item_data[i].num = giftData.reward_item[j - 1].num
						giftData.item_data[i].isbind = giftData.reward_item[j - 1].is_bind
						giftData.item_data[i].item_id = giftData.reward_item[j - 1].item_id
						giftData.item_data[i].star_count = gift_cfg.equip_star
						giftData.item_data[i].strength_level = 0
						i = i + 1
					else
						giftData.item_data[i].num = giftData.reward_item[j - 1].num
						giftData.item_data[i].isbind = giftData.reward_item[j - 1].is_bind
						giftData.item_data[i].item_id = giftData.reward_item[j - 1].item_id
						giftData.item_data[i].star_level = gift_cfg.equip_star
						giftData.item_data[i].param = {}
						giftData.item_data[i].param.star_level = gift_cfg.equip_star
						i = i + 1
					end
				end
            else
                local v = giftData.reward_item[j - 1]
                local drop_id = v.item_id  --item_id有可能是掉落表id，，
                if drop_list and drop_list[drop_id] and drop_list[drop_id][1] then
                    local drop_data = drop_list[drop_id][1]
                    item_cfg = self:GetItemConfig(drop_data.item_id)
                    if item_cfg then
                        giftData.item_data[i] = __TableCopy(item_cfg)
                        giftData.item_data[i].num = v.num
                        giftData.item_data[i].isbind = v.bind
                        giftData.item_data[i].item_id = drop_data.item_id
                        giftData.item_data[i].star_level = drop_data.param
                        giftData.item_data[i].param = {}
                        giftData.item_data[i].param.star_level = drop_data.param
                        i = i + 1
                    else
                        print_error("物品没有配置 >>> ", drop_data.item_id)
                    end
                end
                if IsEmptyTable(item_cfg) then
                    print_error("物品没有配置 >>> ", v.item_id)
                end

			end
		end
	end

	return giftData
end

--根据物品id获得在【背包、材料背包】中的index
function ItemWGData:GetItemIndex(item_id)
	for k,v in pairs(self.bag_data_list) do
		if v.item_id == item_id then
			return v.index
		end
	end

	for k,v in pairs(self.stuff_data_list) do
		if v.item_id == item_id then
			return v.index
		end
	end

	return -1
end

--根据物品id获得在【背包】中所有的index
function ItemWGData:GetItemListIndex(item_id)
	local index_list = {}
	for k,v in pairs(self.bag_data_list) do
		if v.item_id == item_id then
			table.insert(index_list, v.index)
		end
	end
	return index_list
end

--根据物品id获得在【材料背包】中所有的index
function ItemWGData:GetItemListIndexByStuffBag(item_id)
	local index_list = {}
	for k,v in pairs(self.stuff_data_list) do
		if v.item_id == item_id then
			table.insert(index_list, v.index)
		end
	end
	return index_list
end

--根据物品id获得在【背包】 中物品（如果同个物品出现在多个格子只能拿到第一个）
function ItemWGData:GetItem(item_id)
	for k,v in pairs(self.bag_data_list) do
		if v.item_id == item_id then
			return v
		end
	end
	return nil
end

--根据物品id获得在 【材料背包】 中物品（如果同个物品出现在多个格子只能拿到第一个）
function ItemWGData:GetStuffItem(item_id)
	for k,v in pairs(self.stuff_data_list) do
		if v.item_id == item_id then
			return v
		end
	end
	return nil
end

--根据物品id获得在【背包】中物品（根据count计算第几个）
function ItemWGData:GetItemAndCount(item_id,count)
	local num = 0
	for k,v in pairs(self.bag_data_list) do
		if v.item_id == item_id then
			num = num + 1
			if num == count then
				return v
			end
		end
	end
	return nil
end

--根据物品id获得在【材料背包】中物品（根据count计算第几个）
function ItemWGData:GetItemAndCountByStuffBag(item_id,count)
	local num = 0
	for k,v in pairs(self.stuff_data_list) do
		if v.item_id == item_id then
			num = num + 1
			if num == count then
				return v
			end
		end
	end
	return nil
end

--连续使用同种物品
function ItemWGData:GetContinueUseItem(item_id, num)
	local tab = {}
	local total_num = num
	for k,v in pairs(self.bag_data_list) do
		if v.item_id == item_id then
			local tab2 = {}
			tab2.index = v.index
			tab2.num = v.num
			table.insert(tab, tab2)
			total_num = total_num - v.num
			if total_num <= 0 then
				tab[#tab].num = v.num + total_num
				return tab
			end
		end
	end
	return nil
end

function ItemWGData:IsValidItem(item_index, item_id)
	local data = self:GetGridData(item_index)

	if nil ~= data and data.item_id == item_id then
		return true
	end

	return false
end

--根据物品id获得在背包中物品 限时物品，获得时间最早的一个
function ItemWGData:GetItemTime(item_id)
	local time = 0
	local data = nil
	for k,v in pairs(self.bag_data_list) do
		if v.item_id == item_id then
			if time == 0 then
				time = v.invalid_time
				data = v
			elseif time < v.invalid_time then
				time = v.invalid_time
				data = v
			end
		end
	end
	return data
end

--根据物品id获得在背包中物品 限时物品，获得时间最最晚的一个
function ItemWGData:GetItemTimeLastOne(item_id)
	local time = 0
	local data = nil
	for k,v in pairs(self.bag_data_list) do
		if v.item_id == item_id then
			if time == 0 then
				time = v.invalid_time
				data = v
			elseif time < v.invalid_time then
				time = v.invalid_time
				data = v
			end
		end
	end
	return data
end

--背包里是否有某个物品
function ItemWGData:GetHasItemInBag(item_id)
	local  index = self:GetItemIndex(item_id)
	if index >= 0 then
		return true
	else
		return false
	end
end

--获得某个格子的数据
function ItemWGData:GetGridData(index)
	if index == nil then
		return nil
	end

	local data = self.bag_data_list[index]
	if index >= (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
		data = self.stuff_data_list[index - (COMMON_CONSTS.MAX_BAG_COUNT * 2)]
	elseif index >= COMMON_CONSTS.MAX_BAG_COUNT and index < (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
		data = self.storage_data_list[index - COMMON_CONSTS.MAX_BAG_COUNT]
	end
	return data
end

-- 通过sub_type获取装备所在部位
function ItemWGData.GetEquipTypeName(sub_type)
	local part = -1
	if sub_type == GameEnum.EQUIP_TYPE_TOUKUI then
		part = GameEnum.EQUIP_INDEX_TOUKUI								--头盔
	elseif sub_type == GameEnum.EQUIP_TYPE_YIFU then
		part = GameEnum.EQUIP_INDEX_YIFU								--衣服
	elseif sub_type == GameEnum.EQUIP_TYPE_KUZI then
		part = GameEnum.EQUIP_INDEX_KUZI								--裤子
	elseif sub_type == GameEnum.EQUIP_TYPE_XIANLIAN then
		part = GameEnum.EQUIP_INDEX_XIANLIAN							--仙链
	elseif sub_type == GameEnum.EQUIP_TYPE_XIEZI then
		part = GameEnum.EQUIP_INDEX_XIEZI								--鞋子
	elseif sub_type == GameEnum.EQUIP_TYPE_WUQI then
		part = GameEnum.EQUIP_INDEX_WUQI								--武器
	elseif sub_type == GameEnum.EQUIP_TYPE_XIANZHUI then
		part = GameEnum.EQUIP_INDEX_XIANZHUI							--仙坠
	elseif sub_type == GameEnum.EQUIP_TYPE_XIANFU then
		part = GameEnum.EQUIP_INDEX_XIANFU								--仙符
	elseif sub_type == GameEnum.EQUIP_TYPE_XIANJIE then
		part = GameEnum.EQUIP_INDEX_XIANJIE								--戒指
	elseif sub_type == GameEnum.EQUIP_TYPE_XIANZHUO then
		part = GameEnum.EQUIP_INDEX_XIANZHUO							--仙镯
	elseif sub_type == GameEnum.EQUIP_TYPE_HUFU then
		part = GameEnum.EQUIP_INDEX_HUFU								--护符
	elseif sub_type == GameEnum.EQUIP_TYPE_MIANJIA then
		part = GameEnum.EQUIP_INDEX_MIANJIA								--面甲
	elseif sub_type == GameEnum.EQUIP_TYPE_XIANGLIAN_1 then
		part = GameEnum.EQUIP_INDEX_XIANGLIAN_1							--项链
	elseif sub_type == GameEnum.EQUIP_TYPE_DIAOZHUI then
		part = GameEnum.EQUIP_INDEX_DIAOZHUI							--吊坠
	end
	return part
end

--通过item_id获取该装备所在部位
function ItemWGData.GetEquipPartByItemId( item_id )
	local cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
	if cfg and big_type and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return ItemWGData.GetEquipTypeName(cfg.sub_type)
	end
	return nil
end

-- 是否可以合成
function ItemWGData.IsCompose(item_id)
	local compose_list = ConfigManager.Instance:GetAutoConfig("compose_auto").compose_list
	local stuff_id, stuff_num
	for k,v in pairs(compose_list) do
		for i = 1, 4 do 	--最多4种材料
			stuff_id = v["stuff_id_" .. i] or 0
			stuff_num = v["stuff_count_" .. i] or 0
			if stuff_id > 0 and stuff_num > 0 and stuff_id == item_id and v.level < 9999 then -- 新增限制：配置等级小于9999才显示合成
				return true
			end
		end
	end

	return false
end

function ItemWGData:GetEquipStoneItemList(stone_type)
	return self.equip_stone_list[stone_type] or {}
end

function ItemWGData:UpdateEquipStoneList()
	local baoshi_bag_list = {}
	baoshi_bag_list[GameEnum.STONE_GONGJI] = {}
	baoshi_bag_list[GameEnum.STONE_FANGYU] = {}

	local stuff_list = self:GetStuffStorgeItemData()
	local baoshi_cfg
	for item, data in pairs(stuff_list) do
		baoshi_cfg = EquipmentWGData.Instance:GetBaoShiCfgByItemId(data.item_id)
		if baoshi_cfg then
			table.insert(baoshi_bag_list[baoshi_cfg.stone_type], data)
		end
	end

	self.equip_stone_list = baoshi_bag_list
	self:CalcEquipStoneNumAndValue()
	EquipmentWGData.Instance:UpdateAllEquipStoneSlotRemind()
end

function ItemWGData:UpdateShiTianStoneList()
	local shitian_stone_bag = {}
	local suit_num = ShiTianSuitWGData.Instance:GetSuitTypeCfgLength()
	for suit_seq = 0, suit_num do
		shitian_stone_bag[suit_seq] = {
			[GameEnum.ATK_STONE] = {},
			[GameEnum.DEF_STONE] = {},
			[GameEnum.HP_STONE] = {},
		}
	end

	if not IsEmptyTable(shitian_stone_bag) then
		local stuff_list = self:GetStuffStorgeItemData()
		local stone_cfg
		for item, data in pairs(stuff_list) do
			stone_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(data.item_id)
			if stone_cfg then
				table.insert(shitian_stone_bag[stone_cfg.stone_blong_suit][stone_cfg.shitianstone_type], data)
			end
		end
	end

	self.shitian_stone_list = shitian_stone_bag
	self:CalcShiTianStoneNumAndValue()
end

function ItemWGData:GetShiTianStoneItemList(suit_seq, stone_type)
	return (self.shitian_stone_list[suit_seq] or {})[stone_type] or {}
end

function ItemWGData:UpdateDaoHangEquipStoneList()
	local baoshi_bag_list = {}
	local stuff_list = self:GetStuffStorgeItemData()
	for item, data in pairs(stuff_list) do
		if MultiFunctionWGData.Instance:IsDaoHangKeLingStore(data.item_id) then
			table.insert(baoshi_bag_list, data)
		end
	end

	self.daohang_keling_store_list = baoshi_bag_list
	MultiFunctionWGData.Instance:UpdateDaoHangKeLingRemind()
end

function ItemWGData:GetDaoHangEquipStoneList()
	return self.daohang_keling_store_list
end

function ItemWGData:GetEquipLingYuItemList(lingyu_type)
	return self.equip_lingyu_list[lingyu_type] or {}
end

function ItemWGData:UpdateEquipLingYuList()
	local lingyu_bag_list = {}
	lingyu_bag_list[GameEnum.LINGYU_GONGJI] = {}
	lingyu_bag_list[GameEnum.LINGYU_FANGYU] = {}
	local stuff_list = self:GetStuffStorgeItemData()
	local lingyu_cfg
	for item, data in pairs(stuff_list) do
		lingyu_cfg = EquipmentLingYuWGData.Instance:GetLingYuCfgByItemId(data.item_id)
		if lingyu_cfg then
			table.insert(lingyu_bag_list[lingyu_cfg.lingyu_type], data)
		end
	end

	self.equip_lingyu_list = lingyu_bag_list
	self:CalcEquipLingYuNumAndValue()
	EquipmentLingYuWGData.Instance:UpdateEquipLingYuSlotRemind()
end

-- 统计玩家背包装备，缓存装备信息 更新所有：update_all,  物品数据：item_data, 物品增删：is_add
--【必定限制】阶数 颜色 星数 【可能限制】性别 部位
-- self.equip_compose_data_list[阶数][颜色][星数] = {[性别], [部位]}
function ItemWGData:UpdateComposeEquipList(update_all, item_data, is_add)
	if not self.equip_compose_data_list or update_all then
		self.equip_compose_data_list = {}

		for k,v in pairs(self.bag_data_list) do
			self:ChangeComposeEquipListData(v, true, true)
		end

		RemindManager.Instance:Fire(RemindName.Compose_Male_Equip)
		RemindManager.Instance:Fire(RemindName.Compose_Female_Equip)
		RemindManager.Instance:Fire(RemindName.Compose_Jewelry_Equip)
	else
		self:ChangeComposeEquipListData(item_data, is_add, false)
	end
end

function ItemWGData:GetComposeEquipListByData(order, color, star)
	if not self.equip_compose_data_list then
		self:UpdateComposeEquipList(true)
	end

	local empty_table = {}
	return (((self.equip_compose_data_list or empty_table)[order] or empty_table)[color] or empty_table)[star]
end

function ItemWGData:ChangeComposeEquipListData(item_data, is_add, update_all)
	if not item_data then
		return
	end

	local item_cfg, big_type = self:GetItemConfig(item_data.item_id)
	if not item_cfg or big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return
	end

	local order = item_cfg.order
	local color = item_cfg.color
	local star = item_data.param and item_data.param.star_level or 0
	local sex = item_cfg.limit_sex
	--local part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)

	if not self.equip_compose_data_list then
		self.equip_compose_data_list = {}
	end

	if not self.equip_compose_data_list[order] then
		self.equip_compose_data_list[order] = {}
	end

	if not self.equip_compose_data_list[order][color] then
		self.equip_compose_data_list[order][color] = {}
	end

	if not self.equip_compose_data_list[order][color][star] then
		self.equip_compose_data_list[order][color][star] = {}
	end

	if is_add then
		local data = {}
		data.index = item_data.index
		data.item_id = item_data.item_id
		data.limit_sex = sex
		data.sub_type = item_cfg.sub_type--part
		table.insert(self.equip_compose_data_list[order][color][star], data)
	else
		local table_idx = -1
		local data = self.equip_compose_data_list[order][color][star]
		for k, v in pairs(data) do
			if v.index == item_data.index then
				table_idx = k
			end
		end

		if table_idx >= 0 then
			table.remove(self.equip_compose_data_list[order][color][star], table_idx)
		end
	end

	if not update_all then
		RemindManager.Instance:Fire(RemindName.Compose_Male_Equip)
		RemindManager.Instance:Fire(RemindName.Compose_Female_Equip)
		RemindManager.Instance:Fire(RemindName.Compose_Jewelry_Equip)
	end
end

function ItemWGData:CalcEquipStoneNumAndValue()
	local stone_bag_value_list = {}
	for stone_type = 1, GameEnum.STONE_FANGYU do
		local equip_body_list = EquipBodyWGData.Instance:GetAllEquipBodyDataList()
		stone_bag_value_list[stone_type] =  {}

		for k, v in pairs(equip_body_list) do
			stone_bag_value_list[stone_type][v.seq] = EquipmentWGData.Instance:GetBaoShiConversionTableInBag(stone_type, v.jade_level_limit)
		end
		-- self.stone_bag_value_list[stone_type] = EquipmentWGData.Instance:GetBaoShiConversionTableInBag(stone_type, jade_level_limit)
	end

	self.stone_bag_value_list = stone_bag_value_list
end

function ItemWGData:GetEquipStoneNumAndValueByType(stone_type, equip_body_seq)
	if not self.stone_bag_value_list then
		self:CalcEquipStoneNumAndValue()
	end

	return (self.stone_bag_value_list[stone_type] or {})[equip_body_seq]
end

function ItemWGData:CalcEquipLingYuNumAndValue()
	self.lingyu_bag_value_list = {}
	for lingyu_type = 1, GameEnum.LINGYU_FANGYU do
		self.lingyu_bag_value_list[lingyu_type] = EquipmentLingYuWGData.Instance:GetLingYuConversionTableInBag(lingyu_type)
	end
end

function ItemWGData:GetEquipLingYuNumAndValueByType(lingyu_type)
	if not self.lingyu_bag_value_list then
		self:CalcEquipLingYuNumAndValue()
	end

	return self.lingyu_bag_value_list[lingyu_type]
end

function ItemWGData:CalcShiTianStoneNumAndValue()
	self.stsuit_stone_bag_value_list = {}
	local suit_cfg = ShiTianSuitWGData.Instance:GetSuitTypeCfg()
	for suit_seq = 0, #suit_cfg do
		self.stsuit_stone_bag_value_list[suit_seq] = {}
		for stone_type = 0, GameEnum.HP_STONE do
			self.stsuit_stone_bag_value_list[suit_seq][stone_type] = ShiTianSuitStrengthenWGData.Instance:GetBaoShiConversionTableInBag(suit_seq, stone_type)
		end
	end
end

function ItemWGData:GetShiTianStoneNumAndValueByType(suit_seq, stone_type)
	if not self.stsuit_stone_bag_value_list then
		self:CalcShiTianStoneNumAndValue()
	end

	return (self.stsuit_stone_bag_value_list[suit_seq] or {})[stone_type] or {}
end

function ItemWGData:IsComposeType(sub_type)
	return 1 == self.sub_type_list[sub_type]
end

function ItemWGData.IsDeCompose(item_id)
	-- local compose_list = ConfigManager.Instance:GetAutoConfig("compose_auto").compose_list
	-- for k, v in pairs(compose_list) do
	-- 	if v.stuff_id_1 == item_id and v.type == 5 then --5是可以分解的类型
	-- 		return true
	-- 	end
	-- end
	return false
end

function ItemWGData.ComposeGetTypeById(item_id)
	local compose_list = ConfigManager.Instance:GetAutoConfig("compose_auto").compose_list
	for k, v in pairs(compose_list) do
		if v.stuff_id_1 == item_id then
			return v.type
		end
	end
end

-- equip_data（废弃）
-- is_html 富文本
function ItemWGData:GetItemName(item_id, equip_data, is_html, size)
	local item_cfg, big_type = self:GetItemConfig(item_id)
	if item_cfg == nil then
		return ""
	end
	
	local name = item_cfg.name
	if is_html then
		local color = self:GetItemColor(item_id, equip_data)
		name = HtmlTool.GetHtml(name, color , size)
	end
	return name
end

-- 获得物品名称
function ItemWGData:GetItemConstName(item_cfg, item_num)
	if not item_cfg then
		return ""
	end
	if not item_num or item_num <= 1 then
		return item_cfg.name
	end
	if self:IsConstItemByUseType(item_cfg.use_type) then
		local name_str = item_cfg.name
		return self:ConvertToSortMoney(item_cfg, item_num, name_str)
	end
	return item_cfg.name
end

-- 获得物品描述
function ItemWGData:GetItemConstDesc(item_cfg, item_num, is_desc)
	if not item_cfg then
		return ""
	end

	local description = self:ConvertDesc(item_cfg, item_num)
	return description
end

function ItemWGData:ConvertToSortMoney(item_cfg, item_num, str)
	local num = item_cfg.param1 * item_num
	local num_str = CommonDataManager.ConverExpByThousand(num, false, true)
	str = string.gsub(str, Language.Common.Wan, "", 1)
	str = string.gsub(str, Language.Common.Yi, "", 1)

	if string.find(str, "color", 1) then
		str = string.gsub(str, "<color=(.+)>(.+)</color>", string.format("<color=%s>%s</color>", "%1", num_str))
	else
		str = string.gsub(str, "%d+%.*%d+", num_str, 1)
	end

	return str
end

function ItemWGData:ConvertDesc(item_cfg, item_num)
	local desc = ""
	if item_cfg then
		local show_cap_item_id = item_cfg.id
		desc = item_cfg.description or ""
		-- 检查是否有合成所需数目标签
		if string.find(desc, "<composed_amount>") then
			local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
			if compose_cfg then
				desc = string.gsub(desc, "<composed_amount>", compose_cfg.stuff_count_1)
				show_cap_item_id = compose_cfg.product_id
			else
				print_error("物品描述的<composed_amount>标签，没有在合成表中找到合成配置，请检查物品或合成配置，id:" .. item_cfg.id)
			end
		end

		if item_num and item_num > 1 and self:IsConstItemByUseType(item_cfg.use_type) then
			desc = self:ConvertToSortMoney(item_cfg, item_num, desc)
		end

		local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_cap_item_id, item_cfg.sys_attr_cap_location)
    	if need_get_sys then
    		if sys_type == ITEMTIPS_SYSTEM.MOUNT then
        		local _ , capability = ItemShowWGData.Instance:GetMountAttrByData(show_cap_item_id)
        		desc = desc.. "\n".. string.format(Language.Tip.ActiveAddCap,capability)
        	elseif sys_type == ITEMTIPS_SYSTEM.LING_CHONG then
        		local _ , capability = ItemShowWGData.Instance:GetLingChongAttrByData(show_cap_item_id)
        		desc = desc.. "\n".. string.format(Language.Tip.ActiveAddCap,capability)
        	elseif sys_type == ITEMTIPS_SYSTEM.HUA_KUN then
        		local _ , capability = ItemShowWGData.Instance:GetHuaKunAttrByData(show_cap_item_id)
        		desc = desc.. "\n".. string.format(Language.Tip.ActiveAddCap,capability)
        	elseif sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA then
        		local _ , capability = ItemShowWGData.Instance:GetTianShenAttrByData(show_cap_item_id)
        		desc = desc.. "\n".. string.format(Language.Tip.ActiveAddCap,capability)
        	elseif sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
        		local _ , capability = ItemShowWGData.Instance:GetXianWaAttrByData(show_cap_item_id)
        		desc = desc.. "\n".. string.format(Language.Tip.ActiveAddCap,capability)
        	end
    	end
	end
	return desc
end

function ItemWGData:GetItemNameDarkColor(item_id, size)
	local item_cfg = self:GetItemConfig(item_id)
	if item_cfg == nil then
		return ""
	end
	
	local name = item_cfg.name
	local color = ITEM_COLOR[item_cfg.color] or ITEM_COLOR[0]
	name = HtmlTool.GetHtml(name, color , size)

	return name
end

function ItemWGData:GetItemColor(item_id, equip_data)
	local item_cfg, big_type = self:GetItemConfig(item_id)
	if item_cfg == nil then
		return COLOR3B.WHITE, -1
	end
	local color_t = nil
	local color = nil
	color = ITEM_COLOR[item_cfg.color]
	return color, item_cfg.color
end

-- 获得提示使用的物品色
function ItemWGData:GetTipsItemColor(item_id, equip_data)
	local item_cfg, big_type = self:GetItemConfig(item_id)
	if item_cfg == nil then
		return MAIN_VIEW_COLOR.WHITE, -1
	end
	local color = GET_TIP_ITEM_COLOR[item_cfg.color] or MAIN_VIEW_COLOR.WHITE
	return color, item_cfg.color
end

-- 获得物品图标资源
function ItemWGData:GetTipsItemIcon(item_id, is_big_icon)
	local item_cfg = self:GetItemConfig(item_id)
	if item_cfg == nil then
		return nil, nil
	end

	local bundle, asset
	local icon_id = item_cfg.icon_id

	if item_cfg.is_man_or_woman == 1 and RoleWGData.Instance.role_vo.sex == 0 then
		icon_id = item_cfg.woman_icon
	end

	if item_cfg.is_other_prof == RoleWGData.Instance:GetRoleProf() then--剑 琴职业区分
		icon_id = item_cfg.other_prof_icon
	end

	if not is_big_icon then
		bundle, asset = ResPath.GetItem(icon_id)
	else
		bundle, asset = ResPath.GetF2RawImagesPNG(icon_id)
	end

	return bundle, asset
end

--获得礼包内容
function ItemWGData:GetItemListInGift(item_id)
	local item_list = {}
	local gift_cfg, big_type = self:GetItemConfig(item_id)

	if self.save_gift_drop_list[item_id] then
		return self.save_gift_drop_list[item_id], gift_cfg
	end

	if gift_cfg and big_type == GameEnum.ITEM_BIGTYPE_GIF then
		local main_prof = RoleWGData.Instance:GetRoleProf()
        local item_cfg
        local drop_list = self:GetConformPlayerDropList()
        local gift_star_level = gift_cfg.item_cell_star_level
		for i=0, #gift_cfg.reward_item do
			local value = gift_cfg.reward_item[i]
			if value then
				item_cfg = self:GetItemConfig(value.item_id)
				if item_cfg then
					if 0 == gift_cfg.is_check_prof or item_cfg.limit_prof == main_prof or item_cfg.limit_prof == 5 then
						if gift_star_level then
							value.star_count = gift_star_level
							value.star_level = gift_star_level
							value.param = {star_level = gift_star_level}
						end
						table.insert(item_list, value)
					end
                else  --item_id有可能是掉落表id，，
                    local drop_id = value.item_id
                    if drop_list and drop_list[drop_id] and drop_list[drop_id][1] then
                        local drop_data = drop_list[drop_id][1]
                        item_cfg = self:GetItemConfig(drop_data.item_id)
                        if item_cfg then
                            local item_data = {}
                            item_data.num = value.num
                            item_data.isbind = value.bind
                            item_data.item_id = drop_data.item_id
                            item_data.star_level = drop_data.param
                            item_data.param = {}
                            item_data.param.star_level = drop_data.param
                            table.insert(item_list, item_data)
                        else
                            print_error("物品没有配置 >>> ", drop_data.item_id)
                        end
                    end
                    if IsEmptyTable(item_cfg) then
                        print_error("物品没有配置 >>> ", value.item_id)
                    end
				end
			end
		end
		self.save_gift_drop_list[item_id] = item_list
	end

	return item_list, gift_cfg
end

--是否是情缘装备
function ItemWGData:GetIsQingyuanEquip(item_id)
	return MarryWGData.Instance:GetEquipCfgById(item_id) ~= nil
end

--是否是法宝
function ItemWGData.GetIsFabao(item_id)
	return false
end

--是否是符文
function ItemWGData.GetIsFuWen(item_id)
	return false
end

-- 获取是否有可快速使用的物品
function ItemWGData:GetIsHasQuickUseItem()
	local remain_offline_rest_time = OfflineRestWGData.Instance:GetRemainOfflineRestTime()
	local hour = math.floor(remain_offline_rest_time / 3600)
	local role_level = RoleWGData.Instance.role_vo.level
	local item_cfg = nil
	for k,v in pairs(self.bag_data_list) do
		item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and 1 == item_cfg.choose_use and item_cfg.limit_level <= role_level
			and self:GetIsCanUseItem(v.item_id, item_cfg.use_daytimes) then
			if v.item_id == SPECIAL_GUAJICARD.IDONE or v.item_id == SPECIAL_GUAJICARD.IDTWO then
				if hour < 20 then
					return true
				end
			else
				return true
			end
		end
	end

	return false
end

--获取有红点显示的礼包
function ItemWGData:GetHasShowGiftItemRed()
	if not IsEmptyTable(self.show_red_gift_item_list) then
		for k,v in pairs(self.show_red_gift_item_list) do
			if v then
				return true
			end
		end
	end
	return false
end

--判断能否使用物品
function ItemWGData:GetIsCanUseItem(item_id, use_daytimes)
	if not item_id then return end
	if use_daytimes then
		if use_daytimes == 0 then return true end
		return (use_daytimes - self:GetItemUseTimes(item_id) > 0) and true or false
	else
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		if item_cfg.use_daytimes - self:GetItemUseTimes(item_id) > 0 then
			return true
		end
		return false
	end
end

----------------------------------------------------
-- 物品CD begin
----------------------------------------------------
function ItemWGData:SetColddownInfoList(colddown_info_list)
	self.colddown_info_list = {}
	for k, v in pairs(colddown_info_list) do
		local end_time = math.max(v.end_time - TimeWGCtrl.Instance:GetServerTime(), 0)
		end_time = end_time + Status.NowTime
		self.colddown_info_list[v.colddown_id] = end_time
	end

	self:NoticeColddownChange(0)
end

function ItemWGData:SetColddownInfo(colddown_id, end_time)
	self.colddown_info_list[colddown_id] = end_time

	self:NoticeColddownChange(colddown_id)
end

function ItemWGData:GetColddownEndTime(colddown_id)
	return self.colddown_info_list[colddown_id] or 0
end

function ItemWGData:NotifyColddownChangeCallBack(callback)
	self.notify_colddown_callback_list[callback] = callback
end

function ItemWGData:UnNotifyColddownChangeCallBack(callback)
	self.notify_colddown_callback_list[callback] = nil
end

function ItemWGData:NoticeColddownChange(colddown_id)
	for k, v in pairs(self.notify_colddown_callback_list) do
		v(colddown_id)
	end
end
----------------------------------------------------
-- 物品CD end
----------------------------------------------------

function ItemWGData:SetUseTimesList(use_times_list)
	self.use_times_list = use_times_list
end

function ItemWGData:GetItemUseTimes(item_id)
	return self.use_times_list[item_id] or 0
end

function ItemWGData:SetBreakColorFlag(flag)
	self.break_color_flag = flag
end

function ItemWGData:SetTreasureColorFlag(flag)
	self.treasure_color_flag = flag
end

function ItemWGData:SetUpCardFlag(flag)
	self.up_card_flag = flag
end

-- 掉落捡取装备回收
function ItemWGData:CheckAutoBreakHandler(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.break_color_flag and PUT_REASON_TYPE.PUT_REASON_PICK == put_reason and new_num > old_num then
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(change_item_id)
		if item_cfg and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_JINGLING
			-- 过滤4件装备自动回收
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_HUFU and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_MIANJIA
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIANGLIAN_1 and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_DIAOZHUI
			and change_item_id < 10000 then
			-- local _, break_color = SettingWGData.Instance:GetPickBreaKData()
			local break_color = SettingWGData.Instance:GetSettingData(SETTING_TYPE.AUTO_RECYCLE_COLOR)
			if item_cfg.order <= break_color then
				if RoleBagWGData.Instance:GetItemCanForge(change_item_id) then
					RoleBagWGCtrl.Instance:SendShenluRonglian({change_item_index})
				end
				-- BagWGCtrl.Instance:SendDiscardItem(change_item_index, new_num - old_num, change_item_id, new_num,  1)
			end
		end
	end
end

-- 掉落捡取宝物回收
function ItemWGData:CheckAutoTreasureHandler(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.treasure_color_flag and PUT_REASON_TYPE.PUT_REASON_PICK == put_reason and new_num > old_num then
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(change_item_id)
		if item_cfg and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_JINGLING
			-- 过滤8件装备自动回收
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_TOUKUI and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_YIFU
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_KUZI and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIANLIAN
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIEZI and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_WUQI
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIANZHUI and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIANFU
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIANJIE and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIANZHUO then

			-- local _, _, _, treasure_color = SettingWGData.Instance:GetPickBreaKData()
			if item_cfg.color <= 1 then
				BagWGCtrl.Instance:SendDiscardItem(change_item_index, new_num - old_num, change_item_id, new_num,  1)
			end
		end
	end
end

--是否是小鬼
function ItemWGData.GetIsXiaogGui(item_id)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		return false
	end
	return item_cfg.sub_type == GameEnum.EQUIP_TYPE_XIAOGUI
end

--是否使用绑元
function ItemWGData.GetIsUseBindGold(item_id)
	local is_use_bind_gold = 0
	local xiaogiu_cfg = EquipmentWGData.GetXiaoGuiCfg(item_id)
	if xiaogiu_cfg then
		is_use_bind_gold = xiaogiu_cfg.is_bind_gold
	end
	return is_use_bind_gold
end

-- 获得背包和身上的所有过期和未过期的小鬼
function ItemWGData:GetBagXiaoGuiList(imp_type)
	local guoqi_list = {}
	local noguoqi_list = {}
	for k,v in pairs(self:GetBagItemDataList()) do
		if ItemWGData.GetIsXiaogGui(v.item_id) then
			local xiaogiu_cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
			local time = math.max(v.invalid_time - TimeWGCtrl.Instance:GetServerTime(), 0)
			if xiaogiu_cfg and xiaogiu_cfg.imp_type == imp_type then
				if time <= 0 then
					v.is_inbag = IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_RENEW_KNAPSACK
					guoqi_list[#guoqi_list + 1] = v
				else
					noguoqi_list[#noguoqi_list + 1] = v
				end
			end
		end
	end

	local imp_guard_info = EquipWGData.Instance:GetmpGuardInfo()
	if imp_guard_info.used_imp_type == imp_type then
		local time = math.max(imp_guard_info.item_wrapper.invalid_time - TimeWGCtrl.Instance:GetServerTime(), 0)
		if time <= 0 then
			imp_guard_info.item_wrapper.is_inbag = IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_RENEW_PUTON
			guoqi_list[#guoqi_list + 1] = imp_guard_info.item_wrapper
		else
			noguoqi_list[#noguoqi_list + 1] = imp_guard_info.item_wrapper
		end
	end
	return guoqi_list, noguoqi_list
end

function ItemWGData:GetGuoQiXiaoGui(imp_type)
	local is_overdue = false
	local xiaogui_data = nil
	local guoqi_list, noguoqi_list = self:GetBagXiaoGuiList(imp_type)
	if #noguoqi_list > 0 then
		is_overdue = false
	else
		if #guoqi_list > 0 then
			is_overdue = true
			xiaogui_data = guoqi_list[1]
		else
			is_overdue = true
			xiaogui_data = nil
		end
	end
	return is_overdue, xiaogui_data
end

--获得集市可以上架商品
function ItemWGData:GetMarketItemDataList()
	local data_list = {}
	local num = 0
	for k,v in pairs(self.bag_data_list) do
		if v.is_bind == 0 and MarketWGData.Instance:CheckIsCanMarket(v) and v.item_id ~= 22030
			and v.item_id ~= 22031 and v.item_id ~= 22032 then
			data_list[num] = v
			num = num + 1
		end
	end
	for k,v in pairs(self.stuff_data_list) do
		if v.is_bind == 0 and MarketWGData.Instance:CheckIsCanMarket(v) and v.item_id ~= 22030
			and v.item_id ~= 22031 and v.item_id ~= 22032 then
			data_list[num] = v
			num = num + 1
		end
	end
	return data_list
end


function ItemWGData:SetBaoXiangIdList(protocol)

	self.baoxiang_id = protocol.item_id
	self.item_id_list = protocol.item_id_list
end

function ItemWGData:GetBaoXiangIdList()
	return self.baoxiang_id,self.item_id_list
end
--判断是否显示宝箱面板
function ItemWGData:GetShowBaoXiangTipCfg(item_id)
	local list = self:GetGiftCfg()
	return list[item_id]
end

-- 获得背包里的紫色装备
function ItemWGData:GetBagPurpleEqList()
	local data_list = {}
	for k,v in pairs(self:GetBagItemDataList()) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and item_cfg.color == 3 then
			data_list[v.index] = v
		end
	end
	return data_list
end

-- 使用背包物品
function ItemWGData:UseItem(item_id)
	local item_data = self:GetItem(item_id)
	BagWGCtrl.Instance:SendUseItem(item_data.index, item_data.num)
end

function ItemWGData:GetItemIdByIndex(index)
	if index == nil then
		return -1
	end

	local data
	if index < COMMON_CONSTS.MAX_BAG_COUNT then
		data = self.bag_data_list[index]
	-- elseif index >= COMMON_CONSTS.MAX_BAG_COUNT and index < (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
	-- 	data = self.storage_data_list[index - COMMON_CONSTS.MAX_BAG_COUNT]
	elseif index >= (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
		data = self.stuff_data_list[index - (COMMON_CONSTS.MAX_BAG_COUNT * 2)]
	end

	return data and data.item_id or -1
end

function ItemWGData:GetDebugNotifyChangeCount(t)
	t.itemlist_change_count = 0
	t.item_change_count = 0
	for k,v in pairs(self.notify_datalist_change_callback_list) do
		t.itemlist_change_count = t.itemlist_change_count + 1
	end

	for k,v in pairs(self.notify_data_change_callback_list) do
		t.item_change_count = t.item_change_count + 1
	end
end

function ItemWGData:IsExistsListen(callback)
	return nil ~= self.notify_data_change_callback_map[callback]
end

-- 需要特殊处理显示道具数量的
function ItemWGData:IsConstItemByUseType(use_type)
	if use_type == Item_Use_Type.Gold or use_type == Item_Use_Type.BIND_XIANYU
		or use_type == Item_Use_Type.ShengWang  or use_type == Item_Use_Type.XianYu2 
		or use_type == Item_Use_Type.LUCKY_VALUE then --or use_type == Item_Use_Type.XianYu then
		return true
	end
	
	return false
end

function ItemWGData.GetItemCfgByStr(string)
	local tab = string.split(string,",")
	local item_data
	local item_list = {}
	local item
	for i,v in ipairs(tab) do
		item_data = string.split(v,":")
		item = {}
		item.item_id = tonumber(item_data[1])
		item.num = tonumber(item_data[2])
		item.is_bind = tonumber(item_data[3])
		table.insert(item_list,item)
	end
	return item_list
end

--获取背包中所有未过期，并且超过人物等级限制的小鬼
function ItemWGData:GetXiaoGuiList(impguard_type)
	local role_level = RoleWGData.Instance.role_vo.level
	local xiaogui_list = {}
	for k,v in pairs(self:GetBagItemDataList()) do
		if v.item_id > 0 and ItemWGData.GetIsXiaogGui(v.item_id) then
			local cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
			local is_overdue = true
			-- 期限小鬼判断是否过期
			if v.item_id == 10101 then
				is_overdue = v.invalid_time - TimeWGCtrl.Instance:GetServerTime() > 0
			end
			if cfg and cfg.level_limit <= role_level and impguard_type == cfg.impguard_type and is_overdue then
				v.color = cfg.color
				table.insert(xiaogui_list, v)
			end
		end
	end
	return xiaogui_list
 end

 --根据小鬼类型拿到背包跟身上所有的过期和未过期的小鬼
 function ItemWGData:GetAllXiaoGuiList(impguard_type)
	local guoqi_list = {}
	local noguoqi_list = {}
	for k,v in pairs(self:GetBagItemDataList()) do
		if ItemWGData.GetIsXiaogGui(v.item_id) then
			local xiaogiu_cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
			local time = math.max(v.invalid_time - TimeWGCtrl.Instance:GetServerTime(), 0)
			if xiaogiu_cfg and xiaogiu_cfg.impguard_type == impguard_type then
				if time <= 0 then
					v.is_inbag = IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_RENEW_KNAPSACK
					guoqi_list[#guoqi_list + 1] = v
				else
					noguoqi_list[#noguoqi_list + 1] = v
				end
			end
		end
	end

	local imp_guard_info = EquipWGData.Instance:GetmpGuardInfo()
	if imp_guard_info.item_wrapper[impguard_type] then
		if imp_guard_info.item_wrapper[impguard_type].item_id ~= 0 then
			local xiaogui_info = imp_guard_info.item_wrapper[impguard_type]
			local xiaogiu_cfg = EquipmentWGData.GetXiaoGuiCfg(xiaogui_info.item_id)
			if xiaogiu_cfg.impguard_type == impguard_type then
			local time = math.max(xiaogui_info.invalid_time - TimeWGCtrl.Instance:GetServerTime(), 0)
				if time <= 0 then
					xiaogui_info.is_inbag = IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_RENEW_PUTON
					guoqi_list[#guoqi_list + 1] = xiaogui_info
				else
					noguoqi_list[#noguoqi_list + 1] = xiaogui_info
				end
			end
		end
	end
	return guoqi_list, noguoqi_list
 end

 function ItemWGData:GetItemDesc(item_cfg)
	-- body
	local desc = ""
	if not item_cfg then return desc end

	desc = item_cfg.description .. "\n"
	local biaoti = nil
	local description = nil
	for i=2,10 do
		biaoti = item_cfg["biaoti" .. i]
		description = item_cfg["description" .. i]
		if biaoti and description and "" ~= biaoti and "" ~= description then
			desc = desc .. biaoti .. "\n"
			desc = desc .. description .. "\n"
		end
	end

	if item_cfg["xiushici"] then
		desc = string.format("<color=#C26AF5>%s</color>%s", desc, item_cfg["xiushici"])
	end

	return desc
end

function ItemWGData:IsSpecialRandGift(item_id)
	local item_cfg, item_type = self:GetItemConfig(item_id)
	if item_cfg and item_type == GameEnum.ITEM_BIGTYPE_GIF and item_cfg.special_show == 1 then
		return true
	end

	return false
end

function ItemWGData:GetConformPlayerDropList()
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local role_prof = RoleWGData.Instance:GetRoleProf()

	if not self.conform_drop_list or (self.old_dorp_sex ~= role_sex)
	or (self.old_dorp_prof ~= role_prof) then
		self.conform_drop_list = {}
		local no_limit_sex = -1
		local no_limit_prof = 0
		local drop_auto = ConfigManager.Instance:GetAutoConfig("drop_list_auto")
		local drop_cfg = drop_auto and drop_auto.drop_list
		if IsEmptyTable(drop_cfg) then
			return self.conform_drop_list
		end

		-- 筛选
		for k, v in ipairs(drop_cfg) do
			local key = v.drop_id
			if not self.conform_drop_list[key] then
				self.conform_drop_list[key] = {}
			end

			for i_idx, i_data in ipairs(v.item_list) do
				if (i_data.sex == role_sex or i_data.sex == no_limit_sex) and
				 (i_data.prof == role_prof or i_data.prof == no_limit_prof) then
					table.insert(self.conform_drop_list[key], i_data)
				end
			end
		end

		self.old_dorp_sex = role_sex
		self.old_dorp_prof = role_prof
	end

	return self.conform_drop_list
end

--获取机器人盟主装备
function ItemWGData:GetRobotEquipsByDropId(drop_id)
    local drop_auto = ConfigManager.Instance:GetAutoConfig("drop_list_auto")
    local drop_cfg = drop_auto and drop_auto.drop_list
    if IsEmptyTable(drop_cfg) then
        return {}
    end
    for k, v in ipairs(drop_cfg) do
        if drop_id == v.drop_id then
            return v
        end
    end
    return {}
end

-- 部分道具(随机礼包里的) 根据限制转换数据
function ItemWGData:GetGiftItemConvertibleData(data)
	if not data then
		return data
	end
	local item_cfg, big_type = self:GetItemConfig(data.item_id)
	local is_special_gift = self:IsSpecialRandGift(data.item_id)
	if item_cfg == nil or not is_special_gift then
		return data
	end

	local drop_list = self:GetConformPlayerDropList()
	local drop_id = item_cfg.drop_device_id
	local multiple = data.num or 1
	if drop_list and drop_list[drop_id] and drop_list[drop_id][1] then
		local drop_data = drop_list[drop_id][1]
		local show_cfg = self:GetItemConfig(drop_data.item_id)
		if show_cfg then
			local tmp_data = {}--__TableCopy(show_cfg)
			tmp_data.item_id = show_cfg.item_id or show_cfg.id
			tmp_data.num = drop_data.num * multiple
			tmp_data.is_bind = drop_data.bind
			tmp_data.param = {star_level = drop_data.param}
			return tmp_data
		end
	end

	return data
end

function ItemWGData:GetGiftDropList(item_id, times) --支持多重掉落套娃 --times防止死递归
	if times > 5 then
		print_log("drop_cfg recures more than 5 times")
		return {}
	end

	local drop_list = self:GetConformPlayerDropList()
	local gift_cfg, big_type = self:GetItemConfig(item_id)
	local is_drop_id = not IsEmptyTable(drop_list[item_id])
	if (gift_cfg and big_type == GameEnum.ITEM_BIGTYPE_GIF) or is_drop_id then
		local drop_item_list = {}
		local drop_cfg = {}
		if is_drop_id then
			drop_cfg = drop_list[item_id]
		else
			drop_cfg = drop_list[gift_cfg.drop_device_id]
		end

		if not drop_cfg then
			return drop_item_list
		end

		local prof = GameVoManager.Instance:GetMainRoleVo().prof % 10
		local num = 0
		for k,v in pairs(drop_cfg) do
			if not IsEmptyTable(drop_list[v.param]) then
				local drop_item_list2 = self:GetGiftDropList(v.param, times + 1)
				if not IsEmptyTable(drop_item_list2) then
					for k,v in pairs(drop_item_list2) do
						num = num + 1
						drop_item_list[num] = v
					end
				end
			elseif v.prof == prof or v.prof == 0 or v.prof == 5 then
				num = num + 1
				drop_item_list[num] = {}
				drop_item_list[num].item_id = v.item_id
				drop_item_list[num].num = v.num
				drop_item_list[num].bind = v.bind
				drop_item_list[num].sex = v.sex
				drop_item_list[num].prof = v.prof
				drop_item_list[num].param = {star_level = v.param}
			end
		end

		return drop_item_list
	else
		return {}
	end
end

function ItemWGData:AutoGetGiftDropDesc(item_id)
	local drop_item_list = self:GetGiftDropList(item_id,1)

	if IsEmptyTable(drop_item_list) then
		return ""
	end

	local max_color = GameEnum.ITEM_COLOR_XUAN_QING
	for k,v in pairs(drop_item_list) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(tonumber(v.item_id))
		if item_cfg then
			v.name = item_cfg.name
			v.color = item_cfg.color
			v.sort_index = (max_color - v.color) * 1000000 + v.item_id
		else
			v.name = ""
			v.color = 1
			v.sort_index = 1
			print_log("cfg is nil item_id:",v.item_id)
		end
	end

	table.sort(drop_item_list, SortTools.KeyLowerSorter("sort_index"))
	local desc_str = ""
	for k,v in pairs(drop_item_list) do
		if v.name ~= "" then
			if desc_str ~= "" then
				desc_str = desc_str.."\n"
			end
			desc_str = desc_str..ToColorStr(v.name, ITEM_COLOR[v.color]).."x"..v.num
		end
	end
	return desc_str
end

--缓存拾取的最好的装备
function ItemWGData:SaveBestDropEquip(protocol)
	local delay_time = 3
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(protocol.item_id)
	local can_show, show_type = self:CheckItemCanShowBestDrop(item_cfg, big_type)
	if can_show and protocol.item_index ~= -1 then
		local new_queip_data = self:GetGridData(protocol.item_index)
		--装备类型才判断,其他珍稀物品直接展示
		if show_type == DROP_TIPS_SHOW_TYPE.Eqiup and not TipWGData.Instance:CheckCanShowData(new_queip_data) then
			return
		end
		-- print_error("FFFFF======== 缓存拾取的最好的装备", item_cfg.name, protocol.item_id, can_show, show_type)
		if nil == self.last_drop_timestamp then
			self.last_drop_timestamp = protocol.drop_timestamp
			self.save_equip_index = nil
			self.already_showing = false
			if self.save_equip_delay then
				GlobalTimerQuest:CancelQuest(self.save_equip_delay)
				self.save_equip_delay = nil
			end
			self.save_equip_delay = GlobalTimerQuest:AddDelayTimer(function ()
				self.already_showing = true
				self:ShowBestDropEquip(show_type)
			end,delay_time)
		end

		if math.abs(self.last_drop_timestamp - protocol.drop_timestamp) > 3 then --第二次掉落
			self.save_equip_index = nil
			self.already_showing = false
			self.last_drop_timestamp = protocol.drop_timestamp
			if self.save_equip_delay then
				GlobalTimerQuest:CancelQuest(self.save_equip_delay)
				self.save_equip_delay = nil
			end
			self.save_equip_delay = GlobalTimerQuest:AddDelayTimer(function ()
				self.already_showing = true
				self:ShowBestDropEquip(show_type)
			end,delay_time)
		end
		if not self.save_equip_index then
			self.save_equip_index = protocol.item_index
			self.save_equip_color = item_cfg.color
			self.save_equip_id = protocol.item_id
		else
			local old_queip_data = self:GetGridData(self.save_equip_index)
			local need_change = false
			if new_queip_data and old_queip_data then
				if old_queip_data.item_id ~= self.save_equip_id then --背包数据改变
					need_change = true
				elseif item_cfg.color > self.save_equip_color then
					need_change = true
                elseif item_cfg.color == self.save_equip_color and new_queip_data.param then
                    local old_lv = old_queip_data.param and old_queip_data.param.level or 0
					need_change = new_queip_data.param.star_level >= old_lv
				end
			end
			if need_change then
				self.save_equip_index = protocol.item_index
				self.save_equip_color = item_cfg.color
				self.save_equip_id = protocol.item_id
				if self.already_showing then
					self:ShowBestDropEquip(show_type)
				end
			end
		end
	end
end

function ItemWGData:CheckItemCanShowBestDrop(item_cfg, big_type)
	local can_show = false
	local show_type = DROP_TIPS_SHOW_TYPE.Eqiup
	if item_cfg then
		local item_id = item_cfg.id
		--装备
		if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			can_show = true
			show_type = DROP_TIPS_SHOW_TYPE.Eqiup
		end
		-----------------时装类型-----------------
		if not can_show then
			local part_type = NewAppearanceWGData.Instance:GetFashionTypeByItemId(item_id)
			can_show = part_type > 0
			if part_type == SHIZHUANG_TYPE.WING then--翅膀
				show_type = DROP_TIPS_SHOW_TYPE.Wing
			elseif part_type == SHIZHUANG_TYPE.FABAO then--法宝
				show_type = DROP_TIPS_SHOW_TYPE.Fabao
			end
		end

		--化鲲
		if not can_show and NewAppearanceWGData.Instance:GetKunActCfgByItemId(item_id) then
			can_show = true
			show_type = DROP_TIPS_SHOW_TYPE.Kun
		end
	end

	return can_show, show_type
end

function ItemWGData:ShowBestDropEquip(show_type)
	--[[
	local data = {}
	data.save_equip_index = self.save_equip_index
	data.save_equip_color = self.save_equip_color
	data.save_equip_id = self.save_equip_id
	data.last_drop_timestamp = self.last_drop_timestamp
	data.show_type = show_type or DROP_TIPS_SHOW_TYPE.Eqiup
	TipWGCtrl.Instance:OpenDropEquipShow(data)
	--]]
end

function ItemWGData:GetItemGiftShowRed(item_id)
	return self.show_red_gift_item_list[item_id] or false
end

function ItemWGData:SetItemGiftShowRed(item_id,flag)
	if flag then
		local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local key = "gift_"..item_id.."day"..cur_day
		local value = PlayerPrefsUtil.GetInt(key)
		if value == 1 then
			self.show_red_gift_item_list[item_id] = false
		else
			PlayerPrefsUtil.SetInt(key,1)
			self.show_red_gift_item_list[item_id] = true
		end
	else
		self.show_red_gift_item_list[item_id] = false
	end
end

function ItemWGData:CheckItemGiftShowRedEvent()
	if self.now_check_show_red then
		RemindManager.Instance:Fire(RemindName.BagBag)
	end
end

function ItemWGData:GetItemIconByItemId(item_id)
    local cur_item_id = item_id
    -- 部分道具(随机礼包里的) 根据限制改变显示数据
    local is_special_gift = ItemWGData.Instance:IsSpecialRandGift(cur_item_id)
    if is_special_gift then
        local old_itemid = cur_item_id
        local change_data = ItemWGData.Instance:GetGiftItemConvertibleData({item_id = cur_item_id})
        if change_data and old_itemid ~= change_data.item_id then
            cur_item_id = change_data.item_id
        end
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(cur_item_id)
    return item_cfg and item_cfg.icon_id or 0
end

--判断是否显示雷电特效
--传入物品 zhengui_effect 字段
function ItemWGData:CheckIsShowZhenGuiEffect(zhengui_num)
	local is_show = false
	if zhengui_num then
		is_show = zhengui_num >= 6 and zhengui_num ~= 9
	end
	return is_show
end


--判断是否是材料，是则返回合成的目标物品id
function ItemWGData:GetComposeProductid(item_id)
	item_id = tonumber(item_id)
	if item_id == 46048 then
		return item_id
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    local temp_item_id = item_id
    if item_cfg and item_cfg.is_chip and 1 == item_cfg.is_chip then
        local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(temp_item_id)
        if compose_cfg then
            temp_item_id = compose_cfg.product_id
        end
    end

    return temp_item_id
end

-- 判断是否是仙玉宝箱
function ItemWGData:IsNeedGoldGift(item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg and item_cfg.need_gold and item_cfg.need_gold > 0 then
		return true
	end
	return false
end

-- 根据掉落id获得掉落配置
function ItemWGData:GetDropCfgByDropId(drop_id)
	return self.drop_cfg[drop_id]
end

function ItemWGData:ParseDropCfgByDropId(drop_id)

end

-- 获取背包内所有自选礼包能开出这个物品的 礼包列表 物品数量
function ItemWGData:GetOptionalGiftItem(item_id)
	if not item_id then
		return {}, 0
	end
	local bag_data_list = self:GetBagItemDataList()
	local item_cfg, item_type = nil, nil
	local gift_type = GameEnum.ITEM_BIGTYPE_GIF
	local drop_list = {}
	local gift_list = {}
	local open_num = 0
	for _,data in pairs(bag_data_list) do
		item_cfg,item_type = self:GetItemConfig(data.item_id)
		if item_type == gift_type and item_cfg.max_open_num == 1 then
			drop_list = self:GetItemListInGift(data.item_id)
			for i=1,#drop_list do
				if drop_list[i].item_id == item_id then
					gift_list[#gift_list + 1] = data
					open_num = open_num + (drop_list[i].num or 1) * (data.num or 1)
					break
				end
			end
		end
	end
	return gift_list, open_num
end

function ItemWGData:CheckScoietyGiftItem(item_id, num)
	if self.societgift_flower_item[item_id] or self.societgift_xianlv_gift_item[item_id] then
		if self.societgift_flower_item[item_id] then
			self.societgift_flower_item[item_id] = num
		elseif self.societgift_xianlv_gift_item[item_id] then
			self.societgift_xianlv_gift_item[item_id] = num
		end

		if not FunOpen.Instance:GetFunIsOpened(FunName.Marry) then
			return
		end

		if MarryWGData.Instance:GetIsSocietyGiftOpened() and not SocietyWGData.Instance:GetIsLoverOnline() then
			return
		end

		local repetition_num = 0
		local gift_item_id = item_id
		if num > 0 then
			repetition_num = 1
			if not self.societgift_xianlv_gift_item[item_id] then
				for k,v in pairs(self.societgift_xianlv_gift_item) do
					if v > 0 then
						gift_item_id = k
						break
					end
				end
			end
		else
			for k,v in pairs(self.societgift_xianlv_gift_item) do
				if v > 0 then
					gift_item_id = k
					repetition_num = 1
					break
				end
			end
			if repetition_num == 0 then
				for k,v in pairs(self.societgift_flower_item) do
					if v > 0 then
						gift_item_id = k
						repetition_num = 1
						break
					end
				end
			end
		end

		-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLV_ZENGSONG, repetition_num, function ()
		-- 	if repetition_num == 0 then
		-- 		return false
		-- 	end

		-- 	if self.societgift_xianlv_gift_item[gift_item_id] then
		-- 		--ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
		-- 	else
		-- 		local has_gift = false
		-- 		for k,v in pairs(self.societgift_xianlv_gift_item) do
		-- 			if v > 0 then
		-- 				has_gift = true
		-- 				break
		-- 			end
		-- 		end
		-- 		if has_gift then
		-- 			--ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
		-- 		else
		-- 			FlowerWGCtrl.Instance:Open({item_id = gift_item_id})
		-- 		end
		-- 	end
		-- 	MarryWGData.Instance:SetSocietyGiftOpened(true)

		-- 	return SocietyWGData.Instance:GetIsLoverOnline() or not MarryWGData.Instance:GetIsSocietyGiftOpened()
		-- end)
	end
end

function ItemWGData:CheckHasScoietyGiftItem()
	local has_gift, has_flower = false, false
	local gift_item_id, flower_item_id = 0, 0
	if not IsEmptyTable(self.societgift_xianlv_gift_item) then
		for k,v in pairs(self.societgift_xianlv_gift_item) do
			if v > 0 then
				has_gift = true
				gift_item_id = k
				break
			end
		end
	end

	if not IsEmptyTable(self.societgift_flower_item) then
		for k,v in pairs(self.societgift_flower_item) do
			if v > 0 then
				has_flower = true
				flower_item_id = k
				break
			end
		end
	end

	return has_gift, has_flower, gift_item_id, flower_item_id
end

-- 通过掉落id 获取全部道具
function ItemWGData:GetDropAllShowItemList(drop_id)
	local drop_list = self:GetConformPlayerDropList()
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local role_prof = RoleWGData.Instance:GetRoleProf()

	local acc_item_list = {}
	local item_id, is_bind = 0, 0
	local function get_all_item(drop_id)
		local list = drop_list[drop_id]
		if not list then
			return
		end

		for k,v in pairs(list) do
			item_id = v.item_id
			is_bind = v.bind
			if item_id > 0 then
				if acc_item_list[item_id] == nil then
					acc_item_list[item_id] = {}
				end

				if acc_item_list[item_id][is_bind] == nil then
					local item_cfg = self:GetItemConfig(item_id)
					local color = item_cfg and item_cfg.color or 0
					acc_item_list[item_id][is_bind] = {}
					acc_item_list[item_id][is_bind].num = 0
					acc_item_list[item_id][is_bind].sort_index = color * 1000000 + item_id
				end

				local data = acc_item_list[item_id][is_bind]

				data.item_id = item_id
				data.num = data.num + v.num
				data.is_bind = is_bind
				data.param = {star_level = v.param}

			elseif v.param > 0 then
				get_all_item(v.param)
			end
		end
	end

	get_all_item(drop_id)

	local item_list = {}
	for k, is_bind_list in pairs(acc_item_list) do
		for is_bind, data in pairs(is_bind_list) do
			table.insert(item_list, data)
		end
	end

	SortTools.SortDesc(item_list, "sort_index")
	return item_list
end

function ItemWGData:SetLimitNumByUseTimeList(use_dan_list)
	if use_dan_list then
		self.use_dan_list = use_dan_list
	else
		return self.use_dan_list
	end
end

function ItemWGData:GetLimitNumByUseTime(use_num)
	for k,v in pairs(self.dan_limit_cfg) do
		if v.use_min_num <= use_num and v.use_max_num >= use_num then
			return v
		end
	end

	return {}
end

-- 根据ItemId 获取物品价值
function ItemWGData:GetItemValueByItemId(item_id)
	if self.item_value_cfg == nil or self.item_default_value == nil then
		local item_value_cfg = ConfigManager.Instance:GetAutoConfig("itemvalue_auto")
		local item_value_data = item_value_cfg.item_value
		local item_value_cache = {}

		for k, v in pairs(item_value_cfg.item_value) do
			if UNITY_EDITOR then
				if nil ~= item_value_cache[v.id] then
					print_error("重复定义物品价值，item_id = ", v.id)
				end
			end

			item_value_cache[v.id] = v.value
		end

		self.item_value_cfg = item_value_cache
		self.item_default_value = item_value_cfg.other[1].default_value
	end

	return (self.item_value_cfg or {})[item_id] or self.item_default_value
end

-- 获取装备的实际可穿戴等级 item_cfg:可传item_cfg和item_id
function ItemWGData:GetEquipLimitLevel(item_cfg)
	if type(item_cfg) == "number" then
		item_cfg = self:GetItemConfig(item_cfg)
	end
	if IsEmptyTable(item_cfg) then
        return 0
    end
	-- 普通角色装备 背包里的10个
	if self:IsNormalRoleEquip(item_cfg.sub_type) then
		return item_cfg.limit_level - CultivationWGData.Instance:GetEquipLevelLimit()
	end
	-- 其他
	return item_cfg.limit_level
end

-- 是否为普通角色装备 10个 sub_type:sub_type
function ItemWGData:IsNormalRoleEquip(sub_type)
	if nil ~= sub_type and sub_type >= GameEnum.EQUIP_TYPE_TOUKUI and sub_type <= GameEnum.EQUIP_TYPE_XIANZHUO then
		return true
	end
	return false
end

function ItemWGData:GetSpecialBeamAsset(item_id)
	local cfg = self.special_beam_cfg[item_id] or {}
	return cfg.eff_bundle, cfg.eff_asset
end

--显示基本的描述description
function ItemWGData:ShowItemDescription(item_cfg)
	local desc_str = ""

	if item_cfg == nil then
		return
	end

	local description = ItemWGData.Instance:GetItemConstDesc(item_cfg, 1)
	if 84 == item_cfg.use_type then               -- 使用类型为84的具体描述读配置
		description = string.format(description, CommonDataManager.ConverExp(self:GetLeveReward(item_cfg.param1)))
	end

	description = CommonDataManager.ParseGameName(description)
	if description and description ~= "" then
		-- 碎片的物品描述内加上已拥有数量
	    if item_cfg.is_chip == 1 and not FairyLandEquipmentWGData.Instance:GetIsGodBookChip(item_cfg.id) then
	    	local has_num = ToColorStr(ItemWGData.Instance:GetItemNumInBagById(item_cfg.id), COLOR3B.D_GREEN)
	    	local has_num_str = string.format(Language.F2Tip.HasItemNumStr, has_num)
	    	desc_str = string.format("%s\n%s", description, has_num_str)
	    else
	    	desc_str = description
	    end
	end

	return desc_str
end

function ItemWGData:GetLeveReward(param)
	local role_reward_cfg = ConfigManager.Instance:GetAutoConfig("role_level_reward_auto").level_reward_list
	local role_level = RoleWGData.Instance.role_vo.level
	if role_reward_cfg and role_reward_cfg[role_level] then
		return role_reward_cfg[role_level]["exp_dan_exp_" .. param + 1]
	end
end
