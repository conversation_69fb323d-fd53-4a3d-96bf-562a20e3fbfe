﻿using System;
using Nirvana;
using UnityEngine;

// 实现了单个弹道的播放逻辑，包括速度、加速度、移动曲线等，并在更新时处理弹道的移动和命中逻辑。
public sealed class ProjectileSingle : Projectile
{
    [SerializeField]
    private float speed = 5.0f;

    [SerializeField]
    private float acceleration = 10.0f;

    [SerializeField]
    private AnimationCurve moveXCurve = AnimationCurve.Linear(0.0f, 1.0f, 1.0f, 1.0f);

    [SerializeField]
    private float moveXMultiplier = 0.0f;

    [SerializeField]
    private AnimationCurve moveYCurve = AnimationCurve.Linear(0.0f, 1.0f, 1.0f, 1.0f);

    [SerializeField]
    private float moveYMultiplier = 0.0f;

    [SerializeField]
    private EffectControl hitEffect;

    [SerializeField]
    private bool hitEffectWithRotation = true;

    private Vector3 sourceScale;
    private Transform target;
    private int layer;
    private Action hited;
    private Action complete;

    private Vector3 startPosition;
    private Vector3 normalPosition;
    private float currentSpeed;                     // 弹道的当前速度

    private EffectControl effect;
    private Vector3 targetPosition;

    public override void Play(
        Vector3 sourceScale, 
        Transform target, 
        int layer, 
        Action hited, 
        Action complete)
    {
        this.sourceScale = sourceScale;
        this.target = target;
        this.layer = layer;
        this.hited = hited;
        this.complete = complete;

        this.startPosition = this.transform.position;
        this.normalPosition = this.transform.position;
        this.currentSpeed = this.speed;

        if (this.effect != null)
        {
            this.effect.Reset();
            this.effect.Play();
        }
    }

    private void Awake()
    {
        this.effect = this.GetComponent<EffectControl>();
    }

    public void Update()
    {
        if (this.hited == null && this.complete == null)
        {
            return;
        }

        // 更新目标位置
        if (this.target != null)
        {
            this.targetPosition = this.target.position;
        }

        // 更新弹道的速度
        this.currentSpeed += this.acceleration * Time.deltaTime;

        // 计算弹道的移动
        var offset = this.targetPosition - this.normalPosition;
        var total = targetPosition - this.startPosition;
        var radio = 1.0f - offset.magnitude / total.magnitude;

        var direction = offset.normalized;
        var velocity = direction * this.currentSpeed;

        var movement = velocity * Time.deltaTime;
        if (movement.sqrMagnitude >= offset.sqrMagnitude)
        {
            // 弹道到达目标位置，触发命中特效和回调
            this.transform.position = this.targetPosition;
            if (this.hitEffect != null)
            {
                if (EventDispatcher.Instance != null)
                {
                    EventDispatcher.Instance.OnProjectileSingleEffect(hitEffect, this.transform.position, this.transform.rotation, this.hitEffectWithRotation, this.sourceScale, this.layer);
                }
                else if (GameRoot.Instance == null)
                {
                    var effect = GameObjectPool.Instance.Spawn(hitEffect, null);
                    if (this.hitEffectWithRotation)
                    {
                        effect.transform.SetPositionAndRotation(
                            this.transform.position, 
                            this.transform.rotation);
                    }
                    else
                    {
                        effect.transform.position = this.transform.position;
                    }

                    effect.transform.localScale = this.sourceScale;
                    effect.gameObject.SetLayerRecursively(this.layer);
                
                    effect.FinishEvent += () => GameObjectPool.Instance.Free(effect.gameObject);
                    effect.Reset();
                    effect.Play();
                }
                else
                {
                    this.effect = null;
                }
            }

            // 调用 hited 和 complete 回调
            if (this.hited != null)
            {
                var hited = this.hited;
                this.hited = null;
                hited();
            }

            if (this.complete != null)
            {
                var complete = this.complete;
                this.complete = null;
                complete();
            }
        }
        else
        {
            // 更新弹道的位置和朝向
            this.normalPosition += movement;
            var movementPosition = this.normalPosition;
            var movementUp = Vector3.up;
            var movementRight = Vector3.Cross(direction, movementUp);

            if (!Mathf.Approximately(this.moveXMultiplier, 0.0f))
            {
                var moveX =
                    this.moveXMultiplier * this.moveXCurve.Evaluate(radio);
                movementPosition += movementRight * moveX;
            }

            if (!Mathf.Approximately(this.moveYMultiplier, 0.0f))
            {
                var moveY =
                    this.moveYMultiplier * this.moveYCurve.Evaluate(radio);
                movementPosition += movementUp * moveY;
            }

            this.transform.position = movementPosition;
            this.transform.LookAt(targetPosition);
        }
    }
}
