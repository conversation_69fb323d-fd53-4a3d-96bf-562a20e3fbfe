--限时外观过期
AppearanceRemoveView = AppearanceRemoveView or BaseClass(SafeBaseView)

local STATIC_TIME = 10					--静止时间

local IS_PREFAB = { --是否为预制体
	[ROLE_APPE_TYPE.PHOTO] = true,
	[ROLE_APPE_TYPE.BUBBLE] = true,
	[ROLE_APPE_TYPE.TITLE] = true,
}

function AppearanceRemoveView:__init()
	self.view_layer = UiLayer.Pop
	self.view_style = ViewStyle.Half
	self.can_do_fade = false
	self:SetMaskBg(true, true, nil, BindTool.Bind1(self.DoCloseTween, self))
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:AddViewResource(0, "uis/view/appearance_ui_prefab", "layout_remove_view")

	self.info = nil
end

function AppearanceRemoveView:ReleaseCallBack()
	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
    end

    if self.static_count_down then
		CountDown.Instance:RemoveCountDown(self.static_count_down)
		self.static_count_down = nil
	end
end

function AppearanceRemoveView:LoadCallBack()
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model_display)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
end

function AppearanceRemoveView:OpenCallBack()

end

function AppearanceRemoveView:CloseCallBack()
	TipWGCtrl.Instance:PlayAniTab(GuideModuleName.AppearanceRemoveView, "sub")
end

--模型路径，模型id，模型名字
--@attr.name 模型的名字
--@attr.model_type 模型的类型：坐骑，羽翼，法宝，灵宠等
function AppearanceRemoveView:SetContent(info)
	if nil == info or IsEmptyTable(info) then
		return
	end

	self.info = info
	local param = AppearanceWGData.Instance:GetNewAppearanceCfg(info.appe_type, info.appe_image_id, info.index_param)
	--print_error("----param----", param)
	self.param = param
	if self:IsOpen() then
		self:Flush()
	end
end

function AppearanceRemoveView:OnFlush()
	self:StartCountDown()

	if self.param == nil or self.model_display == nil or self.param.item_id == 0 then
		return
	end

	local body_show_type = self.param.type

	self:FlushModel()
end


function AppearanceRemoveView:DoCloseTween()
	self.info = nil
	if self:OpenNextAppeInfo() then
		return
	end

	self:Close()
end

--看看是否还有下一个形象可以展示
function AppearanceRemoveView:OpenNextAppeInfo()
	local list = AppearanceWGCtrl.Instance:GetRemoveAppeInfoList()
	if IsEmptyTable(list) then
		return false
	end

	local can_open = AppearanceWGCtrl.Instance:CanOpenRemoveView()
	AppearanceWGCtrl.Instance:OpenRemoveAppeView()
	if can_open then
		return true
	end

	return false
end

function AppearanceRemoveView:GetInfo()
	return self.info
end

function AppearanceRemoveView:StopCountDown()
	if self.static_count_down then
		CountDown.Instance:RemoveCountDown(self.static_count_down)
		self.static_count_down = nil
	end
end

--开始倒计时结束
function AppearanceRemoveView:StartCountDown()
	self:StopCountDown()
	local function time_func(elapse_time, total_time)
		self.node_list["close_time"].text.text = string.format(Language.Common.AutoCloseTimerTxt, math.ceil(total_time - elapse_time))
		if elapse_time >= total_time then
			self:DoCloseTween()
			self:StopCountDown()
			return
		end
	end

	self.static_count_down = CountDown.Instance:AddCountDown(STATIC_TIME, 1, time_func)
end

function AppearanceRemoveView:FlushModel()
    local display_data = {}

    if self.param.item_id ~= 0 then
    	display_data.item_id = self.param.item_id
    	display_data.should_ani = true

		display_data.render_type = IS_PREFAB[self.param.type] and 1 or 0

		self.model_display:SetData(display_data)
		self.node_list["obj_name"].text.text = self.param.name or ""
    end
end