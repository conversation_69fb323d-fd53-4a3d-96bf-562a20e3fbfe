EquipBaptizeUpGradeView = EquipBaptizeUpGradeView or BaseClass(SafeBaseView)

function EquipBaptizeUpGradeView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_baptize_up_grade_view")
end

function EquipBaptizeUpGradeView:SetDataAndOpen(info)
    self.info = info

    if not IsEmptyTable(self.info) then
        if self:IsOpen() then
            self:Flush()
        else
            self:Open()
        end
    end
end

function EquipBaptizeUpGradeView:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end
end

function EquipBaptizeUpGradeView:ReleaseCallBack()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function EquipBaptizeUpGradeView:OnFlush()
    if not IsEmptyTable(self.info) then
        self.item:SetData(self.info)

        local baptize_part_info = EquipmentWGData.Instance:GetBaptizeInfoByEquipBodyIndex(self.info.index)
        if baptize_part_info and baptize_part_info.grade then
            -- local str = Language.Equipment.BaptizeGradeColorName[baptize_part_info.grade] or ""
            local target_grade = EquipmentWGData.Instance:GetBaptizeTargetGrade(self.info.index)
            local str = Language.Equipment.BaptizeGradeColorName[target_grade] or ""
            self.item:SetRightTopImageText(str)
        end
    end
end