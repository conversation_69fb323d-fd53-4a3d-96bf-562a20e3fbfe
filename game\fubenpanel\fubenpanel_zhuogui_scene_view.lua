FuBenPanelZhouGuiSceneView = FuBenPanelZhouGuiSceneView or BaseClass(SafeBaseView)
function FuBenPanelZhouGuiSceneView:__init()
	self:AddViewResource(0, "uis/view/zhuogui_scene_ui_prefab", "layout_zhuogui_scene_view")
	self.view_cache_time = 0
    self.open_tween = nil
	self.close_tween = nil
    self.view_layer = UiLayer.MainUIHigh

    self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
end

function FuBenPanelZhouGuiSceneView:ReleaseCallBack()
    self.old_event_id = nil
    self.cur_goal_state = nil
    self.cur_monstor_obj_data = nil
    self.cur_destination_obj_id = nil
    self.cur_gather_obj_data = nil
    self.destination_x = nil
    self.destination_y = nil
    self.cur_role_pos_x = nil
    self.cur_role_pos_y = nil
    self.is_update_arrow = nil
    RemindManager.Instance:UnBind(self.remind_callback)

    if self.mainrole_pos_change_event then
        GlobalEventSystem:UnBind(self.mainrole_pos_change_event)
        self.mainrole_pos_change_event = nil
    end

    if self.obj_leave_event then
		GlobalEventSystem:UnBind(self.obj_leave_event)
		self.obj_leave_event = nil
	end

    if self.monster_enter_visible then
		GlobalEventSystem:UnBind(self.monster_enter_visible)
		self.monster_enter_visible = nil
	end

    if self.gather_enter_visible then
		GlobalEventSystem:UnBind(self.gather_enter_visible)
		self.gather_enter_visible = nil
	end

    if self.role_start_gather_event then
		GlobalEventSystem:UnBind(self.role_start_gather_event)
		self.role_start_gather_event = nil
	end

    if self.role_complete_gather_event then
		GlobalEventSystem:UnBind(self.role_complete_gather_event)
		self.role_complete_gather_event = nil
	end

    if self.role_stop_gather_event then
		GlobalEventSystem:UnBind(self.role_stop_gather_event)
		self.role_stop_gather_event = nil
	end

    if self.camera_handle_change then
        GlobalEventSystem:UnBind(self.camera_handle_change)
        self.camera_handle_change = nil
    end

    if self.new_event_alert then
        self.new_event_alert:DeleteMe()
        self.new_event_alert = nil
    end

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

    if self.tired_root_obj then
        ResMgr:Destroy(self.tired_root_obj)
        self.tired_root_obj = nil
    end
end

function FuBenPanelZhouGuiSceneView:LoadCallBack()
    self.old_event_id = nil
    self.destination_x = nil
    self.destination_y = nil
    self.cur_role_pos_x = nil
    self.cur_role_pos_y = nil

    self.cur_event_state = nil
    self.cur_goal_state = GHOST_FB_GOAL_STATUS.FIND
    self.cur_monstor_obj_data = nil
    self.cur_gather_obj_data = nil
    self.cur_destination_obj_id = nil
    self.mainrole_pos_change_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.OnMainRolePosChange, self))
    self.obj_leave_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DELETE, BindTool.Bind(self.OnObjDelete, self))
    self.monster_enter_visible = GlobalEventSystem:Bind(ObjectEventType.VISIBLE_OBJ_ENTER_MONSTER, BindTool.Bind(self.OnMonsterEnterVisible, self))
    self.gather_enter_visible = GlobalEventSystem:Bind(ObjectEventType.VISIBLE_OBJ_ENTER_GATHER, BindTool.Bind(self.OnGatherEnterVisible, self))
    self.role_start_gather_event = GlobalEventSystem:Bind(ObjectEventType.START_GATHER, BindTool.Bind(self.OnRoleStartGather, self))
    self.role_complete_gather_event = GlobalEventSystem:Bind(ObjectEventType.STOP_GATHER, BindTool.Bind(self.OnRoleCompleteGather, self))
    self.role_stop_gather_event = GlobalEventSystem:Bind(ObjectEventType.STOP_GATHER, BindTool.Bind(self.OnRoleStopGather, self))
    self.camera_handle_change = GlobalEventSystem:Bind(MainUIEventType.CAMERA_HANDLE_CHANGE, BindTool.Bind(self.OnCameraHandleChange, self))

	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
    XUI.AddClickEventListener(self.node_list["btn_event"], BindTool.Bind(self.OnClickEvent, self))
    XUI.AddClickEventListener(self.node_list["btn_goal"], BindTool.Bind(self.OnClickGoal, self))
    XUI.AddClickEventListener(self.node_list["btn_compass"], BindTool.Bind(self.OnClickCompass, self))
    XUI.AddClickEventListener(self.node_list["tired_root"], BindTool.Bind(self.OnClickTiredTips, self))
    XUI.AddClickEventListener(self.node_list["btn_task"], BindTool.Bind(self.OnClickOpenZhuoGuiTask, self))
    XUI.AddClickEventListener(self.node_list["btn_tujie"], BindTool.Bind(self.OnClickOpenTuJieView, self))
    
    RemindManager.Instance:Bind(self.remind_callback, RemindName.ZhuoGuiFuBen)
    self:OnViewLoadCalcCurObjAndStatus()
end

function FuBenPanelZhouGuiSceneView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()

	if self.node_list["panel_root"] then
		self.obj = self.node_list["panel_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3.zero
		self.obj.transform.localScale = Vector3.one
	end

    if self.node_list["tired_root"] then
        self.tired_root_obj = self.node_list["tired_root"].gameObject
        mainui_ctrl:AddBtnToFbIconGroup2Line(self.tired_root_obj)
    end

	if self.is_out_fb then
        self.obj:SetActive(false)
        self.tired_root_obj:SetActive(false)
    else
        self.obj:SetActive(true)
        self.tired_root_obj:SetActive(true)
    end

    self.is_out_fb = nil
end

function FuBenPanelZhouGuiSceneView:OpenCallBack()
    self.is_out_fb = nil
end

function FuBenPanelZhouGuiSceneView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end

    if self.tired_root_obj then
        self.tired_root_obj:SetActive(false)
    end
end

function FuBenPanelZhouGuiSceneView:OnRemindChange(remind_name, num)
    if remind_name == RemindName.ZhuoGuiFuBen then
        self.node_list["btn_task_remind"]:SetActive(num > 0)
    end
end

-- 角色位置
function FuBenPanelZhouGuiSceneView:OnMainRolePosChange(pos_x, pos_y)
    self.cur_role_pos_x = pos_x
    self.cur_role_pos_y = pos_y

    if self.destination_x == nil or self.destination_y == nil then
        return
    end

    local distance = GameMath.GetDistance(pos_x, pos_y, self.destination_x, self.destination_y, true)
    -- print_error("---distance---", distance, self.cur_event_state, self.cur_goal_state)

    local goal_state
    if distance <= 10 and self.cur_destination_obj_id then
        goal_state = GHOST_FB_GOAL_STATUS.ARRIVE
    elseif self.cur_monstor_obj_data and self.cur_monstor_obj_data.type == 3 then
        goal_state = GHOST_FB_GOAL_STATUS.BOSS
    elseif self.cur_gather_obj_data and self.cur_gather_obj_data.type == 3 then
        goal_state = GHOST_FB_GOAL_STATUS.STELE
    elseif self.cur_gather_obj_data and self.cur_gather_obj_data.type == 2 then
        goal_state = GHOST_FB_GOAL_STATUS.BOX
    elseif self.cur_monstor_obj_data then
        goal_state = GHOST_FB_GOAL_STATUS.MONSTER
    else
        goal_state = GHOST_FB_GOAL_STATUS.FIND
    end
    
    if goal_state and goal_state ~= self.cur_goal_state then
        self.cur_goal_state = goal_state
        self:UpdateZhuoGuiState()
    end

    if self.cur_goal_state == GHOST_FB_GOAL_STATUS.FIND then
        self:UpdatePosStr(self.destination_x, self.destination_y, pos_x, pos_y, true)
    end
end

function FuBenPanelZhouGuiSceneView:ToDestinationDistanceIsMeet()
    local main_role = Scene.Instance:GetMainRole()
    if main_role then
        local pos_x, pos_y = main_role:GetLogicPos()
        if self.destination_x == nil or self.destination_y == nil then
            return false
        end

        local distance = GameMath.GetDistance(pos_x, pos_y, self.destination_x, self.destination_y, true)
        return distance <= 10
    end

    return false
end

-- 界面加载完后，再次获取下场景的对象信息
function FuBenPanelZhouGuiSceneView:OnViewLoadCalcCurObjAndStatus()
    local gather_list = Scene.Instance:GetGatherList()
	for k,v in pairs(gather_list) do
        local vo = v.vo
        if vo.special_gather_type == SPECIAL_GATHER_TYPE.GHOST_FB_EVENT then
            self.cur_destination_obj_id = vo.obj_id
        elseif vo.special_gather_type == SPECIAL_GATHER_TYPE.GHOST_FB_EVENT_FINISH then
            local cfg = FuBenPanelWGData.Instance:GetZhuoGuiEventCfg(vo.param4)
            if cfg then
                self.cur_gather_obj_data = {obj_id = vo.obj_id, type = cfg.type}
            end
        end
	end

    local uuid = RoleWGData.Instance:GetUUid()
    local monster_list = Scene.Instance:GetMonsterList()
    for k,v in pairs(monster_list) do
        local vo = v.vo
        if vo.wabao_owner_uuid == uuid then
            local cfg = FuBenPanelWGData.Instance:GetZhuoGuiEventCfg(vo.special_param)
            if cfg then
                if cfg.type == 3 then
                    self.cur_monstor_obj_data = {obj_id = vo.obj_id, type = 3}
                else
                    self.cur_monstor_obj_data = {obj_id = vo.obj_id, type = 1}
                end
            end
        end
    end

    local goal_state
    if self.cur_destination_obj_id and self:ToDestinationDistanceIsMeet() then
        goal_state = GHOST_FB_GOAL_STATUS.ARRIVE
    elseif self.cur_monstor_obj_data and self.cur_monstor_obj_data.type == 3 then
        goal_state = GHOST_FB_GOAL_STATUS.BOSS
    elseif self.cur_gather_obj_data and self.cur_gather_obj_data.type == 3 then
        goal_state = GHOST_FB_GOAL_STATUS.STELE
    elseif self.cur_gather_obj_data and self.cur_gather_obj_data.type == 2 then
        goal_state = GHOST_FB_GOAL_STATUS.BOX
    elseif self.cur_monstor_obj_data then
        goal_state = GHOST_FB_GOAL_STATUS.MONSTER
    else
        goal_state = GHOST_FB_GOAL_STATUS.FIND
    end

    self.cur_goal_state = goal_state
    self:OnCameraHandleChange()
end


-- obj被销毁
function FuBenPanelZhouGuiSceneView:OnObjDelete(del_obj)
    if del_obj == nil or del_obj.vo == nil then
        return
    end
    -- print_error("----obj离开视野 uid------", del_obj.vo.obj_id, del_obj:GetType())
    local obj_type = del_obj:GetType()
    if obj_type ~= SceneObjType.GatherObj and obj_type ~= SceneObjType.Monster then
        return
    end

    local obj_id = del_obj.vo.obj_id
    if self.cur_monstor_obj_data and obj_id == self.cur_monstor_obj_data.obj_id then
        self.cur_monstor_obj_data = nil
    elseif self.cur_gather_obj_data and obj_id == self.cur_gather_obj_data.obj_id then
        self.cur_gather_obj_data = nil
    elseif obj_id == self.cur_destination_obj_id then
        self.cur_destination_obj_id = nil
    end

    local goal_state
    if not self.cur_monstor_obj_data and not self.cur_gather_obj_data then
        if self.cur_destination_obj_id then
            if self:ToDestinationDistanceIsMeet() then
                goal_state = GHOST_FB_GOAL_STATUS.ARRIVE
            else
                goal_state = GHOST_FB_GOAL_STATUS.FIND
            end
        else
            goal_state = GHOST_FB_GOAL_STATUS.FIND
        end
    end

    if not goal_state or goal_state == self.cur_goal_state then
        return
    end

    self.cur_goal_state = goal_state
    self:UpdateZhuoGuiState()
end

-- 怪物进入视野
function FuBenPanelZhouGuiSceneView:OnMonsterEnterVisible(vo, obj)
    -- print_error("----怪物进入视野 uid------", vo.obj_id, vo.wabao_owner_uuid, RoleWGData.Instance:GetUUid(), vo.special_param)
    if vo.wabao_owner_uuid ~= RoleWGData.Instance:GetUUid() then
        return
    end

    local goal_state
    local cfg = FuBenPanelWGData.Instance:GetZhuoGuiEventCfg(vo.special_param)
    if cfg then
        if cfg.type == 3 then
            goal_state = GHOST_FB_GOAL_STATUS.BOSS
            self.cur_monstor_obj_data = {obj_id = vo.obj_id, type = 3}
        else
            goal_state = GHOST_FB_GOAL_STATUS.MONSTER
            self.cur_monstor_obj_data = {obj_id = vo.obj_id, type = 1}
        end
    end

    -- print_error("----怪物进入视野 goal_state------", goal_state, vo.obj_id)
    if not goal_state or goal_state == self.cur_goal_state then
        return
    end

    self.cur_goal_state = goal_state
    self:UpdateZhuoGuiState()
end

-- 采集物进入视野
function FuBenPanelZhouGuiSceneView:OnGatherEnterVisible(vo, obj)
    -- print_error("----采集物进入视野 uid------", vo.special_gather_type, vo.param, RoleWGData.Instance:InCrossGetOriginUid(), vo.param4)
    if vo.param ~= RoleWGData.Instance:InCrossGetOriginUid() then
        return
    end

    local goal_state
    if vo.special_gather_type == SPECIAL_GATHER_TYPE.GHOST_FB_EVENT then
        self.cur_destination_obj_id = vo.obj_id
        -- goal_state = GHOST_FB_GOAL_STATUS.ARRIVE
    elseif vo.special_gather_type == SPECIAL_GATHER_TYPE.GHOST_FB_EVENT_FINISH then
        local cfg = FuBenPanelWGData.Instance:GetZhuoGuiEventCfg(vo.param4)
        if cfg then
            if cfg.type == 2 then
                goal_state = GHOST_FB_GOAL_STATUS.BOX
            elseif cfg.type == 3 then
                goal_state = GHOST_FB_GOAL_STATUS.STELE
            end

            self.cur_gather_obj_data = {obj_id = vo.obj_id, type = cfg.type}
        end
    end

    -- print_error("----采集物进入视野 goal_state------", goal_state, vo.obj_id)
    if not goal_state or goal_state == self.cur_goal_state then
        return
    end
    
    self.cur_goal_state = goal_state
    self:UpdateZhuoGuiState()
end

function FuBenPanelZhouGuiSceneView:OnRoleStartGather()
    if self.node_list["slider_effect"] then
        self.node_list["slider_effect"]:SetActive(false)
        self.node_list["slider_effect"]:SetActive(true)
    end
end

function FuBenPanelZhouGuiSceneView:OnRoleCompleteGather()
    if self.node_list["slider_effect"] then
        self.node_list["slider_effect"]:SetActive(false)
    end
end

function FuBenPanelZhouGuiSceneView:OnRoleStopGather()
    if self.node_list["slider_effect"] then
        self.node_list["slider_effect"]:SetActive(false)
    end
end

local UpdateArrowTime = 0
function FuBenPanelZhouGuiSceneView:OnCameraHandleChange()
    if self.cur_event_state ~= GHOST_FB_EVENT_STATUS.ACCEPT or self.cur_goal_state == GHOST_FB_GOAL_STATUS.ARRIVE then
        return
    end

    local now_time = Status.NowTime
	if now_time < UpdateArrowTime + 0.02 then
		return
	end
    UpdateArrowTime = now_time

    if self.cur_role_pos_x == nil or self.cur_role_pos_y == nil or self.destination_x == nil or self.destination_y == nil then
        return
    end

    local camera_forward = MainCamera.transform.forward
    local camera_vector2 = {x = camera_forward.x, y = camera_forward.z}

    local destination_voctor2 = {x = self.destination_x - self.cur_role_pos_x, y = self.destination_y - self.cur_role_pos_y}
    local destination_unit_vector2 = GameMath.GetVector2Unit(destination_voctor2)
    local rotation = GameMath.GetVector2Angle(camera_vector2, destination_unit_vector2)
    local cross = GameMath.GetVector2Cross(camera_vector2, destination_unit_vector2)
    local new_rotation = cross.z >= 0 and rotation or - rotation
    if self.node_list["compass_arrow"] then
        self.node_list["compass_arrow"].transform.rotation = Quaternion.Euler(0, 0, new_rotation)
    end
end

function FuBenPanelZhouGuiSceneView:UpdateZhuoGuiState()
    -- print_error("----UpdateZhuoGuiState----", self.cur_event_state, self.cur_goal_state)
    if self.cur_event_state == nil then
        return
    end

    local is_event_none = self.cur_event_state == GHOST_FB_EVENT_STATUS.NONE
    local is_event_accept = self.cur_event_state == GHOST_FB_EVENT_STATUS.ACCEPT
    local is_event_finish = self.cur_event_state == GHOST_FB_EVENT_STATUS.FINISH

    local other_cfg = FuBenPanelWGData.Instance:GetZhuoGuiOtherCfg()
    -- 事件文本内容更新
    if is_event_none then
        self.node_list["compass"]:CustomSetActive(false)
        self.node_list["goal"]:CustomSetActive(false)

        local npc_cfg = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[other_cfg.npcid]
        if npc_cfg then
            self.node_list["event_desc"].text.text = string.format(Language.ZhuoGuiFuBen.EventDesc[self.cur_event_state], npc_cfg.name)
        else
            self.node_list["event_desc"].text.text = ""
        end

        return
    else
        local is_show = is_event_accept or self.cur_monstor_obj_data ~= nil or self.cur_gather_obj_data ~= nil
        self.node_list["compass"]:CustomSetActive(is_show)
        self.node_list["goal"]:CustomSetActive(is_show)
    end

    if is_event_finish then
        self.node_list["event_desc"].text.text = Language.ZhuoGuiFuBen.EventDesc[self.cur_event_state]
    else
        local max_count = other_cfg.max_refresh_event_times
        local cur_count = FuBenPanelWGData.Instance:GetZhuoGuiEventTimes()
        self.node_list["event_desc"].text.text = string.format(Language.ZhuoGuiFuBen.EventDesc[self.cur_event_state], max_count - cur_count, max_count)
    end

    if self.cur_goal_state == nil then
        return
    end
    
    local goal_state = self.cur_goal_state
    local is_goal_find = goal_state == GHOST_FB_GOAL_STATUS.FIND
    -- 目标文本内容更新
    if is_goal_find and is_event_accept then
        self:UpdateOnceCurPosText()
    elseif is_goal_find then
        self.node_list["goal_desc"].text.text = Language.ZhuoGuiFuBen.GoalDesc[0]
    else
        self.node_list["goal_desc"].text.text = Language.ZhuoGuiFuBen.GoalDesc[goal_state]
    end
    
    -- 罗盘
    local is_show_arrow = is_event_accept and goal_state ~= GHOST_FB_GOAL_STATUS.ARRIVE
    if is_show_arrow and self.is_update_arrow then
        self:OnCameraHandleChange()
        self.is_update_arrow = nil
    end
    self.node_list["compass_arrow"]:CustomSetActive(is_show_arrow)


    local hide_find_icon = goal_state == GHOST_FB_GOAL_STATUS.ARRIVE
                                        or goal_state == GHOST_FB_GOAL_STATUS.BOX
                                        or goal_state == GHOST_FB_GOAL_STATUS.STELE
    self.node_list["find_icon"]:CustomSetActive(not hide_find_icon)
    self.node_list["explore_icon"]:CustomSetActive(goal_state == GHOST_FB_GOAL_STATUS.ARRIVE)
    self.node_list["gather_icon"]:CustomSetActive(goal_state == GHOST_FB_GOAL_STATUS.BOX)
    self.node_list["seal_icon"]:CustomSetActive(goal_state == GHOST_FB_GOAL_STATUS.STELE)
end

function FuBenPanelZhouGuiSceneView:UpdateOnceCurPosText()
    local main_role = Scene.Instance:GetMainRole()
    if main_role and self.destination_x ~= nil and self.destination_y ~= nil then
        local pos_x, pos_y = main_role:GetLogicPos()
        self.cur_role_pos_x = pos_x
        self.cur_role_pos_y = pos_y
        self:UpdatePosStr(self.destination_x, self.destination_y, pos_x, pos_y)
    end
end

function FuBenPanelZhouGuiSceneView:UpdatePosStr(destination_x, destination_y, pos_x, pos_y, need_update_arrow)
    if self.node_list["goal_desc"] then
        self.node_list["goal_desc"].text.text = string.format(Language.ZhuoGuiFuBen.GoalDesc[1], destination_x, destination_y, pos_x, pos_y)
    end

    if need_update_arrow then
        self:OnCameraHandleChange()
    end
end

function FuBenPanelZhouGuiSceneView:OnFlush()
    self.cur_event_state = FuBenPanelWGData.Instance:GetZhuoGuiEventSatus()
    local other_cfg = FuBenPanelWGData.Instance:GetZhuoGuiOtherCfg()
    local cur_nuqi = FuBenPanelWGData.Instance:GetZhuoGuiNuQi()
    self.node_list["tired_value"].text.text = string.format(Language.FuBenPanel.FuBenEnterTime, cur_nuqi, other_cfg.nuqi_max)

    local event_info = FuBenPanelWGData.Instance:GetGhostFbEventInfo()
    if self.cur_event_state == GHOST_FB_EVENT_STATUS.ACCEPT then
        self.destination_x = event_info.destination_x
        self.destination_y = event_info.destination_y
    else
        self.destination_x = nil
        self.destination_y = nil
    end

    self.is_update_arrow = true
    self:UpdateZhuoGuiState()

    local event_id = event_info.event_id

    -- 新事件刷新
    if self.old_event_id and self.old_event_id > 0 and event_id == 0 then
        local npc_cfg = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[other_cfg.npcid]
        local npc_name = npc_cfg and npc_cfg.name or ""
        if nil == self.new_event_alert then
            self.new_event_alert = Alert.New()
        end

        self.new_event_alert:SetLableString(string.format(Language.ZhuoGuiFuBen.NewEventTips, npc_name))
        self.new_event_alert:SetOkFunc(function()
            FuBenPanelWGCtrl.Instance:FindZhuoGuiNpc()
        end)

        self.new_event_alert:Open()
    elseif self.old_event_id and self.old_event_id > 0 and event_id ~= self.old_event_id then
        local cfg = FuBenPanelWGData.Instance:GetZhuoGuiEventCfg(self.old_event_id)
        if cfg then
            TipWGCtrl.Instance:ShowSystemMsg(cfg.talk)
        end
    end

    self.old_event_id = event_id
end


function FuBenPanelZhouGuiSceneView:OnClickEvent()
    FuBenPanelWGCtrl.Instance:FindZhuoGuiNpc()
end

function FuBenPanelZhouGuiSceneView:OnClickGoal()
    self:OnClickCompass()
end

function FuBenPanelZhouGuiSceneView:OnClickCompass()
    local obj_id
    if self.cur_goal_state == GHOST_FB_GOAL_STATUS.ARRIVE then
        obj_id = self.cur_destination_obj_id
    elseif self.cur_goal_state == GHOST_FB_GOAL_STATUS.BOX
    or self.cur_goal_state == GHOST_FB_GOAL_STATUS.STELE then
        if self.cur_gather_obj_data then
            obj_id = self.cur_gather_obj_data.obj_id
        end
    end

    if obj_id then
        local obj = Scene.Instance:GetObj(obj_id)
        if obj and obj.vo then
            local vo = obj.vo
            MoveCache.SetEndType(MoveEndType.GatherById)
		    MoveCache.param1 = vo.gather_id
            GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), vo.pos_x, vo.pos_y, 2)
        end
    end
end

function FuBenPanelZhouGuiSceneView:OnClickTiredTips()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.ZhuoGuiFuBen.NuQiWanFaTips[1])
	rule_tip:SetContent(Language.ZhuoGuiFuBen.NuQiWanFaTips[2], nil, nil, nil, true)
end

function FuBenPanelZhouGuiSceneView:OnClickOpenZhuoGuiTask()
    FuBenPanelWGCtrl.Instance:OpenZhuoGuiFubenTaskView()
end

function FuBenPanelZhouGuiSceneView:OnClickOpenTuJieView()
    FuBenPanelWGCtrl.Instance:OpenZhuoGuiBossTuJieView()
end