MultiFunctionWGData = MultiFunctionWGData or BaseClass()

function MultiFunctionWGData:__init()
	if MultiFunctionWGData.Instance then 
		print_error("[MultiFunctionWGData] Attemp to create a singleton twice !")
	end

	MultiFunctionWGData.Instance = self
	--self:InitCharmCfg()
	self:InitDaoHangCfg()
	self:InitPiXiuCfg()
end

function MultiFunctionWGData:__delete()
	MultiFunctionWGData.Instance = nil
	--self:DeleteCharmData()
	self:DeleteDaoHangData()
	self:DeletePiXiuData()
end

