
--boss召唤面板
BossInvokeView = BossInvokeView or BaseClass(SafeBaseView)

function BossInvokeView:__init()
    self.view_name = "BossInvokeView"
    -- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -19), sizeDelta = Vector2(780, 520)})
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_invoke")
	self:SetMaskBg(true)
end

function BossInvokeView:OpenCallBack()

end

function BossInvokeView:LoadCallBack()

	-- self.node_list.title_view_name.text.text = Language.Boss.LieBiao
    -- self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    -- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	self.ph_boss_list = AsyncListView.New(BossInvokeRender, self.node_list["ph_boss_list"])

    if self.item_data_change_callback == nil then
        self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
	    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
    end

    self.node_list["use_item_icon"].button:AddClickListener(BindTool.Bind(self.ShowCostItemTips, self))
    self.use_item_id = 0
end

function BossInvokeView:ReleaseCallBack()
	if nil ~= self.ph_boss_list then
		self.ph_boss_list:DeleteMe()
		self.ph_boss_list = nil
    end

    if self.item_data_change_callback then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
        self.item_data_change_callback = nil
    end

    self.boss_data = nil
    self.use_item_id = nil
end

function BossInvokeView:CloseCallBack()
    self.boss_data = nil
    self.use_item_id = nil
end

function BossInvokeView:SetData(data, is_from_first_kill)
    self.boss_data = data
    self.is_from_first_kill = is_from_first_kill
end

function BossInvokeView:OnFlush()
    local scene_id = Scene.Instance:GetSceneId()
    local boss_list = BossWGData.Instance:GetBossListBySceneId(scene_id)
    self.ph_boss_list:SetDataList(boss_list)

    if not self.use_item_id or self.use_item_id == 0 then
        self.use_item_id = self:GetUseItemId()
    end

    self:FlushItemNum()
end

function BossInvokeView:FlushItemNum()
    if self.use_item_id and self.use_item_id > 0 then
        local item_cfg = ItemWGData.Instance:GetItemConfig(self.use_item_id)
        self.node_list["use_item_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))

        local item_count = 0
        local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
		local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
		if is_in_team and is_leader and self.use_item_id then
			item_count = NewTeamWGData.Instance:GetSelectUseShareItemInfo(self.use_item_id)
		else
			item_count = ItemWGData.Instance:GetItemNumInBagById(self.use_item_id)
		end

        self.node_list.item_num.text.text = item_count
    end
end

function BossInvokeView:GetUseItemId()
    local scene_type = Scene.Instance:GetSceneType()
    local use_item_id = 0
    local invoke_cfg = MainuiWGData.Instance:GetBossInvokeCfg()
    if scene_type == SceneType.KF_BOSS then --蛮荒
        use_item_id =  tonumber(invoke_cfg.boss_invoke_card_2)
    elseif scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
        use_item_id = tonumber(invoke_cfg.boss_invoke_card_1)
    end

    return use_item_id
end

function BossInvokeView:OnItemDataChange(change_item_id)
    if self.use_item_id == change_item_id then
        self:FlushItemNum()
	end
end

function BossInvokeView:ShowCostItemTips()
    if self.use_item_id and self.use_item_id > 0 then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.use_item_id}) 
    end
end

-------------------------------------------------------------------------------------------
BossInvokeRender = BossInvokeRender or BaseClass(BaseRender)
function BossInvokeRender:__delete()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end
    
	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
end

function BossInvokeRender:LoadCallBack()
    self.node_list["btn_invoke"].button:AddClickListener(BindTool.Bind(self.OnClickBossInvoke, self))--boss召唤
end

function BossInvokeRender:OnFlush()
    local monster_info = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
    self.node_list.jieshu_txt.text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(monster_info.boss_jieshu))
    local level
    if self.data.boss_level then
        level = self.data.boss_level
    else
        level = monster_info.level
    end
    --self.node_list.render_bg:SetActive(self.index % 2 == 1)
    self.node_list.level.text.text = "Lv." .. level
    self.node_list.boss_name.text.text = monster_info.name
    self:RefreshRemainTime()
    if self.refresh_event == nil then
        self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime,self),1)
    end
end

function BossInvokeRender:RefreshRemainTime()
    local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
	if boss_server_info == nil then
		boss_server_info = BossWGData.Instance:GetCrossBossById(self.data.boss_id)
	end
	local time = 0
	if boss_server_info ~= nil then
		time = (boss_server_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime()
	end
	local state = time > 1
	if state then
		self.node_list["time"].text.text = ToColorStr(TimeUtil.FormatSecond(time), COLOR3B.PINK)
	else
		self.node_list["time"].text.text = Language.Boss.HasRefresh
		if self.refresh_event then
			GlobalTimerQuest:CancelQuest(self.refresh_event)
			self.refresh_event = nil
		end
	end
end

function BossInvokeRender:OnClickBossInvoke()
    local boss_data = BossWGData.Instance:GetBossAllInfoByBossId(self.data.boss_id)
    if IsEmptyTable(boss_data) then
        print_error("获取不到boss配置，boss_id=", self.data.boss_id)
        return
    end
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local monster_info = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
    local boss_level = monster_info.level
    local item_id = self:GetUseItemId()
    local num, role_id = NewTeamWGData.Instance:GetSelectUseShareItemInfo(item_id)
    if role_level - boss_level >= boss_data.max_delta_level then
        if nil == self.alert_window then
            self.alert_window = Alert.New(nil, nil, nil, nil, true)
        end
        self.alert_window:SetLableString(Language.Boss.InvokeLvToLow)
        self.alert_window:SetShowCheckBox(true, "boss_invoke")
        self.alert_window:SetOkFunc(
            function()
                FuBenWGCtrl.Instance:SendBossRebirth(FB_USE_ITEM.INVOKEBOSS, self.data.boss_id, 0, role_id)
                --BossWGCtrl.Instance:CloseBossInvokeView()
            end)
        self.alert_window:Open()
    else
        FuBenWGCtrl.Instance:SendBossRebirth(FB_USE_ITEM.INVOKEBOSS, self.data.boss_id, 0, role_id)
        --BossWGCtrl.Instance:CloseBossInvokeView()
    end
    
end

function BossInvokeRender:GetUseItemId()
    local scene_type = Scene.Instance:GetSceneType()
    local use_item_id = 0
    local invoke_cfg = MainuiWGData.Instance:GetBossInvokeCfg()
    if scene_type == SceneType.KF_BOSS then --蛮荒
        use_item_id =  tonumber(invoke_cfg.boss_invoke_card_2)
    elseif scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
        use_item_id = tonumber(invoke_cfg.boss_invoke_card_1)
    end

    return use_item_id
end
