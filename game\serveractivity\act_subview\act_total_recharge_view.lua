----------------------------------------------------
-- 累计充值
----------------------------------------------------
ActTotalRechargeView = ActTotalRechargeView or BaseClass(ActBaseViewTwo)

function ActTotalRechargeView:__init(act_id)
	self.ui_config = {"uis/view/act_subview_ui_prefab", "ActSubviewUi"}
	self.config_tab = {
		{"layout_act_reward_list",{0}},
	}

	self.act_id = act_id
end

function ActTotalRechargeView:__delete()

end

function ActTotalRechargeView:ReleaseCallBack()
	if self.reward_list ~= nil then 
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	self.need_refresh = nil
	self.has_load_callback = nil
end

function ActTotalRechargeView:LoadCallBack()
	self.reward_list = AsyncListView.New(ActTotalRechargeItemRender,self.node_list["ph_login_gift_list"])
	self.has_load_callback = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

function ActTotalRechargeView:RefreshView(param_list)
	if not self.has_load_callback then
		self.need_refresh = true
		return
	end
	self:RefreshTopDesc()
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(self.act_id)
	local reward_cfg = ServerActivityWGData.Instance:GetActivityItemDataListNew(self.act_id)
	self.reward_list:SetDataList(reward_cfg,3)

end



-------------------------------------------------------------------
------------------         itemRender           -------------------
-------------------------------------------------------------------
ActTotalRechargeItemRender = ActTotalRechargeItemRender or BaseClass(BaseRender)
function ActTotalRechargeItemRender:__init()
	
end

function ActTotalRechargeItemRender:__delete()
	
end

function ActTotalRechargeItemRender:ReleaseCallBack()
	for i,v in ipairs(self.item_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end

	self.item_list = {}
end

function ActTotalRechargeItemRender:LoadCallBack()
	self.item_list = {}
	for i=1,5 do
		self.item_list[i] = ItemCell.New(self.node_list["ph_cell_" .. i])
	end
	XUI.AddClickEventListener(self.node_list.btn_receive, BindTool.Bind1(self.OnClickLingQu, self))

	self.btn_cache = {1,1,1}
	self.cell_cache = {1,1,1,1,1}
	self.btn_state = {self.node_list.btn_receive,self.node_list.img_has,self.node_list.img_no}
end

function ActTotalRechargeItemRender:OnFlush()
	if nil == self.data then return end
	local item_data = self.data.item_list
	local cell_cache = {false,false,false,false,false}
	for i=1,5 do
		if nil == item_data[i] then
			cell_cache[i] = false
		else
			self.item_list[i]:SetData(item_data[i])
			cell_cache[i] = true
		end
	end

	for k,v in pairs(cell_cache) do
		if self.cell_cache[k] ~= cell_cache[k] then
			self.cell_cache[k] = cell_cache[k]
			self.item_list[k]:SetActive(cell_cache[k])
		end
	end

	local btn_cache = {false,false,false}
	if self.data.is_geted then
		btn_cache = {true,false,false}
	else
		btn_cache = {false,false,true}
	end

	if self.data.is_have_opp then
		btn_cache = {false,true,false}
	end

	for k,v in pairs(btn_cache) do
		if self.btn_cache[k] ~= btn_cache[k] then
			self.btn_cache[k] = btn_cache[k]
			self.btn_state[k]:SetActive(btn_cache[k])
		end
	end
	self.node_list.lbl_desc.text.text = ToColorStr(self.data.instruction,COLOR3B.WHITE)
end

function ActTotalRechargeItemRender:OnClickLingQu()
	local param_t ={}
	param_t = {
		rand_activity_type = self.data.reward_type,
		opera_type = RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE.RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_FETCH_REWARD,
		param_1 = self.data.seq
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

--override
function ActTotalRechargeItemRender:CreateSelectEffect()
	
end