﻿#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using Nirvana;
using System;

public abstract class DynamicChecker
{
    protected string title;
    protected string notice;
    private List<CheckObject> list = new List<CheckObject>();
    protected bool hasHighLevel;

    public bool CheckBundle(string bundleName, string assetName)
    {
		var filter = GetFilterList();
        if (filter.Contains(bundleName))
        {
            return false;
        }
        bool needCheck = NeedCheck(bundleName);
        if (needCheck)
        {
            assetName = Path.GetFileNameWithoutExtension(assetName);
            var assetPaths = AssetDatabase.GetAssetPathsFromAssetBundleAndAssetName(bundleName, assetName);
            if (assetPaths.Length > 0)
            {
                var assetPath = assetPaths[0];
                Check(assetPath, objects =>
                {
                    if (objects.Length > 0)
                    {
                        for (int i = 0; i < objects.Length; ++i)
                        {
                            var obj = objects[i];
                            list.Add(obj);
                            hasHighLevel = obj.warningLevel == WarningLevel.High;
						}
						ShowWindow();
					}
				});
                
            }
        }

        return needCheck;
    }

	public void ClearShowWindowList()
	{
		list.Clear();
	}

    private void ShowWindow()
    {
        if (this.list.Count <= 0)
            return;
        var window = this.GetWindow();
        window.Init();
        window.List = this.list;
        window.Show();
        window.position = new Rect(new Rect(1200, 600, 800, 600));
        window.Focus();
        window.onClose = () => 
        {
            //Scheduler.Delay(ShowWindow, 1);
        };

        window.onLostFocus = () =>
        {
            if (hasHighLevel)
            {
                //window.position = new Rect(new Rect(1200, 600, 800, 600));
            }
        };
    }

    protected abstract bool NeedCheck(string bundleName);
    protected abstract void Check(string assetPath, Action<CheckObject[]> action);
    protected abstract BaseWarningWindow GetWindow();
    protected abstract HashSet<string> GetFilterList();
}

public enum WarningLevel
{
    Low, Normal, High
}

public struct CheckObject
{
    public UnityEngine.Object obj;
    public string exception;
    public WarningLevel warningLevel;
    public GameObject node;

    public CheckObject(UnityEngine.Object obj, string exception, WarningLevel warningLevel, GameObject node = null)
    {
        this.obj = obj;
        this.exception = exception;
        this.warningLevel = warningLevel;
        this.node = node;
    }
}
#endif
