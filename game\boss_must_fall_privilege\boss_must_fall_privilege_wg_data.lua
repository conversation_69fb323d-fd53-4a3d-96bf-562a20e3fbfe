BossMustFallPrivilegeWGData = BossMustFallPrivilegeWGData or BaseClass()

function BossMustFallPrivilegeWGData:__init()
    if BossMustFallPrivilegeWGData.Instance ~= nil then
		print_error("[BossMustFallPrivilegeWGData] attempt to create singleton twice!")
		return
	end

    local worldboss_auto = ConfigManager.Instance:GetAutoConfig("worldboss_auto")
    self.level_cfg = ListToMap(worldboss_auto.vipBoss_extra_rewards_upgrade, "level")
    self.other_time_cfg = ListToMap(worldboss_auto.vipBoss_extra_rewards_free_acquire, "seq")
    self.extra_rewards_cost_cfg = ListToMap(worldboss_auto.vipBoss_extra_rewards_cost, "seq")
	self.extra_rewards_cfg = ListToMapList(worldboss_auto.vipBoss_extra_rewards, "level")

    self.bless_value = 0
	self.level = 1
	self.state = 0
	self.buy_times = 0
	self.remain_times = 0
	self.acquire_times = 0
    self.online_time = 0
	self.free_get_privilege_time = {}
	self.online_time_str = ""
	self.can_free_get_time = 0

	for k, v in pairs(self.other_time_cfg) do
		if v.free_cd > 0 then
			local time_str = TimeUtil.FormatSecondDHM7(v.free_cd)

			if self.online_time_str == "" then
				self.online_time_str = time_str
			else
				self.online_time_str = self.online_time_str .. "," .. time_str
			end
		end

		self.can_free_get_time = self.can_free_get_time + 1
	end

	BossMustFallPrivilegeWGData.Instance = self
end

function BossMustFallPrivilegeWGData:__delete()
	BossMustFallPrivilegeWGData.Instance = nil
end

function BossMustFallPrivilegeWGData:SetVipBossExtraRewardsInfo(protocol)
	local free_get_privilege_time = {}
    self.online_time = protocol.online_time         -- online_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	local index = 0
	for k, v in pairs(self.other_time_cfg) do
		if self.online_time <  v.free_cd then
			local target_time = server_time + v.free_cd - self.online_time
			table.insert(free_get_privilege_time, target_time)
		end
	end

	self.free_get_privilege_time = free_get_privilege_time
    self.bless_value = protocol.bless_value 	    -- 祝福值
	self.level = protocol.level			            -- 等级
	self.state = protocol.state     		        -- 状态
	self.buy_times = protocol.buy_times		        -- 已购买次数    
	self.remain_times = protocol.remain_times	    -- 剩余次数  当前能用次数 
	self.acquire_times = protocol.acquire_times 	-- 已免费领取次数
end

function BossMustFallPrivilegeWGData:GetLevelCfg()
    return self.level_cfg or {}
end

function BossMustFallPrivilegeWGData:GetLevelCfgByLevel(level)
    return self.level_cfg[level] or {}
end

function BossMustFallPrivilegeWGData:GetLevelRewardCfg(level)
    local reward_list = {}

	if IsEmptyTable(self.extra_rewards_cfg[level]) then
		return reward_list
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	for k, v in pairs(self.extra_rewards_cfg[level]) do
		if role_level >= v.minimum_role_level and role_level <= v.maximum_role_level then
			return SortDataByItemColor(v.reward_item)
		end
	end

	return reward_list
end

function BossMustFallPrivilegeWGData:GetNextFrteeGetTime()
	if IsEmptyTable(self.free_get_privilege_time) then
		return -1
	end

	local target_time = -1
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	for k, v in pairs(self.free_get_privilege_time) do
		if v > server_time then
			target_time = v
			break
		end
	end

	return target_time
end

function BossMustFallPrivilegeWGData:GetAllFreeTimeStr()
	return self.online_time_str
end

function BossMustFallPrivilegeWGData:GetCurLevel()
	return self.level, self:GetLevelCfgByLevel(self.level)
end

function BossMustFallPrivilegeWGData:GetCurBlessValue()
	return self.bless_value
end

function BossMustFallPrivilegeWGData:IsMaxLevel()
	return (self.level > 0) and IsEmptyTable(self:GetLevelCfgByLevel(self.level + 1))
end

-- 0为关闭，1为开启
function BossMustFallPrivilegeWGData:GetPrivilegeState()
	return self.state
end

function BossMustFallPrivilegeWGData:GetPrivilegeTime()
	return self.remain_times, self.can_free_get_time
end

function BossMustFallPrivilegeWGData:GetBuyMustFallPrivilegeCfg()
	return self.buy_times , self.extra_rewards_cost_cfg
end