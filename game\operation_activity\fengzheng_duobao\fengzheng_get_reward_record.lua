FZGetRewardRecord = FZGetRewardRecord or BaseClass(SafeBaseView)

function FZGetRewardRecord:__init()
    self:AddViewResource(0, "uis/view/operation_activity_ui/fengzheng_ui_prefab", "layout_fz_duobao_record")
	self:SetMaskBg(true, true)
end

function FZGetRewardRecord:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function FZGetRewardRecord:ShowIndexCallBack()
    FZGetRewardWGCtrl.Instance:SendOpera(FZ_XUNBAO_OPERA_TYPE.DRAW_RECORD)
    FZGetRewardWGData.Instance:UpdateRecordCount()
end

function FZGetRewardRecord:CloseCalBack()
    FZGetRewardWGData.Instance:UpdateRecordCount()
end

function FZGetRewardRecord:LoadCallBack()
    self.node_list.TextViewName.text.text = Language.Xunbao.RecordTitle
    self.record_list = AsyncListView.New(FZGetRewardRecordRender, self.node_list["record_list"])
    local view_info_cfg = FZGetRewardWGData.Instance:GetViewCfgByInterface()
    if nil == view_info_cfg or IsEmptyTable(view_info_cfg) then
        return
    end
    local asset, bundle = ResPath.GetF2RawImagesPNG(view_info_cfg.record_img_bg)
    self.node_list["img_bg"].raw_image:LoadSprite(asset, bundle, function ()
            self.node_list["img_bg"].raw_image:SetNativeSize()
        end)
end

function FZGetRewardRecord:OnFlush()
    local data = FZGetRewardWGData.Instance:GetRecordInfo()
    self.record_list:SetDataList(data)
    self.node_list["no_flag"]:SetActive(IsEmptyTable(data))
end
-------------------------------------------------------------------------------------
FZGetRewardRecordRender = FZGetRewardRecordRender or BaseClass(BaseRender)
function FZGetRewardRecordRender:OnFlush()
    self.node_list['time'].text.text = TimeUtil.FormatYMDHMS(self.data.timestamp)

    local str = Language.TSXunBao.TxtRecord
    local name = self.data.name
    local reward = FZGetRewardWGData.Instance:GetRewardInfoByRewardID(self.data.reward_id)
    reward = reward.reward_item
    if not reward then
        return
    end
    local cfg = ItemWGData.Instance:GetItemConfig(reward.item_id)
    local layer_cfg = FZGetRewardWGData.Instance:GetDangWeiCfgByGradeAndlayer(self.data.layer + 1)
    str = string.format(str, name, layer_cfg.name, ITEM_COLOR[cfg.color], cfg.name, reward.num)
    self.node_list["info"].text.text = str
end