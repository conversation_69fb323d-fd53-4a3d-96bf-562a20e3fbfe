GodPurchaseWGData = GodPurchaseWGData or BaseClass()

function GodPurchaseWGData:__init()
	if GodPurchaseWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[GodPurchaseWGData] attempt to create singleton twice!")
		return
	end

	GodPurchaseWGData.Instance = self

	self:InitCfg()
	self.grade = 1
	self.shop_buy_flag = {}
	self.show_tip = true
end

function GodPurchaseWGData:__delete()
	GodPurchaseWGData.Instance = nil
	self.grade = nil
	self.shop_buy_flag = nil
end

function GodPurchaseWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_god_rmb_buy_auto")
	self.shop_cfg = ListToMapList(cfg.shop, "grade")
	self.day_pro_cfg = ListToMapList(cfg.day_pro, "day")
	self.open_day_cfg = cfg.open_day
	self.other_cfg = cfg.other[1]
end

function GodPurchaseWGData:GetGarde()
	if self.grade == 0 then
		local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_god_rmb_buy_auto").open_day
		for k,v in ipairs(cfg) do
			if server_day >= v.start_day and server_day <= v.end_day then
				return v.grade
			end
		end
	end

	return self.grade
end

function GodPurchaseWGData:SetAllInfo(protocol)
	self.grade = protocol.grade
	self.shop_buy_flag = protocol.shop_buy_flag
end

function GodPurchaseWGData:GetShopIsBuyFlag(seq)
	return self.shop_buy_flag[seq] == 1 or false
end

function GodPurchaseWGData:GetOtherCfg()
	return self.other_cfg
end

function GodPurchaseWGData:GetCurShopCfg()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local grade
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then -- 活动开启拿服务端配置  不然根据当前天数拿配置表数据
		grade = self.grade
	else
		grade = self:GetGarde()
	end

	local cur_grade_cfg = self.shop_cfg[grade] or {}

	return cur_grade_cfg
end

function GodPurchaseWGData:GetCurShopIsAllBuy()
	local all_buy = true
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local grade
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then -- 活动开启拿服务端配置  不然根据当前天数拿配置表数据
		grade = self.grade
	else
		grade = self:GetGarde()
	end

	local cur_grade_cfg = self.shop_cfg[grade] or {}
	for i, v in ipairs(cur_grade_cfg) do
		if self.shop_buy_flag[v.seq] and self.shop_buy_flag[v.seq] == 0 and v.buy_type == 1 then
			all_buy = false
			break
		end
	end
	
	return all_buy
end

function GodPurchaseWGData:GetTipShowShopCfg()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg = {}
	for i, v in ipairs(self.open_day_cfg) do
		if server_day >= v.start_day and server_day <= v.end_day then
			cfg = v
		end
	end

	return cfg
end

function GodPurchaseWGData:GetIsShowTip()
	local is_show = false
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_buy = self:GetCurShopIsAllBuy()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    if self.show_tip then
	-- 存在天数配置或者活动开着没买(等级满足) 
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
		if self.day_pro_cfg[server_day] or (activity_info and activity_info.status == ACTIVITY_STATUS.OPEN and (not is_buy)) then
			if role_level >= self.other_cfg.open_level then
				is_show = true
			end
		end
	end

	return is_show
end

function GodPurchaseWGData:SetIsShowTip(state)
	self.show_tip = state
end