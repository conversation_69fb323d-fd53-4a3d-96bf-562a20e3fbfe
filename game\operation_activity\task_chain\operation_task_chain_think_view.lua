OperationTaskChainThinkView = OperationTaskChainThinkView or BaseClass(SafeBaseView)

function OperationTaskChainThinkView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain_think")
end

function OperationTaskChainThinkView:ReleaseCallBack()
	
end

function OperationTaskChainThinkView:LoadCallBack()
	self.node_list.btn_recharge.button:AddClickListener(BindTool.Bind1(self.OnClickGo, self))
	-- self:FlushPictureAndTextContent()
end

function OperationTaskChainThinkView:CloseCallBack()
	-- 清空弹面板标志
	OperationTaskChainWGData.Instance:SetShowThinkData(nil)
end

function OperationTaskChainThinkView:FlushPictureAndTextContent()
	local task_chain_interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if not task_chain_interface_cfg then
		return
	end

	self.node_list.RawImage.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(task_chain_interface_cfg.pic1_1))
	self.node_list.RawImage.raw_image:SetNativeSize()
	self.node_list.title_img.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic1_2))
	self.node_list.title_img.image:SetNativeSize()
	self.node_list.tip_bg.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic1_3))
	self.node_list.btn_recharge.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic1_5))
	self.node_list.btn_recharge.image:SetNativeSize()
	self.node_list.btn_close.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic1_4))
	self.node_list.btn_close.image:SetNativeSize()
end

function OperationTaskChainThinkView:OnClickGo()
	self:Close()
	ViewManager.Instance:Open(GuideModuleName.OperationTaskView)
end

function OperationTaskChainThinkView:ShowIndexCallBack(index)
end

function OperationTaskChainThinkView:OnFlush(param_t, index)
	local value = OperationTaskChainWGData.Instance:GetShowThinkData()
	if value == nil then
		return
	end

	local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
	if day_index_cfg == nil then
		return
	end

	local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
	if task_chain_cfg == nil then
		return
	end

	local interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if not interface_cfg then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(interface_cfg.item_id)
	local item_name = item_cfg and item_cfg.name or ""

	self.node_list.str_tip.text.text = task_chain_cfg.end_content or ""
	self.node_list.str_name.text.text = task_chain_cfg.npc_name or ""
	self.node_list.str_value.text.text = string.format(Language.OpertionAcitvity.TaskChain.GetMingWangStr, item_name, value)

	local is_show = false
	local reward_list = OperationTaskChainWGData.Instance:GetShowMingWangRewardList()
	if reward_list ~= nil then
		for k,v in pairs(reward_list) do
			if v.is_can then
				is_show = true
				break
			end
		end
	end
	
	self.node_list.img_red:SetActive(is_show)
end