require("game/operation_activity/watering_flowers/watering_flower_wg_data")
require("game/operation_activity/watering_flowers/watering_record_view")

WateringFlowersWGCtrl = WateringFlowersWGCtrl or BaseClass(BaseWGCtrl)

QUERY_TYPE = {
	QUERY_TYPE_HELP_INFO = 0, 		-- 请求帮浇水信息
	QUERY_TYPE_INVITE = 1, 			-- 邀请帮助浇水
	QUERY_TYPE_FLOWER_INFO = 2, 	-- 请求盆栽信息
}

function WateringFlowersWGCtrl:__init()
	if WateringFlowersWGCtrl.Instance then
		ErrorLog("[WateringFlowersWGCtrl] Attemp to create a singleton twice !")
	end
	WateringFlowersWGCtrl.Instance = self

	self.watering_flowers_data = WateringFlowersWGData.New()
	self.watering_record_view = WateringRecordView.New()
	
	--上线要自己请求一次好友跟仙盟成员的花盆信息
	self.is_send_friend_list = false

	self:RegisterAllProtocols()

	OperationActivityWGCtrl.Instance:ListenHotUpdate(WateringFlowersWGData.ConfigPath, BindTool.Bind(self.OnHotUpdate, self))

	self:BindGlobalEvent(OtherEventType.PASS_DAY2,BindTool.Bind(self.DoDayPass, self))

end

function WateringFlowersWGCtrl:__delete()
	WateringFlowersWGCtrl.Instance = nil

	if self.watering_flowers_data then
		self.watering_flowers_data:DeleteMe()
		self.watering_flowers_data = nil
	end

	if self.watering_record_view then
		self.watering_record_view:DeleteMe()
		self.watering_record_view = nil
	end
end

function WateringFlowersWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAWaterFlowerInfo,'OnSCOAWaterFlowerInfo')
	self:RegisterProtocol(SCOAWaterFlowerHelpFriendInfo,'OnSCOAWaterFlowerHelpFriendInfo')
	self:RegisterProtocol(SCOAWaterFlowerHelpMeInfo,'OnSCOAWaterFlowerHelpMeInfo')
	self:RegisterProtocol(SCOAWaterFlowerInviteHelpOtherAck,'OnSCOAWaterFlowerInviteHelpOtherAck')
	self:RegisterProtocol(SCOAWaterFlowerWaterLog,'OnSCOAWaterFlowerWaterLog')
	self:RegisterProtocol(SCOAWaterFlowerOtherRoleFlower,'OnSCOAWaterFlowerOtherRoleFlower')
	self:RegisterProtocol(SCOAWaterFlowerSingleRoleInfo,'OnSCOAWaterFlowerSingleRoleInfo')
	self:RegisterProtocol(SCOAWaterFlowerFetchReward, 'OnSCOAWaterFlowerFetchReward')

	self:RegisterProtocol(CSOAWaterFlowerInfo)
	self:RegisterProtocol(CSOAWaterFlowerHelpFriendReqInfo)

end

function WateringFlowersWGCtrl:OpenWateringRecordView()
	if self.watering_record_view and not self.watering_record_view:IsOpen() then
		self.watering_record_view:Open()
	end
end

function WateringFlowersWGCtrl:CloseWateringRecordView()
	if self.watering_record_view and self.watering_record_view:IsOpen() then
		self.watering_record_view:Close()
	end
end

-- 9011 个人信息下发
function WateringFlowersWGCtrl:OnSCOAWaterFlowerInfo(protocol)
	-- print_error("FFFF======= 个人信息下发", protocol)
	self.watering_flowers_data:SetWaterFlowerInfo(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_watering_flowers)
	RemindManager.Instance:Fire(RemindName.OperationWateringFlowers)

	if not self.is_send_friend_list then
		self:SendAllFirendAndMemberFlowerInfo()
		self.is_send_friend_list = true
	end
end

-- 9012 我帮浇过的人
function WateringFlowersWGCtrl:OnSCOAWaterFlowerHelpFriendInfo(protocol)
	-- print_error("FFFF======= 我帮浇过的人", protocol)
	self.watering_flowers_data:SetWaterFlowerHelpFriendInfo(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_watering_flowers)
	RemindManager.Instance:Fire(RemindName.OperationWateringFlowers)
end

-- 9014 下发一些人的帮浇次数
function WateringFlowersWGCtrl:OnSCOAWaterFlowerHelpMeInfo(protocol)
	-- print_error("FFFF======= 下发一些人的帮浇次数", protocol)
	self.watering_flowers_data:SetWaterFlowerHelpMeInfo(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_watering_flowers)
	RemindManager.Instance:Fire(RemindName.OperationWateringFlowers)
end

-- 9015 通知被某人邀请浇水
function WateringFlowersWGCtrl:OnSCOAWaterFlowerInviteHelpOtherAck(protocol)
	-- print_error("FFFF======= 通知被某人邀请浇水", protocol)
	self.watering_flowers_data:SetWaterFlowerInviteHelpOtherAck(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_watering_flowers)
	RemindManager.Instance:Fire(RemindName.OperationWateringFlowers)
end

-- 9016 下发浇水日志
function WateringFlowersWGCtrl:OnSCOAWaterFlowerWaterLog(protocol)
	-- print_error("FFFF======= 下发浇水日志", protocol)

	self.watering_flowers_data:SetWaterFlowerWaterLog(protocol)
	RemindManager.Instance:Fire(RemindName.OperationWateringFlowers)
end

-- 9017 其他玩家盆栽信息
function WateringFlowersWGCtrl:OnSCOAWaterFlowerOtherRoleFlower(protocol)
	-- print_error("FFFF======= 其他玩家盆栽信息", protocol)
	self.watering_flowers_data:SetWaterFlowerOtherRoleFlower(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_watering_flowers)
	RemindManager.Instance:Fire(RemindName.OperationWateringFlowers)
end

-- 9018 个人信息(其他人)下发
function WateringFlowersWGCtrl:OnSCOAWaterFlowerSingleRoleInfo(protocol)
	-- print_error("FFFF======= 个人信息(其他人)下发", protocol)
	self.watering_flowers_data:SetOtherWaterFlowerInfo(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_watering_flowers)
	RemindManager.Instance:Fire(RemindName.OperationWateringFlowers)
end

-- 9010 种花浇水通用请求协议
function WateringFlowersWGCtrl:CSOAWaterFlowerInfo(type, param_0, param_1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOAWaterFlowerInfo)
	protocol.type = type or 0
	protocol.param_0 = param_0 or 0
	protocol.param_1 = param_1 or 0
	protocol:EncodeAndSend()
end

-- 9013 查询这些人的帮浇次数，回复9014
function WateringFlowersWGCtrl:CSOAWaterFlowerHelpFriendReqInfo(type, count, uid_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOAWaterFlowerHelpFriendReqInfo)
	protocol.type = type or 0
	protocol.count = count or 0
	protocol.uid_list = uid_list or {}
	protocol:EncodeAndSend()
end

-- 9019 成熟奖励列表
function WateringFlowersWGCtrl:OnSCOAWaterFlowerFetchReward(protocol)
	-- print_error("FFFF======= 成熟奖励列表", protocol)
	if not IsEmptyTable(protocol.reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, protocol.reward_list, false)
	end
end


--请求所有好友和仙盟成员花盆信息
function WateringFlowersWGCtrl:SendAllFirendAndMemberFlowerInfo()
	local uid_list = {}
	local count = 0
	local friend_list = SocietyWGData.Instance:GetSCFriendList()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	if friend_list and not IsEmptyTable(friend_list) then
		for k,v in pairs(friend_list) do
			if v.user_id > 0 then
				count = count + 1
				table.insert(uid_list, v.user_id)
			end
		end
	end

	local member_list = GuildDataConst.GUILD_MEMBER_LIST

	if member_list and member_list.list and not IsEmptyTable(member_list.list) then
		for k,v in pairs(member_list.list) do

			if not IsEmptyTable(v) and v.uid > 0 and v.uid ~= role_id then
				count = count + 1
				table.insert(uid_list, v.uid)
			end
		end
	end
	self:CSOAWaterFlowerHelpFriendReqInfo(QUERY_TYPE.QUERY_TYPE_FLOWER_INFO, count, uid_list)
end

function WateringFlowersWGCtrl:HideFriendListView()
	local operation_activity_view = OperationActivityWGCtrl.Instance:GetViewInShowIndex(TabIndex.operation_act_watering_flowers)

	if operation_activity_view then
		operation_activity_view:OnClickBackBtn()
	end
end

function WateringFlowersWGCtrl:OnHotUpdate()
	self.watering_flowers_data:FlushWateringFlowersCfg()
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_watering_flowers, "waterflower_hot_update")
end

function WateringFlowersWGCtrl:DoDayPass()
	local status = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS)

	if not status then
		return
	end

	--跳天重新请求种花信息，服务器说准点请求可能数据还没重置完所以延时5秒钟请求
	GlobalTimerQuest:AddDelayTimer(function ()
		self:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_INFO_REQ)
		self:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_OTHER_HELP_ME_INFO_REQ)
	end, 5)
end
