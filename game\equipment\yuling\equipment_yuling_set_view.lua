function EquipmentView:InitImperialSpiritSetView()
	self.iss_select_equip_part = -1
	self.iss_select_equip_data = {}

	if not self.imperial_spirit_set_list then
		self.imperial_spirit_set_list = AsyncListView.New(EquipYuLingItemRender, self.node_list.imperial_spirit_set_list)
		self.imperial_spirit_set_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectImperialSpiritSet<PERSON><PERSON><PERSON>, self))
	end

	if not self.imperial_spirit_eq_item then
		self.imperial_spirit_eq_item = ItemCell.New(self.node_list.imperial_spirit_eq_item) 
	end

	if not self.imperial_spirit_set_item then
		self.imperial_spirit_set_item = ItemCell.New(self.node_list.imperial_spirit_set_item_cost_pos)
	end

	if not self.imperial_spirit_set_attr_list then
		self.imperial_spirit_set_attr_list = AsyncBaseGrid.New()
		local bundle = "uis/view/equipment_ui_prefab"
		local asset = "imperial_spirit_eq_attr_item"
		self.imperial_spirit_set_attr_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list.imperial_spirit_set_attr_list,
		assetBundle = bundle, assetName = asset, itemRender = YuLingEQAttrItem})
		self.imperial_spirit_set_attr_list:SetStartZeroIndex(false)
	end

	if self.imperial_spirit_attr_list == nil then
        self.imperial_spirit_attr_list = {}
        local node_num = self.node_list["imperial_spirit_attr_list"].transform.childCount
        for i = 1, node_num do
            self.imperial_spirit_attr_list[i] = CommonAddAttrRender.New(self.node_list["imperial_spirit_attr_list"]:FindObj("attr_" .. i))
            local cell = CommonAddAttrRender.New(self.node_list["imperial_spirit_attr_list"]:FindObj("attr_" .. i))
		    cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(true)
            self.imperial_spirit_attr_list[i] = cell
        end
    end

	XUI.AddClickEventListener(self.node_list.btn_imperial_spirit_set_upgrade, BindTool.Bind(self.OnClickImpiritSetUpGrade, self))
end

function EquipmentView:DeleteImperialSpiritSetView()
	if self.imperial_spirit_set_list then
		self.imperial_spirit_set_list:DeleteMe()
		self.imperial_spirit_set_list = nil
	end

	if self.imperial_spirit_eq_item then
		self.imperial_spirit_eq_item:DeleteMe()
		self.imperial_spirit_eq_item = nil
	end

	if self.imperial_spirit_set_attr_list then
		self.imperial_spirit_set_attr_list:DeleteMe()
	   self.imperial_spirit_set_attr_list = nil
	end

	if self.imperial_spirit_set_item then
		self.imperial_spirit_set_item:DeleteMe()
		self.imperial_spirit_set_item = nil
	end

	if self.imperial_spirit_attr_list then
        for k, v in pairs(self.imperial_spirit_attr_list) do
            v:DeleteMe()
        end
        self.imperial_spirit_attr_list = nil
    end
end

function EquipmentView:ShowIndexImperialSpiritSetView()
end

function EquipmentView:FlushEquipImperialSpiritSetView(param_t)
	local data_list = EquipmentWGData.Instance:GetEquipImperialSpiritList()
	EquipmentWGData.Instance:GetQingHuaImperialSpiritList()
	local jump_index = EquipmentWGData.Instance:GetEquipYuLingJumpIndex(self.iss_select_equip_part, true)
	self.imperial_spirit_set_list:SetDataList(data_list)
	self.imperial_spirit_set_list:JumpToIndex(jump_index)

	self:FlushImperialSpiritSetMid()
	self:FlushImperialSpiritSetRight()
end

function EquipmentView:OnSelectImperialSpiritSetHandler(item)
	if nil == item or nil == item.data then
		return
	end

	if item.data.index == self.iss_select_equip_part then
		return
	end

	self.iss_select_equip_data = item.data
	self.iss_select_equip_part = item.data.index

	self:FlushImperialSpiritSetMid()
	self:FlushImperialSpiritSetRight()
end

function EquipmentView:FlushImperialSpiritSetMid()
	if IsEmptyTable(self.iss_select_equip_data) then
		return	
	end

	self.imperial_spirit_eq_item:SetData(self.iss_select_equip_data)

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.iss_select_equip_data.item_id)
	local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	local imperial_sprit_part_data = EquipmentWGData.Instance:GetYuLingDataByEquipIndex(self.iss_select_equip_part)
	local equip_cap_Attr_data = EquipmentWGData.Instance:GetEquipCapAndAttrList(self.iss_select_equip_part)
	self.node_list.imperial_spirit_name.text.text = string.format(Language.EquipmentImperialSpirit.Equipment_name, item_name, COLOR3B.GREEN, imperial_sprit_part_data.equip_part_add_per) 
	-- self.node_list.imperial_spirit_eq_cap_value.text.text = imperial_sprit_part_data.capability

	local cap = equip_cap_Attr_data.capability or 0
	self.node_list.imperial_spirit_eq_cap_value.text.text = cap
	self.node_list.equip_yuling_set_capability:SetActive(cap > 0)

	local slider_fill_value = {0.1,0.2,0.3,0.4,0.6,0.7,0.8,0.9,1}
	local equip_part_active_per = #EquipmentWGData.Instance:GetEquipActivePerData(self.iss_select_equip_part)
	local slider_per = equip_part_active_per > 0 and slider_fill_value[equip_part_active_per-1] or 0
	self.node_list.imperial_spirit_eq_slider.image.fillAmount = slider_per

	for i = 0, 9 do
		local data = imperial_sprit_part_data[i]
		local active = data.is_unlock == 1

		if active then
			local asset_name = "a2_fl_bs_" .. imperial_sprit_part_data[i].show_icon
			local bundel, asset = ResPath.GetEquipmentIcon(asset_name)
			self.node_list["imperial_spirit_set_star_icon" .. i].image:LoadSprite(bundel, asset, function()
				self.node_list["imperial_spirit_set_star_icon" .. i].image:SetNativeSize()
			end)
		end

		self.node_list["imperial_spirit_set_" .. i]:SetActive(active)
	end

	-- self.imperial_spirit_set_attr_list:SetDataList(imperial_sprit_part_data.attr_list)
	local attr_list = equip_cap_Attr_data.attr_list or {}
	self.imperial_spirit_set_attr_list:SetDataList(attr_list)
	self.node_list.equip_yuling_attr_bg:SetActive(false)

	local yuling_attr_list, active_num = EquipmentWGData.Instance:ShowEquipSetYuLingAttrList(self.iss_select_equip_part)

	local need_show_up_effect = false

	if nil ~= self.iss_select_equip_part_cache and nil ~= self.iss_equip_active_num_cahe then
		if (self.iss_select_equip_part_cache == self.iss_select_equip_part) and (active_num - self.iss_equip_active_num_cahe == 1) then
			need_show_up_effect = true
		end
	end

	for k, v in pairs(self.imperial_spirit_attr_list) do
		v:SetData(yuling_attr_list[k])

		if need_show_up_effect then
			v:PlayAttrValueUpEffect()
		end
	end

	self.iss_select_equip_part_cache = self.iss_select_equip_part
	self.iss_equip_active_num_cahe = active_num
end

function EquipmentView:FlushImperialSpiritSetRight()
	if IsEmptyTable(self.iss_select_equip_data) then
		return	
	end

	local imperial_sprit_part_data = EquipmentWGData.Instance:GetYuLingDataByEquipIndex(self.iss_select_equip_part)
	local equip_part_active_per = #EquipmentWGData.Instance:GetEquipActivePerData(self.iss_select_equip_part)
	local current_active_num = equip_part_active_per
	local not_active = current_active_num <= 0
	local max_active = current_active_num == 10
	local cur_level_str = ""
	local next_level_str = ""
	local cur_name = ""

	if not_active then
		local add_per_weight = EquipmentWGData.Instance:GetImperialSpiritForgeAddPerWeight(self.iss_select_equip_part, 0)
		cur_level_str = string.format(Language.EquipmentImperialSpirit.NextImperialSpiritLevel, imperial_sprit_part_data[0].name)
		next_level_str = string.format(Language.EquipmentImperialSpirit.NextImperialSpiritDivineSealAdd, add_per_weight)
		cur_name = string.format(Language.EquipmentImperialSpirit.NextImperialSpiritLevel, imperial_sprit_part_data[0].name)
	elseif max_active then
		local add_per_weight = EquipmentWGData.Instance:GetImperialSpiritForgeAddPerWeight(self.iss_select_equip_part, 9)
		cur_level_str = string.format(Language.EquipmentImperialSpirit.CurrentImperialSpiritLevel, imperial_sprit_part_data[current_active_num - 1].name)
		next_level_str = string.format(Language.EquipmentImperialSpirit.CurrentImperialSpiritDivineSealAdd, add_per_weight)
		cur_name = string.format(Language.EquipmentImperialSpirit.NextImperialSpiritLevel, imperial_sprit_part_data[current_active_num - 1].name)
	else
		local add_per_weight = EquipmentWGData.Instance:GetImperialSpiritForgeAddPerWeight(self.iss_select_equip_part, current_active_num - 1)
		cur_level_str = string.format(Language.EquipmentImperialSpirit.CurrentImperialSpiritLevel, imperial_sprit_part_data[current_active_num - 1].name)
		next_level_str = string.format(Language.EquipmentImperialSpirit.NextImperialSpiritDivineSealAdd, add_per_weight)
		cur_name = string.format(Language.EquipmentImperialSpirit.NextImperialSpiritLevel, imperial_sprit_part_data[current_active_num - 1].name)
	end

	if not max_active then
		local cfg = EquipmentWGData.Instance:GetImperialSpiritHoleCfg(self.iss_select_equip_part, current_active_num)
		local cost_item_id = cfg.cost_item_id
		local cost_item_num = cfg.cost_item_num
		local cost_coin =  cfg.cost_coin
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

		self.imperial_spirit_set_item:SetFlushCallBack(function ()
			local enough = item_num >= cost_item_num
			local right_text = ToColorStr(item_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
			self.imperial_spirit_set_item:SetRightBottomColorText(right_text)
			self.imperial_spirit_set_item:SetRightBottomTextVisible(true)
		end)
		self.imperial_spirit_set_item:SetData({item_id = cost_item_id, num = cost_item_num, is_bind = 0})

		local color_str = ""
		local role_bind_gold = RoleWGData.Instance.role_info.bind_gold or 0
		local coin_str_color = cost_coin <= role_bind_gold and COLOR3B.GREEN or COLOR3B.PINK
		local cur_coin_str = ToColorStr(CommonDataManager.ConverExpByThousand(role_bind_gold), coin_str_color)
		color_str = cur_coin_str .. "/" .. CommonDataManager.ConverExpByThousand(cost_coin)
		self.node_list.imperial_spirit_bangyu_desc.text.text = color_str
	end

	self.node_list.imperial_spirit_eq_name.text.text = cur_name
	self.node_list.imperial_spirit_set_item_cost_pos:SetActive(not max_active)
	self.node_list.imperial_spirit_set_max_state:SetActive(max_active)
	self.node_list.btn_imperial_spirit_set_upgrade:SetActive(not max_active)
	self.node_list.imperial_spirit_bangyu:SetActive(not max_active)
end

function EquipmentView:OnClickImpiritSetUpGrade()
	if IsEmptyTable(self.iss_select_equip_data) or nil == self.iss_select_equip_part then
		return	
	end
	
	local imperial_sprit_part_data = EquipmentWGData.Instance:GetYuLingDataByEquipIndex(self.iss_select_equip_part)
	local current_active_num = #EquipmentWGData.Instance:GetEquipActivePerData(self.iss_select_equip_part)
	local max_active = current_active_num == 10

	if not max_active then
		local role_bind_gold = RoleWGData.Instance.role_info.bind_gold or 0
		local gold = RoleWGData.Instance.role_info.gold or 0
		local cfg = EquipmentWGData.Instance:GetImperialSpiritHoleCfg(self.iss_select_equip_part, current_active_num)
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)
		
		local item_enough = item_num >= cfg.cost_item_num
		local bind_gold_enough = role_bind_gold >= cfg.cost_coin
		local all_gold_enough = (gold + role_bind_gold) >= cfg.cost_coin

		local function ok_func()
			EquipmentWGCtrl.Instance:SendEquipYuLingOperate(EQUIP_YULING_OPERATE_TYPE.EQUIP_YULING_OPERATE_TYPE_UNLOCK, self.iss_select_equip_part, current_active_num)
		end

		if item_enough and bind_gold_enough then
			ok_func()
		elseif item_enough and not bind_gold_enough and all_gold_enough then
			TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
		elseif not item_enough then
			TipWGCtrl.Instance:OpenItem({item_id = cfg.cost_item_id})
		elseif not bind_gold_enough and not all_gold_enough then
			TipWGCtrl.Instance:OpenItem({item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD})
		end
	end
end

EquipYuLingItemRender = EquipYuLingItemRender or BaseClass(BaseRender)
function EquipYuLingItemRender:__init()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.imperial_spirit_list_item_item)
		self.item_cell:SetItemTipFrom(ItemTip.FROM_EQUIPMENT_YULING)
		self.item_cell:SetIsShowTips(false)
	end
end

function EquipYuLingItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function EquipYuLingItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item_cell:SetData(self.data)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg then
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		self.node_list.imperial_spirit_list_item_name.text.text = item_name
	end
	self.node_list.imperial_spirit_list_item_remind:SetActive(EquipmentWGData.Instance:GetImperialSpiritSetRemind(self.data.index))

	local imperial_sprit_data = EquipmentWGData.Instance:GetYuLingDataByEquipIndex(self.data.index)
	local num = #EquipmentWGData.Instance:GetEquipActivePerData(self.data.index)
	local show_num = num > 9 and 9 or num

	for i = 0, 9 do
		if i <= show_num then
			local item_data = imperial_sprit_data[i] or {}
			if not IsEmptyTable(item_data) and item_data.is_unlock == 1 then
				local asset_name = "a2_fl_bs_" .. item_data.show_icon
				local bundel, asset = ResPath.GetEquipmentIcon(asset_name)
				self.node_list["equip_imperial_spirit_list_item_icon_" .. i].image:LoadSprite(bundel, asset, function()
					self.node_list["equip_imperial_spirit_list_item_icon_" .. i].image:SetNativeSize()
				end)
	
				self.node_list["equip_imperial_spirit_list_item_suo_" .. i]:SetActive(false)
				self.node_list["equip_imperial_spirit_list_item_icon_" .. i]:SetActive(true)
			else
				self.node_list["equip_imperial_spirit_list_item_icon_" .. i]:SetActive(false)
				self.node_list["equip_imperial_spirit_list_item_suo_" .. i]:SetActive(true)
			end
			self.node_list["equip_yuling_item_" .. i]:SetActive(true)
		else
			self.node_list["equip_yuling_item_" .. i]:SetActive(false)
		end
	end
end


function EquipYuLingItemRender:OnSelectChange(is_select)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list["imperial_spirit_list_item_bg"]:SetActive(not is_select)
	self.node_list["imperial_spirit_list_item_bg_hl"]:SetActive(is_select)
	if item_cfg then
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		self.node_list.hl_imperial_spirit_list_item_name.text.text = item_name
	end
end

YuLingEQAttrItem = YuLingEQAttrItem or BaseClass(BaseGridRender)
function YuLingEQAttrItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.imperial_spirit_eq_attr_item_name.text.text = self.data.attr_name
	self.node_list.imperial_spirit_eq_attr_item_value.text.text = self.data.value_str
end