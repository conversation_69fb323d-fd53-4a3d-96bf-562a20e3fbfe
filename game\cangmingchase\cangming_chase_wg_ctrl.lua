require("game/cangmingchase/cangming_chase_view")
require("game/cangmingchase/cangming_chase_wg_data")

CangMingChaseWGCtrl = CangMingChaseWGCtrl or BaseClass(BaseWGCtrl)

function CangMingChaseWGCtrl:__init()
	if CangMingChaseWGCtrl.Instance then
        error("[CangMingChaseWGCtrl]:Attempt to create singleton twice!")
	end

	CangMingChaseWGCtrl.Instance = self
	self.data = CangMingChaseWGData.New()
	self.cangming_act_view = CangMingChaseView.New(GuideModuleName.CangMingChaseView)
	self:RegisterAllProtocols()

	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
end

function CangMingChaseWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.cangming_act_view:DeleteMe()
	self.cangming_act_view = nil

	if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

	CangMingChaseWGCtrl.Instance = nil
end

function CangMingChaseWGCtrl:CheckNeedCloseAct()
	local is_buy_rmb, is_buy_free = self.data:GetShopIsBuyFlag()
	if is_buy_rmb and is_buy_free then--奖励领取完了,关闭入口
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CANGMING_BUY, ACTIVITY_STATUS.CLOSE, 0, 0)
	end
end

-- 注册协议
function CangMingChaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAWaistLightInfo, "SCOAWaistLightInfo")     --沧溟直购
end

function CangMingChaseWGCtrl:SCOAWaistLightInfo(protocol)
	self.data:SetShopIsBuyFlag(protocol)
	if self.cangming_act_view:IsOpen() then
		self.cangming_act_view:Flush()
	end

	self:CheckNeedCloseAct()
	RemindManager.Instance:Fire(RemindName.CangMingBuyAct)
end

function CangMingChaseWGCtrl:OpenView()
	if not self.cangming_act_view:IsOpen() then
		self.cangming_act_view:Open()
	else
		self.cangming_act_view:Flush()
	end
end

-- 活动改变
function CangMingChaseWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CANGMING_BUY and status == ACTIVITY_STATUS.OPEN then
		self:CheckNeedCloseAct()
    end
end
