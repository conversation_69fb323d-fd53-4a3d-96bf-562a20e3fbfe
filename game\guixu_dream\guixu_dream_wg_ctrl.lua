require("game/guixu_dream/guixu_dream_wg_data")
require("game/guixu_dream/guixu_dream_view")
require("game/guixu_dream/guixu_dream_item")
require("game/guixu_dream/guixu_dream_attr")
-- 【鸿蒙神藏】
GuiXuDreamWGCtrl = GuiXuDreamWGCtrl or BaseClass(BaseWGCtrl)

function GuiXuDreamWGCtrl:__init()
	if nil ~= GuiXuDreamWGCtrl.Instance then
		ErrorLog("[GuiXuDreamWGCtrl] attempt to create singleton twice!")
		return
	end
	GuiXuDreamWGCtrl.Instance = self

    self.data = GuiXuDreamWGData.New()
	self.view = GuiXuDreamView.New(GuideModuleName.GuiXuDreamView)
	self.guixu_dream_attr_view = GuiXuDreamAttrView.New()
end

function GuiXuDreamWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

    self.guixu_dream_attr_view:DeleteMe()
    self.guixu_dream_attr_view = nil

    GuiXuDreamWGCtrl.Instance = nil
end

function GuiXuDreamWGCtrl:OnPartActiveResult(result, suit, part)
	if result == 1 then
		if self.guixu_dream_attr_view:IsOpen() then
			self.guixu_dream_attr_view:DoActiveEffect(suit, part)
		end
	end
end

function GuiXuDreamWGCtrl:OpenGGuiXuDreamAttrView(suit_seq)
	self.guixu_dream_attr_view:SetDataAndOpen(suit_seq)
end

function GuiXuDreamWGCtrl:FlushGuiXuDreamAttrView(suit_seq)
	if self.guixu_dream_attr_view:IsOpen() then
		self.guixu_dream_attr_view:Flush(nil, "all", {suit_seq = suit_seq})
	end
end

function GuiXuDreamWGCtrl:GetGuiXuDreamAttrView()
	return self.guixu_dream_attr_view
end

