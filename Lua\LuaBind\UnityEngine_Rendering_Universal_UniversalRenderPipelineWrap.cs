﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Rendering_Universal_UniversalRenderPipelineWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Rendering.Universal.UniversalRenderPipeline), typeof(UnityEngine.Rendering.RenderPipeline));
		<PERSON><PERSON>Function("RenderSingleCamera", RenderSingleCamera);
		<PERSON><PERSON>Function("IsGameCamera", IsGameCamera);
		<PERSON><PERSON>Function("GetLightAttenuationAndSpotDirection", GetLightAttenuationAndSpotDirection);
		<PERSON><PERSON>Function("InitializeLightConstants_Common", InitializeLightConstants_Common);
		<PERSON><PERSON>ction("New", _CreateUnityEngine_Rendering_Universal_UniversalRenderPipeline);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("k_ShaderTagName", get_k_ShaderTagName, null);
		<PERSON><PERSON>("maxShadowBias", get_maxShadow<PERSON>ias, null);
		<PERSON><PERSON>("minRenderScale", get_minRenderScale, null);
		<PERSON><PERSON>ar("maxRenderScale", get_maxRenderScale, null);
		L.RegVar("maxPerObjectLights", get_maxPerObjectLights, null);
		L.RegVar("maxVisibleAdditionalLights", get_maxVisibleAdditionalLights, null);
		L.RegVar("defaultSettings", get_defaultSettings, null);
		L.RegVar("asset", get_asset, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Rendering_Universal_UniversalRenderPipeline(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset arg0 = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)ToLua.CheckObject<UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset>(L, 1);
				UnityEngine.Rendering.Universal.UniversalRenderPipeline obj = new UnityEngine.Rendering.Universal.UniversalRenderPipeline(arg0);
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Rendering.Universal.UniversalRenderPipeline.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RenderSingleCamera(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.ScriptableRenderContext arg0 = StackTraits<UnityEngine.Rendering.ScriptableRenderContext>.Check(L, 1);
			UnityEngine.Camera arg1 = (UnityEngine.Camera)ToLua.CheckObject(L, 2, typeof(UnityEngine.Camera));
			UnityEngine.Rendering.Universal.UniversalRenderPipeline.RenderSingleCamera(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsGameCamera(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 1, typeof(UnityEngine.Camera));
			bool o = UnityEngine.Rendering.Universal.UniversalRenderPipeline.IsGameCamera(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLightAttenuationAndSpotDirection(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 7);
			UnityEngine.LightType arg0 = (UnityEngine.LightType)ToLua.CheckObject(L, 1, typeof(UnityEngine.LightType));
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Matrix4x4 arg2 = StackTraits<UnityEngine.Matrix4x4>.Check(L, 3);
			float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
			System.Nullable<float> arg4 = ToLua.CheckNullable<float>(L, 5);
			UnityEngine.Vector4 arg5;
			UnityEngine.Vector4 arg6;
			UnityEngine.Rendering.Universal.UniversalRenderPipeline.GetLightAttenuationAndSpotDirection(arg0, arg1, arg2, arg3, arg4, out arg5, out arg6);
			ToLua.Push(L, arg5);
			ToLua.Push(L, arg6);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitializeLightConstants_Common(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 7);
			Unity.Collections.NativeArray<UnityEngine.Rendering.VisibleLight> arg0 = StackTraits<Unity.Collections.NativeArray<UnityEngine.Rendering.VisibleLight>>.Check(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Vector4 arg2;
			UnityEngine.Vector4 arg3;
			UnityEngine.Vector4 arg4;
			UnityEngine.Vector4 arg5;
			UnityEngine.Vector4 arg6;
			UnityEngine.Rendering.Universal.UniversalRenderPipeline.InitializeLightConstants_Common(arg0, arg1, out arg2, out arg3, out arg4, out arg5, out arg6);
			ToLua.Push(L, arg2);
			ToLua.Push(L, arg3);
			ToLua.Push(L, arg4);
			ToLua.Push(L, arg5);
			ToLua.Push(L, arg6);
			return 5;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_k_ShaderTagName(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.Rendering.Universal.UniversalRenderPipeline.k_ShaderTagName);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxShadowBias(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Rendering.Universal.UniversalRenderPipeline.maxShadowBias);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_minRenderScale(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Rendering.Universal.UniversalRenderPipeline.minRenderScale);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxRenderScale(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Rendering.Universal.UniversalRenderPipeline.maxRenderScale);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxPerObjectLights(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.Rendering.Universal.UniversalRenderPipeline.maxPerObjectLights);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxVisibleAdditionalLights(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.Rendering.Universal.UniversalRenderPipeline.maxVisibleAdditionalLights);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultSettings(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipeline obj = (UnityEngine.Rendering.Universal.UniversalRenderPipeline)o;
			UnityEngine.Rendering.RenderPipelineGlobalSettings ret = obj.defaultSettings;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultSettings on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_asset(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UnityEngine.Rendering.Universal.UniversalRenderPipeline.asset);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

