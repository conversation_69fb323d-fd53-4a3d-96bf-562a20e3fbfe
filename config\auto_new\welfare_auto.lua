-- R-日常福利.xls
local item_table={
[1]={item_id=40228,num=1,is_bind=1},
[2]={item_id=26193,num=1,is_bind=1},
[3]={item_id=26194,num=1,is_bind=1},
[4]={item_id=26148,num=30,is_bind=1},
[5]={item_id=22531,num=15,is_bind=1},
[6]={item_id=26346,num=30,is_bind=1},
[7]={item_id=36422,num=19200,is_bind=1},
[8]={item_id=36416,num=2880000,is_bind=1},
[9]={item_id=26148,num=10,is_bind=1},
[10]={item_id=22531,num=5,is_bind=1},
[11]={item_id=26346,num=10,is_bind=1},
[12]={item_id=36422,num=6400,is_bind=1},
[13]={item_id=36416,num=960000,is_bind=1},
[14]={item_id=43944,num=2,is_bind=1},
[15]={item_id=36416,num=1500000,is_bind=1},
[16]={item_id=43944,num=1,is_bind=1},
[17]={item_id=36416,num=500000,is_bind=1},
[18]={item_id=44180,num=1,is_bind=1},
[19]={item_id=26346,num=20,is_bind=1},
[20]={item_id=26349,num=10,is_bind=1},
[21]={item_id=26344,num=20,is_bind=1},
[22]={item_id=44180,num=2,is_bind=1},
[23]={item_id=44180,num=3,is_bind=1},
[24]={item_id=26346,num=6,is_bind=1},
[25]={item_id=26349,num=3,is_bind=1},
[26]={item_id=26344,num=6,is_bind=1},
[27]={item_id=30447,num=1,is_bind=1},
[28]={item_id=22530,num=6,is_bind=1},
[29]={item_id=22530,num=7,is_bind=1},
[30]={item_id=22530,num=8,is_bind=1},
[31]={item_id=22530,num=9,is_bind=1},
[32]={item_id=22530,num=10,is_bind=1},
[33]={item_id=22530,num=11,is_bind=1},
[34]={item_id=22530,num=12,is_bind=1},
[35]={item_id=22530,num=13,is_bind=1},
[36]={item_id=30448,num=1,is_bind=1},
[37]={item_id=22530,num=2,is_bind=1},
[38]={item_id=22530,num=3,is_bind=1},
[39]={item_id=22530,num=4,is_bind=1},
[40]={item_id=22530,num=5,is_bind=1},
[41]={item_id=26344,num=1,is_bind=1},
[42]={item_id=36550,num=400,is_bind=1},
[43]={item_id=22008,num=1,is_bind=1},
[44]={item_id=36550,num=200,is_bind=1},
[45]={item_id=43949,num=1,is_bind=1},
[46]={item_id=26200,num=2,is_bind=1},
[47]={item_id=26415,num=3,is_bind=1},
[48]={item_id=43950,num=1,is_bind=1},
[49]={item_id=26200,num=3,is_bind=1},
[50]={item_id=43951,num=1,is_bind=1},
[51]={item_id=26415,num=4,is_bind=1},
[52]={item_id=43952,num=1,is_bind=1},
[53]={item_id=43953,num=1,is_bind=1},
[54]={item_id=43954,num=1,is_bind=1},
[55]={item_id=26200,num=4,is_bind=1},
[56]={item_id=43955,num=1,is_bind=1},
[57]={item_id=26415,num=5,is_bind=1},
[58]={item_id=43956,num=1,is_bind=1},
[59]={item_id=43957,num=1,is_bind=1},
[60]={item_id=43958,num=1,is_bind=1},
[61]={item_id=43959,num=1,is_bind=1},
[62]={item_id=44488,num=1,is_bind=1},
[63]={item_id=44489,num=1,is_bind=1},
[64]={item_id=44490,num=1,is_bind=1},
[65]={item_id=44491,num=1,is_bind=1},
[66]={item_id=44492,num=1,is_bind=1},
[67]={item_id=43902,num=1,is_bind=1},
[68]={item_id=27852,num=1,is_bind=1},
[69]={item_id=43903,num=1,is_bind=1},
[70]={item_id=43904,num=1,is_bind=1},
[71]={item_id=43905,num=1,is_bind=1},
[72]={item_id=43906,num=1,is_bind=1},
[73]={item_id=43907,num=1,is_bind=1},
[74]={item_id=27811,num=1,is_bind=1},
[75]={item_id=43908,num=1,is_bind=1},
[76]={item_id=43909,num=1,is_bind=1},
[77]={item_id=30447,num=3,is_bind=1},
[78]={item_id=30443,num=5,is_bind=1},
[79]={item_id=22532,num=3,is_bind=1},
[80]={item_id=22531,num=3,is_bind=1},
[81]={item_id=30808,num=1,is_bind=1},
[82]={item_id=30795,num=1,is_bind=1},
[83]={item_id=26393,num=1,is_bind=1},
[84]={item_id=26392,num=3,is_bind=1},
[85]={item_id=26391,num=8,is_bind=1},
[86]={item_id=63006,num=1,is_bind=1},
[87]={item_id=63005,num=2,is_bind=1},
[88]={item_id=26200,num=10,is_bind=1},
[89]={item_id=26415,num=15,is_bind=1},
[90]={item_id=30423,num=5,is_bind=1},
[91]={item_id=43075,num=2,is_bind=1},
[92]={item_id=36537,num=2500,is_bind=1},
[93]={item_id=30423,num=1,is_bind=1},
[94]={item_id=43075,num=1,is_bind=1},
[95]={item_id=36537,num=800,is_bind=1},
[96]={item_id=30447,num=6,is_bind=1},
[97]={item_id=30443,num=4,is_bind=1},
[98]={item_id=26369,num=1,is_bind=1},
[99]={item_id=26380,num=1,is_bind=1},
[100]={item_id=26367,num=1,is_bind=1},
[101]={item_id=36423,num=6400,is_bind=1},
[102]={item_id=36420,num=400,is_bind=1},
[103]={item_id=36416,num=400000,is_bind=1},
[104]={item_id=30443,num=2,is_bind=1},
[105]={item_id=36423,num=3200,is_bind=1},
[106]={item_id=36420,num=200,is_bind=1},
[107]={item_id=36416,num=200000,is_bind=1},
[108]={item_id=39986,num=3,is_bind=1},
[109]={item_id=26129,num=12,is_bind=1},
[110]={item_id=26355,num=1,is_bind=1},
[111]={item_id=26200,num=6,is_bind=1},
[112]={item_id=39986,num=1,is_bind=1},
[113]={item_id=26129,num=6,is_bind=1},
[114]={item_id=26129,num=5,is_bind=1},
[115]={item_id=30447,num=2,is_bind=1},
[116]={item_id=26346,num=3,is_bind=1},
[117]={item_id=36423,num=2100,is_bind=1},
[118]={item_id=26129,num=2,is_bind=1},
[119]={item_id=26346,num=1,is_bind=1},
[120]={item_id=36423,num=1050,is_bind=1},
[121]={item_id=26129,num=4,is_bind=1},
[122]={item_id=36420,num=120,is_bind=1},
[123]={item_id=26203,num=10,is_bind=1},
[124]={item_id=36420,num=60,is_bind=1},
[125]={item_id=26203,num=5,is_bind=1},
[126]={item_id=26200,num=1,is_bind=1},
[127]={item_id=26129,num=10,is_bind=1},
[128]={item_id=39144,num=3500,is_bind=1},
[129]={item_id=26355,num=2,is_bind=1},
[130]={item_id=27830,num=1,is_bind=1},
[131]={item_id=39144,num=1750,is_bind=1},
[132]={item_id=63000,num=2,is_bind=1},
[133]={item_id=26502,num=1,is_bind=1},
[134]={item_id=26517,num=1,is_bind=1},
[135]={item_id=40212,num=2,is_bind=1},
[136]={item_id=26415,num=2,is_bind=1},
[137]={item_id=63000,num=1,is_bind=1},
[138]={item_id=40212,num=1,is_bind=1},
[139]={item_id=26415,num=1,is_bind=1},
[140]={item_id=36420,num=100,is_bind=1},
[141]={item_id=36420,num=50,is_bind=1},
[142]={item_id=30447,num=4,is_bind=1},
[143]={item_id=43090,num=1,is_bind=1},
[144]={item_id=43091,num=1,is_bind=1},
[145]={item_id=36537,num=1000,is_bind=1},
[146]={item_id=22624,num=1,is_bind=1},
[147]={item_id=36537,num=500,is_bind=1},
[148]={item_id=26130,num=5,is_bind=1},
[149]={item_id=26214,num=1,is_bind=1},
[150]={item_id=26367,num=2,is_bind=1},
[151]={item_id=26368,num=4,is_bind=1},
[152]={item_id=30423,num=3,is_bind=1},
[153]={item_id=26344,num=10,is_bind=1},
[154]={item_id=26200,num=20,is_bind=1},
[155]={item_id=26130,num=1,is_bind=1},
[156]={item_id=26368,num=1,is_bind=1},
[157]={item_id=36420,num=30,is_bind=1},
[158]={item_id=26344,num=3,is_bind=1},
[159]={item_id=22877,num=1,is_bind=1},
[160]={item_id=26122,num=1,is_bind=1},
[161]={item_id=26165,num=10,is_bind=1},
[162]={item_id=22611,num=1,is_bind=1},
[163]={item_id=22011,num=1,is_bind=1},
[164]={item_id=22763,num=1,is_bind=1},
[165]={item_id=26344,num=50,is_bind=1},
[166]={item_id=22010,num=1,is_bind=1},
[167]={item_id=22012,num=1,is_bind=1},
[168]={item_id=22767,num=1,is_bind=1},
[169]={item_id=26364,num=1,is_bind=1},
[170]={item_id=26365,num=1,is_bind=1},
[171]={item_id=22575,num=1,is_bind=1},
[172]={item_id=22621,num=1,is_bind=1},
[173]={item_id=26344,num=5,is_bind=1},
[174]={item_id=26346,num=5,is_bind=1},
[175]={item_id=22117,num=1,is_bind=1},
[176]={item_id=26200,num=5,is_bind=1},
[177]={item_id=26345,num=1,is_bind=1},
[178]={item_id=22530,num=1,is_bind=1},
[179]={item_id=30447,num=10,is_bind=1},
[180]={item_id=22531,num=1,is_bind=1},
[181]={item_id=46041,num=1,is_bind=1},
[182]={item_id=26914,num=5,is_bind=1},
[183]={item_id=26349,num=5,is_bind=1},
[184]={item_id=22576,num=1,is_bind=1},
[185]={item_id=26500,num=1,is_bind=1},
[186]={item_id=26515,num=1,is_bind=1},
[187]={item_id=26356,num=1,is_bind=1},
[188]={item_id=26157,num=1,is_bind=1},
[189]={item_id=39785,num=1,is_bind=1},
[190]={item_id=26349,num=1,is_bind=1},
[191]={item_id=30424,num=1,is_bind=1},
[192]={item_id=26165,num=1,is_bind=1},
[193]={item_id=26501,num=1,is_bind=1},
[194]={item_id=26516,num=1,is_bind=1},
[195]={item_id=39788,num=1,is_bind=1},
[196]={item_id=39153,num=1,is_bind=1},
[197]={item_id=26125,num=1,is_bind=1},
[198]={item_id=26148,num=5,is_bind=1},
[199]={item_id=26121,num=1,is_bind=1},
[200]={item_id=26149,num=5,is_bind=1},
[201]={item_id=22532,num=1,is_bind=1},
[202]={item_id=26347,num=1,is_bind=1},
[203]={item_id=26377,num=1,is_bind=1},
[204]={item_id=26376,num=5,is_bind=1},
[205]={item_id=26358,num=1,is_bind=1},
[206]={item_id=30425,num=1,is_bind=1},
[207]={item_id=26359,num=1,is_bind=1},
[208]={item_id=26378,num=1,is_bind=1},
[209]={item_id=26191,num=1,is_bind=1},
[210]={item_id=26350,num=1,is_bind=1},
[211]={item_id=22578,num=1,is_bind=1},
[212]={item_id=26379,num=1,is_bind=1},
[213]={item_id=39935,num=1,is_bind=1},
[214]={item_id=30447,num=20,is_bind=1},
[215]={item_id=40006,num=1,is_bind=1},
[216]={item_id=30447,num=30,is_bind=1},
[217]={item_id=39786,num=1,is_bind=1},
[218]={item_id=26000,num=1,is_bind=1},
[219]={item_id=26165,num=5,is_bind=1},
[220]={item_id=22537,num=1,is_bind=1},
}

return {
offline_exp={
{cost_perhour=1,},
{type=1,},
{type=2,factor=2,},
{type=3,factor=4,cost_perhour=5,}
},

offline_exp_meta_table_map={
},
uplevel_reward={
{},
{seq=1,level=50,},
{seq=2,level=70,},
{seq=3,level=90,},
{seq=4,level=120,},
{seq=5,level=150,},
{seq=6,level=170,},
{seq=7,level=200,limit_or_not=1,limit_count=8,},
{seq=8,level=230,limit_or_not=1,limit_count=6,},
{seq=9,level=250,limit_or_not=1,limit_count=5,},
{seq=10,level=270,limit_or_not=1,limit_count=4,},
{seq=11,level=300,limit_or_not=1,limit_count=3,}
},

uplevel_reward_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
sign_in={
{},
{day=2,},
{day=3,},
{day=4,vip_4_times=3,vip_5_times=3,vip_6_times=3,vip_7_times=3,vip_8_times=3,vip_9_times=3,vip_10_times=3,vip_11_times=3,vip_12_times=3,vip_13_times=3,vip_14_times=3,vip_15_times=3,vip_16_times=3,vip_17_times=3,vip_18_times=3,vip_19_times=3,vip_20_times=3,vip_21_times=3,vip_22_times=3,vip_23_times=3,vip_24_times=3,vip_25_times=3,vip_26_times=3,vip_27_times=3,vip_28_times=3,vip_29_times=3,vip_30_times=3,},
{day=5,},
{day=6,},
{day=7,reward_item=item_table[1],},
{day=8,},
{day=9,},
{day=10,},
{day=11,},
{day=12,},
{day=13,},
{day=14,reward_item=item_table[1],},
{day=15,},
{day=16,},
{day=17,},
{day=18,},
{day=19,},
{day=20,},
{day=21,},
{day=22,vip_7_times=6,vip_8_times=6,vip_9_times=6,vip_10_times=6,vip_11_times=6,vip_12_times=6,vip_13_times=6,vip_14_times=6,vip_15_times=6,vip_16_times=6,vip_17_times=6,vip_18_times=6,vip_19_times=6,vip_20_times=6,vip_21_times=6,vip_22_times=6,vip_23_times=6,vip_24_times=6,vip_25_times=6,vip_26_times=6,vip_27_times=6,vip_28_times=6,vip_29_times=6,vip_30_times=6,},
{day=23,vip_6_times=4,vip_7_times=4,vip_8_times=4,vip_9_times=4,vip_10_times=4,vip_11_times=4,vip_12_times=4,vip_13_times=4,vip_14_times=4,vip_15_times=4,vip_16_times=4,vip_17_times=4,vip_18_times=4,vip_19_times=4,vip_20_times=4,vip_21_times=4,vip_22_times=4,vip_23_times=4,vip_24_times=4,vip_25_times=4,vip_26_times=4,vip_27_times=4,vip_28_times=4,vip_29_times=4,vip_30_times=4,},
{day=24,},
{day=25,},
{day=26,},
{day=27,},
{day=28,reward_item=item_table[1],vip_8_times=8,vip_9_times=8,vip_10_times=8,vip_11_times=8,vip_12_times=8,vip_13_times=8,vip_14_times=8,vip_15_times=8,vip_16_times=8,vip_17_times=8,vip_18_times=8,vip_19_times=8,vip_20_times=8,vip_21_times=8,vip_22_times=8,vip_23_times=8,vip_24_times=8,vip_25_times=8,vip_26_times=8,vip_27_times=8,vip_28_times=8,vip_29_times=8,vip_30_times=8,},
{indexes=2,},
{day=2,},
{day=3,},
{indexes=2,vip_6_times=3,},
{day=5,},
{day=6,},
{indexes=2,},
{indexes=2,vip_8_times=6,},
{indexes=2,},
{day=10,},
{indexes=2,},
{day=12,},
{day=13,},
{indexes=2,vip_7_times=4,},
{day=15,},
{day=16,},
{indexes=2,vip_7_times=4,},
{day=18,},
{day=19,},
{day=20,},
{indexes=2,},
{indexes=2,},
{day=23,},
{day=24,},
{indexes=2,},
{day=26,},
{indexes=2,},
{indexes=2,},
{indexes=3,},
{day=2,},
{day=3,},
{indexes=3,vip_7_times=3,},
{day=5,},
{day=6,},
{indexes=3,},
{day=8,},
{day=9,},
{day=10,},
{day=11,},
{day=12,},
{day=13,},
{indexes=3,vip_8_times=4,},
{day=15,},
{day=16,},
{day=17,},
{day=18,},
{indexes=3,},
{day=20,},
{day=21,},
{indexes=3,vip_9_times=6,},
{indexes=3,day=23,},
{day=24,},
{indexes=3,},
{day=26,vip_7_times=4,},
{day=27,},
{indexes=3,vip_10_times=8,},
{indexes=4,},
{day=2,},
{day=3,},
{indexes=4,vip_8_times=3,},
{day=5,},
{day=6,},
{indexes=4,},
{day=8,},
{day=9,},
{day=10,},
{day=11,},
{day=12,},
{indexes=4,},
{indexes=4,vip_9_times=4,},
{day=15,},
{day=16,},
{day=17,},
{day=18,},
{day=19,},
{indexes=4,vip_8_times=4,},
{indexes=4,},
{indexes=4,vip_10_times=6,},
{indexes=4,vip_9_times=4,},
{day=24,},
{day=25,},
{day=26,},
{indexes=4,},
{indexes=4,vip_11_times=8,},
{indexes=5,},
{day=2,},
{day=3,},
{indexes=5,day=4,vip_9_times=3,vip_10_times=3,vip_11_times=3,vip_12_times=3,vip_13_times=3,vip_14_times=3,vip_15_times=3,vip_16_times=3,vip_17_times=3,vip_18_times=3,vip_19_times=3,vip_20_times=3,vip_21_times=3,vip_22_times=3,vip_23_times=3,vip_24_times=3,vip_25_times=3,vip_26_times=3,vip_27_times=3,vip_28_times=3,vip_29_times=3,vip_30_times=3,},
{indexes=5,day=5,vip_10_times=4,vip_11_times=4,vip_12_times=4,vip_13_times=4,vip_14_times=4,vip_15_times=4,vip_16_times=4,vip_17_times=4,vip_18_times=4,vip_19_times=4,vip_20_times=4,vip_21_times=4,vip_22_times=4,vip_23_times=4,vip_24_times=4,vip_25_times=4,vip_26_times=4,vip_27_times=4,vip_28_times=4,vip_29_times=4,vip_30_times=4,},
{day=6,},
{day=7,},
{indexes=5,day=8,vip_11_times=6,vip_12_times=6,vip_13_times=6,vip_14_times=6,vip_15_times=6,vip_16_times=6,vip_17_times=6,vip_18_times=6,vip_19_times=6,vip_20_times=6,vip_21_times=6,vip_22_times=6,vip_23_times=6,vip_24_times=6,vip_25_times=6,vip_26_times=6,vip_27_times=6,vip_28_times=6,vip_29_times=6,vip_30_times=6,},
{indexes=5,},
{day=10,},
{day=11,vip_9_times=4,},
{day=12,},
{day=13,},
{day=14,reward_item=item_table[1],},
{day=15,},
{day=16,},
{day=17,},
{day=18,},
{day=19,},
{day=20,},
{indexes=5,},
{day=22,},
{day=23,},
{day=24,},
{indexes=5,},
{day=26,},
{day=27,},
{indexes=5,day=28,reward_item=item_table[1],vip_12_times=8,vip_13_times=8,vip_14_times=8,vip_15_times=8,vip_16_times=8,vip_17_times=8,vip_18_times=8,vip_19_times=8,vip_20_times=8,vip_21_times=8,vip_22_times=8,vip_23_times=8,vip_24_times=8,vip_25_times=8,vip_26_times=8,vip_27_times=8,vip_28_times=8,vip_29_times=8,vip_30_times=8,},
{indexes=6,vip_2_times=2,vip_3_times=2,vip_4_times=2,vip_5_times=2,vip_6_times=2,vip_7_times=2,vip_8_times=2,vip_9_times=2,vip_10_times=2,vip_11_times=2,vip_12_times=2,vip_13_times=2,vip_14_times=2,vip_15_times=2,vip_16_times=2,vip_17_times=2,vip_18_times=2,vip_19_times=2,vip_20_times=2,vip_21_times=2,vip_22_times=2,vip_23_times=2,vip_24_times=2,vip_25_times=2,vip_26_times=2,vip_27_times=2,vip_28_times=2,vip_29_times=2,vip_30_times=2,},
{day=2,},
{day=3,vip_3_times=2,},
{day=4,},
{day=5,},
{day=6,},
{day=7,},
{day=8,},
{day=9,},
{day=10,},
{day=11,},
{day=12,},
{indexes=6,day=13,vip_5_times=2,vip_6_times=2,vip_7_times=2,vip_8_times=2,vip_9_times=2,vip_10_times=2,vip_11_times=2,vip_12_times=2,vip_13_times=2,vip_14_times=2,vip_15_times=2,vip_16_times=2,vip_17_times=2,vip_18_times=2,vip_19_times=2,vip_20_times=2,vip_21_times=2,vip_22_times=2,vip_23_times=2,vip_24_times=2,vip_25_times=2,vip_26_times=2,vip_27_times=2,vip_28_times=2,vip_29_times=2,vip_30_times=2,},
{day=14,},
{day=15,},
{day=16,},
{day=17,},
{indexes=6,},
{indexes=6,},
{day=20,vip_4_times=2,},
{day=21,vip_5_times=2,},
{day=22,},
{indexes=6,},
{day=24,},
{day=25,},
{day=26,},
{day=27,},
{indexes=6,day=28,reward_item=item_table[1],vip_6_times=2,vip_7_times=2,vip_8_times=2,vip_9_times=2,vip_10_times=2,vip_11_times=2,vip_12_times=2,vip_13_times=2,vip_14_times=2,vip_15_times=2,vip_16_times=2,vip_17_times=2,vip_18_times=2,vip_19_times=2,vip_20_times=2,vip_21_times=2,vip_22_times=2,vip_23_times=2,vip_24_times=2,vip_25_times=2,vip_26_times=2,vip_27_times=2,vip_28_times=2,vip_29_times=2,vip_30_times=2,},
{indexes=7,vip_1_times=2,},
{day=2,},
{day=3,},
{day=4,},
{day=5,},
{day=6,},
{day=7,},
{day=8,},
{day=9,},
{day=10,},
{day=11,},
{indexes=7,},
{indexes=7,vip_4_times=2,},
{indexes=7,vip_5_times=2,},
{day=15,},
{day=16,},
{day=17,},
{day=18,},
{day=19,},
{day=20,},
{indexes=7,vip_4_times=2,},
{indexes=7,day=22,},
{day=23,},
{day=24,vip_3_times=2,},
{day=25,},
{day=26,},
{day=27,},
{day=28,}
},

sign_in_meta_table_map={
[81]=25,	-- depth:1
[80]=81,	-- depth:2
[130]=113,	-- depth:1
[131]=130,	-- depth:2
[75]=131,	-- depth:3
[74]=75,	-- depth:4
[71]=74,	-- depth:5
[68]=71,	-- depth:6
[83]=68,	-- depth:7
[136]=131,	-- depth:3
[65]=83,	-- depth:8
[62]=65,	-- depth:9
[121]=65,	-- depth:9
[59]=62,	-- depth:10
[58]=59,	-- depth:11
[69]=58,	-- depth:12
[111]=83,	-- depth:8
[86]=111,	-- depth:9
[87]=86,	-- depth:10
[108]=87,	-- depth:11
[124]=121,	-- depth:10
[103]=108,	-- depth:12
[114]=124,	-- depth:11
[102]=103,	-- depth:13
[99]=102,	-- depth:14
[125]=114,	-- depth:12
[97]=125,	-- depth:13
[115]=125,	-- depth:13
[96]=97,	-- depth:14
[139]=115,	-- depth:14
[127]=139,	-- depth:15
[118]=127,	-- depth:16
[93]=96,	-- depth:15
[90]=93,	-- depth:16
[109]=90,	-- depth:17
[55]=139,	-- depth:15
[137]=109,	-- depth:18
[53]=137,	-- depth:19
[180]=96,	-- depth:15
[159]=103,	-- depth:13
[191]=180,	-- depth:16
[34]=53,	-- depth:20
[163]=191,	-- depth:17
[165]=163,	-- depth:18
[31]=34,	-- depth:21
[187]=191,	-- depth:17
[30]=31,	-- depth:22
[166]=165,	-- depth:19
[186]=187,	-- depth:18
[170]=186,	-- depth:19
[172]=170,	-- depth:20
[173]=172,	-- depth:21
[21]=7,	-- depth:1
[184]=173,	-- depth:22
[177]=184,	-- depth:23
[179]=177,	-- depth:24
[158]=186,	-- depth:19
[193]=179,	-- depth:25
[37]=177,	-- depth:24
[156]=158,	-- depth:20
[40]=37,	-- depth:25
[41]=40,	-- depth:26
[52]=41,	-- depth:27
[142]=156,	-- depth:21
[144]=142,	-- depth:22
[152]=144,	-- depth:23
[43]=52,	-- depth:28
[151]=152,	-- depth:24
[145]=151,	-- depth:25
[47]=43,	-- depth:29
[194]=193,	-- depth:26
[149]=145,	-- depth:26
[46]=47,	-- depth:30
[49]=21,	-- depth:2
[63]=7,	-- depth:1
[91]=63,	-- depth:2
[77]=63,	-- depth:2
[35]=91,	-- depth:3
[133]=77,	-- depth:3
[119]=133,	-- depth:4
[105]=133,	-- depth:4
[134]=120,	-- depth:1
[112]=140,	-- depth:1
[106]=134,	-- depth:2
[128]=117,	-- depth:1
[92]=106,	-- depth:3
[129]=128,	-- depth:2
[135]=129,	-- depth:3
[107]=135,	-- depth:4
[100]=107,	-- depth:5
[126]=117,	-- depth:1
[123]=117,	-- depth:1
[89]=100,	-- depth:6
[84]=112,	-- depth:2
[78]=106,	-- depth:3
[132]=123,	-- depth:2
[64]=78,	-- depth:4
[138]=132,	-- depth:3
[101]=89,	-- depth:7
[122]=138,	-- depth:4
[98]=126,	-- depth:2
[104]=132,	-- depth:3
[95]=104,	-- depth:4
[94]=95,	-- depth:5
[88]=116,	-- depth:1
[79]=104,	-- depth:4
[73]=79,	-- depth:5
[72]=73,	-- depth:6
[8]=22,	-- depth:1
[110]=94,	-- depth:6
[61]=72,	-- depth:7
[36]=64,	-- depth:5
[45]=73,	-- depth:6
[44]=45,	-- depth:7
[20]=23,	-- depth:1
[17]=20,	-- depth:2
[33]=44,	-- depth:8
[16]=17,	-- depth:3
[10]=16,	-- depth:4
[11]=10,	-- depth:5
[50]=22,	-- depth:1
[82]=79,	-- depth:5
[76]=82,	-- depth:6
[70]=98,	-- depth:3
[67]=76,	-- depth:7
[66]=67,	-- depth:8
[60]=88,	-- depth:2
[56]=28,	-- depth:1
[26]=11,	-- depth:6
[5]=26,	-- depth:7
[51]=33,	-- depth:9
[39]=11,	-- depth:6
[48]=39,	-- depth:7
[42]=70,	-- depth:4
[54]=48,	-- depth:8
[14]=23,	-- depth:1
[32]=60,	-- depth:3
[38]=54,	-- depth:9
[167]=153,	-- depth:1
[154]=168,	-- depth:1
[181]=153,	-- depth:1
[182]=154,	-- depth:2
[160]=153,	-- depth:1
[196]=182,	-- depth:3
[164]=160,	-- depth:2
[195]=181,	-- depth:2
[161]=168,	-- depth:1
[146]=164,	-- depth:3
[150]=146,	-- depth:4
[147]=161,	-- depth:2
[192]=181,	-- depth:2
[143]=160,	-- depth:2
[189]=161,	-- depth:2
[188]=192,	-- depth:3
[185]=188,	-- depth:4
[148]=143,	-- depth:3
[157]=148,	-- depth:4
[178]=185,	-- depth:5
[162]=157,	-- depth:5
[175]=189,	-- depth:3
[174]=178,	-- depth:6
[171]=174,	-- depth:7
[155]=141,	-- depth:1
[190]=141,	-- depth:1
[176]=190,	-- depth:2
[169]=141,	-- depth:1
[183]=169,	-- depth:2
},
sign_indexes={
{},
{indexes=2,grade_rise=350,grade_end=449,},
{indexes=3,grade_rise=450,grade_end=2000,}
},

sign_indexes_meta_table_map={
},
sign_heap_in={
{},
{heap_times=7,reward_type=1,reward_item=item_table[2],sign_word="周周有你",},
{heap_times=14,reward_type=2,sign_word="勤勤恳恳",},
{heap_times=21,reward_type=3,sign_word="再接再厉",},
{heap_times=28,reward_type=4,reward_item=item_table[3],sign_word="风雨无阻",},
{indexes=2,},
{indexes=2,},
{indexes=2,},
{indexes=2,},
{indexes=2,},
{indexes=3,},
{indexes=3,},
{indexes=3,},
{indexes=3,},
{indexes=3,},
{indexes=4,},
{indexes=4,},
{indexes=4,},
{indexes=4,},
{indexes=4,},
{indexes=5,},
{indexes=5,},
{indexes=5,},
{indexes=5,},
{indexes=5,},
{indexes=6,},
{indexes=6,},
{indexes=6,},
{indexes=6,},
{indexes=6,},
{indexes=7,},
{indexes=7,},
{indexes=7,},
{indexes=7,},
{indexes=7,}
},

sign_heap_in_meta_table_map={
[33]=3,	-- depth:1
[29]=4,	-- depth:1
[28]=33,	-- depth:2
[24]=29,	-- depth:2
[23]=28,	-- depth:3
[19]=24,	-- depth:3
[34]=19,	-- depth:4
[18]=23,	-- depth:4
[9]=34,	-- depth:5
[14]=9,	-- depth:6
[13]=18,	-- depth:5
[8]=13,	-- depth:6
[32]=2,	-- depth:1
[30]=5,	-- depth:1
[7]=32,	-- depth:2
[15]=30,	-- depth:2
[17]=7,	-- depth:3
[25]=15,	-- depth:3
[10]=25,	-- depth:4
[12]=17,	-- depth:4
[22]=12,	-- depth:5
[20]=10,	-- depth:5
[27]=22,	-- depth:6
[35]=20,	-- depth:6
},
activity_find={
{activity_type=27,},
{name="沧海夺锋",find_type=4,activity_type=3074,module_name="act_jjc#arena_kf1v1",},
{name="永夜幻都",find_type=7,activity_type=3073,module_name="kuafuhonorhalls",},
{name="逐鹿仙缘",find_type=8,activity_type=3075,module_name="kuafupvp",},
{name="诛仙战场",find_type=9,activity_type=3102,module_name="activity_hall",},
{name="天玑迷城",find_type=11,activity_type=3087,module_name="kuafuYeZhanWangCheng",},
{name="大闹天宫",find_type=16,activity_type=3120,module_name="ConquestWarView#conquest_war_kfkz",},
{name="冠绝征战",find_type=17,activity_type=3112,module_name="ConquestWarView#country_map_flag_grabbing_battlefield",},
{name="巨兽讨伐",find_type=18,activity_type=37,},
{name="神龙来袭",find_type=19,activity_type=3123,module_name="ConquestWarView#boss_invasion",},
{name="日常悬赏",daily_find=1,find_type=0,module_name="daily_task",},
{name="主城护送",daily_find=1,find_type=1,module_name="husong_task",},
{name="熔火之心",find_type=2,vip_type=5,module_name="fubenpanel_copper",},
{name="暗翼之巢",daily_find=1,vip_type=15,module_name="",},
{name="天梯争霸",find_type=6,vip_type=6,module_name="act_jjc",},
{name="天峰夺宝",find_type=7,vip_type=17,module_name="fubenpanel_equip_high",},
{name="日月修行",find_type=4,vip_type=12,module_name="fubenpanel_exp",},
{name="神灵仙岛",find_type=13,vip_type=27,module_name="baguamizhenview",},
{name="昆仑玉虚",daily_find=1,find_type=16,module_name="fubenpanel_control_beasts",},
{name="神女无梦",daily_find=1,find_type=17,module_name="fubenpanel_beauty",}
},

activity_find_meta_table_map={
[13]=20,	-- depth:1
[15]=20,	-- depth:1
[16]=20,	-- depth:1
[17]=20,	-- depth:1
[18]=20,	-- depth:1
},
activity_find_reward={
{find_type=0,exp_factor=40,openserver_day_1_reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6],[3]=item_table[7],[4]=item_table[8]},openserver_day_2_reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6],[3]=item_table[7],[4]=item_table[8]},},
{find_type=0,exp_factor=13,openserver_day_1_reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12],[4]=item_table[13]},openserver_day_2_reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12],[4]=item_table[13]},},
{find_type=1,level=1,cost=50,vip_extra_cost=25,exp_factor=50,openserver_day_1_reward_item={[0]=item_table[14],[1]=item_table[15]},openserver_day_2_reward_item={[0]=item_table[14],[1]=item_table[15]},},
{find_type=1,exp_factor=17,openserver_day_1_reward_item={[0]=item_table[16],[1]=item_table[17]},openserver_day_2_reward_item={[0]=item_table[16],[1]=item_table[17]},},
{level=1,max_level=100,openserver_day_1_reward_item={[0]=item_table[18],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21]},openserver_day_2_reward_item={[0]=item_table[18],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21]},},
{level=101,max_level=200,},
{level=201,max_level=300,},
{level=301,max_level=429,openserver_day_1_reward_item={[0]=item_table[22],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21]},openserver_day_2_reward_item={[0]=item_table[22],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21]},},
{level=430,max_level=579,},
{level=580,max_level=679,},
{find_type=2,level=680,cost=50,vip_extra_cost=25,exp_factor=15,openserver_day_1_reward_item={[0]=item_table[23],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21]},openserver_day_2_reward_item={[0]=item_table[23],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21]},},
{level=1,max_level=100,openserver_day_1_reward_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[26]},openserver_day_2_reward_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[26]},},
{level=101,max_level=200,},
{level=201,max_level=300,},
{level=301,max_level=429,},
{level=430,max_level=579,},
{level=580,max_level=679,},
{find_type=2,get_type=0,level=680,exp_factor=5,},
{find_type=3,max_level=219,openserver_day_1_reward_item={[0]=item_table[27],[1]=item_table[28]},openserver_day_2_reward_item={[0]=item_table[27],[1]=item_table[28]},},
{level=220,max_level=269,openserver_day_1_reward_item={[0]=item_table[27],[1]=item_table[29]},openserver_day_2_reward_item={[0]=item_table[27],[1]=item_table[29]},},
{level=270,max_level=309,openserver_day_1_reward_item={[0]=item_table[27],[1]=item_table[30]},openserver_day_2_reward_item={[0]=item_table[27],[1]=item_table[30]},},
{level=310,max_level=370,openserver_day_1_reward_item={[0]=item_table[27],[1]=item_table[31]},openserver_day_2_reward_item={[0]=item_table[27],[1]=item_table[31]},},
{level=371,max_level=449,},
{level=450,max_level=549,openserver_day_1_reward_item={[0]=item_table[27],[1]=item_table[32]},openserver_day_2_reward_item={[0]=item_table[27],[1]=item_table[32]},},
{level=550,max_level=649,openserver_day_1_reward_item={[0]=item_table[27],[1]=item_table[33]},openserver_day_2_reward_item={[0]=item_table[27],[1]=item_table[33]},},
{level=650,max_level=749,},
{level=750,max_level=849,openserver_day_1_reward_item={[0]=item_table[27],[1]=item_table[34]},openserver_day_2_reward_item={[0]=item_table[27],[1]=item_table[34]},},
{find_type=3,level=850,openserver_day_1_reward_item={[0]=item_table[27],[1]=item_table[35]},openserver_day_2_reward_item={[0]=item_table[27],[1]=item_table[35]},},
{level=100,max_level=219,},
{level=220,max_level=269,openserver_day_1_reward_item={[0]=item_table[36],[1]=item_table[37]},openserver_day_2_reward_item={[0]=item_table[36],[1]=item_table[37]},},
{level=270,max_level=309,openserver_day_1_reward_item={[0]=item_table[36],[1]=item_table[38]},openserver_day_2_reward_item={[0]=item_table[36],[1]=item_table[38]},},
{level=310,max_level=370,},
{level=371,max_level=449,},
{level=450,max_level=549,},
{level=550,max_level=649,openserver_day_1_reward_item={[0]=item_table[36],[1]=item_table[39]},openserver_day_2_reward_item={[0]=item_table[36],[1]=item_table[39]},},
{level=650,max_level=749,},
{level=750,max_level=849,},
{find_type=3,level=850,openserver_day_1_reward_item={[0]=item_table[36],[1]=item_table[40]},openserver_day_2_reward_item={[0]=item_table[36],[1]=item_table[40]},},
{find_type=4,level=80,openserver_day_1_reward_item={},openserver_day_2_reward_item={},},
{find_type=4,level=80,openserver_day_1_reward_item={},openserver_day_2_reward_item={},},
{find_type=6,level=100,openserver_day_1_reward_item={[0]=item_table[41],[1]=item_table[42],[2]=item_table[43]},openserver_day_2_reward_item={[0]=item_table[41],[1]=item_table[42],[2]=item_table[43]},},
{find_type=6,get_type=0,level=100,exp_factor=7,openserver_day_1_reward_item={[0]=item_table[41],[1]=item_table[44],[2]=item_table[43]},openserver_day_2_reward_item={[0]=item_table[41],[1]=item_table[44],[2]=item_table[43]},},
{level=1,max_level=199,openserver_day_1_reward_item={[0]=item_table[45],[1]=item_table[46],[2]=item_table[47]},openserver_day_2_reward_item={[0]=item_table[45],[1]=item_table[46],[2]=item_table[47]},},
{level=200,max_level=249,openserver_day_1_reward_item={[0]=item_table[46],[1]=item_table[48],[2]=item_table[47]},openserver_day_2_reward_item={[0]=item_table[46],[1]=item_table[48],[2]=item_table[47]},},
{level=250,max_level=299,openserver_day_1_reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[51]},openserver_day_2_reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[51]},},
{level=300,max_level=349,openserver_day_1_reward_item={[0]=item_table[49],[1]=item_table[52],[2]=item_table[51]},openserver_day_2_reward_item={[0]=item_table[49],[1]=item_table[52],[2]=item_table[51]},},
{level=350,max_level=419,openserver_day_1_reward_item={[0]=item_table[49],[1]=item_table[53],[2]=item_table[51]},openserver_day_2_reward_item={[0]=item_table[49],[1]=item_table[53],[2]=item_table[51]},},
{level=420,max_level=519,openserver_day_1_reward_item={[0]=item_table[49],[1]=item_table[54],[2]=item_table[51]},openserver_day_2_reward_item={[0]=item_table[49],[1]=item_table[54],[2]=item_table[51]},},
{level=520,max_level=619,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[56],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[56],[2]=item_table[57]},},
{level=620,max_level=719,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[58],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[58],[2]=item_table[57]},},
{level=720,max_level=819,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[59],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[59],[2]=item_table[57]},},
{level=820,max_level=919,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[60],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[60],[2]=item_table[57]},},
{level=920,max_level=1019,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[61],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[61],[2]=item_table[57]},},
{level=1020,max_level=1119,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[62],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[62],[2]=item_table[57]},},
{level=1120,max_level=1219,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[63],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[63],[2]=item_table[57]},},
{level=1220,max_level=1319,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[64],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[64],[2]=item_table[57]},},
{level=1320,max_level=1419,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[65],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[65],[2]=item_table[57]},},
{level=1420,cost=50,vip_extra_cost=25,exp_factor=60,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[66],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[66],[2]=item_table[57]},},
{level=1,max_level=199,openserver_day_1_reward_item={[0]=item_table[45],[1]=item_table[46],[2]=item_table[47]},openserver_day_2_reward_item={[0]=item_table[45],[1]=item_table[46],[2]=item_table[47]},},
{level=200,max_level=249,openserver_day_1_reward_item={[0]=item_table[46],[1]=item_table[48],[2]=item_table[47]},openserver_day_2_reward_item={[0]=item_table[46],[1]=item_table[48],[2]=item_table[47]},},
{level=250,max_level=299,openserver_day_1_reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[51]},openserver_day_2_reward_item={[0]=item_table[49],[1]=item_table[50],[2]=item_table[51]},},
{level=300,max_level=349,openserver_day_1_reward_item={[0]=item_table[49],[1]=item_table[52],[2]=item_table[51]},openserver_day_2_reward_item={[0]=item_table[49],[1]=item_table[52],[2]=item_table[51]},},
{level=350,max_level=419,openserver_day_1_reward_item={[0]=item_table[49],[1]=item_table[53],[2]=item_table[51]},openserver_day_2_reward_item={[0]=item_table[49],[1]=item_table[53],[2]=item_table[51]},},
{level=420,max_level=519,openserver_day_1_reward_item={[0]=item_table[49],[1]=item_table[54],[2]=item_table[51]},openserver_day_2_reward_item={[0]=item_table[49],[1]=item_table[54],[2]=item_table[51]},},
{level=520,max_level=619,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[56],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[56],[2]=item_table[57]},},
{level=620,max_level=719,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[58],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[58],[2]=item_table[57]},},
{level=720,max_level=819,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[59],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[59],[2]=item_table[57]},},
{level=820,max_level=919,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[60],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[60],[2]=item_table[57]},},
{level=920,max_level=1019,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[61],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[61],[2]=item_table[57]},},
{level=1020,max_level=1119,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[62],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[62],[2]=item_table[57]},},
{level=1120,max_level=1219,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[63],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[63],[2]=item_table[57]},},
{level=1220,max_level=1319,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[64],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[64],[2]=item_table[57]},},
{level=1320,max_level=1419,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[65],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[65],[2]=item_table[57]},},
{get_type=0,level=1420,openserver_day_1_reward_item={[0]=item_table[55],[1]=item_table[66],[2]=item_table[57]},openserver_day_2_reward_item={[0]=item_table[55],[1]=item_table[66],[2]=item_table[57]},},
{level=1,max_level=291,openserver_day_1_reward_item={[0]=item_table[67],[1]=item_table[68]},openserver_day_2_reward_item={[0]=item_table[67],[1]=item_table[68]},},
{level=292,max_level=339,openserver_day_1_reward_item={[0]=item_table[69],[1]=item_table[68]},openserver_day_2_reward_item={[0]=item_table[69],[1]=item_table[68]},},
{level=340,max_level=399,openserver_day_1_reward_item={[0]=item_table[70],[1]=item_table[68]},openserver_day_2_reward_item={[0]=item_table[70],[1]=item_table[68]},},
{level=400,max_level=459,openserver_day_1_reward_item={[0]=item_table[71],[1]=item_table[68]},openserver_day_2_reward_item={[0]=item_table[71],[1]=item_table[68]},},
{level=460,max_level=517,openserver_day_2_reward_item={[0]=item_table[72],[1]=item_table[68]},},
{level=518,max_level=650,openserver_day_1_reward_item={[0]=item_table[73],[1]=item_table[74]},openserver_day_2_reward_item={[0]=item_table[73],[1]=item_table[74]},},
{level=651,max_level=713,openserver_day_1_reward_item={[0]=item_table[75],[1]=item_table[74]},openserver_day_2_reward_item={[0]=item_table[75],[1]=item_table[74]},},
{find_type=13,level=714,cost=50,vip_extra_cost=25,openserver_day_1_reward_item={[0]=item_table[76],[1]=item_table[74]},openserver_day_2_reward_item={[0]=item_table[76],[1]=item_table[74]},},
{level=1,max_level=291,openserver_day_1_reward_item={[0]=item_table[67],[1]=item_table[68]},openserver_day_2_reward_item={[0]=item_table[67],[1]=item_table[68]},},
{level=292,max_level=339,openserver_day_1_reward_item={[0]=item_table[69],[1]=item_table[68]},openserver_day_2_reward_item={[0]=item_table[69],[1]=item_table[68]},},
{level=340,max_level=399,openserver_day_1_reward_item={[0]=item_table[70],[1]=item_table[68]},openserver_day_2_reward_item={[0]=item_table[70],[1]=item_table[68]},},
{level=400,max_level=459,openserver_day_1_reward_item={[0]=item_table[71],[1]=item_table[68]},openserver_day_2_reward_item={[0]=item_table[71],[1]=item_table[68]},},
{level=460,max_level=517,openserver_day_2_reward_item={[0]=item_table[72],[1]=item_table[68]},},
{level=518,max_level=650,openserver_day_1_reward_item={[0]=item_table[73],[1]=item_table[74]},openserver_day_2_reward_item={[0]=item_table[73],[1]=item_table[74]},},
{level=651,max_level=713,openserver_day_1_reward_item={[0]=item_table[75],[1]=item_table[74]},openserver_day_2_reward_item={[0]=item_table[75],[1]=item_table[74]},},
{find_type=13,get_type=0,level=714,exp_factor=6,openserver_day_1_reward_item={[0]=item_table[76],[1]=item_table[74]},openserver_day_2_reward_item={[0]=item_table[76],[1]=item_table[74]},},
{find_type=16,get_type=0,level=1,exp_factor=60,openserver_day_1_reward_item={[0]=item_table[77],[1]=item_table[78],[2]=item_table[79],[3]=item_table[80]},openserver_day_2_reward_item={[0]=item_table[77],[1]=item_table[78],[2]=item_table[79],[3]=item_table[80]},},
{find_type=16,level=1,cost=200,vip_extra_cost=100,openserver_day_1_reward_item={[0]=item_table[81],[1]=item_table[82],[2]=item_table[27],[3]=item_table[78],[4]=item_table[79],[5]=item_table[80]},openserver_day_2_reward_item={[0]=item_table[81],[1]=item_table[82],[2]=item_table[27],[3]=item_table[78],[4]=item_table[79],[5]=item_table[80]},},
{find_type=17,openserver_day_1_reward_item={[0]=item_table[83],[1]=item_table[84],[2]=item_table[85]},openserver_day_2_reward_item={[0]=item_table[83],[1]=item_table[84],[2]=item_table[85]},},
{find_type=17,openserver_day_1_reward_item={[0]=item_table[86],[1]=item_table[87],[2]=item_table[83],[3]=item_table[84],[4]=item_table[85]},openserver_day_2_reward_item={[0]=item_table[86],[1]=item_table[87],[2]=item_table[83],[3]=item_table[84],[4]=item_table[85]},},
{find_type=3,openserver_day_1_reward_item={[0]=item_table[88],[1]=item_table[89],[2]=item_table[90],[3]=item_table[91],[4]=item_table[92]},openserver_day_2_reward_item={[0]=item_table[88],[1]=item_table[89],[2]=item_table[90],[3]=item_table[91],[4]=item_table[92]},},
{find_type=3,openserver_day_1_reward_item={[0]=item_table[49],[1]=item_table[57],[2]=item_table[93],[3]=item_table[94],[4]=item_table[95]},openserver_day_2_reward_item={[0]=item_table[49],[1]=item_table[57],[2]=item_table[93],[3]=item_table[94],[4]=item_table[95]},},
{find_type=4,exp_factor=100,openserver_day_1_reward_item={[0]=item_table[96],[1]=item_table[97],[2]=item_table[98],[3]=item_table[99],[4]=item_table[100],[5]=item_table[101],[6]=item_table[102],[7]=item_table[103]},openserver_day_2_reward_item={[0]=item_table[96],[1]=item_table[97],[2]=item_table[98],[3]=item_table[99],[4]=item_table[100],[5]=item_table[101],[6]=item_table[102],[7]=item_table[103]},},
{find_type=4,exp_factor=33,openserver_day_1_reward_item={[0]=item_table[77],[1]=item_table[104],[2]=item_table[99],[3]=item_table[100],[4]=item_table[105],[5]=item_table[106],[6]=item_table[107]},openserver_day_2_reward_item={[0]=item_table[77],[1]=item_table[104],[2]=item_table[99],[3]=item_table[100],[4]=item_table[105],[5]=item_table[106],[6]=item_table[107]},},
{daily_find=0,cost=50,vip_extra_cost=25,exp_factor=90,openserver_day_1_reward_item={[0]=item_table[108],[1]=item_table[109],[2]=item_table[110],[3]=item_table[111],[4]=item_table[19]},openserver_day_2_reward_item={[0]=item_table[108],[1]=item_table[109],[2]=item_table[110],[3]=item_table[111],[4]=item_table[19]},},
{daily_find=0,get_type=0,exp_factor=30,openserver_day_1_reward_item={[0]=item_table[112],[1]=item_table[113],[2]=item_table[110],[3]=item_table[49],[4]=item_table[11]},openserver_day_2_reward_item={[0]=item_table[112],[1]=item_table[113],[2]=item_table[110],[3]=item_table[49],[4]=item_table[11]},},
{find_type=8,openserver_day_1_reward_item={[0]=item_table[114],[1]=item_table[115],[2]=item_table[55],[3]=item_table[116],[4]=item_table[117]},openserver_day_2_reward_item={[0]=item_table[114],[1]=item_table[115],[2]=item_table[55],[3]=item_table[116],[4]=item_table[117]},},
{find_type=8,openserver_day_1_reward_item={[0]=item_table[118],[1]=item_table[27],[2]=item_table[46],[3]=item_table[119],[4]=item_table[120]},openserver_day_2_reward_item={[0]=item_table[118],[1]=item_table[27],[2]=item_table[46],[3]=item_table[119],[4]=item_table[120]},},
{find_type=9,openserver_day_1_reward_item={[0]=item_table[121],[1]=item_table[77],[2]=item_table[122],[3]=item_table[93],[4]=item_table[123],[5]=item_table[46]},openserver_day_2_reward_item={[0]=item_table[121],[1]=item_table[77],[2]=item_table[122],[3]=item_table[93],[4]=item_table[123],[5]=item_table[46]},},
{find_type=9,openserver_day_1_reward_item={[0]=item_table[118],[1]=item_table[27],[2]=item_table[124],[3]=item_table[93],[4]=item_table[125],[5]=item_table[126]},openserver_day_2_reward_item={[0]=item_table[118],[1]=item_table[27],[2]=item_table[124],[3]=item_table[93],[4]=item_table[125],[5]=item_table[126]},},
{find_type=11,openserver_day_1_reward_item={[0]=item_table[127],[1]=item_table[74],[2]=item_table[128],[3]=item_table[129],[4]=item_table[130]},openserver_day_2_reward_item={[0]=item_table[127],[1]=item_table[74],[2]=item_table[128],[3]=item_table[129],[4]=item_table[130]},},
{find_type=11,openserver_day_1_reward_item={[0]=item_table[114],[1]=item_table[74],[2]=item_table[131],[3]=item_table[110],[4]=item_table[130]},openserver_day_2_reward_item={[0]=item_table[114],[1]=item_table[74],[2]=item_table[131],[3]=item_table[110],[4]=item_table[130]},},
{find_type=16,exp_factor=0,openserver_day_1_reward_item={[0]=item_table[132],[1]=item_table[133],[2]=item_table[134],[3]=item_table[135],[4]=item_table[136]},openserver_day_2_reward_item={[0]=item_table[132],[1]=item_table[133],[2]=item_table[134],[3]=item_table[135],[4]=item_table[136]},},
{find_type=16,exp_factor=0,openserver_day_1_reward_item={[0]=item_table[137],[1]=item_table[133],[2]=item_table[138],[3]=item_table[139]},openserver_day_2_reward_item={[0]=item_table[137],[1]=item_table[133],[2]=item_table[138],[3]=item_table[139]},},
{find_type=17,level=999,openserver_day_1_reward_item={[0]=item_table[140]},openserver_day_2_reward_item={[0]=item_table[140]},},
{find_type=17,level=999,openserver_day_1_reward_item={[0]=item_table[141]},openserver_day_2_reward_item={[0]=item_table[141]},},
{find_type=18,openserver_day_1_reward_item={[0]=item_table[142],[1]=item_table[143],[2]=item_table[94],[3]=item_table[144],[4]=item_table[145],[5]=item_table[146]},openserver_day_2_reward_item={[0]=item_table[142],[1]=item_table[143],[2]=item_table[94],[3]=item_table[144],[4]=item_table[145],[5]=item_table[146]},},
{find_type=18,openserver_day_1_reward_item={[0]=item_table[115],[1]=item_table[143],[2]=item_table[94],[3]=item_table[144],[4]=item_table[147],[5]=item_table[146]},openserver_day_2_reward_item={[0]=item_table[115],[1]=item_table[143],[2]=item_table[94],[3]=item_table[144],[4]=item_table[147],[5]=item_table[146]},},
{find_type=19,exp_factor=150,openserver_day_1_reward_item={[0]=item_table[148],[1]=item_table[149],[2]=item_table[150],[3]=item_table[151],[4]=item_table[140],[5]=item_table[152],[6]=item_table[153],[7]=item_table[154]},openserver_day_2_reward_item={[0]=item_table[148],[1]=item_table[149],[2]=item_table[150],[3]=item_table[151],[4]=item_table[140],[5]=item_table[152],[6]=item_table[153],[7]=item_table[154]},},
{find_type=19,exp_factor=50,openserver_day_1_reward_item={[0]=item_table[155],[1]=item_table[100],[2]=item_table[156],[3]=item_table[157],[4]=item_table[93],[5]=item_table[158],[6]=item_table[111]},openserver_day_2_reward_item={[0]=item_table[155],[1]=item_table[100],[2]=item_table[156],[3]=item_table[157],[4]=item_table[93],[5]=item_table[158],[6]=item_table[111]},}
},

activity_find_reward_meta_table_map={
[64]=74,	-- depth:1
[40]=74,	-- depth:1
[73]=74,	-- depth:1
[17]=18,	-- depth:1
[16]=18,	-- depth:1
[15]=18,	-- depth:1
[63]=74,	-- depth:1
[71]=74,	-- depth:1
[72]=74,	-- depth:1
[69]=74,	-- depth:1
[68]=74,	-- depth:1
[67]=74,	-- depth:1
[66]=74,	-- depth:1
[65]=74,	-- depth:1
[59]=74,	-- depth:1
[60]=74,	-- depth:1
[61]=74,	-- depth:1
[62]=74,	-- depth:1
[70]=74,	-- depth:1
[38]=18,	-- depth:1
[114]=100,	-- depth:1
[102]=100,	-- depth:1
[104]=100,	-- depth:1
[96]=100,	-- depth:1
[106]=100,	-- depth:1
[94]=92,	-- depth:1
[98]=100,	-- depth:1
[4]=91,	-- depth:1
[93]=91,	-- depth:1
[112]=100,	-- depth:1
[2]=91,	-- depth:1
[108]=100,	-- depth:1
[110]=108,	-- depth:2
[111]=99,	-- depth:1
[105]=99,	-- depth:1
[103]=99,	-- depth:1
[101]=99,	-- depth:1
[107]=99,	-- depth:1
[76]=82,	-- depth:1
[77]=82,	-- depth:1
[78]=82,	-- depth:1
[79]=78,	-- depth:2
[97]=99,	-- depth:1
[80]=82,	-- depth:1
[81]=82,	-- depth:1
[95]=99,	-- depth:1
[83]=90,	-- depth:1
[84]=90,	-- depth:1
[85]=90,	-- depth:1
[86]=90,	-- depth:1
[88]=90,	-- depth:1
[89]=90,	-- depth:1
[75]=82,	-- depth:1
[87]=86,	-- depth:2
[57]=58,	-- depth:1
[56]=58,	-- depth:1
[12]=18,	-- depth:1
[13]=12,	-- depth:2
[14]=12,	-- depth:2
[28]=11,	-- depth:1
[113]=99,	-- depth:1
[30]=38,	-- depth:2
[31]=38,	-- depth:2
[32]=31,	-- depth:3
[33]=31,	-- depth:3
[34]=31,	-- depth:3
[35]=38,	-- depth:2
[36]=35,	-- depth:3
[37]=35,	-- depth:3
[29]=30,	-- depth:3
[41]=11,	-- depth:1
[55]=58,	-- depth:1
[54]=58,	-- depth:1
[53]=58,	-- depth:1
[52]=58,	-- depth:1
[51]=58,	-- depth:1
[39]=58,	-- depth:1
[50]=58,	-- depth:1
[1]=3,	-- depth:1
[45]=58,	-- depth:1
[43]=58,	-- depth:1
[48]=58,	-- depth:1
[47]=58,	-- depth:1
[46]=58,	-- depth:1
[49]=58,	-- depth:1
[44]=58,	-- depth:1
[5]=11,	-- depth:1
[6]=5,	-- depth:2
[109]=107,	-- depth:2
[7]=5,	-- depth:2
[8]=11,	-- depth:1
[9]=8,	-- depth:2
[27]=28,	-- depth:2
[19]=41,	-- depth:2
[20]=28,	-- depth:2
[22]=28,	-- depth:2
[23]=22,	-- depth:3
[24]=28,	-- depth:2
[25]=28,	-- depth:2
[26]=25,	-- depth:3
[10]=8,	-- depth:2
[21]=28,	-- depth:2
},
hundred_rebate={
{need_level=200,gold_is_bind=1,},
{seq=1,need_gold=98,need_day=8,reward_gold=98,reward_item={[0]=item_table[159],[1]=item_table[160],[2]=item_table[160],[3]=item_table[161],[4]=item_table[162],[5]=item_table[163]},},
{seq=2,need_gold=888,need_day=13,reward_gold=888,reward_item={[0]=item_table[164],[1]=item_table[165],[2]=item_table[100],[3]=item_table[156],[4]=item_table[166],[5]=item_table[167]},},
{seq=3,need_gold=1088,need_day=19,reward_gold=1088,reward_item={[0]=item_table[168],[1]=item_table[169],[2]=item_table[170],[3]=item_table[166],[4]=item_table[167]},}
},

hundred_rebate_meta_table_map={
},
online_reward={
{reward_item=item_table[171],},
{seq=1,reward_item=item_table[172],},
{seq=2,},
{seq=3,},
{reward_type=1,online_min=30,},
{seq=1,reward_item=item_table[172],},
{reward_type=1,online_min=30,},
{seq=3,},
{reward_type=2,online_min=60,},
{reward_type=2,online_min=60,},
{reward_type=2,online_min=60,},
{seq=3,},
{reward_type=3,online_min=120,},
{reward_type=3,online_min=120,},
{reward_type=3,online_min=120,},
{seq=3,}
},

online_reward_meta_table_map={
[5]=1,	-- depth:1
[7]=3,	-- depth:1
[8]=7,	-- depth:2
[9]=5,	-- depth:2
[11]=7,	-- depth:2
[12]=11,	-- depth:3
[13]=5,	-- depth:2
[15]=7,	-- depth:2
[16]=15,	-- depth:3
[6]=7,	-- depth:2
[10]=6,	-- depth:3
[14]=6,	-- depth:3
},
VIP_reward={
{},
{seq=1,level=50,},
{seq=2,level=70,},
{seq=3,vip_limit=2,level=90,},
{seq=4,level=120,},
{seq=5,vip_limit=3,level=150,},
{seq=6,level=170,},
{seq=7,vip_limit=4,level=200,limit_or_not=1,limit_count=8,},
{seq=8,level=230,limit_count=6,},
{seq=9,level=250,limit_count=5,},
{seq=10,vip_limit=5,level=270,limit_count=4,},
{seq=11,level=300,limit_count=3,}
},

VIP_reward_meta_table_map={
[5]=4,	-- depth:1
[7]=6,	-- depth:1
[9]=8,	-- depth:1
[10]=8,	-- depth:1
[11]=8,	-- depth:1
[12]=11,	-- depth:2
},
equip_show={
{star_level=1,},
{item_ID=7126,},
{item_ID=134,},
{item_ID=2140,},
{item_ID=1146,},
{item_ID=4314,},
{item_ID=334,},
{item_ID=2340,},
{item_ID=1346,}
},

equip_show_meta_table_map={
[2]=1,	-- depth:1
[6]=2,	-- depth:2
},
level_vip_reward={
{vip_level_limit=3,level_gift_item={[0]=item_table[173],[1]=item_table[162]},vip_gift_item={[0]=item_table[174],[1]=item_table[175],[2]=item_table[162]},server_count=0,broadcast_num=0,},
{index=2,level_limit=60,level_gift_item={[0]=item_table[173],[1]=item_table[176]},vip_gift_item={[0]=item_table[174],[1]=item_table[173],[2]=item_table[176]},},
{index=3,level_limit=70,level_gift_item={[0]=item_table[177],[1]=item_table[173]},vip_gift_item={[0]=item_table[110],[1]=item_table[176],[2]=item_table[178]},},
{index=4,level_limit=90,level_gift_item={[0]=item_table[179],[1]=item_table[180],[2]=item_table[162]},vip_gift_item={[0]=item_table[179],[1]=item_table[180],[2]=item_table[175],[3]=item_table[162]},},
{index=5,level_limit=100,level_gift_item={[0]=item_table[181],[1]=item_table[173],[2]=item_table[182]},vip_gift_item={[0]=item_table[181],[1]=item_table[183],[2]=item_table[176],[3]=item_table[182]},},
{index=6,level_limit=110,level_gift_item={[0]=item_table[173],[1]=item_table[180],[2]=item_table[88]},vip_gift_item={[0]=item_table[100],[1]=item_table[180],[2]=item_table[174],[3]=item_table[88]},},
{index=7,level_limit=120,level_gift_item={[0]=item_table[184],[1]=item_table[41],[2]=item_table[112],[3]=item_table[185]},vip_gift_item={[0]=item_table[184],[1]=item_table[174],[2]=item_table[112],[3]=item_table[185]},},
{index=8,level_limit=130,level_gift_item={[0]=item_table[179],[1]=item_table[80],[2]=item_table[162],[3]=item_table[186]},vip_gift_item={[0]=item_table[179],[1]=item_table[187],[2]=item_table[162],[3]=item_table[186]},},
{index=9,level_limit=140,level_gift_item={[0]=item_table[188],[1]=item_table[173],[2]=item_table[176],[3]=item_table[162],[4]=item_table[189]},vip_gift_item={[0]=item_table[110],[1]=item_table[174],[2]=item_table[176],[3]=item_table[162],[4]=item_table[189]},},
{index=10,level_limit=150,level_gift_item={[0]=item_table[184],[1]=item_table[190],[2]=item_table[112],[3]=item_table[162],[4]=item_table[189]},vip_gift_item={[0]=item_table[184],[1]=item_table[174],[2]=item_table[112],[3]=item_table[162],[4]=item_table[189]},},
{index=11,level_limit=160,level_gift_item={[0]=item_table[191],[1]=item_table[192],[2]=item_table[193],[3]=item_table[194],[4]=item_table[189]},vip_gift_item={[0]=item_table[191],[1]=item_table[192],[2]=item_table[193],[3]=item_table[194],[4]=item_table[189]},},
{index=12,level_limit=170,level_gift_item={[0]=item_table[179],[1]=item_table[182],[2]=item_table[125],[3]=item_table[176],[4]=item_table[195]},vip_gift_item={[0]=item_table[179],[1]=item_table[110],[2]=item_table[182],[3]=item_table[125],[4]=item_table[176],[5]=item_table[195]},},
{index=13,level_limit=180,level_gift_item={[0]=item_table[196],[1]=item_table[183],[2]=item_table[197],[3]=item_table[198],[4]=item_table[195]},vip_gift_item={[0]=item_table[196],[1]=item_table[187],[2]=item_table[183],[3]=item_table[199],[4]=item_table[198],[5]=item_table[195]},},
{index=14,level_limit=190,level_gift_item={[0]=item_table[184],[1]=item_table[180],[2]=item_table[112],[3]=item_table[200],[4]=item_table[195]},vip_gift_item={[0]=item_table[201],[1]=item_table[184],[2]=item_table[100],[3]=item_table[112],[4]=item_table[200],[5]=item_table[195]},},
{index=15,level_limit=200,level_gift_item={[0]=item_table[133],[1]=item_table[202],[2]=item_table[174],[3]=item_table[162],[4]=item_table[195]},vip_gift_item={[0]=item_table[133],[1]=item_table[156],[2]=item_table[202],[3]=item_table[174],[4]=item_table[166],[5]=item_table[195]},},
{index=16,level_limit=210,level_gift_item={[0]=item_table[179],[1]=item_table[203],[2]=item_table[204],[3]=item_table[80],[4]=item_table[195]},vip_gift_item={[0]=item_table[179],[1]=item_table[205],[2]=item_table[203],[3]=item_table[204],[4]=item_table[80],[5]=item_table[195]},},
{index=17,level_limit=220,level_gift_item={[0]=item_table[191],[1]=item_table[184],[2]=item_table[180],[3]=item_table[112],[4]=item_table[195]},vip_gift_item={[0]=item_table[206],[1]=item_table[207],[2]=item_table[191],[3]=item_table[180],[4]=item_table[112],[5]=item_table[195]},},
{index=18,level_limit=230,level_gift_item={[0]=item_table[134],[1]=item_table[177],[2]=item_table[173],[3]=item_table[193],[4]=item_table[195]},vip_gift_item={[0]=item_table[134],[1]=item_table[208],[2]=item_table[177],[3]=item_table[173],[4]=item_table[193],[5]=item_table[195]},},
{index=19,level_limit=240,vip_level_limit=4,level_gift_item={[0]=item_table[209],[1]=item_table[210],[2]=item_table[183],[3]=item_table[194],[4]=item_table[195]},vip_gift_item={[0]=item_table[211],[1]=item_table[212],[2]=item_table[210],[3]=item_table[183],[4]=item_table[194],[5]=item_table[195]},server_count=500,broadcast_num=10,},
{index=20,level_limit=250,level_gift_item={[0]=item_table[179],[1]=item_table[182],[2]=item_table[125],[3]=item_table[176],[4]=item_table[213]},vip_gift_item={[0]=item_table[179],[1]=item_table[110],[2]=item_table[182],[3]=item_table[125],[4]=item_table[176],[5]=item_table[213]},},
{index=21,level_limit=260,level_gift_item={[0]=item_table[196],[1]=item_table[183],[2]=item_table[200],[3]=item_table[198],[4]=item_table[213]},vip_gift_item={[0]=item_table[196],[1]=item_table[187],[2]=item_table[183],[3]=item_table[199],[4]=item_table[198],[5]=item_table[213]},},
{index=22,level_limit=270,level_gift_item={[0]=item_table[184],[1]=item_table[180],[2]=item_table[112],[3]=item_table[200],[4]=item_table[213]},vip_gift_item={[0]=item_table[201],[1]=item_table[184],[2]=item_table[100],[3]=item_table[112],[4]=item_table[200],[5]=item_table[213]},},
{index=23,level_limit=280,vip_level_limit=6,level_gift_item={[0]=item_table[133],[1]=item_table[202],[2]=item_table[174],[3]=item_table[162],[4]=item_table[213]},vip_gift_item={[0]=item_table[133],[1]=item_table[156],[2]=item_table[202],[3]=item_table[174],[4]=item_table[166],[5]=item_table[213]},server_count=300,broadcast_num=10,},
{index=24,level_limit=290,level_gift_item={[0]=item_table[214],[1]=item_table[203],[2]=item_table[204],[3]=item_table[80],[4]=item_table[213]},vip_gift_item={[0]=item_table[214],[1]=item_table[205],[2]=item_table[203],[3]=item_table[204],[4]=item_table[80],[5]=item_table[213]},},
{index=25,level_limit=300,level_gift_item={[0]=item_table[191],[1]=item_table[184],[2]=item_table[180],[3]=item_table[112],[4]=item_table[213]},vip_gift_item={[0]=item_table[206],[1]=item_table[207],[2]=item_table[191],[3]=item_table[180],[4]=item_table[112],[5]=item_table[213]},},
{index=26,level_limit=310,level_gift_item={[0]=item_table[134],[1]=item_table[177],[2]=item_table[173],[3]=item_table[193],[4]=item_table[215]},vip_gift_item={[0]=item_table[134],[1]=item_table[208],[2]=item_table[177],[3]=item_table[173],[4]=item_table[193],[5]=item_table[215]},},
{index=27,level_limit=320,level_gift_item={[0]=item_table[209],[1]=item_table[210],[2]=item_table[183],[3]=item_table[194],[4]=item_table[215]},vip_gift_item={[0]=item_table[211],[1]=item_table[212],[2]=item_table[210],[3]=item_table[183],[4]=item_table[194],[5]=item_table[215]},},
{index=28,level_limit=330,level_gift_item={[0]=item_table[216],[1]=item_table[182],[2]=item_table[125],[3]=item_table[176],[4]=item_table[215]},vip_gift_item={[0]=item_table[216],[1]=item_table[110],[2]=item_table[182],[3]=item_table[125],[4]=item_table[176],[5]=item_table[215]},},
{index=29,level_limit=340,level_gift_item={[0]=item_table[196],[1]=item_table[183],[2]=item_table[200],[3]=item_table[198],[4]=item_table[215]},vip_gift_item={[0]=item_table[196],[1]=item_table[187],[2]=item_table[183],[3]=item_table[199],[4]=item_table[198],[5]=item_table[215]},},
{index=30,level_limit=350,level_gift_item={[0]=item_table[184],[1]=item_table[180],[2]=item_table[112],[3]=item_table[200],[4]=item_table[215]},vip_gift_item={[0]=item_table[201],[1]=item_table[184],[2]=item_table[100],[3]=item_table[112],[4]=item_table[200],[5]=item_table[215]},},
{index=31,level_limit=360,level_gift_item={[0]=item_table[133],[1]=item_table[202],[2]=item_table[174],[3]=item_table[162],[4]=item_table[215]},vip_gift_item={[0]=item_table[133],[1]=item_table[156],[2]=item_table[202],[3]=item_table[174],[4]=item_table[166],[5]=item_table[215]},},
{index=32,level_limit=370,level_gift_item={[0]=item_table[214],[1]=item_table[203],[2]=item_table[204],[3]=item_table[80],[4]=item_table[1]},vip_gift_item={[0]=item_table[214],[1]=item_table[205],[2]=item_table[203],[3]=item_table[204],[4]=item_table[80],[5]=item_table[1]},},
{index=33,level_limit=380,level_gift_item={[0]=item_table[191],[1]=item_table[184],[2]=item_table[180],[3]=item_table[112],[4]=item_table[1]},vip_gift_item={[0]=item_table[206],[1]=item_table[207],[2]=item_table[191],[3]=item_table[180],[4]=item_table[112],[5]=item_table[1]},},
{index=34,level_limit=390,level_gift_item={[0]=item_table[134],[1]=item_table[177],[2]=item_table[173],[3]=item_table[193],[4]=item_table[1]},vip_gift_item={[0]=item_table[134],[1]=item_table[208],[2]=item_table[177],[3]=item_table[173],[4]=item_table[193],[5]=item_table[1]},},
{index=35,level_limit=400,level_gift_item={[0]=item_table[209],[1]=item_table[210],[2]=item_table[183],[3]=item_table[194],[4]=item_table[1]},vip_gift_item={[0]=item_table[211],[1]=item_table[212],[2]=item_table[210],[3]=item_table[183],[4]=item_table[194],[5]=item_table[1]},},
{index=36,level_limit=410,level_gift_item={[0]=item_table[216],[1]=item_table[182],[2]=item_table[125],[3]=item_table[176],[4]=item_table[1]},vip_gift_item={[0]=item_table[216],[1]=item_table[110],[2]=item_table[182],[3]=item_table[125],[4]=item_table[176],[5]=item_table[1]},},
{index=37,level_limit=420,level_gift_item={[0]=item_table[196],[1]=item_table[183],[2]=item_table[200],[3]=item_table[198],[4]=item_table[1]},vip_gift_item={[0]=item_table[196],[1]=item_table[187],[2]=item_table[183],[3]=item_table[199],[4]=item_table[198],[5]=item_table[1]},},
{index=38,level_limit=430,level_gift_item={[0]=item_table[184],[1]=item_table[180],[2]=item_table[112],[3]=item_table[200],[4]=item_table[1]},vip_gift_item={[0]=item_table[201],[1]=item_table[184],[2]=item_table[100],[3]=item_table[112],[4]=item_table[200],[5]=item_table[1]},},
{index=39,level_limit=440,level_gift_item={[0]=item_table[133],[1]=item_table[202],[2]=item_table[174],[3]=item_table[162],[4]=item_table[1]},vip_gift_item={[0]=item_table[133],[1]=item_table[156],[2]=item_table[202],[3]=item_table[174],[4]=item_table[166],[5]=item_table[1]},},
{index=40,level_limit=450,},
{index=41,level_limit=460,vip_level_limit=7,server_count=100,},
{index=42,level_limit=470,level_gift_item={[0]=item_table[134],[1]=item_table[177],[2]=item_table[173],[3]=item_table[193],[4]=item_table[217]},vip_gift_item={[0]=item_table[134],[1]=item_table[208],[2]=item_table[177],[3]=item_table[173],[4]=item_table[193],[5]=item_table[217]},},
{index=43,level_limit=480,level_gift_item={[0]=item_table[209],[1]=item_table[210],[2]=item_table[183],[3]=item_table[194],[4]=item_table[217]},vip_gift_item={[0]=item_table[211],[1]=item_table[212],[2]=item_table[210],[3]=item_table[183],[4]=item_table[194],[5]=item_table[217]},},
{index=44,level_limit=490,level_gift_item={[0]=item_table[216],[1]=item_table[182],[2]=item_table[125],[3]=item_table[176],[4]=item_table[217]},vip_gift_item={[0]=item_table[216],[1]=item_table[110],[2]=item_table[182],[3]=item_table[125],[4]=item_table[176],[5]=item_table[217]},},
{index=45,level_limit=500,level_gift_item={[0]=item_table[196],[1]=item_table[183],[2]=item_table[200],[3]=item_table[198],[4]=item_table[217]},vip_gift_item={[0]=item_table[196],[1]=item_table[187],[2]=item_table[183],[3]=item_table[199],[4]=item_table[198],[5]=item_table[217]},},
{index=46,level_limit=510,level_gift_item={[0]=item_table[184],[1]=item_table[180],[2]=item_table[112],[3]=item_table[200],[4]=item_table[217]},vip_gift_item={[0]=item_table[201],[1]=item_table[184],[2]=item_table[100],[3]=item_table[112],[4]=item_table[200],[5]=item_table[217]},},
{index=47,level_limit=520,level_gift_item={[0]=item_table[133],[1]=item_table[202],[2]=item_table[174],[3]=item_table[162],[4]=item_table[217]},vip_gift_item={[0]=item_table[133],[1]=item_table[156],[2]=item_table[202],[3]=item_table[174],[4]=item_table[166],[5]=item_table[217]},},
{index=48,level_limit=530,level_gift_item={[0]=item_table[214],[1]=item_table[203],[2]=item_table[204],[3]=item_table[80],[4]=item_table[217]},vip_gift_item={[0]=item_table[214],[1]=item_table[205],[2]=item_table[203],[3]=item_table[204],[4]=item_table[80],[5]=item_table[217]},},
{index=49,level_limit=540,vip_level_limit=8,server_count=50,},
{index=50,level_limit=550,},
{index=51,level_limit=560,},
{index=52,level_limit=570,},
{index=53,level_limit=580,},
{index=54,level_limit=590,},
{index=55,level_limit=600,},
{index=56,level_limit=610,},
{index=57,level_limit=620,},
{index=58,level_limit=630,},
{index=59,level_limit=640,},
{index=60,level_limit=650,},
{index=61,level_limit=660,},
{index=62,level_limit=670,level_gift_item={[0]=item_table[184],[1]=item_table[180],[2]=item_table[112],[3]=item_table[200],[4]=item_table[217]},vip_gift_item={[0]=item_table[201],[1]=item_table[184],[2]=item_table[100],[3]=item_table[112],[4]=item_table[200],[5]=item_table[217]},},
{index=63,level_limit=680,},
{index=64,level_limit=690,level_gift_item={[0]=item_table[214],[1]=item_table[203],[2]=item_table[204],[3]=item_table[80],[4]=item_table[217]},vip_gift_item={[0]=item_table[214],[1]=item_table[205],[2]=item_table[203],[3]=item_table[204],[4]=item_table[80],[5]=item_table[217]},},
{index=65,level_limit=700,},
{index=66,level_limit=710,level_gift_item={[0]=item_table[134],[1]=item_table[177],[2]=item_table[173],[3]=item_table[193],[4]=item_table[217]},vip_gift_item={[0]=item_table[134],[1]=item_table[208],[2]=item_table[177],[3]=item_table[173],[4]=item_table[193],[5]=item_table[217]},},
{index=67,level_limit=720,level_gift_item={[0]=item_table[209],[1]=item_table[210],[2]=item_table[183],[3]=item_table[194],[4]=item_table[217]},vip_gift_item={[0]=item_table[211],[1]=item_table[212],[2]=item_table[210],[3]=item_table[183],[4]=item_table[194],[5]=item_table[217]},},
{index=68,level_limit=730,level_gift_item={[0]=item_table[216],[1]=item_table[182],[2]=item_table[125],[3]=item_table[176],[4]=item_table[217]},vip_gift_item={[0]=item_table[216],[1]=item_table[110],[2]=item_table[182],[3]=item_table[125],[4]=item_table[176],[5]=item_table[217]},},
{index=69,level_limit=740,level_gift_item={[0]=item_table[196],[1]=item_table[183],[2]=item_table[200],[3]=item_table[198],[4]=item_table[217]},vip_gift_item={[0]=item_table[196],[1]=item_table[187],[2]=item_table[183],[3]=item_table[199],[4]=item_table[198],[5]=item_table[217]},},
{index=70,level_limit=750,},
{index=71,level_limit=760,},
{index=72,level_limit=770,},
{index=73,level_limit=780,},
{index=74,level_limit=790,},
{index=75,level_limit=800,},
{index=76,level_limit=810,},
{index=77,level_limit=820,},
{index=78,level_limit=830,},
{index=79,level_limit=840,},
{index=80,level_limit=850,},
{index=81,level_limit=860,},
{index=82,level_limit=870,},
{index=83,level_limit=880,},
{index=84,level_limit=890,},
{index=85,level_limit=900,},
{index=86,level_limit=910,},
{index=87,level_limit=920,},
{index=88,level_limit=930,},
{index=89,level_limit=940,},
{index=90,level_limit=950,},
{index=91,level_limit=960,},
{index=92,level_limit=970,},
{index=93,level_limit=980,},
{index=94,level_limit=990,},
{index=95,level_limit=1000,level_gift_item={[0]=item_table[133],[1]=item_table[202],[2]=item_table[174],[3]=item_table[162],[4]=item_table[217]},vip_gift_item={[0]=item_table[133],[1]=item_table[156],[2]=item_table[202],[3]=item_table[174],[4]=item_table[166],[5]=item_table[217]},}
},

level_vip_reward_meta_table_map={
[63]=95,	-- depth:1
[60]=68,	-- depth:1
[59]=67,	-- depth:1
[58]=66,	-- depth:1
[56]=64,	-- depth:1
[55]=95,	-- depth:1
[54]=62,	-- depth:1
[53]=69,	-- depth:1
[52]=68,	-- depth:1
[61]=69,	-- depth:1
[70]=62,	-- depth:1
[72]=64,	-- depth:1
[93]=69,	-- depth:1
[92]=68,	-- depth:1
[91]=67,	-- depth:1
[90]=66,	-- depth:1
[88]=64,	-- depth:1
[87]=95,	-- depth:1
[86]=62,	-- depth:1
[85]=69,	-- depth:1
[71]=95,	-- depth:1
[84]=68,	-- depth:1
[82]=66,	-- depth:1
[80]=64,	-- depth:1
[79]=95,	-- depth:1
[78]=62,	-- depth:1
[77]=69,	-- depth:1
[76]=68,	-- depth:1
[75]=67,	-- depth:1
[74]=66,	-- depth:1
[83]=67,	-- depth:1
[94]=62,	-- depth:1
[46]=49,	-- depth:1
[47]=49,	-- depth:1
[32]=41,	-- depth:1
[33]=41,	-- depth:1
[34]=41,	-- depth:1
[35]=41,	-- depth:1
[36]=41,	-- depth:1
[37]=41,	-- depth:1
[38]=41,	-- depth:1
[48]=49,	-- depth:1
[40]=32,	-- depth:2
[42]=49,	-- depth:1
[43]=49,	-- depth:1
[44]=49,	-- depth:1
[51]=43,	-- depth:2
[50]=42,	-- depth:2
[39]=41,	-- depth:1
[45]=49,	-- depth:1
[13]=1,	-- depth:1
[12]=1,	-- depth:1
[11]=1,	-- depth:1
[10]=1,	-- depth:1
[9]=1,	-- depth:1
[6]=1,	-- depth:1
[7]=1,	-- depth:1
[14]=1,	-- depth:1
[5]=1,	-- depth:1
[4]=1,	-- depth:1
[3]=1,	-- depth:1
[2]=1,	-- depth:1
[8]=1,	-- depth:1
[15]=1,	-- depth:1
[17]=19,	-- depth:1
[18]=19,	-- depth:1
[20]=19,	-- depth:1
[21]=19,	-- depth:1
[22]=19,	-- depth:1
[25]=23,	-- depth:1
[26]=23,	-- depth:1
[27]=23,	-- depth:1
[28]=23,	-- depth:1
[29]=23,	-- depth:1
[30]=23,	-- depth:1
[31]=23,	-- depth:1
[16]=1,	-- depth:1
[24]=23,	-- depth:1
},
bind_phone_reware={
{}
},

bind_phone_reware_meta_table_map={
},
offline_exp_default_table={type=0,factor=1,cost_perhour=3,},

uplevel_reward_default_table={seq=0,level=30,reward_item_prof_1={[0]=item_table[218]},reward_item_prof_2={[0]=item_table[218]},limit_or_not=0,limit_count=0,},

other_default_table={level_limit=30,retroactive_open=0,find_sign_in_cost=50,baibei_open_level=40,level_limit=300,VIP_limit=4,seq=1008,item_id=22537,exchange_key="yy666666",exchange_item_id=28721,remind_level=0,end_level=350,cul_level=3,},

sign_in_default_table={indexes=1,day=1,reward_item=item_table[215],vip_0_times=0,vip_1_times=0,vip_2_times=0,vip_3_times=0,vip_4_times=0,vip_5_times=0,vip_6_times=0,vip_7_times=0,vip_8_times=0,vip_9_times=0,vip_10_times=0,vip_11_times=0,vip_12_times=0,vip_13_times=0,vip_14_times=0,vip_15_times=0,vip_16_times=0,vip_17_times=0,vip_18_times=0,vip_19_times=0,vip_20_times=0,vip_21_times=0,vip_22_times=0,vip_23_times=0,vip_24_times=0,vip_25_times=0,vip_26_times=0,vip_27_times=0,vip_28_times=0,vip_29_times=0,vip_30_times=0,vip_times=0,show_vip=0,},

sign_indexes_default_table={indexes=1,grade_rise=1,grade_end=349,},

sign_heap_in_default_table={indexes=1,heap_times=2,reward_type=0,reward_item=item_table[217],sign_word="明日约定",},

activity_find_default_table={name="圣域守护",daily_find=0,find_type=3,vip_type=-1,activity_type=0,is_open=1,module_name="guild#guild_activity",},

activity_find_reward_default_table={daily_find=1,find_type=7,get_type=1,level=120,max_level=2000,cost=100,vip_extra_cost=50,exp_factor=20,openserver_day_1_reward_item={[0]=item_table[18],[1]=item_table[24],[2]=item_table[25],[3]=item_table[26]},openserver_day_2_reward_item={[0]=item_table[18],[1]=item_table[24],[2]=item_table[25],[3]=item_table[26]},},

hundred_rebate_default_table={seq=0,need_level=30,continued_days=15,buy_day=3,need_gold=0,need_day=6,close_day=22,reward_gold=388,gold_is_bind=0,reward_item={[0]=item_table[202],[1]=item_table[202],[2]=item_table[210],[3]=item_table[210],[4]=item_table[162],[5]=item_table[219]},},

online_reward_default_table={reward_type=0,seq=0,online_min=10,reward_item=item_table[220],},

VIP_reward_default_table={seq=0,vip_limit=1,level=30,reward_item={[0]=item_table[218]},limit_or_not=0,limit_count=0,},

equip_show_default_table={item_ID=4114,star_level=2,},

level_vip_reward_default_table={index=1,level_limit=50,vip_level_limit=10,level_gift_equip_star=1,level_gift_item={[0]=item_table[191],[1]=item_table[184],[2]=item_table[180],[3]=item_table[112],[4]=item_table[217]},vip_level_gift_star=1,vip_gift_item={[0]=item_table[206],[1]=item_table[207],[2]=item_table[191],[3]=item_table[180],[4]=item_table[112],[5]=item_table[217]},server_count=20,broadcast_num=5,},

bind_phone_reware_default_table={gift_index=47,gift_item_id=28870,}

}

