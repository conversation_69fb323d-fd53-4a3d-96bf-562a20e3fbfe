﻿using UnityEditor;
using UnityEngine;
using System.IO;
using System;
using System.Collections.Generic;
using System.Text;

[CustomEditor(typeof(ActorRender))]
public class ActorRenderEditor : Editor
{
    private bool isGray;
    private bool isMultiColor;

    public override void OnInspectorGUI()
    {
        this.DrawDefaultInspector();
        
        ActorRender actorRender = (ActorRender)this.target;
        
        EditorGUILayout.Space();
        
        // 特效管理按钮
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("收集特效数据"))
        {
            actorRender.CollectEffectAttachments();
            EditorUtility.SetDirty(target);
        }
        
        if (GUILayout.Button("清理所有特效"))
        {
            if (EditorUtility.DisplayDialog("确认清理", "慎重！！！确定要清理所有特效绑定吗？", "确定", "取消"))
            {
                actorRender.ClearAllEffectGameObjectAttachs();
                EditorUtility.SetDirty(target);
            }
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        
        if (GUILayout.Button("AutoFetch"))
        {
            actorRender.AutoFetch();
            this.AutoSelectMat();
        }

        if (GUILayout.Button("AutomaticLOD"))
        {
            ActorRender obj = target as ActorRender;
            var prefab = PrefabUtility.GetPrefabParent(obj);
            string meshPath = Path.GetDirectoryName(AssetDatabase.GetAssetPath(prefab.GetInstanceID()));
            GenerateLOD(obj.gameObject, meshPath);
        }

        if (Application.isPlaying)
        {
            if (GUILayout.Button("Play Gray"))
            {
                isGray = !isGray;
                actorRender.SetIsGray(isGray);
            }

            if (GUILayout.Button("Play Green"))
            {
                isMultiColor = !isMultiColor;
                actorRender.SetIsMultiColor(isMultiColor, Color.green);
            }

            if (GUILayout.Button("Play FadeOutEffect"))
            {
                actorRender.PlayFadeEffect(false, 3, null);
            }

            if (GUILayout.Button("Play FadeInEffect"))
            {
                actorRender.PlayFadeEffect(true, 3, null);
            }

            if (GUILayout.Button("Play 消融"))
            {
                actorRender.PlayDissolveEffect();
            }

            if (GUILayout.Button("Play 受击"))
            {
                actorRender.PlayBlinkEffect();
            }
        }
    }

    static int s_nLastProgress = -1;
    static string s_strLastTitle = "";
    static string s_strLastMessage = "";
    static AutomaticLOD curObj;
    static Action curAction;
    static string meshPath;

    static int setIndex = 0;
    static float startTime = 0;
    static List<string> setPath;
    public delegate void autoCheckDelegate();

    static void Progress(string strTitle, string strMessage, float fT)
    {
        int nPercent = Mathf.RoundToInt(fT * 100.0f);

        if (nPercent != s_nLastProgress || s_strLastTitle != strTitle || s_strLastMessage != strMessage)
        {
            s_strLastTitle = strTitle;
            s_strLastMessage = strMessage;
            s_nLastProgress = nPercent;
        }
    }

    public static void UpdateEditor()
    {
        if (curObj)
        {
            if (curObj.m_listLODLevels.Count > 0)
            {
                var obj = curObj;
                curObj = null;

                GenerateLOD(obj.gameObject, meshPath);

                UltimateGameTools.MeshSimplifier.Simplifier[] simplifiers = obj.GetComponentsInChildren<UltimateGameTools.MeshSimplifier.Simplifier>();
                foreach (var sim in simplifiers)
                {
                    DestroyImmediate(sim, true);
                }

                AutomaticLOD[] automaticLODs = obj.GetComponentsInChildren<AutomaticLOD>();
                foreach (var lod in automaticLODs)
                {
                    DestroyImmediate(lod, true);
                }

                if (null != curAction)
                {
                    curAction();
                }
            }
        }
    }

    public static void AutoGenerateLOD(GameObject obj, Action callback, float compression)
    {
        meshPath = Path.GetDirectoryName(AssetDatabase.GetAssetPath(obj.GetInstanceID()));
        s_nLastProgress = -1;
        curAction = callback;

        AutomaticLOD automaticLOD = obj.AddComponent<AutomaticLOD>();
        curObj = automaticLOD;
        CreateDefaultLODS(2, automaticLOD, true, compression);
        automaticLOD.ComputeLODData(true, Progress);
        automaticLOD.ComputeAllLODMeshes(true, Progress);
    }

    private static void CreateDefaultLODS(int nLevels, AutomaticLOD root, bool bRecurseIntoChildren, float compression)
    {
        List<AutomaticLOD.LODLevelData> listLODLevels = new List<AutomaticLOD.LODLevelData>();

        for (int i = 0; i < nLevels; i++)
        {
            AutomaticLOD.LODLevelData data = new AutomaticLOD.LODLevelData();
            float oneminust = (float)(nLevels - i) / (float)nLevels;
            oneminust = oneminust < 1 ? compression : 1;

            data.m_fScreenCoverage = Mathf.Pow(1.0f - 1.0f - oneminust, 6f);
            data.m_fMaxCameraDistance = i == 0 ? 0.0f : i * 100.0f;
            data.m_fMeshVerticesAmount = oneminust;
            data.m_mesh = null;
            data.m_bUsesOriginalMesh = false;
            data.m_nColorEditorBarIndex = i;

            listLODLevels.Add(data);
        }

        root.SetLODLevels(listLODLevels, AutomaticLOD.EvalMode.ScreenCoverage, 1000.0f, bRecurseIntoChildren);
    }

    private static void GenerateLOD(GameObject obj, string meshPath)
    {
        var meshList = AutomaticLODTool.ComputeMesh(obj);

        if (null == meshList || meshList.Length <= 0)
        {
            return;
        }

        for (int i = 0; i < meshList.Length; ++i)
        {
            string path = Path.Combine(meshPath, string.Format("{0}_LOD{1}.asset", obj.name, i + 1));
            AssetDatabase.CreateAsset(meshList[i], path);
            AssetDatabase.Refresh();

            var fileString = OpenFile(path);
            SetReadable(ref fileString, false);
            SaveFile(path, fileString);
        }
    }

    private static void SetReadable(ref string fileString, bool readable)
    {
        if (readable)
        {
            fileString = fileString.Replace("m_IsReadable: 0", "m_IsReadable: 1");
        }
        else
        {
            fileString = fileString.Replace("m_IsReadable: 1", "m_IsReadable: 0");
        }
    }

    private static string OpenFile(string filePath)
    {
        FileStream fs = new FileStream(filePath, FileMode.Open);
        byte[] bytes = new byte[fs.Length];
        fs.Read(bytes, 0, bytes.Length);
        string fileString = Encoding.Default.GetString(bytes);
        fs.Close();
        return fileString;
    }

    private static void SaveFile(string filePath, string fileString)
    {
        FileStream fs = new FileStream(filePath, FileMode.Open);
        byte[] bytes = Encoding.Default.GetBytes(fileString);
        fs.Write(bytes, 0, bytes.Length);
        fs.Close();
    }

    private void AutoSelectMat()
    {
        var actorRender = (ActorRender)this.target;
        var list = actorRender.GetRenderList();
        List<string> findPath = new List<string>();
        if (list.Count > 0)
        {
            for (int i = 0; i < list.Count; i++)
            {
                var item = list[i];
                if (item.renderer.sharedMaterial != null)
                {
                    string path = AssetDatabase.GetAssetPath(item.renderer.sharedMaterial.GetInstanceID());
                    if (path != "" && !findPath.Contains(path))
                    {
                        findPath.Add(path);
                    }
                }
            }
        }

        if (findPath.Count <= 0)
        {
            return;
        }

        setPath = findPath;
        setIndex = 0;
        startTime = 25;

        EditorApplication.update += AutoSetMatDefine;
    }

    public void AutoSetMatDefine()
    {
        startTime = startTime + Time.deltaTime;
        string file = setPath[setIndex];
        bool isCancel = EditorUtility.DisplayCancelableProgressBar("正在设置材质球默认属性", file, (float)setIndex / (float)setPath.Count);
        var path = setPath[setIndex];
        if (setPath != null && setPath.Count > 0 && setIndex < setPath.Count && setPath[setIndex] != null && startTime >= 25)
        {
            if (path.EndsWith(".mat"))
            {
                var real_path = path.Replace('\\', '/');
                Material mat = (Material)AssetDatabase.LoadAssetAtPath(real_path, typeof(Material));
                Selection.activeObject = mat;
                EditorUtility.SetDirty(mat);
                setIndex = setIndex + 1;
                startTime = 0;
            }
            else
                setIndex = setIndex + 1;
        }

        if (isCancel || setIndex >= setPath.Count)
        {
            EditorApplication.update -= AutoSetMatDefine;
            EditorUtility.ClearProgressBar();
            setIndex = 0;
            setPath.Clear();
            startTime = 0;
            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
        }
    }

    private string GetSkinTypeLabel(ChangeSkin.SkinType skinType)
    {
        return skinType switch
        {
            ChangeSkin.SkinType.Body => "身体",
            ChangeSkin.SkinType.Face => "脸部", 
            ChangeSkin.SkinType.Hair => "头发",
            ChangeSkin.SkinType.Eyeball => "眼球",
            _ => skinType.ToString()
        };
    }
}
