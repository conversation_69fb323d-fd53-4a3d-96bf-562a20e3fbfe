--------------------------------------------------
--王者特权.
--------------------------------------------------
function RechargeView:InitRechargeKingVipView()
	self.node_list.buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyKingVip, self))
	self.node_list.free_box_btn.button:AddClickListener(BindTool.Bind(self.OnClickKingVipFreeBox, self))
	self.node_list.tips_text_btn.button:AddClickListener(BindTool.Bind(self.OnClickOpenRechargeView, self))
	self.node_list.vip_info_btn.button:AddClickListener(BindTool.Bind(self.OnClickOpenKingV<PERSON>T<PERSON><PERSON>, self))

	self:LoginKingVipTimeCountDown()

	--顺序很重要.与计算顺序对应.
	self.zhanli_type =
	{
		ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING, --仙翼.
		ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO, --神器.
		ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING, --神武.
		ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN, --背饰.
		MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG,   --伙伴.
		MOUNT_LINGCHONG_APPE_TYPE.MOUNT,       --灵骑.
		0,                                     --天罚.
		1,                                     --炼狱.
		2,                                     --噬魂.
		3,                                     --深渊.
		4                                      --幻梦.
	}

	--是否激活特权.
	self.is_active = false
end

function RechargeView:DeleteRechargeKingVipView()
	if CountDownManager.Instance:HasCountDown("king_vip_down") then
		CountDownManager.Instance:RemoveCountDown("king_vip_down")
	end

	if self.free_box_tween then
		self.free_box_tween:Kill()
		self.free_box_tween = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end
end

function RechargeView:ShowRechargeKingVipView()

end

function RechargeView:FlushRechargeKingVipView()
	self.is_active = RechargeWGCtrl.Instance:GetKingVipIsActive()

	self:SetKingVipBuyBtn()
	self:SetKingVipFreeBox()
	self:ShowKingVipModel()
	self:SetKingVipAdd()
	self:SetZhanLi()
end

function RechargeView:SetKingVipBuyBtn()
	local price = RechargeWGData.Instance:GetKingVipPrice()
	local text = ""

	if self.is_active then
		text = Language.KingVip.price_text2
	else
		local change_price = RoleWGData.GetPayMoneyStr(price)
		text = string.format(Language.KingVip.price_text, change_price)
	end
	self.node_list.buy_text.text.text = text

	XUI.SetButtonEnabled(self.node_list.buy_btn, not self.is_active)
end

function RechargeView:SetKingVipFreeBox()
	local is_get = RechargeWGData.Instance:GetKingVipIsCanaGetFreeBox()
	local is_remind = is_get == 0 and true or false

	self.node_list.free_box_remind:SetActive(is_remind)
	XUI.SetButtonEnabled(self.node_list.free_box_btn, is_remind)

	if is_remind then
		if self.free_box_tween then
			self.free_box_tween:Restart()
		else
			if self.free_box_tween then
				self.free_box_tween:Kill()
				self.free_box_tween = nil
			end

			self.free_box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.free_box_icon.transform, self.free_box_tween)
		end
	elseif self.free_box_tween then
		self.free_box_tween:Pause()
		self.node_list.free_box_icon.transform.localRotation = Quaternion.identity
	end
end

function RechargeView:ShowKingVipModel()
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model_pos)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	local display_data = {}
	display_data.item_id = RechargeWGData.Instance:GetKingVipModel()
	display_data.render_type = 0
	display_data.need_wp_tween = true
	display_data.hide_model_block = true

	self.model_display:SetData(display_data)
end

function RechargeView:SetKingVipAdd()
	local cur_vip_lv = VipWGData.Instance:GetRoleVipLevel()
	local data = RechargeWGData.Instance:GetKingVipAddition(cur_vip_lv)
	if IsEmptyTable(data) then
		return
	end

	local exp_add = math.ceil(data.add_11 / 100)
	local attr_add = math.ceil(data.add_0 / 100)
	self.node_list.exp_text.text.text = string.format("+%s%%", exp_add)
	self.node_list.attr_text.text.text = string.format("+%s%%", attr_add)
end

function RechargeView:SetZhanLi()
	local cur_vip_lv = VipWGData.Instance:GetRoleVipLevel()
	local data = RechargeWGData.Instance:GetKingVipAddition(cur_vip_lv)
	if IsEmptyTable(data) then
		return
	end

	local zhanli = 0

	for index, value in ipairs(self.zhanli_type) do
		local attr_list, capability

		if index < 5 then
			local level = NewAppearanceWGData.Instance:GetAdvancedLevel(value)
			attr_list, capability = NewAppearanceWGData.Instance:GetAdvancedAttrListAndCap(value, level)
		elseif index == 5 or index == 6 then
			local info = NewAppearanceWGData.Instance:GetQiChongInfo(value)
			if IsEmptyTable(info) then
				capability = 0
				return
			end

			local star_level = info.star_level
			attr_list, capability = NewAppearanceWGData.Instance:GetBaseQiChongAttrListAndCap(value, star_level)
		else
			capability = DragonTempleWGData.Instance:GethatchCapBySeq(value)
		end

		zhanli = zhanli + (capability * (data["add_" .. index - 1] / 10000 * 100))
	end

	self.node_list.zhanli_text.text.text = "+" .. zhanli
end

-- 每日宝箱点击.
function RechargeView:OnClickKingVipFreeBox()
	RechargeWGCtrl.Instance:OnCSKingPrivilegeInfoReq()

	local item = RechargeWGData.Instance:GetKingVipFreeItem()
	if IsEmptyTable(item) then
		return
	end

	TipWGCtrl.Instance:ShowGetReward(nil, item, false, nil, nil, false)
end

-- 购买.
function RechargeView:OnClickBuyKingVip()
	local info = RechargeWGData.Instance:GetKingVipRmbBuyCfg()
	if IsEmptyTable(info) then
		return
	end
	RechargeWGCtrl.Instance:Recharge(info.rmb_price, info.rmb_type, info.rmb_seq)
end

-- 激活跳转界面.
function RechargeView:OnClickOpenRechargeView()
	self:Open(TabIndex.recharge_vip)
end

-- 打开加成详情界面.
function RechargeView:OnClickOpenKingVipTips()
	RechargeWGCtrl.Instance:OpenKingVipTips()
end

------------------------------------倒计时
function RechargeView:LoginKingVipTimeCountDown()
	local text = ""

	local end_time = RechargeWGData.Instance:GetKingVipEndTime()

	if end_time <= 0 then
		text = Language.KingVip.validity
	else
		if end_time > TimeWGCtrl.Instance:GetServerTime() then
			text = string.format(Language.KingVip.coundtime,
				TimeUtil.FormatSecondDHM6(end_time - TimeWGCtrl.Instance:GetServerTime()))
			CountDownManager.Instance:AddCountDown("king_vip_down",
				BindTool.Bind1(self.UpdateKingVipCountDown, self),
				BindTool.Bind1(self.OnCompleteKingVip, self), end_time, nil, 1)
		end
	end

	self.node_list.act_time_text.text.text = text
end

function RechargeView:UpdateKingVipCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.act_time_text.text.text = string.format(Language.KingVip.coundtime,
			TimeUtil.FormatSecondDHM6(valid_time))
	end
end

function RechargeView:OnCompleteKingVip()
	self.node_list.act_time_text.text.text = Language.KingVip.validity
	self:Close()
end
