-- 暗器详情-背包列表
HWBuildItemRender = HWBuildItemRender or BaseClass(BaseRender)

function HWBuildItemRender:__init()
end

function HWBuildItemRender:SetIsShowTips(flag)
    self.is_showtip = flag
end

function HWBuildItemRender:LoadCallBack(instance)
    if not self.item_cell_equip then
        self.item_cell_equip = ItemCell.New(self.node_list["item_pos"])
        self.item_cell_equip:SetUseButton(false)
        self.item_cell_equip:SetData(nil)
    end
end

function HWBuildItemRender:FlushMaterialCheck()
    if self.data == nil then
        return
    end
    local is_material = self.data.is_material or false
    if self.node_list["img_check"] then
        self.node_list["img_check"]:SetActive(is_material == true)
    end
end

function HWBuildItemRender:OnFlush()
    if IsEmptyTable(self.data) or self.item_cell_equip == nil then
        if self.node_list["remind"] then
            self.node_list["remind"]:SetActive(false)
        end

        if self.node_list["img_check"] then
            self.node_list["img_check"]:SetActive(false)
        end

        self.node_list["dress"]:SetActive(false)
        self.node_list.img_touch:SetActive(false)
        self.item_cell_equip:SetData(nil)
        return
    end
    self.item_cell_equip:SetData(self.data)
    self.item_cell_equip:SetDefaultEff(false)

    self.node_list.img_touch:SetActive(not IsEmptyTable(self.data))
    if self.node_list["remind"] then
        self.node_list["remind"]:SetActive(self.data.can_compose == true)
    end

    self.node_list["dress"]:SetActive(self.data.is_dress == true)
    local is_material = self.data.is_material or false
    if self.node_list["img_check"] then
        self.node_list["img_check"]:SetActive(is_material == true)
    end
end

function HWBuildItemRender:OnSelectChange(is_select)
    -- 高亮
    if nil == is_select or not next(self.data) then
        self.node_list.img_highlight:SetActive(false)
        return
    end
    self.node_list.img_highlight:SetActive(is_select)
end


function HWBuildItemRender:__delete()
    if self.item_cell_equip ~= nil then
        self.item_cell_equip:DeleteMe()
    end
    self.item_cell = nil
end
