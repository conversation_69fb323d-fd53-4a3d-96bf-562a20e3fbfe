require("game/customized_rumors/customized_rumors_wg_data")
require("game/customized_rumors/customized_rumors_view")
require("game/customized_rumors/customized_rumors")
require("game/customized_rumors/customized_rumors_tip")
require("game/customized_rumors/bullscreen_barrage_view")
require("game/customized_rumors/customized_rumors_broadcast")
require("game/customized_rumors/customized_rumors_set_view")
require("game/customized_rumors/customizaed_rumors_exchange_view")
require("game/customized_rumors/base_special_rumors_cell")
require("game/customized_rumors/regional_barrage")
require("game/customized_rumors/customized_set_barrage_view")

CustomizedRumorsWGCtrl = CustomizedRumorsWGCtrl or BaseClass(BaseWGCtrl)

function CustomizedRumorsWGCtrl:__init()
	if CustomizedRumorsWGCtrl.Instance ~= nil then
		ErrorLog("[CustomizedRumorsWGCtrl] Attemp to create a singleton twice !")
	end
	CustomizedRumorsWGCtrl.Instance = self
	self.view = CustomizedRumorsView.New(GuideModuleName.CustomizedRumorsView)
	self.data = CustomizedRumorsWGData.New()
	self.rumors_tip = CustomizedRumorsTipView.New()
	self.bullscreen_barrage_view = BullScreenBarrageView.New()
	self.rumors_set_view = CustomizaedRumorsSetView.New()
	self.rumors_exchange_view = CustomizaedRumorsExchangeView.New()
	self.custom_set_barrage_view = CustomizedSetBarrageView.New()

	self:RegisterProtocol(CSDiyChuanWenClientReq)
	self:RegisterProtocol(SCDiyChuanWenAllInfo, "OnSCDiyChuanWenAllInfo")
	self:RegisterProtocol(SCDiyChuanWenGradeFlagInfo, "OnSCDiyChuanWenGradeFlagInfo")
	self:RegisterProtocol(SCDiyChuanWenGradeRewardFlagInfo, "OnSCDiyChuanWenGradeRewardFlagInfo")
	self:RegisterProtocol(SCDiyChuanWenChooseDescInfo, "OnSCDiyChuanWenChooseDescInfo")
	self:RegisterProtocol(SCDiyChuanWenDiyDescInfo, "OnSCDiyChuanWenDiyDescInfo")
	self:RegisterProtocol(SCDiyChuanWenDiyDescShow, "OnSCDiyChuanWenDiyDescShow")
	self:RegisterProtocol(SCDiyChuanWenRoleSendRewad, "OnSCDiyChuanWenRoleSendRewad")
	self:RegisterProtocol(SCDiyChuanWenActiveSkin, "OnSCDiyChuanWenActiveSkin")
	self:RegisterProtocol(SCDiyChuanWenRecharge, "OnSCDiyChuanWenRecharge")
	self:RegisterProtocol(SCDiyChuanWenCloseInfo, "OnSCDiyChuanWenCloseInfo")

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function CustomizedRumorsWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil
	
	self.rumors_tip:DeleteMe()
	self.rumors_tip = nil

	self.data:DeleteMe()
	self.data = nil

	self.bullscreen_barrage_view:DeleteMe()
	self.bullscreen_barrage_view = nil

	self.rumors_set_view:DeleteMe()
	self.rumors_set_view = nil

	self.rumors_exchange_view:DeleteMe()
	self.rumors_exchange_view = nil

	self.custom_set_barrage_view:DeleteMe()
	self.custom_set_barrage_view = nil

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
	end

	CustomizedRumorsWGCtrl.Instance = nil
end

-------------------------------------protocol_start-----------------------------------------
function CustomizedRumorsWGCtrl:SendDiyChuanWenClientRequest(operate_type, param1, param2, param3, param_str)
	-- print_error("传闻请求", operate_type, param1, param2, param3, param_str)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDiyChuanWenClientReq)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param_str = param_str or 0
	protocol:EncodeAndSend()
end

function CustomizedRumorsWGCtrl:OnSCDiyChuanWenAllInfo(protocol)
	-- print_error("--------OnSCDiyChuanWenAllInfo---------", protocol)
	self.data:SetRumorAllInfo(protocol)
	RemindManager.Instance:Fire(RemindName.CustomizedRumors)
	RemindManager.Instance:Fire(RemindName.CustomizedRumorsBroadcast)
	ViewManager.Instance:FlushView(GuideModuleName.CustomizedRumorsView)
end

function CustomizedRumorsWGCtrl:OnSCDiyChuanWenGradeFlagInfo(protocol)
	-- print_error("--------OnSCDiyChuanWenGradeFlagInfo---------", protocol)
	self.data:UpdateGradeFlagInfo(protocol)
	RemindManager.Instance:Fire(RemindName.CustomizedRumorsBroadcast)
	ViewManager.Instance:FlushView(GuideModuleName.CustomizedRumorsView, TabIndex.customized_rumors_broadcast)
	self:FlushRumorsExchangeView()
end

function CustomizedRumorsWGCtrl:OnSCDiyChuanWenGradeRewardFlagInfo(protocol)
	-- print_error("--------OnSCDiyChuanWenGradeRewardFlagInfo---------", protocol)
	self.data:UpdateRewardFlagInfo(protocol)
	RemindManager.Instance:Fire(RemindName.CustomizedRumorsBroadcast)
	ViewManager.Instance:FlushView(GuideModuleName.CustomizedRumorsView, TabIndex.customized_rumors_broadcast)
	self:FlushRumorsExchangeView()
end

function CustomizedRumorsWGCtrl:OnSCDiyChuanWenChooseDescInfo(protocol)      -- 皮肤选择
	-- print_error("--------OnSCDiyChuanWenChooseDescInfo---------", protocol)
	self.data:UpdateChooseDescInfo(protocol)
	RemindManager.Instance:Fire(RemindName.CustomizedRumors)
	self:FlushRumorsSetView()
	ViewManager.Instance:FlushView(GuideModuleName.CustomizedRumorsView, TabIndex.customized_rumors)
end

function CustomizedRumorsWGCtrl:OnSCDiyChuanWenDiyDescInfo(protocol) --主动请求定制返回结果  只针对自定义
	-- print_error("--------OnSCDiyChuanWenDiyDescInfo---------", protocol)
	self.data:UpdateDiyDescInfo(protocol)
	RemindManager.Instance:Fire(RemindName.CustomizedRumors)
	self:FlushRumorsSetView()
	ViewManager.Instance:FlushView(GuideModuleName.CustomizedRumorsView, TabIndex.customized_rumors)
end

function CustomizedRumorsWGCtrl:OnSCDiyChuanWenDiyDescShow(protocol) -- 弹窗的传闻 主界面
	-- print_error("----打脸图----OnSCDiyChuanWenDiyDescShow---------", protocol)

	local desc_type = protocol.desc_type    -- 自定义的类型（1上线、2击杀、3复活）
	local role_name = protocol.role_name      --玩家名字
	local choose_skin = protocol.choose_skin   -- 档位seq
	local choose_desc = protocol.choose_desc -- -1 是自定义的   不是-1对应传闻预设内容 seq
	local diy_desc_str = protocol.diy_desc_str   --自定义内容
	local kill_role_name = protocol.kill_role_name -- 被杀人的名字
	local usid = protocol.usid   -- 杀人者服务区id
	local death_role_usid = protocol.death_role_usid  --被杀人的服务器id
	local desc_cfg = self.data:GetRumorGearShowInfo(desc_type, choose_skin)

	local my_role_name = RoleWGData.Instance:GetAttr("name")

	if my_role_name ~= role_name then
		local shield_custom_rumor = SettingWGData.Instance:GetSettingData(SETTING_TYPE.CLOSE_OTHERS_RUMOR)
		if shield_custom_rumor then
			return
		end
	end

	if not IsEmptyTable(desc_cfg) then
		local top_desc = ""
		if desc_cfg.desc_type == 2 then
			top_desc = string.format(desc_cfg.rumor_top_desc, usid.temp_low, role_name, death_role_usid.temp_low, kill_role_name)
		else
			top_desc = string.format(desc_cfg.rumor_top_desc, usid.temp_low, role_name)
		end

		local content = ""
		if choose_desc == -1 then
			content = diy_desc_str
		else
			content = self.data:GetRumorPresetContentData(desc_type, choose_skin, choose_desc).desc
		end

		local rumor_data = {
			rumor_type = desc_cfg.rumor_type,
			desc_rumors_content = top_desc .. content,   -- 对接数据后 如果定制了显示定制
			show_yoyo_tween = true,
		}
	
		self:OpenRumorsTipView(rumor_data)
	end
end

function CustomizedRumorsWGCtrl:OnSCDiyChuanWenRoleSendRewad(protocol)   -- 下发的霸屏广播
	-- print_error("--------OnSCDiyChuanWenRoleSendRewad-----下发的霸屏广播----", protocol)
	local grade_cfg = self.data:GetBroadcastGradeCfgByGrade(protocol.grade)
	local server_id = RoleWGData.Instance:GetCurServerId()
    local desc_content = string.format(grade_cfg.broadcast_content, server_id, protocol.role_name, grade_cfg.need_rmb_num)

	local reward_idx = bit:d2b_l2h(protocol.reward_idx, nil, true)
    local reward_data_list = {}
    for k, v in pairs(grade_cfg.choose_item) do
        if reward_idx[k] == 1 then
            table.insert(reward_data_list, v)
        end
    end
	local data = {}
	data.rumor_type = grade_cfg.rumor_type
	data.desc_content = desc_content
	data.reward_data_list = reward_data_list
	data.show_time = grade_cfg.show_time
	self:OpenBullScreenBarrageView(data)
end

function CustomizedRumorsWGCtrl:OnSCDiyChuanWenActiveSkin(protocol)
	-- print_error("--------OnSCDiyChuanWenActiveSkin---------", protocol)
	self.data:UpdateActiveSkin(protocol)
	RemindManager.Instance:Fire(RemindName.CustomizedRumors)
	ViewManager.Instance:FlushView(GuideModuleName.CustomizedRumorsView, TabIndex.customized_rumors)
end

function CustomizedRumorsWGCtrl:OnSCDiyChuanWenRecharge(protocol)
	-- print_error("--------OnSCDiyChuanWenRecharge---------", protocol)
	self.data:SetRMBRecharge(protocol)
	RemindManager.Instance:Fire(RemindName.CustomizedRumorsBroadcast)

	ViewManager.Instance:FlushView(GuideModuleName.CustomizedRumorsView, TabIndex.customized_rumors_broadcast)
end

-- 0 开启 1关闭
function CustomizedRumorsWGCtrl:OnSCDiyChuanWenCloseInfo(protocol)
	self.data:UpdataCloseInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.CustomizedRumorsView, TabIndex.customized_rumors)
end

-- type 1上线 2击杀  3复活  4, 5,  提交审核一个自定义传闻
function CustomizedRumorsWGCtrl:SendRumorRequest(type, content, callback)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local plat_id = CHANNEL_AGENT_ID
	local server_id = main_role_vo.server_id or 0
	local user_id = GameVoManager.Instance:GetUserVo().account_user_id
	-- local role_id = main_role_vo.origin_uid and main_role_vo.origin_uid ~= 0 and main_role_vo.origin_uid or main_role_vo.role_id
	-- local role_name = RoleWGData.Instance:GetAttr("name")
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local type = type or 1
	local content = content or ""
 	local time = os.time()
	local key = GlobalUrlSignKey
	local sign_tab = {
		content,
		plat_id,
		role_id,
		-- role_name,
		server_id,
		time,
		type,
		user_id,
		key
	}

	local sign_str = table.concat(sign_tab)
	local sign = MD52.GetMD5(sign_str)

	local real_url = string.format("%s/v1/client/add_rumor.php?plat_id=%s&server_id=%s&user_id=%s&role_id=%s&type=%s&content=%s&time=%s&sign=%s",
	GlobalUrl, plat_id, server_id, user_id, role_id, type, content, tostring(time), sign)

	-- http://192.168.0.135/a2/test/add_rumor.php
	-- local real_url = string.format("%s/a2/test/add_rumor.php?plat_id=%s&server_id=%s&user_id=%s&role_id=%s&type=%s&content=%s&time=%s&sign=%s",
    -- 'http://192.168.0.135', plat_id, server_id, user_id, role_id, type, content, tostring(time), sign)

	local time_out_time = 3
	local is_out_time = true

	local callback = function(url, is_succ, data)
		is_out_time = false
		self:CancelDelayFunc()

		if not is_succ then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.RequestFail)
			return
		end

		local info = cjson.decode(data)
		if info == nil then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.RequestFail)
			return
		end

		if nil == info.ret or info.ret ~= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.RequestFail)
			return
		end

		if callback then
			callback()
		end
	end

	HttpClient:Request(real_url, callback)

	self:CancelDelayFunc()
	self.request_delay = GlobalTimerQuest:AddDelayTimer(function()
		if is_out_time then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.RequestOffTime)
		end
	end, time_out_time)
end

function CustomizedRumorsWGCtrl:CancelDelayFunc()
	if self.request_delay then
		GlobalTimerQuest:CancelQuest(self.request_delay)
		self.request_delay = nil
	end
end
---------------------------------------protocol_end------------------------------------------

-----------------------------------特殊传闻弹出---------------------------------
function CustomizedRumorsWGCtrl:OpenRumorsTipView(data)
	if self.rumors_tip then
		self.rumors_tip:SetDataList(data)

		if not self.rumors_tip:IsOpen() then
			self.rumors_tip:Open()
		end
	end
end

function CustomizedRumorsWGCtrl:StopShowRumorsTip(close_open_func)
	if self.rumors_tip and self.rumors_tip:IsOpen() then
		self.rumors_tip:StopNextShow(close_open_func)
	end
end

function CustomizedRumorsWGCtrl:OpenBullScreenBarrageView(data)
	if self.bullscreen_barrage_view then
		self.bullscreen_barrage_view:SetDataList(data)
		
		if not self.rumors_tip:IsOpen() then
			self.bullscreen_barrage_view:Open()
		end
	end
end

function CustomizedRumorsWGCtrl:AddOneTemporaryDanMu(desc_content)
	if self.bullscreen_barrage_view and self.bullscreen_barrage_view:IsOpen() then
		self.bullscreen_barrage_view:AddOneSpecialDanMu(desc_content)
	end
end

function CustomizedRumorsWGCtrl:OpenRumorsSetView(rumors_data, type_data)
	if self.rumors_set_view then
		self.rumors_set_view:SetDataInfo(rumors_data, type_data)
		self.rumors_set_view:Open()
	end
end

function CustomizedRumorsWGCtrl:FlushRumorsSetView()
	if self.rumors_set_view then
		self.rumors_set_view:Flush()
	end
end

function CustomizedRumorsWGCtrl:OpenRumorsExchangeView(grade)
	if self.rumors_exchange_view then
		self.rumors_exchange_view:SetDefaultSelectGrade(grade)
		self.rumors_exchange_view:Open()
	end
end

function CustomizedRumorsWGCtrl:FlushRumorsExchangeView()
	if self.rumors_exchange_view then
		self.rumors_exchange_view:Flush()
	end
end

function CustomizedRumorsWGCtrl:OpenCustomizedSetBarrageView()
	if self.custom_set_barrage_view then
		self.custom_set_barrage_view:Open()
	end
end

function CustomizedRumorsWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:IsRumorActiveItem(change_item_id) then
			self.data:CalRumorRemind()
			RemindManager.Instance:Fire(RemindName.CustomizedRumors)
			ViewManager.Instance:FlushView(GuideModuleName.CustomizedRumorsView, TabIndex.customized_rumors)
		end
	end
end