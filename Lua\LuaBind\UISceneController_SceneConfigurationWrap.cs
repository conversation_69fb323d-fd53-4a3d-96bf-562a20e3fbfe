﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UISceneController_SceneConfigurationWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(UISceneController.SceneConfiguration), typeof(System.Object));
		<PERSON><PERSON>unction("New", _CreateUISceneController_SceneConfiguration);
		<PERSON><PERSON>ction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("configName", get_configName, set_configName);
		<PERSON><PERSON>("transformConfigs", get_transformConfigs, set_transformConfigs);
		<PERSON><PERSON>("activeStateConfigs", get_activeStateConfigs, set_activeStateConfigs);
		<PERSON><PERSON>("meshRendererMaterialConfigs", get_meshRendererMaterialConfigs, set_meshRendererMaterialConfigs);
		<PERSON><PERSON>("cameraConfig", get_cameraConfig, set_cameraConfig);
		<PERSON><PERSON>("environmentData", get_environmentData, set_environmentData);
		<PERSON><PERSON>("characterData", get_characterData, set_characterData);
		L.Reg<PERSON>ar("waterMats", get_waterMats, set_waterMats);
		L.RegVar("bgPanelMats", get_bgPanelMats, set_bgPanelMats);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUISceneController_SceneConfiguration(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				UISceneController.SceneConfiguration obj = new UISceneController.SceneConfiguration(arg0);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UISceneController.SceneConfiguration.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_configName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			string ret = obj.configName;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index configName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_transformConfigs(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			System.Collections.Generic.List<UISceneController.TransformConfiguration> ret = obj.transformConfigs;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index transformConfigs on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_activeStateConfigs(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			System.Collections.Generic.List<UISceneController.ActiveStateConfiguration> ret = obj.activeStateConfigs;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index activeStateConfigs on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_meshRendererMaterialConfigs(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			System.Collections.Generic.List<UISceneController.MeshRendererMaterialConfig> ret = obj.meshRendererMaterialConfigs;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index meshRendererMaterialConfigs on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cameraConfig(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			UISceneController.CameraConfiguration ret = obj.cameraConfig;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cameraConfig on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_environmentData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			SceneEnvironment.EnvironmentData ret = obj.environmentData;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index environmentData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_characterData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			SceneEnvironment.CharacterData ret = obj.characterData;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_waterMats(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			UnityEngine.Material[] ret = obj.waterMats;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index waterMats on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bgPanelMats(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			UnityEngine.Material[] ret = obj.bgPanelMats;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bgPanelMats on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_configName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.configName = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index configName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_transformConfigs(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			System.Collections.Generic.List<UISceneController.TransformConfiguration> arg0 = (System.Collections.Generic.List<UISceneController.TransformConfiguration>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UISceneController.TransformConfiguration>));
			obj.transformConfigs = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index transformConfigs on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_activeStateConfigs(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			System.Collections.Generic.List<UISceneController.ActiveStateConfiguration> arg0 = (System.Collections.Generic.List<UISceneController.ActiveStateConfiguration>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UISceneController.ActiveStateConfiguration>));
			obj.activeStateConfigs = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index activeStateConfigs on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_meshRendererMaterialConfigs(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			System.Collections.Generic.List<UISceneController.MeshRendererMaterialConfig> arg0 = (System.Collections.Generic.List<UISceneController.MeshRendererMaterialConfig>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UISceneController.MeshRendererMaterialConfig>));
			obj.meshRendererMaterialConfigs = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index meshRendererMaterialConfigs on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cameraConfig(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			UISceneController.CameraConfiguration arg0 = (UISceneController.CameraConfiguration)ToLua.CheckObject<UISceneController.CameraConfiguration>(L, 2);
			obj.cameraConfig = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cameraConfig on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_environmentData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			SceneEnvironment.EnvironmentData arg0 = (SceneEnvironment.EnvironmentData)ToLua.CheckObject<SceneEnvironment.EnvironmentData>(L, 2);
			obj.environmentData = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index environmentData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_characterData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			SceneEnvironment.CharacterData arg0 = (SceneEnvironment.CharacterData)ToLua.CheckObject<SceneEnvironment.CharacterData>(L, 2);
			obj.characterData = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_waterMats(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			UnityEngine.Material[] arg0 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 2);
			obj.waterMats = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index waterMats on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_bgPanelMats(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController.SceneConfiguration obj = (UISceneController.SceneConfiguration)o;
			UnityEngine.Material[] arg0 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 2);
			obj.bgPanelMats = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bgPanelMats on a nil value");
		}
	}
}

