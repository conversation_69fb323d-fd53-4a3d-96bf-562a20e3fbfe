-- 引导获取界面
BossMaBiSkillShowView = BossMaBiSkillShowView or BaseClass(CommonSkillShowView)

function BossMaBiSkillShowView:__init()
	self.view_layer = UiLayer.PopTop
	self:ClearViewTween()
    self:SetMaskBg(true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "skill_show_scene")
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_bossmabi_skill_show_view")
end

function BossMaBiSkillShowView:LoadCallBack()
	self:InitShower()
	self:InitEnemy()
	self:InitCameraSet()
	
	if not self.guild_list then
		self.guild_list = AsyncListView.New(MaBiGuildListItemRender, self.node_list.guild_list)
		self.guild_list:SetSelectCallBack(BindTool.Bind(self.OnBossMaBiClickItem, self))
		self.guild_list:SetDefaultSelectIndex(1)
		local mabi_skill_show_cfg = CommonSkillShowData.Instance:GetBossMaBiSkillSHowCfg()
		self.guild_list:SetDataList(mabi_skill_show_cfg)
	end

	if not self.jump_list then
		self.jump_list = AsyncListView.New(MaBiJumpListItemRender, self.node_list.jump_group_list)
		self.jump_list:SetSelectCallBack(BindTool.Bind(self.OnBossMaBiTabClickItem, self))
		self.jump_list:SetDefaultSelectIndex(nil)
		local tab_list= CommonSkillShowData.Instance:GetBossMaBiTabIndexList(1)
		self.jump_list:SetDataList(tab_list)
	end

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.list_fb_reward)

		local data_list = PersonMaBiBossWGData.Instance:GetRewardDataList()
		self.reward_list:SetDataList(data_list)
		self.reward_list:SetStartZeroIndex(true)
	end

	self.show_guild = true
	self.node_list.tips.text.text = Language.MaBiSkillShow.TipsDesc

	XUI.AddClickEventListener(self.node_list.btn_change, BindTool.Bind(self.OnClickChangeBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_enter_fb, BindTool.Bind(self.OnClickEnterFbBtn, self))

	if not self.attr_change then
		self.attr_change = BindTool.Bind(self.RoleDataChangeCallback, self)
		local attr_list = CommonSkillShowData.Instance:GetBossMaBiAttrNameList()
		if IsEmptyTable(attr_list) then
			return
		end
		RoleWGData.Instance:NotifyAttrChange(self.attr_change, attr_list)
	end

	local can_enter_fb_time = PersonMaBiBossWGData.Instance:GetCanEnterFbTime()
	self.node_list.desc_enter_fb.text.text = string.format(Language.PersonMabiBoss.EnterFbLimitTime, can_enter_fb_time)

	local attr_list = CommonSkillShowData.Instance:GetBossMaBiAttrNameList()
	if IsEmptyTable(attr_list) and not attr_list[1] then
		return
	end
	--当前显示的属性.
	self.cur_show_attr_name = attr_list[1]

	-- 设置RT
	self.render_width = self.node_list.rt_raw_image.rect.rect.width				--  RT 宽度
	self.render_height = self.node_list.rt_raw_image.rect.rect.height			--  RT 高度
	self.render_texture = UnityEngine.RenderTexture.GetTemporary(self.render_width, self.render_height, 24, UnityEngine.Experimental.Rendering.GraphicsFormat.B10G11R11_UFloatPack32)
	self.node_list.rt_raw_image.raw_image.texture = self.render_texture
	self.node_list.rt_raw_image.raw_image.enabled = true
	self.node_list.Camera.camera.targetTexture = self.render_texture
	local externalAlphaHelper = self.node_list.Camera.camera.gameObject:AddComponent(typeof(ExternalAlphaHelper))
	externalAlphaHelper.targetImage = self.node_list.rt_raw_image.raw_image.__meta__
end

function BossMaBiSkillShowView:InitCameraSet()
	local camera_ndoe = self.node_list.CameraPos.transform
	if camera_ndoe then
		camera_ndoe.localPosition = Vector3(-7, 16, -20)
		camera_ndoe.localEulerAngles = Vector3(36, 28, 8)
	end
end

function BossMaBiSkillShowView:ReleaseCallBack()
	self:ClearDelayAddMaBiBuffOpera()

	if self.guild_list then
		self.guild_list:DeleteMe()
		self.guild_list = nil
	end

	if self.jump_list then
		self.jump_list:DeleteMe()
		self.jump_list = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if self.attr_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
		self.attr_change = nil
	end

	self.render_texture = nil
	self.render_width = nil
	self.render_height = nil

	CommonSkillShowView.ReleaseCallBack(self)
end

function BossMaBiSkillShowView:ShowIndexCallBack()
	local can_enter_fb_flag = PersonMaBiBossWGData.Instance:GetCanEnterFBFlag()
	self.show_guild = not can_enter_fb_flag

	self.node_list.guild_list:CustomSetActive(self.show_guild)
	self.node_list.jump_group:CustomSetActive(self.show_guild)
	self.node_list.enter_fb_panel:CustomSetActive(can_enter_fb_flag)
end

function BossMaBiSkillShowView:OnFlush(param_t, index)
	self:InitShower()
	self:ChangeEnemyShowPos()

	self:FlushAttr()
	local can_enter_fb_flag = PersonMaBiBossWGData.Instance:GetCanEnterFBFlag()
	self.node_list.flag_experience:CustomSetActive(can_enter_fb_flag)
	self.node_list.btn_change:CustomSetActive(can_enter_fb_flag)

	if not can_enter_fb_flag then
		self.node_list.guild_list:CustomSetActive(true)
		self.node_list.jump_group:CustomSetActive(true)
		self.node_list.enter_fb_panel:CustomSetActive(false)
	end
end

function BossMaBiSkillShowView:InitEnemy()
	if nil ~= self.enemy_obj then
		return
	end

	self.is_enemy_loaded = false
	local enemy_vo = RoleVo.New()
	enemy_vo.name = ""
	enemy_vo.level = 1
	enemy_vo.sex = 1
	enemy_vo.prof = 1
	enemy_vo.max_hp = 10
	enemy_vo.hp = 10
	enemy_vo.special_res_id = 8002 -- 8101001
	self.enemy_obj = SkillShowerMonster.New(enemy_vo)
	local draw_obj = self.enemy_obj:GetDrawObj()
	draw_obj:SetScale(1, 1, 1)
	self.enemy_obj:SetModelLoadCallback(function ()
		self.is_enemy_loaded = true
		self:TryPlaySkillAttack()
	end)

	local draw_obj_trans = draw_obj:GetTransfrom()
	draw_obj_trans:SetParent(self.node_list["enemy_pos"].gameObject.transform)
	draw_obj_trans.localPosition = u3dpool.vec3(0, 0, 0)
	draw_obj_trans.localRotation = Quaternion.Euler(0, -30, 0)
	Transform.SetLocalScaleXYZ(self.node_list["enemy_pos"].transform, 1.8, 1.8, 1.8)
end

function BossMaBiSkillShowView:ChangeEnemyShowPos()
	Transform.SetLocalPosition(self.node_list["enemy_pos"].transform, u3dpool.vec3(-2, -2, 0))
end

function BossMaBiSkillShowView:InitShower()
    if nil ~= self.shower_obj then
		self.shower_obj:DeleteMe()
	end

	self.is_shower_loaded = false
	local role_vo = RoleWGData.Instance:GetRoleVo()
	local shower_vo = RoleVo.New()
	shower_vo.name = ""
	shower_vo.level = 1
	shower_vo.sex = role_vo.sex or 1
	shower_vo.prof = role_vo.prof or 1
	shower_vo.max_hp = 10
	shower_vo.hp = 10
	shower_vo.shenwu_appeid = role_vo.shenwu_appeid
	shower_vo.appearance = ProtocolStruct.RoleAppearance()
	shower_vo.appearance.fashion_body = role_vo.appearance.fashion_body
	shower_vo.appearance.fashion_wuqi = role_vo.appearance.fashion_wuqi

	local shower_obj = SkillShower.New(shower_vo)
	shower_obj:SetEnemyPos(self.node_list["shower_pos"].gameObject)
	shower_obj:SetModelLoadCallback(function ()
		self.is_shower_loaded = true
		self:TryPlaySkillAttack()
	end)

	shower_obj:SetAttackHitCallback(BindTool.Bind(self.AttackHitCallback, self))

	self.shower_obj = shower_obj
	local draw_obj = shower_obj:GetDrawObj()
	draw_obj:SetScale(1, 1, 1)
	local draw_obj_trans = draw_obj:GetTransfrom()
	draw_obj_trans:SetParent(self.node_list["shower_pos"].gameObject.transform)
	draw_obj_trans.localPosition = u3dpool.vec3(0, 0, 0)
	draw_obj_trans.localRotation = Quaternion.Euler(0, 0, 0)

	Transform.SetLocalPosition(self.node_list["shower_pos"].transform, u3dpool.vec3(-9, -2, 1))
end

function BossMaBiSkillShowView:TryPlaySkillAttack()
	if not self.is_shower_loaded or not self.is_enemy_loaded then
		return
	end

	self:ClearDelayLoopPlaySkillAttackTimer()
	self:ClearDelayDoFuncTimer()
	self:ClearSpeclaiSkillEffect()
	self:ClearBuffEffect()

	local loop_time = 8
    self:DelayDoFunc(3)

	self.delay_loop_skill_attack_timer = GlobalTimerQuest:AddTimesTimer(function ()
		self:TryPlaySkillAttack()
	end, loop_time, 1)
end

function BossMaBiSkillShowView:DelayDoFunc(time)
	self.delay_do_func_timer = GlobalTimerQuest:AddTimesTimer(function ()
		self:TryDoGeneralAttack()
	end, time or 0, 1)
end

function BossMaBiSkillShowView:SimulateGeneralAttackOpera()
	self:ClearDelaySimulateGeneralAttackOpera()
	if IsEmptyTable(self.general_attack_list) then
		return
	end

	local data = self.general_attack_list[self.general_attack_index] or {}
	local skill_id = data.skill_id
	--如果是麻痹界面，则添加麻痹buff.
	if not skill_id and self.cur_show_attr_name == "boss_palsy_per" then
		self:TryAddMaBiBuffToMonster()
		return
	end

	self.general_attack_index = self.general_attack_index + 1
	self:SimulateSingleGeneralAttackOpera(skill_id, self.general_attack_index)
	local do_next_action_time = self:GetRoleSkillTime(skill_id, self.general_attack_index)
	
	if self.enemy_obj then
		self.enemy_obj:CheckMaBi(BUFF_FLUSH_REASON.REMOVE)
	end

    if do_next_action_time > 0 then
		self.general_attack_timer = GlobalTimerQuest:AddTimesTimer(function ()
			self:SimulateGeneralAttackOpera()
		end, do_next_action_time, 1)
	end
end

function BossMaBiSkillShowView:TryAddMaBiBuffToMonster()
	self:ClearDelayAddMaBiBuffOpera()

	self.add_mabi_buff_timer = GlobalTimerQuest:AddTimesTimer(function ()
		-- 策划觉得配置表5s太长， 要求时间调短为3s
		local cur_time = TimeWGCtrl.Instance:GetServerTime()
		local buff_delay_time = 3
		if self.enemy_obj then
			self.enemy_obj:AddBuff(16, nil, cur_time + buff_delay_time)
		end
	end, 0.2, 1)
end

function BossMaBiSkillShowView:ClearDelayAddMaBiBuffOpera()
	if self.add_mabi_buff_timer then
        GlobalTimerQuest:CancelQuest(self.add_mabi_buff_timer)
        self.add_mabi_buff_timer = nil
    end
end

function BossMaBiSkillShowView:OnBossMaBiClickItem(item)
	if not item.data then
		return
	end

	self.node_list.title_text.text.text = item.data.title or ""
	self.node_list.desc_text.text.text = item.data.desc or ""

	self.node_list.attr_desc_text.text.text = item.data.attr_desc or ""

	self.cur_show_attr_name = item.data.attr_name
	self:FlushAttr()

	if self.cur_show_attr_name ~= "boss_palsy_per" then
		self:ClearDelayAddMaBiBuffOpera()
		self.enemy_obj:RemoveBeautyBuff(16)
	end

	local tab_list = CommonSkillShowData.Instance:GetBossMaBiTabIndexList(item.data.index)
	self.jump_list:SetDataList(tab_list)
end

function BossMaBiSkillShowView:OnBossMaBiTabClickItem(item)
	if not item.data then
		return
	end

	local tab_cfg = CommonSkillShowData.Instance:GetBossMaBiTabCfg(tonumber(item.data))
	if IsEmptyTable(tab_cfg) then
		return
	end

	self:Close()
	FunOpen.Instance:OpenViewNameByCfg(tab_cfg.open_view)
end

function BossMaBiSkillShowView:OnClickChangeBtn()
	self.show_guild = not self.show_guild
	self.node_list.guild_list:CustomSetActive(self.show_guild)
	self.node_list.jump_group:CustomSetActive(self.show_guild)
	self.node_list.enter_fb_panel:CustomSetActive(not self.show_guild)
end

function BossMaBiSkillShowView:OnClickEnterFbBtn()
	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.PERSON_MABIBOSS_FB)
	self:Close()
end

function BossMaBiSkillShowView:RoleDataChangeCallback(key, value)
	local attr_list = CommonSkillShowData.Instance:GetBossMaBiAttrNameList()
	if IsEmptyTable(attr_list) then
		return
	end

	for k, v in pairs(attr_list) do
		if key == v then
			self:FlushAttr()
		end
	end
end

function BossMaBiSkillShowView:FlushAttr()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if not main_role_vo then
		return
	end

	local is_show_lv_icon = false

	local attr_value = main_role_vo[self.cur_show_attr_name] or 0
	if self.cur_show_attr_name == "level" then
		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(attr_value)
		attr_value = role_level

		is_show_lv_icon = is_vis
	end
	self.node_list.attr_icon:SetActive(is_show_lv_icon)
	
	if self.node_list.attr_text then
		local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(self.cur_show_attr_name)
		local can_tiyan = PersonMaBiBossWGData.Instance:GetCanEnterFBFlag()
		local value = is_per and attr_value / 100 or attr_value
		local per_desc = is_per and "%" or ""
		local cur_desc = value .. per_desc
		local target_value_str = can_tiyan and cur_desc .. "+100%" or cur_desc
		self.node_list.attr_text.text.text = target_value_str
	end
end

-----------------------------MaBiGuildListItemRender------------------------------
MaBiGuildListItemRender = MaBiGuildListItemRender or BaseClass(BaseRender)

function MaBiGuildListItemRender:OnFlush()
	if not self.data then
		return
	end

	local bundle, asset = ResPath.GetRawImagesPNG("a3_boss_btn_" .. self.data.bg)
	self.node_list.bg.raw_image:LoadSprite(bundle, asset, function ()
		self.node_list.bg.raw_image:SetNativeSize()
	end)
end

function MaBiGuildListItemRender:OnSelectChange(is_select)
	if is_select then
		self.node_list.img_select:SetActive(true)
	else
		self.node_list.img_select:SetActive(false)
	end
end

-----------------------------MaBiJumpListItemRender------------------------------
MaBiJumpListItemRender = MaBiJumpListItemRender or BaseClass(BaseRender)

function MaBiJumpListItemRender:LoadCallBack()
	self.view:SetActive(true)
end

function MaBiJumpListItemRender:OnFlush()
	if not self.data then
		return
	end

	local tab_cfg = CommonSkillShowData.Instance:GetBossMaBiTabCfg(tonumber(self.data))
	if IsEmptyTable(tab_cfg) then
		return
	end

	self.node_list.icon_image.image:LoadSprite(ResPath.GetSkillShowImg("a3_bossmb_icon_" .. tab_cfg.icon))

	-- self.node_list.title_image.image:LoadSprite(ResPath.GetSkillShowImg("a2_bossmb_text_" .. tab_cfg.icon))

	self.node_list.title_text.text.text = tab_cfg.title or ""
end