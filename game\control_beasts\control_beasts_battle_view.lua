local BATTLE_MODEL_SHOW_POS = {
    [1] = Vector3(0.6, 0.3, -5),
    [2] = Vector3(-3.2, 0.3, 0),
    [3] = Vector3(4.5, 0.3, 0),
}

function ControlBeastsWGView:LoadBattleViewCallBack()
    if not self.main_battle_render_list then
        self.main_battle_render_list = {}

        for i = 1, 3 do
            local battle_render_obj = self.node_list.main_battle_render_list:FindObj(string.format("battle_render_0%d", i))
            if battle_render_obj then
                local cell = BeststsBattleItemRender.New(battle_render_obj)
                cell:SetBattleModelParent(self, true)
                cell:SetIndex(i)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectMainCB, self))
                self.main_battle_render_list[i] = cell
            end
        end
    end

    -- 初始化6辅战
	if not self.sub_battle_render_list then
        self.sub_battle_render_list = {}

        for i = 1, 6 do
            local cell_obj = self.node_list.sub_battle_render_list:FindObj(string.format("sub_battle_render_list_grid/battle_render_0%d", i))
            if cell_obj then
                local cell = BeststsBattleSubItemRender.New(cell_obj)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectAssistCB, self))
                cell:SetIndex(i)
                self.sub_battle_render_list[i] = cell
            end
        end
	end

    XUI.AddClickEventListener(self.node_list.beasts_all_attr_show_btn, BindTool.Bind2(self.BeastsAllAttrShowClick, self))               -- 属性详情
    XUI.AddClickEventListener(self.node_list.beasts_battle_gm_btn, BindTool.Bind2(self.OpenBeastElementClick, self))                    -- 属性详情
    XUI.AddClickEventListener(self.node_list.beasts_king_btn, BindTool.Bind2(self.OpenBeastKingClick, self))                            -- 兽王点击
end

function ControlBeastsWGView:OpenBattleViewCallBack()
end

function ControlBeastsWGView:CloseBattleViewCallBack()
end

function ControlBeastsWGView:ShowBattleViewCallBack()
end

function ControlBeastsWGView:ReleaseBattleViewCallBack()
	if self.main_battle_render_list and #self.main_battle_render_list > 0 then
		for _, render_cell in ipairs(self.main_battle_render_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.main_battle_render_list = nil
	end

    if self.sub_battle_render_list and #self.sub_battle_render_list > 0 then
		for _, render_cell in ipairs(self.sub_battle_render_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.sub_battle_render_list = nil
	end

    self.now_active_gm_type = nil
    self.old_fetters_type = nil
    self.old_type_count = nil
    self.old_extra_type = nil
end
-----------------------------------------------------------------------------
function ControlBeastsWGView:OnSelectMainCB(beasts_item)
	if nil == beasts_item or nil == beasts_item.data then
		return
	end

    local last_unlock = ControlBeastsWGData.Instance:GetLastHoleLockStatusByHoleId(beasts_item.data.hole_id)
    if not last_unlock then
        local str = string.format(Language.ContralBeasts.ErrorTip21, string.format(Language.ContralBeasts.HoleTips1, NumberToChinaNumber(beasts_item.data.hole_id)))
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    if beasts_item.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE then
        local str = Language.ContralBeasts.HoleTips1

        local hole_data = {
            title_name = string.format(str, NumberToChinaNumber(beasts_item.index)),
            hole_id = beasts_item.data.hole_id,
        }

        ControlBeastsWGCtrl.Instance:OpenHoleTipsView(hole_data)
    else
        local is_change = beasts_item.data.beasts_bag_id ~= -1
        local battle_list = ControlBeastsWGData.Instance:GetBattleBeastsList(is_change, beasts_item.index, beasts_item.data.beasts_bag_id)
        ControlBeastsWGCtrl.Instance:OpenBeastsBattleSelectView(battle_list)
    end
end


function ControlBeastsWGView:OnSelectAssistCB(beasts_item)
	if (not beasts_item) or (not beasts_item.data) then
		return
	end

    local last_unlock = ControlBeastsWGData.Instance:GetLastHoleLockStatusByHoleId(beasts_item.data.hole_id)
    if not last_unlock then
        local str = string.format(Language.ContralBeasts.ErrorTip21, string.format(Language.ContralBeasts.HoleTips2, NumberToChinaNumber(beasts_item.index - 1)))
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    local cell_index = beasts_item.index
    if beasts_item.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE then
        local str = Language.ContralBeasts.HoleTips2

        local hole_data = {
            title_name = string.format(str, NumberToChinaNumber(beasts_item.index)),
            hole_id = beasts_item.data.hole_id,
        }

        ControlBeastsWGCtrl.Instance:OpenHoleTipsView(hole_data)
    else
        local is_change = beasts_item.data.beasts_bag_id ~= -1
        local hole_index = cell_index + 3
        local battle_list = ControlBeastsWGData.Instance:GetBattleBeastsList(is_change, hole_index, beasts_item.data.beasts_bag_id)
        ControlBeastsWGCtrl.Instance:OpenBeastsBattleSelectView(battle_list)
    end
end

-----------------------------------------------------------------------------
function ControlBeastsWGView:FlushBattleViewCallBack(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
            -- 独立出来方便刷新
            -- 刷新出战三个
            self:FlushMainBattleItem()
            -- 刷新辅战三个
            self:FlushAssistBattleItem()
            -- 刷新战斗力共鸣效果
        elseif k == "main" then
            self:FlushMainBattleItem()
        elseif k == "assist" then
            self:FlushAssistBattleItem()
        end
    end

    self:FlushBeastsGMPower()
    self:FlushBeastsKing()
end

-- 刷新主战位
function ControlBeastsWGView:FlushMainBattleItem()
    local main_list_data = ControlBeastsWGData.Instance:GetHoleMainData()
    if main_list_data and self.main_battle_render_list then   -- 设置三主战位的数据
        for i, battle_cell in ipairs(self.main_battle_render_list) do
            if battle_cell and main_list_data[i] then
                battle_cell:SetData(main_list_data[i])
            end
        end
    end
end

-- 刷新辅战位
function ControlBeastsWGView:FlushAssistBattleItem()
    local assist_list_data = ControlBeastsWGData.Instance:GetHoleAssistData()

    for i, battle_cell in ipairs(self.sub_battle_render_list) do
        if battle_cell and assist_list_data[i] then
            battle_cell:SetData(assist_list_data[i])
        end
    end
end

-- 刷新战斗力共鸣效果
function ControlBeastsWGView:FlushBeastsGMPower()
    local main_list_data = ControlBeastsWGData.Instance:GetHoleMainData()
    local assist_list_data = ControlBeastsWGData.Instance:GetHoleAssistData()  
    -- 刷新战斗力
    local all_cap = 0
    local battle_list = {}
    local is_main_battle_red = false
    local is_sub_battle_red = false

    -- 主战位
    for _, main_data in ipairs(main_list_data) do
        if main_data.red then
            is_main_battle_red = true
        end

		if main_data.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(main_data.beasts_bag_id)
            if beast_data then
                local cap, _ =  ControlBeastsWGData.Instance:GetBeastsCapValue(beast_data.server_data, nil, nil, main_data.hole_id, main_data.hole_level)
                all_cap = all_cap + cap

                local server_data = beast_data.server_data
                local beast_id = server_data.beast_id
                local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
                table.insert(battle_list, beast_cfg.fetters_type)
            end
        else
            table.insert(battle_list, 0)
        end
    end

    -- 辅战位
    for _, assist_data in ipairs(assist_list_data) do
        if assist_data.red then
            is_sub_battle_red = true
        end

        if assist_data.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(assist_data.beasts_bag_id)
            if beast_data then
                local cap, _ =  ControlBeastsWGData.Instance:GetBeastsCapValue(beast_data.server_data, nil, nil, assist_data.hole_id, assist_data.hole_level)
                all_cap = all_cap + cap
            end
        end
    end

    local type_count_table = {}     -- 基础类型数量
    local extra_type_count = 0      -- 特殊类型数量
    local now_type = 0              -- 当前的类型
    local next_type = 0             -- 下一个类型
    local extra_type = 0            -- 当前的特殊类型
    local fetter_data = nil
    local fetter_type = 0
    
    for i, fetters_type in ipairs(battle_list) do
        local type_value = fetters_type % 10                    -- 取余10表示当前的具体类型
        local type_extra_value = math.floor(fetters_type / 10)        -- 除以10表示当前特殊类型

        self.node_list[string.format("beasts_battle_gm_slot_%d", i)]:CustomSetActive(type_value ~= 0)
        local next_index = i + 1

        if next_index > #battle_list then
            next_index = 1
        end

        next_type = battle_list[next_index]
        self.node_list[string.format("beasts_battle_gm_slot_image_%d", i)]:CustomSetActive(type_value ~= 0 and next_type ~= 0 and next_type == type_value)

        if type_value ~= 0 then
            local slot_str = string.format("a3_hs_gm_type_%d", type_value)
            local slot_di_str = string.format("a3_hs_gm_di_%d_%d", type_value, i)

            local bundle, asset = ResPath.GetControlBeastsImg(slot_di_str)
            self.node_list[string.format("beasts_battle_gm_slot_image_%d", i)].image:LoadSprite(bundle, asset)

            local bundle, asset = ResPath.GetControlBeastsImg(slot_str)
            self.node_list[string.format("beasts_battle_gm_slot_%d", i)].image:LoadSprite(bundle, asset)

            if BEAST_EFFECT_TYPE[type_value] then
                bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_TYPE[type_value])
                self.node_list[string.format("beasts_battle_gm_slot_%d", i)]:ChangeAsset(bundle, asset)
            end

            -- 这里记录规则，当前记录为一个出现下一个直接替换，如果为两个则取两个不做替换
            if type_count_table[type_value] then
                type_count_table[type_value] = type_count_table[type_value] + 1
            else
                type_count_table[type_value] = 1
            end

            local temp_fetter_data = ControlBeastsWGData.Instance:GetFetterCfgByTypeNumber(type_value, type_count_table[type_value])
            if temp_fetter_data ~= nil then
                fetter_type = now_type
                fetter_data = temp_fetter_data
            end
        end

        if type_extra_value ~= 0 then
            if extra_type ~= type_extra_value then
                extra_type = type_extra_value
                extra_type_count = 1
            else
                extra_type_count = extra_type_count + 1
            end
        end
    end

    self.node_list.beasts_battle_cap_value.text.text = all_cap
    self.node_list.beasts_battle_gm_image:CustomSetActive(true)
    -- 长度达到条件激活共鸣
    if fetter_data ~= nil then
        local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_gm_zi_%d", fetter_type))
        self.node_list.beasts_battle_gm_image.image:LoadSprite(bundle, asset)
        self.now_active_gm_type = fetter_type

        if self.old_fetters_type ~= nil and self.old_type_count ~= nil then
            if self.old_fetters_type ~= fetter_data.fetters_type or self.old_type_count ~= fetter_data.type_num then
                ControlBeastsWGCtrl.Instance:OpenBeastElementActiveView(fetter_data.fetters_type, fetter_data.type_num)
            end
        end

        self.old_fetters_type = fetter_data.fetters_type
        self.old_type_count = fetter_data.type_num
    else
        local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_gm_zi_%d", 0))
        self.node_list.beasts_battle_gm_image.image:LoadSprite(bundle, asset)
        self.now_active_gm_type = fetter_type

        self.old_fetters_type = 0
        self.old_type_count = 0
    end

    -- 展示特殊类型
    if extra_type_count == #battle_list then
        local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_gm_zi_%d", extra_type))
        self.node_list.beasts_battle_gm_image.image:LoadSprite(bundle, asset)
        self.now_active_gm_type = extra_type

        for i, _ in ipairs(battle_list) do
            self.node_list[string.format("beasts_battle_gm_slot_%d", i)]:CustomSetActive(true)
            self.node_list[string.format("beasts_battle_gm_slot_image_%d", i)]:CustomSetActive(true)
            local slot_str = string.format("a3_hs_gm_type_%d", extra_type)
            local slot_di_str = string.format("a3_hs_gm_di_%d_%d", extra_type, i)
    
            local bundle, asset = ResPath.GetControlBeastsImg(slot_di_str)
            self.node_list[string.format("beasts_battle_gm_slot_image_%d", i)].image:LoadSprite(bundle, asset)

            bundle, asset = ResPath.GetControlBeastsImg(slot_str)
            self.node_list[string.format("beasts_battle_gm_slot_%d", i)].image:LoadSprite(bundle, asset)

            if BEAST_EFFECT_TYPE[extra_type] then
                bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_TYPE[extra_type])
                self.node_list[string.format("beasts_battle_gm_slot_%d", i)]:ChangeAsset(bundle, asset)
            end
        end

        if self.old_extra_type == 0 then
            ControlBeastsWGCtrl.Instance:OpenBeastElementActiveView(extra_type, extra_type_count)
        end

        self.old_extra_type = extra_type
    else
        self.old_extra_type = 0
    end
end

-- 兽王属性
function ControlBeastsWGView:FlushBeastsKing()
    local beast_base_info = ControlBeastsWGData.Instance:GetBeastBaseInfo()

    if not beast_base_info then
        return
    end

    local now_king_lv = beast_base_info.beast_king_level
    local cfg_data = ControlBeastsWGData.Instance:GetBeastKingDataBylevel(now_king_lv)
    self.node_list.beasts_king_btn_red:CustomSetActive(false)

    if cfg_data then
        local list, is_can_unlock = ControlBeastsWGData.Instance:SetBeastKingConditionData(cfg_data)
        if IsEmptyTable(list) then
            self.node_list.beasts_king_btn_name.text.text = Language.Skill.MaxLevel
        else
            self.node_list.beasts_king_btn_name.text.text = list and list[1] and list[1].desc or ""
        end

        self.node_list.beasts_king_btn_red:CustomSetActive(is_can_unlock)
    end
end

-- 引导切换出战幻兽
function ControlBeastsWGView:OperateChangeBattleSlotOne()
    if (not self.main_battle_render_list) or (not self.main_battle_render_list[1]) then
        return
    end

    local beasts_item = self.main_battle_render_list[1]
    self:OnSelectMainCB(beasts_item)
end

----------------------------点击方法---------------------------------------------
-- 切换培养页签
function ControlBeastsWGView:ChangeBattleShowRoot(show_type)
    if self.cur_battle_select_type == show_type then
        return
    end

    self.cur_battle_select_type = show_type
    self:FlushBattleButtonState()
end

function ControlBeastsWGView:OpenBeastElementClick()
    ControlBeastsWGCtrl.Instance:OpenBeastElementView(self.now_active_gm_type)
end

-- 打开兽王属性
function ControlBeastsWGView:OpenBeastKingClick()
    ControlBeastsWGCtrl.Instance:OpenBeastsKingView()
end

-- 当前上阵的所有属性和
function ControlBeastsWGView:BeastsAllAttrShowClick()
    --当前上阵的所有属性和
    -- print_error("当前上阵的所有属性和窗口")
    ControlBeastsWGCtrl.Instance:OpenBeastsBattleAttrView()
end
----------------------------------灵兽主战出战位item-----------------------
BeststsBattleItemRender = BeststsBattleItemRender or BaseClass(BaseRender)
function BeststsBattleItemRender:SetBattleModelParent(parent_view, is_main_ballte)
    self.parent_view = parent_view
    self.is_main_ballte = is_main_ballte

    -- 模型展示
	if (not self.show_model) and self.parent_view then
		self.show_model = RoleModel.New()
		self.show_model:SetUISceneModel(self.node_list["ph_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self.parent_view:AddUiRoleModel(self.show_model, TabIndex.beasts_battle)
	end
end

function BeststsBattleItemRender:LoadCallBack()
    if not self.star_list then
		self.star_list = {}
		for i = 1, 5 do
			self.star_list[i] = self.node_list["star" .. i]
		end
	end

    if self.node_list.retreat_beast_btn then
        XUI.AddClickEventListener(self.node_list.retreat_beast_btn, BindTool.Bind2(self.RetreatBeast, self))
    end

    self.toggle_status = false
    self.show_status = false
end

function BeststsBattleItemRender:__delete()
    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    self.old_beasts_bag_id = nil
    self.star_list = nil
    self.model_res_id = nil
    self.parent_view = nil
    self.is_main_ballte = nil
    self.toggle_status = nil
    self.show_status = nil
end

function BeststsBattleItemRender:OnFlush()
    if not self.data then 
        return 
    end

    self.node_list.ph_display:CustomSetActive(self.data.beasts_bag_id ~= -1)
    self.node_list.lock:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)
    self.node_list.add_btn:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.ACTIVE and self.data.beasts_bag_id == -1)
    self.node_list.remind:CustomSetActive(self.data.beasts_bag_id == -1 and self.data.red)
    self.node_list.up_root:CustomSetActive(self.data.beasts_bag_id == -1)
    self.node_list.beasts_show_root:CustomSetActive(self.data.beasts_bag_id ~= -1)
    self.node_list.beasts_show_red:CustomSetActive(self.data.red)
    self.node_list.lock_tip_root:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)

    -- 存在上阵，展示
    self.show_model:SetVisible(self.data.beasts_bag_id ~= -1)
    if self.data.beasts_bag_id ~= -1 then
        local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self.data.beasts_bag_id)
        if beast_data and beast_data.server_data then
            -- 展示模型
            local server_data = beast_data.server_data
            local beast_id = server_data.beast_id
            local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(beast_id, server_data.use_skin)
            if self.model_res_id ~= res_id and self.show_model ~= nil then
                self.model_res_id = res_id
                local bundle, asset = ResPath.GetBeastsModel(res_id)
                
                self.show_model:SetMainAsset(bundle, asset)
                self.show_model:SetUSAdjustmentNodeLocalScale(0.85)
                local pos_index = self.is_main_ballte and self.index or self.index + 3
                local pos = BATTLE_MODEL_SHOW_POS[pos_index]
                self.show_model:SetUSAdjustmentNodeLocalPosition(pos.x, pos.y, pos.z)
            end

            -- 展示其他数据
            local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
            if beast_cfg then
                local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
                self.node_list.active_jingshen_img.image:LoadSprite(bundle, asset, function()
                    self.node_list.active_jingshen_img.image:SetNativeSize()
                end)

                if BEAST_EFFECT_COLOR[beast_cfg.beast_color] then
                    bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[beast_cfg.beast_color])
                    self.node_list.active_jingshen_img:ChangeAsset(bundle, asset)
                end
        
                local star_res_list = GetSpecialStarImgResByStar3(beast_cfg.beast_star)
                local no_have_star = "a3_ty_xx_zc0"
                for k,v in pairs(self.star_list) do
                    v:CustomSetActive(star_res_list[k] ~= no_have_star)
                    v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
                end
        
                local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
                self.node_list.beasts_element_img.image:LoadSprite(bundle, asset, function()
                    self.node_list.beasts_element_img.image:SetNativeSize()
                end)
                
                self.node_list.beasts_name.text.text = beast_cfg.beast_name
                self.node_list.beasts_level.text.text = server_data.beast_level
            end
        end
    else
        -- 展示解锁条件
        -- 展示解锁条件
        local last_unlock = ControlBeastsWGData.Instance:GetLastHoleLockStatusByHoleId(self.data.hole_id)
        self.node_list.lock_tip_root:CustomSetActive(last_unlock and self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)

        if last_unlock then
            local is_can_unlock, hole_data = ControlBeastsWGData.Instance:GetHoleStatusData(self.data.hole_id)
            if is_can_unlock then
                self.node_list.condition_text.text.text = ToColorStr(Language.ContralBeasts.HoleTips19, COLOR3B.D_GREEN)
            else
                if hole_data then
                    local num_color = is_can_unlock and COLOR3B.D_GREEN or COLOR3B.RED
                    local new_str = string.format(hole_data.str, ToColorStr(hole_data.now, num_color), hole_data.aim)
                    self.node_list.condition_text.text.text = new_str
                else
                    self.node_list.condition_text.text.text = ""
                end
            end
        end
    end
end

-- 下阵
function BeststsBattleItemRender:RetreatBeast()
    local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self.data.beasts_bag_id)
    if beast_data and beast_data.server_data then
        ControlBeastsWGCtrl.Instance:SendOperateTypeRetreatBeast(beast_data.server_data.stand_by_slot)
    end
end

----------------------------------灵兽辅战出战位item-----------------------
BeststsBattleSubItemRender = BeststsBattleSubItemRender or BaseClass(BaseRender)
function BeststsBattleSubItemRender:LoadCallBack()
    if not self.beast_item then
        self.beast_item = ItemCell.New(self.node_list.ph_display)
        self.beast_item:SetIsShowTips(false)
        self.beast_item:SetClickCallBack(BindTool.Bind(self.OnClick, self))
    end
end

function BeststsBattleSubItemRender:OnClick()
	BaseRender.OnClick(self)
end

function BeststsBattleSubItemRender:__delete()
    if self.beast_item then
        self.beast_item:DeleteMe()
        self.beast_item = nil
    end
end

function BeststsBattleSubItemRender:OnFlush()
    if not self.data then 
        return 
    end

    self.node_list.ph_display:CustomSetActive(self.data.beasts_bag_id ~= -1)
    self.node_list.lock:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)
    self.node_list.add_btn:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.ACTIVE and self.data.beasts_bag_id == -1)
    self.node_list.remind:CustomSetActive(self.data.red)
    self.node_list.up_root:CustomSetActive(self.data.beasts_bag_id == -1)

    -- 存在上阵，展示
    if self.data.beasts_bag_id ~= -1 then
        local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self.data.beasts_bag_id)
        self.beast_item:SetData(beast_data)
    else
        -- 展示解锁条件
        local last_unlock = ControlBeastsWGData.Instance:GetLastHoleLockStatusByHoleId(self.data.hole_id)
        self.node_list.condition_text:CustomSetActive(last_unlock and self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)
        self.node_list.other_lock_tips:CustomSetActive(last_unlock and self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)

        if last_unlock then
            local is_can_unlock, hole_data = ControlBeastsWGData.Instance:GetHoleStatusData(self.data.hole_id)
            if is_can_unlock then
                self.node_list.condition_text.text.text = ToColorStr(Language.ContralBeasts.HoleTips19, COLOR3B.D_GREEN)
            else
                if hole_data then
                    local num_color = is_can_unlock and COLOR3B.D_GREEN or COLOR3B.RED
                    local new_str = string.format(hole_data.str, ToColorStr(hole_data.now, num_color), hole_data.aim)
                    self.node_list.condition_text.text.text = new_str
                else
                    self.node_list.condition_text.text.text = ""
                end
            end
        end
    end
end

