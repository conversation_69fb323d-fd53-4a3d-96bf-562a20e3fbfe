FakeTianShenFbSceneLogic = FakeTianShenFbSceneLogic or BaseClass(CommonFbLogic)

function FakeTianShenFbSceneLogic:__init()
    self.obj_dead_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnMonsterDead, self))
end

function FakeTianShenFbSceneLogic:__delete()
    if self.obj_dead_event then
        GlobalEventSystem:UnBind(self.obj_dead_event)
        self.obj_dead_event = nil
    end
end

function FakeTianShenFbSceneLogic:Enter(old_scene_type, new_scene_type)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
     MainuiWGCtrl.Instance:FlushView(0,"hide_bs_btn")
    self.story_view = StoryView.New(GuideModuleName.StoryView)
    self.story_view:SetOpenCallBack(function ()
        local step_list_cfg = ConfigManager.Instance:GetAutoConfig("story_auto").tianshen_fb_story
        if nil ~= step_list_cfg then
            RobertManager.Instance:Start()
            self.story = Story.New(step_list_cfg, self.story_view)
            self.story:SetTrigger(S_STEP_TRIGGER.ENTER_SCENE)
        end
    end)
    self.story_view:Open()

    local ctrl = MainuiWGCtrl.Instance
    ctrl:AddInitCallBack(nil, function()
        ctrl:SetTaskContents(false)
        ctrl:SetOtherContents(true)
        ctrl:SetFBNameState(true, Scene.Instance:GetSceneName())
        ctrl:SetTeamBtnState(true)
        FuBenPanelWGCtrl.Instance:OpenTianShenLogicView()
    end)

    local cfg = FuBenPanelWGData.Instance:GetTianShenFirstLayerSceneLogicCfg()
    if nil == cfg then
    	return
    end

  --   local other_cfg = FuBenPanelWGData.Instance:GetTianShenOtherCfg()
  --   FuBenPanelWGCtrl.Instance:OpenStarAniView({time3 = cfg.star_time_3, time2 = cfg.star_time_2, time1 = cfg.star_time_1,
		-- per1 = other_cfg.star1, per2 = other_cfg.star2, per3 = other_cfg.star3, str = Language.Boss.StarAniStr,})
	Scene.SendGetAllObjMoveInfoReq()

    UiInstanceMgr.Instance:DoFBStartDown(TimeWGCtrl.Instance:GetServerTime() + 5, function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
end

function FakeTianShenFbSceneLogic:Out(old_scene_type, new_scene_type)
    CommonFbLogic.Out(self, old_scene_type, new_scene_type)
    FuBenPanelWGCtrl.Instance:CloseTianShenLogicView()
    MainuiWGCtrl.Instance:SetTaskContents(true)
    -- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    FuBenPanelWGCtrl.Instance:CloseStarAniView()
    UiInstanceMgr.Instance:ColseFBStartDown()
	ViewManager.Instance:CloseAll()
    FuBenWGCtrl.Instance:CheckLeaveOpenView(old_scene_type, TabIndex.fubenpanel_tianshen)
    RobertManager.Instance:SetPause(false)
    if nil ~= self.story then
        self.story:DeleteMe()
        self.story = nil
    end

    if nil ~= self.story_view then
        self.story_view:Close()
        self.story_view:DeleteMe()
        self.story_view = nil
    end

    FunctionGuide.Instance:EndGuide()
end

function FakeTianShenFbSceneLogic:OnMonsterDead(obj)
    if obj:IsMonster() then
    	FuBenWGData.Instance:OnFbSceneLogicInfo({kill_boss_num = 1})
    	ViewManager.Instance:FlushView(GuideModuleName.TianShenLogicView)
    end
    FuBenWGCtrl.Instance:SetOutFbTime(0, true, true) --结算的时候，这里传一个0进去，为了隐藏退出按钮下的倒计时
	if obj:IsMonster() then
		local cur_star_num = 3--FuBenPanelWGCtrl.Instance:GetCurStarNum()
		local reward_items = {}
        local wts_reward_cfg = ConfigManager.Instance:GetAutoConfig("newer_cfg_auto").wts_reward[1]
        if wts_reward_cfg then
            if wts_reward_cfg.reward_item[0] then
                reward_items[1] = wts_reward_cfg.reward_item[0]
            end
            for k,v in ipairs(wts_reward_cfg.reward_item) do
               table.insert(reward_items, v)
            end
        end
        FuBenWGCtrl.Instance:OpenWin(SceneType.FakeTianShenFb, reward_items, 0, 0, cur_star_num, 5)
        FuBenWGCtrl.SendWTSReward(1)
	else
		--self:OpenFuBenLoseView()
		FuBenWGCtrl.Instance:OpenLose(SceneType.TIAN_SHEN_FB)
	end
	FuBenPanelWGCtrl.Instance:CloseStarAniView()
end