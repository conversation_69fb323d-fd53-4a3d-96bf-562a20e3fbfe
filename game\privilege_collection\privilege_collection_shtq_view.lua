function PrivilegeCollectionView:SHTQLoadIndecCallBack()
    XUI.AddClickEventListener(self.node_list.shtq_buy_btn, BindTool.Bind1(self.OnClickSHTQBuyBtn, self))
    XUI.AddClickEventListener(self.node_list.shtq_get_exp_btn, BindTool.Bind1(self.OnClickSHTQGetExpBtn, self))
    XUI.AddClickEventListener(self.node_list.shtq_exp_group_btn, BindTool.Bind1(self.OnClickSHTQExpGroupBtn, self))

    self.scroll_prop_attr_list = AsyncListView.New(SHTQPrivilegeAttrRender, self.node_list.shtq_attr_list)

    if self.shtq_reward_grid == nil then
		self.shtq_reward_grid = AsyncBaseGrid.New()
		self.shtq_reward_grid:CreateCells({col = 3,
                        change_cells_num = 1,
                        list_view = self.node_list["shtq_cell_grid"],
				        itemRender = ItemCell})
		self.shtq_reward_grid:SetStartZeroIndex(true)
	end

    if not self.shtq_model_display then
		self.shtq_model_display = OperationActRender.New(self.node_list.shtq_model_display)
		self.shtq_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    self.shtq_show_model_item_id = nil
end

function PrivilegeCollectionView:SHTQReleaseCallBack()
    if self.scroll_prop_attr_list then
        self.scroll_prop_attr_list:DeleteMe()
        self.scroll_prop_attr_list = nil
    end

    if self.shtq_reward_grid then
        self.shtq_reward_grid:DeleteMe()
        self.shtq_reward_grid = nil
    end

    if self.shtq_model_display then
		self.shtq_model_display:DeleteMe()
		self.shtq_model_display = nil
    end

    self.shtq_show_model_item_id = nil
end

function PrivilegeCollectionView:SHTQShowIndecCallBack()

end

function PrivilegeCollectionView:SHTQOnFlush()
    self:SetSHTQShowInfo()
    self:SetSHTQModelShow()
end

function PrivilegeCollectionView:SetSHTQShowInfo()
    local is_act = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeIsActivate()
    local cur_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSeq()
    local cur_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(cur_seq)
    local next_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(cur_seq + 1)

    local attr_data = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, next_cfg, "attr_id", "attr_value", 1, 8)
    self.scroll_prop_attr_list:SetDataList(attr_data)

    if not IsEmptyTable(next_cfg) then
        local price = RoleWGData.GetPayMoneyStr(next_cfg.price, next_cfg.rmb_type, next_cfg.rmb_seq)
        self.node_list.shtq_price_text.text.text = price
    end

    local can_get_exp_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeExpFlag()
    local scene_type = Scene.Instance:GetSceneType()
    self.node_list.shtq_get_exp_btn:SetActive((is_act and not IsEmptyTable(next_cfg)) and cur_seq > can_get_exp_seq)    --已购买且未领取.
    self.node_list.shtq_buy_btn:SetActive(not IsEmptyTable(next_cfg) and cur_seq <= can_get_exp_seq)
    self.node_list.shtq_flag_green:SetActive(is_act and IsEmptyTable(next_cfg))

    if IsEmptyTable(next_cfg) then
        next_cfg = cur_cfg
    end

    local is_sell_out = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeIsSellOut()
    self.node_list.shtq_next_ratio_text:SetActive(not is_sell_out)
    self.node_list.shtq_next_exp_add_text:SetActive(not is_sell_out)

    local now_value = is_act and cur_cfg.up_num * 100 or 0
    self.node_list.shtq_now_ratio_text.text.text = string.format(Language.PrivilegeCollection.SHTQSpAttrCurStr, now_value)
    self.node_list.shtq_next_ratio_text.text.text = string.format(Language.PrivilegeCollection.SHTQSpAttrNextStr, next_cfg.up_num * 100)

    local now_value = is_act and cur_cfg.get_exp_per / 100 or 0
    self.node_list.shtq_now_exp_add_text.text.text = string.format(Language.PrivilegeCollection.SHTQSpAttrExpCurStr, now_value)
    self.node_list.shtq_next_exp_add_text.text.text = string.format(Language.PrivilegeCollection.SHTQSpAttrNextStr, next_cfg.get_exp_per / 100)

    self.node_list.shtq_slog_text.text.text = next_cfg.up_num

    self.shtq_reward_grid:SetDataList(next_cfg.reward_item)

    local save_exp = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSaveExp()
    local save_exp_str = is_sell_out and Language.PrivilegeCollection.SHTQSaveExpStr2 or Language.PrivilegeCollection.SHTQSaveExpStr
    self.node_list.shtq_add_exp_text.text.text = string.format(save_exp_str, CommonDataManager.ConverExp(save_exp))
    self.node_list.shtq_exp_group_text.text.text = is_sell_out and Language.PrivilegeCollection.SHTQSaveExpDesc2 or Language.PrivilegeCollection.SHTQSaveExpDesc1

    local can_up_lv_str
	local can_up_level, can_up_baifenbi = PrivilegeCollectionWGData.Instance:SaveExpCanUpLevel(next_cfg.seq >= 0 and next_cfg.seq or 0)
    if can_up_level > 0 then
        can_up_lv_str = string.format(Language.PrivilegeCollection.SHTQUpLvStr, can_up_level)
    else
        local can_up_baifenbi_str = 0
        if can_up_baifenbi >= 0.1 then
            can_up_baifenbi_str = string.format("%.1f", can_up_baifenbi)
        elseif can_up_baifenbi > 0 and can_up_baifenbi < 0.1 then
            can_up_baifenbi_str = 0.1
        end
        can_up_lv_str = string.format(Language.PrivilegeCollection.SHTQUpLvStr, can_up_baifenbi_str)
    end
    self.node_list.shtq_up_lv_text.text.text = can_up_lv_str

    local bg_effect_str = can_up_level >= 1 and "UI_shtq_bg1" or "UI_shtq_bg"
    local bundle, asset = ResPath.GetEffectUi(bg_effect_str)
    self.node_list.shtq_exp_group_bg_effect:ChangeAsset(bundle, asset)
end

function PrivilegeCollectionView:SetSHTQModelShow()
    local is_act = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeIsActivate()
    local cur_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSeq()

    local level_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(cur_seq + 1)

    if not level_cfg then
        level_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(cur_seq)
    end

    if self.shtq_show_model_item_id ~= level_cfg.model_show_itemid then
        local display_data = {}
        display_data.item_id = level_cfg.model_show_itemid
        display_data.should_ani = true
        display_data.render_type = 0
        display_data.skip_rest_action = true
        display_data.model_rt_type = ModelRTSCaleType.M
        display_data.can_drag = false

        if level_cfg.display_pos and level_cfg.display_pos ~= "" then
            local pos_list = string.split(level_cfg.display_pos, "|")
            local pos_x = tonumber(pos_list[1]) or 0
            local pos_y = tonumber(pos_list[2]) or 0
            local pos_z = tonumber(pos_list[3]) or 0
    
            display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
        end
    
        if level_cfg.display_rotation and level_cfg.display_rotation ~= "" then
            local rot_list = string.split(level_cfg.display_rotation, "|")
            local rot_x = tonumber(rot_list[1]) or 0
            local rot_y = tonumber(rot_list[2]) or 0
            local rot_z = tonumber(rot_list[3]) or 0
    
            display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
        end

        display_data.model_adjust_root_local_scale = level_cfg.display_scale

        self.shtq_model_display:SetData(display_data)
    end

    self.shtq_show_model_item_id = level_cfg.model_show_itemid

    local _, fight = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(level_cfg, "attr_id", "attr_value", 1, 8)
    if not is_act then
        fight = 0
    end

    -- 战力
    local capability, show_max_cap, _ = 0, false, nil
    local show_item_id = level_cfg.model_show_itemid
    if ItemWGData.GetIsXiaogGui(show_item_id) then
        _, capability = ItemShowWGData.Instance:GetShouHuAttrByData(show_item_id)
    elseif HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(show_item_id) then
        capability = HiddenWeaponWGData.Instance:GetItemCapability(show_item_id)
    else
        local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
        if item_cfg then
            local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_item_id, item_cfg.sys_attr_cap_location)
            if sys_type == ITEMTIPS_SYSTEM.MOUNT or sys_type == ITEMTIPS_SYSTEM.LING_CHONG
            or sys_type == ITEMTIPS_SYSTEM.HUA_KUN or sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA
            or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
                show_max_cap = false
                capability = ItemShowWGData.CalculateCapability(show_item_id, show_max_cap) or 0
            else
                capability = ItemShowWGData.CalculateCapability(show_item_id) or 0
            end
        end
    end

    self.node_list.shtq_cap_value.text.text = capability + fight
end

function PrivilegeCollectionView:OnClickSHTQBuyBtn()
    local cur_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSeq()
    local next_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(cur_seq + 1)
    if not next_cfg then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.PrivilegeCollection.MaxLevel)
        return
    end

    RechargeWGCtrl.Instance:Recharge(next_cfg.price, next_cfg.rmb_type, next_cfg.rmb_seq)
end

--领取档位奖励.
function PrivilegeCollectionView:OnClickSHTQGetExpBtn()
    local is_act = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeIsActivate()
    local cur_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSeq()
    local can_get_exp_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeExpFlag()
    if is_act and cur_seq > can_get_exp_seq then
        if can_get_exp_seq < 0 then
            can_get_exp_seq = 0
        end
        PrivilegeCollectionWGCtrl.Instance:SendGuardPrivilegeOperate(GUARD_PRIVILEGE_OPER_TYPE.GUARD_PRIVILEGE_OPER_TYPE_FETCH_EXP, can_get_exp_seq)
    end
end

--只领取经验值.
function PrivilegeCollectionView:OnClickSHTQExpGroupBtn()
    local is_sell_out = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeIsSellOut()
    if is_sell_out then
        local is_act = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeIsActivate()
        local cur_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSeq()
        local save_exp = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSaveExp()
        if is_act and cur_seq >= 0 and save_exp > 0 then
            PrivilegeCollectionWGCtrl.Instance:SendGuardPrivilegeOperate(GUARD_PRIVILEGE_OPER_TYPE.GUARD_PRIVILEGE_OPER_TYPE_FETCH_EXP, cur_seq)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.PrivilegeCollection.SHTQExpGroupBtnStr2)
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.PrivilegeCollection.SHTQExpGroupBtnStr)
    end
end

------------------------------------SHTQPrivilegeAttrRender------------------------------------
SHTQPrivilegeAttrRender = SHTQPrivilegeAttrRender or BaseClass(CommonAddAttrRender)

function SHTQPrivilegeAttrRender:LoadCallBack()
    CommonAddAttrRender.LoadCallBack(self)
    self:SetAttrNameNeedSpace(true)
end

function SHTQPrivilegeAttrRender:OnFlush()
    CommonAddAttrRender.OnFlush(self)
    local pos_x = self.data.add_value == 0 and 94 or 0
    self.node_list.node_now_parent.transform.localPosition = Vector3(pos_x, 0, 0)

    self.node_list.arrow:SetActive(self.data.add_value > 0)
    self.node_list.add_value:SetActive(self.data.add_value > 0)
end