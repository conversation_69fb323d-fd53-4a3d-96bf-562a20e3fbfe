﻿using System;

namespace Nirvana
{
	/// <summary>
	/// 网络读缓冲区（环形窗口的线性实现）。
	/// 
	/// 负责累计 Socket 读取到的数据，提供
	/// - Prepare(length): 预留写入空间并返回写入起始位置
	/// - Submit(length): 提交写入长度
	/// - Consume(length): 消耗已读数据，并在完全消费后复位读写指针
	/// - Reserve(capacity): 在需要时搬移/扩容以保证容量
	/// </summary>
	internal sealed class EnhancedNetBuffer
	{
		/// <summary>
		/// 指定初始容量构造。
		/// </summary>
		public EnhancedNetBuffer(int capacity)
		{
			this.buffer = new byte[capacity];
			this.readPos = 0;
			this.writePos = 0;
		}

		/// <summary>
		/// 原始缓冲区数组。
		/// </summary>
		public byte[] Buffer
		{
			get
			{
				return this.buffer;
			}
		}

		/// <summary>
		/// 有效数据起始下标。
		/// </summary>
		public int DataStart
		{
			get
			{
				return this.readPos;
			}
		}

		/// <summary>
		/// 当前有效载荷大小（字节）。
		/// </summary>
		public int Payload
		{
			get
			{
				return this.writePos - this.readPos;
			}
		}

		/// <summary>
		/// 预留 length 字节写入空间，必要时搬移或扩容，返回可写起始位置。
		/// </summary>
		public int Prepare(int length)
		{
			bool flag = this.buffer.Length - this.writePos < length;
			if (flag)
			{
				this.Reserve(this.Payload + length);
			}
			return this.writePos;
		}

		/// <summary>
		/// 提交写入的字节数，返回提交是否在边界内（越界将被夹取至 buffer.Length 并返回 false）。
		/// </summary>
		public bool Submit(int length)
		{
			this.writePos += length;
			bool flag = this.writePos > this.buffer.Length;
			bool flag2;
			if (flag)
			{
				this.writePos = this.buffer.Length;
				flag2 = false;
			}
			else
			{
				flag2 = true;
			}
			return flag2;
		}

		/// <summary>
		/// 消费 length 字节有效数据；若全部消费完，复位读写指针为 0。
		/// </summary>
		public void Consume(int length)
		{
			this.readPos += length;
			bool flag = this.readPos >= this.writePos;
			if (flag)
			{
				this.readPos = 0;
				this.writePos = 0;
			}
		}

		/// <summary>
		/// 确保至少可容纳 capacity 字节的空间。
		/// 若剩余空间足够则前移数据，否则按需要扩容（最多翻倍与 capacity 中较小值）。
		/// </summary>
		public void Reserve(int capacity)
		{
			bool flag = this.buffer.Length - this.readPos >= capacity;
			if (flag)
			{
				int payload = this.Payload;
				Array.Copy(this.buffer, this.readPos, this.buffer, 0, payload);
				this.readPos = 0;
				this.writePos = payload;
			}
			else
			{
				int num = Math.Min(this.buffer.Length * 2, capacity);
				byte[] array = new byte[num];
				int payload2 = this.Payload;
				Array.Copy(this.buffer, this.readPos, array, 0, payload2);
				this.buffer = array;
				this.readPos = 0;
				this.writePos = payload2;
			}
		}

		private byte[] buffer; // 底层字节数组

		private int readPos; // 有效数据起始位置

		private int writePos; // 写入结束位置（开区间）
	}
}
