ActivityPrivilegeBuyView = ActivityPrivilegeBuyView or BaseClass(SafeBaseView)

function ActivityPrivilegeBuyView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()

    self:AddViewResource(0, "uis/view/priviege_purchase_ui_prefab", "layout_priviege_purchase_panal")
end

function ActivityPrivilegeBuyView:__delete()

end

function ActivityPrivilegeBuyView:ReleaseCallBack()
    self.left_select_index = nil
    self.reward_show_index = nil
    self:CleanActTimer()
    self:CleanBtnTimer()

    if self.priviege_buy_list then
        self.priviege_buy_list:DeleteMe()
        self.priviege_buy_list = nil
    end

    if self.right_item_list then
        self.right_item_list:DeleteMe()
        self.right_item_list = nil
    end
end

function ActivityPrivilegeBuyView:OpenCallBack()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.PRIVILEGEBUY, 1)
end

function ActivityPrivilegeBuyView:LoadCallBack()
   if not self.priviege_buy_list then
       self.priviege_buy_list = AsyncListView.New(PriviegeListRender, self.node_list["priviege_list"])
       self.priviege_buy_list:SetSelectCallBack(BindTool.Bind(self.LeftListCallBack, self))
    end

    --创建右边列表
    if not self.right_item_list then
        self.right_item_list = AsyncListView.New(PriviegeBuyItemRender, self.node_list["priviege_item_list"])
    end

    self.node_list["btn_prompt"].button:AddClickListener(BindTool.Bind(self.PromptBtnClick, self))
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))
    self.node_list["activate_btn"].button:AddClickListener(BindTool.Bind(self.ActivateBtnClick, self))

    local total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.PRIVILEGEBUY)

    self:UpdateTimeStr(total_time)
    if total_time > 0 then
        self.act_timer = CountDown.Instance:AddCountDown(total_time, 0.5,
        function(elapse_time, total_time)
            local time = math.floor(total_time - elapse_time)
            self:UpdateTimeStr(time)
        end,
        function()
            self:UpdateTimeStr(0)
        end
        )
    end

end

-- 清除倒计时器1
function ActivityPrivilegeBuyView:CleanActTimer()
    if self.act_timer and CountDown.Instance:HasCountDown(self.act_timer) then
        CountDown.Instance:RemoveCountDown(self.act_timer)
        self.act_timer = nil
    end
end

function ActivityPrivilegeBuyView:UpdateTimeStr(time)
    if self.node_list["activity_down_time"] then
        self.node_list["activity_down_time"].text.text = TimeUtil.FormatTimeLanguage2(time)
    end
end

function ActivityPrivilegeBuyView:ShowIndexCallBack(index)
    self.need_select_new = true   -- 是否需要重新选择
    self.left_select_index = -1
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.PrivilegeBuyView, ACTIVITY_TYPE.PRIVILEGEBUY)
end

function ActivityPrivilegeBuyView:OnFlush()
    --给列表赋值,获取配置表的值
    local data_list = ActivityPrivilegeBuyWGData.Instance:GetTeQuanShowList()

    self.priviege_buy_list:SetDataList(data_list)
  --  left_list:SetDataList()

    if self.need_select_new then
        local dafult_index = 1
        self.need_select_new = nil
        for k,v in ipairs(data_list) do
            if v.is_remind then
                dafult_index = k
                break
            end
        end

        self.priviege_buy_list:JumpToIndex(dafult_index)  --列表跳转
        return
    end

   self:FlushRightPanel()
end

function ActivityPrivilegeBuyView:LeftListCallBack(cell)
    if not cell or not cell:GetData() then
        return
    end

    local index = cell:GetIndex()
    if index == self.left_select_index then
        return
    end

   -- print_error("index",index)
    self.reward_show_index = nil
    self.privilege_select_data = cell:GetData()
    self.left_select_index = index
    self:FlushRightPanel()
end

--刷新右边物品
function ActivityPrivilegeBuyView:FlushRightPanel()
    local data = self.privilege_select_data
    if data == nil then
        return
    end

    local act_day = ActivityPrivilegeBuyWGData.Instance:GetActDay()
    local list = ActivityPrivilegeBuyWGData.Instance:GetRewardList(data.seq)
    local is_buy = ActivityPrivilegeBuyWGData.Instance:GetTeQuanIsBuy(data.seq)
    self.right_item_list:SetDataList(list)

    if is_buy then
        local jump_index = act_day >= #list and #list or act_day
        for k,v in ipairs(list) do
            local is_remind = ActivityPrivilegeBuyWGData.Instance:GetCurDayRewardIsCanGet(v.seq, v.activity_day)
            if is_remind then
                jump_index = k
                break
            end
        end

        if self.reward_show_index ~= jump_index then
            self.reward_show_index = jump_index
            self.right_item_list:JumpToIndex(jump_index)  --列表跳转
        end
    elseif self.reward_show_index == nil then
        self.right_item_list:JumpToIndex(1)
    end

    XUI.SetButtonEnabled(self.node_list["activate_btn"], not is_buy)
    
    local price = RoleWGData.GetPayMoneyStr(data.rmb_price, data.rmb_type, data.rmb_seq)
    self.node_list["activate_txt"].text.text = price
    self.node_list["activate_btn"]:SetActive(not is_buy)

    --特权显示时间
    self:CleanBtnTimer()
    self.node_list["priviege_time"]:SetActive(not is_buy)
    if is_buy or data.close_day < act_day then
        return
    end

    local time = TimeWGCtrl.Instance:GetServerTime()   
    local today_rest_time = TimeUtil.GetTodayRestTime(time)  --获取当天剩余时间
    local total_time = (data.close_day - act_day) * 24 * 3600 - today_rest_time 
    self:UpdateBtnTimeStr(total_time)
    if total_time > 0 then
        self.btn_act_timer = CountDown.Instance:AddCountDown(total_time, 0.5,
        function(elapse_time, total_time)
            local rest_time = math.floor(total_time - elapse_time)
            self:UpdateBtnTimeStr(rest_time)
        end,
        function()
            self:UpdateBtnTimeStr(0)
        end
        )
    end
end

function ActivityPrivilegeBuyView:ActivateBtnClick()
    local data = self.privilege_select_data    --拿到这个数据  表示每个标签的信息
    if data == nil then
        return
    end

    local is_buy = ActivityPrivilegeBuyWGData.Instance:GetTeQuanIsBuy(data.seq)
    if is_buy then
        return
    end
    RechargeWGCtrl.Instance:Recharge(data.rmb_price, data.rmb_type, data.rmb_seq)
end

-- 清除倒计时器1
function ActivityPrivilegeBuyView:CleanBtnTimer()
    if self.btn_act_timer and CountDown.Instance:HasCountDown(self.btn_act_timer) then
        CountDown.Instance:RemoveCountDown(self.btn_act_timer)
        self.btn_act_timer = nil
    end
end

function ActivityPrivilegeBuyView:UpdateBtnTimeStr(time)
    if self.node_list["priviege_down_time"] then
        self.node_list["priviege_down_time"].text.text = TimeUtil.FormatSecondDHM9(time)
    end
end

--点击了提示按钮
function ActivityPrivilegeBuyView:PromptBtnClick()
    local role_tip = RuleTip.Instance

    role_tip:SetContent(Language.PrivilegeBuy.PrivilegeRuleContent, Language.PrivilegeBuy.PrivilegeRule)
end






---列表格子
PriviegeListRender = PriviegeListRender or BaseClass(BaseRender)

function PriviegeListRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list["text"].text.text = self.data.name
    self.node_list["text_hl"].text.text = self.data.name
    self.node_list["red_point"]:CustomSetActive(self.data.is_remind)
end

function PriviegeListRender:OnSelectChange(is_select)
    self.node_list["not_image"]:CustomSetActive(not is_select)
    self.node_list["selete_image"]:CustomSetActive(is_select)
end




--创建右边物品列表
PriviegeBuyItemRender = PriviegeBuyItemRender or BaseClass(BaseRender)
function PriviegeBuyItemRender:__init()
    self.top_item_cell = {}
    self.top_item_cell = ItemCell.New(self.node_list["privilege_item"])

    self.reward_list = {}
    for i = 1, 6 do
        self.reward_list[i] = ItemCell.New(self.node_list["reward_list_".. math.ceil(i / 3)])
    end

    XUI.AddClickEventListener(self.node_list["get_btn"], BindTool.Bind(self.OnClickGetBtn, self))
end

function PriviegeBuyItemRender:LoadCallBack()
    
end

function PriviegeBuyItemRender:__delete()
    if self.top_item_cell then
        self.top_item_cell:DeleteMe()
        self.top_item_cell = nil
    end

    if self.reward_list then
        for k,v in pairs(self.reward_list) do
            v:DeleteMe()
        end

        self.reward_list = nil
    end
end

function PriviegeBuyItemRender:OnFlush()
    if not self.data then
        return
    end

    local reward_item = self.data.reward_item
    self.top_item_cell:SetData(reward_item[0])

    for i = 1, #self.reward_list do
        if reward_item[i] then
            self.reward_list[i]:SetData(reward_item[i])
            self.reward_list[i]:SetActive(true)
        else
            self.reward_list[i]:SetActive(false)
        end
    end

    local is_buy = ActivityPrivilegeBuyWGData.Instance:GetTeQuanIsBuy(self.data.seq)
    local is_get = ActivityPrivilegeBuyWGData.Instance:GetRewardIsGet(self.data.seq, self.data.activity_day)
    local act_day = ActivityPrivilegeBuyWGData.Instance:GetActDay()
    self.node_list["get_flag"]:SetActive(is_get)
    self.node_list["get_btn"]:SetActive(not is_get)
    local is_remind = is_buy and not is_get and act_day >= self.data.activity_day

    self.node_list["red_mind"]:SetActive(is_remind)

    if not is_get then
        local is_grey = not is_buy or self.data.activity_day > act_day
        if is_grey then
            XUI.SetGraphicGrey(self.node_list["get_btn"], is_grey)
        else
            XUI.SetButtonEnabled(self.node_list["get_btn"], not is_grey)
        end
    end

   local offset_day = self.data.activity_day - act_day
    if offset_day < 1 then
        self.node_list["get_txt"].text.text = Language.PrivilegeBuy.PrivilegeLingQu
    elseif offset_day == 1 then
        self.node_list["get_txt"].text.text = Language.PrivilegeBuy.PrivilegeNextLingQu
    else
        self.node_list["get_txt"].text.text = string.format(Language.PrivilegeBuy.PrivilegeLingQuDay, offset_day)
    end
end


function PriviegeBuyItemRender:OnClickGetBtn()
    if not self.data then
        return
    end

    local is_get = ActivityPrivilegeBuyWGData.Instance:GetRewardIsGet(self.data.seq, self.data.activity_day)
    if is_get then
        TipWGCtrl.Instance:ShowSystemMsg(Language.PrivilegeBuy.PrivilegeWeiLing)
        return
    end

    local is_buy = ActivityPrivilegeBuyWGData.Instance:GetTeQuanIsBuy(self.data.seq)
    if not is_buy then
        TipWGCtrl.Instance:ShowSystemMsg(Language.PrivilegeBuy.PrivilegeNotBuy)
        return
    end

    local act_day = ActivityPrivilegeBuyWGData.Instance:GetActDay()
    if self.data.activity_day > act_day then
        TipWGCtrl.Instance:ShowSystemMsg(Language.PrivilegeBuy.PrivilegeNotTime)
        return
    end

    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.PRIVILEGEBUY, 2, self.data.seq, self.data.activity_day)
end


