TipsNumberShowView = TipsNumberShowView or BaseClass(BaseRender)
local VECTOR2_ZERO = Vector2(0,0)
local LOACL_Y = 152.7

function TipsNumberShowView:__init()
	self.rich_text = self.node_list["RichText"]
	self.system_tips = self.node_list["SystemTips"]
	self.canvas_group = self.node_list.SystemNumberViewNode.canvas_group
	self.begin_pos = self.system_tips.rect.anchoredPosition
	self.rich_text_bg = self.node_list["RichTextBg"]
	self.system_tips_has_bg = self.node_list["SystemTipsHasBg"]
	self.begin_pos_bg = self.system_tips_has_bg.rect.anchoredPosition
	self.node_bg = self.node_list["NodeBg"]

	self.messge = ""
	self.close_timer = nil
	self.anim_speed = 3
	self.index = 0
	self.has_finished = false
end

function TipsNumberShowView:__delete()
	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end

	if self.tween then
		self.tween:Kill()
		self.tween = nil
	end
end

function TipsNumberShowView:LoadCallBack()

end

function TipsNumberShowView:SystemTipsVis()
	return self.canvas_group and self.canvas_group.alpha > 0 and self:IsOpen()
end

function TipsNumberShowView:AddIndex()
	self.index = self.index + 1
	self:DoMove()
end

function TipsNumberShowView:Show(msg, speed, pos, index)
	self:SetActive(true)
	self.has_finished = false
	self.index = index or 0
	if self.system_tips and self.begin_pos then
		RectTransform.SetAnchoredPositionXY(self.system_tips.rect, self.begin_pos.x, self.begin_pos.y)
	end

	if self.system_tips_has_bg and self.begin_pos_bg then
		RectTransform.SetAnchoredPositionXY(self.system_tips_has_bg.rect, self.begin_pos_bg.x, self.begin_pos_bg.y)
	end

	if "string" ~= type(msg[1]) then
		return
	end

	pos = pos or VECTOR2_ZERO
	self.area_tips_con = 0
	speed = speed or 1

	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end
	self.close_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CloseTips, self), 5)

	self.messge = msg[1] or ""
	self.pos = pos
	self.fontSize = msg[2]
	self.is_has_bg = msg[4] or false

	local node_x = RectTransform.GetAnchoredPositionXY(self.node_list.SystemNumberViewNode.rect)
	RectTransform.SetAnchoredPositionXY(self.node_list.SystemNumberViewNode.rect, node_x, LOACL_Y + self.pos.y)
	self:FlushView(self.pos, self.fontSize)
	self:DoMove()
end

function TipsNumberShowView:ChangeSpeed(speed)

end

function TipsNumberShowView:DoMove()
	if nil == self.begin_pos or nil == self.system_tips or self.system_tips_has_bg == nil or self.begin_pos_bg == nil then
		return
	end

	local node = self.is_has_bg and self.system_tips_has_bg or self.system_tips
	local begin_pos = self.is_has_bg and self.begin_pos_bg or self.begin_pos
	if self.index == 0 then
		RectTransform.SetAnchoredPositionXY(node.rect, begin_pos.x, 30 * self.index - 55)
	else
		RectTransform.SetAnchoredPositionXY(node.rect, begin_pos.x, 30 * self.index)
	end

	self.tween = node.transform:DOAnchorPosY(25 * (self.index + 1), 1 / self.anim_speed)
	self.tween:SetEase(DG.Tweening.Ease.Linear)
end

function TipsNumberShowView:CloseTips()
	self.has_finished = true
	self:SetActive(false)

	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end

	if self.tween then
		self.tween:Kill()
		self.tween = nil
	end

	if self.system_tips then
		RectTransform.SetAnchoredPositionXY(self.system_tips.rect, self.begin_pos.x, 0)
	end

	if self.system_tips_has_bg then
		RectTransform.SetAnchoredPositionXY(self.system_tips_has_bg.rect, self.begin_pos_bg.x, 0)
	end
end

function TipsNumberShowView:FlushView(pos, fontSize)
	if self.is_has_bg then
		self.system_tips_has_bg:SetActive(true)
		self.system_tips:SetActive(false)
		self.rich_text_bg.text.text = self.messge
	else
		self.system_tips_has_bg:SetActive(false)
		self.system_tips:SetActive(true)
		self.rich_text.text.text = self.messge
	end
	
	if self.rich_text then
		RectTransform.SetAnchoredPositionXY(self.rich_text.rect, pos.x, 0)
	end

	if self.rich_text_bg and self.node_bg then
		RectTransform.SetAnchoredPositionXY(self.node_bg.rect, pos.x, 0)
	end
end

function TipsNumberShowView:GetAnimSpeed()
	return self.anim_speed
end

function TipsNumberShowView:IsOpen()
	return not self.has_finished
end