FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)

local ITEM_COUNT_MAX = 5
function FuBenPanelView:InitBaGuaZhenView()
	XUI.AddClickEventListener(self.node_list["btn_fb_add_count"],BindTool.Bind(self.OnClinkBaGuanZhenFbBuyHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_fb_clear"],BindTool.Bind(self.OnClinkSaoDangBaGuaFB, self))
	XUI.AddClickEventListener(self.node_list["btn_fb_enter"],BindTool.Bind(self.OnClickBaGuaZhenGoBtn, self))
	--难度五个格子
	self.bgmz_cur_data_list = BaGuaMiZhenWGData.Instance:GetBossInfoCfg()

	self.bagua_cell_list = {}
	for i = 1, ITEM_COUNT_MAX do
		self.bagua_cell_list[i] = BaGuaCell.New(self.node_list["ph_bagua_render_" .. i])
		self.bagua_cell_list[i]:SetData(self.bgmz_cur_data_list[i])
		XUI.AddClickEventListener(self.node_list["ph_bagua_render_" .. i], BindTool.Bind(self.BaGuaBossSelectCallBack, self,i))
	end

	--奖励预览
	local reward_list = BaGuaMiZhenWGData.Instance:GetBaGuaZhenRewardList()
	if not self.reward_bagua_list then
		self.reward_bagua_list = AsyncListView.New(BaGuaZhenFbRewardItemRender,self.node_list["baguazhen_reward_list"])
		self.reward_bagua_list:SetDataList(reward_list, 3)
	end

	self.node_list.desc_baguamizhen_ads.text.text = Language.FuBenPanel.BaGuaMiZhenAds
	local other_cfg = BaGuaMiZhenWGData.Instance:GetBaGuaFbOther()
	self.node_list.desc_baguamizhen.text.text = other_cfg and other_cfg.fb_des or ""

   	-- self:ShowBaGuaShuoMing()
end

-- function FuBenPanelView:ShowBaGuaShuoMing()
-- 	-- for i = 1, 9 do
-- 	-- 	if Language.FuBenPanel.TongTianTaShuoMing[i] then
-- 	-- 		self.node_list["bagua_info"..i]:SetActive(true)
-- 	-- 		if i == 2 or i== 3 then
-- 	-- 			self.node_list["bagua_info"..i].text.text = "" 
-- 	-- 		else
-- 	-- 			self.node_list["bagua_info"..i].text.text = Language.FuBenPanel.TongTianTaShuoMing[i]
-- 	-- 		end
			
-- 	-- 	else
-- 	-- 		self.node_list["bagua_info"..i]:SetActive(false)
-- 	-- 	end
-- 	-- end

-- 	-- self.node_list.text_desc.text.text = Language.FuBenPanel.BaguaDesc
-- end


function FuBenPanelView:ShowBaGuaZhenCallBack()
	local tuijian_zhanli = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenTuiJianBuyLevel()
	local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
	local jump_k = 1
	
	for k,v in pairs(self.bgmz_cur_data_list) do
		if role_zhanli >= tuijian_zhanli[v.type] then
			jump_k = k
		end
	end
	self:FlushRewardListAndText(jump_k)
end

function FuBenPanelView:ShowBaGuaAnimCallBack()
	local tuijian_zhanli = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenTuiJianBuyLevel()
	local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
	local jump_k = 1
	
	for k,v in pairs(self.bgmz_cur_data_list) do
		if role_zhanli >= tuijian_zhanli[v.type] then
			jump_k = k
		end
	end
	
	-- TODO 跳转
	-- self.baguamizhen_hte_boss_list:JumpToIndex(jump_k)
	self:BaGuaBossSelectCallBack(jump_k)
end

function FuBenPanelView:DeleteBaGuaZhenView()
 	-- if self.baguamizhen_hte_boss_list then
	-- 	self.baguamizhen_hte_boss_list:DeleteMe()
	-- 	self.baguamizhen_hte_boss_list = nil
	-- end
	if self.bagua_cell_list then
		for i = 1, ITEM_COUNT_MAX do
			if self.bagua_cell_list[i] then
				self.bagua_cell_list[i]:DeleteMe()
				self.bagua_cell_list[i] = nil
			end
		end
	end
	self.bagua_cell_list = {}

    if self.reward_bagua_list then
        self.reward_bagua_list:DeleteMe()
        self.reward_bagua_list = nil
    end

    self.bgmz_cur_data_list = nil
    self.is_doing_bgmz_roll = nil
    -- self.boss_display_model = nil
    self.cache_resid = nil
end

function FuBenPanelView:DoBaGuaFBTweenStart()
    UITween.CleanAllTween(GuideModuleName.FuBenPanel)
    self.do_bagua_fb_tween = true
end

function FuBenPanelView:FlushBaGuaZhenView()
	self:OnFlushBaGuaMiZhenAddEffect()
	--每天免费次数
    local day_free_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenDayFreeTimes()
    --今日进入次数
    local day_enter_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenEnterTimes()
    --今日购买次数
    local day_buy_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenBuyTimes()
    --可进入次数
    local remain_times = day_free_times + day_buy_times - day_enter_times
    --总次数
    local total_times = day_free_times + day_buy_times

	self.node_list["txt_fb_count"].text.text = string.format(Language.FuBenPanel.FuBenEnterTime, remain_times, total_times)
    --小于零红色
    if remain_times > 0 then
        --self.node_list["txt_fb_count"].text.text = string.format(Language.FuBenPanel.HTEFbEnterTimes2, remain_times, total_times)
        self:IsShowButtonRedPoint()
    else
        --self.node_list["txt_fb_count"].text.text = string.format(Language.FuBenPanel.HTEFbEnterTimesNotEnough2, remain_times, total_times)
        self.node_list["btn_enter_redpoint"]:SetActive(false)
        self.node_list["btn_clear_redpoint"]:SetActive(false)
    end

   	local now_count, buy_times, next_vip, next_count = FuBenPanelWGData.Instance:GetNextVipParam(FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB)
	local can_buy = (now_count - buy_times) >0
	self.node_list["bagua_effectpos"]:SetActive(can_buy and remain_times == 0)
end

 --扫荡按钮红点
function FuBenPanelView:IsShowButtonRedPoint()
    local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
	local saodang_zhanli = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenSaoDangBuyLevel(5)
	local baguamizhen_isOpen,_ = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenIsOpen()

	if role_zhanli >= saodang_zhanli and baguamizhen_isOpen then
		local cfg = FuBenWGData.Instance:GetSaoDangCfg(FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB)
		local item = cfg.item
		local item_num = ItemWGData.Instance:GetItemNumInBagById(item.item_id)
		self.node_list["btn_enter_redpoint"]:SetActive(false)
		self.node_list["btn_clear_redpoint"]:SetActive(item and item_num > 0)
	else
		self.node_list["btn_enter_redpoint"]:SetActive(true)
		self.node_list["btn_clear_redpoint"]:SetActive(false)
	end

end

function FuBenPanelView:BaGuaMiZhenOnClickTips()
   	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.FuBenPanel.BaGuaMiZhenTipsTitle)
		role_tip:SetContent(Language.FuBenPanel.BaGuaMiZhenTipsDec)
	end
end

function FuBenPanelView:OnFlushBaGuaMiZhenAddEffect()
    if not self:IsOpen() or not self:IsLoadedIndex(TabIndex.fubenpanel_bagua) then
        return
    end
    --[[if self.node_list.baguamizhen_effect then
        self.node_list.baguamizhen_effect:SetActive(FuBenPanelWGData.Instance:CheckBaGuaMiZhenEffect())
    end--]]
end

--加号加次数
function FuBenPanelView:OnClinkBaGuanZhenFbBuyHandler()
	----TODO
	--FuBenPanelWGCtrl.Instance:OpenBaGuaMiZhenBuy()
	FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB)
end

--进入
function FuBenPanelView:OnClickBaGuaZhenGoBtn()
	if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end
	--TODO判断选择的那个,发送协议
	--可进入次数(协议取)
	local baguamizhen_isOpen,fuben_open_level = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenIsOpen(self.layer)
	if baguamizhen_isOpen then
		local can_enter_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenCanEnterTimes()
		--进入副本需要的东西
		--local item_id = FuBenPanelWGData.Instance:GetTianShenItemId()
		if can_enter_times <= 0 then
			--FuBenPanelWGCtrl.Instance:OpenBaGuaMiZhenBuy()
			self:OnClinkBaGuanZhenFbBuyHandler()
        else
            FuBenPanelWGData.Instance:SetBaGuaCurEnterLayer(self.layer)
			FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB, self.layer)
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.FuBenPanel.CopperNoKaiQi, fuben_open_level))
	end
end



--扫荡
function FuBenPanelView:OnClinkSaoDangBaGuaFB()
	--角色战力
	local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
	--当前选中的难度扫荡战力
	local limit_zhanli = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenSaoDangBuyLevel(self.layer)
	--今日可用次数 = 原本有的 + vip开的 - 使用的
	local can_enter_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenCanEnterTimes()
	if role_zhanli >= limit_zhanli then
		if can_enter_times > 0 then
			FuBenWGCtrl.Instance:ShowSaoDangPanel(FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB, self.layer, nil, nil)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)--无次数
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FuBenPanel.FunNotSaoDang, limit_zhanli))

	end


end

function FuBenPanelView:FlushBGMZCellSelect(value)
	for i = 1, 5 do
		self.bagua_cell_list[i]:OnSelectChange(i == value)
	end
end

function FuBenPanelView:BaGuaBossSelectCallBack(cell_index)
	if nil == self.bagua_cell_list[cell_index] then
		return 
	end
 	self:FlushRewardListAndText(cell_index)
	self:FlushBGMZCellSelect(cell_index)
end


function FuBenPanelView:FlushBaGuaModel(select_index)
	if not self.boss_display_model then
		return
	end

	local cur_boss_id = BaGuaMiZhenWGData.Instance:GetCurLayerBossID(select_index)
	local model_type, bundle, asset, res_id, tianshen_index = BossWGData.GetMonsterResByMonsterId(cur_boss_id)
	if self.cache_resid == res_id then
		return
	end

	self.cache_resid = res_id
	if model_type == ClientBossType.WeaponAniTSBoss and res_id > 0 and tianshen_index then
		self.boss_display_model:SetTianShenModel(res_id, tianshen_index, false, nil)
	elseif bundle and asset then
		self.boss_display_model:SetMainAsset(bundle, asset)
	end
end

--选择不同的难度刷新
function FuBenPanelView:FlushRewardListAndText(select_index)
	if not self.reward_bagua_list then
		return
	end
	
	self.layer = select_index
	--刷新扫荡text数值
	local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
	local cur_saodang_zhanli = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenSaoDangBuyLevel(self.layer)
	local color
	if role_zhanli >= cur_saodang_zhanli then
		color = COLOR3B.DEFAULT_NUM
	else
		color = COLOR3B.RED
	end

	local tuijian_zhanli = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenZhanLiBuyLevelAndLayer(self.layer)

    cur_saodang_zhanli = ToColorStr(cur_saodang_zhanli, color)
    self.node_list["bagua_info2"].text.text = string.format(Language.FuBenPanel.BaGuaZhenSaoDangText_Cant, cur_saodang_zhanli)
	self.node_list["bagua_info3"].text.text = string.format(Language.FuBenPanel.BaGuaZhenTuiJianText_Cant, tuijian_zhanli)
	--刷新奖励
	local reward_list = BaGuaMiZhenWGData.Instance:GetBaGuaZhenRewardList(self.layer)
	self.reward_bagua_list:SetDataList(reward_list, 3)
end


-------------------------------------------------------

BaGuaCell = BaGuaCell or BaseClass(BaseRender)
function BaGuaCell:__init()
	self.is_paly_tween = false
end

function BaGuaCell:OnFlush()
	if not self.data then
		return 
	end

	self.node_list.fb_desc.text.text = self.data.name_fb
	self.node_list.hl_fb_desc.text.text = self.data.name_fb
end

function BaGuaCell:OnSelectChange(is_select)
	self.node_list.select_image:SetActive(is_select)
	self.node_list.bg:SetActive(not is_select)
end

---------------------BaGuaZhenFbRewardItemRender------------------------
BaGuaZhenFbRewardItemRender = BaGuaZhenFbRewardItemRender or BaseClass(BaseRender)
function BaGuaZhenFbRewardItemRender:__init()
   self.base_cell = ItemCell.New(self.node_list["pos"])

end
function BaGuaZhenFbRewardItemRender:__delete()
	if self.base_cell then
        self.base_cell:DeleteMe()
        self.base_cell = nil
    end
end
function BaGuaZhenFbRewardItemRender:OnFlush()
	self.base_cell:SetData(self.data)
end
