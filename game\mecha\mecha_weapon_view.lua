function MechaView:LoadWeaponCallBack()
    if not self.mwp_mecha_list then
        self.mwp_mecha_list = AsyncListView.New(MWPMechaListRender, self.node_list.mwp_mecha_list)
        self.mwp_mecha_list:SetDefaultSelectIndex(nil)
        self.mwp_mecha_list:SetStartZeroIndex(true)
        self.mwp_mecha_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMWPMechaHandler, self))
    end

    if not self.mwp_mecha_part_list then
        self.mwp_mecha_part_list = AsyncListView.New(MWPMechaPartListRender, self.node_list.mwp_mecha_part_list)
        self.mwp_mecha_part_list:SetDefaultSelectIndex(nil)
        self.mwp_mecha_part_list:SetStartZeroIndex(true)
        self.mwp_mecha_part_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMWPMechaPart<PERSON><PERSON><PERSON>, self))
    end

    if not self.mwp_mecha_part_cell_list then
        self.mwp_mecha_part_cell_list = AsyncListView.New(MWPMechaPartCellListRender, self.node_list.mwp_mecha_part_cell_list)
        self.mwp_mecha_part_cell_list:SetDefaultSelectIndex(nil)
        self.mwp_mecha_part_cell_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMWPMechaPartCellHandler, self))
    end

    if not self.mwp_attr_info_list then
        self.mwp_attr_info_list = AsyncListView.New(MWPAttrInfoListRender, self.node_list.mwp_attr_info_list)
    end

    if not self.mwp_active_item then
        self.mwp_active_item = ItemCell.New(self.node_list.mwp_active_item)
    end

    if not self.mwp_skill_info_list then
        self.mwp_skill_info_list = AsyncListView.New(MWPSkillInfoItemRender, self.node_list.mwp_skill_info_list)
    end

    if not self.mwp_up_level_item then
        self.mwp_up_level_item = ItemCell.New(self.node_list.mwp_up_level_item)
    end

    if not self.mwp_gundam_model then
        self.mwp_gundam_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["mwp_modle_root"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.mwp_gundam_model:SetRenderTexUI3DModel(display_data)
        -- self.mwp_gundam_model:SetUI3DModel(self.node_list["mwp_modle_root"].transform, self.node_list["mwp_modle_root"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    self.node_list.mwp_attr_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMWPTog,self, 1))
    self.node_list.mwp_uplevel_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMWPTog,self, 2))

    XUI.AddClickEventListener(self.node_list.btn_mwp_jnyl, BindTool.Bind(self.OnClickMWPJnyl, self))
    XUI.AddClickEventListener(self.node_list.btn_mwp_zbk, BindTool.Bind(self.OnClickMWPZbk, self))
    XUI.AddClickEventListener(self.node_list.btn_mwp_active, BindTool.Bind(self.OnClickMWPActive, self))
    XUI.AddClickEventListener(self.node_list.btn_mwp_up_level, BindTool.Bind(self.OnClickMWPUpLevel, self))

    self.mwp_select_mecha_seq = -1 
    self.mwp_select_mecha_data = {}
    self.mwp_select_mecha_part = -1
    self.mwp_select_mecha_weapon = -1 
    self.mwp_select_mecha_weapon_type = -1  
    self.mwp_select_mecha_part_data = {}
    self.mwp_select_mecha_part_cell_sort = -1
    self.mwp_select_mecha_part_cell_index = -1
    self.mwp_select_mecha_part_cell_data = {}
    self.mwp_right_tog_select_id = 1

    self.mwp_can_show_sort_tip = true
    self.mwp_show_action = false
end

function MechaView:ShowWeaponCallBack()
    self:RightPanleShowTween(self.node_list.mwp_right_tween_root0, self.node_list.mwp_right_tween_root0)

    if self.mwp_gundam_model then
        self.mwp_gundam_model:SetRotation({x = 0, y = 180, z = 0})
        self.mwp_gundam_model:PlayRoleShowAction()
    end
end

function MechaView:ChangeWeaponCallBack()
    
end

function MechaView:ReleaseWeaponCallBack()
    if self.mwp_mecha_list then
        self.mwp_mecha_list:DeleteMe()
        self.mwp_mecha_list = nil
    end

    if self.mwp_mecha_part_list then
        self.mwp_mecha_part_list:DeleteMe()
        self.mwp_mecha_part_list = nil
    end

    if self.mwp_mecha_part_cell_list then
        self.mwp_mecha_part_cell_list:DeleteMe()
        self.mwp_mecha_part_cell_list = nil
    end

    if self.mwp_active_item then
        self.mwp_active_item:DeleteMe()
        self.mwp_active_item = nil
    end

    if self.mwp_skill_info_list then
        self.mwp_skill_info_list:DeleteMe()
        self.mwp_skill_info_list = nil
    end

    if self.mwp_up_level_item then
        self.mwp_up_level_item:DeleteMe()
        self.mwp_up_level_item = nil
    end

    if self.mwp_gundam_model then
        self.mwp_gundam_model:DeleteMe()
        self.mwp_gundam_model = nil
    end

    if self.mwp_attr_info_list then
        self.mwp_attr_info_list:DeleteMe()
        self.mwp_attr_info_list = nil
    end

    self.mwp_model_cache = nil
    self.mwp_show_action = false
end

function MechaView:OnFlushWeaponCallBack()
    local mecha_show_data = MechaWGData.Instance:GetMechaShowDataList()
    self.mwp_mecha_list:SetDataList(mecha_show_data)
    self.mwp_mecha_list:JumpToIndex(self:GetMWPSelectMecha(mecha_show_data))
end

------------------------------------选择回调------------------------------
function MechaView:OnSelectMWPMechaHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end
    self.mwp_can_show_sort_tip = false
    local data = item.data

    if self.mwp_select_mecha_seq ~= data.seq then
        self.mwp_show_action = false
    end

    self.mwp_select_mecha_seq = data.seq
    self.mwp_select_mecha_data = data

    local is_wear_complete = MechaWGData.Instance:IsMechaWearComplete(self.mwp_select_mecha_seq)
    self.node_list.flag_mwp_need_zbk:CustomSetActive(not is_wear_complete)

    local part_data_list = MechaWGData.Instance:GetMechaWeaponPartShowDataList(self.mwp_select_mecha_seq, MECHA_PART_TYPE.WEAPON)
    local cap = MechaWGData.Instance:GetMWPShowCap(self.mwp_select_mecha_seq)
    self.node_list.mwp_cap_value.text.text = cap
    self.mwp_mecha_part_list:SetDataList(part_data_list)
    self.mwp_mecha_part_list:JumpToIndex(self:GetMWPSelectWeaponType(part_data_list))
end

function MechaView:OnSelectMWPMechaPartHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end
    self.mwp_can_show_sort_tip = false
    local data = item.data

    local part_data
    for k, v in pairs(data) do
        part_data = v
        break
    end

    self.mwp_select_mecha_part = part_data.part
    self.mwp_select_mecha_weapon = part_data.weapon_type
    self.mwp_select_mecha_weapon_type = item.index
    self.mwp_select_mecha_part_data = data

    local part_cell_data_list = MechaWGData.Instance:GetMechaWeaponPartCellShowDataList(self.mwp_select_mecha_seq, MECHA_PART_TYPE.WEAPON, self.mwp_select_mecha_weapon_type)
    self.mwp_mecha_part_cell_list:SetDataList(part_cell_data_list)
    self.mwp_mecha_part_cell_list:JumpToIndex(self:GetMWPSelectMechaPartCell(part_cell_data_list))
end

function MechaView:OnSelectMWPMechaPartCellHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    if self.mwp_can_show_sort_tip and self.mwp_select_mecha_part_cell_sort == data.sort then
        TipWGCtrl.Instance:OpenItem({item_id = data.active_item_id})
    end
    self.mwp_select_mecha_part_cell_sort = data.sort
    self.mwp_select_mecha_part_cell_index = item.index
    self.mwp_select_mecha_part_cell_data = data
    self.node_list.mwp_desc_name.text.text = data.part_name

    local _, attr_data_list = MechaWGData.Instance:GetPartUpLevelAttrDataList(data.seq)
    self.mwp_attr_info_list:SetDataList(attr_data_list)
    self:FlushMWPMidModel()
    self:FlushMWPRight()
    self.mwp_can_show_sort_tip = true
end

function MechaView:OnClickMWPTog(index)
    if index == 1 then
        self:FlushMWPAttrInfoPanel()
    else
        self:FlushMWPUplevelPanel()
    end

    self.mwp_right_tog_select_id = index
end

function MechaView:FlushMWPRight()
    local tog_select_id = self:GetMWPRightTogSelect()

    if self.mwp_right_tog_select_id == tog_select_id then
        if tog_select_id == 1 then
            self:FlushMWPAttrInfoPanel()
        else
            self:FlushMWPUplevelPanel()
        end
    else
        if tog_select_id == 1 then
            self.node_list.mwp_attr_tog.toggle.isOn = true
        else
            self.node_list.mwp_uplevel_tog.toggle.isOn = true
        end
    end

    self.mwp_right_tog_select_id = tog_select_id
end

function MechaView:FlushMWPAttrInfoPanel()
    local seq = self.mwp_select_mecha_part_cell_data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)
    local is_active = cur_star >= 0
    self.node_list.mwp_active_info:CustomSetActive(not is_active)
    self.node_list.mwp_skill_info:CustomSetActive(is_active)

    self.node_list.img_mwp_attr_top_title.text.text = string.format(Language.Mecha.MechaAttrNameTitle, Language.Mecha.MechaWeaponTypeName[self.mwp_select_mecha_seq][self.mwp_select_mecha_weapon_type])

    if is_active then
        local skill_info = MechaWGData.Instance:GetWeaponShowSkillCfg(seq)

        if not IsEmptyTable(skill_info) then
            local skill_data_list = string.split(skill_info.skill, "|")

            local skill_list = {}
            for k, v in pairs(skill_data_list) do
                table.insert(skill_list, {skill_id = tonumber(v)})
            end

            self.mwp_skill_info_list:SetDataList(skill_list)
        end
    else
        cur_star = cur_star >= 0 and cur_star or 0
        local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(seq, cur_star)

        if not IsEmptyTable(up_star_cfg) then
            local active_item_id = up_star_cfg.cost_item_id
            local cost_item_num = up_star_cfg.cost_item_num

            local has_num = ItemWGData.Instance:GetItemNumInBagById(active_item_id)
            local color = has_num >= cost_item_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
            local str = has_num .. "/" .. cost_item_num
            self.mwp_active_item:SetFlushCallBack(function ()
                self.mwp_active_item:SetRightBottomColorText(str, color)
                self.mwp_active_item:SetRightBottomTextVisible(true)
            end)
            self.mwp_active_item:SetData({item_id = active_item_id})

            local item_cfg = ItemWGData.Instance:GetItemConfig(active_item_id)
            self.node_list.mwp_desc_active_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
            self.node_list.btn_mwp_active_remind:CustomSetActive(has_num >= cost_item_num)
        end
    end
end

function MechaView:FlushMWPUplevelPanel()
    local seq = self.mwp_select_mecha_part_cell_data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)
    local is_avtive = cur_star >= 0
    local is_max_star = MechaWGData.Instance:IsPartIsMaxStar(seq)
    self.node_list.mwp_up_level_info:CustomSetActive(not is_max_star)
    self.node_list.mwp_up_level_max:CustomSetActive(is_max_star)
    self.node_list.img_mwp_attr_top_title.text.text = string.format(Language.Mecha.MechaAttrNameTitle, Language.Mecha.MechaWeaponTypeName[self.mwp_select_mecha_weapon_type])

    if not is_max_star then
        cur_star = cur_star >= 0 and cur_star or 0

        local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(seq, cur_star)
        if not IsEmptyTable(up_star_cfg) then
            local active_item_id = up_star_cfg.cost_item_id
            local cost_item_num = up_star_cfg.cost_item_num
            local has_num = ItemWGData.Instance:GetItemNumInBagById(active_item_id)
            local color = has_num >= cost_item_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
            local str = has_num .. "/" .. cost_item_num
            self.mwp_up_level_item:SetFlushCallBack(function ()
                self.mwp_up_level_item:SetRightBottomColorText(str, color)
                self.mwp_up_level_item:SetRightBottomTextVisible(true)
            end)
            
            self.mwp_up_level_item:SetData({item_id = active_item_id})
            self.node_list.btn_mwp_up_level_remind:CustomSetActive(is_avtive and has_num >= cost_item_num )
        end

        for i = 1, 5 do
            local asset_index = 2
            if cur_star > 0 then
                if cur_star > 5 then
                    asset_index = (cur_star - 5) >= i and 3 or 1
                else
                    asset_index = cur_star >= i and 1 or 2
                end
            end

            local bundle, asset = ResPath.GetMechaImg("a2_jj_di_xing" .. asset_index)
            self.node_list["mwp_star" .. i].image:LoadSprite(bundle, asset, function ()
                self.node_list["mwp_star" .. i].image:SetNativeSize()
            end)
        end

        XUI.SetButtonEnabled(self.node_list.btn_mwp_up_level, is_avtive)
    end
end

function MechaView:FlushMWPMidModel()
    local part_list = {}
    local base_part = MechaWGData.Instance:GetMechaBasePartListByMechaSeq(self.mwp_select_mecha_seq)
    for k, v in pairs(base_part) do
        local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
        part_list[part_cfg.part] = part_cfg.res_id
    end

    local putton_list = MechaWGData.Instance:GetMechaPartWearPartList(self.mwp_select_mecha_seq)
    if not IsEmptyTable(putton_list) then
        for k, v in pairs(putton_list) do
            if v >= 0 then
                local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
                part_list[part_cfg.part] = part_cfg.res_id
            end
        end
    end

    part_list[self.mwp_select_mecha_part_cell_data.part] = self.mwp_select_mecha_part_cell_data.res_id

    local part_info = {
		gundam_seq = self.mwp_select_mecha_seq,
		gundam_body_res = part_list[MECHA_PART_TYPE.BODY] or 0,
        gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON] or 0,
		gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND] or 0,
        gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND] or 0,
		gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT] or 0,
        gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
		gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING] or 0,
        gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING] or 0,
	}

    if self:IsMWPModelChange(part_info) then
        self.mwp_model_cache = part_info
        self.mwp_gundam_model:SetGundamModel(part_info)

        if not self.mwp_show_action then
            self.mwp_show_action = true
            self.mwp_gundam_model:PlayRoleShowAction()
        end
    end
end

function MechaView:IsMWPModelChange(part_info)
    if IsEmptyTable(self.mwp_model_cache) then
        return true
    end

    for k, v in pairs(part_info) do
        if self.mwp_model_cache[k] ~= v then
            return true
        end
    end
    
    return false
end

------------------------------------计算选择------------------------------
function MechaView:GetMWPSelectMecha(mecha_show_data)
    if self.mwp_select_mecha_seq >= 0 then
        local cur_remind = MechaWGData.Instance:GetMWPMechaRemind(self.mwp_select_mecha_seq)

        if cur_remind then
            return self.mwp_select_mecha_seq
        end
    end

    if not IsEmptyTable(mecha_show_data) then
        for k, v in pairs(mecha_show_data) do
            if self.mwp_select_mecha_seq ~= v.seq then
                local remind = MechaWGData.Instance:GetMWPMechaRemind(v.seq)
                if remind then
                    return v.seq
                end
            end
        end
    end

    return self.mwp_select_mecha_seq >= 0 and  self.mwp_select_mecha_seq or 0
end

function MechaView:GetMWPSelectWeaponType(part_data_list)
    if self.mwp_select_mecha_weapon_type > 0 then
        local cur_remind = MechaWGData.Instance:GetMWPMechaWeaponTypeRemind(self.mwp_select_mecha_seq, self.mwp_select_mecha_weapon_type)
        if cur_remind then
            return self.mwp_select_mecha_weapon_type
        end
    end

    if not IsEmptyTable(part_data_list) then
        for k, v in pairs(part_data_list) do
            local cur_remind = MechaWGData.Instance:GetMWPMechaWeaponTypeRemind(self.mwp_select_mecha_seq, k)
            if cur_remind then
                return k
            end
        end
    end

    return self.mwp_select_mecha_weapon_type >= 0 and self.mwp_select_mecha_weapon_type or 0
end

function MechaView:GetMWPSelectMechaPartCell(part_cell_data_list)
    if self.mwp_select_mecha_part_cell_sort > 0 then
        local remind = MechaWGData.Instance:GetMWPechaPartCellRemind(self.mwp_select_mecha_seq, self.mwp_select_mecha_weapon_type, self.mwp_select_mecha_part_cell_sort)
        if remind then
            return self.mwp_select_mecha_part_cell_index
        end
    end

    if not IsEmptyTable(part_cell_data_list) then
        for k, v in pairs(part_cell_data_list) do
            if v.sort ~= self.mwp_select_mecha_part_cell_sort then
                local remind = MechaWGData.Instance:GetMWPechaPartCellRemind(v.mechan_seq, v.weapon_type, v.sort)

                if remind then
                    return k
                end
            end
        end
    end

    return self.mwp_select_mecha_part_cell_index > 0 and  self.mwp_select_mecha_part_cell_index or 1
end

function MechaView:GetMWPRightTogSelect()
    local part_cell_remind_tab = MechaWGData.Instance:GetMWPMechaPartCellRemindTab(self.mwp_select_mecha_seq, self.mwp_select_mecha_weapon_type, self.mwp_select_mecha_part_cell_sort)
    
    if not IsEmptyTable(part_cell_remind_tab) then
        if self.mwp_right_tog_select_id == 1 then
            if part_cell_remind_tab.active_remind then
                return 1
            elseif part_cell_remind_tab.up_level_remind then
                return 2
            end
        else
            if part_cell_remind_tab.up_level_remind then
                return 2
            elseif part_cell_remind_tab.active_remind then
                return 1
            end
        end
    end

    return self.mwp_right_tog_select_id >= 1 and self.mwp_right_tog_select_id or 1
end


---------------------------------------btns--------------------------------------------
function MechaView:OnClickMWPJnyl()
    if not self.mwp_select_mecha_data then
        return
    end

    CommonSkillShowCtrl.Instance:SetMechaSkillViewDataAndOpen({gundam_seq = self.mwp_select_mecha_data.seq, weapon_type = self.mwp_select_mecha_weapon})
end

function MechaView:OnClickMWPZbk()
    if not MechaWGData.Instance:IsMechaActive(self.mwp_select_mecha_seq) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Mecha.ArnanentNotActiveTips)
        return
    end

    MechaWGCtrl.Instance:OpenMechaArmamentView(self.mwp_select_mecha_seq)
end

function MechaView:OnClickMWPActive()
    local seq = self.mwp_select_mecha_part_cell_data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)
    if cur_star < 0 then
        cur_star = cur_star >= 0 and cur_star or 0
        local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(seq, cur_star)

        if not IsEmptyTable(up_star_cfg) then
            local cust_item_id = up_star_cfg.cost_item_id
            local cost_item_num = up_star_cfg.cost_item_num
            local has_num = ItemWGData.Instance:GetItemNumInBagById(cust_item_id)
    
            if has_num >= cost_item_num then
                MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.PART_STAR, seq)
                TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0)})
            else
                TipWGCtrl.Instance:OpenItem({item_id = cust_item_id})
            end
        end
    end
end

function MechaView:OnClickMWPUpLevel()
    local seq = self.mwp_select_mecha_part_cell_data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)

    if cur_star >= 0 then
        local up_star_cfg = MechaWGData.Instance:GetPartUpStarCfg(seq, cur_star)

        if not IsEmptyTable(up_star_cfg) then
            local cust_item_id = up_star_cfg.cost_item_id
            local cost_item_num = up_star_cfg.cost_item_num
            local has_num = ItemWGData.Instance:GetItemNumInBagById(cust_item_id)
    
            if has_num >= cost_item_num then
                MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.PART_STAR, seq)
                TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengxing, is_success = true, pos = Vector2(0, 0)})
            else
                TipWGCtrl.Instance:OpenItem({item_id = cust_item_id})
            end
        end
    end
end

------------------------------------MWPMechaListRender------------------------------
MWPMechaListRender = MWPMechaListRender or BaseClass(BaseRender)

function MWPMechaListRender:LoadCallBack()
end

function MWPMechaListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local icon_bundle, icon_asset = ResPath.GetMechaImg(self.data.icon)
    self.node_list.mecha_icon.image:LoadSprite(icon_bundle, icon_asset, function ()
        self.node_list.mecha_icon.image:SetNativeSize()
    end)

    self.node_list.desc_name.text.text = self.data.name

    local remind = MechaWGData.Instance:GetMWPMechaRemind(self.data.seq)
    self.node_list.remind:CustomSetActive(remind)

    local is_wear_complete = MechaWGData.Instance:IsMechaWearComplete(self.data.seq)
    self.node_list.flag_wear_complete:CustomSetActive(is_wear_complete)
end

function MWPMechaListRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_nor:CustomSetActive(not is_select)
    self.node_list.bg_select:CustomSetActive(is_select)
    self.node_list.select_hl:CustomSetActive(is_select)
end

------------------------------------MWPMechaPartListRender------------------------------
MWPMechaPartListRender = MWPMechaPartListRender or BaseClass(BaseRender)

function MWPMechaPartListRender:LoadCallBack()
end

function MWPMechaPartListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local part_data
    for k, v in pairs(self.data) do
        part_data = v
        break
    end

    local weapon_type = part_data.weapon_type
    local mecha_seq = part_data.mechan_seq
    local weapon_cfg = MechaWGData.MECHA_WEAPON_SHOW_CFG[weapon_type] or MechaWGData.MECHA_WEAPON_SHOW_CFG[0]

    local weapon_name = (Language.Mecha.MechaWeaponTypeName[mecha_seq] or {})[weapon_type] or ""
    self.node_list.desc_name.text.text = weapon_name
    self.node_list.desc_name_hl.text.text = weapon_name

    local nor_bundle, nor_asset = ResPath.GetMechaImg(weapon_cfg.icon)
    self.node_list.icon_nor.image:LoadSprite(nor_bundle, nor_asset, function ()
        self.node_list.icon_nor.image:SetNativeSize()
    end)

    local hl_bundle, hl_asset = ResPath.GetMechaImg(weapon_cfg.icon_hl)
    self.node_list.icon_hl.image:LoadSprite(hl_bundle, hl_asset, function ()
        self.node_list.icon_hl.image:SetNativeSize()
    end)

    local remind = MechaWGData.Instance:GetMWPMechaWeaponTypeRemind(mecha_seq, weapon_type)
    self.node_list.remind:CustomSetActive(remind)
end

function MWPMechaPartListRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_hl:CustomSetActive(is_select)
end

------------------------------------MWPMechaPartCellListRender------------------------------
MWPMechaPartCellListRender = MWPMechaPartCellListRender or BaseClass(BaseRender)

function MWPMechaPartCellListRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
        self.item:SetIsShowTips(false)
        self.item:SetCellBgEnabled(false)
        self.item:SetUseButton(false)
    end
end

function MWPMechaPartCellListRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function MWPMechaPartCellListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item:SetData({item_id = self.data.active_item_id})
    local seq = self.data.seq
    local cur_star = MechaWGData.Instance:GetPartStarBySeq(seq)
    self.node_list.flag_lock:CustomSetActive(cur_star < 0)
    self.item:MakeGray(cur_star < 0)
    local remind = MechaWGData.Instance:GetMWPechaPartCellRemind(self.data.mechan_seq, self.data.weapon_type, self.data.sort)
    self.node_list.remind:CustomSetActive(remind)
end

function MWPMechaPartCellListRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

---------------------------------MWAttrInfoListRender---------------------------
MWPAttrInfoListRender = MWPAttrInfoListRender or BaseClass(BaseRender)

function MWPAttrInfoListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name or ""
    self.node_list.attr_value.text.text =  self.data.attr_value or 0
    local has_next_value = self.data.add_attr_value and self.data.add_attr_value > 0
    self.node_list.add_value.text.text = has_next_value and self.data.add_value or ""
    self.node_list.arrow:CustomSetActive(has_next_value)
end

------------------------------MWPSkillInfoItemRender---------------------------------
MWPSkillInfoItemRender = MWPSkillInfoItemRender or BaseClass(BaseRender)

function MWPSkillInfoItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local skill_info = SkillWGData.Instance:GetJiJiaSkillConfig(self.data.skill_id)
    if IsEmptyTable(skill_info) then
        return
    end

    self.node_list.skill_name.text.text = skill_info.skill_name
    local common_skill_info = SkillWGData.Instance:GetSkillClientConfig(self.data.skill_id)
    if IsEmptyTable(common_skill_info) then
        return
    end

    local bundle, asset = ResPath.GetSkillIconById(common_skill_info.icon_resource)
    self.node_list.skill_icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.skill_icon.image:SetNativeSize()
    end)

    self.node_list.skill_desc.text.text = common_skill_info.description
end