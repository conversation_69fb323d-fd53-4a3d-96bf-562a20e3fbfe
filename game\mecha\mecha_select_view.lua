-- 选择出战

MechaSelecttView = MechaSelecttView or BaseClass(SafeBaseView)

function MechaSelecttView:__init()
    self:SetMaskBg(false)
    local bundle = "uis/view/mecha_ui_prefab"
    self:AddViewResource(0, bundle, "layout_mecha_pop_bg")
    self:AddViewResource(0, bundle, "layout_mecha_select_view")
end

function MechaSelecttView:SetFightPosData(data)
    self.fight_pos_data = data
end

function MechaSelecttView:LoadCallBack()
    if not self.mecha_select_list then
        self.mecha_select_list = AsyncBaseGrid.New()

		local bundle = "uis/view/mecha_ui_prefab"
		local asset = "mecha_select_list_item"
        self.mecha_select_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list.mecha_select_list,
        assetBundle = bundle, assetName = asset, itemRender = MechaSelectItemRender})
		self.mecha_select_list:SetStartZeroIndex(false)
        self.mecha_select_list:SetSelectCallBack(BindTool.Bind1(self.OnClickMechaItem, self))
    end

    XUI.AddClickEventListener(self.node_list.btn_mfp_zbk, BindTool.Bind(self.GetZBKJumpMecha, self))
end

function MechaSelecttView:ReleaseCallBack()
    self.fight_pos_data = nil

    if self.mecha_select_list then
        self.mecha_select_list:DeleteMe()
        self.mecha_select_list = nil
    end
end

function MechaSelecttView:OnFlush()
    if IsEmptyTable(self.fight_pos_data) then
        self:Close()
        return
    end

    local has_mecha_active = MechaWGData.Instance:HasMechaActive()
    self.node_list.flag_no_mecha_active:CustomSetActive(not has_mecha_active)

    if has_mecha_active then
        local can_fight_data_list = MechaWGData.Instance:GetMachaToFightDataList(self.fight_pos_data)
        local has_to_fight_data = not IsEmptyTable(can_fight_data_list)
        local prepare_mecha_seq = MechaWGData.Instance:GetToFightCanPrepareMecha()
        local has_prepare_mecha_seq = prepare_mecha_seq >= 0

        self.node_list.flag_no_mecha_active:CustomSetActive(not has_to_fight_data and not has_prepare_mecha_seq)
        self.node_list.mecha_select_list:CustomSetActive(has_to_fight_data)
        self.node_list.zbk_panel:CustomSetActive(not has_to_fight_data and has_prepare_mecha_seq)

        if has_to_fight_data then
            local target_data_list = {}

            for k, v in pairs(can_fight_data_list) do
                local data = {}
                data.fight_pos_data = self.fight_pos_data
                data.info_data = v
                table.insert(target_data_list, data)
            end

            self.mecha_select_list:SetDataList(target_data_list)
        end
    end
end

function MechaSelecttView:GetZBKJumpMecha()
    local prepare_mecha_seq = MechaWGData.Instance:GetToFightCanPrepareMecha()
    
    if prepare_mecha_seq >= 0 then
        MechaWGCtrl.Instance:OpenMechaArmamentView(prepare_mecha_seq)
    end
end

function MechaSelecttView:OnClickMechaItem(item)
    if nil == item then
        return
    end

    if item.OnClickBtnToFight then
        item:OnClickBtnToFight()
    end
end

---------------------------------------MechaSelectItemRender----------------------------------------
MechaSelectItemRender = MechaSelectItemRender or BaseClass(BaseRender)

function MechaSelectItemRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
    end

    XUI.AddClickEventListener(self.node_list.btn_to_fight, BindTool.Bind(self.OnClickBtnToFight, self))
end

function MechaSelectItemRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function MechaSelectItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local fight_pos_data = self.data.fight_pos_data
    local info_data = self.data.info_data

    if not IsEmptyTable(fight_pos_data) and not IsEmptyTable(info_data) then
        local active_item_id = info_data.active_item
        local color = ItemWGData.Instance:GetItemColor(active_item_id)
        self.node_list.desc_name.text.text = ToColorStr(self.data.info_data.name, color)
        self.item:SetData({item_id = active_item_id})
        self.node_list.cap_value.text.text = MechaWGData.Instance:GetMechaCapBySeq(info_data.seq)

        local is_bianshen_mecha = MechaWGData.Instance:IsBianShenMecha(info_data.seq)
        local is_help_fight_mecha = MechaWGData.Instance:IsHelpFightMecha(info_data.seq)
        self.node_list.flag_battle:CustomSetActive(is_bianshen_mecha)
        self.node_list.flag_help_battle:CustomSetActive(is_help_fight_mecha)
    end
end

function MechaSelectItemRender:OnClickBtnToFight()
    if IsEmptyTable(self.data) then
        return
    end

    local fight_pos_data = self.data.fight_pos_data
    local info_data = self.data.info_data

    if not IsEmptyTable(fight_pos_data) and not IsEmptyTable(info_data) then
        if fight_pos_data.is_main_battle then
            MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.MAIN_FIGHT, info_data.seq)
        else
            MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.HELP_FIGHT_BATTLE, fight_pos_data.open_cfg.seq, info_data.seq)
        end
    end
end