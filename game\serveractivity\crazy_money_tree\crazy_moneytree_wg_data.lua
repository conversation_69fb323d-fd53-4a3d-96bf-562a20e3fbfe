CrazyMoneyTreeWGData = CrazyMoneyTreeWGData or BaseClass()

function CrazyMoneyTreeWGData:__init()
	if CrazyMoneyTreeWGData.Instance then
		ErrorLog("[CrazyMoneyTreeWGData] Attemp to create a singleton twice !")
	end

	CrazyMoneyTreeWGData.Instance = self
	self.total_chongzhi_gold = 0
	self.chongzhi_gold = 0
	self.cfg_index = 0
	self.is_first_enter = true
	RemindManager.Instance:Register(RemindName.crazy_money_tree, BindTool.Bind(self.RemindChangeCallBack, self))
end

function CrazyMoneyTreeWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.crazy_money_tree)
	CrazyMoneyTreeWGData.Instance = nil
	self.is_first_enter = false
	self.cfg_index = nil
end

function CrazyMoneyTreeWGData:SetRAShakeMoneyInfo(protocol)
	self.total_chongzhi_gold = protocol.total_chongzhi_gold
	self.chongzhi_gold = protocol.chongzhi_gold
	self.cfg_index = protocol.seq 
end

function CrazyMoneyTreeWGData:GetTotalGold()
	 return self.total_chongzhi_gold
end

--已领取元宝
function CrazyMoneyTreeWGData:GetMoney()
	 return self.chongzhi_gold
end

function CrazyMoneyTreeWGData:GetShankeCfgByServerDay()
	if self.cfg_index then
		return self.cfg_index
	end
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local shake_money_cfg = config.shake_money or {}
	local max_open_day = shake_money_cfg[#shake_money_cfg] and shake_money_cfg[#shake_money_cfg].section_start or 0
	local return_seq = 0
	if max_open_day ~= 0 and server_day >= max_open_day then
		return_seq = #shake_money_cfg 
		return return_seq 
	end

	for k,v in pairs(shake_money_cfg) do
		if server_day >= v.section_start and server_day <= v.section_end then
			return_seq = k
			break
		end
	end
	
	return return_seq
end

function CrazyMoneyTreeWGData:GetMaxChongZhiNum()
	local seq = self:GetShankeCfgByServerDay()
	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local shake_money_cfg = {}
	if config then
		shake_money_cfg = config.shake_money
	end
	local return_max = shake_money_cfg[seq] and shake_money_cfg[seq].return_max or 0
	return return_max
end

function CrazyMoneyTreeWGData:GetReturnChongzhi()
	local seq = self:GetShankeCfgByServerDay()
	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local shake_money_cfg = {}
	if config then
		shake_money_cfg = config.shake_money or {}
	end
	local return_chongzhi = shake_money_cfg[seq] and shake_money_cfg[seq].chongzhi_return or 0
	return return_chongzhi
end

--获取红点提醒
function CrazyMoneyTreeWGData:GetCanCrazy()
	local max_chongzhi_num = self:GetMaxChongZhiNum()
	local gold_num = self.total_chongzhi_gold - self.chongzhi_gold
	local cangetgold =  max_chongzhi_num - self.chongzhi_gold
	local has_recive_gold = self:GetReturnChongzhi()
	if gold_num > 0 and cangetgold > 0 then
		if self.chongzhi_gold ==  math.ceil(self.total_chongzhi_gold * has_recive_gold / 100) then
			return false
		else
			return true
		end
	end
	return false
end

function CrazyMoneyTreeWGData:RemindChangeCallBack()
	local show_redpoint = self:GetCanCrazy()
	return show_redpoint and 1 or 0
end

function CrazyMoneyTreeWGData:CanGetGole()
	local gold_num = self.total_chongzhi_gold - self.chongzhi_gold
	return gold_num > 0
end
