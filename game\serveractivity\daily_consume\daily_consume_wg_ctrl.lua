require("game/serveractivity/daily_consume/daily_consume_view")
require("game/serveractivity/daily_consume/daily_consume_wg_data")

DailyConsumeWGCtrl = DailyConsumeWGCtrl or BaseClass(BaseWGCtrl)

function DailyConsumeWGCtrl:__init()
	if DailyConsumeWGCtrl.Instance then
        error("[DailyConsumeWGCtrl]:Attempt to create singleton twice!")
	end
	DailyConsumeWGCtrl.Instance = self

	self.data = DailyConsumeWGData.New()
	self.view = DailyConsumeView.New()

	self:RegisterAllProtocals()

end

function DailyConsumeWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	DailyConsumeWGCtrl.Instance = nil
end

function DailyConsumeWGCtrl:RegisterAllProtocals()
	-- 注册协议
	self:RegisterProtocol(SCDailyTotalConsumeInfo, "OnSCDailyTotalConsumeInfo")
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	-- Remind.Instance:RegisterOneRemind(RemindId.daily_consume, BindTool.Bind1(self.CheckRemind, self))
end

-- 客户端收到协议后
function DailyConsumeWGCtrl:OnSCDailyTotalConsumeInfo(protocol)
	self.data:SetSCDailyTotalConsumeInfo(protocol)
	self.view:Flush()
	-- Remind.Instance:DoRemind(RemindId.daily_consume)
end

function DailyConsumeWGCtrl:Open()
	self.view:Open()
end


function DailyConsumeWGCtrl:MainuiOpenCreate()
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_DAILYCONSUME) or {}
	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		ServerActivityWGCtrl.Instance:SendChongzhiFetchReward(RECHARGEREWARD_REQ_TYPE.DAILY_TOTAL_CONSUME_INFO)
	end
end

-- 检查红点
function DailyConsumeWGCtrl:CheckRemind()
	return self.data:RemindDailyConsume()
end