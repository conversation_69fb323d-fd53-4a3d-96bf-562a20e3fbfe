MARRY_TAB_TYPE =
{
	JH = TabIndex.marry_jiehun,    --结婚
	HJ = TabIndex.marry_hunjie,    --婚戒
	FB = TabIndex.marry_fb,        --副本
	BX = TabIndex.marry_baoxia,    --宝匣
	BB = TabIndex.marry_baby,      --宝宝属性
	PW = TabIndex.marry_profess_wall, --表白
	--ZH = TabIndex.marry_flowers,   --赠花
}

MARRY_MODEL_INDEX = {
	Role_index = 1,
	Lover_index = 2,
	Baby_index = 3,
	BaoXia_index = 4,
	PW_Self = 5,
	PW_Lover = 6,
	Role_Baby_index = 7,
	Lover_Baby_index = 8,
	Notice_Baby_Boy = 9,
	Notice_Baby_Girl = 10,
	Role_fabao_index = 11,
	Lover_fabao_index = 12,
	BaoXia_fabao_index = 13,
	PW_fabao_Self = 14,
	PW_fabao_Lover = 15,
}

MarryView = MarryView or BaseClass(SafeBaseView)

function MarryView:__init()
	self.view_name = GuideModuleName.Marry

	self:LoadConfig()
	self:SetMaskBg(false, true)
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self.tab_sub = {}
	self.remind_tab = {
		{ RemindName.Marry_GetMarry},
		{ RemindName.Marry_Propose, },
		{ RemindName.Marry_Equip, },
		{ RemindName.Marry_Baby, },
		-- { RemindName.Marry_BaoXia, },
		-- { RemindName.Marry_Flowers, },
		{ RemindName.Marry_Copy, },
	}

	self:InitFunctionList()
	self.attr_item_list = {}
	self.bld_item_list = {}

	self.default_index = MARRY_TAB_TYPE.JH
	self.stuff_index = 1

	self.ph_quality_now = nil
	self.ph_quality_need = nil
	self.cur_ring = 0

	self.baby_auto_up = false   -- 宝宝自动进阶快关
	self.is_auto_up = false     --自动提升
	self.tree_auto_upgrade = false -- 仙树自动进阶

	self.is_in_dianchun_status = false
	self.is_safe_area_adapter = true
end

-- function MarryView:__delete()
-- 	-- if self.marry_love_event then
-- 	-- 	GlobalEventSystem:UnBind(self.marry_love_event)
-- 	-- 	self.marry_love_event = nil
-- 	-- end
-- end

function MarryView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.yuyue_alert then
		self.yuyue_alert:DeleteMe()
		self.yuyue_alert = nil
	end

	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	if self.marry_love_event then
		GlobalEventSystem:UnBind(self.marry_love_event)
		self.marry_love_event = nil
	end

	self:DeleteJieHunView()
	self:DeleteHunJieView()
	self:DeleteFbView()
	self:DeleteBaoXiaView()
	self:DeleteBabyAttrView()
	--self:DeleteFlowersView()
	self:DeleteProfessWallView()
	self.is_in_dianchun_status = false
end

function MarryView:LoadConfig()
	local bundle_name = "uis/view/marry_ui_prefab"
	-- self:AddViewResource({TabIndex.marry_jiehun, TabIndex.marry_hunjie, TabIndex.marry_profess_wall}, bundle_name, "layout_a3_qingyuan_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	self:AddViewResource(TabIndex.marry_jiehun, bundle_name, "layout_jiehun")
	self:AddViewResource(TabIndex.marry_hunjie, bundle_name, "layout_hunjie")
	self:AddViewResource(TabIndex.marry_baby, bundle_name, "layout_baby_attr")
	-- self:AddViewResource(TabIndex.marry_baoxia, bundle_name, "layout_baoxia")
	-- self:AddViewResource(TabIndex.marry_flowers, bundle_name, "layout_send_flower_upgrade")
	self:AddViewResource(TabIndex.marry_fb, bundle_name, "layout_fb")
	self:AddViewResource(TabIndex.marry_profess_wall, bundle_name, "layout_profess_wall")
	self:AddViewResource(0, bundle_name, "VerticalTabbar")

	self:SetTabShowUIScene(TabIndex.marry_jiehun, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.MARRY_BODY_INFO})
	self:SetTabShowUIScene(TabIndex.marry_profess_wall, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.MARRY_BODY_INFO2})
	self:SetTabShowUIScene(TabIndex.marry_hunjie, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.MARRY_BODY_INFO2})
	self:SetTabShowUIScene(TabIndex.marry_baby, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.MARRY_BODY_INFO2})
	-- self:SetTabShowUIScene(TabIndex.marry_baby, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.MARRY_BODY_INFO})
	-- self:SetTabShowUIScene(TabIndex.marry_baby, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.MARRY_BODY_INFO})
end

function MarryView:InitFunctionList()
	if not self.init_func_list then
		self.init_func_list = {
			[MARRY_TAB_TYPE.JH] = self.InitJieHunView,
			[MARRY_TAB_TYPE.HJ] = self.InitHunJieView,
			[MARRY_TAB_TYPE.BB] = self.InitBabyAttrView,
			-- [MARRY_TAB_TYPE.BX] = self.InitBaoXiaView,
			[MARRY_TAB_TYPE.FB] = self.InitFbView,
			[MARRY_TAB_TYPE.PW] = self.InitProfessWallView,
			--[MARRY_TAB_TYPE.ZH] = self.InitZhView,
		}
	end
end

function MarryView:LoadCallBack()
	self.tabbar = Tabbar.New(self.node_list)
	self.tabbar:Init(Language.Marry.TabGrop, self.tab_sub, "uis/view/marry_ui_prefab", nil, self.remind_tab)
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Marry, self.tabbar)
	if not self.item_data_event then
		self.item_data_event = BindTool.Bind1(self.BagItemDataChange, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.marry_love_event = GlobalEventSystem:Bind(OtherEventType.ROLE_ONLINE_CHANGE,
		BindTool.Bind(self.OnOtherRoleOnlineChange, self))

	local bundle, asset = ResPath.GetJieHunImg("a3_ty_round_btn_03")
	self.node_list.btn_rule_tips.image:LoadSprite(bundle, asset, function()
		self.node_list.btn_rule_tips.image:SetNativeSize()
	end)
end

function MarryView:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	self:FlushProposeRemind()
	self:HunJieDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	self:FlushBabyBagBiew(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
end

function MarryView:LoadIndexCallBack(index, loaded_times)
	if self.init_func_list[index] then
		self.init_func_list[index](self)
	end
end

--打开帮助面板
function MarryView.OpenTips(content, title)
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(title)
		role_tip:SetContent(content)
	else
		print_error("MarryView", "OpenTips() can not find the get way!")
	end
end

local on_line_state = { [0] = true, [2] = true }
function MarryView:OnOtherRoleOnlineChange(other_role_id, is_online)
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0 --伴侣ID
	if lover_id == 0 or lover_id ~= other_role_id then       --不是伴侣
		return
	end
	if self.show_index == MARRY_TAB_TYPE.FB and self:IsLoadedIndex(MARRY_TAB_TYPE.FB) then
		self:SendLoverInfo()
	end

	--屏蔽点唇
	--if on_line_state[is_online] then --伴侣下线
	--	MarryWGData.Instance:SetCoupleDianChunInfo(nil)
	--	--RemindManager.Instance:Fire(RemindName.Marry_DianChun)
	--	--self:Flush(MARRY_TAB_TYPE.DC)
	--elseif is_online == 1 and self:GetShowIndex() == MARRY_TAB_TYPE.DC then
	--	MarryWGCtrl.Instance:SendInviteLoverReq(2)
	--end
end

function MarryView:OpenCallBack()
	self:JieHunOpenCallBack()
end

function MarryView:CloseCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	self.is_auto_up = false
	self.baby_auto_up = false
	self.tree_auto_upgrade = false
	if self.is_in_dianchun_status then
		MarryWGCtrl.Instance:SendCoupleDianChunCloseReq()
		self.is_in_dianchun_status = false
	end
	if MarryWGCtrl.Instance then
		MarryWGCtrl.Instance:SetCloseDianChunViewFlag()
	end
	self:CloseAutoUpGrade()
	self:JieHunCloseCallBack()
	self:BabyCloseCallBack()
end

function MarryView:ShowIndexCallBack(index)
	local title
	--婚戒自动升级
	MainuiWGCtrl.Instance:CheckCachePower()
	if self:IsPlayHunJieAni() then
		self:ChangeHunJieIndex()
	end

	self:CanPlayBabyEffect(false)
	self:IsPlayBaoBaoAni(false)
	self:StopBabyGradeQuest()
	self:CloseAutoUpGrade()

	self.old_baby_exp = nil
	self.tree_auto_upgrade = false

	if self.pop_alert_hy ~= nil then
		self.pop_alert_hy.record_not_tip = false
	end
	self.old_param2 = nil
	self.old_id = nil
	self.old_star_level = nil
	local bundle, asset = ResPath.GetRawImagesJPG("a3_qy_bj")
	if index == MARRY_TAB_TYPE.DC then
		self:OpenDianChunView()
		if not self.is_in_dianchun_status then
			MarryWGCtrl.Instance:SendInviteLoverReq(2)
			self.is_in_dianchun_status = true
		end
	elseif index == MARRY_TAB_TYPE.BB then
		-- bundle, asset = ResPath.GetRawImagesPNG("a2_qy_zaizaibeij")
		title = Language.Marry.TitleGrop[4]
		MarryWGCtrl.Instance:SendBabyOperaReq(BABY_REQ_TYPE.BABY_REQ_TYPE_ALL_INFO)
	elseif index == MARRY_TAB_TYPE.HJ then
		-- bundle, asset = ResPath.GetRawImagesPNG("a2_qy_zaizaibeij")
		title = Language.Marry.TitleGrop[3]
		MarryWGCtrl.SendQingyuanReqEquipInfo()
	elseif index == MARRY_TAB_TYPE.PW then
		-- bundle, asset = ResPath.GetRawImagesPNG("a2_qy_bbbg")
		title = Language.Marry.TitleGrop[2]
		self:ShowProfessIndexCallBack()
	elseif index == MARRY_TAB_TYPE.JH then
		title = Language.Marry.TitleGrop[1]
		self:ShowJieHunIndexCallBack()
		MarryWGCtrl.Instance:SendBabyOperaReq(BABY_REQ_TYPE.BABY_REQ_TYPE_ALL_INFO)
	elseif index == MARRY_TAB_TYPE.FB then
		-- bundle, asset = ResPath.GetRawImagesPNG("a2_qy_xianyuanbj1")
		title = Language.Marry.TitleGrop[5]
		self:SendLoverInfo()
	-- elseif index == MARRY_TAB_TYPE.BX or index == MARRY_TAB_TYPE.ZH then
		-- bundle, asset = ResPath.GetRawImagesPNG("a2_qy_qyxzbj")
	end

	self.node_list.title_view_name.text.text = title
	-- self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset)
end

function MarryView:OnFlush(param_t, index)
	if index == 0 then
		return
	end

	for k, v in pairs(param_t) do
		if "all" == k then
			if index == MARRY_TAB_TYPE.FB then
				self:OnFlushFb()
				-- elseif index == math.floor(TabIndex.marry_couple_halo / 1000) then
				-- 	self:FlushCoupleHaloView()
			elseif index == MARRY_TAB_TYPE.JH then
				self:SetJieHunPanel()
				self:FlushJieHunView()
			elseif index == MARRY_TAB_TYPE.HJ then
				self:FlushHunJieView()
			-- elseif index == MARRY_TAB_TYPE.BX then
			-- 	self:SetBaoXiaRole()
			-- 	self:FlushBaoXiaView()
				--elseif index == MARRY_TAB_TYPE.DC then
				--	self:FlushDianChunView()
			elseif index == MARRY_TAB_TYPE.BB then
				if v.sel_index then
					local baby_type = v.to_ui_param and tonumber(v.to_ui_param) or 0
					self:SetWantSelectIndex(true, tonumber(v.sel_index), baby_type)
				end

				self:FlushBabyView()
			elseif index == MARRY_TAB_TYPE.PW then
				self:FlushProfessWallView()
			-- elseif index == MARRY_TAB_TYPE.ZH then
			-- 	self:FlushFlowersView()
			end
		elseif "is_from_protocol" == k then
			self:FlushBabyView(true)
		elseif "up_level_effect" == k then
			self:ShowLevelUpEffect(v.effect_id)
		elseif "up_heart_effect" == k then
			self:ShowLevelHeartUpEffect(v.heart)
		elseif "floating_text" == k then
			self:AttrFloatingText(v.add)
		elseif "update_Tab_vis" == k then
			self:SetTabbarVisivle()
		elseif "couple_halo" == k then
			self:FlushCoupleHaloView()
			--elseif "dianchun_fere_status" == k then
			--	self:FlushDianChunFereStatus()
		elseif "flush_self_dice_data" == k then
			self:FlushSelfDiceData()
		elseif "flush_fere_dice_data" == k then
			self:FlushFereDiceData()
		-- elseif "send_flower_level_change" == k or "send_flower_num_change" == k then
		-- 	self:FlushFlowersView(k)
		elseif "flower_item_num_change" == k then
			self:FlushFlowerItemList()
		end
	end
end

-- 隐藏fbtab
function MarryView:SetTabbarVisivle()

end

function MarryView.ChangeRoleFrame(node, frame_incex)
	if frame_incex <= 0 then
		return
	end
	local bundle, asset = ResPath.GetPhotoFrame(frame_incex)
	node.image:LoadSprite(bundle, asset, function()
		node.image:SetNativeSize()
	end)
end

function MarryView:OpenYuYueTips(seq)
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_YUYUE_RESULT, seq, 1)
end

function MarryView:GetBabyAutoUp()
	return self.baby_auto_up
end

function MarryView:SetBabyAutoUp(baby_auto_up)
	self.baby_auto_up = baby_auto_up
end
