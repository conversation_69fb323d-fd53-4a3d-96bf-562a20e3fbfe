
function WorldTreasureView:InitJiangLinView()
	self.jianglin_grade_change = true
	XUI.AddClickEventListener(self.node_list.jl_btn_shilian, BindTool.Bind(self.OnBtnShi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,self))

	local desc_cfg = WorldTreasureWGData.Instance:GetClientDesc(TabIndex.tcdb_jianglin)
	if  not IsEmptyTable(desc_cfg) then
		self.node_list.jl_tip_label.text.text = desc_cfg.rule_desc
	end

	self.jl_sl_reward_list = AsyncListView.New(ItemCell, self.node_list["jl_sl_reward_list"])
	self.jl_yj_reward_list = AsyncListView.New(ItemCell, self.node_list["jl_yi_reward_list"])

	-- self:JLTimeCountDown()
	self:FlushJiangLinView()

	if nil == self.jl_display_model then
		local display_node = self.node_list.jl_display
		self.jl_display_model = RoleModel.New()
        local display_data = {
			parent_node = display_node,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			-- can_drag = true,
		}

		self.jl_display_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.jl_display_model)
	end

	-- self.node_list.ts_title_sl.text.text = Language.TianShenRoad.TianShenXianLingMustFull
	-- self.node_list.ts_title_yj.text.text = Language.TianShenRoad.TianShenXianLingProbabilityFull
end

function WorldTreasureView:ReleaseJiangLinView()
	if self.jl_sl_reward_list then
		self.jl_sl_reward_list:DeleteMe()
		self.jl_sl_reward_list = nil
	end

	if self.jl_yj_reward_list then
		self.jl_yj_reward_list:DeleteMe()
		self.jl_yj_reward_list = nil
	end

	-- 销毁模型
	if self.jl_display_model then
		self.jl_display_model:DeleteMe()
		self.jl_display_model = nil
	end

	-- if self.tsjl_boss_model then
	-- 	self.tsjl_boss_model:DeleteMe()
	-- 	self.tsjl_boss_model = nil
	-- end
	
	CountDownManager.Instance:RemoveCountDown("tianshenroad_jianglin_count_down")
	CountDownManager.Instance:RemoveCountDown("TS_common_count_douwn")
end

function WorldTreasureView:InitFlushDesc()
	local is_dead = WorldTreasureWGData.Instance:GetShiLianBossState()
	if is_dead then
		self.node_list.jl_title_text.text.text = Language.WorldTreasure.ShiLianTime4
		return 
	end
	local is_in_time, time_data = WorldTreasureWGData.Instance:IsInShiLianActivity()
	if is_in_time  then
		self.node_list.jl_title_text.text.text = Language.WorldTreasure.ShiLianTime3
	else
		if time_data then
			local time_str = TimeUtil.FormatHM(time_data.start_timestemp)
			self.node_list.jl_title_text.text.text = string.format(Language.WorldTreasure.JianLinTip, time_str)
		else
			self.node_list.jl_title_text.text.text = ""
		end
	end

	-- local cfg = WorldTreasureWGData.Instance:GetJiangLinFlushTimeCfg()
	-- local time_pramstr = {}
	-- local index = 1
	-- if cfg ~= nil then
	-- 	for k,v in pairs(cfg) do
	-- 		time_pramstr[index] = string.format("%s:%s",string.sub(v.refresh_time,1,2) ,string.sub(v.refresh_time,-2))
	-- 		index = index + 1
	-- 	end
	-- 	self.node_list.jl_title_text.text.text = string.format(Language.WorldTreasure.JianLinTip, time_pramstr[1])
	-- end
end

function WorldTreasureView:TRJLShowIndexCallBack()
	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_FB_BOSS_DEAD_INFO)
	self:FlushJLModel()
	-- self:DoTRJLAnim()
end

function  WorldTreasureView:FlushJLModel()
	local cfg = WorldTreasureWGData.Instance:GetExtraDropModelCfg()
	if not IsEmptyTable(cfg) then
		self.jl_display_model:SetMainAsset(cfg.model_bundle_name, cfg.model_asset_name)

		if cfg.whole_display_pos and cfg.whole_display_pos ~= "" then
			local pos = Split(cfg.whole_display_pos, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.jl_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		if cfg.model_rot and cfg.model_rot ~= "" then
			local rot = Split(cfg.model_rot, "|")
			self.jl_display_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end

		if cfg.model_scale and cfg.model_scale ~= "" then
			self.jl_display_model:SetRTAdjustmentRootLocalScale(cfg.model_scale)
		end
	end
end

function WorldTreasureView:LoadJLVImg()
	local bundle, asset = ResPath.GetWorldTreasureRawImages("jsrh_bg_3")
	self.node_list.jl_bg1.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.jl_bg1.raw_image:SetNativeSize()
	end)

	bundle, asset = ResPath.GetWorldTreasureRawImages("jsrh_bg_2")
	self.node_list.jl_bg2.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.jl_bg2.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_bt_6")
	self.node_list.jl_img_title.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.jl_img_title.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_btd_1")
	self.node_list.jl_title_sl.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.jl_title_sl.raw_image:SetNativeSize()
	end)

	bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_btd_1")
	self.node_list.jl_title_yj.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.jl_title_yj.raw_image:SetNativeSize()
	end)
end

function WorldTreasureView:FlushJiangLinView(param_t, index)
	if self.jianglin_grade_change then
        self.jianglin_grade_change = false
        self:LoadJLVImg()
    end

	local is_open = WorldTreasureWGData.Instance:IsOpenShilian() == 1
	-- if is_open then
	-- 	self.node_list.jl_title_text.text.text = ""
	-- else
	-- 	if refresh_time then
	-- 		self.node_list.jl_title_text.text.text =  string.format(Language.WorldTreasure.ShiLianTime2,TimeUtil.FormatHM(refresh_time.start_timestemp), TimeUtil.FormatHM(refresh_time.end_timestemp))
	-- 	else
	-- 		self.node_list.jl_title_text.text.text = Language.WorldTreasure.ShiLianTime
	-- 	end
		
	-- end
	self.node_list["jl_btn_name"].text.text = Language.TianShenRoad.GoShiLian
	self.node_list.jl_btn_redpoint:SetActive(is_open)
	self.node_list.jl_btn_effect:SetActive(is_open)
	-- XUI.SetGraphicGrey(self.node_list.jl_btn_shilian, not is_open)
	-- XUI.SetButtonEnabled(self.node_list.jl_btn_shilian, is_open)

	self:InitJiangLinRewardList()
	self:InitFlushDesc()
end

function WorldTreasureView:InitJiangLinRewardList()
	local reward_cfg = WorldTreasureWGData.Instance:GetJiangLinReward()
	if reward_cfg and reward_cfg.reward_show then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(reward_cfg.reward_show)
		self.jl_yj_reward_list:SetDataList(data_list)
	end

	local boss_drop_cfg = WorldTreasureWGData.Instance:GetBossDropCfg()
	if not IsEmptyTable(boss_drop_cfg) then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(boss_drop_cfg.random_item)
		self.jl_sl_reward_list:SetDataList(data_list)
	end
end

function WorldTreasureView:OnBtnShiLianClickHnadler()
	local is_open = WorldTreasureWGData.Instance:IsOpenShilian() == 1
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WorldTreasure.UnopenTip)
	end

	local boss_id = WorldTreasureWGData.Instance:GetBossId()
	if boss_id and boss_id > 0 then
		WorldTreasureWGCtrl.Instance:GotoShiLian()
		self:Close()
	end
end

--有效时间倒计时
-- function WorldTreasureView:JLTimeCountDown()
-- 	CountDownManager.Instance:RemoveCountDown("tianshenroad_jianglin_count_down")
-- 	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_jianglin)
-- 	local server_time = TimeWGCtrl.Instance:GetServerTime()
-- 	if invalid_time > server_time then
-- 		self.node_list.jl_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
-- 		CountDownManager.Instance:AddCountDown("tianshenroad_jianglin_count_down", BindTool.Bind1(self.UpdateJLCountDown, self), BindTool.Bind1(self.JLTimeCountDown, self), invalid_time, nil, 1)
-- 	else
-- 		self.node_list.jl_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
-- 		self.node_list.jl_time_label.text.color = Str2C3b(COLOR3B.RED)
-- 	end
-- end

-- function WorldTreasureView:UpdateJLCountDown(elapse_time, total_time)
-- 	self.node_list.jl_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
-- end

function WorldTreasureView:DoTRJLAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView
    UITween.FakeHideShow(self.node_list["ts_jianglin_root"])
    -- RectTransform.SetAnchoredPositionXY(self.node_list["db_list"].rect, 14, 600)
   
    -- self.node_list["db_list"].rect:DOAnchorPos(Vector2(14, -51), tween_info.MoveTime)
    UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["ts_jianglin_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
 --    ReDelayCall(self, function()

	-- end, tween_info.AlphaDelay, "kaifu_bipin_panel_tween")
    -- UITween.DoUpDownCrashTween(self.node_list["ts_duobei_title_img"])
end
