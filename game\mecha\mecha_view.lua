MechaView = MechaView or BaseClass(SafeBaseView)

local MECHA_BG = {
    [10] = "a2_jj_bg_1",
}

function MechaView:__init()
    self.default_index = TabIndex.customized_rumors
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    self:SetMaskBg(false)
    local bundle = "uis/view/mecha_ui_prefab"
    self:AddViewResource(0, bundle, "layout_mecha_bg")
    self:AddViewResource(TabIndex.mecha_fighter_plane, bundle, "layout_mecha_fight_plane_view")
    self:AddViewResource(TabIndex.mecha_wing, bundle, "layout_mecha_wing_view")
    self:AddViewResource(TabIndex.mecha_weapon, bundle, "layout_mecha_weapon_view")
    self:AddViewResource(TabIndex.mecha_to_fight, bundle, "layout_mecha_to_fight_view")
    self:AddViewResource(TabIndex.mecha_equip, bundle, "layout_mecha_equip_view")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
    self:AddViewResource(0, bundle, "layout_mecha_top_panel")

    self.tab_sub = {}
    self.remind_tab = {
        { RemindName.MechaFighterPlane },
        { RemindName.MechaWing },
        { RemindName.MechaWeapon },
        { RemindName.MechaToFight },
        { RemindName.MechaEquip },
    }

    self.last_show_index = -1
end

function MechaView:LoadCallBack()
    if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:Init(Language.Mecha.TabGrop, self.tab_sub, nil, nil, self.remind_tab)
        self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
        FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.MechaView, self.tabbar)
    end
end

function MechaView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    self.last_show_index = nil
    self.bg_res_cache = nil

    self:ReleaseEquipCallBack()
    self:ReleaseToFightCallBack()
    self:ReleaseWeaponCallBack()
    self:ReleaseWingCallBack()
    self:ReleaseFighterPlaneCallBack()
end

function MechaView:LoadIndexCallBack(index)
    if index == TabIndex.mecha_fighter_plane then
        self:LoadFighterPlaneCallBack()
    elseif index == TabIndex.mecha_wing then
        self:LoadWingCallBack()
    elseif index == TabIndex.mecha_weapon then
        self:LoadWeaponCallBack()
    elseif index == TabIndex.mecha_to_fight then
        self:LoadToFightCallBack()
    elseif index == TabIndex.mecha_equip then
        self:LoadEquipCallBack()
    end
end

function MechaView:ShowIndexCallBack(index)
    self.node_list.title_view_name.text.text = Language.Mecha.TabGrop[index / 10]

    self:ChangeMechaBg(index)

    if index == TabIndex.mecha_fighter_plane then
        self:ShowFighterPlaneCallBack()
    elseif index == TabIndex.mecha_wing then
        self:ShowWingCallBack()
    elseif index == TabIndex.mecha_weapon then
        self:ShowWeaponCallBack()
    elseif index == TabIndex.mecha_to_fight then
        self:ShowToFightCallBack()
    elseif index == TabIndex.mecha_equip then
        self:ShowEquipCallBack()
    end

    if index ~= self.last_show_index then
        self:ChangeIndexCallBack(index, self.last_show_index)
    end
end

function MechaView:ChangeIndexCallBack(index, last_show_index)
    if last_show_index == TabIndex.mecha_fighter_plane then
        self:ChangeFighterPlaneCallBack()
    elseif last_show_index == TabIndex.mecha_wing then
        self:ChangeWingCallBack()
    elseif last_show_index == TabIndex.mecha_weapon then
        self:ChangeWeaponCallBack()
    elseif last_show_index == TabIndex.mecha_to_fight then
        self:ChangeToFightCallBack()
    elseif last_show_index == TabIndex.mecha_equip then
        self:ChangeEquipCallBack()
    end

    self.last_show_index = index
end

function MechaView:ChangeMechaBg(index)
    local bg_str = MECHA_BG[index] or MECHA_BG[10]

    if nil == self.bg_res_cache or self.bg_res_cache ~= bg_str then
        self.bg_res_cache = bg_str
        local bundle, asset = ResPath.GetRawImagesPNG(bg_str)
        self.node_list.mecha_bg.raw_image:LoadSprite(bundle, asset, function()
            self.node_list.mecha_bg.raw_image:SetNativeSize()
        end)
    end
end

function MechaView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if k == "all" then
            if index == TabIndex.mecha_fighter_plane then
                self:OnFlushFighterPlaneCallBack()
            elseif index == TabIndex.mecha_wing then
                self:OnFlushWingCallBack()
            elseif index == TabIndex.mecha_weapon then
                self:OnFlushWeaponCallBack()
            elseif index == TabIndex.mecha_to_fight then
                self:OnFlushToFightCallBack()
            elseif index == TabIndex.mecha_equip then
                self:OnFlushEquipCallBack()
            end
        end
    end
end

function MechaView:RightPanleShowTween(right_node, canvas_group_node)
    if right_node then
        RectTransform.SetAnchoredPositionXY(right_node.rect, 500, 0)
        right_node.rect:DOAnchorPos(Vector2(0, 0), 0.8)
    end

    if canvas_group_node and canvas_group_node.canvas_group then
        canvas_group_node.canvas_group.alpha = 0
        canvas_group_node.canvas_group:DoAlpha(0, 1, 0.3)
    end
end
