-- 右上角倒计时
RightUpCountDownView = RightUpCountDownView or BaseClass(SafeBaseView)

function RightUpCountDownView:__init()
    self:AddViewResource(0, "uis/view/eternal_night_ui_prefab", "right_up_time_panel")
    self.view_name = "RightUpCountDownView"
end

function RightUpCountDownView:__delete()
end

function RightUpCountDownView:LoadCallBack()
	self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))
end

function RightUpCountDownView:ShrinkButtonsValueChange(isOn)
	self.node_list.root:SetActive(not isOn)
	-- if isOn then
		-- self.node_list.root.rect:DOAnchorPosX(180, 0.3)
	-- else
		-- self.node_list.root.rect:DOAnchorPosX(-261, 0.3)
	-- end
end

function RightUpCountDownView:ShowIndexCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ( )
		self:FlushTimeCount()
	end)
end

--status 1,准备阶段，2 活动阶段
function RightUpCountDownView:SetEndTime(time, status)
    self.status = status or 2
    self.end_time = time
    self:FlushTimeCount()
end

function RightUpCountDownView:FlushTimeCount()
    local time = self.end_time or 0
    if not self.node_list.time_grade_text then
        return
    end
    if self.status == 2 then
        self.node_list.time_grade_text.text.text = Language.Activity.ActivityStage
    else
        self.node_list.time_grade_text.text.text = Language.Activity.SteadyStage
    end
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    if time - server_time > 0 then
        self.node_list.root:SetActive(true)
		if CountDownManager.Instance:HasCountDown("common_right_up_time") then
			CountDownManager.Instance:RemoveCountDown("common_right_up_time")
		end
		CountDownManager.Instance:AddCountDown("common_right_up_time", BindTool.Bind(self.UpdateTimeCallBack,self), BindTool.Bind(self.CompleteTimeCallBack,self), time, nil, 1)
	else
		self:CompleteTimeCallBack()
	end
end

function RightUpCountDownView:UpdateTimeCallBack(now_time, total_time)
    local time = math.floor(total_time - now_time)
	local time_str = TimeUtil.MSTime(time)
	self.node_list.time_text.text.text = time_str
end

function RightUpCountDownView:CompleteTimeCallBack()
    self.node_list.time_text.text.text = ""
    self.node_list.root:SetActive(false)
end

function RightUpCountDownView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("common_right_up_time") then
		CountDownManager.Instance:RemoveCountDown("common_right_up_time")
	end

	if nil ~= self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end
end