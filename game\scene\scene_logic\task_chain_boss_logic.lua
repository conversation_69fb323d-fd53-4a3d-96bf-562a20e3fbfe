TaskChainBossLogic = TaskChainBossLogic or BaseClass(CommonFbLogic)

function TaskChainBossLogic:__init()
	-- self.obj_die_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_DEAD, BindTool.Bind(self.MainRoleDie,self))
	self.boss_pos_x = 0
	self.boss_pos_y = 0
end

function TaskChainBossLogic:__delete()
	if self.obj_die_event then
		GlobalEventSystem:UnBind(self.obj_die_event)
		self.obj_die_event = nil
	end
end

function TaskChainBossLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetButtonModeClick(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		ViewManager.Instance:Open(GuideModuleName.OperationTaskChainFbInfoView)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	local dungeon_info = OperationTaskChainWGData.Instance:GetBossDungeonInfo()
	if dungeon_info then
		local boss_pos = Split(dungeon_info.boss_pos, "|")
		self.boss_pos_x = tonumber(boss_pos[1]) or 0
		self.boss_pos_y = tonumber(boss_pos[2]) or 0
	end

	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainScheduleView)
end

function TaskChainBossLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	
	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)

	MainuiWGCtrl.Instance:SetButtonModeClick(true)
	UiInstanceMgr.Instance:ColseFBStartDown()
	OperationTaskChainWGCtrl.Instance:ResetInfoView()
	OperationTaskChainWGData.Instance:ResetBossInfo()
	
	FuBenPanelCountDown.Instance:CloseViewHandler()

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		main_role:SetUnderShowInfo(nil)
	end
end

function TaskChainBossLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

-- 怪物是否是敌人
function TaskChainBossLogic:IsMonsterEnemy(target_obj, main_role)
	return true
end

function TaskChainBossLogic:GetGuajiPos()
	return self.boss_pos_x, self.boss_pos_y
end

function TaskChainBossLogic:CanGetMoveObj()
	return false
end

function TaskChainBossLogic:GetMonsterName(monster)
	local color_name = ""
	if monster.vo then
		color_name = ToColorStr(monster.vo.name or "", COLOR3B.RED)
	end
	return color_name
end

function TaskChainBossLogic:MainRoleDie()
	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainFuHuoView)
end

function TaskChainBossLogic:IsTeamLimit(target_obj)
	local is_team = false
	if target_obj ~= nil and not target_obj:IsDeleted() then
		is_team = SocietyWGData.Instance:IsTeamMember(target_obj:GetOriginId())
	end

	return is_team
end