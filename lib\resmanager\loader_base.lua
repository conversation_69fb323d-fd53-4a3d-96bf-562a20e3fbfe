-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local ResUtil = require "lib/resmanager/res_util"
local SceneLoader = require "lib/resmanager/scene_loader"
local FileDownloader = require "lib/resmanager/file_downloader"

local SysUri = System.Uri

local UnityLoadSceneMode = UnityEngine.SceneManagement.LoadSceneMode
local SceneSingleLoadMode = UnityLoadSceneMode.Single
local SceneAdditiveLoadMode = UnityLoadSceneMode.Additive

local UnityGameObject = UnityEngine.GameObject
local UnityDontDestroyOnLoad = UnityGameObject.DontDestroyOnLoad
local UnityInstantiate = UnityGameObject.Instantiate
local SysObj = System.Object

local _tinsert = table.insert
local _sformat = string.format

local M = ResUtil.create_class()

function M:_init()
	self.v_lua_manifest_info = {bundleInfos = {}}
	self.v_manifest_info = {bundleInfos = {}}
	self.v_scene_loader_tbl = {}
	self.v_scene_loader = SceneLoader:new()

	self.is_ignore_hash_check = true
	self.is_can_check_crc = false
end

function M:Update(time, delta_time)
	if self.v_scene_loader then
		self.v_scene_loader:Update()
	end

	for _, scene_loader in ipairs(self.v_scene_loader_tbl) do
		scene_loader:Update()
	end

	if AudioManager then
		AudioManager.Update()
	end
end

function M:CreateEmptyGameObj(name, dont_destroy)
	local gameobj = UnityGameObject()

	if name then
		gameobj.name = name
	end

	if dont_destroy then
		self:DontDestroyOnLoad(gameobj)
	end

	return gameobj
end

function M:DontDestroyOnLoad(gameobj)
	UnityDontDestroyOnLoad(gameobj)
end

function M:Instantiate(res, dont_destroy, parent)
	if IsNil(res) then
		return
	end
	
	local go
	if not IsNil(parent) then
		-- 外部可能会误传gameObject类型进来，这里加个容错先
		go = UnityInstantiate(res, parent.transform, false)
	else
		go = UnityInstantiate(res)
	end

	go.name = res.name
	-- print_error("##############Instantiate", go.name)

	-- self:__CheckRefreshCall(go.name)
	-- 存在父节点的时候，设置DontDestroyOnLoad会报错
	if dont_destroy and nil == go.transform.parent then
		self:DontDestroyOnLoad(go)
	end

	return go
end

function M:__CheckRefreshCall(name)
	local now_frame = UnityEngine.Time.frameCount
	self.last_refresh_frame = self.last_refresh_frame or now_frame
	self.refresh_times_in_frame = self.refresh_times_in_frame or 0
	if self.last_refresh_frame ~= now_frame then
		self.last_refresh_frame = now_frame
		self.refresh_times_in_frame = 1
	else
		self.refresh_times_in_frame = self.refresh_times_in_frame + 1
		if self.refresh_times_in_frame > 1 then
			print_error(self.refresh_times_in_frame, name)
		end
	end
end

function M:DestroySysObj(sys_obj)
	if nil ~= sys_obj then
		SysObj.Destroy(sys_obj)
	end
end

function M:Destroy(gameobj)
	assert(nil)
end

function M:OnHotUpdateLuaComplete()
end

function M:LoadUnitySceneAsync(bundle_name, asset_name, load_mode, callback)
	assert(nil)
end

function M:LoadUnitySceneSync(bundle_name, asset_name, load_mode, callback)
	assert(nil)
end

function M:LoadLocalLuaManifest(name)
	assert(nil)
end

function M:LoadRemoteLuaManifest(callback)
	assert(nil)
end

function M:LoadLocalManifest(name)
	assert(nil)
end

function M:LoadRemoteManifest(name, callback)
	assert(nil)
end

function M:LoadAssetBundle(bundle_name, is_async, callback, cbdata)
	assert(nil)
end

function M:UnLoadAssetBundle(bundle_name)
	assert(nil)
end


function M:GetAllLuaManifestBundles()
    return {}
end

function M:GetAllManifestBundles()
    return {}
end

function M:LoadLevelSync(bundle_name, asset_name, load_mode, callback)
	if load_mode == SceneSingleLoadMode then
		self:_DestroyLoadingScenes()

		self.v_scene_loader:LoadLevelSync(bundle_name, asset_name, load_mode, callback)
	else
		local scene_loader = SceneLoader:new()
		_tinsert(self.v_scene_loader_tbl, scene_loader)
		scene_loader:LoadLevelSync(bundle_name, asset_name, load_mode, callback)
	end
end

function M:LoadLevelAsync(bundle_name, asset_name, load_mode, callback)
	if load_mode == SceneSingleLoadMode then
		self:_DestroyLoadingScenes()

		self.v_scene_loader:LoadLevelAsync(bundle_name, asset_name, load_mode, callback)
	else
		local scene_loader = SceneLoader:new()
		_tinsert(self.v_scene_loader_tbl, scene_loader)
		scene_loader:LoadLevelAsync(bundle_name, asset_name, load_mode, callback)
	end
end

function M:_DestroyLoadingScenes()
	self.v_scene_loader:Destroy()

	for _, scene_loader in ipairs(self.v_scene_loader_tbl) do
		scene_loader:Destroy()
	end

	self.v_scene_loader_tbl = {}
end

function M:UpdateBundle(bundle_name, update_delegate, complete)
	assert(nil)
end

function M:GetBundlesWithoutCached(bundle_name)
	assert(nil)
end

function M:GetManifestInfo()
	return self.v_manifest_info
end

function M:IsLuaVersionCached(bundle_name)
	local hash = nil
	if nil ~= self.v_lua_manifest_info.bundleInfos[bundle_name] then
		hash = self.v_lua_manifest_info.bundleInfos[bundle_name].hash
	end

	return ResUtil.IsFileExist("LuaAssetBundle/".. bundle_name, hash)
end

function M:SetAssetLuaVersion(asset_lua_version)
	self.v_asset_lua_version = asset_lua_version
end

function M:GetAssetLuaVersion()
	return self.v_asset_lua_version
end

function M:IsVersionCached(bundle_name, hash)
	if nil == hash and nil ~= self.v_manifest_info.bundleInfos[bundle_name] then
		hash = self.v_manifest_info.bundleInfos[bundle_name].hash
	end

	return ResUtil.IsFileExist(bundle_name, hash)
end

function M:SetAssetVersion(asset_version)
	self.v_asset_version = asset_version
end

function M:GetAssetVersion()
	return self.v_asset_version
end

function M:SetDownloadingURL(downloading_url)
	if downloading_url == nil then
		print_error("[LoaderBase] set downloading_url is nil")
		return
	end

	print_log("set donwloading url:", downloading_url)
	self.v_downloading_url = downloading_url
	self.is_ignore_hash_check = false
	self.is_can_check_crc = true
end

function M:SetDownloadingURL2(downloading_url2)
	print_log("set donwloading url2:", downloading_url2)
	self.v_downloading_url2 = downloading_url2
end

-- 设置DownloadingURL
-- 在线热更新专用接口
function M:SetRuntimeDownloadingURL(downloading_url)
	if downloading_url == nil then
		print_error("[LoaderBase] set runtime downloading_url is nil")
		return
	end

	self.v_runtime_downloading_url = downloading_url
end

function M:GetDownloadingURL()
	return self.v_downloading_url
end

function M:GetRemotePath(bundle_name, version, retry_count, force_url2)
	if nil ~= self.v_downloading_url2 and (force_url2 or (nil ~= retry_count and retry_count > 0)) then
		if force_url2 or retry_count % 3 == 0 then
			local path = SysUri.EscapeUriString(_sformat("%s/%s?v=%s", self.v_downloading_url2, bundle_name, version))
			print_error("切换到url2:", path)
			return path
		else
			return SysUri.EscapeUriString(_sformat("%s/%s?v=%s", self.v_downloading_url, bundle_name, version))
		end
	else
		return SysUri.EscapeUriString(_sformat("%s/%s?v=%s", self.v_downloading_url, bundle_name, version))
	end
end

-- 获取下载地址
-- 在线热更新专用接口
function M:GetRuntimeRemotePath(bundle_name, version)
	return SysUri.EscapeUriString(_sformat("%s/%s?v=%s", self.v_runtime_downloading_url, ResUtil.GetFileEncryptPath(bundle_name), version))
end

function M:GetIsIgnoreHashCheck()
	return self.is_ignore_hash_check
end

function M:GetIsCanCheckCRC()
	return self.is_can_check_crc
end

function M:GetLuaHashCode()
	return ""
end

function M:GetHashCode()
	return ""
end

function M:IsBundleMode()
	return false
end

function M.ExistedInStreaming(path)
	return ResUtil.ExistedInStreaming(path)
end

return M

