RoleBagView = RoleBagView or BaseClass(SafeBaseView)

function RoleBagView:InitStuffBagView(index)
	self.stuff_arrange_time = false

	if nil == self.role_info_widget then
		self:CreateRoleInfoWidget()
	end

	self.open_num = COMMON_CONSTS.MAX_STUFF_SRORGE_COUNT

	if not self.stuff_bag_grid then
		self.stuff_bag_grid = AsyncBaseGrid.New()
		self.stuff_bag_grid:SetIsShowTips(false)                       --  ,change_cells_num = 2
		self.stuff_bag_grid:CreateCells({col = 4, cell_count = self.open_num, list_view = self.node_list["list_view"],itemRender = StuffBagCell})
		self.stuff_bag_grid:SetSelectCallBack(BindTool.Bind1(self.SelectStuffBagCellCallBack, self))
	end

	XUI.AddClickEventListener(self.node_list["btn_stuff_clearup"], BindTool.Bind1(self.OnStuffBagCleanupHandler, self))

	self:FlushModel()
end

function RoleBagView:DeleteStuffBagView()
	if nil ~= self.stuff_bag_grid then
		self.stuff_bag_grid:DeleteMe()
		self.stuff_bag_grid = nil
	end

	self.fabao_model = {}
	self.role_model = {}
	self.stuff_arrange_time = nil
	if CountDownManager.Instance:HasCountDown('StuffBagCleanup') then
		CountDownManager.Instance:RemoveCountDown('StuffBagCleanup')
	end
end

function RoleBagView:SelectStuffBagCallback(index, reloaddata)
	--动画
	-- UITween.MoveToShowPanel(self.node_list["Btn_Container"], Vector2(0,-500), Vector2(0, 40.5), 0.5, DG.Tweening.Ease.Linear)
	-- UITween.MoveToShowPanel(self.node_list["Stuff_Right_Container"], Vector2(1000,0), Vector2(0, 0), 0.5, DG.Tweening.Ease.Linear)
	-- UITween.AlpahShowPanel(self.node_list["Btn_Container"], true, 1.5)
	-- UITween.AlpahShowPanel(self.node_list["Stuff_Right_Container"], true, 1.5)
	self:FlushStuffBagView()
end

function RoleBagView:SelectStuffBagCellCallBack(cell)
	if nil == cell then
		return
	end

	if self:TryOpenCell(cell) then
		return
	end

	local cell_data = cell:GetData()
	if nil == cell_data or not next(cell_data) then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(cell_data.item_id)
	if ItemWGData.GetIsXiaogGui(cell_data.item_id) then
		local time = math.max(cell_data.invalid_time - TimeWGCtrl.Instance:GetServerTime(), 0)
		local is_overdue = time <= 0 and true or false
		if is_overdue then
			TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROM_GUOQI)
		else
			TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROM_NO_GUOQI)
		end
	else
		local btn_callback_event
		local is_market = MarketWGData.Instance:CheckIsCanMarket(cell_data)
		local is_open = FunOpen.Instance:GetFunIsOpened(FunName.OtherMarket)
		if item_cfg and 0 == item_cfg.isbind and is_market and is_open then
			btn_callback_event = {}
			if cell_data.is_bind == 0 then
				btn_callback_event[1] = {btn_text = Language.Common.ShangJia, callback = function()
					--MarketWGCtrl.Instance:OpenMarketTipItemView(cell_data)
					cell_data.knapsack_type = KNAPSACK_TYPE.NORMAL
					ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.market_sell)
					MarketWGCtrl.Instance:OpenMarketTipItemView(cell_data)
				end}
			end
		end
		TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROM_BAG, nil, nil, btn_callback_event)				--打开tip,提示使用
	end
end

function RoleBagView:FlushStuffBagView()
	self.stuff_bag_grid:SetDataList(ItemWGData.Instance:GetStuffStorgeItemData(), 2)
	self.stuff_bag_grid:CancleAllSelectCell()
end

--物品变化
function RoleBagView:OnStuffBagItemDataChange(item_id, index, reason, put_reason, old_num, new_num)
	self:FlushStuffBagView()
end


-- 整理背包
function RoleBagView:OnStuffBagCleanupHandler()
	if self.stuff_arrange_time then return end

	CountDownManager.Instance:AddCountDown('StuffBagCleanup',function (time,total_time)
		time = math.modf(time)
		self.node_list.stuff_cleanup_txt.text.text = total_time - time
	end,
	function ()
		self.node_list.stuff_cleanup_txt.text.text = Language.Bag.CleanUp_1
		self.stuff_arrange_time = false
		XUI.SetButtonEnabled(self.node_list.btn_stuff_clearup,true)
		CountDownManager.Instance:RemoveCountDown('StuffBagCleanup')
	end,
	nil,5,1
	)
	self.node_list.stuff_cleanup_txt.text.text = 5
	XUI.SetButtonEnabled(self.node_list.btn_stuff_clearup,false)
	self.stuff_arrange_time = true
	BagWGCtrl.Instance:SendKnapsackStoragePutInOrder(GameEnum.STORAGER_TYPE_STUFFBAG, 0)
end

StuffBagCell = StuffBagCell or BaseClass(ItemCell)

function StuffBagCell:__init()
	self:SetItemTipFrom(ItemTip.FROM_BAG)
	self:SetCanTrade(true)
	self.need_default_eff = true
end

-- 设置是否选中
function StuffBagCell:SetSelect(is_select, item_call_back)

	if self.select_call_back and not item_call_back then
		self.select_call_back(self.index, is_select)
	end

	if not self:CanSelect() then
		if self.is_select then
			self:TrySetActive("select_effect", nil, false)
			return
		end
	end


	self.is_select = is_select
	if self.is_select then
		self:TrySetActive("select_effect", nil, false)
	else
		self:TrySetActive("select_effect", nil, false)
	end
	if not self.has_load then
		self.need_change = true
		return
	end
	self:OnSelectChange(self.is_select)
end

function StuffBagCell:OnFlush()
	ItemCell.OnFlush(self)
	local show_red = LongZhuWGData.Instance:IsRemindByItemId(self.data.item_id)
	self:SetCanOperateIconVisible(show_red)
end