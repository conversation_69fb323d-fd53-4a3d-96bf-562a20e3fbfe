-- X-新节日活动-道具收集.xls
local item_table={
[1]={item_id=37065,num=1,is_bind=1},
[2]={item_id=26554,num=1,is_bind=1},
[3]={item_id=54807,num=10,is_bind=1},
[4]={item_id=32206,num=1,is_bind=1},
[5]={item_id=27838,num=5,is_bind=1},
[6]={item_id=37246,num=1,is_bind=1},
[7]={item_id=26555,num=1,is_bind=1},
[8]={item_id=32275,num=1,is_bind=1},
[9]={item_id=38152,num=1,is_bind=1},
[10]={item_id=26556,num=1,is_bind=1},
[11]={item_id=54807,num=5,is_bind=1},
[12]={item_id=48119,num=1,is_bind=1},
[13]={item_id=27836,num=5,is_bind=1},
[14]={item_id=37524,num=1,is_bind=1},
[15]={item_id=26557,num=1,is_bind=1},
[16]={item_id=48093,num=1,is_bind=1},
[17]={item_id=27837,num=5,is_bind=1},
[18]={item_id=37649,num=1,is_bind=1},
[19]={item_id=48519,num=8,is_bind=1},
[20]={item_id=54807,num=3,is_bind=1},
[21]={item_id=27836,num=3,is_bind=1},
[22]={item_id=37751,num=1,is_bind=1},
[23]={item_id=48519,num=5,is_bind=1},
[24]={item_id=27837,num=3,is_bind=1},
[25]={item_id=26524,num=2,is_bind=1},
[26]={item_id=48519,num=3,is_bind=1},
[27]={item_id=54807,num=1,is_bind=1},
[28]={item_id=27837,num=2,is_bind=1},
[29]={item_id=26524,num=1,is_bind=1},
[30]={item_id=48519,num=2,is_bind=1},
[31]={item_id=30454,num=1,is_bind=1},
[32]={item_id=30453,num=1,is_bind=1},
[33]={item_id=30452,num=1,is_bind=1},
[34]={item_id=30451,num=1,is_bind=1},
[35]={item_id=30450,num=1,is_bind=1},
[36]={item_id=47504,num=1,is_bind=1},
[37]={item_id=48419,num=1,is_bind=1},
[38]={item_id=54807,num=2,is_bind=1},
[39]={item_id=44180,num=8,is_bind=1},
[40]={item_id=47505,num=1,is_bind=1},
[41]={item_id=48418,num=1,is_bind=1},
[42]={item_id=44180,num=6,is_bind=1},
[43]={item_id=47506,num=1,is_bind=1},
[44]={item_id=54806,num=5,is_bind=1},
[45]={item_id=44180,num=5,is_bind=1},
[46]={item_id=47507,num=1,is_bind=1},
[47]={item_id=54806,num=4,is_bind=1},
[48]={item_id=47508,num=1,is_bind=1},
[49]={item_id=48417,num=1,is_bind=1},
[50]={item_id=54806,num=3,is_bind=1},
[51]={item_id=47509,num=1,is_bind=1},
[52]={item_id=54805,num=10,is_bind=1},
[53]={item_id=47510,num=1,is_bind=1},
[54]={item_id=48416,num=1,is_bind=1},
[55]={item_id=54805,num=5,is_bind=1},
[56]={item_id=47511,num=1,is_bind=1},
[57]={item_id=32577,num=1,is_bind=1},
[58]={item_id=44180,num=10,is_bind=1},
[59]={item_id=47464,num=1,is_bind=1},
[60]={item_id=47465,num=1,is_bind=1},
[61]={item_id=47466,num=1,is_bind=1},
[62]={item_id=47467,num=1,is_bind=1},
[63]={item_id=47468,num=1,is_bind=1},
[64]={item_id=47469,num=1,is_bind=1},
[65]={item_id=47470,num=1,is_bind=1},
[66]={item_id=47471,num=1,is_bind=1},
[67]={item_id=26524,num=3,is_bind=1},
[68]={item_id=27836,num=2,is_bind=1},
[69]={item_id=32578,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=9,end_day=9999,grade=2,}
},

open_day_meta_table_map={
},
personal_reward={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{seq=1,need_num=200000,reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[3],[3]=item_table[8],[4]=item_table[5]},},
{seq=2,need_num=150000,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12],[4]=item_table[13]},},
{seq=3,need_num=120000,reward_item={[0]=item_table[14],[1]=item_table[15],[2]=item_table[11],[3]=item_table[16],[4]=item_table[17]},},
{seq=4,need_num=80000,reward_item={[0]=item_table[18],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21]},},
{seq=5,need_num=60000,reward_item={[0]=item_table[22],[1]=item_table[23],[2]=item_table[20],[3]=item_table[24]},},
{seq=6,need_num=40000,},
{seq=7,need_num=20000,reward_item={[0]=item_table[25],[1]=item_table[26],[2]=item_table[27],[3]=item_table[28]},},
{seq=8,need_num=10000,reward_item={[0]=item_table[29],[1]=item_table[30],[2]=item_table[27]},},
{grade=2,reward_item={[0]=item_table[31],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{grade=2,reward_item={[0]=item_table[32],[1]=item_table[7],[2]=item_table[3],[3]=item_table[8],[4]=item_table[5]},},
{grade=2,reward_item={[0]=item_table[33],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12],[4]=item_table[13]},},
{grade=2,reward_item={[0]=item_table[34],[1]=item_table[15],[2]=item_table[11],[3]=item_table[16],[4]=item_table[17]},},
{grade=2,reward_item={[0]=item_table[35],[1]=item_table[19],[2]=item_table[20],[3]=item_table[21]},},
{grade=2,reward_item={[0]=item_table[35],[1]=item_table[23],[2]=item_table[20],[3]=item_table[24]},},
{grade=2,},
{grade=2,},
{grade=2,}
},

personal_reward_meta_table_map={
[16]=7,	-- depth:1
[17]=8,	-- depth:1
[11]=2,	-- depth:1
[12]=3,	-- depth:1
[13]=4,	-- depth:1
[14]=5,	-- depth:1
[15]=6,	-- depth:1
[18]=9,	-- depth:1
},
jieyi_reward={
{},
{seq=1,need_num=100000,reward_item={[0]=item_table[36],[1]=item_table[37],[2]=item_table[38],[3]=item_table[39]},},
{seq=2,need_num=80000,reward_item={[0]=item_table[40],[1]=item_table[41],[2]=item_table[27],[3]=item_table[42]},},
{seq=3,need_num=60000,reward_item={[0]=item_table[43],[1]=item_table[41],[2]=item_table[44],[3]=item_table[45]},},
{seq=4,need_num=40000,reward_item={[0]=item_table[46],[1]=item_table[41],[2]=item_table[47],[3]=item_table[45]},},
{seq=5,need_num=20000,reward_item={[0]=item_table[48],[1]=item_table[49],[2]=item_table[50]},},
{seq=6,need_num=12000,reward_item={[0]=item_table[51],[1]=item_table[49],[2]=item_table[52]},},
{seq=7,need_num=4000,reward_item={[0]=item_table[53],[1]=item_table[54],[2]=item_table[55]},},
{seq=8,need_num=2000,reward_item={[0]=item_table[56],[1]=item_table[54],[2]=item_table[55]},},
{grade=2,reward_item={[0]=item_table[57],[1]=item_table[37],[2]=item_table[20],[3]=item_table[58]},},
{grade=2,reward_item={[0]=item_table[59],[1]=item_table[37],[2]=item_table[38],[3]=item_table[39]},},
{grade=2,reward_item={[0]=item_table[60],[1]=item_table[41],[2]=item_table[27],[3]=item_table[42]},},
{grade=2,reward_item={[0]=item_table[61],[1]=item_table[41],[2]=item_table[44],[3]=item_table[45]},},
{grade=2,reward_item={[0]=item_table[62],[1]=item_table[41],[2]=item_table[47],[3]=item_table[45]},},
{grade=2,reward_item={[0]=item_table[63],[1]=item_table[49],[2]=item_table[50]},},
{grade=2,reward_item={[0]=item_table[64],[1]=item_table[49],[2]=item_table[52]},},
{grade=2,reward_item={[0]=item_table[65],[1]=item_table[54],[2]=item_table[55]},},
{grade=2,reward_item={[0]=item_table[66],[1]=item_table[54],[2]=item_table[55]},}
},

jieyi_reward_meta_table_map={
[17]=8,	-- depth:1
[11]=2,	-- depth:1
[12]=3,	-- depth:1
[13]=4,	-- depth:1
[14]=5,	-- depth:1
[15]=6,	-- depth:1
[16]=7,	-- depth:1
[18]=9,	-- depth:1
},
model_show={
{},
{grade=2,model_show_itemid=30454,display_pos="-200|-100",display_scale=1.2,}
},

model_show_meta_table_map={
},
other_default_table={},

open_day_default_table={start_day=1,end_day=8,grade=1,},

personal_reward_default_table={grade=1,seq=0,need_num=250000,reward_item={[0]=item_table[67],[1]=item_table[23],[2]=item_table[38],[3]=item_table[68]},},

jieyi_reward_default_table={grade=1,seq=0,need_num=120000,reward_item={[0]=item_table[69],[1]=item_table[37],[2]=item_table[20],[3]=item_table[58]},},

model_show_default_table={grade=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=37065,display_pos="-200|-165",rotation="0|0|0",display_scale=1,}

}

