-- 日常批处理
-- 仙盟批处理
----------------------------------
TaskDailyBatchTips = TaskDailyBatchTips or BaseClass(SafeBaseView)

function TaskDailyBatchTips:__init()
	self:LoadConfig()
	self.max = 0
	self.cur_num = 0
end

function TaskDailyBatchTips:LoadConfig()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true,true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(598, 420)})
	self:AddViewResource(0, "uis/view/task_prefab", "task_daily_bath_tip")
end

function TaskDailyBatchTips:ReleaseCallBack()
	if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function TaskDailyBatchTips:CloseCallBack()
	self.task_cfg = nil
	self.max = 0
	self.cur_num = 0
	self.total_money = 0
	self.uint_money = 0
end

function TaskDailyBatchTips:LoadCallBack()
	self:SetViewName(Language.Task.daily_batch_tip_name)
	self.node_list.slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSoundValueChange, self))
	XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind1(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind1(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.OnClickCancel, self))
end

function TaskDailyBatchTips:OnFlush(param_list)
	if not param_list or not param_list.all then return end
	self.task_cfg = param_list.all
	if self.task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
		local task_data = TaskWGData.Instance:GetGuildInfo()
		self.max = not IsEmptyTable(task_data) and (TaskWGData.Instance:GetTaskGuildWeekMaxCount() - task_data.complete_task_count) or 0
		self.node_list.text_can_num.text.text = string.format(Language.Task.guild_batch_can_num, self.max)
	elseif self.task_cfg.task_type == GameEnum.TASK_TYPE_RI then
		local commint_times = DailyWGData.Instance:GetTaskCommintTimes()
		self.max = TUMO_COMMIT_TIMES - commint_times + 1
		if self.max >= TUMO_COMMIT_TIMES then
			self.max = TUMO_COMMIT_TIMES
		end
		self.node_list.text_can_num.text.text = string.format(Language.Task.daily_batch_can_num, self.max)
	end
	self.node_list.slider.slider.maxValue = self.max
	self.node_list.slider.slider.minValue = 1
	self.node_list.slider.slider.value = self.max
	self:SetCurNum(self.max)
end

function TaskDailyBatchTips:CalculationMoney()
	-- body
	local task_data = TaskWGData.Instance:GetGuildInfo()
	local start_index = math.max(1, task_data.complete_task_count)
	local end_index = task_data.complete_task_count + self.cur_num

	local other_cfg = TaskWGData.Instance:GetOtherInfo()
	local total_num = end_index - start_index
	if 1 == start_index then
		total_num = total_num + 1
	end
	local num_1 = 0
	local num_2 = 0
	local list = {}
	for i=start_index,end_index do
		if 0 == i % 10 then
			num_1 = num_1 + 1
		end
		table.insert(list, i)
	end
	num_2 = total_num - num_1

	local total_money = other_cfg.guild_task_gold * num_2 + other_cfg.commit_equip_guild_task_gold * num_1
	return total_money
end

function TaskDailyBatchTips:OnClickConfirm()
	if not self.task_cfg then return end
	-- local other_cfg = TaskWGData.Instance:GetOtherInfo()
	-- local total_gold = self.cur_num * other_cfg.daily_onekey_gold
    local function ok_callback()
    	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
    	if not main_role_vo then return end
    	if self.total_money > main_role_vo.bind_gold + main_role_vo.gold then
    		-- self:OpenRechargeAlert()
    		self:Close()
    		UiInstanceMgr.Instance:ShowChongZhiView()
    		-- ViewManager.Instance:Open(GuideModuleName.Vip, "recharge_cz")
    		return true
    	end

    	if self.task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
			GuildWGCtrl.SendFinishAllGuildTask(self.cur_num)
		elseif self.task_cfg.task_type == GameEnum.TASK_TYPE_RI then
			ActivityWGCtrl.Instance:SendTumoCommitTask(1, 0, 0, self.cur_num)
            local has_double = VipPower.Instance:GetHasPower(VipPowerId.daily_task_double)
            -- local view

            if has_double then
                TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_vip_double, is_success = true,pos = Vector2(0, 0)})
            else
                -- TaskWGCtrl.Instance:PlayEffect1()
                TaskWGCtrl.Instance:PlayEffect()
                -- if ViewManager.Instance:IsOpen(GuideModuleName.Task) then
                --     view = ViewManager.Instance:GetView(GuideModuleName.Task)
                -- elseif ViewManager.Instance:IsOpen(GuideModuleName.TaskShangJinView) then
                --     view = ViewManager.Instance:GetView(GuideModuleName.TaskShangJinView)
                -- end
                 -- FightText.Instance:TaskStateChangeText("UI_effects_wcrw_1", view and view.root_node_transform)
            end
		end

		ViewManager.Instance:Close(GuideModuleName.Task)
		self:Close()
    end


    ok_callback()
    -- 策划要求砍掉二次确认弹窗 防止之后又加回来
	-- local str = string.format(Language.Task.TaskQuickTip, self.total_money)
	-- local show_check_box = true

 --    if nil == self.alert then
 --        self.alert = Alert.New(str, ok_callback, nil, nil, show_check_box)
 --        self.alert:SetTextIsLeft(false)
 --    else
 --        self.alert:SetLableString(str)
 --        self.alert:SetOkFunc(ok_callback)
 --        self.alert:SetShowCheckBox(show_check_box)
 --    end

 --    self.alert:Open()
end

function TaskDailyBatchTips:SetCurNum(value)
	local other_cfg = TaskWGData.Instance:GetOtherInfo()
	self.cur_num = value
	self.node_list.lbl_num.text.text = value

	self.uint_money = other_cfg.daily_onekey_gold
	if not IsEmptyTable(self.task_cfg) then
		if self.task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
			self.uint_money = other_cfg.guild_task_gold
		end
	end

	self.total_money = self.cur_num * self.uint_money
	self.node_list.text_total_num.text.text = string.format(Language.Task.daily_batch_total_num, self.total_money, self.uint_money, self.cur_num)
end

function TaskDailyBatchTips:OpenRechargeAlert()
	-- body
	local function ok_callback()
		self:Close()
		UiInstanceMgr.Instance:ShowChongZhiView()
    	-- ViewManager.Instance:Open(GuideModuleName.Vip, "recharge_cz")
    	return true
    end

	local str = Language.Common.goto_recharge
	if nil == self.alert then
        self.alert = Alert.New(str, ok_callback, nil)
    else
        self.alert:SetLableString(str)
        self.alert:SetOkFunc(ok_callback)
    end
    self.alert:Open()
end

function TaskDailyBatchTips:OnClickCancel()
	self:Close()
end

function TaskDailyBatchTips:OnClickSub()
	local cur_num = self.cur_num
	if cur_num <= 1 then return end
	cur_num = cur_num - 1
	self:SetCurNum(cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function TaskDailyBatchTips:OnClickAdd()
	local cur_num = self.cur_num
	if cur_num >= self.max then return end
	cur_num = cur_num + 1
	self:SetCurNum(cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function TaskDailyBatchTips:OnSoundValueChange(float_param)
	self:SetCurNum(float_param)
end
