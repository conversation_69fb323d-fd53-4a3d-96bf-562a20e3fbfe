require("game/fubenpanel/fuben_tabbar")
require("game/fubenpanel/fubenpanel_wg_data")
require("game/fubenpanel/fubenpanel_view")
require("game/fubenpanel/fubenpanel_welkin")
require("game/fubenpanel/fubenpanel_copper_view1")
require("game/fubenpanel/fubenpanel_team_exp")
require("game/fubenpanel/fubenpanel_team_equip")
require("game/fubenpanel/fubenpanel_yuanguxiandian_view")
require("game/fubenpanel/fubenpanel_manhuanggudian")
require("game/fubenpanel/fubenpanel_zhushenta")
require("game/fubenpanel/fubenpanel_tianshen")
require("game/fubenpanel/fubenpanel_bagua")
require("game/fubenpanel/fubenpanel_bootybay")
require("game/fubenpanel/fuben_combine_view")
require("game/fubenpanel/fuben_saodang_view")
require("game/fubenpanel/day_count_change_view")
require("game/fubenpanel/fubenpanel_copper_buy")
require("game/fubenpanel/fubenpanel_copper_saodang")
--require("game/fubenpanel/fubenpanel_copper_task")
require("game/fubenpanel/copper_logic_view")
require("game/fubenpanel/fubenpanel_countdown")
require("game/fubenpanel/fubenpanel_tafang_view")
require("game/fubenpanel/fubenpanel_tafang_buy")
require("game/fubenpanel/fubenpanel_tafang_sweep_msg")
--require("game/fubenpanel/fubenpanel_pet_view")
require("game/fubenpanel/fubenpanel_pet_new_view")
require("game/fubenpanel/fubenpanel_pet_task")
require("game/fubenpanel/fubenpanel_bounty_common")
require("game/fubenpanel/fubenpanel_pet_saodang")
require("game/fubenpanel/fubenpanel_pet_buy")
require("game/fubenpanel/fubenpanel_team_exp_buy_view")
require("game/fubenpanel/guide_boss_view")
require("game/fubenpanel/fubenpanel_fanrenxiuzhen")
require("game/fubenpanel/fuben_star_ani")
require("game/fubenpanel/fuben_star_view")
require("game/fubenpanel/fubenpanel_tianshen_buy")
require("game/fubenpanel/fubenpanel_tianshen_saodang")
require("game/fubenpanel/tianshen_logic_view")
require("game/fubenpanel/fubenpanel_baguamizhen_buy")
require("game/fubenpanel/fubenpanel_baguamizhen_saodang")
require("game/fubenpanel/fubenpanel_baguamizhen_saodang")
require("game/baguamizhen/baguamizhen_task_view")
require("game/fubenpanel/manhuang_first_pass_view")
require("game/fubenpanel/fubenpanel_manhuang_buy")
require("game/fubenpanel/manhuang_logic_view")
require("game/fubenpanel/copper_fb_wg_data")
require("game/fubenpanel/fubenpanel_fanrenxiuzhen_task")
require("game/fubenpanel/fb_common_buy_view")
require("game/fubenpanel/fubenpanel_star_clock")
require("game/fuben/fuben_mingwen_open_view")
require("game/fubenpanel/exp_fuben_alert_view")
require("game/fubenpanel/fubenpanel_zhuogui_wg_data")
require("game/fubenpanel/fubenpanel_zhuogui_view")
require("game/fubenpanel/fubenpanel_zhuogui_scene_view")
require("game/fubenpanel/fubenpanel_zhuogui_task_view")
require("game/fubenpanel/fubenpanel_zhuogui_boss_scene_view")
require("game/fubenpanel/fubenpanel_zhuogui_tujie_view")
require("game/fanrenxiuzhen_reward/fanrenxiuzhen_reward_view")
require("game/fanrenxiuzhen_reward/fanrenxiuzhen_reward_preview")
require("game/fubenpanel/fubenpanel_control_beasts_view")
require("game/fubenpanel/fubenpanel_beauty_view")
require("game/fubenpanel/fubenpanel_wuhun_view")
require("game/fubenpanel/fuben_pop_dialog_view")
require("game/fubenpanel/fubenpanel_rune_tower_view")


FuBenPanelWGCtrl = FuBenPanelWGCtrl or BaseClass(BaseWGCtrl)
function FuBenPanelWGCtrl:__init()
	if FuBenPanelWGCtrl.Instance then
		error("[FuBenPanelWGCtrl]:Attempt to create singleton twice!")
	end
	FuBenPanelWGCtrl.Instance = self

	self.data = FuBenPanelWGData.New()
	self.view = FuBenPanelView.New(GuideModuleName.FuBenPanel)

	self.fb_common_buy_view = FBCommonBuyView.New()
	self.countdown = FuBenPanelCountDown.New()
	---------------铜币本------------
	self.copper_fb_data = CopperFbWGData.New()
	self.copper_buy = CopperBuyView.New(GuideModuleName.FuBenPanelBuyCountInfo)
	self.copper_saodang = CopperSaoDangView.New()
	--self.copper_task = CopperTaskView.New()
	self.copper_logic_view = CopperLogicView.New()

    self.exp_alert_view = ExpFubenAlertView.New()

	self.tafang_saodang = TaFangSweepMsgView.New()

	self.tafang_buy = TaFangBuyView.New(GuideModuleName.FuBenPanelBuyCountInfo)
	----------------赏金及新副本任务栏面板-----------------
	self.fuben_scene_panel_view = BountyTaskView.New()
	----------------------------------------
	----------------------------------
	-----宠物本----
	self.pet_task = PetTaskView.New()
	self.pet_saodang =  PetSaoDangView.New()
	self.pet_buy = PetBuyView.New(GuideModuleName.FuBenPanelBuyCountInfo)

	self.guide_boss_view = GuideBossInfoView.New()

	self.fuben_star_ani = FubenStarAniView.New(GuideModuleName.FubenStarAniView)
	self.fuben_star_view = FuBenStarView.New(GuideModuleName.FuBenStarView)

	-----------八卦阵 ---------
	self.baguazhen_buy = BaGuaMiZhenBuyView.New(GuideModuleName.FuBenPanelBuyCountInfo)
	self.baguazhen_task_view = BaGuaMiZhenTaskView.New(GuideModuleName.BaGuaMiZhenTaskView)
	--凡人修真task
	self.fanrenxiuzhen_task_view = FanRenXiuZhenTask.New(GuideModuleName.FanRenXiuZhenTask)
	self.fanrenxiuzhen_reward_view = FanrenxiuzhenRewardView.New(GuideModuleName.FanRenXiuZhenRewardView)
	self.fanrenxiuzhen_reward_preview = FanrenxiuzhenRewardPreview.New(GuideModuleName.FanrenxiuzhenRewardPreview)
	--战骑本
	-- self.simple_task_view = SmipleTaskView.New()

	-- 多人经验本
	self.team_exp_buy = TeamExpBuy.New(GuideModuleName.FuBenPanelBuyCountInfo)

	-- self.common_saodang_view = CommonSaoDangView.New()
	-- self.common_vipbuy_view = CommonBuyView.New()


	self.fanrenxiuzhen_challenge_tip = FanRenXiuZhenChallengeTips.New()
	self.fanrenxiuzhen_box_tip =FanRenXiuZhenBoxTips.New()
	self.fanrenxiuzhen_reward_show_tip = FanRenXiuZhenRewardShowTip.New()

    --天神
    self.tianshen_buy_view = TianShenBuyView.New(GuideModuleName.FuBenPanelTianShenBuyView)
	self.tianshen_saodang_view = TianShenSaoDangView.New(GuideModuleName.FuBenPanelTianShenSaoDangView)
	self.tianshen_logic_view = TianShenLogicView.New(GuideModuleName.TianShenLogicView)
	--八卦阵
	self.baguamizhen_saodang_view = BaGuaMiZhenSaoDangView.New(GuideModuleName.FuBenPanelBaGuaMiZhenSaoDangView)
	self.baguamizhen_buy_view = BaGuaMiZhenBuyView.New(GuideModuleName.FuBenPanelBaGuaMiZhenBuyView)

	--蛮荒古殿首通奖励界面
	self.manhuang_first_pass_view = ManHuangFirstPassView.New(GuideModuleName.ManHuangGuDianFirstPassView)
    self.manhuang_buy_view = ManHuangBuyView.New(GuideModuleName.ManHuangGuDianBuyView)
	self.manhuang_logic_view = ManHuangLogicView.New(GuideModuleName.ManHuangGuDianLogicView)

	self.mingwen_open_view = FuBenMingWenOpenView.New()

	self.fubenpanel_zhuogui_scene_view = FuBenPanelZhouGuiSceneView.New()
	self.fubenpanel_zhuogui_task_view = FuBenPanelZhuoGuiTaskView.New()
	self.fubenpanel_zhuogui_boss_view = FuBenPanelZhouGuiBossSceneView.New()
	self.fubenpanel_zhuogui_tujie_view = FuBenPanelZhouGuiTuJieView.New()
	self.fuben_pop_dialog_view = FuBenPopDialogView.New(GuideModuleName.FuBenPopDialogView)

	self.is_welkinremind = true
	self:RegisterAllProtocals()

	self.is_show_end_time = false
	self.can_show_tuijian = true
	self:BindGlobalEvent(SceneEventType.CLOSE_LOADING_VIEW,BindTool.Bind(self.SceneLoadComplete,self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function FuBenPanelWGCtrl:__delete()
	self.baguazhen_buy:DeleteMe()
	self.fanrenxiuzhen_task_view:DeleteMe()
	self.fanrenxiuzhen_reward_view:DeleteMe()
	self.fanrenxiuzhen_reward_preview:DeleteMe()
	
	self.baguazhen_task_view:DeleteMe()
	if nil ~= self.fuben_scene_panel_view then
		self.fuben_scene_panel_view:DeleteMe()
		self.fuben_scene_panel_view = nil
	end

	if nil ~= self.pet_task then
		self.pet_task:DeleteMe()
		self.pet_task = nil
	end
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.copper_fb_data then
		self.copper_fb_data:DeleteMe()
		self.copper_fb_data = nil
	end

	if nil ~= self.copper_buy then
		self.copper_buy:DeleteMe()
		self.copper_buy = nil
	end

	if nil ~= self.copper_saodang then
		self.copper_saodang:DeleteMe()
		self.copper_saodang = nil
	end

	if nil ~= self.fuben_star_ani then
		self.fuben_star_ani:DeleteMe()
		self.fuben_star_ani = nil
	end

	if nil ~= self.fuben_star_view then
		self.fuben_star_view:DeleteMe()
		self.fuben_star_view = nil
	end

	if nil ~= self.pet_saodang then
		self.pet_saodang:DeleteMe()
		self.pet_saodang = nil
	end

	if nil ~= self.tafang_saodang then
		self.tafang_saodang:DeleteMe()
		self.tafang_saodang = nil
	end

	--if nil ~= self.copper_task then
	--	self.copper_task:DeleteMe()
	--	self.copper_task = nil
	--end

	if nil ~= self.copper_logic_view then
		self.copper_logic_view:DeleteMe()
		self.copper_logic_view = nil
    end

    if nil ~= self.exp_alert_view then
		self.exp_alert_view:DeleteMe()
		self.exp_alert_view = nil
    end

	if nil ~= self.fb_common_buy_view then
		self.fb_common_buy_view:DeleteMe()
		self.fb_common_buy_view = nil
	end

	if nil ~= self.tafang_buy then
		self.tafang_buy:DeleteMe()
		self.tafang_buy = nil
	end

	if nil ~= self.countdown then
		self.countdown:DeleteMe()
		self.countdown = nil
	end

	if nil ~= self.fanrenxiuzhen_challenge_tip then
		self.fanrenxiuzhen_challenge_tip:DeleteMe()
		self.fanrenxiuzhen_challenge_tip = nil
	end

	if nil ~= self.fanrenxiuzhen_box_tip then
		self.fanrenxiuzhen_box_tip:DeleteMe()
		self.fanrenxiuzhen_box_tip = nil
	end

	if nil ~= self.tianshen_buy_view then
		self.tianshen_buy_view:DeleteMe()
		self.tianshen_buy_view = nil
	end
    if nil ~= self.manhuang_buy_view then
		self.manhuang_buy_view:DeleteMe()
		self.manhuang_buy_view = nil
	end
	if nil ~= self.manhuang_logic_view then
		self.manhuang_logic_view:DeleteMe()
		self.manhuang_logic_view = nil
	end
	if nil ~= self.baguamizhen_buy_view then
		self.baguamizhen_buy_view:DeleteMe()
		self.baguamizhen_buy_view = nil
	end

	if nil ~= self.manhuang_first_pass_view then
		self.manhuang_first_pass_view:DeleteMe()
		self.manhuang_first_pass_view = nil
	end

	if nil ~= self.tianshen_saodang_view then
		self.tianshen_saodang_view:DeleteMe()
		self.tianshen_saodang_view = nil
	end
	if nil ~= self.baguamizhen_saodang_view then
		self.baguamizhen_saodang_view:DeleteMe()
		self.baguamizhen_saodang_view = nil
	end

	if self.fuben_pop_dialog_view then
		self.fuben_pop_dialog_view:DeleteMe()
		self.fuben_pop_dialog_view = nil
	end

	if self.copper_delay then
		GlobalTimerQuest:CancelQuest(self.copper_delay)
	end
	if self.pet_delay then
		GlobalTimerQuest:CancelQuest(self.pet_delay)
	end
	if self.Welkin_delay then
		GlobalTimerQuest:CancelQuest(self.Welkin_delay)
	end
	if self.welkin_jiesuo then
		GlobalTimerQuest:CancelQuest(self.welkin_jiesuo)
	end
	if self.copper_lose_delay then
		GlobalTimerQuest:CancelQuest(self.copper_lose_delay)
	end

	if self.fm_fb_delay then
		GlobalTimerQuest:CancelQuest(self.fm_fb_delay)
		self.fm_fb_delay = nil
	end

	if self.tuijian_timequest then
		GlobalTimerQuest:CancelQuest(self.tuijian_timequest)
		self.tuijian_timequest = nil
	end

	if self.reshow_tuijian_tip_quest then
		GlobalTimerQuest:CancelQuest(self.reshow_tuijian_tip_quest)
		self.reshow_tuijian_tip_quest = nil
	end

	if nil ~= self.pet_buy then
		self.pet_buy:DeleteMe()
		self.pet_buy = nil
	end

	self.guide_boss_view:DeleteMe()
	self.guide_boss_view = nil


	if nil ~= self.team_exp_buy then
		self.team_exp_buy:DeleteMe()
		self.team_exp_buy = nil
	end

    if nil ~= self.tianshen_logic_view then
        self.tianshen_logic_view:DeleteMe()
        self.tianshen_logic_view = nil
    end

	-- if self.common_saodang_view then
	-- 	self.common_saodang_view:DeleteMe()
	-- 	self.common_saodang_view = nil
	-- end

	-- if self.common_vipbuy_view then
	-- 	self.common_vipbuy_view:DeleteMe()
	-- 	self.common_vipbuy_view = nil
	-- end

	if self.mingwen_open_view then
		self.mingwen_open_view:DeleteMe()
		self.mingwen_open_view = nil
	end

	self.fubenpanel_zhuogui_scene_view:DeleteMe()
	self.fubenpanel_zhuogui_scene_view = nil

	self.fubenpanel_zhuogui_task_view:DeleteMe()
	self.fubenpanel_zhuogui_task_view = nil

	self.fubenpanel_zhuogui_boss_view:DeleteMe()
	self.fubenpanel_zhuogui_boss_view = nil

	if self.fubenpanel_zhuogui_tujie_view then
		self.fubenpanel_zhuogui_tujie_view:DeleteMe()
		self.fubenpanel_zhuogui_tujie_view = nil
	end

	if self.fanrenxiuzhen_reward_show_tip then
		self.fanrenxiuzhen_reward_show_tip:DeleteMe()
		self.fanrenxiuzhen_reward_show_tip = nil
	end

	FuBenPanelWGCtrl.Instance = nil
	self.liujie_zhengba_mark = nil
end

function FuBenPanelWGCtrl:MainuiOpenCreate()
	self:SendPataFbInfo()
	self:SendTongBiFbOperate(COIN_FB_OPERA_TYPE.COIN_FB_OPERA_TYPE_GET_FB_INFO)
	FuBenWGCtrl.Instance:SendManHuangGuDianReq(MANHUANGGUDIAN_REQ_TYPE.BASE_INFO)
    FuBenWGCtrl.Instance:SendManHuangGuDianReq(MANHUANGGUDIAN_REQ_TYPE.RANK_INFO)
end

function FuBenPanelWGCtrl:OpenStarAniView(data)
	self.fuben_star_view:SetData(data)
	self.fuben_star_view:Open()
end

function FuBenPanelWGCtrl:CloseStarAniView()
	self.fuben_star_view:Close()
end

function FuBenPanelWGCtrl:GetCurStarNum()
	return self.data.Instance:GetFubenStarNum()
end

function FuBenPanelWGCtrl:FlushStarAniView()
	self.fuben_star_view:Flush()
end

function FuBenPanelWGCtrl:GetFuBenPanemView()
	return self.view
end
function FuBenPanelWGCtrl:SceneLoadComplete()
	if self.is_show_xinmo_skill_index and self.is_show_xinmo_skill_index >= 8 then
		-- local data = {}
		local skill_cfg = SkillWGData.Instance:GetPassiveSkillByIndex(self.is_show_xinmo_skill_index)
		if skill_cfg then
	 		local data = {name = skill_cfg.name, desc = skill_cfg.desc, res_fun = ResPath.GetSkillIconById, icon = skill_cfg.icon}
			TipWGCtrl.Instance:ShowGetNewSkillView2(data)
		end
		self.is_show_xinmo_skill_index = nil
	end
end


function FuBenPanelWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSNewFBOption)

	--天仙阁
	self:RegisterProtocol(CSTianxiangeFbOperate)
	self:RegisterProtocol(CSFbReqRecover)
	self:RegisterProtocol(SCTianxiangeInfo, "OnTianxiangeInfo")
	self:RegisterProtocol(SCTianxiangeSceneInfo, "OnTianxiangeSceneInfo")
	self:RegisterProtocol(SCCommondataInfo, "OnSCCommondataInfo")

	--铜币本(龙王宝藏副本)
	self:RegisterProtocol(SCCoinFBInfo, "OnCoinFBInfo")
	self:RegisterProtocol(SCRoleCoinFBInfo,"OnRoleCoinFBInfo")
	self:RegisterProtocol(CSCoinFBReq)

	--宠物本
	self:RegisterProtocol(CSNewPetFbReq)
	self:RegisterProtocol(SCNewPetFbSceneInfo, "OnChongWuAllFBInfo")
	self:RegisterProtocol(SCNewPetFbOtherInfo, "OnOtherChongWuFBInfo")
	self:RegisterProtocol(SCNewPetFbRewarInfo, "OnPetFbRewarInfo")

	--心魔副本
	self:RegisterProtocol(SCXinMoFBFinishInfo, "OnXinMoFBFinishInfo")
	self:RegisterProtocol(SCXinMoFBRoleInfo, "OnXinMoFBRoleInfo")

	--诛神塔副本界面
	self:RegisterProtocol(SCKillGodTowerInfo, "OnSCKillGodTowerInfo")

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
	self:BindGlobalEvent(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DaycountChange, self))
	self.role_attr_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_attr_change, {"level"})

	--天神副本
	self:RegisterProtocol(CSTianShenFbBuyTimesReq)
	self:RegisterProtocol(SCTianShenFbChapterInfo, "OnSCTianShenFbChapterInfo")
	--八卦迷阵副本
	self:RegisterProtocol(CSBaGuaMiZhenFbBuyTimesReq)

	-- 捉鬼副本
	self:RegisterProtocol(CSGhostFbOperate)
	self:RegisterProtocol(SCGhostFbBaseInfo, "OnGhostFbBaseInfo")
	self:RegisterProtocol(SCGhostFbTaskInfo, "OnGhostFbTaskInfo")
	self:RegisterProtocol(SCGhostFbTaskUpdate, "OnGhostFbTaskUpdate")
	self:RegisterProtocol(SCGhostFbEventInfo, "OnGhostFbEventInfo")
	self:RegisterProtocol(SCGhostFbPersonSceneInfo, "OnGhostFbPersonSceneInfo")
end

--option_type类型（0：进入副本、1：扫荡、2：购买次数、3：进入下一关、4：领取宝箱奖励）
function FuBenPanelWGCtrl:OnCSNewFBOption(option_type, fb_type, param)

	-- 起跳动作
	local scene_type = Scene.Instance:GetSceneType()
	if option_type == 0 or option_type == 3 then
		if option_type == 3 then
			if scene_type == 0 then
				option_type = 0
			end
		end
		Scene.Instance:GetMainRole():JumpUpToFuBenSceneChange(function ()
			Scene.Instance:SetRoleChangeSceneEff(true)

			-- 请求进入副本
			local protocol = ProtocolPool.Instance:GetProtocol(CSNewFBOption)
			protocol.option_type = option_type or 0
			protocol.type = fb_type or 0
			protocol.param = param or 0
			protocol:EncodeAndSend()
		end)
	else
		-- 请求进入副本
		local protocol = ProtocolPool.Instance:GetProtocol(CSNewFBOption)
		protocol.option_type = option_type or 0
		protocol.type = fb_type or 0
		protocol.param = param or 0
		protocol:EncodeAndSend()
	end
end

function FuBenPanelWGCtrl:Open(tab_index, param_t, key)
	-- if FunOpen.Instance:GetFunIsOpened(FunName.FuBenPanel) then
		-- self.view:Open(tab_index, param_t)
		ViewManager.Instance:Open(GuideModuleName.FuBenPanel, tab_index, key, param_t)
		--print_error()
	-- end
end

function FuBenPanelWGCtrl:Flush(index, key, value)
	if self.view:IsOpen() then
		self.view:Flush(index, key, value)
	end
end

function FuBenPanelWGCtrl:OpenCopperBuy(is_once)
	self.copper_buy:Open()
end

function FuBenPanelWGCtrl:CheckOpenCloseTip()
	if self.fanrenxiuzhen_challenge_tip:IsOpen() then
		self.fanrenxiuzhen_challenge_tip:Close()
	end
end

function FuBenPanelWGCtrl:CheckOpenCloseBoxTip()
	if self.fanrenxiuzhen_box_tip:IsOpen() then
		self.fanrenxiuzhen_box_tip:Close()
	end
end

function FuBenPanelWGCtrl:OpenPetBuy(is_once, fuben_type)
	is_once = is_once or false
	fuben_type = fuben_type or FUBEN_TYPE.FBCT_PETBEN
	self.pet_buy:SetIsOnce(is_once)
	self.pet_buy:SetFubenInfo({fb_type = fuben_type})
	self.pet_buy:Open()
end

function FuBenPanelWGCtrl:SetCopperSaoDangIndex(index)
	self.copper_saodang:SetCopperIndex(index)
end

function FuBenPanelWGCtrl:OpenCopperTaskView()
	self.copper_logic_view:Open()
	--self.copper_logic_view:SetTaskActive()
end

function FuBenPanelWGCtrl:OpenExpAlertView(str, okfunc, cancel_func)
    self.exp_alert_view:SetLableString(str)
    self.exp_alert_view:SetCancelFunc(cancel_func)
    self.exp_alert_view:SetOkFunc(okfunc)
    self.exp_alert_view:Open()
end

function FuBenPanelWGCtrl:CloseCopperTaskView()
	self.exp_alert_view:Close()
end

function FuBenPanelWGCtrl:FlushCopperTaskView()
	self.copper_logic_view:Flush()
	--self.copper_logic_view:SetTaskActive()
end

function FuBenPanelWGCtrl:DoStarAniMove()
	--self.copper_task:FirstMoveAni()
end

function FuBenPanelWGCtrl:CloseCopperTaskView()
	--self.copper_logic_view:ResetTaskPanel()
	self.copper_logic_view:Close()
end


--宠物本
function FuBenPanelWGCtrl:SetPetSaoDangIndex(index)
	self.pet_saodang:SetPetIndex(index)
end
function FuBenPanelWGCtrl:OpenPetTaskView()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.PET_FB and scene_type ~= SceneType.FakePetFb then
		return
	end

	self.pet_task:Open()
end

function FuBenPanelWGCtrl:ClosePetTaskView()
	self.pet_task:Close()
end

function FuBenPanelWGCtrl:OpenTeamExpBuyView(fb_type)
	self:OpenBuyView(fb_type)
	-- self.team_exp_buy:SetFbType(fb_type)
	-- self.team_exp_buy:Open()
end

function FuBenPanelWGCtrl:CloseTeamExpBuyView()
	self.team_exp_buy:Close()
end

function FuBenPanelWGCtrl:OpenGuideBossView()
	self.guide_boss_view:Open()
end

function FuBenPanelWGCtrl:FlushGuideBossView()
	self.guide_boss_view:Flush()
end

function FuBenPanelWGCtrl:CloseGuideBossView()
	self.guide_boss_view:Close()
end

----------------八卦迷阵-----------
function FuBenPanelWGCtrl:OpenBaGuanMiZhenBuy()
	self.baguazhen_buy:Open()
end

------------------------------------------天仙阁---------------------------------
function FuBenPanelWGCtrl:WelkinClose()
	if self.view then
		self.view:Close()
	end
end
function FuBenPanelWGCtrl:SendPataFbInfo()
	self:SendTianxiangeFbOperate(PATA_FB_OPERA_TYPE.TIANXIANGEFB_REQ_INFO)
end

function FuBenPanelWGCtrl:SendPataFbRewrad(seq)
	self:SendTianxiangeFbOperate(PATA_FB_OPERA_TYPE.TIANXIANGEFB_FETCH_DAILY_REWARD, seq)
end

--凡人修真领取宝箱奖励
function FuBenPanelWGCtrl:SendLevelFetchReq(level)
	self:SendTianxiangeFbOperate(FAN_REN_XIU_ZHEN_FB_OPERA_TYPE.LEVEL, level)
end

--凡人修真领取章节奖励
function FuBenPanelWGCtrl:SendChapterFetchReq(chapter)
	self:SendTianxiangeFbOperate(FAN_REN_XIU_ZHEN_FB_OPERA_TYPE.CHPATER, chapter)
end


function FuBenPanelWGCtrl:SendTianxiangeFbOperate(operate, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianxiangeFbOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end


function FuBenPanelWGCtrl:OpenChallengeTip(data)
	self.fanrenxiuzhen_challenge_tip:SetData(data)
end

function FuBenPanelWGCtrl:OpenBoxTip(data)
	self.fanrenxiuzhen_box_tip:SetData(data)
end


function FuBenPanelWGCtrl:OnTianxiangeInfo(protocol)

	-- Log("---------FuBenPanelWGCtrl:OnTianxiangeInfo")
	-- print_error("天仙阁",protocol.level, protocol.fetch_level)
	local pass_level = protocol.level
	local pass_fetch_level = protocol.fetch_level
	self.data:SetPassLevel(pass_level)
	self.data:SetPassFetchLevel(pass_fetch_level)
	self.data:SetLevelRewardFlag(protocol.level_reward_flag)
    RankWGCtrl.Instance:SendRankListReq(PersonRankType.TianXianGeRank, function()
		self:Flush(TabIndex.fubenpanel_welkin)
    end)

	RoleWGData.Instance:SetAttr("tianxiange_level", pass_level)

	-- Scene.Instance:GetMainRole():UpdateNameBoard()

	 -- self.view:OnFlushWelkinPanel()
	 self:Flush(TabIndex.fubenpanel_welkin, nil, {xz_normal_flush_all = true})
	RemindManager.Instance:Fire(RemindName.TianXianFuBen)
	self:CacularTuiJianTimer()
    MainuiWGCtrl.Instance:FlushTaskView()
	ViewManager.Instance:FlushView(GuideModuleName.FanrenxiuzhenRewardPreview)
	ViewManager.Instance:FlushView(GuideModuleName.FanRenXiuZhenTask)
end

function FuBenPanelWGCtrl:OnSCCommondataInfo(protocol)
	local tianxia_level_reward_sign = protocol.tianxia_level_reward_sign
	local tianxia_chapter_reward_sign = protocol.tianxia_chapter_reward_sign

	local passlevel = {};
	local passchapter = {}
	local function parse(tab_src, tab)
	  	for index, char in ipairs(tab_src) do
		    for i = 0, 7 do
			    local pass_level = i + (index - 1) * 8

			    if bit:_and(char, bit:_lshift(1, i)) ~= 0 then
			    	-- 通关
			        tab[pass_level] = 1
			    else
			      	tab[pass_level] = 0
			    end
		    end
	  	end
	end

	parse(tianxia_level_reward_sign, passlevel)
	parse(tianxia_chapter_reward_sign, passchapter)
	self.data:SetRewardInfo(passlevel, passchapter)

	self:Flush(TabIndex.fubenpanel_welkin, "xz_fetch_result", {xz_fetch_result = true})
	RemindManager.Instance:Fire(RemindName.TianXianFuBen)
end

function FuBenPanelWGCtrl:OnTianxiangeSceneInfo(protocol)
	self.data:SceneTXGInfo(protocol)
	self.fanrenxiuzhen_task_view:Flush()
	self.is_welkinremind = false
	if 0 ~= protocol.monster_flush_time then
		if CountDownManager.Instance:HasCountDown("level_time_countdown") then
			CountDownManager.Instance:RemoveCountDown("level_time_countdown")
		end

		local pos_x, pos_y = 33, 42
		if protocol.pos_x ~= 0 and protocol.pos_y ~= 0 then
			pos_x, pos_y = protocol.pos_x, protocol.pos_y
		end

		FuBenPanelWGCtrl.Instance:SendRecoverHp()
		GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), pos_x, pos_y)
		UiInstanceMgr.Instance:DoFBStartDown(protocol.monster_flush_time, function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Fb_Welkin and protocol.monster_flush_time == 0 then
		FuBenWGCtrl.Instance:SetOutFbTime(protocol.time_out_stamp, true, true)
	end
	
	if protocol.is_finish == 1 then
		FuBenWGCtrl.Instance:SetOutFbTime(0, true, true)
		self:TianxiangeSceneFinishView()
	end
end

function FuBenPanelWGCtrl:TianxiangeSceneFinishView()

	local welkin_info = FuBenPanelWGData.Instance:GetWelkinInfo()
	local pass_vo = FuBenWGData.CreateCommonPassVo()
	pass_vo.scene_type = SceneType.Fb_Welkin
	pass_vo.is_pass = welkin_info.is_pass
	pass_vo.reward_list = {}
	pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(welkin_info.pass_time_s), COLOR3B.GREEN , 24)
	pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(self.data:GetCurTitleName(welkin_info.level), COLOR3B.YELLOW , 24)
	pass_vo.tip3 = Language.FuBen.NextLevel
	if 0 == welkin_info.is_pass then
		pass_vo.reward_list = {}
		pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
	end
	pass_vo.reward_list = FuBenPanelWGData.Instance:GetFanRenXiuZhenPassRewardItemList() or welkin_info.reward_item
	pass_vo.is_guide = welkin_info.param_1 == 1 		-- 是否是通过引导进入的
	pass_vo.hunjing = welkin_info.hunjing_num
	if 0 ~= pass_vo.is_pass then
		if self.Welkin_delay then
			GlobalTimerQuest:CancelQuest(self.Welkin_delay)
			self.Welkin_delay = nil
		end
		self.Welkin_delay = GlobalTimerQuest:AddDelayTimer(function ()
			local scne_type = Scene.Instance:GetSceneType()
			if scne_type == SceneType.Fb_Welkin then
				local have_open,open_list = FuBenPanelWGData.Instance:GetLevelChapterRewardItem(welkin_info.level)
				local slot_data = FuBenPanelWGData.Instance:GetLevelChapterOpenSlot(welkin_info.level)
				local confirm_call_back = function ()
					if pass_vo.is_guide then
						FuBenPanelWGData.Instance:AddGuideFinishTimes()
					end
					FuBenWGCtrl.Instance:OpenFuBenNextView(pass_vo)
				end

				if have_open then
					self.mingwen_open_view:SetShowData(open_list, slot_data)
					self.mingwen_open_view:SetConfirmCallBack(confirm_call_back)
					self.mingwen_open_view:Open()
				else
					confirm_call_back()
				end
			end
		end, 1)
	else
		GuajiWGCtrl.Instance:StopGuaji()
		FuBenWGCtrl.Instance:OpenLose(SceneType.Fb_Welkin, pass_vo.auto_exit_time)
	end
end

function FuBenPanelWGCtrl:OnTianxiangeSeq(protocol)
	self.data:GetTXGSeq(protocol)
end

function FuBenPanelWGCtrl:OpenFanRenTaskView()
	self.fanrenxiuzhen_task_view:Open()
end

function FuBenPanelWGCtrl:CloseFanRenTaskView()
	self.fanrenxiuzhen_task_view:Close()
end

function FuBenPanelWGCtrl:FlushFanRenTaskView()
	self.fanrenxiuzhen_task_view:Flush()
end

function FuBenPanelWGCtrl:OpenFanRenXiuZhenRewardView()
	self.fanrenxiuzhen_reward_view:Open()
end

function FuBenPanelWGCtrl:OpenFanRenXiuZhenRewardPreview(index)
	self.fanrenxiuzhen_reward_preview:SetCurIndex(index)
	self.fanrenxiuzhen_reward_preview:Open()
end


------------------------铜币本------------------------------
function FuBenPanelWGCtrl:OnRoleCoinFBInfo(protocol)
	CopperFbWGData.Instance:SetTongBiInfo(protocol)
	self:Flush(TabIndex.fubenpanel_copper)
    FuBenWGCtrl.Instance:FlushCombinePanel()
    self:FlushBuyView()
	RemindManager.Instance:Fire(RemindName.LongWangBaoZang)
end

function FuBenPanelWGCtrl:SendTongBiFbOperate(opera_type,param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCoinFBReq)
	protocol.opera_type = opera_type or 0
	protocol.param = param1 or 0
	protocol:EncodeAndSend()
end


function FuBenPanelWGCtrl:OnCoinFBInfo(protocol)
	if protocol.is_leave_fb == 0 and protocol.is_pass == 0 then
		if protocol.prepare_end_timestamp - TimeWGCtrl.Instance:GetServerTime() > 0 and protocol.cur_wave <= 1 then
			MainuiWGCtrl.Instance:SetShowTimeTextState( false )
			UiInstanceMgr.Instance:DoFBStartDown(protocol.prepare_end_timestamp + 1,function ()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)
		end
	end
	--print_error("铜币本 ", protocol.next_star_timestamp, protocol.next_star_timestamp - TimeWGCtrl.Instance:GetServerTime(), protocol.scene_timeout_timestamp - TimeWGCtrl.Instance:GetServerTime(), protocol)
	CopperFbWGData.Instance:SetCopperScenceInfo(protocol)
	FuBenPanelWGCtrl.Instance:FlushStarAniView()
	self:FlushCopperTaskView()
	--倒计时
	if protocol.scene_timeout_timestamp - TimeWGCtrl.Instance:GetServerTime() > 0 then
		FuBenWGCtrl.Instance:SetOutFbTime(protocol.scene_timeout_timestamp, true, true) --主界面倒计时
	end

	--退出场景
	if protocol.is_finish == 1 and protocol.is_pass == 1 and protocol.is_sweep == 0 then
		self.is_show_end_time = false
		local item_list = CopperFbWGData.Instance:GetDropList(protocol.item_list, protocol.cur_star_num, protocol.get_item_num)
		FuBenWGCtrl.Instance:SetCommonWinData(SceneType.COPPER_FB, item_list, protocol.get_exp, protocol.get_coin, protocol.cur_star_num, 10)
		return
	end

	--扫荡
	if protocol.is_sweep == 1 then
		FuBenWGCtrl.Instance:SetCommonWinData(SceneType.COPPER_FB, protocol.item_list, protocol.get_exp, protocol.get_coin, protocol.cur_star_num, 10, nil, false)
		self.copper_saodang:Close()
		if self.copper_saodang:IsOpen() then
			self.copper_saodang:Flush()
		end
		return
	end

	--失败
	if protocol.is_pass == 0 and protocol.is_finish == 1 then
		FuBenWGCtrl.Instance:OpenLose(SceneType.COPPER_FB)
	end
end

function FuBenPanelWGCtrl:OpenTaFangBuy()
	self.tafang_buy:Open()
end

function FuBenPanelWGCtrl:OpenTaFangSweepMsg()
	self.tafang_saodang:Open()
end

------------------------------------------------------
--赏金任务--四个副本
------------------------------------------------------
function FuBenPanelWGCtrl:OpenBountyTaskView()
	local mainui_ctrl = MainuiWGCtrl.Instance
	mainui_ctrl:AddInitCallBack(nil, function ( )
        mainui_ctrl:SetTaskContents(false)
        mainui_ctrl:SetOtherContents(true)
		mainui_ctrl:SetTeamBtnState(false) --组队按钮
	end)
	self.fuben_scene_panel_view:Open()
end

function FuBenPanelWGCtrl:CloseBountyTaskView()
	local mainui_ctrl = MainuiWGCtrl.Instance
	mainui_ctrl:AddInitCallBack(nil, function ( )
        mainui_ctrl:SetTaskContents(true)
        mainui_ctrl:SetOtherContents(false)
		mainui_ctrl:SetTeamBtnState(true) --组队按钮
	end)
	self.fuben_scene_panel_view:Close()
end

function FuBenPanelWGCtrl:VistWordImg(monster_id)
	if self.fuben_scene_panel_view:IsOpen() then
		self.fuben_scene_panel_view:VistWordImg(monster_id)
	end
end
--任务副本
function FuBenPanelWGCtrl:FlushBountyTaskView(protocol)
	self.data:SetFubenPanelData(protocol)
	if 1 ~= protocol.is_finish then
		if 0 ~= protocol.flush_timestamp then
	   			UiInstanceMgr.Instance:DoFBStartDown(protocol.flush_timestamp, function()
	      			MainuiWGCtrl.Instance:SetFbIconEndCountDown(protocol.time_out_stamp)
	      			end)
	   	end
	   	if protocol.total_allmonster_num == 3 and protocol.kill_allmonster_num == 2 then
			Scene.SendGetAllObjMoveInfoReq()
		end
	end
	if self.fuben_scene_panel_view:IsOpen() then
		self.fuben_scene_panel_view:Flush()
	end

end
--宠物本
function FuBenPanelWGCtrl:SendChongWuFbOperate(req_type,param_1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSNewPetFbReq)
	protocol.req_type = req_type or 0
	protocol.param_1 = param_1 or 0
	protocol:EncodeAndSend()
end

function FuBenPanelWGCtrl:OnChongWuAllFBInfo(protocol)
	-- print_error(protocol.prepare_end_timestamp - TimeWGCtrl.Instance:GetServerTime())
	self.data:SetPetScenceInfo(protocol)
	if self.pet_task:IsOpen() then
		self.pet_task:Flush()
	end
	self:FlushStarAniView()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.PET_FB or scene_type == SceneType.FakePetFb then
		--print_error("==",protocol.finish_timestamp,TimeWGCtrl.Instance:GetServerTime())
		FuBenWGCtrl.Instance:SetOutFbTime(protocol.finish_timestamp, true, true)
	end

	if protocol.is_finish == 1 then
		FuBenWGCtrl.Instance:SetOutFbTime(0, true, true) --隐藏退出按钮下的倒计时
		if protocol.is_pass == 1 then
			-- local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + 10
			-- FuBenPanelCountDown.Instance:SetTimerInfo(out_fb_time,function()
			-- end, {x = 0, y = -50})
		else
			--FuBenWGCtrl.Instance:OpenFuBenLoseView()
			FuBenWGCtrl.Instance:OpenLose(Scene.Instance:GetSceneType())
		end
	end

	--FuBenWGData.Instance:GetSetFBPrepareTime( protocol.prepare_end_timestamp + 1 )
	if protocol.prepare_end_timestamp > 0 and protocol.prepare_end_timestamp > TimeWGCtrl.Instance:GetServerTime() then
		-- UiInstanceMgr.Instance:ShowFBStartDown2(protocol.prepare_end_timestamp)
		self:SetPetFBPrepareTime(protocol.prepare_end_timestamp)
		self.is_pet_sceneani_move = true
		UiInstanceMgr.Instance:DoFBStartDown(protocol.prepare_end_timestamp + 1)
	end

	if protocol.is_pass == 0 and self.fake_petfb_status ~= protocol.status and protocol.status == 1 then
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:ClearAllOperate()
		local dialog_view = ViewManager.Instance:GetView(GuideModuleName.TaskDialog)
		if nil ~= dialog_view and not dialog_view:IsOpen() then
			dialog_view:SetStoryNpcId(108, function ()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)

			dialog_view:Open()
		end
	end
	self.fake_petfb_status = protocol.status
end

function FuBenPanelWGCtrl:OnOtherChongWuFBInfo(protocol)
	--print_error("宠物本信 息 >>>>>>>>>> ", protocol)
	self.data:SetPetAllInfo(protocol)
	self:Flush(TabIndex.fubenpanel_pet, "pet_buy_or_enter_times", {pet_buy_or_enter_times = true})
    self:FlushBuyView()
	if self.pet_buy:IsOpen() then
		self.pet_buy:Flush()
	else
		self:Flush(TabIndex.fubenpanel_pet)
	end
	RemindManager.Instance:Fire(RemindName.XianLingShengDian)
end

function FuBenPanelWGCtrl:OnPetFbRewarInfo(protocol)
	self.data:SetPetFBReward(protocol)
	if protocol.is_pass == 1 then
		if 1 == protocol.is_sweep_fb then
			FuBenWGCtrl.Instance:SetCommonWinData(SceneType.PET_FB, protocol.item_list, protocol.get_exp, protocol.get_coin, protocol.star_num, 15)
			self.pet_saodang:Close()
		else
			FuBenWGCtrl.Instance:SetCommonWinData(Scene.Instance:GetSceneType(), protocol.item_list, protocol.get_exp, protocol.get_coin, protocol.star_num, 15)
		end
	end
end

function FuBenPanelWGCtrl:SetCountDowmTimer(finish_timestamp,call_back)
	self.countdown:SetTimerInfo(finish_timestamp,call_back)
end

function FuBenPanelWGCtrl:SetCountDowmType( str ,type )
	self.countdown:SetTipTxtStr( str ,type)
end

function FuBenPanelWGCtrl:CloseCountDowmView()
	if self.countdown:IsOpen() then
		self.countdown:CloseViewHandler()
	end
end

function FuBenPanelWGCtrl:OnXinMoFBFinishInfo(protocol)
	self.data:SetDemonsInfo(protocol)
	local prepare_end_timestamp = protocol.fb_next_refresh_timestamp - TimeWGCtrl.Instance:GetServerTime()
	if prepare_end_timestamp > 0 then
		UiInstanceMgr.Instance:DoFBStartDown(protocol.fb_next_refresh_timestamp,function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			local scene_type = Scene.Instance:GetSceneType()
			if scene_type == SceneType.DEMONS_FB then
				local finish_timestamp = self.data:GetDemonsInfo().fb_finish_timestamp
				MainuiWGCtrl.Instance:SetFbIconEndCountDown(finish_timestamp)
			end
		end)
	end

	if 1 == protocol.is_finish then
		FuBenPanelCountDown.Instance:SetTimerInfo(protocol.kick_out_timestamp)

		if self.demons_delay then
			GlobalTimerQuest:CancelQuest(self.demons_delay)
		end
		if protocol.is_pass == 1 then
			self.is_show_xinmo_skill_index = protocol.skill_index
			local out_fb_time = protocol.kick_out_timestamp - TimeWGCtrl.Instance:GetServerTime()
			FuBenWGCtrl.Instance:OpenWin(SceneType.DEMONS_FB, protocol.reward_item_list, protocol.get_exp, protocol.get_coin, 3, out_fb_time, protocol.skill_index)
		else
			FuBenWGCtrl.Instance:OpenLose(SceneType.DEMONS_FB)
		end
	end
end

function FuBenPanelWGCtrl:OnXinMoFBRoleInfo(protocol)
	self.data:SetXinMoFBRoleInfo(protocol)
end

function FuBenPanelWGCtrl:DaycountChange()
	self:Flush()
end

function FuBenPanelWGCtrl:CheckCopperCount()
	return self.view:CheckCopperCount()
end

function FuBenPanelWGCtrl:OpenSimpleTaskView()
	-- self.simple_task_view:Open()
end

function FuBenPanelWGCtrl:FlushSimpleTaskView()
	-- if self.simple_task_view:IsOpen() then
	-- 	self.simple_task_view:Flush()
	-- end
end

function FuBenPanelWGCtrl:CloseSimpleTaskView()
	-- self.simple_task_view:Close()
end

function FuBenPanelWGCtrl:OpenCommonSaoDangView(param)
	-- self.common_saodang_view:SetData(param)
end

function FuBenPanelWGCtrl:OpenCommonVipBuyView(param)
	-- self.common_vipbuy_view:SetData(param)
end

function FuBenPanelWGCtrl:GetCommonVipBuyIsOpen()
	-- return self.common_vipbuy_view:IsOpen()
end
--诛神塔
function FuBenPanelWGCtrl:OnSCKillGodTowerInfo(protocol)
	-- self.data:SetZhuShenTaData(protocol)
	-- self:Flush(TabIndex.fubenpanel_zhushen_ta)
end

function FuBenPanelWGCtrl:SetTianXianGeLeaveType(type)
	self.tian_xian_ge_leave_type = type
end

function FuBenPanelWGCtrl:GetTianXianGeLeaveType(type)
	return self.tian_xian_ge_leave_type
end

function FuBenPanelWGCtrl:GetPetLayer()
	return self.view.pet_layer or 0
end

function FuBenPanelWGCtrl:OnRoleAttrChange(key, value , old_value)
	if key == "level" then
		self:Flush(TabIndex.fubenpanel_exp)
	end
end

function FuBenPanelWGCtrl:GetIsCountDowning()
	return self.countdown:IsOpen()
end

function FuBenPanelWGCtrl:IsCheckCanEnterFb()
	local team_list = SocietyWGData.Instance:GetTeamMemberList()
    for k,v in pairs(team_list) do
    	if v.role_id ~= RoleWGData.Instance:InCrossGetOriginUid() then
	    	local scene_cfg = ConfigManager.Instance:GetSceneConfig(v.scene_id)
	    	if scene_cfg and scene_cfg.scene_type ~= SceneType.Common then
	    		return false
	    	end
	    else
	    	local scene_type = Scene.Instance:GetSceneType()
	    	if scene_type ~=  SceneType.Common then
	    		return false
	    	end
	    end
    end
    return true
end

function FuBenPanelWGCtrl:SetPetFBPrepareTime(prepare_time_stamp)
	if prepare_time_stamp then
		self.pet_prepare_time_stamp = prepare_time_stamp
		return
	end
	if self.pet_prepare_time_stamp then
		return self.pet_prepare_time_stamp -  TimeWGCtrl.Instance:GetServerTime() > 0
	else
		return false
	end
end


function FuBenPanelWGCtrl:SetLiuJieZhengBaMark()
	if IS_ON_CROSSSERVER then
		self.liujie_zhengba_mark = true
	end
end

function FuBenPanelWGCtrl:GetCurChapterPage()
	return self.view:GetCurChapterPage()
end

--天神副本请求购买副本进入次数
function FuBenPanelWGCtrl:TianShenFbBuyTimesReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenFbBuyTimesReq)
	protocol:EncodeAndSend()
end

--八卦迷阵副本请求购买副本进入次数
function FuBenPanelWGCtrl:BaGuaMiZhenFbBuyTimesReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSBaGuaMiZhenFbBuyTimesReq)
	protocol:EncodeAndSend()
end

--天神副本通关信息
function FuBenPanelWGCtrl:OnSCTianShenFbChapterInfo(protocol)
	--print_error("天神副本通关信息 ", protocol)
	FuBenPanelWGData.Instance:SetTianShenPassLayer(protocol.cur_layer)
	FuBenPanelWGData.Instance:SetTianShenStarCount(protocol.cur_layer_star)
end

function FuBenPanelWGCtrl:OpenTianShenBuy()
    ViewManager.Instance:Open(GuideModuleName.FuBenPanelTianShenBuyView)
end
--八卦迷阵购买次数界面
function FuBenPanelWGCtrl:OpenBaGuaMiZhenBuy()
    ViewManager.Instance:Open(GuideModuleName.FuBenPanelBaGuaMiZhenBuyView)
end
function FuBenPanelWGCtrl:OpenManHuangGuDianBuy()
    ViewManager.Instance:Open(GuideModuleName.ManHuangGuDianBuyView)
end

function FuBenPanelWGCtrl:OpenTianShenSaoDang(layer)
	self.tianshen_saodang_view:SetPetIndex(layer)
end
function FuBenPanelWGCtrl:SetBaGuaMiZhenSaoDangIndex(layer)
	self.baguamizhen_saodang_view:SetPetIndex(layer)
end

function FuBenPanelWGCtrl:OpenTianShenLogicView()
    ViewManager.Instance:Open(GuideModuleName.TianShenLogicView)
end
function FuBenPanelWGCtrl:CloseTianShenLogicView()
    ViewManager.Instance:Close(GuideModuleName.TianShenLogicView)
end

function FuBenPanelWGCtrl:FlushTianShenLogicView()
    self.tianshen_logic_view:Flush()
end


function FuBenPanelWGCtrl:OpenManHuangLogicView()
	ViewManager.Instance:Open(GuideModuleName.ManHuangGuDianLogicView)
end
function FuBenPanelWGCtrl:CloseManHuangLogicView()
    ViewManager.Instance:Close(GuideModuleName.ManHuangGuDianLogicView)
end
function FuBenPanelWGCtrl:FlushManHuangLogicView()
	ViewManager.Instance:FlushView(GuideModuleName.ManHuangGuDianLogicView)
end

function FuBenPanelWGCtrl:OpenBuyView(data)
	self.fb_common_buy_view:SetData(data)
end
function FuBenPanelWGCtrl:CloseBuyView()
	if self.fb_common_buy_view:IsOpen() then
		self.fb_common_buy_view:Close()
	end
end
function FuBenPanelWGCtrl:FlushBuyView()
	self.fb_common_buy_view:Flush()
end

--跨天，请求一下铜币本信息
function FuBenPanelWGCtrl:OnPassDay()
	GlobalTimerQuest:AddDelayTimer(function()
		self:SendTongBiFbOperate(COIN_FB_OPERA_TYPE.COIN_FB_OPERA_TYPE_GET_FB_INFO)
	end, 5)
end

function FuBenPanelWGCtrl:SendRecoverHp()
	local protocol = ProtocolPool.Instance:GetProtocol(CSFbReqRecover)
	protocol:EncodeAndSend()
end


function FuBenPanelWGCtrl:CheckShowTuiJian()
	if not self.can_show_tuijian then 
		return 
	end

	local check_time ,check_level = self.data:GetFBTuiJianChallengeData()
	if GameVoManager.Instance:GetMainRoleVo().level < check_level then
		return
	end

	if self.data:IsReachMaxLevel() then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.Common then
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	if scene_id == BootyBayWGData.Instance:GetBootyBaySceneId() then
		return
	end

	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(pass_level + 1)
	if IsEmptyTable(cfg) then 
		return 
	end
	
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
	if mainrole_vo.capability < cfg.capability or mainrole_vo.level < cfg.open_level then
		return
	else
		local data = {}
		data.name = Language.FuWen.MingWenGe..cfg.monster_name
		local cap_num,cap_str = CommonDataManager.ConverExpFBNum(cfg.capability)
		data.desc = string.format(Language.FanRenXiuZhen.TuiJianCapability1, cap_num .. cap_str)
		data.boss_id = cfg.boss_id
		data.click_call_back = function ()
			-- FuBenWGData.Instance:SetTXGEnter(true)
			-- FuBenWGCtrl.Instance:SendEnterWelkinFb(false)
			if not ViewManager.Instance:IsOpenByIndex(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_welkin) then
				ViewManager.Instance:Open(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_welkin)
			end
		end
		FuBenWGCtrl.Instance:OpenFuBenNoticeView(data)
	end
end

function FuBenPanelWGCtrl:DealWithTuiJianCloseType(close_type)
	if self.reshow_tuijian_tip_quest then return end

	if self.tuijian_timequest then
		GlobalTimerQuest:CancelQuest(self.tuijian_timequest)
		self.tuijian_timequest = nil
	end

	if close_type then
		self.can_show_tuijian = false
		local check_time ,check_level ,re_check_time = self.data:GetFBTuiJianChallengeData()
		if re_check_time == -1 then return end

		if self.reshow_tuijian_tip_quest then
			GlobalTimerQuest:CancelQuest(self.reshow_tuijian_tip_quest)
			self.reshow_tuijian_tip_quest = nil
		end

		self.reshow_tuijian_tip_quest =  GlobalTimerQuest:AddRunQuest(function()
			self.can_show_tuijian = true
			self:CacularTuiJianTimer()
			GlobalTimerQuest:CancelQuest(self.reshow_tuijian_tip_quest)
			self.reshow_tuijian_tip_quest = nil
		end,re_check_time)
	else
		self:CacularTuiJianTimer()
	end
end

function FuBenPanelWGCtrl:CacularTuiJianTimer()
	if nil == self.tuijian_timequest then
		local check_time = self.data:GetFBTuiJianChallengeData()
		if check_time == -1 then
			return
		end
		self.tuijian_timequest = GlobalTimerQuest:AddRunQuest(function()
			self:CheckShowTuiJian()
		end, check_time)
	end
end

function FuBenPanelWGCtrl:FlushCurIndex()
	if self.view:IsOpen() then
		self.view:FlushCurIndex()
	end
end





-- ======================= 捉鬼 =====================================
-- GHOST_FB_OPERATE_TYPE
function FuBenPanelWGCtrl:SendZhuoGuiOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGhostFbOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function FuBenPanelWGCtrl:OnGhostFbBaseInfo(protocol)
	-- print_error("----捉鬼信息----", protocol)
	self.data:SetGhostFbBaseInfo(protocol)
	self:Flush(TabIndex.fubenpanel_zhuogui)
	self:FlushZhuoGuiFubenSceneView()
end

function FuBenPanelWGCtrl:OnGhostFbTaskInfo(protocol)
	-- print_error("----捉鬼任务信息----")
	self.data:SetGhostFbTaskInfo(protocol)
	self:Flush(TabIndex.fubenpanel_zhuogui)
	self:FlushZhuoGuiFubenTaskView()
	RemindManager.Instance:Fire(RemindName.ZhuoGuiFuBen)
end

function FuBenPanelWGCtrl:OnGhostFbTaskUpdate(protocol)
	-- print_error("----捉鬼任务信息更新----")
	self.data:SetGhostFbTaskUpdate(protocol)
	self:Flush(TabIndex.fubenpanel_zhuogui)
	self:FlushZhuoGuiFubenTaskView()
	RemindManager.Instance:Fire(RemindName.ZhuoGuiFuBen)
end

function FuBenPanelWGCtrl:OnGhostFbEventInfo(protocol)
	-- print_error("----捉鬼事件信息----", protocol)
	self.data:SetGhostFbEventInfo(protocol)
	self:FlushZhuoGuiFubenSceneView()
end

function FuBenPanelWGCtrl:OnGhostFbPersonSceneInfo(protocol)
	-- print_error("----捉鬼个人Boss----", Scene.Instance:GetSceneType())
	self.data:SetGhostFbPersonSceneInfo(protocol)

	if SceneType.GHOST_FB_PERSON == Scene.Instance:GetSceneType() then
		local info = protocol.info
		local out_time = info.kick_out_time > 0 and info.kick_out_time or info.fb_end_time
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_time, true)
	end
end

function FuBenPanelWGCtrl:GetZhuoGuiFubenSceneView()
	return self.fubenpanel_zhuogui_scene_view
end

function FuBenPanelWGCtrl:OpenZhuoGuiFubenSceneView()
	self.fubenpanel_zhuogui_scene_view:Open()
end

function FuBenPanelWGCtrl:CloseZhuoGuiFubenSceneView()
	self.fubenpanel_zhuogui_scene_view:Close()
end

-- 刷新界面
function FuBenPanelWGCtrl:FlushZhuoGuiFubenSceneView(key, param_t)
	if self.fubenpanel_zhuogui_scene_view:IsOpen() then
		self.fubenpanel_zhuogui_scene_view:Flush(0, key, param_t)
	end
end

function FuBenPanelWGCtrl:FindZhuoGuiNpc()
	if Scene.Instance:GetSceneType() ~= SceneType.GHOST_FB_GLOBAL then
		return
	end

	local state = self.data:GetZhuoGuiEventSatus()
	if state ~= GHOST_FB_EVENT_STATUS.NONE then
		return
    end

	local other_cfg = self.data:GetZhuoGuiOtherCfg()
	GuajiWGCtrl.Instance:StopGuaji(nil, true)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(nil, nil, true)
	GuajiWGCtrl.Instance:MoveToNpc(other_cfg.npcid, nil, Scene.Instance:GetSceneId())
end

function FuBenPanelWGCtrl:OpenZhuoGuiFubenTaskView()
	self.fubenpanel_zhuogui_task_view:Open()
end

function FuBenPanelWGCtrl:FlushZhuoGuiFubenTaskView(key, param_t)
	if self.fubenpanel_zhuogui_task_view:IsOpen() then
		self.fubenpanel_zhuogui_task_view:Flush(0, key, param_t)
	end
end

function FuBenPanelWGCtrl:OpenZhuoGuiBossScenceView()
	self.fubenpanel_zhuogui_boss_view:Open()
end

function FuBenPanelWGCtrl:CloseZhuoGuiBossScenceView()
	self.fubenpanel_zhuogui_boss_view:Close()
end

function FuBenPanelWGCtrl:OpenZhuoGuiBossTuJieView()
	self.fubenpanel_zhuogui_tujie_view:Open()
end

function FuBenPanelWGCtrl:OpenFanRenXiuZhenRewardShowTip(data)
	if self.fanrenxiuzhen_reward_show_tip then
		self.fanrenxiuzhen_reward_show_tip:SetDataAndOpen(data)
	end
end

-- 打开一个副本引导弹窗
function FuBenPanelWGCtrl:OpenFuBenGuidePopDialog(data, name, model)
	self.fuben_pop_dialog_view:SetOpenData(data, name, model)
	if not self.fuben_pop_dialog_view:IsOpen() then
		self.fuben_pop_dialog_view:Open()
	else
		self.fuben_pop_dialog_view:Flush()
	end
end

-- 打开一个副本引导弹窗
function FuBenPanelWGCtrl:CloseFuBenGuidePopDialog()
	if self.fuben_pop_dialog_view:IsOpen() then
		self.fuben_pop_dialog_view:Close()
	end
end