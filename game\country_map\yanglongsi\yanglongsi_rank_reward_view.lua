YangLongSiRankRewardView = YangLongSiRankRewardView or BaseClass(SafeBaseView)

function YangLongSiRankRewardView:__init()
	self:SetMaskBg()

	local common_bundle_name = "uis/view/common_panel_prefab"
	local bundle_name = "uis/view/country_map_ui/yanglonsi_ui_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(942, 590)})
	self:AddViewResource(0, bundle_name, "layout_yanglongsi_rank_reward")
end

function YangLongSiRankRewardView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.YangLongSi.RankRewardTitle
	self.node_list.label_reward_text.text.text = Language.YangLongSi.RankRewarDesc

	self.toggle_list = {}
	self.toggle_index = -1

	if not self.yanglongsi_rank_reward_list then
		self.yanglongsi_rank_reward_list = AsyncListView.New(YangLongSiRankRewardItem, self.node_list.yanglongsi_rank_reward_list)
	end

	self:FlushToggle()
end

function YangLongSiRankRewardView:FlushToggle()
	for i = 1, 5 do
		local data = YangLongSiaWGData.Instance:GetRewardCfgBySeq(i)
		local has_data = not IsEmptyTable(data)

		if has_data then
			local render = YangLongSiToggleItem.New(self.node_list["toggle" .. i])
			render:SetIndex(i)
			render:SetData({name = YangLongSiaWGData.Instance:GetBossNameBySeq(i)})
			self.toggle_list[i] = render

			self.node_list["toggle" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickToggle, self, i))
		end

		self.node_list["toggle" .. i]:SetActive(has_data)
	end
end

function YangLongSiRankRewardView:ShowIndexCallBack()
	self.node_list["toggle1"].toggle.isOn = true
end

function YangLongSiRankRewardView:OnClickToggle(index, is_on)
	if index ~= self.toggle_index and is_on then
		self.toggle_index = index
		local data = YangLongSiaWGData.Instance:GetRewardCfgBySeq(index)

		if not IsEmptyTable(data) then
			self.yanglongsi_rank_reward_list:SetDataList(data)
		end
	end
end

function YangLongSiRankRewardView:ReleaseCallBack()
    if not IsEmptyTable(self.toggle_list) then
        for k, v in pairs(self.toggle_list) do
            v:DeleteMe()
        end
    end

	if self.yanglongsi_rank_reward_list then
		self.yanglongsi_rank_reward_list:DeleteMe()
		self.yanglongsi_rank_reward_list = nil
	end
end

YangLongSiToggleItem = YangLongSiToggleItem or BaseClass(BaseRender)

function YangLongSiToggleItem:OnFlush()
	if not IsEmptyTable(self.data) then
		self.node_list.Text.text.text = self.data.name
		self.node_list.Text_hl.text.text = self.data.name
	end
end

YangLongSiRankRewardItem = YangLongSiRankRewardItem or BaseClass(BaseRender)

function YangLongSiRankRewardItem:__init()
	if not self.rank_reward_list then
		self.rank_reward_list = AsyncListView.New(ItemCell, self.node_list.rank_reward_list)
		self.rank_reward_list:SetStartZeroIndex(true)
	end
end

function YangLongSiRankRewardItem:__delete()
	if self.rank_reward_list then
		self.rank_reward_list:DeleteMe()
		self.rank_reward_list = nil
	end
end

function YangLongSiRankRewardItem:OnFlush()
	if not IsEmptyTable(self.data) then
		local rank_str = ""
		local min_rank = self.data.min_rank
		local max_rank = self.data.max_rank

		if min_rank == max_rank then
			rank_str = string.format(Language.YangLongSi.RankRewardRankOne, min_rank)
		else
			rank_str = string.format(Language.YangLongSi.RankRewardRankTwo, min_rank, max_rank)
		end

		self.node_list.rank_id.text.text = rank_str
		self.rank_reward_list:SetDataList(self.data.reward_item)
	end
end