WorldsNO1TimetableView = WorldsNO1TimetableView or BaseClass(SafeBaseView)

function WorldsNO1TimetableView:__init()
    self:SetMaskBg()
	self:LoadConfig()
    self.default_index = TabIndex.worlds_no1_single_match_reward
end

function WorldsNO1TimetableView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/worlds_no1_ui_prefab", "layout_worlds_no1_timetable")
end

function WorldsNO1TimetableView:OpenCallBack()
	-- 请求获取轮次信息
	WorldsNO1WGCtrl.Instance:SendRequestRoundInfo()
end

function WorldsNO1TimetableView:ReleaseCallBack()
	if self.subround_list then
		self.subround_list:DeleteMe()
		self.subround_list = nil
	end
	self.round_index = nil
end

function WorldsNO1TimetableView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["right_btn"], BindTool.Bind(self.OnClickRightBtn, self)) 
	XUI.AddClickEventListener(self.node_list["left_btn"], BindTool.Bind(self.OnClickLeftBtn, self)) 
	self.node_list["title_view_name"].text.text = Language.WorldsNO1.RoundTitle
	self:SetSecondView(nil, self.node_list["size"])
	self.subround_list = AsyncListView.New(WorldsNO1SubroundItem, self.node_list["subround_list"])  	-- 场次列表

	-- 选择当前最新的轮次
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLDS_NO1) then
		self.round_index = WorldsNO1WGData.Instance:GetCurRound()
	else
		self.round_index = WorldsNO1WGData.Instance:GetNextRound()
	end
	
end

function WorldsNO1TimetableView:ShowIndexCallBack(index)
	self:Flush(index)
end

function WorldsNO1TimetableView:OnFlush()
	local index = self:GetShowIndex()
	local data_list = WorldsNO1WGData.Instance:GetRoundCfg(self.round_index)
	self.subround_list:SetDataList(data_list)

	local timetable = WorldsNO1WGData.Instance:GetRoundTimeInfo(self.round_index)
	self.node_list["round_desc"].text.text = data_list[1].round_name .. string.format(Language.WorldsNO1.DayStr2, timetable.open_time_tab.month, timetable.open_time_tab.day)
end

function WorldsNO1TimetableView:OnClickRightBtn()
	local round_cfg = WorldsNO1WGData.Instance:GetRoundCfg()
	self.round_index = math.min(self.round_index + 1, #round_cfg)
	self:Flush()
end

function WorldsNO1TimetableView:OnClickLeftBtn()
	local round_cfg = WorldsNO1WGData.Instance:GetRoundCfg()
	self.round_index = math.max(self.round_index - 1, 1)
	self:Flush()
end

------------------------------- 子轮节点 ----------------------------------------
WorldsNO1SubroundItem = WorldsNO1SubroundItem or BaseClass(BaseRender)
function WorldsNO1SubroundItem:__init()
end

function WorldsNO1SubroundItem:__delete()
end

function WorldsNO1SubroundItem:OnFlush()
	if self.data then
		-- 轮次
		self.node_list["subround_index"].text.text = self.data.subround

		-- 对战时间
		self.node_list["fight_time"].text.text = self.data.subround_show_time

		-- 对战状态
		local subround_status = WorldsNO1WGData.Instance:GetSubroundStatus(self.data.round, self.data.subround)
		self.node_list["fight_status"].text.text = Language.WorldsNO1.StatusStr[subround_status]

		self.node_list["bg"]:SetActive(self.index % 2 == 1)
	end
end