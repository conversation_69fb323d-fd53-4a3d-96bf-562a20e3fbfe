BossView = BossView or BaseClass(SafeBaseView)
local UIOverrideOrder = typeof(UIOverrideOrder)
BossView.CELLNORHIGH = 130
BossView.HIDECELLHIGH = 32
BossView.HIDELISTHIGH = 293
BossView.SGTWEENTIME = 0.3

BossView.ANGLE_DELTA = 22
BossView.DRAG_COUNT = 3

BossViewIndex = {
	WorldBoss = TabIndex.boss_world, --荒蛮魔谷
	VipBoss = TabIndex.boss_vip,  --混沌魔域
	PersonalBoss = TabIndex.boss_personal,
	DabaoBoss = TabIndex.boss_dabao,
	SGBOSS = TabIndex.worserv_boss_sgyj,
	MiJingBoss = TabIndex.boss_mijing,
	DropRecord = TabIndex.boss_drop_record,

	KFBoss = TabIndex.worserv_boss_mh,
	ShengYuBoss = TabIndex.boss_shengyu,
	KFDropRecord = TabIndex.kuafu_drop_record,
	<PERSON>YuanBoss = TabIndex.world_new_shenyuan_boss,
	BOSSTJ = TabIndex.boss_tujian,
}

BossView.ReqType = {
	ENTER = 0,
	ALLINFO = 1,
	CONCERN = 2,
	SINGLELAYERINFO = 2,
	OTHER_CONCERN = 3,
	DROP_HISTORY = 3,
	WORLD_BOSS_REFRESH_SCENE_DIE_TIME = 4,
	VIP_BOSS_RECEIVE_XIANLI = 4,     --param1 --
	WORLD_BOSS_BUY_VIP_ENTER_TIMES = 6, --请求购买进入次数
}

BossView.KfReqType = {
	ALLINFO = 0,
	KILLRECORD = 1,
	DROPRECORD = 2,
	CONCERN = 3,
	UNCONCERN = 4,
	CROSS_BOSS_REFRESH_SCENE_DIE_TIME = 6,
	CROSS_BOSS_BUY_COUNT = 7,
}

BossView.MJKFReqType = {
	GETINFO = 0,
	KILL_RECORD = 1,
	DROP_RECORD = 2,
	CONCERN = 3,
	UNCONCERN = 4,
	FORENOTICE = 5,
}

function BossView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	local boss_bundel = "uis/view/boss_ui_prefab"
	self.ui_config = { boss_bundel, "BossUi" }
	--self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, boss_bundel, "layout_world_boss_model")
	self:AddViewResource(0, boss_bundel, "layout_world_boss")
	self:AddViewResource(BossViewIndex.WorldBoss, boss_bundel, "layout_common_right")
	self:AddViewResource(BossViewIndex.VipBoss, boss_bundel, "layout_common_right")
	self:AddViewResource(BossViewIndex.VipBoss, boss_bundel, "layout_vip_boss_1")
	self:AddViewResource(BossViewIndex.DabaoBoss, boss_bundel, "layout_dabaoboss_right")
	self:AddViewResource(BossViewIndex.DabaoBoss, boss_bundel, "layout_dabao_tip")
	--self:AddViewResource(BossViewIndex.MiJingBoss, boss_bundel, "layout_common_right")
	self:AddViewResource(BossViewIndex.PersonalBoss, boss_bundel, "layout_per_boss_1")
	self:AddViewResource(BossViewIndex.WorldBoss, boss_bundel, "layout_world_boss_1")
	self:AddViewResource(0, boss_bundel, "layout_boss_panel_up")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")
	self:AddViewResource(0, boss_bundel, "layout_boss_common_btn") --公共显示按钮

	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.BOSS_ZI})

	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	self.boss_count_remind_callback = BindTool.Bind1(self.RemindChangeCallback, self)
	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
	self.cur_layer = 1
	self.remind_tab = {
		{ RemindName.Boss_Vip }, { RemindName.Boss_Per }, { RemindName.Boss_World }, { RemindName.Boss_Dabao }
	}
	self.tab_sub = {}
	--self.default_index = TabIndex.boss_world
	self.is_safe_area_adapter = true

	self.layer_index = nil
	self.select_item_data = nil

	self.boss_display_model = nil
	self.open_tween = nil
	self.close_tween = nil
	self.is_load_common = false

	self.old_boss_id = nil

	self.boss_cambered_list = nil
	self.boss_index = 1
	self.boss_drag_select_index = 1
	self.cell_list = nil
end

function BossView:__delete()
end

function BossView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self.select_item_data = nil

	if self.select_world_delay then
		GlobalTimerQuest:CancelQuest(self.select_world_delay)
		self.select_world_delay = nil
	end

	RemindManager.Instance:UnBind(self.boss_remind_callback)
	RemindManager.Instance:UnBind(self.remind_callback)

	for i = 1, 2 do
		if self.cell_list and self.cell_list[i] then
			self.cell_list[i]:DeleteMe()
			self.cell_list[i] = nil
		end
	end
	self.cell_list = nil

	if self.layer_btn_list then
		self.layer_btn_list:DeleteMe()
		self.layer_btn_list = nil
	end


	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	if self.common_countdown_timer then
		GlobalTimerQuest:CancelQuest(self.common_countdown_timer)
		self.common_countdown_timer = nil
	end

	if nil ~= self.boss_display_model then
		self.boss_display_model:DeleteMe()
		self.boss_display_model = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.boss_cambered_list then
		self.boss_cambered_list:DeleteMe()
		self.boss_cambered_list = nil
	end

	self.per_alert_view = nil
	self.cur_layer = 1
	self.layout_boss_info = nil
	self.layout_rare_fall_item = nil
	self.boss_anim = nil
	self.state = nil
	self.cache_resid = nil
	self.btn_play_des_load = nil
	self.is_load_common = false
	self.old_boss_id = nil
	self.cell_old_list = nil

	self.boss_index = nil
	self.boss_drag_select_index = -1

	self:DeleteWorldBossView()
	self:DeleteDabaoBossView()
	self:DeletePerBossView()
	self:DeleteShengyuBossView()

	if ItemWGData.Instance ~= nil then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	end

	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Boss, self.get_guide_ui_event)
	end
end

function BossView:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
	local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(SceneType.WorldBoss)
	if cfg and cfg.consume_item and item_id == cfg.consume_item then
		if self.node_list.world_boss_effect then
			self.node_list.world_boss_effect:SetActive(new_num > 0)
		end
	end
end

function BossView:FlushWorldBossEffect()
	local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(SceneType.WorldBoss)
	if cfg and cfg.consume_item then
		local num = ItemWGData.Instance:GetItemNumInBagById(cfg.consume_item)
		if self.node_list.world_boss_effect then
			self.node_list.world_boss_effect:SetActive(num > 0)
		end
	end
end

function BossView:OpenCallBack()
end

function BossView:CloseCallBack()
	BossWGData.Instance:SetCurrentLocalServerShowIndex(nil)
	BossWGData.Instance:SetBossTuJianIndex(nil, nil, nil)
	self.layer_index = nil
	self.boss_index = nil
	BossWGCtrl.Instance:CloseAllRecordView()
	TaskGuide.Instance:SpecialConditions(true, 5)
end

function BossView:SetRendering(value)
	SafeBaseView.SetRendering(self, value)
end

function BossView:CreateOnceLoad()
	self:CreateToggleList()
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Boss, self.get_guide_ui_event)
	self.node_list["btn_record"].button:AddClickListener(BindTool.Bind(self.OnClickRecord, self))
	self.node_list["btn_open_zhanling"].button:AddClickListener(BindTool.Bind(self.OnClickZhanLing, self))
	self.node_list["btn_open_privilege"].button:AddClickListener(BindTool.Bind(self.OnClickTeQuan, self))
	self.node_list.title_view_name.text.text = Language.ViewName.Boss

	if self.node_list.boss_kill_every_btn then
		XUI.AddClickEventListener(self.node_list["boss_kill_every_btn"], BindTool.Bind(self.OnClickBossKillEvery, self))
	end
	XUI.AddClickEventListener(self.node_list["btn_mwxz"], BindTool.Bind(self.OnClickMWXZBtn, self))
	XUI.AddClickEventListener(self.node_list["button_award_tip"], BindTool.Bind(self.OnClickAwardTip, self))
    XUI.AddClickEventListener(self.node_list.button_pc_shtq, BindTool.Bind(self.OnClickPrivilegeCollectionSHTQBtn, self))
	-- self.remind_callback = BindTool.Bind(self.OnRemindCallBack, self)
    -- RemindManager.Instance:Bind(self.remind_callback, RemindName.LoadEveryDayShop)
end

function BossView:OnRemindCallBack(remind_name, num)
    if remind_name == RemindName.LoadEveryDayShop then
        self.node_list["red_point_mwxz"]:SetActive(num > 0)
    end
end

function BossView:LoadCallBack()
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	local bundle, asset = ResPath.GetRawImagesJPG("a3_boss_bgzi")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if nil == self.boss_display_model then
		self.boss_display_model = RoleModel.New()
		self.boss_display_model:SetUISceneModel(self.node_list["world_boss_model"].event_trigger_listener,
								MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.boss_display_model, 0)
	end

	self:CreateOnceLoad()

	self.boss_remind_callback = BindTool.Bind(self.BossRemindCallback, self)
	RemindManager.Instance:Bind(self.boss_remind_callback, RemindName.Boss_ZhanLing)				-- 战令

	--列表的箭头显示.
	self.node_list.ph_btn_list.scroller.scrollerEndScrolled = BindTool.Bind(self.BossScrollerEndScrolled, self)
end

function BossView:LoadIndexCallBack(index)
	self.node_list.ph_world_boss_cambered_list_1:SetActive(true)
	self.node_list.ph_world_boss_cambered_list_2:SetActive(false)

	local bundle, asset = ResPath.GetRawImagesPNG("a3_boss_bgzizz")
	self.node_list.ph_world_boss_cambered_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.ph_world_boss_cambered_bg.raw_image:SetNativeSize()
	end)

	self:CreatePagesView(index)

	if index == BossViewIndex.VipBoss then
		self.node_list.top_title_bg_1:SetActive(true)
		self.node_list.top_title_bg_2:SetActive(false)
		self.node_list.middle_title_bg_1:SetActive(true)
		self.node_list.middle_title_bg_2:SetActive(false)

		self.node_list["vipboss_xianli_add"].button:AddClickListener(BindTool.Bind1(self.AddXianli, self))
		self.node_list["vipboss_xianli_btn"].button:AddClickListener(BindTool.Bind1(self.ShowXianliRule, self))
		self.node_list["boss_cost_xianli_btn"].button:AddClickListener(BindTool.Bind1(self.ShowXianliRule, self))
		-- self:SetVipBossLayerBtn()

		if self.node_list["btn_vip_firstkill"] then
			self.node_list["btn_vip_firstkill"].button:AddClickListener(BindTool.Bind1(self.BossFirstKillClick, self)) --Boss首杀弹窗
		end
	end

	self:LoadBossBtn()
	if index == BossViewIndex.WorldBoss then
		self:FlushWorldBossEffect()
	end
end

--列表的箭头显示.
function BossView:BossScrollerEndScrolled()
	local val = self.node_list.ph_btn_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.boss_left_arrow:SetActive(val ~= 0 and val > 0.1)
	self.node_list.boss_right_arrow:SetActive(val ~= 0 and val < 0.9)
end

function BossView:ShowXianliRule()
	local title = Language.Boss.BossXianliTitile
	local content = Language.Boss.BossXianliContent
	RuleTip.Instance:SetContent(content, title)
end

function BossView:AddXianli()
	local can_get = BossWGData.Instance:JudgeCanGetBossXianli()
	if can_get then
		BossAssistWGCtrl.Instance:OpenGetEnergyView()
	else
		BossAssistWGCtrl.Instance:OpenQuickUseEnergyView()
	end
end

function BossView:LoadBossBtn()
	if self.node_list["btn_play_des"] and not self.btn_play_des_load then
		self.node_list["btn_play_des"].button:AddClickListener(BindTool.Bind1(self.BrowsePlayInfo, self))
		self.btn_play_des_load = true
	end
end

function BossView:SetVipBossLayerBtn()
	local container = self.node_list["ph_btn_list"].scroll_rect.content
	if not IsNil(container) then
		local hor_layout = container:GetComponent(typeof(UnityEngine.UI.HorizontalLayoutGroup))
		hor_layout.childControlHeight = false
		hor_layout.childControlWidth = true
		hor_layout.childForceExpandHeight = false
		hor_layout.childForceExpandWidth = false
	end
end

function BossView:TabbarLoadCallBack()
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Boss, self.tabbar)

	local ver_cell_list = self.tabbar:GetVerCellList()
	if nil == ver_cell_list then
		return
	end

	for k, v in pairs(ver_cell_list) do
		if v:GetView() and v:GetView().gameObject.activeInHierarchy then
			if v.node_list["flag_gold"] then
				v.node_list["flag_gold"]:CustomSetActive(k * 10 == BossViewIndex.VipBoss)
			end
		end
	end
end

function BossView:CreateToggleList()
	if nil == self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		local ver_path = "uis/view/boss_ui_prefab"
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.TabbarLoadCallBack, self))
		self.tabbar:Init(Language.Boss.TabGrop, nil, ver_path, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end
end

function BossView:OnTabChangeHandler(index)
	if index ~= self.show_index then
		self:ChangeToIndex(index)
	end
end

function BossView:ShowIndexCallBack(index)
	BossWGData.Instance:SetCurrentLocalServerShowIndex(index)
	local view_index = BossWGData.Instance:GetBossTuJianIndex()
	if view_index ~= nil and view_index ~= index then
		BossWGData.Instance:SetBossTuJianIndex(nil, nil, nil)
	end
	self.boss_index = 1
	local tab_index = index
	if self.jump_in then
		tab_index = self.tab_index
	end
	self.cur_select_layer = nil
	self:SetCurLayer(nil)
	-- if index == BossViewIndex.MiJingBoss then
	-- 	BossWGCtrl.Instance:SendCSSecretBossPhysicalReq(MiJingBossReq.INFO)
	-- 	BossWGCtrl.Instance:SendMiJingBossReq(BossView.ReqType.ALLINFO)
	-- else
	if index == BossViewIndex.VipBoss then
		self.node_list["btn_world_firstkill"]:SetActive(false)
		self.node_list["btn_vip_firstkill"]:SetActive(true)
		BossWGCtrl.Instance:SendVipAllReq()
	elseif index == BossViewIndex.DabaoBoss then
		self.node_list["btn_world_firstkill"]:SetActive(false)
		self.node_list["btn_vip_firstkill"]:SetActive(false)
		BossWGCtrl.Instance:SendDongKuBossReq(BossView.ReqType.ALLINFO)
	elseif index == BossViewIndex.WorldBoss then
		self.node_list["btn_world_firstkill"]:SetActive(true)
		self.node_list["btn_vip_firstkill"]:SetActive(false)
		BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.ALLINFO)
	elseif index == BossViewIndex.PersonalBoss then
		self.node_list["btn_world_firstkill"]:SetActive(false)
		self.node_list["btn_vip_firstkill"]:SetActive(false)
		self:OnFlushPerBossView()
	end

	self:OnFlushCommonView()
	self.cache_resid = nil

	-- self:ShowBossViewAnimation(index)
end

function BossView:ShowBossViewAnimation(index)
	local tween_info = UITween_CONSTS.BossSys
	UITween.CleanAllMoveToShowPanel(GuideModuleName.Boss)
	--左侧列表
	UITween.MoveToShowPanel(GuideModuleName.Boss, self.node_list.Left_list_parent, u3dpool.vec2(-400, 0),
		u3dpool.vec2(0, 0)
		, tween_info.LeftMoveTime)

	UITween.CleanAlphaShow(GuideModuleName.Boss)
	UITween.FakeHideShow(self.node_list.btn_goto_kill)
	UITween.FakeHideShow(self.node_list.layout_check_hook)
	UITween.FakeHideShow(self.node_list.world_boss_right_1)
	UITween.FakeHideShow(self.node_list.world_right_container)

	--右边面板
	UITween.MoveToShowPanel(GuideModuleName.Boss, self.node_list.right_common_container, u3dpool.vec2(183, -36),
		u3dpool.vec2(-217, -36), tween_info.RightMoveTime)
	if BossViewIndex.WorldBoss == index then
		UITween.FakeHideShow(self.node_list.world_down_msg)
		ReDelayCall(self, function()
			UITween.AlphaShow(GuideModuleName.Boss, self.node_list.world_down_msg, tween_info.FromAlpha,
				tween_info.ToAlpha,
				tween_info.AlphaTime, tween_info.AlphaShowType)
		end, tween_info.AlphaDelay, "world_boss_view_right")
	elseif BossViewIndex.DabaoBoss == index then
		UITween.FakeHideShow(self.node_list.layout_dabao_tip_container)
		ReDelayCall(self, function()
			UITween.AlphaShow(GuideModuleName.Boss, self.node_list.layout_dabao_tip_container, tween_info.FromAlpha,
				tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
		end, tween_info.AlphaDelay, "dabao_boss_view_right")
	elseif BossViewIndex.VipBoss == index then
		UITween.FakeHideShow(self.node_list.layout_vip_boss_right)
		ReDelayCall(self, function()
			UITween.AlphaShow(GuideModuleName.Boss, self.node_list.layout_vip_boss_right, tween_info.FromAlpha,
				tween_info.ToAlpha
				, tween_info.AlphaTime, tween_info.AlphaShowType)
		end, tween_info.AlphaDelay, "vip_boss_view_right")
	elseif BossViewIndex.PersonalBoss == index then
		UITween.FakeHideShow(self.node_list.layout_per_boss_right)
		ReDelayCall(self, function()
			UITween.AlphaShow(GuideModuleName.Boss, self.node_list.layout_per_boss_right, tween_info.FromAlpha,
				tween_info.ToAlpha
				, tween_info.AlphaTime, tween_info.AlphaShowType)
		end, tween_info.AlphaDelay, "person_boss_view_right")
	end

	ReDelayCall(self, function()
		UITween.AlphaShow(GuideModuleName.Boss, self.node_list.layout_check_hook, tween_info.FromAlpha,
			tween_info.ToAlpha,
			tween_info.AlphaTime, tween_info.AlphaShowType)
		UITween.AlphaShow(GuideModuleName.Boss, self.node_list.btn_goto_kill, tween_info.FromAlpha, tween_info.ToAlpha,
			tween_info.AlphaTime, tween_info.AlphaShowType)
		UITween.AlphaShow(GuideModuleName.Boss, self.node_list.world_boss_right_1, tween_info.FromAlpha,
			tween_info.ToAlpha,
			tween_info.AlphaTime, tween_info.AlphaShowType)
		UITween.AlphaShow(GuideModuleName.Boss, self.node_list.world_right_container, tween_info.FromAlpha,
			tween_info.ToAlpha
			, tween_info.AlphaTime, tween_info.AlphaShowType)
	end, tween_info.AlphaDelay, "boss_view_right")
end

function BossView:CreatePagesView(index)
	self:InitCommonBossView()
	if BossViewIndex.WorldBoss == index then
		self:InitWorldBossView()
		self:CreateRareFallItem()
	elseif BossViewIndex.DabaoBoss == index then
		self:InitDabaoBossView()
		--self:CreateRareFallItem()
	elseif BossViewIndex.VipBoss == index then
		self:CreateRareFallItem()
		self:InitWorldBossView()
		-- elseif BossViewIndex.MiJingBoss == index then
		-- 	self:InitCommonBossView()
		-- 	self:InitMiJingBossView()
		--elseif BossViewIndex.DropRecord == index then
		--	self:IniteDropRecordView()
	elseif BossViewIndex.PersonalBoss == index then
		self:InitPerBossView()
		--elseif BossViewIndex.ShengYuBoss == index then
		--	self:InitNewSYBossView()
	end
end

function BossView:InitCommonBossView()
	if self.is_load_common then
		return
	end
	self.is_load_common = true
	--self.node_list["btn_boss_fall"].button:AddClickListener(BindTool.Bind1(self.BrowseRareFallRecord, self))
	self.node_list["layout_check_hook"].button:AddClickListener(BindTool.Bind1(self.FouseOnBoss, self))
	self.node_list["btn_goto_kill"].button:AddClickListener(BindTool.Bind1(self.GoToKillWorldBoss, self))

	self.cur_boss_id = 1

	self:CreateLayerBtnList()
	self:InitCreateCamberedList()
end

function BossView:InitCreateCamberedList()
	local cambered_list_data = {
		item_render = BossWorldItemRender,
		asset_bundle = "uis/view/boss_ui_prefab",
		asset_name = "ph_boss_cambered_render",

		scroll_list = self.node_list.ph_world_boss_cambered_list_1,
		center_x = 800,
		center_y = -230,
		radius_x = 800,
		radius_y = 800,
		angle_delta = Mathf.PI / BossView.ANGLE_DELTA,
		origin_rotation = Mathf.PI * 0.41,
		is_drag_horizontal = false,
		is_clockwise_list = false,
		speed = 1,
		arg_speed = 0.2,
		viewport_count = BossView.DRAG_COUNT,

		click_item_cb = BindTool.Bind(self.OnClickBossBtn, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragBossToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragBossToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragBossLEndCallBack, self),
	}

	self.boss_cambered_list = CamberedList.New(cambered_list_data)
end

function BossView:SetBossListSelectCellIndex(cell_index)
	if self.boss_index == cell_index then
		return
	end

	self.boss_index = cell_index
	self.boss_drag_select_index = cell_index

	local btn_item_list = self.boss_cambered_list:GetRenderList()
	for k, item_cell in ipairs(btn_item_list) do
		if cell_index == item_cell:GetIndex() then
			self:OnBossSelectedBtnChange(function()
				self:BossLsitSelectCallBack(item_cell, nil, nil, true)
			end, true)
		end
	end
end

function BossView:BossRemindCallback(remind_name, num)
	if remind_name == RemindName.Boss_ZhanLing and self.node_list["zhanling_remind"] then
		self.node_list["zhanling_remind"]:SetActive(num > 0)
	end
end

function BossView:OnClickBossBtn(item_cell, force_jump)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if (not force_jump and self.boss_index == select_index) then
		return
	end

	self.boss_index = select_index
	self.boss_drag_select_index = select_index

	self:OnBossSelectedBtnChange(function()
		self:BossLsitSelectCallBack(item_cell, nil, nil, true)
	end, true)
end

function BossView:OnDragBossToNextCallBack()
	local max_index = self.boss_list_data and #self.boss_list_data or 6
	self.boss_drag_select_index = self.boss_drag_select_index + 1
	self.boss_drag_select_index = self.boss_drag_select_index > max_index and max_index or self.boss_drag_select_index
end

function BossView:OnDragBossToLastCallBack()
	self.boss_drag_select_index = self.boss_drag_select_index - 1
	self.boss_drag_select_index = self.boss_drag_select_index < 1 and 1 or self.boss_drag_select_index
end

function BossView:OnDragBossLEndCallBack()
	self:OnBossSelectedBtnChange(nil, false, self.boss_drag_select_index)
end

function BossView:OnBossSelectedBtnChange(callback, is_click, drag_index)
	if self.boss_cambered_list == nil then
		return
	end

	local to_index = drag_index ~= nil and drag_index or self.boss_index or 1
	self.boss_cambered_list:ScrollToIndex(to_index, callback, is_click)

	if is_click then
		local item_list = self.boss_cambered_list:GetRenderList()
		for k, item_cell in ipairs(item_list) do
			item_cell:SetSelectedHL(to_index)
		end
	end
end

function BossView:PersonKillRewardClick()
	local param_1 = self:GetShowIndex() == BossViewIndex.WorldBoss and 0 or 1
	local _, can_get = BossWGData.Instance:GetIsPersonFirstKilledByBossId(self.cur_boss_id)
	if can_get then
		BossWGCtrl.Instance:OpenFirstkillRedPacketView(self.cur_boss_id, param_1)
	else
		local reward_num = self.select_item_data.person_firstkill_reward
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.RewardPersonFistKill, reward_num))
	end
end

function BossView:WorldKillRewardClick()
	local _, can_get = BossWGData.Instance:GetIsWorldFirstKilledByBossId(self.cur_boss_id)
	local boss_info = BossWGData.Instance:GetBossInfoByBossId(self.cur_boss_id)
	if can_get and boss_info.world_firstkill_reward then
		local boss_type = self:GetShowIndex() == BossViewIndex.WorldBoss and 0 or 1
		BossWGCtrl.Instance:OpenRewardChooseView(self.cur_boss_id, boss_type)
		-- if BossWGData.Instance:GetIsWorldFirstKilledByBossId(self.cur_boss_id) then
		--     BossWGCtrl.Instance:SendFirstKillOpera(BossWGData.CS_BOSS_FIRST_KILL_TYPE.CS_BOSS_FIRST_KILL_TYPE_FETCHTOTAL, self.cur_boss_id, boss_type)
		--     return
		-- end
	else
		BossWGCtrl.Instance:OpenFirstRewardView(self.cur_boss_id)
	end
end

--Boss首杀弹窗
function BossView:BossFirstKillClick()
	BossWGData.Instance:SetCurFirstKillShowView(self.show_index)
	BossWGCtrl.Instance:OpenFirstKillView()
end

-- vip,打宝Boss层按钮
function BossView:CreateLayerBtnList()
	if nil == self.layer_btn_list then
		self.layer_btn_list = AsyncListView.New(BossLayerBtnRender, self.node_list["ph_btn_list"])
		self.layer_btn_list.ListEventCallback = function(layer_btn_list, item_cell)
			if self:GetShowIndex() == BossViewIndex.DabaoBoss then
				local falg, need_level = BossWGData.Instance:GetDaboBossIsEneter(item_cell.index)
				if not falg and item_cell.index ~= 1 then
					need_level = RoleWGData.GetLevelString(need_level)
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.EnterWorldBossLevel, need_level))
					layer_btn_list:SelectIndex(self:GetCurLayer())
					return
				end
			end
			AsyncListView.ListEventCallback(layer_btn_list, item_cell)
			self.boss_index = 1
			self:refreshBossList()
		end
		self.layer_btn_list:SetSelectCallBack(BindTool.Bind1(self.BossLayerBtnSelectCallBack, self))
	end
end

function BossView:CreateRareFallItem()
	if not self.cell_list then
		self.cell_list = {}
		for i = 1, 2 do
			self.cell_list[i] = AsyncBaseGrid.New()
			local t = {}
			t.col = 3
			t.change_cells_num = 1
			t.itemRender = BossRewardCell
			if i == 1 then
				t.list_view = self.node_list["cell_list"]
			elseif i == 2 then
				t.list_view = self.node_list["cell_list_2"]
			end
			self.cell_list[i]:CreateCells(t)
			self.cell_list[i]:SetStartZeroIndex(false)
		end
	end
end

function BossView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if self:IsLoadedIndex(self.show_index) then
				self:RefreshView(self.show_index)
				self:refreshBossList()
			end
		elseif "jum_info" == k then
			--if self.layer_btn_list then
			--self.layer_btn_list:SelectIndex(v.boss_list_layer)

			--end
			--local boss_type = v.boss_type
			--self.layer_index = v.boss_list_layer
			--[[if self.boss_list then
				self.boss_list:SelectIndex(v.boss_list_index)
			end--]]
			--self.boss_index = v.boss_list_index
			self:refreshBossList()
			-- end
		elseif "mj_scene" == k then
			self:refreshBossList()
		elseif "focus_cell" == k then
			self:FlushFocusCell()
		elseif "flush_xianli" == k then
			self:FlushXianliInfo()
			self:refreshBossList()
		end
	end
end

function BossView:RefreshView(index)
	if BossViewIndex.WorldBoss == index then
		--self:OnFlushCommonView()
		self:OnFlushWorldBossView()
	elseif BossViewIndex.VipBoss == index then
		--self:OnFlushCommonView()
		self:OnFlushWorldBossView()
	elseif BossViewIndex.DabaoBoss == index then
		--self:OnFlushCommonView()
		self:OnFlushDabaoBossView()
		-- elseif BossViewIndex.MiJingBoss == index then
		-- 	self:OnFlushCommonView()
		-- 	self:OnFlushMiJingBossView()
		--elseif BossViewIndex.DropRecord == index then
		--self:OnFlushDropRecordView()
	elseif BossViewIndex.PersonalBoss == index then
		--self:OnFlushCommonView()
		self:OnFlushWorldBossView()
		self:OnFlushPerBossView()
	end

	self:FlushBossBtnGoToKillRed(index)
end

function BossView:OnFlushCommonView()
	local view_index, number_index, card_index = BossWGData.Instance:GetBossTuJianIndex()
	local is_dianji = true
	if view_index == nil and number_index == nil and card_index == nil then
		self:refreshBossList()
	else
		is_dianji = false
		self:TuJianJump(view_index, number_index, card_index)
		if self.show_index == BossViewIndex.PersonalBoss then -- 个人boss不需要协议刷新
			self:refreshBossList()
		end
	end
	local index = self:GetShowIndex()
	local btn_list = BossWGData.Instance:GetBossLayerCfg(self.show_index) --Language.Boss.BtnSub[self:GetShowIndex() - 11]
	-- if index == BossViewIndex.MiJingBoss then
	-- 	btn_list = BossWGData.Instance:GetMiJingBtnList()
	-- else
	if index == BossViewIndex.VipBoss then
		btn_list = BossWGData.Instance:GetVipBossLayerLimit(btn_list)
	end

	self.node_list.down_container_1:SetActive(self.show_index ~= BossViewIndex.PersonalBoss)
	self.node_list.down_container_2:SetActive(false)

	--reload后会执行select然后修改self.cur_select_layer的值，所以这里保存下
	local old_select_layer = self.cur_select_layer
	self.cur_select_layer = old_select_layer
	local default_index = self.layer_index or 1
	if self:GetShowIndex() == BossViewIndex.VipBoss then
		if self.layer_index ~= nil then
			default_index = self:GetCurVipLayerIndex(self.layer_index, btn_list) or default_index
		else
			if self.cur_select_layer ~= nil then
				default_index = self.cur_select_layer
			else
				if BossWGData.Instance:GetVipBossLayerIndex() ~= -1 then
					default_index = BossWGData.Instance:GetVipBossLayerIndex()
				else
					default_index = BossWGData.Instance:GetVipBossDefaultLayer(btn_list)
				end
			end
		end
		default_index = default_index == 0 and 1 or default_index --容错保证不会选到0
	elseif self:GetShowIndex() == BossViewIndex.DabaoBoss then
		if self.cur_select_layer ~= nil then
			default_index = self.cur_select_layer
		else
			default_index = BossWGData.Instance:GetDaBaoLevelLayerCfg()
		end
	elseif self:GetShowIndex() == BossViewIndex.WorldBoss then
		if self.layer_index ~= nil then
			default_index = self.layer_index
		else
			if self.cur_select_layer ~= nil then
				default_index = self.cur_select_layer
			else
				if BossWGData.Instance:GetWorldBossLayerIndex() ~= -1 then
					default_index = BossWGData.Instance:GetWorldBossLayerIndex()
				else
					default_index = BossWGData.Instance:GetWorldBossDefLayer()
				end
			end
		end
		-- elseif self:GetShowIndex() == BossViewIndex.MiJingBoss then
		-- 	local role_level = RoleWGData.Instance:GetRoleLevel()
		-- 	default_index = BossWGData.Instance:GetMJBossDefaultLevel(role_level)
	end
	self.layer_index = nil
	-- if LAYER_BOSS_CLICK then
	self.layer_btn_list:SetDataList(btn_list or {})
	if is_dianji then
		self.layer_btn_list:JumpToIndex(default_index)
		-- self.layer_btn_list:SelectIndex(default_index)
	elseif number_index ~= nil and number_index ~= 0 then
		self.layer_btn_list:JumpToIndex(number_index)
	end
	local level = GameVoManager.Instance:GetMainRoleVo().level
	-- local open_level = BossPrivilegeWGData.Instance:PrivilegeOpenLevel()
	-- self.node_list["btn_open_privilege"]:SetActive(level >= open_level)

	if self.node_list["boss_kill_every_btn"] then
		local boss_kill_every_other_cfg = BossKillEveryWGData.Instance:GetOtherCfg()
		local open_boss_kill_every_level = boss_kill_every_other_cfg.open_level
		local is_show_boss_kill_show = BossKillEveryWGData.Instance:CheckShowBossList(self:GetShowIndex())
		self.node_list["boss_kill_every_btn"]:SetActive(level >= open_boss_kill_every_level and is_show_boss_kill_show)
	end

	if self.node_list["btn_open_zhanling"] then
		local zhanlin_is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.BossZhanLingView)
		self.node_list["btn_open_zhanling"]:SetActive(zhanlin_is_open)
	end

	-- if self.node_list["btn_mwxz"] then
	-- 	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.LordEveryDayShopView)
	-- 	self.node_list["btn_mwxz"]:SetActive(is_open)
	-- end

	if self.node_list["button_award_tip"] then
		self.node_list["button_award_tip"]:SetActive(false)
	end

	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.pri_col_shtq)
    self.node_list.button_pc_shtq:SetActive(is_open)
end

function BossView:refreshBossList(need_select)
	--local view_index, number_index, card_index = BossWGData.Instance:GetBossTuJianIndex()
	--if view_index ~= nil and  number_index ~= nil and card_index ~= nil then
	--	return
	--end
	local min_show_len = 4 --界面最多显示5个boss格子，选中第六个可能看不到
	local index = self:GetShowIndex()
	local default_index = self.boss_index or 1
	local list_data = {}

	local cur_layer = self:GetCurLayer()

	if index == BossViewIndex.VipBoss then
		if cur_layer == nil then
			return
		end
		list_data = BossWGData.Instance:GetVipBossListByIndex(cur_layer)
		if not self.boss_index or default_index > #list_data then
			default_index = self:GetVipBossDefaultIndex(default_index, list_data)
		end
	else
		local need_max_level = false
		if index == BossViewIndex.DabaoBoss then
			if cur_layer == nil then
				return
			end
			list_data = BossWGData.Instance:GetDabaoBossListByIndex(cur_layer)
		elseif BossViewIndex.WorldBoss == index then
			if cur_layer == nil then
				return
			end
			list_data = BossWGData.Instance:GetWorldBossListByLayer(cur_layer)
			need_max_level = true
		elseif BossViewIndex.PersonalBoss == index then
			list_data = BossWGData.Instance:GetPersonalBossList()
			need_max_level = true
			--default_index = self:GetDefalutPerBossIndex(default_index, list_data)
			-- elseif BossViewIndex.MiJingBoss == index then
			-- 	list_data = BossWGData.Instance:GetMiJingBossListByIndex(self.cur_layer)
		end

		if not IsEmptyTable(list_data) and (not self.boss_index or default_index > #list_data) then
			default_index = BossWGData.Instance:GetWorldBossDefaultIndex(default_index, list_data, need_max_level)
		end
	end

	self.boss_index = default_index
	self.boss_list_data = list_data
	self.boss_cambered_list:CreateCellList(#list_data)
	local btn_item_list = self.boss_cambered_list:GetRenderList()
	for k, item_cell in ipairs(btn_item_list) do
		local item_data = self.boss_list_data[k]
		item_cell:SetData(item_data)

		if self.boss_index == item_cell:GetIndex() then
			self:OnBossSelectedBtnChange(function()
				self:BossLsitSelectCallBack(item_cell, nil, nil, true)
			end, true)
		end
	end

	self:CheckRoleConcern(list_data)
end

--检查角色关注的boss中有没有等级过高无产出的
function BossView:CheckRoleConcern(list_data)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if not IsEmptyTable(list_data) then
		for k, v in pairs(list_data) do
			if role_level - v.boss_level >= v.max_delta_level then
				local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(v.boss_id)
				if boss_info ~= nil and boss_info.is_concern ~= nil and tonumber(boss_info.is_concern) > 0 then
					local tab_index = self:GetShowIndex()
					local cur_layer = self:GetCurLayer()
					if tab_index == BossViewIndex.WorldBoss then
						BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.CONCERN, v.boss_id, 0)
					elseif tab_index == BossViewIndex.VipBoss then
						BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.OTHER_CONCERN, cur_layer - 1, v.boss_id, 0)
					elseif tab_index == BossViewIndex.DabaoBoss then
						BossWGCtrl.Instance:SendDongKuBossReq(BossView.ReqType.OTHER_CONCERN, cur_layer, v.boss_id, 0)
					end
					BossWGData.Instance:SetBossIsConcernByBossId(v.boss_id, 0)
				end
			end
		end
	end
end

--图鉴跳转过来后的跳转
function BossView:TuJianJump(view_index, num_index, card_index)
	self.layer_index = num_index
	self.boss_index = card_index
end

function BossView:OnFlushBossInfo()
	self:refreshBossAnim()

	self.node_list.vip_xianli_part:SetActive(self.show_index == BossViewIndex.VipBoss)

	if self.show_index ~= BossViewIndex.PersonalBoss then
		self:refreshBossInfo()
		self:refreshRareFall()
	end
end

function BossView:refreshBossInfo(boss_id)
	if self.select_item_data == nil then return end
	if self.show_index == BossViewIndex.PersonalBoss then
		return
	end
	local is_show = true
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level - self.select_item_data.boss_level >= self.select_item_data.max_delta_level
		or (self.select_item_data.need_role_level and role_level < self.select_item_data.need_role_level) then
		is_show = false
	end

	-- self.node_list["layout_check_hook"]:SetActive(is_show and self.show_index ~= BossViewIndex.PersonalBoss)
	self.node_list["layout_check_hook"]:SetActive(false)

	local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.select_item_data.boss_id)
	local is_alive = true
	if boss_info then
		is_alive = boss_info.next_refresh_time <= TimeWGCtrl.Instance:GetServerTime()
	end

	local record_list = BossWGData.Instance:GetBossRefreshInfoByBossId(self.select_item_data.boss_id)
	local first_kill_list = BossWGData.Instance:GetWorldFirstKilledHistoryByBossId(self.select_item_data.boss_id)
	local str
	if record_list ~= nil and record_list.kill_vo[#record_list.kill_vo] then
		str = Language.Boss.LastKillText ..
			ToColorStr(record_list.kill_vo[#record_list.kill_vo].last_killer_name, COLOR3B.WHITE)
	elseif not IsEmptyTable(first_kill_list) and first_kill_list[#first_kill_list] then --首杀历史
		str = Language.Boss.LastKillText .. ToColorStr(first_kill_list[#first_kill_list].killer_role_name, COLOR3B.WHITE)
	else
		str = Language.Boss.LastKillText .. ToColorStr(Language.Common.ZanWu, COLOR3B.WHITE)
	end

	-- if self.show_index == BossViewIndex.DabaoBoss and self.node_list and self.node_list.dabao_last_kill then
	-- 	self.node_list.dabao_last_kill.text.text = str
	-- end
	self:FlushXianliInfo()
end

function BossView:FlushXianliInfo()
	if self.show_index == BossViewIndex.VipBoss and self:IsLoadedIndex(self.show_index)
		and self.select_item_data and self.select_item_data.kill_reduce_xianli then
		local total_xianli = BossAssistWGData.Instance:GetBossXianli()
		local color = total_xianli > 0 and COLOR3B.C8 or COLOR3B.C10
		local max_xianli = BossWGData.Instance:GetBossXianliMax()
		local num_str = ToColorStr((total_xianli .. "/" .. max_xianli), color)
		self.node_list.des_energy_txt.text.text = string.format(Language.Boss.XianliDes, num_str)
		local cost = self.select_item_data.kill_reduce_xianli
		local color1 = total_xianli >= cost and COLOR3B.C8 or COLOR3B.C10
		local cost_str = ToColorStr(cost, color1)
		self.node_list.text_cur_boss_tip.text.text = string.format(Language.Boss.VipCostXianli, cost_str)
		local can_add = BossWGData.Instance:JudgeCanGetBossXianli()
		self.node_list.can_add_remind:SetActive(can_add)
		-- self.node_list.btn_xianli_zhenjia_eff:SetActive(can_add)

		local is_show_lingli = BossWGData.Instance:GetIsShowLingLi()
		self.node_list["des_energy_txt_root"]:SetActive(is_show_lingli > 0)

		local is_enough, is_vipboss = BossAssistWGData.Instance:JudgeIsEnoughBoss(self.select_item_data.boss_id)
		self.node_list.vip_boss_xianli_enough:SetActive(is_enough and self.select_item_data.boss_id > 0)
		self.node_list.vip_boss_xianli_no_enough:SetActive(not is_enough and self.select_item_data.boss_id > 0)

		local boss_info = BossWGData.Instance:GetBossInfoByBossId(self.select_item_data.boss_id)
		local kill_reduce_xianli = boss_info and boss_info.kill_reduce_xianli or 0
		self.node_list.boss_cost_xianli_num.text.text = kill_reduce_xianli
	end
end

function BossView:refreshRareFall()
	local boss_info = self.select_item_data
	if boss_info == nil then return end

	for i = 1, 2 do
		local list = {}
		if self.cell_list == nil or self.cell_list[i] == nil then return end
		if i == 1 then
			local data
			for i, v in pairs(boss_info.drop_item_list) do
				if boss_info.is_item_list then
					data = BossWGData.Instance:GetBossCellInfo(v, boss_info)
				else
					data = {
						item_id = tonumber(v),
						show_duobei = boss_info.show_duobei,
						task_type = boss_info.task_type,
						cell_scale = 0.9,
					}
				end
				table.insert(list, data)
			end

			if self.cell_old_list == nil then
				self.cell_list[i]:SetDataList(list)
				self.cell_old_list = list
			else
				local flag = BossWGData.Instance:BossDataTableEquals(list, self.cell_old_list)
				if flag == false then
					self.cell_list[i]:SetDataList(list)
					self.cell_old_list = list
				end
			end
		else
			if not boss_info.reward_item then
				return
			end

			local data
			for i, v in pairs(boss_info.reward_item) do
				data = {
					item_id = v.item_id,
					cell_scale = 0.9,
				}
				table.insert(list, data)
			end
			self.cell_list[i]:SetDataList(list)
		end
	end
end

function BossView:refreshBossAnim()
	if nil == self.boss_display_model then return end

	local data = self.select_item_data
	local boss_show_id = self.show_index == BossViewIndex.PersonalBoss and data.boss_id or data.view_resouce
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_show_id] --获取boss模型
	if nil ~= monster_cfg and self.boss_display_model then
		if self.cache_resid == monster_cfg.resid and self.boss_display_model then
			if self.boss_display_model:GetDrawObj() ~= nil then
				return
			end
		end

		self.boss_display_model:SetMainAsset(ResPath.GetMonsterModel(monster_cfg.resid))

		self.cache_resid = monster_cfg.resid
		self.boss_display_model:PlayMonsterAction(true)

		self.node_list.world_boss_name_bg:SetActive(true)
		self.node_list.world_boss_name_bg_2:SetActive(false)
		self.node_list.world_boss_name.text.text = monster_cfg.name
	end
end

function BossView:BossLayerBtnSelectCallBack(btnself, _, _, is_click)
	local tab_index = self:GetShowIndex()

	if btnself == nil or BossViewIndex.DropRecord == tab_index or not self:IsLoadedIndex(tab_index) then
		return
	end

	local old_index = self:GetCurLayer()
	local cur_layer = nil
	if self.show_index == BossViewIndex.VipBoss then
		cur_layer = btnself.data.level
		self:OnFlushVipBossView()
	else
		cur_layer = btnself.index
	end

	--如果点击切换层数，选中默认值
	if is_click then
		self.boss_index = nil
	end
	self:SetCurLayer(cur_layer)
	--清除数据
	local _, num_index = BossWGData.Instance:GetBossTuJianIndex()
	if num_index ~= nil and num_index ~= cur_layer then
		BossWGData.Instance:SetBossTuJianIndex(nil, nil, nil)
	end

	if BossViewIndex.WorldBoss == tab_index or BossViewIndex.DabaoBoss == tab_index or BossViewIndex.MiJingBoss == tab_index then
		self.node_list["Txt_goto_kill"].text.text = Language.Boss.GoToKillBtn
	end

	self.cur_select_layer = btnself.index
	if BossViewIndex.DabaoBoss == tab_index then
		self:SelectDabaoLayer(old_index)
		BossWGCtrl.Instance:SendDongKuBossReq(BossView.ReqType.SINGLELAYERINFO, cur_layer)
	end
	if BossViewIndex.WorldBoss == tab_index then
		self:FlushWorldBossView()
	end

	self:FlushBossBtnGoToKillRed(tab_index)
	self:refreshBossList(true)
end

function BossView:FlushBossBtnGoToKillRed(tab_index)
	local is_red = false
	if BossViewIndex.WorldBoss == tab_index then
		is_red = BossWGData.Instance:GetWorldBossRed() == 1
	end

	if self.node_list.btn_goto_kill_red then
		self.node_list.btn_goto_kill_red:SetActive(is_red)
	end
end

function BossView:BossLsitSelectCallBack(boss_cell, _, _, is_click)
	local show_index = self.show_index
	if not boss_cell or IsEmptyTable(boss_cell.data) or not self:IsLoadedIndex(show_index) then
		return
	end

	--清除数据
	local _, num_index, card_index = BossWGData.Instance:GetBossTuJianIndex()
	if num_index ~= nil and card_index ~= boss_cell.index then
		BossWGData.Instance:SetBossTuJianIndex(nil, nil, nil)
	end
	local cur_layer = self:GetCurLayer()
	self.cur_boss_id = boss_cell.data.boss_id
	self.select_item_data = boss_cell.data

	self.cur_boss_scene_id = boss_cell.data.scene_id
	--BossWGData.Instance:SetCurSelectBossID(self:GetShowIndex() , cur_layer, self.cur_boss_id)

	if self:GetShowIndex() == BossViewIndex.DabaoBoss and self:IsLoadedIndex(BossViewIndex.DabaoBoss) then
		self:OnFlushDabaoBossView()
	elseif self:GetShowIndex() == BossViewIndex.PersonalBoss and self:IsLoadedIndex(BossViewIndex.PersonalBoss) then
		self:OnFlushPerBossInfo()
	elseif self:GetShowIndex() == BossViewIndex.WorldBoss and self:IsLoadedIndex(BossViewIndex.WorldBoss) then
		self:FlushBossTimes()
	end

	local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.cur_boss_id) or {}
	local boss_state = BossWGData.Instance:GetBossStatusByBossId(self.cur_boss_id)
	if boss_state == 1 then
		if self.node_list["right_title"] then
			self.node_list["right_title"].text.text = Language.Boss.RightTitleName[2]
			local str = boss_info.belong_name
			if str == "" then
				str = Language.Boss.GuiShuNews
			else
				str = Language.Boss.GuiShuNews2
			end
			self.node_list["right_kill_name"].text.text = str
		end
	else
		if self.node_list["right_title"] then
			self.node_list["right_title"].text.text = Language.Boss.RightTitleName[2]
			self.node_list["right_kill_name"].text.text = Language.Boss.GuiShuNews3
		end
	end
	self:OnFlushBossInfo()
	self.old_boss_id = self.cur_boss_id

	self:OnFlushBossFirstKillInfo()
end

function BossView:OnFlushBossFirstKillInfo()
	if self:GetShowIndex() == BossViewIndex.WorldBoss or self:GetShowIndex() == BossViewIndex.VipBoss then
		local first_killer_info = BossWGData.Instance:GetBossFirstKiller(self.cur_boss_id)
		if first_killer_info then
			if self.node_list.world_boss_first_kill then
				self.node_list.world_boss_first_kill:SetActive(first_killer_info.role_uid > 0)

				if first_killer_info.role_uid > 0 then
					local server_id = UserVo.GetServerId(first_killer_info.role_uid)
					local first_killer_text = string.format(Language.Boss.NameFuIDText2, first_killer_info.role_name)
					if UserVo.IsCrossServer(first_killer_info.role_uid) then
						first_killer_text = string.format(Language.Boss.NameFuIDText, first_killer_info.role_name, server_id)
					end
					self.node_list.world_boss_first_kill_text.text.text = first_killer_text
				end
			end
		end
	else
		self.node_list.world_boss_first_kill:SetActive(false)
	end
end

-- boss信息
function BossView:BrowseBossInfo()
	self:OnFlushBossInfo()
end

--显示击杀boss记录
function BossView:BrowseKillRecord()
	if self:GetShowIndex() == BossViewIndex.WorldBoss or self:GetShowIndex() == BossViewIndex.VipBoss then
		local data        = {}
		data.kill_history = BossWGData.Instance:GetWorldFirstKilledHistoryByBossId(self.cur_boss_id)
		BossWGCtrl.Instance:OpenKillRecordAndSetData(data, true)
	else
		local data = self.select_item_data
		if data ~= nil then
			BossWGCtrl.Instance:SendBossKillRecordReq(data.boss_id)
		end
	end
end

function BossView:SendKillWorldBoss(boss_data)
	local scene_type = Scene.Instance:GetSceneType()
	local same_scene = Scene.Instance:GetSceneId() == boss_data.scene_id
	if not BossWGData.IsSingleBoss(scene_type) and same_scene then --同场景内直接引导
		self:Close()
		BossWGCtrl.Instance:MoveToBoss(self.cur_boss_id, SELECT_BOSS_REASON.VIEW)
		-- elseif BossWGData.IsBossScene(scene_type) then
		-- 	local str = BossWGData.Instance:GetBossSceneTip(boss_data.scene_id, same_scene)
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(str)
	else
		if self:GetShowIndex() == BossViewIndex.VipBoss then -- VipBOSS
			if boss_data and boss_data.need_role_level and boss_data.need_role_level > RoleWGData.Instance:GetRoleLevel() then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelUnLock2,
					RoleWGData.GetLevelString(boss_data.need_role_level)))
				return
			end
			local cur_layer = self:GetCurLayer()
			BossWGData.Instance:SetCurSelectBossID(self:GetShowIndex(), cur_layer, self.cur_boss_id)
			BossWGCtrl.Instance:OnEnterVipBoss()
		elseif self:GetShowIndex() == BossViewIndex.WorldBoss then -- 世界boss
			if boss_data and boss_data.need_role_level and boss_data.need_role_level > RoleWGData.Instance:GetRoleLevel() then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelUnLock2,
					RoleWGData.GetLevelString(boss_data.need_role_level)))
				return
			end
			local cur_layer = self:GetCurLayer()
			BossWGData.Instance:SetCurSelectBossID(self:GetShowIndex(), cur_layer, self.cur_boss_id)
			BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.ENTER, boss_data.layer)
			BossWGData.Instance:SetOldBossID(BossViewIndex.WorldBoss, self.cur_boss_id, cur_layer)
			BossWGData.Instance:SetBossEnterFlag(false)
		elseif self:GetShowIndex() == BossViewIndex.DabaoBoss then -- 打宝boss
			local enter_time, max_times = BossWGData.Instance:GetDaBaoBossRemainEnterTimes()
			if max_times - enter_time <= 0 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.DaBaoBossNoTimes)
				return
			end

			local enter_comsun = BossWGData.Instance:GetDaBaoBossEnterComsun()
			local tiky_id = BossWGData.Instance:GetDabaoBossTikyId()
			BossWGCtrl.Instance:SetEnterBossComsunData(tiky_id, enter_comsun, Language.Boss.EnterDaBao,
				Language.Boss.EnterBossConsum, function()
					-- 回调里重新取值，保证数据正确
					local cur_layer = self:GetCurLayer()
					BossWGData.Instance:SetCurSelectBossID(self:GetShowIndex(), cur_layer, self.cur_boss_id)
					BossWGCtrl.Instance:SendDongKuBossReq(BossView.ReqType.ENTER, cur_layer, self.cur_boss_id)
					BossWGData.Instance:SetBossEnterFlag(true)
				end)
		elseif self:GetShowIndex() == BossViewIndex.PersonalBoss then
			self:GoToKillPerBoss()
		end
		BossWGData.Instance:GetSetLastSelectInfo(self.cur_select_layer, self.boss_index)
	end
end

-- 前往击杀
function BossView:GoToKillWorldBoss(is_guide)
	if TaskWGCtrl.Instance:IsFly() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FlyShoeCanNotDo)
		return
	end
	if is_guide then
		FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.GUIDE_BOSS)
		return
	end
	-- if not TaskWGData.Instance:GetTaskIsCompleted(TaskWGData.GUIDE_BOSS_TASK)
	-- 	and not TaskWGData.Instance:GetTaskIsCanCommint(TaskWGData.GUIDE_BOSS_TASK)  then
	-- 	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.GUIDE_BOSS)
	-- 	self:Close()
	-- else

	local cur_layer = self:GetCurLayer()
	if self.show_index == BossViewIndex.DabaoBoss then
		local _, _, min_order_num, min_color_num = BossWGData.Instance:GetDaboBossIsEneter(cur_layer)
		local enter_flag = CultivationWGData.Instance:GetDaboBossIsCanEneter(min_order_num, min_color_num)
		if not enter_flag then
			local color_text = Language.Common.ColorName4[min_color_num] or ""
			local str = string.format(Language.Boss.DabaoBossEnterLimitText1, min_order_num, color_text)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
			return
		end
	end

	local boss_data = self.select_item_data
	if nil == boss_data then return end
	if self.show_index ~= BossViewIndex.PersonalBoss then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		if role_level - boss_data.boss_level >= boss_data.max_delta_level then
			if nil == self.alert_window then
				self.alert_window = Alert.New(nil, nil, nil, nil, true)
			end
			self.alert_window:SetLableString(Language.Boss.KillLevelHighTips)
			self.alert_window:SetShowCheckBox(true, "Boss" .. self:GetShowIndex())
			self.alert_window:SetBtnDislocation(true)
			self.alert_window:SetCheckBoxDefaultSelect(false)
			self.alert_window:SetCancelString(Language.Boss.Cancel)
			self.alert_window:SetOkString(Language.Boss.QianWang)
			self.alert_window:SetOkFunc(
				function()
					self:SendKillWorldBoss(boss_data)
				end)
			self.alert_window:Open()
		else
			self:SendKillWorldBoss(boss_data)
		end
	else
		self:SendKillWorldBoss(boss_data)
	end
end

-- 关注
function BossView:FouseOnBoss()
	local tabindex = self:GetShowIndex()
	local cur_layer = self:GetCurLayer()
	local data = {}
	data.tabindex = tabindex
	data.cur_layer = cur_layer
	data.is_world_server = false
	BossWGCtrl.Instance:SetFocusViewDataAndOpen(data)
end

function BossView:FlushFocusCell()

end

function BossView:BossLsitGuideSelectCallBack(cell)
	if cell.view then
		cell:OnSelectChange(true)
		self:BossLsitSelectCallBack(cell)
	end
end

function BossView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return
		end
	elseif ui_name == GuideUIName.WorldBossFatigue then
		return self.node_list["text_world_fatigue"]
	elseif ui_name == GuideUIName.PersonBossEnter then
		return self.node_list["btn_goto_kill"], BindTool.Bind1(self.GoToKillPerBoss, self)
	elseif ui_name == GuideUIName.WorldBossEnter then
		local index = ui_param and tonumber(ui_param) or 1
		self:SetBossListSelectCellIndex(index)
		self.boss_index = index
		return self.node_list["btn_goto_kill"], BindTool.Bind2(self.GoToKillWorldBoss, self, false)
	elseif ui_name == GuideUIName.CloseBtn then
		return self.node_list["btn_close_window"], BindTool.Bind1(self.OnCloseHandler, self)
	elseif ui_name == GuideUIName.WorldBossList and self.boss_cambered_list then
		local index = tonumber(ui_param)
		local btn_item_list = self.boss_cambered_list:GetRenderList()
		for k, item_cell in ipairs(btn_item_list) do
			if k == item_cell:GetIndex() then
				return NextGuideStepFlag
			end
			self:SetBossListSelectCellIndex(index)
			return
		end
		self:SetBossListSelectCellIndex(1)
	elseif ui_name == GuideUIName.BossEnterBtn then
		return self.node_list["btn_goto_kill"], BindTool.Bind2(self.GoToKillWorldBoss, self)
	end
end

function BossView:RemindChangeCallback(remind_id, num)
	if self.tabbar == nil then return end
end

-- 玩法介绍
function BossView:BrowsePlayInfo()
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.Boss.PlayTitle)
	if self.show_index == BossViewIndex.WorldBoss then
		role_tip:SetContent(Language.Boss.PlayDes)
	elseif self.show_index == BossViewIndex.VipBoss then
		role_tip:SetContent(Language.Boss.PlayDes2)
	elseif self.show_index == BossViewIndex.DabaoBoss then
		role_tip:SetContent(Language.Boss.DaBaoPlayDes)
	elseif self.show_index == BossViewIndex.PersonalBoss then
		role_tip:SetContent(Language.Boss.PerPlayDes)
	end
end

function BossView:OnClickRecord()
	BossWGCtrl.Instance:SetRecordView(BOSS_RECORD_TYPE.WORLD_BOSS) --1表示本服
	ViewManager.Instance:Open(GuideModuleName.BossDropRecord)
end

--再爆一次 - 特权
function BossView:OnClickTeQuan()
	ViewManager.Instance:Open(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_zaibaoyici)
end

function BossView:OnClickBossKillEvery()
	ViewManager.Instance:Open(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_kill_every)
end

function BossView:OnClickZhanLing()
	BossZhanLingWGCtrl.Instance:OpenZhanLingView()
end

function BossView:SetCurLayer(value)
	self.cur_layer = value
end

function BossView:GetCurLayer()
	return self.cur_layer
end

-- 魔王仙藏
function BossView:OnClickMWXZBtn()
	FunOpen.Instance:OpenViewByName(GuideModuleName.LordEveryDayShopView)
end

function BossView:OnClickAwardTip()
    HundredEquipWGCtrl.Instance:OpenAwardTip()
end

function BossView:OnClickPrivilegeCollectionSHTQBtn()
    ViewManager.Instance:Open(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_shtq)
end

----------------------------------------------------
-- boss列表ItemRender
----------------------------------------------------
BossWorldItemRender = BossWorldItemRender or BaseClass(BaseRender)

function BossWorldItemRender:__init()
	self.click_callback = nil
end

function BossWorldItemRender:__delete()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
	self.tween = nil

	self.click_callback = nil
end

function BossWorldItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list.first_kill_box, BindTool.Bind(self.BrowseKillRecord, self))
end

--显示击杀boss记录
function BossWorldItemRender:BrowseKillRecord()
	local _, can_get = BossWGData.Instance:GetIsWorldFirstKilledByBossId(self.data.boss_id)
	local boss_info = BossWGData.Instance:GetBossInfoByBossId(self.data.boss_id)
	if can_get and boss_info.world_firstkill_reward then
		local view_show_index = BossWGData.Instance:GetCurrentLocalServerShowIndex()
		local boss_type = view_show_index == BossViewIndex.WorldBoss and 0 or 1
		BossWGCtrl.Instance:OpenRewardChooseView(self.data.boss_id, boss_type)
	else
		BossWGCtrl.Instance:OpenFirstRewardView(self.data.boss_id)
	end
end

function BossWorldItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. self.data.big_icon)
	self.node_list["img_boss"].image:LoadSprite(bundle, asset, function()
		self.node_list.img_boss.image:SetNativeSize()
	end)

	-- local bottom_color = self.data.bottom_color
	-- if bottom_color and bottom_color ~= "" and self.node_list.boss_bg then
	-- 	local bg_bundle, bg_asset = ResPath.GetBossUI("a1_boss_bg_" .. bottom_color)
	-- 	self.node_list["boss_bg"].image:LoadSprite(bg_bundle, bg_asset, function()
	-- 		self.node_list.boss_bg.image:SetNativeSize()
	-- 	end)
	-- end

	-- self.node_list["text_boss_name"].text.text = self.data.boss_name
	self.node_list.text_boss_lv.text.text = string.format(Language.Boss.LvText, self.data.boss_level)
	self.node_list["text_boss_state"].text.text = ""

	local monster_info = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
	if monster_info and monster_info.boss_jieshu > 0 then
		self.node_list["text_boss_jie"].text.text = string.format(Language.Boss.JieShu,
			NumberToChinaNumber(monster_info.boss_jieshu))
	else
		self.node_list["text_boss_jie"].text.text = ""
	end

	-- self.node_list.boss_suo:SetActive(false)

	self.is_grey_show = false
	self:OnFlushKFInfo()
	self:OnFlushFirstKill()
	self:OnFlushServerInfo()

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if self.data.need_role_level and role_level < self.data.need_role_level then
		self:ShowLockInfo()
		self.is_grey_show = true
	else
		self:ShowNormalInfo()
	end

	-- self.node_list.gray_mask:SetActive(self.is_grey_show)

	--调整采集物和隐藏boss
	self:OnFlushGather(self.data.type == BossWGData.MonsterType.Gather or
	self.data.type == BossWGData.MonsterType.HideBoss)
	local is_drop = self.data.is_drop_jewelry and self.data.is_drop_jewelry == 1
	if self.node_list.boss_drop_flag then
		self.node_list.boss_drop_flag:SetActive(is_drop)
	end
	self:FlushEnergyState()
end

function BossWorldItemRender:FlushEnergyState()
	self.node_list.boss_energy_state:SetActive(false)
	-- 策划需求 屏蔽完美掉落
	-- local is_enough, is_vipboss = BossAssistWGData.Instance:JudgeIsEnoughBoss(self.data.boss_id)
	-- if is_vipboss then
	--     local str = is_enough and "a1_boss_sbdl" or "a1_boss_shuaijian"
	--     self.node_list.boss_energy_state.image:LoadSprite(ResPath.GetBossUI(str))
	-- end
	-- if self.node_list.boss_energy_state then
	--     self.node_list.boss_energy_state:SetActive(is_vipboss)
	-- end
end

function BossWorldItemRender:OnFlushFirstKill()
	local view_show_index = BossWGData.Instance:GetCurrentLocalServerShowIndex()
	if view_show_index == BossViewIndex.WorldBoss or view_show_index == BossViewIndex.VipBoss then
		local first_killer_info = BossWGData.Instance:GetBossFirstKiller(self.data.boss_id)
		if first_killer_info then
			if self.node_list.first_kill_flag then
				self.node_list.first_kill_flag:SetActive(first_killer_info.role_uid == 0)
			end
		end
	else
		if self.node_list.first_kill_flag then
			self.node_list.first_kill_flag:SetActive(false)
		end
	end
end

function BossWorldItemRender:OnFlushGather(is_gather)
	-- self.node_list.ph_jieshu:SetActive(not is_gather)
	-- self.node_list.img_jieshu_di:SetActive(not is_gather)
end

function BossWorldItemRender:ShowLockInfo()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end

	-- self.node_list.boss_suo:SetActive(true)
	self.node_list["lbl_time"].text.text = ""
	self.node_list["text_boss_state"].text.text = string.format(ToColorStr(Language.Common.LevelUnLock3, COLOR3B.RED),
		RoleWGData.GetLevelString(self.data.need_role_level))
end

function BossWorldItemRender:ShowNormalInfo()
	local str_name = self.data.boss_name
	if self.data.type == BossWGData.MonsterType.Gather then
		if self.data.boss_id == BossWGData.Instance:GetSGGatherShow() then
			local num = BossWGData.Instance:GetShangGuBossSceneOtherInfo(self.data.layer + 1, self.data.type)
			num = num or 0
			str_name = num ~= 0 and string.format("%s（%d）", str_name, num) or str_name
		else
			local layer_num = BossWGData.Instance:GetLayerNumByShowIndex(TabIndex.worserv_boss_hmsy + 1)
			for layer = 1, layer_num do
				local gather = BossWGData.Instance:GetKFGatherInfo(layer)
				if gather.boss_id == self.data.boss_id then
					local boss_info = BossWGData.Instance:GetCrossBossById(self.data.boss_id)
					if boss_info and boss_info.left_num then
						str_name = boss_info.left_num ~= 0 and string.format("%s（%d）", str_name, boss_info.left_num) or
						str_name
						break
					end
				end
			end
		end
	end

	local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
	if boss_server_info then
		self.node_list["bins_bg"]:SetActive(boss_server_info.is_near_death == 1)
		-- self.node_list["gray_mask"]:SetActive(boss_server_info.is_near_death == 1)
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level - self.data.boss_level >= self.data.max_delta_level and
		BossWGData.Instance:GetCurrentLocalServerShowIndex() ~= BossViewIndex.PersonalBoss then
		if self.refresh_event then
			GlobalTimerQuest:CancelQuest(self.refresh_event)
			self.refresh_event = nil
		end
		self.is_grey_show = true
		self.node_list["text_boss_state"].text.text = Language.Boss.LevelHighLimit
		self.node_list["lbl_time"].text.text = ""
		-- self.node_list.boss_suo:SetActive(true)
	else
		self:RefreshRemainTime()
		if self.refresh_event == nil then
			self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime, self), 1)
		end
	end
end

function BossWorldItemRender:OnFlushServerInfo()
	local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
	if boss_server_info == nil then
		return
	end

	self:RefreshConcernTag(boss_server_info.is_concern and boss_server_info.is_concern > 0)
end

function BossWorldItemRender:OnFlushKFInfo()
	local left_num = 1
	local is_sgyj = WorldServerWGCtrl.Instance:GetBossViewShowIndex() == TabIndex.worserv_boss_sgyj
	if is_sgyj then
		left_num = BossWGData.Instance:GetShangGuBossSceneOtherInfo(self.data.layer + 1, self.data.type)
		left_num = left_num or 1
	end

	if self.data.type == BossWGData.MonsterType.HideBoss then
		--self.node_list["lbl_time"].text.text = Language.Boss.SgFlushDes[BossWGData.MonsterType.HideBoss]
	elseif self.data.type == BossWGData.MonsterType.Gather and self.data.boss_id == BossWGData.Instance:GetSGGatherShow() then
		local box_flush_time = BossWGData.Instance:GetSGBoxFlushTime()
		self.node_list["lbl_time"].text.text = box_flush_time > 0 and TimeUtil.FormatHM(box_flush_time) or ""
	end
end

function BossWorldItemRender:RefreshConcernTag(vis)
	local is_show_first = self.node_list.first_kill_flag and self.node_list.first_kill_flag.gameObject.activeSelf
	self.node_list["focus_icon"]:SetActive(vis and not is_show_first)
end

function BossWorldItemRender:RefreshRemainTime()
	if IsEmptyTable(self.data) then
		if self.refresh_event then
			GlobalTimerQuest:CancelQuest(self.refresh_event)
			self.refresh_event = nil
		end
		return
	end

	local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
	if boss_server_info == nil then
		boss_server_info = BossWGData.Instance:GetCrossBossById(self.data.boss_id)
	end
	--local boss_data = BossWGData.Instance:GetBossInfoByBossId(self.data.boss_id)
	--local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local time = 0
	if boss_server_info ~= nil then
		time = (boss_server_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime()
	end

	local state = time > 1
	self.node_list["text_boss_state"].text.text = not state and Language.Boss.RefreshTime3 or ""
	if state then
		self.is_grey_show = true
		self.node_list["lbl_time"].text.text = TimeUtil.FormatSecond(time)
	else
		if self.data.type ~= BossWGData.MonsterType.HideBoss then
			--self.node_list["lbl_time"].text.text = ToColorStr(Language.Boss.HasRefresh, COLOR3B.D_GREEN)
		end
		self.node_list["lbl_time"].text.text = ""
		if self.refresh_event then
			GlobalTimerQuest:CancelQuest(self.refresh_event)
			self.refresh_event = nil
		end
	end
end

function BossWorldItemRender:OnSelectChange(is_select)
	self.node_list.select_image:SetActive(is_select)
end

function BossWorldItemRender:IsHideBoss()
	return self.data.type == BossWGData.MonsterType.HideBoss
end

-- 设置点击回调
function BossWorldItemRender:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function BossWorldItemRender:OnClick()
	if self.click_callback then
		self.click_callback()
	end
end

function BossWorldItemRender:SetSelectedHL(index)
	local is_select = self.index == index

	self.node_list.select_image:SetActive(is_select)
end

------------------------------------------------------
-- 打宝/vipboss/worldBoss层按钮
------------------------------------------------------
BossLayerBtnRender = BossLayerBtnRender or BaseClass(BaseRender)
function BossLayerBtnRender:__init()
	-- self.is_slect_ceng = false
	self.cross_cache = nil
end

function BossLayerBtnRender:__delete()
	-- self.is_slect_ceng = nil
end

function BossLayerBtnRender:OnFlush()
	-- if self.data == nil or self.is_slect_ceng then return end
	if self.data == nil then return end
	self.view:SetActive(true)

	local name = ""
	if type(self.data) == "table" then
		name = self.data.name or ""
	else
		name = self.data
	end
	self.node_list["normal_text"].text.text = name
	self.node_list["select_text"].text.text = name
	self:SetJieValue()
end

function BossLayerBtnRender:SetJieValue()
	local max_index = BossWGCtrl.Instance:GetBossViewShowIndex()
	local list_data = {}
	if max_index == BossViewIndex.VipBoss then
		list_data = BossWGData.Instance:GetVipBossListByIndex(self.index)
	else
		if max_index == BossViewIndex.DabaoBoss then
			list_data = BossWGData.Instance:GetDabaoBossListByIndex(self.index)
		elseif BossViewIndex.WorldBoss == max_index then
			list_data = BossWGData.Instance:GetWorldBossListByLayer(self.index)
		end
	end

	if not list_data then
		return
	end

	local max = list_data[#list_data].boss_jieshu
	local min = list_data[1].boss_jieshu
	local jie_str = ""
	if max == min then
		jie_str = string.format(Language.Boss.JieText2, Language.Boss.hzNum[max + 1])
	else
		jie_str = string.format(Language.Boss.JieText1, Language.Boss.hzNum[min + 1], Language.Boss.hzNum[max + 1])
	end
	local need_level = list_data[1].need_role_level or list_data[1].boss_level
	if RoleWGData.Instance.role_vo.level >= need_level then
		self.node_list.jie_text.text.text = jie_str
		self.node_list.img_lock.text.text = ""
	else
		self.node_list.jie_text.text.text = ""
		self.node_list.img_lock.text.text = string.format(Language.XiuXianShiLian.Lock,
			RoleWGData.GetLevelString(need_level))
		if BossWGCtrl.Instance:GetBossViewShowIndex() == BossViewIndex.DabaoBoss then
			local falg, need_level2 = BossWGData.Instance:GetDaboBossIsEneter(self.index)
			self.node_list.img_lock.text.text = string.format(Language.XiuXianShiLian.Lock,
				RoleWGData.GetLevelString(need_level2))
		end
	end
end

function BossLayerBtnRender:OnSelectChange(is_select)
	self.node_list.select_image:SetActive(is_select)
end

--------------------------------------------------------------------------------
BossRewardCell = BossRewardCell or BaseClass(ItemCell)
function BossRewardCell:__init()
	self:SetSelectEffectImage(false)
end

function BossRewardCell:__delete()

end

function BossRewardCell:OnFlush()
	ItemCell.OnFlush(self)
end

function BossRewardCell:SetData(data)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
	self:GetView():SetActive(item_cfg ~= nil)
	if not item_cfg then
		return
	end

	local scale = data.cell_scale
	if scale then
		self.root_node.rect.localScale = u3dpool.vec3(scale, scale, scale)
	end

	if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		--2019.11.25 策划又去掉男女显示同职业装备的需求
		local param_t = {}
		param_t.star_level = 1
		data.param = param_t
		if data.item_id >= 22540 and data.item_id <= 22569 then --旧神兽装备
			self:SetLeftTopImg(2)
		end

		self:SetItemTipFrom(ItemTip.FROM_BOSS)
	end

	ItemCell.SetData(self, data)
end
