ActorTriggerSound = ActorTriggerSound or BaseClass(ActorTriggerBase)

-- 其他玩家的技能音效数限制
CUR_OTHER_CHARACTER_SKILL_SOUND_NUM = 0
MAX_OTHER_CHARACTER_SKILL_SOUND_NUM = 1

function ActorTriggerSound:__init(anima_name, obj_type)
	self.anima_name = anima_name
	self.obj_type = obj_type
	self.cur_sound_count = 0
	self.enabled = true

	self.sound_data = nil
end

function ActorTriggerSound:InitData(anima_name, obj_type)
	self.anima_name = anima_name
	self.obj_type = obj_type
	self.cur_sound_count = 0
	self.enabled = true

	self.sound_data = nil
	self:Reset()
end

function ActorTriggerSound:Reset()
	self.sound_data = nil
	self:StopSounds()
	ActorTriggerBase.Reset(self)
end

function ActorTriggerSound:__delete()
	self:StopSounds()
	self.sound_data = nil
end

-- 初始化预制体保存的配置数据(单个)
function ActorTriggerSound:Init(sound_data)
	self.sound_data = sound_data
	self.delay = sound_data.soundDelay
end

-- get/set
function ActorTriggerSound:Enalbed(value)
	if value == nil then
		return self.enabled
	end
	self.enabled = value
end

function ActorTriggerSound:OnEventTriggered(source, target, stateInfo)
	local sound_data = self.sound_data
	if not sound_data or not self.enabled then
		return
	end

	if sound_data.soundAudioAsset.IsEmpty then
		return
	end

	-- 限制其他玩家的技能音效数
	if self.obj_type == SceneObjType.Role then
		if CUR_OTHER_CHARACTER_SKILL_SOUND_NUM >= MAX_OTHER_CHARACTER_SKILL_SOUND_NUM then
			return
		end
	end

	self:StopSounds()
	local bundle_name = sound_data.soundAudioAsset.BundleName
	local asset_name = sound_data.soundAudioAsset.AssetName
	AudioManager.PlayAndForget(bundle_name, asset_name, nil, source.transform, function (audio_player)
		self.audio_player = audio_player
		if self.obj_type == SceneObjType.Role then
			CUR_OTHER_CHARACTER_SKILL_SOUND_NUM = CUR_OTHER_CHARACTER_SKILL_SOUND_NUM + 1
		end
	end,
	function ()
		if self.obj_type == SceneObjType.Role then
			CUR_OTHER_CHARACTER_SKILL_SOUND_NUM = CUR_OTHER_CHARACTER_SKILL_SOUND_NUM - 1
		end
	end)
end

function ActorTriggerSound:StopSounds()
	if self.audio_player then
		AudioManager.StopAudio(self.audio_player)
        self.audio_player = nil
	end
end
