------------------------------------------------------------
--背景属性View
------------------------------------------------------------
local ATTR_COUNT = 13
local MAX_DISPLAY_NUM = 11
ExperienceFBCardTipsView = ExperienceFBCardTipsView or BaseClass(SafeBaseView)

function ExperienceFBCardTipsView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
    self.view_name = "ExperienceFBCardTipsView"
    self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_experience_fb_card_tip")
end

function ExperienceFBCardTipsView:LoadCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}

	for i=1,ATTR_COUNT do
		self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
		if self.attr_list[i] then
			self.attr_name_list[i] = self.attr_list[i]:FindObj("attr_name")
			self.attr_value_list[i] = self.attr_list[i]:FindObj("attr_value")
		end
	end
	
	self:Flush()
end

function ExperienceFBCardTipsView:ReleaseCallBack()
	self.attr_list = nil
	self.attr_name_list = nil
	self.attr_value_list = nil
end

function ExperienceFBCardTipsView:OnFlush()
	self.node_list.attr_title_name.text.text = Language.Role.ExpWestAttrName
	local card_list = ExperienceFbWGData.Instance:GetAllCardInfo()
	local scene_info = ExperienceFbWGData.Instance:GetExpWestSceneInfo()

	if (not scene_info) or (not card_list) then
		return
	end

	local level = scene_info.level or 0

	local lv_cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(level)
	if not lv_cfg then
		return
	end

	local card_pool = lv_cfg.card_pool or 0
	local list = {}
	local flag_list = {}

	for i, card_info in ipairs(card_list) do
		local cfg = ExperienceFbWGData.Instance:GetCardCfgByIndexSeq(card_pool, card_info.choose_seq, card_info.choose_level)
		if cfg and cfg.type == 1 then
			if flag_list[cfg.seq] then
				if flag_list[cfg.seq].level < cfg.level then
					flag_list[cfg.seq] = cfg
				end
			else
				flag_list[cfg.seq] = cfg
			end
		end	
	end

	for k, v in pairs(flag_list) do
		table.insert(list, v)
	end

	local length = #list
	local index = 1
	if not IsEmptyTable(list) then
		for i = 1, ATTR_COUNT do
			local attr_data = list[i]
			if attr_data and self.attr_list[i] then
				---设置属性
				self.attr_name_list[i].text.text = attr_data.name
				self.attr_value_list[i].text.text = attr_data.desc
		   end

			if i <= length then
			   index = index + 1
			end
		end
	end

	if index < MAX_DISPLAY_NUM then
        self.node_list.attr_scroll.scroll_rect.enabled = false
    else
        self.node_list.attr_scroll.scroll_rect.enabled = true
    end

	if not IsEmptyTable(card_list) then
		for i = 1, ATTR_COUNT do
			self.attr_list[i]:SetActive(i <= length)
		end
	end

	self.node_list.img_no_record:CustomSetActive(length <= 0)
end
