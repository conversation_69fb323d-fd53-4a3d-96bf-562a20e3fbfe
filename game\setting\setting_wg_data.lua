-- 服务器最大只能存24条
HOT_KEY ={
	SYS_SETTING_1 = 1,								-- 系统设置1
	SYS_SETTING_2 = 2,								-- 系统设置2
	SYS_SETTING_DROPDOWN_1 = 3,						-- 系统设置存储DROPDOWN值
	SYS_SETTING_AUTO_OPERATE = 4,					-- 系统自动操作
	SYS_SETTING_DROPDOWN_2 = 5,						-- 系统设置存储DROPDOWN值
	MARRY_EQUIP = 6,								-- 情缘装备
	FB_ENTER_FLAG1 = 7,								-- 活动副本进入标记
	FB_ENTER_FLAG2 = 8,								-- 活动副本进入标记2
	FB_ENTER_FLAG3 = 9,								-- 活动副本进入标记3
	FB_ENTER_FLAG4 = 10,							-- 活动副本进入标记4
	FB_ENTER_FLAG5 = 11,							-- 活动副本进入标记5
	GUIDE_KEY_FLAG1 = 12,							-- 功能引导标记1
	GUIDE_KEY_FLAG2 = 13,							-- 功能引导标记2
	CAMERA_KEY_FLAG = 14,							-- 摄像机镜头
	CAMERA_ROTATION_X = 15,							-- 摄像机镜头参数-RotationX
	CAMERA_ROTATION_Y = 16,							-- 摄像机镜头参数-RotationY
	CAMERA_DISTANCE = 17,							-- 摄像机镜头参数-Distance
	SUPPLY = 18,									-- 补给设置
	AUTO_PICK_BREAK = 19,							-- 自动拾取自动分解
	GUIDE_KEY_FLAG3 = 14,							-- 功能引导标记3
	GUIDE_KEY_FLAG4 = 15,							-- 功能引导标记4
}

SETTING_TYPE = {
	SHIELD_OTHERS 		= 1,						--屏蔽其他(敌方)玩家
	SELF_SKILL_EFFECT	= 2,						--屏蔽自己技能特效
	SHIELD_SAME_CAMP 	= 3,						--屏蔽友方玩家
	SKILL_EFFECT 		= 4,						--屏蔽他人技能特效
	CLOSE_BG_MUSIC 		= 5,						--关闭背景音乐
	CLOSE_SOUND_EFFECT	= 6,						--关闭音效
	FLOWER_EFFECT		= 7,						--屏蔽送花特效
	FRIEND_REQUEST 		= 8,						--拒绝好友邀请
	STRANGER_CHAT 		= 9,						--拒绝陌生私聊
	CLOSE_TITLE			= 10,   					--屏蔽称号显示
	CLOSE_HEARSAY		= 11,						--屏蔽传闻
	CLOSE_GODDESS		= 12,						--屏蔽女神
	AUTO_RELEASE_SKILL	= 13,						--自动释放技能
	AUTO_RELEASE_ANGER	= 14,						--自动释放怒气技能
	CLOSE_SHOCK_SCREEN	= 15,						--关闭震屏效果
	SHIELD_ENEMY 		= 16,						--屏蔽怪物
	GUAJI_PICKUP_GREEN	= 17,						--自动拾取绿色装备
	GUAJI_PICKUP_BLUE	= 18,						--自动拾取蓝色装备
	GUAJI_PICKUP_PURPLE	= 19,						--自动拾取紫色装备
	GUAJI_PICKUP_ORANGE	= 20,						--自动拾取橙色以上装备
	GUAJI_PICKUP_COIN	= 21,						--自动拾取金币
	GUAJI_PICKUP_OTHER	= 22,						--自动拾取其它道具
	GUAJI_SCALE			= 23,						--自动出售绿蓝装备
	GUAJI_RONGLIAN		= 24,						--自动熔炼紫色装备
	GUAJI_LOCK			= 25,						--自动锁屏
	AUTO_BIANSHEN		= 26,						--自动变身
	AUTO_REVIVE			= 27,						--自动复活
	CHANGE_FPS			= 28,						--切换帧率
	HIDE_GUIZHULEVEL	= 29,						--隐藏贵族气息
	FIX_POS_GUAJI 		= 30,						-- 定点挂机标志
	TIANSHEN_CLOSE_UP   = 31, 						--屏蔽天神变身特写
	AUTO_SIXIANG_SKILL	= 32,						--自动释放四象技能
	AUTO_LONGZHU_SKILL	= 33,						--自动释放龙珠技能
	AUTO_WUXING_SKILL	= 34,						--自动释放五行技能
	REFUSE_STRANGER_TEAM = 35,						-- 拒绝所有人组队邀请（原 拒绝陌生人）
	AUTO_HALO_SKILL     = 36,                       -- 自动释放光环技能
	FIGHT_MOUNT_SKILL_CG_PLAY  = 37,                -- 战斗坐骑技能cg播放
	AUTO_TIANSHEN_HEJI_SKILL     = 38,              -- 挂机时自动释放天神合击技能
	AUTO_YU_LONG        = 39,                       -- 挂机时自动御龙
	AUTO_CUSTOMIZED_SKILL = 40,						-- 挂机时自动释放定制技能
	AUTO_GUNDAM			= 41,						-- 自动高达
	AUTO_ESOTERICA_SKILL = 42,						-- 挂机时自动释放秘笈技能
	CLOSE_OTHERS_RUMOR = 43,						-- 屏蔽自定义传闻
	CLOSE_OTHERS_BEAST = 44,						-- 屏蔽御兽
	AUTO_JINGJIE_BIANSHEN		= 45,				-- 自动境界变身
	AUTO_BEASTS_SKILL = 46,							-- 自动释放御兽技能
	CLOSE_SELECT_EFFECT = 47,						-- 关闭攻击选中效果
	-- CLOSE_WEATHWE = 30,								--关闭天气
	-- AUTO_RECYCLE_COLOR = 100,						--自动出售装备颜色
	-- AUTO_PICK_GREEN	= 101,							--自动拾取绿色
	-- AUTO_PICK_BLUE = 102,							--自动拾取蓝色
	-- AUTO_PICK_PURPLE = 103,							--自动拾取紫色
	-- AUTO_PICK_ORANGE = 104,							--自动拾取橙色以上
	-- AUTO_PICK_COIN = 105,							--自动拾取金钱
	-- AUTO_PICK_OTHER = 106,							--自动拾取其他
	-- AUTO_LUCK_SCREEN = 107,							--自动上锁
}

GUAJI_SETTING_TYPE = {
	GUAJI_PICKUP_GREEN	= 1,						--自动拾取绿色装备
	GUAJI_PICKUP_BLUE	= 2,						--自动拾取蓝色装备
	GUAJI_PICKUP_PURPLE	= 3,						--自动拾取紫色装备
	GUAJI_PICKUP_ORANGE	= 4,						--自动拾取橙色以上装备
	GUAJI_PICKUP_COIN	= 5,						--自动拾取金币
	GUAJI_PICKUP_OTHER	= 6,						--自动拾取其它道具
	GUAJI_SCALE			= 7,						--自动出售绿蓝装备
	GUAJI_RONGLIAN		= 8,						--自动熔炼紫色装备
	GUAJI_LOCK			= 9,						--自动锁屏
}

-- 有些“老板机”型号直接使用最高推荐画质
HIGHEST_QUALITY_PHONE_MODEL = 
{
	["PAL-AL00"] = true, --华为X3
	["ALT-AL10"] = true, --华为X5
	["GRL-AL10"] = true, --华为XT
	["ADY-AL10"] = true, --华为P70
	["HBN-AL80"] = true, --华为P70 Pro+
	["HBP-AL00"] = true, --华为P70 Ultra
	["HBP-AL00"] = true, --华为Mate60 
	["ALN-AL80"] = true, --华为Mate60 Pro
	["ALN-AL10"] = true, --华为Mate60 Pro+
}

-- 帧率相关的控制
local fps_sampler_time = 5 		-- 帧率采样时间
local lower_fps_times = 4 		-- 低帧率连续出现次数
local auto_setting_fps = 30 	-- 低于多少帧率启动自动屏弊
SYSTEM_AUTO_OPERATE = {}

function InitFpsSetting()
	if GAME_FPS <= 30 then
		auto_setting_fps = 25

		SYSTEM_AUTO_OPERATE =
		{
			[SETTING_TYPE.SHIELD_OTHERS] = {fps = 25, index = 32},
			[SETTING_TYPE.SHIELD_SAME_CAMP] = {fps = 25, index = 31},
			[SETTING_TYPE.SKILL_EFFECT] = {fps = 25, index = 30},
			[SETTING_TYPE.FLOWER_EFFECT] = {fps = 25, index = 29},
			[SETTING_TYPE.CLOSE_TITLE] = {fps = 25, index = 28},
			[SETTING_TYPE.CLOSE_GODDESS] = {fps = 25, index = 27},
			[SETTING_TYPE.CLOSE_SHOCK_SCREEN] = {fps = 25, index = 26},
			[SETTING_TYPE.SHIELD_ENEMY] = {fps = 25, index = 25},
			[SETTING_TYPE.SHIELD_SPIRIT] = {fps = 25, index = 24},
			[SETTING_TYPE.CLOSE_WEATHWE] = {fps = 30, index = 23},
		}
	else
		auto_setting_fps = 28

		SYSTEM_AUTO_OPERATE =
		{
			[SETTING_TYPE.SHIELD_OTHERS] = {fps = 25, index = 32},
			[SETTING_TYPE.SHIELD_SAME_CAMP] = {fps = 30, index = 31},
			[SETTING_TYPE.SKILL_EFFECT] = {fps = 30, index = 30},
			[SETTING_TYPE.FLOWER_EFFECT] = {fps = 25, index = 29},
			[SETTING_TYPE.CLOSE_TITLE] = {fps = 25, index = 28},
			[SETTING_TYPE.CLOSE_GODDESS] = {fps = 30, index = 27},
			[SETTING_TYPE.CLOSE_SHOCK_SCREEN] = {fps = 30, index = 26},
			[SETTING_TYPE.SHIELD_ENEMY] = {fps = 25, index = 25},
			[SETTING_TYPE.SHIELD_SPIRIT] = {fps = 25, index = 24},
			[SETTING_TYPE.CLOSE_WEATHWE] = {fps = 30, index = 23},
		}
	end
end

SettingWGData = SettingWGData or BaseClass()
SettingWGData.MAX_INDEX = 32
SEND_CUSTOM_TYPE =
{
	SUGGEST = 1,
	SUBMIT_BUG = 2,
	COMPLAINT = 3,
}

SettingPanel1 =											--设置
{
	[1] = SETTING_TYPE.SHIELD_OTHERS,
	[2] = SETTING_TYPE.SELF_SKILL_EFFECT,
	[3] = SETTING_TYPE.SHIELD_SAME_CAMP,
	[4] = SETTING_TYPE.SKILL_EFFECT,
	[6] = SETTING_TYPE.FRIEND_REQUEST,
	[8] = SETTING_TYPE.FLOWER_EFFECT,
	[9] = SETTING_TYPE.SHIELD_ENEMY,
	[10] = SETTING_TYPE.CLOSE_TITLE,
	[11] = SETTING_TYPE.STRANGER_CHAT,
	[12] = SETTING_TYPE.CLOSE_HEARSAY,
	[13] = SETTING_TYPE.CLOSE_SOUND_EFFECT,
	[14] = SETTING_TYPE.CLOSE_BG_MUSIC,
	[15] = SETTING_TYPE.CHANGE_FPS,
	[16] = SETTING_TYPE.HIDE_GUIZHULEVEL,
	[17] = SETTING_TYPE.TIANSHEN_CLOSE_UP,
	[18] = SETTING_TYPE.REFUSE_STRANGER_TEAM,
	[19] = SETTING_TYPE.CLOSE_SHOCK_SCREEN,
	[20] = SETTING_TYPE.CLOSE_OTHERS_RUMOR,
	[21] = SETTING_TYPE.CLOSE_OTHERS_BEAST,
	[22] = SETTING_TYPE.CLOSE_SELECT_EFFECT,
}

SettingPanel2 =											--挂机
{
	[1] = SETTING_TYPE.GUAJI_PICKUP_GREEN,
	[2] = SETTING_TYPE.GUAJI_PICKUP_BLUE,
	[3] = SETTING_TYPE.GUAJI_PICKUP_PURPLE,
	[4] = SETTING_TYPE.GUAJI_PICKUP_ORANGE,
	[5] = SETTING_TYPE.GUAJI_PICKUP_COIN,
	[6] = SETTING_TYPE.GUAJI_PICKUP_OTHER,
	[7] = SETTING_TYPE.GUAJI_SCALE,
	[8] = SETTING_TYPE.GUAJI_RONGLIAN,
	[9] = SETTING_TYPE.GUAJI_LOCK,
	[10] = SETTING_TYPE.AUTO_BIANSHEN,
	[11] = SETTING_TYPE.AUTO_REVIVE,
	[12] = SETTING_TYPE.FIX_POS_GUAJI,
	[13] = SETTING_TYPE.AUTO_SIXIANG_SKILL,
	[14] = SETTING_TYPE.AUTO_LONGZHU_SKILL,
	[15] = SETTING_TYPE.AUTO_WUXING_SKILL,
	[16] = SETTING_TYPE.AUTO_HALO_SKILL,
	[17] = SETTING_TYPE.FIGHT_MOUNT_SKILL_CG_PLAY,
	[18] = SETTING_TYPE.AUTO_TIANSHEN_HEJI_SKILL,
	[19] = SETTING_TYPE.AUTO_YU_LONG,
	[20] = SETTING_TYPE.AUTO_CUSTOMIZED_SKILL,
	[21] = SETTING_TYPE.AUTO_GUNDAM,
	[22] = SETTING_TYPE.AUTO_ESOTERICA_SKILL,
	[23] = SETTING_TYPE.AUTO_JINGJIE_BIANSHEN,
	[24] = SETTING_TYPE.AUTO_BEASTS_SKILL,
}

SettingPanel3 =											--技能
{
	[1] = GUAJI_SETTING_TYPE.GUAJI_SKILL_1,
	[2] = GUAJI_SETTING_TYPE.GUAJI_SKILL_2,
	[3] = GUAJI_SETTING_TYPE.GUAJI_SKILL_3,
	[4] = GUAJI_SETTING_TYPE.GUAJI_SKILL_4,
	[5] = GUAJI_SETTING_TYPE.GUAJI_SKILL_5,
	[6] = GUAJI_SETTING_TYPE.GUAJI_SKILL_6,
	[7] = GUAJI_SETTING_TYPE.GUAJI_SKILL_7,
}

FixBugSettting = {
	SETTING_TYPE.AUTO_RELEASE_SKILL, --自动释放技能
	SETTING_TYPE.AUTO_RELEASE_ANGER,
	-- SETTING_TYPE.AUTO_PICK_PROPERTY,
	-- SETTING_TYPE.AUTO_LUCK_SCREEN,
	-- SETTING_TYPE.AUTO_PICK_GREEN,
	-- SETTING_TYPE.AUTO_PICK_BLUE,
	-- SETTING_TYPE.AUTO_PICK_PURPLE,
	-- SETTING_TYPE.AUTO_PICK_ORANGE,
	--SETTING_TYPE.AUTO_RELEASE_GODDESS_SKILL,
}

-- 画质等级常量
local QUALITY_LEVEL = {
	EXTREME = 0,    -- 极致
	HIGH = 1,       -- 高清
	MEDIUM = 2,     -- 流畅
	LOW = 3         -- 省电
}

-- 硬件配置阈值
local HARDWARE_THRESHOLDS = {
	-- iOS设备版本阈值
	IOS = {
		IPHONE_LOW_END = 10,
		IPHONE_MID_END = 12,
		IPHONE_HIGH_END = 15,
		IPAD_LOW_END = 11,
		IPAD_MID_END = 12,
	},
	
	-- Android通用阈值
	ANDROID = {
		-- 系统版本
		MIN_VERSION = 9,
		MID_VERSION = 11,
		HIGH_VERSION = 12,
		FLAGSHIP_VERSION = 14,
		
		-- 内存 (MB)
		MIN_MEMORY = 3000,
		MID_MEMORY = 7000,
		HIGH_MEMORY = 9000,
		
		-- CPU核心数
		MIN_CORES = 6,
		MID_CORES = 8,
		
		-- CPU频率 (Hz)
		LOW_CPU_HZ = 1500000,
		MID_CPU_HZ = 2000000,
		HIGH_CPU_HZ = 5000000,
		FLAGSHIP_CPU_HZ = 6000000,
	},
	
	-- 小米/红米特殊阈值
	XIAOMI = {
		LOW_CPU_HZ = 3000000,
		HIGH_CPU_HZ = 5000000,
		FLAGSHIP_CPU_HZ = 6000000,
	},
	
	-- 其他平台
	OTHER = {
		MIN_RENDER_TARGETS = 2,
		HIGH_RENDER_TARGETS = 4,
		MIN_SYSTEM_MEMORY = 2000,
		MID_SYSTEM_MEMORY = 3000,
		HIGH_SYSTEM_MEMORY = 6000,
		MIN_GRAPHICS_MEMORY = 256,
		MID_GRAPHICS_MEMORY = 400,
		HIGH_GRAPHICS_MEMORY = 500,
		MIN_PROCESSOR_COUNT = 2,
		MID_PROCESSOR_COUNT = 4,
		HIGH_PROCESSOR_COUNT = 8,
		MIN_PROCESSOR_FREQ = 1500,
		MID_PROCESSOR_FREQ = 2000,
		HIGH_PROCESSOR_FREQ = 2200,
	}
}

function SettingWGData:__init()
	if SettingWGData.Instance then
		print_error("[SettingWGData] Attemp to create a singleton twice !")
	end
	SettingWGData.Instance = self
	self:BindFps()
	self.setting_list = {}

	self.is_first = true
	self.reward_info = ConfigManager.Instance:GetAutoConfig("updatenotice_auto").other[1]
	self.server_version = 0
	self.fetch_reward_version = 0
	self.main_ui_is_open = false
	self.system_is_setting = false
	self.system_is_setting_t = {}
	self.has_setting = {}
	self.set_data_list = {}
	self.fb_enter_flag1 = {}
	self.fb_enter_flag2 = {}
	self.fb_enter_flag3 = {}
	self.fb_enter_flag4 = {}
	self.fb_enter_flag5 = {}
	self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	RemindManager.Instance:Register(RemindName.Setting, BindTool.Bind(self.GetSettingRemind, self))
	--self.set_fun_open_change = BindTool.Bind(self.FunOpenReShow, self)
	--FunOpen.Instance:NotifyFunOpen(self.set_fun_open_change, self)


	self.screen_bright = 0
	self.fight_toggle_state = false
	self.is_show_cg = false

	self.is_bugfixed_on_first_recv = false
	self.need_luck_view = false
	self.low_fps_count = 0
	self.bugfix_record_t = {}
	self.recommend_quality = self:CheckRecommendQuality()

	self.user_default = {}

	-- InitFpsSetting()

	self.setting_flag_t = {}
	self.guaji_flag_t = {}
	for i=1,32 do
		self.setting_flag_t[i] = 0
		self.guaji_flag_t[i] = 0
	end

	self.notic_data = {}
end

function SettingWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.Setting)
	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end

	-- if self.set_fun_open_change then
	-- 	FunOpen.Instance:UnNotifyFunOpen(self.set_fun_open_change, self)
	-- 	self.set_fun_open_change = nil
	-- end

	SettingWGData.Instance = nil
	self:UnbindFps()


end

function SettingWGData:GetSettingDataListByKey(key)
	local set_data = {}
	set_data = self.set_data_list[key] or {}
	return set_data
end

function SettingWGData:SetSettingDataListByKey(key, item_id, _type)
	local set_data = {}
	set_data = self.set_data_list[key] or {}
	set_data.item_id = item_id or 0
	set_data.type = _type or 0
	self.set_data_list[key] = set_data
end

--只在首次打开面板时调用全球通知
function SettingWGData:OnSettingInfo(set_data)
	self.set_data_list = set_data
	if #set_data ~= 0 then
		for k,v in pairs(HOT_KEY) do
			if self.set_data_list[v] == nil then
				self.set_data_list[v] = {index = v, type = 1, item_id = 0}
			end
		end
	end

	local set_data_list = self.set_data_list
	for k, v in pairs(set_data_list) do
		local item_id_list = bit:d2b(v.item_id)
		if v.index == HOT_KEY.SYS_SETTING_1 then
			for k1, v1 in pairs(SettingPanel1) do
				-- if not self.system_is_setting and self:CheckSystemAuto(i) then
				-- 	self.setting_list[i] = false
				-- 	item_id_list[33 - i] = 0
				-- else
					self.setting_list[v1] = (item_id_list[33 - k1] == 1)
				-- end

				self:FirstGlobleSetting(v1, self.setting_list[v1])
			end
			-- if not self.system_is_setting and set_data_list[4].item_id ~= 0 then
			-- 	local value = bit:b2d(item_id_list)
			-- 	SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.SYS_SETTING_1, value)
			-- 	SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.SYS_SETTING_AUTO_OPERATE, 0)
			-- end
		elseif v.index == HOT_KEY.SYS_SETTING_2 then
			for k1, v1 in pairs(SettingPanel2) do
				-- if not self.system_is_setting and self:CheckSystemAuto(i) then
				-- 	self.setting_list[i] = false
				-- 	item_id_list[33 - i + 16] = 0
				-- else
				self.setting_list[v1] = (item_id_list[33 - k1] == 1)
				-- end
				self:FirstGlobleSetting(v1, self.setting_list[v1])
			end
			-- if not self.system_is_setting and set_data_list[4].item_id ~= 0 then
			-- 	local value = bit:b2d(item_id_list)
			-- 	SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.SYS_SETTING_2, value)
			-- 	SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.SYS_SETTING_AUTO_OPERATE, 0)
			-- end

		-- elseif v.index == HOT_KEY.SYS_SETTING_DROPDOWN_1 then
			-- self:SetPickLimitValue(v.item_id)
			-- for i=1,16 do
			-- 	if not self.system_is_setting and self:CheckSystemAuto(100 + i) then
			-- 		self.setting_list[100 + i] = false
			-- 		item_id_list[33 - i] = 0
			-- 	else
			-- 		self.setting_list[100 + i] = (item_id_list[33 - i] == 1 and true or false)
			-- 	end
			-- 	self:FirstGlobleSetting(100 + i, self.setting_list[100 + i])
			-- end
			-- if not self.system_is_setting and set_data_list[4].item_id ~= 0 then
			-- 	local value = bit:b2d(item_id_list)
			-- 	SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.SYS_SETTING_DROPDOWN_1, value)
			-- 	SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.SYS_SETTING_AUTO_OPERATE, 0)
			-- end
		-- elseif v.index == HOT_KEY.SYS_SETTING_DROPDOWN_2 then
			-- self:SetRecycleLimitValue(v.item_id)
		elseif v.index == HOT_KEY.CAMERA_KEY_FLAG then
			GlobalEventSystem:Fire(SettingEventType.MAIN_CAMERA_MODE_CHANGE, v.item_id)
		end
	end

	self.is_first = false
	-- if not self.system_is_setting and set_data_list[4].item_id ~= 0 then
	-- 	SettingWGCtrl.Instance:SendHotkeyInfoReq()
	-- end

	-- self.fb_enter_flag1 = bit:d2b(set_data_list[HOT_KEY.FB_ENTER_FLAG1].item_id)
	-- self.fb_enter_flag2 = bit:d2b(set_data_list[HOT_KEY.FB_ENTER_FLAG2].item_id)
	-- self.fb_enter_flag3 = bit:d2b(set_data_list[HOT_KEY.FB_ENTER_FLAG3].item_id)
	-- self.fb_enter_flag4 = bit:d2b(set_data_list[HOT_KEY.FB_ENTER_FLAG4].item_id)
	-- self.fb_enter_flag5 = bit:d2b(set_data_list[HOT_KEY.FB_ENTER_FLAG5].item_id)
	-- self.marry_guaji_index = set_data_list[HOT_KEY.MARRY_EQUIP].item_id
end

function SettingWGData:SetHasSetting(index)
	self.has_setting[index] = true
end

function SettingWGData:GetSettingList()
	return self.setting_list
end

function SettingWGData:OnUpdateNoticeInfo(protocol)
	self.server_version = protocol.server_version
	self.fetch_reward_version = protocol.fetch_reward_version
end

function SettingWGData:GetSettingData(setting_type)
	-- if IsLowMemSystem and setting_type == SETTING_TYPE.SKILL_EFFECT then
	-- 	return true
	-- end

	if setting_type == nil then
		return false
	end
	
	return self.setting_list[setting_type]
end

--首次全球通知
function SettingWGData:FirstGlobleSetting(setting_type, value)
	if self.is_first then
		GlobalEventSystem:Fire(self:GetGlobleType(setting_type), self:FixBugValueOnFire(setting_type, value))
	end
end

--每次改变状态时调用
function SettingWGData:SetSettingData1(setting_type, value, is_hot_key)
	if setting_type ~= nil then
		self.setting_list[setting_type] = value
		GlobalEventSystem:Fire(self:GetGlobleType(setting_type), self:FixBugValueOnFire(setting_type, value))
	end

	-- if setting_type == SETTING_TYPE.AUTO_PICK_PROPERTY and self.setting_list[SETTING_TYPE.AUTO_PICK_PROPERTY] then
		-- GlobalEventSystem:Fire(SettingEventType.AUTO_PICK_COLOR, self.setting_list[SETTING_TYPE.AUTO_PICK_COLOR])
	-- if setting_type == SETTING_TYPE.AUTO_RECYCLE_EQUIP then
	-- 	-- GlobalEventSystem:Fire(SettingEventType.AUTO_RECYCLE_COLOR, self.setting_list[SETTING_TYPE.AUTO_RECYCLE_COLOR])
	-- 	if self.setting_list[SETTING_TYPE.AUTO_RECYCLE_EQUIP] then
	-- 		self:SetRecycleLimitValue(2)																		--自动出售绿蓝以下装备
	-- 	else
	-- 		self:SetRecycleLimitValue(0)
	-- 	end
	-- end

	-- if setting_type == SETTING_TYPE.AUTO_RECYCLE_EQUIP or setting_type == SETTING_TYPE.AUTO_TUNSHI_EQUIP then
	-- 	local result1 = self.setting_list[SETTING_TYPE.AUTO_RECYCLE_EQUIP] and 1 or 0
	-- 	local result2 = self.setting_list[SETTING_TYPE.AUTO_TUNSHI_EQUIP] and 1 or 0
	-- 	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_OFFLINE_SETTING_INFO_REQ, result1, result2)
	-- end
	--拒绝陌生人私聊
	-- if setting_type == SETTING_TYPE.STRANGER_CHAT and value == true then
	-- 	SettingWGCtrl.Instance:SendChangeStrangerChat(CLIENT_SETTING_TYPE.CLIENT_SETTING_TYPE_REFUSE_SINGLE_CHAT, 1)
	-- elseif setting_type == SETTING_TYPE.STRANGER_CHAT and value == false then
	-- 	SettingWGCtrl.Instance:SendChangeStrangerChat(CLIENT_SETTING_TYPE.CLIENT_SETTING_TYPE_REFUSE_SINGLE_CHAT, 0)
	-- end

	if is_hot_key then
		self:HotKeyToSetting()
	end
end

--快捷方式进行设置
function SettingWGData:HotKeyToSetting()
	local temp_list_1 = {}
	local temp_list_2 = {}
	for i=1,32 do
		temp_list_1[i] = 0
		temp_list_2[i] = 0
	end

	for k, v in pairs(SettingPanel1) do
		temp_list_1[33 - k] = self.setting_list[v] and 1 or 0
	end
	for k, v in pairs(SettingPanel2) do
		temp_list_2[33 - k] = self.setting_list[v] and 1 or 0
	end

	local value_1 = bit:b2d(temp_list_1)
	local value_2 = bit:b2d(temp_list_2)
	-- local value_3 = bit:b2d(temp_list_3)
	-- local value_4 = self.setting_list[SETTING_TYPE.AUTO_RECYCLE_COLOR]
	local list = {
		[1] = {HOT_KEY.SYS_SETTING_1, value_1},
		[2] = {HOT_KEY.SYS_SETTING_2, value_2}
	}

	local mainrole = Scene.Instance:GetMainRole()
	if mainrole then
		local mainrole_vo = mainrole.vo
		mainrole:SetAttr("vip_level",mainrole_vo.vip_level)
	end
	SettingWGCtrl.Instance:SendChangeHotkeyReq(list)
	-- SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.SYS_SETTING_DROPDOWN_1, value_3)
	-- SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.SYS_SETTING_DROPDOWN_2, value_4)
	-- SettingWGCtrl.Instance:SendHotkeyInfoReq()
end

-- function SettingWGData:SetPickLimitValue(limit_pick_color_value)
-- 	self.setting_list[SETTING_TYPE.AUTO_PICK_COLOR] = limit_pick_color_value
-- 	if self.setting_list[SETTING_TYPE.AUTO_PICK_PROPERTY] then
-- 		GlobalEventSystem:Fire(SettingEventType.AUTO_PICK_COLOR, limit_pick_color_value)
-- 	end
-- end

function SettingWGData:SetRecycleLimitValue(limit_recycle_color_value)
	self.setting_list[SETTING_TYPE.AUTO_RECYCLE_COLOR] = limit_recycle_color_value
	if self.setting_list[SETTING_TYPE.AUTO_RECYCLE_EQUIP] then
		GlobalEventSystem:Fire(SettingEventType.AUTO_RECYCLE_COLOR, limit_recycle_color_value)
	end
end

function SettingWGData:GetGlobleType(setting_type)
	if setting_type == SETTING_TYPE.SHIELD_OTHERS then
		return SettingEventType.SHIELD_OTHERS
	elseif setting_type == SETTING_TYPE.SELF_SKILL_EFFECT then
		return SettingEventType.SELF_SKILL_EFFECT
	elseif setting_type == SETTING_TYPE.SHIELD_SAME_CAMP then
		return SettingEventType.SHIELD_SAME_CAMP
	elseif setting_type == SETTING_TYPE.SKILL_EFFECT then
		return SettingEventType.SKILL_EFFECT
	elseif setting_type == SETTING_TYPE.CLOSE_BG_MUSIC then
		return SettingEventType.CLOSE_BG_MUSIC
	elseif setting_type == SETTING_TYPE.CHANGE_FPS then
		return SettingEventType.CHANGE_FPS
	elseif setting_type == SETTING_TYPE.CLOSE_SOUND_EFFECT then
		return SettingEventType.CLOSE_SOUND_EFFECT
	elseif setting_type == SETTING_TYPE.FLOWER_EFFECT then
		return SettingEventType.FLOWER_EFFECT
	elseif setting_type == SETTING_TYPE.FRIEND_REQUEST then
		return SettingEventType.FRIEND_REQUEST
	elseif setting_type == SETTING_TYPE.STRANGER_CHAT then
		return SettingEventType.STRANGER_CHAT
	elseif setting_type == SETTING_TYPE.CLOSE_TITLE then
		return SettingEventType.CLOSE_TITLE
	elseif setting_type == SETTING_TYPE.CLOSE_HEARSAY then
		return SettingEventType.CLOSE_HEARSAY
	elseif setting_type == SETTING_TYPE.CLOSE_GODDESS then
		return SettingEventType.CLOSE_GODDESS
	elseif setting_type == SETTING_TYPE.AUTO_RELEASE_SKILL then
		return SettingEventType.AUTO_RELEASE_SKILL
	elseif setting_type == SETTING_TYPE.AUTO_RELEASE_ANGER then
		return SettingEventType.AUTO_RELEASE_ANGER
	elseif setting_type == SETTING_TYPE.CLOSE_SHOCK_SCREEN then
		return SettingEventType.CLOSE_SHOCK_SCREEN
	-- elseif setting_type == SETTING_TYPE.AUTO_PICK_PROPERTY then
	-- 	return SettingEventType.AUTO_PICK_PROPERTY
	elseif setting_type == SETTING_TYPE.AUTO_RECYCLE_EQUIP then
		return SettingEventType.AUTO_RECYCLE_EQUIP
	-- elseif setting_type == SETTING_TYPE.USE_NOTBLIND_EQUIP then
	-- 	return SettingEventType.USE_NOTBLIND_EQUIP
	elseif setting_type == SETTING_TYPE.SHIELD_ENEMY then
		return SettingEventType.SHIELD_ENEMY
	elseif setting_type == SETTING_TYPE.SHIELD_SPIRIT then
		return SettingEventType.SHIELD_SPIRIT
	elseif setting_type == SETTING_TYPE.CLOSE_WEATHWE then
		return SettingEventType.CLOSE_WEATHWE
	elseif setting_type == SETTING_TYPE.AUTO_RECYCLE_BLUE then
		return SettingEventType.AUTO_RECYCLE_BLUE
	elseif setting_type == SETTING_TYPE.AUTO_RECYCLE_PURPLE then
		return SettingEventType.AUTO_RECYCLE_PURPLE
	elseif setting_type == SETTING_TYPE.AUTO_RECYCLE_ORANGE then
		return SettingEventType.AUTO_RECYCLE_ORANGE
	elseif setting_type == SETTING_TYPE.GUAJI_LOCK then
		return SettingEventType.AUTO_LUCK_SCREEN
	elseif setting_type == SETTING_TYPE.AUTO_BIANSHEN then
		return SettingEventType.AUTO_BIANSHEN
	elseif setting_type == SETTING_TYPE.AUTO_REVIVE then
		return SettingEventType.AUTO_REVIVE
	elseif setting_type == SETTING_TYPE.AUTO_SIXIANG_SKILL then
		return SettingEventType.AUTO_SIXIANG_SKILL
	elseif setting_type == SETTING_TYPE.AUTO_LONGZHU_SKILL then
		return SettingEventType.AUTO_LONGZHU_SKILL
	elseif setting_type == SETTING_TYPE.AUTO_WUXING_SKILL then
		return SettingEventType.AUTO_WUXING_SKILL
	elseif setting_type == SETTING_TYPE.AUTO_HALO_SKILL then
		return SettingEventType.AUTO_HALO_SKILL
	elseif setting_type == SETTING_TYPE.FIGHT_MOUNT_SKILL_CG_PLAY then
		return SettingEventType.FIGHT_MOUNT_SKILL_CG_PLAY
	elseif setting_type == SETTING_TYPE.AUTO_TIANSHEN_HEJI_SKILL then
		return SettingEventType.AUTO_TIANSHEN_HEJI_SKILL
	elseif setting_type == SETTING_TYPE.AUTO_YU_LONG then
		return SettingEventType.AUTO_YU_LONG
	elseif setting_type == SETTING_TYPE.AUTO_CUSTOMIZED_SKILL then
		return SettingEventType.AUTO_CUSTOMIZED_SKILL
	elseif setting_type == SETTING_TYPE.AUTO_ESOTERICA_SKILL then
		return SettingEventType.AUTO_ESOTERICA_SKILL
	elseif setting_type == SETTING_TYPE.AUTO_BEASTS_SKILL then
		return SettingEventType.AUTO_BEASTS_SKILL
	elseif setting_type == SETTING_TYPE.CLOSE_SELECT_EFFECT then
		return SettingEventType.CLOSE_SELECT_EFFECT
	-- elseif setting_type == SETTING_TYPE.AUTO_RELEASE_GODDESS_SKILL then
	-- 	return SettingEventType.AUTO_RELEASE_GODDESS_SKILL
	-- elseif setting_type == SETTING_TYPE.AUTO_USE_FLY_SHOE then
	-- 	return SettingEventType.AUTO_USE_FLY_SHOE
	-- elseif setting_type == HOT_KEY.CAMERA_ROTATION_X or setting_type == HOT_KEY.CAMERA_ROTATION_Y or setting_type == HOT_KEY.CAMERA_DISTANCE then
	-- 	return SettingEventType.MAIN_CAMERA_SETTING_CHANGE
	elseif setting_type == SETTING_TYPE.REFUSE_STRANGER_TEAM then
		return SettingEventType.REFUSE_STRANGER_TEAM
	elseif setting_type == SETTING_TYPE.AUTO_GUNDAM then
		return SettingEventType.AUTO_GUNDAM
	elseif setting_type == SETTING_TYPE.CLOSE_OTHERS_RUMOR then
		return SettingEventType.CLOSE_OTHERS_RUMOR
	elseif setting_type == SETTING_TYPE.CLOSE_OTHERS_BEAST then
		return SettingEventType.CLOSE_OTHERS_BEAST
	elseif setting_type == SETTING_TYPE.AUTO_JINGJIE_BIANSHEN then
		return SettingEventType.AUTO_JINGJIE_BIANSHEN
	end
	return ""
end

function SettingWGData:SetDefaultSettingList()
	for k,v in pairs(FixBugSettting) do
		self.setting_list[v] = true
	end

	-- 模拟器默认高帧率
	if DeviceTool.IsEmulator() or IS_LOCLA_WINDOWS_DEBUG_EXE then
		self.setting_list[SETTING_TYPE.CHANGE_FPS] = true
	end

	local temp_list_1 = {}
	local temp_list_2 = {}
	local temp_list_3 = {}
	for i=1,32 do
		temp_list_1[i] = 0
		temp_list_2[i] = 0
		temp_list_3[i] = 0
	end
	for i=1, SettingWGData.MAX_INDEX do
		if i <= 16 then
			temp_list_1[33 - i] = self.setting_list[i] and 1 or 0
			temp_list_3[33 - i] = self.setting_list[100 + i] and 1 or 0
		else
			temp_list_2[33 - i + 16] = self.setting_list[i] and 1 or 0
		end
	end

	local value_1 = bit:b2d(temp_list_1)
	local value_2 = bit:b2d(temp_list_2)
	local value_3 = bit:b2d(temp_list_3)
	local set_data_list = {
		[HOT_KEY.SYS_SETTING_1] = {index = HOT_KEY.SYS_SETTING_1, type = 1, item_id = value_1},
		[HOT_KEY.SYS_SETTING_2] = {index = HOT_KEY.SYS_SETTING_2, type = 1, item_id = value_2},
		[HOT_KEY.SYS_SETTING_DROPDOWN_1] = {index = HOT_KEY.SYS_SETTING_DROPDOWN_1, type = 1, item_id = value_3}
	}
	for k,v in pairs(HOT_KEY) do
		if set_data_list[v] == nil then
			set_data_list[v] = {index = v, type = 1, item_id = 0}
		end
	end
	local list = {
		[1] = {set_data_list[HOT_KEY.SYS_SETTING_1].index, set_data_list[HOT_KEY.SYS_SETTING_1].item_id, set_data_list[HOT_KEY.SYS_SETTING_1].type},
		[2] = {set_data_list[HOT_KEY.SYS_SETTING_2].index, set_data_list[HOT_KEY.SYS_SETTING_2].item_id, set_data_list[HOT_KEY.SYS_SETTING_2].type},
		[3] = {set_data_list[HOT_KEY.SYS_SETTING_DROPDOWN_1].index, set_data_list[HOT_KEY.SYS_SETTING_DROPDOWN_1].item_id, set_data_list[HOT_KEY.SYS_SETTING_DROPDOWN_1].type},
		[4] = {set_data_list[HOT_KEY.SYS_SETTING_AUTO_OPERATE].index, set_data_list[HOT_KEY.SYS_SETTING_AUTO_OPERATE].item_id, set_data_list[HOT_KEY.SYS_SETTING_AUTO_OPERATE].type}
	}
	SettingWGCtrl.Instance:SendChangeHotkeyReq(list)
	SettingWGCtrl.Instance:SendHotkeyInfoReq()
	return set_data_list
end

function SettingWGData:GetRewardInfo()
	return self.reward_info
end

--获得当前版本号
function SettingWGData:GetServerVersion()
	return self.server_version
end

--获得已领取的版本号
function SettingWGData:FetchRewardVersion()
	return self.fetch_reward_version
end

--获取领取奖励红点状态
function SettingWGData:GetSettingRemind()
	return self:GetRedPointState() and 1 or 0
end

--获取领取奖励红点状态
function SettingWGData:GetRedPointState()
	return self.fetch_reward_version < self.server_version
end

function SettingWGData:BindFps()
	self.fpsHandler = BindTool.Bind1(self.FpsCallBack, self)
	local fpsSampler = GameRoot.Instance:GetFPSSampler()
	if fpsSampler then
		fpsSampler.SamplePeriod = fps_sampler_time
		fpsSampler.ThresholdDeltaTime = 0.1
		fpsSampler.FPSEvent = fpsSampler.FPSEvent + self.fpsHandler
	end
end

function SettingWGData:UnbindFps()
	if self.fpsHandler ~= nil then
		local fpsSampler = GameRoot.Instance:GetFPSSampler()
		if fpsSampler then
			fpsSampler.FPSEvent = fpsSampler.FPSEvent - self.fpsHandler
			self.fpsHandler = nil
		end
	end
end

function SettingWGData:FpsCallBack(value)
	if not self.main_ui_is_open then
		return
	end

	if not Scene.Instance:GetSceneLogic():IsCanSystemAutoSetting() then
		return
	end

	GlobalEventSystem:Fire(OtherEventType.FPS_SAMPLE_RESULT, value)

	if value <= auto_setting_fps then
		self.low_fps_count = self.low_fps_count + 1
	else
		-- self:ResetAllAutoShield()
		self.low_fps_count = 0
	end

	if self.low_fps_count < lower_fps_times then
		return
	end

	-- if self.system_is_setting == true then
	-- 	return
	-- end
	-- if not self.system_is_setting and value < 25 then
		-- TipWGCtrl.Instance:ShowSystemMsg(Language.Common.DetectionFrameLow)
	-- end
	-- self:SystemAutoSetting(value)
end

function SettingWGData:MainuiOpenCreate()
	self.main_ui_is_open = true
end

function SettingWGData:GetSystemAutoSettingTypeList(item_id)
	local the_list = bit:d2b(item_id)
	local auto_setting_type_list = {}
	if the_list[32] == 1 then auto_setting_type_list[#auto_setting_type_list + 1] = SETTING_TYPE.SHIELD_OTHERS end
	if the_list[31] == 1 then auto_setting_type_list[#auto_setting_type_list + 1] = SETTING_TYPE.SHIELD_SAME_CAMP end
	if the_list[30] == 1 then auto_setting_type_list[#auto_setting_type_list + 1] = SETTING_TYPE.SKILL_EFFECT end
	if the_list[29] == 1 then auto_setting_type_list[#auto_setting_type_list + 1] = SETTING_TYPE.FLOWER_EFFECT end
	if the_list[28] == 1 then auto_setting_type_list[#auto_setting_type_list + 1] = SETTING_TYPE.CLOSE_TITLE end
	if the_list[27] == 1 then auto_setting_type_list[#auto_setting_type_list + 1] = SETTING_TYPE.CLOSE_GODDESS end
	if the_list[26] == 1 then auto_setting_type_list[#auto_setting_type_list + 1] = SETTING_TYPE.CLOSE_SHOCK_SCREEN end
	if the_list[25] == 1 then auto_setting_type_list[#auto_setting_type_list + 1] = SETTING_TYPE.SHIELD_ENEMY end
	if the_list[24] == 1 then auto_setting_type_list[#auto_setting_type_list + 1] = SETTING_TYPE.SHIELD_SPIRIT end
	return auto_setting_type_list
end

--检测是否为系统自动选择
function SettingWGData:CheckSystemAuto(system_type)
	if self.set_data_list[4].item_id == 0 then
		return false
	end
	local system_setting_type_list = self:GetSystemAutoSettingTypeList(self.set_data_list[4].item_id)
	for k,v in pairs(system_setting_type_list) do
		if v == system_type then
			return true
		end
	end
	return false
end

function SettingWGData:ResetAutoShieldRole()
	if self.set_data_list == nil or self.set_data_list[1] == nil or self.set_data_list[4] == nil then
		return
	end

	local temp_list_1 = bit:d2b(self.set_data_list[1].item_id)
	local temp_list = bit:d2b(self.set_data_list[4].item_id)
	local other_auto = SYSTEM_AUTO_OPERATE[SETTING_TYPE.SHIELD_OTHERS]
	local same_camp_auto = SYSTEM_AUTO_OPERATE[SETTING_TYPE.SHIELD_SAME_CAMP]
	if other_auto then
		temp_list[other_auto.index] = 0
		if not self.has_setting[SETTING_TYPE.SHIELD_OTHERS] then
			temp_list_1[33 - SETTING_TYPE.SHIELD_OTHERS] = 0
			self.setting_list[SETTING_TYPE.SHIELD_OTHERS] = false
			self.system_is_setting_t[SETTING_TYPE.SHIELD_OTHERS] = false
			GlobalEventSystem:Fire(self:GetGlobleType(SETTING_TYPE.SHIELD_OTHERS), self:FixBugValueOnFire(SETTING_TYPE.SHIELD_OTHERS, false))
		end
	end
	if same_camp_auto then
		temp_list[same_camp_auto.index] = 0
		if not self.has_setting[SETTING_TYPE.SHIELD_SAME_CAMP] then
			temp_list_1[33 - SETTING_TYPE.SHIELD_SAME_CAMP] = 0
			self.setting_list[SETTING_TYPE.SHIELD_SAME_CAMP] = false
			self.system_is_setting_t[SETTING_TYPE.SHIELD_SAME_CAMP] = false
			GlobalEventSystem:Fire(self:GetGlobleType(SETTING_TYPE.SHIELD_SAME_CAMP), self:FixBugValueOnFire(SETTING_TYPE.SHIELD_SAME_CAMP, false))
		end
	end
	local value_1 = bit:b2d(temp_list_1)
	local value_2 = bit:b2d(temp_list)

	local list = {
		[1] = {HOT_KEY.SYS_SETTING_1, value_1},
		[2] = {HOT_KEY.SYS_SETTING_AUTO_OPERATE, value_2}
	}
	SettingWGCtrl.Instance:SendChangeHotkeyReq(list)
	SettingWGCtrl.Instance:SendHotkeyInfoReq()
end

function SettingWGData:ResetAllAutoShield(is_ignore)
	if not is_ignore then
		if self.low_fps_count == 0 then return end
	end

	local temp_list_1 = bit:d2b(self.set_data_list[1] and self.set_data_list[1].item_id or 0)
	local temp_list_2 = bit:d2b(self.set_data_list[2] and self.set_data_list[2].item_id or 0)
	local temp_list = bit:d2b(self.set_data_list[3] and self.set_data_list[4].item_id or 0)

	-- for k,v in pairs(SYSTEM_AUTO_OPERATE) do
	-- 	temp_list[v.index] = 0
	-- 	if not self.has_setting[k] then
	-- 		if k > 16 then
	-- 			temp_list_2[33 - k + 16] = 0
	-- 		else
	-- 			temp_list_1[33 - k] = 0
	-- 		end
	-- 		self.setting_list[k] = false
	-- 		self.system_is_setting_t[k] = false
	-- 		GlobalEventSystem:Fire(self:GetGlobleType(k), self:FixBugValueOnFire(k, false))
	-- 	end
	-- end
	for k,v in pairs(SYSTEM_AUTO_OPERATE) do
		if temp_list[v.index] == 1 then
			temp_list[v.index] = 0
			if not self.has_setting[k] then
				if k > 16 then
					temp_list_2[33 - k + 16] = 0
				else
					temp_list_1[33 - k] = 0
				end
				self.setting_list[k] = false
				self.system_is_setting_t[k] = false
				GlobalEventSystem:Fire(self:GetGlobleType(k), self:FixBugValueOnFire(k, false))
			end
		end
	end

	local value_1 = bit:b2d(temp_list_1)
	local value_2 = bit:b2d(temp_list_2)
	local value_3 = bit:b2d(temp_list)

	local list = {
		[1] = {HOT_KEY.SYS_SETTING_1, value_1},
		[2] = {HOT_KEY.SYS_SETTING_2, value_2},
		[3] = {HOT_KEY.SYS_SETTING_AUTO_OPERATE, value_3}
	}
	SettingWGCtrl.Instance:SendChangeHotkeyReq(list)
	SettingWGCtrl.Instance:SendHotkeyInfoReq()
end

function SettingWGData:SystemAutoSetting(value)
	if value < 25 then
		self.system_is_setting = true
	end
	local temp_list_1 = bit:d2b(self.set_data_list[1] and self.set_data_list[1].item_id or 0)
	local temp_list_2 = bit:d2b(self.set_data_list[2] and self.set_data_list[2].item_id or 0)
	local system_change_list = {}
	for i=1,32 do
		system_change_list[i] = 0
	end
	local temp_list = bit:d2b(self.set_data_list[4] and self.set_data_list[4].item_id or 0)

	for k,v in pairs(SYSTEM_AUTO_OPERATE) do
		if not self.has_setting[k] then
			if self.setting_list[k] == false and not self.system_is_setting_t[k] and value < v.fps then
				system_change_list[v.index] = 1
				self.system_is_setting_t[k] = true
			end
			if self.setting_list[k] and self.system_is_setting_t[k] and value < v.fps then
				system_change_list[v.index] = 1
			end
		end
	end

	for i=1,16 do
		for k,v in pairs(SYSTEM_AUTO_OPERATE) do
			if i == k then
				if system_change_list[v.index] == 1 and self.setting_list[k] == false then
					temp_list_1[33 - i] = 1
					self.setting_list[i] = true
					GlobalEventSystem:Fire(self:GetGlobleType(i), self:FixBugValueOnFire(i, true))
				elseif system_change_list[v.index] == 0 and temp_list[v.index] == 1 and self.setting_list[k] then
					self.system_is_setting_t[k] = false
					temp_list_1[33 - i] = 0
					self.setting_list[i] = false
					GlobalEventSystem:Fire(self:GetGlobleType(i), self:FixBugValueOnFire(i, false))
				end
				break
			end
		end
	end
	for i=17, SettingWGData.MAX_INDEX do
		for k,v in pairs(SYSTEM_AUTO_OPERATE) do
			if i == k then
				if system_change_list[v.index] == 1 and self.setting_list[k] == false then
					temp_list_2[33 - i + 16] = 1
					self.setting_list[i] = true
					GlobalEventSystem:Fire(self:GetGlobleType(i), self:FixBugValueOnFire(i, true))
				elseif system_change_list[v.index] == 0 and temp_list[v.index] == 1 and self.setting_list[k] then
					self.system_is_setting_t[k] = false
					temp_list_2[33 - i + 16] = 0
					self.setting_list[i] = false
					GlobalEventSystem:Fire(self:GetGlobleType(i), self:FixBugValueOnFire(i, false))
				end
				break
			end
		end
	end

	local value_1 = bit:b2d(temp_list_1)
	local value_2 = bit:b2d(temp_list_2)
	local value_3 = bit:b2d(system_change_list)

	local list = {
		[1] = {HOT_KEY.SYS_SETTING_1, value_1},
		[2] = {HOT_KEY.SYS_SETTING_2, value_2},
		[3] = {HOT_KEY.SYS_SETTING_AUTO_OPERATE, value_3}
	}
	SettingWGCtrl.Instance:SendChangeHotkeyReq(list)
	SettingWGCtrl.Instance:SendHotkeyInfoReq()
end

function SettingWGData:AfterSystemAutoSetting(setting_type)
	if not self.system_is_setting then
		return
	end
	local temp_list = bit:d2b(self.set_data_list[4].item_id)
	if self:CheckSystemAuto(setting_type) then
		for k,v in pairs(SYSTEM_AUTO_OPERATE) do
			if k == setting_type then
				temp_list[v.index] = 0
			end
		end
		local value = bit:b2d(temp_list)
		SettingWGCtrl.Instance:SendChangeHotkeyReq({[1] = {HOT_KEY.SYS_SETTING_AUTO_OPERATE, value}})
	end
end

--返回品质
function SettingWGData:GetQualityName(quality_value)
	local name_list = Language.Setting.QualityName
	return name_list[quality_value] or ""
end

function SettingWGData:GetIssueTypeName(issue_type)
	local name = ""
	if issue_type == SEND_CUSTOM_TYPE.SUGGEST then
		name = Language.Common.Setting.SettingSendType[1]
	elseif issue_type == SEND_CUSTOM_TYPE.SUBMIT_BUG then
		name = Language.Common.Setting.SettingSendType[2]
	elseif issue_type == SEND_CUSTOM_TYPE.COMPLAINT then
		name = Language.Common.Setting.SettingSendType[3]
	end
	return name
end

function SettingWGData:SetFbEnterFlag(scene_type, value, scene_id)
	if scene_type == 0 then
		if BossWGData.IsWorldBossScene(scene_id) then
			self.fb_enter_flag5[32] = value and 1 or 0
		elseif BossWGData.IsDabaoBossScene(scene_id) then
			self.fb_enter_flag5[31] = value and 1 or 0
		elseif BossWGData.IsFamilyBossScene(scene_id) then
			self.fb_enter_flag5[30] = value and 1 or 0
		elseif BossWGData.IsMikuBossScene(scene_id) then
			self.fb_enter_flag5[29] = value and 1 or 0
		elseif BossWGData.IsKfBossScene(scene_id) then
			self.fb_enter_flag5[28] = value and 1 or 0
		elseif BossWGData.IsActiveBossScene(scene_id) then
			self.fb_enter_flag5[27] = value and 1 or 0
		end
	elseif scene_type < 17 then
		self.fb_enter_flag1[scene_type + 16] = value and 1 or 0
	elseif scene_type < 33 then
		self.fb_enter_flag2[scene_type] = value and 1 or 0
	elseif scene_type < 49 then
		self.fb_enter_flag3[scene_type - 16] = value and 1 or 0
	elseif scene_type < 65 then
		self.fb_enter_flag4[scene_type - 32] = value and 1 or 0
	end

	local list = {
		[1] = {HOT_KEY.FB_ENTER_FLAG1, bit:b2d(self.fb_enter_flag1)},
		[2] = {HOT_KEY.FB_ENTER_FLAG2, bit:b2d(self.fb_enter_flag2)},
		[3] = {HOT_KEY.FB_ENTER_FLAG3, bit:b2d(self.fb_enter_flag3)},
		[4] = {HOT_KEY.FB_ENTER_FLAG4, bit:b2d(self.fb_enter_flag4)},
		[5] = {HOT_KEY.FB_ENTER_FLAG5, bit:b2d(self.fb_enter_flag5)}
	}
	SettingWGCtrl.Instance:SendChangeHotkeyReq(list)
end

-- function SettingWGData:HasEnterFb(scene_type, scene_id)
-- 	if next(self.fb_enter_flag1) == nil  then return true end
-- 	if scene_type == 0 then
-- 		if BossWGData.IsWorldBossScene(scene_id) then
-- 			return self.fb_enter_flag5[32] == 1
-- 		elseif BossWGData.IsDabaoBossScene(scene_id) then
-- 			return self.fb_enter_flag5[31] == 1
-- 		elseif BossWGData.IsFamilyBossScene(scene_id) then
-- 			return self.fb_enter_flag5[30] == 1
-- 		elseif BossWGData.IsMikuBossScene(scene_id) then
-- 			return self.fb_enter_flag5[29] == 1
-- 		elseif BossWGData.IsKfBossScene(scene_id) then
-- 			return self.fb_enter_flag5[28] == 1
-- 		elseif BossWGData.IsActiveBossScene(scene_id) then
-- 			return self.fb_enter_flag5[27] == 1
-- 		elseif AncientRelicsData.IsAncientRelics(scene_id) then
-- 			return self.fb_enter_flag5[26] == 1
-- 		end
-- 	elseif scene_type < 17 then
-- 		return self.fb_enter_flag1[scene_type + 16] == 1
-- 	elseif scene_type < 33 then
-- 		return self.fb_enter_flag2[scene_type] == 1
-- 	elseif scene_type < 49 then
-- 		return self.fb_enter_flag3[scene_type - 16] == 1
-- 	elseif scene_type < 65 then
-- 		return self.fb_enter_flag4[scene_type - 32] == 1
-- 	end
-- 	return true
-- end

-- function SettingWGData:SetMarryEquipIndex(marry_guaji_index)
-- 	self.marry_guaji_index = marry_guaji_index
-- 	SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.MARRY_EQUIP, marry_guaji_index)
-- end

function SettingWGData:GetMarryEquipIndex()
	return self.marry_guaji_index
end

function SettingWGData:SetScreenBright(value)
	self.screen_bright = value
end

function SettingWGData:GetScreenBright()
	return self.screen_bright
end

function SettingWGData:SetFightToggleState(value)
	self.fight_toggle_state = value
end

function SettingWGData:GetFightToggleState()
	return self.fight_toggle_state
end

function SettingWGData:SetBugFixRecordValue(setting_type, setting_value)
	if true == setting_value then setting_value = 1 end
	if false == setting_value then setting_value = 0 end

	self.bugfix_record_t[setting_type] = setting_value
	self.setting_flag_t[setting_type] = setting_value
end

function SettingWGData:GetBugFixRecordValue(setting_type)
	if nil ~= self.bugfix_record_t[setting_type] then
		return self.bugfix_record_t[setting_type]
	end

	return 1
end

function SettingWGData:FixBugValueOnFire(setting_type, value)
	if nil ~= self.bugfix_record_t[setting_type] then
		return 1 == self:GetBugFixRecordValue(setting_type)
	end
	return value
end

function SettingWGData:FixBugOnSend(index, value)
	if index == HOT_KEY.SYS_SETTING_1 then
		local list = bit:d2b(value)
		for i=1,16 do
			if nil ~= self.bugfix_record_t[i] then
				list[33 - i] = self:GetBugFixRecordValue(i)
			end
		end
		value = bit:b2d(list)
	end

	if index == HOT_KEY.SYS_SETTING_2 then
		local list = bit:d2b(value)
		for i=17, SettingWGData.MAX_INDEX do
			if nil ~= self.bugfix_record_t[i] then
				list[33 - i + 16] = self:GetBugFixRecordValue(i)
			end
		end
		value = bit:b2d(list)
	end

	if index == HOT_KEY.SYS_SETTING_DROPDOWN_1 then
		local list = bit:d2b(value)
		for i=1,16 do
			if nil ~= self.bugfix_record_t[100 + i] then
				list[33 - i] = self:GetBugFixRecordValue(100 + i)
			end
		end
		value = bit:b2d(list)
	end

	return index, value
end

function SettingWGData:IsFixSettingBugType(setting_type)
	for _, v2 in pairs(FixBugSettting) do
		if v2 == setting_type then
			return true
		end
	end

	return false
end

function SettingWGData:FixBugOnFirstRecv(set_data_list)
	if self.is_bugfixed_on_first_recv then
		return
	end

	self.is_bugfixed_on_first_recv = true

	local level = RoleWGData.Instance:GetRoleLevel() or 0
	local key = "set_setting_defult_flag"
	local set_flag = PlayerPrefsUtil.GetInt(key)
	 -- 新玩家
	if level <= 1 or set_flag ~= 1 then
		-- self:SetSettingData1(SETTING_TYPE.GUAJI_PICKUP_GREEN, true)
		-- self:SetSettingData1(SETTING_TYPE.GUAJI_PICKUP_BLUE, true)
		-- self:SetSettingData1(SETTING_TYPE.GUAJI_PICKUP_PURPLE, true)
		-- self:SetSettingData1(SETTING_TYPE.GUAJI_PICKUP_ORANGE, true)
		-- self:SetSettingData1(SETTING_TYPE.GUAJI_PICKUP_COIN, true)
		-- self:SetSettingData1(SETTING_TYPE.GUAJI_PICKUP_OTHER, true)
		self:SetSettingData1(SETTING_TYPE.AUTO_JINGJIE_BIANSHEN, true)
		self:SetSettingData1(SETTING_TYPE.AUTO_LONGZHU_SKILL, true)
		self:SetSettingData1(SETTING_TYPE.FIGHT_MOUNT_SKILL_CG_PLAY, true)
		self:SetSettingData1(SETTING_TYPE.AUTO_BEASTS_SKILL, true)
		self:HotKeyToSetting()
		PlayerPrefsUtil.SetInt(key, 1)
	end
end

function SettingWGData:SetNeedLuckView(value)
	self.need_luck_view = value
end

function SettingWGData:GetNeedLuckView()
	return self.need_luck_view
end

function SettingWGData:GetRecommendQuality()
	return self.recommend_quality
end

-- 是否屏蔽其他玩家信息
function SettingWGData:IsShieldOtherRole(scene_id)
	if CgManager.Instance:IsCgIng() then
		return true
	end
	return false
end

function SettingWGData:GetSupplyData()
	return self.hp_percent, self.mp_percent
end

function SettingWGData:SetDataByIndex(index, item_id)
	if nil == self.user_default[index] then
		self.user_default[index] = {}
	end
	self.user_default[index].index = index
	self.user_default[index].type = 1
	self.user_default[index].item_id = item_id
	if index == HOT_KEY.SYS_SETTING_1 then
		self.setting_flag_t = bit:d2b(item_id)
	elseif index == HOT_KEY.SYS_SETTING_2 then
		self.guaji_flag_t = bit:d2b(item_id)
	end
end

function SettingWGData:GetSettingDataTable(index)
	return self.setting_list[index]
end

function SettingWGData:GetGuajiDataTable(index)
	return self.setting_list[index]
end

function SettingWGData:CalcPickBreakPercent()
	local data = self:GetDataByIndex(HOT_KEY.AUTO_PICK_BREAK) or 0
	self.pickup_color = data % 10
	self.break_color = math.floor((data % 100) / 10)
	if self.break_color == 0 then
		local num = math.floor((data % 1000) / 100)
		if num > 0 then self.break_color = 10 end
	end

	-- self.break_color = math.floor(data / 10000) % 100
	-- self.card_color = math.floor((data % 1000) / 100)
	self.card_color = 1
	self.treasure_color = math.floor((data % 10000) / 1000)
end

function SettingWGData:GetDataByIndex(index)
	local info = self.user_default[index]
	if nil == info then
		return 0, 0
	end

	return info.item_id, info.type
end

function SettingWGData:SetSettingData(user_default)
	self.user_default = user_default

	self:CalcSupplyPercent()
	self:CalcPickBreakPercent()
end

function SettingWGData:CalcSupplyPercent()
	local data = self:GetDataByIndex(HOT_KEY.SUPPLY) or 0
	self.hp_percent = bit:_rshift(data, 8)
	self.mp_percent = bit:_and(data, 0xff)
end

function SettingWGData:GetPickBreaKData()
	return self.pickup_color, self.break_color, self.card_color, self.treasure_color
end

function SettingWGData:SetPickBreakData(pick_color, break_color, card_color, treasure_color)
	local data = pick_color + break_color * 10 + treasure_color * 1000
	self:SetDataByIndex(HOT_KEY.AUTO_PICK_BREAK, data)
end

function SettingWGData:SetSupplyData(hp_percent, mp_percent)
	local data = bit:_lshift(hp_percent, 8) + mp_percent
	self:SetDataByIndex(HOT_KEY.SUPPLY, data)
end

function SettingWGData:SetTlksClickTime()
	self.tlks_time = Status.NowTime + 60
end

function SettingWGData:GetTlksClickTime()
	return self.tlks_time or 0
end
--设置界面的打开来源(1标识来自挂机卡时间不足查看)
function SettingWGData:SetViewOpenForm(open_formm)
	self.open_formm = open_formm
end

function SettingWGData:GetViewOpenForm()
	return self.open_formm
end

------------------------------------------------------------------------------------------------

function SettingWGData:SetFSFKClickTime()
	self.fsfk_time = Status.NowTime + 30
end

function SettingWGData:GetFSFKClickTime()
	return self.fsfk_time or 0
end

function SettingWGData:SetNoticData(data)
	self.notic_data = data
end

function SettingWGData:GetNoticData()
	return self.notic_data or {}
end

function SettingWGData:GetNoticMainHasRemind(data)
	if not data or data.id == nil then
		return false
	end

	local main_notice_flag = PlayerPrefsUtil.GetInt("LoginMainNoticeHasRead_" .. data.id)
	if main_notice_flag < 1 then
		return true
	end

	return false
end

function SettingWGData:GetAllNoticHasRemind()
	local data = self:GetNoticData()
	if not data or not next(data) then
		return false
	end

	for i,v in ipairs(data) do
		local flag = self:GetNoticMainHasRemind(v)
		if flag then
			return true
		end
	end

	return false
end

function SettingWGData:OnSCRoleFeedbackInfo(protocol)
	self.feed_back_flag = protocol.reward_flag
	--self:ShowMainUiButton()
end

function SettingWGData:FunOpenReShow(func_name)
	-- if func_name == FunName.FeedBackView then
	-- 	self:ShowMainUiButton()
	-- end
end

function SettingWGData:ShowMainUiButton()
	-- if FunOpen.Instance:GetFunIsOpened(FunName.FeedBackView) then
	-- 	local click_back = function ()
	-- 		ViewManager.Instance:Open(GuideModuleName.FeedBackView)
	-- 	end
	-- 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FEED_BACK, 1,click_back)
	-- end
end

function SettingWGData:GetFeedBackRewared()
	if not self.feed_back_cfg then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
		self.feed_back_cfg = other_cfg.item or {}
	end
	return self.feed_back_cfg
end

function SettingWGData:GetFeedBackGetFlag()
	if self.feed_back_flag then
		return self.feed_back_flag == 0
	end
	return true
end

function SettingWGData:IsHideMainRoleVipLv()
	return self:GetSettingData(SETTING_TYPE.HIDE_GUIZHULEVEL)
end

function SettingWGData:IsHideRoleVipLv(flag)
	return flag == 1
end


--==============================================
--==============================================
-- 检查推荐画质质量等级
-- 返回值: 0=极致, 1=高清, 2=流畅, 3=省电
function SettingWGData:CheckRecommendQuality()
	-- 1. 检查特殊环境
	local special_quality = self:CheckSpecialEnvironment()
	if special_quality then
		return special_quality
	end
	
	-- 2. 根据平台检查
	local platform = UnityEngine.Application.platform
	if platform == UnityEngine.RuntimePlatform.IPhonePlayer then
		return self:CheckiOSQuality()
	elseif platform == UnityEngine.RuntimePlatform.Android then
		return self:CheckAndroidQuality()
	else
		return self:CheckOtherPlatformQuality()
	end
end

-- 检查特殊环境（模拟器、编辑器、特殊设备）
function SettingWGData:CheckSpecialEnvironment()
	-- 模拟器和编辑器使用最高画质
	if DeviceTool.IsEmulator() or IS_LOCLA_WINDOWS_DEBUG_EXE or UnityEngine.Application.isEditor then
		return QUALITY_LEVEL.EXTREME
	end
	
	local sysInfo = UnityEngine.SystemInfo
	
	-- 检查特殊低端设备
	for _, device_name in ipairs(LOW_QUALITY_DEVICE) do
		if device_name == sysInfo.deviceName then
			print_log("[CheckRecommendQuality] Special low-end device detected: " .. device_name)
			return QUALITY_LEVEL.LOW
		end
	end
	
	-- 检查特殊低端显卡
	for _, graphics_id in ipairs(LOW_QUALITY_GRAPHICS) do
		if graphics_id == sysInfo.graphicsDeviceID then
			print_log("[CheckRecommendQuality] Special low-end graphics detected: " .. graphics_id)
			return QUALITY_LEVEL.LOW
		end
	end
	
	return nil
end

-- 检查iOS设备画质
function SettingWGData:CheckiOSQuality()
	-- devices_info.platform 请参照 https://www.innerfence.com/howto/apple-ios-devices-dates-versions-instruction-sets
	local devices_info = cjson.decode(DeviceInfo.GetDeviceInfoJsonData())
	local platform_info = self:ParseiOSPlatformInfo(devices_info.platform)
	
	if not platform_info then
		return QUALITY_LEVEL.LOW
	end
	
	local device_type = platform_info.device_type
	local first_id = platform_info.first_id
	local second_id = platform_info.second_id
	
	if device_type == "iPhone" then
		return self:GetiPhoneQuality(first_id, second_id)
	elseif device_type == "iPad" then
		return self:GetiPadQuality(first_id)
	elseif device_type == "iPod" then
		return QUALITY_LEVEL.LOW
	end
	
	return QUALITY_LEVEL.LOW
end

-- 解析iOS设备平台信息
function SettingWGData:ParseiOSPlatformInfo(platform_str)
	if not platform_str then
		return nil
	end
	
	local parts = string.split(platform_str, ",")
	if not parts or #parts < 2 then
		return nil
	end
	
	local device_type = nil
	local num_start_index = 0
	
	if string.match(parts[1], "iPhone") then
		device_type = "iPhone"
		num_start_index = 7
	elseif string.match(parts[1], "iPad") then
		device_type = "iPad"
		num_start_index = 5
	elseif string.match(parts[1], "iPod") then
		device_type = "iPod"
		num_start_index = 5
	else
		return nil
	end
	
	local first_id = tonumber(string.sub(parts[1], num_start_index, -1))
	local second_id = tonumber(parts[2])
	if not first_id or not second_id then
		return nil
	end
	
	return {
		device_type = device_type,
		first_id = first_id,
		second_id = second_id
	}
end

-- 获取iPhone画质等级
function SettingWGData:GetiPhoneQuality(first_id, second_id)
	local thresholds = HARDWARE_THRESHOLDS.IOS
	
	-- iPhone8 Plus 以下为低端机 (iPhone10,3=iPhone X, iPhone10,6=iPhone X Plus除外)
	if first_id < thresholds.IPHONE_LOW_END or 
	   (first_id == thresholds.IPHONE_LOW_END and second_id ~= 3 and second_id ~= 6) then
		return QUALITY_LEVEL.LOW
	end
	
	-- iPhone11系列为中端机
	if first_id < thresholds.IPHONE_MID_END then
		return QUALITY_LEVEL.MEDIUM
	end
	
	-- iPhone15以上为旗舰机
	if first_id >= thresholds.IPHONE_HIGH_END then
		return QUALITY_LEVEL.EXTREME
	end
	
	-- iPhone12-14为高端机
	return QUALITY_LEVEL.HIGH
end

-- 获取iPad画质等级
function SettingWGData:GetiPadQuality(first_id)
	local thresholds = HARDWARE_THRESHOLDS.IOS
	
	if first_id >= thresholds.IPAD_MID_END then
		return QUALITY_LEVEL.HIGH
	elseif first_id >= thresholds.IPAD_LOW_END then
		return QUALITY_LEVEL.MEDIUM
	else
		return QUALITY_LEVEL.LOW
	end
end

-- 检查Android设备画质
function SettingWGData:CheckAndroidQuality()
	local devices_info = cjson.decode(DeviceInfo.GetDeviceInfoJsonData())
	local android_info = self:ParseAndroidInfo(devices_info)
	
	-- 基础硬件检查
	if self:IsAndroidLowEndDevice(android_info) then
		return QUALITY_LEVEL.LOW
	end
	
	-- 特殊品牌处理
	if self:IsXiaomiDevice(android_info.brand) then
		return self:GetXiaomiQuality(android_info)
	end
	
	-- 有些“老板机”型号直接使用最高推荐画质
	if HIGHEST_QUALITY_PHONE_MODEL[android_info.model] then
		return QUALITY_LEVEL.EXTREME
	end
	
	-- 华为特殊处理
	if android_info.brand == "HUAWEI" then
		return QUALITY_LEVEL.HIGH
	end
	
	-- 通用Android设备判断
	return self:GetGeneralAndroidQuality(android_info)
end

-- 解析Android设备信息
function SettingWGData:ParseAndroidInfo(devices_info)
	--解析Android版本号（有些安卓版本带.  比如7.1.2）
	local version_str = devices_info.android_version or "0"
	local android_version = 0
	if string.find(version_str, "%.") then
		android_version = tonumber(string.split(version_str, "%.")[1]) or 0
	else
		android_version = tonumber(version_str) or 0
	end
	
	return {
		version = android_version,
		cpu_hz = tonumber(devices_info.cpu_maxHz) or -1,
		memory = tonumber(devices_info.total_memory) or 0,
		cores = tonumber(devices_info.cpuCores) or 0,
		brand = tostring(devices_info.android_brand or ""),
		model = tostring(devices_info.android_model or "")
	}
end

-- 检查是否为Android低端设备
function SettingWGData:IsAndroidLowEndDevice(android_info)
	local thresholds = HARDWARE_THRESHOLDS.ANDROID
	
	return android_info.version <= thresholds.MIN_VERSION or
		   android_info.memory < thresholds.MIN_MEMORY or
		   android_info.cores <= thresholds.MIN_CORES or
		   (android_info.cpu_hz ~= -1 and android_info.cpu_hz <= thresholds.LOW_CPU_HZ)
end

-- 检查是否为小米/红米设备
function SettingWGData:IsXiaomiDevice(brand)
	return brand == "Xiaomi" or brand == "Redmi"
end

-- 获取小米设备画质等级
function SettingWGData:GetXiaomiQuality(android_info)
	local android_thresholds = HARDWARE_THRESHOLDS.ANDROID
	local xiaomi_thresholds = HARDWARE_THRESHOLDS.XIAOMI
	
	-- 旗舰机判断
	if android_info.version >= android_thresholds.FLAGSHIP_VERSION and 
	   android_info.cpu_hz ~= -1 and android_info.cpu_hz >= xiaomi_thresholds.FLAGSHIP_CPU_HZ then
		return QUALITY_LEVEL.EXTREME
	end
	
	-- 低端机判断
	if android_info.version <= android_thresholds.MIN_VERSION or 
	   (android_info.cpu_hz ~= -1 and android_info.cpu_hz <= xiaomi_thresholds.LOW_CPU_HZ) then
		return QUALITY_LEVEL.LOW
	end
	
	-- 中高端机判断
	if android_info.cpu_hz ~= -1 and android_info.cpu_hz >= xiaomi_thresholds.HIGH_CPU_HZ then
		return android_info.version >= android_thresholds.HIGH_VERSION and QUALITY_LEVEL.HIGH or QUALITY_LEVEL.MEDIUM
	end
	
	-- 默认中端机
	return QUALITY_LEVEL.MEDIUM
end

-- 获取通用Android设备画质等级
function SettingWGData:GetGeneralAndroidQuality(android_info)
	local thresholds = HARDWARE_THRESHOLDS.ANDROID
	
	-- 旗舰机判断
	if android_info.version >= thresholds.FLAGSHIP_VERSION and
	   android_info.memory >= thresholds.HIGH_MEMORY and
	   android_info.cores >= thresholds.MID_CORES and
	   android_info.cpu_hz ~= -1 and android_info.cpu_hz >= thresholds.HIGH_CPU_HZ then
		return QUALITY_LEVEL.EXTREME
	end
	
	-- 高端机判断
	if android_info.version >= thresholds.HIGH_VERSION and
	   android_info.memory >= thresholds.MID_MEMORY and
	   android_info.cores >= thresholds.MID_CORES and
	   android_info.cpu_hz ~= -1 and android_info.cpu_hz >= thresholds.MID_CPU_HZ then
		return QUALITY_LEVEL.HIGH
	end
	
	-- 中端机或低端机
	return android_info.version >= thresholds.MID_VERSION and QUALITY_LEVEL.MEDIUM or QUALITY_LEVEL.LOW
end

-- 检查其他平台画质
function SettingWGData:CheckOtherPlatformQuality()
	local sysInfo = UnityEngine.SystemInfo
	local thresholds = HARDWARE_THRESHOLDS.OTHER
	
	-- 不支持基础功能直接低画质
	if not sysInfo.supportsShadows or not sysInfo.graphicsMultiThreaded then
		return QUALITY_LEVEL.LOW
	end
	
	-- 高配置判断
	if self:MeetsHighEndRequirements(sysInfo, thresholds) then
		return QUALITY_LEVEL.EXTREME
	end
	
	-- 中配置判断
	if self:MeetsMidEndRequirements(sysInfo, thresholds) then
		return QUALITY_LEVEL.HIGH
	end
	
	-- 低配置判断
	if self:MeetsLowEndRequirements(sysInfo, thresholds) then
		return QUALITY_LEVEL.MEDIUM
	end
	
	-- 默认超低配
	return QUALITY_LEVEL.LOW
end

-- 检查是否满足高端配置要求
function SettingWGData:MeetsHighEndRequirements(sysInfo, thresholds)
	return sysInfo.supportedRenderTargetCount >= thresholds.HIGH_RENDER_TARGETS and
		   sysInfo.systemMemorySize >= thresholds.HIGH_SYSTEM_MEMORY and
		   sysInfo.graphicsMemorySize >= thresholds.HIGH_GRAPHICS_MEMORY and
		   sysInfo.processorCount >= thresholds.HIGH_PROCESSOR_COUNT and
		   sysInfo.processorFrequency >= thresholds.HIGH_PROCESSOR_FREQ
end

-- 检查是否满足中端配置要求
function SettingWGData:MeetsMidEndRequirements(sysInfo, thresholds)
	return sysInfo.supportedRenderTargetCount >= thresholds.MIN_RENDER_TARGETS and
		   sysInfo.systemMemorySize >= thresholds.MID_SYSTEM_MEMORY and
		   sysInfo.graphicsMemorySize >= thresholds.MID_GRAPHICS_MEMORY and
		   sysInfo.processorCount >= thresholds.MID_PROCESSOR_COUNT and
		   sysInfo.processorFrequency >= thresholds.MID_PROCESSOR_FREQ
end

-- 检查是否满足低端配置要求
function SettingWGData:MeetsLowEndRequirements(sysInfo, thresholds)
	return sysInfo.supportedRenderTargetCount >= thresholds.MIN_RENDER_TARGETS and
		   sysInfo.systemMemorySize >= thresholds.MIN_SYSTEM_MEMORY and
		   sysInfo.graphicsMemorySize >= thresholds.MIN_GRAPHICS_MEMORY and
		   sysInfo.processorCount >= thresholds.MIN_PROCESSOR_COUNT and
		   sysInfo.processorFrequency >= thresholds.MIN_PROCESSOR_FREQ
end
