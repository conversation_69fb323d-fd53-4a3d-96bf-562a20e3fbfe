function SwornView:InitSwornImporintView()
	self.node_list.imprint_promot.text.text = Language.Sworn.ImprintPromot

    if not self.imprint_cost_item then
        self.imprint_cost_item = ItemCell.New(self.node_list.cost_item_pos)
    end

    self.head_list = {}
    for i = 1, 5 do
        if not self.head_list[i] then
            self.head_list[i] = BaseHeadCell.New(self.node_list["head_list_pos" .. i])
            XUI.AddClickEventListener(self.node_list["image_btn" .. i], BindTool.Bind(self.OnClickJumpBtn, self))
        end
    end

	if nil == self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
        local model_data = SwornWGData.Instance:GetSwornOtherCfg()

	    if not IsEmptyTable(model_data) then
		    local display_data = {}
		    display_data.should_ani = true
		    display_data.bundle_name = model_data.model_bundle_name
		    display_data.asset_name = model_data.model_asset_name
		    display_data.render_type = model_data.model_show_type - 1
		    self.model_display:SetData(display_data)
	    end
    end

    self.do_sworm_one_key_ani = false

    XUI.AddClickEventListener(self.node_list.btn_active, BindTool.Bind(self.OnClickActiveBtn, self))
end

function SwornView:ShowSwornImporintView()	
end

function SwornView:SwornImporintViewReleaseCallBack()
    if self.imprint_cost_item then
        self.imprint_cost_item:DeleteMe()
        self.imprint_cost_item = nil
    end

    if self.yoyo_tween then
        self.yoyo_tween:Kill()
        self.yoyo_tween = nil
    end

    if not IsEmptyTable(self.head_list) then
        for k, v in pairs(self.head_list) do
            v:DeleteMe()
        end
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    self.do_sworm_one_key_ani = nil
end

function SwornView:FlushSwornImporintView()
    local jin_lan_level = SwornWGData.Instance:GetJinLanLevel()
    local cap = SwornWGData.Instance:GetJinLanCap()
    local active = jin_lan_level > 0
    local jin_lan_level_cfg = SwornWGData.Instance:GetJinLanLevelCfg()
    local is_max_level = jin_lan_level_cfg[#jin_lan_level_cfg].level == jin_lan_level

    self.node_list.imprint_level.text.text = string.format(Language.Common.Order, NumberToChinaNumber(jin_lan_level))
    self.node_list.manji_flag:SetActive(active and is_max_level)
    self.node_list.shengji_panel:SetActive(not is_max_level)
    self.node_list.cosu_desc_title.text.text = active and Language.Sworn.UpStarStuff or Language.Sworn.ActiveStuff
    self.node_list.cap_value.text.text = cap

    local attr_cfg = SwornWGData.Instance:GetCurrentJinLanAttr()
    if not IsEmptyTable(attr_cfg) then
        for i = 1, 5 do
            self.node_list["attr" .. i].text.text = attr_cfg[i].attr_name .. " " .. ToColorStr(attr_cfg[i].value_str, COLOR3B.DEFAULT_NUM)
        end
    end

    if not active then
        self.node_list.btn_active_text.text.text = Language.Sworn.Active
    else
        self.node_list.btn_active_text.text.text = self.do_sworm_one_key_ani and Language.Sworn.ShengXIngStop or Language.Sworn.ShengXing
    end

    if self.do_sworm_one_key_ani then
        self:SwornImporintStartUpGradeAni()
    end

    local cost_item_id = jin_lan_level_cfg[jin_lan_level].cost_item_id
    local cost_item_num = jin_lan_level_cfg[jin_lan_level].cost_item_num
    local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    local enough = has_num >= cost_item_num
    self.node_list.active_remind:SetActive(active and not is_max_level and enough)

    self.imprint_cost_item:SetFlushCallBack(function ()
        local right_text = ToColorStr(has_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
        self.imprint_cost_item:SetRightBottomColorText(right_text)
        self.imprint_cost_item:SetRightBottomTextVisible(true)
    end)

    self.imprint_cost_item:SetData({item_id = cost_item_id, num = cost_item_num, is_bind = 0})

    local bundel, asset = ResPath.GetSkillIconById(jin_lan_level_cfg[jin_lan_level].icon)
    self.node_list.skill_icon.image:LoadSprite(bundel, asset, function()
        self.node_list.skill_icon.image:SetNativeSize()
    end)

    self.node_list.skill_active_title.text.text = not active and Language.Sworn.SkillActiveTitle or Language.Sworn.SkillCurrentTitle

    local skill_desc = jin_lan_level_cfg[jin_lan_level].desc
    -- self.node_list.skill_next_level_title:SetActive(active and not is_max_level)

    if active and not is_max_level then
        --self.node_list.skill_next_level_title.text.text = string.format(Language.Sworn.SkillNextLevelTitle, NumberToChinaNumber(jin_lan_level + 1))
        --self.node_list.skill_next_level_desc.text.text = jin_lan_level_cfg[jin_lan_level + 1].desc
        skill_desc = skill_desc .. string.format(Language.Sworn.SkillNextLevelTitle, NumberToChinaNumber(jin_lan_level + 1)) .. jin_lan_level_cfg[jin_lan_level + 1].desc
    end

    self.node_list.skill_active_desc.text.text = skill_desc

    self:FlushImporintHeadCell()
    self:PalyYoyoTween()
end

function SwornView:PalyYoyoTween()
    if self.yoyo_tween == nil then
        local tween_root = self.node_list.tween_root.rect
        self.yoyo_tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 20, 1):SetLoops(-1, DG.Tweening.LoopType.Yoyo):SetEase(DG.Tweening.Ease.Linear)
    end
end

function SwornView:FlushImporintHeadCell()
    local num, data_list = SwornWGData.Instance:GetMyTeamMenberInfo()

    local addtion = 0
    local cfg = SwornWGData.Instance:GetJinLanAddtionCfg()[num] or {}
    if not IsEmptyTable(cfg) then
        addtion = cfg.addition / 100
    end

    self.node_list.resonance.text.text = string.format(Language.Sworn.JinLanAddition, addtion)

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            local active = v.uid > 0
            self.node_list["head_list_pos" .. k]:SetActive(active)
            self.node_list["image_btn" .. k]:SetActive(not active)
            if active then
                local head_data = {fashion_photoframe = v.shizhuang_photoframe}
                head_data.role_id = v.uid
                head_data.prof = v.prof
                head_data.sex = v.sex
                head_data.is_show_main = true
    
                self.head_list[k]:SetImgBg(v.shizhuang_photoframe > 0)
                self.head_list[k]:SetData(head_data)
            end  
        end 
    end
end

function SwornView:OnClickActiveBtn()
    -- local current_level_cfg = SwornWGData.Instance:GetCurrentJinLanLevelCfg()
    local jin_lan_level = SwornWGData.Instance:GetJinLanLevel()

    -- if IsEmptyTable(current_level_cfg) then
    --     return
    -- end

    -- local cost_item_id = current_level_cfg.cost_item_id
    -- local cost_item_num = current_level_cfg.cost_item_num
    -- local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

    -- if has_num >= cost_item_num then
    --     local effect_type

	--     if jin_lan_level == 0 then
    --         effect_type = UIEffectName.s_jihuo
    --     else
    --         effect_type = UIEffectName.s_shengjie
    --     end

    --     TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
	--     is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["imporint_effect_root"]})
    --     SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_JINLAN_UP)
    -- else
    --     local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
    --     local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    --     TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Sworn.NoEnough, name))
    --     TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
    -- end

    -- 一键升级与激活
    if jin_lan_level == 0 then
        if self:IsSwornImporintCanUp(true) then
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo,
            is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["imporint_effect_root"]})

            SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_JINLAN_UP)
        end
    else
        if self:IsSwornImporintCanUp(true) then
            if self.do_sworm_one_key_ani then
                self:SwornImporintOneKeyStop()
            else
                self.do_sworm_one_key_ani = true
                TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengjie,
                is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["imporint_effect_root"]})
                SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_JINLAN_UP)
                self.node_list.btn_active_text.text.text = Language.Sworn.ShengXIngStop
            end
        end
    end
end

function SwornView:OnClickJumpBtn()
    self:ChangeToIndex(TabIndex.sworn_start)
end

--------------------------------一键升级--------------------------------
function SwornView:SwornImporintStartUpGradeAni()
    if self:IsSwornImporintCanUp(false) then
        if self.do_sworm_one_key_ani then
            ReDelayCall(self, function()
                TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengjie,
                is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["imporint_effect_root"]})
                    SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_JINLAN_UP)
             end, 0.5, "sworn_imporint_start_up")
        end
    else
        self:SwornImporintOneKeyStop()
    end
end

function SwornView:IsSwornImporintCanUp(need_show_tip)
    local current_level_cfg = SwornWGData.Instance:GetCurrentJinLanLevelCfg()
    local jin_lan_level = SwornWGData.Instance:GetJinLanLevel()
    local jin_lan_level_cfg = SwornWGData.Instance:GetJinLanLevelCfg()

    if jin_lan_level_cfg then
        if jin_lan_level_cfg[#jin_lan_level_cfg].level == jin_lan_level then
            return false
        end
    end

    if IsEmptyTable(current_level_cfg) then
        return false
    end

    local cost_item_id = current_level_cfg.cost_item_id
    local cost_item_num = current_level_cfg.cost_item_num
    local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

    if has_num >= cost_item_num then
        return true
    else
        if need_show_tip then
            TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
        end

        return false
    end
end

function SwornView:SwornImporintOneKeyStop()
    if self.node_list.btn_active_text then
        self.node_list.btn_active_text.text.text = Language.Sworn.ShengXing
    end

    self.do_sworm_one_key_ani = false
end