NewAppearanceDyeView = NewAppearanceDyeView  or BaseClass(SafeBaseView)
local ColorSelector = typeof(ColorSelector)
local SubColorSelector = typeof(SubColorSelector)

local CUR_SHOW_TYPE = 
{
    SCHEME_PAGE = 0,
    SCHEME_EDIT = 1,
}

local CUR_SHOW_COLOR_TYPE = 
{
    SCHEME_COLOR_HSV = 0,
    SCHEME_COLOR_HEX = 1,
}

local CUR_DYE_COLOR_TYPE = 
{
    LITHE_DYE_COLOR_TYPE = 0,
    FEEL_FREE_COLOR_TYPE = 1,
}

local CUR_DYE_UI_SCENE_INDEX = 
{
    DYE_UI_SCENE_INDEX_1 = 12,
    DYE_UI_SCENE_INDEX_2 = 13,
    DYE_UI_SCENE_INDEX_3 = 14,
    DYE_UI_SCENE_INDEX_4 = 15,
}

local CUR_DYE_MAP_INDEX = 
{
    [1] = CUR_DYE_UI_SCENE_INDEX.DYE_UI_SCENE_INDEX_1,
    [2] = CUR_DYE_UI_SCENE_INDEX.DYE_UI_SCENE_INDEX_2,
    [3] = CUR_DYE_UI_SCENE_INDEX.DYE_UI_SCENE_INDEX_3,
    [4] = CUR_DYE_UI_SCENE_INDEX.DYE_UI_SCENE_INDEX_4,
}

local CUR_FOCUS_SCALE = Vector3(1, 1, 1)
local FOCUS_PART_FROM_POS = 
{
    [0] = Vector3(0, 1.5, -20),
    [1] = Vector3(0, 1.5, -20),
    [2] = Vector3(0, 1.5, -20),
    [3] = Vector3(0, 1.5, -20),
    [4] = Vector3(0, 1.5, -20),
}

local FOCUS_PART_TO_POS = 
{
    [0] = Vector3(0, 2.3, -7),
    [1] = Vector3(0, 2.48, -5),
    [2] = Vector3(0, 2.4, -7),
    [3] = Vector3(0, 1.6, -7),
    [4] = Vector3(0, 0.55, -7),
}

function NewAppearanceDyeView:__init()
	self:SetMaskBg(false, true)
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	-- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/new_appearance_dye_prefab", "layout_fashion_dye")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
    self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.WAIGUAN_DYE})
end

function NewAppearanceDyeView:SetNewAppearancePartIndex(index, part_type)
    self.new_appearancce_index = index
    self.new_appearancce_part_type = part_type
end

function NewAppearanceDyeView:SetOtherShowData(other_show_data)
    self.other_show_data = other_show_data

    if self.other_show_data ~= nil then
        -- 如果使用其他人的方案则直接到编辑界面和直接使用方案一
        local cfg = NewAppearanceDyeWGData.Instance:GetConsumeCfgBySeq(self.other_show_data.seq)
        if cfg ~= nil then
            self.new_appearancce_index = cfg.index
            self.new_appearancce_part_type = cfg.part_type
        end
    end
end

function NewAppearanceDyeView:LoadCallBack()
	-- 模块名称
	self.node_list.title_view_name.text.text = Language.NewAppearanceDye.TitleName

    -- 模型展示
	if not self.show_model then
		self.show_model = RoleModel.New()
		self.show_model:SetUISceneModel(self.node_list["ph_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.show_model, 0)
	end

    if not self.left_scheme_list then
		self.left_scheme_list = AsyncListView.New(DyeSchemeRender, self.node_list.left_scheme_list)
		self.left_scheme_list:SetSelectCallBack(BindTool.Bind(self.SelectSchemeCallBack, self))
        -- self.left_scheme_list:SetLimitSelectFunc(BindTool.Bind(self.SelectSchemeLimit, self))
	end

    if not self.left_edit_part_list then
		self.left_edit_part_list = AsyncListView.New(DyeSchemePartRender, self.node_list.left_edit_part_list)
        self.left_edit_part_list:SetStartZeroIndex(true)
		self.left_edit_part_list:SetSelectCallBack(BindTool.Bind(self.SelectEditorPartCallBack, self))
	end

    if not self.dye_edit_scheme_render then
        self.dye_edit_scheme_render = DyeSchemeRender.New(self.node_list.dye_edit_scheme_render)
    end

    if not self.dye_bg_list then
		self.dye_bg_list = {}
			
		for i = 1, 4 do
			local dye_bg_obj = self.node_list.dye_bg_list:FindObj(string.format("dye_bg_render_%d", i))
			if dye_bg_obj then
				local cell = DyeSchemeBGRender.New(dye_bg_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.SelectDyeSchemeBGCallBack, self))
				self.dye_bg_list[i] = cell
			end
		end
	end

    if not self.part_select_list then
		self.part_select_list = {}
			
		for i = 1, 4 do
			local dye_bg_obj = self.node_list.part_select_list:FindObj(string.format("part_select_render_%d", i))
			if dye_bg_obj then
				local cell = DyeSchemePartRender.New(dye_bg_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.SelectSchemePartCallBack, self))
				self.part_select_list[i] = cell
			end
		end
	end

    if not self.part_HSV_list then
		self.part_HSV_list = {}
        self.part_edit_HSV_list = {}
			
		for i = 1, 3 do
			local part_HSV_obj = self.node_list.part_HSV_list:FindObj(string.format("part_HSV_render_%d", i))
			if part_HSV_obj then
				local cell = DyePartHSVRender.New(part_HSV_obj)
				cell:SetIndex(i)
				self.part_HSV_list[i] = cell
			end

            local edit_part_HSV_obj = self.node_list.part_edit_HSV_list:FindObj(string.format("part_HSV_render_%d", i))
			if edit_part_HSV_obj then
				local cell = DyePartHSVRender.New(edit_part_HSV_obj)
				cell:SetIndex(i)
				self.part_edit_HSV_list[i] = cell
			end
		end
	end

    self.now_show_color = {}
    self.compare_color = {}
    self.color_selector = self.node_list.color_wheel:GetComponent(ColorSelector)
    self.sub_color_selector = self.node_list.sub_color:GetComponent(SubColorSelector)
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.ClickCloseWindow, self))
    XUI.AddClickEventListener(self.node_list.btn_reset_edit, BindTool.Bind(self.ClickResetEdit, self))
    XUI.AddClickEventListener(self.node_list.btn_use_scheme, BindTool.Bind(self.ClickUseScheme, self))
    XUI.AddClickEventListener(self.node_list.btn_unlock_scheme, BindTool.Bind(self.ClickUnlockScheme, self))
    XUI.AddClickEventListener(self.node_list.btn_hit_fishion, BindTool.Bind(self.ClickHitFishion, self))
    XUI.AddClickEventListener(self.node_list.btn_change_title, BindTool.Bind(self.ClickChangeTatle, self))
    XUI.AddClickEventListener(self.node_list.btn_change_bg, BindTool.Bind(self.ClickChangeBgStatus, self))
    XUI.AddClickEventListener(self.node_list.btn_hex_color_share, BindTool.Bind(self.ClickShareScheme, self))
    XUI.AddClickEventListener(self.node_list.fashion_dye_money_btn, BindTool.Bind(self.ClickFashionDyeMoneyBtn, self))
    -- 编辑相关按钮
    XUI.AddClickEventListener(self.node_list.btn_edit_soft, BindTool.Bind(self.ClickEditSoftColor, self))
    XUI.AddClickEventListener(self.node_list.btn_edit_full, BindTool.Bind(self.ClickEditFullColor, self))
    XUI.AddClickEventListener(self.node_list.btn_edit_lens, BindTool.Bind(self.ClickEditLens, self))
    XUI.AddClickEventListener(self.node_list.btn_edit_reset, BindTool.Bind(self.ClickEditReset, self))
    XUI.AddClickEventListener(self.node_list.btn_edit_save, BindTool.Bind(self.ClickEditSave, self))
    XUI.AddClickEventListener(self.node_list.btn_edit_load, BindTool.Bind(self.ClickEditLoad, self))
    XUI.AddClickEventListener(self.node_list.btn_edit_use_scheme, BindTool.Bind(self.ClickEditUseScheme, self))
    XUI.AddClickEventListener(self.node_list.btn_ai_dye, BindTool.Bind(self.ClickEditAiScheme, self))
    XUI.AddClickEventListener(self.node_list.btn_hex_color, BindTool.Bind(self.ClickEditShowHexColor, self))
    XUI.AddClickEventListener(self.node_list.btn_hsv_color, BindTool.Bind(self.ClickEditShowHSVColor, self))
    self.node_list.edit_alpha_slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnEditAlphaValueChange, self))
    XUI.AddClickEventListener(self.node_list.btn_edit_alpha_add, BindTool.Bind2(self.OnClickEditAlphaValue, self, true))
    XUI.AddClickEventListener(self.node_list.btn_edit_alpha_sub, BindTool.Bind2(self.OnClickEditAlphaValue, self, false))

    -- 选择完颜色回调
    if self.sub_color_selector then
        self.sub_color_selector:SetSelectColorCB(BindTool.Bind(self.SelectSubColorCallBack, self))
    end

    self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
    self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.NewAppearanceDyeView, self.get_guide_ui_event)
end

function NewAppearanceDyeView:CloseCallBack()
    self.show_type = nil
    self.show_color_type = nil
    self.other_show_data = nil
    self.flush_model = nil
    self.cur_scheme_index = nil                     -- 当前方案下标
    WardrobeWGCtrl.Instance:FlushCurShowView()
end

function NewAppearanceDyeView:ReleaseCallBack()
    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    if self.left_scheme_list then
        self.left_scheme_list:DeleteMe()
        self.left_scheme_list = nil
    end

    if self.left_edit_part_list then
        self.left_edit_part_list:DeleteMe()
        self.left_edit_part_list = nil
    end

    if self.dye_edit_scheme_render then
        self.dye_edit_scheme_render:DeleteMe()
        self.dye_edit_scheme_render = nil
    end

    if self.dye_bg_list and #self.dye_bg_list > 0 then
		for _, dye_bg_cell in ipairs(self.dye_bg_list) do
			dye_bg_cell:DeleteMe()
			dye_bg_cell = nil
		end

		self.dye_bg_list = nil
	end

    if self.part_select_list and #self.part_select_list > 0 then
		for _, part_select_cell in ipairs(self.part_select_list) do
			part_select_cell:DeleteMe()
			part_select_cell = nil
		end

		self.part_select_list = nil
	end

    if self.part_HSV_list and #self.part_HSV_list > 0 then
		for _, part_HSV_cell in ipairs(self.part_HSV_list) do
			part_HSV_cell:DeleteMe()
			part_HSV_cell = nil
		end

		self.part_HSV_list = nil
	end

    if self.part_edit_HSV_list and #self.part_edit_HSV_list > 0 then
		for _, part_HSV_cell in ipairs(self.part_edit_HSV_list) do
			part_HSV_cell:DeleteMe()
			part_HSV_cell = nil
		end

		self.part_edit_HSV_list = nil
	end

    if self.lock_scheme_alert then
        self.lock_scheme_alert:DeleteMe()
        self.lock_scheme_alert = nil
    end

    self.new_appearancce_index = nil                -- 当前时装下标
    self.new_appearancce_part_type = nil            -- 当前时装代表部位
    self.cur_scheme_index = nil                     -- 当前方案下标
    self.cur_scheme_data = nil                      -- 当前方案数据
    self.cur_scheme_part = nil                      -- 当前方案部位
    self.cur_scheme_part_data = nil                 -- 当前方案部位数据
    self.curr_scheme_bg_index = nil                 -- 当前选择的背景下标
    self.color_selector = nil                       -- 取色器(大环)
    self.sub_color_selector = nil                   -- 取色器(HSV)
    self.now_show_color = nil                       -- 当前的颜色
    self.compare_color = nil
    self.now_cache_color = nil                      -- 当前缓存的颜色
    self.flush_model = nil                          -- 已刷新模型
    self.is_far = nil                               -- 是否是远景
    self.is_show_ai = nil                           -- 是否展示AI
    self.is_btn_cool = nil                          -- 是否还在冷却

    ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
	self.item_data_change_callback = nil
    self:RemoveCoolDelayTimer()
    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.NewAppearanceDyeView, self.get_guide_ui_event)
    self.get_guide_ui_event = nil
end

--移除回调
function NewAppearanceDyeView:RemoveCoolDelayTimer()
    if self.show_cool_timer then
        GlobalTimerQuest:CancelQuest(self.show_cool_timer)
        self.show_cool_timer = nil
    end
end

-----------------------------------------------list call_back start---------------
-- 方案切换
function NewAppearanceDyeView:SelectSchemeCallBack(cell, cell_index, is_default, is_click)
	if cell == nil or cell.data == nil then
		return
	end

    if self.cur_scheme_index == cell_index then
        return
    end

    self.now_show_color = {}
    self.compare_color = {}
    self.cur_scheme_index = cell_index
    self.cur_scheme_data = cell.data
    self:FlushSchemeModelMessage()
    self:FlushRightPartShowView()
end

-- 方案切换限制
function NewAppearanceDyeView:SelectSchemeLimit(cell)
    if cell == nil or cell.data == nil or cell.data.pro_cfg == nil then
        return false
    end

    local cfg = cell.data.pro_cfg
    local lock_status = NewAppearanceDyeWGData.Instance:GetDyeingProjectLockState(cell.data.seq, cell.data.pro_cfg.project_id)
    if lock_status == 0 then
        local gold_num = RoleWGData.Instance.role_info.gold or 0  
        local bind_gold_num = RoleWGData.Instance.role_info.bind_gold or 0
        local score = YanYuGeWGData.Instance:GetCurScore()

        local have_num = 0
        if cfg.consume_type == 1 then
            have_num = gold_num
        elseif cfg.consume_type == 3 then
            have_num = score 
        else
            have_num = bind_gold_num 
        end
    
        if not self.lock_scheme_alert then
            self.lock_scheme_alert = Alert.New()
        end

        local str = string.format(Language.NewAppearanceDye.SchemeTips, NumberToChinaNumber(cell.index))
        local money_str = ""
        if cfg.consume_type == 1 then
            money_str = Language.Role.XianYu
        elseif cfg.consume_type == 3 then
            money_str = Language.Common.CangJingScore  
        else
            money_str = Language.Role.BangYu
        end

        local spend_str = string.format("%s%s", cfg.consume_num, money_str)

        self.lock_scheme_alert:SetLableString(string.format(Language.NewAppearanceDye.LockSchemeTips, spend_str, str))
        self.lock_scheme_alert:SetOkFunc(function()
            if have_num >= cfg.consume_num then
                NewAppearanceDyeWGCtrl.Instance:OnReqDyeingProjectOpen(cell.data.seq, cell.data.pro_cfg.project_id)
            else
                if cfg.consume_type == 1 then
                    VipWGCtrl.Instance:OpenTipNoGold()
                elseif cfg.consume_type == 3 then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CangJingScoreNoEnough)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Bag.NoEnoughBindGold)
                end
            end
        end)
        self.lock_scheme_alert:Open()
        return true
    end

    return false
end

-- 切换背景点击
function NewAppearanceDyeView:SelectDyeSchemeBGCallBack(scheme_cell)
	if scheme_cell == nil or scheme_cell.data == nil then
		return
	end

	if self.curr_scheme_bg_index == scheme_cell.index then
		return
	end

	self.curr_scheme_bg_index = scheme_cell.index

    for i, dye_bg_cell in ipairs(self.dye_bg_list) do
        dye_bg_cell:OnSelectChange(i == self.curr_scheme_bg_index)
    end
    self:FlushSchemeBgForIndex()
end

-- 切换方案部位
function NewAppearanceDyeView:SelectSchemePartCallBack(scheme_cell)
	if self.cur_scheme_part == scheme_cell.index then
		return
	end

    self.cur_scheme_part = scheme_cell.index
    self.cur_scheme_part_data = scheme_cell.data

    for i, part_select_cell in ipairs(self.part_select_list) do
        part_select_cell:OnSelectChange(self.cur_scheme_part == i)
    end

    -- 刷新部位颜色
    self:FlushRightColorPageView()
end

-------------------------------------------------------------------------------------------------
-------------------------------------------------------------------------------------------------
-- 编辑部位切换
function NewAppearanceDyeView:SelectEditorPartCallBack(cell, cell_index, is_default, is_click)
	if cell == nil or cell.data == nil then
		return
	end

    if self.cur_scheme_part == cell_index then
        return
    end

    self.cur_scheme_part = cell_index
    self.cur_scheme_part_data = cell.data

    for i, part_select_cell in ipairs(self.part_select_list) do
        part_select_cell:OnSelectChange(self.cur_scheme_part == i)
    end

    -- 设置聚焦(切换区域还原初始位置)
    if self.show_model ~= nil then
        -- self.is_far = true
        self:FlushLensStatus()
    end
    
    self:FlushRightColorEditView()
end

-- 选择完颜色回调
function NewAppearanceDyeView:SelectSubColorCallBack(final_color, hex_color)
    -- 展示编辑颜色
    if not self.cur_scheme_part then
        return
    end

    local value = self.node_list.edit_alpha_slider.slider.value
    final_color.a = value / 255
    self.now_show_color[self.cur_scheme_part] = final_color
    self.compare_color[self.cur_scheme_part] = {r = final_color.r * 255, g = final_color.g * 255, b = final_color.b * 255, a = value}

    if self.cur_scheme_part == 0 then   -- 0 为全身
        for part, v in ipairs(self.now_show_color) do
            self.now_show_color[part] = final_color
            self.compare_color[part] = {r = final_color.r * 255, g = final_color.g * 255, b = final_color.b * 255, a = value}
            self:FlushRightFinalColor(part)
        end
    else
        self:FlushRightFinalColor()
    end

    self:FlushRightColorEditSpend()
end

--物品变化(这里需要更新红点)
function NewAppearanceDyeView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE or change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		if NewAppearanceDyeWGData.Instance:IsDyeSpendItem(change_item_id) then
            self:FlushRightColorEditSpend()
		end
	end
end
-----------------------------------------------list call_back end---------------
function NewAppearanceDyeView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
            if v.item_id ~= nil and v.item_id ~= "" then
                local fashion_item_id = tonumber(v.item_id)
                local cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(fashion_item_id)
                self.new_appearancce_index = cfg and cfg.index
                self.new_appearancce_part_type = cfg and cfg.part_type
            end
		end
	end

    if (not self.new_appearancce_index) or (not self.new_appearancce_part_type) then
       return 
    end

    self:FlushFishionModel()
    self:FlushRootActiveState()
    self:FlushRootBgSelectState()

    -- 每次刷新清除掉本都缓存颜色(非常关键)
    self.now_show_color = {}
    self.compare_color = {}

    if self.show_type == CUR_SHOW_TYPE.SCHEME_EDIT then
        self:FlushNowShowPartColor()
        self:FlushLeftShowEditView()
        self:FlushNowCacheColor()
    else
        self:FlushLeftShowView()
    end
end

-- 刷新模型
function NewAppearanceDyeView:FlushFishionModel()
    local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.new_appearancce_part_type, self.new_appearancce_index)
    if fashion_cfg and (not self.flush_model) then
        self.flush_model = true
        local res_id = fashion_cfg.resouce
        local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
        role_res_id = AppearanceWGData.GetFashionBodyResIdByResViewId(res_id)
        local extra_role_model_data = {
            weapon_res_id = weapon_res_id,
            animation_name =  SceneObjAnimator.Rest,
        }
        self.show_model:SetRoleResid(role_res_id, nil, extra_role_model_data)
    end
end

-----------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------方案刷新--------------------------------------------------------------
-- 展示节点显隐
function NewAppearanceDyeView:FlushRootBgSelectState()
    self.node_list.dye_bg_root:CustomSetActive(not self.is_show_ai)
    self.node_list.btn_ai_dye:CustomSetActive(self.is_show_ai and self.show_type == CUR_SHOW_TYPE.SCHEME_EDIT)
end

-- 展示节点显隐
function NewAppearanceDyeView:FlushRootActiveState()
    if self.show_type == nil then
        self.show_type = CUR_SHOW_TYPE.SCHEME_PAGE
    end

    if self.other_show_data ~= nil then
        -- 如果使用其他人的方案则直接到编辑界面和直接使用方案一
        local list = NewAppearanceDyeWGData.Instance:GetDyeingSchemeListByPartIndex(self.new_appearancce_index, self.new_appearancce_part_type)
        local cell_index = 1

        if list and list[cell_index] then
            self.cur_scheme_index = cell_index
            self.cur_scheme_data = list[cell_index]
            self.show_type = CUR_SHOW_TYPE.SCHEME_EDIT
        end
    end

    self.node_list.hit_fishion_root:CustomSetActive(self.show_type == CUR_SHOW_TYPE.SCHEME_PAGE)
    self.node_list.fashion_dye_left:CustomSetActive(self.show_type == CUR_SHOW_TYPE.SCHEME_PAGE)
    self.node_list.fashion_dye_right:CustomSetActive(self.show_type == CUR_SHOW_TYPE.SCHEME_PAGE)
    self.node_list.fashion_dye_edit_left:CustomSetActive(self.show_type == CUR_SHOW_TYPE.SCHEME_EDIT)
    self.node_list.fashion_dye_edit_right:CustomSetActive(self.show_type == CUR_SHOW_TYPE.SCHEME_EDIT)
    self.node_list.fashion_dye_money_bar:CustomSetActive(self.show_type == CUR_SHOW_TYPE.SCHEME_EDIT)

    if self.show_type == CUR_SHOW_TYPE.SCHEME_PAGE then
        self:FlushLensStatus(true)

        self.is_show_ai = false
    else
        self.is_far = true
        self.is_show_ai = true
    end
end

-- 刷新左侧展示列表(方案列表和背景列表)
function NewAppearanceDyeView:FlushLeftShowView()
    local list = NewAppearanceDyeWGData.Instance:GetDyeingSchemeListByPartIndex(self.new_appearancce_index, self.new_appearancce_part_type)
    self.left_scheme_list:SetDataList(list)
    local jump_select = self.cur_scheme_index
    self.cur_scheme_index = nil

    if jump_select == nil then
        -- 获取染色配置
        local fashion_dye_cfg = NewAppearanceDyeWGData.Instance:GetConsumeCfgByIndex(self.new_appearancce_index, self.new_appearancce_part_type)
        if not fashion_dye_cfg then
            return
        end

        local server_project_index = NewAppearanceDyeWGData.Instance:GetDyeingIndexInfoBySeq(fashion_dye_cfg.seq)
        local use_project_index = server_project_index + 1
        jump_select = use_project_index > 0 and use_project_index or 1
    end

    self.left_scheme_list:JumpToIndex(jump_select, 5)

    if not self.curr_scheme_bg_index then
        self.curr_scheme_bg_index = 1
    end

    for i, dye_bg_cell in ipairs(self.dye_bg_list) do
        dye_bg_cell:SetData(CUR_DYE_MAP_INDEX[i])
        dye_bg_cell:OnSelectChange(i == self.curr_scheme_bg_index)
    end

    self:FlushSchemeBgForIndex()
end

-- 切换UI场景背景(选择背景回调)
-- self.curr_scheme_bg_index 背景下标
function NewAppearanceDyeView:FlushSchemeBgForIndex()
    if self.curr_scheme_bg_index == nil then
        return
    end

    local ui_scene_index = CUR_DYE_MAP_INDEX[self.curr_scheme_bg_index]
    self.ui_scene_change_config_index = ui_scene_index
    Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.WAIGUAN, ui_scene_index)
end

-- 刷新右侧展示列表
-- self.cur_scheme_data     -- 当前方案数据
-- self.cur_scheme_index    --当前方案下标
function NewAppearanceDyeView:FlushRightPartShowView()
    if not self.cur_scheme_data then
        return
    end

    local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.new_appearancce_part_type, self.new_appearancce_index)
    local lock_status = NewAppearanceDyeWGData.Instance:GetDyeingProjectLockState(self.cur_scheme_data.seq, self.cur_scheme_data.pro_cfg.project_id)
    self.node_list.cheme_lock_root:CustomSetActive(lock_status == 0)
    self.node_list.cheme_unlock_root:CustomSetActive(lock_status ~= 0)
    self.node_list.dye_not_active_txt:CustomSetActive(not is_act)

    local name_str = self.cur_scheme_data.pro_cfg and self.cur_scheme_data.pro_cfg.project_title or "" 
    local desc_str = self.cur_scheme_data.pro_cfg and self.cur_scheme_data.pro_cfg.project_desc or ""
    if self.cur_scheme_data.dyeing_info and self.cur_scheme_data.dyeing_info.name ~= "" then
        name_str = self.cur_scheme_data.dyeing_info.name
    end

    if self.cur_scheme_data.dyeing_info and self.cur_scheme_data.dyeing_info.info ~= "" then
        desc_str = self.cur_scheme_data.dyeing_info.info
    end
  
    if name_str == "" then
        name_str = string.format(Language.NewAppearanceDye.SchemeTips, NumberToChinaNumber(self.index))
    end

    if desc_str == "" then
        desc_str = string.format(Language.NewAppearanceDye.SchemeTips, NumberToChinaNumber(self.index))
    end

    local name_list = {}
    local len = string.utf8len(name_str)
    for i = 1, len do
        local str = UTFSub.SubStringUTF8(name_str, i, i)
        name_list[i] = str
    end

    self.node_list.name_title.text.text = string.format(Language.NewAppearanceDye.SchemeNameStr, 
                                                    name_list[1] or "",
                                                    name_list[2] or "",
                                                    name_list[3] or "",
                                                    name_list[4] or ""
                                                )
    self.node_list.name_title_desc.text.text = desc_str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.name_root.rect)
    self:FlushRightPartShow()
    self:FlushModelPageColor(self.cur_scheme_data.seq, self.cur_scheme_data.dyeing_info.part_color)
end

-- 刷新方案模型
-- self.cur_scheme_data     -- 当前方案数据
-- self.cur_scheme_index    --当前方案下标
function NewAppearanceDyeView:FlushSchemeModelMessage()
    if (not self.cur_scheme_index) or (not self.show_model) or (not self.cur_scheme_data) then
        return
    end

    -- 重置颜色(重置之前的材质色)
    -- self.show_model:ResetPartDyeColor()
    local body_material_id = NewAppearanceDyeWGData.Instance:GetProjectBodyIdCfgBySeq(self.cur_scheme_data.seq, self.cur_scheme_index)
    local hair_material_id = NewAppearanceDyeWGData.Instance:GetProjectHairIdCfgBySeq(self.cur_scheme_data.seq, self.cur_scheme_index)
    local face_material_id = NewAppearanceDyeWGData.Instance:GetProjectFaceIdCfgBySeq(self.cur_scheme_data.seq, self.cur_scheme_index)
    self.show_model:ChangeRoleMaterialsByProjectId(ROLE_SKIN_TYPE.BODY, body_material_id)
    self.show_model:ChangeRoleMaterialsByProjectId(ROLE_SKIN_TYPE.HAIR, hair_material_id)
    self.show_model:ChangeRoleMaterialsByProjectId(ROLE_SKIN_TYPE.FACE, face_material_id)
end

-- 刷新方案界面区域选择
function NewAppearanceDyeView:FlushRightPartShow()
    if not self.cur_scheme_data then
        return
    end 

    local list = self.cur_scheme_data.dyeing_info.part_color
    if self.cur_scheme_part == nil then
        self.cur_scheme_part = 1
    end

    self.cur_scheme_part_data = list[self.cur_scheme_part]
    for i, part_select_cell in ipairs(self.part_select_list) do
        part_select_cell:SetVisible(list[i] ~= nil)

        if list[i] ~= nil then
            part_select_cell:SetData(list[i])
            part_select_cell:OnSelectChange(self.cur_scheme_part == i)
        end
    end

    -- 刷新部位颜色
    self:FlushRightColorPageView()
end

-- 展示方案颜色(只展示保存色)
-- self.cur_scheme_part 区域
-- self.cur_scheme_part_data 区域信息
function NewAppearanceDyeView:FlushRightColorPageView()
    if not self.cur_scheme_part_data then
        return
    end 

    local data = self.cur_scheme_part_data
    local color = nil
    if data.r ~= 0 or data.g ~= 0 or data.b ~= 0 or data.a ~= 0 then
        color = Color.New(data.r / 255, data.g / 255, data.b / 255, data.a / 255)
    else
        local defult_color = NewAppearanceDyeWGData.Instance:GetDefultColorCfgBySeqPart(self.cur_scheme_data.seq, self.cur_scheme_index, self.cur_scheme_part)
        color = UtilU3d.ConvertHexToColor(defult_color)
    end

    local hsv = UtilU3d.ConvertColorToHSV(color)
    for i, part_HSV_cell in ipairs(self.part_HSV_list) do
        local data = math.floor(hsv.x * 360)
        if i == 2 then
            data = math.floor(hsv.y * 100)
        elseif i == 3 then
            data = math.floor(hsv.z * 100)
        end

        part_HSV_cell:SetData(data)
    end

    self.node_list.alpha_txt.text.text = string.format(Language.NewAppearanceDye.AlphaValue, math.floor(color.a * 255))
    self.node_list.alpha_slider.slider.value = color.a * 255
end

-- 刷新模型颜色(只刷新保存颜色)
function NewAppearanceDyeView:FlushModelPageColor(seq, part_color)
    if (not part_color) or (not seq) then
        return
    end

    local dye_color_table = {}
    for show_part, color_data in ipairs(part_color) do
        local dye_index_list_data = NewAppearanceDyeWGData.Instance:GetDyeIndexListBySeqPart(seq, show_part)
        local index_list = dye_index_list_data and dye_index_list_data.dye_index_list

        local color = nil
        if color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0 then
            color = Color.New(color_data.r / 255, color_data.g / 255, color_data.b / 255, color_data.a / 255)
        else
            local defult_color = NewAppearanceDyeWGData.Instance:GetDefultColorCfgBySeqPart(seq, self.cur_scheme_index, show_part, true)
            if defult_color ~= nil then
                color = UtilU3d.ConvertHexToColor(defult_color)
            end
        end

        if show_part == HAIR_PART and color then
            local new_color = {r = math.floor(color.r * 255), g = math.floor(color.g * 255), b = math.floor(color.b * 255), a = math.floor(color.a * 255)}
            if new_color and (new_color.r ~= 0 or new_color.g ~= 0 or new_color.b ~= 0 or new_color.a ~= 0) then
                self.show_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, new_color)
			end
        else
            if index_list and color then
                for _, dye_index in ipairs(index_list) do
                    local dye_color_data = {}
                    dye_color_data.dye_index = dye_index
                    dye_color_data.r = color.r
                    dye_color_data.g = color.g
                    dye_color_data.b = color.b
                    dye_color_data.a = color.a
                    table.insert(dye_color_table, dye_color_data)
                end
            end  
        end
    end

    if self.show_model and (not IsEmptyTable(dye_color_table)) then
        self.show_model:ChangePartDyeColor(dye_color_table)
    end
end
-----------------------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------------------
-- 刷新缓存颜色
function NewAppearanceDyeView:FlushNowCacheColor()
    self.node_list.now_cache_color_root:CustomSetActive(self.now_cache_color ~= nil)

    if not self.now_cache_color then
        return
    end

    self.node_list.now_cache_color_image.image.color = self.now_cache_color
end

-- 刷新本地缓存颜色如何没有保存颜色取默认色
function NewAppearanceDyeView:FlushNowShowPartColor()
    if not self.cur_scheme_data then
        return
    end

    local project_info = NewAppearanceDyeWGData.Instance:GetDyeingInfoBySeqIndex(self.cur_scheme_data.seq, self.cur_scheme_index)
    if (not project_info) or (not project_info.part_color) then
        return
    end

    if self.other_show_data ~= nil then
        -- 如果使用其他人的方案则直接到编辑界面和直接使用方案一
        project_info = self.other_show_data.dyeing_project
    end

    local save_alpha = nil

    for scheme_part, data in ipairs(project_info.part_color) do
        self.compare_color[scheme_part] = {r = data.r, g = data.g, b = data.b, a = data.a}

        local color = nil
        if data.r ~= 0 or data.g ~= 0 or data.b ~= 0 or data.a ~= 0 then
            color = Color.New(data.r / 255, data.g / 255, data.b / 255, data.a / 255)
        else
            local defult_color = NewAppearanceDyeWGData.Instance:GetDefultColorCfgBySeqPart(self.cur_scheme_data.seq, self.cur_scheme_index, scheme_part)
            color = UtilU3d.ConvertHexToColor(defult_color)
            self.compare_color[scheme_part] = {r = color.r, g = color.g, b = color.b, a = color.a * 255}
        end
        
        self.now_show_color[scheme_part] = color
        self:FlushChangeModelDyeColor(scheme_part)
    end

    self.now_show_color[0] = Color.New(1, 1, 1, 1)
end

-- 刷新本地缓存颜色如何没有保存颜色取默认色
function NewAppearanceDyeView:FlushModelNowShowPartColor()
    if not self.now_show_color then
        return
    end

    for scheme_part, show_color in pairs(self.now_show_color) do
        self:FlushChangeModelDyeColor(scheme_part)
    end
end

-- 刷新左侧编辑列表
function NewAppearanceDyeView:FlushLeftShowEditView()
    if not self.cur_scheme_data then
        return
    end

    local list = self.cur_scheme_data.dyeing_info.part_color
    local jump_scheme_part = self.cur_scheme_part
    self.cur_scheme_part = nil

    --插入一份临时的全身数据到0号位置
    local data = {r = 0, g = 0, b = 0, a = 0}
    list[0] = data

    if jump_scheme_part == nil then
        jump_scheme_part = 0
    end

    self.left_edit_part_list:SetDataList(list)
    self.left_edit_part_list:JumpToIndex(jump_scheme_part, 5)
    self.dye_edit_scheme_render:SetIndex(self.cur_scheme_data.pro_cfg.project_id + 1)
    self.dye_edit_scheme_render:SetData(self.cur_scheme_data)
    self.dye_edit_scheme_render:SetRenderSelectState()
end

-- 展示编辑方案颜色
-- self.cur_scheme_part 区域
-- self.cur_scheme_part_data 区域信息
function NewAppearanceDyeView:FlushRightColorEditView()
    self:FlushDyeColorTypeToggle()
    self:FlushColorRootActiveState()

    local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.new_appearancce_part_type, self.new_appearancce_index)
    self.node_list.dye_edit_not_active_txt:CustomSetActive(not is_act)
    self.node_list.dye_edit_active_root:CustomSetActive(is_act)
    local show_color = self.now_show_color[self.cur_scheme_part]

    if not show_color then
        return
    end

    -- 取色器选中颜色
    if self.color_selector ~= nil then  -- 回调刷新花费和模型颜色
        self.color_selector:UpdateColorSelectorWheel(show_color)
    end
    self:FlushRightFinalColor()
    self:FlushRightColorEditSpend()
end

-- 展示颜色开关显隐
function NewAppearanceDyeView:FlushDyeColorTypeToggle(is_change_soft)
    if self.cur_dye_color_type == nil then
        self.cur_dye_color_type = CUR_DYE_COLOR_TYPE.FEEL_FREE_COLOR_TYPE
    end

    if not is_change_soft then
        self.node_list.btn_edit_full.toggle.isOn = self.cur_dye_color_type == CUR_DYE_COLOR_TYPE.FEEL_FREE_COLOR_TYPE
    end

    if is_change_soft then
        if self.color_selector == nil then
            return
        end
    
        if self.cur_dye_color_type == CUR_DYE_COLOR_TYPE.LITHE_DYE_COLOR_TYPE then
            self.color_selector:SetIsSoft(true, 0.7)
        else
            self.color_selector:SetIsSoft(true, 1)
        end
    end
end

-- 展示颜色节点显隐
function NewAppearanceDyeView:FlushColorRootActiveState()
    if self.show_color_type == nil then
        self.show_color_type = CUR_SHOW_COLOR_TYPE.SCHEME_COLOR_HSV
    end

    self.node_list.part_edit_HSV_list:CustomSetActive(self.show_color_type == CUR_SHOW_COLOR_TYPE.SCHEME_COLOR_HSV)
    self.node_list.part_edit_hex_root:CustomSetActive(self.show_color_type == CUR_SHOW_COLOR_TYPE.SCHEME_COLOR_HEX)
    self.node_list.btn_hex_color:CustomSetActive(self.show_color_type == CUR_SHOW_COLOR_TYPE.SCHEME_COLOR_HSV)
    self.node_list.btn_hsv_color:CustomSetActive(self.show_color_type == CUR_SHOW_COLOR_TYPE.SCHEME_COLOR_HEX)
end

-- 展示编辑颜色
function NewAppearanceDyeView:FlushRightFinalColor(force_scheme_part)
    if (not self.cur_scheme_part) or (not self.now_show_color[self.cur_scheme_part]) then
        return
    end

    local show_color = self.now_show_color[self.cur_scheme_part]
    local com_color = self.compare_color[self.cur_scheme_part]
    self.node_list.btn_hex_color_image.image.color = show_color
    self.node_list.part_edit_hex_image.image.color = show_color
    self.node_list.part_edit_hex_txt.text.text = string.sub(UtilU3d.ConvertColorToHex(show_color), 1, 6) 

    local hsv = UtilU3d.ConvertColorToHSV(show_color)
    
    for i, part_edit_HSV_cell in ipairs(self.part_edit_HSV_list) do
        local data = math.floor(hsv.x * 360)
        if i == 2 then
            data = math.floor(hsv.y * 100)
        elseif i == 3 then
            data = math.floor(hsv.z * 100)
        end
        part_edit_HSV_cell:SetData(data)
    end

    self.node_list.edit_alpha_root:CustomSetActive(self.cur_scheme_part ~= HAIR_PART and self.cur_scheme_part ~= 0)
    if self.cur_scheme_part ~= HAIR_PART and self.cur_scheme_part ~= 0 then
        self.node_list.edit_alpha_txt.text.text = string.format(Language.NewAppearanceDye.AlphaValue, com_color and com_color.a or 255)
        self.node_list.edit_alpha_slider.slider.value = com_color and com_color.a or 255
    end
    self:FlushChangeModelDyeColor(force_scheme_part or self.cur_scheme_part)
end

-- 修改模型染色
function NewAppearanceDyeView:FlushChangeModelDyeColor(scheme_part)
    if (not scheme_part) or (not self.now_show_color[scheme_part]) or (not self.cur_scheme_data) then
        return
    end

    local dye_index_list_data = NewAppearanceDyeWGData.Instance:GetDyeIndexListBySeqPart(self.cur_scheme_data.seq, scheme_part)
    local index_list = dye_index_list_data and dye_index_list_data.dye_index_list
    local show_color = self.now_show_color[scheme_part]
    local dye_color_table = {}
    local dye_reset_color_table = {}

    if scheme_part == HAIR_PART then
        if show_color.r ~= 1 or show_color.g ~= 1 or show_color.b ~= 1 then
            local new_color = {r = math.floor(show_color.r * 255), g = math.floor(show_color.g * 255), b = math.floor(show_color.b * 255), a = math.floor(show_color.a * 255)}
            self.show_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, new_color)
        else
            self.show_model:ResetPartHairDyeColor()
        end
    else
        if index_list and show_color then
            for _, dye_index in ipairs(index_list) do
                local dye_color_data = {}
                dye_color_data.dye_index = dye_index
                dye_color_data.r = show_color.r
                dye_color_data.g = show_color.g
                dye_color_data.b = show_color.b
                dye_color_data.a = show_color.a

                if show_color.r ~= 1 or show_color.g ~= 1 or show_color.b ~= 1 then
                    table.insert(dye_color_table, dye_color_data)
                else
                    table.insert(dye_reset_color_table, dye_color_data)
                end
            end
        end
    end

    if self.show_model then
        if not IsEmptyTable(dye_color_table) then
            self.show_model:ChangePartDyeColor(dye_color_table)
        end

        if not IsEmptyTable(dye_reset_color_table) then
            self.show_model:ResetPartDyeTableColor(dye_reset_color_table)
        end
    end
end

-- 刷新染色花费
function NewAppearanceDyeView:FlushRightColorEditSpend()
    if self.cur_scheme_data == nil or self.cur_scheme_part == nil then
        return
    end

    local spend_total_num = 0
    local project_info = NewAppearanceDyeWGData.Instance:GetDyeingInfoBySeqIndex(self.cur_scheme_data.seq, self.cur_scheme_index)
    if (not project_info) or (not project_info.part_color) then
        return
    end

    for show_part, color_data in ipairs(project_info.part_color) do
        local show_color = self.compare_color[show_part]

        if (show_color.r ~= 1 or show_color.g ~= 1 or show_color.b ~= 1 or show_color.a < 0.99) and 
        (show_color.r ~= color_data.r or show_color.g ~= color_data.g or show_color.b ~= color_data.b or show_color.a ~= color_data.a ) then
            local spend_data = NewAppearanceDyeWGData.Instance:GetConsumeSpendCfgBySeqPart(self.cur_scheme_data.seq, show_part)
            local spend_num = spend_data and spend_data.spend
            spend_total_num = spend_total_num + spend_num
        end
    end

    local show_cur_part = self.cur_scheme_part
    if self.cur_scheme_part == 0 then
        show_cur_part = 1
    end

    local spend_data = NewAppearanceDyeWGData.Instance:GetConsumeSpendCfgBySeqPart(self.cur_scheme_data.seq, show_cur_part)
    if spend_data then
        ---状态显示
        local item_num = ItemWGData.Instance:GetItemNumInBagById(spend_data.consume_item)
        local icon_id = ItemWGData.Instance:GetItemIconByItemId(spend_data.consume_item)
        self.node_list.use_scheme_spend_icon.image:LoadSprite(ResPath.GetItem(icon_id))
        self.node_list.use_scheme_spend_txt.text.text = string.format("x%d", spend_total_num)
        self.node_list.fashion_dye_money_icon.image:LoadSprite(ResPath.GetItem(icon_id))
        self.node_list.fashion_dye_money_txt.text.text = item_num
        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.dye_edit_active_root.rect)
    end
end
-------------------------------------------------------------------------------------------------
-------------------------------------------------------------------------------------------------
-- 刷新镜头设置
function NewAppearanceDyeView:FlushLensStatus(is_force_far)
    if not self.cur_scheme_part then
        return
    end

    local cell_index = self.cur_scheme_part

    if self.show_model ~= nil then
        self.show_model:SetModelFocus(FOCUS_PART_TO_POS[cell_index], FOCUS_PART_FROM_POS[cell_index], CUR_FOCUS_SCALE, Vector3.one, 0.8)

        if self.is_far or is_force_far then
            self.show_model:ModelFocusStatus(true)
            self.show_model:ModelFocusMove(false)
        else
            self.show_model:ModelFocusStatus(false)
            self.show_model:ModelFocusMove(true)
        end
    end

    self.node_list.far_status:CustomSetActive(not self.is_far)
    self.node_list.neer_status:CustomSetActive(self.is_far)
end

function NewAppearanceDyeView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
    if ui_name == "dye_edit_scheme_guide_render" then
        local fun = function()
            self.left_edit_part_list:JumpToIndex(0, 5)
        end
        return self.node_list[ui_name], fun
    end
	return self.node_list[ui_name]
end

-------------------------------------------------------------------------------------------------
-- 点击关闭界面
function NewAppearanceDyeView:ClickCloseWindow()
    if self.show_type == nil then
        self:Close()
        return
    end

    if self.show_type == CUR_SHOW_TYPE.SCHEME_PAGE then
        self:Close()
    else
        self.show_type = CUR_SHOW_TYPE.SCHEME_PAGE
        if self.cur_scheme_part == 0 then
            self.cur_scheme_part = 1
        end

        if self.other_show_data ~= nil then
            self.other_show_data = nil
        end
        
        self:Flush()
    end
end

-- 点击编辑
function NewAppearanceDyeView:ClickResetEdit()
    self.show_type = CUR_SHOW_TYPE.SCHEME_EDIT
    self.cur_scheme_part = nil
    self:Flush()
end

-- 点击使用
function NewAppearanceDyeView:ClickUseScheme()
    if self.new_appearancce_part_type == nil or self.new_appearancce_index == nil or self.cur_scheme_data == nil or self.cur_scheme_index == nil then
        return
    end

    ---状态显示
    local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.new_appearancce_part_type, self.new_appearancce_index)
    if not is_act then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearanceDye.UseSchemeTips)
        return
    end

    local server_project_index = NewAppearanceDyeWGData.Instance:GetDyeingIndexInfoBySeq(self.cur_scheme_data.seq)
    local use_project_index = server_project_index + 1

    if use_project_index == self.cur_scheme_index then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearanceDye.UseSchemeTips2)
        return
    end

    local send_color_part = {}
    local part_color = self.cur_scheme_data.dyeing_info.part_color

    if (not part_color) or (not self.cur_scheme_data.seq) then
        return
    end

    for show_part, color_data in ipairs(part_color) do
        local color = nil
        if color_data.r == 0 and color_data.g == 0 and color_data.b == 0 and color_data.a == 0 then
            local defult_color_hex = NewAppearanceDyeWGData.Instance:GetDefultColorCfgBySeqPart(self.cur_scheme_data.seq, self.cur_scheme_index, show_part, true)
            if defult_color_hex ~= nil then
                local defult_color = UtilU3d.ConvertHexToColor(defult_color_hex)
                color =  {r = math.floor(defult_color.r * 255), g = math.floor(defult_color.g * 255), b = math.floor(defult_color.b * 255), a = math.floor(defult_color.a * 255)}
            end
        end

        send_color_part[show_part] = color
    end

    SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearanceDye.SchemeColorUseTips)
    NewAppearanceDyeWGCtrl.Instance:CSShizhuangDyeingOperate(self.cur_scheme_data.seq, self.cur_scheme_index - 1, 0, send_color_part)
    NewAppearanceDyeWGCtrl.Instance:OnReqDyeingUseProject(self.cur_scheme_data.seq, self.cur_scheme_index - 1)
end

-- 点击解锁方案
function NewAppearanceDyeView:ClickUnlockScheme()
    if not self.cur_scheme_data then
        return
    end

    local cfg = self.cur_scheme_data.pro_cfg
    local lock_status = NewAppearanceDyeWGData.Instance:GetDyeingProjectLockState(self.cur_scheme_data.seq, cfg.project_id)
    if lock_status == 0 then
        local gold_num = RoleWGData.Instance.role_info.gold or 0  
        local bind_gold_num = RoleWGData.Instance.role_info.bind_gold or 0
        local score = YanYuGeWGData.Instance:GetCurScore()

        local have_num = 0
        if cfg.consume_type == 1 then
            have_num = gold_num
        elseif cfg.consume_type == 3 then
            have_num = score 
        else
            have_num = bind_gold_num 
        end
    
        if not self.lock_scheme_alert then
            self.lock_scheme_alert = Alert.New()
        end

        local str = string.format(Language.NewAppearanceDye.SchemeTips, NumberToChinaNumber(cfg.project_id + 1))
        local money_str = ""
        if cfg.consume_type == 1 then
            money_str = Language.Role.XianYu
        elseif cfg.consume_type == 3 then
            money_str = Language.Common.CangJingScore  
        else
            money_str = Language.Role.BangYu
        end

        local spend_str = string.format("%s%s", cfg.consume_num, money_str)

        self.lock_scheme_alert:SetLableString(string.format(Language.NewAppearanceDye.LockSchemeTips, spend_str, str))
        self.lock_scheme_alert:SetOkFunc(function()
            if have_num >= cfg.consume_num then
                NewAppearanceDyeWGCtrl.Instance:OnReqDyeingProjectOpen(self.cur_scheme_data.seq, cfg.project_id)
            else
                if cfg.consume_type == 1 then
                    VipWGCtrl.Instance:OpenTipNoGold()
                elseif cfg.consume_type == 3 then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CangJingScoreNoEnough)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Bag.NoEnoughBindGold)
                end
            end
        end)
        self.lock_scheme_alert:Open()
    end
end

-- 隐藏UI
function NewAppearanceDyeView:ClickHitFishion(is_on)
    self.node_list.fashion_dye_left:CustomSetActive(not is_on)
    self.node_list.fashion_dye_right:CustomSetActive(not is_on)
end

-- 改变方案
function NewAppearanceDyeView:ClickChangeTatle()
    if not self.cur_scheme_data then
        return
    end

    NewAppearanceDyeWGCtrl.Instance:OpenAppearanceDyeChangeChemeView(self.cur_scheme_data)
end

-- 背景按钮
function NewAppearanceDyeView:ClickChangeBgStatus()
    self.is_show_ai = not self.is_show_ai
    self:FlushRootBgSelectState()
end

-- 分享染色方案
function NewAppearanceDyeView:ClickShareScheme()
    if self.is_btn_cool then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OperateFrequencyTip)
        return
    end

    self.is_btn_cool = true
    self:RemoveCoolDelayTimer()
	self.show_cool_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self.is_btn_cool = false
	end, 1)

    if self.cur_scheme_data == nil or self.cur_scheme_part == nil then
        return
    end

    local can_share = false
    local project_info = NewAppearanceDyeWGData.Instance:GetDyeingInfoBySeqIndex(self.cur_scheme_data.seq, self.cur_scheme_index)
    if (not project_info) or (not project_info.part_color) then
        return
    end

    for show_part, color_data in ipairs(project_info.part_color) do
        if color_data.r ~= 0 and color_data.g ~= 0 and color_data.b ~= 0 and color_data.a ~= 0 then
            can_share = true
        end
    end

    if not can_share then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearanceDye.SchemeColorShareError)
        return
    end

    NewAppearanceDyeWGCtrl.Instance:OnReqDyeingShare(self.cur_scheme_data.seq, self.cur_scheme_index - 1)
end

-- 轻盈染色
function NewAppearanceDyeView:ClickEditSoftColor(is_on)
    if is_on then
        if self.cur_dye_color_type == CUR_DYE_COLOR_TYPE.LITHE_DYE_COLOR_TYPE then
            return
        end
    
        self.cur_dye_color_type = CUR_DYE_COLOR_TYPE.LITHE_DYE_COLOR_TYPE
        self:FlushDyeColorTypeToggle(true)
    end
end

-- 随心染色
function NewAppearanceDyeView:ClickEditFullColor(is_on)
    if is_on then
        if self.cur_dye_color_type == CUR_DYE_COLOR_TYPE.FEEL_FREE_COLOR_TYPE then
            return
        end
    
        self.cur_dye_color_type = CUR_DYE_COLOR_TYPE.FEEL_FREE_COLOR_TYPE
        self:FlushDyeColorTypeToggle(true)
    end
end

-- 点击镜头切换
function NewAppearanceDyeView:ClickEditLens()
    if self.is_btn_cool then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OperateFrequencyTip)
        return
    end

    self.is_btn_cool = true
    self:RemoveCoolDelayTimer()
	self.show_cool_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self.is_btn_cool = false
	end, 1)

    self.is_far = not self.is_far
    self:FlushLensStatus()
end

-- 点击重置颜色
function NewAppearanceDyeView:ClickEditReset()
    -- 展示编辑颜色
    if (not self.cur_scheme_part) or (not self.now_show_color[self.cur_scheme_part]) or (not self.cur_scheme_part_data) then
        return
    end

    local data = self.cur_scheme_part_data
    local color = nil

    local get_real_color_num = function(color_num)
        if color_num ~= 0 then
            return color_num / 255
        else
            return 1
        end
    end

    color = Color.New(get_real_color_num(data.r), get_real_color_num(data.g), get_real_color_num(data.b), get_real_color_num(data.a))

    self.now_show_color[self.cur_scheme_part] = color
    self.compare_color[self.cur_scheme_part] = data
    if not IsNil(self.color_selector) then
        self.color_selector:UpdateColorSelectorWheel(color)
    end

    if self.cur_scheme_part == 0 then   -- 0 为全身
        for part, v in ipairs(self.now_show_color) do
            self.now_show_color[part] = color
            self.compare_color[part] = {r = color.r * 255, g = color.g * 255, b = color.b * 255, a = color.a * 255}
            self:FlushChangeModelDyeColor(part)
        end
    else
        self:FlushRightFinalColor()
    end
    self:FlushRightColorEditSpend()
end

-- 点击缓存
function NewAppearanceDyeView:ClickEditSave()
    if not self.now_show_color[self.cur_scheme_part] then
        return
    end

    self.now_cache_color = self.now_show_color[self.cur_scheme_part]
    self:FlushNowCacheColor()
end

-- 点击载入
function NewAppearanceDyeView:ClickEditLoad()
    if not self.now_cache_color then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearanceDye.NoCacheColor)
        return
    end

    self.now_show_color[self.cur_scheme_part] = self.now_cache_color
    self.compare_color[self.cur_scheme_part] = {r = self.now_cache_color.r * 255, g = self.now_cache_color.g * 255, b = self.now_cache_color.b * 255, a = self.now_cache_color.a * 255}
    if not IsNil(self.color_selector) then
        self.color_selector:UpdateColorSelectorWheel(self.now_cache_color)
    end

    if self.cur_scheme_part == 0 then   -- 0 为全身
        for part, v in ipairs(self.now_show_color) do
            self.now_show_color[part] = self.now_cache_color
            self.compare_color[part] = {r = self.now_cache_color.r * 255, g = self.now_cache_color.g * 255, b = self.now_cache_color.b * 255, a = self.now_cache_color.a * 255}
            self:FlushChangeModelDyeColor(part)
        end
    else
        self:FlushRightFinalColor()
    end

    self:FlushRightColorEditSpend()
end

-- 点击使用方案
function NewAppearanceDyeView:ClickEditUseScheme()
    if self.new_appearancce_part_type == nil or self.new_appearancce_index == nil or self.cur_scheme_data == nil or self.cur_scheme_index == nil then
        return
    end

    ---状态显示
    local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(self.new_appearancce_part_type, self.new_appearancce_index)
    if not is_act then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearanceDye.UseSchemeTips)
        return
    end

    local spend_total_num = 0
    local project_info = NewAppearanceDyeWGData.Instance:GetDyeingInfoBySeqIndex(self.cur_scheme_data.seq, self.cur_scheme_index)
    if (not project_info) or (not project_info.part_color) then
        return
    end

    for show_part, color_data in ipairs(project_info.part_color) do
        local show_color = self.compare_color[show_part]
        if show_color ~= nil then

            if (show_color.r ~= 1 or show_color.g ~= 1 or show_color.b ~= 1 or show_color.a < 0.99) and 
            (show_color.r ~= color_data.r or show_color.g ~= color_data.g or show_color.b ~= color_data.b or show_color.a ~= color_data.a ) then
                local spend_data = NewAppearanceDyeWGData.Instance:GetConsumeSpendCfgBySeqPart(self.cur_scheme_data.seq, show_part)
                local spend_num = spend_data and spend_data.spend
                spend_total_num = spend_total_num + spend_num
            end
        end
    end

    -- 是否染色
    if spend_total_num == 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearanceDye.NoColorChangeTips)
        return
    end

    local send_color_part = {}
    for i, v in ipairs(self.compare_color) do
        local compare_color_r = math.floor(v.r)
        local compare_color_g = math.floor(v.g)
        local compare_color_b = math.floor(v.b)
        local compare_color_a = math.floor(v.a)
        local data = {}
        data.r = compare_color_r ~= 1 and compare_color_r or 0
        data.g = compare_color_g ~= 1 and compare_color_g or 0
        data.b = compare_color_b ~= 1 and compare_color_b or 0
        data.a = compare_color_a ~= 1 and compare_color_a or 0
        send_color_part[i] = data
    end

    local show_cur_part = self.cur_scheme_part
    if self.cur_scheme_part == 0 then
        show_cur_part = 1
    end

    local spend_data = NewAppearanceDyeWGData.Instance:GetConsumeSpendCfgBySeqPart(self.cur_scheme_data.seq, show_cur_part)
    if spend_data then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(spend_data.consume_item)
        if item_num >= spend_total_num then
            NewAppearanceDyeWGCtrl.Instance:CSShizhuangDyeingOperate(self.cur_scheme_data.seq, self.cur_scheme_index - 1, 1, send_color_part)
            NewAppearanceDyeWGCtrl.Instance:OnReqDyeingUseProject(self.cur_scheme_data.seq, self.cur_scheme_index - 1)
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearanceDye.SchemeColorSaveTips)
        else
            TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = spend_data.consume_item })
        end
    end
end

--道具添加
function NewAppearanceDyeView:ClickFashionDyeMoneyBtn()
    if self.new_appearancce_part_type == nil or self.new_appearancce_index == nil or self.cur_scheme_data == nil or self.cur_scheme_index == nil then
        return
    end

    local show_cur_part = self.cur_scheme_part
    if self.cur_scheme_part == 0 then
        show_cur_part = 1
    end

    local spend_data = NewAppearanceDyeWGData.Instance:GetConsumeSpendCfgBySeqPart(self.cur_scheme_data.seq, show_cur_part)
    if spend_data then
        TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = spend_data.consume_item })
    end
end

-- 点击使用ai方案
function NewAppearanceDyeView:ClickEditAiScheme()
    if not self.cur_scheme_data then
        return
    end

    local ai_list = NewAppearanceDyeWGData.Instance:GetDyeRandomAiListBySeq(self.cur_scheme_data.seq)
    local ai_list_len = #ai_list
    local random_index = math.random(1, ai_list_len)
    local ai_part_list = ai_list[random_index]

    for _, ai_part_data in pairs(ai_part_list) do
        if ai_part_data and ai_part_data.part then
            local hex_color = ai_part_data.hex_color
            local ai_color = UtilU3d.ConvertHexToColor(hex_color)
            self.now_show_color[ai_part_data.part] = ai_color
            self.compare_color[ai_part_data.part] = {r = ai_color.r * 255, g = ai_color.g * 255, b = ai_color.b * 255, a = ai_color.a * 255}

            if ai_part_data.part == self.cur_scheme_part then
                if (not IsNil(self.color_selector)) then
                    self.color_selector:UpdateColorSelectorWheel(ai_color)
                end
                self:FlushRightFinalColor()
            else
                self:FlushChangeModelDyeColor(ai_part_data.part)
            end
        end
    end

    self:FlushRightColorEditSpend()
end

-- 点击展示Hex颜色
function NewAppearanceDyeView:ClickEditShowHexColor()
    if self.show_color_type == CUR_SHOW_COLOR_TYPE.SCHEME_COLOR_HEX then
        return
    end

    self.show_color_type = CUR_SHOW_COLOR_TYPE.SCHEME_COLOR_HEX
    self:FlushColorRootActiveState()
end

-- 点击展示HSV颜色
function NewAppearanceDyeView:ClickEditShowHSVColor()
    if self.show_color_type == CUR_SHOW_COLOR_TYPE.SCHEME_COLOR_HSV then
        return
    end

    self.show_color_type = CUR_SHOW_COLOR_TYPE.SCHEME_COLOR_HSV
    self:FlushColorRootActiveState()
end

-- 透明度浓度变化
function NewAppearanceDyeView:OnEditAlphaValueChange(value)
    -- 展示编辑颜色
    if not self.cur_scheme_part then
        return
    end

    if self.cur_scheme_part == HAIR_PART or self.cur_scheme_part == 0 then
        return
    end

    if not self.compare_color then
        self.compare_color = {}
    end

    self.now_show_color[self.cur_scheme_part].a = value / 255

    if self.compare_color[self.cur_scheme_part] ~= nil and self.compare_color[self.cur_scheme_part].a ~= 1 then
        self.compare_color[self.cur_scheme_part].a = value
    end

    if self.cur_scheme_part == 0 then   -- 0 为全身
        for part, v in ipairs(self.now_show_color) do
            self.now_show_color[part].a = value / 255
            self.compare_color[part].a = value 
            self:FlushRightFinalColor(part)
        end
    else
        self:FlushRightFinalColor()
    end

    self:FlushRightColorEditSpend()
end

-- 透明度浓度变化(按钮加减)
function NewAppearanceDyeView:OnClickEditAlphaValue(is_add)
    -- 展示编辑颜色
    if not self.cur_scheme_part then
        return
    end

    if self.cur_scheme_part == HAIR_PART or self.cur_scheme_part == 0 then
        return
    end

    local value = self.node_list.edit_alpha_slider.slider.value
    local offset = is_add and 1 or -1
    value = value + offset

    if value <= 0 then
        value = 0
    end

    if value >= 255 then
        value = 255
    end

    self.now_show_color[self.cur_scheme_part].a = value / 255

    if self.compare_color[self.cur_scheme_part] ~= nil and self.compare_color[self.cur_scheme_part].a ~= 1 then
        self.compare_color[self.cur_scheme_part].a = value
    end

    if self.cur_scheme_part == 0 then   -- 0 为全身
        for part, v in ipairs(self.now_show_color) do
            self.now_show_color[part].a = value / 255
            self.compare_color[part].a = value 

            self:FlushRightFinalColor(part)
        end
    else
        self:FlushRightFinalColor()
    end

    self:FlushRightColorEditSpend()
end

-------------------------------------------------------------------------------------------------
----------------------------------染色方案item-----------------------
DyeSchemeRender = DyeSchemeRender or BaseClass(BaseRender)
function DyeSchemeRender:OnFlush()
    if not self.data then
        return
    end

    local lock_status = NewAppearanceDyeWGData.Instance:GetDyeingProjectLockState(self.data.seq, self.data.pro_cfg.project_id)
    self.node_list.lock:CustomSetActive(lock_status ~= 1)
    local name_str = self.data.pro_cfg and self.data.pro_cfg.project_title or "" 

    if self.data.dyeing_info then
        if self.data.dyeing_info.name and self.data.dyeing_info.name ~= "" then
            name_str = self.data.dyeing_info.name
        end
    end

    if name_str == "" then
        name_str = string.format(Language.NewAppearanceDye.SchemeTips, NumberToChinaNumber(self.index))
    end

    self.node_list.normal_txt.text.text = name_str
    self.node_list.select_txt.text.text = name_str
end

function DyeSchemeRender:SetRenderSelectState()
    self:OnSelectChange(true)
    self.node_list.lock:CustomSetActive(false)
end

function DyeSchemeRender:OnSelectChange(is_select)
    self.node_list.normal:CustomSetActive(not is_select)
    self.node_list.select:CustomSetActive(is_select)
end

----------------------------------染色方案部位item-----------------------
DyeSchemePartRender = DyeSchemePartRender or BaseClass(BaseRender)
function DyeSchemePartRender:OnFlush()
    if not self.data then
        return
    end
    
    if self.node_list.normal_txt then
        self.node_list.normal_txt.text.text = Language.NewAppearanceDye.SchemePartList[self.index]
    end

    if self.node_list.select_txt then
        self.node_list.select_txt.text.text = Language.NewAppearanceDye.SchemePartList[self.index]
    end
end


function DyeSchemePartRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

----------------------------------染色方案背景item-----------------------
DyeSchemeBGRender = DyeSchemeBGRender or BaseClass(BaseRender)
function DyeSchemeBGRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

----------------------------------染色方案部位HSVitem-----------------------
DyePartHSVRender = DyePartHSVRender or BaseClass(BaseRender)
function DyePartHSVRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.value.text.text = self.data
end
