require("game/compose/compose_longhun_view")
NewComposeView = NewComposeView or BaseClass(SafeBaseView)

COMPOSE_TYPE = {
	BAOSHI = TabIndex.other_compose_stone,				--宝石
	DAOJU = TabIndex.other_compose_daoju,				--道具
	TOAZHUANG = TabIndex.other_compose_taozhuang,		--套装
	--ACT_COMPOSE = TabIndex.other_compose_shitian,       --弑天套装合成
	SHIZHUANG = TabIndex.other_compose_shizhuang,		--时装
	BACKGROUND = TabIndex.other_compose_back,			--奇境

	EQ_HECHENG = TabIndex.other_compose_eq_hecheng_one,       -- 男性合成
	EQ_HECHENG_TWO = TabIndex.other_compose_eq_hecheng_two,        -- 女性合成
	EQ_HECHENG_THREE = TabIndex.other_compose_eq_hecheng_three,   -- 饰品合成
	EQ_HECHENG_SHENSHOW = TabIndex.other_compose_eq_hecheng_shenshou,       -- 神兽合成
	EQ_HECHENG_TIANSHEN = TabIndex.other_compose_eq_hecheng_tianshen,       -- 天神装备合成

	LING_CHONG = TabIndex.other_compose_eq_hecheng_lingchong,        -- 灵宠合成
	XIAO_GUI = TabIndex.other_compose_eq_hecheng_xiaogui,        -- 小鬼合成

	Tian_Shen = TabIndex.other_compose_tianshen,      			-- 天神 合成
	XIAO_GUI2 = TabIndex.other_compose_xiaogui,  			    -- 小鬼2合成
	Pet = TabIndex.other_compose_pet,					 	    -- 合成 宠物
	XianWa = TabIndex.other_compose_kid,						-- 合成 仙蛙
	LongHun = TabIndex.other_compose_longhu,					-- 合成 龙魂
 	SHEN_BING = TabIndex.other_compose_shenbing,					-- 合成 神兵
	Kun = TabIndex.other_compose_kun,							-- 合成 鲲

	SHENGJIE = TabIndex.other_compose_shengjie,					-- 仙器 升阶
	SHENGXING = TabIndex.other_compose_shengxing,				-- 仙器 升星
	BEAST = TabIndex.other_compose_beast,
}

local TOGGLE_MAX_COMPOSE = 16
local ITEM_MAX_HEIGHT = 54

local LIST_CELL_COUNT = 28		--list最大格子数量

local Equip_Sucee_Effect_Pos = Vector2(0, 49)
local Item_Sucee_Effect_Pos = Vector2(100,-64)

local daoju_effect = {}


function NewComposeView:__init(view_name)
	self.view_name = GuideModuleName.Compose
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self:LoadConfig()
	self.is_safe_area_adapter = true
	self.default_index = COMPOSE_TYPE.EQ_HECHENG
	self.cur_select_index = 1
	self.cur_baoshi_select_index = 1
	self.tab_sub = {true, true, true, true, true, true, true}

	self.remind_tab = {
		{RemindName.Compose_Male_Equip, RemindName.Compose_Female_Equip, RemindName.Compose_Jewelry_Equip, RemindName.Compose_ShenShou_Equip},
		{RemindName.Compose_XianQi_ShengJie, RemindName.Compose_XianQi_ShengXing},
		{RemindName.Compose_TianShen, RemindName.Compose_XiaoGui, RemindName.Compose_LongHun, RemindName.Compose_ShenBing, RemindName.Compose_Beast},
		{RemindName.Compose_TaoZhuang, RemindName.Compose_DaoJu, RemindName.Compose_XianQi_Stuff, RemindName.Compose_Background},
	}
	self.need_check_redPoint = true

	self.timer_list = {}
	self.item_data = {}
	daoju_effect = nil

	self.datachange_callback = BindTool.Bind1(self.OnItemDataChange, self)
	self.change_callback = BindTool.Bind(self.OnRoleAttrValueChange, self)
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	self.activity_change_callback = BindTool.Bind(self.HandleActChange, self)

	-- 多脚本公用变量
	self.equip_selected_item_data = nil
	self.need_visble_callback = true
	self.open_tween = nil
	self.close_tween = nil
	self.first_open_compose_equip = true
end

function NewComposeView:__delete()
end

function NewComposeView:LoadConfig()
	local bundle_name = "uis/view/compose_ui_prefab"
	local temp_list_1 = {COMPOSE_TYPE.EQ_HECHENG, COMPOSE_TYPE.EQ_HECHENG_TWO, COMPOSE_TYPE.EQ_HECHENG_THREE,
						COMPOSE_TYPE.EQ_HECHENG_SHENSHOW}

	local temp_list_2 = {COMPOSE_TYPE.EQ_HECHENG, COMPOSE_TYPE.EQ_HECHENG_TWO, COMPOSE_TYPE.EQ_HECHENG_THREE,
						COMPOSE_TYPE.EQ_HECHENG_SHENSHOW}

	local temp_list_3 = {COMPOSE_TYPE.DAOJU, COMPOSE_TYPE.TOAZHUANG, COMPOSE_TYPE.ACT_COMPOSE,
						COMPOSE_TYPE.Tian_Shen, COMPOSE_TYPE.XIAO_GUI2,
						COMPOSE_TYPE.SHIZHUANG, COMPOSE_TYPE.SHEN_BING, TabIndex.other_compose_beast,
						COMPOSE_TYPE.BACKGROUND}
	local temp_list_4 = {COMPOSE_TYPE.SHENGJIE, COMPOSE_TYPE.SHENGXING}
	local temp_list_5 = {COMPOSE_TYPE.LongHun}

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "HorizontalTabbar", {vector2 = Vector2(594, -90)})
	self:AddViewResource(temp_list_1, bundle_name, "layout_hecheng_show_EQ")
	-- self:AddViewResource(COMPOSE_TYPE.EQ_HECHENG_THREE, bundle_name, "layout_hecheng_special_show_EQ")
	self:AddViewResource(temp_list_2, bundle_name, "layout_hecheng_EQ")
	self:AddViewResource(temp_list_2, bundle_name, "layout_hecheng_common_btn")
	self:AddViewResource(temp_list_3, bundle_name, "layout_ui_composecfg1")
	self:AddViewResource(temp_list_4, bundle_name, "layout_compose_xianqi")
	self:AddViewResource(temp_list_5, bundle_name, "layout_ui_composecfg2")
	self:AddViewResource(TabIndex.other_xianqi_stuff, bundle_name, "layout_ui_compose_xqs")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function NewComposeView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg4")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	local menu_list = ComposeWGData.Instance:GetMenuList() 					--获取菜单列表【道具】
	local special_menu_list = ComposeWGData.Instance:GetSpecialComposeMenu() 	--获取特殊菜单列表
	if nil == menu_list or nil == IsEmptyTable(special_menu_list) then
		return
	end

	local tabbar_name = {}
	local tabbar_name2 = {}
	for i = 1, #menu_list do
		table.insert(tabbar_name, menu_list[i].name)
	end

	--添加进特殊合成里面
	for k, v in ipairs(special_menu_list) do
		table.insert(tabbar_name2, v.name)
	end

	self.tabbar = Tabbar.New(self.node_list)
	self.tabbar:SetVerTabbarIconStr("hecheng_view")
	self.tabbar:Init(Language.Compose.TabGroup1, {Language.Compose.TabGroup2, Language.Compose.TabGroup3, tabbar_name2, tabbar_name}, nil, nil, self.remind_tab)
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.TabbarClickCallBack, self))
	self.tabbar:SetCreateHorCallBack(function (hor_index)
		self.tabbar:ChangeToIndex(hor_index)
	end)
	--功能开启
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Compose, self.tabbar)

	self.alert = Alert.New()
	self.alert:SetCheckBoxText(Language.Common.NoticeIsClose)
	self.alert:SetCurLoginNoRemindKey("compose_login_remind")
	self.alert:SetLableString(Language.Compose.Dec)
	self.alert:SetShowCheckBox(true)
	self.alert:SetCheckBoxDefaultSelect(false)

	self.alert2 = Alert.New()
	self.alert2:SetCheckBoxText(Language.Common.NoticeIsClose)
	self.alert2:SetCurLoginNoRemindKey("compose_login_remind")
	self.alert2:SetLableString(Language.Compose.Dec)
	self.alert2:SetShowCheckBox(true)
	self.alert2:SetCheckBoxDefaultSelect(false)

	self.money_bar = MoneyBar.New()
	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)

	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
	RoleWGData.Instance:NotifyAttrChange(self.change_callback, {"coin", "prof"})
	ItemWGData.Instance:NotifyDataChangeCallBack(self.datachange_callback)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Compose, self.get_guide_ui_event)

	self:HandleActTab()

	local bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.NOT_HAVE_ROLEMODEL)
	if self.node_list.RawImage_tongyong then
	 	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function ()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function NewComposeView:LoadIndexCallBack(index)
	if self.node_list.btn_bg_checkbox then
		self.node_list.btn_bg_checkbox:SetActive(true)
		self.node_list.btn_zuida:SetActive(true)
	end

	if index == COMPOSE_TYPE.DAOJU or index == COMPOSE_TYPE.TOAZHUANG
		or index == COMPOSE_TYPE.ACT_COMPOSE or index == COMPOSE_TYPE.XIAO_GUI
		or index == COMPOSE_TYPE.Tian_Shen or index == COMPOSE_TYPE.XIAO_GUI2
		or index == COMPOSE_TYPE.SHEN_BING or index == TabIndex.other_compose_beast
		or index == COMPOSE_TYPE.SHIZHUANG 
		or index == COMPOSE_TYPE.BACKGROUND then
		---[[
		--连线特效  暂时不用
		-- if not daoju_effect then
		-- 	daoju_effect = {}
		-- 	local effect_root = self.node_list.effect_root_normal_compose
		-- 	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_HC2xian)
		-- 	local async_loader = AllocAsyncLoader(self, "compose_daoju_effect")
		-- 	async_loader:SetParent(effect_root.transform)
		-- 	async_loader:SetIsUseObjPool(true)
		-- 	async_loader:Load(bundle_name, asset_name,
		-- 	function (obj)
		-- 		local temp_list = {}
		-- 		for i = 1, 6 do
		-- 			local line = obj.transform:Find("line_" .. i)
		-- 			temp_list[i] = line and line.gameObject
		-- 		end
		-- 		daoju_effect = temp_list
		-- 		self:FlushDaojuEffect()
		-- 	end)
		-- end
		--]]
		if self.stuff_cell_list == nil then
			self.stuff_cell_list = {}
			for i = 1, 4 do
				local cell =  ItemCell.New(self.node_list["ph_cell_pos_"..i])
				-- cell:SetNeedItemGetWay(true)
				self.stuff_cell_list[i] = cell
				cell:SetCellBgEnabled(false)
			end
			--按钮监听
			self:ComposeRegisterAllEvents()
			--下拉菜单大按钮
			self.cur_product_item_data = nil

			if nil == self.accordion_group then
				self.cell_group = {}
				self.accordion_group = {}
				local tab_index = self:GetShowIndex()
				for i=1,TOGGLE_MAX_COMPOSE do
					self.accordion_group[i] = {}
					self.accordion_group[i].text_component = self.node_list["BtnText" .. i]
					self.accordion_group[i].list = self.node_list["List" .. i]
					self:LoadLeftCell(i, tab_index)

					if self.node_list["SelectBtn" .. i] then
						self.node_list["SelectBtn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickParentHandle, self, i))
					end
				end
			end
			self.product_cell = ItemCell.New(self.node_list["ph_product_grid"])
			self.product_cell:SetIsShowTips(true)
		end

	elseif index == COMPOSE_TYPE.EQ_HECHENG or
		index == COMPOSE_TYPE.EQ_HECHENG_TWO or
		index == COMPOSE_TYPE.EQ_HECHENG_THREE or
		index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		self:CreateEquipHeChengScheme()
		self:InitHeChengView()

	elseif index == COMPOSE_TYPE.SHENGJIE or
		-- index == COMPOSE_TYPE.SHENGPIN or
		-- index == COMPOSE_TYPE.ZHENLIAN or
		index == COMPOSE_TYPE.SHENGXING then
		self:InitComposeXianQiView()
	elseif index == COMPOSE_TYPE.LongHun then
		self:InitLongHunCompose()
	elseif index == TabIndex.other_xianqi_stuff then
		self:InitXQSComposeView()
	end
end

function NewComposeView:TabbarClickCallBack(index)
	local open_index = index
	if self.first_open_compose_equip then
		if index == COMPOSE_TYPE.EQ_HECHENG or index == COMPOSE_TYPE.EQ_HECHENG_TWO
			or index == COMPOSE_TYPE.EQ_HECHENG_THREE
			or index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then

			local flag = RoleWGData.Instance:GetRoleSex() == GameEnum.MALE
			open_index = flag and COMPOSE_TYPE.EQ_HECHENG or COMPOSE_TYPE.EQ_HECHENG_TWO
		end
	end

	self:ChangeToIndex(open_index)
end

function NewComposeView:SetSuccessRootEnable(is_enable)
	if self.node_list["success_root"] then
		self.node_list["success_root"]:SetActive(is_enable)
	end
end

function NewComposeView:ReleaseCallBack()
	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end
	self:ReleaseLHHeCheng()
	self:HeChengDeleteMe()
	self:DeleteComposeXianQiView()
	self:ReleaseXQSComposeView()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.stuff_cell_list ~= nil then
		for k,v in ipairs(self.stuff_cell_list) do
			v:DeleteMe()
		end
		self.stuff_cell_list = nil
	end

	if self.cell_group then
		for k,v in pairs(self.cell_group) do
			for k1,v1 in pairs(v) do
				v1:DeleteMe()
			end
			self.cell_group[k] = nil
		end
		self.cell_group = nil
	end

	self.accordion_group = nil

	if nil ~= self.product_cell then
		self.product_cell:DeleteMe()
		self.product_cell = nil
	end

	if self.ningju_event ~= nil then
		GlobalEventSystem:UnBind(self.ningju_event)
		self.ningju_even = nil
	end

	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end

	if self.alert2 then
		self.alert2:DeleteMe()
		self.alert2 = nil
	end

	RoleWGData.Instance:UnNotifyAttrChange(self.change_callback)

	self.item_data = nil
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)

	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Compose, self.get_guide_ui_event)
	end

	daoju_effect = nil

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self.load_compose_cell_complete = nil
	self.first_open_compose_equip = true
	self.jump_to_equip_data = nil
end

function NewComposeView:OpenCallBack()

end

-- 合成事件注册
function NewComposeView:ComposeRegisterAllEvents()
	if self.node_list.btn_zuida and self.node_list.btn_hecheng and self.node_list.btn_bg_checkbox then
		XUI.AddClickEventListener(self.node_list.btn_zuida, BindTool.Bind1(self.OnGetMaxNumHandler, self))
		XUI.AddClickEventListener(self.node_list.btn_hecheng, BindTool.Bind1(self.OnComposeHandler, self))
		XUI.AddClickEventListener(self.node_list.btn_bg_checkbox, BindTool.Bind1(self.OnCheckBoxHandler, self))
	end
end

function NewComposeView:OnClickParentHandle(index)
	if index == nil then
		return
	end

	if self.cell_group ~= nil and self.cell_group[index] ~= nil then
		self.cell_group[index][1]:OnClickItem(true)
		self.cell_group[index][1].view.toggle.isOn = true
	end

	if self.node_list["SelectBtn" .. index].accordion_element.isOn == true then
		self.cur_baoshi_select_index = index
	end
end

--宝石特殊处理 选中当前满足合成条件的那一标签
function NewComposeView:JumpBaoShiCompose(big_type)
	local num,list = ComposeWGData.Instance:GetComposeBaoShiBySubType(big_type)
	if next(list) and big_type == tonumber(list.sub_type) then
		self.cell_group[big_type][1].view.toggle.isOn = false
		self.cell_group[big_type][tonumber(list.index)]:OnClickItem(true)
		self.cell_group[big_type][tonumber(list.index)].view.toggle.isOn = true
	else
		self.cell_group[big_type][1]:OnClickItem(true)
		self.cell_group[big_type][1].view.toggle.isOn = true
	end
end

function NewComposeView:OnShenShouItemChange()
	local index = self.show_index
    if not self:IsLoadedIndex(index) then
    	return
    end
    if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		self:FlushHechengMaterialNum(nil,true)
		self:FlushEquipHeChengList()
    end
end

-- 背包数据发生变化通知
function NewComposeView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local index = self.show_index
    if not self:IsLoadedIndex(index) then
    	return
    end

	if index == COMPOSE_TYPE.DAOJU or index == COMPOSE_TYPE.TOAZHUANG
		or index == COMPOSE_TYPE.ACT_COMPOSE or index == COMPOSE_TYPE.Tian_Shen
		or index == COMPOSE_TYPE.XIAO_GUI2 or index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW
        or index == COMPOSE_TYPE.SHEN_BING or index == TabIndex.other_compose_beast
		or index == COMPOSE_TYPE.BACKGROUND then
		-- self:ShowFlushCompose(index,old_num, new_num)
		--如果仅显示足够材料,则需要刷新左侧下拉菜单
		if ComposeWGData.Instance:GetIsShowEnough() then
			self:ShowFlushCompose(index, nil, nil, true)
		else
			if self.cur_product_item_data then
				self:ReLoadStuffInfo(self.cur_product_item_data)
				self:FlushRemind()
			end
		end
	elseif index == COMPOSE_TYPE.EQ_HECHENG or index == COMPOSE_TYPE.EQ_HECHENG_TWO
		or index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
			self:FlushHechengMaterialNum(change_item_id)
			self:FlushEquipHeChengList()
		end
		self:FlushHechengCurEquipment()
		self:FlushEQHCOnceRemind()
	elseif index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		self:FlushHechengMaterialNum(change_item_id)
	elseif index == COMPOSE_TYPE.SHENGJIE or index == COMPOSE_TYPE.SHENGXING then
		self:FlushComposeXQEquipList()
	elseif index == COMPOSE_TYPE.LongHun then
		self:FlushHeCheng()
	elseif index == TabIndex.other_xianqi_stuff then
		if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
		(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
			self:FlushXQSCRemind()
		end
	end
end

function NewComposeView:OnItemComposeResult()

end

function NewComposeView:LoadLeftCell(index, tab_index)
	local res_async_loader = AllocResAsyncLoader(self, "new_hecheng_item" .. index)
	res_async_loader:Load("uis/view/equipment_suit_ui_prefab", "equipment_hecheng_item", nil, function(new_obj)
		local item_vo = {}
		for i=1, LIST_CELL_COUNT do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.accordion_group[index].list.transform, false)
			obj:GetComponent("Toggle").group = self.accordion_group[index].list.toggle_group
			local item_render = ComposeItemRender.New(obj)
			item_render.parent_view = self
			item_vo[i] = item_render
			if index == TOGGLE_MAX_COMPOSE and i == LIST_CELL_COUNT then
				self.load_compose_cell_complete = true
			end
		end
		self.cell_group[index] = item_vo
		if self.load_compose_cell_complete then
			self:ShowFlushCompose(tab_index, self:GetTranferParentIndex(), self:GetTranferChildIndex())
		end
	end)
end

function NewComposeView:FlushRightItem(item)
	if item == nil then
        return
    end

    local data = item:GetData()
    if data == nil then
        return
    end
	self.cur_product_item_data = data
	local item_config = ItemWGData.Instance:GetItemConfig(self.cur_product_item_data.product_id)
	self.node_list["current_name"].text.text = ToColorStr(item_config.name, ITEM_COLOR[item_config.color])  --当前选中的名字显示 
	
	self:ReLoadProductInfo(data)
	self:ReLoadStuffInfo(data)
end

function NewComposeView:FlushRemind()
	if not self.compose_acc_data then
		return
	end

	local big_type = math.floor(self.show_index / 10)
	for i=1, TOGGLE_MAX_COMPOSE do
		if self.compose_acc_data[i] ~= nil then
			local flag = ComposeWGData.Instance:GetParentRemind(big_type, self.compose_acc_data[i].menu_type, self.compose_acc_data[i].id)
			if not self.node_list["prop_remind" .. i] then
				print_log("remind node is nil",big_type,i)
			else
				self.node_list["prop_remind" .. i]:SetActive(flag)
			end

			if self.cell_group and self.cell_group[i] ~= nil then
				for k,v in pairs(self.cell_group[i]) do
					v:FlushRemind()
				end
			end
		end
	end
end

function NewComposeView:ShowFlushCompose(index, tranfer_parent_index, child_index, is_element_change)
	if not self.load_compose_cell_complete then
		return
	end

	local acc_data_list
	local big_type = math.floor(index / 10)
	if index == COMPOSE_TYPE.Tian_Shen or index == COMPOSE_TYPE.XIAO_GUI2
		or index == COMPOSE_TYPE.SHEN_BING or index == TabIndex.other_compose_beast then
		local menu_list = ComposeWGData.Instance:GetSpecialMenu(index % 10)
		acc_data_list = menu_list and menu_list.sub_menu
	else
		local menu_list = ComposeWGData.Instance:GetMenu(index % 10)
		acc_data_list = menu_list and menu_list.sub_menu
	end

	if acc_data_list == nil then
		return
	end

	self.compose_acc_data = acc_data_list

	for i = 1, TOGGLE_MAX_COMPOSE do
		if is_element_change == nil then
			self.node_list["SelectBtn" .. i].accordion_element.isOn = false
			self.node_list["SelectBtn" .. i]:SetActive(i<= #self.compose_acc_data)
			self.node_list["List" .. i]:SetActive(false)
		end

		if self.cell_group[i] ~= nil then
			for k,v in pairs(self.cell_group[i]) do
				v:SetActive(i <= #self.compose_acc_data)
			end

			if nil ~= self.compose_acc_data[i] then
				local data = self.compose_acc_data[i]
				self.node_list["BtnText" .. i].text.text = data.name
				self.node_list["BtnTextHL" .. i].text.text = data.name
			end
		end
	end

	local parent_value = nil
	for i = 1, TOGGLE_MAX_COMPOSE do
		if self.cell_group[i] ~= nil and nil ~= self.compose_acc_data[i] then
			local data, first_parent_value = ComposeWGData.Instance:GetChildData(big_type, self.compose_acc_data[i].menu_type, self.compose_acc_data[i].id)
			if parent_value == nil then
			 	parent_value = first_parent_value
			end

			for k,v in pairs(self.cell_group[i]) do
				v:SetActive(k <= #data )
				if k <= #data then
					v:SetData(data[k])
				end
			end

			if is_element_change and ComposeWGData.Instance:GetIsShowEnough() then
				self.node_list["List" .. i].layout_element.preferredHeight = #data * ITEM_MAX_HEIGHT
			end
		end
	end

	child_index = child_index or 1
	for i = 1, TOGGLE_MAX_COMPOSE do
		if self.cell_group[i] ~= nil then
			local tmp_parent_index = i
			local child_data = ComposeWGData.Instance:GetChildData(big_type, self.compose_acc_data[i].menu_type, i)
			if tranfer_parent_index ~= nil then
				tmp_parent_index = tranfer_parent_index
				child_data = ComposeWGData.Instance:GetChildData(big_type, self.compose_acc_data[i].menu_type, tmp_parent_index)
				self:RealSelectBtn(tmp_parent_index, child_index, child_data)
				break
			elseif parent_value ~= nil and ComposeWGData.Instance:GetIsShowEnough() then
				tmp_parent_index = parent_value
				child_data = ComposeWGData.Instance:GetChildData(big_type, self.compose_acc_data[i].menu_type, tmp_parent_index)
				self:RealSelectBtn(tmp_parent_index, child_index, child_data)
				break
			end

			local flag = self:RealSelectBtn(i, child_index, child_data)
			if flag then
				break
			end
		end
	end
	self:FlushRemind()
end

function NewComposeView:RealSelectBtn(tmp_parent_index, tranfer_child_index, child_data)
	--配置表得index  不一定是数据中的具体的index
	for k, v in pairs(self.compose_acc_data) do
		if v.id == tmp_parent_index then
			tmp_parent_index = k
			break
		end
	end

	for k, v in pairs(child_data) do
		if v.child_type == tranfer_child_index then
			tranfer_child_index = k
			break
		end
	end

	if not self.node_list["SelectBtn" .. tmp_parent_index]:GetActive() then
		self.node_list["SelectBtn" .. tmp_parent_index]:SetActive(true)
	end

	if self.node_list["SelectBtn" .. tmp_parent_index]:GetActive() then
		self.node_list["SelectBtn" .. tmp_parent_index].accordion_element.isOn = true

		if tranfer_child_index then
			tranfer_child_index = tranfer_child_index or 1
			GlobalTimerQuest:AddDelayTimer(function ()
				if self.cell_group and self.cell_group[tmp_parent_index] and self.cell_group[tmp_parent_index][tranfer_child_index] then
					local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.canvas.worldCamera,
											self.cell_group[tmp_parent_index][tranfer_child_index].view.transform.position)

					local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
								self.node_list.Content1.rect, screen_pos_tbl, UICamera, Vector2(0, 0))
					local per
					local content_size_y = self.node_list.Content1.rect.sizeDelta.y
					local togglesv_size_y = self.node_list.ToggleSV.rect.sizeDelta.y
					if content_size_y + local_position_tbl.y < togglesv_size_y then
						per = 0
					else
						local rect = content_size_y - self.node_list.ToggleSV.rect.sizeDelta.y
						per = (content_size_y + local_position_tbl.y - togglesv_size_y + 70)  / rect
					end
				
					per = math.min(per, 1)
					self.node_list.ToggleSV.scroll_rect.verticalNormalizedPosition = per			
					-- local flag = UnityEngine.RectTransformUtility.RectangleContainsScreenPoint(self.node_list.ToggleSV.rect,
					-- 						screen_pos_tbl, self.canvas.worldCamera)
					-- if not flag then self.node_list.ToggleSV.scroll_rect.verticalNormalizedPosition = 0 end
				end
				self:SetTranferChildIndex(nil)
			end, 0.5)
		end

		if self.cell_group and self.cell_group[tmp_parent_index] and self.cell_group[tmp_parent_index][tranfer_child_index] then
			self.cell_group[tmp_parent_index][tranfer_child_index]:OnClickItem(true)
			self.cell_group[tmp_parent_index][tranfer_child_index].view.toggle.isOn = true
		end
		return true
	end
	return false
end

function NewComposeView:ShowIndexCallBack(index)
	TipWGCtrl.Instance:ForceHideEffect()

	self:SetTranferParentIndex(nil)
	self:SetTranferChildIndex(1)
	self.xianqi_default_index = nil

	local remind_tab_list = ComposeWGData.Instance:GetAIsRmindTab(index)
	local index_type = index % 10
	local big_type = math.floor(index / 10)
	if remind_tab_list then
		if remind_tab_list.big_type == 2 then
			self.xianqi_default_index = remind_tab_list.sub_type
		else
			if remind_tab_list.big_type == big_type then
				local real_index = ComposeWGData.Instance:GetRealJumpIndexByType(remind_tab_list)
				self:SetTranferParentIndex(remind_tab_list.sub_type)
				self:SetTranferChildIndex(real_index)
				self.is_frist_open = false
			end
		end
	end

	if self.show_index == COMPOSE_TYPE.TOAZHUANG then
		-- 套装石头根据性别默认选中对应的
		local sex = RoleWGData.Instance:GetRoleSex()
		local def_index = sex == GameEnum.FEMALE and 2 or 1
		self:SetTranferParentIndex(1)
		self:SetTranferChildIndex(def_index)
	elseif self.show_index == COMPOSE_TYPE.XIAO_GUI2 then
		local sub_type, child_type = ComposeWGData.Instance:GetXiaoGuiComposeJumpTabIdx()
		self:SetTranferParentIndex(sub_type)
		self:SetTranferChildIndex(child_type)
	end

	self.tab_index = index

	if index == COMPOSE_TYPE.DAOJU or index == COMPOSE_TYPE.TOAZHUANG
		or index == COMPOSE_TYPE.ACT_COMPOSE or index == COMPOSE_TYPE.XIAO_GUI
		or index == COMPOSE_TYPE.Tian_Shen or index == COMPOSE_TYPE.XIAO_GUI2
		or index == COMPOSE_TYPE.SHEN_BING
		or index == COMPOSE_TYPE.SHIZHUANG
		or index == COMPOSE_TYPE.BACKGROUND
		or index == TabIndex.other_compose_beast then
		ComposeWGData.Instance:SetIsShowEnough(false)
		self.node_list.img_checkbox_hook:SetActive(false)

		self:ShowFlushCompose(index, self:GetTranferParentIndex(), self:GetTranferChildIndex())

	elseif index == COMPOSE_TYPE.EQ_HECHENG
		or index == COMPOSE_TYPE.EQ_HECHENG_TWO
		or index == COMPOSE_TYPE.EQ_HECHENG_THREE
		or index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		--需求 【合成】选中装备页签的时候，合成界面需要默认选择自身职业的装备的当前等级对应的阶数的页签
		local flag = RoleWGData.Instance:GetRoleSex() == GameEnum.MALE
		local flag_index = flag and COMPOSE_TYPE.EQ_HECHENG or COMPOSE_TYPE.EQ_HECHENG_TWO
		self:ShowEQHCSpliteToggle()
		if index == flag_index then
			local child_index = 1
			local cfg = EquipWGData.Instance:GetRoleCanEquipMaxOrder()
			local max_order,min_order = EquipmentWGData.Instance:GetEquinHeChengShowOrder()
			local max_index = max_order - min_order + 1
			if cfg then
				child_index = cfg.equip_order > min_order and (cfg.equip_order - min_order + 1) or 1
				child_index = child_index >= max_index and max_index or child_index
			else
				child_index = max_index
			end
			self:SetTranferChildIndex(child_index)
		end

	elseif index == COMPOSE_TYPE.LongHun then
		self:ShowLongHunCallBack()
	elseif index == COMPOSE_TYPE.SHENGJIE or index == COMPOSE_TYPE.SHENGXING then
		self:ComposeXianQiShowIndexCallBack(index)
	end

	--处理活动页签显隐藏
	self:HandleActTab()
end

function NewComposeView:HandleActChange(act_type)
	local _type = ComposeWGData.Instance:GetTemporaryActivityType()
	if act_type == _type then
		self:HandleActTab()
	end
end

function NewComposeView:HandleActTab()
	if self.tabbar then
		local act_type = ComposeWGData.Instance:GetTemporaryActivityType()
		local is_show = ActivityWGData.Instance:GetActivityIsOpen(act_type)
		-- self.tabbar:SetToggleVisible(COMPOSE_TYPE.DAOJU, true) --垂直的页签 受横的影响，这里加一个设置为True
		-- self.tabbar:SetToggleVisible(COMPOSE_TYPE.ACT_COMPOSE, is_show)
		-- if not is_show and self.show_index == COMPOSE_TYPE.ACT_COMPOSE then
		-- 	self:ChangeToIndex(COMPOSE_TYPE.DAOJU)
		-- end
	end
end

-- 显示合成产品信息
function NewComposeView:ReLoadProductInfo(seq)
	if nil == seq then
		return
	end
	self.item_data = seq
	-- 获取产品信息
	local product_cfg = ItemWGData.Instance:GetItemConfig(seq.product_id)
	if nil ~= product_cfg then
		if self.product_cell then
			self.product_cell:SetData({item_id = product_cfg.id, num = seq["product_num"], is_bind = 0})
		end
	end
	if seq.is_shenshi then
		self.node_list.rich_compose_gold_part:SetActive(false)
		self.node_list.compose_btn_content.horizontal_layout_group.childAlignment = UnityEngine.TextAnchor.MiddleCenter
	else
		local coin = RoleWGData.Instance.role_info.coin or 0
		local color = coin >= seq.coin and COLOR3B.GREEN or COLOR3B.PINK
		local str_consume_coin = ToColorStr(CommonDataManager.ConverExpByThousand(coin) .. " / " .. CommonDataManager.ConverExpByThousand(seq.coin),color)
		self.node_list.rich_compose_gold.text.text = str_consume_coin
		self.node_list.rich_compose_gold_part:SetActive(seq.coin > 0)
		self.node_list.compose_btn_content.horizontal_layout_group.childAlignment =
			seq.coin > 0 and UnityEngine.TextAnchor.MiddleRight or UnityEngine.TextAnchor.MiddleCenter
		self.node_list.btn_zuida:SetActive(seq.button_synthesis == 0)
	end
end

function NewComposeView:OnRoleAttrValueChange( key )
	if nil == self.item_data then
		return
	end

	if key == "coin" then
		if self.node_list.rich_compose_gold then
            local coin = RoleWGData.Instance.role_info.coin or 0
            local need_coin = self.item_data.coin or 0
			local color = coin >= need_coin and COLOR3B.GREEN or COLOR3B.PINK
			local str_consume_coin = ToColorStr(CommonDataManager.ConverExpByThousand(coin) .. " / " .. CommonDataManager.ConverExpByThousand(need_coin), color)
			self.node_list.rich_compose_gold.text.text = str_consume_coin
		end
	elseif key == "sex" then
		local flag = RoleWGData.Instance:GetRoleSex() == GameEnum.MALE
		self.default_index = flag and COMPOSE_TYPE.EQ_HECHENG or COMPOSE_TYPE.EQ_HECHENG_TWO
	end
end

-- 显示合成材料信息
function NewComposeView:ReLoadStuffInfo(seq)
	if nil == seq or IsEmptyTable(self.stuff_cell_list) then
		return
	end
	for k,v in ipairs(self.stuff_cell_list) do
		v:SetFlushCallBack(function()
			if v.data and v.data.item_id then
				v:SetRightBottomTextVisible(v.data.item_id > 0)
				if k ~= 1 then
					self.node_list["nor_img_lock_"..k]:SetActive(v.data.item_id <= 0)
				end

				local item_count = 0
				local is_xiaogui = ItemWGData.GetIsXiaogGui(seq["stuff_id_"..k])
				if is_xiaogui then
					item_count = ItemWGData.Instance:GetItemNumInBagById(seq["stuff_id_"..k])
					local has_xiaogui = EquipWGData.Instance:GetmpGuardInfoByid(seq["stuff_id_"..k])
					item_count = has_xiaogui and item_count + 1 or item_count
				else
					item_count = ItemWGData.Instance:GetItemNumInBagById(seq["stuff_id_"..k])
				end

				local color = item_count >= v.data.item_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
				v:SetRightBottomText("<color="..color..">"..item_count.."/"..v.data.item_num.."</color>")
			end
		end)

		v:SetData({item_id = seq["stuff_id_" .. k], item_num = seq["stuff_count_" .. k]})
	end
	self:FlushDaojuEffect()
end

function NewComposeView:FlushDaojuEffect()
	if not IsEmptyTable(self.stuff_cell_list) and not IsEmptyTable(daoju_effect) then
		for k, v in pairs(daoju_effect) do
			v:SetActive(false)
		end

		local data_list = self.stuff_cell_list
		local mark_list = {false,false,false,false}
		for i=1,4 do
			if data_list[i] and data_list[i].data and data_list[i].data.item_id then
				mark_list[i] = data_list[i].data.item_id > 0
			else
				mark_list[i] = false
			end

			if daoju_effect[i] then
				daoju_effect[i]:SetActive(mark_list[i])
			end
		end

		if daoju_effect[5] then
			daoju_effect[5]:SetActive(mark_list[2] or mark_list[4])
		end

		if daoju_effect[6] then
			daoju_effect[6]:SetActive(mark_list[1] or mark_list[3])
		end
	end
end

-- 获取可合成的最大数量
function NewComposeView:OnGetMaxNumHandler()
	if nil == self.cur_product_item_data then
		return
	end

	local data = self.cur_product_item_data
	local product_id = data.product_id

	--仙娃合成特殊处理
	local baby_type, baby_id, baby_cfg = MarryWGData.Instance:GetBabyTypeItemId(product_id)
	if baby_type ~= -1 then
		local baby_active = MarryWGData.Instance:GetBabyActiveByItemId(product_id)
		if baby_active then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Compose.BabyNotCompose)
			return
		end
	end

    local select_seq_id = data.producd_seq or 0
	local max_num = ComposeWGData.Instance:ComposeMaxNum(select_seq_id)
	local item_config = ItemWGData.Instance:GetItemConfig(product_id)
	if item_config == nil then
		return
	end
	max_num = max_num > item_config.pile_limit and item_config.pile_limit or max_num 
	local ok_func = BindTool.Bind(self.SendCompos, self, select_seq_id, max_num, product_id)
	if self.alert then
		self.alert:SetOkFunc(ok_func)
	end

	if ItemWGData.Instance:GetEmptyNum() < 0 then
		--没有空格子
		local bag_num = ItemWGData.Instance:GetItemNumInBagById(item_config.id)
		local tmp_num = bag_num % item_config.pile_limit
		max_num = max_num - tmp_num
	end

	if 0 >= max_num then
		local id = ComposeWGData.Instance:GetLackOfGoodsID(select_seq_id)
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = id})
		return
	end

	if ComposeWGData.Instance:IsHaveBindItem(self.cur_product_item_data) and not self.alert:GetIsNeedOpenViewFlag() then
		self.alert:Open()
	else
		self:SendCompos(select_seq_id, max_num, product_id)
	end
end

-- 合成事件
function NewComposeView:OnComposeHandler()
	local num = 1
	num = tonumber(num) or 0
	if num <= 0 or nil == self.cur_product_item_data then
		return
	end

	local data = self.cur_product_item_data
	local product_id = data.product_id
	--仙娃合成特殊处理
	local baby_type, baby_id, baby_cfg = MarryWGData.Instance:GetBabyTypeItemId(product_id)
	if baby_type ~= -1 then
		local baby_active = MarryWGData.Instance:GetBabyActiveByItemId(product_id)
		if baby_active then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Compose.BabyNotCompose)
			return
		end
    end

    if TianShenWGData.Instance:CheckIsTianShenEquip(product_id) then
    	TianShenWGCtrl.Instance:SendShenShiOpera(TIANSHEN_SHENSHI_REQ.TIANSHEN_SHENSHI_REQ_COMPOSE, product_id)
		return
    end

	local select_seq_id = data.producd_seq or 0
	local ok_func = BindTool.Bind(self.SendCompos, self, select_seq_id, num, product_id)
	if self.alert2 then
		self.alert2:SetOkFunc(ok_func)
	end

	local id = ComposeWGData.Instance:GetLackOfGoodsID(select_seq_id)
	if not id then
		if ComposeWGData.Instance:IsHaveBindItem(data) and not self.alert2:GetIsNeedOpenViewFlag() then
			self.alert2:Open()
		else
			self:SendCompos(select_seq_id, num, product_id)
		end
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = id})
	end
end

function NewComposeView:SendCompos(select_seq_id, num, item_id)
	local coin = RoleWGData.Instance.role_info.coin or 0
	local need_coin = self.item_data.coin or 0
	if coin < need_coin then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoCoin)
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN})
		return
	end
	ComposeWGCtrl.Instance:SendComposeReq(select_seq_id, num, 0, item_id)
end

-- 复选框点击
function NewComposeView:OnCheckBoxHandler()
	local is_show_enough = ComposeWGData.Instance:GetIsShowEnough()
	if nil == is_show_enough then
		return
	end
	ComposeWGData.Instance:SetIsShowEnough(not is_show_enough)
	self.node_list.img_checkbox_hook:SetActive(not is_show_enough)
	self:ShowFlushCompose(self.tab_index)
end

function NewComposeView:CreateFailingEffect(index)
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.f_hecheng, is_success = false, pos = Equip_Sucee_Effect_Pos})
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

function NewComposeView:FlushEquipHeChengList()
	if self.show_index == COMPOSE_TYPE.EQ_HECHENG
		or self.show_index == COMPOSE_TYPE.EQ_HECHENG_TWO
		or self.show_index == COMPOSE_TYPE.EQ_HECHENG_THREE
		or self.show_index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		if self.load_equip_cell_complete == true then
			self:FlushEquipHechengListData()
			self:FlushEQHCTargetItem()
		end
	end
end

--装备合成成功特效
function NewComposeView:CreateEquipSucessEffect()
	if self.node_list["success_root"] then
		self.node_list["success_root"]:SetActive(true)

		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng,
							is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["success_root"]})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

--神兽合成成功
function NewComposeView:CreateSSSucessEffect()
	if self.node_list["success_root"] then
		self.node_list["success_root"]:SetActive(true)

		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng,
							is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["success_root"]})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

--物品合成成功
function NewComposeView:ShowEffect()
	if self.node_list["normal_success_root"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng,
							is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["normal_success_root"]})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

--仙器材料物品合成成功
function NewComposeView:ShowXQSCEffect()
	self:CleanStuffInfo()
	local effect_node = self.node_list["xqs_normal_success_root"]
	if effect_node then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng,
							is_success = true, pos = Vector2(0, 0), parent_node = effect_node})
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

--{to_ui_name = 0, to_ui_param = 0, open_param = 2}
function NewComposeView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
			if v['other_compose_shengjie'] then
				self:ShowXianQiEffect(UIEffectName.s_shengjie)
			end

			if v['other_compose_shengxing'] then
				self:ShowXianQiEffect(UIEffectName.s_shengxing)
			end

			if v["shenshou_item_change"] then
				self:OnShenShouItemChange()
				return
			end

			if index == COMPOSE_TYPE.TOAZHUANG or index == COMPOSE_TYPE.SHIZHUANG
			or index == COMPOSE_TYPE.BACKGROUND then
				if v.open_param and v.sub_view_name then
					self:SetTranferParentIndex(tonumber(v.open_param))
					self:SetTranferChildIndex(tonumber(v.sub_view_name))
				end
				self:ShowFlushCompose(index, self:GetTranferParentIndex(), self:GetTranferChildIndex())
			elseif index == COMPOSE_TYPE.EQ_HECHENG or
				index == COMPOSE_TYPE.EQ_HECHENG_TWO or
				index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW or
				index == COMPOSE_TYPE.EQ_HECHENG_THREE then

				if v.open_param then
					self:SetTranferParentIndex(tonumber(v.open_param))
					self:SetTranferChildIndex(tonumber(v.to_ui_param))
				end

				if not IsEmptyTable(v.jump_to_equip_data) then
					self:SetXianQiEquipJumpData(v.jump_to_equip_data)
				else
					self.jump_to_equip_data = nil
				end

				self:ShowFlushHecheng(index, self:GetTranferParentIndex(), self:GetTranferChildIndex())
				self:FlushEquipHeCheng()
				self:XianQiJump()
			elseif index == COMPOSE_TYPE.DAOJU or index == COMPOSE_TYPE.Tian_Shen
			or index == COMPOSE_TYPE.XIAO_GUI2
			or index == COMPOSE_TYPE.SHEN_BING or index == TabIndex.other_compose_beast then
				if v.open_param and v.open_param ~= 0 then
					self:SetTranferParentIndex(tonumber(v.open_param))
					self:SetTranferChildIndex(tonumber(v.sub_view_name))
				end

				self:ShowFlushCompose(index, self:GetTranferParentIndex(), self:GetTranferChildIndex())
			elseif index == COMPOSE_TYPE.LongHun then
				self:FlushHeCheng()

			elseif index == COMPOSE_TYPE.SHENGJIE or index == COMPOSE_TYPE.SHENGXING then
				self:FlushComposeXQEquipList(v.jump_item_id)
			elseif index == TabIndex.other_xianqi_stuff then
				if v.open_param then
					self:SetTranferParentIndex(tonumber(v.open_param))
					self:SetTranferChildIndex(tonumber(v.to_ui_param))
				end

				self:ShowFlushXQSCompose(self:GetTranferParentIndex(), self:GetTranferChildIndex())
			end
		end

		if k == "longhun_compose_success" then
			self:ShowLongHunSuccess()
		elseif k == "equip_compose_item_change" then
			self:FlushHechengMaterialNum(nil, true)
			self:FlushEquipHeChengList()
		elseif k == "beast_compose_item_change" then
			if self.cur_product_item_data then
				self:ReLoadStuffInfo(self.cur_product_item_data)
				self:FlushRemind()
			end
		end
	end
end

function NewComposeView:GetGuideUiCallBack(ui_name, ui_param)
	local cell = nil
	local click_callback = nil
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == COMPOSE_TYPE.EQ_HECHENG or tab_index == COMPOSE_TYPE.EQ_HECHENG_TWO then
			local flag = RoleWGData.Instance:GetRoleSex() == GameEnum.MALE
			tab_index = flag and COMPOSE_TYPE.EQ_HECHENG or COMPOSE_TYPE.EQ_HECHENG_TWO
			if tab_index == self:GetShowIndex() and self.hecheng_select_item_grid and self.hecheng_select_item_grid:GetCell(1) then
				local cell = self.hecheng_select_item_grid:GetCell(1)
				if next(cell:GetData()) then
					self:OnClickSelectEquipment(cell)
				else
					return
				end
			else
				if tab_index ~= self:GetShowIndex() then
					self:ShowIndex(tab_index)
				end
				return
			end
		end
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return
		end

	elseif ui_name == GuideUIName.ComposeTipText then
		return self.node_list["lbl_materials_tip_EQ"]

	elseif ui_name == GuideUIName.ComposeAddOnekey then
		return self.node_list["btn_hecheng_add_onekey_EQ"], BindTool.Bind1(self.OnEquipComposeOperaAddOnekey, self)

	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function NewComposeView:SetEquipSelectedItemData(value)
	self.equip_selected_item_data = value
end

function NewComposeView:GetEquipSelectedItemData()
	return self.equip_selected_item_data
end

function NewComposeView:SetTranferParentIndex(tranfer_parent_index)
	self.tranfer_parent_index = tranfer_parent_index
end

function NewComposeView:GetTranferParentIndex()
	local parent_index = self.tranfer_parent_index or 1
	parent_index = parent_index > 0 and parent_index or 1
	return parent_index
end

function NewComposeView:SetTranferChildIndex(tranfer_child_index)
	self.tranfer_child_index = tranfer_child_index
end

function NewComposeView:GetTranferChildIndex()
	local child_index = self.tranfer_child_index or 1
	child_index = child_index > 0 and child_index or 1
	return child_index
end

function NewComposeView:SetXianQiEquipJumpData(xianqi_equip_jump_data)
	self.xianqi_equip_jump_data = xianqi_equip_jump_data
end

function NewComposeView:GetXianQiEquipJumpData()
	return self.xianqi_equip_jump_data
end

----------------------------------------------------------------------------
-- ComposeItemRender
----------------------------------------------------------------------------
ComposeItemRender = ComposeItemRender or BaseClass(BaseRender)
function ComposeItemRender:__init()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function ComposeItemRender:OnClickItem(is_on)
	if nil == is_on then
		return
	end

	if true == is_on then
		--这里要把其他所有的全部取消选中
		for k,v in pairs(self.parent_view.cell_group) do
			for k1,v1 in pairs(v) do
				v1:OnSelectChange(false)
			end
		end
		self.parent_view:FlushRightItem(self)
		self:OnSelectChange(true)
	else
		self:OnSelectChange(false)
	end
end

function ComposeItemRender:__delete()
	self.text_name = nil
	self.parent_view = nil
end

function ComposeItemRender:OnFlush()
	if self.data == nil then
		return
	end

	local item_config = ItemWGData.Instance:GetItemConfig(self.data.product_id)
	local item_name = item_config and item_config.name or ""
	self.node_list["text_name"].text.text = item_name
	self.node_list["text_name_hl"].text.text = item_name --高亮时的text
end

function ComposeItemRender:FlushRemind()
	if self.data == nil then
		return
	end

	local flag = self.data.is_shenshi and false or ComposeWGData.Instance:GetIsCanComposeByData(self.data)
	self.node_list["remind"]:SetActive(flag)
end

function ComposeItemRender:OnSelectChange(is_select)
	if nil ~= self.node_list["img_bg"] then
		-- if nil ~= self.node_list["text_name"] then
		-- 	local text_color = is_select and COLOR3B.WHITE or COLOR3B.L_ORANGE
		-- 	self.node_list["text_name"].text.text = ToColorStr(self.node_list["text_name"].text.text, text_color)
		-- end
	elseif nil ~= self.node_list["img_icon"]  and nil~= self.node_list["img_flag"] then
		local is_active = self.node_list["img_icon"]:GetActive()
		self.node_list["img_icon"]:SetActive(not is_active)
		self.node_list["img_flag"]:SetActive(is_active)
	end
end
