GuildAnswerRank = GuildAnswerRank or BaseClass(SafeBaseView)

function GuildAnswerRank:__init()
	self:AddViewResource(0, "uis/view/guild_answer_ui_prefab", "layout_guild_answer")
	self.view_layer = UiLayer.Normal
	self.m_IsBusy = false 
	self.m_IsShow = false
	self.open_tween = nil--UITween.ShowFadeUp
    self.close_tween = nil--UITween.HideFadeUp
    self.is_safe_area_adapter = true
    self.view_name = "GuildAnswerRank"
end

function GuildAnswerRank:__delete()
end

function GuildAnswerRank:ReleaseCallBack()
	if self.guild_answer_list then
		self.guild_answer_list:DeleteMe()
		self.guild_answer_list = nil
	end
	if self.forward_tween then
		self.forward_tween:Kill()
		self.forward_tween = nil
	end
	if self.backwards_tween then
		self.backwards_tween:Kill()
		self.backwards_tween = nil
	end
	self.rect_target = nil
	self.load_complete = nil
	self.is_should_forward = nil
	self.is_should_backward = nil
	if self.chat_close_event then
		GlobalEventSystem:UnBind(self.chat_close_event)
		self.chat_close_event = nil
	end
	self.m_IsBusy = false 
	self.m_IsShow = false
	if self.play_backward_call_back then
		self.play_backward_call_back = nil
	end
end

function GuildAnswerRank:LoadCallBack()
	self.is_hide = false
	self:CreateAnswerList()
	self.rect_target = self.node_list["layout_guild_answer_root"].rect
	-- self.chat_close_event = GlobalEventSystem:Bind(OtherEventType.CHAT_HANDLE,BindTool.Bind(self.ChangeState, self))

	-- self.load_complete = true
	-- self.m_IsShow=true
	-- self.rect_target:DOAnchorPosY(1000, 0.5):SetAutoKill(false)
 --        :SetEase(DG.Tweening.Ease.Linear):Pause():OnComplete(
 --        function()
 --             self.m_IsBusy=false
 --        end
 --        ):OnRewind(
 --        function()
 --            self.m_IsBusy=false
 --        end)
 	self.rect_target.gameObject:SetActive(false)
 	self.load_complete = true
 	if self.is_should_forward then
 		self:DOPlayForward()
 		self.is_should_forward = nil
 	end
	if self.is_should_backward then
		self:PlayBackwards(self.play_backward_call_back)
		self.is_should_backward = nil
	end
end

function GuildAnswerRank:DOPlayForward()
 	self:PlayForward()
end

function GuildAnswerRank:OpenCallBack()
	if not self.load_complete then
		self.is_should_forward = true 
		return
	end
	self.is_open = true
	self:DOPlayForward()
end

function GuildAnswerRank:Close()
	self.is_open = false
	self:DOPlayBackwards(function()
		if not self.is_open then
			self.rect_target.gameObject:SetActive(false)
			SafeBaseView.Close(self)
		end
	end)
end

function GuildAnswerRank:GetOpenState()
	return self.is_open
end

function GuildAnswerRank:PlayForward()
	if not self.rect_target then 
	 	return 
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.GUILD_ANSWER_FB then
		self:DOPlayBackwards()
		return
	end
 	self.rect_target.anchoredPosition = Vector2(0, 1000)
 	self.rect_target.gameObject:SetActive(true)
	self.forward_tween = self.rect_target:DOAnchorPosY(0, 0.5):OnComplete(
        function()
        	self:Flush()
    end)
end

function GuildAnswerRank:DOPlayBackwards(callback)
	if not self.load_complete then
		self.is_should_backward = true
		self.play_backward_call_back = callback
		return
	end
	self:PlayBackwards(callback)
end

function GuildAnswerRank:PlayBackwards(callback)
	if not self.rect_target then
	 	return 
	end

	self.rect_target.anchoredPosition = Vector2(0, 0)
	-- self.rect_target.gameObject:SetActive(true)
	self.backwards_tween = self.rect_target:DOAnchorPosY(1000, 0.5):OnComplete(
		function()
			callback()
			if self.play_backward_call_back then
				self.play_backward_call_back = nil
			end
		end)
end

function GuildAnswerRank:ChangeState()
    -- if self.m_IsBusy then
    --     return
    -- end
    self.m_IsBusy=true

    if self.m_IsShow then
        self.rect_target:DOPlayForward()
    else
        self.rect_target:DOPlayBackwards()
    end
    self.m_IsShow = not self.m_IsShow
end


function GuildAnswerRank:ShowIndexCallBack()
	self:Flush()
end

function GuildAnswerRank:OnFlush()
	local player_info = GuildAnswerWGData.Instance:GetQuestionPlayerInfo()
	local guild_rank_info = GuildAnswerWGData.Instance:GetGuildRankInfo()
	if IsEmptyTable(player_info) then 
		return 
	end

	self.node_list.lbl_guild_exp.text.text = (CommonDataManager.ConverExp(player_info.exp))
	self.node_list.lbl_guild_gongxian.text.text = (player_info.guild_gongxian)
	
	if self.guild_answer_list then
		self.guild_answer_list:SetDataList(guild_rank_info)
	end

	self:FlushMyRankData(guild_rank_info)
end

function GuildAnswerRank:FlushMyRankData(guild_rank_info)
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	local my_data = {}
	local rank_id = -1

	if not IsEmptyTable(guild_rank_info) then
		for i = 1, #guild_rank_info do
			if guild_id == guild_rank_info[i].guild_id then
				my_data = guild_rank_info[i]
				rank_id = i
				break
			end
		end
	end

	if not IsEmptyTable(my_data) then
		if rank_id <= 3 then
			self.node_list.my_guild_rank.text.text = ""
			self.node_list.my_img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp".. rank_id))
			self.node_list.my_img_rank:SetActive(true)
		else
			self.node_list.my_guild_rank.text.text = rank_id
		end

		local num = GuildAnswerWGData.Instance:GetAnswerNumByScore(my_data.guild_score)
		self.node_list.my_guild_score.text.text = num
		self.node_list.my_guild_name.text.text = RoleWGData.Instance.role_vo.guild_name
	else
		self.node_list.my_guild_rank.text.text = "--"
		self.node_list.my_guild_score.text.text = "--"
		self.node_list.my_guild_name.text.text = ToColorStr(Language.Guild.SHNotRank, COLOR3B.C10)
		self.node_list.my_img_rank:CustomSetActive(false)
	end
end

function GuildAnswerRank:CreateAnswerList()
	self.guild_answer_list = AsyncListView.New(GuildAnswerItemRender,self.node_list.ph_guild_list)
end

------------------------------------------
------GuildAnswerItemRender
GuildAnswerItemRender = GuildAnswerItemRender or BaseClass(BaseRender)
function GuildAnswerItemRender:OnFlush()
	if not self.data then return end

	if self.index < 4 then
		self.node_list.lbl_guild_rank.text.text = ""
		self.node_list["img_rank"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp".. self.index))
		self.node_list["img_rank"]:SetActive(true)
	else
		self.node_list["img_rank"]:SetActive(false)
		self.node_list.lbl_guild_rank.text.text = self.index
	end

	local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
	local server_id = self.data.server_id or 1
	local server_str = ""
	if is_cross_server_stage then
		server_str = string.format(Language.Common.ServerIdFormat, server_id)
	end
	local guild_name = server_str .. self.data.guild_name
	self.node_list.lbl_guild_name.text.text = guild_name
	local num = GuildAnswerWGData.Instance:GetAnswerNumByScore(self.data.guild_score)
	self.node_list.lbl_guild_score.text.text = (string.format(Language.GuildAnswer.AnswerPlace, num))--OpenServer.TimeMin
end