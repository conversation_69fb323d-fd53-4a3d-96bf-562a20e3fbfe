﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_LoadRawImageURLWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.LoadRawImageURL), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("url", get_url, set_url);
		<PERSON><PERSON>("autoFitNativeSize", get_autoFitNativeSize, set_autoFitNativeSize);
		<PERSON><PERSON>("autoUpdateAspectRatio", get_autoUpdateAspectRatio, set_autoUpdateAspectRatio);
		<PERSON><PERSON>("autoDisable", get_autoDisable, set_autoDisable);
		<PERSON>.<PERSON>("AutoFitNativeSize", get_AutoFitNativeSize, set_AutoFitNativeSize);
		<PERSON><PERSON>("AutoUpdateAspectRatio", get_AutoUpdateAspectRatio, set_AutoUpdateAspectRatio);
		<PERSON><PERSON>("AutoDisable", get_AutoDisable, set_AutoDisable);
		L.RegVar("URL", get_URL, set_URL);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_url(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			string ret = obj.url;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index url on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoFitNativeSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool ret = obj.autoFitNativeSize;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoFitNativeSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoUpdateAspectRatio(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool ret = obj.autoUpdateAspectRatio;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoUpdateAspectRatio on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoDisable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool ret = obj.autoDisable;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoDisable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoFitNativeSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool ret = obj.AutoFitNativeSize;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoFitNativeSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoUpdateAspectRatio(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool ret = obj.AutoUpdateAspectRatio;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoUpdateAspectRatio on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoDisable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool ret = obj.AutoDisable;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoDisable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_URL(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			string ret = obj.URL;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index URL on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_url(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.url = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index url on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoFitNativeSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.autoFitNativeSize = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoFitNativeSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoUpdateAspectRatio(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.autoUpdateAspectRatio = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoUpdateAspectRatio on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoDisable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.autoDisable = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoDisable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AutoFitNativeSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AutoFitNativeSize = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoFitNativeSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AutoUpdateAspectRatio(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AutoUpdateAspectRatio = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoUpdateAspectRatio on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AutoDisable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AutoDisable = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoDisable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_URL(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.LoadRawImageURL obj = (Nirvana.LoadRawImageURL)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.URL = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index URL on a nil value");
		}
	}
}

