﻿using System;
using System.Collections.Generic;
using System.IO;

namespace Nirvana
{
	/// <summary>
	/// 写出消息封装。
	/// 将待发送数据包装为「4 字节小端长度前缀 + 数据体」的格式，
	/// 并提供对象池以减少 GC 分配。
	/// </summary>
	internal sealed class EnhancedWriteMessage
	{
		/// <summary>
		/// 消息体长度（不含 4 字节长度头）。
		/// </summary>
		public uint Length
		{
			get
			{
				return this.length;
			}
		}

		/// <summary>
		/// 已发送的字节数（用于分片异步发送时的进度记录）。
		/// </summary>
		public int ByteSended { get; set; }

		/// <summary>
		/// 透传上下文（通常存放回调等）。
		/// </summary>
		public object Context { get; set; }

		/// <summary>
		/// 返回当前内部缓冲区（包含 4 字节长度头与数据体）。
		/// </summary>
		public byte[] Buffer
		{
			get
			{
				bool flag = this.stream == null;
				byte[] array;
				if (flag)
				{
					array = null;
				}
				else
				{
					array = this.stream.GetBuffer();
				}
				return array;
			}
		}

		/// <summary>
		/// 当前缓冲区有效数据长度（包含 4 字节长度头与数据体）。
		/// </summary>
		public int BufferLength
		{
			get
			{
				bool flag = this.stream == null;
				int num;
				if (flag)
				{
					num = 0;
				}
				else
				{
					num = (int)this.stream.Length;
				}
				return num;
			}
		}

		/// <summary>
		/// 从对象池获取一个可用的 <see cref="EnhancedWriteMessage"/> 实例。
		/// </summary>
		public static EnhancedWriteMessage Alloc()
		{
			bool flag = EnhancedWriteMessage.pool.Count > 0;
			EnhancedWriteMessage writeMessage;
			if (flag)
			{
				writeMessage = EnhancedWriteMessage.pool.Pop();
			}
			else
			{
				writeMessage = new EnhancedWriteMessage();
			}
			return writeMessage;
		}

		/// <summary>
		/// 归还 <see cref="EnhancedWriteMessage"/> 到对象池（最多缓存 8 个）。
		/// 会重置基本状态（长度/已发字节/上下文）。
		/// </summary>
		public static void Free(EnhancedWriteMessage message)
		{
			bool flag = EnhancedWriteMessage.pool.Count < 8;
			if (flag)
			{
				message.length = 0U;
				message.ByteSended = 0;
				message.Context = null;
				EnhancedWriteMessage.pool.Push(message);
			}
		}

		/// <summary>
		/// 设置要发送的数据体，并写入 4 字节小端序长度头。
		/// 内部使用 <see cref="MemoryStream"/> 以便与 Socket 发送配合。
		/// </summary>
		/// <param name="data">数据体（不包含长度头）。</param>
		public void SetData(byte[] data)
		{
			bool flag = this.stream == null;
			if (flag)
			{
				this.stream = new MemoryStream(4 + data.Length);
			}
			else
			{
				this.stream.Seek(0L, SeekOrigin.Begin);
				this.stream.SetLength(0L);
			}
			uint num = (uint)data.Length;
			// 写入 4 字节小端长度头（与读取端 BitConverter.ToUInt32 对应）
			byte[] array = new byte[]
			{
				(byte)(num & 255U),
				(byte)((num >> 8) & 255U),
				(byte)((num >> 16) & 255U),
				(byte)((num >> 24) & 255U)
			};
			this.stream.Write(array, 0, 4);
			this.stream.Write(data, 0, data.Length);
			this.length = num;
		}

		private const int PoolCount = 8; // 对象池最大缓存数量

		private static Stack<EnhancedWriteMessage> pool = new Stack<EnhancedWriteMessage>(); // 简单栈实现对象池

		private uint length; // 数据体长度

		private MemoryStream stream; // 实际缓冲，布局为 [len(4) | payload]
	}
}
