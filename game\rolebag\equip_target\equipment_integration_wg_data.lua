function EquipTargetWGData:InitEIData()
    self.ei_toggle_list = {}
    self.ei_suit_map_equip_list = {}
    self.ei_suit_equip_map_list = {}
    local cfg = ConfigManager.Instance:GetAutoConfig("equip_jicheng_cfg_auto")
    self.ei_suit_map_cfg = ListToMap(cfg.suit_info, "suit_index", "slot_index")
    self.ei_toggle_show_cfg = ListToMap(cfg.toggle_show, "big_type", "suit_index")
    self.ei_suit_acive_map_cfg = ListToMap(cfg.suit_acive_attr, "suit_index")
    self.ei_suit_level_cfg = ListToMap(cfg.suit_level, "suit_index", "level")
    self.ei_decompose_cfg = ListToMap(cfg.decompose, "item_id")
    self.ei_suit_equip_cfg = ListToMap(cfg.suit_info, "equip_1_1_id")  --(道具激活每个角色都一样)
    self.ei_suit_level_item_cfg = ListToMap(cfg.suit_level, "cost_item_id")
    self.ei_big_toggle_cfg = ListToMapList(cfg.toggle_show, "big_type")
    self.ei_suit_cfg = ListToMapList(cfg.suit_info, "suit_index")
    self.ei_decompose_item_cfg = cfg.decompose

    self.suit_act_flag_list = {}
    self.ei_show_level_list = {}
    self.ei_get_big_type_list = {}

    self.suit_level_list = {}
    for k,v in pairs(cfg.toggle_show) do
        self.ei_show_level_list[v.show_level] = true
        self.ei_show_level_list[v.open_level] = true
        self.ei_get_big_type_list[v.suit_index] = v.big_type
    end

    self.ei_map_all_equip_list = {}
    local function AddMapAllEquipList(item_id, data)
        if not item_id then
            return
        end
        
        self.ei_map_all_equip_list[item_id] = self.ei_map_all_equip_list[item_id] or {}
        self.ei_map_all_equip_list[item_id][data.star] = data
    end

    -- 【职业改动修改】
    for k,v in pairs(cfg.suit_info) do
        AddMapAllEquipList(v["equip_1_1_id"], v)
        AddMapAllEquipList(v["equip_1_2_id"], v)
        AddMapAllEquipList(v["equip_1_3_id"], v)
        AddMapAllEquipList(v["equip_1_4_id"], v)
        AddMapAllEquipList(v["equip_0_1_id"], v)
        AddMapAllEquipList(v["equip_0_3_id"], v)
        AddMapAllEquipList(v["equip_0_4_id"], v)
    end

    RemindManager.Instance:Register(RemindName.EquipmentIntegration, BindTool.Bind(self.GetEIAllRemind, self))
end

function EquipTargetWGData:DeleteEIData()
    RemindManager.Instance:UnRegister(RemindName.EquipmentIntegration)
end

function EquipTargetWGData:SetEIAllInfo(protocol)
    -- 初始化信息
    local item_id = 0
    local sex, prof = RoleWGData.Instance:GetRoleSexProf()
    local key = string.format("equip_%s_%s_id", sex, prof)
    for k,v in pairs(self.ei_suit_map_cfg) do
        self.ei_suit_map_equip_list[k] = {}
        for k1, v1 in pairs(v) do
            item_id = v1[key] or 0
            self.ei_suit_map_equip_list[k][k1] = {item_id = item_id, star = v1.star, cfg = v1}
            self.ei_suit_equip_map_list[item_id] = self.ei_suit_equip_map_list[item_id] or {}
            self.ei_suit_equip_map_list[item_id][v1.star] = v1
        end
    end

    self.suit_act_flag_list = protocol and protocol.suit_act_flag_list or self.suit_act_flag_list
    self.suit_level_list = protocol and protocol.suit_level_list or self.suit_level_list
    self:SetEIToggleList()
end

function EquipTargetWGData:SetEISingleInfo(protocol)
    local suit_index = protocol.suit_index
    local slot_index = protocol.slot_index
    if self.suit_act_flag_list[suit_index] then
        self.suit_act_flag_list[suit_index][slot_index] = 1
    end

    self:SetToggleListRemind(suit_index, slot_index, false)
    self:SetToggleListAct(suit_index, slot_index)
    self:UpdateToggleListSuitRemind(suit_index)
end

--套裝单个信息改变
function EquipTargetWGData:SetEISingleSuitLevelInfo(protocol)
    local suit_index = protocol.suit_index
    local level = protocol.level
    if self.suit_level_list[suit_index] then
        self.suit_level_list[suit_index] = level
    end
    self:UpdateToggleListSuitRemind(suit_index)
end

-- 获取槽位是否激活
function EquipTargetWGData:GetSlotIsAct(suit_index, slot_index)
    return (self.suit_act_flag_list[suit_index] or {})[slot_index] == 1
end

-- 获取套装等级
function EquipTargetWGData:GetSuitLevel(suit_index)
    return self.suit_level_list[suit_index]
end

-- 获取套装是否全激活
function EquipTargetWGData:GetSuitIsAllAct(suit_index)
    local cfg = self.ei_suit_map_cfg[suit_index]
    if not cfg then
        return false
    end

    for k,v in pairs(cfg) do
        if not self:GetSlotIsAct(suit_index, v.slot_index) then
            return false
        end
    end

    return true
end

-- 设置菜单列表
function EquipTargetWGData:SetEIToggleList()
    self.ei_toggle_list = {}
    local level = RoleWGData.Instance:GetAttr("level")
    local cfg = ConfigManager.Instance:GetAutoConfig("equip_jicheng_cfg_auto").toggle_show
    local big_type, suit_index = 0, 0
    for k,v in ipairs(cfg) do
        big_type = v.big_type
        suit_index = v.suit_index
        if suit_index == 0 then
            self.ei_toggle_list[big_type] = {name = v.suit_name, type = big_type, child_list = {}, is_show = false, remind_num = 0, is_all_act = true, quality = v.quality, bg = v.bg}
        else
            if self.ei_toggle_list[big_type] then
                local is_show = level >= v.show_level or self:GetOtherOpenCondition(suit_index)
                local is_open = level >= v.open_level or self:GetOtherOpenCondition(suit_index)
                local is_all_act = self:GetSuitIsAllAct(suit_index)
                local suit_level = self:GetSuitLevel(suit_index)
                local is_max_level = suit_level and IsEmptyTable(self:GetEISuitLevelCfg(suit_index, suit_level + 1))--下级配置
                local remind_num, remind_list = self:GetEISuitRemindNum(suit_index, is_open and (not is_all_act or not is_max_level))
                
                if is_show then
                    self.ei_toggle_list[big_type].is_show = true
                end

                if remind_num > 0 then
                    self.ei_toggle_list[big_type].remind_num = self.ei_toggle_list[big_type].remind_num + 1
                end

                if not is_all_act then
                    self.ei_toggle_list[big_type].is_all_act = false
                end
                local data = {name = v.suit_name, type = suit_index, is_show = is_show, is_open = is_open, open_level = v.open_level,
                remind_num = remind_num, remind_list = remind_list, is_all_act = is_all_act, suit_level = suit_level,
                is_max_level = is_max_level, quality = v.quality, bg = v.bg, ring_bg = v.ring_bg, ring_effect = v.ring_effect}
                table.insert(self.ei_toggle_list[big_type].child_list, data)
            end
        end
    end
end

-- 获取菜单列表
function EquipTargetWGData:GetEIToggleList()
    return self.ei_toggle_list
end

-- 获取大类型 by suit_index
function EquipTargetWGData:GetEIBigType(suit_index)
    return self.ei_get_big_type_list[suit_index]
end

function EquipTargetWGData:GetEIBigToggleList(big_type)
    return self.ei_toggle_list[big_type]
end

-- 获取槽位配置
function EquipTargetWGData:GetEISoltCfg(suit_index, slot_index)
    return (self.ei_suit_map_cfg[suit_index] or {})[slot_index]
end

function EquipTargetWGData:GetEIToggleShowCfg(big_type, suit_index)
    return (self.ei_toggle_show_cfg[big_type] or {})[suit_index]
end

-- 获取槽位道具列表
function EquipTargetWGData:GetEISoltEquipList(suit_index)
    return self.ei_suit_map_equip_list[suit_index] or {}
end

-- 获取槽位激活道具
function EquipTargetWGData:GetEISoltEquipId(suit_index, slot_index)
    return (self.ei_suit_map_equip_list[suit_index] or {})[slot_index]
end

-- 获取套装激活配置
function EquipTargetWGData:GetEISuitActCfg(suit_index)
    return self.ei_suit_acive_map_cfg[suit_index]
end

-- 获取套装等级配置
function EquipTargetWGData:GetEISuitLevelCfg(suit_index, level)
    return (self.ei_suit_level_cfg[suit_index] or {})[level]
end

-- 是否是套装物品id (此记录符合角色的道具列表)
function EquipTargetWGData:GetEIIsStuffItemID(item_id)
    return self.ei_suit_equip_map_list[item_id]
end

-- 是否是套装升级物品id
function EquipTargetWGData:GetEIIsLevelStuffItemID(item_id)
    return self.ei_suit_level_item_cfg[item_id]
end

function EquipTargetWGData:GetEICfgByItem(item_id)
    return self.ei_suit_equip_cfg[item_id]
end
-- 是否是可分解物品
function EquipTargetWGData:GetEIIsResloveItemID(item_id)
    return self.ei_decompose_item_cfg[item_id]
end

function EquipTargetWGData:GetEIIsStuffItemIDByStar(item_id, star)
    return (self.ei_suit_equip_map_list[item_id] or {})[star]
end

-- 根据 item_id star 获取道具配置
function EquipTargetWGData:GetEIStuffDataByItemId(item_id, star)
    return (self.ei_map_all_equip_list[item_id] or {})[star]
end

-- 获取道具是否可激活
function EquipTargetWGData:GetEIItemIdCanAct(item_id, star, is_add)
    local cfg = (self.ei_suit_equip_map_list[item_id] or {})[star]
    if cfg then
        local act_flag = self:GetSlotIsAct(cfg.suit_index, cfg.slot_index)
        if (not act_flag and is_add) then
            self:SetToggleListRemind(cfg.suit_index, cfg.slot_index, true)
        elseif not act_flag and not is_add then
            self:UpdateToggleListSuitRemind(cfg.suit_index)
        end

        return not act_flag
    end

    return false
end

-- 获取套装是否可以升级
function EquipTargetWGData:GetEIItemIdCanUpSuitLevel()
    for suit_index, value in pairs(self.ei_suit_level_cfg) do
        local is_all_act = self:GetSuitIsAllAct(suit_index)
        if is_all_act then
            self:UpdateToggleListSuitRemind(suit_index)
        end
    end
end

-- 获取等级展示 等级列表
function EquipTargetWGData:GetEIShowLevelList()
    return self.ei_show_level_list
end

-- 设置菜单单个槽位红点
function EquipTargetWGData:SetToggleListRemind(suit_index, slot_index, is_remind)
    local big_type = self:GetEIBigType(suit_index)
    local suit_list = self:GetEIBigToggleList(big_type)
    if suit_list == nil then
        return
    end

    local suit_remind = 0
    for k,v in pairs(suit_list.child_list) do
        if v.type == suit_index then
            if v.is_open then
                -- 改变套装红点统计
                v.remind_list[slot_index] = is_remind and 1 or 0
                local remind_num = 0
                for slot, v_r in pairs(v.remind_list) do
                    remind_num = remind_num + v_r
                end
                v.remind_num = remind_num
            end
        end

        if v.remind_num > 0 then
            suit_remind = suit_remind + v.remind_num
        end
    end

    suit_list.remind_num = suit_remind
end

-- 更新菜单列表套装红点
function EquipTargetWGData:UpdateToggleListSuitRemind(suit_index)
    local big_type = self:GetEIBigType(suit_index)
    local suit_list = self:GetEIBigToggleList(big_type)
    if suit_list == nil then
        return
    end

    local suit_remind = 0
    for k,v in pairs(suit_list.child_list) do
        if v.type == suit_index then
            if v.is_open then
                local remind_num, remind_list = self:GetEISuitRemindNum(suit_index, true)
                v.remind_num = remind_num
                v.remind_list = remind_list
            end
        end

        if v.remind_num > 0 then
            suit_remind = suit_remind + v.remind_num
        end
    end

    suit_list.remind_num = suit_remind
end

-- 设置列表激活状态
function EquipTargetWGData:SetToggleListAct(suit_index, slot_index)
    local big_type = self:GetEIBigType(suit_index)
    local big_type_data = self:GetEIBigToggleList(big_type)
    if big_type_data == nil then
        return
    end

    local is_all_act = true
    for k,v in pairs(big_type_data.child_list) do
        if v.type == suit_index then
            v.is_all_act = self:GetSuitIsAllAct(suit_index)
        end

        if not v.is_all_act then
            is_all_act = false
        end
    end

    big_type_data.is_all_act = is_all_act
end

-- 获取套装红点
function EquipTargetWGData:GetEISuitRemindNum(suit_index, need_calc)
    local list = self.ei_suit_map_cfg[suit_index]
    local remind_num, remind_list = 0, {}
    if not list then
        return remind_num
    end
    local is_all_act = self:GetSuitIsAllAct(suit_index)                             --套装全激活
    
    for i = 0, #list do
        local data = list[i]
        remind_list[i] = 0
        if need_calc and self:GetSlotRemind(suit_index, data.slot_index) and not is_all_act then
            remind_num = remind_num + 1
            remind_list[i] = 1
        end
    end

    local suit_level = self:GetSuitLevel(suit_index) or 0
    local cur_level_cfg = self:GetEISuitLevelCfg(suit_index, suit_level)
    local next_level_cfg = self:GetEISuitLevelCfg(suit_index, suit_level + 1)
    if need_calc and is_all_act and cur_level_cfg and next_level_cfg then
        local num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
        remind_num = num >= cur_level_cfg.cost_item_num and remind_num + 1 or remind_num
    end

    return remind_num, remind_list
end

function EquipTargetWGData:GetBigTypeRemind(big_type)
    local cfg = self.ei_big_toggle_cfg[big_type] or {}
    local is_remind = false
    for k,v in pairs(cfg) do
        local suit_index = v.suit_index
        is_remind = self:GetSmallTypeRemind(suit_index)
        if is_remind then
            break
        end
    end

    return is_remind
end

function EquipTargetWGData:GetSmallTypeRemind(suit_index)
    local cfg = self.ei_suit_cfg[suit_index] or {}
    local is_remind = false
    for k,v in pairs(cfg) do
        is_remind = self:GetSlotRemind(v.suit_index, v.slot_index)
        if is_remind then
            break
        end
    end

    return is_remind
end

-- 获取槽位红点
function EquipTargetWGData:GetSlotRemind(suit_index, slot_index)
    local is_act = self:GetSlotIsAct(suit_index, slot_index)
    if is_act then
        return false
    end

    local big_type = self:GetEIBigType(suit_index)
    local toggle_show_cfg = self:GetEIToggleShowCfg(big_type, suit_index)
    if toggle_show_cfg then
        -- local level = RoleWGData.Instance:GetAttr("level")
        -- if level < toggle_show_cfg.open_level then
        --     return false
        -- end
    else
        return false
    end


    local need_data = self:GetEISoltEquipId(suit_index, slot_index)
    if not need_data then
        return false
    end

    local need_item_id = need_data.item_id
    local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(need_item_id)
    -- local item_sub_type = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
    local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
    local wear_data = EquipWGData.Instance:GetGridData(equip_body_index)
    if item_cfg then
        if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
            if wear_data and wear_data.param then
                local wear_cfg = ItemWGData.Instance:GetItemConfig(wear_data.item_id)
                if item_cfg.order > wear_cfg.order or need_data.star > wear_data.param.star_level or item_cfg.color > wear_cfg.color then
                    return false
                else
                    local item_list = ItemWGData.Instance:GetComposeEquipListByData(item_cfg.order, item_cfg.color, need_data.star) or {}
                    for k,v in pairs(item_list) do
                        if v.item_id == need_item_id then
                            return true
                        end
                    end
                end
            end
        else
            local item_num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
            return item_num > 0
        end
    end

    return false
end

-- 获取总红点
function EquipTargetWGData:GetEIAllRemind()
    local list = self:GetEIToggleList()
    for k,v in pairs(list) do
        if v.remind_num > 0 then
            return 1
        end
    end

    return 0
end

-- 获取套装激活属性列表
function EquipTargetWGData:GetEISuitActAttrList(suit_index)
    local cfg = self.ei_suit_map_cfg[suit_index]
    local attr_list = {}
    if not cfg then
        return attr_list
    end

    local value_list = {}
    local key_list = AttributeMgr.GetUsefulAttributteByClass(cfg[0])
    for k,v in pairs(key_list) do
        value_list[v] = {old_attr_str = k, value = 0}
    end

    local is_act = false
    for k,v in pairs(cfg) do
        is_act = self:GetSlotIsAct(suit_index, v.slot_index)
        if is_act then
            for cfg_key, attr_str in pairs(key_list) do
                value_list[attr_str].value = value_list[attr_str].value + v[cfg_key]
            end
        end
    end

    for k, v in pairs(value_list) do
		local data = {}
		data.attr_str = k
		data.attr_value = v.value
		data.attr_next_value = 0
		data.add_value = 0
        data.old_attr_str = v.old_attr_str
		data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
		table.insert(attr_list, data)
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

    return attr_list
end

-- 在属性列表，塞入当前可提升属性数据
function EquipTargetWGData:GetEIAttrListAddData(attr_list, suit_index, slot_index)
    local cfg = self:GetEISoltCfg(suit_index, slot_index)
    if not cfg then
        return attr_list
    end

    local value = 0
    for k, v in pairs(attr_list) do
        value = cfg[v.old_attr_str]
		if value and value > 0 then
            v.add_value = value
        else
            v.add_value = 0
        end
	end

    return attr_list
end

-- 获取套装等级属性
function EquipTargetWGData:GetEISuitLevelAttrList(attr_list, suit_index, suit_level)
    local all_attr_list = {}

    local level_cfg = self:GetEISuitLevelCfg(suit_index, suit_level)
    local cur_level_cfg = self:GetEISuitLevelCfg(suit_index, suit_level)
    local next_level_cfg = self:GetEISuitLevelCfg(suit_index, suit_level + 1)
    local attr_level_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 6)

     --判断总属性是否存在该属性类型
    local is_new_attr = function (attr_tab)
        for k, v in pairs(all_attr_list) do
            if attr_tab.attr_str == v.attr_str then
                return false, v
            end
        end

        return true
    end

    local add_attr = function (attr_tab, add_type) -- 1套装激活属性 2套装等级属性
        local new_attr, same_attr = is_new_attr(attr_tab)
        local attr_str
        if new_attr then
            local data = {}
            attr_str = attr_tab.attr_str
            data.attr_str = attr_str
            data.attr_value = attr_tab.attr_value
            data.add_value = add_type == 1 and 0 or attr_tab.add_value
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
            table.insert(all_attr_list, data)
        else
            same_attr.attr_value = same_attr.attr_value + attr_tab.attr_value
            same_attr.add_value = add_type == 1 and 0 or attr_tab.add_value
        end
    end

    --插入套装激活属性
    for k, v in pairs(attr_list) do
        add_attr(v, 1)
    end

    --插入套装等级属性
    for k, v in pairs(attr_level_list) do
        add_attr(v, 2)
    end

    return all_attr_list
end

-- 获取套装当前等级和下一级属性
function EquipTargetWGData:GetEISuitCurAndNextLevelAttrList(attr_list, suit_index, suit_level)
    local cur_attr_list = {}
    local next_attr_list = {}

    local level_cfg = self:GetEISuitLevelCfg(suit_index, suit_level)
    local cur_level_cfg = self:GetEISuitLevelCfg(suit_index, suit_level)
    local next_level_cfg = self:GetEISuitLevelCfg(suit_index, suit_level + 1)
    local attr_level_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 6)

     --判断总属性是否存在该属性类型
    local is_new_attr = function (attr_tab)
        for k, v in pairs(cur_attr_list) do
            if attr_tab.attr_str == v.attr_str then
                return false, v
            end
        end

        return true
    end

    local add_attr = function (attr_tab, add_type) -- 1套装激活属性 2套装等级属性
        local new_attr, same_attr = is_new_attr(attr_tab)
        local attr_str
        if new_attr then
            local data = {}
            attr_str = attr_tab.attr_str
            data.attr_str = attr_str
            data.attr_value = attr_tab.attr_value
            data.next_value = add_type == 1 and 0 or attr_tab.add_value
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
            table.insert(cur_attr_list, data)
        else
            same_attr.attr_value = same_attr.attr_value + attr_tab.attr_value
            same_attr.next_value = add_type == 1 and 0 or attr_tab.add_value
        end
    end

    --插入套装激活属性
    for k, v in pairs(attr_list) do
        add_attr(v, 1)
    end

    --插入套装等级属性
    for k, v in pairs(attr_level_list) do
        add_attr(v, 2)
    end

    if not IsEmptyTable(next_level_cfg) then
        for k, v in pairs(cur_attr_list) do
            local data = {}
            data.attr_str = v.attr_str
            data.attr_value = v.attr_value + v.next_value
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(v.attr_str)
            table.insert(next_attr_list, data)
        end
    end

    return cur_attr_list, next_attr_list
end

-- 总战力计算
function EquipTargetWGData:GetEIActCapability()
    local key_list = AttributeMgr.GetUsefulAttributteByClass(self:GetEISoltCfg(1, 0))
    if IsEmptyTable(key_list) then
        return 0
    end

    local is_act = false
    local cfg = ConfigManager.Instance:GetAutoConfig("equip_jicheng_cfg_auto").suit_info
    local attribute = AttributePool.AllocAttribute()
    for k,v in ipairs(cfg) do
        is_act = self:GetSlotIsAct(v.suit_index, v.slot_index)
        if is_act then
            for cfg_key, attr_str in pairs(key_list) do
                attribute[attr_str] = attribute[attr_str] + v[cfg_key]
            end
        end
    end

    -- 套装激活属性
    local sys_attr_per = 0
    local em_data = EquipmentWGData.Instance
    local act_attribute = AttributePool.AllocAttribute()
    local max_attr_num = 2
    local attr_id, attr_value = 0, 0
    for k,v in ipairs(self.ei_suit_acive_map_cfg) do
        if self:GetSuitIsAllAct(v.suit_index) then
            sys_attr_per = sys_attr_per + v.sys_attr_per
            for i = 1, max_attr_num do
                attr_id = v["attr_id" .. i]
                attr_value = v["attr_value" .. i]
                if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                    local attr_str = em_data:GetAttrStrByAttrId(attr_id)
                    act_attribute[attr_str] = act_attribute[attr_str] + attr_value
                end
            end
        end
    end

    --套装等级属性
    for k, v in ipairs(cfg) do
        local suit_level = self:GetSuitLevel(v.suit_index)
        local cur_level_cfg = self:GetEISuitLevelCfg(v.suit_index, suit_level)
        local is_all_act = self:GetSuitIsAllAct(v.suit_index)
        if cur_level_cfg and is_all_act then
            local attr_level_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, nil, "attr_id", "attr_value", 1, 6)
            for k1, v1 in pairs(attr_level_list) do
                attribute[v1.attr_str] = attribute[v1.attr_str] + v1.attr_value
            end
        end
    end
    -- 系统属性加成
    local add_per = 1 + sys_attr_per * 0.0001
    for k,v in pairs(attribute) do
        if v > 0 then
            attribute[k] = attribute[k] * add_per
        end

        if attribute[k] and act_attribute[k] then
            attribute[k] = attribute[k] + act_attribute[k]
        else
            print_error("---哪个属性key不存在---", k)
        end
    end

    return AttributeMgr.GetCapability(attribute)
end

-- 获取套装首先选中的槽位
function EquipTargetWGData:GetEIFirstJumpSlot(suit_index)
    local slot
    local big_type = self:GetEIBigType(suit_index)
    local suit_list = self:GetEIBigToggleList(big_type)
    if suit_list == nil then
        return slot
    end

    local suit_data
    for k,v in ipairs(suit_list.child_list) do
        if v.type == suit_index then
            suit_data = v
            break
        end
    end

    if not suit_data or suit_data.is_all_act then
        return slot
    end

    local remind_list = suit_data.remind_list
    for i = 0, #remind_list do
        if remind_list[i] > 0 then
            return i
        elseif slot == nil and not self:GetSlotIsAct(suit_index, i) then
            slot = i
        end
    end

    return slot
end

function EquipTargetWGData:GetEISuitActAttrDesc(suit_index)
    local suit_act_cfg = self:GetEISuitActCfg(suit_index)
    local attr_desc = ""
    if suit_act_cfg == nil then
        return attr_desc
    end

    local em_data = EquipmentWGData.Instance
    local attr_id, attr_value = 0, 0
    local max_attr_num = 2
    for i = 1, max_attr_num do
        attr_id = suit_act_cfg["attr_id" .. i]
        attr_value = suit_act_cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local is_per = em_data:GetAttrIsPer(attr_id)
            -- local desc_str = i < max_attr_num and "<color=#9DF5A7>%s</color>  %s\n" or "<color=#9DF5A7>%s</color>  %s"
            local desc_str = i < max_attr_num and "%s <color=#99ffbb>%s</color>   " or "%s <color=#99ffbb>%s</color>  "
            local attr_name = em_data:GetAttrNameByAttrId(attr_id, true)
            attr_desc = attr_desc .. string.format(desc_str, attr_name, is_per and (attr_value * 0.01) .. "%" or attr_value) .. "\n"
        end
    end

    if suit_act_cfg["sys_attr_per"] > 0 then
        attr_desc = attr_desc .. string.format(Language.EquipTarget.SysAttrStr, suit_act_cfg["sys_attr_per"] * 0.01)
    end

    return attr_desc
end

function EquipTargetWGData:GetEISuitActAttrCap(suit_index)
    local suit_act_cfg = self:GetEISuitActCfg(suit_index)
    local attr_desc = ""
    if suit_act_cfg == nil then
        return attr_desc
    end

    local max_attr_num = 2
    local attr_id, attr_value = 0, 0

    local attr_data_list = AttributePool.AllocAttribute()
    for i = 1, max_attr_num do
        attr_id = suit_act_cfg["attr_id" .. i]
        attr_value = suit_act_cfg["attr_value" .. i]
        local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
        attr_data_list[attr_str] = attr_data_list[attr_str] + attr_value
    end

    local capability = AttributeMgr.GetCapability(attr_data_list)

    return capability
end

-- 回收 - 分解列表
function EquipTargetWGData:GetResloveBagList()
	local bag_data = {}
	local bag_stuff_list = ItemWGData.Instance:GetBagItemDataList()
	for k,v in pairs(bag_stuff_list) do
		if self:GetEIIsResloveItemID(v.item_id) and self:GetIsConditionByItemId(v.item_id) then
			table.insert(bag_data, v)
		end
	end

	return bag_data
end

function EquipTargetWGData:GetIsConditionByItemId(item_id)
    local cfg = self:GetEICfgByItem(item_id)
    if cfg then
        local act_flag = self:GetSlotIsAct(cfg.suit_index, cfg.slot_index)
        if act_flag then
            return true
        end
    end

    return false
end

--背包中含有激活物或者槽位有一个激活了  则判断open
function EquipTargetWGData:GetOtherOpenCondition(suit_index)
    local list = self.ei_suit_map_cfg[suit_index]
    if not list then
        return false
    end

    for i = 0, #list do
        local data = list[i]
        local need_data = self:GetEISoltEquipId(suit_index, data.slot_index)
        if not need_data then
            return false
        end

        local is_act = self:GetSlotIsAct(suit_index, data.slot_index)
        if is_act then
            return true
        end

        local need_item_id = need_data.item_id
        local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(need_item_id)
        if item_cfg then
            if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
                local item_list = ItemWGData.Instance:GetComposeEquipListByData(item_cfg.order, item_cfg.color, need_data.star) or {}
                for k, v in pairs(item_list) do
                    if v.item_id == need_item_id then
                        return true
                    end
                end
            else
                local item_num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
                if item_num > 0 then
                    return true
                end
            end
        end
    end

    return false
end

