require("game/rank/rank_item")
RankView = RankView or BaseClass(SafeBaseView)

RankIndex = {
	ZhanLi         = 1, --策划要求去除战力榜
	Competition    = 2, -- 竞技榜
	Equip          = 3,
	Level          = 4,
	-- <PERSON><PERSON><PERSON>          = 5,
	<PERSON>          = 5,
	<PERSON><PERSON><PERSON>     = 6,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  = 7,
	<PERSON><PERSON><PERSON>      = 8,
	<PERSON>abaoGrade     = 9,
	ShenWuGrade    = 10,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  = 11,
	<PERSON><PERSON>WaGrade    = 12,
	FanRenJinJie   = 13,
	<PERSON><PERSON><PERSON>         = 14,
	LingChongGrade = 15,
	Capability     = 16, -- 仙盟戰力榜

	--XianQiGrade		= 16,  -- 仙器排行榜 策划要求去除仙器榜
}

RankMethod = {
	TopTen      = 0, --前十排行
	FullRanking = 1, --完整排行榜
}

function RankView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource({ TabIndex.paihangbang, TabIndex.cross_rank }, "uis/view/common_panel_prefab",
		"layout_a3_background_common_panel")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(TabIndex.paihangbang, "uis/view/rank_ui_prefab", "layout_rank")
	self:AddViewResource(TabIndex.cross_rank, "uis/view/rank_ui_prefab", "layout_rank")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self.item_listview_list = nil --存储所有标签按钮的表
	self.item_guildview_list = nil
	self.cur_index = 0
	self.default_index = TabIndex.paihangbang
	self.cur_btn_index = 0
	self.old_btn_index = -1
	self.rank_kind = 0
	self.rank_type = nil
	self.rank_method = RankMethod.TopTen
	self.background_loader = nil
	self.force_refresh = nil
end

function RankView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.item_listview_list then
		self.item_listview_list:DeleteMe()
		self.item_listview_list = nil
	end

	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

	self.cur_enum_index = 0
	self.rank_kind = 0
	self.jump_index = nil
	self.cur_btn_index = 0
	self.old_btn_index = nil
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.mount_model then
		self.mount_model:DeleteMe()
		self.mount_model = nil
	end

	self.rank_item_data = nil
	self.rank_type = nil
	self.rank_method = RankMethod.TopTen
	if self.background_loader then
        self.background_loader:Destroy()
		self.background_loader = nil
	end
	self.force_refresh = nil
	self:CancelWeaponTween()
	self.origin_display_pos = nil
	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
	--self:ZhuZaiShenDianReleaseCallBack()

	self:CleanXianQiModelAction()
end

function RankView:LoadCallBack()
	--{RemindName.ZhuZaiShenDian},
	self.tabbar = Tabbar.New(self.node_list)
	self.tabbar:SetVerTabbarIconStr("rank_view")
	self.tabbar:Init(Language.Rank.TabGrop, {}, nil, nil, nil)
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.TabbarClick, self))
	self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabbarStr, self))
	--self.node_list.title_view_name.text.text = Language.Rank.PaiHangTitle
	--self.node_list.title_view_name.text.lineSpacing = 1
end

function RankView:OpenCallBack()
end

function RankView:OnFlush(param_t, index)
	if index == TabIndex.paihangbang then
		self:RefreshLocalPage(param_t)
	elseif index == TabIndex.cross_rank then
		self:RefreshCrossPage()
	end
	self:SetTabbarStr()
end

function RankView:LoadIndexCallBack(index)
	if index ~= 0 then
		self:InitRankConfig()
		self:InitScrollBtnList()
		if not self.money_bar then
			self.money_bar = MoneyBar.New()
			local bundle, asset = ResPath.GetWidgets("MoneyBar")
			local show_params = {
				show_gold = true,
				show_bind_gold = true,
				show_coin = true,
				show_silver_ticket = true,
			}
			self.money_bar:SetMoneyShowInfo(0, 0, show_params)
			self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
		end

		if self.node_list.rank_display_model and not self.origin_display_pos then
			self.origin_display_pos = self.node_list.rank_display_model.rect.anchoredPosition
		end

		--self.node_list.moban_btn.button:AddClickListener(BindTool.Bind1(self.OnClickMoban, self))
		self.node_list.lookinfo_btn.button:AddClickListener(BindTool.Bind1(self.LookRoleInfo, self))
		--self.node_list.tips_btn.button:AddClickListener(BindTool.Bind1(self.OnClickTips, self))
		self.node_list.cap_com_btn.button:AddClickListener(BindTool.Bind1(self.OnClickCapCom, self))
		self.node_list.left_common_toggle.toggle:AddValueChangedListener(BindTool.Bind(self.ToggleRankClick, self, true))
		self.node_list.right_common_toggle.toggle:AddValueChangedListener(BindTool.Bind(self.ToggleRankClick, self, false))
		self.node_list.right_common_toggle:SetActive(false)
	end
end

function RankView:ShowIndexCallBack(index)
	self.do_rank_tween = true
	if index == TabIndex.paihangbang then
		self.rank_kind = RankKind.Person
	elseif index == TabIndex.cross_rank then
		self.rank_kind = RankKind.Cross
	end

	if self.list_view and self.cur_btn_index == 0 and self.rank_config then
		--self.list_view:SetDefaultSelectIndex(1)
		self.list_view:SetDataList(self.rank_config, 3)
		self.list_view:JumpToIndex(1)
	end
end

--[[function RankView:FlushView(param_t, index)
	if not self:IsOpen() then  --因为个人排行数据 会刷新多个界面
		return
	end

	self:RefreshLocalPage()
end--]]

function RankView:SetTabbarStr()
	if self.tabbar then
		--self.tabbar:SetVerTabbarStr(Language.Rank.TabGrop)
		self.tabbar:SetVerToggleVisble(TabIndex.cross_rank, false) --屏蔽跨服排行榜
	end
end

function RankView:TabbarClick(index)
	self.cur_btn_index = 0
	self.old_btn_index = -1
	if index == TabIndex.paihangbang then
		self.node_list.title_view_name.text.text = Language.ViewName.rank
	elseif index == TabIndex.cross_rank then
		self.node_list.title_view_name.text.text = Language.ViewName.CrossRank
	end
	self.node_list.title_view_name.text.lineSpacing = 1
	self:ChangeToIndex(index)
end

function RankView:InitScrollBtnList()
	self.list_view = AsyncListView.New(ButtonItem, self.node_list.ph_btn_listview) -- 实例化榜单item  第二个参数为 listview 的对象
	--默认选中第一个
	self.list_view:SetSelectCallBack(BindTool.Bind1(self.OnOpenRankView, self)) -- 添加点击事件
	self.list_view:SetDefaultSelectIndex(1)
	self.list_view:SetDataList(self.rank_config, 3)                             -- 给item设置数据
end

function RankView:SetJumpIndex(index)
	self.jump_index = index
end

-- 打开榜单视图
function RankView:OnOpenRankView(cell, index)
	if not cell or self.cur_btn_index == cell.index or nil == self.rank_config then
		return
	end

	local rank_kind = self.rank_kind
	self.cur_btn_index = cell.index                 -- 设置一个当前按钮的下标
	self.cur_enum_index = cell:GetData().enum_index --在枚举中的索引
	local index = cell.index
	local rank_cfg = self.rank_config[index]
	if nil == rank_cfg then
		return
	end

	self.rank_type = rank_cfg.rank_type
	-- self.node_list.pingjie_text.text.text = Language.Rank.Person_text[rank_cfg.rank_type]
	if rank_kind == RankKind.Person then
		self.node_list.pingjie_text.text.text = Language.Rank.Person_text[rank_cfg.rank_type]
	elseif rank_kind == RankKind.Cross then
		self.node_list.pingjie_text.text.text = Language.Rank.Cross_text[rank_cfg.rank_type]
	end

	if self.tabbar then
		if self.rank_type == PersonRankType.CrossCompetition then
			self.tabbar:SetVerTabbarStr(Language.Rank.TabGrop1)
		else
			self.tabbar:SetVerTabbarStr(Language.Rank.TabGrop)
		end
	end

	local is_cross = false
	if rank_kind == RankKind.Person then
		if rank_cfg.kind == RankKind.Guild then
			rank_kind = rank_cfg.kind
		end
	elseif rank_kind == RankKind.Cross then
		if rank_cfg.rank_type == PersonRankType.Level then
			self.rank_type = CrossRankType.CROSS_Level
		elseif rank_cfg.rank_type == PersonRankType.ZhanLi then
			self.rank_type = CrossRankType.CROSS_ZHANLI
		elseif rank_cfg.rank_type == PersonRankType.Equip then
			self.rank_type = CrossRankType.CROSS_Equip
		elseif rank_cfg.rank_type == PersonRankType.JinJie then
			self.rank_type = CrossRankType.CROSS_JinJie
		elseif rank_cfg.rank_type == PersonRankType.TianXianGeRank then
			self.rank_type = CrossRankType.CROSS_TianXianGeRank
		elseif rank_cfg.rank_type == PersonRankType.MountGrade then
			self.rank_type = CrossRankType.CROSS_MountGrade
		elseif rank_cfg.rank_type == PersonRankType.WingGrade then
			self.rank_type = CrossRankType.CROSS_WingGrade
		elseif rank_cfg.rank_type == PersonRankType.FabaoGrade then
			self.rank_type = CrossRankType.CROSS_FabaoGrade
		elseif rank_cfg.rank_type == PersonRankType.ShenWuGrade then
			self.rank_type = CrossRankType.CROSS_ShenWuGrade
		elseif rank_cfg.rank_type == PersonRankType.JianZhenGrade then
			self.rank_type = CrossRankType.CROSS_JianZhenGrade
		elseif rank_cfg.rank_type == PersonRankType.TianShenGrade then
			self.rank_type = CrossRankType.CROSS_TianShenGrade
		elseif rank_cfg.rank_type == PersonRankType.XianWaGrade then
			self.rank_type = CrossRankType.CROSS_XianWaGrade
		elseif rank_cfg.rank_type == PersonRankType.LingChongGrade then
			self.rank_type = CrossRankType.CROSS_LingChongGrade
		elseif rank_cfg.rank_type == PersonRankType.XianQiGrade then
			self.rank_type = CrossRankType.CROSS_XianQiGrade
		elseif rank_cfg.kind == RankKind.Guild then --仙盟戰力榜
			rank_kind = rank_cfg.kind
			is_cross = true
			self.rank_type = GuildRankType.Level
		end
	end
	--print_error(rank_kind, self.rank_type)
	RankWGCtrl.Instance:SendRankReq(rank_kind, self.rank_type, is_cross) -- 請求排行榜信息
end

-- 初始化排行配置
function RankView:InitRankConfig()
	if nil ~= self.rank_config and nil ~= self.rank_kuafu_config then
		return
	end

	local l = Language.Rank

	self.rank_config = {

		-- 等级榜
		[RankIndex.Level] = { kind = RankKind.Person, rank_type = PersonRankType.Level, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[2], title_text = { l.PaiMing, l.MingZi, l.ZhiYe, l.DengJi }, open_param = FunName
		.rank_level, name_param = Language.Rank.NameList[3] },

		-- 战力榜
		[RankIndex.ZhanLi] = { kind = RankKind.Person, rank_type = PersonRankType.ZhanLi, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[1], title_text = { l.PaiMing, l.MingZi, l.ZhiYe, l.ZhanLi }, open_param = FunName
		.rank_zhanli, name_param = Language.Rank.NameList[1] },

		-- 竞技榜
		[RankIndex.Competition] = { kind = RankKind.Person, rank_type = PersonRankType.CrossCompetition, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[1], open_param = FunName.rank_competition, name_param = Language.Rank.NameList[2] },

		-- 帮派榜 2 ~ 5
		[RankIndex.Capability] = { kind = RankKind.Guild, rank_type = GuildRankType.Capability, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[4], title_text = { l.PaiMing, l.BangPai, l.DengJi_1, l.ZhanLi_1 }, open_param =
		FunName.rank_xianmeng, name_param = Language.Rank.NameList[4] },

		-- 装备榜
		[RankIndex.Equip] = { kind = RankKind.Person, rank_type = PersonRankType.Equip, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[4], title_text = { l.PaiMing, l.MingZi, l.ZhiYe, l.Equip_score }, open_param =
		FunName.rank_equip, name_param = Language.Rank.NameList[5] },
		-- 境界榜
		[RankIndex.JinJie] = { kind = RankKind.Person, rank_type = PersonRankType.JinJie, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[4], title_text = { l.PaiMing, l.MingZi, l.ZhiYe, l.JinJie_1 }, open_param = FunName
		.rank_jinjie, name_param = Language.Rank.NameList[15] },

		-- 凡人修真榜
		[RankIndex.FanRenJinJie] = { kind = RankKind.Person, rank_type = PersonRankType.TianXianGeRank, render =
		RankNewItem, ui_config = self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[4], title_text = { l.PaiMing, l.MingZi, l.ZhiYe, l.History }, open_param = FunName
		.rank_fanrenxiuzhen, name_param = Language.Rank.NameList[16] },

		-- 坐骑榜
		[RankIndex.MountGrade] = { kind = RankKind.Person, rank_type = PersonRankType.MountGrade, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[2], title_text = { l.PaiMing, l.MingZi, l.ZuoQi, l.MountGrade }, open_param =
		FunName.rank_zuoqi, name_param = Language.Rank.NameList[7] },

		-- 羽翼榜
		[RankIndex.WingGrade] = { kind = RankKind.Person, rank_type = PersonRankType.WingGrade, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[2], title_text = { l.PaiMing, l.MingZi, l.ZhiYe, l.WingLevel }, open_param =
		FunName.rank_yuyi, name_param = Language.Rank.NameList[9] },


		-- 法宝榜
		[RankIndex.FabaoGrade] = { kind = RankKind.Person, rank_type = PersonRankType.FabaoGrade, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[2], title_text = { l.PaiMing, l.MingZi, l.ZhiYe, l.FaBaoLevel }, open_param =
		FunName.rank_fabao, name_param = Language.Rank.NameList[8] },

		-- 神兵榜
		[RankIndex.ShenWuGrade] = { kind = RankKind.Person, rank_type = PersonRankType.ShenWuGrade, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[2], title_text = { l.PaiMing, l.MingZi, l.ZhiYe, l.ShenBingLevel }, open_param =
		FunName.rank_shenbing, name_param = Language.Rank.NameList[10] },

		-- 剑阵榜
		[RankIndex.JianZhenGrade] = { kind = RankKind.Person, rank_type = PersonRankType.JianZhenGrade, render =
		RankNewItem, ui_config = self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[1], title_text = { l.PaiMing, l.MingZi, l.LingQi, l.LINGQIGrade }, open_param =
		FunName.rank_jianzhen, name_param = Language.Rank.NameList[12] },

		-- 天神榜
		[RankIndex.TianShenGrade] = { kind = RankKind.Person, rank_type = PersonRankType.TianShenGrade, render =
		RankNewItem, ui_config = self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[1], title_text = { l.PaiMing, l.MingZi, l.BaseBianShenCap, l.BianShenLevel }, open_param =
		FunName.rank_bianshen, name_param = Language.Rank.NameList[14] },

		-- 仙娃榜
		[RankIndex.XianWaGrade] = { kind = RankKind.Person, rank_type = PersonRankType.XianWaGrade, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[1], title_text = { l.PaiMing, l.MingZi, l.LingQi, l.LINGQIGrade }, open_param =
		FunName.rank_baby, name_param = Language.Rank.NameList[13] },

		-- 灵宠榜
		[RankIndex.LingChongGrade] = { kind = RankKind.Person, rank_type = PersonRankType.LingChongGrade, render =
		RankNewItem, ui_config = self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[2], title_text = { l.PaiMing, l.MingZi, l.LingChong, l.LingChongGrade }, open_param =
		FunName.rank_lingchong, name_param = Language.Rank.NameList[11] },

		-- 渡劫榜
		-- [RankIndex.DuJie] = { kind = RankKind.Person, rank_type = PersonRankType.DuJieJingjie, render = RankNewItem, ui_config =
		-- self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
		-- 	time_str = l.FlushTimeStr[1], title_text = { l.PaiMing, l.MingZi, l.ZhiYe, l.DuJieJingjie }, open_param = FunName
		-- .rank_dujie, name_param = Language.Rank.NameList[18] },

		-- 幻兽榜
		[RankIndex.Beast] = { kind = RankKind.Person, rank_type = PersonRankType.BeastCapability, render = RankNewItem, ui_config =
		self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
			time_str = l.FlushTimeStr[1], title_text = { l.PaiMing, l.MingZi, l.Beast, l.BeastZhanLi }, open_param = FunName
		.rank_beast, name_param = Language.Rank.NameList[18] },

		-- 仙器榜
		--[RankIndex.XianQiGrade] = {kind = RankKind.Person, rank_type = PersonRankType.XianQiGrade, render = RankNewItem, ui_config = self.node_list.ph_rankitem_role, asset_name = "ph_rankitem_role",
		--	time_str = l.FlushTimeStr[2], title_text = {l.PaiMing, l.MingZi, l.XianQi, l.XianQiGrade},open_param = FunName.rank_xianqi,name_param = Language.Rank.NameList[17]},
	}

	local open_list = {}
	local index = 1
	for k, v in ipairs(self.rank_config) do
		if FunOpen.Instance:GetFunIsOpened(v.open_param) then
			v.self_index = index
			v.enum_index = k
			table.insert(open_list, v)
			index = index + 1
		end
	end
	self.rank_config = open_list
end

function RankView:HandleSelectRankIndex(rank_type)
	if self.list_view:GetSelectIndex() ~= rank_type and self.rank_config then
		local index = 1
		for k, v in pairs(self.rank_config) do
			if rank_type == v.rank_type then
				index = v.self_index
			end
		end
		self.list_view:JumpToIndex(index)
	end
end

-- 本服排行榜接口
function RankView:RefreshLocalPage(param_t)
	if nil == self.rank_config then
		return
	end

	for k, v in pairs(param_t) do
		if k == "select_rank_type" then
			self:HandleSelectRankIndex(v.rank_type)
		end
	end

	local rank_cfg = self.rank_config[self.cur_btn_index] -- 当前排行榜属性
	if nil == rank_cfg then
		return
	end

	local item_render = rank_cfg.render                                                     -- 生成item对应的类

	if nil == self.item_listview_list then                                                  -- 为空就实例化一个出来
		self.item_listview_list = AsyncListView.New(item_render, rank_cfg.ui_config)        -- rank_cfg.ui_config  listview 对象
		self.item_listview_list:SetSelectCallBack(BindTool.Bind1(self.OnClickListViewItem, self)) -- 设置选中的回调方法
		self.item_listview_list:SetDefaultSelectIndex(1)
	end

	local rank_kind = self.rank_kind
	if rank_cfg.kind == RankKind.Guild then
		rank_kind = rank_cfg.kind
	end

	--local data_list = RankWGData.Instance:GetRankPageData(rank_kind, rank_cfg.rank_type, true)
	local data_list, MyStrengthInfomation = RankWGData.Instance:GetRankListAndMyStrengthInfomation(rank_kind,
		rank_cfg.rank_type, true)
	self.node_list.rich_my_rank_value.text.text = MyStrengthInfomation or ""

	--前十排行榜
	if self.rank_method == RankMethod.TopTen and #data_list > 10 then
		while #data_list > 10 do
			table.remove(data_list, #data_list)
		end
	end

	if #data_list < 1 then
		self:ClearModelData()
	end

	self.node_list.btn_group:SetActive(#data_list > 0)

	self.item_listview_list:SetDataList(data_list)
	if self.cur_btn_index ~= self.old_btn_index then
		self.item_listview_list:JumpToIndex(1)
		self.old_btn_index = self.cur_btn_index
	elseif self.force_refresh then -- 这里增加一个刷新，如果十大和完整角色不一样这里需要刷新一下，一样就不用刷新
		self.item_listview_list:JumpToIndex(self.select_index)
	end

	local cur_rank_data = RankWGData.Instance:GetSelfValueData(rank_kind, rank_cfg.rank_type)
	local my_rank_type = (self.cur_enum_index == RankIndex.Capability) and Language.Rank.MyGuildRank or
	Language.Rank.MyRank
	self.node_list.no_data:SetActive(#data_list < 1)
	self.node_list.xuwei:SetActive(#data_list < 1)
	-- self.node_list.foundation:SetActive(true)
	self.node_list.background_root:SetActive(false)
	if #data_list < 1 then
		self:FlushBackground(BackgroundWGData.Instance.curr_use_back_id)
	end

	self.node_list.layout_display:SetActive(#data_list > 0)
	--self.node_list.moban:SetActive(#data_list > 0 and self.cur_enum_index ~= RankIndex.Capability)
	self.node_list.display_type:SetActive(#data_list > 0 and self.select_index == 1)

	--if self.select_index ~=nil and #data_list > 0 then
	--	self.node_list.remain_value.text.text = RankWGData.Instance:GetRemaindLikesCount()
	--	if data_list[self.select_index] then
	--   	self.node_list.moban_value.text.text = data_list[self.select_index].worship_count
	--    end
	--end

	local cur_self_rank = cur_rank_data.self_rank
	if cur_rank_data and cur_self_rank and cur_self_rank > 0 and cur_self_rank <= MAX_RANK_NUM then
		self.node_list.rich_my_rank.text.text = string.format("%s<color=#9DF5A7>%s</color>", my_rank_type,
			string.format(Language.Rank.RankValue1, cur_self_rank))
	else
		self.node_list.rich_my_rank.text.text = string.format("%s<color=#9DF5A7>%s</color>", my_rank_type,
			Language.Rank.NoRank)
	end
	self.item_listview_list:JumpToTop()
	--self:ViewAnimation()
end

-- 跨服排行榜接口
function RankView:RefreshCrossPage(force)
	if nil == self.rank_config then
		return
	end

	local rank_cfg = self.rank_config[self.cur_btn_index] -- 当前排行榜属性
	if nil == rank_cfg then
		return
	end

	local show_index = self.cur_enum_index                                                  -- 当前按钮索引
	local item_render = rank_cfg.render                                                     -- 生成item对应的类

	if nil == self.item_listview_list then                                                  -- 为空就实例化一个出来
		self.item_listview_list = AsyncListView.New(item_render, rank_cfg.ui_config)        -- rank_cfg.ui_config  listview 对象
		self.item_listview_list:SetSelectCallBack(BindTool.Bind1(self.OnClickListViewItem, self)) -- 设置选中的回调方法
		self.item_listview_list:SetDefaultSelectIndex(1)
	end

	local rank_kind = self.rank_kind
	if rank_cfg.kind == RankKind.Guild then
		rank_kind = rank_cfg.kind
	end

	local data_list = RankWGData.Instance:GetRankPageData(rank_kind, self.rank_type, true)
	if #data_list < 1 then
		self:ClearModelData()
	end
	self.node_list.btn_group:SetActive(#data_list > 0)
	--print_error("Cross", data_list)
	self.item_listview_list:SetDataList(data_list)
	if self.cur_btn_index ~= self.old_btn_index then
		self.item_listview_list:JumpToIndex(1)
		self.old_btn_index = self.cur_btn_index
	elseif self.force_refresh then -- 这里增加一个刷新，如果十大和完整角色不一样这里需要刷新一下，一样就不用刷新
		self.item_listview_list:JumpToIndex(self.select_index)
	end

	local cur_rank_data = RankWGData.Instance:GetSelfValueData(rank_kind, rank_cfg.rank_type)
	local my_rank_type = (show_index == RankIndex.Capability) and Language.Rank.MyGuildRank or Language.Rank.MyRank
	self.node_list.no_data:SetActive(#data_list < 1)
	self.node_list.xuwei:SetActive(#data_list < 1)
	-- self.node_list.foundation:SetActive(true)
	self.node_list.background_root:SetActive(false)
	if #data_list < 1 then
		self:FlushBackground(BackgroundWGData.Instance.curr_use_back_id)
	end
	self.node_list.layout_display:SetActive(#data_list > 0)
	--self.node_list.moban:SetActive(#data_list > 0 and show_index ~= RankIndex.Capability)
	self.node_list.display_type:SetActive(#data_list > 0 and self.select_index == 1)

	--if self.select_index ~=nil and #data_list > 0 then
	--	self.node_list.remain_value.text.text = RankWGData.Instance:GetRemaindLikesCount()
	--	if data_list[self.select_index] then
	--    	self.node_list.moban_value.text.text = data_list[self.select_index].worship_count
	--    end
	--end

	if cur_rank_data and cur_rank_data.self_rank > 0 and cur_rank_data.self_rank < 51 then
		self.node_list.rich_my_rank.text.text = my_rank_type ..
		"<color=#9DF5A7>" .. string.format(Language.Rank.RankValue1, cur_rank_data.self_rank) .. "</color>"
	else
		self.node_list.rich_my_rank.text.text = my_rank_type .. "<color=#9DF5A7>" .. Language.Rank.NoRank .. "</color>"
	end

	self:ViewAnimation()
end

function RankView:FlushDisPlayView(data)
	if not data then
		return
	end
	-- self.node_list.rank_dianzan:SetActive(not self.is_guild_rank)
	-- if not self.is_guild_rank then
	-- 	self.node_list.rmind_count.text.text = RankWGData.Instance:GetRemaindLikesCount()
	-- 	self.node_list.zan_count.text.text = self.data.worship_count
	-- end

	local rank_type = data.rank_type
	self:ClearModelData() --清空模型数据
	local flush_fun = function(protocol)
		if not self.node_list or not self.node_list["display_name"] then return end
		self.node_list.display_name.text.text = protocol.role_name
		--self.node_list.Text.text.text = Language.Rank.MZ..protocol.role_name --前面字体主要是给初始大小，跟内容无关
		self.node_list.display_type_name.text.text = Language.Rank.RankNameTypeList[self.cur_enum_index]
		--self.node_list.remain_value.text.text = RankWGData.Instance:GetRemaindLikesCount()
		--self.node_list.moban_value.text.text = data.worship_count
		self.node_list.display_zhanli.text.text = protocol.capability
		self.node_list["rank_display_model"]:SetActive(true)
		self.node_list["display"]:SetActive(true)
		if nil == self.mount_model then
			self.mount_model = RoleModel.New()
			local display_data = {
				parent_node = self.node_list["rank_display_model"],
				camera_type = MODEL_CAMERA_TYPE.BASE,
				-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
				rt_scale_type = ModelRTSCaleType.M,
				can_drag = true,
			}
			
			self.mount_model:SetRenderTexUI3DModel(display_data)
		end

		self.node_list["display_capability_root"]:SetActive(true)
		--策划需求:本服个别排行榜隐藏战力显示
		if rank_type == PersonRankType.Level
			or rank_type == PersonRankType.JinJie
			or rank_type == PersonRankType.TianXianGeRank
			or rank_type == GuildRankType.Capability
			-- or rank_type == PersonRankType.Equip
			or rank_type == PersonRankType.CrossCompetition then
			self.node_list["display_capability_root"]:SetActive(false)
		end

		--self.node_list["img_zhanli_title"]:SetActive(true)
		--self.node_list["img_pingfen_title"]:SetActive(false)

		if nil == self.role_model then
			self.role_model = RoleModel.New()
			local display_data = {
				parent_node = self.node_list["display"],
				camera_type = MODEL_CAMERA_TYPE.BASE,
				-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
				rt_scale_type = ModelRTSCaleType.L,
				can_drag = true,
			}
			
			self.role_model:SetRenderTexUI3DModel(display_data)
			self.role_model:SetRTAdjustmentRootLocalPosition(-1.6, -0.7, 0)
			self.role_model:SetRTAdjustmentRootLocalScale(1.1)
		end

		if self.rank_kind == RankKind.Person then
			--设置坐骑模型
			if rank_type == PersonRankType.MountGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				if self.mount_model and protocol.mountattr and protocol.mountattr.used_imageid >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetMountModel(protocol.mountattr.used_imageid))
				end
				--设置灵宠模型
			elseif rank_type == PersonRankType.LingChongGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				--print_error(protocol.lingchong)
				if self.mount_model and protocol.lingchong and protocol.lingchong.used_imageid >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetPetModel(protocol.lingchong.used_imageid))
				end
				--设置羽翼模型
			elseif rank_type == PersonRankType.WingGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				if self.mount_model and protocol.updateattr and protocol.updateattr[0] and protocol.updateattr[0].use_image_id >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetWingModel(protocol.updateattr[0].use_image_id))
				end
				--设置法宝模型
			elseif rank_type == PersonRankType.FabaoGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				if self.mount_model and protocol.updateattr and protocol.updateattr[1] and protocol.updateattr[1].use_image_id >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetFaBaoModel(protocol.updateattr[1].use_image_id))
				end
				--设置神兵模型
			elseif rank_type == PersonRankType.ShenWuGrade then
				local weapon_resid
				local appearance = protocol.appearance
				if nil ~= appearance.shenwu_appeid and 0 ~= appearance.shenwu_appeid then
					weapon_resid, _ = RoleWGData.GetFashionWeaponId(protocol.sex, protocol.prof, appearance
					.shenwu_appeid)
				elseif nil ~= appearance.fashion_wuqi and 0 ~= appearance.fashion_wuqi then
					weapon_resid, _ = RoleWGData.GetFashionWeaponId(protocol.sex, protocol.prof, appearance.fashion_wuqi)
				else
					weapon_resid = RoleWGData.GetJobWeaponId(protocol.sex, protocol.prof)
				end
				self.node_list.display_zhanli.text.text = data.flexible_ll
				local bundle, asset = ResPath.GetWeaponModelRes(weapon_resid)
				self.mount_model:SetMainAsset(bundle, asset)
				--设置剑阵模型
			elseif rank_type == PersonRankType.JianZhenGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				if self.mount_model and protocol.updateattr and protocol.updateattr[7] and protocol.updateattr[7].use_image_id >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetJianZhenModel(protocol.updateattr[7].use_image_id))
				end
				-- 设置天神模型
			elseif rank_type == PersonRankType.TianShenGrade then
				self.node_list.display_zhanli.text.text = data.rank_value
				local tianshen_id
				if protocol.tianshen_image_id >= 0 then
					tianshen_id = protocol.tianshen_image_id
				else
					tianshen_id = 0
				end

				local model_tab = TianShenWGData.Instance:GetTianShenCfg(tianshen_id)
				self.mount_model:SetTianShenModel(model_tab.appe_image_id, tianshen_id, false, nil, nil)
				-- 设置仙娃模型
			elseif rank_type == PersonRankType.XianWaGrade then
				self.node_list.display_zhanli.text.text = data.rank_value
				self.mount_model:SetMainAsset(ResPath.GetHaiZiModel(protocol.lover_info.lover_use_baby_id))
				-- 设置仙器模型
			elseif rank_type == PersonRankType.XianQiGrade then
				self.node_list.display_zhanli.text.text = data.rank_value
				self:SetXianQiModel(protocol, data.flexible_int)
				--设置幻兽模型
			elseif rank_type == PersonRankType.BeastCapability then
				self.node_list.display_zhanli.text.text = data.rank_value
				local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(data.flexible_ll)
				local bundle, asset = ResPath.GetBeastsModel(res_id)
				self.mount_model:SetMainAsset(bundle, asset)
				--设置人物模型
			else
				self.node_list["rank_display_model"]:SetActive(false)
				if self.role_model then
					local ignore_table = { ignore_wing = false, ignore_jianzhen = true, ignore_halo = true }
					self.role_model:SetModelResInfo(protocol, ignore_table)
				end

				self:FlushBackground(protocol.back_id)
			end

			--策划需求:装备榜用评分作战力
			--if rank_type == PersonRankType.Equip then
			--	self.node_list.display_zhanli.text.text = data.rank_value
			--	self.node_list["img_zhanli_title"]:SetActive(false)
			--	self.node_list["img_pingfen_title"]:SetActive(true)
			--end

			-- if rank_type == PersonRankType.ShenWuGrade then
			-- 	self:PlayWeaponTween()
			-- else
			-- 	self:CancelWeaponTween()
			-- end
		elseif self.rank_kind == RankKind.Cross then
			--设置坐骑模型
			if rank_type == CrossRankType.CROSS_MountGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				if self.mount_model and protocol.mountattr and protocol.mountattr.used_imageid >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetMountModel(protocol.mountattr.used_imageid))
				end
				--设置灵宠模型
			elseif rank_type == CrossRankType.CROSS_LingChongGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				if self.mount_model and protocol.lingchong and protocol.lingchong.used_imageid >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetPetModel(protocol.lingchong.used_imageid))
				end
				--设置羽翼模型
			elseif rank_type == CrossRankType.CROSS_WingGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				if self.mount_model and protocol.updateattr and protocol.updateattr[0] and protocol.updateattr[0].use_image_id >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetWingModel(protocol.updateattr[0].use_image_id))
				end
				--设置法宝模型
			elseif rank_type == CrossRankType.CROSS_FabaoGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				if self.mount_model and protocol.updateattr and protocol.updateattr[1] and protocol.updateattr[1].use_image_id >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetFaBaoModel(protocol.updateattr[1].use_image_id))
				end
				--设置神兵模型
			elseif rank_type == CrossRankType.CROSS_ShenWuGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll

				local weapon_resid
				local appearance = protocol.appearance
				if nil ~= appearance.shenwu_appeid and 0 ~= appearance.shenwu_appeid then
					weapon_resid, _ = RoleWGData.GetFashionWeaponId(protocol.sex, protocol.prof, appearance
					.shenwu_appeid)
				elseif nil ~= appearance.fashion_wuqi and 0 ~= appearance.fashion_wuqi then
					weapon_resid, _ = RoleWGData.GetFashionWeaponId(protocol.sex, protocol.prof, appearance.fashion_wuqi)
				else
					weapon_resid = RoleWGData.GetJobWeaponId(protocol.sex, protocol.prof)
				end
				local bundle, asset = ResPath.GetWeaponModelRes(weapon_resid)
				self.mount_model:SetMainAsset(bundle, asset)
				--设置剑阵模型
			elseif rank_type == CrossRankType.CROSS_JianZhenGrade then
				self.node_list.display_zhanli.text.text = data.flexible_ll
				if self.mount_model and protocol.updateattr and protocol.updateattr[7] and protocol.updateattr[7].use_image_id >= 0 then
					self.mount_model:SetMainAsset(ResPath.GetJianZhenModel(protocol.updateattr[7].use_image_id))
				end
				-- 设置天神模型
			elseif rank_type == CrossRankType.CROSS_TianShenGrade then
				self.node_list.display_zhanli.text.text = data.rank_value
				local tianshen_id
				if protocol.tianshen_image_id >= 0 then
					tianshen_id = protocol.tianshen_image_id
				else
					tianshen_id = 0
				end

				local model_tab = TianShenWGData.Instance:GetTianShenCfg(tianshen_id)
				self.mount_model:SetTianShenModel(model_tab.appe_image_id, tianshen_id, false, nil, nil)
				-- 设置仙娃模型
			elseif rank_type == CrossRankType.CROSS_XianWaGrade then
				self.node_list.display_zhanli.text.text = data.rank_value
				self.mount_model:SetMainAsset(ResPath.GetHaiZiModel(protocol.lover_info.lover_use_baby_id))
				-- 设置仙器模型
			elseif rank_type == CrossRankType.CROSS_XianQiGrade then
				self.node_list.display_zhanli.text.text = data.rank_value
				self:SetXianQiModel(protocol, data.flexible_int)
				--设置人物模型
			else
				self.node_list["rank_display_model"]:SetActive(false)
				if self.role_model then
					local ignore_table = { ignore_wing = false, ignore_jianzhen = true }
					self.role_model:SetModelResInfo(protocol, ignore_table)
				end

				self:FlushBackground(protocol.back_id)
			end

			-- if rank_type == CrossRankType.CROSS_ShenWuGrade then
			-- 	self:PlayWeaponTween()
			-- else
			-- 	self:CancelWeaponTween()
			-- end
		end
	end

	local plat_type = RoleWGData.Instance.role_vo.plat_type
	local user_id = self.cur_enum_index == RankIndex.Capability and data.tuan_zhang_uid or data.user_id
	--print_error(user_id, plat_type)
	if user_id == 0 then --机器人
		flush_fun(data)
	elseif user_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then --机器人盟主模型
		local data_info = self:OperaRobotGuildLeader(data)
		flush_fun(data_info)
	else
		if self.rank_kind == RankKind.Cross then
			plat_type = data.plat_type
		end
		BrowseWGCtrl.Instance:BrowRoelInfo(user_id, flush_fun, plat_type)
	end
end

-- 机器人盟主处理
function RankView:OperaRobotGuildLeader(data)
	local data_info = {}
	local cfg = GuildWGData.Instance:GetGuildRobotCfgByGuildName(data.guild_name)
	data_info.sex = cfg.sex or 1
	data_info.prof = cfg.prof or 1
	data_info.role_name = data.tuan_zhang_name
	data_info.capability = cfg.mengzhu_cap
	data_info.appearance = {}
	data_info.appearance.fashion_body = cfg.model
	--data_info.appearance.default_face_res_id = cfg.face
	data_info.updateattr = {}
	return data_info
end

-- item 点击回调
function RankView:OnClickListViewItem(item, cell_index, is_default, is_click)
	if not item then
		return
	end

	self.select_index = item:GetIndex()

	local data = item:GetData()
	if self.rank_item_data == data and (not self.force_refresh) then
		return
	end

	self.rank_item_data = data
	if nil == data then
		return
	end

	local rank_cfg = self.rank_config[self.cur_btn_index] -- 当前排行榜属性
	if nil == rank_cfg then
		return
	end

	local rank_kind = self.rank_kind
	if rank_cfg.kind == RankKind.Guild then
		rank_kind = rank_cfg.kind
	end

	self.force_refresh = nil
	local my_data = RankWGData.Instance:GetSelfValueData(rank_kind, rank_cfg.rank_type)
	self.node_list.display_type:SetActive(self.select_index == 1)
	local zan_array = RankWGData.Instance:GetRankZanArray()

	local count = RankWGData.Instance:GetRemaindLikesCount()
	--_count_remain:SetActive(count ~= 0)

	--if count ~= 0 then
	--	for k,v in pairs(zan_array) do
	--		if self.rank_item_data.user_id == v then
	--			self.node_list.moban_count_remain:SetActive(false)
	--			break
	--		else
	--			self.node_list.moban_count_remain:SetActive(true)
	--		end
	--	end
	--end

	--跨服竞技榜特殊处理，跨服玩家无法看信息所以隐藏
	local is_cross = rank_cfg.rank_type == PersonRankType.CrossCompetition
	local no_me = self.rank_item_data.rank_index ~= my_data.self_rank
	local cap_com_open = FunOpen.Instance:GetFunIsOpened(FunName.CapabilityContrastView)

	local user_id = self.cur_enum_index == RankIndex.Capability and data.tuan_zhang_uid or data.user_id
	local is_robot = (user_id == 0 or user_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID) and true or false

	self.node_list.lookinfo_btn:SetActive(no_me and not is_cross)
	self.node_list.cap_com_btn:SetActive(no_me and cap_com_open and not is_robot)

	--不是自己的盟主显示查看
	if (self.rank_item_data.tuan_zhang_uid and self.rank_item_data.tuan_zhang_uid ~= RoleWGData.Instance:GetRoleVo().role_id) then
		self.node_list.lookinfo_btn:SetActive(true)
	end

	--if self.rank_item_data.rank_index == my_data.self_rank then
	--	  self.node_list.moban.rect.anchoredPosition = Vector2(274,-302)
	--else
	--	  self.node_list.moban.rect.anchoredPosition = Vector2(380,-302)
	--end
	self:FlushDisPlayView(self.rank_item_data)
end

--点赞事件
function RankView:OnClickMoban()
	if not self.rank_item_data then
		return
	end

	local count = RankWGData.Instance:GetRemaindLikesCount()
	if count > 0 then
		local zan_array = RankWGData.Instance:GetRankZanArray()
		for k, v in pairs(zan_array) do
			if self.rank_item_data.user_id == v then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Rank.Per_Zan)
				return
			end
		end

		if self.rank_config and self.rank_config[self.cur_btn_index] and self.rank_type then
			local rank_cfg = self.rank_config[self.cur_btn_index]
			local x = 1
			self.node_list.moban_count_remain:SetActive(false)
			local rank_kind = self.rank_kind
			local plat_type = RoleWGData.Instance.role_vo.plat_type

			if rank_kind == RankKind.Person then
				if rank_cfg.kind == RankKind.Guild then
					rank_kind = rank_cfg.kind
				end
			elseif rank_kind == RankKind.Cross then
				plat_type = self.rank_item_data.plat_type
			end

			RankWGCtrl.Instance:SendRankDianZan(plat_type, self.rank_item_data.user_id,
				{ kind = rank_kind, rank_type = self.rank_type })
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Rank.Not_Count)
	end
end

--跨服进度
function RankView:OnClickTips()
	BiZuoWGCtrl.Instance:OpenCompoundCountryView()
end

--打开信息面板
function RankView:LookRoleInfo()
	if not self.item_listview_list then
		return
	end
	local curr_index = self.item_listview_list:GetSelectIndex()
	local cur_item = self.item_listview_list:GetItemAt(curr_index)
	if cur_item then
		cur_item:OnClickEvent()
	end
end

function RankView:OnClickCapCom()
	if self.rank_item_data == nil then
		return
	end

	local user_id = self.rank_item_data.user_id and self.rank_item_data.user_id or self.rank_item_data.tuan_zhang_uid
	CapabilityContrastWGCtrl:ReqCapabilityCmpCoress(self.rank_item_data.plat_type, user_id)
end

--清空模型数据
function RankView:ClearModelData()
	if nil ~= self.mount_model then
		self.mount_model:ClearModel()
	end

	if nil ~= self.role_model then
		self.role_model:ClearModel()
	end

	self:CleanXianQiModelAction()
end

--神兵模型动画
function RankView:PlayWeaponTween()
	if not self.tween_weapon then
		local tween_root = self.node_list["rank_display_model"].rect
		self.tween_weapon = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 50, 1)
		self.tween_weapon:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	else
		self.tween_weapon:Restart()
	end
end

function RankView:CancelWeaponTween()
	if self.tween_weapon then
		self.tween_weapon:Kill()
		self.tween_weapon = nil
		local tween_root = self.node_list["rank_display_model"]
		if tween_root then
			tween_root.rect.anchoredPosition = self.origin_display_pos
		end
	end
end

function RankView:ViewAnimation()
	if not self.do_rank_tween then
		return
	end
	local tween_info = UITween_CONSTS.RankSys

	local tween_root_1 = self.node_list.Left_Container
	local tween_root_2 = self.node_list.rank_mid_1

	UITween.CleanAllTween(GuideModuleName.Rank)
	UITween.FakeHideShow(tween_root_1)
	UITween.FakeHideShow(tween_root_2)

	RectTransform.SetAnchoredPositionXY(self.node_list.rank_left_list_bg.rect, -750, 5)
	RectTransform.SetSizeDeltaXY(self.node_list.display_type.rect, 87, 100)
	self.node_list.display_type:SetActive(false)
	self.node_list.display_type_name:SetActive(false)

	self.node_list.rank_left_list_bg.rect:DOAnchorPos(Vector2(-441, 5), tween_info.MoveTime)

	ReDelayCall(self, function()
		UITween.AlphaShow(GuideModuleName.Rank, tween_root_1, 0, tween_info.ToAlpha, tween_info.AlphaTime)
		self.node_list.display_type:SetActive(true)
		self.node_list.display_type.rect:DOSizeDelta(Vector2(87, 174), tween_info.AlphaTime):OnComplete(function()
			self.node_list.display_type_name:SetActive(true)
		end)

		UITween.AlphaShow(GuideModuleName.Rank, tween_root_2, 0, tween_info.ToAlpha, tween_info.AlphaTime)
		local list = self.item_listview_list:GetAllItems()
		local sort_list = {}

		for i, v in pairs(list) do
			local data = {}
			data.index = v:GetIndex()
			data.item = v
			sort_list[#sort_list + 1] = data
		end
		table.sort(sort_list, SortTools.KeyLowerSorter("index"))

		local count = 0
		local cur_index = 0
		for k, v in ipairs(sort_list) do
			if 0 ~= v.index then
				count = count + 1
			end
			v.item:PalyItemAnimator(count)
		end
	end, tween_info.AlphaDelay, "rank_tween_1")
	self.do_rank_tween = false
end

--设置仙器模型
function RankView:SetXianQiModel(info, xianqi_flag)
	local item_id
	-- print_error("FFFF===== xianqi_flag", xianqi_flag)
	xianqi_flag = xianqi_flag or 1
	-- print_error("FFF====== info.appearance", info.appearance.anqi_id, info.appearance.ruanjia_id)
	if not IsEmptyTable(info.appearance) and info.appearance.anqi_id and info.appearance.ruanjia_id then
		item_id = xianqi_flag == 1 and info.appearance.anqi_id or info.appearance.ruanjia_id
	end
	--print_error("FFFFFF========== item_id", item_id)
	if self.mount_model and item_id and item_id > 0 then
		self.mount_model:SetMainAsset(HiddenWeaponWGData.Instance:GetEquipModel(item_id))
		self:PlayXianQiModelAction(xianqi_flag)
	end
end

--仙器模型不带动作,所以让客户端做动画展示
function RankView:PlayXianQiModelAction(xianqi_flag)
	local obj = self.node_list["rank_display_model"]
	if obj then
		if not self.xianqi_model_start_pos then
			self.xianqi_model_start_pos = obj.transform.localPosition
		end

		if xianqi_flag == 1 then
			self.xianqi_model_tween = obj.transform:DOLocalRotate(Vector3(0, 360, 0), 5,
				DG.Tweening.RotateMode.FastBeyond360)
			self.xianqi_model_tween:SetEase(DG.Tweening.Ease.Linear)
			self.xianqi_model_tween:SetLoops(-1)
		else
			self.xianqi_model_tween = obj.transform:DOLocalMoveY(15, 1):SetEase(DG.Tweening.Ease.Linear):SetLoops(-1,
				DG.Tweening.LoopType.Yoyo)
		end
	end
end

function RankView:CleanXianQiModelAction()
	if self.xianqi_model_tween then
		local obj = self.node_list["rank_display_model"]
		if obj then
			obj.transform.localPosition = Vector3(self.xianqi_model_start_pos.x, self.xianqi_model_start_pos.y,
				self.xianqi_model_start_pos.z)
			obj.transform.localRotation = Quaternion.Euler(0, 0, 1)
		end

		self.xianqi_model_tween:Kill(true)
		self.xianqi_model_tween = nil
	end
end

function RankView:ToggleRankClick(rank_method, is_on)
	if is_on then
		if rank_method then
			self.rank_method = RankMethod.TopTen
		else
			self.rank_method = RankMethod.FullRankings
		end

		self.force_refresh = true -- 这里增加一个刷新，如果十大和完整角色不一样这里需要刷新一下，一样就不用刷新
		self:Flush(TabIndex.paihangbang)
	end
end

function RankView:FlushBackground(background_id)
	-- self.node_list.foundation:SetActive(background_id and background_id == 0)
	self.node_list.background_root:SetActive(background_id and background_id ~= 0)
	if background_id and background_id ~= 0 then
		local asset, bundle = nil, nil
		local back_data = BackgroundWGData.Instance:GetBigDataByID(background_id)

		self.node_list["background_root"]:SetActive(background_id ~= 0 and back_data ~= nil)

		if background_id ~= 0 and back_data then
			asset, bundle = ResPath.BackgroundShow(back_data.item_id)

			if not self.background_loader then
				local background_loader = AllocAsyncLoader(self, "base_tip_back_cell")
				background_loader:SetIsUseObjPool(true)
				background_loader:SetParent(self.node_list["background_root"].transform)
				self.background_loader = background_loader
			end
			self.background_loader:Load(asset, bundle)
		end
	end
end

-------------------------------------------------------------
ButtonItem = ButtonItem or BaseClass(BaseRender)

function ButtonItem:OnFlush()
	if not self.data then return end
	self.node_list.text_item.text.text = self.data.name_param
	self.node_list.text_item2.text.text = self.data.name_param
	self:OnSelectChange(self:IsSelectIndex()) -- 参数为当前是否选中  该方法存在基类中
end

function ButtonItem:OnSelectChange(is_select)
	self.node_list.bg_image:SetActive(not is_select)
	self.node_list.bg_image2:SetActive(is_select)
end
