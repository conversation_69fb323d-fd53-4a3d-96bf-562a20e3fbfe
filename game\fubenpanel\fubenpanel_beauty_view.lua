function FuBenPanelView:InitBeautyView()
	if not self.beauty_reward_list then
		self.beauty_reward_list = AsyncListView.New(FuBenBeautyRewardCell, self.node_list["beauty_reward_list"])
		self.beauty_reward_list:SetStartZeroIndex(true)
	end

	if not self.beauty_leader_reward_list then
		self.beauty_leader_reward_list = AsyncListView.New(FuBenBeautyRewardCell, self.node_list["beauty_leader_reward_list"])
		self.beauty_leader_reward_list:SetStartZeroIndex(true)
	end

	self.node_list["layout_beauty_combine_mark"].button:AddClickListener(BindTool.Bind1(self.OnClickBeautyCombine, self))
	self.node_list["btn_beauty_enter"].button:AddClickListener(BindTool.Bind1(self.OnClickBeautyBaoMingEnter,self))
end

function FuBenPanelView:DeleteBeautyView()
	if self.beauty_reward_list then
		self.beauty_reward_list:DeleteMe()
		self.beauty_reward_list = nil
	end

	if self.beauty_leader_reward_list then
		self.beauty_leader_reward_list:DeleteMe()
		self.beauty_leader_reward_list = nil
	end
end

function FuBenPanelView:FlushBeautyView()
    local cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenTypeModelCfg(GoalTeamType.FbBeautyType, 0)
	if not cfg then
		return 
	end

    self.node_list.desc_beauty_ads.text.text = cfg.title_desc
    self.node_list.beauty_fb_desc.text.text = cfg.fb_desc

    for i = 1, 5 do
		if Language.FuBenPanel.BeautyFuBenShuoming[i] then
			self.node_list["beauty_fb_info_sign"..i]:SetActive(true)
			self.node_list["beauty_fb_info"..i].text.text = Language.FuBenPanel.BeautyFuBenShuoming[i]
		else
			self.node_list["beauty_fb_info_sign"..i]:SetActive(false)
		end
	end

	self.beauty_reward_list:SetDataList(cfg.show_reward_item)
	self.beauty_leader_reward_list:SetDataList(cfg.leader_show_reward_item)
	self:FlushBeautyEnterCount()
end

function FuBenPanelView:FlushBeautyEnterCount()
    if self.node_list["beauty_enter_count"] then
		local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbBeautyType)
		local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbBeautyType)
		local max_count = team_type_cfg.max_times or 0
		local remain_times = max_count - enter_times
		self.node_list["beauty_enter_count"].text.color = Str2C3b(remain_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.L_RED)
        self.node_list["beauty_enter_count"].text.text = string.format(Language.FuBenPanel.FuBenEnterTime, remain_times, max_count)
    end

    local hook_type = FuBenWGData.Instance:GetCombineStatus(FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB2)
    self.node_list.layout_beauty_combine_hook:SetActive(hook_type == 1)
end

function FuBenPanelView:OnClickBeautyCombine()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = combine_cfg[FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB2 + 1].level_limit

    if need_level >= role_level then
        local level_des = RoleWGData.GetLevelString(need_level)
        local str = string.format(Language.FuBenPanel.CombineLimitTips, level_des)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbBeautyType)
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbBeautyType)
	local max_count = team_type_cfg.max_times or 0
	local remain_times = max_count - enter_times

    if remain_times < 2 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
        return
    end
	
    local vas = self.node_list.layout_beauty_combine_hook:GetActive()

    local is_combine = vas == true and 0 or 1
    if vas then
        FuBenWGCtrl.Instance:SendFBUseCombine(is_combine, FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB2)
    else
        local callback_func = function()
        end

        FuBenWGCtrl.Instance:ShowCombinePanel(FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB2, callback_func)
    end
end

function FuBenPanelView:OnClickBeautyBaoMingEnter()
	local team_type = GoalTeamType.FbBeautyType
	local fb_mode = 0

	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbBeautyType)
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbBeautyType)
	local max_count = team_type_cfg.max_times or 0
	local remain_count = max_count - enter_times
    if remain_count <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
        return
    end

    NewTeamWGCtrl.Instance:F2SendTeamFuBenEnter(team_type, fb_mode, 5)
end
---------------------FuBenBeautyRewardCell--------------------------------
FuBenBeautyRewardCell = FuBenBeautyRewardCell or BaseClass(BaseRender)
function FuBenBeautyRewardCell:__init()
	if not self.base_cell then
		self.base_cell = ItemCell.New(self.node_list["pos"])
		self.base_cell:SetIsShowTips(true)
	end
end

function FuBenBeautyRewardCell:__delete()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end

function FuBenBeautyRewardCell:OnFlush()
	if not self.data then
		return
	end

	self.base_cell:SetData(self.data)
    self.node_list.three_flag:SetActive(false)
end