----------------------------------------------------
--开服比拼
----------------------------------------------------

OpenServerCompetitionView = OpenServerCompetitionView or BaseClass(SafeBaseView)
local REWARD_SEQ = {
	GET_REACH = 1, --领取达标
	GET_GIFT = 2, -- 领取每日礼包
	BUY = 3     --每日购买
}

local ITEM_SCALE = Vector3(0.63, 0.63, 0.63)
local REWARD_ITEM_NUM = 5
local function InitRewardIconItem(data, item_list, maxshow)
	maxshow = maxshow or REWARD_ITEM_NUM
	local item_cfg = ItemWGData.Instance:GetGiftConfig(data.reward_item[0].item_id)
	for i = 1, REWARD_ITEM_NUM do
		if data.reward_item[i - 1] == nil or i > maxshow then
			item_list[i]:SetParentActive(false)
		else
			local itemdata = data.reward_item[i - 1]
			local tmp_data = {
				item_id = itemdata.item_id,
				num = itemdata.num,
				is_bind = itemdata.isbind,
				param = {
					star_level = itemdata.star,
					impression = itemdata.impression
				}
			}

			item_list[i]:SetData(tmp_data)
			item_list[i]:SetParentActive(true)
		end
	end
end

local star_value_index_map = {
	["zero_weight"] = 0,
	["one_weight"] = 1,
	["two_weight"] = 2,
	["three_weight"] = 3,
	["four_weight"] = 4,
}

local function GetEquipStarNum(get_way, getway_param)
	local equip_rand_attr_getway = ConfigManager.Instance:GetAutoConfig("equipforge_auto").equip_rand_attr_getway
	if type(equip_rand_attr_getway) ~= "table" then
		print_error("equip_rand_attr_getway error:", equip_rand_attr_getway, get_way, getway_param)
	end
	for _, v in pairs(equip_rand_attr_getway) do
		if (v.get_way == get_way and v.param_1 == getway_param) then
			for k1, v1 in pairs(star_value_index_map) do
				if v[k1] ~= nil and v[k1] ~= "" and v[k1] == 100 then
					return v1
				end
			end
		end
	end
end


local impression_value_index_map = {
	["weight_0"] = 0,
	["weight_1"] = 1,
	["weight_2"] = 2,
	["weight_3"] = 3,
	["weight_4"] = 4,
}

local function GetEquipImpressionNum(get_way, getway_param)
	local impression_get_way = ConfigManager.Instance:GetAutoConfig("equipforge_auto").impression_get_way
	if type(impression_get_way) ~= "table" then
		print_error("impression_get_way error:", impression_get_way, get_way, getway_param)
	end
	for _, v in pairs(impression_get_way) do
		if (v.get_way == get_way and v.param_1 == getway_param) then
			for k1, v1 in pairs(impression_value_index_map) do
				if v[k1] ~= nil and v[k1] ~= "" and v[k1] == 100 then
					return v1
				end
			end
		end
	end
end


local function GetReawrdItemDesc(data)
	local str = ""
	if type(data) ~= "table" then
		return str
	end
	local special_index = data.special_index
	local cur_rush_type = data.cur_rush_type
	str = ToColorStr(data.min_rank .. "-" .. data.max_rank, "#ff5a00") .. Language.Field1v1.Ming

	if data.max_rank == 999999 then
		str = string.format(Language.OpenServer.JiYiWai, data.min_rank .. Language.Field1v1.Ming)
	end
	str = Language.OpenServer.LingQuLimit[cur_rush_type] .. str
	local str2 = ""
	if data.is_last then
		str2 = Language.OpenServer.LingQuLimit2[cur_rush_type]
	end

	local opengame_info = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(cur_rush_type)
	local dec = ""

	if special_index then
		dec = ServerActivityWGData.Instance:GetRushReachGoal(cur_rush_type, special_index)
	else
		dec = data.reach_value
	end
	dec = CommonDataManager.ConverPowerByThousand(dec)


	str = str .. string.format(Language.OpenServer.LingQuDesc, dec, Language.OpenServer.Unit[cur_rush_type])
	if data.is_last then
		str = str2 .. string.format(Language.OpenServer.LingQuDesc2, dec, Language.OpenServer.Unit[cur_rush_type])
	end
	return str;
end

local OpenServerCompetitionStronger = BaseClass(SafeBaseView)

function OpenServerCompetitionView:__init(act_id)
	self.get_icon_list = {}
	self.act_id = act_id

	-- self.ui_config = {'uis/view/open_server_activity_ui_prefab','OpenServerActView'}
	self:AddViewResource(0, "uis/view/act_tab_view_prefab", "layout_open_server_competition")
	self.order = 3
	self.item_list = {}
end

function OpenServerCompetitionView:ReleaseCallBack()
	self.has_load = nil
	self.need_refresh = nil

	if self.open_server_competition_list ~= nil then
		self.open_server_competition_list:DeleteMe()
		self.open_server_competition_list = nil
	end
	if CountDownManager.Instance:HasCountDown("activity_end_time") then
		CountDownManager.Instance:RemoveCountDown("activity_end_time")
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	self:DeleteStrongerList()

	for i, v in ipairs(self.get_icon_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end
	self.get_icon_list = {}

	for k, v in pairs(self.item_list) do
		if v then
			v:DeleteMe()
		end
	end
	self.item_list = {}

	if self.daily_buy_list then
		self.daily_buy_list:DeleteMe()
		self.daily_buy_list = nil
	end

	if self.use_update then
		Runner.Instance:RemoveRunObj(self)
		self.use_update = nil
	end
	if self.footprint_eff_t then
		for k, v in pairs(self.footprint_eff_t) do
			v.role_model:OnRemoveGameObject(v.obj)
		end
		self.footprint_eff_t = nil
	end
	self.is_foot_view = nil
	self.next_create_footprint_time = nil

	self.keep_info_param = nil
	self.keep_view_info_param = nil
end

function OpenServerCompetitionView:SetActId(act_id)
	self.act_id = act_id
end

function OpenServerCompetitionView:LoadCallBack()
	for i = 1, REWARD_ITEM_NUM do
		local item_cell = ItemCell.New(self.node_list["ph_cell_" .. i])
		item_cell:SetItemTipFrom(ItemTip.KAIFU)
		self.node_list["ph_cell_" .. i].rect.localScale = ITEM_SCALE
		self.item_list[i] = item_cell
	end

	self.daily_buy_list = AsyncListView.New(OpenServerCompetitonDailyBuyItem, self.node_list["daily_buy_list"])

	self:InitView()
	self.has_load = true
	self.keep_info_param = nil
	self.keep_view_info_param = nil
end

function OpenServerCompetitionView:ShowIndexCallBack()
	-- ServerActivityWGData.Instance:SetOpenserverCommpetionRed(false)
	self:RefreshView()
end

function OpenServerCompetitionView:CloseCallBack()
	self.keep_view_info_param = nil
end

function OpenServerCompetitionView:InitView()
	self.node_list.gift_btn.animator:SetBool("is_shake", true)

	self.open_server_competition_list = AsyncListView.New(OpenServerCompetitionViewItemRender, self.node_list.ph_list)
	-- self.open_server_competition_list:Create(OpenServerCompetitionViewItemRender, self.node_list.ph_list)

	XUI.AddClickEventListener(self.node_list.btn_rank, BindTool.Bind1(self.OnClickRankView, self))
	XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind1(self.OnClickTip, self))
	XUI.AddClickEventListener(self.node_list.gift_btn, BindTool.Bind1(self.OnClickGift, self))
	XUI.AddClickEventListener(self.node_list.stronger_btn, BindTool.Bind1(self.OnClickStronger, self))
end

function OpenServerCompetitionView:FlushKeepInfo(act_id, oga_today_rush_rank, cur_rush_type, open_day, reward_flags,
												 fetch_flags)
	if self.keep_info_param ~= nil then
		if self.keep_info_param.act_id == act_id
			and self.keep_info_param.oga_today_rush_rank == oga_today_rush_rank
			and self.keep_info_param.cur_rush_type == cur_rush_type
			and self.keep_info_param.open_day == open_day then
			local str = table.concat(reward_flags, "", 1, 31)
			str = reward_flags[0] .. str

			local fetch_str = table.concat(fetch_flags, "", 1, 31)
			fetch_str = fetch_flags[0] .. str

			if self.keep_info_param.reward_flags == str
				and self.keep_info_param.fetch_flags == fetch_str then
				return
			end
		end
	end

	local reward_list_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRewardConfig(cur_rush_type)
	if next(reward_list_cfg) == nil then
		return
	end

	local reward_list = {}
	self.no1_reward = nil

	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(cur_rush_type)
	local act_close_day = opengame_cfg.close_day_index
	local str = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	local is_over = (str.hour >= 23 and act_close_day == open_day) or open_day > act_close_day

	local get_way, getway_params = ServerActivityWGData.Instance:GetGetWay(cur_rush_type)

	local prof = RoleWGData.Instance:GetRoleProf()
	for k, v in ipairs(reward_list_cfg) do
		local prof_equip = nil
		local tmp_v = __TableCopy(v)
		if not v.is_last then --排名奖励才显示职业武器
			prof_equip = tmp_v["reward_item_prof_" .. prof]
		end

		if prof_equip ~= nil then
			-- 需要将装备放在最前面（配置表reward_item是从0开始，不能用insert）
			-- 如果reward_item数目有5个最后一个会被移除
			for i = REWARD_ITEM_NUM - 1, 0, -1 do
				if i == 0 then
					prof_equip.star = GetEquipStarNum(get_way, getway_params[k])
					prof_equip.impression = GetEquipImpressionNum(get_way, getway_params[k])
					tmp_v.reward_item[i] = prof_equip
				elseif tmp_v.reward_item[i - 1] ~= nil then
					tmp_v.reward_item[i] = tmp_v.reward_item[i - 1]
				end
			end
		end
		tmp_v.cur_rush_type = cur_rush_type
		if k == 1 then
			self.no1_reward = tmp_v
		else
			reward_list[k - 1] = tmp_v
			reward_list[k - 1].is_over = is_over
			reward_list[k - 1].my_rank = oga_today_rush_rank
		end
	end

	ServerActivityWGData.Instance:SetOpenServerCompetitionClickRemind(cur_rush_type)
	local reward_flags = __TableCopy(ServerActivityWGData.Instance:GetOpenServerGoalFlags(cur_rush_type))
	local fetch_flags = __TableCopy(ServerActivityWGData.Instance:GetOpenServerGoalFetchFlags(cur_rush_type))

	local can_reward, index = ServerActivityWGData.Instance:OpenServerCompetitonSingleReward(cur_rush_type)

	if can_reward then
		self.open_server_competition_list:SetDataList(reward_list, 1)
	else
		self.open_server_competition_list:SetDataList(reward_list, 0)
	end

	local str = table.concat(reward_flags, "", 1, 31)
	str = reward_flags[0] .. str

	local fetch_str = table.concat(fetch_flags, "", 1, 31)
	fetch_str = fetch_flags[0] .. fetch_str

	self.keep_info_param = {
		act_id = self.act_id,
		oga_today_rush_rank = oga_today_rush_rank,
		cur_rush_type = cur_rush_type,
		open_day = open_day,
		reward_flags = str,
		fetch_flags = fetch_str
	}
end

function OpenServerCompetitionView:RefreshView(param_t)
	if param_t ~= nil and param_t.flush_param ~= nil and param_t.flush_param == "change_index" then
		return
	end

	if not self:IsOpen() then
		return
	end
	if not self.has_load then
		self.need_refresh = true
		return
	end

	local opengame_info = self:GetOpenServerListData()
	local cur_rush_type = self:GetOpenServerTodayRushData()
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(opengame_info.oga_today_rush_type)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local reward_flags = __TableCopy(ServerActivityWGData.Instance:GetOpenServerGoalFlags(cur_rush_type))
	local fetch_flags = __TableCopy(ServerActivityWGData.Instance:GetOpenServerGoalFetchFlags(cur_rush_type))

	-- 每日限购列表
	self.daily_buy_list:SetDataList(ServerActivityWGData.Instance:GetRushDailyBuyCfgByRushType(cur_rush_type))

	-- 旧逻辑，服务端说另一个排行榜数据变了，他是不会重新推的，所以先留着
	if opengame_cfg.close_day_index < open_day then
		local rank_cfg = RankWGData.Instance:GetActMyRank(opengame_cfg.rank_type)
		if rank_cfg then
			opengame_info.oga_today_rush_rank = rank_cfg.my_rank
			opengame_info.oga_today_rush_value = rank_cfg.rank_value
		end
	end

	self:FlushKeepInfo(self.act_id, opengame_info.oga_today_rush_rank, cur_rush_type, open_day, reward_flags, fetch_flags)
	self:FlushViewInfo(self.act_id, cur_rush_type, opengame_info.oga_today_rush_rank, opengame_info.oga_today_rush_type,
		opengame_info.oga_today_rush_value, open_day)
end

function OpenServerCompetitionView:FlushViewInfo(act_id, cur_rush_type, oga_today_rush_rank, oga_today_rush_type,
												 oga_today_rush_value, open_day)
	if CountDownManager.Instance:HasCountDown("activity_end_time") then
		CountDownManager.Instance:RemoveCountDown("activity_end_time")
	end


	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(oga_today_rush_type)

	local can_get = ServerActivityWGData.Instance:OpenServerCompetitonCanGetGift()
	self.node_list.gift_btn:SetActive(can_get)

	-- endregion

	local close_day = opengame_cfg.close_day_index + 1
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local format_time = os.date("*t", now_time)
	local end_time = os.time({
		year = format_time.year,
		month = format_time.month,
		day = format_time.day + close_day - open_day,
		hour = 0,
		min = 0,
		sec = 0
	})
	end_time = end_time - 3600

	if (now_time < end_time) then
		self:UpdateCountDown(now_time, end_time)
		CountDownManager.Instance:AddCountDown(
			"activity_end_time",
			BindTool.Bind1(self.UpdateCountDown, self),
			BindTool.Bind1(self.CompleteCountDown, self),
			end_time,
			nil,
			1
		)
	else
		if self.node_list.lbl_activity_time then
			self.node_list.lbl_activity_time.text.text = Language.Guild.MiJingEnd
		end
	end

	self:FlushViewKeepInfo(act_id, cur_rush_type, oga_today_rush_rank, oga_today_rush_type, oga_today_rush_value)
end

function OpenServerCompetitionView:FlushViewKeepInfo(act_id, cur_rush_type, oga_today_rush_rank, oga_today_rush_type,
													 oga_today_rush_value)
	if self.keep_view_info_param ~= nil then
		if self.keep_view_info_param.act_id == act_id
			and self.keep_view_info_param.oga_today_rush_rank == oga_today_rush_rank
			and self.keep_view_info_param.cur_rush_type == cur_rush_type
			and self.keep_view_info_param.oga_today_rush_type == oga_today_rush_type
			and self.keep_view_info_param.oga_today_rush_value == oga_today_rush_value then
			return
		end
	end

	self.is_foot_view = nil

	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(oga_today_rush_type)

	-- region初始化第一名的奖励
	InitRewardIconItem(self.no1_reward, self.item_list)

	local cur_rush_type = self.no1_reward.cur_rush_type
	self.node_list.no1_name_1.text.text = Language.OpenServer.LingQuLimit2[cur_rush_type]
	self.node_list.no1_name_2.text.text = string.format(Language.OpenServer.NO1Name_2, self.no1_reward.min_rank,
		self.no1_reward.max_rank)
	self.node_list.no1_name_4.text.text = CommonDataManager.ConverPowerByThousand(self.no1_reward.reach_value)
	self.node_list.no1_name_5.text.text = string.format(Language.OpenServer.NO1Name_5,
		Language.OpenServer.Unit[cur_rush_type])
	-- endregion

	-- region 显示模型
	local model_show = opengame_cfg.model_show
	local pos_list = string.split(opengame_cfg.model_position, "|")
	local rot_list = string.split(opengame_cfg.rotation, "|")
	local model_pos = Vector3(tonumber(pos_list[1]), tonumber(pos_list[2]), tonumber(pos_list[3]))
	local model_rot = Vector3(tonumber(rot_list[1]), tonumber(rot_list[2]), tonumber(rot_list[3]))
	local scale = opengame_cfg.scale
	local v3_scale = Vector3(scale, scale, scale)
	local show_type = opengame_cfg.show_type;

	local bundle_list = string.split(model_show, "|")
	if #bundle_list >= 2 then
		local is_image = show_type == 1
		self:SetShowModelActive(is_image)
		if show_type == 1 then -- 图片
			local b, a = bundle_list[1], bundle_list[2]
			self.node_list["img_title_display"].image:LoadSprite(b, a, function()
				XUI.ImageSetNativeSize(self.node_list["img_title_display"])
				self.node_list["img_title_display"].transform.localScale = v3_scale
			end)
			if bundle_list[3] and bundle_list[4] then
				self.node_list["img_title_effect"]:SetActive(true)
				local b, a = bundle_list[3], bundle_list[4]
				self.node_list["img_title_effect"]:ChangeAsset(b, a)
			else
				self.node_list["img_title_effect"]:SetActive(false)
			end
		else
			if not self.model_display then
				self.model_display = RoleModel.New()
				local display_data = {
					parent_node = self.node_list["ph_display"],
					camera_type = MODEL_CAMERA_TYPE.BASE,
					-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
					rt_scale_type = ModelRTSCaleType.M,
					can_drag = true,
				}
				
				self.model_display:SetRenderTexUI3DModel(display_data)
				-- self.model_display:SetUI3DModel(self.node_list.ph_display.transform,
				-- 	self.node_list.ph_display.event_trigger_listener)
			end

			self.model_display:RemoveWeapon()

			if show_type == 2 then --足迹
				local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
				if role_res_id then
					local bundle, asset = ResPath.GetRoleModel(role_res_id)
					self.model_display:SetMainAsset(bundle, asset)
					self.model_display:SetWeaponResid(weapon_res_id)
					self.model_display:PlayRoleAction("run")
					self.is_foot_view = true
					self.foot_res_info = {
						bundle = bundle_list[1],
						asset = bundle_list[2]
					}
					if not self.use_update then
						Runner.Instance:AddRunObj(self, 8)
						self.use_update = true
					end
				end
			elseif show_type == 3 then -- 一般模型
				local bundle_list = string.split(model_show, "|")
				self.model_display:SetMainAsset(bundle_list[1], bundle_list[2])
			end
			self.model_display:CustomDisplayPositionAndRotation(model_pos, model_rot, v3_scale)
		end
	else
		print_error("model_show is error", model_show)
	end

	local my_rank
	if -1 == oga_today_rush_rank or oga_today_rush_rank == 0 then
		my_rank = ToColorStr(Language.Rank.NoRank, "#FF0000")
	else
		my_rank = ToColorStr(oga_today_rush_rank, COLOR3B.GREEN)
	end


	local reward_list = ServerActivityWGData.Instance:GetOpenServerActivityRewardConfig(oga_today_rush_type)
	if oga_today_rush_rank <= reward_list[#reward_list].min_rank and -1 ~= oga_today_rush_rank then
		self.node_list.lbl_my_rank.text.text = my_rank --,COLOR3B.L_ORANGE)
	else
		self.node_list.lbl_my_rank.text.text = my_rank --,COLOR3B.RED)
	end

	local show_type = -1
	if oga_today_rush_type == RUSH_TYPE.RUSH_TYPE_MOUNT then
		-- show_type = 1
		show_type = 4
	elseif
		oga_today_rush_type == RUSH_TYPE.RUSH_TYPE_LINGCHONG or
		oga_today_rush_type == RUSH_TYPE.RUSH_TYPE_WING
	then
		-- show_type = 0
		show_type = 4
	elseif oga_today_rush_type == RUSH_TYPE.RUSH_TYPE_LEVEL then
		show_type = 3
	elseif oga_today_rush_type == RUSH_TYPE.RUSH_TYPE_CAPABILITY then
		show_type = 4
	elseif oga_today_rush_type == RUSH_TYPE.RUSH_TYPE_GEM then
		show_type = 5
	elseif oga_today_rush_type == RUSH_TYPE.RUSH_TYPE_RECHARGE then
		show_type = 100
	elseif oga_today_rush_type == RUSH_TYPE.RUSH_TYPE_XIAOFEICHONGBANG then
		show_type = 7
	end
	-- print_error("opengame_info.oga_today_rush_value:",opengame_info.oga_today_rush_value)

	if -1 == show_type then
		local value = math.floor(oga_today_rush_value / 10) + 1
		if value > 10 then
			value = 10
		end
		self.node_list.lbl_my_value.text.text = value
	elseif show_type == 3 or show_type == 4 or show_type == 5 or show_type == 7 then
		self.node_list.lbl_my_value.text.text = oga_today_rush_value
	elseif show_type == 100 then
		local value = oga_today_rush_value -- math.floor(/ 10)
		self.node_list.lbl_my_value.text.text = value
	else
		if oga_today_rush_value <= 0 then
			self.node_list.lbl_my_value.text.text = string.format(Language.Rank.Jie, 0, 0)
		else
			local temp_1, temp_2 = math.modf(oga_today_rush_value / 10)
			local value = oga_today_rush_value % 10
			if value == 0 then
				value = 10
			end
			if temp_2 > 0 then
				self.node_list.lbl_my_value.text.text =
					string.format(Language.Rank.Jie, math.floor(oga_today_rush_value / 10) + 1, value)
			else
				self.node_list.lbl_my_value.text.text =
					string.format(Language.Rank.Jie, math.floor(oga_today_rush_value / 10), value)
			end
		end
	end

	local cur_rush_type = oga_today_rush_type
	self.node_list.lbl_title_2.text.text = (Language.OpenServer.ILevel[cur_rush_type])

	self.keep_view_info_param = {
		act_id = self.act_id,
		oga_today_rush_rank = oga_today_rush_rank,
		cur_rush_type = cur_rush_type,
		oga_today_rush_type = oga_today_rush_type,
		oga_today_rush_value = oga_today_rush_value
	}
end

function OpenServerCompetitionView:Update(now_time, elapse_time)
	if not self.is_foot_view then
		return
	end
	if self.next_create_footprint_time == 0 then
		self:CreateFootPrint()
		self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
	end

	if self.next_create_footprint_time == nil then --初生时也是位置改变，不播
		self.next_create_footprint_time = 0
	end
	if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
		self.next_create_footprint_time = 0
	end
	self:UpdateFootprintPos()
end

function OpenServerCompetitionView:CreateFootPrint()
	if not self:IsOpen() then
		return
	end

	if nil == self.footprint_eff_t then
		self.footprint_eff_t = {}
	end
	local pos = self.model_display.draw_obj:GetRoot().transform
	if self.foot_res_info then
		local bundle, asset = self.foot_res_info.bundle, self.foot_res_info.asset
		EffectManager.Instance:PlayControlEffect(self, bundle, asset,
			Vector3(pos.position.x, pos.position.y, pos.position.z), nil, pos, nil, function(obj)
			if obj then
				if nil ~= obj then
					if self.model_display then
						table.insert(self.footprint_eff_t, { obj = obj, role_model = self.model_display })
						self.model_display:OnAddGameobject(obj)
					else
						ResPoolMgr:Release(obj)
					end
				end
			end
		end)
	end
	if #self.footprint_eff_t > 2 then
		local obj = table.remove(self.footprint_eff_t, 1)
		obj.role_model:OnRemoveGameObject(obj.obj)
	end
end

function OpenServerCompetitionView:UpdateFootprintPos()
	if nil == self.footprint_eff_t then return end
	for k, v in pairs(self.footprint_eff_t) do
		if not IsNil(v.obj) then
			local pos = v.obj.transform.localPosition
			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
		end
	end
end

function OpenServerCompetitionView:SetShowModelActive(is_image)
	self.node_list["ph_display"]:SetActive(not is_image)
	self.node_list["img_title_display"]:SetActive(is_image)
end

function OpenServerCompetitionView:UpdateCountDown(elapse_time, total_time)
	if self.node_list.lbl_activity_time then
		self.node_list.lbl_activity_time.text.text = (TimeUtil.FormatSecondDHM8(total_time - elapse_time))
	end
end

function OpenServerCompetitionView:CompleteCountDown()
	if self.node_list.lbl_activity_time then
		self.node_list.lbl_activity_time.text.text = ("已结束")
		ServerActivityWGCtrl.Instance:UpdateBtnListActTwo()
		self:RefreshView()
	end
end

function OpenServerCompetitionView:OnFlushRankData(type)
	if ServerActivityWGCtrl.Instance.recharge_rank_view:IsOpen() then
		ServerActivityWGCtrl.Instance.recharge_rank_view:Flush()
	end
end

function OpenServerCompetitionView:OnClickRankView()
	local ranktype_day = self:GetOpenServerTodayRushData()
	RankWGCtrl.Instance:OpenOpenServerRankView(ranktype_day)
end

function OpenServerCompetitionView:OnClickTip()
	local cur_rusn_type = self:GetOpenServerTodayRushData()
	RuleTip.Instance:SetContent(
		string.format(Language.OpenServer.OpenServerDesc[1], Language.OpenServer.TitleTextList[cur_rusn_type]),
		Language.OpenServer.OpenServerTitle[cur_rusn_type]
	)
end

-- 请求服务器（每日礼包，达标奖励,每日购买）
function OpenServerCompetitionView:RequestServerReward(reward_seq)
	local cur_rusn_type = self:GetOpenServerTodayRushData()
	ServerActivityWGCtrl.Instance:SendOpenGameActivityFetchReward(
		OPEN_SERVER_REWARD_TYPE.REWARD_TYPE_RUSH_RANK_REWARD,
		reward_seq,
		cur_rusn_type,
		0
	)
end

-- 点击礼品按钮
function OpenServerCompetitionView:OnClickGift()
	self:RequestServerReward(REWARD_SEQ.GET_GIFT)
end

function OpenServerCompetitionView:DeleteStrongerList()
	if nil ~= self.strongerList then
		self.strongerList:DeleteMe()
		self.strongerList = nil
	end
end

-- 点击变强按钮
function OpenServerCompetitionView:OnClickStronger()
	self:DeleteStrongerList()
	self.strongerList = OpenServerCompetitionStronger.New()
	self.strongerList:SetCurRushType(self:GetOpenServerTodayRushData())
	local btn_rect = self.node_list.stronger_btn.rect
	local screen_pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, btn_rect.position)
	self.strongerList:SetScreenPosition(screen_pos)
	self.strongerList:Open()
end

function OpenServerCompetitionView:GetOpenServerTodayRushData()
	local act_data = self:GetOpenServerListData()
	if act_data ~= nil then
		return act_data.oga_today_rush_type
	end
end

function OpenServerCompetitionView:GetOpenServerListData()
	if ACT_RUSH_TYPE[self.act_id] == nil then
		return
	end
	local act_data = ServerActivityWGData.Instance:GetOpenServerListData(ACT_RUSH_TYPE[self.act_id])
	if act_data ~= nil then
		return act_data
	end
end

-- region CompetitionItem 本界面目前不使用 但是其他界面有使用
-----------------------CompetitionItem--------------------------------------------------------------
CompetitionItem = CompetitionItem or BaseClass(BaseRender)

function CompetitionItem:AddClick(activity_id, open_cfg)
	self.open_cfg = open_cfg
	self.activity_id = activity_id
end

function CompetitionItem:ReleaseCallBack()
	self.open_cfg = nil
	self.activity_id = nil
	self.image_cfg = nil
	self.bottom_icon = nil
end

function CompetitionItem:SetIconPath(asset, name, scale)
	if not self.is_load_complete then
		self.image_cfg = { asset = asset, name = name }
		return
	end
	--if self.node_list then
	self.node_list.icon.image:LoadSprite(asset, name)
	if scale then
		self.node_list.icon.transform.localScale = Vector3(scale, scale, scale)
	end
	--else
	--self.image_cfg = {asset = asset,name = name}
	--end
end

function CompetitionItem:SetRightBottomText(text)
	if self.node_list then
		self.node_list.bottom_icon.text.text = text
	else
		self.bottom_icon = text
	end
end

function CompetitionItem:SetVisible(enabled)
	if self.node_list then
		BaseRender.SetVisible(self, enabled)
	else
		self.active = enabled
	end
end

function CompetitionItem:LoadCallBack()
	self.is_load_complete = true
	XUI.AddClickEventListener(self.node_list.icon, BindTool.Bind(self.OnClickGoToGet, self))
	if self.image_cfg then
		self:SetIconPath(self.image_cfg.asset, self.image_cfg.name)
		self.image_cfg = nil
	end
	if self.bottom_icon then
		self.node_list.bottom_icon.text.text = self.bottom_icon
	end

	if not self.active then
		self:SetVisible(self.active)
	end
end

function CompetitionItem:OnClickGoToGet()
	if self.activity_id ~= "" and self.activity_id > 0 and ActivityWGData.Instance:GetActivityIsOpen(self.activity_id) then
		ActivityWGCtrl.Instance:SendActivityEnterReq(self.activity_id)
	elseif self.open_cfg == "guajimap" then
		local data = TaskWGData.Instance:GetVirtualGuajiTask()
		if not data then
			return
		end
		local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
		if YunbiaoWGData.Instance:GetIsHuShong() then
			TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
			return
		end

		GuajiWGCtrl.Instance:ResetMoveCache()
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(
			function()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		)

		local monster_obj = Scene.Instance:SelectMinDisMonster(data.monster_id)
		if monster_obj then
			MoveCache.SetEndType(MoveEndType.FightByMonsterId)
			GuajiCache.monster_id = data.monster_id
			MoveCache.param1 = data.monster_id

			local range = BossWGData.Instance:GetMonsterRangeByid(data.monster_id)
			GuajiWGCtrl.Instance:MoveToPos(data.scene_id, data.x, data.y, range)
		elseif main_role_vo.vip_level > 0 then
			TaskWGCtrl.Instance:JumpFly(data.scene_id, data.x, data.y, true)
		else
			local item_index = ItemWGData.Instance:GetItemIndex(COMMON_CONSTS.FLY_PROP_ID)
			if -1 ~= item_index then
				TaskWGCtrl.Instance:JumpFly(data.scene_id, data.x, data.y, true)
			else
				TaskWGCtrl.Instance:FlySceneToMove(data.scene_id, data.x, data.y)
			end
		end

		ViewManager.Instance:Close(GuideModuleName.ServerActivityTabView)
	else
		ViewManager.Instance:Close(GuideModuleName.ServerActivityTabView)
		FunOpen.Instance:OpenViewNameByCfg(self.open_cfg)
	end
end

-- endregion

----------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------
-- region OpenServerCompetitionViewItemRender 奖励item
OpenServerCompetitionViewItemRender = OpenServerCompetitionViewItemRender or BaseClass(BaseRender)
function OpenServerCompetitionViewItemRender:__init()
	-- 特殊索引标记   2倒数第2个 1倒数第1个 (最后两个达标奖励)
	self.special_index = 0
	self:CreateChild()
end

function OpenServerCompetitionViewItemRender:__delete()
	for i, v in ipairs(self.item_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end

	self.item_list = {}
end

function OpenServerCompetitionViewItemRender:CreateChild()
	self.item_list = {}
	for i = 1, 5 do
		local item_cell = ItemCell.New(self.node_list["ph_cell_" .. i])
		item_cell:SetItemTipFrom(ItemTip.KAIFU)
		self.node_list["ph_cell_" .. i].rect.localScale = ITEM_SCALE
		self.item_list[i] = item_cell
	end
end

function OpenServerCompetitionViewItemRender:SetSpecialIndex()
	self.special_index = self.data.special_index or 0
end

function OpenServerCompetitionViewItemRender:OnFlush()
	if nil == self.data then
		return
	end
	self:SetSpecialIndex()
	InitRewardIconItem(self.data, self.item_list, 4) --最多只显示4个

	local str = GetReawrdItemDesc(self.data)
	self.node_list.lbl_desc.text.text = (str)

	local show_wait_over = false -- 显示待结算
	local show_get_reward = false -- 显示领奖按钮
	local show_complete = false -- 显示完成
	local show_no_reach = false -- 显示未完成
	local show_is_over = false -- 显示已经结束
	local show_have_send = false -- 显示已发奖

	-- 目标奖励
	if self.data.special_index then
		local can_reward = ServerActivityWGData.Instance:GetRushGoldCanReward(self.data.cur_rush_type,
			self.data.special_index)
		local fetch = ServerActivityWGData.Instance:GetRushGoldRewardFetch(self.data.cur_rush_type,
			self.data.special_index)
		-- 未达标
		if can_reward == false then
			show_no_reach = true
			-- 可领取
		elseif can_reward and not fetch then
			show_get_reward = true
			-- 已领取
		else
			show_complete = true
		end

		-- 排名奖励
	else --只有排名奖励才会进入显示等待结算和已结束
		if self.data.is_over then
			if self.data.my_rank >= self.data.min_rank and self.data.my_rank <= self.data.max_rank then
				show_have_send = true
			else
				show_is_over = true
			end
		else
			show_wait_over = true
		end
	end
	self.node_list.wait_over:SetActive(show_wait_over)
	self.node_list.getreward_btn:SetActive(show_get_reward)
	self.node_list.complete:SetActive(show_complete)
	self.node_list.no_reach:SetActive(show_no_reach)
	self.node_list.is_over:SetActive(show_is_over)
	self.node_list.have_send:SetActive(show_have_send)
	XUI.AddClickEventListener(self.node_list.getreward_btn, BindTool.Bind1(self.OnClickGetReward, self))
end

--领取奖励
function OpenServerCompetitionViewItemRender:OnClickGetReward()
	print(
		"OnClickGetReward()",
		OPEN_SERVER_REWARD_TYPE.REWARD_TYPE_RUSH_RANK_REWARD,
		REWARD_SEQ.GET_REACH,
		self.data.cur_rush_type,
		self.special_index
	)

	-- 领取目标奖励
	if self.data.special_index then
		ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.OGA_RUSH_RANK_OP_TYPE_FETCH_REWARD,
			self.data.cur_rush_type, self.data.special_index)

		-- 领取排名奖励
	else
		ServerActivityWGCtrl.Instance:SendOpenGameActivityFetchReward(
			OPEN_SERVER_REWARD_TYPE.REWARD_TYPE_RUSH_RANK_REWARD,
			REWARD_SEQ.GET_REACH,
			self.data.cur_rush_type,
			self.special_index
		)
	end
end

-- endregion

-- region 打开变强列表
local OpenServerCompetitonStrongerItem = BaseClass(BaseRender)
local COUNT = 5 --每次打开最多显示5个

function OpenServerCompetitionStronger:__init()
	local bundle_name, asset_name = "uis/view/act_tab_view_prefab", "layout_actbtn_list2"
	-- self:LoadAsset(bundle_name, asset_name, instance.transform)
	self:AddViewResource(0, bundle_name, asset_name)
	self.rush_type = 0
end

function OpenServerCompetitionStronger:SetCurRushType(rush_type)
	self.rush_type = rush_type
end

function OpenServerCompetitionStronger:SetScreenPosition(s_pos)
	self.s_pos = s_pos
end

function OpenServerCompetitionStronger:__delete()
end

function OpenServerCompetitionStronger:LoadCallBack()
	self.btn_list = AsyncListView.New(OpenServerCompetitonStrongerItem, self.node_list.ph_btn_list)
	-- self.btn_list:Create(OpenServerCompetitonStrongerItem, self.node_list.ph_btn_list)
	-- self.btn_list:SetSelectCallBack(BindTool.Bind(self.OnClickOpen, self))
	if self.s_pos then
		local rect = self.node_list.layout_actbtn_list2.layout_actbtn_list2.rect
		local p_rect =
			self.node_list.layout_actbtn_list2.layout_actbtn_list2.transform.parent:GetComponent(
				typeof(UnityEngine.RectTransform)
			)
		local width = rect.rect.width
		local height = rect.rect.height
		local _, local_pos =
			UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(p_rect, self.s_pos, UICamera,
				Vector2(0, 0))
		local posx = local_pos.x - width
		local posy = local_pos.y
		rect.localPosition = Vector2(posx, posy)
	end
end

function OpenServerCompetitionStronger:ReleaseCallBack()
	if self.btn_list then
		self.btn_list:DeleteMe()
		self.btn_list = nil
	end
end

function OpenServerCompetitionStronger:OnFlush()
	local icon_list = {}
	local icon_info = ServerActivityWGData.Instance:GetOpenGameActivityConfig().icon_jump
	local cur_rush_type = self.rush_type
	for i, v in ipairs(icon_info) do
		if v.rush_type == cur_rush_type then
			table.insert(icon_list, v)
		end
	end
	self.btn_list:SetDataList(icon_list)
end

function OpenServerCompetitionStronger:OnClickOpen(item)
	if nil == item.data then
		return
	end
	local activity_id = item.data.activity_id
	local open_cfg = item.data.param_1
	if activity_id ~= "" and activity_id > 0 and ActivityWGData.Instance:GetActivityIsOpen(activity_id) then
		ActivityWGCtrl.Instance:SendActivityEnterReq(activity_id)
	elseif open_cfg == "guajimap" then
		local data = TaskWGData.Instance:GetVirtualGuajiTask()
		if not data then
			return
		end
		local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
		if YunbiaoWGData.Instance:GetIsHuShong() then
			TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
			return
		end

		GuajiWGCtrl.Instance:ResetMoveCache()
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(
			function()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		)

		local monster_obj = Scene.Instance:SelectMinDisMonster(data.monster_id)
		if monster_obj then
			MoveCache.SetEndType(MoveEndType.FightByMonsterId)
			GuajiCache.monster_id = data.monster_id
			MoveCache.param1 = data.monster_id

			local range = BossWGData.Instance:GetMonsterRangeByid(data.monster_id)
			GuajiWGCtrl.Instance:MoveToPos(data.scene_id, data.x, data.y, range)
		elseif main_role_vo.vip_level > 0 then
			TaskWGCtrl.Instance:JumpFly(data.scene_id, data.x, data.y, true)
		else
			local item_index = ItemWGData.Instance:GetItemIndex(COMMON_CONSTS.FLY_PROP_ID)
			if -1 ~= item_index then
				TaskWGCtrl.Instance:JumpFly(data.scene_id, data.x, data.y, true)
			else
				TaskWGCtrl.Instance:FlySceneToMove(data.scene_id, data.x, data.y)
			end
		end

		ViewManager.Instance:Close(GuideModuleName.ServerActivityTabView)
	elseif open_cfg == "husong" then
		ActIvityHallWGCtrl.Instance:DoHuSong()
		-- ViewManager.Instance:Close(GuideModuleName.ServerActivityTabView)
	else
		-- ViewManager.Instance:Close(GuideModuleName.ServerActivityTabView)
		FunOpen.Instance:OpenViewNameByCfg(open_cfg)
	end
	self:Close()
end

function OpenServerCompetitonStrongerItem:OnFlush()
	if self.data then
		self.node_list["lbl_caozuo_name"].text.text = self.data.icon_describe
	end
end

--endregion

--------------------------- 每日限购item -------------------------------
OpenServerCompetitonDailyBuyItem = OpenServerCompetitonDailyBuyItem or BaseClass(BaseRender)
function OpenServerCompetitonDailyBuyItem:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])

	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind1(self.OnClickBuy, self))
end

function OpenServerCompetitonDailyBuyItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function OpenServerCompetitonDailyBuyItem:OnFlush()
	self.item_cell:SetData(self.data.daily_buy_item[0])

	self.node_list["orignal_pricevalue"].text.text = self.data.old_gold or 0                                             -- 原价

	self.node_list["cur_pricevalue"].text.text = self.data.need_gold or 0                                                -- 现价

	self.node_list["prop_name"].text.text = ItemWGData.Instance:GetItemName(self.data.daily_buy_item[0].item_id, nil,
		true)                                                                                                            -- 道具名称

	local limit_times = self.data.daily_buy_times or 1                                                                   -- 限购次数
	local buy_times = ServerActivityWGData.Instance:GetCompetitionDailyBuyTimes(self.data.rush_type, self.data.index)    -- 已购买次数

	self.node_list["buy_limit_times"].text.text = string.format(Language.OpenServer.DailyBuyLimit, buy_times, limit_times) -- 限购次数

	local can_buy = ServerActivityWGData.Instance:GetCompetitionDailyCanBuy(self.data.rush_type, self.data.index)
	XUI.SetButtonEnabled(self.node_list["btn_buy"], can_buy)                                              -- 购买按钮置灰

	self.node_list["buy_btn_text"].text.text = can_buy and Language.Common.GouMai or Language.Common
	.SellOut                                                                                              -- 按钮文字
end

-- 点击购买按钮
function OpenServerCompetitonDailyBuyItem:OnClickBuy()
	ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.OGA_RUSH_RANK_OP_TYPE_DAILY_BUY,
		self.data.rush_type, self.data.index - 1)
end
