﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Rendering_Universal_DecalProjectorWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Rendering.Universal.DecalProjector), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>("Validate", Validate);
		<PERSON><PERSON>("IsValid", IsValid);
		L<PERSON>unction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON>.Reg<PERSON>ar("material", get_material, set_material);
		<PERSON><PERSON>("drawDistance", get_drawDistance, set_drawDistance);
		<PERSON><PERSON>("fadeScale", get_fadeScale, set_fadeScale);
		<PERSON><PERSON>("startAngleFade", get_startAngleFade, set_startAngleFade);
		<PERSON><PERSON>("endAngleFade", get_endAngleFade, set_endAngleFade);
		<PERSON><PERSON>("uvScale", get_uvScale, set_uvScale);
		<PERSON><PERSON>("uvBias", get_uvBias, set_uvBias);
		<PERSON><PERSON>Var("scaleMode", get_scaleMode, set_scaleMode);
		L.RegVar("pivot", get_pivot, set_pivot);
		L.RegVar("size", get_size, set_size);
		L.RegVar("fadeFactor", get_fadeFactor, set_fadeFactor);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Validate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)ToLua.CheckObject<UnityEngine.Rendering.Universal.DecalProjector>(L, 1);
			obj.Validate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsValid(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)ToLua.CheckObject<UnityEngine.Rendering.Universal.DecalProjector>(L, 1);
			bool o = obj.IsValid();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_material(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Material ret = obj.material;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index material on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_drawDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float ret = obj.drawDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index drawDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fadeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float ret = obj.fadeScale;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startAngleFade(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float ret = obj.startAngleFade;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startAngleFade on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_endAngleFade(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float ret = obj.endAngleFade;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endAngleFade on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uvScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Vector2 ret = obj.uvScale;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uvBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Vector2 ret = obj.uvBias;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvBias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scaleMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Rendering.Universal.DecalScaleMode ret = obj.scaleMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scaleMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pivot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Vector3 ret = obj.pivot;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pivot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_size(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Vector3 ret = obj.size;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index size on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fadeFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float ret = obj.fadeFactor;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_material(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.material = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index material on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_drawDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.drawDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index drawDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fadeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.fadeScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startAngleFade(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.startAngleFade = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startAngleFade on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_endAngleFade(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.endAngleFade = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endAngleFade on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_uvScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.uvScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_uvBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.uvBias = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvBias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_scaleMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Rendering.Universal.DecalScaleMode arg0 = (UnityEngine.Rendering.Universal.DecalScaleMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.Universal.DecalScaleMode));
			obj.scaleMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scaleMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pivot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.pivot = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pivot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_size(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.size = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index size on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fadeFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.DecalProjector obj = (UnityEngine.Rendering.Universal.DecalProjector)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.fadeFactor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeFactor on a nil value");
		}
	}
}

