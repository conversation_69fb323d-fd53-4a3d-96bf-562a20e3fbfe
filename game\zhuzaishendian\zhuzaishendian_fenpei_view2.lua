ZhuZaiShenDianFenPeiView = ZhuZaiShenDianFenPeiView or BaseClass(SafeBaseView)

function ZhuZaiShenDianFenPeiView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/zhuzaishendian_ui_prefab", "layout_zhuzaishendian_fenpei")
	self.data = nil
end

function ZhuZaiShenDianFenPeiView:__delete()
end

function ZhuZaiShenDianFenPeiView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(967,600)
	self:SetSecondView(Vector2(870,550))
	self.node_list.title_view_name.text.text = Language.Field1v1.FenPeiPanelName
	self.menber_list = AsyncListView.New(MenberRender, self.node_list.right_list)
	self.menber_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectMemberListItemHandler, self))
	self.reward_bag = AsyncBaseGrid.New()
	self.reward_bag:CreateCells({col = 5, cell_count = 20, itemRender = FenPeiBagCell, list_view = self.node_list["left_list"]})
	self.reward_bag:SetStartZeroIndex(false)
	self.reward_bag:SetSelectCallBack(BindTool.Bind1(self.SelectBagCellCallBack, self))
	XUI.AddClickEventListener(self.node_list["fenpei_btn"], BindTool.Bind(self.OnClickBtn, self))
	XUI.AddClickEventListener(self.node_list["fenpei_record_btn"], BindTool.Bind(self.OnClickRecordBtn, self))
end

function ZhuZaiShenDianFenPeiView:ReleaseCallBack()
	if self.menber_list then
		self.menber_list:DeleteMe()
		self.menber_list = nil
	end

	if self.reward_bag then
		self.reward_bag:DeleteMe()
		self.reward_bag = nil
	end

	self.role_data = nil
	self.materia_num_data = nil
	self.temp_data_list = nil
end

function ZhuZaiShenDianFenPeiView:OnFlush()
	if not self.data then return end
	self.temp_data_list = {}
	local allocate_list = ZhuZaiShenDianWGData.Instance:GetFecthFlag()
	for k,v in pairs(self.data) do
		local temp_data = {}
		temp_data.item_id = v.item_id
		temp_data.is_bind = v.is_bind
		temp_data.index = k
		temp_data.num = v.num - (allocate_list[k] or 0)
		table.insert(self.temp_data_list,temp_data)
	end
	self.reward_bag:SetDataList(self.temp_data_list,2)

	local m_list = GuildDataConst.GUILD_MEMBER_LIST
	self.member_datasource = {}
	for i = 1, m_list.count do
		local item = m_list.list[i]
		local datasource = {uid = item.uid, role_name = item.role_name, level = item.level, sex = item.sex, prof = item.prof,
			post = item.post, vip_type = item.vip_type, vip_level = item.vip_level, is_online = item.is_online, join_time = item.join_time,
			guild_battle_allocate_time = item.guild_battle_allocate_time or 0 ,
			last_login_time = item.last_login_time, gongxian = item.gongxian, total_gongxian = item.total_gongxian, capability = item.capability}
		table.insert(self.member_datasource, datasource)
	end
	if nil ~= self.menber_list then
		table.sort(self.member_datasource, BindTool.Bind1(self.SortMemberList, self))
		self.menber_list:SetDataList(self.member_datasource,2)
	end
end

function ZhuZaiShenDianFenPeiView:SetBagData(data)
	self.data = data
end

function ZhuZaiShenDianFenPeiView:SelectBagCellCallBack(item)
	-- local data = item.data
	--if data.uid then
	--data.index1 = item.select_index
		self.materia_num_data = item.data
	--end
end

function ZhuZaiShenDianFenPeiView:OnSelectMemberListItemHandler( item )
	local data = item.data
	self.role_data = data
end

function ZhuZaiShenDianFenPeiView:OnClickRecordBtn()
	ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleKeepWinRewardAllocateRecord()
	ZhuZaiShenDianWGCtrl.Instance:OpenAllocationRecordView()
end

function ZhuZaiShenDianFenPeiView:OnClickBtn( )
	local  firstguild_id = {}
	firstguild_id = ZhuZaiShenDianWGData.Instance:GetGuildId()

	if nil == self.role_data then														--没有选择成员
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.PleaseSelectRole)
		return
	end

	if nil == self.materia_num_data then												--没有选中物品
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.PleaseSelectMaterial)
		return
	end

	if self.role_data then			--该成员分配次数超过配置次数
		for k,v in pairs(self.member_datasource) do
			if self.role_data.role_name == v.role_name then
				if v.guild_battle_allocate_time >= 5 then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.CountNunLimit)
					return
				end
			end
		end
	end

	if self.materia_num_data then					--物品数量为0
		for k,v in pairs(self.temp_data_list) do
			if self.materia_num_data.index == v.index then
				if v.num <= 0 then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.MaterialNunLimit)
					return
				end
			end
		end
	end

	if self.role_data.uid and next(firstguild_id) then
 		ZhuZaiShenDianWGCtrl.Instance:SendCSGuildAppointKeepWinReward(firstguild_id[1].guild_id, self.role_data.uid, self.materia_num_data.index)
 	end
 	-- self:Close()
end

-- 排序成员列表
function ZhuZaiShenDianFenPeiView:SortMemberList(a, b)
	local a_post = GuildDataConst.GUILD_POST_AUTHORITY_LIST[a.post]
	local b_post = GuildDataConst.GUILD_POST_AUTHORITY_LIST[b.post]


	if nil == a_post or nil == b_post then
		return false
	end

	if a.is_online == b.is_online then
		if a.is_online == 0 then
			return a.last_login_time > b.last_login_time
		else
			if a_post.post_index == b_post.post_index then
				return a.capability > b.capability
			else
				return a_post.post_index < b_post.post_index
			end
		end
	else
		return a.is_online > b.is_online
	end

end

------------------------------------------------------------成員列表------------------------------------------------------------

MenberRender = MenberRender or BaseClass(BaseRender)

function MenberRender:__init()
	-- body
end

function MenberRender:__delete()
	-- body
end

function MenberRender:OnFlush()
	local count = ZhuZaiShenDianWGData.Instance:GetRecordTimes()

	self.node_list.name_text.text.text = self.data.role_name
	self.node_list.kefenpei_text.text.text = string.format(Language.Field1v1.LiuJieFenPeiCount,count - self.data.guild_battle_allocate_time,count)
end

-- 选择状态改变
function MenberRender:OnSelectChange(is_select)
	self.node_list.selece_image:SetActive(is_select)
end

------------------------------------------------------------獎勵列表------------------------------------------------------------

FenPeiBagCell = FenPeiBagCell or BaseClass(ItemCell)

function FenPeiBagCell:__init()

end

function FenPeiBagCell:__delete()

end

function FenPeiBagCell:OnFlush()
	ItemCell.OnFlush(self)
	if nil == self.data.item_id then
		return
	end

	local show_num = self.data.num
	self:SetRightBottomTextVisible(true)
	self:SetRightBottomText(show_num)
end
------------------------------------------------------------分配记录------------------------------------------------------------


AllocationRecordView = AllocationRecordView or BaseClass(SafeBaseView)

function AllocationRecordView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/kuafu_guild_battle_ui_prefab", "FenPeiRecord")
end

function AllocationRecordView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(700,450)
	self.node_list.title_view_name.text.text = Language.Field1v1.FenPeiRecordPanelName
	self.record_list = AsyncListView.New(AllocationRecordRender, self.node_list.record_list)
end

function AllocationRecordView:ShowIndexCallBack()
	self:Flush()
end

function AllocationRecordView:ReleaseCallBack()
	if self.record_list then
		self.record_list:DeleteMe()
		self.record_list = nil
	end
end

function AllocationRecordView:OnFlush()
	local record_data = ZhuZaiShenDianWGData.Instance:GetAllocationRecordInfo()
	if not IsEmptyTable(record_data) then
		table.sort( record_data, SortTools.KeyUpperSorter("allocate_timestamp") )
	end
	self.record_list:SetDataList(record_data,0)
end


------------------------------------------------------------AllocationRecordRender------------------------------------------------------------

AllocationRecordRender = AllocationRecordRender or BaseClass(BaseRender)

function AllocationRecordRender:__init()
	-- body
end

function AllocationRecordRender:OnFlush()
	local time = os.date("%m-%d %X", self.data.allocate_timestamp)
	local item_config = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local item_msg = item_config.name
	self.node_list.time_date.text.text = "["..time.."]"
	self.node_list.contenr.text.text = string.format(Language.Field1v1.FenPeiRecord,RoleWGData.Instance.role_vo.name,
		ITEM_COLOR[item_config.color],item_msg,self.data.to_user)

end
