-- 鸭子详情tips
DuckDetailsTips = DuckDetailsTips or BaseClass(SafeBaseView)

function DuckDetailsTips:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/duck_race_ui_prefab", "duck_details_layout")
end

function DuckDetailsTips:__delete()

end

function DuckDetailsTips:OpenCallBack()
end

function DuckDetailsTips:CloseCallBack()

end

function DuckDetailsTips:SetData(duck_info)
	self.duck_info = duck_info
end

function DuckDetailsTips:LoadCallBack()
end

function DuckDetailsTips:ReleaseCallBack()
end

function DuckDetailsTips:OnFlush()
	local duck_cfg = DuckRaceWGData.Instance:GetDuckCfg(self.duck_info.duck_id)
	local monster_cfg = BossWGData.Instance:GetMonsterInfo(DuckRaceWGData.Instance:GetDuckMonsterId(self.duck_info.index))
	local trait_cfg = DuckRaceWGData.Instance:GetDuckTraitCfg(self.duck_info.index)
	-- self.node_list["title"].text.text = string.format(Language.DuckRace.DetailsTitle, monster_cfg.name)
	self.node_list["name"].text.text = string.format(Language.DuckRace.DetailsName, monster_cfg.name)
	self.node_list["weight"].text.text = string.format(Language.DuckRace.DetailsWeight, trait_cfg.weight)
	self.node_list["hobby"].text.text = string.format(Language.DuckRace.DetailsHobby, trait_cfg.hobby)
	self.node_list["win_times"].text.text = string.format(Language.DuckRace.DetailsWinTimes, self.duck_info.week_win_times)
	self.node_list["tili"].text.text = string.format(Language.DuckRace.DetailsTili, duck_cfg.tili)
	self.node_list["xingfen"].text.text = string.format(Language.DuckRace.DetailsXingfen, duck_cfg.xingfen)
	self.node_list["bottom_desc"].text.text = trait_cfg.tips_desc
end
