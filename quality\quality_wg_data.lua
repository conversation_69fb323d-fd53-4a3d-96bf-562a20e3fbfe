QualityWGData = QualityWGData or BaseClass()

QualityPolicy =
{
	UnitQuality = 1, 	-- 优先保证单位品质
	UnitCount = 2, 		-- 优先保证单位数量
}

--  0.高配 1.中配 2.低配 3.超低配
QualityWGData.SceneObjMaxAppearConut =
{											--0.高配    1.中配    2.低配    3.超低配  4.极限配置
	[ShieldObjType.Role] = 					{[0] = 20, [1] = 10, [2] = 5,  [3] = 2,  [4] = 1},
	[ShieldObjType.SkillShower] = 			{[0] = 10, [1] = 8, [2] = 6, [3] = 4,  [4] = 2},
	[ShieldObjType.Monster] = 				{[0] = 25, [1] = 15, [2] = 10, [3] = 5,  [4] = 2},
	[ShieldObjType.Boss] = 					{[0] = 10, [1] = 7,  [2] = 4,  [3] = 2,  [4] = 1},
	[ShieldObjType.FallItem] = 				{[0] = 40, [1] = 30, [2] = 20, [3] = 10, [4] = 2},
	[ShieldObjType.SoulBoy] = 				{[0] = 10, [1] = 5,  [2] = 2,  [3] = 0,  [4] = 0},
	[ShieldObjType.Guard] = 				{[0] = 10, [1] = 5,  [2] = 2,  [3] = 0,  [4] = 0},
	[ShieldObjType.Pet] = 					{[0] = 10, [1] = 5,  [2] = 2,  [3] = 0,  [4] = 0},
	[ShieldObjType.RoleSkillEffect] = 		{[0] = 5,  [1] = 3,  [2] = 1,  [3] = 0,  [4] = 0},
	[ShieldObjType.MainRoleSkillEffect] = 	{[0] = 2,  [1] = 2,  [2] = 2,  [3] = 2,  [4] = 0},
	[ShieldObjType.RoleFollowUI] = 			{[0] = 30, [1] = 15, [2] = 10, [3] = 5,  [4] = 1},
	[ShieldObjType.OthersFollowTitle] = 	{[0] = 20, [1] = 10, [2] = 5,  [3] = 0,  [4] = 0},
	[ShieldObjType.FallItemFollowUI] = 		{[0] = 35, [1] = 25, [2] = 15, [3] = 10, [4] = 0},
	[ShieldObjType.Npc] = 					{[0] = 20, [1] = 15, [2] = 10, [3] = 5,  [4] = 1},
	[ShieldObjType.Shadow] = 				{[0] = 40, [1] = 30, [2] = 20, [3] = 10, [4] = 2},
	[ShieldObjType.NpcFollowUI] = 			{[0] = 20, [1] = 15, [2] = 10, [3] = 5,  [4] = 2},
	[ShieldObjType.RoleEffect] = 			{[0] = 5, [1] = 3, [2] = 1, [3] = 0,  [4] = 0},
	[ShieldObjType.PartWuHunZhenShen] = 	{[0] = 20, [1] = 10, [2] = 5,  [3] = 2,  [4] = 1},
	[ShieldObjType.SceneEffectObj] = 		{[0] = 4, [1] = 3, [2] = 2, [3] = 1,  [4] = 0},
	[ShieldObjType.BeastObj] = 				{[0] = 10, [1] = 5,  [2] = 2,  [3] = 0,  [4] = 0},
	[ShieldObjType.ShuangShengTianShen] = 	{[0] = 10, [1] = 5,  [2] = 2,  [3] = 0,  [4] = 0},
	[ShieldObjType.TaskCallInfoObj] = 	    {[0] = 10, [1] = 5,  [2] = 2,  [3] = 0,  [4] = 0},
}

-- 帧数降低时，自动屏蔽
-- 数字越小的越先屏蔽(0-8)
QualityWGData.PartQualityLevel =
{
	[ShieldObjType.BuffEffect] = 1,
	[ShieldObjType.SitEffect] = 1,
	[ShieldObjType.PartBaoJuEffect] = 2,
	[ShieldObjType.PartShouHuan] = 2,
	[ShieldObjType.FollowObjEffect] = 2,
	[ShieldObjType.PetEffect] = 3,
	[ShieldObjType.XiaoTianQuanEffect] = 3,
	[ShieldObjType.GuardEffect] = 2,
	[ShieldObjType.BeastEffect] = 3,
	[ShieldObjType.TaskCallInfoEffect] = 3,

	[ShieldObjType.PartWeaponEffect] = 3,
	[ShieldObjType.PartTailEffect] = 2,
	[ShieldObjType.PartWingEffect] = 3,
	[ShieldObjType.PartJianlingEffect] = 3,
	[ShieldObjType.PartMountEffect] = 3,
	[ShieldObjType.PartHalo] = 2,
	[ShieldObjType.PartSkillHalo] = 2,
	[ShieldObjType.PartGodOrDemonHalo] = 2,
	[ShieldObjType.PartMask] = 3,
	[ShieldObjType.PartWaist] = 3,
	[ShieldObjType.PartFoot] = 2,
	[ShieldObjType.PartBaoJu] = 3,
	[ShieldObjType.PartTail] = 3,
	[ShieldObjType.PartFaZhen] = 3,
	[ShieldObjType.FallItemEffect] = 2,
	[ShieldObjType.PartFootTrail] = 2,
	[ShieldObjType.PartSoulRing1] = 2,
	[ShieldObjType.PartSoulRing2] = 2,
	[ShieldObjType.PartSoulRing3] = 2,
	[ShieldObjType.PartSoulRing4] = 2,
	[ShieldObjType.PartSoulRing5] = 2,
	[ShieldObjType.PartSoulRing6] = 2,
	[ShieldObjType.PartSoulRing7] = 2,
	[ShieldObjType.PartSoulRing8] = 2,
	[ShieldObjType.PartSoulRingSelect] = 2,

	[ShieldObjType.PartWing] = 4,
	[ShieldObjType.PartJianling] = 4,
	[ShieldObjType.RoleSkillEffect] = 2,
	[ShieldObjType.Pet] = 4,
	[ShieldObjType.Guard] = 3,
	[ShieldObjType.OthersFollowTitle] = 3,

	[ShieldObjType.OtherFallItemFollowUI] = 5,
	[ShieldObjType.NpcFollowUI] = 5,
	[ShieldObjType.SceneEffectObj] = 2,
	[ShieldObjType.BeastObj] = 4,
	[ShieldObjType.ShuangShengTianShen] = 5,
	[ShieldObjType.TaskCallInfoObj] = 4,
}

function QualityWGData:__init()
	if QualityWGData.Instance then
		print_error("QualityWGData to create singleton twice")
	end
	QualityWGData.Instance = self

	-- local config = ConfigManager.Instance:GetAutoConfig("model_quality_auto")
	self.model_quality_config = {}
	-- for k,v in pairs(config.model_quality_offset) do
	-- 	self.model_quality_config[v.bundle_name] = self.model_quality_config[v.bundle_name] or {}
	-- 	self.model_quality_config[v.bundle_name][tostring(v.asset_name)] = v.offset
	-- end

	self.model_effect_quality_config = {}
	-- for k,v in pairs(config.model_effect_quality_offset) do
	-- 	self.model_effect_quality_config[v.bundle_name] = self.model_effect_quality_config[v.bundle_name] or {}
	-- 	self.model_effect_quality_config[v.bundle_name][tostring(v.asset_name)] = v.offset
	-- end
end

function QualityWGData:__delete()
	QualityWGData.Instance = nil
end

-- local Profiler = UnityEngine.Profiling.Profiler
function QualityWGData:GetMaxAppearCount(shield_obj_type, quality_level)
	if LOW_GRAPHICS_MEMORY then
		quality_level = quality_level < 2 and 2 or quality_level
	end

	return QualityWGData.SceneObjMaxAppearConut[shield_obj_type][quality_level]
end

-- 根据AB路径获取该模型的品质偏移值
-- 偏移值越大，说明该模型越值钱，则越难被屏蔽
function QualityWGData:GetModelQualityOffsetConfig(bundle_name, asset_name)
	local offset = 0
	if self.model_quality_config[bundle_name] then
		offset = self.model_quality_config[bundle_name][asset_name] or 0
	end

	return offset
end

-- 根据AB路径获取该模型上，用GameObjectAttach组件加载出来的特效品质偏移值
-- 偏移值越大，则越难被屏蔽
function QualityWGData:GetModelEffectQualityOffsetConfig(bundle_name, asset_name)
	local offset = 0
	if self.model_effect_quality_config[bundle_name] then
		offset = self.model_effect_quality_config[bundle_name][asset_name] or 0
	end

	return offset
end