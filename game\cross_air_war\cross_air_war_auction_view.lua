CrossAirWarAuctionView = CrossAirWarAuctionView or BaseClass(SafeBaseView)
function CrossAirWarAuctionView:__init()
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/cross_air_war_ui_prefab", "layout_kf_air_war_auction_view")
end

function CrossAirWarAuctionView:LoadCallBack()
	if not self.auction_list then
		self.auction_list = AsyncListView.New(AirWarAuctionPriceRender, self.node_list.auction_list)
	end

	if not self.regional_barrage then
        self.regional_barrage = RegionalBarrage.New(self.node_list.regional_barrage_area)
		self.regional_barrage:SetGetDanMuInfoFunc(BindTool.Bind(function ()
            return {}
        end, self))
		self.regional_barrage:ChangeCellAsset("uis/view/cross_air_war_ui_prefab", "BullscreenBarrageItemCell")
		self.regional_barrage:StartShowDanMu(0.3)
    end

	XUI.AddClickEventListener(self.node_list["btn_close_win"],BindTool.Bind(self.Close, self))
	XUI.AddClickEventListener(self.node_list["btn_change_page_left"],BindTool.Bind(self.OnClickChangePage, self, -1))
	XUI.AddClickEventListener(self.node_list["btn_change_page_right"],BindTool.Bind(self.OnClickChangePage, self, 1))
	XUI.AddClickEventListener(self.node_list["btn_rule"], BindTool.Bind(self.OnClickAuctionRule, self))
end

function CrossAirWarAuctionView:ReleaseCallBack()
	self.cur_max_round = nil
	self.cur_choose_round = nil

	if self.auction_list then
		self.auction_list:DeleteMe()
		self.auction_list = nil
	end

	if self.regional_barrage then
        self.regional_barrage:DeleteMe()
        self.regional_barrage = nil 
    end

	self:RemoveAuctionPriceCountDown()
end

function CrossAirWarAuctionView:OnFlush(param_t)
	self:FlushShowAuctionRound()
	self:FlushShowAuctionList()
	self:FlushShowAuctionStatus()
end

-- 刷新当前拍卖物品 (当前拍卖的物品)
function CrossAirWarAuctionView:FlushShowAuctionRound()
	self.cur_max_round = CrossAirWarWGData.Instance:GetMaxAirWarAuctionInfoRound()
	self.cur_choose_round = CrossAirWarWGData.Instance:GetCurAirWarAuctionInfoRound()
end

-- 刷新当前拍卖物品 (当前拍卖的物品)
function CrossAirWarAuctionView:FlushShowAuctionList()
	if not self.cur_choose_round then
		return
	end

	local list = CrossAirWarWGData.Instance:GetAuctionListByRound(self.cur_choose_round)
	self.auction_list:SetDataList(list)

	local progress_str = string.format("(%s/%s)", ToColorStr(self.cur_choose_round + 1, COLOR3B.GREEN), self.cur_max_round)
	local auction_prog_str = string.format(Language.CrossAirWar.AuctionTitle, progress_str)  
	self.node_list.auction_prog_txt.text.text = auction_prog_str
	self.node_list.btn_change_page_left:CustomSetActive(self.cur_choose_round > 0)
	self.node_list.btn_change_page_right:CustomSetActive(self.cur_choose_round < self.cur_max_round - 1)
end

-- 刷新当前拍卖物品 (当前拍卖的物品)
function CrossAirWarAuctionView:FlushShowAuctionStatus()
	local status_time = CrossAirWarWGData.Instance:GetAirWarSceneNextStatusTime()
	self:FlushCurAuctionItemCDTimer(status_time)
end

-- 刷新当前拍卖物品出价CD 
function CrossAirWarAuctionView:FlushCurAuctionItemCDTimer(status_time)
	self:RemoveAuctionPriceCountDown()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local remain_time = status_time - server_time
	self.node_list.auction_cd_root:CustomSetActive(remain_time > 0)
	local base_cfg = CrossAirWarWGData.Instance:GetBaseCfg()
	local auction_time = base_cfg and base_cfg.auction_time or 1

	if remain_time > 0 then
		self.node_list.auction_cd_time.text.text = TimeUtil.FormatSecondDHM8(remain_time)
		self.node_list.auction_cd_slider.slider.value = math.floor(remain_time) / auction_time

		local function timer_func(elapse_time, total_time)
			local show_time = total_time - elapse_time
			local time_str = TimeUtil.FormatSecondDHM8(show_time)
			self.node_list.auction_cd_time.text.text = time_str

			local base_cfg = CrossAirWarWGData.Instance:GetBaseCfg()
			local auction_time = base_cfg and base_cfg.auction_time or 1
			self.node_list.auction_cd_slider.slider.value = math.floor(show_time) / auction_time
		end

		local complete_fun  = function()
			self.node_list.auction_cd_root:CustomSetActive(false)
			self:RemoveAuctionPriceCountDown()
		end
		
		self.count_down = CountDown.Instance:AddCountDown(remain_time, 1, timer_func, complete_fun)
	end
end

function CrossAirWarAuctionView:RemoveAuctionPriceCountDown()
	if self.count_down then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

-- 添加一条拍卖成功弹幕
function CrossAirWarAuctionView:AddSucAutionBarrage(data)
	local action_item = CrossAirWarWGData.Instance:GetSingleAuctionItemInfo(data.round, data.seq)
	local auction_data = action_item.auction_data
	if auction_data then
		local item_name = ""
		local item_num = 1
		local user_name = auction_data.name or ""
		local cfg = CrossAirWarWGData.Instance:GetAuctionItemCfgBySeq(auction_data.auction_seq)

		if cfg then
			local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.auction_item.item_id)
			item_name = item_cfg and item_cfg.name or ""
			item_num = cfg.auction_item.num
		end

		local desc_content = string.format(Language.CrossAirWar.AuctionGetItemSuccess, user_name, auction_data.auction_price, item_name, item_num)
		self:AddOneSpecialDanMu(desc_content)
	end
end

-- 添加一条流拍弹幕
function CrossAirWarAuctionView:AddFailAutionBarrage(data)
	local cfg = CrossAirWarWGData.Instance:GetAuctionItemCfgBySeq(data.auction_seq)
	local item_name = ""
	local item_num = 1

	if cfg then
		local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.auction_item.item_id)
		item_name = item_cfg and item_cfg.name or ""
		item_num = cfg.auction_item.num
	end

	local desc_content = string.format(Language.CrossAirWar.AuctionGetItemFail, item_name, item_num)
	self:AddOneSpecialDanMu(desc_content)
end

-- 添加一条弹幕
function CrossAirWarAuctionView:AddOneSpecialDanMu(desc_content)
    if self.regional_barrage then
        local danmu_info = {
            desc_content = desc_content,
            need_random_color = false,
            is_shield_bg = true,
            bg_bundle = nil,
            bg_asset = nil,
            move_speed = math.random(200, 500),
            color_data_list = ITEM_COLOR_DARK
        }
        self.regional_barrage:AddOneTemporaryDanMu(danmu_info)
    end
end

-------------------------------------------------------------------------------
-- 切换
function CrossAirWarAuctionView:OnClickChangePage(offset)
	self.cur_choose_round = self.cur_choose_round + offset

	if self.cur_choose_round < 0 then
		self.cur_choose_round = 0
	end

	if self.cur_choose_round > self.cur_max_round - 1 then
		self.cur_choose_round = self.cur_max_round - 1
	end

	self:FlushShowAuctionList()
end

-- 灵玉池规则
function CrossAirWarAuctionView:OnClickAuctionRule()
	local rule_tip = RuleTip.Instance
	local rule_title = Language.CrossAirWar.AuctionRuleTitle
	local rule_content = Language.CrossAirWar.AuctionRuleContent
	
	rule_tip:SetTitle(rule_title)
	rule_tip:SetContent(rule_content, nil, nil, nil, true)
end

-------------------------- AirWarAuctionPriceRender --------------------------
AirWarAuctionPriceRender = AirWarAuctionPriceRender or BaseClass(BaseRender)
function AirWarAuctionPriceRender:LoadCallBack()
	if not self.auction_item_cell then
		self.auction_item_cell = ItemCell.New(self.node_list.item_root)
	end

	XUI.AddClickEventListener(self.node_list["btn_offer_price"], BindTool.Bind(self.OnClickPriceOffer, self))			-- 出价
	XUI.AddClickEventListener(self.node_list["btn_owner_offer_price"], BindTool.Bind(self.OnClickPricePad, self)) 		--手动出价
end

function AirWarAuctionPriceRender:ReleaseCallBack()
	if self.auction_item_cell then
		self.auction_item_cell:DeleteMe()
		self.auction_item_cell = nil
	end

	if self.num_keypad then
		self.num_keypad:Close()
		self.num_keypad = nil
	end
end

function AirWarAuctionPriceRender:OnFlush()
	if not self.data then return end

	local auction_data = self.data.auction_data
	local cfg = CrossAirWarWGData.Instance:GetAuctionItemCfgBySeq(auction_data.auction_seq)
	self.cur_round = CrossAirWarWGData.Instance:GetCurAirWarAuctionInfoRound()

	-- 物品信息
	self.auction_item_cell:SetData(cfg.auction_item)
    local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.auction_item.item_id)
	self.node_list.item_name.text.text = string.format("%sx%s", item_cfg and item_cfg.name or "", cfg.auction_item.num)
	local color_str = string.format("a3_dntg_zppm_wpdi_%d", cfg and cfg.rarity or 1)
	self.node_list.color_image.raw_image:LoadSprite(ResPath.GetRawImagesPNG(color_str))

	-- 展示原价
	self.node_list.origin_price_root:CustomSetActive(cfg.origin_price ~= nil and cfg.origin_price ~= 0 and auction_data.round == self.cur_round)
	if cfg.origin_price ~= nil and cfg.origin_price ~= 0 then
		self.node_list.origin_price_sign.image:LoadSprite(ResPath.GetCommonIcon(ResPath.GetMoneyIcon(cfg.money_type)))
		self.node_list.origin_price_txt.text.text = cfg.origin_price
	end

	if self.num_keypad then
		self.num_keypad:Close()
	end
	
	self.node_list.auction_end:CustomSetActive(auction_data.round < self.cur_round)
	self.node_list.btn_owner_offer_price:CustomSetActive(auction_data.round >= self.cur_round)
	self.node_list.btn_offer_price:CustomSetActive(auction_data.round >= self.cur_round)
	self.node_list.auction_end_status:CustomSetActive(auction_data.auction_price ~= 0)
	self.node_list.auction_end_pass:CustomSetActive(auction_data.auction_price == 0)
	self.node_list.offer_price_sign.image:LoadSprite(ResPath.GetCommonIcon(ResPath.GetMoneyIcon(cfg.money_type)))
	self.node_list.now_price_sign.image:LoadSprite(ResPath.GetCommonIcon(ResPath.GetMoneyIcon(cfg.money_type)))
	self.node_list.now_price_txt.text.text = auction_data.auction_price ~= 0 and auction_data.auction_price or cfg.base_price
	local str = auction_data.auction_price ~= 0 and auction_data.name or Language.CrossAirWar.AuctionWaitRole
	local str_color = auction_data.auction_price ~= 0 and "#5C3C2A" or "#a84b05"
	local final_str = ToColorStr(str, str_color)
	self.node_list.now_price_role_name.text.text = final_str
	local now_price = auction_data.auction_price ~= 0 and auction_data.auction_price + cfg.auction_base_price or cfg.base_price
	self.node_list.offer_price_txt.text.text = now_price
end

-- 出价
function AirWarAuctionPriceRender:OnClickPriceOffer(num)
	if (not self.data) or (not self.data.auction_data) then return end
	local auction_data = self.data.auction_data
	self.cur_round = CrossAirWarWGData.Instance:GetCurAirWarAuctionInfoRound()

	if auction_data.round > self.cur_round then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossAirWar.AuctionHint4)
		return
	end

	local cfg = CrossAirWarWGData.Instance:GetAuctionItemCfgBySeq(auction_data.auction_seq)
	local gold_num = RoleWGData.Instance.role_info.gold or 0  
	local bind_gold_num = RoleWGData.Instance.role_info.bind_gold or 0
	local have_num = cfg.money_type == 1 and gold_num or bind_gold_num
	local auction_uuid = auction_data and auction_data.auction_uuid
	local now_price = auction_data.auction_price ~= 0 and auction_data.auction_price + cfg.auction_base_price or cfg.base_price

	if num ~= nil then
		if now_price > num then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossAirWar.AuctionHint1)
			return
		end

		now_price = num
	end

	local role_uuid = RoleWGData.Instance:GetUUid()
	if auction_uuid == role_uuid then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossAirWar.AuctionHint3)
		return
	end

	if have_num >= now_price then
		CrossAirWarWGCtrl.Instance:SendCSCrossAirWarOperateAuction(self.data.index, now_price)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossAirWar["GoldRemindTxt" .. (cfg.money_type or 1)])
	end
end

-- 手动出价
function AirWarAuctionPriceRender:OnClickPricePad()
	if (not self.data) or (not self.data.auction_data) then return end
	local auction_data = self.data.auction_data
	self.cur_round = CrossAirWarWGData.Instance:GetCurAirWarAuctionInfoRound()
	if auction_data.round > self.cur_round then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossAirWar.AuctionHint4)
		return
	end

	local cfg = CrossAirWarWGData.Instance:GetAuctionItemCfgBySeq(auction_data.auction_seq)
	local now_price = auction_data.auction_price ~= 0 and auction_data.auction_price + cfg.auction_base_price or cfg.base_price
	if self.num_keypad == nil then
		self.num_keypad = TipWGCtrl.Instance:GetPopNumView()
	end

	self.num_keypad:Open()
	self.num_keypad:SetNum(now_price)
	self.num_keypad:SetMaxValue(cfg.max_price or 99999999)
	self.num_keypad:SetMinValue(now_price)
	self.num_keypad:SetOkCallBack(BindTool.Bind(self.OnChangeNum, self))
end

-- 手动出价
function AirWarAuctionPriceRender:OnChangeNum(num)
	self:OnClickPriceOffer(num)
end