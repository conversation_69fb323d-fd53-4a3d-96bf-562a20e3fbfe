CangJinShopWGData = CangJinShopWGData or BaseClass()

function CangJinShopWGData:__init()
	if CangJinShopWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[CangJinShopWGData] attempt to create singleton twice!")
		return
	end

	CangJinShopWGData.Instance = self
	self:InitCfg()

	self.score = 0
	self.tequan_flag = {}
	self.tequan_every_day_reward = {}
	self.tequan_choose_attr1_flag = {}
	self.tequan_choose_attr2_flag = {}
	self.suit_shop = {}
	self.limit_shop_flag = {}
	self.refresh_limit_count = 0
	self.limit_shop = {}

	self.tequan_show_list = {}
	self.choose_left_attr = {}
	self.choose_right_attr = {}

	-- 当前轮次
	self.cur_grade = 0
	self.limit_shop_refresh_count = 0
	self.convert_info = {}

	-- 【红点】
	RemindManager.Instance:Register(RemindName.CangJinExchangeTeQuan, BindTool.Bind(self.GetCangJinExchangeTeQuanRemind, self))
	RemindManager.Instance:Register(RemindName.CangJinExchangeLimitShop, BindTool.Bind(self.GetCangJinExchangeLimitShopRemind, self))
end

function CangJinShopWGData:__delete()
	CangJinShopWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.CangJinExchangeTeQuan)
	RemindManager.Instance:UnRegister(RemindName.CangJinExchangeLimitShop)
end

function CangJinShopWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("cangjinshangpu_cfg_auto")
	self.suit_shop_cfg = cfg.suit_shop
	self.limit_shop_cfg = cfg.limit_shop
	self.limit_shop_refresh_cfg = cfg.limit_shop_refresh
	self.tequan_cfg = cfg.tequan
	self.initial_panel_cfg = ListToMap(cfg.initial_panel, "grade", "index")
	self.choose_attr_cfg = cfg.choose_attr
	self.convert_cfg = ListToMap(cfg.convert, "grade", "seq")
	self.other_cfg = cfg.other[1]
	self.grade_cfg = cfg.grade

	local convert_cache = {}
	local convert_cost_item_cache = {}
	for k, v in pairs(self.convert_cfg) do
		convert_cache[k] = convert_cache[k] or {}
		for i, u in pairs(v) do
			local stuff_data = {}
			convert_cache[k][i] = convert_cache[k][i] or {}
			local need_item_list = string.split(u.need_item, ";")
			
			for m, n in pairs(need_item_list) do
				if n then
					local item_info = string.split(n, ",")
					local item_id, num = tonumber(item_info[1]), tonumber(item_info[2])

					if item_id and num then
						convert_cost_item_cache[item_id] = item_id
						table.insert(stuff_data, {item_id = item_id, num = num})
					end
				end
			end
			convert_cache[k][i] = stuff_data
		end
	end

	self.convert_cache = convert_cache
	self.convert_cost_item_cache = convert_cost_item_cache
end

function CangJinShopWGData:GetModelCfgByIndex(index)
	local grade = self:GetCurGrade()
	return (self.initial_panel_cfg[grade] or {})[index]
end

function CangJinShopWGData:SetCangJinShopAllInfo(protocol)
	self.score = protocol.score
	self.tequan_flag = protocol.tequan_flag
	self.tequan_every_day_reward = protocol.tequan_every_day_reward
	self.tequan_choose_attr1_flag = protocol.tequan_choose_attr1_flag
	self.tequan_choose_attr2_flag = protocol.tequan_choose_attr2_flag
	self.suit_shop = protocol.suit_shop
	self.limit_shop_flag = protocol.limit_shop_flag
	self.refresh_limit_count = protocol.refresh_limit_count
	self.limit_shop = protocol.limit_shop

	self.cur_grade = protocol.grade
	self.limit_shop_refresh_count = protocol.limit_shop_refresh_count
	self.convert_info = protocol.convert

	self:UpdataChooseAtrrList()
	self:UpdataShowTeQuanList()
end

function CangJinShopWGData:SetConvertInfo(protocol)
	self.convert_info = protocol.convert
end

function CangJinShopWGData:ScoreInfoChange(protocol)
	self.score = protocol.score
end

function CangJinShopWGData:SuitShopChange(protocol)
    self.suit_shop = protocol.suit_shop
end

function CangJinShopWGData:LimitShopInfoChange(protocol)
	self.limit_shop_flag = protocol.limit_shop_flag
	self.refresh_limit_count = protocol.refresh_limit_count
	self.limit_shop = protocol.limit_shop
	self.limit_shop_refresh_count = protocol.limit_shop_refresh_count
end

function CangJinShopWGData:TeQuanInfoChange(protocol)
	self.tequan_flag = protocol.tequan_flag
	self.tequan_every_day_reward = protocol.tequan_every_day_reward
	self.tequan_choose_attr1_flag = protocol.tequan_choose_attr1_flag
	self.tequan_choose_attr2_flag = protocol.tequan_choose_attr2_flag

	self:UpdataChooseAtrrList()
	self:UpdataShowTeQuanList()
end

function CangJinShopWGData:GetCurScore()
	return self.score
end

function CangJinShopWGData:GetShowSuitInfo()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local suit_list = {}
	for k, v in pairs(self.suit_shop_cfg) do
		if server_day >= v.min_open_server_day then
			local data = {}
			local seq =  v.seq
			data.seq = seq
			data.cfg = v
			data.buy_times = self.suit_shop[seq] or 0
			data.is_all_buy = (v.buy_limit - data.buy_times) <= 0 and 1 or 0
			table.insert(suit_list, data)
		end
	end 	

	if not IsEmptyTable(suit_list) then
		table.sort(suit_list, SortTools.KeyLowerSorters("is_all_buy", "seq"))
	end

	return suit_list
end

function CangJinShopWGData:GetShowLimitShopInfo()
	local shop_list = {}
	for k, v in pairs(self.limit_shop_flag) do
		if v == 1 and self.limit_shop_cfg[k] then
			local data = {}
			local seq = self.limit_shop_cfg[k].seq
			data.seq = seq
			data.cfg = self.limit_shop_cfg[k]
			data.buy_times = self.limit_shop[k] or 0
			table.insert(shop_list, data)
		end
	end

	if not IsEmptyTable(shop_list) then
		table.sort(shop_list, SortTools.KeyLowerSorters("seq"))
	end

	return shop_list
end

function CangJinShopWGData:GetLimitShopRefreshCfg()
	local flush_count = self.refresh_limit_count + 1
	for k, v in ipairs(self.limit_shop_refresh_cfg) do
		if v.min_count <= flush_count and v.max_count >= flush_count then
			return v
		end
	end

	return self.limit_shop_refresh_cfg[#self.limit_shop_refresh_cfg]
end

function CangJinShopWGData:GetTeQuanActFlag(seq)
	return self.tequan_flag[seq] or 0
end

function CangJinShopWGData:UpdataShowTeQuanList()
	local tequan_list = {}
	local already_choose_attr = self:IsAlreadyChooseAttr()
	for k, v in pairs(self.tequan_cfg) do
		local data = {}
		local seq = v.seq
		data.seq = seq
		data.cfg = v
		data.act_flag = self:GetTeQuanActFlag(seq)
		data.day_reward_flag = self.tequan_every_day_reward[seq] or 0
		local is_can_get = v.is_can_get or 0
		data.reward_red = data.act_flag == 1 and is_can_get == 1 and data.day_reward_flag == 0
		data.attr_red = data.act_flag == 1 and v.is_can_choose == 1 and (not already_choose_attr)
		data.remind = data.reward_red or data.attr_red
		table.insert(tequan_list, data)
	end

	if not IsEmptyTable(tequan_list) then
		table.sort(tequan_list, SortTools.KeyLowerSorters("seq"))
	end

	self.tequan_show_list = tequan_list
end

function CangJinShopWGData:GetShowTeQuanList()
	return self.tequan_show_list
end

function CangJinShopWGData:UpdataChooseAtrrList()
	local left_attr_list = {}
	local right_attr_list = {}

	local insert_attr_func = function (list, cfg, is_left)
		local data = {}
		data.seq = cfg.seq
		if is_left then
			if cfg.base_choose_attr and cfg.base_choose_attr ~= "" then
				data.attr_list = self:SplitChooseAttr(cfg.base_choose_attr)
			end
		else
			if cfg.per_choose_attr and cfg.per_choose_attr ~= "" then
				data.attr_list = self:SplitChooseAttr(cfg.per_choose_attr)
			end
		end

		data.is_choose = self:GetChooseAttrFlag(is_left, data.seq) == 1
		table.insert(list, data)
    end

	for k, v in pairs(self.choose_attr_cfg) do
		insert_attr_func(left_attr_list, v, true)
		insert_attr_func(right_attr_list, v, false)
	end

	if not IsEmptyTable(left_attr_list) then
		table.sort(left_attr_list, SortTools.KeyLowerSorters("seq"))
	end

	if not IsEmptyTable(right_attr_list) then
		table.sort(right_attr_list, SortTools.KeyLowerSorters("seq"))
	end

	self.choose_left_attr = left_attr_list
	self.choose_right_attr = right_attr_list
end

---拆解属性
function CangJinShopWGData:SplitChooseAttr(str)
	local attr_all_data = {}

	local attr_list = Split(str, "|")
	for _, info in ipairs(attr_list) do
		if info then
			local list_info = Split(info, ":")
			attr_all_data.attr_str = tonumber(list_info[1])
			attr_all_data.attr_value = tonumber(list_info[2])
		end
	end

	return attr_all_data
end

function CangJinShopWGData:GetChooseAttrFlag(is_left, seq)
	if is_left then
		return self.tequan_choose_attr1_flag[seq] or 0
	else
		return self.tequan_choose_attr2_flag[seq] or 0
	end
end

--判断是否已经选择属性了
function CangJinShopWGData:IsAlreadyChooseAttr()
	for _, v in ipairs(self.choose_left_attr) do
		if v.is_choose then
			return true
		end
	end

	return false
end

--- 获取属性列表
function CangJinShopWGData:GetChooseAttrList(is_left)
	return is_left and self.choose_left_attr or self.choose_right_attr
end

--是否能选择属性
function CangJinShopWGData:GetIsActChooseAttrTeQuan()
	for k, v in pairs(self.tequan_show_list) do
		if v.cfg.is_can_choose == 1 and v.act_flag == 1 then
			return true
		end
	end

	return false
end

--获得当前选择所有属性（格式化）
function CangJinShopWGData:GetAllChooseAttrList()
	local all_attr_list = {}
	local attr_info = AttributePool.AllocAttribute()
	local capability = 0

	local get_attr_func = function (list, choose_attr)
		for k, v in pairs(choose_attr) do
			if v.is_choose  then
				local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_list.attr_str)
				local attr_value = v.attr_list.attr_value
				list[attr_str] = list[attr_str] and list[attr_str] + attr_value or attr_value
			end
		end
	end

	get_attr_func(all_attr_list, self.choose_left_attr)
	get_attr_func(all_attr_list, self.choose_right_attr)

	for k, v in pairs(all_attr_list) do
		attr_info[k] = attr_info[k] + v
	end

	capability = AttributeMgr.GetCapability(attr_info)
	return all_attr_list, capability
end

---红点
function CangJinShopWGData:GetCangJinExchangeTeQuanRemind()
	for k, v in pairs(self.tequan_show_list) do
		if v.remind then
			return 1
		end
	end

	return 0
end

function CangJinShopWGData:GetCurGrade()
	return self.cur_grade
end

function CangJinShopWGData:GetConvertCfgDataList()
	local grade = self:GetCurGrade()
	return self.convert_cfg[grade] or {}
end

function CangJinShopWGData:GetConvertNumBySeq(seq)
	return self.convert_info[seq] or 0
end

function CangJinShopWGData:GetShowLimitAllShopInfo()
	return self.limit_shop_cfg
end

function CangJinShopWGData:GetCangJinExchangeLimitShopRemind()
	local data_list = self:GetConvertCfgDataList()

	if not IsEmptyTable(data_list) then
		for k, v in pairs(data_list) do
			if self:CheckCanConvert(v) then
				return 1
			end
		end
	end

	return 0
end

function CangJinShopWGData:CheckCanConvert(data)
	if data.limit > 0 then
		local exchange_time = self:GetConvertNumBySeq(data.seq)
		if exchange_time >= data.limit then
			return false
		end
	end

	local convert_cost_cache = self:GetConvertCacheByGradeAndSeq(data.grade, data.seq)
	if not IsEmptyTable(convert_cost_cache) then
		for k, v in pairs(convert_cost_cache) do
			local cost_item_id, num = v.item_id, v.num
			local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

			if item_num < num then
				return false
			end
		end
	end

	local need_score = data.need_score
	if need_score > 0 then
		local cur_score = self:GetCurScore()
		if cur_score < need_score then
			return false
		end
	end

	return true
end

function CangJinShopWGData:GetConvertCacheByGradeAndSeq(grade, seq)
	return (self.convert_cache[grade] or {})[seq] or {}
end

function CangJinShopWGData:IsConvertCostItem(change_item_id)
	return nil ~= self.convert_cost_item_cache[change_item_id]
end

function CangJinShopWGData:GetOtherCfg()
	return self.other_cfg
end

function CangJinShopWGData:GetLimitShopRefreshCount()
	return self.limit_shop_refresh_count
end

function CangJinShopWGData:GetVirtualItemRechargeScore()
	return self.other_cfg.show_item
end

function CangJinShopWGData:GetExchangeLimitTime()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local grade = self:GetCurGrade()
	local time = 0

	for k, v in pairs(self.grade_cfg) do
		if grade == v.grade then
			if server_day >= v.open_min_day and server_day <= v.open_max_day then
				local time_diff = v.open_max_day - server_day
				time = TimeWGCtrl.Instance:NowDayTimeEnd(server_time) + time_diff * 24 *60 * 60
				break
			end
		end
	end

	return time
end

function CangJinShopWGData:IsTeQuanScore(item_id)
	return item_id == self.other_cfg.show_item
end