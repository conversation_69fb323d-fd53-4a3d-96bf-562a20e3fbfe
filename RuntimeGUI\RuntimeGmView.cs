﻿using System.IO;
using UnityEngine;
using LuaInterface;
using System.Collections.Generic;

public class RuntimeGmView : RuntimeBaseView
{
    private string gmContent;
    private Rect windowRect;
    public RuntimeGmView() : base(RuntimeViewName.GM)
    {
        windowRect = new Rect((Screen.width - 700) * 0.5f, (Screen.height - 300) * 0.5f, 700, 300);
    }

    override protected void OnReapintWindow(int windowid)
    {
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("重新登陆", GUILayout.Height(40), GUILayout.Height(40)))
        {
            GameRoot.Instance.ExecuteGm("/jy_cmd restart");
        }

        GUILayout.Space(10);
        gmContent = GUILayout.TextField(gmContent, GUILayout.MinWidth(1), GUILayout.Height(40));
        if (GUILayout.Button("发送命令", GUILayout.Width(100), GUILayout.Height(40)))
        {
            if (gmContent == "/jy_cmd luadebug")
            {
                ReadLuaAssetBundle();
            }
            else
            {
                //if (curGmIndex >= 0 && gmItemDir != null && gmItemDir.Count > 0 && gmItemDir.Count >= curGmIndex + 1)
                //{
                //    GMItem item = gmItemDir[curGmIndex];
                //    foreach (string gm in item.gmList)
                //    {
                //        GameRoot.Instance.ExecuteGm(gm);
                //    }
                //    return;
                //}
                GameRoot.Instance.ExecuteGm(gmContent);
            }
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);


        if (!this.TryReadGmCommandFile())
        {
            OldVersionGm();
        }

        // 工具栏
        GUILayout.Space(10);
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("跑新手资源"))
        {
            RuntimeGUIMgr.Instance.ShowResourceRecorderWindow();
        }
        GUILayout.EndHorizontal();
    }

    private void OldVersionGm()
    {
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Gmlist"))
        {
            SetGMbarStr("/jy_cmd gmlist 4");
        }
        if (GUILayout.Button("GmAdditem"))
        {
            SetGMbarStr("/jy_gm additem:101 1 0");
        }
        if (GUILayout.Button("GmActiveNextState"))
        {
            SetGMbarStr("/jy_gm activitynextstate:5");
        }
        if (GUILayout.Button("GM获得"))
        {
            SetGMbarStr("/jy_cmd quickitem");
        }
        if (GUILayout.Button("关闭自动挂机"))
        {
            SetGMbarStr("/jy_cmd guaji off");
            GameRoot.Instance.ExecuteGm("/jy_cmd guaji off");
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("清空货币"))
        {
            SetGMbarStr("/jy_gm decmoney:9999999999");
        }
        if (GUILayout.Button("设置VIP"))
        {
            SetGMbarStr("/jy_gm setvip:12");
        }
        if (GUILayout.Button("充值"))
        {
            SetGMbarStr("/jy_gm addchongzhi:9999999999");
        }
        if (GUILayout.Button("清空背包"))
        {
            SetGMbarStr("/jy_gm clearbag:");
        }
        if (GUILayout.Button("增加攻击力"))
        {
            SetGMbarStr("/jy_gm changegongji:9999999999");
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("转职"))
        {
            SetGMbarStr("/jy_gm zhuanzhi:");
        }
        if (GUILayout.Button("热执行代码"))
        {
            SetGMbarStr("/jy_cmd InsertLua ");
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("加等级"))
        {
            SetGMbarStr("/jy_gm setrolelevel:300");
        }
        if (GUILayout.Button("加科技点"))
        {
            SetGMbarStr("/jy_gm xingchen:1 1000");
        }
        if (GUILayout.Button("加科技币"))
        {
            SetGMbarStr("/jy_gm xingchen:2 1000");
        }
        if (GUILayout.Button("resetdaycount"))
        {
            SetGMbarStr("/jy_gm resetdaycount:");
        }
        if (GUILayout.Button("addday"))
        {
            SetGMbarStr("/jy_gm addday:");
        }
        if (GUILayout.Button("服务器热更新"))
        {
            SetGMbarStr("/jy_gm hotupdate:");
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("一键获取本职业15阶装备"))
        {
            SetGMbarStr("/jy_cmd fifteenthqeuip");
        }
        if (GUILayout.Button("跳任务"))
        {
            SetGMbarStr("/jy_gm jumptotrunk:1880");
        }
        if (GUILayout.Button("添加6件神兽装备"))
        {
            SetGMbarStr("/jy_gm additem:22544 1 0");
            SetGMbarStr("/jy_gm additem:22550 1 0");
            SetGMbarStr("/jy_gm additem:22556 1 0");
            SetGMbarStr("/jy_gm additem:22562 1 0");
            SetGMbarStr("/jy_gm additem:22568 1 0");
            SetGMbarStr("/jy_gm additem:23626 1 0");
        }
        GUILayout.EndHorizontal();

    }

    private void ZhuanZhi()
    {
        SetGMbarStr("/jy_cmd gmlist 4");
        SetGMbarStr("/jy_gm zhuanzhi:");
        SetGMbarStr("/jy_gm zhuanzhi:");
        SetGMbarStr("/jy_gm zhuanzhi:");
        SetGMbarStr("/jy_gm zhuanzhi:");
        SetGMbarStr("/jy_gm zhuanzhi:");
    }

    public void SetGMbarStr(string gmContent)
    {
        this.gmContent = gmContent;
    }

    private Vector2 gmCommandListScoll;
    private const int scrollWidth = 680;
    private const int scollHeight = 180;
    Dictionary<int, GMItem> gmItemDir = null;
    bool isUseOldVersionGM = true;
    bool isGMCommandFileInit = false;
    int curGmIndex = -1;
    private bool TryReadGmCommandFile()
    {
        if (this.gmItemDir == null && !isGMCommandFileInit)
        {
            LuaState luaState = GameRoot.Instance.LuaState;
            if (luaState == null)
                return false;

            luaState.DoString("gm_command_list = require(\"config/gm_command_list\")");
            LuaTable gmCommandList = luaState.GetTable("gm_command_list.gm_command_list");
            if (gmCommandList == null)
                return false;

            isGMCommandFileInit = true;
            gmItemDir = new Dictionary<int, GMItem>();
            int count = gmCommandList.Length;
            for (int i = 0; i < count; i++)
            {
                var command = (LuaTable)gmCommandList[i + 1];

                GMItem item = new GMItem();
                item.btnName = (string)command["btn_name"];
                item.autoExecute = (string)command["auto_execute"] == "true";

                var gm_list = (LuaTable)command["gm"];
                int gmCount = gm_list.Length;
                List<string> gmList = new List<string>();
                for (int j = 1; j <= gmCount; j++)
                {
                    var temp = (LuaTable)gm_list[j];
                    string gm = (string)temp["command"];
                    gmList.Add(gm);
                }
                item.gmList = gmList;
                gmItemDir.Add(i, item);
            }

            isUseOldVersionGM = false;
        }

        if (isUseOldVersionGM && isGMCommandFileInit)
        {
            return false;
        }

        gmCommandListScoll = GUILayout.BeginScrollView(gmCommandListScoll, GUILayout.Width(scrollWidth), GUILayout.Height(scollHeight));

        int col = 100; //行
        int row = 4; //列
        for (int i = 0; i < col; i++)
        {
            GUILayout.BeginVertical();
            GUILayout.BeginHorizontal();
            for (int j = 0; j < row; j ++)
            {
                int index = i * row + j;
                if (index + 1 > gmItemDir.Count)
                    break;

                GMItem item = gmItemDir[index];
                if (GUILayout.Button(item.btnName, GUILayout.Width(160), GUILayout.Height(30)))
                {
                    curGmIndex = index;
                    SetGMbarStr(item.gmList[0]);
                    if (item.autoExecute)
                    {
                        foreach (string gm in item.gmList)
                        {
                            GameRoot.Instance.ExecuteGm(gm);
                        }
                    }
                }
            }
            GUILayout.EndHorizontal();
            GUILayout.EndVertical();
        }

        GUILayout.EndScrollView();
        return true;
    }

    private struct GMItem
    {
        public string btnName;
        public List<string> gmList;
        public bool autoExecute;
    }

    private void ReadLuaAssetBundle()
    {
        string cacheDir = string.Format("{0}/BundleCache/LuaAssetBundle", Application.persistentDataPath);
        string luaTargetDir = string.Format("{0}/Lua", Application.persistentDataPath);
        string luaMd5Path = string.Format("{0}/lua_md5_list.txt", cacheDir);
        if (!File.Exists(luaMd5Path))
        {
            Debug.LogErrorFormat("找不到Md5文件 {0}", luaMd5Path);
            return;
        }
        string[] lines = File.ReadAllLines(string.Format("{0}/lua_md5_list.txt", cacheDir));
        for (int i = 0; i < lines.Length; i++)
        {
            string[] ary = lines[i].Split(' ');
            if (ary.Length != 2) continue;

            string bundleName = ary[0];
            string md5 = ary[1];
            if (bundleName.StartsWith("lua/"))
            {
                AssetBundle assetBundle = AssetBundle.LoadFromFile(string.Format("{0}/{1}-{2}", cacheDir, bundleName, md5));
                string[] assetNames = assetBundle.GetAllAssetNames();
                for (int m = 0; m < assetNames.Length; m++)
                {
                    string targetLuaPath = string.Format("{0}/{1}", luaTargetDir, assetNames[m]);
                    targetLuaPath = targetLuaPath.Replace(".bytes", "").Replace("assets/game/luabundle", "");
                    RuntimeAssetHelper.InsureDirectory(targetLuaPath);

                    var textAsset = assetBundle.LoadAsset<TextAsset>(assetNames[m]);
                    File.WriteAllBytes(targetLuaPath, textAsset.bytes);
                }

                assetBundle.Unload(true);
            }
        }

        Debug.LogFormat("解析完成{0}", luaTargetDir);
    }
}
