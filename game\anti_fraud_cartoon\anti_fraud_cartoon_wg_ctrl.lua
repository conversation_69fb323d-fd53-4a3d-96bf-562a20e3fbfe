require("game/anti_fraud_cartoon/anti_fraud_cartoon_wg_data")
require("game/anti_fraud_cartoon/anti_fraud_cartoon_view")

-- 防骗漫画
AntiFraudCartoonWGCtrl = AntiFraudCartoonWGCtrl or BaseClass(BaseWGCtrl)

function AntiFraudCartoonWGCtrl:__init()
	if AntiFraudCartoonWGCtrl.Instance ~= nil then
		ErrorLog("[AntiFraudCartoonWGCtrl] attempt to create singleton twice!")
		return
	end

	AntiFraudCartoonWGCtrl.Instance = self
	self.data = AntiFraudCartoonWGData.New()
	self.view = AntiFraudCartoonView.New(GuideModuleName.AntiFraudCartoonView)

	self:RegisterAllProtocols()
end

function AntiFraudCartoonWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	AntiFraudCartoonWGCtrl.Instance = nil
end

function AntiFraudCartoonWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCartoonOperateReq) 														-- 操作
	self:RegisterProtocol(SCCartoonRewardInfo, "OnSCCartoonRewardInfo")  							-- 奖励信息
end

-- 操作（对应枚举CARTOON_OPERATE_TYPE）
function AntiFraudCartoonWGCtrl:CSCartoonOperateReq(op_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCartoonOperateReq)
	protocol.operate_type = op_type
	protocol:EncodeAndSend()
end

-- 请求领取奖励
function AntiFraudCartoonWGCtrl:SendFetch()
	self:CSCartoonOperateReq(CARTOON_OPERATE_TYPE.CARTOON_GET_REARD)
end

-- 领取信息
function AntiFraudCartoonWGCtrl:OnSCCartoonRewardInfo(protocol)
	self.data:SetIsFetched(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.AntiFraudCartoonView)
	RemindManager.Instance:Fire(RemindName.AntiFraudCartoonRemind)
end