PrivilegeCollectionWGData = PrivilegeCollectionWGData or BaseClass()

PrivilegeCollectionWGData.HOLY_WEAPON_PAGE_ATTR_NUM = 4
PrivilegeCollectionWGData.HOLY_WEAPON_SKILL_ATTR_NUM = 2--圣技属性数量

function PrivilegeCollectionWGData:__init()
	if PrivilegeCollectionWGData.Instance then
		Error<PERSON>og("[PrivilegeCollectionWGData] attempt to create singleton twice!")
		return
	end

	PrivilegeCollectionWGData.Instance = self

	self:InitHolyWeaponCfg()
	self.holy_weapon_info_list = {}
	self.holy_skill_process_list = {}

	self.guard_privilege_buy_flag = {}			-- 特权购买标记
	self.guard_privilege_fetch_exp_flag = {}	-- 经验领取标记
	self.guard_privilege_save_exp = 0			-- 储存经验
	self.guard_privilege_show_main_tips = true	-- 主界面显示提示气泡.

	RemindManager.Instance:Register(RemindName.PrivilegeCollection_ZZTQ, BindTool.Bind(self.GetZZTQRedPoint, self))
	RemindManager.Instance:Register(RemindName.PrivilegeCollection_NSTQ, BindTool.Bind(self.GetNSTQRedPoint, self))
	RemindManager.Instance:Register(RemindName.PrivilegeCollection_ZJTQ, BindTool.Bind(self.GetZJTQRedPoint, self))
	RemindManager.Instance:Register(RemindName.PrivilegeCollection_SQCY, BindTool.Bind(self.GetSQCYRedPoint, self))
end

function PrivilegeCollectionWGData:__delete()
	PrivilegeCollectionWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.PrivilegeCollection_ZZTQ)
	RemindManager.Instance:UnRegister(RemindName.PrivilegeCollection_NSTQ)
	RemindManager.Instance:UnRegister(RemindName.PrivilegeCollection_ZJTQ)
	RemindManager.Instance:UnRegister(RemindName.PrivilegeCollection_SQCY)
end

-----------------------------------REMIND_START------------------------------

function PrivilegeCollectionWGData:GetNSTQRedPoint()
	local daily_open_flag = RechargeVolumeWGData.Instance:GetDailyOpenFlag()

	if not daily_open_flag then
		return 1
	end

	return 0
end

function PrivilegeCollectionWGData:GetZJTQRedPoint()
	return 0
end
-------------------------------------REMIND_END-------------------------------


------------------------------------------ZZTQ_START----------------------------------------
function PrivilegeCollectionWGData:GetZZTQRedPoint()
    local tequan_list = self:GetZZTQDataList()
    local score = self:GetCurScore()

    if not IsEmptyTable(tequan_list) then
        for k, v in pairs(tequan_list) do
			if self:GetZZTQPriRedPointBySeq(v.seq) then
				return 1
			end
        end
    end

    return 0
end

function PrivilegeCollectionWGData:GetZZTQPriRedPointBySeq(seq)
	if self:IsTeQuanUnLockBySeq(seq) then
		if not self:IsGetTeQuanEveryDayReward(seq) then
			return true
		end
	end

	return false
end

function PrivilegeCollectionWGData:GetCurScore()
	return YanYuGeWGData.Instance:GetCurScore()
end

function PrivilegeCollectionWGData:GetZZTQDataList()
	return YanYuGeWGData.Instance:GetZZTQDataList()
end

function PrivilegeCollectionWGData:GetZZTQDataListNoSort()
	return YanYuGeWGData.Instance:GetZZTQDataListNoSort()
end

function PrivilegeCollectionWGData:GetRealRechargeNum()
	return YanYuGeWGData.Instance:GetRealRechargeNum()
end

function PrivilegeCollectionWGData:IsTeQuanUnLockBySeq(seq)
	return YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(seq)
end

function PrivilegeCollectionWGData:IsGetTequanReward(seq)
	return YanYuGeWGData.Instance:IsGetTequanReward(seq)
end

function PrivilegeCollectionWGData:IsTequanHasDayReward(seq)
	return YanYuGeWGData.Instance:IsTequanHasDayReward(seq)
end

function PrivilegeCollectionWGData:IsGetTeQuanEveryDayReward(seq)
	return YanYuGeWGData.Instance:IsGetTeQuanEveryDayReward(seq)
end

function PrivilegeCollectionWGData:GetTeQuanCfgBySeq(seq)
	return YanYuGeWGData.Instance:GetTeQuanCfgBySeq(seq)
end

function PrivilegeCollectionWGData:GetOneKeyTequanCfgByIndex(index)
	return YanYuGeWGData.Instance:GetOneKeyTequanCfgByIndex(index)
end

function PrivilegeCollectionWGData:IsShowZZTQOneKeyPackageTeQuan()
	return 1 == YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_one_key_package_tequan")
end
------------------------------------------ZZTQ_END----------------------------------------

------------------------------------------NSTQ_START--------------------------------------
function PrivilegeCollectionWGData:GetRmbCfg()
	return RechargeVolumeWGData.Instance:GetRmbCfg()
end

function PrivilegeCollectionWGData:GetRmbCfgBySeq(seq)
	return RechargeVolumeWGData.Instance:GetRmbCfgBySeq(seq)
end

function PrivilegeCollectionWGData:GetRmbBuyStateBySeq(seq)
	return RechargeVolumeWGData.Instance:GetRmbBuyStateBySeq(seq)
end

function PrivilegeCollectionWGData:GetDefaultSelectIndex()
	return RechargeVolumeWGData.Instance:GetDefaultSelectIndex()
end

function PrivilegeCollectionWGData:GetRmbBuySeq()
	return RechargeVolumeWGData.Instance:GetRmbBuySeq()
end

------------------------------------------NSTQ_END----------------------------------------

------------------------------------------ZJTQ_START--------------------------------------
function PrivilegeCollectionWGData:GetShowTZSDDataList()
	return YanYuGeWGData.Instance:GetShowTZSDDataList()
end

function PrivilegeCollectionWGData:GetShowTZSDDataList()
	return YanYuGeWGData.Instance:GetShowTZSDDataList()
end

function PrivilegeCollectionWGData:IsSuitSeqIsUnLock(seq)
	return YanYuGeWGData.Instance:IsSuitSeqIsUnLock(seq)
end

function PrivilegeCollectionWGData:GetCurSuitShopDataBySeq(seq)
    return YanYuGeWGData.Instance:GetCurSuitShopDataBySeq(seq)
end
------------------------------------------ZJTQ_END----------------------------------------

------------------------------------------SQCY_END----------------------------------------
function PrivilegeCollectionWGData:InitHolyWeaponCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("holy_weapon_auto")
	self.holy_weapon_cfg = ListToMap(cfg.holy_weapon, "id", "level")
	self.holy_weapon_skill_cfg = ListToMap(cfg.special_skill, "id")
	self.holy_weapon_purchase_cfg = ListToMap(cfg.purchase, "id", "level")
	self.holy_weapon_view_show_cfg = ListToMap(cfg.view_cfg, "id")
	self.holy_weapon_other_cfg = cfg.other

	local guard_privilege_cfg = ConfigManager.Instance:GetAutoConfig("guard_privilege_auto")
	self.guard_privilege_cfg = ListToMap(guard_privilege_cfg.privilege, "seq")
	self.guard_privilege_other_cfg = guard_privilege_cfg.other[1]
end

function PrivilegeCollectionWGData:GetSQCYRedPoint()
	local open_state = self:GetHolyWeaponOpenState()
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.PrivilegeCollectionSQCY)

	if is_open and open_state then
		local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
		local save_open_day = PlayerPrefsUtil.GetInt("PrivilegeCollectionSQCY" .. main_role_id)

		if open_server_day > save_open_day then
			return 1
		end
	end

	return 0
end

function PrivilegeCollectionWGData:SetHolyWeaponList(protocol)
	self.holy_weapon_info_list = protocol.holy_weapon_info_list
	self.holy_skill_process_list = protocol.holy_skill_process_list
end

function PrivilegeCollectionWGData:SetHolyWeaponInfo(protocol)
	local info = self:GetHolyWeaponInfoById(protocol.id)
	if info then
		info.level = protocol.level
		info.purchase_times = protocol.purchase_times
	end
end

function PrivilegeCollectionWGData:SetHolyWeaponSkillProcess(protocol)
	local process = self.holy_skill_process_list[protocol.id] or 0
	local cur_level = self:GetHolyWeaponLevel(protocol.id)
	local cfg = self:GetHolyWeaponCfg(protocol.id, cur_level <= 0 and cur_level + 1 or cur_level)
	local max_process = cfg and cfg.max_process
	if process < max_process then
		process = process + protocol.skill_process
		self.holy_skill_process_list[protocol.id] = process
	end	
end

function PrivilegeCollectionWGData:GetHolyWeaponInfoById(id)
	return (self.holy_weapon_info_list or {})[id]
end

function PrivilegeCollectionWGData:GetHolyWeaponCfg(id, level)
	return ((self.holy_weapon_cfg or {})[id] or {})[level] or {}
end

function PrivilegeCollectionWGData:GetHolyWeaponLevel(id)
	return ((self.holy_weapon_info_list or {})[id] or {}).level or 0
end

function PrivilegeCollectionWGData:GetHolyWeaponMaxLevel(id)
	local cfg_list = (self.holy_weapon_cfg or {})[id]
	return cfg_list and (cfg_list[#cfg_list] or {}).level or 0
end

function PrivilegeCollectionWGData:GetHolyWeaponAttrList(id, level)
	local cfg_list = self.holy_weapon_cfg
	if IsEmptyTable(cfg_list) then
		return {}
	end
	
	local cur_level = level and level or self:GetHolyWeaponLevel(id)
	local max_level = self:GetHolyWeaponMaxLevel(id)
	local is_max = cur_level >= max_level
	local next_level = is_max and cur_level or cur_level + 1
	local cfg = cfg_list[id][cur_level]
	local next_cfg = cfg_list[id][next_level]
	local list = {}
	local attr, attr_str
	for i = 1, PrivilegeCollectionWGData.HOLY_WEAPON_PAGE_ATTR_NUM do
		attr = {}
		attr.attr_id = cfg["attr_id" .. i]
		attr.attr_value = cfg["attr_value" .. i]
		attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr.attr_id)
		attr.add_value = is_max and 0 or next_cfg["attr_value" .. i]
		attr.attr_str = attr_str
		attr.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
		table.insert(list, attr)
	end
	if not IsEmptyTable(list) then
        table.sort(list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return list
end

function PrivilegeCollectionWGData:GetHolyWeaponSkillAttrList(skill_id)
	local skill_cfg = self:GetHolySkillCfgById(skill_id)
	local attr_data_list = {}
	if IsEmptyTable(skill_cfg) then
		return attr_data_list
	end

	local next_skill_cfg = self:GetHolySkillCfgById(skill_id + 1)
	local is_max = IsEmptyTable(next_skill_cfg)

	local attr, attr_str
	for i = 1, 2 do 
		attr = {}
		attr.attr_id = skill_cfg["attr_id" .. i]
		attr.attr_value = skill_cfg["attr_value" .. i]
		attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr.attr_id)
		attr.add_value = is_max and 0 or next_skill_cfg["attr_value" .. i]
		attr.attr_str = attr_str
		attr.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
		table.insert(attr_data_list, attr)
	end

	if not IsEmptyTable(attr_data_list) then
        table.sort(attr_data_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return attr_data_list
end

function PrivilegeCollectionWGData:GetHolyWeaponSkillMaxAttrValueList(target_cfg)
	local skill_cfg = self:GetHolySkillCfgById(target_cfg.skill_id)
	local attr_data_list = {}
	if IsEmptyTable(skill_cfg) then
		return attr_data_list
	end

	local attr, attr_str
	for i = 1, 2 do
		if skill_cfg["attr_id" .. i] > 0 and skill_cfg["attr_value" .. i] > 0 then
			attr = {}
			attr.attr_id = skill_cfg["attr_id" .. i]
			attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr.attr_id)
			attr.attr_str = attr_str
			attr.attr_value = math.ceil(skill_cfg["attr_value" .. i] * target_cfg.max_process / skill_cfg.rate)
			attr.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
		end

		table.insert(attr_data_list, attr)
	end

	if not IsEmptyTable(attr_data_list) then
        table.sort(attr_data_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return attr_data_list
end

function PrivilegeCollectionWGData:GetHolyWeaponPurchaseCfg(id, level)
	local max_level = self:GetHolyWeaponMaxLevel(id)
	local cur_level = self:GetHolyWeaponLevel(id)
	if cur_level >= max_level then
		return ((self.holy_weapon_purchase_cfg or {})[id] or {})[cur_level - 1] or {}
	else
		return ((self.holy_weapon_purchase_cfg or {})[id] or {})[level] or {}
	end
end

function PrivilegeCollectionWGData:GetHolyWeaponDataList()
	local cfg_list = self.holy_weapon_cfg
	if IsEmptyTable(cfg_list) then
		return {}
	end

	local data_list = {}
	local cur_level, max_level, cfg, purchase_cfg, data
	for id, v in pairs(cfg_list) do
		if self:GetHolyWeaponIsOpen(id) then
			data = {}
			cur_level = self:GetHolyWeaponLevel(id)
			purchase_cfg = self:GetHolyWeaponPurchaseCfg(id, cur_level)
			max_level = self:GetHolyWeaponMaxLevel(id)
			cfg = self:GetHolyWeaponCfg(id, cur_level)
			data.skill_desc, data.skill_desc2 = self:GetSkillDescById(id)
			data.is_max = cur_level >= max_level	
			data.purchase_cfg = purchase_cfg
		
			data.cfg = cfg
			table.insert(data_list, data)
		end
	end

	return data_list
end

function PrivilegeCollectionWGData:GetHolyWeaponIsOpen(id)
	local cur_level = self:GetHolyWeaponLevel(id)
	local purchase_cfg = self:GetHolyWeaponPurchaseCfg(id, cur_level)
	local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	
	local preid_list = string.split(purchase_cfg.pre_ids, "|")
	if (preid_list[1] == "" or purchase_cfg.pre_ids == "|") and cur_openserver_day >= purchase_cfg.min_server_open_day then 
		return true 
	end

	local purchase_times
	for i = 1, #preid_list do
		purchase_times = ((self:GetHolyWeaponInfoById(tonumber(preid_list[i])) or {}).purchase_times or 0)
		if purchase_times > 0 and cur_openserver_day >= purchase_cfg.min_server_open_day then
			return true
		end
	end

	return false
end

--获得魂技效果描述
function PrivilegeCollectionWGData:GetSkillDescById(id)
	local str, str2 = "", ""
	local cur_level = self:GetHolyWeaponLevel(id)
	local cfg = self:GetHolyWeaponCfg(id, cur_level)
	local info = self:GetHolyWeaponInfoById(id)
	local skill_cfg = self:GetHolySkillCfgById(cfg and cfg.skill_id)

	if IsEmptyTable(cfg) or IsEmptyTable(info) or IsEmptyTable(skill_cfg) then
		return str, str2
	end

	local skill_process = self:GetHolySkillProcessById(id)
	str = string.format(skill_cfg.desc, skill_process <= 0 and 0 or math.floor(skill_process / skill_cfg.rate))
	str2 = string.format(skill_cfg.desc2, skill_process <= 0 and 0 or math.floor(skill_process / skill_cfg.rate))
	return str, str2

	-- local cur_level = self:GetHolyWeaponLevel(id)
	-- local max_level = self:GetHolyWeaponMaxLevel(id)
	-- local cfg = self:GetHolyWeaponCfg(id, cur_level <= 0 and cur_level + 1 or cur_level)
	-- local skill_cfg = self:GetHolySkillCfgById(cfg and cfg.skill_id)
	-- local info = self:GetHolyWeaponInfoById(id)
	-- if IsEmptyTable(skill_cfg) or IsEmptyTable(info) or IsEmptyTable(cfg) then
	-- 	return "", ""
	-- end

	-- local skill_desc1, skill_desc2 = "", ""
	-- if cur_level <= 0 then--未激活魂技描述
	-- 	local str = self:GetHolySkillFomatDesc(id, cur_level)
	-- 	skill_desc1 = string.format(Language.PrivilegeCollection.SkillDescNoBuy, skill_cfg.skill_name, str)
	-- else
	-- 	--当前魂技效果
	-- 	local str = self:GetHolySkillFomatDesc(id, cur_level)					
		
	-- 	--当前等级属性
	-- 	local is_normal_skill = skill_cfg.rate <= 0 or skill_cfg.process_type <= 0
	-- 	local attr_desc = ""
	-- 	if not is_normal_skill then
	-- 		local value_str1, skill_name1, value_str2, skill_name2 = self:GetHolySkillNameAndValueStr(id, cur_level, true)
	-- 		local skill_process = self:GetHolySkillProcessById(id)
	-- 		local process_num = ToColorStr(skill_process, COLOR3B.GREEN)
	-- 		local process_max_num1 = math.floor((cfg.max_process * skill_cfg.attr_value1) / skill_cfg.rate)

	-- 		local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(skill_cfg.attr_id1)
	-- 		process_max_num1 = self:GetHolySkillValueStr(process_max_num1, attr_str)
	-- 		if skill_name2 == "" then

	-- 			attr_desc = self:FormatHolySkillDescSafely(skill_cfg.desc2, process_num, cfg.max_process,
	-- 				value_str1, process_max_num1 .. skill_name1)

	-- 		else
	-- 			local process_max_num2 = math.floor((cfg.max_process * skill_cfg.attr_value2) / skill_cfg.rate)

	-- 			attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(skill_cfg.attr_id2)
	-- 			process_max_num2 = self:GetHolySkillValueStr(process_max_num2, attr_str)
	-- 			attr_desc = self:FormatHolySkillDescSafely(skill_cfg.desc2, process_num, cfg.max_process,
	-- 				value_str1, process_max_num1 .. skill_name1, value_str2, process_max_num2 .. skill_name2)

	-- 		end
			
				
	-- 		attr_desc = string.format(Language.PrivilegeCollection.HolySkillAttrCur, attr_desc)	
	-- 	end

	-- 	skill_desc1 = string.format(Language.PrivilegeCollection.HolySkillStr, str .. attr_desc)
	-- 	--下级魂技效果
	-- 	if cur_level < max_level then
	-- 		skill_desc2 = self:GetHolySkillFomatDesc(id, cur_level + 1)			
	-- 		skill_desc2 = string.format(Language.PrivilegeCollection.HolySkillNext, skill_desc2)
	-- 	end						
	-- end
	

	-- return skill_desc1 .. skill_desc2
end

-- function PrivilegeCollectionWGData:FormatHolySkillDescSafely(desc, ...)
-- 	if type(desc) ~= "string" then
-- 		return ""
-- 	end

-- 	local _, need_count = string.gsub(desc, "%%s", "")
-- 	local has_count = select("#", ...)
-- 	if need_count <= 0 then
-- 		return desc
-- 	elseif has_count >= need_count then
-- 		return string.format(desc, ...)
-- 	else
-- 		print_error("参数不匹配，字符串格式化失败！请检查一下配置表：", desc)
-- 	end

-- 	return ""
-- end

-- function PrivilegeCollectionWGData:GetHolySkillFomatDesc(id, level)
-- 	local cfg = self:GetHolyWeaponCfg(id, level <=0 and level + 1 or level)
-- 	local skill_cfg = self:GetHolySkillCfgById(cfg and cfg.skill_id)
-- 	if IsEmptyTable(skill_cfg) or IsEmptyTable(cfg) then
-- 		return ""
-- 	end
	
-- 	local str = ""
-- 	local is_normal_skill = skill_cfg.rate <= 0 or skill_cfg.process_type <= 0
-- 	local value_str1, skill_name1, value_str2, skill_name2 = self:GetHolySkillNameAndValueStr(id, level, false)
-- 	if is_normal_skill and skill_name2 == "" then
-- 		str = self:FormatHolySkillDescSafely(skill_cfg.desc, value_str1 .. skill_name1)
-- 	elseif is_normal_skill then
-- 		str = self:FormatHolySkillDescSafely(skill_cfg.desc, value_str1 .. skill_name1, value_str2 .. skill_name2)
-- 	elseif not is_normal_skill and skill_name2 == "" then
-- 		local rate_str = ToColorStr(skill_cfg.rate, COLOR3B.GREEN)
-- 		local max_str = ToColorStr(cfg.max_process, COLOR3B.GREEN)
-- 		str = self:FormatHolySkillDescSafely(skill_cfg.desc,
-- 					rate_str, value_str1 .. skill_name1, max_str)
-- 	else
-- 		local rate_str = ToColorStr(skill_cfg.rate, COLOR3B.GREEN)
-- 		local max_str = ToColorStr(cfg.max_process, COLOR3B.GREEN)
-- 		str = self:FormatHolySkillDescSafely(skill_cfg.desc,
-- 					rate_str, value_str1 .. skill_name1, value_str2 .. skill_name2, max_str)
-- 	end
						
-- 	return str
-- end

function PrivilegeCollectionWGData:GetHolySkillNameAndValueStr(id, level, need_process)
	local cfg = self:GetHolyWeaponCfg(id, level <=0 and level + 1 or level)
	local skill_cfg = self:GetHolySkillCfgById(cfg and cfg.skill_id)
	if IsEmptyTable(skill_cfg) or IsEmptyTable(cfg) then
		return "", "", "", ""
	end
	
	local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(skill_cfg.attr_id1)
	local skill_name1 = EquipmentWGData.Instance:GetAttrName(attr_str, false, false)
	local value1 = skill_cfg.attr_value1

	local skill_name2, value2 = "", 0
	if skill_cfg.attr_id2 > 0 or skill_cfg.attr_value2 >0 then
		attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(skill_cfg.attr_id2)
		skill_name2 = EquipmentWGData.Instance:GetAttrName(attr_str, false, false)
		value2 = skill_cfg.attr_value2
	end

	local is_normal_skill = skill_cfg.rate <= 0 or skill_cfg.process_type <= 0
	if not is_normal_skill and need_process then
		local skill_process = self:GetHolySkillProcessById(id)
		local process_num = math.floor(skill_process / skill_cfg.rate)
		value1 = process_num * skill_cfg.attr_value1
		value2 = process_num * skill_cfg.attr_value2
	end

	local value_str1 = ToColorStr(self:GetHolySkillValueStr(value1, attr_str), COLOR3B.GREEN)
	local value_str2 = ToColorStr(self:GetHolySkillValueStr(value2, attr_str), COLOR3B.GREEN)

	return value_str1, skill_name1, value_str2, skill_name2
end

function PrivilegeCollectionWGData:GetHolySkillAttrList(id, level)
	level = level == nil and self:GetHolyWeaponLevel(id) or level
	local cfg = self:GetHolyWeaponCfg(id, level <= 0 and level + 1 or level)
	local skill_cfg = self:GetHolySkillCfgById(cfg and cfg.skill_id)
	if IsEmptyTable(skill_cfg) or IsEmptyTable(cfg) then
		return "", ""
	end
	
	local is_normal_skill = skill_cfg.rate <= 0 or skill_cfg.process_type <= 0
	local list, attr, skill_process, process_num, attr_str = {}, nil, nil, nil, nil
	for i = 1, PrivilegeCollectionWGData.HOLY_WEAPON_SKILL_ATTR_NUM do
		if skill_cfg["attr_id" .. i] > 0 then 
			attr = {}
			attr.attr_id = skill_cfg["attr_id" .. i]
			attr.attr_value = skill_cfg["attr_value" .. i]

			if not is_normal_skill then
				skill_process = self:GetHolySkillProcessById(id)
				process_num = math.floor(skill_process / skill_cfg.rate)
				attr.attr_value = process_num * attr.attr_value
			end

			attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr.attr_id)
			attr.attr_str = attr_str
			attr.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			table.insert(list, attr)
		end
	end

	return list
end

function PrivilegeCollectionWGData:GetHolySkillCfgById(skill_id)
	return self.holy_weapon_skill_cfg[skill_id] or {}
end

function PrivilegeCollectionWGData:GetHolyWeaponCap(id)
	local attribute = AttributePool.AllocAttribute()
	local cap = 0
	local cur_level = self:GetHolyWeaponLevel(id)
	cur_level = cur_level > 0 and cur_level or 1
	local attr_list = self:GetHolyWeaponAttrList(id, cur_level)
	if not IsEmptyTable(attr_list) then
		for i, v in pairs(attr_list) do
        	attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end
	end

	local holy_skill_attr_list = self:GetHolySkillAttrList(id, cur_level)
	if not IsEmptyTable(holy_skill_attr_list) then
		for i, v in pairs(holy_skill_attr_list) do
        	attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end
	end

	cap = AttributeMgr.GetCapability(attribute)
	return cap
end

function PrivilegeCollectionWGData:GetHolyWeaponOpenState()
	local cfg = self.holy_weapon_other_cfg[1]
	if IsEmptyTable(cfg) then
		return false
	end

	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	return role_level >= cfg.role_level
end

function PrivilegeCollectionWGData:GetHolySkillValueStr(value, attr_str)
	local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_str)
	local value = is_per and value / 100 .. "%" or value
	return value
end

function PrivilegeCollectionWGData:GetHolySkillProcessById(id)
	return (self.holy_skill_process_list or {})[id] or 0
end

function PrivilegeCollectionWGData:GetHolyWeaponViewShowCfg(id)
	return self.holy_weapon_view_show_cfg[id]
end
---------------------------------------------------------------------------------------

-----------------------------------Cal_START------------------------------
function PrivilegeCollectionWGData:IsCanShowZJTQ()
	local data_list = YanYuGeWGData.Instance:GetZZTQDataList()

	for k, v in pairs(data_list) do
		if not YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(v.seq) then
			return false
		end
	end

	return true
end

function PrivilegeCollectionWGData:GetShowModelCfgListBySeq(seq)
	return YanYuGeWGData.Instance:GetShowModelCfgListBySeq(seq)
end

function PrivilegeCollectionWGData:GetShowModelThemeCfgBySeq(seq)
	return YanYuGeWGData.Instance:GetShowModelThemeCfgBySeq(seq)
end
-------------------------------------CAL_END-------------------------------

-----------------------------------守护特权 start ------------------------------
function PrivilegeCollectionWGData:GetGuardPrivilegeCfg()
	return self.guard_privilege_cfg
end

function PrivilegeCollectionWGData:GetGuardPrivilegeOther()
	return self.guard_privilege_other_cfg
end

function PrivilegeCollectionWGData:SetGuardPrivilegeList(protocol)
	self.guard_privilege_buy_flag = protocol.privilege_buy_flag			-- 特权购买标记
	self.guard_privilege_fetch_exp_flag = protocol.fetch_exp_flag		-- 经验领取标记
	self.guard_privilege_save_exp = protocol.save_exp					-- 储存经验
end

--判断首次下发协议不显示恭喜获得.
function PrivilegeCollectionWGData:GetGuardPrivilegeIsFirstSend()
	return IsEmptyTable(self.guard_privilege_buy_flag)
end

--是否激活.
function PrivilegeCollectionWGData:GetGuardPrivilegeIsActivate()
	local is_act = false
	for k, v in pairs(self.guard_privilege_buy_flag) do
		if v == 1 then
			is_act = true
			break
		end
	end

	return is_act
end

--是否买完领取完所有档位奖励.
function PrivilegeCollectionWGData:GetGuardPrivilegeIsSellOut()
	return self.guard_privilege_fetch_exp_flag[#self.guard_privilege_cfg] == 1
end

function PrivilegeCollectionWGData:GetGuardPrivilegeSeq()
	local seq = -1
	for i = 0, #self.guard_privilege_buy_flag - 1 do
		local show_cfg = self:GetGuardPrivilegeCfgBySeq(i)
		if self.guard_privilege_buy_flag[i] == 1 and show_cfg then
			seq = i
		end
	end

	return seq
end

function PrivilegeCollectionWGData:GetGuardPrivilegeCfgBySeq(seq)
	return self.guard_privilege_cfg[seq]
end

--未领取经验值的档位.
function PrivilegeCollectionWGData:GetGuardPrivilegeExpFlag()
	local is_act = self:GetGuardPrivilegeIsActivate()
	local cur_seq = self:GetGuardPrivilegeSeq()

	local seq = -1
	if is_act then
		for i = 0, #self.guard_privilege_fetch_exp_flag - 1 do
			local show_cfg = self:GetGuardPrivilegeCfgBySeq(i)
			if self.guard_privilege_fetch_exp_flag[i] == 0 and show_cfg then
				seq = i
				break
			end
		end
	end

	return seq
end

function PrivilegeCollectionWGData:GetGuardPrivilegeSaveExp()
	--不计算经验衰减.
	-- local exp_per = RoleWGData.Instance:GetRoleExpCorrection()
	return self.guard_privilege_save_exp-- * exp_per or 0
end

function PrivilegeCollectionWGData:SaveExpCanUpLevel(seq)
	--可升级的经验值，从多少级开始算.
	local function calc_func(exp, level)
		local role_level = level
		--角色可用来升级的经验
		local sum_pool = exp
		--当前等级角色升级所需经验
		local max_exp

		--可升级别
		local can_up_exp
		for i = 0, RoleWGData.GetRoleMaxLevel() do
			local level = role_level + i
			if level > RoleWGData.GetRoleMaxLevel() then
				level = RoleWGData.GetRoleMaxLevel()
			end

			local cfg = RoleWGData.Instance.GetRoleExpCfgByLv(level)
			if cfg then
				max_exp = cfg.exp
				can_up_exp = sum_pool/max_exp
				if sum_pool - max_exp >= 0 then
					sum_pool = sum_pool - max_exp 
				else
					--可提升等级，可提升百分百.
					return i, can_up_exp
				end
			else
				break
			end
		end
		return 0, 0
	end

	local show_cfg = self:GetGuardPrivilegeCfgBySeq(seq)
	local pool_exp = self:GetGuardPrivilegeSaveExp()
	if pool_exp <= 0 then
		return 0, 0
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local role_exp = role_vo.exp
	local role_level = role_vo.level

	--计算溢出等级.
	local overflow_lv, overflow_exp_pro = calc_func(role_exp, role_level)
	--计算实际等级.
	local reality_lv = role_level + overflow_lv
	--计算实际可增加的经验值.
	local reality_exp = pool_exp + overflow_exp_pro
	--计算经验池实际可增加等级.
	local can_up_lv, can_up_exp_pro = calc_func(reality_exp, reality_lv)

	return can_up_lv, can_up_exp_pro
end

function PrivilegeCollectionWGData:GetGuardPrivilegeSHTQHundredfoldDropPro()
	local add_value = 0
	local is_act = self:GetGuardPrivilegeIsActivate()
	if is_act then
		local cur_seq = self:GetGuardPrivilegeSeq()
		local cur_cfg = self:GetGuardPrivilegeCfgBySeq(cur_seq)
		add_value = cur_cfg.up_num * 100
	end

	return add_value
end

function PrivilegeCollectionWGData:GetGuardPrivilegeSHTQMainTipsFlag()
	return self.guard_privilege_show_main_tips
end

function PrivilegeCollectionWGData:SetGuardPrivilegeSHTQMainTipsFlag(value)
	self.guard_privilege_show_main_tips = value
end
-------------------------------------守护特权 end-------------------------------