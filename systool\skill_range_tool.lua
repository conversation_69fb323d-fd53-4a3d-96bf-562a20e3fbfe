SkillRangeTool = SkillRangeTool or BaseClass()

-- 这是用来GM测试用的，用来方便测试看数据
local MaxTime = 2

function SkillRangeTool:__init()
	self.pos_list = {}
	self.reset_timer = 0
	self.is_need_check = false
	self.line_render = nil
	self.is_open_darw = false
	self.draw_line_obj = nil
end

function SkillRangeTool:DrawSkillRange(range_type, pos, r, r2, angle)
	if IsNil(self.line_render) then
		return
	end

	self.pos_list = {}
	local z_value = 0

	if range_type == ATTACK_RANGE_TYPE.SQUARE_ME then
		z_value = pos.y
		self:DrawSquare(pos.x, pos.z, pos.y, self:GetRealRange(r))
	elseif range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET then
		z_value = pos.y
		self:DrawSquare(pos.x, pos.z, pos.y, self:GetRealRange(r))
	elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_ME then
		z_value = pos.y
		self:DrawCircular(pos.x, pos.z, pos.y, self:GetRealRange(r))
	elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET then
		z_value = pos.y
		self:DrawCircular(pos.x, pos.z, pos.y, self:GetRealRange(r))
	elseif range_type == ATTACK_RANGE_TYPE.RECTANGLE_ME or range_type == ATTACK_RANGE_TYPE.SQUARE_CHONG_FENG then
		z_value = pos.y
		self:DrawRectangle(pos.x, pos.z, pos.y, self:GetRealRange(r), self:GetRealRange(r2))
	elseif range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
		-- if deliverer == nil then
		-- 	return
		-- end

		-- local pos = deliverer:GetLuaPosition()
		-- local x, y = deliverer:GetLogicPos()
		z_value = pos.y

		-- local de_draw_obj = deliverer:GetDrawObj()
		-- if de_draw_obj == nil or de_draw_obj.root_transform == nil then
		-- 	return
		-- end

  --   	local angle = de_draw_obj.root_transform.eulerAngles.y
		self:DrawSector(pos.x, pos.z, pos.y, self:GetRealRange(r), self:GetRealRange(r2), angle)
	end

	local count = #self.pos_list
	if count == 0 then
		return
	end

	if IsNil(self.line_render) then
		return
	end

	local index = 0
	self.line_render.positionCount = count

	for k,v in pairs(self.pos_list) do
		self.line_render:SetPosition(index, Vector3(v.x, z_value + 1.5, v.y))
		index = index + 1
	end

	self.reset_timer = Status.NowTime
	self.is_need_check = true
end

function SkillRangeTool:GetRealRange(range)
	return range
end

function SkillRangeTool:CheckDel()
	if IsNil(self.line_render) then
		return
	end

	if Status.NowTime - self.reset_timer > MaxTime and self.is_need_check then
		if IsNil(self.line_render) then
			return
		end

		self.line_render.positionCount = 0
		self.is_need_check = false	
	end
end

function SkillRangeTool:DrawSquare(x, y, z, range)
	if self.line_render == nil then
		return
	end

	local r = range * 0.5
	self.pos_list[1] = {x = x - r, y = y - r}
	self.pos_list[2] = {x = x - r, y = y + r}
	self.pos_list[3] = {x = x + r, y = y + r}
	self.pos_list[4] = {x = x + r, y = y - r}
	self.pos_list[5] = {x = x - r, y = y - r}
	self.pos_list[6] = {x = x - r, y = y - r}
	self.pos_list[7] = {x = x - r, y = y + r}
	self.pos_list[8] = {x = x + r, y = y + r}
	self.pos_list[9] = {x = x + r, y = y - r}
end

function SkillRangeTool:DrawCircular(x, y, z, range)
	if self.line_render == nil then
		return
	end

	local r = range
	local angle = math.rad(360 / 50)
	for i = 1, 50 do
		local pos = {}
		pos.x = x + math.sin(angle * i) * r
		pos.y = y + math.cos(angle * i) * r
		-- local q = Quaternion.Euler(rotate.eulerAngles.x, rotate.eulerAngles.y - (angle * i), rotate.eulerAngles.z)
		-- pos.x = x + q.x * r
		-- pos.y = y + q.y * r
		table.insert(self.pos_list, pos)
	end
	table.insert(self.pos_list, self.pos_list[#self.pos_list])
end

function SkillRangeTool:DrawRectangle(x, y, z, range, range2)
	if self.line_render == nil then
		return
	end

	local r_w = range * 0.5
	local r_h = range2 * 0.5
	self.pos_list[1] = {x = x - r_w, y = y - r_h}
	self.pos_list[2] = {x = x - r_w, y = y + r_h}
	self.pos_list[3] = {x = x + r_w, y = y + r_h}
	self.pos_list[4] = {x = x + r_w, y = y - r_h}
	self.pos_list[5] = {x = x - r_w, y = y - r_h}
	self.pos_list[6] = {x = x - r_w, y = y - r_h}
	self.pos_list[7] = {x = x - r_w, y = y + r_h}
	self.pos_list[8] = {x = x + r_w, y = y + r_h}
	self.pos_list[9] = {x = x + r_w, y = y - r_h}
end

function SkillRangeTool:DrawSector(x, y, z, range, range2, root_angle)
	if self.line_render == nil then
		return
	end

	local r = range
	local start = root_angle - range2 * 0.5
	local num = range2 / 10
	table.insert(self.pos_list, {x = x, y = y})
	for i = 0, num do
		local pos = {}
		local angle = math.rad(i / num * range2 + start)
		pos.x = x + math.sin(angle) * r
		pos.y = y + math.cos(angle) * r
		table.insert(self.pos_list, pos)
	end
	table.insert(self.pos_list, {x = x, y = y})
end

function SkillRangeTool:SetDrawLineObj(draw_line_obj)
	self.draw_line_obj = draw_line_obj
end

function SkillRangeTool:GetLineRender()
	if not self.is_open_darw then
		self.line_render = nil
		return
	end

	if IsNil(self.line_render) then
		if self.draw_line_obj ~= nil then
			self.line_render = self.draw_line_obj:GetComponent(typeof(UnityEngine.LineRenderer))
		end
	end
end

function SkillRangeTool:SetOpenDraw(is_open_darw)
	self.is_open_darw = is_open_darw
	if self.is_open_darw then
		self:GetLineRender()
	else
		if not IsNil(self.line_render) then
			self.line_render.positionCount = 0
			self.is_need_check = false			
		end
	end
end
