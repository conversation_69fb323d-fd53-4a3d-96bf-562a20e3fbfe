return {
	["other"]={
		{draw_once_need_score=8000,open_level=610,first_draw_reward_item={item_id=22000,num=1,is_bind=1,},draw_ten_times_need_score=75000,},},
	["online_reward"]={
		{consume_item_count=50,reward_bind_gold_max_count=200,reward_bind_gold=4,reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin=10000,},
		{consume_item_count=130,reward_bind_gold_max_count=250,reward_bind_gold=6,reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin_max_count=1200000,level=2,},
		{consume_item_count=210,reward_bind_gold_max_count=300,reward_bind_gold=8,reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin_max_count=1600000,level=3,reward_bind_coin=20000,},
		{reward_bind_gold=10,reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin_max_count=2000000,level=4,reward_bind_coin=25000,},
		{consume_item_count=370,reward_bind_gold_max_count=400,reward_bind_gold=12,reward_bind_coin_max_count=2400000,level=5,reward_bind_coin=30000,},
		{consume_item_count=450,reward_bind_gold_max_count=450,reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin_max_count=2800000,level=6,reward_bind_coin=35000,},
		{consume_item_count=550,reward_bind_gold_max_count=500,reward_bind_gold=16,reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin_max_count=3200000,level=7,reward_bind_coin=40000,},
		{consume_item_count=650,reward_bind_gold_max_count=550,reward_bind_gold=18,reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin_max_count=3600000,level=8,reward_bind_coin=45000,},
		{consume_item_count=750,reward_bind_gold_max_count=600,reward_bind_gold=20,reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin_max_count=4000000,level=9,reward_bind_coin=50000,},
		{consume_item_count=900,reward_bind_gold_max_count=650,reward_bind_gold=22,reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin_max_count=4400000,level=10,reward_bind_coin=55000,},},
	["draw_reward_info"]={
		[0]={weight=10000,reward_item={item_id=22000,num=1,is_bind=1,},seq=0,},
		[2]={weight=3000,reward_item={item_id=22000,num=1,is_bind=1,},seq=2,},
		[4]={weight=1000,reward_item={item_id=22000,num=1,is_bind=1,},seq=4,},
		[8]={weight=400,reward_item={item_id=22000,num=1,is_bind=1,},seq=8,},
		[16]={reward_item={item_id=22000,num=1,is_bind=1,},seq=16,},
		[17]={reward_item={item_id=22000,num=1,is_bind=1,},seq=17,},
		[9]={weight=200,reward_item={item_id=22000,num=1,is_bind=1,},seq=9,},
		[18]={seq=18,},
		[5]={reward_item={item_id=22000,num=1,is_bind=1,},seq=5,is_show=1,},
		[10]={weight=5000,reward_item={item_id=22000,num=1,is_bind=1,},seq=10,},
		[20]={reward_item={item_id=22000,num=1,is_bind=1,},seq=20,},
		[15]={weight=1000,reward_item={item_id=22000,num=1,is_bind=1,},seq=15,},
		[21]={is_broadcast=1,weight=50,reward_item={item_id=22000,num=1,is_bind=1,},seq=21,is_show=1,},
		[11]={weight=12000,reward_item={item_id=22000,num=1,is_bind=1,},seq=11,},
		[22]={is_broadcast=1,weight=50,reward_item={item_id=22000,num=1,is_bind=1,},seq=22,},
		[3]={weight=2000,reward_item={item_id=22000,num=1,is_bind=1,},seq=3,},
		[6]={weight=1200,reward_item={item_id=22000,num=1,is_bind=1,},seq=6,},
		[12]={weight=20000,reward_item={item_id=22000,num=1,is_bind=1,},seq=12,},
		[24]={is_broadcast=1,weight=50,reward_item={item_id=22000,num=1,is_bind=1,},seq=24,},
		[23]={is_broadcast=1,weight=50,reward_item={item_id=22000,num=1,is_bind=1,},seq=23,},
		[25]={is_broadcast=1,weight=50,reward_item={item_id=22000,num=1,is_bind=1,},seq=25,},
		[13]={weight=5000,reward_item={item_id=22000,num=1,is_bind=1,},seq=13,},
		[26]={weight=3000,reward_item={item_id=22000,num=1,is_bind=1,},seq=26,},
		[27]={weight=3000,reward_item={item_id=22000,num=1,is_bind=1,},seq=27,},
		[7]={weight=800,reward_item={item_id=22000,num=1,is_bind=1,},seq=7,},
		[14]={weight=2000,reward_item={item_id=22000,num=1,is_bind=1,},seq=14,},
		[28]={weight=3000,reward_item={item_id=22000,num=1,is_bind=1,},seq=28,is_show=1,},
		[19]={reward_item={item_id=22000,num=1,is_bind=1,},seq=19,},
		[29]={weight=2000,reward_item={item_id=22000,num=1,is_bind=1,},seq=29,},
		[1]={weight=5000,reward_item={item_id=22000,num=1,is_bind=1,},},},
	["upgrade"]={
		{maxhp=0,grade=0,gongji=0,need_val=800,fangyu=0,},
		{consume_item_count=2,maxhp=5110,gongji=435,need_val=1000,fangyu=348,},
		{consume_item_count=4,grade=2,gongji=1377,need_val=1100,fangyu=1101,},
		{consume_item_count=6,maxhp=34370,grade=3,gongji=2927,need_val=1200,},
		{consume_item_count=8,maxhp=61120,grade=4,need_val=1500,fangyu=4163,},
		{consume_item_count=10,maxhp=98160,grade=5,gongji=8361,need_val=2000,fangyu=6686,},
		{consume_item_count=13,maxhp=147520,grade=6,gongji=12565,fangyu=10049,},
		{consume_item_count=18,maxhp=211670,grade=7,gongji=18029,fangyu=14418,},
		{consume_item_count=24,maxhp=293570,grade=8,gongji=25005,need_val=4000,fangyu=19997,},
		{consume_item_count=30,maxhp=396770,grade=9,gongji=33796,need_val=5000,fangyu=27027,},
		{consume_item_count=99,maxhp=525540,grade=10,gongji=44764,need_val=9999,fangyu=35799,},},
	["online_reward_default_table"]={consume_item_count=290,consume_item=22000,reward_bind_gold_max_count=350,reward_bind_gold=14,
		reward_gift={item_id=22000,num=1,is_bind=1,},reward_bind_coin_max_count=800000,reward_bind_gold_interval=20,level=1,reward_gift_interval=180,reward_bind_coin_interval=10,reward_bind_coin=15000,},
	["draw_reward_info_default_table"]={is_broadcast=0,weight=4000,
		reward_item={item_id=22000,num=1,is_bind=1,},seq=1,is_show=0,},
	["upgrade_default_table"]={add_val=10,consume_item_count=1,consume_item=22000,maxhp=16170,grade=1,jianren=0,gongji=5206,shanbi=0,mingzhong=0,baoji=0,need_val=3000,fangyu=2341,},
}
