

XianQiJieFengRewardTips = XianQiJieFengRewardTips or BaseClass(SafeBaseView)

function XianQiJieFengRewardTips:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self.view_name = "TianShenRoadUi"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 2), sizeDelta = Vector2(730,464)})
	self:AddViewResource(0, "uis/view/act_xianqi_jiefeng_ui_prefab", "layout_lhbx_reward")
end

function XianQiJieFengRewardTips:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function XianQiJieFengRewardTips:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.XiuZhenRoad.ViewName
	self.reward_list_view = AsyncListView.New(XianQiJieFengReWardItemRender, self.node_list["ph_item_list"])
	self:FlushView()
end

function XianQiJieFengRewardTips:OnFlush(param_t, index)
	self:FlushView()
end

function XianQiJieFengRewardTips:FlushView()
	local cfg = ActXianQiJieFengWGData.Instance:GetXQRewardCfg()
	--数据处理
	local data_list = {}
	for k,v in pairs(cfg) do
		local item = {}
		item.rewards = {}
		item.ID = v.ID
		item.score = v.jifen
		item.state = ActXianQiJieFengWGData.Instance:GetBXState(v.ID)
		for k2,v2 in pairs(v.reward_item) do
			table.insert(item.rewards,v2)
		end

		if item.state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.state == ActivityRewardState.BKL then
			item.sort = 1
		else
			item.sort = 2
		end
		table.insert(data_list, item)
	end
	table.sort(data_list, SortTools.KeyLowerSorter("sort","ID"))

	self.reward_list_view:SetDataList(data_list)
	self.node_list["star_num"].text.text = ActXianQiJieFengWGData.Instance:GetDangweiScore() .. "奖励点数"
end

-----------------------------------------------------------------------------------------------

XianQiJieFengReWardItemRender = XianQiJieFengReWardItemRender or BaseClass(BaseRender)

function XianQiJieFengReWardItemRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function XianQiJieFengReWardItemRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
end

function XianQiJieFengReWardItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return 
	end

	self.reward_list:SetDataList(SortTableKey(data.rewards))

	self.node_list["lbl_lscs"].text.text = self.data.score .. "奖励点数"
	self.node_list["btn_lingqu"]:SetActive(self.data.state == ActivityRewardState.KLQ)
	self.node_list["image_ylq"]:SetActive(self.data.state == ActivityRewardState.YLQ)
	self.node_list["btn_weilingqu"]:SetActive(self.data.state == ActivityRewardState.BKL)
end

function XianQiJieFengReWardItemRender:OnClickLingQu()
	ActXianQiJieFengWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_WOYAOLONGHUN,WOYAOXIANQI_OP_TYPE.DANGWEI_REWARD,self.data.ID)
end
