﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Nirvana;

public class UIReAdjustLocalPosToInt : MonoBehaviour
{
    private void LateUpdate()
    {
#if UNITY_EDITOR
        Text[] texts = this.GetComponentsInChildren<Text>();

        HashSet<Transform> needResets = new HashSet<Transform>();
        foreach (var text in texts)
        {
            if (!needResets.Contains(text.transform))
            {
                needResets.Add(text.transform);
            }

            RectTransform[] transforms = text.GetComponentsInParent<RectTransform>();

            foreach (var transform in transforms)
            {
                if (!needResets.Contains(transform))
                {
                    needResets.Add(transform);
                }
            }
        }

        foreach (var transform in needResets)
        {
            Vector3 pos = transform.localPosition;
            pos.x = System.Convert.ToInt32(pos.x);
            pos.y = System.Convert.ToInt32(pos.y);
            pos.z = System.Convert.ToInt32(pos.z);
            transform.localPosition = pos;
        }

#endif
    }
}
