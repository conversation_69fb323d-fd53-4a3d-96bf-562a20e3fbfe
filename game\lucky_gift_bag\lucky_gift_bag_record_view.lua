LuckyGiftBagRecordView = LuckyGiftBagRecordView or BaseClass(SafeBaseView)

function LuckyGiftBagRecordView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(850,500)})
    self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "Lucky_gift_bag_record_view")
	self:SetMaskBg(true, true)
end

function LuckyGiftBagRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function LuckyGiftBagRecordView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.LuckyGiftBag.RewardListTitle
	if nil == self.record_list then
		self.record_list = AsyncListView.New(LuckyGiftBagRecordRender, self.node_list["record_list"])
	end
end

function LuckyGiftBagRecordView:OnFlush()
    local data_list = LuckyGiftBagWgData.Instance:GetRateRecordList()
    if not IsEmptyTable(data_list) then
        self.record_list:SetDataList(data_list)
    end
    self.node_list["no_flag"]:SetActive(IsEmptyTable(data_list))
end

-------------------------------------------------------------------------------------
LuckyGiftBagRecordRender = LuckyGiftBagRecordRender or BaseClass(BaseRender)

function LuckyGiftBagRecordRender:OnFlush()
    if not self.data then
        return
    end

    local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if not cfg then
        print_error('物品配置读取不到   item_id:',self.data.item_id, self.data)
        return
    end
    local str = string.format(Language.LuckyGiftBag.Record, self.data.name, ITEM_COLOR[cfg.color], cfg.name, self.data.num)
    self.node_list["info"].text.text = str
end