EternalNightRankView = EternalNightRankView or BaseClass(SafeBaseView)

function EternalNightRankView:__init()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/eternal_night_ui_prefab", "layout_eternal_night_rank")
	self:SetMaskBg(true)
end

function EternalNightRankView:__delete()
end

function EternalNightRankView:LoadCallBack()
	self.list_view = AsyncListView.New(EternalNightRankItem,self.node_list["list_view"])
	self.item_list = AsyncListView.New(ItemCell,self.node_list["item_list"])
	self.item_list:SetCreateCellCallBack(function (cell)
		if cell.SetItemTipFrom then
			cell:SetItemTipFrom(ItemTip.FROM_ETERNAL_NIGHT)
		end
	end)

	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind1(self.Close, self))
	self.node_list.victory:SetActive(true)
	self.node_list.title_img.image:LoadSprite(ResPath.GetCommonPanel("a3_gxhd_lbl_pm"))
end

function EternalNightRankView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function EternalNightRankView:OnFlush()
	local rank_data = EternalNightWGData.Instance:GetRankList()
	self.list_view:SetDataList(rank_data)
	local my_data = EternalNightWGData.Instance:GetPlayerInfoBySelfId()
	if my_data then
		local rank = my_data and my_data.rank or 0
		local score = my_data and my_data.score or 0
		local combo_kill_num = my_data and my_data.combo_kill_num or 0
		local name = my_data and my_data.name or ""
		if rank <= 3 then
			self.node_list["rank_img"]:SetActive(true)
			self.node_list["rank_num"]:SetActive(false)
			self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..rank))
			self.node_list["rank_img"].image:SetNativeSize()
		else
			self.node_list["rank_img"]:SetActive(false)
			self.node_list["rank_num"]:SetActive(true)
		end
		self.node_list["rank_num"].text.text = rank
		self.node_list["score"].text.text = score
		self.node_list["kill_num"].text.text = combo_kill_num
		self.node_list["name"].text.text = name
	end
	local item_data = {}
	local self_info = EternalNightWGData.Instance:GetSelfInfo()
	if not IsEmptyTable(self_info) then
		local equip_list = self_info.equip_list
		for i=EternalNightWGData.StartJiPingNum,#equip_list do
			if equip_list[i] and equip_list[i] > 0 then
				local data = {item_id = equip_list[i]}
				table.insert(item_data,data)
			end
		end
		self.node_list["item_list"]:SetActive(not IsEmptyTable(item_data))
		self.node_list["not_item_list"]:SetActive(IsEmptyTable(item_data))
		if not IsEmptyTable(item_data) then
			self.item_list:SetDataList(item_data)
		end
	end
end


-----------------ItemRender-------------
EternalNightRankItem = EternalNightRankItem or BaseClass(BaseRender)

function EternalNightRankItem:__init()
	XUI.AddClickEventListener(self.node_list["go_btn"],BindTool.Bind(self.ClickGoBtn,self))
	self.item_list = AsyncListView.New(ItemCell,self.node_list["item_list"])
	self.item_list:SetCreateCellCallBack(function (cell)
		if cell.SetItemTipFrom then
			cell:SetItemTipFrom(ItemTip.FROM_ETERNAL_NIGHT)
		end
	end)
	self.all_obj_move_event = GlobalEventSystem:Bind(OtherEventType.SendAllObjMoveInfoEvent,BindTool.Bind(self.AllObjMoveInfoCallBack,self))
end

function EternalNightRankItem:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	if self.all_obj_move_event then
		GlobalEventSystem:UnBind(self.all_obj_move_event)
		self.all_obj_move_event = nil
	end
end

function EternalNightRankItem:OnFlush()
	if not self.data then return end
	if self.data.rank <= 3 then
		self.node_list["rank_img"]:SetActive(true)
		self.node_list["bg"]:SetActive(true)
		self.node_list["rank_num"]:SetActive(false)
		self.node_list["line"]:SetActive(false)
		self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..self.data.rank))
		self.node_list["rank_img"].image:SetNativeSize()
		self.node_list["bg"].image:LoadSprite(ResPath.GetCommonImages("a3_ty_list_"..self.data.rank))
	else
		self.node_list["rank_img"]:SetActive(false)
		self.node_list["bg"]:SetActive(false)
		self.node_list["line"]:SetActive(true)
		self.node_list["rank_num"]:SetActive(true)
		self.node_list["rank_num"].text.text = self.data.rank
	end

	local role_uuid = RoleWGData.Instance:GetUUid()
	local name = self.data.name
	if role_uuid == self.data.uuid then
		name = ToColorStr(name, COLOR3B.D_GREEN)
	end
	
	self.node_list["name"].text.text = name
	self.node_list["kill_num"].text.text = self.data.combo_kill_num
	self.node_list["score"].text.text = self.data.score
	local item_data = {}
	for i=EternalNightWGData.StartJiPingNum,#self.data.equip_list do
		if self.data.equip_list[i] and self.data.equip_list[i] > 0 then
			local data = {item_id = self.data.equip_list[i]}
			table.insert(item_data,data)
		end
	end

	self.node_list["item_list"]:SetActive(not IsEmptyTable(item_data))
	self.node_list["not_item_list"]:SetActive(IsEmptyTable(item_data))
	local role_uuid = RoleWGData.Instance:GetUUid()
	local is_self = role_uuid == self.data.uuid
	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType() or 1
	local live_time = self.data.live_time or 0
	local my_data = EternalNightWGData.Instance:GetPlayerInfoBySelfId()
	local my_live_time = my_data.live_time or 0
	if cur_grade_type == EternalNightWGData.GradeType.Eliminate then
		self.node_list["go_btn"]:SetActive(not is_self and live_time <= 0 and my_live_time <= 0)
	else
		self.node_list["go_btn"]:SetActive(not is_self)
	end
	if not IsEmptyTable(item_data) then
		self.item_list:SetDataList(item_data)
	end 
end

function EternalNightRankItem:ClickGoBtn()
	local scene_type = Scene.Instance:GetSceneType()
	EternalNightWGData.Instance:SetGoByRoleUUid(self.data.uuid)
	-- if scene_type == SceneType.ETERNAL_NIGHT_FINAL then
	-- 	Scene.Instance:SendGetAllObjMoveInfoReq()
	-- elseif scene_type == SceneType.ETERNAL_NIGHT then
	-- 	EternalNightWGCtrl.Instance:SendCSEternalNightGetPlayerPos(self.data.uuid)
	-- end
	EternalNightWGCtrl.Instance:SendCSEternalNightGetPlayerPos(self.data.uuid)
	ViewManager.Instance:Close(GuideModuleName.EternalNightRankView)
end

function EternalNightRankItem:AllObjMoveInfoCallBack()
	local scene_type = Scene.Instance:GetSceneType()
	local uuid = EternalNightWGData.Instance:GetGoByRoleUUid()
	if scene_type == SceneType.ETERNAL_NIGHT_FINAL and uuid then
		local move_obj,role_obj = EternalNightWGData.Instance:GetSceneRoleObjById(uuid)
		if move_obj and role_obj then
			local scene_id = Scene.Instance:GetSceneId()
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
		        GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, role_obj, SceneTargetSelectType.SCENE)
		    end)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, move_obj.pos_x, move_obj.pos_y,0)
		end
	end
end