{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10108_skill1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10108/10108_skill1_prefab", "AssetName": "10108_skill1", "AssetGUID": "7838b6a967e12344089e68025f66e3c6", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10108_skill2", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10108/10108_skill2_prefab", "AssetName": "10108_skill2", "AssetGUID": "47f84d7b9524ca84b8d880ce4d2a9e42", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10108_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10108/10108_skill3_prefab", "AssetName": "10108_skill3", "AssetGUID": "57459c906a1e2024fbb45024672c517a", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10108_attack", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10108/10108_attack_prefab", "AssetName": "10108_attack", "AssetGUID": "33256106bd75e364681042da381e8b90", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10108_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10108/10108_skill3_prefab", "AssetName": "10108_skill3", "AssetGUID": "57459c906a1e2024fbb45024672c517a", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": -1.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10108", "AssetName": "MingJiangcombo_1_1", "AssetGUID": "63ec4023d041a014a814fd12e560f77f", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10108", "AssetName": "MingJiangcombo_1_2", "AssetGUID": "0573bfc8ccfbbf84580730361cc7a2b1", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10108", "AssetName": "MingJiangcombo_1_3", "AssetGUID": "145d0f2d879d9554695871cd933c7859", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10108", "AssetName": "MingJiangattack1", "AssetGUID": "b5be331143c67e7468fe7030f39b4c0b", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10108", "AssetName": "MingJiangattack2", "AssetGUID": "3c330122274067a4c930e4ee57b97a2a", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10108", "AssetName": "MingJiangattack3", "AssetGUID": "ed102873b0f75cf4e9e960f56080dc3f", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack3", "soundBtnName": "attack3", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "attack1", "eventName": "attack1/begin", "numberOfShakes": 4, "distance": 1.0, "speed": 1000.0, "delay": 0.2, "decay": 0.0}], "radialBlurs": []}}