local MAX_MODEL_COUNT = 2
local MAX_ITEM_COUNT = 6

function TreasurePalaceView:LifeTimeRechargeInit()
    if not self.lifetime_toggle_list then
        self.lifetime_toggle_list = AsyncListView.New(LifeTimeRewardRender, self.node_list.lifetime_toggle_list)
        self.lifetime_toggle_list:SetSelectCallBack(BindTool.Bind(self.OnSelectLifeTimeToggle, self))
        self.lifetime_toggle_list:SetStartZeroIndex(true)
        self:FlushLifeTimeToggleList()
    end

    if not self.lifetime_model_list then
        self.lifetime_model_list = {}
        for i = 1, MAX_MODEL_COUNT do
            local model_render = OperationActRender.New(self.node_list["lifetime_model_pos_" .. i])
            model_render:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
            self.lifetime_model_list[i] = model_render
        end
    end

    -- if not self.lifetime_reward_list then
    --     self.lifetime_reward_list = {}
    --     for i = 0, MAX_ITEM_COUNT - 1 do
    --         local item_cell = ItemCell.New(self.node_list.lifetime_reward_list:FindObj("lifetime_cell_" .. i))
    --         item_cell:SetIsUseRoundQualityBg(true)
    --         item_cell:SetCellBgEnabled(false)
    --         self.lifetime_reward_list[i] = item_cell
    --     end
    -- end

    if not self.zbd_lefttime_reward_list then
        self.zbd_lefttime_reward_list = AsyncListView.New(ZBKLeftTimeRewardListItemRender, self.node_list.zbd_lefttime_reward_list)
    end

    self.cur_select_seq = -1
    XUI.AddClickEventListener(self.node_list.lifetime_get_btn, BindTool.Bind(self.OnClickLifeTimeGetBtn, self))
end

function TreasurePalaceView:LifeTimeRechargeReleaseCallBack()
    if self.lifetime_toggle_list then
        self.lifetime_toggle_list:DeleteMe()
        self.lifetime_toggle_list = nil
    end

    if self.lifetime_model_list then
        for k, v in pairs(self.lifetime_model_list) do
            v:DeleteMe()
        end

        self.lifetime_model_list = nil
    end

    -- if self.lifetime_reward_list then
    --     for k, v in pairs(self.lifetime_reward_list) do
    --         v:DeleteMe()
    --     end

    --     self.lifetime_reward_list = nil
    -- end

    if self.zbd_lefttime_reward_list then
        self.zbd_lefttime_reward_list:DeleteMe()
        self.zbd_lefttime_reward_list = nil
    end

    self.old_show_seq = nil
end

function TreasurePalaceView:LifeTimeRechargeShowIndexCallBack()
    self.lifetime_need_jump = true
    self.first_enter = true
    self:LifeTimeToggleListJumpToRemind()
end

function TreasurePalaceView:LifeTimeRechargeFlush()
    local cur_show_info = TreasurePalaceData.Instance:GetHistoryCfgBySeq(self.cur_select_seq)
    if not cur_show_info then
        return
    end

    local history_rmb_num = TreasurePalaceData.Instance:GetHistoryRMBNum()
    local can_get = history_rmb_num >= cur_show_info.need_num
    local is_get = TreasurePalaceData.Instance:GetHistoryRewardBySeq(cur_show_info.seq) > 0
    self.node_list.lifetime_get_btn:SetActive(not is_get)
    self.node_list.lifetime_remind:SetActive(not is_get and can_get)
    self.node_list.lifetime_get_flag:SetActive(is_get)

    local color = can_get and COLOR3B.DEFAULT_NUM or COLOR3B.PINK
    local need_num = RoleWGData.GetPayMoneyStr(cur_show_info.need_num)
    self.node_list.lifetime_history_rmb_num.text.text = string.format(Language.TreasurePalace.LifetimeHistoryRMBNum, color, history_rmb_num, need_num)
    self.node_list.lifetime_get_btn_text.text.text = can_get and Language.TreasurePalace.LifetimeCanGet or Language.TreasurePalace.LifetimeNoGet
end

function TreasurePalaceView:FlushLifeTimeRightPanel()
    local model_data_list = TreasurePalaceData.Instance:GetModelDataByTypeAndSeq(TreasurePalace_Model_Type.LifeTime, self.cur_select_seq)
    if IsEmptyTable(model_data_list) or self.old_show_seq == self.cur_select_seq then
        return
    end

    self.old_show_seq = self.cur_select_seq
    for k, v in pairs(self.lifetime_model_list) do
        local model_obj = self.node_list["lifetime_model_pos_" .. k]
        if model_data_list[k] then
            local display_data = model_data_list[k].display_data
            model_obj:SetActive(display_data.render_type ~= -1)
            if display_data.render_type == -1 then
                return
            end

            local transform_info = model_data_list[k].transform_info
            if not display_data.model_click_func then
                display_data.model_click_func = function ()
                    if display_data.itemid == nil or type(display_data.itemid) == "string" or display_data.itemid <= 0 then
                        return
                    end

                    TipWGCtrl.Instance:OpenItem({item_id = display_data.itemid})
                end
            end

            v:SetData(display_data)
            RectTransform.SetAnchoredPositionXY(model_obj.rect, transform_info.pos_x, transform_info.pos_y)
            model_obj.rect.rotation = Quaternion.Euler(transform_info.rot_x, transform_info.rot_y, transform_info.rot_z)
            Transform.SetLocalScaleXYZ(model_obj.transform, transform_info.scale, transform_info.scale, transform_info.scale)
        else
            model_obj:SetActive(false)
        end
    end

    self:FlushLifeTimeRewardInfo()
end

function TreasurePalaceView:FlushLifeTimeRewardInfo()
    -- local cur_show_info = TreasurePalaceData.Instance:GetHistoryCfgBySeq(self.cur_select_seq)
    -- if not cur_show_info then
    --     return
    -- end

    -- for k, v in pairs(self.lifetime_reward_list) do
    --     local cell_data = cur_show_info.reward_item[k]
    --     if cell_data then
    --         v:SetData(cell_data)
    --     end
    -- end

    local bundle, asset = ResPath.GetF2RawImagesPNG("a2_zbd_zcbd_title" .. self.cur_select_seq)
    self.node_list.lifetime_title_img.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.lifetime_title_img.raw_image:SetNativeSize()
    end)
end

function TreasurePalaceView:FlushLifeTimeToggleList()
    local toggle_data_list = TreasurePalaceData.Instance:GetHistoryToggleDataList()
    self.lifetime_toggle_list:SetDataList(toggle_data_list)
end

function TreasurePalaceView:LifeTimeToggleListJumpToRemind()
    if self.lifetime_need_jump then
        self.lifetime_need_jump = false
        local jump_index, is_jump = TreasurePalaceData.Instance:GetLifeTimeToggleListRemindIndex()
        if self.first_enter or is_jump then
            self.first_enter = false
            self.lifetime_toggle_list:JumpToIndex(jump_index)
        else
            self:LifeTimeRechargeFlush()
        end
    end
end

function TreasurePalaceView:OnSelectLifeTimeToggle(cell)
    local cell_data = cell:GetData()
    if not cell_data then
        return
    end

    if self.cur_select_seq == cell_data.seq then
        return
    end

    self.cur_select_seq = cell_data.seq
    self:FlushLifeTimeRightPanel()
    self:LifeTimeRechargeFlush()
    self:FlushLifeTimeRewardList()
end

function TreasurePalaceView:OnClickLifeTimeGetBtn()
    local cur_show_info = TreasurePalaceData.Instance:GetHistoryCfgBySeq(self.cur_select_seq)
    if not cur_show_info then
        return
    end

    if TreasurePalaceData.Instance:GetHistoryRMBNum() < cur_show_info.need_num then
        ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
        return
    end

    TreasurePalaceCtrl.Instance:SendZhenBaoDianClientReq(ZHENBAODIAN_OPERA_TYPE.GET_HISTORY_REWARD, cur_show_info.seq)
end

function TreasurePalaceView:FlushLifeTimeRewardList()
    local data_list = TreasurePalaceData.Instance:GetLifeTimeRewardDataListBySeq(self.cur_select_seq)
    self.zbd_lefttime_reward_list:SetDataList(data_list)
end

--------------- LifeTimeRewardRender终生累充二级页签 ---------------
LifeTimeRewardRender = LifeTimeRewardRender or BaseClass(BaseRender)

function LifeTimeRewardRender:OnFlush()
    if not self.data then
        return
    end

    local price = RoleWGData.GetPayMoneyStr(self.data.need_num)
    local recharge_str = string.format(Language.TreasurePalace.RechargeNum, price)
    self.node_list.nor_txt.text.text = recharge_str
    self.node_list.hl_txt.text.text = recharge_str

    local is_get = TreasurePalaceData.Instance:GetHistoryRewardBySeq(self.data.seq) > 0
    local can_get = TreasurePalaceData.Instance:GetHistoryRMBNum() >= self.data.need_num
    self.node_list.remind:SetActive(not is_get and can_get)
end

function LifeTimeRewardRender:OnSelectChange(is_select)
    self.node_list.nor_img:SetActive(not is_select)
    self.node_list.hl_img:SetActive(is_select)
end

ZBKLeftTimeRewardListItemRender = ZBKLeftTimeRewardListItemRender or BaseClass(BaseRender)

function ZBKLeftTimeRewardListItemRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function ZBKLeftTimeRewardListItemRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function ZBKLeftTimeRewardListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.reward_list:SetDataList(self.data.reward_item)
    self.node_list.desc_name.text.text = self.data.reward_title
end