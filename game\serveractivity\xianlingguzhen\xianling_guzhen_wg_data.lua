XianLingGuZhenWGData = XianLingGuZhenWGData or BaseClass()

function XianLingGuZhenWGData:__init()
	if XianLingGuZhenWGData.Instance then
		error("[XianLingGuZhenWGData] Attempt to create singleton twice!")
		return
	end
	XianLingGuZhenWGData.Instance = self
	-- self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
	local all_cfg = ConfigManager.Instance:GetAutoConfig("ra_lingxi_mangtu_auto")

	self.task_cfg = ListToMap(all_cfg.task, "grade", "task_id")
	self.special_reward_cfg = ListToMap(all_cfg.special_reward, "grade", "day", "sort_index")
	self.special_reward_id_cfg = ListToMap(all_cfg.special_reward, "grade", "day", "reward_pool_seq", "seq")
	self.normol_reward_cfg = ListToMap(all_cfg.normol_reward, "grade", "seq", "sort_index")
	self.item_random_desc = ListToMapList(all_cfg.item_random_desc, "grade")
	self.server_draw_times_reward_cfg = ListToMap(all_cfg.server_draw_times_reward, "grade", "seq")
	self.open_day_cfg = all_cfg.open_day
	self.other_cfg = all_cfg.other[1]

	-- self.lingxi_item_cfg = ListToMap(all_cfg.item,"open_server_day","order")
	-- self.show_way_list_cfg = ListToMap(all_cfg.show,"order")
	-- self.re_model_show = ListToMap(all_cfg.re_model_show, "itemid")

	self.no_loger_tip = false
	self.no_ohdj_tip = false
	self.jump_anim = false
	self.next_trun_wait_time = 20
	self.show_record_info = {}
	self.cur_show_index = 0
	self.xianling_resultreward_datalist = {}
	self.reward_info_data = {}
	self.draw_times = 0
	self.server_draw_times_reward_flag = {}

	-- self:InitAllCfgByItemId()
	if nil == self.item_change_callback then
		self.item_change_callback = BindTool.Bind(self.ItemChangeCallBack,self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)
	end

	--RemindManager.Instance:Register(RemindName.XianLing_GuZhen, BindTool.Bind(self.GetXianLingGuZhenRed, self))
	RemindManager.Instance:Register(RemindName.XianLing_GuZhen_DRAW, BindTool.Bind(self.GetXianLingGuZhenRed, self))
	RemindManager.Instance:Register(RemindName.XianLingGuZhen_QMHB, BindTool.Bind(self.GetXianLingGuZhenQMHBRed, self))
	RemindManager.Instance:Register(RemindName.XianLingGuZhen_QMLJ, BindTool.Bind(self.GetXianLingGuZhenQMLJRed, self))

end

function XianLingGuZhenWGData:__delete()
	-- if nil ~= self.pass_day_event then
	-- 	GlobalEventSystem:UnBind(self.pass_day_event)
	-- 	self.pass_day_event = nil
	-- end

	if self.item_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
		self.item_change_callback = nil
	end

	self.xianling_resultreward_datalist = nil

	--RemindManager.Instance:UnRegister(RemindName.XianLing_GuZhen)
	RemindManager.Instance:UnRegister(RemindName.XianLing_GuZhen_DRAW)
	RemindManager.Instance:UnRegister(RemindName.XianLingGuZhen_QMHB)
	RemindManager.Instance:UnRegister(RemindName.XianLingGuZhen_QMLJ)

	--self.all_cfg_by_item_id = nil
	XianLingGuZhenWGData.Instance = nil
end

---------------------------------------REMIND_START-------------------------------------
function XianLingGuZhenWGData:GetXianLingGuZhenRed()
	if self.xianling_task and not IsEmptyTable(self.xianling_task) then
		for k, v in pairs(self.xianling_task) do
			if 1 == self:GetXianLingTaskStatus2(v.task_id) then
				return 1
			end
		end
	end

	if not self:IsGetDailyReward() then
		return 1
	end

	return self:CheckCanDraw()
end

function XianLingGuZhenWGData:CheckCanDraw()
	local consume_item = self:GetConsumeItemID()
	if IsEmptyTable(consume_item) then
		return 0
	end

	local have_num = ItemWGData.Instance:GetItemNumInBagById(consume_item.item_id) or 0
	local need_num = consume_item.num or 1
	local red = need_num <= have_num and 1 or 0
	return red
end

-- 有能够领取的红包
function XianLingGuZhenWGData:GetXianLingGuZhenQMHBRed()
	local red_paper_data, world_red_paper, wait_data_list = self:GetQMHBRedPaperDataList()

	if not IsEmptyTable(wait_data_list) then
		return 1
	end

	if not IsEmptyTable(red_paper_data) then
		for k, v in pairs(red_paper_data) do
			local red_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperCfgByTypeAndLevel(v.paper_type, v.paper_level)

			if not IsEmptyTable(red_cfg) then
				local record_list = v.record_list
				local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
				local is_get = false
		
				local get_over = #record_list >= red_cfg.num
				if not IsEmptyTable(record_list) then
					for i, u in pairs(record_list) do
						if u.uid == my_uid then
							is_get = true
							break
						end
					end
				end

				if not get_over and not is_get then
					return 1
				end
			end
		end
	end

	return 0
end

function XianLingGuZhenWGData:GetXianLingGuZhenQMLJRed()
	local data_list = self:GetCurServerDrawTimeRewardCfg()

	if not IsEmptyTable(data_list) then
		local draw_time = self:GetNewLingXiMangTuServerDrawTimes()

		for k, v in pairs(data_list) do
			local time_enogth = draw_time >= v.draw_times
			local get_flag = self:GetServerDrawTimesRewardFlag(v.seq)
			local is_get = get_flag == 1

			if not is_get and time_enogth then
				return 1
			end
		end
	end

	return 0
end

function XianLingGuZhenWGData:GetXianLingTaskRed()
	if self.xianling_task and not IsEmptyTable(self.xianling_task) then
		for k, v in pairs(self.xianling_task ) do
			if 1 == self:GetXianLingTaskStatus2(v.task_id) then
				return true
			end
		end
	end

	return false
end
----------------------------------------REMIND_END--------------------------------------

---------------------------------------PROTOCOL_START-----------------------------------
function XianLingGuZhenWGData:ItemChangeCallBack(change_item_id)
	local consume_item = XianLingGuZhenWGData.Instance:GetConsumeItemID()
	if consume_item.item_id == nil then
		return
	end

	if consume_item.item_id == change_item_id then
		RemindManager.Instance:Fire(RemindName.XianLing_GuZhen_DRAW)
	end
end

function XianLingGuZhenWGData:OnSCNewLingXiMangTuAllInfo(protocol)
	-- self.role_draw_count = protocol.role_draw_count
	-- self.cur_reward_seq = protocol.cur_reward_seq
	-- self.server_total_draw_count = protocol.server_total_draw_count
	-- self.cur_reward_end_timestamp = protocol.cur_reward_end_timestamp
	-- self.special_count = protocol.count
	-- self.draw_number = protocol.draw_number
	-- self.once_time_draw_number = protocol.once_time_draw_number
	-- self.special_reward_count_list = protocol.special_reward_count_list
	-- self.special_reward_turn_list = protocol.special_reward_turn_list
	-- self.extra_add_special_timestamp = protocol.extra_add_special_timestamp
	-- self.req_role_count_list = protocol.req_role_count_list
	-- self.reward_pool_seq = protocol.reward_pool_seq

	local t_reward_pool_seq = protocol.reward_pool_seq
	self.reward_info_data[t_reward_pool_seq] = {
		role_draw_count = protocol.role_draw_count,
		cur_reward_seq = protocol.cur_reward_seq,
		server_total_draw_count = protocol.server_total_draw_count,
		cur_reward_end_timestamp = protocol.cur_reward_end_timestamp,
		special_reward_count_list = protocol.special_reward_count_list,
		extra_add_special_timestamp = protocol.extra_add_special_timestamp,
		reward_pool_seq = protocol.reward_pool_seq,
		draw_number = DeepCopy(protocol.draw_number),
		once_time_draw_number = DeepCopy(protocol.once_time_draw_number),
	}

	self:CheckGrade()
	-- self:CheckChangeCountList()
end

function XianLingGuZhenWGData:OnSCNewLingXiMangTuTaskInfo(protocol)
	self.xianling_task = protocol.task_list
	self.task_list_id = protocol.task_list_id
end

function XianLingGuZhenWGData:OnSCNewLingXiMangTuDrawRecord(protocol)
	self.record_count = protocol.count
	self.record_list = protocol.record_list
	self:CacularShowRecordList()
end

function XianLingGuZhenWGData:SetXianLingResultRewardDataList(protocol)
	if not protocol then
		return
	end

	local reward_item_list = {}
	for k, v in pairs(protocol.reward_item_list) do
		table.insert(reward_item_list, v)
	end
	self.xianling_resultreward_datalist = reward_item_list
end

function XianLingGuZhenWGData:SetNewLingXiMangTuServerDrawTimesInfo(protocol)
	self.draw_times = protocol.draw_times
	self.server_draw_times_reward_flag = bit:d2b_l2h(protocol.server_draw_times_reward_flag, nil, true)
end

function XianLingGuZhenWGData:SetNewLingxiMangTuDailyRewardInfo(protocol)
	self.daily_reward_flag = protocol.flag
end

function XianLingGuZhenWGData:GetRewardInfoByRewardPoorSeq(reward_pool_seq)
	return self.reward_info_data[reward_pool_seq] or {}
end

function XianLingGuZhenWGData:SaveLastBigReward(protocol)
	self.last_big_reward = protocol
end

function XianLingGuZhenWGData:GetLasBigReward()
	return self.last_big_reward or {}
end
----------------------------------------PROTOCOL_END------------------------------------

---------------------------------------CFG_GET_START------------------------------------
function XianLingGuZhenWGData:GetOtherCfg()
	return self.other_cfg
end

function XianLingGuZhenWGData:GetGrade()
	return self.cur_grade or 1
end

function XianLingGuZhenWGData:GetCurOpenDayCfg()
	local act_open_day = ActivityWGData.Instance:GetActivityOpenInServerDay(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN)
	local open_day_cfg = self.open_day_cfg
	for k,v in ipairs(open_day_cfg) do
		if act_open_day >= v.start_day and act_open_day <= v.end_day then
			return v
		end
	end
end

function XianLingGuZhenWGData:GetSpcialRewardCfg(reward_pool_seq, seq)
	local grade = self:GetGrade()
	local day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN)
	return (((self.special_reward_id_cfg[grade] or {})[day] or {})[reward_pool_seq] or {})[seq] or {}
end

function XianLingGuZhenWGData:GetCurSpcialRewardDataList()
	local grade = self:GetGrade()
	local day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN)
	return (self.special_reward_id_cfg[grade] or {})[day] or {}
end

function XianLingGuZhenWGData:GetConsumeItemID()
	return self.other_cfg.consume_item or {}, self.other_cfg.consume_gold or 300
end

function XianLingGuZhenWGData:GetCurNormalListData(cur_reward_seq)
	local grade = self:GetGrade()
	return (self.normol_reward_cfg[grade] or {})[cur_reward_seq] or {}
end

function XianLingGuZhenWGData:GetNolongerTipFlag()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local has_today_flag = PlayerPrefsUtil.GetInt("xianling_guzhen_flag" .. main_role_id) == open_day
	return has_today_flag
	--return self.no_loger_tip
end

function XianLingGuZhenWGData:SetNoLongerTipFlag(value)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local today_notips_open_day = value and open_day or 0
    PlayerPrefsUtil.SetInt("xianling_guzhen_flag" .. main_role_id, today_notips_open_day)
	--self.no_loger_tip = value
end

function XianLingGuZhenWGData:GetNoOHDJTipFlag()
	return self.no_ohdj_tip
end

function XianLingGuZhenWGData:SetNoOHDJTipFlag(value)
	self.no_ohdj_tip = value
end

function XianLingGuZhenWGData:GetNextTurnTime()
	return self.next_trun_wait_time
end

function XianLingGuZhenWGData:SetNextTurnTime(value)
	self.next_trun_wait_time = value
end

-- 0:所有号码 1:本次抽奖的号码
function XianLingGuZhenWGData:GetDrawNumberStr(flag, target_reward_pool_seq)
	local str = ""
	local index = 1
	local data = {}
	local pool_data = self.reward_info_data[target_reward_pool_seq]
	if flag == 0 then
		data = (pool_data or {}).draw_number or {}
	else
		data = (pool_data or {}).once_time_draw_number or {}
	end

	for k, v in ipairs(data) do
		if v >= 0 then
			v = v + 1
			if index ~= 1 then
				str = string.format(Language.XianLingGuZhen.NumberStr, str, v)
			else
				str = str .. v
				index = 0
			end
		else

		end
	end

	return str
end

function XianLingGuZhenWGData:GetMarkRewardPoolSeq()
	return self.target_reward_pool_seq or 1
end

function XianLingGuZhenWGData:SetMarkRewardPoolSeq(target_reward_pool_seq)
	self.target_reward_pool_seq = target_reward_pool_seq
end

----------------------------------------CFG_GET_END-------------------------------------

---------------------------------------CAL_START-------------------------------------
function XianLingGuZhenWGData:CacularShowRecordList()
	if IsEmptyTable(self.record_list) then
		return
	end

	self.show_record_info = {}
	local index = 1

	for t = 1, 5 do
		self.show_record_info[t] = {}
	end

	for i = #self.record_list , 0, -1 do
		if IsEmptyTable(self.show_record_info[index]) and index <= 5 then
			self.show_record_info[index] = self.record_list[i]
			index = index + 1
		end
	end
end


function XianLingGuZhenWGData:GetLingXiGetWayListData()
	-- if not self.show_way_list_cfg then
	-- 	return {}
	-- end
	-- local data_list = {}
	-- local num = 0
	-- for k,v in pairs(self.show_way_list_cfg) do
	-- 	num = num + 1
	-- 	data_list[num] = {}
	-- 	data_list[num].bt_type = tonumber(v.bt_type)
	-- 	data_list[num].name = v.name
	-- 	data_list[num].des = v.des
	-- 	data_list[num].open_param = v.open_param
	-- 	data_list[num].icon = v.icon
	-- 	data_list[num].des_picture = v.des_picture
	-- 	data_list[num].is_golden = v.is_golden
	-- 	if tonumber(v.bt_type) == 1 then
	-- 		data_list[num].is_finish = false
	-- 	elseif tonumber(v.bt_type) == 2 then --vip4判断
	-- 		-- local vip_level = GameVoManager.Instance:GetMainRoleVo().vip_level
	-- 		-- if vip_level >= 6 then
	-- 		-- 	data_list[num].is_finish = true
	-- 		-- else
	-- 		-- 	data_list[num].is_finish = false
	-- 		-- end
	-- 		data_list[num].is_finish = RechargeWGData.Instance:IsBuyVipZeroBuy() --0元购v6活动
	-- 	elseif tonumber(v.bt_type) == 3 then --3是否购买特权投资
	-- 		data_list[num].is_finish = RechargeWGData.Instance:IsBuyAllMaxGradeTouZiPlanCheckLevel()
	-- 	elseif tonumber(v.bt_type) == 4 then --监听是否购买月卡投资
	-- 		data_list[num].is_finish = not RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.MonthCard)
	-- 	elseif tonumber(v.bt_type) == 5 then --监听开服活动是否结束	
	-- 		local open_server_activity_status =	ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPEN_SERVER)
	-- 		data_list[num].is_finish = open_server_activity_status.status == ACTIVITY_STATUS.CLOSE
	-- 	elseif tonumber(v.bt_type) == 6 then --监听是否购买vip投资
	-- 		data_list[num].is_finish = not RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)

	-- 	elseif tonumber(v.bt_type) == 7 then -- 0元购
	-- 		local all_data  = LayoutZeroBuyWGData.Instance:GetZeroBuyData()
	-- 		data_list[num].is_finish = true
	-- 		for k,v in pairs(all_data) do
	-- 			if v.state == 1 or v.state == 2 then
	-- 				data_list[num].is_finish = false
	-- 				break
	-- 			end
	-- 		end
	-- 	end

	-- 	if data_list[num].is_finish then
	-- 		data_list[num].order = v.order + 10086
	-- 	else
	-- 		data_list[num].order = v.order
	-- 	end
		
	-- end
	-- table.sort(data_list, SortTools.KeyLowerSorter("order"))
	local open_list = {}
	local grade = self:GetGrade()
	if self.task_cfg[grade] then
		for k,v in pairs(self.task_cfg[grade]) do
			if v.task_type == 8 then
				if PierreDirectPurchaseWGData.Instance:CheckActIsOpen() then
					local t = Split(v.open_view,"#")
					if t[2] then
						local gift_is_open,reason = PierreDirectPurchaseWGData.Instance:GetBugTypeIsOpen(tonumber(t[2]))
						if gift_is_open then
							table.insert(open_list,v)
						end
					end
				else

				end
			else
				table.insert(open_list,v)
			end
		end
	end

	local task_data = open_list
	table.sort(task_data,function (a,b)
		return self:GetTaskSortIndex(a.task_id) > self:GetTaskSortIndex(b.task_id)
	end)
	return task_data or {}
	--return data_list
end

function XianLingGuZhenWGData:GetTaskSortIndex(task_id)
	local status = self:GetXianLingTaskStatus2(task_id)
	if status == 1 then
		return 10000 - task_id
	end

	if status == 0 then
		return 1000 - task_id
	end

	if status == 2 then
		return 100 - task_id
	end

	return 1000 - task_id
end

function XianLingGuZhenWGData:GetXianLingTaskStatus(task_id)
	if not self.xianling_task then
		return {}
	end
	for k,v in pairs(self.xianling_task) do
		if v.task_id == task_id then
			return v
		end
	end
end

function XianLingGuZhenWGData:GetXianLingTaskStatus2(task_id)
	if not self.task_list_id then
		return 0
	end
	return self.task_list_id[task_id] and self.task_list_id[task_id].reward_flag or 0
end

function XianLingGuZhenWGData:CheckGrade()
	local act_open_day = ActivityWGData.Instance:GetActivityOpenInServerDay(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN)
	local open_day_cfg = ConfigManager.Instance:GetAutoConfig("ra_lingxi_mangtu_auto").open_day
	for k,v in ipairs(open_day_cfg) do
		if act_open_day >= v.start_day and act_open_day <= v.end_day then
			self.cur_grade = v.grade
		end
	end
end

function XianLingGuZhenWGData:GetWorldRewardInfoData()
	local data_list = {}
	if self.record_list and self.record_count then
		for k,v in pairs(self.record_list) do
			data_list[self.record_count - k + 1] = v
		end
	end
	return data_list or {}, self.record_count or 0
end

function XianLingGuZhenWGData:GetCurBigRewardCount(reward_pool_seq)
	local info = self:GetRewardInfoByRewardPoorSeq(reward_pool_seq)
	
	if IsEmptyTable(info) then
		return 0, 0
	end

	local cfg = self:GetSpcialRewardCfg(reward_pool_seq, info.cur_reward_seq)
	if not cfg then
		return 0, 0
	end

	local limite_count = cfg.total_weight or 1
	local left_count = limite_count - info.server_total_draw_count
	left_count = left_count > 0 and left_count or 0
	return left_count, limite_count
end

function XianLingGuZhenWGData:GetCurBigLeftRewardCount(reward_pool_seq)
	local info = self:GetRewardInfoByRewardPoorSeq(reward_pool_seq)

	if IsEmptyTable(info) then
		return 0, 0
	end

	local cfg = self:GetSpcialRewardCfg(reward_pool_seq, info.cur_reward_seq)
	if not cfg then
		return 0,0
	end

	local limite_count = cfg.count_limit or 1
	local left_count = info.special_reward_count_list[info.cur_reward_seq + 1] or 0
	return left_count, limite_count
end

function XianLingGuZhenWGData:GetNextShowRecord()
	self.cur_show_index = self.cur_show_index + 1
	if self.cur_show_index > 5 then
		self.cur_show_index = 1
	end

	if IsEmptyTable(self.show_record_info[self.cur_show_index]) then
		self.cur_show_index = 1
	end
	return self.show_record_info[self.cur_show_index]
end

function XianLingGuZhenWGData:IsOpenLingXiChuanWen()
	local role_level = RoleWGData.Instance:GetRoleLevel() or -1
	local limite_level = self.other_cfg.str_level or 0
	return role_level >= limite_level
end

function XianLingGuZhenWGData:GetSpecialCfgByItemId(item_id)
	local grade = self:GetGrade()
	local day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN)
	local list = (self.special_reward_cfg[grade] or {})[day] or {}

	for k,v in pairs(list) do
		if v.reward_item and v.reward_item[0] and v.reward_item[0].item_id == item_id then
			return v
		end
	end

	return {}
end

function XianLingGuZhenWGData:GetCurSpecialCfgData()
	local grade = self:GetGrade()
	local day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN)
	local list = (self.special_reward_cfg[grade] or {})[day] or {}
	return list
end

function XianLingGuZhenWGData:GetXianLingResultRewardDataList()
	return self.xianling_resultreward_datalist or {}
end

function XianLingGuZhenWGData:GetGaiLvInfo()
	local grade = self:GetGrade()
	return self.item_random_desc[grade] or {}
end

function XianLingGuZhenWGData:GetCurEndTimeByPoolSeq(reward_pool_seq)
	return (self.reward_info_data[reward_pool_seq] or {}).cur_reward_end_timestamp or -1
end
----------------------------------------CAL_END--------------------------------------

---------------------------------------QMFL_START-------------------------------------
function XianLingGuZhenWGData:GetCurServerDrawTimeRewardCfg()
	local grade = self:GetGrade()
	return self.server_draw_times_reward_cfg[grade] or {}
end

function XianLingGuZhenWGData:GetNewLingXiMangTuServerDrawTimes()
	return self.draw_times
end

function XianLingGuZhenWGData:GetServerDrawTimesRewardFlag(seq)
	return self.server_draw_times_reward_flag[seq] or 0 -- 0:未领取 1:已领取
end

function XianLingGuZhenWGData:IsGetDailyReward()
	return self.daily_reward_flag == 1
end

function XianLingGuZhenWGData:IsCanGetQMFLByData(data)
	local draw_time = self:GetNewLingXiMangTuServerDrawTimes()
    local need_time = data.draw_times
    local time_enogth = draw_time >= need_time
    local get_flag = self:GetServerDrawTimesRewardFlag(data.seq)
    local is_get = get_flag == 1
	return not is_get and time_enogth, is_get
end
----------------------------------------QMFL_END--------------------------------------

---------------------------------------QMHB_START-------------------------------------
function XianLingGuZhenWGData:GetQMHBRedPaperDataList()
	local data_list = {}
	-- 世界红包
	local world_red_paper = WelfareWGData.Instance:GetWorldRedpaperAllInfo()
	if not IsEmptyTable(world_red_paper) then
		for k, v in pairs(world_red_paper) do
			if v.paper_type == 3 then
				local temp_list = TableCopy(v)
				temp_list.is_world_paper = true
				table.insert(data_list, temp_list)
			end
		end
	end

	--待发放红包
	local wait_data_list = self:GetQMHBWaitRedPaperDataList()
	if not IsEmptyTable(wait_data_list) then
		for k, v in pairs(wait_data_list) do
			table.insert(data_list, v)
		end
	end

	return data_list, world_red_paper, wait_data_list
end

function XianLingGuZhenWGData:GetQMHBWaitRedPaperDataList()
	local data_list = {}
	
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local wait_red_paper = person_info.redpaper_list

	if not IsEmptyTable(wait_red_paper) then
		for k, v in pairs(wait_red_paper) do
			if v.red_paper_type == 2 and v.paper_type == 3 then
				local temp_list = TableCopy(v)
				temp_list.is_wait_send = true
				table.insert(data_list, temp_list)
			end
		end
	end

	return data_list
end
----------------------------------------QMHB_END--------------------------------------

-- function XianLingGuZhenWGData:InitAllCfgByItemId()
-- 	-- self.all_cfg_by_item_id = {}
-- 	-- for _, day_cfg_list in pairs(self.lingxi_item_cfg) do
-- 	-- 	for _, day_cfg in pairs(day_cfg_list) do
-- 	-- 		if day_cfg and day_cfg.itemid and not self.all_cfg_by_item_id[day_cfg.itemid] then
-- 	-- 			self.all_cfg_by_item_id[day_cfg.itemid] = day_cfg
-- 	-- 		end
-- 	-- 	end
-- 	-- end
-- end

-- function XianLingGuZhenWGData:GetCfgByItemId(item_id)
-- 	--return self.all_cfg_by_item_id and self.all_cfg_by_item_id[item_id]
-- end




-- function XianLingGuZhenWGData:OnDayChange()
-- 	-- local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
-- 	-- local index = -1
-- 	-- for k,v in pairs(self.lingxi_item_cfg) do
-- 	-- 	if k <= open_day then
-- 	-- 		if index == -1 then
-- 	-- 			index = k
-- 	-- 		elseif index < k then
-- 	-- 			index = k
-- 	-- 		end
-- 	-- 	end
-- 	-- end
-- 	-- self.cur_all_item_data = self.lingxi_item_cfg[index] or {}
-- end

-- function XianLingGuZhenWGData:GetCurXianLingItemData()
-- 	-- if nil == self.cur_all_item_data then
-- 	-- 	self:OnDayChange()
-- 	-- 	return {}
-- 	-- end
-- 	--return self.cur_xianling_item_data or {}
-- end

-- function XianLingGuZhenWGData:GetOnceRollCost()
-- 	--return self.other_cfg.can_cost_value or 0, self.ra_xianling_zhen_val or 0
-- end

-- function XianLingGuZhenWGData:OnSCRAXianlingzhenInfo(protocol)
-- 	--self.ra_xianling_zhen_val = protocol.ra_xianling_zhen_val
-- end

-- -- function XianLingGuZhenWGData:OnSCRAXianlingzhenWorldRewardInfo(protocol)
-- -- 	self.world_reward_info_list = protocol.item_list
-- -- 	self.world_reward_info_num = protocol.count
-- -- end






-- function XianLingGuZhenWGData:GetRuleContant()
-- 	--return self.other_cfg.des or ""
-- 	return ""
-- end

-- function XianLingGuZhenWGData:OnSCRAXianlingzhenReplaceRecordInfo(protocol)
-- 	-- local data_list = protocol.replace_info
-- 	-- self.cur_xianling_item_data = {}
-- 	-- if nil == self.cur_all_item_data then
-- 	-- 	self:OnDayChange()
-- 	-- end

-- 	-- for k,v in pairs(self.cur_all_item_data) do
-- 	-- 	for t,q in pairs(data_list) do
-- 	-- 		if v.itemid == q.item_id or v.replace_item_0 == q.item_id  then
-- 	-- 			local index = q.replace_grade-1
-- 	-- 			if index >= 0 then
-- 	-- 				self.cur_xianling_item_data[v.order] = {}
-- 	-- 				self.cur_xianling_item_data[v.order].itemid = v["replace_item_"..index]
-- 	-- 			end
-- 	-- 		end
-- 	-- 	end
-- 	-- 	if nil == self.cur_xianling_item_data[v.order] then
-- 	-- 		self.cur_xianling_item_data[v.order] = v
-- 	-- 	end
-- 	-- end
-- end


-- function XianLingGuZhenWGData:SaveWaitChangeFlag(value)
-- 	self.need_wait = value
-- end

-- function XianLingGuZhenWGData:GetWaitChangeFlag()
-- 	return self.need_wait or false
-- end

-- function XianLingGuZhenWGData:GetShowModelCfgByItemid(item_id)
-- 	-- if IsEmptyTable(self.show_model_list_by_itemid) then
-- 	-- 	self:InitAllShowModelCfg()
-- 	-- end

-- 	-- return self.show_model_list_by_itemid and self.show_model_list_by_itemid[item_id]
-- end

-- function XianLingGuZhenWGData:InitAllShowModelCfg()
-- 	-- self.show_model_list_by_itemid = {}
-- 	-- if self.other_cfg.mod_show and self.other_cfg.mod_show ~= "" then
-- 	-- 	local all_item_list = Split(self.other_cfg.mod_show, ",")
-- 	-- 	local temp_id
-- 	-- 	for k, item_id in pairs(all_item_list) do
-- 	-- 		temp_id = tonumber(item_id)
-- 	-- 		if temp_id then
-- 	-- 			self.show_model_list_by_itemid[temp_id] = temp_id
-- 	-- 		end
-- 	-- 	end
-- 	-- end
-- end

-- function XianLingGuZhenWGData:GetDefShowModelCfg()
-- 	-- if IsEmptyTable(self.show_model_list_by_itemid) then
-- 	-- 	self:InitAllShowModelCfg()
-- 	-- end

-- 	-- if IsEmptyTable(self.def_show_model_list) then
-- 	-- 	local temp_t = {}
-- 	-- 	for k, v in pairs(self.show_model_list_by_itemid) do
-- 	-- 		table.insert(temp_t, v)
-- 	-- 	end
-- 	-- 	self.def_show_model_list = temp_t
-- 	-- end

-- 	-- return self.def_show_model_list
-- end

-- function XianLingGuZhenWGData:GetModelCfgByItemId(item_id)
-- 	return self.re_model_show and self.re_model_show[item_id]
-- end

-- function XianLingGuZhenWGData:GetModelResByItemId(item_id)
-- 	local cfg = self:GetModelCfgByItemId(item_id)
-- 	local model_res, model_type
-- 	if cfg then
-- 		model_res = cfg.model_res
-- 		model_type = cfg.model_type
-- 	end
-- 	return model_res or 0, model_type or 0
-- end






-- function XianLingGuZhenWGData:GetSpecialSortIndex(seq)

-- end

-- function XianLingGuZhenWGData:GetPersonalBuyTime()
-- 	return self.role_draw_count or 0
-- end


-- function XianLingGuZhenWGData:GetCurNormalItemListData()
-- 	local grade = self:GetGrade()
-- 	local item_list = {}
-- 	for k, v in pairs((self.normol_reward_cfg[grade] or {})[self.cur_reward_seq] or {}) do
-- 		table.insert(item_list, v.reward_item[0])
-- 	end
-- 	return item_list
-- end


-- function XianLingGuZhenWGData:GetExtraAddTime()
-- 	return self.extra_add_special_timestamp or -1
-- end


-- function XianLingGuZhenWGData:GetJumpAnimFlag()
-- 	return self.jump_anim
-- end

-- function XianLingGuZhenWGData:SetJumpAnimFlag(value)
-- 	self.jump_anim = value
-- end

-- function XianLingGuZhenWGData:GetBigRewardHadBeenAdd(seq)
-- 	seq = seq + 1
-- 	if self.special_reward_turn_list and self.special_reward_turn_list[seq] then
-- 		local turn = self.special_reward_turn_list[seq]
-- 		local uuid_str = RoleWGData.Instance:GetUUIDStr()
-- 		if PlayerPrefsUtil.HasKey(uuid_str.."LingXi"..seq) then
-- 			local save_turn = PlayerPrefsUtil.GetString(uuid_str.."LingXi"..seq)
-- 			return tonumber(save_turn) == turn
-- 		end
-- 		return false
-- 	end
-- 	return false
-- end

-- function XianLingGuZhenWGData:SetBigRewardBeenAdd(seq)
-- 	seq = seq + 1
-- 	if self.special_reward_turn_list and self.special_reward_turn_list[seq] then
-- 		local turn = self.special_reward_turn_list[seq]
-- 		local uuid_str = RoleWGData.Instance:GetUUIDStr()
-- 		PlayerPrefsUtil.SetString(uuid_str.."LingXi"..seq, turn)
-- 		PlayerPrefsUtil.Save()

-- 		if not self.login_click_add_seq then
-- 			self.login_click_add_seq = {}
-- 		end

-- 		if not self.login_click_add_seq[seq] then
-- 			self.login_click_add_seq[seq] = {}
-- 			self.login_click_add_seq[seq].turn = turn
-- 			self.login_click_add_seq[seq].status = 1
-- 		else
-- 			if self.login_click_add_seq[seq].turn ~= turn then
-- 				self.login_click_add_seq[seq].turn = turn
-- 				self.login_click_add_seq[seq].status = 1
-- 			end
-- 		end
-- 	end
-- end



-- function XianLingGuZhenWGData:GetCurRequirePeople(seq)
-- 	seq = seq + 1
-- 	if self.req_role_count_list and self.req_role_count_list[seq] then
-- 		return self.req_role_count_list[seq]
-- 	end
-- 	return 0
-- end

-- function XianLingGuZhenWGData:GetCurBigRewardIndex()
-- 	return self.cur_reward_seq or -1
-- end



-- -- function XianLingGuZhenWGData:GetSpecialRewardListData()
-- -- 	local data_list = {}
-- -- 	local index = 0
-- -- 	local grade = self:GetGrade()
-- -- 	if not self.cur_reward_seq then 
-- -- 		return self.special_reward_cfg[grade]
-- -- 	end

-- -- 	local list = self.special_reward_cfg[grade] or {}
-- -- 	local max_num = #list
-- -- 	for k,v in pairs(list) do
-- -- 		if v.seq ~= self.cur_reward_seq then
-- -- 			index = v.seq - self.cur_reward_seq
-- -- 			if index > 0 then
-- -- 				data_list[index] = v
-- -- 			else
-- -- 				data_list[max_num + index] = v
-- -- 			end
-- -- 		end
-- -- 	end

-- -- 	return data_list
-- -- end

-- -- function XianLingGuZhenWGData:ShowClickAddTip(seq)
-- -- 	local cfg = self:GetSpcialRewardCfg(seq - 1)
-- -- 	if cfg then
-- -- 		local reward_id = cfg.reward_item[0] and cfg.reward_item[0].item_id or 0
-- -- 		if reward_id == 0 then return end
-- -- 		local item_cfg = ItemWGData.Instance:GetItemConfig(reward_id)

-- -- 		if not item_cfg or IsEmptyTable(item_cfg) then
-- -- 			return
-- -- 		end

-- -- 		local cur_require_people = self:GetCurRequirePeople(seq -1)
-- -- 		if cur_require_people >= cfg.refresh_need_role_count then
-- -- 			return
-- -- 		end

-- -- 		local color = cur_require_people < cfg.refresh_need_role_count and COLOR3B.RED or COLOR3B.GREEN
-- -- 		local str = string.format(Language.XianLingGuZhen.AddRequireSucc,ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color]),ToColorStr(cur_require_people,color),cfg.refresh_need_role_count)
-- -- 		XianLingGuZhenWGCtrl.Instance:ShowClickAddTip(str)
-- -- 	end
-- -- end

-- -- function XianLingGuZhenWGData:GetSpecialRewardCfg()
-- -- 	local cfg = self:GetSpcialRewardCfg(self.cur_reward_seq)
-- -- 	return cfg or {}
-- -- end

-- -- function XianLingGuZhenWGData:GetCurBigLeftRewardCountBySeq(seq)
-- -- 	local cfg = self:GetSpcialRewardCfg(seq)
-- -- 	if not self.special_reward_count_list or not cfg then
-- -- 		return 0,0
-- -- 	end

-- -- 	local limit_count = cfg[seq].count_limit or 1

-- -- 	local left_count = self.special_reward_count_list[seq + 1]
-- -- 	return left_count,limit_count
-- -- end

-- -- function XianLingGuZhenWGData:CheckChangeCountList()
-- -- 	if not self.old_req_role_count_list then
-- -- 		self.old_req_role_count_list = {}
-- -- 	end

-- -- 	for k,v in pairs(self.req_role_count_list) do
-- -- 		if self.old_req_role_count_list and self.old_req_role_count_list[k] and self.old_req_role_count_list[k] ~= v and v ~= 0 then
-- -- 			local turn = self.special_reward_turn_list[k]
-- -- 			if self.login_click_add_seq and self.login_click_add_seq[k] and self.login_click_add_seq[k].turn == turn and self.login_click_add_seq[k].status == 1 then
-- -- 				self:ShowClickAddTip(k)
-- -- 				self.login_click_add_seq[k].status = 2
-- -- 			end
-- -- 			self.old_req_role_count_list[k] = v
-- -- 		else
-- -- 			self.old_req_role_count_list[k] = v
-- -- 		end
-- -- 	end
-- -- end