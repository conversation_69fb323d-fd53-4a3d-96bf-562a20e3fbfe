NewFightMountLwsjView = NewFightMountLwsjView or BaseClass(SafeBaseView)

function NewFightMountLwsjView:__init()
    self.view_style = ViewStyle.Full
    self.view_layer = UiLayer.Normal
    self.is_safe_area_adapter = true

    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/new_fight_mount_ui_prefab", "layout_lwsj_view")
end

function NewFightMountLwsjView:__delete()
end

function NewFightMountLwsjView:ReleaseCallBack()
    if self.lwsj_list_view then
        self.lwsj_list_view:DeleteMe()
        self.lwsj_list_view = nil
    end
end

function NewFightMountLwsjView:OpenCallBack()

end

function NewFightMountLwsjView:LoadCallBack()
    if not self.lwsj_list_view then
        self.lwsj_list_view = AsyncListView.New(NewFightMountLwsjRender, self.node_list["list_view"])
    end
end

function NewFightMountLwsjView:ShowIndexCallBack()

end

function NewFightMountLwsjView:OnFlush()
    local all_mount_cfg = NewFightMountWGData.Instance:GetAllFightMountTypeCfg()
    if not IsEmptyTable(all_mount_cfg) then
        self.lwsj_list_view:SetDataList(all_mount_cfg)
    end
end

----------------NewFightMountLwsjRender-------------------
NewFightMountLwsjRender = NewFightMountLwsjRender or BaseClass(BaseRender)
function NewFightMountLwsjRender:__init()

end

function NewFightMountLwsjRender:__delete()

end

function NewFightMountLwsjRender:OnFlush()
    if not self.data then
        return
    end

    local data = self.data

    local bundle, asset = ResPath.GetNewFightMountImg("a2_zdzq_icon_" .. data.mount_seq)
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)

    self.node_list.name.text.text = data.lw_name
    self.node_list.lw_desc.text.text = data.lw_desc

    local mount_skill_level = NewFightMountWGData.Instance:GetMountSkillLevelBySeq(data.mount_seq)
    local skill_level_cfg = NewFightMountWGData.Instance:GetUpSkillLevelCfg(data.mount_seq, mount_skill_level)
    self.node_list.lw_value.text.text = skill_level_cfg and skill_level_cfg.lw_value or 0
end
