local VECTOR2_POS = Vector2(570,68)
local BOY = 0 		--男孩
local GIRL = 1 		--女孩

function MarryView:InitBabyAttrView()
		--仙娃最大阶数
	self.baby_max_grade = MarryWGData.Instance:GetBabyMaxGrade() or 0
	-- if not self.baby_list_view then
		self.active_cell = ItemCell.New(self.node_list["ph_active_cell"]) 	--激活后显示的格子
		self.active_cell:SetNeedItemGetWay(true)
		self.active_cell:SetHideRightDownBgLessNum(-1)
		self.not_active_cell = ItemCell.New(self.node_list["ph_not_active_cell"])	--激活前显示的格子

		-- 模型展示
		if (not self.baby_display) then
			self.baby_display = RoleModel.New()
			self.baby_display:SetUISceneModel(self.node_list["baby_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
			self:AddUiRoleModel(self.baby_display, TabIndex.marry_baby)
		end

		-- self.baby_display = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["baby_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.M,
		-- 	can_drag = true,
		-- }
		
		-- self.baby_display:SetRenderTexUI3DModel(display_data)
		-- -- self.baby_display:SetUI3DModel(self.node_list["baby_display"].transform, self.node_list["baby_display"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		-- self:AddUiRoleModel(self.baby_display)

		-- self.baby_list_view = AsyncListView.New(BabyListItemRender, self.node_list["ph_baby_list"])
		-- self.baby_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectBabyListCallBack, self))
		self.baby_skill_view = AsyncListView.New(BabySkillItemRender, self.node_list["ph_skill_list"])
		self.baby_skill_view:SetSelectCallBack(BindTool.Bind1(self.SelectSkillCallBack, self))
        self.baby_skill_view:SetDefaultSelectIndex(nil)
		XUI.AddClickEventListener(self.node_list["btn_baby_huanhua"], BindTool.Bind1(self.BabyHuanHuaHandler, self))
		-- XUI.AddClickEventListener(self.node_list["btn_baby_boylist"], BindTool.Bind(self.OnClickInterfaceBaby, self, BOY))
		-- XUI.AddClickEventListener(self.node_list["btn_baby_girllist"], BindTool.Bind(self.OnClickInterfaceBaby, self, GIRL))
		XUI.AddClickEventListener(self.node_list["btn_babay_tips"], BindTool.Bind1(self.OnClickBabyTips, self))
		XUI.AddClickEventListener(self.node_list["btn_baby_levelup"], BindTool.Bind1(self.OnClickBabyLevelUp, self))
		-- XUI.AddClickEventListener(self.node_list["layout_btn_skill"], BindTool.Bind1(self.OnClickSkillVisble, self))

	--end
	self.baby_type = BOY	       --  BOY、男  GIRL、女
	self.baby_id = -1
	self.active_need_baby = 0
	self.cur_baby_select = 1
    -- self:FlushBabySkillList()
    self.is_first_enter = nil
	self.baby_itemcell_list = {}
	self.load_bar_complete = false
	self.is_baby_active = nil
	self.load_bar_complete_callback = nil
	self:LoadBabyNavBar()
end

----------------------------------左侧导航栏start-------------------------------------------
function MarryView:LoadBabyNavBar()
	for i = 1, 2 do
			self.node_list["SelectBtn" .. i]:SetActive(true)
			self.node_list["text_"..i].text.text = Language.Marry.BabyOperationType[i] or ""
			self.node_list["texthl_" .. i].text.text = Language.Marry.BabyOperationType[i] or ""
			self.node_list["SelectBtn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickBabyBarHandler, self, i))
			self:LoadNavBarItemCell(i)
	end
end

function MarryView:OnClickBabyBarHandler(index, isOn)
	if index == nil or not isOn then
		return
	end
	if self.baby_type == index - 1 then
		return
	end

	self:ClearBaoBaoData()
	self:StopBabyGradeQuest()
	self.baby_type = index - 1
    if not self.is_can_select then
        local index = MarryWGData.Instance:GetDefaultIndexByBabyType(self.baby_type)
		self.cur_baby_select = index ~= 0 and index or 1
	end
	self:FlushNavBarSelect(self.baby_type,self.cur_baby_select)
end

function MarryView:LoadNavBarItemCell(index)
	local res_async_loader = AllocResAsyncLoader(self, "marry_baby_nav_item" .. index)
	local data_list = MarryWGData.Instance:GetForViewBabyNewListCfg(index - 1)
	local item_count = #data_list
	res_async_loader:Load("uis/view/marry_ui_prefab", "marry_baby_itembar", nil,
		function(new_obj)
			local item_vo = {}
			for i=1, item_count do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.node_list["List"..index].transform, false)
				obj:GetComponent("Toggle").group = self.node_list["List"..index].toggle_group
				local item_render = BabyBarItemCellRender.New(obj)
				item_render.parent_view = self
				item_render:SetIndex(i)
				item_vo[i] = item_render
				if i == item_count then
					self.load_bar_cell_complete = true
				end
				obj:SetActive(true)
			end

			self.baby_itemcell_list[index] = item_vo
			if self.load_bar_cell_complete then
				self:ShowFlushNavBarItemCell(index, data_list)
			end
		end)
end

function MarryView:ShowFlushNavBarItemCell(index, data_list)
	if not self.load_bar_cell_complete then
		return
	end
	local bar_cell_list = self.baby_itemcell_list[index]
	for k, v in pairs(bar_cell_list) do
		if nil == data_list[k] then
			break
		end
		v:SetData(data_list[k])
	end

	if index == 2 then 
		self.load_bar_complete = true
		if nil ~= self.load_bar_complete_callback then
			self.load_bar_complete_callback()
			self.load_bar_complete_callback = nil 
		end
	end
end

function MarryView:FlushNavBarItemCellData(data_list)
	local navbar_render_list = self.baby_itemcell_list[self.baby_type + 1]
	local baby_show_id = -1
	if IsEmptyTable(navbar_render_list) then
		return
	end
	for k, v in pairs(navbar_render_list) do
		if nil == data_list[k] then
			break
		end
		if baby_show_id == -1 and data_list[k].id == self.baby_id then
			baby_show_id = k
		end
		v:SetData(data_list[k])
	end
	if self.baby_type and self.baby_id then
		self:FlushNavBarSelect(self.baby_type,baby_show_id)
	end

end

function MarryView:FlushNavBarSelect(baby_type, select_id)
	local open_navbar = function ()
		for i = 1, 2 do
			self.node_list["SelectBtn"..i].accordion_element.isOn = i - 1 == baby_type
		end

		local child_item_list = self.baby_itemcell_list[baby_type + 1]
		for i = 1, #child_item_list do
			child_item_list[i]:OnBabyItemSelectChange(i == select_id)
		end
	end
	if 	self.load_bar_complete then
		open_navbar()
	else
		self.load_bar_complete_callback = open_navbar
	end
end
----------------------------------左侧导航栏end-------------------------------------------

function MarryView:DeleteBabyAttrView()
	if nil ~= self.active_cell then
		self.active_cell:DeleteMe()
		self.active_cell = nil
	end

	if nil ~= self.not_active_cell then
		self.not_active_cell:DeleteMe()
		self.not_active_cell = nil
	end

	-- if nil ~= self.baby_list_view then
	-- 	self.baby_list_view:DeleteMe()
	-- 	self.baby_list_view = nil
	-- end

	if nil ~= self.baby_skill_view then
		self.baby_skill_view:DeleteMe()
		self.baby_skill_view = nil
	end

    if self.jinjie_delay then
        GlobalTimerQuest:CancelQuest(self.jinjie_delay)
        self.jinjie_delay = nil
    end
	self.baobao_btn_str = ""
	self:DoKillBaoBaoAni()
	self.baby_id = -1
	self.active_need_baby = 0
	self.old_baby_type = 0
	self.baby_type = 0
	self.cur_baby_select = 1
    self.cur_item_id = nil
	self.is_first_enter = nil
    self.is_can_select = false
	if self.baby_display then
		self.baby_display:DeleteMe()
		self.baby_display = nil
	end

	if self.baby_itemcell_list then
		for k,v in pairs(self.baby_itemcell_list) do
			for k1,v1 in pairs(v) do
				v1:DeleteMe()
			end
			self.baby_itemcell_list[k] = nil
		end
		self.baby_itemcell_list = nil
	end

	self.load_bar_complete_callback = nil
	self.load_bar_complete = false
	self.is_baby_active = nil
end

function MarryView:BabyCloseCallBack()
    self.is_first_enter = nil
    self.is_can_select = false
end

function MarryView:DoKillBaoBaoAni()
	self.not_kill_baobao_ani = false
	DG.Tweening.DOTween.Kill('baobao_slider_tween')
end

function MarryView:FlushBabyBagBiew( change_item_id, change_item_index, change_reason, put_reason, old_num, new_num )
	if self:IsOpen() and self:IsLoadedIndex(TabIndex.marry_baby) and old_num and new_num and new_num > old_num then
		self:FlushBaoBaoSlider(true)
		self:FlushBabyList(true)
	end
end

function MarryView:FlushBabyView(is_from_protocol)
	--刷新宝宝列表
	self:FlushBabyList(is_from_protocol)

	self:FlushBaoBaoSlider()

	if self.baby_type and self.cur_baby_select then
		self:FlushNavBarSelect(self.baby_type,self.cur_baby_select)
	end
end

--点击切换按钮：从男宝宝切换到女宝宝
function MarryView:OnClickInterfaceBaby(baby_type)
	if self.baby_type == baby_type then
		return
	end

	self:ClearBaoBaoData()
	self:StopBabyGradeQuest()
	self.baby_type = baby_type
    if not self.is_can_select then
        local index = MarryWGData.Instance:GetDefaultIndexByBabyType(self.baby_type)
		self.cur_baby_select = index ~= 0 and index or 1
	end

    self:FlushBabyList()
end

--刷新宝宝列表
function MarryView:FlushBabyList(is_from_protocol)
	if self:GetBabyAutoUp() then
		return
	end
	self:UpdateBabyList(is_from_protocol)
end

function MarryView:SetWantSelectIndex(is_can_select, id, baby_type)
    self.is_can_select = is_can_select
    self.baby_type = baby_type
    -- if baby_type == BOY then
	-- 	self.node_list["choose_boy_img"]:SetActive(true)
	-- 	self.node_list["choose_girl_img"]:SetActive(false)
	-- else
	-- 	self.node_list["choose_girl_img"]:SetActive(true)
	-- 	self.node_list["choose_boy_img"]:SetActive(false)
	-- end
    local select_index = MarryWGData.Instance:GetSelectIndexByRealId(self.baby_type, id)
	self.cur_baby_select = select_index
	--多次导航跳转
	self:FlushNavBarSelect(self.baby_type, self.cur_baby_select)
end

function MarryView:GetDefaultTypeAndIndex()
    local index_boy = MarryWGData.Instance:GetDefaultIndexByBabyType(BOY)
    local index_girl = MarryWGData.Instance:GetDefaultIndexByBabyType(GIRL)
    local baby_type = BOY
    local cur_baby_select = 1
    if index_boy ~= 0 then
        cur_baby_select = index_boy
    elseif index_girl ~= 0 then
        cur_baby_select = index_girl
        baby_type = GIRL
    end
    return baby_type, cur_baby_select
end

function MarryView:UpdateBabyList(is_from_protocol)
	-- if self.baby_list_view then
        local baby_list_cfg = MarryWGData.Instance:GetForViewBabyNewListCfg(self.baby_type)
		if self.is_can_select then
			-- self.baby_list_view:SetDefaultSelectIndex(self.cur_baby_select)
            -- self.baby_list_view:SetDataList(baby_list_cfg)
			-- if self.cur_baby_select then
			-- 	local way = self.cur_baby_select > 5 and 1 or 0
            -- 	self.baby_list_view:JumptToPrecent(way)
			-- end

			self.is_can_select = false
            self.is_first_enter = 1

			self:FlushNavBarItemCellData(baby_list_cfg)
            return
        end
        if self.is_first_enter == nil then
            local baby_type = BOY
            baby_type, self.cur_baby_select = self:GetDefaultTypeAndIndex()
            if baby_type ~= self.baby_type then
                self:OnClickInterfaceBaby(baby_type)
            end
            baby_list_cfg = MarryWGData.Instance:GetForViewBabyNewListCfg(self.baby_type)
            self.is_first_enter = 1
        end
        if nil ~= self.cur_baby_select and self.cur_item_id then
            if is_from_protocol then
                self.cur_baby_select = MarryWGData.Instance:GetSelectIndexByItemId(self.baby_type, self.cur_item_id)
            end
		end
        -- self.baby_list_view:SetDefaultSelectIndex(self.cur_baby_select)
        -- self.baby_list_view:SetDataList(baby_list_cfg, 0)
		-- if self.cur_baby_select then
        -- 	local way = self.cur_baby_select > 5 and 1 or 0
        -- 	self.baby_list_view:JumptToPrecent(way)
		-- end
		self:FlushNavBarItemCellData(baby_list_cfg)
	--end
	-- local bundle, asset = ResPath.GetMarryResPath2(self.baby_type == BOY and "word_baby1" or "word_baby2")--望子成龙	望女成凤
	-- self.node_list["img_baby_flag"].image:LoadSprite(bundle, asset, function ()
	-- 												self.node_list["img_baby_flag"].image:SetNativeSize()
	-- 											end)
end

--刷新模型
function MarryView:FlushBabyDisPlay()
    if self.baby_display then
        local cfg = MarryWGData.Instance:GetBabyListCfgByID(self.baby_id, self.baby_type)
        if cfg and cfg.appe_image_id then
            local bundle, asset = ResPath.GetHaiZiModel(cfg.appe_image_id)
            if bundle == nil and asset == nil then
                print_error("can not find the baby asset, appe_image_id ==",cfg.appe_image_id)
                return
            end

            self.baby_display:SetMainAsset(bundle, asset,function()
                self.baby_display:PlaySoulAction()
            end)
			self.baby_display:SetUSAdjustmentNodeLocalPosition(-0.8, 0, 0)
        else
            print_error("can not find the baby cfg, baby_type ==",self.baby_type,"baby_id ==",self.baby_id)
            return
        end

	end
end

--刷新技能列表信息
function MarryView:FlushBabySkillList()
	local baby_skill_cfg = MarryWGData.Instance:GetBabySkillCfg(self.baby_type, self.baby_id)
	if self.baby_skill_view and not IsEmptyTable(baby_skill_cfg) then
		self.node_list["layout_baby_skill"]:SetActive(true)
		self.baby_skill_view:SetDataList(baby_skill_cfg, 0)
	else
		self.node_list["layout_baby_skill"]:SetActive(false)
	end
end

function MarryView:FlushBaoBaoSlider(is_from_click)

	local marry_data = MarryWGData.Instance
	local baby_list_info = marry_data:GetBabyListInfoByType(self.baby_type, self.baby_id)
	if nil == baby_list_info then
		return
	end

	if self.node_list.baby_state then
		self.node_list.baby_state.text.text = self.is_baby_active == 1 and string.format(Language.Tip.Order, baby_list_info and baby_list_info.grade or 0) or Language.Marry.XianWaNotActive
	end

	local old_level = self:BaoBaoOldGrade()
	local old_progress = self:BaoBaoOldProgress()
	local cur_level = baby_list_info.grade
	local cur_progress = baby_list_info.bless_value
	local level_cfg = marry_data:GetBabyAttrCfg(self.baby_type, self.baby_id, cur_level)
	local is_max_level = not (marry_data:GetBabyAttrCfg(self.baby_type, self.baby_id, cur_level + 1).gongji > -1)
	if (old_level == -1 and old_progress == -1) or (old_level == cur_level and old_progress == cur_progress) or (old_level == -1 and cur_level == 0) then
		self:FlushBabyRightAttrContent()
		if (old_level == -1) or old_level == 0 and cur_level == 1 then
			self:FlushBabyDisPlay()
		end
	else
		--属性发生变化
		self:CanPlayBabyEffect(true)
		local level_add = cur_level - old_level
		local add_exp_num = 0
		for i = 1, level_add do
			add_exp_num = add_exp_num + marry_data:GetBabyAttrCfg(self.baby_type, self.baby_id, old_level + i).bless_value
		end
		add_exp_num = add_exp_num - old_progress + cur_progress
		self:PlayBaoBaoAni(level_cfg and level_cfg.bless_value or 1, is_max_level, level_add, old_level, cur_progress, add_exp_num, is_from_click)
	end
	self:BaoBaoOldGrade(cur_level)
    self:BaoBaoOldProgress(cur_progress)

end

--当前等级属性
function MarryView:SetBaoBaoCurAttrValue(baby_attr_cfg, baby_skill_attr)
	self.node_list["lbl_gongji_value"].text.text = baby_attr_cfg.gongji + baby_skill_attr.gongji
	self.node_list["lbl_hp_value"].text.text = baby_attr_cfg.maxhp + baby_skill_attr.maxhp
	self.node_list["lbl_pojia_value"].text.text = baby_attr_cfg.pojia + baby_skill_attr.pojia
    self.node_list["lbl_fangyu_value"].text.text = baby_attr_cfg.fangyu + baby_skill_attr.fangyu
    local value = baby_attr_cfg.shengming_jc_per + baby_skill_attr.shengming_jc_per
	self.node_list["lbl_hpjc_value"].text.text = value/ 100 .."%"
end

function MarryView:SetShengmingJcActive(cur_active, next_active)
    self.node_list["lbl_hpjc_value"]:SetActive(cur_active)
    self.node_list["lbl_shengming_txt"]:SetActive(cur_active)
    self.node_list["lbl_hpjc_add"]:SetActive(next_active)
    self.node_list["lbl_shengming_add_img"]:SetActive(next_active)
end

--下一级属性
function MarryView:SetBaoBaoNextAttrValue( not_max_grade, baby_next_attr_cfg, baby_next_skill_attr, baby_attr_cfg, baby_skill_attr )
	-- self.node_list["img_jihuo_flag"]:SetActive(not not_max_grade)	--已满级图标
	if not_max_grade then
		self.node_list["lbl_gongji_add"].text.text = (baby_next_attr_cfg.gongji + baby_next_skill_attr.gongji) - (baby_attr_cfg.gongji + baby_skill_attr.gongji)
		self.node_list["lbl_hp_add"].text.text = (baby_next_attr_cfg.maxhp + baby_next_skill_attr.maxhp) - (baby_attr_cfg.maxhp + baby_skill_attr.maxhp)
		self.node_list["lbl_pojia_add"].text.text = (baby_next_attr_cfg.pojia + baby_next_skill_attr.pojia) - (baby_attr_cfg.pojia + baby_skill_attr.pojia)
		self.node_list["lbl_fangyu_add"].text.text = (baby_next_attr_cfg.fangyu + baby_next_skill_attr.fangyu) - (baby_attr_cfg.fangyu + baby_skill_attr.fangyu)
        local cur_value = (baby_attr_cfg.shengming_jc_per + baby_skill_attr.shengming_jc_per)
        local value = (baby_next_attr_cfg.shengming_jc_per + baby_next_skill_attr.shengming_jc_per) - cur_value
        self.node_list["lbl_hpjc_add"].text.text = value/ 100 .."%"
        self:SetShengmingJcActive(cur_value ~= 0 or value ~= 0, value ~= 0)
    end
end

--刷新属性
function MarryView:FlushBabyRightAttrContent(grade)
	self:FlushBabyRightCellContent()
	self:FlushBabySkillList()

	local baby_list_info = MarryWGData.Instance:GetBabyListInfoByType(self.baby_type, self.baby_id)
	if not baby_list_info then
		return
	end

	grade = grade or baby_list_info.grade
	-- local str = ""
	-- if grade == 0 then
	-- 	str = Language.ChinaNub.hzNum[1]
	-- else
	-- 	str = NumberToChinaNumber(grade)
	-- end
	-- self.node_list["grade_show"]:SetActive(grade >= 0)
	-- self.node_list["baby_grade_num"].text.text = string.format(Language.Tip.Order, str)

	local baby_attr_cfg = MarryWGData.Instance:GetBabyAttrCfg(self.baby_type, self.baby_id, grade)
	if nil == baby_attr_cfg or baby_attr_cfg.gongji <= -1 then --读取基本属性出错!!
		return
	end
	local baby_skill_attr = MarryWGData.Instance:GetBabySkillAttrList(self.baby_type, self.baby_id, grade)
	self:SetBaoBaoCurAttrValue(baby_attr_cfg, baby_skill_attr)

	if nil ~= self.baby_type_cache and nil ~= self.baby_id_cache and nil ~= self.baby_grade_cache then
		if (self.baby_type_cache == self.baby_type) and (self.baby_id_cache == self.baby_id) and (grade > self.baby_grade_cache) then
			self:PlayBabyAttrInfoUpEffect()
		end
	end

	self.baby_type_cache = self.baby_type
	self.baby_id_cache = self.baby_id
	self.baby_grade_cache = grade

	local cfg = AttributeMgr.GetAttributteByClass(baby_attr_cfg)
	local skill_cfg = AttributeMgr.GetAttributteByClass(baby_skill_attr)
	local power_value = math.floor(AttributeMgr.GetCapability(cfg)) + math.floor(AttributeMgr.GetCapability(skill_cfg))       --与服务端算法一样
	self.node_list["babyzhandouli"].text.text = power_value

    if baby_attr_cfg.bless_value and  baby_attr_cfg.bless_value ~=0 then
        local persent = baby_list_info.bless_value / baby_attr_cfg.bless_value
        self:SetBabyProgressVal(persent)
    else
        self:SetBabyProgressVal(0)
    end

	local baby_next_attr_cfg = MarryWGData.Instance:GetBabyAttrCfg(self.baby_type, self.baby_id, grade + 1)
	local baby_next_skill_attr = MarryWGData.Instance:GetBabySkillAttrList(self.baby_type, self.baby_id, grade + 1)
	local not_max_grade = baby_next_attr_cfg.gongji > -1
	self:SetBaoBaoNextAttrValue(not_max_grade, baby_next_attr_cfg, baby_next_skill_attr, baby_attr_cfg, baby_skill_attr)

	if not_max_grade then
		local baby_flag = MarryWGData.Instance:GetBabyListFlag(self.baby_type)
        local baby_active = baby_flag[32 - self.baby_id] == 1

		if baby_active then
			self:SetBaoBaoBtnStr(self:IsBaoBaoUpgrade() and Language.Marry.Stop or Language.Marry.QualityUp)
			self:ChangeBaByUpgradeBtnState(true)
		else
			self:SetBaoBaoBtnStr(Language.Common.Activate)
        end
        self.node_list["layout_next_attr"]:SetActive(true)
		self.node_list["lbl_baby_exp"].text.text = (baby_list_info.bless_value .. "/" .. baby_attr_cfg.bless_value)
	else    -- 最大等级
        self:SetBabyProgressVal(1)
        self.node_list["layout_next_attr"]:SetActive(false)
		self.node_list["lbl_baby_exp"].text.text = "max"
		self:SetBaoBaoBtnStr(Language.Medal.AlreadyMaxGrade) 	--已满级
		self:ChangeBaByUpgradeBtnState(false)
	end
end

function MarryView:PlayBabyAttrInfoUpEffect()
	for i = 1, 4 do
		if self.node_list["baby_effect" .. i] then
			local particls = self.node_list["baby_effect" .. i]:GetComponentsInChildren(typeof(UnityEngine.ParticleSystem))
				
			if particls.Length > 0 then
				for i = 0, particls.Length - 1 do
					local effect = particls[i]
	
					if effect then
						if effect.isPlaying then
							effect:Stop()
							effect:Clear()
						end
	
						effect:Play()
					end
				end
			end
		end
	end
end

-------------------------------------------------------------------------------------------------------
------------------------------------刷新材料显示-------------------------------------------------------
-------------------------------------------------------------------------------------------------------
function MarryView:FlushBabyRightCellContent()
	local baby_name_cfg = MarryWGData.Instance:GetBabyNameCfg(self.baby_type, self.baby_id)
	-- if baby_name_cfg then
	-- 	self.node_list["lbl_baby_name"].text.text = baby_name_cfg.baby_name 	--名字
	-- end
	--self:FlushInterfaceBabyRemind() 	--刷新切换宝宝按钮红点提示
	self:FlushNavBarRemind()            --刷新导航栏红点
	local baby_flag = MarryWGData.Instance:GetBabyListFlag(self.baby_type)
	if nil == baby_flag then
		return
	end

	local baby_active = baby_flag[32 - self.baby_id] == 1
	--self:SetBabyActivityIconState(not baby_active)
	self.node_list["layout_baby_noactivate"]:SetActive(not baby_active)
	self.node_list["layout_baby_activate"]:SetActive(baby_active)

	if baby_active then
		self:FlushBaoBaoRightActiveStuff()
	else
		self:FlushBaoBaoRightNoActiveStuff()
	end
end

--宝宝激活时材料显示
function MarryView:FlushBaoBaoRightActiveStuff()
	local marry_data = MarryWGData.Instance
	local baby_other_cfg = marry_data:GetBabyOtherCfg()
	local level_num = ItemWGData.Instance:GetItemNumInBagById(baby_other_cfg.add_bless_item_id)
	local str, baby_desc, baby_tips = "", "", ""

	if self.active_cell then
		self.active_cell:SetData({item_id = baby_other_cfg.add_bless_item_id, num = level_num} )
	end

	baby_tips = Language.Marry.BabyJiHuoTips2		--进阶祝福值
	self.node_list["lbl_baby_title"].text.text = Language.Marry.BabyAttrTitle1

    local cfg = marry_data:GetBabyNameCfgByUseId()
	if cfg and cfg.type == self.baby_type and cfg.id == self.baby_id then
		--self:ChangeBabyHuanHuaBtnState(false)
		--self:SetUseBabyIconState(true)
	else
		--self:ChangeBabyHuanHuaBtnState(true)
		--self:SetUseBabyIconState(false)
	end

	local show_up_level_red = MarryWGData.Instance:CheckBabyMatLevelUp(self.baby_type, self.baby_id)
	self:SetBabyRemindState(show_up_level_red > 0)

	-- self.node_list["rich_baby_tips"].text.text = ToColorStr(baby_desc, COLOR3B.WHITE)
	-- self.node_list["rich_baby_tips_2"].text.text = ToColorStr(baby_tips, COLOR3B.WHITE)
	-- self.node_list["rich_baby_desc"].text.text = ToColorStr(str, COLOR3B.L_ORANGE)
end

--未激活时材料显示
function MarryView:FlushBaoBaoRightNoActiveStuff()
	--self:ChangeBabyHuanHuaBtnState(false) 	--幻化按钮
	--self:SetUseBabyIconState(false)
	self.node_list["lbl_baby_title"].text.text = Language.Marry.BabyAttrTitle2 	--激活仙娃
	self:SetBaoBaoBtnStr(Language.Common.Activate)

	local baby_desc_cfg = MarryWGData.Instance:GetBabyDescCfg(self.baby_type, self.baby_id)
	if nil == baby_desc_cfg then
		return
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.cur_item_id)
	self.not_active_cell:SetData({item_id = self.cur_item_id})
	local role_level = RoleWGData.Instance.role_vo.level
	local is_enable_num = item_num > 0 and ((self.baby_id == 0 and self.baby_type == 0) or (self.baby_type == 1))   --男娃只有第一个使用道具激活，其他的会在前一个10阶后自动激活
	local color = is_enable_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.not_active_cell:SetRightBottomTextVisible(true)
	self.not_active_cell:SetRightBottomColorText(item_num .. "/" .. 1, color)

	if is_enable_num then
		self:ChangeBaByUpgradeBtnState(true)
		self:SetBabyRemindState(true)
	else
		self:ChangeBaByUpgradeBtnState(true)
		self:SetBabyRemindState(false)
	end
	-- self.node_list["rich_baby_tips"].text.text = ToColorStr(baby_desc, COLOR3B.WHITE)
	-- self.node_list["rich_baby_tips_2"].text.text = ToColorStr(baby_tips, COLOR3B.WHITE)
end                                                                                           ---------
-------------------------------------------------------------------------------------------------------

--点击宝宝图标
function MarryView:SelectBabyListCallBack(item,index)
	if not item or not item:GetData() then return end
	local data = item:GetData()

    local temp_id = data.id
    local temp_item_id = data.item_id
	local temp_type = data.type

	if temp_type ~= self.baby_type then
		return
	end

	self.is_baby_active = data.is_baby_active

	--防止重复点击刷新
	if self.baby_id == temp_id and self.old_baby_type == self.baby_type then
		return
	end
	self:CanPlayBabyEffect(false)
	MainuiWGCtrl.Instance:CheckCachePower()
	self:ClearBaoBaoData()
	self.cur_baby_select = index
	self.old_baby_type = self.baby_type
	self.baby_id = temp_id
	self.active_need_baby = data.auto_active_need_baby_grade
    self.cur_item_id = data.item_id
	self:FlushBaoBaoSlider(true)
	self:FlushBabyDisPlay()
	if self.node_list.baby_name then
		self.node_list.baby_name.text.text = data.baby_name
	end
end

--宝宝激活/升级按钮
function MarryView:OnClickBabyLevelUp()
	-- local baby_desc_cfg = MarryWGData.Instance:GetBabyDescCfg(self.baby_type, self.baby_id)
	-- local active_card_cfg = MarryWGData.Instance:GetBabyActiveCard(self.baby_type)
	local baby_flag = MarryWGData.Instance:GetBabyListFlag(self.baby_type)
	if baby_flag[32 - self.baby_id] == 0 then
		-- if baby_desc_cfg then
			-- if baby_desc_cfg.condition == 1 then
				local role_equip_level, lover_equip_level = MarryWGData.Instance:GetMarryEquipLevel()
				-- if role_equip_level >= baby_desc_cfg.condition_value and lover_equip_level >= baby_desc_cfg.condition_value then
				-- 	MarryWGCtrl.Instance:SendBabyOperaReq(BABY_REQ_TYPE.BABY_REQ_TYPE_ACTIVE_BABY, self.baby_type, self.baby_id)
				-- else
					local bag_index = ItemWGData.Instance:GetItemIndex(self.cur_item_id)
					if bag_index == -1 then
						TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.cur_item_id})
					else
						BagWGCtrl.Instance:SendUseItem(bag_index)
					end
				-- end
			-- else
			-- 	MarryWGCtrl.Instance:SendBabyOperaReq(BABY_REQ_TYPE.BABY_REQ_TYPE_ACTIVE_BABY, self.baby_type, self.baby_id)
			-- end
		-- end
    else
		self:AutoLevelUpBaby()
	end
end

--自动升级
function MarryView:AutoLevelUpBaby(state)
	if self:IsBaoBaoUpgrade() and not state then
		self:StopBabyGradeQuest() --再次点击：停止一键进阶
		return
	end
	local baby_other_cfg = MarryWGData.Instance:GetBabyOtherCfg()
	local level_num = ItemWGData.Instance:GetItemNumInBagById(baby_other_cfg.add_bless_item_id)
	if level_num > 0 then --材料足够
		self:SetBaoBaoBtnStr(Language.Marry.Stop)
		self:SetBabyAutoUp(true)
		if self:IsPlayBaoBaoAni() then --如果此时正在播放动画，则返回
			return
		end
		if not self:IsBaoBaoUpgrade() then --如果此时不在升级
            MainuiWGCtrl.Instance:CreateCacheTable()
        end
		MarryWGCtrl.Instance:SendBabyOperaReq(BABY_REQ_TYPE.BABY_REQ_TYPE_UPGRADE, self.baby_type, self.baby_id) --请求服务器升级
    else
        if state == nil then
		    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = baby_other_cfg.add_bless_item_id})
        end
		self:StopBabyGradeQuest()
		self:FlushBabyList()
	end
end

--幻化按钮
function MarryView:BabyHuanHuaHandler()
	local baby_list_info = MarryWGData.Instance:GetBabyListInfoByType(self.baby_type, self.baby_id)
	if baby_list_info then
		MarryWGCtrl.Instance:SendBabyOperaReq(BABY_REQ_TYPE.BABY_REQ_TYPE_CHANGE_BABY, self.baby_type, self.baby_id)
	end
end

--点击问号按钮
function MarryView:OnClickBabyTips()
	MarryView.OpenTips(Language.Marry.BabyViewTips, Language.Marry.BabyViewTitle)
end

--点击技能列表中的小技能
function MarryView:SelectSkillCallBack(item)
	if not item then return end
	item:OnClick()
end

--播放动画
function MarryView:PlayBaoBaoAni(total_exp, image_is_max_level, level_add, slider_level, exp, add_exp_num, is_from_click)
	if self:IsPlayBaoBaoAni() then --如果正在播放动画，直接返回 避免重复刷新
		return
	end
	self:IsPlayBaoBaoAni(true)
	if not self.first_tween and add_exp_num and add_exp_num > 0 then
		TipWGCtrl.Instance:ShowNumberMsg(string.format(Language.Bag.GetRoleItemTxt2, add_exp_num), 0.1, VECTOR2_POS, nil, nil, nil, UiLayer.PopTop) 		--提升飘字
		self.first_tween = true
	end

	local tween_time = 0.3
	if image_is_max_level then --升到满级，播放进度条
		local tween_max = self.node_list["prog9_bg_process"].slider:DOValue(1, tween_time)
		tween_max:SetId('baobao_slider_tween')
		tween_max:OnComplete(function ()
			if self:IsPlayBaoBaoAni() then
				self:IsPlayBaoBaoAni(false)
				self:StopBabyGradeQuest()
				self:FlushBabyRightAttrContent(slider_level + level_add)
				self:UpdateBabyList()
                self:PlayBabyEffect() --播放特效

                --策划要求男娃升到满级自动选中下一个
                if not is_from_click and self.baby_type == 0 then
                    local next_baby_list_info = MarryWGData.Instance:GetBabyListInfoByType(self.baby_type, self.baby_id + 1 )
                    if nil == next_baby_list_info then
                        return
                    end
                    --self.baby_list_view:SelectIndex(self.cur_baby_select + 1)
                end

			end
			MainuiWGCtrl.Instance:DelayShowCachePower(0)
		end)
		self.first_tween = false
		return
	end

	if level_add < 1 then --材料不够升一级
		self:FlushBabyRightAttrContent(slider_level)
	end
	local add_value = level_add + exp/total_exp

	self.fun = function (add_value)
        if self:IsPlayBaoBaoAni() == false then --强制切换到其他宝宝界面的处理
            if self.jinjie_delay then
                GlobalTimerQuest:CancelQuest(self.jinjie_delay)
                self.jinjie_delay = nil
                self.node_list.jinjie_effect_root:SetActive(false)
            end
			self:FlushBabyRightAttrContent() --刷新属性
			return
		end
		local slider = self.node_list["prog9_bg_process"].slider

		if add_value <= 0 then --结束递归，如果还在升级状态，继续请求升级
			self:IsPlayBaoBaoAni(false)
			if self:IsBaoBaoUpgrade() then
				self:AutoLevelUpBaby(true) --继续升级
			end
			self.first_tween = false
			return
		end

		local tween = nil
		if add_value > 1 then
			local time = tonumber(string.format("%.2f", (1 - slider.value) * tween_time))
            tween = slider:DOValue(1, time)
            if not self.jinjie_delay then
                --self.node_list.jinjie_effect_root:SetActive(true)
                self.jinjie_delay = GlobalTimerQuest:AddDelayTimer(function()
                    self.jinjie_delay = nil
                    self.node_list.jinjie_effect_root:SetActive(false)
                end, 1)
            end
		else
			local time = tonumber(string.format("%.2f", (add_value - slider.value) * tween_time))
			tween = slider:DOValue(add_value, time)
		end
		tween:SetId('baobao_slider_tween')

		add_value = add_value - 1
		tween:OnComplete(function ()
			if self:IsPlayBaoBaoAni() == false then
				return
			end
			if add_value >= 0 then
				slider.value = 0
				slider_level = slider_level + 1
				self:FlushBabyRightAttrContent(slider_level) --刷新属性
				self:UpdateBabyList()
				self:PlayBabyEffect()
			end
			if add_value < 1 then --要等特效播放完成后才能调用
				MainuiWGCtrl.Instance:DelayShowCachePower(0) --延时调用
			end
				self.fun(add_value)
		end)
	end
	self.fun(add_value)
end

-------------------------------------------------------------
--#region
-- 幻化按钮显示和隐藏
function MarryView:ChangeBabyHuanHuaBtnState( enable )
	--暂时屏蔽暂时不删
	self.node_list["btn_baby_huanhua"]:SetActive(enable)
end

-- 已幻化图标
function MarryView:SetUseBabyIconState( enable )
	self.node_list["use_baby_flag"]:SetActive(enable)
end

--激活/升级按钮红点提示
function MarryView:SetBabyRemindState( enable )
	self.node_list["img_baby_remind"]:SetActive(enable)
end

-- --切换宝宝按钮上的红点提示
-- function MarryView:SetIntrefaceBabyBoyRemindState( enable )
-- 	self.node_list["btn_baby_boylist_remind"]:SetActive(enable)
-- end

-- function MarryView:SetIntrefaceBabyGirlRemindState( enable )
-- 	self.node_list["btn_baby_girllist_remind"]:SetActive(enable)
-- end

-- --刷新切换宝宝按钮红点提示
-- function MarryView:FlushInterfaceBabyRemind()
-- 	local num = MarryWGData.Instance:GetBabyRemindByBabyType(BOY)
-- 	self:SetIntrefaceBabyBoyRemindState(num > 0)
-- 	num  = MarryWGData.Instance:GetBabyRemindByBabyType(GIRL)
-- 	self:SetIntrefaceBabyGirlRemindState(num > 0)
-- end

--更改激活按钮状态
function MarryView:ChangeBaByUpgradeBtnState(enable)
	local baby_flag = MarryWGData.Instance:GetBabyListFlag(self.baby_type)
    local baby_active = baby_flag[32 - self.baby_id] == 1
	self.node_list["img_ymj"]:SetActive(not enable)
	if self.baby_type == BOY and self.active_need_baby ~= 0 and not baby_active then
	 	local baby_name_cfg = MarryWGData.Instance:GetBabyNameCfg(self.baby_type, self.baby_id - 1)
		self.node_list["btn_baby_levelup"]:SetActive(false)
		self.node_list.jihuo_desx:SetActive(true)
		self.node_list.jihuo_desx.text.text = string.format(Language.Marry.XianWaJiHuo, baby_name_cfg.baby_name, self.active_need_baby)
	else
		self.node_list.jihuo_desx:SetActive(false)
		self.node_list["btn_baby_levelup"]:SetActive(enable)
	end
end

--停止自动进阶
function MarryView:StopBabyGradeQuest()
	self:SetBabyAutoUp(false)
	self:SetBaoBaoBtnStr(Language.Marry.QualityUp)	--一键升级
end

-- 未激活图标
function MarryView:SetBabyActivityIconState( enable )
	self.node_list["activate_flag"]:SetActive(enable)
	--XUI.SetGraphicGrey(self.node_list["activate_flag"], enable)
end

function MarryView:SetBabyProgressVal(num)
	if self:IsPlayBaoBaoAni() then
		return
    end
	self.node_list["prog9_bg_process"].slider.value = num
end

--清空缓存
function MarryView:ClearBaoBaoData()
	self:BaoBaoOldGrade(-1)
	self:BaoBaoOldProgress(-1)
	self:IsPlayBaoBaoAni(false)
end

--上一次宝宝等级
function MarryView:BaoBaoOldGrade( grade )
	if nil == grade then
		return self.baobao_old_grade
	end
	self.baobao_old_grade = grade
end

--上一次宝宝进度条
function MarryView:BaoBaoOldProgress(value)
	if nil == value then
		return self.baobao_old_progress
	end
	self.baobao_old_progress = value
end

--播放动画
function MarryView:IsPlayBaoBaoAni( enable )
	if nil == enable then
		return self.is_play_baobao_ani
	end
	self.is_play_baobao_ani = enable
end

--设置升级按钮状态
function MarryView:SetBaoBaoBtnStr( str )
	if str ~= self.baobao_btn_str and self.node_list["btn_baby_levelup_text"] then
		self.baobao_btn_str = str
		self.node_list["btn_baby_levelup_text"].text.text = str
	end
end

--宝宝是否正在升级 true == 正在升级
function MarryView:IsBaoBaoUpgrade()
	return self.baobao_btn_str == Language.Marry.Stop
end

--播放宝宝特效
function MarryView:PlayBabyEffect()
	if self:CanPlayBabyEffect() then
		--local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_baby_jinjie)
		--EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["baby_display"].transform)
		-- EffectManager.Instance:PlayCommonSuccessEffect(self.node_list["ph_active_effect"])
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jinjie, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["ph_active_effect"]})
		AudioService.Instance:PlayAdvancedAudio()
	end
end

function MarryView:CanPlayBabyEffect( enable )
	if nil == enable then
		return self.can_play_baobao_effect
	end
	self.can_play_baobao_effect = enable
end

function MarryView:FlushNavBarRemind()
	local num = MarryWGData.Instance:GetBabyRemindByBabyType(BOY)
	self.node_list.root_remind1:SetActive(num > 0)
	num  = MarryWGData.Instance:GetBabyRemindByBabyType(GIRL)
	self.node_list.root_remind2:SetActive(num > 0)
end
--#endregion
-------------------------------------------------------------


--加载宝宝item-------------------------------------------------------
------------BabyListItemRender
BabyListItemRender = BabyListItemRender or BaseClass(BaseRender)
function BabyListItemRender:__init()

end

function BabyListItemRender:__delete()
	self.baby_list_effect = nil
	if self.baby_grade_num then
		self.baby_grade_num:DeleteMe()
		self.baby_grade_num = nil
	end
end

function BabyListItemRender:OnFlush()
	if nil == self.data then
		return
	end
	local baby_max_grade = MarryWGData.Instance:GetBabyMaxGrade() or 0
	local marrydata_instance = MarryWGData.Instance
	local baby_list_info = marrydata_instance:GetBabyListInfoByType(self.data.type, self.data.id)
	if nil == baby_list_info then
		return
	end
	
    self.node_list["img_baby_number"].text.text = string.format(Language.Tip.Order, baby_list_info.grade)
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    self.node_list["img_baby_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    local name_scale_pose = MarryWGData.Instance:GetBabyNameCfg(self.data.type, self.data.id)
	self.node_list["img_baby_name"].text.text = name_scale_pose.baby_name
	local baby_flag = marrydata_instance:GetBabyListFlag(self.data.type)
	local is_baby_active = (baby_flag[32 - self.data.id] == 1)			--判断宝宝是否激活
	XUI.SetGraphicGrey(self.node_list["img_baby_icon"], not is_baby_active)
	self.node_list["GOJie"]:SetActive(is_baby_active)
	local is_active_flag = false

	if baby_list_info.grade == baby_max_grade then
		is_active_flag = false
	elseif not is_baby_active then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
		if item_num > 0 and ((self.data.id == 0 and self.data.type == 0) or (self.data.type == 1)) then  --男娃只有第一个使用道具激活，其他的会在前一个10阶后自动激活
			is_active_flag = true
		end

	else
		if is_baby_active then
			local show_up_level_red = MarryWGData.Instance:CheckBabyMatLevelUp(self.data.type, self.data.id)
			if show_up_level_red > 0 then
				is_active_flag = true
			end
		end
	end

	self.node_list["img_baby_remind"]:SetActive(is_active_flag)
	self:SetBgIcon()
end

function BabyListItemRender:SetBgIcon()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg then
		self.node_list["role_head_icon_frame"].image:LoadSprite(ResPath.GetF2CommonImages("common_touxiang_" .. (item_cfg.color - 1)))
	end
end

function BabyListItemRender:OnSelectChange(is_select)
	if true == is_select then
		self.node_list.img_baby_hight:SetActive(true)	--高亮
	else
		self.node_list.img_baby_hight:SetActive(false)	--非高亮
	end
end

-----------BabySkillItemRender
BabySkillItemRender = BabySkillItemRender or BaseClass(BaseRender)
function BabySkillItemRender:__init()
	self.open_grade = 0
end

function BabySkillItemRender:__delete()
	self.open_grade = 0
end

function BabySkillItemRender:OnFlush()
	if nil == self.data then return end

	local baby_list_info = MarryWGData.Instance:GetBabyListInfoByType(self.data.type, self.data.id)
	if baby_list_info then
		self.open_grade = baby_list_info.grade
		--XUI.SetGraphicGrey(self.node_list["img_skill_icon"], self.open_grade < self.data.act_need_grade)
		-- self.node_list.text_bg:SetActive(self.open_grade < self.data.act_need_grade)
		self.node_list.mask_bg:SetActive(self.open_grade < self.data.act_need_grade)
		self.node_list.lock:SetActive(self.open_grade < self.data.act_need_grade)
		local limit_str = string.format(Language.Marry.BabyActiveLimit, self.data.act_need_grade)
		self.node_list.skill_active_level.text.text = limit_str
	end
	local bundle, asset = ResPath.GetSkillIconById(self.data.skill_image)
	self.node_list.img_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.img_skill_icon.image:SetNativeSize()
    end)
end

function BabySkillItemRender:OnClick()
	if IsEmptyTable(self.data) then
		return
	end

	local skill_describe = self.data.skill_describe
	local limit_text = ""
	local is_jh = true

	if self.open_grade < self.data.act_need_grade then
		is_jh = false
		limit_text = string.format(Language.Marry.MLSkillOpetText, self.data.act_need_grade)
	else
		limit_text = Language.Marry.SkillActive
	end

	local cfg = MarryWGData.Instance:GetBabyListCfgByID(self.data.id, self.data.type)
	if not cfg then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item_id)

	if not item_cfg then
		return
	end

	local capability = 0
	local skill_attribute = MarryWGData.Instance:GetBabySkillAddAttr(self.data.param1, self.data.param2)
	if skill_attribute ~= nil then
		capability = AttributeMgr.GetCapability(skill_attribute)
	end

	local show_data = {
		icon = self.data.skill_image,
		top_text = self.data.skill_name,
		body_text = skill_describe,
		capability = capability,
		is_lock = not is_jh,
		limit_text = limit_text,
		x = 0,
		y = -120,
		set_pos = true,
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

--关于播放动画思路
--1、正常情况下：点击进阶：播放进度条 -> 如果升级 -> 刷新属性，播放成功特效，飘战斗力 -> 继续升级
--2、正常情况下：点击进阶：播放进度条 -> 如果材料不够升级 -> 刷新属性
--3、特殊情况：@1 玩家不停的点击进阶按钮
--4、特殊情况：@2 玩家点击进阶按钮后，切换了该界面的其他宝宝
--5、特殊情况：@3 玩家点击进阶按钮后，切换到其他界面

------------------------------------导航小标题-----------------------------------------------
BabyBarItemCellRender = BabyBarItemCellRender or BaseClass(BaseRender)
function BabyBarItemCellRender:LoadCallBack()
	if not self.show_item_cell then
        self.show_item_cell = ItemCell.New(self.node_list.icon)
    end

	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function BabyBarItemCellRender:__delete()
	self.parent_view = nil

	if self.show_item_cell then
		self.show_item_cell:DeleteMe()
		self.show_item_cell = nil
	end
end

function BabyBarItemCellRender:OnFlush()
	local data = self:GetData()
	if nil == data then
		return
	end

	self.node_list.normal_text.text.text = data.baby_name
	self.node_list.select_text.text.text = data.baby_name

	local baby_list_info = MarryWGData.Instance:GetBabyListInfoByType(data.type, data.id)
	self.node_list.text_state_bg:SetActive(1 ~= data.is_baby_active)
	self.node_list.normal_text_order:SetActive(1 == data.is_baby_active)
	self.node_list.select_text_order:SetActive(1 == data.is_baby_active)
	self.node_list.text_state.text.text = Language.Marry.XianWaNotActive
	self.node_list.normal_text_order.text.text = string.format(Language.Tip.Order, baby_list_info and baby_list_info.grade or 0)
	self.node_list.select_text_order.text.text = string.format(Language.Tip.Order, baby_list_info and baby_list_info.grade or 0)

	-- local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    -- self.node_list["icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	self.show_item_cell:SetData({item_id = data.item_id})

	self:FlushRemind()
end

function BabyBarItemCellRender:OnClickItem(is_on)
	if nil == is_on then
		return
	end

	if true == is_on then
		self.parent_view:SelectBabyListCallBack(self)
	end	
end

function BabyBarItemCellRender:FlushRemind()
	if not self.data or not self.index then
		return
	end

	local baby_list_info = MarryWGData.Instance:GetBabyListInfoByType(self.data.type, self.data.id)
	local is_active_flag = false
	local baby_max_grade = MarryWGData.Instance:GetBabyMaxGrade() or 0
	local is_baby_active = 1 == self.data.is_baby_active

	if baby_list_info and baby_list_info.grade == baby_max_grade then
		is_active_flag = false
	elseif not is_baby_active then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
		if item_num > 0 and ((self.data.id == 0 and self.data.type == 0) or (self.data.type == 1)) then  --男娃只有第一个使用道具激活，其他的会在前一个10阶后自动激活
			is_active_flag = true
		end

	else
		if is_baby_active then
			local show_up_level_red = MarryWGData.Instance:CheckBabyMatLevelUp(self.data.type, self.data.id)
			if show_up_level_red > 0 then
				is_active_flag = true
			end
		end
	end

	self.node_list["remind"]:SetActive(is_active_flag)
end

function BabyBarItemCellRender:OnBabyItemSelectChange(is_select)
		self.node_list.select:SetActive(is_select)
		self.node_list.normal:SetActive(not is_select)
		self.view.toggle.isOn = is_select
		self:OnClickItem(is_select)
end