﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ActorAttachEffectWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(ActorAttachEffect), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("PlayEffect", PlayEffect);
		<PERSON><PERSON>unction("StopEffect", StopEffect);
		<PERSON><PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("playOnAwake", get_playOnAwake, set_playOnAwake);
		<PERSON><PERSON>("EffectAsset", get_EffectAsset, set_EffectAsset);
		<PERSON><PERSON>("AttachTransform", null, set_AttachTransform);
		<PERSON><PERSON>("SetPosition", null, set_SetPosition);
		<PERSON><PERSON>("Scale", null, set_Scale);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayEffect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorAttachEffect obj = (ActorAttachEffect)ToLua.CheckObject(L, 1, typeof(ActorAttachEffect));
			obj.PlayEffect();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallback<PERSON>ttribute(typeof(LuaCSFunction))]
	static int StopEffect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorAttachEffect obj = (ActorAttachEffect)ToLua.CheckObject(L, 1, typeof(ActorAttachEffect));
			obj.StopEffect();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_playOnAwake(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachEffect obj = (ActorAttachEffect)o;
			bool ret = obj.playOnAwake;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playOnAwake on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EffectAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachEffect obj = (ActorAttachEffect)o;
			Nirvana.AssetID ret = obj.EffectAsset;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EffectAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_playOnAwake(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachEffect obj = (ActorAttachEffect)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.playOnAwake = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playOnAwake on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_EffectAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachEffect obj = (ActorAttachEffect)o;
			Nirvana.AssetID arg0 = StackTraits<Nirvana.AssetID>.Check(L, 2);
			obj.EffectAsset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EffectAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AttachTransform(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachEffect obj = (ActorAttachEffect)o;
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.AttachTransform = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AttachTransform on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SetPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachEffect obj = (ActorAttachEffect)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SetPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Scale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachEffect obj = (ActorAttachEffect)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.Scale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Scale on a nil value");
		}
	}
}

