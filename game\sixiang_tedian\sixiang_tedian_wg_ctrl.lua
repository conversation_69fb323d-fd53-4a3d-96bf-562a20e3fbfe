require("game/sixiang_tedian/sixiang_tedian_wg_data")
require("game/sixiang_tedian/sixiang_tedian_probability_up_view")
require("game/sixiang_tedian/sixiang_tedian_shilian_view")
require("game/sixiang_tedian/sixiang_tedian_zhenxuan_view")
require("game/sixiang_tedian/sixiang_tedian_tehui_view")

SiXiangTeDianWGCtrl = SiXiangTeDianWGCtrl or BaseClass(BaseWGCtrl)

function SiXiangTeDianWGCtrl:__init()
    if SiXiangTeDianWGCtrl.Instance then
        error("[SiXiangTeDianWGCtrl]:Attempt to create singleton twice!")
        return
    end
    SiXiangTeDianWGCtrl.Instance = self

    self.data = SiXiangTeDianWGData.New()
    self.proup_view = SiXiangTeDianProUpView.New(GuideModuleName.SiXiangProUp)
    self.shilian_view = SiXiangTeDianShiLianView.New(GuideModuleName.SiXiangShiLian)
    self.zhenxuan_view = SiXiangTeDianZhenXuanView.New(GuideModuleName.SiXiangZhenXuan)
    self.tehui_view = SiXiangTeDianTeHuiView.New(GuideModuleName.SiXiangTeHui)
    
    self:RegisterAllProtocals()
end

function SiXiangTeDianWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.proup_view:DeleteMe()
    self.proup_view = nil

    self.shilian_view:DeleteMe()
    self.shilian_view = nil

    self.zhenxuan_view:DeleteMe()
    self.zhenxuan_view = nil

    self.tehui_view:DeleteMe()
    self.tehui_view = nil

    SiXiangTeDianWGCtrl.Instance = nil
end

function SiXiangTeDianWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCYuanShenZhaoHuanTeDianInfo, "OnSCSiXiangTeDianInfo")
end

function SiXiangTeDianWGCtrl:OnSCSiXiangTeDianInfo(protocol)
    self.data:SetSiXiangTeDianInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.SiXiangCallView, TabIndex.sixiang_call_sx)
    self.proup_view:Flush()
    self.shilian_view:Flush()
    self.zhenxuan_view:Flush()
    self.tehui_view:Flush()

    MainuiWGCtrl.Instance:FlushView(0, "BaiLianZhaoHuanMainBtnFlush")
end

-- 四象十连、四象甄选、四象特惠 购买
function SiXiangTeDianWGCtrl:BuySiXiangTeDian(act_id, product_id)
    product_id = product_id or 1

    local cfg_list = SiXiangTeDianWGData.Instance:GetActSaleCfg(act_id)
    local sale_info = SiXiangTeDianWGData.Instance:GetSubActSaleInfo(act_id, product_id)
    local cfg = cfg_list and cfg_list[product_id]
    if not cfg or not sale_info then
        return
    end

    if sale_info.status == YuanShenSaleSubActSaleStatus.NotBuy then
        
    elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch then
        SiXiangCallWGCtrl.Instance:SendSiXiangRequest(OP_YUAN_SHEN_ZHAO_HUAN_TYPE.TE_DIAN,
            OP_YUAN_SHEN_ZHAO_HUAN_TYPE_TE_DIAN_TYPE.OP_YUAN_SHEN_ZHAO_HUAN_TYPE_TE_DIAN_TYPE_GET_RMB_REWARD,
            act_id, cfg.product_id)
        return
    elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyAndFetched and sale_info.buy_num >= cfg.limit_buy_times then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.SiXiangCall.HasFetch)
        return
    end

    local buyfunc = function ()
        if cfg.price_type == SiXiangBuyType.RMB then
            RechargeWGCtrl.Instance:Recharge(cfg.special_sale_price, GET_GOLD_REASON.GET_GOLD_REASON_YUAN_SHEN_ZHAO_HUAN, cfg.rmb_seq)
        elseif cfg.price_type == SiXiangBuyType.XY then
            SiXiangCallWGCtrl.Instance:SendSiXiangRequest(OP_YUAN_SHEN_ZHAO_HUAN_TYPE.TE_DIAN,
                OP_YUAN_SHEN_ZHAO_HUAN_TYPE_TE_DIAN_TYPE.OP_YUAN_SHEN_ZHAO_HUAN_TYPE_TE_DIAN_TYPE_BUY_PRODUCT,
                act_id, product_id)
        end
    end

    if cfg.item and cfg.item[0].item_id then
        --local name = ItemWGData.Instance:GetItemConfig(cfg.item[0].item_id).name
        local price_str = RoleWGData.GetPayMoneyStr(cfg.special_sale_price, GET_GOLD_REASON.GET_GOLD_REASON_YUAN_SHEN_ZHAO_HUAN, cfg.rmb_seq)
        local tip_str = ""
        if cfg.price_type == SiXiangBuyType.XY then
            tip_str = string.format(Language.SiXiangCall.XQZXYBuyStr, cfg.special_sale_price, cfg.name)
        elseif cfg.price_type == SiXiangBuyType.RMB then
            tip_str = string.format(Language.SiXiangCall.XQZRNBBugStr, price_str, cfg.name)
        end

        TipWGCtrl.Instance:OpenAlertTips(tip_str, buyfunc)
    else
        buyfunc()
    end
end