TaskView = TaskView or BaseClass(SafeBaseView)

local toggle_index_to_task_type = {
		GameEnum.TASK_TYPE_ZHU,
		GameEnum.TASK_TYPE_ZHI,
		GameEnum.TASK_TYPE_RI,
		GameEnum.TASK_TYPE_MENG,
		GameEnum.TASK_TYPE_GUILD_BUILD,
	}

local toggle_task_type_to_index = {}
for k,v in pairs(toggle_index_to_task_type) do
	toggle_task_type_to_index[v] = k
end

local ui_info = {
	[GameEnum.TASK_TYPE_ZHU] = {
		go_target = {position = Vector3(138, 144, 0), visible = true},
		go_describe = {position = Vector3(138, 144, 0), visible = false},
		go_reward = {position = Vector3(138, 15, 0), visible = true},
		go_other = {position = Vector3(138, -136, 0), visible = false},
	},

	[GameEnum.TASK_TYPE_ZHI] = {
		go_target = {position = Vector3(138, 144, 0), visible = true},
		go_describe = {position = Vector3(138, 136, 0), visible = false},
		go_reward = {position = Vector3(138, 15, 0), visible = true},
		go_other = {position = Vector3(138, 136, 0), visible = false},
	},

	[GameEnum.TASK_TYPE_RI] = {
		go_target = {position = Vector3(138, 144, 0), visible = true},
		go_describe = {position = Vector3(138, 136, 0), visible = false},
		go_reward = {position = Vector3(138, 15, 0), visible = true},
		go_other = {position = Vector3(138, -124, 0), visible = true},
	},

	[GameEnum.TASK_TYPE_MENG] = {
		go_target = {position = Vector3(138, 144, 0), visible = true},
		go_describe = {position = Vector3(138, 136, 0), visible = false},
		go_reward = {position = Vector3(138, 15, 0), visible = true},
		go_other = {position = Vector3(138, -124, 0), visible = true},
	},

	[GameEnum.TASK_TYPE_GUILD_BUILD] = {
		go_target = {position = Vector3(138, 144, 0), visible = true},
		go_describe = {position = Vector3(138, 136, 0), visible = false},
		go_reward = {position = Vector3(138, 15, 0), visible = true},
		go_other = {position = Vector3(138, -124, 0), visible = true},
	},
}
function TaskView:__init()
	self:SetMaskBg(false)
	-- self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/task_prefab", "layout_task_panel")
	self.curr_task = nil
	self.curr_task_type = -1
	self.accor_list = {}
	self.end_pos = Vector3.zero
	self.exp_pos = Vector3.zero
	self.task_callback = BindTool.Bind(self.OnDailyChange, self)
	self.task_guild_callback = BindTool.Bind(self.OnGuildChange, self)

end

function TaskView:__delete()
	self.accor_list = {}
	self.end_pos = Vector3.zero
	self.exp_pos = Vector3.zero
end

function TaskView:LoadCallBack()
	if nil == self.reward_list_view then
		self.reward_list_view = AsyncListView.New(TaskRewardItemRender,self.node_list["ph_reward_list"])
	end

	for i=1,5 do
		self.accor_list[i] = {}
		self.accor_list[i].obj = self.node_list['task_btn_left_' .. i]
		self.accor_list[i].accordion_element = self.accor_list[i].obj.accordion_element
		self.accor_list[i].obj.toggle:AddValueChangedListener(BindTool.Bind(self.AccorBtnClickCallback, self, i))
		self.accor_list[i].list = self.node_list['List' .. i]
		self.accor_list[i].list_item_cell = {}
		self:LoadCell(i)
	end
	--当没有主线任务的时候就会没有索引，所以一次判断有任务则点开
	self.btn_index = 1
	for i=1,5 do
		local task_list = TaskWGData.Instance:GetTaskListIdByType(toggle_index_to_task_type[i])

		--因为主线任务界面必须有数据，不然报错，所以当主线任务y没有的时候先将他隐藏掉
		if i == 1 and IsEmptyTable(task_list) then
			--self.accor_list[i].obj:SetActive(false)
			self.empty_zhu_task = true
		end

		if not TaskWGData.Instance:GetTaskOpen(toggle_index_to_task_type[i]) then
			self.accor_list[i].obj:SetActive(false)
		else
			self.accor_list[i].obj:SetActive(true)
		end

		if not IsEmptyTable(task_list) then
			self.btn_index = i
			-- self.accor_list[i].accordion_element.isOn = true
			break
		end
    end

    --没有支线任务，隐藏
    local zhixian_task_list = TaskWGData.Instance:GetTaskListIdByType(toggle_index_to_task_type[2])
    if IsEmptyTable(zhixian_task_list) then
        self.accor_list[2].obj:SetActive(false)
    end

	if self.empty_zhu_task and self.btn_index == 1 then  -- 没有任务存在的情况
		self.accor_list[1].accordion_element.isOn = true
	end

	self:FlushVipExp()
	XUI.AddClickEventListener(self.node_list["btn_v6exp"], BindTool.Bind1(self.OnClickOpenExp, self))
	XUI.AddClickEventListener(self.node_list["btn_reward"], BindTool.Bind1(self.OnClickReward, self))
	XUI.AddClickEventListener(self.node_list["btn_quick"], BindTool.Bind1(self.OnClickQuick, self))
	XUI.AddClickEventListener(self.node_list["btn_one_key"], BindTool.Bind1(self.OnClickOneKey, self))
	self.task_guild_change = GlobalEventSystem:Bind(OtherEventType.TASK_GUILD_INFO_CHANGE,self.task_guild_callback)
end

function TaskView:LoadCell(index)
	local task_list = TaskWGData.Instance:GetTaskListIdByType(toggle_index_to_task_type[index])
	if 0 == #task_list and  toggle_index_to_task_type[index] == GameEnum.TASK_TYPE_ZHU  then 	--若服务端没发来则自己取下一个主线任务
		local cfg = TaskWGData.Instance:GetNextZhuTaskConfig()
		if cfg ~= nil and cfg.task_id ~= nil then
			task_list[1] = cfg.task_id
		end
	end

	local res_async_loader = AllocResAsyncLoader(self, "task_item" .. index)
	res_async_loader:Load("uis/view/task_prefab", "task_item", nil, function(new_obj)
		for i = 1,#task_list do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			if self.accor_list[index].list then
				obj_transform:SetParent(self.accor_list[index].list.transform, false)
				obj:GetComponent("Toggle").group = self.accor_list[index].list.toggle_group
			end
			local item_cell = TaskListCell.New(obj)
			item_cell.parent_view = self
			item_cell:SetIndex(i)
			item_cell:SetParentIndex(index)
			item_cell:SetData(task_list[i])
			self.accor_list[index].list_item_cell[i] = item_cell
			if i == #task_list and self.btn_index == index then
				self.accor_list[self.btn_index].accordion_element.isOn = false
				self.accor_list[self.btn_index].accordion_element.isOn = true
			end
		end
	end)
end

function TaskView:FlushVipExp()
	local has_double = VipPower.Instance:GetHasPower(VipPowerId.daily_task_double)
	if has_double then
		self.node_list["v6exp_bg"]:SetActive(false)
	else
		local cur_exp = ExpPoolWGData.Instance:PoolExp()
		cur_exp = CommonDataManager.ConverExpByThousand(cur_exp)
		self.node_list["txt_v6exp"].text.text = cur_exp
	end
end

function TaskView:AccorBtnClickCallback(index)
	if self.accor_list[index].accordion_element.isOn then --刷新
		local task_list = TaskWGData.Instance:GetTaskListIdByType(toggle_index_to_task_type[index])
		if 0 == #task_list then --功能开始还未实现，这里暂时全标记为己完成
			self:OnClickProductHandler(nil, toggle_index_to_task_type[index])

			-- if self.accor_list[index] and #self.accor_list[index].list_item_cell == 0 then
			-- 	self:AllTaskDone(index)
			-- 	return
			-- end
		else
			if self.accor_list[index].list_item_cell[1] then
				local obj = self.accor_list[index].list_item_cell[1]:GetView()
				if obj:GetComponent("Toggle").isOn == true then
					self.accor_list[index].list_item_cell[1]:SetData(task_list[1])
					self.accor_list[index].list_item_cell[1]:OnClickItem(true)
					return
				end
				obj:GetComponent("Toggle").isOn = true
			end
		end
		-- for i=1,4 do
		-- 	self.accor_list[i].list:SetActive(i == index)
		-- end
	end
	self:FlushVipExp()
	-- self.node_list.btn_one_key:SetActive(index == GameEnum.TASK_TYPE_MENG)
end

function TaskView:OnGuildChange()
	local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_MENG)
	if 0 == #task_list then return end
	self:DelayChangeTask(task_list[1])
end

function TaskView:OnDailyChange(task_id)
	self:DelayChangeTask(task_id)
end

function TaskView:OnFlush(param_list)
	if not param_list or not param_list.all then return end
	self.end_pos = param_list.all.end_pos
	self.exp_pos = param_list.all.exp_pos

	for k,v in pairs(param_list) do
		if k == "all" then
			if v.select_task_type then
				local index = toggle_task_type_to_index[v.select_task_type]
				self.btn_index = index
				self.node_list["task_btn_left_" .. index].accordion_element.isOn = true
			end
		end
	end
end

-- 解决重复刷新
function TaskView:DelayChangeTask(task_id)
	-- body
	if self.delay_task then
		GlobalTimerQuest:CancelQuest(self.delay_task)
		self.delay_task = nil
	end
	self.delay_task = GlobalTimerQuest:AddDelayTimer(function() self:OnTaskChange(task_id) end, 0.1)
end

function TaskView:OnTaskChange(task_id)
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if not task_cfg then return end
	if not self.curr_task then return end
	local task_data
	if self.curr_task.task_type == GameEnum.TASK_TYPE_RI then
		task_data = DailyWGData.Instance:GetTaskTuMoData()
        if task_data and not IsEmptyTable(task_data) then
            if task_data.commit_times > COMMON_CONSTS.TASK_DAILY_DAY_MAX_COUNT then
				local index = 1
				for i,v in ipairs(toggle_index_to_task_type) do
					if next(TaskWGData.Instance:GetTaskListIdByType(v)) then
						index = i
						break
					end
				end
        		self.accor_list[index].obj.toggle.isOn = true
				self:ChangeLeftBtn()
            	return
            end

        end
	elseif self.curr_task.task_type == GameEnum.TASK_TYPE_MENG then
		task_data = TaskWGData.Instance:GetGuildInfo()
        if task_data and not IsEmptyTable(task_data) then
            if task_data.complete_task_count > TaskWGData.Instance:GetTaskGuildWeekMaxCount() - 1 then
				local index = 1
				for i,v in ipairs(toggle_index_to_task_type) do
					if next(TaskWGData.Instance:GetTaskListIdByType(v)) then
						index = i
						break
					end
				end
        		self.accor_list[index].obj.toggle.isOn = true
            	self:ChangeLeftBtn()
            	return
            end
        end
    elseif self.curr_task.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
		task_data = TaskWGData.Instance:GetGuildBuildInfo()
        if task_data and not IsEmptyTable(task_data) then
            if task_data.complete_task_count > TaskWGData.Instance:GetTaskGuildWeekMaxCount() - 1 then
				local index = 1
				for i,v in ipairs(toggle_index_to_task_type) do
					if next(TaskWGData.Instance:GetTaskListIdByType(v)) then
						index = i
						break
					end
				end
        		self.accor_list[index].obj.toggle.isOn = true
            	self:ChangeLeftBtn()
            	return
            end
        end
	end

	if not self.curr_task then
		return
	end
	if task_cfg.task_type ~= self.curr_task.task_type then
		return
	end
	
	self:OnClickProductHandler(task_cfg)
end

function TaskView:ChangeLeftBtn()
	-- local task_type,task_list
	-- --不判断主线
	-- for i=2,4 do
	-- 	task_type = toggle_index_to_task_type[i]
	-- 	task_list = TaskWGData.Instance:GetTaskListIdByType(task_type)
	-- 	XUI.SetButtonEnabled(self.node_list["task_btn_left_" .. i], 0 ~= #task_list)
	-- 	-- if 0 == #task_list then
	-- 		-- self.node_list["task_btn_left_" .. i].transform:SetAsLastSibling()
	-- 	-- end
	-- end
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_MENG)
	if self.node_list["task_btn_left_4"] then
		local state = (0 ~= mainrolevo.guild_id and #task_list > 0)
		self.node_list["task_btn_left_4"]:SetActive(state)
		if self.node_list["task_btn_left_4"].accordion_element.isOn then
			self.node_list["List4"]:SetActive(state)
		else
			self.node_list["List4"]:SetActive(false)
		end
	end

	if self.node_list["task_btn_left_5"] then
		local state = GuildWGData.Instance:GetGuildBuildTaskIsShow()
		self.node_list["task_btn_left_5"]:SetActive(state)
		self.node_list["List5"]:SetActive(state and self.node_list["task_btn_left_5"].accordion_element.isOn)
	end

	--日常任务未开启的时候不做展示
	if self.node_list["task_btn_left_3"] then
		local state = TaskWGData.Instance:DailyTaskIsOpen()
		self.node_list["task_btn_left_3"]:SetActive(state)
		self.node_list["List3"]:SetActive(state and self.node_list["task_btn_left_3"].accordion_element.isOn)
	end
end

function TaskView:SetLayout(task_cfg)
	local info = ui_info[self.curr_task_type]
	for k,v in pairs(info) do
		-- self.node_list[k].transform.localPosition = v.position
		self.node_list[k]:SetActive(v.visible)
	end
end

-- done_task_type 该类型任务全部完成
function TaskView:OnClickProductHandler(task_cfg, done_task_type)
	self.curr_task = nil

	if done_task_type then
		self.curr_task_type = done_task_type
		self:SetLayout()
		self:AllTaskDone(done_task_type)
		return
	end
	if not task_cfg then return end
	self.curr_task = task_cfg
	self.curr_task_type = self.curr_task.task_type
	self:SetLayout()
-- 第一个任务特殊处理
	self.node_list["text_desc2"]:SetActive(self.curr_task.task_type == GameEnum.TASK_TYPE_MENG)
	self.node_list["btn_quick"]:SetActive(false)
	local is_show_task = TaskWGData.Instance:IsCanShowTask(task_cfg.task_id)
	local status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
	local color = status == GameEnum.TASK_STATUS_COMMIT and COLOR3B.GREEN or COLOR3B.RED
	local des, task_data, temp_count
	if self.curr_task.task_type == GameEnum.TASK_TYPE_ZHU then
		des = task_cfg.task_name .. "<color="..color..">"..Language.Task.task_status2[status] .. "</color>"
	elseif self.curr_task.task_type == GameEnum.TASK_TYPE_ZHI then
		des = task_cfg.task_name .. "<color="..color..">"..Language.Task.task_status2[status] .. "</color>"
	elseif self.curr_task.task_type == GameEnum.TASK_TYPE_RI then              -- 日常
		self.node_list["text_desc"].text.text = Language.Task.ri_chang_desc
		task_data = DailyWGData.Instance:GetTaskTuMoData()
		local commit_times = task_data.commit_times
        -- if task_data.is_accept == 0 and commit_times ~= 0 then
        --     commit_times = commit_times - 1
        -- end
        if task_data and not IsEmptyTable(task_data) then
            temp_count = "[".. commit_times .. "/" .. COMMON_CONSTS.TASK_DAILY_DAY_MAX_COUNT.."]"
        end
        temp_count = temp_count or ""
		des = task_cfg.task_name .. "<color="..COLOR3B.GREEN..">".. temp_count .. "</color>"
		-- if task_data and task_data.is_accept == 0 then  -- 未接任务
		-- 	XUI.SetButtonEnabled(self.node_list["btn_quick"], false)
			-- XUI.SetButtonEnabled(self.node_list["btn_one_key"], false)

		-- else
			XUI.SetButtonEnabled(self.node_list["btn_quick"], true)
			self.node_list["btn_quick"]:SetActive(is_show_task and TaskWGData.Instance:OneKeyBtnIsOpen())
			-- XUI.SetButtonEnabled(self.node_list["btn_one_key"], false)
		-- end
		self.node_list["btn_one_key"]:SetActive(false)
	elseif self.curr_task.task_type == GameEnum.TASK_TYPE_MENG then
		-- self.node_list["btn_one_key"]:SetActive(true)
		self.node_list["text_desc"].text.text = Language.Task.meng_desc
		task_data = TaskWGData.Instance:GetGuildInfo()
        if task_data and not IsEmptyTable(task_data) then
        	local commit_times = task_data.complete_task_count
        	local task_status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
        	if task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS or task_status == GameEnum.TASK_STATUS_COMMIT then
                commit_times = commit_times + 1
            end
			temp_count = "[" .. commit_times .. "/" .. TaskWGData.Instance:GetTaskGuildWeekMaxCount() .. "]"
            --if 0 ~= commit_times % 10 then
            --    commit_times = commit_times % 10
            --elseif 0 ~= commit_times then
            --    commit_times = 10
            --end
            --temp_count = "[".. commit_times .."/10]" -- 策划说改成这样子--策划又要该回去
		end
		temp_count = temp_count or ""
		des = task_cfg.task_name .. "<color="..COLOR3B.GREEN..">".. temp_count .. "</color>"
		-- if task_data and TaskWGData.Instance:GetTaskStatus(task_cfg.task_id) == GameEnum.TASK_STATUS_CAN_ACCEPT then  -- 未接任务
			-- XUI.SetButtonEnabled(self.node_list["btn_quick"], false)
			-- XUI.SetButtonEnabled(self.node_list["btn_one_key"], false)
		-- else
			XUI.SetButtonEnabled(self.node_list["btn_quick"], true)
			self.node_list["btn_quick"]:SetActive(false)
		-- 	XUI.SetButtonEnabled(self.node_list["btn_one_key"], true)
		-- end
	--仙盟建设任务标题
	elseif self.curr_task.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
		self.node_list["text_desc"].text.text = Language.Task.guild_bulid_desc
		local all_task_num = GuildWGData.Instance:GetDayTaskMaxNum()
		local finish_task_num = GuildWGData.Instance:GetBuildTaskFinishNum()
		temp_count = string.format("[%s/%s]",finish_task_num,all_task_num)
		-- des = task_cfg.task_name .. "<color="..COLOR3B.GREEN..">".. temp_count .. "</color>"
		des = Language.Task.task_type_name[GameEnum.TASK_TYPE_GUILD_BUILD].."<color="..COLOR3B.GREEN..">".. temp_count .. "</color>"
		self.node_list["btn_quick"]:SetActive(false)

	end
	self.node_list["text_title"].text.text = des
	local has_double = VipPower.Instance:GetHasPower(VipPowerId.daily_task_double)
	self.node_list.v6exp_bg:SetActive(self.curr_task.task_type == GameEnum.TASK_TYPE_RI and not has_double)
	local level = GameVoManager.Instance:GetMainRoleVo().level
	if not is_show_task then
		des = TaskWGData.Instance:GetFirstDes()[task_cfg.task_type]
		if not des then
			print_error("Error config:::", task_cfg.task_id)
			return
		end
	elseif task_cfg.min_level > level then
		des = string.format(Language.Task.shenjizhi,task_cfg.min_level, level, task_cfg.min_level)
	else
		des = TaskWGData.Instance:GetTaskProDesById(task_cfg.task_id)
		des = string.gsub(des, "%<.-%>", "")
		des = string.gsub(des, "</color>", "")
	end
	self.node_list["text_target"].text.text = des
	self.node_list["text_describe"].text.text = task_cfg.chapter_desc
	-- self.node_list["text_btn_reward"].text.text = Language.Task.task_btn_status[status]

	local reward_list = TaskWGData.Instance:GetTaskReward(task_cfg.task_id)
	if reward_list and not IsEmptyTable(reward_list) then
		self.reward_list_view:SetDataList(reward_list,3)
	end
	self.node_list["btn_reward"]:SetActive(true)
	XUI.SetButtonEnabled(self.node_list["btn_reward"], true)
end

function TaskView:AllTaskDone(task_type)
	self.node_list["text_title"].text.text = Language.Task.task_type_name[task_type] .. "<color=#006a25>（"..Language.Task.task_status[4] .. "）</color>"
	local has_double = VipPower.Instance:GetHasPower(VipPowerId.daily_task_double)
	self.node_list.v6exp_bg:SetActive(task_type == GameEnum.TASK_TYPE_RI and not has_double)
	self.node_list["text_describe"].text.text = "<color=#006a25>（"..Language.Task.task_status[4] .. "）</color>"
	self.node_list["text_target"].text.text = "（"..Language.Task.task_status[4] .. "）"
	self.node_list["text_desc"].text.text = ""
	XUI.SetButtonEnabled(self.node_list["btn_quick"], false)
	self.node_list["btn_quick"]:SetActive(false)
	XUI.SetButtonEnabled(self.node_list["btn_one_key"], false)
	XUI.SetButtonEnabled(self.node_list["btn_reward"], false)
	self.node_list["btn_reward"]:SetActive(false)
	self.node_list["text_desc2"]:SetActive(task_type == GameEnum.TASK_TYPE_MENG)
	if task_type == GameEnum.TASK_TYPE_RI then              -- 日常
		self.node_list["text_desc"].text.text = Language.Task.ri_chang_desc
		self:TaskTypeRiDes()
	elseif task_type == GameEnum.TASK_TYPE_MENG then
		self.node_list["text_desc"].text.text = Language.Task.meng_desc
	elseif task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
		self.node_list["text_desc"].text.text = Language.Task.guild_bulid_desc
		local accept_guild_task = TaskWGData.Instance:GetTaskGuildBuildCfg()
		local task_is_finish = GuildWGData.Instance:GetBuildTaskIsAllFinish()
		--裸奔也可以，判断只是为了保险
		if nil == accept_guild_task and not task_is_finish then
			local all_task_num = GuildWGData.Instance:GetDayTaskMaxNum()
			local finish_task_num = GuildWGData.Instance:GetBuildTaskFinishNum()
			local temp_count = string.format("[%s/%s]",finish_task_num,all_task_num)
			self.node_list["text_title"].text.text = Language.Task.task_type_name[task_type] .. "<color="..COLOR3B.GREEN..">".. temp_count .. "</color>"
			local task_cfg = TaskWGData.Instance:GetTaskConfig(GUILD_BUILD_TASK_OTHER_TYPE.CLIENT_SHOW_TASK_ID)
			self.node_list["text_target"].text.text = string.gsub(task_cfg.accept_desc, "'#.-'","'#A5492D'")
			XUI.SetButtonEnabled(self.node_list["btn_reward"], true)
			self.node_list["btn_reward"]:SetActive(true)
		end
	end
	if self.reward_list_view then
		self.reward_list_view:RemoveAllItem()
	end
end

--日常任务特殊展示处理（未接取任务的时候）
function TaskView:TaskTypeRiDes()
	local is_finish = TaskWGData.Instance:DailyTaskIsFinsh()
	if not is_finish then
		self.node_list["text_title"].text.text = Language.Task.dailytasktitle
		self.node_list["text_target"].text.text = Language.Task.dailytaskDesc_1
		XUI.SetButtonEnabled(self.node_list["btn_reward"], true)
		self.node_list["btn_reward"]:SetActive(true)
		self.node_list["go_reward"]:SetActive(false)
	end
end

function TaskView:OnClickOpenExp()
	FunOpen.Instance:OpenViewByName(GuideModuleName.ExpPoolView)
end

function TaskView:OnClickReward()
	--比较特殊提前拦下来
	if self.curr_task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
		FunOpen.Instance:OpenViewByName(GuideModuleName.guild_task)
		self:Close()
		return
	end

	--日常任务
	if self.curr_task_type == GameEnum.TASK_TYPE_RI then
		ViewManager.Instance:Close(GuideModuleName.BiZuo)
		--未接取任务的时候
		local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_RI)
		if task_list and #task_list == 0 then
			TaskWGData.Instance:DoShangJinTask(true)
			self:Close()
			return
		end
	end

	if not self.curr_task then return end
	-- TaskWGCtrl.Instance:DoTask(self.curr_task.task_id)
	local task_id = self.curr_task.task_id
	NEXT_CAN_AUTO_GUIDE_TIME = 0
	-- print_error("task_id======",task_id,TaskWGData.Instance:GetTaskStatus(task_id))
	MainuiWGCtrl.Instance:DoTask(task_id,TaskWGData.Instance:GetTaskStatus(task_id))
	self:Close()
end

function TaskView:OnClickOneKey()
	self:SendHandle(0)
end

function TaskView:OnClickQuick()
	-- body
	if not self.curr_task then return end
	-- self:SendHandle(1)
	ViewManager.Instance:Open(GuideModuleName.TaskDailyBatchTips, 0, nil, self.curr_task)
end

function TaskView:PlayGetEffect()
    local start_obj = self.node_list.effect
    if not start_obj or not self.end_pos or not self.exp_pos then return end
    local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jinyanqiu_wpk)
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, start_obj.transform, nil, nil, nil, Vector3(1.5, 1.5, 1.5))

    bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jinyanqiu_guangqiu)
    TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.Task, bundle_name, asset_name, start_obj, nil, DG.Tweening.Ease.OutCubic, 1, nil, nil, nil, 200, nil, nil, nil, self.end_pos)

    --local exp_view = MainuiWGCtrl.Instance:GetView():GetExpView()
   --[[ end_obj = self.exp_view.view
    local width = end_obj.transform.rect.size.x

    local vector2 = Vector2(end_obj.slider.value * width - width, 0)--]]
    TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.Task, bundle_name, asset_name, start_obj, nil, DG.Tweening.Ease.OutCubic, 1, nil, nil, nil, 200, nil, nil, nil, self.exp_pos)
end


-- 0:全部，1：单条
function TaskView:SendHandle(value)
	-- body
	if not self.curr_task then return end
    local total_gold = -1
    local total_count = -1
    local other_cfg = TaskWGData.Instance:GetOtherInfo()
    if self.curr_task.task_type == GameEnum.TASK_TYPE_RI then
        local task_data = DailyWGData.Instance:GetTaskTuMoData()
        if task_data and next(task_data) ~= nil then
            total_count = COMMON_CONSTS.TASK_DAILY_DAY_MAX_COUNT - task_data.commit_times + 1
        end
        total_count = value == 1 and value or total_count
        total_gold = total_count * (other_cfg.daily_onekey_gold or 0)
        if value == 0 then
        	total_gold = total_gold
        end
    elseif self.curr_task.task_type == GameEnum.TASK_TYPE_MENG then
        local task_data = TaskWGData.Instance:GetGuildInfo()
        if task_data and next(task_data) ~= nil then
            total_count = TaskWGData.Instance:GetTaskGuildWeekMaxCount() - task_data.complete_task_count
        end
        total_count = value == 1 and value or total_count
      	if 1 == value and self.curr_task.condition == GameEnum.TASK_COMPLETE_CONDITION_13 then
    		if self.alert then
    			self.alert:ClearCheckHook()
    		end
    		total_gold = self.curr_task.c_param1
      	else
	        total_gold = total_count * (other_cfg.guild_task_gold or 0)
      	end
    end

    -- 快速完成
    local function ok_callback()
    	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
    	if not main_role_vo then return end
    	if total_gold > main_role_vo.bind_gold + main_role_vo.gold then
    		self:OpenRechargeAlert()
    		return false
    	end
        if self.curr_task.task_type == GameEnum.TASK_TYPE_RI then
        	local commit_all = 1 == value and 2 or 1
            ActivityWGCtrl.Instance:SendTumoCommitTask(commit_all, self.curr_task.task_id, 0)--请求屠魔任务
        elseif self.curr_task.task_type == GameEnum.TASK_TYPE_MENG then
            GuildWGCtrl.SendFinishAllGuildTask(value)
        end
    end

    local str, show_check_box
    if 1 == value then
    	str = string.format(Language.Task.TaskQuickTip, total_gold)
    	show_check_box = true
    else
    	str = string.format(Language.Task.TaskQuicklyTip, total_gold, total_count)
    	show_check_box = false
    end
    if nil == self.alert then
        self.alert = Alert.New(str, ok_callback, nil, nil, show_check_box)
    else
        self.alert:SetLableString(str)
        self.alert:SetOkFunc(ok_callback)
        self.alert:SetShowCheckBox(show_check_box)
    end

    self.alert:Open()
end

function TaskView:OpenRechargeAlert()
	-- body
	local function ok_callback()
    	ViewManager.Instance:Open(GuideModuleName.Vip, "recharge_cz")
    end

	local str = Language.Common.goto_recharge
	if nil == self.alert then
        self.alert = Alert.New(str, ok_callback, nil)
    else
        self.alert:SetLableString(str)
        self.alert:SetOkFunc(ok_callback)
    end
    self.alert:Open()
end

function TaskView:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
	for k,v in pairs(self.accor_list) do
		for i,j in pairs(v.list_item_cell) do
			j:DeleteMe()
		end
	end

	if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

	if self.task_guild_change then
        GlobalEventSystem:UnBind(self.task_guild_change)
        self.task_guild_change = nil
    end

	self.accor_list = {}
	self.curr_task = nil
	self.empty_zhu_task = nil

end

function TaskView:OpenCallBack()
	self.task_event = GlobalEventSystem:Bind(OtherEventType.TASK_INFO_CHANGE, self.task_callback)    -- 任务进度改变
	-- self.task_guild_event = GlobalEventSystem:Bind(OtherEventType.TASK_GUILD_INFO_CHANGE, self.task_guild_callback)	-- 帮派进度

	-- 已加载就直接刷新界面
	if self.curr_task and self.curr_task_type then
		local index = toggle_task_type_to_index[self.curr_task_type]

		if toggle_task_type_to_index[self.curr_task_type] and self.node_list["task_btn_left_" .. index] then
			-- 未选中就选中，选中就刷新
			if self.node_list["task_btn_left_" .. index].accordion_element.isOn then
				local task_list = TaskWGData.Instance:GetTaskListIdByType(self.curr_task_type)

				if 0 == #task_list then --功能开始还未实现，这里暂时全标记为己完成
					self:OnClickProductHandler(nil, self.curr_task_type)
				else
					self:OnClickProductHandler(self.curr_task)
				end
			else
				self.node_list["task_btn_left_" .. index].accordion_element.isOn = true
			end
		end

	end
end

function TaskView:ShowIndexCallBack()
	self:ChangeLeftBtn()
end

function TaskView:CloseCallBack()
	-- self.curr_task_type = self.curr_task_type ~= GameEnum.TASK_TYPE_GUILD_BUILD and -1 or self.curr_task_type
	if self.task_event then
        GlobalEventSystem:UnBind(self.task_event)
        self.task_event = nil
    end

    if self.task_guild_event then
        GlobalEventSystem:UnBind(self.task_guild_event)
        self.task_guild_event = nil
    end

    if self.delay_task then
		GlobalTimerQuest:CancelQuest(self.delay_task)
		self.delay_task = nil
	end
end


TaskListCell = TaskListCell or BaseClass(BaseRender)

function TaskListCell:__init()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem,self))
	self.parent_index = 0
end

function TaskListCell:__delete()
	self.parent_view = nil
	self.task_cfg = nil
end

function TaskListCell:OnClickItem(is_click)
	if is_click then
		local task_list = TaskWGData.Instance:GetTaskListIdByType(toggle_index_to_task_type[self.parent_index])
		if 0 == #task_list then --功能开始还未实现，这里暂时全标记为己完成
			self.parent_view:OnClickProductHandler(nil, toggle_index_to_task_type[self.parent_index])
		else
			self.parent_view:OnClickProductHandler(self.task_cfg)
		end
	end
end

function TaskListCell:OnFlush()
	local task_cfg = TaskWGData.Instance:GetTaskConfig(self.data)
	if not task_cfg then 	--若服务端没发来则自己取下一个主线任务
		task_cfg = TaskWGData.Instance:GetNextZhuTaskConfig()
	end
	self.task_cfg = task_cfg
	local task_name = string.gsub(task_cfg.task_name, "%(.-%)", "")
	self.node_list.task_name.text.text = task_name
end

function TaskListCell:SetParentIndex(parent_index)
	-- body
	self.parent_index = parent_index
end

-------------------------------------------TaskRewardItemRender-------------------------------------------------------
TaskRewardItemRender = TaskRewardItemRender or BaseClass(BaseRender)
function TaskRewardItemRender:__init()

end

function TaskRewardItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TaskRewardItemRender:OnFlush()
	if nil == self.data then
		return
	end
	if nil == self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.cell_item)
	end
	self.item_cell:SetData(self.data)

end