local TOGGLE_MAX_COUNT = 10
local LIST_CELL_COUNT = 10		--list最大格子数量

function HuanHuaFetterView:InitInfoView()
    if not self.part_cell_list then
		self.part_cell_list = {}
		for i = 0, 6 do
			self.part_cell_list[i] = FetterPartTypeCell.New(self.node_list["part_fetter_cell_" .. i])
			self.part_cell_list[i]:SetIndex(i)
            self.part_cell_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectPartCallBack, self))
		end
	end

    if not self.part_model_display then
        self.part_model_display = OperationActRender.New(self.node_list["display_model"])
        self.part_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE)
	end

    XUI.AddClickEventListener(self.node_list.btn_act, BindTool.Bind(self.OnClickAct, self))  --激活套装属性button
    self:CreatToggleList()
    self.attr_item_list = {}
end

function HuanHuaFetterView:InfoReleaseCallBack()
    self.stop_cur = nil
    self.load_cell_complete = nil
    self.accordion_list = nil
    self.flush_wait_flag = nil
    self.force_big_type = nil
    self.force_suit_type = nil
    self.select_big_type = nil
    self.select_small_type = nil
    self.select_part_index = nil
    self.select_part_data = nil
    
    if self.suit_cell_group then
        for k, v in pairs(self.suit_cell_group) do
            for k1, v1 in pairs(v) do
                v1:DeleteMe()
            end
        end

        self.suit_cell_group = nil
    end

    if self.big_type_toggle_list ~= nil then
        for k, v in ipairs(self.big_type_toggle_list) do
            v:DeleteMe()
        end
        self.big_type_toggle_list = nil
    end

    if self.part_cell_list then
		for k, v in pairs(self.part_cell_list) do
			v:DeleteMe()
		end
		self.part_cell_list = nil
	end

    if self.part_model_display then
        self.part_model_display:DeleteMe()
        self.part_model_display = nil
	end

    if self.attr_item_list then
		for k,v in pairs(self.attr_item_list) do
            if v.cell then
			    v.cell:DeleteMe()
            end
		end
		self.attr_item_list = nil
	end
end

function HuanHuaFetterView:InfoShowIndexCallBack()
    if self.load_cell_complete then
        self.select_big_type = nil
        self.select_small_type = nil

        for i = 1, TOGGLE_MAX_COUNT do
            self.big_type_toggle_list[i]:SetAccordionElementState(false)
        end
    end
end

function HuanHuaFetterView:CreatToggleList()
   -- print_error("---创建标签---")
    if nil ~= self.accordion_list then
        return
    end

    self.suit_cell_group = {}
    self.accordion_list = {}
    self.big_type_toggle_list = {}
    self.load_cell_complete = false
    local content_node = self.node_list["toggle_content"]
    for i = 1, TOGGLE_MAX_COUNT do
        self.accordion_list[i] = content_node:FindObj("List" .. i)
        local big_btn_cell = FetterBigTypeToggleRender.New(content_node:FindObj("SelectBtn" .. i))
        big_btn_cell:SetIndex(i)
        big_btn_cell:SetOnlyClickCallBack(BindTool.Bind(self.OnClickBigTypeToggle, self))
        self.big_type_toggle_list[i] = big_btn_cell

        self:LoadSuitCellList(i, TOGGLE_MAX_COUNT, LIST_CELL_COUNT)
    end
end

-- 加载子标签
function HuanHuaFetterView:LoadSuitCellList(index, big_type_num, suit_num)
    -- print_error("---加载子标签---", index)
    local res_async_loader = AllocResAsyncLoader(self, "hh_fetter_item" .. index)
	res_async_loader:Load("uis/view/huanhua_fetter_ui_prefab", "fetter_toggle_cell", nil, function(new_obj)
		local item_vo_list = {}
		for i = 1, suit_num do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.accordion_list[index].transform, false)

			local item_render = FetterSuitToggleRender.New(obj)
            item_render:SetClickCallBack(BindTool.Bind(self.OnClickSuitToggle, self))
			item_vo_list[i] = item_render
			if index == big_type_num and i == suit_num then
				self.load_cell_complete = true
			end
		end

		self.suit_cell_group[index] = item_vo_list
		if self.load_cell_complete then
            -- 设置数据
            self:FlushToggleAllData()

            -- 加载完 是否要选择标签
            if self.flush_wait_flag then
                self:SelectToggle()
            end
		end
	end)
end

-- 标签选择
function HuanHuaFetterView:SelectToggle(force_big_type, force_suit_type, stop_cur)
    --print_error("---标签选择---", force_big_type, force_suit_type)
    if force_big_type then
        self.force_big_type = force_big_type
    end

    if force_suit_type then
        self.force_suit_type = force_suit_type
    end

    if not self.load_cell_complete then
        return
    else
        self.flush_wait_flag = nil
    end

    local jump_index
    local open_jump_index
    if self.force_big_type then
        jump_index = self.force_big_type
        self.force_big_type = nil
    elseif stop_cur then
        self.stop_cur = true
        jump_index = self.select_big_type
    else
        local toggle_list = HuanHuaFetterWGData.Instance:GetToggleList(math.floor(self.show_index / 10))
        for k,v in ipairs(toggle_list) do
            if v.remind_num > 0 then                           -- 跳红点
                jump_index = k
                break
            end

            if not open_jump_index and v.open_toggle_num > 0 then                           -- 跳红点
                open_jump_index = k
            end
        end
    end

    jump_index = jump_index or open_jump_index or 1
    local suit_type = self.select_suit_data and self.select_suit_data.type or -1
    if self.select_big_type == jump_index and suit_type == force_suit_type then
        self:FlushPanelView()
        return
    end

    if self.select_big_type ~= jump_index then
        self.big_type_toggle_list[jump_index]:SetAccordionElementState(true)
    else
        self:OnClickBigTypeToggle(self.big_type_toggle_list[jump_index])
    end
end
-- 大标签回调
function HuanHuaFetterView:OnClickBigTypeToggle(cell, is_on)
    --print_error("【----点击 大 回调-----】：", cell:GetIndex(), is_on)
	if cell == nil then
		return
	end

    local index = cell:GetIndex()
    local data = cell:GetData()
    if data == nil then
        return
    end

    self.select_big_type = index

    local jump_small_index
    local open_jump_small_index
    if self.force_suit_type then
        jump_small_index = self.force_suit_type
        self.force_suit_type = nil
        self.stop_cur = nil
    elseif self.stop_cur then
        jump_small_index = self.select_small_type
       self.stop_cur = nil
    else
        for k,v in ipairs(data.child_list) do
            local info = v.info
            if info and info.can_act and v.can_open then                               -- 跳红点
                jump_small_index = k
                break
            end

            if not open_jump_small_index and v.can_open then
                open_jump_small_index = k
            end
        end
    end

    jump_small_index = jump_small_index or open_jump_small_index or 1
    local small_type_cell = ((self.suit_cell_group or {})[index] or {})[jump_small_index]
	if small_type_cell then
        small_type_cell:OnClick()
	end
end

-- 子标签回调
function HuanHuaFetterView:OnClickSuitToggle(cell)
    --print_error("【----点击 子 回调-----】：", cell:GetData().type, cell:GetData().name)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

    self.select_suit_data = data
    local type = data.type
    self.select_small_type = type
    local list = self.suit_cell_group[self.select_big_type]
    if list then
        for k,v in pairs(list) do
            v:OnSelectSuitChange(type)
        end
    end

    self:FlushPartList()

    local jump_part_index = 0
    local cell_data = self.part_cell_list[jump_part_index]:GetData()
    if cell_data ~= self.select_part_data then
        self.part_cell_list[jump_part_index]:OnClickCellBtn()
    end
end

function HuanHuaFetterView:OnSelectPartCallBack(cell)
    --print_error("【----点击部位回调-----】：", cell:GetIndex())
    if cell == nil then
        return
    end

    local index = cell:GetIndex()
    local data = cell:GetData()
    if index == self.select_part_index and self.select_part_data == data then
        if self.select_part_data and self.select_part_data.show_item_id > 0  then
            TipWGCtrl.Instance:OpenItem({item_id = self.select_part_data.show_item_id})
        end
        return
    end

    self.select_part_index = index
    self.select_part_data = data
    for k,v in pairs(self.part_cell_list) do
        v:FlushSelectHL(k == index)
    end

    self:FlushPanelView()
end

-- 刷新标签数据
function HuanHuaFetterView:FlushToggleAllData()
    --print_error("---刷新标签数据---")
    if IsEmptyTable(self.big_type_toggle_list) then
        return
    end

    if IsEmptyTable(self.suit_cell_group) then
        return
    end

    local toggle_list = HuanHuaFetterWGData.Instance:GetToggleList(math.floor(self.show_index / 10))
    for k,v in ipairs(self.big_type_toggle_list) do
        local can_open = HuanHuaFetterWGData.Instance:GetTabbarVerOrToggleCanOpen(self.show_index, k)
        v:SetCanOpen(can_open)
        v:SetData(toggle_list[k])
        local suit_list_data = toggle_list[k] and toggle_list[k].child_list or {}
        local suit_list = self.suit_cell_group[k]
        if not IsEmptyTable(suit_list) then
            for k1,v1 in ipairs(suit_list) do
                v1:SetData(suit_list_data[k1])
            end
        end
    end
end

-- 刷新全部
function HuanHuaFetterView:FlushInfoView()
    self:FlushToggleAllData()
    self:FlushPanelView()
    self:FlushPartList()
end

function HuanHuaFetterView:FlushPanelView()
    self:FlushPartModel()
    self:FlushAttrList()
end

function HuanHuaFetterView:FlushPartList()
    if self.select_suit_data == nil then
        return
    end

    local info = self.select_suit_data.info
    for k,v in pairs(self.part_cell_list) do
        v:SetData(info.part_list[k])
    end

    local act_btn = info.act_part_num < info.total_part_num
    self.node_list.btn_act:SetActive(act_btn)
    XUI.SetButtonEnabled(self.node_list.btn_act, info.can_act)
    self.node_list.act_remind:SetActive(info.can_act)
end

function HuanHuaFetterView:FlushPartModel()
	if not self.select_part_data then
		return
	end

	local item_data = self.select_part_data

	if item_data.show_item_id ~= 0 then
		local display_data = {}
		display_data.should_ani = true
		if item_data.show_item_id ~= 0 then
			display_data.item_id = item_data.show_item_id
		end

	    local model_show_type = 1
	    display_data.render_type = model_show_type - 1
	    self.part_model_display:SetData(display_data)
	end
end

function HuanHuaFetterView:FlushAttrList()
    if self.select_suit_data == nil then
        return
    end

    local item_data = self.select_suit_data

	local attr_data = HuanHuaFetterWGData.Instance:GetAttrBySuit(item_data.suit)

	for i,v in ipairs(attr_data) do
		if self.attr_item_list[i] then
			if self.attr_item_list[i].loaded_flag then
				self.attr_item_list[i].cell:SetData(v)
			end
		else
			local async_loader = AllocAsyncLoader(self, "hh_fetter_attr" .. i)
			self.attr_item_list[i] = {}
			self.attr_item_list[i].loaded_flag = false
			async_loader:SetParent(self.node_list["attr_list"].transform)
			async_loader:Load("uis/view/huanhua_fetter_ui_prefab", "fetter_attr_cell", function (obj)
				local cell = FetterAttrRender.New(obj)
				cell:SetData(v)
				self.attr_item_list[i].cell = cell
				self.attr_item_list[i].loaded_flag = true
			end)
		end
	end

	local active_num = #attr_data
	for i,v in ipairs(self.attr_item_list) do
		if v.loaded_flag then
			v.cell:SetActive(i <= active_num)
		end
	end
end

function HuanHuaFetterView:OnClickAct()
	if self.select_suit_data == nil then
        return
    end

    local item_data = self.select_suit_data.info
	local part_list = item_data.part_list
	if item_data.can_act then --判断激活下一套装属性
		local need_num = item_data.suit_less_need - item_data.act_part_num
		for k,v in pairs(part_list) do
			if v.state == REWARD_STATE_TYPE.CAN_FETCH then
				HuanHuaFetterWGCtrl.Instance:SendHuanHuaFetterRequest(HUANHUA_FETTER_OPERATE_TYPE.ACTIVE_PART, item_data.suit, v.part)
				need_num = need_num - 1
				if need_num == 0 then
					break
				end
			end
		end
	end
end

function HuanHuaFetterView:DoActiveEffect()
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0),
                                parent_node = self.node_list["effect_root"]})
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

--======================================================================
-- 大类型toggle
--======================================================================
FetterBigTypeToggleRender = FetterBigTypeToggleRender or BaseClass(BaseRender)
function FetterBigTypeToggleRender:__init()
    self.view.accordion_element:AddClickListener(BindTool.Bind(self.OnClickAccordion, self))
end

function FetterBigTypeToggleRender:__delete()
    self.cur_open_num = nil
end

function FetterBigTypeToggleRender:SetOnlyClickCallBack(callback)
    self.click_callback = callback
end

function FetterBigTypeToggleRender:OnClickAccordion(isOn)
	if nil ~= self.click_callback then
		self.click_callback(self, isOn)
	end
end

function FetterBigTypeToggleRender:SetAccordionElementState(is_on)
    self.view.accordion_element.isOn = is_on
end

function FetterBigTypeToggleRender:SetCanOpen(can_open)
    self.can_open = can_open
end

function FetterBigTypeToggleRender:OnFlush()
	if self.data == nil or not self.can_open then
        self.view:SetActive(false)
		return
	end

    self.node_list.normal_text.text.text = self.data.name
    self.node_list.hl_text.text.text = self.data.name
    self.node_list["remind"]:SetActive(self.data.remind_num > 0)
    self:FlushSuitToggleList()
    self.view:CustomSetActive(true)
end

function FetterBigTypeToggleRender:FlushSuitToggleList()
    local is_on = self.view.accordion_element.isOn
    if is_on and self.cur_open_num and self.cur_open_num ~= self.data.open_toggle_num then
        self:SetAccordionElementState(false)
        self:SetAccordionElementState(true)
    end

    self.cur_open_num = self.data.open_toggle_num
end

--======================================================================
-- 小类型toggle
--======================================================================
FetterSuitToggleRender = FetterSuitToggleRender or BaseClass(BaseRender)
function FetterSuitToggleRender:__init()
    if not self.show_item_cell then
        self.show_item_cell = ItemCell.New(self.node_list["show_item_cell"])
    end
end

function FetterSuitToggleRender:__delete()
    if self.show_item_cell then
        self.show_item_cell:DeleteMe()
        self.show_item_cell = nil
    end
end

function FetterSuitToggleRender:OnFlush()
	if self.data == nil then
        self.view:SetActive(false)
		return
	end

    if not self.data.can_open then
        self.view:SetActive(false)
		return
	end

    self.node_list["name"].text.text = self.data.name
    self.node_list["hl_name"].text.text = self.data.name
    self.view:CustomSetActive(true)
    local info = self.data.info
    self.node_list["remind"]:SetActive(info and info.can_act)

    local show_item_id = info.icon or 0
    self.show_item_cell:SetData({item_id = show_item_id})
    local real_act_num = info and info.real_act_num or 0

    self.node_list.no_active:CustomSetActive(real_act_num == 0)
    self.node_list.pro_num_text:CustomSetActive(real_act_num ~= 0)

    if real_act_num ~= 0 then
        local total_part_num = info and info.total_part_num or 0
        local color = total_part_num <= real_act_num and COLOR3B.D_GREEN or COLOR3B.D_PINK
        local str_num = ToColorStr(real_act_num .. "/" .. total_part_num, color)
        self.node_list.pro_num_text.text.text = string.format(Language.HuanHuaFetter.ActNum, str_num)
    end
end

function FetterSuitToggleRender:OnSelectSuitChange(suit_type)
    if self.data == nil then
        return
    end

    local is_select = suit_type == self.data.type
    self.node_list["normal_bg"]:SetActive(not is_select)
    self.node_list["select_bg"]:SetActive(is_select)
end

--======================================================================
-- 部位格子
--======================================================================
FetterPartTypeCell = FetterPartTypeCell or BaseClass(BaseRender)

function FetterPartTypeCell:__init()
    XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClickCellBtn, self))
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["cell_pos"])
    end
end

function FetterPartTypeCell:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    self.item_click_callback = nil
end

function FetterPartTypeCell:OnFlush()
    if self.data == nil then
        self.item_cell:SetData({})
        self.node_list.name.text.text = ""
        return
    end

    if self.data.show_item_id then
        -- self.item_cell:SetData({item_id = self.data.show_item_id})
        --self.item_cell:SetShowCualityBg(false)
        self.item_cell:SetIsUseRoundQualityBg(true)
        self.item_cell:SetData({item_id = self.data.show_item_id})
        self.item_cell:Nodes("item_icon").transform.localScale = Vector3(0.7, 0.7, 0.7)
        self.item_cell:SetEffectRootEnable(false)
        self.item_cell:SetCellBgEnabled(false)
        self.item_cell:SetBindIconVisible(false)

    	--self.item_cell:MakeGray(self.data.state == REWARD_STATE_TYPE.UNDONE)
    end

    self.node_list.lock_state:SetActive(self.data.state == REWARD_STATE_TYPE.UNDONE)
    self.node_list.name.text.text = Language.HuanHuaFetter.PartName[self.index] or ""
end

function FetterPartTypeCell:SetCellClickCallBack(call_back)
    self.item_click_callback = call_back
end

function FetterPartTypeCell:OnClickCellBtn()
    if self.item_click_callback then
        self.item_click_callback(self)
    end
end

function FetterPartTypeCell:FlushSelectHL(is_select)
    self.node_list["select_hl"]:SetActive(is_select)
end

--======================================================================
-- 属性格子
--======================================================================
FetterAttrRender = FetterAttrRender or BaseClass(BaseRender)

function FetterAttrRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
	end

    local str_color = data.is_act and COLOR3B.DEFAULT or "#bbbfbf"
    local attr_color = data.is_act and COLOR3B.DEFAULT_NUM or "#bbbfbf"

    local need_str = string.format(Language.HuanHuaFetter.NeedNumAct, self.data.need_num)
    self.node_list.need_num.text.text = ToColorStr(need_str, str_color)
    local attr_list = data.attr_list[1]
    local attr_name = EquipmentWGData.Instance:GetAttrName(attr_list.attr_str)
    local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_list.attr_str)
    local per_desc = is_per and "%" or ""
    local value_str = is_per and attr_list.attr_value / 100 or attr_list.attr_value

    local attr_str = string.format(Language.HuanHuaFetter.AttrStr, attr_name, ToColorStr("+" .. value_str .. per_desc, attr_color))
    self.node_list.attr_value.text.text = ToColorStr(attr_str, str_color)
end