HolyBeastCallWGData = HolyBeastCallWGData or BaseClass()

function HolyBeastCallWGData:__init()
	if HolyBeastCallWGData.Instance then
		print_error("[HolyBeastCallWGData] Attempt to create singleton twice!")
		return
	end

	HolyBeastCallWGData.Instance = self

    self:InitParam()
    self:InitConfig()
end

function HolyBeastCallWGData:__delete()
    HolyBeastCallWGData.Instance = nil
end

function HolyBeastCallWGData:InitParam()
	self.draw_times = 0
	self.draw_limit = -1
	self.grade = 1
	self.is_acquire = 0
    self.rmb_num = 0
end

function HolyBeastCallWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("shengshou_draw_auto")
    self.rewards_cfg = ListToMapList(cfg.rewards, "grade")
    self.item_cost_cfg = cfg.item_cost
    self.item_random_desc_cfg = ListToMapList(cfg.item_random_desc, "grade")
    self.model_show_cfg = ListToMap(cfg.model_show, "grade")

    self.item_cache = {}
    for k, v in pairs(self.item_cost_cfg) do
        local item_id = v.cost_item_id
        if nil == self.item_cache[item_id] then
            self.item_cache[item_id] = item_id
        end
    end

    RemindManager.Instance:Register(RemindName.HolyBeastCall, BindTool.Bind(self.GetHolyBeastCallRemind, self))
end

function HolyBeastCallWGData:GetHolyBeastCallRemind()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HOLY_BEAST_CALL)

    if is_open then
        if not self:IsGetDailyRewardFlag() then
            return 1
        end

        if not self:IsHolyBeastCallDrawLimit() then
            local item_cost_cfg = self:GetItemCostCfg()
            local cur_data = item_cost_cfg[1] or {}

            if not IsEmptyTable(cur_data) then
                local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_data.cost_item_id)
                
                if cur_data.cost_item_num <= item_num then
                    return 1
                end
            end
        end
    end

    return 0
end

function HolyBeastCallWGData:SetShengShouDrawInfo(protocol)
    self.rmb_num = protocol.rmb_num
	self.draw_times = protocol.draw_times
	self.draw_limit = protocol.draw_limit
	self.grade = protocol.grade
	self.is_acquire = protocol.is_acquire
end

function HolyBeastCallWGData:GetItemCostCfg()
    return self.item_cost_cfg or {}
end

function HolyBeastCallWGData:IsGetDailyRewardFlag()
    return self.is_acquire == 1
end

function HolyBeastCallWGData:GetShengShowRmbNum()
    return self.rmb_num
end

function HolyBeastCallWGData:GetShowRewardItemList()
    local grade_cfg = self.rewards_cfg[self.grade] or {}
    local rare_list = {}
    local show_list =  {}

    if IsEmptyTable(grade_cfg) then
        return rare_list, show_list
    end

    for k, v in pairs(grade_cfg) do
        if v.is_show then
            table.insert(show_list, v)
        end

        if v.pos_id > 0 then
            rare_list[v.pos_id] = v
        end
    end

    return rare_list, show_list
end

function HolyBeastCallWGData:GetDrawTimeAndLimitTime()
    return 	self.draw_times, self.draw_limit
end

function HolyBeastCallWGData:GetItemRangeDesc()
    return self.item_random_desc_cfg[self.grade] or {}
end

function HolyBeastCallWGData:IsHolyBeastCallItem(change_item_id)
    return nil ~= self.item_cache[change_item_id]
end

function HolyBeastCallWGData:IsHolyBeastCallDrawLimit()
    return self.draw_limit <= self.draw_times
end

function HolyBeastCallWGData:GetCurGrade()
    return self.grade
end

function HolyBeastCallWGData:GetCurModelCfg()
    return self.model_show_cfg[self.grade] or {}
end