function BossNewPrivilegeView:BossMaBiLoadCallBack()
	self.boss_mabi_total_list = {}
	self:FlushBossMabiTabList()

	if not self.jump_list then
		self.jump_list = AsyncListView.New(BossMaBiJumpListItemRender, self.node_list.jump_group_list)
		self.jump_list:SetSelectCallBack(BindTool.Bind(self.OnBossMaBiTabClickItem, self))
		self.jump_list:SetDefaultSelectIndex(nil)
		--默认展开第一个.
		local tab_list = CommonSkillShowData.Instance:GetBossMaBiTabIndexList(1)
		self.jump_list:SetDataList(tab_list)
	end

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.list_fb_reward)

		local data_list = PersonMaBiBossWGData.Instance:GetRewardDataList()
		self.reward_list:SetDataList(data_list)
		self.reward_list:SetStartZeroIndex(true)
	end

	self.show_guild = true

	XUI.AddClickEventListener(self.node_list.btn_change, BindTool.Bind(self.OnClickChangeBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_enter_fb, BindTool.Bind(self.OnClickEnterFbBtn, self))

	if not self.attr_change then
		self.attr_change = BindTool.Bind(self.RoleDataChangeCallback, self)
		local attr_list = CommonSkillShowData.Instance:GetBossMaBiAttrNameList()
		if IsEmptyTable(attr_list) then
			return
		end
		RoleWGData.Instance:NotifyAttrChange(self.attr_change, attr_list)
	end

	local can_enter_fb_time = PersonMaBiBossWGData.Instance:GetCanEnterFbTime()
	self.node_list.desc_enter_fb.text.text = string.format(Language.PersonMabiBoss.EnterFbLimitTime, can_enter_fb_time)
end

function BossNewPrivilegeView:BossMaBiReleaseCallBack()
	if self.boss_mabi_total_list then
		for k, v in pairs(self.boss_mabi_total_list) do
			v:DeleteMe()
		end
		self.boss_mabi_total_list = nil
	end

	if self.jump_list then
		self.jump_list:DeleteMe()
		self.jump_list = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if self.attr_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
		self.attr_change = nil
	end

	self.render_texture = nil
	self.render_width = nil
	self.render_height = nil
end

function BossNewPrivilegeView:BossMaBiShowIndexCallBack()
	local can_enter_fb_flag = PersonMaBiBossWGData.Instance:GetCanEnterFBFlag()
	self.show_guild = not can_enter_fb_flag

	self.node_list.jump_group_list:CustomSetActive(self.show_guild)
	self.node_list.enter_fb_panel:CustomSetActive(can_enter_fb_flag)
end

function BossNewPrivilegeView:BossMaBiOnFlush(param_t, index)
	local can_enter_fb_flag = PersonMaBiBossWGData.Instance:GetCanEnterFBFlag()
	self.node_list.flag_experience:CustomSetActive(can_enter_fb_flag)
	self.node_list.btn_change:CustomSetActive(can_enter_fb_flag)

	if not can_enter_fb_flag then
		self.node_list.jump_group_list:CustomSetActive(true)
		self.node_list.enter_fb_panel:CustomSetActive(false)
	end
end

function BossNewPrivilegeView:OnBossMaBiClickItem(item)
	if not item.data then
		return
	end

	local tab_list = CommonSkillShowData.Instance:GetBossMaBiTabIndexList(item.data.index)
	self.jump_list:SetDataList(tab_list)

	for index, value in ipairs(self.boss_mabi_total_list) do
		value:OnSelectChange(item.data.index)
	end
end

function BossNewPrivilegeView:OnBossMaBiTabClickItem(item)
	if not item.data then
		return
	end

	local tab_cfg = CommonSkillShowData.Instance:GetBossMaBiTabCfg(tonumber(item.data))
	if IsEmptyTable(tab_cfg) then
		return
	end

	self:Close()
	FunOpen.Instance:OpenViewNameByCfg(tab_cfg.open_view)
end

function BossNewPrivilegeView:OnClickChangeBtn()
	self.show_guild = not self.show_guild
	self.node_list.jump_group_list:CustomSetActive(self.show_guild)
	self.node_list.enter_fb_panel:CustomSetActive(not self.show_guild)
end

function BossNewPrivilegeView:OnClickEnterFbBtn()
	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.PERSON_MABIBOSS_FB)
	self:Close()
end

function BossNewPrivilegeView:RoleDataChangeCallback(key, value)
	local attr_list = CommonSkillShowData.Instance:GetBossMaBiAttrNameList()
	if IsEmptyTable(attr_list) then
		return
	end

	for k, v in ipairs(attr_list) do
		if key == v then
			if self.boss_mabi_total_list[k] then
				self.boss_mabi_total_list[k]:FlushAttr()
			end
		end
	end
end

function BossNewPrivilegeView:FlushBossMabiTabList()
	local list_data = CommonSkillShowData.Instance:GetBossMaBiSkillSHowCfg()
	for i, v in ipairs(list_data) do
		if self.boss_mabi_total_list[i] then
			self.boss_mabi_total_list[i]:SetData(v)
		else
			local async_loader = AllocAsyncLoader(self, "boss_mabi_cell" .. i)
			async_loader:SetParent(self.node_list["boss_mabi_list"].transform)
			async_loader:Load("uis/view/boss_ui_prefab", "boss_mabi_cell", function(obj)
				local cell = BossMaBiGuildListItemRender.New(obj)
				cell:SetClickBtnCallBack(BindTool.Bind(self.OnBossMaBiClickItem, self))
				cell:SetData(v)
				--默认展开第一个.
				cell:OnSelectChange(1)
				self.boss_mabi_total_list[i] = cell
			end)
		end
	end
end

-----------------------------BossMaBiGuildListItemRender------------------------------
BossMaBiGuildListItemRender = BossMaBiGuildListItemRender or BaseClass(BaseRender)

function BossMaBiGuildListItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.ClickBtn, self))
end

function BossMaBiGuildListItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.title_text.text.text = self.data.title or ""
	self.node_list.attr_desc_text.text.text = self.data.attr_desc or ""
	self.node_list.desc_text.text.text = self.data.desc or ""

	self:FlushAttr()
end

function BossMaBiGuildListItemRender:OnSelectChange(select_idx)
	local is_select = select_idx == self.data.index
	self.node_list.sub_panel:SetActive(is_select)
	self.node_list.arrow_down:SetActive(is_select)
	self.node_list.arrow_up:SetActive(not is_select)
end

function BossMaBiGuildListItemRender:FlushAttr()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if not main_role_vo then
		return
	end

	if nil == self.data.attr_name or "" == self.data.attr_name then
		self.node_list.attr_icon:SetActive(false)
		self.node_list.attr_text.text.text = ""
		return
	end

	local is_show_lv_icon = false

	local attr_value = main_role_vo[self.data.attr_name] or 0
	if self.data.attr_name == "level" then
		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(attr_value)
		attr_value = role_level

		is_show_lv_icon = is_vis
	end
	self.node_list.attr_icon:SetActive(is_show_lv_icon)

	if self.node_list.attr_text then
		local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(self.data.attr_name)
		local can_tiyan = PersonMaBiBossWGData.Instance:GetCanEnterFBFlag()
		local value = is_per and attr_value / 100 or attr_value
		local per_desc = is_per and "%" or ""
		local cur_desc = value .. per_desc
		local target_value_str = can_tiyan and cur_desc .. "+100%" or cur_desc
		self.node_list.attr_text.text.text = target_value_str
	end
end

function BossMaBiGuildListItemRender:ClickBtn()
	if self.click_callback then
		self.click_callback(self)
	end
end

function BossMaBiGuildListItemRender:SetClickBtnCallBack(callback)
	self.click_callback = callback
end

-----------------------------BossMaBiJumpListItemRender------------------------------
BossMaBiJumpListItemRender = BossMaBiJumpListItemRender or BaseClass(BaseRender)

function BossMaBiJumpListItemRender:LoadCallBack()
	self.view:SetActive(true)
end

function BossMaBiJumpListItemRender:OnFlush()
	if not self.data then
		return
	end

	local tab_cfg = CommonSkillShowData.Instance:GetBossMaBiTabCfg(tonumber(self.data))
	if IsEmptyTable(tab_cfg) then
		return
	end
	local bundle, asset = ResPath.GetSkillShowImg("a3_bossmb_icon_" .. tab_cfg.icon)
	self.node_list.icon_image.image:LoadSprite(bundle, asset, function()
		self.node_list.icon_image.image:SetNativeSize()
	end)

	self.node_list.title_text.text.text = tab_cfg.title or ""
end
