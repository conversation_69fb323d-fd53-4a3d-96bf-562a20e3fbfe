MergeZhaoCaiMiaoMiaoRecordPanel = MergeZhaoCaiMiaoMiaoRecordPanel or BaseClass(SafeBaseView)

function MergeZhaoCaiMiaoMiaoRecordPanel:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(852, 410)})
	self:AddViewResource(0, "uis/view/merge_activity_ui/zhaocai_miaomiao_prefab", "layout_zc_miaomiao_record")
end

function MergeZhaoCaiMiaoMiaoRecordPanel:LoadIndexCallBack()
	-- body
end

function MergeZhaoCaiMiaoMiaoRecordPanel:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.MergeActivity.MiaoMiaoTitleView
end

function MergeZhaoCaiMiaoMiaoRecordPanel:OnFlush()
	if nil == self.record_list then
		self.record_list = AsyncListView.New(MergeMiaoMiaoRecordRender, self.node_list.record_list)
	end
	local data_list = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoRecord()
	table.sort(data_list, SortTools.KeyUpperSorter("record_time"))
	self.record_list:SetDataList(data_list)
	self.node_list.no_flag:SetActive(#data_list <= 0)
end

function MergeZhaoCaiMiaoMiaoRecordPanel:ShowIndexCallBack()

end

function MergeZhaoCaiMiaoMiaoRecordPanel:ReleaseCallBack()
	if self.record_list then
		self.record_list:DeleteMe()
		self.record_list = nil
	end
end


MergeMiaoMiaoRecordRender = MergeMiaoMiaoRecordRender or BaseClass(BaseRender)
function MergeMiaoMiaoRecordRender:LoadCallBack()
	
end

function MergeMiaoMiaoRecordRender:OnFlush()
	if not self.data then
        return
    end

    self.node_list['time'].text.text = TimeUtil.FormatYMDHMS(self.data.record_time)
    local act_name = MergeZhaoCaiMiaoWGData.Instance:GetActName()
    local interface_cfg = MergeZhaoCaiMiaoWGData.Instance:GetPanelUiInterface()
    local str = string.format(interface_cfg.pic_21, act_name, self.data.xianyu)
    self.node_list["info"].text.text = str
end