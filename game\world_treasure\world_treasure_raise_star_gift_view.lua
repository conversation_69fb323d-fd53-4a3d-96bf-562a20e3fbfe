local IS_BUY_RMB_FLAG = 1
function WorldTreasureView:ReleaseCallBack_RaiseStarGift()
    if self.rs_reward_list then
        self.rs_reward_list:DeleteMe()
        self.rs_reward_list = nil
    end

    if self.rs_model_display then
		self.rs_model_display:DeleteMe()
		self.rs_model_display = nil
	end
end

function WorldTreasureView:LoadIndexCallBack_RaiseStarGift()
    if not self.rs_reward_list then
        self.rs_reward_list = AsyncListView.New(WorldTreasureRaiseStarGiftRender, self.node_list.rs_reward_list)
        self.rs_reward_list:SetStartZeroIndex(true)
    end

    if not self.rs_model_display then
		self.rs_model_display = OperationActRender.New(self.node_list.rs_display_model)
		self.rs_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    XUI.AddClickEventListener(self.node_list.rs_btn_goto_func, BindTool.Bind1(self.OnClickRSGotoFunc, self))
    XUI.AddClickEventListener(self.node_list.rs_btn_buy, BindTool.Bind1(self.OnClickRSBuyPrivilege, self))
end

function WorldTreasureView:ShowIndexCallBack_RaiseStarGift()
    self:DoRaiseStarCellsAnim()
end

function WorldTreasureView:OnFlush_RaiseStarGift(param_t, index)    
    self:UpdateRaiseStarGiftView()
end

function WorldTreasureView:UpdateRaiseStarGiftView()
    local grade_cfg = WorldTreasureWGData.Instance:GetUpstarGiftGradeCfg()
    if not grade_cfg then
        return
    end

    local bundle, asset = ResPath.GetRawImagesPNG("a3_hs_sxlz_text0" .. grade_cfg.img_id)
    self.node_list["rs_title_img"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["rs_title_img"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetRawImagesPNG("a3_sxlz_btdb_" .. grade_cfg.img_id)
    self.node_list["rs_title_bg"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["rs_title_bg"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetWorldTreasureImg("a3_sxlz_sm_" .. grade_cfg.img_id)
    self.node_list["rs_desc_bg"].image:LoadSprite(bundle, asset, function()
        self.node_list["rs_desc_bg"].image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetWorldTreasureImg("a3_sxlz_tips_btn_" .. grade_cfg.img_id)
    self.node_list["rs_btn_tips"].image:LoadSprite(bundle, asset, function()
        self.node_list["rs_btn_tips"].image:SetNativeSize()
    end)

    local data_list = WorldTreasureWGData.Instance:GetUpstarGiftCfg()
    self.rs_reward_list:SetDataList(data_list)

    TryDelayCall(self, function()
        local index = WorldTreasureWGData.Instance:GetJumpRewardIndex()
        self.rs_reward_list:JumpToIndex(index, 3)
    end, 0.5, "delay_show_rs_list")


    -- spine动画
    --[[ 暂时屏蔽，改为使用模型
	local bundle_name, asset_name = ResPath.GetWorldTreasureSpine(grade_cfg.spine_id)
	self.node_list["yushou_spine"]:ChangeAsset(bundle_name, asset_name)

    local scale = grade_cfg.scale
    if scale and scale ~= "" then
        Transform.SetLocalScaleXYZ(self.node_list.yushou_spine.transform, scale, scale, scale)
	end
    ]]
    
    local show_buy_str = ''
    local buy_flag = WorldTreasureWGData.Instance:GetUpstarGiftRmbBuyFlag()
    self.node_list.rs_btn_buy:SetActive(buy_flag ~= IS_BUY_RMB_FLAG)
    self.node_list.rs_pay_lock:SetActive(buy_flag ~= IS_BUY_RMB_FLAG)

    if buy_flag ~= IS_BUY_RMB_FLAG then
        local buy_str = RoleWGData.GetPayMoneyStr(grade_cfg.price, grade_cfg.rmb_type, grade_cfg.rmb_seq)
        show_buy_str = string.format(Language.WorldTreasure.RaiseStarGift_RMBBuy, buy_str)
    end
    self.node_list.rs_btn_buy_txt.text.text = show_buy_str


    self:SetRaiseStarGiftModelShow()
end

function WorldTreasureView:SetRaiseStarGiftModelShow()
	local model_cfg = WorldTreasureWGData.Instance:GetUpstarGiftGradeCfg()
	local display_data = {}
	display_data.item_id = model_cfg.model_show_itemid
	display_data.should_ani = true
	display_data.render_type = model_cfg.model_show_type and model_cfg.model_show_type - 1 or 0
	display_data.bundle_name = model_cfg.model_bundle_name
	display_data.asset_name = model_cfg.model_asset_name
	display_data.model_rt_type = ModelRTSCaleType.XL

	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local position_tab = string.split(model_cfg.display_pos, "|")
		local rotation_tab = string.split(model_cfg.display_rotation, "|")
		local vector_pos = Vector3(position_tab[1], position_tab[2], position_tab[3])
		local vector_rot = Vector3(rotation_tab[1], rotation_tab[2], rotation_tab[3])
		display_data.model_adjust_root_local_position = vector_pos or Vector3(0, 0, 0)
		display_data.model_adjust_root_local_rotation = vector_rot or Vector3(0, 0, 0)
		local scale = model_cfg.display_scale or 1
		display_data.model_adjust_root_local_scale = Vector3(scale, scale, scale)
	end
	self.rs_model_display:SetData(display_data)
end

function WorldTreasureView:OnClickRSGotoFunc()
    ViewManager.Instance:Open(GuideModuleName.ControlBeastsPrizeDrawWGView, nil, "oa_act_draw_type_item")
end

function WorldTreasureView:OnClickRSBuyPrivilege()
    local buy_flag = WorldTreasureWGData.Instance:GetUpstarGiftRmbBuyFlag()
    local grade_cfg = WorldTreasureWGData.Instance:GetUpstarGiftGradeCfg()
    if not grade_cfg or buy_flag == IS_BUY_RMB_FLAG then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WorldTreasure.RaiseStarGift_RMBHadBuyHint)
        return
    end
    RechargeWGCtrl.Instance:Recharge(grade_cfg.price, grade_cfg.rmb_type, grade_cfg.rmb_seq)
end

function WorldTreasureView:DoRaiseStarCellsAnim()
	local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender
	self.node_list["rs_reward_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["rs_reward_list"]:SetActive(true)
        local list =  self.rs_reward_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyItemAnim(count)
        end
    end, tween_info.DelayDoTime, "raise_star_cell_tween")
end

------------------------------------------WorldTreasureRaiseStarGiftRender-------------------------------------------
WorldTreasureRaiseStarGiftRender = WorldTreasureRaiseStarGiftRender or BaseClass(BaseRender)
function WorldTreasureRaiseStarGiftRender:__init()
end

function WorldTreasureRaiseStarGiftRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_get_nor_reward, BindTool.Bind1(self.OnClickGetNormalReward, self))
    XUI.AddClickEventListener(self.node_list.btn_get_high_reward, BindTool.Bind1(self.OnClickGetHighReward, self))

    XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind1(self.OnClickGoToFunc, self))
    XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind1(self.OnClickGetReward, self))

    -- XUI.AddClickEventListener(self.node_list.btn_buy1, BindTool.Bind1(self.OnClickBuy, self))
    -- XUI.AddClickEventListener(self.node_list.btn_buy2, BindTool.Bind1(self.OnClickBuy, self))

    -- self.normal_item = ItemCell.New(self.node_list.normal_item)

    self.normal_item_list = AsyncListView.New(ItemCell, self.node_list.normal_item_list)
    self.normal_item_list:SetStartZeroIndex(true)

    self.high_item_list = AsyncListView.New(ItemCell, self.node_list.high_item_list)
    self.high_item_list:SetStartZeroIndex(true)
end

function WorldTreasureRaiseStarGiftRender:__delete()
    -- if self.normal_item then
    --     self.normal_item:DeleteMe()
    --     self.normal_item = nil
    -- end

    if self.normal_item_list then
        self.normal_item_list:DeleteMe()
        self.normal_item_list = nil
    end

    if self.high_item_list then
        self.high_item_list:DeleteMe()
        self.high_item_list = nil
    end
end


function WorldTreasureRaiseStarGiftRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    -- 0: 不可领取 1: 可领取 2: 已领取
    local free_reward_state = WorldTreasureWGData.Instance:GetUpstarGiftFreeRewardState(self.data.seq)
    -- 0: 没领取 1: 已领取
    local pay_reward_flag = WorldTreasureWGData.Instance:GetUpstarGiftPayRewardFlag(self.data.seq)

    local buy_flag = WorldTreasureWGData.Instance:GetUpstarGiftRmbBuyFlag()
    local grade_cfg = WorldTreasureWGData.Instance:GetUpstarGiftGradeCfg()

    self.node_list.condition_desc.text.text = string.format(Language.WorldTreasure.TargetDesc, self.data.target)

    local is_all_get = free_reward_state == 2
    local is_can_get = free_reward_state == 1
    if buy_flag == IS_BUY_RMB_FLAG then
        is_all_get = free_reward_state == 2 and pay_reward_flag == 1
        is_can_get = free_reward_state == 1 or (pay_reward_flag == 0 and free_reward_state > 0)
    else
        is_all_get = false
    end

    local goto_show_str = Language.WorldTreasure.RaiseStarGift_GotoHint1
    if buy_flag ~= IS_BUY_RMB_FLAG and free_reward_state == 2 then
        local buy_str = RoleWGData.GetPayMoneyStr(grade_cfg.price, grade_cfg.rmb_type, grade_cfg.rmb_seq)
        goto_show_str = string.format(Language.WorldTreasure.RaiseStarGift_RMBBuy, buy_str)
    end
    self.node_list.goto_txt.text.text = goto_show_str

    local is_show_goto_btn = true
    if not is_can_get then
        is_show_goto_btn = not is_all_get
    else
        is_show_goto_btn = false
    end
    self.node_list.btn_goto:SetActive(is_show_goto_btn)
    self.node_list.btn_get:SetActive(is_can_get)


    -- self.node_list.normal_item_red:SetActive(free_reward_state == 1)
    self.node_list.btn_get_nor_reward:SetActive(free_reward_state == 1)
    self.node_list.btn_get_high_reward:SetActive(pay_reward_flag == 0 and buy_flag == IS_BUY_RMB_FLAG and free_reward_state > 0)

 
    self.node_list.buy_flag:SetActive(is_all_get)


    -- self.normal_item:SetFlushCallBack(function ()
	-- 	self.normal_item:ResetSelectEffect()
	-- 	self.normal_item:SetLingQuVisible(free_reward_state == 2)
	-- 	self.normal_item:SetRedPointEff(free_reward_state == 1)
	-- end)
    -- self.normal_item:SetData(self.data.free_reward[0])

    self.normal_item_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:ResetSelectEffect()
            item_cell:SetLingQuVisible(free_reward_state == 2)
            item_cell:SetRedPointEff(free_reward_state == 1)
        end
    end)
    self.normal_item_list:SetDataList(self.data.free_reward)

    self.high_item_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:ResetSelectEffect()
            item_cell:SetRedPointEff(free_reward_state > 0 and pay_reward_flag == 0 and buy_flag == IS_BUY_RMB_FLAG)
            item_cell:SetLingQuVisible(pay_reward_flag == 1)
        end
    end)
    self.high_item_list:SetDataList(self.data.pay_reward)

    -- if self.data.money_type == 1 then
    --     self.node_list.btn_buy2:SetActive(pay_reward_flag == 0)
    --     self.node_list.btn_buy1:SetActive(false)
    --     --XUI.SetButtonEnabled(self.node_list.btn_buy2, free_reward_state ~= 0)

    --     local btn_txt = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    --     self.node_list.price_text2.text.text = btn_txt
    -- else
    --     self.node_list.btn_buy2:SetActive(false)
    --     self.node_list.btn_buy1:SetActive(pay_reward_flag == 0)
    --     --XUI.SetButtonEnabled(self.node_list.btn_buy1, free_reward_state ~= 0)

    --     self.node_list.price_text1.text.text = self.data.price
    -- end

    -- self.node_list["buy_lock"]:SetActive(free_reward_state == 0)

    local grade_cfg = WorldTreasureWGData.Instance:GetUpstarGiftGradeCfg()
    if grade_cfg then
        local bundle, asset = ResPath.GetRawImagesPNG("a3_sxlz_task_djdb_" .. grade_cfg.img_id)
        self.node_list["bg"].raw_image:LoadSprite(bundle, asset, function()
            self.node_list["bg"].raw_image:SetNativeSize()
        end)
    end
end

function WorldTreasureRaiseStarGiftRender:OnClickGetNormalReward()
    if IsEmptyTable(self.data) then
        return
    end

    WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_UPSTAR_GIFT_FREE_REWARD, self.data.seq)
    -- local buy_flag = WorldTreasureWGData.Instance:GetUpstarGiftRmbBuyFlag()
    -- local pay_reward_flag = WorldTreasureWGData.Instance:GetUpstarGiftPayRewardFlag(self.data.seq)
    -- if buy_flag and pay_reward_flag == 0 then
    --     WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_UPSTAR_GIFT_PAY_REWARD, self.data.seq)
    -- end
end

function WorldTreasureRaiseStarGiftRender:OnClickGetHighReward()
    if IsEmptyTable(self.data) then
        return
    end
    WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_UPSTAR_GIFT_FREE_REWARD, self.data.seq)

    -- WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_UPSTAR_GIFT_PAY_REWARD, self.data.seq)
    -- local free_reward_state = WorldTreasureWGData.Instance:GetUpstarGiftFreeRewardState(self.data.seq)
    -- if free_reward_state == 1 then
    --     WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_UPSTAR_GIFT_FREE_REWARD, self.data.seq)
    -- end
end

function WorldTreasureRaiseStarGiftRender:OnClickGoToFunc()
    local buy_flag = WorldTreasureWGData.Instance:GetUpstarGiftRmbBuyFlag()
    local pay_reward_flag = WorldTreasureWGData.Instance:GetUpstarGiftPayRewardFlag(self.data.seq)
    local free_reward_state = WorldTreasureWGData.Instance:GetUpstarGiftFreeRewardState(self.data.seq)

    if buy_flag ~= IS_BUY_RMB_FLAG and free_reward_state == 2 then
        local buy_flag = WorldTreasureWGData.Instance:GetUpstarGiftRmbBuyFlag()
        local grade_cfg = WorldTreasureWGData.Instance:GetUpstarGiftGradeCfg()
        if not grade_cfg or buy_flag == IS_BUY_RMB_FLAG then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.WorldTreasure.RaiseStarGift_RMBHadBuyHint)
            return
        end
        RechargeWGCtrl.Instance:Recharge(grade_cfg.price, grade_cfg.rmb_type, grade_cfg.rmb_seq)
        return 
    end

    ViewManager.Instance:Open(GuideModuleName.ControlBeastsPrizeDrawWGView, nil, "oa_act_draw_type_item")
end

function WorldTreasureRaiseStarGiftRender:OnClickGetReward()
    local buy_flag = WorldTreasureWGData.Instance:GetUpstarGiftRmbBuyFlag()
    local pay_reward_flag = WorldTreasureWGData.Instance:GetUpstarGiftPayRewardFlag(self.data.seq)
    local free_reward_state = WorldTreasureWGData.Instance:GetUpstarGiftFreeRewardState(self.data.seq)

    if (free_reward_state > 0 and pay_reward_flag == 0 and buy_flag == IS_BUY_RMB_FLAG) or free_reward_state == 1 then
        WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_UPSTAR_GIFT_FREE_REWARD, self.data.seq)
    end
end

function WorldTreasureRaiseStarGiftRender:PalyItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.WorldTreasureView, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay * wait_index, "raise_star_item_" .. wait_index)
end

-- function WorldTreasureRaiseStarGiftRender:OnClickBuy()
--     if IsEmptyTable(self.data) then
--         return
--     end

--     local free_reward_state = WorldTreasureWGData.Instance:GetUpstarGiftFreeRewardState(self.data.seq)
--     if free_reward_state == 0 then
--         TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.Locked)
--         return
--     end

--     if self.data.money_type == 1 then
--         RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
--     else
--         local is_enough = RoleWGData.Instance:GetIsEnoughUseGold(self.data.price)
--         if is_enough then
--             WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_UPSTAR_GIFT_PAY_REWARD, self.data.seq)
--         else
--             VipWGCtrl.Instance:OpenTipNoGold()
--         end
--     end
-- end

