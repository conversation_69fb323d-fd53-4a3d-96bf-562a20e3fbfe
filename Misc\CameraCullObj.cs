﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Game;

public class CameraCullObjMgr : Nirvana.Singleton<CameraCullObjMgr>
{
    private float nextCheckCullTime = 0;
    private HashSet<CameraCullObj> cullObjs = new HashSet<CameraCullObj>();
    private bool isUpdating = false;

    public void OnGameStop()
    {
        cullObjs.Clear();
    }

    public void UpdateCull()
    {
        if (Time.time - this.nextCheckCullTime >= 0.2f)
        {
            this.nextCheckCullTime = Time.time;
            var iter = this.cullObjs.GetEnumerator();
            this.isUpdating = true;
            while (iter.MoveNext())
            {
                if (null != iter.Current)
                {
                    iter.Current.RefreshCullStatus();
                }
            }

            this.isUpdating = false;
        }
    }

    public void AddCullObj(CameraCullObj cullObj, string name = "")
    {
        if (this.isUpdating)
        {
            Debug.LogError("CameraCullObjMgr is updating!   " + name);
        }
        this.cullObjs.Add(cullObj);
    }

    public void RemoveCullObj(CameraCullObj cullObj)
    {
        this.cullObjs.Remove(cullObj);
    }
}

public class CameraCullObj : MonoBehaviour
{
    [SerializeField]
    private int cullDistance = 50;

    [SerializeField]
    private bool onlyCullDistance = false;  // 有些长形特效，如果还考虑摄象机方向，将很容易被裁掉

    private bool isCulled = false;
    private GameObjectAttach objAttach;

#if UNITY_EDITOR
    public int CullDistance
    {
        get { return cullDistance; }
        set { cullDistance = value; }
    }

    public bool IsOnlyCullDistance
    {
        get { return onlyCullDistance; }
        set { onlyCullDistance = value; }
    }
#endif

    private void Awake()
    {
        this.objAttach = this.GetComponent<GameObjectAttach>();
        if (null != this.objAttach)
        {
            this.objAttach.enabled = false;
        }
        else
        {
            this.gameObject.SetActive(false);
        }
        isCulled = true;

        CameraCullObjMgr.Instance.AddCullObj(this, this.gameObject.name);
    }

    private void OnDestroy()
    {
        CameraCullObjMgr.Instance.RemoveCullObj(this);
    }

    public void RefreshCullStatus()
    {
        if (null != Camera.main)
        {
            bool isCulled = false;
            var vector = Camera.main.transform.position - this.transform.position;
            bool isDistanceOver = vector.sqrMagnitude >= cullDistance * cullDistance;

            if (onlyCullDistance)
            {
                isCulled = isDistanceOver;
            }
            else
            {
                if (isDistanceOver || Vector3.Dot(vector, Camera.main.transform.forward) > 0)
                {
                    isCulled = true;
                }
            }

            this.SetIsCulled(isCulled);
        }
    }

    private void SetIsCulled(bool isCulled)
    {
        if (this.isCulled == isCulled)
        {
            return;
        }

        this.isCulled = isCulled;
        if (null != this.objAttach)
        {
            this.objAttach.enabled = !isCulled;
        }
        else
        {
            this.gameObject.SetActive(!isCulled);
        }
    }
}
