----------------HmGodPartTypeCell---------------
HmGodPartTypeCell = HmGodPartTypeCell or BaseClass(BaseRender)

function HmGodPartTypeCell:__init()
end

function HmGodPartTypeCell:LoadCallBack()

end

function HmGodPartTypeCell:__delete()

end

function HmGodPartTypeCell:OnFlush()
    if self.data == nil then
        return
    end

    local pos = self.data.state == REWARD_STATE_TYPE.UNDONE and Vector2(0, 0) or Vector2(5, 0)
    local image = self.data.state == REWARD_STATE_TYPE.UNDONE and "a2_snsy_tbk02" or "a2_snsy_tbk"
    local bundle, asset = ResPath.GetHmGodImg(image)
    self.node_list.cell_bg.image:LoadSprite(bundle, asset, function()
        self.node_list.cell_bg.image:SetNativeSize()
    end)
    
    image = self.data.state == REWARD_STATE_TYPE.UNDONE and "a2_snsy_suo_" or "a2_snsy_"
    bundle, asset = ResPath.GetHmGodImg(image .. self.data.icon_type)
    self.node_list.cell_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.cell_icon.image:SetNativeSize()
    end)

    self.node_list.suo:SetActive(self.data.state == REWARD_STATE_TYPE.UNDONE)
    self.node_list["cell_icon"].rect.anchoredPosition = pos
    local color = self.data.state == REWARD_STATE_TYPE.UNDONE and TIPS_COLOR.ATTR_NAME or COLOR3B.WHITE
    self.node_list.cell_name.text.text = ToColorStr(Language.HmGosView.IconTypeName[self.data.icon_type], color)
end

--
HmGodAttrRender = HmGodAttrRender or BaseClass(BaseRender)
function HmGodAttrRender:__init()
    self.attr_list = {}
    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
    end
end

function HmGodAttrRender:__delete()
    self.attr_list = nil
end

function HmGodAttrRender:SetFromItemTip(bool)
    self.is_from_itemtip = bool
end

function HmGodAttrRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
	end

    local need_color = TIPS_COLOR.GETWAY_VALUE
    local attri_name_color = COLOR3B.WHITE
    local attri_color = COLOR3B.WHITE
    if data.is_act then
        attri_color = self.is_from_itemtip and COLOR3B.DEFAULT_NUM or COLOR3B.DEFAULT_NUM
        need_color = self.is_from_itemtip and COLOR3B.WHITE or TIPS_COLOR.GETWAY_VALUE
        attri_name_color = self.is_from_itemtip and COLOR3B.WHITE or COLOR3B.WHITE
    else
        need_color = self.is_from_itemtip and TIPS_COLOR.ATTR_NAME or TIPS_COLOR.ATTR_NAME
        attri_color = self.is_from_itemtip and TIPS_COLOR.ATTR_NAME or TIPS_COLOR.ATTR_NAME
        attri_name_color = self.is_from_itemtip and TIPS_COLOR.ATTR_NAME or TIPS_COLOR.ATTR_NAME
    end

    local need_str
    if self.is_from_itemtip then
        if self.node_list.suit_icon then
            XUI.SetGraphicGrey(self.node_list.suit_icon, not data.is_act)
        end
        need_str = data.all_act_attr and Language.Wardrobe.AllActDesc1 or string.format(Language.Wardrobe.SuitNumCompany1, data.need_num)
    else
        need_str = data.all_act_attr and Language.Wardrobe.AllActDesc or string.format(Language.Wardrobe.SuitNumCompany, data.need_num)
    end

    self.node_list.need_num.text.text = ToColorStr(need_str, need_color)
    local list = data.attr_list

    for k, v in ipairs(self.attr_list) do
        if list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_type)
            local name = ""
            local attr_str = ""
            local value = ""
            if list[k].attr_type == "add_per" then
                name = Language.HmGosView.SpecialAttr
                value = is_per and list[k].value or list[k].value / 100 .. "%"
                
                local format_str = self.is_from_itemtip and "%s   %s" or "%s %s"
                if self.is_from_itemtip then
                    value = ToColorStr(value, attri_color)
                    name = ToColorStr(name, attri_name_color)     
                    attr_str = string.format(format_str, name, value)
                else
                    value = ToColorStr(value, attri_color) 
                    name = ToColorStr(name, attri_name_color)
                    attr_str = string.format(format_str, name, value)
                end
            else
                name = EquipmentWGData.Instance:GetAttrName(list[k].attr_type, false)
                value = is_per and list[k].value or list[k].value / 100 .. "%"
                
                if self.is_from_itemtip then 
                    name = ToColorStr(name, attri_name_color)
                    value = ToColorStr(value, attri_color)     
                    attr_str = string.format("%s   %s", name, value)
                elseif is_per then
                    value = ToColorStr(value, attri_color) 
                    name = ToColorStr(name, attri_name_color)
                    attr_str = string.format("%s         %s", name, value)
                else
                    value = ToColorStr(value, attri_color) 
                    name = ToColorStr(name, attri_name_color)
                    attr_str = string.format("%s     %s", name, value)
                end
            end

            v.text.text = attr_str--ToColorStr(attr_str, attri_color)
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end
end

--======================================================================
-- 大类型toggle
--======================================================================
HmGodBigTypeToggleRender = HmGodBigTypeToggleRender or BaseClass(BaseRender)
function HmGodBigTypeToggleRender:__init()
    --self.view.accordion_element:AddClickListener(BindTool.Bind(self.OnClickAccordion, self))
    self.view.accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickAccordion, self))
end

function HmGodBigTypeToggleRender:__delete()
end

function HmGodBigTypeToggleRender:SetOnlyClickCallBack(callback)
    self.click_callback = callback
end

function HmGodBigTypeToggleRender:OnClickAccordion(isOn)
	if nil ~= self.click_callback then
		self.click_callback(self, isOn)
	end
end

function HmGodBigTypeToggleRender:SetAccordionElementState(is_on)
    self.view.accordion_element.isOn = is_on
end

function HmGodBigTypeToggleRender:OnFlush()
	if self.data == nil then
        self.view:SetActive(false)
		return
	end

    self.view:CustomSetActive(true)
    local bundle, asset = ResPath.GetHmGodImg("a2_snsy_bg_" .. self.data.suit)
    self.node_list.normal_img.image:LoadSprite(bundle, asset, function()
        self.node_list.normal_img.image:SetNativeSize()
    end)

    local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.data.suit)
    if suit_data and self.node_list.remind then
        self.node_list.remind:SetActive(suit_data.can_act and self.data.state ~= REWARD_STATE_TYPE.UNDONE)
    end
end

function HmGodBigTypeToggleRender:CellSelect()
	self.node_list["select_img"]:SetActive(true)
end

function HmGodBigTypeToggleRender:CellCancelSelect()
	self.node_list["select_img"]:SetActive(false)
end

--======================================================================
-- 小类型toggle
--======================================================================
HmGodSuitToggleRender = HmGodSuitToggleRender or BaseClass(BaseRender)
function HmGodSuitToggleRender:__init()
    self.cell_list = {}
    for i = 1, 6 do
        self.cell_list[i] = BaseHmGodCell.New(self.node_list["base_god_cell_" .. i])
        self.cell_list[i]:SetIndex(i)
    end
end

function HmGodSuitToggleRender:__delete()
    if self.cell_list then
        for k, v in pairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = nil
    end
end


function HmGodSuitToggleRender:SetOnlyClickCallBack(callback)
    self.click_callback = callback
end

function HmGodSuitToggleRender:OnFlush()
	for i = 1, 6 do
		if self.data.part_list[i] then
			self.cell_list[i]:SetData(self.data.part_list[i])
			self.cell_list[i]:SetActive(true)
            self.cell_list[i]:SetClickCallBack(self.click_callback)
		else
			self.cell_list[i]:SetActive(false)
		end
	end
end

----------------BaseHmGodCell------------
BaseHmGodCell = BaseHmGodCell or BaseClass(BaseRender)

function BaseHmGodCell:__init()
end

function BaseHmGodCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClick, self))
    if self.node_list.cell_pos then
   		self.cell_item = ItemCell.New(self.node_list["cell_pos"])
    end
end

function BaseHmGodCell:__delete()
	if self.cell_item then
		self.cell_item:DeleteMe()
		self.cell_item = nil
	end
end

function BaseHmGodCell:OnFlush()
    if self.data == nil then
        return
    end

    if self.cell_item and self.data.show_item_id then
    	self.cell_item:SetData({item_id = self.data.show_item_id})
    	self.cell_item:MakeGray(self.data.state == REWARD_STATE_TYPE.UNDONE)
    end

    local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.data.suit_seq)
   	if suit_data and self.node_list.act_remind then
   		self.node_list.act_remind:SetActive(suit_data.can_act and self.data.state ~= REWARD_STATE_TYPE.UNDONE)
   	end
end

function HmGodSuitToggleRender:SetClickCallBack(callback)
    self.click_callback = callback
end

function BaseHmGodCell:OnClick()
    if nil ~= self.click_callback then
		self.click_callback(self)
	end
end

function BaseHmGodCell:CellSelect()
	self.node_list["select_hl"]:SetActive(true)
end

function BaseHmGodCell:CellCancelSelect()
	self.node_list["select_hl"]:SetActive(false)
end