HandleClickPoint = {}

--单向跳转（只允许普通场景跳转到其他场景，不允许其他场景跳转到普通场景或别的场景）
function HandleClickPoint.CheckSceneIdOperate(scene_id, params)
    local cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
    if cfg then
        local cur_scene_type = Scene.Instance:GetSceneType()
        local cur_scene_cfg = ConfigManager.Instance:GetSceneConfig(Scene.Instance:GetSceneId())
        local cur_scene_id = Scene.Instance:GetSceneId()
        local target_scene_type = cfg.scene_type
        if cur_scene_id ~= scene_id and target_scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
            ViewManager.Instance:Open(GuideModuleName.TreasureHunt, "treasurehunt_equip")
            return
        end

        if cur_scene_type == SceneType.WorldBoss or cur_scene_type == SceneType.VIP_BOSS then
            -- local has_demage = (BossAssistWGData.Instance:HasBossDamage() or BossAssistWGData.Instance:HasMainRoleHurt())
            -- local ok_func = function ()
            --     TaskWGCtrl.Instance:FlySceneToMove(tonumber(params[2]) or 0, tonumber(params[3]) or 0,  tonumber(params[4]) or 0)
            -- end
            -- if has_demage then
            --     local text_dec = Language.Boss.OutSceneTips
            --     TipWGCtrl.Instance:OpenConfirmAlertTips(text_dec, ok_func)
            --     return
            -- else
            --     ok_func()
            --     return
            -- end
        --跨服boss飞不同层跨服boss 
        elseif cur_scene_type == SceneType.KF_BOSS and cur_scene_type == target_scene_type and cur_scene_id ~= scene_id then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.CanNotEnterOther, cur_scene_cfg.name))
            return
        elseif cur_scene_type ~= SceneType.Common then
            if target_scene_type == SceneType.Common then --其他场景跳普通场景，直接返回
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.CanNotEnterOther, cur_scene_cfg.name))
                return
            elseif target_scene_type ~= SceneType.Common and cur_scene_type ~= target_scene_type then --其他场景跳其他场景，直接返回
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.CanNotEnterOther, cur_scene_cfg.name))
                return
            elseif cur_scene_id == scene_id then
                TaskWGCtrl.Instance:FlySceneToMove(tonumber(params[2]) or 0, tonumber(params[3]) or 0,  tonumber(params[4]) or 0, nil, params[5])
                return
            end
        end
        if target_scene_type == SceneType.Common then  														--普通场景
            if scene_id == 1003 then --策划需求主城坐标直接飞过去
                GuajiWGCtrl.Instance:FlyToScenePos(scene_id, tonumber(params[3]) , tonumber(params[4]), false, nil, params[5])
            else
                TaskWGCtrl.Instance:FlySceneToMove(tonumber(params[2]) or 0, tonumber(params[3]) or 0, tonumber(params[4]) or 0, nil, params[5])
            end
        elseif target_scene_type == SceneType.Fb_Welkin then                                                -- 铭纹阁
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_welkin")
        elseif target_scene_type == SceneType.COPPER_FB then                                                -- 龙王宝藏
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_copper")
        elseif target_scene_type == SceneType.PET_FB then                                                   -- 宠物本
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_pet")
        elseif target_scene_type == SceneType.FakePetFb then                                                -- 伪宠物本
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_pet")
        elseif target_scene_type == SceneType.DefenseFb then                                                -- 塔防
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_tafang")
        elseif target_scene_type == SceneType.Wujinjitan then                                               -- 炼狱洞窟
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_exp")
        elseif target_scene_type == SceneType.HIGH_TEAM_EQUIP_FB then                                       -- 远古仙殿
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_equip_high")
        elseif target_scene_type == SceneType.ZHUSHENTA_FB then                                             -- 诛神塔
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_zhushen_ta")
        elseif target_scene_type == SceneType.QingYuanFB then                                               -- 情缘副本
            ViewManager.Instance:Open(GuideModuleName.Marry, "fmarry_fb")
        elseif target_scene_type == SceneType.HunYanFb then                                                 -- 婚宴副本
            ViewManager.Instance:Open(GuideModuleName.Marry_DeMandView)
        elseif target_scene_type == SceneType.DEVILCOME_FB then                                             -- 魔王降临
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNoEnterFb)
        elseif target_scene_type == SceneType.GAINT_FB then                                                 -- 擎天玉柱
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNoEnterFb)
        elseif target_scene_type == SceneType.KUNLUNTRIAL_FB then                                           -- 昆仑试炼
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNoEnterFb)
        elseif target_scene_type == SceneType.DEMONS_FB then                                                -- 心魔副本
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNoEnterFb)
        elseif target_scene_type == SceneType.FBCT_NEWPLAYERFB then                                         -- 新手副本
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNoEnterFb)
        elseif target_scene_type == SceneType.ZHUANZHI_FB then                                              -- 转职副本
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNoEnterFb)
        elseif target_scene_type == SceneType.ZhuXie then  													--诛邪战场
            HandleClickPoint.SendActivityEnterReq(ACTIVITY_TYPE.ZHUXIE)
        elseif target_scene_type == SceneType.XianMengzhan then  											--仙盟争霸
            if HandleClickPoint.CheckNotHasGuild() then
                return
            end
            local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.XIANMENGZHAN)
            if not activity_info or activity_info.status == ACTIVITY_STATUS.CLOSE then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
                return
            end
            ViewManager.Instance:Open(GuideModuleName.Guild, FunName.GuildWar)
            --HandleClickPoint.SendGuildActivityEnterReq(ACTIVITY_TYPE.XIANMENGZHAN)
        elseif target_scene_type == SceneType.GuildMiJingFB then  											--仙盟秘境(仙盟守护)
            if HandleClickPoint.CheckNotHasGuild() then
                return
            end
            local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_FB)
            if not activity_info or activity_info.status == ACTIVITY_STATUS.CLOSE then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
                return
            end
            if HandleClickPoint.CheckMainUIActIsLimit(ACTIVITY_TYPE.GUILD_FB) then
                return
            end
            --GuildWGCtrl.Instance:SendGuildFbEnterReq()
            ViewManager.Instance:Open(GuideModuleName.Guild, FunName.GuildShouHu)
        elseif target_scene_type == SceneType.Kf_Honorhalls then  												--跨服荣誉殿堂(青云之颠)
            HandleClickPoint.SendActivityEnterReq(ACTIVITY_TYPE.KF_HONORHALLS)
        elseif target_scene_type == SceneType.GuildBoss then  													--仙盟BOSS
            --GuildWGCtrl.Instance.view:OnChallengeBosss()
            if HandleClickPoint.CheckNotHasGuild() then
                return
            end
            if not GuildBossWGData.Instance:IsGuildBossOpen() then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
                return
            end
              --GuildAnswerWGCtrl.Instance:SendGuildQuestionEnterReq()
            ViewManager.Instance:Open(GuideModuleName.Guild, FunName.GuildBoss)

        elseif target_scene_type == SceneType.KF_BOSS then  													--跨服BOSS
            BossWGCtrl.Instance:GoToHelpGuildAssist(params)
        elseif target_scene_type == SceneType.WorldBoss then  													--世界BOSS
            BossWGCtrl.Instance:GoToHelpGuildAssist(params)
        elseif target_scene_type == SceneType.VIP_BOSS then  													--VIP_BOSS(首领之家)
            BossWGCtrl.Instance:GoToHelpGuildAssist(params)
        elseif target_scene_type == SceneType.PERSON_BOSS then  												--个人BOSS
            ViewManager.Instance:Open(GuideModuleName.Boss, FunName.PersonalBoss)
        elseif target_scene_type == SceneType.Shenyuan_boss then  											--深渊BOSS
            BossWGCtrl.Instance:GoToHelpGuildAssist(params)
        elseif target_scene_type == SceneType.DABAO_BOSS then  												    --打宝BOSS
            ViewManager.Instance:Open(GuideModuleName.Boss, FunName.DaBaoBoss)
        elseif target_scene_type == SceneType.SHENGYU_BOSS then  												--圣域BOSS
            if HandleClickPoint.GetFunIsNotOpenedByTabName(FunName.ShengYuBoss) then
                return
            end
            local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KUAFUSACREDBOSS)
            if not activity_info or activity_info.status == ACTIVITY_STATUS.CLOSE then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
                return
            end
            ViewManager.Instance:Open(GuideModuleName.Boss, FunName.ShengYuBoss)
        elseif target_scene_type == SceneType.KFSHENYUN_FB then  												--陨落BOSS
            ViewManager.Instance:Open(GuideModuleName.WorldServer, FunName.YuLuoBoss)
        elseif target_scene_type == SceneType.XianJie_Boss then									
            ViewManager.Instance:Open(GuideModuleName.WorldServer, FunName.XianJieBoss)
        elseif target_scene_type == SceneType.SG_BOSS then  												 	--上古BOSS
            ViewManager.Instance:Open(GuideModuleName.WorldServer, FunName.ShangGuBoss)
        elseif target_scene_type == SceneType.MJ_BOSS then  												 	--秘境BOSS
            ViewManager.Instance:Open(GuideModuleName.Boss, FunName.MiJingBoss)
        elseif target_scene_type == SceneType.HotSpring then  												 	--温泉
            HandleClickPoint.SendActivityEnterReq(ACTIVITY_TYPE.HOTSPRING)
        elseif target_scene_type == SceneType.GUILD_ANSWER_FB then  											--帮派答题(仙盟答题)
            if HandleClickPoint.CheckNotHasGuild() then
                return
            end
            local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_ANSWER)
            if not activity_info or activity_info.status == ACTIVITY_STATUS.CLOSE then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
                return
            end
            if HandleClickPoint.CheckMainUIActIsLimit(ACTIVITY_TYPE.GUILD_ANSWER) then
                return
            end
            --GuildAnswerWGCtrl.Instance:SendGuildQuestionEnterReq()

            ViewManager.Instance:Open(GuideModuleName.Guild, FunName.GuildDaTi)

        elseif target_scene_type == SceneType.YEZHANWANGCHENGFUBEN then  										--夜战王城
            HandleClickPoint.CheckYeZhanWangCheng()
        elseif target_scene_type == SceneType.Kf_OneVOne_Prepare then 											--跨服1V1准备场景
            Field1v1WGCtrl.Instance:EnterFieldPrepareScene(ACTIVITY_TYPE.KF_ONEVONE)
        elseif target_scene_type == SceneType.Kf_PvP_Prepare then 												--跨服PVP准备场景
            KF3V3WGCtrl.Instance:EnterPrepareScene()
        elseif target_scene_type == SceneType.Kf_OneVOne then 											        --跨服1V1
            ViewManager.Instance:Open(GuideModuleName.ActJjc, FunName.KF1V1)
        elseif target_scene_type == SceneType.Kf_PVP then 												        --跨服PVP（3V3）
            ViewManager.Instance:Open(GuideModuleName.ActJjc, FunName.KFPVP)
        elseif target_scene_type == SceneType.CROSS_LIEKUN then 												--猎鲲地带
            local is_open,zone = GuildWGData.Instance:GetIsCanJoinLiekun(scene_id)
            if not is_open then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
                return
            end
            HandleClickPoint.OnClickGoToLieKun(zone)
        elseif target_scene_type == SceneType.CROSS_LAND_WAR then 												--阵地战
            ViewManager.Instance:Open(GuideModuleName.PositionalWarfareView, FunName.PositionalWarfareView)
        elseif target_scene_type == SceneType.SCENE_TYPE_LAND_WAR_FB then
            ViewManager.Instance:Open(GuideModuleName.LandWarFbPersonView, FunName.LandWarFbPersonView)
        end
    end
end

--夜战王城根据等级进去 本服或者跨服
function HandleClickPoint.CheckYeZhanWangCheng()
    local level = GameVoManager.Instance:GetMainRoleVo().level
    local kuafu_act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)
    if level < kuafu_act_cfg.level then
        local benfu_act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.YEZHANWANGCHENG)
        if level < benfu_act_cfg.level then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelOpen, benfu_act_cfg.level))
            return true
        end
        local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.YEZHANWANGCHENG)
        if not activity_info or activity_info.status == ACTIVITY_STATUS.CLOSE or activity_info.status == ACTIVITY_STATUS.STANDY then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
            return true
        end
        KuafuYeZhanWangChengWGCtrl.Instance:SendNightFightEnterReq()
        return false
    end
    if level > kuafu_act_cfg.level_max then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotEnterAct)
        return true
    end
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)
    if not activity_info or activity_info.status == ACTIVITY_STATUS.CLOSE or activity_info.status == ACTIVITY_STATUS.STANDY then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
        return true
    end
    CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)
    return false
end

function HandleClickPoint.SendGuildActivityEnterReq(act_type)
    if HandleClickPoint.CheckNotHasGuild() then
        return
    end
    HandleClickPoint.SendActivityEnterReq(act_type)
end

--（活动类型， 是否准备场景可以进去）
function HandleClickPoint.SendActivityEnterReq(act_type, is_can_prepare_enter)
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
    if not activity_info or activity_info.status == ACTIVITY_STATUS.CLOSE then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
        return
    end

    if not is_can_prepare_enter then
        if activity_info.status == ACTIVITY_STATUS.STANDY then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
            return
        end
    end

    if HandleClickPoint.CheckMainUIActIsLimit(act_type) then
        return
    end
    ActivityWGCtrl.Instance:SendActivityEnterReq(act_type)
end

function HandleClickPoint.GetFunIsNotOpenedByTabName(fun_name)
    local is_open, tips = FunOpen.Instance:GetFunIsOpenedByTabName(fun_name, true)
    if not is_open then
        SysMsgWGCtrl.Instance:ErrorRemind(tips)
        return true
    end
    return false
end

function HandleClickPoint.CheckFunNotOpen(fun_name)
    local is_open, tips = FunOpen.Instance:GetFunIsOpened(fun_name, true)
    if not is_open then
        SysMsgWGCtrl.Instance:ErrorRemind(tips)
        return true
    end
    return false
end

--帮派判断
function HandleClickPoint.CheckNotHasGuild()
    if GameVoManager.Instance:GetMainRoleVo().guild_id <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotEnterNoGuild)
        return true
    end
    return false
end

--等级判断
function HandleClickPoint.CheckMainUIActIsLimit(act_type)
    local act_cfg = DailyWGData.Instance:GetActivityConfig(act_type)
    if nil == act_cfg then
        return false
    end
    --是否需要等级上限
    local is_need_limit = 0
    local level = GameVoManager.Instance:GetMainRoleVo().level
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local week_day = TimeUtil.FormatSecond3MYHM1(server_time)

    if act_cfg.limit_day and "" ~= act_cfg.limit_day then
        local open_day_list = Split(act_cfg.limit_day,":")
        for k,v in pairs(open_day_list) do
            if tonumber(v) == tonumber(week_day) then
                is_need_limit = 1
                break
            end
        end
    end

    if 1 == is_need_limit then --根据周几来判断是否有等级上限
        if level < act_cfg.level  then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelOpen, act_cfg.level))
            return true
        end

        if level > act_cfg.level_max then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotEnterAct)
            return true
        end
    else
        if level < act_cfg.level then	--等级不足
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelOpen, act_cfg.level))
            return true
        end
    end
    return false
end

function HandleClickPoint.OnClickGoToLieKun(zone)
    if not zone then return end
    if IS_ON_CROSSSERVER and zone then
        local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
        local scene_id = GuildWGData.Instance:GetLieKunSceneId(zone)
        GuildWGCtrl.Instance:SendCrossLieKunFBReq(CROSS_LIEKUNFB_REQ_TYPE.LIEKUNFB_TYPE_GOTO, scene_id, scene_key)
        return
    end
    if zone then
        CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_LIEKUN, zone)
    end
end

return HandleClickPoint