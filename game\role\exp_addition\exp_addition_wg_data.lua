ExpAdditionWGData = ExpAdditionWGData or BaseClass()

local ROLE_SHOUZUO_ID = 8
local ROLE_JIEZHI_ID = 9
local ROLE_TONGXINSUO_ID = 0
local BUBUGAOSHENG_TITLE_ID = 4145
function ExpAdditionWGData:__init()
	if ExpAdditionWGData.Instance then
		error("[ExpAdditionWGData] Attempt to create singleton twice!")
		return
	end
	ExpAdditionWGData.Instance = self
	self.data_list = {}
	self.total_addition_value = 0
	self.expaddititon_cfg = ConfigManager.Instance:GetAutoConfig("expaddition_auto").correlation_config
	self.expaddititon_grade_cfg = ConfigManager.Instance:GetAutoConfig("expaddition_auto").score
	self.exp_efficiency = ConfigManager.Instance:GetAutoConfig("rest_auto").exp_efficiency
	self.offlinehang_exp_add = ConfigManager.Instance:GetAutoConfig("expaddition_auto").offlinehang_exp_add
	-- self:CreatDataList()
end

function ExpAdditionWGData:__delete()
	ExpAdditionWGData.Instance = nil
	self.data_list = nil
	self.total_addition_value = 0
end

function ExpAdditionWGData:GetTotalAdditionValue()
	local total_addition_value = self.total_addition_value
	local expaddititon_grade_cfg = self.expaddititon_grade_cfg
	local data_list = {}

	for i = 1, #expaddititon_grade_cfg do
		if total_addition_value <= expaddititon_grade_cfg[i].score then
			data_list.score_rank = expaddititon_grade_cfg[i].score_rank
			data_list.score_show = expaddititon_grade_cfg[i].score_show
			break
		else
			data_list.score_rank = expaddititon_grade_cfg[#expaddititon_grade_cfg].score_rank
			data_list.score_show = expaddititon_grade_cfg[#expaddititon_grade_cfg].score_show
		end
	end

	return data_list
end

function ExpAdditionWGData:CreatDataList()
	local addition_value_list = self:GetCurAddition()
	self.data_list = {}
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local cur_type = 0
	self.total_addition_value = 0
	for k,v in pairs(self.expaddititon_cfg) do
		if not addition_value_list[v.addition_type] then
			break
		end
		local addition_value = string.format("%.2f", addition_value_list[v.addition_type])
		local linjiezhi = string.format("%.2f",v.linjiezhi)
		if role_level >= v.appear_class and cur_type ~= v.addition_type and addition_value == linjiezhi then
			cur_type = v.addition_type
			self.total_addition_value = self.total_addition_value + v.additionvalue
			local temp_list = {}
			local recommend_value = role_level >= v.replace and v.recommend_2 or v.recommend_1
			temp_list.can_click = v.whether_click
			temp_list.name = v.name
			temp_list.recommend_value = recommend_value
			temp_list.open_panel_name = v.open_panel_name
			temp_list.addition_value = addition_value
			temp_list.linjiezhi = linjiezhi
			temp_list.type = v.addition_type
			temp_list.tips_type = v.tips_type
			temp_list.virtual_id = v.virtual_id
			temp_list.default_flag = true
			temp_list.addition_show = v.additionshow
			if temp_list.name == Language.ExpAddition.ExpAddLeiXing[1] then						--经验守护
				local xiaogui = EquipWGData.Instance:GetmpGuardInfo().item_wrapper
				local exp_xiaogui = EquipWGData.Instance:GetJingYanBaoBaoByXiaoGuiList(xiaogui)
				if exp_xiaogui == nil then
					temp_list.item_id = v.addition_icon
				else
					temp_list.item_id = exp_xiaogui.item_id
					temp_list.default_flag = false
				end
			elseif temp_list.name == Language.ExpAddition.ExpAddLeiXing[2] then					--戒指加成
			--主角装备,戒指的槽位 9
				local shouzuo = EquipWGData.Instance:GetGridData(GameEnum.EQUIP_INDEX_XIANJIE)
				if shouzuo == nil then
					temp_list.item_id = v.addition_icon
				else
					temp_list.item_id = shouzuo.item_id
					temp_list.default_flag = false
				end
			elseif temp_list.name == Language.ExpAddition.ExpAddLeiXing[3] then					--手镯加成
				local jiezhi = EquipWGData.Instance:GetGridData(GameEnum.EQUIP_INDEX_XIANZHUO)
				if jiezhi == nil then
					temp_list.item_id = v.addition_icon
				else
					temp_list.item_id = jiezhi.item_id
				end
			--防止策划变卦 暂时保留
			-- elseif temp_list.name == Language.ExpAddition.ExpAddLeiXing[4] then				--经验药水
			-- 	temp_list.item_id = v.addition_icon
			elseif temp_list.name == Language.ExpAddition.ExpAddLeiXing[5] then					--同心锁
			--主角同心锁装备的  0
				local equip_info = MarryWGData.Instance:GetRingInfo(ROLE_TONGXINSUO_ID).item_id
				if equip_info == 0 then
					temp_list.item_id = v.addition_icon
				else
					temp_list.item_id = equip_info
				end
			--只有步步高升称号有经验加成属性,所以这个ID可以写死
			elseif temp_list.name == Language.ExpAddition.ExpAddLeiXing[6] then					--步步高升称号
				local title_info = TitleWGData.Instance:GetConfig(BUBUGAOSHENG_TITLE_ID)
				if title_info == nil then
					temp_list.item_id = v.addition_icon
				else
					temp_list.item_id = title_info.item_id
				end

			else
				temp_list.item_id = v.addition_icon
			end

			table.insert(self.data_list, temp_list)
		end
	end
end

function ExpAdditionWGData:IsEquipmentItem(id)
	if id then
		local data_list1 = self:GetOfflineDataList()
		if not IsEmptyTable(data_list1) then
			for k,v in pairs(data_list1) do
				if id > 0 and v.default_flag then
					return true
				end
			end
		end

		local data_list2 = self:GetDataList()
		if not IsEmptyTable(data_list2) then
			for k,v in pairs(data_list2) do
				if id > 0 and v.default_flag then
					return true
				end
			end
		end
	end

	return false
end

function ExpAdditionWGData:GetDataList()
	return self.data_list
end

function ExpAdditionWGData:GetCurAddition()
	local addition_list = {}
	local addition_value = SkillWGData.Instance:GetExpEfficiencyInfo()
	if addition_value then
		table.insert(addition_list,addition_value.imp_guard_add_per)			--经验守护
		table.insert(addition_list,addition_value.vip_extra_percent)			--VIP加成
		table.insert(addition_list,addition_value.buff_extra_percent)			--经验药水
		table.insert(addition_list,addition_value.team_extra_exp_per)			--组队加成
		table.insert(addition_list,addition_value.wing_extra_exp_per)			--羽翼加成
		table.insert(addition_list,addition_value.xiuzhenzhilu_extra_exp_per)	--大圣归来
		table.insert(addition_list,addition_value.xiuxianzhilv_exp_per)			--修仙之旅
	end

	return addition_list
end


function ExpAdditionWGData:CloseView()
	if RoleWGCtrl.Instance.exp_addititon_view:IsOpen() then
		RoleWGCtrl.Instance.exp_addititon_view:Close()
	end
	ViewManager.Instance:Close(GuideModuleName.RoleView)
	ViewManager.Instance:Close(GuideModuleName.BiZuo)
end

function ExpAdditionWGData:GetCurOffLineExpExtra()
	local addition_value_list = self:GetCurAddition()

	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local capability = GameVoManager.Instance:GetMainRoleVo().capability
	local total_addition = 0
	for k,v in pairs(addition_value_list) do
		total_addition = total_addition + v
	end

	local cfg = self.exp_efficiency[role_level]
	local value = 1 * cfg.std_exp + total_addition * capability * 1.2 / (cfg.std_cap + capability * 0.2)
	return value, total_addition
end

function ExpAdditionWGData:GetOffLineStdExp()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local cfg = self.exp_efficiency[role_level]
	if cfg then
		return cfg.std_exp 
	end
	return 0
end

--离线挂机倍数调整
function ExpAdditionWGData:GetOffLineStdExpTimes()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local cfg = self.exp_efficiency[role_level]
	return cfg and cfg.exp_times or 1
end

--离线经验加成计算
function ExpAdditionWGData:CreatOfflineDataList()
	local addition_value_list = self:GetCurOfflineAddition()
	self.offline_data_list = {}
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local cur_type = 0
	for k,v in pairs(self.offlinehang_exp_add) do
		local addition_value = string.format("%.2f", addition_value_list[v.addition_type])
		local linjiezhi = string.format("%.2f",v.linjiezhi)
		if role_level >= v.appear_class and cur_type ~= v.addition_type and  addition_value == linjiezhi then
			cur_type = v.addition_type
			local temp_list = {}
			local recommend_value = role_level >= v.replace and v.recommend_2 or v.recommend_1
			temp_list.can_click = v.whether_click
			temp_list.name = v.name
			temp_list.recommend_value = recommend_value
			temp_list.open_panel_name = v.open_panel_name
			temp_list.addition_value = addition_value
			temp_list.linjiezhi = linjiezhi
			temp_list.type = v.addition_type
			temp_list.tips_type = v.tips_type
			temp_list.virtual_id = v.virtual_id
			temp_list.default_flag = true
			if temp_list.name == Language.ExpAddition.ExpAddLeiXing[1] then						--经验守护
				local xiaogui = EquipWGData.Instance:GetmpGuardInfo().item_wrapper
				local exp_xiaogui = EquipWGData.Instance:GetJingYanBaoBao(xiaogui)
				if exp_xiaogui == nil then
					temp_list.item_id = v.addition_icon
				else
					temp_list.item_id = exp_xiaogui.item_id
				end
			elseif temp_list.name == Language.ExpAddition.ExpAddLeiXing[2] then					--戒指加成
				local shouzuo = EquipWGData.Instance:GetGridData(9)
				if shouzuo == nil then
					temp_list.item_id = v.addition_icon
				else
					temp_list.item_id = shouzuo.item_id
					temp_list.default_flag = false
				end
			elseif temp_list.name == Language.ExpAddition.ExpAddLeiXing[3] then					--手镯加成
				local jiezhi = EquipWGData.Instance:GetGridData(8)
				if jiezhi == nil then
					temp_list.item_id = v.addition_icon
				else
					temp_list.item_id = jiezhi.item_id
					temp_list.default_flag = false
				end
			elseif temp_list.name == Language.ExpAddition.ExpAddLeiXing[5] then					--同心锁
				local equip_info = MarryWGData.Instance:GetRingInfo(0).item_id
				if equip_info == 0 then
					temp_list.item_id = v.addition_icon
				else
					temp_list.item_id = equip_info
				end

			else
				temp_list.item_id = v.addition_icon
			end


			table.insert(self.offline_data_list,temp_list)
		end
	end
end

function ExpAdditionWGData:GetOfflineDataList()
	return self.offline_data_list
end

function ExpAdditionWGData:GetCurOfflineAddition()
	local addition_list = {}
	local addition_value = SkillWGData.Instance:GetExpEfficiencyInfo()
	table.insert(addition_list,addition_value.imp_guard_add_per)			--经验守护
	table.insert(addition_list,addition_value.vip_extra_percent)			--VIP加成
	table.insert(addition_list,addition_value.buff_extra_percent)			--经验药水
	table.insert(addition_list,addition_value.wing_extra_exp_per)			--羽翼加成
	table.insert(addition_list,addition_value.xiuzhenzhilu_extra_exp_per)	--大圣归来
	table.insert(addition_list,addition_value.xiuxianzhilv_exp_per)			--修仙之旅
	return addition_list
end

function ExpAdditionWGData:GetAllOfflineExpAdd()
	local offline_data_list = self:GetOfflineDataList()
	local recommend_value = 0
	if offline_data_list then
		for k,v in pairs(offline_data_list) do
			recommend_value = recommend_value + v.recommend_value
		end
	end
	return recommend_value
end
