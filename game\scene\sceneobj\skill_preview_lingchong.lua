SkillPreviewLingChong = SkillPreviewLingChong or BaseClass(LingChong)
function SkillPreviewLingChong:__init(vo)
    self.obj_type = SceneObjType.SkillShower
    self.shield_obj_type = ShieldObjType.SkillShower
    
    -- self.draw_obj:SetCurDetailLevel(SceneObjDetailLevel.High)
    if SceneObjLODManager.Instance then
        SceneObjLODManager.Instance:Remove(self)
    end
end

function SkillPreviewLingChong:TryUsePetSkill()

end

function SkillPreviewLingChong:DoMove()
	
end

function SkillPreviewLingChong:Update()

end

function SkillPreviewLingChong:WadnerForce()
	
end