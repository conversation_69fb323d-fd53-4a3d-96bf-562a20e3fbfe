-- 便捷组队
--------------
NewTeamQuickView = NewTeamQuickView or BaseClass(SafeBaseView)
function NewTeamQuickView:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_quick")
	self:SetMaskBg()
	self.team_target_cfg = {}
	self.list_view = {}
	self.table_index = 1
end

function NewTeamQuickView:__delete()

end

function NewTeamQuickView:ReleaseCallBack()
	if self.invite_list then
		self.invite_list:DeleteMe()
		self.invite_list = nil
	end
	self.cur_select_data = nil
	self.select_toggle_1 = nil

	if self.team_goal_list then
		for k,v in pairs(self.team_goal_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.team_goal_list = nil
	end

	for k, v in pairs(self.list_view) do
		v:DeleteMe()
	end
	self.list_view = {}
	self.has_specail_load = false
end

function NewTeamQuickView:CloseCallBack()
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	PlayerPrefsUtil.SetInt("team_type", team_type)
	PlayerPrefsUtil.SetInt("fb_mode", fb_mode)
end

function NewTeamQuickView:OpenCallBack()

end

function NewTeamQuickView:LoadCallBack()
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.node_list.title_view_name.text.text = Language.NewTeam.QuickTeam
	self.node_list["layout_blank_tip2"]:SetActive(false)

	self:InItBtnList()
	self.invite_list = AsyncListView.New(QuickListItem, self.node_list["ph_quick_list"])

	XUI.AddClickEventListener(self.node_list["btn_auto_match"], BindTool.Bind1(self.OnClickAutoMatch, self))
	XUI.AddClickEventListener(self.node_list["btn_flush"], BindTool.Bind1(self.OnClickFlush, self))
	XUI.AddClickEventListener(self.node_list["btn_create_team"], BindTool.Bind1(self.OnClickCrateTeam, self))

end

function NewTeamQuickView:InItBtnList()
	-- local list_delegate = self.node_list["list_view_left"].list_simple_delegate
	-- list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
	-- list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshCell, self)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	self.team_target_cfg = __TableCopy(NewTeamWGData.Instance:GetTeamTargetCfg())
	-- table.remove(self.team_target_cfg, 1)
	local team_type_all = {}
	team_type_all.team_type = -1
	team_type_all.fb_mode = -1
	team_type_all.fb_type = -1
	team_type_all.role_min_level = 1
	team_type_all.role_max_level = RoleWGData.GetRoleMaxLevel()
	team_type_all.scene_id = 0
	team_type_all.pos_x = 0
	team_type_all.pos_y = 0
	table.insert(self.team_target_cfg,1,team_type_all)
	self.team_target_cfg[1].team_type_name = Language.NewTeam.AllTeam
	self.team_target_cfg[2].team_type_name = Language.NewTeam.CurMapTeam

	for i = #self.team_target_cfg, 1, -1 do
		if self.team_target_cfg[i].role_min_level > role_level or self.team_target_cfg[i].team_type == 5 then
			table.remove(self.team_target_cfg, i)
		end
	end

	local team_type_five = {}
	team_type_five.team_type = 5
	team_type_five.fb_type = 46
	team_type_five.fb_mode = 0
	team_type_five.role_min_level = 420
	team_type_five.role_max_level = RoleWGData.GetRoleMaxLevel()
	team_type_five.scene_id = 6142
	team_type_five.pos_x = 67
	team_type_five.pos_y = 30
	team_type_five.team_type_name = "诛神塔"
	team_type_five.image = 12
	table.insert(self.team_target_cfg, team_type_five)
	local goal_list, specail_list = NewTeamWGData.Instance:GetTeamGoalSpecial()
	self.team_goal_list = {}
	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	for i,v in ipairs(goal_list) do
		local res_async_loader = AllocResAsyncLoader(self, "btn_team_common")
		res_async_loader:Load("uis/view/new_team_ui_prefab", "btn_team_common", nil, function(new_obj)
			local obj = ResMgr:Instantiate(new_obj)
			obj.transform:SetParent(self.node_list["viewport"].transform, false)
			self.team_goal_list[i] = GoalListItemRender.New(obj)
			self.team_goal_list[i]:SetData(v)
			self.team_goal_list[i].view.button:AddClickListener(BindTool.Bind(self.OnClickTeamGoal, self, self.team_goal_list[i]))
			if now_team_type > 0 and v.team_type == now_team_type and v.fb_mode == now_fb_mode then
				self:OnClickTeamGoal(self.team_goal_list[i])
			elseif v.team_type == -1 and v.fb_mode == -1 then
				self:OnClickTeamGoal(self.team_goal_list[i])
			end
		end)
	end

	if not IsEmptyTable(specail_list) then
		self.normal_goal_count = #goal_list
		local callback = function(btn)
			local res_async_loader = AllocResAsyncLoader(self, "fb_item")
			res_async_loader:Load("uis/view/new_team_ui_prefab", "fb_item", nil, function(new_obj)
				for i,v in ipairs(specail_list) do
					local obj = ResMgr:Instantiate(new_obj)
					obj.transform:SetParent(btn, false)
					self.team_goal_list[self.normal_goal_count + i] = GoalListItemRender.New(obj)
					self.team_goal_list[self.normal_goal_count + i]:SetData(v)
					self.team_goal_list[self.normal_goal_count + i].view.button:AddClickListener(BindTool.Bind(self.OnClickTeamGoal, self, self.team_goal_list[self.normal_goal_count + i]))
					if i == #specail_list then
						self.has_specail_load = true
					end
					if self.need_speail_load then
						self:InitBtnSelectShow(true)
					end
				end
			end)
		end

		local res_async_loader = AllocResAsyncLoader(self, "Content")
		res_async_loader:Load("uis/view/new_team_ui_prefab", "Content", nil, function(new_obj)
			local obj = ResMgr:Instantiate(new_obj)
			obj.transform:SetParent(self.node_list["viewport"].transform, false)
			local btn = obj.transform:Find("List1").transform
			callback(btn)
			self.select_toggle_1 = obj.transform:Find("SelectBtn1"):GetComponent(typeof(UnityEngine.UI.Toggle))
			self.select_toggle_1:AddValueChangedListener(BindTool.Bind(self.InitBtnSelectShow, self))
			for i,v in ipairs(specail_list) do
				if v.team_type == now_team_type and v.fb_mode == now_fb_mode then
					self.select_toggle_1.isOn = true
					break
				end
			end
		end)
	end
	-- self.node_list.list_view_left.scroller:RefreshAndReloadActiveCellViews(true)
	-- self.left_invite_list:SetDataList(self.team_target_cfg)
end

function NewTeamQuickView:InitBtnSelectShow(is_on)
	if is_on then
		if self.has_specail_load then
			local _, specail_list = NewTeamWGData.Instance:GetTeamGoalSpecial()
			local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
			local state = false
			for i,v in ipairs(specail_list) do
				if v.team_type == now_team_type and v.fb_mode == now_fb_mode then
					self:OnClickTeamGoal(self.team_goal_list[self.normal_goal_count + i])
					state = true
				end
			end
			if not state then
				self:OnClickTeamGoal(self.team_goal_list[self.normal_goal_count + 1])
			end
		end
	end
end

-- function NewTeamQuickView:GetNumberOfCells()
-- 	return #self.team_target_cfg
-- end

-- function NewTeamQuickView:RefreshCell(cell, cell_index)
-- 	cell_index = cell_index + 1
-- 	local group_cell = self.list_view[cell]
-- 	if group_cell == nil then
-- 		group_cell = QuickItemRender.New(cell.gameObject)
-- 		group_cell.parent = self
-- 		self.list_view[cell] = group_cell
-- 		group_cell:SetToggleGroup(self.node_list["list_view_left"].toggle_group)
-- 	end
-- 	group_cell:SetIndex(cell_index)
-- 	group_cell:SetData(self.team_target_cfg)
-- 	group_cell:SetToggle(self.table_index)
-- 	-- group_cell:SetCallBack(BindTool.Bind(self.OnTabChangeHandler, self))
-- end

function NewTeamQuickView:OnClickTeamGoal(cell)
	self.cur_select_data = cell.data
	self:OnClickSelectTeam(cell.data)
	self:FlushAllHighLight(cell.data)
end

function NewTeamQuickView:FlushAllHighLight(data)
	for k,v in pairs(self.team_goal_list) do
		v:OnSelectChange(data)
	end
	if data.fb_type ~= 44 then
		if self.select_toggle_1 then
			self.select_toggle_1.isOn = false
		end
	end
end

function NewTeamQuickView:OnTabChangeHandler(index)
	-- if self.table_index == index then return end
	-- self.table_index = index
	-- self:ShowIndex(index)
	-- for _,v in ipairs(self.list_view) do
	-- 	print_error(v.index)
	-- 	v.toggle.isOn = index == v.index
	-- end
end

function NewTeamQuickView:OnClickSelectTeam(data)
	if data == nil then return end
	local info = data
	-- 这里暂时写40 等副本完成再改
	if info.fb_type == -1 and RoleWGData.Instance.role_vo.level >= FunOpen.Instance:GetFunByName(FunName.FubenpanelEquip).close_trigger_param then
		self.node_list["btn_create_team"]:SetActive(false)
	else
		self.node_list["btn_create_team"]:SetActive(true)
	end
	if info.team_type >= 0 and info.fb_mode >= 0 then
		NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
	end
	SocietyWGCtrl.Instance:SendTeamListReq(info.team_type, info.fb_mode)
end

function NewTeamQuickView:OnClickAutoMatch()
	local info = self.cur_select_data
	if not info then
		return
	end
    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()  --获取服务器最高世界等级
    local top_lv = info.role_max_level or top_user_level
	NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
	NewTeamWGData.Instance:SetTeamLimitLevel(info.role_min_level, top_lv)
	local operate = NewTeamWGData.Instance:GetIsMatching() and 1 or 0
	NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode)
	--self:Close()
end

function NewTeamQuickView:OnClickFlush()
	self:OnClickSelectTeam(self.cur_select_data)
end

function NewTeamQuickView:OnClickCrateTeam()
	local info = self.cur_select_data
    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()  --获取服务器最高世界等级
    local top_lv = info.role_max_level or top_user_level
	NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
	NewTeamWGData.Instance:SetTeamLimitLevel(info.role_min_level, top_lv)
	NewTeamWGCtrl.Instance:SendCreateTeam(info.team_type, info.fb_mode, info.role_min_level, top_lv)
	self:Close()
end

function NewTeamQuickView:ShowIndexCallBack()
	self:FlsuhLeftSelect()
end

function NewTeamQuickView:FlsuhLeftSelect()
	local goal_list, specail_list = NewTeamWGData.Instance:GetTeamGoalSpecial()
	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if now_team_type > 0 then
		for i,v in ipairs(self.team_goal_list) do
			if v.data and v.data.team_type == now_team_type and v.data.fb_mode == now_fb_mode then
				self:OnClickTeamGoal(v)
				break
			end
		end
		if self.select_toggle_1 and now_team_type == 4 then
			self.select_toggle_1.isOn = true
		end
	elseif self.team_goal_list[1] then
		self:OnClickTeamGoal(self.team_goal_list[1])
	end
end

function NewTeamQuickView:OnFlush()
	local team_list = NewTeamWGData.Instance:GetNearTeamList()
	if team_list.count == 0 then
		self.node_list["layout_blank_tip2"]:SetActive(true)
	else
		self.node_list["layout_blank_tip2"]:SetActive(false)
	end
	self.invite_list:SetDataList(team_list.team_list, 0)
end

------------------itemRender-----------------
QuickListItem = QuickListItem or BaseClass(BaseRender)

function QuickListItem:__init()
	XUI.AddClickEventListener(self.node_list["btn_apply"], BindTool.Bind1(self.OnClickApply, self))
end

function QuickListItem:__delete()

end

function QuickListItem:OnFlush()
	self.node_list["lbl_role_name"].text.text = (self.data.leader_name)
	self.node_list["lbl_count"].text.text = (self.data.cur_member_num .. "/" .. 3)
end

function QuickListItem:OnClickApply()
	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.AlreadyInTeam)
		return
	end
	SocietyWGCtrl.Instance:SendReqJoinTeam(self.data.team_index)
	NewTeamWGCtrl.Instance.quick_view:OnClickFlush()
end

------------------QuickItemRender-----------------
QuickItemRender = QuickItemRender or BaseClass(BaseRender)
function QuickItemRender:__init()
	self.parent = nil
	self.text = self.node_list["Text"].text
	-- self.hl_text = self.node_list["text_hight"].text
	self.toggle = self.node_list["btn_team_common"].toggle
	self.index = 1
	self.toggle:AddClickListener(BindTool.Bind(self.OnClickItem, self))
	-- self.toggle:AddValueChangedListener(BindTool.Bind(self.OnValueChange,self))
end

function QuickItemRender:__delete()
	self.parent = nil
end

function QuickItemRender:SetIndex(index)
	self.index = index
end

function QuickItemRender:OnClickItem()
	if not self.parent then return end
	self.parent:OnTabChangeHandler(self.index)
end

function QuickItemRender:SetToggle(index)
	self.toggle.isOn = index == self.index
end

function QuickItemRender:SetToggleGroup(toggle_group)
	self.toggle.group = toggle_group
end

function QuickItemRender:SetData(data)
	self.text.text = data[self.index].team_type_name
	-- self.hl_text.text = data[self.index].team_type_name
end
