FlowerAttrView = FlowerAttrView or BaseClass(SafeBaseView)

function FlowerAttrView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/sendflower_ui_prefab", "layout_flower_attr_tip")
end

function FlowerAttrView:ReleaseCallBack()
	if self.attr_list then
		for k, v in pairs(self.attr_list) do
			v:DeleteMe()
		end
		self.attr_list = nil
	end
end

function FlowerAttrView:SetData(tips_data)
	if not tips_data then return end
	self.data = tips_data

	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function FlowerAttrView:LoadCallBack()
	if not self.attr_list then
		self.attr_list = {}
		local parent_node = self.node_list["attr_list"]
		local attr_num = parent_node.transform.childCount
		for i = 1, attr_num do
			local cell = CommonAttrRender.New(parent_node:FindObj("attr_" .. i))
			cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(self.data.is_need_space or true)
			cell:SetAttrNeedMaoHao(self.data.is_need_maohao or false)
			if self.data.prefix_text then
				cell:SetAttrValuePrefix(self.data.prefix_text)
			end
			self.attr_list[i] = cell
		end
	end
end

function FlowerAttrView:OnFlush()
	self.node_list.attr_title_name.text.text = self.data.title_text or ""
	local has_attr_data = not IsEmptyTable(self.data.attr_data)
	self.node_list.attr_scroll:CustomSetActive(has_attr_data)
	self.node_list.img_no_record:CustomSetActive(not has_attr_data)

	if has_attr_data then
		local attr_data = self.data.attr_data
		for k, v in pairs(self.attr_list) do
			local attr_final_data = attr_data[k]
			v:SetVisible(attr_final_data ~= nil)
		
			if attr_final_data ~= nil then
				v:SetData(attr_final_data)

				if attr_final_data.reset_name ~= nil then
					v:ResetName(attr_final_data.reset_name)
				end
			end
		end

		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["attr_scroll"].rect)
	end
end
