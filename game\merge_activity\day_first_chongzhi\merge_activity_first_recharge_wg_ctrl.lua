require("game/merge_activity/day_first_chongzhi/merge_activity_first_recharge_wg_data")

MergeFirstRechargeWGCtrl = MergeFirstRechargeWGCtrl or BaseClass(BaseWGCtrl)
function MergeFirstRechargeWGCtrl:__init()
	if MergeFirstRechargeWGCtrl.Instance then
		ErrorLog("[MergeFirstRechargeWGCtrl] Attemp to create a singleton twice !")
	end
	MergeFirstRechargeWGCtrl.Instance = self
	self.data = MergeFirstRechargeWGData.New()
	self:RegisterAllProtocols()
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function MergeFirstRechargeWGCtrl:__delete()
	MergeFirstRechargeWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil

	if self.FRdelay_timer then
		GlobalTimerQuest:CancelQuest(self.FRdelay_timer)
		self.FRdelay_timer = nil
	end
end

function MergeFirstRechargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCSADayFirstRechargeInfo, "OnSCCSADayFirstRechargeInfo")
	self:RegisterProtocol(CSCSOADayFirstRechargeOpera)
end

function MergeFirstRechargeWGCtrl:OnSCCSADayFirstRechargeInfo(protocol)
	self.data:SetFirstRechargeData(protocol)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2109)
	RemindManager.Instance:Fire(RemindName.MergeFirstRecharge)
	MergeActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DAY_FIRST_RECHARGE)
end

function MergeFirstRechargeWGCtrl:SendDayShouChongReq(type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCSOADayFirstRechargeOpera)
	protocol.type = type
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()

end

function MergeFirstRechargeWGCtrl:OnPassDay()
	--做个容错，跨天的时候，服务器的数据可能还没同步过来
	self.FRdelay_timer = GlobalTimerQuest:AddDelayTimer(function()
		MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2109)
		self.FRdelay_timer = nil
	end, 1)
end