#if UNITY_EDITOR


using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using Object = UnityEngine.Object;

namespace ProceduralLOD
{
    public class Simplifier
    {
        public GameObject toModify;
        
        public virtual IEnumerator Simplify(GameObject toSimplify, float strength)
        {
            yield return null;
        }
        public virtual void BeginModify(GameObject toModify, float strength)
        {
            this.toModify = toModify;
        }

        public virtual float EndModify()
        {
            return 0;
        }

    }

    public struct ObjectMeshPair
    {
        public Object obj; //GameObject Or Renderer
        public Mesh sourceMesh;
        public Mesh simplifiedMesh;

        public ObjectMeshPair(Object obj, Mesh sourceMesh, Mesh simplifiedMesh)
        {
            this.obj = obj;
            this.sourceMesh = sourceMesh;
            this.simplifiedMesh = simplifiedMesh;
        }
    }
}

#endif