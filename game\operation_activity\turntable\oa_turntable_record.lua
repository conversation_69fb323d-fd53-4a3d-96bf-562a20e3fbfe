OATurnTableRecord = OATurnTableRecord or BaseClass(SafeBaseView)

function OATurnTableRecord:__init()
    self:SetMaskBg(true, true)
    -- self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(790, 408)})
    self:AddViewResource(0, "uis/view/operation_turntable_prefab", "layout_ts_turnable_record")

end

function OATurnTableRecord:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
    self.data_list = nil
end

function OATurnTableRecord:ShowIndexCallBack()
    OATurnTableWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.RECORD)
    OATurnTableWGData.Instance:ClearRecordCount()
end

function OATurnTableRecord:LoadCallBack()

    self.node_list.title_view_name.text.text = Language.OATurnTable.RecordTitle
    self.record_list = AsyncListView.New(OATurnTableRecordRender, self.node_list["record_list"])
    -- self.record_list:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize,self))
end

local LINE_SPACING = 40
function OATurnTableRecord:ChangeCellSize(data_index)
    local data = self.data_list and self.data_list[data_index + 1] 
    if not data then return 0 end

    local name = data.name
    local reward = OATurnTableWGData.Instance:GetTurnTableRewardInfoById(data.reward_id)
    reward = reward.reward_item
    if not reward then
        return 0
    end

    local record_cfg = OATurnTableWGData.Instance:GetTurnTableRecordByItemId(reward.item_id) 
    if not record_cfg then
        return 0
    end

    local str = record_cfg.record_copy
    local cfg = ItemWGData.Instance:GetItemConfig(reward.item_id)
    local layer_cfg = OATurnTableWGData.Instance:GetLayerCfgByLayerNum(data.layer)
    str = string.format(str, name, layer_cfg.name, ITEM_COLOR[cfg.color], cfg.name, reward.num)

    self.node_list["TestText"].text.text = str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)

    local hight = math.ceil(self.node_list["TestText"].rect.rect.height) + LINE_SPACING
    return hight or 0
end

function OATurnTableRecord:OnFlush()
    local data = OATurnTableWGData.Instance:GetRecordInfo()
    self.data_list = data
    self.record_list:SetDataList(data, 3)
    self.node_list["no_invite"]:SetActive(IsEmptyTable(data))
end

function OATurnTableRecord:CloseCalBack()
    OATurnTableWGData.Instance:ClearRecordCount()
end
-------------------------------------------------------------------------------------
OATurnTableRecordRender = OATurnTableRecordRender or BaseClass(BaseRender)
function OATurnTableRecordRender:OnFlush()
    -- self.node_list['time'].text.text = TimeUtil.FormatYMDHMS(self.data.timestamp)


    local name = self.data.name
    local reward = OATurnTableWGData.Instance:GetTurnTableRewardInfoById(self.data.reward_id)
    reward = reward.reward_item
    if not reward then
        return
    end

    local record_cfg = OATurnTableWGData.Instance:GetTurnTableRecordByItemId(reward.item_id) 
    if not record_cfg then
        print_error("兔女郎记录信息配置为空 reward.item_id, record_cfg", reward.item_id, record_cfg)
        return
    end

    local str = record_cfg.record_copy

    local cfg = ItemWGData.Instance:GetItemConfig(reward.item_id)

    local layer_cfg = OATurnTableWGData.Instance:GetLayerCfgByLayerNum(self.data.layer)

    str = string.format(str, name, layer_cfg.name, ITEM_COLOR[cfg.color], cfg.name, reward.num)

    self.node_list["desc"].text.text = str
end