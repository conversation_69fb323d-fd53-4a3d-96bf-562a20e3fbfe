BiZuoFindView = BiZuoFindView or BaseClass(SafeBaseView)

function BiZuoFindView:__init()
	-- self:SetModal(true)
	self:SetMaskBg(false)
	self:LoadConfig()
end

function BiZuoFindView:LoadConfig()
	self:AddViewResource(0, "uis/view/bizuo_ui_prefab", "layout_find")
end
 
function BiZuoFindView:__delete()

end

function BiZuoFindView:ReleaseCallBack()
	if self.alert_tips then
		self.alert_tips:DeleteMe()
		self.alert_tips = nil
	end

	if self.alert_all_tips then
		self.alert_all_tips:DeleteMe()
		self.alert_all_tips = nil
	end

	if self.find_list then
		self.find_list:DeleteMe()
		self.find_list = nil
	end

	if self.daily_tip_txt then 
		self.daily_tip_txt = nil
	end
end

function BiZuoFindView:CreateAlert1()
	if not self.alert_tips then
		self.alert_tips = Alert.New()
	end
	return self.alert_tips
end

function BiZuoFindView:CreateAlert2()
	if not self.alert_all_tips then
		self.alert_all_tips = Alert.New()
	end
end

function BiZuoFindView:LoadCallBack()
	-- 创建任务列表
	self.find_list = AsyncListView.New(FindWorkItemRender, self.node_list.ph_find_list)
	self.daily_tip_txt = self.node_list.lbl_daily_tip
	self.daily_tip_txt:SetActive(false)

	-- 创建提示框
	self:CreateAlert1()

	-- -- 创建一键找回提示框
	self:CreateAlert2()
	
	XUI.AddClickEventListener(self.node_list.btn_find_all, BindTool.Bind1(self.OpenAlert, self))
end

function BiZuoFindView:OpenAlert()
	self:CreateAlert2()
	local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
	local cost_text = Language.Common.GoldText
	local all_yuanbao = BiZuoWGData.Instance:GetFindAllYuanBao()
	if all_yuanbao == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuo.ErrorFind)
		return
	end 
	if bind_gold_num >= all_yuanbao then
		cost_text = Language.Common.BindGoldText
	end 
	self.alert_all_tips:SetOkFunc(BindTool.Bind1(self.OnClickFindAll, self))
	self.alert_all_tips:SetLableString(string.format(Language.BiZuo.FindAllDes, cost_text, all_yuanbao))
	self.alert_all_tips:SetShowCheckBox(true)
	self.alert_all_tips:Open()
end

function BiZuoFindView:OnClickFindAll()
	-- BiZuoWGCtrl.Instance:SendBiZuoOperate(DAILY_WORK_OPERA_REQ_TYPE.DW_OPERA_REQ_TYPE_FIND_BACK, 0, 1)
	BiZuoWGCtrl.Instance:SendBiZuoOperate(DAILY_WORK_OPERA_REQ_TYPE.DW_OPERA_REQ_TYPE_FIND_BACK,nil,nil,nil,0,1)
end

function BiZuoFindView:OpenCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
end

function BiZuoFindView:CloseCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
end

function BiZuoFindView:ShowIndexCallBack()
	self:Flush()
end

function BiZuoFindView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
			local find_info = BiZuoWGData.Instance:GetFindList() or {}
			if find_info then

			local  complete_times_list2 = {}
			local num = math.floor(#find_info/2)
			local task_num = #find_info
			--local mod2 = math.mod(#complete_times_list,2)
			local mod2 = #find_info % 2 
			for i = num, 1, -1 do
				local two_item = {}
				two_item[1] = find_info[i*2-1]
				two_item[2] = find_info[i*2]
				complete_times_list2[i] = two_item
 				--  complete_times_list2[i][1] = complete_times_list[i*2]
			 	-- complete_times_list2[i][2] = complete_times_list[i*2+1]
			end
			if mod2 == 1 then 
				local two_item = {}
				two_item[1] = find_info[task_num]
				complete_times_list2[num+1] = two_item
			end
				self.find_list:SetDataList(complete_times_list2, 0)
			end

			if next(find_info) == nil then
				self.daily_tip_txt:SetActive(true)
			else
				self.daily_tip_txt:SetActive(false)
			end
	end
end

---------------itemRender-------------------------
FindWorkItemRender = FindWorkItemRender or BaseClass(BaseRender)

function FindWorkItemRender:__init()
end

function FindWorkItemRender:__delete()
	self.need_yuanbao = 0
	self.need_yuanbao2 = 0
end

function FindWorkItemRender:LoadCallBack()
	self.need_yuanbao = 0
	self.need_yuanbao2 = 0
	self.node_list.btn_find_one.button:AddClickListener(BindTool.Bind(self.OpenAlert, self,1))
	self.node_list.btn_find_one2.button:AddClickListener(BindTool.Bind(self.OpenAlert, self,2))
end

function FindWorkItemRender:OnFlush()
	if not self.data then return end
	-- 刷新图标

	self.node_list.img_icon.image:LoadSprite(ResPath.GetBiZuo(self.data[1].icon))
	self.node_list.text_name.text.text = self.data[1].name
	-- -- 刷新数据
	local other = BiZuoWGData.Instance:GetWorkOther()
	if other and next(other) ~= nil then
		local jinyan = (self.data[1].complete_max_times - self.data[1].buy_times - self.data[1].yesterday_complete_times) * self.data[1].exp_per_times
		local gold_per_exp = other[1].gold_per_exp or 0
		self.need_yuanbao = math.ceil(gold_per_exp * jinyan / 10000)
		self.node_list.lbl_find_jinyan.text.text = jinyan
		self.node_list.lbl_find_yuanbao.text.text = self.need_yuanbao
	end
	self.node_list.item2:SetActive(self.data[2] ~= nil)
	if self.data[2] then
		self.node_list.img_icon2.image:LoadSprite(ResPath.GetBiZuo(self.data[2].icon))
		self.node_list.text_name2.text.text = self.data[2].name

		-- -- 刷新数据
		local other = BiZuoWGData.Instance:GetWorkOther()
		if other and next(other) ~= nil then
			local jinyan = (self.data[2].complete_max_times - self.data[2].buy_times - self.data[2].yesterday_complete_times) * self.data[2].exp_per_times
			local gold_per_exp = other[1].gold_per_exp or 0
			self.need_yuanbao2 = math.ceil(gold_per_exp * jinyan / 10000)
			self.node_list.lbl_find_jinyan2.text.text = jinyan
			self.node_list.lbl_find_yuanbao2.text.text = self.need_yuanbao2
		end
	end
end

function FindWorkItemRender:OpenAlert(index)
	if not self.data then return end
	local need_yuanbao = 0 
	local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
	local cost_text = Language.Common.GoldText

	if index == 1 then
		need_yuanbao = self.need_yuanbao 
	elseif index == 2 then
		need_yuanbao =  self.need_yuanbao2
	end

	if bind_gold_num >= need_yuanbao then
		cost_text = Language.Common.BindGoldText
	end
	local alert = BiZuoWGCtrl.Instance.find_view:CreateAlert1()
	alert:SetOkFunc(BindTool.Bind(self.OnClickFind, self,index))
	alert:SetShowCheckBox(true)
	alert:SetLableString(string.format(Language.BiZuo.FindOneDes, cost_text, need_yuanbao , self.data[index].name))
	alert:Open()
end

function FindWorkItemRender:OnClickFind(index)
	BiZuoWGCtrl.Instance:SendBiZuoOperate(DAILY_WORK_OPERA_REQ_TYPE.DW_OPERA_REQ_TYPE_FIND_BACK,nil,nil,nil,self.data[index].type,0)
end