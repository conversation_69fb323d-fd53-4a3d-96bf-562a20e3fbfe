
function QuanMinBeiZhanView:InitLongHunView()
	self.lh_save_gift_id = nil
	
	--XUI.AddClickEventListener(self.node_list.lh_btn_tip, BindTool.Bind(self.OnLHBtn<PERSON>ip<PERSON><PERSON><PERSON><PERSON><PERSON>,self))
	XUI.AddClickEventListener(self.node_list.lh_btn_gift_buy, BindTool.Bind(self.OnLHBtnBuyClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.lh_btn_quick_compose, BindTool.Bind(self.OnLHBtnCompose<PERSON><PERSON><PERSON><PERSON><PERSON>,self))  

	-- local theme_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeCfg(TabIndex.quanmin_beizhan_longhun)
	-- if theme_cfg ~= nil then
	-- 	self.node_list.lh_tip_label.text.text = theme_cfg.rule_tip
	-- end

	self.lh_task_list = AsyncListView.New(BZTaskItemRender, self.node_list.lh_task_list)
	self.lh_gift_list = AsyncListView.New(ItemCell, self.node_list.lh_gift_list)

	self:InitLongHunBoxList()
	self:InitLongHunModel()
	self:HLTimeCountDown()
	self:FlushLHView()
end

function QuanMinBeiZhanView:ReleaseLongHunView()
	if self.lh_task_list then
		self.lh_task_list:DeleteMe()
		self.lh_task_list = nil
	end

	if self.lh_gift_list then
		self.lh_gift_list:DeleteMe()
		self.lh_gift_list = nil
	end

	-- if self.lh_sp_item then
	-- 	self.lh_sp_item:DeleteMe()
	-- 	self.lh_sp_item = nil
	-- end

	if self.bx_list then
		for k,v in pairs(self.bx_list) do
			v:DeleteMe()
		end
		self.bx_list = nil
	end

	if self.sq_tween_sequence then
        self.sq_tween_sequence:Kill()
        self.sq_tween_sequence = nil
    end

	CountDownManager.Instance:RemoveCountDown("quanminbeizhan_longhun_count_down")
end

function QuanMinBeiZhanView:ShenLongHunIndexCallBack()
	
end

function QuanMinBeiZhanView:InitLongHunModel()
	local other_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeOtherCfg()
	if not other_cfg or not other_cfg[1] then
		return
	end
	local xianwa_str = other_cfg[1].longhun_xianwa or ""
	local xianwa_info = string.split(xianwa_str, "|")
	local xianwa_cfg = MarryWGData.Instance:GetBabyListCfgByID(xianwa_info[2], xianwa_info[1])
	if not xianwa_cfg then
		return
	end
end

function QuanMinBeiZhanView:InitLongHunBoxList()
	local bx_list = {}
	local gift_list = QuanMinBeiZhanWGData.Instance:GetLHRewardCfg()
	local bx_root = self.node_list.lh_score_layout
	if gift_list then
		--gift_list = SortTableKey(gift_list, true)
		for i=1,#gift_list do
			local item = QuanMinLoadBXItem.New(bx_root)
			item:SetData(gift_list[i])
			bx_list[i] = item
		end
	end
	self.bx_list = bx_list
end

--更新界面数据
function QuanMinBeiZhanView:FlushLHView()
	self:FlushLHTaskList()
	self:FlushGiftData()
	self:FlushScore()
	self:FlushLH()
end

function QuanMinBeiZhanView:FlushLHTaskList()
	local task_list = QuanMinBeiZhanWGData.Instance:GetLHTaskList()
	if task_list then
		self.lh_task_list:SetDataList(task_list)
	end
end

function QuanMinBeiZhanView:FlushGiftData()
	local gift_cfg ,is_finish = QuanMinBeiZhanWGData.Instance:GetCanBuyGiftData()

	if (self.lh_save_gift_id and self.lh_save_gift_id ~= gift_cfg.ID) then
		self.lh_save_gift_id = gift_cfg.ID
		self:TurnPageTween()
		return
	end
	self.lh_save_gift_id = gift_cfg.ID

	if gift_cfg ~= nil then
		local sort_data = SortDataByItemColor(gift_cfg.reward_item)
		self.lh_gift_list:SetDataList(sort_data)
		self.node_list.lh_gift_old_price.text.text = gift_cfg.old_price
		self.node_list.lh_gift_new_price.text.text = gift_cfg.price
		self.node_list.lh_btn_yilingqu:SetActive(is_finish)
		self.node_list.lh_btn_gift_buy:SetActive(not is_finish)
		self.node_list.lh_dazhe:SetActive(not is_finish)
		self.node_list.lh_zekou.text.text = string.format(Language.KuaFuMiaoSha.Discount, math.floor(gift_cfg.price * 1.0 / gift_cfg.old_price * 10))
	end
end

function QuanMinBeiZhanView:TurnPageTween()
	if self.sq_tween_sequence then
		return
	end
	local tween_1 = self.node_list.lh_gift_list.transform:DOLocalRotate(Vector3(90,0,0), 0.5)
	local tween_2 = self.node_list.lh_gift_list.transform:DOLocalRotate(Vector3(360,0,0), 0.8, DG.Tweening.RotateMode.FastBeyond360)
	local tween_sequence = DG.Tweening.DOTween.Sequence()
	tween_sequence:Append(tween_1)
	tween_sequence:AppendCallback(function ()
		self:FlushGiftData()
	end)
	tween_sequence:Append(tween_2)
	tween_sequence:OnComplete(function ()
		self.sq_tween_sequence = nil
	end)
	tween_sequence:SetEase(DG.Tweening.Ease.Linear)
	self.sq_tween_sequence = tween_sequence
end

function QuanMinBeiZhanView:FlushScore()
	local score = QuanMinBeiZhanWGData.Instance:GetDangweiScore()
	self.node_list.lh_score.text.text = score

	-- local cur_progress = QuanMinBeiZhanWGData.Instance:CalcIrregularProgress(score)
	-- if cur_progress > 0 then
	-- 	self.node_list.lh_progress.image.fillAmount = cur_progress
	-- else
	-- 	self.node_list.lh_progress.image.fillAmount = 0
	-- end

	if self.bx_list then
		for k,v in pairs(self.bx_list) do
			v:Flush()
		end
	end
end

function QuanMinBeiZhanView:FlushLH()
	local lhsp_cfg, other_cfg = QuanMinBeiZhanWGData.Instance:GetLHSuiPianCfg()
	if lhsp_cfg ~= nil and other_cfg ~= nil then
		-- if self.lh_sp_item == nil then
		-- 	self.lh_sp_item = ItemCell.New(self.node_list.lh_compose_cell)
		-- end

		--local color ="<color='#00ff00'>"  
		local item_num = ItemWGData.Instance:GetItemNumInBagById(lhsp_cfg.stuff_id_1)
		-- if item_num < lhsp_cfg.stuff_count_1 then
		-- 	color = "<color='#ff5a00'>"
		-- end

		-- local item = {}
		-- item.item_id = lhsp_cfg.stuff_id_1
		-- item.num = item_num
		-- self.lh_sp_item:SetData(item)

		--self.node_list.lh_shenqi_name.text.text = ItemWGData.Instance:GetItemName(lhsp_cfg.stuff_id_1)
		--self.node_list.lh_compose_need.text.text = string.format(Language.QuanMinBeiZhan.LongHunStr1, color, item_num, lhsp_cfg.stuff_count_1)

		XUI.SetButtonEnabled(self.node_list.lh_btn_quick_compose, true)

		if item_num < lhsp_cfg.stuff_count_1 then
			XUI.SetGraphicGrey(self.node_list.lh_btn_quick_compose, true)
			self.node_list.lh_hc_red_point:SetActive(false)
			--self.node_list.lh_hc_lq_effect:SetActive(false)
		else
			XUI.SetGraphicGrey(self.node_list.lh_btn_quick_compose, false)
			self.node_list.lh_hc_red_point:SetActive(true)
			--self.node_list.lh_hc_lq_effect:SetActive(true)
		end
	end
end

-- function QuanMinBeiZhanView:OnLHBtnTipClickHnadler()
-- 	local role_tip = RuleTip.Instance
-- 	if role_tip then
-- 		local title,desc = QuanMinBeiZhanWGData.Instance:GetActivityTip(TabIndex.quanmin_beizhan_longhun)
-- 		if title ~= nil and desc ~= nil then
-- 			role_tip:SetTitle(title)
-- 			role_tip:SetContent(desc)
-- 		end
-- 	end
-- end   

function QuanMinBeiZhanView:OnLHBtnBuyClickHnadler()
	if self.sq_tween_sequence then
		return
	end
	local gift_cfg, is_finish = QuanMinBeiZhanWGData.Instance:GetCanBuyGiftData()
	if gift_cfg then
		TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat3, gift_cfg.price, gift_cfg.gift_name), function ()
				local cur_gift_index = QuanMinBeiZhanWGData.Instance:GetLHGiftIndex()
				QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_WOYAOLONGHUN,WOYAOSHENQI_OP_TYPE.BUYLIBAO, cur_gift_index + 1)
			end)
	end
end

function QuanMinBeiZhanView:OnLHBtnComposeClickHnadler()
	local lhsp_cfg, other_cfg = QuanMinBeiZhanWGData.Instance:GetLHSuiPianCfg()
	if 	lhsp_cfg ~= nil then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(lhsp_cfg.stuff_id_1)
		if item_num < lhsp_cfg.stuff_count_1 then
	        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = lhsp_cfg.stuff_id_1})
	    else
	        QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_WOYAOLONGHUN,WOYAOSHENQI_OP_TYPE.HECHENG_SHENQI)
	    end
	end
end


--有效时间倒计时
function QuanMinBeiZhanView:HLTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("quanminbeizhan_longhun_count_down")
	local invalid_time = QuanMinBeiZhanWGData.Instance:GetActivityInValidTime(TabIndex.quanmin_beizhan_longhun)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.lh_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("quanminbeizhan_longhun_count_down", BindTool.Bind1(self.UpdateLHCountDown, self), BindTool.Bind1(self.HLTimeCountDown, self), invalid_time, nil, 1)
	else
		self.node_list.lh_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.lh_time_label.text.color = Str2C3b(COLOR3B.RED)
	end
end

function QuanMinBeiZhanView:UpdateLHCountDown(elapse_time, total_time)
	self.node_list.lh_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

-------------------------------------------------------------------------------------------------

BZTaskItemRender = BZTaskItemRender or BaseClass(BaseRender)

function BZTaskItemRender:LoadCallBack()
	self.item_list = {}
	self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
	self.node_list["btn_go"].button:AddClickListener(BindTool.Bind1(self.OnClickGoHnadler, self))
	self.sq_task_reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	self.sq_task_reward_list:SetIsDelayFlush(false)
end

function BZTaskItemRender:__delete()
	if self.sq_task_reward_list then
		self.sq_task_reward_list:DeleteMe()
		self.sq_task_reward_list = nil
	end
end

function BZTaskItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end 

	local receive_state = QuanMinBeiZhanWGData.Instance:GetLHTaskState(data.cfg.ID)
	self.node_list["task_name"].text.text = data.cfg.task_name
	self.node_list["condition"].text.text = self:GetTaskDesc()

	self.node_list["btn_lingqu"]:SetActive(receive_state == 1)
	self.node_list["btn_yilingqu"]:SetActive(receive_state == 2)
	self.node_list["btn_go"]:SetActive(receive_state == 0 and data.cfg.panel ~= "")

	-- for i=1,5 do
	-- 	self.node_list["star"..i]:SetActive(data.cfg.reward_jifen >= i)
	-- end

	self.sq_task_reward_list:SetDataList(SortTableKey(data.cfg.reward_item))
end

function BZTaskItemRender:OnClickRewardHnadler(sender)
	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_WOYAOLONGHUN, WOYAOSHENQI_OP_TYPE.TASK_REWARD, self.data.cfg.ID)
end

function BZTaskItemRender:OnClickGoHnadler(sender)
	if self.data and self.data.cfg then
		local param = string.split(self.data.cfg.panel,"#")  
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end

function BZTaskItemRender:GetTaskDesc()
	local str = self.data.cfg.task_desc
	local param = QuanMinBeiZhanWGData.Instance:GetLongHunTaskParamNum(self.data.cfg.ID)
	if param ~= nil then
		local pos = string.find(str, "%%s")
		if pos ~= nil then
			local color1 = "<color=#009621>%d</color>"
			local color2 = "<color=#ff0000>%d</color>"
			str = param >= self.data.cfg.task_param1 and string.format(str,string.format(color1, self.data.cfg.task_param1)) or string.format(str,string.format(color2, param))
		end
		str = string.format("<color=#a5492d>%s</color>",str)
	end
	return str
end