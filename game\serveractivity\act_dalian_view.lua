
ServerActivityDaLianView = ServerActivityDaLianView or BaseClass(SafeBaseView)
function ServerActivityDaLianView:__init()
	self:SetMaskBg(true,true,nil,BindTool.Bind(self.OnclickClose,self))
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_open_server_dalian")
end

function ServerActivityDaLianView:__delete()

end

function ServerActivityDaLianView:ReleaseCallBack()
	-- if self.dalian_down_time and CountDownManager.Instance:HasCountDown(self.dalian_down_time) then
	-- 	CountDownManager.Instance:RemoveCountDown(self.dalian_down_time)
	-- end
end

function ServerActivityDaLianView:LoadCallBack()
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACT_OPEN_SERVER_DALIAN)
	local end_time, start_time = activity_data.end_time, activity_data.start_time
	local end_time_tab = os.date("*t", end_time)
	local start_time_tab = os.date("*t", start_time)

	local start_str = string.format(Language.QuanMinBeiZhan.DaLianStr, start_time_tab.month, start_time_tab.day, start_time_tab.hour, start_time_tab.min, start_time_tab.sec)
	local end_str = string.format(Language.QuanMinBeiZhan.DaLianStr, end_time_tab.month, end_time_tab.day, end_time_tab.hour, end_time_tab.min, end_time_tab.sec)
	self.node_list.zl_time_label.text.text = string.format("活动时间：%s-%s", start_str, end_str)

	self.node_list.zl_content.text.text = Language.ServerActZhenLong.DaLianStr
	-- self.node_list.tip.text.text = string.format(Language.TianShenRoad.DaLianStr, 6)
	-- self.dalian_down_time = "dalian_down_time"
	-- CountDownManager.Instance:AddCountDown(self.dalian_down_time, function(elapse_time, total_time)
	-- 	local invalid_time = total_time - elapse_time
	-- 	if invalid_time > 0 then
	-- 		self.node_list.tip.text.text = string.format(Language.TianShenRoad.DaLianStr, math.floor(invalid_time))
	-- 	end
	-- end, 
	-- function()
	-- 	self:Close()
	-- end, TimeWGCtrl.Instance:GetServerTime() + 6, nil, 1)
	XUI.AddClickEventListener(self.node_list.zl_close_window, BindTool.Bind(self.OnclickClose,self))
end

function ServerActivityDaLianView:OnclickClose()
	self:Close()
end