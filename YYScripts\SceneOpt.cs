﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


using YYGame.Code;

public class SceneOpt : MonoBehaviour {

    private void Awake()
    {
        GameObject rootObj = GameObject.Find("Main/Models");
        if (null == rootObj)
        {
            Debug.Log("Main/Models 不存在");
            return;
        }


        GameObject gInstancingObj = YYGInstancingPreProccess.GetGo("GInstancing");
        GameObject gNotBatch = YYGInstancingPreProccess.GetGo("NotBatch");

        YYGInstancingPreProccess yYGInstancingPreProccess = new YYGInstancingPreProccess(rootObj);
        yYGInstancingPreProccess.Check();
        yYGInstancingPreProccess.MoveTo(gInstancingObj);
        //yYGInstancingPreProccess.MoveToNotBatch(gNotBatch);

        CullingGroupSpawn cullingGroupSpawn = gInstancingObj.AddComponent<CullingGroupSpawn>();
        cullingGroupSpawn.OnAwake(yYGInstancingPreProccess.listMesh,yYGInstancingPreProccess.listObjParent);
        cullingGroupSpawn.OnStart();
    }

    private void OnEnable()
    {
        
    }

}
