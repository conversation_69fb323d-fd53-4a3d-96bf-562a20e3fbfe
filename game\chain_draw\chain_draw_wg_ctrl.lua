require("game/chain_draw/chain_draw_wg_data")
require("game/chain_draw/chain_draw_view")

ChainDrawWGCtrl = ChainDrawWGCtrl or BaseClass(BaseWGCtrl)

function ChainDrawWGCtrl:__init()
	if ChainDrawWGCtrl.Instance then
		<PERSON>rror<PERSON><PERSON>("[ChainDrawWGCtrl] attempt to create singleton twice!")
		return
	end

	ChainDrawWGCtrl.Instance = self
	self.data = ChainDrawWGData.New()
    self.view = ChainDrawView.New(GuideModuleName.ChainDrawView)
  
    self:RegisterAllProtocols()

    self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
end

function ChainDrawWGCtrl:__delete()
	ChainDrawWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function ChainDrawWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local other_cfg = self.data:GetOtherCfg()
    if change_item_id == other_cfg.cost_item_id then
        ViewManager.Instance:FlushView(GuideModuleName.ChainDrawView, nil, "flush_cost")
        RemindManager.Instance:Fire(RemindName.ChainDraw)
    end
end

function ChainDrawWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAChainDrawInfo, "OnSCOAChainDrawInfo")
	self:RegisterProtocol(SCOAChainDrawTaskUpdate, "OnSCOAChainDrawTaskUpdate")
    self:RegisterProtocol(SCOAChainDrawTaskInfo, "OnSCOAChainDrawTaskInfo")
end

function ChainDrawWGCtrl:ReqChainDrawInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHAIN_DRAW
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function ChainDrawWGCtrl:OnSCOAChainDrawInfo(protocol)
	--print_error("========一线牵========",protocol)
    local old_spe_reward_state = self.data:GetAllSpeRewardflag()
    local reward_list = {}
	local reward_cfg = self.data:GetAllSpeRewardCfg()
	if not IsEmptyTable(old_spe_reward_state) then
		for k, v in pairs(protocol.special_reward_flag) do
			if old_spe_reward_state[k] ~= v and old_spe_reward_state[k] == 0 then
				table.insert(reward_list, reward_cfg[k].item)
			end
		end
	end

	if not IsEmptyTable(reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end

    self.data:SetAllChainDrawInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.ChainDrawView)
	RemindManager.Instance:Fire(RemindName.ChainDraw)
end

function ChainDrawWGCtrl:OnSCOAChainDrawTaskUpdate(protocol)
	--print_error("========单个任务========",protocol)
	self.data:SetSingleTaskInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ChainDrawView, nil, "flush_task")
	RemindManager.Instance:Fire(RemindName.ChainDraw)
end

function ChainDrawWGCtrl:OnSCOAChainDrawTaskInfo(protocol)
	--print_error("========全部任务========",protocol)
	self.data:SetAllTaskInfo(protocol)
    RemindManager.Instance:Fire(RemindName.ChainDraw)
end