KfShenYunSceneLogic = KfShenYunSceneLogic or BaseClass(CrossServerSceneLogic)
--KfShenYunSceneLogic.TREASURECRYTAL = {200, 199}
KfShenYunSceneLogic.TREASURECRYTAL = {199,200}
function KfShenYunSceneLogic:__init()
	self.stop_gather_handler = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.CheckPlayerIsDie, self))
end

function KfShenYunSceneLogic:__delete()
	self.before_act_mode = nil
	if self.complete_gather then
		GlobalEventSystem:UnBind(self.complete_gather)
		self.complete_gather = nil
	end
	if self.stop_gather_handler then
		GlobalEventSystem:UnBind(self.stop_gather_handler)
		self.stop_gather_handler = nil
	end
end

-- 进入场景
function KfShenYunSceneLogic:Enter(old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		MainuiWGCtrl.Instance:ShowMainuiMenu(false)
		-- MainuiWGCtrl.Instance:SetTaskActive( false )
		CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
		BossWGCtrl.Instance:OpenSyFbTask()
		BossWGCtrl.Instance:Close()
	end
	-- local main_role = Scene.Instance:GetMainRole()

	self.complete_gather = GlobalEventSystem:Bind(ObjectEventType.COMPLETE_GATHER,BindTool.Bind(function()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end))
	--XuiBaseView.CloseAllView()
	BossWGCtrl.Instance:SendSYXuShiBossReq(GameEnum.SHENGYINFB_TYPE_GET_REFRESH_TIME)
	-- self.before_act_mode = main_role:GetVo().attack_mode
	-- MainuiWGCtrl.Instance:SendSetAttackMode(1) --切换到强制模式
	BossWGCtrl.Instance:EnterMoveSYBoss()
	--BossWGCtrl.Instance.shengyin_view:GoToPos()
end

-- 获取挂机打怪的位置
function KfShenYunSceneLogic:GetGuiJiMonsterPos()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = nil
    local target_y = nil
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end

-- function KfShenYunSceneLogic:OnLoadingComplete()
-- 	-- print_error("OnLoadingComplete")
-- 	 MainuiWGCtrl.Instance:ShowMainuiMenu(false)
-- end
function KfShenYunSceneLogic:Out()
	FuhuoWGCtrl.Instance:CloseCurView()
	MainuiWGCtrl.Instance:ShowMainuiMenu(true)
	-- MainuiWGCtrl.Instance:SetTaskActive( true )
	CommonFbLogic.Out(self)
	-- if self.before_act_mode ~= nil then
	-- 	MainuiWGCtrl.Instance:SendSetAttackMode(self.before_act_mode)
	-- end
	if self.complete_gather then
		GlobalEventSystem:UnBind(self.complete_gather)
		self.complete_gather = nil
	end
	-- UiInstanceMgr.Instance:CloseRewardAction()
	BossWGCtrl.Instance:CloseSYBattleFollow()
	local jingli  =BossWGData.Instance:GetSHenYinJinLi()
	if jingli == 0 then
		BossWGCtrl.Instance:OpenDriveView(SceneType.KFSHENYUN_FB)
	end
end

function KfShenYunSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	if self:RolePickUpFallItem() then
		return true
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
		local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
		target_obj = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
		if target_obj ~= nil and self:IsRoleEnemy(target_obj, main_role) then
			MainuiWGCtrl.Instance:SetTargetObj(target_obj)
			self:SetGuaiJi(GUAI_JI_TYPE.ROLE)
		end
	end


	target_obj = MainuiWGData.Instance:GetTargetObj()
	local search_boss = true
	if target_obj == nil or (target_obj ~= nil and target_obj:GetType() == SceneObjType.GatherObj) then
		for k, v in pairs(KfShenYunSceneLogic.TREASURECRYTAL) do
			local gather_obj = Scene.Instance:SelectMinDisGather(v)
			print_error(gather_obj)
			if nil ~= gather_obj then
				local obj_cfg = BossWGData.Instance:GetTuTengObj(gather_obj:GetObjId())
				if obj_cfg.owner_id == 0 then
					MainuiWGCtrl.Instance:SetTargetObj(gather_obj)
					self:MoveToObj(gather_obj)
					search_boss = false
				end
			end
		end
	end


	target_obj = MainuiWGData.Instance:GetTargetObj()
	if  (target_obj ~= nil and target_obj:GetType() ~= SceneObjType.GatherObj) or (search_boss or target_obj == nil)  then
		BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
		return
	end
end

-- 是否是挂机打怪的敌人
function KfShenYunSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj)  then
		return false
	end
	return true
end

-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function KfShenYunSceneLogic:GetGuajiCharacter()
	local target_obj
	target_obj = self:GetNormalRole()
	if target_obj ~= nil then
		return target_obj
	end
	-- target_obj = self:GetCanGatherList()
	-- if target_obj ~= nil then
	-- 	return target_obj
	-- end
	if target_obj == nil then
		return self:GetMonster()
	end
end

function KfShenYunSceneLogic:GetCanGatherList()
	local gather_list = Scene.Instance:GetGatherList()
	for k,v in pairs(gather_list) do
		if self:CheckHasOwner(v.vo.obj_id) then
			return v
		end
	end
end

function KfShenYunSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			GuajiCache.target_obj = v
			return v
		end
	end
end

function KfShenYunSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()
		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function KfShenYunSceneLogic:CheckHasOwner(id)
	local info = BossWGData.Instance:GetTuTengObj(id)
	local self_obj_id = Scene.Instance:GetMainRole().vo.obj_id
	return info.owner_id == 0
end

function KfShenYunSceneLogic:Update( now_time, elapse_time)
	BaseSceneLogic.Update(self,now_time,elapse_time)
	if GuajiCache.guaji_type == GuajiType.Auto then
		-- local cache = MoveEndType.AttackTarget
		-- local obj = self:GetNormalRole()
		-- if obj == nil then
			local obj = self:GetCanGatherList()
			if obj then
				-- cache = MoveEndType.Gather
				GuajiWGCtrl.Instance:StopGuaji()
		-- 	end
		-- end
		-- if obj then
			-- GuajiWGCtrl.Instance:StopGuaji()
			MoveCache.SetEndType(MoveEndType.Gather)
			MoveCache.target_obj = obj
			-- GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
			-- 		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			-- end)
			GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(),obj.vo.pos_x,obj.vo.pos_y,COMMON_CONSTS.GATHER_TRIIGER_RANGE)
		end
	end
end

function KfShenYunSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	return BossWGData.Instance:FilterMapShenYunMonster(), true
end

function KfShenYunSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

function KfShenYunSceneLogic:GetFbSceneMonsterCfg()
	local list = BossWGData.Instance:FilterMapShenYunMonster()
	return list,#list
end
function KfShenYunSceneLogic:CheckPlayerIsDie(obj_dead)
	--print_error(obj_dead:GetObjId())
	if not IS_ON_CROSSSERVER or obj_dead:GetType() == SceneObjType.Monster then return end
	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.obj_id == obj_dead:GetObjId() then
		local role_obj = Scene.Instance:GetMainRole()
		if not role_obj or not role_obj:GetFollowUi() then return end
		role_obj:GetFollowUi():SetSpecialImage(false)

	else
		local obj = Scene.Instance:GetRoleByObjId(obj_dead:GetObjId())
		if obj then
			obj:GetFollowUi():SetSpecialImage(false)
		end
	end
end
