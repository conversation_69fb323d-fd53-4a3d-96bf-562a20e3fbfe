-- 红点相关的内容，应该写在单独一个类，注意红点分暗器部分和软甲部分
HiddenWeaponRemindManager = HiddenWeaponRemindManager or BaseClass()

function HiddenWeaponRemindManager:__init()
    RemindManager.Instance:Register(RemindName.ShenJiEquip_BetterEquip1, BindTool.Bind(self.HasBetterEquip, self, 1))
    RemindManager.Instance:Register(RemindName.ShenJiEquip_BetterEquip2, BindTool.Bind(self.HasBetterEquip, self, 2))
    RemindManager.Instance:Register(RemindName.ShenJiEquip_KM1, BindTool.Bind(self.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self, 1))
    RemindManager.Instance:Register(RemindName.ShenJiEquip_KM2, BindTool.Bind(self.IsKemingEnough, self, 2))

    RemindManager.Instance:Register(RemindName.ShenJiEquip_ZL1, BindTool.Bind(self.CanZL, self, 1))
    RemindManager.Instance:Register(RemindName.ShenJiEquip_ZL2, BindTool.Bind(self.CanZL, self, 2))

    RemindManager.Instance:Register(RemindName.ShenJiEquip_JJ1, BindTool.Bind(self.CanJJ, self, 1))
    RemindManager.Instance:Register(RemindName.ShenJiEquip_JJ2, BindTool.Bind(self.CanJJ, self, 2))

    RemindManager.Instance:Register(RemindName.ShenJiEquip_AWAKE1, BindTool.Bind(self.GetAwakeRemindCount, self, 1))
    RemindManager.Instance:Register(RemindName.ShenJiEquip_AWAKE2, BindTool.Bind(self.GetAwakeRemindCount, self, 2))
    RemindManager.Instance:Register(RemindName.ShenJiEquip_BUILD1, BindTool.Bind(self.CanEquipBuild, self, 1))
    RemindManager.Instance:Register(RemindName.ShenJiEquip_BUILD2, BindTool.Bind(self.CanEquipBuild, self, 2))
    RemindManager.Instance:Register(RemindName.ShenJiEquip_FENJIE, BindTool.Bind(self.GetDeComposeCount, self))

    local func = BindTool.Bind1(self.OnRemindChanged, self)
    for index, value in ipairs(RemindGroud[RemindName.ShenJiEquipSub]) do
        RemindManager.Instance:Bind(func, value)
    end
    -- 物品变化触发红点
    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.ShenJiEquip_JJ1, {29850}, nil)
    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.ShenJiEquip_JJ2, {29851}, nil)
    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.ShenJiEquip_KM1, {29852}, nil)
    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.ShenJiEquip_KM2, {29853}, nil)
    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.ShenJiEquip_AWAKE1, {29854}, nil)
    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.ShenJiEquip_AWAKE2, {29855}, nil)
    -- local list = {29850, 29851, 29852, 29853,29854,29855}
    -- BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.SiXiangCall_XQZL, list, nil)
end

-- 暗器/软甲大类别下 是否有红点
-- view_toggle_index:神技当前页签序号 判断当前页前是否需要显示左侧红点
function HiddenWeaponRemindManager:IsWeaponTypeRed(weapon_type, view_toggle_index)
    local better_equip_remind = weapon_type == 1 and RemindName.ShenJiEquip_BetterEquip1 or RemindName.ShenJiEquip_BetterEquip2
    local build_remind = weapon_type == 1 and RemindName.ShenJiEquip_BUILD1 or RemindName.ShenJiEquip_BUILD2


    if view_toggle_index == TabIndex.shenji_equip_detail then
        if RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_FENJIE) > 0 then
            return true
        end

        if RemindManager.Instance:GetRemind(better_equip_remind) > 0 then
            return true
        end

        return false
    elseif view_toggle_index == TabIndex.shenji_equip_upgrade then
        return self:IsStrenghenRed(weapon_type)
    elseif view_toggle_index == TabIndex.shenji_equip_compose then
        return RemindManager.Instance:GetRemind(build_remind) > 0
    elseif view_toggle_index then
        return false
    end

    if RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_FENJIE) > 0 then
        return true
    end

    if RemindManager.Instance:GetRemind(better_equip_remind) > 0 then
        return true
    end
    
    if self:IsStrenghenRed(weapon_type) then
        return true
    end

    if RemindManager.Instance:GetRemind(build_remind) > 0 then
        return true
    end

    return false
end

-- 强化是否有红点（注灵/进阶、刻铭、觉醒）
function HiddenWeaponRemindManager:IsStrenghenRed(weapon_type)
    if weapon_type == 1 then
        if RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_KM1) > 0 then
            return true
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_AWAKE1) > 0 then
            return true
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_ZL1) > 0 then
            return true
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_JJ1) > 0 then
            return true
        else
            return false
        end
    elseif weapon_type == 2 then
        if RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_KM2) > 0 then
            return true
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_AWAKE2) > 0 then
            return true
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_ZL2) > 0 then
            return true
        elseif RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_JJ2) > 0 then
            return true
        else
            return false
        end
    end
    return false
end

function HiddenWeaponRemindManager:OnRemindChanged(remind_name, num)
    -- 刷新界面
    if HiddenWeaponWGCtrl.Instance.view and HiddenWeaponWGCtrl.Instance.view:IsOpen() and HiddenWeaponWGCtrl.Instance.view:IsLoaded() then
        HiddenWeaponWGCtrl.Instance.view:RefreshSubView("grid")
    end
    -- 判断功能开启
    local is_fun_open = FunOpen.Instance:GetFunIsOpened(FunName.ShenJi)
    if not is_fun_open then
        return
    end

    local repetition_num = 0
    if num > 0 then
        repetition_num = 999
    end
    if remind_name == RemindName.ShenJiEquip_BetterEquip then
        local weapon_type = 1
        if RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_BetterEquip1) > 0 then
            weapon_type = 1
        else
            weapon_type = 2
        end
        MainuiWGCtrl.Instance:InvateTip(
            MAINUI_TIP_TYPE.SHENJI_EQUIP_DETAIL,
            repetition_num,
            function()
                FunOpen.Instance:OpenViewByName(GuideModuleName.HiddenWeaponView, 1, {weapon_type = weapon_type})
                return true
            end
        )
    elseif remind_name == RemindName.ShenJiEquip_KM then
        local weapon_type = 1
        if RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_KM1) > 0 then
            weapon_type = 1
        else
            weapon_type = 2
        end
        MainuiWGCtrl.Instance:InvateTip(
            MAINUI_TIP_TYPE.SHENJI_EQUIP_KM,
            repetition_num,
            function()
                FunOpen.Instance:OpenViewByName(
                    GuideModuleName.HiddenWeaponView,
                    2,
                    {weapon_type = weapon_type, sub_type = 2}
                )
                return true
            end
        )
    elseif remind_name == RemindName.ShenJiEquip_BUILD then
        local weapon_type = 1
        if RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_BUILD1) > 0 then
            weapon_type = 1
        else
            weapon_type = 2
        end
        MainuiWGCtrl.Instance:InvateTip(
            MAINUI_TIP_TYPE.SHENJI_EQUIP_BUILD,
            repetition_num,
            function()
                FunOpen.Instance:OpenViewByName(GuideModuleName.HiddenWeaponView, 3, {weapon_type = weapon_type})
                return true
            end
        )
    elseif remind_name == RemindName.ShenJiEquip_AWAKE then
        local weapon_type = 1
        if RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_AWAKE1) > 0 then
            weapon_type = 1
        else
            weapon_type = 2
        end
        MainuiWGCtrl.Instance:InvateTip(
            MAINUI_TIP_TYPE.SHENJI_EQUIP_AWAKE,
            repetition_num,
            function()
                FunOpen.Instance:OpenViewByName(
                    GuideModuleName.HiddenWeaponView,
                    2,
                    {weapon_type = weapon_type, sub_type = 3}
                )
                return true
            end
        )
    elseif remind_name == RemindName.ShenJiEquip_JJ then
        local weapon_type = 1
        if RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_JJ1) > 0 then
            weapon_type = 1
        else
            weapon_type = 2
        end
        MainuiWGCtrl.Instance:InvateTip(
            MAINUI_TIP_TYPE.SHENJI_EQUIP_ZL2,
            repetition_num,
            function()
                FunOpen.Instance:OpenViewByName(
                    GuideModuleName.HiddenWeaponView,
                    2,
                    {weapon_type = weapon_type, sub_type = 1}
                )
                return true
            end
        )
    elseif remind_name == RemindName.ShenJiEquip_ZL then
        local re_num = 0
        local weapon_type = 1
        local zl_num1 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_ZL1)
        local zl_num2 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_ZL2)
        if zl_num1 >= 100 then
            re_num = 999
            weapon_type = 1
        elseif zl_num2 >= 100 then
            re_num = 999
            weapon_type = 2
        else
            re_num = 0
        end
        MainuiWGCtrl.Instance:InvateTip(
            MAINUI_TIP_TYPE.SHENJI_EQUIP_ZL,
            re_num,
            function()
                FunOpen.Instance:OpenViewByName(
                    GuideModuleName.HiddenWeaponView,
                    2,
                    {weapon_type = weapon_type, sub_type = 1}
                )
                return true
            end
        )
    end
end

function HiddenWeaponRemindManager:__delete()
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_BetterEquip1)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_BetterEquip2)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_FENJIE)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_KM1)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_KM2)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_JJ2)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_JJ1)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_ZL2)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_ZL1)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_AWAKE2)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_AWAKE1)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_BUILD2)
    RemindManager.Instance:UnRegister(RemindName.ShenJiEquip_BUILD1)
end

function HiddenWeaponRemindManager:FireAllRemind()
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_BetterEquip2)
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_BetterEquip1)
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_FENJIE)

    RemindManager.Instance:Fire(RemindName.ShenJiEquip_KM2)
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_KM1)

    RemindManager.Instance:Fire(RemindName.ShenJiEquip_JJ2)
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_JJ1)
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_ZL2)
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_ZL1)
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_AWAKE2)
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_AWAKE1)

    RemindManager.Instance:Fire(RemindName.ShenJiEquip_BUILD2)
    RemindManager.Instance:Fire(RemindName.ShenJiEquip_BUILD1)

    RemindManager.Instance:Fire(RemindName.SiXiangCall_XQZL)
end

-- 包括神机现世，一般用于背包红点
function HiddenWeaponRemindManager:GetRemindCounts()
    local xianshi_remind = RemindManager.Instance:GetRemind(RemindName.ShenJiXianShi)
    if xianshi_remind > 0 then
        return 1
    end
    return self:GetEquipRemindCounts()
end

-- 神机装备内红点，判断暗器和灵甲
function HiddenWeaponRemindManager:GetEquipRemindCounts()
    local red1 = HiddenWeaponRemindManager:IsWeaponTypeRed(1)
    local red2 = HiddenWeaponRemindManager:IsWeaponTypeRed(2)
    if red1 == true or red2 == true then
        return 1
    end
    return 0
end

-- 是否有更好的装备
function HiddenWeaponRemindManager:HasBetterEquip(weapon_type)
    if self:PreCheck() == false then
        return 0
    end

    local has_better = false
    -- 当前装备
    local equips = HiddenWeaponWGData.Instance:GetSCShenJiEquipGrid()
    local equiped = equips[weapon_type]
    -- 背包装备
    local bag_list = HiddenWeaponWGData.Instance:GetDataList(weapon_type)
    for index, value in ipairs(bag_list) do
        if value.equip.big_type == weapon_type and value.is_better == true then
            return 1
        end
    end

    return 0
end

-- 是否能注灵（当前暗器、灵甲精华不为0时，且暗器、灵甲注灵等级未满级，且当前未非进阶阶段）
-- 当精华数量足够注灵至下一级时，返回>100。（只有当精华数量足够注灵至下一级时，才需要同步至变强“神机注灵”）
-- 只是能注灵，返回1。不能注灵，返回0
function HiddenWeaponRemindManager:CanZL(weapon_type)
    if self:PreCheck() == false then
        return 0
    end

    local equips = HiddenWeaponWGData.Instance:GetSCShenJiEquipGrid()
    local equiped = equips[weapon_type]
    if equiped == nil or equiped.equip == nil or equiped.protocol_equip_item == nil then
        return 0
    end
    -- 是否满级
    local node_type = equiped.equip.node_type
    local max_lv = HiddenWeaponWGData.Instance:GetZLAttrMaxLv(weapon_type, node_type)
    local cur_lv = equiped.protocol_equip_item.level
    if max_lv == nil or cur_lv == nil then
        return 0
    end
    local is_max = (cur_lv >= max_lv)
    if is_max then
        return 0
    end

    -- 进阶中，返回
    if HiddenWeaponWGData.Instance:IsCanUpGrade(weapon_type) == true then
        return 0
    end
    -- 是否能升一级
    local can_up_level = true

    local cfg = HiddenWeaponWGData.Instance:GetUpLevelCfg(weapon_type, node_type, cur_lv + 1)
    if cfg == nil then
        return 0
    end
    local need_exp = (cfg.need_exp or 0) - (equiped.protocol_equip_item.next_lv_add_exp or 0)
    local has_exp = equiped.protocol_equip_item.exp_value
    if need_exp < 0 or has_exp <= 0 then
         return 0
    end
    if has_exp >= need_exp then
        can_up_level = true
    else
        can_up_level = false
    end
    
    --print_log(equiped.protocol_equip_item.next_lv_add_exp .. "/" .. cfg.need_exp .. "|" .. has_exp)
    if can_up_level then
        return 100
    end
    -- 是否有经验（能注灵）

    if has_exp > 0 then
        return 1
    else
        return 0
    end
end

-- 注灵是否能进阶
function HiddenWeaponRemindManager:CanJJ(weapon_type)
    if self:PreCheck() == false then
        return 0
    end

    local equips = HiddenWeaponWGData.Instance:GetSCShenJiEquipGrid()
    local equiped = equips[weapon_type]
    if equiped == nil or equiped.equip == nil or equiped.protocol_equip_item == nil then
        return 0
    end

    if not HiddenWeaponWGData.Instance:IsCanUpGrade(weapon_type) then
        return 0
    end
    -- 判断材料是否足够
    local cur_grade = equiped.protocol_equip_item.grade
    local next_grade = cur_grade + 1
    local next_cfg = HiddenWeaponWGData.Instance:GetUpgradeCfg(weapon_type, equiped.equip.node_type, next_grade)
    local consume_item_id = next_cfg["consume_item"]
    local consume_item_num = next_cfg["consume_num"] or 999
    local has_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)
    if has_num >= consume_item_num then
        return 1
    end
    return 0
end

-- 是否能刻铭/突破
function HiddenWeaponRemindManager:IsKemingEnough(weapon_type)
    if self:PreCheck() == false then
        return 0
    end
    local equips = HiddenWeaponWGData.Instance:GetSCShenJiEquipGrid()
    local equiped = equips[weapon_type]
    if equiped == nil or equiped.equip == nil or equiped.protocol_equip_item == nil then
        return 0
    end
    -- 判断是否能刻铭
    if not HiddenWeaponWGData.Instance:CanEquipKm(equiped) then
        return 0
    end
    -- 是不是达到刻铭最大等级？
    if HiddenWeaponWGData.Instance:isKmMax(equiped.protocol_equip_item) then
        return 0
    end
    -- 先判断突破（不需要材料）
    local is_tupo = HiddenWeaponWGData.Instance:isNeedTupo(equiped.protocol_equip_item)
    if is_tupo then
        return 1
    end
    -- 是否替换中（不显示红点）
    for index, value in ipairs(equiped.protocol_equip_item.next_rand_attr_list) do
        if value > 0 or value < 0 then
            return 0
        end
    end

    -- 存在刻铭，判断材料
    local consume_cfg = HiddenWeaponWGData.Instance:GetKemingConsumeCfg(weapon_type)
    if consume_cfg and consume_cfg.consume_item then
        local has_num = ItemWGData.Instance:GetItemNumInBagById(consume_cfg.consume_item)
        if has_num >= consume_cfg.consume_num then
            return 1
        end
    end
    return 0
end

-- 觉醒红点数
function HiddenWeaponRemindManager:GetAwakeRemindCount(weapon_type)
    if self:PreCheck() == false then
        return 0
    end

    local equips = HiddenWeaponWGData.Instance:GetSCShenJiEquipGrid()
    local equiped = equips[weapon_type]
    if equiped == nil or equiped.equip == nil or equiped.protocol_equip_item == nil then
        return 0
    end
    local special_effect_level = equiped.protocol_equip_item.special_effect_level or RoleWGData.GetRoleMaxLevel()
    -- 是否满级
    local data_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(weapon_type)
    local is_max = (special_effect_level >= (#data_cfg - 1))
    if is_max then
        return 0
    end
    -- 材料是否足够
    local next_cfg = data_cfg[special_effect_level + 2]
    if next_cfg == nil then
        return 0
    end
    local consume_item_id = next_cfg["consume_item"]
    local consume_item_num = next_cfg["consume_num"] or 99999
    local has_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)
    if has_num >= consume_item_num then
        return 1
    end
    return 0
end

function HiddenWeaponRemindManager:CanEquipBuild(weapon_type)
    if self:PreCheck() == false then
        return 0
    end
    -- 需要把身上装备转成map方便读取
    -- 判断装备是否有下一级
    -- 判断是否有足够材料
    local data_list = HiddenWeaponWGData.Instance:GetDataList(weapon_type, -1)
    for index, value in ipairs(data_list) do
        if value.can_compose then
            return 1
        end
    end
    return 0
end

function HiddenWeaponRemindManager:GetDeComposeCount()
    if self:PreCheck() == false then
        return 0
    end
    if HiddenWeaponWGData.Instance:IsDataContainsDecompose() == true then
        return 1
    end
    return 0
end

function HiddenWeaponRemindManager:PreCheck()
    -- 判断功能开启
    local is_fun_open = FunOpen.Instance:GetFunIsOpened(FunName.ShenJi)
    if not is_fun_open then
        return false
    end
    return true
end
