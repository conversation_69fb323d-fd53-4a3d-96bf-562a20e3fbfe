-- /jy_gm crossflagbattlegmoperate:1 0 0 0 
-- enum CROSS_FLAG_BATTLE_GM_OPERATE_TYPE
-- {
--     CROSS_FLAG_BATTLE_GM_OPERATE_TYPE_MIN = 0,

--     CROSS_FLAG_BATTLE_GM_OPERATE_TYPE_FORCE_START,
--     CROSS_FLAG_BATTLE_GM_OPERATE_TYPE_FORCE_END,

--     CROSS_FLAG_BATTLE_GM_OPERATE_TYPE_MAX,
-- };
-- require("game/cross_flag_grabbing_battlefield/conquest_war_view")
-- require("game/cross_flag_grabbing_battlefield/flag_grabbing_battlefield_view")
require("game/cross_flag_grabbing_battlefield/flag_grabbing_battlefield_scenetop_view")
require("game/cross_flag_grabbing_battlefield/cross_flag_grabbing_battlefield_wg_data")
require("game/cross_flag_grabbing_battlefield/flag_grabbing_battlefield_task_view")
require("game/cross_flag_grabbing_battlefield/flag_grabbing_battlefield_explain_view")
require("game/cross_flag_grabbing_battlefield/flag_grabbing_battlefield_jiesuan_view")
require("game/cross_flag_grabbing_battlefield/flag_grabbing_battlefield_reward_view")
require("game/cross_flag_grabbing_battlefield/flag_grabbing_battlefield_rank_view")
require("game/cross_flag_grabbing_battlefield/flag_grabbing_battlefield_scenetop_view")
require("game/cross_flag_grabbing_battlefield/flag_grabbing_battlefield_reward_show_view")

CrossFlagGrabbingBattleFieldWGCtrl = CrossFlagGrabbingBattleFieldWGCtrl or BaseClass(BaseWGCtrl)

function CrossFlagGrabbingBattleFieldWGCtrl:__init()
    if CrossFlagGrabbingBattleFieldWGCtrl.Instance then
		print_error("[CrossFlagGrabbingBattleFieldWGCtrl]:Attempt to create singleton twice!")
	end

    CrossFlagGrabbingBattleFieldWGCtrl.Instance = self

    -- if not self.conquest_war_view then
    --     self.conquest_war_view = ConquestWarView.New(GuideModuleName.ConquestWarView)
    -- end

    if not self.fgb_task_view then
        self.fgb_task_view = FlagGrabbingBattleFieldTaskView.New()
    end

    if not self.fgb_explain_view then
        self.fgb_explain_view = FlagGrabbingBattleFieldExplainView.New()
    end

    if not self.fgb_jiesuan_view then
        self.fgb_jiesuan_view = FlagGrabbingBattleFieldJieSuanView.New()
    end

    if not self.fgb_person_reward_view then
        self.fgb_person_reward_view = FlagGrabbingBattleFieldRewardView.New()
    end

    if not self.fgb_rank_view then
        self.fgb_rank_view = FlagGrabbingBattleFieldRankView.New()
    end

    if not self.fgb_scenetop_view then
        self.fgb_scenetop_view = FlagGrabbingBattleFieldSceneTopView.New()
    end

    if not self.fgb_reward_show_view then
        self.fgb_reward_show_view = FlagGrabbingBattleFieldRewardShowView.New()
    end

    if not self.data then
        self.data = CrossFlagGrabbingBattleFieldWGData.New()
    end

    self.is_end = false
    self:RegisterAllProtocols()
end

function CrossFlagGrabbingBattleFieldWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSCrossFlagBattleOperate)
	self:RegisterProtocol(SCCrossFlagBattleRoomInfo, "OnSCCrossFlagBattleRoomInfo")
    self:RegisterProtocol(SCCrossFlagBattleBaseInfo, "OnSCCrossFlagBattleBaseInfo")
    self:RegisterProtocol(SCCrossFlagBattleScoreRank, "OnSCCrossFlagBattleScoreRank")
    self:RegisterProtocol(SCCrossFlagBattleContendInfo, "OnSCCrossFlagBattleContendInfo")
    self:RegisterProtocol(SCCrossFlagBattleKillInfo, "OnSCCrossFlagBattleKillInfo")
    self:RegisterProtocol(SCCrossFlagBattleRoomEnd, "OnSCCrossFlagBattleRoomEnd")
    self:RegisterProtocol(SCCrossFlagBattleSceneInfo, "OnSCCrossFlagBattleSceneInfo")

    self:RegisterProtocol(SCCrossFlagBattleGatherInfo, "OnSCCrossFlagBattleGatherInfo")
    self:RegisterProtocol(SCCrossFlagBattleGatherRemove, "OnSCCrossFlagBattleGatherRemove")
end

function CrossFlagGrabbingBattleFieldWGCtrl:__delete()
    CrossFlagGrabbingBattleFieldWGCtrl.Instance = nil
    -- if self.conquest_war_view then
    --     self.conquest_war_view:DeleteMe()
    --     self.conquest_war_view = nil
    -- end

    if self.fgb_task_view then
        self.fgb_task_view:DeleteMe()
        self.fgb_task_view = nil
    end

    if self.fgb_explain_view then
        self.fgb_explain_view:DeleteMe()
        self.fgb_explain_view = nil
    end

    if self.fgb_jiesuan_view then
        self.fgb_jiesuan_view:DeleteMe()
        self.fgb_jiesuan_view = nil
    end

    if self.fgb_person_reward_view then
        self.fgb_person_reward_view:DeleteMe()
        self.fgb_person_reward_view = nil
    end

    if self.fgb_rank_view then
        self.fgb_rank_view:DeleteMe()
        self.fgb_rank_view = nil
    end
    
    if self.fgb_scenetop_view then
        self.fgb_scenetop_view:DeleteMe()
        self.fgb_scenetop_view = nil
    end

    if self.fgb_reward_show_view then
        self.fgb_reward_show_view:DeleteMe()
        self.fgb_reward_show_view = nil
    end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    self.is_end = nil
end

----------------------------------protocol_start----------------------------------
function CrossFlagGrabbingBattleFieldWGCtrl:OnCSCrossFlagBattleOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossFlagBattleOperate)
	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol.param3 = param3 or 0
 	protocol:EncodeAndSend()
end

--如果room_id大于0且end_time等于0的时候,代表已匹配未开始
function CrossFlagGrabbingBattleFieldWGCtrl:OnSCCrossFlagBattleRoomInfo(protocol)
    local old_fgb_state = self.data:GetMyFGBState()
    self.data:SetFGBRoomInfo(protocol)
    local new_fgb_state = self.data:GetMyFGBState()
    local act_is_ready = (old_fgb_state == CROSS_FLAG_BATTLE_STATE_TYPE.MATCHING or old_fgb_state == CROSS_FLAG_BATTLE_STATE_TYPE.NOMATCH) and 
        new_fgb_state == CROSS_FLAG_BATTLE_STATE_TYPE.MATCHSUC

    if act_is_ready then
        self.is_end = false
        local auto_enter_time = self.data:GetFGBAutoEnterTime()

        TipWGCtrl.Instance:OpenSecondConfirmationView(Language.FlagGrabbingBattlefield.FGBActivityEnterTips, function ()
            CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD)
        end, nil, Language.Common.ActivityPosStr[5], auto_enter_time)
    end

    RemindManager.Instance:Fire(RemindName.CrossFGB)
    ViewManager.Instance:FlushView(GuideModuleName.ConquestWarView, TabIndex.country_map_flag_grabbing_battlefield)

    if self.fgb_scenetop_view and self.fgb_scenetop_view:IsOpen() then
        self.fgb_scenetop_view:Flush()
    end

    if self.fgb_task_view and self.fgb_task_view:IsOpen() then
        self.fgb_task_view:Flush()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OnSCCrossFlagBattleBaseInfo(protocol)
    local old_score = self.data:GetFGBCurScore()
    self.data:SetFGBBaseInfo(protocol)
    local new_score = protocol.score

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.CROSS_FLAG_GRABBING_BATTLEFIELD then
        if new_score > old_score then
            local score_desc = string.format(Language.FlagGrabbingBattlefield.FGBScoreGetDesc, new_score - old_score)
            TipWGCtrl.Instance:ShowNumberMsg(score_desc, 0.1, Vector2(245, 200), nil, nil, true)
        end
    end

    local logic = Scene.Instance:GetSceneLogic()
    if logic and logic.UpdateRoleFGBInfo then
        logic:UpdateRoleFGBInfo()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OnSCCrossFlagBattleScoreRank(protocol)
    self.data:SetFGBScoreRank(protocol)

    if self.fgb_rank_view and self.fgb_rank_view:IsOpen() then
        self.fgb_rank_view:Flush()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OnSCCrossFlagBattleContendInfo(protocol)
    local old_contend_list = self.data:GetFGBContendInfo()
    self.data:SetFGBContendInfo(protocol)
    self:CheckFGBGetContendMsg(old_contend_list, protocol.contend_list)
    local scene_type = Scene.Instance:GetSceneType()

    if scene_type == SceneType.CROSS_FLAG_GRABBING_BATTLEFIELD then
        local scene_logic = Scene.Instance:GetSceneLogic()
        
        if scene_logic then
            scene_logic:UpdateFGBEffect(old_contend_list, protocol.contend_list)
        end
    end

    if self.fgb_task_view and self.fgb_task_view:IsOpen() then
        self.fgb_task_view:Flush()
    end

    if self.fgb_scenetop_view and self.fgb_scenetop_view:IsOpen() then
        self.fgb_scenetop_view:Flush(0, "flush_contend")
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:CheckFGBGetContendMsg(old_contend_list, new_contend_list)
    if IsEmptyTable(old_contend_list) then
        return
    end

    local function show_zc_rune_msg(point_id, team_id, contend_name)
        local team_old_value1 = ((old_contend_list[point_id] or {}).camp_value_list or {})[team_id] or 0
        local team_new_value1 = ((new_contend_list[point_id] or {}).camp_value_list or {})[team_id] or 0

        if team_old_value1 <= 0 and team_new_value1 > 0 then
            local tram_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCampCfgByCampId(team_id)
			local team_name = tram_cfg and tram_cfg.camp_name or ""
            --TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.OccupyTheContentionPoint, team_name, contend_name))
            self:AddFGBGetContendMsg({team_id = team_id, seq = point_id})
        end
    end

    for i = 0, 2 do
        local contend_cfg = self.data:GetFGBContendBySeq(i)
        local contend_name = contend_cfg.contend_name
        show_zc_rune_msg(i, 0, contend_name)
        show_zc_rune_msg(i, 1, contend_name)
	end
end

function CrossFlagGrabbingBattleFieldWGCtrl:AddFGBGetContendMsg(info)
	if not self.fgb_msg_list then
		self.fgb_msg_list = {}
	end

    table.insert(self.fgb_msg_list, info)

	if nil == self.show_fgb_msg_delay then
		self:ShowFGBGetContendMsg()
	end
end

function CrossFlagGrabbingBattleFieldWGCtrl:ShowFGBGetContendMsg()
    local param = table.remove(self.fgb_msg_list, 1)

	if param == nil then
		self.show_fgb_msg_delay = nil
		return
	end

    if self.fgb_scenetop_view and self.fgb_scenetop_view:IsOpen() then
        self.fgb_scenetop_view:ShowFGBMsg(param)
    end

	if not self.show_fgb_msg_delay then
		self.show_fgb_msg_delay = GlobalTimerQuest:AddDelayTimer(function()
			self.show_fgb_msg_delay = nil
			self:ShowFGBGetContendMsg()
		end, 1.5)
	end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OnSCCrossFlagBattleKillInfo(protocol)
    local combo_kill_num = protocol.combo_kill_num
    local kill_player = protocol.kill_player
    local dead_player = protocol.dead_player

    local info = {
        param = combo_kill_num,
        killer_name = kill_player.name,
        killer_sex = kill_player.sex,
        killer_uid = kill_player.uuid,
        killer_prof = kill_player.prof,
        killer_is_red_side = 0,
        target_name = dead_player.name,
        target_sex = dead_player.sex, 
        target_prof = dead_player.prof, 
        target_uid = dead_player.uuid,
        target_is_red_side = 1,
        }

    TipWGCtrl.Instance:AddZCRuneMsgCenter(info)
end


function CrossFlagGrabbingBattleFieldWGCtrl:OnSCCrossFlagBattleRoomEnd(protocol)
    self.is_end = true
    CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBJieSuanView(protocol)
end

function CrossFlagGrabbingBattleFieldWGCtrl:OnSCCrossFlagBattleSceneInfo(protocol)
    self.data:SetFGBSceneInfo(protocol)

    if self.fgb_task_view and self.fgb_task_view:IsOpen() then
        self.fgb_task_view:Flush()
    end

    if self.fgb_scenetop_view and self.fgb_scenetop_view:IsOpen() then
        self.fgb_scenetop_view:Flush(0, "flush_time")
    end

    RemindManager.Instance:Fire(RemindName.CrossFGB)
end

function CrossFlagGrabbingBattleFieldWGCtrl:GetIsEnd()
    return self.is_end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OnSCCrossFlagBattleGatherInfo(protocol)
    self.data:SetFGBMapGatherInfo(protocol)
    MapWGCtrl.Instance:FlushMapLocalView("gather_icon")
end

function CrossFlagGrabbingBattleFieldWGCtrl:OnSCCrossFlagBattleGatherRemove(protocol)
    self.data:UpdateFGBMapGatherInfo(protocol)
    MapWGCtrl.Instance:FlushMapLocalView("gather_icon")
end

------------------------------------protocol_end----------------------------------
function CrossFlagGrabbingBattleFieldWGCtrl:OpenFGBExplainView(need_count_down, ignore_jump)
    local function flush_fgb_explain_view()
        if self.fgb_explain_view and not self.fgb_explain_view:IsOpen() then
            self.fgb_explain_view:Open()
            -- self.fgb_explain_view:FlushCountDown(need_count_down)
            self.fgb_explain_view:Flush(0, "flush_count_dowm", {need_count_down = need_count_down})
        end
    end

    local ignore_jump = ignore_jump or false
    if not ignore_jump and self.data:NeedJumpFGBExplainView() then
        if not self.data:IsFGBExplainToday() then
            flush_fgb_explain_view()
        end
    else
        flush_fgb_explain_view()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OpenFGBJieSuanView(data_info)
    if self.fgb_jiesuan_view and not self.fgb_jiesuan_view:IsOpen() then
        self.fgb_jiesuan_view:SetData(data_info)
        self.fgb_jiesuan_view:Open()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OpenFGBPersonRewardView(is_show_score)
    if self.fgb_person_reward_view and not self.fgb_person_reward_view:IsOpen() then
        self.fgb_person_reward_view:SetData(is_show_score)
        self.fgb_person_reward_view:Open()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OpenFGBRewardShowView()
    if self.fgb_reward_show_view and not self.fgb_reward_show_view:IsOpen() then
        self.fgb_reward_show_view:Open()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OpenFGBRankView()
    if self.fgb_rank_view and not self.fgb_rank_view:IsOpen() then
        self.fgb_rank_view:Open()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:CloseFGBRankView()
    if self.fgb_rank_view and self.fgb_rank_view:IsOpen() then
        self.fgb_rank_view:Close()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:GetFGBRankView()
    return self.fgb_rank_view
end

function CrossFlagGrabbingBattleFieldWGCtrl:OpenFGBSceneTopView()
    if self.fgb_scenetop_view and not self.fgb_scenetop_view:IsOpen() then
        self.fgb_scenetop_view:Open()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:CloseFGBSceneTopView()
    if self.fgb_scenetop_view and self.fgb_scenetop_view:IsOpen() then
        self.fgb_scenetop_view:Close()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:GetFGBSceneTopView()
    return self.fgb_scenetop_view
end

function CrossFlagGrabbingBattleFieldWGCtrl:OpenFGBTaskView()
    if self.fgb_task_view and not self.fgb_task_view:IsOpen() then
        self.fgb_task_view:Open()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:CloseFGBTaskView()
    if self.fgb_task_view and self.fgb_task_view:IsOpen() then
        self.fgb_task_view:Close()
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:GetFGBTaskView()
    return self.fgb_task_view
end

function CrossFlagGrabbingBattleFieldWGCtrl:FGBGoToArea(area_info)
    local scene_type = Scene.Instance:GetSceneType()
    local scene_id = Scene.Instance:GetSceneId()

    if scene_type == SceneType.CROSS_FLAG_GRABBING_BATTLEFIELD then
        local split_list = string.split(area_info.pos, ",")
        local pos_x = split_list[1]
		local pos_y = split_list[2]
        local not_block = not AStarFindWay:IsBlock(pos_x, pos_y)

		if not_block then
            local call_back = function ()
                GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
            end
        
            GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
            GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, 3, nil, nil, nil, call_back)
        end
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OnEnterFGBPoint(seq)
    if self.fgb_scenetop_view and self.fgb_scenetop_view:IsOpen() then
        self.fgb_scenetop_view:OnEnterFGBPoint(seq)
    end
end

function CrossFlagGrabbingBattleFieldWGCtrl:OnOutFGBPoint(seq)
    if self.fgb_scenetop_view and self.fgb_scenetop_view:IsOpen() then
        self.fgb_scenetop_view:OnOutFGBPoint(seq)
    end
end

