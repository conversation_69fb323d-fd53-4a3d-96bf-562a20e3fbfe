-- 选择组队目标
NewTeamChangeGoalView = NewTeamChangeGoalView or BaseClass(SafeBaseView)
function NewTeamChangeGoalView:__init()
	--self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(814, 578)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_change_goal")
	self:SetMaskBg(true)
	self.select_index = 0
	self.min_level = 0
	self.max_level = 0
	self.select_btn_index = 1
	self.select_btn_index2 = 1
	self.list_view = {}
	self.team_goal_list2 = nil
	self.team_goal_list = nil
	self.goal_cfg = nil
	self.min_value = 0
	self.max_value = 0
end

function NewTeamChangeGoalView:__delete()
	self.list_view = {}
end

function NewTeamChangeGoalView:ReleaseCallBack()
	if self.radio_btn then
		self.radio_btn:DeleteMe()
		self.radio_btn = nil
	end
	for k, v in pairs(self.list_view) do
		v:DeleteMe()
	end
    self.is_pingtai = false

    -- if self.team_goal_list then
	-- 	for k,v in pairs(self.team_goal_list) do
	-- 		if v then
	-- 			v:DeleteMe()
	-- 		end
	-- 	end
	-- 	self.team_goal_list = nil
	-- end

	if nil ~= self.team_goal_list then
        self.team_goal_list:DeleteMe()
        self.team_goal_list = nil
    end

	if nil ~= self.team_goal_list2 then
        self.team_goal_list2:DeleteMe()
        self.team_goal_list2 = nil
    end

	if self.toggle_list then
		self.toggle_list = {}
	end

	self.zhedie_index_list = nil
	self.goal_cfg = nil
end

function NewTeamChangeGoalView:CloseCallBack()
	self.min_value = 0
	self.max_value = 0
end

function NewTeamChangeGoalView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["size"])
    --self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    --self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.node_list.title_view_name.text.text = Language.NewTeam.TitleChangeGoal2
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list.close_progress, BindTool.Bind(self.OnClickCloseProgress, self))
	--XUI.AddClickEventListener(self.node_list["btn_sub"], BindTool.Bind2(self.OnClickSub, self, 1))
	--XUI.AddClickEventListener(self.node_list["btn_add"], BindTool.Bind2(self.OnClickAdd, self, 1))
	--XUI.AddClickEventListener(self.node_list["btn_sub1"], BindTool.Bind2(self.OnClickSub, self, 2))
	--XUI.AddClickEventListener(self.node_list["btn_add1"], BindTool.Bind2(self.OnClickAdd, self, 2))

	self.node_list["slider"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnMinLevelValueChange, self))
	self.node_list["slider_1"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnMaxLevelValueChange, self))
	self:InItBtnList()
end

function NewTeamChangeGoalView:OnFlush()
	self:FlushLevelLimit()
end

function NewTeamChangeGoalView:ShowIndexCallBack()
	self:OnClickSelectGoal()
end

function NewTeamChangeGoalView:SetIsPingTai(is_pingtai)
	self.is_pingtai = is_pingtai
end

-- function NewTeamChangeGoalView:OnClickSub(index)
-- 	local role_min_level, role_max_level = NewTeamWGData.Instance:GetCurTeamLimitLevel()
-- 	if self.is_pingtai then
-- 		role_min_level, role_max_level = NewTeamWGData.Instance:GetPingTaiCurTeamLimitLevel()
-- 	end
-- 	local level_dis = role_max_level - role_min_level
-- 	if 1 == index then
-- 		local team_target_cfg = self.cur_select_target--self.team_target_cfg[self.select_index]
-- 		self.min_level = self.min_level - 1 <= role_min_level and role_min_level or self.min_level - 1
-- 		self.node_list["slider"].slider.value = (self.min_level - role_min_level) / level_dis
-- 	else
-- 		local team_target_cfg = self.cur_select_target--self.team_target_cfg[self.select_index]
-- 		self.max_level = self.max_level - 1 <= role_min_level and role_min_level or self.max_level - 1
-- 		self.node_list["slider_1"].slider.value = (self.max_level - role_min_level) / level_dis
-- 	end
-- end

-- function NewTeamChangeGoalView:OnClickAdd(index)
-- 	local role_min_level, role_max_level = NewTeamWGData.Instance:GetCurTeamLimitLevel()
-- 	if self.is_pingtai then
-- 		role_min_level, role_max_level = NewTeamWGData.Instance:GetPingTaiCurTeamLimitLevel()
-- 	end
-- 	local level_dis = role_max_level - role_min_level
-- 	if 1 == index then
-- 		local team_target_cfg = self.cur_select_target--self.team_target_cfg[self.select_index]
-- 		self.min_level = self.min_level + 1 >= role_max_level and role_max_level or self.min_level + 1
-- 		self.node_list["slider"].slider.value = (self.min_level - role_min_level) / level_dis
-- 	else
-- 		local team_target_cfg = self.cur_select_target--self.team_target_cfg[self.select_index]
-- 		self.max_level = self.max_level + 1 >= role_max_level and role_max_level or self.max_level + 1
-- 		self.node_list["slider_1"].slider.value = (self.max_level - role_min_level) / level_dis
-- 	end
-- end

function NewTeamChangeGoalView:OnMinLevelValueChange(value)
	self.min_value = value
	local role_max_level
	local role_min_level
	if self.goal_cfg then
		local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
		role_max_level = self.goal_cfg.role_max_level or top_user_level
		role_min_level = self.goal_cfg.role_min_level
	else
		role_min_level, role_max_level = NewTeamWGData.Instance:GetCurTeamLimitLevel()
	end

	if self.is_pingtai then
		role_min_level, role_max_level = NewTeamWGData.Instance:GetPingTaiCurTeamLimitLevel()
	end
	local num = role_min_level + value * (role_max_level - role_min_level)
	self.min_level = num == 0 and role_min_level or num
	self.min_level = GameMath.Round(self.min_level)

	local is_vis, min_level = RoleWGData.Instance:GetDianFengLevel(self.min_level)
	self.node_list["lbl_role_level_min"].text.text = min_level
	self.node_list.feixian_image_min:SetActive(is_vis)
	self.node_list["lbl_role_level_min"].rect.anchoredPosition = Vector2(is_vis and 27 or 4)
	self:FlushLevelLimit()
end

function NewTeamChangeGoalView:OnMaxLevelValueChange(value)
	self.max_value = value
	local role_max_level
	local role_min_level
	if self.goal_cfg then
		local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
		role_max_level = self.goal_cfg.role_max_level or top_user_level
		role_min_level = self.goal_cfg.role_min_level
	else
		role_min_level, role_max_level = NewTeamWGData.Instance:GetCurTeamLimitLevel()
	end

	if self.is_pingtai then
		role_min_level, role_max_level = NewTeamWGData.Instance:GetPingTaiCurTeamLimitLevel()
	end
	local num = role_min_level + value * (role_max_level - role_min_level)
	self.max_level = num == 0 and role_min_level or num
	self.max_level = GameMath.Round(self.max_level)

	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.max_level)
	self.node_list["lbl_role_level_max"].text.text = role_level
	self.node_list.feixian_image_max:SetActive(is_vis)
	self.node_list["lbl_role_level_max"].rect.anchoredPosition = Vector2(is_vis and 27 or 4)
	self:FlushLevelLimit()
end

function NewTeamChangeGoalView:FlushLevelLimit()
	local str = string.format(Language.NewTeam.PTLevelLimitTop2, self.min_level, self.max_level)
	EmojiTextUtil.ParseRichText(self.node_list["level_limit_text"].emoji_text, str, 24, COLOR3B.WHITE)
end

function NewTeamChangeGoalView:OnClickConfirm()
	if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.DifferentZhandui3)
		return
	end

	if self.min_level > self.max_level then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.NewTeam.Tips))
	elseif self.min_level > RoleWGData.Instance:GetRoleLevel() then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.NewTeam.Tips2))
	else
		if self.is_pingtai then
			NewTeamWGData.Instance:SetCurFiltrateLevelLimit(self.min_level, self.max_level, true)
			local pingtai_team_type, pingtai_fb_mode = nil ~= self.select_goal_cfg and self.select_goal_cfg.team_type, self.select_goal_cfg.fb_mode or NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
			SocietyWGCtrl.Instance:SendTeamListReq(pingtai_team_type, pingtai_fb_mode)
            self:Close()
		else
			NewTeamWGData.Instance:SetTeamLimitLevel(self.min_level, self.max_level)
			if 1 == SocietyWGData.Instance:GetIsInTeam() then
				local team_type, team_mode = nil ~= self.select_goal_cfg and self.select_goal_cfg.team_type,  nil ~= self.select_goal_cfg and self.select_goal_cfg.fb_mode or NewTeamWGData.Instance:GetTeamTypeAndMode()
				NewTeamWGCtrl.Instance:SendChangeTeamLimit(team_type, team_mode, self.min_level, self.max_level)
			end
			NewTeamWGCtrl.Instance:Flush()
			self:Close()
		end
		NewTeamWGCtrl.Instance:OpenRecruitView()
	end
end

function NewTeamChangeGoalView:OnClickCloseProgress()
	self.node_list.shezhi.toggle.isOn = false
end

function NewTeamChangeGoalView:ClickMenuSubButton(data, toggle)
	if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		if data.team_type ~= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CanNotChangeGoal)
			return
		end
	end
	if SocietyWGData.Instance:GetIsInTeam() == 1 and SocietyWGData.Instance:GetIsTeamLeader() ~= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		return
	end
	if NewTeamWGData.Instance:GetIsMatching() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo2)
		return
	end

	if toggle then
		toggle.isOn = not toggle.isOn
	end
end

function NewTeamChangeGoalView:FlushAllHighLight(data)
	for k,v in pairs(self.team_goal_list) do
		v:OnSelectChange(data)
	end

	local is_zhedie = NewTeamWGData.Instance:GetIsZheDieBtn(data.team_type)
	if is_zhedie then --如果当前选中折叠菜单
		for i,v in pairs(self.toggle_list) do
			if i == data.team_type then
				if v.toggle.isOn ~= true then
					v.toggle.isOn = true
				end
			else
				if v.toggle.isOn ~= false then
					v.toggle.isOn = false
				end
			end
		end
	else
		for i,v in pairs(self.toggle_list) do
			if v.toggle then
				if v.toggle.isOn ~= false then
					v.toggle.isOn = false
				end
			end
		end
	end
end

function NewTeamChangeGoalView:OnClickNewTeamGoal2(item, cell_index, is_default, is_click)
	local btn_index = cell_index
	local goal_cfg = item.data
	self.goal_cfg = goal_cfg

	self.node_list["slider"].slider.value = 0
	self.node_list["slider_1"].slider.value = 1
	self:OnMinLevelValueChange(0)
	self:OnMaxLevelValueChange(1)

	if goal_cfg.role_min_level > RoleWGData.Instance:GetRoleLevel() then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.NewTeam.Tips1))
		return
	end
	self.select_btn_index2 = btn_index
	self.select_goal_cfg = goal_cfg

	--组队的最大等级改为等级排行的第一名
    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
    local top_lv = goal_cfg.role_max_level or top_user_level

	NewTeamWGData.Instance:SetTeamTypeAndMode(goal_cfg.team_type, goal_cfg.fb_mode)
	NewTeamWGData.Instance:SetTeamLimitLevel(goal_cfg.role_min_level, top_lv)
	NewTeamWGData.Instance:GetSetCurSelectTarget(goal_cfg)
	if 1 == SocietyWGData.Instance:GetIsInTeam() then
		NewTeamWGCtrl.Instance:SendChangeTeamLimit(goal_cfg.team_type, goal_cfg.fb_mode, goal_cfg.role_min_level, top_lv)
	end
	--self:FlushAllHighLight(item.data)
end

function NewTeamChangeGoalView:OnClickNewTeamGoal(item, cell_index, is_default, is_click)
	if item == nil then
		return
	end

	local data = {}

	local btn_index = cell_index
    if is_click and self.select_btn_index ~= nil and self.select_btn_index == btn_index then
        return
    end

	if NewTeamWGData.Instance:GetIsMatching() then
		if is_click then --只有是手动点击才弹错误码
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo2)
		else -- 匹配中，刚进来的时候也要高亮
			self:FlushAllHighLight(item.data)
		end
		return
	else
		--没在匹配中， 并且不是队长, 并且不是手动点击， （用来组队的情况下， 进来我的队伍界面，只高亮，不弹错误码）
		if SocietyWGData.Instance:GetIsInTeam() == 1 and SocietyWGData.Instance:GetIsTeamLeader() ~= 1 and not is_click then
			self:FlushAllHighLight(item.data)
			return
		end
	end

	if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		if is_click and item.data.team_type ~= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CanNotChangeGoal)
			return
		end
	end

	if SocietyWGData and SocietyWGData.Instance and 1 == SocietyWGData.Instance:GetIsInTeam() and 0 == SocietyWGData.Instance:GetIsTeamLeader() then
		if is_click then --只有是手动点击才弹错误码
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		end
		return
	end

	local goal_cfg = item.data
	if goal_cfg.team_type == GoalTeamType.QingYuanFb then
		local team_member_count = SocietyWGData.Instance:GetTeamMemberCount()
		local team_member_list = SocietyWGData.Instance:GetTeamMemberList()
		if team_member_count > QingYuanLimitCount then
			local tips_str = string.format(Language.NewTeam.OverflowMaxTeamMemberCount, goal_cfg.team_type_name or "")
			SysMsgWGCtrl.Instance:ErrorRemind(tips_str)
			return
		end
		for i,v in ipairs(team_member_list) do
			if v.level < goal_cfg.role_min_level then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.TeamMemberLevelLower)
				return
			end
		end
	end

	local is_zhedie = NewTeamWGData.Instance:GetIsZheDieBtn(item.data.team_type)
	if not is_zhedie then
		table.insert(data, item.data)
	else
		local specail_list = NewTeamWGData.Instance:GetListByTeamType(item.data.team_type)
		data = specail_list
	end

	self.team_goal_list2 = AsyncListView.New(GoalListItemRender, self.node_list.fb_type_list2)
	self.team_goal_list2:SetSelectCallBack(BindTool.Bind1(self.OnClickNewTeamGoal2, self))
	self.team_goal_list2:SetDataList(data)
	if is_zhedie and self.select_btn_index2 > 1 then
		self.team_goal_list2:JumpToIndex(self.select_btn_index2, 5)
	end

	self.select_btn_index = btn_index
end

function NewTeamChangeGoalView:InItBtnList()
	local goal_list = NewTeamWGData.Instance:GetMenuBtnList()
    if not self.toggle_list then
		self.toggle_list = {}
	end

	if not self.zhedie_index_list then
		self.zhedie_index_list = {}
	end
    self.btn_gameObject_list = {}

	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()

	if now_team_type == 0 and now_fb_mode == 0 then
		now_team_type = 0
		now_fb_mode = 1
	end

	self.myteam_total_complete_count = #goal_list 	--加载总数
	self.cur_complete_count = 0 			--当前加载数

	self.team_goal_list = AsyncListView.New(GoalListItemRender, self.node_list.fb_type_list)
	self.team_goal_list:SetSelectCallBack(BindTool.Bind1(self.OnClickNewTeamGoal, self))
	self.team_goal_list:SetDataList(goal_list)

	-- local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()

	-- if now_team_type == 0 and now_fb_mode == 0 then
	-- 	now_team_type = 0
	-- 	now_fb_mode = 1
	-- end
	for i, v in ipairs(goal_list) do
		if now_team_type >= 0 and v.team_type == now_team_type and v.fb_mode == now_fb_mode then
			self.select_btn_index = i
			break
		end
	end

	local is_zhedie = NewTeamWGData.Instance:GetIsZheDieBtn(goal_list[self.select_btn_index].team_type)
	if is_zhedie then
		local specail_list = NewTeamWGData.Instance:GetListByTeamType(goal_list[self.select_btn_index].team_type)
		for i, v in ipairs(specail_list) do
			if v.fb_mode == now_fb_mode then
				self.select_btn_index2 = i
				break
			end
		end
	end

	self.team_goal_list:JumpToIndex(self.select_btn_index, 7)
end

function NewTeamChangeGoalView:OnClickSelectGoal()
	local role_min_level, role_max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
	local min_limit_level, max_limit_level = NewTeamWGData.Instance:GetCurTeamLimitLevel()
	local level_dis = max_limit_level - min_limit_level

	if self.is_pingtai then
		role_min_level, role_max_level = NewTeamWGData.Instance:GetCurFiltrateLevelLimit()
		local team_type, teamfb_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
		local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, teamfb_mode)
        local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级 
        local top_lv = goal_info.role_max_level or top_user_level
		if not goal_info or IsEmptyTable(goal_info) then
			min_limit_level, max_limit_level = 1, top_lv
		else
			min_limit_level, max_limit_level = goal_info.role_min_level, top_lv
		end

		level_dis = max_limit_level - min_limit_level
	end

	local min_value = (role_min_level -  min_limit_level)/ level_dis
	local max_value = (role_max_level -  min_limit_level)/ level_dis
	self.node_list["slider"].slider.value = min_value
	self.node_list["slider_1"].slider.value = max_value
	self:OnMinLevelValueChange(min_value)
	self:OnMaxLevelValueChange(max_value)

	-- self.min_level = role_min_level
	-- self.max_level = role_max_level
	-- local is_vis, min_level = RoleWGData.Instance:GetDianFengLevel(self.min_level)
	-- self.node_list["lbl_role_level_min"].text.text = min_level
	-- self.node_list.feixian_image_min:SetActive(is_vis)
	-- self.node_list["lbl_role_level_min"].rect.anchoredPosition = Vector2(is_vis and 27 or 4)

	-- local is_vis2, max_level = RoleWGData.Instance:GetDianFengLevel(self.max_level)
	-- self.node_list["lbl_role_level_max"].text.text = max_level
	-- self.node_list.feixian_image_max:SetActive(is_vis2)
	-- self.cur_select_target = NewTeamWGData.Instance:GetSetCurSelectTarget()
	-- self.node_list["lbl_role_level_max"].rect.anchoredPosition = Vector2(is_vis2 and 27 or 4)
end

function NewTeamChangeGoalView:GetLevelLimit()
	return self.min_level, self.max_level
end

------------------GoalListItem-----------------
-- GoalListItem = GoalListItem or BaseClass(BaseRender)

-- function GoalListItem:__init()
-- 	self.parent = nil
-- 	self.text = self.node_list["Text"].text
-- 	-- self.hl_text = self.node_list["text_hight"].text
-- 	self.toggle = self.node_list["btn_team_common"].toggle
-- 	self.index = 1
-- 	self.toggle:AddClickListener(BindTool.Bind(self.OnClickItem, self))
-- end

-- function GoalListItem:__delete()
-- 	self.parent = nil
-- end

-- function GoalListItem:SetIndex(index)
-- 	self.index = index
-- end

-- function GoalListItem:OnClickItem()
-- 	if not self.parent then return end
-- 	self.parent:OnClickSelectGoal()
-- end

-- function GoalListItem:SetToggle(index)
-- 	self.toggle.isOn = index == self.index
-- end

-- function GoalListItem:SetToggleGroup(toggle_group)
-- 	self.toggle.group = toggle_group
-- end

-- function GoalListItem:SetData(data)
-- 	self.text.text = data.team_type_name
-- 	-- self.hl_text.text = data[self.index].team_type_name
-- end
