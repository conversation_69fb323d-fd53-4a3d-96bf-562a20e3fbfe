local MODEL_MAX_NUM = 3 -- 模型最大数量

function WorldTreasureView:ReleaseCallBack_LimitBuy()
    if self.limit_buy_list_view then
        self.limit_buy_list_view:DeleteMe()
        self.limit_buy_list_view = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.lb_daily_gift_box_tween then
		self.lb_daily_gift_box_tween:Kill()
		self.lb_daily_gift_box_tween = nil
	end

    if self.lb_model_display1 then
		self.lb_model_display1:DeleteMe()
		self.lb_model_display1 = nil
	end

    if self.lb_model_display2 then
		self.lb_model_display2:DeleteMe()
		self.lb_model_display2 = nil
	end

    if self.lb_model_display3 then
		self.lb_model_display3:DeleteMe()
		self.lb_model_display3 = nil
	end

    if CountDownManager.Instance:HasCountDown("world_treasure_lb_discount_time_count_down") then
        CountDownManager.Instance:RemoveCountDown("world_treasure_lb_discount_time_count_down")
    end

    self:ModelCancelQuest()

    self.cur_show_index = nil
end

function WorldTreasureView:LoadIndexCallBack_LimitBuy()
    self.flash_sale1_grade_change = true
    self.cur_show_index = 1
    self.limit_buy_list_view = AsyncListView.New(WorldTreasureLimitBuyItem, self.node_list.limit_buy_list_view)

    if not self.lb_model_display1 then
        self.lb_model_display1 = OperationActRender.New(self.node_list["lb_model_display1"])
        self.lb_model_display1:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.lb_model_display2 then
        self.lb_model_display2 = OperationActRender.New(self.node_list["lb_model_display2"])
        self.lb_model_display2:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.lb_model_display3 then
        self.lb_model_display3 = OperationActRender.New(self.node_list["lb_model_display3"])
        self.lb_model_display3:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
    -- if not self.money_bar then
	-- 	self.money_bar = MoneyBar.New()
	-- 	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	-- 	local show_params = {
    --     show_gold = true, show_bind_gold = true,
    --     show_coin = true, show_silver_ticket = true,
    --     }
    --     self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	-- 	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	-- end

    XUI.AddClickEventListener(self.node_list["daily_gift_get_btn"], BindTool.Bind1(self.OnClickDailyGiftBox, self))
    XUI.AddClickEventListener(self.node_list["lb_btn_left"], BindTool.Bind(self.OnClickModelSwitch, self, true))
	XUI.AddClickEventListener(self.node_list["lb_btn_right"], BindTool.Bind(self.OnClickModelSwitch, self, false))
    self:FlushAllModelData_LimitBuy()
    self:FlushShowModel()
    self:StartQuest()
end

function WorldTreasureView:LoadLbImg()
	local bundle, asset = ResPath.GetWorldTreasureRawImages("jsrh_bg_2")
	self.node_list.lb_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.lb_bg.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_bt_2")
	self.node_list.lb_title_img.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.lb_title_img.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_js_2")
	self.node_list.lb_desc_img.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.lb_desc_img.raw_image:SetNativeSize()
	end)

    local cur_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
    self.node_list.discount_time_text.text.color = StrToColor(Language.WorldTreasure.GradeColorList2[cur_grade])
end

function WorldTreasureView:ShowIndexCallBack_LimitBuy()

end

function WorldTreasureView:FlushAllModelData_LimitBuy()
    local model_cfg = WorldTreasureWGData.Instance:GetShopModelCfg()
    for k, v in pairs(model_cfg) do
        self:FlushModelData_LimitBuy(k, v)
    end
end

function WorldTreasureView:FlushModelData_LimitBuy(index, model_cfg)
    local display_data = {}
	display_data.should_ani = true
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = model_cfg["model_bundle_name"]
    display_data.asset_name = model_cfg["model_asset_name"]
    local model_show_type = tonumber(model_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rot_list = string.split(model_cfg.rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_cfg.display_scale and model_cfg.display_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_cfg.display_scale
	end
    --display_data.model_rt_type = ModelRTSCaleType.L
    self["lb_model_display" .. index]:SetData(display_data)

    local pos_x, pos_y = 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list["lb_model_display" .. index].rect, pos_x, pos_y)
end

function WorldTreasureView:StartQuest()
    self:ModelCancelQuest()
	if not self.notice_timer_quest then
		self.notice_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.OnFlushModelSwitch, self),
			5)
	end
end

function WorldTreasureView:ModelCancelQuest()
	if self.notice_timer_quest then
		GlobalTimerQuest:CancelQuest(self.notice_timer_quest)
		self.notice_timer_quest = nil
	end
end

function WorldTreasureView:OnFlushModelSwitch()
	self.cur_show_index = self.cur_show_index < MODEL_MAX_NUM and self.cur_show_index + 1 or 1
	self:FlushShowModel()
end

function WorldTreasureView:OnClickModelSwitch(is_left)
	self.cur_show_index = is_left and math.max(self.cur_show_index - 1, 1) or math.min(self.cur_show_index + 1, MODEL_MAX_NUM)
	self:FlushShowModel()
	self:StartQuest()
end

function WorldTreasureView:FlushShowModel()
	self.node_list["lb_model_display1"]:SetActive(1 == self.cur_show_index)
	self.node_list["lb_model_display2"]:SetActive(2 == self.cur_show_index)
	self.node_list["lb_model_display3"]:SetActive(3 == self.cur_show_index)

	self.node_list["lb_btn_left"]:SetActive(1 ~= self.cur_show_index)
	self.node_list["lb_btn_right"]:SetActive(3 ~= self.cur_show_index)
end

function WorldTreasureView:OnFlush_LimitBuy(param_t, index)
    if self.flash_sale1_grade_change then
        self.flash_sale1_grade_change = false
        self:LoadLbImg()
        self:FlushAllModelData_LimitBuy()
    end
    self:UpdateLimitBuyView()
end

function WorldTreasureView:UpdateLimitBuyView()
    local list_data = WorldTreasureWGData.Instance:GetLimitBuyListData()
    self.limit_buy_list_view:SetDataList(list_data)

    -- 图片资源
	-- local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
	-- if not IsEmptyTable(grade_cfg) then
	-- 	self:SetImageRes("img_title_shop", grade_cfg.shop_title)
	-- else
    --     local grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
	-- 	print_error("未找到对应档位配置,grade:" .. grade)
	-- end

    local daily_reward_flag = WorldTreasureWGData.Instance:GetDailyRewardFlag()
    local is_remind = daily_reward_flag == 0
    self.node_list.daily_gift_root:SetActive(is_remind)
    self.node_list.daily_gift_red_point:SetActive(is_remind)

    if is_remind then
        if self.lb_daily_gift_box_tween then
            self.lb_daily_gift_box_tween:Restart()
        else
            if self.lb_daily_gift_box_tween then
                self.lb_daily_gift_box_tween:Kill()
                self.lb_daily_gift_box_tween = nil
            end
            
            self.lb_daily_gift_box_tween = DG.Tweening.DOTween.Sequence()
            UITween.ShakeAnimi(self.node_list.box_img.transform, self.lb_daily_gift_box_tween)
        end
    elseif self.lb_daily_gift_box_tween then
        self.lb_daily_gift_box_tween:Pause()
        self.node_list.box_img.transform.localRotation = Quaternion.identity
    end

    self:FlushDiscountTimeCountDown()
end

--有效时间倒计时
function WorldTreasureView:FlushDiscountTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("world_treasure_lb_discount_time_count_down")
	local discount_time = WorldTreasureWGData.Instance:GetLimitBuyDiscountTime()
	if discount_time > 0 then
		self.node_list["discount_time_text"]:SetActive(true)
		self.node_list["discount_time_text"].text.text = string.format(Language.WorldTreasure.DiscountTimeDesc, TimeUtil.FormatSecondDHM2(discount_time))
		CountDownManager.Instance:AddCountDown("world_treasure_lb_discount_time_count_down",
        BindTool.Bind1(self.LBUpdateCountDown, self),
        BindTool.Bind1(self.LBCountDownOnComplete, self),
        nil, discount_time, 1)
	else
		self.node_list["discount_time_text"]:SetActive(false)
	end
end

function WorldTreasureView:LBUpdateCountDown(elapse_time, total_time)
	self.node_list["discount_time_text"].text.text = string.format(Language.WorldTreasure.DiscountTimeDesc, TimeUtil.FormatSecondDHM2(total_time - elapse_time))
end

function WorldTreasureView:LBCountDownOnComplete()
	self.node_list["discount_time_text"]:SetActive(false)
    CountDownManager.Instance:RemoveCountDown("world_treasure_lb_discount_time_count_down")
	self:Flush()
end

function WorldTreasureView:OnClickDailyGiftBox()
    WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_SPECIAL_FETCH_DAILY_REWARD)
end

WorldTreasureLimitBuyItem = WorldTreasureLimitBuyItem or BaseClass(BaseRender)

function WorldTreasureLimitBuyItem:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind1(self.OnClickBuy, self))

    self.item_cell = ItemCell.New(self.node_list.item_parent)
end

function WorldTreasureLimitBuyItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function WorldTreasureLimitBuyItem:OnFlush()
    if not self.data then
        return
    end

    local num = WorldTreasureWGData.Instance:GetShopBuyNum(self.data.seq)
    local remain_num = self.data.buy_count_limit - num
    local btn_txt = ""
    local discount_time = WorldTreasureWGData.Instance:GetLimitBuyDiscountTime()

    if discount_time > 0 then
        self.node_list["old_price_panel"]:SetActive(true)
        self.node_list.old_price_text.text.text = string.format(Language.WorldTreasure.LimitBuyTxt9, self.data.price)
        btn_txt = RoleWGData.GetPayMoneyStr(self.data.discount_price, self.data.discount_rmb_type, self.data.rmb_seq)
    else
        self.node_list["old_price_panel"]:SetActive(false)
        btn_txt = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    end

    if remain_num <= 0 then
        btn_txt = Language.WorldTreasure.LimitBuyTxt5
    end

    self.node_list.text_price.text.text = btn_txt
    local reward_item = self.data.reward_item[0]
    self.item_cell:SetData(reward_item)
    local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
    self.node_list.text_name.text.text = item_cfg.name

    self.node_list.desc.text.text = self.data.goods_txt
    --self.node_list.text_num.text.text = string.format(Language.WorldTreasure.LimitBuyTxt1, self.data.buy_count_limit - num, self.data.buy_count_limit)
    XUI.SetButtonEnabled(self.node_list["btn_buy"], remain_num > 0)
    local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
    local buy_flag = WorldTreasureWGData.Instance:GetRmbShopBuyFlag()
    if grade_cfg.is_can_buy_one == 1 then
        if remain_num > 0 then
            self.node_list["btn_buy"]:SetActive(buy_flag == 0)
        else
            self.node_list["btn_buy"]:SetActive(true)
        end
    end

    local cur_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
    local bundle, asset = ResPath.GetWorldTreasureImg("a3_xtyg_btd_3" .. cur_grade)
	self.node_list.desc_bg.image:LoadSprite(bundle, asset)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_yq")
	self.node_list.bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.bg.raw_image:SetNativeSize()
	end)

    self.node_list.text_name.text.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
    self.node_list.desc.text.color = StrToColor(Language.WorldTreasure.GradeColorList2[cur_grade])
end

function WorldTreasureLimitBuyItem:OnClickBuy()
    local grade_cfg = WorldTreasureWGData.Instance:GetCurGradeCfg()
    local buy_flag = WorldTreasureWGData.Instance:GetRmbShopBuyFlag()
    local discount_time = WorldTreasureWGData.Instance:GetLimitBuyDiscountTime()
    local rmb_type = discount_time > 0 and self.data.discount_rmb_type or self.data.rmb_type
    local price = discount_time > 0 and self.data.discount_price or self.data.price

    if grade_cfg.is_can_buy_one == 1 then
        if buy_flag == 0 then
            WorldTreasureWGCtrl.Instance:OpenLimitBuyTipAndSetData(self.data)
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.LimitBuyTxt6)
        end
        return
    else
        local num = WorldTreasureWGData.Instance:GetShopBuyNum(self.data.seq)
        if num >= self.data.buy_count_limit then
            return
        end

        if self.data.type == 2 then
            RechargeWGCtrl.Instance:Recharge(price, rmb_type, self.data.rmb_seq)
        end
    end
end