﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class RuntimeGUIMgrWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(RuntimeGUIMgr), typeof(Nirvana.Singleton<RuntimeGUIMgr>));
		<PERSON><PERSON>Function("IsGUIOpening", IsGUIOpening);
		<PERSON><PERSON>RegFunction("IsAndroidGM", IsAndroidGM);
		<PERSON><PERSON>RegFunction("Init", Init);
		<PERSON><PERSON>RegFunction("OnGameStop", OnGameStop);
		<PERSON><PERSON>RegFunction("OnApplicationQuit", OnApplicationQuit);
		<PERSON><PERSON>RegFunction("ForceOpenGUI", ForceOpenGUI);
		<PERSON><PERSON>RegFunction("GetDataPath", GetDataPath);
		<PERSON><PERSON>Function("IsUseLocalLuaFile", IsUseLocalLuaFile);
		<PERSON><PERSON>RegFunction("IsDebugLuaAB", IsDebugLuaAB);
		<PERSON><PERSON>unction("GetCurUseSVNTime", GetCurUseSVNTime);
		<PERSON><PERSON>Function("GetNewSVNTime", GetNewSVNTime);
		<PERSON><PERSON>Function("GetCurWindowsExeSvnTime", GetCurWindowsExeSvnTime);
		L.RegFunction("GetNewWindowsExeSvnTime", GetNewWindowsExeSvnTime);
		L.RegFunction("GetGameSpeed", GetGameSpeed);
		L.RegFunction("TryOpenLuaProfiler", TryOpenLuaProfiler);
		L.RegFunction("ShowLoginWindow", ShowLoginWindow);
		L.RegFunction("SetLoadObjectCallback", SetLoadObjectCallback);
		L.RegFunction("OnLoadObject", OnLoadObject);
		L.RegFunction("ShowResourceRecorderWindow", ShowResourceRecorderWindow);
		L.RegFunction("GetDebugGUI", GetDebugGUI);
		L.RegFunction("GetGUIBlock", GetGUIBlock);
		L.RegFunction("DelAllCache", DelAllCache);
		L.RegFunction("OnGUI", OnGUI);
		L.RegFunction("Update", Update);
		L.RegFunction("New", _CreateRuntimeGUIMgr);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("isShowGMGui", get_isShowGMGui, set_isShowGMGui);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateRuntimeGUIMgr(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				RuntimeGUIMgr obj = new RuntimeGUIMgr();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: RuntimeGUIMgr.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsGUIOpening(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			bool o = obj.IsGUIOpening();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsAndroidGM(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			bool o = obj.IsAndroidGM();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Init(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			obj.Init();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGameStop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			obj.OnGameStop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnApplicationQuit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			obj.OnApplicationQuit();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceOpenGUI(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			obj.ForceOpenGUI();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDataPath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			string o = obj.GetDataPath();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsUseLocalLuaFile(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			bool o = obj.IsUseLocalLuaFile();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsDebugLuaAB(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			bool o = obj.IsDebugLuaAB();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCurUseSVNTime(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			string o = obj.GetCurUseSVNTime();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNewSVNTime(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			string o = obj.GetNewSVNTime();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCurWindowsExeSvnTime(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			string o = obj.GetCurWindowsExeSvnTime();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNewWindowsExeSvnTime(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			string o = obj.GetNewWindowsExeSvnTime();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGameSpeed(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			int o = obj.GetGameSpeed();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TryOpenLuaProfiler(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			obj.TryOpenLuaProfiler();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ShowLoginWindow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			UnityEngine.Events.UnityAction arg0 = (UnityEngine.Events.UnityAction)ToLua.CheckDelegate<UnityEngine.Events.UnityAction>(L, 2);
			obj.ShowLoginWindow(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLoadObjectCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			UnityEngine.Events.UnityAction<string,string,UnityEngine.Object> arg0 = (UnityEngine.Events.UnityAction<string,string,UnityEngine.Object>)ToLua.CheckDelegate<UnityEngine.Events.UnityAction<string,string,UnityEngine.Object>>(L, 2);
			obj.SetLoadObjectCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnLoadObject(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			string arg1 = ToLua.CheckString(L, 3);
			UnityEngine.Object arg2 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 4);
			obj.OnLoadObject(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ShowResourceRecorderWindow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			obj.ShowResourceRecorderWindow();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDebugGUI(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			DebugRuntimeGUI o = obj.GetDebugGUI();
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGUIBlock(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			RuntimeGUIBlock o = obj.GetGUIBlock();
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DelAllCache(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			obj.DelAllCache();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGUI(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			obj.OnGUI();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Update(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeGUIMgr obj = (RuntimeGUIMgr)ToLua.CheckObject<RuntimeGUIMgr>(L, 1);
			obj.Update();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isShowGMGui(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, RuntimeGUIMgr.isShowGMGui);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isShowGMGui(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			RuntimeGUIMgr.isShowGMGui = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

