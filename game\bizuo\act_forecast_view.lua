ActForecastView = ActForecastView or BaseClass(SafeBaseView)

function ActForecastView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:LoadConfig()
	self.act_type = nil
end

function ActForecastView:LoadConfig()
	self:AddViewResource(0, "uis/view/bizuo_ui_prefab", "layout_act_forecast")
end

function ActForecastView:__delete()
	self.act_type = nil

	if self.reward_list then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end

end

function ActForecastView:LoadCallBack()
	self.node_list.go_btn.button:AddClickListener(BindTool.Bind(self.OnClickGoBtn,self))
	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.OnClickCloseBtn,self))
end

function ActForecastView:SetActType(act_type)
	self.act_type = act_type
end

function ActForecastView:ShowIndexCallBack()
	self.reward_list = {}
	self:Flush()
end


function ActForecastView:ReleaseCallBack()
	if CountDown.Instance:HasCountDown(self.quest) then
        CountDown.Instance:RemoveCountDown(self.quest)
        self.quest = nil
    end
end

function ActForecastView:OnFlush()
	self.data = BiZuoWGData.Instance:GetActForecastDataByActType(self.act_type)
	local act_cfg = BiZuoWGData.Instance:GetActivityHallCfgByActType(self.act_type)
	if not self.data then return end
	self.reward_list = {}
	for k,v in pairs(self.data.reward_item) do
		self.reward_list[k] = ItemCell.New(self.node_list.reward_list)
		self.reward_list[k]:SetData(v)
	end
	local format_time = os.date("*t",TimeWGCtrl.Instance:GetServerTime())
	--當天0點的時間戳
	local today_zero_timestamp = os.time{year=format_time.year, month=format_time.month, day=format_time.day, hour=0, min = 0, sec=0}
	local temp_time_list = Split(self.data.end_time,":")
	--預告結束時間戳
	local end_timestamp = (temp_time_list[1] * 3600) + (temp_time_list[2] * 60) + temp_time_list[2]
	local remain_time = (today_zero_timestamp + end_timestamp) - TimeWGCtrl.Instance:GetServerTime()
	self.node_list.act_bg.raw_image:LoadURLSprite(ResPath.GetF2RawImagesJPG("act_forecast_bg_" .. self.act_type))
	self.node_list.text_img.image:LoadSprite(ResPath.GetCommonOthers("act_forecast_text_" .. self.act_type))

	if CountDown.Instance:HasCountDown(self.quest) then
        CountDown.Instance:RemoveCountDown(self.quest)
        self.quest = nil
    end

    self.node_list.timer_value.text.text = string.format(Language.BiZuo.ActOpenTime, self.data.act_name, act_cfg.open_time)

	self:ChangeTime(0, remain_time)
    self.quest = CountDown.Instance:AddCountDown(remain_time, 1, BindTool.Bind(self.ChangeTime, self),BindTool.Bind(self.CompleteTime, self))
end

function ActForecastView:ChangeTime(elapse_time, total_time)
	-- if self.node_list.timer_value then
 --    	self.node_list.timer_value.text.text = string.format(Language.BiZuo.ActOpenCountDown, self.data.act_name, TimeUtil.FormatSecond(math.floor(total_time - elapse_time)))
 --    end
end

function ActForecastView:CompleteTime()
    self:Close()
end

function ActForecastView:OnClickGoBtn()
	local temp_list = Split(self.data.view_path,"#")
	local view_name = temp_list[1]
	local tab_index = temp_list[2] or 0
	ViewManager.Instance:Open(GuideModuleName[view_name],TabIndex[tab_index])
	self:Close()
end

function ActForecastView:OnClickCloseBtn()
	self:Close()
end

function ActForecastView:CloseCallBack()
	if self.reward_list then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end
end
