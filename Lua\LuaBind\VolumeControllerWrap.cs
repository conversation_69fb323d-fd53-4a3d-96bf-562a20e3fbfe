﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class VolumeControllerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(VolumeController), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("GetVolume", GetVolume);
		<PERSON><PERSON>unction("SetVolumeEnable", SetVolumeEnable);
		<PERSON><PERSON>unction("SetProfile", SetProfile);
		<PERSON><PERSON>unction("GetBloom", GetBloom);
		L.RegFunction("SetBloomEnabled", SetBloomEnabled);
		L.RegFunction("GetDepthOfField", GetDepthOfField);
		L.RegFunction("SetDepthOfFieldEnabled", SetDepthOfFieldEnabled);
		L.RegFunction("GetVignette", GetVignette);
		<PERSON><PERSON>RegFunction("SetVignetteEnabled", SetVignetteEnabled);
		<PERSON><PERSON>unction("GetColorAdjustments", GetColorAdjustments);
		<PERSON><PERSON>RegFunction("SetColorAdjustmentsEnabled", SetColorAdjustmentsEnabled);
		<PERSON><PERSON>unction("GetColorCurves", GetColorCurves);
		L.RegFunction("SetColorCurvesEnabled", SetColorCurvesEnabled);
		L.RegFunction("GetColorLookup", GetColorLookup);
		L.RegFunction("SetColorLookupEnabled", SetColorLookupEnabled);
		L.RegFunction("GetFilmGrain", GetFilmGrain);
		L.RegFunction("SetFilmGrainEnabled", SetFilmGrainEnabled);
		L.RegFunction("GetLensDistortion", GetLensDistortion);
		L.RegFunction("SetLensDistortionEnabled", SetLensDistortionEnabled);
		L.RegFunction("GetLiftGammaGain", GetLiftGammaGain);
		L.RegFunction("SetLiftGammaGainEnabled", SetLiftGammaGainEnabled);
		L.RegFunction("GetMotionBlur", GetMotionBlur);
		L.RegFunction("SetMotionBlurEnabled", SetMotionBlurEnabled);
		L.RegFunction("GetRadialBlur", GetRadialBlur);
		L.RegFunction("SetRadialBlurEnabled", SetRadialBlurEnabled);
		L.RegFunction("DoRadialBlur", DoRadialBlur);
		L.RegFunction("StopRadialBlurTweenImmediately", StopRadialBlurTweenImmediately);
		L.RegFunction("PauseRadialBlurTween", PauseRadialBlurTween);
		L.RegFunction("ResumeRadialBlurTween", ResumeRadialBlurTween);
		L.RegFunction("GetPaniniProjection", GetPaniniProjection);
		L.RegFunction("SetPaniniProjectionEnabled", SetPaniniProjectionEnabled);
		L.RegFunction("GetShadowsMidtonesHighlights", GetShadowsMidtonesHighlights);
		L.RegFunction("SetShadowsMidtonesHighlightsEnabled", SetShadowsMidtonesHighlightsEnabled);
		L.RegFunction("GetSplitToning", GetSplitToning);
		L.RegFunction("SetSplitToningEnabled", SetSplitToningEnabled);
		L.RegFunction("GetTonemapping", GetTonemapping);
		L.RegFunction("SetTonemappingEnabled", SetTonemappingEnabled);
		L.RegFunction("GetWhiteBalance", GetWhiteBalance);
		L.RegFunction("SetWhiteBalanceEnabled", SetWhiteBalanceEnabled);
		L.RegFunction("GetChannelMixer", GetChannelMixer);
		L.RegFunction("SetChannelMixerEnabled", SetChannelMixerEnabled);
		L.RegFunction("GetChromaticAberration", GetChromaticAberration);
		L.RegFunction("SetChromaticAberrationEnabled", SetChromaticAberrationEnabled);
		L.RegFunction("GetNewMotionBlur", GetNewMotionBlur);
		L.RegFunction("SetNewMotionBlurEnabled", SetNewMotionBlurEnabled);
		L.RegFunction("SetNewMotionBlurDist", SetNewMotionBlurDist);
		L.RegFunction("SetNewMotionBlurStrength", SetNewMotionBlurStrength);
		L.RegFunction("DoNewMotionBlurStrength", DoNewMotionBlurStrength);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetVolume(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Volume o = obj.GetVolume();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetVolumeEnable(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetVolumeEnable(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetProfile(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.VolumeProfile arg0 = (UnityEngine.Rendering.VolumeProfile)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.VolumeProfile));
			obj.SetProfile(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetBloom(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.Bloom o = obj.GetBloom();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetBloomEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetBloomEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDepthOfField(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.DepthOfField o = obj.GetDepthOfField();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDepthOfFieldEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetDepthOfFieldEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetVignette(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.Vignette o = obj.GetVignette();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetVignetteEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetVignetteEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetColorAdjustments(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.ColorAdjustments o = obj.GetColorAdjustments();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetColorAdjustmentsEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetColorAdjustmentsEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetColorCurves(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.ColorCurves o = obj.GetColorCurves();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetColorCurvesEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetColorCurvesEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetColorLookup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.ColorLookup o = obj.GetColorLookup();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetColorLookupEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetColorLookupEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetFilmGrain(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.FilmGrain o = obj.GetFilmGrain();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetFilmGrainEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetFilmGrainEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLensDistortion(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.LensDistortion o = obj.GetLensDistortion();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLensDistortionEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetLensDistortionEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLiftGammaGain(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.LiftGammaGain o = obj.GetLiftGammaGain();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLiftGammaGainEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetLiftGammaGainEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMotionBlur(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.MotionBlur o = obj.GetMotionBlur();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMotionBlurEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetMotionBlurEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRadialBlur(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			RadialBlur o = obj.GetRadialBlur();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRadialBlurEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetRadialBlurEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoRadialBlur(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
				obj.DoRadialBlur(arg0);
				return 0;
			}
			else if (count == 3)
			{
				VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.DoRadialBlur(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.DoRadialBlur(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5)
			{
				VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				obj.DoRadialBlur(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, float, float, float>(L, 2))
			{
				VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 3);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 4);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 5);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 6);
				obj.DoRadialBlur(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UnityEngine.Vector2, float, float, float, float>(L, 2))
			{
				VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 3);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 4);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 5);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 6);
				obj.DoRadialBlur(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: VolumeController.DoRadialBlur");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopRadialBlurTweenImmediately(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			obj.StopRadialBlurTweenImmediately();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PauseRadialBlurTween(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			obj.PauseRadialBlurTween();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResumeRadialBlurTween(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			obj.ResumeRadialBlurTween();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPaniniProjection(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.PaniniProjection o = obj.GetPaniniProjection();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPaniniProjectionEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetPaniniProjectionEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetShadowsMidtonesHighlights(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.ShadowsMidtonesHighlights o = obj.GetShadowsMidtonesHighlights();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetShadowsMidtonesHighlightsEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetShadowsMidtonesHighlightsEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSplitToning(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.SplitToning o = obj.GetSplitToning();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSplitToningEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetSplitToningEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTonemapping(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.Tonemapping o = obj.GetTonemapping();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTonemappingEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetTonemappingEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetWhiteBalance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.WhiteBalance o = obj.GetWhiteBalance();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetWhiteBalanceEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetWhiteBalanceEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetChannelMixer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.ChannelMixer o = obj.GetChannelMixer();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetChannelMixerEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetChannelMixerEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetChromaticAberration(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			UnityEngine.Rendering.Universal.ChromaticAberration o = obj.GetChromaticAberration();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetChromaticAberrationEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetChromaticAberrationEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNewMotionBlur(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			NewMotionBlur o = obj.GetNewMotionBlur();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetNewMotionBlurEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetNewMotionBlurEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetNewMotionBlurDist(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetNewMotionBlurDist(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetNewMotionBlurStrength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetNewMotionBlurStrength(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoNewMotionBlurStrength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			VolumeController obj = (VolumeController)ToLua.CheckObject<VolumeController>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.DoNewMotionBlurStrength(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

