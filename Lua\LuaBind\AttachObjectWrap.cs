﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class AttachObjectWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(AttachObject), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("SetAttached", SetAttached);
		<PERSON><PERSON>("CleanAttached", CleanAttached);
		<PERSON><PERSON>ction("SetTransform", SetTransform);
		<PERSON><PERSON>unction("ChangePhysiqueConfig", ChangePhysiqueConfig);
		<PERSON><PERSON>RegFunction("GetPhysiqueConfigPos", GetPhysiqueConfigPos);
		<PERSON><PERSON>unction("GetPhysiqueConfigRot", GetPhysiqueConfigRot);
		<PERSON><PERSON>unction("GetPhysiqueConfigSca", GetPhysiqueConfigSca);
		<PERSON><PERSON>RegFunction("SaveSelfForPrefabs", SaveSelfForPrefabs);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("physiqueConfig", get_physiqueConfig, set_physiqueConfig);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetAttached(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			AttachObject obj = (AttachObject)ToLua.CheckObject(L, 1, typeof(AttachObject));
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.SetAttached(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CleanAttached(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			AttachObject obj = (AttachObject)ToLua.CheckObject(L, 1, typeof(AttachObject));
			obj.CleanAttached();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTransform(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			AttachObject obj = (AttachObject)ToLua.CheckObject(L, 1, typeof(AttachObject));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetTransform(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangePhysiqueConfig(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			AttachObject obj = (AttachObject)ToLua.CheckObject(L, 1, typeof(AttachObject));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
			UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 5);
			obj.ChangePhysiqueConfig(arg0, arg1, arg2, arg3);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPhysiqueConfigPos(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			AttachObject obj = (AttachObject)ToLua.CheckObject(L, 1, typeof(AttachObject));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Vector3 o = obj.GetPhysiqueConfigPos(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPhysiqueConfigRot(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			AttachObject obj = (AttachObject)ToLua.CheckObject(L, 1, typeof(AttachObject));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Vector3 o = obj.GetPhysiqueConfigRot(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPhysiqueConfigSca(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			AttachObject obj = (AttachObject)ToLua.CheckObject(L, 1, typeof(AttachObject));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Vector3 o = obj.GetPhysiqueConfigSca(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SaveSelfForPrefabs(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			AttachObject obj = (AttachObject)ToLua.CheckObject(L, 1, typeof(AttachObject));
			string arg0 = ToLua.CheckString(L, 2);
			string arg1 = ToLua.CheckString(L, 3);
			obj.SaveSelfForPrefabs(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_physiqueConfig(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			AttachObject obj = (AttachObject)o;
			AttachObject.PhysiqueConfig[] ret = obj.physiqueConfig;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index physiqueConfig on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_physiqueConfig(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			AttachObject obj = (AttachObject)o;
			AttachObject.PhysiqueConfig[] arg0 = ToLua.CheckStructArray<AttachObject.PhysiqueConfig>(L, 2);
			obj.physiqueConfig = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index physiqueConfig on a nil value");
		}
	}
}

