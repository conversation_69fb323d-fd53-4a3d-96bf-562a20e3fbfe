PursuitGameView = PursuitGameView or BaseClass(SafeBaseView)

function PursuitGameView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false, true)
	self.view_name = "PursuitGameView"
	self:AddViewResource(0, "uis/view/world_treasure_ui_prefab", "layout_pursuit_game_view")
end

function PursuitGameView:ReleaseCallBack()
	if self.turn_task_list then
		self.turn_task_list:DeleteMe()
		self.turn_task_list = nil
	end

	if self.grid_list then
		for k, v in pairs(self.grid_list) do
			v:DeleteMe()
		end
		self.grid_list = nil
	end
end

function PursuitGameView:LoadCallBack()
	if not self.turn_task_list then
		self.turn_task_list = AsyncListView.New(PursuitTurnTaskItem, self.node_list["turn_reward_list"])
	end

	if not self.grid_list then
		self.grid_list = {}
		local node_num = self.node_list["grid_root"].transform.childCount
		for i = 1, node_num do
			self.grid_list[i] = BubbleGridCell.New(self.node_list["grid_root"]:FindObj("game_grid_cell_" .. i))
			self.grid_list[i]:SetIndex(i)
		end
	end
end

function PursuitGameView:OpenCallBack()
	--WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_PURSUIT_INFO)
	--local badge_num = WorldTreasureWGData.Instance:GetCurBadgeNum()
end

function PursuitGameView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushGrid()
			self:FlushTurnTaskList()
		elseif k == "flush_grid" then
			self:FlushGrid()
		elseif k == "flush_turn" then
			self:FlushTurnTaskList()
		end
	end
end

function PursuitGameView:FlushTurnTaskList()
	local data_list = WorldTreasureWGData.Instance:GetPursuitTurnDataList()
	self.turn_task_list:SetDataList(data_list)
end

function PursuitGameView:FlushGrid()
	local data_list = WorldTreasureWGData.Instance:GetPursuitGridDataList()
	for i, v in ipairs(self.grid_list) do
		v:SetData(data_list[i])
	end

	local dart_num = WorldTreasureWGData.Instance:GetPursuitDartNum()
	self.node_list["txt_cart_num"].text.text = dart_num

	local badge_num = WorldTreasureWGData.Instance:GetCurBadgeNum()
	for i = 1, 3 do
		self.node_list["badge_check_" .. i]:SetActive(i <= badge_num)
	end

	if badge_num >= 3 then
		ReDelayCall(self, function()
			WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_PURSUIT_NEXT_TURN)
		end, 1.4, "pursuit_game_next_turn")
	end
end

----------------------------------------------------------------------------
--PursuitTurnTaskItem 轮次任务Item
----------------------------------------------------------------------------
PursuitTurnTaskItem = PursuitTurnTaskItem or BaseClass(BaseRender)

function PursuitTurnTaskItem:__init()

end

function PursuitTurnTaskItem:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_item_list"])
		self.reward_list:SetStartZeroIndex(true)
	end
	--XUI.AddClickEventListener(self.node_list["btn_get_reward"], BindTool.Bind1(self.OnClickGetRewardBtn, self))
end

function PursuitTurnTaskItem:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function PursuitTurnTaskItem:OnFlush()
	if self.data == nil then
		return
	end
	self.node_list["txt_turn"].text.text = string.format(Language.WorldTreasure.Turn, NumberToChinaNumber(self.data.turn))

	local cur_turn = WorldTreasureWGData.Instance:GetPursuitGameTurn()
	local can_get_reward = cur_turn >= self.data.turn and not self.data.is_received

	--XUI.SetButtonEnabled(self.node_list["btn_get_reward"], can_get_reward)
	-- 不需要手动领取了
	self.node_list["btn_get_reward"]:SetActive(false)--(not self.data.is_received)
	--self.node_list["reward_flag"]:SetActive(self.data.is_received)
	self.node_list["remind"]:SetActive(can_get_reward)

	self.reward_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetLingQuVisible(self.data.is_received)
            --item_cell:SetRedPointEff(can_get_reward)
        end
    end)
	self.reward_list:SetDataList(self.data.reward_item)
end

-- 不需要手动领取了
-- function PursuitTurnTaskItem:OnClickGetRewardBtn()
-- 	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_PURSUIT_TURN_REWARD, self.data.turn)
-- end

----------------------------------------------------------------------------
--BubbleGridCell 格子
----------------------------------------------------------------------------
BubbleGridCell = BubbleGridCell or BaseClass(BaseRender)

local PURSUIT_GRID_SHOW_STATE = {
	UNBREAK = 0,
	EMPTY = 1,
	BADGE = 2,
	ARROW_UP = 3,		-- 上
	ARROW_DOWN = 4,		-- 下
	ARROW_LEFT = 5,		-- 左
	ARROW_RIGHT = 6,	-- 右
	ARROW_ROW = 7,		-- 行
	ARROW_COLUMN = 8,	-- 列
}

local OFFSETS = {
	{-1, 0, PURSUIT_GRID_SHOW_STATE.ARROW_UP}, 
	{1, 0, PURSUIT_GRID_SHOW_STATE.ARROW_DOWN}, 
	{0, -1, PURSUIT_GRID_SHOW_STATE.ARROW_LEFT}, 
	{0, 1, PURSUIT_GRID_SHOW_STATE.ARROW_RIGHT}
}

function BubbleGridCell:__init()
	self.old_state = nil
end

function BubbleGridCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["button_click"], BindTool.Bind1(self.OnClickGrid, self))
end

function BubbleGridCell:ReleaseCallBack()
	self.old_state = nil
	self.show_num = nil
	self:ClearTween()
end

function BubbleGridCell:OnFlush()
	if not self.data then
		return
	end
	local data = self.data
	local show_state

	if data.break_flag == 0 then
		show_state = PURSUIT_GRID_SHOW_STATE.UNBREAK
	elseif data.reward_type ~= 0 then
		show_state = PURSUIT_GRID_SHOW_STATE.BADGE
		local badge_img_name = data.reward_type == 1 and "a3_ttjl_myxz_icon01" or "a3_ttjl_myxz_icon02"
		local bundle, asset = ResPath.GetWorldTreasureImg(badge_img_name)
		self.node_list["badge"].image:LoadSprite(bundle, asset)
	else
		-- 计算箭头
		for _, offset in ipairs(OFFSETS) do
			local nx, ny = data.x + offset[1], data.y + offset[2]
			if nx >= 1 and nx <= 4 and ny >= 1 and ny <= 4 then
				local grid_data = WorldTreasureWGData.Instance:GetPursuitGridData(nx, ny)
				if grid_data.break_flag == 0 and grid_data.reward_type ~= 0 then
					grid_data.need_notice_anim = true
					show_state = offset[3]
					break
				end
			end
		end
		
		if not show_state then
			local row_badges, col_badges = 0, 0
			for i = 1, 4 do
				if i ~= data.y then
					local grid_data = WorldTreasureWGData.Instance:GetPursuitGridData(data.x, i)
					if grid_data.break_flag == 0 and grid_data.reward_type ~= 0 then
						-- 检查之间是否有空格子，如果自己和徽章之间已经有空格，则不显示箭头
						local is_have_temp_empty
						local start_num = i < data.y and i or data.y
						local end_num = i < data.y and data.y or i
						for j = start_num, end_num do
							local temp_grid_data = WorldTreasureWGData.Instance:GetPursuitGridData(data.x, j)
							if j ~= data.y and temp_grid_data.break_flag == 1 and temp_grid_data.reward_type == 0 then
								is_have_temp_empty = true
								break
							end
						end
						if not is_have_temp_empty then
							row_badges = row_badges + 1
						end
					end
				end
			end
			for i = 1, 4 do
				if i ~= data.x then
					local grid_data = WorldTreasureWGData.Instance:GetPursuitGridData(i, data.y)
					if grid_data.break_flag == 0 and grid_data.reward_type ~= 0 then
						-- 检查之间是否有空格子，如果自己和徽章之间已经有空格，则不显示箭头
						local is_have_temp_empty
						local start_num = i < data.x and i or data.x
						local end_num = i < data.x and data.x or i
						for j = start_num, end_num do
							local temp_grid_data = WorldTreasureWGData.Instance:GetPursuitGridData(j, data.y)
							if j ~= data.x and temp_grid_data.break_flag == 1 and temp_grid_data.reward_type == 0 then
								is_have_temp_empty = true
								break
							end
						end
						if not is_have_temp_empty then
							col_badges = col_badges + 1
						end
					end
				end
			end

			if row_badges > 0 then
				show_state = PURSUIT_GRID_SHOW_STATE.ARROW_ROW
				self.show_num = row_badges
				for i = 1, 4 do
					if i ~= data.y then
						local grid_data = WorldTreasureWGData.Instance:GetPursuitGridData(data.x, i)
						if grid_data.break_flag == 0 then
							grid_data.need_notice_anim = true
						end
					end
				end
			elseif col_badges > 0 then
				show_state = PURSUIT_GRID_SHOW_STATE.ARROW_COLUMN
				self.show_num = col_badges
				for i = 1, 4 do
					if i ~= data.x then
						local grid_data = WorldTreasureWGData.Instance:GetPursuitGridData(i, data.y)
						if grid_data.break_flag == 0 then
							grid_data.need_notice_anim = true
						end
					end
				end
			end
		end

		if not show_state then
			show_state = PURSUIT_GRID_SHOW_STATE.EMPTY
		end
	end

	local break_index = WorldTreasureWGData.Instance:GetBreakGridIndex()
	if break_index == self.data.index then
		self.node_list["img_bubble"]:SetActive(false)
		local bundle, asset = ResPath.GetEffectUi("UI_TTJL_myxz_hit")
		local pos = Vector3(0,0,0)
		EffectManager.Instance:PlayEffect(self, bundle, asset, self.node_list["anim_root"].transform, pos, nil, nil, 2)
		WorldTreasureWGData.Instance:SetBreakGridIndex(nil)
	end

	if self.old_state == nil then
		self:ChangeShowState(show_state, false)
		self.old_state = show_state
	elseif self.old_state ~= show_state then
		-- 动画中不可点击
		WorldTreasureWGData.Instance:SetPursuitClickBlock(true)
		if show_state ~= PURSUIT_GRID_SHOW_STATE.EMPTY then
			self:ChangeShowState(PURSUIT_GRID_SHOW_STATE.EMPTY, true)
			ReDelayCall(self, function()
				self:ChangeShowState(show_state, true)
				self.old_state = show_state
			end, 0.7, "pursuit_grid_change_state" .. data.index)
		else
			self:ChangeShowState(PURSUIT_GRID_SHOW_STATE.EMPTY, true)
			self.old_state = show_state
		end
	end

	ReDelayCall(self, function()
		if data.need_notice_anim then
			self:PlayNotceAnim()
			data.need_notice_anim = false
		end
		WorldTreasureWGData.Instance:SetPursuitClickBlock(false)
	end, 0.7, "pursuit_grid_notice_anim" .. data.index)
end

function BubbleGridCell:ChangeShowState(state, need_anim)
	self:SetNodeShow("img_bubble", state == PURSUIT_GRID_SHOW_STATE.UNBREAK, need_anim)
	self:SetNodeShow("badge", state == PURSUIT_GRID_SHOW_STATE.BADGE, need_anim)
	self:SetNodeShow("arrow_single", state == PURSUIT_GRID_SHOW_STATE.ARROW_UP or state == PURSUIT_GRID_SHOW_STATE.ARROW_DOWN or
									state == PURSUIT_GRID_SHOW_STATE.ARROW_LEFT or state == PURSUIT_GRID_SHOW_STATE.ARROW_RIGHT, need_anim)
	self:SetNodeShow("arrow_double", state == PURSUIT_GRID_SHOW_STATE.ARROW_ROW or state == PURSUIT_GRID_SHOW_STATE.ARROW_COLUMN, need_anim)
	self:SetNodeShow("txt_num", state == PURSUIT_GRID_SHOW_STATE.ARROW_ROW or state == PURSUIT_GRID_SHOW_STATE.ARROW_COLUMN, need_anim)

	if state == PURSUIT_GRID_SHOW_STATE.ARROW_UP then
		self.node_list["arrow_single"].transform.rotation = Quaternion.Euler(0, 0, -90)
	elseif state == PURSUIT_GRID_SHOW_STATE.ARROW_DOWN then
		self.node_list["arrow_single"].transform.rotation = Quaternion.Euler(0, 0, 90)
	elseif state == PURSUIT_GRID_SHOW_STATE.ARROW_LEFT then
		self.node_list["arrow_single"].transform.rotation = Quaternion.Euler(0, 0, 0)
	elseif state == PURSUIT_GRID_SHOW_STATE.ARROW_RIGHT then
		self.node_list["arrow_single"].transform.rotation = Quaternion.Euler(0, 0, 180)
	elseif state == PURSUIT_GRID_SHOW_STATE.ARROW_ROW then
		self.node_list["arrow_double"].transform.rotation = Quaternion.Euler(0, 0, 0)
		self.node_list["txt_num"].text.text = self.show_num or 0
	elseif state == PURSUIT_GRID_SHOW_STATE.ARROW_COLUMN then
		self.node_list["arrow_double"].transform.rotation = Quaternion.Euler(0, 0, 90)
		self.node_list["txt_num"].text.text = self.show_num or 0
	end
end

function BubbleGridCell:ClearTween()
	if self.scale_tweener then
		self.scale_tweener:Kill()
		self.scale_tweener = nil
	end

	if self.notice_tweener then
		self.notice_tweener:Kill()
		self.notice_tweener = nil
	end
end

function BubbleGridCell:SetNodeShow(node, is_show, need_anim)
	if not self.node_list[node] then
		return
	end

	--self:ClearTween()
	if is_show then
		self.node_list[node]:SetActive(true)
		if need_anim then
			--local from_scale, to_scale = 0, 1--u3dpool.vec3(0, 0, 0), u3dpool.vec3(1, 1, 1)
			self.node_list[node].transform.localScale = u3dpool.vec3(0, 1, 0)
			self.scale_tweener = self.node_list[node].transform:DOScale(1, 0.2)
			self.scale_tweener:SetEase(DG.Tweening.Ease.Linear)
		else
			self.node_list[node].transform.localScale = u3dpool.vec3(1, 1, 1)
		end
	else
		if self.node_list[node].gameObject.activeSelf then
			if need_anim then
				--local from_scale, to_scale = 1, 0-- u3dpool.vec3(1, 1, 1), u3dpool.vec3(0, 0, 0)
				self.node_list[node].transform.localScale = u3dpool.vec3(1, 1, 1)
				self.scale_tweener = self.node_list[node].transform:DOScale(0, 0.2)
				self.scale_tweener:SetEase(DG.Tweening.Ease.Linear)
				self.scale_tweener:OnComplete(function()
					self.node_list[node]:SetActive(false)
				end)
			else
				self.node_list[node].transform.localScale = u3dpool.vec3(1, 1, 1)
				self.node_list[node]:SetActive(false)
			end
		end
	end
end

function BubbleGridCell:OnClickGrid()
	local is_block = WorldTreasureWGData.Instance:GetPursuitClickBlock()
	if is_block then
		return
	end

	if not self.data or self.data.break_flag ~= 0 then
		return
	end
	
	local dart_num = WorldTreasureWGData.Instance:GetPursuitDartNum()
	if dart_num <= 0 then
		local item_cfg = ItemWGData.Instance:GetItemConfig(65587)
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.WorldTreasure.NotEnough, item_cfg.name))
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = 65587})
		return
	end

	WorldTreasureWGCtrl.Instance:PursuitClickReq(self.data.index)
end

function BubbleGridCell:PlayNotceAnim()
	local tween_root = self.node_list["anim_root"].rect
	self.notice_tweener = tween_root:DOScale(1.2, 0.25)
	self.notice_tweener:SetLoops(4, DG.Tweening.LoopType.Yoyo)
	self.notice_tweener:SetEase(DG.Tweening.Ease.InOutSine)
end