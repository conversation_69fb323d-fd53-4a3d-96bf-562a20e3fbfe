﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ListViewPageScrollWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ListViewPageScroll), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("JumpToPage", JumpToPage);
		<PERSON><PERSON>unction("GetNowPage", GetNowPage);
		<PERSON><PERSON>unction("SetPageCellNumBer", SetPageCellNumBer);
		L.RegFunction("SetPageCount", SetPageCount);
		<PERSON>.RegFunction("SetChangeAction", SetChangeAction);
		<PERSON><PERSON>Function("JumpToPageImmidate", JumpToPageImmidate);
		<PERSON><PERSON>Function("OnBeginDrag", OnBeginDrag);
		L.RegFunction("OnEndDrag", OnEndDrag);
		L.RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("JumpToPageEvent", get_JumpToPageEvent, set_JumpToPageEvent);
		<PERSON><PERSON>lass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int JumpToPage(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ListViewPageScroll obj = (ListViewPageScroll)ToLua.CheckObject(L, 1, typeof(ListViewPageScroll));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.JumpToPage(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNowPage(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ListViewPageScroll obj = (ListViewPageScroll)ToLua.CheckObject(L, 1, typeof(ListViewPageScroll));
			int o = obj.GetNowPage();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPageCellNumBer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ListViewPageScroll obj = (ListViewPageScroll)ToLua.CheckObject(L, 1, typeof(ListViewPageScroll));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetPageCellNumBer(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPageCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ListViewPageScroll obj = (ListViewPageScroll)ToLua.CheckObject(L, 1, typeof(ListViewPageScroll));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetPageCount(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetChangeAction(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ListViewPageScroll obj = (ListViewPageScroll)ToLua.CheckObject(L, 1, typeof(ListViewPageScroll));
			System.Func<float,bool> arg0 = (System.Func<float,bool>)ToLua.CheckDelegate<System.Func<float,bool>>(L, 2);
			obj.SetChangeAction(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int JumpToPageImmidate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ListViewPageScroll obj = (ListViewPageScroll)ToLua.CheckObject(L, 1, typeof(ListViewPageScroll));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.JumpToPageImmidate(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnBeginDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ListViewPageScroll obj = (ListViewPageScroll)ToLua.CheckObject(L, 1, typeof(ListViewPageScroll));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnBeginDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnEndDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ListViewPageScroll obj = (ListViewPageScroll)ToLua.CheckObject(L, 1, typeof(ListViewPageScroll));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnEndDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_JumpToPageEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_JumpToPageEvent(IntPtr L)
	{
		try
		{
			ListViewPageScroll obj = (ListViewPageScroll)ToLua.CheckObject(L, 1, typeof(ListViewPageScroll));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'ListViewPageScroll.JumpToPageEvent' can only appear on the left hand side of += or -= when used outside of the type 'ListViewPageScroll'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.JumpToPageEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.JumpToPageEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

