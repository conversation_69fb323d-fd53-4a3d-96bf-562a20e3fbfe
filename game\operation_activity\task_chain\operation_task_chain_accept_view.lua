OperationTaskChainAcceptView = OperationTaskChainAcceptView or BaseClass(SafeBaseView)

function OperationTaskChainAcceptView:__init()
	self.view_style = ViewStyle.Full
	self.view_layer = UiLayer.Pop

	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain_accept_view")
end

function OperationTaskChainAcceptView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("task_chain_accept_act_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_accept_act_timer")
	end

	if CountDownManager.Instance:HasCountDown("task_chain_accept_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_accept_timer")
	end

	if self.task_chain_task_list then
		for k, v in pairs(self.task_chain_task_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.task_chain_task_list = nil
	end

	if self.reward_grid then
		self.reward_grid:DeleteMe()
		self.reward_grid = nil
	end

	if self.display_model ~= nil then
		self.display_model:DeleteMe()
		self.display_model = nil
	end
end

function OperationTaskChainAcceptView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("task_chain_accept_act_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_accept_act_timer")
	end

	if CountDownManager.Instance:HasCountDown("task_chain_accept_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_accept_timer")
	end
end
 
function OperationTaskChainAcceptView:LoadCallBack()
	self:InitParam()

	self.reward_grid = AsyncBaseGrid.New()
	self.reward_grid:SetStartZeroIndex(false)
	self.reward_grid:CreateCells({ col = 3, change_cells_num = 1, list_view = self.node_list["list_reward"] })

	self.node_list.btn_go.button:AddClickListener(BindTool.Bind1(self.OnClickTaskChainGo, self))

	self.display_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["root_obj"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = false,
	}
	
	self.display_model:SetRenderTexUI3DModel(display_data)
	-- self.display_model:SetUI3DModel(self.node_list.root_obj.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE,
	-- 	MODEL_OFFSET_TYPE.NORMALIZE)
	self:AddUiRoleModel(self.display_model)
	-- self:FlushPictureAndTextContent()
end

function OperationTaskChainAcceptView:FlushPictureAndTextContent()
	local task_chain_interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if not task_chain_interface_cfg then
		return
	end

	self.node_list.RawImage_tongyong.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(task_chain_interface_cfg.pic2_1))
	self.node_list.RawImage_tongyong.raw_image:SetNativeSize()
	self.node_list.zhuangshi_2.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic2_2))
	self.node_list.zhuangshi_2.image:SetNativeSize()
	self.node_list.zhuangshi_1.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic2_2))
	self.node_list.zhuangshi_1.image:SetNativeSize()
	self.node_list.think_gift_flag.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic_11))
	self.node_list.think_gift_flag.image:SetNativeSize()
	self.node_list.reward_list_bg.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic_12))
	self.node_list.reward_list_bg.image:SetNativeSize()
end

function OperationTaskChainAcceptView:OnClickTaskChainGo()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	self:Close()
end

function OperationTaskChainAcceptView:InitParam()
	self.task_chain_btn_str = ""
end

function OperationTaskChainAcceptView:OnFlush()
	self:FlushPanel()
	self:FlushActTime()
	self:FlushAutoClose()
	self:FlushTaskChainShowReward()
	-- self:FlushTaskChainTaskInfo()
end

function OperationTaskChainAcceptView:FlushPanel()
	local info = OperationTaskChainWGData.Instance:GetTaskChainActInfo()
	local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
	if info and day_index_cfg then
		local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
		if task_chain_cfg ~= nil then
			self.node_list.str_task_desc.text.text = task_chain_cfg.desc
			self.node_list.str_btn_info.text.text = task_chain_cfg.btn_str
			self.task_chain_btn_str = task_chain_cfg.btn_str

			local cfg = nil
			local task_tab = Split(task_chain_cfg.task_ids, "|")
			if task_tab ~= nil and #task_tab > 0 then
				local task_id = task_tab[info.task_idx + 1]
				if task_id ~= nil and task_id ~= "" then
					cfg = OperationTaskChainWGData.Instance:GetTaskChainTaskCfg(tonumber(task_id))
				end
			end

			if cfg ~= nil then
				self:FlushDisplayModel(cfg.npc_model, cfg.npc_scale, cfg.npc_pos)
				self.node_list.title_img.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(cfg.npc_title_img))
				local index = info.task_idx + 1
				self.node_list.max_num_img.image:LoadSprite(ResPath.GetOperationTaskChainF2("max_num_" .. index))
			end
		end
	end
end

function OperationTaskChainAcceptView:FlushDisplayModel(npc_id, scale, npc_pos)
	local monster_list = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list
	if not self.display_model then
		return
	end

	scale = scale or 1
	Transform.SetLocalScaleXYZ(self.node_list.root_obj.transform, scale, scale, scale)

	if npc_pos then
		local model_pos = Split(npc_pos, "|")
		if model_pos then
			RectTransform.SetAnchoredPositionXY(self.node_list.root_obj.rect, model_pos[1] or 0, model_pos[2] or 0)
		end
	end

	local model_type, bundle, asset, res_id, tianshen_index = BossWGData.GetMonsterResByMonsterId(npc_id)
	if model_type == ClientBossType.WeaponAniTSBoss and res_id > 0 and tianshen_index then
		self.display_model:SetTianShenModel(res_id, tianshen_index, false, nil)
	elseif bundle and asset then
		self.display_model:SetMainAsset(bundle, asset)
	end
end

function OperationTaskChainAcceptView:FlushActTime()
	if CountDownManager.Instance:HasCountDown("task_chain_accept_act_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_accept_act_timer")
	end
	local info = OperationTaskChainWGData.Instance:GetTaskChainActInfo()
	if info then
		if info.cur_task_status == OPERATION_TASK_CHAIN_ACT_STATUS.STANDY then
			local count_time = info.cur_task_status_end_timestamp - TimeWGCtrl.Instance:GetServerTime()
			if count_time > 0 then
				self.node_list.time_desc.text.text = Language.OpertionAcitvity.TaskChain.LastTime
				CountDownManager.Instance:AddCountDown("task_chain_accept_act_timer",
					BindTool.Bind1(self.FlushTaskChainActTime, self), nil, info.cur_task_status_end_timestamp, nil, 1)
			end
		else
			self.node_list.str_time.text.text = ""
			self.node_list.time_desc.text.text = ""
		end
	end
end

function OperationTaskChainAcceptView:FlushTaskChainActTime(elapse_time, total_time)
	self.node_list.str_time.text.text = TimeUtil.FormatSecond(total_time - elapse_time)
end

function OperationTaskChainAcceptView:FlushAutoClose()
	if CountDownManager.Instance:HasCountDown("task_chain_accept_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_accept_timer")
	end

	local is_open = OperationTaskChainWGData.Instance:GetTaskChainIsOpen()
	if not is_open then
		return
	end

	self.node_list.str_btn_info.text.text = string.format(Language.OpertionAcitvity.TaskChain.TaskChainBtnCountDownStr,
		self.task_chain_btn_str, 5)

	CountDownManager.Instance:AddCountDown("task_chain_accept_timer", BindTool.Bind1(self.FlushAutoClick, self),
		BindTool.Bind(self.CompleteAutoClick, self), nil, 5, 1)
end

function OperationTaskChainAcceptView:FlushAutoClick(elapse_time, total_time)
	local value = math.ceil(total_time - elapse_time)
	self.node_list.str_btn_info.text.text = string.format(Language.OpertionAcitvity.TaskChain.TaskChainBtnCountDownStr,
		self.task_chain_btn_str, value)
end

function OperationTaskChainAcceptView:CompleteAutoClick()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	self:Close()
end

function OperationTaskChainAcceptView:FlushTaskChainShowReward()
	local data_list = OperationTaskChainWGData.Instance:GetShowRewardList()
	if data_list then
		self.reward_grid:SetDataList(data_list)
	end
end

function OperationTaskChainAcceptView:FlushTaskChainTaskInfo()
	local is_need_load = false
	if self.task_chain_task_list == nil then
		is_need_load = true
	end

	local show_list = OperationTaskChainWGData.Instance:GetShowTaskListByActFb()
	if show_list == nil or #show_list == 0 then
		return
	end

	local show_num = #show_list

	if not self.task_chain_task_list or show_num > #self.task_chain_task_list then
		local asset, bundle = ResPath.GetOperationTaskChainPrefabF2("render_task_chain_icon")
		local res_async_loader = AllocResAsyncLoader(self, "render_task_chain_icon")
		res_async_loader:Load(asset, bundle, nil,
			function(new_obj)
				if nil == new_obj then
					return
				end

				local task_list = self.task_chain_task_list or {}
				local cell_root = nil
				for i = #task_list + 1, show_num do
					cell_root = self.node_list["task_root_" .. i]
					if cell_root then
						local obj = ResMgr:Instantiate(new_obj)
						obj.transform:SetParent(cell_root.transform, false)
						task_list[#task_list + 1] = TaskChainTaskInfoRender.New(obj)
					end
				end
				self.task_chain_task_list = task_list

				for k, v in pairs(task_list) do
					v:SetData(show_list[k])
				end
			end)
	else
		for i = 1, show_num do
			if self.task_chain_task_list[i] ~= nil then
				self.task_chain_task_list[i]:SetActive(true)
				self.task_chain_task_list[i]:SetData(show_list[i])
			end
		end

		if show_num < #self.task_chain_task_list then
			for i = show_num, #self.task_chain_task_list do
				self.task_chain_task_list[i]:SetActive(false)
			end
		end
	end
end
