-- 灵猫献玉（原地藏红包）
DiZangRedPackView = DiZangRedPackView  or BaseClass(SafeBaseView)
function DiZangRedPackView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/dizang_redpack_ui_prefab", "layout_dizang_redpack")
end

function DiZangRedPackView:__delete()

end

function DiZangRedPackView:LoadCallBack()
    self.num_enough = nil
    self.is_fetch = nil

    self.task_list = AsyncListView.New(DZRPTaskRender, self.node_list["task_list"])
    self.record_list = AsyncListView.New(DZRPRecordRender, self.node_list["record_list"])

    XUI.AddClickEventListener(self.node_list["btn_get"], BindTool.Bind(self.OnClickGet, self))
    XUI.AddClickEventListener(self.node_list["btn_tips"], BindTool.Bind(self.OnClickTips, self))
end

function DiZangRedPackView:ReleaseCallBack()
    self:CleanDiZangRedPackTimer()

    if self.task_list then
        self.task_list:DeleteMe()
        self.task_list = nil
    end

    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end

    self.num_enough = nil
    self.is_fetch = nil
end

function DiZangRedPackView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushLeftView()
            self:FlushRightView()
        elseif k == "flush_num" then
            self:FlushLeftView()
        elseif k == "flush_record" then
            self:FlushRightView()
        end
    end
end

function DiZangRedPackView:FlushLeftView()
    local reward_cfg = DiZangRedPackWGData.Instance:GetCurRewardCfg()
    if reward_cfg == nil then
        return
    end

    local task_id_list = reward_cfg.task_id_list
    local cur_num = reward_cfg.start_num
    for k,v in pairs(task_id_list) do
        local task_cfg = DiZangRedPackWGData.Instance:GetTaskCfg(v)
        local info = DiZangRedPackWGData.Instance:GetTaskInfo(v)
        if task_cfg and info and info.is_finish == 1 then
            cur_num = cur_num + task_cfg.add_num
        end
    end

    local reward = reward_cfg.reward_item or {}
    local item_name = ItemWGData.Instance:GetItemName(reward.item_id)
    self.node_list.tip_text.text.text = string.format(Language.DiZangRedPack.TipDesc, reward_cfg.finish_num, reward.num or 0, item_name or "")
    
    self.task_list:SetDataList(task_id_list)

    self.num_enough = cur_num >= reward_cfg.finish_num
    self.is_fetch = DiZangRedPackWGData.Instance:GetIsFetch()

    self.node_list["cur_num"].text.text = cur_num
    self.node_list["need_num"].text.text = string.format(Language.DiZangRedPack.TiXianDesc, reward_cfg.finish_num, reward.num or 0, item_name or "")

    self.node_list["btn_get_remind"]:CustomSetActive(self.num_enough and not self.is_fetch)

    local remain_time = DiZangRedPackWGData.Instance:GetTimeRemaining()
	self:CleanDiZangRedPackTimer()
	self:ShowDiZangRedPackTime(remain_time)
	if remain_time > 0 then
		self.dizang_redpack_timer = CountDown.Instance:AddCountDown(remain_time, 0.5,
		function(elapse_time, total_time)
			local time = math.floor(total_time - elapse_time)
			self:ShowDiZangRedPackTime(time)
		end,
		function()
			self:ShowDiZangRedPackTime(0)
		end)
	end

    XUI.SetGraphicGrey(self.node_list["btn_get"], self.is_fetch)
end

function DiZangRedPackView:CleanDiZangRedPackTimer()
    if self.dizang_redpack_timer and CountDown.Instance:HasCountDown(self.dizang_redpack_timer) then
        CountDown.Instance:RemoveCountDown(self.dizang_redpack_timer)
        self.dizang_redpack_timer = nil
    end
end

function DiZangRedPackView:ShowDiZangRedPackTime(time)
	local time_str = TimeUtil.FormatSecondDHM4(time)
	if self.node_list["remain_time_desc"] then
		self.node_list["remain_time_desc"].text.text = time_str
	end
end

function DiZangRedPackView:FlushRightView()
    local record_list = DiZangRedPackWGData.Instance:GetRecordList()
    self.record_list:SetDataList(record_list)
    self.record_list:JumptToPrecent(1)
    
    self.node_list.no_record_tip:CustomSetActive(#record_list <= 0)
end


function DiZangRedPackView:OnClickGet()
    if self.num_enough == nil or self.is_fetch == nil then
        return
    end

    if not self.num_enough then
        if self.node_list["need_num"] then
            SysMsgWGCtrl.Instance:ErrorRemind(self.node_list["need_num"].text.text)
        end
        return
    end

    if self.is_fetch then
        local reward_cfg = DiZangRedPackWGData.Instance:GetCurRewardCfg()
        if reward_cfg then
            local item_name = ItemWGData.Instance:GetItemName(reward_cfg.item_id, nil, true)
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.DiZangRedPack.IsFetch, item_name))
        end
        return
    end

    DiZangRedPackWGCtrl.Instance:SendReq(3)
    self:Close() -- 领取自动关闭
end

function DiZangRedPackView:OnClickTips()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.DiZangRedPack.TipsTitle)
	rule_tip:SetContent(Language.DiZangRedPack.TipsContent, nil, nil, nil, true)
end

-------------------------------- DZRPTaskRender -------------------------------
DZRPTaskRender = DZRPTaskRender or BaseClass(BaseRender)
function DZRPTaskRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind(self.GoTo, self))
end

function DZRPTaskRender:OnFlush()
    if self.data == nil then
        self.view:SetActive(false)
        return
    end

    local task_cfg = DiZangRedPackWGData.Instance:GetTaskCfg(self.data)
    local info = DiZangRedPackWGData.Instance:GetTaskInfo(self.data)
    if task_cfg == nil or info == nil then
        self.view:SetActive(false)
        return
    end

    local is_finish = info.is_finish == 1
    local desc = task_cfg.desc
    -- local desc = ToColorStr(string.format(Language.DiZangRedPack.TaskDesc, self.index, task_cfg.desc), is_finish and COLOR3B.GREEN or COLOR3B.DEFAULT)
    local color = is_finish and COLOR3B.C26 or COLOR3B.C3
    local process = (task_cfg.task_type == 1) and math.floor(info.progress_num / 60) or info.progress_num
    self.node_list["desc"].text.text = string.format(desc, color, process)
    self.node_list["desc_2"].text.text = string.format(Language.DiZangRedPack.TaskDesc2, task_cfg.add_num)
    self.node_list["is_finish"]:SetActive(is_finish)
    
    local is_hide_goto = not task_cfg.open_panel or task_cfg.open_panel == ""
    self.node_list["btn_goto"]:SetActive(not is_hide_goto and not is_finish)

    self.view:SetActive(true)
end

function DZRPTaskRender:GoTo()
    if self.data == nil then
        return
    end

    local task_cfg = DiZangRedPackWGData.Instance:GetTaskCfg(self.data)
    if task_cfg and task_cfg.open_panel and task_cfg.open_panel ~= "" then
        ViewManager.Instance:OpenByCfg(task_cfg.open_panel)
    end
end

-------------------------------- DZRPRecordRender -------------------------------
DZRPRecordRender = DZRPRecordRender or BaseClass(BaseRender)
function DZRPRecordRender:OnFlush()
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    item_name = string.format("%s%s", self.data.num, item_name)
    self.node_list["desc"].text.text = string.format(Language.DiZangRedPack.RecordDesc, self.data.name, item_name)
end
