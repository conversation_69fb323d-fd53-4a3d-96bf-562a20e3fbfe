ShiTianSuitResonanceView = ShiTianSuitResonanceView or BaseClass(SafeBaseView)

local MAX_ATTR_NUM = 6

function ShiTianSuitResonanceView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/shitian_suit_ui_prefab", "layout_shitiansuit_resonance")
end

function ShiTianSuitResonanceView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.act_btn, BindTool.Bind(self.OnClickActiveBtn, self))

    if not self.cur_attr_list then
        self.cur_attr_list = {}
        for i = 1, MAX_ATTR_NUM do
            self.cur_attr_list[i] = CommonAttrRender.New(self.node_list.cur_attr_list:FindObj("attr_item" .. i))
        end
    end

    if not self.next_attr_list then
        self.next_attr_list = {}
        for i = 1, MAX_ATTR_NUM do
            self.next_attr_list[i] = CommonAttrRender.New(self.node_list.next_attr_list:FindObj("attr_item" .. i))
        end
    end
end

function ShiTianSuitResonanceView:ReleaseCallBack()
    if self.cur_attr_list then
        for k, v in pairs(self.cur_attr_list) do
            v:DeleteMe()
        end

        self.cur_attr_list = nil
    end

    if self.next_attr_list then
        for k, v in pairs(self.next_attr_list) do
            v:DeleteMe()
        end

        self.next_attr_list = nil
    end
end

function ShiTianSuitResonanceView:OpenCallBack()
    self.flush_all_info = true
end

function ShiTianSuitResonanceView:SetShowData(data)
    self.show_data = data
end

function ShiTianSuitResonanceView:OnFlush(parma_t)
    if IsEmptyTable(self.show_data) then
        return
    end

    for k, v in pairs(parma_t) do
        if k == "flush_info" then
            self.flush_all_info = true
        end
    end

    local suit_seq = self.show_data.show_suit_seq
    local show_type = self.show_data.show_type
    local cur_level_cfg, next_level_cfg = ShiTianSuitStrengthenWGData.Instance:GetCurAndNextTotalLevelCfg(suit_seq, show_type)
    local cur_attr_list = EquipWGData.GetSortAttrListByTypeCfg(cur_level_cfg, "attr_id", "attr_val", 1, 6)
    local next_attr_list = EquipWGData.GetSortAttrListByTypeCfg(next_level_cfg, "attr_id", "attr_val", 1, 6)
    local not_act = IsEmptyTable(cur_attr_list)
    local is_max = IsEmptyTable(next_attr_list)
    local can_act = ShiTianSuitStrengthenWGData.Instance:GetCurTotalCanActiveOrIsMax(suit_seq, show_type)

    if self.flush_all_info then
        local cur_total_str
        local total_level_str
        local cur_total_level = 0
        local target_total_level = 0
        if show_type == SHOW_TYPE.Strengthen then
            cur_total_str = cur_level_cfg["total_level"] or 0
            total_level_str = next_level_cfg["total_level"] or 0
            cur_total_level = ShiTianSuitStrengthenWGData.Instance:GetStrengthenTotalLevelBySeq(suit_seq)
            target_total_level = next_level_cfg["total_level"] or 0
        elseif show_type == SHOW_TYPE.Gemstone then
            cur_total_str = cur_level_cfg["total_shitianstone"] or 0
            total_level_str = next_level_cfg["total_shitianstone"] or 0
            cur_total_level = ShiTianSuitStrengthenWGData.Instance:GetStoneTotalLevelBySeq(suit_seq)
            target_total_level = next_level_cfg["total_shitianstone"] or 0
        elseif show_type == SHOW_TYPE.InfuseSoul then
            local cur_quality = cur_level_cfg["total_quality"] or 0
            local target_quality = next_level_cfg["total_quality"] or 0
            cur_total_str = Language.ShiTianSuit.InfuseSoulQuality[cur_quality]
            total_level_str = Language.ShiTianSuit.InfuseSoulQuality[target_quality]
            cur_total_level = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulTotalLevelBySeq(suit_seq)
            target_total_level = STSUIT_MAX_PART_COUNT
        end

        local color = can_act and COLOR3B.DEFAULT_NUM or COLOR3B.PINK
        self.node_list.cur_total_num.text.text = string.format(Language.ShiTianSuit.ResonanceCurTotalLevel[show_type], cur_total_str or "")
        self.node_list.total_num.text.text = string.format(Language.ShiTianSuit.ResonanceNeedTotalLevel[show_type], total_level_str or "", color, cur_total_level, target_total_level)
        self.node_list.cur_total_top:SetActive(not not_act)

        self.node_list.act_remind:SetActive(not is_max and can_act)
        self.node_list.act_btn:SetActive(not is_max)
        self.node_list.max_flag:SetActive(is_max)
        XUI.SetButtonEnabled(self.node_list.act_btn, can_act)

        self.flush_all_info = false
        self.node_list.title_txt.text.text = Language.ShiTianSuit.ResonanceTitle[show_type]
        self.node_list.empty_tips:SetActive(not_act)
        self.node_list.next_state:SetActive(not is_max)

        for k, v in pairs(self.cur_attr_list) do
            v:SetData(cur_attr_list[k])
        end

        for k, v in pairs(self.next_attr_list) do
            v:SetData(next_attr_list[k])
        end

        self.node_list.cur_scorll.scroll_rect.verticalNormalizedPosition = 1
        self.node_list.next_scorll.scroll_rect.verticalNormalizedPosition = 1
    end
end

function ShiTianSuitResonanceView:OnClickActiveBtn()
    if IsEmptyTable(self.show_data) then
        return
    end

    local is_max = ShiTianSuitStrengthenWGData.Instance:GetCurTotalCanActiveOrIsMax(self.show_data.show_suit_seq, self.show_data.show_type, true)
    local can_act = ShiTianSuitStrengthenWGData.Instance:GetCurTotalCanActiveOrIsMax(self.show_data.show_suit_seq, self.show_data.show_type)
    local req_type = self:GetReqType()

    if is_max then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.ResonanceMaxTip[self.show_data.show_type])
        return
    end

    if not can_act then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.TotalNotCanActiveTip)
        return
    end

    ShiTianSuitStrengthenWGCtrl.Instance:SendCSShiTianStrengthenReq(req_type, self.show_data.show_suit_seq)
end

function ShiTianSuitResonanceView:GetReqType()
    if not self.show_data then
        return 0
    end

    if self.show_data.show_type == SHOW_TYPE.Strengthen then
        return SHITIANSTONE_OPERA_TYPE.STRENGTH_ACTIVE
    elseif self.show_data.show_type == SHOW_TYPE.Gemstone then
        return SHITIANSTONE_OPERA_TYPE.STONE_ACTIVE
    elseif self.show_data.show_type == SHOW_TYPE.InfuseSoul then
        return SHITIANSTONE_OPERA_TYPE.FUMO_ACTIVE
    end

    return 0
end
