CrossMarryRankWGData = CrossMarryRankWGData or BaseClass()
function CrossMarryRankWGData:__init()
	if CrossMarryRankWGData.Instance then
		ErrorLog("[CrossMarryRankWGData] Attempt to create singleton twice!")
		return
	end
    -- 单例
	CrossMarryRankWGData.Instance = self

    self.rank_data = nil
    self.sort_rank_list = nil

    self:InitConfig()
end

function CrossMarryRankWGData:__delete()
    CrossMarryRankWGData.Instance = nil
    self.rank_data = nil
    self.sort_rank_list = nil
end

-- 初始化配置
function CrossMarryRankWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("cross_couple_chongzhi_rank_auto")
    if not IsEmptyTable(cfg) then
        self.rank_cfg_list = ListToMapList(cfg.reward, "grade")
    end
end

-- 设置协议信息
function CrossMarryRankWGData:SetRankData(protocol)
    self.rank_data = {}
    self.rank_data.self_rank = protocol.self_rank
    self.rank_data.self_recharge_value = protocol.self_recharge_value
    self.rank_data.rank_info_list = protocol.rank_info_list
    self.rank_data.couple_info = protocol.couple_info
    self.rank_data.grade =  protocol.grade
    self:SetSortRankList()
end

function CrossMarryRankWGData:SetSortRankList()
    local cfg_list = self:GetRankCfgList()
    self.sort_rank_list = {}
    if IsEmptyTable(cfg_list) then
        return 
    end

    --拿到配置表中的最大名次数
    local max_rank = cfg_list[#cfg_list].max_rank
    for i=1,max_rank do
        local data = self.rank_data.rank_info_list[i]
        if not data then
            data = {
                rank = i,       --rank 排名数
                is_rank = true  --判断是否是虚位以待, 
            }
        else
            data.is_rank = false
        end

        self.sort_rank_list[i] = data
    end
end

function CrossMarryRankWGData:GetSortRankList()
    return self.sort_rank_list or {}
end

--获取榜一信息
function CrossMarryRankWGData:GetTopRankInfo()
    return ((self.rank_data or {}).rank_info_list or {})[1]
end

function CrossMarryRankWGData:GetRankData()
    return self.rank_data or {}
end

--获取当前轮次排名奖励配置
function CrossMarryRankWGData:GetRankCfgList()
    if IsEmptyTable(self.rank_data) then
        return nil
    end

    return (self.rank_cfg_list or {})[self.rank_data.grade]
end

--取最后一条上榜配置
function CrossMarryRankWGData:GetLastRankCfg()
    local cfg_list = self:GetRankCfgList()
    if IsEmptyTable(cfg_list) then
        return nil
    end
    return cfg_list[#cfg_list] or {}
end

function CrossMarryRankWGData:GetRankCfgByRank(rank)
    local rank_cfg_list = self:GetRankCfgList()
    if IsEmptyTable(rank_cfg_list) then
        return nil
    end

    for k, v in ipairs(rank_cfg_list) do
        if rank >= v.min_rank and rank <= v.max_rank then
            return v
        end
    end

    return nil
end

function CrossMarryRankWGData:GetRankCfgByRecharge(value)
    local rank_cfg_list = self:GetRankCfgList()
    if IsEmptyTable(rank_cfg_list) then
        return nil
    end

    local reach_cfg = {}
    for i = #rank_cfg_list, 1, -1 do
        if value >= rank_cfg_list[i].reach_value then
            reach_cfg = rank_cfg_list[i]
        end
    end

    return reach_cfg
end

--计算再充值多少可以提升到第几名
function CrossMarryRankWGData:GetNeedRechargeToNextRank()
    local rank_data = self:GetRankData()
    local cfg_list = self:GetRankCfgList()
    if IsEmptyTable(rank_data) or IsEmptyTable(cfg_list) then
        return 0, 0
    end

    local rank_list = rank_data.rank_info_list
    local rank = rank_data.self_rank
    local self_recharge = rank_data.self_recharge_value
    local need_charge = 0
    local next_rank = 0
    local next_cfg = nil
    local next_info = nil
    --获取下一级的配置
    if rank <= 0 then--未上榜
        next_cfg = self:GetLastRankCfg()
    elseif rank <= 1 then--榜一
        return 0, 0
    else
        next_cfg = self:GetRankCfgByRank(rank - 1)
    end

    if IsEmptyTable(next_cfg) then
        return 0, 0
    end

    for i = next_cfg.max_rank, next_cfg.min_rank, -1 do--取能达到排名区间的最大值
        next_rank = i
        if rank_list[i] then 
            next_rank = i >= next_cfg.max_rank and i or i + 1
            break
        end
    end

    next_info = rank_list[next_rank]
    need_charge = next_cfg.reach_value - self_recharge 
    if next_info and next_info.recharge_value >= next_cfg.reach_value then--在排名榜上此位置已经有人，取排名榜上的值
        need_charge = next_info.recharge_value - self_recharge + 1
    end

    return need_charge, next_rank
end

--计算自己可以达到第几名
function CrossMarryRankWGData:GetCanReachRank()
    local rank_data = self:GetRankData()
    local self_recharge = rank_data.self_recharge_value
    local reach_cfg = self:GetRankCfgByRecharge(self_recharge)--在配置里能达到几名
    if IsEmptyTable(reach_cfg) then
        return 0
    end
    
    local rank_list = self:GetSortRankList()
    local rank = reach_cfg.min_rank
    for i = #rank_list , 1, -1 do--在排行榜里能排几名
        if not rank_list[i].is_rank and self_recharge < rank_list[i].recharge_value then
            rank = i + 1
        end
    end

    local last_cfg = self:GetLastRankCfg()
    if rank > last_cfg.max_rank then--未上榜
        return 0
    end

    return rank
end