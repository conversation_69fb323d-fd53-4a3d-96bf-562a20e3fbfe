EternalNightRewardView = EternalNightRewardView or BaseClass(SafeBaseView)

local SelectState = {
    Person = 1,
    Rank = 2
}

function EternalNightRewardView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 560)})
	self:AddViewResource(0, "uis/view/eternal_night_ui_prefab", "layout_eternal_night_reward")
   	self:SetMaskBg()
end

function EternalNightRewardView:__delete()
end

function EternalNightRewardView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.act_jjc_yulan_reward
	self.person_list = AsyncListView.New(EternalNightPersonRewardItem, self.node_list["person_list"])
	self.rank_list = AsyncListView.New(EternalNightRankRewardItem, self.node_list["rank_list"])

	self.node_list["btn_person"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSwitch, self, SelectState.Person))
	self.node_list["btn_rank"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSwitch, self, SelectState.Rank))
	self:OnClickSwitch(SelectState.Person)
end

function EternalNightRewardView:ReleaseCallBack()
	if self.person_list then
		self.person_list:DeleteMe()
		self.person_list = nil
	end
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
end

function EternalNightRewardView:ShowIndexCallBack()
	self.node_list["btn_person"].toggle.isOn = true
end

function EternalNightRewardView:OnFlush()

end

function EternalNightRewardView:OnClickSwitch(state)
    if state == SelectState.Person then
        local data_list = EternalNightWGData.Instance:GetScoreRewardCfg()
        self.person_list:SetDataList(data_list)
    elseif state == SelectState.Rank then
        local data_list = EternalNightWGData.Instance:GetRankRewardCfg()
        self.rank_list:SetDataList(data_list)
    end
end


-----------------------EternalNightPersonRewardItem-----------------
EternalNightPersonRewardItem = EternalNightPersonRewardItem or BaseClass(BaseRender)

function EternalNightPersonRewardItem:__init()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
end

function EternalNightPersonRewardItem:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function EternalNightPersonRewardItem:OnFlush()
	if not self.data then return end 
	--self.node_list["bg"]:SetActive(self.index % 2 == 1)
	local reward_item = {}
	if self.data.reward_item then
		for i=0,#self.data.reward_item do
			table.insert(reward_item,self.data.reward_item[i])
		end
	end

	self.node_list["text"].text.text = string.format(Language.EternalNight.ScoreStr1,self.data.score)
	local player_info = EternalNightWGData.Instance:GetPlayerInfoBySelfId()
	local my_score = player_info and player_info.score or 0
	--self.node_list["ylq_img"]:SetActive(my_score >= self.data.score)

	self.reward_list:SetRefreshCallback(function(cell, cell_index)
        if cell then
            cell:SetReceiveVisible(my_score >= self.data.score)
        end
    end)
	self.reward_list:SetDataList(reward_item)
end


-----------------------EternalNightRankRewardItem-----------------
EternalNightRankRewardItem = EternalNightRankRewardItem or BaseClass(BaseRender)

function EternalNightRankRewardItem:__init()
	self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])
end

function EternalNightRankRewardItem:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function EternalNightRankRewardItem:OnFlush()
	if not self.data then return end
	--self.node_list["bg"]:SetActive(self.index % 2 == 1) 
	local reward_item = {}
	if self.data.reward_item then
		for i=0,#self.data.reward_item do
			table.insert(reward_item,self.data.reward_item[i])
		end
	end
	self.reward_list:SetDataList(reward_item)
	local rank_str = ""
	if self.data.min_rank == self.data.max_rank then
		rank_str = self.data.min_rank
	else
		rank_str = self.data.min_rank .. "-" .. self.data.max_rank
	end
	self.node_list["text"].text.text = string.format(Language.EternalNight.RankStr,rank_str)
end
