TogetherInviteFriendView = TogetherInviteFriendView or BaseClass(SafeBaseView)

function TogetherInviteFriendView:__init()
	self.view_layer = UiLayer.Pop
    self:SetMaskBg(true)
    self.view_name = "TogetherInviteFriendView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(814, 578)})
    self:AddViewResource(0, "uis/view/world_treasure_ui_prefab", "layout_together_invite_friend_view")
end

function TogetherInviteFriendView:ReleaseCallBack()
	if self.invite_list_view then
		self.invite_list_view:DeleteMe()
		self.invite_list_view = nil
	end
end

function TogetherInviteFriendView:LoadCallBack()
	if not self.invite_list_view then
		self.invite_list_view = AsyncListView.New(TogetherFriendListCell, self.node_list["together_invite_list"])
	end
	self.node_list["title_view_name"].text.text = Language.WorldTreasure.TogetherTitle
	self.node_list["lbl_tips"].text.text = Language.WorldTreasure.TogetherNoFriendTips
end

function TogetherInviteFriendView:OpenCallBack()
	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_WALK_TOGETHER_FRIEND_INFO)
end

function TogetherInviteFriendView:OnFlush()
	local friend_list = WorldTreasureWGData.Instance:GetTogetherInviteFriendList()--SocietyWGData.Instance:GetFriendList2()
	self.node_list["layout_blank_tip"]:SetActive(#friend_list <= 0)
	self.node_list["together_invite_list"]:SetActive(#friend_list > 0)
	self.invite_list_view:SetDataList(friend_list)
end

function TogetherInviteFriendView:FlushTextInvite()
    if not IsEmptyTable(self.invite_list_view.cell_list) then
        for k, v in pairs(self.invite_list_view.cell_list) do
            v:FlushTextInvite()
        end
    end
end

----------------------------------------------------------------------------
--TogetherFriendListCell 		好友邀请列表
----------------------------------------------------------------------------

TogetherFriendListCell = TogetherFriendListCell or BaseClass(BaseRender)

function TogetherFriendListCell:__init()

end

function TogetherFriendListCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_invite"], BindTool.Bind1(self.OnClickInvite, self))
	if not self.head_cell then
		self.head_cell = BaseHeadCell.New(self.node_list["head_cell"])
	end
	
end

function TogetherFriendListCell:ReleaseCallBack()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function TogetherFriendListCell:OnFlush()
	if self.data == nil or self.data.uid == 0 then
		return
	end

	self:FlushTextInvite()

	local data = {}
	data.role_id = self.data.uid
	data.prof = self.data.prof
	data.sex = self.data.sex
	data.fashion_photoframe = self.data.shizhuang_photoframe or self.data.fashion_photoframe
    self.head_cell:SetData(data)

	self.node_list["lbl_role_name"].text.text = self.data.user_name

	--XUI.UpdateRoleHead(self.node_list["head_cell"], self.node_list["custom_role_head"], self.data.user_id, self.data.sex,self.data.prof, false,nil,true)

	
	local level_str = string.format(Language.NewTeam.PTLevel3, self.data.level)
	EmojiTextUtil.ParseRichText(self.node_list["lbl_role_level"].emoji_text, level_str, 20, COLOR3B.WHITE)
end

function TogetherFriendListCell:FlushTextInvite()
    local role_id = self.data.uid
    if WorldTreasureWGData.Instance:GetCacheCDByRoleid(role_id) > 0 then
		local cd = WorldTreasureWGData.Instance:GetCacheCDByRoleid(role_id)
        self.node_list["text_invite"].text.text = string.format(Language.NewTeam.RemainTime, cd)
        XUI.SetButtonEnabled(self.node_list["btn_invite"], false)
        return
    end
    XUI.SetButtonEnabled(self.node_list["btn_invite"], true)
	self.node_list["text_invite"].text.text = (Language.NewTeam.Invite)
end

function TogetherFriendListCell:OnClickInvite()
	if self.data == nil or self.data.uid == 0 then
		return
	end
	WorldTreasureWGData.Instance:AddCacheCDList(self.data.uid, 10)
	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_WALK_TOGETHER_INVITE, self.data.uid)
end