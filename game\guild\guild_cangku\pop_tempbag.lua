-----------------------------宗门捐献背包-------------------------------
function GuildView:GuildBagLoadCallBack()
	self.tempbag_grid = ShenShouGrid.New()
	local cell_count = 200

	self.tempbag_grid:CreateCells({col = 5, cell_count = cell_count, list_view = self.node_list["ph_tempbag_grid"], itemRender = CKPopBagCell})
	self.tempbag_grid:SetSelectCallBack(BindTool.Bind1(self.SelectCellCallBack, self))
	self.tempbag_grid:SetIsMultiSelect(true)
	self.tempbag_grid:SetStartZeroIndex(false)

	--批量贡献
	self.node_list.btn_juanxian.button:AddClickListener(BindTool.Bind(self.OnClcikJuanXian,self))

	self.pinzhi_data_list = Language.Guild.GuildPinZhiName
	self.pinzhi_name = ""
	self.pinzhi_jie = 0

	self.pinjie_data_list = Language.Guild.GuildPinJieName2
	self.pinjie_color = Language.Guild.GuildPinJieName2[1][2]
	self.pinjie_name = ""

	-- 品质
	self.guildbag_pinzhi_list_view = AsyncListView.New(CKPinZhiListRender, self.node_list["guildbag_ph_pinzhi_list"])
	self.guildbag_pinzhi_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectGuildBagPinZhiItemCallBack, self))

	self.guildbag_pinzhi_list_view:SetDataList(self.pinzhi_data_list)
	self.node_list.guildbag_layout_pinzhi_list:SetActive(false)
	self.node_list.guildbag_layout_pinjie_list:SetActive(false)
	self.node_list.guildbag_jiantou_pinzhi_down:SetActive(not self.node_list.guildbag_layout_pinzhi_list:GetActive())
	self.node_list.guildbag_jiantou_pinzhi_up:SetActive(self.node_list.guildbag_layout_pinzhi_list:GetActive())
	self.node_list.guildbag_jiantou_pinjie_down:SetActive(not self.node_list.guildbag_layout_pinjie_list:GetActive())
	self.node_list.guildbag_jiantou_pinjie_up:SetActive(self.node_list.guildbag_layout_pinjie_list:GetActive())

	-- 品阶
	self.guildbag_pinjie_list_view = AsyncListView.New(CKPinJieListRender, self.node_list["guildbag_ph_pinjie_list"])
	self.guildbag_pinjie_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectGuildBagPinJieItemCallBack, self))
	self.guildbag_pinjie_list_view:SetDataList(self.pinjie_data_list)
	self.node_list.btn_pinzhi.button:AddClickListener(BindTool.Bind(self.OnGuildBagClickCKPinZhi, self))
	self.node_list.btn_pinjie.button:AddClickListener(BindTool.Bind(self.OnGuildBagClickCKPinJie, self))

	--点击其他区域关闭
	self.node_list.close_pinzhi_list.button:AddClickListener(BindTool.Bind(self.OnGuildBagClickCKPinZhi, self))
	self.node_list.close_pinjie_list.button:AddClickListener(BindTool.Bind(self.OnGuildBagClickCKPinJie, self))

	self.node_list.pinzhi_text.text.text = Language.Guild.GuildPinZhiName[1][1] or ""
	self.node_list.pinjie_text.text.text = Language.Guild.GuildPinJieName2[1][1] or ""
end

function GuildView:GuildBagReleaseCallBack()
	if self.tempbag_grid then
		self.tempbag_grid:DeleteMe()
		self.tempbag_grid = nil
	end
	if self.guildbag_pinjie_list_view then
		self.guildbag_pinjie_list_view:DeleteMe()
		self.guildbag_pinjie_list_view = nil
	end
	if self.guildbag_pinzhi_list_view then
		self.guildbag_pinzhi_list_view:DeleteMe()
		self.guildbag_pinzhi_list_view = nil
	end
end

function GuildView:FlushGuildBagList(is_select)
	local data = GuildCangKuWGData.Instance:GetEquipBagData(self.pinzhi_jie,self.pinjie_color)
    self.tempbag_grid:SetDataList(data)
    if is_select then
		local bagnum = GuildCangKuWGData.Instance:GetItemListNum() + #data--仓库的总容量
		local open_count = GuildCangKuWGData.Instance:GetOpengridCount()
		if bagnum >= open_count then
			local has_count = GuildCangKuWGData.Instance:GetItemListNum()
			local num = open_count - has_count
			self.tempbag_grid:SetAllSelectCell(num)
		else
			self.tempbag_grid:SetAllSelectCell(#data)
		end
    end
	self:FlushConsume()
end

function GuildView:SelectCellCallBack(cell,can_not_select)
	-- print_error(">>>>>>>>>>cell",cell.data)
	if can_not_select then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.RongLiangMax)--仓库容量不足，不可以捐献
		return
	end
	self:FlushConsume()
end

function GuildView:ClearBagList(is_select)
	self.tempbag_grid:CancleAllSelectCell()
	self.node_list.jifen_text:SetActive(false)
	self:FlushGuildBagList(is_select)
end

function GuildView:FlushConsume()
	local select_data = self.tempbag_grid:GetAllSelectCell()
	local bagnum = GuildCangKuWGData.Instance:GetItemListNum() + #select_data--仓库的总容量
	local open_count = GuildCangKuWGData.Instance:GetOpengridCount()
	if bagnum >= open_count then
		self.tempbag_grid:SetNoSelectState(true)
		-- self.node_list.jifen_text.text.text = 0
		-- return
	else
		self.tempbag_grid:SetNoSelectState(false)
	end
	
	local num = 0
	if not IsEmptyTable(select_data) then
		for k,v in pairs(select_data) do
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			local star_level = v.param and v.param.star_level or 0
			local score = GuildCangKuWGData.Instance:GetStorageAccessScore(item_cfg.order,item_cfg.color,star_level)
			num = num + score
		end
	end
	self.node_list.jifen_text.text.text = "+" .. num
	self.node_list.jifen_text:SetActive( 0 ~= num)
end

function GuildView:SelectGuildBagPinZhiItemCallBack(item, cell_index, is_default, is_click)
	if is_default then return end
	if not item or not item:GetData() then return end
	local data = item:GetData()
	self.pinzhi_jie = data[2]
	self.pinzhi_name = data[1]
	self.node_list.pinzhi_text.text.text = self.pinzhi_name
	self.node_list.guildbag_layout_pinzhi_list:SetActive(false)
	self.node_list.guildbag_jiantou_pinzhi_down:SetActive(not self.node_list.guildbag_layout_pinzhi_list:GetActive())
    self.node_list.guildbag_jiantou_pinzhi_up:SetActive(self.node_list.guildbag_layout_pinzhi_list:GetActive())
    local is_select = self.pinzhi_jie ~= 0
	self:ClearBagList(is_select)
end

function GuildView:SelectGuildBagPinJieItemCallBack(item, cell_index, is_default, is_click)
	if is_default then return end
	if not item or not item:GetData() then return end
	local data = item:GetData()
	self.pinjie_color = data[2]
	self.pinjie_name = data[1]
	self.node_list.pinjie_text.text.text = self.pinjie_name
	self.node_list.guildbag_layout_pinjie_list:SetActive(false)
	self.node_list.guildbag_jiantou_pinjie_down:SetActive(not self.node_list.guildbag_layout_pinjie_list:GetActive())
    self.node_list.guildbag_jiantou_pinjie_up:SetActive(self.node_list.guildbag_layout_pinjie_list:GetActive())
    local is_select = self.pinjie_color ~= 0
	self:ClearBagList(is_select)
end

function GuildView:OnClcikJuanXian()
	local destroy_list = self.tempbag_grid:GetAllSelectCell()
	if destroy_list and not next(destroy_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.JuanXianTips)
	else
		local destroy_item_list = {}
		for k,v in pairs(destroy_list) do
			destroy_item_list[#destroy_item_list + 1] = {storge_index = v.index, item_id = v.item_id}
		end
		local bagnum = GuildCangKuWGData.Instance:GetItemListNum() + #destroy_item_list--仓库的总容量
		local open_count = GuildCangKuWGData.Instance:GetOpengridCount()
		if bagnum > open_count then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.RongLiangMax)--仓库容量不足，不可以捐献
			return
		end

		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_juanxian, is_success = true, pos = Vector2(0, 0)})
		GuildWGCtrl.Instance:SendStorgetDonteItems(#destroy_item_list, destroy_item_list) --发送协议的地方
		--self:Close()
	end
	self.tempbag_grid:CancleAllSelectCell()
end

function GuildView:OnGuildBagClickCKPinZhi()
	self.node_list.guildbag_layout_pinzhi_list:SetActive(not self.node_list.guildbag_layout_pinzhi_list:GetActive())
	self.node_list.guildbag_jiantou_pinzhi_down:SetActive(not self.node_list.guildbag_layout_pinzhi_list:GetActive())
	self.node_list.guildbag_jiantou_pinzhi_up:SetActive(self.node_list.guildbag_layout_pinzhi_list:GetActive())
end

function GuildView:OnGuildBagClickCKPinJie()
	self.node_list.guildbag_layout_pinjie_list:SetActive(not self.node_list.guildbag_layout_pinjie_list:GetActive())
	self.node_list.guildbag_jiantou_pinjie_down:SetActive(not self.node_list.guildbag_layout_pinjie_list:GetActive())
	self.node_list.guildbag_jiantou_pinjie_up:SetActive(self.node_list.guildbag_layout_pinjie_list:GetActive())
end

----------------------仓库背包格子--------------------------
CKPopBagCell = CKPopBagCell or BaseClass(ItemCell)
function CKPopBagCell:__init()
	self.need_default_eff = true
	self:SetItemTipFrom(ItemTip.FROM_BAG_ON_GUILD_STORGE)
    self:SetIsShowTips(false)
    self:UseNewSelectEffect(true)
end

function CKPopBagCell:SetSelect(is_select, item_call_back)
	ItemCell.SetSelectEffect(self, is_select)
end