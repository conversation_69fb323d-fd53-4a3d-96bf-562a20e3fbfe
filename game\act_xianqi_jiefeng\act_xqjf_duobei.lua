

function ActXianQiJieFengView:InitDBView()
	XUI.AddClickEventListener(self.node_list.db_btn_tip, BindTool.Bind(self.OnDBBtnTipClick<PERSON>nadler,self))

	local theme_cfg = TianshenRoadWGData.Instance:GetThemeCfgByTabIndex(TabIndex.tianshenroad_duobei)
	if theme_cfg ~= nil then
		self.node_list.db_tip_label.text.text = theme_cfg.rule_tip
		self.node_list.db_title_desc.text.text = theme_cfg.rule_desc
	end

	self.db_reward_list = AsyncListView.New(XQJFDuoBeiItemRender, self.node_list.db_list)

	self:FlushDBView()
	self:DBTimeCountDown()
end

function ActXianQiJieFengView:ReleaseDBView()
	if self.db_reward_list then
		self.db_reward_list:DeleteMe()
		self.db_reward_list = nil
	end
	CountDownManager.Instance:RemoveCountDown("xianqijiefeng_duobei_count_down")
end

function ActXianQiJieFengView:FlushDBView()
	self:FlushDBReward()
end

function ActXianQiJieFengView:FlushDBReward()
	local info_list = ActXianQiJieFengWGData.Instance:GetDuoBeiInfo()
	if info_list and self.db_reward_list then
		self.db_reward_list:SetDataList(info_list)
	end
end

function ActXianQiJieFengView:OnDBBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = ActXianQiJieFengWGData.Instance:GetActivityTip(TabIndex.xianqi_jiefeng_duobei)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

--有效时间倒计时
function ActXianQiJieFengView:DBTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("xianqijiefeng_duobei_count_down")
	local invalid_time = ActXianQiJieFengWGData.Instance:GetActivityInValidTime(TabIndex.xianqi_jiefeng_duobei)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("xianqijiefeng_duobei_count_down", BindTool.Bind1(self.UpdateDBCountDown, self), BindTool.Bind1(self.DBTimeCountDown, self), invalid_time, nil, 1)
	end
end

function ActXianQiJieFengView:UpdateDBCountDown(elapse_time, total_time)
	self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

-------------------------------------

XQJFDuoBeiItemRender = XQJFDuoBeiItemRender or BaseClass(BaseRender)

function XQJFDuoBeiItemRender:LoadCallBack()
	--self.db_reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnClickDuoBei, self))
end

function XQJFDuoBeiItemRender:__delete()
	-- if self.db_reward_item_list then
	-- 	self.db_reward_item_list:DeleteMe()
	-- 	self.db_reward_item_list = nil
	-- end
end

function XQJFDuoBeiItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	local bundle, asset = ResPath.GetCommonIcon(data.cfg.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)
	--self.node_list.beishu.text.text = string.format("x%d", data.cfg.reward_mult)
	local beishu = CommonDataManager.GetDaXie(data.cfg.reward_mult)
	beishu = beishu == Language.TianShenRoad.twoconversion[1] and Language.TianShenRoad.twoconversion[2] or beishu
	self.node_list.beishu.text.text = string.format(Language.TianShenRoad.beishudown,beishu)
	self.node_list.name.text.text = data.cfg.wanfa_name

	-- if IsEmptyTable(data.cfg.reward_item) then
	-- 	local reward_list = TianshenRoadWGData.Instance:GetDuoBeiRewardListByTaskType(data.cfg.task_type)
	-- 	self.db_reward_item_list:SetDataList(reward_list)
	-- else
	-- 	local list = SortTableKey(data.cfg.reward_item)
	-- 	self.db_reward_item_list:SetDataList(list)
	-- end
end

function XQJFDuoBeiItemRender:OnBtnClickDuoBei()
	local data = self:GetData()
	if data and data.cfg then
		FunOpen.Instance:OpenViewNameByCfg(data.cfg.panel)
	end
end