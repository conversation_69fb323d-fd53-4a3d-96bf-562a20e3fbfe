BaGuaMiZhenWGData = BaGuaMiZhenWGData or BaseClass()

function BaGuaMiZhenWGData:__init()
	if BaGuaMiZhenWGData.Instance then
		print_error("[BaGuaMiZhenWGData] Attempt to create singleton twice!")
		return
	end
	BaGuaMiZhenWGData.Instance = self

	local baguazhen_cfg = ConfigManager.Instance:GetAutoConfig("baguamizhenfb_cfg_auto")
	self.old_reward_cfg = baguazhen_cfg.level_cfg
	self.reward_cfg = ListToMapList(baguazhen_cfg.level_cfg, "type")
	self.reward_cfg_type_index = ListToMap(baguazhen_cfg.level_cfg, "type", "type_index")
	self.boss_info_cfg = baguazhen_cfg.fuben_open
	self.other_cfg = baguazhen_cfg.other
	self.role_level = nil
end

function BaGuaMiZhenWGData:OnSCBaGuaMiZhenInfo(protocol)
	self.role_level = protocol.role_lv
end

function BaGuaMiZhenWGData:GetBaGuaFbOther()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("baguamizhenfb_cfg_auto").other[1]
	return other_cfg
end

function BaGuaMiZhenWGData:GetZeroTimeRoleLevel()
	local def_cfg = self:GetDataByTypeIndex(1, 1)
	--创角当天，默认玩家打最低难度区间的怪物
	local role_vo = RoleWGData.Instance:GetRoleVo()
	local end_time = TimeWGCtrl.Instance:NowDayTimeEnd(role_vo.create_timestamp)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if end_time > server_time then
		return def_cfg.min_lv
	end

	if self.role_level then
		if def_cfg.min_lv > self.role_level then
			self.role_level = def_cfg.min_lv
		end
		return self.role_level
	end

	return def_cfg.min_lv
end

function BaGuaMiZhenWGData:__delete()
	BaGuaMiZhenWGData.Instance = nil
	self.role_level = nil
end

--奖励预览(读配置,外部界面上的奖励,随机奖励在后面)
function BaGuaMiZhenWGData:GetBaGuaZhenRewardList(layer)
	--自身等级
	local role_level = self:GetZeroTimeRoleLevel()
	--选择难度(简易)
	local part = layer and layer or 1
	--匹配奖励
	local reward_list = {}
	local reward_cfg = self.reward_cfg[part]
	if not reward_cfg then
		return reward_list
	end

	for k,v in pairs(reward_cfg) do
		if v.min_lv <= role_level and v.max_lv >= role_level then
			local next_data = self:GetDataByTypeIndex(part, v.type_index)
			if not next_data then
				next_data = v
			end
			for i,v in pairs(next_data.show_reward_item) do
				v.baguamizhen_random = 0
				v.show_duobei = true
				v.task_type = RATSDUOBEI_TASK.FUWENFUBEN
				v.sort_index1 = i + 1
				--v.sort_index2 = 10 - ItemWGData.Instance:GetItemConfig(v.item_id).color
				reward_list[i+1] = v
				--table.insert(reward_list,v)
			end
			--table.sort(reward_list,SortTools.KeyLowerSorter("sort_index2", "sort_index1"))
			for i,v in pairs(next_data.random_item) do
				v.baguamizhen_random = 1
				table.insert(reward_list,v)
			end
		end
	end

	return reward_list
end

--奖励预览(读配置内部task界面上的奖励,随机奖励在前面)
function BaGuaMiZhenWGData:GetBaGuaZhenRewardList1(layer)
	--自身等级
	local role_level = self:GetZeroTimeRoleLevel()
	--选择难度(简易)
	local part = layer and layer or 1
	--匹配奖励
	local reward_list = {}
	local reward_cfg = self.reward_cfg[part]
	if not reward_cfg then
		return reward_list
	end

	for k,v in pairs(reward_cfg) do
		if v.min_lv <= role_level and v.max_lv >= role_level then
			local next_data = self:GetDataByTypeIndex(part, v.type_index)
			if not next_data then
				next_data = v
			end
			for i,v in pairs(next_data.random_item) do
				v.baguamizhen_random = 1
				v.show_duobei = true
				v.task_type = RATSDUOBEI_TASK.FUWENFUBEN
				table.insert(reward_list,v)
			end

			for i,v in pairs(next_data.reward_item) do
				v.baguamizhen_random = 0
				table.insert(reward_list,v)
			end
		end
	end
	return reward_list
end

--boos信息
function BaGuaMiZhenWGData:GetBossInfoCfg()
	return self.boss_info_cfg
end

--boos信息
function BaGuaMiZhenWGData:GetBossInfoLVCfg(layer)
	local fuben_open = self:GetBossInfoCfg()
	for k,v in pairs(fuben_open) do
		if v.type == layer then
			return v.lv
		end
	end
end

--判断当前八卦迷阵本是否开启
function BaGuaMiZhenWGData:GetBaGuaMiZhenIsOpen(layer)
	local layer = layer or 1
	local role_level = self:GetZeroTimeRoleLevel()
	local fuben_open_level = self:GetBossInfoLVCfg(layer)
	if role_level >= fuben_open_level then
		return true,fuben_open_level
	else
		return false,fuben_open_level
	end
end

--根据等级读取各个难度的推荐战力(五个推荐战力,用来显示在外部面板上)
function BaGuaMiZhenWGData:GetBaGuaMiZhenTuiJianBuyLevel()
	local role_level = self:GetZeroTimeRoleLevel()
	local tuijianzhanli = {}
	local reward_cfg = {}
	for i = 1, 5 do
		reward_cfg = self.reward_cfg[i]
		if not IsEmptyTable(reward_cfg) then
			for k, v in ipairs(reward_cfg) do
				if v.min_lv <= role_level and v.max_lv >= role_level then
					local next_data = self:GetDataByTypeIndex(i, v.type_index)
					if not next_data then
						next_data = v
					end
					table.insert(tuijianzhanli, next_data.tuijian_zl)
				end
			end
		end
	end

	return tuijianzhanli
end

--根据难度和等级获取推荐战力(单个,用来显示在副本内的taskpanel上)
function BaGuaMiZhenWGData:GetBaGuaMiZhenZhanLiBuyLevelAndLayer(layer)
	local role_level = self:GetZeroTimeRoleLevel()
	local reward_cfg = self.reward_cfg[layer]
	if not reward_cfg then
		return 0
	end

	for k,v in pairs(reward_cfg) do
		if v.min_lv <= role_level and v.max_lv >= role_level then
			local next_data = self:GetDataByTypeIndex(layer, v.type_index)
			if not next_data then
				next_data = v
			end
			return next_data.tuijian_zl
		end
	end

	return reward_cfg[1].tuijian_zl
end

--根据等级读取各个难度的扫荡战力
function BaGuaMiZhenWGData:GetBaGuaMiZhenSaoDangBuyLevel(layer)
	local role_level = self:GetZeroTimeRoleLevel()
	local reward_cfg = self.reward_cfg[layer]
	if not reward_cfg then
		return 0
	end

	for k,v in pairs(reward_cfg) do
		if v.min_lv <= role_level and v.max_lv >= role_level then
			local next_data = self:GetDataByTypeIndex(layer, v.type_index)
			if not next_data then
				next_data = v
			end
			return next_data.saodang_zl
		end
	end

	return reward_cfg[1].saodang_zl
end

--根据等级,战力 来推荐默认选中副本难度
function BaGuaMiZhenWGData:GetLayerBuyLevelAndZhanLi()
	--个人战斗力和等级
	local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
	local role_level = self:GetZeroTimeRoleLevel()

	for k,v in pairs(self.old_reward_cfg) do
		if  v.min_lv <= role_level and v.max_lv >= role_level and role_zhanli < v.tuijian_zl then
			local layer = v.type - 1
			if layer < 1 then layer = 1 end
			if layer > 5 then layer = 5 end
			return layer
		end
	end

	return 5
end

--扫荡消耗 物品和数量
function BaGuaMiZhenWGData:GetBaGuaMiZhenItemId()
	local item = self.other_cfg[1].sweep_mark_id
	local num = self.other_cfg[1].sweep_mark_num
	return item,num, self.other_cfg[1].seq
end

function BaGuaMiZhenWGData:GetCurLayerBossID(layer)
	local role_level = self:GetZeroTimeRoleLevel()
	local reward_cfg = self.reward_cfg[layer]
	if not reward_cfg then
		return 0
	end

	for k,v in pairs(reward_cfg) do
		if v.min_lv <= role_level and v.max_lv >= role_level then
			local next_data = self:GetDataByTypeIndex(layer, v.type_index)
			if not next_data then
				next_data = v
			end
			return next_data.boss_id
		end
	end

	return reward_cfg[1].boss_id
end

function BaGuaMiZhenWGData:GetCurBossName(layer)
	local boss_id = self:GetCurLayerBossID(layer)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list
	local monster_name = monster_cfg[boss_id] and monster_cfg[boss_id].name or ""
	return monster_name
end

--根据组,组内索引获取配置
function BaGuaMiZhenWGData:GetDataByTypeIndex(_type, type_index)
	if self.reward_cfg_type_index[_type] and self.reward_cfg_type_index[_type][type_index] then
		return self.reward_cfg_type_index[_type][type_index]
	end
	return nil
end
