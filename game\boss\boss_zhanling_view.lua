BossZhanLingView = BossZhanLingView or BaseClass(SafeBaseView)

function BossZhanLingView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_zhanling_view")
end

function BossZhanLingView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_buy_zhanling_level, BindTool.Bind(self.OnClickBugZhanLingLevelBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_active_high_zhanling, BindTool.Bind(self.OnClickActiveHighZhanLingBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_m_get_all_reward, BindTool.Bind(self.OnClickGetAllReward, self))

    self.scorll_index = -1
    local max_level_cfg = BossZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()
    self.max_show_level = max_level_cfg and max_level_cfg.seq or 0

    if not self.gd_show_reward_item then
        self.gd_show_reward_item = BossZhanLingRewardRender.New(self.node_list["gd_reward_cell"])
        self.gd_show_reward_item:SetIsGuDingReward(true)
    end

    if not self.left_show_reward_list then
        self.left_show_reward_list = AsyncListView.New(ItemCell, self.node_list.left_show_reward_list)
        self.left_show_reward_list:SetStartZeroIndex(true)
    end

    if not self.m_reward_list then
        self.m_reward_list = AsyncListView.New(BossZhanLingRewardRender, self.node_list.m_reward_list)
        self.m_reward_list:SetStartZeroIndex(true)
        self.node_list.m_reward_list.scroller.scrollerEndScrolled = BindTool.Bind(self.OnScrollValueChanged, self)
    end

    local daily_score_limit = BossZhanLingWGData.Instance:GetDailyScoreLimit()
    self.node_list.tips_label.text.text = string.format(Language.BossZhanLing.ZhanLingTipLable, daily_score_limit)
    BossZhanLingWGCtrl.Instance:SendBossZhanLingReq(BOSS_ZHANLING_OPERATE_TYPE.HUNT_MONSTER_ORDER_OPERA_TYPE_INFO)
end

function BossZhanLingView:ReleaseCallBack()
    if self.left_show_reward_list then
        self.left_show_reward_list:DeleteMe()
        self.left_show_reward_list = nil
    end

    if self.m_reward_list then
        self.m_reward_list:DeleteMe()
        self.m_reward_list = nil
    end

    if self.gd_show_reward_item then
        self.gd_show_reward_item:DeleteMe()
        self.gd_show_reward_item = nil
    end

    if CountDownManager.Instance:HasCountDown("BossZhanLingView") then
        CountDownManager.Instance:RemoveCountDown("BossZhanLingView")
    end

    self.max_show_level = nil
    self.old_gd_show_level = nil
    self.scorll_index = nil
end

function BossZhanLingView:SetJumpRewardflag(bool)
    self.jump_reward_flag = bool
end

function BossZhanLingView:SetZhanLingTime()
    local time = BossZhanLingWGData.Instance:GetZhanLingResetTime()
    local server_time = TimeWGCtrl.Instance:GetServerTime()

    if time > server_time then
        if CountDownManager.Instance:HasCountDown("BossZhanLingView") then
            CountDownManager.Instance:RemoveCountDown("BossZhanLingView")
        end

        self.node_list.reset_time_str.text.text = string.format(Language.BossZhanLing.ZhanLingReSetTimeDesc, TimeUtil.FormatSecondDHM2(time - server_time))
		CountDownManager.Instance:AddCountDown("BossZhanLingView", function (elapse_time, total_time)
            local valid_time = total_time - elapse_time
            if valid_time > 0 then
                if self.node_list.reset_time_str then
                    self.node_list.reset_time_str.text.text = string.format(Language.BossZhanLing.ZhanLingReSetTimeDesc, TimeUtil.FormatSecondDHM2(valid_time))
                end
            end
        end, function()
            if self.node_list.reset_time_str then
                self.node_list.reset_time_str.text.text = ""
            end
        end, time, nil, 1)
    else
        self.node_list.reset_time_str.text.text = ""
    end
end

function BossZhanLingView:OnFlush()
    local reward_cfg = BossZhanLingWGData.Instance:GetCurGradeSeniorOrderCfg()
    if IsEmptyTable(reward_cfg) then
        return
    end

    self:SetZhanLingTime()

    self.left_show_reward_list:SetDataList(reward_cfg.order_preview)

    local zhanling_level = BossZhanLingWGData.Instance:GetZhanLingLevel()
    local cur_zhanling_cfg = BossZhanLingWGData.Instance:GetZhanLingCfg(zhanling_level)
    local next_zhanling_cfg = BossZhanLingWGData.Instance:GetZhanLingCfg(zhanling_level + 1)
    local score = BossZhanLingWGData.Instance:GetScore()
    local is_max = IsEmptyTable(next_zhanling_cfg)
    local start_zhanling_cfg = BossZhanLingWGData.Instance:GetZhanLingCfg(0)

    local min_zhanling_level = score < start_zhanling_cfg.need_score

    if min_zhanling_level then
        self.node_list.m_level_num.text.text = 0
    else
        self.node_list.m_level_num.text.text = zhanling_level + 1
    end

    if is_max then
        self.node_list.m_pregress_label.text.text = score
        self.node_list.m_pregress_slider.slider.value = 1
    else
        local cur_exp_value = min_zhanling_level and score or (score - cur_zhanling_cfg.need_score)
        cur_exp_value = cur_exp_value > 0 and cur_exp_value or 0
        local need_act_value = min_zhanling_level and cur_zhanling_cfg.need_score or (next_zhanling_cfg.need_score - cur_zhanling_cfg.need_score)
        self.node_list.m_pregress_label.text.text = cur_exp_value .. "/" .. need_act_value
        self.node_list.m_pregress_slider.slider.value = cur_exp_value / need_act_value
    end

    self.node_list.tips_text.text.text = is_max and Language.BossZhanLing.ZhanLingTipText2 or
    Language.BossZhanLing.ZhanLingTipText

    local is_open_higer_zhanling = BossZhanLingWGData.Instance:GetHigerOrderRewardFlag()
    self.node_list.m_high_lock:CustomSetActive(not is_open_higer_zhanling)
    self.node_list.btn_active_high_zhanling:SetActive(not is_open_higer_zhanling)

    local reward_list = BossZhanLingWGData.Instance:GetCurZhanLingRewardCfgList()
    self.m_reward_list:SetDataList(reward_list)

    if self.jump_reward_flag then
        self.reward_list_jump_index = self:GetRewardAutoJumpIndex(zhanling_level)
        self:JumpToRealIndex()
        self.jump_reward_flag = nil
    end

    self:FlushGuDingShowReward(self.scorll_index)

    local remind_get = BossZhanLingWGData.Instance:GetZhanLingRemind() == 1
    self.node_list.get_all_red_point:SetActive(remind_get)

    local can_get_all_reward = BossZhanLingWGData.Instance:CanGetZhanLingAllReward()
	self.node_list.btn_m_get_all_reward:SetActive(can_get_all_reward)

    is_max = BossZhanLingWGData.Instance:GetZhanLingIsMaxLevel()
	self.node_list.btn_buy_zhanling_level:SetActive(not is_max)
end

function BossZhanLingView:GetRewardAutoJumpIndex(zhanling_level)
    local reward_list = BossZhanLingWGData.Instance:GetCurZhanLingRewardCfgList()
    local def_level = 0
    local cur_level = zhanling_level
    local max_level_cfg = BossZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()

    if IsEmptyTable(reward_list) then
		return def_level
	end

	for k, v in ipairs(reward_list) do
        local nor_can_get, higer_can_get = BossZhanLingWGData.Instance:IsCanGetZhanLingRewardBySeq(v.seq)
		if nor_can_get or higer_can_get then
			return v.seq
		end
	end

    if cur_level >= max_level_cfg.seq then
		def_level = max_level_cfg.seq
	else
        def_level = cur_level
	end

    return def_level
end

function BossZhanLingView:JumpToRealIndex()
    if self.m_reward_list then
        local percent = 0
        if self.m_reward_list.list_view and self.m_reward_list.list_view.scroller
            and self.reward_list_jump_index > 4 then
            local cell_height = 64
            local cell_num = self.m_reward_list:GetListViewNumbers()
            local total_length = cell_num * cell_height
            local view_show_height = self.node_list["m_reward_list"].rect.sizeDelta.y
            local scroll_size = total_length - view_show_height
            local jump_position = (self.reward_list_jump_index - 1) * cell_height
            percent = jump_position / scroll_size
        end

        self.m_reward_list:JumptToPrecent(percent)
    end
end

function BossZhanLingView:FlushGuDingShowReward(level)
    if not level or level < 0 then
        return
    end

    local max_level_cfg = BossZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()
    if IsEmptyTable(max_level_cfg) then
        return
    end

	local show_level = -1
    local reward_list = BossZhanLingWGData.Instance:GetCurZhanLingRewardCfgList()

    if not IsEmptyTable(reward_list) then
		for k, v in ipairs(reward_list) do
            if ((v.fixed_show > 0) and (v.seq > level) and (show_level < 0 or (show_level > 0 and show_level > v.seq))) then
                show_level = v.seq
            end
        end
	end

	if show_level < 0 then
		show_level = max_level_cfg.seq
	end

    local reward = BossZhanLingWGData.Instance:GetZhanLingCfg(show_level)

    if self.old_gd_show_level ~= show_level then
        self.gd_show_reward_item:SetData(reward)
        self.old_gd_show_level = show_level
    end
end

function BossZhanLingView:OnScrollValueChanged(scroll_obj, start_idx, end_idx)
    local now_index  = end_idx
    if self.scorll_index == now_index then
        return
    end

    self.scorll_index = now_index
    if self.reward_list_jump_index then
        if now_index >= self.reward_list_jump_index then
            self.old_gd_show_level = now_index
            self:FlushGuDingShowReward(now_index)
            self.reward_list_jump_index = nil
        end
    else
        now_index = now_index > self.max_show_level and self.max_show_level or now_index
        self:FlushGuDingShowReward(now_index)
    end
end

function BossZhanLingView:OnClickBugZhanLingLevelBtn()
    BossZhanLingWGCtrl.Instance:OpenZhanLingBuyLevelView()
end

function BossZhanLingView:OnClickActiveHighZhanLingBtn()
    BossZhanLingWGCtrl.Instance:OpenZhanLingUnLockView()
end

function BossZhanLingView:OnClickGetAllReward()
    if BossZhanLingWGData.Instance:GetZhanLingRemind() == 0 then
		TipsSystemManager.Instance:ShowSystemTips(Language.BossZhanLing.NoGetReward)
		return
	end

    BossZhanLingWGCtrl.Instance:SendBossZhanLingReq(BOSS_ZHANLING_OPERATE_TYPE.HUNT_MONSTER_ORDER_OPERA_TYPE_ONE_KEY)
end

------------------------------------------BossZhanLingRewardRender-------------------------------------------
BossZhanLingRewardRender = BossZhanLingRewardRender or BaseClass(BaseRender)
function BossZhanLingRewardRender:__init()
    self.view:SetActive(true)
end

function BossZhanLingRewardRender:LoadCallBack()
    if self.node_list["btn_high_get"] then
        self.node_list["btn_high_get"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward,self))
    end

    self.normal_item = BossZhanLingRewardItem.New(self.node_list.normal_item)

    self.high_item_list = {}
    for i = 1, 2 do
        self.high_item_list[i] = BossZhanLingRewardItem.New(self.node_list["high_item_" .. i])
        self.high_item_list[i]:SetIsHighItem(true)
        self.high_item_list[i]:SetIndex(i)
    end
end

function BossZhanLingRewardRender:__delete()
    if self.normal_item then
        self.normal_item:DeleteMe()
        self.normal_item = nil
    end

    if self.high_item_list then
        for k, v in pairs(self.high_item_list) do
            v:DeleteMe()
        end
        self.high_item_list = nil
    end
end

function BossZhanLingRewardRender:SetIsGuDingReward(bool)
    self.is_guding_reward = bool
end

function BossZhanLingRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local level = self.data.seq
    local nor_can_get, high_can_get = BossZhanLingWGData.Instance:IsCanGetZhanLingRewardBySeq(level)
    self.node_list.title.text.text = level + 1
    if self.node_list["btn_high_get"] then
        self.node_list["btn_high_get"]:SetActive(nor_can_get or high_can_get)
    end

    local cur_zhanling_level = BossZhanLingWGData.Instance:GetZhanLingLevel()
    self.node_list["progress_up"]:SetActive(cur_zhanling_level >= level)
    self.node_list["progress_down"]:SetActive(cur_zhanling_level > level)

    if self.node_list.normal_item_red_point then
        self.node_list.normal_item_red_point:SetActive(nor_can_get)
    end

    if self.node_list.high_red_point1 then
        self.node_list.high_red_point1:SetActive(high_can_get)
    end

    if self.node_list.high_red_point2 then
        self.node_list.high_red_point2:SetActive(high_can_get)
    end

    local nor_is_get, higer_is_get = BossZhanLingWGData.Instance:IsGetZhanLingRewardBySeq(self.data.seq)
    self.normal_item:SetData({reward_data = self.data.free_reward[0], is_get = nor_is_get})
    for k, v in ipairs(self.high_item_list) do
        v:SetData({reward_data = self.data.added_reward[k - 1], is_get = higer_is_get})
    end

    -- if self.node_list.normal_lock then
    --     self.node_list.normal_lock:SetActive(not (nor_is_get or nor_can_get))
    -- end

    -- if self.node_list.high_lock then
    --     local is_open_higer_zhanling = BossZhanLingWGData.Instance:GetHigerOrderRewardFlag()
    --     self.node_list.high_lock:SetActive(not is_open_higer_zhanling or (not (higer_is_get or high_can_get)))
    -- end

    if self.node_list.bg then
        self.node_list.bg:SetActive(self.index % 2 == 0)
    end
end

function BossZhanLingRewardRender:OnClickGetReward()
    if IsEmptyTable(self.data) then
        return
    end

    BossZhanLingWGCtrl.Instance:SendBossZhanLingReq(BOSS_ZHANLING_OPERATE_TYPE.HUNT_MONSTER_ORDER_OPERA_TYPE_GET_REWARD, self.data.seq)
end

---------------------------------BossZhanLingRewardItem--------------------------------------
BossZhanLingRewardItem = BossZhanLingRewardItem or BaseClass(BaseRender)

function BossZhanLingRewardItem:LoadCallBack()
    self.reward_item = ItemCell.New(self.node_list.item_node)
end

function BossZhanLingRewardItem:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end

    self.is_high_item = nil
end

function BossZhanLingRewardItem:SetIsHighItem(bool)
    self.is_high_item = bool
end

function BossZhanLingRewardItem:OnFlush()
    if IsEmptyTable(self.data.reward_data) then
        self.view:SetActive(false)
    else
        self.view:SetActive(true)

        local is_open_higer_zhanling = BossZhanLingWGData.Instance:GetHigerOrderRewardFlag()
        self.reward_item:SetData(self.data.reward_data)
        -- self.node_list.item_lock:SetActive(self.is_high_item and not is_open_higer_zhanling)
        self.node_list.item_isget:SetActive(self.data.is_get)
    end
end