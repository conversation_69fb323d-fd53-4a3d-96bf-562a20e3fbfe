BossPrivilegeWGData = BossPrivilegeWGData or BaseClass()
function BossPrivilegeWGData:__init()
    if BossPrivilegeWGData.Instance ~= nil then
		print_error("[BossPrivilegeWGData] attempt to create singleton twice!")
		return
	end
	BossPrivilegeWGData.Instance = self

	local privilege_cfg = ConfigManager.Instance:GetAutoConfig("boss_drop_privilege_cfg_auto")
	self.privilege_info = privilege_cfg.privilege
	self.privilege_other = privilege_cfg.other
	self.scene_type_info = ListToMap(privilege_cfg.scenetype, "seq")
	self.privilege_type_list = {}
	for k, v in pairs(self.privilege_info) do
		table.insert(self.privilege_type_list, string.split(v.type, "|"))
	end
	self.boss_privilege_info = {}
end

function BossPrivilegeWGData:__delete()
	BossPrivilegeWGData.Instance = nil
end

function BossPrivilegeWGData:SetPrivilegeInfo(protocol)
	self.boss_privilege_info.level_flag = protocol.level_flag --特权升级信息
	self.boss_privilege_info.is_open = protocol.is_open		  --特权是否开启
	self.boss_privilege_info.privilege_times_list = protocol.privilege_times_list --特权副本剩余次数
	local is_active = false
	for k,v in pairs(self.boss_privilege_info.level_flag) do
		if v > 0 then
			is_active = true
			break
		end
	end

	self.boss_privilege_info.is_active = is_active			  --特权是否激活
end

--获取特权开启状态
function BossPrivilegeWGData:GetActivateState()
	return self.boss_privilege_info.is_open or 0
end

--特权升级信息
function BossPrivilegeWGData:GetLevelInfo()
	return self.boss_privilege_info.level_flag or {}
end

--boss再爆一次 次数
function BossPrivilegeWGData:GetBoosTimesInfo()
	return self.boss_privilege_info.privilege_times_list or {}
end

--boos特权激活状态
function BossPrivilegeWGData:GetBossPrivilegeIsActive()
	return self.boss_privilege_info.is_active
end

--当前特权等级
function BossPrivilegeWGData:GetCurPrivilegeLevel()
	local level_list = self:GetLevelInfo()
	local level = 1
	if not IsEmptyTable(level_list) then
		for i = 1, #self.privilege_info do
			if level_list[i] > 0 then
				level = i
			end
		end
	end

	local max_level = (self.privilege_info[#self.privilege_info] or {}).level or 5
	return level, max_level
end

--副本次数配置
function BossPrivilegeWGData:GetPrivilegeTimesCfg(cur_level)
	local type_list = {}
	local data_list = {}
	--local key_list = string.split(self.privilege_info[cur_level].type, "|")
	local key_list = self.privilege_type_list[cur_level]
	local cfg_list = self.privilege_info[cur_level]
	if key_list == nil or cfg_list == nil then
		return
	end

	for k, v in pairs(key_list) do
		local value = string.split(v, ":")
		local data = {
			boss_type = value[1],
			times = value[2]
		}
		table.insert(type_list, data)
	end
	
	local data = {
		level = cfg_list.level,
		condition_type = cfg_list.condition_type,
		condition_value = cfg_list.condition_value,
		rmb_type = cfg_list.rmb_type,
		rmb_seq = cfg_list.rmb_seq,
		type = type_list,
	}	
	table.insert(data_list, data)

	return data_list[1]
end


--副本剩余次数
function BossPrivilegeWGData:GetPrivilegeBossTimes()
	local data_list = {}
	local cur_level = self:GetCurPrivilegeLevel()

	local key_list = self.privilege_type_list[cur_level]
	if key_list == nil then
		return data_list
	end

	for k, v in pairs(key_list) do
		local value_list = string.split(v, ":")
		local type = tonumber(value_list[1])
		local total_time = tonumber(value_list[2])

		local use_times = self:GetBoosTimesByType(type)
		local data = {
			boss_type = type,
			times = total_time - use_times
		}

		table.insert(data_list, data)
	end

	return data_list
end

--是否还有次数
function BossPrivilegeWGData:GetIsPrivilegeTimes()
	local cur_times_list = self:GetPrivilegeBossTimes()
	if cur_times_list == nil then
		return 0
	end

	for k, v in pairs(cur_times_list) do
		if v.times > 0 then
			return 1
		end
	end

	return 0
end

--特权属性
function BossPrivilegeWGData:GetCurPrivilegeAttrList(level)
	local attr_list = {}

	local cfg = self.privilege_info[level]
	if not cfg then
        return attr_list
    end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
        attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
                attr_str = attr_str,
                attr_value = attr_value,
                add_value = 0,
                attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str),
            }

            table.insert(attr_list, data)
        end
    end

	if not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return attr_list
end

--特权开启等级
function BossPrivilegeWGData:PrivilegeOpenLevel()
	return self.privilege_other[1].open_level
end

--当前副本剩余次数
function BossPrivilegeWGData:GetBossFbTimesCfg(type)
	local times_list = self:GetPrivilegeBossTimes()
	local seq = 0
	local is_times = 0
	for k, v in pairs(self.scene_type_info) do
		if v.type == type then
			seq = v.seq
		end
	end

	if not IsEmptyTable(times_list) then
		for k, v in pairs(times_list) do
			if tonumber(v.boss_type) == seq then
				is_times = v.times
			end
		end
	end

	return is_times
end

--当前有再爆一次特权的副本功能名字
function BossPrivilegeWGData:GetBossFuncOpenNameBySeq(seq)
	return self.scene_type_info[seq].func_open_name
end


---------------------------------------------------------------------------
function BossPrivilegeWGData:GetSceneTypeInfoCfg()
	return self.scene_type_info
end

function BossPrivilegeWGData:GetSceneTypeInfoCfgByType(type)
	return self.scene_type_info[type]
end

function BossPrivilegeWGData:GetBoosTimesByType(type)
	return ((self.boss_privilege_info or {}).privilege_times_list or {})[type] or 0
end

function BossPrivilegeWGData:GetPrivilegeTimesCfgByLevel(level)
	return self.privilege_info[level]
end

function BossPrivilegeWGData:GetPrivilegeTimesListCfgByLevel(level)
	local data_list = {}

	local info_list = self:GetPrivilegeTimesCfgByLevel(level)
	if not IsEmptyTable(info_list) then
		local key_list = self.privilege_type_list[level]
		if key_list == nil then
			return data_list
		end

		for k, v in pairs(key_list) do
			local value_list = string.split(v, ":")
			local type = tonumber(value_list[1])
			local total_time = tonumber(value_list[2])
	
			local use_times = self:GetBoosTimesByType(type)
			local data = {
				boss_type = type,
				times = total_time,
				has_times = total_time - use_times,
				cfg = self:GetSceneTypeInfoCfgByType(type),
			}
	
			table.insert(data_list, data)
		end
	end

	return data_list
end