WuHunFrontPropertyTip = WuHunFrontPropertyTip or BaseClass(SafeBaseView)

function WuHunFrontPropertyTip:__init()
	self.is_async_load = false
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self.view_name = "WuHunFrontPropertyTip"
	self:AddViewResource(0, "uis/view/wuhunzhenshen_prefab", "wuhun_front_property_tip")

	self.data = nil
	self.view_data = nil
end

function WuHunFrontPropertyTip:ReleaseCallBack()
	if self.cur_attr_list then
		for k, v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
	end
	self.cur_attr_list = nil

	if self.next_attr_list then
		for k, v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
	end
	self.next_attr_list = nil

	self.view_data = nil
	self.data = nil
end

function WuHunFrontPropertyTip:LoadCallBack()
	self.cur_attr_list = {}
	self.next_attr_list = {}
	for i = 1, 5 do
		self.cur_attr_list[i] = WuHunFrontPropAddAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.next_attr_list[i] = WuHunFrontPropAddAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
	end

	XUI.AddClickEventListener(self.node_list.btn_upgrade, BindTool.Bind(self.OnBtnActiveUpgradeClick, self))
end

function WuHunFrontPropertyTip:OpenCallBack()
end

function WuHunFrontPropertyTip:SetData(data)
    self.data = data
end

function WuHunFrontPropertyTip:OnFlush()
	if not self.data then
		return
	end

    self:SetShowInfo()
end

function WuHunFrontPropertyTip:SetShowInfo()
	self.view_data = self:GetPropShowData()
	self.node_list.lbl_attr_title.text.text = self.view_data.title
	self.node_list.now_level.text.text = self.view_data.now_prop_title
	self.node_list.max_attr_tip:CustomSetActive(self.view_data.now_level == self.view_data.max_level)
	self.node_list.next_level.text.text = self.view_data.next_prop_title
	self.node_list.next_level_bg:CustomSetActive(self.view_data.now_level < self.view_data.max_level)
	self.node_list.no_attr_tip:CustomSetActive(self.view_data.now_level == COMMON_GAME_ENUM.FUYI)
	self.node_list.cur_level_bg:CustomSetActive(self.view_data.now_level > COMMON_GAME_ENUM.FUYI)
	self.node_list.yimanji:CustomSetActive(self.view_data.now_level == self.view_data.max_level)
	self.node_list.btn_upgrade_red:CustomSetActive(self.view_data.show_red)
	self.node_list.btn_upgrade:CustomSetActive(self.view_data.now_level < self.view_data.max_level)

	for i = 1, 5 do
		self.cur_attr_list[i]:SetData(self.view_data.cur_attr_data[i])
		self.next_attr_list[i]:SetData(self.view_data.next_attr_data[i])
	end
end

function WuHunFrontPropertyTip:OnBtnActiveUpgradeClick()
	if not self.view_data.show_red then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontPropTxt7)
		return
	end

	local op_type = WUHUN_FRONT_OPERATE_TYPE.SOUL_STONE_LEVEL_ATTR
	if self.data.prop_showtype == COMMON_GAME_ENUM.TWO then --刻印加成
		op_type = WUHUN_FRONT_OPERATE_TYPE.ENGRAVE_LEVEL_ATTR
	elseif self.data.prop_showtype == COMMON_GAME_ENUM.THREE then  --品阶加成
		op_type = WUHUN_FRONT_OPERATE_TYPE.SOUL_STONE_GRADE_ATTR
	end

	WuHunWGCtrl.Instance:SendWuHunFrontOperate(
        op_type,
        self.data.wuhun_id,
        self.data.front_index,
        0,
        0,
        {}
    )
end

function WuHunFrontPropertyTip:GetPropShowData()
    local function add_func(cfg)
		local attr_data = {}
		if not cfg then
			return attr_data
		end

		for i = 1, 5 do
			local attr_key = cfg["attr_id" .. i]
			local attr_value = cfg["attr_value" .. i]
			if attr_key and attr_value > 0 then
				attr_data[i] = {}
				attr_data[i].name = EquipmentWGData.Instance:GetAttrName(attr_key)
				attr_data[i].value = attr_value
			end
		end

		return attr_data
	end

	local wuhun_id = self.data.wuhun_id
	local front_index = self.data.front_index
	local front_data = WuHunFrontWGData.Instance:GetFrontDataByFrontIndex(wuhun_id, front_index)
	local param = {}
	if self.data.prop_showtype == COMMON_GAME_ENUM.ONE then --强化加成
		local gem_level_index = front_data.gem_level_index or 0
		param.title = Language.WuHunZhenShen.WuhunFrontTitle1
		param.now_level = gem_level_index
		local all_cfgs = WuHunFrontWGData.Instance:GetFrontGemLevelAttr(wuhun_id, front_index)
		param.max_level = all_cfgs and GetTableLen(all_cfgs) - 1 or 0
		local now_cfg = WuHunFrontWGData.Instance:GetFrontGemLevelAttr(wuhun_id, front_index, gem_level_index)
		param.cur_attr_data = add_func(now_cfg)
		local next_cfg = WuHunFrontWGData.Instance:GetFrontGemLevelAttr(wuhun_id, front_index, gem_level_index + 1)
		param.next_attr_data = next_cfg and add_func(next_cfg) or {}
		local level_sum = WuHunFrontWGData.Instance:GetFrontGemLevelSum(wuhun_id, front_index)
		param.show_red = WuHunFrontWGData.Instance:GetFrontGemLevelUpAddPropRed(wuhun_id, front_index, self.data.gem_index)

		param.now_prop_title = string.format(Language.WuHunZhenShen.WuhunFrontPropTxt1, level_sum)
		param.next_prop_title = ""
		if next_cfg then
			local next_level = next_cfg.level_total
			if level_sum >= next_level then
				param.next_prop_title = string.format(Language.WuHunZhenShen.WuhunFrontPropTxt1, next_level)
			else
				param.next_prop_title = string.format(Language.WuHunZhenShen.WuhunFrontPropTxt4, next_level, level_sum, next_level)
			end
		end

	elseif self.data.prop_showtype == COMMON_GAME_ENUM.TWO then --刻印等级
		local engrave_level_index = front_data.engrave_level_index
		param.title = Language.WuHunZhenShen.WuhunFrontTitle2
		param.now_level = engrave_level_index
		local all_cfgs = WuHunFrontWGData.Instance:GetEngraveResonanceCfg(wuhun_id, front_index)
		param.max_level = GetTableLen(all_cfgs) - 1
		local now_cfg = WuHunFrontWGData.Instance:GetEngraveResonanceCfg(wuhun_id, front_index, engrave_level_index) or {}
		param.cur_attr_data = add_func(now_cfg)
		local next_cfg = WuHunFrontWGData.Instance:GetEngraveResonanceCfg(wuhun_id, front_index, engrave_level_index + 1)
		param.next_attr_data = next_cfg and add_func(next_cfg) or {}
		param.show_red = WuHunFrontWGData.Instance:GetFrontGemGongMingRed(wuhun_id, front_index)

		local level_sum = WuHunFrontWGData.Instance:GetFrontEngraveSumLevel(wuhun_id, front_index)
		param.now_prop_title = string.format(Language.WuHunZhenShen.WuhunFrontPropTxt2, now_cfg.level_total)
		param.next_prop_title = ""
		if next_cfg then
			local next_level = next_cfg.level_total
			local color_str = level_sum >= next_level and "9df5a7" or "ff9797"
			param.next_prop_title = string.format(Language.WuHunZhenShen.WuhunFrontPropTxt5, next_level, color_str, level_sum, next_level)
		end

	elseif self.data.prop_showtype == COMMON_GAME_ENUM.THREE then --品阶加成
		local gem_grade_index = front_data.gem_grade_index
		param.title = Language.WuHunZhenShen.WuhunFrontTitle3
		param.now_level = gem_grade_index
		local all_cfgs = WuHunFrontWGData.Instance:GetGemGradeAttrCfg(wuhun_id, front_index)
		param.max_level = GetTableLen(all_cfgs) - 1
		local now_cfg = WuHunFrontWGData.Instance:GetGemGradeAttrCfg(wuhun_id, front_index, gem_grade_index)
		param.cur_attr_data = add_func(now_cfg)
		local next_cfg = WuHunFrontWGData.Instance:GetGemGradeAttrCfg(wuhun_id, front_index, gem_grade_index + 1)
		param.next_attr_data = next_cfg and add_func(next_cfg) or {}
		param.show_red = WuHunFrontWGData.Instance:GetFrontGemGradeAddPropRed(wuhun_id, front_index, self.data.gem_index)
		local now_add_cfg = WuHunFrontWGData.Instance:GetGemGradeAttrAddCfg(now_cfg and now_cfg.min_grade)
		param.now_prop_title = string.format(Language.WuHunZhenShen.WuhunFrontPropTxt3, now_add_cfg and now_add_cfg.name or "")

		param.next_prop_title = ""
		if next_cfg then
			local next_level = next_cfg.min_grade
			local count = 0
			for k_i, k_v in pairs(front_data.gem_list) do
				if k_v.gem_grade >= next_level then
					count = count + 1
				end
			end

			local gem_num = WuHunFrontWGData.Instance:GetMaxFrontByWuhunIdFront(wuhun_id, front_index)
			local add_cfg = WuHunFrontWGData.Instance:GetGemGradeAttrAddCfg(next_cfg.min_grade)
			param.next_prop_title = string.format(Language.WuHunZhenShen.WuhunFrontPropTxt6, add_cfg.name, count, gem_num)
		end

	end

	return param
end



WuHunFrontPropAddAttrRender = WuHunFrontPropAddAttrRender or BaseClass(BaseRender)

function WuHunFrontPropAddAttrRender:OnFlush()
	if not IsEmptyTable(self.data) then
		self.node_list.name.text.text = self.data.name
		self.node_list.value.text.text = self.data.value
		self.view:SetActive(true)
	else
		self.node_list.name.text.text = ""
		self.node_list.value.text.text = ""
		self.view:SetActive(false)
	end
end