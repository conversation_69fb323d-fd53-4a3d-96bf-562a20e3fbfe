BulitInGMView = BulitInGMView or BaseClass(SafeBaseView)
function BulitInGMView:__init()
    self:SetMaskBg()
    self.view_cache_time = nil
    self.default_index = 10

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
                        {vector2 = Vector2(0, 0), sizeDelta = Vector2(1098, 600)})
	self:AddViewResource(10, "uis/view/bulit_in_gm_prefab", "layout_bulit_in_gm")
    self:AddViewResource(20, "uis/view/bulit_in_gm_prefab", "layout_bulit_in_print")
	self:AddViewResource(0, "uis/view/bulit_in_gm_prefab", "VerticalTabbar")
end

function BulitInGMView:__delete()
end

function BulitInGMView:OpenCallBack()
end

function BulitInGMView:CloseCallBack()
end

function BulitInGMView:LoadCallBack()
    self.node_list.title_view_name.text.text = "GMの妙妙屋"
    if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
        local ver_path = "uis/view/bulit_in_gm_prefab"
		self.tabbar:Init({"GM", "日志"}, nil, ver_path)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
	end
end

function BulitInGMView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    self:ReleaseBulitInGMView()
    self:ReleaseBulitInPrintView()
end

function BulitInGMView:LoadIndexCallBack(index)
    if index == 10 then
        self:InitBulitInGMView()
    elseif index == 20 then
        self:InitBulitInPrintView()
    end
end

function BulitInGMView:OnFlush(param_t, index)
    if index == 10 then
        self:FlushBulitInGMView()
    elseif index == 20 then
        self:FlushBulitInPrintView()
    end
end