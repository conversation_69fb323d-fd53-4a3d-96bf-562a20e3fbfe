FuBenRuneTowerLevelShowView = FuBenRuneTowerLevelShowView or BaseClass(SafeBaseView)
local STATIC_TIME = 3 
function FuBenRuneTowerLevelShowView:__init()
    self:SetMaskBg(false, false)
    self.active_close = false
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fuben_rune_towerl_change_level")
end


function FuBenRuneTowerLevelShowView:SetDataAndOpen(level)
	self.cur_level = level
    self:Open()
end

function FuBenRuneTowerLevelShowView:OpenCallBack()

end

function FuBenRuneTowerLevelShowView:CloseCallBack()
end

function FuBenRuneTowerLevelShowView:LoadCallBack()
	GuajiWGCtrl.Instance:StopGuaji()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
end


function FuBenRuneTowerLevelShowView:ReleaseCallBack()
	if self.static_count_down then
		CountDown.Instance:RemoveCountDown(self.static_count_down)
		self.static_count_down = nil
	end
end

function FuBenRuneTowerLevelShowView:ShowIndexCallBack()
    self:StartCountDown()
end

function FuBenRuneTowerLevelShowView:OnFlush()
    if not self.cur_level then
        return
    end

    self.node_list.cur_level_num.text.text = string.format(Language.RuneTower.LevelShow, self.cur_level)
end

function FuBenRuneTowerLevelShowView:StartCountDown()
    if self.static_count_down then
		CountDown.Instance:RemoveCountDown(self.static_count_down)
		self.static_count_down = nil
	end

    self.static_count_down = CountDown.Instance:AddCountDown(STATIC_TIME, 1,  
    function(elapse_time, total_time)
    end,
    function()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        self:Close()
    end)
end