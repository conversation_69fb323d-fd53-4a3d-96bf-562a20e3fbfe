require("game/tianshi_godownhill/tianshi_godownhill_wg_data")
require("game/tianshi_godownhill/tianshi_godownhill_view")
require("game/tianshi_godownhill/tianshi_godownhill_reward_show")

--/jy_gm likenumreset:  清空每日点赞次数
TianShiGodownHillWGCtrl = TianShiGodownHillWGCtrl or BaseClass(BaseWGCtrl)
function TianShiGodownHillWGCtrl:__init()
	if TianShiGodownHillWGCtrl.Instance then
		ErrorLog("[TianShiGodownHillWGCtrl] attempt to create singleton twice!")
		return
	end

	TianShiGodownHillWGCtrl.Instance = self
	self.data = TianShiGodownHillWGData.New()
    self.view = TianShiGodownHillView.New(GuideModuleName.TianShiGodownHillView)
	self.gift_view = TianShiGodownHillRewardShowView.New()
  
    self:RegisterAllProtocols()
end

function TianShiGodownHillWGCtrl:__delete()
	TianShiGodownHillWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil

	self.gift_view:DeleteMe()
	self.gift_view = nil
end

function TianShiGodownHillWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCLikeInfo,"OnSCLikeInfo")
end

function TianShiGodownHillWGCtrl:OnSCLikeInfo(protocol)
	--print_error("===========数据============", protocol)
	self.data:SetAllInfo(protocol)

    if self.view:IsOpen() then
        self.view:Flush()
    end

    RemindManager.Instance:Fire(RemindName.TianShiGodownHill)
end

function TianShiGodownHillWGCtrl:ReqTianShiGodownHillInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIKE
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function TianShiGodownHillWGCtrl:ShowGiftView(data_list)
	self.gift_view:Flush(0, "gift_info", {data_list = data_list})
	self.gift_view:Open()
end

