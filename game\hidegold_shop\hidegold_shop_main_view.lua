HideGoldMainView = HideGoldMainView or BaseClass(SafeBaseView)
function HideGoldMainView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/hidegold_shop_ui_prefab", "layout_hidegold_show_view")
end

function HideGoldMainView:LoadCallBack()
    self:FlushTimeCount()

    XUI.AddClickEventListener(self.node_list["btn_open_shop"], BindTool.Bind(self.OnClickOpenShop, self))
    XUI.AddClickEventListener(self.node_list["btn_all_shop"], BindTool.Bind(self.OnClickAllShop, self))
end

function HideGoldMainView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("hidegold_shop_time") then
        CountDownManager.Instance:RemoveCountDown("hidegold_shop_time")
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end
end

function HideGoldMainView:OpenCallBack()
    HideGoldShopWGCtrl.Instance:ReqActivityHideGoldShopInfo(OA_HIDE_GOLD_SHOP_OPERATE_TYPE.INFO)
end

function HideGoldMainView:OnClickOpenShop()
    HideGoldShopWGCtrl.Instance:OpenShopBuyView()
end

function HideGoldMainView:OnClickAllShop()
    HideGoldShopWGCtrl.Instance:OpenShopItemShowView()
end

function HideGoldMainView:OnFlush(param_t)
    local flush_cfg = HideGoldShopWGData.Instance:GetRefreshCfg()
    if flush_cfg then
        self.node_list["show_model_name"].text.text = flush_cfg.special_show_name
        if flush_cfg.special_show_name == "" then
            self.node_list.model_name_bg:SetActive(false)
        end
    end

    local cur_score = HideGoldShopWGData.Instance:GetCurScore()
    self.node_list["cur_score"].text.text = string.format(Language.HideGoldShop.CurScore, cur_score)

    local score_desc = HideGoldShopWGData.Instance:GetShowGoldDesc()
    self.node_list["desc_1"].text.text = score_desc

    self:LoadModel()
end

function HideGoldMainView:LoadModel()
    if self.model_display == nil then
		self.model_display = OperationActRender.New(self.node_list.model_root)
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)

        local show_model_id_list = HideGoldShopWGData.Instance:GetShowModelId()
        if show_model_id_list then
            local data = {}
            if show_model_id_list ~= 0 and show_model_id_list ~= "" then
                local split_list = string.split(show_model_id_list, "|")
                if #split_list > 1 then
                    local list = {}
                    for k, v in pairs(split_list) do
                        list[tonumber(v)] = true
                    end
                    data.model_item_id_list = list
                else
                    data.item_id = show_model_id_list
                end
            end
            data.should_ani = true
            data.item_id = show_model_id_list
            data.render_type = 0
            self.model_display:SetData(data)
        end
    end
    
    local model_flush_cfg = HideGoldShopWGData.Instance:GetRefreshCfg()
    if model_flush_cfg then
        local pos_x, pos_y = 100, 0
        if  model_flush_cfg.display_pos and model_flush_cfg.display_pos ~= "" then
            local pos_list = string.split(model_flush_cfg.display_pos, "|")
            pos_x = tonumber(pos_list[1]) or pos_x
            pos_y = tonumber(pos_list[2]) or pos_y
        end
    
        local rot_x, rot_y, rot_z = 0, 0, 0
        if  model_flush_cfg.display_rotation and model_flush_cfg.display_rotation ~= "" then
            local rot_list = string.split(model_flush_cfg.display_rotation, "|")
            rot_x = tonumber(rot_list[1]) or rot_x
            rot_y = tonumber(rot_list[2]) or rot_y
            rot_z = tonumber(rot_list[3]) or rot_z
        end
    
        RectTransform.SetAnchoredPositionXY(self.node_list.model_root.rect, pos_x, pos_y)
        self.node_list.model_root.rect.rotation = Quaternion.Euler(rot_x, rot_y, rot_z)
    
        local scale = model_flush_cfg.display_scale
        scale = (scale and scale ~= "" and scale > 0) and scale or 1
        Transform.SetLocalScaleXYZ(self.node_list.model_root.transform, scale, scale, scale)
    end
end

function HideGoldMainView:FlushTimeCount()
    if CountDownManager.Instance:HasCountDown("hidegold_shop_time") then
        CountDownManager.Instance:RemoveCountDown("hidegold_shop_time")
    end

    local time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HIDE_GOLD_SHOP)
    if time > 0 then
        CountDownManager.Instance:AddCountDown("hidegold_shop_time",
            BindTool.Bind(self.FinalUpdateTimeCallBack, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function HideGoldMainView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    if self.node_list.time_down then
        self.node_list.time_down.text.text = time_str
    end
end

function HideGoldMainView:OnComplete()
    self.node_list.act_time.text.text = ""
end