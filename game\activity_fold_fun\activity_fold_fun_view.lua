ActicityFoldFunView = ActicityFoldFunView or BaseClass(SafeBaseView)
local HEIGHT_VALUE = 120
function ActicityFoldFunView:__init()
    self.view_layer = UiLayer.PopTop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:SetMaskBg(true,true)
    self:AddViewResource(0, "uis/view/main_ui_prefab", "layout_act_fold_view")
end

function ActicityFoldFunView:__delete()
end

function ActicityFoldFunView:ReleaseCallBack()
    if self.hall_act_list then
        self.hall_act_list:DeleteMe()
        self.hall_act_list = nil
    end

    if nil ~= self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end

    self.select_act_type = nil
end

function ActicityFoldFunView:OpenCallBack()

end

function ActicityFoldFunView:CloseCallBack()

end

function ActicityFoldFunView:LoadCallBack()
    if self.hall_act_list == nil then
        self.hall_act_list = AsyncBaseGrid.New()
        self.hall_act_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list.hall_list,
                assetBundle = "uis/view/main_ui_prefab", assetName = "fold_act_cell", itemRender = ActicityFoldRender})
        self.hall_act_list:SetStartZeroIndex(false)
    end

    self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))
end

function ActicityFoldFunView:ShowIndexCallBack()

end

function ActicityFoldFunView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
		if "all" == k then
            if v.sel_index then
                self.select_act_type = tonumber(v.sel_index)
            end

            self:FlushViewPos()
            self:FlushPanelInfo()
        end
    end
end

function ActicityFoldFunView:ShrinkButtonsValueChange(isOn)
    self:Close()
end

function ActicityFoldFunView:FlushViewPos()
    if self:IsLoaded() then
        local root_obj = self.node_list["tween_root"]
        local act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(self.select_act_type)
        if act_btn then
            local act_btn_pos = act_btn:GetIconPos()
            local target_pos = TipWGCtrl.Instance:TurnBaseCellPos(act_btn_pos, self:GetRootNode())
            local end_pos = Vector2(target_pos.x, target_pos.y - 80)
            root_obj.transform.anchoredPosition = end_pos
        end
    end
end

function ActicityFoldFunView:FlushPanelInfo()
    if not self.select_act_type then
        return
    end

    local recode_list = ActicityFoldFunWGData.Instance:GetFoldParentActRecord(self.select_act_type)
    local data_list = {}
    for k, v in pairs(recode_list) do
        local data = {}
        data.act_type = v.act_type
        local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(data.act_type) 
        data.sort = act_cfg and act_cfg.sort or 0
        table.insert(data_list, data)
    end
    
    local count = #data_list
    local y_value = math.ceil((count / 3)) * HEIGHT_VALUE
    RectTransform.SetSizeDeltaXY(self.node_list.hall_list.rect, 320, y_value)
    if IsEmptyTable(data_list) then
        self:Close()
        return
    end

    table.sort(data_list, SortTools.KeyLowerSorters("sort", "act_type"))
    self.hall_act_list:SetDataList(data_list)
end

ActicityFoldRender = ActicityFoldRender or BaseClass(BaseRender)

function ActicityFoldRender:LoadCallBack()

end

function ActicityFoldRender:__delete()
    if self.fold_act_cell then
        self.fold_act_cell:DeleteMe()
        self.fold_act_cell = nil
    end
end

function ActicityFoldRender:OnFlush()
    if self.data == nil then
        return
    end

    local act_type = self.data.act_type
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
    local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
    if act_info == nil or act_cfg == nil then
        return
    end

    if self.fold_act_cell == nil then
        self.fold_act_cell = MainActivityBtn.New()
        self.fold_act_cell:CreateBtnAsset(self.node_list.pos, act_type)
        self.fold_act_cell:AddClickEventListener(BindTool.Bind(self.OnClickActIcon, self, self.fold_act_cell))
    end

    self.fold_act_cell:SetData(act_type)
    self.fold_act_cell:Flush("SetSprite", {act_cfg.res_name, act_cfg.act_name_res, act_cfg.bg_res})
    self.fold_act_cell:SetActCfg(act_cfg)
    self.fold_act_cell:Flush("CheckSpecialEffectBtn", {act_type})
    self.fold_act_cell:ShowActiveButton()

    local end_time = act_info.next_time
    local status =  act_info.status
    if status == ACTIVITY_STATUS.STANDY then
        local time_tab = os.date("*t", end_time)
        if time_tab.min % 10 == 9 then
            time_tab.min = time_tab.min + 1
            if time_tab.min >= 60 then
                time_tab.min = 0
                time_tab.hour = time_tab.hour + 1
            end
        elseif time_tab.min % 10 == 1 then
            time_tab.min = time_tab.min - 1
        end

        local open_time_str = string.format("%d:%02d%s", time_tab.hour, time_tab.min, Language.Activity.KaiQi)
        self.fold_act_cell:Flush("SetTime", {open_time_str})
        self.fold_act_cell:Flush("SetActivityStateFlag", {act_type, status})
    elseif status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.XUNYOU then
        if end_time and act_cfg.show_time == 1 then --显示倒计时
            self.fold_act_cell:Flush("SetEndTime", {end_time})
            self.fold_act_cell:Flush("SetActivityStateFlag", {act_type, status})
        else
            self.fold_act_cell:Flush("SetBottomContent", {""})
            self.fold_act_cell:Flush("SetActivityStateFlag", {act_type, ACTIVITY_STATUS.CLOSE})
            self.fold_act_cell:RemoveCountDown()
        end
    end

    if status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.XUNYOU then
        self.fold_act_cell:Flush("ShowAni", {act_cfg.is_show_ani == 1})
        self.fold_act_cell:Flush("SetFoldEffect",{true})
    else
        self.fold_act_cell:Flush("SetFoldEffect",{false})
    end

    if ActRemindList[act_type] then
        local remind_num = RemindManager.Instance:GetRemind(ActRemindList[act_type])
        self.fold_act_cell:Flush("SetRedPoint", {remind_num > 0})
    end
end

function ActicityFoldRender:OnClickActIcon()
    if self.data == nil then
        return
    end

    ViewManager.Instance:Close(GuideModuleName.ActicityFoldFunView)
    MainuiWGCtrl.Instance:ClickEnterActivity(self.data.act_type)
end
