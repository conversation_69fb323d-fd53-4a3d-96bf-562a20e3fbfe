TipsGetBestRewardView = TipsGetBestRewardView or BaseClass(SafeBaseView)

local ani_ten_flag_t = {}
local ANI_SPEED = 0.15
local LeftPadding = 50
local DelayIndex = 0
local OneCellWidth = 142
local DelaySpeed = 0.13
local TweenSpeed = 0.4

function TipsGetBestRewardView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_gift_best_result")
	self.close_tween = nil
end

function TipsGetBestRewardView:__delete()

end

function TipsGetBestRewardView:ReleaseCallBack()
	if nil ~= self.rect_list then
		self.rect_list:DeleteMe()
		self.rect_list = nil
	end

	if self.ten_time_quest then
		GlobalTimerQuest:CancelQuest(self.ten_time_quest)
		self.ten_time_quest = nil
	end

	if self.single_list then
		for i,v in ipairs(self.single_list) do
			v:DeleteMe()
		end
		self.single_list = nil
	end

end

function TipsGetBestRewardView:LoadCallBack()
	self:InitXunBaoZhanshi()
end

function TipsGetBestRewardView:ShowIndexCallBack()
	self:Flush()
end

--初始化格子
function TipsGetBestRewardView:InitXunBaoZhanshi()
	self.rect_list = AsyncListView.New(GiftBestRewardCell, self.node_list.ph_zhanshii_cell)

	if not self.single_list then
		self.single_list = {}
		for i=1,8 do
			self.single_list[i] = GiftBestRewardSingleCell.New(self.node_list['single_cell_' .. i]) 
		end
	end

end

function TipsGetBestRewardView:SetData(item_id, id_list)
	self.item_id = item_id
	self.id_list = id_list
end

local Sort_Type = {
	[GameEnum.ITEM_BIGTYPE_EQUIPMENT] = 10,
	[GameEnum.ITEM_BIGTYPE_EXPENSE] = 9,
	[GameEnum.ITEM_BIGTYPE_GIF] = 8,
	[GameEnum.ITEM_BIGTYPE_OTHER] = 7,
	[GameEnum.ITEM_BIGTYPE_VIRTUAL] = 6,
}
function TipsGetBestRewardView:SortItem(item_list)
	local item_cfg,item_type
	for i,v in ipairs(item_list) do
		item_cfg,item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		v.color = item_cfg.color
		v.item_type = Sort_Type[item_type]
	end
	SortTools.SortDesc(item_list,"color","item_type")  
	return item_list
end

--刷新数据
function TipsGetBestRewardView:OnFlush()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.item_id)
	local gift_info
	if self.id_list and #self.id_list > 0 then
		gift_info = self.id_list
	else 							--固定礼包
		gift_info = ItemWGData.Instance:GetItemListInGift(self.item_id)
		if gift_info == nil then
			return
		end
		--print_error("固定礼包", gift_info)
	end
	gift_info = self:SortItem(gift_info)

	if self.ten_time_quest then
		GlobalTimerQuest:CancelQuest(self.ten_time_quest)
		self.ten_time_quest = nil
	end

	if self.delay_time_1 then
		GlobalTimerQuest:CancelQuest(self.delay_time_1)
		self.delay_time_1 = nil
	end

	if self.delay_time_2 then
		GlobalTimerQuest:CancelQuest(self.delay_time_2)
		self.delay_time_2 = nil
	end

	self.node_list.list_mask:SetActive(false) 
	self.node_list.ph_zhanshii_cell:SetActive(#gift_info > 8) 
	self.node_list.single_list:SetActive(#gift_info <= 8) 

	if #gift_info > 8 then
		DelayIndex = (self.node_list.list_mask.rect.rect.width - LeftPadding) / OneCellWidth + 1
		ani_ten_flag_t = {}
		self.rect_list:SetDataList(gift_info,0)
		self.node_list.list_mask:SetActive(true) 

		self.delay_time_1 = GlobalTimerQuest:AddDelayTimer(function ()
			local index = 0
			local fun = function ()
				index = index + 1
				if not self.node_list or not self.node_list.ph_zhanshii_cell then return end
				self.node_list.ph_zhanshii_cell.scroll_rect.horizontalNormalizedPosition = index / math.floor(#gift_info / TweenSpeed)
				if self.node_list.ph_zhanshii_cell.scroll_rect.horizontalNormalizedPosition >= 1 then
					self.node_list.list_mask:SetActive(false) 
					if self.delay_time_2 then
						GlobalTimerQuest:CancelQuest(self.delay_time_2)
						self.delay_time_2 = nil
					end
				end
			end
			self.delay_time_2 = GlobalTimerQuest:AddTimesTimer(fun,0.03,math.floor(#gift_info / TweenSpeed) + 10)
		end, DelayIndex * DelaySpeed)
		return
	end

	for i,v in ipairs(self.single_list) do
		v:SetVisible(gift_info[i] ~= nil)
		v:SetAlpha(false)
		v:SetData(gift_info[i])
	end
	self.tween_index = 1
	self.ten_time_quest = GlobalTimerQuest:AddTimesTimer(function()
		if self.tween_index > #self.single_list then return end
		self.single_list[self.tween_index]:DoAnim()
		self.single_list[self.tween_index]:SetAlpha(true)

		self.tween_index = self.tween_index + 1
	end, ANI_SPEED, #gift_info)

end


----------------------------------------------------------------------------------------------
GiftBestRewardSingleCell = GiftBestRewardSingleCell or BaseClass(BaseRender)
function GiftBestRewardSingleCell:LoadCallBack()
	self.base_cell = ItemCell.New(self.node_list["cell"])
	self.base_cell:SetItemTipFrom(ItemTip.FROM_GET_REWARD)
end

function GiftBestRewardSingleCell:ReleaseCallBack()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end
end

function GiftBestRewardSingleCell:SetAlpha(value)
	self.view.canvas_group.alpha = value and 1 or 0
end

local scale1 = Vector3(1.4, 1.4, 1) --大小1
local scale2 = Vector3(1, 1, 1) --大小1
local effect_name = {

}
function GiftBestRewardSingleCell:DoAnim()
	self.view.transform.localScale = scale1

    local scale_tween_2 = self.view.rect:DOScale(scale2,0.3)
    scale_tween_2:SetEase(DG.Tweening.Ease.InQuad)

	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end
	
    self.sequence = DG.Tweening.DOTween.Sequence()
    self.sequence:Append(scale_tween_2)
    self.sequence:AppendCallback(function ()
		local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    	local eff_level = cfg.color
		ani_ten_flag_t[#ani_ten_flag_t + 1] = true
    	
		if not effect_name[eff_level] then
			self.node_list.effect_attach:SetActive(false)
    		return
    	end
        
		local bundle, asset = ResPath.GetEffectUi(effect_name[eff_level])
        if self.node_list.effect_attach and self.node_list.effect_attach.game_obj_attach then
	        self.node_list.effect_attach.game_obj_attach.BundleName = nil
       		self.node_list.effect_attach.game_obj_attach.AssetName = nil
		end

		self.node_list.effect_attach:SetActive(true)
        self.node_list.effect_attach:ChangeAsset(bundle,asset)
    end)

end

function GiftBestRewardSingleCell:FlushEffect()
	local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local eff_level = cfg.color
	
	if not effect_name[eff_level] then
		self.node_list.effect_attach:SetActive(false)
    	return
    end
        
	local bundle, asset = ResPath.GetEffectUi(effect_name[eff_level])
	if self.node_list.effect_attach and self.node_list.effect_attach.game_obj_attach then
		self.node_list.effect_attach.game_obj_attach.BundleName = nil
	self.node_list.effect_attach.game_obj_attach.AssetName = nil
	end
	self.node_list.effect_attach:SetActive(true)
	self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

local name_color = {
	"#35ab6c",
	"#7fb8f1",
	"#c26af5",
	"#cfa94d",
	"#ff5252",
	"#ff47c9",
}
function GiftBestRewardSingleCell:OnFlush()
	if not self.data then return end
	self.base_cell:SetData(self.data)

	if self.node_list.cell_name then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		self.node_list.cell_name.text.text = ToColorStr(item_cfg.name,name_color[item_cfg.color])
	end
end

---------------------- GiftBestRewardCell --------------------------------
GiftBestRewardCell = GiftBestRewardCell or BaseClass(GiftBestRewardSingleCell)
function GiftBestRewardCell:OnFlush()
	if not self.data then return end
	self.base_cell:SetData(self.data)

	if self.node_list.cell_name then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		self.node_list.cell_name.text.text = ToColorStr(item_cfg.name,name_color[item_cfg.color])
	end

	if self.delay_tween then
		GlobalTimerQuest:CancelQuest(self.delay_tween)
		self.delay_tween = nil
	end

	if not ani_ten_flag_t[self.index] then
		self:SetAlpha(false)
		if self.index <= DelayIndex then
			self.delay_tween = GlobalTimerQuest:AddDelayTimer(function ()
				self:SetAlpha(true)
				self:DoAnim()
			end,(self.index-1)*0.15)
		else
			self.delay_tween = GlobalTimerQuest:AddDelayTimer(function ()
				self:SetAlpha(true)
				self:DoAnim()
			end,0.4)
		end
	else
		self:SetAlpha(true)
		self:Flush()
	end

end



