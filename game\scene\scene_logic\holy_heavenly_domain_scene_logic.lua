HolyHeavenlyDomainSceneLogic = HolyHeavenlyDomainSceneLogic or BaseClass(CommonFbLogic)

function HolyHeavenlyDomainSceneLogic:__init()
	self.update_elaspe_time = 0
end

function HolyHeavenlyDomainSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.HolyHeavenlyDomain.HHDFBName)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		HolyHeavenlyDomainWGCtrl.Instance:OpenFBTaskView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	self:InitGoToPos()
	self.update_elaspe_time = 0
end

function HolyHeavenlyDomainSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

    HolyHeavenlyDomainWGCtrl.Instance:CloseFBTaskView()
    MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
end

function HolyHeavenlyDomainSceneLogic:Update(now_time, elapse_time)
	BaseSceneLogic.Update(self, now_time, elapse_time)

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and main_role:IsDeleted() then
		return
	end

	if now_time > self.update_elaspe_time then
		if GuajiCache.guaji_type == GuajiType.Auto then
			local obj_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
			if not IsEmptyTable(obj_list) then
				local obj_id_list = {}
	
				for k, v in pairs(obj_list)do
					table.insert(obj_id_list, v:GetObjId())
				end
	
				if not IsEmptyTable(obj_id_list) then
					self.update_elaspe_time = now_time + 2
					GlobalTimerQuest:AddTimesTimer(function ()
						Scene.ScenePickItem(obj_id_list)
					end, 1, 1)
				end
			end
		end
	end
end

function HolyHeavenlyDomainSceneLogic:InitGoToPos()
	local sence_id = Scene.Instance:GetSceneId()
	local boss_data = HolyHeavenlyDomainWGData.Instance:GetEnterFbBossDataCache()
	if IsEmptyTable(boss_data) then
		return
	end

	local pos_list = string.split(boss_data.monster_pos, ",")
	local boss_x, boss_y = pos_list[1], pos_list[2]

	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	GuajiWGCtrl.Instance:MoveToPos(sence_id, boss_x, boss_y)
end

-- 是否是挂机打怪的敌人
function HolyHeavenlyDomainSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end

	return true
end

function HolyHeavenlyDomainSceneLogic:IsMonsterEnemy(target_obj, main_role)
	local power = HolyHeavenlyDomainWGData.Instance:GetMapXianLiValue()

	if power < 0 then
		return false, Language.HolyHeavenlyDomain.OutTire
	end

	if target_obj:IsBoss() then
		local vo = target_obj:GetVo()
		local boss_id = vo.monster_id or -1

		if boss_id > 0 and vo.special_param > 0 then
			local seq = vo.special_param % 1000
			local city_seq = math.floor(vo.special_param / 1000) - 1
			local boss_cfg = HolyHeavenlyDomainWGData.Instance:GetCountryBossCfg(city_seq, seq)

			if boss_cfg and boss_cfg.monster_id == boss_id then
				if power < boss_cfg.need_power then
					return false, Language.HolyHeavenlyDomain.OutTire
				end
			end
		end
	end

	return true
end