CBA_OPERA_TYPE = 													-- 封测活动请求操作类型
	{
		INVALID 					= 0,
		REQ_INFO 					= 1,						-- 请求活动信息
		FETCH_LOING_REWARD 			= 2,						-- 领取登录奖励
		FETCH_GUILD_REWARD 			= 3,						-- 领取仙盟奖励
		FETCH_MARRY_REWARD 			= 4,						-- 领取结婚奖励
		FETCH_ONLINE_REWARD 		= 5,						-- 领取在线奖励
		FETCH_UPLEVEL_REWARD 		= 6,						-- 领取升级奖励
		FETCH_JOIN_ACTIVITY_REWARD 	= 7,						-- 领取参与活动奖励
		FETCH_EXP_FB_REWARD 		= 8,						-- 领取经验副本奖励
		FETCH_EQUIP_FB_REWARD	 	= 9,						-- 领取装备副本奖励
		FETCH_TD_FB_REWARD 			= 10,						-- 领取塔防副本奖励
		FETCH_CHALLENGE_FB_REWARD 	= 11,						-- 领取挑战副本奖励

		CBA_OPERA_TYPE_MAX = 12,
	}

ActCloseBetaData = ActCloseBetaData or BaseClass()

function ActCloseBetaData:__init()
	if ActCloseBetaData.Instance ~= nil then
		ErrorLog("[ActCloseBetaData] attempt to create singleton twice!")
		return
	end
	self.is_open_close_beta = false
	self.close_beta_info = {}
	ActCloseBetaData.Instance = self
end

function ActCloseBetaData:__delete()
	ActCloseBetaData.Instance = nil
end

function ActCloseBetaData:SetCloseBetaData(protocol)
	self.close_beta_info.has_fetch_login_reward = protocol.has_fetch_login_reward 							-- 是否已经领取当日登录奖励
	self.close_beta_info.has_fetch_guild_reward = protocol.has_fetch_guild_reward 							-- 是否已经领取仙盟奖励
	self.close_beta_info.has_fetch_marry_reward = protocol.has_fetch_marry_reward 							-- 是否已经领取了结婚奖励
	self.close_beta_info.has_fetch_online_reward = bit:d2b(protocol.has_fetch_online_reward)				-- 是否已经领取了当日的在线奖励
	self.close_beta_info.fetch_uplevel_reward_flag = bit:d2b(protocol.fetch_uplevel_reward_flag)			-- 升级奖励领取标记
	self.close_beta_info.join_activity_flag = bit:d2b(protocol.join_activity_flag) 							-- 参与活动标记
	self.close_beta_info.fetch_activity_reward_flag = bit:d2b(protocol.fetch_activity_reward_flag)			-- 领取活动奖励标记
	--挑战大礼
	self.close_beta_info.expfb_satisfy_reward_cond_flag = bit:d2b(protocol.expfb_satisfy_reward_cond_flag) 				-- 经验本满足奖励条件标记
	self.close_beta_info.expfb_fetch_reward_flag = bit:d2b(protocol.expfb_fetch_reward_flag) 							-- 经验本领取奖励标记

	self.close_beta_info.equipfb_satisfy_reward_cond_flag = bit:d2b(protocol.equipfb_satisfy_reward_cond_flag) 			-- 装备本满足奖励条件标记
	self.close_beta_info.equipfb_fetch_reward_flag = bit:d2b(protocol.equipfb_fetch_reward_flag) 						-- 装备本领取奖励标记

	self.close_beta_info.tdfb_satisfy_reward_cond_flag = bit:d2b(protocol.tdfb_satisfy_reward_cond_flag)				-- 塔防本满足奖励条件标记
	self.close_beta_info.tdfb_fetch_reward_flag = bit:d2b(protocol.tdfb_fetch_reward_flag) 								-- 塔防本领取奖励标记

	self.close_beta_info.challengefb_satisfy_reward_cond_flag = bit:d2b(protocol.challengefb_satisfy_reward_cond_flag) 	-- 挑战本满足奖励条件标记
	self.close_beta_info.challengefb_fetch_reeward_flag =bit:d2b( protocol.challengefb_fetch_reeward_flag) 				-- 挑战本领取奖励标记

	self.close_beta_info.total_login_days = protocol.total_login_days 	-- 总共登录天数
	self.close_beta_info.online_time = protocol.online_time				-- 总共在线时间
end

function ActCloseBetaData:SetIsCloseBetaOpen(is_open_close_beta)
	self.is_open_close_beta = is_open_close_beta
end

function ActCloseBetaData:GetIsCloseBetaOpen()
	return self.is_open_close_beta
end

--获取活动按钮列表的配置
function ActCloseBetaData:GetShowOpenActList(is_sort)
	local show_act_cfg_list = {}
	local temp_act_id_dic = {} --避免内容重复

	local show_act_cfg = ServerActivityWGData.Instance:GetShowActConfig()
	local list = show_act_cfg[ACTIVITY_TYPE.CLOSE_BETA]
	if list ~= nil then
		for _, id in pairs(list) do
			local is_act_finish = false
			--local is_act_finish = ServerActivityWGData.Instance:GetActIsFinish(id)
			if false == is_act_finish and temp_act_id_dic[id] == nil then
				local act_cfg = ServerActivityWGData.Instance:GetClientActCfg(id)
				if act_cfg ~= nil then
					table.insert(show_act_cfg_list, act_cfg)
					temp_act_id_dic[id] = 1
				end
			end
		end
	end

	if is_sort then
		table.sort(show_act_cfg_list, SortTools.KeyLowerSorter("order"))
	end

	return show_act_cfg_list
end

function ActCloseBetaData:GetCloseBetaVipCfg()
	return ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").vip_level_cfg
end

--获取仙盟大礼信息
function ActCloseBetaData:GetGuildRewardInfo()
	local reward_info = {}
	reward_info.is_can_get = RoleWGData.Instance.role_vo.guild_id ~= 0
	reward_info.is_gotten = self.close_beta_info.has_fetch_guild_reward == 1
	reward_info.item_data = ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").other[1].guild_item
	reward_info.bind_callback = function()
		ServerActivityWGCtrl.Instance:SendCloseBetaActivityOperaReq({opera_type = CBA_OPERA_TYPE.FETCH_GUILD_REWARD})
	end
	return reward_info
end

--获取情缘大礼信息
function ActCloseBetaData:GetMarryRewardInfo()
	local reward_info = {}
	reward_info.is_can_get = RoleWGData.Instance.role_vo.last_marry_time > 0
	reward_info.is_gotten = self.close_beta_info.has_fetch_marry_reward == 1
	reward_info.item_data = ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").other[1].marry_item
	reward_info.bind_callback = function()
		ServerActivityWGCtrl.Instance:SendCloseBetaActivityOperaReq({opera_type = CBA_OPERA_TYPE.FETCH_MARRY_REWARD})
	end
	return reward_info
end

--获取登录大礼信息
function ActCloseBetaData:GetLoginRewardInfo()
	local reward_info = {}
	reward_info.is_can_get = self.close_beta_info.has_fetch_login_reward == 0
	reward_info.is_gotten = self.close_beta_info.has_fetch_login_reward == 1

	local login_day = self.close_beta_info.total_login_days
	local login_reward = ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").login_reward
	local item_data = {}
	for k, v in pairs(login_reward) do
		if v.logn_day == login_day then
			item_data = {item_id = 65534, num = v.reward_gold, is_bind = 0}
			break
		end
		if k == #login_reward then
			item_data = {item_id = 65534, num = v.reward_gold, is_bind = 0}
		end
	end
	reward_info.item_data = item_data
	reward_info.bind_callback = function()
		ServerActivityWGCtrl.Instance:SendCloseBetaActivityOperaReq({opera_type = CBA_OPERA_TYPE.FETCH_LOING_REWARD})
	end
	return reward_info
end

function ActCloseBetaData.CreateRewardItemVo()
	local data = {}
	data.act_id = 0
	data.is_geted = false
	data.is_can_get = false
	data.reward_type = 0
	data.reward_seq = 0
	data.instruction = ""
	data.btn_name = ""
	data.item_list = {}
	data.item_func = nil
	return data
end

function ActCloseBetaData:GetChallengeClientCfg(challenge_type)
	local fb_reward_client_config = ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").fb_reward_client_config
	return fb_reward_client_config[challenge_type]
end

function ActCloseBetaData:GetActRewardList(act_id)
	if act_id == ServerActClientId.FC_LEVEL_REWARD then
		return self:GetUpLevelRewardList(act_id)
	elseif act_id == ServerActClientId.FC_ONLINE_REWARD then
		return self:GetOnlineRewardList(act_id)
	elseif act_id == ServerActClientId.FC_CHALLENGE_REWARD then
		return self:GetChallengeRewardList(act_id)
	elseif act_id == ServerActClientId.FC_ACTIVITY_REWARD then
		return self:GetActivityRewardList(act_id)
	end
end

function ActCloseBetaData:GetCloseBetaCurValue(act_id, is_str)
	if act_id == ServerActClientId.FC_LEVEL_REWARD then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		if is_str then
			return RoleWGData.GetLevelString(role_level)
		else
			return role_level
		end
	elseif act_id == ServerActClientId.FC_ONLINE_REWARD then
		return ActCloseBetaData.GetFormatTimeStr(self.close_beta_info.online_time)
	end
end

--升级狂欢
function ActCloseBetaData:GetUpLevelRewardList(act_id)
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if nil == open_act_cfg then
		return
	end
	local reward_cfg_list = ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").uplevel_reward
	local data_list = {}
	for i = 0, table.maxn(reward_cfg_list) do
		local v = reward_cfg_list[i]
		if v ~= nil then
			local act_id = open_act_cfg.id
			local btn_name = open_act_cfg.item_btn
			local reward_type = open_act_cfg.get_type
			local reward_seq = v.seq or 0
			local is_geted = self.close_beta_info.fetch_uplevel_reward_flag[32 - i] == 1
			local cur_value = self:GetCloseBetaCurValue(act_id)
			local need_num = v.need_level
			local is_can_get = false
			if not is_geted and cur_value >= need_num then
				is_can_get = true
			end
			local is_gotten = is_geted and 1 or 0

			local instruction = open_act_cfg.item_desc
			local limit_name_list = Split(open_act_cfg.limit, ",") --可能为达到目标有多个限制条件值，如vip5达到XX人
			for k, value in pairs(limit_name_list) do
				local target_value = v[value] or 0
				local replace_limit_str = "limit_value_" .. k  --替换目标名
				if "need_level" == value then
					target_value = RoleWGData.GetLevelString(target_value)
				end
				instruction = XmlUtil.RelaceTagContent(instruction, replace_limit_str, target_value)
			end
			instruction = XmlUtil.RelaceTagContent(instruction, "cur_value", cur_value)

			local item_func = function()
				ServerActivityWGCtrl.Instance:SendCloseBetaActivityOperaReq({opera_type = CBA_OPERA_TYPE.FETCH_UPLEVEL_REWARD, param_1 = reward_seq})
			end

			local data = ActCloseBetaData.CreateRewardItemVo()
			data.act_id = act_id
			data.is_geted = is_geted
			data.is_gotten = is_gotten
			data.is_can_get = is_can_get
			data.reward_type = reward_type
			data.reward_seq = reward_seq
			data.instruction = instruction
			data.item_func = item_func
			data.item_list = {{item_id = 65534, num = v.reward_gold, is_bind = 0}}

			table.insert(data_list, data)
		end
	end
	table.sort(data_list, ActCloseBetaData.KeySortFunc("is_gotten", "reward_seq"))
	return data_list
end

function ActCloseBetaData.KeySortFunc(sort_key_name1, sort_key_name2)
	return function(a, b)
		local order_a = 100
		local order_b = 100
		if a[sort_key_name1] < b[sort_key_name1] then
			order_a = order_a + 10
		elseif a[sort_key_name1] > b[sort_key_name1] then
			order_b = order_b + 10
		end

		if nil == sort_key_name2 then  return order_a > order_b end

		if a[sort_key_name2] < b[sort_key_name2] then
			order_a = order_a + 1
		elseif a[sort_key_name2] > b[sort_key_name2] then
			order_b = order_b + 1
		end

		return order_a > order_b
	end
end

function ActCloseBetaData.GetFormatTimeStr(left_time)
	local time_t = TimeUtil.Format2TableDHM(left_time)
	local time_str = ""
	if time_t.hour ~= 0 then
		time_str = time_str .. string.format(Language.OpenServer.TimeHour, time_t.hour)
	end
	if time_t.min ~= 0 then
		time_str = time_str .. string.format(Language.OpenServer.TimeMin, time_t.min)
	end
	if time_t.hour == 0 and time_t.min == 0 then
		time_str = Language.OpenServer.LessOneMin
	end
	return time_str
end

--在线奖励
function ActCloseBetaData:GetOnlineRewardList(act_id)
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if nil == open_act_cfg then
		return
	end
	local data_list = {}
	local reward_cfg_list = ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").online_time_s_reward
	for i = 0, table.maxn(reward_cfg_list) do
		local v = reward_cfg_list[i]
		if v ~= nil then
			local act_id = open_act_cfg.id
			local btn_name = open_act_cfg.item_btn
			local reward_type = open_act_cfg.get_type
			local reward_seq = v.seq or 0
			local is_geted = self.close_beta_info.has_fetch_online_reward[32 - i] == 1
			local cur_value = self.close_beta_info.online_time
			local need_num = v.need_online_time_s
			local is_can_get = false
			if not is_geted and cur_value >= need_num then
				is_can_get = true
			end
			local is_gotten = is_geted and 1 or 0

			local instruction = open_act_cfg.item_desc
			local limit_name_list = Split(open_act_cfg.limit, ",") --可能为达到目标有多个限制条件值，如vip5达到XX人
			for k, value in pairs(limit_name_list) do
				local left_time = v[value] or 0
				local time_str = ActCloseBetaData.GetFormatTimeStr(left_time)
				local replace_limit_str = "limit_value_" .. k  --替换目标名
				instruction = XmlUtil.RelaceTagContent(instruction, replace_limit_str, time_str)
			end
			cur_value = self:GetCloseBetaCurValue(act_id)
			instruction = XmlUtil.RelaceTagContent(instruction, "cur_value", cur_value)

			local item_func = function()
				ServerActivityWGCtrl.Instance:SendCloseBetaActivityOperaReq({opera_type = CBA_OPERA_TYPE.FETCH_ONLINE_REWARD, param_1 = reward_seq})
			end

			local data = ActCloseBetaData.CreateRewardItemVo()
			data.act_id = act_id
			data.is_geted = is_geted
			data.is_gotten = is_gotten
			data.is_can_get = is_can_get
			data.reward_type = reward_type
			data.reward_seq = reward_seq
			data.instruction = instruction
			data.item_func = item_func
			data.item_list = {{item_id = 65534, num = v.reward_gold, is_bind = 0}}

			table.insert(data_list, data)
		end
	end
	table.sort(data_list, ActCloseBetaData.KeySortFunc("is_gotten", "reward_seq"))
	return data_list
end

--参与活动奖励
function ActCloseBetaData:GetActivityRewardList(act_id)
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if nil == open_act_cfg then
		return
	end
	local reward_cfg_list = ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").join_activity_reward

	local data_list = {}
	for i = 0, table.maxn(reward_cfg_list) do
		local v = reward_cfg_list[i]
		local is_open_today = ActivityWGData.Instance:GetActivityIsInToday(v.activity_type)
		if v ~= nil and is_open_today then
			local act_id = open_act_cfg.id
			local btn_name = open_act_cfg.item_btn
			local reward_type = open_act_cfg.get_type
			local reward_seq = v.seq or 0
			local is_geted = self.close_beta_info.fetch_activity_reward_flag[32 - reward_seq] == 1
			local is_join = self.close_beta_info.join_activity_flag[32 - reward_seq] == 1

			local is_can_get = false
			if not is_geted and is_join then
				is_can_get = true
			end
			local item_func = function()
				ServerActivityWGCtrl.Instance:SendCloseBetaActivityOperaReq({opera_type = CBA_OPERA_TYPE.FETCH_JOIN_ACTIVITY_REWARD, param_1 = reward_seq})
			end
			local instruction = string.format(Language.Activity.JoinActCanGet, ActivityWGData.GetActivityName(v.activity_type))

			local data = ActCloseBetaData.CreateRewardItemVo()
			data.act_id = act_id
			data.is_geted = is_geted
			data.is_can_get = is_can_get
			data.reward_type = reward_type
			data.reward_seq = reward_seq
			data.instruction = instruction
			data.item_func = item_func
			data.item_list = {{item_id = 65534, num = v.reward_gold, is_bind = 0}}

			table.insert(data_list, data)
		end
	end
	return data_list
end

--挑战大礼
function ActCloseBetaData:GetChallengeRewardList(act_id)
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if nil == open_act_cfg then
		return
	end
	local data_list = {}
	if true then return data_list end
	for challenge_type = 1, 4 do
		local reward_cfg_list = self:GetChallengeListCfg(challenge_type)
		local last_unget_seq = self:GetChallengeLastUnGetReward(challenge_type)
		local is_geted = last_unget_seq > (#reward_cfg_list)

		local reward_data = {}
		reward_data = reward_cfg_list[last_unget_seq] or reward_cfg_list[#reward_cfg_list]

		local challenge_cfg = self:GetChallengeClientCfg(challenge_type)

		local is_can_get = (not is_geted) and self:GetChallengeIsSatisfyGetReward(challenge_type, last_unget_seq)
		local reward_type = challenge_cfg.get_type
		local reward_seq = reward_data.seq

		local instruction = challenge_cfg.item_desc
		local limit_name_list = Split(challenge_cfg.limit, ",") --可能为达到目标有多个限制条件值，如vip5达到XX人
		for k, value in pairs(limit_name_list) do
			local target_value = reward_data[value] or 0
			if value == "need_chapter" or value == "need_level" then
				target_value = target_value + 1
			end
			local replace_limit_str = "limit_value_" .. k  --替换目标名
			instruction = XmlUtil.RelaceTagContent(instruction, replace_limit_str, target_value)
		end
		if is_geted then
			instruction = challenge_cfg.fb_name .. Language.OpenServer.AllPass
		end

		local item_func = nil
		if is_can_get then
			item_func = function()
				ServerActivityWGCtrl.Instance:SendCloseBetaActivityOperaReq({opera_type = reward_type, param_1 = reward_seq})
			end
		else
			item_func = function()
				local open_param = Split(challenge_cfg.item_btn, "#")
				if "" ~= open_param[2] then
					local view_name, tab_index = open_param[2], open_param[3]
					FunOpen.Instance:OpenViewByName(view_name, tab_index)
				end
			end
		end

		local data = ActCloseBetaData.CreateRewardItemVo()
		data.act_id = act_id
		data.is_geted = is_geted
		data.is_can_get = is_can_get
		data.reward_type = reward_type
		data.reward_seq = reward_seq
		data.instruction = instruction
		data.item_func = item_func
		data.item_list = {{item_id = 65534, num = reward_data.reward_gold, is_bind = 0}}

		table.insert(data_list, data)
	end

	return data_list
end

function ActCloseBetaData:GetChallengeListCfg(challenge_type)
	if 1 == challenge_type then
		return ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").expfb_cfg
	elseif 2 == challenge_type then
		return ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").equipfb_cfg
	elseif 3 == challenge_type then
		return ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").tdfb_cfg
	elseif 4 == challenge_type then
		return ConfigManager.Instance:GetAutoConfig("closedbetaconfig_auto").qualityfb_cfg
	end
end

function ActCloseBetaData:GetChallengeLastUnGetReward(challenge_type)
	local fetch_flag = nil
	if 1 == challenge_type then
		fetch_flag = self.close_beta_info.expfb_fetch_reward_flag
	elseif 2 == challenge_type then
		fetch_flag = self.close_beta_info.equipfb_fetch_reward_flag
	elseif 3 == challenge_type then
		fetch_flag = self.close_beta_info.tdfb_fetch_reward_flag
	elseif 4 == challenge_type then
		fetch_flag = self.close_beta_info.challengefb_fetch_reeward_flag
	end
	local last_unget_seq = 0
	for i = 1, 32 do
		if fetch_flag[33 - i] == 0 then
			last_unget_seq = i - 1
			break
		end
	end
	return last_unget_seq
end

function ActCloseBetaData:GetChallengeIsSatisfyGetReward(challenge_type, seq)
	local is_can_get = false
	local index = 32 - seq
	if 1 == challenge_type then
		is_can_get = self.close_beta_info.expfb_satisfy_reward_cond_flag[index] == 1
	elseif 2 == challenge_type then
		is_can_get = self.close_beta_info.equipfb_satisfy_reward_cond_flag[index] == 1
	elseif 3 == challenge_type then
		is_can_get = self.close_beta_info.tdfb_satisfy_reward_cond_flag[index] == 1
	elseif 4 == challenge_type then
		is_can_get = self.close_beta_info.challengefb_satisfy_reward_cond_flag[index] == 1
	end
	return is_can_get
end