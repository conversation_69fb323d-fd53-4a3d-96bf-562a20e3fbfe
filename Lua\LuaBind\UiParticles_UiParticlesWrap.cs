﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UiParticles_UiParticlesWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(UiParticles.UiParticles), typeof(UnityEngine.UI.MaskableGraphic));
		<PERSON><PERSON>Function("SetMaterialDirty", SetMaterialDirty);
		<PERSON><PERSON>ction("GetModifiedMaterial", GetModifiedMaterial);
		<PERSON><PERSON>unction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON>.Reg<PERSON>ar("ParticleSystem", get_ParticleSystem, set_ParticleSystem);
		<PERSON><PERSON>("mainTexture", get_mainTexture, null);
		<PERSON>.<PERSON>ar("RenderMode", get_RenderMode, set_RenderMode);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMaterialDirty(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UiParticles.UiParticles obj = (UiParticles.UiParticles)ToLua.CheckObject<UiParticles.UiParticles>(L, 1);
			obj.SetMaterialDirty();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetModifiedMaterial(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UiParticles.UiParticles obj = (UiParticles.UiParticles)ToLua.CheckObject<UiParticles.UiParticles>(L, 1);
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			UnityEngine.Material o = obj.GetModifiedMaterial(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ParticleSystem(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UiParticles.UiParticles obj = (UiParticles.UiParticles)o;
			UnityEngine.ParticleSystem ret = obj.ParticleSystem;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ParticleSystem on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mainTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UiParticles.UiParticles obj = (UiParticles.UiParticles)o;
			UnityEngine.Texture ret = obj.mainTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mainTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RenderMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UiParticles.UiParticles obj = (UiParticles.UiParticles)o;
			UiParticles.UiParticleRenderMode ret = obj.RenderMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RenderMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ParticleSystem(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UiParticles.UiParticles obj = (UiParticles.UiParticles)o;
			UnityEngine.ParticleSystem arg0 = (UnityEngine.ParticleSystem)ToLua.CheckObject(L, 2, typeof(UnityEngine.ParticleSystem));
			obj.ParticleSystem = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ParticleSystem on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RenderMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UiParticles.UiParticles obj = (UiParticles.UiParticles)o;
			UiParticles.UiParticleRenderMode arg0 = (UiParticles.UiParticleRenderMode)ToLua.CheckObject(L, 2, typeof(UiParticles.UiParticleRenderMode));
			obj.RenderMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RenderMode on a nil value");
		}
	}
}

