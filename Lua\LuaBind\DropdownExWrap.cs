﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class DropdownExWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(DropdownEx), typeof(UnityEngine.UI.Dropdown));
		<PERSON><PERSON>Function("ShowGameObject", ShowGameObject);
		<PERSON><PERSON>unction("AddListChangeListener", AddListChangeListener);
		<PERSON><PERSON>unction("RemoveListChangeListener", RemoveListChangeListener);
		L.RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("activeGameObject", get_activeGameObject, set_activeGameObject);
		<PERSON>.<PERSON>("inactiveGameObject", get_inactiveGameObject, set_inactiveGameObject);
		<PERSON><PERSON>("OnShowStatusChange", get_OnShowStatusChange, set_OnShowStatusChange);
		<PERSON><PERSON>("ListShowStatus", get_ListShowStatus, set_ListShowStatus);
		<PERSON><PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ShowGameObject(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			DropdownEx obj = (DropdownEx)ToLua.CheckObject<DropdownEx>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.ShowGameObject(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddListChangeListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			DropdownEx obj = (DropdownEx)ToLua.CheckObject<DropdownEx>(L, 1);
			System.Action<bool> arg0 = (System.Action<bool>)ToLua.CheckDelegate<System.Action<bool>>(L, 2);
			obj.AddListChangeListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveListChangeListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			DropdownEx obj = (DropdownEx)ToLua.CheckObject<DropdownEx>(L, 1);
			System.Action<bool> arg0 = (System.Action<bool>)ToLua.CheckDelegate<System.Action<bool>>(L, 2);
			obj.RemoveListChangeListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_activeGameObject(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DropdownEx obj = (DropdownEx)o;
			UnityEngine.GameObject[] ret = obj.activeGameObject;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index activeGameObject on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_inactiveGameObject(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DropdownEx obj = (DropdownEx)o;
			UnityEngine.GameObject[] ret = obj.inactiveGameObject;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index inactiveGameObject on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnShowStatusChange(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DropdownEx obj = (DropdownEx)o;
			System.Action<bool> ret = obj.OnShowStatusChange;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OnShowStatusChange on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ListShowStatus(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DropdownEx obj = (DropdownEx)o;
			bool ret = obj.ListShowStatus;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ListShowStatus on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_activeGameObject(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DropdownEx obj = (DropdownEx)o;
			UnityEngine.GameObject[] arg0 = ToLua.CheckObjectArray<UnityEngine.GameObject>(L, 2);
			obj.activeGameObject = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index activeGameObject on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_inactiveGameObject(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DropdownEx obj = (DropdownEx)o;
			UnityEngine.GameObject[] arg0 = ToLua.CheckObjectArray<UnityEngine.GameObject>(L, 2);
			obj.inactiveGameObject = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index inactiveGameObject on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnShowStatusChange(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DropdownEx obj = (DropdownEx)o;
			System.Action<bool> arg0 = (System.Action<bool>)ToLua.CheckDelegate<System.Action<bool>>(L, 2);
			obj.OnShowStatusChange = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OnShowStatusChange on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ListShowStatus(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DropdownEx obj = (DropdownEx)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.ListShowStatus = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ListShowStatus on a nil value");
		}
	}
}

