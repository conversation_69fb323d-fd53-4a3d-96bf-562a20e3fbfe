JinyintaView = JinyintaView or BaseClass(SafeBaseView)
JinyintaView.SHOW_EFF = {
	[GameEnum.ITEM_COLOR_GREEN] = 111,							-- 绿
	[GameEnum.ITEM_COLOR_BLUE] = 110,							-- 蓝
	[GameEnum.ITEM_COLOR_PURPLE] = 112,							-- 紫
	[GameEnum.ITEM_COLOR_ORANGE] = 109,							-- 橙
	[GameEnum.ITEM_COLOR_RED] = 132,							-- 红
}

function JinyintaView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/jinyinta_ui_prefab", "layout_gold_silvertower")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
	self.cell_list = {}
	self.news_list = nil
	self.free_times = 0
	self.cur_layout_pos = nil
	self.pos_num = 0
end

function JinyintaView:__delete()
	self.cur_layout_pos = nil
	self.pos_num = nil

end

function JinyintaView:ReleaseCallBack()
	Runner.Instance:RemoveRunObj(self)
	self.cur_layout_pos = nil
	self.pos_num = 0
	for k,v in pairs(self.cell_list) do
		for k,v in pairs(v) do
			v:DeleteMe()
		end
	end
	self.cell_list = {}
	if self.news_list then
		self.news_list:DeleteMe()
		self.news_list = nil
	end

	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end

	if CountDownManager.Instance:HasCountDown("act_jinyinta") then
		CountDownManager.Instance:RemoveCountDown("act_jinyinta")
	end
	if CountDownManager.Instance:HasCountDown("jinyinta_free_time") then
		CountDownManager.Instance:RemoveCountDown("jinyinta_free_time")
	end

	if self.change_callback then
		if nil ~= RoleWGData.Instance then
			RoleWGData.Instance:UnNotifyAttrChange(self.change_callback)
		end
		self.change_callback = nil
	end
end

function JinyintaView:LoadCallBack()
		-- 加载标题
		self.node_list["img_title"].image:LoadSprite(ResPath.GetActivityTitle("Jinyinta"))
		XUI.ImageSetNativeSize(self.node_list["img_title"])

		self:CreateItems()
		self:CreateNewsList()

		XUI.AddClickEventListener(self.node_list.layout_turn_one, BindTool.Bind2(self.OnClickDraw, self, 1))
		XUI.AddClickEventListener(self.node_list.layout_turn_ten, BindTool.Bind2(self.OnClickDraw, self, 10))
		XUI.AddClickEventListener(self.node_list.layout_turn_fifty, BindTool.Bind2(self.OnClickDraw, self, 50))
		XUI.AddClickEventListener(self.node_list.btn_howplay,BindTool.Bind(self.OnClickTips, self))
		XUI.AddClickEventListener(self.node_list.btn_rechange, BindTool.Bind(self.OnClickXunBaoScore, self))
		self.node_list.lbl_free_times.text.text = (Language.OpenServer.GoBuy)

		if not self.change_callback then
			self.change_callback = BindTool.Bind(self.PlayerDataChangeCallback, self)
			RoleWGData.Instance:NotifyAttrChange(self.change_callback, {"gold"})
		end
		local main_role_vo = RoleWGData.Instance:GetRoleInfo()
		self.node_list.lbl_money.text.text = CommonDataManager.ConverMoney(tonumber( main_role_vo['gold']))
		Runner.Instance:AddRunObj(self)

		local cutom_head_info = RoleWGData.Instance:GetCustomHeadInfo()
		XUI.UpdateMainRoleHead(cutom_head_info.avatar_key_big, 
								cutom_head_info.avatar_key_small, 
								self.node_list.img_flag_head,
								self.node_list.custom_flag_head,
								main_role_vo.role_id,
								main_role_vo.sex, main_role_vo.prof,
								nil, true, false)
end

function JinyintaView:PlayerDataChangeCallback(attr_name, value)
	if attr_name == 'gold' then
		self.node_list.lbl_money.text.text = CommonDataManager.ConverMoney(tonumber(value))
	end
end

function JinyintaView:OpenCallBack()
	local param_t = {rand_activity_type = ACTIVITY_TYPE.RAND_JINYINTA, opera_type = RendActOperaType.QUERY_INFO}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	JinyintaWGCtrl.Instance:SendXunbaoReq(0)
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, GuideModuleName.JinyintaView, 0, BURIED_EVENT_PARAM.openView)
end

function JinyintaView:CloseCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, GuideModuleName.JinyintaView, 0, BURIED_EVENT_PARAM.closeView)
end

function JinyintaView:ShowIndexCallBack(index)
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, index, ACTIVITY_TYPE.RAND_JINYINTA, BURIED_EVENT_PARAM.openView)
end

function JinyintaView:Update(now_time, elapse_time)
	if self.cur_layout_pos ~= nil then
		self.pos_num = self.pos_num + elapse_time
		self.node_list.layout_flag.rect.anchoredPosition = Vector2(self.cur_layout_pos.x,self.cur_layout_pos.y + math.sin(math.rad(180*self.pos_num))*10)
	end
end

function JinyintaView:CreateItems()
	local show_list = JinyintaWGData.Instance:GetShowItems()
	for i = 1, 6 do
		for j = 1, 6 do
			local ph = self.node_list['ph_item_list_' .. i]
			if ph and show_list[i] and show_list[i][j] then
				local cell = ItemCell.New(ph)
				self.cell_list[i] = self.cell_list[i] or {}
				self.cell_list[i][j] = cell
				cell:SetData(show_list[i][j])
			end
		end
	end
end

-- 获奖列表
function JinyintaView:CreateNewsList()
	self.news_list = AsyncListView.New(JinyintaItemRender,self.node_list.ph_winner_list)
end

function JinyintaView:OnFlush()
	local cur_layout = JinyintaWGData.Instance:GetCurLayer() + 1
	if cur_layout and self.node_list['ph_item_list_' .. cur_layout] then
		self.cur_layout_pos = self.node_list['ph_item_list_' .. cur_layout].rect.anchoredPosition
	end
	local one_cost = JinyintaWGData.Instance:GetOneDrawCost(cur_layout)

	-- 如果背包有天天向上钥匙优先显示钥匙
	local keyNum = ItemWGData.Instance:GetItemNumInBagById(JinyintaWGData.Instance:GetLotterykeyID())
	if keyNum > 0 then
		self.node_list.lbl_one.text.text = (keyNum)
		self.node_list.img_item2:SetActive(false)
		self.node_list.lbl_ten:SetActive(false)
	else
		self.node_list.lbl_one.text.text = (one_cost)
		self.node_list.img_item2:SetActive(true)
		self.node_list.lbl_ten.text.text = (one_cost * 10)
		self.node_list.lbl_ten:SetActive(true)
	end
	-- 如果背包有天天向上五十连抽钥匙优先显示钥匙
	local randact_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if randact_cfg == nil or randact_cfg.other == nil or randact_cfg.other[1].level_lottery_50_item_id == nil then return end
	local id_fiftychou = randact_cfg.other[1].level_lottery_50_item_id
	local chuizi_num = ItemWGData.Instance:GetItemNumInBagById(id_fiftychou)
	if chuizi_num > 0 then
		self.node_list.lbl_fifty.text.text = (chuizi_num)
		self.node_list.img_item3:SetActive(true)
		self.node_list.lbl_fifty:SetActive(true)
	elseif keyNum > 0 then
		self.node_list.img_item3:SetActive(false)
		self.node_list.lbl_fifty:SetActive(false)
	else
		self.node_list.lbl_fifty.text.text = (one_cost * 50)
		self.node_list.img_item3:SetActive(true)
		self.node_list.lbl_fifty:SetActive(true)
	end



	local jifen_num = JinyintaWGData.Instance:GetXunBaoScore()
	self.node_list.lbl_jifen.text.text = (jifen_num)

	local history_data = JinyintaWGData.Instance:GetHistoryList()
	self.news_list:SetDataList(history_data,3)

	local act_statu = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_JINYINTA)
	if CountDownManager.Instance:HasCountDown("act_jinyinta") then
		CountDownManager.Instance:RemoveCountDown("act_jinyinta")
	end
	if act_statu and ACTIVITY_STATUS.OPEN == act_statu.status then
		local next_time = act_statu.next_time or 0
		self:UpdataRollerTime(TimeWGCtrl.Instance:GetServerTime(), next_time)
		CountDownManager.Instance:AddCountDown("act_jinyinta", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), next_time, nil, 1)
	else
		self:CompleteRollerTime()
	end
	self:FlushMianFeiNum()

	if CountDownManager.Instance:HasCountDown("jinyinta_free_time") then
		CountDownManager.Instance:RemoveCountDown("jinyinta_free_time")
	end
	self.free_times = JinyintaWGData.Instance:GetFreeTimes()
	if 5 ~= self.free_times then
		if self.free_times > 0 then
			self:SetLblOneHidpOrDis(true)
		else
			self:SetLblOneHidpOrDis(false)
		end
		local NextFreeRemainTime = JinyintaWGData.Instance:GetNextFreeRemainTime()
		local severtime = TimeWGCtrl.Instance:GetServerTime()
		local next_time = severtime + NextFreeRemainTime
		self:UpdataRemainTime(severtime, next_time)
		if CountDownManager.Instance:HasCountDown("jinyinta_free_time") then
			CountDownManager.Instance:RemoveCountDown("jinyinta_free_time")
		end

		if 0 == NextFreeRemainTime then
			return
		end
		CountDownManager.Instance:AddCountDown("jinyinta_free_time", BindTool.Bind1(self.UpdataRemainTime, self), BindTool.Bind1(self.CompleteRemainTime, self), next_time, nil, 1)
	else
		self:SetLblOneHidpOrDis(true)
	end

end

function JinyintaView:FlushMianFeiNum()
	local act_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if act_cfg == nil or act_cfg.other == nil or act_cfg.other[1].level_lottery_50_item_id == nil then
		self.node_list.rich_miao_shou:SetActive(false)
		return
	end
	local stuff_id = act_cfg.other[1].level_lottery_50_item_id
	local item_name = ItemWGData.Instance:GetItemName(stuff_id)
	local item_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
	if item_num > 0 then
		local content = string.format(Language.LotteryTree.Miaoshou, item_num, item_name)
		self.node_list.rich_miao_shou.text.text = content
		self.node_list.rich_miao_shou:SetActive(false)
	else
		self.node_list.rich_miao_shou:SetActive(false)
	end
end

function JinyintaView:UpdataRollerTime(elapse_time, total_time)
	local time = total_time - elapse_time
	if time > 0 then
		local format_time = TimeUtil.Format2TableDHM(time)
		local str_list = Language.Common.TimeList
		local time_str = ""
		if format_time.day > 0 then
			time_str = format_time.day .. str_list.d
		end
		if format_time.hour > 0 then
			time_str = time_str .. format_time.hour .. str_list.h
		end
		time_str = time_str .. format_time.min .. str_list.min

		self.node_list.lbl_open_time.text.text = (time_str)
	end
end

function JinyintaView:CompleteRollerTime()
	self.node_list.lbl_open_time.text.text = ("0")
end

function JinyintaView:UpdataRemainTime(elapse_time, total_time)
	local retain_time = total_time - elapse_time
	if self.free_times > 0 then
		self.node_list.lbl_free_times.text.text = (Language.Jinyinta.FreeTimes .. self.free_times)
	elseif retain_time > 0 then
		self.node_list.lbl_free_times.text.text = (TimeUtil.FormatSecond2HMS(retain_time) .. Language.Jinyinta.HouMianFei)
	else
		self.node_list.lbl_free_times.text.text = ("")
	end
end


function JinyintaView:CompleteRemainTime()
	if CountDownManager.Instance:HasCountDown("jinyinta_free_time") then
		CountDownManager.Instance:RemoveCountDown("jinyinta_free_time")
	end
	self:SetLblOneHidpOrDis(true)
end

function JinyintaView:SetLblOneHidpOrDis(flag)
	if true == flag then
		self.node_list.lbl_free_times:SetActive(true)
		self.node_list.lbl_free_times.text.text =(Language.Jinyinta.FreeTimes..self.free_times)
	else
		if 0 == self.free_times then
			self.node_list.lbl_free_times.text.text =(Language.Jinyinta.FreeTimes..self.free_times)
		end
	end
end

function JinyintaView:OnClickDraw(count)
	JinyintaWGData.Instance:SetDrawCount(count)
	local keyNum = ItemWGData.Instance:GetItemNumInBagById(JinyintaWGData.Instance:GetLotterykeyID())

	local randact_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if randact_cfg == nil or randact_cfg.other == nil or randact_cfg.other[1].level_lottery_50_item_id == nil then return end
	local chuizi_num = ItemWGData.Instance:GetItemNumInBagById(randact_cfg.other[1].level_lottery_50_item_id)
	if chuizi_num > 0 then
		self:sendChestShopOperate(count)
	elseif keyNum < count then
		if self.alert == nil then
			self.alert = Alert.New()
			self.alert:SetShowCheckBox(true)
		end
		local cur_layout = JinyintaWGData.Instance:GetCurLayer()
		local cost = JinyintaWGData.Instance:GetOneDrawCost(cur_layout) * count
		self.alert:SetOkFunc(BindTool.Bind2(self.sendChestShopOperate, self, count))
		self.alert:SetLableString(string.format(Language.PigTreasure.AlertTips, cost, count))
		self.alert:Open()
	else
		self:sendChestShopOperate(count)
	end
end

function JinyintaView:sendChestShopOperate(count)
	local param_t = {rand_activity_type = ACTIVITY_TYPE.RAND_JINYINTA, opera_type = RendActOperaType.GET_ITEM, param_1 = count}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function JinyintaView:OnClickTips()
 	RuleTip.Instance:SetContent(Language.OpenServer.JinyintaAfterTips, Language.OpenServer.JinyintaTips)
end

function JinyintaView:OnClickXunBaoScore()
	JinyintaWGCtrl.Instance:OpenExchange()
end


--------------------JinyintaItemRender--------------------
JinyintaItemRender = JinyintaItemRender or BaseClass(BaseRender)
function JinyintaItemRender:__init()
	self:CreateChild()
end
function JinyintaItemRender:CreateChild()
	self.lbl_winner_name = self.node_list.text_winner_name
	self.lbl_reward_name = self.node_list.text_reward_name
end

function JinyintaItemRender:__delete()
	self.lbl_winner_name = nil
	self.lbl_reward_name = nil
end

function JinyintaItemRender:OnFlush()
	if not self.data then
		return
	end
	self.lbl_winner_name.text.text = (self.data.user_name)
	local content = ""
	local color = COLOR3B.WHITE
	local item_info = JinyintaWGData.Instance:GetOneShowItem(self.data.reward_index)
	if item_info then
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_info.item_id)
		if item_cfg then
			color = ITEM_COLOR[item_cfg.color]
			content =  ToColorStr(item_cfg.name .. " x" ..  item_info.num,color)
		end
	end
	self.lbl_reward_name.text.text = (content)
end

function JinyintaItemRender:CreateSelectEffect()
end