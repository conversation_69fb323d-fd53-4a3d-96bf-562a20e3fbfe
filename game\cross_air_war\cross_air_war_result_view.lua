CrossAirWarResultView = CrossAirWarResultView or BaseClass(SafeBaseView)

function CrossAirWarResultView:__init()
	self:AddViewResource(0, "uis/view/cross_air_war_ui_prefab", "layout_kf_air_war_result")
   	self:SetMaskBg(false, false)
end

function CrossAirWarResultView:LoadCallBack()
	if not self.auction_reward_list then
		self.auction_reward_list = AsyncListView.New(ItemCell, self.node_list["auction_reward_list"])
	end

	if not self.role_reward_list then
		self.role_reward_list = AsyncListView.New(ItemCell, self.node_list["role_reward_list"])
	end
end

function CrossAirWarResultView:ReleaseCallBack()
	if self.auction_reward_list then
		self.auction_reward_list:DeleteMe()
		self.auction_reward_list = nil
	end

	if self.role_reward_list then
		self.role_reward_list:DeleteMe()
		self.role_reward_list = nil
	end
end

function CrossAirWarResultView:OnFlush(param)
	local auction_list, person_list, scoring_index = CrossAirWarWGData.Instance:GetFinalBossReawardList()
	self.auction_reward_list:SetDataList(auction_list)
	self.role_reward_list:SetDataList(person_list)
	self.node_list.kfkz_time_score_bg:CustomSetActive(scoring_index > 0)
	
	if scoring_index > 0 then
		self.node_list.kfkz_time_score_img.image:LoadSprite(ResPath.GetCommon(string.format("a3_fb_ysz_%d", scoring_index)))
	end
end