---------------
--开宗立派
---------------
function ServerActivityTabView:KZLPReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("Inneglectable_end_time") then
        CountDownManager.Instance:RemoveCountDown("Inneglectable_end_time")
    end

	-- if self.kzlp_guild_list_view then
	-- 	for key, value in pairs(self.kzlp_guild_list_view) do
	-- 		value:DeleteMe()
	-- 		value = nil
	-- 	end
	-- 	self.kzlp_guild_list_view = nil
	-- end

	-- 销毁循环列表
	if self.kzlp_guild_list then
		self.kzlp_guild_list:DeleteMe()
		self.kzlp_guild_list = nil
	end

	-- self.desc_data_list = nil


	-- self.now_data = nil
end

function ServerActivityTabView:KZLPLoadCallBack()
	-- self.kzlp_guild_list_view = {}

	self.kzlp_guild_list = AsyncListView.New(ActGuildItemRender, self.node_list.kzlp_ph_list)
	
	-- self.kzlp_guild_list:SetSelectCallBack(BindTool.Bind(self.KFGiftOnClickItem, self))

	-- for i = 1 , 8 do
	-- 	self.kzlp_guild_list_view[i] = ActGuildItemRender.New(self.node_list["button_"..i])
	-- 	XUI.AddClickEventListener(self.node_list["button_"..i], BindTool.Bind(self.FlushDesc, self, i))
	-- end

	self:KZLPRefreshView()

end

function ServerActivityTabView:KZLPShowIndexCallBack()
	self.kzlp_guild_list:JumpToTop()
end

function ServerActivityTabView:KZLPOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "act_info" and v.protocol_id == 2718 then
			self:KZLPRefreshView()
		end
	end
end

-- 刷新当前视图
function ServerActivityTabView:KZLPRefreshView()
	if self.kzlp_guild_list then
		local cfg_list = ServerActivityWGData.Instance:GetOpenLeekpaiCfgList()

		local data_list = {}
		for i,data in ipairs(cfg_list) do
			local guild_flag = ServerActivityWGData.Instance:GetCreateGuildFlagList(data.seq)
			local num = ServerActivityWGData.Instance:GetCreateGuildNumList(data.seq + 1)
			

			if guild_flag == 1 then
				data_list[1000 + i] = data
			elseif num <= 0 then
				data_list[10000 + i] = data
			else
				local can_reward = ServerActivityWGData.Instance:GetOpenLeekpaiCanReward(data)
				if can_reward then
					data_list[i] = data
				else
					data_list[100 + i] = data
				end
			end
		end
		data_list = SortTableKey(data_list)

		self.kzlp_guild_list:SetDataList(data_list)
	end

	self:KZLPFlushCountDownTime()

end

function ServerActivityTabView:KZLPFlushCountDownTime()
	if CountDownManager.Instance:HasCountDown("Inneglectable_end_time") then
        CountDownManager.Instance:RemoveCountDown("Inneglectable_end_time")
    end

	local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ServerActClientId.OPEN_INNEGLECTABLE)
	if count_down_time > 0 then
		self.node_list.kzlp_version_act_time.text.text = string.format(Language.OpenServer.ActRemainTime2,TimeUtil.FormatSecondDHM8(count_down_time)) 
		
		CountDownManager.Instance:AddCountDown(
			"Inneglectable_end_time",
			BindTool.Bind(self.KZLPUpdateCountDown, self),
			BindTool.Bind(self.KZLPFlushCountDownTime, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.kzlp_version_act_time.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.kzlp_version_act_time.text.color = Str2C3b(COLOR3B.RED)
	end
end

function ServerActivityTabView:KZLPUpdateCountDown(elapse_time, total_time)
	self.node_list.kzlp_version_act_time.text.text = string.format(Language.OpenServer.ActRemainTime2,TimeUtil.FormatSecondDHM8(total_time - elapse_time)) 
end



--------------------------------ActGuildItemRender-----------------------------
ActGuildItemRender = ActGuildItemRender or BaseClass(BaseRender)

function ActGuildItemRender:__init()
	self.can_reward = false

	XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind(self.OnClickBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind(self.OnClickBtn, self))

end

function ActGuildItemRender:LoadCallBack()
	if self.reward_list == nil then
		local bundle, asset = "uis/view/open_server_activity_ui_prefab", "reward_item"
		self.reward_list = AsyncBaseGrid.New()
		self.reward_list:CreateCells({
			col = 2,
			change_cells_num = 1,
			list_view = self.node_list.reward_list,
			assetBundle = bundle,
			assetName = asset,
			itemRender = ActGuildItemCellRender
		})
		self.reward_list:SetStartZeroIndex(false)
	end
end

function ActGuildItemRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end


function ActGuildItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return 
	end

	self.node_list.rich_title.text.text = data.title_desc or ""
	self.node_list.rich_desc.text.text = data.target_desc or ""
	
	local is_create = RoleWGData.Instance.role_vo.guild_id ~= 0
	local cur_value, max_value = ServerActivityWGData.Instance:GetOpenLeekpaiProgress(data)
	self.node_list.text_progress.text.text = string.format(Language.OpenServer.GuildTaskProgress, cur_value, max_value)

	local guild_flag = ServerActivityWGData.Instance:GetCreateGuildFlagList(data.seq)
	local num = ServerActivityWGData.Instance:GetCreateGuildNumList(data.seq + 1)

	-- 奖励显示
	local item_list = {}
	for key, value in pairs(data.reward_item) do
		local item_data = {}
		item_data.data = value
		item_data.flag = guild_flag
		table.insert(item_list, item_data)
	end
	self.reward_list:SetDataList(item_list)

	self:HideBtn()

	-- 已领取
	if guild_flag == 1 then
		self.node_list.img_green:CustomSetActive(true)
		return
	end
	
	-- 领取完
	if num <= 0 then
		self.node_list.img_red:CustomSetActive(num <= 0)
		return
	end

	-- 已创建
	if is_create then
		local guild_post = RoleWGData.Instance:GetAttr("guild_post")
		if guild_post == GUILD_POST.TUANGZHANG then
			self.node_list.text_btn.text.text = Language.Common.LingQu2
			self.node_list.text_num.text.text = string.format(Language.OpenServer.KzlpGet, num)

			if ServerActivityWGData.Instance:GetOpenLeekpaiCanReward(data) then
				self.node_list.btn_lingqu:CustomSetActive(true)
			else
				self.node_list.btn_goto:CustomSetActive(true)
				self.node_list.text_goto_num.text.text = string.format(Language.OpenServer.KzlpGet, num)
			end
		else
			self.node_list.text_tips.text.text = string.format(Language.OpenServer.NoBnagzhu, num)
			self.node_list.text_tips:CustomSetActive(true)
		end
	--未创建
	else
		if data.reward_type == KAIZONGLIPAI_TARGET_TYPE.creat_guid then
			if not ServerActivityWGData.Instance:GetOpenLeekpaiCanReward(data) then
				self.node_list.btn_goto:CustomSetActive(true)
				self.node_list.text_goto_num.text.text = string.format(Language.OpenServer.KzlpGet, num)
			end
		else
			self.node_list.text_tips.text.text = string.format(Language.OpenServer.NoCreateGuild, num)
			self.node_list.text_tips:CustomSetActive(true)
		end
	end

	local can_reward = false
	if guild_flag == 0 then
		can_reward = ServerActivityWGData.Instance:GetOpenLeekpaiCanReward(data)
	end
	self.node_list.red_point:SetActive(can_reward)
	self.can_reward = can_reward
end

function ActGuildItemRender:HideBtn()
	self.node_list.btn_lingqu:CustomSetActive(false)
	self.node_list.btn_goto:CustomSetActive(false)
	self.node_list.img_green:CustomSetActive(false)
	self.node_list.img_red:CustomSetActive(false)
	self.node_list.text_tips:CustomSetActive(false)
end

function ActGuildItemRender:OnClickBtn()
	local guild_is_open = FunOpen.Instance:GetFunIsOpened(FunName.Guild)
	if not guild_is_open then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.FunOpenTip)
		return
	end

	local data = self:GetData()
	if self.can_reward then
		ServerActivityWGCtrl.Instance:SendOpenGameActivityFetchReward(OPEN_SERVER_REWARD_TYPE.REWARD_TYPE_CREATE_GUILD_REWARD, data.seq)
	elseif data.reward_type == KAIZONGLIPAI_TARGET_TYPE.creat_guid then
		if not GuildWGData.Instance:HasGuild() then
			GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist, {sub_view_name = SubViewName.CreateGuild})
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.NoCreateGuildTip)
		end
	else
		GuildWGCtrl.Instance:Open(TabIndex.guild_info)
	end
end

-----------------
ActGuildItemCellRender = ActGuildItemCellRender or BaseClass(BaseRender)

function ActGuildItemCellRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_pos)
end


function ActGuildItemCellRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ActGuildItemCellRender:OnFlush()
    if not self.data then
        return
    end
	self.item_cell:SetData(self.data.data)
	self.item_cell:SetLingQuVisible(self.data.flag == 1)
end
