local MONNEY_TYPE = {
	[1] = "a3_huobi_xianyu",
	[2] = "a3_huobi_bangyu",
	[3] = "a3_huobi_score",
}

local TYPE_MAX = 3

-- local LIST_VIEW_HIGHT = {
-- 	[1] = Vector2(1050, 362),			--3*3
-- 	[2] = Vector2(1050, 530),			--3*4
-- }

local tianshenroad_miaosha_safe_key = "tianshenroad_miaosha_toggle_key"
local tianshenroad_miaosha_act_countdown_key = "tianshenroad_miaosha_act_countdown"--活动倒计时

function TianshenRoadView:InitHeFuMiaoSha()

end

function TianshenRoadView:LoadIndexCallBackHeFuMiaoSha()
	self.select_type = 1
	self.is_need_slider = true
	TianShenRoadMiaoshaWGData.Instance:SetMiaoShaSelectType(self.select_type)

	if nil == self.miaosha_list_view then
		local bundle, asset = "uis/view/tianshenroad_ui_prefab", "tianshenroad_miaosha_cell"
		self.miaosha_list_view = AsyncBaseGrid.New()
		self.miaosha_list_view:CreateCells({col = 5, itemRender = TianShenRoadMiaoShaCell,
			list_view = self.node_list.miaosha_list_view, assetBundle = bundle, assetName = asset, change_cells_num = 1})
		self.miaosha_list_view:SetStartZeroIndex(false)
	end

	if not self.top_btn_list then
		self.top_btn_list = {}

		for i=1, TYPE_MAX do
			self.top_btn_list[i] = self.node_list["miaosha_top_btn_" .. i]
			self.top_btn_list[i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTopToggle, self, i))
		end

		self.top_btn_list[self.select_type].toggle.isOn = true
	end

	if not self.miaosha_top_gift_list then
		self.miaosha_top_gift_list = {}

		for i=1,5 do
			self.miaosha_top_gift_list[i] = ItemCell.New(self.node_list["miaosha_item_" .. i])
			self.miaosha_top_gift_list[i]:SetClickCallBack(BindTool.Bind(self.OnclickGiftItem, self, i))
			self.miaosha_top_gift_list[i]:SetIsShowTips(false)
			self.miaosha_top_gift_list[i]:SetRightBottomTextVisible(true)
		end
	end

	self:TSMSTimeCountDown()
end

function TianshenRoadView:DeleteHeFuMiaoSha()
	if self.miaosha_list_view then
		self.miaosha_list_view:DeleteMe()
		self.miaosha_list_view = nil
	end

	if self.miaosha_top_gift_list then
		for k,v in pairs(self.miaosha_top_gift_list) do
			v:DeleteMe()
		end
		self.miaosha_top_gift_list = nil
	end

	self.top_btn_list = nil
	self.select_type = nil
	self.is_need_slider = nil
	self.is_clicked_miaosha = false

	CountDownManager.Instance:RemoveCountDown(tianshenroad_miaosha_act_countdown_key)
end

function TianshenRoadView:ShowIndexCallHeFuMiaoSha()
	self.is_clicked_miaosha = true
	for i=1,3 do
		local data = TianShenRoadMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(i)
		if data.shop_id ~= 0 then
			local shop_lib_cfg = TianShenRoadMiaoshaWGData.Instance:GetShopLibConfigByType(i, data.shop_id)

			if shop_lib_cfg then
				self.select_type = i
                self.top_btn_list[self.select_type].toggle.isOn = true
                TianShenRoadMiaoshaWGData.Instance:SetMiaoShaSelectType(self.select_type)
				break
			end
		end
	end
end

function TianshenRoadView:FlushHeFuMiaoSha()
    local cfg = TianShenRoadMiaoshaWGData.Instance:GetOtherCfg()
	self.miaosha_data = TianShenRoadMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(self.select_type)
	self:FlushBottomSlider()
	self:RefreshListView()
	self:FlushTopBtn()
end

--刷新listview
function TianshenRoadView:RefreshListView()
	local data_list = TianShenRoadMiaoshaWGData.Instance:GetHeFuMiaoShaListData(self.select_type)
	self.miaosha_list_view:SetDataList(data_list)
end

local list_progress = {0.2, 0.4, 0.6, 0.8, 1}
--刷新进度条
function TianshenRoadView:FlushBottomSlider()
	if not self.miaosha_data then
		return
	end
	local shop_lib_cfg = TianShenRoadMiaoshaWGData.Instance:GetShopLibConfigByType(self.select_type, self.miaosha_data.shop_id)
	if not shop_lib_cfg then
		self.node_list.miaosha_bottom_slider:SetActive(false)
		return
	end

	-- 赠送额度为0不展示进度条
	if IsEmptyTable(shop_lib_cfg.gift_item) then
		self.node_list.miaosha_bottom_slider:SetActive(false)
		self.is_need_slider = false
		return
	end
    local gift_quota_list_str = Split(shop_lib_cfg.gift_quota, ",")
    local gift_quota_list = {}
    for k, v in pairs(gift_quota_list_str) do
        gift_quota_list[k] = tonumber(v)
    end
	self.is_need_slider = true
	self.node_list.miaosha_bottom_slider:SetActive(true)
    local progress_idx = 0
    if self.miaosha_data.total_quota == 0 then
        self.node_list["miaosha_slider"].slider.value = list_progress[1]
    elseif self.miaosha_data.total_quota >= gift_quota_list[#gift_quota_list] then
        self.node_list["miaosha_slider"].slider.value = 1
    else
        for k, v in pairs(gift_quota_list) do
            if self.miaosha_data.total_quota < v then
                progress_idx = k - 1
                break
            end
        end
        local start = gift_quota_list[progress_idx] or 0
        local start_pro = list_progress[progress_idx -1] or 0
        local add_pergress = (list_progress[progress_idx] - start_pro) * (self.miaosha_data.total_quota - start) / (gift_quota_list[progress_idx + 1] -  start) 
        self.node_list["miaosha_slider"].slider.value = list_progress[progress_idx] + add_pergress
    end

	for i=1,5 do
		-- 物品
		if self.miaosha_top_gift_list[i] and shop_lib_cfg.gift_item[i - 1] then
			self.miaosha_top_gift_list[i]:SetData(shop_lib_cfg.gift_item[i - 1])
		end
		-- 额度
		if gift_quota_list[i] then
			local gift_quota = tonumber(gift_quota_list[i])
			self.node_list["miaosha_value_" .. i].text.text = gift_quota

			if self.miaosha_data.total_quota >= gift_quota and self.miaosha_data.quota_reward_tag[i] == 0 then
				--可领取未领取
				self.node_list["miaosha_effect_" .. i]:SetActive(true)
				self.miaosha_top_gift_list[i]:SetLingQuVisible(false)
			elseif self.miaosha_data.total_quota >= gift_quota and self.miaosha_data.quota_reward_tag[i] == 1 then
				--已领取
				self.node_list["miaosha_effect_" .. i]:SetActive(false)
				self.miaosha_top_gift_list[i]:SetLingQuVisible(true)
			else
				self.node_list["miaosha_effect_" .. i]:SetActive(false)
				self.miaosha_top_gift_list[i]:SetLingQuVisible(false)
			end
		end
	end
	self.node_list.miaosha_total_value.text.text = self.miaosha_data.total_quota

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA)

	if not activity_info or activity_info.end_time == -1 then
		return
	end
end


--专属类型切换
function TianshenRoadView:OnClickTopToggle(index, is_on)
	if is_on then
		if self.select_type == index then
			return
		end
		self.select_type = index
		TianShenRoadMiaoshaWGData.Instance:SetMiaoShaSelectType(self.select_type)
		self:FlushHeFuMiaoSha()
	end
end

--刷新顶部按钮
function TianshenRoadView:FlushTopBtn()
	if not self.miaosha_data then
		return
	end

	for i=1, TYPE_MAX do
		local data = TianShenRoadMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(i)
		if data.shop_id ~= 0 then
			local shop_lib_cfg = TianShenRoadMiaoshaWGData.Instance:GetShopLibConfigByType(i, data.shop_id)

			if shop_lib_cfg then
				self.node_list["miaosha_top_btn_" .. i]:SetActive(true)
				self.node_list["miaosha_top_btn_text_" .. i].text.text = shop_lib_cfg.type_name
				self.node_list["miaosha_top_btn_text_hl_" .. i].text.text = shop_lib_cfg.type_name
			else
				self.node_list["miaosha_top_btn_" .. i]:SetActive(false)
			end
		else
			self.node_list["miaosha_top_btn_" .. i]:SetActive(false)
		end

		self.node_list["miaosha_remind_" .. i]:SetActive(TianShenRoadMiaoshaWGData.Instance:GetMiaoShaRemindByType(i) == 1)
	end

	local time = self.miaosha_data.refresh_time - TimeWGCtrl.Instance:GetServerTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA)

	if not activity_info or activity_info.end_time == -1 then
		return
	end

	local end_time = activity_info.end_time - TimeWGCtrl.Instance:GetServerTime()
end

-- 累计奖励点击事件
function TianshenRoadView:OnclickGiftItem(index)
	if not self.miaosha_data then
		return
	end

	if not self.miaosha_data.quota_reward_tag[index] then
		return
	end

	local shop_lib_cfg = TianShenRoadMiaoshaWGData.Instance:GetShopLibConfigByType(self.select_type, self.miaosha_data.shop_id)

	if not shop_lib_cfg then
		return
	end

	if shop_lib_cfg.gift_item == 0 or shop_lib_cfg.gift_quota == 0 then
		return
	end

	local gift_quota_list = Split(shop_lib_cfg.gift_quota, ",")

	if not gift_quota_list[index] then
		return
	end

	if self.miaosha_data.total_quota >= tonumber(gift_quota_list[index]) and self.miaosha_data.quota_reward_tag[index] == 0 then
		--请求
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA, FA_TIMED_SPIKE_OPERA_TYPE.FA_TIMED_SPIKE_OPERA_TYPE_RECEIVE_GIFT, self.select_type, index - 1)

		if shop_lib_cfg and shop_lib_cfg.gift_item[index - 1] then
			local reward_list = {[1] = shop_lib_cfg.gift_item[index - 1]}
			TipWGCtrl.Instance:ShowGetReward(nil, reward_list, false)
		end
	else
		--提示
		TipWGCtrl.Instance:OpenItem(shop_lib_cfg.gift_item[index - 1])
	end

end

--有效时间倒计时
function TianshenRoadView:TSMSTimeCountDown()
	CountDownManager.Instance:RemoveCountDown(tianshenroad_miaosha_act_countdown_key)
	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_xianshi_miaosha)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list["ts_ms_time_label"].text.text = string.format(Language.TianShenRoadMiaoShaDesc.ActTime2, TimeUtil.FormatSecondDHM2(invalid_time - server_time))
		CountDownManager.Instance:AddCountDown(tianshenroad_miaosha_act_countdown_key, BindTool.Bind1(self.UpdateTSMSCountDown, self), BindTool.Bind1(self.TSMSTimeCountDown, self), invalid_time, nil, 1)
	else
		self.node_list["ts_ms_time_label"].text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
	end
end

function TianshenRoadView:UpdateTSMSCountDown(elapse_time, total_time)
	self.node_list["ts_ms_time_label"].text.text = string.format(Language.TianShenRoadMiaoShaDesc.ActTime2, TimeUtil.FormatSecondDHM2(total_time - elapse_time))
end

-----------------------------------------------------------TianShenRoadMiaoShaCell-------------------------------------------------------------------------------

TianShenRoadMiaoShaCell = TianShenRoadMiaoShaCell or BaseClass(BaseGridRender)

function TianShenRoadMiaoShaCell:LoadCallBack()
	self.node_list.buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self))

	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function TianShenRoadMiaoShaCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TianShenRoadMiaoShaCell:OnFlush()
	if not self.data then
		return
	end
	local select_type = TianShenRoadMiaoshaWGData.Instance:GetMiaoShaSelectType()

	local item_lib_cfg = TianShenRoadMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)
	if not item_lib_cfg then
		return
	end

	self.node_list.original_price_value.text.text = item_lib_cfg.original_price
	self.node_list.original_money_icon.image:LoadSprite(ResPath.GetCommonIcon(MONNEY_TYPE[select_type]))
	self.node_list.original_money_icon.image:SetNativeSize()

	self.node_list.cur_money_icon.image:LoadSprite(ResPath.GetCommonIcon(MONNEY_TYPE[select_type]))
	self.node_list.cur_money_icon.image:SetNativeSize()

    self.node_list.cur_price_value.text.text = item_lib_cfg.current_price
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_lib_cfg.item_id) 
    if IsEmptyTable(item_cfg) then
        print_error("天神之路--秒杀取不到物品配置 item_id =", item_lib_cfg.item_id)
    end
	self.item_cell:SetData({item_id = item_lib_cfg.item_id, num = 1, is_bind = item_lib_cfg.is_bind})
	self.node_list["item_name"].text.text = item_cfg.name

	local discount = NumberToChinaNumber(item_lib_cfg.discount)
	self.node_list.discount_desc.text.text =  string.format(Language.TianShenRoadMiaoShaDesc.DisCount, discount)

	local vip_level = VipWGData.Instance:IsVip() and RoleWGData.Instance.role_vo.vip_level or 0
	local role_level = RoleWGData.Instance:GetRoleLevel()

	if vip_level < item_lib_cfg.vip_level_limit then
		self.node_list.sellout_flag:SetActive(false)
		self.node_list.buy_btn:SetActive(false)
		self.node_list.limit_desc:SetActive(true)
		self.node_list.limit_desc.text.text = string.format(Language.TianShenRoadMiaoShaDesc.LimitDesc1, item_lib_cfg.vip_level_limit)
	elseif role_level < item_lib_cfg.level_limit then
		self.node_list.sellout_flag:SetActive(false)
		self.node_list.buy_btn:SetActive(false)
		self.node_list.limit_desc:SetActive(true)
		self.node_list.limit_desc.text.text = string.format(Language.TianShenRoadMiaoShaDesc.LimitDesc2, item_lib_cfg.level_limit)
	else
		local can_buy = self.data.last_num > 0 and self.data.bought_times < item_lib_cfg.buy_limit_value
		self.node_list.limit_desc:SetActive(false)
		self.node_list.buy_btn:SetActive(can_buy)
		self.node_list.sellout_flag:SetActive(not can_buy)
	end

	local new_falg = TianShenRoadMiaoshaWGData.Instance:GetNewFlagByShopId(self.data.id) or 0

	local str = string.format(Language.TianShenRoadMiaoShaDesc.PresonCount, self.data.bought_times, item_lib_cfg.buy_limit_value)
	self.node_list.limit_bg:SetActive(item_lib_cfg.buy_limit_type == 1 or item_lib_cfg.buy_limit_type == 5)

	local color = self.data.bought_times >= item_lib_cfg.buy_limit_value and COLOR3B.RED or "#5D3C2B"

	self.node_list.limit_count_desc.text.text = ToColorStr(str, color)
	self.item_cell:SetBindIconVisible(false)
	self.item_cell:SetRightBottomTextVisible(false)

	self.node_list.top_bg:SetActive(item_lib_cfg.discount > 0)
end

function TianShenRoadMiaoShaCell:OnClickBuyBtn()
	local item_lib_cfg = TianShenRoadMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)
	if not item_lib_cfg then
		return
	end
   
    local buy_func = function(num)
        local select_type = TianShenRoadMiaoshaWGData.Instance:GetMiaoShaSelectType()
        local hefu_miaosha_data = TianShenRoadMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(select_type)
        if not hefu_miaosha_data then
            return
        end
        local item_lib_cfg = TianShenRoadMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)
        if not item_lib_cfg then
            return
        end
        local state = TianshenRoadWGData.Instance:GetActivityState(TabIndex.tianshenroad_xianshi_miaosha)
        if not state then
            TipWGCtrl.Instance:ShowSystemMsg(Language.TianShenRoadMiaoShaDesc.ActCloseTips)
            return
        end

        local shop_lib_cfg = TianShenRoadMiaoshaWGData.Instance:GetShopLibConfigByType(select_type, hefu_miaosha_data.shop_id)
        if not shop_lib_cfg then
            return
        end
        
        local cur_price = item_lib_cfg.current_price
        local select_type = TianShenRoadMiaoshaWGData.Instance:GetMiaoShaSelectType()

		if select_type == 1 then	--灵玉
			ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA, FA_TIMED_SPIKE_OPERA_TYPE.FA_TIMED_SPIKE_OPERA_TYPE_BUY, hefu_miaosha_data.shop_id, self.data.id, num)
        elseif select_type == 2 then --元宝
            local gold = RoleWGData.Instance:GetRoleInfo().gold
            local bind_gold = RoleWGData.Instance:GetRoleInfo().bind_gold
            if bind_gold < cur_price and (bind_gold + gold) >= cur_price then
                local ok_func = function()
                    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA, FA_TIMED_SPIKE_OPERA_TYPE.FA_TIMED_SPIKE_OPERA_TYPE_BUY, hefu_miaosha_data.shop_id, self.data.id, num)
                end
                TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
                return
            end
		elseif select_type == 3 then --积分
			local has_num = YanYuGeWGData.Instance:GetCurScore()
			if has_num >= cur_price then
				ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA, FA_TIMED_SPIKE_OPERA_TYPE.FA_TIMED_SPIKE_OPERA_TYPE_BUY, hefu_miaosha_data.shop_id, self.data.id, num)
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.NoEnoughScore)
			end
        end
    end

    if item_lib_cfg.buy_limit_value - self.data.bought_times > 1 then
        --通用的批量购买
        HeFuMiaoShaWGCtrl.Instance:OpenBatchBuyView(item_lib_cfg.item_id, 1, 1,  item_lib_cfg.buy_limit_value - self.data.bought_times, buy_func)
    else
        buy_func(1)
    end
end