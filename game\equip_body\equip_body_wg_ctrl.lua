require("game/equip_body/equip_body_wg_data")
require("game/equip_body/equip_body_tupo_view")

EquipBodyWGCtrl = EquipBodyWGCtrl or BaseClass(BaseWGCtrl)

function EquipBodyWGCtrl:__init()
    if EquipBodyWGCtrl.Instance then
		print_error("[EquipBodyWGCtrl] Attemp to create a singleton twice !")
	end

	EquipBodyWGCtrl.Instance = self

    self.data = EquipBodyWGData.New()
    self.tupo_view = RoleEquipBodyTuPoView.New()

    self:RegisterProtocol(CSEquipBodyOperate)
    self:RegisterProtocol(SCEquipBodyBaseInfo, "OnEquipBodyBaseInfo")
end

function EquipBodyWGCtrl:__delete()
    EquipBodyWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.tupo_view then
        self.tupo_view:DeleteMe()
        self.tupo_view = nil
    end
end

-- 请求升级装备肉身
function EquipBodyWGCtrl:SendEquipBodyOperate(operate_type, param1, param2, param3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSEquipBodyOperate)
    protocol.operate_type = operate_type or 0
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
    protocol:EncodeAndSend()
end

function EquipBodyWGCtrl:OnEquipBodyBaseInfo(protocol)
    local has_active_equip_body = self.data:SetEquipBodyBaseInfo(protocol)

    RemindManager.Instance:Fire(RemindName.EquipBodyTuPo)

    if self.tupo_view and self.tupo_view:IsOpen() then
        if has_active_equip_body then
            self.tupo_view:Flush(0, "has_active_equip_body")
        else
            self.tupo_view:Flush()
        end
    end
end

function EquipBodyWGCtrl:SetDataAndOpen(data)
    if self.tupo_view then
        self.tupo_view:SetDataAndOpen(data)
    end
end