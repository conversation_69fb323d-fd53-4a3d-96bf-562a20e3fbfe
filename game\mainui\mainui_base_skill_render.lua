MainUIBaseSkillRender = MainUIBaseSkillRender or BaseClass(BaseRender)
function MainUIBaseSkillRender:__init()
	self.data = self:CreateSkillVo()
	if self.view.event_trigger_listener then
		self.view.event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnPointerUp, self))
		self.view.event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDrag, self))
		self.view.event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnPointerDown, self))
		self.view.event_trigger_listener:AddPointerExitListener(BindTool.Bind(self.OnPointerExit, self))
	else
		self.view.button:AddClickListener(BindTool.Bind(self.OnClickSkill, self))
	end
end

function MainUIBaseSkillRender:__delete()

end

function MainUIBaseSkillRender:CreateSkillVo()
	return {
		is_lock = false,
		skill_id = 0,
		duan_atk = 1,
		skill_index = 0,
	}
end

function MainUIBaseSkillRender:SetParent(parent)
	self.parent = parent

    if self.parent then
		self:SetPointerUpCallBack(BindTool.Bind(self.parent.OnSkillUp, self.parent))
		self:SetDragCallBack(BindTool.Bind(self.parent.OnDragSkill, self.parent))
		self:SetPointerDownCallBack(BindTool.Bind(self.parent.OnClickRangSkill, self.parent))
		self:SetPointerExitCallBack(BindTool.Bind(self.parent.CheckRangState, self.parent))
		self:SetClickSkillCallBack(BindTool.Bind(self.parent.OnClickSkill, self.parent))
    end
end

function MainUIBaseSkillRender:SetData(data)
    self.skill_data = data
    if self.has_load or self.is_use_objpool then
		self:OnFlush()
	else
		self:Flush()
	end
end

function MainUIBaseSkillRender:SetPointerUpCallBack(call_back)
	self.pointer_up_cb = call_back
end

function MainUIBaseSkillRender:SetDragCallBack(call_back)
	self.drag_cb = call_back
end

function MainUIBaseSkillRender:SetPointerDownCallBack(call_back)
	self.pointer_down_cb = call_back
end

function MainUIBaseSkillRender:SetPointerExitCallBack(call_back)
	self.pointer_exit_cb = call_back
end

function MainUIBaseSkillRender:SetClickSkillCallBack(call_back)
	self.click_skill_cb = call_back
end

function MainUIBaseSkillRender:OnPointerUp()
	if self.pointer_up_cb then
		self.pointer_up_cb(self.data)
	end
end

function MainUIBaseSkillRender:OnDrag(eventData)
	if self.drag_cb then
		self.drag_cb(self.data, eventData)
	end
end

function MainUIBaseSkillRender:OnPointerDown()
	if self.pointer_down_cb then
		self.pointer_down_cb(self.data)
	end
end

function MainUIBaseSkillRender:OnPointerExit()
	if self.pointer_exit_cb then
		self.pointer_exit_cb(self.data)
	end
end

function MainUIBaseSkillRender:OnClickSkill()
	if self.click_skill_cb then
		self.click_skill_cb(self.data)
	end
end

-- 沉默
function MainUIBaseSkillRender:FlushForbidStatus(is_show)
	if self.node_list.forbid then
		self.node_list.forbid:CustomSetActive(is_show)
	end
end






MainUIBaseCDSkillRender = MainUIBaseCDSkillRender or BaseClass(MainUIBaseSkillRender)
function MainUIBaseCDSkillRender:__delete()
    self:RemoveCountDownTimer()
end

-- 刷新
function MainUIBaseCDSkillRender:OnFlush()
    if IsEmptyTable(self.skill_data) then
		self:SetSkillCD(0)
		self.view:SetActive(false)
		return
    end

    local skill_id = self.skill_data.skill_id
    local skill_index = self.skill_data.index
    self.data.skill_index = skill_index
    self:UpdateCellData(skill_id, 1)

    --判断是否在cd中
    local skill_end_time = SkillWGData.Instance:GetSkillCDEndTime(skill_index)
    if skill_end_time > Status.NowTime * 1000 then -- 还在cd中
        self:SetSkillCD(skill_end_time - Status.NowTime * 1000)
    end

	-- 技能类型
	local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(skill_id)
	if skill_type_tab and skill_type_tab[1] and tonumber(skill_type_tab[1]) ~= 0 then
		self.node_list.skill_type:SetActive(true)
		self.node_list.skill_type.text.text = Language.Skill.CommonSkillType[skill_type_tab[1]] or ""
	else
		self.node_list.skill_type:SetActive(false)
	end

	self.view:SetActive(true)
end

function MainUIBaseCDSkillRender:UpdateCellData(skill_id, duan_atk)
	if self.data.skill_id == skill_id and self.data.duan_atk == duan_atk then
		return
	end

	self.data.skill_id = skill_id
	self.data.duan_atk = duan_atk
	local icon_id = SkillWGData.Instance:GetSkillIconId(skill_id, duan_atk)
	local bundle, name = ResPath.GetSkillIconById(icon_id)
	self.node_list.icon.image:LoadSprite(bundle, name)
end

----[[--设置技能CD
function MainUIBaseCDSkillRender:RemoveCountDownTimer()
	if self.count_down then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

function MainUIBaseCDSkillRender:RemoveCountDown(is_update)
	if not is_update then
		if self.node_list.cd_mark then
			self.node_list.cd_mark:SetActive(false)
			self.node_list.cd_mark.image.fillAmount = 0
		end

		if self.node_list.cd_text then
			self.node_list.cd_text:SetActive(false)
		end
	end

	self:RemoveCountDownTimer()
end

function MainUIBaseCDSkillRender:SetSkillCD(cd)
	local skill_id = self.data.skill_id

	if self.count_down and
		(self.count_down.total_time - self.count_down.elapse_time * 1000) ~= cd then
		self:RemoveCountDown(true)
	end

	if not self.count_down then
		self.node_list.cd_mark:SetActive(true)
		local is_skill_cd = cd > COMMON_CONSTS.SKILL_GLOBAL_CD
		local fenduan_time = self:JustShowBlackMark(skill_id, cd)

		-- 爆点特效
		if is_skill_cd then
			local bundle_name1, asset_name1 = ResPath.GetEffectUi(Ui_Effect.UI_jiNeng_up)
			EffectManager.Instance:PlayAtTransform(bundle_name1, asset_name1, self.node_list.baodian_eff.transform, 0.5)
		end

		local function timer_func(elapse_time, total_time)
			local hao_miao = 1000
			elapse_time = elapse_time * hao_miao
			if 0 ~= fenduan_time and elapse_time <= fenduan_time then
				self.node_list.cd_mark.image.fillAmount = 1
				self.node_list.cd_text:SetActive(false)
				return
			end

			self.node_list.cd_text:SetActive(true)
			elapse_time = elapse_time - fenduan_time
			total_time = total_time - fenduan_time

			if total_time - elapse_time > hao_miao then
				local fill_amount = 1 - (elapse_time / hao_miao) / (total_time / hao_miao)
				self.node_list.cd_mark.image.fillAmount = fill_amount
				self.node_list.cd_text.text.text = (math.ceil((total_time - elapse_time) / hao_miao))
			else
				local fill_amount = 1 - (elapse_time / hao_miao) / (total_time / hao_miao)
				self.node_list.cd_mark.image.fillAmount = fill_amount
				self.node_list.cd_text.text.text = string.format("%.1f", (total_time - elapse_time) / hao_miao)
			end

			if elapse_time >= total_time then
				self:RemoveCountDown()
				return
			end
		end

		self.count_down = CountDown.Instance:AddCountDown(cd, 0.05, timer_func)
	end
end

-- 特殊需求，多段伤害技能前期，只制灰，不跑CD
function MainUIBaseCDSkillRender:JustShowBlackMark(skill_id, cd)
	local fenduan_skill_cfg = SkillWGData.Instance:GetFenDuanSkill(skill_id)
	if not fenduan_skill_cfg then
		return 0
	end

	local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
	if not skill_cfg or cd < skill_cfg.cd_s then
    	return 0
    end

    -- if cd < skill_cfg.cd_s or skill_cfg.cd_s - cd < time then return 0 end
    return fenduan_skill_cfg.total_time
end
--]]










MainUIHaloSkillRender = MainUIHaloSkillRender or BaseClass(MainUIBaseSkillRender)
function MainUIHaloSkillRender:__init()
	self.old_cur_count = nil
end

function MainUIHaloSkillRender:__delete()
    self.old_cur_count = nil
end

-- 刷新
function MainUIHaloSkillRender:OnFlush()
    if IsEmptyTable(self.skill_data) then
		self.view:SetActive(false)
		return
    end

    local skill_id = self.skill_data.skill_id
    local skill_index = self.skill_data.index
    self.data.skill_index = skill_index
    self:UpdateCellData(skill_id, 1)
	self:FlushCountSlider()
	self.view:SetActive(true)
end

function MainUIHaloSkillRender:UpdateCellData(skill_id, duan_atk)
	if self.data.skill_id == skill_id and self.data.duan_atk == duan_atk then
		return
	end

	self.data.skill_id = skill_id
	self.data.duan_atk = duan_atk
	local icon_id = SkillWGData.Instance:GetSkillIconId(skill_id, duan_atk)
	local bundle, name = ResPath.GetSkillIconById(icon_id)
	self.node_list.icon.image:LoadSprite(bundle, name)

	-- 技能类型
	local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(skill_id)
	if self.node_list.skill_type ~= nil then	-- 加个容错
		if skill_type_tab and skill_type_tab[1] and tonumber(skill_type_tab[1]) ~= 0 then
			self.node_list.skill_type:SetActive(true)
			self.node_list.skill_type.text.text = Language.Skill.CommonSkillType[skill_type_tab[1]] or ""
		else
			self.node_list.skill_type:SetActive(false)
		end
	end
end

function MainUIHaloSkillRender:FlushCountSlider()
	local cur_count = FiveElementsWGData.Instance:GetCurHaloSkillAttackNum()
	local need_num = FiveElementsWGData.Instance:GetHaloSkillAttackNeedNum()
	local is_meet = cur_count >= need_num
	self.node_list.atk_count_text.text.text = need_num - cur_count
	self.node_list.atk_count_progress.slider.value = (need_num - cur_count) / need_num
	self.node_list.atk_count_progress:CustomSetActive(not is_meet)
	self.node_list.halo_skill_effect:CustomSetActive(is_meet)

	if self.old_cur_count and self.old_cur_count == need_num and cur_count < self.old_cur_count then
		self:DoBaoDianEffect()
	end

	self.old_cur_count = cur_count
end

function MainUIHaloSkillRender:DoBaoDianEffect()
	if self.node_list.baodian_eff and self.node_list.baodian_eff:GetActive() then
		local bundle_name1, asset_name1 = ResPath.GetEffectUi(Ui_Effect.UI_jiNeng_up)
		EffectManager.Instance:PlayAtTransform(bundle_name1, asset_name1, self.node_list.baodian_eff.transform, 0.5)
	end
end









MainUIEsotericaSkillRender = MainUIEsotericaSkillRender or BaseClass(BaseRender)
function MainUIEsotericaSkillRender:__init()
	self.view.button:AddClickListener(BindTool.Bind(self.OnClickSkill, self))
end

function MainUIEsotericaSkillRender:__delete()
	self:RemoveCountDownTimer()
end

function MainUIEsotericaSkillRender:SetClickSkillCallBack(call_back)
	self.click_skill_cb = call_back
end

function MainUIEsotericaSkillRender:RemoveCountDownTimer()
	if self.count_down then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

function MainUIEsotericaSkillRender:OnFlush()
	self.node_list.esoterica_skill_effect:CustomSetActive(false)
	self:RemoveCountDownTimer()
	if not self.data then
		self.view:SetActive(false)
		return
	end

	self.view:SetActive(true)
	local icon_id = SkillWGData.Instance:GetSkillIconId(self.data.skill_id)
	local bundle, asset = ResPath.GetSkillIconById(icon_id)
	self.node_list.icon.image:LoadSprite(bundle, asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	local slot_cfg = CultivationWGData.Instance:GetEsotericaCfgBySkillId(self.data.skill_id)
	if not slot_cfg then return end
	local slot_skill_cfg = CultivationWGData.Instance:GetEsotericaSkillLevelCfg(slot_cfg.seq, self.data.skill_level)
	if not slot_skill_cfg then return end

	local rest_time = self.data.end_time - TimeWGCtrl.Instance:GetServerTime()
	local sk_total_time = slot_skill_cfg.skill_time
	if rest_time > 0 then
		local cur_server_time = TimeWGCtrl.Instance:GetServerTime()
		self.node_list.progress.slider.value = (self.data.end_time - cur_server_time) / sk_total_time
		self.count_down = CountDown.Instance:AddCountDown(rest_time, 0.05,
			function (elapse_time, total_time)
				if self.data and self.node_list.progress then
					local cur_time = TimeWGCtrl.Instance:GetServerTime()
					self.node_list.progress.slider.value = (self.data.end_time - cur_time) / sk_total_time
				end
			end,

			function()
				self.view:SetActive(false)
			end
		)
	else
		self.node_list.progress.slider.value = 0
	end

	bundle, asset = ResPath.GetRawImagesPNG("miji_name_" .. slot_cfg.main_skill_img)
    self.node_list.skill_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["skill_img"].raw_image:SetNativeSize()
    end)

	bundle, asset = ResPath.GetUIEffect(slot_cfg.skill_show_ui_effect)
	self.node_list["show_skill_effect"]:ChangeAsset(bundle, asset)
end

function MainUIEsotericaSkillRender:OnClickSkill()
	if self.click_skill_cb then
		self.click_skill_cb(self.data)
	end
end

function MainUIEsotericaSkillRender:PlayUseEsotericaSkillEffect(seq)
	local cfg = CultivationWGData.Instance:GetEsotericaCfg(seq)
	if not cfg then
		return
	end

	self.node_list.esoterica_skill_effect:CustomSetActive(true)
	local use_effect_bundle, use_effect_asset = ResPath.GetEffectUi(cfg.esoterica_skill_use_effect)
	self.node_list.esoterica_skill_effect:ChangeAsset(use_effect_bundle, use_effect_asset)
end