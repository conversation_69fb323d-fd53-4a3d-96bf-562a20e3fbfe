--塔防副本下侧
DefenseFbTowerReformView = DefenseFbTowerReformView or BaseClass(SafeBaseView)

function DefenseFbTowerReformView:__init()
	self:AddViewResource(0, "uis/view/defense_fb_ui_prefab", "layout_tower_updata_view")
	self:SetMaskBg(true,true)
	-- self:SetModal(true)
	-- self:SetIaAnyClickClose(true)
	-- self:SetBgOpacity(150)
end

function DefenseFbTowerReformView:__delete()

end

function DefenseFbTowerReformView:ReleaseCallBack()
	self.defense_pos_list = nil
end

function DefenseFbTowerReformView:LoadCallBack()
	self.mask_bg.image.color = Color.New(0,0,0,0)
	XUI.AddClickEventListener(self.node_list.btn_tower_updata_0, BindTool.Bind(self.OnClickDefenseBtn, self, BUILD_TOWER_OPERA_TYPE.BUILD_TOWER_OPERA_TYPE_UPGRADE, 0))
	XUI.AddClickEventListener(self.node_list.btn_tower_updata_1, BindTool.Bind(self.OnClickDefenseBtn, self, BUILD_TOWER_OPERA_TYPE.BUILD_TOWER_OPERA_TYPE_REMOVE, 0))
end

function DefenseFbTowerReformView:CloseCallBack()
	GlobalEventSystem:Fire(ObjectEventType.TOWER_ATTACK_RANGE)
	self.pos_index = nil
end

function DefenseFbTowerReformView:ShowIndexCallBack(index)
	self:Flush()
end

function DefenseFbTowerReformView:SetTargetObjData(pos_index)
	-- if self.defense_pos_list == nil then
	-- 	self.defense_pos_list = FuBenWGCtrl.Instance.defense_fb_data:GetDefensePosList()
	-- end
	self.pos_index = pos_index

	-- for k,v in ipairs(self.defense_pos_list) do
	-- 	if v.pos_x == target_vo.pos_x and v.pos_y == target_vo.pos_y then
	-- 		self.pos_index = v.pos_index
	-- 		break
	-- 	end
	-- end
end

function DefenseFbTowerReformView:OnFlush(param_list, index)
	if self.pos_index == nil then return end

	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	local defense_douhun = defense_data.douhun or 0

	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	local tower_info_list = defense_data.tower_info_list

	if tower_info_list[self.pos_index] == nil then
		return
	end

	local defense_tower = tower_info_list[self.pos_index]
	local now_defense, next_defense = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerCfg(defense_tower.tower_type, defense_tower.tower_level)

	local monster_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[now_defense.monster_id]
	if nil ~= monster_config then
		self.node_list.lbl_defense_tower_name.text.text = monster_config.name
	end

	if next_defense ~= nil then
		self.node_list.btn_tower_updata_0:SetActive(true)
		local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo() 
		local color = defense_data.douhun < next_defense.need_douhun and COLOR3B.RED or COLOR3B.WHITE
		self.node_list.lbl_tower_updata_0.text.text = ToColorStr(defense_data.douhun .. "/" .. next_defense.need_douhun,color) 
		self.node_list.lbl_defense_tower_text_2.text.text = next_defense.instruction
	else
		self.node_list.btn_tower_updata_0:SetActive(false)
		self.node_list.lbl_tower_updata_0.text.text = Language.DefenseFb.TowerMaxLevel
		self.node_list.lbl_defense_tower_text_2.text.text = Language.DefenseFb.TowerMaxLevel
	end

	if now_defense ~= nil then
		self.node_list.lbl_tower_updata_1.text.text = now_defense.return_douhun
		self.node_list.lbl_defense_tower_text_1.text.text = now_defense.instruction
	end
end

function DefenseFbTowerReformView:OnClickDefenseBtn(operate_type, param2)
	if self.pos_index == nil then return end
	FuBenWGCtrl.Instance:SendBuildTowerReq(operate_type, self.pos_index, param2)
	if operate_type == BUILD_TOWER_OPERA_TYPE.BUILD_TOWER_OPERA_TYPE_REMOVE then
		self:Close()
	end
end