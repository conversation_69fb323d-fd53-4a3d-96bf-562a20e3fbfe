MainBeAtkIcon = MainBeAtkIcon or BaseClass(BaseRender)

function MainBeAtkIcon:__init()
	self.be_atked_icon = self.root_node

	self.atk_icon_show_time = 0
	self.role_vo = nil

	self.node_list["BtnBeAtk"].button:AddClickListener(BindTool.Bind(self.ClickBeAtk, self))
	self.get_ui_callback = BindTool.Bind(self.GetUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.MainUIView, self.get_ui_callback)
	if not self.obj_leave_event then
		self.obj_leave_event = GlobalEventSystem:Bind(SceneEventType.OBJ_LEVEL_ROLE, BindTool.Bind(self.OnObjLeave, self))
	end
	if not self.obj_dead_event then
		self.obj_dead_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnMonsterDead, self))
	end

end

function MainBeAtkIcon:__delete()
	if nil ~= self.atta_kced then
		GlobalTimerQuest:CancelQuest(self.atta_kced)
		self.atta_kced = nil
	end

	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.MainUIView, self.get_ui_callback)
	end
	self.get_ui_callback = nil

	if self.obj_leave_event then
		GlobalEventSystem:UnBind(self.obj_leave_event)
		self.obj_leave_event = nil
	end

	if self.obj_dead_event then
		GlobalEventSystem:UnBind(self.obj_dead_event)
		self.obj_dead_event = nil
	end
end

function MainBeAtkIcon:SetData(role_vo)
	if self.atk_icon_show_time > Status.NowTime then
		return
	end
	self.role_vo = role_vo
	if self.node_list["Image"] and self.node_list["RawImage"] then
		local role_id  = role_vo.origin_uid > 0 and role_vo.origin_uid or role_vo.role_id
		XUI.UpdateRoleHead(self.node_list["Image"], self.node_list["RawImage"],role_id,role_vo.sex,role_vo.prof,false)
	end
	self.node_list["atk_role_name"].text.text = role_vo.name
	self:SetBeAtkIconState(role_vo)
end

function MainBeAtkIcon:SetShowImage(is_show)
	self.node_list["Image"]:SetActive(is_show)
	self.node_list["RawImage"]:SetActive(not is_show)
end

function MainBeAtkIcon:SetBeAtkIconState(role_vo)
	self:SetActive(true)
end

function MainBeAtkIcon:UpdateAtkIconTime()
	if self.atk_icon_show_time <= Status.NowTime then
		if nil ~= self.be_attakced_update_t then
			GlobalTimerQuest:CancelQuest(self.be_attakced_update_t)
			self.be_attakced_update_t = nil
			self:SetActive(false)
		end
	end
end

function MainBeAtkIcon:ClickBeAtk()
	-- self:SetActive(false)
	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId()
	--差不可攻击的情况判断
	if scene_type == SceneType.Common then
		MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.ALL)
		print_error(">>>差一种模式暂时切换成全体模式<<<")
	end
	if nil ~= self.atta_kced then
		GlobalTimerQuest:CancelQuest(self.atta_kced)
		self.atta_kced = nil
	end
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	self.atta_kced = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.AttaKced, self), 0.5)


end

function MainBeAtkIcon:AttaKced()
	if self.role_vo ~= nil then
		local target_obj = Scene.Instance:GetObjectByObjId(self.role_vo.obj_id)
		if target_obj then
			GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, "scene")
		end
	end
	if nil ~= self.atta_kced then
		GlobalTimerQuest:CancelQuest(self.atta_kced)
		self.atta_kced = nil
	end
end

function MainBeAtkIcon:GetUiCallBack(ui_name, ui_param)
	if self[ui_name] then
		if self[ui_name].gameObject.activeInHierarchy then
			return self[ui_name]
		end
	end

	return nil
end

function MainBeAtkIcon:OnObjLeave(obj_id)
	if not self.role_vo then
		return
	end
	if obj_id ~= nil and self.role_vo.obj_id ~= nil and obj_id == self.role_vo.obj_id then
		self:SetActive(false)
	end
end


function MainBeAtkIcon:OnMonsterDead(monster_obj)
	if not self.role_vo then
		return
	end
	if monster_obj ~= nil and monster_obj.vo ~= nil and monster_obj.vo.obj_id ~= nil and self.role_vo.obj_id ~= nil and monster_obj.vo.obj_id == self.role_vo.obj_id then
		self:SetActive(false)
	end
end
