﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_SpriteWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(UnityEngine.Sprite), typeof(UnityEngine.Object));
		<PERSON><PERSON>unction("GetPhysicsShapeCount", GetPhysicsShapeCount);
		<PERSON><PERSON>unction("GetPhysicsShapePointCount", GetPhysicsShapePointCount);
		<PERSON><PERSON>unction("GetPhysicsShape", GetPhysicsShape);
		<PERSON><PERSON>Function("OverridePhysicsShape", OverridePhysicsShape);
		<PERSON><PERSON>unction("OverrideGeometry", OverrideGeometry);
		<PERSON><PERSON>unction("Create", Create);
		L.RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("bounds", get_bounds, null);
		<PERSON><PERSON>("rect", get_rect, null);
		<PERSON><PERSON>("border", get_border, null);
		<PERSON><PERSON>("texture", get_texture, null);
		<PERSON><PERSON>("pixelsPerUnit", get_pixelsPerUnit, null);
		L.RegVar("spriteAtlasTextureScale", get_spriteAtlasTextureScale, null);
		L.RegVar("associatedAlphaSplitTexture", get_associatedAlphaSplitTexture, null);
		L.RegVar("pivot", get_pivot, null);
		L.RegVar("packed", get_packed, null);
		L.RegVar("packingMode", get_packingMode, null);
		L.RegVar("packingRotation", get_packingRotation, null);
		L.RegVar("textureRect", get_textureRect, null);
		L.RegVar("textureRectOffset", get_textureRectOffset, null);
		L.RegVar("vertices", get_vertices, null);
		L.RegVar("triangles", get_triangles, null);
		L.RegVar("uv", get_uv, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPhysicsShapeCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)ToLua.CheckObject(L, 1, typeof(UnityEngine.Sprite));
			int o = obj.GetPhysicsShapeCount();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPhysicsShapePointCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)ToLua.CheckObject(L, 1, typeof(UnityEngine.Sprite));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int o = obj.GetPhysicsShapePointCount(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPhysicsShape(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)ToLua.CheckObject(L, 1, typeof(UnityEngine.Sprite));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			System.Collections.Generic.List<UnityEngine.Vector2> arg1 = (System.Collections.Generic.List<UnityEngine.Vector2>)ToLua.CheckObject(L, 3, typeof(System.Collections.Generic.List<UnityEngine.Vector2>));
			int o = obj.GetPhysicsShape(arg0, arg1);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OverridePhysicsShape(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)ToLua.CheckObject(L, 1, typeof(UnityEngine.Sprite));
			System.Collections.Generic.IList<UnityEngine.Vector2[]> arg0 = (System.Collections.Generic.IList<UnityEngine.Vector2[]>)ToLua.CheckObject<System.Collections.Generic.IList<UnityEngine.Vector2[]>>(L, 2);
			obj.OverridePhysicsShape(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OverrideGeometry(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)ToLua.CheckObject(L, 1, typeof(UnityEngine.Sprite));
			UnityEngine.Vector2[] arg0 = ToLua.CheckStructArray<UnityEngine.Vector2>(L, 2);
			ushort[] arg1 = ToLua.CheckNumberArray<ushort>(L, 3);
			obj.OverrideGeometry(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Create(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 1, typeof(UnityEngine.Texture2D));
				UnityEngine.Rect arg1 = StackTraits<UnityEngine.Rect>.Check(L, 2);
				UnityEngine.Vector2 arg2 = ToLua.ToVector2(L, 3);
				UnityEngine.Sprite o = UnityEngine.Sprite.Create(arg0, arg1, arg2);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 1, typeof(UnityEngine.Texture2D));
				UnityEngine.Rect arg1 = StackTraits<UnityEngine.Rect>.Check(L, 2);
				UnityEngine.Vector2 arg2 = ToLua.ToVector2(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.Sprite o = UnityEngine.Sprite.Create(arg0, arg1, arg2, arg3);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 1, typeof(UnityEngine.Texture2D));
				UnityEngine.Rect arg1 = StackTraits<UnityEngine.Rect>.Check(L, 2);
				UnityEngine.Vector2 arg2 = ToLua.ToVector2(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				uint arg4 = (uint)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.Sprite o = UnityEngine.Sprite.Create(arg0, arg1, arg2, arg3, arg4);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 6)
			{
				UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 1, typeof(UnityEngine.Texture2D));
				UnityEngine.Rect arg1 = StackTraits<UnityEngine.Rect>.Check(L, 2);
				UnityEngine.Vector2 arg2 = ToLua.ToVector2(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				uint arg4 = (uint)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.SpriteMeshType arg5 = (UnityEngine.SpriteMeshType)ToLua.CheckObject(L, 6, typeof(UnityEngine.SpriteMeshType));
				UnityEngine.Sprite o = UnityEngine.Sprite.Create(arg0, arg1, arg2, arg3, arg4, arg5);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 7)
			{
				UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 1, typeof(UnityEngine.Texture2D));
				UnityEngine.Rect arg1 = StackTraits<UnityEngine.Rect>.Check(L, 2);
				UnityEngine.Vector2 arg2 = ToLua.ToVector2(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				uint arg4 = (uint)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.SpriteMeshType arg5 = (UnityEngine.SpriteMeshType)ToLua.CheckObject(L, 6, typeof(UnityEngine.SpriteMeshType));
				UnityEngine.Vector4 arg6 = ToLua.ToVector4(L, 7);
				UnityEngine.Sprite o = UnityEngine.Sprite.Create(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 8)
			{
				UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 1, typeof(UnityEngine.Texture2D));
				UnityEngine.Rect arg1 = StackTraits<UnityEngine.Rect>.Check(L, 2);
				UnityEngine.Vector2 arg2 = ToLua.ToVector2(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				uint arg4 = (uint)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.SpriteMeshType arg5 = (UnityEngine.SpriteMeshType)ToLua.CheckObject(L, 6, typeof(UnityEngine.SpriteMeshType));
				UnityEngine.Vector4 arg6 = ToLua.ToVector4(L, 7);
				bool arg7 = LuaDLL.luaL_checkboolean(L, 8);
				UnityEngine.Sprite o = UnityEngine.Sprite.Create(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Sprite.Create");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Bounds ret = obj.bounds;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bounds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rect(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Rect ret = obj.rect;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rect on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_border(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Vector4 ret = obj.border;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index border on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_texture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Texture2D ret = obj.texture;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index texture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pixelsPerUnit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			float ret = obj.pixelsPerUnit;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pixelsPerUnit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spriteAtlasTextureScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			float ret = obj.spriteAtlasTextureScale;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteAtlasTextureScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_associatedAlphaSplitTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Texture2D ret = obj.associatedAlphaSplitTexture;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index associatedAlphaSplitTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pivot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Vector2 ret = obj.pivot;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pivot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_packed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			bool ret = obj.packed;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index packed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_packingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.SpritePackingMode ret = obj.packingMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index packingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_packingRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.SpritePackingRotation ret = obj.packingRotation;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index packingRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textureRect(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Rect ret = obj.textureRect;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textureRect on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textureRectOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Vector2 ret = obj.textureRectOffset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textureRectOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_vertices(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Vector2[] ret = obj.vertices;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index vertices on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_triangles(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			ushort[] ret = obj.triangles;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index triangles on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uv(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Sprite obj = (UnityEngine.Sprite)o;
			UnityEngine.Vector2[] ret = obj.uv;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uv on a nil value");
		}
	}
}

