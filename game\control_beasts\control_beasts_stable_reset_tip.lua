-- 灵兽培养吞噬转换资质
ControlBeastsStableResetTip = ControlBeastsStableResetTip or BaseClass(SafeBaseView)

function ControlBeastsStableResetTip:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(830, 590)})
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_stable_reset_tips")
    self:SetMaskBg(true)
end

function ControlBeastsStableResetTip:ReleaseCallBack()
	if self.left_flair_attrlist and #self.left_flair_attrlist > 0 then
		for _, culture_cell in ipairs(self.left_flair_attrlist) do
			culture_cell:DeleteMe()
			culture_cell = nil
		end

		self.left_flair_attrlist = nil
	end

    if self.right_flair_attrlist and #self.right_flair_attrlist > 0 then
		for _, culture_cell in ipairs(self.right_flair_attrlist) do
			culture_cell:DeleteMe()
			culture_cell = nil
		end

		self.right_flair_attrlist = nil
	end

    if self.beast_left_item then
		self.beast_left_item:DeleteMe()
        self.beast_left_item = nil
	end

    if self.beast_right_item then
		self.beast_right_item:DeleteMe()
        self.beast_right_item = nil
	end

    if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end	

    self.show_data = nil
    self.last_data = nil
    self.incubate_index = nil
    self.is_can_not_flush = nil
    self.right_score_index = nil
end

function ControlBeastsStableResetTip:SetTipsData(show_data)
    self.show_data = show_data
end

function ControlBeastsStableResetTip:SetTipsIncubateIndex(incubate_index)
    self.incubate_index = incubate_index
end

function ControlBeastsStableResetTip:CloseCallBack()
    self.show_data = nil
    self.last_data = nil
    self.incubate_index = nil
    self.is_can_not_flush = nil
    self.right_score_index = nil
end

function ControlBeastsStableResetTip:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName9

    if not self.beast_left_item then
		self.beast_left_item = ItemCell.New(self.node_list.left_item_pos)
	end

    if not self.beast_right_item then
		self.beast_right_item = ItemCell.New(self.node_list.right_item_item_pos)
	end

    -- 资质属性
    if self.left_flair_attrlist == nil then
        self.left_flair_attrlist = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.layout_left_item_flair_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = BeststResetItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.left_flair_attrlist[i] = cell
            end
        end
    end

    -- 资质属性
    if self.right_flair_attrlist == nil then
        self.right_flair_attrlist = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.layout_right_item_flair_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = BeststResetItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.right_flair_attrlist[i] = cell
            end
        end
    end

    XUI.AddClickEventListener(self.node_list.btn_operate, BindTool.Bind2(self.OnClickBeastsReset, self))
    XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind2(self.OnClickBeastsOwner, self))
    XUI.AddClickEventListener(self.node_list.incubate_quick_const_icon, BindTool.Bind2(self.IncubateQuickItemShow, self))
    
end

-- 刷新
function ControlBeastsStableResetTip:OnFlush(param_t)
    for k, v in pairs(param_t) do
        if k == "item_id" then
            self:RefreshSpendItem()
        else
            if self.is_can_not_flush then
                return
            end
        
            self:RefreshNewMessage()  
            self:RefreshOldMessage()
            self:RefreshSpendItem()
            self.is_can_not_flush = true
        end
    end
end

-- 刷新右侧驭兽信息
function ControlBeastsStableResetTip:RefreshSpendItem()
    --消耗状态
    local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
    if base_cfg then
        local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(base_cfg.incubate_item.item_id)
        if item_cfg then
            local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
            self.node_list.incubate_quick_const_icon.image:LoadSprite(bundle, asset)
            local item_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.incubate_item.item_id)
            local color = item_num >= base_cfg.incubate_item.num and COLOR3B.D_GREEN or COLOR3B.D_RED
            self.node_list.incubate_quick_const_text.text.text = ToColorStr(string.format("%s/%s", base_cfg.incubate_item.num, item_num), color) 
        end
    end
end

-- 刷新右侧驭兽信息
function ControlBeastsStableResetTip:RefreshNewMessage()
    if (not self.show_data) then
        return
    end

    self.node_list.right_item_message_root:CustomSetActive(self.last_data ~= nil)
    self.node_list.right_item_no_data:CustomSetActive(self.last_data == nil)
    if (not self.last_data) or (not self.last_data.server_data) or (not self.last_data.is_have_beast) then
        return
    end

    local new_data = ControlBeastsWGData.Instance:GetBreedingSlotDataById(self.show_data.bag_id)
    if (not new_data) or (not new_data.server_data) or (not new_data.is_have_beast) then
        return
    end

    local server_data = new_data.server_data
    local old_server_data = self.last_data.server_data
    self.beast_right_item:SetData({item_id = server_data.beast_id, is_beast = true})
    local flair_score = 0
    local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(server_data.beast_id)
    local beast_flair_preview = nil

    if beast_map_data and beast_map_data.beast_flair_preview then
        beast_flair_preview = beast_map_data.beast_flair_preview
    end

    if server_data.flair_values then
        for index, bag_flair_cell in ipairs(self.right_flair_attrlist) do
            local temp_data = {}
            temp_data.beast_preview_value = beast_flair_preview and beast_flair_preview[index] and beast_flair_preview[index].max or 0

            if server_data.flair_values[index] then
                flair_score = flair_score + server_data.flair_values[index]
                temp_data.cur_value = server_data.flair_values[index]
                temp_data.compare_value = old_server_data.flair_values[index]
                bag_flair_cell:SetData(temp_data)
            else
                temp_data.cur_value = server_data.effort_value / 10000
                temp_data.compare_value = old_server_data.effort_value / 10000
                bag_flair_cell:SetData(temp_data)    -- 成长值
            end
        end
    end
    
    flair_score = math.floor(flair_score * (server_data.effort_value / 10000)) 
    -- 计算资质评级
    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)

    if beast_cfg then
        local score_index = ControlBeastsWGData.Instance:GetFlairScoreByScore(beast_cfg.beast_star, flair_score)
        self.node_list.right_item_flair_txt.text.text = string.format(Language.ContralBeasts.AttrTitle5, flair_score) 

        self.right_score_index = score_index
        local battle_type_str = string.format("a3_hs_bs_pz%d", score_index) 
        local bundle, asset = ResPath.GetControlBeastsImg(battle_type_str)
        self.node_list.right_item_flair_pz.image:LoadSprite(bundle, asset)
    end
end

-- 刷新左侧驭兽信息
function ControlBeastsStableResetTip:RefreshOldMessage()
    if (not self.show_data) then
        return
    end

    local old_data = self.last_data

    if not old_data then
        old_data = ControlBeastsWGData.Instance:GetBreedingSlotDataById(self.show_data.bag_id)
    end

    if (not old_data) or (not old_data.server_data) or (not old_data.is_have_beast) then
        return
    end

    local server_data = old_data.server_data
    self.beast_left_item:SetData({item_id = server_data.beast_id, is_beast = true})
    local flair_score = 0
    local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(server_data.beast_id)
    local beast_flair_preview = nil

    if beast_map_data and beast_map_data.beast_flair_preview then
        beast_flair_preview = beast_map_data.beast_flair_preview
    end

    if server_data.flair_values then
        for index, bag_flair_cell in ipairs(self.left_flair_attrlist) do
            local temp_data = {}
            temp_data.beast_preview_value = beast_flair_preview and beast_flair_preview[index] and beast_flair_preview[index].max or 0

            if server_data.flair_values[index] then
                flair_score = flair_score + server_data.flair_values[index]
                temp_data.cur_value = server_data.flair_values[index]
                bag_flair_cell:SetData(temp_data)
            else
                temp_data.cur_value = server_data.effort_value / 10000
                bag_flair_cell:SetData(temp_data)    -- 成长值
            end
        end
    end
    
    flair_score = math.floor(flair_score * (server_data.effort_value / 10000)) 
    -- 计算资质评级
    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)

    if beast_cfg then
        local score_index = ControlBeastsWGData.Instance:GetFlairScoreByScore(beast_cfg.beast_star, flair_score)
        self.node_list.left_item_flair_txt.text.text = string.format(Language.ContralBeasts.AttrTitle5, flair_score) 

        local battle_type_str = string.format("a3_hs_bs_pz%d", score_index) 
        local bundle, asset = ResPath.GetControlBeastsImg(battle_type_str)
        self.node_list.left_item_flair_pz.image:LoadSprite(bundle, asset)
    end

    -- 刷新完取最新做缓存
    self.last_data = TableCopy(ControlBeastsWGData.Instance:GetBreedingSlotDataById(self.show_data.bag_id))
end

----------------------------------------------------------------------------------------------------------
-- 认主
function ControlBeastsStableResetTip:OnClickBeastsOwner()
    if (not self.show_data) or (not self.incubate_index) then
        return
    end

    ControlBeastsWGCtrl.Instance:SendOperateTypeIncubateOwner(self.incubate_index)
    self:Close()
end

-- 重新孵化
function ControlBeastsStableResetTip:OnClickBeastsReset()
    if (not self.show_data) then
        return
    end

    local execute_rebreed = function()
        local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
        if base_cfg then
            local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(base_cfg.incubate_item.item_id)
            if item_cfg then
                local item_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.incubate_item.item_id)
                if item_num < base_cfg.incubate_item.num then
                    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = base_cfg.incubate_item.item_id})
                else
                    self.is_can_not_flush = false
                    ControlBeastsWGCtrl.Instance:SendOperateTypeRebreed(self.show_data.bag_id)
                end
            end
        end
    end

    if self.right_score_index == nil or self.right_score_index < 3 then
        execute_rebreed()
    else
        -- 弹出二次确认提示框
        if self.alert_window == nil then
            self.alert_window = Alert.New()
        end
        self.alert_window:SetLableString(Language.ContralBeasts.ResetTips1)
        self.alert_window:SetOkFunc(execute_rebreed)
        self.alert_window:Open()
    end
end

-- 快速孵化的物品展示
function ControlBeastsStableResetTip:IncubateQuickItemShow()
    --设置四个按钮下方消耗状态
    local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
    if base_cfg then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = base_cfg.incubate_item.item_id})
    end
end
---------------------------------------------------------------------
----------------------------------灵兽资质item-----------------------
BeststResetItemRender = BeststResetItemRender or BaseClass(BaseRender)

function BeststResetItemRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.attr_value.text.text = self.data.cur_value
    self.node_list.attr_progress.slider.value = self.data.cur_value / self.data.beast_preview_value
    local proportion_value = self.data.cur_value / self.data.beast_preview_value
    local proportion_num = 1

    if proportion_value > 0.3 and proportion_value < 0.7 then
        proportion_num = 2
    elseif proportion_value > 0.7 then
        proportion_num = 5 
    end

    local fill_str = string.format("a3_hs_jd_%d", proportion_num)
    self.node_list.fill_image.image:LoadSprite(ResPath.GetControlBeastsImg(fill_str))

    if self.node_list.arrow_down and self.data.compare_value ~= nil then
        self.node_list.arrow_down:CustomSetActive(self.data.cur_value < self.data.compare_value)
    end

    if self.node_list.arrow_up and self.data.compare_value ~= nil then
        self.node_list.arrow_up:CustomSetActive(self.data.cur_value > self.data.compare_value)
    end

    -- 获取资质加成属性数据
    local Flair_data = ControlBeastsWGData.Instance:GetFlairAttrDataById(self.index - 1)
    if Flair_data then
        self.node_list.attr_name.text.text = Flair_data.flair_name
    else
        self.node_list.attr_name.text.text = Language.ContralBeasts.SelectError4
    end
end