KFOneVOneEndView = KFOneVOneEndView or BaseClass(SafeBaseView)

function KFOneVOneEndView:__init()
	self.view_style = ViewStyle.Half
	self.default_index = 0
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_min_panel")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf_1v1_end")
end

function KFOneVOneEndView:__delete()

end

function KFOneVOneEndView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("onevone_finish_confirm") then
		CountDownManager.Instance:RemoveCountDown("onevone_finish_confirm")
	end

	if not IsEmptyTable(self.reward_cell_list) then
		for k,v in pairs(self.reward_cell_list) do
			v:DeleteMe()
		end
		self.reward_cell_list = {}
	end

end

function KFOneVOneEndView:LoadCallBack()
	-- self.awrd_score = 10
	-- self.add_score = 10
	self.node_list["btn_out_end"].button:AddClickListener(BindTool.Bind1(self.OnClickBuyOut, self))

	if 0 == self.end_result then
		self.node_list["lose"]:SetActive(true)
		self.node_list["victory"]:SetActive(false)
		self.node_list.rich_jifen.text.text = string.format(Language.Kuafu1V1.ResultTip4,self.awrd_score, self.add_score)--ToColorStr(string.format(Language.Kuafu1V1.ResultTip4, self.awrd_score), COLOR3B.WHITE)
	else
		self.node_list["lose"]:SetActive(false)
		self.node_list["victory"]:SetActive(true)
		self.node_list.rich_jifen.text.text = string.format(Language.Kuafu1V1.ResultTip1, self.awrd_score,self.add_score)--ToColorStr(string.format(Language.Kuafu1V1.ResultTip1, self.awrd_score), COLOR3B.WHITE)
	end


	self.node_list.rich_get_exp:SetActive(false)
	if CountDownManager.Instance:HasCountDown("onevone_finish_confirm") then
		CountDownManager.Instance:RemoveCountDown("onevone_finish_confirm")
	end
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), TimeWGCtrl.Instance:GetServerTime() + 5)
	CountDownManager.Instance:AddCountDown("onevone_finish_confirm", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CloseViewHandler, self),  TimeWGCtrl.Instance:GetServerTime() + 5 , nil, 1)

	self.reward_cell_list = {}
	if not IsEmptyTable(self.reward_data) then
		for i=0,#self.reward_data do
			self.reward_cell_list[i] = ItemCell.New(self.node_list["reward_list"])
			self.reward_cell_list[i]:SetData(self.reward_data[i])
		end
	end

end

function KFOneVOneEndView:OnClickBuyOut()
	Field1v1WGCtrl.Instance:Send1V1ReturnReadyScene()
	self:Close()
end

function KFOneVOneEndView:UpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	self.node_list.lbl_close.text.text = string.format(Language.Field1v1.EndTimeCountDown,temp_seconds)
end

function KFOneVOneEndView:ShowIndexCallBack(index)
	if nil == self.end_result then
		return
	end
end

function KFOneVOneEndView:OpenCallBack()
end

function KFOneVOneEndView:CloseCallBack()
	self.end_result = nil
	self.add_score = nil
	self.add_gongxun = nil
	self.reward_data = nil

	if CountDownManager.Instance:HasCountDown("onevone_finish_confirm") then
		CountDownManager.Instance:RemoveCountDown("onevone_finish_confirm")
	end
	-- Field1v1WGCtrl.Instance:LeaveZhanChangScene()
	for i=1,4 do
		if self["reward_baecell"..i] then
			self["reward_baecell"..i]:DeleteMe()
			self["reward_baecell"..i] = nil
		end
	end
end

function KFOneVOneEndView:SetEndData(data)
	self.end_result = data.result
	self.awrd_score = data.award_score
	self.add_score = data.add_score or 0
	self.add_gongxun = data.add_gongxun
	self.reward_data = data.reward_data
end

function KFOneVOneEndView:CloseViewHandler()
	Field1v1WGCtrl.Instance:Send1V1ReturnReadyScene()
	self:Close()
end
