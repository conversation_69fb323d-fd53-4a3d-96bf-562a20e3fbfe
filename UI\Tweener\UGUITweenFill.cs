﻿using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("UGUI/Tween/UGUI Tween Fill")]
[RequireComponent(typeof(Image))]
public class UGUITweenFill : UGUITweener
{
	[Range(0f, 1f)] public float from = 1f;
	[Range(0f, 1f)] public float to = 1f;

	bool mCached = false;
	Image mSprite;

	void Cache ()
	{
		mCached = true;
		mSprite = GetComponent<Image>();
	}

	public float value
	{
		get
		{
			if (!mCached) Cache();
			if (mSprite != null) return mSprite.fillAmount;
			return 0f;
		}
		set
		{
			if (!mCached) Cache();
			if (mSprite != null) mSprite.fillAmount = value;
		}
	}

	protected override void OnUpdate (float factor, bool isFinished) { value = Mathf.Lerp(from, to, factor); }

	static public UGUITweenFill Begin (GameObject go, float duration, float fill)
	{
		UGUITweenFill comp = UGUITweener.Begin<UGUITweenFill>(go, duration);
		comp.from = comp.value;
		comp.to = fill;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	public override void SetStartToCurrentValue () { from = value; }
	public override void SetEndToCurrentValue () { to = value; }
}
