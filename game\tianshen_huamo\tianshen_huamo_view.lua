TianShenHuamoView = TianShenHuamoView or BaseClass(SafeBaseView)

function TianShenHuamoView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	
	self:SetMaskBg(false)
	local bundle_name = "uis/view/tianshen_huamo_prefab"
	self:AddViewResource(0, bundle_name, "layout_tianshen_hua_mo")
	self:AddViewResource(0, bundle_name, "layout_tianshen_hua_mo_down")
end

function TianShenHuamoView:__delete()

end

function TianShenHuamoView:OpenCallBack()
	self:TianshenInfo(0)
end

function TianShenHuamoView:CloseCallBack()
	self.active_ts_select_skill = nil
	self.active_ts_select_type = nil
	self.active_ts_last_appe_image_id = nil
end

function TianShenHuamoView:LoadCallBack()
	self.curr_select_tianshen_index = nil
	self.mohua_model = nil
	self.show_effect_loader = nil
	self.curr_select_tianshen_data = nil
	self.ts_icon_list = nil
	self.ts_icon_cells = nil
	self.active_ts_select_skill = nil
	self.ts_type_list = nil
	self.ts_type_cells = nil
	self.active_ts_select_type = nil
	self.active_ts_last_appe_image_id = nil
	self.rumo_item = nil
	self.attr_list = nil

	self:RegisterButtonListen()
	self:PlayActivationEffect()
	self:CreateAttrList()
end

function TianShenHuamoView:ReleaseCallBack()
	self.curr_select_tianshen_index = nil
	self.curr_select_tianshen_data = nil
	self.active_ts_select_skill = nil
	self.ts_icon_cells = nil
	self.ts_type_cells = nil

	if self.mohua_model then
		self.mohua_model:DeleteMe()
		self.mohua_model = nil
	end

	if self.show_effect_loader then
		self.show_effect_loader:Destroy()
		self.show_effect_loader = nil
	end

	if self.ts_icon_list then
		for k, v in pairs(self.ts_icon_list) do
			v:DeleteMe()
		end
		self.ts_icon_list = nil
	end

	if self.ts_type_list then
		self.ts_type_list:DeleteMe()
		self.ts_type_list = nil
	end

	if self.rumo_item then
		self.rumo_item:DeleteMe()
		self.rumo_item = nil
	end

	if self.attr_list and #self.attr_list > 0 then
		for _, attr_cell in ipairs(self.attr_list) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end
	end
	self.attr_list = nil
end

function TianShenHuamoView:OnFlush(param_list)
	self:FlushTianShenData(param_list)
	self:FlushTypeRoot(param_list)
end

---注册按钮方法
function TianShenHuamoView:RegisterButtonListen()
	--退出按钮
	self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.Close, self))
	self.node_list["btn_active"].button:AddClickListener(BindTool.Bind(self.TianshenActive, self))
	self.node_list["huamo_reset"].button:AddClickListener(BindTool.Bind(self.TianshenReset, self))
	self.node_list["huamo_huanhua"].button:AddClickListener(BindTool.Bind(self.HuanHUa, self))
	self.node_list["btn_up_level"].button:AddClickListener(BindTool.Bind(self.TianShenHuMoUpStar, self))
	self.node_list["huamo_yi_huanhua"].button:AddClickListener(BindTool.Bind(self.CancelHuanHUa, self))
	XUI.AddClickEventListener(self.node_list["ts_skill_yulan_btn"], BindTool.Bind(self.OnClickSkillYuLanBtn, self))
end

-- 拿取信息
function TianShenHuamoView:TianshenInfo(tianshen_id)
	if tianshen_id then
		if tianshen_id == 0 then
			TianShenHuamoWGCtrl.Instance:CSTianShenRuMoAllInfo()
		else
			TianShenHuamoWGCtrl.Instance:CSTianShenRuMoInfo(tianshen_id)
		end
	end
end

-- 激活
function TianShenHuamoView:TianshenActive()
	local info = TianShenHuamoWGData.Instance:GetHuaMoSubDataById(self.curr_select_tianshen_index,
		self.active_ts_select_type)
	local cfg = TianShenHuamoWGData.Instance:GetHuaMoDataByIdAndLevel(self.curr_select_tianshen_index,
		self.active_ts_select_type)

	if cfg and info then
		local item_count = ItemWGData.Instance:GetItemNumInBagById(cfg.item_id)
		local need_count = 1 -- 策划先固定一个了

		if item_count < need_count then
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = cfg.item_id })
			return
		end

		if (not info.is_activate) or (not info.shenqi_active) or (not info.front_active) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenHuaMo.ConditionError)
			return
		end
	end

	TianShenHuamoWGCtrl.Instance:CSTianShenRuMoActive(self.curr_select_tianshen_index, self.active_ts_select_type)
end

-- 重置
function TianShenHuamoView:TianshenReset()
	local cfg_data = TianShenHuamoWGData.Instance:GetHuaMoDataByIdAndLevel(self.curr_select_tianshen_index,
		self.active_ts_select_type)
	if cfg_data then
		TianShenHuamoWGCtrl.Instance:OpenResetSkill(cfg_data)
	end
end

-- 幻化
function TianShenHuamoView:HuanHUa()
	TianShenHuamoWGCtrl.Instance:CSTianShenRuMoHuanHua(self.curr_select_tianshen_index, self.active_ts_select_type)
end

-- 取消幻化
function TianShenHuamoView:CancelHuanHUa()
	TianShenHuamoWGCtrl.Instance:CSTianShenRuMoHuanHua(self.curr_select_tianshen_index, 0)
end

-- 化魔升星
function TianShenHuamoView:TianShenHuMoUpStar()
	TianShenHuamoWGCtrl.Instance:CSTianShenRuMoUpStar(self.curr_select_tianshen_index, self.active_ts_select_type)
end

function TianShenHuamoView:OnClickSkillYuLanBtn()
	CommonSkillShowCtrl.Instance:SetTianShenSkillViewDataAndOpen({ tianshen_index = self.curr_select_tianshen_index,
		tianshen_huamo_level = self.active_ts_select_type })
end

--展示特效
function TianShenHuamoView:PlayActivationEffect()
	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tiansheng_huohua)
	self.node_list.act_effect:ChangeAsset(bundle_name, asset_name, false)
end

-- 创建一下属性列表
function TianShenHuamoView:CreateAttrList()
	if not self.attr_list then
		self.attr_list = {}
		for i = 1, 8 do
			local cell = CommonAddAttrRender.New(self.node_list.layout_attr:FindObj("attr_" .. i))
			cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(true)
			self.attr_list[i] = cell
		end
	end
end

---刷新选择的数据
function TianShenHuamoView:FlushTianShenData(param_list)
	local cur_select_index = tonumber(TianShenWGData.Instance:InfoTsSelectIndex())

	if cur_select_index == -1 then
		if param_list and param_list.all and param_list.all.open_param then
			cur_select_index = tonumber(param_list.all.open_param)
		else
			if self.curr_select_tianshen_index then
				cur_select_index = self.curr_select_tianshen_index
			else
				cur_select_index = 0
			end
		end
	else
		if param_list and param_list.all and param_list.all.open_param then
			cur_select_index = tonumber(param_list.all.open_param)
		else
			self.active_ts_select_type = TianShenHuamoWGData.Instance:GetTypeIndex(cur_select_index,
				self.active_ts_select_type)
		end
	end

	self.curr_select_tianshen_index = cur_select_index
	self.curr_select_tianshen_data = TianShenWGData.Instance:GetTianShenCfg(self.curr_select_tianshen_index)
end

---刷新类型列表
function TianShenHuamoView:FlushTypeRoot(param_list)
	if not self.curr_select_tianshen_data then
		return
	end

	local select_data = self.curr_select_tianshen_data
	if not select_data then
		return
	end

	if param_list and param_list.all and param_list.all.to_ui_param then
		self.active_ts_select_type = tonumber(param_list.all.to_ui_param)
	end

	---优先选择出带有红点的目标
	if nil == self.active_ts_select_type then
		self.active_ts_select_type = TianShenHuamoWGData.Instance:GetTypeIndex(self.curr_select_tianshen_index)
	end

	if not self.ts_type_list then
		self.ts_type_list = AsyncListView.New(TianShenHuaMoTypeItem, self.node_list.huamo_type_list)
		self.ts_type_list:SetSelectCallBack(BindTool.Bind(self.FlushHuaMoTypeRenderClick, self))
	end

	if not self.ts_type_cells then
		self.ts_type_cells = {}
	end

	local data = TianShenHuamoWGData.Instance:GetHuaMoDataById(select_data.index)
	local refresh_count = data and #data.sub_datas

	if data and data.sub_datas and #data.sub_datas > 0 then
		self.ts_type_list:SetDataList(data.sub_datas)
		self.ts_type_list:SetRefreshCallback(function(cell, cellindex)
			self.ts_type_cells[cellindex] = cell
			if cellindex == refresh_count then
				local cell = self.ts_type_cells[self.active_ts_select_type]

				if not cell then
					return
				end
				local data = cell:GetData()

				if not data then
					return
				end
				self:FlushHuaMoType(cell:GetData(), self.active_ts_select_type)
			end
		end)
	end
end

function TianShenHuamoView:FlushHuaMoType(data, cell_index)
	self:SetTsTypeSelect(data.demon_level)
	self.active_ts_select_type = cell_index
	---切换页签刷新
	self:FlushSkillRoot()
	self:FlushModel()
	self:FlushAttrView()
	self:FlushTianShenStar()
	self:FlushTianActiveItem()
	self:FlushHuaMoSkillRenderClick()
end

function TianShenHuamoView:FlushHuaMoTypeRenderClick(cell, cell_index, is_default, is_click)
	if cell == nil or is_default then
		return
	end

	local index = cell:GetIndex()
	local data = cell:GetData()

	if self.active_ts_select_type == index then
		return
	else ---切换页签
		self:FlushHuaMoType(data, cell_index)
	end
end

function TianShenHuamoView:SetTsTypeSelect(demon_level)
	if (not self.ts_type_cells) or (#self.ts_type_cells <= 0) then
		return
	end

	for _, cell in ipairs(self.ts_type_cells) do
		cell:SetSelect(cell and cell:GetData() and cell:GetData().demon_level == demon_level)
	end
end

---刷新技能列表
function TianShenHuamoView:FlushSkillRoot()
	if not self.curr_select_tianshen_data then
		return
	end
	local select_data = self.curr_select_tianshen_data

	if not select_data then
		return
	end

	---优先选择出带有红点的目标
	if nil == self.active_ts_select_skill then
		self.active_ts_select_skill = TianShenHuamoWGData.Instance:GetSkillIndex(self.curr_select_tianshen_index,
			self.active_ts_select_type)
	end

	if not self.ts_icon_cells then
		self.ts_icon_cells = {}
	end

	if self.ts_icon_list == nil then
		self.ts_icon_list = {}
		for i = 1, 5 do
			self.ts_icon_list[i] = TianShenHuaMoSkillItem.New(self.node_list.ts_icon_list:FindObj("ts_icon_list" .. i))
			self.ts_icon_list[i]:SetIndex(i)
			self.ts_icon_list[i]:SetClickCallBack(BindTool.Bind(self.FlushHuaMoSkillRenderClick, self))
		end
	end

	local data = TianShenHuamoWGData.Instance:GetHuaMoDataById(select_data.index)
	if data and data.sub_datas and #data.sub_datas > 0 then
		for k, v in pairs(data.sub_datas[self.active_ts_select_type].skills) do
			self.ts_icon_list[k]:SetData(v)
		end

		local cell = self.ts_icon_list[self.active_ts_select_skill]

		if not cell then
			return
		end
		local data = cell:GetData()

		if not data then
			return
		end
		self:FlushHuaMoSkill(cell:GetData(), self.active_ts_select_skill)
		-- self.ts_icon_list[k]:SetData(v)
		-- if data.sub_datas[self.active_ts_select_type] then
		-- 	self.ts_icon_list:SetDataList(data.sub_datas[self.active_ts_select_type].skills)
		-- 	local refresh_count = #data.sub_datas[self.active_ts_select_type].skills
		-- 	self.ts_icon_list:SetRefreshCallback(function (cell, cellindex)
		-- 		self.ts_icon_cells[cellindex] = cell
		-- 		if cellindex == refresh_count then

		-- 		end
		-- 	end)
		-- end
	end
end

function TianShenHuamoView:FlushHuaMoSkill(data, cell_index)
	self:SetTsSkillSelect(data.skill_id)
	self.active_ts_select_skill = cell_index

	---刷新一下技能界面
	if TianShenHuamoWGCtrl.Instance:CheckOpenSkillLevelUp() then
		local skill_data = TianShenHuamoWGData.Instance:GetSkillCfgById(self.active_ts_select_type, data.skill_id)
		local tianshen_data = self.curr_select_tianshen_data
		if skill_data and tianshen_data then
			self:ShowSkillLevelUpTips(skill_data, data, cell_index, tianshen_data.index, self.active_ts_select_type)
		end
	end
end

function TianShenHuamoView:FlushHuaMoSkillRenderClick(cell)
	if cell == nil then
		return
	end

	local index = cell:GetIndex()
	local data = cell:GetData()
	if data.lock then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenHuaMo.RuMoNotActive)
		return
	end

	if data.skill_id == 0 then
		TianShenHuamoWGCtrl.Instance:OpenSelectSkill(self.curr_select_tianshen_index, self.active_ts_select_type)
		return
	end

	if self.active_ts_select_skill == index then ---打开技能升级界面
		local skill_data = TianShenHuamoWGData.Instance:GetSkillCfgById(self.active_ts_select_type, data.skill_id)
		local tianshen_data = self.curr_select_tianshen_data
		if skill_data and tianshen_data then
			self:ShowSkillLevelUpTips(skill_data, data, index, tianshen_data.index, self.active_ts_select_type)
		end
	else ---切换页签
		self:SetTsSkillSelect(data.skill_id, index)
	end
end

function TianShenHuamoView:SetTsSkillSelect(skill_id, index)
	self.active_ts_select_skill = index

	if not self.ts_icon_list then
		return
	end

	for k, v in pairs(self.ts_icon_list) do
		v:SetSelect(v and v:GetData() and v:GetData().skill_id == skill_id and v:GetData().skill_id ~= 0)
	end
end

---刷新模型
function TianShenHuamoView:FlushModel()
	if not self.curr_select_tianshen_data then
		return
	end

	local tianshen_data = self.curr_select_tianshen_data
	if tianshen_data then
		local audio = tianshen_data.show_audio or nil

		if self.show_effect_loader then
			self.show_effect_loader:Destroy()
			self.show_effect_loader = nil
		end

		if not self.mohua_model then
			self.mohua_model = RoleModel.New()
			local display_data = {
				parent_node = self.node_list["acti_display"],
				camera_type = MODEL_CAMERA_TYPE.BASE,
				-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
				rt_scale_type = ModelRTSCaleType.M,
				can_drag = true,
			}
			
			self.mohua_model:SetRenderTexUI3DModel(display_data)
			-- self.mohua_model:SetUI3DModel(self.node_list["acti_display"].transform,
			-- 	self.node_list.Block.event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
			self:AddUiRoleModel(self.mohua_model)
		end

		local appe_image_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImage(tianshen_data.index,
			self.active_ts_select_type)
		if not appe_image_id then
			return
		end

		if appe_image_id == self.active_ts_last_appe_image_id then
			return
		end

		self.active_ts_last_appe_image_id = appe_image_id
		self.mohua_model:SetTianShenModel(appe_image_id, tianshen_data.index, true, audio, SceneObjAnimator.Rest)
	end
end

-- 刷新属性面板
function TianShenHuamoView:FlushAttrView()
	if not self.curr_select_tianshen_data then
		return
	end

	local data = self.curr_select_tianshen_data
	local huamo_data = TianShenHuamoWGData.Instance:GetHuaMoSubDataById(data.index, self.active_ts_select_type)
	local attr_list = TianShenHuamoWGData.Instance:GetMainShowAttribute(data.index, self.active_ts_select_type,
		huamo_data and huamo_data.star_level or 0)

	if data and huamo_data and attr_list and self.attr_list then
		for i, attr_cell in ipairs(self.attr_list) do
			local attr_data = attr_list[i]
			local is_hide_next = false

			---处理一下特殊情况
			if attr_data.attr_str and attr_data.attr_value and attr_data.add_value then
				if not huamo_data.lock then
					attr_data.attr_value = attr_data.attr_value + attr_data.add_value --这里加上的是基础属性加上技能变化属性

					if attr_data.star_value and attr_data.star_value > 0 then
						attr_data.attr_value = attr_data.attr_value + attr_data.star_value
					end

					if attr_data.star_next_value and attr_data.star_next_value > 0 then
						attr_data.add_value = attr_data.star_next_value - attr_data.star_value
					else
						attr_data.add_value = 0
					end

					is_hide_next = attr_data.add_value <= 0
				end

				if attr_data.attr_value == 0 and attr_data.add_value == 0 then
					attr_data = nil ---置空隐藏
				end
			end

			if attr_cell then
				attr_cell:SetRealHideNext(is_hide_next)
				attr_cell:SetData(attr_data)
			end
		end
	end

	local tianshen_info                                     = TianShenWGData.Instance:GetTianShenInfoByIndex(data.index)
	local _, attr_hua_mo_list                               = TianShenHuamoWGData.Instance:GetHuaMoAttribute(data.index,
		self.active_ts_select_type, huamo_data and huamo_data.star_level or 0)
	self.node_list["text_bianshen_zhandouli_num"].text.text = AttributeMgr.GetCapability(attr_hua_mo_list)
	--原被动技能的属性显示
	local be_skill_cfg                                      = TianShenWGData.Instance:GetSpecialImagePasvSkillCfg(data
	.index)
	self.node_list.attr_empty:SetActive(not IsEmptyTable(be_skill_cfg))

	if not IsEmptyTable(be_skill_cfg) then
		local tianshen_level = tianshen_info and tianshen_info.level or 0
		local limit_text, t_cfg
		for i = 1, 3 do
			limit_text = ""
			t_cfg = be_skill_cfg[i - 1]
			if t_cfg then
				if tianshen_level < t_cfg.active_grade then
					limit_text = string.format(Language.TianShen.TSBeSkillOpenLimit, t_cfg.active_grade)
				end
				self.node_list["beskill_desc_" .. i].text.text = t_cfg.skill_describe .. limit_text
			end
			self.node_list["beskill_attr_" .. i]:SetActive(t_cfg ~= nil)
		end
	end
end

---刷新天神星级和其他属性
function TianShenHuamoView:FlushTianShenStar()
	if not self.node_list["text_jie"] then
		return
	end

	if not self.curr_select_tianshen_data then
		return
	end

	local data = self.curr_select_tianshen_data
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(data.index)
	local tianshen_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.curr_select_tianshen_index)

	if tianshen_cfg and tianshen_cfg.act_item_id then
		local act_item_cfg = ItemWGData.Instance:GetItemConfig(tianshen_cfg.act_item_id)
		local rumo_name = Language.TianShenHuaMo.DemonName[self.active_ts_select_type]
		local cfg_name = act_item_cfg and act_item_cfg.name or ""
		self.node_list["name"].text.text = string.format(rumo_name, cfg_name)
	end

	local shenshi_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.curr_select_tianshen_index)
	local bundle, asset = ResPath.GetCommon("a3_quality_text_" .. shenshi_data.jingshen + 1)
	self.node_list.active_jingshen_img.image:LoadSprite(bundle, asset, function()
		self.node_list.active_jingshen_img.image:SetNativeSize()
	end)
	local huamo_data = TianShenHuamoWGData.Instance:GetHuaMoDataById(data.index)
	local huamo_sub_data = TianShenHuamoWGData.Instance:GetHuaMoSubDataById(data.index, self.active_ts_select_type)

	--展示幻化和重置
	if huamo_data and huamo_sub_data then
		self.node_list.huamo_huanhua:SetActive((not huamo_sub_data.lock) and huamo_data.huanhua_id and
		huamo_data.huanhua_id ~= self.active_ts_select_type)
		self.node_list.huamo_yi_huanhua:SetActive((not huamo_sub_data.lock) and huamo_data.huanhua_id and
		huamo_data.huanhua_id == self.active_ts_select_type)
		self.node_list.huamo_reset:SetActive((not huamo_sub_data.lock) and
		TianShenHuamoWGData.Instance:CheckSubDataHaveSkill(huamo_sub_data))
	else
		self.node_list.huamo_huanhua:SetActive(false)
		self.node_list.huamo_yi_huanhua:SetActive(false)
		self.node_list.huamo_reset:SetActive(false)
	end

	--[[ 	---设置背景图
	local bundle = "uis/rawimages/a1_mlsj_beijing%d"
	local asset_name = "a1_mlsj_beijing%d.png"
	local bundle_normal =  string.format(bundle, self.active_ts_select_type)
	local asset_name_normal =  string.format(asset_name, self.active_ts_select_type)
	self.node_list["raw_tianshen_bg"].raw_image:LoadSprite(bundle_normal, asset_name_normal, nil) ]]
end

---刷新天神激活物品和激活条件
function TianShenHuamoView:FlushTianActiveItem()
	local info = TianShenHuamoWGData.Instance:GetHuaMoSubDataById(self.curr_select_tianshen_index,
		self.active_ts_select_type)
	local cfg = TianShenHuamoWGData.Instance:GetHuaMoDataByIdAndLevel(self.curr_select_tianshen_index,
		self.active_ts_select_type)
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.curr_select_tianshen_index)
	local tianshen_shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(self.curr_select_tianshen_index)
	local color_str = "<color=%s>%s</color>"

	if info and cfg and tianshen_cfg and tianshen_shenqi_cfg then
		local dec_list = {}
		-- 天神激活
		local color = info.is_activate and COLOR3B.GREEN or COLOR3B.PINK
		local tianshen_name = string.format(color_str, color,
			string.format(Language.TianShenHuaMo.ActiveCondotion, tianshen_cfg.bianshen_name))
		table.insert(dec_list, tianshen_name)
		-- 天神对应神器激活
		color = info.shenqi_active and COLOR3B.GREEN or COLOR3B.PINK
		local shenqi_name = string.format(color_str, color,
			string.format(Language.TianShenHuaMo.ActiveCondotion, tianshen_shenqi_cfg.name))
		table.insert(dec_list, shenqi_name)
		-- 前置条件激活
		color = info.front_active and COLOR3B.GREEN or COLOR3B.PINK
		local front_level = cfg.demon_level - 1 == 0 and 1 or cfg.demon_level - 1
		local rumo_name = Language.TianShenHuaMo.DemonName[front_level]
		local front_name = string.format(rumo_name, tianshen_cfg.bianshen_name)
		local tianshen_name = string.format(color_str, color,
			string.format(Language.TianShenHuaMo.ActiveCondotion, front_name))
		table.insert(dec_list, tianshen_name)

		---只有三个条件，先这样写
		for i = 1, 3 do
			self.node_list["condition" .. i].text.text = dec_list[i]
			if i == 3 then
				self.node_list["condition" .. i]:SetActive(self.active_ts_select_type > 1)
			else
				self.node_list["condition" .. i]:SetActive(self.active_ts_select_type == 1)
			end
		end
	end

	if cfg and info then
		self.node_list["common_flag_red"]:SetActive(false)
		self.node_list["btn_active"]:SetActive(info.lock or info.is_can_active)
		self.node_list["active_condition"]:SetActive(info.lock or info.is_can_active)
		self.node_list["active_remind"]:SetActive(info.lock and info.is_can_active and info.front_active and
		info.shenqi_active)

		---设置星级
		local huamo_sub_star_cfg = TianShenHuamoWGData.Instance:GetStarlevelCfgById(self.curr_select_tianshen_index,
			self.active_ts_select_type, info.star_level + 1)
		local is_full_star = huamo_sub_star_cfg == nil --下一级为空的话则满星了
		self.node_list["is_max_level"]:SetActive((not info.lock) and is_full_star)
		self.node_list["btn_up_level"]:SetActive((not info.lock) and (not is_full_star))
		self.node_list["up_level_remind"]:SetActive((not info.lock) and (not is_full_star) and info.star_up)

		self.node_list.fs_stars_list:SetActive(not info.lock)
		-- 升级
		if not info.lock then
			local star_res_list = GetStarImgResByStar(info.star_level)

			for i = 1, 5 do
				if self.node_list["fs_star_" .. i] then
					self.node_list["fs_star_" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
				end
			end
		end

		local temp_item_id = cfg.item_id
		local need_count = 1 -- 策划先固定一个了

		if (not is_full_star) and (not info.lock) and huamo_sub_star_cfg then
			temp_item_id = huamo_sub_star_cfg.item_id
			need_count = huamo_sub_star_cfg.item_num
		end

		if not self.rumo_item then
			self.rumo_item = ItemCell.New(self.node_list["rumo_item"])
		end

		local item_count = ItemWGData.Instance:GetItemNumInBagById(temp_item_id)
		self.rumo_item:SetData({ item_id = temp_item_id, num = item_count })

		if need_count then
			local str = string.format("%s/%s", item_count, need_count)
			local color = item_count >= need_count and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
			self.rumo_item:SetRightBottomTextVisible(true)
			self.rumo_item:SetRightBottomColorText(str, color)
		end

		local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item_id)
		if item_cfg and item_cfg.is_display_role and item_cfg.is_display_role <= 0 then
			self.rumo_item:SetIsShowTips(false)

			self.rumo_item:SetClickCallBack(function()
				TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = cfg.item_id })
			end)
		else
			self.rumo_item:SetClickCallBack(nil)
			self.rumo_item:SetIsShowTips(true)
		end
	end
end

---刷新天神激活物品和激活条件
function TianShenHuamoView:ShowSkillLevelUpTips(skill_data, data, solt, tianshen_id, demon_level)
	TianShenHuamoWGCtrl.Instance:OpenSkillLevelUp(skill_data, data, solt, tianshen_id, demon_level)
end

---------------------------------------------------TianShenHuaMoSkillItem--------------------------------
TianShenHuaMoSkillItem = TianShenHuaMoSkillItem or BaseClass(BaseRender)

function TianShenHuaMoSkillItem:__init()
end

function TianShenHuaMoSkillItem:__delete()
end

function TianShenHuaMoSkillItem:OnFlush()
	if self.data == nil then
		return
	end

	local is_open = not self.data.lock
	local not_have_skill = self.data.skill_id == 0
	local have_skill = not not_have_skill

	if self.data.skill_id ~= 0 then
		local skill_cfg = TianShenHuamoWGData.Instance:GetSkillCfgById(self.data.demon_level, self.data.skill_id)
		if skill_cfg then
			local bundle, asset = ResPath.GetSkillIconById(skill_cfg.skill_icon)
			self.node_list["skill_icon"].image:LoadSprite(bundle, asset, function()
				self.node_list["skill_icon"].rect.sizeDelta = Vector2(76, 76)
			end)
			self.node_list.lbl_ml_item_name.text.text = skill_cfg.skill_name
			self.node_list.level.text.text = self.data.skill_lv
		end
	end
	self.node_list.can_add:SetActive(is_open and not_have_skill)
	self.node_list.level:SetActive(is_open and have_skill and self.data.skill_lv > 0)
	self.node_list.level_bg:SetActive(is_open and have_skill and self.data.skill_lv > 0)
	self.node_list.skill_icon:SetActive(is_open and have_skill)
	self.node_list.lbl_ml_item_name:SetActive(is_open and have_skill)
	self.node_list.lbl_ml_item_name_bg:SetActive(is_open and have_skill)
	self.node_list.lbl_ml_item_name_bg1:SetActive(not is_open or not_have_skill)
	self.node_list.remind:SetActive(self.data.is_remind)
	self.node_list.img_lock:SetActive(self.data.lock)
end

--[[ function TianShenHuaMoSkillItem:SetSelect(is_select)
	self.node_list.img_hl:SetActive(is_select)
end ]]

---------------------------------------------------TianShenHuaMoSkillItem--------------------------------
TianShenHuaMoTypeItem = TianShenHuaMoTypeItem or BaseClass(BaseRender)
function TianShenHuaMoTypeItem:__init()
end

function TianShenHuaMoTypeItem:__delete()
end

function TianShenHuaMoTypeItem:OnFlush()
	if self.data == nil then
		return
	end

	local bundle = "uis/view/tianshen_huamo/images_atlas"
	local asset_name = "a2_mlsj_qianyetuan%d"
	local image_index = (self.data.demon_level - 1) * 2
	local asset_name_hl = string.format(asset_name, image_index + 1)
	local asset_name_normal = string.format(asset_name, image_index + 2)
	self.node_list["icon_normal"].image:LoadSprite(bundle, asset_name_normal, nil)
	self.node_list["icon_hl"].image:LoadSprite(bundle, asset_name_hl, nil)
	self.node_list["img_type_text"].text.text = Language.TianShenHuaMo.DemonLevel[self.data.demon_level]
	--asset_name = "a1_mlsj_hong%d"
	-- local asset_img_type =  string.format(asset_name, self.data.demon_level)
	-- self.node_list["img_type"].image:LoadSprite(bundle, asset_img_type, nil)
	self.node_list["remind"]:SetActive(self.data.is_remind)
	self.node_list["img_battle"]:SetActive(false)
	self.node_list["flag_tip"]:SetActive(false)
end

function TianShenHuaMoTypeItem:SetSelect(is_select)
	self.node_list.img_hl:SetActive(is_select)
	self.node_list.img_normal:SetActive(not is_select)
end
