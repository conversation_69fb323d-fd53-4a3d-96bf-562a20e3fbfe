KfYeZhanWangChengSceneLogic = KfYeZhanWangChengSceneLogic or BaseClass(CrossServerSceneLogic)

function KfYeZhanWangChengSceneLogic:__init()
	self.last_check_time = 0
	--self.title_set_rule = SimpleRule.New(ShieldObjType.FollowTitle, ShieldRuleWeight.Middle, function()
	--		return false
	--end)
	--self.title_set_rule:Register()

	self.change_app_func = BindTool.Bind(self.ChangeAppearanceFunc, self)
	self.change_role_app = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, self.change_app_func)
	self.change_shadow_app = GlobalEventSystem:Bind(SceneEventType.OBJ_ENTER_SHADOW, self.change_app_func)
end

function KfYeZhanWangChengSceneLogic:__delete()
	self.before_act_mode = nil
	self.appearance = nil
	self.def_wing = nil

	if self.change_role_app then
		GlobalEventSystem:UnBind(self.change_role_app)
		self.change_role_app = nil
	end

	if self.change_shadow_app then
		GlobalEventSystem:UnBind(self.change_shadow_app)
		self.change_shadow_app = nil
	end

	if self.shield_pet_rule then
		self.shield_pet_rule:DeleteMe()
		self.shield_pet_rule = nil
	end

	self.change_app_func = nil
end

function KfYeZhanWangChengSceneLogic:Update(now_time, elapse_time)
	CommonActivityLogic.Update(self, now_time, elapse_time)
end

function KfYeZhanWangChengSceneLogic:CanGetMoveObj()
	return self:CheckActIsOpen()
end

function KfYeZhanWangChengSceneLogic:ShowPrepareTime(next_time1, next_time2)
	if self:CheckActIsOpen() then
		self:ChangeSelfAppearance()
		return
	end
	local time = TimeWGCtrl.Instance:GetServerTime()
	if time < next_time1 then
		time = next_time1
	elseif time < next_time2 then
		time = next_time2
	end
	if time <= TimeWGCtrl.Instance:GetServerTime() then
		return
	end

	CountDownManager.Instance:AddCountDown("YZWC_PREPARE_TIME", BindTool.Bind(self.UpdatePrepareTime, self),
		BindTool.Bind(self.CompleteTimeFunc, self), time, nil, 0.3)
end

function KfYeZhanWangChengSceneLogic:UpdatePrepareTime(elpase_time, total_time)
	local time = math.ceil(total_time - elpase_time)
	KuafuYeZhanWangChengWGCtrl.Instance:ShowTimePrepareView(time)
end

function KfYeZhanWangChengSceneLogic:CompleteTimeFunc()
	self:ChangeAllAppearance()
	self:ChangeSelfAppearance()
end

function KfYeZhanWangChengSceneLogic:SetActStatus()
	if self.has_set_act_state then
		return
	end
	self.has_set_act_state = true

	local act_statu = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.YEZHANWANGCHENG) or {}
	local act_statu2 = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG) or {}
	local scene_info = KuafuYeZhanWangChengWGData.Instance:NightFightSceneInfo()
	if scene_info.next_redistribute_time == 0 or KuafuYeZhanWangChengWGData.Instance:GetCurTurn() < 1 then
		-- 正式第一轮开始的时间
		self:ShowPrepareTime(scene_info.fight_start_time or 0, 0)
	else
		self:ShowPrepareTime(0, 0)
	end

	if act_statu.status == ACTIVITY_STATUS.OPEN or act_statu.status == ACTIVITY_STATUS.STANDY then
		self:OpenActivitySceneCd(ACTIVITY_TYPE.YEZHANWANGCHENG, true)
		self.act_id = ACTIVITY_TYPE.YEZHANWANGCHENG                              -- 特殊处理配置问题  跨服和本服的sceneid相同
	elseif act_statu2.status == ACTIVITY_STATUS.OPEN or act_statu2.status == ACTIVITY_STATUS.STANDY then
		self:OpenActivitySceneCd(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG, true)
		self.act_id = ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG
	end

	self:CheckSceneMapBlock()
end

function KfYeZhanWangChengSceneLogic:Enter(old_scene_type, new_scene_type)
	self.has_set_act_state = false
	-- self:SetActStatus()--设置活动状态和准备信息
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	if old_scene_type == new_scene_type then
		return
	end
	
	local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
	if not guaji_state then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end

	-- KuafuYeZhanWangChengWGCtrl.Instance:Close()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        MainuiWGCtrl.Instance:SetTaskContents(false)
        MainuiWGCtrl.Instance:SetOtherContents(true)
		-- MainuiWGCtrl.Instance:SetFBNameState(true, Language.YeZhanWangCheng.TipsTitle_1)
		MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetMianUITargetPos(nil, -40)
	end)

	if not self.def_wing then
		self.def_wing = NewAppearanceWGData.Instance:GetAdvancedImageCfgByType(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING).appe_image_id
	end

    KuafuYeZhanWangChengWGCtrl.Instance:OpenKuafuYeZhanWangChengFollow()
	TaskWGCtrl.Instance:AddFlyUpList(function ()
		if GuajiCache.guaji_type ~= GuajiType.Auto then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end)

	self.shield_pet_rule = SimpleRule.New(ShieldObjType.Pet, ShieldRuleWeight.Middle, function (obj)
		return not obj:IsOnwerPet()
	end)
	self.shield_pet_rule:Register()
	
	local view = KuafuYeZhanWangChengWGCtrl.Instance:GetKuafuYeZhanWangChengFollow()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
end

function KfYeZhanWangChengSceneLogic:CheckSceneMapBlock(scene_id)
	scene_id = scene_id or Scene.Instance:GetSceneId()
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if nil == act_scene then
		return
	end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(self.act_id)
	if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
		self.is_map_block = true
		self:SetSceneBlock()
		if nil == self.act_state_event then
			self.act_state_event = BindTool.Bind1(self.OnActivityStatus, self)
			ActivityWGData.Instance:NotifyActChangeCallback(self.act_state_event)
		end

		local map_block_bubble_cfg = SceneWGData.Instance:GetMapBlockBubbleCfg()
		if nil == self.role_bubble_timer and map_block_bubble_cfg then
			self.role_bubble_timer = GlobalTimerQuest:AddRunQuest(
				function()
					self:SetRoleBubble(scene_id)
				end, map_block_bubble_cfg.show_time)
		end
	else
		self:RemoveSceneBlock()
	end
end


function KfYeZhanWangChengSceneLogic:Out(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Out(self)
	if old_scene_type == new_scene_type then
		return
	end

	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	MainuiWGCtrl.Instance:SetMianUITargetPos(nil,0)
	KuafuYeZhanWangChengWGCtrl.Instance:CloseKuafuYeZhanWangChengFollow()
	KuafuYeZhanWangChengWGCtrl.Instance:ColseFollowView()
	KuafuYeZhanWangChengWGCtrl.Instance:CloseSeeGiftView()
	KuafuYeZhanWangChengWGData.Instance:ClearScore()

	local view = KuafuYeZhanWangChengWGCtrl.Instance:GetKuafuYeZhanWangChengFollow()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)

	ViewManager.Instance:Close(GuideModuleName.ZCStageView)

	if CountDownManager.Instance:HasCountDown("YZWC_PREPARE_TIME") then
		CountDownManager.Instance:RemoveCountDown("YZWC_PREPARE_TIME")
	end
	--if self.title_set_rule then
	--	self.title_set_rule:DeleteMe()
	--end
	self:ResumeSelfInfo()

	-- 清理传闻缓存信息  战场传闻太多，离开了场景还在发
	ChatWGData.Instance:ClearCacheChuangSystemList()
	TipWGCtrl.Instance:ClearSystemNotice()
end

function KfYeZhanWangChengSceneLogic:GuaiJiRoleUpdate(now_time, elapse_time)
	self:SetGuaiJi(GUAI_JI_TYPE.MONSTER)
end

-- 获取挂机打怪的敌人
function KfYeZhanWangChengSceneLogic:GetGuiJiMonsterEnemy()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local obj,dis = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
	if obj then
		return obj,dis
	else
		return Scene.Instance:SelectObjHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, SelectType.Enemy)
	end
end

function KfYeZhanWangChengSceneLogic:GetFbSceneMonsterListCfg( monsters_list_cfg )
	for k,v in pairs(monsters_list_cfg) do
		local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v.id]
		v.name = cfg.name
		v.level = cfg.level
	end
	return monsters_list_cfg, true
end

function KfYeZhanWangChengSceneLogic:GetFbSceneMonsterCfg( monsters_list_cfg )
	for k,v in pairs(monsters_list_cfg) do
		local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v.id]
		v.name = cfg.name
		v.level = cfg.level
	end
	return monsters_list_cfg, #monsters_list_cfg
end

function KfYeZhanWangChengSceneLogic:CheckActIsOpen()
	return KuafuYeZhanWangChengWGData.Instance:CheckActIsStart()
end

function KfYeZhanWangChengSceneLogic:ChangeAppearanceFunc(obj_id)
	if not self:CheckActIsOpen() then
		self:ChangeNoneFollowUi(obj_id)
		return
	end

	local self_info = KuafuYeZhanWangChengWGData.Instance:GetSetSelfInfo()
	local role = Scene.Instance:GetObj(obj_id)
	local target_side_type
    if role.vo.is_shadow == 1 then
    	--机器人
		local show_cfg = KuafuYeZhanWangChengWGData.Instance:GetTipsShowInfo()
		local min = show_cfg.shadow_min_score or 100
		local max = show_cfg.shadow_max_score or 120
		role:SetAttr("yzwc_ico", role.vo.special_param + 1)-- == 1 and 1 or 2)
		local score = GameMath.Rand(min,max)
		role:SetAttr("yzwc_score")
		self:ChangeFollowUi(role, true, score)
		target_side_type = role.vo.special_param
    else
        local app_show = role.vo.appearance
        self:ChangeParam(app_show)
        role:SetAttr("appearance", app_show)
        role:SetAttr("wing_appeid", self.def_wing)
		local side_type = math.floor(role.vo.special_param / 10000)
        role:SetAttr("yzwc_ico", side_type + 1)-- role.vo.special_param > 9999 and 1 or 2)
		role:SetAttr("yzwc_score")
		self:ChangeFollowUi(role, true)
		target_side_type = side_type
	end

	if self_info and self_info.side_type == target_side_type then
		local follow_ui = role and role:GetFollowUi()
		local follow_hp_bar = follow_ui and follow_ui:GetHpBar()
		if follow_hp_bar then
			follow_hp_bar:AddShieldRule(ShieldRuleWeight.Max, function()
				return true
			end)
		end
	end
	self:CanSelectObj(role)
end

function KfYeZhanWangChengSceneLogic:CanSelectObj(obj)
	if GuajiWGCtrl.Instance:GetCurSelectTargetObj() == nil and self:IsEnemy(obj) then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
	end
end
-- 角色是否是敌人--活动结束也会在里面打
function KfYeZhanWangChengSceneLogic:IsRoleEnemy(target_obj, main_role)
	if target_obj == nil then return false end

	local scene_info = KuafuYeZhanWangChengWGData.Instance:NightFightSceneInfo()
	if IsEmptyTable(scene_info) then
		return false
	end

	if TimeWGCtrl.Instance:GetServerTime() < (scene_info.fight_start_time or 0) then
		return false
	end

	local main_role = main_role or Scene.Instance:GetMainRole()
	local target_vo = target_obj:GetVo()

	if main_role:IsRealDead() then												-- 自己死亡
		return false
	end

	if target_obj:IsRealDead() then												-- 目标死亡
		return false
	end

	if target_vo then
		local self_info = KuafuYeZhanWangChengWGData.Instance:GetSetSelfInfo()
		if self_info.side_type == nil then
			return false
		end

		local target_side_type
		if target_vo.is_shadow ~= 1 then--不是机器人
			target_side_type = math.floor(target_vo.special_param / 10000) --target_vo.special_param > 9999 and 1 or 0 --大于9999是红方
		else
			target_side_type = target_vo.special_param
		end
		
		if target_side_type == self_info.side_type then
			target_obj:SetHpVisiable(false)
			return false
		end
	end

	local is_enemy = BaseSceneLogic.IsRoleEnemy(self, target_obj, main_role)
	if not is_enemy then
		target_obj:SetHpVisiable(false)
	end
	return is_enemy
end

function KfYeZhanWangChengSceneLogic:ChangeFollowUi(role, is_other, shadow_score)
	local follow_ui = role:GetFollowUi()
	if follow_ui then
		follow_ui:SetGuildName("")
		follow_ui:SetLoverName("")
		--local name = is_other and Language.YeZhanWangCheng.MysterMan or role.vo.name
		--local color = role.vo.special_param > 9999 and COLOR3B.RED or COLOR3B.BLUE
		--follow_ui:SetName(ToColorStr(name, color), role)
		follow_ui:SetRoleScore()
	end
end

function KfYeZhanWangChengSceneLogic:ChangeNoneFollowUi(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	if not role then
		return
	end
	role:SetAttr("yzwc_ico", 0)
	role:SetAttr("yzwc_score")
end

function KfYeZhanWangChengSceneLogic:ChangeParam(param)
	param.fashion_wuqi = 0
	param.fashion_body = 0
	param.shouhuan = 0
	param.tail = 0
	param.waist = 0
	param.mask = 0
	param.fashion_guanghuan = 0
	param.fashion_foot = 0
	param.qilinbi = -1
	param.wing_special_imageid = 0
	param.wing_jinhua_grade = 0
end

function KfYeZhanWangChengSceneLogic:ChangeAllAppearance()
	local obj_list = Scene.Instance:GetObjList()
	local role_info = GameVoManager.Instance:GetMainRoleVo()
	for i, v in pairs(obj_list) do
		if v:GetType() == SceneObjType.Role then
			local vo = v.vo
			if vo then
				--self:ChangeParam(vo.appearance)
				v:SetAttr("appearance", vo.appearance)
				v:SetAttr("wing_appeid", self.def_wing or 8100)
				self:ChangeFollowUi(v, vo.role_id ~= role_info.role_id)
			end
		end
	end
end

function KfYeZhanWangChengSceneLogic:ChangeSelfAppearance()
	local main_role = Scene.Instance:GetMainRole()
	local vo = main_role and main_role.vo
	if vo then
		self:AddYezhanDelay(nil, vo.used_title_list, nil)
		--self:ChangeParam(vo.appearance)
		--main_role:SetAttr("appearance", vo.appearance)
		main_role:SetAttr("used_title_list",{})
		--main_role:SetAttr("wing_appeid", self.def_wing or 8100)
		--main_role:UpdateAppearance()
	end
end

function KfYeZhanWangChengSceneLogic:IsSetAutoGuaji()
	return true
end

function KfYeZhanWangChengSceneLogic:AddYezhanDelay(appearance, title, wing_appeid)
	self.appearance = appearance and TableCopy(appearance) or self.appearance
	self.wing_appeid = wing_appeid
end

function KfYeZhanWangChengSceneLogic:ResumeSelfInfo()
	--if self.appearance then
	--	RoleWGData.Instance:SetAttr("appearance", self.appearance)
	--end
	--if self.wing_appeid then
	--	RoleWGData.Instance:SetAttr("wing_appeid", self.wing_appeid)
	--end
	TitleWGCtrl.Instance:SendGetTitleListReq()
end

function KfYeZhanWangChengSceneLogic:PlayAllChuShengEffect()
	local obj_list = Scene.Instance:GetObjList()
	for i, v in pairs(obj_list) do
		if v:GetType() == SceneObjType.Role then
			v:PlayChuShengEffect()
		end
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:PlayChuShengEffect()
	end
end

--重写方法，不显示退出的时间
function KfYeZhanWangChengSceneLogic:OpenActivitySceneCd(act_type, not_show_end)
	-- local out_time = KuafuYeZhanWangChengWGData.Instance:GetActTimeCfg(act_type)
	-- if out_time > TimeWGCtrl.Instance:GetServerTime() then
 --    	MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_time)
 --    end
	 MainuiWGCtrl.Instance:SetFbIconEndCountDown(0)
end

function KfYeZhanWangChengSceneLogic:GetGuajiPos()
	if not self:CheckActIsOpen() then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	self.last_guaji_pos = self.last_guaji_pos and KuafuYeZhanWangChengWGData.Instance:GetNextGuajiPos(self.last_guaji_pos)
			or KuafuYeZhanWangChengWGData.Instance:GetGuajiPos(main_role:GetLogicPos())
	return self.last_guaji_pos.fpos_x, self.last_guaji_pos.fpos_y
	-- local pos_x, pos_y = KuafuYeZhanWangChengWGData.Instance:GetRamdomGuajiPos()
	-- return pos_x, pos_y
end

-- function KfYeZhanWangChengSceneLogic:ForceShieldPet()

-- end

-- 此场景优先保证单位数量
function KfYeZhanWangChengSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function KfYeZhanWangChengSceneLogic:IsEnemyVisiblePriortiy()
	return true
end

function KfYeZhanWangChengSceneLogic:GetRoleNameBoardText(role_vo)
    local self_info = KuafuYeZhanWangChengWGData.Instance:GetSetSelfInfo()
	local target_side_type
	if role_vo.is_shadow ~= 1 then--不是机器人
		target_side_type = math.floor(role_vo.special_param / 10000)-- role_vo.special_param > 9999 and 1 or 0 --大于9999是红方
	else
		target_side_type = role_vo.special_param
	end

    local color = COLOR3B.WHITE
    local role_id = RoleWGData.Instance.role_vo.role_id
    if role_id ~= role_vo.role_id then
    	color = target_side_type ~= self_info.side_type and COLOR3B.RED or COLOR3B.WHITE
    end
	local t = {}
	t.color = color
	t.text =  role_vo.role_id == role_id and role_vo.name or Language.Common.MysteryMen
	return t
end