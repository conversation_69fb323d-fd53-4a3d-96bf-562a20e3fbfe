﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Timeline_AnimationTrackWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(UnityEngine.Timeline.AnimationTrack), typeof(UnityEngine.Timeline.TrackAsset));
		<PERSON><PERSON>Function("CreateClip", CreateClip);
		<PERSON><PERSON>RegFunction("CreateInfiniteClip", CreateInfiniteClip);
		<PERSON><PERSON>unction("CreateRecordableClip", CreateRecordableClip);
		<PERSON><PERSON>RegFunction("GatherProperties", GatherProperties);
		<PERSON>.RegFunction("New", _CreateUnityEngine_Timeline_AnimationTrack);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON>.<PERSON>ar("position", get_position, set_position);
		<PERSON><PERSON>("rotation", get_rotation, set_rotation);
		<PERSON><PERSON>("eulerAngles", get_eulerAngles, set_eulerAngles);
		<PERSON><PERSON>("trackOffset", get_trackOffset, set_trackOffset);
		<PERSON><PERSON>("matchTargetFields", get_matchTargetFields, set_matchTargetFields);
		L.RegVar("infiniteClip", get_infiniteClip, null);
		L.RegVar("avatarMask", get_avatarMask, set_avatarMask);
		L.RegVar("applyAvatarMask", get_applyAvatarMask, set_applyAvatarMask);
		L.RegVar("outputs", get_outputs, null);
		L.RegVar("inClipMode", get_inClipMode, null);
		L.RegVar("infiniteClipOffsetPosition", get_infiniteClipOffsetPosition, set_infiniteClipOffsetPosition);
		L.RegVar("infiniteClipOffsetRotation", get_infiniteClipOffsetRotation, set_infiniteClipOffsetRotation);
		L.RegVar("infiniteClipOffsetEulerAngles", get_infiniteClipOffsetEulerAngles, set_infiniteClipOffsetEulerAngles);
		L.RegVar("infiniteClipPreExtrapolation", get_infiniteClipPreExtrapolation, set_infiniteClipPreExtrapolation);
		L.RegVar("infiniteClipPostExtrapolation", get_infiniteClipPostExtrapolation, set_infiniteClipPostExtrapolation);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Timeline_AnimationTrack(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Timeline.AnimationTrack obj = new UnityEngine.Timeline.AnimationTrack();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Timeline.AnimationTrack.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateClip(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)ToLua.CheckObject<UnityEngine.Timeline.AnimationTrack>(L, 1);
			UnityEngine.AnimationClip arg0 = (UnityEngine.AnimationClip)ToLua.CheckObject(L, 2, typeof(UnityEngine.AnimationClip));
			UnityEngine.Timeline.TimelineClip o = obj.CreateClip(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateInfiniteClip(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)ToLua.CheckObject<UnityEngine.Timeline.AnimationTrack>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			obj.CreateInfiniteClip(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateRecordableClip(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)ToLua.CheckObject<UnityEngine.Timeline.AnimationTrack>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			UnityEngine.Timeline.TimelineClip o = obj.CreateRecordableClip(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GatherProperties(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)ToLua.CheckObject<UnityEngine.Timeline.AnimationTrack>(L, 1);
			UnityEngine.Playables.PlayableDirector arg0 = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 2);
			UnityEngine.Timeline.IPropertyCollector arg1 = (UnityEngine.Timeline.IPropertyCollector)ToLua.CheckObject<UnityEngine.Timeline.IPropertyCollector>(L, 3);
			obj.GatherProperties(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_position(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Vector3 ret = obj.position;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index position on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Quaternion ret = obj.rotation;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_eulerAngles(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Vector3 ret = obj.eulerAngles;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index eulerAngles on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_trackOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Timeline.TrackOffset ret = obj.trackOffset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index trackOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_matchTargetFields(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Timeline.MatchTargetFields ret = obj.matchTargetFields;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index matchTargetFields on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_infiniteClip(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.AnimationClip ret = obj.infiniteClip;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClip on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarMask(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.AvatarMask ret = obj.avatarMask;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarMask on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_applyAvatarMask(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			bool ret = obj.applyAvatarMask;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index applyAvatarMask on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_outputs(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			System.Collections.Generic.IEnumerable<UnityEngine.Playables.PlayableBinding> ret = obj.outputs;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index outputs on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_inClipMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			bool ret = obj.inClipMode;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index inClipMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_infiniteClipOffsetPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Vector3 ret = obj.infiniteClipOffsetPosition;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipOffsetPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_infiniteClipOffsetRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Quaternion ret = obj.infiniteClipOffsetRotation;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipOffsetRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_infiniteClipOffsetEulerAngles(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Vector3 ret = obj.infiniteClipOffsetEulerAngles;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipOffsetEulerAngles on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_infiniteClipPreExtrapolation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Timeline.TimelineClip.ClipExtrapolation ret = obj.infiniteClipPreExtrapolation;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipPreExtrapolation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_infiniteClipPostExtrapolation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Timeline.TimelineClip.ClipExtrapolation ret = obj.infiniteClipPostExtrapolation;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipPostExtrapolation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_position(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.position = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index position on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Quaternion arg0 = ToLua.ToQuaternion(L, 2);
			obj.rotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_eulerAngles(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.eulerAngles = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index eulerAngles on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_trackOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Timeline.TrackOffset arg0 = (UnityEngine.Timeline.TrackOffset)ToLua.CheckObject(L, 2, typeof(UnityEngine.Timeline.TrackOffset));
			obj.trackOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index trackOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_matchTargetFields(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Timeline.MatchTargetFields arg0 = (UnityEngine.Timeline.MatchTargetFields)ToLua.CheckObject(L, 2, typeof(UnityEngine.Timeline.MatchTargetFields));
			obj.matchTargetFields = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index matchTargetFields on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarMask(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.AvatarMask arg0 = (UnityEngine.AvatarMask)ToLua.CheckObject(L, 2, typeof(UnityEngine.AvatarMask));
			obj.avatarMask = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarMask on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_applyAvatarMask(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.applyAvatarMask = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index applyAvatarMask on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_infiniteClipOffsetPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.infiniteClipOffsetPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipOffsetPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_infiniteClipOffsetRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Quaternion arg0 = ToLua.ToQuaternion(L, 2);
			obj.infiniteClipOffsetRotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipOffsetRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_infiniteClipOffsetEulerAngles(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.infiniteClipOffsetEulerAngles = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipOffsetEulerAngles on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_infiniteClipPreExtrapolation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Timeline.TimelineClip.ClipExtrapolation arg0 = (UnityEngine.Timeline.TimelineClip.ClipExtrapolation)ToLua.CheckObject(L, 2, typeof(UnityEngine.Timeline.TimelineClip.ClipExtrapolation));
			obj.infiniteClipPreExtrapolation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipPreExtrapolation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_infiniteClipPostExtrapolation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.AnimationTrack obj = (UnityEngine.Timeline.AnimationTrack)o;
			UnityEngine.Timeline.TimelineClip.ClipExtrapolation arg0 = (UnityEngine.Timeline.TimelineClip.ClipExtrapolation)ToLua.CheckObject(L, 2, typeof(UnityEngine.Timeline.TimelineClip.ClipExtrapolation));
			obj.infiniteClipPostExtrapolation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index infiniteClipPostExtrapolation on a nil value");
		}
	}
}

