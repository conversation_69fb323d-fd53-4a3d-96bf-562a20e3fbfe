--跨服3v3主界面(含有页签：跨服3v3、战队信息)

KF3v3View = KF3v3View or BaseClass(SafeBaseView)
function KF3v3View:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:SetMaskBg(false)
	self.focus_index = TabIndex.kf_pvp_3v3info
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "3v3_render")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function KF3v3View:__delete()

end

function KF3v3View:ReleaseCallBack()
	RemindManager.Instance:UnBind(self.remind_change)
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	self:Delete3V3()

	if self.main_zhan_dui_info_change_event then
		GlobalEventSystem:UnBind(self.main_zhan_dui_info_change_event)
		self.main_zhan_dui_info_change_event = nil
	end
end

function KF3v3View:LoadCallBack()
	self:Init3V3()
	--self:InitZhanDui()
	self.main_zhan_dui_info_change_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_Info_Change,
		BindTool.Bind(self.OnMainZhanDuiInfoChange, self))
	XUI.AddClickEventListener(self.node_list.btn_open_info, BindTool.Bind(self.ClickOpenZhanDuiInfo, self))

	local bundle, asset = ResPath.GetRawImagesPNG("a3_fb_bj_4")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function KF3v3View:OpenCallBack()

end

function KF3v3View:OnFlush()
	if self.focus_index == TabIndex.kf_pvp_3v3info then
		self:Open3V3Info()
	elseif self.focus_index == TabIndex.kf_pvp_zhandui_info then
		self:Open3V3Info() --刷新界面信息，进入/创建战队时
		self:OpenZhanduiInfo()
	end

	self.node_list.title_view_name.text.text = Language.KuafuPVP.KuaFu3V3
	local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
	self.node_list.btn_open_info:SetActive(is_in_zhandui)
	self.node_list.red_point:SetActive(ZhanDuiWGData.Instance:GetApplyCount() > 0)
end

function KF3v3View:CloseCallBack()
	if self.close_param and self.close_param.is_goto_war then
		Field1v1WGCtrl.Instance:CloseView()
	else
		if ViewManager.Instance:IsOpen(GuideModuleName.ActJjc) then
			Field1v1WGCtrl.Instance:Open(TabIndex.arena_enter)
		end
	end
	self.close_param = nil
end

function KF3v3View:OnMainZhanDuiInfoChange(notify_reason)
	if notify_reason == NotifyZhanduiInfoReason.JoinZhandui then -- 玩家自己加入战队
		KF3V3WGCtrl.Instance:Open3V3View(TabIndex.kf_pvp_zhandui_info)
		self:Flush()
	elseif notify_reason == NotifyZhanduiInfoReason.LeaveZhandui
		or notify_reason == NotifyZhanduiInfoReason.BeKicked then -- 主动离开战队、获取被踢出
		self.focus_index = TabIndex.kf_pvp_3v3info
		self:Flush()
	end
end

function KF3v3View:OpenZhanduiInfo()
	self.focus_index = TabIndex.kf_pvp_zhandui_info
	--self.node_list.zhandui_info:SetActive(true)
	--self:ShowIndexZhanDui()
	FunOpen.Instance:OpenViewByName(GuideModuleName.KF3V3ZhanDuiView)
end

function KF3v3View:Open3V3Info()
	self.focus_index = TabIndex.kf_pvp_3v3info
	--self.node_list.zhandui_info:SetActive(false)
	self:ShowIndex3V3()
end

function KF3v3View:ClickOpenZhanDuiInfo()
	self:OpenZhanduiInfo()
end

function KF3v3View:SetOpenData(index)
	self.focus_index = index
	self:Flush()
end
