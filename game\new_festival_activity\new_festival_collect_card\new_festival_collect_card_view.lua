local collect_card_timer_key = "collect_card_timer"

function NewFestivalActivityView:LoadIndexCallBackCollectCard()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.NEW_JRHD_JFHD, OA_COLLECT_CARD_OP_TYPE.INFO)

    if not self.jf_card_list then
        self.jf_card_list = {}
        for i = 1, 5 do
            self.jf_card_list[i] = NewJRCollectCardRender.New(self.node_list.jf_card_list:FindObj("card_item" .. i))
            self.jf_card_list[i]:SetClickCallBack(BindTool.Bind(self.ShowRequestOrGivePanel, self))
        end
    end

    if not self.jf_reward_list then
        self.jf_reward_list = AsyncListView.New(ItemCell, self.node_list.jf_reward_list)
        self.jf_reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.jf_get_btn, BindTool.Bind(self.OnClickNewJRCollectCardGetBtn, self))

    self:InitNFACollectCardImgAndText()
    self:CreateCollectCardCountDown()
end

function NewFestivalActivityView:ReleaseCollectCard()
    if CountDownManager.Instance:HasCountDown(collect_card_timer_key) then
        CountDownManager.Instance:RemoveCountDown(collect_card_timer_key)
    end

    if self.jf_card_list then
        for k, v in pairs(self.jf_card_list) do
            v:DeleteMe()
            v = nil
        end

        self.jf_card_list = nil
    end

    if self.jf_reward_list then
        self.jf_reward_list:DeleteMe()
        self.jf_reward_list = nil
    end
end

function NewFestivalActivityView:OnFlushCollectCard(param_t)
    for k, v in pairs(param_t) do
        if v == "all" then
            self:FlushNewJRCollectCardMidPanel()
            self:FlushNewJRCollectCardRightPanel()
        elseif v == "item_add" then
            self:FlushNewJRCollectCardMidPanel()
            self:FlushNewJRCollectCardNeedItemKindSumPart()
        end
    end
end

function NewFestivalActivityView:FlushNewJRCollectCardMidPanel()
    local item_list = NewFestivalCollectCardWGData.Instance:GetCurGradeItemList()
    if IsEmptyTable(item_list) then
        return
    end

    for k, v in pairs(self.jf_card_list) do
        local data = item_list[k]
        if data then
            v:SetData(data)
        end
    end
end

function NewFestivalActivityView:FlushNewJRCollectCardRightPanel()
    self:FlushNewJRCollectCardNeedItemKindSumPart()

    local reward_list = NewFestivalCollectCardWGData.Instance:GetCurActivityCfg().reward_item_show
    if reward_list then
        self.jf_reward_list:SetDataList(reward_list)
    end
end

function NewFestivalActivityView:FlushNewJRCollectCardNeedItemKindSumPart()
    local redemption_num = NewFestivalCollectCardWGData.Instance:GetRedemptionNum()
    local max_redemption_num = NewFestivalCollectCardWGData.Instance:GetCurActivityOpenParamCfg().redemption_num or 0
    self.node_list.jf_item_num.text.text = string.format(Language.CollectCard.TodayCanExchangeCount, redemption_num, max_redemption_num)

    local item_kind_sum = NewFestivalCollectCardWGData.Instance:GetItemKindSum()
    local item_list = NewFestivalCollectCardWGData.Instance:GetCurGradeItemList()
    self.node_list.jf_get_btn_remind:CustomSetActive(redemption_num < max_redemption_num and item_kind_sum >= #item_list)
end

function NewFestivalActivityView:OnClickNewJRCollectCardGetBtn()
    local item_kind_sum = NewFestivalCollectCardWGData.Instance:GetItemKindSum()
    local item_list = NewFestivalCollectCardWGData.Instance:GetCurGradeItemList()
    if item_kind_sum < #item_list then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CollectCard.ItemNotEnough)
        return
    end

    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.NEW_JRHD_JFHD, OA_COLLECT_CARD_OP_TYPE.GET_REWARD)
end

function NewFestivalActivityView:ShowRequestOrGivePanel(show_panel_type, item_id)
    NewFestivalCollectCardWGCtrl.Instance:OpenCollectCardInfoView(show_panel_type, item_id)
end

function NewFestivalActivityView:InitNFACollectCardImgAndText()
    local get_btn_bundle, get_btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jrsc_btn1")
    self.node_list.jf_get_btn.image:LoadSprite(get_btn_bundle, get_btn_asset, function()
        self.node_list.jf_get_btn.image:SetNativeSize()
    end)

    local benediction_bundle, benediction_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jf_icon")
    self.node_list.jf_benediction_img.image:LoadSprite(benediction_bundle, benediction_asset, function()
        self.node_list.jf_benediction_img.image:SetNativeSize()
    end)

    local time_bg_bundle, time_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_dl_di2")
    self.node_list.jf_time_bg.image:LoadSprite(time_bg_bundle, time_bg_asset, function()
        self.node_list.jf_time_bg.image:SetNativeSize()
    end)

    local title_bundle, title_asset = ResPath.GetNewFestivalRawImages("jf_title")
    self.node_list.jf_title.raw_image:LoadSprite(title_bundle, title_asset, function()
        self.node_list.jf_title.raw_image:SetNativeSize()
    end)

    local reward_bg_bundle, reward_bg_asset = ResPath.GetNewFestivalRawImages("jf_di2")
    self.node_list.jf_reward_bg.raw_image:LoadSprite(reward_bg_bundle, reward_bg_asset, function()
        self.node_list.jf_reward_bg.raw_image:SetNativeSize()
    end)

    local line_bundle, line_asset = ResPath.GetNewFestivalRawImages("jf_line")
    self.node_list.jf_line.raw_image:LoadSprite(line_bundle, line_asset, function()
        self.node_list.jf_line.raw_image:SetNativeSize()
    end)

    local bg_bundle, bg_asset = ResPath.GetNewFestivalRawImages("jf_bg")
    self.node_list.jf_bg.raw_image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list.jf_bg.raw_image:SetNativeSize()
    end)

    self.node_list.jf_rule_content.text.text = Language.CollectCard.RuleContent
    self.node_list.jf_rule_scroll.scroll_rect.verticalNormalizedPosition = 1

    local client_show_cfg = NewFestivalActivityWGData.Instance:GetCollectCardOtherCfg()
    self.jf_time_color = client_show_cfg and client_show_cfg.time_part_color or COLOR3B.D_GREEN
    self.node_list.jf_rule_content.text.color = Str2C3b(client_show_cfg.rule_color)
    self.node_list.jf_get_btn_txt.text.color = Str2C3b(client_show_cfg.get_btn_color)
    self.node_list.jf_rule_title.text.color = Str2C3b(client_show_cfg.rule_color)
    self.node_list.jf_time.text.color = Str2C3b(client_show_cfg.timer_color)
    self.node_list.jf_reward_title.text.color = Str2C3b(client_show_cfg.reward_title_color)
    self.node_list.jf_item_num.text.color = Str2C3b(client_show_cfg.item_num_color)
end

function NewFestivalActivityView:CreateCollectCardCountDown()
    if CountDownManager.Instance:HasCountDown(collect_card_timer_key) then
        return
    end

    local time, total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.NEW_JRHD_JFHD)
    if time > 0 then
        self:CollectCardUpdateCountDown(total_time - time, total_time)
        CountDownManager.Instance:AddCountDown(collect_card_timer_key, BindTool.Bind1(self.CollectCardUpdateCountDown, self), BindTool.Bind1(self.CollectCardCompleteCallBack, self), nil, time, 1)
    else
        self:CollectCardCompleteCallBack()
    end
end

function NewFestivalActivityView:CollectCardUpdateCountDown(elapse_time, total_time)
    if self.node_list and self.node_list.jf_time then
        -- self.node_list.jf_time.text.text = string.format(Language.CollectCard.ActTime, self.jf_time_color, TimeUtil.FormatSecondDHM8(total_time - elapse_time))
        self.node_list.jf_time.text.text = string.format(Language.NewFestivalActivity.ActTime, self.jf_time_color, TimeUtil.FormatSecondDHM8(total_time - elapse_time))
    end
end

function NewFestivalActivityView:CollectCardCompleteCallBack()
    if self.node_list and self.node_list.jf_time then
        self.node_list.jf_time.text.text = Language.Common.ActivityIsEnd
    end
end

-----------------------NewJRCollectCardRender集福道具render
NewJRCollectCardRender = NewJRCollectCardRender or BaseClass(BaseRender)

function NewJRCollectCardRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.cell_pos)
        self.item_cell:SetHideRightDownBgLessNum(0)
    end

    XUI.AddClickEventListener(self.node_list.request_btn, BindTool.Bind(self.ClickCallBack, self, 1))
    XUI.AddClickEventListener(self.node_list.give_btn, BindTool.Bind(self.ClickCallBack, self, 2))

    self:InitImgAndText()
end

function NewJRCollectCardRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function NewJRCollectCardRender:OnFlush()
    if not self.data then
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data)
    self.item_cell:SetData({item_id = self.data, num = item_num})

    local has_req = NewFestivalCollectCardWGData.Instance:GetHasRequestByItemId(self.data)
    local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.NEW_JRHD_JFHD)
    self.node_list.give_btn_remind:CustomSetActive(has_req and act_is_open)
end

function NewJRCollectCardRender:ClickCallBack(show_panel_type)
    if self.click_callback ~= nil then
        self.click_callback(show_panel_type, self.data)
    end
end

function NewJRCollectCardRender:InitImgAndText()
    local bg_bundle, bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jf_di")
    self.node_list.bg.image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list.bg.image:SetNativeSize()
    end)

	local btn_bundle, btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jf_btn2")
    self.node_list.request_btn_img.image:LoadSprite(btn_bundle, btn_asset, function()
        self.node_list.request_btn_img.image:SetNativeSize()
    end)

    self.node_list.give_btn_img.image:LoadSprite(btn_bundle, btn_asset, function()
        self.node_list.give_btn_img.image:SetNativeSize()
    end)

    local client_show_cfg = NewFestivalActivityWGData.Instance:GetCollectCardOtherCfg()
    self.node_list.request_btn_txt.text.color = Str2C3b(client_show_cfg.btn_color1)
    self.node_list.give_btn_txt.text.color = Str2C3b(client_show_cfg.btn_color2)
end