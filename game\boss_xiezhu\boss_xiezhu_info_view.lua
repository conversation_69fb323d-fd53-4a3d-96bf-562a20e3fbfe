
BossXiezhuInfoView = BossXiezhuInfoView or BaseClass(SafeBaseView)
function BossXiezhuInfoView:__init()
	self:AddViewResource(0, "uis/view/boss_xiezhu_ui_prefab", "boss_xiezhu_info_view")
	self.view_layer = UiLayer.MainUIHigh
	self.is_safe_area_adapter = true
end

function BossXiezhuInfoView:__delete()
end

function BossXiezhuInfoView:ReleaseCallBack()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end

	if self.xiezhu_hurt_list then
		self.xiezhu_hurt_list:DeleteMe()
		self.xiezhu_hurt_list = nil
	end

	if CountDownManager.Instance:HasCountDown("boss_xiezhu_send_tiemr") then
		CountDownManager.Instance:RemoveCountDown("boss_xiezhu_send_tiemr")
	end

	if nil ~= self.main_menu_icon_change then
		GlobalEventSystem:UnBind(self.main_menu_icon_change)
		self.main_menu_icon_change = nil
	end

end

function BossXiezhuInfoView:LoadCallBack(index)
	self.show_target_alpah = true
	XUI.AddClickEventListener(self.node_list["xiezhu_list_btn"], BindTool.Bind(self.ClickXiezhuListBtn,self))
	XUI.AddClickEventListener(self.node_list["cancel_xiezhu_btn"], BindTool.Bind(self.ClickCancelXiezhuBtn,self))
	XUI.AddClickEventListener(self.node_list["go_xiezhu_btn"], BindTool.Bind(self.ClickGoXiezhuBtn,self))
	XUI.AddClickEventListener(self.node_list["qingqiu_xiezhu_btn"], BindTool.Bind(self.ClickQingQiuXiezhuBtn,self))
	if self.node_list["go_boss_btn"] then
		XUI.AddClickEventListener(self.node_list["go_boss_btn"], BindTool.Bind(self.ClickGoBossBtn,self))
	end
	XUI.SetGraphicGrey(self.node_list["qingqiu_xiezhu_btn"], false)
	self.node_list["jiantou_down"]:SetActive(true)
	self.node_list["jiantou_up"]:SetActive(false)
	self.xiezhu_hurt_list = AsyncListView.New(BossXiezhuHurtListItem,self.node_list["xiezhu_hurt_list"])
	self.head_cell = BaseHeadCell.New(self.node_list["head_pos"])

	self:FlushSendTimer()

	self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.MainMenuIconChangeEvent, self))

end

function BossXiezhuInfoView:MainMenuIconChangeEvent(isOn)
	if isOn then
		if self.node_list["xiezhu_role_panel"] then
			self.node_list["xiezhu_role_panel"].canvas_group:DoAlpha(1, 0, 0.8)
			self.node_list["xiezhu_role_panel"].canvas_group.blocksRaycasts = false
		end
		if self.node_list["xiezhu_target_panel"] then
			self.node_list["xiezhu_target_panel"].canvas_group:DoAlpha(1, 0, 0.8)
			self.node_list["xiezhu_target_panel"].canvas_group.blocksRaycasts = false
		end
	else
		if self.node_list["xiezhu_role_panel"] then
			self.node_list["xiezhu_role_panel"].canvas_group.blocksRaycasts = true
			self.node_list["xiezhu_role_panel"].canvas_group:DoAlpha(0, 1, 0.8)
		end
		if self.node_list["xiezhu_target_panel"] then
			self.node_list["xiezhu_target_panel"].canvas_group.blocksRaycasts = true
			self.node_list["xiezhu_target_panel"].canvas_group:DoAlpha(0, 1, 0.8)
		end
	end	
end

function BossXiezhuInfoView:FlushSendTimer()
	local sned_timer = BossXiezhuWGData.Instance:GetSendTimer()
	local server_timer = TimeWGCtrl.Instance:GetServerTime()
	local lerp_timer = sned_timer - server_timer
	if lerp_timer > 0 then
		if CountDownManager.Instance:HasCountDown("boss_xiezhu_send_tiemr") then
			CountDownManager.Instance:RemoveCountDown("boss_xiezhu_send_tiemr")
		end
		self:UpdateSendTimer(0,lerp_timer)
		CountDownManager.Instance:AddCountDown("boss_xiezhu_send_tiemr", BindTool.Bind(self.UpdateSendTimer,self), BindTool.Bind(self.SendTimerCallBack,self), nil, lerp_timer, 1)
	else
		self.node_list["qiuzhu_timer"].text.text = ""
		self.node_list["qiuzhu_timer_img"].image.fillAmount = 0
	end
end

function BossXiezhuInfoView:UpdateSendTimer(now_time, elapse_time)
	local time = elapse_time - now_time
	time = math.ceil(time)
	self.node_list["qiuzhu_timer"].text.text = string.format(Language.BossXiezhu.SendTimerStr,time)
	self.node_list["qiuzhu_timer_img"].image.fillAmount = (time / 60)
	local is_grey = XUI.IsGraphicGrey(self.node_list["qingqiu_xiezhu_btn"])
	if not is_grey then
		XUI.SetGraphicGrey(self.node_list["qingqiu_xiezhu_btn"], true)
	end
end

function BossXiezhuInfoView:SendTimerCallBack()
	self.node_list["qiuzhu_timer"].text.text = ""
	self.node_list["qiuzhu_timer_img"].image.fillAmount = 0
	XUI.SetGraphicGrey(self.node_list["qingqiu_xiezhu_btn"], false)
end

function BossXiezhuInfoView:OnFlush()
	local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
	local is_show_target = state == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER
	self.node_list["qingqiu_xiezhu_btn"]:SetActive(not is_show_target)
	self.node_list["qiuzhu_timer"]:SetActive(not is_show_target)
	self.node_list["xiezhu_role_panel"]:SetActive(not is_show_target)
	self.node_list["xiezhu_target_panel"]:SetActive(is_show_target)
	self.node_list["go_boss_btn"]:SetActive(not is_show_target)

	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if parent and parent:GetActive() then
		self.node_list["xiezhu_btns"]:SetActive(true)
	else
		self.node_list["xiezhu_btns"]:SetActive(false)
	end
	
	if is_show_target then
		self:FlushTargetPanel()
	else
		self:FlushRolePanel()
	end
end

function BossXiezhuInfoView:FlushRolePanel()
	local help_me_list = BossXiezhuWGData.Instance:GetHelpMeList()
	self.node_list["xiezhu_role_panel"]:SetActive(not IsEmptyTable(help_me_list))
	self.xiezhu_hurt_list:SetDataList(help_me_list)
end

function BossXiezhuInfoView:FlushTargetPanel()
	local target_info = BossXiezhuWGData.Instance:GetTargetRoleNode()
	if not IsEmptyTable(target_info) then
		local data = {}
		data.role_id = target_info.uid
		data.sex = target_info.sex
		data.prof = target_info.prof
		self.head_cell:SetData(data)
		self.node_list["target_role_name"].text.text = target_info.name
		local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(target_info.monster_id)
		local boss_jieshu = boss_cfg and boss_cfg.boss_jieshu or 0
		self.node_list["target_boss_name"].text.text = string.format(Language.BossXiezhu.XiezhuTargetBossName,boss_cfg and boss_cfg.name or "",boss_jieshu)
	end
end



---------------Button---------------

function BossXiezhuInfoView:ClickXiezhuListBtn()
	local is_active = self.node_list["xiezhu_list_panel"]:GetActive()
	self.node_list["xiezhu_list_panel"]:SetActive(not is_active)
	self.node_list["jiantou_down"]:SetActive(not is_active)
	self.node_list["jiantou_up"]:SetActive(is_active)
end

function BossXiezhuInfoView:ClickCancelXiezhuBtn()
	local ok_fun = function ()
		local target_info = BossXiezhuWGData.Instance:GetTargetRoleNode()
		local role_id = target_info and target_info.uid or 0
		local opera_type = MONSTER_ASSIST_REQ.MONSTER_ASSIST_REQ_CANCEL_REPLY_HELP
		BossXiezhuWGCtrl.Instance:SendCSMonsterAssistReq(opera_type)
		BossXiezhuWGCtrl.Instance:RemoveInvokeRoleInfo(role_id)
		BossXiezhuWGData.Instance:SetGoXiezhuRoleId(0)
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
	end

	local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
	if state == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER then
		TipWGCtrl.Instance:OpenAlertTips(Language.BossXiezhu.CurXiezhuIng,ok_fun)
	end
end

function BossXiezhuInfoView:ClickGoXiezhuBtn()
	local target_info = BossXiezhuWGData.Instance:GetTargetRoleNode()
	local role_id = target_info and target_info.uid or 0
	BossXiezhuWGData.Instance:SetGoXiezhuRoleId(role_id)
	BossXiezhuWGCtrl.Instance:GoToXizhuBoss()
end

function BossXiezhuInfoView:ClickQingQiuXiezhuBtn()
	local server_timer = TimeWGCtrl.Instance:GetServerTime()
	local send_timer = BossXiezhuWGData.Instance:GetSendTimer()
	local lerp_timer = send_timer - server_timer
	if lerp_timer > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OperateFrequencyTip)
		return
	end

	local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
	if state == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.CurNotSendXiezhu)
		return
	end
	local target_obj = GuajiCache.target_obj
	local all_hurt_info = BossAssistWGData.Instance:GetHurtInfo()
	local opera_type = MONSTER_ASSIST_REQ.MONSTER_ASSIST_REQ_INVODE
	local monster_id = all_hurt_info.target_boss_id
	local monster_key = all_hurt_info.monster_key
	if target_obj and not target_obj:IsDeleted() and target_obj:IsBoss() then
		monster_id = target_obj:GetMonsterId()
		monster_key = target_obj:GetMonsterKey()
	end
	local param_0 = ASSIST_BOSS_TYPE.ASSIST_BOSS_TYPE_CROSS_BOSS   --蛮荒神兽
	local param_1 = monster_id
	local param_2 = monster_key
	BossXiezhuWGCtrl.Instance:SendCSMonsterAssistReq(opera_type,param_0,param_1,param_2)
	BossXiezhuWGData.Instance:SetSendTimer(server_timer + 60)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.IsSendXieZhu)
	self:SendQingqiuXiezhuMsg()
	self:FlushSendTimer()
end

function BossXiezhuInfoView:SendQingqiuXiezhuMsg()
	local time = TimeWGCtrl.Instance:GetServerTime()
	local scene_id = Scene.Instance:GetSceneId()
	local all_hurt_info = BossAssistWGData.Instance:GetHurtInfo()
	local target_obj = GuajiCache.target_obj
	local all_hurt_info = BossAssistWGData.Instance:GetHurtInfo()
	local opera_type = MONSTER_ASSIST_REQ.MONSTER_ASSIST_REQ_INVODE
	local monster_id = all_hurt_info.target_boss_id
	local monster_key = all_hurt_info.monster_key
	if target_obj and not target_obj:IsDeleted() and target_obj:IsBoss() then
		monster_id = target_obj:GetMonsterId()
		monster_key = target_obj:GetMonsterKey()
	end
	-- local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_id)
	-- local boss_name = boss_cfg and boss_cfg.name or ""
	local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local str = string.format(Language.BossXiezhu.SendXiezhuMsg,scene_id,monster_id,uid)
	ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.WORLD, str, CHAT_CONTENT_TYPE.TEXT,nil,nil,true)
end

function BossXiezhuInfoView:ClickGoBossBtn()
	local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
    if state == ASSIST_STATUS.ASSIST_STATUS_NOTHING then
        local all_hurt_info = BossAssistWGData.Instance:GetHurtInfo()
        local cur_boss_id = all_hurt_info.target_boss_id
        local obj = Scene.Instance:GetMonstObjByMonstID(cur_boss_id)
        if obj ~= nil and not obj:IsDeleted() then
            GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
    		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
    		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    	end
		return
	end

	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	MoveCache.SetEndType(MoveEndType.FightByMonsterId)
	local target_info = BossXiezhuWGData.Instance:GetTargetRoleNode()
	if not IsEmptyTable(target_info) then
		local pos_x = 0
		local pos_y = 0
		local scene_id = target_info.scene_id
		if target_info.boss_type == ASSIST_BOSS_TYPE.ASSIST_BOSS_TYPE_CROSS_BOSS then
			local boss_cfg = BossWGData.Instance:GetBossInfoByBossId(target_info.monster_id)
			pos_x = boss_cfg.flush_pos_x
			pos_y = boss_cfg.flush_pos_y
		end

		MoveCache.param1 = target_info.monster_id
		MoveCache.param2 = target_info.monster_key
		if pos_x <= 0 and pos_y <= 0 then
			print_error(">>>>>>>>>>boss 配置获取坐标为空!!!",target_info)
		end

		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
			local scene_logic = Scene.Instance:GetSceneLogic()
			if scene_logic ~= nil then
				scene_logic:ClearGuaJiInfo()
				local mian_role = Scene.Instance:GetMainRole()
				if mian_role ~= nil and not mian_role:IsDeleted() then
					local range = BossWGData.Instance:GetMonsterRangeByid(target_info.monster_id)
					local m_pos_x, m_pos_y = mian_role:GetLogicPos()
					if GameMath.GetDistance(pos_x, pos_y, m_pos_x, m_pos_y, false) <= range * range then
						scene_logic:SetGuaJiInfoPos(m_pos_x, m_pos_y, range, target_info.monster_id)
					end
				end
			end
		end)

		local range = BossWGData.Instance:GetMonsterRangeByid(target_info.monster_id)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, range)
	end
end

function BossXiezhuInfoView:ShowOrHideXiezhuInfoBtn(enable)
	self.show_or_hide_btn = enable
	if self.node_list["xiezhu_btns"] then
		self.node_list["xiezhu_btns"]:SetActive(not enable)
	end
end

------------------------------------------------------------------------------------------------
BossXiezhuHurtListItem = BossXiezhuHurtListItem or BaseClass(BaseRender)

function BossXiezhuHurtListItem:__init()
end

function BossXiezhuHurtListItem:__delete()
end

function BossXiezhuHurtListItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	
	self.node_list["role_name"].text.text = self.data.name
	if self.data.hurt <= 0 then
		self.node_list["hurt_value"].text.text = Language.BossXiezhu.CurNotHurt
	else
		self.node_list["hurt_value"].text.text = CommonDataManager.ConverNumber(self.data.hurt)
	end
	local help_me_list = BossXiezhuWGData.Instance:GetHelpMeList()
	local max_info = help_me_list[1]
	if not IsEmptyTable(max_info) then
		local max_hurt_val = max_info.hurt <= 0 and 9999999 or max_info.hurt
		local per = self.data.hurt / max_hurt_val
		self.node_list["hurt_per"].slider.value = per
	end
end
