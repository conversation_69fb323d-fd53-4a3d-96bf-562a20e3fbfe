﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_UI_Selectable_TransitionWrap
{
	public static void Register(LuaState L)
	{
		L.BeginEnum(typeof(UnityEngine.UI.Selectable.Transition));
		<PERSON><PERSON>("None", get_None, null);
		<PERSON><PERSON>("ColorTint", get_ColorTint, null);
		<PERSON><PERSON>("SpriteSwap", get_SpriteSwap, null);
		<PERSON><PERSON>("Animation", get_Animation, null);
		<PERSON><PERSON>RegFunction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UnityEngine.UI.Selectable.Transition>.Check = CheckType;
		StackTraits<UnityEngine.UI.Selectable.Transition>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.UI.Selectable.Transition arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.UI.Selectable.Transition), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_None(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.UI.Selectable.Transition.None);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ColorTint(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.UI.Selectable.Transition.ColorTint);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SpriteSwap(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.UI.Selectable.Transition.SpriteSwap);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Animation(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.UI.Selectable.Transition.Animation);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.UI.Selectable.Transition o = (UnityEngine.UI.Selectable.Transition)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

