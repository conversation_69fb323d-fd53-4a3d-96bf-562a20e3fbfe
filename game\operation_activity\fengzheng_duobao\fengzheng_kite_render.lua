FZKiteRender = FZKiteRender or BaseClass(BaseRender)
function FZKiteRender:__init()
    if not self.func_list then
        self.func_list = {}
    end
end

function FZKiteRender:__delete()
    if self.cell then
        self.cell:DeleteMe()
        self.cell = nil
    end

    self.is_best = nil
    self.kite_state = nil
    self.func_list = {}

    if self.effect_disable_timequest then
        GlobalTimerQuest:CancelQuest(self.effect_disable_timequest)
        self.effect_disable_timequest = nil
    end
    if self.RoteTween then
        self.RoteTween:Kill()
        self.RoteTween = nil
    end

    if self.DOAnchorPosYTween then
        self.DOAnchorPosYTween:Kill()
        self.DOAnchorPosYTween = nil
    end

    if self.DoAlphaTween then
        self.DoAlphaTween:Kill()
        self.DoAlphaTween = nil
    end
end

function FZKiteRender:LoadCallBack()
    self.kite_state = nil
    self.load_callback = true
    if self.node_list["fz_effectobj"] then
        self.node_list["fz_effectobj"]:SetActive(false)
    end
    self:DoDelayFunc()
end

function FZKiteRender:OnFlush()
    local view_info_cfg = FZGetRewardWGData.Instance:GetViewCfgByInterface()
    if nil == view_info_cfg or IsEmptyTable(view_info_cfg) then
        return
    end
    local reward_info = FZGetRewardWGData.Instance:GetRewardInfoByRewardID(self.data)
    self:GetCell()

    self.cell:SetData(reward_info.reward_item)
    local item_id = reward_info.reward_item.item_id
    local item_cell_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    local item_color = 1
    if item_cell_cfg and item_cell_cfg.color then
        item_color = item_cell_cfg.color
    end
    local kite_bg = view_info_cfg.kite_pref
    local ass = kite_bg .. item_color
    self.small_ske_hudie = AllocAsyncLoader(self, "yunying_small_hudie")
    self.small_ske_hudie:SetIsUseObjPool(true)
    self.small_ske_hudie:SetParent(self.node_list["kite_pos"].transform)
    self.small_ske_hudie:Load("uis/view/operation_activity_ui/fengzheng_ui_prefab", ass)

    self:SetPosition(reward_info.x_pos, reward_info.y_pos)

    self.is_best = reward_info.is_best == 1
    if reward_info.is_l_kite == -1 then
        self:SetCenterImg(true)
    else
        self:SetKiteImg(reward_info.is_l_kite, reward_info.reward_item)
    end
end

function FZKiteRender:GetCell()
    if not self.cell then
        self.cell = ItemCell.New(self.node_list["fz_cell"])
    end
end

function FZKiteRender:GetKiteState()
    return self.kite_state and true or false
end

function FZKiteRender:SetKiteState(state)
    if not self.load_callback then
        self:AddDelay(BindTool.Bind(self.SetKiteState, self, state))
        return
    end
    self.kite_state = state
    self.node_list["fz_kite_root"]:SetActive(state)
    if state then
        self.node_list["fz_kite_root"].canvas_group.alpha = 1
         self.node_list["fz_kite_root"].rect.localRotation = Quaternion.Euler(0,0,0)
    end
end

function FZKiteRender:SetCenterImg(state)
    self.node_list["fz_frame"]:SetActive(state)
    self.node_list["fz_cell"].rect.localScale = Vector3(1,1,1)
end

function FZKiteRender:SetPosition(x, y)
    self.node_list["fz_kite_root"].rect.anchoredPosition = Vector2(x, y)
end

function FZKiteRender:SetKiteImg(is_l, item)
    self.node_list["fz_cell"].rect.localScale = Vector3(0.7,0.7,0.7)
    self.node_list["fz_frame"]:SetActive(false)
    local cfg = item and ItemWGData.Instance:GetItemConfig(item.item_id)
    local color = cfg and cfg.color or 1
end

function FZKiteRender:PlayDropAnim(time)
    if self.RoteTween then
        self.RoteTween:Kill()
        self.RoteTween = nil
    end
    if self.DOAnchorPosYTween then
        self.DOAnchorPosYTween:Kill()
        self.DOAnchorPosYTween = nil
    end
    if self.DoAlphaTween then
        self.DoAlphaTween:Kill()
        self.DoAlphaTween = nil
    end
    self.RoteTween = self.node_list["fz_kite_root"].rect:DORotate(Vector3(-180, 0, 0), 0.05):SetEase(DG.Tweening.Ease.Linear)
    self.DOAnchorPosYTween = self.node_list["fz_kite_root"].rect:DOAnchorPosY(-300, time):SetEase(DG.Tweening.Ease.Linear)
    self.DoAlphaTween = self.node_list["fz_kite_root"].canvas_group:DoAlpha(1, 0, time):SetEase(DG.Tweening.Ease.Linear)
    self.node_list["fz_effectobj"]:SetActive(true)

    if self.effect_disable_timequest then
        GlobalTimerQuest:CancelQuest(self.effect_disable_timequest)
        self.effect_disable_timequest = nil
    end
    self.effect_disable_timequest = GlobalTimerQuest:AddDelayTimer(function()
        self.node_list["fz_effectobj"]:SetActive(false)
    end, 0.3)
end

function FZKiteRender:AddDelay(func)
    table.insert(self.func_list, func)
end

function FZKiteRender:DoDelayFunc()
    if not self.func_list then
        self.func_list = {}
    end
    for i, v in ipairs(self.func_list) do
        v()
    end
    self.func_list = {}
end
