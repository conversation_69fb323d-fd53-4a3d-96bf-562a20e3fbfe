RoleDiyAppearanceView = RoleDiyAppearanceView or BaseClass(SafeBaseView)

local ColorSelector = typeof(ColorSelector)
local SubColorSelector = typeof(SubColorSelector)

local CUR_SHOW_BIG_TYPE = 
{
    PRESET_DIY = 1,
    BEAUTY_DIY = 2,
	HAIR_DIY = 3,
}

local CUR_BEAUTY_DIY_TYPE = 
{
	EYE_SHADOW = 1,
	PUPIL = 2,
	MOUTH = 3,
	FACE_DECAL = 4,
}

local COLOR_SELECT_TYPE = 
{
	PUPIL_COLOR = "pupil_color",
	HAIR_COLOR = "hair_color",
}
local CUR_FOCUS_SCALE = 
{
	[0] = Vector3(1, 1, 1),
	[1] = Vector3(1, 1, 1),
}

local ROLE_MODEL_PARAM = {
	[0] = {
		[GameEnum.FEMALE] = {
			[GameEnum.ROLE_PROF_1] = {
				role_pos = Vector3(-0.1, -2.5, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
			[GameEnum.ROLE_PROF_2] = {
				role_pos = Vector3(-0.2, -2.5, 0),
				role_rot = Vector3(0, 10, 0),
				role_scale = Vector3(2, 2, 2),
			},
			[GameEnum.ROLE_PROF_3] = {
				role_pos = Vector3(0, -2.5, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
		},
		[GameEnum.MALE] = {
			[GameEnum.ROLE_PROF_1] = {
				role_pos = Vector3(-0.1, -3.25, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
			[GameEnum.ROLE_PROF_2] = {
				role_pos = Vector3(-0.2, -3, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
			[GameEnum.ROLE_PROF_3] = {
				role_pos = Vector3(-0.2, -3, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
		}
    },
	[1] = {
		[GameEnum.FEMALE] = {
			[GameEnum.ROLE_PROF_1] = {
				role_pos = Vector3(0.5, 0, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
			[GameEnum.ROLE_PROF_2] = {
				role_pos = Vector3(0.5, 0, 0),
				role_rot = Vector3(0, 10, 0),
				role_scale = Vector3(2, 2, 2),
			},
			[GameEnum.ROLE_PROF_3] = {
				role_pos = Vector3(0.6, 0, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
		},
		[GameEnum.MALE] = {
			[GameEnum.ROLE_PROF_1] = {
				role_pos = Vector3(0.5, -0.74, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
			[GameEnum.ROLE_PROF_2] = {
				role_pos = Vector3(0.5, 0, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
			[GameEnum.ROLE_PROF_3] = {
				role_pos = Vector3(0.5, -0.44, 0),
				role_rot = Vector3(0, 0, 0),
				role_scale = Vector3(2, 2, 2),
			},
		}
    },
}

local FOCUS_PART_FROM_POS = 
{
	[0] = {
		[GameEnum.FEMALE] = {
			[GameEnum.ROLE_PROF_1] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.2, -4),
			},
			[GameEnum.ROLE_PROF_2] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.25, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.25, -4),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.25, -4),
			},
			[GameEnum.ROLE_PROF_3] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.2, -4),
			},
		},
	
		[GameEnum.MALE] = {
			[GameEnum.ROLE_PROF_1] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.2, -4),
			},
			[GameEnum.ROLE_PROF_2] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.2, -4),
			},
			[GameEnum.ROLE_PROF_3] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.2, -4),
			},
		},
	},

	[1] = {
		[GameEnum.FEMALE] = {
			[GameEnum.ROLE_PROF_1] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.38, -8),
			},
			[GameEnum.ROLE_PROF_2] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.43, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.43, -8),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.43, -8),
			},
			[GameEnum.ROLE_PROF_3] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.38, -8),
			},
		},
	
		[GameEnum.MALE] = {
			[GameEnum.ROLE_PROF_1] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.38, -8),
			},
			[GameEnum.ROLE_PROF_2] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.38, -8),
			},
			[GameEnum.ROLE_PROF_3] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.38, -8),
			},
		},
	},
}

local FOCUS_PART_TO_POS = 
{
	[0] = {
		[GameEnum.FEMALE] = {
			[GameEnum.ROLE_PROF_1] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.35, -1.8),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.35, -2.8),
			},
			[GameEnum.ROLE_PROF_2] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.25, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.35, -1.8),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.5, -2.8),
			},
			[GameEnum.ROLE_PROF_3] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.45, -1.6),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.5, -2.4),
			},
		},
	
		[GameEnum.MALE] = {
			[GameEnum.ROLE_PROF_1] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.3, -2.1),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.3, -3),
			},
			[GameEnum.ROLE_PROF_2] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.4, -2),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.4, -2.8),
			},
			[GameEnum.ROLE_PROF_3] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 2.2, -4),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 2.4, -2),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 2.4, -2.8),
			},
		},
	},

	[1] = {
		[GameEnum.FEMALE] = {
			[GameEnum.ROLE_PROF_1] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.5, -3.54),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.52, -5.6),
			},
			[GameEnum.ROLE_PROF_2] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.43, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.5, -3.5),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.67, -5.44),
			},
			[GameEnum.ROLE_PROF_3] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.6, -3.4),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.6, -5.4),
			},
		},
	
		[GameEnum.MALE] = {
			[GameEnum.ROLE_PROF_1] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.45, -3.9),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.5, -6),
			},
			[GameEnum.ROLE_PROF_2] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.45, -3.9),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.5, -6),
			},
			[GameEnum.ROLE_PROF_3] = {
				[CUR_SHOW_BIG_TYPE.PRESET_DIY] = Vector3(0, 3.38, -8),
				[CUR_SHOW_BIG_TYPE.BEAUTY_DIY] = Vector3(0, 3.57, -3.95),
				[CUR_SHOW_BIG_TYPE.HAIR_DIY] = Vector3(0, 3.6, -5.7),
			},
		},
	},
}

function RoleDiyAppearanceView:__init()
    self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self.view_cache_time = 0
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self:SetMaskBg(false, true)
	
    --self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/role_diy_appearance_ui_prefab", "layout_role_diy_appearacnce")

	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.FACE_DIY})
end

function RoleDiyAppearanceView:__delete()
end

--[[
	data = {
		operate_type = 0,
		sex = 0,
		prof = 1,
	}
]]

function RoleDiyAppearanceView:SetShowDataAndOpen(data)
	self.setting_data = data
	if data then
		self:Open()
		if self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL then
			ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.CreateRoleDIY, data.sex, data.prof)
		end
	end
end

function RoleDiyAppearanceView:ReleaseData()
	self.setting_data = nil
	self.cur_preset_seq = nil
	
	self.big_type = -1
	self.beauty_diy_type = -1

	self.color_cache_type = nil
	self.color_cache_value = nil
	self.color_alpha_cache = nil

	self.color_selector = nil                       -- 取色器(大环)
    self.sub_color_selector = nil                   -- 取色器(HSV)
	self.need_first_load_model = nil
	self.is_btn_cool = nil                          -- 是否还在冷却
	self.is_far = nil                               -- 是否是远景
	self.scene_camera = nil
	self:RemoveCoolDelayTimer()

	RoleDiyAppearanceWGData.Instance:RemoveDiyAppearaceData()
end

--移除回调
function RoleDiyAppearanceView:RemoveCoolDelayTimer()
    if self.show_cool_timer then
        GlobalTimerQuest:CancelQuest(self.show_cool_timer)
        self.show_cool_timer = nil
    end
end


function RoleDiyAppearanceView:OpenCallBack()
end

function RoleDiyAppearanceView:CloseCallBack()
	self:ReleaseData()
end

function RoleDiyAppearanceView:LoadCallBack()
	-- local bundle, asset = ResPath.GetRawImagesJPG("a3_cj_bjd")
	-- if bundle and asset then
	-- 	self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function ()
	-- 		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- 	end)
	-- end

	if not self.preset_style_list then
		self.preset_style_list = AsyncBaseGrid.New()
		self.preset_style_list:CreateCells({col = 3,change_cells_num = 1, 
											list_view = self.node_list["preset_style_list"],
											assetBundle = "uis/view/role_diy_appearance_ui_prefab", 
											assetName = "preset_style_cell",  
											itemRender = RoleDiyPresetStyleCell})
		self.preset_style_list:SetStartZeroIndex(false)
		self.preset_style_list:SetSelectCallBack(BindTool.Bind(self.OnClickPresetStyle, self))
	end

	if not self.eye_shadow_style_list then
		self.eye_shadow_style_list = AsyncBaseGrid.New()
		self.eye_shadow_style_list:CreateCells({col = 3,change_cells_num = 1, 
											list_view = self.node_list["eye_shadow_style_list"],
											assetBundle = "uis/view/role_diy_appearance_ui_prefab", 
											assetName = "eye_shadow_style_cell",  
											itemRender = RoleDiyEyeShadowStyleCell})
		self.eye_shadow_style_list:SetStartZeroIndex(false)
	end

	if not self.pupil_type_list then
		self.pupil_type_list = AsyncBaseGrid.New()
		self.pupil_type_list:CreateCells({col = 3,change_cells_num = 1, 
											list_view = self.node_list["pupil_type_list"],
											assetBundle = "uis/view/role_diy_appearance_ui_prefab", 
											assetName = "pupil_type_cell",  
											itemRender = RoleDiyPupilTypeCell})
		self.pupil_type_list:SetStartZeroIndex(false)
	end

	if not self.color_suggest_list then
		self.color_suggest_list = AsyncBaseGrid.New()
		self.color_suggest_list:CreateCells({col = 3,change_cells_num = 1,
											list_view = self.node_list["color_suggest_list"],
											assetBundle = "uis/view/role_diy_appearance_ui_prefab", 
											assetName = "color_suggest_cell",  
											itemRender = RoleDiyColorCell})
		self.color_suggest_list:SetStartZeroIndex(true)
		self.color_suggest_list:SetSelectCallBack(BindTool.Bind(self.OnClickColorSuggestCell, self))
	end

	if not self.mouth_type_list then
		self.mouth_type_list = AsyncBaseGrid.New()
		self.mouth_type_list:CreateCells({col = 3,change_cells_num = 1, 
											list_view = self.node_list["mouth_type_list"],
											assetBundle = "uis/view/role_diy_appearance_ui_prefab", 
											assetName = "mouth_type_cell",  
											itemRender = RoleDiyMouthTypeCell})
		self.mouth_type_list:SetStartZeroIndex(false)
	end

	if not self.face_decal_list then
		self.face_decal_list = AsyncBaseGrid.New()
		self.face_decal_list:CreateCells({col = 3,change_cells_num = 1, 
											list_view = self.node_list["face_decal_list"],
											assetBundle = "uis/view/role_diy_appearance_ui_prefab", 
											assetName = "face_decal_cell",  
											itemRender = RoleDiyFaceDecalCell})
		self.face_decal_list:SetStartZeroIndex(false)
	end

	if not self.hair_type_list then
		self.hair_type_list = AsyncBaseGrid.New()
		self.hair_type_list:CreateCells({col = 3,change_cells_num = 1, 
											list_view = self.node_list["hair_type_list"],
											assetBundle = "uis/view/role_diy_appearance_ui_prefab", 
											assetName = "hair_type_cell",  
											itemRender = RoleDiyHairTypeCell})
		self.hair_type_list:SetStartZeroIndex(false)
	end

	--加载模型时装
	if not self.role_model then
		self.role_model = RoleModel.New()
		self.role_model:SetPositionAndRotation(nil, Vector3(0, 180, 0), nil)
		self.role_model:SetUISceneModel(self.node_list["ph_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_model, 0)
		-- 创角界面特殊处理下
		if self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL then
			local pos_node = UnityEngine.GameObject.Find("ModelPos")
			if pos_node then
				self.role_model:SetUIRootParentNode(pos_node)
			end

			local camera_obj = UnityEngine.GameObject.Find("Camera")
			self.scene_camera = camera_obj and camera_obj:GetComponent(typeof(UnityEngine.Camera))
		end
	end

	for i = 1, 3 do
        self.node_list["diy_type_btn_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnSelectBigTypeToggle, self, i))
    end

	for i = 1, 4 do
		XUI.AddClickEventListener(self.node_list["beauty_diy_type_" .. i], BindTool.Bind(self.OnClickSelectBeautyDiyType, self, i))
    end

	if not self.change_diy_cost_cell then
		self.change_diy_cost_cell = ItemCell.New(self.node_list["change_diy_cost"])
	end

	if not self.pupil_type_act_cost_cell then
		self.pupil_type_act_cost_cell = ItemCell.New(self.node_list["pupil_type_act_cost"])
	end

	if not self.face_deca_act_cost_cell then
		self.face_deca_act_cost_cell = ItemCell.New(self.node_list["face_deca_act_cost"])
	end

	self.color_selector = self.node_list.color_wheel:GetComponent(ColorSelector)
    self.sub_color_selector = self.node_list.sub_color:GetComponent(SubColorSelector)

	XUI.AddClickEventListener(self.node_list.spe_return_type_btn, BindTool.Bind(self.OnClickSpeReturnType, self))
	XUI.AddClickEventListener(self.node_list.hide_btn, BindTool.Bind(self.OnClickHideUi, self, 1))
	XUI.AddClickEventListener(self.node_list.hide_close_btn, BindTool.Bind(self.OnClickHideUi, self, 2))
	XUI.AddClickEventListener(self.node_list.close_color_btn, BindTool.Bind(self.OnClickColorPanel, self, 1))
	XUI.AddClickEventListener(self.node_list.pupil_color_img, BindTool.Bind(self.OnClickColorPanel, self, 2))
	XUI.AddClickEventListener(self.node_list.hair_color_img, BindTool.Bind(self.OnClickColorPanel, self, 3))
	
	XUI.AddClickEventListener(self.node_list.sure_color_btn, BindTool.Bind(self.OnClickSureColor, self))
	
	XUI.AddClickEventListener(self.node_list.quash_btn, BindTool.Bind(self.OnClickChangeDiyIndex, self, 1))
	XUI.AddClickEventListener(self.node_list.recover_btn, BindTool.Bind(self.OnClickChangeDiyIndex, self, 2))
	XUI.AddClickEventListener(self.node_list.reset_btn, BindTool.Bind(self.OnClickResetDiy, self))

	XUI.AddClickEventListener(self.node_list.eye_size_add_btn, BindTool.Bind(self.OnClickChangeEyeSize, self, 1))
	XUI.AddClickEventListener(self.node_list.eye_size_minus_btn, BindTool.Bind(self.OnClickChangeEyeSize, self, 2))

	XUI.AddClickEventListener(self.node_list.eye_pos_add_btn, BindTool.Bind(self.OnClickChangeEyePos, self, 1))
	XUI.AddClickEventListener(self.node_list.eye_pos_minus_btn, BindTool.Bind(self.OnClickChangeEyePos, self, 2))

	XUI.AddClickEventListener(self.node_list.pupil_size_add_btn, BindTool.Bind(self.OnClickChangePupilSize, self, 1))
	XUI.AddClickEventListener(self.node_list.pupil_size_minus_btn, BindTool.Bind(self.OnClickChangePupilSize, self, 2))

	XUI.AddClickEventListener(self.node_list.mouth_size_add_btn, BindTool.Bind(self.OnClickChangeMouthSize, self, 1))
	XUI.AddClickEventListener(self.node_list.mouth_size_minus_btn, BindTool.Bind(self.OnClickChangeMouthSize, self, 2))

	XUI.AddClickEventListener(self.node_list.mouth_pos_add_btn, BindTool.Bind(self.OnClickChangeMouthPos, self, 1))
	XUI.AddClickEventListener(self.node_list.mouth_pos_minus_btn, BindTool.Bind(self.OnClickChangeMouthPos, self, 2))

	self.node_list.eye_size_silder.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderChangeEyeSize, self))
	self.node_list.eye_pos_silder.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderChangeEyePos, self))

	self.node_list.pupil_size_silder.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderChangePupilSize, self))

	self.node_list.mouth_size_silder.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderChangeMouthSize, self))
	self.node_list.mouth_pos_silder.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderChangeMouthPos, self))
	
	XUI.AddClickEventListener(self.node_list.lens_btn, BindTool.Bind(self.ClickEditLens, self))
	-- 选择完颜色回调
	if self.sub_color_selector then
		self.sub_color_selector:SetSelectColorCB(BindTool.Bind(self.SelectSubColorCallBack, self))
	end
	
	XUI.AddClickEventListener(self.node_list["return_view_btn"], BindTool.Bind(self.OnCloseView, self, true))
	XUI.AddClickEventListener(self.node_list["create_role_btn"], BindTool.Bind(self.OnClickCreateRole, self))
	XUI.AddClickEventListener(self.node_list["change_diy_btn"], BindTool.Bind(self.OnClickChangeDiy, self))
	XUI.AddClickEventListener(self.node_list["pupil_type_act_btn"], BindTool.Bind(self.OnClickPupilTypeAct, self))
	XUI.AddClickEventListener(self.node_list["face_deca_act_btn"], BindTool.Bind(self.OnClickFaceDecalAct, self))

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
	
	self.big_type = -1
	self.beauty_diy_type = -1
	self.color_cache_type = nil
	self.color_cache_value = nil
	self.color_alpha_cache = 0
	self.node_list["diy_type_btn_1"].toggle.isOn = true
	self:OnSelectBigTypeToggle(1, true)
	self.need_first_load_model = true
end

function RoleDiyAppearanceView:ReleaseCallBack()
	self:ReleaseData()

	if self.preset_style_list then
        self.preset_style_list:DeleteMe()
        self.preset_style_list = nil
    end

	if self.eye_shadow_style_list then
        self.eye_shadow_style_list:DeleteMe()
        self.eye_shadow_style_list = nil
    end

	if self.pupil_type_list then
		self.pupil_type_list:DeleteMe()
        self.pupil_type_list = nil
	end

	if self.color_suggest_list then
		self.color_suggest_list:DeleteMe()
        self.color_suggest_list = nil
	end

	if self.mouth_type_list then
		self.mouth_type_list:DeleteMe()
        self.mouth_type_list = nil
	end

	if self.face_decal_list then
		self.face_decal_list:DeleteMe()
        self.face_decal_list = nil
	end

	if self.hair_type_list then
		self.hair_type_list:DeleteMe()
        self.hair_type_list = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.change_diy_cost_cell then
		self.change_diy_cost_cell:DeleteMe()
		self.change_diy_cost_cell = nil
	end

	if self.pupil_type_act_cost_cell then
		self.pupil_type_act_cost_cell:DeleteMe()
		self.pupil_type_act_cost_cell = nil
	end

	if self.face_deca_act_cost_cell then
		self.face_deca_act_cost_cell:DeleteMe()
		self.face_deca_act_cost_cell = nil
	end

    if self.item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
        self.item_data_change = nil
    end
end

function RoleDiyAppearanceView:ShowIndexCallBack()
     
end

function RoleDiyAppearanceView:OnClickPresetStyle(item)
	if nil == item or nil == item:GetData() then
        return
    end
    
    local data = item:GetData()
	if self.cur_preset_seq == data.preset_seq then
		return
	end

	self.cur_preset_seq = data.preset_seq
	RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.ALL, data)
end

function RoleDiyAppearanceView:OnClickColorSuggestCell(item)
	if nil == item or nil == item:GetData() or self.color_cache_type == nil then
        return
    end
    
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local cur_color = cur_show_diy_appearance_data[self.color_cache_type]
	local cur_color_alpha = 0
	if self.color_cache_type == COLOR_SELECT_TYPE.PUPIL_COLOR then
		cur_color_alpha = cur_show_diy_appearance_data["pupil_color_alpha"]
	end

	if not cur_color then
		return
	end

	local is_change = false
	local cell_data = item:GetData()
	if cell_data.reset_flag == 1 then
		-- 重置
		if self.color_cache_value and self.color_cache_value ~= cur_color then
			self.color_cache_value = cur_color
			self.color_alpha_cache = cur_color_alpha
			is_change = true
		end
	elseif cell_data.reset_flag == 0 then
		if self.color_cache_value ~= cell_data.part_color then
			self.color_cache_value = cell_data.part_color
			self.color_alpha_cache = 0
			is_change = true
		end
	end

	if is_change then
		local data = {}
		data[self.color_cache_type] = self.color_cache_value
		if self.color_cache_type == COLOR_SELECT_TYPE.PUPIL_COLOR then
			data["pupil_color_alpha"] = self.color_alpha_cache
		end

		self:FlushModelDisPlay(data)
	end
end

function RoleDiyAppearanceView:OnFlush(param_t)
	if not self.setting_data then
		return
	end

	for k, v in pairs(param_t) do
		if k == "all" then
			if self.big_type == CUR_SHOW_BIG_TYPE.PRESET_DIY then
				self:FlushPresetDiyPanel()
			elseif self.big_type == CUR_SHOW_BIG_TYPE.BEAUTY_DIY then
				self:FlushBeautyDiyPanel(v.need_jump)
			elseif self.big_type == CUR_SHOW_BIG_TYPE.HAIR_DIY then
				self:FlushHairDiyPanel(v.need_jump)
			end

			self:FlushModelDisPlay()
		end
	end

	self.node_list["create_role_btn"]:SetActive(self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL)

	self:FlushChangeCostInfo()
	self:FlushSpeShowPanel()
	self:FlushRedInfoPanel()
end

function RoleDiyAppearanceView:FlushPresetDiyPanel()
	local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.setting_data.sex, self.setting_data.prof)
	self.preset_style_list:SetDataList(preset_diy_cfg)

	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		self.preset_style_list:JumpToIndexAndSelect(1, 6)
	end
end

function RoleDiyAppearanceView:FlushBeautyDiyPanel(need_jump)
	if self.beauty_diy_type == CUR_BEAUTY_DIY_TYPE.EYE_SHADOW then
		self:FlushEyeShadowPanel(need_jump)
		self:FlushEyeSizePanel()
		self:FlushEyePosPanel()
	elseif self.beauty_diy_type == CUR_BEAUTY_DIY_TYPE.PUPIL then
		self:FlushPupilTypePanel(need_jump)
		self:FlushPupilSizePanel()
		self:FlushPupilColorPanel()
	elseif self.beauty_diy_type == CUR_BEAUTY_DIY_TYPE.MOUSE then
		self:FlushMouthTypePanel(need_jump)
		self:FlushMouthSizePanel()
		self:FlushMouthPosPanel()
	elseif self.beauty_diy_type == CUR_BEAUTY_DIY_TYPE.FACE_DECAL then
		self:FlushFaceDecalPanel(need_jump)
	end
end

function RoleDiyAppearanceView:FlushEyeShadowPanel(need_jump)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local eye_shadow_cfg = RoleDiyAppearanceWGData.Instance:GetEyeShadowTypeCfg()
	self.eye_shadow_style_list:SetDataList(eye_shadow_cfg)
	if need_jump then
		for k, v in pairs(eye_shadow_cfg) do
			if v.eye_shadow_color == cur_show_diy_appearance_data.eye_shadow_color then
				self.eye_shadow_style_list:JumpToIndexAndSelect(k, 6)
				break
			end
		end
	end
end

function RoleDiyAppearanceView:FlushEyeSizePanel()
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()

	self.node_list.eye_size_silder.slider.maxValue = other_cfg.eye_size_max
	self.node_list.eye_size_silder.slider.minValue = other_cfg.eye_size_min 
	self.node_list.eye_size_silder.slider.value = cur_show_diy_appearance_data.eye_size

	self.node_list.eye_size_text.text.text = string.format(Language.RoleDiyAppearanceView.EyeSize, cur_show_diy_appearance_data.eye_size)
end

function RoleDiyAppearanceView:FlushEyePosPanel()
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	self.node_list.eye_pos_silder.slider.maxValue = other_cfg.eye_pos_max
	self.node_list.eye_pos_silder.slider.minValue = other_cfg.eye_pos_min 
	self.node_list.eye_pos_silder.slider.value = cur_show_diy_appearance_data.eye_pos

	self.node_list.eye_pos_text.text.text = string.format(Language.RoleDiyAppearanceView.EyePos, cur_show_diy_appearance_data.eye_pos)
end

function RoleDiyAppearanceView:FlushPupilTypePanel(need_jump)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local pupil_type_cfg = RoleDiyAppearanceWGData.Instance:GetPupilTypeCfg(self.setting_data.operate_type)
	self.pupil_type_list:SetDataList(pupil_type_cfg)
	if need_jump then
		for k, v in pairs(pupil_type_cfg) do
			if v.pupil_type == cur_show_diy_appearance_data.pupil_type then
				self.pupil_type_list:JumpToIndexAndSelect(k, 6)
				break
			end
		end
	end
end

function RoleDiyAppearanceView:FlushPupilSizePanel()
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	self.node_list.pupil_size_silder.slider.maxValue = other_cfg.pupil_size_max
	self.node_list.pupil_size_silder.slider.minValue = other_cfg.pupil_size_min 
	self.node_list.pupil_size_silder.slider.value = cur_show_diy_appearance_data.pupil_size

	self.node_list.pupil_size_text.text.text = string.format(Language.RoleDiyAppearanceView.PupilSize, cur_show_diy_appearance_data.pupil_size)
end

function RoleDiyAppearanceView:FlushPupilColorPanel()
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local pupil_color = cur_show_diy_appearance_data.pupil_color
	local color = UtilU3d.ConvertHexToColor(pupil_color)
	self.node_list.pupil_color_img.image.color = color
end

function RoleDiyAppearanceView:FlushMouthTypePanel(need_jump)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local mouth_type_cfg = RoleDiyAppearanceWGData.Instance:GetMouthTypeCfg()
	self.mouth_type_list:SetDataList(mouth_type_cfg)
	if need_jump then
		for k, v in pairs(mouth_type_cfg) do
			if v.mouth_color == cur_show_diy_appearance_data.mouth_color then
				self.mouth_type_list:JumpToIndexAndSelect(k, 6)
				break
			end
		end
	end
end

function RoleDiyAppearanceView:FlushMouthSizePanel()
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	self.node_list.mouth_size_silder.slider.maxValue = other_cfg.mouth_size_max
	self.node_list.mouth_size_silder.slider.minValue = other_cfg.mouth_size_min 
	self.node_list.mouth_size_silder.slider.value = cur_show_diy_appearance_data.mouth_size

	self.node_list.mouth_size_text.text.text = string.format(Language.RoleDiyAppearanceView.MouthSize, cur_show_diy_appearance_data.mouth_size)
end

function RoleDiyAppearanceView:FlushMouthPosPanel()
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	self.node_list.mouth_pos_silder.slider.maxValue = other_cfg.mouth_pos_max
	self.node_list.mouth_pos_silder.slider.minValue = other_cfg.mouth_pos_min 
	self.node_list.mouth_pos_silder.slider.value = cur_show_diy_appearance_data.mouth_pos

	self.node_list.mouth_pos_text.text.text = string.format(Language.RoleDiyAppearanceView.MouthPos, cur_show_diy_appearance_data.mouth_pos)
end

function RoleDiyAppearanceView:FlushFaceDecalPanel(need_jump)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local face_decal_cfg = RoleDiyAppearanceWGData.Instance:GetFaceDecalCfg(self.setting_data.operate_type)
	local data = {}
	for k, v in pairs(face_decal_cfg) do
		data[k] = {
			cfg = v,
			sex = self.setting_data.sex,
		}
	end

	self.face_decal_list:SetDataList(data)
	if need_jump then
		for k, v in pairs(face_decal_cfg) do
			if v.face_decal_id == cur_show_diy_appearance_data.face_decal_id then
				self.face_decal_list:JumpToIndexAndSelect(k, 6)
				break
			end
		end
	end
end

function RoleDiyAppearanceView:FlushHairDiyPanel(need_jump)
	self:FlushHairTypePanel(need_jump)
	self:FlushHairColorPanel()
end

function RoleDiyAppearanceView:FlushHairTypePanel(need_jump)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local hair_diy_type_cfg = RoleDiyAppearanceWGData.Instance:GetHairDiyTypeCfg(self.setting_data.sex, self.setting_data.prof)
	self.hair_type_list:SetDataList(hair_diy_type_cfg)
	
	if need_jump then
		for k, v in pairs(hair_diy_type_cfg) do
			if v.hair_id == cur_show_diy_appearance_data.hair_id then
				self.hair_type_list:JumpToIndexAndSelect(k, 6)
				break
			end
		end
	end
end

function RoleDiyAppearanceView:FlushHairColorPanel()
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local hair_color = cur_show_diy_appearance_data.hair_color
	local color = UtilU3d.ConvertHexToColor(hair_color)
	self.node_list.hair_color_img.image.color = color
end


function RoleDiyAppearanceView:FlushDiyColorPanel()
	--默认选推荐色界面
	self.node_list.color_btn_1.toggle.isOn = true
	self:FlushDiyColorSuggest()
	self:FlushDiyColorSelector()
end

function RoleDiyAppearanceView:FlushDiyColorSuggest()
	local color_suggest_cfg = RoleDiyAppearanceWGData.Instance:GetColorSuggestCfg()
	self.color_suggest_list:SetDataList(color_suggest_cfg)
	self.color_suggest_list:CancleAllSelectCell()
end

function RoleDiyAppearanceView:FlushDiyColorSelector()
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local cur_color = cur_show_diy_appearance_data[self.color_cache_type]
	if not cur_color then
		return
	end

	-- 取色器选中颜色
	if self.color_selector ~= nil then  -- 回调刷新花费和模型颜色
		self.color_selector:UpdateHexColorSelectorWheel(cur_color)
	end
end

function RoleDiyAppearanceView:OnSelectBigTypeToggle(index, is_on)
	if nil == index or not is_on or self.big_type == index then
		return
	end

	self.node_list["spe_return_type_btn"]:SetActive(index ~= 1)
	if index == 1 then
		self.big_type = CUR_SHOW_BIG_TYPE.PRESET_DIY
		self:FlushPresetDiyPanel()
	elseif index == 2 then
		self.big_type = CUR_SHOW_BIG_TYPE.BEAUTY_DIY
		self.beauty_diy_type = -1
		self:OnClickSelectBeautyDiyType(1, true)
	elseif index == 3 then
		self.big_type = CUR_SHOW_BIG_TYPE.HAIR_DIY	
		self:FlushHairDiyPanel()
	end

	self:FlushLensStatus()
	self:FlushSpeShowPanel()
end

function RoleDiyAppearanceView:OnClickSelectBeautyDiyType(index, need_jump)
	if index == self.beauty_diy_type then
		return
	end
	
	for i = 1, 4 do
		self.node_list["beauty_diy_type_" .. i]:FindObj("nor_img"):CustomSetActive(index ~= i)
		self.node_list["beauty_diy_type_" .. i]:FindObj("hl_img"):CustomSetActive(index == i)
	end

	self.node_list["eye_shadow_panel"]:SetActive(index == CUR_BEAUTY_DIY_TYPE.EYE_SHADOW)
	self.node_list["pupil_info_panel"]:SetActive(index == CUR_BEAUTY_DIY_TYPE.PUPIL)
	self.node_list["mouth_info_panel"]:SetActive(index == CUR_BEAUTY_DIY_TYPE.MOUTH)
	self.node_list["face_decal_info_panel"]:SetActive(index == CUR_BEAUTY_DIY_TYPE.FACE_DECAL)

	if index == 1 then
		self.beauty_diy_type = CUR_BEAUTY_DIY_TYPE.EYE_SHADOW
		self:FlushEyeShadowPanel(need_jump)
		self:FlushEyeSizePanel()
		self:FlushEyePosPanel()
	elseif index == 2 then
		self.beauty_diy_type = CUR_BEAUTY_DIY_TYPE.PUPIL
		self:FlushPupilTypePanel(need_jump)
		self:FlushPupilSizePanel()
		self:FlushPupilColorPanel()
	elseif index == 3 then
		self.beauty_diy_type = CUR_BEAUTY_DIY_TYPE.MOUSE	
		self:FlushMouthTypePanel(need_jump)
		self:FlushMouthSizePanel()
		self:FlushMouthPosPanel()
	elseif index == 4 then
		self.beauty_diy_type = CUR_BEAUTY_DIY_TYPE.FACE_DECAL	
		self:FlushFaceDecalPanel(need_jump)
	end

	self:FlushSpeShowPanel()
end

function RoleDiyAppearanceView:OnClickSpeReturnType()
	local ok_fun = function()
		self.node_list["diy_type_btn_1"].toggle.isOn = true
		if self.setting_data and self.cur_preset_seq then
			local cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.setting_data.sex, self.setting_data.prof, self.cur_preset_seq)
			if cfg then
				RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.ALL, cfg)
			end
		end
    end

	local str = Language.RoleDiyAppearanceView.ReturnErrorStr
	TipWGCtrl.Instance:OpenAlertTips(str, ok_fun, nil, nil, nil)
end

function RoleDiyAppearanceView:OnClickHideUi(index)
	self.node_list["hide_close_btn"]:SetActive(index == 1)
	self.node_list["diy_ui_panel"]:SetActive(index == 2)
end

function RoleDiyAppearanceView:OnClickColorPanel(index, not_flush_model)
	self.node_list["diy_ui_panel"]:SetActive(index == 1)
	self.node_list["diy_color_root"]:SetActive(index ~= 1)
	if index == 1 then
		if not not_flush_model then
			local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
			if cur_show_diy_appearance_data and cur_show_diy_appearance_data[self.color_cache_type] then
				local data = {}
				data[self.color_cache_type] = cur_show_diy_appearance_data[self.color_cache_type]
				if self.color_cache_type == COLOR_SELECT_TYPE.PUPIL_COLOR then
					data["pupil_color_alpha"] = cur_show_diy_appearance_data["pupil_color_alpha"]
				end
				self:FlushModelDisPlay(data)
			end
		end

		self.color_cache_type = nil
		self.color_cache_value = nil
		self.color_alpha_cache = 0
	else
		if index == 2 then
			self.color_cache_type = COLOR_SELECT_TYPE.PUPIL_COLOR
		elseif index == 3 then
			self.color_cache_type = COLOR_SELECT_TYPE.HAIR_COLOR
		end

		self:FlushDiyColorPanel()
	end
end
function RoleDiyAppearanceView:OnClickSureColor()
	if self.color_cache_type and self.color_cache_value then
		local color_cache_type = self.color_cache_type
		local color_cache_value = self.color_cache_value
		local color_alpha_cache = self.color_alpha_cache

		local data = {}
		data[color_cache_type] = color_cache_value
	
		self:OnClickColorPanel(1, true)
		if color_cache_type == COLOR_SELECT_TYPE.PUPIL_COLOR then
			data["pupil_color_alpha"] = color_alpha_cache
			RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.PUPIL_COLOR, data)
		elseif color_cache_type == COLOR_SELECT_TYPE.HAIR_COLOR then
			RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.HAIR_COLOR, data)	
		end
	end
end

function RoleDiyAppearanceView:OnClickChangeDiyIndex(index)
	local record_data, diy_index = RoleDiyAppearanceWGData.Instance:GetRecordDiyDataAndIndex()
	local record_num = #record_data
	if record_num <= 0 then
		return
	end
	if index == 1 then
		if diy_index > 1 then 
			RoleDiyAppearanceWGCtrl.Instance:ChangeCurDiySelectIndex(RoleDiyAppearanceWGData.CHANGE_DIY_INDEX.MINUS)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.RoleDiyAppearanceView.ChangeErrorStr[1])
		end
	elseif index == 2 then
		if diy_index < record_num then
			RoleDiyAppearanceWGCtrl.Instance:ChangeCurDiySelectIndex(RoleDiyAppearanceWGData.CHANGE_DIY_INDEX.ADD)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.RoleDiyAppearanceView.ChangeErrorStr[2])
		end
	end
end

function RoleDiyAppearanceView:OnClickResetDiy()
	if self.setting_data and self.cur_preset_seq then
		local cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.setting_data.sex, self.setting_data.prof, self.cur_preset_seq)
		if not cfg then
			return
		end

		RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.RESET, cfg)
	end
end

function RoleDiyAppearanceView:OnClickChangeEyeSize(index)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	local eye_size = cur_show_diy_appearance_data.eye_size
	local new_eye_size = eye_size
	if index == 1 then
		new_eye_size = math.min(eye_size + 1, other_cfg.eye_size_max)
	elseif index == 2 then
		new_eye_size = math.max(eye_size - 1, other_cfg.eye_size_min)
	end

	if new_eye_size ~= eye_size then
		self:OnSliderChangeEyeSize(new_eye_size)
	end
end

function RoleDiyAppearanceView:OnSliderChangeEyeSize(value)
	local data = {}
	data.eye_size = value
	RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.EYE_SIZE, data)
end

function RoleDiyAppearanceView:OnClickChangeEyePos(index)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	local eye_pos = cur_show_diy_appearance_data.eye_pos
	local new_eye_pos = eye_pos
	if index == 1 then
		new_eye_pos = math.min(eye_pos + 1, other_cfg.eye_pos_max)
	elseif index == 2 then
		new_eye_pos = math.max(eye_pos - 1, other_cfg.eye_pos_min)
	end

	if new_eye_pos ~= eye_pos then
		self:OnSliderChangeEyePos(new_eye_pos)
	end
end

function RoleDiyAppearanceView:OnSliderChangeEyePos(value)
	local data = {}
	data.eye_pos = value
	RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.EYE_POS, data)
end

function RoleDiyAppearanceView:OnClickChangePupilSize(index)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	local pupil_size = cur_show_diy_appearance_data.pupil_size
	local new_pupil_size = pupil_size
	if index == 1 then
		new_pupil_size = math.min(pupil_size + 1, other_cfg.pupil_size_max)
	elseif index == 2 then
		new_pupil_size = math.max(pupil_size - 1, other_cfg.pupil_size_min)
	end

	if new_pupil_size ~= pupil_size then
		self:OnSliderChangePupilSize(new_pupil_size)
	end
end

function RoleDiyAppearanceView:OnSliderChangePupilSize(value)
	local data = {}
	data.pupil_size = value
	RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.PUPIL_SIZE, data)
end

function RoleDiyAppearanceView:OnClickChangeMouthSize(index)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	local mouth_size = cur_show_diy_appearance_data.mouth_size
	local new_mouth_size = mouth_size
	if index == 1 then
		new_mouth_size = math.min(mouth_size + 1, other_cfg.mouth_size_max)
	elseif index == 2 then
		new_mouth_size = math.max(mouth_size - 1, other_cfg.mouth_size_min)
	end

	if new_mouth_size ~= mouth_size then
		self:OnSliderChangeMouthSize(new_mouth_size)
	end
end

function RoleDiyAppearanceView:OnSliderChangeMouthSize(value)
	local data = {}
	data.mouth_size = value
	RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.MOUTH_SIZE, data)
end

function RoleDiyAppearanceView:OnClickChangeMouthPos(index)
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	local mouth_pos = cur_show_diy_appearance_data.mouth_pos
	local new_mouth_pos = mouth_pos
	if index == 1 then
		new_mouth_pos = math.min(mouth_pos + 1, other_cfg.mouth_pos_max)
	elseif index == 2 then
		new_mouth_pos = math.max(mouth_pos - 1, other_cfg.mouth_pos_min)
	end

	if new_mouth_pos ~= mouth_pos then
		self:OnSliderChangeMouthPos(new_mouth_pos)
	end
end

function RoleDiyAppearanceView:OnSliderChangeMouthPos(value)
	local data = {}
	data.mouth_pos = value
	RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.MOUTH_POS, data)
end

-- 选择完颜色回调
function RoleDiyAppearanceView:SelectSubColorCallBack(final_color, hex_color)
    -- 展示编辑颜色
	if not self.color_cache_type or self.color_cache_value == hex_color then
		return
	end

	self.color_cache_value = hex_color
	self.color_alpha_cache = 0

	local data = {}
	data[self.color_cache_type] = self.color_cache_value
	if self.color_cache_type == COLOR_SELECT_TYPE.PUPIL_COLOR then
		data["pupil_color_alpha"] = self.color_alpha_cache
	end

	self:FlushModelDisPlay(data)
end


--刷新模型展示
function RoleDiyAppearanceView:FlushModelDisPlay(spe_show_change_info)
	if not self.role_model then
		return
	end

	if not self.setting_data then
		return
	end

	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local alpha_cfg = RoleDiyAppearanceWGData.Instance:GetAlphaCfg(self.setting_data.sex, self.setting_data.prof, cur_show_diy_appearance_data.face_id)
	if not alpha_cfg then
		return
	end

	if self.need_first_load_model then
		local role_res_id = RoleWGData.GetJobModelId(self.setting_data.sex, self.setting_data.prof)
		local weapon_res_id = RoleWGData.GetJobWeaponId(self.setting_data.sex, self.setting_data.prof)
		local eye_shadow_color = UtilU3d.ConvertHexToColor(cur_show_diy_appearance_data.eye_shadow_color) 
		local cur_eye_shadow_color = {r = math.floor(eye_shadow_color.r * 255), g = math.floor(eye_shadow_color.g * 255), b = math.floor(eye_shadow_color.b * 255), a = math.floor(cur_show_diy_appearance_data.eye_shadow_alpha or 0)}
		local pupil_color = UtilU3d.ConvertHexToColor(cur_show_diy_appearance_data.pupil_color) 
		local cur_pupil_color = {r = math.floor(pupil_color.r * 255), g = math.floor(pupil_color.g * 255), b = math.floor(pupil_color.b * 255), a = math.floor(cur_show_diy_appearance_data.pupil_color_alpha or 0)}
		local mouth_color = UtilU3d.ConvertHexToColor(cur_show_diy_appearance_data.mouth_color) 
		local cur_mouth_color = {r = math.floor(mouth_color.r * 255), g = math.floor(mouth_color.g * 255), b = math.floor(mouth_color.b * 255), a = math.floor(cur_show_diy_appearance_data.mouth_alpha or 0)}
		local hair_color = UtilU3d.ConvertHexToColor(cur_show_diy_appearance_data.hair_color) 
		local cur_hair_color = {r = math.floor(hair_color.r * 255), g = math.floor(hair_color.g * 255), b = math.floor(hair_color.b * 255), a = math.floor(alpha_cfg.hair_alpha or 0)}
		local extra_model_data = {
			sex = self.setting_data.sex,
			prof = self.setting_data.prof,

			d_face_res = cur_show_diy_appearance_data.face_id,
			d_hair_res = cur_show_diy_appearance_data.hair_id,
			d_body_res =  cur_show_diy_appearance_data.body_id,

			eye_size = cur_show_diy_appearance_data.eye_size,
			eye_shadow_color = cur_eye_shadow_color,
			eye_position = cur_show_diy_appearance_data.eye_pos,

			left_pupil_type = cur_show_diy_appearance_data.pupil_type,
			left_pupil_size = cur_show_diy_appearance_data.pupil_size,
			left_pupil_color = cur_pupil_color,

			right_pupil_type = cur_show_diy_appearance_data.pupil_type,
			right_pupil_size = cur_show_diy_appearance_data.pupil_size,
			right_pupil_color = cur_pupil_color,

			mouth_color = cur_mouth_color,
			mouth_size = cur_show_diy_appearance_data.mouth_size,
			mouth_position = cur_show_diy_appearance_data.mouth_pos,

			face_decal_id = cur_show_diy_appearance_data.face_decal_id,
			preset_seq = cur_show_diy_appearance_data.preset_seq,
			hair_color = cur_hair_color,
			animation_name = SceneObjAnimator.RoleDiyAppearance or SceneObjAnimator.UiIdle,
		}
		self.role_model:SetRoleResid(role_res_id, nil, extra_model_data)

		local param
		if self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL then
			param = ((ROLE_MODEL_PARAM[0] or {})[self.setting_data.sex] or {})[self.setting_data.prof]
		else
			param = ((ROLE_MODEL_PARAM[1] or {})[self.setting_data.sex] or {})[self.setting_data.prof]
		end

		if param then
			self.role_model:SetUSAdjustmentNodeLocalPosition(param.role_pos.x, param.role_pos.y, param.role_pos.z)
			self.role_model:SetUSAdjustmentNodeLocalRotation(param.role_rot.x, param.role_rot.y, param.role_rot.z)
			self.role_model:SetUSAdjustmentNodeLocalScale(param.role_scale.x, param.role_scale.y, param.role_scale.z)
		end

	else
		local record_data, diy_index, record_change_data = RoleDiyAppearanceWGData.Instance:GetRecordDiyDataAndIndex()
		if not record_change_data or (not record_change_data[diy_index]) then
			return
		end

		local change_info = spe_show_change_info or record_change_data[diy_index].change_info
		for k, v in pairs(change_info) do
			if k == "hair_id" then
				if change_info["hair_color"] then
					local show_color = UtilU3d.ConvertHexToColor(change_info["hair_color"])  
					local new_color = {r = math.floor(show_color.r * 255), g = math.floor(show_color.g * 255), b = math.floor(show_color.b * 255), a = math.floor(alpha_cfg.hair_alpha or 0)}
					self.role_model:UpdateChangeExtraModelData(HeadCustomizationType.HairColor, new_color)
				end

				local new_hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, v, self.setting_data.sex, self.setting_data.prof)
				self.role_model:ChangeDrawModel(ROLE_SKIN_TYPE.HAIR, new_hair_res)
			elseif k == "eye_size" then
				self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.EyeSize, v)
			elseif k == "eye_pos" then
				self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.EyePosition, v)
			elseif k == "eye_shadow_color" then
				local eye_shadow_alpha = change_info["eye_shadow_alpha"] or 0
				local show_color = UtilU3d.ConvertHexToColor(v)  
				local new_color = {r = math.floor(show_color.r * 255), g = math.floor(show_color.g * 255), b = math.floor(show_color.b * 255), a = math.floor(eye_shadow_alpha)}
				self.role_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.EyeShadowColor, new_color)
			elseif k == "pupil_type" then
				if change_info["pupil_size"] then
					self.role_model:UpdateChangeExtraModelData(HeadCustomizationType.PupilSize_Left, change_info["pupil_size"])
					self.role_model:UpdateChangeExtraModelData(HeadCustomizationType.PupilSize_Right, change_info["pupil_size"])
				end

				if change_info["pupil_color"] then
					local pupil_color_alpha = change_info["pupil_color_alpha"] or 0
					local show_color = UtilU3d.ConvertHexToColor(v)  
					local new_color = {r = math.floor(show_color.r * 255), g = math.floor(show_color.g * 255), b = math.floor(show_color.b * 255), a = math.floor(pupil_color_alpha)}
					self.role_model:UpdateChangeExtraModelData(HeadCustomizationType.PupilColor_Left, new_color)
					self.role_model:UpdateChangeExtraModelData(HeadCustomizationType.PupilColor_Right, new_color)
				end

				self.role_model:ChangeSkinHeadMatTex(HeadCustomizationType.PupilType_Left, v)
				self.role_model:ChangeSkinHeadMatTex(HeadCustomizationType.PupilType_Right, v)
			elseif k == "pupil_size" and change_info["pupil_type"] == nil then
				self.role_model:ChangeSkinHeadMatVal(HeadCustomizationType.PupilSize_Left, v)
				self.role_model:ChangeSkinHeadMatVal(HeadCustomizationType.PupilSize_Right, v)
			elseif k == "pupil_color" and change_info["pupil_type"] == nil then
				local pupil_color_alpha = change_info["pupil_color_alpha"] or 0
				local show_color = UtilU3d.ConvertHexToColor(v)  
				local new_color = {r = math.floor(show_color.r * 255), g = math.floor(show_color.g * 255), b = math.floor(show_color.b * 255), a = math.floor(pupil_color_alpha)}
				self.role_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.PupilColor_Left, new_color)
				self.role_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.PupilColor_Right, new_color)
			elseif k == "mouth_color" then
				local mouth_alpha = change_info["mouth_alpha"] or 0
				local show_color = UtilU3d.ConvertHexToColor(v)  
				local new_color = {r = math.floor(show_color.r * 255), g = math.floor(show_color.g * 255), b = math.floor(show_color.b * 255), a = math.floor(mouth_alpha)}
				self.role_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.MouthColor, new_color)
			elseif k == "mouth_size" then
				self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.MouthSize, v)
			elseif k == "mouth_pos" then
				self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.MouthPosition, v)
			elseif k == "face_decal_id" then
				self.role_model:ChangeSkinHeadMatTex(HeadCustomizationType.FaceDecalTex, v, nil, self.setting_data.sex)
			elseif k == "hair_color" and change_info["hair_id"] == nil then
				local show_color = UtilU3d.ConvertHexToColor(v)  
				local new_color = {r = math.floor(show_color.r * 255), g = math.floor(show_color.g * 255), b = math.floor(show_color.b * 255), a = math.floor(alpha_cfg.hair_alpha or 0)}
				self.role_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, new_color)
			elseif k == "preset_seq" then
				local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.setting_data.sex, self.setting_data.prof, v)
				if preset_diy_cfg then
					self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.EyeAngle, preset_diy_cfg.eye_angle)
					self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.EyeClose, preset_diy_cfg.eye_close)
					self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.EyebrowAngle, preset_diy_cfg.eyebrow_angle)
					self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.NoseSize, preset_diy_cfg.nose_size)
					self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.NoseAngle, preset_diy_cfg.nose_angle)
					self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.MouthAngle, preset_diy_cfg.mouth_angle)
					self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.CheekSize, preset_diy_cfg.cheek_size)
					self.role_model:ChangeSkinHeadRendererBlendShape(HeadCustomizationType.ChinLength, preset_diy_cfg.chin_length)
				end
			end
		end
	end

	self.need_first_load_model = false
end

-- 点击镜头切换
function RoleDiyAppearanceView:ClickEditLens()
    if self.is_btn_cool then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OperateFrequencyTip)
        return
    end

    self.is_btn_cool = true
    self:RemoveCoolDelayTimer()
	self.show_cool_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self.is_btn_cool = false
	end, 1)

    self:FlushLensStatus(not self.is_far)
end

-- 刷新镜头设置
function RoleDiyAppearanceView:FlushLensStatus(is_force_far)
	if self.big_type <= 0 then
		return
	end

	self.is_far = is_force_far
    if self.role_model ~= nil then
		if self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL then
			local focus_part_to_pos = ((FOCUS_PART_TO_POS[0][self.setting_data.sex] or {})[self.setting_data.prof] or {})[self.big_type]
			local focus_part_from_pos = ((FOCUS_PART_FROM_POS[0][self.setting_data.sex] or {})[self.setting_data.prof] or {})[self.big_type]
			local cur_focus_to_scale = CUR_FOCUS_SCALE[0]
			local cur_focus_from_scale = CUR_FOCUS_SCALE[0]
			local node_trans = self.scene_camera and self.scene_camera.transform
			self.role_model:SetModelFocus(focus_part_to_pos, focus_part_from_pos, cur_focus_to_scale, cur_focus_from_scale, 0.4, node_trans)
		else
			local focus_part_to_pos = ((FOCUS_PART_TO_POS[1][self.setting_data.sex] or {})[self.setting_data.prof] or {})[self.big_type]
			local focus_part_from_pos = ((FOCUS_PART_FROM_POS[1][self.setting_data.sex] or {})[self.setting_data.prof] or {})[self.big_type]
			local cur_focus_to_scale = CUR_FOCUS_SCALE[1]
			local cur_focus_from_scale = CUR_FOCUS_SCALE[1]
			self.role_model:SetModelFocus(focus_part_to_pos, focus_part_from_pos, cur_focus_to_scale, cur_focus_from_scale, 0.4)
		end
		

		if self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL then
			local node_trans = self.scene_camera and self.scene_camera.transform
			if self.is_far then
				self.role_model:ModelFocusMove(false, node_trans)
			else
				self.role_model:ModelFocusMove(true, node_trans)
			end
		else
			if self.is_far then
				self.role_model:ModelFocusMove(false)
			else
				self.role_model:ModelFocusMove(true)
			end
		end
    end

    self.node_list.lens_far_img:CustomSetActive(not self.is_far)
    self.node_list.lens_near_img:CustomSetActive(self.is_far)
end

-- return_create  true:手动点击  false：一般为断线重连
function RoleDiyAppearanceView:OnCloseView(return_create)
	if not self.setting_data then
		return
	end

	if return_create then
		local ok_fun = function()
			if self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL then
				local data = {}
				data.operate_type = self.setting_data.operate_type
				data.sex =  self.setting_data.sex
				data.prof = self.setting_data.prof
				data.country = self.setting_data.country
				LoginWGCtrl.Instance:OpenShowRoleAppranceView(data)
			end
		
			self:Close()
		end
	
		local str = Language.RoleDiyAppearanceView.ReturnErrorStr
		TipWGCtrl.Instance:OpenAlertTips(str, ok_fun, nil, nil, nil)
	else
		if self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL then
			LoginWGCtrl.Instance:ChangeLoginViewShow(true)
		end
		self:Close()
	end
end


-- 创建角色按钮
function RoleDiyAppearanceView:OnClickCreateRole()
	if not self.setting_data then
		return
	end

	
	local diy_appearance_info = self:GetCurDiyAppearanceInfo()
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.clickLoginEnterGame)

	local data = {
		sex = self.setting_data.sex,
		prof = self.setting_data.prof,
		country = self.setting_data.country,
		diy_appearance_info = diy_appearance_info,
	}
	
	LoginWGCtrl.Instance:OpenCreateSelectNameView(data)
end

function RoleDiyAppearanceView:GetCurDiyAppearanceInfo()
	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	if IsEmptyTable(cur_show_diy_appearance_data) then
		return
	end

	local alpha_cfg = RoleDiyAppearanceWGData.Instance:GetAlphaCfg(self.setting_data.sex, self.setting_data.prof, cur_show_diy_appearance_data.face_id)
	if not alpha_cfg then
		return
	end

	local eye_shadow_color = UtilU3d.ConvertHexToColor(cur_show_diy_appearance_data.eye_shadow_color) 
	local cur_eye_shadow_color = {r = math.floor(eye_shadow_color.r * 255), g = math.floor(eye_shadow_color.g * 255), b = math.floor(eye_shadow_color.b * 255), a = math.floor(cur_show_diy_appearance_data.eye_shadow_alpha or 0)}
	local pupil_color = UtilU3d.ConvertHexToColor(cur_show_diy_appearance_data.pupil_color) 
	local cur_pupil_color = {r = math.floor(pupil_color.r * 255), g = math.floor(pupil_color.g * 255), b = math.floor(pupil_color.b * 255), a = math.floor(cur_show_diy_appearance_data.pupil_color_alpha or 0)}
	local mouth_color = UtilU3d.ConvertHexToColor(cur_show_diy_appearance_data.mouth_color) 
	local cur_mouth_color = {r = math.floor(mouth_color.r * 255), g = math.floor(mouth_color.g * 255), b = math.floor(mouth_color.b * 255), a = math.floor(cur_show_diy_appearance_data.mouth_alpha or 0)}
	local hair_color = UtilU3d.ConvertHexToColor(cur_show_diy_appearance_data.hair_color) 
	local cur_hair_color = {r = math.floor(hair_color.r * 255), g = math.floor(hair_color.g * 255), b = math.floor(hair_color.b * 255), a = math.floor(alpha_cfg.hair_alpha or 0)}

	local diy_appearance_info = {
		face_id = cur_show_diy_appearance_data.face_id,
		hair_id = cur_show_diy_appearance_data.hair_id,
		body_id =  cur_show_diy_appearance_data.body_id,
		eye_size = cur_show_diy_appearance_data.eye_size,
		eye_shadow_color = cur_eye_shadow_color,
		eye_position = cur_show_diy_appearance_data.eye_pos,
		left_pupil_type = cur_show_diy_appearance_data.pupil_type,
		left_pupil_size = cur_show_diy_appearance_data.pupil_size,
		left_pupil_color = cur_pupil_color,
		right_pupil_type = cur_show_diy_appearance_data.pupil_type,
		right_pupil_size = cur_show_diy_appearance_data.pupil_size,
		right_pupil_color = cur_pupil_color,
		mouth_color = cur_mouth_color,
		mouth_size = cur_show_diy_appearance_data.mouth_size,
		mouth_position = cur_show_diy_appearance_data.mouth_pos,
		face_decal_id = cur_show_diy_appearance_data.face_decal_id,
		hair_color = cur_hair_color,
		preset_seq = cur_show_diy_appearance_data.preset_seq
	}

	return diy_appearance_info
end

function RoleDiyAppearanceView:OnClickChangeDiy()
	if self.setting_data.operate_type ~= ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.diy_consume_id)
	local cost_num = other_cfg.diy_consume_num
	if item_num < cost_num then
		TipWGCtrl.Instance:ShowSystemMsg(Language.DressingRoleDiyView.ErrorTip[2])
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other_cfg.diy_consume_id})
		return
	end

	local diy_list_info = DressingRoleDiyWGData.Instance:GetDiyAppearanceInfoBySexProf(self.setting_data.sex, self.setting_data.prof)
	if not diy_list_info then
		return
	end

	local diy_appearance_info = self:GetCurDiyAppearanceInfo()
	-- 判断选择瞳孔是否激活(左右一样)
	local pupil_cfg = RoleDiyAppearanceWGData.Instance:GetPupilTypeByIdCfg(diy_appearance_info.left_pupil_type)
	if pupil_cfg and pupil_cfg.is_pay_type == 1 then
		local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(pupil_cfg.pay_type, pupil_cfg.pay_seq)
		if active_flag ~= 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleDiyAppearanceView.PupilError)
			return
		end
	end

	local face_decal_cfg = RoleDiyAppearanceWGData.Instance:GetFaceDecalByIdCfg(diy_appearance_info.face_decal_id)
	if face_decal_cfg and face_decal_cfg.is_pay_type == 1 then
		local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(face_decal_cfg.pay_type, face_decal_cfg.pay_seq)
		if active_flag ~= 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleDiyAppearanceView.FaceDecalError)
			return
		end
	end

	local set_num = 0
	for k, v in pairs(diy_list_info.project_list) do
		if v.is_set == 1 then
			set_num = set_num + 1
		end
	end

	if set_num >= (#diy_list_info.project_list + 1) then
		local open_data = {}
		open_data.diy_appearance_info = diy_appearance_info
		open_data.sex = self.setting_data.sex
		open_data.prof = self.setting_data.prof
		DressingRoleDiyWGCtrl.Instance:OpeReplaceDiyView(open_data)
		self:Close()
	else
		--方案从0开始
		local str = string.format(Language.DressingRoleDiyView.DyrNewStr, string.format(Language.DressingRoleDiyView.DiyName[0], set_num + 1))
		local ok_func = function ()
			DressingRoleDiyWGCtrl.Instance:SendCSSetDiyAppearance(self.setting_data.sex, self.setting_data.prof, set_num, diy_appearance_info)
			self:Close()
		end
		TipWGCtrl.Instance:OpenAlertTips(str, ok_func)
	end
end

function RoleDiyAppearanceView:FlushChangeCostInfo()
	if self.setting_data.operate_type ~= ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY then
		return
	end

	local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
	self.change_diy_cost_cell:SetData({item_id = other_cfg.diy_consume_id})

	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.diy_consume_id)
	local cost_num = other_cfg.diy_consume_num
	local str = item_num .. "/" .. cost_num
    local color = item_num >= cost_num and COLOR3B.GREEN or COLOR3B.RED
	self.node_list.change_diy_cost_num.text.text = ToColorStr(str, color)
end

function RoleDiyAppearanceView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		local other_cfg = RoleDiyAppearanceWGData.Instance:GetOtherCfg()
		if change_item_id == other_cfg.diy_consume_id then
			self:FlushChangeCostInfo()
		end
	
		if RoleDiyAppearanceWGData.Instance:IsPayTypeConsumeItemid(change_item_id) then
			self:FlushPupilTypeActInfo()
			self:FlushFaceDecalActInfo()
			self:FlushFaceDecalPanel()
			self:FlushPupilTypePanel()
			self:FlushRedInfoPanel()
		end
	end
end

function RoleDiyAppearanceView:FlushSpeShowPanel()
	local set_default_state = function (diy_data)
		self.node_list["change_diy_btn"]:SetActive(self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY)
		self.node_list["change_diy_cost"]:SetActive(self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY)
		self.node_list["change_diy_cost_num"]:SetActive(self.setting_data.operate_type == ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY)
		self.node_list.pupil_type_act_group:SetActive(false)
		self.node_list.face_deca_act_group:SetActive(false)
	end

	if self.setting_data.operate_type ~= ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY or self.big_type ~= CUR_SHOW_BIG_TYPE.BEAUTY_DIY then
		set_default_state()
		return
	end

	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()

	if self.beauty_diy_type == CUR_BEAUTY_DIY_TYPE.PUPIL then
		local cfg = RoleDiyAppearanceWGData.Instance:GetPupilTypeByIdCfg(cur_show_diy_appearance_data.pupil_type)
		if cfg and cfg.is_pay_type == 1 then
			local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(cfg.pay_type, cfg.pay_seq)
			if active_flag == 1 then
				set_default_state()
			else
				self.node_list["change_diy_btn"]:SetActive(self.setting_data.operate_type == false)
				self.node_list["change_diy_cost"]:SetActive(self.setting_data.operate_type == false)
				self.node_list["change_diy_cost_num"]:SetActive(self.setting_data.operate_type == false)
				self.node_list.pupil_type_act_group:SetActive(true)
				self.node_list.face_deca_act_group:SetActive(false)
				self:FlushPupilTypeActInfo()
			end
		else
			set_default_state()
		end
	elseif self.beauty_diy_type == CUR_BEAUTY_DIY_TYPE.FACE_DECAL then
		local cfg = RoleDiyAppearanceWGData.Instance:GetFaceDecalByIdCfg(cur_show_diy_appearance_data.face_decal_id)
		if cfg and cfg.is_pay_type == 1 then
			local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(cfg.pay_type, cfg.pay_seq)
			if active_flag == 1 then
				set_default_state()
			else
				self.node_list["change_diy_btn"]:SetActive(self.setting_data.operate_type == false)
				self.node_list["change_diy_cost"]:SetActive(self.setting_data.operate_type == false)
				self.node_list["change_diy_cost_num"]:SetActive(self.setting_data.operate_type == false)
				self.node_list.pupil_type_act_group:SetActive(false)
				self.node_list.face_deca_act_group:SetActive(true)
				self:FlushFaceDecalActInfo()
			end
		else
			set_default_state()
		end
	else
		set_default_state()
	end
end

function RoleDiyAppearanceView:FlushPupilTypeActInfo()
	if self.setting_data.operate_type ~= ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY then
		return
	end

	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	local cfg = RoleDiyAppearanceWGData.Instance:GetPupilTypeByIdCfg(cur_show_diy_appearance_data.pupil_type)
	if cfg and cfg.is_pay_type == 1 then
		local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(cfg.pay_type, cfg.pay_seq)
		local pay_cfg = RoleDiyAppearanceWGData.Instance:GetPayCfgByTypeSeq(cfg.pay_type, cfg.pay_seq)
        if pay_cfg then
			self.pupil_type_act_cost_cell:SetData({item_id = pay_cfg.consume_id})
			local item_num = ItemWGData.Instance:GetItemNumInBagById(pay_cfg.consume_id)
			local cost_num = pay_cfg.consume_num
			local str = item_num .. "/" .. cost_num
			local color = item_num >= cost_num and COLOR3B.GREEN or COLOR3B.RED
			self.node_list.pupil_type_act_cost_num.text.text = ToColorStr(str, color)
        end
	end
end

function RoleDiyAppearanceView:OnClickPupilTypeAct()
	if self.setting_data.operate_type ~= ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY then
		return
	end

	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	local cfg = RoleDiyAppearanceWGData.Instance:GetPupilTypeByIdCfg(cur_show_diy_appearance_data.pupil_type)
	if cfg and cfg.is_pay_type ~= 1 then
		return
	end

	local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(cfg.pay_type, cfg.pay_seq)
	local pay_cfg = RoleDiyAppearanceWGData.Instance:GetPayCfgByTypeSeq(cfg.pay_type, cfg.pay_seq)
	if active_flag ~= 1 then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(pay_cfg.consume_id)
		local cost_num = pay_cfg.consume_num

		if item_num >= pay_cfg.consume_num then
			DressingRoleDiyWGCtrl.Instance:SendCSDiyAppearanceOperate(DIY_APPEARANCE_OPER_TYPE.ACTIVE_TYPE, cfg.pay_type, cfg.pay_seq)
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = pay_cfg.consume_id})
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
		end
	end
end


function RoleDiyAppearanceView:FlushFaceDecalActInfo()
	if self.setting_data.operate_type ~= ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY then
		return
	end

	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	local cfg = RoleDiyAppearanceWGData.Instance:GetFaceDecalByIdCfg(cur_show_diy_appearance_data.face_decal_id)
	if cfg and cfg.is_pay_type == 1 then
		local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(cfg.pay_type, cfg.pay_seq)
		local pay_cfg = RoleDiyAppearanceWGData.Instance:GetPayCfgByTypeSeq(cfg.pay_type, cfg.pay_seq)
        if pay_cfg then
			self.face_deca_act_cost_cell:SetData({item_id = pay_cfg.consume_id})
			local item_num = ItemWGData.Instance:GetItemNumInBagById(pay_cfg.consume_id)
			local cost_num = pay_cfg.consume_num
			local str = item_num .. "/" .. cost_num
			local color = item_num >= cost_num and COLOR3B.GREEN or COLOR3B.RED
			self.node_list.face_deca_act_cost_num.text.text = ToColorStr(str, color)
        end
	end
end

function RoleDiyAppearanceView:OnClickFaceDecalAct()
	if self.setting_data.operate_type ~= ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY then
		return
	end

	local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
	local cfg = RoleDiyAppearanceWGData.Instance:GetFaceDecalByIdCfg(cur_show_diy_appearance_data.face_decal_id)
	if cfg and cfg.is_pay_type ~= 1 then
		return
	end

	local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(cfg.pay_type, cfg.pay_seq)
	local pay_cfg = RoleDiyAppearanceWGData.Instance:GetPayCfgByTypeSeq(cfg.pay_type, cfg.pay_seq)
	if active_flag ~= 1 then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(pay_cfg.consume_id)
		local cost_num = pay_cfg.consume_num

		if item_num >= pay_cfg.consume_num then
			DressingRoleDiyWGCtrl.Instance:SendCSDiyAppearanceOperate(DIY_APPEARANCE_OPER_TYPE.ACTIVE_TYPE, cfg.pay_type, cfg.pay_seq)
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = pay_cfg.consume_id})
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
		end
	end
end

function RoleDiyAppearanceView:FlushRedInfoPanel()
	if self.setting_data.operate_type ~= ROLE_DIY_APPEARANCE_TYPE.CHANGE_DIY then
		self.node_list.diy_type_red_2:SetActive(false)
		self.node_list.beauty_diy_red_2:SetActive(false)
		self.node_list.beauty_diy_red_4:SetActive(false)
		return
	end

	local pupil_type_red = false
	local face_decal_red = false

	local pupil_type_cfg = RoleDiyAppearanceWGData.Instance:GetPupilTypeCfg(self.setting_data.operate_type)
	for i, v in pairs(pupil_type_cfg) do
		local pay_cfg = RoleDiyAppearanceWGData.Instance:GetPayCfgByTypeSeq(v.pay_type, v.pay_seq)
		local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(v.pay_type, v.pay_seq)
		if v.is_pay_type == 1 and pay_cfg and active_flag ~= 1 then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(pay_cfg.consume_id)
			local cost_num = pay_cfg.consume_num
			if item_num >= cost_num then
				pupil_type_red = true
				break
			end
		end
	end

	local face_decal_cfg = RoleDiyAppearanceWGData.Instance:GetFaceDecalCfg(self.setting_data.operate_type)
	for i, v in pairs(face_decal_cfg) do
		local pay_cfg = RoleDiyAppearanceWGData.Instance:GetPayCfgByTypeSeq(v.pay_type, v.pay_seq)
		local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(v.pay_type, v.pay_seq)
		if v.is_pay_type == 1 and pay_cfg and active_flag ~= 1 then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(pay_cfg.consume_id)
			local cost_num = pay_cfg.consume_num
			if item_num >= cost_num then
				face_decal_red = true
				break
			end
		end
	end

	self.node_list.diy_type_red_2:SetActive(pupil_type_red or face_decal_red)
	self.node_list.beauty_diy_red_2:SetActive(pupil_type_red)
	self.node_list.beauty_diy_red_4:SetActive(face_decal_red)
end