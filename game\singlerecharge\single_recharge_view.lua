local SINGLE_RECHARGE_OPERATE_TYPE = {
    REQUEST_DATA = 0,
    RECEIVE_REWARD = 1,
}

SingleReChargeView = SingleReChargeView or BaseClass(SafeBaseView)

function SingleReChargeView:__init()
    self:SetMaskBg(true, true)
    self.view_name = GuideModuleName.SingleReChargeView
    self:AddViewResource(0, "uis/view/singlerecharge_prefab", "layout_single_recharge_view")
end

function SingleReChargeView:LoadCallBack()
    if not self.single_recharge_list then
        self.single_recharge_list = AsyncListView.New(SingleRechargeCellRender, self.node_list.single_recharge_list)
    end
end

function SingleReChargeView:OpenCallBack()
    SingleRechargeWgCtrl.Instance:SendOperateReq(SINGLE_RECHARGE_OPERATE_TYPE.REQUEST_DATA)
end

function SingleReChargeView:ReleaseCallBack()
    if self.single_recharge_list then
        self.single_recharge_list:DeleteMe()
        self.single_recharge_list = nil
    end
end

function SingleReChargeView:OnFlush(param_t)
    local data_list = SingleRechargeWgData.Instance:GetSingleRechargeListData()
    if IsEmptyTable(data_list) then
        return
    end
    self.single_recharge_list:SetDataList(data_list)
end

-------------------------------------item_cell------------------------------------------------------------
SingleRechargeCellRender = SingleRechargeCellRender or BaseClass(BaseRender)

function SingleRechargeCellRender:__init()

end

function SingleRechargeCellRender:__delete()
    if self.recharge_cell_reward_lis then
        self.recharge_cell_reward_lis:DeleteMe()
        self.recharge_cell_reward_lis = nil
    end
end

function SingleRechargeCellRender:LoadCallBack()
    if not self.recharge_cell_reward_list then
        self.recharge_cell_reward_list = AsyncListView.New(ItemCell, self.node_list.recharge_cell_reward_list)
        self.recharge_cell_reward_list:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list.btn_recharge, BindTool.Bind1(self.OnClickRechargeBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind1(self.OnClickGetBtn, self))
end

function SingleRechargeCellRender:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then 
        return
    end
    self.recharge_cell_reward_list:SetDataList(data.reward_item)

    local RNB_NUM = data.charge_value > 0 and data.charge_value / RECHARGE_BILI or 0

    --游戏点字数超出
    if RoleWGData.Instance:GetPayMoneyType() == PAY_MONEY_TYPES.GAME_COUNT then
        self.node_list.text_recharge_target_num.text.fontSize = 20
    else
        self.node_list.text_recharge_target_num.text.fontSize = 22
    end
    --根据充值界面参数获取的,后期如果充值界面获取参数变了需要修改
    local price_str = RoleWGData.GetPayMoneyStr(RNB_NUM, 0, 0)
    self.node_list.text_recharge_target_num.text.text = string.format(Language.SingleRecharge.RecgargeDes,price_str)

    local fetch_flag = data.fetch_flag == 1
    local can_fetch_flag = data.can_fetch_flag == 1
    self.node_list.flag_hasget:SetActive(fetch_flag)
    self.node_list.btn_get:SetActive(can_fetch_flag and not fetch_flag)
    self.node_list.btn_recharge:SetActive(not can_fetch_flag and not fetch_flag)
end

function SingleRechargeCellRender:OnClickRechargeBtn()
    ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
end

function SingleRechargeCellRender:OnClickGetBtn()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return 
    end
    SingleRechargeWgCtrl.Instance:SendOperateReq(SINGLE_RECHARGE_OPERATE_TYPE.RECEIVE_REWARD, data.seq)
end
