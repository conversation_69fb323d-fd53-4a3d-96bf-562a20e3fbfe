﻿using UnityEngine;

[ExecuteInEditMode]
[RequireComponent(typeof(MeshFilter))]
[RequireComponent(typeof(MeshRenderer))]
public class MeshSprite : MonoBehaviour
{
    [SerializeField]
    private Sprite m_sprite;
    private MeshFilter meshFilter;
    private MeshRenderer meshRenderer;

    private static Material m_material;

    public Sprite sprite
    {
        get
        {
            return m_sprite;
        }
        set
        {
            m_sprite = value;
            SetMainTexture();
            GenerateFilter();
        }
    }

    private Material material
    {
        get
        {
            if (null == m_material)
            {
                m_material = new Material(Shader.Find("UI/SpriteMesh"));
            }
            return m_material;
        }
    }

    void Awake()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshRenderer = GetComponent<MeshRenderer>();
        meshRenderer.sharedMaterial = material;

        if (null != m_sprite)
        {
            SetMainTexture();
            GenerateFilter();
        }
    }

    private void SetMainTexture()
    {
        meshRenderer = GetComponent<MeshRenderer>();
        if (null != m_sprite)
        {
            Texture texture = m_sprite.texture;
            MaterialPropertyBlock block = new MaterialPropertyBlock();
            block.SetTexture("_MainTex", texture);
            meshRenderer.SetPropertyBlock(block);
        }
        else
        {
            meshRenderer.SetPropertyBlock(null);
        }
    }

    public void GenerateFilter()
    {
        if (null == m_sprite)
            return;

        Mesh mesh = new Mesh();
        Vector3[] vertices = new Vector3[m_sprite.vertices.Length];
        int[] triangles = new int[m_sprite.triangles.Length];

        for (int i = 0; i < vertices.Length; ++i)
        {
            vertices[i] = m_sprite.vertices[i];
        }

        for (int i = 0; i < triangles.Length; ++i)
        {
            triangles[i] = m_sprite.triangles[i];
        }

        mesh.vertices = vertices;
        mesh.triangles = triangles;
        mesh.uv = m_sprite.uv;

        meshFilter.mesh = mesh;
    }

    void OnDrawGizmos()
    {
        Gizmos.color = Color.gray;
        DrawMesh();
    }

    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.green;
        DrawMesh();
    }

    private void DrawMesh()
    {
        if (meshFilter == null)
        {
            return;
        }
        Mesh mesh = meshFilter.sharedMesh;
        if (mesh == null)
        {
            return;
        }
        int[] tris = mesh.triangles;
        for (int i = 0; i < tris.Length; i += 3)
        {
            Gizmos.DrawLine(convert2World(mesh.vertices[tris[i]]), convert2World(mesh.vertices[tris[i + 1]]));
            Gizmos.DrawLine(convert2World(mesh.vertices[tris[i]]), convert2World(mesh.vertices[tris[i + 2]]));
            Gizmos.DrawLine(convert2World(mesh.vertices[tris[i + 1]]), convert2World(mesh.vertices[tris[i + 2]]));
        }
    }

    private Vector3 convert2World(Vector3 src)
    {
        return transform.TransformPoint(src);
    }
}