BOSSInvasionBossQualityAddTip = BOSSInvasionBossQualityAddTip or BaseClass(SafeBaseView)

function BOSSInvasionBossQualityAddTip:__init()
	self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(602, 428)})
	self:AddViewResource(0, "uis/view/boss_invasion_ui_prefab", "layout_boss_invasion_boss_quality_add_tip")
end

function BOSSInvasionBossQualityAddTip:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["buy_add_btn"], BindTool.Bind(self.OnClickBuyAddBtn, self))
    XUI.AddClickEventListener(self.node_list["buy_sub_btn"], BindTool.Bind(self.OnClickBuySubBtn, self))
    XUI.AddClickEventListener(self.node_list["buy_count_bg"], BindTool.Bind(self.OnClickBuyCount, self))
    XUI.AddClickEventListener(self.node_list["buy_one_key_max_btn"], BindTool.Bind(self.OnClickOneKeyMaxBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_add_pro"], BindTool.Bind(self.OnClickAddProBtn, self))

    if not self.data_list then
        self.data_list = AsyncListView.New(ItemCell, self.node_list.data_list)
        self.data_list:SetStartZeroIndex(false)
    end

    self.buy_count = 1
end

function BOSSInvasionBossQualityAddTip:ReleaseCallBack()
    if self.data_list then
        self.data_list:DeleteMe()
        self.data_list = nil
    end
end

function BOSSInvasionBossQualityAddTip:OnFlush()
    self:FlushBuyPanel()
end

function BOSSInvasionBossQualityAddTip:OnClickAddProBtn()
	local is_max_quality = BOSSInvasionWGData.Instance:IsBossMaxQuality()

	if is_max_quality then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.BossQualityMAxColor)
	else
        -- local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION)
        -- if act_is_open then
        --     SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.ACTIsOpenCanNotUpQuality)
        --     return
        -- end

        local status = BOSSInvasionWGData.Instance:GetCurActStatus()

        if status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS or status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS_END then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.ACTIsOpenCanNotUpQuality)
            return
        end

        local other_cfg = BOSSInvasionWGData.Instance:GetOtherCfg()
        local need_gold = other_cfg.up_color_cost_gold * self.buy_count

        local gold = RoleWGData.Instance:GetAttr('gold')
        if gold < need_gold then
            VipWGCtrl.Instance:OpenTipNoGold()
            return
        end

	    BOSSInvasionWGCtrl.Instance:SendBOSSInvasionReq(CROSS_BOSS_STRIKE_OPERATE_TYPE.UP_BOSS_COLOR, self.buy_count)
    end
end

function BOSSInvasionBossQualityAddTip:FlushBuyPanel()
    local other_cfg = BOSSInvasionWGData.Instance:GetOtherCfg()

    if other_cfg then
        self.node_list.desc_tip.tmp.text = string.format(Language.BOSSInvasion.DescAddQualityCost, other_cfg.up_color_cost_gold * self.buy_count, self.buy_count * other_cfg.up_color_add_num)

        local data_list_cfg = other_cfg.up_color_reward_item_show
        local target_data_list = {}

        for k, v in pairs(data_list_cfg) do
            table.insert(target_data_list, {item_id = v.item_id, num = v.num * self.buy_count, is_bind = v.is_bind})
        end

        self.data_list:SetDataList(target_data_list)
    end

    self.node_list["buy_count_label"].tmp.text = self.buy_count
end

function BOSSInvasionBossQualityAddTip:OnClickBuyAddBtn()
    local max_buy_count = BOSSInvasionWGData.Instance:GetCanAddBossQualityCount()

    if self.buy_count < max_buy_count then
        self.buy_count = self.buy_count + 1

        self:FlushBuyPanel()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.BossQualityMaxBuyCount)
    end
end

function BOSSInvasionBossQualityAddTip:OnClickBuySubBtn()
    if self.buy_count > 1 then
        self.buy_count = self.buy_count - 1
        self:FlushBuyPanel()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.BossQualityMinBuyCount)
    end
end

function BOSSInvasionBossQualityAddTip:OnClickBuyCount()
    local max_buy_count = BOSSInvasionWGData.Instance:GetCanAddBossQualityCount()
    local pop_num_view = TipWGCtrl.Instance:GetPopNumView()
	pop_num_view:Open()
	pop_num_view:SetText(1)
	pop_num_view:SetMinValue(1)
	pop_num_view:SetMaxValue(max_buy_count)
	pop_num_view:SetOkCallBack(function (num)
		self.buy_count = num
		self:FlushBuyPanel()
	end)
end

function BOSSInvasionBossQualityAddTip:OnClickOneKeyMaxBtn()
    local max_buy_count = BOSSInvasionWGData.Instance:GetCanAddBossQualityCount()
    self.buy_count = max_buy_count
    self:FlushBuyPanel()
end