function QiFuView:DelGetEnergyView()

    if self.get_item_mid then
        for k, v in pairs(self.get_item_mid) do
            v:DeleteMe()
        end
        self.get_item_mid = nil
    end

    if self.get_item_night then
        for k, v in pairs(self.get_item_night) do
            v:DeleteMe()
        end
        self.get_item_night = nil
    end
  
    self:CleanQiFuTimer()
end

function QiFuView:InitGetEnergyView()
    self.get_item_mid = {}
    self.get_item_night = {}
    XUI.AddClickEventListener(self.node_list["btn_get_1"], BindTool.Bind2(self.OnClickGet, self ,1))
    XUI.AddClickEventListener(self.node_list["btn_get_2"], BindTool.Bind2(self.OnClickGet, self ,2))
end

function QiFuView:OnClickGet(seq)
    local can_get = BossWGData.Instance:QifuCanGetBossXianli(seq)
    if can_get then
        local  target_seq = seq - 1
        BossAssistWGCtrl.Instance:SendGet<PERSON><PERSON><PERSON>(target_seq)
    else
        SysMsgWGCtrl.Instance:<PERSON>rror<PERSON><PERSON><PERSON>(Language.Boss.HadPassTime)
    end
end

function QiFuView:FlushGetEnergyView()
    local cfg = BossWGData.Instance:GetBossXianliCfg()
    for i = 1, 2 do
        local temp_cfg = cfg[i]
        if temp_cfg then
            local welfare_state = self:GetStateBySeq(temp_cfg.seq)
            local welfare_time = ToColorStr(self:GetTimerStr(temp_cfg.start_time, temp_cfg.end_time),COLOR3B.L_GREEN)
            local time_str = Language.Boss.Time_Name_Str[i]..welfare_time
            self.node_list["getenergy_time_txt_"..i].text.text = time_str
            if not IsEmptyTable(cfg) and not IsEmptyTable(temp_cfg) then
                local item = temp_cfg.reward_item
                if not IsEmptyTable(item) then
                    if i == 1 then
                        for k, v in pairs(item) do
                            self.get_item_mid[k] =  self.get_item_mid[k] or ItemCell.New(self.node_list["itemcell_layout_"..i])
                            self.get_item_mid[k]:SetData(v)
                        end
                    else
                        for k, v in pairs(item) do
                            self.get_item_night[k] =  self.get_item_night[k] or ItemCell.New(self.node_list["itemcell_layout_"..i])
                            self.get_item_night[k]:SetData(v)
                        end
                    end
                end
            end
            local can_get = BossWGData.Instance:QifuCanGetBossXianli(i)
            self.node_list["get_text_"..i].text.text = Language.Boss.GetStateStr[welfare_state - 1]
            self.node_list["canget_remind_"..i]:SetActive(can_get)
            self.node_list["btn_get_"..i]:SetActive(welfare_state ~= 3)
            self.node_list["qifu_reward_get_flag_"..i]:SetActive(welfare_state == 3)
            XUI.SetButtonEnabled(self.node_list["btn_get_"..i], can_get)
        end
    end
    
    -- local is_show_lingli = BossWGData.Instance:GetIsShowLingLi()
    -- self.node_list["xianli_root"]:SetActive(is_show_lingli > 0)
    self:FlushTimer()
end

function QiFuView:SetImgAsset(name, node_list)
    local bundle, asset = ResPath.GetBossAssitImg(name)
    node_list.image:LoadSprite(bundle, asset)
end

function QiFuView:GetTimerStr(start_time, end_time)
    local hour1 = string.format("%02d",start_time / 100)
    local min1 = string.format("%02d",start_time % 100)
    local hour2 = string.format("%02d",end_time / 100)
    local min2 = string.format("%02d",end_time % 100) 
    return hour1 .. ":" .. min1 .. "-".. hour2 .. ":" .. min2
end

--已错过1  可领取2  已领取3  时间未到4
function QiFuView:GetStateBySeq(seq)
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local flag_tb = BossAssistWGData.Instance:GetXianliFlagTbIndex(seq)
    local cfg = BossWGData.Instance:GetBossXianliCfg()
    local temp = cfg[seq + 1]
    local start_time = TimeUtil.FormatCfgTimestamp(temp.start_time)
    local end_time = TimeUtil.FormatCfgTimestamp(temp.end_time)
    if flag_tb == 1 then
        return 3
    end
    if start_time <= server_time and server_time <= end_time then
        return 2
    elseif start_time > server_time then --只能在时间段内打开界面
        return 4
    elseif end_time < server_time then
        return 1
    end
end

function QiFuView:FlushTimer()
    local has_reward ,timer = BossWGData.Instance:GetQiFiGetEnergyTimeRemaining()
    if has_reward then
    local interval = 1          -- 回调间隔
    self:CleanQiFuTimer()
    self.qifu_getenergy_timer = CountDown.Instance:AddCountDown(timer, interval,
        function()
        end,
        function()
            RemindManager.Instance:Fire(RemindName.QiFuGetEnergy)
            self:FlushGetEnergyView()
        end
    )
    end
end

function QiFuView:CleanQiFuTimer()
    if self.qifu_getenergy_timer and CountDown.Instance:HasCountDown(self.qifu_getenergy_timer) then
        CountDown.Instance:RemoveCountDown(self.qifu_getenergy_timer)
        self.qifu_getenergy_timer = nil
    end
end