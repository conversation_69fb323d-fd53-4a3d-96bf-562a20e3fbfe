CommonSkillShowData = CommonSkillShowData or BaseClass()

function CommonSkillShowData:__init()
	if CommonSkillShowData.Instance then
		error("[CommonSkillShowData] Attempt to create singleton twice!")
		return
	end

    local cfg = ConfigManager.Instance:GetAutoConfig("role_skill_perform_auto")
    self.full_show_cfg = cfg.full_show
    self.mabi_skill_show_cfg = cfg.boss_mabi
    self.mabi_tab_cfg = ListToMap(cfg.boss_mabi_tab, "id")

    self.mabi_attr_list = {}
	for key, value in pairs(self.mabi_skill_show_cfg) do
        table.insert(self.mabi_attr_list, value.attr_name)
    end

	CommonSkillShowData.Instance = self
end

function CommonSkillShowData:__delete()
    CommonSkillShowData.Instance = nil
end

function CommonSkillShowData.GetBuffVo()
    return {
        target_type = 0,                -- 0自身、1怪
        skill_id = 0,
        buff_id = 0,
        fight_effect_type = 0,
        buff_type = 0,
        alive_time = 2,
        delay_play_time = 0,
    }
end

function CommonSkillShowData.GetSkillAttachIDListByCfg(cfg)
    local attach_id_list = {}
    if not cfg then
        return attach_id_list
    end

    local condition_id, attach_id = 0, 0
    for info_index = 0, 4 do
        local str = cfg["attach_" .. info_index]
        if str and str ~= "" then
            local str_list = Split(str, "|")
            condition_id = tonumber(str_list[1]) or 0
            attach_id = tonumber(str_list[3]) or 0
            if condition_id ~= 0 and attach_id > 0 then
                table.insert(attach_id_list, attach_id)
            end
        end
    end

    return attach_id_list
end

-- 角色技能 Buff列表
function CommonSkillShowData:GetCommonSkillBuffList(skill_id, level)
    local buff_list = {}
    local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, level)
    if not skill_cfg then
        return buff_list
    end

    local attach_id_list = self.GetSkillAttachIDListByCfg(skill_cfg)
    -- print_error("---data  attach_id_list---", attach_id_list)
    local add_buff, target_type = false, 1
    for i, attach_id in ipairs(attach_id_list) do
        local attach_info = SkillWGData.Instance:GetSkillAttachInfoById(attach_id)
        -- print_error("---data  attach_info---", attach_id, attach_info)
        if attach_info then
            for info_index, info in pairs(attach_info) do
                local type = info[1] or 0
                local buff_id = info[3] or 0
                add_buff, target_type = false, 1
                if type == ATTACH_SKILL_TYPE.ADD_EFFECT then
                    add_buff = true
                    target_type = 0
                elseif type == ATTACH_SKILL_TYPE.TARGET_BUFF
                or type == ATTACH_SKILL_TYPE.NOT_BOSS_ADD_EFFECT then
                    add_buff = true
                    target_type = 1
                end

                if add_buff then
                    local buff_cfg = SkillWGData.Instance:GetBuffCfgById(buff_id)
                    if buff_cfg then
                        local buff_vo = self.GetBuffVo()
                        buff_vo.skill_id = skill_id
                        buff_vo.buff_id = buff_id
                        buff_vo.target_type = target_type
                        buff_vo.fight_effect_type = buff_cfg.buff_type
                        buff_vo.buff_type = buff_cfg.sub_type
                        buff_vo.alive_time = buff_cfg.buff_time / 1000
                        buff_vo.delay_play_time = 0
                        table.insert(buff_list, buff_vo)
                    end
                end
            end
        end
    end

    return buff_list
end

-- 角色技能 场景攻击信息
function CommonSkillShowData:GetCommonSkillData(skill_id, level)
    local skill_data = {}
    local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, level)
    if not skill_cfg then
        return skill_data
    end

    local range_type = skill_cfg.range_type
    local attach_id_list = self.GetSkillAttachIDListByCfg(skill_cfg)
    local add_buff, target_type = false, 1
    for i, attach_id in ipairs(attach_id_list) do
        local attach_info = SkillWGData.Instance:GetSkillAttachInfoById(attach_id)
        if attach_info then
            for info_index, info in pairs(attach_info) do
                local type = info[1] or 0
                local buff_id = info[3] or 0
                add_buff, target_type = false, 1
                if type == ATTACH_SKILL_TYPE.ADD_SCENE_EFFECT then
                    skill_data.is_scene_effect = true
                    skill_data.target_type = (range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET or range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET) and 1 or 0
                    local cfg = SkillWGData.Instance:GetSceneEffectCfgById(buff_id)
                    if cfg then
                        local alive_time = cfg.effect_time / 1000
                        skill_data.alive_time = alive_time
                        -- 目前只处理法阵类的场景特效
                        if cfg.effect_type == 1 then
                            local times_hurt_table = {}
                            table.insert(times_hurt_table, 0)
                            for num = 1, cfg.param0 - 1 do
                                table.insert(times_hurt_table, alive_time / cfg.param0)
                            end

                            skill_data.times_hurt_table = times_hurt_table
                        end

                        local scene_effect_cfg = SkillWGData.Instance:GetSceneBuffEffectCfg(cfg.product_method, skill_id)
                        if scene_effect_cfg then
                            skill_data.effect_bundle = scene_effect_cfg.bunble
                            skill_data.effect_asset = scene_effect_cfg.asset
                        end
                    end
                end
            end
        end
    end

    return skill_data
end

-- 技能释放位置
function CommonSkillShowData.GetSkillStartPosIndex(skill_id, level)
    local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, level)
    if not skill_cfg then
        return 0
    end

    local range_type = skill_cfg.range_type
    if range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET or range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET then
        return 1
    end

    return 0
end

-- 获取角色的普攻技能列表
function CommonSkillShowData.GetRoleNormalSkillList(prof, sex)
    prof = prof or RoleWGData.Instance:GetRoleProf()
    sex = sex or RoleWGData.Instance:GetRoleSex()
    local cfg = ConfigManager.Instance:GetAutoConfig("roleskill_auto").normal_skill
    local list = {}
    for index, v in pairs(cfg) do
        if v.prof_limit == prof and v.sex_limit == sex then
            table.insert(list, {skill_id = v.skill_id})
        end
    end

    SortTools.SortAsc(list, "skill_id")
    return list
end

-- 独尊领域（搞的系统技能 未走 角色技能表）
function CommonSkillShowData:GetFaZhenSkillBuffList(fazhen_skill_id)
    local buff_list = {}
    local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(fazhen_skill_id)
    if not skill_cfg then
        return buff_list
    end

    local buff_vo = self.GetBuffVo()
    buff_vo.target_type = 1
    buff_vo.buff_type = skill_cfg.param4
    buff_vo.alive_time = skill_cfg.param5 / 1000
    buff_vo.delay_play_time = skill_cfg.delay / 1000

    table.insert(buff_list, buff_vo)
    return buff_list
end

function CommonSkillShowData:GetTianShenShowList()
    local list = {}
    local cfg = TianShenWGData.Instance:GetTianShenImageCfg()
    for k,v in pairs(cfg) do
        table.insert(list, v)
    end

    SortTools.SortAsc(list, "sort_index")
    return list
end

function CommonSkillShowData:GetTianShenShowByLevelLimitList()
    local list = {}
    local cfg = TianShenWGData.Instance:GetTianShenSkillInfoDataList()
    for k,v in pairs(cfg) do
        table.insert(list, v)
    end

    SortTools.SortAsc(list, "sort_index")
    return list
end

function CommonSkillShowData:GetSkillViewShowParams(skill_id)
    return self.full_show_cfg[skill_id]
end

function CommonSkillShowData:GetBossMaBiSkillSHowCfg()
    return self.mabi_skill_show_cfg
end

--获取boss麻痹功能子页签索引列表.
function CommonSkillShowData:GetBossMaBiTabIndexList(index)
    local tab_lits = {}

	if self.mabi_skill_show_cfg[index] and self.mabi_skill_show_cfg[index].tab_index_list then
		tab_lits = Split(self.mabi_skill_show_cfg[index].tab_index_list, ",")
	end

    return tab_lits
end

--获取boss麻痹功能属性名列表.
function CommonSkillShowData:GetBossMaBiAttrNameList()
    return self.mabi_attr_list
end

function CommonSkillShowData:GetBossMaBiTabCfg(id)
    return self.mabi_tab_cfg[id] or {}
end

-- 获取技能召唤物列表
function CommonSkillShowData:GetCommonSkillSummonObjList(skill_id, level)
    local summon_obj_list = {}
    local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, level)
    if not skill_cfg then
        return summon_obj_list
    end

    local attach_id_list = self.GetSkillAttachIDListByCfg(skill_cfg)
    local type, monster_id = 0, 0
    -- local type, monster_id, alive_time, look_dir, buff_id, rel_pos_x, rel_pos_y = 0, 0, 0, 0, 0, 0, 0
    for i, attach_id in ipairs(attach_id_list) do
        local attach_info = SkillWGData.Instance:GetSkillAttachInfoById(attach_id)
        if attach_info then
            for info_index, info in pairs(attach_info) do
                type = info[1] or 0
                if type == ATTACH_SKILL_TYPE.CALL_MONSTER then
                    monster_id = info[3] or 0
                    local data = {
                        monster_id = monster_id,
                        alive_time = info[4] or 0,
                        look_dir = info[6] or 0,
                        buff_id = info[7] or 0,
                        rel_pos_x = info[8] or 0,
                        rel_pos_y = info[9] or 0,
                    }

                    table.insert(summon_obj_list, data)
                end
            end
        end
    end

    return summon_obj_list
end



local is_attack_buff_list = {
	[BUFF_TYPE.EBT_KNOCK_FLY] = true, 							    -- 击飞 跟眩晕一样不能动 但是受击效果不一样
	[BUFF_TYPE.EBT_TYPE_CLASH] = true,						    	-- 冲撞
	[BUFF_TYPE.EBT_TYPE_FROZON] = true,							    -- 冰冻
	[BUFF_TYPE.EBT_BLEED] = true,									-- 流血
}

-- 受到此buff需要播受击
function CommonSkillShowData.GetIsAttackHurtBuff(buff_type)
    return is_attack_buff_list[buff_type]
end