BoundlessJoyRewardView = BoundlessJoyRewardView or BaseClass(SafeBaseView)
function BoundlessJoyRewardView:__init()
	self:SetMaskBg(true, true)
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(1100, 590)})
	self:AddViewResource(0, "uis/view/boundless_joy_ui_prefab", "layout_boundless_joy_reward_view")
end

function BoundlessJoyRewardView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.BoundlessJoy.reward_title_view_name
    self.choose_reward_list = AsyncListView.New(ChooseRewardListRender, self.node_list["choose_reward_list"])
	self.choose_reward_list:SetStartZeroIndex(true)
end

function BoundlessJoyRewardView:ReleaseCallBack()
    if self.choose_reward_list then
		self.choose_reward_list:DeleteMe()
	    self.choose_reward_list = nil
	end
end

function BoundlessJoyRewardView:OnFlush()
	local reward_list_cfg = BoundlessJoyWGData.Instance:GetALLRewardPoolCfg()
    self.choose_reward_list:SetDataList(reward_list_cfg)
end


-------------------ChooseRewardListRender---------------
ChooseRewardListRender = ChooseRewardListRender or BaseClass(BaseRender)
function ChooseRewardListRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["save_custom_btn"], BindTool.Bind1(self.OnClickSaveRewardBtn, self))

    if not self.change_select_list then
		self.change_select_list = HappySelectRewardGrid.New()
		self.change_select_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["reward_cell_list"],
		assetBundle = "uis/view/boundless_joy_ui_prefab", assetName = "select_small_cell",  itemRender = SelectRewardCellRender})
	end

	self.change_select_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectCallBack, self))
	self.change_select_list:SetStartZeroIndex(false)
	self.change_select_list:SetIsMultiSelect(true)
end

function ChooseRewardListRender:__delete()
	if self.change_select_list then
        self.change_select_list:DeleteMe()
        self.change_select_list = nil
    end
end

function ChooseRewardListRender:OnSelectCallBack()
	self:FlushSelectNum()
end

--保存选中物品
function ChooseRewardListRender:OnClickSaveRewardBtn()
	if not self.data then
		return
	end

	local select_list = self.change_select_list:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		return
	end

	local select_index = 0
	local empty_tab = bit:d2b(0)
	for k, v in pairs(select_list) do
		select_index = v.select_index
		empty_tab[33 - select_index] = 1
	end

	local flag = bit:b2d(empty_tab)
	BoundlessJoyWGCtrl.Instance:SendHappyForeverReq(HAPPY_FOREVER_TYPE.HAPPY_FOREVER_OPERATE_TYPE_CHOOSE, self.data.seq, flag)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.BoundlessJoy.save_success)
end

function ChooseRewardListRender:OnFlush()
	if not self.data then
		return
	end

    local free_shop_cfg = BoundlessJoyWGData.Instance:GetCurFreeShopCfg()
	local buytimes = BoundlessJoyWGData.Instance:GetGiftBuyTimes(self.data.seq)
	local free_shop_flag = BoundlessJoyWGData.Instance:GetFreeShopFlag(self.data.seq)
	local shop_price = string.format(Language.BoundlessJoy.yuan, self.data.rmb_price)
	self.node_list.reward_price_text.text.text = shop_price

	local select_data_list = {}
	for i, v in ipairs(self.data.reward_item) do --0索引必得  不仅筛选
		local data = {}
		data.select_index = i
		data.item_id = v.item_id
		data.num = v.num
		data.is_bind = v.is_bind
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		data.sort = item_cfg and item_cfg.color * 100 + (99 - i) or i
		table.insert(select_data_list, data)
	end
	
	table.sort(select_data_list, SortTools.KeyUpperSorter("sort"))
	self.change_select_list:SetDataList(select_data_list)
	self.change_select_list:SetMeltingOneKeySelcet(self.data.seq)

	self.node_list.sell_out_img:SetActive(free_shop_flag == 1 and buytimes == free_shop_cfg.free_acquire_begin_times)
	self.node_list.save_custom_btn:SetActive(free_shop_flag == 0 or buytimes ~= free_shop_cfg.free_acquire_begin_times)

	self:FlushSelectNum()
end

function ChooseRewardListRender:FlushSelectNum()
	local other_cfg = BoundlessJoyWGData.Instance:GetCurFreeShopCfg()
	local need_select_count = other_cfg.need_select_count
	local select_list = self.change_select_list:GetAllSelectCell()
	local select_num = #select_list
	local color =  select_num < need_select_count and COLOR3B.RED or COLOR3B.GREEN
	local value_str = string.format(Language.BoundlessJoy.cost_num, ToColorStr(select_num, color), need_select_count)
	self.node_list.select_reward_text.text.text = value_str
	XUI.SetButtonEnabled(self.node_list["save_custom_btn"], select_num >= need_select_count)
end



SelectRewardCellRender = SelectRewardCellRender or BaseClass(BaseRender)
function SelectRewardCellRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_pos"])
		self.item_cell:SetIsShowTips(false)
	end

	self.node_list["select_click"].button:AddClickListener(BindTool.Bind(self.OnClickCell,self))
	self.event_listener = self.node_list["select_click"].event_trigger_listener
	if self.event_listener then
		self.event_listener:AddPointerDownListener(BindTool.Bind(self.OnClickPointDown, self))
		self.event_listener:AddPointerUpListener(BindTool.Bind(self.OnClickPointUp, self))
	end

	self.long_touch_flag = false
end

function SelectRewardCellRender:__delete()
	self:CancelClickLongTouchTimer()

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.long_touch_flag = nil
end

function SelectRewardCellRender:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData(self.data)
end

function SelectRewardCellRender:OnSelectChange(is_select)
	self.node_list["select_flag"]:SetActive(is_select)
end

function SelectRewardCellRender:OnClickCell()
	if not self.data then
		return
	end

	if self.long_touch_flag then 
		return
	end
	
	self:OnClick()
end

function SelectRewardCellRender:OnClickPointDown()
	if not self.data then
		return
	end

	self.long_touch_flag = false

	self.long_touch_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		self.long_touch_flag = true
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
		self:CancelClickLongTouchTimer()
	end, 1)
end

function SelectRewardCellRender:OnClickPointUp()
	self:CancelClickLongTouchTimer()
end

function SelectRewardCellRender:CancelClickLongTouchTimer()
	if self.long_touch_delay_timer then
		GlobalTimerQuest:CancelQuest(self.long_touch_delay_timer)
		self.long_touch_delay_timer = nil
	end
end
---------------------HappySelectRewardGrid-------------
HappySelectRewardGrid = HappySelectRewardGrid or BaseClass(AsyncBaseGrid)
function HappySelectRewardGrid:IsSelectMultiNumLimit(cell_index)
	local other_cfg = BoundlessJoyWGData.Instance:GetCurFreeShopCfg()
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= other_cfg.need_select_count then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.BoundlessJoy.limit_select_num)
            return true
        end
    end

    return false
end

function HappySelectRewardGrid:SetMeltingOneKeySelcet(seq)
	self.select_tab[1] = {}
	self.cur_multi_select_num = 0

	local choose_reward_list = BoundlessJoyWGData.Instance:GetChooseRewardPool(seq)
	local rewarred_index_list = {}
	for k, v in pairs(choose_reward_list) do
		rewarred_index_list[v.reward_index] = true
	end

	for k, v in pairs(self.cell_data_list) do
		if rewarred_index_list[v.select_index] then
			self.select_tab[1][k] = true
			self.cur_multi_select_num = self.cur_multi_select_num + 1
		end
	end

	self:__DoRefreshSelectState()
end