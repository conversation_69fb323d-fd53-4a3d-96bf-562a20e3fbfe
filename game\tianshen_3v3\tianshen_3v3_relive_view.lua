TianShen3v3ReliveView = TianShen3v3ReliveView or BaseClass(SafeBaseView)
-- 天神3v3复活面板
function TianShen3v3ReliveView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(false, false)
	self.is_modal = true
	self.is_any_click_close = false
	self.active_close = false
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_tianshen_3v3_relive")
end

function TianShen3v3ReliveView:__delete()
end

function TianShen3v3ReliveView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("tianshen_3v3_fuhuo") then
		CountDownManager.Instance:RemoveCountDown("tianshen_3v3_fuhuo")
    end
    self:KillTweener()
end

function TianShen3v3ReliveView:OpenCallBack()
    self:KillTweener()
end

function TianShen3v3ReliveView:CloseCallBack()
	CountDownManager.Instance:RemoveCountDown("tianshen_3v3_fuhuo")
    self:KillTweener()
end

function TianShen3v3ReliveView:LoadCallBack()
end

function TianShen3v3ReliveView:ShowIndexCallBack()
end

function TianShen3v3ReliveView:OnFlush(param_t)
	self:FlushCountdown()
	self.node_list["killer_name"].text.text = string.format(Language.TianShen3v3.KillerName, TianShen3v3WGData.Instance:GetKillerName())
end

function TianShen3v3ReliveView:FlushCountdown()
    local relive_time = TianShen3v3WGData.Instance:GetReliveTimestamp() - TimeWGCtrl.Instance:GetServerTime()
    if relive_time <= 0 then
        self:Close()
    	return
    end
    if self.fill_tween then
        return
    end

    self:KillTweener()
    self.node_list.yuanquan.image.fillAmount = 1
    self.node_list.tuowei_img.image.fillAmount = 1
    self.node_list.tuowei_img.rect.localRotation = Quaternion.Euler(0, 0, 225)
    self.fill_tween = self.node_list.yuanquan.image:DOFillAmount(0, relive_time):OnUpdate(function()
        local value = self.node_list.yuanquan.image.fillAmount
        local rotate = - 360 * value + 225
        if rotate > 135 then
            self.node_list.tuowei_img.image.fillAmount = value * 4
        end
        self.node_list.tuowei_img.rect.localRotation = Quaternion.Euler(0, 0, - 360 * value + 225)
    end):OnComplete(function()
        self.fill_tween = nil
        self:Close()
    end)
    
	self.node_list["daojishi_text"].text.text = math.floor(relive_time)
	CountDownManager.Instance:RemoveCountDown("tianshen_3v3_fuhuo")
	CountDownManager.Instance:AddCountDown("tianshen_3v3_fuhuo", BindTool.Bind1(self.UpdateCountDownTime, self), BindTool.Bind(self.Close, self), nil, relive_time, 0.5)
end

function TianShen3v3ReliveView:KillTweener()
    if self.fill_tween then
        self.fill_tween:Kill()
        self.fill_tween = nil
    end
end

-- 倒计时每次循环执行的函数
function TianShen3v3ReliveView:UpdateCountDownTime(elapse_time, total_time)
	if not self.node_list["daojishi_text"] then
		return
    end
    local last_time = math.floor(total_time - elapse_time)  
	self.node_list["daojishi_text"].text.text = last_time
end

function TianShen3v3ReliveView:SetKillerName()

end

function TianShen3v3ReliveView:GetUseFuHuoType()
    return FuHuoType.Common
end