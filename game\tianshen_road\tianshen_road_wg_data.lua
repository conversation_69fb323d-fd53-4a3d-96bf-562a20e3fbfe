TianshenRoadWGData = TianshenRoadWGData or BaseClass()

SPECIAL_DROP_ITEM_COUNT = 5
GET_FALL_ITEM_NUM_MAX = 15
GET_PARTIPATE_REWARD_NUM_MAX = 15

function TianshenRoadWGData:__init()
	if TianshenRoadWGData.Instance then
		ErrorLog("[TianshenRoadWGData] Attemp to create a singleton twice !")
	end
	TianshenRoadWGData.Instance = self
	-- RemindManager.Instance:Register(RemindName.XiuZhenRoad, BindTool.Bind(self.IsShowXiuZhenRoadRedPoint, self))
	
	self.top_item = nil
	self.theme_cfg_list = {}
	self:DuoBeiSceneConfig()

	RemindManager.Instance:Register(RemindName.TianShenRoad, BindTool.Bind(self.IsShowMainTianShenRoadRedPoint, self))
	RemindManager.Instance:Register(RemindName.TianShenRoad_Login, BindTool.Bind(self.IsShowLoginRedPoint, self))
	RemindManager.Instance:Register(RemindName.TianShenRoad_FirstRecharge, BindTool.Bind(self.IsShowShouChongRedPoint, self))
	RemindManager.Instance:Register(RemindName.TianShenRoad_TotalReCharge, BindTool.Bind(self.IsShowLeiChongRedPoint, self))
	RemindManager.Instance:Register(RemindName.TianShenRoad_ShenQi, BindTool.Bind(self.IsShowShenQiRedPoint, self)) 
	RemindManager.Instance:Register(RemindName.TianShenRoad_ChongBang, BindTool.Bind(self.IsShowChongBangRedPoint, self))
	RemindManager.Instance:Register(RemindName.TianShenRoad_JiangLin, BindTool.Bind(self.IsShowJianLinRedPoint, self))

	local cfg =  ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto")
	self.act_model_cfg = ListToMap(cfg.activity_model, "activity_id")
end

function TianshenRoadWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.TianShenRoad_Login)
	RemindManager.Instance:UnRegister(RemindName.TianShenRoad_FirstRecharge)
	RemindManager.Instance:UnRegister(RemindName.TianShenRoad_TotalReCharge)
	RemindManager.Instance:UnRegister(RemindName.TianShenRoad_ShenQi)
	RemindManager.Instance:UnRegister(RemindName.TianShenRoad)
	RemindManager.Instance:UnRegister(RemindName.TianShenRoad_ChongBang)
	RemindManager.Instance:UnRegister(RemindName.TianShenRoad_JiangLin)

	TianshenRoadWGData.Instance = nil
end

function TianshenRoadWGData:GetLoginRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").new_login_reward
end

function TianshenRoadWGData:GetActivityThemeCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").tianshen_theme_desc
end

function TianshenRoadWGData:GetActivityThemeOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").tianshen_theme_other
end

function TianshenRoadWGData:GetOtherCfg(key)
	local cfg_list = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").other
	if cfg_list and cfg_list[1] then
		return cfg_list[1][key]
	end
end

function TianshenRoadWGData:SetThemeCfgByTabIndex(tab_index, cfg)
	self.theme_cfg_list[tab_index] = cfg
end

function TianshenRoadWGData:GetThemeCfgByTabIndex(tab_index)
	return self.theme_cfg_list[tab_index]
end

function TianshenRoadWGData:GetActivityName(real_id)
	local cfg = self:GetActivityThemeCfg()
	for k,v in pairs(cfg) do
		if v.real_id == real_id then
			return v.active_name
		end
	end
	return ""
end

------------------------------------  登录奖励 -------------------------------------------
-- 获取登录奖励配置
function TianshenRoadWGData:GetLoginRewardCfgByDay(day)
	local cfg = self:GetLoginRewardCfg()
	for k,v in pairs(cfg) do
		if day == v.day_index then
			return v
		end
	end
end

--获取活动提示
function TianshenRoadWGData:GetActivityTip(index)
	local cfg = self:GetThemeCfgByTabIndex(index)
	if cfg ~= nil then
		return cfg.tab_name, cfg.rule_desc
	end
end

function TianshenRoadWGData:SetLoginRewardInfo(protocol)
	self.LoginDayData = {}
	self.LoginDayData.activity_day_index = protocol.activity_day_index

	local day_list = {}
	local cfg = self:GetLoginRewardCfg()
	for k,v in pairs(cfg) do
		local data = {}
		data.index = v.day_index
		data.vip_lv = v.vip_lv
		data.common_gift_state = protocol.reward_state[v.day_index].common_gift_state 	--是否领取
		data.special_gift_state = protocol.reward_state[v.day_index].special_gift_state
		day_list[v.day_index] = data
	end
	self.LoginDayData.day_list = day_list

	RemindManager.Instance:Fire(RemindName.TianShenRoad_Login)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
end

-- vip等级变化后端不推的
function TianshenRoadWGData:FlushLoginRewardInfo()
	if self.LoginDayData then
		local vip_level = VipWGData.Instance:GetRoleVipLevel()
		local cur_day = self.LoginDayData.activity_day_index or 0
		local day_list = self.LoginDayData.day_list or {}
		for i=1,#day_list do
			if day_list[i].special_gift_state == 0 and cur_day >= day_list[i].index and
				day_list[i].vip_lv > 0 and vip_level >= day_list[i].vip_lv then
				day_list[i].special_gift_state = 1
			end
		end
		self.LoginDayData.day_list = day_list
	end
end

function TianshenRoadWGData:GetLoginRewardInfo(day_index)
	if self.LoginDayData then
		return self.LoginDayData.day_list[day_index]
	end
end

function TianshenRoadWGData:GetLoginJumpIndex()
	local index = 0
	if self.LoginDayData then
		for k, v in pairs(self.LoginDayData.day_list) do
			if v.common_gift_state == TianShenRoadRewardState.KLQ then
				return k
			end

			if v.common_gift_state == TianShenRoadRewardState.BKL and index <= 0 then
				index = k
			end
		end
	end

	return index > 0 and index or 1
end

function TianshenRoadWGData:GetLoginDayData()
	return self.LoginDayData
end

function TianshenRoadWGData:GetLoginDyaIndex()
	if self.LoginDayData == nil or self.LoginDayData.activity_day_index == nil then
		return 0
	end  
	return self.LoginDayData.activity_day_index
end

function TianshenRoadWGData:GetCurSelectDay() 
	local day_index = self:GetFirstHasRewardDayIndex()
	if day_index == 0 and self.LoginDayData then
		if self:GetLoginRewardCfgByDay(self.LoginDayData.activity_day_index + 1)  then
			day_index = self.LoginDayData.activity_day_index + 1
		else
			day_index = self.LoginDayData.activity_day_index
		end
	end
	return day_index
end

function TianshenRoadWGData:SetSelectDayItem(item, select_bg)
	self.cur_day_item_select = item
	self.cur_day_item_select_bg = select_bg
end

function TianshenRoadWGData:GetDyaItemSelect()
	return self.cur_day_item_select, self.cur_day_item_select_bg
end

-------------------------------充值---------------------------------
function TianshenRoadWGData:GetShouChongRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").shouchong_reward
end

function TianshenRoadWGData:GetLeiChongRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").leichonghaoli_reward
end

function TianshenRoadWGData:SetShouChongInfo(protocol)
	if self.ShouChongData == nil then
		self.ShouChongData = {}
	end
	self.ShouChongData.gift_state = protocol.gift_state -- 首充奖励领取状态
	RemindManager.Instance:Fire(RemindName.TianShenRoad_FirstRecharge)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
end

function TianshenRoadWGData:SetLeiChongInfo(protocol)
	local temp_data = {}
	temp_data.cur_xianyu = protocol.cur_xianyu
	temp_data.cur_dangwei = protocol.cur_dangwei
	temp_data.dangwei_num = protocol.dangwei_num
	temp_data.dangwei_info = protocol.dangwei_info
	self.LeiChongData = temp_data

	RemindManager.Instance:Fire(RemindName.TianShenRoad_TotalReCharge)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
end

--获取累充奖励排行数据
function TianshenRoadWGData:GetLeiChongRewardList()
	local list = {}
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		local data = {}
		data.cfg = v
		data.ID = v.ID
		data.receive_state = self:GetLeiChongReceiveState(v.ID)
		if data.receive_state == TianShenRoadRewardState.KLQ then
			data.sort = 0
		elseif data.receive_state == TianShenRoadRewardState.BKL then
			data.sort = 1
		else
			data.sort = 2
		end
		table.insert(list, data)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort", "ID"))
	return list
end

function TianshenRoadWGData:GetLeiChongProgress(leichong_value)  --亲密度进度条
	local cur_progress = 0
	local cfg = self:GetLeiChongRewardCfg()
	local cur_leichong_value = tonumber(leichong_value)
	if next(cfg) == nil or leichong_value == nil then
		return cur_progress
	end
	local progress_list = {0.1, 0.3, 0.52, 0.74, 1}			--对应的进度条值

	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list
		local cur_need = cfg[seq] and cfg[seq].stage_value or 0
		local next_need = cfg[seq + 1] and cfg[seq + 1].stage_value or cfg[#cfg].stage_value
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		if leichong_value > cur_need and leichong_value <= next_need then
			cur_progress = (leichong_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif leichong_value > cfg[#cfg].stage_value then
			cur_progress = progress_list[length]
			break
		end
	end
	return cur_progress
end
-- 获取每日首充奖励配置
function TianshenRoadWGData:GetShouChongRewardCfgByDay(day)
	local cfg = self:GetShouChongRewardCfg()
	for k,v in pairs(cfg) do
		if day == v.day_index then
			return v
		end
	end
	return nil
end

--首充领取状态
function TianshenRoadWGData:GetShouChongGiftState()
	if self.ShouChongData ~= nil then
		return self.ShouChongData.gift_state
	end
	return 0
end

--当前充值仙玉
function TianshenRoadWGData:GetOwnXianYu()
	if self.LeiChongData ~= nil then
		return self.LeiChongData.cur_xianyu
	end
	return 0
end

--获取当前档需要充值的仙玉
function TianshenRoadWGData:GetNeedRechargeXianYU()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		if cur_xianyu < v.stage_value then
			local pre_stage_value = 0
			if i > 1 then
				pre_stage_value = cfg[i-1].stage_value
			end
			return v.stage_value - cur_xianyu, v.stage_value, pre_stage_value
		end
	end
	return 0, cfg[#cfg].stage_value, cfg[#cfg].stage_value
end

--是否已达成
function TianshenRoadWGData:IsRechargeTargetFinish()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	return cur_xianyu >= cfg[#cfg].stage_value
end

--获取领取状态
function TianshenRoadWGData:GetLeiChongReceiveState(index)
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil and self.LeiChongData.dangwei_info[index] ~= nil then
		return self.LeiChongData.dangwei_info[index]
	else
		return 0
	end
end

-------------------------------------- 天神降临 --------------------------------------
function TianshenRoadWGData:GetJiangLinRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("randact_tianshenjianglin_cfg_auto").reward_config
end

function TianshenRoadWGData:GetJiangLinBossDropCfg()
	return ConfigManager.Instance:GetAutoConfig("randact_tianshenjianglin_cfg_auto").boss_drop_cfg
end

function TianshenRoadWGData:GetJiangLinFlushTimeCfg()
	return ConfigManager.Instance:GetAutoConfig("randact_tianshenjianglin_cfg_auto").refresh_time
end

function TianshenRoadWGData:GetJiangLinBossCfg()
	return ConfigManager.Instance:GetAutoConfig("randact_tianshenjianglin_cfg_auto").boss_cfg
end

function TianshenRoadWGData:GetJiangLinOtherCfg(key)
	if key then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("randact_tianshenjianglin_cfg_auto").other
		other_cfg = other_cfg and other_cfg[1]
		return other_cfg and other_cfg[key]
	end
	return ConfigManager.Instance:GetAutoConfig("randact_tianshenjianglin_cfg_auto").other
end

function TianshenRoadWGData:SetTSJLInfo(protocol)
	if self.JiangLinData == nil then
		self.JiangLinData = {}
	end
	self.JiangLinData.world_level = protocol.world_lv
	self.JiangLinData.pass_times = protocol.pass_times
end

function TianshenRoadWGData:SetTSJLFinishInfo(protocol)
	local finish_info = {}
	local reward_list = {}
	local partipate_list = protocol.partipate_list
	for i=1,#partipate_list do
		if partipate_list[i].item_id > 0 then
			reward_list[#reward_list + 1] = partipate_list[i]
		end
	end

	finish_info.partipate_list = reward_list
	finish_info.fall_item_list = protocol.fall_item_list

	self.tsjl_finish_info = finish_info
end

function TianshenRoadWGData:GetTSJLFinishInfo()
	return self.tsjl_finish_info
end

function TianshenRoadWGData:TianShenJianLinActivityState(state, next_time)
	if self.JiangLinData == nil then
		self.JiangLinData = {}
	end
	self.JiangLinData.activity_state = state
	self.JiangLinData.next_time = next_time
	
	if self:IsInTSJLActivity() then
		ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.TIANSHENJIANLIN)
	end
	RemindManager.Instance:Fire(RemindName.TianShenRoad_JiangLin)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
end

function TianshenRoadWGData:GetTianShenJianLinInfo()
	return self.JiangLinData
end

--判断是否在活动开启时间内
function TianshenRoadWGData:IsInTSJLActivity()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.TIANSHENJIANLIN)
	if act_cfg then
		local role_level = RoleWGData.Instance:GetAttr("level")
		if role_level < act_cfg.level or role_level > act_cfg.level_max then
			return false
		end
	end

	if self.JiangLinData and self.JiangLinData.activity_state then
		return self.JiangLinData.activity_state == ACTIVITY_STATUS.OPEN
	end
	return false
end

function TianshenRoadWGData:GetTSJLActivityIsClose()
	if self.JiangLinData ~= nil and self.JiangLinData.activity_state ~= nil then
		return self.JiangLinData.activity_state == ACTIVITY_STATUS.CLOSE
	end
	return false
end

function TianshenRoadWGData:GetJiangLinReward()
	local world_level = RankWGData.Instance:GetWordLevel() or 0
	local reward_cfg = self:GetJiangLinRewardCfg()
	for _,v in pairs(reward_cfg) do
		if world_level >= v.min_lv and world_level <= v.max_lv then
			return v
		end
	end
end

--获得当前bossid
function TianshenRoadWGData:GetBossId()
	local cfg_list = self:GetJiangLinBossCfg()
	if cfg_list then
		local world_level = RankWGData.Instance:GetWordLevel() or 0
		local boss_id = cfg_list[1].boss_id
		local boss_cfg = cfg_list[1]
		for i=1,#cfg_list do
			if world_level > cfg_list[i].limit_lv then
				boss_id = cfg_list[i].boss_id
				boss_cfg = cfg_list[i]
			else
				break
			end
		end
		return boss_id, boss_cfg
	end
end

function TianshenRoadWGData:GetTSJLOpenTime()
	if self.time_pramstr == nil then
		self.time_pramstr = {}
		local cfg = self:GetJiangLinFlushTimeCfg()
		local index = 1
		if cfg ~= nil then
			for k,v in pairs(cfg) do
				local date = {}
				local arr = {}
				for i=1,4 do
					arr[i] = tonumber(string.sub(v.refresh_time,i,i))
				end
				date.hour = arr[1] * 10 + arr[2]
				date.min = arr[3] * 10 + arr[4]
				table.insert(self.time_pramstr, date)
			end
		end
	end

	return self.time_pramstr
end

------------------------------------- 拼图领奖(我要神器) --------------------------------------------
function TianshenRoadWGData:GetSQTaskCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").woyaoshenqi_task
end

function TianshenRoadWGData:GetSQGiftCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").woyaoshenqi_gift
end

function TianshenRoadWGData:GetSQRewardCfg()
	if self.sq_reward_cfg == nil then
		self.sq_reward_cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").woyaoshenqi_reward
	end
	return self.sq_reward_cfg
end

function TianshenRoadWGData:GetSQRewardCfgByID(id)
	if self.sq_reward_cfg_map == nil then
		self.sq_reward_cfg_map = ListToMap(self:GetSQRewardCfg(), "ID")
	end
	return self.sq_reward_cfg_map[id] and self.sq_reward_cfg_map[id].reward_item or {}
end

function TianshenRoadWGData:GetSQRewardList()
	if not IsEmptyTable(self.show_award_list) then
		return self.show_award_list
	end

	local all_awards = self:GetSQRewardCfg()
	self.show_award_list = {}
	for index, value in ipairs(all_awards) do
		local info = {}
		info.title_text = string.format(Language.TianShenRoad.AwardTip, value.jigsaw_num)
		info.reward_item_list = {}
		for index = 0, #value.reward_item do
			if value.reward_item[index] then
				table.insert(info.reward_item_list, value.reward_item[index])
			end
		end
		self.show_award_list[index] = info
	end
	return self.show_award_list
end

function TianshenRoadWGData:GetSQComposeCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").woyaoshenqi_shenqi
end

function TianshenRoadWGData:GetSQInfo()
	return self.SQData
end

function TianshenRoadWGData:GetSQTaskList()
	local list = {}
	local cfg = self:GetSQTaskCfg()
	for i,v in ipairs(cfg) do
		local item = {}
		item.ID = v.ID
		item.cfg = v
		item.receive_state = self:GetSQTaskState(v.ID)
		if item.receive_state == TianShenRoadRewardState.KLQ then
			item.sort = 0
		elseif item.receive_state == TianShenRoadRewardState.BKL then
			item.sort = 1
		else
			item.sort = 2
		end
		table.insert(list, item)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort","ID"))
	return list
end

function TianshenRoadWGData:GetSQTaskState(task_id)
	if self.SQData ~= nil then
		for k,v in pairs(self.SQData.task_list) do
			if v.task_id == task_id then
				return v.gift_state
			end
		end
	end
	return 0
end

function TianshenRoadWGData:SetSQInfo(protocol)
	if self.SQData == nil then
		self.SQData = {}
		self.sq_delay_flush = "sq_delay_flush"
	 	CountDownManager.Instance:AddCountDown(self.sq_delay_flush, nil, function()
	 		RemindManager.Instance:Fire(RemindName.TianShenRoad_ShenQi)
			RemindManager.Instance:Fire(RemindName.TianShenRoad)
	 		CountDownManager.Instance:RemoveCountDown(self.sq_delay_flush)
	 	end, TimeWGCtrl.Instance:GetServerTime() + 3, nil, 3)
	end
	
	self.SQData.buy_libao_cnt = protocol.buy_libao_cnt
	self.SQData.shenqi_frag_num = protocol.shenqi_frag_num
	self.SQData.shenqi_hecheng_state = protocol.shenqi_hecheng_state
	self.SQData.shengqi_total_cap = protocol.shengqi_total_cap
	
	self.SQData.dangwei_state = protocol.dangwei_state
	self.SQData.task_list = protocol.task_list
	self.SQData.jigsaw_list = protocol.jigsaw_list
	RemindManager.Instance:Fire(RemindName.TianShenRoad_ShenQi)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
end

function TianshenRoadWGData:GetShenQiComposeState()
	if self.SQData ~= nil then
		return self.SQData.shenqi_hecheng_state  == TianShenRoadRewardState.YLQ
	end
	return false
end

function TianshenRoadWGData:GetTianshenTotalCap()
	if self.SQData ~= nil and self.SQData.shengqi_total_cap ~= nil then
		return self.SQData.shengqi_total_cap
	end
	return 0
end

function TianshenRoadWGData:GetSQGiftIndex()
	if self.SQData ~= nil and self.SQData.buy_libao_cnt ~= nil then
		return self.SQData.buy_libao_cnt
	end
	return 0
end

--获取当前可以购买的礼包，全部买完展示最后一个
function TianshenRoadWGData:GetCanBuyGiftData()
	local cfg = self:GetSQGiftCfg()
	if self.SQData and self.SQData.buy_libao_cnt < #cfg then
		return cfg[self.SQData.buy_libao_cnt + 1], false
	else
		return cfg[#cfg], true
	end
end

--获取神器配置
function TianshenRoadWGData:GetModelCfg(model_index)
	local model_cfg = ConfigManager.Instance:GetAutoConfig("panel_show_model_auto").show_model
	if model_cfg ~= nil then
		for k,v in pairs(model_cfg) do
			if v.id == model_index then
				return v
			end
		end
	end
	return nil
end

function TianshenRoadWGData:GetDangweiScore()
	local list = self:GetJigsawList()
	local num = 0
	for _, value in pairs(list) do
		if value.id == value.index then
			num = num + 1
		end
	end
	
	return num
end

function TianshenRoadWGData:GetShenQiTaskParamNum(index)
	if self.SQData ~= nil and self.SQData.task_list ~= nil and self.SQData.task_list[index] ~= nil then
		return self.SQData.task_list[index].task_param
	end
	return 0
end

--获取最小未领取的阶段奖励
function TianshenRoadWGData:GetMinBXReward()
	if self.SQData ~= nil and self.SQData.dangwei_state ~= nil then
		local reward_cfg = TianshenRoadWGData:GetSQRewardCfg()
		for i=1,#reward_cfg do
			if self.SQData.dangwei_state[i] ~= nil and self.SQData.dangwei_state[i] ~=2 then
				return i
			end
		end
	end
	return 0
end

function TianshenRoadWGData:IsLastBX(id)
	local reward_cfg = self:GetSQRewardCfg()
	if reward_cfg ~= nil then
		return id == #reward_cfg
	end
	return false
end

function TianshenRoadWGData:GetBXState(id)
	if self.SQData ~= nil and self.SQData.dangwei_state ~= nil then
		return self.SQData.dangwei_state[id]
	end
	return 0
end

function TianshenRoadWGData:GetBXTargetScore(id)
	local reward_cfg = TianshenRoadWGData:GetSQRewardCfg()
	if reward_cfg ~= nil and reward_cfg[id] ~= nil then
		return reward_cfg[id].jigsaw_num
	end
	return 0
end

function TianshenRoadWGData:IsNeedScroll(box_index)
	local reward_cfg = self:GetSQRewardCfg()
	if reward_cfg ~= nil then
		return (#reward_cfg - box_index) >= 5
	end
	return false
end

-- key:位置，value:编号
function TianshenRoadWGData:GetJigsawList()
	if self.SQData ~= nil and self.SQData.jigsaw_list ~= nil then
		return self.SQData.jigsaw_list
	end
	return {}
end

function TianshenRoadWGData:IsRightJigsaw(index)
	local list = self:GetJigsawList()
	local info = list[index]
	return info and info.index == info.id
end

function TianshenRoadWGData:CanDrag(index)
	local list = self:GetJigsawList()
	local info = list[index]
	return info and info.id ~= -1 and info.index ~= info.id
end
-------------------------------魔王有礼----------------------------------
function TianshenRoadWGData:GetMWTaskCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").mowangyouli_task
end

function TianshenRoadWGData:SetMoWangYouLiInfo(protocol)
	self.MoWangYouLiDropCountList = protocol.drop_count_list
end

function TianshenRoadWGData:GetMoWangYouLiDropCountList()
	local drop_count_list = self.MoWangYouLiDropCountList or {}
	local data_list = self.MoWangYouLiDropCountDataList
	
	if not data_list then
		data_list = {}
		local mw_drop_item_str = self:GetOtherCfg("mw_drop_item")
		local max_drop_count_str = self:GetOtherCfg("mw_drop_item_limilt")
		if mw_drop_item_str and max_drop_count_str then
			local drop_item_list = Split(mw_drop_item_str, "|")
			local max_drop_count_list = Split(max_drop_count_str, "|")
			for i=1,#drop_item_list do
				data_list[i] = {item_id = tonumber(drop_item_list[i]), max_count = max_drop_count_list[i] or 0, now_count = 0}
			end
		end
		self.MoWangYouLiDropCountDataList = data_list
	end

	for i=1,#data_list do
		data_list[i].now_count = drop_count_list[i] or 0
	end
	
	return data_list
end

function TianshenRoadWGData:GetMWPreViewReward()
	local cfg = self:GetActivityThemeOtherCfg()
	if cfg and cfg[1] and cfg[1].mw_reward_item then
		return SortTableKey(cfg[1].mw_reward_item)
	end
end

function TianshenRoadWGData:GetMWTaskList()
	local cfg = self:GetMWTaskCfg()
	return cfg
end

function TianshenRoadWGData:GetMWTaskItemForID(id)
	local cfg = self:GetMWTaskCfg()
	if cfg ~= nil then
		return cfg[id]
	end
	return nil
end

-------------------------- 天神冲榜 ----------------------------------
function TianshenRoadWGData:GetCBRankRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").tianshencongbang_rank_cond
end

function TianshenRoadWGData:GetCBCapRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").tianshencongbang_reward
end

function TianshenRoadWGData:GetCBTiShengCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").tianshencongbang_tishen
end

function TianshenRoadWGData:SetChongBangInfo(protocol)
	if self.CBData == nil then
		self.CBData = {}
	end

	self.CBData.rank = protocol.rank
	self.CBData.tianshen_zhanli = protocol.tianshen_zhanli
	self.CBData.zhanli_rewaerd_status = protocol.zhanli_rewaerd_status

	RemindManager.Instance:Fire(RemindName.TianShenRoad_ChongBang)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
	GlobalEventSystem:Fire(CHONGBANG_TIP_EVENT.ACT_CHECK,ChongBangTipWGData.ACT_TIP_TYPE.ACT,ACTIVITY_TYPE.GOD_CHONGBANG)
end

function TianshenRoadWGData:CreatNoRankItemData(nSectionIndex, reach_time)
	reach_time = reach_time or 0
	return {rank_id = nSectionIndex, reach_time = reach_time, zhanli = 0}
end

function TianshenRoadWGData:ExpandRankData(rank_list, nNextIndex, nCurRank, index)
	local next_data = rank_list[nNextIndex]
	if next_data then
		local nNextRank = next_data.rank_id
		if nCurRank + 1 ~= nNextRank then -- 说明断了 要补一下
			for nSectionIndex = nCurRank + 1, nNextRank - 1 do
				local data = {}
				data.no_true_rank = true
				data.index = index
				data.rank_data = self:CreatNoRankItemData(nSectionIndex)
				table.insert(self.CBRankata.rank_list, data)
				index = index + 1
			end
		end
	end

	return index
end

function TianshenRoadWGData:SetChongBangRankInfo(protocol)
	self.CBRankata = {}
	self.CBRankata.rank_list = {}

	self.top_item = protocol.top_item		--第一名的角色数据.

	local index = 1
	local nPreRank = 1
	local cfg = self:GetCBRankRewardCfg()

	for i=1,#protocol.rank_list do
		if i == 1 then
			if protocol.rank_list[i].rank_id ~= 1 then -- 没有第一名
				local item = {}
				item.rank_data = self:CreatNoRankItemData(1)
				item.index = index
				item.no_true_rank = true
				index = index + 1
				table.insert(self.CBRankata.rank_list, item)
				index = self:ExpandRankData(protocol.rank_list, 1, 1, index)
			end
		end

		local item = {}
		item.index = index
		item.rank_data = protocol.rank_list[i]
		local nCurRank = item.rank_data.rank_id
		table.insert(self.CBRankata.rank_list, item)
		index = index + 1

		index = self:ExpandRankData(protocol.rank_list, i + 1, nCurRank, index)
	end

	local rank_cfg = cfg[#cfg]
	local nMaxRank = rank_cfg.rank
	if index - 1 < nMaxRank then
		for nSectionIndex = index, nMaxRank do
			local data = {}
			data.no_true_rank = true
			data.index = nSectionIndex
			data.rank_data = self:CreatNoRankItemData(nSectionIndex)
			table.insert(self.CBRankata.rank_list, data)
		end
	end
end

function TianshenRoadWGData:GetMyRankAndCap()
	if self.CBData ~= nil then
		return self.CBData.rank, self.CBData.tianshen_zhanli
	end
	return 0,0
end

--获取第一名角色信息.
function TianshenRoadWGData:GetTopRoleItemInfo()
	return self.top_item
end

--获取第一名排名奖励
function TianshenRoadWGData:GetCBFirstReward()
	local cfg = self:GetCBRankRewardCfg()
	if cfg and cfg[1] then
		return SortTableKey(cfg[1].reward_item), cfg[1].min_zhanli
	end
end

--获取奖励数据（战力和排名, 剔除第一名排名奖励）
function TianshenRoadWGData:GetCBReward()
	local rank_cfg = self:GetCBRankRewardCfg()
	local cap_cfg = self:GetCBCapRewardCfg()
	local list = {}

	if self.CBData then
		for k,v in ipairs(cap_cfg) do
			local item = {}
			item.data = v
			item.state = self.CBData.zhanli_rewaerd_status[k]
			item.index = v.ID
			if self.CBData.zhanli_rewaerd_status[k] == TianShenRoadRewardState.KLQ then
				item.sort = 0
			elseif self.CBData.zhanli_rewaerd_status[k] == TianShenRoadRewardState.BKL then
				item.sort = 2
			else
				item.sort = 3
			end
			table.insert(list, item)
		end
	end

	if rank_cfg ~= nil then
		for k,v in ipairs(rank_cfg) do
			-- if k ~= 1 then
			-- 	local item = {}
			-- 	item.data = v
			-- 	item.is_rank = true
			-- 	item.sort = 1
			-- 	item.index = v.rank
			-- 	table.insert(list, item)
			-- end
			local item = {}
			item.data = v
			item.is_rank = true
			item.sort = 1
			item.index = v.rank
			table.insert(list, item)
		end
	end
	table.sort(list, SortTools.KeyLowerSorter("sort", "index"))
	return list
end

--获取排行榜数据
function TianshenRoadWGData:GetTSRankInfo()
	if self.CBRankata ~= nil and self.CBRankata.rank_list ~= nil then
		return self.CBRankata.rank_list	
	end
	return nil
end

---------------------------------多倍 -------------------------------------

function TianshenRoadWGData:GetDuoBeiCfg()
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").duobeijiangli
end

function TianshenRoadWGData:SetDuoBeiInfo(protocol)
	self.duobei_data = protocol
	MainuiWGCtrl.Instance:FlushDuoBei()
end

function TianshenRoadWGData:GetDuoBeiInfo()
	if self.duobei_data then
		local cfg = self:GetDuoBeiCfg()
		local list = {}
		for k,v in pairs(cfg) do
			if v.day == self.duobei_data.day then
				local info = {}
				info.cfg = v
				info.cur_finish_num = 0
				if self.duobei_data.task_info[v.task_type] then
					info.cur_finish_num = self.duobei_data.task_info[v.task_type].cur_finish_num
				end
				table.insert(list, info)
			end
		end
		return list
	end
end

function TianshenRoadWGData:GetDuoBeiTimes(task_type)
	if self.duobei_data ~= nil and self:GetActivityState(TabIndex.tianshenroad_duobei) then
		local cfg = self:GetDuoBeiCfg()
		if cfg ~= nil then
			for k,v in pairs(cfg) do
				if v.day == self.duobei_data.day and v.task_type == task_type then
					if FunOpen.Instance:GetFunIsOpened(v.module_name) then
						return v.reward_mult
					end
				end
			end
		end
	end
	return 0
end

function TianshenRoadWGData:GetItemDuoBeiTimes(task_type , item_id)
	if self.duobei_data ~= nil and self:GetActivityState(TabIndex.tianshenroad_duobei) then
		local cfg = self:GetDuoBeiCfg()
		if cfg ~= nil then
			for k,v in pairs(cfg) do
				if v.day == self.duobei_data.day and v.task_type == task_type then
					for k2,v2 in pairs(v.reward_item) do
						if v2.item_id == item_id then
							return v.reward_mult
						end
					end
				end
			end
		end
	end
	return 0
end

function TianshenRoadWGData:GetDuoBeiRewardListByTaskType(task_type)
	if task_type == DUOBEI_LAIXI_TASK_TYPE.HLJ then
		local index = TeamEquipFbWGData.Instance:GetCurFbIndex()
		local reward_list = TeamEquipFbWGData.Instance:GetRewardList(index - 1)
		return reward_list or {}
	elseif task_type == DUOBEI_LAIXI_TASK_TYPE.CQB then
		local cur_layer = FuBenPanelWGData.Instance:GetPetCurLayer()
		local reward_list = FuBenPanelWGData.Instance:GetPetDropList(cur_layer)
		for i=1,#reward_list do
			reward_list[i].show_duobei = false
		end
		return reward_list
	elseif task_type == DUOBEI_LAIXI_TASK_TYPE.JBG then
		local reward_list = CopperFbWGData.Instance:GetRewardItemList(3)
		for i=1,#reward_list do
			reward_list[i].num = 0
			reward_list[i].show_duobei = false
		end
		return reward_list
	elseif task_type == DUOBEI_LAIXI_TASK_TYPE.TTT then
		local moren_layer = BaGuaMiZhenWGData.Instance:GetLayerBuyLevelAndZhanLi()
		local reward_list = BaGuaMiZhenWGData.Instance:GetBaGuaZhenRewardList(moren_layer)
		return reward_list
	elseif task_type == DUOBEI_LAIXI_TASK_TYPE.QYFB then
		local qingyuanfb_reward = MarryWGData.Instance:GetQingYuanFbViewReward()
		local reward_list = {}
		for i=1,#qingyuanfb_reward do
			reward_list[i] = {item_id = qingyuanfb_reward[i].item_id}
		end
		return reward_list
	elseif task_type == DUOBEI_LAIXI_TASK_TYPE.TDL then
		local reward_list = BootyBayWGData.Instance:GetRewaredListData()
		return reward_list
	end
end

function TianshenRoadWGData:GetDuoBeiTaskType(scene_id)
	if self.duobei_scene_config then
		return self.duobei_scene_config[scene_id]
	end
end

function TianshenRoadWGData:DuoBeiSceneConfig()
	self.duobei_scene_config = {}
	self.duobei_scene_config[SceneType.Fb_Welkin] = RATSDUOBEI_TASK.FANRENXIUXIAN
	-- self.duobei_scene_config[SceneType.LingHunGuangChang] = RATSDUOBEI_TASK.FUMOZHANCHUAN
	self.duobei_scene_config[SceneType.TIAN_SHEN_FB] = RATSDUOBEI_TASK.FENGSHENDIAN
	self.duobei_scene_config[SceneType.BaGuaMiZhen_FB] = RATSDUOBEI_TASK.FUWENFUBEN
	self.duobei_scene_config[SceneType.PET_FB] = RATSDUOBEI_TASK.SHOUHUXIANLING
	self.duobei_scene_config[SceneType.HIGH_TEAM_EQUIP_FB] = RATSDUOBEI_TASK.HAIDIFEIXU
	self.duobei_scene_config[SceneType.QingYuanFB] = RATSDUOBEI_TASK.QINYUANFUBEN
	self.duobei_scene_config[SceneType.WorldBoss] = RATSDUOBEI_TASK.SHIJIEMOWANG
	self.duobei_scene_config[SceneType.HONG_MENG_SHEN_YU] = RATSDUOBEI_TASK.HONGMENGSHENYU
	self.duobei_scene_config[SceneType.BOOTYBAY_FB] = RATSDUOBEI_TASK.WABAO 		--藏宝湾单人本任务进入才多倍
	-- self.duobei_scene_config[SceneType.TEAM_BOOTYBAY_FB] = RATSDUOBEI_TASK.WABAO  --藏宝湾组队不双倍
	self.duobei_scene_config[SceneType.COPPER_FB] = RATSDUOBEI_TASK.JINCHANBAOKU
	
	self.duobei_scene_config[SceneType.VIP_BOSS] = RATSDUOBEI_TASK.MOWANGCHAOXUE
	self.duobei_scene_config[SceneType.ManHuangGuDian_FB] = RATSDUOBEI_TASK.MANHUANGUDIAN
	self.duobei_scene_config[SceneType.Field1v1] = RATSDUOBEI_TASK.JINGJICHANG
	self.duobei_scene_config[SceneType.KF_BOSS] = RATSDUOBEI_TASK.MANHUANSHENSHOU
	self.duobei_scene_config[SceneType.Wujinjitan] = RATSDUOBEI_TASK.FUMOZHANCHUAN
end

--获取副本是否有多倍活动开启
function TianshenRoadWGData:GetHasDuoBeiInCopy()
	for i=RATSDUOBEI_TASK.FANRENXIUXIAN, RATSDUOBEI_TASK.HAIDIFEIXU do
		if self:GetDuoBeiTimes(i) > 0 then
			return true
		end
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.WABAO) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANGUDIAN) > 0 then
		return true
	end
	
	return false
end

--获取魔王是否有boss开启
function TianshenRoadWGData:GetHasDuoBeiInBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.SHIJIEMOWANG) > 0 then
		return true
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.MOWANGCHAOXUE) > 0 then
		return true
	end 

	return false 
end

--获取世界服是否有boss开启
function TianshenRoadWGData:GetHasDuoBeiInWorldBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.HONGMENGSHENYU) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANSHENSHOU) > 0 then
		return true
	end

	return false 
end

-------------------------------天神打脸图-----------------------------------------------
function TianshenRoadWGData:GetTianShenDaLianCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").tianshen_dalian
	if cfg ~= nil then
		return cfg[1]
	end
	return nil
end

---------------------------------------------------------------------------
--获取活动是否开启
function TianshenRoadWGData:GetActivityState(tab_index)
	local activity_type = 0
	if tab_index == TabIndex.tianshenroad_login then
		activity_type = ACTIVITY_TYPE.GOD_DENGLU_YOULI
	elseif tab_index == TabIndex.tianshenroad_first_recharge then
		activity_type = ACTIVITY_TYPE.GOD_CHAOZHI_SHOUCHONG
	elseif tab_index == TabIndex.tianshenroad_total_recharge then
		activity_type = ACTIVITY_TYPE.GOD_LEIJI_HAOLI
	elseif tab_index == TabIndex.tianshenroad_shenqi then
		activity_type = ACTIVITY_TYPE.GOD_WANT_SHENQI
	elseif tab_index == TabIndex.tianshenroad_chongbang then
		activity_type = ACTIVITY_TYPE.GOD_CHONGBANG
	elseif tab_index == TabIndex.tianshenroad_jianglin then
		activity_type = ACTIVITY_TYPE.GOD_JIANGLIN
	elseif tab_index == TabIndex.tianshenroad_mowang then
		activity_type = ACTIVITY_TYPE.GOD_MOWANG_YOULI
	elseif tab_index == TabIndex.tianshenroad_duobei then
		activity_type = ACTIVITY_TYPE.GOD_DOUBLE_JIANGLI
	elseif tab_index == TabIndex.tianshenroad_xianshi_miaosha then--限时秒杀
		activity_type = ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA
	end
	return ActivityWGData.Instance:GetActivityIsOpen(activity_type)
end

--获取活动的结束时间
function TianshenRoadWGData:GetActivityInValidTime(tab_index)
	local activity_type = 0
	if tab_index == TabIndex.tianshenroad_login then
		activity_type = ACTIVITY_TYPE.GOD_DENGLU_YOULI
	elseif tab_index == TabIndex.tianshenroad_first_recharge then
		activity_type = ACTIVITY_TYPE.GOD_CHAOZHI_SHOUCHONG
	elseif tab_index == TabIndex.tianshenroad_total_recharge then
		activity_type = ACTIVITY_TYPE.GOD_LEIJI_HAOLI
	elseif tab_index == TabIndex.tianshenroad_shenqi then
		activity_type = ACTIVITY_TYPE.GOD_WANT_SHENQI
	elseif tab_index == TabIndex.tianshenroad_chongbang then
		activity_type = ACTIVITY_TYPE.GOD_CHONGBANG
	elseif tab_index == TabIndex.tianshenroad_jianglin then
		activity_type = ACTIVITY_TYPE.GOD_JIANGLIN
	elseif tab_index == TabIndex.tianshenroad_mowang then
		activity_type = ACTIVITY_TYPE.GOD_MOWANG_YOULI
	elseif tab_index == TabIndex.tianshenroad_duobei then
		activity_type = ACTIVITY_TYPE.GOD_DOUBLE_JIANGLI
	elseif tab_index == TabIndex.tianshenroad_xianshi_miaosha then--限时秒杀
		activity_type = ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA
	else
		activity_type = ACTIVITY_TYPE.RAND_ACT_TIANSHEN_DALIAN	
	end

	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(activity_type)
	if activity_data ~= nil then
		return activity_data.end_time, activity_data.start_time
	end
	return  0
end

function TianshenRoadWGData:GetRemindNameMap()
	if self.remind_name_list == nil then
		self.remind_name_list = {}
		self.remind_name_list[10] = RemindName.TianShenRoad_Login
		self.remind_name_list[20] = RemindName.TianShenRoad_FirstRecharge
		self.remind_name_list[30] = RemindName.TianShenRoad_TotalReCharge
		self.remind_name_list[40] = RemindName.TianShenRoad_ShenQi
		self.remind_name_list[50] = RemindName.TianShenRoad_ChongBang
		self.remind_name_list[60] = RemindName.TianShenRoad_JiangLin
		self.remind_name_list[70] = RemindName.TianShenRoad_MoWang
		-- self.remind_name_list[80] = RemindName.TianShenRoad_DuoBei
		self.remind_name_list[80] = RemindName.TianShenRoad_MiaoSha
	end

	return self.remind_name_list
end

--获取活动的奖励数据
function TianshenRoadWGData:GetActivityRewardState(tab_index)
	local state = 0
	if tab_index == TabIndex.tianshenroad_login then
		state = self:IsShowLoginRedPoint()
	elseif tab_index == TabIndex.tianshenroad_first_recharge then
		state = self:IsShowShouChongRedPoint()
	elseif tab_index == TabIndex.tianshenroad_total_recharge then
		state = self:IsShowLeiChongRedPoint()
	elseif tab_index == TabIndex.tianshenroad_shenqi then
		state = self:IsShowShenQiRedPoint()
	elseif tab_index == TabIndex.tianshenroad_chongbang then
		state = self:IsShowChongBangRedPoint()
	elseif tab_index == TabIndex.tianshenroad_jianglin then
		state = self:IsShowJianLinRedPoint()
	elseif tab_index == TabIndex.tianshenroad_mowang then
	elseif tab_index == TabIndex.tianshenroad_duobei then
	elseif tab_index == TabIndex.tianshenroad_xianshi_miaosha then--限时秒杀
		state = TianShenRoadMiaoshaWGData.Instance:ShowHeFuMiaoshaRemind()
	end
	return  state
end

function TianshenRoadWGData:IsActivityLastDay(tab_index)
	local end_time = self:GetActivityInValidTime(tab_index)
	local end_date = os.date("*t", end_time)
	local cur_date = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	if cur_date.month == end_date.month and cur_date.day == end_date.day then
		return true
	end
	return false
end

--[[
function TianshenRoadWGData:GotoShiLian()
	local boss_id, cfg_data = self:GetBossId()
	if cfg_data then
		RoleWGCtrl.Instance:SetJumpAlertCheck(cfg_data.scene_id, function()
			local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
			if cur_vip_level > 0 then
				EmojiTextUtil.FlyToPos(cfg_data.scene_id, cfg_data.boss_posx, cfg_data.boss_posy)
			else
				GuajiWGCtrl.Instance:MoveToPos(cfg_data.scene_id, cfg_data.boss_posx, cfg_data.boss_posy,0,0,1)
			end
			local move_to_pos_call_back = function ()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(move_to_pos_call_back)
		end, true)
	end
end
--]]
------------------------- 各活动红点数据-------------------------
-- 主界面红点提示
function TianshenRoadWGData:IsShowMainTianShenRoadRedPoint()
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLoginRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowShouChongRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLeiChongRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowShenQiRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowJianLinRedPoint() then
		return 1
	end
	
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowChongBangRedPoint() then
		return 1
	end

	--天神之路--限时秒杀红点
	if ShowRedPoint.SHOW_RED_POINT == TianShenRoadMiaoshaWGData.Instance:ShowHeFuMiaoshaRemind() then
		return 1
	end
	
	return 0
end

function TianshenRoadWGData:IsShowLoginRedPoint()
	if self.LoginDayData ~= nil and self.LoginDayData.day_list ~= nil then
		local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
		for k,v in ipairs(self.LoginDayData.day_list) do
			if v.common_gift_state == TianShenRoadRewardState.KLQ then
				return 1
			end

			if v.special_gift_state == TianShenRoadRewardState.KLQ then
				return 1
			elseif v.special_gift_state == TianShenRoadRewardState.BKL and v.index <= self.LoginDayData.activity_day_index then
				local cfg = self:GetLoginRewardCfgByDay(v.index)
				if cfg.vip_lv ~= "" and cfg.vip_lv > 0 and cur_vip_level >= cfg.vip_lv then
					return 1
				end
			end
		end
	end
	return 0
end

function TianshenRoadWGData:GetFirstHasRewardDayIndex()
	if self.LoginDayData ~= nil and self.LoginDayData.day_list ~= nil then
		local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
		for k,v in ipairs(self.LoginDayData.day_list) do
			local cfg = self:GetLoginRewardCfgByDay(v.index)
			if v.common_gift_state == TianShenRoadRewardState.KLQ then
				return k
			end

			if v.special_gift_state == TianShenRoadRewardState.KLQ then
				return k
			else
				if v.special_gift_state == TianShenRoadRewardState.BKL and v.index <= self.LoginDayData.activity_day_index then
					if cfg.vip_lv ~= "" and cfg.vip_lv > 0 and cur_vip_level >= cfg.vip_lv then
						return k
					end
				end
			end
		end
	end
	return 0
end

function TianshenRoadWGData:IsShowShouChongRedPoint()
	if self.ShouChongData ~= nil then
		if self.ShouChongData.gift_state == TianShenRoadRewardState.KLQ then
			return 1
		end
	end
	return 0
end

function TianshenRoadWGData:IsShowLeiChongRedPoint()
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil then
		for k,v in ipairs(self.LeiChongData.dangwei_info) do
			if v == TianShenRoadRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end

function TianshenRoadWGData:IsShowShenQiRedPoint()
	if self.SQData ~= nil and self.SQData.dangwei_state ~= nil then
		for i,v in ipairs(self.SQData.dangwei_state) do
			if v == TianShenRoadRewardState.KLQ then
				return 1
			end
		end
	end
	return self:IsShenQiTaskRed()
end

function TianshenRoadWGData:IsShenQiTaskRed()
	if self.SQData ~= nil and self.SQData.task_list ~= nil then
		for i,v in pairs(self.SQData.task_list) do
			if v.gift_state == TianShenRoadRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end

function TianshenRoadWGData:IsShowChongBangRedPoint()
	if self.CBData ~= nil and self.CBData.zhanli_rewaerd_status ~= nil then
		for i,v in ipairs(self.CBData.zhanli_rewaerd_status) do
			if v == TianShenRoadRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end

function TianshenRoadWGData:IsShowJianLinRedPoint()
	if self:IsInTSJLActivity() then
		return 1
	end
	return 0
end

function TianshenRoadWGData:CalcIrregularProgress(value)
	local cur_progress = 0
	local progress_list = {1/6, 2/6, 3/6, 4/6, 5/6, 1}        --对应的进度条值
	local max_jifen = self:GetMaxJiFen()
	for k,v in pairs(progress_list) do
		local cur_need = self:GetBXTargetScore(k)
		local next_need = 0
		local next_cfg = self:GetBXTargetCfg(k + 1)
		if IsEmptyTable(next_cfg) then
			next_need = max_jifen
		else
			next_need = next_cfg.jigsaw_num
		end
		local cur_value = progress_list[k] or 0
		local next_value = progress_list[k + 1] and progress_list[k + 1] or progress_list[#progress_list]

		if value >= cur_need and value <= next_need then
			cur_progress = (value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif value < cur_need then
			cur_progress =  value * cur_value / cur_need
			break 
		elseif value >= max_jifen then
			cur_progress = progress_list[#progress_list]
			break
		end
	end
	return cur_progress
end

function TianshenRoadWGData:GetMaxJiFen()
	local max_jifen = 0
	local cfg = self:GetSQRewardCfg()
	for k,v in pairs(cfg) do
		if v.jigsaw_num > max_jifen then
			max_jifen = v.jigsaw_num
		end
	end
	return max_jifen
end

function TianshenRoadWGData:GetBXTargetCfg(id)
	local reward_cfg = self:GetSQRewardCfg()
	if reward_cfg ~= nil and reward_cfg[id] ~= nil then
		return reward_cfg[id]
	end
	return {}
end

--活动按钮开启
function TianshenRoadWGData:OpenActivityIsOpen()
	local role_level = RoleWGData.Instance.role_vo.level
	local cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.GOD_TIANSHENROAD) 
	if cfg and cfg.level <= role_level then
		return true
	end

	return false
end

function TianshenRoadWGData:GetActModelCfgById(act_id)
	return self.act_model_cfg[act_id]
end