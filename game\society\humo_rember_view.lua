HuMoRemberInfoView = HuMoRemberInfoView or BaseClass(SafeBaseView)

function HuMoRemberInfoView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	local assetbundle = "uis/view/society_ui_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, assetbundle, "layout_humo_rember_view")
end

function HuMoRemberInfoView:ReleaseCallBack()
	if self.humo_list then
		self.humo_list:DeleteMe()
		self.humo_list = nil
	end

end

function HuMoRemberInfoView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.anchoredPosition = Vector2(42.5,4)
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(786.5,497.5)
	self:SetSecondView()
	self.node_list["title_view_name"].text.text = Language.Society.HuMoViewName
end

function HuMoRemberInfoView:ShowIndexCallBack()
	if nil == self.humo_list then
		self.humo_list = AsyncListView.New(HuMoRemberItemRender,self.node_list["list_rember"])
	end
end

function HuMoRemberInfoView:OnFlush()
	local data = SocietyWGData.Instance:GetTouchRecordInfo()
	if nil == data then
		self.node_list["list_rember"]:SetActive(false)
		self.node_list["bg0"]:SetActive(false)
		self.node_list["no_rember"]:SetActive(true)
		return
	end
	self.node_list["list_rember"]:SetActive(#data > 0)
	self.node_list["bg0"]:SetActive(#data > 0)
	self.node_list["no_rember"]:SetActive(#data <= 0)

	if self.node_list["bg0"]:GetActive() then
		self:SetHuMoInfo()
	end

	if self.humo_list then
		self.humo_list:SetDataList(data,0)
	end
end

function HuMoRemberInfoView:SetHuMoInfo()
	local shengyu_num = SocietyWGData.Instance:GetDayTouchShengYuNum()

	if self.node_list["today_xiongbao_count"] then
		self.node_list["today_xiongbao_count"].text.text = string.format(Language.Society.HuMoNum,shengyu_num)
	end
	
	local humo_info = SocietyWGData.Instance:GetFriendTouchBaseInfo()
	local vip_level = RoleWGData.Instance.role_vo.vip_level
	local be_humo_num = SocietyWGData.Instance:GetDayBeTouchShengYuNum(vip_level,humo_info.today_be_touch_count)

	self.node_list["today_beibao_count"].text.text = string.format(Language.Society.BeHuMoNum,be_humo_num)
end

HuMoRemberItemRender = HuMoRemberItemRender or BaseClass(BaseRender)
function HuMoRemberItemRender:__init()
	
end

function HuMoRemberItemRender:LoadCallBack()

end

function HuMoRemberItemRender:__delete()

end


function HuMoRemberItemRender:OnFlush()
	if not self.data then
		return
	end 
	local is_double = self.index%2
	--self.node_list["img9_bg"]:SetActive(is_double ~= 0)
	self.node_list["lbl_name"].text.text = string.format(Language.Society.HuMoRemberHint,self.data.record_name)
	local str = os.date("*t", self.data.touch_timestamp)
	self.node_list["label_time"].text.text = string.format(Language.Society.HuMoRemberTimeHint,str.year,str.month,str.day,str.hour,str.min)
end




