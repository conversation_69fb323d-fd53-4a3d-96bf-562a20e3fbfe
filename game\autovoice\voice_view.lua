AutoVoiceView = AutoVoiceView or BaseClass(SafeBaseView)
--语音输入

--是否在范围内
VoiceViewDragState = {
	InRange = 1,
	OutRange = 2,
}

local VoiceTime = 60

function AutoVoiceView:__init()
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "AutoVioceView")
	self.view_layer = UiLayer.PopTop
	self.time_value = 0
	self.recorder_time_stamp = 0
	self.is_main_chat_voice = false
	self.is_main_chat_voice_1 = false
	self.is_cancel_voice = false
	self.drag_state = VoiceViewDragState.InRange

	-- 是否有麦克风权限
	self.has_auth = true
end

function AutoVoiceView:ReleaseCallBack()
	self.time = nil
	self.content = nil
	self.is_load_complete = false
	self:RemoveCountDown()
end

function AutoVoiceView:LoadCallBack()
	self.time = self.node_list["text"]
	self.content = self.node_list["content"]
	self.is_load_complete = true

	self:StartVoiceSecond()
end

function AutoVoiceView:StartVoiceSecond()
	if not self:IsLoadedIndex(0) then
		return
	end

	self:RemoveCountDown()
	self.has_auth = true
	self.time_value = 0
	self.recorder_time_stamp = Status.NowTime

	-- 模拟器暂时关闭录音
	if DeviceTool.IsEmulator() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Chat.EmulatorRecord)
		self:Close()
		return
	end

	--麦克风异常等
	local recorder_callback = function (succ, error_code, msg)
		if not succ then
			if -1 == error_code then
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.NoAudioAuthTip)
			elseif -2 == error_code then
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.IsRocording)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.RocorderError)
			end
		else
			if UNITY_ANDROID then
				-- 录音结束
				if error_code == 1 then
					self:UploadVoice(msg)
				end
			end
		end
	end
	--是否使用收费语音录制
	if IS_FEES_VOICE then
		self.has_auth = AudioGVoice.StartRecorder2()
	else
		if UNITY_ANDROID then
			self.has_auth = AudioRecorder.StartRecorder(recorder_callback)
		else
			self.has_auth = AudioRecorder.Start(recorder_callback)
		end
	end

	if not self.has_auth then
		self:Close()
		return
	end

	self.quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.ChangeTime, self), 0)

	--关闭所有声音
	AudioService.Instance:SetMasterVolume(0.0)
	if self.is_main_chat_voice_1 then
		self.content.rect.anchoredPosition = Vector2(121.8,-16)
	else
		-- if self.is_main_chat_voice then
		self.content.rect.anchoredPosition = Vector2(0,0)
		-- else
		-- 	self.content.rect.anchoredPosition = Vector2(-303,-191)
		-- end
	end
end

function AutoVoiceView:RemoveCountDown()
	if self.quest then
		GlobalTimerQuest:CancelQuest(self.quest)
		self.quest = nil
	end
end

function AutoVoiceView:ChangeTime()
	local elapse_time = Status.NowTime - self.recorder_time_stamp
	local value = 1 - elapse_time / VoiceTime
	self.node_list["progress"].slider.value = math.min(value, 1)
	self.time_value = math.floor(elapse_time)

	if elapse_time >= VoiceTime then
		self:RemoveCountDown()
		self:Close()
	end
end

function AutoVoiceView:SetChannelType(channel_type)
	self.curr_send_channel = channel_type
end

function AutoVoiceView:SetIsMainChatVoice(bo,bo1)
	self.is_main_chat_voice = bo
	self.is_main_chat_voice_1 = bo1

end

function AutoVoiceView:ShowIndexCallBack()
	self:Flush()
end

function AutoVoiceView:OpenCallBack()
	self:StartVoiceSecond()
	self.is_cancel_voice = false
	self:SetState(VoiceViewDragState.InRange)
end

function AutoVoiceView:CloseCallBack()
	self:RemoveCountDown()
	--还原所有声音
	AudioService.Instance:SetMasterVolume(1.0)

	-- 模拟器暂时关闭录音
	if DeviceTool.IsEmulator() then
		return
	end

	if IS_FEES_VOICE then
		self:SendFeesAudioMsg()
		return
	end

	if UNITY_ANDROID then
		AudioRecorder.StopRecorder()
	else
		local path = AudioRecorder.Stop()
		self:UploadVoice(path)
	end
end

function AutoVoiceView:OnFlush()
	self.node_list.Down:SetActive(self.drag_state == VoiceViewDragState.InRange)
	self.node_list.Up:SetActive(self.drag_state == VoiceViewDragState.OutRange)
end

function AutoVoiceView:UploadVoice(path)
	if self.is_cancel_voice then
		return
	end

	--最短录音时间1S
	if self.time_value < 1 then
		if self.has_auth then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.RecordToShort)
		end
		return
	end

	if path == nil or path == "" then
		return
	end

	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local time = math.floor(TimeWGCtrl.Instance:GetServerTime())

	local duration = self.time_value

	self.sound_name = string.format("sound_%s_%s_%s.amr", time, role_id, duration)
	self.sound_url = ChatRecordMgr.GetUrlSoundPath(self.sound_name)

	local callback = BindTool.Bind1(self.UploadCallback, self)

	--上传失败
	local function CancelUpload()
		HttpClient:CancelUpload(self.sound_url, callback)
	end

	if not HttpClient:Upload(self.sound_url, path, callback) then
		CancelUpload()
		print_error("上传失败", self.sound_url, path)
		return
	end
end

--上传语音回调
function AutoVoiceView:UploadCallback(url, path, is_succ)
	if is_succ then
		ChatWGData.Instance:SetChannelCdEndTime(self.curr_send_channel)
		self:SendSoundMsg(self.sound_name)
	end
end

function AutoVoiceView:IsPrivate()
	return CHANNEL_TYPE.PRIVATE == self.curr_send_channel
end

-- 发送语音消息
function AutoVoiceView:SendSoundMsg(message)
	if ChatWGData.Instance:IsPHPVipLimitChat(self.curr_send_channel) then
		return
	end

	local content_type = CHAT_CONTENT_TYPE.AUDIO
	if not self:IsPrivate() then
		ChatWGCtrl.Instance:SendChannelChat(self.curr_send_channel, message, content_type)
	else
		self:SendPrivateAudioMsg(message, content_type)
	end
end

-- 发送收费语音消息
function AutoVoiceView:SendFeesAudioMsg()
	if ChatWGData.Instance:IsPHPVipLimitChat(self.curr_send_channel) then
		return
	end
	
	if not self.has_auth then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.NoAudioAuthTip)
	end

	local duration = self.time_value
	local curr_send_channel = self.curr_send_channel
	local is_private = self:IsPrivate()
	local is_cancel_voice = self.is_cancel_voice
	AudioGVoice.StopRecorder(function (is_succeed, file_id, str)
		if is_succeed then
			if is_cancel_voice or not self.has_auth then
				return
			end

			--最短录音时间1S
			if duration < 1 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.RecordToShort)
				return
			end

			-- 翻译失败
			if nil == str or "" == str or "nil" == str then
				str = Language.Chat.TransformFail
			end

			local content_type = CHAT_CONTENT_TYPE.FEES_AUDIO

			local content = string.format("%s_%s_%s", file_id, str, duration)
			if not is_private then
				ChatWGCtrl.Instance:SendChannelChat(curr_send_channel, content, content_type)
			else
				self:SendPrivateAudioMsg(content, content_type)
			end
		end
	end)
end

-- 发送私聊语音消息
function AutoVoiceView:SendPrivateAudioMsg(message, content_type)
	if ChatWGData.Instance:IsPHPVipLimitChat(CHANNEL_TYPE.PRIVATE) then
		return
	end

	local private_role_id = SocietyWGCtrl.Instance.society_view:GetPrivateRoleId()
	local msg_info = ChatWGData.CreateMsgInfo()
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	msg_info.from_uid = main_vo.role_id
	msg_info.username = main_vo.name
	msg_info.sex = main_vo.sex
	msg_info.camp = main_vo.camp
	msg_info.prof = main_vo.prof
	msg_info.authority_type = main_vo.authority_type
	msg_info.avatar_key_small = main_vo.avatar_key_small
	msg_info.level = main_vo.level
	msg_info.vip_level = main_vo.vip_level
	msg_info.channel_type = CHANNEL_TYPE.PRIVATE
	msg_info.content = message
	msg_info.send_time_str = TimeUtil.FormatTable2HMS(TimeWGCtrl.Instance:GetServerTimeFormat())
	msg_info.content_type = content_type
	msg_info.tuhaojin_color = 0			--土豪金

	ChatWGData.Instance:AddPrivateMsg(private_role_id, msg_info)
	if ChatWGCtrl.Instance.view:IsOpen() then
		ChatWGCtrl.Instance.view:Flush(CHANNEL_TYPE.PRIVATE, {1})
	end
	ChatWGCtrl.Instance:SendSingleChat(private_role_id, message, content_type)
	SocietyWGCtrl.Instance.society_view:UpdateMsg() -- 刷新本地消息
end

function AutoVoiceView:SetIsCancelVoice(is_cancel_voice)
	self.is_cancel_voice = is_cancel_voice
end

function AutoVoiceView:SetState(drag_state)
	if self.drag_state == drag_state then
		return
	end
	self.drag_state = drag_state
	self:Flush()
end

