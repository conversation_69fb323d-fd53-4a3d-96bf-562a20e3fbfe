FightSoulView = FightSoulView or BaseClass(SafeBaseView)
function FightSoulView:__init()
	self.view_style = ViewStyle.Full

	self:SetMaskBg(false)
	self.is_safe_area_adapter = true
	self.default_index = TabIndex.fight_soul_train
	self.remind_tab = {
		{RemindName.FightSoul_Train},
		{RemindName.FightSoul_Compose},
		{RemindName.FightSoul_Bone},
		{RemindName.FightSoul_CuiZhuo},
	}

	self.tab_sub = {}

	local bundle = "uis/view/fight_soul_ui_prefab"
	self:AddViewResource(0, bundle, "layout_fight_soul_bg")
	-- self:AddViewResource(TabIndex.fight_soul_bone, bundle, "layout_fight_soul_bg2")
    self:AddViewResource(TabIndex.fight_soul_train, bundle, "layout_fight_soul_train")
	self:AddViewResource(TabIndex.fight_soul_compose, bundle, "layout_fight_soul_compose")
	self:AddViewResource(TabIndex.fight_soul_bone, bundle, "layout_fight_soul_bone")
	self:AddViewResource(TabIndex.fight_soul_cuizhuo, bundle, "layout_fight_soul_cuizhuo")
    self:AddViewResource({TabIndex.fight_soul_train, TabIndex.fight_soul_bone, TabIndex.fight_soul_cuizhuo}, bundle, "layout_fight_soul_base_left")

	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(0, bundle, "layout_fight_soul_base")
end

function FightSoulView:__delete()

end

function FightSoulView:OpenCallBack()
	FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.FAKE_LOCK_PROTOCOL)
end

function FightSoulView:CloseCallBack()

end

function FightSoulView:LoadCallBack()
    if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabVisible, self))
        self.tabbar:Init(Language.FightSoul.NameTable, nil, nil, nil, self.remind_tab)
        self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
        FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.FightSoulView, self.tabbar)
    end

    -- if not self.money_bar then
    --     self.money_bar = MoneyBar.New()
    --     local bundle, asset = ResPath.GetWidgets("MoneyBar")
	-- 	local show_params = {
	-- 	show_gold = true, show_bind_gold = true,
	-- 	show_coin = true, show_silver_ticket = true,
	-- 	}
	-- 	self.money_bar:SetMoneyShowInfo(0, 0, show_params)
    --     self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    -- end

	if self.remind_callback == nil then
		self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
		RemindManager.Instance:Bind(self.remind_callback, RemindName.SiXiangCall_SXSM)
	end

	XUI.AddClickEventListener(self.node_list["btn_tip"], BindTool.Bind(self.OnClickBtnTip, self))
end

function FightSoulView:ReleaseCallBack()
    -- if self.money_bar then
    --     self.money_bar:DeleteMe()
    --     self.money_bar = nil
    -- end

    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

	if self.fight_show_list then
		for k, v in pairs(self.fight_show_list) do
			v:DeleteMe()
		end

		self.fight_show_list = nil
	end

	if self.remind_callback then
		RemindManager.Instance:UnBind(self.remind_callback)
		self.remind_callback = nil
	end

    self.jump_slot_index = nil
    self.jump_slot_type = nil
    self.select_slot_index = nil
	self.show_fs_model_ani = nil
	self.do_train_tween_flag = nil
	self.do_compose_tween_flag = nil
	self.do_bone_tween_flag = nil
	self.list_load_over = nil

    self:DeleteTrainView()
	self:DeleteComposeView()
	self:DeleteBoneView()
	self:DeleteCuiZhuoView()
end

function FightSoulView:LoadIndexCallBack(index)
	if index == TabIndex.fight_soul_train then
        self:InitFightSoulList()
        self:InitTrainView()
	elseif index == TabIndex.fight_soul_compose then
		self:InitComposeView()
	elseif index == TabIndex.fight_soul_bone then
		self:InitFightSoulList()
		self:InitBoneView()
	elseif index == TabIndex.fight_soul_cuizhuo then
		self:InitFightSoulList()
		self:InitCuiZhuoView()
	end
end

function FightSoulView:InitFightSoulList()
    if self.fight_show_list == nil then
		self.select_slot_index = -1
		self.fight_show_list = {}
		for i = 1, FIGHT_SOUL_TYPE.MAX do
			self.fight_show_list[i] = FightSoulItem.New(self.node_list["fight_soul_cell" .. i])
			self.fight_show_list[i]:SetIndex(i - 1)
			self.fight_show_list[i]:SetSelectCallBack(BindTool.Bind(self.OnSelectFightSoulCB, self))
		end

		self.list_load_over = true
		-- self:InitSlotListTween()
    end
end

function FightSoulView:SetTabVisible()
	local bag_list = FightSoulWGData.Instance:GetFightSoulBagList()
	local had_data = not IsEmptyTable(bag_list)
	if self.tabbar then
		self.tabbar:SetVerToggleVisble(TabIndex.fight_soul_compose, had_data)
	end
end

function FightSoulView:ShowIndexCallBack(index)
    self.jump_slot_index = nil
	if index == TabIndex.fight_soul_train then
		self.do_train_tween_flag = true
		self.jump_slot_index = FightSoulWGData.Instance:GetJumpSlotIndex(index)
	elseif index == TabIndex.fight_soul_compose then
		self.select_compose_index_inbag = nil
		self.do_compose_tween_flag = true
	elseif index == TabIndex.fight_soul_bone then
		self.do_bone_tween_flag = true
		self.jump_slot_index = FightSoulWGData.Instance:GetJumpSlotIndex(index)
	elseif index == TabIndex.fight_soul_cuizhuo then
		self.jump_slot_index = FightSoulWGData.Instance:GetJumpSlotIndex(index)
		self:CuiZhuoShowIndexCallBack()
    end
end

function FightSoulView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
			if v.select_type then
				self.jump_slot_type = v.select_type -- 跳转选择固定类型
			end

			if index == TabIndex.fight_soul_train then
                if self.jump_slot_index and self.jump_slot_index ~= self.select_slot_index then
                    self:FightSoulListSetData()
                else
                    self:FightSoulListSetData()
					self:FlushTrainView()
					self:FlushOutFightPart()
                end
			elseif index == TabIndex.fight_soul_compose then
				self:FlushComposeBag()
			elseif index == TabIndex.fight_soul_bone then
				self:FightSoulListSetData()
				self:FlushBoneView()
			elseif index == TabIndex.fight_soul_cuizhuo then
				self:FightSoulListSetData()
				self:FlushCuiZhuoView()
			end
		elseif k == "soul_list" then
			self:FightSoulListSetData()
		----[[养成
		elseif k == "break_stuff_change" then
			if index == TabIndex.fight_soul_train then
				self:FightSoulListSetData()
				self:FlushTrainView(true)
			end
		elseif k == "uplevel_success" then
			if index == TabIndex.fight_soul_train then
				self:PlayUpLevelEffect()
				self:FightSoulListSetData()
				self:FlushTrainView()
			end
		elseif k == "break_success" then
			if index == TabIndex.fight_soul_train then
				self:PlayBreakEffect()
				self:FightSoulListSetData()
				self:FlushTrainView()
			end
		elseif k == "out_fight" then
			if index == TabIndex.fight_soul_train then
				self:FlushOutFightPart()
				self:FightSoulListSetData()
				if v.is_out_fight then
					self:PlayOutFightEffect()
				end
			end
		--]]
		elseif k == "select_stuff_cb" then
			if index == TabIndex.fight_soul_compose then
				self:SetComposeStuffSelect()
			end
		----[[魂装
		elseif k == "bone_list_list" then
			if index == TabIndex.fight_soul_bone then
				self:FightSoulListSetData()
				self:FlushBoneEquipList()
			end
		elseif k == "bone_strength" then
			if index == TabIndex.fight_soul_bone then
				self:FightSoulListSetData()
				self:FlushBoneEquipList()
				self:FlushBoneStrengthBtn()
				self:FlushSlotCapability()
			end
		elseif k == "bone_suit" then
			if index == TabIndex.fight_soul_bone then
				self:FlushBoneSuitList()
				self:FlushSlotCapability()
			end
		elseif k == "bone_list_compose" then
			if index == TabIndex.fight_soul_bone then
				self:FightSoulListSetData()
				-- self:FlushBoneEquipList()
				self:FlushBoneComposeBtn()
			end
		--]]
		elseif k == "flush_zhuotie_remind" then
			self:FlushCuiZhuoRemind()
		end
	end
end

function FightSoulView:OnSelectFightSoulCB(item, is_click)
    if nil == item or nil == item.data then
		return
	end

    if is_click and self.select_slot_index == item.data.sixiang_slot_index then
		self:ClickTrainWear()
        return
    end

	local cell_index = item.data.sixiang_slot_index
	local solt_select_change = false
	if self.select_slot_index ~= cell_index then
		self.select_slot_index = item.data.sixiang_slot_index

		for k, v in pairs(self.fight_show_list) do
			v:SetSelectIndex(cell_index)
		end

		solt_select_change = true
	end
    
	self.show_fs_model_ani = true
	self.jump_slot_type = nil

	self:FlushOutFightPart()
    self:FlushViewByIndex(solt_select_change)
end

function FightSoulView:FightSoulListSetData()
    local slot_list = FightSoulWGData.Instance:GetFightSoulShowList()
	for k, v in pairs(self.fight_show_list) do
		v:SetData(slot_list[k])
	end

	local real_index
	---[[ 跳转选择固定类型
    if self.jump_slot_type then
    	local fs_item = nil
		for k,v in ipairs(slot_list) do
			fs_item = FightSoulWGData.Instance:GetFightSoulSlot(v.sixiang_slot_index)
			if fs_item and fs_item:GetType() == self.jump_slot_type then
				real_index = k
			end
		end

		if real_index then
			self:OnSelectFightSoulCB(self.fight_show_list[real_index])
			return
		end
    end
    --]]

    if self.jump_slot_index then
		if self.jump_slot_index ~= self.select_slot_index then
			for k,v in ipairs(slot_list) do
				if self.jump_slot_index == v.sixiang_slot_index then
					real_index = k
					break
				end
			end
			
			if real_index then
				self:OnSelectFightSoulCB(self.fight_show_list[real_index])
			end
		end

		self.jump_slot_index = nil
    end

	if real_index then
		return
	end

	if self.select_slot_index == nil or self.select_slot_index == -1 then
		self:OnSelectFightSoulCB(self.fight_show_list[1])
	end
end

function FightSoulView:FlushViewByIndex(solt_select_change)
    if self.show_index == TabIndex.fight_soul_train then
        self:FlushTrainView()
	elseif self.show_index == TabIndex.fight_soul_bone then
		self:FlushBoneView()
	elseif self.show_index == TabIndex.fight_soul_cuizhuo then
		self:FlushCuiZhuoView(solt_select_change)
    end
end

function FightSoulView:GetCurFightSoulSlot()
    return FightSoulWGData.Instance:GetFightSoulSlot(self.select_slot_index)
end



-- 升级特效
function FightSoulView:PlayUpLevelEffect()
	if self.node_list.t_effect_node then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true,
			pos = Vector2(0, 0), parent_node = self.node_list.t_effect_node})
	end

	if self.show_index == TabIndex.fight_soul_train and self.node_list.t_uplevel_node then
		local bundle_name, asset_name = ResPath.GetUIEffect("UI_sixiang_shengji")
			EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.t_uplevel_node.transform,
											nil, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))
	end
end

-- 突破特效
function FightSoulView:PlayBreakEffect()
	if self.node_list.t_effect_node then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_tupo, is_success = true,
			pos = Vector2(0, 0), parent_node = self.node_list.t_effect_node})
	end

	if self.show_index == TabIndex.fight_soul_train and self.node_list.t_break_node then
		local bundle_name, asset_name = ResPath.GetUIEffect("UI_sixiang_tupo")
			EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.t_break_node.transform,
											nil, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))
	end
end

function FightSoulView:PlayOutFightEffect()
	local effect_node = self.show_index == TabIndex.fight_soul_train and self.node_list.t_out_fight_node or self.node_list.b_out_fight_node
	if effect_node then
		local bundle_name, asset_name = ResPath.GetUIEffect("UI_sixiang_chuzhan")
			EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, effect_node.transform,
											nil, Vector3(0, 0, 0), Quaternion.Euler(-6, 0, 0))
	end
end

function FightSoulView:OpenSiXiangCall()
	local data = self:GetCurFightSoulSlot()
	if data then
		SiXiangCallWGData.Instance:SetSiXiangSummonType(data:GetType())
	end
	FunOpen.Instance:OpenViewByName(GuideModuleName.SiXiangCallView, TabIndex.sixiang_call_sx)
	ViewManager.Instance:FlushView(GuideModuleName.SiXiangCallView, TabIndex.sixiang_call_sx, "summon_type_change")
end

function FightSoulView:OnClickBtnTip()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.FightSoul.TipsTitle[self.show_index])
	rule_tip:SetContent(Language.FightSoul.TipsContent[self.show_index], nil, nil, nil, true)
end

function FightSoulView:InitSlotListTween()
	RectTransform.SetAnchoredPositionXY(self.node_list.fight_show_list.rect, -100, -19)
    RectTransform.SetAnchoredPositionXY(self.node_list.out_fight_part.rect, 20, -104)
end

function FightSoulView:MoveSlotListTween()
	if self.fight_show_list then
		local cell_list = self.fight_show_list:GetSortCellList()
		for k,v in ipairs(cell_list) do
			v:PalyItemAnimator(k)
		end
	end

	local tween_info = UITween_CONSTS.SiXiangSys
	RectTransform.SetAnchoredPositionXY(self.node_list.fight_show_list.rect, 89, -19)
	self.node_list.out_fight_part.rect:DOAnchorPos(Vector2(20, 4), tween_info.MoveTime)
end

function FightSoulView:OnRemindChange(remind_name, num)
	if remind_name == RemindName.SiXiangCall_SXSM then
		if self.node_list.b_btn_call_remind then
			self.node_list.b_btn_call_remind:SetActive(num > 0)
		end
	end
end
