local SMALL_TYPE_POS_CFG = {
    [3] = {[1] = {x = -70, y = 12}, [2] = {x = 0, y = -15}, [3] = {x = 70, y = 12}},
    [4] = {[1] = {x = -70, y = 12}, [2] = {x = -26, y = -13}, [3] = {x = 26, y = -13}, [4] = {x = 70, y = 12}},
    [5] = {[1] = {x = -70, y = 12}, [2] = {x = -38, y = -9}, [3] = {x = 0, y = -15}, [4] = {x = 38, y = -9}, [5] = {x = 70, y = 12}},
}

function XiuWeiView:PreviewLoadCallBack()
	self.xiuwei_drag_select_index = 1

	self.select_cur_stage_no = -1
	self.select_cur_stage_index = -1

    self.preview_small_state_list = {}
    for i = 1, 5 do
        self.preview_small_state_list[i] = XiuWeiSmallTypeItemCellRender.New(self.node_list["preview_small_state_cell" .. i])
        self.preview_small_state_list[i]:SetClickCallback(BindTool.Bind(self.OnXiuWeiSmallTypeClick, self))
		self.preview_small_state_list[i]:SetIndex(i)
    end

    if not self.preview_reward_list then
        self.preview_reward_list = AsyncListView.New(XiuWeiPreviewRewardItemCellRender, self.node_list.preview_reward_list)
		self.preview_reward_list:SetStartZeroIndex(false)
    end

	self.attr_item_list = {}
	for i = 1, 7 do
		self.attr_item_list[i] = XiuWeiPreviewAttrItemCellRender.New(self.node_list["attr_item" .. i])
	end

	XUI.AddClickEventListener(self.node_list.btn_reward,BindTool.Bind(self.OnClickBtnReward, self))

    -- 选中当前我的境界
    self:InitCreateCamberedList()
end

function XiuWeiView:PreviewShowIndexCallBack()
	self.preview_cur_show_num = -1
end

function XiuWeiView:PreviewReleaseCallBack()
	if self.xiuwei_cambered_list then
        self.xiuwei_cambered_list:DeleteMe()
        self.xiuwei_cambered_list = nil
    end

    if self.preview_small_state_list then
        for k, v in pairs(self.preview_small_state_list) do
            v:DeleteMe()
        end

        self.preview_small_state_list = nil
    end

	if self.attr_item_list then
        for k, v in pairs(self.attr_item_list) do
            v:DeleteMe()
        end

        self.attr_item_list = nil
    end

    if self.preview_reward_list then
        self.preview_reward_list:DeleteMe()
        self.preview_reward_list = nil
    end

	if self.attr_item_list then
		self.attr_item_list:DeleteMe()
		self.attr_item_list = nil
	end

    self.list_index = nil
	self.xiuwei_drag_select_index = nil
	self.select_cur_stage_no = nil
	self.select_cur_stage_index = nil
	self.preview_cur_show_num = nil
end

function XiuWeiView:OnPreviewFlush()
	local preview_stage_list = CultivationWGData.Instance:GetCultivationPreviewCfg()
	if not self.list_index then
		local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
		local stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()

		local default_select = 1

		for k, v in pairs(preview_stage_list) do
			local is_received = CultivationWGData.Instance:GetIsReceivedPreviewReward(v.stage_no)
			local is_can_receive = not is_received and cur_stage >= v.need_stage
			
			if not is_received and is_can_receive then
				self.list_index = k
				break
			end

			if v.stage_no == stage_cfg.client_stage then
				default_select = k
			end
		end

		self.list_index = self.list_index or default_select
	end

    self.boss_list_data = preview_stage_list
    self.xiuwei_cambered_list:CreateCellList(#preview_stage_list)
    local btn_item_list = self.xiuwei_cambered_list:GetRenderList()
    for k, item_cell in ipairs(btn_item_list) do
        local item_data = self.boss_list_data[k]
        item_cell:SetData(item_data)
        if self.list_index == item_cell:GetIndex() then
            self:OnXiuWeiSelectedBtnChange(function()
                self:XiuWeiLsitSelectCallBack(item_cell)
            end, true)
        end
    end
end

function XiuWeiView:InitCreateCamberedList()
	local preview_stage_list = CultivationWGData.Instance:GetCultivationPreviewCfg()

	local cambered_list_data = {
		item_render = XiuWeiPreviewCamberedListItemCellRender,
		asset_bundle = "uis/view/xiuwei_ui_prefab",
		asset_name = "xiuwei_preview_cambered_render",

		scroll_list = self.node_list.xiuwei_preview_cambered_list,
		center_x = 800,
		center_y = -230,
		radius_x = 700,
		radius_y = 720,
		angle_delta = Mathf.PI / 21,
		origin_rotation = Mathf.PI * 0.42,

		is_drag_horizontal = false,
		is_clockwise_list = false,
		speed = 1,
		arg_speed = 0.2,
		viewport_count = 3,

		click_item_cb = BindTool.Bind(self.OnClickXiuWeiItemBtn, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragXiuWeiToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragXiuWeiToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragXiuWeiLEndCallBack, self),
	}

	self.xiuwei_cambered_list = CamberedList.New(cambered_list_data)
end

function XiuWeiView:OnClickXiuWeiItemBtn(item_cell, force_jump)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if (not force_jump and self.list_index == select_index) then
		return
	end

	self.list_index = select_index
	self.xiuwei_drag_select_index = select_index

	self:OnXiuWeiSelectedBtnChange(function()
		self:XiuWeiLsitSelectCallBack(item_cell)
	end, true)
end

function XiuWeiView:OnDragXiuWeiToNextCallBack()
	local max_index = self.boss_list_data and #self.boss_list_data or 6
	self.xiuwei_drag_select_index = self.xiuwei_drag_select_index + 1
	self.xiuwei_drag_select_index = self.xiuwei_drag_select_index > max_index and max_index or self.xiuwei_drag_select_index
end

function XiuWeiView:OnDragXiuWeiToLastCallBack()
	self.xiuwei_drag_select_index = self.xiuwei_drag_select_index - 1
	self.xiuwei_drag_select_index = self.xiuwei_drag_select_index < 1 and 1 or self.xiuwei_drag_select_index
end

function XiuWeiView:OnDragXiuWeiLEndCallBack()
	self:OnXiuWeiSelectedBtnChange(nil, false, self.xiuwei_drag_select_index)
end

function XiuWeiView:OnXiuWeiSelectedBtnChange(callback, is_click, drag_index)
	if self.xiuwei_cambered_list == nil then
		return
	end

	local to_index = drag_index ~= nil and drag_index or self.list_index or 1
	self.xiuwei_cambered_list:ScrollToIndex(to_index, callback, is_click)

	if is_click then
		local item_list = self.xiuwei_cambered_list:GetRenderList()
		for k, item_cell in ipairs(item_list) do
			item_cell:SetSelectedHL(to_index)
		end
	end
end

function XiuWeiView:XiuWeiLsitSelectCallBack(cell)
	self.select_item_data = cell.data

	local cur_stage_no_change = self.select_cur_stage_no ~= cell.data.stage_no
	self.select_cur_stage_no = self.select_item_data.stage_no

	local state_img_bundle, state_img_asset = ResPath.GetXiuWeiImg(self.select_item_data.stage_title_icon)
    self.node_list.preview_state_img.image:LoadSprite(state_img_bundle, state_img_asset)

	-- 奖励
	local is_received = CultivationWGData.Instance:GetIsReceivedPreviewReward(self.select_item_data.stage_no)
    local is_can_receive = not is_received and CultivationWGData.Instance:GetXiuWeiState() >= self.select_item_data.need_stage 

	if is_received then
        self.node_list.btn_reward:SetActive(false)
    elseif is_can_receive then
        self.node_list.btn_reward:SetActive(true)
    else
        self.node_list.btn_reward:SetActive(false)
    end

	local reawrd_data_list = {}
    for i,v in pairs(self.select_item_data.reward_item) do
        local item_data = {}
        item_data.is_received = is_received
        item_data.is_can_receive = is_can_receive
        item_data.stage_no = self.select_item_data.stage_no
        item_data.item = v
        table.insert(reawrd_data_list, item_data)
    end

	self.preview_reward_list:SetDataList(reawrd_data_list)

	--刷新小境界位置
	local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
	local cur_state_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
	local small_state_show_list = XiuWeiWGData.Instance:GetShowSmallStageList(self.select_item_data.stage_no)

    local show_num = #small_state_show_list
    local is_change_num = false
    if self.preview_cur_show_num ~= show_num then
        is_change_num = true
        self.preview_cur_show_num = show_num
    end

	local jump_index = -1
    for k, v in pairs(self.preview_small_state_list) do
        v:SetData(small_state_show_list[k])
        v:ChangeShowPos(is_change_num, show_num)

		if jump_index < 0 or (small_state_show_list[k] and small_state_show_list[k].stage == cur_stage) then
			jump_index = k
		end
    end

	if cur_stage_no_change and jump_index >= 0 then
		self:OnXiuWeiSmallTypeClick(self.preview_small_state_list[jump_index])
	end
end

function XiuWeiView:OnXiuWeiSmallTypeClick(item)
	self.select_cur_stage_index = item.index

	for k, v in pairs(self.preview_small_state_list) do
		v:SetSelectedHL(self.select_cur_stage_index)
	end

	-- 刷新右侧列表
	local attr_info = {}

	for i = 1, 7 do
		local attr_id = item.data["attr_id" .. i] or 0
		local attr_value = item.data["attr_value" .. i] or 0
		
		if attr_id > 0 and attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str)
			local attr_value = AttributeMgr.PerAttrValue(attr_str, attr_value)
			table.insert(attr_info,  {attr_name = attr_name, attr_value = attr_value})
		end
	end

	for i = 1, 7 do
		self.attr_item_list[i]:SetData(attr_info[i] or {})
	end
end

function XiuWeiView:OnClickBtnReward()
    CultivationWGCtrl.Instance:ReceiveStageReward(self.select_item_data.stage_no)
end

--------------------------------XiuWeiPreviewCamberedListItemCellRender------------------------------------
XiuWeiPreviewCamberedListItemCellRender = XiuWeiPreviewCamberedListItemCellRender or BaseClass(BaseRender)

function XiuWeiPreviewCamberedListItemCellRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

function XiuWeiPreviewCamberedListItemCellRender:__delete()
	self.click_callback = nil
end

function XiuWeiPreviewCamberedListItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
	local stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
	self.node_list.flag_cur_stage:CustomSetActive(stage_cfg.client_stage == self.data.stage_no)

	local bundle, asset = ResPath.GetXiuWeiImg(self.data.stage_title_icon)
	self.node_list.xiuwei_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.xiuwei_icon.image:SetNativeSize()
	end)

	local bundle_hl, asset_hl = ResPath.GetXiuWeiImg(self.data.stage_title_icon_hl)
	self.node_list.xiuwei_icon_hl.image:LoadSprite(bundle_hl, asset_hl, function()
		self.node_list.xiuwei_icon_hl.image:SetNativeSize()
	end)

	local is_received = CultivationWGData.Instance:GetIsReceivedPreviewReward(self.data.stage_no)
    local is_can_receive = not is_received and cur_stage >= self.data.need_stage
	self.node_list.remind:CustomSetActive(not is_received and is_can_receive)
end

function XiuWeiPreviewCamberedListItemCellRender:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function XiuWeiPreviewCamberedListItemCellRender:OnClick()
	if self.click_callback then
		self.click_callback()
	end
end

function XiuWeiPreviewCamberedListItemCellRender:SetSelectedHL(index)
	local is_select = self.index == index
	self.node_list.select_image:SetActive(is_select)
	self.node_list.normal_bg:SetActive(not is_select)
	self.node_list.xiuwei_icon:SetActive(not is_select)
	self.node_list.xiuwei_icon_hl:SetActive(is_select)
end

function XiuWeiPreviewCamberedListItemCellRender:OnSelectChange(is_select)
	self.node_list.select_image:SetActive(is_select)
	self.node_list.normal_bg:SetActive(not is_select)
end

--------------------------------XiuWeiSmallTypeItemCellRender------------------------------------
XiuWeiSmallTypeItemCellRender = XiuWeiSmallTypeItemCellRender or BaseClass(BaseRender)

function XiuWeiSmallTypeItemCellRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

function XiuWeiSmallTypeItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
		self:SetVisible(false)
        return
    end

    local cn_num = NumberToChinaNumber(self.index)
    self.node_list.small_state.text.text = string.format(Language.XiuWei.Level, cn_num)
	self:SetVisible(true)
end

function XiuWeiSmallTypeItemCellRender:SetSelectedHL(index)
	local is_select = self.index == index
	self.node_list.hl:SetActive(is_select)
end

function XiuWeiSmallTypeItemCellRender:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function XiuWeiSmallTypeItemCellRender:OnClick()
	if self.click_callback then
		self.click_callback(self)
	end
end

function XiuWeiSmallTypeItemCellRender:ChangeShowPos(need_change_pos, total_num)
    if not need_change_pos then
        return
    end

    local pos_info = (SMALL_TYPE_POS_CFG[total_num] or {})[self.index]
    if not pos_info then
        return
    end

    self:SetAnchoredPosition(pos_info.x, pos_info.y)
end

-----------------------------------XiuWeiPreviewRewardItemCellRender-------------------------------------
XiuWeiPreviewRewardItemCellRender = XiuWeiPreviewRewardItemCellRender or BaseClass(BaseRender)

function XiuWeiPreviewRewardItemCellRender:LoadCallBack()
	if not self.reward_item then
		self.reward_item = ItemCell.New(self.node_list.pod)
	end
end

function XiuWeiPreviewRewardItemCellRender:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function XiuWeiPreviewRewardItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.reward_item:SetData(self.data.item)
	self.node_list.flag_receive:CustomSetActive(self.data.is_received)
	self.node_list.remind:CustomSetActive(self.data.is_can_receive)
end

-----------------------------------XiuWeiPreviewAttrItemCellRender-------------------------------------
XiuWeiPreviewAttrItemCellRender = XiuWeiPreviewAttrItemCellRender or BaseClass(BaseRender)

function XiuWeiPreviewAttrItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
		self:SetVisible(false)
        return
    end

	self.node_list.attr_name.text.text = self.data.attr_name
	self.node_list.attr_value.text.text = self.data.attr_value

	self:SetVisible(true)
end
