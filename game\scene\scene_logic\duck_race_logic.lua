-- 跨服小鸭疾走
DuckRaceLogic = DuckRaceLogic or BaseClass(CommonFbLogic)

function DuckRaceLogic:__init()
	self.main_role_original_move_speed = 900
end

function DuckRaceLogic:__delete()
end

function DuckRaceLogic:Enter(old_scene_type, new_scene_type)
	self.main_role_original_move_speed = GameVoManager.Instance:GetMainRoleVo().move_speed
	self.last_follow_index = -1
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:Open(GuideModuleName.DuckRaceFight)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(false)
    MainuiWGCtrl.Instance:SetSkillShowState(false)
    -- MainuiWGCtrl.Instance:SetTaskButtonTrue()
    MainuiWGCtrl.Instance:SetOtherContents(true)
    MainuiWGCtrl.Instance:SetTaskContents(false)
    
	DuckRaceWGCtrl.Instance:SendGetAllInfo()

	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_DUCK_RACE)
	if activity_status then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_status.next_time)
	end

	self.guaji_change_event = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, BindTool.Bind(self.BindGuaJiFireEvent,self))

	self.last_monster_x = 0
	self.last_monster_y = 0

	HotSpringWGCtrl.Instance:SetAllRoleAppearance()
end

function DuckRaceLogic:Out()
	CommonFbLogic.Out(self)
	MainuiWGCtrl.Instance:UpdateBianShenBtnState(true)
	ViewManager.Instance:Close(GuideModuleName.DuckRaceFight)
	ViewManager.Instance:Close(GuideModuleName.DuckRace)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetSkillShowState(true)
	MainuiWGCtrl.Instance:SetFbIconEndCountDown(0)
	DuckRaceWGCtrl.Instance:CloseSelectTips()
   	if self.guaji_change_event then
   		GlobalEventSystem:UnBind(self.guaji_change_event)
   		self.guaji_change_event = nil
  	end
end

function DuckRaceLogic:Update(now_time, elapse_time)
	BaseSceneLogic.Update(self, now_time, elapse_time)
	local follow_index = DuckRaceWGData.Instance:GetFollowDuckIndex()
	if follow_index and GuajiCache.guaji_type == GuajiType.Auto then --and (Scene.Instance:GetMainRole():IsStand() or follow_index ~= self.last_follow_index) then
		self:MoveToFollowDuck()
	end
	self.last_follow_index = follow_index
end

function DuckRaceLogic:IsNeedRefollow()
	local follow_index = DuckRaceWGData.Instance:GetFollowDuckIndex()
	return Scene.Instance:GetMainRole():IsStand() or follow_index ~= self.last_follow_index
end

function DuckRaceLogic:MoveToFollowDuck()
	local follow_index = DuckRaceWGData.Instance:GetFollowDuckIndex()
	if follow_index then
		local duck_info = DuckRaceWGData.Instance:GetDuckInfoByIndex(follow_index)
		if duck_info then
			local monster_obj = Scene.Instance:GetObj(duck_info.duck_objid)
			if monster_obj and monster_obj:IsMonster() then
				local x, y = monster_obj:GetLogicPos()
				local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
				local distance = GameMath.GetDistance(x, y, self_x, self_y, true)
				if distance > 0.5 and (self.last_monster_x ~= x or self.last_monster_y ~= y) then  -- 判断是否需要重新跟随
					self.last_monster_x = x
					self.last_monster_y = y
					GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), x, y, nil, nil, nil, nil, function()
				 	end)
				end
			end
		end
	end
end

function DuckRaceLogic:IsEnemy()
	return false
end

function DuckRaceLogic:BindGuaJiFireEvent(guaji_type)
	if guaji_type ~= GuajiType.Auto then
		DuckRaceWGData.Instance:SetFollowDuckIndex(nil)
	elseif guaji_type == GuajiType.Auto then
		
	end
end

function DuckRaceLogic:GetSceneSpecialSpeed(character_obj)
	if character_obj:IsMainRole() then
		local follow_index = DuckRaceWGData.Instance:GetFollowDuckIndex()
		if DuckRaceWGData.Instance:GetCurRoundState() == DUCK_RACE_ROUND_STATE.RACING and follow_index then
			local duck_info = DuckRaceWGData.Instance:GetDuckInfoByIndex(follow_index)
			if duck_info then
				local monster_obj = Scene.Instance:GetObj(duck_info.duck_objid)
				if monster_obj then
					local x, y = monster_obj:GetLogicPos()
					local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
					local distance = GameMath.GetDistance(x, y, self_x, self_y, true)
					local speed_scale = math.max(Mathf.Clamp(distance / 20, 0, 1) - 0.15, 0.02)
					local speed = math.max(self.main_role_original_move_speed * speed_scale, math.max(10, monster_obj.vo.move_speed - 160))
					DuckRaceWGCtrl.Instance:SendMainRoleFollowSpeed(speed)
					return Scene.ServerSpeedToClient(speed)
				end
			end
		end
		DuckRaceWGCtrl.Instance:SendMainRoleFollowSpeed(self.main_role_original_move_speed)
	end
	return 
end