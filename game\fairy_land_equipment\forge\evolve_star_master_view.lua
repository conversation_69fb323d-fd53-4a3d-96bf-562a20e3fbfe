EvolveStarMasterView = EvolveStarMasterView or BaseClass(SafeBaseView)

function EvolveStarMasterView:__init()
    self:SetMaskBg(true, true)
    local bundle = "uis/view/fairy_land_equipment_ui_prefab"
	self:AddViewResource(0, bundle, "layout_evolve_star_master")
end

function EvolveStarMasterView:ReleaseCallBack()
	if self.cur_attr_list then
		for k,v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
	end
	self.cur_attr_list = nil

	if self.next_attr_list then
		for k,v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
	end
	self.next_attr_list = nil
end

function EvolveStarMasterView:LoadCallBack()
	self.cur_attr_list = {}
	self.next_attr_list = {}
	for i = 1, 5 do
		self.cur_attr_list[i] = CommonAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.next_attr_list[i] = CommonAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
		self.cur_attr_list[i]:SetAttrNameNeedSpace(true)
		self.next_attr_list[i]:SetAttrNameNeedSpace(true)
	end

	XUI.AddClickEventListener(self.node_list["left_active_btn"], BindTool.Bind1(self.OnClickActiveBtn, self))
    self.node_list["lbl_attr_title"].text.text = Language.FairyLandEquipment.EvolveTotalAttrTitle
end

--data内容 slot 当前所选的神体  part 当前装备部位 
function EvolveStarMasterView:SetData(data)
	self.data = data
end

function EvolveStarMasterView:OnFlush()
	if IsEmptyTable(self.data) then return end
	local slot = self.data.slot
	local cur_total_act_level = FairyLandEquipmentWGData.Instance:GetEvolveLevelActiveTotalLv(slot)
	local cur_total_level = FairyLandEquipmentWGData.Instance:GetEvolveLevelAllLevel(slot)
	local now_attr_cfg = FairyLandEquipmentWGData.Instance:GetEvolveLevelTotalCfg(slot)
	local next_attr_cfg = FairyLandEquipmentWGData.Instance:GetEvolveLevelTotalCfg(slot, true)
	local is_max_level = not next_attr_cfg
	local has_attr_add = cur_total_act_level > 0--是否有加成
	local can_active = not is_max_level and cur_total_level >= next_attr_cfg.level--是否可激活

	if now_attr_cfg then
		local attr_list = EquipWGData.GetSortAttrListByCfg(now_attr_cfg)
		for k,v in pairs(self.cur_attr_list) do
			v:SetData(attr_list[k])
		end
	end

	if next_attr_cfg then
		local attr_list = EquipWGData.GetSortAttrListByCfg(next_attr_cfg)
		for k,v in pairs(self.next_attr_list) do
			v:SetData(attr_list[k])
		end
	end

	--标题
	self.node_list["cur_level_bg"]:SetActive(has_attr_add)
	if has_attr_add then
		self.node_list["now_level"].text.text = string.format(Language.FairyLandEquipment.EvolveTotalAttrTitle_1, cur_total_level)
	end
	self.node_list["next_level_bg"]:SetActive(not is_max_level)
	if is_max_level then
    	self.node_list["next_level"].text.text = Language.FairyLandEquipment.EvolveTotalAttrTitleMax
	else
    	local next_desc = string.format(Language.FairyLandEquipment.EvolveTotalAttrTitle_1, next_attr_cfg.level)
    	local color = cur_total_level >= next_attr_cfg.level and COLOR3B.GREEN or COLOR3B.RED
    	local next_pro_desc = ToColorStr(string.format("(%s/%s)", cur_total_level, next_attr_cfg.level), color)
    	self.node_list["next_level"].text.text = next_desc .. next_pro_desc
	end

    --属性列表
    self.node_list["no_attr_tip"]:SetActive(not has_attr_add)
    self.node_list["layout_cur_attr_add"]:SetActive(has_attr_add)
    self.node_list["max_attr_tip"]:SetActive(is_max_level)
    self.node_list["layout_next_attr_add"]:SetActive(not is_max_level)

    --按钮
    local can_up = next_attr_cfg and cur_total_level >= next_attr_cfg.level
    XUI.SetButtonEnabled(self.node_list["left_active_btn"], can_up)

    self.node_list["yimanji"]:SetActive(is_max_level)
    self.node_list["left_active_btn"]:SetActive(not is_max_level)
    self.node_list["remind"]:SetActive(not is_max_level and can_active)
	self.node_list["star_master_title"].text.text = Language.FairyLandEquipment.StarMaster
end

function EvolveStarMasterView:OnClickActiveBtn()
	if IsEmptyTable(self.data) then return end
	local slot = self.data.slot
	local cur_total_level = FairyLandEquipmentWGData.Instance:GetEvolveLevelAllLevel(slot)
	local next_attr_cfg = FairyLandEquipmentWGData.Instance:GetEvolveLevelTotalCfg(slot, true)
	if next_attr_cfg and cur_total_level >= next_attr_cfg.level then
		FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.ACT_TOTAL_STAR, slot, next_attr_cfg.level)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FairyLandEquipment.EvolveMasterNotEnough)
	end
end

-- 展示特效
function EvolveStarMasterView:ShowEffect(effect_type, is_success)
	if self.node_list.effect_node then
		TipWGCtrl.Instance:ShowEffect({effect_type = effect_type, is_success = is_success,
			pos = Vector2(0, 0), parent_node = self.node_list.effect_node})
	end
end
