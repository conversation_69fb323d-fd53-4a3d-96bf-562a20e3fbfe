TianShenPurchaseWGData = TianShenPurchaseWGData or BaseClass()

function TianShenPurchaseWGData:__init()
	if TianShenPurchaseWGData.Instance ~= nil then
		ErrorLog("[TianShenPurchaseWGData] attempt to create singleton twice!")
		return
	end

	TianShenPurchaseWGData.Instance = self

	self:InitCfg()
	self.grade = 1
end

function TianShenPurchaseWGData:__delete()
	TianShenPurchaseWGData.Instance = nil
	self.grade = nil
end

function TianShenPurchaseWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_tianshen_rmb_buy_auto")
	self.shop_cfg = ListToMapList(cfg.shop, "grade")
	self.other_cfg = cfg.other[1]
end

function TianShenPurchaseWGData:SetAllInfo(protocol)
	self.grade = protocol.grade
	self.shop_buy_count = protocol.shop_buy_count
end

function TianShenPurchaseWGData:GetOtherCfg()
	return self.other_cfg
end

function TianShenPurchaseWGData:GetShopIsBuyFlag(seq)
	if self.shop_buy_count then
		return self.shop_buy_count[seq] or 0
	end
end

function TianShenPurchaseWGData:GetCurShopCfg()
	local cur_grade_cfg = self.shop_cfg[self.grade] or {}
	return cur_grade_cfg
end

function TianShenPurchaseWGData:GetCurShopIsAllBuy()
	local all_buy = false
	local cur_grade_cfg = self.shop_cfg[self.grade] or {}

	if not IsEmptyTable(cur_grade_cfg) then
		for i, v in ipairs(cur_grade_cfg) do
			if self.shop_buy_count[v.seq] and self.shop_buy_count[v.seq] >= v.buy_count_limit and v.buy_type == 1 then --全套购买才等于all_buy
				all_buy = true
				break
			end
		end
	end

	return all_buy
end