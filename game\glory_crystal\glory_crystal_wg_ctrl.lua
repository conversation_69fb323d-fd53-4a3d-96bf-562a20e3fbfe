require("game/glory_crystal/new_glory_crystal_view")
require("game/glory_crystal/glory_crystal_wg_data")
require("game/glory_crystal/glory_crystal_collect_reward_view")
require("game/glory_crystal/glory_crystal_exchange_shop_view")
require("game/glory_crystal/glory_crystal_exchange_shop_item_render")
require("game/glory_crystal/glory_crystal_haoli_view")
require("game/glory_crystal/glory_crystal_daily_task")
require("game/glory_crystal/glory_crystal_purchase")
require("game/glory_crystal/glory_crystal_acc_recharge")

GloryCrystalWGCtrl = GloryCrystalWGCtrl or BaseClass(BaseWGCtrl)

function GloryCrystalWGCtrl:__init()
	if GloryCrystalWGCtrl.Instance then
		ErrorLog("[GloryCrystalWGCtrl] attempt to create singleton twice!")
		return
	end

	GloryCrystalWGCtrl.Instance = self
	self.data = GloryCrystalWGData.New()
    self.view = NewGloryCrystalView.New(GuideModuleName.GloryCrystalView)
	self.glory_crystal_exchange_shop_view = GloryCrystalExchangeShopView.New(GuideModuleName.GloryCrystalExchangeShopView)
	self.glory_crystal_collect_reward_view = GloryCrystalCollectRewardView.New()
	self.glory_crystal_haoli_view = GloryCrystalHaoLiView.New(GuideModuleName.GloryCrystalHaoLiView)

	self.again_draw = false
  
    self:RegisterAllProtocols()
    self:RegisterAllEvents()
    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function GloryCrystalWGCtrl:__delete()
	GloryCrystalWGCtrl.Instance = nil

	self:UnRegisterAllEvents()

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
	    self.data:DeleteMe()
		self.data = nil
	end

	if self.glory_crystal_collect_reward_view then
		self.glory_crystal_collect_reward_view:DeleteMe()
		self.glory_crystal_collect_reward_view = nil
	end

	if self.glory_crystal_exchange_shop_view then
		self.glory_crystal_exchange_shop_view:DeleteMe()
		self.glory_crystal_exchange_shop_view = nil
	end

	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end

	self.again_draw = nil
end

function GloryCrystalWGCtrl:MainuiOpenCreate()
	self:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.INFO)
end

function GloryCrystalWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAGloryCrystalInfo,"OnSCOAGloryCrystalInfo")
	self:RegisterProtocol(SCOAGloryCrystalDrawResult,"OnSCOAGloryCrystalDrawResult")
end

function GloryCrystalWGCtrl:UnRegisterAllEvents()
	    -- 注销活动改变监听
		if self.act_change then
			ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
			self.act_change = nil
		end
end

function GloryCrystalWGCtrl:RegisterAllEvents()
	-- 活动改变
	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
end

function GloryCrystalWGCtrl:ReqGloryCrystalInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GLORY_CRYSTAL
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function GloryCrystalWGCtrl:OnSCOAGloryCrystalInfo(protocol)
	--print_error("========荣耀水晶奖励========",protocol)
	local old_reward_state = self.data:GetAllRewardState()
	local reward_list = {}
	local reward_cfg = self.data:GetAllTimesRewardCfg()
	if not IsEmptyTable(old_reward_state) then
		for k, v in pairs(protocol.times_reward_flag) do
			if old_reward_state[k] ~= v and old_reward_state[k] == 0 and reward_cfg[k] then
				table.insert(reward_list, reward_cfg[k].item)
			end
		end
	end

	if not IsEmptyTable(reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end

	self.data:SetAllGloryCrystalInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.GloryCrystalExchangeShopView)
	ViewManager.Instance:FlushView(GuideModuleName.GloryCrystalView)
	ViewManager.Instance:FlushView(GuideModuleName.GloryCrystalHaoLiView)
	
	RemindManager.Instance:Fire(RemindName.GloryCrystal)
	RemindManager.Instance:Fire(RemindName.GloryCrystalDailyTask)
	RemindManager.Instance:Fire(RemindName.GloryCrystalPurchase)
	RemindManager.Instance:Fire(RemindName.GloryCrystalAccRecharge)

	if self.glory_crystal_collect_reward_view:IsOpen() then
		self.glory_crystal_collect_reward_view:Flush()
	end
end

function GloryCrystalWGCtrl:OnSCOAGloryCrystalDrawResult(protocol)
	--print_error("========抽奖========",protocol)
	self.data:SetResultData(protocol)
	-- ViewManager.Instance:FlushView(GuideModuleName.GloryCrystalView, nil, "play_ani")
	-- local delay_time = self.data:GetDelayTime()
	-- local baodi_item = self.data:GetBaodItem()

	ViewManager.Instance:FlushView(GuideModuleName.GloryCrystalView)

	local btn_cfg = GloryCrystalWGData.Instance:GetModeCfgByMode(protocol.mode)
    if not btn_cfg then
        return
    end

	local str = string.format(Language.GloryCrystal.BtnStr, btn_cfg.times)
    local ok_func = function ()
		ViewManager.Instance:FlushView(GuideModuleName.GloryCrystalView)
		self:ClickUse(protocol.mode, function ()
			TipWGCtrl.Instance:CloseGetCommonReward()
		end)
    end

	local open_day_cfg = GloryCrystalWGData.Instance:GetOpenDayCfg()
	local item_cfg = ItemWGData.Instance:GetItemConfig(open_day_cfg.cost_item_id)
	if IsEmptyTable(item_cfg) then
		return
	end

	local cost_item_num = btn_cfg.cost_item_num
	local has_num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id) --拥有的材料数量
	if has_num < cost_item_num then
		local other_cfg = GloryCrystalWGData.Instance:GetOtherCfg()
		cost_item_num = other_cfg.cost_score
	end

    local data_list, item_ids = self.data:CalDrawRewardList(protocol)

    local other_info = {}
    other_info.again_text = str
    other_info.stuff_id = open_day_cfg.cost_item_id
    other_info.times = btn_cfg.times
    other_info.spend = cost_item_num
	other_info.cost_type = COST_TYPE.YANYUGE_SCORE
	other_info.is_not_auti_move = true
	-- other_info.show_spend = false
    local best_data = {}
    if IsEmptyTable(item_ids) then
        best_data = nil
    else
        best_data.item_ids = item_ids
    end
    other_info.best_data = best_data

	other_info.get_skip_anim_func = function ()
		return GloryCrystalWGData.Instance:GetSkipSpineStatus()
    end
	other_info.set_skip_anim_func = function (is_skip)
		GloryCrystalWGData.Instance:SetSkipSpineStatus(is_skip)
		ViewManager.Instance:FlushView(GuideModuleName.GloryCrystalView)
    end

    TipWGCtrl.Instance:ShowGetCommonReward(data_list, ok_func, other_info)
end
	
--使用道具并弹窗
function GloryCrystalWGCtrl:ClickUse(mode_type, cb)
	local mode_cfg = self.data:GetModeCfgByMode(mode_type)
	local free_draw_times = self.data:GetFreeDrawTimes()

	if free_draw_times >= mode_cfg.times then
		self:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.DRAW, mode_type)
		self.data:CacheOrGetDrawIndex(mode_type)
		return
	end

	--数量检测
	local open_day_cfg = self.data:GetOpenDayCfg()
	local item_cfg = ItemWGData.Instance:GetItemConfig(open_day_cfg.cost_item_id)
	if IsEmptyTable(item_cfg) or not mode_cfg then
		return
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id) --拥有的材料数量
	-- local not_enough = has_num < mode_cfg.times
	-- if not_enough then
	-- 	--道具不足的弹窗
	-- 	if not self.alert then
	-- 		self.alert = Alert.New()
	-- 	end

	-- 	self.alert:ClearCheckHook()
	-- 	self.alert:SetShowCheckBox(true, "glory_crystal")
	-- 	self.alert:SetCheckBoxDefaultSelect(false)
	-- 	local func = function ()
	-- 		ViewManager.Instance:Open(GuideModuleName.GloryCrystalHaoLiView, TabIndex.glory_crystal_purchase)
	-- 	end

	-- 	local name_str =  ToColorStr(item_cfg.name, COLOR3B.C10)
	-- 	local has_num_str = ToColorStr(has_num, COLOR3B.C10)
	-- 	local str_1 = string.format(Language.Common.NotCostDrawTips, name_str)
	-- 	local str_2 = string.format(Language.Common.DrawTips_2, name_str, has_num_str)
	-- 	local str = string.format("%s\n%s", str_1, str_2)
	
	-- 	self.alert:SetLableString(str)
	-- 	self.alert:SetOkFunc(func)
	-- 	self.alert:Open()
	-- 	return
	-- end

	local cost_item_num = mode_cfg.cost_item_num
	if has_num < cost_item_num then
		local other_cfg = self.data:GetOtherCfg()
		cost_item_num = other_cfg.cost_score
		local need_score_num = cost_item_num * (mode_cfg.cost_item_num - has_num)
		if YanYuGeWGData.Instance:GetCurScore() < need_score_num then
			VipWGCtrl.Instance:OpenTipNoScore()
			-- -- 打开道具不足提示弹窗（带获取途径）
			-- local info = {}
			-- info.cb = cb
			-- TipWGCtrl.Instance:OpenTipsNoItemView(item_cfg.id, info)
			return
		end
	end

	-- 抽奖
	local tips_data = {}
	tips_data.item_id = item_cfg.id
	tips_data.is_can_gold_buy = true
	tips_data.price = cost_item_num
	tips_data.draw_count = mode_cfg.times
	tips_data.has_checkbox = true
	tips_data.checkbox_str = "glory_crystal"
	tips_data.cost_type = COST_TYPE.YANYUGE_SCORE
	-- tips_data.is_can_gold_buy = false
	-- tips_data.is_need_open_recharge_view = false
	TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, function()
		self:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.DRAW, mode_type)
		self.data:CacheOrGetDrawIndex(mode_type)
	end)
end

function GloryCrystalWGCtrl:OpenCollectRewardView()
	if not self.glory_crystal_collect_reward_view:IsOpen() then
		self.glory_crystal_collect_reward_view:Open()
	else
		self.glory_crystal_collect_reward_view:Flush()
	end
end

function GloryCrystalWGCtrl:CloseCollectRewardView()
	if self.glory_crystal_collect_reward_view:IsOpen() then
		self.glory_crystal_collect_reward_view:Close()
	end
end

-- 活动改变
function GloryCrystalWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GLORY_CRYSTAL and status == ACTIVITY_STATUS.OPEN then
		if self.view and self.view:IsOpen() then
			self.view:Flush()
			self.view:OnActivityChange()
		end

		if self.glory_crystal_haoli_view and self.glory_crystal_haoli_view:IsOpen() then
			self.glory_crystal_haoli_view:Flush()
		end
    end
end