StrangeCatalogResloveView = StrangeCatalogResloveView or BaseClass(SafeBaseView)
function StrangeCatalogResloveView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(1000, 570)})
	self:AddViewResource(0, "uis/view/strange_catalog_ui_prefab", "layout_qwtj_reslove_view")
	self.decompose_get_num = 0
end

function StrangeCatalogResloveView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.StrangeCatalog.ResloveName

	if not self.item_grid then
		self.item_grid = QWMeltingGrid.New()
		self.item_grid:SetStartZeroIndex(false)
		self.item_grid:SetIsMultiSelect(true)
		self.item_grid:CreateCells({
			col = 10,
			cell_count = 200,
			list_view = self.node_list["item_grid"],
			itemRender = MeltEquipCell,
			change_cells_num = 2,
		})
		self.item_grid:SetSelectCallBack(BindTool.Bind(self.OnSelectCell, self))
	end

	XUI.AddClickEventListener(self.node_list["btn_reslove"], BindTool.Bind(self.OnClickReslove, self))
	XUI.AddClickEventListener(self.node_list["btn_note"], BindTool.Bind(self.OnClickNote, self))
end

function StrangeCatalogResloveView:ReleaseCallBack()
	if self.item_grid then
		self.item_grid:DeleteMe()
		self.item_grid = nil
	end

	self.decompose_get_num = 0
end

function StrangeCatalogResloveView:OnFlush()
	local item_list = StrangeCatalogWGData.Instance:GetQWTJResloveBagList()
	self.item_grid:SetDataList(item_list)

	local item_id = StrangeCatalogWGData.Instance:GetDecomposeItemId()
	self.node_list.exp_icon.image:LoadSprite(ResPath.GetItem(item_id))

	local num = StrangeCatalogWGData.Instance:CalculateDecomposeGetNum(self.item_grid:GetAllSelectCell())
	self.decompose_get_num = num
	self.node_list.exp_value.text.text = num
end

function StrangeCatalogResloveView:OnClickReslove()
	local select_list = self.item_grid:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Esoterica.ResloveError)
		return
	end

	local reslove_list = {}
	for k,v in pairs(select_list) do
		reslove_list[#reslove_list + 1] = v
	end

	self.item_grid:CancleAllSelectCell()
	StrangeCatalogWGCtrl.Instance:OnHandBookDecompos(reslove_list)
end

function StrangeCatalogResloveView:OnClickNote()
	RuleTip.Instance:SetTitle(Language.StrangeCatalog.ResloveTipsTitle)
	RuleTip.Instance:SetContent(Language.StrangeCatalog.ResloveTipsContent, nil, nil, nil, true)
end

function StrangeCatalogResloveView:OnSelectCell(cell, is_select)
	local data = cell and cell:GetData()
	if IsEmptyTable(data) then
		return
	end

	local cfg_num = StrangeCatalogWGData.Instance:GetDecomposeItemGetNum(data.item_id or 0)
	local num = data.num * cfg_num
	num = is_select and num or -num
	self.decompose_get_num = self.decompose_get_num + num
	self.node_list.exp_value.text.text = self.decompose_get_num 
end

-----------------------QWMeltingGrid------------------------
QWMeltingGrid = QWMeltingGrid or BaseClass(AsyncBaseGrid)
function QWMeltingGrid:IsSelectMultiNumLimit(cell_index)
	if not self.select_tab[1][cell_index] then
		if self.cur_multi_select_num >= TianShenLingHeWGData.MAX_RESLOVE then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenLingHe.ResloveLimit)
			return true
		end
	end

	return false
end

function QWMeltingGrid:SetColorSelcet(select_color_list)
	if IsEmptyTable(select_color_list) then
		return
	end

	self.select_tab[1] = {}
	self.cur_multi_select_num = 0
	local data = self.cell_data_list
	for i = 1, self.has_data_max_index do
		if data[i] and select_color_list[data[i].color] then
			if self.cur_multi_select_num < TianShenLingHeWGData.MAX_RESLOVE then
				self.cur_multi_select_num = self.cur_multi_select_num + 1
				self.select_tab[1][i] = true
			else
				break
			end
		end
	end

	self:__DoRefreshSelectState()
end