CangJinExchangeShopPreview = CangJinExchangeShopPreview or BaseClass(SafeBaseView)

function CangJinExchangeShopPreview:__init()
	self:SetMaskBg(false, true)
	self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/cangjin_shop_prefab", "layout_exchange_limit_shop_preview")
end

function CangJinExchangeShopPreview:LoadCallBack()
    if not self.shop_grid_list then
		self.shop_grid_list = AsyncBaseGrid.New()
		self.shop_grid_list:CreateCells({col = 4,change_cells_num = 1, list_view = self.node_list["shop_grid_list"],
		assetBundle = "uis/view/cangjin_shop_prefab", assetName = "shop_preview_item",  itemRender = CangJinExchangeShopPreviewCell})
		self.shop_grid_list:SetStartZeroIndex(false)
	end
end

function CangJinExchangeShopPreview:ReleaseCallBack()
    if self.shop_grid_list then
        self.shop_grid_list:DeleteMe()
        self.shop_grid_list = nil
    end
end

function CangJinExchangeShopPreview:OnFlush()
    local shop_info = CangJinShopWGData.Instance:GetShowLimitAllShopInfo()
    if not shop_info then
        return
    end

    local show_data = {}
    for k, v in pairs(shop_info) do
		table.insert(show_data, v)
	end

	if not IsEmptyTable(show_data) then
		-- table.sort(show_data, SortTools.KeyLowerSorters("reward_type", "seq"))
        table.sort(show_data, SortTools.KeyUpperSorters("is_grand_prize", "consume_score"))
	end

	self.shop_grid_list:SetDataList(show_data)
end

CangJinExchangeShopPreviewCell = CangJinExchangeShopPreviewCell or BaseClass(BaseRender)

function CangJinExchangeShopPreviewCell:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
    end
end

function CangJinExchangeShopPreviewCell:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function CangJinExchangeShopPreviewCell:OnFlush()
    if self.data == nil then
        return
    end

    local reward_cfg = self.data.reward_item[0]
    if reward_cfg then
        self.item_cell:SetData(reward_cfg)
    end

    local item_name = ItemWGData.Instance:GetItemName(reward_cfg.item_id)
    self.node_list["item_name"].text.text = item_name
    self.node_list["score_text"].text.text = string.format(Language.CangJinShopView.JiFen, self.data.consume_score)

    local discount = self.data.discount
    if discount and discount ~= "" then
        self.node_list.discount_text.text.text = discount
        self.node_list.discount:CustomSetActive(true)
    else
        self.node_list.discount:CustomSetActive(false)
    end

    local is_grand_prize = self.data.is_grand_prize
    self.node_list.bg_hl:CustomSetActive(is_grand_prize and is_grand_prize == 1)
end