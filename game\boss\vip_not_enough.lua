
VipNotEnough = VipNotEnough or BaseClass(SafeBaseView)

function VipNotEnough:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal

    self.view_name = "VipNotEnough"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(694, 440)})
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_vip_not_enough")
end

function VipNotEnough:__delete()

end

function VipNotEnough:ReleaseCallBack()
    if self.click_event then
        self.click_event = nil
   end
end

function VipNotEnough:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.node_list.title_view_name.text.text = Language.Boss.Tishi
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.OnClickOk, self))
end

function VipNotEnough:SetData(data)
    self.data = data
	self:Flush()
end

function VipNotEnough:OnFlush()
    if not self.data then 
        return
    end
    self.node_list.rich_dialog.tmp.text = self.data.tip
    self.click_event =  self.data.click_ok
end

function VipNotEnough:OnClickOk()
   if self.click_event then
        self.click_event()   
   end
   self:Close()
end

