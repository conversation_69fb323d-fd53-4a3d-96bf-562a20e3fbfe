LongZhuTip = LongZhuTip or BaseClass(SafeBaseView)

function LongZhuTip:__init(view_name)
	self.view_name = "LongZhuTip"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/long_zhu_ui_prefab", "longzhu_tip_view")
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self:SetMaskBg(true, true)
end

function LongZhuTip:LoadCallBack()
	self:InitPanel()
end

function LongZhuTip:ReleaseCallBack()
	if self.base_tip then
		self.base_tip:DeleteMe()
		self.base_tip = nil
	end

	if self.now_skill_list then
		for k, v in pairs(self.now_skill_list) do
			v:DeleteMe()
		end
		self.now_skill_list = nil
	end

	if self.nex_skill_list then
		for k, v in pairs(self.nex_skill_list) do
			v:DeleteMe()
		end
		self.nex_skill_list = nil
	end

	self.next_desc_label_list = nil
end

function LongZhuTip:OnFlush(param_t)
	self:FlushLongZhuBaseInfo()
	self:FlushLongZhuAttr()
	self:FlushLongZhuSkill()
	self:FlushLongZhuSkillCapValue()
end

function LongZhuTip:InitPanel()
	self.base_tip = BaseTip.New(self.node_list.tip_root)

	-- 龙珠界面打开时候只显示左边
	local is_open = false
	if ViewManager.Instance:IsOpen(GuideModuleName.LongZhuView) or ViewManager.Instance:IsOpen(GuideModuleName.GoldStoneView) then
		is_open = true
	end

	self.base_tip:SetVisible(not is_open)
	local desc_list = {}
	for i=1,8 do
		desc_list[i] = self.node_list["nex_desc_" .. i]
	end
	self.next_desc_label_list = desc_list

	if self.now_skill_list == nil and self.nex_skill_list == nil then
		self.now_skill_list = {}
		self.nex_skill_list = {}

		for i = 1, 8 do
			self.now_skill_list[i] = LongZhuSkillDescRender.New(self.node_list.now_desc_root:FindObj("now_desc_" .. i))
			self.nex_skill_list[i] = LongZhuSkillDescRender.New(self.node_list.nex_desc_root:FindObj("nex_desc_" .. i))
		end
	end
end

function LongZhuTip:OnClickActiveBtn()
	ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.rolebag_longzhu)
	self:Close()
end

function LongZhuTip:FlushLongZhuBaseInfo()
	-- 龙珠界面打开时候只显示左边
	local is_open = false
	if ViewManager.Instance:IsOpen(GuideModuleName.LongZhuView) or ViewManager.Instance:IsOpen(GuideModuleName.GoldStoneView) then
		is_open = true
	end
	self.base_tip:SetVisible(not is_open)

	if is_open then
		return
	end

	self.base_tip:Reset()

	local tip_item_id = LongZhuWGData.Instance:GetOtherCfg("tip_show_item_id") or 0
	local item_cfg = ItemWGData.Instance:GetItemConfig(tip_item_id)
	if item_cfg then
		local name = item_cfg.name
		self.base_tip:SetItemName(ToColorStr(name, COLOR3B.ORANGE))
	end
	self.base_tip:SetTopColorBg(item_cfg.color)
	self.base_tip:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)

	local is_red = LongZhuWGData.Instance:IsShowLongZhuRedPoint() and 1 or 0
	local btn_info = {btn_name = Language.LongZhu.ActiveTipStr, btn_click = BindTool.Bind1(self.OnClickActiveBtn, self), btn_red = is_red}
	self.base_tip:SetBtnsClick({btn_info})

	local item_cell = self.base_tip:GetItemCell()
	item_cell:SetData({item_id = tip_item_id})

	-- local type_str = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.LongZhu.TypeStr)
	-- self.base_tip:SetEquipSocre(type_str)
	self.base_tip:SetLongZhuTopColorBg()
end

function LongZhuTip:FlushLongZhuAttr()
	local now_cfg = nil
	local nex_cfg = nil
	local level = 0
	local zhanli = 0
	local info_list = {}
	
	for id = 1, LONGZHU_TYPE_MAX do
		level = LongZhuWGData.Instance:GetLongZhuLevelById(id)
		now_cfg = LongZhuWGData.Instance:GetLongZhuCfg(id, level)
		nex_cfg = LongZhuWGData.Instance:GetLongZhuCfg(id, level + 1)
		if now_cfg or nex_cfg then
			local cfg = now_cfg or nex_cfg
			local attr_list, cap_value = LongZhuWGData.Instance:GetLongZhuAttrList(cfg, level > 0)
			local attr_desc = LongZhuWGData.Instance:GetLongZhuAttrStr(attr_list)

			local temp = {title = "", desc = ""}
			temp.title = cfg.name .. " "

			if level <= 0 then
				temp.title = temp.title .. ToColorStr(Language.LongZhu.NoActiveTipStr, COLOR3B.PINK)
			elseif nex_cfg then
				temp.title = temp.title .. string.format("LV.%d", level)
			else
				temp.title = temp.title .. ToColorStr(Language.Skill.YiManJi, COLOR3B.DEFAULT_NUM)
			end

			zhanli = zhanli + cap_value

			temp.desc = attr_desc
			info_list[#info_list + 1] = temp
		end
	end

	self.base_tip:SetItemDesc(info_list)
	self.base_tip:SetCapabilityPanel({capability = zhanli})
end

function LongZhuTip:FlushLongZhuSkill()
	local active_num = LongZhuWGData.Instance:GetLongZhuActiveNum()
	---[[ 当前等级标题
	if active_num >= LONGZHU_TYPE_MAX then
		local avg_level = LongZhuWGData.Instance:GetLongZhuAvgSkillLevel()
		local temp_str = string.format("(%d)", avg_level)
		temp_str = ToColorStr(temp_str, COLOR3B.DEFAULT_NUM)
		self.node_list.title_label.tmp.text = string.format(Language.LongZhu.LevelDesc, temp_str)
	else
		local temp_str = string.format("(%d/%d)", active_num, LONGZHU_TYPE_MAX)
		temp_str = ToColorStr(temp_str, COLOR3B.DEFAULT_NUM)
		self.node_list.title_label.tmp.text = string.format(Language.LongZhu.ActiveAll, temp_str)
	end
	--]]

	local longzhu_level = LongZhuWGData.Instance:GetLongZhuSkillLevel()
	local now_cfg_list, nex_cfg_list = LongZhuWGData.Instance:GetLongZhuSkillDescList()

	---[[ 描述解析(变颜色)
	local now_desc_list = {}
	local nex_desc_list = {}
	local index = 0
	local temp_level = longzhu_level > 0 and longzhu_level or 1
	local cur_skill_desc, nex_skill_desc = "", ""
	for k,v in pairs(now_cfg_list) do
		if v.skill_type > 1 then
			local now_data = {}
			local nex_data = {}
			if temp_level >= v.active_skill_level then
				index = k
				now_data.status = 2
				now_data.desc = v.skill_desc
				now_desc_list[index] = now_data
			else
				-- 未激活
				index = k + 100
				now_data.status = 0
				local desc = string.gsub(v.skill_desc, "<.->", "")
				now_data.desc = ToColorStr(desc, TIPS_COLOR.TITLE)
				now_desc_list[index] = now_data
			end
	
			if nex_cfg_list[k] then
				-- 下级效果
				local desc = nex_cfg_list[k].skill_desc
				desc = ToColorStr(desc, COLOR3B.D_GLOD)
				desc = desc .. "[230]"
				nex_data.status = 1
				nex_data.desc = desc
				nex_desc_list[index] = nex_data
			else
				nex_data.status = now_desc_list[index].status
				nex_data.desc = now_desc_list[index].desc
				nex_desc_list[index] = nex_data
			end
		else
			if temp_level >= v.active_skill_level then
				cur_skill_desc = v.skill_desc
			else
				local desc = string.gsub(v.skill_desc, "<.->", "")
				cur_skill_desc = ToColorStr(desc, TIPS_COLOR.ATTR_NAME)
			end

			if nex_cfg_list[k] then
				-- 下级效果
				local desc = nex_cfg_list[k].skill_desc
				desc = ToColorStr(desc, COLOR3B.D_GLOD)
				desc = desc .. "[230]"
				nex_skill_desc = desc
			else
				nex_skill_desc = cur_skill_desc
			end
		end



		
	end
	now_desc_list = SortTableKey(now_desc_list)
	nex_desc_list = SortTableKey(nex_desc_list)
	--]]


	---[[ 当前等级描述
	if longzhu_level > 0 then
		self.node_list.now_desc_title.tmp.text = Language.LongZhu.NowStr
		self.node_list.now_effect_desc.tmp.text = self:GetConditionDesc()
		self.node_list.now_skill_desc.tmp.text = cur_skill_desc
		for k,v in ipairs(self.now_skill_list) do
			v:SetData(now_desc_list[k])
		end
	end
	self.node_list.now_desc_root:SetActive(longzhu_level > 0)
	--]]


	-- 下一等级描述
	local max_level = LongZhuWGData.Instance:GetMaxLongZhuSkillLevel()
	self.node_list.max_level_root:SetActive(longzhu_level >= max_level)
	self.node_list.nex_desc_root:SetActive(longzhu_level < max_level)
	if longzhu_level >= max_level then
		return
	end

	local next_condition = self:GetConditionDesc(true)
	self.node_list.nex_desc_title.tmp.text = longzhu_level > 0 and Language.LongZhu.NextStr or Language.LongZhu.ActiveStr
	self.node_list.nex_effect_desc.tmp.text = self:GetConditionDesc(true)
	self.node_list.nex_skill_desc.tmp.text = nex_skill_desc
	for k,v in ipairs(self.nex_skill_list) do
		v:SetData(nex_desc_list[k])
	end
end

function LongZhuTip:GetConditionDesc(is_next)
	local next_condition = ""
	local longzhu_level = LongZhuWGData.Instance:GetLongZhuSkillLevel()
	if longzhu_level >= LONGZHU_TYPE_MAX then
		local avg_level = LongZhuWGData.Instance:GetLongZhuAvgSkillLevel()
		if is_next then
			avg_level = avg_level + 1
		end
		next_condition = string.format(Language.LongZhu.LongZhuDesc_level, avg_level)
	else
		if is_next then
			longzhu_level = longzhu_level + 1
		end
		next_condition = string.format(Language.LongZhu.LongZhuDesc_num, longzhu_level)
	end

	next_condition = ToColorStr(next_condition, COLOR3B.DEFAULT_NUM)
	return next_condition
end

function LongZhuTip:FlushLongZhuSkillCapValue()
	local longzhu_level = LongZhuWGData.Instance:GetLongZhuSkillLevel()
	if longzhu_level > 0 then
		local skill_id = LongZhuWGData.Instance:GetOtherCfg("skill_id")
		local skill_cfg = SkillWGData.Instance:GetOtherSkillCfg(skill_id, longzhu_level)
		local cap_value = AttributeMgr.GetCapability(nil, skill_cfg)
		self.node_list.skill_cap_value.tmp.text = cap_value
	else
		self.node_list.skill_cap_value.tmp.text = 0
	end
end


LongZhuSkillDescRender = LongZhuSkillDescRender or BaseClass(BaseRender)
function LongZhuSkillDescRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	self.view:SetActive(true)
	local bundle, asset = ResPath.GetLongZhuImage(self.data.status > 0 and "a2_js_tmlb_jhd" or "a2_js_tmlb_wjhd")
	self.node_list.icon.image:LoadSprite(bundle, asset)
	self.node_list.desc.tmp.text = self.data.desc
end