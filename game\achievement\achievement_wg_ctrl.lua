require("game/achievement/achievement_xunzhang_view")
require("game/achievement/achievement_wg_data")
require("game/achievement/achievement_tip_view")

AchievementWGCtrl = AchievementWGCtrl or BaseClass(BaseWGCtrl)
function AchievementWGCtrl:__init()
	if AchievementWGCtrl.Instance ~= nil then
		ErrorLog("[AchievementWGCtrl] Attemp to create a singleton twice !")
	end
	AchievementWGCtrl.Instance = self

	self.view = AchievementView.New()
	self.data = AchievementWGData.New()
	self.tip_view = AchievementTipiew.New()
	self.OneBtnReceive = false
	self:RegisterAllProtocols()
end

function AchievementWGCtrl:__delete()
	AchievementWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil
	self.data:DeleteMe()
	self.data = nil

	if self.tip_view ~= nil then
		self.tip_view:DeleteMe()
		self.tip_view = nil
	end
	self.is_remind = nil
end

function AchievementWGCtrl:Open(tab_index, param_t)
	self.view:Open(tab_index, param_t)
end

function AchievementWGCtrl:FlushAchievementView(index, key, value)
	self.view:Flush(index, key, value)
end

function AchievementWGCtrl:ViewIsOpen()
	return self.view:IsOpen()
end

function AchievementWGCtrl:OpenTipView(data)
	if self.next_time_show_data then
		table.insert(self.next_time_show_data, data)
		return
	end

	self.tip_view:SetDataList(data)
	if not self.tip_view:IsOpen() then
		self.tip_view:Open()
	end
end

function AchievementWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCAchievementAllInfo, "OnSCAchievementAllInfo")
	self:RegisterProtocol(SCAchievementInfoChange, "OnSCAchievementInfoChange")
	self:RegisterProtocol(CSAchievementFetchReward)
end

function AchievementWGCtrl:OnSCAchievementAllInfo(protocol)
	self.data:SetAllAchievementInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.RoleView)
	RemindManager.Instance:Fire(RemindName.Achievement)
	RemindManager.Instance:Fire(RemindName.AchievementTotal)
	RemindManager.Instance:Fire(RemindName.AchievementParentTable)
end

function AchievementWGCtrl:OnSCAchievementInfoChange(protocol)
	self.data:ChangeAchievementInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.Achievement)
	RemindManager.Instance:Fire(RemindName.AchievementTotal)
	RemindManager.Instance:Fire(RemindName.AchievementParentTable)
end

--发送领取成就操作协议
function AchievementWGCtrl:SendCSAchievementFetchReward(achievement_id, is_achievement_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSAchievementFetchReward)
	protocol.achievement_id = achievement_id or 0
	protocol.is_achievement_id = is_achievement_id or 1 -- 区分一键（0 一键 ， 1 单个）
	protocol:EncodeAndSend()
end

--打开相对应成就面板
function AchievementWGCtrl:TipViewToOpenView(type_sort, client_sort)
	self.data:SetTipViewToOpenViewType(type_sort, client_sort, true)
	self.view:Open(ACHIEVEMENT_TAB_TYPE.CJ)
	RemindManager.Instance:Fire(RemindName.Achievement)
	RemindManager.Instance:Fire(RemindName.AchievementTotal)
	RemindManager.Instance:Fire(RemindName.AchievementParentTable) --成就侧边按钮
end

function AchievementWGCtrl:CheckAchievementRemind(remind_id)

end

--生成成就主界面按钮
function AchievementWGCtrl:CreatMainUiGuildBtn(mainbtn_type, table_index, is_creat)
	if is_creat == 0 then
		MainuiWGCtrl.Instance:InvateTip(mainbtn_type, 0)
	else
		MainuiWGCtrl.Instance:InvateTip(mainbtn_type, 1, function()
			FunOpen.Instance:OpenViewByName(GuideModuleName.Achievement, table_index)
			return true
		end)
	end
end

function AchievementWGCtrl:CheckAchievementTipIsOpen()
	if self.tip_view:IsOpen() then
		return true
	end
	return false
end

function AchievementWGCtrl:StopShowAchievementTip(new_open_view_func)
	if self.tip_view:IsOpen() then
		self.tip_view:StopNextShow(new_open_view_func)
	elseif new_open_view_func then
		new_open_view_func()
	end
end

function AchievementWGCtrl:SaveNextNeedShowData(data)
	self.next_time_show_data = data
end

function AchievementWGCtrl:ContinueShowAchieveTip()
	if self.next_time_show_data and not IsEmptyTable(self.next_time_show_data) then
		for k, v in pairs(self.next_time_show_data) do
			self:ContinueOpenTipView(v)
		end
		self.next_time_show_data = nil
	end
end

function AchievementWGCtrl:ContinueOpenTipView(data)
	self.tip_view:SetDataList(data)
	if not self.tip_view:IsOpen() then
		self.tip_view:Open()
	end
end
