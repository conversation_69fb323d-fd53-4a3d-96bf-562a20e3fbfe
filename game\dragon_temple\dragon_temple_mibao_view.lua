function DragonTempleView:LoadIndexCallBackMiBao()
	if not self.mibao_sever_draw_list then
		self.mibao_sever_draw_list = AsyncListView.New(MibaoSeverDrawListItemRender, self.node_list.mibao_sever_draw_list)
	end

	if not self.mibao_person_draw_list then
		self.mibao_person_draw_list = AsyncListView.New(MibaoPersonDrawListItemRender, self.node_list.mibao_person_draw_list)
	end

	if not self.mibao_gift_list then
		self.mibao_gift_list = AsyncListView.New(MibaoGiftItemRender, self.node_list.mibao_gift_list)
		self.mibao_gift_list:SetStartZeroIndex(false)
		self.mibao_gift_list:SetSelectCallBack(BindTool.Bind1(self.MiBaoGiftSelectCallBack,self))
	end

	if not self.mibao_display then
		self.mibao_display = OperationActRender.New(self.node_list.mibao_display)
		self.mibao_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.mibao_reward_list then
		self.mibao_reward_list = AsyncListView.New(ItemCell ,self.node_list.mibao_reward_list)
		self.mibao_reward_list:SetStartZeroIndex(false)
	end

	self.mibao_select_gift_id = -1
	self.mibao_select_gift_level = -1

    XUI.AddClickEventListener(self.node_list.mibao_see_more_btn, BindTool.Bind1(self.OnMiBaoSeeMoreBtnClick, self))
	XUI.AddClickEventListener(self.node_list.mibao_draw_btn1, BindTool.Bind2(self.OnMiBaoDrawBtnClick, self, 0))
	XUI.AddClickEventListener(self.node_list.mibao_draw_btn2, BindTool.Bind2(self.OnMiBaoDrawBtnClick, self, 1))
	XUI.AddClickEventListener(self.node_list.probability_show_btn, BindTool.Bind(self.OpenGaiLvView, self))

	local draw_mode_cfg = DragonTempleWGData.Instance:GetDrawModelCfg()
	if not IsEmptyTable(draw_mode_cfg) then
		for k, v in pairs(draw_mode_cfg) do
			self.node_list["mibao_draw_btn_text" .. k].text.text = string.format(Language.DragonTemple.RankDrawTime, v.times)
		end
	end
end

function DragonTempleView:ShowIndexCallBackMiBao()
	DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.DRAW_RECORD)

	self:DokMiBaoViewAnim()
end

function DragonTempleView:ReleaseMiBao()
	if self.mibao_sever_draw_list then
		self.mibao_sever_draw_list:DeleteMe()
		self.mibao_sever_draw_list = nil
	end

	if self.mibao_person_draw_list then
		self.mibao_person_draw_list:DeleteMe()
		self.mibao_person_draw_list = nil
	end

	if self.mibao_display then
		self.mibao_display:DeleteMe()
		self.mibao_display = nil
	end

	if self.mibao_gift_list then
		self.mibao_gift_list:DeleteMe()
		self.mibao_gift_list = nil
	end

	if self.mibao_reward_list then
		self.mibao_reward_list:DeleteMe()
		self.mibao_reward_list = nil
	end
end

function DragonTempleView:MiBaoGiftSelectCallBack(item)
	if IsEmptyTable(item.data) then
		return 
	end

	if item.index == self.mibao_select_gift_id then
		return
	end

	self.mibao_select_gift_id = item.index
	self.mibao_select_gift_level = item.data.level

	self:FlushMiBaoReward()
	self:FlushMiBaoModel()
end

function DragonTempleView:OnFlushMiBao()
	local draw_time = DragonTempleWGData.Instance:GetMiBaoDrawTime()
	local draw_mode_cfg = DragonTempleWGData.Instance:GetDrawModelCfg()
	local data_list = DragonTempleWGData.Instance:GetMiBaoActiveDrawPoolDataList()
	self.node_list.mibao_desc.text.text = string.format(Language.DragonTemple.MiBaoDrawTime, draw_time)
	self.mibao_gift_list:SetDataList(data_list)

	for i = 1, 2 do
		local time = ((draw_mode_cfg or {})[i] or {}).times or 0
		self.node_list["mibao_draw_btn_remind" .. i]:SetActive(draw_time >= time)
	end

	self:FlushMiBaoReward()
	self:FlushMiBaoModel()
end

function DragonTempleView:FlushMiBaoPersonRecordList()
	local data_list = DragonTempleWGData.Instance:GetDrawRecordInfoByType(DRAGON_TEMPLE_OPERATE_DRAW_TYPE.PERSON)
	local no_data = IsEmptyTable(data_list)
	self.mibao_person_draw_list:SetDataList(data_list)
	self.node_list.not_mibao_person_draw_info:SetActive(no_data)

end

function DragonTempleView:FlushMiBaoServerRecordList()
	local data_list = DragonTempleWGData.Instance:GetDrawRecordInfoByType(DRAGON_TEMPLE_OPERATE_DRAW_TYPE.SERVER)
	local no_data = IsEmptyTable(data_list)
	self.node_list.no_mibao_sever_draw_info:SetActive(no_data)
	self.mibao_sever_draw_list:SetDataList(data_list)
end

function DragonTempleView:FlushMiBaoReward()
	local reward_data_list = DragonTempleWGData.Instance:GetMiBaoRewardByLevel(self.mibao_select_gift_level)

	if not IsEmptyTable(reward_data_list) then
		self.mibao_reward_list:SetDataList(reward_data_list)	
	end
end

function DragonTempleView:FlushMiBaoModel(level)
    local model_cfg = DragonTempleWGData.Instance:GetMiBaoDrawPoolShowModelDataByLevel(self.mibao_select_gift_level)

	if IsEmptyTable(model_cfg) then
		return
	end

    local display_data = {}
	if model_cfg["model_show_itemid"] ~= 0 and model_cfg["model_show_itemid"] ~= "" then
		local split_list = string.split(model_cfg["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = model_cfg["model_bundle_name"]
	display_data.asset_name = model_cfg["model_asset_name"]
	local model_show_type = tonumber(model_cfg["model_show_type"]) or 1
	display_data.render_type = model_show_type - 1
	display_data.model_click_func = function ()
		TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
	end

	self.mibao_display:SetData(display_data)
	local scale = model_cfg["display_scale"]
	Transform.SetLocalScaleXYZ(self.node_list["mibao_display"].transform, scale, scale, scale)
end

function DragonTempleView:OnMiBaoSeeMoreBtnClick()
	if self.mibao_select_gift_level < 0 then
		return
	end
 
	DragonTempleWGData.Instance:SetMiBaoShowRewardId(self.mibao_select_gift_level)
	DragonTempleWGCtrl.Instance:OpenMiBaoShowRewardView()
end

function DragonTempleView:OnMiBaoDrawBtnClick(draw_type)
	if self.mibao_select_gift_level < 0 then
		return
	end

	local draw_time = DragonTempleWGData.Instance:GetMiBaoDrawTime()
	local draw_cfg = DragonTempleWGData.Instance:GetDrawModelCfg()
	local can_draw = false

	for k, v in pairs(draw_cfg) do
		if v.mode == draw_type then
			can_draw = v.times <= draw_time
			break
		end
	end

	if can_draw then
		DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.DRAW, draw_type, self.mibao_select_gift_level)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.MiBaoDrawTimeNotEnough)
	end
end

function DragonTempleView:DokMiBaoViewAnim()
    local tween_info = UITween_CONSTS.DragonTemple
    RectTransform.SetAnchoredPositionXY(self.node_list.mibao_right_root.rect, 600, 0)

    self.node_list.mibao_right_root.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
end

function DragonTempleView:OpenGaiLvView()
    local info = DragonTempleWGData.Instance:GetGaiLvInfo(self.mibao_select_gift_id)
    TipWGCtrl.Instance:OpenGaiLvShowView(info)
end

MibaoSeverDrawListItemRender = MibaoSeverDrawListItemRender or BaseClass(BaseRender)
function MibaoSeverDrawListItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local reward_pool_level = self.data.reward_pool_level
	local reward_cfg = DragonTempleWGData.Instance:GetMiBaoDrawPoolShowModelDataByLevel(reward_pool_level)

	if not IsEmptyTable(reward_cfg) then
		local reward_name = reward_cfg.reward_name
		local item_id = self.data.item_id
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		local item_color = item_cfg and item_cfg.color or 0
		self.node_list.draw_text.text.text = string.format(Language.DragonTemple.MiBaoServerRecord, self.data.name, reward_name, ITEM_COLOR[item_color], item_cfg.name, self.data.num)
	end
end

MibaoPersonDrawListItemRender = MibaoPersonDrawListItemRender or BaseClass(BaseRender)
function MibaoPersonDrawListItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local reward_pool_level = self.data.reward_pool_level
	local reward_cfg = DragonTempleWGData.Instance:GetMiBaoDrawPoolShowModelDataByLevel(reward_pool_level)

	if not IsEmptyTable(reward_cfg) then
		local reward_name = reward_cfg.reward_name
		local item_id = self.data.item_id
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		local item_color = item_cfg and item_cfg.color or 0
		self.node_list.draw_text.text.text = string.format(Language.DragonTemple.MiBaoPersonRecord, reward_name, ITEM_COLOR[item_color], item_cfg.name, self.data.num)
	end
end

MibaoGiftItemRender = MibaoGiftItemRender or BaseClass(BaseRender)
function MibaoGiftItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local cfg = DragonTempleWGData.Instance:GetMiBaoDrawPoolShowModelDataByLevel(self.data.level)
	if not IsEmptyTable(cfg) then
		self.node_list.name.text.text = cfg.reward_name
		self.node_list.select_name.text.text = cfg.reward_name
	end

	local bundle, asset = ResPath.GetDragonTempleImg("a2_lsd_btn_bg_" .. self.index)
	self.node_list.icon_bg.image:LoadSprite(bundle, asset, function()
        self.node_list.icon_bg.image:SetNativeSize()
    end)

	local bundle, asset = ResPath.GetDragonTempleImg("a2_lsd_btn_bx" .. self.index)
	self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)
end

function MibaoGiftItemRender:OnSelectChange(is_select)
	self.node_list["select"]:SetActive(is_select)
	self.node_list["icon_bg"]:SetActive(not is_select)
	self.node_list["name_bg"]:SetActive(not is_select)
	self.node_list["icon"]:SetActive(is_select)
end