--帮派每日奖励领取界面
GuildDayRewardView = GuildDayRewardView or BaseClass(SafeBaseView)
function GuildDayRewardView:__init()
	self:SetMaskBg(true,true,false)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_reward_panel")
end

function GuildDayRewardView:__delete()

end

function GuildDayRewardView:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GuildDayRewardView:LoadCallBack()
	local item_config = ItemWGData.Instance:GetItemConfig(self.item_id)
	self.item_cell = ItemCell.New()
	local data ={}
	data.item_id = self.item_id
	self.item_cell:SetInstanceParent(self.node_list.ph_cell_2)
	self.item_cell:SetData(data)
	--以防为空出现报错
	if item_config and item_config.name then
		self.node_list.cell_text_2.text.text = item_config.name
	end
end

function GuildDayRewardView:SetRewardData(item_id)
	self.item_id = item_id
end