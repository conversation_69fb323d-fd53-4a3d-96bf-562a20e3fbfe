------------------------ 参考规则 ---------------------------------
--1.新增单个通用颜色添加到COLOR3B中
--2.新增一组颜色，可参考MAIN_VIEW_COLOR
------------------------------------------------------------------
COLOR3B = {
	----------------------【A3】-----------------------------
	C1 = "#a84b05",  -- 亮底通用色
	C2 = "#3c8652",  -- 亮底通用色
	C3 = "#a93c3c",  -- 亮底通用色
	C4 = "#cbfbfd",  -- 通用色值
	C5 = "#4a3908",  -- 通用色值(按钮)
	C6 = "#a7d2eb",  -- 通用色值(大标题)
	C7 = "#fff8bb",  -- 通用色值(小标题)
	C8 = "#6cff95",  -- 通用色值(重点色)
	C9 = "#ffe58c",  -- 通用色值(重点色)
	C10 = "#ff7373", -- 通用色值(提示色)
	C11 = "#67c8ff", -- 通用色值(提示色)
	C12 = "#999999", -- 通用色值(提示色)
	C13 = "#ffffff", -- 通用色值(文本信息)
	C21 = "#1b3654", -- 常规按钮
	C22 = "#452b1f", -- 活动按钮
	C23 = "#2b3044", -- 通用色值(文本信息)
	C24 = "#4e545f", -- 通用色值(比C23浅)
	C25 = "#5b4b46", -- 通用色值(比C26浅)
	C26 = "#5c3c2a", -- 通用色值(文本信息)
	C35 = "#a26756", -- 通用色值(活动or充值文本信息)

	--品质色.
	--暗底.
	A1 = "#79fa82", -- 品质绿色(暗底)
	A2 = "#79b9fa", -- 品质蓝色(暗底)
	A3 = "#cb79fa", -- 品质紫色(暗底)
	A4 = "#fab379", -- 品质橙色(暗底)
	A5 = "#f97878", -- 品质红色(暗底)
	A6 = "#fa79bf", -- 品质粉色(暗底)
	A7 = "#fabf79", -- 品质金色(暗底)
	A8 = "#fa7994", -- 品质幻彩(暗底)
	A9 = "#ffe58c", -- 品质耀金(亮底)
	A10 = "#99ffbb", -- 品质玄青(亮底)
	--亮底.
	L1 = "#499a4f", -- 品质绿色(亮底)
	L2 = "#5986b4", -- 品质蓝色(亮底)
	L3 = "#9166aa", -- 品质紫色(亮底)
	L4 = "#bf824e", -- 品质橙色(亮底)
	L5 = "#da6868", -- 品质红色(亮底)
	L6 = "#de61a4", -- 品质粉色(亮底)
	L7 = "#d8983c", -- 品质金色(亮底)
	L8 = "#e6617e", -- 品质幻彩(亮底)
	L9 = "#d4a500", -- 品质耀金(亮底)
	L10 = "#12a774", -- 品质玄青(亮底)

	-- 暗底色
	WHITE 			= "#ffffff",				-- 白
	BLACK 			= "#001020",				-- 通用色值黑
	BLUE_TITLE		= "#a7d2eb",				-- 通用标题色 (大标题)
	GLOD_TITLE      = "#fff8bb",                -- 通用标题色 (小标题)
	DEFAULT 		= "#cbfbfd",				-- 通用色值
	DEFAULT_NUM 	= "#99ffbb",				-- 数字色

	GREEN           = "#99ffbb",                -- 通用绿色
	GLOD            = "#ffe58c",                -- 通用色值 (重点色)
	RED             = "#ff9292",                -- 通用色值 (提示色)
	BLUE            = "#66c7ff", 				-- 通用色值 (提示色)
	GRAY            = "#999999", 				-- 通用色值 (置灰色)
	
	PURPLE 			= "#cb79fa",				-- 紫
	ORANGE 			= "#fab379",				-- 橙
	PINK 			= "#fa79bf",				-- 粉
	DAZZLING 		= "#fa7994",				-- 炫彩

	D_GREEN 		= "#79fa82",				-- 绿
	D_BLUE 			= "#79b9fa",				-- 蓝
	D_PURPLE 		= "#cb79fa",				-- 紫
	D_ORANGE 		= "#fab379",				-- 橙
	D_RED 			= "#f97878",				-- 红
	D_PINK 			= "#fa79bf",				-- 粉
	D_GLOD 			= "#fabf79",				-- 金
	D_DAZZLING	 	= "#fa7994",				-- 炫彩

	-- 亮底色
	L_ORANGE        = "#a84b05",                -- 亮底通用橘色
	L_GREEN         = "#3c8652",                -- 亮底通用绿色 
	L_RED           = "#a93c3c",                -- 亮底通用红色 

	-- 旧的
	L_DEFAULT		= "#cbfbfd",				-- 通用色
	L_PINK 			= "#fa79bf",				-- 闷骚粉色
	L_GLOD 			= "#ffe58c",				-- 金
	L_DAZZLING	 	= "#fa7994",				-- 炫彩
	L_GRAY 			= "#999999",				-- 灰色
	L_COLOUR        = "#fa7994",                -- 彩色
	L_BLUE 			= "#66c7ff",				-- 蓝
	L_PURPLE 		= "#cb79fa",				-- 紫

	-- 品质色
	QUALITY_GREEN   = "#79fa82",                -- 品质绿色
	QUALITY_BLUE    = "#79b9fa", 				-- 品质蓝色
	QUALITY_PURPLE  = "#cb79fa", 				-- 品质紫色
	QUALITY_ORANGE  = "#fab379", 				-- 品质橙色
	QUALITY_RED     = "#f97878", 				-- 品质红色
	QUALITY_PINK    = "#fa79bf", 				-- 品质粉色
	QUALITY_GLOD    = "#fabf79", 				-- 品质金色
	QUALITY_COLOUR  = "#fa7994", 				-- 品质炫彩
	QUALITY_SHINING_GOLD = "#ffe58c",			-- 品质耀金
	QUALITY_XUAN_QING = "#99ffbb",				-- 品质玄青
	ROLE_NAME_COLOR = "#baffaf",				-- 主角人物头顶名字
	YELLOW 			= "#ffff00",				-- 黄

	BEAST_UP 		= "#fadd79",				-- 幻兽UP
	BEAST_USP 		= "#1d4662",				-- 幻兽USP
}

TIPS_COLOR = {
	SOCRE = COLOR3B.WHITE,			-- 评分颜色
	ATTR_TITLE = COLOR3B.GLOD,		-- 属性类型颜色
	ATTR_NAME = COLOR3B.GRAY,		-- 属性名字颜色 
	ATTR_VALUE = COLOR3B.WHITE,		-- 属性值颜色
	GETWAY_VALUE = COLOR3B.BLUE,	-- 获得途径颜色
}

-- 特殊属性颜色
SPECIAL_ATTR_COLOR = {
	[171] = COLOR3B.PINK, -- boss真伤
	[172] = COLOR3B.PINK, -- boss麻痹
	[173] = COLOR3B.GLOD, -- boss秒杀
	["boss_zhenshang"] = COLOR3B.PINK, -- boss真伤
	["boss_palsy_per"] = COLOR3B.PINK, -- boss麻痹
	["boss_seckill_per"] = COLOR3B.GLOD, -- boss秒杀
	["tianshen_att_per"] = "#FFE58C",
	["tianshen_shield"] = "#FFE58C",
}

-- 格子的数量描述颜色
ITEM_NUM_COLOR = {
	DEFAULT = COLOR3B.WHITE,		-- 默认数量描述
	ENOUGH = COLOR3B.GREEN,				-- 数量足够 (绿)(策划说绿色全用这个)
	NOT_ENOUGH = COLOR3B.RED,			-- 数量不够（红）
}

-- 是否达标 战力或等级等条件是否达成 目前用于副本界面
IS_CAN_COLOR = {
	ENOUGH = "#99ffbb",				-- 达标 (绿)
	NOT_ENOUGH = "#ff9292",			-- 不达标（红）
}

ITEM_COLOR = {
	[GameEnum.ITEM_COLOR_WHITE] = COLOR3B.WHITE,	-- 白
	[GameEnum.ITEM_COLOR_GREEN] = COLOR3B.QUALITY_GREEN,	-- 绿
	[GameEnum.ITEM_COLOR_BLUE] = COLOR3B.QUALITY_BLUE,		-- 蓝
	[GameEnum.ITEM_COLOR_PURPLE] = COLOR3B.QUALITY_PURPLE,	-- 紫
	[GameEnum.ITEM_COLOR_ORANGE] = COLOR3B.QUALITY_ORANGE,	-- 橙
	[GameEnum.ITEM_COLOR_RED] = COLOR3B.QUALITY_RED,		-- 红
	[GameEnum.ITEM_COLOR_PINK] = COLOR3B.QUALITY_PINK,		-- 粉
	[GameEnum.ITEM_COLOR_GLOD] = COLOR3B.QUALITY_GLOD,		-- 金
	[GameEnum.ITEM_COLOR_COLOR_FUL] = COLOR3B.QUALITY_COLOUR,	-- 炫彩
	[GameEnum.ITEM_COLOR_SHINING_GOLD] = COLOR3B.QUALITY_SHINING_GOLD,	-- 耀金
	[GameEnum.ITEM_COLOR_XUAN_QING] = COLOR3B.QUALITY_XUAN_QING,		-- 玄青
	
}

BEAST_ITEM_COLOR = {
	[1] = COLOR3B.QUALITY_GREEN,	-- 绿
	[2] = COLOR3B.QUALITY_BLUE,		-- 蓝
	[3] = COLOR3B.QUALITY_PURPLE,	-- 紫
	[4] = COLOR3B.QUALITY_ORANGE,	-- 橙
	[5] = COLOR3B.QUALITY_RED,		-- 红
	[6] = COLOR3B.QUALITY_COLOUR,	-- SP
	[7] = COLOR3B.BEAST_UP,			-- UP
	[8] = COLOR3B.BEAST_USP,		-- USP
}

ITEM_COLOR_DARK = {
	[GameEnum.ITEM_COLOR_WHITE] = COLOR3B.DEFAULT,				-- 白
	[GameEnum.ITEM_COLOR_GREEN] = COLOR3B.QUALITY_GREEN,		-- 绿
	[GameEnum.ITEM_COLOR_BLUE] = COLOR3B.QUALITY_BLUE,			-- 蓝
	[GameEnum.ITEM_COLOR_PURPLE] = COLOR3B.QUALITY_PURPLE,		-- 紫
	[GameEnum.ITEM_COLOR_ORANGE] = COLOR3B.QUALITY_ORANGE,		-- 橙
	[GameEnum.ITEM_COLOR_RED] = COLOR3B.QUALITY_RED,			-- 红
	[GameEnum.ITEM_COLOR_PINK] = COLOR3B.QUALITY_PINK,			-- 粉
	[GameEnum.ITEM_COLOR_GLOD] = COLOR3B.QUALITY_GLOD,			-- 金
	[GameEnum.ITEM_COLOR_COLOR_FUL] = COLOR3B.QUALITY_COLOUR,	-- 炫彩
	[GameEnum.ITEM_COLOR_SHINING_GOLD] = COLOR3B.QUALITY_SHINING_GOLD,	-- 耀金
	[GameEnum.ITEM_COLOR_XUAN_QING] = COLOR3B.QUALITY_XUAN_QING,		-- 玄青
}

ITEM_COLOR_LIGHT = {
	[GameEnum.ITEM_COLOR_WHITE] = COLOR3B.QUALITY_DEFAULT,	-- 白
	[GameEnum.ITEM_COLOR_GREEN] = COLOR3B.QUALITY_GREEN,	-- 绿
	[GameEnum.ITEM_COLOR_BLUE] = COLOR3B.QUALITY_BLUE,		-- 蓝
	[GameEnum.ITEM_COLOR_PURPLE] = COLOR3B.QUALITY_PURPLE,	-- 紫
	[GameEnum.ITEM_COLOR_ORANGE] = COLOR3B.QUALITY_ORANGE,	-- 橙
	[GameEnum.ITEM_COLOR_RED] = COLOR3B.QUALITY_RED,		-- 红
	[GameEnum.ITEM_COLOR_PINK] = COLOR3B.QUALITY_PINK,		-- 粉
	[GameEnum.ITEM_COLOR_GLOD] = COLOR3B.QUALITY_GLOD,		-- 金
	[GameEnum.ITEM_COLOR_COLOR_FUL] = COLOR3B.QUALITY_COLOUR,	-- 炫彩
	[GameEnum.ITEM_COLOR_SHINING_GOLD] = COLOR3B.QUALITY_SHINING_GOLD,	-- 耀金
	[GameEnum.ITEM_COLOR_XUAN_QING] = COLOR3B.QUALITY_XUAN_QING,		-- 玄青
}

ITEM_COLOR_LIGHT_NEW = {
	[GameEnum.ITEM_COLOR_WHITE] = COLOR3B.QUALITY_DEFAULT,	-- 白
	[GameEnum.ITEM_COLOR_GREEN] = COLOR3B.L1,				-- 绿
	[GameEnum.ITEM_COLOR_BLUE] = COLOR3B.L2,				-- 蓝
	[GameEnum.ITEM_COLOR_PURPLE] = COLOR3B.L3,				-- 紫
	[GameEnum.ITEM_COLOR_ORANGE] = COLOR3B.L4,				-- 橙
	[GameEnum.ITEM_COLOR_RED] = COLOR3B.L5,					-- 红
	[GameEnum.ITEM_COLOR_PINK] = COLOR3B.L6,				-- 粉
	[GameEnum.ITEM_COLOR_GLOD] = COLOR3B.L7,				-- 金
	[GameEnum.ITEM_COLOR_COLOR_FUL] = COLOR3B.L8,			-- 炫彩
	[GameEnum.ITEM_COLOR_SHINING_GOLD] = COLOR3B.L9,		-- 耀金
	[GameEnum.ITEM_COLOR_XUAN_QING] = COLOR3B.L10,			-- 玄青
}

-- 主界面标准色
MAIN_VIEW_COLOR = {
	DESC_COLOR		= "#dcfff6",				-- 一般描述
	WHITE			= "#ffffff",				-- 白
	GREEN			= "#72eba9",				-- 绿
	BLUE			= "#7cd5ff",				-- 蓝
	PURPLE			= "#bb6be0",				-- 紫
	ORANGE			= "#ffc07c",				-- 橙
	RED				= "#ff5252",				-- 红
	YELLOW			= "#fff47c",				-- 黄
	PINK			= "#ffb4fe",				-- 粉
	BROWN_YELLOW	= "#EF9B50",				-- 棕黄色
	BLUISH_BLUE		= "#55fffd",				-- 青蓝色
}

EQUIP_SHENGPIN_COLOR = {
	COLOR3B.GREEN,	-- 绿
	COLOR3B.BLUE,	-- 蓝
	COLOR3B.PURPLE,	-- 紫
	COLOR3B.ORANGE,	-- 橙
	COLOR3B.RED,	-- 红
	COLOR3B.PINK,	-- 粉
}

KuangBao_Color = Color(0.918, 0.255, 0.102, 1)

-- 获得提示物品颜色
GET_TIP_ITEM_COLOR = {
	[GameEnum.ITEM_COLOR_WHITE] = COLOR3B.DEFAULT,		-- 白
	[GameEnum.ITEM_COLOR_GREEN] = COLOR3B.D_GREEN,			-- 绿
	[GameEnum.ITEM_COLOR_BLUE] = COLOR3B.D_BLUE,			-- 蓝
	[GameEnum.ITEM_COLOR_PURPLE] = COLOR3B.D_PURPLE,		-- 紫
	[GameEnum.ITEM_COLOR_ORANGE] = COLOR3B.D_ORANGE,		-- 橙
	[GameEnum.ITEM_COLOR_RED] = COLOR3B.D_RED,				-- 红
	[GameEnum.ITEM_COLOR_PINK] = COLOR3B.D_PINK,			-- 粉
	[GameEnum.ITEM_COLOR_GLOD] = COLOR3B.D_GLOD,			-- 金
	[GameEnum.ITEM_COLOR_COLOR_FUL] = COLOR3B.D_DAZZLING,	-- 炫彩
	[GameEnum.ITEM_COLOR_SHINING_GOLD] = COLOR3B.QUALITY_SHINING_GOLD,	-- 耀金
	[GameEnum.ITEM_COLOR_XUAN_QING] = COLOR3B.QUALITY_XUAN_QING,		-- 玄青
}

FALL_ITEM_COLOR = {
	[GameEnum.ITEM_COLOR_WHITE] = COLOR3B.WHITE,	-- 白
	[GameEnum.ITEM_COLOR_GREEN] = COLOR3B.GREEN,-- 绿
	[GameEnum.ITEM_COLOR_BLUE] = COLOR3B.BLUE,	 -- 蓝
	[GameEnum.ITEM_COLOR_PURPLE] = COLOR3B.PURPLE,	-- 紫
	[GameEnum.ITEM_COLOR_ORANGE] = COLOR3B.ORANGE,	-- 橙
	[GameEnum.ITEM_COLOR_RED] = COLOR3B.RED,		-- 红
	[GameEnum.ITEM_COLOR_PINK] = COLOR3B.PINK,	-- 粉
	[GameEnum.ITEM_COLOR_GLOD] = COLOR3B.GLOD,			-- 金
	[GameEnum.ITEM_COLOR_COLOR_FUL] = COLOR3B.DAZZLING,	-- 炫彩
	[GameEnum.ITEM_COLOR_SHINING_GOLD] = COLOR3B.QUALITY_SHINING_GOLD,	-- 耀金
	[GameEnum.ITEM_COLOR_XUAN_QING] = COLOR3B.QUALITY_XUAN_QING,		-- 玄青
}

EQUIP_COLOR = {
	[GameEnum.EQUIP_COLOR_GREEN] = COLOR3B.GREEN,	-- 绿
	[GameEnum.EQUIP_COLOR_BLUE] = COLOR3B.BLUE,   	-- 蓝
	[GameEnum.EQUIP_COLOR_PURPLE] = COLOR3B.PURPLE,	-- 紫
	[GameEnum.EQUIP_COLOR_ORANGE] = COLOR3B.ORANGE,	-- 橙
	[GameEnum.EQUIP_COLOR_RED] = COLOR3B.RED,		-- 红
	[GameEnum.EQUIP_COLOR_TEMP] = COLOR3B.PINK,		-- 金
}

SHENSHOE_EQUIP_COLOR = {
	[0] = COLOR3B.D_GREEN,	-- 绿
	[1] = COLOR3B.D_BLUE,    -- 蓝
	[2] = COLOR3B.PURPLE,	-- 紫
	[3] = COLOR3B.ORANGE,	-- 橙
	[4] = COLOR3B.RED,		-- 红
	[5] = COLOR3B.QUALITY_PINK,		-- 金
}

SEX_COLOR = {
	[GameEnum.MALE] = {"♂", "00ffff", "#0178fe"},		-- 男性蓝色
	[GameEnum.FEMALE] = {"♀", "db70db", "#df22e7"},		-- 女性粉色
}

CAMP_COLOR3B = {
	[GameEnum.ROLE_CAMP_0] = COLOR3B.WHITE,			-- 白色
	[GameEnum.ROLE_CAMP_1] = COLOR3B.ORANGE,		-- 橙
	[GameEnum.ROLE_CAMP_2] = COLOR3B.GREEN,			-- 绿
	[GameEnum.ROLE_CAMP_3] = COLOR3B.BLUE,			-- 蓝
}

--羽翼坐骑颜色
GRADE_COCOR3B = {
	[0] = COLOR3B.GLOD,								--特殊羽翼金色
	[1] = COLOR3B.GREEN,							-- 绿
	[2] = COLOR3B.GREEN,
	[3] = COLOR3B.BLUE,								-- 蓝
	[4] = COLOR3B.BLUE,
	[5] = COLOR3B.BLUE,								-- 紫
	[6] = COLOR3B.BLUE,
	[7] = COLOR3B.PURPLE,							-- 橙
	[8] = COLOR3B.PURPLE,
	[9] = COLOR3B.PURPLE,							-- 红
	[10] = COLOR3B.ORANGE,
	[11] = COLOR3B.ORANGE,
}

--羽翼坐骑暗地颜色
GRADE_D_COCOR3B = {
	[0] = COLOR3B.D_GLOD,								--特殊羽翼金色
	[1] = COLOR3B.D_GREEN,							-- 绿
	[2] = COLOR3B.D_GREEN,
	[3] = COLOR3B.D_BLUE,								-- 蓝
	[4] = COLOR3B.D_BLUE,
	[5] = COLOR3B.D_BLUE,								-- 紫
	[6] = COLOR3B.D_BLUE,
	[7] = COLOR3B.D_PURPLE,							-- 橙
	[8] = COLOR3B.D_PURPLE,
	[9] = COLOR3B.D_PURPLE,							-- 红
	[10] = COLOR3B.D_ORANGE,
	[11] = COLOR3B.D_ORANGE,
}

--法阵颜色
FAZHEN_COCOR3B = {
	[0] = COLOR3B.WHITE,
	[1] = COLOR3B.GREEN,							-- 绿
	[2] = COLOR3B.GREEN,
	[3] = COLOR3B.BLUE,								-- 蓝
	[4] = COLOR3B.BLUE,
	[5] = COLOR3B.PURPLE,							-- 紫
	[6] = COLOR3B.PURPLE,
	[7] = COLOR3B.ORANGE,							-- 橙
	[8] = COLOR3B.ORANGE,
	[9] = COLOR3B.RED,								-- 红
	[10] = COLOR3B.RED,
}

--职业颜色
PROF_COLOR3B = {										-- 职业
	[GameEnum.ROLE_PROF_1] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_2] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_3] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_4] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_11] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_12] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_13] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_14] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_21] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_22] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_23] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_24] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_31] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_32] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_33] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_34] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_41] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_42] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_43] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_44] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_51] = "#0178fe",	-- 男性蓝色
	[GameEnum.ROLE_PROF_52] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_53] = "#df22e7",	-- 女性粉色
	[GameEnum.ROLE_PROF_54] = "#0178fe",	-- 男性蓝色
}

-- 宠物资质颜色
PET_COLOR3B = {
	[0] = COLOR3B.WHITE,							-- 白
	[1] = COLOR3B.GREEN,							-- 绿
	[2] = COLOR3B.BLUE,								-- 蓝
	[3] = COLOR3B.PURPLE,							-- 紫
	[4] = COLOR3B.ORANGE,							-- 橙
	[5] = COLOR3B.RED,								-- 红
	[6] = COLOR3B.QUALITY_PINK,						-- 粉
}

-- 天神资质颜色
TIANSHEN_COLOR3B = {
	[1] = COLOR3B.PURPLE,							-- 紫
	[2] = COLOR3B.ORANGE,				            -- 橙
	[3] = COLOR3B.RED,							    -- 红
	[4] = COLOR3B.PINK,   			                -- 粉
	[5] = COLOR3B.GLOD,								-- 金
}

-- 天神资质暗底颜色
TIANSHEN_DARK_COLOR3B = {
	[1] = COLOR3B.D_PURPLE,							-- 紫
	[2] = COLOR3B.D_ORANGE,				            -- 橙
	[3] = COLOR3B.D_RED,							-- 红
	[4] = COLOR3B.D_PINK,   			            -- 粉
	[5] = COLOR3B.D_GLOD,							-- 金
}


ITEM_TIP_COLOR = {
	[GameEnum.EQUIP_COLOR_GREEN] = COLOR3B.GREEN,		-- 绿
	[GameEnum.EQUIP_COLOR_BLUE] = COLOR3B.BLUE,			-- 蓝
	[GameEnum.EQUIP_COLOR_PURPLE] = COLOR3B.PURPLE,		-- 紫
	[GameEnum.EQUIP_COLOR_ORANGE] = COLOR3B.ORANGE,		-- 橙
	[GameEnum.EQUIP_COLOR_RED] = COLOR3B.RED,			-- 红
	[GameEnum.EQUIP_COLOR_TEMP] = COLOR3B.PINK,			-- 粉
	[GameEnum.EQUIP_COLOR_GLOD] = COLOR3B.D_GLOD,			-- 金
	[GameEnum.EQUIP_COLOR_FULL] = COLOR3B.DAZZLING,		-- 幻彩色
}

--暗底
ITEM_TIP_D_COLOR = {
	[GameEnum.EQUIP_COLOR_GREEN] = COLOR3B.D_GREEN,			-- 绿
	[GameEnum.EQUIP_COLOR_BLUE] = COLOR3B.D_BLUE,			-- 蓝
	[GameEnum.EQUIP_COLOR_PURPLE] = COLOR3B.D_PURPLE,		-- 紫
	[GameEnum.EQUIP_COLOR_ORANGE] = COLOR3B.D_ORANGE,		-- 橙
	[GameEnum.EQUIP_COLOR_RED] = COLOR3B.D_RED,				-- 红
	[GameEnum.EQUIP_COLOR_TEMP] = COLOR3B.D_PINK,			-- 粉
	[GameEnum.EQUIP_COLOR_GLOD] = COLOR3B.D_GLOD,			-- 金
	[GameEnum.EQUIP_COLOR_FULL] = COLOR3B.D_DAZZLING,		-- 幻彩色
}

JINGJIE_COLOR3B = {
	[1] = COLOR3B.GREEN,            		--绿色
	[2] = COLOR3B.BLUE,                     --蓝色
	[3] = COLOR3B.PURPLE,                   --紫色
	[4] = COLOR3B.ORANGE,                   --橙色
	[5] = COLOR3B.RED,                      --红色
	[6] = COLOR3B.PINK,                     --粉色
}

ChatTextColor = {
	[0] = "#a5492d",
	[1] = "#a5492d",
	[2] = "#073d5c",
	[3] = "#191919",
	[4] = "#a5492d",
	[5] = "#724930",
	[6] = "#724930",
	[7] = "#0e315a",
	[8] = "#432912",
	[9] = "#a5492d",
	[10] = "#510a06",
	[11] = "#432912",
	[12] = "#035b78",
	[13] = "#432912",
}

F2ChatTextColor = {                                  
	[0] = "#99ffbb",--玩家名字                              暗绿色
	[1] = "#ff0000",--怪物名字                              红色
	[2] = "#DF16EC",--NPC                                  亮粉
	[3] = "#fab379",--经验/排名                             暗橙
	[4] = "#fa7994",--副本                                 暗粉
	[5] = "#DF16EC",--活动名称                              亮粉
	[6] = "#79b9fa",--仙盟名字                              暗蓝
	[7] = "#99ffbb",--前往/加入队伍/数量/位置                 暗绿  
	[8] = "#545252",--默认
	[9] = "#FFFFFF",--系统
	[10] = "#006a25",--亮底绿
	[11] = "#fab379",--其他玩家名字                          暗橙
	[12] = "#655bff",--其他玩家名字                          暗粉
	[13] = "#ed6127",--                                     亮橙
}

GuildColor = {
	TRUE = "#ff9292",
	FALSE = "#ffe58c",
}

HolyHeavenlyDomainCityNameColor = {
	[0] = "#FFF075",
	[1] = "#E4A4FF",
	[2] = "#B4FFEE",
	[3] = "#DA5BFF",
	[4] = "#FFA4CB",
	[5] = "#FFCB41",
	[6] = "#B4DDFF",
	[7] = "#E4E66F",
	[8] = "#80E3FF",
	[9] = "#FF65FC",
	[10] = "#9F7DFF",
	[11] = "#46D9FF",
	[12] = "#FFAF7F",
	[13] = "#7FFFB7",
	[14] = "#7FB8FF",
	[15] = "#FF917F",
	[16] = "#FFFFFF",
}

ATTACK_MODE_COLOR = {
	[0] = "#F1F031",
	[1] = "#FFE58C",
	[2] = "#FAB379",
	[3] = "#4FB9E6",
	[6] = "#FA7994",
	[7] = "#41DFDD",
	[8] = "#41DFDD",
	[9] = "#FFE58C",
	[10] = "#F97878",
	[11] = "#CB79FA",
}

F2_TIPS_COLOR = {
	SOCRE = COLOR3B.WHITE,			-- 评分颜色
	ATTR_TITLE = "#F8D999FF",		-- 属性类型颜色
	ATTR_NAME = "#a4a9af",		-- 属性名字颜色
	ATTR_VALUE = "#ffffff",		-- 属性值颜色
	GETWAY_VALUE = "#8FD6FF",		-- 获得途径颜色
}

function C3b2Str(c3b)
	return string.format("%02x%02x%02x", c3b.r, c3b.g, c3b.b)
end

local str_to_color_map = {}
function Str2C3b(str)
	if nil == str or string.len(str) < 7 then
		return Str2C3b(COLOR3B.WHITE)
	end

	local color = str_to_color_map[str]
	if nil ~= color then
		return color
	end

	local temp_list = {}
	for num in string.gmatch(str, "%x") do
		temp_list[#temp_list + 1] = num
	end
	local color_list = {}
	for i=1,#temp_list,2 do
		color_list[#color_list + 1] = tonumber(temp_list[i] .. temp_list[i + 1], 16)
	end

	local color = Color.New(color_list[1] / 255, color_list[2] / 255, color_list[3] / 255)
	str_to_color_map[str] = color
	return color
end

function ToColorStr(str, color)
	str = str or ""
	color = color or COLOR3B.WHITE
	local color_str = "<color=%s>%s</color>"
	return string.format(color_str, color, str)
end

------------------------ 样式包装 Start-------------------------------------

-- 根据品质包装样式  quality:0-10
function ToColorStyleByQuality(str, quality)
	str = str or ""
	quality = quality or 0
	local color_str = "<style=Quality_%s>%s</style>"
	return string.format(color_str, quality, str)
end

-- 根据状态包装样式 主要用于条件显示 is_green:满足条件true 不满足false
function ToColorStyleByState(str, is_green)
	str = str or ""
	if is_green then
		local color_str = "<style=text_green>%s</style>"
		return string.format(color_str, str)
	else
		local color_str = "<style=text_red>%s</style>"
		return string.format(color_str, str)
	end
end

--------------------------样式包装 End---------------------------------------------------------

------------------------ 颜色包装 Start-------------------------------------

-- 根据品质包装颜色
-- quality:0-10
-- is_light:是否是亮底(默认暗底)
function ToColorStrByQuality(str, quality, is_light)
	str = str or ""
	local color = is_light and ITEM_COLOR_LIGHT[quality] or ITEM_COLOR_DARK[quality] 
	local color_str = "<color=%s>%s</color>"
	return string.format(color_str, color, str)
end

-- 根据状态包装颜色 主要用于条件显示 
-- is_green:满足条件true 不满足false
-- is_light:是否是亮底(默认暗底)
function ToColorStrByState(str, is_green, is_light)
	str = str or ""
	local color
	if is_light then
		color = is_green and COLOR3B.C2 or COLOR3B.C3
	else
		color = is_green and COLOR3B.C8 or COLOR3B.C10
	end

	local color_str = "<color=%s>%s</color>"
	return string.format(color_str, color, str)
end

--------------------------颜色包装 End---------------------------------------------------------

QualityText = {
	-- 品质色定义
	[GameEnum.EQUIP_COLOR_GREEN] = {
		[1] = Color.New(102/255, 1, 0, 1),
		[2] = Color.New(241/255, 1, 240/255, 1),
		[3] = Color.New(22/255, 61/255, 0, 1)},		-- 绿
	[GameEnum.EQUIP_COLOR_BLUE] = {
		[1] = Color.New(51/255, 185/255, 1, 1),
		[2] = Color.New(1, 1, 1, 1),
		[3] = Color.New(15/255, 54/255, 86/255, 1)},		-- 蓝
	[GameEnum.EQUIP_COLOR_PURPLE] = {
		[1] = Color.New(253/255, 97/255, 1, 1),
		[2] = Color.New(235/255, 248/255, 254/255, 1),
		[3] = Color.New(75/255, 0, 77/255, 1)},		-- 紫
	[GameEnum.EQUIP_COLOR_ORANGE] = {
		[1] = Color.New(1, 152/255, 68/255, 1),
		[2] = Color.New(1, 1, 1, 1),
		[3] = Color.New(100/255, 46/255, 2/255, 1)},		-- 橙
	[GameEnum.EQUIP_COLOR_RED] = {
		[1] = Color.New(1, 96/255, 96/255, 1),
		[2] = Color.New(1, 1, 1, 1),
		[3] = Color.New(100/255, 2/255, 2/255, 1)},		-- 红
	[GameEnum.EQUIP_COLOR_TEMP] = {
		[1] = Color.New(1, 105/255, 169/255, 1),
		[2] = Color.New(1, 254/255, 243/255, 1),
		[3] = Color.New(100/255, 2/255, 76/255, 1)},  	-- 粉
}

local RankColor = {
	-- 段位色定义
	["bronze"] = {
		[1] = Color.New(76/255, 203/255, 66/255, 1),
		[2] = Color.New(208/255, 1, 209/255, 1),
		[3] = Color.New(52/255, 52/255, 52/255, 1)},			-- 青铜
	["sliver"] =
		QualityText[GameEnum.EQUIP_COLOR_GREEN],
	["gold"] =
		QualityText[GameEnum.EQUIP_COLOR_BLUE],
	["platinum"] =
		QualityText[GameEnum.EQUIP_COLOR_PURPLE],
	["diamond"] =
		QualityText[GameEnum.EQUIP_COLOR_ORANGE],
	["master"] =
		QualityText[GameEnum.EQUIP_COLOR_RED],
	["king"] =
		QualityText[GameEnum.EQUIP_COLOR_TEMP],
}

COLOR3B_1 = {
	-- 亮底通用颜色
	GREEN = Color.New(0/255, 127/255, 24/255, 1),				-- 绿
	GRAY = Color.New(94/255, 91/255, 91/255, 1),				-- 灰

	-- 暗底通用颜色
	D_GREEN = Color.New(149/255, 209/255, 43/255, 1),				-- 绿
	D_GRAY = Color.New(146/255, 145/255, 146/255, 1),				-- 灰

	WHITE = Color.New(255/255, 255/255, 255/255, 1),					--白

	DEFAULT_NUM = Color.New(157/255, 245/255, 167/255, 1),				--默认数字绿
}

setmetatable(QualityText, {__index = function (t, k)
	if RankColor[k] then
		return RankColor[k]
	end
	return t[k]
end})

function ChangeToQualityText(text, color_type, not_out_line)
	if not QualityText[color_type] then
		return
	end
	local gradient = text:GetOrAddComponent(typeof(UIGradient))
	gradient.Color1 = QualityText[color_type][1]
	gradient.Color2 = QualityText[color_type][2]
	if not not_out_line then
		local out_line = text:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
		out_line.effectColor = QualityText[color_type][3]
		out_line.effectDistance = Vector2(0.5,-0.5)
	end
end

-- 改变Text 渐变色 描边色 投影色
function ChangeTextGradientColor(text_node, color_up, color_down, color_outline, color_shadow)
	if not text_node then
		return
	end

	local gradient_component = text_node.gradient
	if gradient_component and color_up and color_down then
		gradient_component.Color1 = Str2C3b(color_up)
		gradient_component.Color2 = Str2C3b(color_down)
	end

	local outline_component = text_node.out_line
	if outline_component and color_outline then
		outline_component.effectColor = Str2C3b(color_outline)
	end

	local shadow_component = text_node.shadow
	if shadow_component and color_shadow then
		shadow_component.effectColor = Str2C3b(color_shadow)
	end
end

-- 运营活动修改渐变字体颜色
function OperationActivityChangeToQualityText(text, color_str_list, not_out_line, is_line)
	local color_str_list1
	if is_line then
		color_str_list1 = Split(color_str_list, "|")
	else
		color_str_list1 = Split(color_str_list, ",")
	end
	local gradient = text:GetOrAddComponent(typeof(UIGradient))
	gradient.Color1 = Str2C3b(color_str_list1[1])
	gradient.Color2 = Str2C3b(color_str_list1[2])
	if not not_out_line then
		local out_line = text:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
		out_line.effectColor = Str2C3b(color_str_list1[3])
		-- out_line.effectDistance = Vector2(0.5,-0.5)
	end
end

function GetRightColor(str,flag,satisfy_color,lack_color)
	local dispose_str = str
	if flag then
		dispose_str = ToColorStr(str,satisfy_color)
	else
		dispose_str = ToColorStr(str,lack_color)
	end
	return dispose_str
end

local str_to_color_map = {}
function StrToColor(str)
	if nil == str or string.len(str) < 7 then
		print_error("[StrToColor] Error", str)
		return StrToColor(COLOR3B.WHITE)
	end

	local color = str_to_color_map[str]
	if nil ~= color then
		return color
	end

	color = Color.New((tonumber(string.sub(str, 2, 3), 16) or 255) / 255,
		(tonumber(string.sub(str, 4, 5), 16) or 255) / 255,
		(tonumber(string.sub(str, 6, 7), 16) or 255) / 255,
		(tonumber(string.sub(str, 8, 9), 16) or 255) / 255)

	str_to_color_map[str] = color
	return color
end
