-- K-跨服赛跑.xls
local item_table={
[1]={item_id=50447,num=2,is_bind=1},
[2]={item_id=50447,num=1,is_bind=1},
[3]={item_id=50447,num=3,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
duck={
{inspire_duck_add_speed=43,disturb_duck_dec_speed=72,disturb_duck_dec_speed_rate=85,disturb_duck_stop_rate=15,},
{id=1,xingfen=2,disturb_duck_dec_speed_rate=85,disturb_duck_stop_rate=15,},
{id=2,xingfen=3,inspire_duck_fail_rate=65,},
{id=3,tili=2,inspire_duck_add_speed=43,disturb_duck_dec_speed=72,disturb_duck_fail_rate=70,},
{id=4,tili=2,xingfen=2,disturb_duck_fail_rate=70,},
{id=5,xingfen=3,inspire_duck_fail_rate=65,},
{id=6,tili=3,disturb_duck_fail_rate=75,},
{id=7,tili=3,xingfen=2,disturb_duck_fail_rate=75,},
{id=8,xingfen=3,inspire_duck_fail_rate=65,}
},

duck_meta_table_map={
[3]=2,	-- depth:1
[6]=5,	-- depth:1
[7]=4,	-- depth:1
[9]=8,	-- depth:1
},
duck_group={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

duck_group_meta_table_map={
},
duck_trait={
{},
{duck_index=1,trait_id=2,monster_id=55002,param1=110,param2=110,weight="99KG",hobby="横行无忌",tips_desc="正在思考比赛完了吃什么...",},
{duck_index=2,trait_id=3,monster_id=55003,param1=90,param2=90,weight="110KG",hobby="勇往直前",tips_desc="比赛...？好像是不能睡觉的...",}
},

duck_trait_meta_table_map={
},
event_area={
{},
{seq=1,center="96,82",},
{seq=2,center="123,58",},
{seq=3,center="165,95",},
{seq=4,center="136,125",}
},

event_area_meta_table_map={
},
rand_event={
{},
{event_id=1,duck_qipao1="本蟹无语！",},
{event_id=2,duck_qipao1="冠军是我的！谁也别抢",},
{event_id=3,duck_qipao1="这方向一定没错",},
{event_id=4,},
{event_id=5,},
{event_id=6,},
{event_id=7,}
},

rand_event_meta_table_map={
},
run_path={
{},
{duck_index=1,run_path_points="117,140|129,142|135,151|133,171|119,170|78,172|69,170|66,157|65,141|88,140|95,134|95,107|94,57|116,55|134,56|143,56|150,64|158,72|160,82|161,105|160,125|147,127|133,127|128,119|129,109|131,94",},
{duck_index=2,run_path_points="117,137|129,141|136,152|133,170|119,170|78,172|69,170|66,157|65,141|88,140|95,134|95,107|94,57|116,55|134,56|143,56|150,64|158,72|160,82|161,105|160,125|147,127|133,127|128,119|129,109|131,94",}
},

run_path_meta_table_map={
},
barrage={
{op_res=1,duck_qipao="感恩！这东西好！",},
{desc="<color=#95d12b>%s</color>对<duck_index>喊道“开饭了”，<duck_index><duck_name>看了看你，<color=#95d12b>似乎没有什么反应</color>",dialog_content="你对<duck_index>喊道“开饭了”，<duck_index><duck_name>看了看你，<color=#95d12b>似乎没有什么反应</color>",},
{desc="<color=#95d12b>%s</color>对<duck_index>喊道“开饭了”，<duck_index><duck_name>似乎没有听到，<color=#95d12b>继续向前跑去</color>",dialog_content="你对<duck_index>喊道“开饭了”，<duck_index><duck_name>似乎没有听到，<color=#95d12b>继续向前跑去</color>",},
{op_res=1,desc="<color=#95d12b>%s</color>对<duck_index>喊道“有小鱼”，<duck_index><duck_name>突然兴奋，<color=#95d12b>跑得更快了</color>！",dialog_content="你对<duck_index>喊道“有小鱼”，<duck_index><duck_name>突然兴奋，<color=#95d12b>跑得更快了</color>！",duck_qipao="冠军我来辣！",},
{desc="<color=#95d12b>%s</color>对<duck_index>喊道“有小鱼”，<duck_index><duck_name>看了看你，<color=#95d12b>似乎没有什么反应</color>",dialog_content="你对<duck_index>喊道“有小鱼”，<duck_index><duck_name>看了看你，<color=#95d12b>似乎没有什么反应</color>",},
{desc="<color=#95d12b>%s</color>对<duck_index>喊道“有小鱼”，<duck_index><duck_name>似乎没有听到，<color=#95d12b>继续向前跑去</color>",dialog_content="你对<duck_index>喊道“有小鱼”，<duck_index><duck_name>似乎没有听到，<color=#95d12b>继续向前跑去</color>",},
{op_res=1,desc="<color=#95d12b>%s</color>对<duck_index>大声鼓励，<duck_index><duck_name>备受鼓舞，<color=#95d12b>发疯一般的向前跑去</color>",dialog_content="你对<duck_index>大声鼓励，<duck_index><duck_name>备受鼓舞，<color=#95d12b>发疯一般的向前跑去</color>",duck_qipao="芜湖芜湖~本蟹起飞！",},
{desc="<color=#95d12b>%s</color>对<duck_index>大声鼓励，<duck_index><duck_name>一脸嫌弃，<color=#95d12b>按部就班的向前跑去</color>",dialog_content="你对<duck_index>大声鼓励，<duck_index><duck_name>一脸嫌弃，<color=#95d12b>按部就班的向前跑去</color>",},
{desc="<color=#95d12b>%s</color>对<duck_index>大声鼓励，<duck_index><duck_name>假装没听到，<color=#95d12b>跑的很悠闲</color>",dialog_content="你对<duck_index>大声鼓励，<duck_index><duck_name>假装没听到，<color=#95d12b>跑的很悠闲</color>",},
{op_res=1,desc="<color=#95d12b>%s</color>对<duck_index>大吼一声“娘子！”，<duck_index><duck_name>受到惊吓，<color=#95d12b>加速向前跑去</color>",dialog_content="你对<duck_index>大吼一声“娘子！”，<duck_index><duck_name>受到惊吓，<color=#95d12b>加速向前跑去</color>",duck_qipao="哎呀，一不小心就跑得更快了呢",},
{desc="<color=#95d12b>%s</color>对<duck_index>大吼一声“娘子！”，<duck_index><duck_name>回答了一句“啊哈？”，<color=#95d12b>然后继续向前跑去</color>",dialog_content="你对<duck_index>大吼一声“娘子！”，<duck_index><duck_name>回答了一句“啊哈？”，<color=#95d12b>然后继续向前跑去</color>",},
{desc="<color=#95d12b>%s</color>对<duck_index>大吼一声“娘子！”，<duck_index><duck_name>受到惊吓，<color=#95d12b>情绪低落的想要退出比赛了</color>",dialog_content="你对<duck_index>大吼一声“娘子！”，<duck_index><duck_name>受到惊吓，<color=#95d12b>情绪低落的想要退出比赛了</color>",},
{op_res=1,desc="<color=#95d12b>%s</color>在<duck_index>旁边做起了广播体操，<duck_index><duck_name>备受鼓舞，<color=#95d12b>开始加速向前奔跑</color>",dialog_content="你在<duck_index>旁边做起了广播体操，<duck_index><duck_name>备受鼓舞，<color=#95d12b>开始加速向前奔跑</color>",duck_qipao="geigei行不行啊！",},
{desc="<color=#95d12b>%s</color>在<duck_index>旁边做起了广播体操，<duck_index><duck_name>一脸嫌弃，<color=#95d12b>甚至想退出比赛了</color>",dialog_content="你在<duck_index>旁边做起了广播体操，<duck_index><duck_name>一脸嫌弃，<color=#95d12b>甚至想退出比赛了</color>",},
{desc="<color=#95d12b>%s</color>在<duck_index>旁边做起了广播体操，<duck_index><duck_name>看完之后，觉得身体不适，<color=#95d12b>但是速度并没有什么变化</color>",dialog_content="你在<duck_index>旁边做起了广播体操，<duck_index><duck_name>看完之后，觉得身体不适，<color=#95d12b>但是速度并没有什么变化</color>",},
{op_res=1,desc="<color=#95d12b>%s</color>悄悄地推了<duck_index>一把，<duck_index><duck_name><color=#95d12b>跑得更快了</color>",dialog_content="你悄悄地推了<duck_index>一把，<duck_index><duck_name><color=#95d12b>跑得更快了</color>",duck_qipao="老板放心！一定第一！",},
{desc="<color=#95d12b>%s</color>悄悄地推了<duck_index>一把，<duck_index><duck_name>一脸懵逼，<color=#95d12b>速度并没有变快</color>",dialog_content="你悄悄地推了<duck_index>一把，<duck_index><duck_name>一脸懵逼，<color=#95d12b>速度并没有变快</color>",},
{desc="<color=#95d12b>%s</color>悄悄地推了<duck_index>一把，<duck_index><duck_name><color=#95d12b>似乎没有受到任何影响</color>",dialog_content="你悄悄地推了<duck_index>一把，<duck_index><duck_name><color=#95d12b>似乎没有受到任何影响</color>",},
{op_res=2,desc="<color=#95d12b>%s</color>朝<duck_index>扔了一碗螺蛳粉，<duck_index><duck_name>被螺蛳粉砸中，晕倒在原地，<color=#95d12b>表情十分痛苦</color>",dialog_content="你朝<duck_index>扔了一碗螺蛳粉，<duck_index><duck_name>被螺蛳粉砸中，晕倒在原地，<color=#95d12b>表情十分痛苦</color>",duck_qipao="什么东西从天而降...",},
{desc="<color=#95d12b>%s</color>朝<duck_index>扔了一碗螺蛳粉，<duck_index><duck_name>受到酸笋味影响，<color=#95d12b>速度减慢了下来</color>",dialog_content="你朝<duck_index>扔了一碗螺蛳粉，<duck_index><duck_name>受到酸笋味影响，<color=#95d12b>速度减慢了下来</color>",duck_qipao="yue，这是什么味道",},
{desc="<color=#95d12b>%s</color>朝<duck_index>扔了一碗螺蛳粉，<duck_index><duck_name>闻闻味更兴奋了，<color=#95d12b>似乎不受影响</color>",dialog_content="你朝<duck_index>扔了一碗螺蛳粉，<duck_index><duck_name>闻闻味更兴奋了，<color=#95d12b>似乎不受影响</color>",},
{desc="<color=#95d12b>%s</color>朝<duck_index>扔了一个打开的榴莲，<duck_index><duck_name>被榴莲砸中，晕倒在原地，<color=#95d12b>看起来不能坚持比赛了</color>",dialog_content="你朝<duck_index>扔了一个打开的榴莲，<duck_index><duck_name>被榴莲砸中，晕倒在原地，<color=#95d12b>看起来不能坚持比赛了</color>",duck_qipao="飞过去个什么？晕...",},
{desc="<color=#95d12b>%s</color>朝<duck_index>扔了一个打开的榴莲，<duck_index><duck_name>被受到臭味影响，<color=#95d12b>速度减慢了下来</color>",dialog_content="你朝<duck_index>扔了一个打开的榴莲，<duck_index><duck_name>被受到臭味影响，<color=#95d12b>速度减慢了下来</color>",duck_qipao="这是什么？闻一下",},
{desc="<color=#95d12b>%s</color>朝<duck_index>扔了一个打开的榴莲，<duck_index><duck_name>觉得神清气爽，<color=#95d12b>并没有受到影响</color>",dialog_content="你朝<duck_index>扔了一个打开的榴莲，<duck_index><duck_name>觉得神清气爽，<color=#95d12b>并没有受到影响</color>",},
{desc="<color=#95d12b>%s</color>假装在<duck_index>面前摔倒，拉着<duck_index><duck_name>的脚不让蟹蟹走，<duck_index><duck_name><color=#95d12b>停在了原地</color>",dialog_content="你假装在<duck_index>面前摔倒，拉着<duck_index><duck_name>的脚不让蟹蟹走，<duck_index><duck_name><color=#95d12b>停在了原地</color>",duck_qipao="再扯蟹jio就断了！",},
{desc="<color=#95d12b>%s</color>假装在<duck_index>面前摔倒，拉着<duck_index><duck_name>的脚不让蟹蟹走，<duck_index><duck_name>的<color=#95d12b>速度慢了下来</color>",dialog_content="你假装在<duck_index>面前摔倒，拉着<duck_index><duck_name>的脚不让蟹蟹走，<duck_index><duck_name>的<color=#95d12b>速度慢了下来</color>",duck_qipao="轻点扯我！还要比赛呢",},
{desc="<color=#95d12b>%s</color>假装在<duck_index>面前摔倒，拉着<duck_index><duck_name>的脚不让蟹蟹走，但是<duck_index><duck_name><color=#95d12b>似乎没有受到影响</color>",dialog_content="你假装在<duck_index>面前摔倒，拉着<duck_index><duck_name>的脚不让蟹蟹走，但是<duck_index><duck_name><color=#95d12b>似乎没有受到影响</color>",},
{desc="<color=#95d12b>%s</color>朝<duck_index>唱起了摇篮曲，<duck_index><duck_name>慢慢陷入沉睡，<color=#95d12b>停留在了原地</color>",dialog_content="你朝<duck_index>唱起了摇篮曲，<duck_index><duck_name>慢慢陷入沉睡，<color=#95d12b>停留在了原地</color>",duck_qipao="怎么突然就..有点..困困的",},
{desc="<color=#95d12b>%s</color>朝<duck_index>唱起了摇篮曲，<duck_index><duck_name>快要睡着了，<color=#95d12b>速度减慢了下来</color>",dialog_content="你朝<duck_index>唱起了摇篮曲，<duck_index><duck_name>快要睡着了，<color=#95d12b>速度减慢了下来</color>",duck_qipao="我快睡着了~",},
{desc="<color=#95d12b>%s</color>朝<duck_index>唱起了摇篮曲，<duck_index><duck_name>听完之后摇了摇头，<color=#95d12b>并没有受到影响</color>",dialog_content="你朝<duck_index>唱起了摇篮曲，<duck_index><duck_name>听完之后摇了摇头，<color=#95d12b>并没有受到影响</color>",},
{desc="<color=#95d12b>%s</color>朝<duck_index>扔了一条小鱼，<duck_index><duck_name><color=#95d12b>停留在原地寻找小鱼的下落</color>",dialog_content="你朝<duck_index>扔了一条小鱼，<duck_index><duck_name><color=#95d12b>停留在原地寻找小鱼的下落</color>",duck_qipao="先吃一点不会影响吧！",},
{desc="<color=#95d12b>%s</color>朝<duck_index>扔了一条小鱼，<duck_index><duck_name>思考了一下，<color=#95d12b>速度慢了下来</color>",dialog_content="你朝<duck_index>扔了一条小鱼，<duck_index><duck_name>思考了一下，<color=#95d12b>速度慢了下来</color>",duck_qipao="u1s1，我觉得要先吃东西！",},
{desc="<color=#95d12b>%s</color>朝<duck_index>扔了一条小鱼，<duck_index><duck_name>不为所动，<color=#95d12b>继续向前跑去</color>",dialog_content="你朝<duck_index>扔了一条小鱼，<duck_index><duck_name>不为所动，<color=#95d12b>继续向前跑去</color>",},
{desc="<color=#95d12b>%s</color>对<duck_index>恶狠狠说“谁第一个到终点就烤谁！”，<duck_index><duck_name>不敢动了，<color=#95d12b>瑟瑟发抖</color>",dialog_content="你对<duck_index>恶狠狠说“谁第一个到终点就烤谁！”，<duck_index><duck_name>不敢动了，<color=#95d12b>瑟瑟发抖</color>",duck_qipao="这和说好的不一样啊~",},
{op_type=2,desc="<color=#95d12b>%s</color>对<duck_index>恶狠狠说“谁第一个到终点就烤谁！”，<duck_index><duck_name>受到了惊吓，<color=#95d12b>开始刻意减慢了速度</color>",dialog_content="你对<duck_index>恶狠狠说“谁第一个到终点就烤谁！”，<duck_index><duck_name>受到了惊吓，<color=#95d12b>开始刻意减慢了速度</color>",duck_qipao="先慢一点，再慢一点",},
{op_type=2,desc="<color=#95d12b>%s</color>对<duck_index>恶狠狠说“谁第一个到终点就烤谁！”，<duck_index><duck_name>不屑一顾，<color=#95d12b>继续向前跑去</color>",dialog_content="你对<duck_index>恶狠狠说“谁第一个到终点就烤谁！”，<duck_index><duck_name>不屑一顾，<color=#95d12b>继续向前跑去</color>",}
},

barrage_meta_table_map={
[33]=36,	-- depth:1
[30]=36,	-- depth:1
[27]=36,	-- depth:1
[24]=36,	-- depth:1
[21]=36,	-- depth:1
[19]=36,	-- depth:1
[35]=10,	-- depth:1
[23]=35,	-- depth:2
[25]=19,	-- depth:2
[26]=35,	-- depth:2
[28]=19,	-- depth:2
[29]=35,	-- depth:2
[31]=19,	-- depth:2
[32]=35,	-- depth:2
[34]=19,	-- depth:2
[20]=35,	-- depth:2
[22]=19,	-- depth:2
},
shortcut_event={
{},
{event_id=1,start_pos="95,135",duck_qipao1="有蟹蟹要反超了！",},
{event_id=2,start_pos="143,57",duck_qipao1="好像这里近一点！",},
{event_id=3,start_pos="157,72",duck_qipao1="我觉得我可以！",},
{event_id=4,start_pos="160,101",duck_qipao1="思考蟹生的意义是什么！",},
{event_id=5,start_pos="130,127",duck_qipao1="我不信！冠军一定是我的！",}
},

shortcut_event_meta_table_map={
},
rank_reward={
{},
{rank=2,item=item_table[1],},
{rank=3,item=item_table[2],}
},

rank_reward_meta_table_map={
},
other_default_table={sceneid=6112,enter_pos_x=104,enter_pos_y=122,enter_range=5,need_pre_task_id=0,round_count=3,bet_interval_sec=120,racing_interval_sec=240,show_result_interval_sec=15,can_fetch_play_coin_per_round=3,play_coin_to_coin_exchange_rate=50000,inspire_duck_cd_second=10,disturb_duck_cd_second=10,inspire_count_per_round=3,disturb_count_per_round=3,coin_num=200000,play_coin_item=91145,duck_vote_time=30,},

duck_default_table={id=0,tili=1,xingfen=1,inspire_duck_add_speed=57,inspire_duck_add_speed_ms=2000,disturb_duck_dec_speed=57,disturb_duck_dec_speed_ms=2000,disturb_duck_stop_ms=2000,inspire_duck_fail_rate=70,disturb_duck_fail_rate=65,disturb_duck_dec_speed_rate=90,disturb_duck_stop_rate=10,},

duck_group_default_table={},

duck_trait_default_table={duck_index=0,trait_id=1,monster_id=55001,param1=3,param2=29,weight="106KG",hobby="大摇大摆",tips_desc="正在思考胜利的意义是什么...",rank_talk_1="这胜利唾手可得|听说过什么叫蟹王吗？|本蟹一出，谁与争锋！",rank_talk_2="再比一次！我不服输！|强烈建议评委看看回放~|吃隔壁那个，我不好吃",rank_talk_3="一定好好锻炼嘤嘤嘤\nT-T|大人，求放过啊\nQAQ",},

event_area_default_table={seq=0,center="121,173",range=3,},

rand_event_default_table={event_id=0,duck_qipao1="",},

run_path_default_table={duck_index=0,run_path_points="117,143|128,143|137,149|133,170|119,170|78,172|69,170|66,157|65,141|88,140|95,134|95,107|94,57|116,55|134,56|143,56|150,64|158,72|160,82|161,105|160,125|147,127|133,127|128,119|129,109|131,94",},

barrage_default_table={op_type=1,op_res=0,desc="<color=#95d12b>%s</color>对<duck_index>喊道“开饭了”，<duck_index><duck_name>突然兴奋，<color=#95d12b>跑得更快了</color>！",dialog_content="你对<duck_index>喊道“开饭了”，<duck_index><duck_name>突然兴奋，<color=#95d12b>跑得更快了</color>！",duck_qipao="",},

shortcut_event_default_table={event_id=0,start_pos="69,171",start_range=3,skip_run_path=1,probability=50,distance=0,duck_qipao1="聪明的蟹蟹找到近路了！",},

rank_reward_default_table={rank=1,item=item_table[3],}

}

