------------------------------------------------------------
--npc对话框
------------------------------------------------------------
NpcDialog = NpcDialog or BaseClass(SafeBaseView)

local npc_rotation = nil
local PLAYER_TALK = "（p）"
local NPC_TALK = "（n）"
local BABY_TALK = "（b）"
local PLAYER_NAME = "（name）"
local PLAY_TALK_AUDIO = "PlayAudio"
Talk_Type = {
	None = 0,
	Player_Talk = 1,
	Npc_Talk = 2,
	Baby_Talk = 3
}

local old_talk_type = nil
local old_audio_asset
local cd_time = 3

-- 记录最后提交的任务，用于协议返回慢时判断下一个任务是否自动进行
NpcDialog.LAST_COMMIT_TASK = 0

function NpcDialog:__init()
	if NpcDialog.Instance then
		ErrorLog("[NpcDialog] Attemp to create a singleton twice !")
	end

	self.view_cache_time = ViewCacheTime.NORMAL
	
	NpcDialog.Instance = self
	self.on_click_btn = BindTool.Bind1(self.DoDialog, self)
	self:SetMaskBg(true,nil,nil,self.on_click_btn)
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:AddViewResource(0, "uis/view/miscpre_load_prefab", "layout_bottom")

	self.reward_cell_list = {}
	self.talk_content = {}
	self.btn_effect = nil

	self.task_id = 0
	self.npc_id = 0
	self.npc_status = 0
	self.param = 0
	self.ignore_t = nil
	self.update_func = BindTool.Bind1(self.UpdateTaskData, self)
	self.update_time = BindTool.Bind1(self.UpdateTime, self)
	self.update_talk = BindTool.Bind1(self.UpdateTalk, self)
	self.injured = false
	self.is_send_submission = true

	self.cur_index = 0
	self.old_index = nil
	self.open_tween = nil
	self.close_tween = nil
end

function NpcDialog:__delete()
	self.update_func = nil
	self.talk_content = nil
	self.update_time = nil
	self.update_talk = nil
	self.on_click_btn = nil

	if nil ~= self.get_guide_ui_event then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.TaskDialog, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	NpcDialog.Instance = nil
end

function NpcDialog:LoadCallBack()
	self.layout_reward = self.node_list["ph_reward_list"]
	self.reward_cell_list = {}
	for i = 1, 3 do
		self.reward_cell_list[i] = ItemCell.New(self.node_list["layout_reward_item" .. i])
	end

	self.layout_btn = self.node_list["layout_btn"]
	XUI.AddClickEventListener(self.layout_btn, BindTool.Bind(self.DoDialog, self, true))
	XUI.AddClickEventListener(self.node_list["continue_btn"], self.on_click_btn)
	XUI.AddClickEventListener(self.node_list["img_npcdialog_bg"], self.on_click_btn)
	XUI.AddClickEventListener(self.node_list["btn_double"], BindTool.Bind1(self.OnClickDouble, self))
	XUI.AddClickEventListener(self.node_list["btn_goto_transfer"], BindTool.Bind1(self.GotoTransferFB, self))
	XUI.AddClickEventListener(self.node_list["btn_cancle_transfer"], BindTool.Bind1(self.CancleTransferUpdate, self))
	-- XUI.AddClickEventListener(self.node_list["dialogskip"], BindTool.Bind1(self.SkipDialog, self), true)
	XUI.AddClickEventListener(self.node_list["btn_submission"], BindTool.Bind1(self.OnClickSubmission, self))
	XUI.AddClickEventListener(self.node_list["tianshenroad_btn"], BindTool.Bind1(self.OnClickTianShenRoadBtn, self))

	self.talk_txt = self.node_list["rich_talk_txt"]
	self.time_text = self.node_list["TimeText"]
	self.time_text.text.text = ""
	-- self:DoOpenAni()

	XUI.AddClickEventListener(self.node_list["btn_xmz_bs"], BindTool.Bind(self.OnClickXMZBS, self))
	XUI.AddClickEventListener(self.node_list["btn_xmz_give"], BindTool.Bind(self.OnClickXMZGive, self))
	XUI.AddClickEventListener(self.node_list["btn_longmai_enter"], BindTool.Bind(self.OnClickEnterLongMai, self))
	XUI.AddClickEventListener(self.node_list["btn_longmai_shop"], BindTool.Bind(self.OnClickLongMaiShop, self))
	XUI.AddClickEventListener(self.node_list["btn_accept_zhuogui_event"], BindTool.Bind(self.OnClickAcceptZhuoGuiEvent, self))

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TaskDialog, self.get_guide_ui_event)
end

function NpcDialog:OpenCallBack()
	self.ignore_time_text = false
	TaskWGData.Instance:NotifyDataChangeCallBack(self.update_func)

	MainuiWGCtrl.Instance:FlushView(0, "duihua_mian_ui_state", {true})
	SafeBaseView.ForceCloseScreenShot()
end

function NpcDialog:ReleaseCallBack()
	if nil ~= self.renwu_countdown then
		GlobalTimerQuest:CancelQuest(self.renwu_countdown)
		self.renwu_countdown = nil
	end

	if self.btn_text_countdown then
		GlobalTimerQuest:CancelQuest(self.btn_text_countdown)
		self.btn_text_countdown = nil
	end

	if self.submission_countdown then
		GlobalTimerQuest:CancelQuest(self.submission_countdown)
		self.submission_countdown = nil
	end

	CountDownManager.Instance:RemoveCountDown("auto_enter_tianshenroad_fb")

	if self.reward_cell_list then
		for k,v in pairs(self.reward_cell_list) do
			v:DeleteMe()
		end
	end
	self.reward_cell_list = nil
	self.layout_reward = nil
	self.layout_btn = nil
	self.talk_txt = nil
	self.time_text = nil
	self.guide_state = 0
	self.talk_content = {}

	if self.bind_eff then
		self.npc_display:OnRemoveGameObject(self.bind_eff)
		self.bind_eff = nil
	end

	if self.npc_display then
		self.npc_display:DeleteMe()
		self.npc_display = nil
	end

	if self.btn_effect then
		ResPoolMgr:Release(self.btn_effect)
		self.btn_effect = nil
	end

	if self.buy_item then
		self.buy_item:DeleteMe()
		self.buy_item = nil
	end

	self.old_index = nil
	NpcDialog.LAST_COMMIT_TASK = 0

	self:ClearGiveXMZStoneTimer()
end

function NpcDialog:OnClose()
	-- body
	TaskGuide.Instance:SpecialConditions(true, 5)
	self:Close()
end

function NpcDialog:OnClickDouble()
	-- body
	local task_cfg = TaskWGData.Instance:GetTaskConfig(self.task_id)
	if task_cfg then
		if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_7 and
			task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_66 then
			self:DoDialog()
			return
		end
	end

	local role_info = RoleWGData.Instance:GetRoleInfo()
	local youli_other = TaskWGData.Instance.youli_other_cfg
	if youli_other.double_cost > role_info.silver_ticket then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = COMMON_CONSTS.VIRTUAL_ITEM_YUANBAO})
		return
	end
	local member = self:GetMember(true)
	if not member then return end
	if member.level < youli_other.level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Task.youli_des03, youli_other.level))
		return
	end

	if 0 == member.is_online then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.youli_des04)
		return
	end
	TaskWGCtrl.Instance:SendYouLi(YouLiType.Type2, member.role_id)
	self:Close()
end

function NpcDialog:GetMember(show_tip)
	-- body
	local member_list = SocietyWGData.Instance:GetTeamMemberList()
	if 2 ~= #member_list then
		if show_tip then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.youli_des02)
		end
		return
	end

	for k,v in pairs(member_list) do
		if v.role_id ~= RoleWGData.Instance.role_vo.role_id then
			return v
		end
	end

	return nil
end

function NpcDialog:ShowEffect(bool)
	-- body
	if not bool then
		if self.btn_effect then
			ResPoolMgr:Release(self.btn_effect)
			self.btn_effect = nil
		end
	else
		if not self.btn_effect then
			local transform = self.node_list["layout_btn"].transform
			local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_duihua)
			EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, transform, 1800, nil, nil, nil,
				function (obj)
				-- body
					self.btn_effect = obj
				end
			)
		end
	end
end

function NpcDialog:FlushModel(talk_type)
	if nil == self.npc_display then
		self.npc_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			offset_type = MODEL_OFFSET_TYPE.NPC,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		
		self.npc_display:SetRenderTexUI3DModel(display_data)
		-- self.npc_display:SetUI3DModel(self.node_list["ph_display"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NPC)
		self:AddUiRoleModel(self.npc_display)
		self.npc_display:SetIsSupportClip(true)
	end
	self.npc_display:SetLoadComplete(BindTool.Bind(self.ModelFunc, self, talk_type))

	if talk_type ~= old_talk_type then
		old_talk_type = talk_type
		-- self:DoModelAni()
		self:DoOpenAni()
	end

	local main_role = Scene.Instance:GetMainRole()
	local bundle, asset
	self.npc_display:RemoveAllModel()
	-- 清空角色数据
	self.npc_display:SetModelResInfo(nil)

	if talk_type == Talk_Type.Npc_Talk then
		local model_id = 1
		local npc_config = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[self.npc_id]
		if npc_config.resid < 1000 then
			model_id = 4008001
		else
			model_id = npc_config.resid
		end
		bundle, asset = ResPath.GetNpcModel(model_id)
		self.npc_display:SetMainAsset(bundle, asset, function()
			if self.npc_display ~= nil then
				self.npc_display:SetIsLerpProbe(true)
			end
		end)

		if npc_config.is_weapon_anim == 1 then
			local weapon_bundle, weapon_asset = ResPath.GetNpcWeaponModel(model_id)
			self.npc_display:SetWeaponModel(weapon_bundle, weapon_asset)
		end

		if TaskWGData.INJURED_NPC[self.npc_id] then
			self.injured = not TaskWGData.Instance:GetTaskIsCanCommint(TaskWGData.INJURED_NPC[self.npc_id]) and not TaskWGData.Instance:GetTaskIsCompleted(TaskWGData.INJURED_NPC[self.npc_id])
			self.npc_display:SetBool("hurt", self.injured)
			if self.injured and model_id == TaskWGData.BIND_NPC then
				local draw_obj = self.npc_display:GetDrawObj()
				if not self.bind_loader and draw_obj then
					local bundle, asset = ResPath.GetEffect("Effect_kunbang")
					self.bind_loader = AllocAsyncLoader(self, "dialog_bind_loader")
					self.bind_loader:SetParent(draw_obj:GetRoot().transform)
					self.bind_loader:Load(bundle, asset, function(obj)
						if IsNil(obj) then
							return
						end
						self.bind_eff = obj
						self.npc_display:OnAddGameobject(obj)
					end)
				end
			elseif self.bind_loader then
				if self.bind_eff then
					self.npc_display:OnRemoveGameObject(self.bind_eff)
					self.bind_eff = nil
				end
				self.bind_loader:Destroy()
				self.bind_loader = nil
			end
		else
			self.injured = false
		end

	elseif talk_type == Talk_Type.Baby_Talk then
		local soul_obj = main_role:GetSoulBoyObj()
		if not soul_obj or not soul_obj:GetVo() then return end

		if soul_obj:IsSoulBoy() then
			bundle, asset = ResPath.GetSoulBoyModel(soul_obj:GetVo().soulboy_lt_id)
		else
			bundle, asset = ResPath.GetHaiZiModel(soul_obj:GetVo().use_baby_id)
		end
		self.npc_display:SetMainAsset(bundle, asset, function()
			if self.npc_display ~= nil then
				self.npc_display:SetIsLerpProbe(true)
			end
		end)
	elseif talk_type == Talk_Type.Player_Talk then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true,
										ignore_waist = true, ignore_mask = true, ignore_shouhuan = true, ignore_tail = true, ignore_jianzhen = true}
		self.npc_display:SetModelResInfo(role_vo, special_status_table)
		if self.npc_display ~= nil then
			self.npc_display:SetIsLerpProbe(true)
		end
	end
end

function NpcDialog:ModelFunc(talk_type, part, obj)
	-- body
	self.npc_display:SetLoadComplete(nil)
	if not obj then return end
	local transform = self.npc_display:GetDrawObj():GetAttachPoint(AttachPoint.UI)
	local default_y = 2.4
	local pos_y = -default_y
	local npc_config = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[self.npc_id]
	if not npc_config then--and talk_type == Talk_Type.Npc_Talk then
		return
	end
	if transform then
		if 5021001 == npc_config.resid then -- 经验小妖
			pos_y = -1.5
		elseif default_y > transform.localPosition.y then
			pos_y = pos_y + (default_y - transform.localPosition.y) * 2
		elseif transform.localPosition.y / default_y > 2 then
			pos_y = default_y * -1.6
		end
	end
end

function NpcDialog:SetStoryNpcId(npc_id, story_talk_end_callback)
	self.story_talk_end_callback = story_talk_end_callback
	self:Flush(0, "all", {npc_id = npc_id})
end

function NpcDialog:SetNpcId(npc_id)			 --一次一个
	local npc = Scene.Instance:GetNpcByNpcId(npc_id)
	if npc then
		npc_rotation = npc:GetRotation()
		self.npc_position = npc:GetRoot().transform.position
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then return end

	local task_cfg = TaskWGData.Instance:GetNpcOneExitsTask(self.npc_id)
	if task_cfg then
		GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_GUIDE_DIALOG, false)
	end

	self.node_list["btn_double"]:SetActive(false)
	self.node_list["ani_parent"]:SetActive(true)
	self.node_list["TimeText"]:SetActive(npc_id ~= GameEnum.Qicheng_Npc) --比翼双飞的按钮位置特殊,需要隐藏时间文本

	SafeBaseView.SetOnlyView(GuideModuleName.TaskDialog)
	self:DoOpenAni()
end

function NpcDialog:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			local ui = self.tabbar:GetToggleByIndex(tab_index)
			return ui, BindTool.Bind(self.ChangeToIndex, self, tab_index)
		end
	elseif ui_name == GuideUIName.NpcDialogLayoutBtn then
		return self.node_list["layout_btn"], BindTool.Bind(self.DoDialog, self, true)
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function NpcDialog:DoOpenAni()
	self.node_list.layout_bottom.bottom_panel:SetActive(true)
	-- UITween.MoveShowPanel(self.node_list.layout_bottom.layout_bottom, Vector3(0, -180, 0), 0.3, DG.Tweening.Ease.InOutSine)
end

function NpcDialog:DoModelAni()
	-- UITween.MoveShowModel(self.npc_display.draw_obj.root.gameObject, Vector3(0, -2, 0), 0.3, DG.Tweening.Ease.InOutSine)
end

function NpcDialog:GetNpcId()
	return self.npc_id
end

function NpcDialog:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			if v ~= nil and v.npc_id ~= nil and v.npc_id ~= "" and v.npc_id ~= 0 then
				self.npc_id = tonumber(v.npc_id)
			end

			local npc_cfg = TaskWGData.Instance:GetNPCConfig(self.npc_id)
			if not npc_cfg then
				print_error("Error NPC ID For NpcDialog: ", self.npc_id, TaskWGData.Instance:CurrTaskId())
				return
			end

			self:SetNpcId(self.npc_id)
			if self:IsTianShenRoad(self.npc_id) then
				return
			end

			local flag = self:ShowNpcTask()
			if not flag then
				self:ShowNpcFun()
			end

			self:FlushTransfer(param_t)
			self:CheckSubmission()
			self:FlushXMZ()
			self:FlushCrossLongMai()
			self:FlushZhuoGui()

		elseif k == "gather_stop" then
			self:ResetGather()

		elseif k == "model_camera_set" then
			self:OnModelCameraSet(v)
		end
	end
end

function NpcDialog:CheckSubmission()
	if not self.is_transfer then
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type ~= nil then
			local npc_id = nil
			local num = OperationTaskChainWGData.Instance:GetOldCanSubmission()
			
			if scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
				npc_id = OperationTaskChainWGData.Instance:GetXunLuoCaiJiOperaInfo()
			elseif scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
				npc_id = OperationTaskChainWGData.Instance:GetCaiJiOperaInfo()
			end

			if npc_id ~= nil and npc_id == self.npc_id and num ~= nil and num > 0 then
				local submission_data = OperationTaskChainWGData.Instance:GetSubmissionData()
				
				self.is_send_submission = false
				self:ClearTimer()
				self:SetSubmissionTimer(submission_data.btn_str)
				self:FlushSubmission(submission_data)
			else
				self:ClearSubmissionTimer()
				self.node_list.layout_submission:SetActive(false)
			end
		end
	end
end

function NpcDialog:SetName(talk_type)
	if 0 == self.npc_id then
		return 
	end
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local main_role = Scene.Instance:GetMainRole()
	local text_name = nil

	if talk_type == Talk_Type.Npc_Talk then
		local npc_cfg = TaskWGData.Instance:GetNPCConfig(self.npc_id)
		if not npc_cfg then
			return 
		end
		text_name = npc_cfg.show_name
	elseif talk_type == Talk_Type.Baby_Talk then
		if main_role then
			local soul_obj = main_role:GetSoulBoyObj()
			
			if soul_obj and soul_obj:GetVo() then
				text_name = soul_obj:GetVo().name
			end
		end
	elseif talk_type == Talk_Type.Player_Talk then
		text_name = main_role_vo.name
	end
	self.node_list.text_role_name:SetActive(talk_type == Talk_Type.Player_Talk)
	self.node_list.text_npc_name:SetActive(talk_type ~= Talk_Type.Player_Talk)
	if talk_type == Talk_Type.Player_Talk then
		self.node_list["text_role_name"].text.text = text_name
	else
		self.node_list["text_npc_name"].text.text = text_name
	end
end

function NpcDialog:ShowNpcFun()
	local npc_cfg = TaskWGData.Instance:GetNPCConfig(self.npc_id)--ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[self.npc_id]
	if npc_cfg == nil or npc_cfg.fun == "" then return false end
	if FunOpen.Instance:GetFunIsOpened(npc_cfg.fun) then
		if npc_cfg.fun == FunName.Marry then
			local lover_id = RoleWGData.Instance.role_vo.lover_uid
			if lover_id > 0 then
				local wedding = MarryWGData.Instance:GetQyFbInfo() or {}
				if wedding.is_hunyan_already_open ~= nil and wedding.is_hunyan_already_open == 0 then
					self:UpdteBtnWord(Language.Task.marry_wedding)
				end
			end
		elseif npc_cfg.fun == FunName.Divorce then
			local lover_id = RoleWGData.Instance.role_vo.lover_uid
			if lover_id > 0 then
				self:UpdteBtnWord(Language.Task.divorce_task)
			end
		end
	end
end

function NpcDialog:UpdteBtnWord(name)
	self.node_list["img_btn_word"].text.text = name
end

function NpcDialog:ShowNpcTask()
	local npc_cfg = TaskWGData.Instance:GetNPCConfig(self.npc_id)
	if npc_cfg == nil then
		return false 
	end

	local exits_task = TaskWGData.Instance:GetNpcOneExitsTask(self.npc_id)
	local talk_id = npc_cfg.talkid

	self.npc_status = exits_task ~= nil and TaskWGData.Instance:GetTaskStatus(exits_task.task_id) or GameEnum.TASK_STATUS_NONE
	-- self.node_list["dialogskip"]:SetActive(exits_task ~= nil)

	local is_visible = false
	local scene_logic = Scene.Instance:GetSceneLogic()
	local fuben_task_tab = scene_logic:GetNpcTaskNeedAloneLogic(self.npc_id)

	--npc奖励
	self:HideReward()
	if exits_task ~= nil then
		self.task_id = exits_task.task_id
		-- self:ShowReward()
	else
		self.task_id = 0
		-- self:HideReward()
	end

	if self.npc_status == GameEnum.TASK_STATUS_CAN_ACCEPT then			--有可接任务
		--print_error("没有可提交任务1")
		-- self:ShowEffect(true)
		self:UpdteBtnWord(Language.Task.accept_task)
		is_visible = true
		if exits_task then
			talk_id = exits_task.accept_dialog
		end

		-- self:ShowReward()
	elseif self.npc_status == GameEnum.TASK_STATUS_COMMIT then			--有可提交任务
		--print_error("有可提交任务")
		-- self:ShowEffect(true)
		self:UpdteBtnWord(Language.Task.commit_task)
		is_visible = true
		if exits_task then
			talk_id = exits_task.commit_dialog
		end

		-- self:ShowReward()
	elseif fuben_task_tab and fuben_task_tab.npc_id == self.npc_id then   -- 副本特殊任务逻辑
		self:UpdteBtnWord(fuben_task_tab.btn_name)
		is_visible = true
		if fuben_task_tab.not_time then
			is_visible = false
		end
	else
		self:ShowEffect(false)
		self:UpdteBtnWord(Language.Task.end_talk)
		self:SetBtnActive(false)
		is_visible = false
	end
	self.guide_state = self.npc_status

	self.time = 5
	----------------------------------------------------
	-- 自动接受任务 or 自动完成任务 倒计时
	if is_visible then
		self:SetTimer()
	end
	----------------------------------------------------
	if fuben_task_tab and fuben_task_tab.btn_time then
		self:SetBtnTextTimer(fuben_task_tab.btn_time)
	end

	self.cur_index = 0
	self:UpdateTalkContent(talk_id)

	return exits_task
end

function NpcDialog:ClearTimer()
	if self.renwu_countdown then
		GlobalTimerQuest:CancelQuest(self.renwu_countdown)
		self.renwu_countdown = nil
	end

	if self.time_text then
		self.time_text.text.text = ""
	end
end

function NpcDialog:SetTimer(ignore_time_text)
	self.time = cd_time
	if self.renwu_countdown then
		GlobalTimerQuest:CancelQuest(self.renwu_countdown)
		self.renwu_countdown = nil
	end
	self.ignore_time_text = ignore_time_text
	local remain_time = cd_time
	self.time_text.text.text = string.format(Language.Task.TaskTime, remain_time)
	self.renwu_countdown = GlobalTimerQuest:AddTimesTimer(self.update_time, 1, remain_time)
end

function NpcDialog:UpdateTime()
	self.time = self.time - 1
	if self.time > 0 and nil ~= self.time_text then
		if self.ignore_time_text then
			self.time_text.text.text = ""
		else
			self.time_text.text.text = string.format(Language.Task.TaskTime, math.ceil(self.time))
		end
	else
		self:DoDialog()
		self.time = cd_time
	end
end

function NpcDialog:ClearSubmissionTimer()
	if self.submission_countdown then
		GlobalTimerQuest:CancelQuest(self.submission_countdown)
		self.submission_countdown = nil
	end
end

function NpcDialog:SetSubmissionTimer(str)
	self.submission_time = cd_time

	if self.submission_countdown then
		GlobalTimerQuest:CancelQuest(self.submission_countdown)
		self.submission_countdown = nil
	end

	if self.node_list.str_submission then
		self.node_list.str_submission.text.text = string.format(Language.OpertionAcitvity.TaskChain.BtnSubmissionStr, str or "", self.submission_time)
	end

	local remain_time = cd_time
	self.submission_countdown = GlobalTimerQuest:AddTimesTimer(BindTool.Bind(self.UpdateSubmissionTime, self, str), 1, remain_time)
end

function NpcDialog:UpdateSubmissionTime(str)
	self.submission_time = self.submission_time - 1
	if self.submission_time > 0 and nil ~= self.node_list.str_submission then
		self.node_list.str_submission.text.text = string.format(Language.OpertionAcitvity.TaskChain.BtnSubmissionStr, str or "", math.ceil(self.submission_time))
	else
		self.submission_time = cd_time
		self:OnClickSubmission()
	end
end

function NpcDialog:SendSubmission()
	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId()
	if scene_type ~= nil then
		if scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
			local npc_id = OperationTaskChainWGData.Instance:GetXunLuoCaiJiOperaInfo()
			if npc_id ~= nil and npc_id == self.npc_id then
				OperationTaskChainWGCtrl.Instance:SendTaskChainHuSongCaiJiCommit()
			end
		elseif scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
			local npc_id = OperationTaskChainWGData.Instance:GetCaiJiOperaInfo()		
			if npc_id ~= nil and npc_id == self.npc_id then
				OperationTaskChainWGCtrl.Instance:SendTaskChainCaiJiCommit()
			end
		end
	end
end

function NpcDialog:OnClickSubmission()
	self.is_send_submission = true
	self:SendSubmission()
	self:Close()
end

--显示特殊的npc对话内容
function NpcDialog:ShowSpecialTalkContent()
	if Scene.Instance:GetSceneType() == SceneType.XianMengzhan then
		local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
		local side = role_info_list and role_info_list.side or 0
		local cfg = GuildBattleRankedWGData.Instance:GetMonsterCfgBySide(side)
		if cfg and cfg.npc_id == self:GetNpcId() then
			local is_xmz_car = Scene.Instance:GetMainRole():IsXMZCar()
			if is_xmz_car then
				local gather_id = role_info_list and role_info_list.gather_id
				if gather_id and gather_id > 0 then
					self.talk_content = {cfg.give_npc_talk}
				else
					self.talk_content = {cfg.not_stone_npc_talk}
				end
			else
				self.talk_content = {cfg.bs_npc_talk}
			end
			self.cur_index = 1
			self:ShowTalk()
			return true
		end
	end
	return false
end

--对话内容
function NpcDialog:UpdateTalkContent(talk_id)
	if self:ShowSpecialTalkContent() then
		return
	end

	local npc = Scene.Instance:GetNpcByNpcId(self.npc_id)

	if talk_id == nil or talk_id == "" then
		local npc_cfg = TaskWGData.Instance:GetNPCConfig(self.npc_id)
		if npc_cfg ~= nil then
			talk_id = npc_cfg.talkid or 0
		end
	end

	local talk_cfg = ConfigManager.Instance:GetAutoConfig("npc_talk_list_auto").npc_talk_list[talk_id]
	if not talk_cfg then
		print_error("没有找到对话配置，请检查，talk_id:" .. talk_id)
		return
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local talk_text = talk_cfg["talk_text_" .. role_vo.sex]
	if not talk_text then
		talk_text = ""
	end
	self.talk_content = Split(talk_text, "||")

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic then
		local fuben_task_tab = scene_logic:GetNpcTaskNeedAloneLogic(self.npc_id)
		if fuben_task_tab and fuben_task_tab.talk_text then
			self.talk_content = Split(fuben_task_tab.talk_text , "||")
		end
	end

	self.cur_index = self.cur_index + 1
	TalkCache.SetTalkAudioStr(nil, nil)

	--talk_audio字段组合规则：(1)音效资源id_(2)第几段对话_(3)性别标识
	--(3)性别标识 是只有当匹配到PLAYER_TALK字段时才会使用，分别是0（女）、1（男），默认为0
	--若匹配到 PLAYER_TALK，则第(3)个标识需要读取当前玩家的性别
	if talk_cfg ~= nil then
		if talk_cfg.talk_audio ~= nil then
			--切割有多少个具体音效
			self.split_talk_audio_list = {}
			local list = Split(talk_cfg.talk_audio, "#")
			for i, v in ipairs(list) do
				if v ~= nil and v ~= "" then
					local once_audio = Split(v, "_")
					local index = tonumber(once_audio[2])
					if index then
						if string.match(v, PLAY_TALK_AUDIO) then
							local audio, _ = string.gsub(v, PLAY_TALK_AUDIO, "")
							self.split_talk_audio_list[index] = audio .. "_" .. RoleWGData.Instance:GetRoleSex()
						else
							self.split_talk_audio_list[index] = v
						end
					end
				end
			end
		else
			TalkCache.SetTalkAudioStr(nil, nil)
		end
	end

	self:ShowTalk()
end

--检测当前对话索引是否有音效资源，如果有，加入缓存，没有，清空缓存
function NpcDialog:HandleCurIndexHasAudio()
	if not self.split_talk_audio_list or IsEmptyTable(self.split_talk_audio_list) then
		return
	end

	if self.split_talk_audio_list[self.cur_index] ~= nil and self.split_talk_audio_list[self.cur_index] ~= "" then
		local str =  Split(self.split_talk_audio_list[self.cur_index], "_")
		local folder = str[1]
		local idx = tonumber(str[2])
		if idx == self.cur_index then
			TalkCache.SetTalkAudioStr(folder, idx)
			if TalkCache.GetTalkAudioStr() == "" then
				TalkCache.SetTalkAudioStr(nil, nil)
			end
		else
			TalkCache.SetTalkAudioStr(nil, nil)
		end
	else
		TalkCache.SetTalkAudioStr(nil, nil)
	end
end

function NpcDialog:ShowTalk()
	self:ClearSubmissionTimer()

	if not self.talk_content or 0 == #self.talk_content then
		return false
	end

	if not self.talk_content[self.cur_index] then
		return
	end

	self:HandleCurIndexHasAudio()
	local talk_content = self.talk_content[self.cur_index]--table.remove(self.talk_content, 1)
	local role_vo = GameVoManager.Instance:GetMainRoleVo()

	if string.match(talk_content, PLAYER_NAME) then
		talk_content = string.gsub(talk_content, PLAYER_NAME, role_vo.name)
	end
	
	self.old_index = self.cur_index
	local talk_type = Talk_Type.None
	local audio_key
	local sex = 0
	if string.match(talk_content, PLAYER_TALK) then
		talk_content = string.gsub(talk_content, PLAYER_TALK, "")
		talk_type = Talk_Type.Player_Talk
		sex = RoleWGData.Instance:GetRoleSex()
	elseif string.match(talk_content, BABY_TALK) then
		talk_content = string.gsub(talk_content, BABY_TALK, "")
		talk_type = Talk_Type.Baby_Talk
	else
		talk_content = string.gsub(talk_content, NPC_TALK, "")
		talk_type = Talk_Type.Npc_Talk
	end
	local next_prof = -1

	if self.talk_content[self.cur_index + 1] ~= nil  then
		if string.match(self.talk_content[self.cur_index + 1], PLAYER_TALK) then
			next_prof = RoleWGData.Instance:GetRoleProf()
		elseif string.match(self.talk_content[self.cur_index + 1], BABY_TALK) then
			next_prof = 0
		elseif string.match(self.talk_content[self.cur_index + 1], NPC_TALK) then
			next_prof = -1
		end
	else
		next_prof = -1
	end
	talk_content = CommonDataManager.ParseTagContent(talk_content, 21)
	self:PlayTalk(talk_content)
	self:SetName(talk_type)
	self:FlushModel(talk_type)

    self.old_npc_id = self.npc_id
    self.old_talk_type = talk_type

    self.node_list["layout_submission"]:SetActive(false)

	local talk_audio_cache, talk_subsection = TalkCache.GetTalkAudioStr()
	if talk_audio_cache and talk_subsection then
		--音效
		_, audio_key = ResPath.GetNpcTalkVoiceRes(talk_audio_cache, talk_subsection, sex)
        if old_audio_asset ~= audio_key then
        	old_audio_asset = audio_key
			TalkCache.PlayNpcTalkAudio(talk_audio_cache, talk_subsection, sex)
		end
	end

	-- param_4:1,购买物品；2,提交物品；3,骰子任务
	-- 当param_4为1或2时，param_5为奖励：“物品id，数量”
	local task_cfg = TaskWGData.Instance:GetTaskConfig(self.task_id)
	-- if not self.talk_content[self.cur_index + 1] and task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_ZHU
	-- and (task_cfg.c_param4 == 1 or task_cfg.c_param4 == 2 or task_cfg.c_param4 == 3) and self.npc_status == GameEnum.TASK_STATUS_COMMIT then
	-- 	if task_cfg.c_param4 == 3 then
	-- 		TaskGuide.Instance:CanAutoAllTask(false)
	-- 		TaskWGCtrl.Instance:OpenTaskDice(task_cfg)
	-- 		self:Close()
	-- 		return true
	-- 	else
	-- 		self.time_text.text.text = ""
	-- 		self:SetTimer(true)
	-- 		self:SetBtnActive(false)
	-- 		self.node_list["ani_parent"]:SetActive(false)
	-- 		self:ShowEffect(false)
	-- 		self.node_list["buy_panel"]:SetActive(true)
	-- 		if self.buy_item == nil then
	-- 			self.buy_item = NpcDialogItem.New(self.node_list["buy_panel"])
	-- 			self.buy_item:SetClickCallBack(BindTool.Bind(self.DoDialog, self))
	-- 		end
	-- 		local reward_t = Split(task_cfg.c_param5, ",")
	-- 		self.buy_item:SetData({reward = {item_id = tonumber(reward_t[1]), num = tonumber(reward_t[2])}, param = task_cfg.c_param4})
	-- 	end
	-- else
	if task_cfg and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_22 then
		self.time_text.text.text = ""
		self:SetTimer(true)
		self:SetBtnActive(false)
		self.node_list["ani_parent"]:SetActive(false)
		self:ShowEffect(false)
		self.node_list["buy_panel"]:SetActive(true)
		if self.buy_item == nil then
			self.buy_item = NpcDialogItem.New(self.node_list["buy_panel"])
			self.buy_item:SetClickCallBack(BindTool.Bind(self.DoDialog, self))
		end
		local reward_t = Split(task_cfg.c_param5, ",")
		local param = self.npc_status == GameEnum.TASK_STATUS_CAN_ACCEPT and 1 or 0
		self.buy_item:SetData(
				{reward = {item_id = tonumber(reward_t[1]), num = tonumber(reward_t[2])},
				 param = param,
				 money_type = task_cfg.c_param1,
				 money = task_cfg.c_param2,
		})
	else
		self.node_list["buy_panel"]:SetActive(false)
		if self.story_talk_end_callback then
			self:SetTimer()
			self:SetBtnActive(false)
			-- self.node_list["continue_btn"]:SetActive(true)
			self.node_list["ani_parent"]:SetActive(true)
			-- self:HideReward()
			self:ShowEffect(false)
			self:UpdteBtnWord(Language.Task.click_continue)
		elseif self.npc_status == GameEnum.TASK_STATUS_NONE or self.npc_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
			self:SetTimer()
			self:SetBtnActive(true)
			-- self.node_list["continue_btn"]:SetActive(false)
			self.node_list["ani_parent"]:SetActive(false)
			-- self:HideReward()
			self:ShowEffect(false)

			local scene_logic = Scene.Instance:GetSceneLogic()
			if scene_logic then
				local fuben_task_tab = scene_logic:GetNpcTaskNeedAloneLogic(self.npc_id)
				if not fuben_task_tab then
					self:UpdteBtnWord(Language.Task.end_talk)
					self:SetBtnActive(false)
					self.node_list["ani_parent"]:SetActive(true)
				end
			end
		elseif 0 == #self.talk_content then
			self:SetBtnActive(true)
			-- self.node_list["continue_btn"]:SetActive(false)
			self.node_list["ani_parent"]:SetActive(false)
			if self.npc_status == GameEnum.TASK_STATUS_CAN_ACCEPT then
				self:ShowEffect(true)
				self:UpdteBtnWord(Language.Task.accept_task)
				-- self:HideReward()
			elseif self.npc_status == GameEnum.TASK_STATUS_COMMIT then
				self:ShowEffect(true)
				self:UpdteBtnWord(Language.Task.commit_task)
				-- self:ShowReward()
			else
				self:ShowEffect(false)
				self:UpdteBtnWord(Language.Task.end_talk)
				-- self:HideReward()
				self:SetBtnActive(false)
			end
		else
			self:SetTimer()
			self:SetBtnActive(false)
			-- self.node_list["continue_btn"]:SetActive(true)
			self.node_list["ani_parent"]:SetActive(true)
			-- self:HideReward()
			self:ShowEffect(false)
			self:UpdteBtnWord(Language.Task.click_continue)
		end
	end

	if self.npc_status == GameEnum.TASK_STATUS_COMMIT then
		if self.cur_index == #self.talk_content then
			-- self:ShowReward()
		else
			self:HideReward()
		end
	else
		self:HideReward()
	end

	if task_cfg and self.npc_status == GameEnum.TASK_STATUS_CAN_ACCEPT and
		task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_7 and
		task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_66 then
			self.node_list["btn_double"]:SetActive(self.cur_index == #self.talk_content)
			self.node_list["ani_parent"]:SetActive(self.cur_index ~= #self.talk_content)

	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic then
		local fuben_task_tab = scene_logic:GetNpcTaskNeedAloneLogic(self.npc_id)

		if fuben_task_tab and fuben_task_tab.hide_btn and self.npc_id == fuben_task_tab.npc_id then
			self:SetBtnActive(fuben_task_tab.hide_btn == 0)
		end
	end

	self.cur_index = self.cur_index + 1
	return true
end

function NpcDialog:SetBtnActive(is_active)
	if not self.node_list["layout_btn"] or IsNil(self.node_list["layout_btn"].gameObject) then
		return
	end

	self.node_list["layout_btn"]:SetActive(is_active)
end

function NpcDialog:PlayTalk(talk_content)
	if self.play_talk_time then
		GlobalTimerQuest:CancelQuest(self.play_talk_time)
		self.play_talk_time = nil
	end

	if 0 == #talk_content then return end
	-- talk_content = talk_content .. "/5" 测试表情
	local talk_len, list = StringLen(talk_content)
	self.update_talk_content = list
	if 0 == #list then return end
	self.talk_index = 0
	self.play_talk_time = GlobalTimerQuest:AddTimesTimer(self.update_talk, 0.02, talk_len)
end

local color_start_len = 17 -- <color=#469cff> 的长度
local color_end_len = 8 -- </color> 的长度
local color_flag = ">"
local end_color_flag = "</color>"
local match_flag = "<color=(.-)</color>"
-- 有颜色文字一起出现
function NpcDialog:CalculationIndex()
	-- body
	local index = self.talk_index
	if index > #self.update_talk_content then return end
	local len = string.len(self.update_talk_content[self.talk_index])
	if len >= 3 then return end -- 中文
	index = self.talk_index + color_start_len - 1
	if index > #self.update_talk_content then return end
	len = string.len(self.update_talk_content[index])
	if len >= 3 then return end -- 中文

	index = index + color_end_len
	if index > #self.update_talk_content then return end
	while(index <= #self.update_talk_content) do
		if self.update_talk_content[index] == color_flag then
			-- self.talk_index = index
			break
		end
		index = index + 1
	end

	local i = self.talk_index
	local str = ""
	while(i <= index) do
		if self.update_talk_content[i] then
			str = str .. self.update_talk_content[i]
		end
		i = i + 1
	end

	local m,n = string.find(str, match_flag)
	if not m then return end
	self.talk_index = index
end

function NpcDialog:UpdateTalk()
	self.talk_index = self.talk_index + 1
	self:CalculationIndex()
	local str = ""
	for i=1,self.talk_index do
		if self.update_talk_content[i] then
			str = str .. self.update_talk_content[i]
		end
	end

	local text = ChatWGData.Instance:FormattingMsg(str)
	EmojiTextUtil.ParseRichText(self.talk_txt.tmp, text, 22)
	local bounds = TMPUtil.GetTMPBounds(self.talk_txt.tmp, self.talk_txt.tmp.text, 564)
	-- 设置文本宽高
	local tmp_rect = self.talk_txt:GetComponent(typeof(UnityEngine.RectTransform))
	tmp_rect.sizeDelta = bounds
end

function NpcDialog:StopTaskDialogAudio()
	local npc_talk_audio = TalkCache.GetNpcTalkAudio()
	if npc_talk_audio then
		self.split_talk_audio_list = {}
		AudioManager.StopAudio(npc_talk_audio)
		TalkCache.SetNpcTalkAudio(nil)
	end
end

function NpcDialog:HideReward()
	if self.layout_reward then
		self.layout_reward:SetActive(false)
	end
end

function NpcDialog:ShowReward()
	local task_cfg = TaskWGData.Instance:GetTaskConfig(self.task_id)
	if not task_cfg then return end

	--仙盟建设任务不做奖励展示
	if task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
		return
	end

	if self.layout_reward then
		self.layout_reward:SetActive(true)
	end

	local reward_list = TaskWGData.Instance:GetTaskReward(self.task_id)
	if not reward_list or #reward_list < 1 then
		self:HideReward()
		return
	end

	for i=1,3 do
		local v = reward_list[i]
		if self.reward_cell_list[i] and v and v.item_id then
			self.reward_cell_list[i]:SetData(v)
			self.node_list["layout_reward_item" .. i]:SetActive(true)
		else
			self.node_list["layout_reward_item" .. i]:SetActive(false)
		end
	end
end

function NpcDialog:SkipDialog()
	self.talk_content = {}
	self:DoDialog()
end

-- 检查任务消耗钱币
function NpcDialog:CheckMoney(task_cfg, need_tips)
	-- body
	need_tips = true
	local flag = true
	local str
	if not task_cfg or task_cfg.condition ~= GameEnum.TASK_COMPLETE_CONDITION_22 then return flag end
	local role_info = RoleWGData.Instance:GetRoleInfo()
	if task_cfg.c_param1 == Shop_Money_Type.Type1 then
		flag = role_info.gold > task_cfg.c_param2
		if not flag then
			str = Language.Shop.MoneyDes1
		end
	elseif task_cfg.c_param1 == Shop_Money_Type.Type2 then
		flag = role_info.bind_gold + role_info.gold > task_cfg.c_param2
		if not flag then
			str = Language.Shop.MoneyDes2
		end
	elseif task_cfg.c_param1 == Shop_Money_Type.Type4 then
		flag = role_info.shengwang > task_cfg.c_param2
		if not flag then
			str = Language.Shop.MoneyDes4
		end
	elseif task_cfg.c_param1 == Shop_Money_Type.Type5 then
		flag = role_info.coin > task_cfg.c_param2
		if not flag then
			str = Language.Shop.MoneyDes5
		end
	end

	if need_tips and str then
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	end
	return flag
end

function NpcDialog:DoDialog(is_click_btn)
	if self:ShowTalk() then return end
	local is_task_fun = false
	if self.task_id ~= nil then
		local task_cfg = TaskWGData.Instance:GetTaskConfig(self.task_id)
		local is_guild_build, task_status = self:SendGuildBuildReq(task_cfg)

		if is_guild_build and self.npc_status ~= GameEnum.TASK_STATUS_CAN_ACCEPT then
			if task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
				GuildWGCtrl.Instance:SendGuildBuildTaskStatesChange(self.task_id)
				is_task_fun = true
				self:Close()
				return
			elseif task_status == GameEnum.TASK_STATUS_COMMIT then
				is_task_fun = true
				self:Close()
				return
			end
		end

		if self.npc_status == GameEnum.TASK_STATUS_CAN_ACCEPT then
			-- if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_22 and not is_click_btn then
			-- 	-- 这个任务需要点按钮接取
			--
			-- 	return
			-- end

			-- if task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN and
			-- TransFerWGData.Instance:GetCurTaskIsCurStageFirst(self.task_id) then
			-- 	local callback = function ()
   --          		if self.task_id then
			-- 			TaskWGCtrl.Instance:SendTaskAccept(self.task_id)
			-- 			return self.task_id
   --          		end
   --          	end
			-- 	TransFerWGCtrl.Instance:Open(TRANSFER_TYPE.TON, {npc_dialog_callback = callback})
			-- else
			if self:CheckMoney(task_cfg) then
				if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
					local task_id = TaskWGData.Instance:GetCurrBountyTask()

					if task_id == nil then
						ViewManager.Instance:Open(GuideModuleName.TaskShangJinView)
					else
						TaskWGCtrl.Instance:SendTaskAccept(self.task_id)	
					end
				else
					TaskWGCtrl.Instance:SendTaskAccept(self.task_id)
				end
			end

			is_task_fun = true
		elseif self.npc_status == GameEnum.TASK_STATUS_COMMIT then
			NpcDialog.LAST_COMMIT_TASK = self.task_id
			TaskWGCtrl.Instance:SendTaskCommit(self.task_id)
			is_task_fun = true
		end
	end



	-- 副本任务特殊逻辑
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic then
		local fuben_task_tab = scene_logic:GetNpcTaskNeedAloneLogic(self.npc_id)

		if fuben_task_tab and fuben_task_tab.npc_id == self.npc_id then
			if fuben_task_tab.only_click_btn_call_back then
				if is_click_btn then
					fuben_task_tab.callback()
				end
			else
				fuben_task_tab.callback()
			end
		end
	end

	if not is_task_fun then
		local npc_cfg = TaskWGData.Instance:GetNPCConfig(self.npc_id)--ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[self.npc_id]
		if npc_cfg ~= nil then
			if npc_cfg.fun == FunName.Marry then
				local lover_id = RoleWGData.Instance.role_vo.lover_uid
				if lover_id > 0 then
					local wedding = MarryWGData.Instance:GetQyFbInfo() or {}
					if wedding.is_hunyan_already_open ~= nil and wedding.is_hunyan_already_open == 0 then
						-- 用来处理开启婚宴的数据 进入婚宴弹窗二次确认。
						if nil == self.marry_hunyan_alert then
							self.marry_hunyan_alert = Alert.New()
						end
						self.marry_hunyan_alert:SetLableString(Language.Wedding.IsOpenHunYan)
						self.marry_hunyan_alert:SetOkFunc(
							function()
								WeddingWGData.Instance:OpenHunyanHandler()
							end)
						self.marry_hunyan_alert:Open()
						return
					end
				end
			elseif npc_cfg.fun == FunName.Divorce then
				local lover_id = RoleWGData.Instance.role_vo.lover_uid
				if lover_id > 0 then
					local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
					if other_cfg ~= nil then
						local divorce_coin_cost = other_cfg.divorce_coin_cost or 0
						local divorce_qingyuan_dec = other_cfg.divorce_qingyuan_dec or 0
						local divorce_intimacy_dec = other_cfg.divorce_intimacy_dec or 0
						local divorce_name = RoleWGData.Instance.role_vo.lover_name or ""

						if divorce_coin_cost > 10000 then
							divorce_coin_cost = math.floor(divorce_coin_cost / 10000) .. Language.Common.Wan
						end

						-- 离婚弹窗二次确认。
						if nil == self.marry_divorce_alert then
							self.marry_divorce_alert = Alert.New()
						end
						self.marry_divorce_alert:SetLableString(string.format(Language.Marry.DivorceConfirm, divorce_coin_cost, divorce_name, divorce_qingyuan_dec, divorce_intimacy_dec))
						self.marry_divorce_alert:SetOkFunc(
							function()
								MarryWGCtrl.Instance.SendCSQingyuanDivorceReqCS()
							end)
						self.marry_divorce_alert:Open()
					end
				end
			end
		end
	end

	if self.is_transfer then
		self:GotoTransferFB()
		return
	end
	TaskGuide.Instance:SpecialConditions(true, 0.5) --防止任务未返回时弹出普通框 0.5s
	self:Close()
end

function NpcDialog:CloseCallBack()
	SafeBaseView.UpdateScreenShot()
	MainuiWGCtrl.Instance:FlushView(0, "duihua_mian_ui_state", {false})
	old_audio_asset = nil

	if self.node_list.layout_bottom then
		self.node_list.layout_bottom.bottom_panel:SetActive(false)
	end
	-- CLOSE_TIME = Status.NowTime
	ViewManager.Instance:RemoveCanInactiveView(GuideModuleName.MainUIView)
	FightText.Instance:SetActive(true)
	SafeBaseView.SetOnlyView(nil)
	self.npc_position = nil

	-- if self.scene_obj_shield_rule then
	-- 	self.scene_obj_shield_rule:DeleteMe()
	-- 	self.scene_obj_shield_rule = nil
	-- end

	-- if self.follow_ui_shield_rule then
	-- 	self.follow_ui_shield_rule:DeleteMe()
	-- 	self.follow_ui_shield_rule = nil
	-- end

	-- if self.shadow_shield_rule then
	-- 	self.shadow_shield_rule:DeleteMe()
	-- 	self.shadow_shield_rule = nil
	-- end

	if nil ~= self.story_talk_end_callback then
		self.story_talk_end_callback()
		self.story_talk_end_callback = nil
	end

	if self.transfer_delay_event then
		GlobalTimerQuest:CancelQuest(self.transfer_delay_event)
		self.transfer_delay_event = nil
	end

	local npc_status = self:GetNpcTaskStatus()
	if npc_status ~= GameEnum.TASK_STATUS_NONE then
		-- 只有在该任务类型是对话的情况下才发送这个请求
		local task_cfg = TaskWGData.Instance:GetTaskConfig(self.task_id)
		if task_cfg ~= nil and task_cfg.condition ~= "" and (task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_0 or task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_19) then
			TaskWGCtrl.Instance:SendTaskNpcTalk(self.npc_id)
		end
	end

    local npc = Scene.Instance:GetNpcByNpcId(self.npc_id)
    if npc then
		if npc:IsWalkNpc() then
			npc:Continue()
		elseif npc_rotation then
			npc:AutoRotation(true)
		end

		-- npc:SetRotation(npc_rotation)
		-- local shadow_obj = npc:GetShadow()
		-- if shadow_obj then
		-- 	shadow_obj:CancelForceSetVisible()
		-- end
	end

	if npc then
		npc:CancelSelect()
	end

 	if self.renwu_countdown then
		GlobalTimerQuest:CancelQuest(self.renwu_countdown)
		self.renwu_countdown = nil
	end

	if self.play_talk_time then
		GlobalTimerQuest:CancelQuest(self.play_talk_time)
		self.play_talk_time = nil
	end

	CountDownManager.Instance:RemoveCountDown("auto_enter_tianshenroad_fb")

	if self.time_text then
		self.time_text.text.text = ""
	end

	local old_state = self.is_send_submission
	self.is_send_submission = true
	if not old_state then
		self:SendSubmission()
	end

	npc_rotation = nil
	self.npc_id = 0
	self.guide_state = 0
	TaskWGData.Instance:UnNotifyDataChangeCallBack(self.update_func)
	self.ignore_t = nil
	--self:StopTaskDialogAudio()

	self:ClearGiveXMZStoneTimer()
end

function NpcDialog:GetNpcTaskStatus()
	return self.npc_status
end

function NpcDialog:GetTaskId()
	return self.task_id
end

function NpcDialog:UpdateTaskData(task_id, reason)
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if not task_cfg or task_cfg.task_type ~= TaskGuide.Instance:CurrTaskType() then return end
	local flag = self:ShowNpcTask()
	if not flag then self:ShowNpcFun() end
end

function NpcDialog:ShieldSceneObj(obj)
	local shield = true
	if self.ignore_t then
		if self.ignore_t[obj:GetObjId()] then
			shield = false
		end
	end

	if obj:IsMainRole() or obj:IsGather() or (obj.IsCityOwnerStatue and obj:IsCityOwnerStatue()) then
		shield = false
	end

	return shield
end

function NpcDialog:SetBtnTextTimer(total_time)
	self.btn_time = total_time + 1
	if self.btn_text_countdown then
		GlobalTimerQuest:CancelQuest(self.btn_text_countdown)
		self.btn_text_countdown = nil
	end
	local remain_time = total_time
	self:UpdateBtnText()
	self.btn_text_countdown = GlobalTimerQuest:AddTimesTimer(BindTool.Bind(self.UpdateBtnText,self), 1, remain_time)
end

function NpcDialog:UpdateBtnText()
	local scene_logic = Scene.Instance:GetSceneLogic()
	local npc_id = self.npc_id
	local fuben_task_tab = scene_logic:GetNpcTaskNeedAloneLogic(npc_id)
	self.btn_time = self.btn_time - 1
	if fuben_task_tab and fuben_task_tab.btn_text_format then
		self:UpdteBtnWord(string.format(fuben_task_tab.btn_text_format, self.btn_time))
	end
	if self.btn_time <= 1 then
		if fuben_task_tab and fuben_task_tab.npc_id == npc_id and fuben_task_tab.callback then
			GlobalTimerQuest:AddDelayTimer(function()
				self:Close()
				fuben_task_tab.callback()
			end, 1)
		end
	end
end

--  转职副本逻辑处理
function NpcDialog:FlushTransfer(param_t)
	local data = param_t["all"]
	if not data or not data.transfer_task_cfg then
		self.node_list.layout_transfer:SetActive(false)
		self.is_transfer = false
		return
	end

	self.node_list.layout_transfer:SetActive(true)
	self.is_transfer = true
	self.transfer_task_cfg = data.transfer_task_cfg
	self:PlayTalk(self.transfer_task_cfg.fb_tip_desc)

	if self.transfer_delay_event then
		GlobalTimerQuest:CancelQuest(self.transfer_delay_event)
		self.transfer_delay_event = nil
	end

	self:TransferUpdate(10)

end

function NpcDialog:TransferUpdate(time)
	self.node_list.btn_goto_transfer_text.text.text = string.format(Language.TransFer.TiaoZhan,time)

	time = time - 1
	if time < 0 then
		if self.transfer_delay_event then
			GlobalTimerQuest:CancelQuest(self.transfer_delay_event)
			self.transfer_delay_event = nil
		end
		-- 执行点击事件
		self:GotoTransferFB()
	else
		self.transfer_delay_event = GlobalTimerQuest:AddDelayTimer(function ()
			self:TransferUpdate(time)
		end,1)
	end
end

-- 点击前往转职副本
function NpcDialog:GotoTransferFB()
	if not self.transfer_task_cfg then return end
	self:Close()
    FuBenWGCtrl.Instance:SendEnterFB(self.transfer_task_cfg.c_param1, self.transfer_task_cfg.c_param3)
end

-- 点击取消挑战
function NpcDialog:CancleTransferUpdate()
	if self.transfer_delay_event then
		GlobalTimerQuest:CancelQuest(self.transfer_delay_event)
		self.transfer_delay_event = nil
	end
	self:Close()
end

--仙盟建设特殊处理
function NpcDialog:SendGuildBuildReq(task_cfg)
	if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_6 then
		local task_status = GuildWGData.Instance:GetGuildBuildTaskStates(self.task_id) or 0
		return true, task_status
	end

	return false, 0
end

function NpcDialog:FlushSubmission(data)
	if data == nil then
		self:ClearSubmissionTimer()
		self.node_list.layout_submission:SetActive(false)
		return
	end

	local color = GameEnum.EQUIP_COLOR_ORANGE
	self.node_list.submission_bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_wpk_" .. color))

	local eff_bundle, eff_asset = ResPath.GetWuPinKuangEffectUi(BaseCell_Ui_Effect[color])
	self.node_list.submission_bg:ChangeAsset(eff_bundle, eff_asset)

	self.node_list.layout_submission:SetActive(true)
	self.node_list.str_submission_name.text.text = data.name or ""
	self.node_list.str_submission_num.text.text = data.num or 0

	local asset, bundle = ResPath.GetOperationTaskChainF2(data.icon)
	self.node_list.img_submission_icon.image:LoadSprite(asset, bundle)
	self.node_list.img_submission_icon.image:SetNativeSize()
end

NpcDialogItem = NpcDialogItem or BaseClass(BaseRender)

function NpcDialogItem:__init()
end

function NpcDialogItem:__delete()
	if self.buy_cell then
		self.buy_cell:DeleteMe()
		self.buy_cell = nil
	end
end

function NpcDialogItem:LoadCallBack()
	self.buy_cell = ItemCell.New(self.node_list.buy_item_bg)
	XUI.AddClickEventListener(self.node_list.buy_btn,BindTool.Bind(self.OnClickBuy, self))
end

function NpcDialogItem:OnFlush()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward.item_id)
	if item_cfg then
		self.buy_cell:SetData(self.data.reward)
		self.node_list.buy_item_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	end

	if self.data.money_type and self.data.param == 1 then
		local bundle, asset = ResPath.GetF2CommonIcon(Shop_Money[self.data.money_type].url)
		self.node_list.buy_btn_icon.image:LoadSprite(bundle, asset)
		self.node_list.buy_btn_txt.text.text = self.data.money or 0
	else
		local icon_res = self.data.param == 1 and "icon_coin" or "icon_get"
		self.node_list.buy_btn_icon.image:LoadSprite(ResPath.GetNpcDialog(icon_res))
		self.node_list.buy_btn_txt.text.text = self.data.param == 1 and Language.Task.Buy or Language.Task.Commit
	end

end

function NpcDialogItem:OnClickBuy()
	if self.callback then
		self.callback(true)
	end
end

function NpcDialogItem:SetClickCallBack(callback)
	self.callback = callback
end

---[[ 需求:活动开启的时候改变NPC的对话，有按钮可以点击回调进入副本
function NpcDialog:IsTianShenRoad(npc_id)
	local is_open,takl_str,call_back = TianshenRoadWGCtrl.Instance:CheckTianShenJianLinNpc(npc_id)
	if not is_open then
		is_open,takl_str,call_back = WorldTreasureWGCtrl.Instance:CheckShiLianNpc(npc_id)
	end
	if not is_open then
		is_open,takl_str,call_back = QuanMinBeiZhanWGCtrl.Instance:CheckXingTianLaiXiNpc(npc_id)
	end
	if not is_open then
		is_open,takl_str,call_back = ActXianQiJieFengWGCtrl.Instance:CheckXingTianLaiXiNpc(npc_id)
	end
	if not is_open then
		is_open,takl_str,call_back = MoWuJiangLinWGCtrl.Instance:CheckMoWuJianLinNpc(npc_id)
	end
	if is_open then
		self:ClearSubmissionTimer()
		self.node_list["ani_parent"]:SetActive(false)
		self:FlushModel(Talk_Type.Npc_Talk)
		self:SetName(Talk_Type.Npc_Talk)
		self:PlayTalk(takl_str)
		self.tianshenroad_btn_call_back = call_back
		self.node_list["tianshenroad_btn"]:SetActive(call_back ~= nil)
		self:AutoEnterFb()
		return true
	else
		self.node_list["tianshenroad_btn"]:SetActive(false)
	end
end

function NpcDialog:AutoEnterFb()
	CountDownManager.Instance:RemoveCountDown("auto_enter_tianshenroad_fb")
	self.node_list.auto_enter_time.text.text = string.format(Language.TianShenRoad.AutoEnterTip, 3)
		CountDownManager.Instance:AddCountDown(
			"auto_enter_tianshenroad_fb",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.OnClickTianShenRoadBtn, self),
			nil,
			3,
			1
		)
end

function NpcDialog:UpdateCountDown(elapse_time, total_time)
	self.node_list.auto_enter_time.text.text = string.format(Language.TianShenRoad.AutoEnterTip, total_time - elapse_time + 1)
end

function NpcDialog:OnClickTianShenRoadBtn()
	if self.tianshenroad_btn_call_back then
		self.tianshenroad_btn_call_back()
	end
	self:Close()
end
--]]

-- 仙盟战npc
function NpcDialog:FlushXMZ()
	self.node_list["layout_xmz_dialog"]:SetActive(false)
	if not self:GetNpcId() or self:GetNpcId() == 0 then
		return
	end
	local scene_type = Scene.Instance:GetSceneType()
	-- self.node_list["ani_parent"]:SetActive(true)
    if scene_type == SceneType.XianMengzhan then
        if GuildBattleRankedWGData.Instance:GetGuildNpcId(0) == self:GetNpcId() or GuildBattleRankedWGData.Instance:GetGuildNpcId(1) == self:GetNpcId() then
			self.node_list["layout_xmz_dialog"]:SetActive(true)
			local is_xmz_car = Scene.Instance:GetMainRole():IsXMZCar()
			self.node_list["xmz_dialog_btn"]:SetActive(not is_xmz_car)
			self.node_list["xmz_give_stone"]:SetActive(false)
			local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
			local side = role_info_list.side
			local npc_id = GuildBattleRankedWGData.Instance:GetGuildNpcId(side)
			local guild_npc_obj = Scene.Instance:GetNpcByNpcId(npc_id) 
			if guild_npc_obj and guild_npc_obj.SetXMZNpcTaskIcon then
				guild_npc_obj:ClearTaskIcon()
			end

			local gather_id = role_info_list.gather_id
			if not is_xmz_car or (gather_id and gather_id > 0) then
				self:ClearTimer()
			end 
			if is_xmz_car and gather_id and gather_id > 0 then
				self.node_list["xmz_give_stone"]:SetActive(true)
				self:SetGiveXMZStoneTimer()
				local gather_cfg = GuildBattleRankedWGData.Instance:GetLingShiCfgById(gather_id)
				if not IsEmptyTable(gather_cfg) then
					local color = gather_cfg.color
					self.node_list.xmz_stone_bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_wpk_" .. color))
					self.node_list.xmz_stone_name.text.text = gather_cfg.name or ""
					self.node_list.xmz_stone_num.text.text = gather_cfg.commit_reward_value
					local asset, bundle = ResPath.GetXMZNewImg(gather_cfg.icon)
					self.node_list.xmz_stone_icon.image:LoadSprite(asset, bundle)
					self.node_list.xmz_stone_icon.image:SetNativeSize()
					self.node_list["ani_parent"]:SetActive(false)
				end
			end
        end
    end
end

function NpcDialog:ClearGiveXMZStoneTimer()
	if self.give_xmz_stone_countdown then
		GlobalTimerQuest:CancelQuest(self.give_xmz_stone_countdown)
		self.give_xmz_stone_countdown = nil
	end
end

function NpcDialog:SetGiveXMZStoneTimer()
	self.give_xmz_stone_time = cd_time

	if self.give_xmz_stone_countdown then
		GlobalTimerQuest:CancelQuest(self.give_xmz_stone_countdown)
		self.give_xmz_stone_countdown = nil
	end

	if self.node_list.btn_xmz_give_txt then
		self.node_list.btn_xmz_give_txt.text.text = string.format(Language.GuildBattleRanked.StoneBtnGive, self.give_xmz_stone_time)
	end

	local remain_time = cd_time
	self.give_xmz_stone_countdown = GlobalTimerQuest:AddTimesTimer(BindTool.Bind(self.UpdateGiveXMZStoneTime, self), 1, remain_time)
end

function NpcDialog:UpdateGiveXMZStoneTime()
	self.give_xmz_stone_time = self.give_xmz_stone_time - 1
	if self.give_xmz_stone_time > 0 and nil ~= self.node_list.btn_xmz_give_txt then
		self.node_list.btn_xmz_give_txt.text.text = string.format(Language.GuildBattleRanked.StoneBtnGive, math.ceil(self.give_xmz_stone_time))
	else
		self.give_xmz_stone_time = cd_time
		self:OnClickXMZGive()
	end
end

--仙盟战 变身按钮 
function NpcDialog:OnClickXMZBS()
	if not GuildBattleRankedWGData.Instance:GetGuildNpcIsCanChanegBody(self.npc_id) then
	    SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.NpcHint)
	    self:Close()
	    return
	end
	local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local bianshen_count = other_cfg.bianshen_count or 2
	local has_num = role_info_list and role_info_list.machine_count or 0
	local can_num = bianshen_count - has_num
	if can_num > 0 then
		local main_role = Scene.Instance:GetMainRole()
		if main_role ~= nil and main_role.vo ~= nil then
			-- 攻城车
			if main_role.vo.special_appearance == SPECIAL_APPEARANCE_TYPE.XMZ then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBattleRanked.CurIsBSTips)
				self:Close()
				return
			end
		end
		GuildBattleRankedWGCtrl.Instance:SendCSGuildBattleOPReq(GUILD_BATTLE_OP_REQ_TYPE.GUILD_BATTLE_REWARD_REQ_TYPE_COMMIT_BIANSHEN)
		TryDelayCall(self, function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end,1,"delay_xmz_bs_guaji")

	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBattleRanked.NotBSNumTips)
	end

	self:Close()
end

--仙盟战 上交灵石 
function NpcDialog:OnClickXMZGive()
	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	if not IsEmptyTable(role_info_list) then
		local gather_id = role_info_list.gather_id
		if gather_id and gather_id > 0 then
			local npc_cfg = GuildBattleRankedWGData.Instance:GetNpcCfgByNpcId(self.npc_id)
			local param1 = npc_cfg.side
			GuildBattleRankedWGCtrl.Instance:SendCSGuildBattleOPReq(GUILD_BATTLE_OP_REQ_TYPE.GUILD_BATTLE_REWARD_REQ_TYPE_COMMIT_LINGSHI,param1)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBattleRanked.CurHasNotStone)
		end
	end
	if self.give_xmz_stone_countdown then
		GlobalTimerQuest:CancelQuest(self.give_xmz_stone_countdown)
		self.give_xmz_stone_countdown = nil
	end
	TryDelayCall(self, function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end,0.5,"delay_xmz_give_guaji")
	self:Close()
end

function NpcDialog:ResetGather()
end

----[[ 跨服龙脉NPC
function NpcDialog:FlushCrossLongMai()
	local cur_npc_id = self:GetNpcId()
	local other_cfg = CrossLongMaiWGData.Instance:GetOtherCfg()
	local longmai_npc_id = other_cfg.npcid
	local is_longmai_npc = cur_npc_id == longmai_npc_id
	if not is_longmai_npc then
		self.node_list["cross_longmai_part"]:SetActive(false)
		return
	end

	local fun_is_open = FunOpen.Instance:GetFunIsOpened(FunName.CrossLongMaiView)
    if not fun_is_open then
		self.node_list["cross_longmai_part"]:SetActive(false)
		return
    end

	self:ClearTimer()
	self.node_list["cross_longmai_part"]:SetActive(true)
	local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_LONGMAI)
	if act_is_open then
		self:PlayTalk(other_cfg.npc_dialog_open)
	else
		self:PlayTalk(other_cfg.npc_dialog_close)
	end

	self.node_list["btn_longmai_enter"]:SetActive(act_is_open)
	self.node_list["btn_longmai_shop"]:SetActive(not act_is_open)
	self.node_list["ani_parent"]:SetActive(false)
end

function NpcDialog:OnClickEnterLongMai()
	CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_LONGMAI)
end

function NpcDialog:OnClickLongMaiShop()
	self:Close()
	ViewManager.Instance:Open(GuideModuleName.LongMaiShop)
end
--]]

----[[ 捉鬼NPC
function NpcDialog:FlushZhuoGui()
	local other_cfg = FuBenPanelWGData.Instance:GetZhuoGuiOtherCfg()
	local cur_npc_id = self:GetNpcId()
	if cur_npc_id ~= other_cfg.npcid or Scene.Instance:GetSceneType() ~= SceneType.GHOST_FB_GLOBAL then
		self.node_list["zhuogui_part"]:SetActive(false)
		return
	end

    local state = FuBenPanelWGData.Instance:GetZhuoGuiEventSatus()
	local show_btn = false
    if state == GHOST_FB_EVENT_STATUS.FINISH then
        self:PlayTalk(other_cfg.talk_text_3)
	elseif state == GHOST_FB_EVENT_STATUS.ACCEPT then
		self:PlayTalk(other_cfg.talk_text_2)
	else
		self:ClearTimer()
		self.node_list["ani_parent"]:SetActive(false)
		self:PlayTalk(other_cfg.talk_text_1)
		show_btn = true
    end

	self.node_list["zhuogui_part"]:SetActive(true)
	self.node_list["btn_accept_zhuogui_event"]:SetActive(show_btn)
end

function NpcDialog:OnClickAcceptZhuoGuiEvent()
	FuBenPanelWGCtrl.Instance:SendZhuoGuiOperate(GHOST_FB_OPERATE_TYPE.REFRESH_EVENT)
	self:Close()
end
--]]

-- 用于调模型展示
function NpcDialog:OnModelCameraSet(param)
	if nil == self.npc_display then
		self.npc_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			offset_type = MODEL_OFFSET_TYPE.NPC,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.npc_display:SetRenderTexUI3DModel(display_data)
		-- self.npc_display:SetUI3DModel(self.node_list["ph_display"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NPC)
		self:AddUiRoleModel(self.npc_display)
	end

	self:DoOpenAni()
	self.npc_display:SetMainAsset(param.bundle, param.asset)
end