
require("game/fight/fight_def")
require("game/fight/fight_wg_data")
require("game/fight/fight_text")
require("game/fight/fight_top_text")
require("game/fight/fight_revenge_wg_ctrl")
-- 战斗
FightWGCtrl = FightWGCtrl or BaseClass(BaseWGCtrl)

function FightWGCtrl:__init()
	if FightWGCtrl.Instance ~= nil then
		print_error("[FightWGCtrl] attempt to create singleton twice!")
		return
	end
	FightWGCtrl.Instance = self

	self.data = FightWGData.New()
	FightText.New()
	self.fight_top_text = FightTopText.New()
	self.fight_top_text:Open()

	self.last_atk_time = 0
	self.last_pet_atk_time = 0
	self.shandianlian_obj_list = {}
	self.huixue_list = {}
	self:RegisterAllProtocols()

	Runner.Instance:AddRunObj(self, 5)
	self.monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list

	self.flush_talent_info = GlobalEventSystem:Bind(SkillEventType.FLUSH_TALENT_INFO,BindTool.Bind1(self.OnTalentEffectInfo, self))
	self.team_info_change_event = GlobalEventSystem:Bind(OtherEventType.TEAM_INFO_CHANGE, BindTool.Bind1(self.OnTeamChange, self))         -- 队伍
	self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainUIInitCallBack, self))
	self:BindGlobalEvent(ObjectEventType.EXIT_FIGHT, BindTool.Bind(self.PlayerExitFight, self))
    self:BindGlobalEvent(ObjectEventType.ENTER_FIGHT, BindTool.Bind(self.PlayerEnterFight, self))

	self.is_debug_fight_log = false
	self.debug_fight_time = 0
	self.debug_fight_timer = nil
	self.dir_skill = nil
end

function FightWGCtrl:__delete()
	self.select_rect_obj_parent_rect = nil
	self.select_rect_obj_statu = nil

	if self.select_rect_obj_loader then
		self.select_rect_obj_loader:DeleteMe()
		self.select_rect_obj_loader = nil
	end

	self.data:DeleteMe()

	self.fight_top_text:DeleteMe()
	self.fight_top_text = nil

	Runner.Instance:RemoveRunObj(self)
	self.monster_cfg = nil

	if self.flush_talent_info then
		GlobalEventSystem:UnBind(self.flush_talent_info)
		self.flush_talent_info = nil
	end

	if self.team_info_change_event then
		GlobalEventSystem:UnBind(self.team_info_change_event)
		self.team_info_change_event = nil
	end

	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end

	self.is_debug_fight_log = false
	self.debug_fight_time = 0
	if self.debug_fight_timer then
		GlobalTimerQuest:CancelQuest(self.debug_fight_timer)
		self.debug_fight_timer = nil
	end

	GlobalTimerQuest:CancelQuest(self.mainrole_bianshen_timer)
	self:RemoveShoutMoveDelayTimer()
	self.is_shout_move = nil
	FightText.Instance:DeleteMe()
	FightWGCtrl.Instance = nil
end

function FightWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCObjChangeBlood, "OnObjChangeBlood")
	self:RegisterProtocol(SCPerformSkill, "OnPerformSkill")
	self:RegisterProtocol(SCPerformAOESkill, "OnPerformAOESkill")

	self:RegisterProtocol(SCRoleReAlive, "OnRoleReAlive")
	self:RegisterProtocol(SCFixPos, "OnFixPos")
	self:RegisterProtocol(SCSkillTargetPos, "OnSkillTargetPos")
	self:RegisterProtocol(SCBuffMark, "OnBuffMark")
	self:RegisterProtocol(SCBuffAdd, "OnBuffAdd")
	self:RegisterProtocol(SCBuffRemove, "OnBuffRemove")
	-- unity3d项目暂时屏蔽
	self:RegisterProtocol(SCEffectList, "OnEffectList")
	self:RegisterProtocol(SCEffectInfo, "OnEffectInfo")
	self:RegisterProtocol(SCEffectRemove, "OnEffectRemove")
	self:RegisterProtocol(SCFightSpecialFloat, "OnFightSpecialFloat")
	self:RegisterProtocol(SCSkillPhase, "OnSkillPhase")
	self:RegisterProtocol(SCHpStoreChangeBlood, "OnSCHpStoreChangeBlood")
	-- unity3d项目暂时屏蔽
	-- self:RegisterProtocol(SCSpecialShieldChangeBlood, "OnSpecialShieldChangeBlood")
	-- self:RegisterProtocol(SCZhiBaoAttack, "OnZhiBaoAttack")
	-- self:RegisterProtocol(SCBianShenView, "BianShenView")

	self:RegisterProtocol(SCExpBuffInfo,"OnSCExpBuffInfo")
	self:RegisterProtocol(SCTSYoujunSkillTargetList,"OnSCTSYoujunSkillTargetList")
	self:RegisterProtocol(SCTSYoujunActionStatusChange,"OnTSYoujunActionStatusChange")
	self:RegisterProtocol(SCUpgradeimageSkill21,"OnSCUpgradeimageSkill21")
	self:RegisterProtocol(SCLoverFbBossConnectionInfo, "SCLoverFbBossConnectionInfo")

	self:RegisterProtocol(SCSkillSpecialAppe,"OnSCSkillSpecialAppe")
	self:RegisterProtocol(SCExplode,"OnSCExplode")
	self:RegisterProtocol(CSPerformChargeSkill)

	self:RegisterProtocol(SCEffectWindMillFinish, "OnSCEffectWindMillFinish")
	self:RegisterProtocol(CSSetAttentionObj)
	self:RegisterProtocol(SCMonsterShieldChange, "OnSCMonsterShieldChange")
end

function FightWGCtrl:Update(now_time, elapse_time)
	self.data:Update(now_time, elapse_time)
	-- FightWGCtrl.TryUseCachaPetSkill()
	self:RemoveSpecialEffectInfo2(now_time, elapse_time)
	self:UpdateShanDianLian(now_time, elapse_time)
end

-- 等级提升特效（人物升级特效）播放
function FightWGCtrl:DoUplevelText(level)
	self.fight_top_text:DoUplevelText(level)
end

function FightWGCtrl:ShowAttrText(attr, num)
	-- 2021.07.31 临时屏蔽属性飘字
	-- self.fight_top_text:ShowAttrText(attr, num)
end

function FightWGCtrl:DoMoneyEffect(money_type, show_transform)
	self.fight_top_text:DoMoneyEffect(money_type, show_transform)
end

-- 闪电链--
function FightWGCtrl:OnSCUpgradeimageSkill21(protocol)
	if self.shandianlian_obj_list[protocol.deliver_obj_id] then return end
	local list = {}
	local tab = nil
	local obj = nil
	local target_obj_id_list = protocol.target_obj_id_list
	for i=1,#target_obj_id_list do
		obj = Scene.Instance:GetObj(target_obj_id_list[i])
		if obj and obj:GetVo() and not obj:IsRealDead() then
			tab = {}
			tab.obj_id = obj:GetObjId()
			tab.main_deliverer_obj_id = protocol.deliver_obj_id
			if IsEmptyTable(list) then
				tab.summoner_obj_id = protocol.deliver_obj_id
			else
				tab.summoner_obj_id = list[#list].obj_id
			end

			tab.pos_x, tab.pos_y = obj:GetLogicPos()
			if tab.summoner_obj_id then
				table.insert(list, tab)
			end
		end
	end

	if 0 == #list then return end
	local fenduan_skill_cfg = SkillWGData.Instance:GetFenDuanSkill(SkillWGData.Skill_Id_11303)
	local total_time = fenduan_skill_cfg and fenduan_skill_cfg.total_time/1000 or 2
	total_time = total_time + Status.NowTime
	self.shandianlian_obj_list[protocol.deliver_obj_id] = {curr_time = 0, total_time = total_time, list = list}
end

function FightWGCtrl:UpdateShanDianLian(now_time, elapse_time)
	-- body
	if IsEmptyTable(self.shandianlian_obj_list) then return end
	local curr_time = nil
	local list = nil
	local tab = nil
	local obj, bundle_name, asset_name, buff_tf = nil, nil, nil, nil
	for k,v in pairs(self.shandianlian_obj_list) do
		curr_time = v.curr_time
		list = v.list
		if not IsEmptyTable(list) and (0 == curr_time or curr_time <= now_time) then
			tab = table.remove(list, 1)
			Scene.Instance:CreateSDX(tab, "Effect_shenbing_lszn_01")
			obj = Scene.Instance:GetObj(tab.obj_id)
			if obj then
				buff_tf = obj.draw_obj:GetAttachPoint(AttachPoint.BuffMiddle)
				buff_tf = buff_tf or obj:GetRoot()
				bundle_name, asset_name = ResPath.GetEffect("Effect_shenbing_lszn_hit")
        		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, buff_tf.transform, 3)
			end

			self.shandianlian_obj_list[k].list = list
			self.shandianlian_obj_list[k].curr_time = now_time + 0.1
		end

		if v.total_time <= now_time then
			self:RemoveShanDianLian(k)
			self.shandianlian_obj_list[k] = nil
		end
	end
end

function FightWGCtrl:RemoveShanDianLian(main_deliverer_obj_id)
	-- body
	local list = Scene.Instance:GetObjListByType(SceneObjType.EffectObj)
	for k,v in pairs(list) do
		if v:GetVo() and v:GetVo().main_deliverer_obj_id == main_deliverer_obj_id then
			Scene.Instance:DeleteObj(v:GetObjId())
		end
	end
end

-- 定罪效果
function FightWGCtrl:DingZuiEffect(protocol)
	local target = Scene.Instance:GetObj(protocol.obj_id)
	local deliverer = Scene.Instance:GetObj(protocol.deliverer)
	if not target or not target:IsCharacter() or not target:GetVo()
		or not deliverer or not deliverer:GetVo() or not deliverer:IsCharacter() then
		return
	end

	local target_root = target:GetRoot()
	local deliverer_root = deliverer:GetRoot()
	local d_pos = deliverer_root.transform.position
	local direction = nil
	if target_root and not IsNil(target_root.gameObject) and target_root.transform.position ~= deliverer_root.transform.position then
		direction = target_root.transform.position - deliverer_root.transform.position
	else
		direction = (d_pos + deliverer_root.transform.forward * 5) - d_pos
	end
	direction.y = 0
	if direction.x == 0 and direction.z == 0 then
		direction.x = 0.1
	end
	local rotation = Quaternion.LookRotation(direction)

	local percent = deliverer.vo.hp / deliverer.vo.max_hp
	local url = percent > 0.3 and "Effect_shenbing_dz_low" or "Effect_shenbing_dz_high"
	local bundle_name, asset_name = ResPath.GetMiscEffect(url)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, deliverer_root.transform, 3, nil, rotation)
end

function FightWGCtrl:OnObjChangeBlood(protocol, is_robert)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or not obj:IsCharacter() then
		return
	end

	local deliverer = Scene.Instance:GetObj(protocol.deliverer)
	--[[if deliverer then
		print_error("接收方:", obj.vo.name, "max_hp:", obj.vo.max_hp, "hp:", obj.vo.hp)
		print_error("发出方:", deliverer.vo.name, "max_hp:", deliverer.vo.max_hp, "hp:", deliverer.vo.hp)
		print_error("OnObjChangeBlood:", "now_blood:", protocol.now_blood, "real_blood:", protocol.real_blood, "fighttype:", protocol.fighttype, protocol)
	end]]

	if protocol.fighttype == FIGHT_TYPE.FIGHT_TYPE_XIXUE and protocol.real_blood > 0 then -- 吸血不在这飘字
		return
	end

	-- 仙镯伤害 只在发出方为主角 飘
	if obj:IsMainRole() and nil ~= deliverer and protocol.fighttype == FIGHT_TYPE.EQUIP_XIANZHOU_SKILL then
		return
	end

	-- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
	local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(protocol.skill)

	--主界面破盾伤害面板
	MainuiWGCtrl.Instance:SetTianShenBreakShieldHurt(protocol)
	obj:SetAttr("hp", protocol.now_blood)
	if protocol.real_blood ~= 0 then
		obj:Deliverer(deliverer)
		obj:SetHitSkillId(protocol.skill)
	end

	----[[ 自己的宠物打的伤害飘字 deliverer发的是人物id所以直接return
	if protocol.fighttype == FIGHT_TYPE.PET then
		if deliverer and deliverer:IsMainRole() then
			obj:DoBeHit(deliverer, 0, 0, protocol.real_blood, protocol.fighttype,nil,protocol.deliverer_wuxing_type)
		end
		return
	end
	--]]

	FightRevengeWGCtrl.Instance:OnObjChangeBlood(protocol)
	if deliverer ~= nil and deliverer:IsRole() then
		GuajiWGCtrl.Instance:TrySaveRecoveryFollowState(deliverer)
	end

	-- 攻击目标造成我方被目标反伤
	--仙器护甲 不需要被强行修改为 FANGSHANG
	if protocol.product_method == PRODUCT_METHOD.REBOUNDHURT and protocol.fighttype ~= FIGHT_TYPE.FIGHT_TYPE_ANQI_ARMOR then
		protocol.fighttype = FIGHT_TYPE.FANGSHANG
	end

	if protocol.real_blood > 0 and protocol.fighttype == FIGHT_TYPE.NORMAL then
		return
	end

	-- 对小怪造成伤害，小怪显示血量条
	if deliverer ~= nil and deliverer:IsMainRole() and obj:IsMonster() then
		obj:SetFollowUIVisible(true)
	end

	-- 单体攻击使用这条协议来播动作
	local target_x, target_y = obj:GetLogicPos()
	if 0 ~= protocol.skill and nil ~= deliverer and not deliverer:IsRole() and not SkillWGData.IsAoeSkill(normal_skill or protocol.skill) then
		-- print_error("single skill attack 单体攻击使用这条协议来播动作  ",protocol.skill)
		if deliverer:IsCharacter() then
			deliverer:DoAttack(protocol.skill, target_x, target_y, protocol.obj_id)
		end
	end

	-- self:OnSpecialBuffAdd(protocol)
	self:OnSpecialEffectInfo(protocol)
	self:OnSpecialEffectInfo2(protocol)
	if FIGHT_TYPE.FUWEN == protocol.fighttype then
		obj:DoBeHitFloatingText(deliverer, protocol.real_blood, protocol.blood, protocol.fighttype)
	end

	local soulBoy_obj = nil
	local xiaotianquan = nil
	-- 没有攻击者或者技能id为0直接处理受击效果
	if nil == deliverer or 0 == protocol.skill then
		if FIGHT_TYPE.XiaoTianQuan == protocol.fighttype and deliverer ~= nil then-- 哮天犬攻击
			xiaotianquan = deliverer:GetXiaoTianQuan()
			if xiaotianquan and not xiaotianquan:IsMove() and not xiaotianquan:IsAtkPlaying() then
				xiaotianquan:DoAttack(protocol.skill, target_x, target_y, protocol.obj_id)
				obj:DoBeHit(deliverer, 0, 0, protocol.real_blood, protocol.fighttype,nil,protocol.deliverer_wuxing_type)

			end
		else
			obj:DoBeHit(deliverer, protocol.skill, protocol.real_blood, protocol.blood, protocol.fighttype,nil,protocol.deliverer_wuxing_type)
		end
	else
		if FIGHT_TYPE.LINGTONG == protocol.fighttype then-- 灵童攻击
			soulBoy_obj = deliverer:GetSoulBoyObj()
			if soulBoy_obj and not soulBoy_obj:IsMove() and not soulBoy_obj:IsAtkPlaying() then
				soulBoy_obj:DoAttack(protocol.skill, target_x, target_y, protocol.obj_id)
			end
		elseif FIGHT_TYPE.XiaoTianQuan == protocol.fighttype then-- 哮天犬攻击
			xiaotianquan = deliverer:GetXiaoTianQuan()
			if xiaotianquan and not xiaotianquan:IsMove() and not xiaotianquan:IsAtkPlaying() then
				xiaotianquan:DoAttack(protocol.skill, target_x, target_y, protocol.obj_id)
			end
		end

		-- 主角攻击特殊处理
		local role_hurt = protocol.blood
		local is_trigger = false

		if deliverer:IsMainRole() then
			-- 不是本次攻击的，或者已经击中直接表现
			if 0 == protocol.real_blood and obj:IsHudun() then -- 护盾
				obj:OnFightSpecialFloat(protocol.real_blood)
			elseif FIGHT_TYPE.FABAO == protocol.fighttype or FIGHT_TYPE.FIGHT_TYPE_LEIJI == protocol.fighttype then-- 法宝
				is_trigger = true
				obj:DoBeHit(deliverer, protocol.skill, protocol.real_blood, role_hurt, protocol.fighttype,FIGHT_TEXT_TYPE.BAOJU,protocol.deliverer_wuxing_type)
				deliverer:OnBaoJuFight()
			elseif FIGHT_TYPE.LINGTONG == protocol.fighttype then-- 灵童
				is_trigger = true
				if soulBoy_obj and not soulBoy_obj:IsMove() then
					-- soulBoy_obj:DoAttack(protocol.skill, target_x, target_y, protocol.obj_id)
					obj:DoBeHit(soulBoy_obj, 0, 0, protocol.real_blood, protocol.fighttype, FIGHT_TEXT_TYPE.GREATE_SOLDIER,protocol.deliverer_wuxing_type)
				end
			elseif (protocol.skill ~= deliverer:GetLastSkillId() or deliverer:AtkIsHit(protocol.skill) 
				or protocol.product_method == PRODUCT_METHOD.PRODUCT_METHOD_STORM
				or protocol.product_method == PRODUCT_METHOD.PRODUCT_METHOD_SIXIANG_1
				or protocol.product_method == PRODUCT_METHOD.PRODUCT_METHOD_SIXIANG_2
				or protocol.product_method == PRODUCT_METHOD.PRODUCT_METHOD_SIXIANG_3
				or protocol.product_method == PRODUCT_METHOD.PRODUCT_METHOD_SIXIANG_4
				or protocol.product_method == PRODUCT_METHOD.PRODUCT_METHOD_MULTI_MU_ARROW
				or protocol.product_method == PRODUCT_METHOD.PRODUCT_METHOD_MULTI_JIN_1) then
				is_trigger = true
				obj:DoBeHit(deliverer, protocol.skill, protocol.real_blood, role_hurt, protocol.fighttype,nil,protocol.deliverer_wuxing_type)
			elseif FIGHT_TYPE.XiaoTianQuan == protocol.fighttype then-- 哮天犬
				is_trigger = true
				if xiaotianquan and not xiaotianquan:IsMove() then
					obj:DoBeHit(deliverer, 0, 0, protocol.real_blood, protocol.fighttype,nil,protocol.deliverer_wuxing_type)
				end
			end

			GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_DO_HIT, protocol)
		end

		--测试战斗用
		if self.is_debug_fight_log then
			self:MyAttackLog(protocol)
		end

		if not is_trigger then
			-- 尚未击中先缓存
			self.data:SaveBeHitInfo(protocol.obj_id, protocol.deliverer, protocol.skill,
				protocol.real_blood, role_hurt, protocol.fighttype,nil,nil,protocol.deliverer_wuxing_type)
		end
	end

	if obj:IsMainRole() and nil ~= deliverer then
		if obj:IsRidingNoFightMount() then
			MountWGCtrl.Instance:SendMountGoonReq(0)
		end

		if Scene.Instance:GetSceneType() == SceneType.COPPER_FB then return end
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_BE_HIT, deliverer)

		if 0 == protocol.real_blood and obj:IsHudun() then -- 护盾
				obj:OnFightSpecialFloat(protocol.real_blood)
		elseif 0 == protocol.real_blood and FIGHT_TYPE.HPSTORE then -- 护盾
				obj:OnFightSpecialFloat(protocol.real_blood)
		elseif FIGHT_TYPE.GEDANG == protocol.fighttype and protocol.real_blood == 0 then
			obj:DoBeHitFloatingText(deliverer, protocol.now_blood, protocol.blood, protocol.fighttype, FIGHT_TYPE.NORMAL, protocol.deliverer_wuxing_type)
		elseif not deliverer:IsMainRole() then
			if deliverer:GetType() == SceneObjType.Trigger then
				-- 3d需要添加的
				-- ReviveWGData.Instance:SetKillerName(deliverer.vo.trigger_name or "")
				obj:DoBeHit(deliverer, protocol.skill, protocol.real_blood, protocol.blood, protocol.fighttype,nil,protocol.deliverer_wuxing_type)
			else
				ReviveWGData.Instance:SetBeHitData(deliverer)
				-- 3d需要添加的
				-- ReviveWGData.Instance:SetKillerName(deliverer: or "")
			end
		end
	end

	-- self:CheckTeamBoss(protocol.deliverer)
end

function FightWGCtrl:CheckTeamBoss(deliverer)
	if BossWGData.IsTeamBossScene() then
		local scene_logic = Scene.Instance:GetSceneLogic()

		if not scene_logic then
			return
		end

		-- local leader_id = scene_logic:GetTeamTargetFlag()

		-- if leader_id == deliverer then
		-- 	scene_logic:AtkTeamLeaderTarget()
		-- end
	end
end

function FightWGCtrl:LostMonster(obj_id, deliverer)
	self.huixue_list[obj_id .. "deliverer" .. deliverer] = nil
end

function FightWGCtrl:OnSCMonsterShieldChange(protocol)
	--print_error("OnSCMonsterShieldChange:::", protocol)
	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if nil == obj or not obj:IsCharacter() then
		return
	end

	if obj:GetType() == SceneObjType.Monster then
		--[[local monster_info = BossWGData.Instance:GetMonsterInfo(obj:GetVo().monster_id)
		local max_shield_value = monster_info and monster_info.shield_value and monster_info.shield_value or 0]]
		local info = {
			obj_id = protocol.obj_id,
			shield_value = protocol.shield_value,
			max_shield_value = protocol.max_shield_value,
			change_shield_value = protocol.change_shield_value,
			shield_recover_time = protocol.shield_recover_time,
		}

		GlobalEventSystem:Fire(ObjectEventType.SPECIAL_SHIELD_CHANGE, info)
	end 
end

function FightWGCtrl:OnSCHpStoreChangeBlood(protocol)
	-- print_error("OnSCHpStoreChangeBlood:::", protocol)
	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if nil == obj or not obj:IsCharacter() then
		return
	end
	GlobalEventSystem:Fire(ObjectEventType.HuDun_Change, protocol)
	-- obj:OnFightSpecialFloat(protocol.max_hp)
end

function FightWGCtrl:OnPerformSkill(protocol)
	local deliverer = Scene.Instance:GetObj(protocol.character)
	if not deliverer or not deliverer:IsCharacter() then
		return
	end

	local target_obj = Scene.Instance:GetObj(protocol.target)
	if not target_obj then
		return
	end

	if deliverer:IsMainRole() and not deliverer:IsRobot() then
		local skill_id = protocol.skill
		-- 如果是仙盟争霸特殊技能，则让它跟普攻表现一样即可
		if deliverer:IsRole() and deliverer:GetVo() and protocol.skill == SkillWGData.Guild_BianShen_Common_Skill_Id then
			local vo = deliverer:GetVo()
			skill_id = use_prof_normal_skill_list[vo.sex] and use_prof_normal_skill_list[vo.sex][vo.prof]
						and use_prof_normal_skill_list[vo.sex][vo.prof][1] or protocol.skill
		end
		TaskGuide.Instance:ClickSkillStopTask()
		SkillWGData.Instance:SetSkillCD(SkillWGData.Instance:GetRealSkillIndex(skill_id), 0)
		local is_robert_fight = RobertManager.Instance:IsPlaying()
		if is_robert_fight then
			self:FlushMainSkillCD(skill_id)
		end
	else
		local target_x, target_y = target_obj:GetLogicPos()
		deliverer.attack_index = protocol.skill_data
		deliverer:DoAttack(protocol.skill, target_x, target_y, protocol.target)
	end
end

function FightWGCtrl:FlushMainSkillCD(skill_id)
	SkillWGCtrl.Instance:OnNoTargetCD(skill_id)
	GlobalEventSystem:Fire(MainUIEventType.CLICK_SKILL_BUTTON,skill_id)
	GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_LIST)
end

function FightWGCtrl:OnPerformAOESkill(protocol)
	-- print_error("-----OnPerformAOESkill---", protocol.skill, protocol)
	local deliverer = Scene.Instance:GetObj(protocol.obj_id)
	if nil == deliverer or not deliverer:IsCharacter() then
		return
	end

	-- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
	local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(protocol.skill)
	-- 主角触发技能喊招
	if deliverer:IsMainRole() then	
		self:TriggerSkillShoutMove(protocol.skill)
	end

	-- 怪物在施法阵过程中，会一直收到AOE，法阵原因不处理
	if deliverer:IsMonster() and AOE_REASON.AOE_REASON_FAZHEN == protocol.aoe_reason then
		return
	end

	local normal_skill_data = SkillWGData.GetSkillinfoConfig(normal_skill or protocol.skill)
	local is_wuhun_skill = SkillWGData.Instance:GetIsWuHunSkill(protocol.skill)
	---增加放技能也触发武魂普攻，如果出现技能则覆盖普攻
	if (normal_skill_data ~= nil or is_wuhun_skill) and deliverer:IsRole() then
		local wu_hun_obj = deliverer:GetWuHun()
		if wu_hun_obj then
			wu_hun_obj:DoAttack(protocol.skill, protocol.pos_x, protocol.pos_y, protocol.target_id)
		end
	end

	-- 秘笈 技能
	if SkillWGData.Instance:GetIsEsotericaSkill(protocol.skill) then
		self:TryPlayEsotericaSkillEff(protocol.skill, deliverer, protocol.target_id, protocol.pos_x, protocol.pos_y)
		return
	end

	-- 活动技能
	if SkillWGData.Instance:GetIsResidentSkill(protocol.skill) then
		self:TryPlayResidentSkillEff(protocol.skill, deliverer, protocol.target_id, protocol.pos_x, protocol.pos_y)
		return
	end

	-- 驭兽 技能
	if SkillWGData.Instance:GetIsBeastsSkill(protocol.skill) then
		self:TryUseBeastSkill(protocol.skill, deliverer, protocol.target_id, protocol.pos_x, protocol.pos_y, protocol.param1)
		return
	end

	-- 时装技能
	if NewAppearanceWGData.Instance:GetFashionSkillEffectCfgBySkillId(protocol.skill) ~= nil then
		self:TryPlayFashionSkillEff(protocol.skill, deliverer, protocol.target_id, protocol.pos_x, protocol.pos_y)
		return
	end

	if not deliverer:IsMainRole() then
		--法阵AOE技能不执行攻击逻辑
		if AOE_REASON.AOE_REASON_FAZHEN ~= protocol.aoe_reason then
			if not is_wuhun_skill then
				deliverer.attack_index = protocol.skill_data
				deliverer:DoAttack(protocol.skill, protocol.pos_x, protocol.pos_y, protocol.target_id)
			end
		end
	else
		TaskGuide.Instance:ClickSkillStopTask()
		local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(normal_skill or protocol.skill)
		if skill_cfg and skill_cfg.is_biansheng == 1 then
			GlobalTimerQuest:CancelQuest(self.mainrole_bianshen_timer)
			--天神变身之后的出场动作，由这个AOE技能下发后触发
			--延迟0.05秒是为了等模型加载完毕后播放，避免天神跟武器动作播放不一致（部分天神的武器自带动作）
			self.mainrole_bianshen_timer = GlobalTimerQuest:AddDelayTimer(function ()
				deliverer.attack_index = protocol.skill_data
				deliverer:DoAttack(protocol.skill, protocol.pos_x, protocol.pos_y, protocol.target_id)
				GlobalTimerQuest:CancelQuest(self.mainrole_bianshen_timer)
			end, 0.05)
		end
	end

	if SkillWGData.Instance:IsSkillToSelf(normal_skill or protocol.skill) then
        deliverer:DoAttack(protocol.skill)
    end

	if deliverer ~= nil and deliverer:IsRole() then
		GuajiWGCtrl.Instance:TrySaveRecoveryFollowState(deliverer)
	end
end

function FightWGCtrl:OnTSYoujunActionStatusChange(protocol)
	local role_obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if nil == role_obj or (SceneObjType.MainRole ~= role_obj:GetType() and SceneObjType.Role ~= role_obj:GetType()) then
		return
	end

	local mingjiang = role_obj:GetFollowMingJiang()
	if mingjiang then
		mingjiang:ChangeStatus(protocol.status)
	end
end

function FightWGCtrl:OnSCTSYoujunSkillTargetList(protocol)
	if nil == Scene.Instance:GetMainRole() then
		return
	end
	local role_obj = Scene.Instance:GetObjectByObjId(protocol.deliver_obj_id)
	if nil == role_obj or (SceneObjType.MainRole ~= role_obj:GetType() and SceneObjType.Role ~= role_obj:GetType()) then
		return
	end

	local target_obj = Scene.Instance:GetObjectByObjId(protocol.target_objid_list[1])
	local deliverer_obj = role_obj:GetFollowMingJiang()
	if target_obj == nil or deliverer_obj == nil then
		return
	end

	local vo = {}
	vo.obj_id = target_obj:GetObjId()
	vo.fighttype = FIGHT_TYPE.TS_YOUJUN
	vo.product_method = 0
	vo.real_injure = 0
	vo.injure = protocol.injure
	vo.skill_id = protocol.skill_id


	local target_x, target_y = target_obj:GetLogicPos()
	deliverer_obj:SetDirectionByXY(target_x, target_y)
	deliverer_obj:DoAttack(vo.skill_id, target_x, target_y, vo.obj_id, target_obj:GetType())

	if nil == Scene.Instance:GetMainRole() then
		return
	end
	local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)
	if nil == role or not role:IsRole() then
		return
	end
end

function FightWGCtrl:OnRoleReAlive(protocol)
	if Scene.Instance:GetSceneType() == SceneType.Field1v1 then return end
	local target_obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == target_obj then
		return
	end
	target_obj:ResetSpecialState()
	target_obj:SetLogicPos(protocol.pos_x, protocol.pos_y)

	if target_obj:IsCharacter() then
		target_obj:DoStand()
	end

	-- ReviveWGData.Instance:SetRoleReviveInfo(protocol)
	if target_obj:IsMainRole() then
		-- 复活的挂机回调改为由血量变化那边执行
		-- FuhuoWGCtrl.Instance:OnMainRoleRealive()
        target_obj:UpdateCameraFollowTarget(true)
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_REALIVE, target_obj)
	end
end

function FightWGCtrl:OnFixPos(protocol)
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		local m_x, m_y = main_role:GetLogicPos()
		if GameMath.GetDistance(m_x, m_y, protocol.x, protocol.y, false) >= 1 then
			main_role:SetLogicPos(protocol.x, protocol.y)
		end
	end
	-- GameMapHelper.LogicToWorld(x, y)
	-- Scene.Instance:GetMainRole():SetLogicPos(protocol.x, protocol.y)
end

-- 技能目标位置
function FightWGCtrl:OnSkillTargetPos(protocol)
	local obj = Scene.Instance:GetObj(protocol.target_obj_id)
	if obj then
		obj:SetLogicPos(protocol.pos_x, protocol.pos_y)
	end
end

function FightWGCtrl:OnBuffMark(protocol)
	-- print_error("OnBuffMark:::", protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or not obj:IsCharacter() then
		return
	end

	-- if obj:IsMainRole() then
	-- 	print_error("---战斗Buff变更--")
	-- end
	obj:SetBuffList(bit:ll2b(protocol.buff_mark_high, protocol.buff_mark_low))
end

function FightWGCtrl:OnSpecialBuffAdd(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if not obj or not obj:IsCharacter() then return end

	if FIGHT_TYPE.HUIXUE == protocol.fighttype then-- 这里处理一个特殊buff
		obj:AddBuff(BUFF_TYPE.EBT_ADD_BLOOD)
		GlobalTimerQuest:AddDelayTimer(function ()
			if obj and not obj:IsRealDead() then
				obj:RemoveBuff(BUFF_TYPE.EBT_ADD_BLOOD)
			end
		end, 2)
	end
end

-- 攻击者的buff
-- 客户端处理的特殊buff
function FightWGCtrl:OnSpecialEffectInfo2(protocol)
	-- body
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	local deliverer = Scene.Instance:GetObj(protocol.deliverer)
	if not obj or not obj:IsCharacter() or not obj:GetVo() or not deliverer or not deliverer:IsCharacter() then return end

	local percent = obj.vo.hp / obj.vo.max_hp
	-- print_error("HP:::", obj.vo.max_hp, obj.vo.hp, percent)

	local skill_id = -1
	local skill_level = nil
	local skill_cfg = nil
	local effect = nil
	local list = FightWGData.Client_Effect_Type2
	for k,v in pairs(list) do
		if not FightWGData.Instance:HasEffectByClientType(v.client_type) and percent < v.percent then
			effect = {}
			effect.obj_id = protocol.deliverer
			effect.effect_type = 0
			effect.product_method = 0
			effect.product_id = 0
			effect.merge_layer = 1
			effect.buff_type = 0
			if v.client_type == EFFECT_CLIENT_TYPE.ECT_SKILL_20228 then
				skill_id = 1
				skill_level = NewAppearanceWGData.Instance:GetAdvancedSkillLevel(ADVANCED_DATA_TYPE.SHENWU, skill_id)
				skill_cfg = NewAppearanceWGData.Instance:GetAdvancedSkillCfg(ADVANCED_DATA_TYPE.SHENWU, skill_id, skill_level)
				effect.param_list = {[1] = 0, [2] = 0, [3] = skill_cfg and skill_cfg.param2 or 0}
				effect.unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_20228
				effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_SKILL_20228
				-- print_error("add param2:::", EFFECT_CLIENT_TYPE.ECT_SKILL_10150, skill_cfg and skill_cfg.param2, skill_level)
			end

			if skill_cfg then
				self.data:OnEffectInfo(effect)
			end
		end
	end
end

local check_buff_time = 0
function FightWGCtrl:RemoveSpecialEffectInfo2(now_time, elapse_time)
	-- body
	if check_buff_time >= now_time then return end
	check_buff_time = now_time +  1

	if Scene.Instance == nil then
		return
	end

	local mian_role = Scene.Instance:GetMainRole()
	if not mian_role then
		return
	end

	local role = mian_role:GetAttackTarget()
	local has_target =  mian_role:IsFightStateByRole() and role and role:IsCharacter() and role:GetVo()
	local percent = 1
	if has_target then
		percent = role.vo.hp / role.vo.max_hp
	end

	local list = FightWGData.Client_Effect_Type2
	for k,v in pairs(list) do
		if v.percent <= percent or not has_target then
			-- print_error("remove:::", v.unique_key, v.percent, percent, has_target)
			self.data:OnEffectRemove(mian_role:GetObjId(), v.unique_key)
		end
	end
end

-- 受击者的buff
-- 客户端处理的特殊buff
function FightWGCtrl:OnSpecialEffectInfo(protocol)
	-- body
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if not obj or not obj:IsCharacter() or not obj:GetVo() or not obj:IsRole() then return end

	local percent = obj.vo.hp / obj.vo.max_hp
	-- print_error("HP:::", obj.vo.max_hp, obj.vo.hp, percent)

	local skill_id = -1
	local skill_level = nil
	local skill_cfg = nil
	local effect = nil
	local list = FightWGData.Client_Effect_Type
	for k,v in pairs(list) do
		if not FightWGData.Instance:HasEffectByClientType(v.client_type) and percent < v.percent then
			effect = {}
			effect.obj_id = protocol.obj_id
			effect.effect_type = 0
			effect.product_method = 0
			effect.product_id = 0
			effect.merge_layer = 1
			effect.buff_type = 0

			if v.client_type == EFFECT_CLIENT_TYPE.ECT_SKILL_10150 then
				skill_id = 2
				skill_level = NewAppearanceWGData.Instance:GetAdvancedSkillLevel(ADVANCED_DATA_TYPE.WING, skill_id)
				skill_cfg = NewAppearanceWGData.Instance:GetAdvancedSkillCfg(ADVANCED_DATA_TYPE.WING, skill_id, skill_level)
				effect.param_list = {[1] = 0, [2] = 0, [3] = skill_cfg and skill_cfg.param2 or 0}
				effect.unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10150
				effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10150
				-- print_error("add param2:::", EFFECT_CLIENT_TYPE.ECT_SKILL_10150, skill_cfg and skill_cfg.param2, skill_level)
			elseif v.client_type == EFFECT_CLIENT_TYPE.ECT_SKILL_10151 then
				skill_id = 3
				skill_level = NewAppearanceWGData.Instance:GetAdvancedSkillLevel(ADVANCED_DATA_TYPE.WING, skill_id)
				skill_cfg = NewAppearanceWGData.Instance:GetAdvancedSkillCfg(ADVANCED_DATA_TYPE.WING, skill_id, skill_level)
				effect.param_list = {[1] = 0, [2] = 0, [3] = skill_cfg and skill_cfg.param2 or 0}
				effect.unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10151
				effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10151
				-- print_error("add param2:::", EFFECT_CLIENT_TYPE.ECT_SKILL_10151, skill_cfg and skill_cfg.param2, skill_level)
			elseif v.client_type == EFFECT_CLIENT_TYPE.ECT_SKILL_10171 then
				skill_id = 1
				skill_level = NewAppearanceWGData.Instance:GetAdvancedSkillLevel(ADVANCED_DATA_TYPE.FABAO, skill_id)
				skill_cfg = NewAppearanceWGData.Instance:GetAdvancedSkillCfg(ADVANCED_DATA_TYPE.FABAO, skill_id, skill_level)
				effect.param_list = {[1] = 0, [2] = 0, [3] = skill_cfg and skill_cfg.param2 or 0}
				effect.unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10171
				effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10171
				-- print_error("add param2:::", EFFECT_CLIENT_TYPE.ECT_SKILL_10171, skill_cfg and skill_cfg.param2, skill_level)
			elseif v.client_type == EFFECT_CLIENT_TYPE.ECT_SKILL_10172 then
				skill_id = 2
				skill_level = NewAppearanceWGData.Instance:GetAdvancedSkillLevel(ADVANCED_DATA_TYPE.JIANZHEN, skill_id)
				skill_cfg = NewAppearanceWGData.Instance:GetAdvancedSkillCfg(ADVANCED_DATA_TYPE.JIANZHEN, skill_id, skill_level)
				effect.param_list = {[1] = 0, [2] = 0, [3] = skill_cfg and skill_cfg.param2 or 0}
				effect.unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10172
				effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10172
				-- print_error("add param2:::", EFFECT_CLIENT_TYPE.ECT_SKILL_10172, skill_cfg and skill_cfg.param2, skill_level)
			elseif v.client_type == EFFECT_CLIENT_TYPE.ECT_SKILL_10173 then
				skill_id = 3
				skill_level = NewAppearanceWGData.Instance:GetAdvancedSkillLevel(ADVANCED_DATA_TYPE.JIANZHEN, skill_id)
				skill_cfg = NewAppearanceWGData.Instance:GetAdvancedSkillCfg(ADVANCED_DATA_TYPE.JIANZHEN, skill_id, skill_level)
				effect.param_list = {[1] = 0, [2] = 0, [3] = skill_cfg and skill_cfg.param2 or 0}
				effect.unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10173
				effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10173
				-- print_error("add param2:::", EFFECT_CLIENT_TYPE.ECT_SKILL_10173, skill_cfg and skill_cfg.param2, skill_level)
			end

			if skill_cfg then
				self.data:OnEffectInfo(effect)
			end
		end
	end
end

function FightWGCtrl:RemoveSpecialEffectInfo(role)
	-- body
	if not role or not role:IsMainRole() then return end
	local percent = role.vo.hp / role.vo.max_hp

	local list = FightWGData.Client_Effect_Type
	for k,v in pairs(list) do
		if v.percent <= percent then
			-- print_error("remove:::", v.unique_key, percent)
			self.data:OnEffectRemove(role:GetObjId(), v.unique_key)
		end
	end
end


function FightWGCtrl:PlayerEnterFight()
end

function FightWGCtrl:PlayerExitFight()
end

function FightWGCtrl:OnTeamChange()
	-- body
	self:OnTalentEffectInfo()
end

function FightWGCtrl:MainUIInitCallBack()
	self:LoadSelectRect()
	self:OnTalentEffectInfo()
end

function FightWGCtrl:OnTalentEffectInfo()
	local skill_id = 308
	if SocietyWGData.Instance:GetIsInTeam() == 0 or SocietyWGData.Instance:GetTeamMemberCount() < 2 then --该buff队伍人数需要大于或这等于2
		self:RemoveTalentEffectInfo()
		return
	end

	local skill_level = RoleWGData.Instance:GetRoleTalentSkillLevel(308) or 0
	if 0 == skill_level then
		self:RemoveTalentEffectInfo()
		return
	end

	local skill_cfg = RoleWGData.Instance:GetRoleTalentSkillCfg(skill_id, skill_level)
	if not skill_cfg then return end

	local effect = FightWGData.Instance:GetEffectByClientType(EFFECT_CLIENT_TYPE.ECT_SKILL_10160)
	if effect then
		if effect.param_list[3] == skill_cfg.param_a and effect.param_list[4] == skill_cfg.param_b then
			return
		end
		self:RemoveTalentEffectInfo()
	end

	local mian_role = Scene.Instance:GetMainRole()
	effect = {}
	effect.obj_id = mian_role and mian_role:GetObjId() or nil
	effect.effect_type = 0
	effect.product_method = 0
	effect.product_id = 0
	effect.merge_layer = 1
	effect.buff_type = 0

	effect.param_list = {[1] = 0, [2] = 0, [3] = skill_cfg.param_a, [4] = skill_cfg.param_b}
	effect.unique_key = EFFECT_CLIENT_TYPE.ECT_SKILL_10160
	effect.client_effect_type = EFFECT_CLIENT_TYPE.ECT_SKILL_10160
	self.data:OnEffectInfo(effect)
end

function FightWGCtrl:RemoveTalentEffectInfo()
	local mian_role = Scene.Instance:GetMainRole()
	if mian_role then
		self:RemoveEffInfo(mian_role:GetObjId(), EFFECT_CLIENT_TYPE.ECT_SKILL_10160)
	end
end

function FightWGCtrl:RemoveEffInfo(obj_id, unique_key)
	self.data:OnEffectRemove(obj_id, unique_key)
end

function FightWGCtrl:OnBuffAdd(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or not obj:IsCharacter() or obj:IsRealDead() then
		return
	end

	self:SetXiaoTianQuan(protocol)
	-- if obj:IsMainRole() then
	-- 	print_error("---添加buff----", protocol.buff_type, protocol.product_id, protocol.client_effect_type)
	-- end

	obj:AddBuff(protocol.buff_type, protocol.product_id, nil, protocol.client_effect_type)
	self:SetRoleBuffAttr(obj, protocol.buff_type, true)

	if obj:GetDrawObj() then
		local color = nil
		if protocol.buff_type == BUFF_TYPE.EBT_KUANG_BAO then
			color = KuangBao_Color
		end

		if color then
			obj:GetDrawObj():SetIsMultiColor(color)
		end
	end

	if obj:IsMainRole() then
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_ADD_BUFF, protocol.buff_type)

		if SkillWGData.Instance:GetBuffLayerCfgByType(protocol.buff_type) ~= nil then
			MainuiWGCtrl.Instance:FlushSkillBuffShow()
		end
	end
end

function FightWGCtrl:OnBuffRemove(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or not obj:IsCharacter() then
		return
	end

	self:RemoveXiaoTianQuan(protocol)
	-- if obj:IsMainRole() then
	-- 	print_error("---移除buff--", protocol.buff_type, protocol.product_id)
	-- end

	obj:RemoveBuff(protocol.buff_type, protocol.product_id)
	self:SetRoleBuffAttr(obj, protocol.buff_type, false)
	if obj:GetDrawObj() and
		(protocol.buff_type == BUFF_TYPE.EBT_BINGDONG or
		 protocol.buff_type == BUFF_TYPE.EBT_KUANG_BAO) then
		obj:GetDrawObj():SetIsMultiColor(nil)
	end

	if protocol.buff_type == BUFF_TYPE.EBT_TYPE_STAR2 then
		obj:ShowBurstBuffRemoveShow(protocol.reason_type, protocol.product_id)
	end

	if obj:IsMainRole() then
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_REMOVE_BUFF, protocol.buff_type)
		if SkillWGData.Instance:GetBuffLayerCfgByType(protocol.buff_type) ~= nil then
			MainuiWGCtrl.Instance:FlushSkillBuffShow()
		end

		if protocol.buff_type == BUFF_TYPE.EBT_TYPE_CHARGE then
			obj:CheckAccumulationCanSend(protocol.reason_type, protocol.product_id)
		end
	end

	GlobalEventSystem:Fire(ObjectEventType.REMOVE_BUFF, protocol)
end

-- 哮天犬
function FightWGCtrl:SetXiaoTianQuan(protocol)
	-- body
	if protocol.buff_type ~= BUFF_TYPE.EBT_XIAOTIANQUAN then return end

	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if not obj or not obj:IsCharacter() or obj:IsRealDead() then return end
	obj:SetAttr("xiaotianquan_id", 42038)
end

-- 哮天犬
function FightWGCtrl:RemoveXiaoTianQuan(protocol)
	-- body
	if protocol.buff_type ~= BUFF_TYPE.EBT_XIAOTIANQUAN then return end

	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if not obj or not obj:IsCharacter() or obj:IsRealDead() then return end
	obj:SetAttr("xiaotianquan_id", -1)
end

function FightWGCtrl:SetRoleBuffAttr(obj, buff_type, is_show)
	local value = buff_type > 32 and (64 - buff_type) or (32 - buff_type)
	local buff_flag = buff_type > 32 and "buff_mark_high" or "buff_mark_low"
	local table = bit:d2b(value)
	table[value] = is_show and 1 or 0
	local new_value = bit:b2d(table)
	-- print_error("SetRoleBuffAttr:::", buff_flag, new_value, buff_type, value, table)
	obj:SetAttr(buff_flag, new_value)
end

function FightWGCtrl:OnEffectList(protocol)
	--print_error("OnEffectList", protocol.effect_list)
	self.data:OnEffectList(protocol)
	self:OnTalentEffectInfo()

	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or not obj:IsRole() then
		return
	end

	KuafuYeZhanWangChengWGCtrl.Instance:OnYZWCBuffChange()
	obj:CheckBuffShow()
	--FuBenWGCtrl.Instance:GuWuValue()
	-- if TipWGCtrl.Instance and TipWGCtrl.Instance.buff_tip:IsOpen() then
	-- 	TipWGCtrl.Instance.buff_tip:OnFlush()
	-- end
end

function FightWGCtrl:OnEffectInfo(protocol)
	-- print_error("OnEffectInfo:::", protocol)
	self.data:OnEffectInfo(protocol)
	--FuBenWGCtrl.Instance:GuWuValue()

	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or obj:IsDeleted() or not obj:IsCharacter() then
		return
	end

	obj:FlushBuffInfo(protocol.buff_type)
	KuafuYeZhanWangChengWGCtrl.Instance:OnYZWCBuffChange()
	if obj:IsMainRole() then
		MainuiWGCtrl.Instance:TryFlushBuffLayer(protocol.buff_type)
	end
end

function FightWGCtrl:OnEffectRemove(protocol)
	-- print_error("OnEffectRemove:::", protocol)
	self.data:OnEffectRemove(protocol.obj_id, protocol.effect_key, protocol.buff_type)

	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or obj:IsDeleted() or not obj:IsCharacter() then
		return
	end
	
	KuafuYeZhanWangChengWGCtrl.Instance:OnYZWCBuffChange()
	obj:FlushBuffInfo(protocol.buff_type)
	if obj:IsMainRole() then
		MainuiWGCtrl.Instance:TryFlushBuffLayer(protocol.buff_type)
	end
end

function FightWGCtrl:OnFightSpecialFloat(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or not obj:IsCharacter() then
		return
	end

	if obj:IsMainRole() then
		obj:OnFightSpecialFloat(protocol.float_value)
	end
end

function FightWGCtrl:OnSpecialShieldChangeBlood(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or not obj:IsCharacter() then
		return
	end
	local info = {
		obj_id = protocol.obj_id,
		real_hurt = protocol.real_hurt,
		left_times = protocol.left_times,
		max_times = protocol.max_times,
	}
	GlobalEventSystem:Fire(ObjectEventType.SPECIAL_SHIELD_CHANGE, info)
end

function FightWGCtrl:OnSkillPhase(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or not obj:IsMonster() then
		return
	end

	if MAGIC_SKILL_PHASE.READING == protocol.phase then
		obj:StartSkillReading(protocol.skill_id, protocol.param1, protocol.target_obj_id, protocol.target_obj_pos_x, protocol.target_obj_pos_y)
	elseif MAGIC_SKILL_PHASE.END == protocol.phase then
		obj:EndSkillReading(protocol.skill_id)
	end
end

function FightWGCtrl:OnZhiBaoAttack(protocol)
	local obj = Scene.Instance:GetObj(protocol.target_id)
	if nil == obj or not obj:IsCharacter() then
		return
	end
	local deliverer = Scene.Instance:GetObj(protocol.attacker_id)
	local is_shield_self = SettingWGData.Instance:GetSettingData(SETTING_TYPE.SELF_SKILL_EFFECT)
	if is_shield_self then
		if deliverer and deliverer:IsMainRole() then
			return
		end
	end
	local is_shield_other = SettingWGData.Instance:GetSettingData(SETTING_TYPE.SKILL_EFFECT)
	if is_shield_other then
		if deliverer and not deliverer:IsMainRole() then
			return
		end
	end

	local fighttype = FIGHT_TYPE.NORMAL
	if protocol.is_baoji == 1 then
		fighttype = FIGHT_TYPE.BAOJI
	end
	if nil == deliverer then
		obj:DoBeHit(nil, 0, 0, protocol.hurt, fighttype, FIGHT_TEXT_TYPE.BAOJU)
	else
		-- 主角攻击特殊处理
		if deliverer:IsMainRole() then
			obj:DoBeHit(deliverer, 0, 0, protocol.hurt, fighttype, FIGHT_TEXT_TYPE.BAOJU)
		end
	end
end

function FightWGCtrl:NextCanAtkTime()
	return self.last_atk_time
end

function FightWGCtrl.SkillGlobalCDPass()
	return FightWGCtrl.Instance:NextCanAtkTime() + (COMMON_CONSTS.SKILL_GLOBAL_CD / 1000) < Status.NowTime
end

function FightWGCtrl:SendPetSkillReq(target_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPetPerformSkill)
	protocol.target_id = target_id
	protocol:EncodeAndSend()
end

function FightWGCtrl.SendPerformSkillReq(skill_index, attack_index, pos_x, pos_y, target_id, is_specialskill, client_pos_x, client_pos_y, dir)
	-- print_error("发送使用技能", skill_index, attack_index, pos_x, pos_y, target_id, is_specialskill)
	if not FightWGData.Instance:CanSendPerformSkill() then
		return
	end

	dir = dir or math.atan2((pos_y - client_pos_y), (pos_x - client_pos_x))
	local protocol = ProtocolPool.Instance:GetProtocol(CSPerformSkillReq)
	protocol.skill_index = skill_index
	protocol.pos_x = pos_x
	protocol.pos_y = pos_y
	protocol.target_id = target_id
	protocol.is_specialskill = is_specialskill and 1 or 0
	protocol.client_pos_x = client_pos_x
	protocol.client_pos_y = client_pos_y
	protocol.skill_data = attack_index
	protocol.dir = dir or 0
	protocol:EncodeAndSend()
end

function FightWGCtrl.SendRoleReAliveReq(realive_type, is_timeout_req, item_index)
	-- print_log("发送复活协议", realive_type, is_timeout_req, item_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleReAliveReq)
	protocol.realive_type = realive_type or 0
	protocol.is_timeout_req = is_timeout_req or 0
	protocol.item_index = item_index or 0
	protocol:EncodeAndSend()
	FightWGData.Instance:SetRoleReAliveType(realive_type)
end

function FightWGCtrl.SendGetEffectListReq(obj_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetEffectListReq)
	protocol.target_obj_id = obj_id
	protocol:EncodeAndSend()
end

-- 发送冲锋请求
function FightWGCtrl:SendChongfengReq(pos_x, pos_y, pos_type, param1, param2)
	pos_type = pos_type or 0
	local protocol = ProtocolPool.Instance:GetProtocol(CSChongfengReq)
	protocol.pos_x = pos_x
	protocol.pos_y = pos_y
	protocol.scene_id = Scene.Instance:GetSceneId() or 0
	protocol.scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
	protocol.type = pos_type
	protocol.param1 = param1 or -1
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

-- 尝试使用宠物技能
function FightWGCtrl:TryUsePetSkill()
	local main_role = Scene.Instance:GetMainRole()
	local pet_obj_list = main_role and main_role:GetPetObjList()
	local pet_obj = pet_obj_list and pet_obj_list[1]
	local target_obj = GuajiCache.target_obj
	if pet_obj and target_obj then
		local target_x, target_y = target_obj:GetLogicPos()
		local target_id = target_obj:GetObjId()
		pet_obj:TryUsePetSkill(target_x, target_y, target_id)
	end
end

-- 尝试使用角色技能
function FightWGCtrl:TryUseRoleSkill(skill_id, target_obj, skill_x, skill_y, skill_type, is_keep)
	if nil == target_obj and (skill_x == nil or skill_y == nil) then
		return false
	end

	if TaskWGCtrl.Instance:IsFly() then
		return false
	end

	if SkillWGData.Instance:IsBackSkill(skill_id) then
		return false
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return false
	end

	if main_role:HasCantAttackBuff(skill_id) and not SkillWGData.Instance:IsSkillToSelf(skill_id) then
		return false
	end
	-- if main_role:IsChenMo() and not SkillWGData.Instance:IsSkillToSelf(skill_id) then
	-- 	return false
	-- end

	--仙盟战采集车 不可攻击角色
	if main_role:IsXMZCar() and target_obj ~= nil and target_obj:IsCharacter() then
		return false
	end

	if main_role:IsAtkPlaying() then
		return false
	end

	if main_role:IsJump() then
        return false
    end

	local can_use, range = SkillWGData.Instance:CanUseSkill(skill_id)
	-- print_error("尝试使用角色技能", skill_id, can_use, range)
	if not can_use then
		return false
	end

	-- if target_obj ~= nil and target_obj:IsMonster() and target_obj:GetVo() then
	-- 	range = BossWGData.Instance:GetMonsterRangeByid(target_obj:GetVo().monster_id)
	-- end

	if SkillWGData.Skill_Id_285 == skill_id then-- 跃击
		range = COMMON_CONSTS.COMMON_CHONGFENG_MAX_DIS

	elseif SkillWGData.Skill_Id_270 == skill_id then
		local cfg = SkillWGData.GetSkillinfoConfig(skill_id)
		if cfg ~= nil then
			range = cfg.distance or range
		end
	end

	-- 移动的目标点
	local use_type = ATTACK_USE_TYPE.GUAJI
	local x, y
	if skill_x ~= nil and skill_y ~= nil then
		x = skill_x
		y = skill_y
		use_type = ATTACK_USE_TYPE.RANG
	elseif target_obj ~= nil then
		x, y = target_obj:GetLogicPos()
	end

	if x == nil or y == nil then
		return false
	end

	PlaySkillFloatWordWGCtrl.Instance:SetFloatWordSkill(skill_id)
	if self:DoAtkOperate(skill_id, x, y, target_obj, false, range, use_type, skill_type, is_keep) then
		GuajiWGCtrl.Instance:UpdateAtk(Status.NowTime)
		GuajiWGCtrl.Instance:UpdateFollowAtkTarget(Status.NowTime)
	end

	return true
end

-- 攻击操作
function FightWGCtrl:DoAtkOperate(skill_id, x, y, target_obj, is_specialskill, range, use_type, skill_type, is_keep)
	-- 停止采集
	-- if Scene.Instance:GetMainRole():GetIsGatherState() then
	-- 	Scene.SendStopGatherReq()
	-- end
	if Scene.Instance:IsChangeSceneIng() then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	if SkillWGData.Instance:IsSkillToSelf(skill_id) then
        local forward = main_role:GetRoot().gameObject.transform.forward
		local dir = math.atan2(forward.z, forward.x)
        local main_role_x, main_role_y = main_role:GetLogicPos()
        FightWGCtrl.SendPerformSkillReq(
                        SkillWGData.Instance:GetRealSkillIndex(skill_id),
                        1,
                        main_role_x,
                        main_role_y,
                        GameVoManager.Instance:GetMainRoleVo().obj_id,
                        -- AtkCache.target_obj_id,
                        false,
                        main_role_x,
                        main_role_y,
                        dir)
        self.last_atk_time = Status.NowTime
        return
    end
	
	local attack_index = 0
	skill_id, attack_index = SkillWGData.ChangeSkillID(skill_id)
	self.last_atk_time = Status.NowTime

	GuajiWGCtrl.SetAtkValid(true)
	AtkCache.skill_id = skill_id
	AtkCache.attack_index = attack_index
	AtkCache.x = x
	AtkCache.y = y
	AtkCache.is_specialskill = is_specialskill
	AtkCache.target_obj = target_obj
	AtkCache.target_obj_id = (nil ~= target_obj) and target_obj:GetObjId() or COMMON_CONSTS.INVALID_OBJID
	AtkCache.range = range or 1
	AtkCache.use_type = use_type or ATTACK_USE_TYPE.GUAJI
	AtkCache.skill_type = skill_type or ATTACK_SKILL_TYPE.NONE

	GuajiWGCtrl.SetMoveValid(false)
	MoveCache.task_id = 0
	MoveCache.task_type = -1

	if not is_keep then
		local old_target_obj = GuajiCache.target_obj
		GuajiCache.target_obj = target_obj
		GuajiCache.target_obj_id = AtkCache.target_obj_id
		if old_target_obj == nil then
			GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
		end
	end

	return true
end

-- 跳跃操作
function FightWGCtrl:DoJump()
	Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_JUMP1)
end

function FightWGCtrl:BianShenView(protocol)
	local scene_obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil ~= scene_obj and scene_obj:IsRole() then
		scene_obj:SetAttr("bianshen_param", protocol.show_image)
	end
end

function FightWGCtrl:CalculateHurt(total_hurt)
	if total_hurt > -4 then
		return total_hurt, 0
	end
	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.soulboy_lt_id == nil or vo.soulboy_lt_id <= 0 then
		return total_hurt, 0
	end
	local rand = (math.random() - 0.5) * 2
	if rand ~= 0 then
		rand = rand / 10
	end
	local nvshen_hurt = math.floor(vo.base_fujia_shanghai * 0.5 * (1 + rand) * -1)
	local role_hurt = total_hurt - nvshen_hurt
	if role_hurt >= 0 or nvshen_hurt / role_hurt > 0.4 then
		nvshen_hurt = math.floor(total_hurt * 0.3)
		role_hurt = total_hurt - nvshen_hurt
	end
	return role_hurt, nvshen_hurt
end

function FightWGCtrl:OnSCExpBuffInfo(protocol)
	self.data:SetExpBuffList(protocol)
	-- if TipWGCtrl.Instance and TipWGCtrl.Instance.buff_tip:IsOpen() then
	-- 	TipWGCtrl.Instance.buff_tip:OnFlush()
	-- end
end

function FightWGCtrl:SCLoverFbBossConnectionInfo(protocol)
	-- if protocol.fighttype == FIGHT_TYPE.HUIXUE and protocol.product_method == PRODUCT_METHOD.MONSTER_HUIXUE then
	for k, deliverer in pairs(protocol.deliverer_list) do
		if self.huixue_list[protocol.target_obj_id .. "deliverer" .. deliverer] then
			return
		end
		local vo_t = {summoner_obj_id = protocol.target_obj_id, main_deliverer_obj_id = -1, obj_id = deliverer}
		self.huixue_list[protocol.target_obj_id .. "deliverer" .. deliverer] = true
		Scene.Instance:CreateSDX(vo_t, "Strengthen_buff_01")
	end
end

function FightWGCtrl:OnSCSkillSpecialAppe(protocol)
	if protocol.skill_appe == SKILL_SPECIAL_APPE.SKILL_SPECIAL_APPE_TYPE_UPGRADE_IMG_22 then
		self:DingZuiEffect(protocol)
	end
end

--buff爆炸时信息下发
function FightWGCtrl:OnSCExplode(protocol)
	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or not obj:IsRole() or obj:IsRealDead() then
		return
	end
	local explode_reason = protocol.explode_reason or 0
	-- 自爆 对应仙盟战的自爆buff 客户端buff类型 12006
	if explode_reason == EXPLODE_REASON.EXPLODE_REASON_ROLE_BOMB_SELF then
		local draw_obj = obj:GetDrawObj()
		if draw_obj then
			local attach_obj = draw_obj:GetPart(SceneObjPart.Main):GetAttachPoint(AttachPoint.BuffBottom)
			if attach_obj then
                local bundle, asset = ResPath.GeBufftEffect("BUFF_zhadan_03")
                -- EffectManager.Instance:PlayAtTransform(bundle, asset, attach_obj.transform, 3)
                EffectManager.Instance:PlayControlEffect(self, bundle, asset, attach_obj.transform.position,nil,attach_obj.transform)
            end
		end
	end
end

function FightWGCtrl:SetDebugFight()
	self.is_debug_fight_log = not self.is_debug_fight_log
	TipsSystemManager.Instance:ShowSystemTips(self.is_debug_fight_log and "战斗打印已开启" or "战斗打印已关闭")

	if GuajiCache.guaji_type == GuajiType.Auto then
		if self.debug_fight_timer then
			GlobalTimerQuest:CancelQuest(self.debug_fight_timer)
		end
		self.debug_fight_timer = GlobalTimerQuest:AddRunQuest(function()
			self.debug_fight_time = self.debug_fight_time + 0.1
		end, 0.1)
	end
end

function FightWGCtrl:StartGuaji(is_auto_guaji)
	if is_auto_guaji and self.is_debug_fight_log then
		if self.debug_fight_timer then
			GlobalTimerQuest:CancelQuest(self.debug_fight_timer)
		end
		self.debug_fight_time = 0
		self.debug_fight_timer = GlobalTimerQuest:AddRunQuest(function()
			self.debug_fight_time = self.debug_fight_time + 0.1
		end, 0.1)
	elseif not is_auto_guaji then
		GlobalTimerQuest:CancelQuest(self.debug_fight_timer)
		self.debug_fight_time = 0
	end
end

local attack_log = "攻击 ==>> 使用技能id:%s   技能伤害:%s   时间:%s"
local pet_attack_log = "宠物攻击 ==>> 宠物使用技能id:%s   宠物技能伤害:%s   时间:%s"
function FightWGCtrl:MyAttackLog(protocol)
	if FIGHT_TYPE.PET == protocol.fighttype or FIGHT_TYPE.LINGTONG == protocol.fighttype then
		print_error(string.format(pet_attack_log, protocol.skill, protocol.real_blood, self.debug_fight_time))
	else
		print_error(string.format(attack_log, protocol.skill, protocol.real_blood, self.debug_fight_time))
	end
end

function FightWGCtrl:SendPerFormCharge(x, y, index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPerformChargeSkill)
	protocol.pos_x = x
	protocol.pos_y = y
	protocol.skill_index = index
	protocol:EncodeAndSend()
end

function FightWGCtrl:GetDirSkill()
	return self.dir_skill
end
----------------------------------------------------------------------------------------------

function FightWGCtrl:OnSCEffectWindMillFinish(protocol)
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and not main_role:IsDeleted() then
		local m_x, m_y = main_role:GetLogicPos()
		main_role:SetIsAtkPlaying(false)
		self:TryUseRoleSkill(protocol.next_perform_skill_id, nil, m_x, m_y)
	end
end


function FightWGCtrl:SendCurAttentionObj(obj_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSetAttentionObj)
	protocol.obj_id = obj_id
	protocol:EncodeAndSend()
end


-- 秘笈技能
function FightWGCtrl:TryPlayEsotericaSkillEff(skill_id, deliverer, target_id, pos_x, pos_y)
	-- print_error("----秘笈技能----", skill_id, target_id, pos_x, pos_y)
	local skill_data = SkillWGData.Instance:GetEsotericaSkillById(skill_id, 1)
	if not skill_data then
		return
	end

	local cfg = nil

	if skill_data.skill_type == SKILL_TYPE.SKILL_24 then
		cfg = CultivationWGData.Instance:GetEsotericaCfgBySkillId(skill_id)
	elseif skill_data.skill_type == SKILL_TYPE.SKILL_27 then
		cfg = UltimateBattlefieldWGData.Instance:GetTalentSkillCfgBySkillId(skill_id)
	end

	self:TryPlayNoRoleActionSkillEff(cfg, deliverer, target_id, pos_x, pos_y)
end

-- 时装技能
function FightWGCtrl:TryPlayFashionSkillEff(skill_id, deliverer, target_id, pos_x, pos_y)
	local cfg = NewAppearanceWGData.Instance:GetFashionSkillEffectCfgBySkillId(skill_id)
	if not cfg then
		return
	end

	self:TryPlayNoRoleActionSkillEff(cfg, deliverer, target_id, pos_x, pos_y)
end

-- 播放无动作的技能特效
function FightWGCtrl:TryPlayNoRoleActionSkillEff(cfg, deliverer, target_id, pos_x, pos_y)
	if not cfg then
		return
	end

	if deliverer == nil or deliverer:IsDeleted() then
        return
    end

	local deliverer_draw_obj = deliverer:GetDrawObj()
	if deliverer_draw_obj == nil or IsNil(deliverer_draw_obj:GetTransfrom()) then
		return
	end

	local play_pos = deliverer_draw_obj:GetTransfrom().position
	local dir_pos = nil
	if pos_x ~= nil and pos_y ~= nil and pos_x ~= 0 and pos_y ~= 0 then
		local target_x, target_y = GameMapHelper.LogicToWorld(pos_x, pos_y)
		play_pos.x = target_x
		play_pos.z = target_y
		dir_pos = play_pos
	else
		local target_obj = Scene.Instance:GetObj(target_id)
		local target_draw_obj = nil
		if target_obj ~= nil and not target_obj:IsDeleted() and target_obj:IsCharacter() then
			target_draw_obj = target_obj:GetDrawObj()
		end

		if target_draw_obj ~= nil then
			play_pos = target_draw_obj:GetTransfrom().position
			dir_pos = play_pos
		end
	end

	if cfg.is_target == 0 then
		play_pos = deliverer_draw_obj:GetTransfrom().position
	end

	local extra_effect_data = {
		referenceNodeHierarchyPath = "root/hurt_root",
		isAttach = true,
		isRotation = false,
	}

	deliverer:TryPlayNoActorEffect(cfg.skill_bundle, cfg.skill_asset, play_pos, dir_pos, nil, extra_effect_data)
	if deliverer:IsMainRole() then
		AudioManager.PlayAndForget(cfg.sound_bundle, cfg.sound_asset, nil, play_pos.transform)
	end
end



-- 尝试使用角色技能
-- 没走通用那套，通用那套有skill_index 和 技能指示器转化
function FightWGCtrl:TryUseRoleSpecialSkill(skill_id, target_obj, skill_x, skill_y, is_need_tips)
	-- if nil == target_obj and (skill_x == nil or skill_y == nil) then
	-- 	return false
	-- end

	if Scene.Instance:IsChangeSceneIng() then
		return false
	end

	if TaskWGCtrl.Instance:IsFly() then
		return false
	end

	if SkillWGData.Instance:IsBackSkill(skill_id) then
		return false
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role or main_role:IsDeleted() then
		return false
	end

	if main_role:HasCantAttackBuff(skill_id) and not SkillWGData.Instance:IsSkillToSelf(skill_id) then
		return false
	end

	--仙盟战采集车 不可攻击角色
	if main_role:IsXMZCar() and target_obj ~= nil and target_obj:IsCharacter() then
		return false
	end

	if is_need_tips then
		if MultiMountWGData.Instance:IsMyMultiMountTake() then
			local ok_func = function()
				MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.CANCLE_RIDE)
			end
	
			TipWGCtrl.Instance:OpenAlertTips(Language.MultiMount.CancelMultiMountTake, ok_func)
			return
		end
	end

	if main_role:IsTeamFollowState() then
		if is_need_tips then
			local function follow_ok_fun()
				GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
				if main_role ~= nil and not main_role:IsDeleted() then
					main_role:SetIsFollowState(false)
					MainuiWGCtrl.Instance:FlushXunLuStates()
				end
			end

			TipWGCtrl.Instance:OpenAlertTips(Language.FollowState.RelieveTip, follow_ok_fun)
		end
		return false
	end

	if main_role:IsGhost() then
		if is_need_tips then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		end

		return false
	end

	--当处于活动准备期间 不能走出安全区时 禁止技能释放
	local scene_id = Scene.Instance:GetSceneId()
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if act_scene then
		local act_id = act_scene.act_id
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(act_id)
		if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
			if is_need_tips then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActCanNotDoSkill)
			end

			return false
		end
	end

	-- 仙盟答题场景
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.GUILD_ANSWER_FB then
		if is_need_tips then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoSkill)
		end

		return false
	elseif scene_type == SceneType.HotSpring or scene_type == SceneType.KF_HotSpring then
		if is_need_tips then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CantUseSKill)
		end

		return false
	end
	-- if main_role:IsAtkPlaying() then
	-- 	return false
	-- end

	if main_role:IsJump() then
        return false
    end

	if main_role:CantPlayerDoMove(true) then
		return false
	end

-- 	if not main_role:CanDoMove({buff = true}) then
-- 		return false
--    end

	PlaySkillFloatWordWGCtrl.Instance:SetFloatWordSkill(skill_id)
	return true
end


-- 驭兽技能
function FightWGCtrl:TryUseBeastSkill(skill_id, deliverer, target_id, pos_x, pos_y, param1)
	if deliverer:IsRole() then
		if param1 and param1 < 4 then
			local beast_obj = deliverer:FindBeast()
			if beast_obj then
				beast_obj:TryUseBeastSkill(skill_id, pos_x, pos_y, target_id, param1 == 2)
			end
		else
			local cfg = ControlBeastsWGData.Instance:GetGroupSkillDataByGroupSkillID(skill_id)

			if not cfg then
				return
			end

			if deliverer == nil or deliverer:IsDeleted() then
				return
			end

			local deliverer_draw_obj = deliverer:GetDrawObj()
			if deliverer_draw_obj == nil or IsNil(deliverer_draw_obj:GetTransfrom()) then
				return
			end

			local play_pos = deliverer_draw_obj:GetTransfrom().position
			local dir_pos = nil
			if pos_x ~= nil and pos_y ~= nil and pos_x ~= 0 and pos_y ~= 0 then
				local target_x, target_y = GameMapHelper.LogicToWorld(pos_x, pos_y)
				play_pos.x = target_x
				play_pos.z = target_y
				dir_pos = play_pos
			else
				local target_obj = Scene.Instance:GetObj(target_id)
				local target_draw_obj = nil
				if target_obj ~= nil and not target_obj:IsDeleted() and target_obj:IsCharacter() then
					target_draw_obj = target_obj:GetDrawObj()
				end

				if target_draw_obj ~= nil then
					play_pos = target_draw_obj:GetTransfrom().position
					dir_pos = play_pos
				end
			end

			if cfg.is_target == 0 then
				play_pos = deliverer_draw_obj:GetTransfrom().position
			end

			local extra_effect_data = {
				referenceNodeHierarchyPath = "root/hurt_root",
				isAttach = false,
				isRotation = true,
			}

			deliverer:TryPlayNoActorEffect(cfg.skill_bundle, cfg.skill_asset, play_pos, dir_pos, nil, extra_effect_data)
		end
	end
end

-- 活动技能
function FightWGCtrl:TryPlayResidentSkillEff(skill_id, deliverer, target_id, pos_x, pos_y)
	--print_error("----活动技能----", skill_id, target_id, pos_x, pos_y)
	local skill_data = SkillWGData.Instance:GetResidentSkillById(skill_id, 1)
	if not skill_data then
		return
	end

	if skill_data.skill_type == SKILL_TYPE.SKILL_30 then
		if deliverer:IsRole() then
			local task_callinfo_obj = deliverer:GetTaskCallInfoObj()
			if task_callinfo_obj then
				task_callinfo_obj:TryUsePartnerSkill(skill_id, pos_x, pos_y, target_id)
			end
		end
	end
end

-- 选中效果
-- ===================
function FightWGCtrl:LoadSelectRect()
	local bundle, asset = ResPath.GetWidgets("SelectRect")
    self.select_rect_obj_loader = AllocAsyncLoader(self, "select_rect_obj")
	local node = GameObject.Find("GameRoot/UILayer/FightCanvas/Root")
	self.select_rect_obj_parent_rect = node:GetComponent(typeof(UnityEngine.RectTransform))
	self.select_rect_obj_loader:SetParent(node.transform)
	self.select_rect_obj_loader:Load(bundle, asset, function(gameobj)
		self.select_rect_obj = gameobj
		self:SetSelectRectObjActive(false)
	end)
end

function FightWGCtrl:SetSelectRectObjActive(val)
	if self.select_rect_obj ~= nil and self.select_rect_obj_statu ~= val then
		self.select_rect_obj_statu = val
		self.select_rect_obj:SetActive(val)
	end
end

local vet2_zero = Vector2(0, 0)
function FightWGCtrl:SetSelectRectObjUIPos(scene_pos)
	self:SetSelectRectObjActive(true)
	if self.select_rect_obj ~= nil and self.select_rect_obj_parent_rect ~= nil and not IsNil(UICamera) then
		local screen_pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(MainCamera, scene_pos)
		local _, ui_local_pos = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.select_rect_obj_parent_rect,
		screen_pos, UICamera, vet2_zero)
		RectTransform.SetAnchoredPositionXY(self.select_rect_obj.transform, ui_local_pos.x, ui_local_pos.y)
	end
end
-- ===================
-- ===================
-- 触发技能喊招
function FightWGCtrl:TriggerSkillShoutMove(skill_id)
	if skill_id == nil then
		return
	end

	local floder_name, asset = SkillWGData.Instance:GetSkillShoutMoveDataBySkillId(skill_id)
	if (self.is_shout_move) or floder_name == nil or asset == nil then
		return
	end

	self:RemoveShoutMoveDelayTimer()
	self.is_shout_move = true
	local cfg = SkillWGData.Instance:GetSkillExtBaseCfg()
	local cool_time = cfg and cfg.shout_move_cd or 10
	local floder_name, asset = SkillWGData.Instance:GetSkillShoutMoveDataBySkillId(skill_id)

	if floder_name ~= nil and asset ~= nil then
		local bundle, asset = ResPath.GetSkillVoiceResByFolder(floder_name, asset)
		TalkCache.PlayGuideTalkAudio(bundle, asset)
	end

	self.show_effect_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self.is_shout_move = nil
	end, cool_time)
end

--移除喊招定时器
function FightWGCtrl:RemoveShoutMoveDelayTimer()
	if self.shout_move_timer then
        GlobalTimerQuest:CancelQuest(self.shout_move_timer)
        self.shout_move_timer = nil
    end	
end
-- ===================