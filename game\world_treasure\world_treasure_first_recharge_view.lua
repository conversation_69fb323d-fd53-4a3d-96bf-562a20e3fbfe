local FR_REWARD_NUM = 5 --奖励数量

function WorldTreasureView:ReleaseCallBack_FirstRecharge()
    if self.fr_reward_list then
		for k, v in pairs(self.fr_reward_list) do
            v:DeleteMe()
		end

		self.fr_reward_list = nil
	end

    if self.fr_reward_tweener then
		self.fr_reward_tweener:Kill()
		self.fr_reward_tweener = nil
	end
end

function WorldTreasureView:LoadIndexCallBack_FirstRecharge()
    self.first_recharge_grade_change = true
    self.fr_reward_list = {}
    for i = 1, FR_REWARD_NUM do
        self.fr_reward_list[i] = WorldTreasureFrRewardItem.New(self.node_list.fr_reward_list:FindObj("fr_reward_" .. i))
    end

    if not self.fr_reward_tweener then
		RectTransform.SetAnchoredPositionXY(self.node_list["fr_reward_list"].rect, 60, -145)
		self.fr_reward_tweener = self.node_list["fr_reward_list"].gameObject.transform:DOAnchorPosY(-120, 1)
		self.fr_reward_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.fr_reward_tweener:SetEase(DG.Tweening.Ease.Linear)
	end

    XUI.AddClickEventListener(self.node_list["fr_btn_receive"], BindTool.Bind1(self.OnClickFrBtnReceive, self))
    XUI.AddClickEventListener(self.node_list["fr_btn_recharge"], BindTool.Bind1(self.OnClickFrBtnRecharge, self))

    self.node_list.fr_desc.text.text = Language.WorldTreasure.FirstRechargeTitleDes
end

function WorldTreasureView:LoadFrImg()
	local bundle, asset = ResPath.GetWorldTreasureRawImages("jsrh_bg_2")
	self.node_list.fr_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.fr_bg.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_xx_hs_1")
	self.node_list.fr_bg2.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.fr_bg2.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_gg_1")
	self.node_list.fr_img_title2.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.fr_img_title2.raw_image:SetNativeSize()
	end)
end

function WorldTreasureView:ShowIndexCallBack_FirstRecharge()
end

function WorldTreasureView:OnFlush_FirstRecharge(param_t, index)
    if self.first_recharge_grade_change then
        self.first_recharge_grade_change = false
        self:LoadFrImg()
    end
    self:UpdateFirstRechargeView()
end

function WorldTreasureView:UpdateFirstRechargeView()
    local cfg = WorldTreasureWGData.Instance:GetShouChongRewardCfg()
    local reward_flag = WorldTreasureWGData.Instance:GetTreasureShouChongRewardFlag()

    if not cfg or reward_flag == -1 then return end
    -- 0: 不可领取 1: 可领取 2: 已领取
    self.node_list.fr_btn_receive:CustomSetActive(reward_flag == 1)
    self.node_list.fr_btn_receive_remind:CustomSetActive(reward_flag == 1)
    self.node_list.fr_ylq_img:CustomSetActive(reward_flag == 2)
    self.node_list.fr_btn_recharge:CustomSetActive(reward_flag == 0)

    local data_list = SortDataByItemColor(cfg.reward_item)
    for i = 1, FR_REWARD_NUM do
        if i <= #data_list then
            self.node_list.fr_reward_list:FindObj("fr_reward_" .. i):SetActive(true)
            self.fr_reward_list[i]:SetData(data_list[i])
        else
            self.node_list.fr_reward_list:FindObj("fr_reward_" .. i):SetActive(false)
        end
    end
end

function WorldTreasureView:OnClickFrBtnReceive()
    WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_SHOUCHONG_FETCH)
end

function WorldTreasureView:OnClickFrBtnRecharge()
    ViewManager.Instance:Open(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_rapidly)
end

WorldTreasureFrRewardItem = WorldTreasureFrRewardItem or BaseClass(BaseRender)

function WorldTreasureFrRewardItem:__init()

end

function WorldTreasureFrRewardItem:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function WorldTreasureFrRewardItem:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_cell"])
    end
end

function WorldTreasureFrRewardItem:OnFlush()
    if self.data == nil then
        return
    end

    local reward_flag = WorldTreasureWGData.Instance:GetTreasureShouChongRewardFlag()
    --self.item_cell:SetIsUseRoundQualityBg(true)
    self.item_cell:SetData(self.data)
    self.item_cell:SetLingQuVisible(reward_flag == 2)
    self.item_cell:SetRedPointEff(reward_flag == 1)
    --self.item_cell:SetCellBgEnabled(false)
    local cur_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
    local bundle, asset = ResPath.GetWorldTreasureImg("a3_xtyg_yq_2" .. cur_grade)
	self.node_list.bg.image:LoadSprite(bundle, asset, function()
		self.node_list.bg.image:SetNativeSize()
	end)
end