TipsRewardRecordView = TipsRewardRecordView or BaseClass(SafeBaseView)

function TipsRewardRecordView:__init()
	self.view_name = "TipsRewardRecordView"
    self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_draw_record_panel")
end

function TipsRewardRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end

    self.info = nil
    self.title_name = nil
end

function TipsRewardRecordView:LoadCallBack()
    self.record_list = AsyncListView.New(CommonRewardRecordItem, self.node_list.record_list)
end

-- 设置属性值
function TipsRewardRecordView:SetData(info, title_name)
	self.info = info
    self.title_name = title_name
    self:Open()
end

function TipsRewardRecordView:OnFlush(param_t)
    self.node_list.title_view_name.text.text = self.title_name or Language.LuckyGiftBag.RewardListTitle
    self:FlushRecordList()
end

function TipsRewardRecordView:FlushRecordList()
    if not self.info then
        return
    end

    local data_list = self.info
    local is_show_list = not IsEmptyTable(data_list)
    if is_show_list then
        self.record_list:SetDataList(data_list)
    end
    self.node_list.record_list:SetActive(is_show_list)
    self.node_list.no_invite:SetActive(not is_show_list)
end

CommonRewardRecordItem = CommonRewardRecordItem or BaseClass(BaseRender)

function CommonRewardRecordItem:OnFlush()
	if not self.data then
		return
    end

	local name = nil
	local color = nil

	if self.data.item_data and self.data.item_data.item_id then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_data.item_id)
		name = item_cfg and item_cfg.name
		color = item_cfg and item_cfg.color
	end

	local final_name = self.data.input_name or name or ""
	local final_color = self.data.input_color or color or 0
	local final_color_str = ITEM_COLOR[final_color]
	
    
    if self.data.consume_time then
        self.node_list.time.text.text = os.date("%m-%d  %X", self.data.consume_time)
    else
        self.node_list.time.text.text = ""
    end

    local role_name = self.data.role_name or self.data.name
                    or (self.data.item_data and self.data.item_data.name) or RoleWGData.Instance:GetAttr("name")

    local str1 = string.format(Language.SiXiangCall.TxtRecord3, role_name)
    local name = string.format(Language.SiXiangCall.TxtRecord1_2, final_color_str, final_name)
    local num = string.format(Language.SiXiangCall.TxtRecord1_3, final_color_str, self.data.item_data and self.data.item_data.num or 1)
    self.node_list.desc.text.text = str1
    self.node_list.txt_btn.text.text = name
    self.node_list.num.text.text = num
end