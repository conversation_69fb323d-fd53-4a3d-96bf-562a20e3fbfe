CharmComposeView = CharmComposeView or BaseClass(SafeBaseView)
function CharmComposeView:__init()
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(1078, 608)})
	self:AddViewResource(0, "uis/view/cultivation_ui/charm_prefab", "layout_charm_compose")
end

function CharmComposeView:LoadCallBack()
	if not self.compose_item_list then
		self.compose_item_list = {}

		for i = 1, 2 do
			local cell = ItemCell.New(self.node_list["item_pos" .. i])
			self.compose_item_list[i] = cell
		end
	end

	if not self.target_item then
		self.target_item = ItemCell.New(self.node_list["target_item_pos"])
	end

	self.cell_list = {}
	self.load_bar_cell_complete = false
	--self.node_list.title_view_name.text.text = Language.Charm.CharmComposeTitle

	self:LoadNavBar()
	XUI.AddClickEventListener(self.node_list.btn_charm_compose, BindTool.Bind(BindTool.Bind(self.OnClickCompose, self)))
end

function CharmComposeView:ReleaseCallBack()
	if self.cell_list ~= nil then
		for k, v in pairs(self.cell_list) do
			for i, j in pairs(v) do
				j:DeleteMe()
			end
		end
		self.cell_list = nil
	end

	if self.compose_item_list ~= nil then
		for k, v in pairs(self.compose_item_list) do
			v:DeleteMe()
		end
		self.compose_item_list = nil
	end

	if self.target_item then
		self.target_item:DeleteMe()
		self.target_item = nil
	end

	if self.big_toggle_render_list then
		for _, v in pairs(self.big_toggle_render_list) do
			v:DeleteMe()
		end
		self.big_toggle_render_list = nil
	end

	self.big_toggle_obj_list = nil
	self.small_togs_node_list = nil

	self.load_bar_cell_complete = nil
	self.big_type = 1
	self.small_type = 1
	self.can_compose = false
	self.stuff_data_list = {}
end

function CharmComposeView:LoadNavBar()
	local data_list = CultivationWGData.Instance:GetCharmComposeBigItemListInfo()
	local data_length = #data_list

	local toggles_content = self.node_list.toggles_content
	self.big_toggle_obj_list = {}
	self.small_togs_node_list = {}
	for i = 1, data_length do
		self.big_toggle_obj_list[i] = toggles_content:FindObj("SelectBtnTj" .. i)
		self.small_togs_node_list[i] = toggles_content:FindObj("ListTj" .. i)
	end

	self.big_toggle_render_list = {}
	for i = 1, data_length do
		if not IsEmptyTable(data_list[i]) then
			local big_toggle = CharmComposeBigTypeRender.New(self.big_toggle_obj_list[i])
			local data = {
				normal_text = data_list[i][0].name,
				select_text = data_list[i][0].name
			}
			big_toggle:SetData(data)
			self.big_toggle_render_list[i] = big_toggle
			self.big_toggle_obj_list[i]:SetActive(true)
			self.big_toggle_obj_list[i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickComposeBarHandler, self, i))
			self:LoadNavBarItemCell(i, i == #data_list)
		else
			self.big_toggle_obj_list[i]:SetActive(false)
		end
	end
end

function CharmComposeView:LoadNavBarItemCell(index, load_end)
	local res_async_loader = AllocResAsyncLoader(self, "charm_compose_list_cell" .. index)
	local data_list = CultivationWGData.Instance:GetComposeSmallItemListInfo(index)
	local item_count = #data_list

	res_async_loader:Load("uis/view/cultivation_ui/charm_prefab", "charm_compose_list_cell", nil, function(new_obj)
		local item_vo = {}

		for i = 1, item_count do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.small_togs_node_list[index].transform, false)
			obj:GetComponent("Toggle").group = self.small_togs_node_list[index].toggle_group
			local item_render = CharmComposeSmallTypeRender.New(obj)
			item_render.parent_view = self
			item_render:SetIndex(i)
			item_render:SetValueChangedCallBack(BindTool.Bind(self.OnClickAccordionChild, self))
			item_vo[i] = item_render

			if i == item_count and load_end then
				self.load_bar_cell_complete = true
			end
			obj:SetActive(true)
		end
		self.cell_list[index] = item_vo

		if self.load_bar_cell_complete then
			-- self:FlushToggleAllData()
			-- self:SelectToggle()
			self:Flush()
		end
	end)
end

-- 点击大标题
function CharmComposeView:OnClickComposeBarHandler(index, is_on)
	if nil == index or not is_on or self.big_type == index then
		return
	end

	self.big_type = index
	self.small_type = CultivationWGData.Instance:GetCharmComposeSmallTypeSelect(index)
	self.cell_list[self.big_type][self.small_type]:OnSelectChange(true)
	self:FlushMidComposePanel()
end

-- 点击小标题
function CharmComposeView:OnClickAccordionChild(item)
	if nil == item or nil == item.data then
		return
	end

	local data = item.data
	local index = item.index
	self.small_type = index
	self:FlushMidComposePanel()
end

function CharmComposeView:OnClickCompose()
	local can_compose = CultivationWGData.Instance:GetCharmComposeSmallTypeRedmind(self.big_type, self.small_type)
	local data_cfg = CultivationWGData.Instance:GetCharmComposeItemCfg(self.big_type, self.small_type)

	if not IsEmptyTable(data_cfg) then
		if can_compose then
			local eqiup_cfg = CultivationWGData.Instance:GetCharmEquipByItemId(data_cfg.item_id)
			if not IsEmptyTable(eqiup_cfg) then
				local ok_func = function()
					TipWGCtrl.Instance:ShowEffect({
						effect_type = UIEffectName.s_hecheng,
						is_success = true,
						pos = Vector2(0, 0)
					})

					local solt = CultivationWGData.Instance:GetCharmComposeNeedSolt(self.big_type, self.small_type)
					CultivationWGCtrl.Instance:OnCSCharmOperate(CHARM_OPERATE_TYPE.COMPOS_EQUIP, data_cfg.item_id, solt)
				end

				if data_cfg.stuff_num2 > 0 then
					local stuff_name1 = ItemWGData.Instance:GetItemName(data_cfg.stuff_id1)
					local color_name1 = ToColorStr(stuff_name1, ItemWGData.Instance:GetTipsItemColor(data_cfg.stuff_id1))
					local stuff_name2 = ItemWGData.Instance:GetItemName(data_cfg.stuff_id2)
					local color_name2 = ToColorStr(stuff_name2, ItemWGData.Instance:GetTipsItemColor(data_cfg.stuff_id2))
					local text_dec = string.format(Language.Charm.ComposeTips, data_cfg.stuff_num1, color_name1, data_cfg.stuff_num2,
									color_name2)
					TipWGCtrl.Instance:OpenCheckAlertTips(text_dec, ok_func, "charm_compose_tips", Language.Common.NoticeIsClose)
				else
					ok_func()
				end
			end
		else
			local item_num1 = CultivationWGData.Instance:GetCharmComposeStuffNum(data_cfg.stuff_id1)
			local item_num2 = ItemWGData.Instance:GetItemNumInBagById(data_cfg.stuff_id2)
			if item_num1 < data_cfg.stuff_num1 then
				TipWGCtrl.Instance:OpenItem({
					item_id = data_cfg.stuff_id1
				})
			elseif item_num2 < data_cfg.stuff_num2 then
				TipWGCtrl.Instance:OpenItem({
					item_id = data_cfg.stuff_id2
				})
			end
		end
	end
end

function CharmComposeView:FlushMidComposePanel()
	local data_cfg = CultivationWGData.Instance:GetCharmComposeItemCfg(self.big_type, self.small_type)

	if IsEmptyTable(data_cfg) then
		return
	end

	self.target_item:SetData({
		item_id = data_cfg.item_id
	})
	local equip_cfg = CultivationWGData.Instance:GetCharmEquipByItemId(data_cfg.item_id)
	if not IsEmptyTable(equip_cfg) then
		local order_str = string.format(Language.Charm.CharmOrderLevel, equip_cfg.order)
		self.target_item:SetRightTopImageText(order_str)
	end

	local item_num1 = CultivationWGData.Instance:GetCharmComposeStuffNum(data_cfg.stuff_id1)
	self.compose_item_list[1]:SetFlushCallBack(function()
		local enough = item_num1 >= data_cfg.stuff_num1
		local right_text = ToColorStr(item_num1 .. "/" .. data_cfg.stuff_num1, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
		self.compose_item_list[1]:SetRightBottomColorText(right_text)
		self.compose_item_list[1]:SetRightBottomTextVisible(true)
	end)
	self.compose_item_list[1]:SetData({item_id = data_cfg.stuff_id1})
	local compose_item_cfg = CultivationWGData.Instance:GetCharmEquipByItemId(data_cfg.stuff_id1)
	if not IsEmptyTable(compose_item_cfg) then
		local order_str = string.format(Language.Charm.CharmOrderLevel, compose_item_cfg.order)
		self.compose_item_list[1]:SetRightTopImageText(order_str)
	end

	local item_num2 = ItemWGData.Instance:GetItemNumInBagById(data_cfg.stuff_id2)
	self.compose_item_list[2]:SetFlushCallBack(function()
		local enough = item_num2 >= data_cfg.stuff_num2
		local right_text = ToColorStr(item_num2 .. "/" .. data_cfg.stuff_num2, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
		self.compose_item_list[2]:SetRightBottomColorText(right_text)
		self.compose_item_list[2]:SetRightBottomTextVisible(true)
	end)
	self.compose_item_list[2]:SetData({item_id = data_cfg.stuff_id2})

	self.node_list.btn_compose_remind:SetActive(item_num1 >= data_cfg.stuff_num1 and item_num2 >= data_cfg.stuff_num2)
end

function CharmComposeView:OnFlush()
	if not self.load_bar_cell_complete then
		return
	end

	local big_type_data_list = CultivationWGData.Instance:GetCharmComposeBigItemListInfo()
	for i = 1, #big_type_data_list do
		local data_list = CultivationWGData.Instance:GetComposeSmallItemListInfo(i)

		for k, v in pairs(self.cell_list[i]) do
			v:SetData(data_list[k])
		end

		local remind = CultivationWGData.Instance:GetCharmComposeBigTypeRedmind(i)
		self.big_toggle_render_list[i]:SetRemind(remind)
	end

	self:SelectToggle()
	self:FlushMidComposePanel()
end

function CharmComposeView:SelectToggle()
	local defaule_compose_select_bigtype, defaule_compose_select_smalltype = CultivationWGData.Instance:GetCharmComposeDefaultSelect()
	self.big_toggle_obj_list[defaule_compose_select_bigtype].accordion_element.isOn = true
	self.cell_list[defaule_compose_select_bigtype][defaule_compose_select_smalltype]:OnSelectChange(true)
	self.big_type = defaule_compose_select_bigtype
	self.small_type = defaule_compose_select_smalltype
end

-------------------------------------------------------------------------------------------------
CharmComposeSmallTypeRender = CharmComposeSmallTypeRender or BaseClass(BaseRender)
function CharmComposeSmallTypeRender:__init()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function CharmComposeSmallTypeRender:__delete()
	self.parent_view = nil
end

function CharmComposeSmallTypeRender:OnFlush()
	local data = self:GetData()
	if nil == data then
		return
	end

	self.node_list.normal_text.text.text = data.name
	self.node_list.select_text.text.text = data.name

	local remind = CultivationWGData.Instance:GetCharmComposeSmallTypeRedmind(self.data.big_type, self.data.small_type)
	self.node_list.remind:SetActive(remind)
end

function CharmComposeSmallTypeRender:SetValueChangedCallBack(call_back)
	self.value_change_call_back = call_back
end

function CharmComposeSmallTypeRender:OnClickItem(is_on)
	if is_on then
		self.value_change_call_back(self)
	end
end

function CharmComposeSmallTypeRender:OnSelectChange(is_select)
	self.node_list.select:SetActive(is_select)
	self.node_list.normal:SetActive(not is_select)
	self.view.toggle.isOn = is_select
end

-------------------------------
-- 大类型Toggle
-------------------------------
CharmComposeBigTypeRender = CharmComposeBigTypeRender or BaseClass(BaseRender)

function CharmComposeBigTypeRender:OnFlush()
	if self.data then
		self.node_list["normal_text"].text.text = self.data.normal_text
		self.node_list["select_text"].text.text = self.data.select_text
	end
end

function CharmComposeBigTypeRender:SetRemind(is_remind)
	self.node_list["remind"]:SetActive(is_remind)
end
