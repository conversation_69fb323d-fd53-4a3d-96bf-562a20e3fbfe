require("game/act_xianqi_jiefeng/act_xianqi_jiefeng_view")
require("game/act_xianqi_jiefeng/act_xianqi_jiefeng_wg_data")

require("game/act_xianqi_jiefeng/act_xqjf_login")
require("game/act_xianqi_jiefeng/act_xqjf_recharge")
require("game/act_xianqi_jiefeng/act_xqjf_duobei")
require("game/act_xianqi_jiefeng/act_xqjf_xianqi")
require("game/act_xianqi_jiefeng/act_xqjf_item")
require("game/act_xianqi_jiefeng/act_xqjf_xianqi_reward")
require("game/act_xianqi_jiefeng/act_xqjf_laixi")
require("game/act_xianqi_jiefeng/act_xqjf_laixi_downtime")
require("game/act_xianqi_jiefeng/act_xqjf_juanxian")
require("game/act_xianqi_jiefeng/act_xqjf_haoli")
require("game/act_xianqi_jiefeng/act_xqjf_dalian_view")
require("game/act_xianqi_jiefeng/act_xqjf_cap")
require("game/act_xianqi_jiefeng/act_xqjf_role_cap")
require("game/act_xianqi_jiefeng/act_xqjf_server_cap")
require("game/act_xianqi_jiefeng/act_xqjf_kanjia")
require("game/act_xianqi_jiefeng/act_xqjf_laixi_fb_view")

require("game/act_xianqi_jiefeng/act_xqjf_xianqi_rank")
require("game/act_xianqi_jiefeng/xianqi_rank_log_view")


--[[--屏蔽不开的活动
	--转盘
	require("game/act_xianqi_jiefeng/act_xqjf_turntable")
	require("game/act_xianqi_jiefeng/act_xqjf_turntable_record")
	require("game/act_xianqi_jiefeng/act_xqjf_turntable_wg_data")
--]]

ActXianQiJieFengWGCtrl = ActXianQiJieFengWGCtrl or BaseClass(BaseWGCtrl)
function ActXianQiJieFengWGCtrl:__init()
	if ActXianQiJieFengWGCtrl.Instance then
		ErrorLog("[ActXianQiJieFengWGCtrl] Attemp to create a singleton twice !")
	end
	ActXianQiJieFengWGCtrl.Instance = self
	
	self.xianqi_jiefeng_data = ActXianQiJieFengWGData.New()

	--转盘
	-- self.xianqi_jiefeng_turntable_data = XQJFTurnTableData.New()
	-- self.record_view = XQJFTurnTableRecord.New(GuideModuleName.XQJFTurnTableRecord)

	self:SetTabIndex()
	
	self.xianqi_jiefeng_view = ActXianQiJieFengView.New() 
	self.lh_reward_tips = XianQiJieFengRewardTips.New()
	self.xingtian_laixi_downtime_view = XianQiJieFengLaiXiDownTime.New()
	self.xj_reward_tip = ActXianQiJieFengQUJXTipView.New()
	self.bz_dalian_view = XianQiJieFengDaLianView.New()
	self.laixi_fb_view = XianQiJieFengLaiXiFBView.New()

	self.xianqi_rank_log_view = XianQiRankLogView.New()

	self:RegisterAllProtocols()
end

function ActXianQiJieFengWGCtrl:__delete()
	ActXianQiJieFengWGCtrl.Instance = nil

	if self.xianqi_jiefeng_data ~= nil then
		self.xianqi_jiefeng_data:DeleteMe()
		self.xianqi_jiefeng_data = nil
	end

	if self.xianqi_jiefeng_turntable_data ~= nil then
		self.xianqi_jiefeng_turntable_data:DeleteMe()
		self.xianqi_jiefeng_turntable_data = nil
	end

	if self.xianqi_jiefeng_view ~= nil then
		self.xianqi_jiefeng_view:DeleteMe()
		self.xianqi_jiefeng_view = nil
	end

	if self.record_view ~= nil then
		self.record_view:DeleteMe()
		self.record_view = nil
	end

	if self.lh_reward_tips ~= nil then
		self.lh_reward_tips:DeleteMe()
		self.lh_reward_tips = nil
	end

	if self.xingtian_laixi_downtime_view ~= nil then
		self.xingtian_laixi_downtime_view:DeleteMe()
		self.xingtian_laixi_downtime_view = nil
	end

	if self.xj_reward_tip ~= nil then
		self.xj_reward_tip:DeleteMe()
		self.xj_reward_tip = nil
	end

	if self.scene_change_complete then
		GlobalEventSystem:UnBind(self.scene_change_complete)
		self.scene_change_complete = nil
	end

	if self.xianqi_rank_log_view then
		self.xianqi_rank_log_view:DeleteMe()
		self.xianqi_rank_log_view = nil
	end

	if self.bz_dalian_view then
		self.bz_dalian_view:DeleteMe()
		self.bz_dalian_view = nil
	end

	if self.mainui_create_complete then
		GlobalEventSystem:UnBind(self.mainui_create_complete)
		self.mainui_create_complete = nil
	end

	if self.attr_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
		self.attr_change = nil
	end
end

function ActXianQiJieFengWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCWoYaoXianQi, "SCWoYaoXianQi")--我要仙器
	--刑天来袭相关
	self:RegisterProtocol(SCRandActXTLX2Info,"SCRandActXTLX2Info")
	self:RegisterProtocol(SCRAXingTianLaiXi2FinishInfo, "OnSCRAXingTianLaiXi2FinishInfo")--完成
	self:RegisterProtocol(SCRandActXTLX2RefreshInfo, "SCRandActXTLX2RefreshInfo")--刷新
	self:RegisterProtocol(CSRandActXTLX2InfoReq)

	-- self:RegisterProtocol(SCQuanFuJuanXian, "SCQuanFuJuanXian")--全服捐献
	self:RegisterProtocol(SCXianQiZhanLiBiPin, "SCXianQiZhanLiBiPin")--战力比拼
	self:RegisterProtocol(SCXianQiThemeBeiZhanHaoLi, "SCXianQiThemeBeiZhanHaoLi")--备战豪礼
	
    -- self:RegisterProtocol(SCBZTunvlangLayerInfo,'OnSCRATunvlangLayerInfo')--转盘相关
    -- self:RegisterProtocol(SCBZTunvlangDrawRecord,'OnSCRATunvlangDrawRecord')
    -- self:RegisterProtocol(SCBZTunvlangDrawResult,'OnSCRATunvlangDrawResult')

    self:RegisterProtocol(SCRAXianQiRankZhanLiRank, 'OnSCRAXianQiRankZhanLiRank')--仙器冲榜
    self:RegisterProtocol(SCRAXianQiRankRoleInfo, 'OnSCRAXianQiRankRoleInfo')

	self.scene_change_complete = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneChangeComplete, self))
	self.mainui_create_complete = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self))
end

function ActXianQiJieFengWGCtrl:Open(tab_index, param_t)
	tab_index = tab_index or self:GetFirstOpenActivity()
	self.xianqi_jiefeng_view:Open(tab_index)
end

function ActXianQiJieFengWGCtrl:OpenLaiXiFbView()
	self.laixi_fb_view:Open()
end

function ActXianQiJieFengWGCtrl:SetTabIndex()
	local XQJF_Tab_Index = {
		xianqi_jiefeng_login	= 10,		-- 备战登录
		xianqi_jiefeng_shouchong = 20,		-- 备战首充
		xianqi_jiefeng_leichong = 30,		-- 备战累充
		xianqi_jiefeng_duobei = 40,			-- 备战多倍
		xianqi_jiefeng_longhun = 50,		-- 备战龙魂
		xianqi_jiefeng_juanxian = 60,		-- 备战捐献
		xianqi_jiefeng_cap	= 71,			-- 个人战力
		xianqi_jiefeng_cap2 = 72,			-- 全服战力
		xianqi_jiefeng_cap3 = 73,			-- 砍价战力
		xianqi_jiefeng_haoli = 81,			-- 备战好礼tab1
		xianqi_jiefeng_haoli2 = 82,			-- 备战好礼tab2
		xianqi_jiefeng_haoli3 = 83,			-- 备战好礼tab3
		xianqi_jiefeng_haoli4 = 84,			-- 备战好礼tab4
		xianqi_jiefeng_laixi = 90,			-- 备战来袭
		xianqi_jiefeng_turntable = 100,		-- 备战兔女郎
		xianqi_jiefeng_longhun_rank = 110,	-- 仙器冲榜
	}

	self.tab_index_list = {}
	local tb = ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").beizhan_theme_dec
	table.sort(tb, SortTools.KeyLowerSorter("rank_id")) 
	for k,v in ipairs(tb) do
		local tab_index = v.rank_id * 10
		if v.real_id == XQJF_Tab_Index.xianqi_jiefeng_login then
			TabIndex.xianqi_jiefeng_login = tab_index
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_login, v)
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_shouchong then
			TabIndex.xianqi_jiefeng_shouchong = tab_index
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_shouchong, v)
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_leichong then
			TabIndex.xianqi_jiefeng_leichong = tab_index
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_leichong, v)
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_duobei then
			TabIndex.xianqi_jiefeng_duobei = tab_index
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_duobei, v)
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_longhun then
			TabIndex.xianqi_jiefeng_longhun = tab_index
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_longhun, v)
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_juanxian then
			TabIndex.xianqi_jiefeng_juanxian = tab_index
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_juanxian, v)
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_cap then
			TabIndex.xianqi_jiefeng_cap = tab_index + 1
			TabIndex.xianqi_jiefeng_cap2 = tab_index + 2
			TabIndex.xianqi_jiefeng_cap3 = tab_index + 3
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_cap, v)
			for i=1,3 do
				table.insert(self.tab_index_list, tab_index + i)
			end
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_haoli then
			TabIndex.xianqi_jiefeng_haoli = tab_index + 1
			TabIndex.xianqi_jiefeng_haoli2 = tab_index + 2
			TabIndex.xianqi_jiefeng_haoli3 = tab_index + 3
			TabIndex.xianqi_jiefeng_haoli4 = tab_index + 4
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_haoli, v)
			for i=1,4 do
				table.insert(self.tab_index_list, tab_index + i)
			end
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_laixi then
			TabIndex.xianqi_jiefeng_laixi = tab_index
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_laixi, v)
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_turntable then
			TabIndex.xianqi_jiefeng_turntable = tab_index
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_turntable, v)
		elseif v.real_id == XQJF_Tab_Index.xianqi_jiefeng_longhun_rank then
			TabIndex.xianqi_jiefeng_longhun_rank = tab_index
			self.xianqi_jiefeng_data:SetThemeCfgByTabIndex(TabIndex.xianqi_jiefeng_longhun_rank, v)
		end
		table.insert(self.tab_index_list, tab_index)
	end
end

-- 获取排序后第一个开启并且有奖励的活动，没有则选中第一个开启的活动
function ActXianQiJieFengWGCtrl:GetFirstOpenActivity()
	local first_tab_index = nil
	if self.tab_index_list ~= nil then
		for i,v in ipairs(self.tab_index_list) do
			if self.xianqi_jiefeng_data:GetActivityState(v) then
				if self.xianqi_jiefeng_data:GetActivityRewardState(v) > 0 then
					return v
				end
				if first_tab_index == nil then
					first_tab_index = v
				end
			end
		end
	end
	return first_tab_index
end

function ActXianQiJieFengWGCtrl:ShowLoginDayReward(day_index)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_login, "login_reward", {day_index})
	end
end

--登录奖励信息(TianshenRoadWGCtrl 调用（协议公用）)
function ActXianQiJieFengWGCtrl:SCNewLoginGiftRet(protocol)
	self.xianqi_jiefeng_data:SetLoginRewardInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_login, "login_view")
	end
end

--首充返回
function ActXianQiJieFengWGCtrl:SCShouChongRet(protocol)
	self.xianqi_jiefeng_data:SetShouChongInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_shouchong, "first_recharge_view")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_shouchong + 3, "first_recharge_view")
	end
end

--累计充值返回
function ActXianQiJieFengWGCtrl:SCLeiChongHaoLi(protocol)
	self.xianqi_jiefeng_data:SetLeiChongInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_leichong, "leichong_recharge_view")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_leichong + 1, "leichong_recharge_view")
	end
end

--多倍来袭返回
function ActXianQiJieFengWGCtrl:SCDuoBeiJiangLi(protocol)
	self.xianqi_jiefeng_data:SetDuoBeiInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_duobei, "duobei_view")
	end
end

--我要仙器返回
function ActXianQiJieFengWGCtrl:SCWoYaoXianQi(protocol)
	self.xianqi_jiefeng_data:SetXQInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_longhun, "longhun_view")
	end

	if self.lh_reward_tips:IsOpen() then
		self.lh_reward_tips:Flush()
	end
end

function ActXianQiJieFengWGCtrl:OpenLongHunRewardTips()
	self.lh_reward_tips:Open()
end

function ActXianQiJieFengWGCtrl:OpenXTLXFBCountDown()
	if self.xianqi_jiefeng_data:IsInXingTianLaiXiActivity() then
		local activity_info = ActXianQiJieFengWGData.Instance:GetJingLinData()
		if activity_info and activity_info.is_final_boss_skill ~= 1 then
			self.xingtian_laixi_downtime_view:Open()
		end
	end
end

function ActXianQiJieFengWGCtrl:OnSCRAXingTianLaiXi2FinishInfo(protocol)
	self.xianqi_jiefeng_data:SetXTLXFinishInfo(protocol)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.XINGTIANLAIXI_FB then
		ActivityWGCtrl.Instance:OpenActJiseSuanView(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
	end
end

--全民捐献返回
function ActXianQiJieFengWGCtrl:SCQuanFuJuanXian(protocol)
	self.xianqi_jiefeng_data:SetQuanFuJuanXianInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_juanxian, "juanxian")
	end
end

--备战好礼
function ActXianQiJieFengWGCtrl:FlushHLViewByPassDay()
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_haoli, "change_day_flush")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_haoli2, "change_day_flush")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_haoli3, "change_day_flush")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_haoli4, "change_day_flush")
	end
end

--备战好礼
function ActXianQiJieFengWGCtrl:SCXianQiThemeBeiZhanHaoLi(protocol)
	self.xianqi_jiefeng_data:SetBeiZhanHaoLiInfo(protocol)
	local old_day = self.xianqi_jiefeng_data:GetHaoLiOldCurDay()
	local new_day = self.xianqi_jiefeng_data:GetHaoLiDayNum()
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_haoli, "haoli")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_haoli2, "haoli")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_haoli3, "haoli")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_haoli4, "haoli")
	end 
	if old_day ~= new_day then
		self:FlushHLViewByPassDay()
	end
end

--战力比拼
function ActXianQiJieFengWGCtrl:SCXianQiZhanLiBiPin(protocol)
	-- print_error("---------SCXianQiZhanLiBiPin------  protocol = ",protocol)
	self.xianqi_jiefeng_data:SetZhanLiBiPinInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_cap, "role_cap")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_cap2, "server_cap")
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_cap3, "kanjia")
	end 
end

function ActXianQiJieFengWGCtrl:FlushKanJia()
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_cap3, "kanjia")
	end 
end

function ActXianQiJieFengWGCtrl:OpenJXRewardTip(data)
	self.xj_reward_tip:Open()
	self.xj_reward_tip:SetData(data)
end

function ActXianQiJieFengWGCtrl:OnSceneChangeComplete()
	local act_info = self.xianqi_jiefeng_data:GetJingLinData()
	if act_info then
		if act_info.activity_state == ACTIVITY_STATUS.STANDY then
			local npc_scene = self.xianqi_jiefeng_data:GetJiangLinOtherCfg("npc_scene")
			local scene_id = Scene.Instance:GetSceneId()
			if npc_scene == scene_id then
				self:XingTianLaiXiCountDown()
			else
				ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
 			end
 		elseif act_info.activity_state == ACTIVITY_STATUS.CLOSE then
 			ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
		end
	else
		ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
	end
end

function ActXianQiJieFengWGCtrl:SendActivityRewardOp(activity_type, opera_type,param1, param2, param3)
	--print_error("FFFF=====仙器解封活动请求 activity_type", activity_type, opera_type,param1, param2, param3)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = activity_type
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param1 or 0
 	protocol.param_2 = param2 or 0
 	protocol.param_3 = param3 or 0
 	protocol:EncodeAndSend()
 end	

---[[ 刑天来袭
function ActXianQiJieFengWGCtrl:CSRandActXTLX2InfoReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActXTLX2InfoReq)
	protocol:EncodeAndSend()
end

function ActXianQiJieFengWGCtrl:EnterXingTianLaiXiFB()
	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_XINGTIAN_LAIXI2)
end

--刑天来袭(降临)返回
function ActXianQiJieFengWGCtrl:SCRandActXTLX2Info(protocol)
	self.xianqi_jiefeng_data:SetXTLXInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_laixi, "xingtian_laixi")
	end
	self.laixi_fb_view:Flush(0, "xingtian_laixi")
end

-- 刷新波数信息
function ActXianQiJieFengWGCtrl:SCRandActXTLX2RefreshInfo(protocol)
	self.xianqi_jiefeng_data:SetXTLXFlushInfo(protocol)
	if protocol.is_final_boss_skill == 1 then
		Scene.Instance:DeleteObjsByType(SceneObjType.QiongQiLaiXi)
        ActivityWGCtrl.Instance:RemoveActNotice(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
        self.xingtian_laixi_downtime_view:Close()
	else
		self.xingtian_laixi_downtime_view:Flush()
	end
	self.laixi_fb_view:Flush()
end

-- 刑天来袭最后10s特殊倒计时
function ActXianQiJieFengWGCtrl:XingTianLaiXiCountDown()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
	if act_info and (act_info.status == ACTIVITY_STATUS.STANDY or act_info.status == ACTIVITY_STATUS.OPEN) then
		if ActivityWGCtrl.Instance:CheckNowCountDownActType(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD) then
			return
		end

		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
		if not act_cfg then
			return
		end

		local role_level = RoleWGData.Instance:GetRoleLevel()
		if role_level < act_cfg.level or role_level > act_cfg.level_max then
			return
		end

		local info_list = {}
		info_list.act_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD
		info_list.calculate_time = 10
		info_list.timestamp = act_info.next_time
		info_list.image_name = act_info.status == ACTIVITY_STATUS.STANDY and "a1_bosslx_laix"
		info_list.is_auto_close = act_info.status == ACTIVITY_STATUS.OPEN
		ActivityWGCtrl.Instance:OpenActCountDown(info_list)
	else
		ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
	end
end

-- NPC对话修改
function ActXianQiJieFengWGCtrl:CheckXingTianLaiXiNpc(npc_id)
	local npcid = self.xianqi_jiefeng_data:GetJiangLinOtherCfg("npcid")
	if npcid ~= npc_id then
		return false
	end

	local act_info = self.xianqi_jiefeng_data:GetJingLinData()
	if act_info then
		if act_info.activity_state == ACTIVITY_STATUS.STANDY then
			return true, Language.XianQiJieFengAct.NPCTalkStr_1
		elseif act_info.activity_state == ACTIVITY_STATUS.OPEN then
			return true, Language.XianQiJieFengAct.NPCTalkStr_2, BindTool.Bind(self.EnterXingTianLaiXiFB, self)
		end
	end
end

-- 前往NPC
function ActXianQiJieFengWGCtrl:GotoShiLian()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.XINGTIANLAIXI_FB then
		return
	end

	local other_cfg = self.xianqi_jiefeng_data:GetJiangLinOtherCfg()
	other_cfg = other_cfg and other_cfg[1]
	if not other_cfg then
		return
	end
	
	RoleWGCtrl.Instance:SetJumpAlertCheck(other_cfg.npc_scene, function()
		GuajiWGCtrl.Instance:MoveToNpc(other_cfg.npcid, nil, other_cfg.npc_scene)
	end, true)
end
--]]

-----------------------------------转盘---------------------------------------
 function ActXianQiJieFengWGCtrl:OnSCRATunvlangLayerInfo(protocol)
	-- print_error("layer", protocol)
	local old_num = self.xianqi_jiefeng_turntable_data:GetLayerRewardNumByLayer(protocol.layer)
	self.xianqi_jiefeng_turntable_data:SetLayerInfo(protocol)
	local new_num = self.xianqi_jiefeng_turntable_data:GetLayerRewardNumByLayer(protocol.layer)
	--是否reset
	if self.xianqi_jiefeng_view:IsOpen() and new_num - old_num ~= 1 then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_turntable, "turntable", {param1 = "normal"})
	end 

	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_Turntable)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng)

	-- 已刷新提示
	local state = self.xianqi_jiefeng_view:IsOpen() and self.xianqi_jiefeng_view:GetShowIndex() == TabIndex.xianqi_jiefeng_turntable
	if protocol.reset_sign == 1 and state then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OATurnTable.Tip_1)
	end
end

function ActXianQiJieFengWGCtrl:OnSCRATunvlangDrawRecord(protocol)
	self.xianqi_jiefeng_turntable_data:SetRecordInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.XQJFTurnTableRecord)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_turntable, "turntable",{param2 = "record"})
	end 
end

function ActXianQiJieFengWGCtrl:OnSCRATunvlangDrawResult(protocol)
	-- print_error("result", protocol)
	self.xianqi_jiefeng_turntable_data:SetResultInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		self.xianqi_jiefeng_view:Flush(TabIndex.xianqi_jiefeng_turntable, "turntable", {param3 = "result"})
	end 
end

function ActXianQiJieFengWGCtrl:SendOpera(opera_type, param_1, param_2, param_3)
	-- print_error(opera_type, param_1, param_2, param_3)
    local t = {}
    t.rand_activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_TURNTABLE
    t.opera_type = opera_type
    t.param_1 = param_1
    t.param_2 = param_2
    t.param_3 = param_3
    ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

------------龙魂冲榜  start  --------------------------------------------------------------
function ActXianQiJieFengWGCtrl:OnSCRAXianQiRankZhanLiRank(protocol)
	-- print_error("OnSCRAXianQiRankZhanLiRank", protocol)
 	self.xianqi_jiefeng_data:SetLongHunRankLogInfo(protocol)
end

function ActXianQiJieFengWGCtrl:OnSCRAXianQiRankRoleInfo(protocol)
	-- print_error("OnSCRAXianQiRankRoleInfo", protocol)
	self.xianqi_jiefeng_data:SetLongHunRankRoleInfo(protocol)
	if self.xianqi_jiefeng_view:IsOpen() then
		if self.xianqi_jiefeng_view:GetShowIndex() == TabIndex.xianqi_jiefeng_longhun_rank then
			self.xianqi_jiefeng_view:Flush()
		end
	end
end

function ActXianQiJieFengWGCtrl:OpenLongHunRankLogView()
	if not self.xianqi_rank_log_view:IsOpen() then
		self.xianqi_rank_log_view:Open()
	end
end

function ActXianQiJieFengWGCtrl:FlushLongHunRankLogView()
	if self.xianqi_rank_log_view:IsOpen() then
		self.xianqi_rank_log_view:Flush()
	end
end
------------龙魂冲榜  end  --------------------------------------------------------------
---[[ 备战大脸图
function ActXianQiJieFengWGCtrl:MainuiOpenCreateCallBack()
end

function ActXianQiJieFengWGCtrl:RoleAttrChange()
end

function ActXianQiJieFengWGCtrl:CheckNeedOpenDaLian()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "xianqijiefeng_dalian_flag")
	local flag = PlayerPrefsUtil.GetInt(key)
	if flag == 1 then
		if self.mainui_create_complete then
			GlobalEventSystem:UnBind(self.mainui_create_complete)
			self.mainui_create_complete = nil
		end
		return false
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG)
	if not act_info or act_info.status ~= ACTIVITY_STATUS.OPEN then
		return false
	end

	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG)
	if not act_cfg then
		return false
	end

	local role_level = RoleWGData.Instance:GetAttr("level")
	if role_level < act_cfg.level or role_level > act_cfg.level_max then
		if not self.attr_change then
			self.attr_change = BindTool.Bind(self.RoleAttrChange, self)
			RoleWGData.Instance:NotifyAttrChange(self.attr_change, {"level"})
		end
		return false
	elseif self.attr_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
		self.attr_change = nil
	end

	if self.mainui_create_complete then
		GlobalEventSystem:UnBind(self.mainui_create_complete)
		self.mainui_create_complete = nil
	end

	-- ViewManager.Instance:OpenByQueue(self.bz_dalian_view)
	return true
end
--]]