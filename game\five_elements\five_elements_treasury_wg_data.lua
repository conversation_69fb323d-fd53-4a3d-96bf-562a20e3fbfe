
function FiveElementsWGData:InitTreasuryData()
    self.reward_poo_mode_cfg = self.five_elements_cfg.reward_poo_mode --抽奖道具
    self.reward_pool_jump_cfg = self.five_elements_cfg.reward_pool_jump --奖池跳转
    self.reward_pool_cfg = ListToMap(self.five_elements_cfg.reward_pool_info, "seq") --奖池信息配置
    self.item_random_desc_cfg = self.five_elements_cfg.item_random_desc

    self.reward_pool_info = {} --奖池信息
    self.reward_record_info = {} --抽奖结果所有信息
    self.record_server_log = {} --抽奖日志
end


--五行宝库抽奖模式
function FiveElementsWGData:GetTreasuryDrawConsumeCfg()
    return self.reward_poo_mode_cfg
end

--五行宝库抽奖道具list
function FiveElementsWGData:GetTreasuryItemDataChangeList()
    if not self.treasury_item_change_list then
        self.treasury_item_change_list = {}
        local cfg = self.reward_poo_mode_cfg
        for i, v in pairs(cfg) do
            table.insert(self.treasury_item_change_list, v.cost_item_id)
        end
    end
    return self.treasury_item_change_list
end


--主界面红点
function FiveElementsWGData:GetTreasuryRed()
    --背包有道具
    if self:GetTreasuryBagItem() then
        return 1
    end
    
    return 0
end

--有道具就有红点
function FiveElementsWGData:GetTreasuryBagItem()
    local item_list = self:GetTreasuryItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

--获取抽奖的选项
function FiveElementsWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end

--动画延迟时间飘字
function FiveElementsWGData:GetTreasuryDelayTime()
    local index = self:CacheOrGetDrawIndex()
    local is_jump = self:GetJumpAni()
    local time = 0
    if index == 2 and (not is_jump) then --十连抽
        time = 4
    end

    return time
end

-----------------------奖池信息
function FiveElementsWGData:SetDrawPoolInfo(protocol)
    self.reward_pool_info.reward_pool_seq = protocol.reward_pool_seq
    self.reward_pool_info.draw_times = protocol.draw_times
end

function FiveElementsWGData:GetDrawPoolInfo()
    return self.reward_pool_info
end

function FiveElementsWGData:GetMainPoolInfo()
    return self.main_pool_seq or -1
end
-------------------------------

-- 恭喜获得跳过动画
function FiveElementsWGData:SetAnimToggleData(enable_anim)
	self.anim_toggle_enable = enable_anim
end

function FiveElementsWGData:GetAnimToggleData(linghe_type)
	return self.anim_toggle_enable
end
---------------------------------抽奖动画相关

function FiveElementsWGData:SetAniIsplaying(flag)
	self.ani_is_playing = flag
end

function FiveElementsWGData:GetAniIsplaying()
	return self.ani_is_playing or false
end

function FiveElementsWGData:GetJumpAni()
	return self.jump_ani or false
end

function FiveElementsWGData:SetJumpAni()
	self.jump_ani = not self.jump_ani
end

--自动使用奖池跳转道具
function FiveElementsWGData:GetAutoUseItem()
    return self.item_jump or false
end

function FiveElementsWGData:SetAutoUseItem()
    self.item_jump = not self.item_jump
end

-------------------------------------------------

--------------------------------奖励列表信息
function FiveElementsWGData:SetTreasuryResultInfo(protocol)
    self.reward_record_info.mode = protocol.mode
    self.reward_record_info.start_seq = protocol.start_seq
    self.reward_record_info.end_seq = protocol.end_seq
    self.reward_record_info.count = protocol.count
    self.reward_record_info.result_item_list = protocol.result_item_list
end

function FiveElementsWGData:GetTreasuryResultInfo()
    return self.reward_record_info
end

function FiveElementsWGData:GetTreasuryResultEndSeq()
    return self.reward_record_info.end_seq or -1
end

--抽奖item索引
function FiveElementsWGData:GetResultSeq()
    local seq_list = {}
    if self.reward_record_info == nil then
        return seq_list
    end

    local cfg_length = #self.reward_record_info.result_item_list
    for i = 1, cfg_length do
        local item = self.reward_record_info.result_item_list[i]
        if item or item.seq then
            table.insert(seq_list, item.seq)
        end
    end

    return seq_list
end
-------------------------------------------

--奖池跳转配置
function FiveElementsWGData:GetPoolJumpCfg()
    return self.reward_pool_jump_cfg[1]
end

--奖池信息配置
function FiveElementsWGData:GetPoolCfg()
    return self.reward_pool_cfg
end

--抽奖日志数据
function FiveElementsWGData:SetServerlog(protocol)
    self.record_server_log = protocol.record_list
end

function FiveElementsWGData:SetNewServerlog(protocol)
    self.record_new_server_log = protocol.record_item
end

function FiveElementsWGData:UpdateNewServerlog()
    if self.record_new_server_log ~= nil then
        table.insert(self.record_server_log, 1, self.record_new_server_log)
    end
end

function FiveElementsWGData:GetServerloglist()
    return self.record_server_log
end

--当前已达到的奖池
function FiveElementsWGData:GetCurPoolSeq()
    local main_seq = self:GetMainPoolInfo()
    local end_info = self:GetTreasuryResultEndSeq()
    local pool_info = self:GetDrawPoolInfo()

    --使用跳转道具返回的
    if pool_info.reward_pool_seq then
        return pool_info.reward_pool_seq
    end

    --抽奖返回的
    if end_info ~= -1 then
        return end_info
    end
    
    --登录返回的
    if main_seq ~= -1 then
        return main_seq
    end

    return 0
end

function FiveElementsWGData:GetRandomGaiLvinfo()
	return self.item_random_desc_cfg
end