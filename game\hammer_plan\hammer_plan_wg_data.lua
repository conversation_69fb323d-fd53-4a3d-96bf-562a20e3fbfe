HammerPlanWGData = HammerPlanWGData or BaseClass()

function HammerPlanWGData:__init()
	if HammerPlanWGData.Instance then
		print_error("[HammerPlanWGData] Attemp to create a singleton twice !")
	end
	HammerPlanWGData.Instance = self

	self.buff_rmb_buy_flag = 1
	self.buff_reward_flag = 0
	self.buff_rmb_buy_times = 0
	self.week_card_cfg = RechargeWGData.Instance:GetWeekCardCfg()
	RemindManager.Instance:Register(RemindName.HammerPlan, BindTool.Bind(self.GetHammerPlanRemind, self))
end

function HammerPlanWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.HammerPlan)
	HammerPlanWGData.Instance = nil
end

function HammerPlanWGData:GetHammerPlanRemind()
	return self:GetHammerPlanState() and 1 or 0
end

function HammerPlanWGData:SetWeekCardActivityInfo(protocol)
	self.buff_rmb_buy_flag =  protocol.buff_rmb_buy_flag			-- buff直购标记 当天  0未买 1已买    策划是否存活
	self.buff_reward_flag = protocol.buff_reward_flag 				-- buff奖励标记  纯奖励标记  0未领取  1已领取
	self.buff_rmb_buy_times = protocol.buff_rmb_buy_times			-- buff直购购买次数
end

function HammerPlanWGData:GetHammerPlanShowModel()
	return (((self.week_card_cfg or {}).other or {})[1] or {}).cehua_model
end

function HammerPlanWGData:GetHammerPlanBuffVirtualGold()
	return (((self.week_card_cfg or {}).other or {})[1] or {}).buff_virtual_gold_2_daily_use_limit
end

function HammerPlanWGData:GetHammerPlanBuffNeedTime()
	return (((self.week_card_cfg or {}).other or {})[1] or {}).forever_buff_need_times
end

function HammerPlanWGData:GetHammerPlanOpenDayCfg()
	local open_day_cfg =  self.week_card_cfg.open_day
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local current_cfg = {}
	local last_cfg =  {}

	for k, v in pairs(open_day_cfg) do
		if server_day >= v.start_day and server_day <= v.end_day then
			current_cfg = v
			break
		end

		last_cfg = v
	end

	return not IsEmptyTable(current_cfg) and current_cfg or last_cfg
end

function HammerPlanWGData:GetDailyRewardDataList()
	local reward_data_list = {}
	local cfg = self:GetHammerPlanOpenDayCfg()
	local reward_item = cfg and cfg.reward_item

	if not IsEmptyTable(reward_item) then
		for k, v in pairs(reward_item) do
			table.insert(reward_data_list, v)
		end
	end
	
	return reward_data_list
end

function HammerPlanWGData:GetBuffBuyTime()
	return self.buff_rmb_buy_times, self:GetHammerPlanBuffNeedTime()
end

function HammerPlanWGData:HasPermanentPrivilege()
	local is_per = false
	local buff_need_day = self:GetHammerPlanBuffNeedTime() or 0
	if buff_need_day > 0 then
		is_per = self.buff_rmb_buy_times >= buff_need_day
	end

	return is_per
end

function HammerPlanWGData:IsGetDailyReward()
	return self.buff_reward_flag == 1
end

function HammerPlanWGData:AreTherePrivileges()
	return self:HasPermanentPrivilege() or self.buff_rmb_buy_flag == 1, self:HasPermanentPrivilege()
end

function HammerPlanWGData:GetHammerPlanState()
	return self.buff_rmb_buy_flag == 0
end

function HammerPlanWGData:HasHammerPlanPrivilegeState()
	return self.buff_rmb_buy_flag == 1
end