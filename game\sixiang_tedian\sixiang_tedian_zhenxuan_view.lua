SiXiangTeDianZhenXuanView = SiXiangTeDianZhenXuanView or BaseClass(SafeBaseView)

local SIXIANG_ZHENXUAN_NAV_ICON = {
	[1] = "a1_hlck_shi",   --死镰
	[2] = "a1_hlck_kai",	--天斧
	[3] = "a1_hlck_zx",	--诛仙
	[4] = "a1_hlck_dun",	--魔盾
}

local SIXIANG_ZHENXUAN_NAV_TEXTTYPE_CFG = {
	[1] = {vertex1 = "#ffffff", vertex2 = "#c8c7ff",},
	[2] = {vertex1 = "#ffffff", vertex2 = "#ffac54",},
	[3] = {vertex1 = "#ffffff", vertex2 = "#ffe670",},
	[4] = {vertex1 = "#ffffff", vertex2 = "#ff9292",},
}


function SiXiangTeDianZhenXuanView:__init(view_name)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/sixiang_call_prefab", "sixiang_zhenxuan_view")
	self:SetMaskBg(true)
end

function SiXiangTeDianZhenXuanView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
end

function SiXiangTeDianZhenXuanView:ReleaseCallBack()
	self.zhenxuan_item_layout_group = nil
	
	if self.zhenxuan_item_list then
		for k,v in pairs(self.zhenxuan_item_list) do
			v:DeleteMe()
		end
		self.zhenxuan_item_list = nil
	end

	if self.gift_item_list then
		for k,v in pairs(self.gift_item_list) do
			v:DeleteMe()
		end
		self.gift_item_list = nil
	end

	if self.select_gift_tween then
		self.select_gift_tween:Kill()
		self.select_gift_tween = nil
	end

	CountDownManager.Instance:RemoveCountDown("sixiang_tedian_zhenxuan")
end

function SiXiangTeDianZhenXuanView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:RefreshView()
		elseif k == "gift_index" then
			self.select_gift_index = v.gift_index or 1
			self:FlushGiftSelectStatus()
			self:FlushConstItem()
			self:FlushGiftDescImg()
			self:FlushBuyBtnState()
		end
	end
end

function SiXiangTeDianZhenXuanView:InitParam()
	self.select_gift_index = 1
	self.play_gift_index = 0
	self.gift_info_list = {}
	self.zhenxuan_item_layout_group = nil
end

function SiXiangTeDianZhenXuanView:InitPanel()
	local parent = self.node_list.zhenxuan_item_root
	local zhenxuan_item_list = {}
	for i=1,6 do
		zhenxuan_item_list[i] = SiXiangSummonItem.New()
		zhenxuan_item_list[i]:DoLoad(parent)
		zhenxuan_item_list[i]:SetIsShowTips(true)
	end
	self.zhenxuan_item_list = zhenxuan_item_list

	local cell_root = self.node_list.item_cell_root
	local gift_item_list = {}
	local node_num = self.node_list.item_cell_root.transform.childCount
	for i=1,node_num do
		local item = SiXiangZhenXuanGiftItem.New(cell_root:FindObj("sixiang_zhenxuan_gift_item_"..i))
		item:SetIndex(i)
		item:DoLoad(cell_root)
		gift_item_list[i] = item
	end
	self.gift_item_list = gift_item_list
end

function SiXiangTeDianZhenXuanView:InitListener()
	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind1(self.OnClickBuyBtn, self))
	XUI.AddClickEventListener(self.node_list.tip_btn, BindTool.Bind1(self.OnClickTipBtn, self))
end

function SiXiangTeDianZhenXuanView:OnClickTipBtn()
	RuleTip.Instance:SetContent(Language.SiXiangCall.ZhenXuanRuleContent, Language.SiXiangCall.ZhenXuanRuleTitle)
end

function SiXiangTeDianZhenXuanView:OnClickBuyBtn()
	local cfg = self:GetSelectActSaleCfg()
	if cfg then
		SiXiangTeDianWGCtrl.Instance:BuySiXiangTeDian(cfg.subactivity_id, cfg.product_id)
	end
end

function SiXiangTeDianZhenXuanView:GetSelectActSaleCfg()
	local cfg_list = SiXiangTeDianWGData.Instance:GetActSaleCfg(self.cur_act_id)
	if cfg_list then
		return cfg_list[self.select_gift_index]
	end
end

function SiXiangTeDianZhenXuanView:RefreshView()
	self.cur_act_id = SiXiangTeDianWGData.Instance:GetCurZhenXuan()
	self:DefaultSelectGiftIndex()
	self:FlushGiftSelectStatus()
	self:FlushGiftList()
	self:FlushActTime()
	self:FlushConstItem()
	self:FlushBuyBtnState()
	self:FlushGiftDescImg()
end

function SiXiangTeDianZhenXuanView:DefaultSelectGiftIndex()
	local cfg_list = SiXiangTeDianWGData.Instance:GetActSaleCfg(self.cur_act_id)
	if IsEmptyTable(cfg_list) then
		return
	end

	local sale_info = nil
	local select_index = 1
	for i=1,#cfg_list do
		sale_info = SiXiangTeDianWGData.Instance:GetSubActSaleInfo(cfg_list[i].subactivity_id, cfg_list[i].product_id)
		if sale_info and (sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch or sale_info.buy_num < cfg_list[i].limit_buy_times) then
			select_index = i
			break
		end
	end
	self.select_gift_index = select_index
end

-- 把礼包里的道具逐个显示出来
function SiXiangTeDianZhenXuanView:FlushConstItem()
	if self.play_gift_index == self.select_gift_index then
		return
	end
	self.play_gift_index = self.select_gift_index

	local gift_data = self.gift_info_list[self.select_gift_index]
	if not gift_data then
		return
	end

	local gift_cfg = ItemWGData.Instance:GetItemConfig(gift_data.item_id)
	if not gift_cfg then
		return
	end

	local star_level = gift_cfg.item_cell_star_level
	local gift_info_list = ItemWGData.Instance:GetGiftDropList(gift_data.item_id, 1)

	local zhenxuan_item_list = self.zhenxuan_item_list
	for i=1,#zhenxuan_item_list do
		local temp_data = gift_info_list[i]
		if temp_data then
			local data = {item_data = {item_id = temp_data.item_id, star = star_level,}}
			zhenxuan_item_list[i]:SetData(data)
			zhenxuan_item_list[i]:SetVisible(true)
		else
			zhenxuan_item_list[i]:SetVisible(false)
		end
	end

	self:PlaySelectTween()
end

-- 刷新购买按钮
function SiXiangTeDianZhenXuanView:FlushBuyBtnState()
	local cfg = self:GetSelectActSaleCfg()
	if not cfg then
		return
	end

	local sale_info = SiXiangTeDianWGData.Instance:GetSubActSaleInfo(self.cur_act_id, cfg.product_id)
	if not sale_info then
		return
	end

	local btn_text = ""
    if sale_info.status == YuanShenSaleSubActSaleStatus.NotBuy then
        --未购买
       -- btn_text = string.format(Language.SiXiangCall.BuyText, cfg.special_sale_price)

		local act_id, product_id = cfg.subactivity_id, cfg.product_id or 1
		local cfg_list = SiXiangTeDianWGData.Instance:GetActSaleCfg(act_id)
		local target_cfg = cfg_list and cfg_list[product_id]
		local flag = target_cfg.product_id * 100 + act_id
		btn_text = RoleWGData.GetPayMoneyStr(cfg.special_sale_price, GET_GOLD_REASON.GET_GOLD_REASON_YUAN_SHEN_ZHAO_HUAN, flag)
    elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch then
        --已购买未领取（rmb商品会用到）
        btn_text = Language.SiXiangCall.CurCanFetch
    elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyAndFetched then
        --已购买已领取
        btn_text = Language.SiXiangCall.HasFetch
    end
    self.node_list.buy_btn_label.text.text = btn_text

    self.node_list.red_point:SetActive(sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch)
    self.node_list.buy_btn:SetActive(sale_info.status ~= YuanShenSaleSubActSaleStatus.HasBuyAndFetched)
end

---[[ 刷新礼包列表
function SiXiangTeDianZhenXuanView:FlushGiftList()
	local cur_act_id = self.cur_act_id
	local cfg_list = SiXiangTeDianWGData.Instance:GetActSaleCfg(cur_act_id)
	if IsEmptyTable(cfg_list) then
		return
	end

	local gift_info_list = {}
	for i=1,#cfg_list do
		if cfg_list[i].item and cfg_list[i].item[0] then
			gift_info_list[#gift_info_list + 1] = cfg_list[i].item[0]
		end
	end
	self.gift_info_list = gift_info_list

	local item_list = self.gift_item_list
	for i=1,#item_list do
		if cfg_list[i] then
			item_list[i]:SetData(cfg_list[i])
			item_list[i]:SetVisible(true)
		else
			item_list[i]:SetVisible(false)
		end
	end
end

-- 刷新选中礼包状态
function SiXiangTeDianZhenXuanView:FlushGiftSelectStatus()
	local select_index = self.select_gift_index
	local item_list = self.gift_item_list
	for i=1,#item_list do
		item_list[i]:SetSelectEffect(i == select_index)
	end
end

function SiXiangTeDianZhenXuanView:FlushGiftDescImg()
	local gift_data = self.gift_info_list[self.select_gift_index]
	local item_cfg = ItemWGData.Instance:GetItemConfig(gift_data and gift_data.item_id)
	if not item_cfg then
		return
	end

end
--]]

---[[ 活动结束倒计时
function SiXiangTeDianZhenXuanView:FlushActTime()
	CountDownManager.Instance:RemoveCountDown("sixiang_tedian_zhenxuan")
    local sever_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = SiXiangTeDianWGData.Instance:GetTeDianEndTimeStamp()
    if end_time <= sever_time then
        self.node_list.time_label.text.text = ""
        return
    end

    self:UpdateCountDown(sever_time, end_time)

    CountDownManager.Instance:AddCountDown(
        "sixiang_tedian_zhenxuan",
        BindTool.Bind(self.UpdateCountDown, self),
        BindTool.Bind(self.FlushActTime, self),
        end_time,
        nil,
        1
    )
end

function SiXiangTeDianZhenXuanView:UpdateCountDown(elapse_time, total_time)
	local time_str = ToColorStr(TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time)), COLOR3B.DEFAULT_NUM)
	self.node_list.time_label.text.text = string.format(Language.SiXiangCall.CloseTimeDesc, time_str)
end
--]]

function SiXiangTeDianZhenXuanView:PlaySelectTween()
	if self.select_gift_tween then
		return
	end

	if not self.zhenxuan_item_layout_group then
		self.zhenxuan_item_layout_group = self.node_list.zhenxuan_item_root:GetComponent(typeof(UnityEngine.UI.HorizontalLayoutGroup))
	end

	local layout_group = self.zhenxuan_item_layout_group
	local canvas_group = self.node_list.zhenxuan_item_root.canvas_group
	canvas_group.alpha = 0

	local tween_alpha = canvas_group:DoAlpha(0, 1, 0.5)
	tween_alpha:OnUpdate(function ()
		layout_group.spacing = -170 * (1 - canvas_group.alpha)
	end)
	tween_alpha:OnComplete(function ()
		self.select_gift_tween = nil
	end)

	self.select_gift_tween = tween_alpha
end




-------------------------------------------------导航Item-------------------------------------------------------

SiXiangZhenXuanGiftItem = SiXiangZhenXuanGiftItem or BaseClass(BaseRender)

function SiXiangZhenXuanGiftItem:DoLoad(cell_root)
	self.cell_root = cell_root
end

function SiXiangZhenXuanGiftItem:__delete()
	self.cell_root = nil
end

function SiXiangZhenXuanGiftItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.sixiang_zhenxuan_gift_item,BindTool.Bind1(self.OnClickGiftItem,self))
end

function SiXiangZhenXuanGiftItem:OnFlush()
	local data = self:GetData()
	local index = self:GetIndex()

	if self.node_list.zhenxuan_gift_name and not IsEmptyTable(data) then
		self.node_list.zhenxuan_gift_name.text.text =  data.name
		local color_cfg = SIXIANG_ZHENXUAN_NAV_TEXTTYPE_CFG[index] and SIXIANG_ZHENXUAN_NAV_TEXTTYPE_CFG[index] or SIXIANG_ZHENXUAN_NAV_TEXTTYPE_CFG[1]
		local gradient = self.node_list.zhenxuan_gift_name.text:GetOrAddComponent(typeof(UIGradient))
		gradient.Color1 = StrToColor(color_cfg.vertex1)
		gradient.Color2 = StrToColor(color_cfg.vertex2)
	end
	self:FlushSellStatus()
end

function SiXiangZhenXuanGiftItem:OnClickGiftItem()
	local index = self:GetIndex()
	ViewManager.Instance:FlushView(GuideModuleName.SiXiangZhenXuan, 0, "gift_index", {gift_index = index})
	return true
end

function SiXiangZhenXuanGiftItem:SetSelectEffect(enable)
	if not self.node_list then
		self.high_flag = enable
		return
	end
	self.node_list.zhenxuan_gift_select.image.enabled = enable
end

function SiXiangZhenXuanGiftItem:FlushSellStatus()
	local data = self:GetData()
	local act_info = SiXiangTeDianWGData.Instance:GetSubActSaleInfo(data.subactivity_id, data.product_id)
	local buy_num = act_info and act_info.buy_num or 0
	local limit_num = data.limit_buy_times or 0

	self.node_list.all_sell_img:SetActive(buy_num >= limit_num)
end