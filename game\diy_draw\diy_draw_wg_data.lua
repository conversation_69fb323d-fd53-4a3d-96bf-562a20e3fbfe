DIYDrawWGData = DIYDrawWGData or BaseClass()

DIYDRAW_TYPE = {
    DIY1 = 0,
    DIY2 = 1,
}

function DIYDrawWGData:__init()
    if DIYDrawWGData.Instance ~= nil then
		print_error("[DIYDrawWGData] attempt to create singleton twice!")
		return
	end
	DIYDrawWGData.Instance = self

    --DIY1抽奖
    local draw1_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_diy1_draw_auto")
    self.diy1draw_mode_cfg = draw1_cfg.mode --抽奖模式
    self.diy1draw_other_cfg = draw1_cfg.other --抽奖道具
    self.special_reward_pool = ListToMapList(draw1_cfg.special_reward_pool, "grade", "activity_day", "seq") --大奖奖池
    self.reward_pool_choose = ListToMapList(draw1_cfg.reward_pool_choose, "grade", "activity_day") --奖池选择
    self.diy1draw_gailv_cfg = ListToMapList(draw1_cfg.item_random_desc, "grade", "activity_day") --概率展示

    self.diydraw_one_result_info = {}
    self.diydraw_one_record_server_log = {}

    --DIY2抽奖
    local draw2_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_diy2_draw_auto")
    self.diy2draw_mode_cfg = draw2_cfg.mode --抽奖模式
    self.diy2draw_other_cfg = draw2_cfg.other --抽奖道具
    self.diy2_special_reward_pool = ListToMapList(draw2_cfg.special_reward_pool, "grade", "activity_day", "seq") --大奖奖池
    self.diy2_reward_pool_choose = ListToMapList(draw2_cfg.reward_pool_choose, "grade", "activity_day") --奖池选择
    self.diy2draw_gailv_cfg = ListToMapList(draw2_cfg.item_random_desc, "grade", "activity_day") --概率展示

    self.diydraw_two_result_info = {}

    RemindManager.Instance:Register(RemindName.DIYDraw_One, BindTool.Bind(self.GetDrawOneRed, self)) --DIY1
    RemindManager.Instance:Register(RemindName.DIYDraw_Two, BindTool.Bind(self.GetDrawTwoRed, self)) --DIY2
    self:DIYDrawOneRemindInBag(RemindName.DIYDraw_One)
    self:DIYDrawTwoRemindInBag(RemindName.DIYDraw_Two)

    self.skip_spine_draw_1_cache = false
end

function DIYDrawWGData:__delete()
    DIYDrawWGData.Instance = nil

    RemindManager.Instance:UnRegister(RemindName.DIYDraw_One)
    RemindManager.Instance:UnRegister(RemindName.DIYDraw_Two)
end

--------------------------------------dly1--------------------------------
--DIY1抽奖所有信息
function DIYDrawWGData:SetDIY1DrawAllInfo(protocol)
    self.grade = protocol.grade
    self.draw_times = protocol.draw_times
    self.choose_pool_seq_list = protocol.choose_pool_seq_list
end

function DIYDrawWGData:GetDIY1PoolSeq(index)
    if self.choose_pool_seq_list then
        return self.choose_pool_seq_list[index] or 0
    end
end

--DIY抽奖档次
function DIYDrawWGData:GetDIY1Grade()
    return self.grade or 1
end

--抽奖模式
function DIYDrawWGData:GetDIY1DrawConsumeCfg()
    return self.diy1draw_mode_cfg
end

--抽奖道具
function DIYDrawWGData:GetDrawOneItem()
	return self.diy1draw_other_cfg[1]
end

--抽奖道具list
function DIYDrawWGData:GetDrawOneItemDataChangeList()
    if not self.diy1draw_item_change_list then
        self.diy1draw_item_change_list = {}
		table.insert(self.diy1draw_item_change_list, self.diy1draw_other_cfg[1].cost_item_id)
    end
    return self.diy1draw_item_change_list
end

--主界面红点
function DIYDrawWGData:GetDrawOneRed()
    --背包有道具
    if self:GetDrawOneBagItem() then
        return 1
    end

    return 0
end

--是否有道具
function DIYDrawWGData:GetDrawOneBagItem()
    local item_list = self:GetDrawOneItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

--获取抽奖的选项
function DIYDrawWGData:CacheOrGetDIY1DrawIndex(btn_index)
	if btn_index then
		self.cache_diy1draw_btn_index = btn_index
	end

	return self.cache_diy1draw_btn_index
end

--抽奖概率展示
function DIYDrawWGData:GetDrawOneProbabilityInfo()
	local grade = self:GetDIY1Grade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW)
	return self.diy1draw_gailv_cfg[grade] and (self.diy1draw_gailv_cfg[grade][act_day] or self.diy1draw_gailv_cfg[1][1]) or self.diy1draw_gailv_cfg[1][1] --需求 读不到默认拿第一个
end

--大奖奖池
function DIYDrawWGData:GetSpecialRewardPoolCfg(seq)
    local grade = self:GetDIY1Grade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW)
    if self.special_reward_pool[grade] and self.special_reward_pool[grade][act_day] then
        return self.special_reward_pool[grade][act_day][seq] or {}
    end
end

--大奖奖池列表
function DIYDrawWGData:GetSpecialRewardPoolListCfg()
    local grade = self:GetDIY1Grade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW)
    if self.special_reward_pool[grade] and self.special_reward_pool[grade][act_day] then
        return self.special_reward_pool[grade][act_day] or {}
    end
end

--奖池选择
function DIYDrawWGData:GetRewardPoolChooseCfg()
    local grade = self:GetDIY1Grade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW)
    return self.reward_pool_choose[grade] and (self.reward_pool_choose[grade][act_day] or {}) or {}
end

--大奖展示数据
function DIYDrawWGData:GetSpecialRewardShowList()
    local other = self:GetDrawOneItem()
    local str_list = Split(other.show_item_id, "|")
    local data_list = {}

    for k, v in pairs(str_list) do
        table.insert(data_list, { item_id = tonumber(v) })
    end

    return data_list
end

--切换奖池
function DIYDrawWGData:GetChooseRewardPoolList()
    local cfg = self:GetRewardPoolChooseCfg()
    local data_list = {}
    local seq_list = {}
    if cfg == nil then
        return data_list
    end

    for k, v in pairs(cfg) do
        local seq_list = self:GetChooseRewardPoolSeqList(cfg, v.index)
        local cur_select_seq = self:GetDIY1PoolSeq(v.index)
        local data = {
            seq_index = v.index,
            seq_list = seq_list,
            cur_seq = cur_select_seq,
        }
        table.insert(data_list, data)
    end
    --print_error(data_list)
    return data_list
end

function DIYDrawWGData:GetChooseRewardPoolSeqList(cfg, index)
    local seq_list = {}
    local reward_seq = string.split(cfg[index + 1].seq_list, "|")
    if reward_seq == nil then
        return seq_list
    end

    for k, v in pairs(reward_seq) do
        local spceial_reward_cfg = self:GetSpecialRewardPoolCfg(tonumber(v))
        if spceial_reward_cfg then
            local seq_data = {
                seq = tonumber(v),
                item = spceial_reward_cfg[1].item
            }
            table.insert(seq_list, seq_data)
        end
    end

    return seq_list
end

--抽奖结果
function DIYDrawWGData:SetDIY1DrawRewardList(protocol)
    self.diydraw_one_result_info.mode = protocol.mode
    self.diydraw_one_result_info.count = protocol.count
    self.diydraw_one_result_info.result_item_list = protocol.result_item_list
end

function DIYDrawWGData:GetDIY1DrawRewardList()
    return self.diydraw_one_result_info
end

function DIYDrawWGData:GetDIY1StuffCfg(item_id)
    return self:GetDrawOneItem().cost_item_id == item_id
end


--背包道具改变红点
function DIYDrawWGData:DIYDrawOneRemindInBag(remind_name)
    local check_list = self:GetDrawOneItemDataChangeList()
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, check_list, nil)
end

--抽奖日志数据
function DIYDrawWGData:SetDIY1Serverlog(protocol)
    self.diydraw_one_record_server_log = protocol.record_list
end

function DIYDrawWGData:UpdateDIY1NewServerlog()
    if self.record_new_server_log ~= nil then
        table.insert(self.diydraw_one_record_server_log, 1, self.record_new_server_log)
    end
end

function DIYDrawWGData:SetDIY1NewServerlog(protocol)
    self.record_new_server_log = protocol.record_item
    self:UpdateDIY1NewServerlog()
end

function DIYDrawWGData:GetDIY1Serverloglist()
    return self.diydraw_one_record_server_log
end

-- 通用格式抽奖记录
function DIYDrawWGData:GetDIY1DrawRecordlist()
    local record_list = {}
    for i, v in ipairs(self.diydraw_one_record_server_log) do
        local data = {}
        data.item_data = v
        data.consume_time = v.record_time
        table.insert(record_list, data)
    end
    return record_list
end

-- 奖励展示
function DIYDrawWGData:GetDIY1RewardPreviewList()
    local gailv_list = self:GetDrawOneProbabilityInfo()
    local reward_item_list = {}
    for i, v in ipairs(gailv_list) do
        local data = { item_id = v.item_id }
        table.insert(reward_item_list, data)
    end
    return reward_item_list
end

--------------------------------------dly2--------------------------------
--DIY2抽奖所有信息
function DIYDrawWGData:SetDIY2DrawAllInfo(protocol)
    self.diy2_grade = protocol.grade
    self.diy2_draw_times = protocol.draw_times
    self.diy2_choose_pool_seq_list = protocol.choose_pool_seq_list
end

function DIYDrawWGData:GetDIY2PoolSeq(index)
    if self.diy2_choose_pool_seq_list then
        return self.diy2_choose_pool_seq_list[index] or 0
    end
end

--DIY抽奖档次
function DIYDrawWGData:GetDIY2Grade()
    return self.diy2_grade or 1
end

--抽奖模式
function DIYDrawWGData:GetDIY2DrawConsumeCfg()
    return self.diy2draw_mode_cfg
end

--抽奖道具
function DIYDrawWGData:GetDrawTwoItem()
	return self.diy2draw_other_cfg[1]
end

--抽奖道具list
function DIYDrawWGData:GetDrawTwoItemDataChangeList()
    if not self.diy2draw_item_change_list then
        self.diy2draw_item_change_list = {}
		table.insert(self.diy2draw_item_change_list, self.diy2draw_other_cfg[1].cost_item_id)
    end
    return self.diy2draw_item_change_list
end

--主界面红点
function DIYDrawWGData:GetDrawTwoRed()
    --背包有道具
    if self:GetDrawTwoBagItem() then
        return 1
    end

    return 0
end

--是否有道具
function DIYDrawWGData:GetDrawTwoBagItem()
    local item_list = self:GetDrawTwoItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

--获取抽奖的选项
function DIYDrawWGData:CacheOrGetDIY2DrawIndex(btn_index)
	if btn_index then
		self.cache_diy2draw_btn_index = btn_index
	end

	return self.cache_diy2draw_btn_index
end

--抽奖概率展示
function DIYDrawWGData:GetDrawTwoProbabilityInfo()
	local grade = self:GetDIY2Grade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW)
	return self.diy2draw_gailv_cfg[grade] and (self.diy2draw_gailv_cfg[grade][act_day] or self.diy2draw_gailv_cfg[1][1]) or self.diy2draw_gailv_cfg[1][1]
end

--大奖奖池
function DIYDrawWGData:GetDIY2SpecialRewardPoolCfg(seq)
    local grade = self:GetDIY2Grade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW)
    if self.diy2_special_reward_pool[grade] and self.diy2_special_reward_pool[grade][act_day] then
        return self.diy2_special_reward_pool[grade][act_day][seq] or {}
    end
end

--奖池选择
function DIYDrawWGData:GetDIY2RewardPoolChooseCfg()
    local grade = self:GetDIY2Grade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW)
    return self.diy2_reward_pool_choose[grade] and (self.diy2_reward_pool_choose[grade][act_day] or {}) or {}
end

--大奖展示数据
function DIYDrawWGData:GetDIY2SpecialRewardShowList()
    local cfg = self:GetDIY2RewardPoolChooseCfg()
    local data_list = {}
    if cfg == nil then
        return data_list
    end

    for k, v in pairs(cfg) do
        local cur_reward_seq = self:GetDIY2PoolSeq(v.index)
        if cur_reward_seq >= 0 then
            local spceial_reward_cfg = self:GetDIY2SpecialRewardPoolCfg(cur_reward_seq)
            if spceial_reward_cfg then
                local data = {
                    item = spceial_reward_cfg[1].item
                }
                table.insert(data_list, data)
            end
        end
        
    end

    return data_list
end

--背包道具改变红点
function DIYDrawWGData:DIYDrawTwoRemindInBag(remind_name)
    local check_list = self:GetDrawTwoItemDataChangeList()
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, check_list, nil)
end

function DIYDrawWGData:GetDIY2StuffCfg(item_id)
    return self:GetDrawTwoItem().cost_item_id == item_id
end

--切换奖池
function DIYDrawWGData:GetDIY2ChooseRewardPoolList()
    local cfg = self:GetDIY2RewardPoolChooseCfg()
    local data_list = {}
    local seq_list = {}
    if cfg == nil then
        return data_list
    end

    for k, v in pairs(cfg) do
        local seq_list = self:GetDIY2ChooseRewardPoolSeqList(cfg, v.index)
        local cur_select_seq = self:GetDIY2PoolSeq(v.index)
        local data = {
            seq_index = v.index,
            seq_list = seq_list,
            cur_seq = cur_select_seq,
        }
        table.insert(data_list, data)
    end
    --print_error(data_list)
    return data_list
end

function DIYDrawWGData:GetDIY2ChooseRewardPoolSeqList(cfg, index)
    local seq_list = {}
    local reward_seq = string.split(cfg[index + 1].seq_list, "|")
    if reward_seq == nil then
        return seq_list
    end

    for k, v in pairs(reward_seq) do
        local spceial_reward_cfg = self:GetDIY2SpecialRewardPoolCfg(tonumber(v))
        if spceial_reward_cfg then
            local seq_data = {
                seq = tonumber(v),
                item = spceial_reward_cfg[1].item
            }
            table.insert(seq_list, seq_data)
        end
    end

    return seq_list
end


--抽奖结果
function DIYDrawWGData:SetDIY2DrawRewardList(protocol)
    self.diydraw_two_result_info.mode = protocol.mode
    self.diydraw_two_result_info.count = protocol.count
    self.diydraw_two_result_info.result_item_list = protocol.result_item_list
end

function DIYDrawWGData:GetDIY2DrawRewardList()
    return self.diydraw_two_result_info
end

function DIYDrawWGData:SetSkipSpineDraw1Status(is_skip)
    is_skip = is_skip or false
    self.skip_spine_draw_1_cache = is_skip
end

function DIYDrawWGData:GetSkipSpineDraw1Status()
    return self.skip_spine_draw_1_cache or false
end