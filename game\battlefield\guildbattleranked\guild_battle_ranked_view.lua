GuildBattleRankedView = GuildBattleRankedView or BaseClass(SafeBaseView)

local che_scale = 2.2
local stone_scale = 2.6
local npc_scale = 2.6

function GuildBattleRankedView:__init()
	self.active_close = false
	self.view_layer = UiLayer.MainUIHigh
	self:AddViewResource(0, "uis/view/guild_battle_new_ui_prefab", "layout_new_task_panel")
    self.view_name = "GuildBattleRankedView"
    self.is_safe_area_adapter = true

end

function GuildBattleRankedView:LoadCallBack()
 	self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))
    self.guild_xmz_fight_state_event = GlobalEventSystem:Bind(Guill_XMZ_FIGHT_STATE.STATE_CHANGE, BindTool.Bind(self.XMZFightStateChange, self))

	XUI.AddClickEventListener(self.node_list["TaskButton1"],BindTool.Bind(self.OnRankedViewType, self,1))
	XUI.AddClickEventListener(self.node_list["TaskButton2"],BindTool.Bind(self.OnRankedViewType, self,2))
	XUI.AddClickEventListener(self.node_list.guild_info_btn1,BindTool.Bind(self.ClickMoveToBoss, self,1))
	XUI.AddClickEventListener(self.node_list.guild_info_btn2,BindTool.Bind(self.ClickMoveToBoss, self,2))
	XUI.AddClickEventListener(self.node_list.guild_info_btn3,BindTool.Bind(self.ClickMoveToLingShi, self,2024))
	XUI.AddClickEventListener(self.node_list.guild_info_btn4,BindTool.Bind(self.ClickMoveToLingShi, self,2023))
	XUI.AddClickEventListener(self.node_list.go_stone_btn,BindTool.Bind(self.ClickMoveToBoss, self))
	self.node_list["TaskButton1"].toggle.isOn = true
	self:OnRankedViewType(1)

	self.role_machine_type = 0						--现在的形态
	self.rank_list = AsyncListView.New(GuildRankItemNewRender, self.node_list.rank_list)

	local user_info = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local side = user_info and user_info.side or 1
		main_role:SetAttr("yzwc_ico",2 - side)
	end

	self.scale_bool = false
	self.touch_move_flag = true
	local canvas_group = self.node_list["get_lingshi_img"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 0
	local chuanwen_panel_canvas_group = self.node_list["chuanwen_panel"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	chuanwen_panel_canvas_group.alpha = 0
	self.last_move_end_time = 0

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local next_time = act_info and act_info.next_time or 0

	local is_standy = act_info and act_info.status == ACTIVITY_STATUS.STANDY
	local xiusai_state = GuildWGData.Instance:GetIsPrepareRestTime()
	local is_show = is_standy or xiusai_state

	local count_down_time = 0
	if act_info.status == ACTIVITY_STATUS.STANDY then
		count_down_time = next_time - server_time
	elseif GuildWGData.Instance:GetIsPrepareRestTime() then
		local start_hour,start_min,during_time,start_hour2,start_min2,all_second,round_time_1,round_time_2 = GuildBattleRankedWGData.Instance:GetGuildBattleOpenTime()
		local start_time = act_info.start_time
		local round_rest_timer = start_time + round_time_1 + during_time
		count_down_time = round_rest_timer - server_time
	end

	if count_down_time > 0 and is_show then
		if CountDownManager.Instance:HasCountDown("xmz_act_standy_countdown") then
			CountDownManager.Instance:RemoveCountDown("xmz_act_standy_countdown")
		end
		self.node_list["act_standy_timer"]:SetActive(true)
		self:UpdateStandyTimer(0,count_down_time)
		CountDownManager.Instance:AddCountDown("xmz_act_standy_countdown", BindTool.Bind(self.UpdateStandyTimer,self), BindTool.Bind(self.StandyTimerCallBack,self), nil, count_down_time, 1)
	else
		self.node_list["act_standy_timer"]:SetActive(false)
		self:ClickMoveToBoss()
	end

	local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
	self:FlushRuleView()
	self:FlushSideIcon()
end

function GuildBattleRankedView:FlushSideIcon()
	local camp = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo().side
	local btn_name1 = camp == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.BLUETEAM and "a2_zjm_sh" or "a2_zjm_js"
	local btn_name2 = camp == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.REDTEAM and "a2_zjm_sh" or "a2_zjm_js"
	local bundle1, asset1 = ResPath.GetXMZNewImg(btn_name1)
	self.node_list.guild_info_btn1.image:LoadSprite(bundle1, asset1,function ()
		self.node_list.guild_info_btn1.image:SetNativeSize()
	end)

	local bundle2, asset2 = ResPath.GetXMZNewImg(btn_name2)
	self.node_list.guild_info_btn2.image:LoadSprite(bundle2, asset2,function ()
		self.node_list.guild_info_btn2.image:SetNativeSize()
	end)

	self.node_list.guild_info_btn_text1.text.text = camp == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.BLUETEAM and "守护" or "击杀"
	self.node_list.guild_info_btn_text2.text.text = camp == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.REDTEAM and "守护" or "击杀"
end

function GuildBattleRankedView:UpdateStandyTimer(now_time, elapse_time)
	local time = elapse_time - now_time
	local time_str = TimeUtil.FormatSecond(math.floor(time), 2)
	self.node_list["standy_time_str"].text.text = time_str
end


function GuildBattleRankedView:StandyTimerCallBack()
	self.node_list["act_standy_timer"]:SetActive(false)
	self.node_list["TaskButton1"].toggle.isOn = false
	self.node_list["TaskButton2"].toggle.isOn = true
	self:FlushGuildBossInfo()
end

function GuildBattleRankedView:ShrinkButtonsValueChange(isOn)
	if isOn then
		self.node_list.act_standy_timer.rect:DOAnchorPosY(200, 0.3)
		self.node_list.boss_active_time_info.canvas_group:DoAlpha(1, 0, 0.3)
	else

		self.node_list.act_standy_timer.rect:DOAnchorPosY(-210, 0.3)
		self.node_list.boss_active_time_info.canvas_group:DoAlpha(0, 1, 0.3)
	end
end

-- function GuildBattleRankedView:CancleChangeBody()
-- 	GuildBattleRankedWGCtrl.Instance:SendCSGuildBattleOPReq(GUILD_BATTLE_OP_REQ_TYPE.GUILD_BATTLE_REWARD_REQ_TYPE_COMMIT_CANCEL)
-- end

function GuildBattleRankedView:ReleaseCallBack()
	self.role_machine_type = 0

	self.scale_bool = false

	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if nil ~= self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end

	if nil ~= self.path_line then
		self.path_line:DeleteMe()
		self.path_line = nil
	end

	if self.role_head_cell then
		self.role_head_cell:SetHeadCellScale(1)
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	if self.change_head_icon then
		GlobalEventSystem:UnBind(self.change_head_icon)
		self.change_head_icon = nil
	end

	if self.guild_xmz_fight_state_event then
		GlobalEventSystem:UnBind(self.guild_xmz_fight_state_event)
		self.guild_xmz_fight_state_event = nil
	end

		if nil ~= self.eh_load_quit then
		GlobalEventSystem:UnBind(self.eh_load_quit)
		self.eh_load_quit = nil
	end

	if nil ~= self.eh_pos_change then
		GlobalEventSystem:UnBind(self.eh_pos_change)
		self.eh_pos_change = nil
	end
	if nil ~= self.eh_move_end then
		GlobalEventSystem:UnBind(self.eh_move_end)
		self.eh_move_end = nil
	end
	if nil ~= self.reset_pos then
		GlobalEventSystem:UnBind(self.reset_pos)
		self.reset_pos = nil
	end

	if nil ~= self.cannot_find_theway then
		GlobalEventSystem:UnBind(self.cannot_find_theway)
		self.cannot_find_theway = nil
	end

	if self.touch_move_event then
		GlobalEventSystem:UnBind(self.touch_move_event)
		self.touch_move_event = nil
	end

	if CountDownManager.Instance:HasCountDown("xmz_fresh_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("xmz_fresh_time_countdown")
	end

	if CountDownManager.Instance:HasCountDown("xmz_act_standy_countdown") then
		CountDownManager.Instance:RemoveCountDown("xmz_act_standy_countdown")
	end


	if self.get_lingshi_tween then
		self.get_lingshi_tween:Kill()
		self.get_lingshi_tween = nil
	end

	if self.show_chuangwen_tween then
		self.show_chuangwen_tween:Kill()
		self.show_chuangwen_tween = nil
	end
	self:GuildBossClearTimeCount()
	self:GuildBossClearDieTimeCount()
end

function GuildBattleRankedView:XMZFightStateChange()
	local cur_state = GuildWGData.Instance:GetCurGuildWarState()
	if cur_state == GuildWGData.GuildWarState.OneState or cur_state == GuildWGData.GuildWarState.TwoState then
		self:FlushRuleView()
	end
end

function GuildBattleRankedView:ShowGetLingShiVlaue(value)
	local start_pos = u3dpool.vec3(0, 200, 0)
	local end_pos = u3dpool.vec3(0, 250, 0)
	local add_value = value.add_lingshi_value
	self.node_list["get_lingshi_img"]:SetActive(true)
	self.node_list["get_lingshi_value"].text.text = "+"..add_value

	if self.get_lingshi_tween then
		self.get_lingshi_tween:Kill()
		self.get_lingshi_tween = nil
	end

	local canvas_group = self.node_list["get_lingshi_img"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 1
	local tween_alpah = canvas_group:DoAlpha(0, 1, 1)
	self.node_list["get_lingshi_img"].transform.anchoredPosition = start_pos
	local tween_move = self.node_list["get_lingshi_img"].transform:DOAnchorPos(end_pos, 1)
	self.get_lingshi_tween = DG.Tweening.DOTween.Sequence()
	self.get_lingshi_tween:Append(tween_alpah)
	self.get_lingshi_tween:Join(tween_move)
	self.get_lingshi_tween:SetEase(DG.Tweening.Ease.Linear)
	self.get_lingshi_tween:OnComplete(function ()
		if self.node_list["get_lingshi_img"] then
			UITween.AlpahShowPanel(self.node_list["get_lingshi_img"],false,1,DG.Tweening.Ease.Linear)
		end
	end)
end

local GuildSideColor = {
	[0] = {out_line = "#264294",gradient1 = "#6debdb",gradient2 = "#ffffff"},
	[1] = {out_line = "#960e0e",gradient1 = "#ffa093",gradient2 = "#ffffff"}
}

function GuildBattleRankedView:ShowChuangWenPanel(info)
	local data = info
	if IsEmptyTable(data) then
		return
	end

	local start_pos = u3dpool.vec3(0, 200, 0)
	local end_pos = u3dpool.vec3(0, 250, 0)

	if self.node_list["chuanwen_panel"] then
		self.node_list["chuanwen_panel"]:SetActive(true)
	--local reason_type = data.reason_type
	for i=1,4 do
		if nil ~= data.img_list[i]  then
			self.node_list["chuangwen_img"..i]:SetActive(true)
			self.node_list["chuangwen_img"..i].image:LoadSprite(data.img_list[i].bundle,data.img_list[i].asset,function ()
				self.node_list["chuangwen_img"..i].image:SetNativeSize()
			end)
		else
			self.node_list["chuangwen_img"..i]:SetActive(false)
		end
		if data.text_list[i] and data.text_list[i] ~= "" then
			self.node_list["chuangwen_text"..i]:SetActive(true)
			self.node_list["chuangwen_text"..i].text.text = data.text_list[i]
			-- if data.need_color and data.need_color[i] then
			-- 	local other_side = data.other_side
			-- 	local side_color = GuildSideColor[other_side]
			-- 	self.node_list["chuangwen_text"..i].gradient.Color1 = StrToColor(side_color.gradient1)
			-- 	self.node_list["chuangwen_text"..i].gradient.Color2 = StrToColor(side_color.gradient2)
			-- 	self.node_list["chuangwen_text"..i].out_line.effectColor = StrToColor(side_color.out_line)
			-- end
		else
			self.node_list["chuangwen_text"..i]:SetActive(false)
		end
	end
	-- local side = data.side
	-- local side_color = GuildSideColor[side]
	-- self.node_list["chuangwen_side"].gradient.Color1 = StrToColor(side_color.gradient1)
	-- self.node_list["chuangwen_side"].gradient.Color2 = StrToColor(side_color.gradient2)
	-- self.node_list["chuangwen_side"].out_line.effectColor = StrToColor(side_color.out_line)
	-- self.node_list["chuangwen_side"].text.text = data.side_str

	if self.show_chuangwen_tween then
		self.show_chuangwen_tween:Kill()
		self.show_chuangwen_tween = nil
	end

	local canvas_group = self.node_list["chuanwen_panel"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 1
	local tween_alpah = canvas_group:DoAlpha(0, 1, 1)
	self.node_list["chuanwen_panel"].transform.anchoredPosition = start_pos
	local tween_move = self.node_list["chuanwen_panel"].transform:DOAnchorPos(end_pos, 1)
	self.show_chuangwen_tween = DG.Tweening.DOTween.Sequence()
	self.show_chuangwen_tween:Append(tween_alpah)
	self.show_chuangwen_tween:Join(tween_move)
	self.show_chuangwen_tween:SetEase(DG.Tweening.Ease.Linear)
	self.show_chuangwen_tween:OnComplete(function ()
			TryDelayCall(self, function ()
				if self.node_list["chuanwen_panel"] then
					UITween.AlpahShowPanel(self.node_list["chuanwen_panel"],false,1,DG.Tweening.Ease.Linear)
				end
			end, 1, "delay_alpah_show_chaungwen")
		end)
	end
end

function GuildBattleRankedView:OnFlush(param_t)
	if not self:IsLoaded() then
		return
	end
	for k,v in pairs(param_t) do
		if k == "all" then
			-- self:FlushRank()
			self:FlushXMZInfo()
		-- elseif k == "che" then
		-- 	self:FlushMapCheIcon()
		-- elseif k == "stone" then
		-- 	self:FlushMapStoneIcon()
		elseif k == "boss_info" then
			self:FlushGuildBossInfo()
			self:FlushBossActiveTime()
		elseif k == "activity" then
			self:FlushRuleView()
			self:ClickMoveToBoss()
		elseif k == "add_lingshi_value" then
			self:ShowGetLingShiVlaue(v)
		-- elseif k == "lingshi_value_change" then
		-- 	self:FlushXMZLingShiValue(v)
		-- elseif k == "show_chuangwen" then
		-- 	self:ShowChuangWenPanel(v)
		end
	end	
end

function GuildBattleRankedView:FlushRank(data)
	if not self:IsLoaded() then return end
	local battle_rank_list = GuildBattleRankedWGData.Instance:GetBattleRankList()
	if not IsEmptyTable(battle_rank_list) and self.rank_list then
		local uuid = RoleWGData.Instance:GetUUid()
		self.rank_list:SetDataList(battle_rank_list,0)
		for k,v in pairs(battle_rank_list) do
			if v.user_id == uuid then
				if self.node_list.my_ranknum ~= nil and self.node_list.my_jifennum ~= nil then
					self.node_list.my_ranknum.text.text = v.rank
					self.node_list.my_jifennum.text.text = v.total_score
					local name = v.user_name
					local name_list = Split(v.user_name, "_")
					if not IsEmptyTable(name_list) then
						name = name_list[1]
					end
					self.node_list.my_name.text.text = name
					local max_info = GuildBattleRankedWGData.Instance:GetMaxBattleRankList()
					local max_score = max_info.total_score > 0 and max_info.total_score or 1
					local slider = tonumber(v.total_score) / tonumber(max_score)
					--self.node_list["my_rank_slider"].slider.value = slider
				end
			end
		end
	end
	local lingshi_cfg = GuildBattleRankedWGData.Instance:GetLingShiCfg()
	local lingshi_list_num = GuildBattleRankedWGData.Instance:GetLingShiListNum()
	local stone_num1 = lingshi_list_num[lingshi_cfg[2024].lingshi_gather] or 0
	local stone_num2 = lingshi_list_num[lingshi_cfg[2023].lingshi_gather] or 0
end

--TODO
function GuildBattleRankedView:FlushXMZInfo()
	local lingshi_cfg = GuildBattleRankedWGData.Instance:GetLingShiCfg()
	local lingshi_list_num = GuildBattleRankedWGData.Instance:GetLingShiListNum()
	local stone_num1 = lingshi_list_num[lingshi_cfg[2024].lingshi_gather] or 0
	local stone_num2 = lingshi_list_num[lingshi_cfg[2023].lingshi_gather] or 0

	self.node_list["guild_top_num3"].text.text = string.format(Language.GuildBattleRanked.StoneNumStr,stone_num1)
	self.node_list["guild_top_num4"].text.text = string.format(Language.GuildBattleRanked.StoneNumStr,stone_num2)
	self.node_list["guild_bom_num3"].text.text = string.format(Language.GuildBattleRanked.GuildHasStoneNum,lingshi_cfg[2024].commit_reward_value)
	self.node_list["guild_bom_num4"].text.text = string.format(Language.GuildBattleRanked.GuildHasStoneNum,lingshi_cfg[2023].commit_reward_value)

	local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local bianshen_count = other_cfg.bianshen_count or 2
	local has_num = role_info_list and role_info_list.machine_count or 0
	local has_num_str = bianshen_count - has_num
	local has_gather_id = role_info_list and role_info_list.gather_id or 0
	local color = has_num_str > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
end

function GuildBattleRankedView:LingShiValueTextChange(text_obj,new_value)
	local old_value = text_obj.text
	local complete_fun = function()
		if text_obj then
	        text_obj.text = new_value
	    end
    end
    local update_fun = function(num)
    	if text_obj then
	        text_obj.text = math.ceil(num)
	    end
    end
    UITween.DONumberTo(text_obj, old_value, new_value, 1.5, update_fun, complete_fun)
end

function GuildBattleRankedView:FlushRuleView()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local is_standy = act_info and act_info.status == ACTIVITY_STATUS.STANDY
	local xiusai_state = GuildWGData.Instance:GetIsPrepareRestTime()
	local is_show = is_standy or xiusai_state
	if is_show then
		self.node_list["TaskButton1"].toggle.isOn = true
		self.node_list["TaskButton2"].toggle.isOn = false
	else
		self.node_list["TaskButton1"].toggle.isOn = false
		self.node_list["TaskButton2"].toggle.isOn = true
	end
end

function GuildBattleRankedView:StartGuaJiEvent()
	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local side = role_info_list.side
	local npc_id = GuildBattleRankedWGData.Instance:GetGuildNpcId(side)
	ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = npc_id})
	GuajiWGCtrl.Instance:StopGuaji()
end

function GuildBattleRankedView:OnClickUseSkill(machine_type)
	if not GuildBattleRankedWGData.Instance:GetRoleSpecialAppearance() then
		self.role_machine_type = 0
	end
	if self.role_machine_type == machine_type then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBattleRanked.XiangTongXieHun)
		return
	end

	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local other_info = GuildBattleRankedWGData.Instance:GetCfgOther()
	if other_info.bianshen_count <= role_info_list.machine_count then
		local str = Language.GuildBattleRanked.BattleChangeBodyLimitNum
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		return
	end

	self.role_machine_type = machine_type
	GuildBattleRankedWGCtrl.Instance:SendGuildBattleBuyMachineReq(machine_type)
end


function GuildBattleRankedView:OnRankedViewType(index)
	if index == 1 then
		self.node_list.layout_info_display:SetActive(true)
		self.node_list.layout_explain_display:SetActive(false)
	else
		self.node_list.layout_info_display:SetActive(false)
		self.node_list.layout_explain_display:SetActive(true)
	end
end

function GuildBattleRankedView:GuaJiMoveToCheck()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local is_standy = act_info and act_info.status == ACTIVITY_STATUS.STANDY
	local xiusai_state = GuildWGData.Instance:GetIsPrepareRestTime()
	local not_guaji = is_standy or xiusai_state
	if not_guaji then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBattleRanked.ActNotOpenGuaji)
		return false
	end

	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
	return true

end

function GuildBattleRankedView:ClickMoveToBoss(index)
	if not self:GuaJiMoveToCheck() then
		return
	end
	local scene_id = Scene.Instance:GetSceneId()
    GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)
	if nil ~= index then
		local pos_x, pos_y = GuildBattleRankedWGData.Instance:GetMonsterGuaJiPos(index)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y,3)
	else
		local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
		local side = role_info_list and role_info_list.side or 0
		local pos_x,pos_y =  GuildBattleRankedWGData.Instance:GetGuildGuaJiPos(side)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y,3)
	end
end

function GuildBattleRankedView:ClickMoveToLingShi(gather_id)
	if not self:GuaJiMoveToCheck() then
		return
	end

	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
		local gather_list = Scene.Instance:GetGatherList()
		if not IsEmptyTable(gather_list) then
			local gather_item = nil
			for k,v in pairs(gather_list)do
				if v.gather_config.id == 2023 or v.gather_config.id == 2024 then
					GuajiWGCtrl.Instance:ClearCurGuaJiInfo(false,false,true)
					local target_x, target_y = v:GetLogicPos()
					gather_item = v
					MoveCache.SetEndType(MoveEndType.Gather)
					MoveCache.param1 = v.gather_config.id
					MoveCache.target_obj = gather_item
					GuajiCache.target_obj_id = v.gather_config.id
					GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(),target_x,target_y,3.5)
					break
				end
			end
		else
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end)

	local scene_id = Scene.Instance:GetSceneId()
	local pos_x,pos_y = GuildBattleRankedWGData.Instance:GetLingShiPosCfg(gather_id)
	GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y,3)
end

function  GuildBattleRankedView:FlushGuildBossInfo()
	local camp = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo().side
	local guild_bossinfo1_1 = ""
	local guild_bossinfo1_2 = ""
	local guild_bossinfo2_1 = ""
	local guild_bossinfo2_2 = ""

	--主角在蓝色方
	if camp == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.BLUETEAM then
		guild_bossinfo1_1 = string.format(Language.GuildBattleRanked.GuildBatterReward[0],Language.GuildBattleRanked.GuildSideList[0])
		guild_bossinfo1_2 = string.format(Language.GuildBattleRanked.GuildBatterReward[1],Language.GuildBattleRanked.GuildSideList[1])
		guild_bossinfo2_1 = string.format(Language.GuildBattleRanked.GuildBatterReward[2],Language.GuildBattleRanked.GuildSideList[0])
		guild_bossinfo2_2 = string.format(Language.GuildBattleRanked.GuildBatterReward[3],Language.GuildBattleRanked.GuildSideList[1])
	else
		guild_bossinfo1_1 = string.format(Language.GuildBattleRanked.GuildBatterReward[2],Language.GuildBattleRanked.GuildSideList[1])
		guild_bossinfo1_2 = string.format(Language.GuildBattleRanked.GuildBatterReward[3],Language.GuildBattleRanked.GuildSideList[0])
		guild_bossinfo2_1 = string.format(Language.GuildBattleRanked.GuildBatterReward[0],Language.GuildBattleRanked.GuildSideList[1])
		guild_bossinfo2_2 = string.format(Language.GuildBattleRanked.GuildBatterReward[1],Language.GuildBattleRanked.GuildSideList[0])
	end

	local color = COLOR3B.WHITE
	local boss_cfg = GuildBattleRankedWGData.Instance:GetGuildBossInfo()
	local has_nodata = IsEmptyTable(boss_cfg) or boss_cfg[1].boss_id == 0
	if has_nodata then
		self.node_list.guild_top_num1.text.text = ToColorStr(guild_bossinfo1_1, color)
		self.node_list.guild_bom_num1.text.text = ToColorStr(guild_bossinfo1_2, color)
		self.node_list.guild_top_num2.text.text = ToColorStr(guild_bossinfo2_1, color)
		self.node_list.guild_bom_num2.text.text = ToColorStr(guild_bossinfo2_2, color)
		self.node_list.guild_flush_time:SetActive(false)
		return
	end

	local blue_boss_cfg = boss_cfg[1]
	local red_boss_cfg = boss_cfg[2]
	if GuildBattleRankedWGData.Instance:IsTheSameBatterArrayMonster(blue_boss_cfg.boss_id) then
		guild_bossinfo1_1 = ToColorStr(guild_bossinfo1_1, blue_boss_cfg.boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_SYSTEM and COLOR3B.D_GREEN or COLOR3B.WHITE)
		guild_bossinfo1_2 = ToColorStr(guild_bossinfo1_2, blue_boss_cfg.boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_ROLE and COLOR3B.RED or COLOR3B.WHITE)
	else
		guild_bossinfo1_1 = ToColorStr(guild_bossinfo1_1, blue_boss_cfg.boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_ROLE and COLOR3B.D_GREEN or COLOR3B.WHITE)
		guild_bossinfo1_2 = ToColorStr(guild_bossinfo1_2, blue_boss_cfg.boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_SYSTEM and COLOR3B.RED or COLOR3B.WHITE)
	end

	if GuildBattleRankedWGData.Instance:IsTheSameBatterArrayMonster(red_boss_cfg.boss_id) then
		guild_bossinfo2_1 = ToColorStr(guild_bossinfo2_1, red_boss_cfg.boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_SYSTEM and COLOR3B.D_GREEN or COLOR3B.WHITE)
		guild_bossinfo2_2 = ToColorStr(guild_bossinfo2_2, red_boss_cfg.boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_ROLE and COLOR3B.RED or COLOR3B.WHITE)
	else
		guild_bossinfo2_1 = ToColorStr(guild_bossinfo2_1, red_boss_cfg.boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_ROLE and COLOR3B.D_GREEN or COLOR3B.WHITE)
		guild_bossinfo2_2 = ToColorStr(guild_bossinfo2_2, red_boss_cfg.boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_SYSTEM and COLOR3B.RED or COLOR3B.WHITE)
	end

	self.node_list.guild_top_num1.text.text = guild_bossinfo1_1
	self.node_list.guild_bom_num1.text.text = guild_bossinfo1_2
	self.node_list.guild_top_num2.text.text = guild_bossinfo2_1
	self.node_list.guild_bom_num2.text.text = guild_bossinfo2_2

	self:FlushGuildBossPromot(boss_cfg)
end

function GuildBattleRankedView:FlushGuildBossPromot(boss_cfg)
	local active_boss = {}
	local die_boss = {}
	local wait_actiove_boss = {}

	for i = 1, #boss_cfg do
		if boss_cfg[i].boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.ACTIVE then
			table.insert(active_boss,boss_cfg[i])
			break
		elseif boss_cfg[i].boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_ROLE then
			table.insert(die_boss,boss_cfg[i])
		elseif  boss_cfg[i].boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.WAIT_TO_ACTIVE  then
			table.insert(wait_actiove_boss,boss_cfg[i])
		end
	end
	if not IsEmptyTable(active_boss) then
		--直接刷属性显示
		self:FlushGuildBossNavText(active_boss[1])
	elseif not IsEmptyTable(die_boss) or (IsEmptyTable(die_boss) and not IsEmptyTable(wait_actiove_boss)) then
		if not IsEmptyTable(die_boss) then
			self:FlushGuildBossNavText(die_boss[1])
		end
		if not IsEmptyTable(wait_actiove_boss) then
			self:TurnOnTheCountDown(wait_actiove_boss[1])
		end
	else
		self.node_list.guild_flush_time:SetActive(false)
	end
end

function GuildBattleRankedView:GuildBossCountFlush()
end

function GuildBattleRankedView:GuildBossCountDown()
end

function GuildBattleRankedView:TurnOnTheCountDown(wait_actiove_boss_cfg)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local boss_cfg = wait_actiove_boss_cfg
	local boss_flush_time = boss_cfg.boss_flush_time
	local cold_time = boss_flush_time - server_time
	self:GuildBossClearTimeCount()

	self.boss_flushtime = CountDown.Instance:AddCountDown(cold_time, 1,
	function ()
		self:FlushGuildBossNavText(wait_actiove_boss_cfg)
	end,
	function ()
		self:GuildBossCountDown()
	end
)
end

function GuildBattleRankedView:GuildBossClearTimeCount()
	if self.boss_flushtime and CountDown.Instance:HasCountDown(self.boss_flushtime) then
        CountDown.Instance:RemoveCountDown(self.boss_flushtime)
        self.boss_flushtime = nil
    end
end

function GuildBattleRankedView:FlushGuildBossNavText(boss_cfg)
	if IsEmptyTable(boss_cfg)then
		return
	end

	local boss_cfg = boss_cfg
	local boss_state = boss_cfg.boss_state_type
	local camp = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo().side
	local is_friend = GuildBattleRankedWGData.Instance:IsTheSameBatterArrayMonster(boss_cfg.boss_id)
	local boss_side = is_friend and camp or (camp == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.BLUETEAM and GuildBattleRankedWGData.GUILD_BATTlE_CAMP.REDTEAM or GuildBattleRankedWGData.GUILD_BATTlE_CAMP.BLUETEAM)
	local flushtime_text_active = true
	local flushgo_active = false
	if boss_state == GuildBattleRankedWGData.GUILD_BOSS_STATE.ACTIVE then
		flushgo_active =true
		self.node_list.flush_time_text.text.text = Language.GuildBattleRanked.GuildBatterBossShouHu[boss_side]
	elseif boss_state == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_ROLE then
		self.node_list.flush_time_text.text.text = Language.GuildBattleRanked.GuildBatterBossShouHu[boss_side + 2]
	elseif boss_state == GuildBattleRankedWGData.GUILD_BOSS_STATE.DIE_BY_SYSTEM then
		flushtime_text_active = false
	else
		flushgo_active =true
	    local server_time = TimeWGCtrl.Instance:GetServerTime()
		local boss_flush_time = boss_cfg.boss_flush_time
		local cold_time = boss_flush_time - server_time
		if math.floor(cold_time) == 30 then
			local protocol = {
				reason_type = GuildBattleRankedWGData.ChuangWenType.GUILD_BOSS_WAIT_TO_FLUSH,
				param_0 = boss_side,
			}
			GuildBattleRankedWGCtrl.Instance:OnSCCrossGuildBattleNotify(protocol)
		end
		if cold_time <= 30 then
			flushtime_text_active = true
			local time_text = TimeUtil.FormatSecond(cold_time,2)
			self.node_list.flush_time_text.text.text = string.format(Language.GuildBattleRanked.GuildBatterBossShouHu[4],Language.GuildBattleRanked.GuildSideList[boss_side],time_text)
		else
			flushtime_text_active = false
		end
	end
	self.node_list.go_stone_btn:SetActive(flushgo_active)
	self.node_list.guild_flush_time:SetActive(flushtime_text_active)
end

function GuildBattleRankedView:FlushBossActiveTime()
	self.node_list.boss_active_time_info_title.text.text = Language.GuildBattleRanked.GuildBossActiveStateDesc
	--local camp = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo().side   --阵容
	local boss_cfg = GuildBattleRankedWGData.Instance:GetGuildBossInfo()
	local has_nodata = IsEmptyTable(boss_cfg) or boss_cfg[1].boss_id == 0
	if has_nodata then
		self.node_list.boss_active_time_info:SetActive(false)
	else
		local target_boss_cfg = {}
		for i = 1, #boss_cfg do
			if boss_cfg[i].boss_state_type == GuildBattleRankedWGData.GUILD_BOSS_STATE.ACTIVE then
				--有存活boss
				target_boss_cfg = boss_cfg[i]
				break
			end
		end
		if not IsEmptyTable(target_boss_cfg) then
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			local boss_dietime = target_boss_cfg.boss_flush_time + GuildBattleRankedWGData.Instance:GetMonsterInfoByBossId(target_boss_cfg.boss_id)[1].live_time

			--and target_boss_cfg.boss_flush_time <= server_time
			if boss_dietime >= server_time then
				--处理初始化显示 计时器显示数据不正常 闪一下
				local time_remaining =  boss_dietime - server_time
				self.node_list.boss_active_time.text.text = TimeUtil.FormatSecond(time_remaining, 2)
				self.node_list.boss_active_time_info:SetActive(true)

				local time_ddifference = boss_dietime - server_time
				self:GuildBossClearDieTimeCount()
				self.boss_activetime = CountDown.Instance:AddCountDown(time_ddifference, 1,
				function (elapse_time, total_time)
					-- local server_time = TimeWGCtrl.Instance:GetServerTime()
					-- local time_remaining =  boss_dietime - server_time
					local time_remaining = total_time - elapse_time
					if time_remaining > 0 and self.node_list.boss_active_time then
						self.node_list.boss_active_time.text.text = TimeUtil.FormatSecond(time_remaining, 2)
					end
				end,
				function ()
					self.node_list.boss_active_time_info:SetActive(false)
				end)
			end
		else
			self.node_list.boss_active_time_info:SetActive(false)
		end
	end
end

function GuildBattleRankedView:GuildBossClearDieTimeCount()
	if self.boss_activetime and CountDown.Instance:HasCountDown(self.boss_activetime) then
        CountDown.Instance:RemoveCountDown(self.boss_activetime)
        self.boss_activetime = nil
    end
end

----------------------------------GuildRankItemNewRender-----------------------------------------------
GuildRankItemNewRender = GuildRankItemNewRender or BaseClass(BaseRender)
function GuildRankItemNewRender:__init()
end

function GuildRankItemNewRender:__delete()

end

function GuildRankItemNewRender:OnFlush()
    if not self.data then
        return
    end
    local show_rank = self.index > 3
    self.node_list.rank_num:SetActive(show_rank)
    self.node_list.rank_img:SetActive(not show_rank)
    if self.index <= 3 then
	    self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..self.index))
    	self.node_list.rank_img.image:SetNativeSize()
    end
    self.node_list.rank_num.text.text = self.index
	local name = self.data.user_name
	local name_list = Split(self.data.user_name, "_")
	if not IsEmptyTable(name_list) then
		name = name_list[1]
	end

	local is_me = self.data.user_id == RoleWGData.Instance:GetUUid()
	
    self.node_list.rank_name.text.text = is_me and ToColorStr(name, COLOR3B.DEFAULT_NUM) or name
    self.node_list.rank_exp.text.text = is_me and ToColorStr(self.data.total_score, COLOR3B.DEFAULT_NUM) or self.data.total_score
    local max_info = GuildBattleRankedWGData.Instance:GetMaxBattleRankList()
	local max_score = max_info.total_score > 0 and max_info.total_score or 1
	local slider = tonumber(self.data.total_score) / tonumber(max_score)
	--self.node_list["rank_slider"].slider.value = slider
end
