--求婚
MarryProposeView = MarryProposeView or BaseClass(SafeBaseView)

function MarryProposeView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false)
	self:LoadConfig()
	self.alert_window = nil
	self.lover_id = 0
	self.marry_type = -1
end

function MarryProposeView:__delete()

end

function MarryProposeView:ReleaseCallBack()
	if self.marry_item_slot then
		for k, v in pairs(self.marry_item_slot) do
			v:DeleteMe()
		end
		self.marry_item_slot = nil
	end
	if nil ~= self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	if self.my_head_cell then
		self.my_head_cell:DeleteMe()
		self.my_head_cell = nil
	end

	if self.ta_head_cell then
		self.ta_head_cell:DeleteMe()
		self.ta_head_cell = nil
	end

	self.cur_select_ring = nil
	self.lover_id = 0
	self.marry_type = -1
	self.str = nil
end

function MarryProposeView:LoadConfig()
	self.is_modal = true
    self.view_name = "MarryProposeView"
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_qiuhun")
end

function MarryProposeView:LoadCallBack()
	self.node_list["btn_marry_qiuhun"].button:AddClickListener(BindTool.Bind1(self.QiuhunHandler, self))

end

function MarryProposeView:SetItemShow(item_data)
	if nil == self.marry_item_slot then
		self.marry_item_slot = {}
	end
	local item_num = #item_data
	local item_alearday_num = #self.marry_item_slot
	for i=0,item_num do
		if nil == self.marry_item_slot[i] then
			self.marry_item_slot[i] = ItemCell.New(self.node_list["ph_item"])
		end
		self.marry_item_slot[i]:SetData(item_data[i])
	end
	for i=0,item_alearday_num do
		if nil ~= self.marry_item_slot[i] then
			self.marry_item_slot[i]:SetActive(i <= item_num)
		end
	end
end

function MarryProposeView:SetData(lover_id, str, marry_type)
	if nil == lover_id then return end
	self.lover_id = lover_id 
	self.str = str or ""
	self.marry_type = marry_type
end

function MarryProposeView:SetRoleHeadImg()
	--自己的头像数据
	if not self.my_head_cell then
		self.my_head_cell = BaseHeadCell.New(self.node_list["my_head"])
	end
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	data.is_show_main = true
	self.my_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.my_head_cell:SetBgActive(false)
	self.my_head_cell:SetData(data)
	--自己的名字
	local role_name = GameVoManager.Instance:GetMainRoleVo().name
	self.node_list["my_name_text"].text.text = role_name

	--对象的头像数据
	if not self.ta_head_cell then
		self.ta_head_cell = BaseHeadCell.New(self.node_list["ta_head"])
    end
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local role_base_info_ack = MarryWGData.Instance:GetLoverInfo2()
    if role_base_info_ack and lover_id > 0 then
		-- local role_vo = role_base_info_ack
		-- local appearance = role_vo and role_vo.appearance
		-- local data = {fashion_photoframe = appearance.fashion_photoframe}
		-- data.role_id = role_vo.role_id
		-- data.prof = role_vo.prof
		-- data.sex = role_vo.sex
		-- self.ta_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
		-- self.ta_head_cell:SetData(data)
		self:GetRoleInfoCallBack(role_base_info_ack)
	elseif self.lover_id and self.lover_id > 0 then
		BrowseWGCtrl.Instance:BrowRoelInfo(self.lover_id, BindTool.Bind1(self.GetRoleInfoCallBack, self))
	end
	self.node_list["ta_name_text"].text.text = self.str
end

function MarryProposeView:GetRoleInfoCallBack(protocol)
	if not self.ta_head_cell then
		self.ta_head_cell = BaseHeadCell.New(self.node_list["ta_head"])
    end
    
	local appearance = protocol.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = protocol.role_id
	data.prof = protocol.prof
	data.sex = protocol.sex
	self.ta_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.ta_head_cell:SetData(data)
	self.ta_head_cell:SetBgActive(false)
	--self.ta_head_cell:SetHeadCellScale(0.6)
end

function MarryProposeView:ShowIndexCallBack()
	--设置头像
	self:SetRoleHeadImg()
	self.node_list["rich_tips"].text.text = string.format(Language.Marry.QiuHun, self.str, self.str)
	local marry_cfg = MarryWGData.Instance:GetOneMarryCfgByType(self.marry_type)
	local is_aleardy_tiqin = MarryWGData.Instance:GetCurRoleIsTiQin(self.marry_type)

	local reward_data_1 = {}
	if is_aleardy_tiqin then
		reward_data_1 = marry_cfg.after_reward_item
	else
		reward_data_1 = marry_cfg.reward_item
	end

	local marry_get_id = MarryWGData.Instance:GetMarryRewardLimitInfo()
	local reward_data = MarryWGData.Instance:ExculeRewardInfo(reward_data_1, marry_get_id, false)

	if reward_data then
		self:SetItemShow(reward_data)
	end
end

function MarryProposeView:QiuhunHandler()
	if MarryWGCtrl.Instance:OnClickQiuHunHandler() then
		return
	end
	
	MarryWGCtrl.Instance:SendMarryReq(MARRY_REQ_TYPE.MARRY_REQ_TYPE_PROPOSE, self.marry_type, self.lover_id)				
	self:Close()
end

function MarryProposeView:SendMarryReq(param_t)
	if param_t.gold_bind > 0 then
		if not RoleWGData.Instance:GetIsEnoughBindGold(param_t.gold_bind) then
			GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, COMMON_CONSTS.VIRTUAL_ITEM_BINDGOL)
			return
		end
	else
		if not RoleWGData.Instance:GetIsEnoughUseGold(param_t.gold) then
			UiInstanceMgr.Instance:ShowChongZhiView()
			return
		end
	end
	MarryWGCtrl.Instance:SendMarryReq(MARRY_REQ_TYPE.MARRY_REQ_TYPE_PROPOSE, param_t.marry_type, self.lover_id)
end