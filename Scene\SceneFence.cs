﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using Nirvana;
using UnityEngine;

#if UNITY_EDITOR

/// <summary>
/// The fence of this scene.
/// </summary>
[ExecuteInEditMode]
public sealed class SceneFence : SceneObject
{
    [SerializeField]
    [Tooltip("The ID of this fence.")]
    private int fenceID;

    [SerializeField]
    [Tooltip("The offset of the door view.")]
    private Vector3 offset;

    [Ser<PERSON><PERSON><PERSON><PERSON>]
    [Tooltip("The rotation of the door view.")]
    private Vector3 rotation;

    /// <summary>
    /// Gets the fence ID.
    /// </summary>
    public int ID
    {
        get { return this.fenceID; }
    }

    /// <summary>
    /// Gets the door offset.
    /// </summary>
    public Vector3 Offset
    {
        get { return this.offset; }
    }

    /// <summary>
    /// Gets the door rotation.
    /// </summary>
    public Vector3 Rotation
    {
        get { return this.rotation; }
    }

    private void Awake()
    {
        this.LoadPreview();
    }

    private void OnValidate()
    {
        if (Application.isPlaying)
        {
            return;
        }

        this.name = "Fence" + this.fenceID.ToString();
        this.LoadPreview();
    }

    private void LoadPreview()
    {
        var previewPrefab = AssetManager.LoadObjectLocal(
            "effects/prefabs", "men_dcq01", typeof(GameObject)) as GameObject;
        if (previewPrefab)
        {
            var preview = GameObject.Instantiate(previewPrefab);
            preview.transform.localPosition = this.offset;
            var previewObj = this.GetOrAddComponent<PreviewObject>();
            previewObj.SetPreview(preview);
        }
    }
}

#endif
