--铭纹寻宝记录面板
LingMaiRecord = LingMaiRecord or BaseClass(SafeBaseView)

function LingMaiRecord:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "LingMaiRecord"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 10), sizeDelta = Vector2(806, 466)})
    self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_lingmai_record")
    self:SetMaskBg(true, true)
end

function LingMaiRecord:ReleaseCallBack()
    if self.flush_msg_list then
		self.flush_msg_list:DeleteMe()
		self.flush_msg_list = nil
	end
end

function LingMaiRecord:LoadCallBack()
    self.flush_msg_list = AsyncListView.New(FlushMsgItem,self.node_list["flush_msg_list"])
	self.flush_msg_list:SetCellSizeDel(BindTool.Bind(self.CellSizeDel, self))
end

function LingMaiRecord:OnFlush()
    local record_info_list = GuildBaoXiangWGData.Instance:GetDailyTreasureRecordInfoList()
	self.flush_msg_list:SetDataList(record_info_list)
	self.node_list["not_flush_msg_tip"]:SetActive(IsEmptyTable(record_info_list))
end

function LingMaiRecord:CellSizeDel(data_index)
	local hight = 40
	local spece = 18
	data_index = data_index + 1
	local list = self.flush_msg_list:GetDataList()
	local data = list[data_index]
	if not data then 
		return hight
	end
	local time_str = ""
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(data.new_quality)
	local msg_str = ""
	if data.is_refuse == 1 then
		msg_str = string.format(Language.BiZuoBaoXiang.RoleRefuseMsg,data.name)
	else
		if data.new_quality == data.old_quality then
			msg_str = string.format(Language.BiZuoBaoXiang.FlushRecordStrNotChange,time_str,data.name)
		else
			local baoxiang_name = treasure_cfg and treasure_cfg.name or ""
			local color = treasure_cfg and treasure_cfg.common_color or 2
			baoxiang_name = ToColorStr(baoxiang_name,ITEM_COLOR[color])
			msg_str = string.format(Language.BiZuoBaoXiang.FlushRecordStr,time_str,data.name,baoxiang_name)
		end
	end
    self.node_list.TestText.text.text = msg_str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)
	hight = math.ceil(self.node_list["TestText"].rect.rect.height) <= 40 and 40 or math.ceil(self.node_list["TestText"].rect.rect.height) + spece
	return hight
end


