ServerActZhenLongSongLiView = ServerActZhenLongSongLiView or BaseClass(SafeBaseView)

function ServerActZhenLongSongLiView:__init(act_id,zodaer)
	self.act_id = act_id
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_zhenlong_songli")
	self.order = 3
end

function ServerActZhenLongSongLiView:__delete()

end

function ServerActZhenLongSongLiView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	self.other_cfg = nil

	if self.count_down and CountDownManager.Instance:HasCountDown(self.count_down) then
		CountDownManager.Instance:RemoveCountDown(self.count_down)
	end
end

function ServerActZhenLongSongLiView:LoadCallBack()
	self.other_cfg = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").other
	self:InitView()
	self:TimeCountDown()
end

function ServerActZhenLongSongLiView:InitView()
	if self.other_cfg ~= nil then
		if not self.reward_list then
			self.reward_list = AsyncListView.New(ZhenLongItemRender, self.node_list["reward_list"])
		end

		local list = {}
		local state_list = string.split(self.other_cfg[1].rare_sign, ",")
		for i = 0, #self.other_cfg[1].reward_show do
			local item = {}
			if state_list[i+1] ~= nil then
				item.rare_state = tonumber(state_list[i+1])
			else
				item.rare_state = 0
			end
			item.reward = self.other_cfg[1].reward_show[i]
			table.insert(list, item)
		end
		self.reward_list:SetDataList(list)
	end

	XUI.AddClickEventListener(self.node_list.btn_xunbao, BindTool.Bind(self.OnBtnXunBaoClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnBtnTipClickHnadler,self))
end

function ServerActZhenLongSongLiView:ShowIndexCallBack()
	if self.node_list.xuanbao_tween_img then
		UITween.DoUpDownCrashTween(self.node_list.xuanbao_tween_img)
	end
end


function ServerActZhenLongSongLiView:OnBtnXunBaoClickHnadler()
	if self.other_cfg ~= nil then
		if FunOpen.Instance:GetFunIsOpenedByMouduleName(self.other_cfg[1].open_panel) then
			local param = string.split(self.other_cfg[1].open_panel,"#")  
			FunOpen.Instance:OpenViewByName(param[1], param[2])
			ServerActivityWGCtrl.Instance:CloseOpenServer2View()
		else
			self.fun_list = ConfigManager.Instance:GetAutoConfig("funopen_auto").funopen_list
			local cfg = self.fun_list[self.other_cfg[1].open_panel]
			if cfg ~= nil then
				FunOpen.Instance:GetFunUnOpenCommonTip(cfg)
			end
		end
	end
end

function ServerActZhenLongSongLiView:OnBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		if self.other_cfg ~= nil then
			role_tip:SetTitle(Language.ServerActZhenLong.title)
			role_tip:SetContent(self.other_cfg[1].zhenglong_desc)
		end
	end
end

--有效时间倒计时
function ServerActZhenLongSongLiView:TimeCountDown()
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACT_OPEN_SERVER_ZHENLONG)
	if activity_data ~= nil then
		self.count_down = "count_down"
		local invalid_time = activity_data.end_time
		if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
			self.node_list.time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
			CountDownManager.Instance:AddCountDown(self.count_down, BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
		end
	end
end

function ServerActZhenLongSongLiView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list.time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function ServerActZhenLongSongLiView:OnComplete()
	self.node_list.time_label.text.text = Language.ServerActZhenLong.ActivityStr1
end

-----------------------------------------------------------------------

ZhenLongItemRender = ZhenLongItemRender or BaseClass(BaseRender)
function ZhenLongItemRender:__init()
	
end

function ZhenLongItemRender:LoadCallBack()

end

function ZhenLongItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function ZhenLongItemRender:OnFlush()
	if not self.data then
		return
	end 

	self.node_list.rare_icon:SetActive(self.data.rare_state > 0)
	self.cell = ItemCell.New(self.node_list["cell"])
	self.cell:SetData(self.data.reward)
end