TipSystemScrollView = TipSystemScrollView or BaseClass(SafeBaseView)

local SPEED = 100						-- 字幕滚动的速度(像素/秒)

function TipSystemScrollView:__init()
	self.view_layer = UiLayer.PopTop
	self.calc_active_close_ui_volume = false
	self:AddViewResource(0, "uis/view/miscpre_load_prefab", "SystemNoticeScrollView")
	self.view_name = "TipSystemScrollView"
	self.is_open = false
	self.str_list = {}
	self.can_do_fade = false
	self.calculate_time_quest = nil
end

function TipSystemScrollView:__delete()
	if nil ~= self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
		self.calculate_time_quest = nil
	end

	self.is_open = false
end

function TipSystemScrollView:LoadCallBack()
	self.text_trans = self.node_list["EmojiText"].rect
	self.mask_width = self.node_list["Mask"].rect.sizeDelta.x
	self.is_open = true
end

function TipSystemScrollView:ReleaseCallBack()
	-- 清理变量和对象
	self.text_trans = nil
	self.tweener = nil
end

function TipSystemScrollView:OnShieldHearsay(value)
	if value then
		self:Close()
	end
end

function TipSystemScrollView:OpenCallBack()
	if nil == self.shield_hearsay then
		self.shield_hearsay = GlobalEventSystem:Bind(SettingEventType.CLOSE_HEARSAY, BindTool.Bind1(self.OnShieldHearsay, self))
	end

	self.tweener = nil
end

function TipSystemScrollView:CloseCallBack()
	if self.shield_hearsay then
		GlobalEventSystem:UnBind(self.shield_hearsay)
		self.shield_hearsay = nil
	end

	if self.tweener then
		self.tweener:Pause()
	end

end

function TipSystemScrollView:SetNotice(str)
	local shield_hearsay = SettingWGData.Instance:GetSettingData(SETTING_TYPE.CLOSE_HEARSAY)
	if shield_hearsay then
		return
	end

	if not self.is_open then
		self:Open()
		self.str_list = {}
		self:AddNotice(str)
		self:Flush()
	else
		self:AddNotice(str)
	end
end

function TipSystemScrollView:AddNotice(str)
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	-- 如果时间超过3分钟的会被清除
	for i = #self.str_list, 1, -1 do
		if self.str_list[i].time < cur_time - 180 then
			table.remove(self.str_list, i)
		end
	end

	table.insert(self.str_list, {str = str, time = cur_time})
end

function TipSystemScrollView:OnFlush()
	if (#self.str_list > 0) then
		local str_data = table.remove(self.str_list, 1)
		EmojiTextUtil.ParseRichText(self.node_list.EmojiText.emoji_text, str_data.str, 20)
		self.text_trans.anchoredPosition = Vector2(0, 0)

		self.calculate_time_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.Calculate, self, str_data), 0.1)
	end
end

-- 计算滚动的时间的位置
function TipSystemScrollView:Calculate(str_data)
	if self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
	end
	self.calculate_time_quest = nil

	if nil == self.text_trans or nil == str_data then
		return
	end

	EmojiTextUtil.ParseRichText(self.node_list.EmojiText.emoji_text, str_data.str, 20)
	local width = self.text_trans.sizeDelta.x
	width = width + self.mask_width
	local duration = width / SPEED

	local tweener = self.text_trans:DOAnchorPosX(-width, duration, false)
	self.tweener = tweener
	tweener:SetEase(DG.Tweening.Ease.Linear)
	tweener:OnComplete(BindTool.Bind(self.OnMoveEnd, self))
end

function TipSystemScrollView:OnMoveEnd()
	if #self.str_list <= 0 then
		self:Close()
	else
		self:Flush()
	end
end
