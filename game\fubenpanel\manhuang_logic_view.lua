ManHuangLogicView = ManHuangLogicView or BaseClass(SafeBaseView)
function ManHuangLogicView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_first_pass_box")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_manhuang_info")
end

function ManHuangLogicView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end
	self.is_out_fb = nil
	if self.manhuang_reward_list then
        self.manhuang_reward_list:DeleteMe()
        self.manhuang_reward_list = nil
    end
end

function ManHuangLogicView:LoadCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
end

function ManHuangLogicView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	self.obj = self.node_list["layout_manhuang_info_root"].gameObject
	self.obj.transform:SetParent(parent.gameObject.transform)
	self.obj.transform.localPosition = Vector3(0, 0, 0)
	self.obj.transform.localScale = Vector3.one
	mainui_ctrl:SetTaskPanel(false)
	-- mainui_ctrl:ChangeTaskBtnName(Scene.Instance:GetSceneName())
	mainui_ctrl:SetAutoGuaJi(true)
	mainui_ctrl:AddBtnToFbIconGroup2Line(self.node_list.btn_first_pass_box)
	if self.is_out_fb then
        self.obj:SetActive(false)
    end
    self.is_out_fb = nil
	self.manhuang_reward_list = AsyncListView.New(ItemCell, self.node_list["manhuang_reward_list"])
	XUI.AddClickEventListener(self.node_list.btn_first_pass_box, BindTool.Bind(self.OnClickManHuangFirstPass, self)) --点击宝箱打开 首通奖励界面
end

function ManHuangLogicView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function ManHuangLogicView:CloseCallBack()
	self.node_list.btn_first_pass_box.transform:SetParent(self.root_node_transform,false)
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end

	-- MainuiWGCtrl.Instance:SetTaskAndTeamCallBack(nil, nil)
	-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.task_text2)
end

function ManHuangLogicView:ShowIndexCallBack()
	self:Flush()
end

function ManHuangLogicView:OnFlush()
	local base_info = ManHuangGuDianWGData.Instance:GetManHuangBaseInfo()
	if base_info then
		--红点
		local is_show_red_point = ManHuangGuDianWGData.Instance:GetIsCanFetch()
		self.node_list.manhuang_red_point:SetActive(is_show_red_point)
		self:ShowManHuangAni(is_show_red_point)
		--2019/12/10屏蔽
		--self.node_list.lbl_tip_right.text.text = string.format(Language.FuBenPanel.ManHuangPassWaveTip, base_info.manhuang_max_wave)
	end
	local scene_info = ManHuangGuDianWGData.Instance:GetSceneInfo()
	if not scene_info then return end
	local data_list = ManHuangGuDianWGData.Instance:GetSceneRewardDataList()
	if data_list[scene_info.curr_wave_index] then
		local item_list = data_list[scene_info.curr_wave_index].item_list
		self.manhuang_reward_list:SetDataList(item_list, 3)
	end

	local cfg = ManHuangGuDianWGData.Instance:GetCfgByWave(scene_info.curr_wave_index)
	if cfg == nil then
		--print_error("刷新蛮荒古殿界面的时候，配置获取不到, 波数：", scene_info.curr_wave_index)
		return
	end

	self.node_list.lbl_boshu.text.text = string.format(Language.FuBenPanel.ManHuangWaveSceneInfoStr2, scene_info.curr_wave_index)
	self.node_list.lbl_desc.text.text = string.format(Language.FuBenPanel.ManHuangWaveSceneInfoStr3,scene_info.cur_wave_monster_num - scene_info.kill_monster_num)
end

function ManHuangLogicView:ShowManHuangAni(is_ani)
	if self.node_list["manhuang_box_icon"] then
		self.node_list["manhuang_box_icon"].animator:SetBool("is_shake", is_ani)
	end
end

function ManHuangLogicView:OnClickManHuangFirstPass()
    ViewManager.Instance:Open(GuideModuleName.ManHuangGuDianFirstPassView)
end
