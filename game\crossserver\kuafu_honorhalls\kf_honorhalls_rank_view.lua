function HonorhallsView:ReleaseRankView()
    if nil ~= self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end
end

function HonorhallsView:LoadRankView()
    self.rank_list = AsyncListView.New(KFHonorhallRankItemRender, self.node_list["rank_list"])
end

function HonorhallsView:FlushRankView()
    local data_list = KuafuHonorhallWGData.Instance:GetThereRankInfo()
    self.rank_list:SetDataList(data_list)

    local my_info = KuafuHonorhallWGData.Instance:GetRoleInfo()
    local rank_num = KuafuHonorhallWGData.Instance:GetRankNum()
    self.node_list["my_rank"].text.text = rank_num > 0 and string.format(Language.Honorhalls.MyRank, rank_num) or Language.Honorhalls.NoRank
    local cur_layer = my_info and my_info.cur_layer + 1 or 0
    cur_layer = cur_layer >= 10 and 10 or cur_layer
    self.node_list["my_pass_str"].text.text = cur_layer > 0 and string.format(Language.Honorhalls.Layer,cur_layer) or ""
end

----------------------------KFHonorhallRankItemRender---------------------------

KFHonorhallRankItemRender = KFHonorhallRankItemRender or BaseClass(BaseRender)
function KFHonorhallRankItemRender:OnFlush()
    if not self.data then
        return
    end

    local flag = self.index <= 3
    self.node_list.rank_img:SetActive(flag)
    self.node_list.rank_text:SetActive(not flag)
    if flag then
        local b,a = ResPath.GetCommonIcon("a3_tb_jp" .. self.index)
		self.node_list.rank_img.image:LoadSprite(b, a, function()
            XUI.ImageSetNativeSize(self.node_list.rank_img)
		end)
    else
        self.node_list.rank_text.text.text = self.index
    end
    local my_uuid = RoleWGData.Instance:GetUUid()
    local name_color = COLOR3B.WHITE
    if my_uuid == self.data.uuid then
        name_color = COLOR3B.DEFAULT_NUM
    end

    self.node_list.role_name.text.text = ToColorStr(self.data.user_name, name_color)
    local cur_layer = self.data.max_layer + 1
    cur_layer = cur_layer >= 10 and 10 or cur_layer
    if cur_layer > 0 then
        self.node_list["pass_str"].text.text = string.format(Language.Honorhalls.Layer,cur_layer)
    else
        self.node_list["pass_str"].text.text = ""
    end
end