GuildWarInviteView = GuildWarInviteView or BaseClass(SafeBaseView)

function GuildWarInviteView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_war_invite")
end

function GuildWarInviteView:__delete()
end

function GuildWarInviteView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	if self.dingji_reward_list then
		self.dingji_reward_list:DeleteMe()
		self.dingji_reward_list = nil
	end

	self:CancelTween()
end

function GuildWarInviteView:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])
	self.dingji_reward_list = AsyncListView.New(ItemCell,self.node_list["dingji_reward_list"])
	XUI.AddClickEventListener(self.node_list["enter_btn"], BindTool.Bind(self.ClickEnterBtn,self))
	XUI.AddClickEventListener(self.node_list["btn_close"],BindTool.Bind(self.OnClickCloseWindow,self))
	local other_cfg = GuildInviteWGData.Instance:GetOtherCfg()
	self.node_list["content_text"].text.text = other_cfg.invite_content
end

function GuildWarInviteView:ShowIndexCallBack()
	self:PlayAnim()
end

function GuildWarInviteView:OnFlush()
	local data = GuildInviteWGData.Instance:GetDefaultReward()
	local temple_reward = GuildInviteWGData.Instance:GetTempleReward()
	self.reward_list:SetDataList(temple_reward)
	self.dingji_reward_list:SetDataList(data)
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ACTIVITY_TYPE_GUILD_INVITE)
	if is_open then
		self.node_list["enter_btn_text"].text.text = Language.Common.EnterTiaoZhang
	else
		local hour,min = GuildInviteWGData.Instance:GetGuildInviteOpenTime()
		self.node_list["enter_btn_text"].text.text = string.format(Language.Guild.GuildInviteOpen,hour,min)
	end
end

function GuildWarInviteView:ClickEnterBtn()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	if guild_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.HaveNotXianMeng)
		GuildWGCtrl.Instance:Open(TabIndex.guild_info)
		return
	end
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ACTIVITY_TYPE_GUILD_INVITE)
	if is_open then
		GuildInviteWGCtrl.Instance:SendCSGuildDingJiEnterScene()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
	end
end

function GuildWarInviteView:OnClickCloseWindow()
	self:Close()
end

function GuildWarInviteView:PlayAnim()
	local zhen_obj = self.node_list["feizhen"]
	local bg_obj = self.node_list["bg_obj"]
	local content = self.node_list["content"]
	local canvas_group = content.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 0
	zhen_obj.transform.localScale = Vector3(4,4,4)
	self:CancelTween()
	local tween = DG.Tweening.DOTween.Sequence()
	bg_obj.transform.anchoredPosition = Vector2(-1090,0)
	zhen_obj.transform.anchoredPosition = Vector2(429,625)
	local tween_1 = bg_obj.transform:DOAnchorPos(Vector2(-99, 0), 0.4)
	local tween_2 = zhen_obj.transform:DOAnchorPos(Vector2(-5, 250), 0.2)
	local tween_3 = zhen_obj.transform:DOScale(1, 0.2)
	local tween_4 = canvas_group:DoAlpha(0, 1, 0.2)
	tween:Append(tween_1)
	tween:Append(tween_2)
	tween:Join(tween_3)
	tween:Append(tween_4)

	self.enter_play_tween = tween 
end

function GuildWarInviteView:CancelTween()
    if self.enter_play_tween then
        self.enter_play_tween:Kill()
        self.enter_play_tween = nil
    end
end