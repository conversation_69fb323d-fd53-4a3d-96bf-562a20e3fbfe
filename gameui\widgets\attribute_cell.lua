AttributeCell = AttributeCell or BaseClass(BaseRender)

AttributeCell.Text_Type = {
	Text_Type1 = 1, 
	Text_Type2 = 2, 
	Text_Type3 = 3, 
}

function AttributeCell:__init(instance)
end

function AttributeCell:__delete()
	self.text_type = nil
end

function AttributeCell:Reset(is_delete)
	self.node_list["text_name"].text.text = ""
	self.node_list["text_curr"].text.text = ""
	self.node_list["text_add"].text.text = ""
	self.node_list["img_add"]:SetActive(false)
	self.node_list["text_add"]:SetActive(false)
end

function AttributeCell:SetType(type)

end

function AttributeCell:SetTextType(text_type)
	self.text_type = text_type
end

function AttributeCell:SetImgAdd(url)
	self.iamge_add_url = url or "arrow_up"
end

function AttributeCell:LoadCallBack()
	self.text_type = nil
	if nil ~= self.iamge_add_url then
		local asset_name, bundle_name = ResPath.GetCommonOthers(self.iamge_add_url)
		self.node_list["img_add"].image:LoadSprite(asset_name,bundle_name,function ()
			self.node_list["img_add"].image:SetNativeSize()
		end)
	end

	if self.ui_type then
		self:LayoutUI(self.ui_type)
	end
end

function AttributeCell:OnFlush()
	-- if not self.data then return end
	if self.data.attr_name then
		if not self.text_type then
			self.node_list["text_name"].text.text = self.data.attr_name
		elseif self.text_type == 1 then
			self.node_list["text_name"].text.text = Language.Common.AttrNameList2[self.data.attr_type]..":"
		else
			self.node_list["text_name"].text.text = Language.Common.AttrName[self.data.attr_type]
		end
	end
	self.node_list["text_curr"].text.text = self.data.attr_value

	local flag = self.data.attr_add and 0 ~= self.data.attr_add
	self.node_list["img_add"]:SetActive(flag)
	self.node_list["text_add"]:SetActive(flag)
	if flag then
		self.node_list["text_add"].text.text = "+" .. self.data.attr_add
	end
end

-- 写死的位置信息
function AttributeCell:LayoutUI(ui_type)
	if nil == self.node_list then
		self.ui_type = ui_type
		return
	end

	local len = string.len(self.node_list["text_name"].text.text)
	if ui_type == AttributeCell_UIType.TianShenUpgrade then
		if len <= 15 then
			self.node_list["text_name"].transform.sizeDelta = Vector2(110, 25)
			self.node_list["text_name"].transform.anchoredPosition = Vector2(90,0)
			self.node_list["text_curr"].transform.anchoredPosition = Vector2(162.5,0)
			self.node_list["img_add"].transform.anchoredPosition = Vector2(53.5,3)
			self.node_list["text_add"].transform.anchoredPosition = Vector2(-8,3)
		else
			self.node_list["text_name"].transform.sizeDelta = Vector2(110, 25)
			self.node_list["text_name"].transform.anchoredPosition = Vector2(90,0)
			self.node_list["text_curr"].transform.anchoredPosition = Vector2(162.5,0)
			self.node_list["img_add"].transform.anchoredPosition = Vector2(124.3,3)
			self.node_list["text_add"].transform.anchoredPosition = Vector2(-8,3)
		end
	else
		if len <= 15 then
			self.node_list["text_name"].transform.sizeDelta = Vector2(110, 25)
			self.node_list["text_name"].transform.anchoredPosition = Vector2(54.5,0)
			self.node_list["text_curr"].transform.anchoredPosition = Vector2(132.5,0)
			self.node_list["img_add"].transform.anchoredPosition = Vector2(47.3,0)
			self.node_list["text_add"].transform.anchoredPosition = Vector2(-30.5,0)
		else
			self.node_list["text_name"].transform.sizeDelta = Vector2(150, 25)
			self.node_list["text_name"].transform.anchoredPosition = Vector2(74.5,0)
			self.node_list["text_curr"].transform.anchoredPosition = Vector2(172.5,0)
			self.node_list["img_add"].transform.anchoredPosition = Vector2(70,0)
			self.node_list["text_add"].transform.anchoredPosition = Vector2(-24.5,0)
		end
	end
end