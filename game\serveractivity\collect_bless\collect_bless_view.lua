CollectBlessView = CollectBlessView or BaseClass(SafeBaseView)

function CollectBlessView:__init()
	self:SetMaskBg(false,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/collect_bless_ui_prefab", "layout_collect_bless")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
	self.reward_cell = {}
	self.card_items = {}
	self.card_datas = {}
	self.select_index = 0
end

function CollectBlessView:__delete()
end

function CollectBlessView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("collect_bless_time") then
		CountDownManager.Instance:RemoveCountDown("collect_bless_time")
	end
	if self.reward_cell then
		for k,v in pairs(self.reward_cell) do
			v:DeleteMe()
		end
	end
	self.reward_cell = {}

	self.is_open_task = true

	if nil ~= self.left_list_view then
		self.left_list_view:DeleteMe()
		self.left_list_view = nil
	end

	if nil ~= self.right_list_view then
		self.right_list_view:DeleteMe()
		self.right_list_view = nil
	end

	if self.draw_alert ~= nil then
		self.draw_alert:DeleteMe()
		self.draw_alert = nil
	end

	for i = 0, 9 do
		if self.card_items[i] ~= nil then
			self.card_items[i]:DeleteMe()
		end
		self.card_items[i] = nil
	end
end

function CollectBlessView:LoadCallBack()
	-- 加载标题
	local bundle, asset = ResPath.GetActivityTitle("CollectBless")
	self.node_list["img_title"].image:LoadSprite(bundle, asset, function()
		XUI.ImageSetNativeSize(self.node_list["img_title"])
	end)
    ---  左边列表
	self.left_list_view = PerfectLoversListView.New(CollectBlessLeftRender,self.node_list.ph_left_list)
	self.left_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectCellItemCallBack, self))

    ---  右边列表
	for i = 1, 10 do
		self.card_datas[i - 1] = {
			index = i - 1,
		}
	end
	self.right_list_view = AsyncBaseGrid.New()
	self.right_list_view:CreateCells({cell_count = 10,col = 5,itemRender = CollectBlessRightRender,assetBundle = self.ui_config[1],assetName = 'ph_item',list_view = self.node_list.ph_right_list})
	self.right_list_view:SetSelectCallBack(BindTool.Bind1(self.OnClickCardHandler, self))
	self.right_list_view:SetDataList(self.card_datas,3)
	self.draw_alert = Alert.New(nil, nil, nil, nil, true)	
	self.draw_alert:SetOkFunc(BindTool.Bind1(self.SendRollCard, self))

    -- 有机会获得的奖励
	for i = 1, 3 do
		local ph = self.node_list["ph_item_" .. i]
		local cell = ItemCell.New(ph)
		self.reward_cell[i] = cell
	end
	XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind1(self.OnClickBtnATips, self))
end

function CollectBlessView:OpenCallBack()

end

function CollectBlessView:ShowIndexCallBack()
	if self.right_list_view then
		self.right_list_view:SetDataList(self.card_datas,3)
	end
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS,
		opera_type = RA_COLLECT_BLESSING_OPEAR_TYPE.RA_COLLECT_BLESSING_OPERA_TYPE_QUERY_INFO,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)

	if CountDownManager.Instance:HasCountDown("collect_bless_time") then
		CountDownManager.Instance:RemoveCountDown("collect_bless_time")
	end
	-- 获取当前活动状态
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS) or {}
	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		local next_time = act_cornucopia_info.next_time or 0
		self:UpdataRollerTime(TimeWGCtrl.Instance:GetServerTime(),next_time)
		CountDownManager.Instance:AddCountDown("collect_bless_time", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), next_time, nil, 1)
	else
		self:CompleteRollerTime()
	end

end

function CollectBlessView:UpdataRollerTime(elapse_time, next_time)
	local time = next_time - elapse_time
	if self.node_list.lbl_time ~= nil then
		if time > 0 then
			local format_time = TimeUtil.Format2TableDHM(time)
			local str_list = Language.Common.TimeList
			local time_str = ""
			if format_time.day > 0 then
				time_str = format_time.day .. str_list.d
			end
			if format_time.hour > 0 then
				time_str = time_str .. format_time.hour .. str_list.h
			end
			time_str = time_str .. format_time.min .. str_list.min

			self.node_list.lbl_time.text.text = (time_str)
		end
	end
end

function CollectBlessView:CompleteRollerTime()
	if self.node_list.lbl_time ~= nil then
		self.node_list.lbl_time.text.text = ("0")
	end
end

-- 点击左边列表
function CollectBlessView:SelectCellItemCallBack(cell)
	local seq = cell:GetData().seq
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS,
		opera_type = RA_COLLECT_BLESSING_OPEAR_TYPE.RA_COLLECT_BLESSING_OPERA_TYPE_FETCH_REWARD,
		param_1 = seq
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

-- 点击翻牌
function CollectBlessView:OnClickCardHandler(sender)
    local empty_num = ItemWGData.Instance:GetEmptyNum()
    if empty_num == 0 then 
    	SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotBagRoom)
    	return
    end
	self.select_index = sender:GetData().index

	local other = CollectBlessWGData.Instance:GetCollectBlessOther()
	local item_count = ItemWGData.Instance:GetItemNumInBagById(other.collect_blessing_consum_stuff)
	local item_cfg = ItemWGData.Instance:GetItemConfig(other.collect_blessing_consum_stuff)
	local flag = CollectBlessWGData.Instance:GetCollectBlessCardRewardFlag(self.select_index)
	if flag == -1 then
		self.is_item = 0
		local other = CollectBlessWGData.Instance:GetCollectBlessOther()
		-- local price_info = ShopWGData.GetItemPrice(other.collect_blessing_gift)

		self.draw_alert:SetLableString(string.format(Language.CollectBless.ConsumeGold , other.collect_blessing_need_gold))
		if item_count <= 0 then
			self.draw_alert:Open()
		else
			self:SendRollCard(self.is_item)
		end
	end
end

function CollectBlessView:UpdateVisibleCD(index, elapse_time, total_time)
	if nil == index then
		return
	end
	local percent =  elapse_time/total_time   --(total_time - elapse_time) * 3
	self.right_list_view:GetCell(index + 1):SetCardAction(percent)
	-- self.card_items[index]:GetView():setTouchEnabled(false)
end

function CollectBlessView:CompleteVisibleCD(index)
	if nil == index then return end
	self.right_list_view:GetCell(index + 1):Flush()
	if CountDownManager.Instance:HasCountDown("collect_bless_card_time") then
		CountDownManager.Instance:RemoveCountDown("collect_bless_card_time")
	end
end

function CollectBlessView:SendRollCard(is_item)

	local other = CollectBlessWGData.Instance:GetCollectBlessOther()
	-- 翻牌令个数
	local item_count = ItemWGData.Instance:GetItemNumInBagById(other.collect_blessing_consum_stuff)
	if item_count <= 0 then
		local gold = RoleWGData.Instance:GetRoleInfo().gold
		if gold < other.collect_blessing_need_gold then
			ActTreasureWGData.Instance:SetAutoUseGiftFlag(true)
			ShopWGCtrl.Instance:SendShopBuy(other.collect_blessing_gift, 1, 1, 0)
			return
		end
		ActTreasureWGData.Instance:SetAutoUseGiftFlag(true)
		ShopWGCtrl.Instance:SendShopBuy(other.collect_blessing_gift, 1, 1, 0)
		if self.select_index ~= nil and self.select_index ~= -1 then
			local param_t = {
				rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS,
				opera_type = RA_BIXIA_OPERA_TYPE.RA_BIXIAXUNBAO_BUY,
				param_1 = self.is_item,
				param_2 = self.select_index,
			}
			ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
			self:FlushCard()
		end
	else
		if self.select_index ~= nil and self.select_index ~= -1 then
			local param_t = {
				rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS,
				opera_type = RA_BIXIA_OPERA_TYPE.RA_BIXIAXUNBAO_BUY,
				param_1 = self.is_item,
				param_2 = self.select_index,
			}
			ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
			self:FlushCard()
		end
	end

end

function CollectBlessView:FlushCard()
	local other = CollectBlessWGData.Instance:GetCollectBlessOther()
	local item_count = ItemWGData.Instance:GetItemNumInBagById(other.collect_blessing_consum_stuff)
	local gold = RoleWGData.Instance:GetRoleInfo().gold

	if item_count > 0 or gold > other.collect_blessing_need_gold then
		if CountDownManager.Instance:HasCountDown("collect_bless_card_time") then
			CountDownManager.Instance:RemoveCountDown("collect_bless_card_time")
		end
		CountDownManager.Instance:AddCountDown("collect_bless_card_time", 
			BindTool.Bind2(self.UpdateVisibleCD, self, self.select_index), 
			BindTool.Bind2(self.CompleteVisibleCD, self, self.select_index), nil, 0.3, 0.03)
	end

end

function CollectBlessView:OnFlush()
	local word_reward_one, word_reward_two = CollectBlessWGData.Instance:GetCollectBlessWordInfo()
	for i=1,3 do
		self.reward_cell[i]:SetData(word_reward_two[i].reward_item)
	end
	local list = {}
	for i=#word_reward_one,1,-1 do
		table.insert(list,word_reward_one[i]) 
	end
	self.left_list_view:SetDataList(list,3)

	local other = CollectBlessWGData.Instance:GetCollectBlessOther()
	local price_info = ShopWGData.GetItemPrice(other.collect_blessing_gift)

	local item_count = ItemWGData.Instance:GetItemNumInBagById(other.collect_blessing_consum_stuff)
	local item_cfg = ItemWGData.Instance:GetItemConfig(other.collect_blessing_consum_stuff)
	
	-- self.node_list.rich_consume.text.text = price_info.gold
	if self.right_list_view:GetCell(self.select_index) then 
		self.right_list_view:GetCell(self.select_index):Flush()
	end
end

function CollectBlessView:OnClickBtnATips()
	RuleTip.Instance:SetContent(Language.CollectBless.Tips, Language.CollectBless.TipsTitle)
end



-----------------------------------------------------
CollectBlessLeftRender = CollectBlessLeftRender or BaseClass(BaseRender)
function CollectBlessLeftRender:__init()
	self:CreateChild()
end

function CollectBlessLeftRender:__delete()
	if nil ~= self.reward_cell then
		self.reward_cell:DeleteMe()
		self.reward_cell = nil
	end
end

function CollectBlessLeftRender:CreateChild()
	local ph_item = self.node_list.ph_item
	self.reward_cell = ItemCell.New(ph_item)

end

function CollectBlessLeftRender:OnFlush()
	if nil == self.data then
		return
	end
	self.node_list.img_high.image.enabled = self:IsSelectIndex() 
	self.node_list.img_word.text.text = string.format(Language.Activity.ActFuText,self.data.seq + 1)
	self.reward_cell:SetData(self.data.reward_item)
	local flag = CollectBlessWGData.Instance:GetCollectBlessWordRewardFlag(self.data.seq)
	self.node_list.img_get_flag:SetActive(flag == 1)
	local word_num = CollectBlessWGData.Instance:GetCollectBlessWordNum()
	self.node_list.img_tag:SetActive(word_num >= (self.data.seq + 1) and flag ~= 1)
end




-----------------------------------------------------
CollectBlessRightRender = CollectBlessRightRender or BaseClass(BaseRender)
function CollectBlessRightRender:__init()
end

function CollectBlessRightRender:__delete()
	if nil ~= self.reward_cell then
		self.reward_cell:DeleteMe()
		self.reward_cell = nil
	end
end

function CollectBlessRightRender:LoadCallBack()
	local ph_cell = self.node_list.ph_cell
	self.reward_cell = ItemCell.New(ph_cell)
end

function CollectBlessRightRender:OnFlush()
	if nil == self.data then return end
	self.node_list.img_card:SetActive(true)
	self.node_list.img_fu:SetActive(false)
	self.node_list.img_card_bg:SetActive(false)
	self.reward_cell:SetActive(false)
	local flag = CollectBlessWGData.Instance:GetCollectBlessCardRewardFlag(self.data.index)
	local info = CollectBlessWGData.Instance:GetCollectBlessCardInfo(flag)
	if flag ~= -1 then
		if info then
			self.node_list.img_fu:SetActive(info.word_flag == 1 and true or false)
			self.node_list.img_card:SetActive(false)
			self.node_list.img_card_bg:SetActive(true)
			if info.word_flag == 0 then
				self.reward_cell:SetActive(true)
				self.reward_cell:SetData(info.reward_item)
			end
		end
	end
end

function CollectBlessRightRender:SetCardAction(percent)
	self.node_list.img_card.transform.localRotation = Quaternion.Euler(0,180*percent,0)
end