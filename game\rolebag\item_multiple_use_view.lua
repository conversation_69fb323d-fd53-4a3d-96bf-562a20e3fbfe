-----------
--批量使用
-----------
local UseWeight = {
	Max = 4,
	Hight = 3,
	Middle = 2,
	Min = 1,
	None = 0,
}

ItemMultipleUseView = ItemMultipleUseView or BaseClass(SafeBaseView)
function ItemMultipleUseView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_use_offline2")
	self.item_data_change_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
end

function ItemMultipleUseView:ReleaseCallBack()
	if ItemWGData.Instance and self.item_data_change_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_event)
	end
	if self.item_list_view then
		self.item_list_view:DeleteMe()
		self.item_list_view = nil
	end
	self.item_list = nil
	self.data = nil
	self.tips = nil
end

function ItemMultipleUseView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Bag.PiLiangUse
	self:SetSecondView(nil, self.node_list["size"])
	self.item_list_view = AsyncListView.New(MultipleUseItemRender, self.node_list.item_list_view)
	self.item_list_view:SetCellSizeDel(BindTool.Bind(self.GetCellSizeDel, self))
	self.node_list.slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSoundValueChange, self))
    XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind1(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind1(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.OnClickCancel, self))
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_event)
end

function ItemMultipleUseView:ShowIndexCallBack()
	self:Flush()
end

function ItemMultipleUseView:OnFlush()
	self.item_list_view:SetDataList(self.item_list, 3)
	self:FlushInfo()

	self.node_list.tips:SetActive(self.tips ~= nil)
	if self.tips then
		self.node_list.tips.text.text = self.tips
	end
end

function ItemMultipleUseView:FlushInfo()
	local has_num = 0
	for i, v in ipairs(self.data) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if v.condition then	--符合条件才算入总数量
			has_num = has_num + num
		end
	end
	self.max = has_num < 999 and has_num or 999
	self.node_list.slider.slider.maxValue = self.max
	self.node_list.slider.slider.minValue = 1
	self.node_list.slider.slider.value = self.cur_num
	self.node_list.slider.slider.enabled = true

	--特殊情况。
	if self.max == 0 then
		self.cur_num = 0
		self.node_list.slider.slider.minValue = 0
		self.node_list.slider.slider.value = 0
	end
	if self.max == 1 then
		self.cur_num = 1
		self.node_list.slider.slider.minValue = 0
		self.node_list.slider.slider.maxValue = 1
		self.node_list.slider.slider.value = 1
		self.node_list.slider.slider.enabled = false
	end
end

function ItemMultipleUseView:CloseCallBack()
    self.tips = nil
end

function ItemMultipleUseView:SetData(data, other_data)
	self.data = data
	if not self.data then
		return
	end
    self.item_list = {}
	local len = #data
	local max_weight = 0
    for i, v in ipairs(data) do
		local insert_data = v
		insert_data.item_id = v.item_id
		insert_data.weight = v.weight
		insert_data.condition = v.condition
		insert_data.len = len
		if max_weight < v.weight then
			max_weight = v.weight
		end
        table.insert(self.item_list, insert_data)
    end
	table.sort(self.item_list, SortTools.KeyLowerSorters("weight"))
	self.max_weight = max_weight
	self.max = 0
	self.cur_num = 1

	if other_data then
		if other_data.tips then
			self.tips = other_data.tips
		end
	end

    self:Open()
end

function ItemMultipleUseView:GetCellSizeDel(data_index)
	if data_index + 1 == #self.data then
		return 100
	end
    return 200
end

function ItemMultipleUseView:GetWeightItemList(weight)
	local item_list = {}
	for i, v in ipairs(self.item_list) do
		if v.weight == weight then
			table.insert(item_list, v)
		end
	end
	return item_list
end

--优先使用权重高的
function ItemMultipleUseView:OnClickConfirm()
	local remain_use_num = self.cur_num
	local is_break = false
	for i = self.max_weight, UseWeight.None, -1 do
		local cur_use_item_list = self:GetWeightItemList(i)
		if not IsEmptyTable(cur_use_item_list) then
			for i, v in ipairs(cur_use_item_list) do
				if v.condition then
					local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
					num = num < self.cur_num and num or self.cur_num
					if num <= 0 then
						break
					end
					--Send protocal
					local item_data = ItemWGData.Instance:GetItem(v.item_id)
					local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
					if not item_data or not item_cfg then
						return end
					if item_data.num >= num then
						BagWGCtrl.Instance:SendUseItem(item_data.index, GameMath.Round(num), 0, item_cfg.need_gold)
					else

						local tab = ItemWGData.Instance:GetContinueUseItem(v.item_id, num)
						if nil ~= tab then
							for i = 1, #tab do
								BagWGCtrl.Instance:SendUseItem(tab[i].index, GameMath.Round(tab[i].num), 0, item_cfg.need_gold)
							end
						else
							print_error("没有找到该配置", v.item_id)
						end
					end

					remain_use_num = remain_use_num - num
					if remain_use_num <= 0 then
						is_break = true
						break
					end
				end
			end
		end
		if is_break then
			break
		end
	end
	self:Close()
end

function ItemMultipleUseView:OnClickCancel()
	self:Close()
end

function ItemMultipleUseView:OnSoundValueChange(float_param)
	self.node_list.lbl_num.text.text = float_param
	self.cur_num = tonumber(float_param)
end

function ItemMultipleUseView:OnClickSub()
	self.cur_num = self.node_list.lbl_num.text.text - 1
	if self.cur_num <= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MinValue1)
	end
	self.cur_num = self.cur_num <= 1 and 1 or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function ItemMultipleUseView:OnClickAdd()
	self.cur_num = self.node_list.lbl_num.text.text + 1
	if self.cur_num >= self.max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
	end
	self.cur_num = self.cur_num >= self.max and self.max or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function ItemMultipleUseView:ItemDataChangeCallback()
	self:Flush()
end



MultipleUseItemRender = MultipleUseItemRender or BaseClass(BaseRender)
function MultipleUseItemRender:__init()
	self.cell = ItemCell.New(self.node_list["pos"])
end

function MultipleUseItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function MultipleUseItemRender:OnFlush()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	self.cell:SetHideRightDownBgLessNum(-1)
	self.cell:SetData({item_id = self.data.item_id, num = item_num})
	self.node_list.huo:SetActive(self.index ~= self.data.len)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list.name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
end