KFZhuXieZhanChang = KFZhuXieZhanChang or BaseClass(CommonActivityLogic)

function KF<PERSON>hu<PERSON>ieZhanChang:__init()
	self.is_auto_task = false
	self.boss_kill_stop_time = 0
end

function KFZhuXieZhanChang:__delete()
	self.my_title = nil
end

function KFZhuXieZhanChang:Enter(old_scene_type, new_scene_type)
	-- print_error("进入跨服诛邪战场")
	self.boss_enter_handler = GlobalEventSystem:Bind(ObjectEventType.VISIBLE_OBJ_ENTER_MONSTER, BindTool.Bind(self.OnBossEnterVisible, self))
	self.target_hp_change_event = GlobalEventSystem:Bind(ObjectEventType.TARGET_HP_CHANGE, BindTool.Bind(self.OnTargetHpChangeHead, self))
	CommonActivityLogic.Enter(self, old_scene_type, new_scene_type)

	self:OpenActivitySceneCd(ACTIVITY_TYPE.KF_ZHUXIE)
	ViewManager.Instance:CloseAll()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ZHUXIE) then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		GuajiWGCtrl.Instance:StopGuaji()
	end
	MainuiWGCtrl.Instance:SetShowTimeTextState(false)
	-- 所有下坐骑(普通和战斗坐骑)
	MountWGCtrl.Instance:AllDownMount()
	ActivityWGCtrl.Instance:OpenKFZhuXieFollowView()
	local scene_id = Scene.Instance:GetSceneId()
	local param_t = {scene_id = scene_id}
	Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, 40, 170, nil, param_t)

	self:CheckSkillCanUse()

	self.is_auto_task = false
	self.boss_die_flag = false
	self.boss_kill_stop_time = 0
end

-- 检查是否可以真正使用技能，避免在准备期间使用技能冲出安全区
function KFZhuXieZhanChang:CheckSkillCanUse()
	FightWGData.Instance:SetCanSendPerformSkill(true)
	self:UnNotifyActEvent()
	local scene_id = Scene.Instance:GetSceneId()
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if nil == act_scene then
		return
	end
	self.zhuxie_act_id = act_scene.act_id

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(self.zhuxie_act_id)
	if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
		if nil == self.zhuxie_act_state_event then
			self.zhuxie_act_state_event = BindTool.Bind1(self.OnZhuXieActivityStatus, self)
			ActivityWGData.Instance:NotifyActChangeCallback(self.zhuxie_act_state_event)
		end
		FightWGData.Instance:SetCanSendPerformSkill(false)
	end
end

function KFZhuXieZhanChang:UnNotifyActEvent()
	if self.zhuxie_act_state_event then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.zhuxie_act_state_event)
		self.zhuxie_act_state_event = nil
	end
end

function KFZhuXieZhanChang:OnZhuXieActivityStatus(activity_type, status, next_time, open_type)
	if nil == self.zhuxie_act_id or self.zhuxie_act_id ~= activity_type then
		return
	end

	if status ~= ACTIVITY_STATUS.STANDY then
		self:UnNotifyActEvent()
		FightWGData.Instance:SetCanSendPerformSkill(true)
	else
		FightWGData.Instance:SetCanSendPerformSkill(false)
	end
end

function KFZhuXieZhanChang:Out()
	FightWGData.Instance:SetCanSendPerformSkill(true)
	self:UnNotifyActEvent()

	if self.boss_enter_handler then
		GlobalEventSystem:UnBind(self.boss_enter_handler)
		self.boss_enter_handler = nil
	end

	if self.target_hp_change_event then
		GlobalEventSystem:UnBind(self.target_hp_change_event)
		self.target_hp_change_event = nil
	end

	ViewManager.Instance:CloseAll()
	CommonActivityLogic.Out(self)
	ActivityWGCtrl.Instance:CloseKFZhuXieFollowView()
	self:ResumeSelfInfo()

	self.is_auto_task = false
	self.time = nil
	self.boss_kill_stop_time = 0
end

function KFZhuXieZhanChang:MoveToGatherRange()
	return 3
end

--无目标状态下获取挂机打怪的位置
function KFZhuXieZhanChang:GetGuajiPos()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ZHUXIE)

	if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
		return
	end

	local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo() or {}
	if IsEmptyTable(boss_info) then
		return
	end

    if self:GetStopTime() >= Status.NowTime then
    	return
    end

	if boss_info.boss_id ~= nil and boss_info.boss_cur_hp > 0 then
		local scene_id = Scene.Instance:GetSceneId()
		local other_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").other[1]
		local pos_list = Split(other_cfg.boss_pos, ",")

		if other_cfg ~= nil and not IsEmptyTable(pos_list) then
			local range = BossWGData.Instance:GetMonsterRangeByid(boss_info.boss_id)
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_list[1], pos_list[2], range)
		end

		return
	end
	local task_id = ActivityWGData.Instance:GetCurTaskid()

	--自己逻辑
	if task_id == nil then
		task_id = ActivityWGData.Instance:GetKFZhuXieANotCompleteTaskID()
		if task_id ~= nil then
			ActivityWGCtrl.Instance:AutoProceedATask(task_id)
			return
		end
	elseif task_id ~= 5 then
		local task_info = ActivityWGData.Instance:GetKFZhuXieTaskInfoByTaskId(task_id) or {}
		local param_value = task_info.param_value or 0
		local max_value = task_info.cfg.param_max_value or 0
		local is_fetched = task_info.is_fetched or 0
		if (is_fetched == 1 and max_value <= param_value) or is_fetched == 1 then
			task_id = ActivityWGData.Instance:GetKFZhuXieANotCompleteTaskID()
			if task_id ~= nil then
				ActivityWGCtrl.Instance:AutoProceedATask(task_id)
				return
			else
				local kill_role_task = ActivityWGData.Instance:GetKFZhuXieTaskInfoByTaskId(5)
				if kill_role_task and kill_role_task.is_fetched == 0 then
					ActivityWGCtrl.Instance:AutoProceedATask(task_id)
				end
				return
			end
		else
			ActivityWGCtrl.Instance:AutoProceedATask(task_id)
			return
		end

		local task_info = ActivityWGData.Instance:GetKFZhuXieInfoByid(task_id) or {}
		local param_value = task_info.param_value or 0
		local param_max_value = task_info.cfg.param_max_value or 0
		local is_fetched = task_info.is_fetched or 0
		if (is_fetched == 1 and param_max_value <= param_value) or is_fetched == 1 then
			task_id = ActivityWGData.Instance:GetKFZhuXieANotCompleteTaskID()
			if task_id ~= nil then
				ActivityWGCtrl.Instance:AutoProceedATask(task_id)
				return
			end
		else
			ActivityWGCtrl.Instance:AutoProceedATask(task_id)
			return
		end
	elseif task_id == 5 then
		ActivityWGCtrl.Instance:AutoProceedATask(task_id)
		return
	end

    local target_distance = 1000 * 1000
    local target_x = nil
    local target_y = nil
    local x, y = Scene.Instance:GetMainRole():GetLogicPos()

    local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()
    for k, v in pairs(obj_move_info_list) do
        local vo = v:GetVo()
        if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
            local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
            if distance < target_distance then
                target_x = vo.pos_x
                target_y = vo.pos_y
                target_distance = distance
            end
        end
    end
    return target_x, target_y
end

-- 获取挂机打怪
function KFZhuXieZhanChang:GetGuajiCharacter()
	local target_obj = nil
	local is_has_target = false
	local is_need_stop = false

	local dis = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() then
		is_has_target = Scene.Instance:IsEnemy(GuajiCache.target_obj)
	end

	-- 无选择目标
	if not is_has_target then
		target_obj = self:GetMonster()
		local main_role = Scene.Instance:GetMainRole()
		if main_role ~= nil and target_obj ~= nil and self:IsEnemy(target_obj, main_role) then
			is_need_stop = true
		end
	else
		target_obj = GuajiCache.target_obj
	end

	return target_obj, dis, is_need_stop
end


-- 角色是否是敌人
function KFZhuXieZhanChang:IsRoleEnemy(target_obj, main_role)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ZHUXIE)
	if nil ~= activity_info then
		local flag = activity_info.status == ACTIVITY_STATUS.STANDY
		if flag then
			return false
		end
	end

	main_role = main_role or Scene.Instance:GetMainRole()
	if nil == target_obj or nil == main_role or not target_obj:IsCharacter() then
		return false
	end
	local attack_mode = main_role:GetVo().attack_mode
	if attack_mode == ATTACK_MODE.PEACE then
		return false
	elseif attack_mode == ATTACK_MODE.TEAM then
		if Scene.Instance:GetIsOpenCrossViewByScene() then
            return not CrossTeamWGData.Instance:GetTargetIsTeamMember(target_obj:GetUUID())
        else
            return not SocietyWGData.Instance:IsTeamMember(target_obj:GetOriginId())
        end
	elseif attack_mode == ATTACK_MODE.GUILD then
		local b1 = not SocietyWGData.Instance:IsTeamMember(target_obj:GetRoleId())
		local b2 = main_role:GetVo().guild_id == 0 or main_role:GetVo().guild_id ~= target_obj:GetVo().guild_id

		if b1 and b2 then
			return true
		end

		if not b1 then
			return b1, Language.Fight.TargetTeam
		end
		return b2, Language.Fight.TargetGuild
	elseif attack_mode == ATTACK_MODE.ALL then
		return true

	elseif attack_mode == ATTACK_MODE.NAMECOLOR then
		return target_obj:GetVo().name_color ~= GameEnum.NAME_COLOR_WHITE

	elseif attack_mode == ATTACK_MODE.CAMP then
		return main_role:GetVo().camp == 0 or (main_role:GetVo().camp ~= target_obj:GetVo().camp)
	end

	return true
end

function KFZhuXieZhanChang:ChangeKFZhuXieTitle()
	local main_role = Scene.Instance:GetMainRole()
	local vo = main_role and main_role.vo
	if vo then
		self:AddKFZhuXieDelay(vo.used_title_list)
		main_role:SetAttr("used_title_list",{})
	end
end

function KFZhuXieZhanChang:AddKFZhuXieDelay(title)
	self.my_title = title and TableCopy(title) or self.my_title
end

function KFZhuXieZhanChang:ResumeSelfInfo()
	if self.my_title then
		RoleWGData.Instance:SetAttr("used_title_list", self.my_title)
	end
end

function KFZhuXieZhanChang:OnBossEnterVisible(monster_vo)
	if nil == monster_vo then
		return
	end

	local zhuxie_info = ActivityWGData.Instance:GetKFZhuXieTaskInfo()
	local min_per = ActivityWGData.Instance:GetZhuXieMinHpPer()
	local cur_per = 0
	if monster_vo.max_hp > 0 then
		cur_per = monster_vo.hp / monster_vo.max_hp
	end

	if zhuxie_info and monster_vo.monster_id == zhuxie_info.boss_id and cur_per <= min_per then
		local obj = Scene.Instance:GetObjectByObjId(monster_vo.obj_id)
		if obj:GetDrawObj() and obj:IsCharacter() then
			obj:GetDrawObj():SetIsMultiColor(KuangBao_Color)
			obj:CheckBossModleScale(1.5)
		end
	end
end

-- 目标血量改变
function KFZhuXieZhanChang:OnTargetHpChangeHead(target_obj, no_change)
	if nil == target_obj or nil == target_obj:GetVo() then
		return
	end

	local monster_vo = target_obj:GetVo()
	local zhuxie_info = ActivityWGData.Instance:GetKFZhuXieTaskInfo()
	local min_per = ActivityWGData.Instance:GetKFZhuXieMinHpPer()
	local cur_per = 0
	if monster_vo.max_hp > 0 then
		cur_per = monster_vo.hp / monster_vo.max_hp
	end

	if zhuxie_info and monster_vo.monster_id == zhuxie_info.boss_id and cur_per <= min_per then
		local obj = Scene.Instance:GetObjectByObjId(monster_vo.obj_id)
		if obj:GetDrawObj() and obj:IsCharacter() then
			obj:GetDrawObj():SetIsMultiColor(KuangBao_Color)
			obj:GetDrawObj():SetScale(1.5, 1.5, 1.5)
		end
	end
end

function KFZhuXieZhanChang:SetIsAutoTask(value)
	self.is_auto_task = value
end

function KFZhuXieZhanChang:GetIsAutoTask()
	return self.is_auto_task
end

function KFZhuXieZhanChang:SetBossDieflag(flag)
	if flag then
		self.boss_kill_stop_time = Status.NowTime + 3
	else
		self.boss_kill_stop_time = 0
	end
end

function KFZhuXieZhanChang:GetStopTime()
	return self.boss_kill_stop_time
end

-- 此场景优先保证单位数量
function KFZhuXieZhanChang:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function KFZhuXieZhanChang:IsEnemyVisiblePriortiy()
	return true
end

-- 此场景优先保证单位数量
function KFZhuXieZhanChang:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function KFZhuXieZhanChang:IsEnemyVisiblePriortiy()
	return true
end
