﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class YYGrass : MonoBehaviour {

    private Vector4[] obstaclePositions = new Vector4[10];
    // Use this for initialization
    void Start () {
		
	}
	
	// Update is called once per frame
	void Update () {
        obstaclePositions[0] = this.transform.position;

        Shader.SetGlobalFloat("_PositionArray", 1);
        Shader.SetGlobalVectorArray("_ObstaclePositions", obstaclePositions);
    }
}
