-- 在线奖励

function WelfareView:InitOnlineRewardView()
	if not self.item_remder then
		self.item_remder = AsyncListView.New(WelfareOnlineRewardItem, self.node_list["ph_reward_list"])
	end
	-- self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickRewardLingQu, self))
end

function WelfareView:DescoryOnlineReward()
	if self.item_remder then
		self.item_remder:DeleteMe()
		self.item_remder = nil
	end
end

function WelfareView:OnFlushOnlineReward()
	if not self.item_remder then
		return
	end

	local list = WelfareWGData.Instance:GetOnlineRewardCfg()
	local the_first_isget = false

	if list[1] then
		local online_time = WelfareWGData.Instance:GetOnlineTime()
		local need_time = list[1].time_stage * 60
		local reward_flag = WelfareWGData.Instance:GetRewardFlag(list[1].stage)
		the_first_isget = online_time > need_time and reward_flag == 1
	end

	local flush_type = 0
	if the_first_isget then
		flush_type = 1
	end

	self.item_remder:SetDataList(list, flush_type)
	local is_can_lingqu = WelfareWGData.Instance:IsShowWelfareOnlineGiftRedPoint()
	-- XUI.SetButtonEnabled(self.node_list["btn_lingqu"], is_can_lingqu == 1)
	-- self.node_list["btn_lingqu"]:SetActive(is_can_lingqu == 1)
end

function WelfareView:OnClickRewardLingQu()
	-- local index = WelfareWGData.Instance:GetCurRewardStage()
	-- if index < 0 then
	-- 	return
	-- end
	WelfareWGCtrl.Instance:SendOnlineGiftOperReq(ONLINE_GIFT_OPERA_TYPE.ONLINE_GIFT_OPERA_TYPE_FETCH_ONE_KEY)
end

----------------------------------------------------------------------------------------------------------------
--背景高度(根据item_num)
local HIGHT_POS_BG = {
	[1] = Vector2(105,288),
	[2] = Vector2(105,379),
	[3] = Vector2(105,466),
}

WelfareOnlineRewardItem = WelfareOnlineRewardItem or BaseClass(BaseRender)

function WelfareOnlineRewardItem:LoadCallBack()
	self.node_list["img_klq"].button:AddClickListener(BindTool.Bind1(self.OnClickLingQu, self))
end

function WelfareOnlineRewardItem:__delete()
	if self.item_cell then
		for i=0,2 do
			self.item_cell[i]:DeleteMe()
		end
		self.item_cell = nil
	end
	if self.end_time_flag and CountDownManager.Instance:HasCountDown(self.end_time_flag) then
		CountDownManager.Instance:RemoveCountDown(self.end_time_flag)
	end
	self.end_time_flag = nil

	if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
end

function WelfareOnlineRewardItem:OnFlush()
	if nil == self.data then
		return
	end
	self.end_time_flag = "end_time_flag"..self.data.stage
	self.node_list["lbl_time"].text.text = string.format(Language.Welfare.OnLineNeedTime,self.data.time_stage)
	self:CreatCell()
	self:SetViewShow()
end

function WelfareOnlineRewardItem:CreatCell()
	if nil == self.item_cell then
		self.item_cell = {}
		for i=0,2 do
			self.item_cell[i] = ItemCell.New(self.node_list["ph_cell_"..i])
		end
	end
	local num  = 1
	for i=0,2 do
		if self.data.stage_reward[i] then
			self.node_list["ph_cell_"..i]:SetActive(true)
			self.item_cell[i]:SetData(self.data.stage_reward[i])
			num = i + 1
		else
			self.node_list["ph_cell_"..i]:SetActive(false)
		end
	end
	self.node_list["img_bg"].rect.sizeDelta = HIGHT_POS_BG[num]
	self.node_list["img_zhezhao"].rect.sizeDelta = HIGHT_POS_BG[num]
end

function WelfareOnlineRewardItem:SetViewShow()
	local cur_stage = WelfareWGData.Instance:GetOneRewardStage()
	local online_time = WelfareWGData.Instance:GetOnlineTime()
	local need_time = self.data.time_stage * 60
	local reward_flag = WelfareWGData.Instance:GetRewardFlag(self.data.stage)
	if online_time >= need_time and reward_flag == 0 then --可领取
		self.node_list["img_klq"]:SetActive(true)
		self.node_list["img_ylq"]:SetActive(false)
		self.node_list["img_red"]:SetActive(true)
		self.node_list["lbl_end_time"]:SetActive(false)
		self.node_list["img_zhezhao"]:SetActive(false)
		self.node_list["img_klq"].button.enabled = true
	elseif online_time >= need_time and reward_flag == 1 then --已领取
		self.node_list["img_klq"]:SetActive(false)
		self.node_list["img_ylq"]:SetActive(true)
		self.node_list["img_red"]:SetActive(false)
		self.node_list["lbl_end_time"]:SetActive(false)
		self.node_list["img_zhezhao"]:SetActive(false)
		self.node_list["img_klq"].button.enabled = false
	else
		local end_time = need_time + WelfareWGData.Instance:GetOnLineStarTime() - online_time
		self:UpdataEndTime(TimeWGCtrl.Instance:GetServerTime(), end_time)
		if self.end_time_flag and CountDownManager.Instance:HasCountDown(self.end_time_flag) then
			CountDownManager.Instance:RemoveCountDown(self.end_time_flag)
		end
		CountDownManager.Instance:AddCountDown(self.end_time_flag, BindTool.Bind1(self.UpdataEndTime, self), BindTool.Bind1(self.CompleteEndCallBack, self), end_time, nil, 1)
		self.node_list["img_klq"]:SetActive(false)
		self.node_list["img_ylq"]:SetActive(false)
		self.node_list["img_red"]:SetActive(false)
		self.node_list["lbl_end_time"]:SetActive(cur_stage == self.data.stage)
		self.node_list["img_zhezhao"]:SetActive(true)
		self.node_list["img_klq"].button.enabled = false
	end
	local max_stage = WelfareWGData.Instance:GetMaxRewardStage()
	local ani_index,is_play = WelfareWGData.Instance:GetOneCanAniIndex()
	if ani_index == self.data.stage and is_play then
		WelfareWGData.Instance:GetAniCanPlay(false)
		self:AniRotate()
	end

end

function WelfareOnlineRewardItem:UpdataEndTime(elapse_time, total_time)
	local time = total_time - elapse_time
	local format_time = TimeUtil.Format2TableDHMS(time)
	local min = format_time.day * 24 * 60 + format_time.hour * 60 + format_time.min
	local s = format_time.s
	local end_desc = string.format(Language.Welfare.OnLineEndTime, min,s)
	self.node_list["lbl_end_time"].text.text = end_desc 
end

function WelfareOnlineRewardItem:CompleteEndCallBack()
	 WelfareWGCtrl.Instance:SendOnlineGiftOperReq(ONLINE_GIFT_OPERA_TYPE.ONLINE_GIFT_OPERA_TYPE_ASK_INFO)
end

function WelfareOnlineRewardItem:OnClickLingQu()
	WelfareWGCtrl.Instance:SendOnlineGiftOperReq(ONLINE_GIFT_OPERA_TYPE.ONLINE_GIFT_OPERA_TYPE_FETCH,self.data.stage)
end

function WelfareOnlineRewardItem:AniRotate()
	local ani_tween = function (trans)
		local sequence = DG.Tweening.DOTween.Sequence()
		sequence:Append(trans:DOLocalRotate(Vector3(0, 30, 0),0.5):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:Append(trans:DOLocalRotate(Vector3(0, -30, 0),1):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:Append(trans:DOLocalRotate(Vector3(0,30,0),1):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:Append(trans:DOLocalRotate(Vector3(0,0,0),1):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:AppendInterval(1)
		return sequence
	end

	if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

	self.sequence = ani_tween(self.node_list.continue_btn.transform):SetLoops(1)
end