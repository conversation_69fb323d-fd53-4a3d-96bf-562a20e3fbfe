CrossLongMaiView = CrossLongMaiView or BaseClass(SafeBaseView)
function CrossLongMaiView:__init()
	self.view_style = ViewStyle.Full
	self:AddViewResource(0, "uis/view/longmai_ui_prefab", "layout_cross_longmai")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")
    self.is_safe_area_adapter = true
end

function CrossLongMaiView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.CrossLongMai.LongMaiTitleName
    self.reward_list_view = AsyncListView.New(ItemCell, self.node_list["reward_list_view"])
    self.reward_list_view:SetStartZeroIndex(true)

    -- 固定物品列表
    if self.fixed_item_list == nil then
        self.fixed_item_list = {}
        for i = 1, 4 do
            self.fixed_item_list[i] = ItemCell.New(self.node_list["rare_item_" .. i])
        end
    end

    -- 界面其他红点
    local other_remind_list = {RemindName.CrossLongMaiAct}
    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    for k,v in pairs(other_remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end

    XUI.AddClickEventListener(self.node_list.btn_shop, BindTool.Bind(self.OnClickShop, self))
    --XUI.AddClickEventListener(self.node_list.btn_rule, BindTool.Bind(self.OnClickRule, self))
    XUI.AddClickEventListener(self.node_list.btn_join, BindTool.Bind(self.OnClickJoin, self))
end

function CrossLongMaiView:ReleaseCallBack()
    RemindManager.Instance:UnBind(self.remind_callback)

    if self.reward_list_view then
        self.reward_list_view:DeleteMe()
        self.reward_list_view = nil
    end

    if self.fixed_item_list then
        for k,v in pairs(self.fixed_item_list) do
            v:DeleteMe()
        end

        self.fixed_item_list = nil
    end
end

function CrossLongMaiView:OtherRemindCallBack(remind_name, num)
    if remind_name == RemindName.CrossLongMaiAct then
        self.node_list["btn_join_remind"]:SetActive(num > 0)
    elseif remind_name == "" then
        self.node_list["btn_shop_remind"]:SetActive(num > 0)
    end
end

function CrossLongMaiView:OnFlush()
    local reward_cfg = CrossLongMaiWGData.Instance:GetDisplayReward()
    self.reward_list_view:SetDataList(reward_cfg.reward_item)
    local rare_cfg = reward_cfg.rare_item
    local week_desc, time_desc = CountryMapActShowWgData.Instance:GetActOpenTimeByActType(ACTIVITY_TYPE.KF_LONGMAI, 5)
    self.node_list.open_time_text.text.text = week_desc .. "   " .. time_desc
    self.node_list.longmai_act_des.text.text = CrossLongMaiWGData.Instance:GetOtherCfg().act_des

    for k,v in ipairs(self.fixed_item_list) do
        v:SetData(rare_cfg[k - 1])
    end
end

function CrossLongMaiView:OnClickShop()
    ViewManager.Instance:Open(GuideModuleName.LongMaiShop)
end

function CrossLongMaiView:OnClickRule()
    CrossLongMaiWGCtrl.Instance:OpenRuleTipsView()
end

function CrossLongMaiView:OnClickJoin()
    self:Close()
    if ViewManager.Instance:IsOpen(GuideModuleName.CountryMapMapView) then
        ViewManager.Instance:Close(GuideModuleName.CountryMapMapView)
    end

    CrossLongMaiWGCtrl.Instance:FindNpc()
end