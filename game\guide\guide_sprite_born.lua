------------------------------------------------------------
--引导精灵出现
------------------------------------------------------------
GuideSpriteBorn = GuideSpriteBorn or BaseClass(SafeBaseView)

function GuideSpriteBorn:__init()
	self:SetMaskBg(true, true, false, BindTool.Bind(self.OnClickNext, self))
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "guide_sprite_born")
	self.view_cache_time = 0
	self.step = 0
	-- self.open_tween = nil
	-- self.close_tween = nil
end

function GuideSpriteBorn:__delete()
end

function GuideSpriteBorn:ReleaseCallBack()
	self:CancelQuest()
	if self.model then
		self.model:DeleteMe()
		self.model = nil
	end
end

function GuideSpriteBorn:CloseCallBack()
end

function GuideSpriteBorn:LoadCallBack()
	self.model= RoleModel.New()
	local display_data = {
		parent_node = self.node_list["bag"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = false,
	}

	self.model:SetRenderTexUI3DModel(display_data)
	self.model:SetMainAsset(ResPath.GetNpcModel(2004001))
	self.model:CustomDisplayPositionAndRotation(Vector3(0, -1, 0), Quaternion.Euler(0, 180, 0), Vector3(2.5, 2.5, 2.5))
end

function GuideSpriteBorn:OnFlush(params)

end

function GuideSpriteBorn:ShowIndexCallBack()
	self.can_skip = true
	if not self.next_handle_timer then
		self.step = 0
		self.node_list.dec.text.text = Language.Guide.SpriteBornTxt[1]
		self:OnClickNext()
	end
end

function GuideSpriteBorn:OnClickNext()
	if not self:IsLoaded() then -- 如果未loadcallback点遮罩，直接return
		return
	end

	if not self.can_skip and self.next_handle_timer then
		return
	end
	self:CancelQuest()
	self.step = self.step + 1
	if self.step == 1 then
		self.can_skip = false
		
		if nil == self.model then
			self.model= RoleModel.New()
			local display_data = {
				parent_node = self.node_list["bag"],
				camera_type = MODEL_CAMERA_TYPE.BASE,
				-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
				rt_scale_type = ModelRTSCaleType.M,
				can_drag = false,
			}

			self.model:SetRenderTexUI3DModel(display_data)
		end

		self.model:SetTrigger(SceneObjAnimator.Rest)
		self.next_handle_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self.node_list.dec.text.text = ""
			self.model:SetMainAsset(ResPath.GetNpcModel(2005001))
			self.model:CustomDisplayPositionAndRotation(Vector3(0, -1.78, 0), Quaternion.Euler(12.42, 180, 0), Vector3(2.5, 2.5, 2.5))
			self.model:SetTrigger(SceneObjAnimator.Rest)
			self.next_handle_timer = GlobalTimerQuest:AddDelayTimer(function ()
				self.node_list.dec.text.text = ""
				self.can_skip = true
				self.next_handle_timer = GlobalTimerQuest:AddDelayTimer(function ()
					self:OnClickNext()
				end, 2)
			end, 3.333)
		end, 2.267)
	else
		self:Close()
	end
end

function GuideSpriteBorn:CancelQuest()
	if self.next_handle_timer then
		GlobalTimerQuest:CancelQuest(self.next_handle_timer)
		self.next_handle_timer = nil
	end
end