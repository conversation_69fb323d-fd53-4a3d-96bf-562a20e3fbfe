local effect_url = "UI_jcgx"
local effect_url1 = "UI_djgx"
local SLOT_INDEX = TianShenWGData.SLOT_INDEX

local attr_name_list = nil

function ShenShiQiangHuaView:JiChengReleaseCallBack()
	if self.base_cell_left then
		self.base_cell_left:DeleteMe()
		self.base_cell_left = nil
	end

	if self.base_cell_right then
		self.base_cell_right:DeleteMe()
		self.base_cell_right = nil
	end

	if self.base_grid then
		self.base_grid:DeleteMe()
		self.base_grid = nil
	end

	self.tianshen_put_equip_num = 0
	self.jicheng_param_list = nil
end

function ShenShiQiangHuaView:JiChengCloseCallBack()
    self:ClearSelectInfo()
    self.base_grid:CancleAllSelectCell()
end

function ShenShiQiangHuaView:JiChengOpenCallBack()
	if self:IsLoadedIndex(TabIndex.shenshi_jicheng) then
		self:InitJiChengBagDataList()
	end
end

function ShenShiQiangHuaView:InitJiChengBagDataList()
	self.jicheng_bag_data_list, self.tianshen_put_equip_num = TianShenWGData.Instance:GetJiChengBag(self:GetJiChengSelectData().index)
end

function ShenShiQiangHuaView:JiChengLoadCallBack()
	self.tianshen_put_equip_num = 0
	self.jicheng_param_list = nil															-- 外面传进来的格子数据

	self.left_slot_data_index = 0 															-- 左边镶嵌孔数据下标，0表示没有数据
	self.right_slot_data_index = 0  														-- 右边镶嵌孔数据下标，0表示没有数据
	self.select_slot_index = SLOT_INDEX.LEFT 												-- 选中哪个镶嵌孔， 0表示都没有选中

	self.base_cell_left = ItemCell.New(self.node_list["jicheng_item_1"])
	self.base_cell_left:AddClickEventListener(BindTool.Bind(self.JiChengItemCallBack, self, SLOT_INDEX.LEFT, self.base_cell_left, false), true)
	self.base_cell_left:SetIsShowTips(false)
	self.base_cell_left:SetSelectEffectImage(false)
	self.base_cell_left:SetToggleGroup(self.node_list["go_img"].toggle_group)
	self.base_cell_left:SetIndex(SLOT_INDEX.LEFT)
	self.base_cell_left:SetCellBgEnabled(false)
	self.base_cell_left:SetUseButton(true)

	self.base_cell_right = ItemCell.New(self.node_list["jicheng_item_2"])
	self.base_cell_right:AddClickEventListener(BindTool.Bind(self.JiChengItemCallBack, self, SLOT_INDEX.RIGHT, self.base_cell_right, false), true)
	self.base_cell_right:SetIsShowTips(false)
	self.base_cell_right:SetSelectEffectImage(false)
	self.base_cell_right:SetToggleGroup(self.node_list["go_img"].toggle_group)
	self.base_cell_right:SetIndex(SLOT_INDEX.RIGHT)
	self.base_cell_right:SetCellBgEnabled(false)
	self.base_cell_right:SetUseButton(true)

	self.base_grid = AsyncBaseGrid.New()
	self.base_grid:SetStartZeroIndex(false)
	self.base_grid:CreateCells({col = 4, cell_count = 200, list_view = self.node_list["ph_main_grid"], itemRender = InheritBagCell})
	self.base_grid:SetSelectCallBack(BindTool.Bind1(self.ClickItemRender, self))
	XUI.AddClickEventListener(self.node_list["btn_jicheng"], BindTool.Bind(self.OnClickJiCheng, self))
	XUI.AddClickEventListener(self.node_list["jicheng_tips_btn"], BindTool.Bind(self.OnClickTipsBtn, self))

	self:InitJiChengBagDataList()
end

function ShenShiQiangHuaView:JiChengOnFlush(param_list)
	for k,v in pairs(param_list) do
		if k == "all" then
			if v.bag_index then
				self.jicheng_param_list = v
				self:ParamListHandle()
			end
			self:JiChengOnFlushView()
		elseif k == "FlushBagView" then
			self:InitJiChengBagDataList()
			-- 卸下物品
			self:ItemXieXia(SLOT_INDEX.LEFT)
			self:ItemXieXia(SLOT_INDEX.RIGHT)

			if 1 == v.reason_type then
				-- 播放特效
				self:ShowJiChengEffect()
			end
		end
	end
end

function ShenShiQiangHuaView:ParamListHandle()
	for index, v in ipairs(self.jicheng_bag_data_list) do
		if v.knapsack_type == self.jicheng_param_list.knapsack_type and v.bag_index == self.jicheng_param_list.bag_index then
			self.left_slot_data_index = index
			self:AutoChangeSelectIndex()
			break
		end
	end
end

function ShenShiQiangHuaView:JiChengOnFlushView()
	self:FlushSlotItem()
	self:FlushBagView()
end

-- 刷新背包
function ShenShiQiangHuaView:FlushBagView()
	local bag_data_list = {}
	for i,v in ipairs(self.jicheng_bag_data_list) do
		if i ~= self.left_slot_data_index and i ~= self.right_slot_data_index then
			v.data_index = i
			v.tianshen_index = self.select_tainshen_cfg.index
			table.insert(bag_data_list, v)
		end
	end

	table.sort(bag_data_list, function (info1,info2)
		local item_cfg1 = ItemWGData.Instance:GetItemConfig(info1.item_id)
		local item_cfg2 = ItemWGData.Instance:GetItemConfig(info2.item_id)
		if info1.is_put_on ~= info2.is_put_on then
			return info1.is_put_on == true
		elseif item_cfg1 and item_cfg2 and item_cfg1.color ~= item_cfg2.color then
			return item_cfg1.color > item_cfg2.color
		elseif item_cfg1 and item_cfg2 and item_cfg1.sub_type ~= item_cfg2.sub_type then
			return item_cfg1.sub_type < item_cfg2.sub_type
		elseif info1.grade_level ~= info2.grade_level then
			return info1.grade_level > info2.grade_level
		elseif info1.stren_level ~= info2.stren_level then
			return info1.stren_level > info2.stren_level
		elseif info1.pingfen ~= info2.pingfen then
			return info1.pingfen > info2.pingfen
		end
		return false
	end)

	self.base_grid:SetDataList(bag_data_list, 0)

    self.base_grid:CancleAllSelectCell()
end

-- 刷新镶嵌孔
function ShenShiQiangHuaView:FlushSlotItem()
	local left_data = self.jicheng_bag_data_list[self.left_slot_data_index]
	self.base_cell_left:SetData(left_data)

	local right_data = self.jicheng_bag_data_list[self.right_slot_data_index]
	self.base_cell_right:SetData(right_data)

	-- 加号
	self.node_list["img_add1"]:SetActive(left_data == nil)
	self.node_list["img_add2"]:SetActive(right_data == nil)

	-- 物品名称
	self.node_list["jicheng_text_jingjie_name1"]:SetActive(false)
	self.node_list["jicheng_text_jingjie_name2"]:SetActive(false)
	if left_data then
		self.node_list["jicheng_text_jingjie_name1"].text.text = ItemWGData.Instance:GetItemName(left_data.item_id)
		self.node_list["jicheng_text_jingjie_name1"]:SetActive(true)
	end

	if right_data then
		self.node_list["jicheng_text_jingjie_name2"].text.text = ItemWGData.Instance:GetItemName(right_data.item_id)	
		self.node_list["jicheng_text_jingjie_name2"]:SetActive(true)
	end

	self:FlushSlotEffect()

	self:FlushJiChengAttr()
end

-- 刷新镶嵌孔特效
function ShenShiQiangHuaView:FlushSlotEffect()
	self.base_cell_left:SetEffectEnable(false, effect_url)
	self.base_cell_right:SetEffectEnable(false, effect_url)
	if self.select_slot_index == SLOT_INDEX.LEFT then
		self.base_cell_left:SetEffectEnable(true, effect_url)
	elseif self.select_slot_index == SLOT_INDEX.RIGHT then
		self.base_cell_right:SetEffectEnable(true, effect_url)
	end
end

-- 刷新属性
function ShenShiQiangHuaView:FlushJiChengAttr()
	if not attr_name_list then
		attr_name_list = {}
		for k,v in pairs(Language.Common.TipsAttrNameList) do
			attr_name_list[k] = v .. "："
		end
	end

	self.node_list["jicheng_right_attr_panel"]:SetActive(false)
	self.node_list["jicheng_left_attr_panel"]:SetActive(false)

	-- 左边属性
	local left_slot_data = self:JiChengLeftData()
	if left_slot_data then
		local item_cfg = ItemWGData.Instance:GetItemConfig(left_slot_data.item_id)
		if item_cfg then
			local cfg = TianShenWGData.Instance:GetEquipStrangeAttr(item_cfg.sub_type, left_slot_data.stren_level)

			if cfg then
				local cur_attr = AttributeMgr.GetAttributteByClass(cfg)
				AttributeMgr.FlushAttr(self.node_list, cur_attr, nil, "left_", nil, nil, attr_name_list)
	
				self.node_list["left_qianghua"].text.text = left_slot_data.stren_level
				self.node_list["jicheng_left_attr_panel"]:SetActive(true)
			end
		end
	end
	self.node_list["jicheng_left_tips"]:SetActive(left_slot_data == nil)

	-- 右边属性
	local right_slot_data = self:JiChengRightData()
	if left_slot_data and right_slot_data then
		local item_cfg = ItemWGData.Instance:GetItemConfig(right_slot_data.item_id)
		if item_cfg then
			local after_cfg = TianShenWGData.Instance:GetEquipStrangeAttr(item_cfg.sub_type, left_slot_data.stren_level)

			local cur_attr = AttributePool.AllocAttribute()
			local after_attr = AttributeMgr.GetAttributteByClass(after_cfg)
			AttributeMgr.FlushAttr(self.node_list, cur_attr, after_attr, "right_", nil, nil, attr_name_list)
		end

		self.node_list["right_add_qianghua"].text.text = left_slot_data.stren_level
		self.node_list["jicheng_right_attr_panel"]:SetActive(true)
	end
	self.node_list["jicheng_right_tips"]:SetActive(right_slot_data == nil)

end

-- 点击镶嵌孔
--is_default 是否是点击调用，true内部调用，false 点击调用
function ShenShiQiangHuaView:JiChengItemCallBack(slot_index, item, is_default)
	local click_data_index = 0
	-- 点击左边镶嵌孔
	if slot_index == SLOT_INDEX.LEFT then
		click_data_index = self.left_slot_data_index
	-- 点击右边镶嵌孔
	elseif slot_index == SLOT_INDEX.RIGHT then
		click_data_index = self.right_slot_data_index
	end 

	if click_data_index == 0 then
		self.select_slot_index = slot_index
		self:FlushSlotItem()
		self:FlushBagView()
		return
	end

    local btn_callback_event = {}
    btn_callback_event[1] = {btn_text = Language.TianShen.XieXia, callback = function()
        self:ItemXieXia(slot_index)
    end}
    TipWGCtrl.Instance:OpenItem(item:GetData(), ItemTip.FROM_TIANSHEN_JICHENG, nil, nil, btn_callback_event)
end

function ShenShiQiangHuaView:JiChengRightData()
	return self.jicheng_bag_data_list[self.right_slot_data_index]
end

function ShenShiQiangHuaView:JiChengLeftData()
	return self.jicheng_bag_data_list[self.left_slot_data_index]
end

-- 点击背包格子
function ShenShiQiangHuaView:ClickItemRender(item)
	local data = item:GetData()
	if not data then
		return
	end
	local data_index = data.data_index

	local flag, reason = TianShenWGData.Instance:JiChengContrastType(data, self.select_slot_index, self:JiChengLeftData(), self:JiChengRightData())
	if not flag then
		if reason and reason ~= "" then
			SysMsgWGCtrl.Instance:ErrorRemind(reason)
		end
		return
	end

	if self.select_slot_index == SLOT_INDEX.LEFT then
		self.left_slot_data_index = data_index
	elseif self.select_slot_index == SLOT_INDEX.RIGHT then
		self.right_slot_data_index = data_index
	end

	self:AutoChangeSelectIndex()
end

-- 自动选中一个没有物品的镶嵌孔并刷新
function ShenShiQiangHuaView:AutoChangeSelectIndex()
	if self.left_slot_data_index == 0 then
		self.select_slot_index = SLOT_INDEX.LEFT
	elseif self.right_slot_data_index == 0 then
		self.select_slot_index = SLOT_INDEX.RIGHT
	else
		self.select_slot_index = SLOT_INDEX.NONE
	end
	self:FlushSlotItem()
	self:FlushBagView()
end

-- 卸下镶嵌孔中的物品
function ShenShiQiangHuaView:ItemXieXia(slot_index)
	if slot_index == SLOT_INDEX.LEFT then
		self.left_slot_data_index = 0
	elseif slot_index == SLOT_INDEX.RIGHT then
		self.right_slot_data_index = 0
	end
	self:AutoChangeSelectIndex()
end

function ShenShiQiangHuaView:CheckJiChengEquipColor(item_id, show_msg)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg then return end

	local select_data = self:GetJiChengSelectData()
	if not select_data then return end

	if select_data.equip_color_max < item_cfg.color then
		if show_msg then
			local tianshen_cfg = TianShenWGData.Instance:GetImageModelByEquipColor(item_cfg.color)
			if not tianshen_cfg then return end
			local index = math.min(tianshen_cfg.series + 1, #PET_COLOR3B)
			local str = string.format(Language.TianShen.ShenShiChuanDai2, PET_COLOR3B[index], Language.Common.ColorName[index], PET_COLOR3B[item_cfg.color], Language.Common.ColorName[item_cfg.color])

			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
		return false
	end
	return true
end

function ShenShiQiangHuaView:OnClickJiCheng()
	if not self.base_cell_left:GetData() or 0 == self.base_cell_left:GetData().item_id or
		not self.base_cell_right:GetData() or 0 == self.base_cell_right:GetData().item_id then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.QianHuaTips8)
		return
	end

	local right_item_cfg = ItemWGData.Instance:GetItemConfig(self.base_cell_right:GetData().item_id)
	if not right_item_cfg then return end

	local select_data = self:GetJiChengSelectData()
	if not select_data then return end
	local fun = function ()
		if not self:CheckJiChengEquipColor(self.base_cell_right:GetData().item_id, self.select_tainshen_cfg.index) then
			return
		end

		local param2 = self.base_cell_left:GetData().bag_index
		local param3 = self.base_cell_left:GetRightBottomTextVisible() and 1 or 0
		local param4 = self.base_cell_right:GetData().bag_index
		local param5 = self.base_cell_right:GetRightBottomTextVisible() and 1 or 0
		TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type10, select_data.index, param2, param3, param4, param5)
	end


	local str = string.format(Language.TianShen.JiChengTips1, PET_COLOR3B[right_item_cfg.color], right_item_cfg.name, self.base_cell_left:GetData().grade_level, self.base_cell_left:GetData().stren_level)
	if self.base_cell_left:GetRightBottomTextVisible() then
		str = str .. Language.TianShen.JiChengTips2
	end
	TipWGCtrl.Instance:OpenAlertTips(str,fun)

end

function ShenShiQiangHuaView:OnClickTipsBtn()
	RuleTip.Instance:SetContent(Language.TianShen.JiChengDes, Language.TianShen.JiChengTitle, nil, nil, true)
end

-- 播放继承特效
function ShenShiQiangHuaView:ShowJiChengEffect()
	local effect_type = Ui_Effect.UI_jichengchenggong
	EffectManager.Instance:PlayCommonSuccessEffect(self.node_list["effect_container"], nil, nil, nil, nil, effect_type)
end

function ShenShiQiangHuaView:GetJiChengSelectData()
	return self.select_tainshen_cfg
end

-------------------------------------------------------------------------------------------------
InheritBagCell = InheritBagCell or BaseClass(BagCell)

function InheritBagCell:__init()
	self:SetIsShowTips(false)
	self:SetItemTipFrom(ItemTip.FROM_TIANSHEN_JICHENG)
end

-- 是否忽略点击
function InheritBagCell:IsIgnoreCKStorgeClick(index)
	return true
end

function InheritBagCell:OnFlush()
	self:SetDefaultEff()
	if self.data.is_use then
		self:ClearAllParts()
		self:SetItemIconValue(self.is_value)
		self:SetSelectEffect(self.is_select_effect)
		return
	end

	BagCell.OnFlush(self, self.data)
	if self:IsPutOn() then
		self:SetRightBottomTextVisible(true)
		self:SetRightBottomText(Language.TianShen.QianHuaTips7)
	else
        self:SetRightBottomTextVisible(false)
	end
end

--设置默认特效
function InheritBagCell:SetDefaultEff(enable, color)
	for k,v in pairs(BaseCell_Ui_Effect) do
		self:SetEffectEnable(false, v)
	end

	self:SetEffectEnable(false, effect_url1)

	if IsEmptyTable(self.data) or self.data.is_use then
		return
	end

	local view = ViewManager.Instance:GetView(GuideModuleName.ShenShiQiangHuaView)
	local left_slot_data = view:JiChengLeftData()
	local right_slot_data = view:JiChengRightData()
	local select_slot_index = view.select_slot_index
	local enable = TianShenWGData.Instance:JiChengContrastType(self.data, select_slot_index, left_slot_data, right_slot_data)

	if enable then
		self:SetEffectRootEnable(true)
		self:SetEffectEnable(true, effect_url1, nil, self.node_list.EffectRoot)
	end
end

function InheritBagCell:IsPutOn()
	return self.data.is_put_on
end
