CSActSubType ={															-- 合服活动子类型（与服务端对应）
	REQ_INFO = 0,

	CSA_SUB_TYPE_LOGIN_REWARD = 1,											-- 登录有礼
	CSA_SUB_TYPE_COLLECT_WORD = 2,											-- 集字有礼
	CSA_SUB_TYPE_CONSUME = 3,												-- 消费有礼
	CSA_SUB_TYPE_DOUBLE_EXP = 4,											-- 双倍经验
	CSA_SUB_TYPE_GUILD_BATTLE1 = 5,											-- 帮派争霸1
	CSA_SUB_TYPE_GUILD_BATTLE2 = 6,											-- 帮派争霸2
	CSA_SUB_TYPE_PANIC_BUY = 7,												-- 特惠秒杀
	CSA_SUB_TYPE_LIMIT_BUY = 8,												-- 限时云购
	CSA_SUB_TYPE_FB_DOUBLE = 9,												-- 副本双倍
	CSA_SUB_TYPE_RECHARGE_RANK = 10,										-- 充值排行
	CSA_SUB_TYPE_RECHARGE_CHOU = 11,										-- 充值抽抽

	TYPE_MAX = 12,
}



--子活动与客户端定义的活动号转换配置
CSActIdToSubType = {
	[ServerActClientId.CS_LOGIN_REWARD] = CSActSubType.CSA_SUB_TYPE_LOGIN_REWARD,				--登录有礼
	[ServerActClientId.CS_COLLECT_WORD] = CSActSubType.CSA_SUB_TYPE_COLLECT_WORD,				--集字有礼
	[ServerActClientId.CS_TYPE_CONSUME] = CSActSubType.CSA_SUB_TYPE_CONSUME,					--消费有礼
	[ServerActClientId.CS_TYPE_DOUBLE_EXP] = CSActSubType.CSA_SUB_TYPE_DOUBLE_EXP,				--双倍经验
	[ServerActClientId.CS_GUILD_BATTLE1] = CSActSubType.CSA_SUB_TYPE_GUILD_BATTLE1,				--帮派争霸1
	[ServerActClientId.CS_GUILD_BATTLE2] = CSActSubType.CSA_SUB_TYPE_GUILD_BATTLE2,				--帮派争霸2
	[ServerActClientId.CS_PANIC_BUY] = CSActSubType.CSA_SUB_TYPE_PANIC_BUY,						--特惠秒杀
	[ServerActClientId.CS_LIMIT_BUY] = CSActSubType.CSA_SUB_TYPE_LIMIT_BUY,						--限时云购
	[ServerActClientId.CS_FB_DOUBLE] = CSActSubType.CSA_SUB_TYPE_FB_DOUBLE,						--副本双倍
	[ServerActClientId.CS_RECHARGE_RANK] = CSActSubType.CSA_SUB_TYPE_RECHARGE_RANK,				--充值排行
	[ServerActClientId.CS_RECHARGE_CHOU] = CSActSubType.CSA_SUB_TYPE_RECHARGE_CHOU,				--充值抽抽
}

CSActState = {
	NO_START = 0,			-- 未开始
	OPEN = 1,				-- 进行中
	FINISH = 2,				-- 结束
}

CSActRankType = {
	INVALID = 0,
	QIANGGOU = 1,
	CHONGZHI = 2,
	CONSUME = 3,

	TYPE_MAX = 4,
}

ActCombineData = ActCombineData or BaseClass()

function ActCombineData:__init()
	if ActCombineData.Instance ~= nil then
		ErrorLog("[ActCombineData] attempt to create singleton twice!")
		return
	end
	self.combine_server_data = {}
	self.combine_person_info = {}
	self.activity_state_list = {}
	self.combine_roll_result = 0
	ActCombineData.Instance = self

	self.is_first_remind = true
end

function ActCombineData:__delete()
	ActCombineData.Instance = nil
end

function ActCombineData:SetCombineSubActivityState(protocol)
	self.activity_state_list = protocol.sub_activity_state_list

	local is_have_activity = false
	local finish_act_id_list = {}
	for k, v in pairs(self.activity_state_list) do
		local act_id = self:GetActIdBySubType(k)
		if nil ~= act_id then
			if v == CSActState.OPEN then
				is_have_activity = true
			else											--已结束或者未开启都当做已结束处理，不显示
				finish_act_id_list[act_id] = act_id
			end

			local time_cfg = self:GetCombineActTimeConfig(k)
			if time_cfg and time_cfg.is_open ~= 1 then
				finish_act_id_list[act_id] = act_id
			end
		end
	end
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.COMBINE_SERVER)
	if is_open and is_have_activity then --关了再开防止数据误删
		ServerActivityWGData.Instance:DeleteActOpeningType(ACTIVITY_TYPE.COMBINE_SERVER)
		ServerActivityWGData.Instance:AddActOpeningType(ACTIVITY_TYPE.COMBINE_SERVER)
	end

	for k, v in pairs(finish_act_id_list) do
		ServerActivityWGData.Instance:SetActFinish(v)
	end


	local open_act_cfg = ServerActivityWGData.Instance:GetShowOpenActList(true, true)
	if 0 == #open_act_cfg then						--开启合服活动 但是一个只活动都没有开
		Log("BUG ACTIVITY_TYPE.COMBINE_SERVER-->> 开启合服活动 但是一个子活动都没有开" )
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.COMBINE_SERVER, ACTIVITY_STATUS.CLOSE)
	end
end

function ActCombineData:GetActIdBySubType(sub_type)
	for k, v in pairs(CSActIdToSubType) do
		if v == sub_type then
			return k
		end
	end
end

function ActCombineData:SetCombineActivityInfo(protocol)
	self.combine_server_data.csa_first_charge_person_num = protocol.csa_first_charge_person_num
	self.combine_server_data.panic_buy_batch = protocol.panic_buy_batch
	self.combine_server_data.server_panic_buy_num_list = protocol.server_panic_buy_num_list
	self.combine_server_data.cloud_buy_batch = protocol.cloud_buy_batch
	self.combine_server_data.server_cloud_buy_num = protocol.server_cloud_buy_num
	self.combine_server_data.role_cloud_buy_record_list = protocol.role_cloud_buy_record_list
	self.combine_server_data.cloud_buy_record_count = protocol.cloud_buy_record_count
	self.combine_server_data.cloud_buy_top_reward_record_list = protocol.cloud_buy_top_reward_record_list
	self.combine_server_data.first_recharge_draw_recoard_count = protocol.first_recharge_draw_recoard_count
	self.combine_server_data.first_recharge_draw_record_list = protocol.first_recharge_draw_record_list
end

function ActCombineData:GetCombineActivityInfo()
	return self.combine_server_data
end

function ActCombineData:SetCombineRoleInfo(protocol)
	self.combine_person_info.csa_guild_battle_reward_type1 = protocol.csa_guild_battle_reward_type1									-- 合服活动 帮派战奖励类型
	self.combine_person_info.csa_guild_battle_reward_flag1 = protocol.csa_guild_battle_reward_flag1									-- 合服活动 帮派战奖励领取标记
	self.combine_person_info.csa_guild_battle_reward_type2 = protocol.csa_guild_battle_reward_type2									-- 合服活动 帮派战奖励类型2
	self.combine_person_info.csa_guild_battle_reward_flag2 = protocol.csa_guild_battle_reward_flag2									-- 合服活动 帮派战奖励领取标记2

	self.combine_person_info.csa_login_gift_fecth_reward_flag = protocol.csa_login_gift_fecth_reward_flag							-- 合服活动 登录有礼领取标记
	self.combine_person_info.csa_total_consume_fetch_reward_flag = protocol.csa_total_consume_fetch_reward_flag						-- 合服活动 消费有礼领取标记
	self.combine_person_info.csa_cloud_buy_times = protocol.csa_cloud_buy_times										-- 合服活动 云购购买次数

	self.combine_person_info.csa_panic_buy_time_list = protocol.csa_panic_buy_time_list

	self.combine_person_info.combine_server_days = protocol.combine_server_days														-- 合服天数
	self.combine_person_info.csa_total_consume_gold_consume_num = protocol.csa_total_consume_gold_consume_num						-- 合服活动 消费有礼消费数量
	self.combine_person_info.csa_first_chongzhi_groupbuy_fetch_flag = protocol.csa_first_chongzhi_groupbuy_fetch_flag				-- 合服首充团购

	self.combine_person_info.csa_recharge_draw_count = protocol.csa_recharge_draw_count												-- 已抽奖次数
	self.combine_person_info.csa_recharge_draw_hit_index = protocol.csa_recharge_draw_hit_index										-- 抽中的索引

	local function cacheActRewardData(act_id, reward_flag, can_reward_flag)
		can_reward_flag = can_reward_flag ~= nil and  bit:d2b(can_reward_flag) or nil
		ServerActivityWGData.Instance:CacheActRewardData(act_id, bit:d2b(reward_flag), nil, can_reward_flag)
	end

	cacheActRewardData(ServerActClientId.CS_GUILD_BATTLE1, protocol.csa_guild_battle_reward_flag1)
	cacheActRewardData(ServerActClientId.CS_GUILD_BATTLE2, protocol.csa_guild_battle_reward_flag2)
	cacheActRewardData(ServerActClientId.CS_LOGIN_REWARD, protocol.csa_login_gift_fecth_reward_flag)
	cacheActRewardData(ServerActClientId.CS_TYPE_CONSUME, protocol.csa_total_consume_fetch_reward_flag)

	-- Remind.Instance:DoRemind(RemindId.act_hotsell)
	-- Remind.Instance:DoRemind(RemindId.act_consume_gift)
	-- -- Remind.Instance:DoRemind(RemindId.act_collection)
	-- Remind.Instance:DoRemind(RemindId.csa_login_gift)
	-- Remind.Instance:DoRemind(RemindId.lucky_buy)
	-- -- Remind.Instance:DoRemind(RemindId.cs_group_buying)

	if self.is_first_remind then
		self.is_first_remind = false
		local remind_item_list = self:GetActZiTieExchangeRemindList()
		-- Remind.Instance:RegisterOneItemRemind(remind_item_list, RemindId.act_collection)
	end
end

function ActCombineData:GetCombineRoleInfo()
	return self.combine_person_info
end

function ActCombineData:GetCombineActivityConfigBy(act_id)
	if act_id == nil then return end
	local act_client_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	if act_client_cfg ~= nil and act_client_cfg.big_type ~= nil then
		return combine_cfg[act_client_cfg.big_type]
	end
end

function ActCombineData:GetCombineActivityOtherConfig()
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	return combine_cfg.other[1]
end

--合服兑换配置
function ActCombineData:GetCombineExchangeCfg()
	local config = ServerActivityWGData.Instance:GetCurrentCombineActivityConfig()
	if config and next(config) ~= nil then
		return config.combine_server_convert
	end
end

--同步摇奖结果
function ActCombineData:SetCombineRollResult(protocol)
	self.combine_roll_result = protocol.ret_seq
end

--幸运抽奖
function ActCombineData:GetLotteryDrawData()
	local roll_item_list = {}
	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local cfg = config.lucky_draw
	for i = 0, #cfg do
		if nil ~= cfg[i] then
			table.insert(roll_item_list, cfg[i])
		end
	end
	return roll_item_list
end

--幸运转盘
function ActCombineData:GetCombineLuckyRollData()
	local roll_item_list = {}
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local roll_cfg = combine_cfg.roll_cfg
	for i = 0, #roll_cfg do
		if nil ~= roll_cfg[i] then
			table.insert(roll_item_list, roll_cfg[i])
		end
	end
	return roll_item_list
end

--获取时间配置
function ActCombineData:GetCombineActTimeConfig(sub_type)
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local activity_time = combine_cfg.activity_time
	local time_cfg = nil
	for k, v in pairs(activity_time) do
		if v.sub_type == sub_type then
			time_cfg = v
		end
	end
	return time_cfg
end

--仙盟战攻城战开启时间
function ActCombineData:GetCombineActOpenTime(activity_type, open_day)
	local start_time = TimeWGCtrl.Instance:GetServerRealCombineTime()
	local act_open_day_time = start_time + 60 * 60 * 24 * (open_day - 1)
	local format_time = os.date("*t", act_open_day_time)
	local config = DailyWGData.Instance:GetActivityConfig(activity_type)
	local open_time_t = Split(config.open_time, "-")
	local time_t = Split(open_time_t[1], ":")
	local open_hour, open_minute = time_t[1], time_t[2]
	local start_real_time = os.time{year=format_time.year, month=format_time.month, day=format_time.day, hour=open_hour, min = open_minute, sec=0}
	local time_left = start_real_time - TimeWGCtrl.Instance:GetServerTime()

	if time_left <= 0 then
		local act_state = ActivityWGData.Instance:GetActivityStatuByType(activity_type)
		if nil ~= act_state then
			return ActivityWGData.GetActivityStatusName(act_state.status)
		else
			return ActivityWGData.GetActivityStatusName(Language.OpenServer.RollActivityEnd)
		end
	else
		return TimeUtil.Format2TableDHM(time_left)
	end
end

--获取活动剩余时间
function ActCombineData:GetCombineActTimeLeft(sub_type)
	--[[
	if sub_type == CSActSubType.XIANMENGZHAN then
		return self:GetCombineActOpenTime(ACTIVITY_TYPE.XIANMENGZHAN, 2)
	elseif sub_type == CSActSubType.GONGCHENGZHAN then
		return self:GetCombineActOpenTime(ACTIVITY_TYPE.GONGCHENGZHAN, 3)
	end
	]]
	local start_time = TimeWGCtrl.Instance:GetServerRealCombineTime()
	local time_cfg = self:GetCombineActTimeConfig(sub_type)
	--格式化剩余时间
	local time_left = 0
	if nil ~= time_cfg and nil ~= start_time then
		local act_end_time = start_time + (time_cfg.end_day - 1) * 60 * 60 * 24
		local format_time = os.date("*t", act_end_time)
		local end_hour, end_minute = math.floor(time_cfg.end_time / 100), time_cfg.end_time % 100
		local end_real_time = os.time{year=format_time.year, month=format_time.month, day=format_time.day, hour=end_hour, min = end_minute, sec=0}

		time_left = end_real_time - TimeWGCtrl.Instance:GetServerTime()
		if time_left < 0 then
			time_left = 0
		end
	end
	return TimeUtil.Format2TableDHM(time_left)
end

function ActCombineData:GetCombineActEndTime(sub_type)
	local start_time = TimeWGCtrl.Instance:GetServerRealCombineTime()
	local time_cfg = self:GetCombineActTimeConfig(sub_type)
	local end_real_time = 0
	--格式化剩余时间
	local time_left = 0
	if nil ~= time_cfg and nil ~= start_time then
		local act_end_time = start_time + (time_cfg.end_day - 1) * 60 * 60 * 24
		local format_time = os.date("*t", act_end_time)
		local end_hour, end_minute = math.floor(time_cfg.end_time / 100), time_cfg.end_time % 100
		end_real_time = os.time{year=format_time.year, month=format_time.month, day=format_time.day, hour=end_hour, min = end_minute, sec=0}
	end
	return end_real_time
end

--获取活动奖励
function ActCombineData:GetCombineRankItemData(sub_type)
	local item_data_list = {}
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local rank_reward = combine_cfg.rank_reward
	for k, v in pairs(rank_reward) do
		if v.sub_type == sub_type then
			for i = 1, v.rank_limit do
				local item = v["reward_item_" .. i]
				table.insert(item_data_list, item)
			end
			return item_data_list
		end
	end
end

--仙盟争霸奖励
function ActCombineData:GetChengZhuReward()
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local other_cfg = combine_cfg.other[1]
	return {other_cfg.gcz_chengzhu_reward, other_cfg.gcz_camp_reward}
end

--首充团购
function ActCombineData:GetGroupBuyReward()
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local csa_firstchongzhi_groupbuy = combine_cfg.csa_firstchongzhi_groupbuy
	local today_cfg = {}
	for k,v in ipairs(csa_firstchongzhi_groupbuy) do
		if self.combine_person_info.combine_server_days == v.combine_server_day then
			table.insert(today_cfg, v)
		end
	end
	return today_cfg
end

--王城争霸奖励
function ActCombineData:GetMengZhuReward()
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local other_cfg = combine_cfg.other[1]
	return {other_cfg.xmz_mengzhu_reward, other_cfg.xmz_camp_reward}
end

function ActCombineData:GetChengZhuRoleId()
	return self.combine_server_data.csa_gcz_winner_roleid
end

function ActCombineData:GetMengZhuRoleId()
	return self.combine_server_data.csa_xmz_winner_roleid
end

function ActCombineData.GetCombineServerName(sub_type)
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local activity_time = combine_cfg.activity_time
	for k,v in pairs(activity_time) do
		if v.sub_type == sub_type then
			return v.name
		end
	end
	return ""
end

function ActCombineData:GetItemDataList(act_id)
	local sub_type = CSActIdToSubType[act_id]
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local rank_reward = combine_cfg.rank_reward
	local item_config = nil

	for k, v in pairs(rank_reward) do
		if v.sub_type == sub_type then
			item_config = v
		end
	end
	if nil == item_config then
		return
	end

	local act_client_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if nil == act_client_cfg then
		return
	end
	local is_unpack_gift = (1 == act_client_cfg.is_unpack_gift)

	local data_list = {}
	for i = 1, 3 do
		local data = {}
		data.act_id = act_id
		data.is_geted = false
		data.is_can_get = false
		data.reward_type = 0
		data.reward_seq = i - 1

		local content = act_client_cfg.item_desc
		local target_value = i
		content = XmlUtil.RelaceTagContent(content, "limit_value_1", target_value)
		local rank_limit = item_config.rank_limit
		content = XmlUtil.RelaceTagContent(content, "limit_value_2", rank_limit)
		data.instruction = content

		data.btn_name = ""
		local item = item_config["reward_item_" .. i]
		data.item_list = ServerActivityWGData.Instance:GetShowRewardListByCfg({item}, is_unpack_gift)
		data.item_func = nil
		table.insert(data_list, data)
	end

	return data_list
end

-- function ActCombineData:GetRemindNum(remind_id)
-- 	if RemindId.cs_login_gift == remind_id then
-- 		return self:GetLoginGiftItemRemind(ServerActClientId.CS_LOGIN_REWARD)
-- 	end
-- 	return 0
-- end

--合服活动登录有礼提示
function ActCombineData:GetLoginGiftItemRemind(act_id)
	if self.combine_person_info == nil then return 0 end
	local login_gift_cfg = self:GetCombineActivityConfigBy(act_id)
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(act_id)

	local day_data = nil
	for k,v in ipairs(login_gift_cfg)do
		if v.combine_days == self.combine_person_info.combine_server_days then
			day_data = v
			break
		end
	end

	if day_data ~= nil and info ~= nil and info.reward_flag[32 - day_data.seq] == 0 then
		return 1
	end

	return 0
end


--合服活动登录有礼
function ActCombineData:GetActLoginGiftData(act_id)
	--屏蔽活动
	-- if nil == self.combine_person_info then
	-- 	return nil
	-- end

	-- local login_days = self.combine_person_info.combine_server_days
	-- local login_gift_cfg = self:GetCombineActivityConfigBy(act_id)
	-- local info = ServerActivityWGData.Instance:GetCacheActRewardData(act_id)
	-- local act_other_cfg = self:GetCombineActivityOtherConfig()

	-- local common_data = {}
	-- common_data.act_id = act_id
	-- common_data.login_days = login_days
	-- common_data.login_gift_cfg = login_gift_cfg
	-- common_data.reward_type = ACTIVITY_TYPE.COMBINE_SERVER
	-- common_data.reward_flag = info.reward_flag
	-- common_data.min_level = act_other_cfg.login_gift_need_level
	-- common_data.item_func = function()
	-- 	ServerActivityWGCtrl.Instance:SendCSARoleOperaReq(CSActSubType.CSA_SUB_TYPE_LOGIN_REWARD)
	-- end

	-- return common_data
end

--合服活动 集字
function ActCombineData:GetActZiTieExchangeData(act_id)
	--屏蔽活动
	-- local zitie_exchange_cfg = self:GetCombineActivityConfigBy(act_id)

	-- local common_data = {}
	-- common_data.word_exchange = __TableCopy(zitie_exchange_cfg)
	-- for k,v in ipairs(common_data.word_exchange)do
	-- 	v.btn_click_fun = function (data)
	-- 		if data ~= nil then
	-- 			ServerActivityWGCtrl.Instance:SendCSARoleOperaReq(CSActSubType.CSA_SUB_TYPE_COLLECT_WORD, data.seq)
	-- 		end
	-- 	end
	-- end

	-- return common_data
end

function ActCombineData:GetActZiTieExchangeRemindList()
	local zitie_exchange_cfg = self:GetCombineActivityConfigBy(ServerActClientId.CS_COLLECT_WORD)

	local item_key = {}
	local item_list = {}
	for k,v in ipairs(zitie_exchange_cfg)do
		for i = 1, 4 do
			if 0 ~= v["item_id_" .. i] and item_key[v["item_id_" .. i]] == nil then
				item_key[v["item_id_" .. i]] = 1
				table.insert(item_list, v["item_id_" .. i])
			end
		end
	end
	return item_list
end

function ActCombineData:GetCommonOneViewData(act_id)
	if self.combine_person_info == nil then return end

	local act_client_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if nil == act_client_cfg then
		return
	end
	local is_unpack_gift = (1 == act_client_cfg.is_unpack_gift)

	if act_id == ServerActClientId.CS_TYPE_CONSUME then
		local act_item_list = {}
		local act_cfg = self:GetCombineActivityConfigBy(act_id)
		local today_recharge = self.combine_person_info.csa_total_consume_gold_consume_num
		local info = ServerActivityWGData.Instance:GetCacheActRewardData(act_id)
		for k,v in ipairs(act_cfg)do
			local item_data = __TableCopy(v)
			local is_lingqu = info.reward_flag[32 - item_data.seq] == 1
			local is_dacheng = today_recharge >= item_data.need_consume_gold

			item_data.item_data_list = ServerActivityWGData.Instance:GetShowRewardListByCfg(item_data.reward_item, is_unpack_gift)
			item_data.sort = 0
			if is_lingqu then
				item_data.sort = 0
				item_data.btn_text = Language.Common.YiLingQu
				item_data.btn_enabled = false
			elseif is_dacheng then
				item_data.sort = 3
				item_data.btn_text = Language.Common.LingQu
				item_data.btn_enabled = true
				item_data.btn_click_fun = function (data)
					ServerActivityWGCtrl.Instance:SendCSARoleOperaReq(CSActSubType.CSA_SUB_TYPE_CONSUME, data.seq)
				end
			else
				item_data.sort = 2
				item_data.btn_text = Language.Common.Recharge
				item_data.btn_enabled = true
				item_data.btn_click_fun = function (data)
					if data ~= nil then
						FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge, "recharge_cz")
					end
				end
			end
			item_data.item_text = string.format(Language.Activity.RandTotalConsumeItemDesc, item_data.need_consume_gold, today_recharge, item_data.need_consume_gold)
			table.insert(act_item_list, item_data)
		end

		table.sort(act_item_list, function(a, b) return a.sort > b.sort end)
		return act_item_list
	elseif act_id == ServerActClientId.CS_GUILD_BATTLE1 or act_id == ServerActClientId.CS_GUILD_BATTLE2 then
		local reward_type1 = -1
		local btn_click_fun = nil
		if act_id == ServerActClientId.CS_GUILD_BATTLE1 then
			reward_type1 = self.combine_person_info.csa_guild_battle_reward_type1
			btn_click_fun = function (data)
				ServerActivityWGCtrl.Instance:SendCSARoleOperaReq(CSActSubType.CSA_SUB_TYPE_GUILD_BATTLE1, data.seq)
			end
		elseif act_id == ServerActClientId.CS_GUILD_BATTLE2 then
			reward_type1 = self.combine_person_info.csa_guild_battle_reward_type2
			btn_click_fun =  function (data)
				ServerActivityWGCtrl.Instance:SendCSARoleOperaReq(CSActSubType.CSA_SUB_TYPE_GUILD_BATTLE2, data.seq)
			end
		end

		local act_item_list = {}
		local act_cfg = self:GetCombineActivityConfigBy(act_id)
		local info = ServerActivityWGData.Instance:GetCacheActRewardData(act_id)

		for k,v in ipairs(act_cfg)do
			local item_data = __TableCopy(v)

			item_data.item_data_list = ServerActivityWGData.Instance:GetShowRewardListByCfg({item_data.reward_item}, is_unpack_gift)

			item_data.item_text = Language.OpenServer.GuildContendLingquDesc[item_data.type]
			if info.reward_flag[32] == 0 and item_data.type == reward_type1 then
				item_data.btn_text = Language.Common.LingQu
				item_data.btn_enabled = true
				item_data.btn_click_fun = btn_click_fun
			elseif item_data.type == reward_type1 then
				item_data.btn_text = Language.Common.YiLingQu
				item_data.btn_enabled = false
			else
				item_data.btn_text = Language.Common.WEIDACHENG
				item_data.btn_enabled = false
			end
			item_data.item_text_rich = true
			table.insert(act_item_list, item_data)
		end
		return act_item_list
	end
end

function ActCombineData:GetCommonTwoViewData(act_id)
	if act_id == ServerActClientId.CS_FB_DOUBLE then
		local act_data = {}
		act_data.img_res_path = ResPath.GetOpenserver("cs_fb_double_bg")
		act_data.btn_text = Language.Boss.GoToKillBtn
		act_data.btn_click_fun = function()
			FunOpen.Instance:OpenViewByName(GuideModuleName.FuBenPanel)
		end
		return act_data
	elseif act_id == ServerActClientId.CS_TYPE_DOUBLE_EXP then
		local act_data = {}
		act_data.img_res_path = ResPath.GetOpenserver("cs_type_double_exp_bg")
		act_data.btn_text = Language.Boss.GoToKillBtn
		act_data.btn_click_fun = function()
			FunOpen.Instance:OpenViewByName(GuideModuleName.FuBenPanel)
		end
		return act_data
	end
end

function ActCombineData:GetCommonTreeViewData(act_id)
	if act_id == ServerActClientId.CS_RECHARGE_RANK then
		local act_cfg = self:GetCombineActivityConfigBy(act_id)
		local rank_index = 1
		local act_data = {}
		local item_data = nil
		local mingci_lisg = {}
		local user_list = RankWGData.Instance:GetRankData(RankKind.Person, PersonRankType.PERSON_RANK_TYPE_CSA_RECHARGE_TOTAL)

		for k,v in ipairs(act_cfg)do
			item_data = {}
			item_data.item_data_list = v.reward_item
			if item_data.item_data_list[0] ~= nil then
				table.insert(item_data.item_data_list, 1, item_data.item_data_list[0])
				item_data.item_data_list[0] = nil
			end
			item_data.rich_item_text = string.format(Language.Activity.RechargeRankItemText1, v.min_chongzhi)
			if v.min_rank == v.max_rank then
				item_data.lbl_item_text_2 = string.format(Language.Activity.RechargeRankItemText2_1, v.min_rank)
			else
				item_data.lbl_item_text_2 = string.format(Language.Activity.RechargeRankItemText2, v.min_rank, v.max_rank)
			end

			item_data.role_list = {}
			for i = v.min_rank, v.max_rank do
				if user_list[rank_index] ~= nil and user_list[rank_index].rank_value >= v.min_chongzhi then
					table.insert(item_data.role_list, {text_t = user_list[rank_index].user_name, text_w = user_list[rank_index].rank_value})
					rank_index = rank_index + 1
				else
					break
				end
			end

			item_data.item_text_3_is_btn = false
			if #item_data.role_list <= 0 then
				item_data.rich_item_text_3 = ""
			else
				item_data.item_text_3_is_btn = true
				item_data.rich_item_text_3 = Language.Activity.RechargeRankItemText3
			end
			item_data.top_text = {Language.Rank.MingZi ,Language.Common.Gold}
			table.insert(act_data, item_data)
		end

		return act_data, Language.Activity.RechargeRankTopText
	end
end

-- 消费排行
function ActCombineData:GetDayConsumeCfg(act_id)
	local data_list = {}
	if nil == self.combine_person_info then
		return data_list
	end

	local server_day = self.combine_person_info.combine_server_days

	local act_client_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if nil == act_client_cfg then
		return data_list
	end

	local consume_cfg = nil
	local big_type = ""
	local big_type_t = Split(act_client_cfg.big_type, "/")
	if big_type_t[1] then
		local is_ios_plat = false
		local p_str = is_ios_plat and "_ios" or ""
		big_type_t[1] = string.gsub(big_type_t[1], "@ios", p_str)
	end
	if nil ~= big_type_t[2] then
		local k1, k2 = big_type_t[1], big_type_t[2]
		if nil ~= ConfigManager.Instance:GetAutoConfig(k1)[k2] then
			consume_cfg = ConfigManager.Instance:GetAutoConfig(k1)[k2]
		end
	end
	if consume_cfg == nil then return data_list end
	for k,v in pairs(consume_cfg) do
		if v.combine_server_day == server_day then
			table.insert(data_list, v)
		end
	end
	return data_list
end
-----------------------特惠秒杀-----------------------------------
-- 合服天数
function ActCombineData:GetCombineServerDay()
	local elapse_time = TimeWGCtrl.Instance:GetServerTime() - TimeWGCtrl.Instance:GetServerRealCombineTime()
	local tab = TimeUtil.Format2TableDHMS(elapse_time)
	return tab.day
end

function ActCombineData:GetMaxPanicDay(act_id)
	local panic_buy_cfg = __TableCopy(self:GetCombineActivityConfigBy(act_id))
	local open_game_day = TimeWGCtrl.Instance:GetCurOpenServerDay() - self:GetCombineServerDay()
	for i, v in ipairs(panic_buy_cfg) do
		if open_game_day <= v.opengame_day then
			return v.opengame_day
		end
	end
end

function ActCombineData:GetPanicBuyCfg(act_id)
	local panic_buy_cfg = __TableCopy(self:GetCombineActivityConfigBy(act_id))
	local combine_act_cfg = self:GetCombineActivityInfo()
	local role_info = self:GetCombineRoleInfo().csa_panic_buy_time_list
	local panic_buy_list = {}
	local max_day = self:GetMaxPanicDay(act_id)
	for i, v in ipairs(panic_buy_cfg) do
		if v.opengame_day == max_day and v.batch == combine_act_cfg.panic_buy_batch then
			v.role_buy_time = role_info[v.seq + 1]
			v.all_buy_time = combine_act_cfg.server_panic_buy_num_list[v.seq + 1]
			table.insert(panic_buy_list, v)
		end
	end
	return panic_buy_list
end

function ActCombineData:GetPanicNextCfg(act_id)
	local panic_buy_cfg = __TableCopy(self:GetCombineActivityConfigBy(act_id))
	local combine_act_cfg = self:GetCombineActivityInfo()
	local panic_buy_list = {}
	local max_day = self:GetMaxPanicDay(act_id)
	for i, v in ipairs(panic_buy_cfg) do
		if v.opengame_day == max_day and v.batch == combine_act_cfg.panic_buy_batch + 1 then
			table.insert(panic_buy_list, v)
		end
	end
	return panic_buy_list
end

function ActCombineData:GetPanicNextTime(need_cal)
	if need_cal then
		local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
		local combine_act_cfg = self:GetCombineActivityInfo()
		local flush_cfg = combine_cfg.panic_buy_flush
		for k, v in pairs(flush_cfg) do
			if v.batch == combine_act_cfg.panic_buy_batch then
				local time = tonumber(v.flush_time)
				local now_time = TimeWGCtrl.Instance:GetServerTimeFormat()
				now_time.hour = time / 100
				now_time.min = time % 100
				self.timestamp = tonumber(os.time(now_time))
				return self.timestamp
			end
		end
		return 0
	end
	return self.timestamp or 0
end

-- 获取摇奖配置信息
function ActCombineData:GetHotSellCfg(act_id)
	local panic_buy_cfg = __TableCopy(self:GetCombineActivityConfigBy(act_id))
	local role_item_list = {}

	if panic_buy_cfg then
		for i = 1, 8 do
			if panic_buy_cfg[i] then
				role_item_list[i] = panic_buy_cfg[i]
			end
		end
	end

	return role_item_list
end

function ActCombineData:SetHotSellRewardCfg(list)
	self.hotsell_reward = list
end

function ActCombineData:GetHotSellRewardCfg()
	return self.hotsell_reward
end

function ActCombineData:GetHotSellWinnerList()
	local list = {}
	for i=1,self.combine_server_data.first_recharge_draw_recoard_count do
		table.insert(list, self.combine_server_data.first_recharge_draw_record_list[i])
	end
	return list
end

-- 获取摇奖配置信息
function ActCombineData:GetHotSellCfg(act_id)
	local panic_buy_cfg = __TableCopy(self:GetCombineActivityConfigBy(act_id))
	local role_item_list = {}

	if panic_buy_cfg then
		for i = 1, 8 do
			if panic_buy_cfg[i] then
				role_item_list[i] = panic_buy_cfg[i]
			end
		end
	end

	return role_item_list
end

function ActCombineData:SetHotSellRewardCfg(list)
	self.hotsell_reward = list
end

function ActCombineData:GetHotSellRewardCfg()
	return self.hotsell_reward
end

function ActCombineData:GetHotSellWinnerList()
	local list = {}
	for i=1,self.combine_server_data.first_recharge_draw_recoard_count do
		table.insert(list, self.combine_server_data.first_recharge_draw_record_list[i])
	end
	return list
end


--限时云购购买次数
function ActCombineData:GetLuckyBuyVipCfg()
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local vip_level = RoleWGData.Instance.role_vo.vip_level
	local buy_times = 0
	for k,v in pairs(combine_cfg.cloud_buy_times) do
		if v.vip_level == vip_level then
			buy_times = v.buy_times
		end
	end
	return buy_times
end

--限时云购普通奖励
function ActCombineData:GetLuckyBuyCommonCfg(batch)
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.COMBINE_SERVER)
	local openday_time = TimeWGCtrl.Instance:GetServerRealStartTime()
	local open_day  = math.ceil((activity_status.start_time  - openday_time)/ (3600*24))
	local cfg = {}
	local day = nil
	for k,v in pairs(combine_cfg.cloud_buy_item) do
		if v and (nil == day or v.opengame_day == day) and open_day <= v.opengame_day then
			day = v.opengame_day
			table.insert(cfg, v)
		end
	end
	local cfg_two = {}
	for k,v in pairs(cfg) do
		if v.batch == batch then
			table.insert(cfg_two, v)
		end
	end
	return cfg_two
end

--限时云购特殊奖励
function ActCombineData:GetLuckyBuySpecialCfg(batch)
	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.COMBINE_SERVER)
	local openday_time = TimeWGCtrl.Instance:GetServerRealStartTime()
	local open_day  = math.ceil((activity_status.start_time  - openday_time)/ (3600*24))

	local cfg = {}
	local day = nil
	for k,v in pairs(combine_cfg.cloud_buy_flush) do
		if v and (nil == day or v.opengame_day == day) and open_day <= v.opengame_day then
			day = v.opengame_day
			table.insert(cfg, v)
		end
	end
	local cfg_two = {}
	for k,v in pairs(cfg) do
		if v.batch == batch then
			table.insert(cfg_two, v)
		end
	end
	return cfg_two
end

--限时云购排行
function ActCombineData:GetLuckyBuyRank()
	local list = {}
	for i=1,self.combine_server_data.cloud_buy_record_count do
		table.insert(list, self.combine_server_data.role_cloud_buy_record_list[i])
	end
	return list
end