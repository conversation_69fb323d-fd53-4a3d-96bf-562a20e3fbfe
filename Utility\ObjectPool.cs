using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

internal class ObjectPool<T> where T : new()
{
    private readonly Stack<T> m_Stack = new Stack<T>();
    private readonly UnityAction<T> m_ActionOnRelease;
    private int newCount = 0;

    public ObjectPool(UnityAction<T> actionOnRelease)
    {
        m_ActionOnRelease = actionOnRelease;
    }

    public T Get()
    {
        newCount++;
        T element;
        if (m_Stack.Count == 0)
        {
            element = new T();
        }
        else
        {
            element = m_Stack.Pop();
        }

        return element;
    }

    public void Release(T element)
    {
        if (m_Stack.Count > 0 && ReferenceEquals(m_Stack.Peek(), element))
            Debug.LogError("Internal error. Trying to destroy object that is already released to pool.");
        if (m_ActionOnRelease != null)
            m_ActionOnRelease(element);
        m_Stack.Push(element);
        --newCount;
    }
}
