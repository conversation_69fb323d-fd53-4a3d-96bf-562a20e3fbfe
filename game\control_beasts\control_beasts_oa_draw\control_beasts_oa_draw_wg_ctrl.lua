require("game/control_beasts/control_beasts_oa_draw/control_beasts_oa_draw_wg_data")

ControlBeastsOADrawWGCtrl = ControlBeastsOADrawWGCtrl or BaseClass(BaseWGCtrl)

function ControlBeastsOADrawWGCtrl:__init()
	if ControlBeastsOADrawWGCtrl.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[ControlBeastsOADrawWGCtrl] attempt to create singleton twice!")
		return
	end

	ControlBeastsOADrawWGCtrl.Instance = self
    self.data = ControlBeastsOADrawWGData.New()

	self:RegisterProtocol(SCOABeastDrawItemInfo, "OnSCOABeastDrawItemInfo")
	self:RegisterProtocol(SCOABeastDrawItemUpdate, "OnSCOABeastDrawItemUpdate")
	self:RegisterProtocol(SCOABeastDrawResult, "OnSCOABeastDrawResult")
	self:RegisterProtocol(SCOABeastDrawRecordInfo, "OnSCOABeastDrawRecordInfo")
	self:RegisterProtocol(SCOABeastDrawRecordAdd, "OnSCOABeastDrawRecordAdd")

	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
end

function ControlBeastsOADrawWGCtrl:__delete()
    ControlBeastsOADrawWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

	if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end
end

---------------------------------运营活动幻兽抽奖STASRT----------------------------------
function ControlBeastsOADrawWGCtrl:SendOABeastDrawOpera(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_OPERA_REQ_CS
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function ControlBeastsOADrawWGCtrl:OnSCOABeastDrawItemInfo(protocol)
	-- print_error("-------protocol--------",protocol)
    self.data:SetOABeastDrawItemInfo(protocol)
    RemindManager.Instance:Fire(RemindName.BeastsCulture)
    RemindManager.Instance:Fire(RemindName.BeastsPrizeDraw)
    ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsPrizeDrawWGView)
end

function ControlBeastsOADrawWGCtrl:OnSCOABeastDrawItemUpdate(protocol)
	-- print_error("-------protocol--------",protocol)
    self.data:OABeastDrawItemUpdate(protocol)
    RemindManager.Instance:Fire(RemindName.BeastsCulture)
    RemindManager.Instance:Fire(RemindName.BeastsPrizeDraw)
    ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsPrizeDrawWGView)
end

function ControlBeastsOADrawWGCtrl:OnSCOABeastDrawResult(protocol)
	-- print_error("-------protocol--------",protocol)
    ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsPrizeDrawWGView, 0, "oa_draw_result", protocol)
	RemindManager.Instance:Fire(RemindName.BeastsCulture)
end

function ControlBeastsOADrawWGCtrl:OnSCOABeastDrawRecordInfo(protocol)
	-- print_error("-------protocol--------",protocol)
    self.data:SetOABeastDrawRecordInfo(protocol)
end

function ControlBeastsOADrawWGCtrl:OnSCOABeastDrawRecordAdd(protocol)
	-- print_error("-------protocol--------",protocol)
    self.data:SetOABeastDrawRecordAdd(protocol)
end
-----------------------------------运营活动幻兽抽奖END-----------------------------------

function ControlBeastsOADrawWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_OPERA_REQ_CS then
		RemindManager.Instance:Fire(RemindName.BeastsCulture)
		RemindManager.Instance:Fire(RemindName.BeastsPrizeDraw)
		ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsPrizeDrawWGView)
		MainuiWGCtrl.Instance:FlushView(0, "BeastsContractBtnFlush")
    end
end