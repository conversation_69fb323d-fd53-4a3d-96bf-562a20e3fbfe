require("game/serveractivity/huanlezadan/huanlezadan_wg_data")
require("game/serveractivity/huanlezadan/huanlezadan_view")
require("game/serveractivity/huanlezadan/huanlezadan_quanfu_view")

HuanlezadanWGCtrl = HuanlezadanWGCtrl or BaseClass(BaseWGCtrl)

function HuanlezadanWGCtrl:__init()
	if HuanlezadanWGCtrl.Instance then
        error("[HuanlezadanWGCtrl]:Attempt to create singleton twice!")
	end
	HuanlezadanWGCtrl.Instance = self

	self.data = HuanlezadanWGData.New()
	self.view = HuanlezadanView.New()
	self.quanFuView = HuanlezadanQuanFuView.New()

	self:RegisterAllProtocals()

end

function HuanlezadanWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if nil ~= self.quanFuView then
		self.quanFuView:DeleteMe()
		self.quanFuView = nil
	end
	if self.mainui_open_event then
		GlobalEventSystem:UnBind(self.mainui_open_event)
		self.mainui_open_event = nil
	end
	HuanlezadanWGCtrl.Instance = nil
end

function HuanlezadanWGCtrl:RegisterAllProtocals()
	-- 注册协议
	self:RegisterProtocol(SCCrossRASmashedEggInfo, "OnSCCrossRASmashedEggInfo")
	-- 注册一个提醒
	-- Remind.Instance:RegisterOneRemind(RemindId.huanlezadan, BindTool.Bind1(self.CheckHuanlezadanRemind, self))
	self.mainui_open_event = self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
end

function HuanlezadanWGCtrl:MainuiOpenCreate()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG) then
		return
	end
	self:SendReq(RA_CS_CROSS_RA_SMASHED_EGG_INFO.CS_CROSS_RA_SMASHED_EGG_INFO)
	RankWGCtrl.Instance:SendCrossGetRankListReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG)
end

-- 客户端收到协议后
function HuanlezadanWGCtrl:OnSCCrossRASmashedEggInfo(protocol)
	self.data:SetSCCrossRASmashedEggInfo(protocol)
	self.view:Flush()
	self.quanFuView:Flush()
	-- 根据ACTIVITY_TYPE来获取活动状态
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG) or {}
	-- 判断活动是否开启
	-- if act_info.status == ACTIVITY_STATUS.OPEN then
	-- 	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.HUANLEZADANRANK, ACTIVITY_STATUS.OPEN)
	-- else
	-- 	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.HUANLEZADANRANK, ACTIVITY_STATUS.CLOSE)
	-- end
	-- Remind.Instance:DoRemind(RemindId.huanlezadan)
end

function HuanlezadanWGCtrl:Open()
	self.view:Open()
end

function HuanlezadanWGCtrl:OpenQuanFuView()
	self.quanFuView:Open()
end

-- 发送请求给服务器
function HuanlezadanWGCtrl:SendReq(type, p1, p2, p3)

	local param_t = {
		activity_type = ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG,
		opera_type = type,
		param_1 = p1,
		param_2 = p2,
		param_3 = p3,
	}
	CrossServerWGCtrl.Instance:SendCrossRandActivityOperaReq(param_t)
end

-- 检查红点
function HuanlezadanWGCtrl:CheckHuanlezadanRemind()
	return self.data:RemindHuanlezadan()
end