require("game/serveractivity/consume_discount/consume_discount_wg_data")

-- 连消回礼
ConsumeDiscountWGCtrl = ConsumeDiscountWGCtrl or BaseClass(BaseWGCtrl)

function ConsumeDiscountWGCtrl:__init()
	if ConsumeDiscountWGCtrl.Instance ~= nil then
		print("[ConsumeDiscountWGCtrl]error:create a singleton twice")
	end
	ConsumeDiscountWGCtrl.Instance = self
	self.data = ConsumeDiscountWGData.New()

	self:RegisterAllProtocols()
end

function ConsumeDiscountWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	ConsumeDiscountWGCtrl.Instance = nil
end

function ConsumeDiscountWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRAContinueConsumeInfo, "OnRAContinueConsumeInfo")

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.SendAllInfoReq, self))
end

function ConsumeDiscountWGCtrl:SendAllInfoReq()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME) then
		return
	end
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME,
		opera_type = RA_CIRCULATION_CHONGZHI_OPERA_TYPE.RA_CIRCULATION_CHONGZHI_OPERA_TYPE_QUERY_INFO
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end
--连消
function ConsumeDiscountWGCtrl:Open()
end

function ConsumeDiscountWGCtrl:OnRAContinueConsumeInfo(protocol)
	self.data:SetRAContinueConsumeInfo(protocol)
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
end

function ConsumeDiscountWGCtrl:CheckConsumeDiscount()
	return self.data:GetRAContinueConsumeRewardNum()
end
