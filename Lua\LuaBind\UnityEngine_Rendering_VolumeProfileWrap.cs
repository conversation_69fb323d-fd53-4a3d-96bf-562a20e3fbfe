﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Rendering_VolumeProfileWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Rendering.VolumeProfile), typeof(UnityEngine.ScriptableObject));
		<PERSON><PERSON>RegFunction("Reset", Reset);
		<PERSON><PERSON>unction("Add", Add);
		<PERSON><PERSON>unction("Remove", Remove);
		<PERSON><PERSON>ction("Has", Has);
		<PERSON><PERSON>RegFunction("HasSubclassOf", HasSubclassOf);
		<PERSON>.RegFunction("GetHashCode", GetHashCode);
		<PERSON><PERSON>RegFunction("New", _CreateUnityEngine_Rendering_VolumeProfile);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON>.Reg<PERSON>ar("components", get_components, set_components);
		<PERSON><PERSON>("isDirty", get_isDirty, set_isDirty);
		<PERSON><PERSON>lass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Rendering_VolumeProfile(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Rendering.VolumeProfile obj = new UnityEngine.Rendering.VolumeProfile();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Rendering.VolumeProfile.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Reset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.VolumeProfile));
			obj.Reset();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Add(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.VolumeProfile));
				System.Type arg0 = ToLua.CheckMonoType(L, 2);
				UnityEngine.Rendering.VolumeComponent o = obj.Add(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 3)
			{
				UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.VolumeProfile));
				System.Type arg0 = ToLua.CheckMonoType(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				UnityEngine.Rendering.VolumeComponent o = obj.Add(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Rendering.VolumeProfile.Add");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Remove(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.VolumeProfile));
			System.Type arg0 = ToLua.CheckMonoType(L, 2);
			obj.Remove(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Has(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.VolumeProfile));
			System.Type arg0 = ToLua.CheckMonoType(L, 2);
			bool o = obj.Has(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HasSubclassOf(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.VolumeProfile));
			System.Type arg0 = ToLua.CheckMonoType(L, 2);
			bool o = obj.HasSubclassOf(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetHashCode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.VolumeProfile));
			int o = obj.GetHashCode();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_components(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)o;
			System.Collections.Generic.List<UnityEngine.Rendering.VolumeComponent> ret = obj.components;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index components on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isDirty(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)o;
			bool ret = obj.isDirty;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isDirty on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_components(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)o;
			System.Collections.Generic.List<UnityEngine.Rendering.VolumeComponent> arg0 = (System.Collections.Generic.List<UnityEngine.Rendering.VolumeComponent>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UnityEngine.Rendering.VolumeComponent>));
			obj.components = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index components on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isDirty(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.VolumeProfile obj = (UnityEngine.Rendering.VolumeProfile)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isDirty = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isDirty on a nil value");
		}
	}
}

