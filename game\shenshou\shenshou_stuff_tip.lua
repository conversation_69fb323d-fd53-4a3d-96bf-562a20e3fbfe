ShenShouStuffTip = ShenShouStuffTip or BaseClass(SafeBaseView)

function ShenShouStuffTip:__init()
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_stufftip")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function ShenShouStuffTip:ReleaseCallBack()
	if self.base_tip then
		self.base_tip:DeleteMe()
		self.base_tip = nil
	end

	self.fromView = nil
end

function ShenShouStuffTip:LoadCallBack(index)
	self.base_tip = BaseTip.New(self.node_list.tip_node)
end

function ShenShouStuffTip:ShowIndexCallBack(index)
end

function ShenShouStuffTip:OnFlush(param_t, index)
	if self.base_tip == nil then
		return
	end

	self.base_tip:Reset()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		return
	end

	self.base_tip:SetItemName(item_cfg.name)
	self.base_tip:SetTopColorBg(item_cfg.color)
	self.base_tip:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)
	local item_cell = self.base_tip:GetItemCell()
	item_cell:SetData(self.data)

	local type_str = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.ShowItemType[item_cfg.goods_type])
	self.base_tip:SetEquipSocre(type_str)

	local rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
	self.base_tip:SetSyntheticalSocre(rich_level)

	local desc_info = {}
	local description = item_cfg.description
	if description and description ~= "" then
		desc_info[#desc_info + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end
	self.base_tip:SetItemDesc(desc_info)
	
	local way_desc_info = {title = Language.F2Tip.GetWayStr, desc = string.format(Language.Common.TipsBrackets, item_cfg.get_msg)}
	self.base_tip:SetGetWayDesc(way_desc_info)

	self:ShowOperationState()
end

function ShenShouStuffTip:SetData(data)
	if nil == data then
		return
	end

	self.data = data.data
	self.fromView = data.fromView or ShenShouEquipTip.FROM_SHENSHOUBAG
	self:Open()
end

function ShenShouStuffTip:ShowOperationState()
	local btn_info_list = {}
	local function set_btn_info(index, btn_name)
		local btn_info = {btn_name = btn_name or Language.ShenShou.ButtonLabel[index], btn_click = BindTool.Bind(self.OperationClickHandler, self, index)}
		btn_info_list[#btn_info_list + 1] = btn_info
	end

	if self.fromView == ShenShouEquipTip.FROM_SHENSHOUBAG then
		set_btn_info(8)
	end

	self.base_tip:SetBtnsClick(btn_info_list)
end

function ShenShouStuffTip:OperationClickHandler(index)
	if self.fromView == ShenShouEquipTip.FROM_SHENSHOUBAG then
		if index == 8 then
			ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.DESTROY, self.data.index)
		end
	end

	self:Close()
end

