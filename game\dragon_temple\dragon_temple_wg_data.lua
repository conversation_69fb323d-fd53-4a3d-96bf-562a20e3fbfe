DragonTempleWGData = DragonTempleWGData or BaseClass()

DragonTempleWGData.EquipCount = 18     --装备数量
--3中类型的装备
DragonTempleWGData.EquipType = {
	BaseType = 0,         --0基础
	SpeType = 1,          --1特殊
	GodType = 2,          --2神级
}

function DragonTempleWGData:__init()
	if DragonTempleWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[DragonTempleWGData] attempt to create singleton twice!")
		return
	end
	DragonTempleWGData.Instance = self

	self.dragon_temple_bag_list = {}
	self.suit_level_list = {}
	self.solt_item_list = {}
	self.hatch_item_list = {}
	self.hatch_remind_list = {}
	self.mibao_person_draw_data_list = {}
	self.mibao_server_draw_data_list = {}
	self.mibao_show_reward_id = -1
	self.rank_bonus_pool_num = 0
	self.rank_longhun_data_list = {}
	self.money_contribute_data_list = {}
	self.daily_donate_times = -1
    self.daily_donate_times_reward_flag = {}
    self.daily_donate_gold = 0
	self.daily_draw_times = 0
	self:InitCfg()
	self.hatch_item_addexp_list = {}
	self.hatch_daily_reward_flag = 0
	self.hatch_rmb_buy_time = 0
	self.dragon_rmb_buy_flag = {}
	self.dan_list = {}
	self.show_tip = true

	-- 【红点】
	RemindManager.Instance:Register(RemindName.DragonTemple_LongShen, BindTool.Bind(self.GetDragonTempleLongShenRemind, self))
	RemindManager.Instance:Register(RemindName.DragonTemple_Equip, BindTool.Bind(self.GetDragonTempleEquipRemind, self))
	RemindManager.Instance:Register(RemindName.DragonTemple_Hatch, BindTool.Bind(self.GetHatchRemind, self))
	RemindManager.Instance:Register(RemindName.DragonTemple_Mibao, BindTool.Bind(self.GetDragonTempleDrawRemind, self))
	RemindManager.Instance:Register(RemindName.DragonTemple_Rank, BindTool.Bind(self.GetDragonTempleRankRemind, self))

end

function DragonTempleWGData:__delete()
	DragonTempleWGData.Instance = nil

	self.dragon_temple_bag_list = nil
	self.suit_level_list = nil
	self.solt_item_list = nil
	self.show_tip = nil

	RemindManager.Instance:UnRegister(RemindName.DragonTemple_LongShen)
	RemindManager.Instance:UnRegister(RemindName.DragonTemple_Equip)
	RemindManager.Instance:UnRegister(RemindName.DragonTemple_Hatch)
	RemindManager.Instance:UnRegister(RemindName.DragonTemple_Mibao)
	RemindManager.Instance:UnRegister(RemindName.DragonTemple_Rank)
end

function DragonTempleWGData:InitCfg()
	-- 【配置】
	local cfg = ConfigManager.Instance:GetAutoConfig("dragon_temple_cfg_auto")
	self.other_cfg = cfg.other[1]
	-- 龙神
	self.level_cfg = ListToMap(cfg.level, "level")
	-- 装备
	self.solt_cfg = ListToMap(cfg.solt, "solt")
	self.type_solt_cfg = ListToMapList(cfg.solt, "type")
	self.solt_level_cfg = ListToMap(cfg.solt_level, "solt", "level")
	self.solt_grade_cfg = ListToMap(cfg.solt_grade, "solt", "grade")
	self.equip_cfg = ListToMap(cfg.solt, "cost_item_id")
	self.suit_cfg = ListToMap(cfg.suit, "solt_type", "level")
	self.suit_skill_cfg = ListToMap(cfg.suit_skill, "solt_type")
	-- 孵化
	self.hatch_name_cfg = ListToMap(cfg.hatch_name, "seq")
	self.hatch_level_cfg = ListToMap(cfg.hatch_level, "seq", "level")
	self.hatch_grade_cfg = ListToMap(cfg.hatch_grade, "seq", "grade")
	self.hatch_stuff_cfg = cfg.hatch_stuff
	self.hatch_skill_cfg = ListToMapList(cfg.hatch_skill, "seq")
	self.hatch_model_type_cfg = ListToMap(cfg.hatch_model_type, "seq", "model_type")
	self.hatch_pellet_cfg = ListToMap(cfg.pellet, "type", "index")
	self.hatch_pellet_cost_cfg = ListToMap(cfg.pellet, "use_item_id")
	-- 秘宝
	self.reward_pool_cfg = ListToMapList(cfg.reward_pool, "level")
	self.reward_model_cfg = ListToMap(cfg.reward_model, "level")
    self.item_random_desc = ListToMapList(cfg.item_random_desc, "type")

	-- 排行
	self.draw_mode_cfg = cfg.draw_mode
	self.donate_reward_cfg = ListToMap(cfg.donate_reward, "seq")
	self.longhun_rank_cfg = cfg.longhun_rank
	self.donate_cfg = cfg.donate[1]
	self.dragon_wild_buy_cfg = cfg.dragon_wild_buy
end

---------------------------协议--------------------------------------
-----Set
--背包全部信息
function DragonTempleWGData:SetDragonTempleBagInfo(protocol)
	self.dragon_temple_bag_list = protocol.grid_list
end

-- 单个背包信息变化
function DragonTempleWGData:SetDragonTempleBagChangeInfo(protocol)
	local new_data = protocol.change_info
	local index = new_data.index
	self.dragon_temple_bag_list[index] = new_data
end

-- 全部信息
function DragonTempleWGData:SetDragonTempleAllInfo(protocol)
	self.level = protocol.level
    self.longhun = protocol.longhun
    self.daily_longhun = protocol.daily_longhun
    self.daily_draw_times = protocol.daily_draw_times
    self.daily_reward_flag = protocol.daily_reward_flag
    self.suit_level_list = protocol.suit_level_list
	self.hatch_daily_reward_flag = protocol.hatch_daily_reward_flag
	self.hatch_rmb_buy_time = protocol.hatch_rmb_buy_time
	self.dragon_rmb_buy_flag = protocol.dragon_rmb_buy_flag
	self.dan_list = protocol.dan_list
end

-- 槽位所有信息
function DragonTempleWGData:SetAllSoltInfo(protocol)
	self.solt_item_list = protocol.solt_item_list
end

-- 单个槽位改变
function DragonTempleWGData:SetSingleSoltInfo(protocol)
    local seq = protocol.solt
    local solt_item = protocol.solt_item
    if self.solt_item_list[seq] then
        self.solt_item_list[seq] = solt_item
    end
end

function DragonTempleWGData:SetHatchItemListInfo(protocol)
	self.hatch_item_list = protocol.hatch_item_list
	self:CalculationHatchAddExpList()
end

function DragonTempleWGData:UpdateHatchItemListInfo(protocol)
	local old_data = self:GetHatchItemListInfoBySeq(protocol.seq)
	local new_data = protocol.hatch_item
	local level_change = new_data.level > old_data.level
	local is_hatch = false

	if (new_data.level - old_data.level == 1) then 
		is_hatch = (old_data.model_change == false and new_data.model_change == true)
	end

	local level_breath = level_change and self:IsHatchItemIsBreathNow(protocol.seq, old_data.level) and not is_hatch
	self.hatch_item_list[protocol.seq] = new_data
	self:UpdateHatchAddExpList(protocol.seq)

	return is_hatch, level_change, level_breath
end

function DragonTempleWGData:SetDragonTempleRankInfo(protocol)
	if protocol.rank_type == DRAGON_TEMPLE_OPERATE_RANK_TYPE.LONGHUN then
		self.rank_longhun_data_list = protocol.rank_item_list
	else
		self.rank_bonus_pool_num = protocol.param
		self.money_contribute_data_list = protocol.rank_item_list
	end
end

function DragonTempleWGData:SetDragonTempleDonateInfo(protocol)
	self.daily_donate_times = protocol.daily_donate_times
    self.daily_donate_times_reward_flag = bit:d2b_l2h(protocol.daily_donate_times_reward_flag, nil, true)
    self.daily_donate_gold = protocol.daily_donate_gold
end

function DragonTempleWGData:SetDragonTempleDrawRecordInfo(protocol)
	if protocol.reocrd_type == DRAGON_TEMPLE_OPERATE_DRAW_TYPE.PERSON then
		self.mibao_person_draw_data_list = protocol.record_list
	else
		self.mibao_server_draw_data_list = protocol.record_list
	end	
end

function DragonTempleWGData:DragonTempleDrawRecordAdd(protocol)
	if protocol.reocrd_type == DRAGON_TEMPLE_OPERATE_DRAW_TYPE.PERSON then
		table.insert(self.mibao_person_draw_data_list, protocol.record_item)
	else
		table.insert(self.mibao_server_draw_data_list, protocol.record_item)
	end	
end

function DragonTempleWGData:SetSingleDanUpdateInfo(protocol)
	local seq = protocol.seq
	if self.dan_list[seq] then
        self.dan_list[seq] = protocol.change_data
    end
end

-----Get
--获得龙神等级
function DragonTempleWGData:GetLongShenLevel()
	return self.level or 0
end

--获得龙魂值
function DragonTempleWGData:GetLongHunValue()
	return self.longhun or 0
end

--获得宝箱领取状态
function DragonTempleWGData:GetDailyRewardSate()
	return self.daily_reward_flag or 0
end

-- 获取背包信息
function DragonTempleWGData:GetBagInfo()
	return self.dragon_temple_bag_list
end

--背包index信息
function DragonTempleWGData:GetDragonTempleBagInfoByIndex(index)
	return self.dragon_temple_bag_list[index]
end

--今日获得龙魂值
function DragonTempleWGData:GetDailyLongHunValue()
	return self.daily_longhun or 0
end

--获得套装等级
function DragonTempleWGData:GetSuitLevelByType(solt_type)
	return self.suit_level_list[solt_type] or 0
end
-------------------

function DragonTempleWGData:GetHatchItemListInfoBySeq(seq)
	return self.hatch_item_list[seq]
end

function DragonTempleWGData:GetRankDataListByRankType(rank_type)
	return rank_type == DRAGON_TEMPLE_OPERATE_RANK_TYPE.LONGHUN and self.rank_longhun_data_list or self.money_contribute_data_list
end

function DragonTempleWGData:GetRankBonusPoolNum()
	return self.rank_bonus_pool_num
end

function DragonTempleWGData:GetDrawRecordInfoByType(draw_type)
	return draw_type == DRAGON_TEMPLE_OPERATE_DRAW_TYPE.PERSON and self.mibao_person_draw_data_list or self.mibao_server_draw_data_list
end

---------------------------协议end--------------------------------------

--获得其他配置
function DragonTempleWGData:GetOtherCfg()
	return self.other_cfg
end

-- 龙神
function DragonTempleWGData:GetLongShenLevelCfg(level)
	return self.level_cfg[level]
end

-- 当前龙神等级战力计算
function DragonTempleWGData:GetLongShenLevelCapability(level)
	local level_cfg = self:GetLongShenLevelCfg(level)
	if IsEmptyTable(level_cfg) then
        return 0
    end

    local longshen_attribute = AttributePool.AllocAttribute()
    local level_attr_list = self:GetLongShenLevelAttrList(level)
    for k, v in pairs(level_attr_list) do
		longshen_attribute[v.attr_str] = longshen_attribute[v.attr_str] + v.attr_value
	end

    return AttributeMgr.GetCapability(longshen_attribute)  
end

--龙神等级属性
function DragonTempleWGData:GetLongShenLevelAttrList(level, no_sort)
	local level_attr_list = {}
	local level_cfg = self:GetLongShenLevelCfg(level)
	if not level_cfg then
		return level_attr_list
	end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
        attr_id = level_cfg["attr_id" .. i]
        attr_value = level_cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
                add_value = 0,
            }

			if not no_sort then
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			end

            table.insert(level_attr_list, data)
        end
    end

	if not no_sort and not IsEmptyTable(level_attr_list) then
        table.sort(level_attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return level_attr_list
end

-- 装备

-- 获取背包中物品数量
function DragonTempleWGData:GetItemNumInBagById(item_id)
	local bag_list = self:GetBagInfo()
	local num = 0
	for k,v in pairs(bag_list) do
		if v.item_id == item_id then
			num = v.num + num
		end
	end
	return num
end

function DragonTempleWGData:GetSoltEquipCfg()
	return self.solt_cfg
end

function DragonTempleWGData:GetTypeSoltEquipCfg(type)
	return self.type_solt_cfg[type]
end
--物品id是否属于龙神装备
function DragonTempleWGData:GetIsDragonTempleEquip(item_id)
	if not item_id then return false end
	return self.equip_cfg[item_id] ~= nil
end

--根据槽位信息配置
function DragonTempleWGData:GetEquipCfgBySolt(solt)
	return self.solt_cfg[solt]
end

--根据id拿槽位信息配置
function DragonTempleWGData:GetEquipCfgById(item_id)
	return self.equip_cfg[item_id]
end

--根据类型拿套装信息配置
function DragonTempleWGData:GetEquipSuitCfgByType(solt_type)
	return self.suit_cfg[solt_type]
end

--获取套装等级配置
function DragonTempleWGData:GetEquipSuitLevelCfg(solt_type, level)
	return (self.suit_cfg[solt_type] or {})[level]
end

--根据类型拿技能信息配置
function DragonTempleWGData:GetEquipSkillByType(solt_type)
	return self.suit_skill_cfg[solt_type]
end

--装备槽位信息
function DragonTempleWGData:GetSoltEquipInfo()
	local info_list = {}
	for k, v in pairs(self.solt_cfg) do
		info_list[k] = self:GetSoltEquipInfoBySolt(v.solt)
	end

	return info_list
end

--装备槽位信息bySlot
function DragonTempleWGData:GetSoltEquipInfoBySolt(solt)
	local equip_cfg = self:GetEquipCfgBySolt(solt)
	local info = {}
	if equip_cfg then
		info.solt = equip_cfg.solt
		info.type = equip_cfg.type
		info.cost_item_id = equip_cfg.cost_item_id
		info.cost_item_num = equip_cfg.cost_item_num
		info.level = (self.solt_item_list[equip_cfg.solt] or {})["level"] or 0
		info.grade = (self.solt_item_list[equip_cfg.solt] or {})["grade"] or 0
	end

	return info
end

--等级配置
function DragonTempleWGData:GetEquipLevelCfg(solt, level)
	return (self.solt_level_cfg[solt] or {})[level]
end

--阶数配置
function DragonTempleWGData:GetEquipGradeCfg(solt, grade)
	return (self.solt_grade_cfg[solt] or {})[grade]
end

--是否达到进阶条件
function DragonTempleWGData:IsCanUpGrade(solt)
	local solt_info = self.solt_item_list[solt]
	if not solt_info then
		return false
	end


	local cur_level = solt_info.level
	local cur_grade = solt_info.grade

	local cur_grade_cfg = self:GetEquipGradeCfg(solt, cur_grade)
    local next_grade_cfg = self:GetEquipGradeCfg(solt, cur_grade + 1)
    if not next_grade_cfg or not cur_grade_cfg then
    	return false
    end

    return cur_level >= cur_grade_cfg.need_level
end

--总战力（装备 + 套装）
function DragonTempleWGData:GetEquipCap()
	local attribute = AttributePool.AllocAttribute()
	local solt_info = self:GetSoltEquipInfo()
	local cap = 0
	if not IsEmptyTable(solt_info) then
		for index, data in pairs(solt_info) do
			if data.level >= 1 then
				local level_attr_list = self:GetEquipLevelAttrList(data.solt, data.level)
				for k, v in pairs(level_attr_list) do
					attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
				end

				local grade_attr_list = self:GetEquipGradeAttrList(data.solt, data.grade)
				for k, v in pairs(grade_attr_list) do
					attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
				end
			end
		end
	end

	for solt_type, level in pairs(self.suit_level_list) do
		if level >= 1 then
			local suit_att_list = self:GetEquipSuitAttrList(solt_type, level)
			for k, v in pairs(suit_att_list) do
				attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
			end
		end
	end

	cap = AttributeMgr.GetCapability(attribute)
	return cap
end

--当前装备所有属性(等级 + 阶数)
function DragonTempleWGData:GetEquipAllAttrList(solt, level, grade)
	if not self.solt_cfg[solt] then
		return
	end

	local all_attr_list = {}
	local is_can_upgrade = self:IsCanUpGrade(solt)
	local cur_level_cfg = self:GetEquipLevelCfg(solt, level)
	local next_level_cfg = self:GetEquipLevelCfg(solt, level + 1)
	local attr_level_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 5)
	
	local cur_grade_cfg = self:GetEquipGradeCfg(solt, grade)
    local next_grade_cfg = self:GetEquipGradeCfg(solt, grade + 1)
    local attr_grade_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_grade_cfg, next_grade_cfg, "attr_id", "attr_value", 1, 5)
   
	--判断总属性是否存在该属性类型
    local is_new_attr = function (attr_tab)
    	for k, v in pairs(all_attr_list) do
    		if attr_tab.attr_str == v.attr_str then
    			return false, v
    		end
    	end

    	return true
    end

	local add_attr = function (attr_tab, add_type) -- 1等级属性 2阶级属性
		local new_attr, same_attr = is_new_attr(attr_tab)
    	if new_attr then
    		local data = {
				attr_str = attr_tab.attr_str,
	            attr_value = attr_tab.attr_value,
	            add_value = add_type == 1 and (is_can_upgrade and 0 or attr_tab.add_value) or (is_can_upgrade and attr_tab.add_value or 0)
			}

			table.insert(all_attr_list, data)
    	else
    		same_attr.attr_value = same_attr.attr_value + attr_tab.attr_value
    		same_attr.add_value = add_type == 1 and (is_can_upgrade and same_attr.add_value or attr_tab.add_value) or (is_can_upgrade and attr_tab.add_value or same_attr.add_value)
    	end
    end

	--插入等级属性
	for k, v in pairs(attr_level_list) do
		add_attr(v, 1)
	end

	--插入阶级属性
	for k, v in pairs(attr_grade_list) do
		add_attr(v, 2)
	end

	return all_attr_list
end

--战力
function DragonTempleWGData:GetEquipAllAttrAndCap(solt, level, grade)
	local attr_list = AttributePool.AllocAttribute()
	local capability = 0
	if not self.solt_cfg[solt] then
		print_error("====槽位不存在=====",solt)
		return attr_list, capability
	end

	local all_attr = self:GetEquipAllAttrList(solt, level, grade)
	for k, v in pairs(all_attr) do
		attr_list[v.attr_str] = attr_list[v.attr_str] + v.attr_value
	end

	capability = AttributeMgr.GetCapability(attr_list)

	return attr_list, capability
end

--装备等級属性
function DragonTempleWGData:GetEquipLevelAttrList(solt, level)
	local level_attr_list = {}
	local level_cfg = self:GetEquipLevelCfg(solt, level)
	if not level_cfg then
		return level_attr_list
	end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
        attr_id = level_cfg["attr_id" .. i]
        attr_value = level_cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
                add_value = 0,
            }

			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
            table.insert(level_attr_list, data)
        end
    end

	if not IsEmptyTable(level_attr_list) then
        table.sort(level_attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return level_attr_list
end

--装备阶级属性
function DragonTempleWGData:GetEquipGradeAttrList(solt, grade)
	local garde_attr_list = {}
	local grade_cfg = self:GetEquipGradeCfg(solt, grade)
	if not grade_cfg then
		return garde_attr_list
	end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
        attr_id = grade_cfg["attr_id" .. i]
        attr_value = grade_cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
                add_value = 0,
            }

			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
            table.insert(garde_attr_list, data)
        end
    end

	if not IsEmptyTable(garde_attr_list) then
        table.sort(garde_attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return garde_attr_list
end

--装备套装属性
function DragonTempleWGData:GetEquipSuitAttrList(solt_type, level)
	local suit_attr_list = {}
	local suit_info_list = self:GetEquipSuitCfgByType(solt_type)
	local cur_suit_info = (suit_info_list or {})[level]
	if not cur_suit_info then
		return cur_suit_info
	end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
        attr_id = cur_suit_info["attr_id" .. i]
        attr_value = cur_suit_info["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
                add_value = 0,
            }

			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
            table.insert(suit_attr_list, data)
        end
    end

	if not IsEmptyTable(suit_attr_list) then
        table.sort(suit_attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return suit_attr_list
end

--该类型装备激活多少件
function DragonTempleWGData:GetEquipActNumBy(solt_type)
	local num = 0
	local solt_info = self:GetSoltEquipInfo()
	local type_equip_info = {}
	if not IsEmptyTable(solt_info) then
		for i, v in pairs(solt_info) do
	    	if v.type == solt_type then
	    		table.insert(type_equip_info, v)
	    		num = v.level > 0 and num + 1 or num
	    	end
	    end
	end

    return num, type_equip_info
end

-----------------------------------remind-------------------------------
function DragonTempleWGData:GetDragonTempleLongShenRemind()
	local longshen_level = self:GetLongShenLevel()
	local cur_level_cfg = self:GetLongShenLevelCfg(longshen_level)
	local next_level_cfg = self:GetLongShenLevelCfg(longshen_level + 1)
	local longhun_value = DragonTempleWGData.Instance:GetLongHunValue()
	if cur_level_cfg and next_level_cfg then
		if longhun_value >= cur_level_cfg.need_longhun then
			return 1
		end
	end
	
	local state = self:GetDailyRewardSate()
	if state == 0 then
		return 1
	end

	return 0
end

--装备红点
function DragonTempleWGData:GetDragonTempleEquipRemind()
	for i = 0, DragonTempleWGData.EquipCount - 1 do
		if self:GetDragonTempleSlotRemind(i) then
			return 1
		end
	end

	return 0
end

--装备类型红点
function DragonTempleWGData:GetDragonTempleEquipTypeRemind(type)
	if type == nil then
		return false
	end

	local type_solt_cfg = self:GetTypeSoltEquipCfg(type)
	if not IsEmptyTable(type_solt_cfg) then
		for k, v in ipairs(type_solt_cfg) do
			if self:GetDragonTempleSlotRemind(v.solt)then
				return true
			end
		end
	end

	return false
end

--每个槽位红点
function DragonTempleWGData:GetDragonTempleSlotRemind(solt)
	if self:GetDragonTempleSlotLevelAndGradeRemind(solt) or self:GetDragonTempleSlotSuitRemind(solt) then
		return true
	end

	return false
end

--升级and进阶红点
function DragonTempleWGData:GetDragonTempleSlotLevelAndGradeRemind(solt)
	local info = self:GetSoltEquipInfoBySolt(solt)
	if IsEmptyTable(info) then
		return false
	end

	local is_act = info.level ~= 0
	if not is_act then
		--激活装备红点
		local num = self:GetItemNumInBagById(info.cost_item_id)
   		if num >= info.cost_item_num then
   			return true
   		end
	else
		local is_can_upgrade = self:IsCanUpGrade(info.solt)
		--判断是走升级还是升阶
		if is_can_upgrade then
			local cur_grade_cfg = self:GetEquipGradeCfg(info.solt, info.grade)
			local next_grade_cfg = self:GetEquipGradeCfg(info.solt, info.grade + 1)
			if next_grade_cfg and cur_grade_cfg then
				local cost_num = self:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
				if cost_num >= cur_grade_cfg.cost_item_num then
					return true
				end
			end
		else
			local cur_level_cfg =self:GetEquipLevelCfg(info.solt, info.level + 1)
			local next_level_cfg =self:GetEquipLevelCfg(info.solt, info.level + 1)
			if next_level_cfg and cur_level_cfg then
				local cost_num = self:GetItemNumInBagById(cur_level_cfg.cost_item_id)
				if cost_num >= cur_level_cfg.cost_item_num then
					return true
				end
			end
		end
	end

	return false
end

--套装红点
function DragonTempleWGData:GetDragonTempleSlotSuitRemind(solt)
	local info = self:GetSoltEquipInfoBySolt(solt)
	if IsEmptyTable(info) then
		return false
	end

	local is_act = info.level ~= 0
	local suit_info = self:GetEquipSuitCfgByType(info.type)
	if not IsEmptyTable(suit_info) and is_act then
		local act_num = self:GetEquipActNumBy(info.type)
		local cur_suit_level = self:GetSuitLevelByType(info.type)
		local next_suit_cfg = suit_info[cur_suit_level + 1]
		if next_suit_cfg and act_num >= next_suit_cfg.need_num then
			return true
		end
	end

	return false
end
------------------------------------------------------------------------

-----------------------------------孵化Start-------------------------------
function DragonTempleWGData:GetHatchRemind()
	self:CalculationHatchRemind()
	local tupo_remind = false
	for i = 0, 4 do
		if self:HatchTupoRemind(i) then
			tupo_remind = true
			break
		end
	end
	
	if self.hatch_remind_list.remind or tupo_remind then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DRAGON_TEMP_HATCH, 1, function ()
			ViewManager.Instance:Open(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)
			return true
		end)
		return 1
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.DRAGON_TEMP_HATCH, 0)
	return 0
end

function DragonTempleWGData:HatchTupoRemind(seq)
	local active = self:CanShowHatchItem(seq)
	local level = NewFightMountWGData.Instance:GetAppearanceLevelBySeq(seq)
	local cur_level_cfg = NewFightMountWGData.Instance:GetUpgradeLevelCfg(seq, level)
	local next_level_cfg = NewFightMountWGData.Instance:GetUpgradeLevelCfg(seq, level + 1)
	if active and cur_level_cfg and next_level_cfg then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
		if item_num >= cur_level_cfg.need_num then
			return true
		end
	end

	return false
end

function DragonTempleWGData:HatchDanRemind(seq)
	local active = self:CanShowHatchItem(seq)
	if not active then
		return false
	end

	local dan_cfg = self.hatch_pellet_cfg[seq]
	if not IsEmptyTable(dan_cfg) then
		for k, v in pairs(dan_cfg) do
			if self:HatchOneDanRemind(seq, v.index) then
				return true
			end
		end
	end

	return false
end

function DragonTempleWGData:HatchOneDanRemind(seq, index)
	local cfg = self:GetHatchPelletCfg(seq, index)
	if not cfg then
		return false
	end

	local role_level = RoleWGData.Instance:GetAttr('level')
	local used_num =  self:GetHatchPelletUseNum(seq, index)
	local had_num = ItemWGData.Instance:GetItemNumInBagById(cfg.use_item_id)

	if cfg.role_level <= role_level and used_num < cfg.use_limit_num and had_num > 0 then
		return true
	end

	return false
end

function DragonTempleWGData:GetHatchLevelRemind(index)
	self:CalculationHatchRemind()
	local remind = (self.hatch_remind_list or {})[index] or false
	return remind
end

function DragonTempleWGData:GetHatchOneLevelRemind(seq)
	local active = self:CanShowHatchItem(seq)
	if not active then
		return false
	end

	local hatch_item_data = self:GetHatchItemListInfoBySeq(seq)
	if IsEmptyTable(hatch_item_data) then
		return false
	end

	local next_level_cfg = self:GetHatchLevelCfg(seq, hatch_item_data.level + 1)
	local is_max_level = IsEmptyTable(next_level_cfg)

	if is_max_level then
		return false
	end
	
	local current_level_cfg = self:GetHatchLevelCfg(seq, hatch_item_data.level)
	if not IsEmptyTable(current_level_cfg) then
		local is_breach = current_level_cfg.need_exp <= 0 and current_level_cfg.cost_gold > 0

		if is_breach then
			local role_gold = RoleWGData.Instance.role_info.gold
			local cost_gold = current_level_cfg.cost_gold
			local cost_enough = cost_gold <= role_gold
			if cost_enough then
				return true
			end
		else
			local can_get_exp = self:HatchCanGetExp()
			local exp_enough = (can_get_exp + hatch_item_data.exp) >= current_level_cfg.need_exp
			local can_up_level = self:IsHatchCanUpLevel(seq)

			if exp_enough or can_up_level then
				return true
			end
		end
	end

	return false
end

function DragonTempleWGData:CalculationHatchRemind()
	local hatch_info = self.hatch_item_list

	if IsEmptyTable(hatch_info) then
		self.hatch_remind_list = {}
		return
	end

	local hatch_remind_list = {}
	hatch_remind_list.remind = false

	for i = 0, 4 do
		local active = self:CanShowHatchItem(i)
		hatch_remind_list[i] = false
		if active then
			if self:GetHatchOneLevelRemind(i) or self:HatchDanRemind(i)then
				hatch_remind_list[i] = true
				hatch_remind_list.remind = true
			end
		else
			hatch_remind_list[i] = false
		end
	end

	self.hatch_remind_list = hatch_remind_list
end

function DragonTempleWGData:GetHatchItemRemind(index)
	self:CalculationHatchRemind()
	local remind = (self.hatch_remind_list or {})[index] or self:HatchTupoRemind(index)
	return remind
end

function DragonTempleWGData:CheckIsHatchItem(change_item_id)
	local stuff_cfg = self:GetHatchStuffCfg()

	for k, v in pairs(stuff_cfg) do
		if change_item_id == v.item_id then
			return true
		end
	end

	return false
end

function DragonTempleWGData:GetHatchSkillDataByIndex(seq, skill_index)
	local skill_cfg = self:GetHatchSkillItemInfo(seq, skill_index)
	local show_data = {}
	
	if IsEmptyTable(skill_cfg) then
		return show_data
	end

	local need_grade = skill_cfg.start_grade
	local grade_data = self:GetHatchGradeCfg(seq, need_grade)
	
	if IsEmptyTable(grade_data) then
		return show_data
	end

	local curent_level_cfg = self:GetHatchItemListInfoBySeq(seq)
	local limit_text = ""

	if not IsEmptyTable(curent_level_cfg) then
		local level = curent_level_cfg.level
		local is_act = level >= grade_data.need_level

		if not is_act then
			limit_text = string.format(Language.DragonTemple.SkillGradeActTips, grade_data.need_level)
		end
	end

	show_data = {
		icon = skill_cfg.skill_icon,
		top_text = skill_cfg.skill_name,
		skill_level = 0,
		body_text = skill_cfg.skill_des,
		limit_text = limit_text,
		x = 0,
		y = 0,
		set_pos2 = true,
		hide_level = true,
	}

	return show_data
end

function DragonTempleWGData:GetHatchSkillItemInfo(seq, index)
	return (self.hatch_skill_cfg[seq] or {})[index]
end

function DragonTempleWGData:GetHatchSkillInfo(seq)
	return self.hatch_skill_cfg[seq]
end

function DragonTempleWGData:GetHatchSkillAttrInfo(seq)
	local hatch_skill_info = {}
	local all_skill_attr_list = {}
	local hatch_skill_cfg = self:GetHatchSkillInfo(seq)
	local hatch_info = self:GetHatchItemListInfoBySeq(seq)
	--判断总属性是否存在该属性类型
	local is_new_attr = function (attr_tab)
		for k, v in pairs(all_skill_attr_list) do
			if attr_tab.attr_str == v.attr_str then
				return false, v
			end
		end

		return true
	end

	local add_attr = function (attr_tab)
        local new_attr, same_attr = is_new_attr(attr_tab)
        local attr_str
        if new_attr then
            local data = {}
            attr_str = attr_tab.attr_str
            data.attr_str = attr_str
            data.attr_value = attr_tab.attr_value
            data.add_value = attr_tab.add_value
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
            table.insert(all_skill_attr_list, data)
        else
            same_attr.attr_value = same_attr.attr_value + attr_tab.attr_value
            same_attr.add_value =  attr_tab.add_value
        end
    end

	if not IsEmptyTable(hatch_skill_cfg) and not IsEmptyTable(hatch_info) then
		local level = hatch_info.level

		for k, v in pairs(hatch_skill_cfg) do
			local start_grade = v.start_grade
			local grade_cfg = self:GetHatchGradeCfg(seq, start_grade)
			local active = false
			if not IsEmptyTable(grade_cfg) then
				active = grade_cfg.need_level <= level
			end

			hatch_skill_info[k] = active
			if active then
				local attr_skill_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(v, nil, "attr_id", "attr_value", 1, 1)
				for k1, v1 in pairs(attr_skill_list) do
					add_attr(v1)
				end
			end
		end
	end

	return hatch_skill_info, all_skill_attr_list
end

function DragonTempleWGData:IsHatchMaxLevel(seq)
	local hatch_info = self:GetHatchItemListInfoBySeq(seq)
	local is_max_level = false

	if not IsEmptyTable(hatch_info) then
		local next_level_data = self:GetHatchLevelCfg(seq, hatch_info.level + 1)
		-- local next_grade_data = self:GetHatchGradeCfg(seq, hatch_info.grade + 1)
		is_max_level = IsEmptyTable(next_level_data) --and IsEmptyTable(next_grade_data)
	end

	return is_max_level
end

function DragonTempleWGData:HatchCanGetExp()
	local can_get_exp = 0
	local stuff_cfg = self:GetHatchStuffCfg()
	for k, v in pairs(stuff_cfg) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if num > 0 then
			can_get_exp = can_get_exp + num * v.exp
		end
	end

	return can_get_exp
end

function DragonTempleWGData:IsCanHatchUpLevel()
	local can_up = false
	local stuff_cfg = self:GetHatchStuffCfg()

	for k, v in pairs(stuff_cfg) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if num > 0 then
			can_up = true
			break
		end
	end

	return can_up
end

function DragonTempleWGData:GetHatchStuffCfg()
	return 	self.hatch_stuff_cfg
end

function DragonTempleWGData:GethatchlevelAttrList(seq)
	local attr_list = {}
	local hatch_info = self:GetHatchItemListInfoBySeq(seq)

	if not IsEmptyTable(hatch_info) then
		local level = hatch_info.level
		attr_list = self:NewCalculationAttract(seq, level)
	end

	return attr_list
end

function DragonTempleWGData:GethatchCapBySeq(seq)
	local attribute = AttributePool.AllocAttribute()
	local cap = 0
	local level_attr_list = {}

	local hatch_info = self:GetHatchItemListInfoBySeq(seq)
	if not IsEmptyTable(hatch_info) then
		local level = hatch_info.level
		level_attr_list = self:NewCalculationAttract(seq, level)
	end

	if not IsEmptyTable(level_attr_list) then 
		for k, v in pairs(level_attr_list) do
			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end
	end

	local mount_level = NewFightMountWGData.Instance:GetAppearanceLevelBySeq(seq)
	local cur_mount_level_cfg = NewFightMountWGData.Instance:GetUpgradeLevelCfg(seq, mount_level)
	if cur_mount_level_cfg then
		local mount_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_mount_level_cfg, nil, "attr_id", "attr_value", 1, 5)
		for k, v in pairs(mount_attr_list) do
			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end
	end

	cap = AttributeMgr.GetCapability(attribute)
	return cap
end

function DragonTempleWGData:NewCalculationAttract(seq, level)
	local all_attr_list = {}
	--只针对孵化基础属性
	local base_attr_per = 0
	local current_level_data = self:GetHatchLevelCfg(seq, level)
	local next_level_data = self:GetHatchLevelCfg(seq, level + 1)
	local attr_level_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(current_level_data, next_level_data, "attr_id", "attr_value", 1, 5)
	local _, skill_attr_list = self:GetHatchSkillAttrInfo(seq)
	local dan_list = self:GetHatchDanlist(seq)

	--判断总属性是否存在该属性类型
	local is_new_attr = function (attr_tab)
		for k, v in pairs(all_attr_list) do
			if attr_tab.attr_str == v.attr_str then
				return false, v
			end
		end

		return true
	end

	local add_attr = function (attr_tab, beishu)
		local new_attr, same_attr = is_new_attr(attr_tab)
		local attr_str
		beishu = beishu or 1
		if new_attr then
			local data = {}
			attr_str = attr_tab.attr_str
			data.attr_str = attr_str
			data.attr_value = math.floor(attr_tab.attr_value * beishu)
			data.add_value = math.floor(attr_tab.add_value * beishu)
			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			table.insert(all_attr_list, data)
		else
			same_attr.attr_value = same_attr.attr_value + math.floor(attr_tab.attr_value * beishu)
			same_attr.add_value =  same_attr.add_value + math.floor(attr_tab.add_value * beishu)
		end
	end

	--插入3个属性丹属性
	if not IsEmptyTable(dan_list) then
		for k, v in pairs(dan_list) do
			if v.used_num > 0 then
				local dan_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(v.cfg, nil, "attr_id", "attr_value", 1, 5)
				for index, dan_attr in pairs(dan_attr_list) do
					dan_attr.attr_value = dan_attr.attr_value * v.used_num
					add_attr(dan_attr)
					base_attr_per = base_attr_per + (v.cfg.base_attr_per * v.used_num)
				end
			end
		end
	end

	--王者特权万分比
    local king_data = RechargeWGData.Instance:GetCurKingVipAddition()
    local is_active = RechargeWGCtrl.Instance:GetKingVipIsActive()
	local king_per = 0
	if not IsEmptyTable(king_data) and is_active then
        local attr_add = king_data["add_" .. seq + 6]
		king_per = attr_add / 10000
	end

	--插入等级属性
	local add_beishu = 1
	for k, v in pairs(attr_level_list) do
		local is_base_attr = EquipmentWGData.Instance:GetAttrIsBase(v.attr_str)
		
		add_beishu = is_base_attr and 1 + (base_attr_per * 0.0001) or 1

		if is_base_attr then
			add_beishu = add_beishu + king_per
		end
		
		add_attr(v, add_beishu)
	end

	--插入技能属性
	for k, v in pairs(skill_attr_list) do
		add_attr(v)
	end

	return all_attr_list
end

function DragonTempleWGData:GetHatchOrderDataList(seq)
	local data_list = {}
	local hatch_grade_limit = self:GetHatchNameCfgBySeq(seq)
	local hatch_item_data = self:GetHatchItemListInfoBySeq(seq)
	
	if not IsEmptyTable(hatch_grade_limit) and not IsEmptyTable(hatch_item_data) then
		local level = hatch_item_data.level
		local grade = level / 10

		for i = 1, hatch_grade_limit.hatch_grade_limit do
			data_list[i] = {is_lock = grade <= i}
		end
	end

	return data_list
end

function DragonTempleWGData:GetHatchLevelCfg(seq, level)
	return (self.hatch_level_cfg[seq] or {})[level]
end

function DragonTempleWGData:GetHatchGradeCfg(seq, grade)
	return (self.hatch_grade_cfg[seq] or {})[grade]
end

-- function DragonTempleWGData:GetHatchShowCfg()
-- 	local level = self:GetLongShenLevel()
-- 	local show_hatch_cfg = self:GetLongShenLevelCfg(level)
-- 	return show_hatch_cfg and show_hatch_cfg.hatch_show or 1
-- end

function DragonTempleWGData:CanShowHatchItem(seq)
	local level = self:GetLongShenLevel()
	local hatch_name_cfg = self:GetHatchNameCfgBySeq(seq)
	local show_level = hatch_name_cfg and hatch_name_cfg.show_level or 0
	return level >= show_level
end

function DragonTempleWGData:GetHatchNameCfgBySeq(seq)
	return self.hatch_name_cfg[seq]
end

function DragonTempleWGData:GetHatchIsModelChange(seq, level)
	local model_change = false
	local hatch_name_cfg = self:GetHatchNameCfgBySeq(seq)

	if not IsEmptyTable(hatch_name_cfg) then
		local hatch_grade_limit = hatch_name_cfg.hatch_grade_limit
		local grade_cfg = self:GetHatchGradeCfg(seq, hatch_grade_limit)

		if not IsEmptyTable(grade_cfg) then
			model_change = level >= grade_cfg.need_level
		end
	end

	return model_change
end

function DragonTempleWGData:GetHatchTogSelect()
	self:CalculationHatchRemind()

	local index = 0
	for i = 0, 4 do
		local remind = (self.hatch_remind_list or {}).remind or self:HatchTupoRemind(i)
		if remind then
			index = i
			break
		end
	end

	return index
end

function DragonTempleWGData:IsHatchBreath()
	for k, v in pairs(self.hatch_item_list) do
		if self:IsHatchItemIsBreathNow(k, v.level) and not self:IsHatchMaxLevel(k) then
			return true
		end
	end

	return false
end


function DragonTempleWGData:GetHatchModelTypeCfgByLevel(seq, level)
	local show_cfg = {}
	local mount_level = NewFightMountWGData.Instance:GetAppearanceLevelBySeq(seq)
	if not IsEmptyTable(self.hatch_model_type_cfg[seq]) then
		for i, v in ipairs(self.hatch_model_type_cfg[seq]) do
			if level >= v.min_level then
				if v.need_model_act == 1 and mount_level > 0 then
					show_cfg = v
				elseif v.need_model_act == 0 then
					show_cfg = v
				end	
			end
		end
	end

	return show_cfg
end

function DragonTempleWGData:GetHatchModelTypeCfgByType(seq, model_type)
	return (self.hatch_model_type_cfg[seq] or {})[model_type]
end

function DragonTempleWGData:GetHatchMaxModelByType(seq)
    return #self.hatch_model_type_cfg[seq]
end

function DragonTempleWGData:GetHatchPelletCfg(type, index)
	return (self.hatch_pellet_cfg[type] or {})[index]
end

function DragonTempleWGData:GetIsHatchPelletCostCfg(stuff_id)
    return self.hatch_pellet_cost_cfg[stuff_id] ~= nil
end

function DragonTempleWGData:GetHatchPelletCostCfg(stuff_id)
    return self.hatch_pellet_cost_cfg[stuff_id]
end

function DragonTempleWGData:GetHatchPelletUseNum(type, index)
	return (self.dan_list[type] or {})[index] or 0
end

-- 属性丹列表
function DragonTempleWGData:GetHatchDanlist(type)
	local list = {}
	local dan_cfg = self.hatch_pellet_cfg[type]
    if not dan_cfg then
        return list
    end

	local role_level = RoleWGData.Instance:GetAttr('level')
    for index = 0, #dan_cfg do
        local cfg = dan_cfg[index]
        local data = {cfg = cfg}
        data.is_open = cfg.role_level <= role_level
        data.used_num =  self:GetHatchPelletUseNum(type, index)
        data.had_num = ItemWGData.Instance:GetItemNumInBagById(cfg.use_item_id)
		data.is_remind = self:HatchOneDanRemind(type, index)
        list[index] = data
    end

    return list
end
-----------------------------------孵化End---------------------------------

-----------------------------------秘宝Start-------------------------------
function DragonTempleWGData:GetMiBaoDrawTime()
	-- 计算抽奖次数
	-- local draw_time = math.floor(self:GetDailyLongHunValue() / self.other_cfg.draw_need_longhun) - self.daily_draw_times
	-- return draw_time > 0 and draw_time or 0
	return self.daily_draw_times
end

function DragonTempleWGData:SetMiBaoShowRewardId(level)
	self.mibao_show_reward_id = level
end

function DragonTempleWGData:GetMiBaoShowReward()
	return self:GetMiBaoRewardByLevel(self.mibao_show_reward_id)
end

function DragonTempleWGData:GetMiBaoRewardByLevel(level)
	return ((self.mibao_reward_cfg or {})[level] or {}).show_reward
end

function DragonTempleWGData:GetMiBaoDrawPoolShowModelDataByLevel(level)
	return self.reward_model_cfg[level]
end

function DragonTempleWGData:GetMiBaoActiveDrawPoolDataList()
	self:CalculationMiBaoReward()
	local level = self:GetLongShenLevel()
	local active_data_list = {}

	for k, v in pairs(self.mibao_reward_cfg) do
		if level >= v.level then
			table.insert(active_data_list, v)
		end
	end

	return active_data_list
end

function DragonTempleWGData:CalculationMiBaoReward()
	if not self.mibao_reward_cfg then
		self.mibao_reward_cfg = {}

		for k, v in pairs(self.reward_pool_cfg) do
			self.mibao_reward_cfg[k] = {}
			self.mibao_reward_cfg[k].level = k
			local show_reward = {}
			
			for i, u in pairs(v) do
				table.insert(show_reward, u.item)
			end
			self.mibao_reward_cfg[k].show_reward = show_reward
		end
	end
end

function DragonTempleWGData:GetDragonTempleDrawRemind()
	local draw_time = self:GetMiBaoDrawTime()
	if draw_time <= 0 then
		return 0
	end

	for k, v in pairs(self.draw_mode_cfg) do
		if draw_time >= v.times then
			return 1
		end
	end

	return 0
end


function DragonTempleWGData:GetGaiLvInfo(select_type)
	return self.item_random_desc[select_type] or {}
end
------------------------------------秘宝End--------------------------------

----------------------------------排行Start--------------------------------
function DragonTempleWGData:GetDragonTempleRankRemind()
	for k, v in pairs(self.donate_reward_cfg) do
		if not self:GetRankSliderItemGetFlag(v.seq)  then
			if self:GetRankSliderItemCanGetFlag(v.seq) then
				return 1
			end
		end
	end

	return 0
end

function DragonTempleWGData:GetScoreRankRewardData(rank_id)
	local data = {}	
	for k, v in pairs(self.longhun_rank_cfg) do
		if rank_id >= v.min_rank and rank_id <= v.max_rank then
			data = {item_id = v.item_id}
			break
		end 
	end

	return data
end

function DragonTempleWGData:GetRankInfoByOperateRankType(operate_rank_type)
	local data_list = self:GetRankDataListByRankType(operate_rank_type)
	local my_rank = -1
	local my_value = 0

	if IsEmptyTable(data_list) then
		return data_list, my_rank, my_value
	end

	local role_uuid = RoleWGData.Instance:GetUUid()
	for k, v in pairs(data_list) do
		if v.uuid == role_uuid then
			my_rank = v.rank
			my_value = v.rank_value 
			break
		end
	end  

	return data_list, my_rank, my_value
end

function DragonTempleWGData:GetLongHunRankCfg()
	return self.longhun_rank_cfg
end

function DragonTempleWGData:GetDonateCfg()
	return self.donate_cfg
end

function DragonTempleWGData:GetDailyDonateTime()
	return self.daily_donate_times
end

function DragonTempleWGData:GetRankSliderItemGetFlag(seq)
	return self.daily_donate_times_reward_flag[seq] == 1
end

function DragonTempleWGData:GetRankSliderItemCanGetFlag(seq)
	local donate_data = self:GetDonateRewardCfgBySeq(seq)

	if not IsEmptyTable(donate_data) then
		return	self.daily_donate_times >= donate_data.need_times
	end

	return false
end

function DragonTempleWGData:GetRankSliderValue()
	local donate_time = self.daily_donate_times
	local value = 0

	if donate_time <= 0 then
		return value
	end

	local per = 1 / ((#self.donate_reward_cfg + 1) * 3 - 1)
	local d_value = 0

	for k, v in pairs(self.donate_reward_cfg) do
		local is_first = k == 0
		local d_per = is_first and 2 * per or 3 * per
		local per_diff_value = v.need_times - d_value
		local cur_diff_value = donate_time - d_value
		d_value = v.need_times

		if donate_time < v.need_times then
			local slider_value = cur_diff_value / per_diff_value * d_per
			value = value + slider_value
			break
		else
			value = value + d_per
		end 
	end

	return value
end

function DragonTempleWGData:GetDrawModelCfg()
	return self.draw_mode_cfg
end

function DragonTempleWGData:GetDonateRewardCfgBySeq(seq)
	return self.donate_reward_cfg[seq]
end

function DragonTempleWGData:GetDonateCutOffTime()
	local cfg = self:GetOtherCfg()
	local time = TimeWGCtrl.Instance:GetServerTime()
	local setting_time = (24 - cfg.rank_summary_time / 100) * 60 * 60
	return TimeWGCtrl.Instance:NowDayTimeEnd(time) - setting_time - time
end

function DragonTempleWGData:CanDonateGetLongHun()
	local longshen_level = self:GetLongShenLevel()
	local cur_level_cfg = self:GetLongShenLevelCfg(longshen_level)
	local daily_longhun = self:GetDailyLongHunValue()

	return daily_longhun < cur_level_cfg.daily_max_longhun
end

-------------------------------------排行End-------------------------------

--------------------------------------------new_start--------------------------------------------
function DragonTempleWGData:GetAddExpSpeed(seq)
	local hatch_name_cfg = self:GetHatchNameCfgBySeq(seq)
	local second_add_exp = self:IsHatchHasPrivilege() and hatch_name_cfg.rmb_buy_second_add_exp or hatch_name_cfg.second_add_exp
	return second_add_exp / self:GetHatchExpAddTimeInterval()
end

function DragonTempleWGData:IsHatchHasPrivilege()
	return self.hatch_rmb_buy_time >= TimeWGCtrl.Instance:GetServerTime()
end

function DragonTempleWGData:GetHatchPrivilegeEndTime()
	return self.hatch_rmb_buy_time
end

function DragonTempleWGData:CalculationHatchAddExpList()
	for i = 0, 4 do
		self:UpdateHatchAddExpList(i)
    end
end

function DragonTempleWGData:IsHatchItemIsBreathNow(seq, level)
	local is_breath = false
	local level = level > 0 and level or 1
	local level_data = self:GetHatchLevelCfg(seq, level)

	if not IsEmptyTable(level_data) then
		is_breath = level_data.need_exp <= 0 and level_data.cost_gold > 0
	end

	return is_breath
end

function DragonTempleWGData:UpdateHatchAddExpList(seq)
	local hatch_item_data = self.hatch_item_list[seq]
	if IsEmptyTable(hatch_item_data) then
		return 
	end

	local is_max_level = self:IsHatchMaxLevel(seq)
	local data = {is_need_check = false, up_level_cd_time = 0}
	local active = self:CanShowHatchItem(seq)

	if active and not is_max_level then
		local level = hatch_item_data.level > 0 and hatch_item_data.level or 1
		local level_data = self:GetHatchLevelCfg(seq, level)
		-- if next_level_data.need_exp > 0 and next_level_data.cost_gold <= 0 then
		if not self:IsHatchItemIsBreathNow(seq, hatch_item_data.level) then
			local add_exp_speed = self:GetAddExpSpeed(seq)
			local need_exp = level_data.need_exp
			local time = TimeWGCtrl.Instance:GetServerTime()
			local add_exp_total_time = math.floor(need_exp / add_exp_speed)

			if need_exp > hatch_item_data.exp then
				data.is_need_check = true
				local totla_time = math.floor((need_exp - hatch_item_data.exp) / add_exp_speed)
				--print_error("计算经验加成时间seq = ",seq,"现有经验 = ",hatch_item_data.exp,"需要经验 = ",need_exp,"速度 = ",add_exp_speed,"需要的时间 = ",math.ceil((need_exp - hatch_item_data.exp) / add_exp_speed))
				data.up_level_cd_time = time + totla_time
				data.up_level_total_time = totla_time
				data.add_exp_total_time = add_exp_total_time
			end
		end
	end

    self.hatch_item_addexp_list[seq] = data
end

function DragonTempleWGData:IsHatchCanUpLevel(seq)
	local hatch_item_data = self.hatch_item_list[seq]
	local level_data = self:GetHatchLevelCfg(seq, hatch_item_data.level)
	if IsEmptyTable(level_data) then
		return false
	end

	if hatch_item_data.exp >= level_data.need_exp then
		return true
	end

	local data = self:GetHatchAutoUplevelInfo(seq)

	if data and data.is_need_check then
		local time = TimeWGCtrl.Instance:GetServerTime()
		local cd_time = data.up_level_cd_time

		return cd_time > 0 and (cd_time <= time or math.abs(time - cd_time) <= 1)
	end

	return false
end

function DragonTempleWGData:GetHatchAutoUplevelInfo(seq)
	return self.hatch_item_addexp_list[seq]
end

function DragonTempleWGData:CanGetHatchDailyReward()
	return self.hatch_daily_reward_flag == 0
end

function DragonTempleWGData:CanAddExpTimer(seq)
	local can_add_exp_time = false
	local hatch_item_info = self:GetHatchItemListInfoBySeq(seq)

	if not IsEmptyTable(hatch_item_info) then
		local current_level_info = self:GetHatchLevelCfg(seq, hatch_item_info.level)

		if not IsEmptyTable(current_level_info) then
			local is_max_level = self:IsHatchMaxLevel(seq)
			-- local is_breach = current_level_info.need_exp <= 0 and current_level_info.cost_gold > 0
			local is_breach = self:IsHatchItemIsBreathNow(seq, hatch_item_info.level)
			local can_up_level = self:IsHatchCanUpLevel(seq)
			
			can_add_exp_time = not is_max_level and not is_breach and not can_up_level
		end
	end

	return can_add_exp_time
end

function DragonTempleWGData:GetHatchExpAddTimeInterval()
	return self.other_cfg.hatch_add_exp_time
end
--------------------------------------------new_end--------------------------------------------


------------------龙战于野------------------------------------------

function DragonTempleWGData:GetShowWildBuyList()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local show_list = {}
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if not IsEmptyTable(self.dragon_wild_buy_cfg) then
		for k, v in pairs(self.dragon_wild_buy_cfg) do
			if (role_level >= v.open_level and server_day >= v.open_day)then
				table.insert(show_list, v)
			end
		end
	end

	if not IsEmptyTable(show_list) then
		table.sort(show_list, SortTools.KeyLowerSorters("seq"))
	end

	return show_list
end

function DragonTempleWGData:GetShowBuyTipList()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local pop_list = {}
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if not IsEmptyTable(self.dragon_wild_buy_cfg) then
		for k, v in pairs(self.dragon_wild_buy_cfg) do
			if (role_level >= v.open_level and server_day >= v.open_day and server_day <= v.close_display_day)then
				table.insert(pop_list, v)
			end
		end
	end

	if not IsEmptyTable(pop_list) then
		table.sort(pop_list, SortTools.KeyLowerSorters("seq"))
	end

	return pop_list
end

function DragonTempleWGData:GetWildIsBuyFlag(seq)
	return self.dragon_rmb_buy_flag[seq] == 1
end

function DragonTempleWGData:ShowWildFunBtn()
	local list = self:GetShowWildBuyList()
    if IsEmptyTable(list) then
        return false
    end

	for i, v in ipairs(list) do
		local is_buy = DragonTempleWGData.Instance:GetWildIsBuyFlag(v.seq)
		if not is_buy then
			return true
		end
	end

	return false
end

function DragonTempleWGData:SetIsShowTip(state)
	self.show_tip = state
end

function DragonTempleWGData:GetIsShowTip()
	local is_show = false
	local list = self:GetShowBuyTipList()
	if self.show_tip then
		for i, v in ipairs(list) do
			local is_buy = self:GetWildIsBuyFlag(v.seq)
			if not is_buy then
				is_show = true
			end
		end
	end

	return is_show
end