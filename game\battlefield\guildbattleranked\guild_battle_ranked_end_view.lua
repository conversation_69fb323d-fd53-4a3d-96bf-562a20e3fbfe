GuildBattleRankedEndView = GuildBattleRankedEndView or BaseClass(SafeBaseView)

function GuildBattleRankedEndView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg()
	self:LoadConfig()
end

function GuildBattleRankedEndView:LoadConfig()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/guild_battle_new_ui_prefab", "layout_guild_battle_new_finish")
end

function GuildBattleRankedEndView:LoadCallBack()
	self.item_list = {}
	local end_data = GuildBattleRankedWGData.Instance:GetGuildBattleEndData()
	local item_data_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneRewardList()
	for i = 1, 3 do
		if item_data_list[i] then
			self.item_list[i] = ItemCell.New(self.node_list["ph_battle_end_" .. i])
			self.item_list[i]:SetData(item_data_list[i])
		end
	end

	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	self.node_list.lbl_battle_end_integral.text.text = (role_info_list.gongxian)

	if role_info_list.side == end_data.winner_side then
		self.node_list.img_battle_end_win.image:LoadSprite(ResPath.GetBigPainting("fuben_finish_bg_3"))
		-- self.node_list.img_battle_end_bg.image:LoadSprite(ResPath.GetGuildBattlePath("guild_battle_end_bg_1"))
	else
		self.node_list.img_battle_end_win.image:LoadSprite(ResPath.GetBigPainting("fuben_finish_bg_4"))
		-- self.node_list.img_battle_end_bg.image:LoadSprite(ResPath.GetGuildBattlePath("guild_battle_end_bg_2"))
	end
	XUI.AddClickEventListener(self.node_list.btn_battle_end, BindTool.Bind1(self.Close, self))

	local close_time = TimeWGCtrl.Instance:GetServerTime() + 10--end_data.delay_kickout_timestamp
	local d_time = close_time - TimeWGCtrl.Instance:GetServerTime()
	if d_time > 0 then
		self:UpdateCloseCountDownTime(1, d_time)
		CountDownManager.Instance:AddCountDown("guild_battle_ranked_close_timer", BindTool.Bind1(self.UpdateCloseCountDownTime, self), BindTool.Bind1(self.CompleteCloseCountDownTime, self), close_time, nil, 1)
	else
		self:CompleteCloseCountDownTime()
	end
end

function GuildBattleRankedEndView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("guild_battle_ranked_close_timer") then
		CountDownManager.Instance:RemoveCountDown("guild_battle_ranked_close_timer")
	end

	if self.item_list ~= nil then
		for k, v in ipairs(self.item_list)do
			v:DeleteMe()
		end
		self.item_list = nil
	end
end

function GuildBattleRankedEndView:OnFlush(param_list, index)

end

function GuildBattleRankedEndView:UpdateCloseCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		self.node_list.lbl_battle_end_exit.text.text = (string.format(Language.GuildBattleRanked.BattleRankedEndTime, math.floor(total_time - elapse_time)))
	end
end

function GuildBattleRankedEndView:CompleteCloseCountDownTime()
	self:Close()
end
