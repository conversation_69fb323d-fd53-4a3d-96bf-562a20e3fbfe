NewComposeView = NewComposeView or BaseClass(SafeBaseView)

local MAX_BASE_VALUE_NUM = 2
local MAX_VALUE_NUM = 18

local NOW_TITLE_IMG = {
	"a3_he_wb_sjq",
	"a3_he_wb_sxq",
	"a3_he_wb_tpq",
}

local NEXT_TITLE_IMG = {
	"a3_he_wb_sjh",
	"a3_he_wb_sxh",
	"a3_he_wb_tph",
}

function NewComposeView:InitComposeXianQiView()
	if self.xianqi_equip_list then
		return
	end

	self.xianqi_equip_list = AsyncListView.New(ComposeXianQiRender, self.node_list.xianqi_equip_list)
	self.xianqi_equip_list:SetSelectCallBack(BindTool.Bind(self.SelectCelllCallBack, self))

	self.xianqi_cur_item = ItemCell.New(self.node_list["xianqi_cur_item"])
	self.xianqi_target_item = ItemCell.New(self.node_list["xianqi_target_item"])
	self.xianqi_cur_item:SetCellBgEnabled(false)
	self.xianqi_cur_item:SetItemTipFrom(ItemTip.FROM_EQUIPMENT)
	self.xianqi_target_item:SetItemTipFrom(ItemTip.FROM_EQUIPBROWSE)
	self.xianqi_target_item:SetCellBgEnabled(false)

	self.xq_stuff_cell_list = {}
	for i = 1, 3 do
		self.xq_stuff_cell_list[i] = ItemCell.New(self.node_list["xq_stuff_cell" .. i])
	end

	self.cur_desc_list_1 = {}
	self.new_desc_list_1 = {}
	self.cur_desc_list_2 = {}
	self.new_desc_list_2 = {}
	self.sx_cur_desc_list = {}
	self.sx_new_desc_list = {}
	local cur_attr_content_1 = self.node_list["cur_attr_content_1"]
	local new_attr_content_1 = self.node_list["new_attr_content_1"]
	local cur_attr_content_2 = self.node_list["cur_attr_content_2"]
	local new_attr_content_2 = self.node_list["new_attr_content_2"]
	local sx_cur_attr_content = self.node_list["sx_cur_attr_content"]
	local sx_new_attr_content = self.node_list["sx_new_attr_content"]

	for i = 1, MAX_BASE_VALUE_NUM do
		self.cur_desc_list_1[i] = ComposeXianQiAttrRender.New(cur_attr_content_1:FindObj("now_attr_value_" .. i))
		self.new_desc_list_1[i] = ComposeXianQiAttrRender.New(new_attr_content_1:FindObj("next_attr_value_" .. i))
	end

	for i = 1, MAX_VALUE_NUM do
		self.cur_desc_list_2[i] = ComposeXianQiAttrRender.New(cur_attr_content_2:FindObj("now_attr_value_" .. i))
		self.new_desc_list_2[i] = ComposeXianQiAttrRender.New(new_attr_content_2:FindObj("next_attr_value_" .. i))
	end

	for i = 1, MAX_VALUE_NUM do
		self.sx_cur_desc_list[i] = ComposeXianQiAttrRender.New(sx_cur_attr_content:FindObj("attr_value_" .. i))
		self.sx_new_desc_list[i] = ComposeXianQiAttrRender.New(sx_new_attr_content:FindObj("attr_value_" .. i))
	end

	self.node_list.btn_upgrade.button:AddClickListener(BindTool.Bind(self.OnClickBtnUpgrade, self))
	self.node_list.btn_xianqi_get_equip.button:AddClickListener(BindTool.Bind(self.OnClickBtnGetWay, self))
	self.node_list.btn_xianqi_change_tab.button:AddClickListener(BindTool.Bind(self.OnClickBtnChangeTab, self))
end

function NewComposeView:DeleteComposeXianQiView()
	if self.xianqi_equip_list then
		self.xianqi_equip_list:DeleteMe()
		self.xianqi_equip_list = nil
	end

	if self.xq_stuff_cell_list then
		for k, v in pairs(self.xq_stuff_cell_list) do
			v:DeleteMe()
		end
		self.xq_stuff_cell_list = nil
	end

	if self.xianqi_cur_item then
		self.xianqi_cur_item:DeleteMe()
		self.xianqi_cur_item = nil
	end

	if self.xianqi_target_item then
		self.xianqi_target_item:DeleteMe()
		self.xianqi_target_item = nil
	end

	if self.cur_desc_list_1 then
		for k, v in pairs(self.cur_desc_list_1) do
			v:DeleteMe()
		end
		self.cur_desc_list_1 = nil
	end

	if self.new_desc_list_1 then
		for k, v in pairs(self.new_desc_list_1) do
			v:DeleteMe()
		end
		self.new_desc_list_1 = nil
	end

	if self.cur_desc_list_2 then
		for k, v in pairs(self.cur_desc_list_2) do
			v:DeleteMe()
		end
		self.cur_desc_list_2 = nil
	end

	if self.new_desc_list_2 then
		for k, v in pairs(self.new_desc_list_2) do
			v:DeleteMe()
		end
		self.new_desc_list_2 = nil
	end

	if self.sx_cur_desc_list then
		for k, v in pairs(self.sx_cur_desc_list) do
			v:DeleteMe()
		end
		self.sx_cur_desc_list = nil
	end

	if self.sx_new_desc_list then
		for k, v in pairs(self.sx_new_desc_list) do
			v:DeleteMe()
		end
		self.sx_new_desc_list = nil
	end

	self.selete_xianqi_part = nil
	self.selete_xianqi_data = nil
end


function NewComposeView:SelectCelllCallBack(cell)
	local data = cell:GetData()
	if data == nil then
		return
	end

	self.selete_xianqi_data = data
	self.selete_xianqi_part = data.index

	self:ComposeXianQiShowIndexCallBack(self.show_index)
	self:FlushComposeXianQiView()
end

function NewComposeView:ComposeXianQiShowIndexCallBack(index)
	self.node_list.xianqi_cur_level:SetActive(index == COMPOSE_TYPE.SHENGJIE)
	self.node_list.xianqi_next_level:SetActive(index == COMPOSE_TYPE.SHENGJIE)
	self.node_list.xianqi_cur_star:SetActive(index == COMPOSE_TYPE.SHENGXING)
	self.node_list.xianqi_next_star:SetActive(index == COMPOSE_TYPE.SHENGXING)
	self.node_list.attr_content_1:SetActive(index == COMPOSE_TYPE.SHENGJIE)
	self.node_list.attr_content_2:SetActive(index == COMPOSE_TYPE.SHENGXING)
end

function NewComposeView:FlushComposeXQEquipList(jump_item_id)
	local data_list = EquipmentWGData.Instance:GetXQComposeDataListByType(self.show_index)
	self.xianqi_equip_list:SetDataList(data_list)
	local equip_index = self:GetComposeXianQiJumpIndex(data_list, jump_item_id)
	self.xianqi_equip_list:JumpToIndex(equip_index)
end

function NewComposeView:GetComposeXianQiJumpIndex(data_list, jump_item_id)
	if not self.xianqi_equip_list or data_list == nil then
		return 1
	end

	local index = self.xianqi_equip_list:GetSelectIndex() or 1
	if jump_item_id ~= nil then
		for k,v in pairs(data_list) do
			if v.equip_data and jump_item_id == v.equip_data.item_id then
				index = k
				self.xianqi_default_index = nil
				break
			end
		end
	end

	if self.xianqi_default_index then
		index = self.xianqi_default_index
		self.xianqi_default_index = nil
	end

	index = index > #data_list and 1 or index
	return index
end

function NewComposeView:ClearComposeXianQiData()
	self.xianqi_cur_item:ClearData()
	self.xianqi_target_item:ClearData()
	self.xianqi_cur_item:SetItemIcon(ResPath.GetCommonImages("a3_ty_suo"))
	self.xianqi_target_item:SetItemIcon(ResPath.GetCommonImages("a3_ty_suo"))

	for i = 1, 3 do
		self.node_list["xq_stuff_cell"..i]:SetActive(false)
		self.xq_stuff_cell_list[i]:ClearData()
	end

	self.node_list["compose_xianqi_down"]:SetActive(false)
	self.node_list["upgrade_remind"]:SetActive(false)

	self.node_list["attr_content_1"]:SetActive(false)
	self.node_list["attr_content_2"]:SetActive(false)

	self.node_list["xianqi_max_text"]:SetActive(false)
	-- self.node_list["xianqi_max_text2"]:SetActive(false)
	self.node_list.xianqi_cur_level:SetActive(false)
	self.node_list.xianqi_next_level:SetActive(false)

	self.node_list["max_level_img"]:SetActive(false)

	local toggle_index = self.show_index % 10

	self.node_list["tips_desc2"].text.text = Language.Compose.ComposeXianQiTips2

	local cur_title_str = NOW_TITLE_IMG[toggle_index] or ""
	local next_title_str = NEXT_TITLE_IMG[toggle_index] or ""
	self.node_list["xianqi_cur_text"].text.text = cur_title_str
	self.node_list["xianqi_target_text"].text.text = next_title_str

	local bundle, asset = ResPath.GetComposeImg(cur_title_str)
	self.node_list.xianqi_cur_state.image:LoadSprite(bundle, asset, function()
		self.node_list.xianqi_cur_state.image:SetNativeSize()
	end)

	local bundle, asset = ResPath.GetComposeImg(next_title_str)
	self.node_list.xianqi_target_state.image:LoadSprite(bundle, asset, function()
		self.node_list.xianqi_target_state.image:SetNativeSize()
	end)

	self.node_list.tips_desc_bg:SetActive(true)
end

function NewComposeView:FlushComposeXianQiView()
	local data = self.selete_xianqi_data
	local equip_data = data.equip_data
	local show_data = data.show_data
	if IsEmptyTable(data) or IsEmptyTable(equip_data) or IsEmptyTable(show_data) then
		return
	end

	if equip_data.item_id <= 0 then
		self:ClearComposeXianQiData()
		local is_show_get = false
		if data.limit_desc then
			self.node_list["tips_desc2"].text.text = data.limit_desc
		else
			is_show_get = true
		end

		self.node_list["btn_xianqi_get_equip"]:SetActive(is_show_get)
		self.node_list["btn_xianqi_change_tab"]:SetActive(not is_show_get)
		self.node_list["xianqi_cur_star"]:SetActive(false)
		self.node_list["xianqi_next_star"]:SetActive(false)
		return
	elseif equip_data.item_id > 0 and data.limit_desc then
		self:ClearComposeXianQiData()
		self.xianqi_cur_item:SetData(equip_data)
		if data.target_equip_data and data.target_equip_data.item_id > 0 then
			self.xianqi_target_item:SetData(data.target_equip_data)
		end
		self.node_list["tips_desc2"].text.text = data.limit_desc

		self.node_list["btn_xianqi_get_equip"]:SetActive(false)
		self.node_list["btn_xianqi_change_tab"]:SetActive(true)

		if self.show_index == COMPOSE_TYPE.SHENGXING then
			local cur_star_res_list2 = GetStarImgResByStar(equip_data.param.star_level)
			for i = 1, 5 do
				self.node_list["xianqi_cur_star" .. i].image:LoadSprite(ResPath.GetCommonImages(cur_star_res_list2[i]))
			end

			local next_star_res_list2 = GetStarImgResByStar(data.target_equip_data.param.star_level)
			for i = 1, 5 do
				self.node_list["xianqi_next_star" .. i].image:LoadSprite(ResPath.GetCommonImages(next_star_res_list2[i]))
			end
		end
		return
	end

	self.node_list.tips_desc_bg:SetActive(false)
	self.node_list.cur_attr_part:SetActive(true)
	self.node_list.new_attr_part:SetActive(true)

	self.xianqi_cur_item:SetData(equip_data)

	self.node_list.max_level_img:SetActive(data.is_max)
	self.node_list["compose_xianqi_down"]:SetActive(not data.is_max)

	local function set_stuff_data(stuff_id, have_num, need_num)
		local is_show = false
		local desc = ""
		need_num = need_num or 0
		if stuff_id and stuff_id > 0 then
			is_show = true
			local have_num_str = have_num
			if stuff_id == 65531 then
				have_num_str = CommonDataManager.ConverMoneyByThousand(have_num)
			else
				have_num_str = have_num
			end
			local color = have_num >= need_num and COLOR3B.GREEN or COLOR3B.PINK
			desc = string.format("<color=%s>%s/%s</color>", color, have_num_str, need_num)
		end

		return is_show, desc
	end

	local toggle_index = self.show_index % 10

	-- self.node_list["xianqi_cur_text"]:SetActive(not data.is_max)
	self.node_list["xianqi_max_text"]:SetActive(data.is_max)
	-- self.node_list["xianqi_max_text2"]:SetActive(data.is_max)
	self.node_list["compose_xianqi_top_right_item"]:SetActive(not data.is_max)

	local btn_name = Language.Compose.ComposeXianQiBtnName[toggle_index] or ""
	local cur_title_str = NOW_TITLE_IMG[toggle_index] or ""
	local next_title_str = NEXT_TITLE_IMG[toggle_index] or ""

	if data.is_max then
		for i = 1, 3 do
			self.node_list["xq_stuff_cell"..i]:SetActive(false)
			self.xq_stuff_cell_list[i]:ClearData()
		end
		self.xianqi_target_item:ClearData()
		self.xianqi_target_item:SetItemIcon(ResPath.GetCommonImages("a3_ty_suo"))
		self.node_list["upgrade_remind"]:SetActive(false)

		self.node_list.max_level_text.text.text = Language.Compose.MaxLevelText[toggle_index] or ""
		self.node_list.xianqi_max_text.text.text = Language.Compose.MaxLevelText2[toggle_index] or ""
		cur_title_str = NEXT_TITLE_IMG[toggle_index] or ""
	else
		for i = 1, 3 do
			local is_show, desc = set_stuff_data(show_data["stuff_id" .. i],  show_data["have_stuff" .. i], show_data["stuff_num" .. i])
			if is_show then
				self.node_list["xq_stuff_cell"..i]:SetActive(true)
				self.xq_stuff_cell_list[i]:SetData({item_id = show_data["stuff_id" .. i], is_bind = 0})
				self.xq_stuff_cell_list[i]:SetRightBottomColorText(desc)
				self.xq_stuff_cell_list[i]:SetRightBottomTextVisible(true)
			else
				self.node_list["xq_stuff_cell"..i]:SetActive(false)
				self.xq_stuff_cell_list[i]:ClearData()
			end
		end

		self.xianqi_target_item:SetData(data.target_equip_data)
		self.node_list["upgrade_remind"]:SetActive(data.show_remind)
	end

	if self.show_index == COMPOSE_TYPE.SHENGJIE then
		local cur_equip_data = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
		local target_equip_data = ItemWGData.Instance:GetItemConfig(data.target_equip_data.item_id)
		self.node_list.xianqi_cur_level.text.text = cur_equip_data and string.format(Language.Compose.ComposeUpJie, cur_equip_data.order) or ""
		self.node_list.xianqi_next_level.text.text = target_equip_data and string.format(Language.Compose.ComposeUpJie, target_equip_data.order) or ""

		local cur_show_list_2 = EquipmentWGData.Instance:GetXQComposeLegendAttr(data.equip_data, false)
		local new_show_list_2 = EquipmentWGData.Instance:GetXQComposeLegendAttr(data.target_equip_data, true)
	
		for i = 1, MAX_VALUE_NUM do
			self.cur_desc_list_2[i]:SetData(cur_show_list_2[i])

			if data.is_max and not IsEmptyTable(cur_show_list_2[i]) then
				new_show_list_2[i] = {is_max = true, show_index = 1 }
			elseif not IsEmptyTable(cur_show_list_2[i]) and not IsEmptyTable(new_show_list_2[i]) then
				--根据策划需求，下一级属性值没有增加的不显示上升箭头（必须确保左右属性值互相对应，否则不生效）.
				if cur_show_list_2[i].name == new_show_list_2[i].name and cur_show_list_2[i].value == new_show_list_2[i].value then
					new_show_list_2[i].not_show_arrow = true
				end
			end
			self.new_desc_list_2[i]:SetData(new_show_list_2[i])
		end
	
		local cur_show_list_1 = EquipmentWGData.Instance:GetXQComposeBaseAttr(data.equip_data, false)
		local new_show_list_1 = EquipmentWGData.Instance:GetXQComposeBaseAttr(data.target_equip_data, true)

		for i = 1, MAX_BASE_VALUE_NUM do
			self.cur_desc_list_1[i]:SetData(cur_show_list_1[i])

			if data.is_max and not IsEmptyTable(cur_show_list_1[i]) then
				new_show_list_1[i] = {is_max = true, show_index = 1 }
			elseif not IsEmptyTable(cur_show_list_1[i]) and not IsEmptyTable(new_show_list_1[i]) then
				--根据策划需求，下一级属性值没有增加的不显示上升箭头（必须确保左右属性值互相对应，否则不生效）.
				if cur_show_list_1[i].name == new_show_list_1[i].name and cur_show_list_1[i].value == new_show_list_1[i].value then
					new_show_list_1[i].not_show_arrow = true
				end
			end

			self.new_desc_list_1[i]:SetData(new_show_list_1[i])
		end

		self.node_list["xianqi_cur_level"]:SetActive(not data.is_max)
	elseif self.show_index == COMPOSE_TYPE.SHENGXING then
		local can_ti_ping = EquipmentWGData.Instance:CheackHasEquipSpecialAttr(data.target_equip_data.item_id)
		if can_ti_ping then
			btn_name = Language.Compose.ComposeXianQiBtnName[3]
			cur_title_str = NOW_TITLE_IMG[3]
			next_title_str = NEXT_TITLE_IMG[3]
		end

		local sx_cur_show_list = EquipmentWGData.Instance:GetXQComposeUSLegendAttr(data.equip_data, false)
		local sx_new_show_list = EquipmentWGData.Instance:GetXQComposeUSLegendAttr(data.target_equip_data, true)

		for i = 1, MAX_VALUE_NUM do
			self.sx_cur_desc_list[i]:SetData(sx_cur_show_list[i])

			if data.is_max and not IsEmptyTable(sx_cur_show_list[i]) then
				sx_new_show_list[i] = {is_max = true, show_index = 1 }
			elseif not IsEmptyTable(sx_cur_show_list[i]) and not IsEmptyTable(sx_new_show_list[i]) then
				--根据策划需求，下一级属性值没有增加的不显示上升箭头（必须确保左右属性值互相对应，否则不生效）.
				if sx_cur_show_list[i].name == sx_new_show_list[i].name and sx_cur_show_list[i].value == sx_new_show_list[i].value then
					sx_new_show_list[i].not_show_arrow = true
				end
			end
			self.sx_new_desc_list[i]:SetData(sx_new_show_list[i])
		end

		if not data.is_max then
			local cur_star_res_list = GetStarImgResByStar(equip_data.param.star_level)
			for i = 1, 5 do
				self.node_list["xianqi_cur_star" .. i].image:LoadSprite(ResPath.GetCommonImages(cur_star_res_list[i]))
			end

			local next_star_res_list = GetStarImgResByStar(data.target_equip_data.param.star_level)
			for i = 1, 5 do
				self.node_list["xianqi_next_star" .. i].image:LoadSprite(ResPath.GetCommonImages(next_star_res_list[i]))
			end
		end

		self.node_list.xianqi_cur_star:SetActive(not data.is_max)
	end

	self.node_list["xianqi_cur_text"].text.text = cur_title_str
	self.node_list["xianqi_target_text"].text.text = next_title_str
	self.node_list.btn_upgrade_text.text.text = btn_name

	local bundle, asset = ResPath.GetComposeImg(cur_title_str)
	self.node_list.xianqi_cur_state.image:LoadSprite(bundle, asset, function()
		self.node_list.xianqi_cur_state.image:SetNativeSize()
	end)

	local bundle, asset = ResPath.GetComposeImg(next_title_str)
	self.node_list.xianqi_target_state.image:LoadSprite(bundle, asset, function()
		self.node_list.xianqi_target_state.image:SetNativeSize()
	end)
end

function NewComposeView:OnClickBtnUpgrade()
	local data = self.selete_xianqi_data
	if IsEmptyTable(data) or IsEmptyTable(data.equip_data) or IsEmptyTable(data.show_data) then
		return
	end

	local show_data = data.show_data

	for i = 1, 3 do
		local stuff_id = show_data["stuff_id" .. i]
		local have_num = show_data["have_stuff" .. i]
		local need_num = show_data["stuff_num" .. i]
		if stuff_id > 0 and need_num > 0 and have_num < need_num then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_id})
			return
		end
	end

	local operate_type = 0
	if self.show_index == TabIndex.other_compose_shengjie then
		operate_type = COMPOSE_EQUIP_OPERA_TYPE.EQUIP_OPERA_TYPE_XIANQI_UPGRADE
	elseif self.show_index == TabIndex.other_compose_shengxing then
		operate_type = COMPOSE_EQUIP_OPERA_TYPE.EQUIP_OPERA_TYPE_XIANQI_UPSTAR
	end

	EquipmentWGCtrl.Instance:SendEquipComposeOperaReq(operate_type, data.show_data.target_equip_id, 0, {data.equip_data.index, 1})
end

function NewComposeView:OnClickBtnGetWay()
	ViewManager.Instance:Open(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_equip)
end

function NewComposeView:OnClickBtnChangeTab()
	ComposeWGCtrl.Instance:Open(TabIndex.other_compose_shengjie, { nil, { other_compose_shengjie = true } })
	self:FlushComposeXQEquipList((((self.selete_xianqi_data or {}).equip_data or {}).item_id) or 0)
end

function NewComposeView:ShowXianQiEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name,
						is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["shenpin_effect_root"]})

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

-------------------------------------------------------------------------------------------
ComposeXianQiRender = ComposeXianQiRender or BaseClass(BaseRender)

function ComposeXianQiRender:__init()
	if not self.upgrade_cell then
		self.upgrade_cell = ItemCell.New(self.node_list["ph_upgrade_item"])
		self.upgrade_cell:SetItemTipFrom(ItemTip.FROM_EQUIPMENT)
	end
	self.node_list["img_is_bag_index"]:SetActive(false)
end

function ComposeXianQiRender:__delete()
	if self.upgrade_cell then
		self.upgrade_cell:DeleteMe()
		self.upgrade_cell = nil
	end
end

function ComposeXianQiRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) or IsEmptyTable(data.equip_data) then
		return
	end

	local equip_data = data.equip_data

	self.node_list["lbl_item_name"].text.text = ""
	self.node_list["rich_item_tip"].text.text = ""

	if equip_data.item_id <= 0 then
		self.upgrade_cell:ClearData()
		self.node_list["img_upgrade_uplevel_remind"]:SetActive(false)
		self.upgrade_cell:SetItemIcon(ResPath.GetEquipIcon(equip_data.index))
		self.upgrade_cell:SetButtonComp(true)

		local desc = ""
		if data.limit_desc then
			desc = data.limit_desc
		else
			local equip_part_str = Language.Stone[equip_data.index]
			desc = string.format("%s【%s】", Language.Equip.EquipUpgradeRenderName, equip_part_str)
		end

		desc = ToColorStr(desc, COLOR3B.DEFAULT)
		self.node_list["lbl_item_name"].text.text = desc
		return
	end

	self.upgrade_cell:SetButtonComp(false)

	local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
	local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])

	self.node_list["lbl_item_name"].text.text = name

	self.node_list["img_upgrade_uplevel_remind"]:SetActive(data.show_remind)

	self.upgrade_cell:SetData(equip_data)
end

function ComposeXianQiRender:OnSelectChange(is_select)
	self.node_list.img9_item_bg_hl:SetActive(is_select)
end

-------------------------------------------------------------------------------
ComposeXianQiAttrRender = ComposeXianQiAttrRender or BaseClass(BaseRender)

function ComposeXianQiAttrRender:OnFlush()
	self.view:SetActive(not IsEmptyTable(self.data))
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.value:SetActive(not self.data.is_max)
	self.node_list.arrow:SetActive(not self.data.is_max and self.data.is_preview and not self.data.not_show_arrow)
	self.node_list.tag:SetActive(not self.data.is_max and self.data.is_preview and self.data.possible > 0)
	self.node_list.type.text.text = not self.data.is_max and self.data.type and Language.Compose.ComposeSpecialType[self.data.type] or ""

	if self.data.is_max then
		self.node_list.name.text.text = Language.Compose.MaxLevelText3[self.data.show_index and self.data.show_index or 1]
	else
		self.node_list.name.text.text = self.data.name
		self.node_list.value.text.text = self.data.value

		if self.data.is_preview and self.data.possible > 0 then
			local bundle, asset = ResPath.GetComposeImg("a3_he_bq_" .. self.data.possible)
			self.node_list.tag.image:LoadSprite(bundle, asset, function()
				self.node_list.tag.image:SetNativeSize()
			end)
		end
	end
end
