GloryCrystalExchangeShopItemRender = GloryCrystalExchangeShopItemRender or BaseClass(BaseRender)

function GloryCrystalExchangeShopItemRender:__init()

end

function GloryCrystalExchangeShopItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GloryCrystalExchangeShopItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["item"])
	self:ChangeStyle()
end

function GloryCrystalExchangeShopItemRender:OnFlush()
	if nil == self.data then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item.item_id)
	if nil == item_cfg then
		print_error("没有配置 ", self.data.item.item_id)
		return
	end

	self.item_cell:SetData({ item_id = self.data.item.item_id, is_bind = self.data.item.is_bind })
	self.item_cell:SetDefaultEff(true)
	self.item_cell:SetBindIconVisible(false)
	self.node_list.item_name.text.text = item_cfg.name

	self.node_list["pz_img"]:SetActive(self.data.item_flag == 1)

	self.item_cell:SetRightBottomTextVisible(false)

	local limit_str_list = GloryCrystalWGData.Instance:ExplainComposeStr(self.data.seq)
	if IsEmptyTable(limit_str_list) then
		self.node_list["buy_limit_text"].text.text = Language.GloryCrystal.NoLimitBuy
	else
		self.node_list["buy_limit_text"].text.text = limit_str_list.str
	end

	local is_can_buy = GloryCrystalWGData.Instance:CanBuyExchangeShopItem(self.data.seq)
	self.node_list["lock"]:SetActive(not is_can_buy)
	self.node_list["limit_text"]:SetActive(not is_can_buy)
	self.node_list["gold"]:SetActive(is_can_buy)
	if not is_can_buy then
		self.node_list["limit_text"].text.text = Language.GloryCrystal.MaxNumConvert
	end

	self:FlushPrice()
end

function GloryCrystalExchangeShopItemRender:FlushPrice()
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(self.data.seq)
	local item_id = shop_cfg.stuff_id_1
	local bundel, asset = ItemWGData.Instance:GetTipsItemIcon(item_id)
	local scale = 0.5
	local vector3 = Vector3(scale, scale, scale)
	if bundel and asset then
		self.node_list["price_icon"].image:LoadSprite(bundel, asset, function()
			self.node_list["price_icon"].image:SetNativeSize()
			self.node_list["price_icon"].transform.localScale = vector3
		end)
	end

	self.node_list["price_text"].text.text = shop_cfg.stuff_num_1
end

function GloryCrystalExchangeShopItemRender:OnSelectChange(is_select)
	self.node_list.select_img:SetActive(is_select)
end

function GloryCrystalExchangeShopItemRender:ChangeStyle()
	local cfg = GloryCrystalWGData.Instance:GetCurViewStyle()
	if not cfg then
		return
	end

	local bg_bundle, bg_asset = ResPath.GetGloryCrystalExchangeShopImg("a3_tsxy_lisd_di21_" .. cfg.color_index)
	self.node_list["bg"].image:LoadSprite(bg_bundle, bg_asset, function()
		self.node_list["bg"].image:SetNativeSize()
	end)

	-- bg_bundle, bg_asset = ResPath.GetGloryCrystalExchangeShopImg("a3_tsxy_lisd_bq_" .. cfg.color_index)
	-- self.node_list.limit_bg.image:LoadSprite(bg_bundle, bg_asset, function()
	-- 	self.node_list.limit_bg.image:SetNativeSize()
	-- end)
end