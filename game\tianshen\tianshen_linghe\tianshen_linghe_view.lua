-- 已屏蔽
TianShenLingHeView = TianShenLingHeView or BaseClass(SafeBaseView)
function TianShenLingHeView:__init()
    self:SetMaskBg()
    self.default_index = TabIndex.tianshen_linghe_uplevel

    local view_bundle = "uis/view/tianshen_linghe_ui_prefab"
    self:AddViewResource(0, view_bundle, "layout_tslh_bg")
    self:AddViewResource(0, view_bundle, "VerticalTabbar")
	self:AddViewResource(TabIndex.tianshen_linghe_uplevel, view_bundle, "layout_tslh_uplevel")
    self:AddViewResource(TabIndex.tianshen_linghe_reslove, view_bundle, "layout_tslh_resolve")
    self:AddViewResource(TabIndex.tianshen_linghe_compose, view_bundle, "layout_linghe_compose")

    self.tab_sub = {}
	self.remind_tab = {
        {RemindName.TianShenLingHeUpLevel},
        {RemindName.TianShenLingHeReslove},
        {RemindName.TianShenLingHeCompose},
	}
end

function TianShenLingHeView:ReleaseCallBack()
    if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

    self:ULReleaseCallBack()
    self:RSReleaseCallBack()
    self:CPReleaseCallBack()
end

function TianShenLingHeView:LoadCallBack()
    if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.TianShenLingHe.TabGrop, self.tab_sub, "uis/view/tianshen_linghe_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.TianShenLingHeView, self.tabbar)
	end
end

function TianShenLingHeView:LoadIndexCallBack(index)
    if index == TabIndex.tianshen_linghe_uplevel then
        self:ULInitView()
    elseif index == TabIndex.tianshen_linghe_reslove then
        self:RSInitView()
    elseif index == TabIndex.tianshen_linghe_compose then
        self:CPInitView()
    end
end

function TianShenLingHeView:ShowIndexCallBack(index)
    if index == TabIndex.tianshen_linghe_uplevel then
        self:ULShowIndexCallBack()
    elseif index == TabIndex.tianshen_linghe_reslove then
        self:RSShowIndexCallBack()
    elseif index == TabIndex.tianshen_linghe_compose then
        self:CPShowIndexCallBack()
    end
end

function TianShenLingHeView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
        if index == TabIndex.tianshen_linghe_uplevel then
            self:SelectFlushULView(v.force_jump_ts_index)
        elseif index == TabIndex.tianshen_linghe_reslove then
            self:FlushResloveView()
        elseif index == TabIndex.tianshen_linghe_compose then
            if k == "all" then
                self.cp_flush_wait_flag = true
                self:CPSelectToggle(v.force_big_type, v.force_small_type, false)
            elseif k == "protocol_change" then
                self.cp_flush_wait_flag = true
                self:FlushCPToggleAllData()
                self:CPSelectToggle(nil, nil, true)
            end
        end
    end
end