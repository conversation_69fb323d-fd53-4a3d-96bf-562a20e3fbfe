SkillPreviewData = SkillPreviewData or BaseClass()

function SkillPreviewData:__init()
	if SkillPreviewData.Instance then
		error("[SkillPreviewData] Attempt to create singleton twice!")
		return
	end

	SkillPreviewData.Instance = self
	self:InitCfg()
end

function SkillPreviewData:InitCfg()
	self.skill_preview_auto = ConfigManager.Instance:GetAutoConfig("skill_preview_auto")
	self.skill_cfg = self.skill_preview_auto.skill
	self.timeline_cfg = self.skill_preview_auto.timeline
	self.role_vo_cfg = self.skill_preview_auto.role_vo
	self.main_role_vo_cfg = self.skill_preview_auto.main_role_vo
end

function SkillPreviewData:__delete()
	self.skill_timeline = nil
	SkillPreviewData.Instance = nil
end

-- 获取对应的配置信息
function SkillPreviewData:GetSkillCfgBySkillId(skill_id)
	return (self.skill_cfg or {})[skill_id]
end

-- 获取对应的配置信息
function SkillPreviewData:GetTimelineCfgByTimeLineId(timeline_id)
	return (self.timeline_cfg or {})[timeline_id]
end

-- 获取对应的配置信息
function SkillPreviewData:GetRoleVoCfgById(id)
	return (self.role_vo_cfg or {})[id]
end

-- 获取对应的属性配置信息
function SkillPreviewData:GetMainRoleVoCfgById(id)
	return (self.main_role_vo_cfg or {})[id]
end

function SkillPreviewData:GetSkillTimeLineDataBySkillId(skill_id)
	if not self.skill_timeline then
		self.skill_timeline = {}
	end

	local data = self.skill_timeline[skill_id]
	if not data then
		data = self:CreateSkillTimeLineData(skill_id)
		self.skill_timeline[skill_id] = data
	end

	return data
end

function SkillPreviewData:CreateSkillTimeLineData(skill_id)
	local cfg = self:GetSkillCfgBySkillId(skill_id)

	if not cfg then
		return nil
	end

	local timeline_str_list = Split(cfg.timeline, ',')
	local timeline_cfg_list = {}

	for i, str in ipairs(timeline_str_list) do
		local str_tab = Split(str, '-')
		local timeline_id = tonumber(str_tab[1])
		local operator_time = tonumber(str_tab[2] or 0)

		local time_line = self:GetTimelineCfgByTimeLineId(timeline_id)
		if time_line then
			local item = {}
			item.timeline_cfg = time_line
			item.operator_time = operator_time
			item.sort_key = operator_time * 10^3 + i
			table.insert(timeline_cfg_list, item)
		else
			print_error("#没拿到时间轴配置,请检查配置,skill_id#", skill_id, str)
		end
	end

	table.sort(timeline_cfg_list, SortTools.KeyUpperSorter("sort_key")) 
	return timeline_cfg_list
end

function SkillPreviewData:GetRoleSkillTime(skill_id)
	local cfg = self:GetSkillCfgBySkillId(skill_id)

	if not cfg then
		return 0
	end

	return cfg.skill_time or 0
end