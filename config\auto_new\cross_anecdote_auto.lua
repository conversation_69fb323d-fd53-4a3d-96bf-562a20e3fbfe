-- K-跨服山海奇闻.xls
local item_table={
[1]={item_id=38926,num=1,is_bind=1},
[2]={item_id=26372,num=3,is_bind=1},
[3]={item_id=26375,num=3,is_bind=1},
[4]={item_id=26373,num=4,is_bind=1},
[5]={item_id=26374,num=4,is_bind=1},
[6]={item_id=26372,num=2,is_bind=1},
[7]={item_id=26375,num=2,is_bind=1},
[8]={item_id=26373,num=3,is_bind=1},
[9]={item_id=26374,num=3,is_bind=1},
[10]={item_id=26372,num=1,is_bind=1},
[11]={item_id=26375,num=1,is_bind=1},
[12]={item_id=26373,num=2,is_bind=1},
[13]={item_id=26374,num=2,is_bind=1},
[14]={item_id=26373,num=1,is_bind=1},
[15]={item_id=26374,num=1,is_bind=1},
[16]={item_id=38927,num=1,is_bind=1},
[17]={item_id=29852,num=5,is_bind=1},
[18]={item_id=29853,num=5,is_bind=1},
[19]={item_id=36979,num=10,is_bind=1},
[20]={item_id=29852,num=3,is_bind=1},
[21]={item_id=29853,num=3,is_bind=1},
[22]={item_id=36979,num=6,is_bind=1},
[23]={item_id=38928,num=1,is_bind=1},
[24]={item_id=29852,num=2,is_bind=1},
[25]={item_id=29853,num=2,is_bind=1},
[26]={item_id=36979,num=5,is_bind=1},
[27]={item_id=38929,num=1,is_bind=1},
[28]={item_id=38930,num=1,is_bind=1},
[29]={item_id=26370,num=5,is_bind=1},
[30]={item_id=36420,num=10,is_bind=1},
[31]={item_id=36416,num=50000,is_bind=1},
[32]={item_id=26370,num=10,is_bind=1},
[33]={item_id=36420,num=20,is_bind=1},
[34]={item_id=36416,num=100000,is_bind=1},
[35]={item_id=26370,num=15,is_bind=1},
[36]={item_id=36420,num=30,is_bind=1},
[37]={item_id=36416,num=150000,is_bind=1},
[38]={item_id=26370,num=20,is_bind=1},
[39]={item_id=36420,num=40,is_bind=1},
[40]={item_id=36416,num=200000,is_bind=1},
[41]={item_id=28875,num=1,is_bind=1},
[42]={item_id=26191,num=1,is_bind=1},
[43]={item_id=26370,num=25,is_bind=1},
[44]={item_id=36420,num=50,is_bind=1},
[45]={item_id=36416,num=250000,is_bind=1},
[46]={item_id=28876,num=1,is_bind=1},
[47]={item_id=36978,num=3,is_bind=1},
[48]={item_id=28877,num=1,is_bind=1},
[49]={item_id=28878,num=1,is_bind=1},
[50]={item_id=36978,num=1,is_bind=1},
[51]={item_id=36978,num=2,is_bind=1},
[52]={item_id=28879,num=1,is_bind=1},
[53]={item_id=29852,num=4,is_bind=1},
[54]={item_id=29853,num=4,is_bind=1},
[55]={item_id=36979,num=8,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
monster={
{},
{monster_seq=1,monster_id=63003,scene_id=5403,scene_type=146,open_day=4,monster_des="你惊扰了程序猿！\n<color=#95d12b>达标最高伤害，将会获得丰厚礼包</color>",monster_strategy="{wordcolor;95d12b;攻略提示}：程序猿经常摸鱼，从小怪中把程序猿揪出来",monster_strategy1="<color=#ff6000>珍惜物品</color>：程序猿会随机捡起场景中的物品获得强大的加成，快<color=#95d12b>打掉物品阻止他！</color>",monster_skill1=73,monster_strategy2="<color=#ff6000>热爱摸鱼</color>：程序猿每隔一段时间就要<color=#95d12b>开始摸鱼了</color>，找出真身可使程序猿进入<color=#95d12b>虚弱</color>状态",monster_skill2=74,chat_monster_strategy="{wordcolor;ff6000;珍惜物品}：程序猿会随机捡起场景中的物品获得强大的加成\n{wordcolor;ff6000;热爱摸鱼}：程序猿每隔一段时间就要开始摸鱼了，找出真身可使程序猿进入{wordcolor;ff6000;虚弱}状态",left_panel_strategy="程序猿摸鱼时，找出正确的<color=#95d12b>程序猿真身</color>使程序猿虚弱；程序猿受到吸引时，<color=#95d12b>打掉增益物品</color>",},
{monster_seq=2,monster_id=63002,scene_id=5402,scene_type=145,open_day=5,monster_des="被五彩斑斓黑逼疯的美术小姐姐！\n<color=#95d12b>达标最高伤害，将会获得丰厚礼包</color>",monster_strategy="{wordcolor;95d12b;攻略提示}：要在规定的时间内调出美术小姐姐需要的颜色",monster_strategy1="<color=#ff6000>神的调色盘</color>：美术小姐姐提出颜色需求，<color=#95d12b>没在时间内调配出来就会被肃清</color>，成功调出对应的颜色可使美术小姐姐进入<color=#95d12b>虚弱</color>状态",monster_skill1=74,monster_strategy2="<color=#ff6000>无尽黑暗</color>：被美术小姐姐释放出技能的话，整个世界都会被黑暗吞噬，<color=#95d12b>无人生还</color>",monster_skill2=75,chat_monster_strategy="{wordcolor;ff6000;神的调色盘}：美术小姐姐提出颜色需求，没在时间内调配出来就会受到攻击，成功调出对应的颜色可使美术小姐姐进入{wordcolor;ff6000;虚弱}状态",left_panel_strategy="站在不同的颜色圈内可以调出不同的色，满足BOSS的要求<color=#95d12b>调出正确的颜色</color>，就可以阻止boss释放秒杀技能。",},
{monster_seq=3,monster_id=63004,scene_id=5404,scene_type=148,open_day=6,monster_des="策划决定为自己的头发复仇\n<color=#95d12b>达标最高伤害，将会获得丰厚礼包</color>",monster_strategy="{wordcolor;95d12b;攻略提示}：策划在偷偷改变世界，有的大侠还没被影响，快打破开关阻止策划",monster_strategy1="<color=#ff6000>挖坑</color>：策划最擅长挖坑，掉入坑里的玩家将会被策划<color=#95d12b>眩晕</color>",monster_skill1=75,monster_strategy2="<color=#ff6000>猛猪突进</color>：策划用尽浑身蛮力撞向一个目标，被撞到会受到大量伤害且眩晕！",monster_skill2=76,monster_strategy3="<color=#ff6000>瞎改</color>：策划动用自己的力量改变世界，结果造成了世界瘫痪，击碎策划的<color=#95d12b>开关</color>救出你的队友！",monster_skill3=77,chat_monster_strategy="{wordcolor;ff6000;挖坑}：策划最擅长挖坑，掉入坑里的玩家将会被策划眩晕\n{wordcolor;ff6000;猛猪突进}：策划用尽浑身蛮力撞向一个目标，被撞到受到伤害且眩晕\n{wordcolor;ff6000;瞎改}：策划动用自己的力量改变世界，结果造成了世界瘫痪，击碎策划的{wordcolor;95d12b;开关}可救出队友！",left_panel_strategy="快速打破策划的<color=#95d12b>开关</color>。\n别踩<color=#95d12b>沙坑</color>。\n躲避策划的<color=#95d12b>肉弹冲击</color>。",},
{monster_seq=4,monster_id=63005,scene_id=5405,scene_type=147,open_day=7,monster_des="BOSS,说的就是我！\n<color=#95d12b>达标最高伤害，将会获得丰厚礼包</color>",monster_strategy="{wordcolor;95d12b;攻略提示}：打工是不可能打工的",monster_strategy1="<color=#ff6000>暴怒火焰</color>：老板的怒火会造成大量伤害，明智的选择是躲开！",monster_skill1=78,monster_strategy2="<color=#ff6000>禁止摸鱼</color>：不好好工作，<color=#95d12b>聚在一起</color>摸鱼的话，就会被老板痛击，跟队友分开，躲避爆炸！",monster_skill2=80,chat_monster_strategy="{wordcolor;ff6000;暴怒火焰}：老板的怒火会造成大量伤害，明智的选择是躲开！\n{wordcolor;ff6000;禁止摸鱼}：不好好工作，聚在一起摸鱼的话，就会被老板痛击，跟队友分开，躲避爆炸！",left_panel_strategy="小心躲避老板的<color=#95d12b>怒火</color>。\nBOSS释放了炸弹时，<color=#95d12b>避免</color>和队友待在一起。",}
},

monster_meta_table_map={
},
monster_param={
{param2=15,param9=10,param10=63007,param11=1000,param13=100000,param14=100000,param15=13,param16=7,param18=3014,},
{monster_id=63003,param1=3,param2=20,param3=8,param4=100000,param5=10,param6=7,param7=1,param8=1,param9=6002,param10=14,param11=50,param12=50,param17=0,},
{monster_id=63002,param3=11015,param4=1000,param5=1,param6=8,param7=63007,param8=100000,param12=0,param17=0,},
{monster_id=63004,param1=4,param2=3001,param3=63006,param4=3002,param5=6,param6=10,param7=6002,param8=63017,param9=3005,param10=11016,param13=1,param14=10,param15=3,param16=3010,param17=5,param18=4,param19=15,param20=5,param21=5,param22=5,param23=100000,},
{monster_id=63005,param1=3,param3=3003,param4=5,param5=0,param6=0,param7=0,param8=0,param12=3,param13=6003,param14=3015,param15=3006,param16=30001,param18=63035,param19=5000,param20=3014,param21=20,param22=10,param23=10,}
},

monster_param_meta_table_map={
},
dialog={
{},
{id=2,content="哎呀 你们居然知道答案",},
{id=3,content="你们对我想要的颜色一无所知！",},
{id=4,content="binggo！！回答正确！",},
{id=5,content="就是你要五彩斑斓黑？",},
{id=6,content="又加班，摸会鱼都不行!!",},
{id=7,content="一时摸鱼一时爽！！",},
{id=8,content="给我回来加班！",},
{id=9,content="这你都答不对，吃我一击！",},
{id=10,content="同时站到正确的答案内可躲避客服小姐姐伤害",},
{id=11,content="跳跃躲避客服小姐姐暴力一击",},
{id=12,content="客服进入虚弱状态 抓紧时间揍她！",},
{id=13,content="我被肥宅策划点名了，躲在%s后面",},
{id=14,content="选择正确的颜色规避美术小姐姐的技能",},
{id=15,content="呜呜呜，痛痛痛~~",},
{id=16,content="天黑请闭眼！！",},
{id=17,content="给我倒下！嘿嘿嘿",time=7,},
{id=18,content="承受我的怒火吧！",}
},

dialog_meta_table_map={
[10]=17,	-- depth:1
[11]=17,	-- depth:1
[12]=17,	-- depth:1
[13]=17,	-- depth:1
[14]=17,	-- depth:1
[15]=17,	-- depth:1
[16]=17,	-- depth:1
[18]=17,	-- depth:1
},
rank_reward={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{min_rank=4,max_rank=10,reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9]},},
{min_rank=11,max_rank=20,reward_item={[0]=item_table[10],[1]=item_table[11],[2]=item_table[12],[3]=item_table[13]},},
{min_rank=21,max_rank=40,reward_item={[0]=item_table[14],[1]=item_table[15]},},
{monster_seq=1,reward_item={[0]=item_table[16],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19]},},
{monster_seq=1,},
{monster_seq=1,reward_item={[0]=item_table[20],[1]=item_table[21],[2]=item_table[22]},},
{monster_seq=1,},
{monster_seq=2,reward_item={[0]=item_table[23],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19]},},
{monster_seq=2,},
{monster_seq=2,},
{monster_seq=2,reward_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[26]},},
{monster_seq=3,reward_item={[0]=item_table[27],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19]},},
{monster_seq=3,},
{monster_seq=3,},
{monster_seq=3,},
{monster_seq=4,reward_item={[0]=item_table[28],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19]},},
{monster_seq=4,min_rank=4,max_rank=10,},
{monster_seq=4,},
{monster_seq=4,}
},

rank_reward_meta_table_map={
[14]=18,	-- depth:1
[10]=14,	-- depth:2
[6]=10,	-- depth:3
[7]=3,	-- depth:1
[19]=7,	-- depth:2
[11]=19,	-- depth:3
[12]=4,	-- depth:1
[15]=11,	-- depth:4
[16]=12,	-- depth:2
[8]=16,	-- depth:3
[20]=8,	-- depth:4
},
grade_reward={
{reward_item={[0]=item_table[29],[1]=item_table[30],[2]=item_table[31]},},
{grade=1,value=3410000,reward_item={[0]=item_table[32],[1]=item_table[33],[2]=item_table[34]},},
{grade=2,value=6680000,reward_item={[0]=item_table[35],[1]=item_table[36],[2]=item_table[37]},},
{grade=3,value=12110000,reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[40]},},
{grade=4,value=14460000,reward_item={[0]=item_table[41],[1]=item_table[42],[2]=item_table[43],[3]=item_table[44],[4]=item_table[45]},},
{monster_seq=1,value=1300000,},
{monster_seq=1,value=3470000,},
{monster_seq=1,value=6160000,},
{monster_seq=1,value=14150000,},
{monster_seq=1,grade=4,value=16575000,reward_item={[0]=item_table[46],[1]=item_table[42],[2]=item_table[47],[3]=item_table[44],[4]=item_table[45]},},
{monster_seq=2,value=1770000,},
{monster_seq=2,value=5000000,},
{monster_seq=2,value=8880000,},
{monster_seq=2,value=17244000,},
{monster_seq=2,grade=4,value=20940000,reward_item={[0]=item_table[48],[1]=item_table[42],[2]=item_table[47],[3]=item_table[44],[4]=item_table[45]},},
{monster_seq=3,value=2230000,},
{monster_seq=3,value=6070000,},
{monster_seq=3,value=10790000,},
{monster_seq=3,grade=3,value=15367500,reward_item={[0]=item_table[47],[1]=item_table[39],[2]=item_table[40]},},
{monster_seq=3,grade=4,value=22117500,reward_item={[0]=item_table[49],[1]=item_table[42],[2]=item_table[47],[3]=item_table[44],[4]=item_table[45]},},
{monster_seq=4,value=2710000,},
{monster_seq=4,grade=1,value=7390000,reward_item={[0]=item_table[50],[1]=item_table[33],[2]=item_table[34]},},
{monster_seq=4,grade=2,value=11030000,reward_item={[0]=item_table[51],[1]=item_table[36],[2]=item_table[37]},},
{monster_seq=4,value=15660000,},
{monster_seq=4,grade=4,value=23077500,reward_item={[0]=item_table[52],[1]=item_table[42],[2]=item_table[47],[3]=item_table[44],[4]=item_table[45]},}
},

grade_reward_meta_table_map={
[18]=23,	-- depth:1
[17]=22,	-- depth:1
[13]=23,	-- depth:1
[14]=19,	-- depth:1
[24]=19,	-- depth:1
[12]=22,	-- depth:1
[9]=19,	-- depth:1
[8]=23,	-- depth:1
[7]=22,	-- depth:1
},
customer_service_question={
{},
{question_id=2,content="蜈蚣，蚂蚁，蜘蛛哪一个没有俸禄？",answer1="蚂蚁",answer2="蜈蚣",answer3="蜘蛛",right_answers=2,reason="(无功不受禄)",},
{question_id=3,content="麒麟到了北极变成了什么？",answer1="郭麒麟",answer2="冰麒麟",answer3="火麒麟",right_answers=2,reason="(冰淇淋)",},
{question_id=4,content="下面哪种动物把龙藏起来了？",answer1="马",answer2="虎",answer3="蛇",right_answers=2,reason="(卧虎藏龙)",},
{question_id=5,content="天道中谁的腿最长",answer1="火",answer2="木",answer3="金",reason="(火腿肠)",},
{question_id=6,content="托尼老师出门一定会带什么",answer1="发蜡",answer2="水",answer3="手机",right_answers=2,reason="(拖泥带水)",},
{question_id=7,content="“天生我材必有用“的下一句",answer1="无尽长江滚滚来",answer2="一行白鹭上青天",answer3="千金散尽还复来",right_answers=3,reason="",},
{question_id=8,content="哪种花最没力？",answer1="茉莉花",answer2="玫瑰花",answer3="月季花",reason="(好一朵美丽的茉莉花)",},
{question_id=9,content="愚公移山的时候唱什么歌？",answer1="亮晶晶",answer2="好汉歌",answer3="香水有毒",reason="(一闪一闪亮晶晶)",},
{question_id=10,content="布最怕什么？",answer1="刀",answer2="剪子",answer3="一万",right_answers=3,reason="(不怕一万就怕万一)",}
},

customer_service_question_meta_table_map={
},
art_question={
{content="红",red_num=1,blue_num=0,an_res="Effects_z_quan_hongse",img_res_id="color_1",ask_text="今天的心情是：红",right_str="1红",},
{question_id=2,content="绿",yellow_num=1,an_res="Effects_z_quan_lvse",img_res_id="color_5",ask_text="今天的心情是：绿",hint_text="<color=#95d12b><size=20>提示：站在不同的颜色光圈组合成特殊颜色</size></color>",right_str="1蓝1黄",},
{question_id=3,content="橘黄",yellow_num=2,an_res="Effects_z_quan_juhuang",img_res_id="color_7",ask_text="今天的心情是：橘黄",hint_text="<color=#95d12b><size=21>提示：站在不同的颜色光圈组合成特殊颜色</size></color>",right_str="1红2黄",},
{question_id=4,content="橘红",red_num=2,blue_num=0,yellow_num=1,an_res="Effects_z_quan_juhong",img_res_id="color_8",ask_text="今天的心情是：橘红",hint_text="<color=#95d12b><size=22>提示：站在不同的颜色光圈组合成特殊颜色</size></color>",right_str="2红1黄",},
{question_id=5,blue_num=2,yellow_num=1,hint_text="<color=#95d12b><size=23>提示：站在不同的颜色光圈组合成特殊颜色</size></color>",},
{question_id=6,content="红紫",red_num=2,an_res="Effects_z_quan_hongzi",img_res_id="color_11",ask_text="今天的心情是：红紫",hint_text="<color=#95d12b><size=24>提示：站在不同的颜色光圈组合成特殊颜色</size></color>",right_str="2红1蓝",},
{question_id=7,content="蓝紫",blue_num=2,an_res="Effects_z_quan_lanzi",img_res_id="color_12",ask_text="今天的心情是：蓝紫",hint_text="<color=#95d12b><size=25>提示：站在不同的颜色光圈组合成特殊颜色</size></color>",right_str="1红2蓝",},
{question_id=8,content="草绿",yellow_num=2,an_res="Effects_z_quan_caolv",img_res_id="color_9",ask_text="今天的心情是：草绿",hint_text="<color=#95d12b><size=26>提示：站在不同的颜色光圈组合成特殊颜色</size></color>",right_str="1蓝2黄",},
{question_id=9,hint_text="<color=#95d12b><size=27>提示：站在不同的颜色光圈组合成特殊颜色</size></color>",},
{question_id=10,hint_text="<color=#95d12b><size=28>提示：站在不同的颜色光圈组合成特殊颜色</size></color>",}
},

art_question_meta_table_map={
[9]=5,	-- depth:1
[10]=6,	-- depth:1
[7]=1,	-- depth:1
[3]=1,	-- depth:1
},
programmer_guise={
{is_boss=1,},
{monster_id=63009,},
{monster_id=63010,},
{monster_id=63011,},
{monster_id=63012,},
{monster_id=63013,},
{monster_id=63014,},
{monster_id=63015,},
{monster_id=63016,},
{monster_id=63029,},
{monster_id=63030,},
{monster_id=63031,},
{monster_id=63032,},
{monster_id=63033,},
{monster_id=63034,}
},

programmer_guise_meta_table_map={
[2]=1,	-- depth:1
[3]=2,	-- depth:2
},
programmer_item={
{},
{monster_id=63022,goods_name="花酒",goods_used_desc="程序员喝下<color=#ff6000>花酒</color>",goods_used_desc_1="感觉头晕目眩，身体虚弱起来",goods_fail_desc="<color=#ff6000>花酒</color>被打破，程序猿很气愤",},
{monster_id=63023,buff_id=3009,goods_name="大宝剑",goods_used_desc="程序猿捡起<color=#ff6000>大宝剑</color>",goods_used_desc_1="感觉热血沸腾，处于无敌状态",goods_fail_desc="<color=#ff6000>大宝剑</color>被打破，程序猿很气愤",},
{monster_id=63027,goods_name="生发剂",goods_used_desc="程序猿捡起<color=#ff6000>生发剂</color>",goods_used_desc_1="头发长了，但是身体变弱了",goods_fail_desc="<color=#ff6000>生发剂</color>被打破，程序猿很气愤",},
{monster_id=63025,buff_id=3013,goods_name="腰椎康复指南",goods_used_desc="程序猿阅读起<color=#ff6000>康复指南</color>",goods_used_desc_1="坐直了腰也好了，自身防御提升",goods_fail_desc="<color=#ff6000>腰椎康复指南</color>被打破，程序猿很气愤",},
{monster_id=63026,goods_name="鼓励师",goods_used_desc="程序猿抱起了<color=#ff6000>鼓励师</color>",goods_used_desc_1="一顿放松之后，身体虚弱起来",goods_fail_desc="<color=#ff6000>程序猿鼓励师</color>被赶走，程序猿很气愤",},
{monster_id=63020,buff_id=3013,goods_name="小花花",goods_used_desc="程序猿捡到<color=#ff6000>小花花</color>",goods_used_desc_1="感觉自己更帅了，自身防御提升",goods_fail_desc="<color=#ff6000>小花花</color>被打破，程序猿很气愤",}
},

programmer_item_meta_table_map={
},
gift={
{},
{monster_seq=1,item_id=28876,des_res="sh_wenan2",},
{monster_seq=2,item_id=28877,des_res="sh_wenan3",},
{monster_seq=3,item_id=28878,des_res="sh_wenan4",},
{monster_seq=4,item_id=28879,des_res="sh_wenan5",}
},

gift_meta_table_map={
},
create_monster_posi={
{pos1_x=31,pos1_y=52,pos2_x=22,pos2_y=39,pos3_x=35.7,pos3_y=61.7,pos4_x=31,pos4_y=52,},
{pos1_x=41,pos1_y=55,pos2_x=55,pos2_y=39,pos3_x=47.2,pos3_y=61.7,pos4_y=55,},
{pos1_x=52,pos1_y=51,pos2_x=39,pos2_y=57,pos3_x=55.7,pos3_y=55.7,pos4_x=52,pos4_y=51,},
{pos1_x=57,pos1_y=39,pos2_x=41,pos2_y=29,pos3_x=61.7,pos3_y=45.7,pos4_x=57,},
{pos1_x=53,pos1_y=27,pos3_x=62.2,pos3_y=29.75,pos4_x=53,pos4_y=27,},
{pos1_x=41,pos1_y=22,pos3_x=57.7,pos3_y=21.7,pos4_y=22,},
{pos1_x=30,pos1_y=26,pos3_x=50,pos4_x=30,pos4_y=26,},
{pos1_x=24,pos1_y=39,pos3_x=40,pos4_x=24,},
{pos3_x=30,pos4_x=22,},
{pos3_x=25.7,pos3_y=21,pos4_x=55,},
{pos3_x=20.7,pos3_y=30,pos4_x=39,pos4_y=57,},
{pos3_x=16.7,pos3_y=38,pos4_y=29,},
{pos3_x=18.7,pos3_y=46,pos4_x=32.7,},
{pos3_x=22.7,pos3_y=52.7,pos4_x=48.7,},
{pos3_x=28.7,pos3_y=59,pos4_x=40,pos4_y=46,},
{pos3_y=0,pos4_x=40,pos4_y=31,},
{pos4_x=40.7,},
{pos4_x=35.7,pos4_y=46.7,}
},

create_monster_posi_meta_table_map={
[17]=16,	-- depth:1
[18]=16,	-- depth:1
},
art_center_color={
{red_num=3,},
{content="黄",yellow_num=3,effect_color="Effects_z_quan_huangse",},
{content="蓝",blue_num=3,effect_color="Effects_z_quan_lanse",},
{content="橘黄",red_num=1,yellow_num=2,effect_color="Effects_z_quan_juhuang",},
{content="橘红",red_num=2,yellow_num=1,effect_color="Effects_z_quan_juhong",},
{content="草绿",blue_num=1,yellow_num=2,effect_color="Effects_z_quan_caolv",},
{content="墨绿",blue_num=2,yellow_num=1,effect_color="Effects_z_quan_molv",},
{content="红紫",red_num=2,blue_num=1,effect_color="Effects_z_quan_hongzi",},
{content="蓝紫",red_num=1,blue_num=2,effect_color="Effects_z_quan_lanzi",}
},

art_center_color_meta_table_map={
},
buff_effect={
{},
{buff_type=10,buff_name="无敌",buff_des="<name>热血沸腾，变为无敌状态",},
{buff_type=46,buff_name="防御提升",buff_des="对<name>造成的伤害减少",}
},

buff_effect_meta_table_map={
},
other_default_table={wait_summary_time=2355,open_day=999,end_day=1000,cs_question_pos="40,54|25,45|56,44",cs_question_pos_range=6,art_red_pos="40,23",art_blue_pos="27,46",art_yellow_pos="53,46",art_radius=16,art_invaild_radius=5,succ_res="Effect_boss_guangxian_h",defeat_res="Effect_boss_guangxian_huise",answer_delay_time=5,pg_kill_not_boss_buckle=0,pg_kill_not_boss_add_buff=3007,time_length=2,monster_not_die_buff=1009,pg_guise_succ_add_buff=3013,boss_la_xuanyun_buff=3010,scheme_clash_xuanyun_buff=3011,scheme_clash_time=10,speed=22,kick_out_time=6,warning_width=2,warning_length=30,},

monster_default_table={monster_seq=0,monster_id=63001,monster_pos="40,39",scene_id=5401,scene_type=144,open_day=3,challenge_time=180,can_relive=0,is_not_die=1,monster_des="客服小姐姐决定毁灭这个世界！\n<color=#95d12b>达标最高伤害，将会获得丰厚礼包</color>",monster_strategy="{wordcolor;95d12b;攻略提示}：客服小姐姐释放技能后会进入虚弱状态，把握机会",monster_strategy1="<color=#ff6000>灵魂拷问</color>：客服小姐姐整天被问问题，头痛欲裂，反过来对大侠进行灵魂拷问，<color=#95d12b>答错将受到大量伤害！</color>，答对可使客服进入<color=#95d12b>虚弱</color>状态",monster_skill1=72,monster_strategy2="<color=#ff6000>暴力一击</color>：客服小姐姐的愤怒一击，造成超高伤害！全员成功躲避可使客服进入<color=#95d12b>虚弱</color>状态",monster_skill2=73,monster_strategy3="",monster_skill3=0,monster_strategy4="",monster_skill4=0,chat_monster_strategy="{wordcolor;ff6000;灵魂拷问}：客服小姐姐整天被问问题，头痛欲裂，反过来对大侠进行灵魂拷问{wordcolor;ff6000;答错将受到大量伤害！}，答对可使客服进入{wordcolor;ff6000;虚弱}状态\n{wordcolor;ff6000;暴力一击}：客服小姐姐的愤怒一击，造成超高伤害！真的超高那种！！，全员成功躲避可使客服进入{wordcolor;ff6000;虚弱}状态",left_panel_strategy="全员站在<color=#95d12b>正确的答案</color>圈内可躲避boss大招，使用<color=#95d12b>跳跃</color>技能可躲避boss大招",},

monster_param_default_table={monster_id=63001,param1=7,param2=10,param3=10,param4=11014,param5=63007,param6=2,param7=10,param8=11013,param9=0,param10=0,param11=0,param12=1000,param13=0,param14=0,param15=0,param16=0,param17=3,param18=0,param19=0,param20=0,param21=0,param22=0,param23=0,param24=0,param25=0,},

dialog_default_table={id=1,content="现在到我向你们提问了",time=5,is_show=1,},

rank_reward_default_table={monster_seq=0,min_rank=1,max_rank=3,reward_item={[0]=item_table[53],[1]=item_table[54],[2]=item_table[55]},},

grade_reward_default_table={monster_seq=0,grade=0,value=1110000,reward_item={[0]=item_table[50],[1]=item_table[30],[2]=item_table[31]},},

customer_service_question_default_table={question_id=1,content="狼来了，猜一种水果",answer1="杨桃",answer2="苹果",answer3="芒果",right_answers=1,res1="Effect_boss_h",res2="Effect_boss_Lan",res3="Effect_boss_h_s",color1="#ff9696",color2="#90f0ff",color3="#fff59e",bg_res1="sh_hong",bg_res2="sh_lan",bg_res3="sh_huang",enter_res1="Effect_boss_h_chixv",enter_res2="Effect_boss_Lan_chixv",enter_res3="Effect_boss_hs_chixv",reason="(羊逃)",},

art_question_default_table={question_id=1,content="墨绿",red_num=0,blue_num=1,yellow_num=0,red_res="Effects_fazhen01_hongse",blue_res="Effects_fazhen01_lan",yellow_res="Effects_fazhen01_huang",an_res="Effects_z_quan_molv",img_res_id="color_10",ask_text="今天的心情是：墨绿",hint_text="<color=#95d12b><size=20>提示：所有队员站在对应的颜色上</size></color>",right_str="2蓝1黄",},

programmer_guise_default_table={monster_id=63008,is_boss=0,},

programmer_item_default_table={monster_id=63021,buff_id=3006,goods_name="大力丸",goods_used_desc="程序猿吞下<color=#ff6000>大力药丸</color>",aim_offset="0|1",result_type=1,goods_used_desc_1="拳头变硬了，但是身体变弱了",goods_fail_desc="<color=#ff6000>大力药丸</color>被打破，程序猿很气愤",},

gift_default_table={monster_seq=0,item_id=28875,discount=1,des_res="sh_wenan1",},

create_monster_posi_default_table={pos1_x=0,pos1_y=0,pos2_x=0,pos2_y=0,pos3_x=0,pos3_y=16,pos4_x=41,pos4_y=39,},

art_center_color_default_table={content="红",red_num=0,blue_num=0,yellow_num=0,effect_color="Effects_z_quan_hongse",},

buff_effect_default_table={buff_type=74,buff_name="虚弱",buff_des="<name>进入虚弱，受到的伤害提升",}

}

