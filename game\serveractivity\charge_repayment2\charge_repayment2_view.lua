--充值回馈
ChargeRepayment2 = ChargeRepayment2 or BaseClass(SafeBaseView)

function ChargeRepayment2:__init()
	self:AddViewResource(0, "uis/view/charge_repayment2_ui_prefab", "layout_activity_charge_repayment2")
	self.payment_item_list = {}
	self.open_tween = nil
	self.close_tween = nil
end

function ChargeRepayment2:ReleaseCallBack()
	if nil ~= self.payment_item_list then
		for k,v in ipairs(self.payment_item_list) do
			v:DeleteMe()
		end
		self.payment_item_list = {}
	end

	if CountDownManager.Instance:HasCountDown("act_payment2") then
		CountDownManager.Instance:RemoveCountDown("act_payment2")
	end
end

function ChargeRepayment2:OpenCallBack()
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2, 
			opera_type = RA_CHARGE_REPAYMENT2_OPERA_TYPE.RA_CHARGE_REPAYMENT2_OPERA_TYPE_QUERY_INFO})
end

function ChargeRepayment2:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_recharge, BindTool.Bind(self.OnClickRecharge, self))
	for i=1,6 do
		XUI.AddClickEventListener(self.node_list["Button_get_reward_"..i], BindTool.Bind(self.OnClickGiftItem, self,i))
	end
end

function ChargeRepayment2:OnClickRecharge()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end

function ChargeRepayment2:ShowIndexCallBack()
	-- self:Flush()
end

function ChargeRepayment2:RefreshView()
	self:Flush()
end

function ChargeRepayment2:OnFlush()
	self:UpdateScrollView()
	self:FlushPayMentlList()
	self:UpdateRemainTimeCD()
	local total_recharge = ChargeRepayment2WGData.Instance:GetTotalRecharge()
	self.node_list.lbl_recharge_num.text.text = total_recharge
end

function ChargeRepayment2:UpdateScrollView()
	local charge_repayment2_cfg = ChargeRepayment2WGData.Instance:GetPaymentData()
	
	local payment_item_num = #charge_repayment2_cfg
	self.payment_item_num = payment_item_num
	
	for i = 1, payment_item_num  do
		local item = self.payment_item_list[i]
		if nil == item then
			--item = PayMentItemRender.New()
			item = ItemCell.New(self.node_list["ph_item_"..i])
			self.payment_item_list[i] = item
		end
	end
end

function ChargeRepayment2:OnClickGiftItem(index)
	local param_t = {rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2, 
		opera_type = RA_CHARGE_REPAYMENT2_OPERA_TYPE.RA_CHARGE_REPAYMENT2_OPERA_TYPE_FETCH_REWARD, param_1 = index - 1}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	AudioService.Instance:PlayRewardAudio()
end
 
function ChargeRepayment2:FlushPayMentlList()
	local data = ChargeRepayment2WGData.Instance:GetPaymentData()
	-- table.sort(data, SortTools.KeyUpperSorter("charge_value"))
	for k,v in ipairs(self.payment_item_list) do
		v:SetActive(nil ~= data[k])
		if nil ~= data[k] then
			v:SetData(data[k].reward_item)
		end
		self.node_list["ph_gold_num_pos_"..k].text.text =string.format(Language.Recharge.CurAddMoneyNumLimit,data[k].charge_value) 
		local has_fetch_reward_flag = ChargeRepayment2WGData.Instance:GetRewardHasFetchFlag()
		local oga_fetch_flag = bit:d2b(has_fetch_reward_flag)
		local is_auto_fetch = oga_fetch_flag[33 - k] == 1  --是否领取
		
		local reward_active_flag = ChargeRepayment2WGData.Instance:GetRewardHasActiveFlag()
		local oga_active_flag = bit:d2b(reward_active_flag)
		local is_auto_active = oga_active_flag[33 - k] == 1  ---是否激活

		local isgray = (not is_auto_active) or is_auto_fetch  

		if is_auto_fetch then
			--v:MakeGray(isgray)
			self.node_list["img_lingqu_"..k]:SetActive(is_auto_fetch)
			self.node_list["get_reward_text_"..k].text.text = Language.Recharge.YiLingQu--"已领取"
			XUI.SetButtonEnabled(self.node_list["Button_get_reward_"..k], false)
			--self.node_list["Button_get_reward_"..k]:SetActive(false)
		else
			if is_auto_active then 
			self.node_list["Button_get_reward_"..k]:SetActive(true)
			self.node_list["get_reward_text_"..k].text.text = Language.Welfare.LingQu--Language.Achieve.KeLingQu--"可领取"
			XUI.SetButtonEnabled(self.node_list["Button_get_reward_"..k], true)
				--v:MakeGray(false)
				--self.node_list["img_lingqu_"..k]:SetActive(false)
			else
				--self.node_list["Button_get_reward_"..k]:SetActive(false)
				self.node_list["get_reward_text_"..k].text.text = Language.Welfare.LingQu--Language.LightTower.LabelIsCantReceive--"不可领取"
				XUI.SetButtonEnabled(self.node_list["Button_get_reward_"..k], false)
				--v:MakeGray(true)
				--self.node_list["img_lingqu_"..k]:SetActive(false)
			end
			
			--v:MakeGray(false)
			self.node_list["img_lingqu_"..k]:SetActive(is_auto_fetch)
		end
	end
end

function ChargeRepayment2:UpdateRemainTimeCD()
	local act_statu =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2)
	if CountDownManager.Instance:HasCountDown("act_payment2") then
		CountDownManager.Instance:RemoveCountDown("act_payment2")
	end
	if act_statu and ACTIVITY_STATUS.OPEN == act_statu.status then
		local next_time = act_statu.next_time or 0
		self:UpdataRollerTime(TimeWGCtrl.Instance:GetServerTime(), next_time)
		CountDownManager.Instance:AddCountDown("act_payment2", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), next_time, nil, 1)	
	else
		self:CompleteRollerTime()
	end
end

function ChargeRepayment2:UpdataRollerTime(elapse_time, total_time)
	local time = total_time - elapse_time
	if time > 0 then
		local format_time = TimeUtil.Format2TableDHMS(time)
		local str_list = Language.Common.TimeList
		local time_str = ""
		if format_time.day > 0 then
			time_str = format_time.day .. str_list.d
		end
		if format_time.hour > 0 then
			time_str = time_str .. format_time.hour .. str_list.h
		end
		time_str = time_str .. format_time.min .. str_list.min
		
		time_str = time_str ..format_time.s.. str_list.s
		
		

		self.node_list.lbl_remain_time.text.text = time_str
	end
end

function ChargeRepayment2:CompleteRollerTime()
	self.node_list.lbl_remain_time.text.text = "0"
end

function ChargeRepayment2:PlayTween()
	self.open_tween = UITween.ShowFadeUp
end

function ChargeRepayment2:CloseCallBack()
	self.open_tween = nil
end