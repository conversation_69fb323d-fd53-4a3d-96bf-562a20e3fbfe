require("game/sworn/sworn_view")
require("game/sworn/sworn_start_view")
require("game/sworn/sworn_imporint_view")
require("game/sworn/sworn_apply_view")
require("game/sworn/sworn_protocol_view")
require("game/sworn/sworn_invite_view")
require("game/sworn/sworn_suit_view")
require("game/sworn/sworn_uplevel_view")
require("game/sworn/sworn_upstar_view")
require("game/sworn/sworn_suit_attr")
require("game/sworn/sworn_wg_data")
require("game/sworn/sworn_suit_wg_data")
require("game/sworn/sworn_task_view")
require("game/sworn/sworn_taoyuan_view")
require("game/sworn/sworn_build_wg_data")

SwornWGCtrl = SwornWGCtrl or BaseClass(BaseWGCtrl)

function SwornWGCtrl:__init()
	if SwornWGCtrl.Instance ~= nil then
		print_error("[SwornWGCtrl] attempt to create singleton twice!")
		return
	end

	SwornWGCtrl.Instance = self
	self.view = SwornView.New(GuideModuleName.SwornView)
	self.data = SwornWGData.New()
	self.protocol_view = SwornProtocolView.New()
	self.invite_view = SwornInviteView.New()
	self.equip_attr_view = SwornEquipAttrView.New()
	self.Sworn_invite_view = Alert.New()
	self:RegisterAllProtocals()


	self.need_open_sworn_view = false
	self.be_invite = false
	self.be_invite_protocol = {}

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function SwornWGCtrl:__delete()
	SwornWGCtrl.Instance = nil

	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.protocol_view then
		self.protocol_view:DeleteMe()
		self.protocol_view = nil
	end

	if self.equip_attr_view then
		self.equip_attr_view:DeleteMe()
		self.equip_attr_view = nil
	end

	if self.invite_view then
		self.invite_view:DeleteMe()
		self.invite_view = nil
	end

	if self.Sworn_invite_view then
		self.Sworn_invite_view:DeleteMe()
		self.Sworn_invite_view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	self.be_invite_protocol = nil
end

function SwornWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSJieYiOperate)
	self:RegisterProtocol(SCJieYiBaseInfo, "OnSCJieYiBaseInfo")
	self:RegisterProtocol(SCJieYiPartInfo, "OnSCJieYiPartInfo")
	self:RegisterProtocol(SCJieYiPartUpdate, "OnSCJieYiPartUpdate")
	self:RegisterProtocol(SCJieYiTeamInfo, "OnSCJieYiTeamInfo")
	self:RegisterProtocol(SCJieYiTeamList, "OnSCJieYiTeamList")
	self:RegisterProtocol(SCJieYiTeamJoinInvite, "OnSCJieYiTeamJoinInvite")
	self:RegisterProtocol(SCJieYiTeamJoinReq, "OnSCJieYiTeamJoinReq")
	self:RegisterProtocol(SCJieYiTeamVoteInfo, "OnSCJieYiTeamVoteInfo")

	self:RegisterProtocol(SCJieYiTeamBuildAllInfo, "OnSCJieYiTeamBuildAllInfo")
	self:RegisterProtocol(SCJieYiTeamBuildTaskUpdate, "OnSCJieYiTeamBuildTaskUpdate")
	self:RegisterProtocol(SCJieYiTeamBuildLevelUpdate, "OnSCJieYiTeamBuildLevelUpdate")
	self:RegisterProtocol(SCJieYiTeamBuildFanliRewardUpdate, "OnSCJieYiTeamBuildFanliRewardUpdate")
	self:RegisterProtocol(SCJieYiTeamBuildActiveUpdate, "OnSCJieYiTeamBuildActiveUpdate")
end

-- 金兰印记等级  没激活 0级
function SwornWGCtrl:OnSCJieYiBaseInfo(protocol)
	SwornWGData.Instance:SetJinLanLevel(protocol)
	RemindManager.Instance:Fire(RemindName.Sworn_Imprint)
	RemindManager.Instance:Fire(RemindName.Sworn_Apply)
	HomesWGCtrl.Instance:FlushHomesView()
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.sworn_imprint)
	end
end

--金兰装备信息
function SwornWGCtrl:OnSCJieYiPartInfo(protocol)
	self.data:SetSuitAllInfo(protocol)
	if self.view:IsOpen() then
		RemindManager.Instance:Fire(RemindName.Sworn_Suit)
		RemindManager.Instance:Fire(RemindName.Sworn_Upstar)
		RemindManager.Instance:Fire(RemindName.Sworn_Uplevel)
	end

	RemindManager.Instance:Fire(RemindName.Sworn_Equip)
	HomesWGCtrl.Instance:FlushHomesView()
end

--金兰装备更新
function SwornWGCtrl:OnSCJieYiPartUpdate(protocol)
	self.data:UpdateSuitInfo(protocol)
	if self.view:IsOpen() then
		self.view:ChangeSuitTabStatus()
		self.view:Flush(TabIndex.sworn_suit)
		self.view:Flush(TabIndex.sworn_upstar)
		self.view:Flush(TabIndex.sworn_uplevel)
		RemindManager.Instance:Fire(RemindName.Sworn_Suit)
		RemindManager.Instance:Fire(RemindName.Sworn_Upstar)
		RemindManager.Instance:Fire(RemindName.Sworn_Uplevel)
	end

	if self.equip_attr_view:IsOpen() then
		self.equip_attr_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.Sworn_Equip)
	HomesWGCtrl.Instance:FlushHomesView()
end

function SwornWGCtrl:GetViewSelectSuit()
	if self.view:IsOpen() then
		return self.view.select_suit_index
	end

	return -1
end

function SwornWGCtrl:OnSCJieYiTeamInfo(protocol)
	-- print_error("自己结义队伍信息",protocol)
	self.data:SetJieYiTeamInfo(protocol)

	if self.need_open_sworn_view then
		self.need_open_sworn_view = false

		if not self.view:IsOpen() then
			self.view:Open()
		else
			self.view:Flush()
		end
	else
		if self.view:IsOpen() then
			self.view:Flush()
		end
	end

	if self.invite_view:IsOpen() then
		self.invite_view:Flush()
	end

	GlobalEventSystem:Fire(OtherEventType.Sworn_State_Change)
	RemindManager.Instance:Fire(RemindName.Sworn_Build_Task)
	RemindManager.Instance:Fire(RemindName.Sworn_Build_Taoyuan)
end

function SwornWGCtrl:OnSCJieYiTeamList(protocol)
	-- print_error("结义队伍信息",protocol)
	self.data:SetJieYiTeamList(protocol)

	local sworn_state = self.data:GetMySwornState()

	if sworn_state ~= SwornWGData.SWORN_TYPE.HAS_SWORN then
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.sworn_apply)
		end
	end
end

-- 结义被邀请收到此协议
function SwornWGCtrl:OnSCJieYiTeamJoinInvite(protocol)
	-- print_error("结义被邀请收到此协议",protocol)
	self.be_invite_protocol = protocol
	local state = self.data:IsSwornNow()

	self.Sworn_invite_view:SetOkFunc(function ()
		if state then
			self:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_INVITE_RET, protocol.jieyi_id, protocol.invite_player.uid, 1)
		else
			if self.protocol_view and not self.protocol_view:IsOpen() then
				self.protocol_view:Open()
				self.be_invite = true
			end

			self.need_open_sworn_view = true
		end
	end)

	self.Sworn_invite_view:SetCancelFunc(function ()
		self.be_invite = false
		self:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_INVITE_RET, self.be_invite_protocol.jieyi_id, self.be_invite_protocol.invite_player.uid, 0)
	end)


	self.Sworn_invite_view:SetCloseFunc(function ()
		self.be_invite = false
		self:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_INVITE_RET, self.be_invite_protocol.jieyi_id, self.be_invite_protocol.invite_player.uid, 0)
	end)

	self.Sworn_invite_view:SetLableString(string.format(Language.Sworn.SwornInviteDesc, protocol.invite_player.name))
	self.Sworn_invite_view:SetLableRectWidth(460)
	self.Sworn_invite_view:Open()
end

function SwornWGCtrl:OnSCJieYiTeamJoinReq(protocol)
	-- print_error("主动申请加入结义",protocol)
	self.data:SetJieYiTeamJoinReq(protocol)
	RemindManager.Instance:Fire(RemindName.Sworn_Apply)
	HomesWGCtrl.Instance:FlushHomesView()
	local sworn_state = self.data:GetMySwornState()
	if sworn_state == SwornWGData.SWORN_TYPE.HAS_SWORN then
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.sworn_apply)
		end
	end
end

function SwornWGCtrl:OnSCJieYiTeamVoteInfo(protocol)
	self.data:SetJieYiTeamVoteInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Sworn_Start)
	HomesWGCtrl.Instance:FlushHomesView()
	local sworn_state = self.data:GetMySwornState()
	if sworn_state == SwornWGData.SWORN_TYPE.HAS_SWORN then
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.sworn_start)
		end
	end
end

function SwornWGCtrl:FlushSwornApply()
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.sworn_apply)
	end
end

-- 通用请求请求操作
function SwornWGCtrl:SendSwornRequest(opera_type, param_1, param_2, param_3)
	-- print_error("发送请求",opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSJieYiOperate)
	protocol.operate_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function SwornWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.data:CheckIsImporintItem(change_item_id) then
		RemindManager.Instance:Fire(RemindName.Sworn_Imprint)
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.sworn_imprint)
		end
	end

	local is_add = change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
					(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num)
	if not is_add then
		return
	end

	if self.data:CheckIsSwornStartItem(change_item_id) then
		if self.view:IsOpen() and self.view.show_index == TabIndex.sworn_start then
			self.view:FlushCostItem()
		end
	end
	
	local fire_remind_name
	if self.data:IsUpLevelStuffID(change_item_id) then
		fire_remind_name = RemindName.Sworn_Uplevel
	elseif self.data:IsJinLanEquip(change_item_id) then
		local is_act = self.data:GetHoleIsActByItemId(change_item_id)
		if is_act then
			local is_max = self.data:GetIsStarMaxLevelByItemId(change_item_id)
			if not is_max then
				fire_remind_name = RemindName.Sworn_Upstar
			end
		else
			fire_remind_name = RemindName.Sworn_Suit
		end
	end

	if self.view:IsOpen() then
		self.view:Flush()
		if fire_remind_name then
			RemindManager.Instance:Fire(fire_remind_name)
		end
	end

	if fire_remind_name then
		RemindManager.Instance:Fire(RemindName.Sworn_Equip)
	end

	HomesWGCtrl.Instance:FlushHomesView()
end

function SwornWGCtrl:OnPassDay()
	GlobalTimerQuest:AddDelayTimer(function()
		ViewManager.Instance:FlushView(GuideModuleName.SwornView, TabIndex.sworn_task, "task_flush_time")
	end, 2)
end

function SwornWGCtrl:OpenSwornProtocolView()
	if self.protocol_view and not self.protocol_view:IsOpen() then
		self.protocol_view:Open()
	end
end

function SwornWGCtrl:OpenSwornInviteView()
	if self.invite_view and not self.invite_view:IsOpen() then
		self.invite_view:Open()
	end
end

function SwornWGCtrl:FlushTextInvite()
	if self.invite_view:IsOpen() and self.invite_view:IsLoaded() then
	    self.invite_view:FlushTextInvite()
    end
end

function SwornWGCtrl:CloseSwornInviteView()
	if self.invite_view and self.invite_view:IsOpen() then
		self.invite_view:Close()
	end
end

function SwornWGCtrl:CreateOrJoinTeam()
	if self.be_invite then
		self.be_invite = false
		local enough, item_id, num = self.data:GetJieYiCostEnough()

		if not enough then
			local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
        	local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        	TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Sworn.NoEnough, name))
			return
		end

		self:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_INVITE_RET, self.be_invite_protocol.jieyi_id, self.be_invite_protocol.invite_player.uid, 1)
	else
		SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_CREATE_TEAM)
	end
end

function SwornWGCtrl:SetBeInvuteState(state)
	if not state and not IsEmptyTable(self.be_invite_protocol) and self.be_invite then
		self:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_JOIN_INVITE_RET, self.be_invite_protocol.jieyi_id, self.be_invite_protocol.invite_player.uid, 0)
		local data = self.data:GetSwornOtherCfg()
		if not IsEmptyTable(data) and data.cost_item_id then
			TipWGCtrl.Instance:OpenItem({item_id = data.cost_item_id})
		end
	end

	self.be_invite = state
end

--套装激活界面
function SwornWGCtrl:OpenSuitAttrView(suit_index)
	self.equip_attr_view:SetDataAndOpen(suit_index)
end


----------------------结义建设拓展------------------

function SwornWGCtrl:OnSCJieYiTeamBuildAllInfo(protocol)
	--print_error("-------AllInfo------",protocol)
	self.data:SetBuildAllInfo(protocol)

	RemindManager.Instance:Fire(RemindName.Sworn_Build_Task)
	RemindManager.Instance:Fire(RemindName.Sworn_Build_Taoyuan)
end

function SwornWGCtrl:OnSCJieYiTeamBuildTaskUpdate(protocol)
	-- print_error("-------TaskUpdate------",protocol)
	self.data:SetBuildTaskUpdate(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.SwornView, TabIndex.sworn_task)
	RemindManager.Instance:Fire(RemindName.Sworn_Build_Task)
end

function SwornWGCtrl:OnSCJieYiTeamBuildLevelUpdate(protocol)
	-- print_error("-------LevelUpdate------",protocol)
	self.data:SetBuildLevelUpdate(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.SwornView, TabIndex.sworn_taoyuan)
end

function SwornWGCtrl:OnSCJieYiTeamBuildFanliRewardUpdate(protocol)
	-- print_error("-------FanliRewardUpdate------",protocol)
	self.data:SetBuildFanliRewardUpdate(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.SwornView, TabIndex.sworn_taoyuan)
	RemindManager.Instance:Fire(RemindName.Sworn_Build_Taoyuan)
end

function SwornWGCtrl:OnSCJieYiTeamBuildActiveUpdate(protocol)
	-- print_error("-------ActiveUpdate------",protocol)
	self.data:SetBuildActiveUpdate(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.SwornView, TabIndex.sworn_task)
	RemindManager.Instance:Fire(RemindName.Sworn_Build_Task)
end