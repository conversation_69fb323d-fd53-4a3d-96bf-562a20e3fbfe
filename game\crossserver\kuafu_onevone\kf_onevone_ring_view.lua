-- require("game/crossserver/kuafu_onevone/kf_onevone_rank_gongxun")
require("game/crossserver/kuafu_onevone/kf_onevone_rank_match_aim")
require("game/crossserver/kuafu_onevone/kf_onevone_rank_ranking")


KfOneVOneRing = KfOneVOneRing or BaseClass(SafeBaseView)

RING_TAB_TYPE = {
	KingRing = TabIndex.kfonevone_ring,				-- 王者之戒
	KingLingPai = TabIndex.kfpvp_lingpai,			-- 王者令牌
}

function KfOneVOneRing:__init()
	self.view_name = GuideModuleName.WZring
	self.is_modal = true
	self:LoadConfig()
	self.default_index = RING_TAB_TYPE.KingRing
	self:SetMaskBg()
	self.view_style = ViewStyle.Full
end

function KfOneVOneRing:__delete()

end

function KfOneVOneRing:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	if self.ring_reward_list then
		self.ring_reward_list:DeleteMe()
		self.ring_reward_list = nil
	end
	if self.ring_reward_list_wing then
		self.ring_reward_list_wing:DeleteMe()
		self.ring_reward_list_wing = nil
	end
end

function KfOneVOneRing:LoadConfig()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_wzcard")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_panel3_2")
end

function KfOneVOneRing:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.KF_Ring_LingPai.TitleName

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.KF_Ring_LingPai.TabGrop)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end
	self:InitListView()
end

--初始化列表
function KfOneVOneRing:InitListView()
	if not self.ring_reward_list then
		self.ring_reward_list = AsyncListView.New(RingItemRender,self.node_list.ph_list_view)
	end
	if not self.ring_reward_list_wing then
		self.ring_reward_list_wing = AsyncListView.New(RingItemRender,self.node_list.ph_list_view_wing)
	end
end

function KfOneVOneRing:ShowIndexCallBack(index)
	self:Flush(index)
	self.node_list.title_view_name.text.text = Language.KF_Ring_LingPai["TitleName" .. index/10]
end

function KfOneVOneRing:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == RING_TAB_TYPE.KingRing then
				self:OnFlushRingView()
			elseif index == RING_TAB_TYPE.KingLingPai then
				self:OnFlushLingPaiView()
			end
		end
	end
end

function KfOneVOneRing:OnFlushRingView()
	local cfg  = KuafuOnevoneWGData.Instance:GetWZRingCfg()
	self.node_list.ph_list_view:SetActive(false)
	local kf1v1_info = KuafuOnevoneWGData.Instance:Get1V1Info()
	local limit_times_cfg = KuafuOnevoneWGData.Instance:GetKFOneVOneOtherCfg()
	local limit_times = kf1v1_info.cross_lvl_total_join_times >= limit_times_cfg.atleast_join_times and limit_times_cfg.atleast_join_times or kf1v1_info.cross_lvl_total_join_times
	self.node_list.changlle_count_limit.text.text = string.format(Language.Kuafu1V1.OneVOneBottomText,limit_times,limit_times_cfg.atleast_join_times)
	if self.ring_reward_list then
		if self.node_list.ph_list_view_wing:GetActive() then
		--self.ring_reward_list:ChangeDefaultIndex()

			self.ring_reward_list_wing:SetDataList(cfg)
			
		else
			self.node_list.ph_list_view_wing:SetActive(true)
			self.ring_reward_list_wing:SetDataList(cfg)
			local selecr_list_index =  KuafuOnevoneWGData.Instance:GetRingSelectIndex()
			--print_error(selecr_list_index)
			if IsEmptyTable(cfg[1]) then
				self.node_list.ph_list_view_wing.scroller:ReloadData(1)
			else
				if selecr_list_index <= 3 and selecr_list_index > 0 then
					self.node_list.ph_list_view_wing.scroller:ReloadData(0)
				elseif #cfg - selecr_list_index == 1 or selecr_list_index <= 0 then
					self.node_list.ph_list_view_wing.scroller:ReloadData(1)
				else
					local huadong = (selecr_list_index - 2)/(#cfg - 3 )
					self.node_list.ph_list_view_wing.scroller:ReloadData(huadong)
				end
			end
		end
	end
end

function KfOneVOneRing:OnFlushLingPaiView()
	local cfg  = KuafuPVPWGData.Instance:GetWZLingPaiCfg()
	if nil == cfg then
		return
	end

	self.node_list.ph_list_view_wing:SetActive(false)

	if self.ring_reward_list then
		if self.node_list.ph_list_view:GetActive() then
			self.ring_reward_list:SetDataList(cfg)
		else
			self.node_list.ph_list_view:SetActive(true)
			self.ring_reward_list:SetDataList(cfg)
			self.node_list.ph_list_view.scroller:ReloadData(1)
		end
	end
end

-------------------------------------------
RingItemRender = RingItemRender or BaseClass(BaseRender)
function RingItemRender:__init()
end

function RingItemRender:__delete()
end

function RingItemRender:ReleaseCallBack()
	if self.pei_dai_alert then
		self.pei_dai_alert:DeleteMe()
		self.pei_dai_alert = nil
	end
end

function RingItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_select, BindTool.Bind1(self.OnClickToGetReward, self))
	XUI.AddClickEventListener(self.node_list.img_pic, BindTool.Bind1(self.OpenSeasonReward, self))

end

--设置战斗力
function RingItemRender:SetCapacity(value)
	value = value or 0
	self.node_list.text_ml_zhandouli_num.text.text = value
end

function RingItemRender:OpenSeasonReward()
	if nil == self.data.type then return end
	local mark
	if self.data.type == "cross_1v1_auto" then
		mark = KFPVP_TYPE.ONE
	else
		mark = KFPVP_TYPE.MORE
	end
	ViewManager.Instance:Open(GuideModuleName.PvPSeasonReward)
	ViewManager.Instance:FlushView(GuideModuleName.PvPSeasonReward, nil, nil, {mark})
end

function RingItemRender:OnFlush()
	if not self.data then return end
	if IsEmptyTable(self.data) then
		self.node_list.ph_itme_ring:SetActive(false)
		return
	end
	self.node_list.ph_itme_ring:SetActive(true)
	-- self.node_list.img_season.image:LoadSprite(ResPath.GetKf1V1Old("img_season"..self.data.index)) --赛季：第一赛季

	self.node_list.img_season.text.text = string.format(Language.Kuafu1V1.SeasonNum,CommonDataManager.GetDaXie(self.data.index))

	local data_list = {}
	self.node_list.effect_arna_op:SetActive(false)
	self.node_list.improve_tween:SetActive(false)
	if self.data.type == "cross_1v1_auto" then
		--self.node_list.img_pic.image:LoadSprite(ResPath.GetKf1V1Old(self.data.pic)) 				--赛季图标
		local effect_index = self.data.particle_index % 6 == 0 and 6 or self.data.particle_index % 6 
		local b,a = "effects2/prefab/ui/ui_jiezhi0"..effect_index.."_prefab","UI_jiezhi0"..effect_index
		self.node_list.img_pic:ChangeAsset(b,a)
		--EffectManager.Instance:PlayAtTransform("effects2/prefab/ui/ui_jiezhi01_prefab", , self.node_list.img_pic.transform)
		if self.data.index == self.data.cur_season then
			data_list = KuafuOnevoneWGData.Instance:GetWZringAttributeCfg(self.data.index,self.data.flag,true)
		else
			--print_error(self.data)
			if self.data.show_get_ring_index >= 0 then
				data_list = KuafuOnevoneWGData.Instance:GetWZringAttributeCfg(self.data.index,self.data.show_get_ring_index)
			else
				data_list = KuafuOnevoneWGData.Instance:GetWZringAttributeCfg(self.data.index,self.data.flag)
			end
			
		end
		local cap_1,cap_2 = KuafuOnevoneWGData.Instance:GetUseRingCfg()
		local is_flag  = KuafuOnevoneWGData.Instance:GetWZringSelectCfg(self.data.index)
		if not is_flag and data_list and data_list.zhan_li and (data_list.zhan_li > cap_1 or data_list.zhan_li > cap_2) and self.data.index ~= self.data.cur_season then
			self.node_list.effect_arna_op:SetActive(true)
			if self.data.index < self.data.cur_season and self.data.show_get_ring_index < 0 then
				self.node_list.improve_tween:SetActive(true)
			end
		end
	else
		self.node_list.img_pic.image:LoadSprite(ResPath.GetKf3V3Old(self.data.pic))							--赛季图标
		data_list = KuafuPVPWGData.Instance:GetWZCardAttributeCfg(self.data.index,self.data.flag)
	end
	if self.data.flag < 0  then
		if self.data.index < self.data.cur_season then 
			self.node_list.layout_att_txt:SetActive(false)
			self.node_list.img_zhandouli_bg:SetActive(false)
			-- self.node_list.img_zhandoulibg:SetActive(false)
			--self.node_list.lbl_peidai:SetActive(false)
			self.node_list.img_activity_flag:SetActive(true)
			self.node_list.btn_select:SetActive(false)
			self.node_list.rich_sjexplain.image:LoadSprite(ResPath.GetKf1V1("old_season_text"))
			self.node_list.rich_sjexplain:SetActive(true)
		elseif self.data.index == self.data.cur_season then 
			self.node_list.rich_sjexplain:SetActive(false)
			self.node_list.img_zhandouli_bg:SetActive(true)
			self.node_list.layout_att_txt:SetActive(true)
			--self.node_list.lbl_peidai:SetActive(false)
			self.node_list.btn_select:SetActive(false)
			self.node_list.img_activity_flag:SetActive(true)
			self.node_list.autoname_title.text.text = Language.Kuafu1V1.BestAttrBrowse
		else
			self.node_list.img_zhandouli_bg:SetActive(false)
			self.node_list.layout_att_txt:SetActive(false)
			self.node_list.rich_sjexplain:SetActive(true)
			self.node_list.rich_sjexplain.image:LoadSprite(ResPath.GetKf1V1("new_season_text"))
			self.node_list.btn_select:SetActive(false)
			--self.node_list.lbl_peidai:SetActive(true)
			self.node_list.img_activity_flag:SetActive(false)
			--self.node_list.autoname_title.text.text = Language.Kuafu1V1.JieZhiAttr
		end
	else
		self.node_list.rich_sjexplain:SetActive(false)
		self.node_list.img_zhandouli_bg:SetActive(true)
		self.node_list.layout_att_txt:SetActive(true)
		self.node_list.btn_select:SetActive(true)
		self.node_list.img_activity_flag:SetActive(false)
		self.node_list.autoname_title.text.text = Language.Kuafu1V1.JieZhiAttr
	end
	local vas1 = self.node_list.rich_sjexplain:GetActive()
	local vas2 = self.node_list.img_activity_flag:GetActive()
	local vas3 = self.node_list.layout_att_txt:GetActive()
	if self.data.show_get_ring_index >= 0 then
		self.node_list.rich_sjexplain:SetActive(false)
		self.node_list.img_activity_flag:SetActive(false)
		self.node_list.layout_att_txt:SetActive(true)
		self.node_list.please_use:SetActive(true)
	else
		-- self.node_list.rich_sjexplain:SetActive(vas1)
		-- self.node_list.img_activity_flag:SetActive(vas2)
		-- self.node_list.layout_att_txt:SetActive(vas3)
		self.node_list.please_use:SetActive(false)
	end

	if self.data.flag >= 0 or self.data.show_get_ring_index >= 0 or self.data.index == self.data.cur_season then

		 if not IsEmptyTable(data_list) then
			local des_gongji = string.format(Language.Kuafu1V1.GongJi,data_list.gong_ji)
			local des_hp = string.format(Language.Kuafu1V1.MaxHp,data_list.max_hp)
			local des_pojia = string.format(Language.Kuafu1V1.PoJia,data_list.po_jia)
			local des_wufang  = string.format(Language.Kuafu1V1.WuFang,data_list.fang_yu)
			local des_fafang = string.format(Language.Kuafu1V1.FaFang,data_list.fa_fang_yu)
			local reduce_bfb = math.ceil((data_list.reduce_hurt/100))
			local add_bfb =  math.ceil((data_list.add_hurt/100))
			local des_ir = string.format(Language.Kuafu1V1.EduceHur,reduce_bfb)

			local des_addhrt = string.format(Language.Kuafu1V1.AddHurt,add_bfb)
			local attr_list = {}
			for i=1,6 do
				self.node_list["lbl_attr"..i]:SetActive(false)
				attr_list[i] = {}
			end
			attr_list[1].str = des_hp
			attr_list[1].attr_num = data_list.max_hp
			attr_list[2].str = des_gongji
			attr_list[2].attr_num = data_list.gong_ji
			attr_list[3].str = des_wufang
			attr_list[3].attr_num = data_list.fang_yu
			attr_list[4].str = des_pojia
			attr_list[4].attr_num = data_list.po_jia
			attr_list[5].str = string.format(Language.Kuafu1V1.AddHurt,add_bfb)
			attr_list[5].attr_num = data_list.add_hurt
			attr_list[6].str = des_ir
			attr_list[6].attr_num = data_list.reduce_hurt
			local end_attr_list = {}
			for k,v in pairs(attr_list) do
				if v.attr_num > 0 then
					table.insert(end_attr_list,v)
				end
			end
			for k,v in pairs(end_attr_list) do
				self.node_list["lbl_attr"..k].text.text = v.str
				self.node_list["lbl_attr"..k]:SetActive(true)
			end
			self:SetCapacity(data_list.zhan_li> 0 and data_list.zhan_li or 0)
		end
	end
	 self.node_list.img_ring_bg.image:LoadSprite(ResPath.FieldOneVOne("bg_season"))
	 self.node_list.img_flag.image:LoadSprite(ResPath.FieldOneVOne("blue_flag"))
	local str = ""
	 if self.data.cur_season > self.data.index then
	 	self.node_list.img_activity_flag.image:LoadSprite(ResPath.FieldOneVOne("season_end"))
	 elseif self.data.cur_season == self.data.index then
	 	-- if self.data.type == "cross_1v1_auto" then
	 	-- 	str = Language.Kuafu1V1.WZringTip2
	 	-- else
	 	-- 	str = Language.Kuafu1V1.WZcardTip2
	 	-- end
	 	self.node_list.img_flag.image:LoadSprite(ResPath.FieldOneVOne("red_flag"))
	 	self.node_list.img_ring_bg.image:LoadSprite(ResPath.FieldOneVOne("bg_season"))
	 	self.node_list.img_activity_flag.image:LoadSprite(ResPath.FieldOneVOne("season_holding"))
	 elseif self.data.cur_season < self.data.index then
	 	-- if self.data.type == "cross_1v1_auto" then
	 	-- 	str = Language.Kuafu1V1.WZringTip1
	 	-- else
	 	-- 	str = Language.Kuafu1V1.WZcardTip1
	 	-- end
	 	self.node_list.img_activity_flag.image:LoadSprite(ResPath.FieldOneVOne("season_no_start"))
	 end




	if self.data.type == "cross_1v1_auto" then
	local is_flag  = KuafuOnevoneWGData.Instance:GetWZringSelectCfg(self.data.index)
	self.node_list.img_select_hook:SetActive(is_flag)
	else
		local is_flag  = KuafuPVPWGData.Instance:GetWZCardSelectCfg(self.data.index)
		self.node_list.img_select_hook:SetActive(is_flag)
	end



	-- if self.data.flag == -1 and  self.data.index < self.data.cur_season then
	-- 	if self.data.type == "cross_1v1_auto" then
	--  		str = Language.Kuafu1V1.WZringTip2
	--  	else
	--  		str = Language.Kuafu1V1.WZcardTip2
	--  	end
	-- end
	--self.node_list.rich_sjexplain.text.text = str
	if self.data.flag < 0 and self.data.index < self.data.cur_season then 
		self.node_list.season_state:SetActive(true)
		self.node_list.season_state.text.text = Language.Kuafu1V1.SeasonOver
		self.node_list.jiezhi_bg_render.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("kf_rank_bg_2"))
	elseif self.data.index > self.data.cur_season then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local time_table = os.date("*t", server_time)
		local month_day_count = os.date("%d",os.time({year=time_table.year,month=time_table.month+1,day=0}))
		if time_table.month == 12 then
			self.node_list.season_state.text.text = string.format(Language.Kuafu1V1.SeasonWillStart,1) 
		else
			self.node_list.season_state.text.text = string.format(Language.Kuafu1V1.SeasonWillStart,time_table.month + 1) 
		end
		self.node_list.season_state:SetActive(true)
		self.node_list.jiezhi_bg_render.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("kf_rank_bg_6"))
		
	else
		if self.data.index == self.data.cur_season then
			self.node_list.jiezhi_bg_render.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("kf_rank_bg_6"))
		else
			self.node_list.jiezhi_bg_render.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("kf_rank_bg_"..self.data.index))
		end
		self.node_list.season_state:SetActive(false)
	end

end

function RingItemRender:OnClickToGetReward(p_sender)
	local is_show_enough = self.node_list.img_select_hook:GetActive()
	if nil == is_show_enough then
		return
	end

	if self.data.type == "cross_1v1_auto" then
		-- print_error("self.data.type",self.data.type)
		local is_flag  = KuafuOnevoneWGData.Instance:GetWZringSelectCfg(self.data.index)
		self.node_list.img_select_hook:SetActive(is_flag)
		local data_seq  = KuafuOnevoneWGData.Instance:GetWZringAttributeCfg(self.data.index,self.data.flag)
		if not data_seq then
			--print_error("can not read the cfg!!")
			return
		end
		local cap_1,cap_2,cap_cfg_1,cap_cfg_2 = KuafuOnevoneWGData.Instance:GetUseRingCfg()
		if not is_show_enough then
			if cap_1 <= 0 or cap_2 <= 0 then
				KuafuOnevoneWGCtrl.Instance:SendCross1v1Ring(CROSS_1V1_RING_OPER_TYPE.CROSS_1V1_RING_OPER_WEAR,data_seq.seq)
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.WearSuccess)
			else
				local di_zhanli = cap_1 < cap_2 and  cap_1 or cap_2
				local base_info = KuafuOnevoneWGData.Instance:Get1V1Info()
				local use_ring_info = KuafuOnevoneWGData.Instance:GetRoleHasUseRing()
				local use_part_1,use_part_2 = use_ring_info[1],use_ring_info[2]
				local seq
				local seq_cfg
				if cap_1 < cap_2 then
					seq = base_info.cross_1v1_have_ring[use_part_1]
					seq_cfg  = KuafuOnevoneWGData.Instance:GetWZringAttributeCfg(cap_cfg_1.season,seq)
				else
					seq = base_info.cross_1v1_have_ring[use_part_2]
					seq_cfg  = KuafuOnevoneWGData.Instance:GetWZringAttributeCfg(cap_cfg_2.season,seq)
				end

				if di_zhanli >data_seq.zhan_li then
					if nil == self.pei_dai_alert then
						self.pei_dai_alert = Alert.New()
						self.pei_dai_alert:SetShowCheckBox(true)
						self.pei_dai_alert:SetCheckBoxDefaultSelect(false)
						local ok_func = function ()

							KuafuOnevoneWGCtrl.Instance:SendCross1v1Ring(CROSS_1V1_RING_OPER_TYPE.CROSS_1V1_RING_OPER_OFF,seq_cfg.seq)
							KuafuOnevoneWGCtrl.Instance:SendCross1v1Ring(CROSS_1V1_RING_OPER_TYPE.CROSS_1V1_RING_OPER_WEAR,data_seq.seq)
						end
						self.pei_dai_alert:SetOkFunc(ok_func)
						self.pei_dai_alert:SetLableString(Language.Kuafu1V1.PeiLowPowerDaiTipsText)
						self.pei_dai_alert:SetMarkName("onevone_pei_dai_alert")
						
					end
					self.pei_dai_alert:Open()
				else
					KuafuOnevoneWGCtrl.Instance:SendCross1v1Ring(CROSS_1V1_RING_OPER_TYPE.CROSS_1V1_RING_OPER_OFF,seq_cfg.seq)
					KuafuOnevoneWGCtrl.Instance:SendCross1v1Ring(CROSS_1V1_RING_OPER_TYPE.CROSS_1V1_RING_OPER_WEAR,data_seq.seq)
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.ReplaceLowPower)
				end
			end
		else
			KuafuOnevoneWGCtrl.Instance:SendCross1v1Ring(CROSS_1V1_RING_OPER_TYPE.CROSS_1V1_RING_OPER_OFF,data_seq.seq)
		end

	elseif self.data.type == "kuafu_tvt_auto" then
		-- print_error("self.data.type",self.data.type)
		local is_flag  = KuafuPVPWGData.Instance:GetWZCardSelectCfg(self.data.index)
--		print_error(is_flag,self.data.index)
		self.node_list.img_select_hook:SetActive(is_flag)
		--self.node_list.img_select_hook:SetActive(not is_show_enough)
		local data_seq  = KuafuPVPWGData.Instance:GetWZCardAttributeCfg(self.data.index, self.data.flag)
		if not data_seq then
		--	print_error("can not read the cfg!!")
			return
		end
		--is_show_enough = self.node_list.img_select_hook:GetActive()
		if not is_show_enough then
			KuafuPVPWGCtrl.Instance:SendCrossPVPCard(CROSS_PVP_CARD_OPER_TYPE.CROSS_PVP_CARD_OPER_WEAR, data_seq.seq)
		else
			KuafuPVPWGCtrl.Instance:SendCrossPVPCard(CROSS_PVP_CARD_OPER_TYPE.CROSS_PVP_CARD_OPER_OFF, data_seq.seq)
		end

	else
		--print_error("can not find the data.type！！",self.data.type)
	end
end

function RingItemRender:CreateSelectEffect()
end

