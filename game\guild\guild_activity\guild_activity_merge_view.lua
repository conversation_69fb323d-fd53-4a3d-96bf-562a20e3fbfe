
GuildActivityView = GuildActivityView or BaseClass(SafeBaseView)

function GuildActivityView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg()

	local bundle_name = "uis/view/guild_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, bundle_name, "layout_guild_acticity_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function GuildActivityView:OpenCallBack()

end

function GuildActivityView:CloseCallBack()
end

function GuildActivityView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.Guild.GuildActivityTitle

	if not self.activity_item_list then
		self.activity_item_list = AsyncListView.New(GuildActItemRender, self.node_list["guild_activity_list"])
		self.activity_item_list:SetSelectCallBack(BindTool.Bind(self.OnSelectActicityCallback, self))
	end

	if not self.reward_item_list then
		self.reward_item_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
	end

	XUI.AddClickEventListener(self.node_list["btn_go_activity"], BindTool.Bind(self.OnClickGoBtn, self))

	self.all_activity_change_callback = BindTool.Bind(self.OnAllActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.all_activity_change_callback)
end

function GuildActivityView:ReleaseCallBack()
	if self.activity_item_list then
		self.activity_item_list:DeleteMe()
		self.activity_item_list = nil
	end

	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end

	if self.all_activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.all_activity_change_callback)
		self.all_activity_change_callback = nil
	end

	self.sel_act_type = nil
end

function GuildActivityView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			local guild_act_data = GuildActivityWGData.Instance:GetActData()
			self.activity_item_list:SetDataList(guild_act_data)
			local jump_index = self.sel_index or 1
			if v.activity_type then
				for i, data in ipairs(guild_act_data) do
					if data.cfg.type == v.activity_type then
						jump_index = i
					end
				end
			end
			self.activity_item_list:JumpToIndex(jump_index)
			--self:FlushInfoPart()
		end
	end
end

function GuildActivityView:FlushInfoPart()
	if not self.sel_act_data then
		return
	end

	local act_data = self.sel_act_data
	self.node_list["txt_activity_title"].text.text = act_data.cfg.act_name
	self.node_list["txt_activity_desc"].text.text = act_data.cfg.act_rule
	self.reward_item_list:SetDataList(act_data.reward_list)

	if not act_data.act_hall_cfg then
		return
	end

	local time = act_data.act_hall_cfg.open_tips .. act_data.act_hall_cfg.open_time .. "-" .. act_data.act_hall_cfg.close_time
	local str = string.format(Language.Guild.ActivityTime, time)
	self.node_list["txt_open_time"].text.text = str

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(self.sel_act_data.cfg.act_id)
	self:FlushBtnState((act_info or {}).status)
end

function GuildActivityView:OnSelectActicityCallback(item, index)
	if not item or not item.data then
		return
	end

	self.sel_index = index
	self.sel_act_data = item.data
	self.sel_act_type = self.sel_act_data.cfg.type
	

	local bundle, asset = ResPath.GetRawImagesPNG(self.sel_act_data.cfg.bg_name)
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
	
	self:FlushInfoPart()
end

function GuildActivityView:OnClickGoBtn()
	if not self.sel_act_type then
		return
	end

    if self.sel_act_type == GuildActivityWGData.Act_Type.Boss then
        local data = GuildActivityWGData.Instance:GetActDataByType(self.sel_act_type)
        if not data or not data.cfg then
            return
        end

        GuildBossWGCtrl.Instance:GuildActBossGoFunc()
    elseif self.sel_act_type == GuildActivityWGData.Act_Type.DaTi then
        if RoleWGData.Instance.role_vo.guild_id == 0 then
            GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist)
        else
            FunOpen.Instance:OpenViewNameByCfg("guildanswer")
        end
    elseif self.sel_act_type == GuildActivityWGData.Act_Type.ShouHu then
        local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_FB)
        if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
            return
        end

        local is_finish = GuildWGData.Instance:IsFinishGuildFb()
        if is_finish then
        	SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ShouHuFinish)
        	return
        end

        GuildWGCtrl.Instance:SendGuildFbEnterReq()
    end
end

function GuildActivityView:OnAllActChange(activity_type, status, next_time, open_type)
	if not self.sel_act_data then
		return
	end
	if activity_type == self.sel_act_data.cfg.act_id then
		self:FlushBtnState(status)
	end
end

function GuildActivityView:FlushBtnState(status)
	if status == ACTIVITY_STATUS.OPEN then
		self.node_list["btn_go_activity"]:CustomSetActive(true)
		self.node_list["go_wait_flag_text"].text.text = Language.Guild.Doing
	else
		self.node_list["btn_go_activity"]:CustomSetActive(false)
		self.node_list["go_wait_flag_text"].text.text = Language.Guild.NotOpenDesc
	end
end

------------------------------------
-- 活动TabbarItem
------------------------------------
GuildActItemRender = GuildActItemRender or BaseClass(BaseRender)

function GuildActItemRender:OnFlush()
	if not self.data or not self.data.act_hall_cfg then
		return
	end
	local info = ActivityWGData.Instance:GetActivityCfgByType(self.data.cfg.act_id)

	self.node_list["txt_nor"].text.text = self.data.cfg.act_name
	self.node_list["text_hl"].text.text = self.data.cfg.act_name

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(self.data.cfg.act_id)
	local str
	if not ActivityWGData.Instance:CheckActOpenByRoleLevel(self.data.cfg.act_id) then
		str = string.format(Language.Guild.LimitLevel, info.limit_level)
	elseif is_open then
		str = Language.Guild.Doing
	else
		local time = self.data.act_hall_cfg.open_time .. "-" .. self.data.act_hall_cfg.close_time
		str = ToColorStr(time, COLOR3B.GRAY)
	end
	self.node_list["info"].text.text = str
end

function GuildActItemRender:OnSelectChange(is_select)
	self.node_list["normal"]:SetActive(not is_select)
	self.node_list["img_hl"]:SetActive(is_select)
end
