KF3V3FetchRewardView = KF3V3FetchRewardView or BaseClass(SafeBaseView)

function KF3V3FetchRewardView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_pvp_reward")
end

function KF3V3FetchRewardView:ReleaseCallBack()
    if self.role_info_change then
        GlobalEventSystem:UnBind(self.role_info_change)
        self.role_info_change = nil
    end
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function KF3V3FetchRewardView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.KuafuPVP.ViewName_JieDuanJiangLi
    self:SetSecondView(nil, self.node_list["size"])
    self.reward_list = AsyncListView.New(KF3V3FetchRewardViewRender, self.node_list.ph_item_list)
    self.role_info_change = GlobalEventSystem:Bind(OtherEventType.KF3V3ZhanDuiRoleInfoChnage, BindTool.Bind(self.OnRoleInfoChange, self)) --人物信息改变
end

function KF3V3FetchRewardView:ShowIndexCallBack()
    self:Flush()
end

function KF3V3FetchRewardView:OnFlush()
    local data_list = KF3V3WGData.Instance:GetSortMatchTimesRewardList()
    self.reward_list:SetDataList(data_list)
    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    self.node_list.today_match_count.text.text = string.format(Language.KuafuPVP.TodayChang, role_info.today_match_times)
end

function KF3V3FetchRewardView:OnRoleInfoChange()
    self:Flush()
end


KF3V3FetchRewardViewRender = KF3V3FetchRewardViewRender or BaseClass(BaseRender)
function KF3V3FetchRewardViewRender:__init()
    self.item_list = AsyncListView.New(KF3V3RankRewardItemCell, self.node_list.item_list)
    XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind(self.OnClickFetch, self))
end

function KF3V3FetchRewardViewRender:__delete()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function KF3V3FetchRewardViewRender:OnFlush()
    local item_data_list = {}
    for i = 0, 20 do
        if self.data.reward[i] then
            table.insert(item_data_list, self.data.reward[i])
        else
            break
        end
    end

    self.item_list:SetDataList(item_data_list)
    self.node_list.title.text.text = string.format(Language.KuafuPVP.TodayJoinCountTip, self.data.match_times)
    local flag_list = ZhanDuiWGData.Instance:GetFetchFlagList()
    local has_fetch = flag_list[32 - self.data.seq] == 1
    local asset_name = has_fetch and "a2_ty_bq_2" or "a2_ty_bq_3"
    self.node_list.image_ylq.image:LoadSprite(ResPath.GetCommonImages(asset_name))
    self.node_list.fetch_state.text.text = has_fetch and Language.KuafuPVP.YiLingQu or Language.KuafuPVP.WeiLingQu
    self.node_list.btn_lingqu:SetActive(self.data.can_fetch or false)
    self.node_list.image_ylq:SetActive(not self.data.can_fetch)
end

function KF3V3FetchRewardViewRender:OnClickFetch()
    ZhanDuiWGCtrl.Instance:SendZhanDuiFetchJieDuanReward(self.data.seq)
end
