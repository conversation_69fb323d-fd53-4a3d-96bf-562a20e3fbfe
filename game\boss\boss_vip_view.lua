function BossView:DeleteVipBossView()

end

function BossView:InitVipBossView()
end

function BossView:OnFlushVipBossView()
	local list_index = self.layer_btn_list:GetSelectIndex()
    local enter_vip_cfg = BossWGData.Instance:GetEnterVipCfgbyIndex(list_index)
    local vip_lv = VipWGData.Instance:GetRoleVipLevel()
    self.node_list["text_vip_tips"].text.text = enter_vip_cfg.level_des
    if enter_vip_cfg.free_vip_level ~= 0 and enter_vip_cfg.free_vip_level > vip_lv then
        self.node_list["Txt_goto_kill"].text.text = "V" .. enter_vip_cfg.free_vip_level .. Language.Boss.GoToKill
    else
        self.node_list["Txt_goto_kill"].text.text = Language.Boss.GoToKillBtn
    end
end

-- 玩法介绍
function BossView:AddVipBossCount()
	if self.show_index == BossViewIndex.VipBoss then
		BossWGCtrl.Instance:OpenBossVipTimesView(VipPowerId.boss_home_buy_times)
	end
end

-- 默认选中之前进入的--旧的vip需求已删除
function BossView:GetVipBossDefaultIndex(default_index, list_data)
	return BossWGData.Instance:GetWorldBossDefaultIndex(default_index, list_data, true)
end

function BossView:GetCurVipLayerIndex(cur_layer, btn_list)
	for i, v in ipairs(btn_list) do
		if v.level == cur_layer then
			return i
		end
	end
end