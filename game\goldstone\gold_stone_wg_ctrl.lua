require("game/goldstone/gold_stone_view")
require("game/goldstone/gold_stone_wg_data")

GoldStoneWGCtrl = GoldStoneWGCtrl or BaseClass(BaseWGCtrl)

function GoldStoneWGCtrl:__init()
	if GoldStoneWGCtrl.Instance then
        error("[GoldStoneWGCtrl]:Attempt to create singleton twice!")
	end

	GoldStoneWGCtrl.Instance = self
	self.data = GoldStoneWGData.New()
	self.gold_stone_view = GoldStoneView.New(GuideModuleName.GoldStoneView)
	self:RegisterAllProtocols()

	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
end

function GoldStoneWGCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.open_fun_change then
		GlobalEventSystem:UnBind(self.open_fun_change)
		self.open_fun_change = nil
	end

	if self.gold_stone_view then
		self.gold_stone_view:DeleteMe()
		self.gold_stone_view = nil
	end

	if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

	GoldStoneWGCtrl.Instance = nil
end

function GoldStoneWGCtrl:CheckNeedCloseAct()
	local is_buy_rmb, is_buy_free = self.data:GetShopIsBuyFlag()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOLD_STONE_VIEW)
	if is_buy_rmb and is_buy_free then--奖励领取完了,关闭入口
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.GOLD_STONE_VIEW, ACTIVITY_STATUS.CLOSE)
	elseif activity_info and activity_info.status == ACTIVITY_STATUS.CLOSE then
		local is_open = FunOpen.Instance:GetFunIsOpened(FunName.GoldStoneView)
	    local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
	    ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.GOLD_STONE_VIEW, state)
	end
end

-- 注册协议
function GoldStoneWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCGoldTalkInfo, "SCGoldTalkInfo")
	self:RegisterProtocol(CSGoldTalkOperate)
end

function GoldStoneWGCtrl:SCGoldTalkInfo(protocol)
	--print_error("SCGoldTalkInfo", protocol)
	self.data:SetShopIsBuyFlag(protocol)

	if self.gold_stone_view:IsOpen() then
		self.gold_stone_view:Flush()
	end

	self:CheckNeedCloseAct()
	RemindManager.Instance:Fire(RemindName.GoldStoneRemind)
end

function GoldStoneWGCtrl:SendAllGetReward(operate_type, param1)
    local protocol = ProtocolPool.Instance:GetProtocol(CSGoldTalkOperate)
    protocol.operate_type = operate_type
    protocol.param1 = param1
    protocol:EncodeAndSend()
end

function GoldStoneWGCtrl:OpenView()
	if not self.gold_stone_view:IsOpen() then
		self.gold_stone_view:Open()
	else
		self.gold_stone_view:Flush()
	end
end

function GoldStoneWGCtrl:OnDayChange()
	if self.gold_stone_view:IsOpen() then
		self.gold_stone_view:Flush()
	end
end

-- 活动改变
function GoldStoneWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.GOLD_STONE_VIEW and status == ACTIVITY_STATUS.OPEN then
		self:CheckNeedCloseAct()
    end
end

function GoldStoneWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.GoldStoneView then
		is_open = FunOpen.Instance:GetFunIsOpened(FunName.GoldStoneView)
        local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.GOLD_STONE_VIEW, state)
    end
end