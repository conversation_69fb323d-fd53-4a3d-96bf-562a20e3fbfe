VipServiceWindowView = VipServiceWindowView or BaseClass(SafeBaseView)
local AUTO_CLOSE_TIME = 10

function VipServiceWindowView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

    self:AddViewResource(0, "uis/view/downloadweb_ui_prefab", "layout_vip_service_window")
end

function VipServiceWindowView:LoadCallBack()
	if not self.reward_item_list then
		self.reward_item_list = AsyncListView.New(VipServiceRewardItemRender, self.node_list.reward_item_list)
        self.reward_item_list:SetStartZeroIndex(true)
	end

    XUI.AddClickEventListener(self.node_list.go_web, BindTool.Bind1(self.OnClickGoWeb, self))
    XUI.AddClickEventListener(self.node_list.daily_reward_btn, BindTool.Bind1(self.OnClickReceive, self))
end

function VipServiceWindowView:ReleaseCallBack()
	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end

    if self.daily_reward_tween then
		self.daily_reward_tween:Kill()
		self.daily_reward_tween = nil
	end

    self:ClearCloseTimer()
    self.is_auto_close = nil
end

function VipServiceWindowView:SetIsAutoClose(is_auto_close)
    self.is_auto_close = is_auto_close
end

function VipServiceWindowView:OnFlush()
    local show_desc = VipServiceWindowWGData.Instance:GetCurAgentServiceShowDesc()
    local daily_reward_flag = VipServiceWindowWGData.Instance:GetDailyRewardFlag()
    if show_desc and "" ~= show_desc then
        local desc_list = Split(show_desc, "|")
        self.node_list["desc_txt"].text.text = desc_list[1]
        self.node_list["reward_desc"].text.text = desc_list[2]
        self.node_list["vip_desc_text1"].text.text = desc_list[3]
        self.node_list["vip_desc_text2"].text.text = desc_list[4]
        self.node_list["vip_line_txt"].text.text = desc_list[5]
        self.node_list["go_web_btn_text"].text.text = desc_list[6]
        self.node_list["vip_desc_text_content1"]:SetActive(desc_list[3] ~= "")
        self.node_list["vip_desc_text_content2"]:SetActive(desc_list[4] ~= "")
        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.vip_desc_text_content1.rect)
        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.vip_desc_text_content2.rect)
    end

    self.node_list["vip_line_number"].text.text = GLOBAL_CONFIG.param_list.vip_service_number

    VipServiceWindowWGCtrl.Instance:SetVipService(self.node_list["load_img"])

    local reward_list = VipServiceWindowWGData.Instance:GetAgentServiceReward()
    if reward_list then
        self.reward_item_list:SetDataList(reward_list)
    end

    self.node_list.close_time:CustomSetActive(self.is_auto_close ~= nil and self.is_auto_close)
    if self.is_auto_close then
        self:ClearCloseTimer()

        self.node_list.close_time.text.text = string.format("%s(%ds)", Language.Common.ClickPlaceCloseView, AUTO_CLOSE_TIME)
        CountDownManager.Instance:AddCountDown("vip_service_close_timer",
        BindTool.Bind1(self.UpdateCloseCountDownTime, self), BindTool.Bind1(self.Close, self), nil, AUTO_CLOSE_TIME, 1)
    end

    local is_receive = daily_reward_flag == 1
    self.node_list.daily_reward_flag:SetActive(is_receive)
    self.node_list.daily_reward_btn_remind:SetActive(not is_receive)
    if not is_receive then
		if self.daily_reward_tween then
			self.daily_reward_tween:Restart()
		else
			if self.daily_reward_tween then
				self.daily_reward_tween:Kill()
				self.daily_reward_tween = nil
			end

			self.daily_reward_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.daily_reward_icon.transform, self.daily_reward_tween, 1)
		end
	elseif self.daily_reward_tween then
		self.daily_reward_tween:Pause()
		self.node_list.daily_reward_icon.transform.localRotation = Quaternion.identity
	end
end

function VipServiceWindowView:UpdateCloseCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
        self.node_list.close_time.text.text = string.format("%s(%ds)", Language.Common.ClickPlaceCloseView, math.floor(total_time - elapse_time))
	end
end

function VipServiceWindowView:ClearCloseTimer()
    if CountDownManager.Instance:HasCountDown("vip_service_close_timer") then
		CountDownManager.Instance:RemoveCountDown("vip_service_close_timer")
	end
end

function VipServiceWindowView:OnClickGoWeb()
    VipServiceWindowWGCtrl.Instance:OpenWebView()
end

function VipServiceWindowView:OnClickReceive()
    local daily_reward_flag = VipServiceWindowWGData.Instance:GetDailyRewardFlag()
    if daily_reward_flag == 0 then
        RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_FETCH_POP_SERVICE_REWARD)
    end
end

VipServiceRewardItemRender = VipServiceRewardItemRender or BaseClass(BaseRender)
function VipServiceRewardItemRender:__init()
end

function VipServiceRewardItemRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function VipServiceRewardItemRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function VipServiceRewardItemRender:OnFlush()
    if not self.data then return end

    self.item_cell:SetData(self.data)
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if cfg then
        self.node_list.item_name.text.text = ToColorStr(cfg.name, ITEM_COLOR[cfg.color])
    end
end