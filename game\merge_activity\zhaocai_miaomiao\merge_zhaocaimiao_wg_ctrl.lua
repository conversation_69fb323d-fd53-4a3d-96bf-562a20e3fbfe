require("game/merge_activity/zhaocai_miaomiao/merge_zhaocaimiao_wg_data")
require("game/merge_activity/zhaocai_miaomiao/merge_zhaocaimiao_view")
require("game/merge_activity/zhaocai_miaomiao/merge_zhaocaimiao_result_view")
require("game/merge_activity/zhaocai_miaomiao/merge_zhaocaimiao_recharge_tip")
-- require("game/operation_activity/tiancai_chushen/tiancai_chushen_second_view")
-- require("game/operation_activity/tiancai_chushen/tiancai_chushen_result_view")

MergeZhaoCaiMiaoWGCtrl = MergeZhaoCaiMiaoWGCtrl or BaseClass(BaseWGCtrl)

function MergeZhaoCaiMiaoWGCtrl:__init()
	if MergeZhaoCaiMiaoWGCtrl.Instance then
		ErrorLog("[MergeZhaoCaiMiaoWGCtrl] Attemp to create a singleton twice !")
	end
	MergeZhaoCaiMiaoWGCtrl.Instance = self

	self.zhaocaimiao_data = MergeZhaoCaiMiaoWGData.New()
	self.zhaocai_miaomiao_record_view = MergeZhaoCaiMiaoMiaoRecordPanel.New()
	self.zhaocai_miaomiao_recharge_tip = MergeZhaoCaiMiaoMiaoRechargeTipsPanel.New()
	-- self.chushen_result_view = ChuShenCookResultPanel.New()
	-- self.chushen_unlock_view = ChuShenRewardShowPanel.New()

	self:RegisterAllProtocols()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MiaoMiaoInfoReq, self))
	--MergeActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/operation_activity_zhaocaimiaomiao_auto", BindTool.Bind(self.OnHotUpdate, self))
end

function MergeZhaoCaiMiaoWGCtrl:__delete()
	MergeZhaoCaiMiaoWGCtrl.Instance = nil

	if self.zhaocaimiao_data then
		self.zhaocaimiao_data:DeleteMe()
		self.zhaocaimiao_data = nil
	end

	if self.zhaocai_miaomiao_recharge_tip then
		self.zhaocai_miaomiao_recharge_tip:DeleteMe()
		self.zhaocai_miaomiao_recharge_tip = nil
	end

end

function MergeZhaoCaiMiaoWGCtrl:OpenMiaoMiaoRecordPanel()
	if self.zhaocai_miaomiao_record_view then
		self.zhaocai_miaomiao_record_view:Open()
	end
end

function MergeZhaoCaiMiaoWGCtrl:OpenRechagreTips(tip_type, num, oa_index)
	oa_index = oa_index or MergeZhaoCaiMiaoWGData.Instance:GetAciOaIndex()
	self.zhaocai_miaomiao_recharge_tip:SetContentType(tip_type, num, oa_index)
end

function MergeZhaoCaiMiaoWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCSAZhaoCaiMaoInfo,'OnSCCSAZhaoCaiMaoInfo')
	self:RegisterProtocol(SCCSAZhaoCaiMaoChouJiangInfo,'OnSCCSAZhaoCaiMaoChouJiangInfo')
	self:RegisterProtocol(SCCSAZhaoCaiMaoChouJiangRecord,'OnSCCSAZhaoCaiMaoChouJiangRecord')--抽奖记录
	self:RegisterProtocol(CSCSAZhaoCaiMaoOp)
end

function MergeZhaoCaiMiaoWGCtrl:MiaoMiaoOperateSeq(operate_typr, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCSAZhaoCaiMaoOp)
	protocol.operate_type = operate_typr
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function MergeZhaoCaiMiaoWGCtrl:OnSCCSAZhaoCaiMaoInfo(protocol)
	local all_count = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoTotalCountActInfo()
	MergeActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.MERGE_ACT_ZHAOCAIMAO)
	local is_play_count_add = protocol.tot_choujiang_times > all_count and all_count >= 0
	local is_open = ViewManager.Instance:IsOpenByIndex(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2112)
    self.zhaocaimiao_data:SetInfo(protocol)
    if is_open then
        if protocol.op_type == CSA_ZHAOCAIMAO_OP_TYPE.CSA_ZHAOCAIMAO_OP_FETCH_CHONGZHI_TASK then --播放充值成功
		    MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2112, "ZhaoCaiMiaoMiaoPlayNum")
        elseif protocol.op_type == CSA_ZHAOCAIMAO_OP_TYPE.CSA_ZHAOCAIMAO_OP_FETCH_CHOUJIANG  then --领取 播放仙玉动画
            MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2112, "ZhaoCaiMiaoMiaoPlayXianyu")
        else
            MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2112)
        end
    end
	RemindManager.Instance:Fire(RemindName.MergeZhaoCaiMiaoMiao)
end

function MergeZhaoCaiMiaoWGCtrl:OnSCCSAZhaoCaiMaoChouJiangInfo( protocol )
	MergeZhaoCaiMiaoWGData.Instance:SetResultMoney(protocol.cur_choujiang_xianyu)
	if protocol.cur_choujiang_xianyu > 0 then
        --self:OpenMiaoMiaoResultPanel()
        MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2112, "ZhaoCaiMiaoMiaoPlayTween") --抽奖 播放数字动画
	end
end

function MergeZhaoCaiMiaoWGCtrl:MiaoMiaoInfoReq()
	self:MiaoMiaoOperateSeq(ZHAOCAIMAO_OP_TYPE.ZHAOCAIMAO_INFO)

end
function MergeZhaoCaiMiaoWGCtrl:OnHotUpdate()
	self.zhaocaimiao_data:LoadConfig()
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2112, "ZhaoCaiMiaoMiaoHotUpdata")
end

function MergeZhaoCaiMiaoWGCtrl:OnSCCSAZhaoCaiMaoChouJiangRecord(protocol)
	self.zhaocaimiao_data:SetRecordInfo(protocol)
	self.zhaocai_miaomiao_record_view:Flush()
end