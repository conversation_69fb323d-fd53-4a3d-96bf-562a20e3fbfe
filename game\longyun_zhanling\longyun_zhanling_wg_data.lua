LongYunZhanLingWGData = LongYunZhanLingWGData or BaseClass()

function LongYunZhanLingWGData:__init()
	if nil ~= LongYunZhanLingWGData.Instance then
		ErrorLog("[LongYunZhanLingWGData]:Attempt to create singleton twice!")
	end
	LongYunZhanLingWGData.Instance = self

	self:InitParam()
	self:InitConfig()

	-- 每日任务
	RemindManager.Instance:Register(RemindName.LongYunZhanLingTask,
		BindTool.Bind(self.GetLongYunZhanLingTaskRemind, self))
end

function LongYunZhanLingWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.LongYunZhanLingTask)
	LongYunZhanLingWGData.Instance = nil
end

-- 默认参数
function LongYunZhanLingWGData:InitParam()
	self.devote = 0                --当前贡献值.
	self.special_flag = {}         --特殊标记--第一个位 贡献积分额外奖励   锁标记     第二位 战令标记.
	self.fame = 0                  --当前名望值.
	self.fame_reward_flag = {}     --名望奖励领取标记.
	self.task_red_list = nil       --红点列表.
	self.task_level = 0            --任务等级.
	self.task_value_list = {}      --任务进度列表.
	self.task_reward_flag = {}     --任务奖励标记.
	self.cycle_group_index = 0     -- 战令的档次group_index
	self.order_reward_flag = {}    --普通战令奖励标记.
	self.order_added_reward_flag = {} --高级战令奖励标记.
	self.zhanling_level = 0
	self.zhanling_max_level_cfg = {}
end

-- 初始化配置
function LongYunZhanLingWGData:InitConfig()
	local country_auto = ConfigManager.Instance:GetAutoConfig("country_auto")
	self.other_cfg = country_auto.other[1]
	self.daily_task_info_cfg = ListToMapList(country_auto.daily_task, "week_day")
	self.daily_fame_reward_cfg = country_auto.daily_fame_reward
	self.task_type_cfg = ListToMapList(country_auto.task, "task_type")
	self.task_cfg = ListToMap(country_auto.task, "task_id")
	self.task_quality_cfg = country_auto.task_quality
	self.group_cfg = ListToMap(country_auto.order_group, "index")
	self.order_cfg = ListToMap(country_auto.order, "group_index", "seq")
	self.order_exp_cfg = ListToMapList(country_auto.order_exp, "group_index")

	self:InitDataCache()
end

-------------------------------------INIT_CACHE_START-----------------------------------------
function LongYunZhanLingWGData:InitDataCache()
	local zhanling_max_level_cfg = {}
	for k, v in pairs(self.order_cfg) do
		zhanling_max_level_cfg[k] = zhanling_max_level_cfg[k] or {}

		local max_level_cfg = {}
		for i, u in pairs(v) do
			if IsEmptyTable(max_level_cfg) or u.need_devote > max_level_cfg.need_devote then
				max_level_cfg = u
			end
		end

		zhanling_max_level_cfg[k] = max_level_cfg
	end

	self.zhanling_max_level_cfg = zhanling_max_level_cfg
end

-------------------------------------INIT_CACHE_END-----------------------------------------

function LongYunZhanLingWGData:GetTaskCfg(task_id)
	return self.task_cfg[task_id]
end

function LongYunZhanLingWGData:GetTaskTypeCfg(task_type)
	return self.task_type_cfg[task_type]
end

function LongYunZhanLingWGData:GetTaskQualityList()
	return self.task_quality_cfg
end

function LongYunZhanLingWGData:GetDailyFameRewardCfg()
	return self.daily_fame_reward_cfg
end

function LongYunZhanLingWGData:GetDailyTaskCfg(task_week_day, level)
	if task_week_day == nil or level == nil then
		return nil
	end

	local data = self.daily_task_info_cfg[task_week_day]
	if data ~= nil then
		local cfg = nil
		for i = 1, #data do
			if data[i].level_limit_min <= level then
				cfg = data[i]
			else
				break
			end
		end

		return cfg
	end

	return nil
end

function LongYunZhanLingWGData:GetTaskList(task_type)
	local data_list = {}
	local data = self:GetDailyTaskCfg(self:GetNowWeekDate(), self.task_level)

	if data ~= nil then
		local t = Split(data.task_id_list, "|")
		local index = 1
		for i = 1, #t do
			local task_index = tonumber(t[i])
			local cfg = self:GetTaskCfg(task_index)
			if cfg and task_type == cfg.quality then
				local sort_value = cfg.task_id

				local is_get = false
				local value = 0
				if self.task_reward_flag[i] then
					is_get = self.task_reward_flag[i] == 1
				end

				if self.task_value_list[cfg.task_type] then
					value = self.task_value_list[cfg.task_type]
				end

				if value >= cfg.task_value and not is_get then
					sort_value = sort_value + 10000
				elseif not is_get then
					sort_value = sort_value + 1000
				end

				data_list[index] = { cfg = cfg, is_get = is_get, value = value, task_index = i, sort_value = sort_value }
				index = index + 1
			end
		end
	end

	table.sort(data_list, SortTools.KeyUpperSorter("sort_value"))
	return data_list
end

function LongYunZhanLingWGData:GetTerritoryFameList()
	local max_value = 0
	local data_list = {}
	local data = self:GetDailyFameRewardCfg()
	if data ~= nil then
		for i = 0, #data do
			if max_value < data[i].need_fame then
				max_value = data[i].need_fame
			end
		end

		data_list = data
	end

	return data_list, max_value
end

--任务奖励进度
function LongYunZhanLingWGData:GetCurSliderProgress()
	local reward_cfg = {}
	local cur_progress = 0
	local cur_act_light = 0
	local reward_cfg, max_value = self:GetTerritoryFameList()
	local cur_fame = self:GetLongYunZhanLingFame()
	if IsEmptyTable(reward_cfg) or cur_fame == nil then
		return cur_progress
	end

	local progress_list = { 0.07, 0.186, 0.303, 0.42, 0.537, 0.654, 0.771, 0.888, 1 } --对应的进度条值
	for k, v in pairs(progress_list) do
		local seq = k - 1
		local reward_seq = k - 2
		local length = #progress_list
		local cur_need = reward_cfg[reward_seq] and reward_cfg[reward_seq].need_fame or 0
		local next_need = reward_cfg[reward_seq + 1] and reward_cfg[reward_seq + 1].need_fame or
			reward_cfg[#reward_cfg].need_fame
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		if cur_fame > cur_need and cur_fame <= next_need then
			cur_progress = (cur_fame - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif cur_fame > reward_cfg[#reward_cfg].need_fame then
			cur_progress = progress_list[length]
			break
		end
		if cur_fame > cur_need then
			cur_act_light = cur_act_light + 1
		end
	end
	return cur_progress, cur_act_light
end

function LongYunZhanLingWGData:GetLongYunZhanLingTaskRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.LongYunZhanLingTaskView) then
		return 0
	end

	local data = self:GetDailyFameRewardCfg()
	local role_fame = self:GetLongYunZhanLingFame()
	if data ~= nil then
		for i = 1, #data do
			if role_fame >= data[i].need_fame then
				local is_can = self:CheckDailyFameRewardCanGet(data[i].reward_index)
				if is_can then
					return 1
				end
			end
		end
	end

	if self.task_red_list ~= nil then
		for k, v in pairs(self.task_red_list) do
			if v.num > 0 then
				return 1
			end
		end
	end

	local remind_get = self:GetZhanLingRemind() == 1
	if remind_get then
		return 1
	end

	return 0
end

function LongYunZhanLingWGData:GetZhanLingMaxLevelCfg()
	local group_index = self:GetMyGroupIndex()
	return self.zhanling_max_level_cfg[group_index]
end

function LongYunZhanLingWGData:GetCurGroupCfg()
	local group_index = self:GetMyGroupIndex()
	return self.group_cfg[group_index]
end

function LongYunZhanLingWGData:GetCurZhanLingRewardCfgList()
	local group_index = self:GetMyGroupIndex()
	return self.order_cfg[group_index]
end

function LongYunZhanLingWGData:CalZhanLingLevel()
	local level = 0
	local devote = self:GetLongYunZhanLingDevote()

	if devote > 0 then
		local zhanling_reward_cfg = self:GetCurZhanLingRewardCfgList()

		if not IsEmptyTable(zhanling_reward_cfg) then
			for k, v in pairs(zhanling_reward_cfg) do
				if devote >= v.need_devote and v.seq > level then
					level = v.seq
				end
			end
		end
	end

	self.zhanling_level = level
end

function LongYunZhanLingWGData:GetZhanLingLevel()
	return self.zhanling_level
end

function LongYunZhanLingWGData:GetZhanLingLevelByDevote(devote)
	local cur_zhanling_cfg = self:GetCurZhanLingRewardCfgList()
	local level = 0

	for k, v in pairs(cur_zhanling_cfg) do
		if devote < v.need_devote then
			break
		end

		level = v.seq
	end

	return level
end

function LongYunZhanLingWGData:GetZhanLingCfg(seq)
	local group_index = self:GetMyGroupIndex()
	return (self.order_cfg[group_index] or {})[seq]
end

function LongYunZhanLingWGData:GetZhanLingBuyLevelCfg()
	local group_index = self:GetMyGroupIndex()
	return self.order_exp_cfg[group_index]
end

-- 是否解锁高级战令
function LongYunZhanLingWGData:GetHigerOrderRewardFlag()
	return self:GetSpecialFlag(COUNTRY_OPERATE_SPECIAL_FLAG_TYPE.ORDER_ADDED)
end

-- 普通奖励 高级奖励 领取标记
function LongYunZhanLingWGData:IsGetZhanLingRewardBySeq(seq)
	return self.order_reward_flag[seq] == 1, self.order_added_reward_flag[seq] == 1
end

-- 普通奖励 高级奖励能否领取
function LongYunZhanLingWGData:IsCanGetZhanLingRewardBySeq(seq)
	local can_get_nor, can_get_highr = false, false
	-- 没领取
	local is_get_nor, is_get_highr = self:IsGetZhanLingRewardBySeq(seq)

	local cur_reward_cfg = self:GetZhanLingCfg(seq)

	if IsEmptyTable(cur_reward_cfg) then
		return can_get_nor, can_get_highr
	end

	local cur_devote = self:GetLongYunZhanLingDevote()
	local is_open_higer_zhanling = self:GetHigerOrderRewardFlag()
	can_get_nor = not is_get_nor and (cur_devote >= cur_reward_cfg.need_devote)
	can_get_highr = not is_get_highr and (cur_devote >= cur_reward_cfg.need_devote) and is_open_higer_zhanling

	return can_get_nor, can_get_highr
end

function LongYunZhanLingWGData:GetZhanLingRemind()
	local reward_list = self:GetCurZhanLingRewardCfgList()

	if not IsEmptyTable(reward_list) then
		for k, v in pairs(reward_list) do
			local nor_can_get, high_can_get = self:IsCanGetZhanLingRewardBySeq(v.seq)
			if nor_can_get or high_can_get then
				return 1
			end
		end
	end

	return 0
end

--可以获取奖励.
function LongYunZhanLingWGData:CanGetZhanLingAllReward()
	local reward_list = self:GetCurZhanLingRewardCfgList()
	local count = 0

	if not IsEmptyTable(reward_list) then
		for k, v in pairs(reward_list) do
			local is_get_nor, is_get_highr = self:IsGetZhanLingRewardBySeq(v.seq)
			if not is_get_nor or not is_get_highr then
				count = count + 1
			end
		end
	end

	return count > 0 and true or false
end

--获取战令是否最高级.
function LongYunZhanLingWGData:GetZhanLingIsMaxLevel()
	local cur_lv = self:GetZhanLingLevel()
	local max_level_cfg = self:GetZhanLingMaxLevelCfg()

	if IsEmptyTable(max_level_cfg) then
		return false
	end

	return max_level_cfg.seq == cur_lv
end

--获取重置贡献与战令的时间戳.
function LongYunZhanLingWGData:GetZhanLingResetTime()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cycle_day = self.other_cfg.order_cycle_day
	local day = cur_day % cycle_day

	-- 获取当天剩余时间
	local sur_time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
	local time = ((day - 1) * 86400) + sur_time

	return time
end

--获取重置贡献与战令的固定周期时间戳.
function LongYunZhanLingWGData:GetZhanLingCycleTime()
	return self.other_cfg.order_cycle_day * 86400
end

--获取当前是星期几.
function LongYunZhanLingWGData:GetNowWeekDate()
	local date = tonumber(os.date("%w", TimeWGCtrl.Instance:GetServerTime()))
	return date
end

--判断战令功能是否开启.
function LongYunZhanLingWGData:CheckZhanLingIsOpen()
	local role_lv = RoleWGData.Instance:GetRoleLevel()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	return server_day >= self.other_cfg.open_day and role_lv >= self.other_cfg.open_level
end

-----------------------------------------协议-------------------------------------------
--基础信息.
function LongYunZhanLingWGData:SetCountryRoleFameInfo(protocol)
	self.devote = protocol.devote
	self.special_flag = protocol.special_flag
	self.fame = protocol.fame
	self.fame_reward_flag = protocol.fame_reward_flag

	self:CalZhanLingLevel()
end

--获取当前贡献值.
function LongYunZhanLingWGData:GetLongYunZhanLingDevote()
	return self.devote
end

--获取贡献标记.
function LongYunZhanLingWGData:GetLongYunZhanLingSpecial()
	return self.special_flag
end

function LongYunZhanLingWGData:GetSpecialFlag(seq)
	return self.special_flag[seq] == 1
end

--获取当前名望值.
function LongYunZhanLingWGData:GetLongYunZhanLingFame()
	return self.fame
end

--获取名望奖励领取标记.
function LongYunZhanLingWGData:GetLongYunZhanLingFameReward()
	return self.fame_reward_flag
end

--任务信息.
function LongYunZhanLingWGData:SetCountryAllTaskValeuInfo(protocol)
	self.task_level = protocol.task_level
	self.task_value_list = protocol.task_value_list

	local bit_list = {}
	local index = 1
	for i, v in ipairs(protocol.task_reward_get_flag) do
		bit_list = bit:d2b(v, bit_list)
		for ii = 32, 25, -1 do
			self.task_reward_flag[index] = bit_list[ii]
			index = index + 1
		end
	end
	self:SetTaskRedInfo()
end

function LongYunZhanLingWGData:SetTaskRedInfo()
	self.task_red_list = {} --重置.
	local data = self:GetDailyTaskCfg(self:GetNowWeekDate(), self.task_level)
	if data ~= nil then
		local t = Split(data.task_id_list, "|")
		for i = 1, #t do
			local task_index = tonumber(t[i])
			local cfg = self:GetTaskCfg(task_index)
			local is_get = false
			local value = 0
			if self.task_reward_flag[i] then
				is_get = self.task_reward_flag[i] == 1
			end

			if cfg and self.task_value_list[cfg.task_type] then
				value = self.task_value_list[cfg.task_type]
			end

			if cfg and not is_get and value >= cfg.task_value then
				if self.task_red_list[cfg.quality] == nil then
					self.task_red_list[cfg.quality] = { num = 0, task_list = {} }
				end
				self.task_red_list[cfg.quality].num = self.task_red_list[cfg.quality].num + 1
				self.task_red_list[cfg.quality].task_list[cfg.task_id] = 1
			end
		end
	end
end

function LongYunZhanLingWGData:SetCountryCurTaskValueInfo(protocol)
	self.task_value_list[protocol.task_type] = protocol.task_value
	self:FlushTaskRedInfo(protocol.task_type, protocol.task_value)
end

--战令信息.
function LongYunZhanLingWGData:SetLongYunZhanLingInfo(protocol)
	self.cycle_group_index = protocol.order_group_index -- 战令的档次group_index

	local function cal_order_reward_flag(data)
		local index = 0
		local bit_list = {}
		local order_reward_flag = {}

		for i, v in ipairs(data) do
			bit_list = bit:d2b(v, bit_list)
			for ii = 1, 8 do
				order_reward_flag[index] = bit_list[33 - ii]
				index = index + 1
			end
		end

		return order_reward_flag
	end

	self.order_reward_flag = cal_order_reward_flag(protocol.order_reward_flag)
	self.order_added_reward_flag = cal_order_reward_flag(protocol.order_added_reward_flag)
end

function LongYunZhanLingWGData:GetMyGroupIndex()
	return self.cycle_group_index
end

function LongYunZhanLingWGData:GetTaskJumpToIndex(select_index)
	select_index = select_index or 1
	local cfg = self:GetTaskQualityList()
	if IsEmptyTable(cfg) then
		return select_index
	end

	for k, v in ipairs(cfg) do
		if LongYunZhanLingWGData.Instance:GetTaskRemindByQuality(v.quality) then
			return k --v.quality
		end
	end

	return select_index
end

function LongYunZhanLingWGData:GetTaskRemindByQuality(quality)
	local has_red = false
	if self.task_red_list ~= nil then
		if self.task_red_list[quality] ~= nil then
			has_red = self.task_red_list[quality].num > 0
		end
	end

	return has_red
end

function LongYunZhanLingWGData:FlushTaskRedInfo(task_type, task_value)
	if self.task_red_list == nil then
		return
	end
	local task_type_cfg = self:GetTaskTypeCfg(task_type)
	local data = self:GetDailyTaskCfg(self:GetNowWeekDate(), self.task_level)
	if task_type_cfg ~= nil then
		for k, v in pairs(task_type_cfg) do
			local cfg = self:GetTaskCfg(v.task_id)
			if data ~= nil then
				local t = Split(data.task_id_list, "|")
				for i = 1, #t do
					local task_index = tonumber(t[i])
					local is_get = false
					if self.task_reward_flag[i] then
						is_get = self.task_reward_flag[i] == 1
					end
					if cfg and v.task_id == task_index then
						if not is_get and task_value >= cfg.task_value then
							if self.task_red_list[cfg.quality] == nil then
								self.task_red_list[cfg.quality] = { num = 0, task_list = {} }
							end
							self.task_red_list[cfg.quality].num = self.task_red_list[cfg.quality].num + 1
							self.task_red_list[cfg.quality].task_list[v.task_id] = 1
						end
					end
				end
			end
		end
	end
end

function LongYunZhanLingWGData:CheckDailyFameRewardCanGet(index)
	local is_can = false
	if self.fame_reward_flag ~= nil and self.fame_reward_flag[index] == 0 then
		is_can = true
	end

	return is_can
end
