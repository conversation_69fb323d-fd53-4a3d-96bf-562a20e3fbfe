PhantomDreamlandSceneLogic = PhantomDreamlandSceneLogic or BaseClass(CommonFbLogic)

function PhantomDreamlandSceneLogic:__init()
end

function PhantomDreamlandSceneLogic:__delete()
end

function PhantomDreamlandSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	TaskWGCtrl.Instance:AddFlyUpList(function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		-- if GuajiCache.guaji_type ~= GuajiType.Auto then
		-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		-- end
	end)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		if Scene.Instance:GetSceneType() == SceneType.PHANTOM_DREAMLAND_FB then
			MainuiWGCtrl.Instance:SetTaskContents(false)
			MainuiWGCtrl.Instance:SetOtherContents(true)
			MainuiWGCtrl.Instance:SetFBNameState(false)
			MainuiWGCtrl.Instance:SetTeamBtnState(false)
			PhantomDreamlandWGCtrl.Instance:OpenDreamlandFbSceneView()
		end
	end)

	local view = PhantomDreamlandWGCtrl.Instance:GetDreamlandFbSceneView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
end

function PhantomDreamlandSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	
	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	
	local view = PhantomDreamlandWGCtrl.Instance:GetDreamlandFbSceneView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	PhantomDreamlandWGCtrl.Instance:CloseDreamlandFbSceneView()
end