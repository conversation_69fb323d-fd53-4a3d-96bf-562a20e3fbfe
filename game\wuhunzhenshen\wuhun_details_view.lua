

function WuHunView:WuHunDetailsInit()
	self:ClearViewTween()
	self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)
end

function WuHunView:WuHunDetailsLoadCallBack()
	if self.wuhun_skill_list == nil then
		self.wuhun_skill_list = {}
		for i = 1, 4 do
			local skill_obj = self.node_list["wuhun_skill_list"]:FindObj(string.format("btn_skill%d", i))
			if skill_obj then
				local cell = TianShenWuHunSkillRender.New(skill_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.OnSelectTianShenWuHunSkillCallBack, self, cell))
				self.wuhun_skill_list[i] = cell
			end
		end
	end

	XUI.AddClickEventListener(self.node_list.wuhun_skill_show_btn, BindTool.Bind2(self.WuHunSkillShow, self))
	XUI.AddClickEventListener(self.node_list.wuhun_peiyang_btn, BindTool.Bind2(self.<PERSON><PERSON><PERSON><PERSON>, self))
	XUI.AddClickEventListener(self.node_list.wuhun_hunli_btn, BindTool.Bind2(self.WuHunLipanelOpen, self))
	XUI.AddClickEventListener(self.node_list.wuhun_tequan_show, BindTool.Bind(self.OnClickBtnOpenWuHunPrerogativeView, self))
end

function WuHunView:WuHunDetailsReleseCallBack()
	if self.wuhun_skill_list and #self.wuhun_skill_list > 0 then
		for _, wuhun_skill_cell in ipairs(self.wuhun_skill_list) do
			wuhun_skill_cell:DeleteMe()
			wuhun_skill_cell = nil
		end

		self.wuhun_skill_list = nil
	end
end

-- 武魂技能点击返回
function WuHunView:OnSelectTianShenWuHunSkillCallBack(item)
	if nil == item or nil == item.data then
		return
	end

	local skill_data = item.data
	if skill_data then
		if skill_data.no_open_wuhun then
			-- 提示未激活任何武魂
			SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunErrorTips2)
		else
			-- 打开替换技能
			WuHunWGCtrl.Instance:OpenWuHunSkillView()
		end
	end
end

function WuHunView:FlushWuHunJump()
	local skill_data = WuHunWGData.Instance:GetWuHunSkillData()

	if self.wuhun_skill_list and skill_data then
		for i, skill_cell in ipairs(self.wuhun_skill_list) do
			skill_cell:SetData(skill_data[i])
		end
	end
end

----------------------------------------------------------------------------------------------
function WuHunView:WuHunSkillShow()
	local wuhun_id = 1001 or 0
	local wuhun_lv = 0
	CommonSkillShowCtrl.Instance:SetWuHunSkillViewDataAndOpen({
		wuhun_id = wuhun_id,
		wuhun_lv = wuhun_lv,
	})
end

function WuHunView:WuHunPeiYang()
	WuHunWGCtrl.Instance:OpenWuHunCultivateView()
end

-- 打开武魂之力介绍界面
function WuHunView:WuHunLipanelOpen()
	WuHunWGCtrl.Instance:OpenWuHunZhanLiView()
end

-- 打开武魂特权界面.
function WuHunView:OnClickBtnOpenWuHunPrerogativeView()

	local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.WuHunPrerogativeView)
	if is_open then
		ViewManager.Instance:Open(GuideModuleName.WuHunPrerogativeView)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FunOpenTip)
	end
end

--===================================================================
TianShenWuHunSkillRender = TianShenWuHunSkillRender or BaseClass(BaseRender)
function TianShenWuHunSkillRender:OnFlush()
	if self.data then
		self.node_list["skill_icon"]:SetActive(self.data.is_have_skill)
		-- self.node_list["skill_dec"]:SetActive(self.data.is_have_skill)
		-- self.node_list["add_skill"]:SetActive(not self.data.is_have_skill)
		if self.data.is_have_skill then
			-- 设置技能图标
			local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(self.data.wuhun_id)

			if not active_cfg then
				self.node_list.skill_icon:CustomSetActive(false)
				return
			end

			local skill_level = WuHunWGData.Instance:GetWuHunSkillLevel(self.data.wuhun_id, self.data.level, self.data.promotion_state)
			local wuhun_skill_client_cfg = WuHunWGData.Instance:GetWuHunClientSkillCfg(active_cfg.skill_id, skill_level)

			if not wuhun_skill_client_cfg then
				self.node_list.skill_icon:CustomSetActive(false)
				return
			end

			self.node_list.skill_icon:CustomSetActive(true)
			self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(wuhun_skill_client_cfg.icon_resource))

			local wuhun_skill_skill_cfg = WuHunWGData.Instance:GetWuHunSkillCfg(active_cfg.skill_id, skill_level)

			if not wuhun_skill_skill_cfg then
				return
			end

			-- self.node_list.skill_dec.text.text = wuhun_skill_skill_cfg.skill_name
		end

		self.node_list["btn_skill_red"]:SetActive(self.data.is_can_add_skill)
	end
end