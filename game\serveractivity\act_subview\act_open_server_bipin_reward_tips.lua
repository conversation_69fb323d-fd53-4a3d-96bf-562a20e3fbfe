OpenServerRewardTips = OpenServerRewardTips or BaseClass(SafeBaseView)

function OpenServerRewardTips:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop

	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_open_server_bipin_reward")
end

function OpenServerRewardTips:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function OpenServerRewardTips:LoadCallBack()
	self.node_list["close_btn"].button:AddClickListener(BindTool.Bind(self.Close, self))

	if nil == self.reward_list_view then
		self.reward_list_view = AsyncListView.New(OpenServerRewardTipsRender, self.node_list["ph_item_list"])
	end

	self.rush_type = 1
end

function OpenServerRewardTips:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.rush_type then
				self.rush_type = v.rush_type
			end
		end
	end

	if self.reward_list_view then
		local target_reward_data_list = ServerActivityWGData.Instance:GetRushLastRewardItemListCfg(self.rush_type)
		local target_value_list = ServerActivityWGData.Instance:GetRushReachGoalList(self.rush_type)
		local data_list = {}
		for i = 1, #target_reward_data_list do
			data_list[i] = {
				rush_type = self.rush_type,
				target_value = target_value_list[i - 1],
				reward_data = target_reward_data_list[i],
				seq = i - 1
			}
		end

		local is_special_rank = ServerActivityWGData.Instance:IsSpecialRank(self.rush_type)
		local temp_list = {}

		for k, data in pairs(data_list) do
			local goal_reward_fetch = ServerActivityWGData.Instance:GetOpenServerGoalFetchFlags(data.rush_type, data.seq)
			--if is_special_rank then
			temp_list[goal_reward_fetch * 200 + k] = data
			--end
		end

		--if is_special_rank then
		data_list = SortTableKey(temp_list)
		--end

		self.reward_list_view:SetDataList(data_list)

		-- 奖励信息
		local title_desc = ""
		if ServerActivityWGData.Instance:IsSpecialRank(self.rush_type) then
			local rank_name = Language.OpenServer.LingQuLimit2[self.rush_type] or ""
			local my_desc = string.format(Language.OpenServer.My_Type, rank_name)
			title_desc = my_desc
		else
			local rank_name = Language.OpenServer.LingQuLimit2[self.rush_type] or ""
			title_desc = rank_name
		end

		local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(self.rush_type)
		local rank_data = RankWGData.Instance:GetActMyRank(opengame_cfg and opengame_cfg.rank_type)
		local value_str = ""
		if opengame_cfg and opengame_cfg.ranking_title and opengame_cfg.ranking_name then
			if Language.Common.GradeText == opengame_cfg.ranking_title then -- 如果是阶数
				if rank_data.rank_value <= 0 then
					value_str = string.format(Language.Rank.Jie, math.floor(rank_data.rank_value / 10), 0)
				else
					local _, temp = math.modf(rank_data.rank_value / 10)
					local value = rank_data.rank_value % 10
					if value == 0 then
						value = 10
					end
					if temp > 0 then
						value_str = string.format(Language.Rank.Jie, math.floor(rank_data.rank_value / 10) + 1, value)
					else
						value_str = string.format(Language.Rank.Jie, math.floor(rank_data.rank_value / 10), value)
					end
				end
			elseif is_special_rank then -- 特殊需求显示阶数
				local body_show_type = self.rush_type == 3 and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
				local info = NewAppearanceWGData.Instance:GetQiChongInfo(body_show_type)
				if not info then return end
				local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, info.star_level) or {}
				value_str = string.format(Language.Rank.Jie, cfg.grade_num, cfg.star_num)
			else
				value_str = rank_data.rank_value
			end
			self.node_list["star_num"].text.text = string.format("%s：%s", title_desc, value_str)
		end
	end
end

--------------------------------OpenServerRewardTipsRender--------------------------------
OpenServerRewardTipsRender = OpenServerRewardTipsRender or BaseClass(BaseRender)
function OpenServerRewardTipsRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function OpenServerRewardTipsRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))

	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell)
	end
end

function OpenServerRewardTipsRender:OnFlush()
	if not self.data then
		return
	end

	local reward_list = SortTableKey(self.data.reward_data)
	self.item_cell:SetData(reward_list[1])

	local desc = ""
	if ServerActivityWGData.Instance:IsSpecialRank(self.data.rush_type) then
		local body_show_type = self.data.rush_type == 3 and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
		local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, self.data.target_value) or {}
		desc = string.format(Language.OpenServer.BiPinRewardDesc_3, cfg.grade_num or 0, cfg.star_num or 0)
	else
		desc = self.data.target_value
	end
	local pre_text = Language.OpenServer.LingQuLimit5[self.data.rush_type] or ""
	self.node_list.lbl_lscs.text.text = pre_text .. desc

	local goal_reward_fetch = ServerActivityWGData.Instance:GetOpenServerGoalFetchFlags(self.data.rush_type,
		self.data.seq)
	local goal_reward_flag = ServerActivityWGData.Instance:GetOpenServerGoalFlags(self.data.rush_type, self.data.seq)
	local can_reward = goal_reward_flag == 1 and goal_reward_fetch == 0
	self.node_list.btn_lingqu:SetActive(can_reward)
	self.node_list.remind:SetActive(can_reward)
	self.node_list.image_ylq:SetActive(goal_reward_fetch == 1)
	self.node_list.image_wdc:SetActive(goal_reward_flag == 0)
end

function OpenServerRewardTipsRender:OnClickLingQu()
	if not self.data then
		return
	end

	local goal_reward_flag = ServerActivityWGData.Instance:GetOpenServerGoalFlags(self.data.rush_type, self.data.seq)
	local goal_reward_fetch = ServerActivityWGData.Instance:GetOpenServerGoalFetchFlags(self.data.rush_type,
		self.data.seq)
	local can_reward = goal_reward_flag == 1 and goal_reward_fetch == 0
	local seq = self.data.seq
	if ServerActivityWGData.Instance:IsSpecialRank(self.data.rush_type) then
		seq = self.data.seq + 1
		if not can_reward then
			local cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(self.data.rush_type)
			if cfg and cfg.turn_link ~= "" then
				FunOpen.Instance:OpenViewNameByCfg(cfg.turn_link)
				return
			end
		end
	end

	ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_REWARD_TYPE_DAILY_FETCH,
		self.data.rush_type, seq)
end
