CrossTreasureRewardView = CrossTreasureRewardView or BaseClass(SafeBaseView)
function CrossTreasureRewardView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(832, 596)})
	self:AddViewResource(0, "uis/view/cross_treasure_ui_prefab", "layout_cross_treasure_reward")
	self:SetMaskBg(true)
end

function CrossTreasureRewardView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.act_jjc_yulan_reward
	if not self.gather_reward_list then
		self.gather_reward_list = AsyncListView.New(CrossTreasureReward, self.node_list.gather_reward_list)
	end

	if not self.steal_reward_list then
		self.steal_reward_list = AsyncListView.New(CrossTreasureStealReward, self.node_list.steal_reward_list)
	end
end

function CrossTreasureRewardView:ReleaseCallBack()
	if self.gather_reward_list then
		self.gather_reward_list:DeleteMe()
		self.gather_reward_list = nil
	end

	if self.steal_reward_list then
		self.steal_reward_list:DeleteMe()
		self.steal_reward_list = nil
	end
end

function CrossTreasureRewardView:OnFlush()
	local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
	if not lingzhu_list then
		return
	end

	local aim_data_list = {}
	for i, v in ipairs(lingzhu_list) do
		local data_list = CrossTreasureWGData.Instance:GetAllTreasureLevelCfgBySeq(v.seq)
		if data_list then
			for _, data in ipairs(data_list) do
				table.insert(aim_data_list, data)
			end
		end
	end

	self.gather_reward_list:SetDataList(aim_data_list)
	self.steal_reward_list:SetDataList(aim_data_list)
end


-----------------------CrossTreasureReward-----------------
CrossTreasureReward = CrossTreasureReward or BaseClass(BaseRender)
function CrossTreasureReward:__init()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell,self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end
end

function CrossTreasureReward:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function CrossTreasureReward:OnFlush()
	if not self.data then return end 
	if self.data.gather_reward_item then
		self.reward_list:SetDataList(self.data.gather_reward_item)
	end

	self.node_list.type_name.text.text =  ToColorStr(self.data.name, ITEM_COLOR[self.data.color])
end

-----------------------CrossTreasureStealReward-----------------
CrossTreasureStealReward = CrossTreasureStealReward or BaseClass(BaseRender)
function CrossTreasureStealReward:__init()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell,self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end
end

function CrossTreasureStealReward:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function CrossTreasureStealReward:OnFlush()
	if not self.data then return end 

	if self.data.loot_reward_item then
		self.reward_list:SetDataList(self.data.loot_reward_item)
	end

	self.node_list.type_name.text.text =  ToColorStr(self.data.name, ITEM_COLOR[self.data.color])
end
