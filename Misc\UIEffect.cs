﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UIEffect : MaskableGraphic, IOverrideOrder
{
    private enum ZTestMode
    {
        Disabled,
        Never,
        Less,
        Equal,
        LessEqual,
        Greater,
        NotEqual,
        GreaterEqual,
        Always
    }

    private static readonly ObjectPool<CullStateChangedEvent> s_CullstateChangeEventPool = new ObjectPool<CullStateChangedEvent>(l => l.RemoveAllListeners());

    private Transform rootCanvasTransform;
    private Canvas groupCanvas;

    private Dictionary<Renderer, int> renderDic = new Dictionary<Renderer, int>();
    private List<RendererItem> sortOrderList = null;

    public bool IsIgnoreTimeScale = false;
    public bool isOffZTest = false;
    public int orderOffset = 0;

    private ParticleSystem[] particleSystems;
    private float deltaTime;
    private float timeAtLastFrame;
    private bool isCliped = false;

    private List<CanvasGroup> canvasGroupList = new List<CanvasGroup>();
    private float canvasGroupAlpha = 1f;

    protected override void Awake()
    {
        base.Awake();

        if (Application.isPlaying)
        {
            this.ResetRootCanvas();
        }

        var renderers = ListPool<Renderer>.Get();
        this.GetComponentsInChildren<Renderer>(true, renderers);
        for (int i = 0; i < renderers.Count; i++)
        {
            this.SetUILayer(renderers[i]);
            renderDic.Add(renderers[i], renderers[i].sortingOrder);
        }

        ListPool<Renderer>.Release(renderers);

        if (null == onCullStateChanged)
            onCullStateChanged = s_CullstateChangeEventPool.Get();
        onCullStateChanged.AddListener(CullChanged);
        ResetRootCamera();
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        this.ResetRootCanvas();
        this.ResumeClipState();
        this.ResetCanvasGroupList();
        this.UpdateAlpha();
        this.TrySetZTestOff();

#if UNITY_EDITOR
        ActiveMonitor.OnEnableObj(this.gameObject, "UIEffect");
#endif
    }

    protected override void OnDisable()
    {
        base.OnDisable();

#if UNITY_EDITOR
        ActiveMonitor.OnDisableObj(this.gameObject, "UIEffect");
#endif
    }

    void Update()
    {
        this.UpdateEffectInTimeScaleZero();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();

        if (Application.isPlaying)
        {
            OverrideOrderGroupMgr.Instance.RemoveFromGroup(this.groupCanvas, this);
            this.groupCanvas = null;
            this.rootCanvasTransform = null;
        }

        if (null != onCullStateChanged)
        {
            s_CullstateChangeEventPool.Release(onCullStateChanged);
            onCullStateChanged = null;
        }

        canvasGroupList.Clear();
    }

    protected override void OnTransformParentChanged()
    {
        base.OnTransformParentChanged();

        if (Application.isPlaying)
        {
            this.ResumeClipState();
            this.ResetRootCanvas();
            OverrideOrderGroupMgr.Instance.SetGroupCanvasDirty(this.groupCanvas);

            this.ResetCanvasGroupList();
        }
    }

    protected override void OnCanvasGroupChanged()
    {
        base.OnCanvasGroupChanged();
        this.UpdateAlpha();
    }

    protected override void OnPopulateMesh(VertexHelper toFill)
    {
        toFill.Clear();
    }

    public GameObject GetTarget()
    {
        if (null == this) return null;

        return this.gameObject;
    }

    void UpdateEffectInTimeScaleZero()
    {
        if (!IsIgnoreTimeScale)
            return;

        if (null == particleSystems)
        {
            particleSystems = this.GetComponentsInChildren<ParticleSystem>(true);
        }

        deltaTime = Time.realtimeSinceStartup - timeAtLastFrame;
        timeAtLastFrame = Time.realtimeSinceStartup;
        if (Mathf.Abs(Time.timeScale) < 1e-6)
        {
            for (int i = 0; i < particleSystems.Length; i++)
            {
                particleSystems[i].Simulate(deltaTime, false, false);
                particleSystems[i].Play();
            }
        }
    }

    private void SetUILayer(Renderer renderer)
    {
        if (null != renderer) renderer.gameObject.layer = GameLayers.UIEffect;
    }

    private void RevertLayer(Renderer renderer)
    {
        if (null != renderer) renderer.gameObject.layer = GameLayers.Default;
    }

    public void SetOverrideOrder(int order, int orderInterval, int maxOrder, out int incOrder)
    {
        incOrder = 0;
        if (null == sortOrderList)
        {
            sortOrderList = new List<RendererItem>(renderDic.Count);
            var iter = renderDic.GetEnumerator();
            while (iter.MoveNext())
            {
                sortOrderList.Add(new RendererItem(iter.Current.Key, iter.Current.Value));
                sortOrderList.Sort(CompareRenderer);
            }
        }

        if (sortOrderList.Count <= 0)
        {
            return;
        }

        int curOrder = sortOrderList[0].order;
        int childMax = maxOrder - 1;
        order = Mathf.Min(childMax, order);
        for (int i = 0; i < sortOrderList.Count; ++i)
        {
            var item = sortOrderList[i];
            if (null == item.renderer)
                continue;

            if (curOrder != item.order)
            {
                curOrder = item.order;
                ++order;
                order = Mathf.Min(childMax, order);
                item.renderer.sortingOrder = order + orderOffset;
                ++incOrder;
            }
            else
            {
                item.renderer.sortingOrder = order + orderOffset;
            }
        }
    }

    private List<Material> materialList;
    private List<Material> GetCloneMaterialList()
    {
        if (null == materialList)
        {
            materialList = new List<Material>();
        }

        // 可能还没有执行Awake，所以materialList数量为0时要再检查一遍
        if (materialList.Count <= 0)
        {
            var iter = renderDic.GetEnumerator();
            while (iter.MoveNext())
            {
                var mat = MaterialMgr.Instance.GetClonedMaterial(iter.Current.Key);
                materialList.Add(mat);
            }
        }

        return materialList;
    }

    private void ResumeClipState()
    {
        if (this.isCliped)
        {
            this.isCliped = false;
            for (int i = 0; i < this.materialList.Count; i++)
            {
                var material = this.materialList[i];
                if (null != material)
                {
                    if (material.shader.name == "Srp/Standrad/EffectCf")
                    {
                        material.DisableKeyword("ENABLE_POST_EFFECT");
                    }
                    else
                    {
                        material.DisableKeyword("ENABLE_CLIP_RECT");
                    }
                }
            }
        }
    }

    private void ResetCanvasGroupList()
    {
        canvasGroupList.Clear();
        GetComponentsInParent<CanvasGroup>(false, canvasGroupList);
    }

    private void ResetRootCanvas()
    {
        OverrideOrderGroupMgr.Instance.RemoveFromGroup(this.groupCanvas, this);
        this.groupCanvas = null;
        this.rootCanvasTransform = null;
        this.sortOrderList = null;

        var canvasScalers = ListPool<CanvasScaler>.Get();
        this.GetComponentsInParent(true, canvasScalers);
        if (canvasScalers.Count > 0)
        {
            var canvasScaler = canvasScalers[0];
            this.groupCanvas = OverrideOrderGroupMgr.Instance.AddToGroup(this);
            this.rootCanvasTransform = canvasScaler.transform;
        }

        ListPool<CanvasScaler>.Release(canvasScalers);
    }

    private Camera rootCamera;
    private void ResetRootCamera()
    {
        this.rootCamera = null;

        List<Canvas> canvas = new List<Canvas>();
        this.GetComponentsInParent(true, canvas);

        if (canvas.Count > 0)
        {
            Canvas canva = canvas[0];
            rootCamera = canva.worldCamera;
        }
    }

    public override void SetClipRect(Rect clipRect, bool validRect)
    {

        if (rootCamera == null)
        {
            return;
        }

        if (clipRect == null)
        {
            return;
        }


        if (null == this.rootCanvasTransform)
        {
            return;
        }

        base.SetClipRect(clipRect, validRect);

        if (validRect)
        {
            var materials = this.GetCloneMaterialList();
            this.isCliped = true;
            for (int i = 0; i < materials.Count; i++)
            {
                var material = materials[i];
                if (null != material)
                {
                    if (material.shader.name == "Srp/Standard/EffectCf")
                    {
                        material.EnableKeyword("ENABLE_POST_EFFECT");
                        material.SetFloat("_PostEffectType", 64);
                    }
                    else
                    {
                        material.EnableKeyword("ENABLE_CLIP_RECT");
                    }

                    Vector3 minWorldPos = this.rootCanvasTransform.TransformPoint(new Vector2(clipRect.x, clipRect.y));
                    Vector3 maxWorldPos = this.rootCanvasTransform.TransformPoint(new Vector2(clipRect.x + clipRect.width, clipRect.y + clipRect.height));
                    Vector3 minScreenPos = rootCamera.WorldToScreenPoint(minWorldPos);
                    Vector3 maxScreenPos = rootCamera.WorldToScreenPoint(maxWorldPos);

                    //Vector4 rect = new Vector4(minWorldPos.x, minWorldPos.y, maxWorldPos.x, maxWorldPos.y);
                    Vector4 rect = new Vector4(minScreenPos.x, minScreenPos.y, maxScreenPos.x, maxScreenPos.y);
                    material.SetVector("_ClipRect", rect);
                }
            }
        }
        else
        {
            if (null != this.materialList)
            {
                for (int i = 0; i < this.materialList.Count; i++)
                {
                    if (null != this.materialList[i])
                    {
                        this.materialList[i].DisableKeyword("ENABLE_CLIP_RECT");
                    }
                }
            }
        }
    }

    private void UpdateAlpha()
    {
        float alpha = 1f;
        for (int i = 0; i < canvasGroupList.Count; ++i)
        {
            if (null != canvasGroupList[i])
            {
                alpha *= canvasGroupList[i].alpha;
            }
        }

        if (canvasGroupAlpha != alpha)
        {
            canvasGroupAlpha = alpha;

            var materials = this.GetCloneMaterialList();
            for (int i = 0; i < materials.Count; i++)
            {
                if (null != materials[i])
                {
                    materials[i].SetFloat("_AlphaMultiplier", canvasGroupAlpha);
                }
            }
        }
    }

    // 关掉ZTest，以使不会被模型遮挡。（有需要的特效才处理，避免无谓的克隆材质球）
    private void TrySetZTestOff()
    {
        if (!this.isOffZTest) return;

        var materials = this.GetCloneMaterialList();
        for (int i = 0; i < materials.Count; i++)
        {
            if (null != materials[i])
            {
                materials[i].SetFloat("_ZTest", (float)ZTestMode.Always);
            }
        }

    }

    private void CullChanged(bool cull)
    {
        var list = ListPool<ParticleSystem>.Get();
        GetComponentsInChildren(list);

        for (int i = 0; i < list.Count; ++i)
        {
            var particle = list[i];
            particle.GetComponent<Renderer>().enabled = !cull;
        }

        ListPool<ParticleSystem>.Release(list);

        var iter = renderDic.GetEnumerator();
        while (iter.MoveNext())
        {
            iter.Current.Key.enabled = !cull;
        }
    }


    private struct RendererItem
    {
        public Renderer renderer;
        public int order;
        public RendererItem(Renderer renderer, int order)
        {
            this.renderer = renderer;
            order = order + 60000;
            this.order = order;
        }
    }

    private int CompareRenderer(RendererItem x, RendererItem y)
    {
        if (x.order > y.order)
            return 1;
        else if (x.order == y.order)
            return 0;
        else
            return -1;

    }

#if UNITY_EDITOR
    protected override void Reset()
    {
        base.Reset();
        raycastTarget = false;
    }
#endif
}
