require("game/pierre_direct_purchase/cambered_list")
require("game/pierre_direct_purchase/pierre_direct_purchase_wg_data")
require("game/pierre_direct_purchase/pierre_direct_purchase_view")
require("game/pierre_direct_purchase/pierre_direct_purchase_alert")


PierreDirectPurchaseWGCtrl = PierreDirectPurchaseWGCtrl or BaseClass(BaseWGCtrl)
function PierreDirectPurchaseWGCtrl:__init()
	if PierreDirectPurchaseWGCtrl.Instance then
		error("[PierreDirectPurchaseWGCtrl]:Attempt to create singleton twice!")
	end
	PierreDirectPurchaseWGCtrl.Instance = self

	self.act_is_open = false
    self.data = PierreDirectPurchaseWGData.New()
    self.view = PierreDirectPurchaseView.New(GuideModuleName.PierreDirectPurchaseView)

	self:RegisterAllProtocols()

	-- 天数监听
	self.day_pass_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind(self.<PERSON><PERSON>hang<PERSON>, self))

	-- 等级变化监听
	self.role_attr_change = BindTool.Bind(self.RoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_attr_change, {"level", "vip_level"})

	-- 玩家充值信息监听
	self.recharge_event = GlobalEventSystem:Bind(AuditEvent.RECHARGE_CHANGE, BindTool.Bind(self.RechargeChange, self))
end

function PierreDirectPurchaseWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	GlobalEventSystem:UnBind(self.day_pass_event)
	self.day_pass_event = nil

	RoleWGData.Instance:UnNotifyAttrChange(self.role_attr_change)
	self.role_attr_change = nil

	GlobalEventSystem:UnBind(self.recharge_event)
	self.recharge_event = nil

	self.act_is_open = nil
	PierreDirectPurchaseWGCtrl.Instance = nil
end

function PierreDirectPurchaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSRoleZhenPingZhiGouReq)
	self:RegisterProtocol(SCRoleZhenPingZhiGouInfo, "OnSCRoleZhenPingZhiGouInfo")
end

function PierreDirectPurchaseWGCtrl:OpenView()
    self.view:Open()
end

function PierreDirectPurchaseWGCtrl:FlushView(key, param_t)
	if self.view:IsOpen() then
    	self.view:Flush(nil, key, param_t)
	end
	
	PrivilegedGuidanceWGCtrl.Instance:FlushGuidanceView()
	ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_privilege)
	RemindManager.Instance:Fire(RemindName.PrivilegedGuidance)
end

function PierreDirectPurchaseWGCtrl:SendRequest(opera_type)
	-- print_error("【-----臻品直购操作----】：", opera_type)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleZhenPingZhiGouReq)
 	protocol.opera_type = opera_type or 0

 	protocol:EncodeAndSend()
end

function PierreDirectPurchaseWGCtrl:OnSCRoleZhenPingZhiGouInfo(protocol)
	-- print_error("【-----臻品直购信息----】：", protocol.daliy_reward_flag, protocol.zhigou_list)
	self.data:SetBuyInfo(protocol)
	self:FlushView("flag_change")
	RemindManager.Instance:Fire(RemindName.PierreDirectPurchase)
	self:SetActvityBtnBubble()
end

-- 活动入口
function PierreDirectPurchaseWGCtrl:ShowOrHideOpenActvityIcon()
	local is_open = self.data:CheckActIsOpen()
	--print_error("【------活动入口---】：", is_open)
	if is_open == self.act_is_open then
		return
	end

	self.act_is_open = is_open
	local status = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.ACT_PIERRE_DIRECT_PURCHASE, status)
	RemindManager.Instance:Fire(RemindName.PierreDirectPurchase)

	self:SetActvityBtnBubble()
end

-- 活动入口气泡
function PierreDirectPurchaseWGCtrl:SetActvityBtnBubble()
	local act_type = ACTIVITY_TYPE.ACT_PIERRE_DIRECT_PURCHASE
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(act_type)
	if is_open then
		local is_show_bubble = self.data:GetIsShowMainSpecialBubble()
		MainuiWGCtrl.Instance:FlushMainUiActBtnBubbleInfo(act_type, is_show_bubble, Language.PierreDirectPurchase.SpecialAcTBtnDesc)
	end
end

function PierreDirectPurchaseWGCtrl:DayChange()
	-- print_error("【-----天数----】：")
	local open_day, close_day, min_role_level, min_vip_level = self.data:GetActDayData()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if server_day < open_day or server_day > close_day then
		return
	end

	self:FlushView("data_change")
	self:ShowOrHideOpenActvityIcon()
end

function PierreDirectPurchaseWGCtrl:RoleAttrChange(attr_name, value, old_value)
	local open_day, close_day, min_role_level, min_vip_level = self.data:GetActDayData()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if server_day < open_day or server_day >= close_day then
		return
	end

	if attr_name == "level" and value >= min_role_level then
		-- print_error("【-----等级----】：")
		self:FlushView("data_change")
		self:ShowOrHideOpenActvityIcon()
	elseif attr_name == "vip_level" and value >= min_vip_level then
		-- print_error("【-----vip----】：")
		self:FlushView("data_change")
		self:ShowOrHideOpenActvityIcon()
	end
end

function PierreDirectPurchaseWGCtrl:OnBuyCallBack(protocol)
	if protocol.result == 1 then
		local reward_list = self.data:GetBuyRewardList(protocol.param1, protocol.param2)
		if not IsEmptyTable(reward_list) then
			TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
		end
	end
end

function PierreDirectPurchaseWGCtrl:RechargeChange(acc_total_gold)
	-- print_error("【-----累充----】：", acc_total_gold)
	self:FlushView("data_change")
	self:ShowOrHideOpenActvityIcon()
end
