-- 天神-出战
local DEFAULT_BATTLE_POSITION = {
	[1] = {x = -500, y = -42},
	[2] = {x = -240, y = -90},
	[3] = {x = 20, y = -90},
	[4] = {x = 294, y = -42},
}

local EFFECT_VALUE_X = 90 --拖动有效更换范围
local EFFECT_VALUE_Y = 200

function TianShenView:BattleReleaseCallBack()
	if self.battlet_index_cell_list then
		for k, v in pairs(self.battlet_index_cell_list) do
			v:DeleteMe()
		end
	end

	TianShenWGData.Instance:SetBattleDrugStatus(false)
	self.battlet_index_cell_list = {}
	self.battle_image_list = nil
end

function TianShenView:BattleLoadCallBack()
	self.battlet_index_cell_list = {}

	local item_cell = nil
	for i = 1, 4 do
		item_cell = BattleItem1.New(self.node_list["layout_battle_item" .. i], self)
		item_cell:SetIndex(i)
		self.battlet_index_cell_list[i] = item_cell
		self.node_list["battle_event" .. i].event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnChangeOrderUp, self, i))
		self.node_list["battle_event" .. i].event_trigger_listener:AddDragListener(BindTool.Bind2(self.OnDragChangeOrder, self, i))
		self.node_list["battle_event" .. i].event_trigger_listener:AddPointerDownListener(BindTool.Bind2(self.OnClickChangeOrder, self, i))
		self.node_list["battle_event" .. i].event_trigger_listener:AddPointerExitListener(BindTool.Bind2(self.CheckChangeOrderState, self, i))
	end
	
	XUI.AddClickEventListener(self.node_list["ts_guanghuan_btn"], BindTool.Bind(self.OnClickTSGuangHuanBtn, self))

	--self:PlayBattleEffect()
end

function TianShenView:PlayBattleEffect()
	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tiansheng_juqi)
	self.node_list.bt_effect:ChangeAsset(bundle_name, asset_name, false)
end

function TianShenView:BattleShowIndexCallBack()

end

function TianShenView:OnFlushBattleItem()
	if not self:IsLoadedIndex(0) then return end
	self.battle_image_list = TianShenWGData.Instance:GetFightTisnShen(true)
	for i = 1, 4 do
		self.node_list["layout_battle_item"..i].transform.anchoredPosition = DEFAULT_BATTLE_POSITION[i]
	end
	for i=1,#self.battlet_index_cell_list do
		self.battlet_index_cell_list[i]:SetData(self.battle_image_list[i - 1])
	end
end

function TianShenView:BattleOnFlush()
	self:OnFlushBattleItem()
end

function TianShenView:CacularDragTianShenIndex(position)
	if self.click_tianshen_order_index and  self.doing_order_change then
		for k, v in pairs(DEFAULT_BATTLE_POSITION) do
			if math.abs(v.x - position.x) <= EFFECT_VALUE_X and math.abs(v.y - position.y) <= EFFECT_VALUE_Y then
				if self.battle_image_list and self.battle_image_list[k - 1] then
					if self.battle_image_list[k - 1].appe_image_id and self.battle_image_list[k - 1].appe_image_id > 0 then
						return k
					end
				end
				break
			end
		end
	end
	return -1
end

function TianShenView:OnChangeOrderUp(index) --结束拖动
	if self.doing_order_change then
		if self.click_tianshen_order_index and self.tianshen_drag_finally_pos then
			TianShenWGData.Instance:SetBattleDrugStatus(true)
			local finally_index = self:CacularDragTianShenIndex(self.tianshen_drag_finally_pos)
			if finally_index ~= -1 and finally_index ~= self.click_tianshen_order_index then
				self.node_list["layout_battle_item"..finally_index].transform.anchoredPosition = DEFAULT_BATTLE_POSITION[self.click_tianshen_order_index]
				self.node_list["layout_battle_item"..self.click_tianshen_order_index].transform.anchoredPosition = DEFAULT_BATTLE_POSITION[finally_index]
				for i =1, 4 do
					if i ~= self.click_tianshen_order_index and i ~= finally_index then
						self.node_list["layout_battle_item"..i].transform.anchoredPosition = DEFAULT_BATTLE_POSITION[i]
					end
				end
				TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type20, self.battle_image_list[finally_index-1].index,self.battle_image_list[self.click_tianshen_order_index-1].index)						
			else
				for i =1, 4 do
					self.node_list["layout_battle_item"..i].transform.anchoredPosition = DEFAULT_BATTLE_POSITION[i]
				end

			end
		end
	end

	GlobalTimerQuest:AddDelayTimer(function ()
		TianShenWGData.Instance:SetBattleDrugStatus(false)
	end,0.2)

	self.tianshen_drag_finally_pos = nil
	self.click_tianshen_order_index = nil
	self.doing_order_change = false
end

function TianShenView:OnDragChangeOrder(index,eventData)  --持续更新
	if self.doing_order_change then
		self.drag_tianshen_order_index = index
		local x = eventData.position.x
		local y = eventData.position.y
		local _, position = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.node_list["layout_root"].transform, eventData.position , UICamera, Vector2(0, 0))
		self.node_list["layout_battle_item"..self.click_tianshen_order_index].transform.anchoredPosition = position
		-- if not self.data then
		TianShenWGData.Instance:SetBattleDrugStatus(false)
		-- else
		--TianShenWGData.Instance:SetBattleDrugStatus(true)
		-- end
		self.tianshen_drag_finally_pos = position
		local get_someone_index = self:CacularDragTianShenIndex(position)
		if get_someone_index ~= -1 and get_someone_index ~= self.click_tianshen_order_index then
			for i =1, 4 do
				if i == get_someone_index then
					self.node_list["layout_battle_item"..i].transform.anchoredPosition = DEFAULT_BATTLE_POSITION[self.click_tianshen_order_index]
				elseif i ~= self.click_tianshen_order_index then
					self.node_list["layout_battle_item"..i].transform.anchoredPosition = DEFAULT_BATTLE_POSITION[i]
				end
			end
		end
	end
end

function TianShenView:OnClickChangeOrder(index)
	TianShenWGData.Instance:SetBattleDrugStatus(false)
	self.click_tianshen_order_index = index
	self.drag_tianshen_order_index = nil
	self.tianshen_drag_finally_pos = nil
end

function TianShenView:CheckChangeOrderState(index) --离开
	if self.click_tianshen_order_index and self.click_tianshen_order_index == index then --拖动离开，开始显示拖动icon
		self.doing_order_change = true
	end
end

function TianShenView:OnClickTSGuangHuanBtn()
	TianShenWGCtrl.Instance:OpenTianShenZhenFaView()
end

-------------------------------------------------------------------------------
BattleItem1 = BattleItem1 or BaseClass(BaseRender)

function BattleItem1:__init()
	XUI.AddClickEventListener(self.node_list["img_add"], BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list["img_lock"],BindTool.Bind(self.LockOnClick,self))
	XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind(self.OnClick, self))
	--XUI.AddClickEventListener(self.node_list["battle_wuxing"], BindTool.Bind(self.OnClickBtnWuXingTip,self))
	self.role_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["ph_bianshen_display"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.S,
		can_drag = true,
	}
	
	self.role_model:SetRenderTexUI3DModel(display_data)
	self.role_model:SetVisible(true)
	-- self.role_model:SetUI3DModel(self.node_list["ph_bianshen_display"].transform, self.node_list.Block.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	self.role_model.parent_view = self
	self.call_back = nil
	self.is_open = false
	self.old_appe_image_id = nil
	self.old_shuangshen_appe_id = nil
end

function BattleItem1:__delete()
	self.is_open = false
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	self.old_shuangshen_appe_id = nil
end

function BattleItem1:ClickCallBack(call_back)
	self.call_back = call_back
end

function BattleItem1:OnClickBtnWuXingTip()
	ViewManager.Instance:Open(GuideModuleName.WuXingTip)
end

function BattleItem1:OnClick()
	if TianShenWGData.Instance:GetBattleDrugStatus() then
		return
	end

	if nil ~= self.call_back then
		self.call_back(self.index)
	end

	local has_list = TianShenWGData.Instance:GetFightTisnShen(false)
	if #has_list > 0 then
		ViewManager.Instance:Open(GuideModuleName.TianShenSelectView, nil, nil, {index = self.index - 1, data = self.data or false})
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.NoTianShenTips)
	end
end

function BattleItem1:LockOnClick()
	local zhan_sit = TianShenWGData.Instance:GetTianShenSitCfgByIndex(self.index)
	if zhan_sit == nil then
		return
	end

	if 0 ~= zhan_sit.item_id then
		TianShenWGCtrl.Instance:OpenTianShenFightAlertOpenTip(zhan_sit)
		--ViewManager.Instance:Open(GuideModuleName.TianShenFightAlert)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(self.node_list["text_level"].text.text)
	end
end

function BattleItem1:OnFlush()
	self.node_list["ts_name"]:SetActive(false)
	self:FlushStatus()
	if not self.is_open then
		self.node_list["img_lock"]:SetActive(true)
		self.node_list["img_add"]:SetActive(false)
		self.node_list.btn:SetActive(false)
		return
	end

	if self.data then
		self.node_list["ts_name"]:SetActive(true)
		local _, huamo_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(self.data.index)
		local normal_name = self.data.bianshen_name
		local rumo_name =  huamo_id and huamo_id ~= 0 and Language.TianShenHuaMo.DemonName[huamo_id] or nil
		if rumo_name then
			normal_name = string.format(rumo_name, normal_name)
		end
		self.node_list.ts_name_text.text.text = normal_name
		local shenshi_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.data.index)
		local bundle, asset = ResPath.GetCommon("a2_quality_text_" .. shenshi_data.jingshen + 1)
		local bundel1, asset1 = ResPath.GetCommon("a2_sl_di_" .. shenshi_data.jingshen + 1)
		self.node_list.active_jingshen_img.image:LoadSprite(bundle, asset, function()
			self.node_list.active_jingshen_img.image:SetNativeSize()
		end)
		self.node_list.Pz_Bg.image:LoadSprite(bundel1, asset1, function()
			self.node_list.Pz_Bg.image:SetNativeSize()
		end)
	end

	self.node_list["img_add"]:SetActive(not self.data)
	self.node_list.btn:SetActive(self.data ~= nil)
	self.node_list["ph_bianshen_display"]:SetActive(self.data ~= nil)
	if self.data then
		self:FlushModelDisPlay()
	else
		self.old_appe_image_id = nil
		self.role_model:RemoveMain()
	end

	-- local wuxing_index = 0
	-- if self.data and self.data.index then
	-- 	wuxing_index = TianShenWGData.Instance:GetTianShenWuXingByIndex(self.data.index)
	-- end

	-- if wuxing_index ~= 0 then
	-- 	local bundle,asset = ResPath.GetF2CommonImages("wuxing_small_" .. wuxing_index)
	-- 	self.node_list["battle_wuxing"].image:LoadSprite(bundle,asset)
	-- 	self.node_list["battle_wuxing"]:SetActive(true)
	-- else
	-- 	self.node_list["battle_wuxing"]:SetActive(false)
	-- end

end

--刷新模型展示
function BattleItem1:FlushModelDisPlay()
	local image_cfg = TianShenWGData.Instance:GetImageModelByAppeId(self.data.appe_image_id)
	if not image_cfg then
		return
	end
	
	---添加化魔展示
	local appe_image_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(self.data.index) or self.data.appe_image_id
	local shiqi_scale = self.data.shiqi_scale or 1
	if not appe_image_id then
		return
	end

	local is_force_refresh_shuangsheng = false
	local is_have_shuangsheng, app_image_id_data = TianShenWGData.Instance:CheckHaveShuangShengTianShen(self.data.index)

	if self.old_appe_image_id ~= appe_image_id then
		self.old_appe_image_id = appe_image_id
		self.role_model:SetTianShenModel(appe_image_id, self.data.index, true, nil, SceneObjAnimator.Rest, function ()
            if is_have_shuangsheng and app_image_id_data ~= nil then
                self.role_model:TryChangePartScale(SceneObjPart.Main, app_image_id_data.model_scale)
            end
        end, app_image_id_data and app_image_id_data.shiqi_scale or shiqi_scale)
		is_force_refresh_shuangsheng = true
	end

	if is_have_shuangsheng and app_image_id_data ~= nil then
		-- 这里加多一项改变大小，相同的资源未变化会导致不会回调
		self.role_model:TryChangePartScale(SceneObjPart.Main, app_image_id_data.model_scale)
		self.role_model:SetShuangShengTianShenModel(app_image_id_data.appe_image_id, not is_force_refresh_shuangsheng)
	else
		self.role_model:RemoveShuangShengTianShenUI(true)
		self.role_model:TryResetChangePartScale(SceneObjPart.Main)
		self.role_model:TryResetChangePartScale(SceneObjPart.Weapon)
	end
end

function BattleItem1:FlushStatus()
	local zhan_sit = TianShenWGData.Instance:GetTianShenSitCfgByIndex(self.index)
	if zhan_sit == nil then
		self.node_list["go_open_limit"]:SetActive(true)
		self.node_list["img_lock"]:SetActive(true)
		self.node_list["operate_red"]:SetActive(false)
		return
	end

	local tianshen_info = TianShenWGData.Instance:TianShenInfo()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	if zhan_sit.level_limit > main_role_vo.level then
		self.is_open = false
		self.node_list["text_level"].text.text = zhan_sit.level_limit .. Language.TianShen.OpenLimit1
	elseif zhan_sit.vip_limit > main_role_vo.vip_level then
		self.is_open = false
		self.node_list["text_level"].text.text = string.format(Language.TianShen.OpenLimit2, zhan_sit.vip_limit)
	elseif zhan_sit.zhuan_limit > role_zhuan then
		self.is_open = false
		self.node_list["text_level"].text.text = string.format(Language.TianShen.OpenLimit3, zhan_sit.zhuan_limit)
	elseif 0 ~= zhan_sit.item_id then
		self.is_open = 1 == tianshen_info.tianshen_chuzhan_slot_active_flags[zhan_sit.sit_index]
		if not self.is_open then
			self.node_list["text_level"].text.text = Language.TianShen.OpenLimit4
		end
	else
		self.is_open = true
	end

	self.node_list["go_open_limit"]:SetActive(not self.is_open)
	self.node_list["img_lock"]:SetActive(not self.is_open)
	local flag = TianShenWGData.Instance:SingleChuZhanRemind(zhan_sit)
	self.node_list["operate_red"]:SetActive(flag)
end
