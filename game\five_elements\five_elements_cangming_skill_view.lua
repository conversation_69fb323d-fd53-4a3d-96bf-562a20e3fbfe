FiveElementsCangMingSkillView = FiveElementsCangMingSkillView or BaseClass(SafeBaseView)

function FiveElementsCangMingSkillView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
                        {vertor2 = Vector2(0, -4), sizeDelta = Vector2(1100, 590)})
    self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "layout_cangming_skill")
end

function FiveElementsCangMingSkillView:ReleaseCallBack()
	if self.skill_grid_list then
		self.skill_grid_list:DeleteMe()
		self.skill_grid_list = nil
	end

	self.select_skill_index = nil
	self.select_skill_data = nil
end

function FiveElementsCangMingSkillView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.FiveElements.SkillTitle
	
	local show_data_list = FiveElementsWGData.Instance:GetWaistLightList()
	if nil == self.skill_grid_list then
        self.skill_grid_list = AsyncBaseGrid.New()
        self.skill_grid_list:SetStartZeroIndex(false)
        self.skill_grid_list:CreateCells({
            col = 3,
            cell_count = #show_data_list,
            list_view = self.node_list["skill_list_gird"],
            itemRender = CangMingSkillGridCell,
            assetBundle = "uis/view/five_elements_ui_prefab",
            assetName = "cangming_skill_item",
        })
        self.skill_grid_list:SetSelectCallBack(BindTool.Bind1(self.OnClickSkillCell, self))
    end

	XUI.AddClickEventListener(self.node_list.use_btn, BindTool.Bind(self.OnClickUseSkill, self))

	self.select_skill_index = nil
	self.select_skill_data = nil
end

function FiveElementsCangMingSkillView:OnFlush(param_t, index)
	local show_data_list = FiveElementsWGData.Instance:GetWaistLightList()
	self.skill_grid_list:SetDataList(show_data_list)

	if not self.select_skill_index then
		local select_cell_index = 1
		self.skill_grid_list:JumpToIndexAndSelect(select_cell_index)
	end
end

function FiveElementsCangMingSkillView:OnClickSkillCell(cell)
    if not cell or not cell.data  or self.select_skill_index == cell:GetIndex() then
        return
    end

	self.select_skill_index = cell:GetIndex()
	self.select_skill_data = cell.data
	self:FlushPanel()
end

function FiveElementsCangMingSkillView:FlushPanel()
	if not self.select_skill_data then
		return
	end

	local info = FiveElementsWGData.Instance:GetWaistLightSkillInfo(self.select_skill_data.skill_id)
	if IsEmptyTable(info) then
		return
	end

	local skill_cfg = SkillWGData.Instance:GetHaloSkillById(self.select_skill_data.skill_id, info.level)
	if skill_cfg then
		self.node_list.skill_name.text.text = skill_cfg.skill_name
		local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_cfg.skill_id, info.level)
		if clien_skill_cfg then
			local s_bundle, s_asset = ResPath.GetSkillIconById(clien_skill_cfg.icon_resource)
			self.node_list.skill_icon.image:LoadSprite(s_bundle, s_asset)
			self.node_list.skill_desc.text.text = clien_skill_cfg.description
		end

		local str_type = info.is_use and 2 or 1
		self.node_list.btn_text.text.text = Language.FiveElements.SkillBtnStr[str_type]
	end
end

function FiveElementsCangMingSkillView:OnClickUseSkill()
	if not self.select_skill_data then
		return
	end

	local info = FiveElementsWGData.Instance:GetWaistLightSkillInfo(self.select_skill_data.skill_id)
	if IsEmptyTable(info) then
		return
	end
	
	if info.is_use then
		TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElements.SkillBtnStr[2])
	else
		if info.is_open then
			FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.SET_SKILL, self.select_skill_data.type)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElements.NotActSkill)
		end
	end
end
------------
CangMingSkillGridCell = CangMingSkillGridCell or BaseClass(BaseRender)

function CangMingSkillGridCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.item_node, BindTool.Bind(self.OnClick, self))
end

function CangMingSkillGridCell:__delete()

end

function CangMingSkillGridCell:OnFlush()
	if nil == self.data then
		return
	end

	local info = FiveElementsWGData.Instance:GetWaistLightSkillInfo(self.data.skill_id)
	if not IsEmptyTable(info) then
		local skill_cfg = SkillWGData.Instance:GetHaloSkillById(self.data.skill_id, info.level)
		if skill_cfg then
			self.node_list.name.text.text = skill_cfg.skill_name
			local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_cfg.skill_id, info.level)
			if clien_skill_cfg then
				local s_bundle, s_asset = ResPath.GetSkillIconById(clien_skill_cfg.icon_resource)
				self.node_list.icon.image:LoadSprite(s_bundle, s_asset)
			end
		end

		self.node_list.use_img:SetActive(info.is_use)
		XUI.SetGraphicGrey(self.node_list["icon"], not info.is_open)
	end	
end

function CangMingSkillGridCell:OnSelectChange(is_select)
	if is_select == nil or self.data == nil then
        return
    end

	self.node_list.highlight:SetActive(is_select)
end

-- 点击回调
function CangMingSkillGridCell:OnClick()
    if self.data == nil then
        return
    end

	if nil ~= self.click_callback then
		self.click_callback(self)
	end
end