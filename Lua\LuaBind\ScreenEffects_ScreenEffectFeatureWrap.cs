﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ScreenEffects_ScreenEffectFeatureWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ScreenEffects.ScreenEffectFeature), typeof(UnityEngine.MonoBehaviour));
		L<PERSON>RegFunction(".geti", get_Item);
		<PERSON><PERSON>RegFunction("get_Item", get_Item);
		<PERSON><PERSON>unction("DoEffect", DoEffect);
		<PERSON><PERSON>ar("this", _this, null);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("layerMask", get_layerMask, set_layerMask);
		<PERSON><PERSON>("effects", get_effects, set_effects);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _get_this(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<int>(L, 2))
			{
				ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)ToLua.CheckObject<ScreenEffects.ScreenEffectFeature>(L, 1);
				int arg0 = (int)LuaDLL.lua_tonumber(L, 2);
				ScreenEffects.ScreenEffect o = obj[arg0];
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string>(L, 2))
			{
				ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)ToLua.CheckObject<ScreenEffects.ScreenEffectFeature>(L, 1);
				string arg0 = ToLua.ToString(L, 2);
				ScreenEffects.ScreenEffect o = obj[arg0];
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to operator method: ScreenEffects.ScreenEffectFeature.this");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _this(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushvalue(L, 1);
			LuaDLL.tolua_bindthis(L, _get_this, null);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Item(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<int>(L, 2))
			{
				ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)ToLua.CheckObject<ScreenEffects.ScreenEffectFeature>(L, 1);
				int arg0 = (int)LuaDLL.lua_tonumber(L, 2);
				ScreenEffects.ScreenEffect o = obj[arg0];
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string>(L, 2))
			{
				ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)ToLua.CheckObject<ScreenEffects.ScreenEffectFeature>(L, 1);
				string arg0 = ToLua.ToString(L, 2);
				ScreenEffects.ScreenEffect o = obj[arg0];
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: ScreenEffects.ScreenEffectFeature.get_Item");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoEffect(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4 && TypeChecker.CheckTypes<string, float, float>(L, 2))
			{
				ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)ToLua.CheckObject<ScreenEffects.ScreenEffectFeature>(L, 1);
				string arg0 = ToLua.ToString(L, 2);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 3);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 4);
				ScreenEffects.ScreenEffect o = obj.DoEffect(arg0, arg1, arg2);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<ScreenEffects.ScreenEffect, float, float>(L, 2))
			{
				ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)ToLua.CheckObject<ScreenEffects.ScreenEffectFeature>(L, 1);
				ScreenEffects.ScreenEffect arg0 = (ScreenEffects.ScreenEffect)ToLua.ToObject(L, 2);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 3);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 4);
				obj.DoEffect(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: ScreenEffects.ScreenEffectFeature.DoEffect");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_layerMask(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)o;
			UnityEngine.LayerMask ret = obj.layerMask;
			ToLua.PushLayerMask(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index layerMask on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_effects(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)o;
			System.Collections.Generic.List<ScreenEffects.ScreenEffect> ret = obj.effects;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index effects on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_layerMask(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)o;
			UnityEngine.LayerMask arg0 = ToLua.ToLayerMask(L, 2);
			obj.layerMask = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index layerMask on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_effects(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ScreenEffects.ScreenEffectFeature obj = (ScreenEffects.ScreenEffectFeature)o;
			System.Collections.Generic.List<ScreenEffects.ScreenEffect> arg0 = (System.Collections.Generic.List<ScreenEffects.ScreenEffect>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<ScreenEffects.ScreenEffect>));
			obj.effects = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index effects on a nil value");
		}
	}
}

