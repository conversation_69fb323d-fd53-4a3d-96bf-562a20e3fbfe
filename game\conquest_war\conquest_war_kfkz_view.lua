-- 跨服空战 
function ConquestWarView:LoadIndexCallBackKFKZView()
	self:FlushFGBEnterBtnState()

	if not self.kfkz_reward_list then
		self.kfkz_reward_list = AsyncListView.New(ItemCell, self.node_list.kfkz_reward_list)
		self.kfkz_reward_list:SetStartZeroIndex(true)
	end

	local data = ConquestWarWGData.Instance:GetConquestWarActivityInfoById(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_AIR_WAR)
	if not IsEmptyTable(data) then
		self.kfkz_reward_list:SetDataList(data.activity_item)

		self.node_list["txt_kfkz_title"].text.text = data.activity_title
		self.node_list["txt_kfkz_open_time"].text.text = string.format(Language.FlagGrabbingBattlefield.ActivityTime1, data.time_1, data.time_2)
		self.node_list["txt_kfkz_desc"].text.text = data.activity_illustrate
	end
	
	XUI.AddClickEventListener(self.node_list.go_kfkz_btn, BindTool.Bind1(self.OnClickGoToKFKZ, self))
	XUI.AddClickEventListener(self.node_list.btn_kfkz_reward_show, BindTool.Bind(self.OnClickKFKZRewardShow, self))

	self.kfkz_activity_change_callback = BindTool.Bind(self.OnKFKZActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.kfkz_activity_change_callback)
end

function ConquestWarView:ReleaseKFKZView()
	if self.kfkz_reward_list then
		self.kfkz_reward_list:DeleteMe()
		self.kfkz_reward_list = nil
	end

	if self.kfkz_activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.kfkz_activity_change_callback)
		self.kfkz_activity_change_callback = nil
	end
end

function ConquestWarView:KFKZShowIndexCallBack()

end

function ConquestWarView:OnClickKFKZRewardShow()
	CrossAirWarWGCtrl.Instance:OpenRewardPreview()
end

function ConquestWarView:OnFlushKFKZView(param_t, index)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_AIR_WAR)
	if not activity_info then return end

	self:FlushKFKZBtnState(activity_info.status)
end

function ConquestWarView:OnClickGoToKFKZ()
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_AIR_WAR)
    local act_status = CrossAirWarWGData.Instance:GetAirWarActivityStatus()

	if act_info and act_info.status ~= ACTIVITY_STATUS.OPEN then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
	else
		if act_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_END 
			and act_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_GATHER 
			and act_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_AUCTION then
        	CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_AIR_WAR)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FlagGrabbingBattlefield.TianGongEnd)
		end
	end
end

function ConquestWarView:OnKFKZActChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_AIR_WAR and self:IsOpen() then
		self:FlushKFKZBtnState(status)
	end
end

function ConquestWarView:FlushKFKZBtnState(status)
    local act_status = CrossAirWarWGData.Instance:GetAirWarActivityStatus()

	if status == ACTIVITY_STATUS.STANDY then
		self.node_list["go_kfkz_btn"]:CustomSetActive(true)
		self.node_list["go_kfkz_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Standy
	elseif status == ACTIVITY_STATUS.OPEN and act_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_END
	and act_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_GATHER and act_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_AUCTION then
		self.node_list["go_kfkz_btn"]:CustomSetActive(true)
		self.node_list["go_kfkz_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Doing
	elseif status == ACTIVITY_STATUS.OPEN and (act_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_END
	or act_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_GATHER or act_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_AUCTION) then
		self.node_list["go_kfkz_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.TianGongEnd
	else
		self.node_list["go_kfkz_btn"]:CustomSetActive(false)
		self.node_list["go_kfkz_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.NotOpenDesc
	end
end
