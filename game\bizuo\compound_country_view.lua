--合国进度界面
----------------------------------
CompoundCountryView = CompoundCountryView or BaseClass(SafeBaseView)

function CompoundCountryView:__init()
	self:LoadConfig()
end

function CompoundCountryView:LoadConfig()
	self:SetMaskBg(false)
	self.view_layer = UiLayer.Pop
	self.view_name = "CompoundCountryView"
	self:AddViewResource(0, "uis/view/bizuo_ui_prefab", "layout_compound_country")
end

function CompoundCountryView:ReleaseCallBack()
end

function CompoundCountryView:LoadCallBack()
end

function CompoundCountryView:ShowIndexCallBack()
end

function CompoundCountryView:OnFlush()
end


----------------------------------------------------------------------------------
CompoundCountryItemRander = CompoundCountryItemRander or BaseClass(BaseRender)
function CompoundCountryItemRander:__init()
end

function CompoundCountryItemRander:__delete()

end

function CompoundCountryItemRander:LoadCallBack()
end

function CompoundCountryItemRander:OnFlush()
end

----------------------------------------------------------------------------------
CompoundCountryServerItem = CompoundCountryServerItem or BaseClass(BaseRender)

function CompoundCountryServerItem:__init()
end

function CompoundCountryServerItem:__delete()
end

function CompoundCountryServerItem:OnFlush()
end