﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public partial class YYLightManager
{
    public const int MaximumLightCount = 16;
    private readonly Vector4[] lightViewportPositionsDir = new Vector4[MaximumLightCount];
    private readonly Vector4[] lightColorsDir = new Vector4[MaximumLightCount];
    private readonly Vector4[] lightVar1Dir = new Vector4[MaximumLightCount];

    //
    private readonly Vector4[] lightPositionsDir = new Vector4[MaximumLightCount];

    private readonly Vector4[] lightPowerDir = new Vector4[MaximumLightCount];


    //
    private void SetGlobalShaders()
    {
        float t = Time.timeSinceLevelLoad;
        Vector4 time = new Vector4(t * 0.05f, t, (float)System.Math.Truncate(t * 0.05f), (float)System.Math.Truncate(t));
        Shader.SetGlobalVector(WMS._WeatherMakerTime, time);
    }
   //

    private void SetLightsByTypeToShader(Camera camera, YYShaderProperties m)
    {
        int dirLightCount = 0;

        lightColorsDir[0] = Vector4.zero;
        lightVar1Dir[0] = Vector4.zero;

        if (Application.isPlaying && lights.Count <= 0)
        {
            Debug.Log("没有灯光，请检查");
        }

        for (int i = 0; i < lights.Count; i++)
        {
            LightState light = lights[i];
            if (light == null || light.Light == null)
            {
                lights.RemoveAt(i--);
                continue;
            }
            switch (light.Light.type)
            {
                case LightType.Directional:
                    if (ProcessLightProperties(light, camera,
                            ref lightPositionsDir[dirLightCount],
                            ref lightColorsDir[dirLightCount],
                            ref lightViewportPositionsDir[dirLightCount],
                            ref lightPowerDir[dirLightCount],
                            ref lightVar1Dir[dirLightCount]))
                    {
                        dirLightCount++;
                    }
                    break;

                default:
                    break;
            }
        }

        //dir lights
        m.SetVectorArray(WMS._WeatherMakerDirLightColor, lightColorsDir);
        m.SetVectorArray(WMS._WeatherMakerDirLightViewportPosition, lightViewportPositionsDir);
        m.SetVectorArray(WMS._WeatherMakerDirLightVar1, lightVar1Dir);

        //
        m.SetVectorArray(WMS._WeatherMakerDirLightPosition, lightPositionsDir);
        m.SetVectorArray(WMS._WeatherMakerDirLightPower, lightPowerDir);
    }

    private bool ProcessLightProperties
       (
           LightState lightState,
           Camera camera,
           ref Vector4 pos,
           ref Vector4 color,
           ref Vector4 viewportPos,
           ref Vector4 lightPower,
           ref Vector4 lightVar1
        )
    {
        if (lightState == null)
        {
            return false;
        }
        Light light = lightState.Light;
        if (light == null || !light.enabled )//|| light.color.a <= 0.001f || light.intensity <= 0.001f || light.range <= 0.001f ||
            //!IntersectLight(light))
        {
            return false;
        }
        lightVar1.x = lightState.Update(camera);
        SetShaderViewportPosition(light, camera, ref viewportPos, ref lightVar1);
        color = new Vector4(light.color.r, light.color.g, light.color.b, light.intensity);
        //Debug.Log("color is " + color.x);
        lightPower = Vector4.zero;


        // LightType.Directional
        pos = -light.transform.forward;
        pos.w = -1.0f;
        YYCelestialObject obj = light.GetComponent<YYCelestialObject>();
        float isSun;
        if (obj == null)
        {
            isSun = 1.0f;
        }
        else
        {
            if (obj.OrbitTypeIsPerspective != (camera == null || !camera.orthographic))
            {
                return false;
            }
            isSun = (obj.IsSun ? 1.0f : 0.0f);
            lightVar1.z = obj.ShaftMultiplier;
            float intensity = light.intensity;
            lightVar1.w = Mathf.Clamp(intensity * Mathf.Pow(1.0f - Mathf.Abs(pos.y), obj.HorizonMultiplier.z) * obj.HorizonMultiplier.x, intensity, intensity + obj.HorizonMultiplier.y);
        }

        // none shadows
        if (obj == null)
        {
            lightPower = new Vector4(1.0f, 1.0f, light.shadowStrength, 1.0f);
        }
        else
        {
            lightPower = new Vector4(obj.LightPower, obj.LightMultiplier, light.shadowStrength, 1.0f);
        }





        return true;
    }

    private void SetShaderViewportPosition(Light light, Camera camera, ref Vector4 viewportPosition, ref Vector4 lightVar1)
    {
        if (camera == null)// || YYWeather.GetCameraType(camera) != WeatherMakerCameraType.Normal)
        {
            return;
        }

        viewportPosition = camera.WorldToViewportPoint(light.transform.position);

        // as dir light leaves viewport, fade out
        Vector2 viewportCenter = new Vector2((camera.rect.min.x + camera.rect.max.x) * 0.5f, (camera.rect.min.y + camera.rect.max.y) * 0.5f);
        float distanceFromCenterViewport = ((Vector2)viewportPosition - viewportCenter).magnitude * 0.5f;
        viewportPosition.w = light.intensity * Mathf.SmoothStep(1.0f, 0.0f, distanceFromCenterViewport);

        foreach (YYCelestialObject sun in Suns)
        {
            if (sun != null && sun.Light == light)
            {
                sun.ViewportPosition = viewportPosition;
                lightVar1.y = (sun.OrbitType == WeatherOrbitType.FromEarth || sun.OrbitType == WeatherOrbitType.CustomPerspective ? 0.0f : 1.0f);
                return;
            }
        }

        //foreach (YYCelestialObject moon in Moons)
        //{
        //    if (moon != null && moon.Light == light)
        //    {
        //        moon.ViewportPosition = viewportPosition;
        //        lightVar1.y = (moon.OrbitType == WeatherMakerOrbitType.FromEarth || moon.OrbitType == WeatherMakerOrbitType.CustomPerspective ? 0.0f : 1.0f);
        //        break;
        //    }
        //}

    }
}
