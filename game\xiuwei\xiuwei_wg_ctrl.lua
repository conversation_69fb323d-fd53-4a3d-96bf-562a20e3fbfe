require("game/xiuwei/xiuwei_view")
require("game/xiuwei/xiuwei_wg_data")
require("game/xiuwei/xiuwei_preview_view")

XiuWeiWGCtrl = XiuWeiWGCtrl or BaseClass(BaseWGCtrl)

function XiuWeiWGCtrl:__init()
	if XiuWeiWGCtrl.Instance then
		print_error("[XiuWeiWGCtrl]:Attempt to create singleton twice!")
	end

	XiuWeiWGCtrl.Instance = self
    self.data = XiuWeiWGData.New()
    self.view = XiuWeiView.New(GuideModuleName.XiuWeiView)
	-- self.preview_view = XiuWeiPreviewView.New(GuideModuleName.XiuWeiPreviewView)

    self:RegisterAllProtocols()
end

function XiuWeiWGCtrl:__delete()
    if self.data then
        self.data:DeleteMe()
	    self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
	    self.view = nil
    end

	-- if self.preview_view then
    --     self.preview_view:DeleteMe()
	--     self.preview_view = nil
    -- end

    XiuWeiWGCtrl.Instance = nil
end

--------------------------protocol_start---------------------------
function XiuWeiWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRoleXiuWeiTaskInfo, "OnSCRoleXiuWeiTaskInfo")
	self:RegisterProtocol(SCRoleXiuWeiTaskUpdate, "OnSCRoleXiuWeiTaskUpdate")
end

function XiuWeiWGCtrl:OnSCRoleXiuWeiTaskInfo(protocol)
	self.data:SetAllTaskData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView)
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
	RemindManager.Instance:Fire(RemindName.XiuWei)
end

function XiuWeiWGCtrl:OnSCRoleXiuWeiTaskUpdate(protocol)
	self.data:SetSingleTaskData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView)
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
	RemindManager.Instance:Fire(RemindName.XiuWei)
end
----------------------------protocol_end---------------------------

function XiuWeiWGCtrl:ShowLevelNormalGuideView(root_id)
	ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView, nil, "show_level_guide", {root_id = root_id})
end