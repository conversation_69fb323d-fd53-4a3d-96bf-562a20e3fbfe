HiddenWeaponRequest = HiddenWeaponRequest or {}

-- enum EQUIP_OPERA_TYPE
--     {
--         EQUIP_OPERA_TYPE_GRID_INFO = 1,                 -- 请求装备格子信息返回
--         EQUIP_OPERA_TYPE_BAG_INFO = 2,                      -- 请求装备背包返回
--         EQUIP_OPERA_TYPE_UPLEVEL = 3,                       -- 升级 param1 = (1:暗器, 2:软甲), param2 = 将要使用的经验
--         EQUIP_OPERA_TYPE_UPGRADE = 4,                       -- 进阶 param1 = (1:暗器, 2:软甲)
--         EQUIP_OPERA_TYPE_RAND_ATTR = 5,                     -- 刻铭 param1 = (1:暗器, 2:软甲), param2 = (0:刻铭, 1:放弃, 2:保存)
--         EQUIP_OPERA_TYPE_RAND_ATTR_BREAK = 6,               -- 突破刻铭上限 param1 = (1:暗器, 2:软甲)
--         EQUIP_OPERA_TYPE_SPECIAL_EFFECT_UPLEVEL = 7,        -- 专属效果觉醒 param1 = (1:暗器, 2:软甲)
--         EQUIP_OPERA_TYPE_UP_BAG_COLOR_STAR = 8,             -- 背包升品升星 param1 = 主材料背包索引, param2 = 参数个数, param_list = 子材料背包索引列表
--         EQUIP_OPERA_TYPE_UP_GRID_COLOR_STAR = 9,            -- 装备栏升品升星 param1 = (1:暗器, 2:软甲), param2 = 参数个数, param_list = 子材料背包索引列表
--         EQUIP_OPERA_TYPE_PUT_ON = 10,                        -- 穿戴装备 param1 = 背包索引
--         EQUIP_OPERA_TYPE_DECOMPOSE = 11,                     -- 一键分解
--     };


function HiddenWeaponRequest:getParamIndexByWeaponType(weapon_type)
    return weapon_type
end

-- 请求装备格子信息返回
function HiddenWeaponRequest:ReqGridInfo()
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiEquipOpera)
    send_protocol.type = 1
    send_protocol:EncodeAndSend()
end

-- 请求装备背包返回
function HiddenWeaponRequest:ReqBagList()
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiEquipOpera)
    send_protocol.type = 2
    send_protocol:EncodeAndSend()
end

-- param1 为暗器软甲类型
function HiddenWeaponRequest:ReqCommonWithWeaponType(proto_type, weapon_type, param2)
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiEquipOpera)
    send_protocol.type = proto_type
    send_protocol.param1 = self:getParamIndexByWeaponType(weapon_type)
    send_protocol.param2 = param2 or 0
    send_protocol:EncodeAndSend()
end

-- 升级
function HiddenWeaponRequest:ReqUpgrade(weapon_type, param2)

    self:ReqCommonWithWeaponType(3, weapon_type, param2)
end

-- 进阶
function HiddenWeaponRequest:ReqUpgrade2(weapon_type)
    self:ReqCommonWithWeaponType(4, weapon_type)
end

-- 刻铭
-- param2 = (0:刻铭, 1:放弃, 2:保存)
function HiddenWeaponRequest:ReqRand(weapon_type, action_type)
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiEquipOpera)
    send_protocol.type = 5
    send_protocol.param1 = self:getParamIndexByWeaponType(weapon_type)
    send_protocol.param2 = action_type
    send_protocol:EncodeAndSend()
end

-- 突破上限
function HiddenWeaponRequest:ReqRandLimit(weapon_type)
    self:ReqCommonWithWeaponType(6, weapon_type)
end

-- 觉醒
function HiddenWeaponRequest:ReqAwake(weapon_type)
    self:ReqCommonWithWeaponType(7, weapon_type)
end

-- 背包升品升星
function HiddenWeaponRequest:ReqCompose1(main_index, material_indexs)
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiEquipOpera)
    send_protocol.type = 8
    send_protocol.param1 = main_index
    send_protocol.param2 = #material_indexs
    send_protocol.param_list = material_indexs
    send_protocol:EncodeAndSend()
end

-- 装备栏升品升星
function HiddenWeaponRequest:ReqCurrEquipCompose(weapon_type, material_indexs)
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiEquipOpera)
    send_protocol.type = 9
    send_protocol.param1 = weapon_type
    send_protocol.param2 = #material_indexs
    send_protocol.param_list = material_indexs
    send_protocol:EncodeAndSend()
end

-- 穿装备
function HiddenWeaponRequest:ReqEquip(index)
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiEquipOpera)
    send_protocol.type = 10
    send_protocol.param1 = index
    send_protocol:EncodeAndSend()
end

-- 分解
function HiddenWeaponRequest:ReqDecompose()
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiEquipOpera)
    send_protocol.type = 11
    send_protocol:EncodeAndSend()
end

-- 请求装备背包返回
function HiddenWeaponRequest:ReqRLQuickUpgrade(upgrade_list)
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiQuickUpgrade)
    send_protocol.count = #upgrade_list
    send_protocol.upgrade_list = upgrade_list
    send_protocol:EncodeAndSend()
end