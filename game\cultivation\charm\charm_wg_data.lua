CultivationWGData = CultivationWGData or BaseClass()

CultivationWGData.CHARM_SUIT_TYPE = {
	YANG = 1,
	YING = 2,
}

CultivationWGData.CHARM_EQUIP_TYPE = {
	YING = 0,
	YANG = 1,
	SPECIAL = 2,
}

function CultivationWGData:InitCharmCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("charm_cfg_auto")
	self.solt_cfg = ListToMap(cfg.solt, "solt")
	self.equip_cfg = ListToMap(cfg.equip, "item_id")
	self.compose_menu_cfg = ListToMap(cfg.compose_menu, "big_type", "small_type")
	self.compos_cfg = ListToMap(cfg.compos, "big_type", "small_type")
	-- self.suit_cfg = ListToMap(cfg.suit, "type", "need_color", "need_order", "need_num")
	self.suit_attr_cfg = ListToMap(cfg.suit, "type", "need_num", "need_order", "need_color")
	self.pan_level_cfg = ListToMap(cfg.pan_level, "level")
	self.bead_level_cfg = ListToMap(cfg.bead_level, "seq", "level")
	self.equip_show_cfg = ListToMapList(cfg.equip, "order")
	self.equip_show_stage_cfg = ListToMap(cfg.equip, "need_xiuwei_stage")
	
	self.bag_grid_count = 0
	self.bag_grid_list = {}
	self.charm_solt_info = {}
	self.bag_one_key_cache = {}
	self.charm_compose_remind = false
	self.charm_compose_cache = {}
	self.charm_compose_equiped_cache = {}
	self.charm_compose_stuff_cache = {}
	self.charm_compose_cache_remind = {}
	self.charm_suit_cache = {}
	self.slot_count_cache = {}
	self.suit_atr_info = {}

	self.suit_cap_attr_cache = {}
	self.charm_solt_cap_attr_cache = {}
	self.charm_one_key_wear_datalist = {}

	self.yinyang_level = 0
    self.yinyang_exp = 0
	self.yinyang_bead_level_list = {}
	self.charm_longzhu_attr_cache = {}
	self.charm_longzhu_addition_cache = {}
	self.solt_level_color = {}

	RemindManager.Instance:Register(RemindName.Charm_Holy_Seal, BindTool.Bind(self.GetHolySealRemind, self))
	RemindManager.Instance:Register(RemindName.Charm_LingZhu, BindTool.Bind(self.GetLingZhuRemind, self))
end

function CultivationWGData:DeleteCharmData()
    RemindManager.Instance:UnRegister(RemindName.Charm_Holy_Seal)
	RemindManager.Instance:UnRegister(RemindName.Charm_LingZhu)
end

----------------------------------------remind_start-------------------------------------------
function CultivationWGData:GetHolySealRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.CharmHolySeal) then
		return 0
	end

	if self:GetCharmOneKeyWearRemind() then
		return 1
	end

	if self:GetCharmComposeRemind() then
		return 1
	end

	if self:GetLingZhuRemind() > 0 then
		return 1
	end

	return 0
end

function CultivationWGData:GetLingZhuRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.CharmLingZhu) then
		return 0
	end
	
    if not self:IsCharmYingYangMaxLevel() then
		local longzhu_level = self:GetCharmYingYangLevel()
        local current_level_cfg = self:GetCharmYingYangLevelCfg(longzhu_level)
		
        if self:GetCharmYingYangExp() >= current_level_cfg.need_exp then
			return 1
        end
    end

	return 0
end

function CultivationWGData:GetCharmOneKeyWearRemind()
	return self:GetCharmOneKeyWearDataList() > 0
end

function  CultivationWGData:GetCharmComposeRemind()
	return self.charm_compose_remind
end
-----------------------------------------remind_end--------------------------------------------

----------------------------------------common_get_start---------------------------------------
function CultivationWGData:IsCharmEquip(change_item_id)
	return nil ~= self.equip_cfg[change_item_id]
end
-----------------------------------------common_get_end----------------------------------------

-----------------------------------------protocol_start----------------------------------------
function CultivationWGData:SetCharmSoltInfo(protocol)
	self.charm_solt_info = protocol.solt_item_list
	self:CalculationCharmEquipedEquip()
	self:CalculationCharmSuitCache()
	self:CalculationCharmSuitCapAndAttr()
	self:CalculationCharmGeneralAttrDataList()
	self:CalculationCharmOneKeyWearDataList()
	self.solt_level_color = protocol.solt_level_color
end

function CultivationWGData:CharmSoltUpdate(protocol)
	self.charm_solt_info[protocol.solt] = protocol.solt_item
	self:CalculationCharmEquipedEquip()
	self:CalculationCharmSuitCache()
	self:CalculationCharmSuitCapAndAttr()
	self:CalculationCharmGeneralAttrDataList()
	self:CalculationCharmOneKeyWearDataList()

	if not self.solt_level_color[protocol.solt] then
		self.solt_level_color[protocol.solt] = {}
	end

	self.solt_level_color[protocol.solt]["max_level"] = protocol.max_level
	self.solt_level_color[protocol.solt]["max_color"] = protocol.max_color
end

function CultivationWGData:SetCharmBagInfo(protocol)
	self.bag_grid_count = protocol.grid_count
	self.bag_grid_list = protocol.grid_list
	self:CalculationCharmBagCache(self.bag_grid_list)
	self:CalculationCharmOneKeyWearDataList()
	self:CalculationCharmComposeRemind()
	-- print_error("背包信息 count = ",self.bag_grid_count, " 数据 =  " ,self.bag_grid_list)
end

function CultivationWGData:UpdateCharmBagInfo(protocol)
	local new_data = protocol.grid_info
	local old_data = self.bag_grid_list[new_data.index]
	local no_old_data = IsEmptyTable(old_data)
	local is_add = false
	local add_num = 0

	-- 添加  修改 删除
	if (no_old_data and new_data.item_id > 0 and new_data.num > 0) or (not no_old_data and old_data.item_id <= 0 and new_data.item_id > 0 and new_data.num > 0) then
		-- print_error("添加------------  " ,protocol.grid_info)
		self.bag_grid_list[new_data.index] = new_data
		is_add = true
		add_num = new_data.num
	elseif not no_old_data and old_data.item_id > 0 and old_data.num > 0 and new_data.item_id > 0 and new_data.num > 0  then
		-- print_error("修改------------  " ,protocol.grid_info)
		self.bag_grid_list[new_data.index] = new_data
		is_add = new_data.num > old_data.num
		add_num = new_data.num - old_data.num
	else
		self.bag_grid_list[new_data.index] = nil
	end

	self:CalculationCharmBagCache(self.bag_grid_list)
	self:CalculationCharmComposeRemind()
	self:CalculationCharmOneKeyWearDataList()

	return is_add, add_num
end

function CultivationWGData:GetCharmBagDataList()
	return self.bag_grid_list
end

function CultivationWGData:GetBagDataList()
	local bag_grid_list = self.bag_grid_list
	local bag_list = {}
	if #bag_grid_list < 11 then
		for i = 0, 10 do
			if bag_grid_list[i] then
				table.insert(bag_list, bag_grid_list[i])
			else
				table.insert(bag_list, {item_id = 0})
			end
		end
	else
		return bag_grid_list
	end

	return bag_list
end

function CultivationWGData:GetCharmBagDataByIndex(index)
	return self.bag_grid_list[index]
end

function CultivationWGData:GetCharmBagSortDataList()
	return SortDataByItemColor(self.bag_grid_list)
end

function CultivationWGData:GetCharmSoltDataBySolt(solt)
	return self.charm_solt_info[solt]
end


function CultivationWGData:GetCharmSoltInfo()
	local charm_solt_info = {}
	local special_sign, yang_sign, ying_sign = 0, 1, 6

	for k, v in pairs(self:GetCharmHolySealSoltCfg()) do
		local solt_info = self:GetCharmSoltDataBySolt(v.solt)

		if v.type == CultivationWGData.CHARM_EQUIP_TYPE.SPECIAL then
			charm_solt_info[special_sign] = solt_info
		elseif v.type == CultivationWGData.CHARM_EQUIP_TYPE.YANG then
			charm_solt_info[yang_sign] = solt_info
			yang_sign = yang_sign + 1
		else
			charm_solt_info[ying_sign] = solt_info
			ying_sign = ying_sign + 1
		end
	end

	return charm_solt_info
end

function CultivationWGData:SetCharmYingYangInfo(protocol)
	self.yinyang_level = protocol.yinyang_level
    self.yinyang_exp = protocol.yinyang_exp
    self.yinyang_bead_level_list = protocol.yinyang_bead_level_list

	-- self:CalculationCharmComposeRemind()
	self:CalculationCharmLongZhuAttrDataList()
end

function CultivationWGData:GetCharmYingYangLevel()
	return self.yinyang_level
end

function CultivationWGData:GetCharmYingYangExp()
	return self.yinyang_exp
end

function CultivationWGData:GetCharmYingYangBeadLevel(index)
	return self.yinyang_bead_level_list[index]
end

function CultivationWGData:GetCharmYingYangBeadChangeIndex(yinyang_bead_level_list)
	local index = -1
	
	if IsEmptyTable(self.yinyang_bead_level_list) then
		return index
	end

	for i = 0, 7 do
		if self:GetCharmYingYangBeadLevel(i) < yinyang_bead_level_list[i] then
			index = i
			break
		end 
	end

	return index
end
------------------------------------------protocol_end-----------------------------------------

--------------------------------------------cfg_start------------------------------------------
function CultivationWGData:GetCharmEquipByItemId(item_id)
	return self.equip_cfg[item_id]
end

function CultivationWGData:GetCharmEquipShowCfg()
	return self.equip_show_cfg
end

function CultivationWGData:GetCharmEquipShowStageCfg()
	return self.equip_show_stage_cfg
end

function CultivationWGData:GetCharmComposeBigItemListInfo()
	return self.compose_menu_cfg
end

function CultivationWGData:GetComposeSmallItemListInfo(index)
	return self.compose_menu_cfg[index]
end

function CultivationWGData:GetCharmHolySealSoltCfg()
	return self.solt_cfg
end

function CultivationWGData:GetCharmHolySealSoltCfgBySolt(solt)
	return self.solt_cfg[solt]
end

function CultivationWGData:GetCharmComposeItemCfg(big_type, small_type)
	return ((self.compos_cfg[big_type] or {})[small_type] or {})
end

function CultivationWGData:GetCharmYingYangLevelCfg(level)
	return self.pan_level_cfg[level]
end

function CultivationWGData:GetCurrentCharmYingYangLevelCfg()
	return self.pan_level_cfg[self.yinyang_level]
end

function CultivationWGData:GetBeadLevelCfg(seq, level)
	return 	(self.bead_level_cfg[seq] or {})[level] or {}
end

function CultivationWGData:IsBeadMaxLevel(seq)
	return IsEmptyTable(self:GetBeadLevelCfg(seq, self:GetCharmYingYangBeadLevel(seq) + 1))
end

-- 根据境界获取槽位开启数量
function CultivationWGData:GetSlotCountByStage(stage)
	if self.slot_count_cache[1] and self.slot_count_cache[1] == stage then
		return self.slot_count_cache[2]
	end
	local count = 0
	for key, value in pairs(self.solt_cfg) do
		if stage >= value.stage then
			count = count + 1
		end
	end
	self.slot_count_cache[1] = stage
	self.slot_count_cache[2] = count
	return count
end
---------------------------------------------cfg_End-------------------------------------------

--------------------------------------------cal_start------------------------------------------
-- 规则 先看阶数 阶数大可直接换 阶数一样 品质更高 可替换
function CultivationWGData:CanHolySealItemUp(item_id)
	local cfg = self:GetCharmEquipByItemId(item_id)
	
	if IsEmptyTable(cfg) then
		return false
	else
		local current_data = self:GetCharmSoltDataBySolt(cfg.solt)

		if IsEmptyTable(current_data) then
			return false
		end

		-- 孔位 修为等级判断
		local need_stage = self.solt_cfg[cfg.solt].stage
		if self:GetXiuWeiState() < need_stage then
			return false
		end

		-- 装备 修为等级判断
		local stage_level = CultivationWGData.Instance:GetXiuWeiState()
		if stage_level < cfg.need_xiuwei_stage then
			return false
		end

		if current_data.item_id <= 0 then
			return true
		end

		local current_data_cfg = self:GetCharmEquipByItemId(current_data.item_id)
		local _, item_color = ItemWGData.Instance:GetItemColor(item_id)
		local _, solt_color = ItemWGData.Instance:GetItemColor(current_data.item_id)
		return (cfg.order > current_data_cfg.order) or ((cfg.order == current_data_cfg.order) and (item_color > solt_color))
	end
end

-- 装备缓存
function CultivationWGData:CalculationCharmBagCache(bag_grid_list)
	if IsEmptyTable(bag_grid_list) then
		self.bag_one_key_cache = {}
		self.charm_compose_cache = {}
		return
	end

	local bag_one_key_cache = {}
	local charm_compose_cache = {}

	for k, v in pairs(bag_grid_list) do
		local equip_cfg = self:GetCharmEquipByItemId(v.item_id)
		if not IsEmptyTable(equip_cfg) then
			local _,equip_color = ItemWGData.Instance:GetItemColor(v.item_id)

			-- 一键穿戴
			bag_one_key_cache[equip_cfg.solt] = bag_one_key_cache[equip_cfg.solt] or {}
			table.insert(bag_one_key_cache[equip_cfg.solt], {order = equip_cfg.order, color = equip_color, index = v.index, item_id = v.item_id})

			--合成
			charm_compose_cache[v.item_id] = charm_compose_cache[v.item_id] or {item_id = v.item_id, num = 0}
			charm_compose_cache[v.item_id].num = charm_compose_cache[v.item_id].num + v.num
		end
	end

	if not IsEmptyTable(bag_one_key_cache) then
		for i = 0, 10 do
			if not IsEmptyTable(bag_one_key_cache[i]) then
				table.sort(bag_one_key_cache[i], SortTools.KeyUpperSorters("order", "color"))
			end
		end
	end

	self.bag_one_key_cache = bag_one_key_cache
	self.charm_compose_cache = charm_compose_cache
end

-- 缓存已装备的装备
function CultivationWGData:CalculationCharmEquipedEquip()
	local charm_compose_equiped_cache = {}
	local charm_suit_cache = {}

	for k, v in pairs(self:GetCharmHolySealSoltCfg()) do
		local solt_info = self:GetCharmSoltDataBySolt(v.solt)

		if solt_info.item_id > 0 then
			charm_compose_equiped_cache[solt_info.item_id] = charm_compose_equiped_cache[solt_info.item_id] or {item_id = solt_info.item_id, num = 0}
			charm_compose_equiped_cache[solt_info.item_id].num = charm_compose_equiped_cache[solt_info.item_id].num + 1
			charm_suit_cache[v.type] = charm_suit_cache[v.type] or {}
			-- local _, color = ItemWGData.Instance:GetItemColor(solt_info.item_id)

			-- for i = color, GameEnum.ITEM_COLOR_GREEN, -1 do
			-- 	charm_suit_cache[v.type][i] = charm_suit_cache[v.type][i] or {}
			-- 	local equip_cfg = self:GetCharmEquipByItemId(solt_info.item_id)

			-- 	for j = equip_cfg.order, 1, -1 do
			-- 		charm_suit_cache[v.type][i][j] = charm_suit_cache[v.type][i][j] or {num = 0}
			-- 		charm_suit_cache[v.type][i][j].num = charm_suit_cache[v.type][i][j].num + 1
			-- 	end
			-- end

			-- 2023/10/25 策划ZBP要求每一个type 取一个 2/4/6件得属性 阶数可以抵做下一阶得任意颜色，比如2阶可以看作1阶任意颜色 跨越品质限制  先阶级 后品质
			local equip_cfg = self:GetCharmEquipByItemId(solt_info.item_id)
			local _, color = ItemWGData.Instance:GetItemColor(solt_info.item_id)

			if equip_cfg.order > 1 then
				for j = equip_cfg.order - 1, 1, -1 do
					charm_suit_cache[v.type][j] = charm_suit_cache[v.type][j] or {}
					for i = GameEnum.ITEM_COLOR_COLOR_FUL, GameEnum.ITEM_COLOR_GREEN, -1 do
						charm_suit_cache[v.type][j][i] = charm_suit_cache[v.type][j][i] or {num = 0}
						charm_suit_cache[v.type][j][i].num = charm_suit_cache[v.type][j][i].num + 1
					end
				end
			end

			local cur_order = equip_cfg.order
			charm_suit_cache[v.type][cur_order] = charm_suit_cache[v.type][cur_order] or {}
			for i = color, GameEnum.ITEM_COLOR_GREEN, -1 do
				charm_suit_cache[v.type][cur_order][i] = charm_suit_cache[v.type][cur_order][i] or {num = 0}
				charm_suit_cache[v.type][cur_order][i].num = charm_suit_cache[v.type][cur_order][i].num + 1
			end
		end
	end

	self.charm_suit_cache = charm_suit_cache
	self.charm_compose_equiped_cache = charm_compose_equiped_cache
end

function CultivationWGData:GetCharmOneKeyBagCache(solt)
	return self.bag_one_key_cache[solt]
end

function CultivationWGData:CalculationCharmOneKeyWearDataList()
	local data_list = {}

	for k, v in pairs(self:GetCharmHolySealSoltCfg()) do
		local change_data = self:GetCharmOneKeyBagCache(v.solt)
		local best_quality_data = ((change_data or {})[1] or {})

		if not IsEmptyTable(best_quality_data) then
			if self:CanHolySealItemUp(best_quality_data.item_id) then
				table.insert(data_list, {item_id = best_quality_data.item_id, bag_index = best_quality_data.index, solt = v.solt})
			end
		end
	end

	self.charm_one_key_wear_datalist = data_list
end

function CultivationWGData:GetCharmOneKeyWearDataList()
	if IsEmptyTable(self.charm_one_key_wear_datalist) then
		return 0, {}
	end
	
	return #self.charm_one_key_wear_datalist, self.charm_one_key_wear_datalist
end

function CultivationWGData:CalculationCharmComposeRemind()
	local charm_compose_remind = false
	local charm_compose_cache_remind = {}

	local big_type, small_type = 1, 1

	for k, v in pairs(self.compose_menu_cfg) do
		big_type = k
		charm_compose_cache_remind[big_type] = charm_compose_cache_remind[big_type] or {big_type = big_type, is_remind = false, child_list = {}}
		charm_compose_cache_remind[big_type].is_remind = false

		for i = 1, #v do
			small_type = v[i].small_type
			local cfg = self:GetCharmComposeItemCfg(big_type, small_type)

			if not IsEmptyTable(cfg) then
				local has_equip_num = (self.charm_compose_cache[cfg.stuff_id1] or {}).num or 0
				local equiped_num = (self.charm_compose_equiped_cache[cfg.stuff_id1] or {}).num or 0
				local cost_equip_enough = (has_equip_num + equiped_num) >= cfg.stuff_num1
				local has_stuff_num = ItemWGData.Instance:GetItemNumInBagById(cfg.stuff_id2)
				local cost_stuff_enough = has_stuff_num >= cfg.stuff_num2
				local can_compose = cost_equip_enough and cost_stuff_enough
	
				if not self.charm_compose_stuff_cache[cfg.stuff_id2] then
					self.charm_compose_stuff_cache[cfg.stuff_id2] = cfg.stuff_id2
				end
	
				if can_compose then
					charm_compose_cache_remind[big_type].is_remind = true
					charm_compose_remind = true
				end
	
				charm_compose_cache_remind[big_type].child_list[small_type] = charm_compose_cache_remind[big_type].child_list[small_type] or {big_type = big_type, small_type = small_type ,is_remind = false}
				charm_compose_cache_remind[big_type].child_list[small_type].is_remind = can_compose
			end
		end
	end

	self.charm_compose_remind = charm_compose_remind
	self.charm_compose_cache_remind = charm_compose_cache_remind
end

function CultivationWGData:GetCharmComposeBigTypeRedmind(big_type)
	return (self.charm_compose_cache_remind[big_type] or {}).is_remind or false
end

function CultivationWGData:GetCharmComposeSmallTypeRedmind(big_type, small_type)
	return (((self.charm_compose_cache_remind[big_type] or {}).child_list or {})[small_type] or {}).is_remind or false
end

-- 合成如果是需要拿已装备的装备则返回 装备solt 否者 - 1
function CultivationWGData:GetCharmComposeNeedSolt(big_type, small_type)
	local solt = -1
	local data_cfg = self:GetCharmComposeItemCfg(big_type, small_type)
	
	if IsEmptyTable(data_cfg) then
		return solt
	end

	local equiped_num = (self.charm_compose_equiped_cache[data_cfg.stuff_id1] or {}).num or 0
	if equiped_num > 0 then
		local equip_cfg = self:GetCharmEquipByItemId(data_cfg.stuff_id1)

		if not IsEmptyTable(equip_cfg) then
			solt = equip_cfg.solt
		end
	end

	return solt
end

function CultivationWGData:GetCharmComposeDefaultSelect()
	local target_big_type, target_small_type = 1, 1
	local big_type, small_type = 1, 1

	for k, v in pairs(self.compose_menu_cfg) do
		big_type = k

		if self:GetCharmComposeBigTypeRedmind(big_type) then
			for i = 1, #v do
				small_type = v[i].small_type

				if self:GetCharmComposeSmallTypeRedmind(big_type, small_type) then
					return big_type, small_type
				end
			end
		end
	end
	
	return target_big_type, target_small_type
end

function CultivationWGData:GetCharmComposeSmallTypeSelect(big_type)
	local small_type = 1
	local data_list = self:GetComposeSmallItemListInfo(big_type)

	for i = 1, #data_list do
		if self:GetCharmComposeSmallTypeRedmind(big_type, data_list[i].small_type) then
			return data_list[i].small_type
		end
	end

	return small_type
end

function CultivationWGData:GetCharmComposeStuffNum(item_id)
	local equiped_num = (self.charm_compose_equiped_cache[item_id] or {}).num or 0
	local stuff_num = (self.charm_compose_cache[item_id] or {}).num or 0

	return equiped_num + stuff_num
end

function CultivationWGData:IsCharmComposeStuff(item_id)
	return nil ~= self.charm_compose_stuff_cache[item_id]
end

-- 2023/10/25 策划ZBP要求每一个type 取一个 2/4/6件得属性 阶数可以抵做下一阶得任意颜色，比如2阶可以看作1阶任意颜色 跨越品质限制  先阶级 后品质
function CultivationWGData:CalculationCharmSuitCache()
	local suit_atr_info = {}

	for type, type_tab in pairs(self.suit_attr_cfg) do -- 类型
		local suit_attr = {}

		for num, order_tab in pairs(type_tab) do  -- 数量
			local suit_attr_item = {}
			for order, color_tab in pairs(order_tab) do  --阶级
				for color, value_tab in pairs(color_tab) do --品质
					local has_num = self:GetCharmSuitCache(type, order, color)
					local is_need_special = value_tab.is_need_special == 1
					local real_num = is_need_special and (self:GetCharmSuitCache(CultivationWGData.CHARM_EQUIP_TYPE.SPECIAL, order, color) + has_num) or has_num

					if value_tab.need_num <= real_num then
						if IsEmptyTable(suit_attr_item) or (value_tab.need_order > suit_attr_item.need_order) or ((value_tab.need_order == suit_attr_item.need_order) and (value_tab.need_color > suit_attr_item.need_color)) then
							suit_attr_item = value_tab
						end
					end
				end
			end

			if not IsEmptyTable(suit_attr_item) then
				table.insert(suit_attr, suit_attr_item)
			end
		end

		suit_atr_info[type] = suit_attr
	end

	self.suit_atr_info = suit_atr_info
end

function CultivationWGData:GetCharmSuitCache(type, color, order)
	return (((self.charm_suit_cache[type] or {})[color] or {})[order] or {}).num or 0
end

function CultivationWGData:GetCharmSuitAttrDataList(type)
	return self.suit_atr_info[type]
end

function CultivationWGData:GetCharmSuitCap()
	return self.suit_cap_attr_cache.cap or 0
end

function CultivationWGData:GetCharmGeneralAttrDataList()
	return self.charm_solt_cap_attr_cache.attr_list or {} , self.charm_solt_cap_attr_cache.cap or 0
end

function CultivationWGData:CalculationCharmSuitCapAndAttr()
	local sign_data = {}
	local attr_info = {}
	local attr_count = 0

	for i = 0, 1 do
		local data_list = self:GetCharmSuitAttrDataList(i)

		if not IsEmptyTable(data_list) then
			for k, v in pairs(data_list) do
				for j = 1, 5 do
					local attr_id = v["attr_id" .. j]
					local attr_value = v["attr_value" .. j]

					if attr_id and attr_value and attr_id > 0 and attr_value > 0 then
						if not sign_data[attr_id] then
							attr_count = attr_count + 1
							sign_data[attr_id] = attr_count
							attr_info["attr_id" .. attr_count] = attr_id
							attr_info["attr_value" .. attr_count] = attr_value
						else
							attr_info["attr_value" .. sign_data[attr_id]] = attr_info["attr_value" .. sign_data[attr_id]] + attr_value
						end
					end
				end
			end
		end
	end

	local attr_list, cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(attr_info,  "attr_id", "attr_value", nil, nil, 1, attr_count)
	self.suit_cap_attr_cache.cap = cap
	self.suit_cap_attr_cache.attr_list = attr_list
end

-- 属性由普通属性改成了加成属性 不适用了
-- function CultivationWGData:CalculationCharmGeneralAttrDataList()
-- 	local sign_data = {}
-- 	local attr_info = {}
-- 	local attr_count = 0

-- 	for k, v in pairs(self:GetCharmHolySealSoltCfg()) do
-- 		local solt_info = self:GetCharmSoltDataBySolt(v.solt)

-- 		if not IsEmptyTable(solt_info) and solt_info.item_id > 0 then
-- 			local equip_cfg = self:GetCharmEquipByItemId(solt_info.item_id)

-- 			if not IsEmptyTable(equip_cfg) then
-- 				for j = 1, 7 do
-- 					local attr_id = equip_cfg["attr_id" .. j]
-- 					local attr_value = equip_cfg["attr_value" .. j]

-- 					if attr_id and attr_value and attr_id > 0 and attr_value > 0 then
-- 						if not sign_data[attr_id] then
-- 							attr_count = attr_count + 1
-- 							sign_data[attr_id] = attr_count
-- 							attr_info["attr_id" .. attr_count] = attr_id
-- 							attr_info["attr_value" .. attr_count] = attr_value
-- 						else
-- 							attr_info["attr_value" .. sign_data[attr_id]] = attr_info["attr_value" .. sign_data[attr_id]] + attr_value
-- 						end
-- 					end
-- 				end
				
-- 			end
-- 		end
-- 	end

-- 	local attr_list, cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(attr_info,  "attr_id", "attr_value", nil, nil, 1, attr_count)
-- 	self.charm_solt_cap_attr_cache.cap = cap
-- 	self.charm_solt_cap_attr_cache.attr_list = attr_list
-- end

-- 获取穿戴的加成总百分比列表  Language.Charm.AttrName
function CultivationWGData:CalculationCharmGeneralAttrDataList()
	local rate_list = self:GetAllAddRate()
	if IsEmptyTable(rate_list) then
		return
	end
	local attr_list = {}

	for i = 1, CHARM_RATE_TYPE.MAX do
		local data = {}
		data.attr_name = Language.Charm.AttrName[i]
        data.value_str = string.format("%s%%",tonumber(rate_list[i])/100)
		data.attr_sort = i
		table.insert(attr_list, data)
	end
	self.charm_solt_cap_attr_cache.attr_list = attr_list
	self.charm_solt_cap_attr_cache.cap = 0
end

function CultivationWGData:GetHolySealCap()
	local solt_cap = self.charm_solt_cap_attr_cache.cap or 0
	local suit_cap =  self.suit_cap_attr_cache.cap or 0
	return solt_cap + suit_cap
end


function CultivationWGData:IsCharmYingYangMaxLevel()
	return IsEmptyTable(self:GetCharmYingYangLevelCfg(self:GetCharmYingYangLevel() + 1))
end

function CultivationWGData:IsCharmYingYangBeadMaxLevel(index)
	return false
end

function CultivationWGData:CalculationCharmLongZhuAttrDataList()
	local charm_longzhu_addition_cache = {}
	local charm_longzhu_attr_cache = {}


	local pan_level = self:GetCharmYingYangLevel()
	local pan_level_cfg = self:GetCharmYingYangLevelCfg(pan_level)
	local next_pan_level = self:GetCharmYingYangLevelCfg(pan_level + 1)
	
	for i = 1, 8 do
		local longzhu_addition_id = i - 1
		local level = self:GetCharmYingYangBeadLevel(longzhu_addition_id)
		local current_attr_data = self:GetBeadLevelCfg(longzhu_addition_id, level)
		charm_longzhu_addition_cache[longzhu_addition_id] = current_attr_data

		local attr_id = pan_level_cfg["attr_id" .. i]
		local attr_value = pan_level_cfg["attr_value" .. i]

		if attr_id and attr_id > 0 and attr_value then --and attr_value > 0
			local longzhu_attr_data = {}
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
			longzhu_attr_data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
			longzhu_attr_data.attr_value = AttributeMgr.PerAttrValue(attr_str, attr_value)

			if not IsEmptyTable(next_pan_level) then
				local next_attr_id = next_pan_level["attr_id" .. i]
				local next_attr_value = next_pan_level["attr_value" .. i]

				if next_attr_id and next_attr_id > 0 and next_attr_value and next_attr_value > 0 then
					longzhu_attr_data.add_value_sign = true
					longzhu_attr_data.add_value = AttributeMgr.PerAttrValue(attr_str, next_attr_value)
				else
					longzhu_attr_data.add_value_sign = false
					longzhu_attr_data.add_value = AttributeMgr.PerAttrValue(attr_str, 0)
				end
			else
				longzhu_attr_data.add_value_sign = false
				longzhu_attr_data.add_value = AttributeMgr.PerAttrValue(attr_str, 0)
			end

			charm_longzhu_attr_cache[i] = longzhu_attr_data
		end
	end

	self.charm_longzhu_addition_cache = charm_longzhu_addition_cache
	self.charm_longzhu_attr_cache = charm_longzhu_attr_cache
end

function CultivationWGData:GetCharmLongZhuAttrDataList()
	return self.charm_longzhu_attr_cache
end

function CultivationWGData:GetCharmLongZhuAdditionCache()
	return self.charm_longzhu_addition_cache
end

function CultivationWGData:CanCharmLingZhuUpLevel()
	local is_max_level = self:IsCharmYingYangMaxLevel()
	if is_max_level then
		return is_max_level, false
	end

	local current_level_cfg = CultivationWGData.Instance:GetCharmYingYangLevelCfg(self:GetCharmYingYangLevel())
	return is_max_level, self:GetCharmYingYangExp() >= current_level_cfg.need_exp
end

function CultivationWGData:GetCharmItemData(cfg)
	local attr_list = {}
	local capability = 0

	if IsEmptyTable(cfg) then
		return attr_list, capability
	end
	for i = 1, CHARM_RATE_TYPE.MAX do
		if cfg["add_type"..i] and cfg["add_type"..i] ~= '0' then
			local data = {}
			data.attr_name = Language.Charm.AttrName[i]
            data.value_str = string.format("%s%%",tonumber(cfg["add_type"..i])/100)
			data.attr_sort = i
			table.insert(attr_list, data)
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list, capability
end

-- 获取穿戴的加成总百分比  Language.Charm.AttrName
function CultivationWGData:GetAllAddRate()
	local rate_list = {}

	for slot = 0, 10 do
		local current_data = self:GetCharmSoltDataBySolt(slot)
		if current_data and current_data.item_id > 0 then
			local cfg = CultivationWGData.Instance:GetCharmEquipByItemId(current_data.item_id)
			for i = 1, CHARM_RATE_TYPE.MAX do
				if rate_list[i] then
					rate_list[i] = rate_list[i] + cfg["add_type"..i]
				else
					rate_list[i] = cfg["add_type"..i]
				end
				
			end

		end
	end
	return rate_list
end



-- 获取某个类型加成总百分比  Language.Charm.AttrName
function CultivationWGData:GetAddRateByType(type)
	local add_rate = 0

	for slot = 0, 10 do
		local current_data = self:GetCharmSoltDataBySolt(slot)
		if current_data and current_data.item_id > 0 then
			local cfg = CultivationWGData.Instance:GetCharmEquipByItemId(current_data.item_id)
			add_rate = add_rate + cfg["add_type"..type]
		end
	end
	return add_rate
end
---------------------------------------------cal_end-------------------------------------------

--打宝boss进入条件
function CultivationWGData:GetDaboBossIsCanEneter(need_order, need_color)
	--全部槽位（背包或者已装备）满足条件
	if need_order == 0 or need_color == 0 then
		return true
	end

	for slot = 0, 10 do
		local better_equip_info = {}
		better_equip_info = self.solt_level_color
		if not better_equip_info then
			return
		end

		if better_equip_info[slot].max_level < need_order then
			return false
		elseif better_equip_info[slot].max_level == need_order and better_equip_info[slot].max_color < need_color then
			return false
		end
	end

	return true
end

function CultivationWGData:GetCharmSoltInfoAddColor()
	local chip_data = self:GetCharmSoltInfo()
	local equip_chip_data_slot = {}
	local equip_chip_data = {}
	for k, v in pairs(chip_data) do
		if chip_data and v.item_id ~= 0 then
			local _, equip_color = ItemWGData.Instance:GetItemColor(v.item_id)
			local equip_cfg = self:GetCharmEquipByItemId(v.item_id)
			equip_chip_data_slot[equip_cfg.solt] = {item_id = v.item_id, color = equip_color , solt = equip_cfg.solt, order = equip_cfg.order}
			equip_chip_data = {item_id = v.item_id, color = equip_color , solt = equip_cfg.solt, order = equip_cfg.order}
		end
	end

	return equip_chip_data_slot, equip_chip_data
end