-- K-跨服消费榜.xls
local item_table={
[1]={item_id=46588,num=1000,is_bind=1},
[2]={item_id=26464,num=1,is_bind=1},
[3]={item_id=26462,num=1,is_bind=1},
[4]={item_id=56404,num=1,is_bind=1},
[5]={item_id=56414,num=2,is_bind=1},
[6]={item_id=31259,num=2,is_bind=1},
[7]={item_id=46588,num=500,is_bind=1},
[8]={item_id=26463,num=1,is_bind=1},
[9]={item_id=26674,num=1,is_bind=1},
[10]={item_id=26689,num=1,is_bind=1},
[11]={item_id=46588,num=300,is_bind=1},
[12]={item_id=26461,num=1,is_bind=1},
[13]={item_id=26673,num=1,is_bind=1},
[14]={item_id=26688,num=1,is_bind=1},
[15]={item_id=31259,num=1,is_bind=1},
[16]={item_id=46588,num=150,is_bind=1},
[17]={item_id=26459,num=1,is_bind=1},
[18]={item_id=26672,num=1,is_bind=1},
[19]={item_id=26687,num=1,is_bind=1},
[20]={item_id=46588,num=100,is_bind=1},
[21]={item_id=26455,num=1,is_bind=1},
[22]={item_id=26671,num=1,is_bind=1},
[23]={item_id=26686,num=1,is_bind=1},
[24]={item_id=48499,num=1,is_bind=1},
[25]={item_id=46588,num=1500,is_bind=1},
[26]={item_id=56405,num=1,is_bind=1},
[27]={item_id=56415,num=3,is_bind=1},
[28]={item_id=31259,num=3,is_bind=1},
}

return {
open_day={
{}
},

open_day_meta_table_map={
},
rank_reward={
{},
{min_rank=2,max_rank=2,reach_value=2400000,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{min_rank=3,max_rank=3,reach_value=1500000,reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[3],[3]=item_table[9],[4]=item_table[10],[5]=item_table[6]},},
{min_rank=4,max_rank=5,reach_value=900000,reward_item={[0]=item_table[11],[1]=item_table[8],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14],[5]=item_table[15]},},
{min_rank=6,max_rank=10,reach_value=600000,reward_item={[0]=item_table[16],[1]=item_table[3],[2]=item_table[17],[3]=item_table[18],[4]=item_table[19],[5]=item_table[15]},},
{min_rank=11,max_rank=20,reach_value=300000,reward_item={[0]=item_table[20],[1]=item_table[12],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23],[5]=item_table[15]},}
},

rank_reward_meta_table_map={
},
open_day_default_table={start_day=1,end_day=99999,grade=1,},

rank_reward_default_table={grade=1,min_rank=1,max_rank=1,reach_value=3000000,reward_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[2],[3]=item_table[8],[4]=item_table[26],[5]=item_table[27],[6]=item_table[28]},}

}

