﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_UI_ColorBlockWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.UI.ColorBlock), null);
		<PERSON><PERSON>RegFunction("Equals", Equals);
		L<PERSON>RegFunction("GetHashCode", GetHashCode);
		<PERSON><PERSON>RegFunction("New", _CreateUnityEngine_UI_ColorBlock);
		L<PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("defaultColorBlock", get_defaultColorBlock, set_defaultColorBlock);
		<PERSON><PERSON>("normalColor", get_normalColor, set_normalColor);
		L.<PERSON>ar("highlightedColor", get_highlightedColor, set_highlightedColor);
		<PERSON>.Reg<PERSON>ar("pressedColor", get_pressedColor, set_pressedColor);
		<PERSON><PERSON>("selectedColor", get_selectedColor, set_selectedColor);
		<PERSON><PERSON>("disabledColor", get_disabledColor, set_disabledColor);
		<PERSON><PERSON>("colorMultiplier", get_colorMultiplier, set_colorMultiplier);
		L.RegVar("fadeDuration", get_fadeDuration, set_fadeDuration);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_UI_ColorBlock(IntPtr L)
	{
		UnityEngine.UI.ColorBlock obj = new UnityEngine.UI.ColorBlock();
		ToLua.PushValue(L, obj);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Equals(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<UnityEngine.UI.ColorBlock>(L, 2))
			{
				UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)ToLua.CheckObject(L, 1, typeof(UnityEngine.UI.ColorBlock));
				UnityEngine.UI.ColorBlock arg0 = StackTraits<UnityEngine.UI.ColorBlock>.To(L, 2);
				bool o = obj.Equals(arg0);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<object>(L, 2))
			{
				UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)ToLua.CheckObject(L, 1, typeof(UnityEngine.UI.ColorBlock));
				object arg0 = ToLua.ToVarObject(L, 2);
				bool o = obj.Equals(arg0);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.UI.ColorBlock.Equals");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.UI.ColorBlock arg0 = StackTraits<UnityEngine.UI.ColorBlock>.To(L, 1);
			UnityEngine.UI.ColorBlock arg1 = StackTraits<UnityEngine.UI.ColorBlock>.To(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetHashCode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)ToLua.CheckObject(L, 1, typeof(UnityEngine.UI.ColorBlock));
			int o = obj.GetHashCode();
			LuaDLL.lua_pushinteger(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultColorBlock(IntPtr L)
	{
		try
		{
			ToLua.PushValue(L, UnityEngine.UI.ColorBlock.defaultColorBlock);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_normalColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color ret = obj.normalColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index normalColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_highlightedColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color ret = obj.highlightedColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index highlightedColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pressedColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color ret = obj.pressedColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pressedColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_selectedColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color ret = obj.selectedColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectedColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_disabledColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color ret = obj.disabledColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disabledColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_colorMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			float ret = obj.colorMultiplier;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorMultiplier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fadeDuration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			float ret = obj.fadeDuration;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeDuration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultColorBlock(IntPtr L)
	{
		try
		{
			UnityEngine.UI.ColorBlock arg0 = StackTraits<UnityEngine.UI.ColorBlock>.Check(L, 2);
			UnityEngine.UI.ColorBlock.defaultColorBlock = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_normalColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.normalColor = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index normalColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_highlightedColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.highlightedColor = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index highlightedColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pressedColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.pressedColor = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pressedColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_selectedColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.selectedColor = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectedColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_disabledColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.disabledColor = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disabledColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_colorMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.colorMultiplier = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorMultiplier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fadeDuration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.ColorBlock obj = (UnityEngine.UI.ColorBlock)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.fadeDuration = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeDuration on a nil value");
		}
	}
}

