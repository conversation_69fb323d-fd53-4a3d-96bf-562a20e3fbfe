﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEngine.Rendering;


#if UNITY_EDITOR
using UnityEditor;
#endif

public class YYCameraDepth : MonoBehaviour
{
    public RenderTexture depthRT;
    public RenderTexture colorRT;


    private static YYCameraDepth instance;

    public static YYCameraDepth Instance
    {
        get { return instance; }
    }


    private Camera _Camera = null;

  
    private void OnEnable()
    {
        instance = this;
        _Camera = Camera.main;

        InitBuffer(_Camera);

       

       
    }

    public void InitBuffer(Camera bufferCam)
    {
        if (colorRT == null)
        {
            var sourceFormat = _Camera.allowHDR ? defaultHDRRenderTextureFormat : RenderTextureFormat.Default;

            colorRT = RenderTexture.GetTemporary(Screen.width, Screen.height, 0,sourceFormat);
            depthRT = RenderTexture.GetTemporary(Screen.width, Screen.height, 24, RenderTextureFormat.Depth);
        }
        bufferCam.SetTargetBuffers(colorRT.colorBuffer, depthRT.depthBuffer);
        //bufferCam.SetTargetBuffers(null, depthBufferRT.depthBuffer);

        //bufferCam.targetTexture = colorRT;

        
        Shader.SetGlobalTexture("_YYDepthTexture", depthRT);
        Shader.SetGlobalFloat("_DepthType", (float)1);
        // _DepthType
        //Shader.SetGlobalFloat()
    }

    private void OnDisable()
    {
        instance = null;
        Shader.SetGlobalFloat("_DepthType", (float)0);

        ReleaseBuffer(_Camera);
    }

    public void ReleaseBuffer(Camera bufferCam)
    {
        if (colorRT != null)
        {
            RenderTexture.ReleaseTemporary(colorRT);
            RenderTexture.ReleaseTemporary(depthRT);
            colorRT = null;
            depthRT = null;
        }
        bufferCam.targetTexture = null;
    }

    private void OnPreRender()
    {
        //Shader.SetGlobalTexture("_YYDepthTexture", depthRT);
    }

    private void OnPostRender()
    {
        Graphics.Blit(colorRT, (RenderTexture)null);
    }



    static RenderTextureFormat defaultHDRRenderTextureFormat
    {
        get
        {
#if UNITY_ANDROID || UNITY_IPHONE || UNITY_IOS || UNITY_TVOS || UNITY_SWITCH || UNITY_EDITOR
            //#if UNITY_ANDROID || UNITY_IPHONE || UNITY_TVOS || UNITY_SWITCH || UNITY_EDITOR
            //RenderTextureFormat format = RenderTextureFormat.RGB111110Float;
            RenderTextureFormat format = RenderTextureFormat.ARGBHalf;
#if UNITY_EDITOR
            var target = EditorUserBuildSettings.activeBuildTarget;
            if (target != BuildTarget.Android && target != BuildTarget.iOS && target != BuildTarget.tvOS && target != BuildTarget.Switch)
                return RenderTextureFormat.DefaultHDR;
#endif // UNITY_EDITOR
            //if (format.IsSupported())
            if(SystemInfo.SupportsRenderTextureFormat((RenderTextureFormat)format))
                return format;
#endif // UNITY_ANDROID || UNITY_IPHONE || UNITY_TVOS || UNITY_SWITCH || UNITY_EDITOR
            return RenderTextureFormat.DefaultHDR;
        }
    }

}
