--喜迎财神
GodOfWealthView = GodOfWealthView or BaseClass(SafeBaseView)

local total_num = 8     --总的类别数量
local rotate_time = 4   --旋转动画时间

local SpineAnimType = {
	--[1] = {"push30", 3.667 - 0.2 },
	[1] = {"push60", 4.333 - 0.2 },
	[2] = {"push60", 4.333 - 0.2 },
	[3] = {"push60", 4.333 - 0.2 },
	[4] = {"push60", 4.333 - 0.2 },
	[5] = {"push60", 4.333 - 0.2 },
	[6] = {"push60", 4.333 - 0.2 },
	[7] = {"push60", 4.333 - 0.2 },
    [8] = {"push60", 4.333 - 0.2 },
}

function GodOfWealthView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.default_index = TabIndex.god_of_wealth

    local bundle = "uis/view/god_of_wealth_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(TabIndex.god_of_wealth, bundle, "layout_god_of_wealth")
    self:AddViewResource(TabIndex.god_of_wealth_lucky_star, bundle, "layout_god_of_wealth_luckystar")
	self:AddViewResource(0, bundle, "VerticalTabbar")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

	self.remind_tab = {
		{RemindName.Wealth_god_fanyu},
		{RemindName.Wealth_god_lucky_wealth},
	}
end

function GodOfWealthView:ReleaseCallBack()
    if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
    end

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
    end

    self:GodOfWealthReleaseCallBack()
	self:LuckyStarReleaseCallBack()
end

function GodOfWealthView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarNameImgRes(ResPath.GetGodOfWealthImg(), "a3_zcjb_tab")
        self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabbarInfo, self))
		self.tabbar:Init(Language.GodOfWealth.TabGrop, nil, "uis/view/god_of_wealth_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.GodOfWealthView, self.tabbar)
	end

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
            show_gold = true, show_bind_gold = true,
            show_coin = true, show_silver_ticket = true,
            show_cash_point = true
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end



    self.node_list.title_view_name.text.text = Language.GodOfWealth.TitleName
end

function GodOfWealthView:SetTabbarInfo()
    -- 2025.4.25 暂时屏蔽全部页签
    self.tabbar:SetVerToggleCanvas(TabIndex.god_of_wealth, false)
    self.tabbar:SetVerToggleCanvas(TabIndex.god_of_wealth_lucky_star, false)
end

function GodOfWealthView:LoadIndexCallBack(index)
	if index == TabIndex.god_of_wealth then
		self:GodOfWealthLoadCallBack()
	elseif index == TabIndex.god_of_wealth_lucky_star then
		self:LuckyStarLoadCallBack()
	end
end

function GodOfWealthView:ShowIndexCallBack(index)
	local bundle, asset = ResPath.GetRawImagesPNG("a3_zcjb_bg")
    if index == TabIndex.god_of_wealth then
        --self:GodOfWealthSetUITween()
		bundle, asset = ResPath.GetRawImagesPNG("a3_zcjb_bg")
    elseif index == TabIndex.god_of_wealth_lucky_star then
        bundle, asset = ResPath.GetRawImagesPNG("a3_zcjb_bg2")
    end
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function GodOfWealthView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.god_of_wealth then
				self:GodOfWealthOnFlush()
			elseif index == TabIndex.god_of_wealth_lucky_star then
				self:LuckyStarOnFlush()
			end
		end
	end
end

function GodOfWealthView:GodOfWealthLoadCallBack()
	self.tuibi_skeleton = self.node_list["spine_tuibiji"].gameObject:GetComponent("SkeletonGraphic")

	if not self.draw_cost_item then
		self.draw_cost_item = ItemCell.New(self.node_list["cost_item_cell_root"])
	end

	--[[
    for k = 1, total_num do
        self.node_list["high_light"..k]:SetActive(false)
    end
	]]
    
	--self.node_list.desc_info_text.text.text = Language.GodOfWealth.ActivitiDesc
    --self.node_list.record_list_title.text.text = Language.GodOfWealth.RecordListTitle
    self.node_list.auto_chongzhi_text.text.text = Language.GodOfWealth.BtnAutoRecharge

    self:SetRewardPoolTime()
    local is_skip = GodOfWealthWGData.Instance:GetSkipAniFlag()
    self.node_list["toggle_mark"]:SetActive(is_skip)

    if not self.recharge_type_list then
        self.recharge_type_list = AsyncListView.New(RechargeTypeListItemRender, self.node_list.recharge_type_list)
    end

	--[[
    if not self.luck_deaw_reward_list then
        self.luck_deaw_reward_list = AsyncListView.New(ItemCell, self.node_list.luck_deaw_reward_list)
    end
	]]

    if not self.reward_item_list then
        self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.quanying_reward_list)
        self.reward_item_list:SetStartZeroIndex(true)
    end

	if not self.next_reward_item_cell then
		self.next_reward_item_cell = ItemCell.New(self.node_list.next_reward_item_cell_root)
	end

    if not self.wealth_records_list then
        self.wealth_records_list = AsyncBaseGrid.New()
        local bundle = "uis/view/god_of_wealth_ui_prefab"
		local asset = "god_wealth_record_cell"
        self.wealth_records_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["wealth_records_list"],
            assetBundle = bundle, assetName = asset, itemRender = GodOfWealthRecordItemRender2})
        self.wealth_records_list:SetStartZeroIndex(false)
    end

	if not self.draw_item_list then
		self.draw_item_list = {}
		for i = 1, total_num do
			self.draw_item_list[i] = GODDrawMulItem.New(self.node_list.draw_reward_grid:FindObj("draw_item_" .. i))
			self.draw_item_list[i]:SetIndex(i)
		end
	end

	self.node_list["toggle_jump"].button:AddClickListener(BindTool.Bind(self.OnClickJumpAni, self))
    XUI.AddClickEventListener(self.node_list.btn_arrow, BindTool.Bind(self.OnClickArrow, self))
    XUI.AddClickEventListener(self.node_list.btn_chongzhi, BindTool.Bind(self.OnClickRecharge, self))
    XUI.AddClickEventListener(self.node_list.btn_auto_chongzhi, BindTool.Bind(self.OnClickAutoRecharge, self))
    --XUI.AddClickEventListener(self.node_list.btn_god_gift, BindTool.Bind2(self.ChangePanelState, self, false))
    XUI.AddClickEventListener(self.node_list.btn_back_draw, BindTool.Bind2(self.ChangePanelState, self, true))
    XUI.AddClickEventListener(self.node_list.btn_cifu, BindTool.Bind(self.OnClickOpenCiFuView, self))
    XUI.AddClickEventListener(self.node_list.btn_all_buy_reward, BindTool.Bind(self.OnClickAllReward, self))
    XUI.AddClickEventListener(self.node_list.god_records_tips, BindTool.Bind(self.OnClickRecordsTip, self))
	XUI.AddClickEventListener(self.node_list.probability_show_btn, BindTool.Bind(self.OpenGaiLvView, self))
    XUI.AddClickEventListener(self.node_list.rule_btn, BindTool.Bind(self.OnClickBtnTips, self))
end

function GodOfWealthView:GodOfWealthReleaseCallBack()
	if self.draw_cost_item then
		self.draw_cost_item:DeleteMe()
		self.draw_cost_item = nil
	end

    if self.wealth_records_list then
        self.wealth_records_list:DeleteMe()
        self.wealth_records_list = nil
    end

    if self.tween then
		self.tween:Kill()
		self.tween = nil
    end
	
    if self.recharge_list then
        self.recharge_list:DeleteMe()
        self.recharge_list = nil
    end

    if self.recharge_type_list then
        self.recharge_type_list:DeleteMe()
        self.recharge_type_list = nil
    end

	--[[
    if self.luck_deaw_reward_list then
        self.luck_deaw_reward_list:DeleteMe()
        self.luck_deaw_reward_list = nil
    end
	]]

    if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end

    if self.next_reward_item_cell then
		self.next_reward_item_cell:DeleteMe()
		self.next_reward_item_cell = nil
	end

	if self.draw_item_list then
		for k, v in pairs(self.draw_item_list) do
			v:DeleteMe()
		end
		self.draw_item_list = nil
	end

	self.cur_draw_times = nil
	self.is_playing_draw_anim = nil
    self.tuibi_skeleton = nil
	self.old_value = nil
    self:CleanTimer()
	--self:ClearValueChangeTimer()
end

function GodOfWealthView:SetRewardPoolTime()
    local pool_summary_time = GodOfWealthWGData.Instance:GetPoolSummaryTime()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local time = pool_summary_time - server_time
    local total_time = time > 0 and time or 0

    if total_time > 0 then
        local interval = 1

        self:CleanTimer()
        self.timer = CountDown.Instance:AddCountDown(total_time, interval,
            function(elapse_time, total_time)
                if self.node_list.open_reward_time then
                    local new_time = TimeUtil.FormatSecondDHM6(total_time - elapse_time)
                    self.node_list.open_reward_time.text.text = string.format(Language.GodOfWealth.OpenRewardTime, new_time)
                end
            end,
            function()
                if self.node_list.open_reward_time then
                    self.node_list.open_reward_time.text.text = ""
                end
            end
        )
    end
end

function GodOfWealthView:CleanTimer()
    if self.timer and CountDown.Instance:HasCountDown(self.timer) then
        CountDown.Instance:RemoveCountDown(self.timer)
        self.timer = nil
    end
end

--[[
function GodOfWealthView:GodOfWealthSetUITween()
    UITween.CleanAllTween(GuideModuleName.GodOfWealthView)
    -- UITween.FakeHideShow(self.node_list.record_list)

    --图标和数字动画
    UITween.FakeHideShow(self.node_list.num_bg)
    for k = 1, 8 do
        UITween.FakeToShow(self.node_list.num_bg)
        local move_delay_time = 0.2 * k

        if self.node_list["icon_"..k] then
            UITween.FakeHideShow(self.node_list["icon_"..k])
            ReDelayCall(self, function()
                if self.node_list and self.node_list["icon_"..k] then
                    UITween.AlphaShow(GuideModuleName.GodOfWealthView, self.node_list["icon_"..k], 0, 1, 0.5, DG.Tweening.Ease.Linear)
                end
            end, move_delay_time, "god_of_wealth_num_" .. k)
        end
    end
end
]]


function GodOfWealthView:GodOfWealthOnFlush()
    local can_draw_times = GodOfWealthWGData.Instance:GetCanDrawTime()
	local draw_item_id = GodOfWealthWGData.Instance:GetDrawItemId()
    --self.node_list.cur_times_txt.text.text = string.format(Language.GodOfWealth.DeawRemainTime, can_draw_times)
    --self.node_list.btn_arrow_effect:SetActive(can_draw_times > 0)
    self.draw_cost_item:SetData({ item_id = draw_item_id })
	local show_num_str = can_draw_times <= 0 and ToColorStr(can_draw_times, COLOR3B.RED) or tostring(can_draw_times)
	self.draw_cost_item:SetRightBottomTextVisible(true)
	self.draw_cost_item:SetRightBottomText(can_draw_times)
	self.node_list["img_cost_num_red"]:SetActive(can_draw_times > 0)
    if self.cur_draw_times and self.cur_draw_times < can_draw_times then
        self:PlayGetDrawTimesEffect()
	end
	self.cur_draw_times = can_draw_times

    local rewaed_list_data = GodOfWealthWGData.Instance:GetAllBuyRewardList()
	if self.reward_item_list then
		self.reward_item_list:SetDataList(rewaed_list_data)
	end

    local current_rmb_bug_cfg, max_rmb_bug_cfg = GodOfWealthWGData.Instance:GetCurrentRmbBuyCfg()
    local has_data = not IsEmptyTable(current_rmb_bug_cfg)
    XUI.SetButtonEnabled(self.node_list.btn_chongzhi, has_data)
    XUI.SetButtonEnabled(self.node_list.btn_auto_chongzhi, has_data)

    local need_rmb = has_data and current_rmb_bug_cfg.price or max_rmb_bug_cfg.price
	local rmb_type = has_data and current_rmb_bug_cfg.rmb_type or max_rmb_bug_cfg.rmb_type
    local rmb_seq = has_data and current_rmb_bug_cfg.rmb_seq or max_rmb_bug_cfg.rmb_seq
	local price_str = RoleWGData.GetPayMoneyStr(need_rmb, rmb_type, rmb_seq)
    self.node_list.chongzhi_text.text.text = price_str

    local is_free = has_data and current_rmb_bug_cfg.is_free or max_rmb_bug_cfg.is_free
    self.node_list.img_chongzhi_free:CustomSetActive(is_free == 1)

    local money_type = RoleWGData.GetPayMoneyType()
    --self.node_list.dollar:SetActive(money_type == PAY_MONEY_TYPES.DOLLAR)
    --self.node_list.yuan:SetActive(money_type == PAY_MONEY_TYPES.RMB)
    --self.node_list.gamepoint:SetActive(money_type == PAY_MONEY_TYPES.GAME_COUNT)
    local money_num = RoleWGData.GetPayMoneyChange(need_rmb, rmb_type, rmb_seq)
    --self.node_list.recharge_num.text.text = money_num

    if not GodOfWealthWGData.Instance:GetDrawState() then
        self:FlushPer()
    end

    self:FlushRechargePanel()

    local can_show_recharge_btn = GodOfWealthWGData.Instance:IsCanShowRechargePanel()
    --self.node_list.btn_god_gift:SetActive(can_show_recharge_btn)
    self.node_list.btn_cifu:SetActive(can_show_recharge_btn)

    local cifu_red = GodOfWealthWGData.Instance:GetBlessingAllRed()
    self.node_list["cifu_remind"]:SetActive(cifu_red)

    local data_list = GodOfWealthWGData.Instance:GetReChargeRmbBuyCfg()
    self.recharge_type_list:SetDataList(data_list)

	--[[
	local item_list = GodOfWealthWGData.Instance:GetLuckyStarShowItemList()
	local remind = GodOfWealthWGData.Instance:GetLuckyStarRemind()
	self.luck_deaw_reward_list:SetDataList(item_list)
	self.node_list.luck_draw_remind:CustomSetActive(remind)
	]]--
    
	local all_buy_reward_red = GodOfWealthWGData.Instance:AllBuyRewardFlagRemind()
	self.node_list.all_buy_reward_red:SetActive(all_buy_reward_red)
	
	local all_buy = GodOfWealthWGData.Instance:GetAllReChargeRmbBuyFlag()
	local is_get_all = all_buy and not all_buy_reward_red
	self.node_list.btn_all_buy_reward:SetActive(not is_get_all)
	self.node_list.all_buy_has_get_flag:SetActive(is_get_all)
	self.node_list["quanying_text_h"].text.text = all_buy_reward_red and Language.GodOfWealth.GetReward or Language.GodOfWealth.OneKeyBuy

    self.node_list.gift_desc.text.text = Language.GodOfWealth.GiftDesc

    local is_summary = GodOfWealthWGData.Instance:GetWealthGodIsSummaryToday()
    self.node_list.summary_desc:SetActive(is_summary)
    self.node_list.wealth_records_list:SetActive(is_summary)
    self.node_list.no_record_desc:SetActive(is_summary)
    self.node_list.open_reward_time:SetActive(not is_summary)
    self.node_list.is_get_reward_chance_desc:SetActive(not is_summary)
    if is_summary then
        local count, rank_item_list = GodOfWealthWGData.Instance:GetWealthGodRankInfo()
        self.node_list["wealth_records_list"]:SetActive(count > 0)
        self.node_list["no_record_desc"]:SetActive(count <= 0)
        if count > 0 and not IsEmptyTable(rank_item_list) then
            self.wealth_records_list:SetDataList(rank_item_list)
        end
    else
        local is_have = GodOfWealthWGData.Instance:GetWealthGodIsHaveRewardChance()
        self.node_list.is_get_reward_chance_desc.text.text = is_have and Language.GodOfWealth.GetRewardChanceDesc or Language.GodOfWealth.NotGetRewardChanceDesc
    end
    --[[
	if all_buy and not all_buy_reward_red then
		XUI.SetGraphicGrey(self.node_list.btn_all_buy_reward, true)
		self.node_list.quanying_text_h:SetActive(false)
		self.node_list.quanying_text_n:SetActive(true)
	else
		XUI.SetGraphicGrey(self.node_list.btn_all_buy_reward, false)
		self.node_list.quanying_text_h:SetActive(true)
		self.node_list.quanying_text_n:SetActive(false)
	end
	]]--
end

function GodOfWealthView:PlayGetDrawTimesEffect()
	self.node_list["effect_root_boom"]:SetActive(false)
    self.node_list["effect_root_boom"]:SetActive(true)
	ReDelayCall(self, function()
		self.node_list["effect_root_boom"]:SetActive(false)
	end, 2, "GodOfWealthDrawTimesAdded")
end

function GodOfWealthView:FlushPer()
	local beishu_cfg = GodOfWealthWGData.Instance:GetCurrentMultiplayCfg()

    if not IsEmptyTable(beishu_cfg) then
        for i = 1, total_num do
            self.draw_item_list[i]:SetData(beishu_cfg[i])
            self.draw_item_list[i]:SetHLImage(false)
            self.draw_item_list[i]:SetMask(false)
            --self.node_list["beishu_" .. i].text.text = string.format(Language.GodOfWealth.BeiShuDes, beishu_cfg[i].beishu)
        end   
    end

    local max_per = GodOfWealthWGData.Instance:GetMaxPer()
    self.node_list.txt_max_per.text.text = string.format(Language.GodOfWealth.MaxPer, max_per)
    local has_draw_time = GodOfWealthWGData.Instance:GetCurDrawTimes()
    local draw_cfg = GodOfWealthWGData.Instance:GetDrawCfg(has_draw_time)
    self.node_list["txt_base_lingyu"].text.text = string.format(Language.GodOfWealth.BaseGold, draw_cfg.base_gold)

    local pool_gold = GodOfWealthWGData.Instance:GetWealthGodPoolInfo()
    self.node_list.reward_num.text.text = pool_gold

	-- 下次必得
    local next_reward_item = GodOfWealthWGData.Instance:GetNextDrawRewardShowItem()
	self.node_list["next_reward_root"]:SetActive(not IsEmptyTable(next_reward_item))
    self.next_reward_item_cell:SetData(next_reward_item[0] or {})

	-- 返还灵玉
	--self:ChangeShowLingYu(draw_cfg.gold_show)
end

function GodOfWealthView:ReceiveProtocolPlayAnim()
	-- 抽奖结果
	local index, per = GodOfWealthWGData.Instance:GetGodOfWealthCurIndex()
	-- 获得灵玉
	local has_draw_time = GodOfWealthWGData.Instance:GetCurDrawTimes()
    local draw_cfg = GodOfWealthWGData.Instance:GetDrawCfg(has_draw_time - 1)
    local next_draw_cfg = GodOfWealthWGData.Instance:GetDrawCfgReal(has_draw_time)
	local reward_gold = draw_cfg.base_gold * per

	--前端展示获得道具 -- 策划需求按倍数显示灵玉
	local show_item_list = {}
	local reward_gold_item = { item_id = 65534, num = draw_cfg.base_gold }
	if not IsEmptyTable(draw_cfg.reward_item) then
		table.insert(show_item_list, draw_cfg.reward_item[0])
	end
	for i = 1, per do
		table.insert(show_item_list, reward_gold_item)
	end

	-- 是否跳过动画
	local is_skip = GodOfWealthWGData.Instance:GetSkipAniFlag()

    local other_info = {}
    local show_str = string.format(Language.GodOfWealth.TuiBiRewardDesc, per, reward_gold)
    if not IsEmptyTable(next_draw_cfg) then
        show_str = show_str .. string.format(Language.GodOfWealth.TuiBiRewardDesc2, next_draw_cfg.gold_show)
    end
    other_info.show_reward_desc = show_str
    --[[
	if is_skip then
		self:FlushPer()
		TipWGCtrl.Instance:ShowGetCommonReward(show_item_list, nil, other_info)
		GodOfWealthWGData.Instance:SetDrawState(false)
	else
        self.is_playing_draw_anim = true
        self:PlayTuiBiAnim(index)
        -- 灵玉数字变化，延迟3秒开始播放
        ReDelayCall(self, function()
            self:PlayLingYuValueAnim(draw_cfg.gold_show, 0, SpineAnimType[index][2])
        end, 3, "GodOfWealthDraw2")
		ReDelayCall(self, function()
            self:ClearValueChangeTimer()
            self:FlushPer()
			TipWGCtrl.Instance:ShowGetCommonReward(show_item_list, nil, other_info)
			GodOfWealthWGData.Instance:SetDrawState(false)
            self.is_playing_draw_anim = false
		end, SpineAnimType[index][2], "GodOfWealthDraw")
	end
	]]
	-- 为防止以后策划还要改成转盘形式，代码就先不删了
    if is_skip then
        for i = 1, total_num do
            self.draw_item_list[i]:SetHLImage(i == index)
        end
        self:FlushPer()
        TipWGCtrl.Instance:ShowGetCommonReward(show_item_list, nil, other_info)
        GodOfWealthWGData.Instance:SetDrawState(false)
        --指针与获得提示
        self.node_list.parent_arrow.transform.localRotation = Quaternion.Euler(0, 0, -360 / total_num * (index))
    else
        -- 倍数转盘动画
		self.tween = DG.Tweening.DOTween.Sequence()
        local tween_rotate = self.node_list.parent_arrow.transform:DORotate(Vector3(0, 0, -360 * rotate_time - 360 / total_num * (index)), rotate_time, DG.Tweening.RotateMode.FastBeyond360)
        tween_rotate:SetEase(DG.Tweening.Ease.OutCubic)
        self.tween:Append(tween_rotate)
        self.tween:OnUpdate(function()
            local rotate_z = self.node_list.parent_arrow.transform.localEulerAngles.z
            local tween_idx = self:GetCurPointIndex(rotate_z)
            for i = 1, total_num do
                self.draw_item_list[i]:SetHLImage(i == tween_idx)
            end
        end)
        self.tween:OnComplete(function()
            --self:FlushPer()
            --GodOfWealthWGData.Instance:SetDrawState(false)
            for i = 1, total_num do
                self.draw_item_list[i]:SetHLImage(i == index)
            end
            self.tween:Kill()
            self.tween = nil
        end)

        -- spine动画
        self.is_playing_draw_anim = true
        self:PlayTuiBiAnim(index)
		ReDelayCall(self, function()
            self:FlushPer()
			TipWGCtrl.Instance:ShowGetCommonReward(show_item_list, nil, other_info)
			GodOfWealthWGData.Instance:SetDrawState(false)
            self.is_playing_draw_anim = false
		end, SpineAnimType[index][2], "GodOfWealthDraw")
    end
end

---播放推币动画
---@param index number 抽奖结果index
function GodOfWealthView:PlayTuiBiAnim(index)
	self.tuibi_skeleton.AnimationState:SetAnimation(0, "fall", false)
	self.tuibi_skeleton.AnimationState:AddAnimation(0, SpineAnimType[index][1], false, 0)
	self.tuibi_skeleton.AnimationState:AddAnimation(0, "idle", true, 0)
end

function GodOfWealthView:ClearValueChangeTimer()
	if self.number_timer and CountDown.Instance:HasCountDown(self.number_timer) then
        CountDown.Instance:RemoveCountDown(self.number_timer)
        self.number_timer = nil
    end
end

-- 数字变化
function GodOfWealthView:PlayLingYuValueAnim(start_value, end_value, duration)
    --local start_value = self.old_value or 0
    local duration = duration or 0
    self:ClearValueChangeTimer()
	self.number_timer = CountDown.Instance:AddCountDown(duration, 0,
        function(elapse_time, total_time)
            local cur_show_value = start_value + (end_value - start_value) * (elapse_time / total_time)
            self:ChangeShowLingYu(cur_show_value)
        end,
        function()
			self:ChangeShowLingYu(end_value)
		end
	)
end

function GodOfWealthView:ChangeShowLingYu(value)
	self.old_value = value
	self.node_list["txt_lingyu_count"].text.text = self:PadToSixDigits(value)
end

---显示6位数字(整数)，不足补0，多余忽略
---@param number number
function GodOfWealthView:PadToSixDigits(number)
	local intNumber = math.floor(math.abs(number))
	local numberStr = tostring(intNumber)
    if #numberStr > 6 then
        numberStr = string.sub(numberStr, -6)
    end
	return string.format("%06d", tonumber(numberStr))
end

function GodOfWealthView:GetCurPointIndex(rotate_z)
    local rotate = 360 / total_num
    return total_num - math.floor((rotate_z + rotate / 2) / rotate)
end

function GodOfWealthView:OnClickRecharge()
    local current_rmb_bug_cfg = GodOfWealthWGData.Instance:GetCurrentRmbBuyCfg()
    if not IsEmptyTable(current_rmb_bug_cfg) and current_rmb_bug_cfg.price >= 0 then
        if current_rmb_bug_cfg.is_free == 1 then
            GodOfWealthWGCtrl.Instance:SendOperaReq(WEALTH_GOD_OPERATE_TYPE.FETCH_FREE_RMB_BUY, current_rmb_bug_cfg.seq)
        else
            RechargeWGCtrl.Instance:Recharge(current_rmb_bug_cfg.price, current_rmb_bug_cfg.rmb_type, current_rmb_bug_cfg.seq)
        end
    end
end

function GodOfWealthView:OnClickAutoRecharge()
    ViewManager.Instance:Open(GuideModuleName.YiJianZhaoCaiView)
end

function GodOfWealthView:OnClickArrow()
	if self.tween then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FortuneCat.IsInDrawing)
        return
    end
    
	if self.is_playing_draw_anim then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FortuneCat.IsInDrawing)
        return
	end

	local can_draw_time = GodOfWealthWGData.Instance:GetCanDrawTime()
    if can_draw_time <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.GodOfWealth.NoDrawTime)
        return 
    end

	--[[
    for k = 1, total_num do
        self.node_list["high_light"..k]:SetActive(false)
    end
	]]

    GodOfWealthWGCtrl.Instance:SendOperaReq(WEALTH_GOD_OPERATE_TYPE.WEALTH_GOD_OPERATE_TYPE_DRAW)
    GodOfWealthWGData.Instance:SetDrawState(true)
end

function GodOfWealthView:OnClickOpenCiFuView()
    GodOfWealthWGCtrl.Instance:OpenBlessingView()
end

function GodOfWealthView:OnClickJumpAni()
    GodOfWealthWGData.Instance:SetSkipAniFlag()
    local is_skip = GodOfWealthWGData.Instance:GetSkipAniFlag()
    self.node_list["toggle_mark"]:SetActive(is_skip)
end

function GodOfWealthView:ChangePanelState(show_desc)
    -- self.node_list.desc_panel:SetActive(show_desc)
    --self.node_list.recharge_panel:SetActive(not show_desc)
    self.node_list.recharge_type_list:CustomSetActive(show_desc)
    self.node_list.btn_auto_chongzhi:CustomSetActive(show_desc)
    --self.node_list.btn_god_gift:CustomSetActive(show_desc)
end

function GodOfWealthView:FlushRechargePanel()
    if not self.recharge_list then
        self.recharge_list = AsyncListView.New(GodOfWealthRechargeItemRender, self.node_list.recharge_list)
        self.recharge_list:SetStartZeroIndex(true)
    end

    local data_list = GodOfWealthWGData.Instance:GetRMBBuyPerCfg()
    local has_data = not IsEmptyTable(data_list)

    if has_data then
        self.recharge_list:SetDataList(data_list)
    end

    self.node_list.not_recharge_list:SetActive(not has_data)
    self.node_list.recharge_list:SetActive(has_data)
end

function GodOfWealthView:OnClickAllReward()
    local buy_falg = GodOfWealthWGData.Instance:GetAllReChargeRmbBuyFlag()
    if buy_falg then
        GodOfWealthWGCtrl.Instance:SendAllGetReward()
    else
        --ViewManager.Instance:Open(GuideModuleName.YiJianZhaoCaiView)
		local current_rmb_bug_cfg = GodOfWealthWGData.Instance:GetCurrentRmbBuyCfg()
		local need_num, need_count = GodOfWealthWGData.Instance:GetAutoRenBuyNeed()
		if not IsEmptyTable(current_rmb_bug_cfg) and need_num > 0 then
			RechargeWGCtrl.Instance:Recharge(need_num, current_rmb_bug_cfg.rmb_type, 100 + need_count)
		end
    end
end

--[[
function GodOfWealthView:OnClickRuleTip()
    RuleTip.Instance:SetContent(Language.GodOfWealth.TipsContent, Language.GodOfWealth.TipsTitle)
end
]]

function GodOfWealthView:OnClickRecordsTip()
    GodOfWealthWGCtrl.Instance:OpenRecordsTipView()
end

function GodOfWealthView:OpenGaiLvView()
    local info = GodOfWealthWGData.Instance:GetGaiLvInfo()
    TipWGCtrl.Instance:OpenTipsRewardProView(info)
end

function GodOfWealthView:OnClickBtnTips()
    RuleTip.Instance:SetContent(Language.GodOfWealth.RewardPoolRuleContent, Language.GodOfWealth.RewardPoolRuleTitle)
end

----------------------------------------------GodOfWealthRecordItemRender------------------------------------------------------
GodOfWealthRecordItemRender = GodOfWealthRecordItemRender or BaseClass(BaseRender)

function GodOfWealthRecordItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.contant.text.text = string.format(Language.GodOfWealth.RankMsgInfo, Language.Common.UpNum[self.data.rank], self.data.name, self.data.gold)
end

----------------------------------------------GodOfWealthRecordItemRender2------------------------------------------------------
GodOfWealthRecordItemRender2 = GodOfWealthRecordItemRender2 or BaseClass(BaseRender)

function GodOfWealthRecordItemRender2:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.contant.text.text = string.format(Language.GodOfWealth.RankMsgInfo2, Language.Common.UpNum[self.data.rank], self.data.name, self.data.gold)
end

GodOfWealthRechargeItemRender = GodOfWealthRechargeItemRender or BaseClass(BaseRender)
function GodOfWealthRechargeItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind1(self.OnClickBuyBtn, self))
end

function GodOfWealthRechargeItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.power_text.text.text = string.format(Language.GodOfWealth.RechargeMultiple, self.data.multiple)
    self.node_list.price.text.text = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    self.node_list.xianyu_num.text.text = self.data.get_gold
    local buy_flag = GodOfWealthWGData.Instance:GetWealthGodBugPerFlag(self.data.seq)
    XUI.SetGraphicGrey(self.node_list.btn_buy, buy_flag)
    self.node_list.btn_buy_text.text.text = buy_flag and Language.GodOfWealth.SoldOut or Language.GodOfWealth.BuyNow
end

function GodOfWealthRechargeItemRender:OnClickBuyBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local buy_flag = GodOfWealthWGData.Instance:GetWealthGodBugPerFlag(self.data.seq)

    if not buy_flag then
        RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.GodOfWealth.SoldOut)
    end
end

--------------------------------RechargeTypeListItemRender------------------------------------------
RechargeTypeListItemRender = RechargeTypeListItemRender or BaseClass(BaseRender)

function RechargeTypeListItemRender:LoadCallBack()
    if not self.recharge_reward_list then
        self.recharge_reward_list = AsyncListView.New(ItemCell, self.node_list.recharge_reward_list)
        self.recharge_reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.bug_btn, BindTool.Bind(self.OnClickBugBtn, self))
end

function RechargeTypeListItemRender:__delete()
    if self.recharge_reward_list then
        self.recharge_reward_list:DeleteMe()
        self.recharge_reward_list = nil
    end
end

function RechargeTypeListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local data = self.data
    local cfg = data.cfg
    local reward_data = cfg and cfg.reward_item or {}
    self.recharge_reward_list:SetDataList(reward_data)
    self.node_list.bug_btn:CustomSetActive(not data.bug_flag)
    self.node_list.has_get_flag:CustomSetActive(data.bug_flag)
    self.node_list.effect_dianji:SetActive(cfg.is_free == 1)
    self.node_list.desc_bg:SetActive(cfg.is_calc_pool == 1)
    if not data.bug_flag then
        if cfg.is_free == 1 then
            self.node_list.btn_text.text.text = Language.GodOfWealth.Free
			self.node_list.img_free_red:SetActive(true)
        else
			local need_rmb = cfg.price
			local rmb_type = cfg.rmb_type
			local rmb_seq = cfg.rmb_seq
			local price_str = RoleWGData.GetPayMoneyStr(need_rmb, rmb_type, rmb_seq)
            self.node_list.btn_text.text.text = price_str
			self.node_list.img_free_red:SetActive(false)
		end
        --self.node_list.free_flag:CustomSetActive(cfg.is_free == 1)
        XUI.SetButtonEnabled(self.node_list.bug_btn, data.can_buy)
    end
end

function RechargeTypeListItemRender:OnClickBugBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local data = self.data
    local cfg = data.cfg

    if not IsEmptyTable(cfg) and cfg.price >= 0 then
        if cfg.is_free == 1 then
            GodOfWealthWGCtrl.Instance:SendOperaReq(WEALTH_GOD_OPERATE_TYPE.FETCH_FREE_RMB_BUY, cfg.seq)
        else
            RechargeWGCtrl.Instance:Recharge(cfg.price, cfg.rmb_type, cfg.seq)
        end
    end
end

--------------------------------
-- 抽奖倍数Item
--------------------------------
GODDrawMulItem = GODDrawMulItem or BaseClass(BaseRender)

function GODDrawMulItem:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset = ResPath.GetGodOfWealthImg("a3_zcjb_icon_ly" .. self.index)
    self.node_list["img_lingyu"].image:LoadSprite(bundle, asset)
    self.node_list["txt_lingyu_mul"].text.text = string.format(Language.GodOfWealth.Multy, self.data.beishu)
end

function GODDrawMulItem:SetMask(is_show_mask)
    self.node_list["mask"]:SetActive(is_show_mask)
end

function GODDrawMulItem:SetHLImage(is_hl)
    self.node_list["bg_hl"]:SetActive(is_hl)
    self.node_list["mask"]:SetActive(not is_hl)
end