FairyLandEquipmentNoticeView = FairyLandEquipmentNoticeView or BaseClass(SafeBaseView)
function FairyLandEquipmentNoticeView:__init()
	self:SetMaskBg(true, true)

	local bundle = "uis/view/fairy_land_equipment_ui_prefab"
	self:AddViewResource(0, bundle, "layout_fairy_land_equipment_notice")
end

function FairyLandEquipmentNoticeView:OpenCallBack()
	FairyLandEquipmentWGData.Instance:SetFairyLandEquipNoticeRemind()
end

function FairyLandEquipmentNoticeView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["close_btn"], BindTool.Bind1(self.Close, self))
	XUI.AddClickEventListener(self.node_list["goto_btn"], BindTool.Bind1(self.OnClickOpenFLEViewBtn, self))

	if not self.show_reward_list then
		self.show_reward_list = AsyncListView.New(ItemCell, self.node_list["item_list"])
	end

	if not self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["model_root"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.role_model:SetRenderTexUI3DModel(display_data)

		-- self.role_model:SetUI3DModel(self.node_list["model_root"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
	end
end

function FairyLandEquipmentNoticeView:ReleaseCallBack()
	if self.show_reward_list then
		self.show_reward_list:DeleteMe()
		self.show_reward_list = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end
end

function FairyLandEquipmentNoticeView:OnFlush()
	local temp_data = FairyLandEquipmentWGData.Instance:GetOtherCfgByKey("fakeitem")
	if not IsEmptyTable(temp_data) then
		self.show_reward_list:SetDataList(SortTableKey(temp_data))
	end

	local fun_cfg = FunOpen.Instance:GetFunByName(FunName.FairyLandEquipmentView)
	self.node_list["open_fun_desc"].text.text = RoleWGData.Instance:TransToDianFengLevelStr(fun_cfg and
	fun_cfg.trigger_param or 550)

	--设置模型
	local model_id = FairyLandEquipmentWGData.Instance:GetOtherCfgByKey("notice_model") or 0
	local role_sex = RoleWGData.Instance:GetRoleSex()
	local model_scale = role_sex == GameEnum.FEMALE and 1 or 0.8
	self.node_list["model_root"].transform.localScale = Vector3(model_scale, model_scale, model_scale)
	self.role_model:SetMainAsset(ResPath.GetFairyLandGodBodyModel(model_id, role_sex))
end

function FairyLandEquipmentNoticeView:OnClickOpenFLEViewBtn()
	ViewManager.Instance:Open(GuideModuleName.FairyLandEquipmentView)
end
