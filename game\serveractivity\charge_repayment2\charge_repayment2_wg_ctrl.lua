-- require("game/serveractivity/charge_repayment2/charge_repayment2_view")
require("game/serveractivity/charge_repayment2/charge_repayment2_wg_data")

ChargeRepayment2WGCtrl = ChargeRepayment2WGCtrl or BaseClass(BaseWGCtrl)

function ChargeRepayment2WGCtrl:__init()
	if ChargeRepayment2WGCtrl.Instance then
		ErrorLog("[ChargeRepayment2WGCtrl] attempt to create singleton twice!")
		return
	end
	ChargeRepayment2WGCtrl.Instance = self

	-- self.view = ChargeRepayment2.New()
	self.data = ChargeRepayment2WGData.New()

	self:RegisterAllProtocols()

	-- Remind.Instance:RegisterOneRemind(RemindId.charge_repayment2, BindTool.Bind1(self.CheckChargeReward2, self))
end

function ChargeRepayment2WGCtrl:__delete()
	-- if nil ~= self.view then
	-- 	self.view:DeleteMe()
	-- 	self.view = nil
	-- end
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	ChargeRepayment2WGCtrl.Instance = nil
end

function ChargeRepayment2WGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCChargeReward2Info, "OnChargeReward2Info")
	self:BindGlobalEvent(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind1(self.OnLoadingComplete, self))
end

function ChargeRepayment2WGCtrl:OnLoadingComplete()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2) then
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2,
			opera_type = RA_CHARGE_REPAYMENT2_OPERA_TYPE.RA_CHARGE_REPAYMENT2_OPERA_TYPE_QUERY_INFO})
	end
end

function ChargeRepayment2WGCtrl:Open()
	-- self.view:Open()
end

function ChargeRepayment2WGCtrl:OnChargeReward2Info(protocol)
	self.data:SetPayMent2Info(protocol)
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
end

function ChargeRepayment2WGCtrl:CheckChargeReward2()
	return self.data:GetChargeReward2Num()
end
