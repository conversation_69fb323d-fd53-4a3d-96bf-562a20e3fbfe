FairyLandGodBookActView = FairyLandGodBookActView or BaseClass(SafeBaseView)
function FairyLandGodBookActView:__init()
    self.view_name = "FairyLandGodBookActView"
	self:SetMaskBg(false)
    self:AddViewResource(0, "uis/view/fairy_land_equipment_ui_prefab", "layout_god_book_act_view")
end

function FairyLandGodBookActView:__delete()

end

function FairyLandGodBookActView:ReleaseCallBack()
    self:CleanDissolveTweenCD()
    self:CancelStoryTime()
    self.slot = nil
    self.page = nil
    self.img_material = nil
end

function FairyLandGodBookActView:LoadCallBack()
    self.img_material = self.node_list.page.raw_image.material
    self.img_material:SetFloat("_DissolveProgress", 1)
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))
end

function FairyLandGodBookActView:SetDataAndOpen(slot, page, only_look)
    self.slot = slot
    self.page = page
    self.only_look = only_look
	self:Open()
end

function FairyLandGodBookActView:CloseCallBack()
    if self.page == nil or self.only_look then
        return
    end

    local max_page = FairyLandEquipmentWGData.Instance:GetActBodyNeedNum()
    if self.page == max_page - 1 then
        FairyLandEquipmentWGCtrl.Instance:OpenGodBodyActView(self.slot)
    else
        FairyLandEquipmentWGCtrl.Instance:FlushEquipMentView(nil, "jump_book")
    end
end

function FairyLandGodBookActView:GetBookData()
    return FairyLandEquipmentWGData.Instance:GetGodBookActAttrCfg(self.slot, self.page)
end

function FairyLandGodBookActView:OnFlush()
    if self.page == nil then
        return
    end

    self:InitTween()
    self.node_list.title.image.enabled = false
    local bundle, asset = ResPath.GetFairyLandEquipImages("page_name_" .. self.page)
    self.node_list.title.image:LoadSprite(bundle, asset, function()
        self.node_list.title.image:SetNativeSize()
        self.node_list.title.image.enabled = true
    end)

    self.node_list.page.raw_image.enabled = false
    bundle, asset = ResPath.GetF2RawImagesPNG("god_book_page" .. self.page)
    self.node_list.page.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.page.raw_image:SetNativeSize()
        self.node_list.page.raw_image.enabled = true
    end)

    self:DoTween()
end

function FairyLandGodBookActView:InitTween()
    RectTransform.SetSizeDeltaXY(self.node_list.tween_node.rect, 280, 568)
    self.node_list.story_text1.text.text = ""
    self.node_list.story_text2.text.text = ""
    self.node_list.story_text3.text.text = ""
    self.img_material:SetFloat("_GreyProgress", 1)
    self.img_material:SetFloat("_DissolveProgress", 1)
    self.node_list.close_remind:SetActive(false)
    self.node_list.btn_close:SetActive(false)
end

function FairyLandGodBookActView:DoTween()
    local move_time = 0.6
    self.node_list.tween_node.rect:DOSizeDelta(Vector2(920, 568), move_time)
    ReDelayCall(self, function()
        self:PlayDissolve()
        self:PlayStory()
    end, move_time, "FairyLandGodBookActView")
end

function FairyLandGodBookActView:CleanDissolveTweenCD()
    if CountDown.Instance:HasCountDown(self.dissolve_tween_quest) then
        CountDown.Instance:RemoveCountDown(self.dissolve_tween_quest)
        self.dissolve_tween_quest = nil
    end
end

function FairyLandGodBookActView:PlayDissolve()
    self:CleanDissolveTweenCD()
    local total_time = 1
    local interval = 0.02
    self.dissolve_tween_quest = CountDown.Instance:AddCountDown(total_time, interval,
                            function(elapse_time, total_time)
                                self:UpdateDissolve(1 - elapse_time / total_time)
                            end,
                            function()
                                self:UpdateDissolve(-0.1)
                                self.node_list.close_remind:SetActive(true)
                                self.node_list.btn_close:SetActive(true)
                            end)
end

function FairyLandGodBookActView:UpdateDissolve(progress)
    if self.img_material then
        self.img_material:SetFloat("_DissolveProgress", progress)
    end
end

function FairyLandGodBookActView:CancelStoryTime()
    if self.play_story_time then
        GlobalTimerQuest:CancelQuest(self.play_story_time)
        self.play_story_time = nil
    end
end

function FairyLandGodBookActView:PlayStory()
    self:CancelStoryTime()
    local book_act_data = self:GetBookData()
    if book_act_data == nil then
        return
    end

    local story_content = book_act_data.tes
    local story_len, story_content_list = StringLen(story_content)
    self.story_content_list = story_content_list
    if story_len == 0 then
        return
    end

    self.story_index = 0
    self.play_story_time = GlobalTimerQuest:AddTimesTimer(BindTool.Bind(self.UpdateStory, self), 0.04, story_len)
end

local one_story_text_len = 16
function FairyLandGodBookActView:UpdateStory()
    if IsEmptyTable(self.story_content_list) then
        return
    end

    self.story_index = self.story_index + 1
    local str1, str2, str3 = "", "", ""
    if self.story_index <= one_story_text_len then
        str1 = table.concat(self.story_content_list, nil, 1, self.story_index)
    elseif self.story_index <= one_story_text_len * 2 then
        str1 = table.concat(self.story_content_list, nil, 1, one_story_text_len)
        str2 = table.concat(self.story_content_list, nil, one_story_text_len + 1, self.story_index)
    elseif self.story_index <= one_story_text_len * 3 then
        str1 = table.concat(self.story_content_list, nil, 1, one_story_text_len)
        str2 = table.concat(self.story_content_list, nil, one_story_text_len + 1, one_story_text_len * 2)
        str3 = table.concat(self.story_content_list, nil, one_story_text_len * 2 + 1, self.story_index)
    else
        str1 = table.concat(self.story_content_list, nil, 1, one_story_text_len)
        str2 = table.concat(self.story_content_list, nil, one_story_text_len + 1, one_story_text_len * 2)
        str3 = table.concat(self.story_content_list, nil, one_story_text_len * 2 + 1, one_story_text_len * 3)
    end

    if self.node_list.story_text1 then
        self.node_list.story_text1.text.text = str1
    end

    if self.node_list.story_text2 then
        self.node_list.story_text2.text.text = str2
    end

    if self.node_list.story_text3 then
        self.node_list.story_text3.text.text = str3
    end
end
