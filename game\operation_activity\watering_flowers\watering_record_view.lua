WateringRecordView = WateringRecordView or BaseClass(SafeBaseView)

function WateringRecordView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(852, 410)})
	self:AddViewResource(0, "uis/view/operation_watering_flowers_prefab", "watering_record_view")
end

function WateringRecordView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.OpertionAcitvity.WateringFlowers.RecordTitle
	if not self.list_view then
		self.list_view = AsyncListView.New(WateringRecordRender, self.node_list.list_view)
	end

	self.interface_cfg = WateringFlowersWGData.Instance:GetInterfaceCfg()
end

function WateringRecordView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
end

function WateringRecordView:ShowIndexCallBack()
	-- body
end

function WateringRecordView:OnFlush()
	self.data = WateringFlowersWGData.Instance:GetWaterFlowerWaterLog()

	if not self.data or not self.data.record_list or IsEmptyTable(self.data.record_list) then
		return
	end

	table.sort(self.data.record_list, SortTools.KeyUpperSorters("timestamp"))

	self.list_view:SetDataList(self.data.record_list)
	self.node_list.layout_blank_tip:SetActive(#self.data.record_list <= 0)
end

--------------------------------------------------WateringRecordRender--------------------------------------------------------------

WateringRecordRender = WateringRecordRender or BaseClass(BaseRender)

function WateringRecordRender:__init()
	self.interface_cfg = WateringFlowersWGData.Instance:GetInterfaceCfg()
end

function WateringRecordRender:__delete()

end

function WateringRecordRender:OnFlush()
	if not self.data then
		return
	end

	local temp_time = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	local history_params = os.date("*t", self.data.timestamp)
	local time_text = ""
	if temp_time.day > history_params.day then
		time_text = string.format(Language.Common.XXMXXD, history_params.month, history_params.day)
	else
		local tiem = self.data.timestamp
		time_text = string.format(Language.Common.XXHXXM, history_params.hour, history_params.min)
	end

	self.node_list.time.text.text = TimeUtil.FormatYMDHMS(self.data.timestamp)

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local pot_cfg = WateringFlowersWGData.Instance:GetFlowerCfgByType(self.data.flower_seq)

	local str = ""
	if self.interface_cfg and pot_cfg then
		local str_list = WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_23")
		-- local str_list = self.interface_cfg.desc_text_23--Split(self.interface_cfg.desc_text_23, ",")
		if self.data.uid == role_id then
			--自己浇的水
			str = string.format(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_24"), str_list, str_list, pot_cfg.dec_ripe_time) 
			EmojiTextUtil.ParseRichText(self.node_list["emoji_desc"].tmp, str, 21, COLOR3B.RED)
		else
			--别人浇的水
			str = string.format(WateringFlowersWGData.Instance:GetInterfaceDesc("desc_text_22"), self.data.name, str_list, str_list, pot_cfg.dec_ripe_time, self.data.uid) 
			EmojiTextUtil.ParseRichText(self.node_list["emoji_desc"].tmp, str, 20)
		end
	end
end