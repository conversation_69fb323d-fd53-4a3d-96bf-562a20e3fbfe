BillionSubsidyShopCartView = BillionSubsidyShopCartView or BaseClass(SafeBaseView)

local SHOP_TYPE_HEIGHT = 50
local SHOP_ITEM_HEIGHT = 106

local STATE_TYPE = {
	NORMAL = 1,	  --普通模式(使用服务端数据)
	MANAGE = 2,   --管理模式(客户端维护数据，退出模式就清理数据)
}

local CUR_STATE = STATE_TYPE.NORMAL


function BillionSubsidyShopCartView:__init()
	self.is_async_load = false
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self.record_open_flag = {}

	local bundle = "uis/view/billion_subsidy_ui_prefab"
	self:AddViewResource(0, bundle, "layout_billion_subsidy_shop_cart_view")
end

function BillionSubsidyShopCartView:LoadCallBack()
	self.discount_dropdown_select = 0
	XUI.AddClickEventListener(self.node_list["btn_manage"], BindTool.Bind(self.OnClickManage, self))
	XUI.AddClickEventListener(self.node_list["btn_exit_manage"], BindTool.Bind(self.OnClickExitManage, self))
	XUI.AddClickEventListener(self.node_list["btn_all"], BindTool.Bind(self.OnClickAll, self))
	XUI.AddClickEventListener(self.node_list["btn_delete"], BindTool.Bind(self.OnClickDelete, self))
	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuy, self))
	XUI.AddClickEventListener(self.node_list["btn_clear_all"], BindTool.Bind(self.OnClickClearAll, self))
	XUI.AddClickEventListener(self.node_list["btn_goto"], BindTool.Bind(self.OnClickGoto, self))
	

	self.shop_cart_list = AsyncListView.New(ShopCartItem, self.node_list.shop_cart_list)
	self.shop_cart_list:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize, self))

	-- 满减劵下拉框
	self.node_list["dropdown_discount"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnDiscountDropdownChange, self))

end

function BillionSubsidyShopCartView:SetPlaneIndex(index)
    self.plane_index = index
end

function BillionSubsidyShopCartView:ReleaseCallBack()

end



function BillionSubsidyShopCartView:__delete()

end

function BillionSubsidyShopCartView:OpenCallBack()

end

function BillionSubsidyShopCartView:CloseCallBack()
	BillionSubsidyWGData.Instance:ClearInputFlag()
	self.discount_dropdown_select = 0
	self:OnClickExitManage()
end

function BillionSubsidyShopCartView:OnFlush()

	-- 状态显示
	if CUR_STATE == STATE_TYPE.NORMAL then
		self.node_list.btn_group:SetActive(true)
		self.node_list.manage_btn_group:SetActive(false)
		self.node_list.buttom_btn_group:SetActive(true)
		self.node_list.buttom_manage_btn_group:SetActive(false)
	else
		self.node_list.btn_group:SetActive(false)
		self.node_list.manage_btn_group:SetActive(true)
		self.node_list.buttom_btn_group:SetActive(false)
		self.node_list.buttom_manage_btn_group:SetActive(true)
	end

	local limit_time = BillionSubsidyWGData.Instance:IsInLimitTime()
	if limit_time then
		self.node_list.empty_tips_desc.text.text = Language.BillionSubsidy.LimitTime
		self.node_list.common_empty_tips_yellow.rect.sizeDelta = Vector2(520, 80)
		--
	else
		--304
		self.node_list.empty_tips_desc.text.text = Language.BillionSubsidy.EmptyCart
		self.node_list.common_empty_tips_yellow.rect.sizeDelta = Vector2(304, 80)

	end
	

	local benefits_cfg_list = BillionSubsidyWGData.Instance:GetMenberBenefitsList()
	local vip_levl = BillionSubsidyWGData.Instance:GetMemberLevel()
	self.node_list.btn_goto:SetActive(vip_levl < #benefits_cfg_list) 

	local is_empty_shop_cart = BillionSubsidyWGData.Instance:IsEmptyShopCart()
	local rmb_price, gold_price = 0,0
	-- 空数据
	if is_empty_shop_cart or limit_time then
		self.node_list.common_empty_tips_yellow:SetActive(true)
		self.node_list.shop_cart_list:SetActive(false)


		self:FlushDiscount()
		-- self:FlushRmbShow()
		self.node_list.text_rmb_price.text.text = "￥"..rmb_price
		if gold_price == 0 then
			self.node_list.text_gold_price.text.text = ""
		else
			self.node_list.text_gold_price.text.text = "<sprite name=\"221\">"..gold_price
		end
		return
	end
	self.node_list.common_empty_tips_yellow:SetActive(false)
	self.node_list.shop_cart_list:SetActive(true)

	-- 有数据
	local shop_data_list = BillionSubsidyWGData.Instance:GetShopData()

	self.all_shop_data_list = {}
	local show_count = 0
	for k, v in pairs(shop_data_list) do
		local num, data_list = BillionSubsidyWGData.Instance:GetShopNumInCartByType(k)
		if num > 0 then
			show_count = show_count + 1
			-- 第一个数据为类型信息
			-- type == -1时为类型信息
			-- seq 记录代表的类型
			local type_data = {}
			type_data.shop_type = k
			type_data.item_seq = -1
			table.insert(self.all_shop_data_list,type_data)

			if BillionSubsidyWGData.Instance:GetShopCartOpenFlag(k) then
				for i, item_data in ipairs(data_list) do
					table.insert(self.all_shop_data_list,item_data)
				end
			end
		end
	end
	self.shop_cart_list:SetDataList(self.all_shop_data_list)

	self:FlushDiscount()
	self:FlushRmbShow()

end

-- 刷新价格
function BillionSubsidyShopCartView:FlushRmbShow()
	local rmb_price, gold_price = BillionSubsidyWGData.Instance:CounteShopCartPrice()
	local end_rmb_price = rmb_price
	if self.discount_dropdown_select > 0 then
		-- local show_index =  BillionSubsidyWGCtrl.Instance:GetCurShowIndex()
		-- local dc_all_data_list = BillionSubsidyWGData.Instance:GetDiscountCouponAllDataList(show_index)

		local dc_all_data = self.filter_data_list[self.discount_dropdown_select]
		if dc_all_data and end_rmb_price >= dc_all_data.quota_limit then
			end_rmb_price = end_rmb_price - dc_all_data.reduce_quota
		end
	end
	if end_rmb_price < 0 then
		end_rmb_price = 0
	end
	if end_rmb_price == rmb_price then
		self.node_list.text_rmb_price.text.text = "￥"..rmb_price
		self.end_rmb_price = end_rmb_price
	else
		self.node_list.text_rmb_price.text.text = string.format("￥%s <color=#5d3c2b><s>￥%s</s></color>",end_rmb_price, rmb_price)
		self.end_rmb_price = end_rmb_price
		
	end

	if gold_price == 0 then
		self.node_list.text_gold_price.text.text = ""
	else
		self.node_list.text_gold_price.text.text = "<sprite name=\"221\">"..gold_price
	end
end

-- 刷新满减券下拉框
function BillionSubsidyShopCartView:FlushDiscount()

	local is_in_limit_time = BillionSubsidyWGData.Instance:IsInLimitTime()
	if is_in_limit_time then
		self.discount_dropdown_select = 0
		self.node_list.dropdown_discount.dropdown:SetValueWithoutNotify(self.discount_dropdown_select)
		self:OnDiscountDropdownChange(self.discount_dropdown_select )
		return 
	end
	local is_has_use_no_limit_times = BillionSubsidyWGData.Instance:GetHasUseNoLimitDiscountTicketChance()
	local is_has_use_limit__times = BillionSubsidyWGData.Instance:GetHasUseLimitDiscountTicketChance()
	
	local rmb_price, gold_price = BillionSubsidyWGData.Instance:CounteShopCartPrice()

    local dc_all_data_list = BillionSubsidyWGData.Instance:GetDiscountCouponAllDataList()

	self.filter_data_list = {}
	local list_string = System.Collections.Generic.List_string.New()
	list_string:Add(Language.BillionSubsidy.NotUse)
	for i, data in ipairs(dc_all_data_list) do
		if data.type ~= TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then

			if data.quota_limit == 0 then
				local name = string.format(Language.BillionSubsidy.DirectDCSelectDesc,data.reduce_quota)
				
				if data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON and not is_has_use_no_limit_times then
					list_string:Add("<sprite name=\"303\">"..name)
				else
					list_string:Add(name)
				end
				table.insert(self.filter_data_list,data)
			else
				if rmb_price >= data.quota_limit then
					local name = string.format(Language.BillionSubsidy.FullDCSelectDesc,data.quota_limit,data.reduce_quota)
					if data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON and not is_has_use_limit__times then
						list_string:Add("<sprite name=\"303\">"..name)
					else
						list_string:Add(name)
					end
					table.insert(self.filter_data_list,data)
				end
			end
		end

	end

	self.node_list.dropdown_discount.dropdown:ClearOptions()
	self.node_list.dropdown_discount.dropdown:AddOptions(list_string)

	-- 设置初始化折扣

	if not is_has_use_no_limit_times and not is_has_use_limit__times then
		self.discount_dropdown_select = 0
	end
	-- 如果是0或者超过了范围就重新设置
	if (#self.filter_data_list > 0 and self.discount_dropdown_select == 0) or 
	(self.discount_dropdown_select and self.discount_dropdown_select > #self.filter_data_list) then
		self.discount_dropdown_select = 0
		for i, v in ipairs(self.filter_data_list) do
			-- 超过0就说明选到了，后面就不判断了
			if self.discount_dropdown_select == 0 then
				if v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON and is_has_use_limit__times then
					self.discount_dropdown_select = i
				elseif v.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON and is_has_use_no_limit_times then
					self.discount_dropdown_select = i
				end
			end

		end
	end



	-- print_error("self.discount_dropdown_select", self.discount_dropdown_select)
	-- print_error("default_select", default_select)
	self.node_list.dropdown_discount.dropdown:SetValueWithoutNotify(self.discount_dropdown_select)
	self:OnDiscountDropdownChange(self.discount_dropdown_select )
end

-- 品质筛选下拉框更变
function BillionSubsidyShopCartView:OnDiscountDropdownChange(index)



	local is_has_use_no_limit_times = BillionSubsidyWGData.Instance:GetHasUseNoLimitDiscountTicketChance()
	local is_has_use_limit__times = BillionSubsidyWGData.Instance:GetHasUseLimitDiscountTicketChance()

	local is_reset = false
	local dc_all_data = self.filter_data_list[index]
	if dc_all_data then
		if dc_all_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON and not is_has_use_no_limit_times then
			is_reset = true
		elseif dc_all_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON and not is_has_use_limit__times then
			is_reset = true
		end
	end

	if is_reset then
		self.node_list.dropdown_discount.dropdown:SetValueWithoutNotify(self.discount_dropdown_select)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.UseDiscountTimes)
		local benefits_cfg_list = BillionSubsidyWGData.Instance:GetMenberBenefitsList()
		local vip_levl = BillionSubsidyWGData.Instance:GetMemberLevel()
		if vip_levl < #benefits_cfg_list then
			TipWGCtrl.Instance:OpenAlertTips(Language.BillionSubsidy.UseDiscountTimesDesc, function ()
				BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
			end)
			
		end
	else
		self.discount_dropdown_select = index
		--print_error("self.discount_dropdown_select===",self.discount_dropdown_select)
		self:FlushRmbShow()
	end
	-- 
    -- local fc_num = BillionSubsidyWGData.Instance:GetUsedFreeTicketNum()
    -- local dc_num = BillionSubsidyWGData.Instance:GetUsedDiscountTicketNum()

    -- local have_fc_use_num = member_level_cfg.free_ticket_daily_use_limit - fc_num
    -- local have_dc_use_num = member_level_cfg.no_limit_discount_daily_use_limit - dc_num
	-- if self.data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON then
	-- 	self.node_list.limit_use_text.text.text = string.format(Language.BillionSubsidy.DCQuotaLimit2, have_dc_use_num)
	-- else
end

function BillionSubsidyShopCartView:OnClickOpenRuleViewBtn()

end

function BillionSubsidyShopCartView:OnClickCheckBtn()

end

-- 管理模式
function BillionSubsidyShopCartView:OnClickManage()
	CUR_STATE = STATE_TYPE.MANAGE
	BillionSubsidyWGCtrl.Instance:FlushShopCart()
end

-- 退出管理模式
function BillionSubsidyShopCartView:OnClickExitManage()
	BillionSubsidyWGData.Instance:ClearShopCartClearList()
	CUR_STATE = STATE_TYPE.NORMAL
	BillionSubsidyWGCtrl.Instance:FlushShopCart()
end

-- 全选
function BillionSubsidyShopCartView:OnClickAll()
	for i = 1, BILLION_SUBSIDY_SHOP_TYPE_MAX do
		BillionSubsidyWGCtrl.Instance:SetShopItemChoose(i, -1, 1)
	end
end

-- 清空购物车
function BillionSubsidyShopCartView:OnClickClearAll()
	BillionSubsidyWGCtrl.Instance:ClearAllShopCart()
end

-- 打开会员界面
function BillionSubsidyShopCartView:OnClickGoto()
	BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
end

-- 一键结算
function BillionSubsidyShopCartView:OnClickBuy()
	if BillionSubsidyWGData.Instance:IsInLimitTime() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.LimitTime)
		BillionSubsidyWGCtrl.Instance:FlushShopCart()
		return
	end
	if BillionSubsidyWGData.Instance:IsShopCartLock() then
		TipWGCtrl.Instance:OpenAlertTips(Language.BillionSubsidy.ShopCartIsLock,function()
			BillionSubsidyWGCtrl.Instance:ShopCartLock(0)
		end)
		return
	end
	if self.discount_dropdown_select > 0 and self.filter_data_list then
		local dc_all_data = self.filter_data_list[self.discount_dropdown_select]
		--0元走百亿补贴协议，不走直购
		if self.end_rmb_price == 0 and dc_all_data then
			-- print_error("直接发协议")
			local ticket_type = dc_all_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON and 1 or 2
			BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.USE_FREE_OR_DISCOUNT_TICKET_RMB_BUY, 
			GET_GOLD_REASON.GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY, 0, ticket_type, dc_all_data.data_seq, nil)
		else
			BillionSubsidyWGData.Instance:SetShopCartCurDiscountData(dc_all_data)
			BillionSubsidyWGCtrl.Instance:ShopCartLock(1)
		end

	else
		if self.end_rmb_price == 0 then
			BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.USE_FREE_OR_DISCOUNT_TICKET_RMB_BUY, 
			GET_GOLD_REASON.GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY, 0, 0, 0, nil)
		else
			BillionSubsidyWGData.Instance:SetShopCartCurDiscountData()
			BillionSubsidyWGCtrl.Instance:ShopCartLock(1)
		end
	end

	local rmb_price, gold_price = BillionSubsidyWGData.Instance:CounteShopCartPrice()
	local is_enough = RoleWGData.Instance:GetIsEnoughAllGold(gold_price)
    if not is_enough then
		VipWGCtrl.Instance:OpenTipNoGold()
    end
	
end

-- 删除
function BillionSubsidyShopCartView:OnClickDelete()
	local clear_list = BillionSubsidyWGData.Instance:GetShopCartClearList()
	for type, v in pairs(clear_list) do
		for item_seq = 0, BILLION_SUBSIDY_SHOP_TYPE_SEQ_MAX - 1 do
			if v[item_seq] == 1 then
				BillionSubsidyWGCtrl.Instance:RemoveItemToShopCart(type, item_seq, 128)
			end
		end
	end
end

function BillionSubsidyShopCartView:ChangeCellSize(data_index)
	if self.all_shop_data_list[data_index+1].item_seq == -1 then
		return SHOP_TYPE_HEIGHT

	else
		return SHOP_ITEM_HEIGHT
	end
end
-------------------------------------------------------------------------------------------

ShopCartItem = ShopCartItem or BaseClass(BaseRender)
function ShopCartItem:LoadCallBack()

	self.item_cell = ItemCell.New(self.node_list.item_root)
	XUI.AddClickEventListener(self.node_list.common_checkbox, BindTool.Bind(self.OnClickItem, self))
	XUI.AddClickEventListener(self.node_list.toggle_all, BindTool.Bind(self.OnClickAllItem, self))
	XUI.AddClickEventListener(self.node_list.type_root, BindTool.Bind(self.OnClickOpen, self))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list.btn_lose, BindTool.Bind(self.OnClickLose, self))
	XUI.AddClickEventListener(self.node_list.img_num, BindTool.Bind(self.OnClickShowInput, self))
	XUI.AddClickEventListener(self.node_list.btn_hide_input, BindTool.Bind(self.OnClickHideInput, self))
	
end

function ShopCartItem:__delete()
	self:ClearTimeLimit()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ShopCartItem:OnClickAdd()
	-- 买一付三不可调整数量
	if self.data.shop_type == BillionSubsidyWGData.ShopType.FYMS then
		return 
	end
	local num_in_cart = BillionSubsidyWGData.Instance:GetShopNumInCart(self.data.shop_type, self.data.item_seq)
	-- 协议char上限
	if num_in_cart >= 128 then
		return
	end
	local cfg = BillionSubsidyWGData.Instance:GetShopCfgByTypeAndSeq(self.data.shop_type, self.data.item_seq)

	local buy_limit = cfg and cfg.buy_limit or 0
	BillionSubsidyWGCtrl.Instance:AddItemToShopCart(self.data.shop_type, self.data.item_seq, 1, buy_limit, true)
end

function ShopCartItem:OnClickLose()
	local num_in_cart = BillionSubsidyWGData.Instance:GetShopNumInCart(self.data.shop_type, self.data.item_seq)
	if num_in_cart == 1 then
		return
	end

	BillionSubsidyWGCtrl.Instance:RemoveItemToShopCart(self.data.shop_type, self.data.item_seq, 1)
end

function ShopCartItem:OnClickShowInput()
	if self.data.shop_type ~= BillionSubsidyWGData.ShopType.FYMS then
		self.node_list.input_num:SetActive(true)
		self.node_list.img_num:SetActive(false)

		BillionSubsidyWGData.Instance:SetInputFlag(self.data.shop_type, self.data.item_seq, true)
	end

end

function ShopCartItem:OnClickHideInput()
	self.node_list.input_num:SetActive(false)
	self.node_list.img_num:SetActive(true)
	BillionSubsidyWGData.Instance:SetInputFlag(self.data.shop_type, self.data.item_seq, false)
end


function ShopCartItem:OnClickOpen(is_on)
	local is_open = BillionSubsidyWGData.Instance:GetShopCartOpenFlag(self.data.shop_type)
	if is_open ~= is_on then
		BillionSubsidyWGData.Instance:SetShopCartOpenFlag(self.data.shop_type, is_on)
		BillionSubsidyWGCtrl.Instance:FlushShopCart()
	end

end

function ShopCartItem:OnClickItem(is_on)
	if CUR_STATE == STATE_TYPE.NORMAL then
		local is_choose = BillionSubsidyWGData.Instance:GetShopItemChooseFlag(self.data.shop_type, self.data.item_seq)
		if is_on ~= is_choose then
			BillionSubsidyWGCtrl.Instance:SetShopItemChoose(self.data.shop_type, self.data.item_seq, is_on and 1 or 0)
		end
	elseif CUR_STATE == STATE_TYPE.MANAGE then
		local is_choose = BillionSubsidyWGData.Instance:IsNeedClear(self.data.shop_type, self.data.item_seq)
		if is_on ~= is_choose then
			if is_on then
				BillionSubsidyWGData.Instance:AddShopCartClearList(self.data.shop_type, self.data.item_seq)
			else
				BillionSubsidyWGData.Instance:RemoveShopCartClearList(self.data.shop_type, self.data.item_seq)
			end
			
			BillionSubsidyWGCtrl.Instance:FlushShopCart()
		end
	end
end

function ShopCartItem:OnClickAllItem(is_on)
	if CUR_STATE == STATE_TYPE.NORMAL then
		local is_all = BillionSubsidyWGData.Instance:GetShopItemChooseFlag(self.data.shop_type, self.data.item_seq)
		
		if is_on ~= is_all then
			BillionSubsidyWGCtrl.Instance:SetShopItemChoose(self.data.shop_type, self.data.item_seq, is_on and 1 or 0)
		end
		
	elseif CUR_STATE == STATE_TYPE.MANAGE then
		local is_all = BillionSubsidyWGData.Instance:IsNeedClear(self.data.shop_type, self.data.item_seq)
		if is_on ~= is_all then
			if is_on then
				BillionSubsidyWGData.Instance:AddShopCartClearList(self.data.shop_type, self.data.item_seq)
			else
				BillionSubsidyWGData.Instance:RemoveShopCartClearList(self.data.shop_type, self.data.item_seq)
			end
			BillionSubsidyWGCtrl.Instance:FlushShopCart()
		end
	end
end

function ShopCartItem:OnFlush()
	self:ClearTimeLimit()
	if not self.data then
		return
	end
	self.target_height = self.data.item_seq == -1 and SHOP_TYPE_HEIGHT or SHOP_ITEM_HEIGHT
	self:SetItemScale()

	if self.data.item_seq == -1 then
		self:FlushType()
	else
		self:FlushItem()
	end

end

function ShopCartItem:FlushType()
	local name = Language.BillionSubsidy.TabSub[self.data.shop_type]
	self.node_list.text_btn.text.text = name
	self.node_list.text_high_btn.text.text = name

	self.node_list.type_root:SetActive(true)
	self.node_list.data_root:SetActive(false)

	-- 勾选状态
	local is_on = BillionSubsidyWGData.Instance:GetShopCartOpenFlag(self.data.shop_type)
	self.node_list.type_root.toggle.isOn = is_on

	if CUR_STATE == STATE_TYPE.NORMAL then
		self.node_list.toggle_all.toggle.isOn = BillionSubsidyWGData.Instance:GetShopItemChooseFlag(self.data.shop_type, self.data.item_seq)
	elseif CUR_STATE == STATE_TYPE.MANAGE then
		self.node_list.toggle_all.toggle.isOn = BillionSubsidyWGData.Instance:IsNeedClear(self.data.shop_type, self.data.item_seq)
	end

end

function ShopCartItem:ClearTimeLimit()
	if self.timer_limit then
		GlobalTimerQuest:CancelQuest(self.timer_limit)
		self.timer_limit = nil
	end
end

function ShopCartItem:FlushItem()
	self.node_list.type_root:SetActive(false)
	self.node_list.data_root:SetActive(true)

	-- 配置
	local cfg = BillionSubsidyWGData.Instance:GetShopCfgByTypeAndSeq(self.data.shop_type, self.data.item_seq)
	local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.reward[0].item_id)

	self.item_cell:SetData({item_id = cfg.reward[0].item_id,num = cfg.reward[0].num})

	-- 物品数量
	local num_in_cart = BillionSubsidyWGData.Instance:GetShopNumInCart(self.data.shop_type, self.data.item_seq)
	self.node_list.text_num.text.text = "x"..num_in_cart
	-- 单个物品总价
	if self.data.shop_type == BillionSubsidyWGData.ShopType.XSSD then
		local start_timestamp = BillionSubsidyWGData.Instance:GetXDZKStartDiscountTimestamp()
		local end_time = start_timestamp + cfg.limit_time_discount_duration
		local cur_server_time = TimeWGCtrl.Instance:GetServerTime()
		local is_limit_time_discount = end_time > cur_server_time
		local show_price = is_limit_time_discount and cfg.limit_time_discount_price or cfg.price
		self.node_list.text_price.text.text = "￥"..show_price*num_in_cart

		self.timer_limit = GlobalTimerQuest:AddTimesTimer(function ()
			BillionSubsidyWGCtrl.Instance:FlushShopCart()
		end, end_time - cur_server_time, 1)
	elseif self.data.shop_type == BillionSubsidyWGData.ShopType.LYSD then
		local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
		local is_receive = member_level >= cfg.free_member_level
		if is_receive then
			self.node_list.text_price.text.text = "<sprite name=\"221\">0"
		else
			self.node_list.text_price.text.text = "<sprite name=\"221\">"..cfg.price*num_in_cart
		end
		
	else
		self.node_list.text_price.text.text = "￥"..cfg.price*num_in_cart
	end
	
	-- 物品名字
	self.node_list.text_name.text.text = item_cfg.name


	-- 勾选状态
	local is_choose
	if CUR_STATE == STATE_TYPE.NORMAL then
		is_choose = BillionSubsidyWGData.Instance:GetShopItemChooseFlag(self.data.shop_type, self.data.item_seq)
	elseif CUR_STATE == STATE_TYPE.MANAGE then
		is_choose = BillionSubsidyWGData.Instance:IsNeedClear(self.data.shop_type, self.data.item_seq)
	end
	-- 勾选状态
	self.node_list.common_checkbox.toggle.enabled = false
	if is_choose ~= self.node_list.common_checkbox.toggle then
		self.node_list.common_checkbox.toggle.isOn = is_choose
	end
	self.node_list.common_checkbox.toggle.enabled = true

	self.node_list.input_num:SetActive(false)
	self.node_list.img_num:SetActive(true)

	-- 物品数量
	self.node_list.text_num_2.text.text = num_in_cart
	
	if BillionSubsidyWGData.Instance:GetInputFlag(self.data.shop_type, self.data.item_seq) then
		self:OnClickShowInput()
	end
end

function ShopCartItem:SetItemScale()
	self:ClearTweener()
	local rect = self.node_list["rect_size"].rect
	self.size_change_tweener = rect:DOSizeDelta(Vector2(292, self.target_height), 0)
	self.size_change_tweener:OnUpdate(function()
		self.view.layout_element.minHeight = rect.sizeDelta.y
	end)
end

function ShopCartItem:ClearTweener()
    if self.size_change_tweener then
		self.size_change_tweener:Kill()
		self.size_change_timer = nil
    end
end
--------------------------------AccordionList--------------------------------------------
