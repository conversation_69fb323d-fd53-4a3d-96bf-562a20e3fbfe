TipsAutoOpenGiftView = TipsAutoOpenGiftView or BaseClass(SafeBaseView)

function TipsAutoOpenGiftView:__init(view_name)
	self.view_name = "TipsAutoOpenGiftView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
						{vector = Vector2(3, -8), sizeDelta = Vector2(740, 439)})
	self:AddViewResource(0, "uis/view/tips/autogift_prefab", "layout_auto_gift_view")
	self:SetMaskBg(true, true)
end

function TipsAutoOpenGiftView:OpenCallBack()
	self:InitParam()
end

function TipsAutoOpenGiftView:LoadCallBack()
	self:InitPanel()
	self:InitListener()
end

function TipsAutoOpenGiftView:ReleaseCallBack()
	if self.gift_cell_list then
		for _,v in pairs(self.gift_cell_list) do
			v:DeleteMe()
		end
		self.gift_cell_list = nil
	end
end

function TipsAutoOpenGiftView:InitParam()
	self.gift_list = {}
	self.max_value = 0
	self.need_value = 1
	self.select_index = 0
	self.target_data = nil
end

function TipsAutoOpenGiftView:InitPanel()
	self.node_list.title_view_name.text.text = Language.F2Tip.AutoOpenGiftViewTitle
	local cell_root = self.node_list.gift_list_root
	local cell_list = {}
	for i = 1, 10 do
		cell_list[i] = ItemCell.New(cell_root)
		cell_list[i]:SetIndex(i)
		cell_list[i]:SetTipClickCallBack(BindTool.Bind(self.OnClickGiftCell, self, i))
	end
	self.gift_cell_list = cell_list
end

function TipsAutoOpenGiftView:InitListener()
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.OnClickCancelBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickSureBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind(self.OnNumBtnAddOrSub, self, -1))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind(self.OnNumBtnAddOrSub, self, 1))
	self.node_list.num_slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderValueChange, self))
	self.node_list.num_slider.slider.minValue = 1
end

function TipsAutoOpenGiftView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "gift_info" then
			self.gift_list = v.gift_list
			self.target_data = v.target_data
			self.need_value = v.need_num or 1
		end
	end
	self:FlushGiftList()
	self:FlushTargetDesc()
end

function TipsAutoOpenGiftView:FlushGiftList()
	if IsEmptyTable(self.gift_list) then
		return
	end

	local gift_list = self.gift_list
	local cell_list = self.gift_cell_list
	for i = 1, #cell_list do
		local cell = cell_list[i]
		if cell then
			if gift_list[i] then
				cell:SetData(gift_list[i])
				cell:SetVisible(true)
			else
				cell:SetVisible(false)
			end
		end
	end

	self:OnClickGiftCell(1)
end

function TipsAutoOpenGiftView:FlushTargetDesc()
	if IsEmptyTable(self.target_data) then
		return
	end

	local data = self.target_data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	self.node_list.desc_label.text.text = string.format(Language.F2Tip.NeedItemDesc, item_name, self.need_value)
end

function TipsAutoOpenGiftView:OnClickCancelBtn()
	self:Close()
end

function TipsAutoOpenGiftView:OnClickSureBtn()
	local gift_data = self.gift_list and self.gift_list[self.select_index]
	if IsEmptyTable(gift_data) then
		return
	end

	local value = self.node_list.num_slider.slider.value
	local drop_list = ItemWGData.Instance:GetItemListInGift(gift_data.item_id)
	local target_id = self.target_data.item_id

	local gift_index = 1
	for i=1,#drop_list do
		if drop_list[i].item_id == target_id then
			gift_index = i
			break
		end
	end
	gift_index = gift_index - 1

	local select_item_index = {}
	for i = 0, GameEnum.SELECT_ITEM_MAX_NUM do
		select_item_index[i] = i == gift_index and 1 or 0
	end

	BagWGCtrl.Instance:SendUseItem(gift_data.index, value, 0, 0, select_item_index)

	self:Close()
end

function TipsAutoOpenGiftView:OnSliderValueChange(value)
	self.node_list.lbl_num.text.text = value
end

function TipsAutoOpenGiftView:OnNumBtnAddOrSub(num)
	local value = self.node_list.num_slider.slider.value
	value = value + num
	if value > 0 and value <= self.max_value then
		self.node_list.num_slider.slider.value = value
		self.node_list.lbl_num.text.text = value
	end
end

function TipsAutoOpenGiftView:OnClickGiftCell(index)
	if self.select_index == index then
		return true
	end
	self.select_index = index

	local cell_list = self.gift_cell_list
	for i = 1, #cell_list do
		cell_list[i]:SetSelectEffect(i == index)
	end

	local data = self.gift_list[index]
	self.max_value = data and data.num or 1

	local value = 1
	if self.max_value > self.need_value then
		value = self.need_value
	else
		value = self.max_value
	end

	self.node_list.num_slider.slider.maxValue = self.max_value
	self.node_list.num_slider.slider.value = value
	self.node_list.lbl_num.text.text = value

	return true
end