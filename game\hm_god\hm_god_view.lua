HmGodView = HmGodView or BaseClass(SafeBaseView)

function HmGodView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false)

	self:AddViewResource(0, "uis/view/hm_god_ui_prefab", "layout_hmgod_bg")
	self:AddViewResource(0, "uis/view/hm_god_ui_prefab", "layout_hmgod")
end

function HmGodView:__delete()

end

function HmGodView:OpenCallBack()
end

function HmGodView:CloseCallBack()
end

function HmGodView:LoadCallBack()
	if not self.part_type_cell_list then
		self.part_type_cell_list = {}
		for i = 1, 6 do
			self.part_type_cell_list[i] = HmGodPartTypeCell.New(self.node_list["suit_part_type_cell_" .. i])
			self.part_type_cell_list[i]:SetIndex(i)
		end
	end

	--加载模型时装
	if nil == self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.role_model:SetRenderTexUI3DModel(display_data)

		-- self.role_model:SetUI3DModel(self.node_list["ph_display"].transform,
		-- 	self.node_list["EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_model)
	end

	if not self.part_model_display then
		self.part_model_display = OperationActRender.New(self.node_list["part_display_model"])
		self.part_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE)
	end

	self.attr_item_list = {}

	XUI.AddClickEventListener(self.node_list["god_act_btn"], BindTool.Bind(self.OnClickGoAct, self))
	XUI.AddClickEventListener(self.node_list.btn_wear_all, BindTool.Bind(self.OnClickWearAll, self)) --激活套装属性button
	XUI.AddClickEventListener(self.node_list.skill_click, BindTool.Bind(self.OnClickSkill, self))
	XUI.AddClickEventListener(self.node_list.skill_click_1, BindTool.Bind(self.OnClickSkill, self))
	self:CreatToggleList()
end

function HmGodView:ReleaseCallBack()
	if self.part_type_cell_list then
		for k, v in pairs(self.part_type_cell_list) do
			v:DeleteMe()
		end
		self.part_type_cell_list = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if CountDownManager.Instance:HasCountDown("god_act_time") then
		CountDownManager.Instance:RemoveCountDown("god_act_time")
	end

	if self.part_model_display then
		self.part_model_display:DeleteMe()
		self.part_model_display = nil
	end

	if self.attr_item_list then
		for k, v in pairs(self.attr_item_list) do
			v.cell:DeleteMe()
		end
		self.attr_item_list = nil
	end

	self.accordion_list = nil
	self.load_cell_complete = nil
	self.force_big_type = nil
	self.flush_wait_flag = nil
	self.stop_cur = nil
	self.select_item_data = nil
	self.select_item = nil
	self.select_big_type = nil
	if self.suit_cell_group then
		for k, v in pairs(self.suit_cell_group) do
			for k1, v1 in pairs(v) do
				v1:DeleteMe()
			end
			self.suit_cell_group[k] = nil
		end
		self.suit_cell_group = nil
	end

	if self.big_type_toggle_list ~= nil then
		for k, v in ipairs(self.big_type_toggle_list) do
			v:DeleteMe()
		end
		self.big_type_toggle_list = nil
	end

	self:RemoveWeaponAnimTimer()
end

function HmGodView:ShowIndexCallBack()

end

function HmGodView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self.flush_wait_flag = true
			self:SelectToggle(v.force_big_type)
			self:FlushToggleAllData()
			self:FulshPanelView()
		elseif k == "protocol_change" then
			self.flush_wait_flag = true
			self:SelectToggle(nil, true)
			self:FlushToggleAllData()
			self:FulshPanelView(true)
		end
	end
end

function HmGodView:CreatToggleList()
	--print_error("---创建标签---")
	if nil ~= self.accordion_list then
		return
	end

	self.accordion_list = {}
	self.suit_cell_group = {}
	self.big_type_toggle_list = {}
	self.load_cell_complete = false
	local content_node = self.node_list["toggle_content"]
	local show_list = HmGodWGData.Instance:GetSuitShowList()
	local toggle_length = #show_list
	for i = 1, toggle_length do
		self.accordion_list[i] = content_node:FindObj("List" .. i)
		local big_btn_cell = HmGodBigTypeToggleRender.New(content_node:FindObj("SelectBtn" .. i))
		big_btn_cell:SetIndex(i)
		big_btn_cell:SetOnlyClickCallBack(BindTool.Bind(self.OnClickBigTypeToggle, self))
		self.big_type_toggle_list[i] = big_btn_cell
		local suit_length = show_list[i].part_list and 1 or 0 -- 每个大标签其实只有一套，具体物品数据在小格子里面（封装了三层render）
		self:LoadSuitCellList(i, toggle_length, suit_length)
	end
end

-- 加载子标签
function HmGodView:LoadSuitCellList(index, big_type_num, suit_num)
	--print_error("---加载子标签---", index)
	local res_async_loader = AllocResAsyncLoader(self, "hmgod_item" .. index)
	res_async_loader:Load("uis/view/hm_god_ui_prefab", "hm_god_toggle_cell", nil, function(new_obj)
		local item_vo_list = {}
		for i = 1, suit_num do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.accordion_list[index].transform, false)

			local item_render = HmGodSuitToggleRender.New(obj)
			item_render:SetOnlyClickCallBack(BindTool.Bind(self.OnClickSuitToggle, self))
			item_vo_list[i] = item_render
			if index == big_type_num and i == suit_num then
				self.load_cell_complete = true
			end
		end

		self.suit_cell_group[index] = item_vo_list
		if self.load_cell_complete then
			-- 设置数据
			self:FlushToggleAllData()

			-- 加载完 是否要选择标签
			if self.flush_wait_flag then
				self:SelectToggle()
			end
		end
	end)
end

-- 标签选择
function HmGodView:SelectToggle(force_big_type, stop_cur)
	--print_error("---标签选择---", force_big_type,stop_cur)
	if force_big_type then
		self.force_big_type = force_big_type
	end

	if not self.load_cell_complete then
		return
	else
		self.flush_wait_flag = nil
	end

	local jump_index
	if self.force_big_type then
		jump_index = self.force_big_type
		self.force_big_type = nil
	elseif stop_cur then
		self.stop_cur = true
		jump_index = self.select_big_type
	else
		local group_index, item_index = HmGodWGData.Instance:GetJumpHmGodRemindIndex()
		jump_index = group_index
	end

	jump_index = jump_index or 1
	if self.select_big_type ~= jump_index then
		self.big_type_toggle_list[jump_index]:SetAccordionElementState(true)
	else
		self:FulshPanelView(true)
		return
	end
end

-- 大标签回调
function HmGodView:OnClickBigTypeToggle(cell, is_on)
	--print_error("【----点击 大 回调-----】：",is_on,"======",cell:GetData())
	if cell == nil then
		return
	end

	local index = cell:GetIndex()
	local data = cell:GetData()
	if data == nil then
		return
	end

	self.select_big_type = index
	if nil ~= self.select_item then
		self.select_item:CellCancelSelect()
	end

	self.select_item = cell
	self.select_item:CellSelect()
	self:FulshShowView(data.part_list[0])
end

-- 子标签回调
function HmGodView:OnClickSuitToggle(cell)
	--print_error("【----点击 子 回调-----】：", cell:GetData())
	if cell == nil then
		return
	end

	local data = cell:GetData()
	if data == nil then
		return
	end

	if self.select_item_data == data then
		if data and data.show_item_id > 0 then
			TipWGCtrl.Instance:OpenItem({ item_id = data.show_item_id })
		end
		return
	end

	if nil ~= self.select_item then
		self.select_item:CellCancelSelect()
	end

	self.select_item = cell
	self.select_item:CellSelect()
	self:FulshShowView(data)
end

-- 刷新标签数据
function HmGodView:FlushToggleAllData()
	--print_error("---刷新标签数据---")
	if IsEmptyTable(self.big_type_toggle_list) then
		return
	end

	if IsEmptyTable(self.suit_cell_group) then
		return
	end

	local show_list_data = HmGodWGData.Instance:GetSuitShowList()
	for k, v in ipairs(self.big_type_toggle_list) do
		v:SetData(show_list_data[k])
		local suit_list = self.suit_cell_group[k]
		if not IsEmptyTable(suit_list) then
			for k1, v1 in ipairs(suit_list) do
				v1:SetData(show_list_data[k])
			end
		end
	end
end

function HmGodView:FulshShowBigBg(data)
	if data then
		local suit_info = HmGodWGData.Instance:GetHmSuitInfoBySuit(data.suit_seq)
		self.node_list.Img_suit_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a2_snsy_bg_" .. suit_info.badground))
	end
end

--刷新
function HmGodView:FulshShowView(select_data)
	--print_error("select_data",select_data)
	if select_data and self.select_item_data == select_data then
		return
	end

	self.select_item_data = select_data
	self:FulshShowBigBg(self.select_item_data)
	self.node_list.panel_view_1:SetActive(self.select_item_data.type == 0) -- 总套装面板
	self.node_list.panel_view_2:SetActive(self.select_item_data.type ~= 0) -- 没部位显示面板
	local theme_cfg = WardrobeWGData.Instance:GetThemeCfgBySuit(self.select_item_data.suit_seq)
	if theme_cfg and theme_cfg.skill_icon then
		self.node_list.suit_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(theme_cfg.skill_icon))
		self.node_list.suit_skill_icon_1.image:LoadSprite(ResPath.GetSkillIconById(theme_cfg.skill_icon))
	end

	self:FulshPanelView()
end

function HmGodView:FulshPanelView(no_fulsh_model)
	if not self.select_item_data then
		return
	end

	if self.select_item_data.type == 0 then
		self:FulshShowAllSuitView(no_fulsh_model)
	else
		self:FulshShowPartView(no_fulsh_model)
	end
end

function HmGodView:FulshShowAllSuitView(no_fulsh_model)
	if not self.select_item_data then
		return
	end

	local suit_info = HmGodWGData.Instance:GetHmSuitInfoBySuit(self.select_item_data.suit_seq)
	if suit_info then
		for i = 1, 6 do
			if suit_info.part_list[i] then
				self.part_type_cell_list[i]:SetData(suit_info.part_list[i])
				self.part_type_cell_list[i]:SetActive(true)
			else
				self.part_type_cell_list[i]:SetActive(false)
			end
		end

		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
		.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
		self.node_list.god_act_btn:SetActive(activity_info and activity_info.status == ACTIVITY_STATUS.OPEN)
		self.node_list.suit_name.text.text = suit_info.suit_name
		self:FlushCapStr(suit_info.part_list)
	end

	self:FlushActTimeCount()
	if not no_fulsh_model then
		self:FlushSuitModel()
	end
end

function HmGodView:FlushCapStr(info)
	local capability = 0
	for k, v in pairs(info) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.show_item_id)
		if item_cfg then
			capability = capability + ItemShowWGData.CalculateCapability(v.show_item_id)
		end
	end

	self.node_list.cap_value.text.text = capability
end

function HmGodView:FlushActTimeCount()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local time
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	else
		time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
	end

	if time > 0 then
		if CountDownManager.Instance:HasCountDown("god_act_time") then
			CountDownManager.Instance:RemoveCountDown("god_act_time")
		end

		CountDownManager.Instance:AddCountDown("god_act_time",
			BindTool.Bind(self.FinalUpdateTimeCallBack, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	else
		self:OnComplete()
	end
end

function HmGodView:FinalUpdateTimeCallBack(now_time, total_time)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list["act_time"].text.text = time_str
end

function HmGodView:OnComplete()
	self.node_list.act_time.text.text = ""
end

function HmGodView:FlushSuitModel()
	if not self.role_model and (not self.select_item_data) then
		return
	end

	local item_data = self.select_item_data
	local show_list = WardrobeWGData.Instance:GetActivationPartList(item_data.suit_seq)

	if IsEmptyTable(show_list) then
		return
	end

	--清理掉回调
	self.role_model:RemoveAllModel()

	self.is_foot_view = false
	self.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	self.mount_res_id = 0
	self.mount_action = ""
	local has_fashion_show = false

	local res_id, fashion_cfg
	for k, data in pairs(show_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				self.body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
				has_fashion_show = true
			end
		elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
			fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
			if fashion_cfg then
				self.mount_res_id = fashion_cfg.appe_image_id
				self.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					self.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
			fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
			if fashion_cfg then
				self.mount_res_id = fashion_cfg.active_id
				self.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					self.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end
			end
		end
	end


	local d_body_res, d_hair_res, d_face_res
	local main_role = not has_fashion_show and Scene.Instance:GetMainRole()
	local vo = main_role and main_role:GetVo()
	if not has_fashion_show and vo and vo.appearance then
		if vo.appearance.fashion_body == 0 then
			d_body_res = vo.appearance.default_body_res_id
			d_hair_res = vo.appearance.default_hair_res_id
			d_face_res = vo.appearance.default_face_res_id
		end
	end

	local extra_role_model_data = {
        d_face_res = d_face_res,
        d_hair_res = d_hair_res,
		d_body_res = d_body_res,
		no_need_do_anim = true,
    }
	if self.mount_res_id > 0 then
		self.role_model:SetRoleResid(self.body_res_id, nil, extra_role_model_data)
	else
		self.role_model:SetRoleResid(self.body_res_id, function()
			if self.mount_res_id == 0 then
				if self.show_role_idel_ani then
					self.role_model:PlayRoleShowAction()
					self.show_role_idel_ani = false
				else
					self.role_model:PlayIdleAni()
				end
			end
		end, extra_role_model_data)
	end

	if self.mount_res_id > 0 then
		self.role_model:SetMountResid(self.mount_res_id)
		self.role_model:PlayStartAction(self.mount_action)
	end

	for k, v in pairs(show_list) do
		self:ShowModelByData(v)
	end

	self:ChangeModelShowScale()
end

function HmGodView:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end

	local res_id, fashion_cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce

			if data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
				self.role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.role_model:SetWeaponResid(res_id)
			end
		end
	end
end

function HmGodView:ChangeModelShowScale()
	if not self.select_item_data then
		return
	end

	local item_data = self.select_item_data
	local data = WardrobeWGData.Instance:GetThemeCfgBySuit(item_data.suit_seq)
	if IsEmptyTable(data) then
		return
	end

	local pos_str = data.main_pos
	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ph_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	end

	local rotate_str = data.main_rot
	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		if self.role_model then
			self.role_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
		end
	end

	local scale = data.main_scale
	if scale and scale ~= "" then
		RectTransform.SetLocalScale(self.node_list.ph_display.rect, scale)
	end
end

function HmGodView:FulshShowPartView(no_fulsh_model)
	if not self.select_item_data then
		return
	end

	self:FlushPartRight()
	self:FlushAttrList()
	if not no_fulsh_model then
		self:FlushPartModel()
	end
end

function HmGodView:FlushPartModel()
	if not self.select_item_data then
		return
	end

	local item_data = self.select_item_data

	if item_data.show_item_id ~= 0 then
		local display_data = {}
		display_data.should_ani = true
		if item_data.show_item_id ~= 0 and item_data.show_item_id ~= "" then
			local split_list = string.split(item_data.show_item_id, "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					list[tonumber(v)] = true
				end
				display_data.model_item_id_list = list
			else
				display_data.item_id = item_data.show_item_id
			end
		end

		local model_show_type = 1
		display_data.render_type = model_show_type - 1
		self.part_model_display:SetData(display_data)
	end
end

function HmGodView:FlushPartRight()
	if not self.select_item_data then
		return
	end

	local item_data = self.select_item_data
	--self.node_list.wjh_img:SetActive(item_data.state == REWARD_STATE_TYPE.UNDONE)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.show_item_id)
	self.node_list.part_item_name.text.text = item_cfg.name

	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(item_data.suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end

	local act_btn = suit_data.act_part_num >= suit_data.total_part_num

	if suit_data.can_act then
		self.node_list["btn_wear_red"]:SetActive(true)
	else
		self.node_list["btn_wear_red"]:SetActive(false)
	end
	if act_btn then
		self.node_list["btn_wear_text"].text.text = Language.HmGosView.WearbtnText[1]
		XUI.SetButtonEnabled(self.node_list.btn_wear_all, true)
	else
		self.node_list["btn_wear_text"].text.text = Language.HmGosView.WearbtnText[2]
		if suit_data.can_act then
			XUI.SetButtonEnabled(self.node_list.btn_wear_all, true)
		else
			XUI.SetButtonEnabled(self.node_list.btn_wear_all, false)
		end
	end
end

--数据属性列表
function HmGodView:FlushAttrList()
	if not self.select_item_data then
		return
	end

	local item_data = self.select_item_data
	local attr_data = WardrobeWGData.Instance:GetAttrBySuit(item_data.suit_seq)

	for i, v in ipairs(attr_data) do
		if self.attr_item_list[i] then
			if self.attr_item_list[i].loaded_flag then
				self.attr_item_list[i].cell:SetData(v)
			end
		else
			local async_loader = AllocAsyncLoader(self, "hmgod_attr" .. i)
			self.attr_item_list[i] = {}
			self.attr_item_list[i].loaded_flag = false
			async_loader:SetParent(self.node_list["attr_list"].transform)
			async_loader:Load("uis/view/hm_god_ui_prefab", "hmgod_attr_cell", function(obj)
				local cell = HmGodAttrRender.New(obj)
				cell:SetData(v)
				self.attr_item_list[i].cell = cell
				self.attr_item_list[i].loaded_flag = true
			end)
		end
	end

	local active_num = #attr_data
	for i, v in ipairs(self.attr_item_list) do
		if v.loaded_flag then
			v.cell:SetActive(i <= active_num)
		end
	end
end

function HmGodView:OnClickGoAct()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		ViewManager.Instance:Open(GuideModuleName.GodPchaseView)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HmGosView.ActEnd)
	end
end

function HmGodView:OnClickWearAll()
	if not self.select_item_data then
		return
	end

	local item_data = self.select_item_data
	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(item_data.suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end
	local part_list = suit_data.part_list

	if suit_data.total_part_num == suit_data.act_part_num then --所有已激活
		for k, v in pairs(part_list) do
			if v.state == REWARD_STATE_TYPE.FINISH then
				self:SendReqByData(v)
			end
		end
		TipWGCtrl.Instance:ShowSystemMsg(Language.HmGosView.WearTips)
		local bundle_name, asset_name = ResPath.GetEffect(Ui_Effect.UI_huanhua)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.wear_effect_root.transform,
			nil, Vector3(0, -10, 0), Quaternion.Euler(-6, 0, 0))
	end

	if suit_data.can_act then --判断激活下一套装属性
		local need_num = suit_data.suit_less_need - suit_data.act_part_num
		for k, v in pairs(part_list) do
			if v.state == REWARD_STATE_TYPE.CAN_FETCH then
				WardrobeWGCtrl.Instance:SendWardrobeRequest(WARDROBE_OP_TYPE.WARDROBE_OPERATE_TYPE_ACTIVE_PART,
					item_data.suit_seq, v.part)
				need_num = need_num - 1
				if need_num == 0 then
					break
				end
			end
		end
	end
end

function HmGodView:SendReqByData(data)
	if IsEmptyTable(data) then
		return
	end

	local is_wearing = false
	local fashion_cfg, res_id, index_id
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			index_id = fashion_cfg.index
			is_wearing = WardrobeWGData.Instance:CheckIsSameRes(data.type, data.param1, res_id)
			if not is_wearing then
				if data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING, res_id, index_id)
				elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO, res_id, index_id)
				elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN, res_id, index_id)
				elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
					NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE,
						ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING, res_id, index_id)
				else
					NewAppearanceWGCtrl.Instance:OnUseFashion(data.param1, data.param2, 1)
				end
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			res_id = fashion_cfg.appe_image_id
			index_id = fashion_cfg.image_id
			is_wearing = WardrobeWGData.Instance:CheckIsSameRes(data.type, nil, res_id)
			if not is_wearing then
				NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, res_id, index_id)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			is_wearing = WardrobeWGData.Instance:CheckIsSameRes(data.type, nil, fashion_cfg.active_id)
			if not is_wearing then
				NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.USE_KUN, fashion_cfg.id)
			end
		end
	end
end

function HmGodView:DoActiveEffect(suit, part)
	if not self.select_item_data then
		return
	end

	local item_data = self.select_item_data
	if suit ~= item_data.suit_seq then
		return
	end

	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(suit)

	if suit_data and suit_data.suit_less_need == 0 or suit_data.suit_less_need > suit_data.act_part_num then
		self.node_list["effect_root"]:SetActive(true)
		TipWGCtrl.Instance:ShowEffect({
			effect_type = UIEffectName.s_jihuo,
			is_success = true,
			pos = Vector2(0, 0),
			parent_node = self.node_list["effect_root"]
		})
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

function HmGodView:OnClickSkill()
	if not self.select_item_data then
		return
	end

	local item_data = self.select_item_data
	HmGodWGCtrl.Instance:OpenHmGodSkillView(item_data.suit_seq)
end
