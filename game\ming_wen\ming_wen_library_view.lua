MingWenLibraryView = MingWenLibraryView or BaseClass(SafeBaseView)

function MingWenLibraryView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
end

function MingWenLibraryView:__delete()

end

function MingWenLibraryView:ReleaseCallBack()
	if self.posy_library_list then
        self.posy_library_list:DeleteMe()
        self.posy_library_list = nil
    end
end

-- 加载配置
function MingWenLibraryView:LoadConfig()
	local bundle_name = "uis/view/ming_wen_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_commmon_second_panel", { sizeDelta = Vector2(1082, 612) })
	self:AddViewResource(0,bundle_name,"layout_mingwen_library")
end

function MingWenLibraryView:Load<PERSON>allBack(index, loaded_times)
	self.node_list.title_view_name.text.text = Language.ViewName[GuideModuleName.MingWenLibraryView]

	local data_list = MingWenWGData.Instance:GetPosyLibraryAllData()

	if not self.posy_library_list then
        self.posy_library_list = AsyncBaseGrid.New()
        local bundle = "uis/view/ming_wen_ui_prefab"
		local asset = "mingwen_library_cell"
        self.posy_library_list:CreateCells({col = 5, change_cells_num = 1, list_view = self.node_list["posy_library_list"],
            assetBundle = bundle, assetName = asset, itemRender = MingWenLibraryCell})
        self.posy_library_list:SetStartZeroIndex(false)
    end

	self.posy_library_list:SetDataList(data_list)
end

function MingWenLibraryView:OnFlush(param_t)
	local once_chapter_count = 10
	local pass_level = MingWenWGData.Instance:GetPassLevel() + 1
	local chapter_level = math.ceil((pass_level) / once_chapter_count)
	local layer = pass_level % once_chapter_count
	if layer == 0 and pass_level >= once_chapter_count then
		--打到当前章节的最后一层
		layer = once_chapter_count
	end

	self.node_list.pass_level_text.text.text = string.format(Language.MingWenView.PassLevel, chapter_level, layer)
end

MingWenLibraryCell = MingWenLibraryCell or BaseClass(BaseRender)

function MingWenLibraryCell:__init()

end

function MingWenLibraryCell:__delete()
	if self.mingwen_cell then
        self.mingwen_cell:DeleteMe()
        self.mingwen_cell = nil
    end
end

function MingWenLibraryCell:LoadCallBack()
	self.mingwen_cell = MingWenItemRender.New(self.node_list.mingwen_cell)
end

function MingWenLibraryCell:OnFlush()
	self.mingwen_cell:SetData({item_id = self.data.item_id})
	local chapter_level = math.ceil((self.data.tianxiange_fb_pass_level + 1) / 10)
	self.node_list.desc.text.text = string.format(Language.MingWenView.PassDesc, chapter_level)
end