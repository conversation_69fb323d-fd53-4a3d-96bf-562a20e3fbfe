require("game/activity_amount_accumulatively_recharged/activity_amount_recharged_wg_data")
require("game/activity_amount_accumulatively_recharged/activity_amount_recharged_view")
require("game/activity_amount_accumulatively_recharged/activity_amount_recharged_rank_view")


ActivityAmountRechargedWGCtrl = ActivityAmountRechargedWGCtrl or BaseClass(BaseWGCtrl)

function ActivityAmountRechargedWGCtrl:__init()
    if ActivityAmountRechargedWGCtrl.Instance then
		error("[ActivityAmountRechargedWGCtrl]:Attempt to create singleton twice!")
	end

    ActivityAmountRechargedWGCtrl.Instance = self

    self.view = ActivityAmountRechargedView.New(GuideModuleName.AmountRechargedView)
    self.data = ActivityAmountRechargedWGData.New()
    self.recharged_rank_view = ActivityAmountRankView.New()

    --注册协议
    self:RegisterAllProtocols()

end

function ActivityAmountRechargedWGCtrl:__delete()
    ActivityAmountRechargedWGCtrl.Instance = nil

    if self.data ~= nil then
        self.data:DeleteMe()
        self.data = nil
    end
    
    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.recharged_rank_view then
        self.recharged_rank_view:DeleteMe()
        self.recharged_rank_view = nil
    end
end

function ActivityAmountRechargedWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCCrossChongZhiRankInfo, "OnSCCrossChongZhiRankInfo")
end

--跨服充值榜请求
function ActivityAmountRechargedWGCtrl:SendAmountRechargedRank(opera_type,param_1,param_2,param_3)
	local param_t = {}
	param_t.activity_type = ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_CHONGZHI_RANK
	param_t.opera_type = opera_type or 0
	param_t.param_1 = param_1 or 0
	param_t.param_2 = param_2 or 0
	param_t.param_3 = param_3 or 0
	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end

--接受协议
function ActivityAmountRechargedWGCtrl:OnSCCrossChongZhiRankInfo(protocol)
   -- print_error("protocol", protocol)
    --数据存储
    self.data:SetRankInfo(protocol)

    if self.view:IsOpen() then
        self.view:Flush(0, "rank_list")
    end
end

--打开充值排行榜的方法
function ActivityAmountRechargedWGCtrl:OpenAmountRankView()
	self.recharged_rank_view:Open()
end