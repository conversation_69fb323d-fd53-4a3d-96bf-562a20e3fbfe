-- K-跨服3v3(新).xls
local item_table={
[1]={item_id=26129,num=6,is_bind=1},
[2]={item_id=26200,num=4,is_bind=1},
[3]={item_id=26346,num=3,is_bind=1},
[4]={item_id=30447,num=1,is_bind=1},
[5]={item_id=26129,num=9,is_bind=1},
[6]={item_id=26346,num=4,is_bind=1},
[7]={item_id=26214,num=1,is_bind=1},
[8]={item_id=26129,num=11,is_bind=1},
[9]={item_id=26200,num=6,is_bind=1},
[10]={item_id=26203,num=4,is_bind=1},
[11]={item_id=26346,num=5,is_bind=1},
[12]={item_id=30447,num=2,is_bind=1},
[13]={item_id=26129,num=14,is_bind=1},
[14]={item_id=30447,num=3,is_bind=1},
[15]={item_id=46566,num=30,is_bind=1},
[16]={item_id=26130,num=25,is_bind=1},
[17]={item_id=26191,num=2,is_bind=1},
[18]={item_id=48567,num=1,is_bind=1},
[19]={item_id=26357,num=2,is_bind=1},
[20]={item_id=26348,num=2,is_bind=1},
[21]={item_id=26356,num=5,is_bind=1},
[22]={item_id=26355,num=5,is_bind=1},
[23]={item_id=46566,num=25,is_bind=1},
[24]={item_id=26130,num=21,is_bind=1},
[25]={item_id=26357,num=1,is_bind=1},
[26]={item_id=26348,num=1,is_bind=1},
[27]={item_id=46566,num=20,is_bind=1},
[28]={item_id=26130,num=18,is_bind=1},
[29]={item_id=26191,num=1,is_bind=1},
[30]={item_id=26356,num=4,is_bind=1},
[31]={item_id=26347,num=15,is_bind=1},
[32]={item_id=26355,num=4,is_bind=1},
[33]={item_id=30447,num=32,is_bind=1},
[34]={item_id=26348,num=6,is_bind=1},
[35]={item_id=46048,num=10,is_bind=1},
[36]={item_id=36416,num=2400000,is_bind=1},
[37]={item_id=30447,num=28,is_bind=1},
[38]={item_id=26348,num=3,is_bind=1},
[39]={item_id=46048,num=5,is_bind=1},
[40]={item_id=26356,num=3,is_bind=1},
[41]={item_id=30447,num=20,is_bind=1},
[42]={item_id=46048,num=3,is_bind=1},
[43]={item_id=26347,num=10,is_bind=1},
[44]={item_id=30447,num=16,is_bind=1},
[45]={item_id=46048,num=1,is_bind=1},
[46]={item_id=26347,num=5,is_bind=1},
[47]={item_id=26355,num=3,is_bind=1},
[48]={item_id=30447,num=12,is_bind=1},
[49]={item_id=26346,num=20,is_bind=1},
[50]={item_id=36422,num=1200,is_bind=1},
[51]={item_id=36422,num=900,is_bind=1},
[52]={item_id=26129,num=5,is_bind=1},
[53]={item_id=26346,num=1,is_bind=1},
[54]={item_id=37245,num=1,is_bind=1},
[55]={item_id=26130,num=30,is_bind=1},
[56]={item_id=26193,num=1,is_bind=1},
[57]={item_id=26191,num=3,is_bind=1},
[58]={item_id=48559,num=1,is_bind=1},
[59]={item_id=26357,num=3,is_bind=1},
[60]={item_id=26356,num=6,is_bind=1},
[61]={item_id=26355,num=6,is_bind=1},
[62]={item_id=30447,num=40,is_bind=1},
[63]={item_id=26348,num=8,is_bind=1},
[64]={item_id=46048,num=20,is_bind=1},
}

return {
zhandui={
{}
},

zhandui_meta_table_map={
},
zhandui_lingpai={
{active_condition=0,condition_param=0,color_type=1,},
{lingpai_id=1,active_condition=1,condition_param=8,desc="队长vip达到V8",icon=27569,lingpai_name="精英战队",},
{lingpai_id=2,active_condition=2,condition_param=1000,desc="逐鹿仙缘死亡1000次",icon=27570,lingpai_name="视死如归",},
{lingpai_id=3,active_condition=3,desc="逐鹿仙缘连胜20场",icon=27571,lingpai_name="战无不胜",},
{lingpai_id=4,active_condition=3,condition_param=50,desc="逐鹿仙缘连胜50场",icon=27572,lingpai_name="所向披靡",},
{lingpai_id=5,active_condition=4,desc="逐鹿仙缘连败20场",icon=27573,lingpai_name="百折不挠",},
{lingpai_id=6,condition_param="1,8",valid_time_min=43200,desc="在逐鹿仙缘赛季中获得1-8名",icon=27574,lingpai_name="巅峰战神",},
{lingpai_id=7,condition_param="9,20",valid_time_min=43200,desc="在逐鹿仙缘赛季中获得9-20名",icon=27575,lingpai_name="王城英雄",},
{lingpai_id=8,condition_param="21,50",valid_time_min=43200,desc="在逐鹿仙缘赛季中获得21-50名",icon=27576,lingpai_name="江湖豪杰",}
},

zhandui_lingpai_meta_table_map={
},
open={
{}
},

open_meta_table_map={
},
match={
{}
},

match_meta_table_map={
},
grade={
{},
{star=2,star_num=2,name="翡翠四阶",score=91,},
{star=3,star_num=3,name="翡翠三阶",score=181,},
{star=4,star_num=4,name="翡翠二阶",score=271,},
{star=5,star_num=5,name="翡翠一阶",score=361,},
{grade=2,star_num=6,name="紫琼五阶",score=451,win_score=100,rank_id=3,tier_name="流光紫琼",},
{star=2,star_num=7,name="紫琼四阶",score=651,},
{star=3,star_num=8,name="紫琼三阶",score=851,},
{star=4,star_num=9,name="紫琼二阶",score=1051,},
{star=5,star_num=10,name="紫琼一阶",score=1251,},
{grade=3,star_num=11,name="无双五阶",score=1451,win_score=150,rank_id=4,tier_name="无双大师",},
{star=2,star_num=12,name="无双四阶",score=2001,},
{star=3,star_num=13,name="无双三阶",score=2551,},
{star=4,star_num=14,name="无双二阶",score=3101,},
{star=5,star_num=15,name="无双一阶",score=3651,},
{grade=4,star_num=16,name="宗师五阶",score=4201,win_score=200,rank_id=5,tier_name="傲世宗师",},
{star=2,star_num=17,name="宗师四阶",score=5151,},
{star=3,star_num=18,name="宗师三阶",score=6101,},
{star=4,star_num=19,name="宗师二阶",score=7051,},
{star=5,star_num=20,name="宗师一阶",score=8001,},
{grade=5,star_num=21,name="盖世五阶",score=8951,win_score=300,rank_id=6,tier_name="盖世绝伦",},
{star=2,star_num=22,name="盖世四阶",score=10451,},
{star=3,star_num=23,name="盖世三阶",score=11951,},
{star=4,star_num=24,name="盖世二阶",score=13451,},
{star=5,star_num=25,name="盖世一阶",score=14951,},
{grade=6,star_num=26,name="王者五阶",score=16451,win_score=450,rank_id=7,tier_name="最强王者",},
{star=2,star_num=27,name="王者四阶",score=19451,},
{star=3,star_num=28,name="王者三阶",score=22451,},
{star=4,star_num=29,name="王者二阶",score=25451,},
{star=5,star_num=30,name="王者一阶",score=28451,}
},

grade_meta_table_map={
[25]=21,	-- depth:1
[27]=26,	-- depth:1
[24]=21,	-- depth:1
[23]=21,	-- depth:1
[22]=21,	-- depth:1
[28]=26,	-- depth:1
[20]=16,	-- depth:1
[19]=16,	-- depth:1
[15]=11,	-- depth:1
[17]=16,	-- depth:1
[29]=26,	-- depth:1
[14]=11,	-- depth:1
[13]=11,	-- depth:1
[12]=11,	-- depth:1
[10]=6,	-- depth:1
[9]=6,	-- depth:1
[8]=6,	-- depth:1
[7]=6,	-- depth:1
[18]=16,	-- depth:1
[30]=26,	-- depth:1
},
pk_reward={
{}
},

pk_reward_meta_table_map={
},
match_times_reward={
{},
{seq=1,match_times=5,reward={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},reward_gift=18711,},
{seq=2,match_times=7,reward={[0]=item_table[5],[1]=item_table[2],[2]=item_table[6],[3]=item_table[7],[4]=item_table[4]},reward_gift=18712,},
{seq=3,match_times=10,reward={[0]=item_table[8],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[7],[5]=item_table[12]},reward_gift=18713,},
{seq=4,match_times=15,reward={[0]=item_table[13],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[7],[5]=item_table[14]},reward_gift=18714,}
},

match_times_reward_meta_table_map={
},
season_rank_reward={
{},
{seq=1,lower_rank=2,upper_rank=3,reward={[0]=item_table[15],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[19],[5]=item_table[20],[6]=item_table[21],[7]=item_table[22]},reward_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[19],[5]=item_table[20],[6]=item_table[21],[7]=item_table[22]},rank_text="第2~3名",},
{seq=2,lower_rank=4,upper_rank=10,reward={[0]=item_table[23],[1]=item_table[24],[2]=item_table[17],[3]=item_table[25],[4]=item_table[26],[5]=item_table[18],[6]=item_table[21],[7]=item_table[22]},reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[17],[3]=item_table[25],[4]=item_table[26],[5]=item_table[18],[6]=item_table[21],[7]=item_table[22]},rank_text="第4~10名",},
{seq=3,lower_rank=11,upper_rank=50,reward={[0]=item_table[27],[1]=item_table[28],[2]=item_table[29],[3]=item_table[25],[4]=item_table[18],[5]=item_table[30],[6]=item_table[31],[7]=item_table[32]},reward_item={[0]=item_table[27],[1]=item_table[28],[2]=item_table[29],[3]=item_table[25],[4]=item_table[18],[5]=item_table[30],[6]=item_table[31],[7]=item_table[32]},rank_text="第11~50名",}
},

season_rank_reward_meta_table_map={
},
season_score_reward={
{},
{seq=1,lower_score=9151,upper_score=16650,reward={[0]=item_table[33],[1]=item_table[25],[2]=item_table[34],[3]=item_table[35],[4]=item_table[30],[5]=item_table[22],[6]=item_table[36]},reward_item={[0]=item_table[33],[1]=item_table[25],[2]=item_table[34],[3]=item_table[35],[4]=item_table[30],[5]=item_table[22],[6]=item_table[36]},duanwei_name="盖世绝伦",icon=5,rank_id=6,},
{seq=2,lower_score=4351,upper_score=9150,reward={[0]=item_table[37],[1]=item_table[25],[2]=item_table[38],[3]=item_table[39],[4]=item_table[40],[5]=item_table[22],[6]=item_table[36]},reward_item={[0]=item_table[37],[1]=item_table[25],[2]=item_table[38],[3]=item_table[39],[4]=item_table[40],[5]=item_table[22],[6]=item_table[36]},duanwei_name="傲世宗师",icon=4,rank_id=5,},
{seq=3,lower_score=1651,upper_score=4350,reward={[0]=item_table[41],[1]=item_table[42],[2]=item_table[40],[3]=item_table[43],[4]=item_table[22],[5]=item_table[36]},reward_item={[0]=item_table[41],[1]=item_table[42],[2]=item_table[40],[3]=item_table[43],[4]=item_table[22],[5]=item_table[36]},duanwei_name="无双大师",icon=3,rank_id=4,},
{seq=4,lower_score=451,upper_score=1650,reward={[0]=item_table[44],[1]=item_table[45],[2]=item_table[40],[3]=item_table[46],[4]=item_table[47],[5]=item_table[36]},reward_item={[0]=item_table[44],[1]=item_table[45],[2]=item_table[40],[3]=item_table[46],[4]=item_table[47],[5]=item_table[36]},duanwei_name="流光紫琼",icon=2,rank_id=3,},
{seq=5,lower_score=0,upper_score=450,reward={[0]=item_table[48],[1]=item_table[40],[2]=item_table[47],[3]=item_table[49],[4]=item_table[36]},reward_item={[0]=item_table[48],[1]=item_table[40],[2]=item_table[47],[3]=item_table[49],[4]=item_table[36]},duanwei_name="不屈翡翠",icon=1,rank_id=2,}
},

season_score_reward_meta_table_map={
},
pk={
{}
},

pk_meta_table_map={
},
robot_rand_attr={
{},
{},
{},
{}
},

robot_rand_attr_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
zhandui_default_table={},

zhandui_lingpai_default_table={lingpai_id=0,active_condition=5,condition_param=20,valid_time_min=0,desc="默认获得",icon=27568,color_type=2,lingpai_name="新生战队",},

open_default_table={openserver_day=0,},

match_default_table={},

grade_default_table={grade=1,star=1,star_num=1,name="翡翠五阶",icon="",score=0,win_score=45,lose_score=-25,next_season_init_score=0,rank_id=2,tier_name="不屈翡翠",},

pk_reward_default_table={win_reward={[0]=item_table[50]},lose_reward={[0]=item_table[51]},mvp_reward={},reward_time_limit=10,mvp_reward_time_limit=0,score_reward_time_limit=15,},

match_times_reward_default_table={seq=0,match_times=1,reward={[0]=item_table[52],[1]=item_table[2],[2]=item_table[53],[3]=item_table[4]},reward_gift=18710,},

season_rank_reward_default_table={seq=0,lower_rank=1,upper_rank=1,reward={[0]=item_table[54],[1]=item_table[55],[2]=item_table[56],[3]=item_table[57],[4]=item_table[58],[5]=item_table[59],[6]=item_table[38],[7]=item_table[60],[8]=item_table[61]},reward_item={[0]=item_table[54],[1]=item_table[55],[2]=item_table[56],[3]=item_table[57],[4]=item_table[58],[5]=item_table[59],[6]=item_table[38],[7]=item_table[60],[8]=item_table[61]},need_match_times=15,is_show_icon=0,rank_text="第1名",},

season_score_reward_default_table={seq=0,lower_score=16651,upper_score=99999999,reward={[0]=item_table[62],[1]=item_table[19],[2]=item_table[63],[3]=item_table[64],[4]=item_table[30],[5]=item_table[22],[6]=item_table[36]},reward_item={[0]=item_table[62],[1]=item_table[19],[2]=item_table[63],[3]=item_table[64],[4]=item_table[30],[5]=item_table[22],[6]=item_table[36]},need_match_times=15,duanwei_name="最强王者",icon=6,rank_id=7,},

pk_default_table={dead_model=9998001,dead_model_nv=9999001,},

robot_rand_attr_default_table={},

other_default_table={target_pos="100|178",}

}

