function GameAssistantView:LoadAssistantTodayActivity()
    -- 活跃获得
	if not self.today_act_list then
		self.today_act_list = AsyncFancyAnimView.New(AssistantTodayActRender, self.node_list.today_act_list)
		self.today_act_list:SetSelectCallBack(BindTool.Bind(self.OnTodayActListOnClickCB, self))
	end

    XUI.AddClickEventListener(self.node_list.today_btn_go_to, BindTool.Bind(self.OnClickTodayGoTo, self))
end

function GameAssistantView:OnTodayActListOnClickCB(item, cell_index, is_default, is_click)
	local data = item:GetData()
	if IsEmptyTable(data) then
		return
	end
	
	if self.select_index == cell_index then
		return
	end

	self.select_index = cell_index
	self.today_act_data = data
	self:FlushTodayActCurrSelectData()
end

function GameAssistantView:ReleaseAssistantTodayActivity()
    if self.today_act_list then
        self.today_act_list:DeleteMe()
        self.today_act_list = nil
    end

	self.select_index = nil
	self.today_act_data = nil
end

function GameAssistantView:CloseAssistantTodayActivity()

end

function GameAssistantView:ShowAssistantTodayActivity()

end

function GameAssistantView:OnClickTodayGoTo()
	if not self.today_act_data then
		return
	end

	FunOpen.Instance:OpenViewNameByCfg(self.today_act_data.get_way_path)
end

-- 刷新界面
function GameAssistantView:FlushAssistantTodayActivity(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushAssistantTodayMessage()
		end
	end
end

function GameAssistantView:FlushAssistantTodayMessage()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local list = GameAssistantWGData.Instance:GetTodayCfgListByDay(cur_day)
	self.today_act_list:SetDataList(list)
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local level_des = RoleWGData.GetLevelString(role_level)
	self.node_list.today_act_level.text.text = string.format(Language.GameAssistant.AssistantRoleLv, level_des)
	self.node_list.today_act_day.text.text = string.format(Language.GameAssistant.AssistantServerDay, cur_day)

	if self.select_index == nil then
		self.today_act_list:JumpToIndex(0, 1)
	end
end

function GameAssistantView:FlushTodayActCurrSelectData()
	if not self.today_act_data then
		return
	end

	self.node_list.today_act_name.text.text = self.today_act_data.get_way_title
	self.node_list.today_act_desc.text.text = self.today_act_data.get_way_desc
end

-----------------------------------------------------------------------------
AssistantTodayActRender = AssistantTodayActRender or BaseClass(BaseRender)
function AssistantTodayActRender:LoadCallBack()
    -- 活跃获得
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end
end

function AssistantTodayActRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function AssistantTodayActRender:OnFlush()
	if not self.data then
		return
	end

	if self.data.get_way_icon ~= nil and self.data.get_way_icon ~= "" and self.data.get_way_icon > 0 then
		local bundle, asset = ResPath.GetRawImagesPNG(string.format("a3_bfzy_jrwf_tu%02d_1", self.data.get_way_icon))
		self.node_list.act_raw_image.raw_image:LoadSprite(bundle, asset)
	end

	self.reward_list:SetDataList(self.data.reward_item_show)
end

function AssistantTodayActRender:OnSelectChange(is_select)
	-- self:SetScale(is_select)
end

function AssistantTodayActRender:SetScale(is_select)
	-- if is_select then
	-- 	self.node_list.today_act_change_root.transform.localScale = Vector3(1, 1, 1)
	-- else
	-- 	self.node_list.today_act_change_root.transform.localScale = Vector3(0.8, 0.8, 0.8)
	-- end
end
