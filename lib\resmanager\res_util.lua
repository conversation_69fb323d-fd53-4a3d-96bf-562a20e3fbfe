-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local M = {}

local AppStreamingAssetsPath = UnityEngine.Application.streamingAssetsPath
local SysFile = System.IO.File
local SysPath = System.IO.Path
local SysDirectory = System.IO.Directory
local SysSearchOption = System.IO.SearchOption
local UnityResourceFind = UnityEngine.Resources.FindObjectsOfTypeAll
local GameRootGetAliasResPath = GameRoot.Instance.GetAliasResPath
local _sformat = string.format
local _slower = string.lower
local NewString = System.String.New

local FileExistTbl = {}
local table_pool = {}
local string_pool = {}

M.memory_debug = false
M.memory_tag = "memory_tag"
M.cache_path = ""
M.streaming_files = {}
M.log_debug = false
M.log_list = {}
M.black_list = {}
M.is_ios_encrypt_asset = false
M.is_android_encrypt_asset = false
ResLoadPriority = {
	min = 0,
	steal = 1,				-- 偷偷加载
	low = 2,				-- 玩家不关心的东西，允许慢慢出来，比如其他玩家角色，头顶名字等,
	mid = 3,
	high = 4,				-- 如异步加载角色身体时
	faster_async_low = 5,	-- 主要用于UI,如加载sprite物品图标，（下载和加载AB用同步，取资源用异步)
	faster_async_mid = 6,	-- 主要用于UI,如加载小界面时，（下载和加载AB用同步，取资源用异步)
	faster_async_high = 7,	-- 主要用于UI, 如加载大界面时，（下载和加载AB用同步，取资源用异步)
	sync = 8,				-- 同步，同步加载时用，下载的文件后会马上同步写入，无视大小文件
	max = 9,
}

ResPoolReleasePolicy = {
	min = 0,
	Defualt = 1,		-- 默认释放
	NotDestroy = 2,		-- 不Destory(永存)
	Culling = 3,		-- 放到Act池(不隐藏，通过摄象机裁剪令其不可见)
	DestroyQuick = 4,		-- 马上释放
	max = 5,
}

function M.create_child_mt(base)
	local child = setmetatable({}, base)
	child.__index = child
	return child
end

local function _create_class_new(self, ...)
	local ret = setmetatable({}, self)
	ret:_init(...)
	return ret
end

local function _create_class_on_destroy(self)
end

function M.create_class()
	local class = {}
	class.__index = class

	class.new = _create_class_new
	class.on_destroy = _create_class_on_destroy

	return class
end

function M.GetAssetFullPath(bundle_name, asset_name)
	return _sformat("%s/%s", bundle_name, _slower(asset_name))
end

function M.SetBaseCachePath(path)
	if not path or path == "" then
		return
	end

	if not SysDirectory.Exists(path) then
		SysDirectory.CreateDirectory(path)
	end

	M.base_cache_path = path
end

function M.GetBaseCachePath()
	return M.base_cache_path
end

function M.GetCachePath(bundle_name, hash)
	if M.is_ios_encrypt_asset or M.is_android_encrypt_asset then
		local relative_path = ""
		if hash then
			relative_path = _sformat("%s-%s", bundle_name, hash)
		else
			relative_path = bundle_name
		end

		relative_path = M.GetFileEncryptPath(relative_path)
		return _sformat("%s/%s", M.base_cache_path, relative_path)
	else
		if hash then
			return _sformat("%s/%s-%s", M.base_cache_path, bundle_name, hash)
		else
			return _sformat("%s/%s", M.base_cache_path, bundle_name)
		end
	end
end

function M.RemoveHashCode(path)
	local match_index = string.find(path, "-")
	return (match_index ~= nil and match_index ~= -1) and string.sub(path, 1, match_index - 1) or path
end

function M.GetBundleFilePath(bundle_name, hash)
	local record_path = FileExistTbl[bundle_name]
	if nil ~= record_path then
		return record_path
	end

	-- 是否在Cache目录存在
	local bundle_file_path = M.GetCachePath(bundle_name, hash)
	local is_cache_exist = SysFile.Exists(bundle_file_path)
	if is_cache_exist then
		FileExistTbl[bundle_name] = bundle_file_path
		return bundle_file_path
	end

	-- 是否在包体内
	if nil == hash then
		local file_list_bundle_path = _sformat("AssetBundle/%s", bundle_name)
		local is_exist_in_streaming = M.ExistedInStreaming(file_list_bundle_path)
		if is_exist_in_streaming then
			local bundle_name_enpath = (ResUtil.is_ios_encrypt_asset or M.is_android_encrypt_asset) and M.GetStreamingFileEncryptPath(bundle_name) or bundle_name
			local bundle_path = _sformat("AssetBundle/%s", M.RemoveHashCode(bundle_name_enpath))
			bundle_file_path = M.GetSteamingAssetPath(bundle_path)
			FileExistTbl[bundle_name] = bundle_file_path
			return bundle_file_path
		end
	else
		local bundle_name_enpath = (ResUtil.is_ios_encrypt_asset or M.is_android_encrypt_asset) and M.GetStreamingFileEncryptPath(bundle_name) or bundle_name
		local bundle_path = _sformat("AssetBundle/%s-%s", M.RemoveHashCode(bundle_name_enpath), hash)
		local file_list_bundle_path = _sformat("AssetBundle/%s-%s", bundle_name, hash)
		local is_exist_in_streaming = M.ExistedInStreaming(file_list_bundle_path)
		if is_exist_in_streaming then
			bundle_file_path = M.GetSteamingAssetPath(bundle_path)
			FileExistTbl[bundle_name] = bundle_file_path
			return bundle_file_path
		end
	end

	local bundle_name_enpath = (ResUtil.is_ios_encrypt_asset or M.is_android_encrypt_asset) and M.GetFileEncryptPath(bundle_name) or bundle_name
	-- 当没有指定Download时，将无视hash值寻找
	if ResMgr:GetIsIgnoreHashCheck() then
		local file_name = SysPath.GetFileName(bundle_name_enpath)
		-- 从Canche目录中找
		local bundle_dir = SysPath.GetDirectoryName(M.GetCachePath(bundle_name, hash))
		if SysDirectory.Exists(bundle_dir) then
			local file_list = SysDirectory.GetFiles(bundle_dir, _sformat("%s-*", file_name), SysSearchOption.TopDirectoryOnly)
			if nil ~= file_list and file_list.Length > 0 then
				return file_list:GetValue(0)
			end
		end

		-- 在包体中找
		local find_start
		bundle_name_enpath = (ResUtil.is_ios_encrypt_asset or M.is_android_encrypt_asset) and M.GetStreamingFileEncryptPath(bundle_name) or bundle_name
		for k, _ in pairs(M.streaming_files) do
			find_start, _ = string.find(k, _sformat("AssetBundle/%s%s", bundle_name_enpath, "%-"))
			if 1 == find_start then
				bundle_file_path = M.GetSteamingAssetPath(k)
				-- FileExistTbl[bundle_name] = bundle_file_path
				return bundle_file_path
			end
		end
	end

	return nil
end

-- 删除文件路径缓存
function M.ClearFilePathCache()
	FileExistTbl = {}
end

function M.GetFileEncryptPath(path)
	return EncryptMgr.GetEncryptPath(path)
end

function M.GetStreamingFileEncryptPath(path)
	return EncryptMgr.GetStreamingEncryptPath(path)
end

function M.ReadEncryptFile(path)
	return EncryptMgr.ReadEncryptFile(path)
end

function M.ReadStreamingEncryptFile(path)
	return EncryptMgr.ReadStreamingEncryptFile(path)
end

function M.GetEncryptKey()
	return EncryptMgr.GetEncryptKey()
end

function M.GetStreamingEncryptKey()
	return EncryptMgr.GetStreamingEncryptKey()
end

function M.GetEncryptKeyLength()
	return EncryptMgr.GetEncryptKeyLength()
end

function M.GetStreamingEncryptKeyLength()
	return EncryptMgr.GetStreamingEncryptKeyLength()
end

function M.LoadFileHelper(path)
	local full_path = M.GetCachePath(path)
	local is_sysfile_exist = SysFile.Exists(full_path)
	if is_sysfile_exist then
		if ResUtil.is_ios_encrypt_asset or M.is_android_encrypt_asset then
			return M.ReadEncryptFile(full_path)
		else
			return SysFile.ReadAllText(full_path)
		end
	else
		if M.is_android_encrypt_asset then
			local alias_path = "AssetBundle/" .. M.GetStreamingFileEncryptPath(path)
			return StreamingAssets.EncryptReadAllText(alias_path, M.GetStreamingEncryptKey())
		elseif M.is_ios_encrypt_asset then
			local alias_path = M.GetSteamingAssetPath("AssetBundle/" .. M.GetStreamingFileEncryptPath(path))
			return M.ReadStreamingEncryptFile(alias_path)
		else
			local alias_path = M.GetAliasResPath("AssetBundle/" .. path)
			return StreamingAssets.ReadAllText(alias_path)
		end
	end
end

function M.LoadApkFileHelper(path)
	if M.is_android_encrypt_asset then
		local alias_path = "AssetBundle/" .. M.GetStreamingFileEncryptPath(path)
		return StreamingAssets.EncryptReadAllText(alias_path, M.GetStreamingEncryptKey())
	elseif M.is_ios_encrypt_asset then
		local alias_path = M.GetSteamingAssetPath("AssetBundle/" .. M.GetStreamingFileEncryptPath(path))
		return M.ReadStreamingEncryptFile(alias_path)
	else
		local alias_path = M.GetAliasResPath("AssetBundle/" .. path)
		return StreamingAssets.ReadAllText(alias_path)
	end
end

function M.IsFileExist(bundle_name, hash)
	return nil == M.black_list[bundle_name] and nil ~= M.GetBundleFilePath(bundle_name, hash)
end

-- 删除缓存中所有对应的bundle
function M.DelCacheBundle(bundle_name)
	local cache_path = M.GetCachePath(bundle_name)
	local bundle_dir = SysPath.GetDirectoryName(cache_path)
	if SysDirectory.Exists(bundle_dir) then
		local file_name = SysPath.GetFileName(cache_path)
		local file_list = SysDirectory.GetFiles(bundle_dir, _sformat("%s-*", file_name), SysSearchOption.TopDirectoryOnly)
		if nil ~= file_list and file_list.Length > 0 then
			FileExistTbl[bundle_name] = nil
			for i = 0, file_list.Length - 1 do
				if SysFile.Exists(file_list[i]) then
					-- print_error("[ResUtil] DelCacheBundle", bundle_name, file_list[i])
					os.remove(file_list[i])
				end
			end
		end
	end
end

function M.RemoveFile(bundle_name, file_path)
	if nil == file_path then
		print_error("[ResUtil]RemoveFile Fail", bundle_name)
		return
	end

	if nil ~= bundle_name then
		FileExistTbl[bundle_name] = nil
	else
		print_error("[ResUtil] BundleName is Nil", file_path)
	end

	if SysFile.Exists(file_path) then
		os.remove(file_path)
	end
end

function M.ClearBundleFileCache(bundle_name)
	if nil ~= bundle_name then
		FileExistTbl[bundle_name] = nil
	end
end

function M.ExistedInCache(bundle_name, hash)
	local path = M.GetCachePath(bundle_name, hash)
	if nil == M.black_list[bundle_name] and SysFile.Exists(path) then
		return true
	end

	return false
end

function M.IsCachePath(path)
	if nil == path then return false end

	local cache_dir = "BundleCache"
	if M.is_ios_encrypt_asset or M.is_android_encrypt_asset then
		cache_dir = M.GetFileEncryptPath("BundleCache")
	end

	local _start = string.find(path, _sformat("/%s/", cache_dir))
	return nil ~= _start
end

function M.ExistedInStreaming(path)
	return M.streaming_files[path]
end

function M.InitStreamingFilesInfo()
	M.streaming_files = {}
	local data = nil
	if M.is_android_encrypt_asset then
		local path = M.GetStreamingFileEncryptPath("file_list.txt")
		data = StreamingAssets.EncryptReadAllText(path, M.GetStreamingEncryptKey())
	elseif M.is_ios_encrypt_asset then
		local en_file_list = M.GetStreamingFileEncryptPath("file_list.txt")
		local path = M.GetSteamingAssetPath(en_file_list)
		--print_error("InitStreamingFilesInfo en_file_list, path =", en_file_list, path)
		data = M.ReadEncryptFile(path)
	else
		local path = M.GetAliasResPath("file_list.txt")
		data = StreamingAssets.ReadAllText(path)
	end
	--print_error("InitStreamingFilesInfo file_list data =", data)
	local lines = Split(data, '\n')
	for _, line in ipairs(lines) do
		M.streaming_files[line] = true
	end
end

function M.InitEncryptKey()
	if nil ~= EncryptMgr then
		M.is_ios_encrypt_asset = UNITY_IOS and EncryptMgr.IsEncryptAsset() or false
		M.is_android_encrypt_asset = UNITY_ANDROID and EncryptMgr.IsEncryptAsset() or false
	end
end

function M.GetSteamingAssetPath(path)
	path = M.GetAliasResPath(path)
	return _sformat("%s/%s", AppStreamingAssetsPath, path)
end

function M.GetAgentAssetPath(path)
	path = M.GetSteamingAssetPath(path)

	-- if M.is_ios_encrypt_asset then
	-- 	local target_path = EncryptMgr.DecryptAgentAssets(path)
	-- 	if nil ~= target_path and "" ~= target_path then
	-- 		path = target_path
	-- 	end
	-- end

	return path
end

function M.GetTable()
	local t = next(table_pool)

	if nil == t then
		t = {}
	else
		table_pool[t] = nil
	end

	return t
end

function M.ReleaseTable(t)
	table_pool[t] = t
end

function M.GetCString(lua_str)
	local c_str = string_pool[lua_str]
	if nil == c_str then
		c_str = NewString(lua_str)
		string_pool[lua_str] = c_str
	end
	return c_str
end

function M.AddProfileBeginSample(flag)
	if IS_OPEN_TOLUA_PROFILE then
		local lua_str = "lua:" .. flag
		ToLuaProfile.AddProfileBeginSample(ResUtil.GetCString(lua_str))
	end
end

function M.AddProfileEndSample()
	if IS_OPEN_TOLUA_PROFILE then
		ToLuaProfile.AddProfileEndSample()
	end
end

function M.GetAliasResPath(path)
	return GameRootGetAliasResPath(path)
end

function M.Log(...)
	if not UNITY_EDITOR then
		return
	end

	print_log(...)

	local param = {...}
	local log_str = string.format("%s", socket.gettime())
	for _, v in ipairs(param) do
		log_str = log_str .. "		" .. tostring(v)
	end

	table.insert(M.log_list, log_str)
end

function M.OutputLog()
	local content = ""
	for _, v in ipairs(M.log_list) do
		content = content .. v .. "\n"
	end
	local file_path = UnityEngine.Application.dataPath .. "/../temp/log.txt"
	local f = assert(io.open(file_path,'w'))
	f:write(content)
	f:close()
end

return M

