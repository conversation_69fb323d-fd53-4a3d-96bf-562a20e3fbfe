require("game/merge_activity/merge_act_btn")
require("game/merge_activity/merge_tab_button")
require("game/merge_activity/merge_activity_wg_data")
require("game/merge_activity/merge_activity_view")
require("game/merge_activity/login_youli/login_youli_view")
require("game/merge_activity/day_first_chongzhi/merge_activity_first_recharge_view")
require("game/merge_activity/liemo_daren/liemo_daren_view")
require("game/merge_activity/hefu_miaosha_view/hefu_miaosha_view")
require("game/merge_activity/hefu_juling_view/hefu_juling_view")
require("game/merge_activity/merge_leichong/merge_leichong_recharge_view")
require("game/merge_activity/duobei/merge_duobei_view")
require("game/merge_activity/merge_special_rank/merge_special_rank_view")
require("game/merge_activity/merge_exchange_shop/merge_exchange_shop_view")


MergeActivityWGCtrl = MergeActivityWGCtrl or BaseClass(BaseWGCtrl)
function MergeActivityWGCtrl:__init()
	MergeActivityWGCtrl.Instance = self

	self.merge_data = MergeActivityWGData.New()
	self.merge_view = MergeActivityView.New(GuideModuleName.MergeActivityView)

	--和服活动总ItemDataChange注册
	self.item_data_change_event = BindTool.Bind1(self.ItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_event)
end

function MergeActivityWGCtrl:__delete()
	MergeActivityWGCtrl.Instance = nil

	self.merge_data:DeleteMe()
    self.merge_data = nil

	self.merge_view:DeleteMe()
	self.merge_view = nil

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_event)
	self.item_data_change_event = nil
end

function MergeActivityWGCtrl:ItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	MergeActivityWGData.Instance:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	MergeExchangeShopWGData.Instance:ItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
end

function MergeActivityWGCtrl:Open(tab_index, param_t)
	local tab_index = tab_index
	if nil == tab_index then --这一步只是为了防止出错而已为了兼容报错的情况下可走
		tab_index = self:GetFirstOpenActivity()
	end

	local activity_type = self.merge_data:GetCurSelectActivityType(tab_index)
	local state = self.merge_data:GetActivityState(activity_type)

	if not state then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeActivity.ActivityNoOpenHint)
		return
	end

	self.merge_view:TabbarReloadData(true, tab_index)
	self.merge_view:Open(tab_index)
end

-- 获取排序后第一个开启的活动
function MergeActivityWGCtrl:GetFirstOpenActivity()
	local first_tab_index = self.merge_data:GetOneOpenTabIndex()
	return first_tab_index
end

function MergeActivityWGCtrl:GetActivityIsOpen(tab_index)
	if not tab_index or not TabIndex[tab_index] then
		return false
	end

	local activity_type = self.merge_data:GetCurSelectActivityType(TabIndex[tab_index])
	local state = self.merge_data:GetActivityState(activity_type)

	if state then
		return true
	end

	return false
end

function MergeActivityWGCtrl:ChangeSelectIndex()
	--活动关闭的时候对页签进行一次刷新并且重新做选中
	if self.merge_view:IsOpen() then
		--优先判断是否还有已开启的活动
		if self.merge_data:GetActivityIsClose() then
			self.merge_view:Close()
			return
		end

		self.merge_view:SetTabSate()
		--如果当前选择的活动是开启状态那么返回
		local index = self.merge_view:GetShowIndex() or 0
		local activity_type = self.merge_data:GetCurSelectActivityType(index)
		local is_open = self.merge_data:GetActivityState(activity_type)
		if is_open then
			return
		end
		self:Open() --有意传空让他做选择
	end
end

function MergeActivityWGCtrl:FlushView(...)
	-- 防止为空
	if self.merge_view then
		self.merge_view:Flush(...)
	end
end

function MergeActivityWGCtrl:GetViewIsOpen()
	if self.merge_view then
		return self.merge_view:IsOpen()
	end

	return false
end

function MergeActivityWGCtrl:GetViewInShowIndex(tab_index)
	if self.merge_view and self.merge_view:IsOpen() and self.merge_view:GetShowIndex() == tab_index then
		return self.merge_view
	end
end