-- 玩法说明界面

LandWarFbPersonDescriptionView = LandWarFbPersonDescriptionView or BaseClass(SafeBaseView)

function LandWarFbPersonDescriptionView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 594)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_land_war_fb_description_view")
end

function LandWarFbPersonDescriptionView:OpenCallBack()
end

function LandWarFbPersonDescriptionView:LoadCallBack()
    self:SetPWMSgInfo()
end

function LandWarFbPersonDescriptionView:SetPWMSgInfo()
    self.node_list.desc_gameplay_description.text.text = Language.LandWarFbPersonView.GameplayDescription
    self.node_list.title_view_name.text.text = Language.LandWarFbPersonView.GameplayDescriptionView

    self.node_list.desc_title1.text.text = Language.LandWarFbPersonView.GPDescTitle1
    self.node_list.desc_title2.text.text = Language.LandWarFbPersonView.GPDescTitle2
    self.node_list.desc_title3.text.text = Language.LandWarFbPersonView.GPDescTitle3

    self.node_list.desc_title_content1.text.text = Language.LandWarFbPersonView.GPDescTitleContent1
    self.node_list.desc_title_content2.text.text = Language.LandWarFbPersonView.GPDescTitleContent2
    self.node_list.desc_title_content3.text.text = Language.LandWarFbPersonView.GPDescTitleContent3

    self.node_list.desc_content1.text.text = Language.LandWarFbPersonView.GPDescContent1
    self.node_list.desc_content2.text.text = Language.LandWarFbPersonView.GPDescContent2
    self.node_list.desc_content3.text.text = Language.LandWarFbPersonView.GPDescContent3

    if not self.boss_info_list then
        self.boss_info_list = {}
        local des_cfg = PositionalWarfareWGData.Instance:GetGamePlayDesCfg()

        for i = 1, 3 do
            self.boss_info_list[i] = LandWarFbDescriptionBossInfoCellRender.New(self.node_list["boss_info" .. i])
            self.boss_info_list[i]:SetData(des_cfg and des_cfg[i])
        end
    end
end

function LandWarFbPersonDescriptionView:ReleaseCallBack()
    if self.boss_info_list then
        for k, v in pairs(self.boss_info_list) do
            v:DeleteMe()
        end

        self.boss_info_list = nil
    end
end

function LandWarFbPersonDescriptionView:OnFlush()

end

-------------------------------------------LandWarFbDescriptionBossInfoCellRender--------------------------------------
LandWarFbDescriptionBossInfoCellRender = LandWarFbDescriptionBossInfoCellRender or BaseClass(BaseRender)

function LandWarFbDescriptionBossInfoCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    for i = 1, 3 do
        local data = self.data[i]

        local icon_bundle, icon_asset = ResPath.GetBossIcon("wrod_boss_".. data.icon)
        self.node_list["boss_icon" .. i].image:LoadSprite(icon_bundle, icon_asset,function ()
            self.node_list["boss_icon" .. i].image:SetNativeSize()
        end)

        self.node_list["boss_sign" ..i].text.text = data.name
        self.node_list["boss_num" ..i].text.text = "x" .. data.num
        self.node_list["boss_score" ..i].text.text = string.format(Language.PositionalWarfare.GPDescBossScore, data.kill_capture_score)
    end
end