-- K-跨服1vn.xls
local item_table={
[1]={item_id=57994,num=10,is_bind=1},
[2]={item_id=57995,num=3,is_bind=1},
[3]={item_id=26450,num=2,is_bind=1},
[4]={item_id=57993,num=15,is_bind=1},
[5]={item_id=26368,num=5,is_bind=1},
[6]={item_id=26344,num=10,is_bind=1},
[7]={item_id=57994,num=6,is_bind=1},
[8]={item_id=57995,num=1,is_bind=1},
[9]={item_id=26444,num=1,is_bind=1},
[10]={item_id=57993,num=12,is_bind=1},
[11]={item_id=26368,num=2,is_bind=1},
[12]={item_id=26344,num=5,is_bind=1},
[13]={item_id=57994,num=8,is_bind=1},
[14]={item_id=57995,num=2,is_bind=1},
[15]={item_id=26450,num=1,is_bind=1},
[16]={item_id=57993,num=13,is_bind=1},
[17]={item_id=26368,num=4,is_bind=1},
[18]={item_id=26344,num=8,is_bind=1},
[19]={item_id=57994,num=5,is_bind=1},
[20]={item_id=26368,num=1,is_bind=1},
[21]={item_id=26446,num=3,is_bind=1},
[22]={item_id=57993,num=3,is_bind=1},
[23]={item_id=28666,num=3,is_bind=1},
[24]={item_id=28665,num=5,is_bind=1},
[25]={item_id=26437,num=1,is_bind=1},
[26]={item_id=26446,num=1,is_bind=1},
[27]={item_id=57993,num=1,is_bind=1},
[28]={item_id=28666,num=1,is_bind=1},
[29]={item_id=28665,num=2,is_bind=1},
[30]={item_id=57994,num=15,is_bind=1},
[31]={item_id=57995,num=6,is_bind=1},
[32]={item_id=26455,num=2,is_bind=1},
[33]={item_id=57993,num=30,is_bind=1},
[34]={item_id=26369,num=1,is_bind=1},
[35]={item_id=26345,num=5,is_bind=1},
[36]={item_id=57994,num=9,is_bind=1},
[37]={item_id=57993,num=18,is_bind=1},
[38]={item_id=26368,num=3,is_bind=1},
[39]={item_id=26345,num=2,is_bind=1},
[40]={item_id=57994,num=12,is_bind=1},
[41]={item_id=57995,num=4,is_bind=1},
[42]={item_id=26455,num=1,is_bind=1},
[43]={item_id=57993,num=20,is_bind=1},
[44]={item_id=26345,num=3,is_bind=1},
[45]={item_id=26345,num=1,is_bind=1},
[46]={item_id=26447,num=3,is_bind=1},
[47]={item_id=26447,num=1,is_bind=1},
[48]={item_id=57994,num=20,is_bind=1},
[49]={item_id=57995,num=8,is_bind=1},
[50]={item_id=26459,num=3,is_bind=1},
[51]={item_id=57993,num=50,is_bind=1},
[52]={item_id=26369,num=2,is_bind=1},
[53]={item_id=26345,num=10,is_bind=1},
[54]={item_id=57993,num=24,is_bind=1},
[55]={item_id=26459,num=1,is_bind=1},
[56]={item_id=26448,num=3,is_bind=1},
[57]={item_id=26448,num=1,is_bind=1},
[58]={item_id=57994,num=50,is_bind=1},
[59]={item_id=57995,num=12,is_bind=1},
[60]={item_id=26463,num=5,is_bind=1},
[61]={item_id=57993,num=100,is_bind=1},
[62]={item_id=26369,num=5,is_bind=1},
[63]={item_id=26345,num=15,is_bind=1},
[64]={item_id=26461,num=1,is_bind=1},
[65]={item_id=26449,num=3,is_bind=1},
[66]={item_id=26449,num=1,is_bind=1},
[67]={item_id=57996,num=1,is_bind=1},
[68]={item_id=28446,num=1,is_bind=1},
[69]={item_id=56316,num=2,is_bind=1},
[70]={item_id=57993,num=5,is_bind=1},
[71]={item_id=28447,num=2,is_bind=1},
[72]={item_id=56316,num=3,is_bind=1},
[73]={item_id=28446,num=2,is_bind=1},
[74]={item_id=56316,num=4,is_bind=1},
[75]={item_id=28447,num=3,is_bind=1},
[76]={item_id=26444,num=2,is_bind=1},
[77]={item_id=56316,num=5,is_bind=1},
[78]={item_id=57996,num=2,is_bind=1},
[79]={item_id=28446,num=3,is_bind=1},
[80]={item_id=56317,num=1,is_bind=1},
[81]={item_id=28448,num=1,is_bind=1},
[82]={item_id=56317,num=2,is_bind=1},
[83]={item_id=56317,num=3,is_bind=1},
[84]={item_id=28448,num=2,is_bind=1},
[85]={item_id=56317,num=4,is_bind=1},
[86]={item_id=26455,num=3,is_bind=1},
[87]={item_id=56317,num=5,is_bind=1},
[88]={item_id=57997,num=20,is_bind=1},
[89]={item_id=26569,num=1,is_bind=1},
[90]={item_id=26504,num=1,is_bind=1},
[91]={item_id=26519,num=1,is_bind=1},
[92]={item_id=57993,num=45,is_bind=1},
[93]={item_id=57997,num=18,is_bind=1},
[94]={item_id=26460,num=1,is_bind=1},
[95]={item_id=57993,num=40,is_bind=1},
[96]={item_id=57997,num=15,is_bind=1},
[97]={item_id=26503,num=1,is_bind=1},
[98]={item_id=26518,num=1,is_bind=1},
[99]={item_id=57993,num=35,is_bind=1},
[100]={item_id=57997,num=10,is_bind=1},
[101]={item_id=57997,num=8,is_bind=1},
[102]={item_id=26502,num=1,is_bind=1},
[103]={item_id=26517,num=1,is_bind=1},
[104]={item_id=57993,num=25,is_bind=1},
[105]={item_id=57997,num=6,is_bind=1},
[106]={item_id=57997,num=4,is_bind=1},
[107]={item_id=26501,num=1,is_bind=1},
[108]={item_id=26516,num=1,is_bind=1},
[109]={item_id=57997,num=2,is_bind=1},
[110]={item_id=57993,num=10,is_bind=1},
[111]={item_id=37650,num=1,is_bind=1},
[112]={item_id=37248,num=1,is_bind=1},
[113]={item_id=37467,num=1,is_bind=1},
[114]={item_id=50369,num=1,is_bind=1},
[115]={item_id=50370,num=1,is_bind=1},
[116]={item_id=50371,num=1,is_bind=1},
[117]={item_id=50372,num=1,is_bind=1},
[118]={item_id=50329,num=1,is_bind=1},
[119]={item_id=50330,num=1,is_bind=1},
[120]={item_id=50331,num=1,is_bind=1},
[121]={item_id=50332,num=1,is_bind=1},
[122]={item_id=37028,num=1,is_bind=1},
[123]={item_id=37411,num=1,is_bind=1},
[124]={item_id=37045,num=1,is_bind=1},
[125]={item_id=38772,num=1,is_bind=1},
[126]={item_id=38740,num=1,is_bind=1},
[127]={item_id=37032,num=1,is_bind=1},
[128]={item_id=38415,num=1,is_bind=1},
[129]={item_id=38748,num=1,is_bind=1},
[130]={item_id=28455,num=1,is_bind=1},
[131]={item_id=28456,num=1,is_bind=1},
[132]={item_id=28457,num=1,is_bind=1},
[133]={item_id=47416,num=1,is_bind=1},
[134]={item_id=47417,num=1,is_bind=1},
[135]={item_id=47418,num=1,is_bind=1},
[136]={item_id=47419,num=1,is_bind=1},
[137]={item_id=47420,num=1,is_bind=1},
[138]={item_id=47421,num=1,is_bind=1},
[139]={item_id=47422,num=1,is_bind=1},
[140]={item_id=47423,num=1,is_bind=1},
[141]={item_id=47400,num=1,is_bind=1},
[142]={item_id=47401,num=1,is_bind=1},
[143]={item_id=47402,num=1,is_bind=1},
[144]={item_id=47403,num=1,is_bind=1},
[145]={item_id=47404,num=1,is_bind=1},
[146]={item_id=47405,num=1,is_bind=1},
[147]={item_id=47406,num=1,is_bind=1},
[148]={item_id=47407,num=1,is_bind=1},
[149]={item_id=47560,num=1,is_bind=1},
[150]={item_id=47561,num=1,is_bind=1},
[151]={item_id=47562,num=1,is_bind=1},
[152]={item_id=47563,num=1,is_bind=1},
[153]={item_id=47564,num=1,is_bind=1},
[154]={item_id=47565,num=1,is_bind=1},
[155]={item_id=47566,num=1,is_bind=1},
[156]={item_id=47567,num=1,is_bind=1},
[157]={item_id=57800,num=1,is_bind=1},
[158]={item_id=57801,num=1,is_bind=1},
[159]={item_id=26122,num=1,is_bind=1},
[160]={item_id=26367,num=2,is_bind=1},
[161]={item_id=57994,num=3,is_bind=1},
[162]={item_id=57993,num=6,is_bind=1},
[163]={item_id=26367,num=1,is_bind=1},
[164]={item_id=26344,num=2,is_bind=1},
[165]={item_id=26445,num=3,is_bind=1},
[166]={item_id=26445,num=1,is_bind=1},
[167]={item_id=39989,num=1,is_bind=1},
[168]={item_id=26214,num=2,is_bind=1},
[169]={item_id=26123,num=1,is_bind=1},
[170]={item_id=39988,num=1,is_bind=1},
[171]={item_id=26214,num=1,is_bind=1},
[172]={item_id=28447,num=1,is_bind=1},
[173]={item_id=56316,num=1,is_bind=1},
[174]={item_id=57997,num=25,is_bind=1},
[175]={item_id=26570,num=1,is_bind=1},
[176]={item_id=26463,num=1,is_bind=1},
[177]={item_id=26505,num=1,is_bind=1},
[178]={item_id=26520,num=1,is_bind=1},
[179]={item_id=37753,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
stage={
[0]={seq=0,},
[1]={seq=1,camp0_player_num=8,camp1_player_num=24,camp0_win_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},camp0_fail_reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11],[5]=item_table[12]},camp1_win_reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[16],[4]=item_table[17],[5]=item_table[18]},camp1_fail_reward_item={[0]=item_table[19],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[20],[5]=item_table[12]},guess_succ_reward_item={[0]=item_table[9],[1]=item_table[21],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24]},guess_fail_reward_item={[0]=item_table[25],[1]=item_table[26],[2]=item_table[27],[3]=item_table[28],[4]=item_table[29]},},
[2]={seq=2,camp0_player_num=4,camp1_player_num=28,camp0_win_reward_item={[0]=item_table[30],[1]=item_table[31],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34],[5]=item_table[35]},camp0_fail_reward_item={[0]=item_table[36],[1]=item_table[14],[2]=item_table[15],[3]=item_table[37],[4]=item_table[38],[5]=item_table[39]},camp1_win_reward_item={[0]=item_table[40],[1]=item_table[41],[2]=item_table[42],[3]=item_table[43],[4]=item_table[34],[5]=item_table[44]},camp1_fail_reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[37],[4]=item_table[11],[5]=item_table[45]},guess_succ_reward_item={[0]=item_table[9],[1]=item_table[46],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24]},guess_fail_reward_item={[0]=item_table[25],[1]=item_table[47],[2]=item_table[27],[3]=item_table[28],[4]=item_table[29]},},
[3]={seq=3,camp0_player_num=2,camp1_player_num=30,camp0_win_reward_item={[0]=item_table[48],[1]=item_table[49],[2]=item_table[50],[3]=item_table[51],[4]=item_table[52],[5]=item_table[53]},camp0_fail_reward_item={[0]=item_table[40],[1]=item_table[14],[2]=item_table[42],[3]=item_table[54],[4]=item_table[34],[5]=item_table[35]},camp1_win_reward_item={[0]=item_table[30],[1]=item_table[31],[2]=item_table[55],[3]=item_table[33],[4]=item_table[34],[5]=item_table[35]},camp1_fail_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[42],[3]=item_table[54],[4]=item_table[34],[5]=item_table[44]},guess_succ_reward_item={[0]=item_table[9],[1]=item_table[56],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24]},guess_fail_reward_item={[0]=item_table[25],[1]=item_table[57],[2]=item_table[27],[3]=item_table[28],[4]=item_table[29]},},
[4]={seq=4,camp0_player_num=1,camp1_player_num=31,camp0_win_reward_item={[0]=item_table[58],[1]=item_table[59],[2]=item_table[60],[3]=item_table[61],[4]=item_table[62],[5]=item_table[63]},camp0_fail_reward_item={[0]=item_table[30],[1]=item_table[2],[2]=item_table[55],[3]=item_table[33],[4]=item_table[52],[5]=item_table[53]},camp1_win_reward_item={[0]=item_table[48],[1]=item_table[49],[2]=item_table[64],[3]=item_table[51],[4]=item_table[34],[5]=item_table[53]},camp1_fail_reward_item={[0]=item_table[40],[1]=item_table[2],[2]=item_table[55],[3]=item_table[33],[4]=item_table[34],[5]=item_table[35]},guess_succ_reward_item={[0]=item_table[9],[1]=item_table[65],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24]},guess_fail_reward_item={[0]=item_table[25],[1]=item_table[66],[2]=item_table[27],[3]=item_table[28],[4]=item_table[29]},}
},

stage_meta_table_map={
},
camp={
[0]={seq=0,},
[1]={seq=1,camp_name="弑神队",}
},

camp_meta_table_map={
},
question={
[0]={seq=0,correct_answer=3,},
[1]={seq=1,question="中国历史上第一个统一的封建王朝是？",answer1="明朝",answer2="秦朝",answer3="汉朝",answer4="清朝",},
[2]={seq=2,question="《本草纲目》的作者是？",answer1="华佗",answer2="扁鹊",answer3="周杰伦",answer4="李时珍",correct_answer=4,},
[3]={seq=3,question="历史上被封为封狼居胥的有？",answer1="霍去病",answer2="李靖",answer3="朱棣",answer4="以上都是",correct_answer=4,},
[4]={seq=4,question="哪个选项不是《四大发明》之一？",answer1="蹴鞠",answer2="指南针",answer3="火药",answer4="造纸术",correct_answer=1,},
[5]={seq=5,question="古有四大文明古国，以下哪个不是？",answer1="古巴比伦",answer2="古埃及",answer3="中国",answer4="古波斯",correct_answer=4,},
[6]={seq=6,question="中国在哪一年举办了奥运会？",answer1=2006,answer2=2007,answer3=2008,answer4=2009,correct_answer=3,},
[7]={seq=7,question="哪个不是江南四大才子？",answer1="祝允明",answer2="白居易",answer3="唐寅",answer4="徐祯卿",},
[8]={seq=8,question="唐朝首都在哪？",answer1="洛阳",answer2="长安",answer3="扬州",answer4="益州",},
[9]={seq=9,question="哪个不是《四大名著》？",answer1="西游记",answer2="水浒传",answer3="三国演义",answer4="孙子兵法",correct_answer=4,},
[10]={seq=10,question="演唱歌曲《青花瓷》的歌手是？",answer1="周杰伦",answer2="张学友",answer3="刘德华",answer4="古巨基",correct_answer=1,},
[11]={seq=11,question="香港在哪一年回归祖国？",answer1="1997年7月1日",answer2="1997年8月1日",answer3="1998年7月1日",answer4="1998年8月1日",correct_answer=1,},
[12]={seq=12,question="哪个不是孙悟空绰号？",answer1="孙行者",answer2="斗战胜佛",answer3="至尊宝",answer4="美猴王",correct_answer=3,},
[13]={seq=13,question="新中国成立于哪年？",answer1="1949年10月1日",answer2="1948年10月1日",answer3="1949年11月1日",answer4="1948年9月1日",correct_answer=1,},
[14]={seq=14,question="中国两弹一星之父是？",answer1="邓稼先",answer2="袁隆平",answer3="邓小平",answer4="孙中山",correct_answer=1,},
[15]={seq=15,question="中国航天之父是？",answer1="莫言",answer2="钱学森",answer3="鲁迅",answer4="李宁",},
[16]={seq=16,question="中国杂交水稻之父是？",answer1="宋庆龄",answer2="林则徐",answer3="袁隆平",answer4="白居易",correct_answer=3,},
[17]={seq=17,question="丝绸之路的起点和终点是？",answer1="咸阳到罗马",answer2="长安到罗马",answer3="长安到敦煌",answer4="咸阳到敦煌",}
},

question_meta_table_map={
},
talent={
[0]={seq=0,},
[1]={seq=1,name="战神降临",desc="任你亿万星辰，我自一剑斩之，最好的防御就是进攻，追求极端的攻击力，只为一击杀敌，攻击值提高1000，我有三尺剑可斩日月星。",simple_desc="攻击值提高1000",product_id=11121,},
[2]={seq=2,name="不朽堡垒",desc="千捶万击还坚挺，任尔狂风骤雨袭，真正的防御是不朽的，只要击不破我的防御，那就磨没你，防御值提高1000，不朽称为不朽，就是因为难击杀。",simple_desc="防御值提高1000",product_id=11122,},
[3]={seq=3,name="攻城拔寨",desc="一气破甲两千六，凛凛剑意似当年，如果三千越甲可吞吴，那我一气破之，破甲值提高1000，十年兵甲误苍生，三千越甲可吞吴，吾皆一气破之。",simple_desc="破甲值提高1000",},
[4]={seq=4,name="魔鬼盛宴",desc="尔之灵魂，皆为吾之盛宴。我把自己的灵魂摆在魔鬼的餐桌上，只为赢得此战，生命值提高5%，哪怕出卖自己的灵魂，我都只为了战胜你。",simple_desc="生命值提高5%",},
[5]={seq=5,name="诸神黄昏",desc="真灵忘我诛仙剑，斩尽天下不正神，对于心怀不轨的神仙，那今天就是他们的黄昏，攻击力提高5%！神又如何，魔又何罢，尔之黄昏由吾带来。",simple_desc="攻击力提高5%",},
[6]={seq=6,name="牢不可破 ",desc="铜墙铁壁牢不破，坚不可摧旌旗张，只有坚不可摧加固若金汤才能守护自己想守护的一切吧，防御提高5%，我永远是你最坚强的后盾，为你遮风挡雨。",simple_desc="防御提高5%",},
[7]={seq=7,name="半步武神",desc="仙人之下我无敌，仙人之上一换一，你强任你强，我亦可强行一换一，破甲提高5%，世上本无神，由我成就半步武神。",simple_desc="破甲提高5%",},
[8]={seq=8,name="幽灵疾步",desc="迅疾月边捎玉兔，迟回日里拂金鸡，打不过还跑不过吗，只要你打不到我，就不会对我产生伤害，速度提高200%，天下武功，唯快不破。",simple_desc="速度提高200%",},
[9]={seq=9,name="黑色天灾",desc="吾之双足踏出战火。吾之双手紧握毁灭，吾名天帝，无敌时间5秒，在这5秒之内，不会死亡，我是一个百战不死的人，天难灭，地难葬！",simple_desc="无敌持续5秒",},
[10]={seq=10,name="地狱葬歌",desc="我在聆听自己的葬歌，生又如何，死又如何，从地狱中来，回地狱中去，闪避值提高200%，我是很狂妄，但我有狂妄的本钱，我无需知道死人的名字。",simple_desc="闪避提高200%",},
[11]={seq=11,name="一指剑意",desc="此剑抚平天下不事，此剑无愧世间有愧人，心中无悔，即可尽情出剑，反弹伤害200%，吾有四十年郁气出不得，今日不得不一吐胸臆。",simple_desc="反弹伤害200%",},
[12]={seq=12,skill_id=7208,name="无尽征战",desc="仙路尽头谁为峰，一见唯我道成空，无敌资质，神挡杀神，魔挡杀魔，生命恢复150%，吾可独断万古，唯负了她",talent_type=2,simple_desc="蓝银领域",product_id=0,},
[13]={seq=13,skill_id=7209,name="无极之道",desc="活在当世，何惧未来，对敌人的仁慈，就是对自己残忍，对敌人造成150%多段伤害，我可以摘星捉月，我可以粉碎星空，却不能让你活下来",talent_type=2,simple_desc="赤炎领域",product_id=0,},
[14]={seq=14,skill_id=7210,name="破碎虚空",desc="我若成神，魔奈我何，我若成魔，佛又何惧，神魔见我需低眉，对敌人造成150%多段伤害，天不生我无敌者，仙途万古如长夜",talent_type=2,simple_desc="圣盾领域",product_id=0,},
[15]={seq=15,skill_id=7211,name="苍穹天变",desc="天上剑仙三百万，见我也须尽低眉，如果无敌是一个代词，那就只能是我的代号，对敌人造成150%多段伤害，何必为死人写诗歌，不如死在此地留遗言。",talent_type=2,simple_desc="噬魂领域",product_id=0,}
},

talent_meta_table_map={
[4]=1,	-- depth:1
[5]=2,	-- depth:1
[7]=1,	-- depth:1
[8]=2,	-- depth:1
[10]=1,	-- depth:1
[11]=2,	-- depth:1
},
talent_refresh={
[1]={question_times=1,},
[2]={question_times=2,},
[3]={question_times=3,refresh_times=2,min_pro=40,max_pro=60,},
[4]={question_times=4,},
[5]={question_times=5,refresh_times=3,min_pro=70,max_pro=80,},
[6]={question_times=6,},
[7]={question_times=7,refresh_times=4,min_pro=81,max_pro=89,},
[8]={question_times=8,},
[9]={question_times=9,refresh_times=5,min_pro=90,max_pro=100,},
[10]={question_times=10,}
},

talent_refresh_meta_table_map={
[4]=3,	-- depth:1
[6]=5,	-- depth:1
[8]=7,	-- depth:1
[10]=9,	-- depth:1
},
falling={
[11]={seq=11,},
[12]={seq=12,product_id=21120,},
[13]={seq=13,product_id=21121,},
[14]={seq=14,product_id=21122,}
},

falling_meta_table_map={
},
score_reward={
[0]={seq=0,},
[1]={seq=1,need_score=800,reward_item={[0]=item_table[67],[1]=item_table[68],[2]=item_table[25],[3]=item_table[69],[4]=item_table[70]},},
[2]={seq=2,need_score=1200,reward_item={[0]=item_table[67],[1]=item_table[71],[2]=item_table[9],[3]=item_table[72],[4]=item_table[70]},},
[3]={seq=3,need_score=1600,reward_item={[0]=item_table[67],[1]=item_table[73],[2]=item_table[9],[3]=item_table[74],[4]=item_table[70]},},
[4]={seq=4,need_score=2000,reward_item={[0]=item_table[67],[1]=item_table[75],[2]=item_table[76],[3]=item_table[77],[4]=item_table[70]},},
[5]={seq=5,need_score=2400,reward_item={[0]=item_table[78],[1]=item_table[79],[2]=item_table[15],[3]=item_table[80],[4]=item_table[70]},},
[6]={seq=6,need_score=2800,reward_item={[0]=item_table[78],[1]=item_table[81],[2]=item_table[15],[3]=item_table[82],[4]=item_table[70]},},
[7]={seq=7,need_score=3200,reward_item={[0]=item_table[78],[1]=item_table[81],[2]=item_table[32],[3]=item_table[83],[4]=item_table[70]},},
[8]={seq=8,need_score=3600,reward_item={[0]=item_table[78],[1]=item_table[84],[2]=item_table[32],[3]=item_table[85],[4]=item_table[70]},},
[9]={seq=9,need_score=4200,reward_item={[0]=item_table[78],[1]=item_table[84],[2]=item_table[86],[3]=item_table[87],[4]=item_table[70]},}
},

score_reward_meta_table_map={
},
score_rank_reward={
{},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[88],[1]=item_table[89],[2]=item_table[55],[3]=item_table[90],[4]=item_table[91],[5]=item_table[92]},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[93],[1]=item_table[89],[2]=item_table[94],[3]=item_table[90],[4]=item_table[91],[5]=item_table[95]},},
{min_rank=4,max_rank=5,reward_item={[0]=item_table[96],[1]=item_table[42],[2]=item_table[97],[3]=item_table[98],[4]=item_table[99]},},
{min_rank=6,max_rank=8,reward_item={[0]=item_table[100],[1]=item_table[15],[2]=item_table[97],[3]=item_table[98],[4]=item_table[33]},},
{min_rank=9,max_rank=10,reward_item={[0]=item_table[101],[1]=item_table[15],[2]=item_table[102],[3]=item_table[103],[4]=item_table[104]},},
{min_rank=11,max_rank=15,reward_item={[0]=item_table[105],[1]=item_table[9],[2]=item_table[102],[3]=item_table[103],[4]=item_table[43]},},
{min_rank=16,max_rank=20,reward_item={[0]=item_table[106],[1]=item_table[9],[2]=item_table[107],[3]=item_table[108],[4]=item_table[43]},},
{min_rank=21,max_rank=25,reward_item={[0]=item_table[109],[1]=item_table[25],[2]=item_table[107],[3]=item_table[108],[4]=item_table[4]},},
{min_rank=26,max_rank=32,reward_item={[0]=item_table[109],[1]=item_table[25],[2]=item_table[107],[3]=item_table[108],[4]=item_table[110]},}
},

score_rank_reward_meta_table_map={
},
convert={
[0]={seq=0,times_limit=1,stuff_num_1=1000,},
[1]={seq=1,item=item_table[111],},
[2]={seq=2,times_limit=1,item=item_table[112],stuff_num_1=1400,},
[3]={seq=3,item=item_table[113],},
[4]={seq=4,times_limit=1,item=item_table[114],stuff_num_1=385,},
[5]={seq=5,item=item_table[115],},
[6]={seq=6,item=item_table[116],},
[7]={seq=7,item=item_table[117],},
[8]={seq=8,item=item_table[118],},
[9]={seq=9,item=item_table[119],},
[10]={seq=10,item=item_table[120],},
[11]={seq=11,item=item_table[121],},
[12]={seq=12,times_limit=1,item=item_table[122],stuff_num_1=1200,},
[13]={seq=13,times_limit=1,item=item_table[123],stuff_num_1=800,},
[14]={seq=14,times_limit=1,item=item_table[124],stuff_num_1=700,},
[15]={seq=15,times_limit=1,item=item_table[125],stuff_num_1=400,},
[16]={seq=16,item=item_table[126],},
[17]={seq=17,times_limit=1,item=item_table[127],stuff_num_1=180,},
[18]={seq=18,item=item_table[128],},
[19]={seq=19,item=item_table[129],},
[20]={seq=20,times_limit=9999,item=item_table[130],stuff_num_1=7,},
[21]={seq=21,item=item_table[131],},
[22]={seq=22,times_limit=9999,item=item_table[132],stuff_num_1=60,},
[23]={seq=23,item=item_table[133],},
[24]={seq=24,item=item_table[134],},
[25]={seq=25,item=item_table[135],},
[26]={seq=26,item=item_table[136],},
[27]={seq=27,item=item_table[137],},
[28]={seq=28,item=item_table[138],},
[29]={seq=29,item=item_table[139],},
[30]={seq=30,item=item_table[140],},
[31]={seq=31,item=item_table[141],},
[32]={seq=32,item=item_table[142],},
[33]={seq=33,item=item_table[143],},
[34]={seq=34,item=item_table[144],},
[35]={seq=35,item=item_table[145],},
[36]={seq=36,item=item_table[146],},
[37]={seq=37,item=item_table[147],},
[38]={seq=38,item=item_table[148],},
[39]={seq=39,item=item_table[149],stuff_num_1=150,},
[40]={seq=40,item=item_table[150],},
[41]={seq=41,item=item_table[151],},
[42]={seq=42,item=item_table[152],},
[43]={seq=43,item=item_table[153],},
[44]={seq=44,item=item_table[154],},
[45]={seq=45,item=item_table[155],},
[46]={seq=46,item=item_table[156],}
},

convert_meta_table_map={
[40]=39,	-- depth:1
[41]=39,	-- depth:1
[42]=39,	-- depth:1
[43]=39,	-- depth:1
[44]=39,	-- depth:1
[45]=39,	-- depth:1
[46]=39,	-- depth:1
[1]=0,	-- depth:1
[3]=2,	-- depth:1
[5]=4,	-- depth:1
[6]=4,	-- depth:1
[7]=4,	-- depth:1
[8]=4,	-- depth:1
[9]=4,	-- depth:1
[10]=4,	-- depth:1
[16]=14,	-- depth:1
[18]=0,	-- depth:1
[19]=14,	-- depth:1
[11]=4,	-- depth:1
[21]=20,	-- depth:1
},
barrage={
[0]={seq=0,},
[1]={seq=1,barrage_content="大佬，我要给你生猴子",},
[2]={seq=2,barrage_content="广东吴彦祖发来贺电",},
[3]={seq=3,barrage_content="还有谁！我要打10个",},
[4]={seq=4,barrage_content="别动，吻我",},
[5]={seq=5,barrage_content="666666啊，真滴强",},
[6]={seq=6,barrage_content="这波操作，我给满分",},
[7]={seq=7,barrage_content="1打31都能成功？牛批plus",},
[8]={seq=8,barrage_content="我就问，大佬缺女朋友吗，情人呢",},
[9]={seq=9,barrage_content="佛山电焊本焊发来贺电",},
[10]={seq=10,barrage_content="湖南彭于晏发来贺电",},
[11]={seq=11,barrage_content=66666666666666,},
[12]={seq=12,barrage_content="斗罗小陆唐四发来贺电",},
[13]={seq=13,barrage_content="祝大佬一年抱一，明年抱俩",},
[14]={seq=14,barrage_content="马栏山大学校长发来贺电",},
[15]={seq=15,barrage_content="我就不信，还有人能打赢你",},
[16]={seq=16,barrage_content="大哥，打BOSS带带我，我负责扣666",},
[17]={seq=17,barrage_content="大哥，我是你的小迷妹",},
[18]={seq=18,barrage_content="爱你不是两三天，而是每一天",},
[19]={seq=19,barrage_content="爱我，别走，如果你说，你不爱我",},
[20]={seq=20,barrage_content="上去给你一套QWER连招，舔晕你",},
[21]={seq=21,barrage_content="大哥大哥，买瓜不，包熟",},
[22]={seq=22,barrage_content="就这，就这？？？",},
[23]={seq=23,barrage_content="别问，问就是我大哥",},
[24]={seq=24,barrage_content="哥，收小弟不？我拍马屁贼溜",},
[25]={seq=25,barrage_content="红红火火恍恍惚惚",},
[26]={seq=26,barrage_content="哈哈哈哈哈哈h",},
[27]={seq=27,barrage_content="好嗨哟，感觉人生已经达到了巅峰",},
[28]={seq=28,barrage_content="谁都不服，就服你",},
[29]={seq=29,barrage_content="你也不行啊，老弟",},
[30]={seq=30,barrage_content="上去就是一锤子",},
[31]={seq=31,barrage_content="一般，嗯，真的一般",},
[32]={seq=32,barrage_content="我不信，除非你倒立吃翔",}
},

barrage_meta_table_map={
},
skill={
[7208]={skill_id=7208,},
[7209]={skill_id=7209,skill_asset="zjzc_skill02",sound_asset="miji2",},
[7210]={skill_id=7210,skill_asset="zjzc_skill03",sound_asset="miji3",},
[7211]={skill_id=7211,skill_asset="zjzc_skill04",sound_asset="miji4",}
},

skill_meta_table_map={
},
robot={
{},
{},
{},
{},
{},
{},
{},
{}
},

robot_meta_table_map={
},
other_default_table={open_day=16,open_level=300,standy_scene_id=2001,scene_id=2002,room_player_num_max=32,kill_score=50,attend_kill_score=20,dead_score=10,guess_time=10,question_num=10,question_time=10,question_standby_time=120,random_talent_num=3,gain_exp_time=10,exp_coefficient=5,camp0_pos="164,228",camp1_pos="103,77",worship_reward_item={[0]=item_table[25],[1]=item_table[157],[2]=item_table[158],[3]=item_table[159],[4]=item_table[70]},be_worship_reward_item={[0]=item_table[25],[1]=item_table[157],[2]=item_table[158],[3]=item_table[159],[4]=item_table[70]},find_postion="240,282",worship_times=3,send_barrage_reward_times=3,send_barrage_cd=3,send_barrage_reward_item={[0]=item_table[25],[1]=item_table[157],[2]=item_table[158],[3]=item_table[159],[4]=item_table[70]},barrage_show_time=30,title_id=9015,},

stage_default_table={seq=0,time=180,camp0_player_num=16,camp1_player_num=16,camp0_win_reward_item={[0]=item_table[19],[1]=item_table[8],[2]=item_table[9],[3]=item_table[110],[4]=item_table[160],[5]=item_table[12]},camp0_fail_reward_item={[0]=item_table[161],[1]=item_table[8],[2]=item_table[25],[3]=item_table[162],[4]=item_table[163],[5]=item_table[164]},camp1_win_reward_item={[0]=item_table[19],[1]=item_table[8],[2]=item_table[9],[3]=item_table[110],[4]=item_table[160],[5]=item_table[12]},camp1_fail_reward_item={[0]=item_table[161],[1]=item_table[8],[2]=item_table[25],[3]=item_table[162],[4]=item_table[163],[5]=item_table[164]},guess_succ_reward_item={[0]=item_table[9],[1]=item_table[165],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24]},guess_fail_reward_item={[0]=item_table[25],[1]=item_table[166],[2]=item_table[27],[3]=item_table[28],[4]=item_table[29]},},

camp_default_table={seq=0,camp_name="登神者",stage_win_score=30,stage_fail_score=20,},

question_default_table={seq=0,question="李白，字太白，号青莲居士，唐朝浪漫主义诗人，被后人誉为？",answer1="诗神",answer2="诗圣",answer3="诗仙",answer4="诗杰",correct_answer=2,succ_reward_item={[0]=item_table[167],[1]=item_table[168],[2]=item_table[169],[3]=item_table[70]},fail_reward_item={[0]=item_table[170],[1]=item_table[171],[2]=item_table[159],[3]=item_table[70]},},

talent_default_table={seq=0,need_score=100,skill_id=0,skill_cd_time=10,name="灵魂熔炉",desc="我以我魂炼于炉，只为无敌天地间。在熔炉里面炼器自己的灵魂，增强自己的体魄 ，生命值上限提高10000，有我无敌，活着才能更好的输出。",talent_type=1,simple_desc="生命提升10000",product_id=11120,},

talent_refresh_default_table={question_times=1,refresh_times=1,min_pro=10,max_pro=30,},

falling_default_table={seq=11,product_id=0,},

score_reward_default_table={seq=0,need_score=400,reward_item={[0]=item_table[67],[1]=item_table[172],[2]=item_table[25],[3]=item_table[173],[4]=item_table[70]},},

score_rank_reward_default_table={min_rank=1,max_rank=1,reward_item={[0]=item_table[174],[1]=item_table[175],[2]=item_table[176],[3]=item_table[177],[4]=item_table[178],[5]=item_table[51]},},

convert_default_table={seq=0,times_limit=10,item=item_table[179],stuff_id_1=57993,stuff_num_1=100,stuff_id_2=0,stuff_num_2=0,stuff_id_3=0,stuff_num_3=0,},

barrage_default_table={seq=0,barrage_content="老板太强了，无敌6666",},

skill_default_table={skill_id=7208,skill_bundle="effects2/prefab/zhanchang_prefab",skill_asset="zjzc_skill01",sound_bundle="audios/sfxs/roleskill/jineng",sound_asset="miji1",is_target=0,},

robot_default_table={}

}

