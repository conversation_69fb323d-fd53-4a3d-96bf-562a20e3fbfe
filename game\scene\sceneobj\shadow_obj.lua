ShadowObj = ShadowObj or BaseClass(VisibleObj)

function ShadowObj:__init()
	self.shield_obj_type = ShieldObjType.Shadow
end

function ShadowObj:__delete()
	if self.loader then
		self.loader:DeleteMe()
		self.loader = nil
	end
end

function ShadowObj:Create(node, shield_obj_type)
	self.loader = self.loader or AllocAsyncLoader(self, "shadow_loader")
	self.loader:SetParent(node)
	self.loader:SetIsUseObjPool(true)
	self.loader:SetIsInQueueLoad(true)
	if shield_obj_type then
		self.shield_obj_type = shield_obj_type
	end
	self:CreateShieldHandle()
end

function ShadowObj:Destroy()
	if self.loader then
		self.loader:Destroy()
	end
end

function ShadowObj:VisibleChanged(visible)
	if self.loader then
		if visible then
			self.loader:Load("actors/shadow_prefab", "ObjShadow")
		else
			self.loader:Destroy()
		end
	end
end