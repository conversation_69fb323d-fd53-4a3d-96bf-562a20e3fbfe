-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local M = {}
local GAME_OBJ_LOADERS = {}
M._class_type = true

local function LoadCallBack(obj, game_obj_attach)
	-- if G_IsDeveloper and obj and obj:GetComponentInChildren(typeof(UnityEngine.TrailRenderer)) and nil == obj:GetComponent(typeof(TrailRendererController)) then
	-- 	print_error("not exists TrailRendererController component!!!", obj.name)
	-- end

	if not IsNil(game_obj_attach) then
		game_obj_attach:OnLoadComplete(obj)
	end
end

function M.EnableGameObjAttachEvent(enabled_list)
	for i = 0, enabled_list.Count - 1 do
		local game_obj_attach = enabled_list[i]
		if not IsNil(game_obj_attach) then
			local bundle_name, asset_name = game_obj_attach.BundleName, game_obj_attach.AssetName
			if bundle_name and bundle_name ~= "" and
				asset_name and asset_name ~= "" then

				local loader = M.AllocLoader(game_obj_attach)
				loader:SetLoadPriority(ResLoadPriority.low)
				loader:SetIsUseObjPool(true)
				loader:Load(bundle_name, asset_name, LoadCallBack, game_obj_attach)
			end
		end
	end
end

-- 这里传的是gameobjattach
function M.DisableGameObjAttachEvent(disabled_list)
	for i = 0, disabled_list.Count - 1 do
		local game_obj_attach = disabled_list[i]
		if nil ~= game_obj_attach and nil ~= M.__gameobj_loaders then
			local loader_key = "id_" .. game_obj_attach:GetInstanceID()
			local gameobj_loader = M.__gameobj_loaders[loader_key]
			if gameobj_loader then
				gameobj_loader:Destroy()
			end
		end
	end
end

-- 这里传的是gameobjattach的数组
function M.DisableGameObjAttachArray(disabled_list)
	for i = 0, disabled_list.Length - 1 do
		local game_obj_attach = disabled_list[i]
		if nil ~= game_obj_attach and nil ~= M.__gameobj_loaders then
			local loader_key = "id_" .. game_obj_attach:GetInstanceID()
			local gameobj_loader = M.__gameobj_loaders[loader_key]
			if gameobj_loader then
				gameobj_loader:Destroy()
			end
		end
	end
end

-- 这里传的是instance_id
function M.DestroyGameObjAttachEvent(destroyed_list)
	for i = 0, destroyed_list.Count - 1 do
		DelGameObjLoader(M, "id_" .. destroyed_list[i])
	end
end

function M.AllocLoader(game_obj_attach)
	local loader = AllocAsyncLoader(M, "id_" .. game_obj_attach:GetInstanceID())
	loader:SetParent(game_obj_attach.transform)
	return loader
end

GameObjAttachEventHandle = M

return M
