BOSSInvasionMainQualityTip = BOSSInvasionMainQualityTip or BaseClass(SafeBaseView)

function BOSSInvasionMainQualityTip:__init()
	self.view_layer = UiLayer.MainUIHigh
	self:AddViewResource(0, "uis/view/boss_invasion_ui_prefab", "layout_boss_invasion_main_quality_tip")
end

function BOSSInvasionMainQualityTip:SetDataAndOpen(time_data)
	if IsEmptyTable(time_data) then
		return
	end

	self.time_data = time_data
	self:FlushTime()
	if self:IsOpen() then
		self:Flush()
	else
		self:Open()
	end
end

function BOSSInvasionMainQualityTip:LoadCallBack()
    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["model_root"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
		
		self:FlushModel()
	end

	self.node_list.desc_boss_tip.text.text = Language.BOSSInvasion.MianQualityTip
	XUI.AddClickEventListener(self.node_list.btn_jump, BindTool.Bind(self.OnClickJumpBtn, self)) 
end

function BOSSInvasionMainQualityTip:CloseCallBack()
	BOSSInvasionWGCtrl.Instance:CheckShowMainQualityTip()
end

function BOSSInvasionMainQualityTip:ReleaseCallBack()
	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end
end

function BOSSInvasionMainQualityTip:OnFlush()
	local cur_coss_color_cfg = BOSSInvasionWGData.Instance:GetCurBossColorCfg()
	if cur_coss_color_cfg and cur_coss_color_cfg.tip_bg then
		local bundle, asset = ResPath.GetRawImagesPNG(cur_coss_color_cfg.tip_bg)
		self.node_list.bg.raw_image:LoadSprite(bundle, asset)
	end

	local cur_boss_num = BOSSInvasionWGData.Instance:GetCurBossNum()
	local next_quality_cfg = BOSSInvasionWGData.Instance:GetNextBossColorListCfg()
	self.node_list.desc_boss_num.text.text = string.format(Language.BOSSInvasion.MianQualityTipSlider, cur_boss_num, next_quality_cfg.need_num)

	local slider_value = BOSSInvasionWGData.Instance:CalBossQualitySLidervalue()
	self.node_list.slider.slider.value = slider_value
end

function BOSSInvasionMainQualityTip:FlushTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local now_day_start_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)
	local end_time = now_day_start_time + math.floor(self.time_data.end_time / 100) * 60 * 60 + (self.time_data.end_time % 100) * 60

	if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
        self.delay_timer = nil
    end

	self.delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
	end, end_time - server_time + 1)
end

function BOSSInvasionMainQualityTip:FlushModel()
	local model_data = BOSSInvasionWGData.Instance:GetOtherCfg()
	local display_data = {}
	display_data.should_ani = true

	if model_data.tip_model_show_itemid ~= 0 and model_data.tip_model_show_itemid ~= "" then
		local split_list = string.split(model_data.tip_model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_data.tip_model_show_itemid
		end
	end

	display_data.bundle_name = model_data.tip_model_bundle_name
    display_data.asset_name = model_data.tip_model_asset_name
	local model_show_type = tonumber(model_data["tip_model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
	display_data.model_rt_type = ModelRTSCaleType.M
	display_data.camera_depth = -3
	display_data.can_drag = false

	if model_data.tip_model_pos and model_data.tip_model_pos ~= "" then
		local pos_list = string.split(model_data.tip_model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if model_data.tip_model_rot and model_data.tip_model_rot ~= "" then
		local rot_list = string.split(model_data.tip_model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_data.tip_model_scale and model_data.tip_model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_data.tip_model_scale
	end

	self.model_display:SetData(display_data)
end

function BOSSInvasionMainQualityTip:OnClickJumpBtn()
	BOSSInvasionWGCtrl.Instance:OpenQualityView()
	self:Close()
end