LingYaoMaiHuoView = LingYaoMaiHuoView or BaseClass(SafeBaseView)

function LingYaoMaiHuoView:__init(view_name)
	self.view_style = ViewStyle.Full
	self.view_name = "LingYaoMaiHuoView"
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/lingyaomaihuo_ui_prefab", "layout_lingyaomaihuo")
	self.m_cfg_list = nil
end

function LingYaoMaiHuoView:__delete()

end

function LingYaoMaiHuoView:ReleaseCallBack()
	self.m_cfg_list = nil
	if self.reward_item_list then
		for k,v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end
	CountDownManager.Instance:RemoveCountDown("lingyaomaihuo")
end

function LingYaoMaiHuoView:OpenCallBack()
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACT_LINGYAOMAIHUO, opera_type = CS_RA_BUDDHABAG_OPERA_TYPE.CS_RA_BUDDHABAG_OPERA_TYPE_INFO})
end

function LingYaoMaiHuoView:LoadCallBack()
	self.reward_item_list = {}
	self.node_list.buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self))
	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.Close, self))
end

function LingYaoMaiHuoView:OnFlush()
	local index = ServerActivityWGData.Instance:GetLingYaoMaiHuoIndex()
	local cfg_list = ServerActivityWGData.Instance:GetLingYaoMaiHuoCfg(index)
	self.m_cfg_list = cfg_list
	if cfg_list then
		self.node_list.buy_btn_label.text.text = cfg_list.btn_label or ""
	else
		self.node_list.buy_btn_label.text.text = Language.OpenServer.XieXieHuiGu
	end
	XUI.SetButtonEnabled(self.node_list.buy_btn, cfg_list ~= nil)
	self.node_list.effect_root:SetActive(cfg_list ~= nil)

	self:FlushTime()
	self:FlushBanner()
	self:FlushReward()
end

function LingYaoMaiHuoView:FlushTime()
	local sub_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACT_LINGYAOMAIHUO)
	sub_time = sub_time > 0 and sub_time or 0
	self.node_list.time_label.text.text = Language.OpenServer.ActRemainTime .. TimeUtil.FormatSecondDHM2(sub_time)
	if sub_time > 0 then
		CountDownManager.Instance:AddCountDown("lingyaomaihuo", function (time, total_time)
			self.node_list.time_label.text.text = Language.OpenServer.ActRemainTime .. TimeUtil.FormatSecondDHM(total_time - time)
		end, nil, nil, sub_time, 60)
	end
end

function LingYaoMaiHuoView:FlushBanner()
	local index = ServerActivityWGData.Instance:GetLingYaoMaiHuoIndex(true)
	local max_index = ServerActivityWGData.Instance:GetLingYaoMaiHuoMaxIndex()
	local banner = nil
	for i = 0, max_index do
		banner = self.node_list["price_img_" .. i]
		if banner then
			banner:SetActive(i == index)
		else
			break
		end
	end
	if max_index+1  == index then
		self.node_list["price_img_1"]:SetActive(true)
	end
end

function LingYaoMaiHuoView:FlushReward()
	local cfg_list = self.m_cfg_list
	if not cfg_list then
		return
	end
	local reward_list = cfg_list.reward_item
	local item_list = self.reward_item_list
	local leng = GetTableLen(reward_list)

	if leng > #item_list then
		local parent = self.node_list.reward_content
		for i = #item_list + 1, leng do
			item_list[i] = ItemCell.New(parent)
		end
		self.reward_item_list = item_list 
	end

	local data = nil
	for i=1,#item_list do
		data = reward_list[i - 1]
		if data then
			item_list[i]:SetData(data)
		end
		item_list[i]:SetActive(data ~= nil)
	end
end

function LingYaoMaiHuoView:OnClickBuyBtn()
	local cfg_list = self.m_cfg_list
	if not cfg_list then
		return
	end
	local gold = RoleWGData.Instance:GetAttr('gold')
	if gold < cfg_list.price then
		VipWGCtrl.Instance:OpenTipNoGold()
		return
	end
	if cfg_list.price >= COMMON_CONSTS.AlertConst then
		TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat3, cfg_list.price, cfg_list.name or ""), function ()
			ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_LINGYAOMAIHUO, CS_RA_BUDDHABAG_OPERA_TYPE.CS_RA_BUDDHABAG_OPERA_TYPE_BUY, cfg_list.index)
		end)
	else
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_LINGYAOMAIHUO, CS_RA_BUDDHABAG_OPERA_TYPE.CS_RA_BUDDHABAG_OPERA_TYPE_BUY, cfg_list.index)
	end
end