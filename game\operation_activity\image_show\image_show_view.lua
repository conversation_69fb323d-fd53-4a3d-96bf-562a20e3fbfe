OperationActivityView = OperationActivityView or BaseClass(SafeBaseView)

function OperationActivityView:InitImageShow()
	
end

function OperationActivityView:LoadIndexCallBackImageShow()
	self.node_list["is_btn_get"].button:AddClickListener(BindTool.Bind(self.OnClickISGoToGet,self))
	self.image_show_interface_cfg = ImageShowWGData.Instance:GetImageShowFaceCfg()
	self.image_show_cur_cfg = ImageShowWGData.Instance:GetImageShowCurCfg()
	self:FlushImageShowPictrue()
	self.is_list_view = AsyncListView.New(ImageShowCell, self.node_list.is_list)
	self.is_list_view:SetSelectCallBack(BindTool.Bind(self.OnClickImageShowCell,self))

end

function OperationActivityView:DeleteImageShow()
	self.cur_is_show_data = nil
	if self.is_model_display then
		self.is_model_display:DeleteMe()
		self.is_model_display = nil
	end
	if self.is_list_view then
		self.is_list_view:DeleteMe()
		self.is_list_view = nil
	end
	if self.image_show_head_cell then
		self.image_show_head_cell:DeleteMe()
		self.image_show_head_cell = nil
	end

	if self.is_tween_yoyo then
		self.is_tween_yoyo:Kill()
		self.is_tween_yoyo = nil
	end

	if self.bubble_loader then
		self.bubble_loader:DeleteMe()
		self.bubble_loader = nil
	end
	
	self:CancelWeaponTween()
	-- self:ClearFootEff()
end

function OperationActivityView:ShowIndexCallImageShow()

end

function OperationActivityView:FlushImageShow()
	self:SetRuleInfoActive(false)
	self:SetTimeInfoActive(false)
	self:FlushImageShowListData()
	self:FlushIsModel()
end

--刷新ui
function OperationActivityView:FlushImageShowPictrue()
	if not self.image_show_interface_cfg or IsEmptyTable(self.image_show_interface_cfg) then
		return
	end

	self.node_list.is_big_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(self.image_show_interface_cfg.is_big_bg))
	self.node_list.is_big_bg.raw_image:SetNativeSize()


	self.node_list.is_list_big_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(self.image_show_interface_cfg.is_list_big_bg))
	--self.node_list.is_list_big_bg.image:SetNativeSize()

	self.node_list.is_list_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(self.image_show_interface_cfg.list_bg))
	--self.node_list.is_list_bg.image:SetNativeSize()

	self.node_list.is_name_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(self.image_show_interface_cfg.is_name_bg))
	self.node_list.is_name_bg.image:SetNativeSize()
end

function OperationActivityView:FlushImageShowListData()

	if IsEmptyTable(self.image_show_cur_cfg) then
		return
	end
	local grade = self.image_show_cur_cfg.grade or 0
	local list_data = ImageShowWGData.Instance:GetImageShowRewardData(grade)
	table.sort(list_data, function (a,b)
		local a_cap = ImageShowWGData.Instance:GetItemMaxCapById(a.item_id)
		local b_cap = ImageShowWGData.Instance:GetItemMaxCapById(b.item_id)
		return a_cap > b_cap
	end)

	if not self.cur_is_show_data and list_data and list_data[1] then
		self.cur_is_show_data = list_data[1]
		ImageShowWGData.Instance:SetCurShowItemID(self.cur_is_show_data.item_id)
	end
	self.is_list_view:SetDataList(list_data)
end

function OperationActivityView:OnClickISGoToGet()
	if not self.cur_is_show_data then return end
	ViewManager.Instance:OpenByCfg(self.cur_is_show_data.open_panel)
end

function OperationActivityView:OnClickImageShowCell(cell)
	local cell_data = cell:GetData()
	self.cur_is_show_data = cell_data
	ImageShowWGData.Instance:SetCurShowItemID(self.cur_is_show_data.item_id)
	self.is_list_view:RefreshActiveCellViews()

	-- self:ClearFootEff()
	self:FlushIsModel()
end

-- 模型展示
function OperationActivityView:FlushIsModel()
	self.node_list["is_bubble_cell_root"]:SetActive(false)
	self.node_list["is_head_cell_root"]:SetActive(false)
	self.node_list["is_title_cell_root"]:SetActive(false)
	self.node_list["is_role_model"]:SetActive(false)
	if not self.cur_is_show_data or not self.cur_is_show_data.item_id then 
		return 
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.cur_is_show_data.item_id)
	if item_cfg == nil then
		print_error("DisplayItemTip","ShowDisplay() item_cfg is a nil value: item_id = ",self.cur_is_show_data.item_id)
		return
	end
	local capability = ItemShowWGData.CalculateCapability(self.cur_is_show_data.item_id, true)
	local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_cfg.sys_attr_cap_location)
		if sys_type == ITEMTIPS_SYSTEM.MOUNT or
			sys_type == ITEMTIPS_SYSTEM.LING_CHONG or
			sys_type == ITEMTIPS_SYSTEM.HUA_KUN or
			sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA or
			sys_type == ITEMTIPS_SYSTEM.FASHION or
			sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
			self.node_list["is_common_max_capability_style"]:SetActive(true)
			self.node_list["is_common_capability_style"]:SetActive(false)
			self.node_list["is_max_cap_value"].text.text = capability or 0
		else
			self.node_list["is_common_max_capability_style"]:SetActive(false)
			self.node_list["is_common_capability_style"]:SetActive(true)
			self.node_list["is_cap_value"].text.text = capability or 0
		end

		self.node_list["is_name_text"].text.text = item_cfg.name or ""

	if item_cfg.id and item_cfg.id > 0 then
		item_cfg = ItemWGData.Instance:GetItemConfig(item_cfg.id)
		if item_cfg == nil then
			print_error("DisplayItemTip","ShowDisplay() item_cfg is a nil value: id = ",item_cfg.id)
			return
		end
	end

	local display_type = item_cfg.is_display_role
	if nil == display_type or item_cfg.is_display_role == 0 or item_cfg.is_display_role == "" then
		return
	end

	if not self.is_model_display then
    	self.is_model_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["is_role_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.is_model_display:SetRenderTexUI3DModel(display_data)
		-- local event_trigger = self.node_list["is_model_event"].event_trigger_listener
	--self.node_list["model_event"]:SetActive(is_need_event_trigger)
		-- self.is_model_display:SetUI3DModel(self.node_list["is_role_model"].transform, event_trigger, 1, nil, MODEL_CAMERA_TYPE.BASE)
		-- self.is_model_display:SetCameraOffsetType(nil)
	else
		self.is_model_display:ClearModel()
	end

	self.is_model_display:RemoveFootTrail()
    self:CancelWeaponTween()

	local path, bundle, asset, res_id, image_type, attr_cfg, animation_type, name, part_type
	self.is_model_display:PlayRoleAction(SceneObjAnimator.UiIdle)

	if display_type == DisplayItemTip.Display_type.MOUNT_LINGCHONG then 			--坐骑
        res_id, attr_cfg = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_cfg.id)
		local item_id
		if item_cfg.param2 then
			item_id = string.split(item_cfg.param2, "|")
		end
		res_id = res_id or item_id[1]
		path = ResPath.GetMountModel
        animation_type = "mount"
        self.node_list["is_role_model"]:SetActive(true)
    elseif display_type == DisplayItemTip.Display_type.LINGCHONG then 			--灵宠
        res_id, attr_cfg = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_cfg.id)
        local item_id
        if item_cfg.param2 then
            item_id = string.split(item_cfg.param2, "|")
        end
        res_id = res_id or item_id[1]
        path = ResPath.GetPetModel
        animation_type = "soul"
        self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.KUN then 				--鲲
		path = ResPath.GetMountModel
		local kun_base_cfg = NewAppearanceWGData.Instance:GetKunActCfgByItemId(item_cfg.id)
		if kun_base_cfg then
			res_id = kun_base_cfg.active_id
		end
		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.FASHION then
		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		part_type = part_type or item_cfg.param1
		path, res_id, animation_type = self:GetFashionModlePathFun(res_id, part_type, item_cfg)

		if part_type == SHIZHUANG_TYPE.HALO then
			self.is_model_display:SetMainAsset(ResPath.GetHaloModel(res_id))
		else
			self:ShowRoleModel(res_id)
			local _, weapon_res_id, __ = AppearanceWGData.Instance:GetRoleResId()

			self.is_model_display:SetWeaponResid(weapon_res_id)
		end
		self.node_list["is_role_model"]:SetActive(true)
		return
	elseif display_type == DisplayItemTip.Display_type.LIANSHI or
		display_type == DisplayItemTip.Display_type.YAOSHI or
		display_type == DisplayItemTip.Display_type.WEIBA or
		display_type == DisplayItemTip.Display_type.SHOUHUAN or
		display_type == DisplayItemTip.Display_type.FOOT then 				--时装   + 默认的剑-- +当前武器模型

		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		part_type = part_type or item_cfg.param1

		if res_id and part_type then
			path, res_id, animation_type = self:GetFashionModlePathFun(res_id, part_type, item_cfg)
		end

		if part_type == SHIZHUANG_TYPE.FOOT then--足迹不展示武器
			self.is_model_display:SetFootTrailModel(res_id)
			self.is_model_display:PlayRoleAction(SceneObjAnimator.Move)
			self.is_model_display:SetRotation(MODEL_ROTATION_TYPE.FOOT)
		else
			local _, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
			self.is_model_display:SetWeaponResid(weapon_res_id)
		end
		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.WING then 						--羽翼
		--1.获取翅膀模型
		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		local item_id
		if item_cfg.param2 then
			item_id = string.split(item_cfg.param2, "|")
		end
		res_id = res_id or item_id[1]
		path = ResPath.GetWingModel
		animation_type = "wing"
		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI then 						--神兵
        res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = attr_cfg and attr_cfg.resouce or item_cfg.param2

        -- self:PlayWeaponTween()
		if res_id then
			self:ShowWeapon(res_id)
		end
		self.node_list["is_role_model"]:SetActive(true)
		return
	elseif display_type == DisplayItemTip.Display_type.FABAO then 						--法宝
		res_id, image_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		path = ResPath.GetFaBaoModel
		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.JIANZHEN then 					--剑阵
		res_id, image_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		path = ResPath.GetJianZhenModel
		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.BABY then 					--宝宝
		res_id, attr_cfg = MarryWGData.Instance:GetBabyResByItemId(item_cfg.id)
		if attr_cfg then
			path = ResPath.GetHaiZiModel
			animation_type = "soul"
		end
		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.CHENGHAO then 				--称号
		attr_cfg = TitleWGData.Instance:GetConfig(item_cfg.param1)
		local title_id = item_cfg.param1
		local asset, bundle = ResPath.GetTitleModel(title_id)
		local effect_asset, effect_bundle
		if attr_cfg and attr_cfg.is_show_effid == 1 then
			effect_asset, effect_bundle = ResPath.GetEffectUi(attr_cfg.is_zhengui == 0 and Ui_Effect.UI_title_eff_0 or Ui_Effect.UI_title_eff_1)
		end
		self:SetTitleCell(title_id, asset, bundle, effect_asset, effect_bundle)
		self.node_list["is_title_cell_root"]:SetActive(true)
		return
	elseif display_type == DisplayItemTip.Display_type.XIAOGUI then 					--小鬼
		path = ResPath.GetGuardModel
		-- attr_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		if xiaogui_cfg then
			res_id = xiaogui_cfg.appe_image_id
		end
		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.BUBBLE then 					--气泡
		local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(self.cur_is_show_data.item_id)
		if not fashion_cfg or IsEmptyTable(fashion_cfg) then return end
		local bubble_id = fashion_cfg.resouce or 0

		local asset, bundle = ResPath.ChatBigBubbleBig(bubble_id)
		self:SetBubbleCell(asset, bundle)
		self.node_list["is_bubble_cell_root"]:SetActive(true)
		return
	elseif display_type == DisplayItemTip.Display_type.PHOTOFRAME then 				--相框
		local photoframe_id = item_cfg.param2
		local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
		if not IsEmptyTable(compose_cfg) then
			local item_cfg2 = ItemWGData.Instance:GetItemConfig(compose_cfg.product_id)
			photoframe_id = item_cfg2.param2
		end
		local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(SHIZHUANG_TYPE.PHOTOFRAME, photoframe_id)
		photoframe_id = cfg and cfg.resouce or photoframe_id
		local data = {fashion_photoframe = photoframe_id}
		local head_cell = self:GetHeadCell()
		head_cell:SetImgBg(true)
		head_cell:SetData(data)
		head_cell:SetBgActive(false)
		self.node_list["is_head_cell_root"]:SetActive(true)
		return
	elseif display_type == DisplayItemTip.Display_type.TIANSHEN then 				--天神
		local magic_image = TianShenWGData.Instance:GetAppeImage(item_cfg.id)
		if not magic_image then
			return
		end
		-- if magic_image.tianshen_location then
		-- 	self.base_tips:SetDingWeiInfo(magic_image.tianshen_location)
		-- end
		self.is_model_display:SetTianShenModel(magic_image.appe_image_id, magic_image.index, false, nil)

		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.TIANSHEN_SHENSHIWAIGUAN then 				--天神神饰外观
		local shenqi_waiguan_cfg = TianShenWGData.Instance:GetWaiGuanCfgByStuff(item_cfg.id)
		if not shenqi_waiguan_cfg then
			return
		end
		
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(shenqi_waiguan_cfg.index)
		if not tianshen_cfg then
			return
		end

		local b, a = ResPath.GetBianShenModel(tianshen_cfg.appe_image_id)
		self.is_model_display:SetMainAsset(b, a)

		bundle, asset = TianShenWGData.Instance:GetTianShenWeapon(tianshen_cfg.index, shenqi_waiguan_cfg.waiguan_id)
		self.is_model_display:SetWeaponModel(bundle, asset)
		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.SHENQI then --天神神器

		local shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(item_cfg.id)
		if not shenqi_cfg then
			local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
			if compose_cfg then
				shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(compose_cfg.product_id)
			end
			if not shenqi_cfg then
				return
			end
		end

		local b, a = ResPath.GetTianShenShenQiPath(shenqi_cfg.ts_res_idui)
		self.is_model_display:SetMainAsset(b, a)
		-- self:PlayWeaponTween()
		self.node_list["is_role_model"]:SetActive(true)
	elseif display_type == DisplayItemTip.Display_type.FIGHTSOUL then -- 战魂（四象）
		self.is_model_display:SetCameraOffsetType(MODEL_OFFSET_TYPE.NORMALIZE)
		local fs_cfg = FightSoulWGData.Instance:GetFightSoulItemCfg(self.cur_is_show_data.item_id)
		if fs_cfg then
			res_id = fs_cfg.sixiang_type
			path = ResPath.GetFightSoulModel
			animation_type = "soul_no_do_ideal"
		end
		self.node_list["is_role_model"]:SetActive(true)
	end

	self:FlushISModelAction(path, res_id, animation_type)
	self.node_list["is_role_model"]:SetActive(true)
end

function OperationActivityView:FlushISModelAction(path_fun, res_id, animation_type, other_asset)
	if path_fun == nil or res_id == nil then
		return
	end
	if self.is_model_display then
		local bundle, asset = path_fun(res_id)
		self.is_model_display:SetMainAsset(bundle, asset)
	end
	if animation_type and animation_type ~= "" then
		if animation_type == "mount" then 			--坐骑
			self.is_model_display:PlayMountAction()
		elseif animation_type == "wing" then 		--羽翼
			self.is_model_display:PlayWingAction()
		elseif animation_type == "soul" then 		--灵童
			self.is_model_display:PlaySoulAction()
		elseif animation_type == "soul_no_do_idle" then --灵童, 无需再播待机动作
			self.is_model_display:PlaySoulAction()
		end
	end
end


function OperationActivityView:CancelWeaponTween()
	if self.is_tween_weapon then
        self.is_tween_weapon:Kill()
        self.is_tween_weapon = nil
        local tween_root = self.node_list["is_role_model"].rect
        if tween_root then
            tween_root.anchoredPosition = Vector2(0, 0)
        end
	end
end

-- function OperationActivityView:ClearFootEff()
-- 	if self.footprint_eff_t ~= nil then
-- 		for k,v in pairs(self.footprint_eff_t) do
-- 			if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
-- 				-- ResPoolMgr:Release(v.obj)
-- 				v.role_model:OnRemoveGameObject(v.obj)
-- 				v.obj:SetActive(false)
-- 			end
-- 		end
-- 	end

-- 	self.footprint_eff_t = {}
-- 	self.is_foot_view = false
-- end

function OperationActivityView:GetFashionModlePathFun(res_id, part_type, item_cfg)
	if part_type == nil then
		print_error("DisplayItemTip:GetFashionModlePathFun() can not found the type!! id:::", item_cfg and item_cfg.id)
		return nil
	end

	local path = nil
	local animation_type = nil

	if part_type == SHIZHUANG_TYPE.BODY then							--时装
		path = ResPath.GetRoleModel
		local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(item_cfg.id)
		if fashion_cfg then
			local prof = GameVoManager.Instance:GetMainRoleVo().prof
			res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
		end
	elseif part_type == SHIZHUANG_TYPE.FOOT then							--足迹
		-- self.is_foot_view = true
		path = nil
		-- self.foot_effect_id = res_id
		-- if not self.use_update then
		-- 	Runner.Instance:AddRunObj(self, 8)
		-- 	self.use_update = true
		-- end
		self:ShowRoleModel()
	elseif part_type == SHIZHUANG_TYPE.HALO then						--光环
		path = ResPath.GetHaloModel
	elseif part_type == SHIZHUANG_TYPE.LINGGONG then						--灵弓
		path = ResPath.GetSoulBoyWeaponModel2
	elseif part_type == SHIZHUANG_TYPE.MASK then 							--脸饰
		self:ShowRoleModel()
		self.is_model_display:SetMaskResid(res_id)
	elseif part_type == SHIZHUANG_TYPE.BELT then 							--腰饰
		self:ShowRoleModel()
		self.is_model_display:SetWaistResid(res_id)
	elseif part_type == SHIZHUANG_TYPE.WEIBA then 							--尾巴
		self:ShowRoleModel()
		self.is_model_display:SetTailResid(res_id)
		self.is_model_display:SetRotation(MODEL_ROTATION_TYPE.WEIBA)
	elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then 						--手环
		self:ShowRoleModel()
		self.is_model_display:SetShouHuanResid(res_id)
	else

	end

	return path, res_id, animation_type
end

-- function OperationActivityView:Update(now_time, elapse_time)
-- 	if not self.is_foot_view then
-- 		return
-- 	end
-- 	if self.next_create_footprint_time == 0 then
--         self:CreateFootPrint()
--         self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
--     end
--     if self.next_create_footprint_time == nil then --初生时也是位置改变，不播
--         self.next_create_footprint_time = 0
--     end
--     if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
--         self.next_create_footprint_time = 0
--     end
--     self:UpdateFootprintPos()
-- end

-- function OperationActivityView:CreateFootPrint()
-- 	if nil == self.foot_effect_id then
-- 		return
-- 	end
-- 	if nil == self.footprint_eff_t then
-- 		self.footprint_eff_t = {}
-- 	end
-- 	if not self.is_model_display then
-- 		return
-- 	end
--     local pos = self.is_model_display.draw_obj:GetRoot().transform
--     local bundle, asset = ResPath.GetUIFootEffect(self.foot_effect_id)
-- 	EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.position.x , pos.position.y, pos.position.z), nil, pos, nil, function(obj)
-- 		if obj then
-- 			if nil ~= obj then
-- 				if self.is_model_display then
-- 					obj.transform.localPosition = Vector3.zero
-- 					obj:SetActive(false)
-- 					obj:SetActive(true)
-- 					table.insert(self.footprint_eff_t, {obj = obj, role_model = self.is_model_display})
-- 					self.is_model_display:OnAddGameobject(obj)
-- 				else
-- 					ResPoolMgr:Release(obj)
-- 				end
-- 			end
-- 		end
--    	end)
-- 	if #self.footprint_eff_t > 2 then
-- 		local obj = table.remove(self.footprint_eff_t, 1)
-- 		obj.role_model:OnRemoveGameObject(obj.obj)
-- 		if not IsNil(obj.obj) then
-- 			obj.obj:SetActive(false)
-- 		end
-- 	end
-- end

-- function OperationActivityView:UpdateFootprintPos()
-- 	if nil == self.footprint_eff_t then
-- 		return
-- 	end
-- 	for k,v in pairs(self.footprint_eff_t) do
-- 		if not IsNil(v.obj) then
-- 			local pos = v.obj.transform.localPosition
-- 			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
-- 		end
-- 	end
-- end


function OperationActivityView:PlayWeaponTween()
	if not self.is_tween_weapon then
		local tween_root = self.node_list["is_role_model"].rect
		self.is_tween_weapon = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 50, 1)
		self.is_tween_weapon:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	else
		self.is_tween_weapon:Restart()
	end
end

function OperationActivityView:ShowWeapon(weapon_res_index)
    local weapon_res_id, weapon_res_id_2 = AppearanceWGData.GetFashionWeaponIdByResViewId(weapon_res_index)
    local bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)
    self.is_model_display:SetMainAsset(bundle, asset)
end



--显示当前玩家模型
function OperationActivityView:ShowRoleModel(role_res_id)
	role_res_id = role_res_id or AppearanceWGData.Instance:GetRoleResId()
	self.is_model_display:SetRoleResid(role_res_id)
end

function OperationActivityView:SetTitleCell(title_id, asset, bundle, effect_asset, effect_bundle)
	--if self.is_load then
		self.node_list["is_title_display"]:ChangeAsset(asset, bundle, false, function(obj)
			if IsNil(obj) then
				return
			end
		
			local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
			if not diy_cfg then
				return
			end
		
			local text_obj = obj.gameObject.transform:Find("Text")
			local title_cfg = TitleWGData.Instance:GetConfig(title_id)
			if text_obj == nil or not title_cfg then
				return
			end
			
			local title_text = text_obj.gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
			title_text.text = title_cfg.name
		end)

		if effect_asset and effect_bundle then
			self.node_list["is_title_effect"]:ChangeAsset(effect_asset, effect_bundle)
			self.node_list["is_title_effect"]:SetActive(true)
		else
			self.node_list["is_title_effect"]:SetActive(false)
		end

		-- self:FlushLeftPanel(key_str.title)
		self:PlayYoyoTween()
	--end
end

function OperationActivityView:SetBubbleCell(asset, bundle)
	if not self.bubble_loader then
		local bubble_loader = AllocAsyncLoader(self, "base_tip_bubble_cell")
		bubble_loader:SetIsUseObjPool(true)
		bubble_loader:SetParent(self.node_list["is_bubble_cell_root"].transform)
		self.bubble_loader = bubble_loader
	end

	self.bubble_loader:Load(asset, bundle)
	-- self:FlushLeftPanel(key_str.bubble)
	self:PlayYoyoTween()
end

function OperationActivityView:GetHeadCell()
	if not self.image_show_head_cell then
		self.image_show_head_cell = BaseHeadCell.New(self.node_list["is_head_cell_root"])
	end

	-- self:FlushLeftPanel(key_str.head)
	self:PlayYoyoTween()
	return self.image_show_head_cell
end

function OperationActivityView:PlayYoyoTween()
	if not self.is_tween_yoyo then
		local tween_root = self.node_list["is_tween_root"].rect
		self.is_tween_yoyo = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 40, 1.5)
		self.is_tween_yoyo:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	else
		self.is_tween_yoyo:Restart()
	end
end

ImageShowCell = ImageShowCell or BaseClass(BaseRender)

function ImageShowCell:__init()
	self.item_cell = ItemCell.New(self.node_list["is_cell_item_cell"])
end

function ImageShowCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ImageShowCell:LoadCallBack()
	local face_cfg = ImageShowWGData.Instance:GetCellImageShowFaceCfg()

	self.node_list.is_cell_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(face_cfg.list_item_bg))
	--self.node_list.is_cell_bg.image:SetNativeSize()

	self.node_list.is_cell_name_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(face_cfg.is_cell_name_bg))
	--self.node_list.is_cell_name_bg.image:SetNativeSize()
	self.node_list["is_cell_hl"].image:LoadSprite(ResPath.GetOperationActivityImagePath(face_cfg.is_cell_hl))
end

function ImageShowCell:OnFlush()
	self:FlushISCellHL()
	if self.data then
		local cell_data = {}
		cell_data.item_id = self.data.item_id
		self.item_cell:SetData(cell_data)
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		self.node_list["is_cell_have_get_falg"]:SetActive(false)
		--显示当前模型激活状态
		if item_cfg == nil then
			return
		end

		if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG then
			return
		end

		local check_item_id = self.data.item_id

		--判断是否为碎片类型
		if item_cfg.is_chip == 1 then
			local comp_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
			if not IsEmptyTable(comp_cfg) and comp_cfg.product_id then
				check_item_id = comp_cfg.product_id
			end
		end

		local part_type = NewAppearanceWGData.Instance:GetFashionTypeByItemId(check_item_id)	
		local fashion_level = NewAppearanceWGData.Instance:GetFashionLevelByItemId(check_item_id)

		local desc = false
		if part_type and part_type >= 0 then
			if fashion_level > 0 then
				local max_level = NewAppearanceWGData.Instance:GetFashionMaxLevelById(check_item_id) or 0
				local is_not_max_level = fashion_level < max_level
   	        	if is_not_max_level then
   	            	if fashion_level - 1 == 0 then
   	                	desc = true
   	             	else
   	                	desc = true
   	            	end
				--elseif IS_IMAGE[item_cfg.is_display_role] then
				--	desc = true
				else
					desc = true
				end
			else
				desc = false
			end
		else
			local is_active = false
			local title_cfg = TitleWGData.Instance:GetTitleConfigByItemId(check_item_id) -- 是否为称号
			local tianshen_cfg = TianShenWGData.Instance:GetTianShenActItemCfgByActId(check_item_id) --是否为天神
			local shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(check_item_id)
        	if part_type == -1 then
        	    if MarryWGData.Instance:IsBabyActiveCard(check_item_id) then
        	        is_active = MarryWGData.Instance:GetBabyActiveByItemId(check_item_id)
        	   	elseif title_cfg and not IsEmptyTable(title_cfg) then -- 判断称号是否激活
        	   		is_active = TitleWGData.Instance:IsThisTitleActive(title_cfg.title_id)
        	   	elseif tianshen_cfg and not IsEmptyTable(tianshen_cfg) then -- 判断天神是否激活
        	   		is_active = TianShenWGData.Instance:IsActivation(tianshen_cfg.index)
        	   	elseif shenqi_cfg and not IsEmptyTable(shenqi_cfg) then
        	   		is_active = TianShenWGData.Instance:IsShenQiActivation(shenqi_cfg.index)
        	    else
        	        is_active = NewAppearanceWGData.Instance:GetQiChongIsActByItemId(check_item_id)
        	    end
	  		else
	  			is_active = NewAppearanceWGData.Instance:GetFashionIsActByItemId(check_item_id)
	  		end
	
			if is_active then
				desc = true
			else
				desc = false
			end
		end
		self.node_list["is_cell_have_get_falg"]:SetActive(desc)
		self.node_list["is_cell_name_text"].text.text = item_cfg.name
	end
end

function ImageShowCell:FlushISCellHL()
	self.node_list["is_cell_hl"]:SetActive(false)
	if self.data and self.data.item_id then
		local show_id = ImageShowWGData.Instance:GetCurShowItemID()
		self.node_list["is_cell_hl"]:SetActive(show_id == self.data.item_id)
	end
end