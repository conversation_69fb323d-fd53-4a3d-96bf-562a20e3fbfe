--招财猫
FortuneCatView = FortuneCatView or BaseClass(SafeBaseView)

local stay_interval = 0.2  --停顿时间
local reset_time = 0.3  --归位动画时间
local rotate_time = 5   --旋转动画时间
local total_num = 8     --总的类别数量

local zcjb_start_pos = {
    [1] = -38,
    [2] = -38,
    [3] = -98,
    [4] = -158,
}
local zcjb_end_pos = {
    [1] = -38,
    [2] = -98,
    [3] = -158,
    [4] = -218,
}

local EFF_MAX_COUNT = 30

function FortuneCatView:__init()
	self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true
	self.default_index = 10

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, "uis/view/fortunecat_ui_prefab", "layout_fortune_cat")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function FortuneCatView:ReleaseCallBack()
    if self.cell_item then
		self.cell_item:DeleteMe()
		self.cell_item = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.tween_2 then
		self.tween_2:Kill()
		self.tween_2 = nil
    end

    if CountDownManager.Instance:HasCountDown("fortune_cat_time") then
        CountDownManager.Instance:RemoveCountDown("fortune_cat_time")
    end
    self.is_do_tween = false
end

function FortuneCatView:OpenCallBack()
    FortuneCatWGCtrl.Instance:SendOperaReq(FortuneCatWGData.OA_FORTUNECAT_OPER.OA_FORTUNECAT_TYPE_SEND_PERSON_INFO)
end

function FortuneCatView:ShowIndexCallBack()
    UITween.CleanAllTween(GuideModuleName.FortuneCatView)
    self.do_cell_tween = true
    local tween_info = UITween_CONSTS.FortuneCatSys
    -- 图标和数字动画
    UITween.FakeHideShow(self.node_list.num_bg)
    for k = 1, 8 do
        UITween.FakeToShow(self.node_list.num_bg)
        local move_delay_time = tween_info.NumNextDoDelay * k
        if self.node_list["icon_"..k] then
            UITween.FakeHideShow(self.node_list["icon_"..k])
            ReDelayCall(self, function()
                if self.node_list and self.node_list["icon_"..k] then
                    UITween.AlphaShow(GuideModuleName.FortuneCatView,self.node_list["icon_"..k], 0, 1, tween_info.TimeInterval, DG.Tweening.Ease.Linear)
                end
            end, move_delay_time, "fortunecat_num_" .. k)
        end
    end

	local item_id = FortuneCatWGData.Instance:GeOtherCfgByKey("show_item")
    self.cell_item:SetShowCualityBg(false)
    self.cell_item:SetCellBgEnabled(false)
	self.cell_item:SetData({item_id = item_id})

    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.FortuneCatView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT)
end

function FortuneCatView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FortuneCat.TitleName
	local bundle, asset = ResPath.GetRawImagesPNG("a3_fkmtl_bg")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self:InitMoneyBar()
	self:InitTabbar()
    self:InitCountDown()

    local cfg = FortuneCatWGData.Instance:GetRewardCfg()
    for k,v in pairs(cfg) do
        if self.node_list["beishu_" .. k] then
            self.node_list["beishu_" .. k].text.text = string.format(Language.FortuneCat.BeiShuDes, tonumber(v.multiple_value)/100)
            local icon_bundle, icon_asset = ResPath.GetFortuneCatImg("a3_fkmtl_dj"..k)
            self.node_list["icon_box" .. k].image:LoadSpriteAsync(icon_bundle, icon_asset)
        end
    end

    self.cell_item = ItemCell.New(self.node_list.cell_node)

    local is_skip = FortuneCatWGData.Instance:GetSkipAniFlag()
    self.node_list["toggle_mark"]:SetActive(is_skip)

    for k = 1, total_num do
        self.node_list["high_light"..k]:SetActive(false)
    end

	self.node_list["btn_arrow"].button:AddClickListener(BindTool.Bind(self.OnClickArrow, self))
    self.node_list["toggle_jump"].button:AddClickListener(BindTool.Bind(self.OnClickJumpAni, self))
    self.node_list["btn_chongzhi"].button:AddClickListener(BindTool.Bind(self.OnClickChongzhi, self))
    self.node_list["btn_getscore"].button:AddClickListener(BindTool.Bind(self.OnClickGetScore, self))
    self.node_list["record_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBtnRecord, self))
    self.node_list["btn_target"].button:AddClickListener(BindTool.Bind(self.OnClickBtnTarget, self))
end

function FortuneCatView:InitTabbar()
	local toggle_name_list = { Language.FortuneCat.TitleName }
	local bundle_name = "uis/view/open_server_activity_ui_prefab"
	local common_path = "uis/view/common_panel_prefab"
	self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
	self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	self.tabbar:Init(toggle_name_list, nil, common_path, bundle_name, {})
end

function FortuneCatView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_cangjin_score = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function FortuneCatView:SetDelayFlushRecord(is_delay)
    self.is_delay_flush = is_delay
end

function FortuneCatView:GetIsDotween()
    return self.is_do_tween
end

function FortuneCatView:OnFlush()
    self:FlushDetails()
    self:FlushBtnStates()
end

function FortuneCatView:FlushDetails()
    if not self.node_list or not self.node_list.cur_times_txt then return end
    local person_remain = FortuneCatWGData.Instance:GetCurRemainTimes()
    self.node_list.cur_times_txt.text.text = person_remain --当前剩余次数

    local cur_times = FortuneCatWGData.Instance:GetCurTimes() --抽了多少次奖
    local value1 = FortuneCatWGData.Instance:GetCurConsumeValue(cur_times)
    self.node_list.mid_num_txt.text.text = value1 --中间抽奖消耗

    local value2 = FortuneCatWGData.Instance:GetFortuneCatNextRechargeNum()
    self.node_list.cost_num_txt.text.text = value2 --.. " " --左边充值消耗
    local remain_times = FortuneCatWGData.Instance:GetWorldRemainTimes()

    XUI.SetButtonEnabled(self.node_list["btn_chongzhi"], remain_times > 0)
end

function FortuneCatView:FlushBtnStates()
    local cur_times = FortuneCatWGData.Instance:GetCurTimes() --抽了多少次奖
    local value1, type_value = FortuneCatWGData.Instance:GetCurConsumeValue(cur_times)
    local role_info = RoleWGData.Instance:GetRoleInfo()         -- 人物数据

    local icon_id
	local count
    if type_value == FORTUNECAT_HUOBI_TYPE.CANGJING_SCORE then
        icon_id = COMMON_CONSTS.VIRTUAL_ITEM_CANG_JIN_SCORE
        count = YanYuGeWGData.Instance:GetCurScore()
    else
        icon_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
        count = role_info.gold
    end

    self.is_not_enough = count < value1
    local bundle, asset = ResPath.GetItem(icon_id)
    self.node_list.cost_icon.image:LoadSpriteAsync(bundle, asset)
end

function FortuneCatView:ReceiveProtocol()
    local index = FortuneCatWGData.Instance:GetFortuneCatCurIndex()
    local is_skip = FortuneCatWGData.Instance:GetSkipAniFlag()
	for k = 1, total_num do
		self.node_list["high_light"..k]:SetActive(k == index)
	end
    if is_skip then
        --外圈旋转.
        self.node_list.item_group.transform.localRotation = Quaternion.Euler(0, 0, 360 / total_num * (index))
        --修正item旋转.
        for i = 1, total_num do
            self.node_list["icon_" .. i].transform.localRotation = Quaternion.Euler(0, 0, -360 / total_num * (index))
        end
    else
        --外圈旋转.
        self.tween_2 = DG.Tweening.DOTween.Sequence()
        local tween_rotate_2 = self.node_list.item_group.transform:DORotate(
            Vector3(0, 0, -360 * rotate_time + 360 / total_num * (index)),
            rotate_time, DG.Tweening.RotateMode.FastBeyond360)
		tween_rotate_2:SetEase(DG.Tweening.Ease.OutCubic)
        self.tween_2:Append(tween_rotate_2)
        self.tween_2:OnUpdate(function()
            local rotate_z = self.node_list.item_group.transform.localEulerAngles.z
            for i = 1, total_num do
                self.node_list["icon_" .. i].transform.localRotation = Quaternion.Euler(0, 0, -rotate_z)
            end
        end)

        self.tween_2:OnComplete(function()
            self.tween_2:Kill()
            self.tween_2 = nil
            local rotate_z = self.node_list.item_group.transform.localEulerAngles.z
            for i = 1, total_num do
                self.node_list["icon_" .. i].transform.localRotation = Quaternion.Euler(0, 0, -rotate_z)
            end
        end)
    end
end


function FortuneCatView:InitCountDown()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT)
	if CountDownManager.Instance:HasCountDown("fortune_cat_time") then
		CountDownManager.Instance:RemoveCountDown("fortune_cat_time")
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown("fortune_cat_time",
			BindTool.Bind(self.UpdateCountDownTime, self),
			BindTool.Bind(self.CompleteCountDownTime, self),
			nil, time, 1)
	else
		self:CompleteCountDownTime()
	end
end

-- 倒计时每次循环执行的函数
function FortuneCatView:UpdateCountDownTime(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list["count_down_txt"].text.text = string.format(Language.FortuneCat.CountDown, time_str)
end

function FortuneCatView:CompleteCountDownTime()
	self.node_list.count_down_txt.text.text = ""
	self:Close()
end

function FortuneCatView:OnClickJumpAni()
    FortuneCatWGData.Instance:SetSkipAniFlag()
    local is_skip = FortuneCatWGData.Instance:GetSkipAniFlag()
    self.node_list["toggle_mark"]:SetActive(is_skip)
end

function FortuneCatView:OnClickBtnRecord()
    FortuneCatWGCtrl.Instance:SendOperaReq(FortuneCatWGData.OA_FORTUNECAT_OPER.OA_FORTUNECAT_TYPE_SEND_SERVER_INFO) --请求信息
    local world_record_list = FortuneCatWGData.Instance:GetRecordListByType(2)
    local person_record_list = FortuneCatWGData.Instance:GetRecordListByType(1)
    FortuneCatWGCtrl.Instance:OpenTreasureRecordView(world_record_list, person_record_list)
end

function FortuneCatView:OnClickBtnTarget()
    FortuneCatWGCtrl.Instance:OpenTargetView()
end

function FortuneCatView:OnClickArrow()
    local remain_times = FortuneCatWGData.Instance:GetWorldRemainTimes()
    if remain_times <= 0 then
        return
    end

    if self.is_not_enough then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CangJingScoreNoEnough)
	    ViewManager.Instance:Open(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_yytq)
        return
    end

    if self.tween_2 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FortuneCat.IsInDrawing)
        return
    end

    for k = 1, total_num do
        self.node_list["high_light"..k]:SetActive(false)
    end
    FortuneCatWGCtrl.Instance:SendOperaReq(FortuneCatWGData.OA_FORTUNECAT_OPER.OA_FORTUNECAT_TYPE_DRAW) --抽奖
    local is_skip = FortuneCatWGData.Instance:GetSkipAniFlag()
    if not is_skip then
        self.is_do_tween = true
    end
end

function FortuneCatView:OnClickChongzhi()
    ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
end

function FortuneCatView:OnClickGetScore()
    ViewManager.Instance:Open(GuideModuleName.YanYuGeEntranceView)
end