ActivityDragonSecretWGData = ActivityDragonSecretWGData or BaseClass()

LOTTER_NUM_TYPE ={
    SINGLE_DRAW = 1,
    REPEATEDLY_DRAW = 2,

    DATA_NUM = 5,
    DEFAULT_NUMBER = 50,  -- 抽奖次数默认50次
    TEN_NUMBER_DRAWS = 10, --抽奖次数默认10次
}

function ActivityDragonSecretWGData:__init()
    if ActivityDragonSecretWGData.Instance then
        error("[ActivityPrivilegeBuyWGData] Attempt to create singleton twice!")
    end

    ActivityDragonSecretWGData.Instance = self

    self.grade = 0
    self.round  = 0
    self.refresh_round_times = 0
    self.refresh_round_reward_flag = {}
    self.reward_pool_times_list = {}
    
    self.act_day_open = 0

    --抽奖结果
    self.dragon_draw_result_info = {}

    --初始化配置信息
    self:InitConfig()

    --红点注册
    RemindManager.Instance:Register(RemindName.RemindDragonSecret, BindTool.Bind(self.GetRemind, self))
end

function ActivityDragonSecretWGData:__delete()
    --红点注销
    RemindManager.Instance:UnRegister(RemindName.RemindDragonSecret)

    ActivityDragonSecretWGData.Instance = nil
end

function ActivityDragonSecretWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_dragon_store_auto")

    --通过档次拿到重置奖励配置表
    self.reset_reward_cfg = ListToMapList(cfg.reset_reward, "grade")

    --self.mode_cfg = ListToMap(cfg.mode, "mode")
    --通过档次和轮次拿到奖池的配置信息
    self.reward_pool_cfg = ListToMapList(cfg.reward_pool, "grade", "round")
end

function ActivityDragonSecretWGData:SetInfo(protocol)
    local old_list = self.reward_pool_times_list
    self.grade = protocol.grade   -- 用于判断当前档次
    self.round = protocol.round   -- 用于判断当前轮次
    self.refresh_round_times = protocol.refresh_round_times -- 用于判断奖池刷新次数
    self.refresh_round_reward_flag = protocol.refresh_round_reward_flag --用于奖池刷新的状态
    self.reward_pool_times_list = protocol.reward_pool_times_list       --用于奖池索引状态
end

--抽奖结果
function ActivityDragonSecretWGData:SetDragonDrawRewardInfo(protocol)
    self.dragon_draw_result_info.times = protocol.times
    self.dragon_draw_result_info.count = protocol.count
    self.dragon_draw_result_info.result_item_list = protocol.result_item_list
end

function ActivityDragonSecretWGData:GetDragonDrawRewardInfo()
    return self.dragon_draw_result_info
end

--保存抽奖的选项
function ActivityDragonSecretWGData:SetOrGetDragonDrawIndex(btn_index)
    if btn_index then
      --  print_error("btn_index", btn_index)
        self.cache_dragon_btn_index = btn_index
    end
   -- print_error("self.cache_dragon_btn_index", self.cache_dragon_btn_index)
    return self.cache_dragon_btn_index
end

--返回活动天数
function ActivityDragonSecretWGData:GetActDay()
    return self.act_day_open
end

--返回当前档次
function ActivityDragonSecretWGData:GetActDraGrade()
    return self.grade
end

--返回当前轮次
function ActivityDragonSecretWGData:GetActDraRound()
    return self.round
end

--返回当前奖池的刷新次数
function ActivityDragonSecretWGData:GetActDraRefresh()
    return self.refresh_round_times
end

--根据索引判断大奖的重置次数领取状态 1已领取  0未领取
function ActivityDragonSecretWGData:GetRoundReward(seq)
    return self.refresh_round_reward_flag[seq] == 1
end

--通过当前档次和轮次拿到当前奖池剩余物品的抽到多少次(被抽次数)
function ActivityDragonSecretWGData:GetRewardPool(seq)
    return self.reward_pool_times_list[seq] or 0
end

--其他配置
function ActivityDragonSecretWGData:GetQiTaCfg()
    local other_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_dragon_store_auto").other[1]
    return other_cfg
end

--返回标签数据
function ActivityDragonSecretWGData:GetRoundTagsList()
    local data_list = {}
    for i = 0, LOTTER_NUM_TYPE.DATA_NUM do
        local data = {
            seq = i
        }
        table.insert(data_list, data)
    end
    --print_error(data_list)

    return data_list
end

--拿到奖品的总数量
function ActivityDragonSecretWGData:GetPrizeSum()
    local prize_sum = 0
    local prize_list = self:GetCurrentList(self.grade, self.round)
    for k, v in ipairs(prize_list) do
        prize_sum = prize_sum +  v.max_times - self:GetRewardPool(v.seq)
    end

    -- print_error("prize_sum", prize_sum)
    return prize_sum
end

--拿到重置奖励表
function ActivityDragonSecretWGData:GetResetTashList()
    local show_list = {}
    local cfg_list = self.reset_reward_cfg[self.grade] or {}
    for k, v in ipairs(cfg_list) do
        local data = __TableCopy(v)
        data.is_remind = self:GetIsCanReceive(v.times, v.seq)
        table.insert(show_list, data)
    end

   -- print_error("show_list",#show_list,show_list[1])
    --进行排序
    --拿到是否领取的标记
    local data_list = {}
    for i = 1, #show_list do
        if self:GetRoundReward(show_list[i].seq) then
            data_list[100 + i] = show_list[i]
        else
            data_list[i] = show_list[i]
        end
    end
    data_list = SortTableKey(data_list)

    return data_list
end

--判断是否是可以领取状态
function ActivityDragonSecretWGData:GetIsCanReceive(times, seq)
  --  print_error(self.refresh_round_times, times, not self:GetRoundReward(seq))
    return self.refresh_round_times >= times and (not self:GetRoundReward(seq)) 
end

--通过当前档次和轮次拿到当前列表的数据
function ActivityDragonSecretWGData:GetCurrentList(grade, round) --从1开始
    return (self.reward_pool_cfg[grade] or {})[round] or {}
end

--拿到小奖中的奖励信息
function ActivityDragonSecretWGData:GetSmallPrizeList()
    local small_cfg_list = {}
    local small_prize_list = self:GetCurrentList(self.grade, self.round)
    for i = 1, #small_prize_list do
        if small_prize_list[i].seq ~= 0 then
            table.insert(small_cfg_list, small_prize_list[i])
        end
    end
    return small_cfg_list
end

--判断当前轮大奖是否抽完,根据次数
function ActivityDragonSecretWGData:GetAwardsIsCanFinish()
     local num =  self:GetRewardPool(0)  --当前轮次的第0个索引
     local num1 = self:GetCurrentList(self.grade, self.round)
     if not num1[1] then
        return false
     end
     if num >= num1[1].max_times then
         return true
     end

     return false
end

--红点
function ActivityDragonSecretWGData:GetRemind()
    local cfg_list = self.reset_reward_cfg[self.grade] or {}
    for k, v in ipairs(cfg_list) do
        if self:GetIsCanReceive(v.times, v.seq) then
            return 1
        end
    end

    return 0
end



