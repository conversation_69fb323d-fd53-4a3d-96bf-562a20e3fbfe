SiXiangCallRecordView = SiXiangCallRecordView or BaseClass(SafeBaseView)

function SiXiangCallRecordView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(866, 566)})
    self:AddViewResource(0, "uis/view/sixiang_call_prefab", "layout_sixiang_call_record")
end

function SiXiangCallRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function SiXiangCallRecordView:LoadCallBack()
    self.toggle_index = 1

    self.node_list.txt_tip2.text.text = Language.SiXiangCall.RecordTip

    self.node_list.btn_all.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, SiXiangRecordType.ALL))
    self.node_list.btn_self.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, SiXiangRecordType.SELF))

    self.record_list = AsyncListView.New(SiXiangCallRecordRender, self.node_list["role_list"])
end

function SiXiangCallRecordView:ShowIndexCallBack()
    self.node_list["btn_all"].toggle.isOn = true
end

function SiXiangCallRecordView:OnClickSwitch(state)
    self.toggle_index = state
    if state == SiXiangRecordType.SELF then
        SiXiangCallWGCtrl.Instance:SendSiXiangRequest(OP_YUAN_SHEN_ZHAO_HUAN_TYPE.INFO)
    end
    self:FlushRecordList()
end

function SiXiangCallRecordView:OnFlush(param_t)
    self:FlushRecordList()
    self:FlushTitle()
end

function SiXiangCallRecordView:FlushRecordList()
    local data_list = SiXiangCallWGData.Instance:GetSiXiangCallRecordData(self.toggle_index)
    local is_show_list = not IsEmptyTable(data_list)
    if is_show_list then
        self.record_list:SetDataList(data_list)
    end
    self.node_list["role_list"]:SetActive(is_show_list)
    self.node_list["no_invite"]:SetActive(not is_show_list)
end

function SiXiangCallRecordView:FlushTitle()
    self.node_list.title_view_name.text.text = Language.SiXiangCall.RecordTitle
end

-------------------------------------------------------------------------------------------------------

SiXiangCallRecordRender = SiXiangCallRecordRender or BaseClass(BaseRender)

function SiXiangCallRecordRender:LoadCallBack()
    self.node_list["txt_btn"].button:AddClickListener(BindTool.Bind(self.OnclickItem, self))
end

function SiXiangCallRecordRender:OnFlush()
    local data = self:GetData()
    local index = self:GetIndex()
    local mark = (index % 2) == 1
    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not item_cfg then
        return
    end

    -- local color = mark and ITEM_COLOR[item_cfg.color] or ITEM_COLOR[item_cfg.color]
    local color = ITEM_COLOR[item_cfg.color]
    local role_name = data.role_name or RoleWGData.Instance:GetAttr("name")
    local str1 = string.format(Language.SiXiangCall.TxtRecord3, role_name)
    local name = string.format(Language.SiXiangCall.TxtRecord1_2, color, item_cfg.name)
    local num = string.format(Language.SiXiangCall.TxtRecord1_3, color, data.num)

    self.node_list["desc"].text.text = str1
    self.node_list["txt_btn"].text.text = name
    self.node_list["num"].text.text = num
    self.node_list["time"].text.text = os.date("%m-%d  %X", data.draw_time)
    self.node_list["root_bg"].image.enabled = mark
end

function SiXiangCallRecordRender:OnclickItem()
    local data = self:GetData()
    if data == nil then
        return
    end

    local item_data = SiXiangCallWGData.Instance:GetSummonItemDataByItemId(data.item_id)
    item_data.star = data.star or item_data.star
    TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_NORMAL, nil)
end