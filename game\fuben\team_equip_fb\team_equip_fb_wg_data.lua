TeamEquipFbWGData = TeamEquipFbWGData or BaseClass()


function TeamEquipFbWGData:__init()
	TeamEquipFbWGData.Instance = self
	self.team_equip_all_data = {}
	self.team_equip_role_data = {}
	--self.hte_info = {}
	self.hte_entered_times = 0
	self.hte_buy_times = 0
	self.team_equip_fb_cfg = ConfigManager.Instance:GetAutoConfig("team_equipfb_cfg_auto")
	local all_cfg = ConfigManager.Instance:GetAutoConfig("yuangu_equipfb_cfg_auto")
	self.team_fb_xiandian_cfg = all_cfg.layer
	self.buy_times_cfg = ListToMap(all_cfg.buy_times, "buy_times")
	self.reward_cfg = all_cfg.reward
	local count = 0
	for i, v in pairs(all_cfg.buy_times) do
		count = count + 1
	end
	self.max_buy_times = count
	self:InitEquipFbConfig()
end

function TeamEquipFbWGData:__delete()
	self.has_get_item_list = {}
	self.three_drop_list = {}
end

function TeamEquipFbWGData:GetTeamEquipFbCfg()
	return self.team_equip_fb_cfg
end

function TeamEquipFbWGData:SetTeamEquipFBInfo(protocol)
	self.team_equip_all_data.level = protocol.level
	self.team_equip_all_data.is_finish = protocol.is_finish
	self.team_equip_all_data.is_pass = protocol.is_pass
	self.team_equip_all_data.curr_wave_index = protocol.curr_wave_index
	self.team_equip_all_data.max_wave_count = protocol.max_wave_count
	self.team_equip_all_data.cur_star_num = protocol.cur_star_num
	self.team_equip_all_data.prepare_end_timestamp = protocol.prepare_end_timestamp
	self.team_equip_all_data.finish_timestamp = protocol.finish_timestamp
	self.team_equip_all_data.next_star_timestamp = protocol.next_star_timestamp
	self.team_equip_all_data.next_wave_refresh_timestamp = protocol.next_wave_refresh_timestamp
	self.team_equip_all_data.kick_out_timestamp = protocol.kick_out_timestamp
	self.team_equip_all_data.pass_time_s = protocol.pass_time_s
	self.team_equip_all_data.kill_monster_num = protocol.kill_monster_num
	self.team_equip_all_data.cur_wave_monster_num = protocol.cur_wave_monster_num
end

function TeamEquipFbWGData:SetTeamEquipRoleInfo(protocol)
	self.team_equip_role_data.layer = protocol.layer
	self.team_equip_role_data.is_fb_info = protocol.is_fb_info
	self.team_equip_role_data.day_enterfb_times = protocol.day_enterfb_times
	self.team_equip_role_data.help_times = protocol.help_times
	self.team_equip_role_data.is_pass = protocol.is_pass
	self.team_equip_role_data.star_num= protocol.star_num
	self.team_equip_role_data.get_exp= protocol.get_exp
	self.team_equip_role_data.get_coin= protocol.get_coin
	self.team_equip_role_data.reward_item_list = protocol.reward_item_list
end

function TeamEquipFbWGData:GetTeamEquipFBInfo()
	return self.team_equip_all_data
end

function TeamEquipFbWGData:GetTeamEquipRoleInfo()
	return self.team_equip_role_data
end

function TeamEquipFbWGData:GetTeamEquipReward(index)
	return self.team_equip_fb_cfg.layer[index].reward_item
end

function TeamEquipFbWGData:GetTeamEquipProbability()
	local layer = self.team_equip_all_data.level
	local star = self.team_equip_all_data.cur_star_num
	local str = {[0] = "zero", [1] = "one", [2] = "two", [3] = "three", }
	return self.team_equip_fb_cfg.layer[layer + 1][str[star] .. "_star_Prob"]
end

function TeamEquipFbWGData:GetTeamEquipFbName()
	local layer = self:GetTeamEquipFBInfo().level
	return self.team_equip_fb_cfg.layer[layer + 1].name
end
-------------------------------------------------
-- 远古仙殿
-------------------------------------------------
function TeamEquipFbWGData:GetHighTeamEquipFbOther()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("yuangu_equipfb_cfg_auto").other[1]
	return other_cfg
end

function TeamEquipFbWGData:GetHighTeamEquipFbList()
	local list = ConfigManager.Instance:GetAutoConfig("yuangu_equipfb_cfg_auto").layer
	return list
end

function TeamEquipFbWGData:GetHTEVipCfg()
	local vip_level_cfg = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.HIGH_TEAM_EQUIP_BUY_TIMES)
	return vip_level_cfg
end

function TeamEquipFbWGData:GetVipBuyTimes(vip_cfg, vip_level)
	local vip_level_cfg = vip_cfg
	for i = 0, 12 do
		if vip_level_cfg["param_" .. vip_level] < vip_level_cfg["param_" .. i] then
			return i
		elseif vip_level_cfg["param_" .. vip_level] == vip_level_cfg["param_" .. 12] then
			return vip_level
		end
	end
	return -1
end

function TeamEquipFbWGData:SetHTEFbInfo(info)
	self.hte_info = info
end

function TeamEquipFbWGData:GetHTEFbInfo()
	return self.hte_info
end

function TeamEquipFbWGData:GetIsZhuZhan()
	return self.hte_info.is_helper == 1
end

--function TeamEquipFbWGData:GetFubenName()
--	local layer = self.hte_info.level
--	return self.team_fb_xiandian_cfg[layer+1].name
--end
function TeamEquipFbWGData:GetFubenFirstInfo()
	return self.team_fb_xiandian_cfg[1]
end
function TeamEquipFbWGData:GetRobertName()
	local name1,name2 
	local step_list_cfg = ConfigManager.Instance:GetAutoConfig("story_auto").robert_role
	for k,v in pairs(step_list_cfg) do
		if v.id == 1002 then
			name1 = v.name
		end
		if v.id == 1003 then
			name2 = v.name
		end
	end
	return name1,name2 
end
function TeamEquipFbWGData:GetMonsterLevelNum(wave)
	if nil == wave or wave < 0 then return 0 end
	local wave_cfg = ConfigManager.Instance:GetAutoConfig("yuangu_equipfb_cfg_auto").monster
	local all_count = 0
	for k,v in pairs(wave_cfg) do
		if v.layer == 0 and v.wave == wave then
			all_count = all_count + 1
		end
	end
	return all_count
end
--function TeamEquipFbWGData:GetHteFbLevelNum()
--	if next(self.hte_info) == nil then return 0 end
--	return self.hte_info.cur_level_num
--end
--function TeamEquipFbWGData:GetHteFbLevelIcon()
--	local layer = self.hte_info.level
--	return self.team_fb_xiandian_cfg[layer+1].big_icon
--end

function TeamEquipFbWGData:SetHTEFbTimes(entered_times)
	self.hte_entered_times = entered_times
end

function TeamEquipFbWGData:GetHTEFbEnteredTimes()
	return self.hte_entered_times
end

function TeamEquipFbWGData:GetHTEFbTotalTimes()
	return self:GetHighTeamEquipFbOther().everyday_times + self.hte_buy_times
end

-- VIP权限可购买次数
function TeamEquipFbWGData:GetCanBuyTimes()
	return tonumber(VipWGData.Instance:GetVipSpecPermissionsValue(VIP_LEVEL_AUTH_TYPE.HIGH_TEAM_EQUIP_BUY_TIMES))
end

function TeamEquipFbWGData:SetHTEFbVipBuyTimes(buy_time)
	self.hte_buy_times = buy_time
end

function TeamEquipFbWGData:GetHTEFbVipBuyTimes()
	return self.hte_buy_times
end

function TeamEquipFbWGData:SetXieZhuTimes(buy_time)
	self.xiezhu_times = buy_time
end

function TeamEquipFbWGData:GetXieZhuTimes()
	return self.xiezhu_times
end

function TeamEquipFbWGData:GetXieZhuTotalTimes()
	return self:GetHighTeamEquipFbOther().help_times
end

function TeamEquipFbWGData:GetHTEFbTimes()
	local total_times = self:GetHighTeamEquipFbOther().everyday_times + self.hte_buy_times
	return total_times - self.hte_entered_times, total_times
end

function TeamEquipFbWGData:CheckYuanGuXianDianCount()
   local remain_times, total_times = self:GetHTEFbTimes()
   return remain_times > 0
end

function TeamEquipFbWGData:GetBossByLayer(layer)
	local list = self:GetHighTeamEquipFbList()
	for k, v in pairs(list) do
		if v.layer == layer then
			return v
		end
	end
end

function TeamEquipFbWGData:GetMonsterCfg()
    local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list --获取boss模型
    local boss_list = TeamEquipFbWGData.Instance:GetHighTeamEquipFbList()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local list = {}
    local cur_page = 1
    if not boss_list or not next(boss_list) then return list end
    for k,v in pairs(boss_list) do
    	local temp_list = {}
    	temp_list["layer"] = v.layer
    	temp_list["scene_id"] = v.scene_id
    	temp_list["is_active"] = role_level >= v.need_role_level
    	temp_list["reward_list"] = __TableCopy(v.reward_item) 
    	temp_list["scale"] = v.scale
    	temp_list["name"] = v.name
    	temp_list["level"] = v.need_role_level--string.format(Language.Common.Level1,v.need_role_level)
    	temp_list["limit_level"] = v.need_role_level
	    temp_list["boss_res_id"] = monster_cfg[v.view_resouce].resid
	    temp_list["boss_name"] = monster_cfg[v.view_resouce].name
	    temp_list["boss_icon"] = monster_cfg[v.view_resouce].small_icon
	    table.insert(list,temp_list)
	    if role_level >= v.need_role_level then
	    	cur_page = k
	    end
    end
    return list,cur_page
end

--初始化装备本配置
function TeamEquipFbWGData:InitEquipFbConfig()
	if not self.equipfb_list then
		self.equipfb_list = {}
	end
	self.three_drop_list = {}
	local count = 0
	local cfg_list = TeamEquipFbWGData.Instance:GetHighTeamEquipFbList()
	for prof = GameEnum.ROLE_PROF_1, GameEnum.ROLE_PROF_3 do
		self.three_drop_list[prof] = {}
		local key = "item_" .. prof
		for i = 1, 20 do
			local data = cfg_list[i]
			if data then
				self.three_drop_list[prof][i] = {}
				if not self.equipfb_list[i] then
					self.equipfb_list[i] = cfg_list[i]
					count = count + 1
				end

				if data[key] then
					local split_tab = Split(data[key], "|")
					for k1, v1 in pairs(split_tab) do
						self.three_drop_list[prof][i][tonumber(v1)] = true
					end
				end
			else
				break
			end
		end
	end

	self.max_equip_fb_count = count
end

--检测是否三星必掉落 ( self.three_drop_list , layer 从1开始)
function TeamEquipFbWGData:CheckEquipFbIsThreeDrop(layer, item_id)
	local prof = RoleWGData.Instance:GetRoleProf()
	return ((self.three_drop_list[prof] or {})[layer] or {})[item_id]
end

function TeamEquipFbWGData:GetEquipFbList()
	return self.equipfb_list
end

function TeamEquipFbWGData:GetEquipFbListLimitLevel()
	local list = {}
	-- local level = GameVoManager.Instance:GetMainRoleVo().level
	for i, v in ipairs(self.equipfb_list) do
		--if level >= v.number_show then
			table.insert(list, v)
		--end
	end

	local temp_list = {}
	local length = #list
	for t,q in pairs(list) do
		temp_list[t] = q
	end
	
	return temp_list
end

--获取最大装备数量
function TeamEquipFbWGData:GetEquipFbMaxCount()
	return self.max_equip_fb_count
end

--获取当前最大关卡Index
function TeamEquipFbWGData:GetCurFbIndex()
	local cfg_list = TeamEquipFbWGData.Instance:GetHighTeamEquipFbList()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cur_index = 1
	for i,v in pairs(cfg_list) do
		if role_level >= v.need_role_level then
	    	cur_index = i
	    end
	end
	return cur_index
end

--获取奖励
function TeamEquipFbWGData:GetRewardList(layer)
	local data_list = {}
	local item_list = {}
	local prof = RoleWGData.Instance:GetRoleProf()
	for i,v in ipairs(self.equipfb_list) do
		if v.layer == layer then
			item_list = v["reward_item_" .. prof] or {}
		end
	end

	for i = 0, 20 do
		if item_list[i] then
			local data = {}
			data.num = item_list[i].num
			data.is_bind = item_list[i].is_bind
			local _, big_type = ItemWGData.Instance:GetItemConfig(item_list[i].item_id)
			data.item_id = tonumber(item_list[i].item_id)
			data.show_duobei = true
			data.task_type = RATSDUOBEI_TASK.HAIDIFEIXU
			if self:CheckEquipFbIsThreeDrop(layer + 1, data.item_id) then
				data.is_three_must_drop = true
			end
			table.insert(data_list, data)
		else
			break
		end
	end

	return data_list
end

--记录之前关卡所有可能获得的物品
function TeamEquipFbWGData:RecordHasGetItemList()
	if not self.has_get_item_list then
		self.has_get_item_list = {}
	end
	
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local prof = RoleWGData.Instance:GetRoleProf()
	local cfg = self.equipfb_list
	for i, v in pairs(cfg) do
		--三星通关的记录物品id
		if role_level >= v.need_role_level then
			local item_list = v["reward_item_" .. prof] or {}
			for k1, v1 in pairs(item_list) do
				if not self.has_get_item_list[v1.item_id] then
					self.has_get_item_list[v1.item_id] = true
				end
			end
		end
	end
end

--是否新标记
function TeamEquipFbWGData:GetIsNew(item_id)
	if not self.has_get_item_list then
		return false
	end
	return not self.has_get_item_list[item_id]
end

function TeamEquipFbWGData:GetComsumeGold()
	local other_cfg = self:GetHighTeamEquipFbOther()
	return other_cfg.buy_times_need_gold
end

function TeamEquipFbWGData:GetBuyTimesComsumeGold(will_buy_times)
	if self.buy_times_cfg[will_buy_times] then
		return self.buy_times_cfg[will_buy_times].price
	end
	if will_buy_times > self.max_buy_times then
		return self.buy_times_cfg[self.max_buy_times].price
	end
	return self.buy_times_cfg[1].price
end

function TeamEquipFbWGData:GetSceneCurCfg()
	local cfg = nil
	local scene_id = Scene.Instance:GetSceneId()
	for i, v in pairs(self.team_fb_xiandian_cfg) do
		if v.scene_id == scene_id then
			cfg = v
		end
	end
	return cfg
end

function TeamEquipFbWGData:GetStarTimeList()
	local cfg = self:GetSceneCurCfg()
	local time_list = {}
	if not cfg then
		return time_list
	end

	for i, v in ipairs(self.reward_cfg) do
		if v.layer == cfg.layer then
			time_list[v.star_num] = v.time_length
		end
	end

	return time_list
end
