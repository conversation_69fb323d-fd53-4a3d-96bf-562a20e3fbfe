function FestivalActivityView:OpenCallBackDuoBei()
	FestivalDuoBeiWGCtrl.Instance:SendDayDuoBeiReq()
end

function FestivalActivityView:LoadIndexCallBackDuoBei()
	-- self:DBTimeCountDown()
	self:SetDuoBeiCommonImg()
end

function FestivalActivityView:SetDuoBeiCommonImg()
	local duobei_raw_bg_bundle, duobei_raw_bg_asset = ResPath.GetFestivalRawImages("dbhd")
 	self.node_list["duobei_raw_bg"].raw_image:LoadSprite(duobei_raw_bg_bundle, duobei_raw_bg_asset, function ()
 		self.node_list["duobei_raw_bg"].raw_image:SetNativeSize()
  	end)

	local duobei_list_bg_bundle, duobei_list_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_di1")
	self.node_list["duobei_list_bg"].image:LoadSprite(duobei_list_bg_bundle, duobei_list_bg_asset)

end

function FestivalActivityView:ReleaseDBView()
	if self.db_reward_list then
		self.db_reward_list:DeleteMe()
		self.db_reward_list = nil
	end

	if self.duoibei_count_down and CountDownManager.Instance:HasCountDown(self.duoibei_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.duoibei_count_down)
	end
end

function FestivalActivityView:FlushDBView()
	self:FlushDBReward()
end

function FestivalActivityView:SetDuoBeiTips()
    local cfg = FestivalActDuoBeiWGData.Instance:GetCurParamCfg()
	self:SetRuleInfo(cfg.activity_des, Language.MergeDuoBeiRecharge.TipsActivityHint)
	self:SetOutsideRuleTips(Language.MergeDuoBeiRecharge.DuoBeiRechargeState)
end

function FestivalActivityView:FlushDBReward()
  	local des_cfg = FestivalActDuoBeiWGData.Instance:GetCurParamCfg()
 	local cfg = FestivalActDuoBeiWGData.Instance:GetDuoBeiInfo()
	if cfg ~= nil then
    if not self.db_reward_list then
        self.db_reward_list = AsyncListView.New(FestivalDuoBeiItemRender, self.node_list.db_list)
    end
    self.db_reward_list:SetDataList(cfg)
	end
  self.node_list.doubei_text.text.text = des_cfg.activity_des
end

--有效时间倒计时
-- function FestivalActivityView:DBTimeCountDown()
-- 	self.duoibei_count_down = "duoibei_count_down"
-- 	local invalid_time = FestivalActDuoBeiWGData.Instance:GetActivityInValidTime()
-- 	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
-- 		self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
-- 		CountDownManager.Instance:AddCountDown(self.duoibei_count_down, BindTool.Bind1(self.UpdateDBCountDown, self), BindTool.Bind1(self.OnDBComplete, self), invalid_time, nil, 1)
-- 	end
-- end

-- function FestivalActivityView:UpdateDBCountDown(elapse_time, total_time)
-- 	local valid_time = total_time - elapse_time
-- 	if valid_time > 0 then
-- 		self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
-- 	end
-- end

-- function FestivalActivityView:OnDBComplete()
-- 	self.node_list.db_time_label.text.text = ""
-- end

-------------------------------------

FestivalDuoBeiItemRender = FestivalDuoBeiItemRender or BaseClass(BaseRender)
function FestivalDuoBeiItemRender:__init()
	--TODO
	self.db_render_info_cfg = {}
	
end

function FestivalDuoBeiItemRender:LoadCallBack()
    self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnClickDuoBei, self))
	self.node_list["btn"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_btn_huang"))
    self:SetDuoBeiItemCommonImg()
end

function FestivalDuoBeiItemRender:SetDuoBeiItemCommonImg()
	local duobei_item_bg_bundle, duobei_item_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_lb")
	self.node_list["duobei_item_bg"].image:LoadSprite(duobei_item_bg_bundle, duobei_item_bg_asset)

 	local duobei_item_yuan_bundle, duobei_item_yuan_asset = ResPath.GetFestivalActImages("a2_jrkh_dbhd_bt")
	self.node_list["duobei_item_yuan"].image:LoadSprite(duobei_item_yuan_bundle, duobei_item_yuan_asset, function ()
		self.node_list["duobei_item_yuan"].image:SetNativeSize()
	end)

	local beishu_bg_bundle, beishu_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_dbhd_sbdl")
	self.node_list["beishu_bg"].image:LoadSprite(beishu_bg_bundle, beishu_bg_asset, function ()
		self.node_list["beishu_bg"].image:SetNativeSize()
	end)
end

function FestivalDuoBeiItemRender:__delete()

end

function FestivalDuoBeiItemRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function FestivalDuoBeiItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.name.text.text = self.data.cfg.wanfa_name

	local list = {}
    local show_len = 3
    if not IsEmptyTable(self.data.cfg.reward_item) then
        for i=0,#self.data.cfg.reward_item do
            if i == show_len then
                break
            end
            table.insert(list, self.data.cfg.reward_item[i])
        end
    else
        list = TianshenRoadWGData.Instance:GetDuoBeiRewardListByTaskType(self.data.cfg.task_type)
    end

end

function FestivalDuoBeiItemRender:OnBtnClickDuoBei()
	if self.data and self.data.cfg then
		local param = string.split(self.data.cfg.panel,"#")  
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end
