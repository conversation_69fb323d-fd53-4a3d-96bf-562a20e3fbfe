YanYuGeNobleView = YanYuGeNobleView or BaseClass(SafeBaseView)

function YanYuGeNobleView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_noble_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function YanYuGeNobleView:LoadCallBack()
    if not self.noble_act_task_list then
        self.noble_act_task_list = AsyncBaseGrid.New()
        local t = {col = 3,
            change_cells_num = 1,
            list_view = self.node_list.noble_act_task_list,
            assetBundle = "uis/view/yanyuge_ui_prefab",
            assetName = "noble_task_item_cell",
            itemRender = YYGNobleTaskItemCellRender
        }

        self.noble_act_task_list:CreateCells(t)
        self.noble_act_task_list:SetStartZeroIndex(false)
    end
    
    if not self.noble_func_task_list then
        self.noble_func_task_list = AsyncBaseGrid.New()
        local t = {col = 3,
            change_cells_num = 1,
            list_view = self.node_list.noble_func_task_list,
            assetBundle = "uis/view/yanyuge_ui_prefab",
            assetName = "noble_act_task_item_cell",
            itemRender = YYGNobleTaskItemCellRender
        }

        self.noble_func_task_list:CreateCells(t)
        self.noble_func_task_list:SetStartZeroIndex(false)
    end

    if not self.noble_os_task_list then
        self.noble_os_task_list = AsyncBaseGrid.New()
        local t = {col = 2,
            change_cells_num = 1,
            list_view = self.node_list.noble_os_task_list,
            assetBundle = "uis/view/yanyuge_ui_prefab",
            assetName = "noble_open_server_task_item_cell",
            itemRender = YYGNobleOSTaskItemCellRender
        }

        self.noble_os_task_list:CreateCells(t)
        self.noble_os_task_list:SetStartZeroIndex(false)
    end

    -- self.node_list.desc_noble_tip_content.tmp.text = Language.YanYuGe.NobleDescTipContent

    for i = 1, 3 do
        local str = Language.YanYuGe.NobleDescTipContent[i]

        if nil ~= str and "" ~= str then
            self.node_list["desc_noble_tip" ..i].tmp.text = str
            self.node_list["desc_noble_tip_item" .. i]:CustomSetActive(true)  
        else
            self.node_list["desc_noble_tip_item" .. i]:CustomSetActive(false)    
        end
    end

    self.node_list.desc_noble.tmp.text = Language.YanYuGe.NobleAddExpTipStr
    XUI.AddClickEventListener(self.node_list.btn_add_noble, BindTool.Bind(self.OnClickAddNobleBtn, self))

    local bundle, asset = ResPath.GetRawImagesPNG("a3_yyg_yygz_bj")
    if self.node_list.RawImage_tongyong and nil ~= bundle and nil ~= asset then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")
    local item_cfg = ItemWGData.Instance:GetItemConfig(show_item)
    if item_cfg then
        self.node_list.score_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    XUI.AddClickEventListener(self.node_list.score_bg, BindTool.Bind(self.OnClickScoreBtn, self))
end

function YanYuGeNobleView:ReleaseCallBack()
    if self.noble_act_task_list then
        self.noble_act_task_list:DeleteMe()
        self.noble_act_task_list = nil
    end

    if self.noble_func_task_list then
        self.noble_func_task_list:DeleteMe()
        self.noble_func_task_list = nil
    end

    if self.noble_os_task_list then
        self.noble_os_task_list:DeleteMe()
        self.noble_os_task_list = nil
    end
end

function YanYuGeNobleView:OnFlush(param_t)
    local noble_level = YanYuGeWGData.Instance:GetNobilityLevel()
    self.node_list.desc_noble_level.tmp.text = noble_level
    self.node_list.desc_pro_title.tmp.text = noble_level
    local cur_level_cfg = YanYuGeWGData.Instance:GetNobilityLevelCfgByLevel(noble_level)
    local next_level_cfg = YanYuGeWGData.Instance:GetNobilityLevelCfgByLevel(noble_level + 1)
    local is_max_level = IsEmptyTable(next_level_cfg)
    local nobility_exp = YanYuGeWGData.Instance:GetNobilityExp()
    local nobility_daily_add_score = YanYuGeWGData.Instance:GetNobilityDailyAddScore()

    self.node_list.noble_slider.slider.value = nobility_exp / cur_level_cfg.uplevel_exp
    self.node_list.desc_noble_value.tmp.text = string.format(Language.YanYuGe.NobleSliderValueStr, COLOR3B.C8, CommonDataManager.ConverNumber(nobility_exp), CommonDataManager.ConverNumber(cur_level_cfg.uplevel_exp)) 

    self.node_list.normal_title_img:SetActive(not is_max_level)
    self.node_list.max_title_img:SetActive(is_max_level)
    if is_max_level then
        self.node_list.desc_noble_score.tmp.text = string.format(Language.YanYuGe.NobleMaxScoreShowTipStr, nobility_daily_add_score, cur_level_cfg.daily_score_limit)
        self.node_list.arrow:CustomSetActive(false)
        self.node_list.desc_noble_next_score.tmp.text = ""

        self.node_list.desc_noble_recharge_volume.tmp.text = string.format(Language.YanYuGe.NobleMaxRechargeVolumeShowTipStr, cur_level_cfg.add_recharge_volume)
        self.node_list.recharge_volume_arrow:CustomSetActive(false)
        self.node_list.desc_noble_next_recharge_volume.tmp.text = ""
    else
        self.node_list.desc_noble_score.tmp.text = string.format(Language.YanYuGe.NobleScoreShowTipStr, nobility_daily_add_score, cur_level_cfg.daily_score_limit)
        self.node_list.arrow:CustomSetActive(true)
        self.node_list.desc_noble_next_score.tmp.text = string.format(Language.YanYuGe.NobleNextScoreShowTipStr, next_level_cfg.daily_score_limit)

        self.node_list.desc_noble_recharge_volume.tmp.text = string.format(Language.YanYuGe.NobleRechargeVolumeShowTipStr, cur_level_cfg.add_recharge_volume)
        self.node_list.recharge_volume_arrow:CustomSetActive(true)
        self.node_list.desc_noble_next_recharge_volume.tmp.text = string.format(Language.YanYuGe.NobleNextScoreShowTipStr, next_level_cfg.add_recharge_volume)

        local is_equal = cur_level_cfg.add_recharge_volume == next_level_cfg.add_recharge_volume
        self.node_list.xf_level_text.tmp.text = noble_level + 1
        local daily_score_limit = next_level_cfg.daily_score_limit - cur_level_cfg.daily_score_limit
        local add_recharge_volume = next_level_cfg.add_recharge_volume - cur_level_cfg.add_recharge_volume
        self.node_list.reward_num_text.tmp.text = is_equal and daily_score_limit or add_recharge_volume
        local img_str = is_equal and "a3_yyg_xfnc_bt2" or "a3_yyg_xfnc_bt1"
        local bundle, asset = ResPath.GetRawImagesPNG(img_str)
        self.node_list["normal_title_img"].raw_image:LoadSprite(bundle, asset, function()
            self.node_list["normal_title_img"].raw_image:SetNativeSize()
        end)
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.desc_noble_score_root.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.desc_noble_recharge_volume_root.rect)

    local scene_data_list, act_data_list, open_server_data_list = YanYuGeWGData.Instance:GetYYGZShowTaskDataList()
    self.noble_act_task_list:SetDataList(scene_data_list)
    self.noble_func_task_list:SetDataList(act_data_list)
    self.noble_os_task_list:SetDataList(open_server_data_list)

    local is_can_show = YanYuGeWGData.Instance:GetYYGZIsCanShowOpenServerTask()
    local is_show_open_server = is_can_show and not IsEmptyTable(open_server_data_list)
    self.node_list.normal_content:CustomSetActive(not is_show_open_server)
    self.node_list.open_server_task_content:CustomSetActive(is_show_open_server)

    local score = YanYuGeWGData.Instance:GetCurScore()
    self.node_list.cur_score.tmp.text = score

    -- local cur_batch = YanYuGeWGData.Instance:GetOpenServerTaskCurBatch()
    -- if cur_batch <= 2 then
    --     local bundle, asset = ResPath.GetRawImagesPNG("a3_yyg_xfnc_bt" .. cur_batch)
    --     self.node_list["normal_title_img"].raw_image:LoadSprite(bundle, asset, function()
    --         self.node_list["normal_title_img"].raw_image:SetNativeSize()
    --     end)
    -- end

    self.node_list.desc_act_title.text.text = Language.YanYuGe.DescActTitle
    self.node_list.desc_func_title.text.text = Language.YanYuGe.DescFuncTitle
    self.node_list.desc_os_title.text.text = Language.YanYuGe.DescOsTitle
    self.node_list.desc_noble_tip_title.text.text = Language.YanYuGe.DescNobleTipTitle
end

function YanYuGeNobleView:OnClickAddNobleBtn()
    -- YanYuGeWGCtrl.Instance.level_up_tip:Open()
    ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip)
end

function YanYuGeNobleView:OnClickScoreBtn()
    local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")

    if show_item and show_item > 0 then
        TipWGCtrl.Instance:OpenItem({ item_id = show_item })
    end
end

---------------------------------YYGNobleTaskItemCellRender--------------------------------
YYGNobleTaskItemCellRender = YYGNobleTaskItemCellRender or BaseClass(BaseRender)

function YYGNobleTaskItemCellRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.item1, BindTool.Bind(self.OnClickItemBtn, self, 1))
    XUI.AddClickEventListener(self.node_list.item2, BindTool.Bind(self.OnClickItemBtn, self, 2))
    XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick))
end

function YYGNobleTaskItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    local info = self.data.info
    if YanYuGeWGData.Instance:IsNeedFormatType(cfg.task_type ) then
        self.node_list.desc_name.text.text = string.format(cfg.name,info.progress)
    else
        self.node_list.desc_name.text.text = cfg.name
    end
    if cfg.task_type == YANYUGE_NOBLE_TASK_TYPE.TYPE10 or cfg.task_type == YANYUGE_NOBLE_TASK_TYPE.TYPE11 then
        -- self.node_list.desc_name.text.fontSize = 18
    else
        -- self.node_list.desc_name.text.fontSize = 24
    end

    
    if cfg.complete_limit > 0 then
        self.node_list.desc_task_pro.tmp.text = string.format("<color=#6CFF95>%s</color>/%s", info.complete_count, cfg.complete_limit)
    else
        self.node_list.desc_task_pro.tmp.text = Language.YanYuGe.NobleInfiniteStr
    end

    local is_complete = self.data.is_complete
    local can_get = info.progress >= cfg.param2

    self.node_list.remind:CustomSetActive(can_get and ((cfg.complete_limit <= 0) or (info.complete_count < cfg.complete_limit)))
    self.node_list.remind_num.text.text = "" --math.ceil(info.progress / cfg.param2)

	if cfg.bg ~= "" then
        local bundle, asset = ResPath.GetRawImagesPNG(cfg.bg)
		self.node_list["bg"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["bg"].raw_image:SetNativeSize()
		end)
	end

    self.node_list.act_root:CustomSetActive(not is_complete)
    self.node_list.not_act_root:CustomSetActive(is_complete)

    if not is_complete then
        local star_res_list = GetStarImgResByStar(cfg.star_num)
        for i = 1, 5 do
            -- self.node_list["star"..i]:CustomSetActive(i <= cfg.star_num)
            self.node_list["star"..i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
        end

        local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")
        local item_cfg = ItemWGData.Instance:GetItemConfig(show_item)

        if item_cfg then
            self.node_list.desc_item_icon1.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
        end
        self.node_list.desc_item_num1.tmp.text = cfg.score

        -- local show_exp_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_exp_item")
        -- local exp_item_cfg = ItemWGData.Instance:GetItemConfig(show_exp_item)
        -- if exp_item_cfg then
        --     self.node_list.desc_item_icon2.image:LoadSprite(ResPath.GetItem(exp_item_cfg.icon_id))
        -- end
        self.node_list.desc_item_num2.tmp.text = cfg.exp
    else
        self.node_list.flag_active:CustomSetActive(cfg.show_type == YANYUGE_NOBLE_SHOW_TYPE.ACTIVITY)
        self.node_list.flag_complete:CustomSetActive(cfg.show_type == YANYUGE_NOBLE_SHOW_TYPE.SCENE)
        if YanYuGeWGData.Instance:IsNeedFormatType(cfg.task_type ) then
            local num_str = cfg.param2
            if cfg.param2 > 10000 then
                num_str = math.floor(cfg.param2 / 10000) .. Language.Common.Wan
            end
            self.node_list.desc_name.text.text = string.format(cfg.name,num_str)
        else
            self.node_list.desc_name.text.text = cfg.name
        end
        if cfg.task_type == YANYUGE_NOBLE_TASK_TYPE.TYPE10 or cfg.task_type == YANYUGE_NOBLE_TASK_TYPE.TYPE11 then
            self.node_list.desc_name.text.fontSize = 18
        else
            self.node_list.desc_name.text.fontSize = 24
        end
    end
end

function YYGNobleTaskItemCellRender:OnClickItemBtn(index)
    local item_id = 0
    if index == 1 then
        item_id = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")
    elseif index == 2 then
        item_id = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_exp_item")
    end

    if item_id > 0 then
       TipWGCtrl.Instance:OpenItem({item_id = item_id}) 
    end
end

function YYGNobleTaskItemCellRender:OnClick()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    local info = self.data.info

    if (info.progress >= cfg.param2) and ((cfg.complete_limit <= 0) or (info.complete_count < cfg.complete_limit)) then
        YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_NOBILITY_TASK_REWARD, cfg.seq)
        return
    end

    local open_param = ((self.data or {}).cfg or {}).open_param

    if open_param and open_param ~= "" then
        if open_param == GuideModuleName.MainUIView then
            ViewManager.Instance:CloseAll()
        else
            FunOpen.Instance:OpenViewNameByCfg(open_param)
        end
    end
end

---------------------------------YYGNobleOSTaskItemCellRender--------------------------------
YYGNobleOSTaskItemCellRender = YYGNobleOSTaskItemCellRender or BaseClass(BaseRender)

function YYGNobleOSTaskItemCellRender:LoadCallBack()
    self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
    XUI.AddClickEventListener(self.node_list.btn_receive, BindTool.Bind(self.OnClickReceiveBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickGoBtn, self))
end

function YYGNobleOSTaskItemCellRender:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function YYGNobleOSTaskItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    local info = self.data.info
    -- 特殊处理
    if YanYuGeWGData.Instance:IsNeedFormatType(cfg.task_type ) then
        self.node_list.name.text.text = string.format(cfg.name,info.progress)
    else
        self.node_list.name.text.text = cfg.name
    end
    if cfg.task_type == YANYUGE_NOBLE_TASK_TYPE.TYPE10 or cfg.task_type == YANYUGE_NOBLE_TASK_TYPE.TYPE11 then
        self.node_list.name.text.fontSize = 18
    else
        self.node_list.name.text.fontSize = 24
    end

    self.node_list.desc.text.text = cfg.desc

	if cfg.bg ~= "" then
        local bundle, asset = ResPath.GetRawImagesPNG(cfg.bg)
		self.node_list["bg"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["bg"].raw_image:SetNativeSize()
		end)
	end

    local is_complete = self.data.is_complete
    local can_get = info.progress >= cfg.param2
    self.node_list.remind:CustomSetActive(can_get ) --and ((cfg.complete_limit <= 0) or (info.complete_count < cfg.complete_limit))

    local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")
    local show_exp_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_exp_item")
    local data_list = {}
    if cfg.score ~= "" and cfg.score > 0 then
        table.insert(data_list, {item_id = show_item, num = cfg.score})
    end

    if cfg.exp ~= "" and cfg.exp > 0 then
        table.insert(data_list, {item_id = show_exp_item, num = cfg.exp})
    end
    self.reward_list:SetDataList(data_list)

    self.node_list.flag_complete:CustomSetActive(is_complete)
    self.node_list.btn_receive:SetActive(info.progress >= cfg.param2 and not is_complete)
    self.node_list.btn_go:SetActive(info.progress < cfg.param2 and not is_complete)
end

function YYGNobleOSTaskItemCellRender:OnClickReceiveBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    local info = self.data.info

    if (info.progress >= cfg.param2) and ((cfg.complete_limit <= 0) or (info.complete_count < cfg.complete_limit)) then
        YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_NOBILITY_TASK_REWARD, cfg.seq)
    end
end

function YYGNobleOSTaskItemCellRender:OnClickGoBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local open_param = ((self.data or {}).cfg or {}).open_param

    if open_param and open_param ~= "" then
        if open_param == GuideModuleName.MainUIView then
            ViewManager.Instance:CloseAll()
        else
            FunOpen.Instance:OpenViewNameByCfg(open_param)
        end
    end
end