require("game/revive/revive_wg_data")
require("game/revive/revive_view")

ReviveWGCtrl = ReviveWGCtrl or BaseClass(BaseWGCtrl)

function ReviveWGCtrl:__init()
	if ReviveWGCtrl.Instance ~= nil then
		print_error("[ReviveWGCtrl] Attemp to create a singleton twice !")
		return
	end

	ReviveWGCtrl.Instance = self

	self.revive_data = ReviveWGData.New()
	-- self.revive_view = ReviveView.New(GuideModuleName.ReviveView)
end

function ReviveWGCtrl:__delete()
	-- self.revive_view:DeleteMe()
	-- self.revive_view = nil

	self.revive_data:DeleteMe()
	self.revive_data = nil

	ReviveWGCtrl.Instance = nil
end