require("game/role/role_wg_data")
require("game/role/role_test_view")

require("game/role/equip_wg_data")
require("game/role/role_view")
require("game/role/role_items")
require("game/role/skill/role_skill_view")
require("game/role/skill/skill_upgrade_view")
require("game/role/skill/role_skill_passive")
require("game/role/skill/skill_reset_view")
require("game/role/skill/skill_up_wg_data")
-- require("game/role/title/new_role_info_view")
require("game/role/change_head_view")
require("game/role/role_rename")
-- require("game/role/role_rename_guild")
-- require("game/role/role_money_view")
require("game/role/role_intro_view")
-- require("game/role/role_gonglve_tips")
-- require("game/role/role_trace")
-- require("game/role/xianjie/xianjie_wg_ctrl")
-- require("game/role/xianjie/role_xianjie_view")
require("game/role/role_talent/role_talent_wg_ctrl")
require("game/role/role_talent/role_talent_wg_data")
require("game/role/role_talent/role_talent_view")
require("game/role/role_talent/role_talent_tips")
-- require("game/role/role_xiaogui_view")
require("game/role/role_equip_attr")
require("game/role/exp_addition/exp_addition_view")
require("game/role/exp_addition/exp_addition_wg_data")
require("game/role/head_protocol_tips")
require("game/role/head_big_view")
require("game/role/refining/role_refining_view")
require("game/role/refining/base_refining_cell")
require("game/role/role_branch/role_branch_view")
require("game/role/role_branch/title/role_title_view")
require("game/role/change_roles/change_roles_wg_data")
require("game/role/change_roles/change_roles_view")
require("game/role/change_roles/change_roles_wg_ctrl")

require("game.role.jingjie.jingjie_wg_data")
require("game.role.jingjie.xianwei_jingjie_view")
require("game.role.jingjie.xianwei_jingmai_view")
require("game.role.jingjie.xianwei_jingmai_attr_view")
require("game.role.jingjie.jingjie_up_tip_view")
--------------------------------------------------------------
--角色相关，如属性，装备等
--------------------------------------------------------------
RoleWGCtrl = RoleWGCtrl or BaseClass(BaseWGCtrl)
function RoleWGCtrl:__init()
    if RoleWGCtrl.Instance then
        ErrorLog("[RoleWGCtrl] Attemp to create a singleton twice !")
    end
    -- self.rename_guild_view = RoleRenameGuildView.New()
    -- self.trace_view = RoleTrace.New()
    -- self.gonglve_tips = RoleGongLveTips.New()
    -- self.role_xiaogui_view = RoleXiaoguiView.New()
    RoleWGCtrl.Instance = self
    self.role_data = RoleWGData.New()
    self.role_test_view = RoleTestView.New(GuideModuleName.TestView)

    self.role_view = RoleView.New(GuideModuleName.RoleView)
    self.skill_view = SkillView.New(GuideModuleName.SkillView)
    self.role_talent_tips = RoleTalentTips.New()
    self.skill_reset_view = SkillResetView.New(GuideModuleName.SkillReset)
    self.rename_view = RoleRename.New()
    self.change_head_view = ChangeHeadView.New(GuideModuleName.ChangeHeadView)
    self.equip_attr_desc = RoleEquipAttrView.New()
    self.exp_addititon_view = ExpAdditionView.New(GuideModuleName.ExpAdditionView)
    self.exp_addititon_data = ExpAdditionWGData.New()
    self.head_protocol_tips = HeadProtocolTips.New()
    self.head_big_view = HeadBigView.New()
    self.role_branch_view = RoleBranchView.New(GuideModuleName.RoleBranchView)

    self.change_roles_data = ChangeRolesWGData.New()

    self:ChangeRolesRegisterAllProtocols()
    self:RegisterAllProtocols()

    self.delay_protocol_list = {}
    self.own_boss_list = {}
    self.is_has_roledata = false
    self.impguard_show = false

    self.money_info = {
        bind_coin = 0,
        coin = 0,
        gold = 0,
        bind_gold = 0,
        shengwang = 0,
        silver_ticket = 0,
        zhan_ling = 0,
        zhan_hun = 0,
        replace_coin = 0,
        cash_point = 0,
        recharge_volume = 0,
        chivalrous = 0,
    }

    self.first_time_set_money = true
    self.first_recive_prot = true
    self.is_first_rember = true --是否首次记录银票和声望
    self:InitRoleTalentWGCtrl()

    self.is_open_attr_monitor = false
    self.suit_equip_coin = 0
    self.suit_equip_coin_in_sit = 0
    self.is_open_main_buff_monitor = false

    self:JingJieInit()
end

function RoleWGCtrl:__delete()
    RoleWGCtrl.Instance = nil
    self.delay_protocol_list = {}
    self.own_boss_list = {}
    self.is_has_roledata = nil
    self.impguard_show = nil
    self.money_info = {}
    self.first_time_set_money = nil
    self.first_recive_prot = nil
    self.is_first_rember = nil
    self.equip_flag = nil
    self.has_click_zero = nil
    self.has_recharge_info = nil
    self.is_open_attr_monitor = false
    self.is_open_main_buff_monitor = false

    if self.role_test_view then
        self.role_test_view:DeleteMe()
        self.role_test_view = nil
    end

    if self.equip_attr_desc then
        self.equip_attr_desc:DeleteMe()
        self.equip_attr_desc = nil
    end

    if self.skill_view then
        self.skill_view:DeleteMe()
        self.skill_view = nil
    end

    if self.skill_reset_view then
        self.skill_reset_view:DeleteMe()
        self.skill_reset_view = nil
    end

    if self.head_big_view then
        self.head_big_view:DeleteMe()
        self.head_big_view = nil
    end

    if self.role_view then
        self.role_view:DeleteMe()
        self.role_view = nil
    end

    if self.rename_view then
        self.rename_view:DeleteMe()
        self.rename_view = nil
    end

    if self.role_branch_view then
        self.role_branch_view:DeleteMe()
        self.role_branch_view = nil
    end

    if self.trace_view then
        self.trace_view:DeleteMe()
        self.trace_view = nil
    end

    if self.change_head_view then
        self.change_head_view:DeleteMe()
        self.change_head_view = nil
    end

    if self.gonglve_tips then
        self.gonglve_tips:DeleteMe()
        self.gonglve_tips = nil
    end

    if self.role_xiaogui_view then
        self.role_xiaogui_view:DeleteMe()
        self.role_xiaogui_view = nil
    end

    self.delay_protocol_list = nil
    self.first_recive_prot = nil

    if self.money_show_delay then
        GlobalTimerQuest:CancelQuest(self.money_show_delay)
        self.money_show_delay = nil
    end
    -- cocos2d项目暂时屏蔽
    -- self:DeleteRoleTalentWGCtrl()

    if self.role_data then
        self.role_data:DeleteMe()
        self.role_data = nil
    end

    if self.exp_addititon_view then
        self.exp_addititon_view:DeleteMe()
        self.exp_addititon_view = nil
    end

    if self.exp_addititon_data then
        self.exp_addititon_data:DeleteMe()
        self.exp_addititon_data = nil
    end

    if self.role_talent_tips then
        self.role_talent_tips:DeleteMe()
        self.role_talent_tips = nil
    end

    self.head_protocol_tips:DeleteMe()
    self.head_protocol_tips = nil

    self.change_roles_data:DeleteMe()
    self.change_roles_data = nil

    if self.suit_equip_add_coin_delay then
        GlobalTimerQuest:CancelQuest(self.suit_equip_add_coin_delay)
        self.suit_equip_add_coin_delay = nil
    end

    if self.item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
        self.item_data_change = nil
    end

    self.suit_equip_coin = 0
    self.suit_equip_coin_in_sit = 0

    RemindManager.Instance:UnRegister(RemindName.XiaoGui_BecomeStronger)

    self:JingJieDelete()
end

function RoleWGCtrl:RoleTestViewOpen()
    self.role_test_view:Open()
end

function RoleWGCtrl:RegisterAllProtocols()
    --人物属性
    self:RegisterProtocol(SCLevelChange, "OnLevelChange")
    self:RegisterProtocol(SCRoleChangeProf, "OnRoleChangeProf")
    self:RegisterProtocol(SCRoleAttributeValue, "OnRoleAttributeValue")
    self:RegisterProtocol(SCRoleHpValue, "OnSCRoleHpValue")
    self:RegisterProtocol(SCChaExpChange, "OnChaExpChange")
    self:RegisterProtocol(SCRoleInfoAck, "OnRoleInfoAck")
    self:RegisterProtocol(SCMoneyChange, "OnMoneyChange")
    self:RegisterProtocol(SCRoleNameColorChange, "OnRoleNameColorChange")
    self:RegisterProtocol(SCRoleEvilChange, "OnRoleEvilChange")
    self:RegisterProtocol(SCCapabilityChange, "OnCapabilityChange")
    self:RegisterProtocol(SCRoleXianhun, "OnRoleXianhun")
    self:RegisterProtocol(SCRoleYuanli, "OnRoleYuanli")
    self:RegisterProtocol(SCRoleNuqi, "OnRoleNuqi")
    self:RegisterProtocol(SCNvWaShi, "OnNvWaShi")
    self:RegisterProtocol(SCRoleHunli, "OnRoleHunli")
    self:RegisterProtocol(SCRoleLingJing, "OnRoleLingJing")
    self:RegisterProtocol(SCRoleChengJiu, "OnRoleChengJiu")
    self:RegisterProtocol(SCRoleGongxun, "OnRoleGongxun")
    self:RegisterProtocol(SCRoleDayRevivalTimes, "OnRoleDayRevivalTimes")
    self:RegisterProtocol(SCBattleFieldHonorChange, "OnBattleFieldHonorChange")
    self:RegisterProtocol(SCOtherUserOnlineStatus, "OnOtherUserOnlineStatus")
    self:RegisterProtocol(SCRoleShengwang, "OnSCRoleShengwang")
    self:RegisterProtocol(SCRoleExpExtraPer, "OnRoleExpExtraPer")
    self:RegisterProtocol(SCCommonInfo, "OnCommonInfo")
    self:RegisterProtocol(SCSpecialParamChange, "OnSpecialParamChange")
    self:RegisterProtocol(SCRoleOwnBossInfoChange, "OnRoleOwnBossInfoChange")
    self:RegisterProtocol(SCOtherResourceInfo, "OnSCOtherResourceInfo")
    self:RegisterProtocol(SCEffectBloodChange, "OnSCEffectBloodChange")
    self:RegisterProtocol(SCRoleAuthority, "OnSCRoleAuthority")

    --人物装备列表
    self:RegisterProtocol(SCEquipList, "OnEquipList")
    self:RegisterProtocol(SCEquipChange, "OnEquipChange")
    self:RegisterProtocol(SCEquipmentTihuanRet, "OnEquipmentTihuanRet")
    self:RegisterProtocol(CSTakeOffEquip)
    self:RegisterProtocol(SCEquipmentActiveStarInfo, "OnEquipmentActiveStarInfo")

    self:RegisterProtocol(CSGetExpExtraPer)

    -- 用户挂机自定义数据
    self:RegisterProtocol(CSSetClientCfgReq)
    self:RegisterProtocol(CSSetCustomInfo)
    self:RegisterProtocol(SCGuaJiAndCustomInfo, "OnGuaJiAndCustomInfo")

    -- 更名
    self:RegisterProtocol(CSRoleResetName)
    self:RegisterProtocol(SCRoleResetName, "OnRoleResetName")
    self:RegisterProtocol(CSGuildResetName)

    self:RegisterProtocol(CSRoleZhuanSheng)

    self:RegisterProtocol(CSReqCommonOpreate)

    --追踪令
    self:RegisterProtocol(CSSeekRoleWhere)
    self:RegisterProtocol(SCSeekRoleInfo, "OnSeekRoleInfo")

    -- 人物回血提示
    self:RegisterProtocol(SCRoleRecover, "OnRoleRecover")

    -- 小鬼守护
    self:RegisterProtocol(CSImpGuardOperaReq)

    self:RegisterProtocol(SCImpGuardInfo, "OnImpGuardInfo")

    -- 当日首次登陆
    self:RegisterProtocol(SCRoleDayFirstLogin, "OnSCRoleDayFirstLogin")

    -- 头像
    self:RegisterProtocol(CSUseAttrHead)          -- 1360 使用系统头像
    self:RegisterProtocol(CSSetAvatarTimeStamp)   -- 1465 使用自定义头像
    self:RegisterProtocol(CSHeadAgreeProtocolReq) -- 8494 认同自定义头像协议
    self:RegisterProtocol(SCSendAttrHead, "OnSCSendAttrHead")

    --跨服boss采集物采集时扣血
    self:RegisterProtocol(SCRoleHpChangeOnGather, "OnSCRoleHpChangeOnGather")

    --注册角色装备可替换提醒

    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
    self:RegisterProtocol(CSEquipUpStarNowActiveLevel)

    RemindManager.Instance:Register(RemindName.XiaoGui_BecomeStronger,
        BindTool.Bind1(self.CheckXiaoGuiBecomeStronger, self))

    self:RegisterXiaoGuiBecomeStrongerRemindInBag(RemindName.XiaoGui_BecomeStronger)

    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

    self:JingJiewRegisterAllProtocols()
end

function RoleWGCtrl:RegisterXiaoGuiBecomeStrongerRemindInBag(remind_name)
    local type_list = { GameEnum.EQUIP_TYPE_XIAOGUI }
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, nil, type_list)
end

function RoleWGCtrl:MainuiOpenCreate()
    self.SendGuaJiAndCustomReq()
end

function RoleWGCtrl:GetRoleView()
    return self.role_view
end

function RoleWGCtrl:OpenTitle()
    self.role_view:Open()
    self.role_view:OnClickTitle()
end

function RoleWGCtrl:Open(index, param_t)
    if param_t ~= nil and param_t.sub_view_name == SubViewName.ReName then
        self.rename_view:Open()
        return
    end
    if param_t ~= nil and param_t.sub_view_name == SubViewName.RoleTrace then
        self.trace_view:Open()
        return
    end

    if param_t ~= nil and param_t.sub_view_name == SubViewName.ReNameGuild then
        self.rename_guild_view:Open()
        return
    end

    self.role_view:Open(index)
    if nil == param_t then
        return
    end

    -- 选中技能
    if nil ~= param_t.skill_id or param_t.to_ui_name == "skill_id" then
        local skill_id = param_t.skill_id and param_t.skill_id or tonumber(param_t.to_ui_param)
        self.role_view:Flush(math.floor(TabIndex.role_zhudong_skill / 1000), "skill_id", { skill_id })
    end

    if nil ~= param_t.sub_view_name then
        if param_t.sub_view_name == SubViewName.ReRoleIcon then
            self:OpenChangeHead()
        end
    end
end

function RoleWGCtrl:FlushXianjie(key, param)
    if self.role_view then
        self.role_view:Flush(math.floor(TabIndex.role_xianjie / 1000), key, param)
    end
end

function RoleWGCtrl:FlushView(index, key, param)
    if self.role_view:IsOpen() then
        self.role_view:Flush(index, key, param)
    end
end

function RoleWGCtrl:FlushRoleWorldLevel()
    if self.role_view:IsOpen() then
        self.role_view:FlushWorldLevel()
    end
end

function RoleWGCtrl:OpenChangeHead()
    if not self.change_head_view:IsOpen() then
        ViewManager.Instance:Open(GuideModuleName.ChangeHeadView)
    end
end

function RoleWGCtrl:OpenHeadBigView(data)
    self.head_big_view:SetDataAndOpen(data)
end

function RoleWGCtrl:OpenHeadBigViewByPath(path)
    self.head_big_view:SetPath(path)
end

function RoleWGCtrl:SetGonglveTipsData(data)
    if self.gonglve_tips then
        self.gonglve_tips:SetEquipTipsData(data)
    end
end

function RoleWGCtrl:Close()
    self.role_view:Close()
    self.change_head_view:Close()
end

--请求全部信息，必须进入场景后且主ui加载完成
function RoleWGCtrl:SendReqAllInfo()
    local protocol = CSAllInfoReq.New()
    protocol:EncodeAndSend()
end

--登录时主角角色信息返回
function RoleWGCtrl:OnRoleInfoAck(protocol)
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    AvatarManager.Instance:SetAvatarKey(main_role_vo.role_id, protocol.attr_t.avatar_key_big,
        protocol.attr_t.avatar_key_small, false)
    local obj = Scene.Instance:GetMainRole()
    for k, v in pairs(protocol.attr_t) do
        if obj then
            obj:SetAttr(k, v)
        end
    end

    -- main_role_vo.base_move_speed = Scene.ServerSpeedToClient(protocol.attr_t.base_move_speed)
    -- main_role_vo.move_speed = Scene.ServerSpeedToClient(protocol.attr_t.move_speed)

    --创建场景角色
    Scene.Instance:CreateMainRole()
    -- --打开主界面
    if not MainuiWGCtrl.Instance.view:IsOpen() and not MainuiWGCtrl.Instance.view:IsLoaded() then
        MainuiWGCtrl.Instance:Open()
        -- local scene_type = Scene.Instance:GetSceneType()
        -- MainuiWGCtrl.Instance:ChangeMainuiBySceneType(0, scene_type)
    end

    if not self.is_has_roledata then
        ReportManager:ReportRole(REPORT_ROLE_ACTTION.enterGame)
    end
    
    self.is_has_roledata = true
    self:DoDelayProtocolList()

    RoleWGData.Instance:RoleInfoOk()
    GlobalEventSystem:Fire(LoginEventType.RECV_MAIN_ROLE_INFO)
    if MainuiWGCtrl.Instance.mode ~= main_role_vo.attack_mode then
        MainuiWGCtrl.Instance.mode = main_role_vo.attack_mode
    end

    --主动请求一次仙盟红包信息
    if main_role_vo.guild_id ~= 0 then
        GuildWGCtrl.Instance:SendGuildRedPocketOperate(GUILD_RED_POCKET_OPERATE_TYPE.GUILD_RED_POCKET_OPERATE_INFO_LIST,
            0, 0)
        GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, main_role_vo.guild_id) --仙盟信息
        GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_APPLY_FOR_INFO,
            main_role_vo.guild_id)                                                                                 --仙盟申请列表
        -- GuildWGCtrl.Instance:SendGuildDevelopOperate(GUILD_BUILD_OPERA_TYPE.GUILD_DEVELOP_CONTRIBUTE, 1, 0)
    else
        GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.ALL_GUILD_BASE_INFO,
            main_role_vo.guild_id) --主动请求仙盟的所有帮派信息
    end

    RemindManager.Instance:Fire(RemindName.Guild) --仙盟主界面
    -- RemindManager.Instance:Fire(RemindName.ZhuZaiShenDian)--仙盟主界面

    --登陆成功的时候需要我主动请求一次的协议主要是为了红点
    MainuiWGCtrl.Instance:FlushView(0, "seven_day_icon")
    -- Location.Instance:OnStart()
    self:FlushBianQiangButton()
    MainuiWGCtrl.Instance:FlushSitState()
    MainuiWGCtrl.Instance:OnFlushUINameColor(true)

    if protocol.attr_t.authority_type == AUTHORITY_TYPE.GM then
        BulitInGMCtrl.Instance.gm_top_view:Open()
    end

    --请求手机绑定数据
    --这里需要保证角色信息初始化完。
    --    WelfareWGCtrl.Instance:RequestPhoneBindInfo()
end

function RoleWGCtrl:OnSCRoleAuthority(protocol)
    local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
    if obj ~= nil then
        obj:SetAttr("authority_type", protocol.authority_type)
        if obj:IsMainRole() and protocol.authority_type == AUTHORITY_TYPE.GM then
            BulitInGMCtrl.Instance.gm_top_view:Open()
        end
    end
end

ATTR_ADD_TXT_T = {
    -- shengming_max = "sm",
    -- gongji = "gj",
    -- fangyu = "fy",
    -- pojia = "pj",
    yuansu_sh = "yssh",
    yuansu_hj = "yshj",
    shanbi_per = "sbl",
    mingzhong_per = "mzl",
    baoji_per = "bjl",
    kangbao_per = "kbl",
    lianji_per = "ljjl",
    lianjikang_per = "ljdk",
    jichuan_per = "jc",
    jichuankang_per = "jcdk",
    shengming_qq = "smqq",
    fangtan = "ftsh",
    shanghai_zs = "zssh",
    fangyu_zs = "zshj",
    zengshang_boss_per = "slzs",
    shanghai_jn = "jnsh",
    jineng_shanghai_zj_per = "jnzs",
    jineng_shanghai_jm_per = "jnms",
    huo_shanghai_zj_per = "zbqhjc",
    boss_zhenshang = "bszs",
    boss_palsy_per = "bspl",
    boss_seckill_per = "bsms",
    lei_shanghai_zj_per = "lfshjc",
    lei_shanghai_jm_per = "lfshjm",
    gedang_ms_my_per = "gdmsmy",
    baoji_shanghai_jm_per = "bjms",
    lianji_shanghai_jm_per = "ljjm",
    jianshang_boss_per = "sljs",
    zengshang_guaiwu_per = "gwzs",
    jianshang_guaiwu_per = "gwjs",
    zengshang_per = "wjzs",
    jianshang_per = "wjjs",
    zengshang_bs_per = "bszs",
    jianshang_bs_per = "bsjs",
    mingzhong_yc_per = "ycztmz",
    dikang_yc_per = "ycztdk",
    zhiliaoxiaoguo_per = "zlxg",
    zengshang_yc_per = "ycztjc",
    podang_per = "pdl",
    baoji_shanghai_per = "bj",
    gedang_ms_per = "gdms",
    shengming_hf = "smhf",
    shengming_hf_per = "smhfjc",
    zengshang_gx_per = "gxzs",
    zengshang_xr_per = "xrzs",
    shanghai_jc_per = "shjc",
    shanghai_quan_jc_per = "qsxsh",
    shanghai_jm_per = "shjm",
    shanghai_quan_jm_per = "qsxjm",
    yuansu_jk_per = "ysjk",
    yuansu_kx_per = "yskx",
    lianji_shanghai_per = "ljsh",
    per_pvp_add_hurt = "wjzs",
    per_pvp_reduce_hurt = "wjjs",
    max_hp = "sm",
    gong_ji = "gj",
    fang_yu = "fy",
    shan_bi = "sbl",
    ming_zhong = "mzl",
    bao_ji = "bjl",
    per_baoji_zengshang = "bj",
    per_baoji_jianshang = "bjms",
    po_jia = "pj",
    jian_ren = "jr",
    fujia_shanghai = "shjc",
    dikang_shanghai = "shjm",
    wuxing_gongji = "wxsh",
    wuxing_fangyu = "wxfy",
    per_baoji = "bjl",
    per_kangbao = "bjdk",
    per_skill_zengshang = "jnsh",
    per_skill_jianshang = 'jnms',
    hp = "sm",
    per_shanghai_add = "shjc",
    per_shanghai_reduce = "shjm",
    huixinyiji_per = "hxyj",
    huixinyiji_kang_per = "hxdk",
    gedang_per = "gd",
    gedang_chuantou_per = "gdct",
    gedang_jianshang_per = "gdjs",
    per_mingzhong = "mzl",
    per_shanbi = "sbl",
    baoji_shanghai = "bj",
    zhuagnbei_sm_jc_per = "zbsmjc",      --115 装备生命加成+
    zhuagnbei_gj_jc_per = "zbgjjc",      --116 装备攻击加成+
    zhuagnbei_fy_jc_per = "zbfyjc",      --117 装备防御加成+
    zhuagnbei_pj_jc_per = "zbpjjc",      --118 装备破甲加成+
    shengming_jc_per = "smjc",           --119 生命加成+
    gongji_jc_per = "gjjc",              --120 攻击加成+
    jianren_per = "fyjc",                --121 防御加成+
    fangyu_jc_per = "fyjc",
    chuantou_per = "pjjc",               --122 破甲加成+
    pojia_jc_per = "pjjc",
    yuansu_sh_jc_per = "wxshjc",         --123 五行伤害加成+
    yuansu_hj_jc_per = "wxhjjc",         --124 五行护甲加成+
    shengming_qq_jc_per = "smqqjc",      --129 生命窃取加成+
    fangtan_jc_per = "ftshjc",           --130 反弹伤害加成+
    shanghai_zs_jc_per = "zsshjc",       --131 真实伤害加成+
    fangyu_zs_jc_per = "zshjjc",         --132 真实护甲加成+
    zengshang_boss_dk_per = "slzsdk",    --137 首领增伤抵抗+
    jianshang_boss_dk_per = "sljsdk",    --138 首领减伤抵抗+
    zengshang_guaiwu_dk_per = "gwzsdk",  --139 怪物增伤抵抗+
    jianshang_guaiwu_dk_per = "gwjsdk",  --140 怪物减伤抵抗+
    gongji_sd = "gjsd",                  --154 攻击速度+
    move_speed_per = "ydsd",             --156 移动速度+
    shaguai_jb_diaoluo_per = "sgjbdl",   --167 杀怪金币掉落+
    kangbao_shanghai = "kb",             --180 抗暴伤害固定值+--策划要求改成 抗暴 显示
    shengming_zb_role_jc_per = "jcsmjc", --181 基础生命加成+
    gongji_zb_role_jc_per = "jcgjjc",    --182 基础攻击加成+
    fangyu_zb_role_jc_per = "jcfyjc",    --183 基础防御加成+
    pojia_zb_role_jc_per = "jcpjjc",     --184 基础破甲加成+
    gongji_wuqi_jc_per = "wqgjjc",       --185 武器攻击加成+
    pojia_wuqi_jc_per = "wqpjjc",        --186 武器破甲加成+
    gongji_shiping_jc_per = "spgjjc",    --187 饰品攻击加成+
    kill_monster_per_exp = "dgjyjc",     --188 打怪经验加成+
    baoji_jc_per = "bjjc",               --189 暴击加成+
    kangbao_jc_per = "kbjc",             --190 抗暴加成+
    rare_exterior_rate_per = "zxwgdljc", --191 珍稀外观掉落加成+
    rare_equip_rate_per = "zxzbdljc",    --192 珍稀装备掉落加成+
    move_speed = "ydsd",
}
--打印专用，非通用枚举
local BaseAttrType =
{
    [GameEnum.BASE_CHARINTATTR_TYPE_MAXHP] = GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_JC_PER,
    [GameEnum.BASE_CHARINTATTR_TYPE_GONGJI] = GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_JC_PER,
    [GameEnum.BASE_CHARINTATTR_TYPE_FANGYU] = GameEnum.BASE_CHARINTATTR_TYPE_JIANREN_PER,
    [GameEnum.BASE_CHARINTATTR_TYPE_POJIA] = GameEnum.BASE_CHARINTATTR_TYPE_CHUANTOU_PER,
    [GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_SH] = GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_SH_JC_PER,
    [GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_HJ] = GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_HJ_JC_PER,
}

local BasePetAttrType =
{
    [GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_JC_PER] = "shengming_jc_per",
    [GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_JC_PER] = "gongji_jc_per",
    [GameEnum.BASE_CHARINTATTR_TYPE_JIANREN_PER] = "fangyu_jc_per",
    [GameEnum.BASE_CHARINTATTR_TYPE_CHUANTOU_PER] = "pojia_jc_per",
    [GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_SH_JC_PER] = "yuansu_sh_jc_per",
    [GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_HJ_JC_PER] = "yuansu_hj_jc_per",
}

-- 广播血量的协议单独提出来，不用OnRoleAttributeValue了
function RoleWGCtrl:OnSCRoleHpValue(protocol)
    local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
    if obj == nil or not obj:IsCharacter() or not obj:GetVo() then
        return
    end

    -- 加血特效
    local hp = obj:GetVo().hp
    if protocol.hp_value > hp then
        obj:PlayAddHpEffect()
        FightWGCtrl.Instance:RemoveSpecialEffectInfo(obj)
    end
    obj:SetAttr("hp", protocol.hp_value, protocol.attr_notify_reason)
end

--角色、怪物信息改变
function RoleWGCtrl:OnRoleAttributeValue(protocol)
    local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
    if obj == nil or not obj:IsCharacter() then
        return
    end

    if obj:IsMainRole() then
        if self.is_open_attr_monitor and UnityEngine.Debug.isDebugBuild then
            for k, v in pairs(protocol.attr_pair_list) do
                local attr_name = RoleWGData.Instance:GetRoleAttrNameByType(v.attr_type)

                local debug_str = ""
                local way_str = protocol.attr_notify_reason
                if Language.ServerAttrSource and Language.ServerAttrSource.Source[protocol.attr_notify_reason] then
                    way_str = Language.ServerAttrSource.Source[protocol.attr_notify_reason]
                end

                local change_value = 0
                if attr_name ~= nil and attr_name ~= "" then
                    local is_has_base_attr_addtion = false
                    local old_value = obj:GetAttr(attr_name)
                    if old_value ~= nil then
                        change_value = v.attr_value - old_value
                    end

                    attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_name)
                    if nil ~= BaseAttrType[v.attr_type] then
                        local attr_per_name = BasePetAttrType[BaseAttrType[v.attr_type]]
                        local attr_per_value = RoleWGData.Instance:GetAttr(attr_per_name)
                        if nil ~= attr_per_value and attr_per_value ~= 0 then
                            debug_str = string.format(Language.ServerAttrSource.HasName2, attr_name, v.attr_type,
                                v.attr_type, v.attr_value, change_value, way_str, tostring(attr_per_value / 100) .. "%")
                        else
                            debug_str = string.format(Language.ServerAttrSource.HasName, attr_name, v.attr_type,
                                v.attr_type, v.attr_value, change_value, way_str)
                        end
                    else
                        if v.attr_type >= 200 then
                            attr_name = Language.ServerAttrSource.FightAttr .. attr_name
                        end
                        debug_str = string.format(Language.ServerAttrSource.HasName, attr_name, v.attr_type, v.attr_type,
                            v.attr_value, change_value, way_str)
                    end
                else
                    debug_str = string.format(Language.ServerAttrSource.NoName, v.attr_type, v.attr_value, way_str)
                end

                if change_value ~= 0 then
                    print_log(debug_str)
                end
            end
        end
    end

    for k, v in pairs(protocol.attr_pair_list) do
        local attr_name = RoleWGData.Instance:GetRoleAttrNameByType(v.attr_type)
        --防止报错如果需要再添加
        if attr_name ~= nil then
            -- 加血特效
            if obj:IsMainRole() and ATTR_ADD_TXT_T[attr_name] then
                local old_attr = obj:GetAttr(attr_name)
                if v.attr_type == GameEnum.FIGHT_CHARINTATTR_TYPE_HP then
                    if obj:IsRealDead() and Scene.Instance:GetSceneType() == SceneType.Field1v1 then
                        return
                    end
                elseif old_attr and old_attr < v.attr_value then
                    local show_attr = math.ceil(v.attr_value - old_attr)
                    if EquipmentWGData.Instance:GetAttrIsPerByAttrId(v.attr_type) then
                        show_attr = (show_attr / 100) .. "%"
                    end
                    FightWGCtrl.Instance:ShowAttrText(ATTR_ADD_TXT_T[attr_name], show_attr)
                end
            end

            obj:SetAttr(attr_name, v.attr_value, protocol.attr_notify_reason)
        end
    end
end

function RoleWGCtrl:GetHasDelayShowMoney()
    return self.delay_show_money
end

function RoleWGCtrl:SetHasDelayShowMoney(value)
    if self.money_show_delay and self.delay_show_money == true and value == false then
        GlobalTimerQuest:CancelQuest(self.money_show_delay)
        self.money_show_delay = nil
        self:PlayAddGoldEffect()
    end

    self.delay_show_money = value
end

--金钱改变
function RoleWGCtrl:OnMoneyChange(protocol)
    GlobalEventSystem:Fire(OtherEventType.Gold_Change_Event, protocol)

    -- 代币
    local replace_coin = protocol.gold_virtual[1]
    -- 现金点
    local cash_point = protocol.gold_virtual[2]
    --充值卷
    local recharge_volume = protocol.gold_virtual[3]

    if protocol.reason_type == COST_REASON_TYPE.COIN_FB_ADD_MONEY then -- 金币本不飘字
        local coin = protocol.coin - self.money_info.coin
        if 0 ~= self.money_info.coin and 0 ~= coin then
            Scene.Instance:SimulationFall(coin, protocol.change_type, nil, FallItemHideType.ToRole)
        end
    elseif protocol.reason_type == COST_REASON_TYPE.GOLD_ADD_WEALTH_GOD_DRAW then --喜迎财神延迟
        GodOfWealthWGCtrl.Instance:SetDelayShowGetGold(protocol.gold - self.money_info.gold)
    elseif not self.first_recive_prot then
        local data = {}
        local str_list = {}
        local str_lose_list = {}
        --铜币
        if protocol.coin > self.money_info.coin then
            data = ItemWGData.Instance:GetItemConfig(65535)
            if protocol.reason_type == COST_REASON_TYPE.SUIT_EQUIP_ADD_COIN then -- 套装技能增加铜币
                local add_coin = protocol.coin - self.money_info.coin
                self:AddSuitEquipAdditionCoin(add_coin)
            else
                if protocol.reason_type ~= COST_REASON_TYPE.CREATE_GUILD_FAIL and protocol.reason_type ~= COST_REASON_TYPE.QIFU_ADD_COIN then
                    table.insert(str_list,
                        string.format(Language.Bag.GetItemTxt, data.name, protocol.coin - self.money_info.coin))
                end
                AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.GoldCoinsFall))
                ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.JinBi, protocol.coin - self.money_info.coin)
            end
        elseif protocol.coin < self.money_info.coin then
            -- if protocol.reason_type == COST_REASON_TYPE.COST_GIFT then
            data = ItemWGData.Instance:GetItemConfig(65535)
            if protocol.reason_type ~= COST_REASON_TYPE.COST_CREATE_GUILD then
                table.insert(str_lose_list,
                    string.format(Language.Bag.LoseItemTxt, data.name, self.money_info.coin - protocol.coin))
            end
            -- end
        end

        --
        if protocol.gold > self.money_info.gold then
            data = ItemWGData.Instance:GetItemConfig(65534)
            if protocol.reason_type == COST_REASON_TYPE.REBATE_DAY_ACTIVITY then
                RebateActivityWGCtrl.Instance:SetDelayShowTipsNum(protocol.gold - self.money_info.gold)
            elseif protocol.reason_type == COST_REASON_TYPE.GOLD_ADD_CROSS_GOLD_ZHUANGPAN then --仙玉转盘
                XianyuTrunTableWGCtrl.Instance:SetDelayShowGetGold(protocol.gold - self.money_info.gold)
            elseif protocol.reason_type ~= COST_REASON_TYPE.CREATE_GUILD_FAIL and protocol.reason_type ~= COST_REASON_TYPE.QIFU_ADD_COIN
                and protocol.reason_type ~= COST_REASON_TYPE.GOLD_ADD_MERGE_ZCMIAIMIAO then
                table.insert(str_list,
                    string.format(Language.Bag.GetItemTxt, data.name, protocol.gold - self.money_info.gold))
                --GlobalEventSystem:Fire(OtherEventType.Gold_Change_Event,GameEnum.MONEY_BAR.GOLD)
            end
            ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.XianYu, protocol.gold - self.money_info.gold)
        elseif protocol.gold < self.money_info.gold then
            -- if protocol.reason_type == COST_REASON_TYPE.COST_GIFT then
            data = ItemWGData.Instance:GetItemConfig(65534)
            if protocol.reason_type ~= COST_REASON_TYPE.COST_CREATE_GUILD then
                table.insert(str_lose_list,
                    string.format(Language.Bag.LoseItemTxt, data.name, self.money_info.gold - protocol.gold))
            end
            -- end
        end

        if protocol.bind_gold > self.money_info.bind_gold then
            data = ItemWGData.Instance:GetItemConfig(65533)
            -- table.insert(str_list, string.format(Language.Bag.GetItemTxt, ToColorStr(data.name,GET_TIP_ITEM_COLOR[data.color - 1]), protocol.bind_gold - self.money_info.bind_gold))
            if protocol.reason_type ~= COST_REASON_TYPE.CREATE_GUILD_FAIL and protocol.reason_type ~= COST_REASON_TYPE.QIFU_ADD_COIN then
                table.insert(str_list,
                    string.format(Language.Bag.GetItemTxt, data.name, protocol.bind_gold - self.money_info.bind_gold))
            end
            ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.BangYu, protocol.bind_gold - self.money_info.bind_gold)

            -- 刷新经脉界面
            ViewManager.Instance:FlushView(GuideModuleName.RoleView, TabIndex.jingmai, "all")
            RemindManager.Instance:Fire(RemindName.JingMai)
        elseif protocol.bind_gold < self.money_info.bind_gold then
            -- if protocol.reason_type == COST_REASON_TYPE.COST_GIFT then
            data = ItemWGData.Instance:GetItemConfig(65533)
            if protocol.reason_type ~= COST_REASON_TYPE.COST_CREATE_GUILD then
                table.insert(str_lose_list,
                    string.format(Language.Bag.LoseItemTxt, data.name, self.money_info.bind_gold - protocol.bind_gold))
            end
            -- end
        elseif replace_coin > self.money_info.replace_coin then
            data = ItemWGData.Instance:GetItemConfig(65539)
            if data then
                table.insert(str_list,
                    string.format(Language.Bag.GetItemTxt, data.name, replace_coin - self.money_info.replace_coin))
            end
        elseif cash_point > self.money_info.cash_point then
            data = ItemWGData.Instance:GetItemConfig(65540)
            if data then
                table.insert(str_list,
                    string.format(Language.Bag.GetItemTxt, data.name, cash_point - self.money_info.cash_point))
            end
        elseif recharge_volume > self.money_info.recharge_volume then
            data = ItemWGData.Instance:GetItemConfig(65541)
            if data then
                table.insert(str_list,
                    string.format(Language.Bag.GetItemTxt, data.name, recharge_volume - self.money_info.recharge_volume))
            end
        end

        for i, v in ipairs(str_list) do
            if protocol.reason_type == COST_REASON_TYPE.ADD_PICK_COIN then
                TipWGCtrl.Instance:ShowNumberMsg(v, nil, GameEnum.RD_FLOAT_V, 24, true)
            else
                SysMsgWGCtrl.Instance:ErrorRemind(v)
            end
        end

        for i, v in ipairs(str_lose_list) do
            SysMsgWGCtrl.Instance:ErrorRemind(v)
        end
    end

    self.role_data:SetAttr("replace_coin", replace_coin)
    self.role_data:SetAttr("cash_point", cash_point)
    self.role_data:SetAttr("recharge_volume", recharge_volume)

    -- 有现金点开启功能
    if cash_point > 0 and not FunOpen.Instance:GetFunIsOpened(FunName.CashPointView) then
        FunOpen.Instance:CheckFunOpenByFunName(FunName.CashPointView)
    end

    RoleWGData.Instance:SetBeforeLastMoney(protocol.gold, protocol.bind_gold, nil, protocol.coin, nil,
        nil, cash_point, self.first_recive_prot)
    self.first_recive_prot = false
    self.money_info.coin = protocol.coin
    self.money_info.bind_coin = protocol.bind_coin
    self.money_info.bind_gold = protocol.bind_gold
    self.money_info.gold = protocol.gold
    self.money_info.replace_coin = replace_coin
    self.money_info.cash_point = cash_point
    self.money_info.recharge_volume = recharge_volume

    if self.delay_show_money then
        if self.money_show_delay == nil then
            self.money_show_delay = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.PlayAddGoldEffect, self), 5)
        end
    else
        self:PlayAddGoldEffect()
    end

    --刷新事件
    for _, v in ipairs(RemindByMoneyChange) do
        RemindManager.Instance:Fire(v)
    end
end

-- 播放元宝增加特效
function RoleWGCtrl:PlayAddGoldEffect()
    local role_info = RoleWGData.Instance:GetRoleInfo()
    local is_bind_gold_change = self.money_info.bind_gold > (role_info.bind_gold or 0) or
        self.money_info.gold > (role_info.gold or 0)
    if self.first_time_set_money then
        self.first_time_set_money = nil
        self:SetMoneyChange()
    elseif is_bind_gold_change then
        self:SetMoneyChange()
    else
        self:SetMoneyChange()
    end
end

function RoleWGCtrl:SetMoneyChange()
    self.delay_show_money = false

    RoleWGData.Instance:SetMainRoleInfoValue("shengwang", self.money_info.shengwang)
    RoleWGData.Instance:SetMainRoleInfoValue("gold", self.money_info.gold)
    RoleWGData.Instance:SetMainRoleInfoValue("bind_gold", self.money_info.bind_gold)
    RoleWGData.Instance:SetMainRoleInfoValue("coin", self.money_info.coin)
    RoleWGData.Instance:SetMainRoleInfoValue("bind_coin", self.money_info.bind_coin)
    RoleWGData.Instance:SetMainRoleInfoValue("silver_ticket", self.money_info.silver_ticket)
    RoleWGData.Instance:SetMainRoleInfoValue("zhan_ling", self.money_info.zhan_ling)
    RoleWGData.Instance:SetMainRoleInfoValue("zhan_hun", self.money_info.zhan_hun)
    RoleWGData.Instance:SetMainRoleInfoValue("chivalrous", self.money_info.chivalrous)
    RoleWGData.Instance:SetMainRoleInfoValue("cash_point", self.money_info.cash_point)
end

--经验改变
function RoleWGCtrl:OnChaExpChange(protocol)
    RoleWGData.Instance:SetExpDelta(protocol.delta)
    RoleWGData.Instance:SetExpReason(protocol.reason)
    RoleWGData.Instance:SetAttr("exp", protocol.exp)
    RoleWGData.Instance:SetAttr("vip_extra_role_exp", protocol.vip_extra_role_exp)
    -- local main_role = Scene.Instance:GetMainRole()
    -- local from = true
    -- if not main_role:IsAtkPlaying() then --不在战斗中的时候就飘字飘到界面最上边
    --     from = false
    -- end
    --经验效率加成or杀怪
    if EXP_ADD_REASON.EXP_ADD_REASON_KILL_MONSTER == protocol.reason
        or EXP_ADD_REASON.EXP_ADD_REASON_ADD_ALL == protocol.reason
        or EXP_ADD_REASON.EXP_ADD_REASON_WORLD == protocol.reason
        or EXP_ADD_REASON.EXP_ADD_REASON_HOTSPRING == protocol.reason
        or EXP_ADD_REASON.EXP_ADD_REASON_SCENE == protocol.reason then
        local num_msg = ""
        if protocol.exp_add_per > 0 then
            num_msg = string.format(Language.Bag.GetRoleItemTxt2,
                protocol.delta .. string.format(Language.Skill.Num, GameMath.Round(protocol.exp_add_per / 100) + 100))
            TipWGCtrl.Instance:ShowExp(num_msg, true)
        else
            TipWGCtrl.Instance:ShowExp(string.format(Language.Bag.GetRoleItemTxt2, protocol.delta), true)
        end
    else
        TipWGCtrl.Instance:ShowExp(string.format(Language.Bag.GetRoleItemTxt2, protocol.delta), true)
    end
end

--等级改变
function RoleWGCtrl:OnLevelChange(protocol)
    local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
    if obj ~= nil and obj:IsRole() then
        local old_level = obj:GetAttr("level")
        obj:SetAttr("level", protocol.level)
        obj:SetAttr("exp", protocol.exp)
        obj:SetAttr("max_exp", protocol.max_exp)
        obj:SetAttr("vip_extra_role_exp", protocol.vip_extra_role_exp)

        local new_level = protocol.level
        if obj:IsMainRole() and old_level < new_level then
            GlobalEventSystem:Fire(OtherEventType.ROLE_LEVEL_UP)
            for i = old_level + 1, protocol.level do
                FightWGCtrl.Instance:DoUplevelText(i)
            end

            -- 前200级 每5级。200级后每1级 上报
            if new_level <= 200 then
                if not self.report_main_role_level or (new_level - self.report_main_role_level) >= 5 then
                    self.report_main_role_level = new_level
                    ReportManager:ReportRole(REPORT_ROLE_ACTTION.levelUp, new_level)
                end
            else
                ReportManager:ReportRole(REPORT_ROLE_ACTTION.levelUp, new_level)
            end
        end

        --充值界面各种弹窗
        local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
        if main_role_vo.obj_id == protocol.obj_id then
            local is_vip_power = VipPower.Instance:GetHasPower(VipPowerId.scene_fly)
            local super_transmit_cfg = ConfigManager.Instance:GetAutoConfig("funopen_auto").funopen_list.super_transmit
            if super_transmit_cfg then
                local open_level = super_transmit_cfg.task_level
                if protocol.level == open_level and not is_vip_power then
                    FunOpen.Instance:OpenViewByName(GuideModuleName.SuperTransmit)
                end
            end
            local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
            local user_vo = GameVoManager.Instance:GetUserVo()
            if AgentAdapter.ReportOnRoleLevUp and main_role_vo ~= nil then
                local zone_name = user_vo.plat_server_id .. "服-" .. user_vo.plat_server_name
                AgentAdapter:ReportOnRoleLevUp(main_role_vo.role_id, main_role_vo.role_name, main_role_vo.level,
                    main_role_vo.server_id, zone_name)
            end

            --刷新每日必做数据
            BiZuoWGData.Instance:UpdateBiZuoInfo()

            LoginWGData.Instance:SetServerListLevel()

            self:FlushBianQiangButton()
        end
    end
end

function RoleWGCtrl:FlushBianQiangButton(recharge, card)
    MainuiWGData.Instance:SetShouChongBianqiang(false)
    if recharge then
        self.has_recharge_info = true
    end

    if not self.has_recharge_info then --第一次充值协议下发后开始判断
        return
    end

    if not (FunOpen.Instance:GetFunIsOpened(FunName.ShouChong) and ServerActivityWGData.Instance:FirstChargeIsOpen()) then
        return
    end

    local num = RechargeWGData.Instance:GetHistoryRecharge() <= 0 and 1 or 0
    if num ~= 0 then
        -- 特殊需求：当没有其他变强的时候首充不要显示, 故首充变强从原逻辑剥离开来
        -- 特殊需求：首值过后将不再显示 变强--首充
        MainuiWGData.Instance:SetShouChongBianqiang(true, function()
            FunOpen.Instance:OpenViewByName(GuideModuleName.FirstRechargeView)
        end)
    end
end

--飞升转职
function RoleWGCtrl:OnRoleChangeProf(protocol)
    local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
    if obj ~= nil and obj:IsRole() then
        local old_zhuanzhi_level = self.role_data:GetZhuanZhiNumber()
        obj:SetAttr("prof", protocol.prof)
        local prof = math.floor(protocol.prof / 10)
        -- 转职成功弹窗
        if protocol.obj_id == Scene.Instance.main_role:GetObjId() and prof > old_zhuanzhi_level then
            local data_list = TransFerWGData.Instance:GetTransFerSingleRewardList(prof, true)
            TransFerWGCtrl.Instance:OpenTransferSuccessView(prof)
        end
        prof = (prof < 1 and 1 or prof) * 10
        
        ViewManager.Instance:FlushView(GuideModuleName.ZhuanSheng)
        ViewManager.Instance:FlushView(GuideModuleName.DujieView)
    end
end

--战斗力变更
function RoleWGCtrl:OnCapabilityChange(protocol)
    self.role_data:SetCapabilityList(protocol.capability_list)

    local other_cap = 0
    --在永夜之巅需要加上装备的加战力
    local scene_type = Scene.Instance:GetSceneType()
    RoleWGData.Instance:SetOriginCapability(protocol.capability)
    if scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then
        local self_info = EternalNightWGData.Instance:GetSelfInfo()
        other_cap = self_info.equip_attr_info and self_info.equip_attr_info.equip_capability or 0
    end

    local cap = protocol.capability + other_cap
    RoleWGData.Instance:SetAttr("capability", cap)

    if self.role_view:IsOpen() then
        self.role_view:FlushRoleZhandouli()
    end
end

--仙魂变化
function RoleWGCtrl:OnRoleXianhun(protocol)
    local delta_xianhun = protocol.xianhun - GameVoManager.Instance:GetMainRoleVo().xianhun
    if delta_xianhun > 0 then
    end

    RoleWGData.Instance:SetAttr("xianhun", protocol.xianhun)
end

--特殊值改变
function RoleWGCtrl:OnSpecialParamChange(protocol)
    local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
    if obj ~= nil and obj:IsRole() then
        obj:SetAttr("special_param", protocol.special_param1)
        obj:SetAttr("special_param2", protocol.special_param2)
        -- obj:UpdateNameBoard()
        -- Scene.Instance:SceneSpecialHandler(obj)
    end
end

--银票和声望和侠义值改变
function RoleWGCtrl:OnSCOtherResourceInfo(protocol)
    GlobalEventSystem:Fire(OtherEventType.Gold_Change_Event, protocol)
    if protocol.reason == COST_REASON_TYPE.COIN_FB_ADD_MONEY then
        local silver_ticket = protocol.silver_ticket - self.money_info.silver_ticket
        if 0 ~= self.money_info.silver_ticket and 0 ~= silver_ticket then
            Scene.Instance:SimulationFall(silver_ticket, 0, nil, FallItemHideType.ToRole)
        end
    end

    if not self.is_first_rember then
        local str_list = {}
        local str_lose_list = {}
        if protocol.shengwang > self.money_info.shengwang then
            table.insert(str_list,
                string.format(Language.Bag.GetItemTxt, Language.Bag.ShengWangName,
                    protocol.shengwang - self.money_info.shengwang))
            ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.ShengWang, protocol.shengwang - self.money_info.shengwang)
        elseif protocol.shengwang < self.money_info.shengwang then
            table.insert(str_lose_list,
                string.format(Language.Bag.LoseItemTxt, Language.Bag.ShengWangName,
                    self.money_info.shengwang - protocol.shengwang))
        end

        if protocol.silver_ticket > self.money_info.silver_ticket then
            table.insert(str_list,
                string.format(Language.Bag.GetItemTxt, Language.Bag.SilverName,
                    protocol.silver_ticket - self.money_info.silver_ticket))
            ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.YuanBao,
                protocol.silver_ticket - self.money_info.silver_ticket)
        elseif protocol.silver_ticket < self.money_info.silver_ticket then
            table.insert(str_lose_list,
                string.format(Language.Bag.LoseItemTxt, Language.Bag.SilverName,
                    self.money_info.silver_ticket - protocol.silver_ticket))
        end

        if protocol.zhan_ling > self.money_info.zhan_ling then
            ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.ZhanLing, protocol.zhan_ling - self.money_info.zhan_ling)
        end

        if protocol.zhan_hun > self.money_info.zhan_hun then
            ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.ZhanHun, protocol.zhan_hun - self.money_info.zhan_hun)
        end

        if protocol.chivalrous > self.money_info.chivalrous then
            table.insert(str_list,
                string.format(Language.Bag.GetItemTxt, Language.Bag.ChivalrousName,
                    protocol.chivalrous - self.money_info.chivalrous))
            ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.Chivalrous,
                protocol.chivalrous - self.money_info.chivalrous)
        elseif protocol.chivalrous < self.money_info.chivalrous then
            table.insert(str_lose_list,
                string.format(Language.Bag.LoseItemTxt, Language.Bag.ChivalrousName,
                    self.money_info.chivalrous - protocol.chivalrous))
        end

        for i, v in ipairs(str_list) do
            SysMsgWGCtrl.Instance:ErrorRemind(v)
        end
        for i, v in ipairs(str_lose_list) do
            SysMsgWGCtrl.Instance:ErrorRemind(v)
        end
    end

    self.money_info.shengwang = protocol.shengwang
    self.money_info.silver_ticket = protocol.silver_ticket
    self.money_info.zhan_ling = protocol.zhan_ling
    self.money_info.zhan_hun = protocol.zhan_hun
    self.money_info.chivalrous = protocol.chivalrous
    RoleWGData.Instance:SetBeforeLastMoney(nil, nil, protocol.silver_ticket, nil, protocol.shengwang,
        protocol.chivalrous, nil, self.is_first_rember)

    self.is_first_rember = false
    self:SetMoneyChange()

    GlobalEventSystem:Fire(OtherEventType.Gold_Change_Event_End, protocol)
end

function RoleWGCtrl:OnRoleOwnBossInfoChange(protocol)
    local sence_type = Scene.Instance:GetSceneType()
    -- print_error("---boss 归属---", protocol.monster_obj_id, protocol.owner_name)
    if sence_type == SceneType.PERSON_BOSS or
        sence_type == SceneType.Kf_PVP or
        sence_type == SceneType.Kf_PVP or
        sence_type == SceneType.HIGH_TEAM_EQUIP_FB then
        return
    end

    local target_objid
    if SceneObj.select_obj and SceneObj.select_obj:GetVo() then
        target_objid = SceneObj.select_obj.vo.obj_id
    end

    -- local target_obj, target_objid = MainuiWGData.Instance:GetTargetObj()
    BossWGData.Instance:SetOwnBossList(protocol)

    -- 无奈的修复方式
    if target_objid == protocol.monster_obj_id then
        GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION, nil)
    end

    local name = nil

    if sence_type == SceneType.CROSS_LAND_WAR then
        local owner_name = protocol.owner_name
        local camp = -1
        -- 遍历拥有着列表

        for k, v in pairs(protocol.owner_list) do
            if owner_name == v.name then
                local uuid = RoleWGData.Instance:GetUUid()
                if uuid == v.owner_uuid then
                    camp = PositionalWarfareWGData.Instance:GetMyCamp()

                    if camp >= 0 then
                        local camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(camp)
                        name = camp_cfg and camp_cfg.camp_name or protocol.owner_name
                    else
                        name = protocol.owner_name
                    end
                else
                    local monster_obj_id = protocol.monster_obj_id

                    BrowseWGCtrl.Instance:BrowRoelInfo(v.owner_uid, function(protocol)
                        local guild_id = protocol.guild_id
                        local server_id = protocol.server_id

                        local camp_cfg = PositionalWarfareWGData.Instance:GetCampCfgByServerIdAndGUildId(server_id,
                            guild_id)
                        name = camp_cfg and camp_cfg.camp_name or owner_name

                        BossWGData.Instance:SetOwnBossListName(monster_obj_id, name)

                        if monster_obj_id == target_objid then
                            GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION, name)
                        end
                    end)

                    return
                end

                break
            end
        end
    else
        if protocol.is_there_a_team == 1 then
            if sence_type == SceneType.Shenyuan_boss then --深渊boss不显示[队]
                name = protocol.owner_name
            else
                name = string.format(Language.Boss.BossTeam, protocol.owner_name)
            end
        else
            name = protocol.owner_name
        end
    end

    BossWGData.Instance:SetOwnBossListName(protocol.monster_obj_id, name)

    if protocol.monster_obj_id == target_objid then
        GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION, name)
    end

    -- 跨服龙脉
    if sence_type == SceneType.CrossLongMai then
        CrossLongMaiWGData.Instance:SetOwnBossInfo(protocol)
        CrossLongMaiWGCtrl.Instance:FlushSceneView("boss_list")
    end
end

--仙魂变化
function RoleWGCtrl:OnRoleYuanli(protocol)
    local delta_yuanli = protocol.yuanli - GameVoManager.Instance:GetMainRoleVo().yuanli
    if delta_yuanli > 0 then
        -- SystemHint.Instance:FloatingLabel(string.format(Language.SysRemind.AddLingQi, delta_yuanli))
    end

    RoleWGData.Instance:SetAttr("yuanli", protocol.yuanli)
end

--怒气变化
function RoleWGCtrl:OnRoleNuqi(protocol)
    -- print_error("【----怒气变化-----】：", protocol.nuqi)
    RoleWGData.Instance:SetAttr("nuqi", protocol.nuqi)

    MainuiWGCtrl.Instance:UpdataSiXiangSkillNuQi()
end

function RoleWGCtrl:OnNvWaShi(protocol)
    local delta_nvwashi = protocol.nv_wa_shi - GameVoManager.Instance:GetMainRoleVo().nv_wa_shi
    if delta_nvwashi > 0 then
        -- SystemHint.Instance:FloatingLabel(string.format(Language.SysRemind.AddNvWaShi, delta_nvwashi))
    end

    RoleWGData.Instance:SetAttr("nv_wa_shi", protocol.nv_wa_shi)
    print("RoleWGCtrl:OnNvWaShi change", protocol.nv_wa_shi)
end

function RoleWGCtrl:OnRoleHunli(protocol)
    local delta_hunli = protocol.hunli - GameVoManager.Instance:GetMainRoleVo().hunli
    if delta_hunli > 0 then
        -- SystemHint.Instance:FloatingLabel(string.format(Language.SysRemind.AddHunLi, delta_hunli))
    end

    RoleWGData.Instance:SetAttr("hunli", protocol.hunli)
    print("RoleWGCtrl:OnRoleHunli change", protocol.hunli)
end

function RoleWGCtrl:OnRoleLingJing(protocol)
    local delta_lingjing = protocol.lingjing - GameVoManager.Instance:GetMainRoleVo().lingjing
    if delta_lingjing > 0 then
        -- SystemHint.Instance:FloatingLabel(string.format(Language.SysRemind.AddLingJing, delta_lingjing))
    end

    RoleWGData.Instance:SetAttr("lingjing", protocol.lingjing)
    print("RoleWGCtrl:OnRoleLingJing change", protocol.lingjing)
end

function RoleWGCtrl:OnRoleChengJiu(protocol)
    local delta_chengjiu = protocol.chengjiu - GameVoManager.Instance:GetMainRoleVo().chengjiu
    if delta_chengjiu > 0 then
        -- SystemHint.Instance:FloatingLabel(string.format(Language.SysRemind.AddChengJiu, delta_chengjiu))
    end

    RoleWGData.Instance:SetAttr("chengjiu", protocol.chengjiu)
    print("RoleWGCtrl:OnRoleChengJiu change", protocol.chengjiu)
end

function RoleWGCtrl:OnRoleGongxun(protocol)
    if protocol.detal_gongxun > 0 then
        -- SystemHint.Instance:FloatingLabel(string.format(Language.SysRemind.AddGongxun, protocol.detal_gongxun))
    end

    RoleWGData.Instance:SetAttr("gongxun", protocol.gongxun)
    print("RoleWGCtrl:OnRoleGongxun change", protocol.gongxun)
end

function RoleWGCtrl:OnRoleDayRevivalTimes(protocol)
    RoleWGData.Instance:SetAttr("day_revival_times", protocol.day_revival_times)
end

--荣誉积分
function RoleWGCtrl:OnBattleFieldHonorChange(protocol)
    RoleWGData.Instance:SetAttr("honour", protocol.honor)
    --荣誉
    local item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR
    local data = ItemWGData.Instance:GetItemConfig(item_id)
    if protocol.delta_honor > 0 then
        local str = string.format(Language.SysRemind.AddHonor, protocol.delta_honor)
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Bag.GetItemTxt, data.name, protocol.delta_honor))
    elseif protocol.delta_honor < 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Bag.LoseItemTxt, data.name,
            math.abs(protocol.delta_honor)))
    end
end

--声望
function RoleWGCtrl:OnSCRoleShengwang(protocol)
    -- local shengwang = protocol.shengwang - GameVoManager.Instance:GetMainRoleVo().shengwang
    -- if shengwang > 0 then
    -- SystemHint.Instance:FloatingLabel(string.format(Language.SysRemind.AddShengWang, shengwang))
    -- end
    -- RoleWGData.Instance:SetAttr("shengwang", protocol.shengwang)
    RoleWGData.Instance:SetAttr("day_reward_shengwang", protocol.day_reward_shengwang)
    RoleWGData.Instance:SetAttr("day_assist_shengwang", protocol.day_assist_shengwang)
    ViewManager.Instance:FlushView(GuideModuleName.BossAssist)
end

--经验加成
function RoleWGCtrl:OnRoleExpExtraPer(protocol)
    -- cocos2d项目暂时屏蔽
    -- Log("RoleWGCtrl:OnRoleExpExtraPer type:" .. protocol.exp_extra_type .. "   value" .. protocol.exp_extra_per)
    -- if 0 == protocol.exp_extra_type then   -- 所有经验加成
    -- self.role_data:SetExpExtraPer(protocol.exp_extra_per)
    -- self.role_view:Flush(11,"exp_extra")

    -- elseif 1 == protocol.exp_extra_type then -- 组队经验加成
    -- TaskFollow.Instance:GetTeamExp(protocol.exp_extra_per)
    -- TaskFollow.Instance:OnTeamDataChange()
    -- end
end

--其它玩家在线信息改变
function RoleWGCtrl:OnOtherUserOnlineStatus(protocol)
    GlobalEventSystem:Fire(OtherEventType.ROLE_ONLINE_CHANGE, protocol.role_id, protocol.is_online)
end

--角色名字颜色改变
function RoleWGCtrl:OnRoleNameColorChange(protocol)
    local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
    if obj ~= nil and obj:IsRole() then
        obj:SetAttr("name_color", protocol.name_color)
        -- obj:GetVo().name_color = protocol.name_color
        -- obj:UpdateNameBoard()
        QualityManager.Instance:RefreshShieldSameCampRule()
        obj:UpdateTitle()
    end
end

--罪恶值值改变
function RoleWGCtrl:OnRoleEvilChange(protocol)
    local role_vo = RoleWGData.Instance.role_vo
    local dif_evil = protocol.evil - role_vo.evil
    -- local sign = dif_evil > 0 and "+" or ""
    -- local evil_dec = "{wordcolor;ff0000;" .. string.format(Language.SysRemind.AddEvil, sign .. dif_evil) .. "}"
    -- SystemHint.Instance:FloatingLabel(evil_dec)
    RoleWGData.Instance:SetAttr("evil", protocol.evil)
    BuffTip.Instance:FlushRedEvil()
end

--装备列表
function RoleWGCtrl:OnEquipList(protocol)
    -- print_error("----人物装备列表----", #protocol.equip_list, protocol.equip_list)
    EquipWGData.Instance:SetDataList(protocol.equip_list, protocol.fabao_info)
    -- EquipWGData.Instance:SetEquipStarActiveLevel(protocol.equip_active_star)
    GlobalEventSystem:Fire(OtherEventType.StrangerStarLevel)
    GlobalEventSystem:Fire(OtherEventType.RoleStrangerStarLevel)
    RemindManager.Instance:Fire(RemindName.BagSSS)
end

--单个装备改变
function RoleWGCtrl:OnEquipChange(protocol)
    -- print_error("----单个装备改变----", protocol)
    EquipWGData.Instance:ChangeDataInGrid(protocol.equip_data)
end

function RoleWGCtrl:OnEquipmentActiveStarInfo(protocol)
    EquipWGData.Instance:SetEquipStarActiveLevel(protocol.active_total_star_list)
end

function RoleWGCtrl:OnEquipmentTihuanRet(protocol)
    EquipWGData.Instance:OnEquipmentTihuanRet(protocol)
end

function RoleWGCtrl:OnRoleRecover(protocol)
end

--跨服boss采集物采集时扣血
function RoleWGCtrl:OnSCRoleHpChangeOnGather(protocol)
    if protocol.change_hp < 0 then --扣血
        local main_role = Scene.Instance:GetMainRole()
        -- local draw_obj = main_role:GetDrawObj()
        -- local bottom_point = draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
        -- FightText.Instance:ShowBeHurt(protocol.change_hp, nil, bottom_point, nil, true)

        local data = {}
        data.fighttype = FIGHT_TYPE.NORMAL
        data.blood = protocol.change_hp
        HUDManager.Instance:ShowHurtEnter(main_role, data)
    end
end

--脱下装备
function RoleWGCtrl:CSTakeOffEquip(index)
    local cmd = ProtocolPool.Instance:GetProtocol(CSTakeOffEquip)
    cmd.index = index or -1
    cmd:EncodeAndSend()
end

function RoleWGCtrl:GetIsHasRoleData()
    return self.is_has_roledata
end

--注册延迟理协议，针对角色属性未返回时，其他需要用到角色属性的协议先返回
--服务端设计上的问题
function RoleWGCtrl:RegisterDelayProtocol(fun, protocol)
    if self.is_has_roledata then
        return
    end
    self.delay_protocol_list[#self.delay_protocol_list + 1] = fun
end

--角色属性返回后处理注册的延迟协议
function RoleWGCtrl:DoDelayProtocolList()
    for k, v in pairs(self.delay_protocol_list) do
        v()
    end
end

-- 修改名称
function RoleWGCtrl.SendRoleResetName(new_name, is_item_reset)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRoleResetName)
    protocol.is_item_reset = is_item_reset
    protocol.new_name = new_name
    protocol:EncodeAndSend()
end

-- 请求用户自定义数据
function RoleWGCtrl.SendGuaJiAndCustomReq()
    -- print_error('--SendGuaJiAndCustomReq----------------------')
    -- 废弃，此返回已加到1454返回 2019/1/10
    -- local protocol = ProtocolPool.Instance:GetProtocol(CSSetClientCfgReq)
    -- protocol:EncodeAndSend()
end

-- 请求用户自定义数据返回
function RoleWGCtrl:OnGuaJiAndCustomInfo(protocol)
    -- print_error('OnGuaJiAndCustomInfo', protocol.guaji_info)
    RoleWGData.Instance:SetGuaJiAndCustomInfo(protocol)
    ChatWGData.Instance:SetGuaJiAndCustomInfo(protocol)
    GlobalEventSystem:Fire(SkillEventType.FLUSH_SKILL_LIST)
end

-- 设置用户自定义数据
function RoleWGCtrl.SetCustomInfo(str)
    -- print_error('---2789---', str)
    if string.len(str) > 256 then
        print_error('save string len is too long ')
        return
    end

    local protocol = ProtocolPool.Instance:GetProtocol(CSSetCustomInfo)
    protocol.str = str
    protocol:EncodeAndSend()
end

-- 修改仙盟名称
function RoleWGCtrl.SendGuildResetName(new_name, guild_id)
    guild_id = guild_id or RoleWGData.Instance.role_vo.guild_id
    local protocol = ProtocolPool.Instance:GetProtocol(CSGuildResetName)
    protocol.guild_id = guild_id
    protocol.new_name = new_name
    protocol:EncodeAndSend()
end

-- 名称修改
function RoleWGCtrl:OnRoleResetName(protocol)
    local name = protocol.game_name
    local obj_id = protocol.obj_id
    if obj_id == Scene.Instance.main_role:GetObjId() then -- 如果是自己
        local obj = Scene.Instance.main_role
        if obj then
            RoleWGData.Instance:SetAttr("name", name)
            RoleWGData.Instance:SetAttr("role_name", name)
            obj:GetVo().name = name or ""
            obj:GetVo().role_name = name or ""
            obj:ReloadUIName()
            GlobalLocalRoleName = name
            -- obj:UpdateNameBoard()
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.ChangeNameSuc)
        end

        local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
        local user_vo = GameVoManager.Instance:GetUserVo()
        if AgentAdapter.SubmitRoleData and main_role_vo ~= nil and tonumber(main_role_vo.server_id) < 2000 then
            local zone_name = user_vo.plat_server_id .. "服-" .. user_vo.plat_server_name
            AgentAdapter:SubmitRoleData(main_role_vo.role_id, main_role_vo.role_name, main_role_vo.level,
                main_role_vo.server_id, zone_name)
        end

        ReportManager:ReportRole(REPORT_ROLE_ACTTION.rename)
    else
        for k, v in pairs(Scene.Instance.obj_list) do
            if v:GetObjId() == obj_id then
                v:GetVo().name = name or ""
                v:ReloadUIName()
                -- v:UpdateNameBoard()
            end
        end
    end
    self.role_view:Flush(TabIndex.role_intro)
    if self.rename_view:IsOpen() then
        self.rename_view:Close()
    end
end

--对角色改名切换商城特殊处理
function RoleWGCtrl:GetRenameViewOpen()
    return self.rename_view:IsOpen()
end

function RoleWGCtrl:SetRenameViewActive(active)
    self.rename_view:SetRootNodeActive(active)
end

function RoleWGCtrl:SendRoleZhuanSheng()
    local protocol = ProtocolPool.Instance:GetProtocol(CSRoleZhuanSheng)
    protocol:EncodeAndSend()
end

function RoleWGCtrl:ReqRoleExpExtraPer()
    local protocol = ProtocolPool.Instance:GetProtocol(CSGetExpExtraPer)
    protocol:EncodeAndSend()
end

function RoleWGCtrl:ReqCommonOpreate(operate_type, param1, param2, param3, param4)
    local protocol = ProtocolPool.Instance:GetProtocol(CSReqCommonOpreate)
    protocol.operate_type = operate_type
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
    protocol.param4 = param4 or 0

    protocol:EncodeAndSend()
end

function RoleWGCtrl:OnCommonInfo(protocol)
    -- print_error("RoleWGCtrl:OnCommonInfo", protocol)
    -- cocos2d项目代码暂时屏蔽
    if protocol.info_type == SC_COMMON_INFO_TYPE.SCIT_JINGHUA_HUSONG_INFO then        -- 同步精华护送信息
        self:OnJinghuaInfo(protocol)
    elseif protocol.info_type == SC_COMMON_INFO_TYPE.SCIT_RAND_ACT_ZHUANFU_INFO then  -- 随机活动专服信息
        ServerActivityWGData.Instance:SetServerSystemInfo(protocol)
    elseif protocol.info_type == SC_COMMON_INFO_TYPE.SCIT_GUILD_DEVOTE_INFO then -- 仙盟贡献
        local is_first = protocol.param4 == 1
        if protocol.param1 > 0 and not is_first then
            local old_longhun = RoleWGData.Instance:GetAttr("longhun")
            local lerp_longhun = protocol.param1 - old_longhun
            if lerp_longhun > 0 then
                local str = string.format(Language.Bag.GetItemTxt, Language.Bag.BangGong, lerp_longhun)
                SysMsgWGCtrl.Instance:ErrorRemind(str)
                ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.GuildGongXian, lerp_longhun)
            end
        end
        RoleWGData.Instance:SetAttr("longhun", protocol.param1)
        RoleWGData.Instance:SetAttr("history_longhun", protocol.param2)
        RoleWGData.Instance:SetAttr("day_longhun", protocol.param3)
    end
end

-- 追踪令
function RoleWGCtrl:SendSeekRoleWhere(name)
    local protocol = ProtocolPool.Instance:GetProtocol(CSSeekRoleWhere)
    protocol.seek_name = name
    protocol:EncodeAndSend()
end

-- 追踪令返回
function RoleWGCtrl:OnSeekRoleInfo(protocol)
    if protocol.scene_id <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.TraceOutLine)
        return
    end
    local scene_cfg = ConfigManager.Instance:GetSceneConfig(protocol.scene_id)
    local scene_type = scene_cfg and scene_cfg.scene_type or nil

    if nil == scene_type or 0 ~= scene_type then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.TraceOnFb)
        return
    end

    local scene_name = scene_cfg and scene_cfg.name or ""
    local alert = nil
    local function close_callback()
        if nil ~= alert then
            alert:DeleteMe()
            alert = nil
        end
    end
    local function ok_callback()
        Scene.Instance:GetSceneLogic():FlyToPos(protocol.pos_x, protocol.pos_y, protocol.scene_id, SceneObjType.Common,
            false)
    end
    local str = string.format(Language.Role.TraceReturnTxt, scene_name)
    alert = Alert.New(str, ok_callback, nil, close_callback)
    alert:Open()
end

function RoleWGCtrl:OnOneTaskDataChange(task_id, reason)

end

function RoleWGCtrl:OnTaskDataListChange()
end

-- 小鬼守护续费 脱下
function RoleWGCtrl:SendImpGuardOperaReq(opera_type, param1, param2)
    -- print_error('SendImpGuardOperaReq---------',opera_type, param1, param2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSImpGuardOperaReq)
    protocol.opera_type = opera_type
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol:EncodeAndSend()
end

function RoleWGCtrl:OnImpGuardInfo(protocol)
    local imp_guard_info = EquipWGData.Instance:GetmpGuardInfo()

    for i = 1, 2 do
        if imp_guard_info.item_wrapper[i] and imp_guard_info.item_wrapper[i].item_id <= 0 and
            protocol.item_wrapper[i].item_id > 0 then
            local item_id = protocol.item_wrapper[i].item_id
            item_id = item_id == 10101 and 10100 or item_id --10101为10100的限时版，没有配置只能写死拿10100的
            if RoleWGData.GetRolePlayerPrefsInt("guard_new" .. item_id) ~= 1 then
                RoleWGData.SetRolePlayerPrefsInt("guard_new" .. item_id, 1)
                local imp_cfg = EquipmentWGData.Instance:GetGuardCfgByItemID(item_id)
                if imp_cfg then
                    local info = {}
                    local item_cfg = ItemWGData.Instance:GetItemConfig(imp_cfg.item_id)
                    if item_cfg then
                        info.name = item_cfg.name
                    end
                    info.appe_type = ROLE_APPE_TYPE.IMP
                    info.appe_image_id = imp_cfg.item_id
                    info.path = ResPath.GetGuardModel
                    info.position = imp_cfg.obtain_position
                    info.rotation = imp_cfg.obtain_rotation
                    info.scale = imp_cfg.obtain_scale
                    info.cfg = imp_cfg
                    AppearanceWGCtrl.Instance:AddAndOpenNewAppeInfoList(info)
                end
            end
        end
    end


    EquipWGData.Instance:SetImpGuardInfo(protocol) -- 设置小鬼数据

    if self.impguard_show == false then
        self:FlushXiaoGuiInvateTip()
    end
    if not self.equip_flag then
        FunctionGuide.Instance:ReSetXiaoGuiFlag()
        self.equip_flag = true
        local data_list = ItemWGData.Instance:GetBagItemDataList()
        for k, v in pairs(data_list) do
            FunctionGuide.Instance:OnItemDataChange(v.item_id, v.index, GameEnum.DATALIST_CHANGE_REASON_CHECK
            , PUT_REASON_TYPE.PUT_REASON_ChECK_TIP)
        end
    end

    RemindManager.Instance:Fire(RemindName.XiaoGui_BecomeStronger)
end

function RoleWGCtrl:FlushXiaoGuiInvateTip()
    self.impguard_show = true
end

function RoleWGCtrl:FlushXiaoGuiInvateTipByType(imp_type)
    local is_overdue, xiaogui_data = ItemWGData.Instance:GetGuoQiXiaoGui(imp_type)
    if is_overdue then
        if xiaogui_data then
            -- self.role_xiaogui_view:SetData(xiaogui_data, imp_type)

            local callback = function()
                -- self.role_xiaogui_view:SetData(xiaogui_data, imp_type)
                MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAOGUI_XUFEI_1 + imp_type - 1, 0)
            end
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAOGUI_XUFEI_1 + imp_type - 1, 1, callback)
        else
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAOGUI_XUFEI_1 + imp_type - 1, 0)
        end
    else
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAOGUI_XUFEI_1 + imp_type - 1, 0)
    end
end

function RoleWGCtrl:OpenEquipAttr()
    if self.equip_attr_desc:IsOpen() then
        self.equip_attr_desc:Flush()
    else
        self.equip_attr_desc:Open()
    end
end

function RoleWGCtrl:FlushEquipAttr()
    if self.equip_attr_desc:IsOpen() then
        self.equip_attr_desc:Flush()
    end
end

function RoleWGCtrl:CloseEquipAttr()
    if self.equip_attr_desc:IsOpen() then
        self.equip_attr_desc:Close()
    end
end

function RoleWGCtrl:SetEquipTextData(index, equip_body_seq)
    self.equip_attr_desc:SetTextData(index, equip_body_seq)
end

function RoleWGCtrl:OnSCRoleDayFirstLogin(protocol)
    self.role_data:SetDayFirstLoginFlag(1 == protocol.day_first_login_flag)
end

function RoleWGCtrl:OpenView()
    if self.role_view then
        self.role_view:Open()
        --self:SendRoleTelentOperate(ROLE_TALENT_OPERATE_TYPE.ROLE_TALENT_OPERATE_TYPE_INFO)
    end
end

function RoleWGCtrl:OpenRoleTitleView(title_id)
    -- if self.role_branch_view then
    --     if title_id then
    --         -- self.role_title_view:SetJumpParam(tonumber(title_id))
    --     end
    --     -- self.role_title_view:Open()
    -- end

    FunOpen.Instance:OpenViewByName(GuideModuleName.RoleBranchView, TabIndex.cheng_hao, { title_id = title_id })
end

function RoleWGCtrl:FlushTitleView()
    if self.role_branch_view:IsOpen() then
        self.role_branch_view:Flush(TabIndex.cheng_hao)
    end
end

function RoleWGCtrl:PlayTitleUpLevelEff()
    if self.role_branch_view:IsOpen() then
        self.role_branch_view:Flush(TabIndex.cheng_hao, "play_uplevel_effect")
    end
end

-- -- 刷新界面
-- function RoleWGCtrl:FlushBackGroundView()
--     if self.role_branch_view:IsOpen() then
--         self.role_branch_view:Flush(TabIndex.qi_jing_bei_jing)
--     end
-- end

function RoleWGCtrl:ShowActionSucessEffect(effect_type)
    if self.role_branch_view:IsOpen() then
        self.role_branch_view:ShowActionSucessEffect(effect_type)
    end
end

-- 快速使用打开界面
function RoleWGCtrl:OpenBackViewByUse(background_id)
    FunOpen.Instance:OpenViewByName(GuideModuleName.RoleBranchView, TabIndex.sky_curtain, { background_id = background_id })
    -- self.view:SetJumpParam(background_id)
    -- if self.view:IsOpen() then
    --     self.view:FlushJump()
    -- end
    -- -- 直接调打开
    -- self.view:Open()
end

--当前使用的头像信息
function RoleWGCtrl:OnSCSendAttrHead(protocol)
    local old_use_head = self.role_data:GetCurUseHead()
    self.role_data:SetHeadIconInfo(protocol)
    local new_use_head = self.role_data:GetCurUseHead()
    if self.change_head_view:IsOpen() then
        self.change_head_view:Flush()
    end

    if self.head_protocol_tips:IsOpen() then
        self.head_protocol_tips:Flush()
    end

    if old_use_head ~= new_use_head then
        local role_id = self.role_data:InCrossGetOriginUid()
        AvatarManager.Instance:SetAvatarKey(role_id, protocol.avatar_key_big, protocol.avatar_key_small)
        GlobalEventSystem:Fire(OtherEventType.CHANGE_HEAD_ICON)
    end

    RemindManager.Instance:Fire(RemindName.Role_Head)
end

--使用系统头像
function RoleWGCtrl:SendUseAttrHead(index)
    -- print_error("【----使用系统头像-----】：", index)
    local protocol = ProtocolPool.Instance:GetProtocol(CSUseAttrHead)
    protocol.index = index or 0
    protocol:EncodeAndSend()
end

-- 修改自定义头像
function RoleWGCtrl.SendSetAvatarTimeStamp(avatar_key_big, avatar_key_small)
    -- print_error("【-----修改自定义头像----】：", avatar_key_big, avatar_key_small)
    local protocol = ProtocolPool.Instance:GetProtocol(CSSetAvatarTimeStamp)
    protocol.avatar_key_big = avatar_key_big or 0
    protocol.avatar_key_small = avatar_key_small or 0
    protocol:EncodeAndSend()
end

-- 认同自定义头像协议
function RoleWGCtrl:SendHeadAgreeProtocolReq(op_type)
    -- print_error("【-----认同自定义头像协议----】：")
    local protocol = ProtocolPool.Instance:GetProtocol(CSHeadAgreeProtocolReq)
    protocol.op_type = op_type or -1
    protocol:EncodeAndSend()
end

function RoleWGCtrl:OpenHeadProtocolTips(call_back)
    self.head_protocol_tips:SetDataAndOpen(call_back)
end

--获取当前银票
function RoleWGCtrl:GetCurSilver()
    return self.money_info.silver_ticket or 0
end

--获取当前声望
function RoleWGCtrl:GetCurShengWhang()
    return self.money_info.shengwang or 0
end

-- 变身血条下发
function RoleWGCtrl:OnSCEffectBloodChange(protocol)
    -- print_error('OnSCEffectBloodChange-------', protocol)

    local scene_obj = Scene.Instance:GetObj(protocol.obj_id)
    if nil ~= scene_obj and scene_obj:IsRole() then
        scene_obj:SetAttr("bianshen_hp", protocol.left_hp)
        scene_obj:SetAttr("bianshen_max_hp", protocol.max_hp)
        if protocol.real_hurt > 0 then
            scene_obj:OnFightSpecialFloat(protocol.real_hurt)
        else
            -- 护盾掉血也要飘字
            if scene_obj:IsMainRole() then
                scene_obj:DoBeHitFloatingText(nil, protocol.left_hp, protocol.real_hurt, protocol.fighttype,
                    FIGHT_TEXT_TYPE.NORMAL, protocol.wuxing_type)
            else
                if protocol.deliverer_id > 0 then
                    local deliverer = Scene.Instance:GetObj(protocol.deliverer_id)
                    if deliverer ~= nil and deliverer:IsMainRole() then
                        scene_obj:DoBeHitFloatingText(Scene.Instance:GetMainRole(), protocol.left_hp, protocol.real_hurt,
                            protocol.fighttype, FIGHT_TEXT_TYPE.NORMAL, protocol.wuxing_type)
                    end
                end
            end
        end

        if protocol.product_id > 0 then
            -- 变身血条的buff客户端特殊处理
            local effectinfo = SCEffectInfo.New()
            effectinfo.buff_type = 0
            effectinfo.obj_id = protocol.obj_id

            effectinfo.effect_type = 0
            effectinfo.product_method = 0
            effectinfo.product_id = protocol.product_id
            effectinfo.unique_key = 10000
            effectinfo.client_effect_type = 0
            effectinfo.merge_layer = 2
            effectinfo.param_count = 1
            effectinfo.param_list = {}
            effectinfo.param_list[2] = protocol.protect_hp_per

            FightWGData.Instance:OnEffectInfo(effectinfo)

            GlobalTimerQuest:AddDelayTimer(function()
                FightWGData.Instance:OnEffectRemove(protocol.obj_id, 10000)
            end, 3)
        end
    end
end

function RoleWGCtrl:RemoveEffectInfo()

end

function RoleWGCtrl:OpenRoleTalentTips(data)
    self.role_talent_tips:SetData(data)
    if self.role_talent_tips:IsOpen() then
        self.role_talent_tips:Flush()
    else
        self.role_talent_tips:Open()
    end
end

function RoleWGCtrl:SetExpAddititonData(from_view)
    if self.exp_addititon_view then
        self.exp_addititon_view:SetOfflineNeedInfo(from_view)
    end
end

function RoleWGCtrl:CheckXiaoGuiBecomeStronger()
    local has_become_stronger = 0
    local bag_xiaogui_info = {}
    local xiaogui_info = EquipWGData.Instance:GetmpGuardInfo()

    if not xiaogui_info or IsEmptyTable(xiaogui_info.item_wrapper) then
        return has_become_stronger
    end

    --两种小鬼
    for i = 1, 2 do
        local xiaogui_type = 1
        if xiaogui_info.item_wrapper[i].item_id > 0 then
            local cur_xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(xiaogui_info.item_wrapper[i].item_id)
            xiaogui_type = cur_xiaogui_cfg.impguard_type
        else
            if i == 2 and xiaogui_info.item_wrapper[1].item_id > 0 and xiaogui_info.item_wrapper[2].item_id <= 0 then
                local cur_xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(xiaogui_info.item_wrapper[1].item_id)
                xiaogui_type = cur_xiaogui_cfg.impguard_type == 1 and 2 or 1
            elseif i == 1 and xiaogui_info.item_wrapper[2].item_id > 0 and xiaogui_info.item_wrapper[1].item_id <= 0 then
                local cur_xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(xiaogui_info.item_wrapper[2].item_id)
                xiaogui_type = cur_xiaogui_cfg.impguard_type == 1 and 2 or 1
            end
        end
        local xiaogui_list = ItemWGData.Instance:GetXiaoGuiList(xiaogui_type)

        local temp_xiaogui_info
        local temp_info
        for k, v in pairs(xiaogui_list) do
            local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)

            if xiaogui_cfg then
                if not temp_xiaogui_info then
                    temp_xiaogui_info = xiaogui_cfg
                    temp_info = v
                else
                    if temp_xiaogui_info.color < xiaogui_cfg.color then
                        temp_xiaogui_info = xiaogui_cfg
                        temp_info = v
                    end
                end
            end
        end

        if temp_xiaogui_info then
            local weae_guard_info
            local weae_guard_id
            local is_open_list = { false, false }
            --两个格子
            for j = 1, 2 do
                local data = xiaogui_info.item_wrapper[j]
                is_open_list[j] = xiaogui_info["used_imp_type_" .. j] >= 0
                if data and data.item_id and data.item_id > 0 then
                    local cfg = EquipmentWGData.GetXiaoGuiCfg(data.item_id)
                    if cfg.impguard_type == xiaogui_type then
                        weae_guard_info = cfg
                        weae_guard_id = data.item_id
                        break
                    end
                end
            end
            --是否穿戴着显示小鬼
            local has_xianshi = (weae_guard_id ~= nil and weae_guard_id == 10101 and temp_info.item_id == 10100)

            if (is_open_list[i] and not weae_guard_id) or
                (weae_guard_info and temp_xiaogui_info.color > weae_guard_info.color) or has_xianshi then
                has_become_stronger = 1
                bag_xiaogui_info = temp_info
                break
            end
        end
    end

    if has_become_stronger == 1 and not IsEmptyTable(bag_xiaogui_info) then
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAOGUI_SHOUHU, has_become_stronger, function()
            ViewManager.Instance:Open(GuideModuleName.Bag)
            TipWGCtrl.Instance:OpenItem(bag_xiaogui_info, ItemTip.FROM_BAG)
        end)
    else
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIAOGUI_SHOUHU, has_become_stronger)
    end

    return has_become_stronger
end

function RoleWGCtrl:SetJumpAlertCheck(scene_id, callback, ignore_common, need_operate)
    if scene_id == nil then
        print_error("invaild jump scene_id", scene_id)
        return
    end
    --巡游中直接拦截掉
    if MarryWGData.Instance:GetOwnIsXunyou() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInXunYou)
        return
    end

    -- 护送中不可操作
    if YunbiaoWGData.Instance:GetIsHuShong() then
        TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
        return
    end

    if TaskWGCtrl.Instance:IsFly() then
        return
    end

    if (Scene.Instance:GetSceneId() == scene_id) or
        (ignore_common and Scene.Instance:GetSceneType() == SceneType.Common) then
        TaskWGCtrl.Instance:OnOperateFlyShoeParam()
        callback()
        return
    end

    local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
    if scene_cfg.scene_type == SceneType.Common and ignore_common then
        scene_id = MapWGData.WORLDCFG[1]
    end

    local func = function()
        if need_operate then
            TaskWGCtrl.Instance:OnOperateFlyShoeParam()
        end

        if not self.enter_scene_callback[scene_id] then
            self.enter_scene_callback[scene_id] = {}
        end
        table.insert(self.enter_scene_callback[scene_id], callback)
    end

    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()

    if fb_scene_cfg.need_show_jump_alert == 1 then
        MainuiWGCtrl.Instance:OnClickLevelFB(func)
    elseif fb_scene_cfg.need_show_jump_alert == 0 then
        FuBenWGCtrl.Instance:SendLeaveFB()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OutFb)
    end
end

--激活装备星级加成
function RoleWGCtrl:SendActiceEquipStarLevel(star_active_level)
    local protocol = ProtocolPool.Instance:GetProtocol(CSEquipUpStarNowActiveLevel)
    protocol.star_active_level = star_active_level
    protocol:EncodeAndSend()
end

function RoleWGCtrl:SetAttrMonitorValue(value)
    self.is_open_attr_monitor = value
end

function RoleWGCtrl:GetAttrMonitor()
    return self.is_open_attr_monitor
end

function RoleWGCtrl:SetMainBuffMonitorValue(value)
    self.is_open_main_buff_monitor = value
end

function RoleWGCtrl:GetMainBuffMonitor()
    return self.is_open_main_buff_monitor
end

function RoleWGCtrl:AddSuitEquipAdditionCoin(add_coin)
    local main_role = Scene.Instance:GetMainRole()
    if nil == main_role then
        return
    end

    if not self.suit_equip_add_coin_delay then
        self.suit_equip_add_coin_delay
        = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateSuitEquipCoinDelay, self),
            COMMON_CONSTS.SUIT_EQUIP_ADD_COIN_FLOATTEXT_TIME)
    end
    if main_role:GetIsInSit() then
        self.suit_equip_coin_in_sit = self.suit_equip_coin_in_sit + add_coin
    else
        self.suit_equip_coin = self.suit_equip_coin + add_coin
    end
end

function RoleWGCtrl:UpdateSuitEquipCoinDelay()
    if self.suit_equip_coin and self.suit_equip_coin > 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Bag.GetSuitEquipAddCoin, self.suit_equip_coin))
        self.suit_equip_coin = 0
    end

    if self.suit_equip_coin and self.suit_equip_coin > 0 or self.suit_equip_coin_in_sit and self.suit_equip_coin_in_sit > 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Bag.GetSuitEquipAddCoin,
            self.suit_equip_coin + self.suit_equip_coin_in_sit))
    end
    self.suit_equip_coin = 0
    self.suit_equip_coin_in_sit = 0
end

function RoleWGCtrl:RemoveSuitEquipCoinDelay(clear_sit_coin)
    if clear_sit_coin then
        if self.suit_equip_coin_in_sit and self.suit_equip_coin_in_sit > 0 then
            if self.suit_equip_add_coin_delay then
                GlobalTimerQuest:CancelQuest(self.suit_equip_add_coin_delay)
                self.suit_equip_add_coin_delay = nil
            end
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Bag.GetSuitEquipAddCoin, self
                .suit_equip_coin_in_sit))
        end
        self.suit_equip_coin_in_sit = 0
    else
        if self.suit_equip_coin and self.suit_equip_coin > 0 then
            if self.suit_equip_add_coin_delay then
                GlobalTimerQuest:CancelQuest(self.suit_equip_add_coin_delay)
                self.suit_equip_add_coin_delay = nil
            end
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Bag.GetSuitEquipAddCoin, self.suit_equip_coin))
        end
        self.suit_equip_coin = 0
    end
end

-- 物品变化
function RoleWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if change_reason ~= GameEnum.DATALIST_CHANGE_REASON_REMOVE then
        if self.role_data:IsHeadActStuff(change_item_id) then
            self.role_data:SortHeadCfgList()
            if self.change_head_view:IsOpen() then
                self.change_head_view:Flush()
            end
            RemindManager.Instance:Fire(RemindName.Role_Head)
        end
    end

    if self.skill_view and self.skill_view:IsOpen() then
        self.skill_view:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    end
    
    local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
    if item_cfg and (item_cfg.use_type == Item_Use_Type.Title or item_cfg.use_type == Item_Use_Type.BACKGROUND) then
        -- 物品数量增加
        if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
        (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
            if self.role_branch_view and self.role_branch_view:IsOpen() then
                self.role_branch_view:Flush()
            end
        end
        
        -- 物品数量减少
        if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE or
        (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num < old_num) then
            if self.role_branch_view and self.role_branch_view:IsOpen() then
                self.role_branch_view:Flush()
            end
        end
    end
end

---------------------------------------------------境界---------------------------------------------------
function RoleWGCtrl:JingJieInit()
    self.jingjie_data = JingJieWGData.New()
    self.jingmai_attr_view = JingMaiAttrView.New()
    self.jingjie_up_tip_view = JingJieUpTipView.New()

    self.role_data_change = BindTool.Bind1(self.RoleCapChange, self)
    RoleWGData.Instance:NotifyAttrChange(self.role_data_change, { "capability" })
    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成

    self.gold_callback = BindTool.Bind(self.MoneyChangeEvent, self)
    if not self.money_change_event and self.gold_callback then
        self.money_change_event = GlobalEventSystem:Bind(OtherEventType.Gold_Change_Event, self.gold_callback)
    end
end

function RoleWGCtrl:JingJieDelete()
    if nil ~= self.jingjie_data then
        self.jingjie_data:DeleteMe()
        self.jingjie_data = nil
    end

    if self.jingmai_attr_view then
        self.jingmai_attr_view:DeleteMe()
        self.jingmai_attr_view = nil
    end

    if self.jingjie_up_tip_view then
        self.jingjie_up_tip_view:DeleteMe()
        self.jingjie_up_tip_view = nil
    end

    if RoleWGData.Instance and self.role_data_change then
        RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
        self.role_data_change = nil
    end

    GlobalEventSystem:UnBind(self.money_change_event)
    self.money_change_event = nil
end

function RoleWGCtrl:OpenJingMaiAttrView()
    if self.jingmai_attr_view and not self.jingmai_attr_view:IsOpen() then
        self.jingmai_attr_view:Open()
    end
end

function RoleWGCtrl:MoneyChangeEvent(protocol)
    RemindManager.Instance:Fire(RemindName.JingMai)
end

function RoleWGCtrl:JingJiewRegisterAllProtocols()
    self:RegisterProtocol(SCRoleJingJie, "OnJingJieInfo")
    self:RegisterProtocol(CSPromoteJingJie)
    self:RegisterProtocol(CSRoleJingMaiReq)
    self:RegisterProtocol(SCRoleJingMai, "OnJingmaiInfo")
end

function RoleWGCtrl:OnJingJieInfo(protocol)
    self.jingjie_data:SetJingJieInfo(protocol)
    local main_role = Scene.Instance:GetMainRole()
    if main_role then
        main_role:SetAttr("jingjie_level", protocol.jingjie_level)
    end

    ViewManager.Instance:FlushView(GuideModuleName.RoleView, TabIndex.role_intro)
    RemindManager.Instance:Fire(RemindName.JingMai)
    ViewManager.Instance:FlushView(GuideModuleName.RoleView, TabIndex.jingjie, "all",
        { upgrade_succeed = protocol.upgrade_succeed })
end

function RoleWGCtrl:OnJingmaiInfo(protocol)
    local is_jump_meridians = false
    if protocol then
        --消耗品提示.
        local mer_data = self.jingjie_data:GetMeridiansInfo()
        if mer_data then
            if protocol.longhun_exp > mer_data.longhun_exp then
                local add_value = protocol.longhun_exp - mer_data.longhun_exp
                local other_cfg = JingJieWGData.Instance:GetOtherCfg()
                if other_cfg then
                    local cfg = ItemWGData.Instance:GetItemConfig(other_cfg.show_item)
                    if not IsEmptyTable(cfg) then
                        TipWGCtrl.Instance:ShowSystemMsg(string.format(
                            Language.JingMai.AddItem, ToColorStr(Language.JingMai.JingMaiName, GET_TIP_ITEM_COLOR[cfg.color]), add_value))
                    end
                end
            end
        end

        if protocol.opera_type == JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_BREAK_UP then
            self.jingjie_data:SyncCondensateInfo(protocol)
            local curr_meridians_data = JingJieWGData.Instance:GetMeridiansInfo()
            if curr_meridians_data then
                local meridians_attr_cfg = JingJieWGData.Instance:GetMeridiansAttrById(curr_meridians_data.whole_body_id,
                    curr_meridians_data.meridians_id)
                if meridians_attr_cfg == nil then
                    return
                end

                local data = {
                    name = meridians_attr_cfg.name,
                    desc = meridians_attr_cfg.skill_describe,
                    res_fun = ResPath.GetSkillIconById,
                    icon = meridians_attr_cfg.icon,
                }
                if protocol.is_success == 1 then
                    TipWGCtrl.Instance:ShowGetNewSkillView2(data)
                end
            end
            is_jump_meridians = true
            -- 领取上一个技能，并刷新下一个经脉
            self.jingjie_data:SyncMeridiansInfo(protocol)
        else
            self.jingjie_data:SyncMeridiansInfo(protocol)
        end
    end

    -- 刷新经脉界面
    ViewManager.Instance:FlushView(GuideModuleName.RoleView, TabIndex.jingmai, "all", {
        upgrade_succeed = protocol.is_success,
        opera_type = protocol.opera_type,
        is_jump_meridians = is_jump_meridians,
    })
    RemindManager.Instance:Fire(RemindName.JingMai)
end

function RoleWGCtrl:RoleCapChange()
    RemindManager.Instance:Fire(RemindName.JingJie)
end

function RoleWGCtrl:MainuiOpenCreateCallBack()
    RemindManager.Instance:Fire(RemindName.JingJie)
    RemindManager.Instance:Fire(RemindName.JingMai)
end

-- 请求升级境界
function RoleWGCtrl:SendJingJieUp(opera_type, param)
    local protocol = ProtocolPool.Instance:GetProtocol(CSPromoteJingJie)
    protocol.opera_type = opera_type or 0
    protocol.is_auto_buy = param or 0
    protocol:EncodeAndSend()
end

-- 请求经脉操作
function RoleWGCtrl:SendJingMaiOperate(opera_type, param)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRoleJingMaiReq)
    protocol.opera_type = opera_type or 0
    protocol.param1 = param or 0
    protocol:EncodeAndSend()
end

-- 请求经脉信息
function RoleWGCtrl:ReqJingMaiInfo(param)
    self:SendJingMaiOperate(JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_SEND_INFO, param)
end

-- 请求经脉升级
function RoleWGCtrl:ReqJingMaiUpgrade(param)
    self:SendJingMaiOperate(JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_UPGRADE, param)
end

-- 请求经脉凝气
function RoleWGCtrl:ReqJingMaiCondensate(param)
    self:SendJingMaiOperate(JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_CONDENSATE, param)
end

-- 请求经脉通脉完成领取技能
function RoleWGCtrl:ReqJingMaiGetSkill(param)
    self:SendJingMaiOperate(JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_GET_SKILL, param)
end

-- 请求经脉突破
function RoleWGCtrl:ReqJingMaiBreakUp(param)
    self:SendJingMaiOperate(JINGMAI_OPERA_TYPE.JINGMAI_OPERA_TYPE_BREAK_UP, param)
end

function RoleWGCtrl:OpenJingjieUpTipView()
    if self.jingjie_up_tip_view then
        self.jingjie_up_tip_view:Open()
    end
end