ActGobalXunBaoReWardTip = ActGobalXunBaoReWardTip or BaseClass(SafeBaseView)

function ActGobalXunBaoReWardTip:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/act_subview_ui_prefab", "layout_act_gobal_xunbao_tips")
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
end

function ActGobalXunBaoReWardTip:__delete()

end

function ActGobalXunBaoReWardTip:ReleaseCallBack()
	if self.list_reward then
		self.list_reward:DeleteMe()
		self.list_reward = nil
	end
end

function ActGobalXunBaoReWardTip:LoadCallBack()
	self.list_reward = AsyncListView.New(YaoQianShuRewardRender,self.node_list.reward_list)
	self.node_list.title_view_name.text.text = Language.OpenServer.RewardTitle
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
	self:FlushList()
end

function ActGobalXunBaoReWardTip:FlushList()
	local gobal_xunbao_cfg = ServerActivityWGData.Instance:GetGobalXunbao()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local times_cfg = rand_config.nationa_celebration_times
	local nationa_celebration_times = ServerActivityWGData.Instance:GetRandActivityConfig(times_cfg, ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)
	self.list_reward:SetDataList(nationa_celebration_times,0)
	self.node_list.txt_down3.text.text = (string.format(Language.OpenServer.GobalXunbaoTurninCount, gobal_xunbao_cfg.commit_times))
end

--------------------------------------------
--YaoQianShuRewardRender
--------------------------------------------

YaoQianShuRewardRender = YaoQianShuRewardRender or BaseClass(BaseRender)
function YaoQianShuRewardRender:__init()
end

function YaoQianShuRewardRender:__delete()
	if self.reward_cell then
		self.reward_cell:DeleteMe()
		self.reward_cell = nil
	end
end

function YaoQianShuRewardRender:LoadCallBack()
	self.reward_cell = ItemCell.New(self.node_list.ph_cell_1)
end

function YaoQianShuRewardRender:OnFlush()
	if nil == self.data then
		return
	end
	self.reward_cell:SetData(self.data.reward_item)
	self.node_list.text_times.text.text = (self.data.commit_times)
end

function YaoQianShuRewardRender:OnSelectChange(is_select)
	self.node_list.high_light:SetActive(is_select)
end