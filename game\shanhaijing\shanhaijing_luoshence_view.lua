ShanHaiJingLSCView = ShanHaiJingLSCView or BaseClass(SafeBaseView)

function ShanHaiJingLSCView:__init()
    self.view_style = ViewStyle.Full
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/shj_ui_prefab", "layout_luoshence")
end

function ShanHaiJingLSCView:LoadCallBack()
    self.lsc_select_type = -1
    self.lsc_select_drawings_id = -1
    self.lsc_select_cjip_slot = - 1
    self.is_load_complete = true
    self.luoshence_bar_cell_list = {}

    if not self.luoshence_cost_item then
        self.luoshence_cost_item = ItemCell.New(self.node_list.luoshence_cost_item)
    end

    -- if not self.luoshence_attr_list then
    --     self.luoshence_attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.luoshence_attr_list)
    -- end

    if not self.luoshence_attr_list then
        self.luoshence_attr_list = {}

        for i = 1, 8 do
            local attr_render = CommonAddAttrRender.New(self.node_list["luoshence_attr_item" .. i])
            self.luoshence_attr_list[i] = attr_render
            self.luoshence_attr_list[i]:SetIndex(i)
        end
    end

    self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.cell_parent)

    if nil == self.lsc_list_view_cell then
		self.lsc_list_view_cell = AsyncListView.New(LuoSHenCeBarItemCellRender, self.node_list.lsc_cell_list)
		self.lsc_list_view_cell:SetSelectCallBack(BindTool.Bind1(self.OnClickLSCCell, self))
        self.lsc_list_view_cell:SetStartZeroIndex(true)
	end

    if not self.luoshence_cell_list then
        self.luoshence_cell_list = {}
        for i = 0, 7 do
            self.luoshence_cell_list[i] = LuoShenCeCellRender.New(self.node_list["luoshence_cell" .. i])
            self.luoshence_cell_list[i]:SetIndex(i)
            self.luoshence_cell_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickLuoShenCeCell, self))
        end
        self:SetParentScrollRect(self.node_list.img_scroll.scroll_rect)
    end

    self.node_list.title_name.text.text = Language.LuoShenCe.title_name

    XUI.AddClickEventListener(self.node_list.luoshence_suit, BindTool.Bind(self.OnClickLuoShenCeSuit, self))
	XUI.AddClickEventListener(self.node_list.luoshence_tip, BindTool.Bind(self.OnClickLuoShenCeTip, self))
    XUI.AddClickEventListener(self.node_list.btn_luoshence_active, BindTool.Bind(self.OnClickLuoShenCeActive, self))
	XUI.AddClickEventListener(self.node_list.btn_luoshence_uplevel, BindTool.Bind(self.OnClickLuoShenCeUpLevel, self))
    XUI.AddClickEventListener(self.node_list.btn_close_windows, BindTool.Bind1(self.Close, self))
end

function ShanHaiJingLSCView:ReleaseCallBack()
    self.lsc_select_type = nil
    self.lsc_select_drawings_id = nil
    self.lsc_select_cjip_slot = nil
    self:ClearLuoShenCeTween()

    if self.luoshence_cost_item then
        self.luoshence_cost_item:DeleteMe()
        self.luoshence_cost_item = nil
    end

    -- if self.luoshence_attr_list then
    --     self.luoshence_attr_list:DeleteMe()
    --     self.luoshence_attr_list = nil
    -- end

    if self.luoshence_attr_list then
        for i = 1, 8 do
            self.luoshence_attr_list[i]:DeleteMe()
            self.luoshence_attr_list[i] = nil
        end

        self.luoshence_attr_list = nil
    end

    if self.luoshence_cell_list then
        for k, v in pairs(self.luoshence_cell_list) do
            v:DeleteMe()
        end

        self.luoshence_cell_list = nil
    end
    
    if self.luoshence_bar_cell_list then
        for k, v in pairs(self.luoshence_bar_cell_list) do
            for i, u in pairs(v) do
                u:DeleteMe()
            end
        end

        self.luoshence_bar_cell_list = nil
    end

    if self.nested_scroll_rect then
        self.nested_scroll_rect:DeleteMe()
        self.nested_scroll_rect = nil
    end

    if nil ~= self.lsc_list_view_cell then
        self.lsc_list_view_cell:DeleteMe()
        self.lsc_list_view_cell = nil
    end

    self.is_load_complete = false
    self.parent_scroll_rect = nil
end

function ShanHaiJingLSCView:SetParentScrollRect(scroll_rect)
    self.parent_scroll_rect = scroll_rect

    if self.is_load_complete then
        self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
    end
end

function ShanHaiJingLSCView:GetLuoShenCeSelectCellIndex()
    local select_cell_id = self.lsc_select_drawings_id < 0 and 0 or self.lsc_select_drawings_id
    local cell_remind = ShanHaiJingLSCWGData.Instance:GetLuoShenCeTypeCellRemind(self.lsc_select_type, select_cell_id)
    if cell_remind then
        return select_cell_id
    end

    local cfg = ShanHaiJingLSCWGData.Instance:GetLuoShenCeCardGroupTypeCfg(self.lsc_select_type)
    if not IsEmptyTable(cfg) then
        for k, v in pairs(cfg) do
            local type_remind = ShanHaiJingLSCWGData.Instance:GetLuoShenCeTypeCellRemind(self.lsc_select_type, v.drawings_id)
            if type_remind then
                return v.drawings_id
            end
        end
    end

    return select_cell_id
end

function ShanHaiJingLSCView:OnClickLSCCell(item)
	if nil == item or nil == item.data then
		return 
	end

    local data = item.data
    if self.lsc_select_type == data.type and self.lsc_select_drawings_id == data.drawings_id then
        return
    end

    self.lsc_select_type = data.type
    self.lsc_select_drawings_id = data.drawings_id
    self:FlushLSCMidSlot()

    local select_slot = self:GetLuoShenCeSelectSlot()
    self:OnClickLuoShenCeCell(self.luoshence_cell_list[select_slot], false, true)
    self:FlushLSCMidSlotSelect()
end

function ShanHaiJingLSCView:GetLuoShenCeSelectSlot()
    local select_slot = self.lsc_select_cjip_slot < 0 and 0 or self.lsc_select_cjip_slot
    local slot_remind = ShanHaiJingLSCWGData.Instance:GetLuoShenCeCellSlotRemind(self.lsc_select_type, self.lsc_select_drawings_id, select_slot)

    if slot_remind then
        return select_slot
    end

    local slot_cfg = ShanHaiJingLSCWGData.Instance:GetLuoShenCeChipCellListCfg(self.lsc_select_type, self.lsc_select_drawings_id)
    for k, v in pairs(slot_cfg) do
        if ShanHaiJingLSCWGData.Instance:GetLuoShenCeCellSlotRemind(self.lsc_select_type, self.lsc_select_drawings_id, v.chip_slot) then
            return v.chip_slot
        end
    end

    return select_slot
end

function ShanHaiJingLSCView:OnClickLuoShenCeCell(item, need_tips, force_flush)
    if nil == item or nil == item.data then
		return
	end

    if self.lsc_select_cjip_slot == item.index then
        if need_tips then
            TipWGCtrl.Instance:OpenItem({item_id = item.data.chip_id})
        end

        if not force_flush then
            return
        end
    end

    self.lsc_select_cjip_slot = item.index
    if self.lsc_select_cjip_slot > 4 then
        local from_value = self.node_list.img_scroll.scroll_rect.horizontalNormalizedPosition
        UITween.DoScrollRectHorizontalPosition(self.node_list.img_scroll, from_value, 1, 0.5, nil, nil)
    else
        local from_value = self.node_list.img_scroll.scroll_rect.horizontalNormalizedPosition
        UITween.DoScrollRectHorizontalPosition(self.node_list.img_scroll, from_value, 0, 0.5, nil, nil)
    end
    
    self:FlushLSCMidSlotSelect()
    self:FlushLSCRightPanel()
end

function ShanHaiJingLSCView:ShowIndexCallBack()
    self:ClearLuoShenCeTween()
    self:ResetTweenState()
    self:PlayLuoShenCeTween()
end

function ShanHaiJingLSCView:OnFlush()
    local cap = ShanHaiJingLSCWGData.Instance:GetLuoShenCeCap()
    self.node_list.luoshence_cap_value.text.text = cap
    local data_list = ShanHaiJingLSCWGData.Instance:CheckLscImgIsOpen(0)
    if IsEmptyTable(data_list) then
        return
    end

    self.lsc_list_view_cell:SetDataList(data_list)
    local need_jump_list_red = true
    if self.lsc_select_type >= 0 and self.lsc_select_drawings_id >= 0 then
        local select_remind = ShanHaiJingLSCWGData.Instance:GetLuoShenCeTypeCellRemind(self.lsc_select_type, self.lsc_select_drawings_id)
        if select_remind then
            local select_slot = self:GetLuoShenCeSelectSlot()
            if self.luoshence_cell_list[select_slot] then
                self:OnClickLuoShenCeCell(self.luoshence_cell_list[select_slot], false, true)
            end

            need_jump_list_red = false
        end
    end

    if need_jump_list_red then
        local jump_index = self.lsc_list_view_cell:GetSelectIndex() or 0
        for i = 0, #data_list do
            local info = data_list[i]
            local remind = ShanHaiJingLSCWGData.Instance:GetLuoShenCeTypeCellRemind(info.type, info.drawings_id)
            if remind then
                jump_index = i
                break
            end
        end

        self.lsc_list_view_cell:JumpToIndex(jump_index)
    end

    self:FlushLSCRightPanel()
    self:FlushLSCMidSlot()
    self:FlushLSCMidSlotSelect()
end

function ShanHaiJingLSCView:FlushLSCMidSlot()
    if self.lsc_select_type < 0 or self.lsc_select_drawings_id < 0 then
        return
    end

    local data_list = ShanHaiJingLSCWGData.Instance:GetLuoShenCeChipCellListCfg(self.lsc_select_type, self.lsc_select_drawings_id)
    if not IsEmptyTable(data_list) then
        for i = 0, 7 do
            self.luoshence_cell_list[i]:SetData(data_list[i])
        end
    end
end

function ShanHaiJingLSCView:FlushLSCMidSlotSelect()
    if self.lsc_select_cjip_slot < 0 then
        return
    end
    
    for i = 0, 7 do
        self.node_list["luoshence_cell_select" .. (i + 1)]:CustomSetActive(i == self.lsc_select_cjip_slot)
        local level = ShanHaiJingLSCWGData.Instance:GetLuoShenCeCellData(self.lsc_select_type, self.lsc_select_drawings_id, i)
        XUI.SetGraphicGrey(self.node_list["luoshence_cell_bg" .. i], level < 0)
    end
end

function ShanHaiJingLSCView:FlushLSCRightPanel()
    local cfg = ShanHaiJingLSCWGData.Instance:GetLuoShenCeChipCellCfg(self.lsc_select_type, self.lsc_select_drawings_id, self.lsc_select_cjip_slot)
    if not IsEmptyTable(cfg) then
        local info = ShanHaiJingLSCWGData.Instance:GetLuoShenCeCellData(self.lsc_select_type, self.lsc_select_drawings_id, self.lsc_select_cjip_slot)
        self.node_list.luoshence_right_title.text.text = cfg.name
        local next_level_cfg = ShanHaiJingLSCWGData.Instance:GetLuoShenCeUpGradeNum(info + 1)
        local is_max_level = IsEmptyTable(next_level_cfg)
        local active = info >= 0
        local attr_list = self:GetLuoShenCeCellCardAttrList(cfg, info, is_max_level, active)

        -- self.luoshence_attr_list:SetDataList(attr_list)

        local need_show_effect = false
        if nil ~= self.lsc_select_type_cache and nil ~= self.lsc_select_drawings_id_cache and nil ~= self.lsc_select_cjip_slot_cache and nil ~= self.lsc_evel_cache then
            if (self.lsc_select_type_cache == self.lsc_select_type) and (self.lsc_select_drawings_id_cache == self.lsc_select_drawings_id) and (self.lsc_select_cjip_slot_cache == self.lsc_select_cjip_slot)
             and (info - self.lsc_evel_cache == 1) then
                need_show_effect = true
                -- self:OnLSCAttrValueChange()
            end
        end

        self.lsc_select_type_cache = self.lsc_select_type
        self.lsc_select_drawings_id_cache = self.lsc_select_drawings_id
        self.lsc_select_cjip_slot_cache = self.lsc_select_cjip_slot
        self.lsc_evel_cache = info

        for i = 1, #self.luoshence_attr_list do
            self.luoshence_attr_list[i]:SetData(attr_list[i])

            if need_show_effect then
                self.luoshence_attr_list[i]:PlayAttrValueUpEffect()
            end
        end

        self.node_list.stars_list:CustomSetActive(active)
        self.node_list.btn_luoshence_active:CustomSetActive(not active)
        self.node_list.btn_luoshence_uplevel:CustomSetActive(active and not is_max_level)
        self.node_list.luoshence_max_star:CustomSetActive(is_max_level)
        self.node_list.luoshence_cost_item:CustomSetActive(not is_max_level)

        local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.chip_id)
        if item_cfg then
            local bundle, asset = ResPath.GetNoPackPNG("a2_qqjyt_pzk" .. item_cfg.color)
            self.node_list.select_box_img.image:LoadSprite(bundle, asset)
        end

        local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.chip_id)
        local cost_item_num = ShanHaiJingLSCWGData.Instance:GetLuoShenCeActiveCardNeedNum(info)
        local enough = cost_item_num <= item_num

        if not is_max_level then
            self.luoshence_cost_item:SetFlushCallBack(function ()
                local right_text = ToColorStr(item_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
                self.luoshence_cost_item:SetRightBottomColorText(right_text)
                self.luoshence_cost_item:SetRightBottomTextVisible(true)
            end)
            self.luoshence_cost_item:SetData({item_id = cfg.chip_id})
        end

        if active then
            local star_res_list = GetStarImgResByStar(info)
            local bundle, asset = "", ""
            for i = 1, GameEnum.ITEM_MAX_STAR do
                if self.node_list["lsc_star_" .. i] then
                    if i <= info then
                        bundle, asset = ResPath.GetCommonImages(star_res_list[i])
                        self.node_list["lsc_star_" .. i].image:LoadSprite(bundle, asset)
                    else
                        bundle, asset = ResPath.GetCommonImages("a2_ty_xx_d_d")
                        self.node_list["lsc_star_" .. i].image:LoadSprite(bundle, asset)
                    end
                end
            end

            if not is_max_level then
                self.node_list.btn_luoshence_uplevel_remind:CustomSetActive(enough)
            end
        else
            self.node_list.btn_luoshence_active_remind:CustomSetActive(enough)
        end
    end
end

function ShanHaiJingLSCView:GetLuoShenCeCellCardAttrList(cfg, level, is_max_level, active)
    local data_list = {}

    for i = 1, 8 do
        local attr_id = cfg["attr_id" .. i]
        local attr_value = cfg["attr_value" .. i]
        
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local data = {}
            local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
            data.attr_str = attr_str
            data.attr_value = not active and 0 or (attr_value * (level + 1))
            data.add_value = not active and attr_value or is_max_level and 0 or attr_value
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
            table.insert(data_list, data)
        end
    end

    if not IsEmptyTable(data_list) then
        table.sort(data_list, SortTools.KeyLowerSorter("attr_sort"))
    end

    return data_list
end

function ShanHaiJingLSCView:OnClickLuoShenCeSuit()
    ShanHaiJingLSCWGCtrl.Instance:SetLSCSuitAttrDataAndOpen(self.lsc_select_type, self.lsc_select_drawings_id)
end

function ShanHaiJingLSCView:OnClickLuoShenCeTip()
    RuleTip.Instance:SetContent(Language.LuoShenCe.TipContent, Language.LuoShenCe.TipTitle)
end

function ShanHaiJingLSCView:OnClickLuoShenCeActive()
    self:LuoShenCeOpera()
end

function ShanHaiJingLSCView:OnClickLuoShenCeUpLevel()
    self:LuoShenCeOpera()
end

function ShanHaiJingLSCView:LuoShenCeOpera()
    local cfg = ShanHaiJingLSCWGData.Instance:GetLuoShenCeChipCellCfg(self.lsc_select_type, self.lsc_select_drawings_id, self.lsc_select_cjip_slot)
    
    if not IsEmptyTable(cfg) then
        local info = ShanHaiJingLSCWGData.Instance:GetLuoShenCeCellData(self.lsc_select_type, self.lsc_select_drawings_id, self.lsc_select_cjip_slot)
        local next_level_cfg = ShanHaiJingLSCWGData.Instance:GetLuoShenCeUpGradeNum(info + 1)
        local is_max_level = IsEmptyTable(next_level_cfg)

        if is_max_level then
            return
        end

        local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.chip_id)
        local cost_item_num = ShanHaiJingLSCWGData.Instance:GetLuoShenCeActiveCardNeedNum(info)
        if cost_item_num <= item_num then
            ShanHaiJingLSCWGCtrl.Instance:SendCSDrawingsComposeOperate(DRAWINGS_COMPOSE_OPERATE_TYPE.USE_CHIP, self.lsc_select_type, self.lsc_select_drawings_id, self.lsc_select_cjip_slot)
        else
            TipWGCtrl.Instance:OpenItem({item_id = cfg.chip_id})
        end
    end
end

function ShanHaiJingLSCView:ClearLuoShenCeTween()
    if self.luoshence_tween then
        self.luoshence_tween:Kill()
        self.luoshence_tween = nil
    end
end

function ShanHaiJingLSCView:PlayLuoShenCeTween()
    if not self.luoshence_tween then
        local left_tween = self.node_list.tween_left.rect:DOAnchorPosX(-494, 0.5)
        local right_tween = self.node_list.tween_right.rect:DOAnchorPosX(577, 0.5)
        local bg_tween = self.node_list.tween_bg.rect:DOSizeDelta(Vector2(960, 540), 0.5)

        self.luoshence_tween = DG.Tweening.DOTween.Sequence()
        self.luoshence_tween:Join(left_tween)
        self.luoshence_tween:Join(right_tween)
        self.luoshence_tween:Join(bg_tween)
        self.luoshence_tween:OnComplete(function()
            if self.node_list then
                self.node_list.tween_root:SetActive(false)
                UITween.FakeToShow(self.node_list.fake_hide_root)
            end
        end)
    end
end

function ShanHaiJingLSCView:ResetTweenState()
    UITween.FakeHideShow(self.node_list.fake_hide_root)
    self.node_list.tween_root:SetActive(true)
    RectTransform.SetAnchoredPositionXY(self.node_list.tween_left.rect, -30, 13)
    RectTransform.SetAnchoredPositionXY(self.node_list.tween_right.rect, 110, -33)
    RectTransform.SetSizeDeltaXY(self.node_list.tween_bg.rect, 0, 540)
end

-- function ShanHaiJingLSCView:OnLSCAttrValueChange()
-- 	if self.luoshence_attr_list then
-- 		local cell_list = self.luoshence_attr_list:GetAllItems()

-- 		if not IsEmptyTable(cell_list) then
-- 			for k, v in pairs(cell_list) do
-- 				v:PlayAttrValueUpEffect(0.2)
-- 			end
-- 		end
-- 	end
-- end

-----------------------------------------LuoShenCeCellRender-----------------------------------
LuoShenCeCellRender = LuoShenCeCellRender or BaseClass(BaseRender)

function LuoShenCeCellRender:__delete()
    self.call_back = nil
end

function LuoShenCeCellRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClickClickBtn,self))
end

function LuoShenCeCellRender:OnFlush()
	if nil == self.data then
		return
	end

    local info = ShanHaiJingLSCWGData.Instance:GetLuoShenCeCellData(self.data.type, self.data.drawings_id, self.index)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.chip_id)
    local cost_item_num = ShanHaiJingLSCWGData.Instance:GetLuoShenCeActiveCardNeedNum(info)
    local enough = cost_item_num <= item_num
    local next_level_cfg = ShanHaiJingLSCWGData.Instance:GetLuoShenCeUpGradeNum(info + 1)
    local is_max_level = IsEmptyTable(next_level_cfg)

    self.node_list.slot_red:SetActive(enough and not is_max_level)
    local bundle, asset = ResPath.GetF2RawImagesPNG(self.data.bg)
    self.node_list.bg.raw_image:LoadSprite(bundle, asset)
end

function LuoShenCeCellRender:SetClickCallBack(call_back)
    self.call_back = call_back
end

function LuoShenCeCellRender:OnClickClickBtn()
    if self.call_back then
        self:call_back(self)
    end
end

-------------------------------LuoSHenCeBarItemCellRender-------------------------------------
LuoSHenCeBarItemCellRender = LuoSHenCeBarItemCellRender or BaseClass(BaseRender)

function LuoSHenCeBarItemCellRender:__init()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function LuoSHenCeBarItemCellRender:__delete()
end

function LuoSHenCeBarItemCellRender:OnFlush()
	if nil == self.data then
		return
	end
	self.node_list.normal_text.text.text = self.data.name
	self.node_list.select_text.text.text = self.data.name
    self:FlushRemind()
end

function LuoSHenCeBarItemCellRender:SetClickCallBack(call_back)
    self.call_back = call_back
end

function LuoSHenCeBarItemCellRender:OnClickItem(is_on)
	if nil == is_on then
		return
	end

	if is_on then
        if self.call_back then
            self:call_back(self)
        end
    end
end

function LuoSHenCeBarItemCellRender:FlushRemind()
	if not self.data or not self.index then
		return
	end

    local remind = ShanHaiJingLSCWGData.Instance:GetLuoShenCeTypeCellRemind(self.data.type, self.data.drawings_id)
	self.node_list.remind:SetActive(remind)
end

function LuoSHenCeBarItemCellRender:OnSelectChange(is_select)
	self.node_list.select:SetActive(is_select)
	self.node_list.normal:SetActive(not is_select)
	self.view.toggle.isOn = is_select
end