PaoKuSceneLogic = PaoKuSceneLogic or BaseClass(CommonFbLogic)
function PaoKuSceneLogic:__init()
end

function PaoKuSceneLogic:__delete()
end

function PaoKuSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	-- PaoKuWGCtrl.Instance:OpenPaoKuSkillBar()

	--local start_time = PaoKuData.Instance.paoku_time
	-- local end_time = start_time - TimeWGCtrl.Instance:GetServerTime()
	-- if end_time > 0 then
	-- 	-- UiInstanceMgr.Instance:ShowSceneNormalCountDown(end_time,2)
	-- end
end

function PaoKuSceneLogic:Out()
	CommonFbLogic.Out(self)
	
	-- PaoKuWGCtrl.Instance:ClosePaoKuSkillBar()
end

function PaoKuSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end