local empty_table = {}
local this = FairyLandEquipmentWGData
function this:InitGodBodyCfgData()
    local main_cfg = ConfigManager.Instance:GetAutoConfig("xianjie_equip_auto")
    self.god_body_map_cfg = ListToMap(main_cfg.god_body, "slot")
    self.god_book_map_cfg = ListToMap(main_cfg.god_book, "page")
    self.god_book_chip_map_cfg = ListToMap(main_cfg.god_book_chip, "item_id")
    self.god_book_to_chip_cfg = ListToMap(main_cfg.god_book_chip, "slot", "page", "part")
    self.god_book_act_attr_cfg = ListToMap(main_cfg.god_book_act_attr, "slot", "page")
    self.god_body_uplevel_map_cfg = ListToMap(main_cfg.gb_uplevel, "slot", "level")
    self.god_body_upgrade_map_cfg = ListToMap(main_cfg.gb_upgrade, "slot", "grade")
    self.god_body_skill_map_cfg = ListToMap(main_cfg.gb_grade_skill, "slot", "skill_index", "skill_level")
end

function this:InitGodBodyParam()
    self.god_body_act_num = 0
    self.page_part_remind_flag = {}
    self.page_part_act_flag = {}
    self.uplevel_right_flag = {}
    self.upgrade_right_flag = {}
    self.cache_gb_skill_attr = {}
    self.gb_upgrade_stuff_list = {}
end

function this:RegisterGodBodyRemindInBag()
    local map_lsit, item_id_list = {}, {}
    local main_cfg = ConfigManager.Instance:GetAutoConfig("xianjie_equip_auto")
    for k,v in ipairs(main_cfg.gb_upgrade) do
        if map_lsit[v.stuff_id] == nil then
            map_lsit[v.stuff_id] = true
            self.gb_upgrade_stuff_list[v.stuff_id] = true
        end
    end

    for k,v in pairs(map_lsit) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.FLE_GodBody, item_id_list)
end

-- 是否渡劫材料
function this:IsGodBodyUpgradeStuff(item_id)
    return self.gb_upgrade_stuff_list[item_id]
end

-- 激活神体需要的书页数
function this:GetActBodyNeedNum()
    if self.act_body_page_num == nil then
        self.act_body_page_num = self.god_body_other_cfg.act_body_page_num
    end

    return self.act_body_page_num or 0
end

-- 激活书页需要的碎片数
function this:GetActPageNeedNum()
    if self.act_page_chip_num == nil then
        self.act_page_chip_num = self.god_body_other_cfg.act_page_chip_num
    end

    return self.act_page_chip_num or 0
end

function this:GetGodBodyAllCfg()
    return self.god_body_map_cfg
end

function this:GetGodBodyCfg(slot)
    return self.god_body_map_cfg[slot]
end

-- from 0
function this:GetGodBodyMaxNum()
    return #self.god_body_map_cfg
end

function this:GetGodBookAllCfg()
    return self.god_book_map_cfg
end

function this:GetGodBookCfg(page)
    return self.god_book_map_cfg[page]
end

function this:GetGodBookChipCfgByItemId(item_id)
    return self.god_book_chip_map_cfg[item_id]
end

function this:IsGodBookChipItem(item_id)
    return self.god_book_chip_map_cfg[item_id] ~= nil
end

function this:GetGodBookChipCfgByData(slot, page, part)
    return ((self.god_book_to_chip_cfg[slot] or empty_table)[page] or empty_table)[part]
end

function this:GetGodBookActAttrCfg(slot, page)
    return (self.god_book_act_attr_cfg[slot] or empty_table)[page]
end

function this:GetIsGodBookChip(item_id)
    return self.god_book_chip_map_cfg[item_id] ~= nil
end

--============================================================================--
--================================【天书】=====================================--
function this:SetGodBodyAllInfo(protocol)
    local info_list = protocol.all_slot_info
    self.page_part_remind_flag = {}
    self.page_part_act_flag = {}
    for slot, info in pairs(info_list) do
        self:UpdatePartDataByGodBody(slot, info)
        self.page_part_remind_flag[slot] = info.page_part_remind_flag
        self.page_part_act_flag[slot] = info.page_part_act_flag
    end

    self:CalcGodBodyActNum()

    -- local list = self:GetGodBodyList()
    -- for slot = 0, #list do
    --     local data = list[slot]
    --     print_error(string.format("神体：%s, level:%s, grade:%s, page_num:%s", slot, data:GetLevel(), data:GetGrade(), data:GetPageGatherNum()))
    -- end
end

function this:SetGodBodySingleInfo(protocol)
    local slot = protocol.slot_index
    local info = protocol.slot_info
    self:UpdatePartDataByGodBody(slot, info)
    self.page_part_remind_flag[slot] = info.page_part_remind_flag
    self.page_part_act_flag[slot] = info.page_part_act_flag

    self:CalcGodBodyActNum()

    -- local list = self:GetGodBodyList()
    -- for slot = 0, #list do
    --     local data = list[slot]
    --     print_error(string.format("神体：%s, level:%s, grade:%s, page_num:%s", slot, data:GetLevel(), data:GetGrade(), data:GetPageGatherNum()))
    -- end
end

-- 统计神体激活数
function this:CalcGodBodyActNum()
    local gb_act_num = 0
    local list = self:GetGodBodyList()
    for k,v in pairs(list) do
        if v:GetIsAct() then
            gb_act_num = gb_act_num + 1
        end
    end

	self.god_body_act_num = gb_act_num
end

-- 神体激活数
function this:GetGodBodyActNum()
    return self.god_body_act_num
end

-- 当前最高神体转数
function this:GetCurMaxGodBody()
    local max_gb = self.god_body_act_num - 1
    max_gb = max_gb > 0 and max_gb or 0
    return max_gb
end

-- 获取书页部位 新获得提醒
function this:GetPagePartRemind(slot, page, part)
    local god_body = self:GetGodBodyData(slot)
    local page_is_act = god_body and god_body:GetPageIsAct(page)
    local part_is_act = self:GetPagePartAct(slot, page, part)
    local no_flag = ((self.page_part_remind_flag[slot] or empty_table)[page] or empty_table)[32 - part] == 0
    return not page_is_act and part_is_act and no_flag
end

-- 获取书页部位激活列表
function this:GetPageAllPartActList(slot, page)
    return (self.page_part_act_flag[slot] or empty_table)[page] or empty_table
end

-- 获取书页部位 是否激活
function this:GetPagePartAct(slot, page, part)
    return ((self.page_part_act_flag[slot] or empty_table)[page] or empty_table)[32 - part] == 1
end

-- function this:GetPageActProgress(slot, page)
--     local act_num = 0
--     local act_progress = 0
--     local total_num = self:GetActPageNeedNum()
--     for i = 0, total_num - 1 do
--         if ((self.page_part_act_flag[slot] or empty_table)[page] or empty_table)[32 - i] == 1 then
--             act_num = act_num + 1
--         end
--     end

--     act_progress = act_num / total_num * 100
--     return act_progress
-- end

function this:GetPageActProgress(slot, page)
    local act_num = self:GetPageChipGatherNum(slot, page)
    local act_progress = 0
    local total_num = self:GetActPageNeedNum()

    act_progress = act_num / total_num * 100
    return act_progress
end

function this:GetPageIsAct(slot, page)
    local god_body = self:GetGodBodyData(slot)
    local is_act = false
    if god_body then
        if god_body:GetPageIsAct(page) then
            is_act = true
        end
    end

    return is_act
end

-- 获取书页 激活状态
function this:GetPageActState(slot, page)
    local god_body = self:GetGodBodyData(slot)
    local state = GOODS_STATE_TYPE.UNACT
    if god_body then
        if god_body:GetPageIsAct(page) then
            state = GOODS_STATE_TYPE.NORMAL
        else
            if page < god_body:GetPageGatherNum() + 1 then
                state = GOODS_STATE_TYPE.READY_ACT
            end
        end
    end

    return state
end

-- 书页收集量
function this:GetPageGatherNum(slot)
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return 0
    end

    return god_body:GetPageGatherNum()
end

-- 书页碎片收集量
function this:GetPageChipGatherNum(slot, page)
    local flag = self:GetPageAllPartActList(slot, page)
    local max_part_num = self:GetActPageNeedNum()
    local num = 0
    for part = 0, max_part_num do
        if flag[32 - part] == 1 then
            num = num + 1
        end
    end

    return num
end

-- 书页激活提醒
function this:GetPageActRemind(slot, page)
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return false
    end

    local is_act = god_body:GetPageIsAct(page)
    local read_new = self:GetPageReadRemind(slot, page)
    local max_part_num = self:GetActPageNeedNum()
    local gather_chip_num = self:GetPageChipGatherNum(slot, page)
    return not is_act and not read_new and gather_chip_num >= max_part_num
end

-- 神体激活提醒
-- 新需求：最后一页书页 不用手动激活天书，直接激活神体
function this:GetGodBodyActRemind(slot)
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return false
    end

    local is_act = god_body:GetIsAct()
    local max_page_num = self:GetActBodyNeedNum()
    local page_act_remind = self:GetPageActRemind(slot, max_page_num - 1)
    return not is_act and page_act_remind
end

-- 书页是否可激活，不需要查看一下的操作
function this:GetPageActRemindWithoutNew(slot, page)
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return false
    end

    local is_act = god_body:GetPageIsAct(page)
    local max_part_num = self:GetActPageNeedNum()
    local gather_chip_num = self:GetPageChipGatherNum(slot, page)
    return not is_act and gather_chip_num >= max_part_num
end

-- 书页有新获得
function this:GetPageReadRemind(slot, page)
    local max_part = self:GetActPageNeedNum()
    for part = 0, max_part - 1 do
        if self:GetPagePartRemind(slot, page, part) then
            return true
        end
    end

    return false
end

function this:GetPageReadRemindList(slot, page)
    local max_part = self:GetActPageNeedNum()
    local get_new, is_get_new = false, false
    local get_new_list = {}
    for part = 0, max_part - 1 do
        -- 新获得提醒
        get_new = self:GetPagePartRemind(slot, page, part)
        get_new_list[part] = get_new
        if get_new then
            is_get_new = true
        end
    end

    return get_new_list, is_get_new
end

-- 一书页红点
function this:GetPageRemind(slot, page)
    local page_act_remind = false
    local max_part = self:GetActPageNeedNum()
    for part = 0, max_part - 1 do
        -- 新获得提醒
        if self:GetPagePartRemind(slot, page, part) then
            return true, page_act_remind
        end

        -- 书页提醒
        if self:GetPageActRemind(slot, page) then
            page_act_remind = true
            return true, page_act_remind
        end
    end

    return false, page_act_remind
end

-- 天书 - 神体红点
function this:GeSlotGodBookRemind(slot, total_remind)
    local page_num = self:GetActBodyNeedNum()
    local remind, page_act_remind = false, false
    for page = 0, page_num - 1 do
        remind, page_act_remind = self:GetPageRemind(slot, page)
        if remind then
            return remind, page_act_remind and page or false
        end
    end

    return remind, page_act_remind
end

-- 天书红点
function this:GetGodBookRemind()
    local gb_list = self:GetGodBodyList()
    if IsEmptyTable(gb_list) then
    	return 0
    end

    local slot = 0
    -- 激活神体提醒 加入变强
    for slot = 0, #gb_list do
        if self:GetGodBodyActRemind(slot) then
            self:ToBeStrengthenRuleView(1, MAINUI_TIP_TYPE.FLE_GODBODY_JIHUO, "open_rule",
                                {to_slot = slot, to_ui_param = self:GetActBodyNeedNum() - 1})
            return 1
        end
    end
    self:ToBeStrengthen(0, MAINUI_TIP_TYPE.FLE_GODBODY_JIHUO)

    local remind, page_act_remind
    for k, v in pairs(gb_list) do
        slot = v:GetSlotIndex()
        remind, page_act_remind = self:GeSlotGodBookRemind(slot, true)
        if remind then
            if page_act_remind then
                self:ToBeStrengthenRuleView(1, MAINUI_TIP_TYPE.FLE_GODBOOK_JIHUO, "open_rule",
                                    {to_slot = slot, to_ui_param = page_act_remind})
            end
            return 1
        end
    end
    self:ToBeStrengthen(0, MAINUI_TIP_TYPE.FLE_GODBOOK_JIHUO)

    return 0
end

-- 当前书页，前后书页的提醒 dir 前:-1， 后:1
function this:GetOtherPageRemind(slot, cur_page, dir)
    local max_page_num = self:GetActBodyNeedNum()
    if dir > 0 and cur_page >= max_page_num then
        return false
    elseif dir < 0 and cur_page <= 0 then
        return false
    end

    local start_page = cur_page + dir
    local end_page = dir > 0 and max_page_num or 0
    for page = start_page, end_page, dir do
        if self:GetPageRemind(slot, page) then
            return true
        end
    end

    return false
end

-- 获得天书总属性
function this:GetGBAllActAttr(slot, cur_page)
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return
    end

    local attr_key_list
    local attribute = AttributePool.AllocAttribute()
    local function get_attr(cfg)
        if cfg == nil then
            return
        end

        if attr_key_list == nil then
            attr_key_list = AttributeMgr.GetUsefulAttributteByClass(cfg)
        end

        for k,v in pairs(attr_key_list) do
            attribute[v] = attribute[v] + cfg[k]
        end
    end

    local max_page = self:GetActBodyNeedNum()
    local chip_max_num = self:GetActPageNeedNum()

    if cur_page and cur_page >= 0 then
        local state = self:GetPageActState(slot, cur_page)
        if state == GOODS_STATE_TYPE.NORMAL or state == GOODS_STATE_TYPE.READY_ACT then
            for part = 0, chip_max_num - 1 do
                if self:GetPagePartAct(slot, cur_page, part) then
                    local cfg = self:GetGodBookChipCfgByData(slot, cur_page, part)
                    get_attr(cfg)
                end
            end
        end

        if state == GOODS_STATE_TYPE.NORMAL then
            local cfg = self:GetGodBookActAttrCfg(slot, cur_page)
            if cfg then
                get_attr(cfg)
            end
        end
    else
        for page = 0, max_page - 1 do
            local state = self:GetPageActState(slot, page)
            if state == GOODS_STATE_TYPE.NORMAL or state == GOODS_STATE_TYPE.READY_ACT then
                for part = 0, chip_max_num - 1 do
                    if self:GetPagePartAct(slot, page, part) then
                        local cfg = self:GetGodBookChipCfgByData(slot, page, part)
                        get_attr(cfg)
                    end
                end
            end

            if state == GOODS_STATE_TYPE.NORMAL then
                local cfg = self:GetGodBookActAttrCfg(slot, page)
                if cfg then
                    get_attr(cfg)
                end
            end
        end
    end

    return attribute
end

--不传cur_page是获取神体总属性，传入cur_page是获取当前页的总属性
function this:GetGBAllActAttrList(slot, cur_page)
    local attr_list = {}
    local attribute = self:GetGBAllActAttr(slot, cur_page)
    if attribute == nil then
        return attr_list
    end

    local ready_act_cfg = {}
    local max_page = self:GetActBodyNeedNum()
    for page = 0, max_page - 1 do 
        if self:GetPageActRemind(slot, page) then
            ready_act_cfg = self:GetGodBookActAttrCfg(slot, page)
            break
        end
    end

    local attr_cfg_list
    if attr_cfg_list == nil then
        _, attr_cfg_list = AttributeMgr.GetUsefulAttributteByClass(ready_act_cfg)
    end

    for k, v in pairs(attribute) do
        if v > 0 then
            local data = {}
            data.attr_str = k
            data.attr_value = v
            data.add_value = ready_act_cfg[attr_cfg_list[k]] or 0
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
            table.insert(attr_list, data)
        end
    end

    -- 预览
    if IsEmptyTable(attr_list) then
        local cfg = self:GetGodBookChipCfgByData(slot, 0, 0)
        local attr_key_list = AttributeMgr.GetUsefulAttributteByClass(cfg)
        for k,v in pairs(attr_key_list) do
            local data = {}
            data.attr_str = k
            data.attr_value = 0
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
            table.insert(attr_list, data)
        end
    end

    table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    return attr_list
end

function this:GetGBAllActAttrCap(slot)
    local cap = 0
    local attribute = self:GetGBAllActAttr(slot)
    if attribute then
        cap = AttributeMgr.GetCapability(attribute)
    end

    return cap
end

function this:GetDuJieMinHp()
    -- local cfg = self.jj_cfg.other[1]
    -- local cfg = self:GetJingJieCfgBylevel(self.jj_level + 1)
    return GameMath.Rand(1, 10)
end

function this:GetDuJieHpList(min_hp)
    local sum = 100 - min_hp
    local avr = GameMath.Round(sum/GOD_BODY_DUJIE_NUM)
    -- local float_val = self.jj_cfg.other[1].float_val
    local float_val = 40
    local float_d_value = GameMath.Round(avr * float_val/100)
    local float_max = avr + float_d_value
    local float_min = avr - float_d_value
    local dif_var = 0
    local hp_list = {}
    for i=1,GOD_BODY_DUJIE_NUM - 1 do
        local rand = GameMath.Round(GameMath.Rand(float_min, float_max))
        dif_var = dif_var + avr - rand
        for j=1,10 do
            local left_num = GOD_BODY_DUJIE_NUM - i
            if dif_var / left_num > float_d_value then
                local t_rand = GameMath.Round(GameMath.Rand(0, dif_var))
                local t_rand_1 = rand + t_rand
                if t_rand_1 > float_max then
                    rand = float_max
                    dif_var = dif_var - float_d_value
                else
                    rand = rand + t_rand
                    dif_var = dif_var - t_rand
                end
            elseif dif_var / left_num < -float_d_value then
                local t_rand = GameMath.Round(GameMath.Rand(0, math.abs(dif_var)))
                local t_rand_1 = rand - t_rand
                if t_rand_1 < float_min then
                    rand = float_min
                    dif_var = dif_var + float_d_value
                else
                    rand = rand - t_rand
                    dif_var = dif_var + t_rand
                end
            else
                break
            end
        end

        hp_list[i] = rand
        sum = sum - rand
    end
    hp_list[GOD_BODY_DUJIE_NUM] = sum

    return hp_list
end

--击中后到下一个雷的时间
function this:GetDuJieTime(num)
    local time_list = {}
    for i=1,GOD_BODY_DUJIE_NUM do
        time_list[i] = 1
    end
    return time_list
end

--============================================================================--
--================================【神体】=====================================--
-- 获取神体升级配置
function this:GetGBUpLevelCfg(slot, level)
    return (self.god_body_uplevel_map_cfg[slot] or empty_table)[level]
end

-- 获取神体最大等级
function this:GetGBMaxLevel(slot)
    local cfg = self.god_body_uplevel_map_cfg[slot]
    if cfg then
        return cfg[#cfg].level
    end

    return 0
end

-- 获取神体渡劫配置
function this:GetGBUpGradeCfg(slot, grade)
    return (self.god_body_upgrade_map_cfg[slot] or empty_table)[grade]
end

-- 获取神体修炼特权配置
function this:GetGBUplevelRightCfg(buy_seq)
    for k,v in ipairs(self.main_cfg.gb_xiulian_right) do
        if buy_seq == v.buy_seq then
            return v
        end
    end

    return nil
end

-- 获取神体渡劫特权配置
function this:GetGBUpgradeRightCfg(buy_seq)
    for k,v in ipairs(self.main_cfg.gb_up_right) do
        if buy_seq == v.buy_seq then
            return v
        end
    end

    return nil
end

function this:SetGodBodyRightBuyInfo(protocol)
    self.uplevel_right_flag = protocol.uplevel_right_flag
    self.upgrade_right_flag = protocol.upgrade_right_flag
end

-- 获取修炼特权是否购买
function this:GetGodBodyUplevelRightIsBuy(buy_seq)
    return self.uplevel_right_flag[32- buy_seq] == 1
end

-- 获取渡劫特权是否购买
function this:GetGodBodyUpgradeRightIsBuy(buy_seq)
    return self.upgrade_right_flag[32- buy_seq] == 1
end

-- 计算升级消耗
function this:CalcUpLevelCost(end_time, slot, level)
    end_time = end_time or 0
    local cfg = self.god_body_other_cfg
    local float_cost = cfg.bind_gold_float
    local level_cfg = self:GetGBUpLevelCfg(slot, level)
    local total_time, total_gold = 0, 0
    if level_cfg then
        total_time = level_cfg.uplevel_time
        total_gold = level_cfg.need_total_gold
    end

    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local rest_time = end_time - now_time
    rest_time = rest_time < 0 and 0 or rest_time
    total_time = total_time <= 0 and 1 or total_time
    local cost_bind_gold = (rest_time / total_time) * total_gold
    cost_bind_gold = math.ceil(cost_bind_gold / float_cost) * float_cost
    return cost_bind_gold
end

-- 获取渡劫一定成功祝福值
function this:GetGBUpGradeNeedBless()
    return self.god_body_other_cfg.up_value or 1
end

-- 获取当前和下级的升级属性
function this:GetUpLevelShowAttr(slot, cur_level)
    local cur_cfg = self:GetGBUpLevelCfg(slot, cur_level)
    local next_cfg = self:GetGBUpLevelCfg(slot, cur_level + 1)
    local attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_cfg, next_cfg)
    return attr_list
end

-- 获取技能配置
function this:GetGodBodySkillCfg(slot, skill_index, skill_level)
    return ((self.god_body_skill_map_cfg[slot] or empty_table)[skill_index] or empty_table)[skill_level]
end

-- 获取技能属性
function this:GetGodBodySkillAttr(slot, skill_index, skill_level)
    local attr_list = {}
    local cfg = self:GetGodBodySkillCfg(slot, skill_index, skill_level)
    if cfg == nil then
        return attr_list
    end

    attr_list = ((self.cache_gb_skill_attr[slot] or empty_table)[skill_index] or empty_table)[skill_level]
    if attr_list == nil then
        local cahche_attr = self.cache_gb_skill_attr
        cahche_attr[slot] = cahche_attr[slot] or {}
        cahche_attr[slot][skill_index] = cahche_attr[slot][skill_index] or {}
        local attr_id_list = Split(cfg.attr_type_list, "|")
        local attr_value_list = Split(cfg.attr_value_list, "|")
        local temp_list = {}
        for k,v in ipairs(attr_id_list) do
            local attr_id = tonumber(v)
            local attr_value = tonumber(attr_value_list[k])
            if attr_id > 0 and attr_value > 0 then
                local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
                temp_list[attr_str] = attr_value
            end
        end

        cahche_attr[slot][skill_index][skill_level] = temp_list
        attr_list = temp_list
    end

    return attr_list
end

function this:GetGodBodySkillAttrTipsShow(slot, skill_index, skill_level)
    local attr_list = self:GetGodBodySkillAttr(slot, skill_index, skill_level)
    local sort_list, show_list = {}, {}
    for attr_str, value in pairs(attr_list) do
        local data = {}
        data.attr_str = attr_str
        data.value = value
        data.sort = AttributeMgr.GetSortAttributeIndex(attr_str)
        sort_list[#sort_list + 1] = data
    end

    if not IsEmptyTable(sort_list) then
    	table.sort(sort_list, SortTools.KeyLowerSorter("sort"))
    end

    local is_per
    local str_color = "#C4B8A8FF"
    local value_color = COLOR3B.D_GREEN
    for k,v in ipairs(sort_list) do
        local attr_str = v.attr_str
        is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_str)
        attr_str = Language.Common.AttrName[attr_str]
        local attr_value = is_per and v.value / 100 .. "%" or v.value
        local desc_str = string.format("<color=%s>%s<color=%s> %s</color></color>",
                        str_color, attr_str,
                        value_color, attr_value)

        table.insert(show_list, desc_str)
    end

    local skill_cfg = self:GetGodBodySkillCfg(slot, skill_index, skill_level)
    if skill_cfg and skill_cfg.gb_attr_add_per > 0 then
        local attr_str = Language.FairyLandEquipment.SkillAddPer
        local desc_str = string.format("<color=%s>%s <color=%s>%s%%</color></color>",
                        str_color, attr_str,
                        value_color, skill_cfg.gb_attr_add_per * 0.01)
        table.insert(show_list, desc_str)
    end

    return show_list
end

function this:GetGodBodySkillCapability(slot, skill_index, skill_level)
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return 0
    end

    local base_attribute = AttributePool.AllocAttribute()
    local attr_list = self:GetGodBodySkillAttr(slot, skill_index, skill_level)
    for attr, value in pairs(attr_list) do
        local attr_str = AttributeMgr.GetAttributteKey(attr)
        if base_attribute[attr_str] ~= nil and value > 0 then
            base_attribute[attr_str] = base_attribute[attr_str] + value
        end
    end

    -- 神体属性加成
    local skill_cfg = self:GetGodBodySkillCfg(slot, skill_index, skill_level)
    if skill_cfg and skill_cfg.gb_attr_add_per > 0 then
        local level = god_body:GetLevel()
        local uplevel_attr = self:GetGBUpLevelCfg(slot, level)
        if uplevel_attr ~= nil then
            local add_per = skill_cfg.gb_attr_add_per * 0.0001
            for attr, value in pairs(uplevel_attr) do
                local attr_str = AttributeMgr.GetAttributteKey(attr)
                if base_attribute[attr_str] ~= nil and value > 0 then
                    base_attribute[attr_str] = base_attribute[attr_str] + value * add_per
                end
            end
        end
    end

    return AttributeMgr.GetCapability(base_attribute)
end

-- 获取技能展示列表
function this:GetGoBSkillShowList(slot)
    local show_list = {}
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return show_list
    end

    local skill_cfg_list = self.god_body_skill_map_cfg[slot] or {}
    if IsEmptyTable(skill_cfg_list) then
    	return show_list
    end

    local cur_grade = god_body:GetGrade()
    local index, act_num = 0, 0
    for skill_index, level_list in ipairs(skill_cfg_list) do
        local show_level
        local max_level = level_list[#level_list].skill_level
        for skill_level, cfg in ipairs(level_list) do
            if show_level == nil or cur_grade >= cfg.grade then
                show_level = cfg.skill_level
            end
        end

        local cfg, next_cfg
        if show_level then
            cfg = level_list[show_level]
            next_cfg = level_list[show_level + 1]
        end

        if cfg then
            local data = {}
            local is_act = cur_grade >= cfg.grade
            act_num = is_act and act_num + 1 or act_num
            index = index + 1
            data.slot = cfg.slot
            data.grade = cfg.grade
            data.skill_index = cfg.skill_index
            data.skill_name = cfg.skill_name
            data.skill_icon = cfg.skill_icon
            data.skill_level = cfg.skill_level
            data.is_act = is_act
            data.is_max = cfg.skill_level >= max_level
            data.next_grade = (is_act and not data.is_max and next_cfg) and next_cfg.grade or cfg.grade
            data.is_next_act = not is_act and (index == act_num + 1)
            data.hide_next = data.is_max

            show_list[index] = data
        end
    end

    return show_list
end

-- 获取神体战力
function this:GetGodBodyCapability(slot)
	local capability = 0
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return capability
    end

	local attr_str
    local slot = god_body:GetSlotIndex()
    local level = god_body:GetLevel()
    local grade = god_body:GetGrade()
	local base_attribute = AttributePool.AllocAttribute()
	-- 等级属性
	local uplevel_attr = self:GetGBUpLevelCfg(slot, level)
	if uplevel_attr ~= nil then
		for attr, value in pairs(uplevel_attr) do
            attr_str = AttributeMgr.GetAttributteKey(attr)
            if base_attribute[attr_str] ~= nil and value > 0 then
                base_attribute[attr_str] = base_attribute[attr_str] + value
            end
        end
	end

    -- 技能属性
    local skill_act_list = {}
    local skill_cfg_list = self.god_body_skill_map_cfg[slot] or {}
    for skill_index, level_list in ipairs(skill_cfg_list) do
        skill_act_list[skill_index] = 0
        for skill_level, cfg in ipairs(level_list) do
            if grade >= cfg.grade then
                skill_act_list[skill_index] = skill_level
            end
        end
    end

    for k,v in pairs(skill_act_list) do
        if v > 0 then
            local attr_list = self:GetGodBodySkillAttr(slot, k, v)
            for attr, value in pairs(attr_list) do
                attr_str = AttributeMgr.GetAttributteKey(attr)
                if base_attribute[attr_str] ~= nil and value > 0 then
                    base_attribute[attr_str] = base_attribute[attr_str] + value
                end
            end

            -- 神体属性加成
            local gb_attr_add_per = 0

            local skill_cfg = self:GetGodBodySkillCfg(slot, k, v)
            if skill_cfg and skill_cfg.gb_attr_add_per > 0 then
                gb_attr_add_per = gb_attr_add_per + skill_cfg.gb_attr_add_per * 0.0001
            end

            local evolve_add_per = FairyLandEquipmentWGData.Instance:GetEvolveHasGBAttrAddPer(slot)
            gb_attr_add_per = gb_attr_add_per + evolve_add_per * 0.0001

            if uplevel_attr ~= nil then
                for attr, value in pairs(uplevel_attr) do
                    attr_str = AttributeMgr.GetAttributteKey(attr)
                    if base_attribute[attr_str] ~= nil and value > 0 then
                        base_attribute[attr_str] = base_attribute[attr_str] + value * gb_attr_add_per
                    end
                end
            end
        end
    end

    -- 特权
    local is_buy_uplevel_right = god_body:GetIsBuyUplevelRight()
    local is_buy_upgrade_right = god_body:GetIsBuyUpgradeRight()
    if is_buy_uplevel_right then
        capability = capability + self:GetGodBodyUplevelRightCapability(slot)
    end

    if is_buy_upgrade_right then
        capability = capability + self:GetGodBodyUpgradeRightCapability(slot)
    end


    capability = AttributeMgr.GetCapability(base_attribute)
    return capability
end

-- 获取修炼特权战力
function this:GetGodBodyUplevelRightCapability(slot)
    local capability = 0
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return capability
    end

    local buy_seq = god_body:GetRightBuySeq()
    local buy_cfg = self:GetGBUplevelRightCfg(buy_seq)
    if buy_cfg == nil then
        return capability
    end

    local attr_str
    local base_attribute = AttributePool.AllocAttribute()
	if buy_cfg ~= nil then
		for attr, value in pairs(buy_cfg) do
            attr_str = AttributeMgr.GetAttributteKey(attr)
            if base_attribute[attr_str] ~= nil and value > 0 then
                base_attribute[attr_str] = base_attribute[attr_str] + value
            end
        end
	end

    capability = AttributeMgr.GetCapability(base_attribute)
    return capability
end

-- 获取渡劫特权战力
function this:GetGodBodyUpgradeRightCapability(slot)
    local capability = 0
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil then
        return capability
    end

    local buy_seq = god_body:GetRightBuySeq()
    local buy_cfg = self:GetGBUpgradeRightCfg(buy_seq)
    if buy_cfg == nil then
        return capability
    end

    local attr_str
    local base_attribute = AttributePool.AllocAttribute()
	if buy_cfg ~= nil then
		for attr, value in pairs(buy_cfg) do
            attr_str = AttributeMgr.GetAttributteKey(attr)
            if base_attribute[attr_str] ~= nil and value > 0 then
                base_attribute[attr_str] = base_attribute[attr_str] + value
            end
        end
	end

    capability = AttributeMgr.GetCapability(base_attribute)
    return capability
end

function this:GetGodBodySlotRemind(slot)
    local god_body = self:GetGodBodyData(slot)
    if god_body == nil or not god_body:GetIsAct() then
        return false
    end

    local is_max_level = god_body:GetIsMaxLevel()
    if is_max_level then
        return false
    end

    local slot = god_body:GetSlotIndex()
    local level = god_body:GetLevel()
    local grade = god_body:GetGrade()
    local grade_cfg = self:GetGBUpGradeCfg(slot, grade)
    local cur_max_up_level = grade_cfg and grade_cfg.up_max_level or 0
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = god_body:GetUplevelEndTime()
    local rest_time = end_time - now_time

    -- 修炼中
    if rest_time > 0 and level < cur_max_up_level then
        return false
    -- 渡劫阶段
    elseif level == cur_max_up_level then
        if grade_cfg then
            -- 祝福值
            local bless_value = god_body:GetBlessValue()
            local need_bless_value = self:GetGBUpGradeNeedBless()
            if bless_value >= need_bless_value then
                return true
            end

            -- 渡劫材料
            local num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.stuff_id)
            if num >= grade_cfg.stuff_num then
                return true
            end
        end
    else
        return true
    end
end

function this:GetGodBodyRemind()
    local gb_list = self:GetGodBodyList()
    for slot = 0, #gb_list do
        if self:GetGodBodySlotRemind(slot) then
            self:ToBeStrengthen(1, MAINUI_TIP_TYPE.FLE_GODBODY_UPLEVEL,
                                TabIndex.fairy_land_eq_god_body, "all",
                                {to_ui_name = slot})
            return 1
        end
    end

    self:ToBeStrengthen(0, MAINUI_TIP_TYPE.FLE_GODBODY_UPLEVEL)
    return 0
end

function this:GetGodBodyUplevelRemindCD()
    local is_need_remind, remind_end_time = false, 0
    local gb_list = self:GetGodBodyList()
    local now_time = TimeWGCtrl.Instance:GetServerTime()

    for slot = 0, #gb_list do
        local bg_data = gb_list[slot]
        if bg_data and bg_data:GetIsAct()
        and not bg_data:GetIsMaxLevel()
        and not bg_data:GetIsBuyUplevelRight() then
            local end_time = bg_data:GetUplevelEndTime()
            if end_time > now_time and (remind_end_time == 0 or remind_end_time > end_time) then
                remind_end_time = end_time
                is_need_remind = true
            end
        end
    end

    return is_need_remind, remind_end_time
end
--============================================================================--
--================================【渡劫】=====================================--
