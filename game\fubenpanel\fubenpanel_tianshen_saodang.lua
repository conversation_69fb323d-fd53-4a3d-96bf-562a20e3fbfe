TianShenSaoDangView = TianShenSaoDangView or BaseClass(SafeBaseView)

function TianShenSaoDangView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_tianshen_saodang")

	self.saodang_index = -1
	self.item_data_change_callback = BindTool.Bind1(self.Flush, self)
end

function TianShenSaoDangView:__delete()

end

function TianShenSaoDangView:ReleaseCallBack()
	if self.saodang_cell then
		self.saodang_cell:DeleteMe()
		self.saodang_cell = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function TianShenSaoDangView:ShowIndexCallBack()
	self:Flush()
end

function TianShenSaoDangView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.FuBenPanel.TianShenSaoDangXiaoHao
	self:SetSecondView(nil, self.node_list["size"])
	self.saodang_cell = ItemCell.New(self.node_list["ph_cell"])
	self.node_list["btn_ok"].button:AddClickListener(BindTool.Bind(self.OnClinkOkHandler, self))
	XUI.AddClickEventListener(self.node_list.btn__pet_saodang_cancel, BindTool.Bind1(self.PetSaoDangViewClose, self))
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function TianShenSaoDangView:ShowIndexCallBack()
	self:Flush()
end

function TianShenSaoDangView:PetSaoDangViewClose()
	self:Close()
end

function TianShenSaoDangView:OnFlush()
	--local star_num = FuBenPanelWGData.Instance:SetPetStarDisplay(self.saodang_index)
	--local str = ""
	--if star_num and star_num >= 0 then
	--	str = string.format(Language.FuBenPanel.CopperSaoDangTips, star_num, star_num)
	--end
	--self.node_list["rich_saodang_des"].text.text = str
	local other_cfg = FuBenPanelWGData.Instance:GetTianShenOtherCfg()
	if self.saodang_cell then
		self.saodang_cell:SetData({item_id = other_cfg.sweep_item_id})
	end
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_item_id)
	if item_num < other_cfg.sweep_consum_item_num then
		self.node_list["lbl_item_num"].text.text = "<color=#ff0000>"..item_num.."</color>" .. "/" .. other_cfg.sweep_consum_item_num
	else
		self.node_list["lbl_item_num"].text.text = item_num .. "/" .. other_cfg.sweep_consum_item_num
	end
end

function TianShenSaoDangView:SetPetIndex(index)
	self.saodang_index = index
	self:Open()
end

function TianShenSaoDangView:OnClinkOkHandler()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyTianShenCfg()
	local buy_times = FuBenPanelWGData.Instance:GetTianShenBuyTimes()
	buy_times = vip_buy_cfg["param_" .. role_vip] - buy_times
	local other_cfg = FuBenPanelWGData.Instance:GetTianShenOtherCfg()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_item_id)
	if item_num < other_cfg.sweep_consum_item_num then
		GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, other_cfg.sweep_item_id, other_cfg.sweep_consum_item_num, other_cfg.seq)
		return
	end
	if buy_times > 0 and FuBenPanelWGData.Instance:GetTianShenCanEnterTimes() <= 0 then
		FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_TIANSHEN_FB)
	else
		FuBenWGCtrl.Instance:SendSwipeFB(FUBEN_TYPE.FBCT_TIANSHEN_FB, self.saodang_index)
	end
end