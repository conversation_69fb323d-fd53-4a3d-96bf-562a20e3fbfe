MainUIBianShenRender = MainUIBianShenRender or BaseClass(BaseRender)
function MainUIBianShenRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.skill_item,BindTool.Bind(self.OnClickSkill, self))
	self.is_bianshen_zhong = nil
end

function MainUIBianShenRender:__delete()
	self.parent = nil
	local key = self:GetCDKey()
	if CountDownManager.Instance:HasCountDown(key) then
		CountDownManager.Instance:RemoveCountDown(key)
	end

	self.is_bianshen_zhong = nil
end

function MainUIBianShenRender:SetParent(parent)
	self.parent = parent
end

function MainUIBianShenRender:SetCDKey(key)
	self.other_cd_key = key
end

function MainUIBianShenRender:GetCDKey()
	return (self.other_cd_key or "mianui_bianshen_countdown") .. self.index
end

-- 技能点击事件
function MainUIBianShenRender:OnClickSkill()
	self.parent:OnClickTianShenListTween()
	local main_role = Scene.Instance:GetMainRole()
	if Scene.Instance:GetSceneType() == SceneType.GUILD_ANSWER_FB then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoBianShen)
		return
	end

	local is_chuan_gong = main_role:IsChuanGong()
	if is_chuan_gong then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.Passing)
		return
	end

	if main_role:IsGhost() or main_role.vo.special_appearance > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	if main_role:IsRidingFightMount() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewFightMount.CanNotDoThis)
		return
	end

	if self:NotCanUseMainUIBianShenRender() then
		return
	end

	if main_role:IsJump() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	-- 如果没有技能信息
	if self.not_has_skill then
		ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_activation)
	else
		local special_appearance = RoleWGData.Instance:GetAttr("special_appearance")
		if special_appearance == SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.ClickSkillTip1)
			return
		end

		if self.can_use_skill then
			-- 下坐骑
			MountWGCtrl.Instance:SendMountGoonReq(0)
			GuajiWGCtrl.Instance:ResetMoveCache()	--策划需求
			TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type15, self.image_seq)
		end
	end
end

function MainUIBianShenRender:OnFlush()
	self.is_bianshen_zhong = nil
	local data = self.data
	if IsEmptyTable(data) then
		return
	end
	
	self.node_list.Icon:SetActive(not data.not_has_skill)
	self.not_has_skill = data.not_has_skill
	local is_unlock = TianShenWGData.Instance:IsChuZhanIndexOpen(self.index - 1) 		-- 出战格子是否已开启
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	self.node_list.Add:SetActive(data.not_has_skill)
	self.node_list.CDText:SetActive(false)
	self.node_list.CDMask:SetActive(false)
	-- 没有技能
	if data.not_has_skill then
		self.node_list.Icon:SetActive(false)
		self.node_list.ts_anniu_eff:SetActive(false)
	else
		-- 天神变身结束时间戳
		local bianshen_end_time = data.bianshen_end_time
		local bianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(data.image_index)
		if not bianshen_cfg then
			return
		end
		
		self.bianshen_cfg = bianshen_cfg
		-- 下次可变身时间戳
		self.next_bianshen_time = data.next_bianshen_time
		-- 技能icon
		self.node_list.Icon.image:LoadSprite(ResPath.GetSkillIconById(bianshen_cfg.bianshen_icon))
		self.node_list.Icon.image:SetNativeSize()

		self.can_use_skill = server_time >= self.next_bianshen_time
		self.image_seq = bianshen_cfg.index
		-- 如果技能可用
		if self.can_use_skill then
			-- XUI.SetGraphicGrey(self.node_list.Icon, false)
			self.node_list["progress_bg"]:SetActive(true)
			self.node_list["progress_bg"].slider.value = 1
			self.node_list["hight_light"]:SetActive(false)
			local special_appearance = RoleWGData.Instance:GetAttr("special_appearance")  --判断是否是天神状态中
			if special_appearance == SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM then
				self.node_list.ts_anniu_eff:SetActive(false)
			else
				self.node_list.ts_anniu_eff:SetActive(true)
			end
		else
			self:FlushCountDown(self.next_bianshen_time, bianshen_end_time)
			self.node_list.ts_anniu_eff:SetActive(false)
		end

		local shuangsheng_show_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarData(data.image_index)
    
		if (not shuangsheng_show_data) or (shuangsheng_show_data.curr_aura_id == -1) then
			self.node_list.shuangsheng_type:SetActive(false)
		else
			self.node_list.shuangsheng_type:SetActive(true)
			local app_image_id_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(shuangsheng_show_data.curr_aura_id)
			if app_image_id_data then
				self.node_list.shuangsheng_type.image:LoadSprite(ResPath.GetCommon(string.format("a2_sssl_level_%d", app_image_id_data.sssl_index or 1)))
			else
				self.node_list.shuangsheng_type:SetActive(false)
			end
		end
	end
end

-- 刷新倒计时   end_time 下次可用时间戳   bianshen_end_time  变身结束时间戳
function MainUIBianShenRender:FlushCountDown(end_time, bianshen_end_time)
	local key = self:GetCDKey()
	if CountDownManager.Instance:HasCountDown(key) then
		CountDownManager.Instance:RemoveCountDown(key)
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self:FlushTime(0, end_time - server_time)
	if self.next_bianshen_time > server_time then
		CountDownManager.Instance:AddCountDown(key, BindTool.Bind(self.FlushTime, self), BindTool.Bind(self.Flush, self), self.next_bianshen_time, nil, 0.1)
	end
end

function MainUIBianShenRender:FlushTime(elapse_time, total_time)
	if not self.data or self.data.not_has_skill then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local bianshen_end_time = self.data.bianshen_end_time
	local bianshenzhong = bianshen_end_time > server_time 		-- 是否变身中(变身中不显示cd)
	local cur_bianshen_status =  self.next_bianshen_time and self.next_bianshen_time <= server_time or bianshenzhong

	if self.is_bianshen_zhong == nil or (cur_bianshen_status ~= self.is_bianshen_zhong) then
		self.is_bianshen_zhong = cur_bianshen_status
		if self.is_bianshen_zhong then
			self.node_list["progress_bg"]:SetActive(true)
			self.node_list.CDText:SetActive(false)
			self.node_list.CDMask:SetActive(false)
			self.node_list["hight_light"]:SetActive(bianshenzhong)
		else
			self.node_list["hight_light"]:SetActive(false)
			self.node_list["progress_bg"]:SetActive(false)
			self.node_list.CDText:SetActive(true)
			self.node_list.CDMask:SetActive(true)
		end
	end

	if cur_bianshen_status then
		local value = (bianshen_end_time - server_time) / self.bianshen_cfg.bianshen_duration_s
		self.node_list["progress_bg"].slider.value = value
		if bianshenzhong then
			self.node_list["hight_light"].transform.localRotation = Quaternion.Euler(0, 0, -360 * (1 - value))
		end
	else
		self.node_list.CDText.text.text = string.format("%0.1f",self.next_bianshen_time - server_time)
		self.node_list.CDMask.image.fillAmount = (self.next_bianshen_time - server_time) / self.bianshen_cfg.bianshen_cd_s
	end
end

--能否使用变身技能
--true -- 表示不能使用改技能
function MainUIBianShenRender:NotCanUseMainUIBianShenRender()
	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove(true) then
		return true
	end

	return false
end