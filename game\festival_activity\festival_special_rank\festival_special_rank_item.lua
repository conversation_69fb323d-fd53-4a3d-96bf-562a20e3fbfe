FestivalSPRankItem = FestivalSPRankItem or BaseClass(BaseRender)
function FestivalSPRankItem:LoadCallBack()
    self.item = ItemCell.New(self.node_list.item_pos)
end
function FestivalSPRankItem:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end
function FestivalSPRankItem:OnFlush()
    if not self.data then
        return
    end
    self.item:SetData(self.data)
end