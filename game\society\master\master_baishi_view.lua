MasterBaishiView = MasterBaishiView or BaseClass(SafeBaseView)

function MasterBaishiView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:LoadConfig()

	self.item_list = {}
	self.alert_window = nil
	self.shifu_id = 0
end

function MasterBaishiView:__delete()

end

function MasterBaishiView:ReleaseCallBack()
	if self.item_list then
		for k,v in pairs(self.item_list) do
			v:DeleteMe()
		end
	end
	
	if nil ~= self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	self.item_list = {}
	self.cur_select_gift = nil
end

function MasterBaishiView:LoadConfig()
	self:AddViewResource(0, "uis/view/master_ui_prefab", "layout_baishi")
end

function MasterBaishiView:LoadCallBack()
	self.node_list["btn_baishi"].button:AddClickListener(BindTool.Bind1(self.<PERSON>uh<PERSON><PERSON><PERSON><PERSON>, self))
	self:CreateRingItem()
end

function MasterBaishiView:OpenCallBack()

end

function MasterBaishiView:CreateRingItem()
	self.item_list = {}
	local baishi_cfg =  ConfigManager.Instance:GetAutoConfig("shituconfig_auto").baishi_gift or {}
	for i=1,3 do
		local name_txt = self.node_list["label_giftname" .. i].text
		local money_txt = self.node_list["rich_giftmoney" .. i].text
		local ring_cfg = baishi_cfg[i] or {}
		local item = ItemCell.New()
		item:SetInstanceParent(self.node_list["ph_item"..i])
		item:SetClickCallBack(BindTool.Bind1(self.SelectMarryTypeHandler, self))

		local item_data = ring_cfg.reward_item
		if item_data then
			item:SetData(item_data)
			local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
			if item_cfg then
				name_txt.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			end
		end

		local need_coin = ring_cfg.need_coin or 0
		local need_coin_str = ""
		if need_coin > 10000 then
			need_coin_str = math.floor(need_coin / 10000) .. Language.Common.Wan
		end

		need_coin_str = "<color=#006a25>" .. need_coin .. "</color>" .. Language.Common.Coin
		local need_gold = ring_cfg.need_gold or 0
		local need_gold_str = "<color=#006a25>" .. need_gold .. "</color>" .. Language.Common.Gold
		local str_value = need_coin > 0 and need_coin_str or need_gold_str
		money_txt.text = str_value

		table.insert(self.item_list, item)
	end

	if self.item_list[1] then
		self:SelectMarryTypeHandler(self.item_list[1])
	end
end

function MasterBaishiView:SetData(lover_id, str)
	if nil == lover_id then
		return
	end

	self.shifu_id = lover_id
	self.str = str or ""
	self:Open()
end

function MasterBaishiView:ShowIndexCallBack()
	local str_value = Language.Master.MasterProposeTips
	str_value = string.format(str_value, self.str)
	self.node_list["rich_tips"].text.text = str_value
end

function MasterBaishiView:QiuhunHandler()
	if nil == self.cur_select_gift then
		return
	end

	local ring_id = self.cur_select_gift:GetData().item_id
	local ring_num = self.cur_select_gift:GetData().num
	local baishi_type = MasterWGData.Instance:GetBaishiTypeById(ring_id,ring_num)
	if baishi_type then
		if nil == self.alert_window then
			self.alert_window = Alert.New()
		end

		local ring_cfg =   MasterWGData.Instance:GetOneBaishiCfgByType(baishi_type) or {}
		local coin = ring_cfg.need_coin or 0
		local gold = ring_cfg.need_gold or 0
		local money = coin > 0 and coin or gold
		if money > 10000 then
			money = (money / 10000) .. Language.Common.Wan
		end

		money = coin > 0 and (money .. Language.Common.Coin) or (money .. Language.Common.Gold)
		local item_cfg = ItemWGData.Instance:GetItemConfig(ring_id)
		local name = ""
		local color = COLOR3B.WHITE
		if item_cfg then
			name = item_cfg.name
			color = ITEM_COLOR[item_cfg.color]
		end

		local des = string.format(Language.Master.ProposeConfirm, money, HtmlTool.GetHtml(name, color),ring_num)
		self.alert_window:SetLableString(des)
		local param_t = {baishi_type = baishi_type, coin = coin, gold = gold}
		self.alert_window:SetOkFunc(BindTool.Bind2(self.SendMarryReq, self, param_t))
		self.alert_window:Open()
		self:Close()
	end
end

function MasterBaishiView:SendMarryReq(param_t)
	if param_t.coin > 0 then
		if not RoleWGData.GetIsEnoughAllCoin(param_t.coin) then
			GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, COMMON_CONSTS.VIRTUAL_ITEM_COIN)
			return
		end
	else
		if not RoleWGData.Instance:GetIsEnoughUseGold(param_t.gold) then
			UiInstanceMgr.Instance:ShowChongZhiView()
			return
		end
	end

	MasterWGCtrl.Instance:SendBaishi(self.shifu_id, param_t.baishi_type)
end

function MasterBaishiView:SelectMarryTypeHandler(ring)
	if self.cur_select_gift then
		self.cur_select_gift:SetSelect(false)
	end
	self.cur_select_gift = ring
	self.cur_select_gift:SetSelect(true)
end

function MasterBaishiView:CloseCallBack()
end