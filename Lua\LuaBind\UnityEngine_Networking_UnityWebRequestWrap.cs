﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Networking_UnityWebRequestWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(UnityEngine.Networking.UnityWebRequest), typeof(System.Object));
		<PERSON><PERSON>RegFunction("ClearCookieCache", ClearCookieCache);
		<PERSON><PERSON>unction("Dispose", Dispose);
		<PERSON><PERSON>ction("SendWebRequest", SendWebRequest);
		<PERSON><PERSON>RegFunction("Abort", Abort);
		<PERSON><PERSON>unction("GetRequestHeader", GetRequestHeader);
		<PERSON><PERSON>ction("SetRequestHeader", SetRequestHeader);
		<PERSON><PERSON>("GetResponseHeader", GetResponseHeader);
		<PERSON><PERSON>unction("GetResponseHeaders", GetResponseHeaders);
		<PERSON><PERSON>Function("Get", Get);
		<PERSON><PERSON>unction("Delete", Delete);
		<PERSON><PERSON>unction("Head", Head);
		<PERSON><PERSON>RegFunction("Put", Put);
		<PERSON><PERSON>un<PERSON>("Post", Post);
		<PERSON><PERSON>un<PERSON>("EscapeURL", EscapeURL);
		<PERSON><PERSON>unction("UnEscapeURL", UnEscapeURL);
		L.RegFunction("SerializeFormSections", SerializeFormSections);
		L.RegFunction("GenerateBoundary", GenerateBoundary);
		L.RegFunction("SerializeSimpleForm", SerializeSimpleForm);
		L.RegFunction("GetByteDownloads", GetByteDownloads);
		L.RegFunction("New", _CreateUnityEngine_Networking_UnityWebRequest);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("kHttpVerbGET", get_kHttpVerbGET, null);
		L.RegVar("kHttpVerbHEAD", get_kHttpVerbHEAD, null);
		L.RegVar("kHttpVerbPOST", get_kHttpVerbPOST, null);
		L.RegVar("kHttpVerbPUT", get_kHttpVerbPUT, null);
		L.RegVar("kHttpVerbCREATE", get_kHttpVerbCREATE, null);
		L.RegVar("kHttpVerbDELETE", get_kHttpVerbDELETE, null);
		L.RegVar("disposeCertificateHandlerOnDispose", get_disposeCertificateHandlerOnDispose, set_disposeCertificateHandlerOnDispose);
		L.RegVar("disposeDownloadHandlerOnDispose", get_disposeDownloadHandlerOnDispose, set_disposeDownloadHandlerOnDispose);
		L.RegVar("disposeUploadHandlerOnDispose", get_disposeUploadHandlerOnDispose, set_disposeUploadHandlerOnDispose);
		L.RegVar("method", get_method, set_method);
		L.RegVar("error", get_error, null);
		L.RegVar("useHttpContinue", get_useHttpContinue, set_useHttpContinue);
		L.RegVar("url", get_url, set_url);
		L.RegVar("uri", get_uri, set_uri);
		L.RegVar("responseCode", get_responseCode, null);
		L.RegVar("uploadProgress", get_uploadProgress, null);
		L.RegVar("isModifiable", get_isModifiable, null);
		L.RegVar("isDone", get_isDone, null);
		L.RegVar("result", get_result, null);
		L.RegVar("downloadProgress", get_downloadProgress, null);
		L.RegVar("uploadedBytes", get_uploadedBytes, null);
		L.RegVar("downloadedBytes", get_downloadedBytes, null);
		L.RegVar("redirectLimit", get_redirectLimit, set_redirectLimit);
		L.RegVar("uploadHandler", get_uploadHandler, set_uploadHandler);
		L.RegVar("downloadHandler", get_downloadHandler, set_downloadHandler);
		L.RegVar("certificateHandler", get_certificateHandler, set_certificateHandler);
		L.RegVar("timeout", get_timeout, set_timeout);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Networking_UnityWebRequest(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Networking.UnityWebRequest obj = new UnityEngine.Networking.UnityWebRequest();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 1 && TypeChecker.CheckTypes<string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				UnityEngine.Networking.UnityWebRequest obj = new UnityEngine.Networking.UnityWebRequest(arg0);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 1 && TypeChecker.CheckTypes<System.Uri>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				UnityEngine.Networking.UnityWebRequest obj = new UnityEngine.Networking.UnityWebRequest(arg0);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string, string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				UnityEngine.Networking.UnityWebRequest obj = new UnityEngine.Networking.UnityWebRequest(arg0, arg1);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Uri, string>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				UnityEngine.Networking.UnityWebRequest obj = new UnityEngine.Networking.UnityWebRequest(arg0, arg1);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<string, string, UnityEngine.Networking.DownloadHandler, UnityEngine.Networking.UploadHandler>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				UnityEngine.Networking.DownloadHandler arg2 = (UnityEngine.Networking.DownloadHandler)ToLua.ToObject(L, 3);
				UnityEngine.Networking.UploadHandler arg3 = (UnityEngine.Networking.UploadHandler)ToLua.ToObject(L, 4);
				UnityEngine.Networking.UnityWebRequest obj = new UnityEngine.Networking.UnityWebRequest(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<System.Uri, string, UnityEngine.Networking.DownloadHandler, UnityEngine.Networking.UploadHandler>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				UnityEngine.Networking.DownloadHandler arg2 = (UnityEngine.Networking.DownloadHandler)ToLua.ToObject(L, 3);
				UnityEngine.Networking.UploadHandler arg3 = (UnityEngine.Networking.UploadHandler)ToLua.ToObject(L, 4);
				UnityEngine.Networking.UnityWebRequest obj = new UnityEngine.Networking.UnityWebRequest(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Networking.UnityWebRequest.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearCookieCache(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Networking.UnityWebRequest.ClearCookieCache();
				return 0;
			}
			else if (count == 1)
			{
				System.Uri arg0 = (System.Uri)ToLua.CheckObject<System.Uri>(L, 1);
				UnityEngine.Networking.UnityWebRequest.ClearCookieCache(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Networking.UnityWebRequest.ClearCookieCache");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Dispose(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 1);
			obj.Dispose();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SendWebRequest(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 1);
			UnityEngine.Networking.UnityWebRequestAsyncOperation o = obj.SendWebRequest();
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Abort(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 1);
			obj.Abort();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRequestHeader(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			string o = obj.GetRequestHeader(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRequestHeader(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			string arg1 = ToLua.CheckString(L, 3);
			obj.SetRequestHeader(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetResponseHeader(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			string o = obj.GetResponseHeader(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetResponseHeaders(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 1);
			System.Collections.Generic.Dictionary<string,string> o = obj.GetResponseHeaders();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Get(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1 && TypeChecker.CheckTypes<string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Get(arg0);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 1 && TypeChecker.CheckTypes<System.Uri>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Get(arg0);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Networking.UnityWebRequest.Get");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Delete(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1 && TypeChecker.CheckTypes<string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Delete(arg0);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 1 && TypeChecker.CheckTypes<System.Uri>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Delete(arg0);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Networking.UnityWebRequest.Delete");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Head(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1 && TypeChecker.CheckTypes<string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Head(arg0);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 1 && TypeChecker.CheckTypes<System.Uri>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Head(arg0);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Networking.UnityWebRequest.Head");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Put(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<string, byte[]>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				byte[] arg1 = ToLua.CheckByteBuffer(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Put(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Uri, byte[]>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				byte[] arg1 = ToLua.CheckByteBuffer(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Put(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string, string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Put(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Uri, string>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Put(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Networking.UnityWebRequest.Put");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Post(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<string, string>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Uri, string>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string, UnityEngine.WWWForm>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				UnityEngine.WWWForm arg1 = (UnityEngine.WWWForm)ToLua.ToObject(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Uri, UnityEngine.WWWForm>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				UnityEngine.WWWForm arg1 = (UnityEngine.WWWForm)ToLua.ToObject(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string, System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection> arg1 = (System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>)ToLua.ToObject(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Uri, System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection> arg1 = (System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>)ToLua.ToObject(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string, System.Collections.Generic.Dictionary<string,string>>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				System.Collections.Generic.Dictionary<string,string> arg1 = (System.Collections.Generic.Dictionary<string,string>)ToLua.ToObject(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Uri, System.Collections.Generic.Dictionary<string,string>>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				System.Collections.Generic.Dictionary<string,string> arg1 = (System.Collections.Generic.Dictionary<string,string>)ToLua.ToObject(L, 2);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<string, System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>, byte[]>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection> arg1 = (System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>)ToLua.ToObject(L, 2);
				byte[] arg2 = ToLua.CheckByteBuffer(L, 3);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1, arg2);
				ToLua.PushObject(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<System.Uri, System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>, byte[]>(L, 1))
			{
				System.Uri arg0 = (System.Uri)ToLua.ToObject(L, 1);
				System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection> arg1 = (System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>)ToLua.ToObject(L, 2);
				byte[] arg2 = ToLua.CheckByteBuffer(L, 3);
				UnityEngine.Networking.UnityWebRequest o = UnityEngine.Networking.UnityWebRequest.Post(arg0, arg1, arg2);
				ToLua.PushObject(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Networking.UnityWebRequest.Post");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EscapeURL(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string o = UnityEngine.Networking.UnityWebRequest.EscapeURL(arg0);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				System.Text.Encoding arg1 = (System.Text.Encoding)ToLua.CheckObject<System.Text.Encoding>(L, 2);
				string o = UnityEngine.Networking.UnityWebRequest.EscapeURL(arg0, arg1);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Networking.UnityWebRequest.EscapeURL");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnEscapeURL(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string o = UnityEngine.Networking.UnityWebRequest.UnEscapeURL(arg0);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				System.Text.Encoding arg1 = (System.Text.Encoding)ToLua.CheckObject<System.Text.Encoding>(L, 2);
				string o = UnityEngine.Networking.UnityWebRequest.UnEscapeURL(arg0, arg1);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Networking.UnityWebRequest.UnEscapeURL");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SerializeFormSections(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection> arg0 = (System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>)ToLua.CheckObject(L, 1, typeof(System.Collections.Generic.List<UnityEngine.Networking.IMultipartFormSection>));
			byte[] arg1 = ToLua.CheckByteBuffer(L, 2);
			byte[] o = UnityEngine.Networking.UnityWebRequest.SerializeFormSections(arg0, arg1);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GenerateBoundary(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			byte[] o = UnityEngine.Networking.UnityWebRequest.GenerateBoundary();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SerializeSimpleForm(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Collections.Generic.Dictionary<string,string> arg0 = (System.Collections.Generic.Dictionary<string,string>)ToLua.CheckObject(L, 1, typeof(System.Collections.Generic.Dictionary<string,string>));
			byte[] o = UnityEngine.Networking.UnityWebRequest.SerializeSimpleForm(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetByteDownloads(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)ToLua.CheckObject<UnityEngine.Networking.UnityWebRequest>(L, 1);
			double o = obj.GetByteDownloads();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_kHttpVerbGET(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.Networking.UnityWebRequest.kHttpVerbGET);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_kHttpVerbHEAD(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.Networking.UnityWebRequest.kHttpVerbHEAD);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_kHttpVerbPOST(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.Networking.UnityWebRequest.kHttpVerbPOST);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_kHttpVerbPUT(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.Networking.UnityWebRequest.kHttpVerbPUT);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_kHttpVerbCREATE(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.Networking.UnityWebRequest.kHttpVerbCREATE);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_kHttpVerbDELETE(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, UnityEngine.Networking.UnityWebRequest.kHttpVerbDELETE);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_disposeCertificateHandlerOnDispose(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool ret = obj.disposeCertificateHandlerOnDispose;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disposeCertificateHandlerOnDispose on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_disposeDownloadHandlerOnDispose(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool ret = obj.disposeDownloadHandlerOnDispose;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disposeDownloadHandlerOnDispose on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_disposeUploadHandlerOnDispose(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool ret = obj.disposeUploadHandlerOnDispose;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disposeUploadHandlerOnDispose on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_method(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			string ret = obj.method;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index method on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_error(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			string ret = obj.error;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index error on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_useHttpContinue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool ret = obj.useHttpContinue;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useHttpContinue on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_url(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			string ret = obj.url;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index url on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uri(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			System.Uri ret = obj.uri;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uri on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_responseCode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			long ret = obj.responseCode;
			LuaDLL.tolua_pushint64(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index responseCode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uploadProgress(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			float ret = obj.uploadProgress;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uploadProgress on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isModifiable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool ret = obj.isModifiable;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isModifiable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isDone(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool ret = obj.isDone;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isDone on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_result(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			UnityEngine.Networking.UnityWebRequest.Result ret = obj.result;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index result on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_downloadProgress(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			float ret = obj.downloadProgress;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index downloadProgress on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uploadedBytes(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			ulong ret = obj.uploadedBytes;
			LuaDLL.tolua_pushuint64(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uploadedBytes on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_downloadedBytes(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			ulong ret = obj.downloadedBytes;
			LuaDLL.tolua_pushuint64(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index downloadedBytes on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_redirectLimit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			int ret = obj.redirectLimit;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index redirectLimit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uploadHandler(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			UnityEngine.Networking.UploadHandler ret = obj.uploadHandler;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uploadHandler on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_downloadHandler(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			UnityEngine.Networking.DownloadHandler ret = obj.downloadHandler;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index downloadHandler on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_certificateHandler(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			UnityEngine.Networking.CertificateHandler ret = obj.certificateHandler;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index certificateHandler on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_timeout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			int ret = obj.timeout;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeout on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_disposeCertificateHandlerOnDispose(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.disposeCertificateHandlerOnDispose = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disposeCertificateHandlerOnDispose on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_disposeDownloadHandlerOnDispose(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.disposeDownloadHandlerOnDispose = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disposeDownloadHandlerOnDispose on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_disposeUploadHandlerOnDispose(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.disposeUploadHandlerOnDispose = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index disposeUploadHandlerOnDispose on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_method(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.method = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index method on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_useHttpContinue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.useHttpContinue = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useHttpContinue on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_url(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.url = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index url on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_uri(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			System.Uri arg0 = (System.Uri)ToLua.CheckObject<System.Uri>(L, 2);
			obj.uri = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uri on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_redirectLimit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.redirectLimit = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index redirectLimit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_uploadHandler(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			UnityEngine.Networking.UploadHandler arg0 = (UnityEngine.Networking.UploadHandler)ToLua.CheckObject<UnityEngine.Networking.UploadHandler>(L, 2);
			obj.uploadHandler = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uploadHandler on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_downloadHandler(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			UnityEngine.Networking.DownloadHandler arg0 = (UnityEngine.Networking.DownloadHandler)ToLua.CheckObject<UnityEngine.Networking.DownloadHandler>(L, 2);
			obj.downloadHandler = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index downloadHandler on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_certificateHandler(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			UnityEngine.Networking.CertificateHandler arg0 = (UnityEngine.Networking.CertificateHandler)ToLua.CheckObject<UnityEngine.Networking.CertificateHandler>(L, 2);
			obj.certificateHandler = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index certificateHandler on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_timeout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Networking.UnityWebRequest obj = (UnityEngine.Networking.UnityWebRequest)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.timeout = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeout on a nil value");
		}
	}
}

