YinianMagicWGData = YinianMagicWGData or BaseClass()

function YinianMagicWGData:__init()
	if YinianMagicWGData.Instance then
		print_error("[YinianMagicWGData] Attempt to create singleton twice!")
		return
	end

	YinianMagicWGData.Instance = self
    self:InitConfig()

    self.type = -1
    self.daily_rmb_buy_flag = 0
    self.rmb_buy_flag = {}
    self.level = 0
    self.exp = 0
    self.chongzhi_exp = 0
    self.level_reward_flag = {}
    self.grade = 0
    self.bless = 0
    self.chongzhi_bless = 0
    self.use_grade = 0
    self.task_list = {}

    RemindManager.Instance:Register(RemindName.YinianMagic, BindTool.Bind(self.GetYinianMagicRemind, self))
end

function YinianMagicWGData:__delete()
    YinianMagicWGData.Instance = nil

    RemindManager.Instance:UnRegister(RemindName.YinianMagic)
end

function YinianMagicWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("god_or_demon_cfg_auto")
    self.title_cfg = ListToMap(cfg.title, "type", "level")
    self.type_cfg = cfg.type
    self.level_cfg = ListToMap(cfg.level, "type", "level", "stage")
    self.task_cfg = ListToMap(cfg.task, "task_id")
    self.rmb_buy_cfg = cfg.rmb_buy
    self.grade_cfg = ListToMap(cfg.grade, "type", "grade")
    self.skill_cfg = ListToMap(cfg.skill, "type", "skill_id")
    self.model_type_cfg = ListToMap(cfg.model_type, "type", "model_type")
    self.appe_image_cfg = ListToMap(cfg.model_type, "appe_image_id")
    self.other_cfg = cfg.other[1]
end

--协议数据
function YinianMagicWGData:SetAllGodOrDemonInfo(protocol)
	self.type = protocol.type
	self.daily_rmb_buy_flag = protocol.daily_rmb_buy_flag
	self.rmb_buy_flag = protocol.rmb_buy_flag
	self.level = protocol.level
	self.exp = protocol.exp
    self.chongzhi_exp = protocol.chongzhi_exp
	self.level_reward_flag = protocol.level_reward_flag
	self.grade =  protocol.grade
	self.bless = protocol.bless
    self.chongzhi_bless = protocol.chongzhi_bless
	self.use_grade = protocol.use_grade
end

function YinianMagicWGData:SetAllTaskInfo(protocol)
    self.task_list = protocol.task_list
end

-- 单个任务信息改变
function YinianMagicWGData:SetSingleTaskInfo(protocol)
    local data = protocol.change_data
    for i, v in pairs(self.task_list ) do
        if v.task_id == data.task_id then
            self.task_list[v.task_id] = data
            return
        end
    end
end

function YinianMagicWGData:GetAllTaskList()
	local task_data_list = {}
	if not IsEmptyTable(self.task_cfg) then
		for k, v in pairs(self.task_cfg) do
			local task_data = {}
			task_data.task_type = v.task_type
			task_data.task_id = v.task_id
			task_data.des = v.des
			task_data.target = v.target
			task_data.open_panel = v.open_panel
            task_data.add_exp = v.add_exp
            task_data.add_bless = v.add_bless
			local status = self.task_list[v.task_id] and self.task_list[v.task_id].status or REWARD_STATE_TYPE.UNDONE
			task_data.status = status
			task_data.progress_num_flag = self.task_list[v.task_id] and self.task_list[v.task_id].progress_num_flag or 0
	
			if status == REWARD_STATE_TYPE.UNDONE then
				task_data.sort_index = 100
			elseif status == REWARD_STATE_TYPE.CAN_FETCH then
				task_data.sort_index = 10
			elseif status == REWARD_STATE_TYPE.FINISH then
				task_data.sort_index = 1000
			end

            table.insert(task_data_list, task_data)
		end
	end

	table.sort(task_data_list, SortTools.KeyLowerSorters("sort_index", "task_id"))
	return task_data_list
end

function YinianMagicWGData:GetCurSelcetType()
    return self.type
end

function YinianMagicWGData:GetCurExpValue()
    return self.exp
end

function YinianMagicWGData:GetCurLevel()
    return self.level
end

function YinianMagicWGData:GetCurDailyGiftState()
    return self.daily_rmb_buy_flag == 1
end

function YinianMagicWGData:GetCurUseGrade()
    return self.use_grade
end

--获取充值得到的经验值和祝福值
function YinianMagicWGData:GetChongZhiGetExpAndBless()
    return self.chongzhi_exp, self.chongzhi_bless
end

-- 获取等级奖励状态
function YinianMagicWGData:GetLevelRewardState(stage)
	return self.level_reward_flag[stage] and self.level_reward_flag[stage] == 1
end

--获得直购等级
function YinianMagicWGData:GetPurchaseLevel()
    local level = 0
    for k, v in ipairs(self.rmb_buy_flag) do
        if v == 1 then
            level = k
        else
            break
        end
    end

    return level
end

function YinianMagicWGData:GetTitleTypeInfo()
    local open_slot = {}
    local list = self.title_cfg[self.type]
    local cur_level = self:GetCurLevel()
	if list then
		for k, v in pairs(list) do
			if v.level <= 4 then
				table.insert(open_slot,v)
			else
                if cur_level >= v.level then
                    table.insert(open_slot, v)
                end
			end
		end
	end

    return open_slot
end

function YinianMagicWGData:GetCurGrade()
	return self.grade
end

function YinianMagicWGData:GetCurBless()
	return self.bless
end

function YinianMagicWGData:GetCurUseGrade()
	return self.use_grade
end

function YinianMagicWGData:GetTypeCfgByType(type)
    return self.type_cfg[type]
end

function YinianMagicWGData:GetLevelReWardInfo(type, level)
    return (self.level_cfg[type] or {})[level]
end

function YinianMagicWGData:GetOtherCfg()
	return self.other_cfg
end

function YinianMagicWGData:GetGradeCfg(type, grade)
    return (self.grade_cfg[type] or {})[grade]
end

function YinianMagicWGData:GetMaxGrade(type)
    if self.grade_cfg[type] then
        return #self.grade_cfg[type]
    end
end

function YinianMagicWGData:GetSkillList(type)
    return self.skill_cfg[type]
end

function YinianMagicWGData:GetSkillCfgById(type, skill_id)
    return (self.skill_cfg[type] or {})[skill_id]
end

function YinianMagicWGData:GetModelTypeCfg(type, model_type)
   return (self.model_type_cfg[type] or {})[model_type]
end

function YinianMagicWGData:GetAppeImageCfg(appe_image_id)
    return self.appe_image_cfg[appe_image_id]
 end

function YinianMagicWGData:GetMaxModelType(type)
    return #self.model_type_cfg[type]
end

function YinianMagicWGData:GetPurchaseCfgbyLevel(level)
	return self.rmb_buy_cfg[level]
end

--经验进度
function YinianMagicWGData:GetCurExpProgress(type, level, exp_num)
	local cur_progress = 0
	local cfg = self:GetLevelReWardInfo(type, level)
	local cur_exp_value = tonumber(exp_num)
	if next(cfg) == nil or exp_num == nil then
		return cur_progress
	end

    local progress_list = {[0] = 0.14, [1] = 0.28, [2] = 0.42, [3] = 0.56, [4] = 0.7, [5] = 0.84, [6] = 1}			--对应的进度条值
	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list
		local cur_need = cfg[seq] and cfg[seq].need_exp or 0
		local next_need = cfg[seq + 1] and cfg[seq + 1].need_exp or cfg[#cfg].need_exp
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		if cur_exp_value > cur_need and cur_exp_value <= next_need then
			cur_progress = (cur_exp_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif cur_exp_value > cfg[#cfg].need_exp then
			cur_progress = progress_list[length]
			break
		end
	end

	return cur_progress
end

function YinianMagicWGData:GetCuLevelRewardIsGet()
    local all_get = true
    local reward_info = self:GetLevelReWardInfo(self.type, self.level)
    if IsEmptyTable(reward_info) then
        return false
    end

    for k, v in pairs(reward_info) do
        local is_get = self:GetLevelRewardState(v.stage)
        if not is_get then
            return false
        end
    end

    return true
end

function YinianMagicWGData:GetCapability()
    local capability = 0
    local select_type = self:GetCurSelcetType()
    if select_type <= 0 then
        return capability
    end

    local cur_grade = self:GetCurGrade()
    return self:GetCapabilityNum(select_type, cur_grade)
end

function YinianMagicWGData:GetMaxCapability(select_type)
    local grade = self:GetMaxGrade(select_type)
    return self:GetCapabilityNum(select_type, grade)
end

function YinianMagicWGData:GetCapabilityNum(select_type, grade)
    local capability = 0
    local base_attribute = AttributePool.AllocAttribute()
    local cur_cfg = self:GetGradeCfg(select_type, grade)
    -- 阶级属性
    local grade_attr = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, nil, "attr_id", "attr_value", 1, 5)
	if grade_attr ~= nil then
		for key, value in pairs(grade_attr) do
            if base_attribute[value.attr_str] ~= nil and value.attr_value > 0 then
                base_attribute[value.attr_str] = base_attribute[value.attr_str] + value.attr_value
            end
        end
	end

    --技能属性
    local attr_add_per = 0
    local skill_list = self:GetSkillList(select_type)
    for skill_index, skill_cfg in ipairs(skill_list) do
        if grade >= skill_cfg.need_grade then
            local skill_attr = EquipWGData.GetSortAttrListHaveNextByTypeCfg(skill_cfg, nil, "attr_id", "attr_value", 1, 4)
            for key, value in pairs(skill_attr) do
                if base_attribute[value.attr_str] ~= nil and value.attr_value > 0 then
                    base_attribute[value.attr_str] = base_attribute[value.attr_str] + value.attr_value
                end
            end

            attr_add_per = attr_add_per + skill_cfg.attr_per * 0.0001
        end
    end

    if grade_attr ~= nil then
        for attr, value in pairs(grade_attr) do
            if base_attribute[value.attr_str] ~= nil and value.attr_value > 0 then
                base_attribute[value.attr_str] = base_attribute[value.attr_str] + value.attr_value * attr_add_per 
            end

        end
    end

    capability = AttributeMgr.GetCapability(base_attribute)
    return capability
end

function YinianMagicWGData:GetSkillCapability(type, skill_id)
    local skill_cfg = self:GetSkillCfgById(type, skill_id)
    local capability = 0
    local base_attribute = AttributePool.AllocAttribute()
    if not skill_cfg then
        return capability
    end

    local skill_attr = EquipWGData.GetSortAttrListHaveNextByTypeCfg(skill_cfg, nil, "attr_id", "attr_value", 1, 4)
    for key, value in pairs(skill_attr) do
        if base_attribute[value.attr_str] ~= nil and value.attr_value > 0 then
            base_attribute[value.attr_str] = base_attribute[value.attr_str] + value.attr_value
        end
    end

    local attr_add_per = skill_cfg.attr_per * 0.0001
    if attr_add_per > 0 then
        local select_type = self:GetCurSelcetType()
        local cur_grade = self:GetCurGrade()
        local cur_cfg = self:GetGradeCfg(select_type, cur_grade)
        -- 阶级属性
        local grade_attr = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, nil, "attr_id", "attr_value", 1, 5)
        if grade_attr ~= nil then
            for key, value in pairs(grade_attr) do
                if base_attribute[value.attr_str] ~= nil and value.attr_value > 0 then
                    base_attribute[value.attr_str] = base_attribute[value.attr_str] + value.attr_value * attr_add_per
                end
            end
        end
    end

    return AttributeMgr.GetCapability(base_attribute)
end


---------------红点----------------

function YinianMagicWGData:GetYinianMagicRemind()
    local select_type = self:GetCurSelcetType()
    if select_type <= 0 then
        return 0
    end

    if self:GetGradeRemind() then
        return 1
    elseif self:GetTaskRemind() then
        return 1
    end

    local show_info = YinianMagicWGData.Instance:GetTitleTypeInfo()
    if not IsEmptyTable(show_info) then
        for i, v in ipairs(show_info) do
            if self:GetLevelRewardRemind(v.level) then
                return 1
            end
        end
    end
    
    if self.chongzhi_exp > 0 or self.chongzhi_bless > 0 then
        return 1
    end

	return 0
end

--阶级红点
function YinianMagicWGData:GetGradeRemind()
    local cur_grade = self:GetCurGrade()
	local select_type = self:GetCurSelcetType()
	local cur_bless = self:GetCurBless()
	local cur_cfg = self:GetGradeCfg(select_type, cur_grade)
	local next_cfg = self:GetGradeCfg(select_type, cur_grade + 1)
	local need_bless = cur_cfg and cur_cfg.need_bless or 0
    
    if not IsEmptyTable(cur_cfg) and  not IsEmptyTable(next_cfg) then
        if cur_bless >= need_bless then
            return true
        end
    end

    return false
end

--任务红点
function YinianMagicWGData:GetTaskRemind()
	for i, v in ipairs(self.task_list) do
		if v.status == REWARD_STATE_TYPE.CAN_FETCH then
            return true
		end
	end

	return false
end

--等级奖励红点
function YinianMagicWGData:GetLevelRewardRemind(select_level)
    local cur_level = self:GetCurLevel()
    local exp_value = self:GetCurExpValue()
    local select_type = self:GetCurSelcetType()
    local reward_info = self:GetLevelReWardInfo(select_type, select_level)
    if IsEmptyTable(reward_info) then
        return false
    end

    for k, v in pairs(reward_info) do
        local is_get = false
        if cur_level > select_level then
            is_get = true
        elseif cur_level == select_level then
            is_get = self:GetLevelRewardState(v.stage)
        end

        if not is_get and exp_value >= v.need_exp and cur_level == select_level then
            return true
        end
    end

    return false
end

