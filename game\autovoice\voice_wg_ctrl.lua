require("game/autovoice/voice_view")
require("game/autovoice/chat_record_mgr")

AutoVoiceWGCtrl = AutoVoiceWGCtrl or BaseClass(BaseWGCtrl)

function AutoVoiceWGCtrl:__init()
	if AutoVoiceWGCtrl.Instance then
		print_error("[AutoVoiceWGCtrl]:Attempt to create singleton twice!")
	end
	AutoVoiceWGCtrl.Instance = self

	self.view = AutoVoiceView.New()
end

function AutoVoiceWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil

	AutoVoiceWGCtrl.Instance = nil
end

function AutoVoiceWGCtrl:ShowVoiceView(channel_type)
	self.view:SetChannelType(channel_type)
	self.view:Open()
end

function AutoVoiceWGCtrl:SetIsMainChatVoice(bo,bo1)
	self.view:SetIsMainChatVoice(bo,bo1)
end

function AutoVoiceWGCtrl:SetIsCancelVoice(bo)
	self.view:SetIsCancelVoice(bo)
end

function AutoVoiceWGCtrl:SetState(state)
	self.view:SetState(state)
end