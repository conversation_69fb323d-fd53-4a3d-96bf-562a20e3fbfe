TripleRechargeView = TripleRechargeView or BaseClass(SafeBaseView)

local BTN_COUNT = 4
local REWARD_COUNT = 3

function TripleRechargeView:__init()
    self:SetMaskBg()
    self.view_style = ViewStyle.Half
	self:AddViewResource(0, "uis/view/triple_recharge_ui_prefab", "layout_triple_recharge")
end

function TripleRechargeView:LoadCallBack()
    self.select_index = -1

    local data_list = TripleRechargeWGData.Instance:GetCurGradeCfg()

    if not self.btn_list then
        self.btn_list = {}
        for i = 0, BTN_COUNT - 1 do
            if i <= #data_list then
                self.node_list["btn" .. i]:SetActive(true)
                self.btn_list[i] = TripleRechargeBtnRender.New(self.node_list["btn" .. i])
                self.btn_list[i]:SetData(data_list[i])
                self.btn_list[i]:SetIndex(i)
                self.btn_list[i]:AddClickEventListener(BindTool.Bind(self.OnClickBtn<PERSON><PERSON><PERSON>, self, i))
            else
                self.node_list["btn" .. i]:SetActive(false)
            end
        end
    end

    if not self.reward_list then
        self.reward_list = {}
        for i = 1, REWARD_COUNT do
            self.reward_list[i] = ItemCell.New(self.node_list["reward" .. i])
        end
    end

    if not self.reward_cell then
        self.reward_cell = ItemCell.New(self.node_list.reward_cell)
    end

    XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuyBtn, self))

    self:OnClickBtnHandler(0)
end

function TripleRechargeView:ReleaseCallBack()
    --self:CleanActTimer()

    if self.btn_list then
        for k, v in pairs(self.btn_list) do
            v:DeleteMe()
        end
        self.btn_list = nil
    end

    if self.reward_list then
        for k, v in pairs(self.reward_list) do
            v:DeleteMe()
        end
        self.reward_list = nil
    end

    if self.reward_cell then
        self.reward_cell:DeleteMe()
        self.reward_cell = nil
    end
end

function TripleRechargeView:OpenCallBack()
end

function TripleRechargeView:CloseCallBack()
end

function TripleRechargeView:ShowIndexCallBack()
    -- if not self.act_timer then
    --     local total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TRIPLE_RECHARGE)
    --     if total_time > 0 then
    --         self.act_timer = CountDown.Instance:AddCountDown(total_time, 0.5,
    --         function (elapse_time, act_time)
    --             local time = math.floor(act_time - elapse_time)
    --             self:UpdateTimeStr(time)
    --         end,
    --         function ()
    --             self:Close()
    --         end
    --         )
    --     end
    -- end
end

function TripleRechargeView:OnFlush()
    self:FlushAllPanel()
end

function TripleRechargeView:FlushAllPanel()
    local cfg = TripleRechargeWGData.Instance:GetCurGradeCfgBySeq(self.select_index)
    if IsEmptyTable(cfg) then
        return
    end

    local reward_list = SortTableKey(cfg.show_lingyu)

    for i = 1, REWARD_COUNT do
        if i <= #reward_list then
            self.node_list["reward" .. i]:SetActive(true)
            self.reward_list[i]:SetData(reward_list[i])
        else
            self.node_list["reward" .. i]:SetActive(false)
        end
    end

    self.reward_cell:SetData(cfg.reward_item[0])
    self.node_list.buy_text.text.text = string.format(Language.Common.MoneyTypes[0], cfg.price)
end

function TripleRechargeView:OnClickBtnHandler(index)
	if self.select_index == index then
		return
	end

	self.select_index = index

	for k, v in pairs(self.btn_list) do
		v:SetSelect(k == index)
	end

    self:FlushAllPanel()
end

function TripleRechargeView:OnClickBuyBtn()
    local cfg = TripleRechargeWGData.Instance:GetCurGradeCfgBySeq(self.select_index)
    if IsEmptyTable(cfg) then
        return
    end

    RechargeWGCtrl.Instance:Recharge(cfg.price, cfg.rmb_type, cfg.rmb_seq)
end

-- function TripleRechargeView:UpdateTimeStr(time)
--     if self.node_list["act_txt"] then
--         self.node_list["act_txt"].text.text = string.format(Language.BiZuo.Act_Time_Segment, TimeUtil.FormatSecondDHM9(time))
--     end
-- end

-- function TripleRechargeView:CleanActTimer()
--     if self.act_timer and CountDown.Instance:HasCountDown(self.act_timer) then
--         CountDown.Instance:RemoveCountDown(self.act_timer)
--         self.act_timer = nil
--     end
-- end

------------ 档位 ------------
TripleRechargeBtnRender = TripleRechargeBtnRender or BaseClass(BaseRender)
function TripleRechargeBtnRender:OnFlush()
    if not self.data then
        self.view:SetActive(false)
        return
    end

    self.view:SetActive(true)
    self.node_list.num_text.text.text = self.data.add_lingyu
    self.node_list.num_text_hl.text.text = self.data.add_lingyu
end

function TripleRechargeBtnRender:SetSelect(bool)
    self.node_list.content_nor:SetActive(not bool)
	self.node_list.content_hl:SetActive(bool)
end