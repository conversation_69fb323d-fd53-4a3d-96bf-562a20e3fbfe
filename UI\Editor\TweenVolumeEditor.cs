﻿using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(UGUITweenVolume))]
public class TweenVolumeEditor : UITweenerEditor 
{

	public override void OnInspectorGUI ()
	{
		GUILayout.Space(6f);
		UGUITweenEditorTools.SetLabelWidth(120f);

		UGUITweenVolume tw = target as UGUITweenVolume;
		GUI.changed = false;

		float from = EditorGUILayout.Slider("From", tw.from, 0f, 1f);
		float to = EditorGUILayout.Slider("To", tw.to, 0f, 1f);

		if (GUI.changed)
		{
			UGUITweenEditorTools.RegisterUndo("Tween Change", tw);
			tw.from = from;
			tw.to = to;
			UGUITweenEditorTools.SetDirty(tw);
		}

		DrawCommonProperties();
	}
}
