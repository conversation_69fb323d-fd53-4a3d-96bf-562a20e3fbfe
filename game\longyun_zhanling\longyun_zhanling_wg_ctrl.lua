require("game/longyun_zhanling/longyun_zhanling_wg_data")
require("game/longyun_zhanling/longyun_zhanling_task_view")
require("game/longyun_zhanling/longyun_zhanling_view")
require("game/longyun_zhanling/longyun_unlock_zhanling_view")
require("game/longyun_zhanling/longyun_buy_zhanling_level_view")

LongYunZhanLingWGCtrl = LongYunZhanLingWGCtrl or BaseClass(BaseWGCtrl)

function LongYunZhanLingWGCtrl:__init()
	if nil ~= LongYunZhanLingWGCtrl.Instance then
		ErrorLog("[LongYunZhanLingWGCtrl]:Attempt to create singleton twice!")
	end
	LongYunZhanLingWGCtrl.Instance = self

	self.data = LongYunZhanLingWGData.New()
	self.task_view = LongYunZhanLingTaskView.New(GuideModuleName.LongYunZhanLingTaskView)
	self.zhanling_view = LongYunZhanLingView.New(GuideModuleName.LongYunZhanLingView)
	self.unlock_zhanling_view = LongYunUnlockZhanLingView.New()
	self.buy_zhanling_level_view = LongYunBuyZhanLingLevelView.New()

	self:RegisterAllProtocols()

	self.money_info = {
		devote = 40033, -- 贡献
		fame = 40032, -- 声望
	}

	self.is_first_rember = true -- 首次记录贡献和声望
end

function LongYunZhanLingWGCtrl:__delete()
	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.task_view ~= nil then
		self.task_view:DeleteMe()
		self.task_view = nil
	end

	if self.zhanling_view ~= nil then
		self.zhanling_view:DeleteMe()
		self.zhanling_view = nil
	end

	if self.unlock_zhanling_view ~= nil then
		self.unlock_zhanling_view:DeleteMe()
		self.unlock_zhanling_view = nil
	end

	if self.buy_zhanling_level_view ~= nil then
		self.buy_zhanling_level_view:DeleteMe()
		self.buy_zhanling_level_view = nil
	end

	LongYunZhanLingWGCtrl.Instance = nil

	self.is_first_rember = nil
	self.money_info = nil
end

-- 协议注册
function LongYunZhanLingWGCtrl:RegisterAllProtocols()
	-- 操作.
	self:RegisterProtocol(CSCountryOperate, "SendCSCountryOperate")
	-- 基础信息.
	self:RegisterProtocol(SCCountryBaseInfo, "OnSCCountryBaseInfo")
	-- 任务信息.
	self:RegisterProtocol(SCCountryTaskInfo, "OnSCCountryTaskInfo")
	-- 任务更新.
	self:RegisterProtocol(SCCountryTaskUpdate, "OnSCCountryTaskUpdate")
	-- 战令信息.
	self:RegisterProtocol(SCCountryOrderInfo, "OnSCCountryOrderInfo")
end

function LongYunZhanLingWGCtrl:SendCSCountryOperate(opera, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCountryOperate)
	protocol.op_type = opera or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function LongYunZhanLingWGCtrl:OnSCCountryBaseInfo(protocol)
	-- print_error("----------------OnSCCountryBaseInfo基础信息---------------", protocol)
	if not self.is_first_rember then
		local temp_money_info = {
			devote = self.data:GetLongYunZhanLingDevote(),
			fame = self.data:GetLongYunZhanLingFame(),
		}

		for k, v in pairs(self.money_info) do
			local item_cfg = ItemWGData.Instance:GetItemConfig(v)
			if item_cfg then
				if protocol[k] and temp_money_info[k] and protocol[k] > temp_money_info[k] then
					local str = string.format(Language.Bag.GetItemTxt, item_cfg.name, protocol[k] - temp_money_info[k])
					SysMsgWGCtrl.Instance:ErrorRemind(str)
				end
			end
		end
	end
	self.is_first_rember = false

	self.data:SetCountryRoleFameInfo(protocol)
	local main_role = Scene.Instance:GetMainRole()
	main_role:SetAttr("fame", protocol.fame)

	if self.task_view:IsOpen() then
		self.task_view:Flush(0, "info")
	end

	RemindManager.Instance:Fire(RemindName.LongYunZhanLingTask)
end

function LongYunZhanLingWGCtrl:OnSCCountryTaskInfo(protocol)
	-- print_error("----------------OnSCCountryTaskInfo任务信息---------------", protocol)
	self.data:SetCountryAllTaskValeuInfo(protocol)

	RemindManager.Instance:Fire(RemindName.LongYunZhanLingTask)
	ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView, nil, "flush_cell")

	if self.task_view:IsOpen() then
		self.task_view:Flush()
	end
end

function LongYunZhanLingWGCtrl:OnSCCountryTaskUpdate(protocol)
	-- print_error("----------------OnSCCountryTaskUpdate任务更新---------------", protocol)
	self.data:SetCountryCurTaskValueInfo(protocol)

	RemindManager.Instance:Fire(RemindName.LongYunZhanLingTask)
	ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView, nil, "flush_cell")

	if self.task_view:IsOpen() then
		self.task_view:Flush()
	end

	if self.zhanling_view:IsOpen() then
		self.zhanling_view:Flush()
	end

	if self.buy_zhanling_level_view:IsOpen() then
		self.buy_zhanling_level_view:Flush()
	end

	if self.unlock_zhanling_view:IsOpen() then
		self.unlock_zhanling_view:Flush()
	end
end

function LongYunZhanLingWGCtrl:OnSCCountryOrderInfo(protocol)
	-- print_error("----------------OnSCCountryOrderInfo战令信息---------------", protocol)
	self.data:SetLongYunZhanLingInfo(protocol)

	if self.task_view:IsOpen() then
		self.task_view:Flush()
	end

	if self.zhanling_view:IsOpen() then
		self.zhanling_view:Flush()
	end

	if self.buy_zhanling_level_view:IsOpen() then
		self.buy_zhanling_level_view:Flush()
	end

	if self.unlock_zhanling_view:IsOpen() then
		self.unlock_zhanling_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.LongYunZhanLingTask)
end

function LongYunZhanLingWGCtrl:OpenZhanLingView()
	if self.zhanling_view then
		self.zhanling_view:Open()
	end
end

function LongYunZhanLingWGCtrl:OpenUnLockZhanLingView()
	if self.unlock_zhanling_view then
		self.unlock_zhanling_view:Open()
	end
end

function LongYunZhanLingWGCtrl:OpenBugZhanLingLevelView()
	if self.buy_zhanling_level_view then
		self.buy_zhanling_level_view:Open()
	end
end
