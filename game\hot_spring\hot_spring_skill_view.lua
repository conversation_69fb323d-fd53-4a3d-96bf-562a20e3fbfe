HotSpringSkillView = HotSpringSkillView or BaseClass(SafeBaseView)

local SKILL_SNOW = "skill_snow_hot_spring" 	--扔雪球
local SKILL_WATER = "skill_water_hot_spring"	--泼水

local TIMER = 0.05 			--每次刷新的间隔

local SKILL_SNOW_NUM = 1
local SKILL_WATER_NUM = 2

function HotSpringSkillView:__init()
	self.active_close = false
	self:AddViewResource(0, "uis/view/hot_spring_ui_prefab", "layout_skill")
end

function HotSpringSkillView:__delete()
	self.anmo_cd_time = 0
	self.intimidation_cd_time = 0
end

function HotSpringSkillView:ReleaseCallBack()
	if self.skill_render then
		self.skill_render:SetInstanceParent(self.root_node_transform)
		self.skill_render:DeleteMe()
		self.skill_render = nil
    end
    if CountDownManager.Instance:HasCountDown(SKILL_SNOW) then
		CountDownManager.Instance:RemoveCountDown(SKILL_SNOW)
    end
    if CountDownManager.Instance:HasCountDown(SKILL_WATER) then
		CountDownManager.Instance:RemoveCountDown(SKILL_WATER)
	end
end

function HotSpringSkillView:CloseCallBack()
	self.node_list.root_skill:SetActive(false)
end

function HotSpringSkillView:LoadCallBack()
	self.anmo_cd_time = 0
	self.intimidation_cd_time = 0
	local callback = function ()
			local parent = MainuiWGCtrl.Instance:GetSkillContent()
			self.node_list.root_skill:SetActive(true)
			if parent then
				self.skill_render = BaseRender.New(self.node_list.root_skill)
				self.skill_render:SetInstanceParent(parent)
			else
				print_error("can not find the parent!!")
			end
			for i = 0, 3 do
				self.node_list["layout_skill_item_" .. i].button:AddClickListener(BindTool.Bind(self.ShowNearRoleList, self,i))
			end

			self.is_load_finish = true
			self:Flush()
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,callback)
end

function HotSpringSkillView:OnFlush()
	local other_cfg = HotSpringWGData.Instance:GetHotSpringOtherCfg()
	if other_cfg.game_day_max_times then
		local anmo_times, intimidation_times, anmo_cd_time, intimidation_cd_time = HotSpringWGData.Instance:GetHotspringPlayerInfo()
		local color = ""

		-- 按摩剩余次数
		do
			local anmo_times_num = other_cfg.game_day_max_times - anmo_times
			anmo_times_num = math.max(anmo_times_num, 0)
			if anmo_times_num <= 0 then
				XUI.SetGraphicGrey(self.node_list.img_skill_1, true)
				-- self.node_list["layout_skill_item_1"].button.interactable = false
				--color = COLOR3B.RED
			else
				XUI.SetGraphicGrey(self.node_list.img_skill_1, false)
				-- self.node_list["layout_skill_item_1"].button.interactable = true
				--color = COLOR3B.GREEN
			end
			--print_error("剩余数量显示：", anmo_times_num, anmo_times, other_cfg.game_day_max_times )
			self.node_list.lbl_skill_num_1.text.text = anmo_times_num--ToColorStr(anmo_times_num,color)
		end

		-- 恐吓剩余次数
		do
			local intimidation_times_num = other_cfg.game_day_max_times - intimidation_times
			intimidation_times_num = math.max(intimidation_times_num, 0)
			if intimidation_times_num <= 0 then
				XUI.SetGraphicGrey(self.node_list.img_skill_2, true)
				-- self.node_list["layout_skill_item_2"].button.interactable = false
				--color = COLOR3B.RED
			else
				XUI.SetGraphicGrey(self.node_list.img_skill_2, false)
				-- self.node_list["layout_skill_item_2"].button.interactable = true
				--color = COLOR3B.GREEN
			end
			self.node_list.lbl_skill_num_2.text.text = intimidation_times_num--ToColorStr(intimidation_times_num, color)
		end

		--采集剩余次数
		do
			local gather_time = other_cfg.gather_role_count - HotSpringWGData.Instance:GetGatherInfo()
			gather_time = math.max(gather_time, 0)
			if gather_time <= 0 then
				XUI.SetGraphicGrey(self.node_list.img_skill_3, true)
				-- self.node_list["layout_skill_item_3"].button.interactable = false
				--color = COLOR3B.RED
			else
				XUI.SetGraphicGrey(self.node_list.img_skill_3, false)
				-- self.node_list["layout_skill_item_3"].button.interactable = true
				--color = COLOR3B.GREEN
			end
			self.node_list.lbl_skill_num_3.text.text = gather_time--ToColorStr(gather_time,color)
		end


		local count_indeance = CountDownManager.Instance
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if anmo_cd_time > server_time then
			self.anmo_cd_time = anmo_cd_time
			self:SetCDMaskShow(SKILL_SNOW_NUM,true)
			if not count_indeance:HasCountDown(SKILL_SNOW) then
				count_indeance:AddCountDown(SKILL_SNOW, BindTool.Bind(self.UpdateSkillCD1, self),
					BindTool.Bind(self.CompleteSkillCD, self, SKILL_SNOW_NUM), anmo_cd_time, nil, TIMER)
			end
		end

		if intimidation_cd_time > server_time then
			self.intimidation_cd_time = intimidation_cd_time
			self:SetCDMaskShow(SKILL_WATER_NUM,true)
			if not count_indeance:HasCountDown(SKILL_WATER) then
				count_indeance:AddCountDown(SKILL_WATER, BindTool.Bind(self.UpdateSkillCD2, self),
					BindTool.Bind(self.CompleteSkillCD, self, SKILL_WATER_NUM), intimidation_cd_time, nil, TIMER)
			end
		end
	end
end


--计算cd
function HotSpringSkillView:UpdateSkillCD1(elapse_time,total_time)
	self:SetCDMaskVal(SKILL_SNOW_NUM,(1 - elapse_time / total_time))
	-- self:SetCDText(SKILL_SNOW_NUM,string.format("%0.1f", (1 - elapse_time / total_time)))
		self:SetCDText(SKILL_SNOW_NUM,string.format("%0.1f", (total_time - elapse_time)))
end

function HotSpringSkillView:UpdateSkillCD2(elapse_time,total_time)
	self:SetCDMaskVal(SKILL_WATER_NUM,(1 - elapse_time / total_time))
	-- self:SetCDText(SKILL_WATER_NUM,string.format("%0.1f", (1 - elapse_time / total_time)))
	self:SetCDText(SKILL_WATER_NUM,string.format("%0.1f", (total_time - elapse_time)))
end

function HotSpringSkillView:CompleteSkillCD(index)
	if index == 1 then
		self.anmo_cd_time = 0
	elseif index == 2 then
		self.intimidation_cd_time = 0
	end

	self:SetCDMaskShow(index,false)
end

function HotSpringSkillView:SetCDMaskVal(index,val)
    if self.node_list["img_skill_mark_" .. index] then
        self.node_list["img_skill_mark_" .. index].image.fillAmount = val
    end
end

function HotSpringSkillView:SetCDMaskShow(index,enable)
    if self.node_list["img_skill_mark_" .. index] then
        self.node_list["img_skill_mark_" .. index]:SetActive(enable)
    end
    if self.node_list["img_skill_text_" .. index] then
        self.node_list["img_skill_text_" .. index]:SetActive(enable)
    end
end

function HotSpringSkillView:SetCDText( index,val )
    if self.node_list["img_skill_text_" .. index] then
        self.node_list["img_skill_text_" .. index].text.text = val
    end
end

function HotSpringSkillView:UnBindMoveEvent()
	if nil ~= self.move_event then
		GlobalEventSystem:UnBind(self.move_event)
		self.move_event = nil
	end
end

function HotSpringSkillView:ShowNearRoleList(param)
    local act_info
	if not IS_ON_CROSSSERVER then
		act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HOTSPRING)
	else
		act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HOTSPRING)
	end

	if act_info and act_info.status == ACTIVITY_STATUS.STANDY then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi2) --活动尚未开始
		return
	end
	local main_role = Scene.Instance:GetMainRole()
	if main_role.vo.anmo_state == ANMO_TYPE.IS_ANMO then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip1_2)
		return
	end
	if main_role.vo.shuangxiu_state == 1 then
		local role_2 = Scene.Instance:GetObjectByObjId(main_role.vo.shuangxiu_partner_obj)
		local name = role_2 and role_2.vo.name or ""
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.HotSpring.ShuangXiuTip_1, name))
		return
	end
	local anmo_times, intimidation_times, anmo_cd_time, intimidation_cd_time = HotSpringWGData.Instance:GetHotspringPlayerInfo()
	local other_cfg = HotSpringWGData.Instance:GetHotSpringOtherCfg()
	if param == 1 then
		local snow_times_num = other_cfg.game_day_max_times - anmo_times
		if snow_times_num <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.NoEnoughTime)
			return
		end
		if self.anmo_cd_time > 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.SkillCD)
			return
		end
	elseif param == 2 then
		local intimidation_times_num = other_cfg.game_day_max_times - intimidation_times
		if intimidation_times_num <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.NoEnoughTime)
			return
		end
		if self.intimidation_cd_time > 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.SkillCD)
			return
		end
	elseif param == 3 then
		local gather_time = other_cfg.gather_role_count - HotSpringWGData.Instance:GetGatherInfo()
		if gather_time <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.NoEnoughTime)
			return
		end
	end
    if param == 3 then --采集
        self:OnSkillBtnHandler(param)
    else
        HotSpringWGData.Instance:SetCurOperType(param)
        HotSpringWGCtrl.Instance:OpenRoleListView()
    end
end

--双修，按摩，泼热水, 采集 0-3
function HotSpringSkillView:OnSkillBtnHandler(param)
    local main_role = Scene.Instance:GetMainRole()
	local x, y = main_role:GetLogicPos()
	local target_obj = main_role.select_obj

    if main_role.vo.anmo_state == ANMO_TYPE.IS_ANMO then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip1_5)
        return
    end

    if nil == target_obj or not target_obj:IsGather() then
        local distance_limit = COMMON_CONSTS.SELECT_OBJ_HOTSPRING * COMMON_CONSTS.SELECT_OBJ_HOTSPRING
        target_obj = Scene.Instance:SelectNearGather(distance_limit)
    end

    if nil == target_obj then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.SelectTarget2)
        return
    end
	
	local tab = {target_obj = target_obj, main_role = main_role,param = param}
	local target_x, target_y = target_obj:GetLogicPos()

	local distance = GameMath.GetDistance(x, y, target_x, target_y, true)

	local juli = 0
	if param ~= 0 and param ~= 1 then
		juli = 14
	end
	if param == 3 then
		juli = 3
		target_obj:OnClicked()
	end

	if math.floor(distance) > juli then
		self:UnBindMoveEvent()
			local sence_id = Scene.Instance:GetSceneId()
			local ctrl = GuajiWGCtrl.Instance

			ctrl:SetMoveToPosCallBack(
				function ()
					self:OnMainRoleMoveEnd(tab)
    			end)
			ctrl:MoveToPos(sence_id,target_x, target_y, juli)
	else
		self:OnMainRoleMoveEnd(tab)
	end
end

function HotSpringSkillView:OnMainRoleMoveEnd(tab)
	self:UnBindMoveEvent()
	if nil == tab or nil == tab.param or nil == tab.main_role or nil == tab.target_obj then
		return
	end
	local param = tab.param
	local main_role = tab.main_role
	local target_obj = tab.target_obj

	if nil == target_obj or target_obj.vo == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.SelectTarget)
		return
	end

	if main_role.vo.is_in_snow or main_role.vo.is_in_hot_water then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.StateYunXuan)
		return
	end

	if target_obj.vo and (target_obj.vo.is_in_snow or target_obj.vo.is_in_hot_water) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.StateYunXuanTarget)
		return
	end

	--
	if param == 0 then
		if main_role.vo.anmo_state == ANMO_TYPE.IS_ANMO then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip1_2)
			return
		end
		HotSpringWGCtrl.Instance:SendHotspringAction(target_obj.vo.obj_id, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_SHUANGXIU)
		-- if target_obj.vo and target_obj.vo.is_in_hot_spring and main_role.vo.is_in_hot_spring then
		-- -- if target_obj.vo and  main_role.vo.is_in_hot_spring then
		-- 	HotSpringWGCtrl.Instance:SendHotspringAction(target_obj.vo.obj_id, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_SHUANGXIU)
		-- elseif not main_role.vo.is_in_hot_spring then
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.MainRoleNotInHotSpring)
		-- else
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.TargetRoleNotInHotSpring)
		-- end
	elseif param == 1 then
		if main_role.vo.shuangxiu_state == 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack)
			return
		end
		if main_role.vo.anmo_state == ANMO_TYPE.IS_ANMO then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip1_2)
			return
		end


		if target_obj.vo and target_obj.vo.is_in_hot_spring and main_role.vo.is_in_hot_spring then
			HotSpringWGCtrl.Instance:SendHotspringAction(target_obj.vo.obj_id, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_ANMO)
		elseif not main_role.vo.is_in_hot_spring then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip)
		else
			--SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotThrowBallTargetInSpring)
		end
	elseif param == 2 then
		if main_role.vo.shuangxiu_state == 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack)
			return
		end
		if main_role.vo.anmo_state == ANMO_TYPE.IS_ANMO then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack2)
			return
		end
		if target_obj.vo and  target_obj.vo.anmo_state == ANMO_TYPE.IS_ANMO then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack4)
			return
		end
		-- CanNotAttack4
		if target_obj.vo and  target_obj.vo.shuangxiu_state == SHUANGXIU_TYPE.IS_SHUANGXIU then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack3)
			return
		end

		if target_obj.vo and target_obj.vo.is_in_hot_spring and main_role.vo.is_in_hot_spring then
			HotSpringWGCtrl.Instance:SendHotspringAction(target_obj.vo.obj_id, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_INTIMIDATION)
		elseif not main_role.vo.is_in_hot_spring then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.MainRoleNotInHotSpring)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotSplashHotWater)
		end
	elseif param == 3 then
		if main_role.vo.anmo_state == ANMO_TYPE.IS_ANMO then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack2)
			return
		end
		GuajiWGCtrl.Instance:OnOperateGather()
	end
	Scene.Instance:GetMainRole():ChangeToCommonState()
end


-- 选取最近的对象
function HotSpringSkillView:SelectObjHelperFilter(obj_type, x, y, distance_limit, select_type, ignore_table, fitter_callback)
    -- print_error("SelectObjHelper",obj_type, x, y, distance_limit, select_type, ignore_table)
    local obj_list = Scene.Instance:GetObjListByType(obj_type)
    -- print_error("SelectObjHelper obj_list ",obj_list)
    local target_obj = nil
    local target_distance = distance_limit
    local target_x, target_y, distance = 0, 0, 0
    local can_select = true
    local target_obj_reserved = nil
    local target_distance_reserved = distance_limit

    for _, v in pairs(obj_list) do
        if v:IsCharacter() and not v:IsPerformer() then
            can_select = true
            if SelectType.Friend == select_type and not v:IsMainRole() then
                can_select = self.scene_logic:IsFriend(v, self.main_role)
            elseif SelectType.Enemy == select_type then
                can_select = self.scene_logic:IsEnemy(v, self.main_role, ignore_table)
            end

            if can_select then
                target_x, target_y = v:GetLogicPos()
                local r_x, r_y = v:GetRealPos()
                -- print_error("rxy",r_x,r_y)
                distance = GameMath.GetDistance(x, y, target_x, target_y, false)
                -- print_error("distance",distance)

                local isBlock = AStarFindWay:IsBlock(target_x, target_y)
                local fitter_condition = fitter_callback and fitter_callback(v, target_x, target_y) or nil
                -- 优先寻找非障碍区的
                if not isBlock then
                	if fitter_condition ~= nil then
                		if fitter_condition == 1 then
                			if distance < target_distance then
		                        target_obj = v
		                        target_distance = distance
		                    end
                		end
	                else
	                   	if distance < target_distance then
	                        target_obj = v
	                        target_distance = distance
	                    end
                	end
                else
                    if distance < target_distance_reserved then
                        target_obj_reserved = v
                        target_distance_reserved = distance
                    end
                end
            end
        end
    end

    if nil == target_obj then
        return target_obj_reserved, target_distance_reserved
    end

    return target_obj, target_distance
end
