XianjieData = XianjieData or BaseClass()
function XianjieData:__init()
	if XianjieData.Instance then
		ErrorLog("[XianjieData] Attemp to create a singleton twice !")
	end
	XianjieData.Instance = self
	self.shengwang_info = {
		xianjie_level = 0,
		xiandan_level = 0,
		process_times = 0,
		need_gold_on_uplevel_next_xiandan = 0,
	}

	self.shuxingdanlist ={}
end

function XianjieData:__delete()
	XianjieData.Instance = nil
end

function XianjieData:SetShuxingdanlist(list)
	self.shuxingdanlist = list
end

function XianjieData:GetShuxingdanlist()
	return self.shuxingdanlist
end

function XianjieData:SetShengwangInfo(info)
	self.shengwang_info.xianjie_level = info.xianjie_level
	self.shengwang_info.xiandan_level = info.xiandan_level
	self.shengwang_info.process_times = info.process_times
	self.shengwang_info.need_gold_on_uplevel_next_xiandan = info.need_gold_on_uplevel_next_xiandan
end

function XianjieData:GetShengwangInfo()
	return self.shengwang_info
end

function XianjieData.GetMaxXianJieLv()
	return #ConfigManager.Instance:GetAutoConfig("shengwang_auto").xianjie
end

function XianjieData.GetXianJieCfgByLv(lv)
	local xianjie_cfg = ConfigManager.Instance:GetAutoConfig("shengwang_auto").xianjie
	return xianjie_cfg[lv]
end

function XianjieData.GetXianDanAttrByLevel(lv, process_times, is_once)
	local xiandan_cfg = ConfigManager.Instance:GetAutoConfig("shengwang_auto").xiandan
	local attri = AttributePool.AllocAttribute()
	if not is_once then
		for k,v in pairs(xiandan_cfg) do
			if v.level <= lv + 1 then
				local cfg = AttributePool.AllocAttribute()
				local times = v.max_times
				if v.level > lv then
					times = math.min(process_times, v.max_times)
				end
				cfg.max_hp = v.once_add_maxhp * times
				cfg.gong_ji = v.once_add_gongji * times
				cfg.fang_yu = v.once_add_fangyu * times
				cfg.ming_zhong = v.once_add_mingzhong * times				
				attri = AttributeMgr.AddAttributeAttr(attri, cfg)
			end
		end
	else
		if xiandan_cfg[lv + 1] and process_times <= xiandan_cfg[lv + 1].max_times then
			attri.max_hp = xiandan_cfg[lv + 1].once_add_maxhp
			attri.gong_ji = xiandan_cfg[lv + 1].once_add_gongji
			attri.fang_yu = xiandan_cfg[lv + 1].once_add_fangyu
			attri.ming_zhong = xiandan_cfg[lv + 1].once_add_mingzhong
		end
	end
	AttributeMgr.AttributeAddProfRate(attri)
	return attri
end

function XianjieData.GetXiandanCfgByLv(lv)
	local xianjie_cfg = ConfigManager.Instance:GetAutoConfig("shengwang_auto").xiandan
	return xianjie_cfg[lv]
end

function XianjieData.GetMaxXiandanLv()
	return #ConfigManager.Instance:GetAutoConfig("shengwang_auto").xiandan
end

function XianjieData.GetMaxXiandanCostGoldByLv(lv)
	for i,v in ipairs(ConfigManager.Instance:GetAutoConfig("shengwang_auto").xiandan_gold) do
		if v.times_min <= lv and v.times_max >= lv then
			return v.gold
		end
	end
	return 0
end

-- 获取可升仙阶提示
function XianjieData:GetXianjieRemind()
	local count = 0
	local xianjie_cfg = self.GetXianJieCfgByLv(self.shengwang_info.xianjie_level + 1)
	if xianjie_cfg and xianjie_cfg.shengwang <= RoleWGData.Instance.role_vo.shengwang then
		count = 1
	end
	return count
end