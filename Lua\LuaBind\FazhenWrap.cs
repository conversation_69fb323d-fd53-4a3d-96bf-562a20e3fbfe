﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FazhenWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Fazhen), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("SetSize", SetSize);
		<PERSON><PERSON>RegFunction("SetRotateY", SetRotateY);
		<PERSON><PERSON>RegFunction("SetInitSpeed", SetInitSpeed);
		<PERSON><PERSON>unction("Play", Play);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Fazhen obj = (Fazhen)ToLua.CheckObject(L, 1, typeof(Fazhen));
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.SetSize(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRotateY(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Fazhen obj = (Fazhen)ToLua.CheckObject(L, 1, typeof(Fazhen));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetRotateY(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetInitSpeed(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Fazhen obj = (Fazhen)ToLua.CheckObject(L, 1, typeof(Fazhen));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetInitSpeed(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			Fazhen obj = (Fazhen)ToLua.CheckObject(L, 1, typeof(Fazhen));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.Play(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

