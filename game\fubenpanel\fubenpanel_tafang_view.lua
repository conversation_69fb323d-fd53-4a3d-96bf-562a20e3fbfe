FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)

--六道轮回(塔防本)
function FuBenPanelView:InittafangView()
	self.tafang_fb_cells = {}
	self.node_list["btn_tafang_buy"].button:AddClickListener(BindTool.Bind1(self.OnClickTaFangCount, self))
	self.node_list["btn_tafang_saodang"].button:AddClickListener(BindTool.Bind1(self.OnClinkTfSaoDangHand<PERSON>, self))
	self.node_list["btn_enter_tafangfb"].button:AddClickListener(BindTool.Bind1(self.GoToTaFangFb, self))
	self.is_can_enter_tafang_fb = false
	self:CreateTaFangFbCells()
	self.day_count_change = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DaycountChange, self))
end

function FuBenPanelView:DeleteTfFuBenPanelView()
	if self.tafang_fb_cells then
		for k,v in pairs(self.tafang_fb_cells) do
			v:DeleteMe()
		end
		self.tafang_fb_cells = nil
	end

	if self.day_count_change then
		GlobalEventSystem:UnBind(self.day_count_change)
		self.day_count_change = nil
	end

	self.tafang_btn_effect = nil
	self.is_can_enter_tafang_fb = false
end

function FuBenPanelView:CreateTaFangFbCells()
	for i = 0, 3 do
		if not self.tafang_fb_cells[i] then
			local ph = self.node_list["ph_tafang_cell_" .. i]
			local tafang_cell = ItemCell.New(ph)
			self.tafang_fb_cells[i] = tafang_cell
		end
	end

end

function FuBenPanelView:OnClickTaFangCount()
	FuBenPanelWGCtrl.Instance:OpenTaFangBuy()
end

function FuBenPanelView:OnClinkTfSaoDangHandler()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local limit_level = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg().sweep_level
	if role_level < limit_level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FuBenPanel.FunNotOpen, limit_level))
		-- self:OnClickTaFangCount()
	elseif self.enter_tf_times <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
	else
		FuBenPanelWGCtrl.Instance:OpenTaFangSweepMsg()
	end
end

function FuBenPanelView:GoToTaFangFb()
	if self.enter_tf_times > 0 then
		--if self.is_can_enter_tafang_fb then  return end
		self.is_can_enter_tafang_fb = true
		FuBenWGCtrl.Instance:SendTaFangFb()
		--self:Close()
	else
		--print_error("哪里进的这里")

		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
		self:OnClickTaFangCount()
	end
end

function FuBenPanelView:OnFlushTFView()
	local tf_cfg_other = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()
	if tf_cfg_other then
		for k,v in pairs(self.tafang_fb_cells) do
			if not tf_cfg_other.reward_item[k] then
				self.tafang_fb_cells[k]:SetVisible(false)
			else
				self.tafang_fb_cells[k]:SetVisible(true)

			end 
			v:SetData(tf_cfg_other.reward_item[k])
		end


		local des = string.format(Language.FuBenPanel.FbLevelLimitTips, tf_cfg_other.enter_level)
		
		self.node_list["rich_tafang_des_1"].text.text = des
		self.node_list["rich_tafang_des_2"].text.text = Language.FuBenPanel.TaFangFbTips2


		local tf_fb_buy_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
		local tf_fb_join_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_ENTER_TIMES) or 0
		self.enter_tf_times = tf_cfg_other.enter_free_times + tf_fb_buy_num - tf_fb_join_num
		
		if self.enter_tf_times > 0 then
			self.node_list["lbl_tafang_count"].text.text = self.enter_tf_times .. "/" .. tf_cfg_other.enter_free_times + tf_fb_buy_num
		else
			self.node_list["lbl_tafang_count"].text.text = string.format(Language.FuBenPanel.FuBenEnterCount,self.enter_tf_times,tf_cfg_other.enter_free_times + tf_fb_buy_num)
		end

		


		
	end

	
	self.node_list["img_tafang_remind"]:SetActive(false)
	self.node_list["img_ta_fang_stamp"]:SetActive(FuBenPanelWGData.Instance:GetIsDoubleDrop(DOUBLE_DROP_FB_TYPE.TAFANG_FB))


	self:TafangAddCountBtnEffect()
	self:FlushTaFangEffect()
end

function FuBenPanelView:FlushTaFangEffect()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyTfCountCfg()
	local day_buy_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
	local have_count = vip_buy_cfg["param_" .. role_vip] - day_buy_times
	self.node_list.tower_effect:SetActive(have_count > 0)
end

function FuBenPanelView:DaycountChange(day_counter_id)
	if day_counter_id == -1 or day_counter_id == DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_ENTER_TIMES 
		or day_counter_id == DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES then
		self:Flush(TabIndex.fubenpanel_tafang)
		if FuBenPanelWGCtrl.Instance.tafang_buy:IsOpen() then
			FuBenPanelWGCtrl.Instance.tafang_buy:Flush()
		end
	end
end

function FuBenPanelView:CheckTaFangCount()   -- 判断塔防本有无进入次数
	local tf_cfg_other = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()
	local tf_fb_buy_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
	local tf_fb_join_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_ENTER_TIMES) or 0
	local enter_tf_times = tf_cfg_other.enter_free_times + tf_fb_buy_num - tf_fb_join_num

	local role_level = RoleWGData.Instance.role_vo.level
	local tf_cfg_other = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()

	return role_level >= tf_cfg_other.enter_level and enter_tf_times > 0 
end

function FuBenPanelView:TafangAddCountBtnEffect()
	
end