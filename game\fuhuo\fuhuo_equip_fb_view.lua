FuhuoEquipFbView = FuhuoEquipFbView or BaseClass(SafeBaseView)

function FuhuoEquipFbView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(false,true)
	self.is_modal = true
	self.active_close = false
	self:AddViewResource(0, "uis/view/fuhuo_ui_prefab", "layout_fuhuo_fb2")
end

function FuhuoEquipFbView:__delete()

end

function FuhuoEquipFbView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("fuhuo_time") then
		CountDownManager.Instance:RemoveCountDown("fuhuo_time")
	end

	if self.fuhuo_time then
		self.fuhuo_time:DeleteMe()
		self.fuhuo_time = nil
	end
end

function FuhuoEquipFbView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("fuhuo_time") then
		CountDownManager.Instance:RemoveCountDown("fuhuo_time")
	end
end

function FuhuoEquipFbView:LoadCallBack()
	
end

function FuhuoEquipFbView:ShowIndexCallBack()
	-- self:Flush()
end

function FuhuoEquipFbView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "daojishi" then
			self.fb_time = v.time or 5
			self.node_list["fuhuo_time_slider"].image.fillAmount = 1
			self:UpdateTime(0, self.fb_time)
			if CountDownManager.Instance:HasCountDown("fuhuo_time") then
				CountDownManager.Instance:RemoveCountDown("fuhuo_time")
			end

			CountDownManager.Instance:AddCountDown("fuhuo_time", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), nil, self.fb_time, 0.02)
		end
	end
end

function FuhuoEquipFbView:OpenEquipFbFuhuo()
end

function FuhuoEquipFbView:UpdateTime(elapse_time, total_time)
	self.node_list["text_progress1"].text.text = math.ceil(total_time - elapse_time)
	local main_role = Scene.Instance:GetMainRole()
	if main_role and not main_role:IsRealDead() then
		self:Close()
	end

	if self.node_list["fuhuo_time_slider"] then
		local last_time = math.floor(total_time - elapse_time)
		self.node_list["fuhuo_time_slider"].image.fillAmount = last_time / total_time
	end
end

function FuhuoEquipFbView:CompleteTime()
	self.is_auto = true
	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
	self:Close()
end

function FuhuoEquipFbView:SetKillerName(killer_name)
	self.killer_name = killer_name or ''
	self:Flush()
end

function FuhuoEquipFbView:FuhuoCallback()
	self.is_auto = false
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.YEZHANWANGCHENGFUBEN then
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			main_role:PlayChuShengEffect()
		end
	else
		MainuiWGCtrl.Instance:SetAutoGuaJi(true)
	end
end

function FuhuoEquipFbView:GetUseFuHuoType()
	return FuHuoType.Common
end