require("game/serveractivity/kuafu_consumption/kuafu_consumption_view")
require("game/serveractivity/kuafu_consumption/kuafu_consumption_wg_data")

------------跨服充值排行榜----------------
KuafuConsumptionRankWGCtrl = KuafuConsumptionRankWGCtrl or BaseClass(BaseWGCtrl)

function KuafuConsumptionRankWGCtrl:__init()
	if KuafuConsumptionRankWGCtrl.Instance ~= nil then
		ErrorLog("[KuafuConsumptionRankWGCtrl] attempt to create singleton twice!")
		return
	end
	KuafuConsumptionRankWGCtrl.Instance = self
	self.data = KuafuConsumptionRankWGData.New()
	self.view = KuafuConsumptionRankView.New()

	self:RegisterAllProtocols()
end

function KuafuConsumptionRankWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	KuafuConsumptionRankWGCtrl.Instance = nil
end

function KuafuConsumptionRankWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCrossRAConsumeRankGetRank)											 -- 跨服消费 排行榜请求
	self:RegisterProtocol(SCCrossRAConsumeRankGetRankACK, "OnCrossRAXiaofeiRankAck")			 -- 跨服消费 排行榜信息
	self:RegisterProtocol(SCCrossRAConsumeRankConsuemInfo, "OnCrossRAXiaofeiRankChongzhiInfo")	 -- 跨服消费信息
end

function KuafuConsumptionRankWGCtrl:Open()
	self.view:Open()
end

function KuafuConsumptionRankWGCtrl:OnCrossRAXiaofeiRankAck(protocol)
	self.data:SetCrossRAXiaofeiRankAck(protocol)
	self.view:Flush(0, "kuafu_recharge_rank")
end

function KuafuConsumptionRankWGCtrl:OnCrossRAXiaofeiRankChongzhiInfo(protocol)
	self.data:SetCrossRAChongzhiRankChongzhiInfo(protocol)
	self.view:Flush(0, "recharge_info")
end

function KuafuConsumptionRankWGCtrl:SendCrossRAXiaofeiRankReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossRAConsumeRankGetRank)
	protocol:EncodeAndSend()
end
