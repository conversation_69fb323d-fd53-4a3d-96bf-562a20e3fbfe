function FlowerView:InitFlowerSendPanel()
	self.select_index = -1
	self.cur_num = 1
	self.cur_cell = nil
	self.flower_attr_data = nil

    self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	self.select_friend_callback = BindTool.Bind(self.SelectFrendCallBack, self)

	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell)
	end

	self.keyboard_bind = BindTool.Bind(self.OnChangeNum, self)
	self:SetFlowerItem()
	--self:CreateFriendList() --好友列表
	XUI.AddClickEventListener(self.node_list["btn_open_friend_list"], BindTool.Bind1(self.OnClickFriendList, self))
	XUI.AddClickEventListener(self.node_list["btn_send"], BindTool.Bind1(self.OnClickBtnSend, self))
	XUI.AddClickEventListener(self.node_list["btn_add"], BindTool.Bind1(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list["btn_sub"], BindTool.Bind1(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list["btn_max"], BindTool.Bind1(self.OnClickMax, self))
	XUI.AddClickEventListener(self.node_list["btn_flower_num"], BindTool.Bind1(self.OnInputClickHandler, self))
    XUI.AddClickEventListener(self.node_list.flower_attr_tips_btn, BindTool.Bind(self.OnClickTipsAttrBtn, self))

	for i = 1, 2 do
		self.node_list["toggle_type_"..i].toggle:AddValueChangedListener(BindTool.Bind(self.OnToggleFlowerType, self, i))
	end
end

function FlowerView:ReleaseFlowerSendPanel()
    if self.flower_item then
		self.flower_item:DeleteMe()
		self.flower_item = nil
	end
	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
    end

    if self.send_window then
		self.send_window:DeleteMe()
		self.send_window = nil
    end

	if self.role_loverhead_cell then
		self.role_loverhead_cell:DeleteMe()
		self.role_loverhead_cell = nil
    end

	if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

	if self.flower_items then
        for k, v in pairs(self.flower_items) do
            v:DeleteMe()
        end
        self.flower_items = nil
    end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	self.accepter_data = nil

	self.flower_attr_data = nil
end

function FlowerView:FlushFlowerSendPanel(param_t)
	for k, v in pairs(param_t) do
		if "all" == k then
			if nil ~= self.accepter_data then
				local data = SocietyWGData.Instance:GetFirstFriend(self.accepter_data.gamename)
				self:SetFriendHand(data)
			else
				self.node_list["flower_attr_tips_btn"]:SetActive(false)
				self.node_list["lbl_lover_name"].text.text = Language.Flower.SelectObj
				self.node_list["txt_taraget_charm"].text.text = 0
				self.node_list["txt_taraget_timacy"].text.text = 0
				self.node_list["change_head_img"]:SetActive(false)
				self.node_list["ph_lover_head"]:SetActive(false)
				self.node_list["no_head_img"]:SetActive(true)
				--self.node_list["role_name"].text.text = ""
			end
		
			local role_vo = RoleWGData.Instance:GetRoleVo()
			self.node_list.txt_my_charm.text.text = role_vo.all_charm
		
			if self.cur_cell then
				local item_num = ItemWGData.Instance:GetItemNumInBagById(self.cur_cell.data.item_id)
				self.node_list.item_have_num.text.text = string.format(Language.Flower.HaveItemNun, item_num or 0)
				self.cur_num = 1
				self:OnChangeNum(self.cur_num)
				self:FlushBuyCost()
			end

			if v.flower_type ~= nil then
				for i = 1, 2 do
					self.node_list["toggle_type_" .. i].toggle.isOn = i == v.flower_type
				end
				self:OnToggleFlowerType(v.flower_type, true)
			end

			for i = 1, 2 do
				local remind = MarryWGData.Instance:GetSendFlowersItemRemindByType(i) == 1
				self.node_list["flowe_tog_remind_" .. i]:SetActive(remind)
			end
		end
	end
end

function FlowerView:SetAccepter(data)
	self.accepter_data = data
end

function FlowerView:SetFlowerItem()
	if nil == self.flower_items then
		self.flower_items = {}

		local flower_data = MarryWGData.Instance:GetSendFlowerCfg()
		if IsEmptyTable(flower_data) then return end

		--local num = self.node_list["flower_item_root"].transform.childCount
		for i = 1, 4 do
            local cell = FlowerItemRender.New(self.node_list.flower_item_root:FindObj("flower_item_" .. i))
            --cell:SetSelectCallBack(BindTool.Bind1(self.OnClickSHJBarHandler, self))
            cell:SetIndex(i)
			cell:SetData(flower_data[i])
			cell:SetBtnClickEvent(BindTool.Bind(self.ClickSendHandler, self))
            self.flower_items[i] = cell
        end

		for i = 5, 8 do
			local cell = FlowerItemRender.New(self.node_list.flower_item_root_sp:FindObj("flower_item_" .. i))
			--cell:SetSelectCallBack(BindTool.Bind1(self.OnClickSHJBarHandler, self))
			cell:SetIndex(i)
			cell:SetData(flower_data[i])
			cell:SetBtnClickEvent(BindTool.Bind(self.ClickSendHandler, self))
			self.flower_items[i] = cell
		end
		self:OnToggleFlowerType(1, true)
		--self:ClickSendHandler(self.flower_items[1])
	end
end

function FlowerView:ItemDataChangeCallback(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local flower_data = MarryWGData.Instance:GetSendFlowerCfg()
	if nil == flower_data then return end
	for k,v in pairs(flower_data) do
		if v.item_id == change_item_id then
			self:FlushFlowerItem()
		end
	end
end

function FlowerView:FlushFlowerItem()
	if nil == self.flower_items then return end
	self:Flush()
	for k,v in pairs(self.flower_items) do
		v:Flush()
	end
end

function FlowerView:OnClickAdd()
	if self.cur_num >= 999 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.MaxItemNum)
		return
	end

	if self.cur_cell.data.type == 2 then --活动限定类型
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.cur_cell.data.item_id)
		if self.cur_num >= item_num then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.MaxItemNum)
			return
		end
	end

	self.cur_num = self.cur_num + 1
	self:OnChangeNum(self.cur_num)
end

function FlowerView:OnClickSub()
	if self.cur_num <= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.MinItemNum)
		return
	end

	self.cur_num = self.cur_num - 1
	self:OnChangeNum(self.cur_num)
end

function FlowerView:OnClickMax()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.cur_cell.data.item_id)
	if self.cur_cell.data.type == 1 then -- 普通类型
		self.cur_num = item_num > 0 and item_num or 999

		if item_num <= 0 then
			local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.cur_cell.data.seq)
			local price = 0
			if shop_cfg then
				price = shop_cfg.price
			end

			local gold = RoleWGData.Instance:GetRoleVo().gold
			item_num = math.floor(gold / price)
		end
	end

	self.cur_num = item_num == 0 and 1 or item_num	--背包没有，默认显示1.
	self:OnChangeNum(self.cur_num)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.MaxItemNum)
end

function FlowerView:OnChangeNum(num)
	-- local item_num = ItemWGData.Instance:GetItemNumInBagById(self.select_data.item_id)
	-- self.cell.item_num = num
	-- if self.cell.item_num <= 0 then
	-- 	self.cell.item_num = 1
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.MinItemNum)

	-- elseif self.cell.item_num > item_num then
	-- 	self.cell.item_num = item_num
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.LimitItemNum)
	-- elseif self.cell.item_num > COMMON_CONSTS.MAX_BUY_COUNT then
	-- 	self.cell.item_num = COMMON_CONSTS.MAX_BUY_COUNT
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.MaxItemNum)
	-- end
	self.node_list.txt_taraget_add_charm.text.text = self.cur_cell.data.add_charm * num
	self.node_list.txt_my_add_charm.text.text = self.cur_cell.data.add_charm * num
	self.node_list.txt_taraget_add_timacy.text.text = self.cur_cell.data.add_qinmi * num
	self.node_list["cur_num"].text.text = num
	self.cur_num = num

	self:FlushBuyCost()
end

function FlowerView:OnInputClickHandler()
	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(self.cur_num)
	num_keypad:SetMaxValue(999)
	num_keypad:SetMinValue(1)
	num_keypad:SetOkCallBack(self.keyboard_bind)
end

-- 送花
function FlowerView:OnClickBtnSend()
	if self.accepter_data == nil  then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.NotSelectObj)
		return
	end
	local target_uid = self.accepter_data.role_id
	if nil == target_uid or target_uid <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.NotSelectObj)
		return
	end
	if SocietyWGData.Instance:FindFriend(target_uid) then
		self:SendFlowerHandler(self.select_index, target_uid)
	else
		--[[
		if nil == self.alert_window then
			self.alert_window = MarryAlert.New(nil, nil, nil, nil, true)
		end
		self.alert_window:SetLableString(Language.Flower.NotFriendYips)
		self.alert_window:SetOkFunc(BindTool.Bind3(self.SendFlowerHandler, self, self.select_index, target_uid))
		self.alert_window:Open()
		]]
		self:SendFlowerHandler(self.select_index, target_uid)
	end
end

function FlowerView:OnToggleFlowerType(index, is_on)
	self.node_list["flower_item_root"]:SetActive(index == 1 and is_on)
	self.node_list["flower_item_root_sp"]:SetActive(index == 2 and is_on)
	self:ClickSendHandler(self.flower_items[(index - 1) * 4 + 1])
end

function FlowerView:ClickSendHandler(cell)
	if not cell or self.select_index == cell.index then
		return
	end

	self.select_index = cell.index
	self.cur_cell = cell
	self.cur_num = 1
	self.item_cell:SetData({item_id = cell.data.item_id})
	local cfg = ItemWGData.Instance:GetItemConfig(cell.data.item_id)
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cell.data.item_id)
	self.node_list.item_name.text.text = cfg and cfg.name or ""
	self:OnChangeNum(self.cur_num)
	self.node_list.item_have_num.text.text = string.format(Language.Flower.HaveItemNun, item_num or 0)
	local num1 = self.node_list.flower_item_root.transform.childCount

	local bundle, asset = ResPath.GetRawImagesPNG("a3_zh_jk_" .. cell.index)
	XUI.SetNodeImage(self.node_list.show_img, bundle, asset)

	for i, v in ipairs(self.flower_items) do
		v:SetSelect(i == cell.index)
	end
end

function FlowerView:FlushBuyCost()
	if self.cur_cell.data.type == 1 then -- 普通类型
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.cur_cell.data.item_id)
		local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.cur_cell.data.seq)
		local price = 0
		if shop_cfg then
			price = shop_cfg.price
		end
	
		local num = self.cur_num - item_num
		if num > 0 then
			self.node_list.cost_content:SetActive(true)
			self.node_list.normal_text:SetActive(false)
			self.node_list.buy_cost_value.text.text = price * num
		else
			self.node_list.cost_content:SetActive(false)
			self.node_list.normal_text:SetActive(true)
		end
	elseif self.cur_cell.data.type == 2 then --活动限定类型
		self.node_list.cost_content:SetActive(false)
		self.node_list.normal_text:SetActive(true)
	end
end

function FlowerView:SendFlowerHandler(index, target_uid)
	local data = self:GetSendFlowerInfo(index)
	if data.need_buy == 1 then --需要买
		if index > 2 then
			--local cell_data = self.cell:GetData()
			if nil == self.send_window then
				self.send_window = Alert.New(nil, nil, nil, nil, true)
			end
			local cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			local item_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
			local name = ToColorStr(cfg.name, ITEM_COLOR[cfg.color])
			local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(data.seq)
			local price = 0
			if shop_cfg then
				price = shop_cfg.price
			end

			local num = self.cur_num - item_num

			self.send_window:SetShowCheckBox(true, "flower_view_send_window2")
			self.send_window:SetLableString(string.format(Language.Flower.SendBuyFlower, price * num, name))
			self.send_window:SetOkFunc(function()
				FlowerWGCtrl.Instance:SendGiveFlower(data.grid_index, data.item_id, target_uid, self.cur_num, 0, data.seq)
			end)
			self.send_window:Open()
		else
			FlowerWGCtrl.Instance:SendGiveFlower(data.grid_index, data.item_id, target_uid, self.cur_num, 0, data.seq)
		end
	elseif data.need_buy == -1 then -- 活动类型且数量不够
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.HasNotFlowerTip)
	else
		FlowerWGCtrl.Instance:SendGiveFlower(data.grid_index, data.item_id, target_uid, self.cur_num, 0, data.seq)
	end
end

function FlowerView:OnClickFriendList()
	ProfessWallWGCtrl.Instance:SetSelectFriendCallBack(self.select_friend_callback)
	ViewManager.Instance:Open(GuideModuleName.ProfessSelectFriendView, TabIndex.send_flower_obj)
end

function FlowerView:SelectFrendCallBack(role_info)
	if not role_info then return end
	self:SetFriendHand(role_info)
end

function FlowerView:SetFriendHand(data)
	if data == nil then return end

	self.flower_attr_data = data

	-- if self.node_list["role_name"] then
	-- 	self.node_list["role_name"].text.text = data.gamename
	-- end
	
	if self.node_list["lbl_lover_name"] then
		self.node_list["lbl_lover_name"].text.text = data.gamename
		-- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.lbl_lover_name.rect)
	end

	self.node_list["flower_attr_tips_btn"]:SetActive(true)
	self.node_list["charm"]:SetActive(data.is_myself ~= true)
	self.node_list["my_charm"]:SetActive(data.is_myself == true)
	-- self.node_list["timacy"]:SetActive(data.is_myself ~= true)
	if not data.is_myself then
		self.node_list["txt_taraget_charm"].text.text = data.charm -- 魅力值
		self.node_list["txt_taraget_timacy"].text.text = data.intimacy
	end

	--设置头像
	local data1 = {}
	data1.appearance = {fashion_photoframe = data.fashion_photoframe}
	data1.gamename = data.gamename
	data1.role_id = data.user_id
	data1.prof = data.prof
	data1.sex = data.sex
	data1.is_online = data.is_online
	self.accepter_data = data1
	self:SetLoverHead(data1)
	--self:OnClickFriendList()
end

function FlowerView:SetLoverHead(data1)
    if not self.role_loverhead_cell then
		self.role_loverhead_cell = BaseHeadCell.New(self.node_list["ph_lover_head"])
    end
	self.node_list["change_head_img"]:SetActive(true)
	self.node_list["ph_lover_head"]:SetActive(true)
	self.node_list["no_head_img"]:SetActive(false)
    local role_vo = data1
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = role_vo.role_id
	data.prof = role_vo.prof
    data.sex = role_vo.sex
	data.is_online = role_vo.is_online
	--self.role_loverhead_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.role_loverhead_cell:SetData(data)
	self.role_loverhead_cell:SetGray(data.is_online == 0)
	self.role_loverhead_cell:SetBgActive(false)
end

function FlowerView:GetSendFlowerInfo(index)
	local select_data = self.flower_items[index]:GetData()
	local data = {}
	local item_num = ItemWGData.Instance:GetItemNumInBagById(select_data.item_id)
	if item_num > 0 then
		local grid_index = ItemWGData.Instance:GetItemIndex(select_data.item_id)
		data.grid_index = grid_index
	else
		data.grid_index = -1
	end

	if item_num >= self.cur_num then
		--data.item_num = item_num > 999 and 999 or item_num
		data.seq = select_data.seq
		data.need_buy = 0
		data.item_id = select_data.item_id
	else
		--data.item_num = 1
		data.seq = select_data.seq
		data.need_buy = select_data.type == 2 and -1 or 1
		data.item_id = select_data.item_id
	end
	return data
end

function FlowerView:OnClickTipsAttrBtn()
    local tips_data = self:GetFlowerAttrTipsData()
    FlowerWGCtrl.Instance:OpenTipsAttrView(tips_data)
end

--获取和对方的亲密度、魅力值等属性.
function FlowerView:GetFlowerAttrTipsData()
	if not self.flower_attr_data then
		return
	end

	local attr_list = {}

	--加入属性值.1：魅力值，2：亲密度，3：我的魅力值.
	for per_index = 1, 3 do
		local data_attr_per = {}
		data_attr_per.attr_str = per_index

		local attr_value = 0
		if not self.flower_attr_data.is_myself then
			if per_index == 1 then
				attr_value = self.flower_attr_data.charm
			elseif per_index == 2 then
				attr_value = self.flower_attr_data.intimacy
			else
				local role_vo = RoleWGData.Instance:GetRoleVo()
				attr_value = role_vo.all_charm
			end
		end

		data_attr_per.attr_value = attr_value
		data_attr_per.is_per = false
		data_attr_per.reset_name = Language.Flower.AttrType[per_index]
		attr_list[per_index] = data_attr_per
	end

	local return_table = {}
	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	local tips_data = {
		title_text = Language.Flower.AttrTitle,
		attr_data = return_table,
		-- prefix_text = "+",
	}

	return tips_data
end

------------------------------------------------------------------------
-- FlowerItemRender  赠花列表
FlowerItemRender = FlowerItemRender or BaseClass(BaseRender)

function FlowerItemRender:__init()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell)
	end
end

function FlowerItemRender:__delete()
	if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function FlowerItemRender:LoadCallBack()
    --self.node_list["btn_send"].button:AddClickListener(BindTool.Bind(self.OnSendBtnClick, self))
	self.node_list["btn_select"].button:AddClickListener(BindTool.Bind(self.OnSelectBtnClick, self))
end

function FlowerItemRender:SetBtnClickEvent(callback)
    self.btn_click = callback
end

function FlowerItemRender:OnSelectBtnClick()
    if self.btn_click then
        self.btn_click(self)
    end
end

function FlowerItemRender:SetSelect(bool)
	self.node_list.img_hl:SetActive(bool)
end

function FlowerItemRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list["txt_charm_value"].text.text = self.data.add_charm
    self.node_list["txt_timacy_value"].text.text = self.data.add_qinmi

    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	self.item_cell:SetShowCualityBg(false)
	self.item_cell:SetData({item_id = self.data.item_id})
	self.item_cell:SetCellBgEnabled(false)
	self.item_cell:SetEffectRootEnable(false)
    --self.node_list["img_flower_icon"]:SetActive(item_num ~= 0)
    -- local cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)
    -- local price = 0
    -- if cfg then
    --     price = cfg.price
    -- else
    --     print_error("取不到商城配置, seq =", self.data.seq)
    -- end
    -- self.node_list["lbl_price"].text.text = price
    local name = ItemWGData.Instance:GetItemName(self.data.item_id)
    --local num_str = item_num > 0 and ToColorStr(item_num, COLOR3B.DEFAULT_NUM) or item_num
    self.node_list["text_item_name"].text.text = name
    -- local icon_name = "a2_zsxh_di"..self.data.bottom_color
    -- self.node_list["img9_bg"].image:LoadSprite(ResPath.GetF2SendFlowerImagePath(icon_name))
    -- self.node_list["title_bg"].image:LoadSprite(ResPath.GetF2SendFlowerImagePath("namedi_"..self.data.bottom_color))
    -- if nil == self.item_cell then
    -- 	self.item_cell =  ItemCell.New(self.node_list["ph_cell"])
    -- 	self.item_cell:SetData({item_id = self.data.item_id})
    -- end
    self.item_num = item_num > 999 and 999 or item_num
	self.node_list.remind:CustomSetActive(self.item_num > 0)
    -- self.node_list["cur_num"].text.text = self.item_num
end