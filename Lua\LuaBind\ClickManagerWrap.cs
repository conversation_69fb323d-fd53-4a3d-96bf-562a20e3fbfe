﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ClickManagerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ClickManager), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("ListenClickGround", ListenClickGround);
		<PERSON><PERSON>unction("UnlistenClickGround", UnlistenClickGround);
		<PERSON><PERSON>unction("SetResearveArea", SetResearveArea);
		L.RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Instance", get_Instance, null);
		<PERSON><PERSON>RegFunction("ClickGroundDelegate", ClickManager_ClickGroundDelegate);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListenClickGround(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ClickManager obj = (ClickManager)ToLua.CheckObject(L, 1, typeof(ClickManager));
			ClickManager.ClickGroundDelegate arg0 = (ClickManager.ClickGroundDelegate)ToLua.CheckDelegate<ClickManager.ClickGroundDelegate>(L, 2);
			ClickManager.ClickGroundDelegate o = obj.ListenClickGround(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnlistenClickGround(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ClickManager obj = (ClickManager)ToLua.CheckObject(L, 1, typeof(ClickManager));
			ClickManager.ClickGroundDelegate arg0 = (ClickManager.ClickGroundDelegate)ToLua.CheckDelegate<ClickManager.ClickGroundDelegate>(L, 2);
			obj.UnlistenClickGround(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetResearveArea(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ClickManager obj = (ClickManager)ToLua.CheckObject(L, 1, typeof(ClickManager));
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
			obj.SetResearveArea(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Instance(IntPtr L)
	{
		try
		{
			ToLua.PushSealed(L, ClickManager.Instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClickManager_ClickGroundDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<ClickManager.ClickGroundDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<ClickManager.ClickGroundDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

