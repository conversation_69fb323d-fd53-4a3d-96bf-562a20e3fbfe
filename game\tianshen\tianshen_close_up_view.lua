TianShenCloseUpView = TianShenCloseUpView or BaseClass(SafeBaseView)

function TianShenCloseUpView:__init()
	self.open_tween = nil
	self.close_tween = nil
	self.active_close = false
	self.blocks_raycasts = false
	-- self.task_panel_flag = 0
	self.view_cache_time = 0
	self.view_layer = UiLayer.MainUIHigh

	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_tianshen_close_up")
end

function TianShenCloseUpView:__delete()
end

function TianShenCloseUpView:SetData(appearance_param)
	self.appearance_param = appearance_param
end

function TianShenCloseUpView:ReleaseCallBack()
	-- self.task_panel_flag = 0
	self.obj = nil
end

function TianShenCloseUpView:OpenCallBack()
	self.delay_close_timer = GlobalTimerQuest:AddDelayTimer(function() 
		self:Close() 
	end, 4.5) -- 避免没关掉界面
	MainuiWGCtrl.Instance:HideOrShowChatView(true)
	-- if MainuiWGCtrl.Instance:GetPlayTaskButtonStatus() == false then
	-- 	MainuiWGCtrl.Instance:PlayTaskButtonTween(true)
	-- 	self.task_panel_flag = 1
	-- end
	if self:IsLoaded() then
		self:ShowCloseUp()
	end
end

function TianShenCloseUpView:CloseCallBack()
	MainuiWGCtrl.Instance:HideOrShowChatView(false)
	--MainuiWGCtrl.Instance:SetEqTargetButtonShow()
	-- if self.task_panel_flag == 1 then
	-- 	MainuiWGCtrl.Instance:PlayTaskButtonTween(false)
	-- 	self.task_panel_flag = 0
	-- end            

	if self.delay_close_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.delay_close_timer)
        self.delay_close_timer = nil
    end

	if self.delay_close_timer_2 ~= nil then
        GlobalTimerQuest:CancelQuest(self.delay_close_timer_2)
        self.delay_close_timer_2 = nil
    end
end

function TianShenCloseUpView:LoadCallBack()
	self:ShowCloseUp()
end

function TianShenCloseUpView:ShowCloseUp()
	if self.obj then
		ResMgr:Destroy(self.obj)
		self.obj = nil
	end

	-- self.node_list["tianshen_close_up"]:SetActive(false)
	-- local bundle, asset = ResPath.GetTianShenCloseUpSpine(self.appearance_param)
	-- local loader = AllocResAsyncLoader(self, "ChannelImage")
	-- loader:Load(bundle, asset, typeof(UnityEngine.GameObject), function (new_obj)
	-- 	if nil ~= new_obj then
	-- 		self.obj = ResMgr:Instantiate(new_obj)
	-- 		self.obj.transform:SetParent(self.node_list["ren"].transform, false)
	-- 		self.node_list["tianshen_close_up"]:SetActive(true)
	-- 		self.delay_close_timer_2 = GlobalTimerQuest:AddDelayTimer(function() 
	-- 			self:Close() 
	-- 		end, 2.3)
	-- 	else
	-- 		self:Close()
	-- 	end
	-- end)
	
	-- local bg_bundle, bg_name = ResPath.GetF2RawImagesPNG("bianshen_bg_" .. self.appearance_param)
	-- self.node_list["bg"].raw_image:LoadSpriteAsync(bg_bundle, bg_name, function()
	-- 	self.node_list["bg"].raw_image:SetNativeSize()
	-- end)

	-- local zi_bundle, zi_name = ResPath.GetF2RawImagesPNG("bianshen_zi_" .. self.appearance_param)
	-- self.node_list["zi"].raw_image:LoadSpriteAsync(zi_bundle, zi_name, function()
	-- 	self.node_list["zi"].raw_image:SetNativeSize()
 -- 	end)

 	local show_bundle, show_name = ResPath.GetF2RawImagesPNG("show_bg_" .. self.appearance_param)
	self.node_list["bg"].raw_image:LoadSpriteAsync(show_bundle, show_name, function()
		self.node_list["bg"].raw_image:SetNativeSize()

		if not self:IsOpen() then
			return
		end

		if self.delay_close_timer_2 ~= nil then
	        GlobalTimerQuest:CancelQuest(self.delay_close_timer_2)
	        self.delay_close_timer_2 = nil
	    end

		self.delay_close_timer_2 = GlobalTimerQuest:AddDelayTimer(function() 
			self:Close() 
		end, 2.3)
 	end)
end
