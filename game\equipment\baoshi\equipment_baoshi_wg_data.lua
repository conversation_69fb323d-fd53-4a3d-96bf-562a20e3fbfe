EquipmentWGData = EquipmentWGData or BaseClass()

function EquipmentWGData:InitBaoShiData()
    local stoneconfig_cfg = ConfigManager.Instance:GetAutoConfig("newstoneconfig_auto")
    self.total_stone_cfg = stoneconfig_cfg.total_stone
    self.stone_level_up_cfg = stoneconfig_cfg.stone_level_up
    self.stone_polish_cfg = stoneconfig_cfg.stone_polish
    self.stone_polish_limit_cfg = stoneconfig_cfg.polish_max
    self.stone_menu_cfg = stoneconfig_cfg.stone_menu
    --self.stone_refine_stuff = ListToMap(stoneconfig_cfg.refine_stuff, "part_index", "seq")

    self.stone_cfg = stoneconfig_cfg.stone
    self.list_to_map_stone_cfg = ListToMap(stoneconfig_cfg.stone, "item_id")
    self.stone_equip_part_cfg = ListToMap(stoneconfig_cfg.stone_equip_part, "equip_part")
    self.old_stone_level_up_cfg = ListToMap(stoneconfig_cfg.stone_level_up,"old_stone_item_id")
    self.new_stone_level_up_cfg = ListToMap(stoneconfig_cfg.stone_level_up,"new_stone_item_id")
    self.stone_slot_open_limit = ListToMap(stoneconfig_cfg.stone_slot_open_limit, "part_index", "slot_index")
    self.stone_stone_refine_cfg = ListToMap(stoneconfig_cfg.stone_refine2, "refine_level")
    self.stone_stone_jl_cost_cfg = ListToMap(stoneconfig_cfg.stone_refine2_consume, "part")

    self.first_lev_stone_price = stoneconfig_cfg.stone_level_up[1].price

    self.total_stone_level = {}					-- 宝石总等级
    self.stone_active_star_level = {}
    self.stone_infos = {}						-- 宝石列表
    self.refine_level = {}
    self.slot_remind_infos = {}

    self:CtreBaoShiConversionTable()
end

function EquipmentWGData:CtreBaoShiConversionTable()
	self.baoshi_table = {}
	for i,v in ipairs(self.stone_cfg) do
		local need_num = 0
		local item_id = v.item_id
		self.baoshi_table[item_id] = {}

		local baoshi_level_cfg = self:GetBaoShiLevelUpCfg(item_id)
        local price = self.first_lev_stone_price

		if baoshi_level_cfg then
			if v.level <= 1 then
				price = need_num * self.first_lev_stone_price
			else
				need_num = self:BaoShiConversion(item_id)
				price = need_num * self.first_lev_stone_price
			end

			self.baoshi_table[item_id].num = need_num
			self.baoshi_table[item_id].price = price
		else
			if v.level <= 1 then
				self.baoshi_table[item_id].num = 0
				self.baoshi_table[item_id].price = price
			end
		end
	end
end

-------------------------------------------PROTOCOL_START--------------------------------------------
function EquipmentWGData:SetStoneInfo(protocol)
    self.stone_infos = protocol.stone_infos
    self.refine_level = protocol.refine_level
    self:UpdateAllEquipStoneSlotRemind()
end

function EquipmentWGData:UpdateStoneInfo(protocol)
    local equip_body_index = protocol.index
    self.refine_level[equip_body_index] = protocol.refine_level
    self.stone_infos[equip_body_index] = protocol.stone_info
    self:UpdateEquipStoneInfo(equip_body_index)
end

function EquipmentWGData:OnStoneBaseInfo(protocol)
    self.total_stone_level = protocol.total_stone_level                -- 25肉身宝石总等级
	self.stone_active_star_level = protocol.active_total_level    -- 25肉身宝石激活总等级
    self:UpdateAllEquipStoneSlotRemind()
end

-- 获取宝石总等级
function EquipmentWGData:GetTotalStoneLevel(equip_body_seq)
	return self.total_stone_level[equip_body_seq] or 0
end

-- 获取已经镶嵌的宝石的列表数据
function EquipmentWGData:GetStoneInfo()
	return self.stone_infos
end

function EquipmentWGData:GetStoneRefineLevelByPart(equip_body_index)
	return self.refine_level[equip_body_index] or 0
end

-- 装备的StoneInfo
function EquipmentWGData:GetStoneInfoListByIndex(equip_body_index)
	return self.stone_infos[equip_body_index] or {}
end

--获取身上某个宝石孔中的宝石data
function EquipmentWGData:GetBaoShiDataBySelectIndex(equip_body_index, slot_id)
    return (self.stone_infos[equip_body_index] or {})[slot_id] or {}
end

function EquipmentWGData:GetStoneActiveLevel(equip_body_seq)
	return self.stone_active_star_level[equip_body_seq] or 0
end

function EquipmentWGData:GetAllStoneActiveStarLevel()
    return self.stone_active_star_level
end

function EquipmentWGData:UpdateAllEquipStoneSlotRemind()
    if IsEmptyTable(self.stone_infos) then
        return
    end

    for equip_index = 0, GameEnum.MAX_ROLE_EQUIP_NUM - 1 do
        self:UpdateEquipStoneInfo(equip_index)
    end



    -- for part = 0, GameEnum.MAX_STONE_PART_NUM - 1 do
    --     self.slot_remind_infos[part] = {}
	-- 	for index = 0, GameEnum.MAX_STONE_COUNT - 1 do
    --         self.slot_remind_infos[part][index] = {}
    --         self.slot_remind_infos[part][index].is_have_batter = self:GetBaoShiSlotCanAddRemind(part, index)
    --         self.slot_remind_infos[part][index].is_can_upgrade = self:GetBaoShiSlotCanUpGrade(part, index)
    --     end
    -- end


    -- print_error("TODO 镶嵌加成红点")
    -- -- 镶嵌加成红点
	-- local stone_active_num = self:GetEquipStoneActiveRemind()
	-- if stone_active_num > 0 then
    --     local open_func = function ()
    --         FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_baoshi)
    --         RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.STONE_TIP)
    --         RoleWGCtrl.Instance:OpenEquipAttr()
    --     end
    --     MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STONE_ATTR_ACTIVE , 1, open_func)
	-- 	return 1
    -- else
    --     MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STONE_ATTR_ACTIVE, 0)
	-- end

end

function EquipmentWGData:UpdateEquipStoneInfo(equip_body_index)
    self.slot_remind_infos[equip_body_index] = {}

    for index = 0, GameEnum.MAX_STONE_COUNT - 1 do
        self.slot_remind_infos[equip_body_index][index] = {}
        self.slot_remind_infos[equip_body_index][index].is_have_batter = self:GetBaoShiSlotCanAddRemind(equip_body_index, index)
        self.slot_remind_infos[equip_body_index][index].is_can_upgrade = self:GetBaoShiSlotCanUpGrade(equip_body_index, index)
    end
end

--------------------------------------------PROTOCOL_END-----------------------------------------------

-------------------------------------------REMIND_START------------------------------------------------
-- 宝石红点提醒
function EquipmentWGData:GetEquipBaoShiRemindNum()
    local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()
	if not IsEmptyTable(total_equip_body_data_list) then
		for k, v in pairs(total_equip_body_data_list) do
			if self:GetEquipBodyBSXQRemind(v.seq) > 0 then
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_Bao, 1, function ()
					FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_baoshi)
				end)

				return 1
			end
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_Bao, 0)
	return 0
end

-- 肉身宝石镶嵌红点
function EquipmentWGData:GetEquipBodyBSXQRemind(equip_body_seq)
    --  获取肉身显示的装备
    local equip_data_list = self:GetBSXQShowEquipList(equip_body_seq, true)

    if IsEmptyTable(equip_data_list) then
        return 0
    end

    -- 洗炼阶数加成红点
    if self:GetEquipStoneActiveRemind(equip_body_seq) > 0 then
        return 1
    end

    -- 镶嵌红点
    for k, data in pairs(equip_data_list) do
        if self:GetEquipBodyBSXQEquipRemind(data.index) > 0 then
            return 1
        end
    end

    return 0
end

-- 镶嵌加成红点
function EquipmentWGData:GetEquipStoneActiveRemind(equip_body_seq)
	local baoshi_total_level = self:GetTotalStoneLevel(equip_body_seq)
	local active_level = self:GetStoneActiveLevel(equip_body_seq)
	local next_total_cfg = self:GetBaoShiTotalStoneCfg(equip_body_seq, active_level, true)

	if next_total_cfg then
        if  next_total_cfg.total_stone <= baoshi_total_level
            and active_level <= next_total_cfg.total_stone then
		    return 1
        end
	end

	return 0
end

-- 肉身装备宝石镶嵌红点
function EquipmentWGData:GetEquipBodyBSXQEquipRemind(equip_body_index)
    for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
        if self:GetEquipBodyBSXQEquipSlotRemind(equip_body_index, slot_index) > 0 then
            return 1
        end
    end

    return 0
end

-- 肉身装备宝石 孔位 镶嵌红点
function EquipmentWGData:GetEquipBodyBSXQEquipSlotRemind(equip_body_index, slot_id)
    local is_have_batter = self:GetBaoShiSlotCanAddRemind(equip_body_index, slot_id)

    if is_have_batter then
        return 1 
    end

    local is_can_upgrade = self:GetBaoShiSlotCanUpGrade(equip_body_index, slot_id)
    if is_can_upgrade then
        return 1 
    end

    return 0
end
 
-- 孔位是否有能镶嵌宝石
function EquipmentWGData:GetBaoShiSlotCanAddRemind(equip_body_index, slot_index)
    if not equip_body_index or not slot_index then
        return false
    end

    local is_open = self:BaoShiSlotIsOpen(equip_body_index, slot_index)
    if not is_open then
        return false
    end

    local list = self:GetBaoShiInlayAttrType(equip_body_index, slot_index)

    for k, v in pairs(list) do
        if v.is_show_remind then
            return true
        end
    end

    return false
end

-- 空位能否升级宝石
function EquipmentWGData:GetBaoShiSlotCanUpGrade(equip_body_index, slot_index)
    local is_open = self:BaoShiSlotIsOpen(equip_body_index, slot_index)
    if not is_open then
        return false
    end

    local is_inlay = self:BaoShiSlotIsInlay(equip_body_index ,slot_index)
    if not is_inlay then
        return false
    end

    local slot_stone_item_id = self:GetBaoShiItemIdBySelectIndex(equip_body_index, slot_index)
    if slot_stone_item_id > 0 then
        local stone_cfg = self:GetBaoShiCfgByItemId(slot_stone_item_id)

        if stone_cfg then
            local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
            local jade_level_limit = EquipBodyWGData.Instance:GetEquipStoneJadeLevelLimit(equip_body_seq)

            if stone_cfg.level >= jade_level_limit then
               return false 
            end
        end
    end

    local need_price = self:GetBaoShiUpgradePrice(equip_body_index, slot_index)
    return need_price <= 0
end

function EquipmentWGData:GetEquipPartIsAllInlayStone(equip_part)
    local open_num = 0
    local inlay_num = 0
    for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
        local slot_data = self:GetBaoShiDataBySelectIndex(equip_part, slot_index)
        if slot_data and slot_data.is_open == 1 then
            open_num = open_num + 1
        end

        if slot_data and slot_data.is_inlay then
            inlay_num = inlay_num + 1
        end
    end

    return open_num == inlay_num
end

function EquipmentWGData:GetEquipPartMinLevelStone(equip_body_index)
    local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local jade_level_limit = EquipBodyWGData.Instance:GetEquipStoneJadeLevelLimit(equip_body_seq)

    local min_level_index = -1
    local min_level = -1
    for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
        local slot_data = self:GetBaoShiDataBySelectIndex(equip_body_index, slot_index)
        local next_stone = nil
        local stone_cfg = nil
        if slot_data.item_id and slot_data.item_id > 0 then
            next_stone = self:GetBaoShiLevelUpCfgByOldId(slot_data.item_id)
            stone_cfg = self:GetBaoShiCfgByItemId(slot_data.item_id)
        end

        if next_stone and stone_cfg then
            local cur_level = stone_cfg.level
            if (cur_level < jade_level_limit) and (min_level_index == -1 or (cur_level < min_level)) then
                min_level_index = slot_index
                min_level = cur_level
            end
        end
    end

    return min_level_index
end

--------------------------------------------REMIND_END-------------------------------------------------

-------------------------------------------CFG_GET_START-----------------------------------------------
-- 根据装备部位索引获得宝石类型
function EquipmentWGData:GetStoneTypeByEquipPart(equip_body_index)
    local equip_part = equip_body_index % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
    return (self.stone_equip_part_cfg[equip_part] or {}).stone_type or 1
end

-- 根据物品ID获取宝石配置
function EquipmentWGData:GetBaoShiCfgByItemId(item_id)
	return self.list_to_map_stone_cfg[item_id]
end

-- 根据现在物品ID获取宝石升级配置
function EquipmentWGData:GetBaoShiLevelUpCfgByOldId(old_stone_item_id)
	return self.old_stone_level_up_cfg[old_stone_item_id]
end

-- 根据升级的物品ID获取宝石升级配置
function EquipmentWGData:GetBaoShiLevelUpCfg(new_stone_item_id)
    return self.new_stone_level_up_cfg[new_stone_item_id]
end

-- 获取限制宝石配置
function EquipmentWGData:GetLimitBaoShiOpenCfg(equip_body_index ,slot_index)
    return (self.stone_slot_open_limit[equip_body_index % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM] or {})[slot_index] or {}
end
--------------------------------------------CFG_GET_END------------------------------------------------

---------------------------------------------CAL_START-------------------------------------------------
-- 获取人物身上的装备宝石数据列表(带remind功能的)
function EquipmentWGData:GetBSXQShowEquipList(equip_body_seq, not_need_sort)
	local data_list = EquipWGData.Instance:GetDataList()		--身上穿戴的装备列表

	local equip_data = {}

	if not data_list then
		return equip_data
	end

	local seq = 1
    local start_equip_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
    local end_equip_body_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + GameEnum.EQUIP_INDEX_XIANZHUO

	for k, v in pairs(data_list) do
		if v.index >= start_equip_index and v.index <= end_equip_body_index then
			equip_data[seq] = v
			equip_data[seq].stone = self.stone_infos[v.index]
			seq = seq + 1
		end
	end

	if not_need_sort then
		return equip_data
	end

	return EquipmentWGData.SortEquipTabForAwake(equip_data)
end

-- 宝石槽是否开启
function EquipmentWGData:BaoShiSlotIsOpen(equip_body_index ,slot_id)
    local slot_data = self:GetBaoShiDataBySelectIndex(equip_body_index, slot_id)
    return slot_data and slot_data.is_open == 1
end

-- 宝石槽是否镶嵌
function EquipmentWGData:BaoShiSlotIsInlay(equip_body_index ,slot_index)
    local slot_data = self:GetBaoShiDataBySelectIndex(equip_body_index, slot_index)
    return slot_data and slot_data.is_inlay
end

--【修改标记】
-- 根据装备部位索引,装备孔索引 获取宝贝中的宝石
function EquipmentWGData:GetBaoShiInlayAttrType(equip_part_index, slot_index)
	local item_list = {}
    local stone_type = self:GetStoneTypeByEquipPart(equip_part_index)
	local cur_item_data = self:GetBaoShiDataBySelectIndex(equip_part_index, slot_index) 	--当前镶嵌的宝石
	if cur_item_data == nil then
		return {}
	end

	local cur_stone_cfg = self:GetBaoShiCfgByItemId(cur_item_data.item_id)
	local bs_item_info = ItemWGData.Instance:GetEquipStoneItemList(stone_type)

    local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_part_index)
    local jade_level_limit = EquipBodyWGData.Instance:GetEquipStoneJadeLevelLimit(equip_body_seq)

	for k, v in pairs(bs_item_info) do
		local item_cfg = self:GetBaoShiCfgByItemId(v.item_id)

		if item_cfg then
			v.level = item_cfg.level
			v.sort_type = item_cfg.sort_type
			v.stone_type = item_cfg.stone_type
			v.goto_shop = false

			if cur_stone_cfg then
				v.is_show_remind = cur_stone_cfg.level < v.level and v.level <= jade_level_limit
			elseif cur_item_data.is_open and cur_item_data.item_id == 0 and v.level <= jade_level_limit then
				v.is_show_remind = true
			else
				v.is_show_remind = false
			end

			item_list[#item_list + 1] = v
		end
	end

	return item_list
end

function EquipmentWGData:GetBaoShiUpgradePrice(equip_body_index, select_index)
	local old_stone_item_id = self:GetBaoShiItemIdBySelectIndex(equip_body_index, select_index)  --当前宝石id
    local equip_body_seq = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	return self:ComputBaoShiUpgradePrice(old_stone_item_id, equip_body_seq)
end

--获取身上某个宝石孔中的宝石id
function EquipmentWGData:GetBaoShiItemIdBySelectIndex(equip_body_index, select_index)
    local slot_info = self:GetBaoShiDataBySelectIndex(equip_body_index, select_index)
    return (slot_info or {}).item_id or 0
end

--计算升级宝石花费的价格
function EquipmentWGData:ComputBaoShiUpgradePrice(old_stone_item_id, equip_body_seq)
	if not old_stone_item_id or old_stone_item_id <= 0 then
		return 999999999
	end

	local cfg = self:GetBaoShiLevelUpCfgByOldId(old_stone_item_id)
	--已经满级
	if cfg == nil then
		return 999999999
	end

	--目标需要的换算数量
	local new_stone_item_id = cfg.new_stone_item_id 			          --新宝石id
	local new_conversion_list = self:GetBaoShiConversionTableByid(new_stone_item_id)
	if IsEmptyTable(new_conversion_list) then
		return 999999999
	end

	--当前宝石孔的换算数量
	local cur_conversion_list = self:GetBaoShiConversionTableByid(old_stone_item_id)
	local cur_stone_cfg = self:GetBaoShiCfgByItemId(old_stone_item_id)
	local cur_num, cur_price = 0, 0

	cur_num = cur_conversion_list.num
	cur_price = cur_conversion_list.price

	--背包所有宝石的换算数量
	local bag_conversion_list = ItemWGData.Instance:GetEquipStoneNumAndValueByType(cur_stone_cfg.stone_type, equip_body_seq)
	local new_stone_item_price = 0
	local have_price = cur_price + bag_conversion_list.price
	if new_conversion_list.price > have_price then
		new_stone_item_price = new_conversion_list.price - have_price
	end

	return new_stone_item_price
end

function EquipmentWGData:GetBaoShiConversionTableByid(item_id)
    return self.baoshi_table[item_id] or {}
end

--宝石等级，每级需要多少个
--根据等级换算成1级宝石
function EquipmentWGData:BaoShiConversion(item_id)
    local need_num = 1
    local function calc_num(stone_id)
        local new_cfg = self:GetBaoShiLevelUpCfg(stone_id)
        if new_cfg then
            need_num = need_num * new_cfg.need_num
            calc_num(new_cfg.old_stone_item_id)
        end
    end

    calc_num(item_id)
    return need_num > 1 and need_num or 0
end

function EquipmentWGData:GetStoneSlotHaveBatterState(equip_body_index, slot_index)
    return ((self.slot_remind_infos[equip_body_index] or {})[slot_index] or {}).is_have_batter or false
end

function EquipmentWGData:GetBaoShiTotalStoneCfg(equip_body_seq, level, is_next)
	local curr_cfg = nil
	local next_cfg = nil

    local max_level_limit = EquipBodyWGData.Instance:GetEquipStoneActiveLimitLevel(equip_body_seq)
    local max_level = math.min(level, max_level_limit)

    for k, v in pairs(self.total_stone_cfg) do
        if v.total_stone > max_level then
            if v.total_stone <= max_level_limit then
                next_cfg = v
            end
            
            break
        end

        curr_cfg = v
    end

	-- for k, v in pairs(self.total_stone_cfg) do
	-- 	if v.total_stone <= max_level then
	-- 		curr_cfg = v

    --         if not IsEmptyTable(self.total_stone_cfg[k + 1]) then
    --             if self.total_stone_cfg[k + 1].total_stone <= max_level_limit then
    --                 next_cfg = self.total_stone_cfg[k + 1]
    --             end
    --         end
	-- 	end
	-- end

	if is_next then
		if nil == curr_cfg then
			return self.total_stone_cfg[1]
		else
			return next_cfg
		end
	end

	return curr_cfg
end

function EquipmentWGData:GetStoneActiveLevelChange()
	local active_level = self:GetAllStoneActiveStarLevel()

	if nil == self.old_stone_active_star_level then
		self.old_stone_active_star_level = active_level
		return false
	end

    local level_change = false
    if active_level then
        for k, v in pairs(active_level) do
            local old_lv = self.old_stone_active_star_level[k]

            if old_lv and old_lv < v then
                level_change = true 
            end
        end
    end

    self.old_stone_active_star_level = active_level

	-- if active_level and self.old_stone_active_star_level < self.stone_active_star_level then
	-- 	self.old_stone_active_star_level = active_level
	-- 	return true
	-- end

	return level_change
end

--背包中装备索引index，宝石孔
--注意，每个宝石孔镶嵌的宝石类型需要做筛选
function EquipmentWGData:GetBaoShiListByStone(equip_body_index, slot_index)
    local stone_info_list = {}
    local stone_type = self:GetStoneTypeByEquipPart(equip_body_index)
	local cur_item_data = self:GetBaoShiDataBySelectIndex(equip_body_index, slot_index) 	--当前镶嵌的宝石
	if IsEmptyTable(cur_item_data) or cur_item_data.is_open ~= 1 then
		return stone_info_list
	end

	local cur_stone_cfg = self:GetBaoShiCfgByItemId(cur_item_data.item_id)

    local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
    local jade_level_limit = EquipBodyWGData.Instance:GetEquipStoneJadeLevelLimit(equip_body_seq)

	local bs_item_info = ItemWGData.Instance:GetEquipStoneItemList(stone_type)
	for k, data in pairs(bs_item_info) do
        local data_stone_cfg = self:GetBaoShiCfgByItemId(data.item_id)
        if data_stone_cfg then
            if data_stone_cfg.level <= jade_level_limit then
                if cur_item_data.is_inlay and cur_stone_cfg then
                    if data_stone_cfg.level > cur_stone_cfg.level then
                        stone_info_list[#stone_info_list + 1] = data
                    end
                else
                    stone_info_list[#stone_info_list + 1] = data
                end
            end
        end
    end

    if not IsEmptyTable(stone_info_list) then
        table.sort(stone_info_list, SortTools.KeyUpperSorter("item_id"))
    end

	return stone_info_list
end

function EquipmentWGData:GetBaoShiMenu(part_index)
	local stone_type = self:GetStoneTypeByEquipPart(part_index)
	local stone_menu_cfg = ConfigManager.Instance:GetAutoConfig("newstoneconfig_auto").stone_menu
	if IsEmptyTable(stone_menu_cfg) then
        return {}
    end

	for k, v in ipairs(stone_menu_cfg) do
		if stone_type == v.stone_type then
			return v
		end
	end

	return {}
end

-- 获取升级宝石需要低级宝石描述
function EquipmentWGData:CalcUpgradeNeedStoneStr(old_stone_item_id)
    if not old_stone_item_id then
		return nil
	end

	local cfg = self:GetBaoShiLevelUpCfgByOldId(old_stone_item_id)
    if IsEmptyTable(cfg) then
        return nil
    end

    local new_stone_item_id = cfg.new_stone_item_id 			            --新宝石id
    local new_stone_item_price = (cfg.price * cfg.need_num) - cfg.price		--新宝石价格
    local expend_tab = {}

    while cfg ~= nil do
        local have_count = ItemWGData.Instance:GetItemNumInBagById(old_stone_item_id)
        if have_count > 0 then
            local need_count = new_stone_item_price / cfg.price
            local offest_count = have_count - need_count
            local add_count = offest_count > 0 and need_count or have_count
            if add_count > 0 then
                table.insert(expend_tab, {item_id = old_stone_item_id, count = add_count})
            end

            if offest_count > 0 then
                break
            end
            new_stone_item_price = new_stone_item_price - cfg.price * have_count
        end

        local temp_cfg = self:GetBaoShiLevelUpCfg(old_stone_item_id)
        if temp_cfg == nil then
            break
        end

        old_stone_item_id = temp_cfg.old_stone_item_id
        cfg = self:GetBaoShiLevelUpCfgByOldId(old_stone_item_id)
    end

    if IsEmptyTable(expend_tab) then
        return nil
    else
        local str = ""
        for i, v in ipairs(expend_tab) do
            local name = ItemWGData.Instance:GetItemName(v.item_id, nil, true)
            if i < #expend_tab then
                str = str .. name .. "*" .. v.count .. "，"
            else
                str = str .. name .. "*" .. v.count
            end
        end

        return str
    end

    return nil
end

-- 拥有的同类型宝石列表
function EquipmentWGData:GetInBagSameTypeStoneList(equip_indx, slot_index)
    local stone_info_list = {}
    local stone_type = self:GetStoneTypeByEquipPart(equip_indx)
	local bs_item_info = ItemWGData.Instance:GetEquipStoneItemList(stone_type)

    local equip_body_seq = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_indx)
    local jade_level_limit = EquipBodyWGData.Instance:GetEquipStoneJadeLevelLimit(equip_body_seq)

	for k, data in pairs(bs_item_info) do
        local data_stone_cfg = self:GetBaoShiCfgByItemId(data.item_id)
        if data_stone_cfg and data_stone_cfg.level <= jade_level_limit then
            stone_info_list[#stone_info_list + 1] = data
        end
    end

    if not IsEmptyTable(stone_info_list) then
        table.sort(stone_info_list, SortTools.KeyUpperSorter("item_id"))
    end

	return stone_info_list
end

-- 装备是否镶嵌了宝石
function EquipmentWGData:GetEquipPartIsInlayStone(equip_body_index)
    for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
        if self:BaoShiSlotIsInlay(equip_body_index ,slot_index) then
            return true
        end
    end

    return false
end
----------------------------------------------CAL_END--------------------------------------------------



















































function EquipmentWGData:GetStoneSlotCanUpgradeState(equip_part, slot_index)
    local empty_table = {}
    return (((self.slot_remind_infos or empty_table)[equip_part] or empty_table)[slot_index]
        or empty_table).is_can_upgrade or false
end


-- 是否宝石物品
function EquipmentWGData:IsBaoShiItem(item_id)
	return self.list_to_map_stone_cfg[item_id] ~= nil
end

function EquipmentWGData:GetStoneCfgByID(item_id)
    return self.list_to_map_stone_cfg[item_id]
end

-- 在背包内的宝石转换成一级的总数 和 总价值    
function EquipmentWGData:GetBaoShiConversionTableInBag(stone_type, jade_level_limit)
	local conversion_list = {}
	local need_num = 0
	local price = self.first_lev_stone_price

	local bag_list = ItemWGData.Instance:GetEquipStoneItemList(stone_type)
	for k,v in pairs(bag_list) do
		local baoshi_cfg = self:GetBaoShiCfgByItemId(v.item_id)

        if baoshi_cfg.level <= jade_level_limit then
            local cfg = self:GetBaoShiConversionTableByid(v.item_id)
            need_num = need_num + (cfg.num * v.num)
            if baoshi_cfg.level <= 1 then
                need_num = (need_num + 1 * v.num)
            end
        end
	end

	conversion_list.num = need_num
	conversion_list.price = price * need_num
	return conversion_list
end






















-- function EquipmentWGData:GetOneRemindStoneInfo()
-- 	local list_data = self:GetEquipBaoShiShowList()
-- 	if IsEmptyTable(list_data) then
-- 		return -1
-- 	end

-- 	-- 优先提醒可替换镶嵌
-- 	for k, equip_data in ipairs(list_data) do
-- 		for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
-- 			local is_have_batter = self:GetStoneSlotHaveBatterState(equip_data.index, slot_index)
-- 			if is_have_batter then
-- 				return equip_data.index
-- 			end
-- 		end
-- 	end

-- 	-- 可升级
-- 	for k, equip_data in ipairs(list_data) do
-- 		for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
-- 			local is_can_upgrade = self:GetStoneSlotCanUpgradeState(equip_data.index, slot_index)
-- 			if is_can_upgrade then
-- 				return equip_data.index
-- 			end
-- 		end
-- 	end

-- 	return -1
-- end

function EquipmentWGData:GetEquipPartCanUpStone(equip_part)
    for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
        local is_can_upgrade = self:GetStoneSlotCanUpgradeState(equip_part, slot_index)

        if is_can_upgrade then
            return true
        end
    end

    return false
end

function EquipmentWGData:GetEquipPartCanInlayStone(equip_part)
    for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
        local is_have_batter = self:GetStoneSlotHaveBatterState(equip_part, slot_index)
        if is_have_batter then
            return true
        end
    end

    return false
end










-- 一键镶嵌
function EquipmentWGData:GetAllBaoShiOneKeyInlay(equip_body_seq)
    local temp_baoshi_list = {}
    local all_one_key_list = {}
    local sort_equip_body_equip_list = {}

    local equip_list = EquipWGData.Instance:GetDataList()
    for k, v in pairs(equip_list) do
        local cur_equip_body_seq = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(v.index)

        if cur_equip_body_seq == equip_body_seq then
            table.insert(sort_equip_body_equip_list, v)

            -- local list = self:GetBaoShiOneKeyInlay(v.index, temp_baoshi_list)
            -- for _, data in pairs(list) do
            --     table.insert(all_one_key_list, data)
            -- end
        end

        -- local order = EquipmentWGData.EquipBodySeqAndPartToEquipBodyIndex(equip_body_seq, v.index)
        -- if order == select_order then
        --     local list = self:GetBaoShiOneKeyInlay(v.index, temp_baoshi_list)
        --     for _, data in pairs(list) do
        --         table.insert(all_one_key_list, data)
        --     end
        -- end
    end

    if not IsEmptyTable(sort_equip_body_equip_list) then
        sort_equip_body_equip_list = EquipmentWGData.SortEquipTabForAwake(sort_equip_body_equip_list)

        for k, v in pairs(sort_equip_body_equip_list) do
            local list = self:GetBaoShiOneKeyInlay(v.index, temp_baoshi_list)

            for _, data in pairs(list) do
                table.insert(all_one_key_list, data)
            end
        end
    end

    return all_one_key_list
end

function EquipmentWGData:GetBaoShiOneKeyInlay(equip_body_index, temp_use_baoshi_list)
    local one_key_list = {}
    local stone_type = self:GetStoneTypeByEquipPart(equip_body_index)
    local bag_stone_list = ItemWGData.Instance:GetEquipStoneItemList(stone_type)
    if not temp_use_baoshi_list[stone_type] then
        temp_use_baoshi_list[stone_type] = {}
    end

    if IsEmptyTable(bag_stone_list) then
        return one_key_list
    end

    local equip_body_seq = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
    local jade_level_limit = EquipBodyWGData.Instance:GetEquipStoneJadeLevelLimit(equip_body_seq)

    -- 镶嵌空槽
    local had_inlay_itemid_list = {}
    local temp_item_data = {item_id = 0, index = -1}
    local is_open, is_inlay
    for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
        local slot_data = self:GetBaoShiDataBySelectIndex(equip_body_index, slot_index)
        is_open = slot_data.is_open == 1
        is_inlay = slot_data.is_inlay

        -- 缓存
        if is_open and is_inlay then
            table.insert(had_inlay_itemid_list, {part_index = equip_body_index, slot_index = slot_index, item_id = slot_data.item_id})
        elseif is_open and not is_inlay then
            temp_item_data = {item_id = 0, index = -1}
            for k,v in pairs(bag_stone_list) do
                local stone_cfg = self:GetBaoShiCfgByItemId(v.item_id)

                if stone_cfg.level <= jade_level_limit then
                    local use_num = temp_use_baoshi_list[stone_type][v.index] or 0
                    if v.num > use_num and v.item_id > temp_item_data.item_id then
                        temp_item_data = {item_id = v.item_id, index = v.index}
                    end
                end
            end

            if temp_item_data.item_id > 0 then
                local stone_cfg = self:GetBaoShiCfgByItemId(temp_item_data.item_id)
                if stone_cfg.level <= jade_level_limit then
                    local index = temp_item_data.index
                    temp_use_baoshi_list[stone_type][index] = temp_use_baoshi_list[stone_type][index] and temp_use_baoshi_list[stone_type][index] + 1 or 1
                    table.insert(one_key_list, {part_index = equip_body_index, slot_index = slot_index, bag_index = index})
                end
            end
        end
    end

    -- 排序
    SortTools.SortAsc(had_inlay_itemid_list, "item_id", "slot_index")
    local item_id = 0
    -- 替换上更高级
    for k,v in ipairs(had_inlay_itemid_list) do
        item_id = v.item_id
        temp_item_data = {item_id = 0, index = -1}
        for item_k, item_data in pairs(bag_stone_list) do
            local stone_cfg = self:GetBaoShiCfgByItemId(item_data.item_id)

            if stone_cfg.level <= jade_level_limit then
                local use_num = temp_use_baoshi_list[stone_type][item_data.index] or 0
                if item_data.num > use_num and item_data.item_id > item_id and item_data.item_id > temp_item_data.item_id then
                    temp_item_data = {item_id = item_data.item_id, index = item_data.index}
                end 
            end
        end

        if temp_item_data.item_id > 0 then
            local stone_cfg = self:GetBaoShiCfgByItemId(temp_item_data.item_id)
            if stone_cfg.level <= jade_level_limit then
                local index = temp_item_data.index
                temp_use_baoshi_list[stone_type][index] = temp_use_baoshi_list[stone_type][index] and temp_use_baoshi_list[stone_type][index] + 1 or 1
                table.insert(one_key_list, {part_index = equip_body_index, slot_index = v.slot_index, bag_index = index})
            end
        end
    end

    return one_key_list
end
































-------------------------------------------------------------------------------------------------------
---------------------------------------------- 宝石精炼 ------------------------------------------------
-------------------------------------------------------------------------------------------------------

-----------------------------------------------REMIND_START--------------------------------------------
function EquipmentWGData:GetEquipBSJinglianRemindNum()
    local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZYPJLEquipBodyDataList()

    if not IsEmptyTable(total_equip_body_data_list) then
        for k, v in pairs(total_equip_body_data_list) do
            if self:GetEquipBodyBSJLRemind(v.seq) > 0 then
                local open_func = function ()
                    FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_baoshi_jl)
                end
                
                MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_Bao, 1, open_func)
                
                return 1
            end
        end
    end

    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_Bao, 0)
	return 0
end

function EquipmentWGData:GetEquipBodyBSJLRemind(equip_body_seq)
    local equip_data_list = self:GetEquipBaoShiShowList(equip_body_seq, true)

    if not IsEmptyTable(equip_data_list) then
        for k, v in pairs(equip_data_list) do
            if self:GetBSJLRemindByPart(v.index) then
                return 1
            end
        end
    end
    
    return 0
end

function EquipmentWGData:GetBSJLRemindByPart(equip_body_index)
    local is_inlay = self:GetBSJLIsInlayStoneByPart(equip_body_index)
    if not is_inlay then
        return false
    end

	local refine_level = self:GetStoneRefineLevelByPart(equip_body_index)
    local equip_body_seq = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
    local max_refine_level = EquipBodyWGData.Instance:GetRefineLevelLimit(equip_body_seq)

    if max_refine_level <= refine_level then
        return false
    end

	local nxet_cost_cfg = self:GetStoneRefineByLevel(refine_level + 1)
    local cost_itemid = self:GetBSJLCostItemIdByPart(equip_body_index)

	if nxet_cost_cfg then
		local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_itemid)
		local need_num = nxet_cost_cfg.stuff_num
		return have_num >= need_num
	end

	return false
end


------------------------------------------------REMIND_END---------------------------------------------

-----------------------------------------------CFG_GET_START-------------------------------------------
function EquipmentWGData:GetBSJLCostItemIdByPart(equip_body_index)
    local item_id = 0
    local equip_part = equip_body_index % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM

    if self.stone_stone_jl_cost_cfg[equip_part] then
        item_id = self.stone_stone_jl_cost_cfg[equip_part].stuff_id
    end

    return item_id
end
------------------------------------------------CFG_GET_END--------------------------------------------

-------------------------------------------------CAL_START---------------------------------------------
-- 获取人物身上的装备宝石数据列表(带remind功能的)
function EquipmentWGData:GetEquipBaoShiShowList(equip_body_seq, not_need_sort)
	local data_list = EquipWGData.Instance:GetDataList()		--身上穿戴的装备列表

	local equip_data = {}

	if not data_list then
		return equip_data
	end

	local seq = 1
    local start_equip_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
    local end_equip_body_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + GameEnum.EQUIP_INDEX_XIANZHUO

	for k, v in pairs(data_list) do
		if v.index >= start_equip_index and v.index <= end_equip_body_index then
			equip_data[seq] = v
			equip_data[seq].stone = self.stone_infos[k]
			seq = seq + 1
		end
	end

	if not_need_sort then
		return equip_data
	end

	return EquipmentWGData.SortEquipTabForAwake(equip_data)
end

function EquipmentWGData:GetBSJLIsInlayStoneByPart(equip_body_index)
	local stone_infos = self:GetStoneInfoListByIndex(equip_body_index)

	if stone_infos then
		for k = 0, GameEnum.MAX_STONE_COUNT - 1 do
			if stone_infos[k] and stone_infos[k].item_id > 0 then
				return true
			end
		end
	end

	return false
end

-- 根据部位 获取所有宝石存在的属性值,  --need_preview 需要预览
function EquipmentWGData:GetAllBSAttrByPart(equip_body_index, need_preview)
	local bs_info = self:GetStoneInfoListByIndex(equip_body_index)

    local attr_list = {}
	if bs_info then
		for i = 0, GameEnum.MAX_STONE_COUNT - 1 do
			if bs_info[i] and bs_info[i].item_id > 0 then
				local stone_cfg = self:GetBaoShiCfgByItemId(bs_info[i].item_id)
                if not stone_cfg then
                    break
                end

                for j = 1, GameEnum.EQUIP_BAOSHI_ATTR_NUM do
        			local attr_id = stone_cfg["attr_type" .. j]
        			local value = stone_cfg["attr_val" .. j]
        			if value > 0 then
                        if not attr_list[attr_id] then
                            attr_list[attr_id] = {}
                            attr_list[attr_id].value = value
                            attr_list[attr_id].sort = j
                        else
                            attr_list[attr_id].value = attr_list[attr_id].value + value
                        end
        			end
        		end
			end
		end
	end

    -- 默认预览
    if IsEmptyTable(attr_list) and need_preview then
        local baoshi_type = self:GetStoneTypeByEquipPart(equip_body_index)
        local id_key = {26500, 26515}
        local stone_cfg = EquipmentWGData.Instance:GetBaoShiCfgByItemId(id_key[baoshi_type] or id_key[1])
        for j = 1, GameEnum.EQUIP_BAOSHI_ATTR_NUM do
            local attr_id = stone_cfg["attr_type" .. j]
            local value = stone_cfg["attr_val" .. j]
            if value > 0 then
                if not attr_list[attr_id] then
                    attr_list[attr_id] = {}
                    attr_list[attr_id].value = 0
                    attr_list[attr_id].sort = j
                else
                    attr_list[attr_id].value = 0
                end
            end
        end
    end

    local real_attr_list = {}

    if need_preview then
        for k, v in pairs(attr_list) do
            local data = {}
            data.attr_id = k
            data.value = v.value
            data.sort = v.sort
            table.insert(real_attr_list, data)
        end

        if not IsEmptyTable(real_attr_list) then
            table.sort(real_attr_list, SortTools.KeyLowerSorter("sort"))
        end
    else
        real_attr_list = attr_list
    end

	return real_attr_list
end

function EquipmentWGData:GetSingleBaoShiAttrList(item_id)
    local attr_list = {}
    local stone_cfg = self:GetBaoShiCfgByItemId(item_id)

    if IsEmptyTable(stone_cfg) then
        return attr_list
    end

    for j = 1, GameEnum.EQUIP_BAOSHI_ATTR_NUM do
        local attr_id = stone_cfg["attr_type" .. j]
        local value = stone_cfg["attr_val" .. j]
        if value > 0 then
            local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
            if not attr_list[attr_str] then
                attr_list[attr_str] = value
            else
                attr_list[attr_str] = attr_list[attr_str] + value
            end
        end
    end

    return attr_list
end

-- 获取玉魄精炼评分
function EquipmentWGData:GetBSJLScoreByPart(equip_part)
    local attr_list = self:GetAllBSAttrByPart(equip_part, false)

    local score = 0
    for k, v in pairs(attr_list) do
        local pingfen = TipWGData.Instance:GetXianPingSpecialAttrByOrder(k, v.value)
        score = score + pingfen
    end

    return score
end
-------------------------------------------------CAL_END----------------------------------------------






















function EquipmentWGData:GetStoneRefineByLevel(level)
    return self.stone_stone_refine_cfg[level]
end







function EquipmentWGData:GetBSJLOneRemindStoneInfo()
    local list_data = self:GetEquipBaoShiShowList()
	if IsEmptyTable(list_data) then
		return -1
	end

	-- 可精炼
	for k, equip_data in ipairs(list_data) do
		if self:GetBSJLRemindByPart(equip_data.index) then
            return equip_data.index
        end
	end

	return -1
end
--============================= 宝石精炼 end ====================================
