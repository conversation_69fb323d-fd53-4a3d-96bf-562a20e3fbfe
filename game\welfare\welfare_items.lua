UpGradeRender = UpGradeRender or BaseClass(BaseRender)
function UpGradeRender:__init()

end

function UpGradeRender:LoadCallBack()
	self.item_list = {}


	self.img_sjlingqu = self.node_list["img_sjlingqu"]
	self.btn_lingqu = self.node_list["btn_lingqu"]
	self.img_weilingqu = self.node_list["img_weilingqu"]
	self.node_list.lbl_surplus:SetActive(false)
	self.btn_lingqu.button:AddClickListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
end

function UpGradeRender:__delete()
	self.item_list = nil
	self.btn_lingqu = nil
	self.img_sjlingqu = nil
end

function UpGradeRender:OnClickRewardHnadler(sender)
	if self.data.status == SEVEN_COMPLETE_STATUS.WEIDACHENG then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.TipLessLeveal)
		WelfareWGData.Instance:SetDataFrom(true)
	elseif self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU then
		WelfareWGData.Instance:SetDataFrom(true)
		WelfareWGCtrl.Instance:SendWelfareUplevelReward(self.data.seq,GITT_LINGQU_TYPE.UPGRADE)
		AudioService.Instance:PlayRewardAudio()
	end
end

function UpGradeRender:OnFlush()
	if not self.data then
		return
	end

	self.img_sjlingqu.text.text = self.data.level..Language.Welfare.JiLiBao
	local status = self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU or self.data.status == SEVEN_COMPLETE_STATUS.WEIDACHENG 
	XUI.SetButtonEnabled(self.btn_lingqu, self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU)
	self.btn_lingqu:SetActive(self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU)
	self.img_weilingqu:SetActive(self.data.status == SEVEN_COMPLETE_STATUS.WEIDACHENG)
	-- effect = self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU
	self.btn_lingqu:SetActive(self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU)
	self.node_list["image_title"]:SetActive(not status)

	if self.data.status == SEVEN_COMPLETE_STATUS.WEIDACHENG then
		-- self.btn_lingqu:setTitleText(Language.Common.LingQu)
		self.node_list.lbl_btn_text.text.text = Language.Welfare.LingQuSpace
	elseif self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU then

		self.node_list.lbl_btn_text.text.text = Language.Welfare.LingQuSpace
	elseif self.data.status == SEVEN_COMPLETE_STATUS.YILINGQU then
		self.node_list.lbl_btn_text.text.text = Language.Common.YiLingQu
	end

	self.node_list.lbl_surplus:SetActive(false)
	if  self.data.limit_or_not == 1 then
		local level_num  = WelfareWGData.Instance:GetUpLevelInfo(self.data.seq + 1)
		if level_num ~= nil then
			-- print_error(self.data.limit_count,level_num)
			local shengyu_num = (self.data.limit_count - level_num) >= 0 and  (self.data.limit_count - level_num) or 0
			local str = string.format(Language.Welfare.ResidueDegree,shengyu_num)
			self.node_list.lbl_surplus.text.text = str
			self.node_list.lbl_surplus:SetActive(true)
			if  self.data.limit_count <= level_num then
				XUI.SetButtonEnabled(self.btn_lingqu, false)
				-- effect = false
				self.node_list.lbl_btn_text.text.text = Language.Welfare.RewardOver
			end
		end
	end
	-- if effect and status then
	-- 	self.node_list.effect:SetActive(true)
	-- else
	-- 	self.node_list.effect:SetActive(false)
	-- end

	if self.data.gift_data then
		for k, v in pairs(self.item_list) do
			v:SetData(nil)
		end
		-- print_error("self.data.gift_data",self.data.gift_data,"\n","self.data.gift_data.item_data",self.data.gift_data.item_data)
		-- for k, v in pairs(self.data.gift_data.item_data) do
		for k, v in pairs(self.data.gift_data) do
			if not self.item_list[k+1] then
				if self.node_list["ph_cell_"..k+1] then
					self.item_list[k+1] = ItemCell.New(self.node_list["ph_cell_"..k+1])
				end
			end
			local itemvo = CommonStruct.ItemDataWrapper()
			itemvo.item_id = v.id or v.item_id
			itemvo.num = v.num
			itemvo.is_bind = v.isbind
			if v.star_level then
				itemvo.star_level = v.star_level
			end
			local data_list = {}
			-- local item_list = ItemWGData.Instance:GetGiftConfig(itemvo.item_id)
			local _, item_type = ItemWGData.Instance:GetItemConfig(itemvo.item_id)

			-- if v.id == 28614 then   --这个礼包不解析礼包
			-- 	item_list = nil
			-- end
			-- if item_list and next(item_list) ~= nil then
			-- 	for k,v in pairs(item_list.item_data) do
			-- 		if v.limit_prof == RoleWGData.Instance.role_vo.prof % 10 or v.limit_prof == 5 then
			-- 			table.insert(data_list, v)
			-- 		end
			-- 	end

			-- 	if next(data_list) ~= nil then
			-- 		if data_list[1].param and data_list[1].param.star_level > 0 then
			-- 			self.item_list[k+1]:SetItemTipFrom(ItemTip.FROM_WELFARE_ITEM)
			-- 		end
			-- 		self.item_list[k+1]:SetData({item_id = data_list[1].id, num = data_list[1].num, is_bind = data_list[1].is_bind, param = data_list[1].param})
			-- 		-- self:ReturnNeedShow(k+1,{item_id = data_list[1].id, num = data_list[1].num, is_bind = data_list[1].is_bind, param = data_list[1].param})
			-- 	end
			if item_type == GameEnum.ITEM_BIGTYPE_GIF then
				self.item_list[k+1]:SetItemTipFrom(ItemTip.FROM_WELFARE_ITEM)
				self.item_list[k+1]:SetData(itemvo)
			elseif item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
				self.item_list[k+1]:SetItemTipFrom(ItemTip.FROM_FULILOGIN)
				self.item_list[k+1]:SetData(itemvo)
			else
				if self.item_list[k+1] then
					self.item_list[k+1]:SetItemTipFrom(ItemTip.FROM_NORMAL)
					self.item_list[k+1]:SetData(itemvo)
					-- self:ReturnNeedShow(k+1,itemvo)
				end
			end

			for k, v in pairs(self.item_list) do
				v:SetActive(v.data ~= nil)
			end
		end
	end
end


--右下角特殊处理
function UpGradeRender:ReturnNeedShow(index,data)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
	self.item_list[index]:SetLeftTopTextVisible(false)
	if big_type == GameEnum.ITEM_BIGTYPE_EXPENSE then
		--绑定元宝,铜币
		if 8 == item_cfg.use_type or 2 == item_cfg.use_type then
			if data.num > 0 then
				local coin = CommonDataManager.ConverExp(item_cfg.param1*data.num,true,true)
				self.item_list[index]:SetRightBottomTextVisible(true)
				self.item_list[index]:SetRightBottomColorText(coin)
			end
		end
	elseif data.num > 1 then
		self.item_list[index]:SetRightBottomTextVisible(true)
	else
		self.item_list[index]:SetRightBottomTextVisible(false)
	end
end

function UpGradeRender:CreateSelectEffect()

end

function UpGradeRender:GetGuideLinqu()
	return self.btn_lingqu, BindTool.Bind1(self.OnClickRewardHnadler, self)
end


-------------------------------------------
UpGradeRewardRender = UpGradeRewardRender or BaseClass(BaseRender)

function UpGradeRewardRender:__init()

end

function UpGradeRewardRender:LoadCallBack()

end

function UpGradeRewardRender:__delete()

end

function UpGradeRewardRender:OnFlush()

end


----------------------------------------------------------------------------
--FriendRankItemRender
----------------------------------------------------------------------------

FriendRankItemRender = FriendRankItemRender or BaseClass(BaseRender)
function FriendRankItemRender:__init()
end

function FriendRankItemRender:CreateChild()
	self.rank = self.node_list["lbl_rank"]
	self.name = self.node_list["lbl_name"]
	self.level = self.node_list["lbl_level"]

end

function FriendRankItemRender:__delete()
end

function FriendRankItemRender:OnFlush()
	if nil == self.data then
		return
	end

	self.rank.text.text = self.index
	self.name.text.text = self.data.role_name
	self.level.text.text = self.data.level
	local icon = self.index % 2 ~= 0 and "bg_list" or "bg_list_02"
	local bundle, asset = ResPath.GetCommonBackGround(icon)
	self.node_list["img_friend_item_bg"].image:LoadSprite(bundle, asset)
end

function FriendRankItemRender:CreateSelectEffect()

end

----------------------------------------------------------------------------
--FriendRewardItemRender
----------------------------------------------------------------------------
FriendRewardItemRender = FriendRewardItemRender or BaseClass(BaseRender)
function FriendRewardItemRender:__init()
	self.is_show_tips = false
end

function FriendRewardItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function FriendRewardItemRender:LoadCallBack()
	self.lbl_item_tip = self.node_list["lbl_item_tip"]
	self.lbl_jindu = self.node_list["lbl_jindu"]
	self.btn_get_reward = self.node_list["btn_get_reward"]
	-- self.btn_get_reward:addClickEventListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
	XUI.AddClickEventListener(self.btn_get_reward, BindTool.Bind1(self.OnClickRewardHnadler, self))

	self.item_cell = ItemCell.New(self.node_list["ph_cell"])
	self.item_cell:SetIsShowTips(false)
	self.item_cell:SetClickCallBack(BindTool.Bind1(self.ItemcellClickCallBack, self))
end

function FriendRewardItemRender:OnClickRewardHnadler(sender)
	if self.data.status == SEVEN_COMPLETE_STATUS.WEIDACHENG then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.TipRewardError)
	elseif self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU then
		WelfareWGCtrl.Instance:SendFriendinviteOperate(FRIENDINVITE_TYPE.FRIENDINVITE_FETCH_REWARD, self.data.index)
	elseif self.data.status == SEVEN_COMPLETE_STATUS.YILINGQU then
	end
end

function FriendRewardItemRender:OnFlush()
	if nil == self.data then
		return
	end

	self.data.cur_num = self.data.cur_num or 0
	self.lbl_jindu.text.text = self.data.cur_num .. "/" .. self.data.need_num
	self.lbl_item_tip.text.text = string.format(Language.Welfare.FriendRewardItemTip, self.data.need_num, self.data.role_level)
	if self.data.index == 0 then
		self.lbl_item_tip.text.text = Language.Welfare.FriendRewardItemTip0
	end
	XUI.SetButtonEnabled(self.btn_get_reward, self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU or self.data.status == SEVEN_COMPLETE_STATUS.WEIDACHENG)
	local percent = math.floor((self.data.cur_num / self.data.need_num) * 100)
	local percent = percent <= 100 and percent or 100
	self.node_list["prog_friend_yq_jindu"].slider.value = percent

	if self.data.status == SEVEN_COMPLETE_STATUS.WEIDACHENG then
		self.node_list["btn_get_reward_text"].text.text = Language.Common.LingQu
	elseif self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU then
		self.node_list["btn_get_reward_text"].text.text = Language.Common.LingQu
	elseif self.data.status == SEVEN_COMPLETE_STATUS.YILINGQU then
		self.node_list["btn_get_reward_text"].text.text = Language.Common.YiLingQu
	end

	self.item_cell:SetData({item_id = self.data.reward_item.item_id, num = self.data.reward_item.num, is_bind = self.data.reward_item.is_bind})
	-- self.is_show_tips = (self.data.status == SEVEN_COMPLETE_STATUS.YILINGQU) -- 已领取的item不显示提示
	if not self.is_show_tips then
		self.item_cell:SetIsShowTips(true)
	else
		self.item_cell:SetIsShowTips(false)
	end
	-- self.item_cell:MakeGray(self.data.status == 0)	-- 已领取的item_cell变灰色
end

function FriendRewardItemRender:ItemcellClickCallBack()
	if not self.is_show_tips then
		return
	end
	if self.click_callback then
		self.click_callback(self)
	end
end

function FriendRewardItemRender:OnSelectChange(is_select)
	if not self.node_list["img_bg"] then
		return
	end
	if is_select then
		self.node_list["img_bg"].image:LoadSprite(ResPath.GetCommonButtonToggle_atlas("btn_side_hl_02"))
	else
		self.node_list["img_bg"].image:LoadSprite(ResPath.GetCommonButtonToggle_atlas("btn_side_02"))
	end
end
