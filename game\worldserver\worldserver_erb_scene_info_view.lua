WorldServerERBSceneInfoView = WorldServerERBSceneInfoView or BaseClass(SafeBaseView)

function WorldServerERBSceneInfoView:__init()
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_everyday_recharge_boss_task")
end

function WorldServerERBSceneInfoView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_special_boss"], BindTool.Bind(self.OnClickSpecialBoss, self))

    if not self.day_pass then
        self.day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPass, self))
    end

    self:SetOutFbTip()
end

function WorldServerERBSceneInfoView:ReleaseCallBack()
    if nil ~= self.day_pass then
		GlobalEventSystem:UnBind(self.day_pass)
		self.day_pass = nil
	end

    if CountDownManager.Instance:HasCountDown("WorldServerERBSceneInfoView") then
        CountDownManager.Instance:RemoveCountDown("WorldServerERBSceneInfoView")
    end
end

function WorldServerERBSceneInfoView:OnFlush()
    local scene_id = Scene.Instance:GetSceneId()
    local specil_boss_cfg = BossWGData.Instance:GetSceneSpecialBossCfgBySceneId(scene_id)
    local has_special_data = not IsEmptyTable(specil_boss_cfg)
    self.node_list.btn_special_boss:CustomSetActive(has_special_data)

    if has_special_data then
        local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. specil_boss_cfg.big_icon)
        self.node_list.icon_boss.image:LoadSprite(bundle, asset, function()
            self.node_list.icon_boss.image:SetNativeSize()
        end)

        self.node_list.boss_name.text.text = specil_boss_cfg.special_show_name

        local boss_info = BossWGData.Instance:GetERBBossInfoByBossId(specil_boss_cfg.boss_id)
        local next_refresh_time = boss_info.next_refresh_time or 0
        local alive = next_refresh_time == 0

        self.node_list.desc_rebirth_progress:CustomSetActive(not alive)
        self.node_list.desc_to_kill_boss:CustomSetActive(alive)
        self.node_list.can_go_effect:CustomSetActive(alive)

        if alive then
            self.node_list.boss_rebirth_slider.image.fillAmount = 1
        else
            local need_kill_boss_num = specil_boss_cfg.kill_boss_num
            local kill_boss_num = BossWGData.Instance:GetERBSceneKillBossNumBySceneId(scene_id)
            local target_show_num = kill_boss_num % need_kill_boss_num

            self.node_list.desc_rebirth_progress.text.text = target_show_num .. "/" .. need_kill_boss_num
            self.node_list.boss_rebirth_slider.image.fillAmount = target_show_num / need_kill_boss_num
        end
    end
end

function WorldServerERBSceneInfoView:OnClickSpecialBoss()
    local scene_id = Scene.Instance:GetSceneId()
    local specil_boss_cfg = BossWGData.Instance:GetSceneSpecialBossCfgBySceneId(scene_id)

    if not IsEmptyTable(specil_boss_cfg) then
        GuajiWGCtrl.Instance:StopGuaji()
        Scene.Instance:ClearAllOperate()

        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic ~= nil then
            scene_logic:ClearGuaJiInfo()
        end

        local range = BossWGData.Instance:GetMonsterRangeByid(specil_boss_cfg.boss_id)
        local pos_x, pos_y = specil_boss_cfg.x_pos, specil_boss_cfg.y_pos

        GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic ~= nil then
                local mian_role = Scene.Instance:GetMainRole()
                if mian_role ~= nil and not mian_role:IsDeleted() then
                    local main_pos_x, main_pos_y = mian_role:GetLogicPos()
                    if GameMath.GetDistance(pos_x, pos_y, main_pos_x, main_pos_y, false) <= range * range then
                        scene_logic:SetGuaJiInfoPos(pos_x, pos_y, range, specil_boss_cfg.boss_id)
                    end
                end
            end

            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        end)

    GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, range)
    end
end

function WorldServerERBSceneInfoView:SpecialBossRebirthNotice()
    local scene_id = Scene.Instance:GetSceneId()
    local specil_boss_cfg = BossWGData.Instance:GetSceneSpecialBossCfgBySceneId(scene_id)

    if not IsEmptyTable(specil_boss_cfg) then
        TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.ErbSpecialBossRebirthNotice, specil_boss_cfg.special_show_name))
    end
end

function WorldServerERBSceneInfoView:SetOutFbTip()
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("WorldServerERBSceneInfoView") then
            CountDownManager.Instance:RemoveCountDown("WorldServerERBSceneInfoView")
        end

        CountDownManager.Instance:AddCountDown("WorldServerERBSceneInfoView", 
            BindTool.Bind(self.UpdateTime, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    end
end

function WorldServerERBSceneInfoView:UpdateTime(now_time, total_time)
    local time = math.ceil(total_time - now_time)

    if time <= 5 then
        if self.node_list.out_fb_root then
            self.node_list.out_fb_root:CustomSetActive(true)
        end

        if self.node_list.desc_out_fb_tip then
            self.node_list.desc_out_fb_tip.text.text = string.format(Language.Boss.ErbOutFbTip, time)
        end
    end
end

function WorldServerERBSceneInfoView:OnComplete()
    if self.node_list.out_fb_root then
        self.node_list.out_fb_root:CustomSetActive(false)
    end
end

function WorldServerERBSceneInfoView:OnDayPass()
    self:SetOutFbTip()
    self:OnComplete()
end