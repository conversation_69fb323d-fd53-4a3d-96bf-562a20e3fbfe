﻿using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(UGUITweenFOV))]
public class TweenFOVEditor : UITweenerEditor {

	public override void OnInspectorGUI ()
	{
		GUILayout.Space(6f);
		UGUITweenEditorTools.SetLabelWidth(120f);

		UGUITweenFOV tw = target as UGUITweenFOV;
		GUI.changed = false;

		float from = EditorGUILayout.Slider("From", tw.from, 1f, 180f);
		float to = EditorGUILayout.Slider("To", tw.to, 1f, 180f);

		if (GUI.changed)
		{
			UGUITweenEditorTools.RegisterUndo("Tween Change", tw);
			tw.from = from;
			tw.to = to;
			UGUITweenEditorTools.SetDirty(tw);
		}

		DrawCommonProperties();
	}
}
