﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TopViewMapCameraWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(TopViewMapCamera), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("SetIgnoreListEnabled", SetIgnoreListEnabled);
		<PERSON><PERSON>unction("SnapCameraEulerAngles", SnapCameraEulerAngles);
		<PERSON><PERSON>ction("TransformWorldToUV", TransformWorldToUV);
		<PERSON><PERSON>Function("TransformUVToWorld", TransformUVToWorld);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("MapTexture", get_MapTexture, set_MapTexture);
		<PERSON><PERSON>("MapTextureWidth", get_MapTextureWidth, null);
		<PERSON><PERSON>("MapTextureHeight", get_MapTextureHeight, null);
		<PERSON><PERSON>();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIgnoreListEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TopViewMapCamera obj = (TopViewMapCamera)ToLua.CheckObject(L, 1, typeof(TopViewMapCamera));
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIgnoreListEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SnapCameraEulerAngles(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TopViewMapCamera obj = (TopViewMapCamera)ToLua.CheckObject(L, 1, typeof(TopViewMapCamera));
			obj.SnapCameraEulerAngles();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TransformWorldToUV(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TopViewMapCamera obj = (TopViewMapCamera)ToLua.CheckObject(L, 1, typeof(TopViewMapCamera));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector2 o = obj.TransformWorldToUV(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TransformUVToWorld(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TopViewMapCamera obj = (TopViewMapCamera)ToLua.CheckObject(L, 1, typeof(TopViewMapCamera));
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			UnityEngine.Vector3 o = obj.TransformUVToWorld(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MapTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TopViewMapCamera obj = (TopViewMapCamera)o;
			UnityEngine.Texture2D ret = obj.MapTexture;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MapTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MapTextureWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TopViewMapCamera obj = (TopViewMapCamera)o;
			int ret = obj.MapTextureWidth;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MapTextureWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MapTextureHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TopViewMapCamera obj = (TopViewMapCamera)o;
			int ret = obj.MapTextureHeight;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MapTextureHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MapTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TopViewMapCamera obj = (TopViewMapCamera)o;
			UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 2, typeof(UnityEngine.Texture2D));
			obj.MapTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MapTexture on a nil value");
		}
	}
}

