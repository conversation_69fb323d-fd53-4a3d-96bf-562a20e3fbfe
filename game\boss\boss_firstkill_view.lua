BossFirstKillView = BossFirstKillView or BaseClass(SafeBaseView)
function BossFirstKillView:__init()
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_firstkill")
    self.tab_index = 10
end

function BossFirstKillView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["mh_btn"], BindTool.Bind(self.OnClickSwitch, self, BossViewIndex.WorldBoss))
    XUI.AddClickEventListener(self.node_list["my_btn"], BindTool.Bind(self.OnClickSwitch, self, BossViewIndex.VipBoss))
    XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind1(self.Close, self))

    if self.firstkill_btn_list == nil then
        self.firstkill_btn_list = AsyncListView.New(BossFirstLayerBtnRender, self.node_list["layer_btn_list"])
        self.firstkill_btn_list:SetSelectCallBack(BindTool.Bind1(self.FirstKillBtnSelectCallBack,self))
    end

    -- if self.boss_firstkill_list == nil then
    --     local bundle = "uis/view/boss_ui_prefab"
	-- 	local asset = "boss_firstkill_item"
    --     self.boss_firstkill_list = AsyncBaseGrid.New()
    --     self.boss_firstkill_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["boss_firstkill_list"],
	-- 		assetBundle = bundle, assetName = asset, itemRender = BossFirstKillItemRender})
    --     self.boss_firstkill_list:SetStartZeroIndex(false)
    -- end

    if self.boss_firstkill_list == nil then
        self.boss_firstkill_list = AsyncListView.New(BossFirstKillItemRender, self.node_list["boss_firstkill_list"])
    end

    --self:OnClickSwitch(self.tab_index)
    BossWGData.Instance:SetCurFirstkillLayer(nil)

    local world_boss_red, world_show_view = BossWGData.Instance:GetWorldBossFirstKillRed()
    local vip_boss_red, vip_show_view = BossWGData.Instance:GetVipBossFirstKillRed()

    if world_show_view ~= nil then
        self:OnClickSwitch(world_show_view)
    elseif vip_show_view ~= nil then
        self:OnClickSwitch(vip_show_view)
    else
        self:OnClickSwitch(self.tab_index)
    end
end

function BossFirstKillView:ReleaseCallBack()
    if self.firstkill_btn_list then
        self.firstkill_btn_list:DeleteMe()
        self.firstkill_btn_list = nil
    end

    if self.boss_firstkill_list then
        self.boss_firstkill_list:DeleteMe()
        self.boss_firstkill_list = nil
    end
end

function BossFirstKillView:OpenCallBack()
    self.tab_index = BossWGData.Instance:GetCurFirstKillShowView()
end

function BossFirstKillView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushBtnList()
			self:RefreshFirstKillList()
		elseif "jump_info" == k then
			self.tab_index = v.tab_index
			BossWGData.Instance:SetCurFirstKillShowView(v.tab_index)
			BossWGData.Instance:SetCurFirstkillLayer(v.layer)
			self:FlushBtnList(true)
			self:RefreshFirstKillList(v.boss_id)
		end
	end
end

function BossFirstKillView:OnClickSwitch(show_index)
    self.tab_index = show_index

    local is_open, tip
    if self.tab_index == BossViewIndex.WorldBoss then
		is_open, tip = FunOpen.Instance:GetFunIsOpenedByTabName("boss_world", true)
	elseif self.tab_index == BossViewIndex.VipBoss then
		is_open, tip = FunOpen.Instance:GetFunIsOpenedByTabName("boss_vip", true)
	end

	if not is_open then
		if tip and tip ~= "" then
			SysMsgWGCtrl.Instance:ErrorRemind(tip)
		end
		return
	end

    self:ChangeTabPanel()
    if BossWGData.Instance:GetCurFirstKillShowView() == self.tab_index then
        return
    end

    BossWGData.Instance:SetCurFirstKillShowView(show_index)
    self:Flush()
end

function BossFirstKillView:ChangeTabPanel()
    self.node_list.mh_normal:SetActive(self.tab_index ~= BossViewIndex.WorldBoss)
    self.node_list.mh_hight:SetActive(self.tab_index == BossViewIndex.WorldBoss)
    self.node_list.my_normal:SetActive(self.tab_index ~= BossViewIndex.VipBoss)
    self.node_list.my_hight:SetActive(self.tab_index == BossViewIndex.VipBoss)
end

function BossFirstKillView:FirstKillBtnSelectCallBack(btnself)
    local cur_layer = nil
    local default_index = BossWGData.Instance:GetCurFirstkillLayer()
    local show_index = BossWGData.Instance:GetCurFirstKillShowView()

    if self.tab_index == BossViewIndex.VipBoss then
        if show_index == self.tab_index and default_index == btnself.data.level then
            return
        end
        cur_layer = btnself.data.level
    else
        if show_index == self.tab_index and default_index == btnself.index then
            return
        end
        cur_layer = btnself.index
    end

    BossWGData.Instance:SetCurFirstkillLayer(cur_layer)
    self:RefreshFirstKillList()
end

function BossFirstKillView:FlushBtnList(jump)
    self:OnClickSwitch(self.tab_index)

    local world_boss_red, _ = BossWGData.Instance:GetWorldBossFirstKillRed()
    local vip_boss_red, _ = BossWGData.Instance:GetVipBossFirstKillRed()
    if self.node_list["world_boss_red"] then
        self.node_list["world_boss_red"]:SetActive(world_boss_red > 0)
    end

    if self.node_list["vip_boss_red"] then
        self.node_list["vip_boss_red"]:SetActive(vip_boss_red > 0)
    end

    local btn_list = BossWGData.Instance:GetBossLayerCfg(self.tab_index)
    if self.tab_index == BossViewIndex.VipBoss then
		btn_list = BossWGData.Instance:GetVipBossLayerLimit(btn_list)
	end

	local default_index = BossWGData.Instance:GetCurFirstkillLayer()
	if not jump then
		if self.tab_index == BossViewIndex.VipBoss then
			if BossWGData.Instance:GetVipBossLayerIndex() ~= -1 then
				default_index = BossWGData.Instance:GetVipBossLayerIndex()
			else
				default_index = BossWGData.Instance:GetVipBossDefaultLayer(btn_list)
			end
			default_index = default_index == 0 and 1 or default_index --容错保证不会选到0
		elseif self.tab_index == BossViewIndex.WorldBoss then
			if BossWGData.Instance:GetWorldBossLayerIndex() ~= -1 then
				default_index = BossWGData.Instance:GetWorldBossLayerIndex()
			else
				default_index = BossWGData.Instance:GetWorldBossDefLayer()
			end
		end
	end

    self.firstkill_btn_list:SetDataList(btn_list or {})
    self.firstkill_btn_list:JumpToIndex(default_index)
end

function BossFirstKillView:RefreshFirstKillList(jump_boss_id)
    local list_data = {}
    local cur_layer = BossWGData.Instance:GetCurFirstkillLayer()
    if self.tab_index == BossViewIndex.VipBoss then --混沌魔域
        if cur_layer == nil then
			return
		end
        list_data = BossWGData.Instance:GetVipBossListByIndex(cur_layer)
    elseif self.tab_index == BossViewIndex.WorldBoss then --蛮荒魔谷
        if cur_layer == nil then
            return
        end
        list_data = BossWGData.Instance:GetWorldBossListByLayer(cur_layer)
    end

    if list_data then
        table.sort(list_data, function(a, b)
            local is_show_global_a = BossWGData.Instance:GetIsWorldFirstKilledByBossId(a.boss_id)
            local is_show_global_b = BossWGData.Instance:GetIsWorldFirstKilledByBossId(b.boss_id)
    
            local sort_a = 0
            if not is_show_global_a then
                sort_a = 100
            end
    
            local sort_b = 0
            if not is_show_global_b then
                sort_b = 100
            end
    
            if sort_a == sort_b then
                if a.boss_level < b.boss_level then
                    sort_b = 100
                end
            end
    
            return sort_a < sort_b
        end)
    end

	self.boss_firstkill_list:SetDataList(list_data or {})
	if list_data then
		for k, v in pairs(list_data) do
			if jump_boss_id then
				if jump_boss_id == v.boss_id then
					self.boss_firstkill_list:JumpToIndex(k)
					break
				end
			elseif BossWGData.Instance:IsShowRedPoint(v.boss_id) then
				self.boss_firstkill_list:JumpToIndex(k)
				break
			end
		end
	end
end

-------------------------------BossFirstKillItemRender-------------------------------
BossFirstKillItemRender = BossFirstKillItemRender or BaseClass(BaseRender)
function BossFirstKillItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["boss_xs_box"], BindTool.Bind(self.FirstKillBoxRecord, self))
	XUI.AddClickEventListener(self.node_list["btn_goto"], BindTool.Bind(self.FirstKillBossGoto, self))
end

function BossFirstKillItemRender:__delete()
	self:KillBoxinImgShakeAnim()
end

function BossFirstKillItemRender:SendKillWorldBoss(boss_data)
    local scene_type = Scene.Instance:GetSceneType()
	local same_scene = Scene.Instance:GetSceneId() == boss_data.scene_id
    if not BossWGData.IsSingleBoss(scene_type) and same_scene then--同场景内直接引导
		--self:Close()
		BossWGCtrl.Instance:MoveToBoss(boss_data.boss_id, SELECT_BOSS_REASON.VIEW)
    else
        local show_index = BossWGData.Instance:GetCurFirstKillShowView()
        if show_index == BossViewIndex.VipBoss then            -- VipBOSS
            if boss_data and boss_data.need_role_level and boss_data.need_role_level > RoleWGData.Instance:GetRoleLevel() then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelUnLock2, RoleWGData.GetLevelString(boss_data.need_role_level)))
				return
            end
            local cur_layer = BossWGData.Instance:GetCurFirstkillLayer()
            BossWGData.Instance:SetCurSelectBossID(show_index , cur_layer, boss_data.boss_id)
			BossWGCtrl.Instance:OnEnterVipBoss()
		elseif show_index == BossViewIndex.WorldBoss then          -- 世界boss
			if boss_data and boss_data.need_role_level and boss_data.need_role_level > RoleWGData.Instance:GetRoleLevel() then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LevelUnLock2, RoleWGData.GetLevelString(boss_data.need_role_level)))
				return
            end
            local cur_layer = BossWGData.Instance:GetCurFirstkillLayer()
            BossWGData.Instance:SetCurSelectBossID(show_index , cur_layer, boss_data.boss_id)
			BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.ENTER, boss_data.layer)
			BossWGData.Instance:SetOldBossID(BossViewIndex.WorldBoss, boss_data.boss_id, cur_layer)
			BossWGData.Instance:SetBossEnterFlag(false)
        end

    end
end

--立即前往
function BossFirstKillItemRender:FirstKillBossGoto()
    if TaskWGCtrl.Instance:IsFly() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FlyShoeCanNotDo)
		return
	end

    if IsEmptyTable(self.data) then
		return
	end

    local show_index = BossWGData.Instance:GetCurFirstKillShowView()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    if role_level - self.data.boss_level >= self.data.max_delta_level then
        if nil == self.alert_window then
            self.alert_window = Alert.New(nil, nil, nil, nil, true)
        end
        self.alert_window:SetLableString(Language.Boss.KillLevelHighTips)
        self.alert_window:SetShowCheckBox(true,"Boss"..show_index)
        self.alert_window:SetBtnDislocation(true)
        self.alert_window:SetCheckBoxDefaultSelect(false)
        self.alert_window:SetCancelString(Language.Boss.Cancel)
        self.alert_window:SetOkString(Language.Boss.QianWang)
        self.alert_window:SetOkFunc(
            function()
                self:SendKillWorldBoss(self.data)
            end)
        self.alert_window:Open()
    else
        self:SendKillWorldBoss(self.data)
    end
end

--点击宝箱
function BossFirstKillItemRender:FirstKillBoxRecord()
    if IsEmptyTable(self.data) then
		return
	end

    local _, can_get = BossWGData.Instance:GetIsWorldFirstKilledByBossId(self.data.boss_id)
    local boss_info = BossWGData.Instance:GetBossInfoByBossId(self.data.boss_id)
    if can_get and boss_info.world_firstkill_reward then
    	local view_show_index = BossWGData.Instance:GetCurFirstKillShowView()
        local boss_type = view_show_index == BossViewIndex.WorldBoss and 0 or 1
        BossWGCtrl.Instance:OpenRewardChooseView(self.data.boss_id, boss_type)
    else
        -- BossWGCtrl.Instance:OpenFirstRewardView(self.data.boss_id)       --首杀奖励预览.

        local boss_cfg = BossWGData.Instance:GetBossInfoByBossId(self.data.boss_id)
        local data = ItemWGData.Instance:GetItemListInGift(boss_cfg.world_firstkill_reward)
        local data_list =
        {
            view_type = RewardShowViewType.Normal,
            reward_item_list = ListIndexStartFromZero(data)
        }
        RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
    end
end

function BossFirstKillItemRender:OnFlush()
    if IsEmptyTable(self.data) then
		return
	end

    self.show_global_box = false
    self.is_grey_show = false
    --self.show_fk_flag = false

    local role_level = RoleWGData.Instance:GetRoleLevel()
	if self.data.need_role_level and role_level < self.data.need_role_level then
		self.node_list["boss_lv"].text.text = "LV." .. self.data.boss_level
        --self.node_list["be_kill_flag"]:SetActive(false)
        self.node_list["lbl_time"].text.text = string.format(Language.Common.LevelUnLock3, RoleWGData.GetLevelString(self.data.need_role_level))

        self.is_grey_show = true
	else
		if role_level - self.data.boss_level >= self.data.max_delta_level then
            self.is_grey_show = true
            self.node_list["lbl_time"].text.text = Language.Boss.LevelHighLimit
            --self.node_list["be_kill_flag"]:SetActive(false)
        end
	end

    self.node_list["btn_goto"]:SetActive(not self.is_grey_show)
    self.node_list["lbl_time"]:SetActive(self.is_grey_show)

    self:OnFlushFirstKill()

    -- if self.data.need_role_level and role_level > self.data.need_role_level then
    --     if role_level - self.data.boss_level < self.data.max_delta_level then
    --         local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
    --         local time = 0
    --         if boss_server_info ~= nil then
    --             time = (boss_server_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime()
    --         end

    --         local state = time > 1
    --         self.node_list["be_kill_flag"]:SetActive(state and not self.show_fk_flag and not self.show_global_box)
    --     end
	-- end





    self.node_list["boss_name"].text.text = self.data.boss_name
    self.node_list["boss_lv"].text.text = "LV." .. self.data.boss_level

    local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
    local monster_info = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
    if monster_info and  monster_info.boss_jieshu > 0 and
	(boss_server_info == nil or (boss_server_info.is_concern and boss_server_info.is_concern <= 0)) then
        self.node_list["layer_num"].text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(monster_info.boss_jieshu))
    end

    local bundle, asset = ResPath.GetBossUI("a3_boos_head_" .. monster_info.resid)
	self.node_list["img_boss_head"].image:LoadSprite(bundle,asset, function()
		self.node_list["img_boss_head"].image:SetNativeSize()
	end)

end


function BossFirstKillItemRender:OnFlushFirstKill()
    --宝箱
    local is_show_person, can_get = BossWGData.Instance:GetIsPersonFirstKilledByBossId(self.data.boss_id)
    local is_show_global, can_get_golbal = BossWGData.Instance:GetIsWorldFirstKilledByBossId(self.data.boss_id)
    local cur_count, total_count = BossWGData.Instance:GetWorldFirstKilledProgress(self.data.boss_id)

    -- 有宝箱领取
    if is_show_global and can_get_golbal then
        self.show_global_box = true
    -- 未打过boss，全服数量有剩余
    elseif is_show_person and not can_get and total_count > cur_count then
        self.show_global_box = true
    -- 首杀（未打过boss or 有个人红包）
    elseif (not is_show_person) and (not can_get_golbal) then
        self.show_global_box = true
    end

    --初始化.
    self.node_list["boss_xs_root"]:SetActive(true)
    self.node_list["be_get_flag"]:SetActive(false)

    if can_get_golbal then
        if not self.box_img_tween_shake then
            self.box_img_tween_shake = DG.Tweening.DOTween.Sequence()
            UITween.ShakeAnimi(self.node_list["boss_xs_box"].transform, self.box_img_tween_shake, 1)
        end
    else
        self:KillBoxinImgShakeAnim()
        self.node_list["boss_xs_box"].transform.rotation = Quaternion.Euler(0, 0, 0)
    end

    if total_count == -1 then  -- 首层boss首杀奖励无上限
        self.node_list["xs_box_txt"].text.text = Language.Boss.FirstKillProgress5
    else
        self.node_list["xs_box_txt"].text.text = string.format(Language.Boss.FirstKillProgress4, total_count - cur_count)
    end

    if not is_show_global and not (total_count - cur_count <= 0) then
        self.node_list["be_get_flag"]:SetActive(not can_get_golbal)
        self.node_list["xs_box_txt"].text.text = string.format(Language.Boss.FirstKillProgress6)
        if not can_get_golbal then
            self.node_list["boss_xs_root"]:SetActive(false)
        end
    elseif not is_show_global and (total_count - cur_count <= 0) then
        if total_count == -1 then  -- 首层boss首杀奖励无上限
            self.node_list["be_get_flag"]:SetActive(true)
            self.node_list["boss_xs_root"]:SetActive(false)
        else
            self.node_list["xs_box_txt"].text.text = string.format(Language.Boss.FirstKillProgress7)
            self.node_list["be_get_flag"]:SetActive(false)
            self.node_list["boss_xs_root"]:SetActive(true)
        end
    end

    if self.node_list["box_red"] then
        if BossWGData.Instance:IsShowRedPoint(self.data.boss_id) then
            self.node_list["box_red"]:SetActive(true)
        else
            self.node_list["box_red"]:SetActive(false)
        end

    end
end

function BossFirstKillItemRender:KillBoxinImgShakeAnim()
    if self.box_img_tween_shake then
		self.box_img_tween_shake:Kill(true)
		self.box_img_tween_shake = nil
    end
end



BossFirstLayerBtnRender = BossFirstLayerBtnRender or BaseClass(BaseRender)
function BossFirstLayerBtnRender:__init()
	-- self.is_slect_ceng = false
end

function BossFirstLayerBtnRender:__delete()
	-- self.is_slect_ceng = nil
end

function BossFirstLayerBtnRender:OnFlush()
	 -- if self.data == nil or self.is_slect_ceng then return end
    if self.data == nil then return end
    self.view:SetActive(true)

    local name = ""
    if type(self.data) == "table" then
    	name = self.data.name or ""
    else
    	name = self.data
    end
    --print_error(self.data)
    self.node_list["text"].text.text = name
    self.node_list["text_hl"].text.text = name

    local show_index = BossWGData.Instance:GetCurFirstKillShowView()
    if show_index == BossViewIndex.WorldBoss then
        local tab_red = BossWGData.Instance:GetWorldBossLayerList()
        local red_flag = false
        for k, v in pairs(tab_red) do
            if self.index == v then
                red_flag = true
            end
        end
        self.node_list["red"]:SetActive(red_flag)
    elseif show_index == BossViewIndex.VipBoss then
        local tab_red = BossWGData.Instance:GetVipBossLayerIndex()
        --print_error("Vipred", tab_red)
        self.node_list["red"]:SetActive(tab_red == self.data.level)
    end
end

function BossFirstLayerBtnRender:OnSelectChange(is_select)
	if type(self.data) == "table" and self.data.limit then
		self.node_list["img_lock"]:SetActive(true)
		self.node_list["img_normal"]:SetActive(false)
		self.node_list["img_select"]:SetActive(false)
	else
		self.node_list["img_lock"]:SetActive(false)
		self.node_list["img_normal"]:SetActive(not is_select)
		self.node_list["img_select"]:SetActive(is_select)
	end
end