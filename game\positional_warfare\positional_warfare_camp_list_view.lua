-- 阵营列表界面

PositionalWarfareCampListView = PositionalWarfareCampListView or BaseClass(SafeBaseView)

function PositionalWarfareCampListView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 594)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_camp_list_view")
end

function PositionalWarfareCampListView:OpenCallBack()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.RANK_INFO, CROSS_LAND_WAR_RANK_TYPE.CAMP_DEVOTE)
end

function PositionalWarfareCampListView:LoadCallBack()
    if not self.camp_list then
        self.camp_list = AsyncListView.New(PWCampListCellRender, self.node_list.camp_list) 
    end

    self.node_list.title_view_name.text.text = Language.PositionalWarfare.CampListViewName
    self.node_list.desc_tip.text.text = Language.PositionalWarfare.CampListViewTip
end

function PositionalWarfareCampListView:ReleaseCallBack()
    if self.camp_list then
        self.camp_list:DeleteMe()
        self.camp_list = nil
    end
end

function PositionalWarfareCampListView:OnFlush()
    local data_list = PositionalWarfareWGData.Instance:GetRankDataListByRankType(CROSS_LAND_WAR_RANK_TYPE.CAMP_DEVOTE)
    local has_data = not IsEmptyTable(data_list)
    self.node_list.camp_list:CustomSetActive(has_data)
    self.node_list.no_camp_list_data:CustomSetActive(not has_data)

    if has_data then
        self.camp_list:SetDataList(data_list)
    end

    self:FlushMyData(data_list)
end

function PositionalWarfareCampListView:FlushMyData(data_list)
    local my_data = {}

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if v.uuid == RoleWGData.Instance:GetUUid() then
                my_data = v 
                break
            end
        end
    end

    self.node_list.role_name.text.text = RoleWGData.Instance:GetAttr("name")
    self.node_list.server_name.text.text = string.format(Language.PositionalWarfare.ServerDefName, RoleWGData.Instance:GetOriginServerId())
    self.node_list.cap.text.text = RoleWGData.Instance:GetMainRoleCap()
    self.node_list.score.text.text = my_data and my_data.rank_value or 0
end

----------------------------------PWCampListCellRender---------------------------------
PWCampListCellRender = PWCampListCellRender or BaseClass(BaseRender)
function PWCampListCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.role_name.text.text = self.data.name
    self.node_list.server_name.text.text = string.format(Language.PositionalWarfare.ServerDefName, self.data.usid.temp_low)
    self.node_list.score.text.text = self.data.rank_value
    self.node_list.cap.text.text = self.data.cap
end