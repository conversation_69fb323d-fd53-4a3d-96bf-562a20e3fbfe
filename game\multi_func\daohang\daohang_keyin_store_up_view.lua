DaoHangKeYinUpStoreView = DaoHangKeYinUpStoreView or BaseClass(SafeBaseView)

function DaoHangKeYinUpStoreView:__init()
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(736, 672)})
	local daohang_bundle_name = "uis/view/multi_function_ui/daohang_prefab"
	self:AddViewResource(0, daohang_bundle_name, "layout_daohang_baoshi_upgrade")
end

function DaoHangKeYinUpStoreView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Charm.DaoHangKeYinUpStoreTitle

	if not self.left_cell then
		self.left_cell = ItemCell.New(self.node_list.ph_cell1)
	end

	if not self.right_cell then
		self.right_cell = ItemCell.New(self.node_list.ph_cell2)
	end

	if not self.stone_list_view then
		self.stone_list_view = AsyncListView.New(ItemCell, self.node_list["have_stone_list"])
	end

	XUI.AddClickEventListener(self.node_list.btn_upgrade, BindTool.Bind(self.OnUpgradeClick,self))
end

function DaoHangKeYinUpStoreView:ReleaseCallBack()
	self.data_info = nil

	if self.left_cell then
		self.left_cell:DeleteMe()
		self.left_cell = nil
	end

	if self.right_cell then
		self.right_cell:DeleteMe()
		self.right_cell = nil
	end

	if self.stone_list_view then
		self.stone_list_view:DeleteMe()
		self.stone_list_view = nil
	end
end

function DaoHangKeYinUpStoreView:SetData(data_info)
	self.data_info = data_info
end

function DaoHangKeYinUpStoreView:OnFlush()
	if IsEmptyTable(self.data_info) then
		return
	end

	local item_id = self.data_info.item_id
	local store_cfg = MultiFunctionWGData.Instance:GetDaoHangKeLingStoreCfg(item_id)
	
	if not IsEmptyTable(store_cfg) then
		local next_item_id = store_cfg.next_item_id
		self.left_cell:SetData({item_id = item_id})
		local left_item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		local left_item_name = left_item_cfg and left_item_cfg.name or ""
		local left_item_name_color = left_item_cfg and left_item_cfg.color or GameEnum.ITEM_COLOR_WHITE
		self.node_list.lbl_name1.text.text = ToColorStr(left_item_name, ITEM_COLOR[left_item_name_color])

		self.right_cell:SetData({item_id = next_item_id})
		local next_item_cfg = ItemWGData.Instance:GetItemConfig(next_item_id)
		local next_item_name = next_item_cfg and next_item_cfg.name or ""
		local next_item_name_color = next_item_cfg and next_item_cfg.color or GameEnum.ITEM_COLOR_WHITE
		self.node_list.lbl_name2.text.text = ToColorStr(next_item_name, ITEM_COLOR[next_item_name_color])

		local next_store_cfg = MultiFunctionWGData.Instance:GetDaoHangKeLingStoreCfg(next_item_id)
		local left_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(store_cfg, "attr_id", "attr_value")
		local right_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(next_store_cfg, "attr_id", "attr_value")

		for i = 1, 4 do
			local left_attr = left_attr_data[i]
			local right_attr = right_attr_data[i]
			local has_left_attr = not IsEmptyTable(left_attr)
			local has_right_attr = not IsEmptyTable(right_attr)

			if has_left_attr then
				self.node_list["lbl_attr1" .. "_" .. i].text.text = left_attr.attr_name .. "	" .. ToColorStr(left_attr.value_str, COLOR3B.GREEN)
			end

			if has_right_attr then
				self.node_list["lbl_attr2" .. "_" .. i].text.text = right_attr.attr_name .. "	" .. ToColorStr(right_attr.value_str, COLOR3B.GREEN)
			end

			self.node_list["lbl_attr1" .. "_" .. i]:CustomSetActive(has_left_attr)
			self.node_list["lbl_attr2" .. "_" .. i]:CustomSetActive(has_right_attr)
		end
	end

	local store_list = self.data_info.up_store_list
	if not IsEmptyTable(store_list) then
		self.stone_list_view:SetDataList(store_list)
	end
end

function DaoHangKeYinUpStoreView:OnUpgradeClick()
	if IsEmptyTable(self.data_info) then
		return
	end

	MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.INLAY_UPLEVEL, self.data_info.slot)
	self:Close()
end