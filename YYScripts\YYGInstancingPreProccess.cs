﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


namespace YYGame.Code
{
    public class YYGInstancingPreProccess
    {
        GameObject rootObj;
        public YYGInstancingPreProccess(GameObject root)
        {
            rootObj = root;
        }
        private class LightMapItem
        {
            public int lightMapIndex;
            public List<GameObject> renderObjList;
        }

        private class MaterialItem
        {
            public Material material;
            public int refCount;
            public Dictionary<int, LightMapItem> lightMapDic;
        }
        private class MeshItem
        {
            public Mesh mesh;
            public long meshSize;
            public long vertexCount;

            public Dictionary<Material, MaterialItem> materialDic;
        }

        Dictionary<Mesh, MeshItem> meshDic = new Dictionary<Mesh, MeshItem>();
        Dictionary<Material, Dictionary<int, Material>> materialLightMapDic = new Dictionary<Material, Dictionary<int, Material>>();

        int getMatCount = 0;
        private Material GetMaterialLightMap(Material srcMaterial, int lightMapIndex)
        {
            getMatCount++;
            Dictionary<int, Material> lightMapDic;
            Material material;
            if (!materialLightMapDic.TryGetValue(srcMaterial, out lightMapDic))
            {
                //ccc  2
                lightMapDic = new Dictionary<int, Material>();

                // cc 3
                material = new Material(srcMaterial);
                // aa 3
                lightMapDic.Add(lightMapIndex, material);

                //aa2
                materialLightMapDic.Add(srcMaterial, lightMapDic);
                return material;
            }
            else
            {
                if (!lightMapDic.TryGetValue(lightMapIndex, out material))
                {
                    // cc 3
                    material = new Material(srcMaterial);
                    // aa 3
                    lightMapDic.Add(lightMapIndex, material);

                }
                return material;

            }
        }

        public void ShowMatCount()
        {
            int realMat = 0;
            foreach (var matDic in materialLightMapDic)
            {
                foreach (var lightMapDic in matDic.Value)
                {
                    realMat++;
                }
            }

            Debug.Log("realMat is " + realMat + " getMatCount is" + getMatCount);
        }
        public void Check(bool isShow = false)
        {
            getMatCount = 0;
            materialLightMapDic.Clear();
            meshDic.Clear();
            MeshRenderer[] meshRenderers = rootObj.GetComponentsInChildren<MeshRenderer>();
            for (int i = 0; i < meshRenderers.Length; i++)
            {
                MeshRenderer meshRenderer = meshRenderers[i];

                MeshFilter meshFilter = meshRenderer.GetComponent<MeshFilter>(); //meshFilters[i];
                Mesh mesh = meshFilter.sharedMesh;
                Material material = meshFilter.gameObject.GetComponent<MeshRenderer>().sharedMaterial;
                if (null == mesh)
                {
                    Debug.Log("mesh == null" + meshFilter.gameObject.name);
                    continue;
                }
                if (null == material)
                {
                    Debug.Log("material == null" + mesh.name);
                    continue;
                }
                MeshItem meshItem;
                MaterialItem materialItem;
                LightMapItem lightMapItem;
                if (!meshDic.TryGetValue(mesh, out meshItem))
                {
                    //1 生成 meshItem
                    meshItem = new MeshItem();
                    meshItem.mesh = mesh;
                    //mesh.vertexCount
                    meshItem.vertexCount = mesh.vertexCount;
                    meshItem.meshSize = UnityEngine.Profiling.Profiler.GetRuntimeMemorySizeLong(mesh);
                    meshItem.materialDic = new Dictionary<Material, MaterialItem>();
                    //2 生成 materialItem
                    materialItem = new MaterialItem();
                    materialItem.material = material;
                    materialItem.refCount = 1;
                    materialItem.lightMapDic = new Dictionary<int, LightMapItem>();
                    //3 生成 lightMapItem
                    lightMapItem = new LightMapItem();
                    lightMapItem.lightMapIndex = meshRenderer.lightmapIndex;
                    lightMapItem.renderObjList = new List<GameObject>() { meshFilter.gameObject };
                    // 3加入 lightMapItem
                    materialItem.lightMapDic.Add(meshRenderer.lightmapIndex, lightMapItem);
                    // 2加入 materialItem
                    meshItem.materialDic.Add(material, materialItem);
                    // 1加入 meshItem
                    meshDic.Add(mesh, meshItem);
                }
                else
                {
                    if (!meshItem.materialDic.TryGetValue(material, out materialItem))
                    {
                        //2 生成 materialItem
                        materialItem = new MaterialItem();
                        materialItem.material = material;
                        materialItem.refCount = 1;
                        materialItem.lightMapDic = new Dictionary<int, LightMapItem>();
                        //3 生成 lightMapItem
                        lightMapItem = new LightMapItem();
                        lightMapItem.lightMapIndex = meshRenderer.lightmapIndex;
                        lightMapItem.renderObjList = new List<GameObject>() { meshFilter.gameObject };
                        // 3加入 lightMapItem
                        materialItem.lightMapDic.Add(meshRenderer.lightmapIndex, lightMapItem);
                        // 2加入 materialItem
                        meshItem.materialDic.Add(material, materialItem);
                    }
                    else
                    {
                        ++materialItem.refCount;

                        if (!materialItem.lightMapDic.TryGetValue(meshRenderer.lightmapIndex, out lightMapItem))
                        {
                            //3 生成 lightMapItem
                            lightMapItem = new LightMapItem();
                            lightMapItem.lightMapIndex = meshRenderer.lightmapIndex;
                            lightMapItem.renderObjList = new List<GameObject>() { meshFilter.gameObject };
                            // 3加入 lightMapItem
                            materialItem.lightMapDic.Add(meshRenderer.lightmapIndex, lightMapItem);
                        }
                        else
                        {
                            //materialItem.lightMapDic.Add(meshRenderer.lightmapIndex, lightMapItem);
                            lightMapItem.renderObjList.Add(meshFilter.gameObject);
                        }
                    }
                }

            }

            if (isShow)
            {
                Debug.Log(MaxCount + "Count 大的是");
                Show(1);
                Debug.Log(MaxSize + "Size 大的是");
                Show(2);
                Debug.Log("Count Size 都大的是");
                Show(3);
                Debug.Log(MaxVertexCount + "MaxVertexCount 大的是");
                Show(4);
            }
        }
        private void Show(int type)
        {
            int m_count = 0;

            foreach (var meshItem in meshDic)
            {
                //Debug.Log("name is " + item.Key.name + "  No. is" + item.Value.refCount);

                foreach (var materialItem in meshItem.Value.materialDic)
                {
                    long sum = meshItem.Value.meshSize * materialItem.Value.refCount;
                    float fSum = (float)sum / 1024f;
                    fSum = fSum / 1024f;
                    //
                    long sumVertexCount = meshItem.Value.vertexCount * materialItem.Value.refCount;

                    //
                    switch (type)
                    {
                        case 1:
                            if (materialItem.Value.refCount > MaxCount)
                            {
                                m_count++;

                                Debug.Log("mesh: " + meshItem.Key.name + "  material: " + materialItem.Key.name +
                                    " no is " + materialItem.Value.refCount + " sum is " + fSum.ToString("f2") + "mb"
                                    + " sumVertexCount is "+sumVertexCount);


                                if (IsGInstancingShader(materialItem.Value.material.shader) == false)
                                {
                                    Debug.LogError("bigCount notGInstancing shader name is" + materialItem.Value.material.shader.name);
                                    //continue;
                                }
                            }
                            break;
                        case 2:
                            if (fSum >= MaxSize)
                            {
                                m_count++;

                                Debug.Log("mesh: " + meshItem.Key.name + "  material: " + materialItem.Key.name +
                                   " no is " + materialItem.Value.refCount + " sum is " + fSum.ToString("f2") + "mb"
                                   + " sumVertexCount is " + sumVertexCount);
                            }
                            break;
                        case 3:
                            if (materialItem.Value.refCount > MaxCount && fSum >= MaxSize)
                            {

                                m_count++;
                                Debug.Log("mesh: " + meshItem.Key.name + "  material: " + materialItem.Key.name +
                                  " no is " + materialItem.Value.refCount + " sum is " + fSum.ToString("f2") + "mb"
                                  + " sumVertexCount is " + sumVertexCount);
                            }
                            break;
                        case 4:
                            if (sumVertexCount >= MaxVertexCount)
                            {

                                m_count++;
                                Debug.Log("mesh: " + meshItem.Key.name + "  material: " + materialItem.Key.name +
                                  " no is " + materialItem.Value.refCount + " sum is " + fSum.ToString("f2") + "mb"
                                  + " sumVertexCount is " + sumVertexCount);
                            }
                            break;
                    }

                }
            }

            Debug.Log("no. is " + m_count);
        }
        private bool IsGInstancingShader(Shader shader)
        {
            if (shader.name == "YY/YYStandardGrass" ||
                shader.name.StartsWith("YifStandard"))
                return true;
            return false;
        }
        private int MaxCount = 20;
        private float MaxSize = 5.0f;
        private long MaxVertexCount = 20000;
        //  数量太多的话 进行gpuinstancing 
        // Vertex 顶点 太多的话  不进行合批

        public List<MeshRenderer> listMesh = new List<MeshRenderer>();
        public List<GameObject> listObjParent = new List<GameObject>();
        public void MoveTo(GameObject gIGo)
        {
            listMesh.Clear();
            listObjParent.Clear();

            foreach (var meshItem in meshDic)
            {
                foreach (var materialItem in meshItem.Value.materialDic)
                {
                    if (materialItem.Value.refCount < MaxCount)
                    {
                        continue;
                    }
                    if (IsGInstancingShader(materialItem.Value.material.shader) == false)
                    {
                        Debug.LogError(meshItem.Value.mesh.name + "bigRefCount" + materialItem.Value.refCount +
                            " notGInstancing shader name is" + materialItem.Value.material.shader.name);
                        continue;
                    }
                    foreach (var lightMapItem in materialItem.Value.lightMapDic)
                    {
                        GameObject go = new GameObject(meshItem.Key.name + "_" + materialItem.Key.name + "_" + lightMapItem.Key);
                        go.transform.parent = gIGo.transform;
                        GInstancing gInstancing = go.AddComponent<GInstancing>();
                        gInstancing.material = GetMaterialLightMap(materialItem.Key, lightMapItem.Key);//new Material(materialItem.Key);

                        if (gInstancing.material.enableInstancing == false)
                        {
                            gInstancing.material.enableInstancing = true;
                        }
                        if (lightMapItem.Key != -1)
                        {
                            if (LightmapSettings.lightmaps.Length > lightMapItem.Key)
                            {
                                gInstancing.lightmapTexture = LightmapSettings.lightmaps[lightMapItem.Key].lightmapColor;
                            }
                            else
                            {
                                Debug.LogError("no lightMap");
                            }
                        }
                        else
                        {
                            Debug.LogError("lightMapIndex == -1" + meshItem.Key.name);
                        }
                        foreach (var goItem in lightMapItem.Value.renderObjList)
                        {
                            
                            goItem.transform.parent = go.transform;
                            listMesh.Add(goItem.GetComponent<MeshRenderer>());
                            listObjParent.Add(go);
                            goItem.SetActive(false);
                        }
                        go.SetActive(false);
                    }
                }
            }

        }
        public void MoveToNotBatch(GameObject gIGo)
        {
            foreach (var meshItem in meshDic)
            {

                foreach (var materialItem in meshItem.Value.materialDic)
                {
                    if (materialItem.Value.refCount >= 20)
                    {
                        //Debug.LogError(">20  进行ginstance" + meshItem.Value.mesh.name);
                        continue;
                    }

                    long sumVertexCount = meshItem.Value.vertexCount * materialItem.Value.refCount;
                    if (sumVertexCount < MaxVertexCount)
                    {
                        continue;
                    }
                    GameObject go = new GameObject(meshItem.Key.name + "_" + materialItem.Key.name);

                    foreach (var lightMapItem in materialItem.Value.lightMapDic)
                    {
                        go.transform.parent = gIGo.transform;

                        foreach (var goItem in lightMapItem.Value.renderObjList)
                        {
                            goItem.transform.parent = go.transform;
                        }
                    }

                }


            }

        }


        public static GameObject GetGo(string goName)
        {
            string allName = "Main/" + goName;
            GameObject gInstancingObj = GameObject.Find(allName);

            if (null == gInstancingObj)
            {
                GameObject mainGo = GameObject.Find("Main");

                gInstancingObj = new GameObject(goName);
                gInstancingObj.transform.parent = mainGo.transform;


            }

            return gInstancingObj;
        }


    }
}
