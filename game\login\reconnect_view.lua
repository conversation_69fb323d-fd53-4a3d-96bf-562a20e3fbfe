ReconnectView = ReconnectView or BaseClass(SafeBaseView)

function ReconnectView:__init()
	self.view_layer = UiLayer.Disconnect
	self:SetMaskBg(false)

	self.is_modal = true
	self.play_audio = true
	self.active_close = false
	self.open_tween = nil
	self.close_tween = nil

	self:AddViewResource(0, "uis/view/login_ui_prefab", "layout_reconnect")
	self.is_req_new_version_ing = false
	self.is_restarting = false
end

function ReconnectView:__delete()
end

function ReconnectView:ReleaseCallBack()
end

function ReconnectView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.return_login, BindTool.Bind(self.OnReturnLogin,self))
	XUI.AddClickEventListener(self.node_list.reconnect, BindTool.Bind(self.OnReconnect,self))
	self:UpdateView()
end

function ReconnectView:OnReturnLogin()
	UtilU3d.CacheData("select_role_state", 0)
	UtilU3d.CacheData("no_enter_scene_server", nil)
	self.is_restarting = true
	self:Close()

	GlobalEventSystem:Fire(LoginEventType.LOGOUT)
end

-- 切换角色
function ReconnectView:OnReconnect()
	-- 重启时，如果登陆过sdk,则在登陆那块直接跳过sdk的登陆
	if nil ~= GameVoManager and nil ~= GameVoManager.Instance then
		local uservo = GameVoManager.Instance:GetUserVo()
		if nil ~= uservo and uservo.account_user_id ~= "" and uservo.account_user_id ~= nil then
			UtilU3d.CacheData("select_role_state", 1)
			UtilU3d.CacheData("utilu3d_cahce_account_user_id", uservo.account_user_id)
			UtilU3d.CacheData("select_role_state_plat_server_id", uservo.plat_server_id)
		end

		if not uservo.is_in_game_server and ViewManager ~= nil and ViewManager.Instance ~= nil and ViewManager.Instance:IsOpen(GuideModuleName.Login) then
			if LoginWGData ~= nil and LoginWGData.Instance ~= nil then
				local last_server = LoginWGData.Instance:ViewSelectServerID()
				if last_server ~= nil then
					UtilU3d.CacheData("no_enter_scene_server", last_server)
				end
			end
		else
			UtilU3d.CacheData("no_enter_scene_server", nil)
		end

		GameRestart()
	end

	self:Close()
end

function ReconnectView:SetAutoConnect(custom_disconnect_notice_type)
	self.custom_disconnect_notice_type = custom_disconnect_notice_type
end

function ReconnectView:OpenCallBack()
	SocietyWGCtrl.Instance:ClearTeamInfo()
	FuBenWGData.Instance:DefFBState(false)

	TaskGuide.Instance:CanAutoAllTask(false)
	GuajiWGCtrl.Instance:StopGuaji()
	self:UpdateView()
end

function ReconnectView:CloseCallBack()
end

function ReconnectView:UpdateView()
	if not self:IsLoadedIndex(0) then return end
	self.node_list["text_head"].text.text = Language.Notcie.Tips[self.custom_disconnect_notice_type] or Language.Login.ReconnectTips2

	local show_reconnect_btn = true
	if self.custom_disconnect_notice_type == nil
	or self.custom_disconnect_notice_type == DISCONNECT_NOTICE_TYPE.LOGIN_FORBID
	or self.custom_disconnect_notice_type == DISCONNECT_NOTICE_TYPE.HEARTBEAT_ERROR
	or self.custom_disconnect_notice_type == DISCONNECT_NOTICE_TYPE.CLIENT_CHANGE_VERSION
	or self.custom_disconnect_notice_type == DISCONNECT_NOTICE_TYPE.SERVER_MAINTENANCE
	or self.custom_disconnect_notice_type == DISCONNECT_NOTICE_TYPE.LUA_INITDB_FAIL
	or self.custom_disconnect_notice_type == DISCONNECT_NOTICE_TYPE.CONNECT_LOGIN_SERVER_ERROR then
		show_reconnect_btn = false
	end

	self.node_list["reconnect"]:SetActive(show_reconnect_btn)
end

