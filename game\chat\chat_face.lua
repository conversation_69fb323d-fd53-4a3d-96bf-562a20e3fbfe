ChatFaceView = ChatFaceView or BaseClass(SafeBaseView)

local ClickType = {
	Normal = 0,
	Dynamic = 1,
}

function ChatFaceView:__init()
	--self.ctrl = ChatWGCtrl.Instance
	--self:SetMaskBg(true,true)
	self.view_layer = UiLayer.PopWhite
	--self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	-- self.is_modal = true

	-- self:LoadConfig()
	self.show_state = ClickType.Normal
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "SpeakerFace")
	self.open_view = false
end

function ChatFaceView:__delete()

end

function ChatFaceView:ReleaseCallBack()
	if self.grid_normal_face then
		self.grid_normal_face:DeleteMe()
		self.grid_normal_face = nil
	end

	if self.grid_word_face then
		self.grid_word_face:DeleteMe()
		self.grid_word_face = nil
	end
end

function ChatFaceView:LoadCallBack()
	--self.node_list["grid_normal_face"].button:AddClickListener(BindTool.Bind(self.ClickNormal, self))
	self.node_list["grid_normal_face"].toggle:AddClickListener(BindTool.Bind(self.ClickNormal, self))
	self.node_list["grid_word_face"].toggle:AddClickListener(BindTool.Bind(self.ClickDynamic, self))
	self.node_list["Block"].button:AddClickListener(BindTool.Bind(self.CloseBtn, self))
	self:ClickNormal()
end


function ChatFaceView:OpenCallBack()
   self.open_view = true
end

function ChatFaceView:OnFlush()
	if self.open_view then
    	self:FlushHideOrActive(true)
    	self.open_view = false                                                                             
    end
end

function ChatFaceView:CloseBtn()
	self:FlushHideOrActive(false)
	GlobalTimerQuest:AddDelayTimer(function()
		SafeBaseView.Close(self)
	end,0.2)
end

function ChatFaceView:FlushHideOrActive(is_active)
	local x_value = 0
	if is_active then
		x_value = 0
		self.node_list["Block"]:SetActive(true)
	else
		x_value = 500
		self.node_list["Block"]:SetActive(false)
	end
	local Hide_animator = self.node_list["more_container"].rect:DOAnchorPosX(x_value, 0.2)
	Hide_animator:SetEase(DG.Tweening.Ease.Linear)
end

function ChatFaceView:ClickNormal()
	self.node_list["grid_normal_face"].toggle.isOn = true
	self.node_list["normal_face_list"]:SetActive(true)
	self.node_list["big_face_list"]:SetActive(false)

	local bundle = "uis/view/chat_ui_prefab"
	local asset = "NorItemRender"
	if self.grid_normal_face == nil then
		self.grid_normal_face  = AsyncBaseGrid.New()

		self.grid_normal_face:CreateCells({col = 3, cell_count = 40,
		assetName = asset,
		assetBundle = bundle,
		itemRender = EmojiIconCell,
		list_view = self.node_list["normal_face_list"]
		})

		--客户端固定写死数据
		self.table_data_normal = ChatWGData.Instance:GetNormalFaceCfg()
		self.grid_normal_face:SetSelectCallBack(BindTool.Bind(self.ClickCallBack, self))
		self.grid_normal_face:SetStartZeroIndex(false)
	end
	self.grid_normal_face:SetDataList(self.table_data_normal)
end

--点击格子回调
function ChatFaceView:ClickCallBack(cell)
	local data = cell:GetData()
	if nil == data then
		return
	end

	--数据转换
	if nil ~= ChatWGCtrl.Instance:IsOpenHornPopView() then
		if ChatWGData.ExamineEditText(ChatWGCtrl.Instance:GetHornInputEdit(), 2) then
			ChatWGCtrl.Instance:AddHornInputEdit( "/" .. data.id)
			-- ChatWGData.Instance:InsertItemTab(data)
		end
	else
		if ChatWGData.ExamineEditText(ChatWGCtrl.Instance:GetTransmitInputEdit(), 2) then
			ChatWGCtrl.Instance:AddTransmitInputEdit( "/" .. data.id)
			-- ChatWGData.Instance:InsertItemTab(data)
		end
	end

end


function ChatFaceView:ClickDynamic()
	self.node_list["grid_word_face"].toggle.isOn = true
	self.node_list["normal_face_list"]:SetActive(false)
	self.node_list["big_face_list"]:SetActive(true)
	if self.grid_word_face == nil then
		self.big_table_data = ChatWGData.Instance:GetGifFaceCfg()
		local bundle2 = "uis/view/chat_ui_prefab"
		local asset2 = "Big_ItemRender"
		self.grid_word_face = AsyncBaseGrid.New()
		self.grid_word_face:CreateCells({col = 3, cell_count = 24,
			assetName = asset2,
			assetBundle = bundle2,
		 	itemRender = BigFaceIconCell,
			list_view = self.node_list["big_face_list"] })
		self.grid_word_face:SetStartZeroIndex(false)
		self.grid_word_face:SetSelectCallBack(BindTool.Bind(self.ClickCallBack, self))
	end
	self.grid_word_face:SetDataList(self.big_table_data)
end


function ChatFaceView:ChangeView()
	local PrefabName = ""
	if self.show_state == ClickType.Normal then
		PrefabName = "NormalFaceView"
	elseif self.show_state == ClickType.Dynamic then
		PrefabName = "BigFaceView"
	end


	-- PrefabPool.Instance:Load(AssetID("uis/view/chat_ui_prefab", PrefabName), function(prefab)
	-- if prefab then
	-- 	if self.show_state == ClickType.Normal then
	-- 		local list_delegate = self.list_view.list_simple_delegate
	-- 		list_delegate.CellPrefab = prefab:GetComponent(typeof(ListViewCell))
	-- 		self.list_view.scroller:ReloadData(0)
	-- 	elseif self.show_state == ClickType.Dynamic then
	-- 		if self.list_view_bigface ~= nil then
	-- 			local list_delegate = self.list_view_bigface.list_simple_delegate
	-- 			list_delegate.CellPrefab = prefab:GetComponent(typeof(ListViewCell))
	-- 			self.list_view_bigface.scroller:ReloadData(0)
	-- 		end
	-- 	end

	-- 	PrefabPool.Instance:Free(prefab)
	-- end)

end




--普通表情
EmojiIconCell = EmojiIconCell or BaseClass(BaseGridRender)

function EmojiIconCell:__init()

end

function EmojiIconCell:OnFlush()
	if self.data == nil then return end
	local index = tonumber(self.data.id)
	if not self.old_index or self.old_index ~= index then
		self.old_index = index
		local dec_num = "<size=52><sprite name=\"".. index .. "\"></size>"
		self.node_list.content.tmp.text = dec_num
	end
end


--大表情
BigFaceIconCell = BigFaceIconCell or BaseClass(BaseGridRender)

function BigFaceIconCell:__init()
end

function BigFaceIconCell:__delete()
end

function BigFaceIconCell:OnFlush()
	self:ReloadIcon()
end

function BigFaceIconCell:ReloadIcon()
	if self.data == nil then
		return
	end

	if nil == self.node_list["Frame"] or nil == self.node_list["Frame"].transform then
		return
	end

	local index = tonumber(self.data.id)
	if not index then
		return
	end

	if index >= 10 and index <= 99 then
		index = "0" .. index
	elseif index < 10 and index >= 0 then
		index = "00" .. index
	end 
	
	local dec_num = "<size=52><sprite name=\"".. index .. "\"></size>"
	self.node_list.big_content.tmp.text = dec_num
end


ChatWordFaceRender = ChatWordFaceRender or BaseClass(BaseGridRender)
function ChatWordFaceRender:__init()

end

function ChatWordFaceRender:__delete()

end

function ChatWordFaceRender:LoadCallBack()

end

function ChatWordFaceRender:OnFlush()
	if nil == self.data then return end
	self.node_list["ItemCell"].text.text = self.data.content
end
