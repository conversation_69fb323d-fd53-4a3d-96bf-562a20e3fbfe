TianShenBaGuaSXTips = TianShenBaGuaSXTips or BaseClass(SafeBaseView)

function TianShenBaGuaSXTips:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_bagua_sx_tip")
end

function TianShenBaGuaSXTips:__delete()
end

function TianShenBaGuaSXTips:ReleaseCallBack()
    if self.bagua_attr_list then
        self.bagua_attr_list:DeleteMe()
        self.bagua_attr_list = nil
    end

    self.cur_select_bagua = nil
end

function TianShenBaGuaSXTips:LoadCallBack()
    if nil == self.bagua_attr_list then
        self.bagua_attr_list = AsyncListView.New(BaGuaAttrListCell, self.node_list.attr_list)
    end
end

function TianShenBaGuaSXTips:OnFlush()
    local _, all_attr_list, all_is_pre = TianShenBaGuaWGData.Instance:GetBaGuaSlotZhanLi(self.cur_select_bagua)
    local attr_list_data = {}
    local num = 0
    local a, b
    if IsEmptyTable(all_attr_list) then 
        self.bagua_attr_list:SetDataList(all_attr_list)
        return
    end

    for k,v in pairs(all_attr_list) do
        num = num + 1
        attr_list_data[num] = {}
        attr_list_data[num].name = k
        attr_list_data[num].value = v
        attr_list_data[num].is_pre = all_is_pre[k].is_pre or false
        attr_list_data[num].attr_type = tonumber(all_is_pre[k].attr_type) or 1000
        attr_list_data[num].sort_index = TianShenBaGuaWGData.Instance:GetBaGuaAttrSortIndex(attr_list_data[num].attr_type)
    end

    table.sort(attr_list_data, function (a,b)
        if a.sort_index < b.sort_index then
            return true
        end
        return false
    end)

    all_attr_list = {}
    num = 0
    for k,v in pairs(attr_list_data) do
        num = num + 1
        a = math.ceil(num/2)
        b = num % 2
        b = b == 0 and 2 or b
        if not all_attr_list[a] then
            all_attr_list[a] = {}
        end
        
        all_attr_list[a][b] = {}
        all_attr_list[a][b] = v
    end
    self.bagua_attr_list:SetDataList(all_attr_list)
end

function TianShenBaGuaSXTips:SetComposeData(cur_select_bagua)
    self.cur_select_bagua = cur_select_bagua
end