﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class NodePrefabListWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(NodePrefabList), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("InstantiateAllNodeInEditor", InstantiateAllNodeInEditor);
		<PERSON><PERSON>unction("DestroyAllNodeInEditor", DestroyAllNodeInEditor);
		L.RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("nodeList", get_nodeList, set_nodeList);
		L<PERSON>lass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InstantiateAllNodeInEditor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			NodePrefabList obj = (NodePrefabList)ToLua.CheckObject<NodePrefabList>(L, 1);
			obj.InstantiateAllNodeInEditor();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DestroyAllNodeInEditor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			NodePrefabList obj = (NodePrefabList)ToLua.CheckObject<NodePrefabList>(L, 1);
			obj.DestroyAllNodeInEditor();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_nodeList(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			NodePrefabList obj = (NodePrefabList)o;
			System.Collections.Generic.List<NodePrefabElement> ret = obj.nodeList;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index nodeList on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_nodeList(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			NodePrefabList obj = (NodePrefabList)o;
			System.Collections.Generic.List<NodePrefabElement> arg0 = (System.Collections.Generic.List<NodePrefabElement>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<NodePrefabElement>));
			obj.nodeList = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index nodeList on a nil value");
		}
	}
}

