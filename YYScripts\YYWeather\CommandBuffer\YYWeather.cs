﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class YYWeather : MonoBehaviour
{

#if UNITY_EDITOR

    /// <summary>Allow the scene camera to render Weather Maker. This can result in UI corruption due to Unity bugs, so turn off if you see UI corruption.</summary>
    [Tooltip("Allow the scene camera to render Weather Maker. This can result in UI corruption due to Unity bugs, so turn off if you see UI corruption.")]
    public bool AllowSceneCamera = true;

#endif

    private static YYWeather instance;
    /// <summary>
    /// Shared instance of weather maker manager script
    /// </summary>
    public static YYWeather Instance
    {
        get { return FindOrCreateInstance<YYWeather>(ref instance, true); }
    }

    public CameraMode CameraType;

    public static CameraMode ResolveCameraMode(CameraMode? mode = null, Camera camera = null)
    {
        camera = (camera == null ? Camera.main : camera);
        if (mode == null)
        {
            mode = (Instance == null ? CameraMode.Auto : Instance.CameraType);
        }
        if (camera == null)
        {
            if (mode.Value == CameraMode.Auto)
            {
                return CameraMode.Perspective;
            }
            return mode.Value;
        }
        else if (mode.Value == CameraMode.OrthographicXY || (mode.Value == CameraMode.Auto && camera.orthographic))
        {
            return CameraMode.OrthographicXY;
        }
        else if (mode.Value == CameraMode.Perspective || (mode.Value == CameraMode.Auto && !camera.orthographic))
        {
            return CameraMode.Perspective;
        }
        return CameraMode.OrthographicXZ;
    }

    private void Update()
    {
        SetGlobalShaderProperties();
    }
    private void SetGlobalShaderProperties()
    {
        Shader.SetGlobalFloat(WMS._WeatherMakerCloudGlobalShadow, 1.0f);
        Shader.SetGlobalFloat(WMS._WeatherMakerCloudGlobalShadow2, 1.0f);
    }


    public static void RemoveNull<T>()
    {
        if(nullInstances.Contains(typeof(T)))
        {
            nullInstances.Remove(typeof(T));
        }
    }

    private static readonly HashSet<System.Type> nullInstances = new HashSet<System.Type>();
    public static T FindOrCreateInstance<T>(ref T instance, bool required = false) where T : MonoBehaviour
    {
        if (instance == null && !nullInstances.Contains(typeof(T)) && !System.Text.RegularExpressions.Regex.IsMatch(StackTraceUtility.ExtractStackTrace(), "OnDestroy|OnDisable"))
        {
            T[] scripts = GameObject.FindObjectsOfType<T>();
            foreach (T script in scripts)
            {
                if (script.enabled || script is YYWeather)
                {
                    instance = script;
                    nullInstances.Remove(typeof(T));
                    break;
                }
            }
            if (instance == null)
            {
                if (required)
                {
                    if (Application.isPlaying)
                    {
                        Debug.LogFormat("Object of type {0} is required, please ensure it is active in the WeatherMakerPrefab", typeof(T));
                    }
                }
                else
                {
                    nullInstances.Add(typeof(T));
                }
            }
        }
        return instance;
    }


    public static bool ShouldIgnoreCamera(MonoBehaviour script, Camera camera, bool ignoreReflections = true)
    {
        if (camera == null || Instance == null || script == null || !script.enabled || !script.gameObject.activeInHierarchy ||
               camera.cameraType == UnityEngine.CameraType.Preview )//|| (Instance.AllowCameras.Count == 0 && Instance.AllowCamerasNames.Count == 0 && Instance.AllowCamerasNamesPartial.Count == 0) ||
               //(camera.CachedName().IndexOf("depth", System.StringComparison.OrdinalIgnoreCase) >= 0))
        {
            return true;
        }

#if UNITY_EDITOR

        else if (camera.cameraType == UnityEngine.CameraType.SceneView)
        {
            return (YYWeather.Instance == null || !YYWeather.Instance.AllowSceneCamera);
        }

#endif
        return false;
        //WeatherCameraType type = YYWeather.GetCameraType(camera);

        //// if camera is not in allow list and
        //// camera is not an allowed reflection camera and
        //// camera is not an allowed reflection probe camera
        //// then ignore it
        //bool notInAllowList = !Instance.AllowCameras.Contains(camera);
        //bool notInAllowNameList = true;
        //foreach (string s in Instance.AllowCamerasNames)
        //{
        //    if (camera.CachedName() == s)
        //    {
        //        notInAllowNameList = false;
        //        break;
        //    }
        //}
        //if (notInAllowNameList)
        //{
        //    foreach (string s in Instance.AllowCamerasNamesPartial)
        //    {
        //        if (camera.CachedName().IndexOf(s, System.StringComparison.OrdinalIgnoreCase) >= 0)
        //        {
        //            notInAllowNameList = false;
        //            break;
        //        }
        //    }
        //}
        //bool ignoreReflection = (ignoreReflections || type != WeatherMakerCameraType.Reflection);
        //bool ignoreProbe = (Instance.PerformanceProfile.IgnoreReflectionProbes || type != WeatherMakerCameraType.CubeMap);
        //bool ignore = notInAllowList && notInAllowNameList && ignoreReflection && ignoreProbe;
        //return ignore;


    }
}
