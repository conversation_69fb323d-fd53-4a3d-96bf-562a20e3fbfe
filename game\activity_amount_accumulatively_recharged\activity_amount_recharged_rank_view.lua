ActivityAmountRankView = ActivityAmountRankView or BaseClass(SafeBaseView)

function ActivityAmountRankView:__init()
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -20), sizeDelta = Vector2(970, 614)})
    self:AddViewResource(0, "uis/view/activity_amount_recharged_ui_prefab", "amount_rank_view")
end

function ActivityAmountRankView:OpenCallBack()
    --请求信息的协议
    ActivityAmountRechargedWGCtrl.Instance:SendAmountRechargedRank(CROSS_CHONGZHI_RANK_OPERATE_TYPE.RANK_INFO)
end

function ActivityAmountRankView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.AmountRecharged.RechargeRankTitle

    self.amount_rank_list = AsyncListView.New(AmountRankItemRender, self.node_list["ph_item_list"])
end

function ActivityAmountRankView:ReleaseCallBack()
    if self.amount_rank_list then
        self.amount_rank_list:DeleteMe()
        self.amount_rank_list = nil
    end
end

function ActivityAmountRankView:OnFlush()
    self:FlushViewShow()
end


--刷新显示信息
function ActivityAmountRankView:FlushViewShow()
    --获取排行榜信息
    local data_list = ActivityAmountRechargedWGData.Instance:GetRechargedRankSort()
    if data_list then
        self.amount_rank_list:SetDataList(data_list)
    end

    --获取自己的上榜信息
    local my_rank_data = ActivityAmountRechargedWGData.Instance:GetMyRankData()
    --获取到自己的累计金额
    local my_rank_value = ActivityAmountRechargedWGData.Instance:GetRankValue()
    --拿到配置表信息
    local recharged_rank_cfg = ActivityAmountRechargedWGData.Instance:GetRechargedRankCfg()

    --拿到配置表信息的最低上榜条件的金额数
    if IsEmptyTable(recharged_rank_cfg) then
        return 
    end

    local min_reach_value = recharged_rank_cfg[#recharged_rank_cfg].reach_value
  

    --最低上榜条件的显示
    self.node_list["my_rank"].text.text = string.format(Language.AmountRecharged.RechargeRankCondition, min_reach_value)

    --我的累计充值的显示
    self.node_list["total_value"].text.text = string.format(Language.AmountRecharged.RechargeRankMoney, my_rank_value)

    --判断自己是否上榜  小于等于0是未上榜
    --还需充值数
    local recharged_money = 0
    local next_ranking = 0
   
    if my_rank_data <= 0 then
        --拿到上榜最后一名的信息
        next_ranking = recharged_rank_cfg[#recharged_rank_cfg].max_rank
        local monery_num = ActivityAmountRechargedWGData.Instance:GetMoneyNum(recharged_rank_cfg[#recharged_rank_cfg].max_rank)
        if monery_num  > 0 then
            recharged_money = monery_num - my_rank_value + 1
        else
            recharged_money = recharged_rank_cfg[#recharged_rank_cfg].reach_value - my_rank_value
        end
       

    elseif my_rank_data == 1 then
        self.node_list["btn_prompt"]:CustomSetActive(not my_rank_data == 1)
        return
    else
        --上一名的信息
        next_ranking = my_rank_data - 1
       --获取上一名在那个配置区间
        local qujian_cfg = ActivityAmountRechargedWGData.Instance:GetRankItemCfg(next_ranking)

        local monery_num = ActivityAmountRechargedWGData.Instance:GetMoneyNum(next_ranking)
       if monery_num <= 0 then
            if qujian_cfg then
            recharged_money = qujian_cfg.reach_value - my_rank_value
            end
       else
            recharged_money = monery_num - my_rank_value + 1
       end
    end
    self.node_list["ascension_txt"].text.text = string.format(Language.AmountRecharged.RechargeRankPromote, recharged_money, next_ranking)

end








--------列表格子
AmountRankItemRender = AmountRankItemRender or BaseClass(BaseRender)

function AmountRankItemRender:OnFlush()
    if not self.data then
        return
    end
    
    local is_top_3 = self.data.rank <= 3
    local player_rank = self.data.rank
    self.node_list["need_cap_value"].text.text = Language.AmountRecharged.BaoMi
    self.node_list["need_cap_value2"].text.text = Language.AmountRecharged.BaoMi

    --使用进行判断is_rank, true 显示虚位以待
    local user_name = ""
    if self.data.is_rank then
      user_name = Language.AmountRecharged.XuWeiYiDai
    else
        local name_data = Split(self.data.name, "_")
        user_name = "["..name_data[2].."]"..name_data[1]
    end

    self.node_list["player_name"].text.text = user_name
	self.node_list["player_name2"].text.text = user_name

	self.node_list["player_name"].text.enabled = not is_top_3
	self.node_list["player_name2"].text.enabled = is_top_3

    self.node_list["need_cap_value"].text.enabled = not is_top_3
	self.node_list["need_cap_value2"].text.enabled = is_top_3

    self.node_list["img_rank"].image.enabled = is_top_3
 
    if not is_top_3 then
        self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_bt_2"))
        self.node_list.rank.text.text = player_rank
    else
        self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. player_rank))
        self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_" .. player_rank))
        self.node_list.rank.text.text = ""
    end
end
