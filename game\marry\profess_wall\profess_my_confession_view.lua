function ProfessView:LoadMyConfessionViewCallBack()
    if not self.pw_my_list then
        self.pw_my_list = AsyncBaseGrid.New()
        local bundle = "uis/view/marry_ui_prefab"
		local asset = "profess_note_my_item"
        self.pw_my_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list["pw_my_list"],
            assetBundle = bundle, assetName = asset, itemRender = ProfessWallInfoMyCellRender})
        self.pw_my_list:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list.btn_pw_my_qiuhun, BindTool.Bind(self.OpenSelectPWMyProfess, self))
end

function ProfessView:ShowMyConfessionViewCallBack()

end

function ProfessView:ReleaseMyConfessionViewCallBack()
    if self.pw_my_list then
        self.pw_my_list:DeleteMe()
        self.pw_my_list = nil
    end
end

function ProfessView:OnFlushMyConfessionViewCallBack()
    local _, temp_data = ActivePerfertQingrenWGData.Instance:GetPersonalInfo()
    local no_data = IsEmptyTable(temp_data)
    self.pw_my_list:SetDataList(temp_data)
    self.node_list.pw_my_no_data:CustomSetActive(no_data)
    self.node_list.pw_my_list:CustomSetActive(not no_data)
end

function ProfessView:OpenSelectPWMyProfess()
	ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
end

---------------------------------ProfessWallInfoMyCellRender--------------------------------
ProfessWallInfoMyCellRender = ProfessWallInfoMyCellRender or BaseClass(ProfessWallInfoAllCellRender)

function ProfessWallInfoMyCellRender:SetHeadCell()
    local data = {}
    data.role_id = RoleWGData.Instance:GetOriginUid()
    data.prof = RoleWGData.Instance:GetRoleProf()
    data.sex = RoleWGData.Instance:GetRoleSex()
    self.head_cell:SetData(data)

    local bundle = "uis/view/marry_ui/images_atlas"
    local asset = "a3_qy_tq_yd"
    self.head_cell:ChangeBg(bundle, asset, true)
end