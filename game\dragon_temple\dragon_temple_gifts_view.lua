DragonTempleGiftsView = DragonTempleGiftsView or BaseClass(SafeBaseView)
function DragonTempleGiftsView:__init()
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/dragon_temple_ui_prefab", "layout_dragon_temple_gift")
end

function DragonTempleGiftsView:__delete()

end

function DragonTempleGiftsView:ReleaseCallBack()
	
end

function DragonTempleGiftsView:LoadCallBack()
end

function DragonTempleGiftsView:LoadIndexCallBack(index)
	
end


function DragonTempleGiftsView:ShowIndexCallBack(index)
	
end

function DragonTempleGiftsView:OnFlush(param_t, index)
	
end