FUBEN_TYPE =
{
	FBCT_DAILY_FB = 1,				-- 武器材料，过图副本
	FBCT_STORYFB = 2,				-- 剧情副本
	FBCT_CHALLENGE = 3,				-- 品质材料，挑战副本
	FBCT_PHASE = 4,					-- 商城道具，阶段副本
	FBCT_TOWERDEFEND_TEAM = 5,		-- 防具材料，塔防副本
	FBCT_GUILD_MONSTER = 6,			-- 仙盟神兽副本
	FBCT_COUNT = 7,					--
	FBCT_TEAMFB = 8, 				-- 多人副本
	FBCT_QINGYUAN = 9,				-- 情缘副本
	FBCT_ZHANSHENDIAN = 10,			-- 战神殿副本
	FBCT_HUNYAN = 11,				-- 婚宴副本
	FBCT_MANYTOWER = 12,			-- 多人塔防副本
	FBCT_ZHUANZHI_PERSONAL = 13,	-- 个人转职副本
	FBCT_MIGONGXIANFU_TEAM = 14,	-- 迷宫仙府副本
-- FBCT_WUSHUANG = 15,				-- 无双副本	FBCT_PATA = 16,					-- 爬塔
	FBCT_CAMPGAOJIDUOBAO = 17,		-- 师门高级夺宝
	FBCT_LIMIT_TIME_DABAO = 18,		-- 限时打宝副本
	FBCT_WELKIN = 20, 				-- 天仙阁
	FBCT_LINGYUFB = 21, 			-- 灵玉副本
	FBCT_EQUIPFB = 22,				-- 装备本
	FBCT_WELFAREFB = 23,			-- 福利本
	FBCT_WEAPONFB = 24,				-- 武器本
	FBCT_WUJINJITAN = 25,			-- 无尽祭坛(炼狱洞窟)
	FBCT_EXPFB = 26,				-- 经验副本
	FBCT_SHITUPATA = 27,			-- 师徒爬塔副本
	GAOZHANFB = 28,					-- 高战副本
	WUSHUANGFB = 29,				-- 无双副本
	FBCT_MULTI_CHALLENGE = 30,		-- 多人爬塔
	FBCT_TONGBIBEN = 32, 			-- 铜币本
	FBCT_PETBEN = 33, 				-- 宠物本
	FBCT_TAFANG = 34, 				-- 塔防副本
	FBCT_TASKFB_BUFF = 35,			-- 任务副本-buff怪
	FBCT_TASKFB_SHIZHU = 36,		-- 任务副本-石柱怪
	FBCT_TASKFB_SHOUHU = 37,		-- 任务副本-守护仙女
	FBCT_TASKFB_ZIBAO = 38,			-- 任务副本-自爆怪
	FBCT_TASKFB_ZHILIAO = 39,		-- 任务副本-治疗怪CSEnterFB
	TEAM_EQUIP = 40, 				-- 多人装备本
	XIN_MO_FB = 41, 				-- 心魔副本
	FBCT_NEWPLAYERFB = 42, 			-- 新手副本
	FBCT_PERSON_BOSS = 43,          -- 个人boss副本
	HIGH_TEAM_EQUIP = 44,           -- 远古仙殿
	FBCT_XINGTIAN_LAIXI2 = 45,		-- 仙器解封--刑天来袭
	ZHUSHENTA_FB = 46,				-- 诛神塔
	GUIDE_BOSS = 47,				-- 引导boss
	LINGHUNGUANGCHANG = 48,			-- 灵魂广场
	YUANGUXIANDIANYINDAO = 49,		-- 远古仙殿引导副本类型
	FBCT_WABAO_TEAM_FB = 50,		-- 挖宝组队本
	FBCT_TIANSHEN_FB = 51,			-- 天神副本
	FBCT_BAGUAMIZHEN_FB = 52,		-- 八卦迷阵副本
	FB_MANHUANG_GUDIAN_FB = 53,		-- 组队-蛮荒古殿副本
	FBCT_FAKE_TIANSHEN_FB = 54,		-- 伪天神副本
	FBCT_FAKE_PETBEN = 55,			-- 伪宠物本
	FBCT_SHENYUAN_BOSS = 56,		-- 深渊boss
	PERSON_SPECIAL_BOSS = 62,		-- 个人特殊Boss副本
	FBCT_GHOST_GLOBAL_FB = 63,		-- 捉鬼副本 - 公共
	PERSON_MABIBOSS_FB = 64,        -- 麻痹BOSS副本
	FB_WUHUN_TOWER = 65,			-- 武魂塔副本		
	FB_WUHUN_EXP_WEST = 66,			-- 历练西行副本	
	HUNDREDFOLD_DROP_FB = 67,       --百倍爆率真充boss副本
	FBCT_TEAM_COMMON_BOSS_FB1 = 68,	--通用组队副本类型1
	FBCT_TEAM_COMMON_BOSS_FB2 = 69,	--通用组队副本类型2
	FBCT_TEAM_COMMON_BOSS_FB3 = 70,	--通用组队副本类型3
	FBCT_TEAM_COMMON_TOWER_FB1 = 71,--通用组队符文塔副本类型1
	FBCT_TEAM_COMMON_TOWER_FB2 = 72,--通用组队符文塔副本类型2
}

NewFuBenType = {
	Type_5 = 5,		-- 击败圣兽
	Type_6 = 6,		-- 殊死一战
	Type_7 = 7,		-- 精灵复仇
}

--根据副本类型来获取进入/退出是否播放人物飞天的特效
PlayEffectFuBenType =
{
	[FUBEN_TYPE.FBCT_WELKIN] = 1,
	[FUBEN_TYPE.FBCT_TONGBIBEN] = 1,
	[FUBEN_TYPE.FBCT_PETBEN] = 1,
	[FUBEN_TYPE.FBCT_TAFANG] = 1,
	[FUBEN_TYPE.TEAM_EQUIP] = 1,
	[FUBEN_TYPE.FBCT_NEWPLAYERFB] = 1,
	[FUBEN_TYPE.FBCT_PERSON_BOSS] = 1,
	[FUBEN_TYPE.FBCT_WUJINJITAN] = 1,
}

GUWU_FB_TYPE =
  {
    GUWU_FB_TYPE_INVALID = 0,
    GUWU_FB_TYPE_EXP_FB = 1,
    GUWU_FB_TYPE_WORLD_BOSS = 2,
  }

FuBenWGData = FuBenWGData or BaseClass()

function FuBenWGData:__init()
	if FuBenWGData.Instance then
		ErrorLog("[FuBenWGData]:Attempt to create singleton twice!")
		return
	end
	FuBenWGData.Instance = self

	self.story_roll_pool = {}
	self.fb_scene_logic_info = {}
	self.teamfb_ysjt_scene_info = {}
	self.fb_tower_defend_info = {
		reason = 0,
		time_out_stamp = 0,
		is_finish = 0,
		is_pass = 0,
		pass_time_s = 0,
		life_tower_left_hp = 0,
		life_tower_left_maxhp = 0,
		curr_wave = 0,
		energy = 0,
		next_wave_refresh_time = 0,
		clear_wave_count = 0,
		last_perform_time_list = {},
		get_coin = 0,
		pick_drop_list = {},
	}
	self.out_fb_time = 0
	self.team_tower_auto_reflesh = true
	self.pick_info = {}
	self.drop_info = {}
	self.score = 0
	self.left_get_score_times = 0

	self.entered_fb = nil
	self.teamexp_add_hurt = 0
	self.jingyanben_first_time_flag = 0

	self.taskfb_cfg = ConfigManager.Instance:GetAutoConfig("taskfb_config_auto")
	local newplayer_fb_cfg_auto = ConfigManager.Instance:GetAutoConfig("newplayer_fb_cfg_auto")
	self.newplayer_fb_type_cfg = ListToMap(newplayer_fb_cfg_auto.fb_type, "fb_type")
	self.newplayer_fb_id_cfg = ListToMap(newplayer_fb_cfg_auto.fb_type, "scene_id")
	self.newplayer_fb_monster_cfg = ListToMap(newplayer_fb_cfg_auto.monster, "fb_type", "wave", "seq")
	self.newplayer_fb_monster_cg_cfg = ListToMap(newplayer_fb_cfg_auto.monster_cg, "scene_id")

	self.bagua_scene_info = {
		type_id = 0,
	}
	self.guwu_info = {
		scene_type = 0,
		coin_guwu_count = 0,
		gold_guwu_count = 0,
	}

	self.fb_auto_coin_guwu_flag = 0
	self.fb_auto_gold_guwu_flag = 0
	self.lose_scene_type_cache = nil
end

function FuBenWGData:__delete()
	FuBenWGData.Instance = nil
end

function FuBenWGData:GetSceneGuwuType()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Wujinjitan or scene_type == SceneType.LingHunGuangChang then
		return GUWU_FB_TYPE.GUWU_FB_TYPE_EXP_FB
	elseif scene_type == SceneType.WorldBoss or scene_type == SceneType.GUIDE_BOSS then
		return GUWU_FB_TYPE.GUWU_FB_TYPE_WORLD_BOSS
	end
	return 0
end

function FuBenWGData:SetGuWuData(protocol)
	self.fb_auto_coin_guwu_flag = protocol.fb_auto_coin_guwu_flag
	self.fb_auto_gold_guwu_flag = protocol.fb_auto_gold_guwu_flag
end

function FuBenWGData:GetGuWuData()
	local index = self:GetSceneGuwuType()
	return bit:_and(self.fb_auto_coin_guwu_flag, bit:_lshift(1,index)) ~= 0 and 1 or 0 ,
	bit:_and(self.fb_auto_gold_guwu_flag, bit:_lshift(1,index)) ~= 0 and 1 or 0
end

function FuBenWGData:GetTaskFBCfg()
	-- body
	return self.taskfb_cfg
end

--副本左边任务面板-- fb_task_data.tabbar = {"word_fuben_info", "word_boss"}
-------------------- fb_task_data.text_t = {{文字，替换文字，倒计时形式，段落格式}}
function FuBenWGData:GetFormatTaskData()
	local scene_type = Scene.Instance:GetSceneType()
	local fb_task_data = {}
	if SceneType.StoryFB == scene_type then
		fb_task_data = self:GetStoryFbSceneTaskInfo()
	elseif SceneType.ExpFb == scene_type then
		fb_task_data = self:GetExpFbSceneTaskInfo()
	elseif SceneType.CoinFb == scene_type then
		fb_task_data = self:GetCoinFbSceneTaskInfo()
	elseif SceneType.TeamFB == scene_type then
		fb_task_data = self:GetYsjtTeamFbSceneTaskInfo()
	-- elseif SceneType.ManyTowerFB == scene_type then
	-- 	fb_task_data = self:GetTeamTowerDefendTaskInfo()
	elseif SceneType.MiGongXianFu == scene_type then
		fb_task_data = self:GetMgxfTeamFbSceneTaskInfo()
	elseif SceneType.HunYanFb == scene_type then
		fb_task_data = WeddingWGData.Instance:GetWeddingInfodata()
	elseif SceneType.YaoShouPlaza == scene_type then
		fb_task_data = ActivityWGData.Instance:GetYaoShouPlazaData()
	elseif SceneType.SuoYaoTa == scene_type then
		fb_task_data = ActivityWGData.Instance:GetSuoYaoTaData()
	elseif SceneType.ShuiJing == scene_type then
		fb_task_data = ActivityWGData.Instance:GetShuiJingData()
	-- elseif SceneType.QingYuanFB == scene_type then
	-- 	fb_task_data = MarryWGData.Instance:GetMarryFbData()
	elseif SceneType.GuildMiJingFB == scene_type then
		fb_task_data = GuildWGData.Instance:GetGuildFbData()
	-- elseif SceneType.GongChengZhan == scene_type then
	-- 	fb_task_data = GongChengData.Instance:GetGongChengZhanTaskInfo()
	elseif SceneType.TowerDefend == scene_type then
		fb_task_data = self:GetTowerDefendTaskInfo()
	elseif SceneType.ZhongKui == scene_type then
		fb_task_data = self:GetZhongKuiSceneTaskInfo()
	elseif SceneType.LingyuFb == scene_type then
		fb_task_data = DailyWGData.Instance:GetLingyuSceneTaskInfo()
	elseif SceneType.WeaponFb == scene_type then
		fb_task_data = self:GetPeteqFbSceneTaskInfo()
	elseif SceneType.EquipFb == scene_type then
		fb_task_data = self:GetEquipFbSceneTaskInfo()
	elseif SceneType.Wujinjitan == scene_type then
		fb_task_data = self:GetWujinjitanSceneTaskInfo()
	elseif SceneType.DaBaoFb == scene_type then
		fb_task_data = self:GetFbDaBaoSceneTaskInfo()
	end
	return fb_task_data
end

function FuBenWGData:OnFbSceneLogicInfo(protocol)
	self:ResetFbSceneLogicInfo()
	self.fb_scene_logic_info.time_out_stamp = protocol.time_out_stamp   				--副本超时结束时间戳（可用于倒计时）
	self.fb_scene_logic_info.flush_timestamp = protocol.flush_timestamp   				--boss刷新时间戳（可用于倒计时）
	self.fb_scene_logic_info.scene_type = protocol.scene_type 							--场景类型
	self.fb_scene_logic_info.is_finish = protocol.is_finish 							--是否结束
	self.fb_scene_logic_info.is_pass = protocol.is_pass 								--是否通关
	self.fb_scene_logic_info.is_active_leave_fb = protocol.is_active_leave_fb 			--是否主动退出
	self.fb_scene_logic_info.total_boss_num = protocol.total_boss_num 					--boss总数量
	self.fb_scene_logic_info.total_allmonster_num = protocol.total_allmonster_num 		--怪物总数量（包括普通怪和boss)
	self.fb_scene_logic_info.kill_boss_num = protocol.kill_boss_num 					--已击杀boss数量
	self.fb_scene_logic_info.kill_allmonster_num = protocol.kill_allmonster_num 		--已击杀怪物总数量（包括普通怪和boss)

	self.fb_scene_logic_info.pass_time_s = protocol.pass_time_s 						--进入副本到目前经过的时间（少）
	self.fb_scene_logic_info.coin = protocol.coin 										--铜币
	self.fb_scene_logic_info.exp = protocol.exp 										--经验

	self.fb_scene_logic_info.param1 = protocol.param1
	self.fb_scene_logic_info.param2 = protocol.param2
	self.fb_scene_logic_info.param3 = protocol.param3

end

function FuBenWGData:GetIsActiveLeaveFb()
	if self.fb_scene_logic_info ~= nil then
		return self.fb_scene_logic_info.is_active_leave_fb or 0
	end
	return 0
end


function FuBenWGData:SetFbGuWuInfo(info)
	self.guwu_info.scene_type = info.scene_type
	self.guwu_info.coin_guwu_count = info.coin_guwu_count
	self.guwu_info.gold_guwu_count = info.gold_guwu_count
end

function FuBenWGData:GetFbGuWuInfo()
	return self.guwu_info
end
function FuBenWGData:IsPass()
	return self.fb_scene_logic_info.is_pass == 1
end

-- 八卦迷阵本信息
function FuBenWGData:SetBaGuaMiZhenFBStatus(protocol)
	self.bagua_scene_info.type_id = protocol.type_id
end


function FuBenWGData:GetBaGuaMiZhenFBStatus()
	return self.bagua_scene_info.type_id
end

function FuBenWGData:ClearBaGuaMiZhenFBStatus()
	self.bagua_scene_info = {
		type_id = 0,
	}
end
-- 个人塔防
function FuBenWGData:OnTowerDefendInfo(protocol)
	local vo = self.fb_tower_defend_info
	vo.reason = protocol.reason
	vo.time_out_stamp = protocol.time_out_stamp
	vo.is_finish = protocol.is_finish
	vo.is_pass = protocol.is_pass
	vo.pass_time_s = protocol.pass_time_s
	vo.life_tower_left_hp = protocol.life_tower_left_hp
	vo.life_tower_left_maxhp = protocol.life_tower_left_maxhp
	vo.curr_wave = protocol.curr_wave
	vo.energy = protocol.energy
	vo.next_wave_refresh_time = protocol.next_wave_refresh_time
	vo.clear_wave_count = protocol.clear_wave_count
	vo.last_perform_time_list = protocol.last_perform_time_list
	vo.get_coin = protocol.get_coin
	vo.pick_drop_list = protocol.pick_drop_list
	if vo.is_finish ~= 1 and self.team_tower_auto_reflesh and vo.curr_wave + 1 == vo.clear_wave_count then
		FuBenWGCtrl.Instance:SendTeamTowerDefendNextWave()
	end
end

-- 宠装本
function FuBenWGData:OnPeteqFbInfo(protocol)
	self.peteq_fb_info = self.peteq_fb_info or {}
	local vo = self.peteq_fb_info
	vo.time_out_stamp = protocol.time_out_stamp
	vo.is_finish = protocol.is_finish
	vo.is_pass = protocol.is_pass
	vo.kill_boss_num = protocol.kill_boss_num
	vo.total_boss_num = protocol.total_boss_num
	vo.kill_allmonster_num = protocol.kill_allmonster_num
	vo.total_monster_num = protocol.total_monster_num
	vo.pass_time = protocol.pass_time
end

-- 装备本
function FuBenWGData:OnEquipFbInfo(protocol)
	self.equip_fb_info = self.equip_fb_info or {}
	local vo = self.equip_fb_info
	vo.time_out_stamp = protocol.time_out_stamp
	vo.is_finish = protocol.is_finish
	vo.is_pass = protocol.is_pass
	vo.kill_boss_num = protocol.kill_boss_num
	vo.total_boss_num = protocol.total_boss_num
	vo.kill_allmonster_num = protocol.kill_allmonster_num
	vo.total_monster_num = protocol.total_monster_num
	vo.pass_time = protocol.pass_time
end

--装备副本任务信息
function FuBenWGData:GetEquipFbSceneTaskInfo()
	local fb_task_data = {}
	if nil == self.equip_fb_info then return fb_task_data end
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.KillBossText, self.equip_fb_info.kill_boss_num or 0),},
		},
	}
	return fb_task_data
end

function FuBenWGData:GetTowerDefendInfo()
	return self.fb_tower_defend_info
end

function FuBenWGData:SetOutFbTime(out_fb_time)
	self.out_fb_time = out_fb_time
end

function FuBenWGData:GetOutFbTime()
	return self.out_fb_time
end

function FuBenWGData:ResetFbSceneLogicInfo()
	for k,v in pairs(self.fb_scene_logic_info) do
		self.fb_scene_logic_info[k] = 0
	end
end
--副本信息
function FuBenWGData:GetFbSceneLogicInfo()
	local scene_type = Scene.Instance:GetSceneType()
	if SceneType.TeamFB == scene_type then
		return self.teamfb_ysjt_scene_info
	-- elseif SceneType.ManyTowerFB == scene_type then
	-- 	return self.teamfb_tower_scene_info
	elseif SceneType.MiGongXianFu == scene_type then
		return self.teamfb_mgxf_scene_info
	elseif SceneType.HIGH_TEAM_EQUIP_FB == scene_type then
		return TeamEquipFbWGData.Instance:GetHTEFbInfo()
	elseif SceneType.COPPER_FB == scene_type then
		return CopperFbWGData.Instance:GetCopperScenceInfo()
	elseif SceneType.FakeTianShenFb == scene_type then
		local ts_other_cfg = FuBenPanelWGData:GetTianShenOtherCfg()
		return {
			is_finish = 0,
			flush_timestamp = TimeWGCtrl.Instance:GetServerTime() + 5,
			time_out_stamp = TimeWGCtrl.Instance:GetServerTime() + ts_other_cfg.fb_time_s,
		}
	else
		return self.fb_scene_logic_info
	end
end

--副本星级面板使用的数据
function FuBenWGData:GetStarViewFbSceneInfo()
	local scene_type = Scene.Instance:GetSceneType()
	if SceneType.TeamFB == scene_type then
		return self.teamfb_ysjt_scene_info
	elseif SceneType.MiGongXianFu == scene_type then
		return self.teamfb_mgxf_scene_info
	elseif SceneType.HIGH_TEAM_EQUIP_FB == scene_type then
		local scene_info = TeamEquipFbWGData.Instance:GetHTEFbInfo()
		if not IsEmptyTable(scene_info) then
			local all_monster_dead = scene_info.curr_wave_index == scene_info.max_wave_count
					and scene_info.kill_monster_num == scene_info.cur_wave_monster_num
			return {
				all_monster_dead = all_monster_dead,
				is_finish = scene_info.is_finish or 0,
				flush_timestamp = scene_info.prepare_end_timestamp,
				time_out_stamp = scene_info.finish_timestamp,
				next_star_timestamp = scene_info.next_star_timestamp,
			}
		end
		return nil
	elseif SceneType.COPPER_FB == scene_type then
		local scene_info = {} 
		if CopperFbWGData.Instance then
			scene_info = CopperFbWGData.Instance:GetCopperScenceInfo()
		end
		if not IsEmptyTable(scene_info) then
			return {
				is_finish = scene_info.is_finish or 0,
				flush_timestamp = scene_info.prepare_end_timestamp,
				time_out_stamp = scene_info.time_out_timestamp,
				next_star_timestamp = scene_info.next_star_timestamp,
			}
		end
		return nil
	elseif SceneType.PET_FB == scene_type or SceneType.FakePetFb == scene_type then
		local scene_info = FuBenPanelWGData.Instance:GetPetScenceAllInfo()
		if not IsEmptyTable(scene_info) then
			return {
				is_finish = scene_info.is_finish or 0,
				flush_timestamp = scene_info.prepare_end_timestamp,
				time_out_stamp = scene_info.finish_timestamp,
				next_star_timestamp = scene_info.next_star_timestamp,
			}
		end
		return nil
	elseif SceneType.FakeTianShenFb == scene_type then
		local ts_other_cfg = FuBenPanelWGData:GetTianShenOtherCfg()
		return {
			is_finish = 0,
			flush_timestamp = TimeWGCtrl.Instance:GetServerTime() + 5,
			time_out_stamp = TimeWGCtrl.Instance:GetServerTime() + ts_other_cfg.fb_time_s,
			next_star_timestamp = 0,
		}
	elseif SceneType.GuildBoss == scene_type then
		local scence_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
		return {
			is_finish = scence_info.is_finish,
			flush_timestamp = scence_info.start_timestamp,
			time_out_stamp = 0,
			next_star_timestamp = scence_info.next_star_timestamp,
		}
	else
		return self.fb_scene_logic_info
	end
end

-- 切场景时调用
function FuBenWGData:ClearFbSceneLogicInfo()
	self.fb_scene_logic_info = {}
end

--结束奖励：铜币，经验，爬塔本
function FuBenWGData:GetFbFinishRewardItem()
	local reward_list = {}
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ExpFb then
		reward_list[1] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_EXP, num = self.fb_scene_logic_info.exp, is_bind = 0}
	elseif scene_type == SceneType.CoinFb then
		reward_list[1] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN, num = self.fb_scene_logic_info.coin}
	elseif SceneType.Fb_Welkin == scene_type then
		-- reward_list = PataData.Instance:GetPataFbFinishReward()
	end
	return reward_list
end

--剧情副本任务信息
function FuBenWGData:GetStoryFbSceneTaskInfo()
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.KillBossText, self.fb_scene_logic_info.kill_boss_num or 0),},
		},
	}
	return fb_task_data
end
--经验副本任务信息
function FuBenWGData:GetExpFbSceneTaskInfo()
	local left_num = self.fb_scene_logic_info.total_allmonster_num - self.fb_scene_logic_info.kill_allmonster_num
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.CurLeftMonster, left_num >= 0 and left_num or 0),},
			{str = Language.FuBen.KillAllToNext,},
		},
	}
	return fb_task_data
end
--铜币副本任务信息
function FuBenWGData:GetCoinFbSceneTaskInfo()
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.CurWaveNumber, self.fb_scene_logic_info.param1 or 0)},
			{str = Language.FuBen.NextMonsterTime,},
			{str = HtmlTool.GetHtml("%s", COLOR3B.YELLOW, 20), alg = RichHAlignment.HA_CENTER, timer = self.fb_scene_logic_info.param2 or 0},
		},
	}
	if self.fb_scene_logic_info.param1 > 4 then --写死第5波是最后一波（服务器端改了再改）
		fb_task_data[1].text_t[3] = {str = HtmlTool.GetHtml(Language.FuBen.LastWave, COLOR3B.YELLOW, 20),  alg = RichHAlignment.HA_CENTER}
	end
	return fb_task_data
end

--剧情副本翻牌信息
function FuBenWGData:OnStoryRollPool(protocol)
	self.story_roll_pool = protocol.roll_list
end
function FuBenWGData:GetStoryRollPool()
	return self.story_roll_pool
end

-----------------------------------------
--多人--妖兽祭坛场景信息

function FuBenWGData.GetJiTanAttrEffectId(attr_type)
	if 0 == attr_type then
		return 3162
	elseif 1 == attr_type then
		return 3163
	elseif 2 == attr_type then
		return 3160
	elseif 3 == attr_type then
		return 3161
	end
end

function FuBenWGData:OnYsjtTeamFbSceneLogicInfo(protocol)
	self.teamfb_ysjt_scene_info = {}
	self.teamfb_ysjt_scene_info.time_out_stamp = protocol.time_out_stamp
	self.teamfb_ysjt_scene_info.scene_type = SceneType.TeamFB
	self.teamfb_ysjt_scene_info.is_finish = protocol.is_finish
	self.teamfb_ysjt_scene_info.is_pass = protocol.is_pass
	self.teamfb_ysjt_scene_info.is_active_leave_fb = protocol.is_active_leave_fb
	self.teamfb_ysjt_scene_info.kill_boss_num = protocol.kill_boss_num
	self.teamfb_ysjt_scene_info.pass_time_s = protocol.pass_time_s
	self.teamfb_ysjt_scene_info.mode = protocol.mode							--当前困难模式，就是进入该副本时传的困难度
	self.teamfb_ysjt_scene_info.boss_attr_type = protocol.boss_attr_type		--boss当前属性类型
	self.teamfb_ysjt_scene_info.role_attrs = protocol.role_attrs				--属性加成角色id为索引
end

function FuBenWGData:GetTeamFbSceneLogicInfo()
	return self.teamfb_ysjt_scene_info
end

function FuBenWGData:GetYsjtTeamFbSceneTaskInfo()
	if nil == self.teamfb_ysjt_scene_info then
		return
	end
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.KillBossText, self.teamfb_ysjt_scene_info.kill_boss_num)},
		},
	}
	return fb_task_data
end

function FuBenWGData:GetYsjtTeamFbMonsterId(mode)
	local mode_list = ConfigManager.Instance:GetAutoConfig("yaoshoujitanteamfbconfig_auto").mode_list
	for k,v in pairs(mode_list) do
		if mode == v.mode then
			return v.boss_id
		end
	end
end

function FuBenWGData:GetYsjtTeamFbFinishReward()
	local reward_list = {}
	if self.teamfb_ysjt_scene_info and  0 == self.teamfb_ysjt_scene_info.is_pass then
		return reward_list
	end
	local mode_list = ConfigManager.Instance:GetAutoConfig("yaoshoujitanteamfbconfig_auto").mode_list
	for k,v in pairs(mode_list) do
		if self.teamfb_ysjt_scene_info and self.teamfb_ysjt_scene_info.mode == v.mode then
			for index = 1, 3 do
				if v["reward_item" .. index] then
					table.insert(reward_list, v["reward_item" .. index])
				end
			end
		end
	end
	return reward_list
end

-----------------------------------------
--多人--组队塔防场景信息
function FuBenWGData:OnTeamTowerDefendInfo(protocol)
	-- self.old_clear_wave = self.teamfb_tower_scene_info and self.teamfb_tower_scene_info.clear_wave
	-- self.teamfb_tower_scene_info = {}
	-- self.teamfb_tower_scene_info.scene_type = SceneType.ManyTowerFB
	-- self.teamfb_tower_scene_info.time_out_stamp = protocol.time_out_stamp
	-- self.teamfb_tower_scene_info.is_finish = protocol.is_finish
	-- self.teamfb_tower_scene_info.is_pass = protocol.is_pass
	-- self.teamfb_tower_scene_info.pass_time_s = protocol.pass_time_s
	-- -- self.teamfb_tower_scene_info.reason = protocol.reason  									--下发原因 1.初始化 2.下一波
	-- self.teamfb_tower_scene_info.life_tower_left_hp = protocol.life_tower_left_hp
	-- self.teamfb_tower_scene_info.life_tower_left_maxhp = protocol.life_tower_left_maxhp
	-- self.teamfb_tower_scene_info.curr_wave = protocol.curr_wave
	-- self.teamfb_tower_scene_info.next_wave_refresh_time = protocol.next_wave_refresh_time
	-- self.teamfb_tower_scene_info.clear_wave = protocol.clear_wave 							--消灭波数
	-- self.teamfb_tower_scene_info.mode = protocol.mode
	-- if self.team_tower_auto_reflesh and self.old_clear_wave and self.old_clear_wave ~= self.teamfb_tower_scene_info.clear_wave then
	-- 	if 1 == SocietyWGData.Instance:GetIsTeamLeader() then
	-- 		FuBenWGCtrl.Instance:SendTeamTowerDefendNextWave()
	-- 	end
	-- end
end

function FuBenWGData:GetTeamTowerDefendInfo()
	return self.teamfb_tower_scene_info
end

function FuBenWGData:GetTeamTowerDefendTaskInfo()
	if nil == self.teamfb_tower_scene_info then
		return
	end
	local per = math.floor(self.teamfb_tower_scene_info.life_tower_left_hp / self.teamfb_tower_scene_info.life_tower_left_maxhp * 100)
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.CurWaveNumber, self.teamfb_tower_scene_info.curr_wave + 1 .. "/" .. DailyWGData.Instance:GetReamTowerMaxWave())},
			{str = Language.FuBen.NextMonsterTime,},
			{str = HtmlTool.GetHtml("%s", COLOR3B.YELLOW, 20), timer = self.teamfb_tower_scene_info.next_wave_refresh_time, alg = RichHAlignment.HA_CENTER},
			{str = ""},
			{str = ""},
			{str = ""},
			{str = ""},
		},
		prog_t = {
			{x = 120, y = 125, txt = per .. "%", per = per, p = "img9_159"}
		},
		btns_t = {
			{x = 120, y = 78, func = BindTool.Bind(self.OnTeamTowerDefendNextWave, self), visible = true, enable = true, txt = Language.FuBen.RefreshNow},
		},
		check_t = {
			{x = 40, y = 30, func = BindTool.Bind(self.SetTeamTowerAutoRefresh, self), txt = Language.FuBen.AutoRefresh, def_sec = self.team_tower_auto_reflesh}
		},
	}
	return fb_task_data
end

function FuBenWGData:GetTowerDefendTaskInfo()
	local fb_task_data = {}
	local per = math.floor(self.fb_tower_defend_info.life_tower_left_hp / self.fb_tower_defend_info.life_tower_left_maxhp * 100)
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.CurWaveNumber, self.fb_tower_defend_info.curr_wave + 1 .. "/ 5")},
			{str = string.format(Language.FuBen.KillWaveNumber, self.fb_tower_defend_info.clear_wave_count .. "/ 5")},
			{str = Language.FuBen.NextMonsterTime,},
			{str = HtmlTool.GetHtml("%s", COLOR3B.YELLOW, 20), timer = self.fb_tower_defend_info.next_wave_refresh_time, alg = RichHAlignment.HA_CENTER},
			{str = Language.FuBen.TowerBlood,},
			{str = ""},
			{str = ""},
			{str = ""},
			{str = ""},
		},
		prog_t = {
			{x = 120, y = 125, txt = per .. "%", per = per, p = "img9_159"}
		},
		btns_t = {
			{x = 120, y = 90, func = BindTool.Bind(FuBenWGCtrl.Instance.SendTeamTowerDefendNextWave, FuBenWGCtrl.Instance), visible = true, enable = true, txt = Language.FuBen.RefreshNow},
		},
		check_t = {
			{x = 40, y = 42, func = BindTool.Bind(self.SetTeamTowerAutoRefresh, self), txt = Language.FuBen.AutoRefresh, def_sec = self.team_tower_auto_reflesh}
		},
	}
	return fb_task_data
end

function FuBenWGData:OnTeamTowerDefendNextWave()
	if 1 == SocietyWGData.Instance:GetIsTeamLeader() then
		FuBenWGCtrl.Instance:SendTeamTowerDefendNextWave()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ManyTower.NoLeaderTips2)
	end
end

function FuBenWGData:SetTeamTowerAutoRefresh(auto)
	self.team_tower_auto_reflesh = auto
end

-- function FuBenWGData:GetTowerDefendTeamFbFinishReward()
-- 	local reward_list = {}
-- 	if self.teamfb_tower_scene_info and  0 == self.teamfb_tower_scene_info.is_pass then
-- 		return reward_list
-- 	end
-- 	local pass_reward = ConfigManager.Instance:GetAutoConfig("towerdefendteam_auto").pass_reward
-- 	for k,v in pairs(pass_reward) do
-- 		if self.teamfb_tower_scene_info and self.teamfb_tower_scene_info.mode == v.mode then
-- 			for index = 1, 3 do
-- 				if v["item" .. index] then
-- 					table.insert(reward_list, v["item" .. index])
-- 				end
-- 			end
-- 		end
-- 	end
-- 	return reward_list
-- end

-----------------------------------------
--多人--迷宫仙府场景信息

function FuBenWGData.GetMiGongDoorEffectId(status)
	if MIGONGXIANFU_STATUS_TYPE.MGXF_DOOR_STATUS_NONE == status then
		return 3151
	elseif MIGONGXIANFU_STATUS_TYPE.MGXF_DOOR_STATUS_TO_PRVE == status or
		MIGONGXIANFU_STATUS_TYPE.MGXF_DOOR_STATUS_TO_HERE == status then
		return 3153
	elseif MIGONGXIANFU_STATUS_TYPE.MGXF_DOOR_STATUS_TO_HIDE == status then
		return 3152
	else
		return 3150
	end
end

function FuBenWGData:OnMgxfTeamFbSceneLogicInfo(protocol)
	self.teamfb_mgxf_scene_info = {}
	self.teamfb_mgxf_scene_info.scene_type = SceneType.MiGongXianFu
	self.teamfb_mgxf_scene_info.time_out_stamp = protocol.time_out_stamp
	self.teamfb_mgxf_scene_info.is_finish = protocol.is_finish
	self.teamfb_mgxf_scene_info.is_pass = protocol.is_pass
	self.teamfb_mgxf_scene_info.pass_time_s = protocol.pass_time_s
	self.teamfb_mgxf_scene_info.mode = protocol.mode
	self.teamfb_mgxf_scene_info.layer = protocol.layer  									--玩家自己所处的层
	self.teamfb_mgxf_scene_info.kill_hide_boos_num = protocol.kill_hide_boos_num
	self.teamfb_mgxf_scene_info.kill_end_boss_num = protocol.kill_end_boss_num
	self.teamfb_mgxf_scene_info.door_status_list = protocol.door_status_list				--传送点当前状态
end

function FuBenWGData:GetMgxfTeamFbSceneLogicInfo()
	return self.teamfb_mgxf_scene_info
end

function FuBenWGData:GetMgxfTeamFbSceneTaskInfo()
	if nil == self.teamfb_mgxf_scene_info then
		return
	end
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.HiddenChapterBoss, self.teamfb_mgxf_scene_info.kill_hide_boos_num, 1)},
			{str = string.format(Language.FuBen.FinalBoss, self.teamfb_mgxf_scene_info.kill_end_boss_num, 1)},
			{str = Language.FuBen.KillHiddenDesc},
		},
	}
	return fb_task_data
end

function FuBenWGData:GetMgxfTeamFbFinishReward()
	local reward_list = {}
	if self.teamfb_mgxf_scene_info and  0 == self.teamfb_mgxf_scene_info.is_pass then
		return reward_list
	end
	local mode_list = ConfigManager.Instance:GetAutoConfig("migongxianfuteamfbconfig_auto").mode_list
	for k,v in pairs(mode_list) do
		if self.teamfb_mgxf_scene_info and self.teamfb_mgxf_scene_info.mode == v.mode then
			for index = 1, 3 do
				if v["reward_item" .. index] then
					table.insert(reward_list, v["reward_item" .. index])
				end
			end
		end
	end
	return reward_list
end

function FuBenWGData:GetMgxfTeamFbLayerConfig(layer)
	local layer_list = ConfigManager.Instance:GetAutoConfig("migongxianfuteamfbconfig_auto").layer_list
	for k,v in pairs(layer_list) do
		if v.layer == layer then
			return v
		end
	end
end

function FuBenWGData:GetPeteqFbSceneTaskInfo()
	local fb_task_data = {}
	if nil == self.peteq_fb_info then
		return fb_task_data
	end
	local scene_id = Scene.Instance:GetSceneId() or 0
	local fb_cfg = DailyWGData.Instance:GetPeteqLevelCfgBySceneId(scene_id)
	if nil == fb_cfg then
		return fb_task_data
	end
	local has_time = self.peteq_fb_info.time_out_stamp - TimeWGCtrl.Instance:GetServerTime()
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.FubenHasTime, TimeUtil.FormatSecond(has_time, 2))},
			{str = string.format(Language.FuBen.FubenPassTime, TimeUtil.FormatSecond( self.peteq_fb_info.pass_time, 2))},
			{str = Language.FuBen.FubenScore},
			{str = " " .. "3★ " .. string.format(Language.FuBen.NTGFB, TimeUtil.FormatSecond(fb_cfg.sec_3_star, 2))},
			{str = " " .. "2★ " .. string.format(Language.FuBen.NTGFB, TimeUtil.FormatSecond(fb_cfg.sec_2_star, 2))},
			{str = " " .. "1★ " .. Language.FuBen.TGFB},
			{str = "" },
		},
	}
	return fb_task_data
end

----------------------------------------------------
-- 创建结算信息
----------------------------------------------------
function FuBenWGData.CreatePassVo()
	return {
		fb_type = 0,
		is_passed = 0, 			--1 显示通关评价
		clear_wave_count = 0,
		item_list = {},
		auto_exit_time = 10,	-- 自动退出时间
		tip1 = nil,
		tip2 = nil,
		tip3 = nil,
		param = nil
	}
end

--通用结束面板vo
function FuBenWGData.CreateCommonPassVo()
	return {
		title_img = "word_tgpj",
		scene_type = 0,
		is_pass = 0,
		star = 0,
		reward_list = {},
		auto_exit_time = 10,
		tip1 = Language.FuBen.TongGuanTime,
		tip2 = Language.FuBen.TongGuanReward,
		tip3 = "",
		jinghua = 0,
		is_guide = false,
	}
end

function FuBenWGData.GetFbSceneConfig(scene_type)
	local fb_scene_cfg_list = ConfigManager.Instance:GetAutoConfig("fb_scene_config_auto").fb_scene_cfg_list
	return fb_scene_cfg_list[scene_type]
end

----------------------------------------------------
-- 钟馗捉鬼信息
----------------------------------------------------
function FuBenWGData:GetZhongKuiSceneTaskInfo()
	local zhongkuizhuagui_fb_info = ActivityWGData.Instance:GetZhuaGuiFBInfo()

	local text = {}
	local str = ""
	local boss_count = zhongkuizhuagui_fb_info.boss_isdead == 1 and 0 or 1
	if ActivityWGData.Instance:GetIsHasSpecialMonster() then
		text = {
			{str = string.format(Language.FuBen.KillMonsterText, zhongkuizhuagui_fb_info.monster_count)},
			{str = string.format(Language.FuBen.KillFuliMonsterText, boss_count)},
		}
	else
		text = {
			{str = string.format(Language.FuBen.KillMonsterText, zhongkuizhuagui_fb_info.monster_count)},
		}
	end

	local fb_task_data = {}
	local text_t = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = text,
	}
	return fb_task_data
end

-- 掉落
function FuBenWGData:SetPickInfo(info)
	self.pick_info = {}
	self.pick_info[info.scene_type] = info
end

function FuBenWGData:GetPickInfo(scene_type)
	return self.pick_info[scene_type]
end

function FuBenWGData:GetPickRewardList(scene_type)
	local info = self.pick_info[scene_type]
	local pick_list = {}
	if nil == info then return pick_list end
	if info.exp > 0 then
		local vo = {}
		vo.item_id = COMMON_CONSTS.VIRTUAL_ITEM_EXP
		vo.num = info.exp
		table.insert(pick_list, vo)
	end
	if info.bind_coin > 0 then
		local vo = {}
		vo.item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN
		vo.num = info.bind_coin
		table.insert(pick_list, vo)
	end
	if info.bind_gold > 0 then
		local vo = {}
		vo.item_id = COMMON_CONSTS.VIRTUAL_ITEM_BINDGOL
		vo.num = info.bind_gold
		table.insert(pick_list, vo)
	end
	for k,v in pairs(info.item_list) do
		table.insert(pick_list, v)
	end
	return pick_list
end

------无尽祭坛

function FuBenWGData:OnWujinjitanSceneInfo(info)
	self.wujinjitan_scene_info = self.wujinjitan_scene_info or {}
	self.wujinjitan_scene_info.time_out_stamp = info.time_out_stamp
	self.wujinjitan_scene_info.is_finish = info.is_finish
	self.wujinjitan_scene_info.is_pass = info.is_pass
	self.wujinjitan_scene_info.pass_time = info.pass_time
	self.wujinjitan_scene_info.cur_wave = info.cur_wave
	self.wujinjitan_scene_info.cur_wave_finish_time = info.cur_wave_finish_time
	self.wujinjitan_scene_info.wave_remain_monster_num = info.wave_remain_monster_num
	self.wujinjitan_scene_info.opener_roleid = info.opener_roleid
end

function FuBenWGData:GetWujinjitanTeamFbFinishReward()
	local reward_list = {}
	if nil == self.wujinjitan_scene_info then
		return reward_list
	end
	local is_opener = self.wujinjitan_scene_info.opener_roleid == RoleWGData.Instance.role_vo.role_id
	local wave_cfg = ConfigManager.Instance:GetAutoConfig("teamwujinjitanfbconfig_auto").wave
	for k,v in pairs(wave_cfg) do
		if v.wave == self.wujinjitan_scene_info.cur_wave then
			if is_opener then
				table.insert(reward_list, v.open_reward)
			else
				table.insert(reward_list, v.assist_reward)
			end
		end
	end
	return reward_list
end

function FuBenWGData:GetWujinjitanWaveCfg(wave)
	local wave_cfg = ConfigManager.Instance:GetAutoConfig("teamwujinjitanfbconfig_auto").wave
	for k,v in pairs(wave_cfg) do
		if v.wave == wave then
			return v
		end
	end
	return nil
end

function FuBenWGData:GetWujinjitanMaxWave()
	local wave_cfg = ConfigManager.Instance:GetAutoConfig("teamwujinjitanfbconfig_auto").wave
	return wave_cfg[#wave_cfg].wave
end

function FuBenWGData:GetWujinjitanSceneTaskInfo()
	local fb_task_data = {}
	if nil == self.wujinjitan_scene_info then return fb_task_data end
	local wave_cfg = self:GetWujinjitanWaveCfg(self.wujinjitan_scene_info.cur_wave)
	if nil == wave_cfg then return fb_task_data end
	local max_wave = self:GetWujinjitanMaxWave()
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = Language.FuBen.LayerHasTime, alg = RichHAlignment.HA_LEFT, timer = self.wujinjitan_scene_info.cur_wave_finish_time},
			{str = string.format(Language.FuBen.FubenJindu, self.wujinjitan_scene_info.cur_wave + 1, max_wave + 1)},
			{str = string.format(Language.FuBen.CurLeftMonster, self.wujinjitan_scene_info.wave_remain_monster_num)},
			{str = Language.FuBen.PassLayerReward},
			{str = ""},
			{str = ""},
			{str = ""},
			{str = Language.FuBen.OpenerAndAssist},
		},
		item_t = {
			{itemRender = ItemCell, item_data = wave_cfg.open_reward, x = 20, y = 50},
			{itemRender = ItemCell, item_data = wave_cfg.assist_reward, x = 120, y = 50},
		}
	}
	return fb_task_data
end

-- 打宝副本任务信息
function FuBenWGData:GetFbDaBaoSceneTaskInfo()
	local has_time = MapWGData.Instance:GetServerTime()
	has_time = has_time - TimeWGCtrl.Instance:GetServerTime()
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = string.format(Language.FuBen.FubenHasTime, TimeUtil.FormatSecond2HMS(has_time))},
		},
	}
	return fb_task_data
end

function FuBenWGData:SetDropInfo(info)
	self.drop_info = {}
	self.drop_info[info.fb_type] = info
end

function FuBenWGData:GetDropInfo(fb_type)
	return self.drop_info[fb_type]
end

function FuBenWGData:GetFbEndInfo(get_coin, get_exp, get_reward_list)
	return {get_coin = get_coin, get_exp = get_exp, get_reward_list = get_reward_list}
end

-- 副本中不出现活动弹窗和转换按钮
function FuBenWGData:IsInFuBenScene()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.Common then
		return true
	end
	return false
end

function FuBenWGData:SetNewPlayerFBInfo(protocol)
	self.new_player_data = protocol.new_player_data

	--服务器下发延时时调用
	if self.new_player_data_callback then
		self.new_player_data_callback()
		self.new_player_data_callback = nil
	end
	GlobalEventSystem:Fire(MainUIEventType.SETMAINTASK)
end

function FuBenWGData:SetNewPlayerFBPassInfo(protocol)
	self.new_player_fb_pass_flag = protocol.new_player_fb_pass_flag
end

function FuBenWGData:GetWaterQiLinFBFlag()
	if not self.new_player_fb_pass_flag then return 0 end
	return self.new_player_fb_pass_flag[5]
end

function FuBenWGData:GetNewPlayerFBInfo()
	return self.new_player_data or {}
end

--新手副本回调
function FuBenWGData:SetNewPlayerFBInfoCallBack(callback)
	self.new_player_data_callback = callback
end

-- 切场景时调用
function FuBenWGData:ClearNewPlayerFBInfo()
	self.new_player_data = nil
end

function FuBenWGData:SetEnterFb(entered_fb)
	self.entered_fb = entered_fb
end

function FuBenWGData:GetEnterFb()
	return self.entered_fb
end

function FuBenWGData:SetTeamExpFbAddHurt(add_hurt)
	self.teamexp_add_hurt = add_hurt
end

function FuBenWGData:GetTeamExpFbAddHurt()
	return self.teamexp_add_hurt
end

function FuBenWGData:GetNewPlayerFbCfg(fb_type)
	return self.newplayer_fb_type_cfg[fb_type]
end

function FuBenWGData:GetNewPlayerFbCfgBySceneId(scene_id)
	return self.newplayer_fb_id_cfg[scene_id]
end

function FuBenWGData:GetSceneNeedHideObjGroup(scene_id)
	local cfg = self:GetNewPlayerFbCfgBySceneId(scene_id)
	if cfg and cfg.hide_scene_object_group ~= "" then
		return cfg.hide_scene_object_group
	end

	return -1
end

function FuBenWGData:GetNewPlayerFbMonsteCount(fb_type)
	local count = 0
	if self.newplayer_fb_monster_cfg[fb_type] then
		for k,v in pairs(self.newplayer_fb_monster_cfg[fb_type]) do
			for k1,v1 in pairs(v) do
				count = count + v1.monster_num
			end
		end
	end
	return count
end

function FuBenWGData:GetNewPlayerFbMonsterCfg(...)
	return CheckList(self.newplayer_fb_monster_cfg, ...)
end

function FuBenWGData:GetNewPlayerFbBossShowCfg(scene_id)
	return self.newplayer_fb_monster_cg_cfg[scene_id]
end

function FuBenWGData:CheckNewPlayerFbBossShowCfgHaveBossId(scene_id, boss_id)
	local fb_monster_cg_cfg = self:GetNewPlayerFbBossShowCfg(scene_id)
	if fb_monster_cg_cfg and fb_monster_cg_cfg.boss_id == boss_id then
		return true
	end
	
	return false
end

function FuBenWGData:FiltrateDefenseDrop(data_list)
	local stuff_id = ConfigManager.Instance:GetAutoConfig("juhun_cfg_auto").other[1].stuff1_id

	local sort_func = function (a, b)
		local item_cfg_a = ItemWGData.Instance:GetItemConfig(a.item_id)
		local item_cfg_b = ItemWGData.Instance:GetItemConfig(b.item_id)
		local order_a = 1000
		local order_b = 1000
		if item_cfg_a.param1 > 128 and item_cfg_a.param1 < 134 then -- 灵魂碎片
			order_a = order_a + 100
		end

		if item_cfg_b.param1 > 128 and item_cfg_b.param1 < 134 then -- 灵魂碎片
			order_b = order_b + 100
		end
		order_a = order_a - item_cfg_a.color
		order_b = order_b - item_cfg_b.color

		return order_a < order_b
	end

	local record_list = {}

	for k,v in pairs(data_list) do
		if v.item_id ~= stuff_id then -- 筛选掉仙魂石
			table.insert(record_list, v)
		end
	end

	table.sort(record_list, sort_func)
	record_list[0] = table.remove(record_list, 1)
	return record_list
end

---------------------诛神塔 ------------------------
--设置诛神塔副本信息
function FuBenWGData:SetZhushenTaFbInfo(info)
	self.zhushenta_info_list = info
end
--获取诛神塔副本信息
function FuBenWGData:GetZhushenTaFbInfo()
	return self.zhushenta_info_list
end

--根据层级获取怪物总数
function FuBenWGData:GetMonsterTotalCountBylayout(layout_level)
	local zhushenta_level_list = ConfigManager.Instance:GetAutoConfig("killgod_fb_cfg_auto").level
	for i ,v in pairs(zhushenta_level_list) do
		if v.level == layout_level then
			return v
		end
	end
	return {}
end

function FuBenWGData:GetZSTDropPer(star_num)
	local other_cfg = ConfigManager.Instance:GetAutoConfig("killgod_fb_cfg_auto").other
	if star_num == 3 then
		return other_cfg[1].three_star_Prob
	end
	if star_num == 2 then
		return other_cfg[1].two_star_Prob
	end
	if star_num == 1 then
		return other_cfg[1].one_star_Prob
	end
	if star_num == 0 then
		return other_cfg[1].zero_star_Prob
	end

end

function FuBenWGData:SetKillBossTimeStamp(value)
	self.kill_boss_time_stamp = value
end
function FuBenWGData:GetKillBossTimeStamp()
	return self.kill_boss_time_stamp
end

function FuBenWGData:GetIsCountDownKillBoss()
	return self.is_one_boss_kill
end
function FuBenWGData:SetIsCountDownKillBoss(is_kill_one)
	self.is_one_boss_kill = is_kill_one
end
function FuBenWGData:SetEXPFbIsFirstEnter(value)
	self.jingyanben_first_time_flag = value
--	print_error("设置是否是第一次进入 jingyanben_first_time_flag",self.jingyanben_first_time_flag)
end
function FuBenWGData:GetEXPFbIsFirstEnter()
	return self.jingyanben_first_time_flag
end

function FuBenWGData:DefFBState( counting )
	if counting then
		self.fb_counting = true
		return
	end
	return self.fb_counting
end

function FuBenWGData:SetTXGEnter(bo)
	self.fb_txg_enter_flag = bo
end

function FuBenWGData:GetTXGEnter()
	return self.fb_txg_enter_flag
end

function FuBenWGData:SetCurPetIndex(index)
	self.cur_pet_index = index
end

function FuBenWGData:GetCurPetIndex()
	return self.cur_pet_index
end

-- 获取副本进入的第一个视角
function FuBenWGData:GetFbCamearCfg(scene_id, scene_type)
	-- 跨服龙脉
	if scene_type == SceneType.CrossLongMai then
		return CrossLongMaiWGData.Instance:GetCameraDefaultAngle()
	end

	local camear_cfg = nil
	local fb_config = ConfigManager.Instance:GetAutoConfig("fb_scene_config_auto")
	if fb_config then
		camear_cfg = fb_config.camera_view
	end

	if camear_cfg then
		for k,v in pairs(camear_cfg) do
			if v.scene_id == scene_id then
				return v
			end
		end
	end
end

function FuBenWGData:GetOrdCamearCfg(scene_id, x, y)
	if nil == self.ord_camear_cfg then
		local fb_config = ConfigManager.Instance:GetAutoConfig("fb_scene_config_auto")
		if fb_config then
			self.ord_camear_cfg = fb_config.ord_camera_view
		end
	end
	if self.ord_camear_cfg then
		for k,v in pairs(self.ord_camear_cfg) do
			if v.scene_id == scene_id then
				local dis = GameMath.GetDistance(x, y, v.point_x, v.point_y, false)
				if dis <= 400 then
					return v
				end
			end
		end
	end
end

function FuBenWGData:SetCombineMark(protocol)
	if protocol and protocol.use_combine_flag then
		self.use_combine_flag =  bit:d2b(protocol.use_combine_flag) 
		self.combine_count = protocol.combine_count
		return
	end
	return self.use_combine_flag or 0,self.combine_count or 1
end

function FuBenWGData:GetCombineStatus(fb_type)
	if self.use_combine_flag then
		return self.use_combine_flag[32-fb_type]
	end
end

function FuBenWGData:GetCombineCfg()
	local combine_cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto")
	return combine_cfg.fb_combine
end

function FuBenWGData:GetSaoDangCfg(fb_type)
	local combine_cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto")
	if combine_cfg and combine_cfg.fb_sweep then
		for i,v in ipairs(combine_cfg.fb_sweep) do
			if v.type == fb_type then
				return v
			end
		end
	end
	return nil
end

function FuBenWGData:GetSetFBPrepareTime( time )
	if time then
		self.fb_prepare_time = time
		return
	end
	return self.fb_prepare_time or 0
end

function FuBenWGData:SetGuildShoeHuEndTime( protocol )
	self.guild_shouhu_time = protocol.finish_timestamp
end

function FuBenWGData:GetGuildShoeHuEndTime()
	return self.guild_shouhu_time
end
function FuBenWGData:SetEnterFbCombineCount(fb_type, count)
	if fb_type == FB_COMBINE_TYPE.WUJINJITAN or fb_type == FB_COMBINE_TYPE.LINGHUN_GUANGCHANG then
		self.exp_combine_count = count
	elseif fb_type == FB_COMBINE_TYPE.YUANGU then
		self.yuangu_combine_count = count
	elseif fb_type == FB_COMBINE_TYPE.KILL_GOD_TOWER then
		self.zhushenta_combine_count = count
	elseif fb_type == FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN then
		self.manhuang_combine_count = count
	elseif fb_type == FB_COMBINE_TYPE.COPPER_FB then
		self.copper_combine_count = count
	elseif fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB1 then
		self.control_beasts_combine_count = count
	elseif fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB2 then
		self.beauty_combine_count = count
	elseif fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB3 then
		self.wuhun_combine_count = count
	elseif fb_type == FB_COMBINE_TYPE.TEAM_COMMON_TOWER_FB1 then
		self.rune_tower_combine_count = count
	end
end
function FuBenWGData:GetEnterFbCombineCount(fb_type)
	if fb_type == FB_COMBINE_TYPE.WUJINJITAN or fb_type == FB_COMBINE_TYPE.LINGHUN_GUANGCHANG then
		return self.exp_combine_count or 0
	elseif fb_type == FB_COMBINE_TYPE.YUANGU then
		return self.yuangu_combine_count or 0
	elseif fb_type == FB_COMBINE_TYPE.KILL_GOD_TOWER then
		return self.zhushenta_combine_count or 0
	elseif fb_type == FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN then
		return self.manhuang_combine_count or 0
	elseif fb_type == FB_COMBINE_TYPE.COPPER_FB then
		return self.copper_combine_count or 0
	elseif fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB1 then
		return self.control_beasts_combine_count or 0
	elseif fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB2 then
		return self.beauty_combine_count or 0
	elseif fb_type == FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB3 then
		return self.wuhun_combine_count or 0
	elseif fb_type == FB_COMBINE_TYPE.TEAM_COMMON_TOWER_FB1 then
		return self.rune_tower_combine_count or 0
	end
end
function FuBenWGData:ResertEnterFbCombineCount()
	self.exp_combine_count = 0
	self.yuangu_combine_count = 0
	self.zhushenta_combine_count = 0
	self.manhuang_combine_count = 0
	self.copper_combine_count = 0
	self.control_beasts_combine_count = 0
	self.beauty_combine_count = 0
	self.wuhun_combine_count = 0
	self.rune_tower_combine_count = 0
end

--设置副本主动离开标记
function FuBenWGData:SetFuBenInitiativeToLeave(scene_type, is_initiative_to_leave)
	if not self.initiative_leave_list then
		self.initiative_leave_list = {}
	end
	self.initiative_leave_list[scene_type] = is_initiative_to_leave
end

--获取副本主动离开标记
function FuBenWGData:GetFuBenInitiativeToLeave(scene_type)
	if not self.initiative_leave_list then
		self.initiative_leave_list = {}
	end
	return self.initiative_leave_list[scene_type]
end

function FuBenWGData:SetLeaveFbStr(str, ignore)
	self.level_str = str
	self.is_ignore_boss_scene = ignore
end

function FuBenWGData:GetLeaveFbStr()
	if self.level_str then
		return self.level_str, self.is_ignore_boss_scene
	else
		return Language.Dungeon.ConfirmLevelFB, true
	end
end

function FuBenWGData:GetFuhuoBtn()
	if not self.fuhuo_sort then
		self.fuhuo_sort = {
			{	-- 装备
				img_name = 'a2_zjm_fb_tub5',
				view_name = GuideModuleName.Equipment,
				view_name2 = "btn_equip_name",
				tab_index = TabIndex.equipment_strength,
			},
			{	-- 坐骑
				img_name = 'a2_zjm_fb_tub6',
				view_name = GuideModuleName.NewAppearanceWGView,
				view_name2 = "btn_mount_name",
				tab_index = TabIndex.new_appearance_upgrade_mount,
			},
			{	-- 商店
				img_name = 'a2_zjm_fb_tub3',
				view_name = GuideModuleName.Market,
				view_name2 = "btn_shop_name",
				tab_index = nil,
			},
		}
	end

	return self.fuhuo_sort
end

function FuBenWGData:SetLoseSceneTypeChche(scene_type)
	self.lose_scene_type_cache = scene_type
end

function FuBenWGData:GetLoseSceneTypeChche()
	return self.lose_scene_type_cache
end