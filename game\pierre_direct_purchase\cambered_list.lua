local PI = Mathf.PI
local TWO_PI = Mathf.PI * 2

-- 模拟弧形列表(可实现环)
CamberedList = CamberedList or BaseClass()

function CamberedList:__init(param)
    local parent = param.scroll_list
    self.rect_transform = parent.rect
    self.parent_transform = parent.gameObject.transform

    self.angle_delta = param.angle_delta or 0   -- 两个item之间的差角
    self.center_x = param.center_x or 0         -- 椭圆圆心-横坐标
    self.center_y = param.center_y or 0         -- 椭圆圆心-纵坐标
    self.radius_x = param.radius_x or 300       -- 椭圆半长轴（长短相等则为正圆）
    self.radius_y = param.radius_y or 300       -- 椭圆半短轴
    self.scale_min = param.scale_min or 1       -- 最小缩放比例（可实现近大远小的效果）
    self.scale_max = param.scale_max or 1       -- 最大缩放比例
    self.alpha_min = param.alpha_min or 1       -- 最小透明比例（可实现近显远隐的效果）
    self.alpha_max = param.alpha_max or 1       -- 最大透明比例
    self.speed = param.speed or 0.3             -- 移动速度参数
    self.is_circle = param.is_circle or false   -- 标记，平分item之间的差角
    self.arg_adjust = param.arg_adjust or 1     -- 调整位置参数（可实现近疏远密的效果）
    self.origin_rotation = param.origin_rotation or 0       -- 椭圆的旋转角度（将椭圆按角度旋转）（可实现不同角度开始）
    self.arg_speed = (param.arg_speed or 1) / 100           -- 手动拖动时的速度控制参数
    self.viewport_count = param.viewport_count or 0         -- 固定数量（X > 0 则前x个选中不改变角度）
    self.limit_drag_over_angle = param.limit_drag_over_angle or 0     -- 限制列表超出角度滑动
    self.limit_drag_over_angle_min = param.limit_drag_over_angle_min or 0     -- 限制列表超出角度滑动

    -- 默认左右拖拽，false 上下拖拽
    self.is_drag_horizontal = param.is_drag_horizontal == nil and true or param.is_drag_horizontal

    -- 默认列表为顺时针
    self.is_clockwise_list = param.is_clockwise_list == nil and true or param.is_clockwise_list
    if not self.is_clockwise_list then
        self.origin_rotation = -self.origin_rotation
    end

    self.can_drag = param.can_drag == nil and true or param.can_drag
    self.item_render = param.item_render or ItemCell
    self.asset_bundle = param.asset_bundle
    self.asset_name = param.asset_name
    self.is_assist = param.is_assist or false   -- 是否处理遮挡关系
    self.behind = param.behind
    self.front = param.front
    self.click_item_cb = param.click_item_cb
    self.drag_to_last_cb = param.drag_to_last_cb
    self.drag_to_next_cb = param.drag_to_next_cb
    self.on_drag_end_cb = param.on_drag_end_cb
    self.set_item_pos_cb = param.set_item_pos_cb
    self.on_begin_drag_cb = param.on_begin_drag_cb


    self.drag_angle = 0                         -- 拖动的角度
    self.angle_rotate = 0                       -- 旋转的角度
    self.is_dragging = false                    -- 拖拽中
    self.is_moving = false                      -- 移动中
    self.trigger_drag_to_last = false           -- 拖拽到上一个
    self.trigger_drag_to_next = false           -- 拖拽到下一个
    self.drag_dir = self.is_clockwise_list and -1 or 1     -- 拖拽方向
    self.need_change_scale = param.need_change_scale        -- 需要改变item尺寸
    self.need_limit_drag_angle = self.limit_drag_over_angle > 0
    self.limit_drag_angle = 0


    self.render_count = 0
    self.list_render = {}
    self.list_render_assist = {} -- 辅助列表，用于处理遮挡关系

    -- 设置拖动回调
    if parent.event_trigger_listener then
        self.event_listener = parent.event_trigger_listener
        self.event_listener:AddBeginDragListener(BindTool.Bind(self.OnBeginDrag, self))
        self.event_listener:AddDragListener(BindTool.Bind(self.OnDrag, self, 1))
        self.event_listener:AddEndDragListener(BindTool.Bind(self.OnEndDrag, self))
    end

    -- 水平拖拽分上下两部分
    self.up_drag_root = param.up_drag_root
    self.down_drag_root = param.down_drag_root
    self.need_updown_click = param.need_updown_click
    -- 上
    self.up_event_listener = self.up_drag_root and self.up_drag_root.event_trigger_listener
    if self.up_event_listener then
        self.up_event_listener:AddBeginDragListener(BindTool.Bind(self.OnBeginDrag, self))
        self.up_event_listener:AddDragListener(BindTool.Bind(self.OnDrag, self, 1))
        self.up_event_listener:AddEndDragListener(BindTool.Bind(self.OnEndDrag, self))
        self.up_event_listener:AddPointerClickListener(BindTool.Bind(self.OnPointerClick, self))
    end

    -- 下
    self.down_event_listener = self.down_drag_root and self.down_drag_root.event_trigger_listener
    if self.down_event_listener then
        self.down_event_listener:AddBeginDragListener(BindTool.Bind(self.OnBeginDrag, self))
        self.down_event_listener:AddDragListener(BindTool.Bind(self.OnDrag, self, -1))
        self.down_event_listener:AddEndDragListener(BindTool.Bind(self.OnEndDrag, self))
        self.down_event_listener:AddPointerClickListener(BindTool.Bind(self.OnPointerClick, self))
    end
end

function CamberedList:__delete()
    if self.list_render then
        for index, render in pairs(self.list_render) do
            render:DeleteMe()
        end

        self.list_render = nil
        self.list_render_assist = nil
    end

    if self.quest_move then
        GlobalTimerQuest:CancelQuest(self.quest_move)
        self.quest_move = nil
    end

    if self.count_down_timer then
        GlobalTimerQuest:CancelQuest(self.count_down_timer) 
        self.count_down_timer = nil
    end

    self.is_assist = nil
    self.behind = nil
    self.front = nil
    self.rect_transform = nil
    self.parent_transform = nil
    self.event_listener = nil
    self.up_event_listener = nil
    self.down_event_listener = nil
    self.angle_delta = nil
    self.center_x = nil
    self.center_y = nil
    self.radius_x = nil
    self.radius_y = nil
    self.scale_min = nil
    self.scale_max = nil
    self.speed = nil
    self.is_circle = nil
    self.arg_adjust = nil
    self.origin_rotation = nil
    self.arg_speed = nil
    self.is_drag_horizontal = nil
    self.is_clockwise_list = nil
    self.item_render = nil
    self.asset_bundle = nil
    self.asset_name = nil
    self.click_item_cb = nil
    self.drag_to_last_cb = nil
    self.drag_to_next_cb = nil
    self.on_drag_end_cb = nil
    self.set_item_pos_cb = nil
    self.drag_angle = nil
    self.angle_rotate = nil
    self.is_dragging = nil
    self.is_moving = nil
    self.render_count = nil
    self.trigger_drag_to_last = nil
    self.trigger_drag_to_next = nil
end

-- 创建 render
function CamberedList:CreateCellList(render_count)
    self.render_count = render_count or 0
    local load_callback = function(obj)
        obj.transform.localPosition = self.parent_transform.localPosition
    end

    for i = 1, self.render_count do
        if self.list_render[i] == nil then
            local render = self.item_render.New()
            render:LoadAsset(self.asset_bundle, self.asset_name, self.parent_transform, load_callback)
            render:SetIndex(i)
            if self.click_item_cb ~= nil then
                render:SetClickCallback(BindTool.Bind(self.click_item_cb, render))
            end

            self.list_render[i] = render
            self.list_render_assist[i] = render
        end
    end

    for i = 1, #self.list_render do
        self.list_render[i]:SetActive(i <= self.render_count)
    end

    -- 平均 计算连个item之间的差角（圆）
    if self.is_circle and self.render_count > 0 then
        self.angle_delta = TWO_PI / self.render_count
    end

    if not self.is_circle and self.need_limit_drag_angle then
        local offest_num = self.viewport_count > 0 and self.viewport_count or 1
        self.limit_drag_angle = (self.render_count - offest_num) * self.angle_delta + self.limit_drag_over_angle
    end

    -- 设置每个item的位置和缩放
    self:SetPositionByDistance()
    return self.list_render
end

function CamberedList:GetRenderList()
    return self.list_render
end

function CamberedList:GetActRenderNum()
    return self.render_count or 0
end

--
function CamberedList:ScrollToIndex(index, callback, is_click)
    local offset_index = index - 1
    if self.viewport_count > 0 then
        offset_index = index - self.viewport_count
        offset_index = offset_index > 0 and offset_index or 0
    end

    local angle = self.angle_delta * offset_index
    if not self.is_clockwise_list then
        angle = TWO_PI - angle
    end

    --print_error("【-----ScrollToIndex----】：", self.angle_rotate)
    if not self.is_moving and is_click then
        self.target_angle = angle
        self:StartMove(angle - self.angle_rotate, callback)
    else
        self:StopMove()

        self.angle_rotate = angle
        self:SetPositionByDistance()

        if callback then
            callback()
        end
    end
end

-- 旋转
function CamberedList:StartMove(angle, callback)
    --print_error("【------StartMove---】：", angle)
    self.is_moving = true
    local abs_angle = math.abs(angle)

    if abs_angle > PI then  -- 超过半圈，则走相反方向，走最短路程
        if angle > 0 then
            angle = angle - TWO_PI
        else
            angle = angle + TWO_PI
        end
        abs_angle = math.abs(angle)
    end

    local multiplier = 5
    local delta_time = 0.02
    local speed = multiplier * self.speed * angle * 6 / PI
    local delta = speed * delta_time
    self.total_delta = 0

    self.quest_move = GlobalTimerQuest:InvokeRepeating(function()
            if not self.is_dragging then
                self.total_delta = self.total_delta + math.abs(delta)
                self:AddToRotateAngle(delta)
                self:SetPositionByDistance()

                if self.total_delta + 0.0001 >= abs_angle then
                    -- 由于移速和间隔时间的不同，最终停止位置可能有些许误差，在这里校正一下
                    self.angle_rotate = self.target_angle
                    self:SetPositionByDistance()
                    self:StopMove()
                    if callback then
                        callback()
                    end
                end
            end
        end, 0, delta_time, bit:_rshift(-1, 2)
    )
end

function CamberedList:StopMove()
    -- print_error("【------StopMove---】：")
    self.is_moving = false

    if self.quest_move then
        GlobalTimerQuest:CancelQuest(self.quest_move)
        self.quest_move = nil
    end
end

-- 将一个角度加到旋转角上, 控制旋转角在 [0, 2π] 之间， 防止角度过大越界
function CamberedList:AddToRotateAngle(delta)
    self.angle_rotate = self.angle_rotate + delta
    self.angle_rotate = self.angle_rotate - (math.floor(self.angle_rotate / 2 / PI) * TWO_PI)
    return self.angle_rotate
end

function CamberedList:SetPositionByDistance()
    -- 限制拖拽范围
    if self.limit_drag_angle > 0 and self.angle_rotate > self.limit_drag_angle then
        return
    end

    -- 限制拖拽范围
    if self.limit_drag_over_angle_min > 0 and self.angle_rotate < self.limit_drag_over_angle_min then
        return
    end

    if IsEmptyTable(self.list_render) then
    	return
    end

    -- 根据当前的旋转角设置每个item的大小和位置
    for index, render in ipairs(self.list_render) do
        if index <= self.render_count then
            self:ChangeRenderTransform(index, render)
        end
    end

    if self.set_item_pos_cb then
        self.set_item_pos_cb()
    end
end

function CamberedList:ChangeRenderTransform(index, render)
    local num = index - 1
    local real_angle = 0
    if self.is_clockwise_list then
        real_angle = TWO_PI + (self.angle_delta * num) - self.angle_rotate
    else
        real_angle = TWO_PI - (self.angle_delta * num) - self.angle_rotate
    end

    
    -- 将每个item的旋转角控制在[0，2π]之间
    -- if (real_angle > 4 * PI) then
    --     real_angle = real_angle - 4 * PI
    -- end

    -- if (real_angle > TWO_PI) then
    --     real_angle = real_angle - TWO_PI
    -- end

    real_angle = real_angle % TWO_PI

    -- 根据参数AdjustArg参数调整角度参数
    if (real_angle < PI) then
        real_angle = real_angle * (self.arg_adjust + (1 - self.arg_adjust) * real_angle / PI)
    else
        real_angle = TWO_PI - ((TWO_PI - real_angle) * (self.arg_adjust + (1 - self.arg_adjust) * (TWO_PI - real_angle) / PI))
    end

    local sinValue = math.sin(real_angle)
    local cosValue = math.cos(real_angle)
    -- 根据椭圆的旋转角设置位置
    local originalXPosition = sinValue * self.radius_x
    local originalYPosition = cosValue * self.radius_y
    local xPosition = math.cos(self.origin_rotation) * originalXPosition + math.sin(self.origin_rotation) * originalYPosition + self.center_x
    local yPosition = -math.sin(self.origin_rotation) * originalXPosition + math.cos(self.origin_rotation) * originalYPosition + self.center_y

    if render.view and render.view.transform then
        RectTransform.SetAnchoredPositionXY(render.view.transform, xPosition, yPosition)
    end

    if not self.need_change_scale then
        return
    end

    -- 根据椭圆的旋转角设置缩放    
    local scaleValue
    local alphaValue
    real_angle = (real_angle - self.origin_rotation) % TWO_PI
    if real_angle < PI then
        scaleValue = self.scale_min + (self.scale_max - self.scale_min) * (real_angle) / PI
        alphaValue = self.alpha_min + (self.alpha_max - self.alpha_min) * (real_angle) / PI
    else
        scaleValue = self.scale_min + (self.scale_max - self.scale_min) * (TWO_PI - (real_angle)) / PI
        alphaValue = self.alpha_min + (self.alpha_max - self.alpha_min) * (TWO_PI - (real_angle)) / PI
    end

    render.view.transform.localScale = Vector3.one * scaleValue

    if render.view and (not IsNil(render.view.canvas_group)) then
        render.view.canvas_group.alpha = alphaValue
    end

    if self.is_assist then
        --self:ChangeRenderParent(index, render)
         -- 根据缩放大小判断item之间的遮挡关系，重新进行层级的排序
        table.sort(self.list_render_assist, BindTool.Bind(self.AssistItemComparison, self))
        for i, list_render in ipairs(self.list_render_assist) do
            list_render.view.transform:SetAsLastSibling()
        end
    end
end

function CamberedList:ChangeRenderParent(index, render)
    local parent = self.behind
    if self.render_count - index < 3 then
        parent = self.front
    end

    render.view.gameObject.transform:SetParent(parent.gameObject.transform)
end

-- 层级比较函数，这里使用item的缩放（大小）进行比较，可以根据需要进行修改
function CamberedList:AssistItemComparison(render1, render2)
    local scale_x1 = render1.view.transform.localScale.x
    local scale_x2 = render2.view.transform.localScale.x
    local diff = scale_x1 - scale_x2
    if diff > 0 then
        return false
    end

    if diff < 0 then
        return true
    end

    if render1.index < render2.index then
        return false
    else
        return true
    end
end

function CamberedList:OnBeginDrag(event_data)
    --print_error("--OnBeginDrag--", event_data.delta)
    if not self.can_drag or self.is_moving then
        return
    end

    if self.on_begin_drag_cb then
        self.on_begin_drag_cb()
    end

    self:StopMove()
    self.last_speed = nil
    self.is_dragging = true
    self.drag_angle = 0
    self.trigger_drag_to_next = false
    self.trigger_drag_to_last = false
end

function CamberedList:OnEndDrag(event_data)
    --print_error("--OnEndDrag--", event_data.delta)
    if not self.can_drag or self.is_moving then
        return
    end

    if self.on_drag_end_cb then
        self.on_drag_end_cb()
    end

    self.is_dragging = false
end

function CamberedList:OnPointerClick(event_data)
    if not self.need_updown_click or self.is_dragging or self.click_item_cb == nil then
        return
    end
    -- print_error("--OnPointerClick--", event_data.position)

	--转换屏幕坐标为本地坐标
	local _, local_pos = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.rect_transform, 
                                                event_data.position, event_data.pressEventCamera, Vector2(0, 0))
    local click_render
    local render_pos = {x = 0, y = 0, z = 0}
    local render_scale = {x = 0, y = 0, z = 0}
    local render_size = {x = 0, y = 0}
    local range_x = {min = 0, max = 0}
    local range_y = {min = 0, max = 0}
    local width, height = 0, 0
    for k,v in ipairs(self.list_render) do
        render_pos = v.view.rect.localPosition
        render_scale = v.view.rect.localScale
        render_size = v.view.rect.sizeDelta
        width = render_size.x * render_scale.x * 0.5
        height = render_size.y * render_scale.y * 0.5
        range_x.min = render_pos.x - width
        range_x.max = render_pos.x + width
        range_y.min = render_pos.y - height
        range_y.max = render_pos.y + height

        if (range_x.min <= local_pos.x and local_pos.x <= range_x.max)
        and (range_y.min <= local_pos.y and local_pos.y <= range_y.max) then
            click_render = v
            break
        end
    end

    self.click_item_cb(click_render)
end

-- reverse 反向
function CamberedList:OnDrag(reverse, event_data)
    --print_error("--OnDrag--", reverse, event_data.delta)
    if not self.can_drag or self.is_moving then
        return
    end

    reverse = reverse or 1
    local speed = 0
    if self.is_drag_horizontal then
        speed = (-self.drag_dir * reverse) * event_data.delta.x * (self.speed * self.arg_speed)        -- 向左拖动，表示顺时针旋转
    else
        speed = (-self.drag_dir * reverse) * event_data.delta.y * (self.speed * self.arg_speed)          -- 向上拖动，表示顺时针旋转
    end

    -- 拖动过程中改变了方向
    if self.last_speed and self.last_speed * speed < 0 then
        self.drag_angle = speed
        self.trigger_drag_to_next = false
        self.trigger_drag_to_last = false
    end

    self.drag_angle = self.drag_angle + speed
    if self.is_clockwise_list then
        self:OnClockwiseDragSelect()
    else
        self:OnAnticlockwiseDragSelect()
    end

    self.last_speed = speed
    self:AddToRotateAngle(speed)
    self:SetPositionByDistance()
end

-- 逆时针拖拽选中
function CamberedList:OnAnticlockwiseDragSelect()
    if self.drag_angle > 0 then
        -- 拖动角度过半自动切换到上一个item
        if not self.trigger_drag_to_last and self.drag_angle >= self.angle_delta / 2 then
            self.trigger_drag_to_last = true
            if self.drag_to_last_cb then
                self.drag_to_last_cb()
            end
        end

        if self.drag_angle >= self.angle_delta then
            self.drag_angle = 0
            self.trigger_drag_to_last = false
        end
    else
        -- 拖动角度过半自动切换到下一个item
        if not self.trigger_drag_to_next and -self.drag_angle >= self.angle_delta / 2 then
            self.trigger_drag_to_next = true
            if self.drag_to_next_cb then
                self.drag_to_next_cb()
            end
        end

        if -self.drag_angle >= self.angle_delta then
            self.drag_angle = 0
            self.trigger_drag_to_next = false
        end
    end
end

-- 顺时针拖拽选中
function CamberedList:OnClockwiseDragSelect()
    if self.drag_angle > 0 then
        -- 拖动角度过半自动切换到下一个item
        if not self.trigger_drag_to_next and self.drag_angle >= self.angle_delta / 2 then
            self.trigger_drag_to_next = true
            if self.drag_to_next_cb then
                self.drag_to_next_cb()
            end
        end

        if self.drag_angle >= self.angle_delta then
            self.drag_angle = 0
            self.trigger_drag_to_next = false
        end
    else
        -- 拖动角度过半自动切换到上一个item
        if not self.trigger_drag_to_last and -self.drag_angle >= self.angle_delta / 2 then
            self.trigger_drag_to_last = true
            if self.drag_to_last_cb then
                self.drag_to_last_cb()
            end
        end

        if -self.drag_angle >= self.angle_delta then
            self.drag_angle = 0
            self.trigger_drag_to_last = false
        end
    end
end

--自动旋转
function CamberedList:AutoMove()
    if not self.is_assist then
        return
    end

    if self.count_down_timer then
        GlobalTimerQuest:CancelQuest(self.count_down_timer)
        self.count_down_timer = nil
    end

    local offset_index = 1
    if self.viewport_count > 0 then
        offset_index = offset_index > 0 and offset_index or 0
    end

    local angle = self.angle_delta * offset_index
    if not self.is_clockwise_list then
        angle = TWO_PI - angle
    end

    self.target_angle = angle
    local multiplier = 1
    local delta_time = 0.02
    local speed = multiplier * self.speed * angle * 6 / PI
    local delta = speed * delta_time
    self.count_down_timer = GlobalTimerQuest:InvokeRepeating(function()
        self:AddToRotateAngle(delta)
        self:SetPositionByDistance()
        for i, list_render in ipairs(self.list_render_assist) do
           self:ChangeRenderParent(i, list_render)
        end
    end, 0, 0.05, bit:_rshift(-1, 2))
end

function CamberedList:GetAssistRenderList()
    return self.list_render_assist
end