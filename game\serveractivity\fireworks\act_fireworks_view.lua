----------------------------------------------------
-- 烟花庆典(版本活动)
----------------------------------------------------
ActYanHuaQingDianView = ActYanHuaQingDianView or BaseClass(SafeBaseView)

function ActYanHuaQingDianView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/fireworks_prefab", "layout_fireworks")
	self.datachange_callback = BindTool.Bind(self.DataChange, self) --监听物品变化
end

function ActYanHuaQingDianView:__delete()
end

function ActYanHuaQingDianView:ReleaseCallBack()
	if nil ~= self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
	if nil ~= self.spe_cell then
		self.spe_cell:DeleteMe()
		self.spe_cell = nil
	end
	if self.item_cell then
		for k,v in pairs(self.item_cell) do
			if v then
				v:DeleteMe()
			end
		end
		self.item_cell = nil
	end

	if self.onclick_buy_ten then
		GlobalEventSystem:UnBind(self.onclick_buy_ten)
		self.onclick_buy_ten = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)
end

function ActYanHuaQingDianView:LoadCallBack()
	self.spe_cell = ItemCell.New(self.node_list.spe_cell)

	self.item_cell = {}
	for i=1,12 do
		self.item_cell[i] = ItemCell.New(self.node_list["cell" .. i])
	end

	self.node_list.img_remind_btn_one:SetActive(false)
	self.node_list.img_remind_btn_ten:SetActive(false)
	XUI.AddClickEventListener(self.node_list.btn_once, BindTool.Bind1(self.OnClickBuyOne, self))
	XUI.AddClickEventListener(self.node_list.btn_ten, BindTool.Bind1(self.OnClickBuyTen, self))
	XUI.AddClickEventListener(self.node_list.item_icon_1, BindTool.Bind1(self.OnClickItemIconOne, self))
	XUI.AddClickEventListener(self.node_list.item_icon_2, BindTool.Bind1(self.OnClickItemIconTen, self))
	self.onclick_buy_ten = GlobalEventSystem:Bind(YanhuaEventType.AGAIN, BindTool.Bind(self.OnClickBuyTen, self))

	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().other
	self.cost_item_id = cfg[1].yanhua_celebration_cost_item_id --材料Id
	self.yanhua_gift = cfg[1].yanhua_celebration_gift --烟花礼盒Id
	self.ten_buy_count = 10 --10次购买材料数量
	self.one_buy_count = 1 --1次购买材料数量

	ItemWGData.Instance:NotifyDataChangeCallBack(self.datachange_callback)
end

function ActYanHuaQingDianView:ShowIndexCallBack()
	self:StartCountDown()
	self:Flush()
end

function ActYanHuaQingDianView:OnClickItemIconOne()
	TipWGCtrl.Instance:OpenItem({item_id = self.cost_item_id})
end

function ActYanHuaQingDianView:OnClickItemIconTen()
	TipWGCtrl.Instance:OpenItem({item_id = self.cost_item_id})
end

function ActYanHuaQingDianView:DataChange(item_id)
	RemindManager.Instance:Fire(RemindName.Fireworks)
	if not self:IsOpen() then
		return
	end
	if item_id == self.cost_item_id or item_id == self.yanhua_gift then
		self:Flush()
	end
end

--点击购买一次
function ActYanHuaQingDianView:OnClickBuyOne()
	local bag_item_count = ItemWGData.Instance:GetItemNumInBagById(self.cost_item_id)
	if bag_item_count < self.one_buy_count then 
		local need_count = self.one_buy_count - bag_item_count
		local item_price = ShopWGData.GetItemGold(self.yanhua_gift)
		local consume_gold = need_count * item_price
		self:OpenConsumeTip(consume_gold,need_count, 0 )
	elseif bag_item_count >= self.one_buy_count then 
		local param_t = {
			rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_FIREWORKS,
			opera_type = RA_YANHUA_QINGDIAN_OPERA_TYPE.CHOU_ONE
		}
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	end
end

--点击购买十次
function ActYanHuaQingDianView:OnClickBuyTen()
	local bag_item_count = ItemWGData.Instance:GetItemNumInBagById(self.cost_item_id)
	if bag_item_count < self.ten_buy_count then 
		local need_count = self.ten_buy_count - bag_item_count
		local item_price = ShopWGData.GetItemGold(self.yanhua_gift)
		local consume_gold = need_count * item_price
		self:OpenConsumeTip(consume_gold,need_count,1)
	elseif bag_item_count >= self.ten_buy_count then 
		local param_t = {
			rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_FIREWORKS,
			opera_type = RA_YANHUA_QINGDIAN_OPERA_TYPE.CHOU_TEN
		}
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	end
end


--消耗Tip确认框
function ActYanHuaQingDianView:OpenConsumeTip(gold, item_count,type_s)
	if nil == self.alert_window then
		self.alert_window = Alert.New()
	end
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_FIREWORKS,
		opera_type = RA_YANHUA_QINGDIAN_OPERA_TYPE.CHOU_TEN
	}
	if type_s == 1 then 
		param_t.opera_type = RA_YANHUA_QINGDIAN_OPERA_TYPE.CHOU_TEN
	else
		param_t.opera_type = RA_YANHUA_QINGDIAN_OPERA_TYPE.CHOU_ONE
	end
	local item_name,gift_name
	local cfg1 = ItemWGData.Instance:GetItemConfig(self.cost_item_id)
	if cfg1 then
		item_name = cfg1.name
	else
		print_error("配置为空,item_id为",self.cost_item_id)
		return
	end
	local cfg2 = ItemWGData.Instance:GetItemConfig(self.yanhua_gift)
	if cfg2 then
		gift_name = cfg2.name
	else
		print_error("配置为空,item_id为",self.yanhua_gift)
		return
	end
	local des = string.format(Language.YanHuaQingDian.ConsumeTip, item_name, gold, item_count, gift_name)
	self.alert_window:SetLableString(des)
	self.alert_window:SetShowCheckBox(true)
	self.alert_window:SetOkFunc(function ()	
		ShopWGCtrl.Instance:SendShopBuy(self.yanhua_gift, item_count , 1, 0)
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	end)
	self.alert_window:Open()
end

function ActYanHuaQingDianView:OnFlush()
	if not self:IsOpen() then
		return
	end
	self:RefreshTopDesc()

	local reawrad_list , reward_item_id = ServerActivityWGData.Instance:GetActYanHuaRewardCfg()
	local bag_item_count = ItemWGData.Instance:GetItemNumInBagById(self.cost_item_id)
	self.spe_cell:SetData({item_id = reward_item_id})
	for i=1,12 do
		if reawrad_list[i - 1] then
			self.item_cell[i]:SetActive(true)
			self.item_cell[i]:SetData(reawrad_list[i - 1])
		else
			self.item_cell[i]:SetActive(false)
		end
	end
	local is_remind = ServerActivityWGData.Instance:GetActYanHuaRemind()
	self.node_list.img_remind_btn_one:SetActive(is_remind == 1)
	self.node_list.img_remind_btn_ten:SetActive(bag_item_count >= 10)
end

function ActYanHuaQingDianView:StartCountDown()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_FIREWORKS)
	local CDM = CountDownManager.Instance
	if CDM:HasCountDown("act_firework") then
		CDM:RemoveCountDown("act_firework")
	end
	CDM:AddCountDown("act_firework",BindTool.Bind(self.UpdateCountDownTime, self),
		BindTool.Bind(self.CompleteCountDownTime, self), act_info.next_time, nil, 0.3)	
end

function ActYanHuaQingDianView:RefreshTopDesc()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_FIREWORKS)
	local star_str = ""
	local end_str = ""
	if act_info then
		-- star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
		-- end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time - 1)
		end_str = TimeUtil.FormatSecondDHM(act_info.next_time - act_info.start_time)
	end
	self.node_list.txt_time.text.text = ToColorStr(end_str,COLOR3B.GREEN)
	-- self.node_list.txt_time.text.text = (star_str .. "----" .. end_str)
end

function ActYanHuaQingDianView:UpdateCountDownTime( elapse_time, total_time )
	local time = TimeUtil.FormatSecondDHM(total_time - elapse_time)
	self.node_list.txt_time.text.text = ToColorStr(time,COLOR3B.GREEN)
end

function ActYanHuaQingDianView:CompleteCountDownTime()
	self:RefreshTopDesc()
end

function ActYanHuaQingDianView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("act_firework") then
		CountDownManager.Instance:RemoveCountDown("act_firework")
	end
end