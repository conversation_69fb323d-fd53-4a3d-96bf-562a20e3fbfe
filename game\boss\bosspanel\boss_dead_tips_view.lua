--boss死亡提示面板
BossDeadTipsView = BossDeadTipsView or BaseClass(SafeBaseView)

function BossDeadTipsView:__init()
	self:SetMaskBg(false, false)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.view_layer = UiLayer.PopWhite
	self.view_name = "BossDeadTipsView"
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_dead_tips")
end

function BossDeadTipsView:LoadCallBack()
	self.node_list.root.animation_player:Play("Play", function()
		self:Close()
	end)
end
