EquipmentWGCtrl = EquipmentWGCtrl or BaseClass(BaseWGCtrl)
function EquipmentWGCtrl:InitImperialSpiritCtrl()
	self:RegisterImperialSpiritProtocel()

	self.role_data_event = BindTool.Bind1(self.RoleDataChangeCallback, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_event, {"bind_gold"})
	self.item_data_change_callback = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function EquipmentWGCtrl:DeleteImperialSpiritCtrl()
	if self.item_data_change_callback then
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
	end

	if self.role_data_event then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_event)
	end
end

function EquipmentWGCtrl:RegisterImperialSpiritProtocel()
	self:RegisterProtocol(CSEquipYuLingOperate)
	self:RegisterProtocol(SCEquipYuLingPartInfo, "OnSCEquipYuLingPartInfo")
	self:RegisterProtocol(SCEquipYuLingPartUpdate, "OnSCEquipYuLingPartUpdate")	
end

-- 请求装备操作
function EquipmentWGCtrl:SendEquipYuLingOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEquipYuLingOperate)
	protocol.operate_type = operate_type
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 装备信息
function EquipmentWGCtrl:OnSCEquipYuLingPartInfo(protocol)
	self.data:SetEquipYuLingPartInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Equipment_Imperial_Spirit_Set)
	RemindManager.Instance:Fire(RemindName.Equipment_Imperial_Spirit_Strength)

	self:FlushYuLingView()
end

-- 装备信息更新
function EquipmentWGCtrl:OnSCEquipYuLingPartUpdate(protocol)
	self.data:EquipYuLingPartUpdate(protocol)
	RemindManager.Instance:Fire(RemindName.Equipment_Imperial_Spirit_Set)
	RemindManager.Instance:Fire(RemindName.Equipment_Imperial_Spirit_Strength)

	self:FlushYuLingView()
end

function EquipmentWGCtrl:FlushYuLingView()
	local is_load_set = (self.view.show_index == TabIndex.equipment_imperial_spirit_set) and self.view:IsLoadedIndex(TabIndex.equipment_imperial_spirit_set)
	local is_load_strength = (self.view.show_index == TabIndex.equipment_imperial_spirit_strengtn) and self.view:IsLoadedIndex(TabIndex.equipment_imperial_spirit_strengtn)
	if self.view:IsOpen() and (is_load_set or is_load_strength) then
		self.view:Flush()
	end
end

function EquipmentWGCtrl:RoleDataChangeCallback(key, value)
	if key == "bind_gold" then
		self.data:CalculationEquipYuLingRemind()
		RemindManager.Instance:Fire(RemindName.Equipment_Imperial_Spirit_Set)
		RemindManager.Instance:Fire(RemindName.Equipment_Imperial_Spirit_Strength)
		self:FlushYuLingView()
	end
end

function EquipmentWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		local is_set_item = false
		local is_strength_item = false
		local is_zhushen_item = false
		local is_zhushentai_item = false

		if self.data:CheckIsImperialSpiritSetItem(change_item_id) then
			is_set_item = true
		end

		if self.data:CheckIsImperialSpiritStrengthItem(change_item_id) then	
			is_strength_item = true
		end

		if self.data:IsZhuShenChangeItem(change_item_id) then
			is_zhushen_item = true
		end

		if self.data:IsZhuShenTaiChangeItem(change_item_id) then
			is_zhushentai_item = true
		end

		if is_set_item or is_strength_item then
			self.data:CalculationEquipYuLingRemind()

			if is_set_item then
				RemindManager.Instance:Fire(RemindName.Equipment_Imperial_Spirit_Set)
			end

			if is_strength_item then
				RemindManager.Instance:Fire(RemindName.Equipment_Imperial_Spirit_Strength)
			end

			self:FlushYuLingView()
		end

		if is_zhushen_item or is_zhushentai_item then
			if is_zhushen_item then
				RemindManager.Instance:Fire(RemindName.Equipment_ZhuShen)
			end

			if is_zhushentai_item then
				RemindManager.Instance:Fire(RemindName.Equipment_ZhuShenTai)
			end

			self:FlushZhuShenView()
		end
	end
end