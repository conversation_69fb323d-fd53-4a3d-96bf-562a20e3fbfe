XianyuTrunTableGetRewardView = XianyuTrunTableGetRewardView or BaseClass(SafeBaseView)

local ani_ten_flag_t = {}
local ANI_SPEED = 0.15
local MAX_COUNT = 50
local MAX_LINE_COUNT = 5

function XianyuTrunTableGetRewardView:__init()
    self:SetMaskBg(true,true)
    self.view_layer = UiLayer.Pop
    self.view_name = "XianyuTrunTableGetRewardView"
    self:AddViewResource(0, "uis/view/xianyu_trun_table_prefab", "xianyu_trun_table_get_reward")
end

function XianyuTrunTableGetRewardView:__delete()
end

function XianyuTrunTableGetRewardView:ReleaseCallBack()
    if self.tween_time_quest then
        GlobalTimerQuest:CancelQuest(self.tween_time_quest)
        self.tween_time_quest = nil
    end
    if self.cell_list then
        for i,v in ipairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = {}
    end

    self.click_delay = nil

    if self.big_reward_sequence then
        self.big_reward_sequence:Kill()
        self.big_reward_sequence = nil
    end

end

function XianyuTrunTableGetRewardView:LoadCallBack(index, loaded_times)
    self.play_big_reward_anim_ing = false
    self.cell_list = {}
    self.play_big_reward_anim_list = {}
    XUI.AddClickEventListener(self.node_list.list_mask,BindTool.Bind(self.ImmPlayToBigReward,self))
    XUI.AddClickEventListener(self.node_list.btn_cancel,BindTool.Bind(self.Close,self))
    XUI.AddClickEventListener(self.node_list.fish_btn,BindTool.Bind(self.DrawAgain,self))
    self.node_list["btn_ignore"].button:AddClickListener(BindTool.Bind(self.OnClickIgnore, self))
end

function XianyuTrunTableGetRewardView:ShowIndexCallBack()
    self:Flush()
end

--初始化格子
local MAX_DELTA = 580
local SINGLE_DELTA = 105
local LINE_COUNT = 10
local ONE_LINE_DELTA = 200
function XianyuTrunTableGetRewardView:FlushCell(id_list)
    if not id_list then
        return
    end

    local length = math.ceil(#id_list / 10)
    for i=1,MAX_LINE_COUNT do
        self.node_list['list_' .. i]:SetActive(length >= i)
    end

    if length == 1 then
        self.node_list.bg.rect.sizeDelta = Vector2(0, ONE_LINE_DELTA)
    else
        self.node_list.bg.rect.sizeDelta = Vector2(0, MAX_DELTA - SINGLE_DELTA * (5 - length))
    end

    for i=1,MAX_COUNT do
        if id_list[i] then
            if not self.cell_list[i] then
                self.cell_list[i] = XianyuTrunTableRewardSingleCell.New(self.node_list['single_cell_' .. i])
                self.cell_list[i]:SetAnimIndex(i)
            end
            self.cell_list[i]:SetAlpha(false)
            self.cell_list[i]:SetVisible(true)
            -- 刷新格子
            self.cell_list[i]:SetData(id_list[i])
        else
            if self.cell_list[i] then
                self.cell_list[i]:SetAlpha(false)
                self.cell_list[i]:SetVisible(false)
            end
        end
    end


    for i = (length - 1) * LINE_COUNT + 1,length * LINE_COUNT do
        self.node_list['single_cell_' .. i]:SetActive(id_list[i] ~= nil)
    end
end

function XianyuTrunTableGetRewardView:CloseCallBack()
    if self.cell_list then --关闭的时候直接释放，因为跳过动画后显示会奇怪
        for i,v in ipairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = {}
    end
    -- ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, TabIndex.festival_activity_2270, "all", {[2] = "reset_ani"})
end

function XianyuTrunTableGetRewardView:SetData(id_list)
    self.id_list = id_list
end


--刷新数据
function XianyuTrunTableGetRewardView:OnFlush(param)
    for i, v in pairs(param) do
        if i == "all" then
            self:FlushView()
        end
    end
    self:FlushIgnore()
end

function XianyuTrunTableGetRewardView:FlushView()
    self.node_list.list_mask:SetActive(true)
    local gift_info = self.id_list

    if self.tween_time_quest then
        GlobalTimerQuest:CancelQuest(self.tween_time_quest)
        self.tween_time_quest = nil
    end

    self:FlushCell(gift_info)
    local no_tween = XianyuTrunTableWGData.Instance:GetJumpAni()

    if no_tween then
        self.tween_index = 1
        self:ImmPlayToBigReward()
    else
        self.tween_index = 1
        self.tween_time_quest = GlobalTimerQuest:AddTimesTimer(function()
            if self.tween_index > MAX_COUNT then
                GlobalTimerQuest:CancelQuest(self.tween_time_quest)
                self.tween_time_quest = nil
                return
            end
            if self.cell_list[self.tween_index - 1] and not self.cell_list[self.tween_index - 1]:GetCanPlayNext() then
                return
            end

            if self.cell_list[self.tween_index] then
                self.cell_list[self.tween_index]:DoAnim()
                self.cell_list[self.tween_index]:SetAlpha(true)
            end

            if self.cell_list[self.tween_index] and self.cell_list[self.tween_index]:GetIsBigReawrd() then
                local data = self.cell_list[self.tween_index]:GetData()
                self:PlayBigRewardAnim(data)
            end

            self.tween_index = self.tween_index + 1

            if self.tween_index >= #gift_info then
                self.node_list.list_mask:SetActive(false)
                return
            end

        end, ANI_SPEED, #gift_info*3)
    end
    -- 刷新按钮
    self:FlushBtn()
end

local big_reward_scale1 = Vector3(0, 0, 0) --大小1
local big_reward_scale2 = Vector3(1, 1, 1) --大小1
function XianyuTrunTableGetRewardView:PlayBigRewardAnim(data)
    
    if self.play_big_reward_anim_ing then
        table.insert(self.play_big_reward_anim_list,data)
    else
        self:RealPlayBigRewardAnim(data)
    end
end

function XianyuTrunTableGetRewardView:RealPlayBigRewardAnim(data)
    self.play_big_reward_anim_ing = true
    self.node_list["big_reward"]:SetActive(true)
    local cfg = data.cfg
    local get_gold = data.get_gold
    if cfg.show > 0 then
        local bunlle,asset = ResPath.GetXianyuTrunTableImg("a1_xyly_reward" .. cfg.show)
        self.node_list["get_reward_show"].image:LoadSprite(bunlle,asset,function ()
            self.node_list["get_reward_show"].image:SetNativeSize()
        end)
    end
    self.node_list["get_gold_text"].text.text = get_gold
    self.node_list["big_reward"].transform.localScale = big_reward_scale1
    local scale_tween_2 = self.node_list["big_reward"].rect:DOScale(big_reward_scale2,0.3)
    scale_tween_2:SetEase(DG.Tweening.Ease.InQuad)

    local scale_tween_1 = self.node_list["big_reward"].rect:DOScale(big_reward_scale1,0.3)
    scale_tween_1:SetEase(DG.Tweening.Ease.InQuad)

    local old_value = 0
    local text_obj = self.node_list["get_gold_text"].text
    local complete_fun = function()
        if text_obj then
            text_obj.text = get_gold
        end
    end
    local update_fun = function(num)
        if text_obj then
            text_obj.text = math.ceil(num)
        end
    end
    UITween.DONumberTo(text_obj, old_value, get_gold, 1.5, update_fun, complete_fun)

    if self.big_reward_sequence then
        self.big_reward_sequence:Kill()
        self.big_reward_sequence = nil
    end
    self.big_reward_sequence = DG.Tweening.DOTween.Sequence()
    self.big_reward_sequence:Append(scale_tween_2)
    self.big_reward_sequence:AppendInterval(2)
    self.big_reward_sequence:Append(scale_tween_1)
    self.big_reward_sequence:AppendCallback(function ()
        self.play_big_reward_anim_ing = false
        if self.node_list["big_reward"] then
            self.node_list["big_reward"]:SetActive(false)
        end
        if #self.play_big_reward_anim_list > 0 then
            local data = table.remove(self.play_big_reward_anim_list)
            self:RealPlayBigRewardAnim(data)
        end
    end)
end

-- 快速播放到下一个大奖
function XianyuTrunTableGetRewardView:ImmPlayToBigReward()
    for i = self.tween_index, #self.cell_list do
        if self.id_list[i] then
            -- 如果是最后一个物品，需要隐藏遮罩
            self.cell_list[i]:ImmShowItem()
            local is_big = self.id_list[i].cfg.show > 0
            if is_big then
                self:PlayBigRewardAnim(self.id_list[i])
            end
        end
        self.tween_index = #self.id_list
        self.node_list.list_mask:SetActive(false)
    end
end


function XianyuTrunTableGetRewardView:DrawAgain()
    if self.click_delay then
        return
    end
    self.click_delay = true
    GlobalTimerQuest:AddDelayTimer(function()
        self.click_delay = nil
    end, 0.3)
    local draw_type = XianyuTrunTableWGData.Instance:GetCurRollDrawType()
    XianyuTrunTableWGCtrl.Instance:OnSendRoll(draw_type)
    self:Close()
end

function XianyuTrunTableGetRewardView:FlushBtn()
    local draw_type = XianyuTrunTableWGData.Instance:GetCurRollDrawType()
    if not draw_type then
        return
    end

    local draw_type_cfg = XianyuTrunTableWGData.Instance:GetDrawModeCfg(draw_type)
    if not draw_type_cfg then
        return
    end

    self.node_list.fish_btn_root:SetActive(true)
    -- local main_role_vo = RoleWGData.Instance:GetRoleInfo()
    -- local has_gold = main_role_vo["gold"]

    local str = string.format(Language.XianyuTrunTable.BtnStr, draw_type_cfg.count)
    self.node_list["fish_btn_txt"].text.text = str
    -- local value,temp_value = CommonDataManager.ConverNumValue(has_gold)
    self.node_list["fish_btn_cost"].text.text = draw_type_cfg.cost_gold

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["fish_btn_cost"].rect)

end

function XianyuTrunTableGetRewardView:OnClickFishCostBtn(item_id)
    if not item_id then
        return
    end
    TipWGCtrl.Instance:OpenItem({item_id = item_id, num = 1, is_bind = 1},ItemTip.FROM_NORMAL)
end

function XianyuTrunTableGetRewardView:OnClickIgnore()
    local is_ignore = XianyuTrunTableWGData.Instance:GetJumpAni() == 1
    self.node_list["img_yes"]:SetActive(not is_ignore)
end

function XianyuTrunTableGetRewardView:FlushIgnore()
    local is_ignore = XianyuTrunTableWGData.Instance:GetJumpAni() == 1
    self.node_list["img_yes"]:SetActive(is_ignore)
end

----------------------------------------------------------------------------------------------
XianyuTrunTableRewardSingleCell = XianyuTrunTableRewardSingleCell or BaseClass(BaseRender)
function XianyuTrunTableRewardSingleCell:LoadCallBack()
    self.graphic_raycast = self.node_list.cell:GetComponent(typeof(UnityEngine.UI.GraphicRaycaster))
end

function XianyuTrunTableRewardSingleCell:ReleaseCallBack()
    if self.base_cell then
        self.base_cell:DeleteMe()
        self.base_cell = nil
    end
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    self.graphic_raycast = nil
end

function XianyuTrunTableRewardSingleCell:SetAlpha(value)
    if self.graphic_raycast then
        self.graphic_raycast.enabled = value
    end
    self.view.canvas_group.alpha = value and 1 or 0
end

local scale1 = Vector3(1.4, 1.4, 1.4) --大小1
local scale2 = Vector3(0.9, 0.9, 0.9) --大小1
local effect_name = {

}
local start_effect_name = {
    [5] = "ui_jinjichenggong",
    [6] = "ui_jinjichenggong",
}

function XianyuTrunTableRewardSingleCell:GetCanPlayNext()
    return self.can_play_next or false
end


function XianyuTrunTableRewardSingleCell:GetIsBigReawrd()
    local is_big = self.data.cfg.show > 0
    return is_big
end


XianyuTrunTableRewardSingleCell.Time = 1
function XianyuTrunTableRewardSingleCell:DoAnim()
    self.can_play_next = false

    local cfg = ItemWGData.Instance:GetItemConfig(self.data.cfg.item_id)
    if not cfg then
        return
    end
    local eff_level = cfg.color

    self.view.transform.localScale = scale1
    local scale_tween_2 = self.view.rect:DOScale(scale2,0.3)
    scale_tween_2:SetEase(DG.Tweening.Ease.InQuad)

    self.sequence = DG.Tweening.DOTween.Sequence()
        -- 不同类型的奖励，播放不同的特效
    -- if self.data.cfg.show > 0 then
    --     self.sequence:AppendInterval(XianyuTrunTableRewardSingleCell.Time)
    --     local bundle = string.format("effects2/prefab/ui/%s_prefab", string.lower('UI_jinli_saoguang'))
    --     local asset = 'UI_jinli_saoguang'
    --     self.node_list.effect_attach.rect:SetAsLastSibling()
    --     self.node_list.effect_attach:SetActive(true)
    --     --self.node_list.effect_attach:ChangeAsset(bundle,asset)
    -- end

    self.sequence:AppendCallback(function ()
        self.can_play_next = true
        self.node_list.effect_attach.rect:SetAsFirstSibling()
        self:PlayStartEffect()
    end)
    self.sequence:Append(scale_tween_2)
    self.sequence:AppendCallback(function ()
        ani_ten_flag_t[#ani_ten_flag_t + 1] = true
        -- if not effect_name[eff_level] then
        --     self.node_list.effect_attach:SetActive(false)
        --     return
        -- end

        -- local bundle = string.format("effects2/prefab/ui/%s_prefab", string.lower(effect_name[eff_level]))
        -- local asset = effect_name[eff_level]

        -- self.node_list.effect_attach:SetActive(true)
        -- self.node_list.effect_attach:ChangeAsset(bundle,asset)
    end)

end

function XianyuTrunTableRewardSingleCell:ImmShowItem()
    self.can_play_next = true
    self:SetAlpha(true)
    -- self:FlushEffect()
end

function XianyuTrunTableRewardSingleCell:PlayStartEffect()
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.cfg.item_id)
    local eff_level = cfg.color

    if not start_effect_name[eff_level] then
        self.node_list.effect_attach:SetActive(false)
        return
    end

    local bundle, asset = ResPath.GetEffectUi(start_effect_name[eff_level])

     self.node_list.effect_attach:SetActive(true)
     self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

function XianyuTrunTableRewardSingleCell:FlushEffect()
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.cfg.item_id)
    local eff_level = cfg.color

    -- if not effect_name[eff_level] then
    --     self.node_list.effect_attach:SetActive(false)
    --     return
    -- end

    -- local bundle = string.format("effects2/prefab/ui/%s_prefab", string.lower(effect_name[eff_level]))
    -- local asset = effect_name[eff_level]
    --  if self.node_list.effect_attach and self.node_list.effect_attach.game_obj_attach then
    --      self.node_list.effect_attach.game_obj_attach.BundleName = nil
    --     self.node_list.effect_attach.game_obj_attach.AssetName = nil
    --  end
    --  self.node_list.effect_attach:SetActive(true)
    --  self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

function XianyuTrunTableRewardSingleCell:OnFlush()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    if not self.data then
        return
    end
    self:FlushBaseCell()

end

function XianyuTrunTableRewardSingleCell:FlushBaseCell()
    if not self.base_cell then
        self.base_cell = ItemCell.New(self.node_list["cell"])
        self.base_cell:SetItemTipFrom(ItemTip.FROM_GET_REWARD)
    end
    local item_data = {}
    local get_gold = self.data.get_gold
    item_data.item_id = self.data.cfg.item_id
    item_data.num = get_gold > 1 and get_gold or 1
    self.base_cell:SetData(item_data)
    if get_gold > 0 then
        self.base_cell:SetItemIcon(ResPath.GetItem(65534))
    end
end

function XianyuTrunTableRewardSingleCell:SetAnimIndex(index)
    self.anim_index = index
end