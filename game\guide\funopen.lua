-- 审核服需要关闭的功能
local AUDIT_VERSION_CLOSE_FUNC = {
	[FunName.ShouChong] = true,
	[FunName.DailyRecharge] = true,
	[FunName.Welfare] = true,
	[FunName.QIFU] = true,
	[FunName.TreasureHunt] = true,
	[FunName.Rank] = true,
	[FunName.XiuZhenRoadView] = true,
	[FunName.ShenJiNotice] = true,
	[FunName.TianShenJuexing] = true,
	[FunName.RechargeVip] = true,
	[FunName.RechargeTQTZ] = true,
	[FunName.recharge_week_buy] = true,
	[FunName.VIPTouZi] = true,
	[FunName.RechargeMonthcard] = true,
	[FunName.SiXiangCallView] = true,
	[FunName.ShenJiTianCiView] = true,
	[FunName.MarryNotice] = true,
	[FunName.qifu_yunshi] = true,
	[FunName.MainUILongZhuSkill] = true,
	[FunName.LongZhuView] = true,
	[FunName.ShenJi] = true,
	[FunName.LifeIndulgenceView] = true,
}

-- 审核服默认永远开启的功能
local AUDIT_VERSION_OPEN_FUNC = {
	[FunName.RechargeUi] = true,
}

-- 内购相关功能
local RECHARGE_FUNC = {
	[FunName.RechargeUi] = true,
	[FunName.WingLinZhiSkillView] = true,
	[FunName.FaBaoLinZhiSkillView] = true,
	[FunName.ShenBingLinZhiSkillView] = true,
	[FunName.JianZhenLinZhiSkillView] = true,
	[FunName.YiYuanHaoLiView] = true,
	[GuideModuleName.XianQiShiLian] = true,
	[GuideModuleName.FightSoulExpPoolView] = true,
	[GuideModuleName.Vip] = true,
}

-- 有些功能要未开启的时候也要有红点，功能开启了红点要消失
local LISTEN_OPEN_REMIND_TAB = {
}


FunOpen = FunOpen or BaseClass()
function FunOpen:__init()
	if FunOpen.Instance ~= nil then
		ErrorLog("[FunOpen] attempt to create singleton twice!")
		return
	end
	FunOpen.Instance = self

	-- 功能开启
	self.fun_list = ConfigManager.Instance:GetAutoConfig("funopen_auto").funopen_list
	-- 功能开启 - 功能预告
	self.advance_notice_list = ConfigManager.Instance:GetAutoConfig("funopen_auto").advance_notice
	self.normal_scene_story_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("story_auto").normal_scene_story, "scene_id")
	self.funopen_seq_list = {}

	for k, v in pairs(self.fun_list) do
		self.funopen_seq_list[v.funopen_seq] = v
	end


	self.opened_list = {}				-- 已开启列表

	self.close_list = {}				-- 满足关闭条件的功能
	self.tab_funname_map = {}
	self.tab_index_funname_map = {}
	self.open_notify_callback_list = {}	-- 消息关注列表
	self.close_notify_callback_list = {}
	self.delay_cache_callback_list = {}

	self.funui_list = {}				-- 功能UI列表
    self.fp_funui_list = {}
	self.fp_funui_sp_list = {}
	self.open_param_list = {}			-- 功能开启打开界面参数缓存
	self.avdance_open_list = {}			-- 功能预告开启缓存
	self.enforce_close_list = {}			-- 后台强制关闭缓存 1正常 0强制关闭

	self.all_fun_checked = false
	self.all_fun_checked_shop = false 			-- 商城购买数据检测功能开启
	self.is_scene_effect_opening = false 		-- 场景动画是否在开启中

	self.init_check_all_ready_t = {
		["task"] = false,
		["level"] = false,
		["vip"] = false,
		["paladin_data"] = false,
	}

	self:InitTabFunNameMap()

	-- 功能红点列表  功能开启刷新红点
	self.remind_fun_t = {}
	for remind_name, tab_name in pairs(RemindFunName) do
		if nil == self.remind_fun_t[tab_name] then
			self.remind_fun_t[tab_name] = {}
			self.remind_fun_t[tab_name][remind_name] = remind_name
		else
			self.remind_fun_t[tab_name][remind_name] = remind_name
		end
	end

	self.task_data_change = BindTool.Bind(self.OnOneTaskDataChange, self)
	self.task_data_list_change = BindTool.Bind(self.OnTaskDataList, self)
	self.role_data_change = BindTool.Bind(self.OnRoleAttrValueChange, self)
	-- 减少逻辑量 屏蔽
	-- self.scene_change_complete_event = GlobalEventSystem:Bind(SceneEventType.SCENE_CHANGE_COMPLETE, BindTool.Bind(self.OnSceneChangeComplete, self))
	self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind(self.DayChange, self))
	self.world_level_change_event = GlobalEventSystem:Bind(OtherEventType.GS_COUNT_WORLD_LEVEL_CHANGE, BindTool.Bind(self.OnKFWorldLevelChange, self))
	self.real_recharge_event = GlobalEventSystem:Bind(ROLE_REAL_RECHARGE_NUM_CHANGE.REAL_RECHARGE, BindTool.Bind(self.RealRechargeChange, self))

	self.event_wait_init_flag_list = {
		[OtherEventType.PASS_DAY2] = true,
		[ROLE_REAL_RECHARGE_NUM_CHANGE.REAL_RECHARGE] = true,
	}

end


function FunOpen:__delete()
	if TaskWGData and TaskWGData.Instance then
		TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_data_change)
		TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_data_list_change)
	end

	if self.role_data_change and RoleWGData.Instance then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	-- GlobalEventSystem:UnBind(self.scene_change_complete_event)
	GlobalEventSystem:UnBind(self.pass_day_event)
	GlobalEventSystem:UnBind(self.world_level_change_event)

	if CountDownManager.Instance:HasCountDown("openfun_fly_dealy") then
		CountDownManager.Instance:RemoveCountDown("openfun_fly_dealy")
	end

	self.open_notify_callback_list = {}
	self.close_notify_callback_list = {}
	self.open_param_list = {}
	FunOpen.Instance = nil
end

-- 当全部ctrl加载回调
function FunOpen:OnAllWGCtrlInited()
	TaskWGData.Instance:NotifyDataChangeCallBack(self.task_data_change, false)
	TaskWGData.Instance:NotifyDataChangeCallBack(self.task_data_list_change, true)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"prof", "level", "vip_level"})
	MainuiWGCtrl.Instance:InitActivityAggregationBtn()
end

function FunOpen:SetSystemOpenLimitInfo(protocol)
	self.enforce_close_list = {}
	for i, v in ipairs(protocol.enforce_close_list) do
		self.enforce_close_list[v] = 0
	end
end

function FunOpen:SetSystemOpenLimitUpdate(protocol)

	self.enforce_close_list[protocol.system_type] = protocol.is_open

	local fun_cfg = self.funopen_seq_list[protocol.system_type] 
	if protocol.is_open == 0 then
		self:CloseFun(fun_cfg, true, nil, true)
		-- 设置按钮显示(副屏是加锁和遮罩)
		self:DoUIVisible(fun_cfg, false)
	else
		self:OpenFun(fun_cfg, true)
	end
end

-- 是否后台强制关闭
function FunOpen:IsEnforceClose(funopen_seq)
	return self.enforce_close_list[funopen_seq] == 0
end

-- 是否后台强制关闭
function FunOpen:IsEnforceCloseByFunName(fun_name)
	local fun_cfg = self:GetFunByName(fun_name)
	if fun_cfg == nil then
		return false
	end
	return self.enforce_close_list[fun_cfg.funopen_seq] == 0
end

function FunOpen:InitTabFunNameMap()
	self.tab_funname_map = {}
	self.module_tab_map = {}
	self.tab_index_funname_map = {}

	local open_param_t = nil
	for k,v in pairs(self.fun_list) do
		open_param_t = self:GetOpenParam(v.open_param)
		if open_param_t then
			local tab_index_or_view = open_param_t[1]
			local view_name = open_param_t[2]
			self.tab_funname_map[tab_index_or_view] = v.name

			if nil ~= view_name then
				local tab_list = self.module_tab_map[view_name]
				if nil == tab_list then
					tab_list = {}
					self.module_tab_map[view_name] = tab_list
				end
				table.insert(tab_list, tab_index_or_view)

				local index = TabIndex[tab_index_or_view] or 0
				if index > 0 then
					self.tab_index_funname_map[view_name] = self.tab_index_funname_map[view_name] or {}
					self.tab_index_funname_map[view_name][index] = v.name
				end
			end
		end
	end
end

-- 缓存open view param参数
function FunOpen:GetOpenParam(open_param)
	if open_param == nil or open_param == "" then
		return nil
	end

	if self.open_param_list[open_param] ~= nil then
		return self.open_param_list[open_param]
	else
		self.open_param_list[open_param] = Split(open_param,"#")
		return self.open_param_list[open_param]
	end
end

-- 获取功能开启配置
function FunOpen:GetFunCfgList()
	return self.fun_list
end

-- 获取功能开启配置 by name
function FunOpen:GetFunByName(fun_name)
	return self.fun_list[fun_name]
end

-- 获取功能预告配置
function FunOpen:GetAdvanceNoticeCfg(fun_name)
	return self.advance_notice_list[fun_name]
end

function FunOpen:GetNorSceneStoryCfg(scene_id)
	return self.normal_scene_story_cfg[scene_id]
end

-- 根据视图名字获得功能名
function FunOpen:GetFunNameByViewName(view_name)
	return ModuleNameToFunName[view_name]
end

--根据标签名找功能名
function FunOpen:GetFunNameByTabName(tab_name)
	return self.tab_funname_map[tab_name]
end

--根据任务类型获得功能名
function FunOpen:GetFunNameByTaskType(task_type)
	local fun_name = nil
	if task_type == GameEnum.TASK_TYPE_RI then
		fun_name = FunName.DailyTask
	elseif task_type == GameEnum.TASK_TYPE_HU then
		fun_name = FunName.HusongTask
	elseif task_type == GameEnum.TASK_TYPE_MENG then
		fun_name = FunName.GuildTask
	end

	return fun_name
end

--根据界面名 和 标签数值 找功能名
function FunOpen:GetFunNameByViewNameAndNumberIndex(view_name, number_index)
	return (self.tab_index_funname_map[view_name] or {})[number_index]
end

--观察功能开启时，回调带fun_name
function FunOpen:NotifyFunOpen(call_back)
	for k,v in pairs(self.open_notify_callback_list) do
		if v == call_back then
			return
		end
	end

	self.open_notify_callback_list[#self.open_notify_callback_list + 1] = call_back
end

function FunOpen:UnNotifyFunOpen(call_back)
	for k,v in pairs(self.open_notify_callback_list) do
		if v == call_back then
			self.open_notify_callback_list[k] = nil
			break
		end
	end
end

function FunOpen:NotifyFunClose(call_back)
	for k,v in pairs(self.close_notify_callback_list) do
		if v == call_back then
			return
		end
	end

	self.close_notify_callback_list[#self.close_notify_callback_list + 1] = call_back
end

function FunOpen:UnNotifyFunClose(call_back)
	for k,v in pairs(self.close_notify_callback_list) do
		if v == call_back then
			self.close_notify_callback_list[k] = nil
			break
		end
	end
end

-- 注册功能ui, 并即时检查可见性
-- 添加前注意是否有重复关联
function FunOpen:RegisterFunUi(name, ui)
	if name ~= "" and name ~= nil and ui ~= nil and self.fun_list[name] ~= nil then
		self.funui_list[name] = ui
		self:DoOpenFun(self:GetFunByName(name), true)
	end
end

function FunOpen:UnRegsiterFunUi(name)
	self.funui_list[name] = nil
end

--注册标签式功能ui，提供所在模块名称，方便批量注册
function FunOpen:RegsiterTabFunUi(module_name, ui)
	for k,v in pairs(self.fun_list) do
		if v.open_param ~= "" then
			local tab = self:GetOpenParam(v.open_param)
			if tab ~= nil and tab[2] == module_name and TabIndex[tab[1]] then
				self:RegisterFunUi(v.name, ui)
			end
		end
	end
end

--注销标签式功能
function FunOpen:UnRegsiterTabFunUi(module_name)
	for k,v in pairs(self.fun_list) do
		if v.open_param ~= "" then
			local tab = self:GetOpenParam(v.open_param)
			if tab ~= nil and tab[2] == module_name and TabIndex[tab[1]] then
				self.funui_list[v.name] = nil
			end
		end
	end
end

--主界面按钮组功能开启
function FunOpen:RegisterFunOpenUi(name_list, ui_list)
	for k,v in pairs(name_list) do
		local module_name = v[1]
		local fun_name = ModuleNameToFunName[module_name]
		if v[3] then
			fun_name = v[3]
		end
	
		if module_name and fun_name then
			self.funui_list[fun_name] = ui_list[k]
			self:DoOpenFun(self:GetFunByName(fun_name), true)
		end
	end
end

-- 主界面副屏按钮功能开启
function FunOpen:RegisterMianUIFPFunOpen(name_list, ui_list)
	for k,v in pairs(name_list) do
		local module_name = v[1]
		local fun_name = ModuleNameToFunName[module_name]
		if module_name and fun_name then
			if v.fp_special_fun_open then
				self.fp_funui_sp_list[fun_name] = ui_list[k]
			else
				self.fp_funui_list[fun_name] = ui_list[k]
			end
			
			self:DoOpenFun(self:GetFunByName(fun_name), true)
		end
	end
end



--------------------
--- 事件监听 ----
--------------------
-- 全部任务下发 初始化检查功能开始
function FunOpen:OnTaskDataList(reason)
	if reason == TASK_LIST_CHANGE_REASON_COMPLETED then
		self.init_check_all_ready_t["task"] = true
		self.init_check_all_ready_t["level"] = true
		self:CheckAllFunOpen()
	end
end

-- 任务改变触发功能开启
function FunOpen:OnOneTaskDataChange(task_id, reason)
	-- 移除
	if reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		for k,v in pairs(self.fun_list) do
			-- 触发条件 提交任务后
            if v.trigger_type == GuideTriggerType.CommitTask then
                if v.trigger_param == task_id then
            		-- 天神觉醒
	                if k == FunName.TianShenJuexing then
	                    if TianShenJuexingWGData.Instance and TianShenJuexingWGData.Instance:GetMainTianShenAwakenBtnState() then
	                        self:CloseFun(v)
	                    else
	                        self:OpenFun(v)
	                    end
	                else
	                    self:OpenFun(v)
	                end
                end
            -- 退出副本后 兼容gm跳过任务，不进副本也可触发的情况
			elseif v.trigger_type == GuideTriggerType.OutFb then
				if v.with_param == task_id then
                    self:OpenFun(v)
				end
			end

			-- 关闭条件
			if v.close_trigger_type == GuideTriggerType.CommitTask then
				if v.close_trigger_param == task_id then
					self:CloseFun(v)
				end
			end
		end
	end

	-- 添加
	if reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
		for k,v in pairs(self.fun_list) do
			-- 触发条件 接受任务后
			if v.trigger_type == GuideTriggerType.AcceptTask then
				if v.trigger_param == task_id then
                    self:OpenFun(v)
				end
			end

			-- 关闭条件 接受任务后
			if v.close_trigger_type == GuideTriggerType.AcceptTask then
				if v.close_trigger_param == task_id then
					self:CloseFun(v)
				end
			end
		end
	end

	-- 更新
	if reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then
		local task_cfg = TaskWGData.Instance:GetTaskInfo(task_id)
		if task_cfg ~= nil and task_cfg.is_complete ~= 0 then
			for k,v in pairs(self.fun_list) do
				-- 触发条件 达到可提交任务时
				if v.trigger_type == GuideTriggerType.CanCommintTask then
					if v.trigger_param == task_id then
	                    self:OpenFun(v)
					end
				end

				-- 关闭条件 达到可提交任务时
				if v.close_trigger_type == GuideTriggerType.CanCommintTask then
					if v.close_trigger_param == task_id then
						self:CloseFun(v)
					end
				end
			end
		end
	end
end

-- 角色属性改变
function FunOpen:OnRoleAttrValueChange(key, value, old_value)
	local is_init = value == old_value
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local vip_level = RoleWGData.Instance.role_vo.vip_level
	local real_recharge_val = RechargeWGData.Instance:GetRealChongZhiRmb()

	local trigger_type, trigger_param, trigger_param2, trigger_param3
	local is_open = false

	if key == "level" then
		for k,v in pairs(self.fun_list) do
			is_open = false
			local module_name = v.name
			trigger_type =v.trigger_type
			trigger_param = v.trigger_param
			trigger_param2 = v.trigger_param2
			trigger_param3 = v.trigger_param3

			-- 触发条件 升级后
			if trigger_type == GuideTriggerType.LevelUp then
                if trigger_param ~= "" and value >= trigger_param then
                	-- -- 角色神兵特殊处理
                    -- if module_name == FunName.NewAppearanceUpgradeLingChong then
					-- 	if TianShenJuexingWGData.Instance:GetShenWuFuncIsOpen() then
					-- 		is_open = true
                    --     end
                    -- else
                    	is_open = true
                   	-- end
                else
					if self.advance_notice_list[module_name] ~= nil and not self.avdance_open_list[module_name] then
						is_open = true
					end
				end

			elseif trigger_type == GuideTriggerType.OpenSeverDay then
				if trigger_param ~= "" and server_day >= trigger_param then
					if trigger_param2 ~= "" then
						if role_level >= trigger_param2 then
							is_open = true
						end
					else
						is_open = true
					end
				end

			elseif trigger_type == GuideTriggerType.KFAndRoleLevelUp then
				local kf_world_level = BiZuoWGData.Instance and CrossServerWGData.Instance:GetServerMeanWorldLevel()
				if trigger_param ~= "" and value >= trigger_param and v.cross_gs_level ~= "" and kf_world_level >= v.cross_gs_level  then
				    is_open = true
				else
					if self.advance_notice_list[module_name] ~= nil and not self.avdance_open_list[module_name] then
						is_open = true
					end
				end

			elseif trigger_type == GuideTriggerType.DayAndSpecialCondition and trigger_param == -1 then
				is_open = SystemForceWGData.Instance:GetCheckFunOpen()
			elseif trigger_type == GuideTriggerType.LevelAndViplevel and trigger_param ~= "" and trigger_param2 ~= "" then
				if vip_level >= trigger_param and role_level >= trigger_param2 then
					self:OpenFun(v, false, is_init)
				end
			elseif trigger_type == GuideTriggerType.RechargeVal_Day_Level then
				if trigger_param3 ~= "" and trigger_param ~= "" then
					if real_recharge_val >= trigger_param and role_level >= trigger_param3 then
						is_open = true
					end
					
					if is_open and trigger_param2 ~= "" and server_day < trigger_param2 then
						is_open = false
					end
				end
            end

            -- 关闭条件
			if v.close_trigger_type == GuideTriggerType.LevelUp then
				if v.close_trigger_param ~= "" and value >= v.close_trigger_param then
					self:CloseFun(v, false)
					is_open = false
				end
			elseif v.close_trigger_type == GuideTriggerType.OpenSeverDay then
				if v.close_trigger_param ~= "" and server_day >= v.close_trigger_param then
					self:CloseFun(v, false)
					is_open = false
				end
			end

			if is_open then
				self:OpenFun(v, false, is_init)
			end
		end
	elseif key == "prof" then
		local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
		for k,v in pairs(self.fun_list) do
			trigger_type = v.trigger_type
			trigger_param = v.trigger_param
			if trigger_type == GuideTriggerType.ZhuanZhi then
				if trigger_param ~= "" and role_zhuan >= trigger_param then
					self:OpenFun(v, false, is_init)
				end
			end
		end
	elseif key == "vip_level" then
		for k,v in pairs(self.fun_list) do
			trigger_type = v.trigger_type
			trigger_param = v.trigger_param
			trigger_param2 = v.trigger_param2
			if trigger_type == GuideTriggerType.LevelAndViplevel and trigger_param ~= "" and trigger_param2 ~= "" then
				if vip_level >= trigger_param and role_level >= trigger_param2 then
					self:OpenFun(v, false, is_init)
				end
			end
		end
	end
end

--在收到vip数据时检查是否能开启
--vip是跟主角信息不是同一条协议
function FunOpen:CheckVipConditionOnAcceptData()
	local is_init = self.init_check_all_ready_t["vip"] == false
	self.init_check_all_ready_t["vip"] = true
	local flag = false
	local vip_lv = RoleWGData.Instance.role_vo.vip_level
	for k,v in pairs(self.fun_list) do
		--vip达到认为触发
		if v.vip_lev ~= "" and self.opened_list[v.name] ~= 1 and vip_lv >= v.vip_lev then
			self.opened_list[v.name] = 1
			GlobalEventSystem:Fire(OpenFunEventType.OPEN_TRIGGER, false, v.name, true)
			self:DoOpenFun(v, is_init)
			flag = true
		end
	end

	if flag then
		MainuiWGCtrl.Instance:UpdateFunuiPos(not is_init)
	end
end

-- 场景改变完成
function FunOpen:OnSceneChangeComplete(old_scene_type, new_scene_type)
	if old_scene_type == SceneType.Common or new_scene_type ~= SceneType.Common then
		return
	end

	local out_fb = GuideTriggerType.OutFb
	for k,v in pairs(self.fun_list) do
		if v.trigger_type == out_fb and v.trigger_param == old_scene_type then
			if v.with_param ~= "" then
				if TaskWGData.Instance:GetTaskIsAccepted(v.with_param) then
					self:OpenFun(v)
				end
			else
				self:OpenFun(v)
			end
		end
	end
end

-- 天数改变
function FunOpen:DayChange()
	local is_init = self.event_wait_init_flag_list[OtherEventType.PASS_DAY2]
	if is_init then
		self.event_wait_init_flag_list[OtherEventType.PASS_DAY2] = false
	end

	local is_open = false
	local trigger_type, trigger_param, trigger_param2, trigger_param3, close_trigger_type, close_trigger_param

	local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local real_recharge_val = RechargeWGData.Instance:GetRealChongZhiRmb()

	for k,v in pairs(self.fun_list) do

		-- 是否后台强制关闭中
		if self.enforce_close_list[v.funopen_seq] == 0 then
			self:CloseFun(v)
		else
			trigger_type = v.trigger_type
			trigger_param = v.trigger_param
			trigger_param2 = v.trigger_param2
			trigger_param3 = v.trigger_param3
			close_trigger_type = v.close_trigger_type
			close_trigger_param = v.close_trigger_param
			is_open = false
			if trigger_type == GuideTriggerType.OpenSeverDay then
				if trigger_param ~= "" and cur_open_day >= trigger_param then
					if trigger_param2 ~= "" then
						if role_level >= trigger_param2 then
							is_open = true
						end
					else
						is_open = true
					end
				end
	
			elseif trigger_type == GuideTriggerType.DayAndSpecialCondition and trigger_param == -1 then
				-- is_open = SystemForceWGData.Instance:GetCheckOpenBySeq(trigger_param)
				is_open = SystemForceWGData.Instance:GetCheckFunOpen()
	
			elseif trigger_type == GuideTriggerType.RechargeVal_Day_Level then
				if trigger_param2 ~= "" and trigger_param ~= "" then
					if real_recharge_val >= trigger_param and cur_open_day >= trigger_param2 then
						is_open = true
					end
	
					if is_open and trigger_param3 ~= "" and role_level < trigger_param3 then
						is_open = false
					end
				end
			end
	
			if nil ~= close_trigger_type and "" ~= close_trigger_type and close_trigger_type == GuideTriggerType.OpenSeverDay then
				if close_trigger_param ~= "" and cur_open_day >= close_trigger_param then
					self:CloseFun(v)
					is_open = false
				end
			end
	
			if is_open then
				self:OpenFun(v, false, is_init)
			end
	
			-- --角色神兵特殊处理
			-- if v.name == FunName.NewAppearanceUpgradeLingChong then
			--     if TianShenJuexingWGData.Instance:GetShenWuFuncIsOpen() then
			--         self:OpenFun(v, true, true)
			--     end
			-- end
		end
		
	end
end

--跨服世界等级变化判断加功能开启
function FunOpen:OnKFWorldLevelChange()
	local kf_world_level = BiZuoWGData.Instance and CrossServerWGData.Instance:GetServerMeanWorldLevel()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local trigger_param
	for k,v in pairs(self.fun_list) do
		-- 是否后台强制关闭中
		if self.enforce_close_list[v.funopen_seq] == 0 then
			self:CloseFun(v)
		elseif v.trigger_type == GuideTriggerType.KFAndRoleLevelUp then
			trigger_param = v.trigger_param
			if trigger_param ~= "" and role_level >= trigger_param and v.cross_gs_level ~= "" and kf_world_level >= v.cross_gs_level  then
        	    self:OpenFun(v, false)
			end
		end
	end
end

-- 
function FunOpen:RealRechargeChange()
	local real_recharge_val = RechargeWGData.Instance:GetRealChongZhiRmb()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	local is_init = self.event_wait_init_flag_list[ROLE_REAL_RECHARGE_NUM_CHANGE.REAL_RECHARGE]
	if is_init then
		self.event_wait_init_flag_list[ROLE_REAL_RECHARGE_NUM_CHANGE.REAL_RECHARGE] = false
	end

	local is_open = false
	local trigger_param, trigger_param2, trigger_param3
	for k,v in pairs(self.fun_list) do
		-- 是否后台强制关闭中
		if self.enforce_close_list[v.funopen_seq] == 0 then
			self:CloseFun(v)
		else
			is_open = false
			if v.trigger_type == GuideTriggerType.RechargeVal_Day_Level then
				trigger_param = v.trigger_param
				trigger_param2 = v.trigger_param2
				trigger_param3 = v.trigger_param3
	
				if trigger_param ~= "" and real_recharge_val >= trigger_param then
					is_open = true
					if trigger_param2 ~= "" and cur_open_day < trigger_param2 then
						is_open = false
					end
	
					if is_open and trigger_param3 ~= "" and role_level < trigger_param3 then
						is_open = false
					end
				end
			end
	
			if is_open then
				self:OpenFun(v, false, is_init)
			end
		end
	end
end

--系统预告任务信息变化
function FunOpen:OnSystemForceChange(seq)
	local need_open, func_is_open
	for k,v in pairs(self.fun_list) do
		if v.trigger_type == GuideTriggerType.DayAndSpecialCondition and (v.trigger_param == seq or v.trigger_param == -1) then
			func_is_open = self:GetFunIsOpened(v.name)
			-- need_open = SystemForceWGData.Instance:GetCheckOpenBySeq(v.trigger_param)
			need_open = SystemForceWGData.Instance:GetCheckFunOpen(v.trigger_param)

			if not need_open and func_is_open then
				self:CloseFun(v)
			elseif need_open and not func_is_open then
				self:OpenFun(v)
			end
		end
	end
end

--------------------
--- 功能开启记录 ----
--------------------
-- 初始化检查功能开启
function FunOpen:CheckAllFunOpen()
	if self.all_fun_checked then
		return
	end

	self.all_fun_checked = true
	local is_open = false
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local vip_level = RoleWGData.Instance.role_vo.vip_level
	local kf_world_level = BiZuoWGData.Instance and CrossServerWGData.Instance:GetServerMeanWorldLevel()
	local real_recharge_val = RechargeWGData.Instance:GetRealChongZhiRmb()

	local trigger_type, trigger_param, trigger_param2, trigger_param3, with_param, close_trigger_type, close_trigger_param
	for k, v in pairs(self.fun_list) do
		is_open = false
		trigger_type = v.trigger_type
		trigger_param = v.trigger_param
		trigger_param2 = v.trigger_param2
		trigger_param3 = v.trigger_param3
		with_param = v.with_param

		if trigger_type == GuideTriggerType.LevelUp then
			if trigger_param ~= "" and role_level >= trigger_param then
				is_open = true
			end
			
		elseif trigger_type == GuideTriggerType.CommitTask then
			if TaskWGData.Instance:GetTaskIsCompleted(trigger_param) then
				is_open = true
			end

		elseif trigger_type == GuideTriggerType.AcceptTask then
			if (TaskWGData.Instance:GetTaskIsAccepted(trigger_param) or TaskWGData.Instance:GetTaskIsCompleted(trigger_param)) then
				is_open = true
			end
		elseif trigger_type == GuideTriggerType.OutFb then
			if (TaskWGData.Instance:GetTaskIsAccepted(with_param) or TaskWGData.Instance:GetTaskIsCompleted(with_param)) then
				is_open = true
			end
		elseif trigger_type == GuideTriggerType.OpenSeverDay then
			if trigger_param ~= "" and server_day >= trigger_param then
				if trigger_param2 ~= "" then
					if role_level >= trigger_param2 then
						is_open = true
					end
				else
					is_open = true
				end
			end

		elseif trigger_type == GuideTriggerType.CanCommintTask then
			if (TaskWGData.Instance:GetTaskIsCanCommint(trigger_param) or TaskWGData.Instance:GetTaskIsCompleted(trigger_param)) then
				is_open = true
			end
		elseif trigger_type == GuideTriggerType.ZhuanZhi then
			is_open = trigger_param ~= "" and role_zhuan >= trigger_param

		elseif trigger_type == GuideTriggerType.KFAndRoleLevelUp then
			if trigger_param ~= "" and v.cross_gs_level ~= "" then
				is_open = kf_world_level >= v.cross_gs_level and role_level >= trigger_param
			end

		elseif trigger_type == GuideTriggerType.ChildFunOpen then
			local child_list = Split(v.child_fun, "|")
			for k1,v1 in pairs(child_list) do
				local child_open = self:GetFunIsOpened(v1)
				if child_open then
					is_open = true
				end
			end
		elseif trigger_type == GuideTriggerType.DayAndSpecialCondition and trigger_param == -1 then
			-- is_open = SystemForceWGData.Instance:GetCheckOpenBySeq(trigger_param)
			is_open = SystemForceWGData.Instance:GetCheckFunOpen()
		elseif trigger_type == GuideTriggerType.LevelAndViplevel and trigger_param ~= "" and trigger_param2 ~= "" then  
   			if vip_level >= trigger_param and role_level >= trigger_param2 then
				is_open = true
			end

		elseif trigger_type == GuideTriggerType.RechargeVal_Day_Level then
			if trigger_param ~= "" and real_recharge_val >= trigger_param then
				is_open = true
				if trigger_param2 ~= "" and server_day < trigger_param2 then
					is_open = false
				end
		
				if is_open and trigger_param3 ~= "" and role_level < trigger_param3 then
					is_open = false
				end
			end
		end

		--再判断是否关闭
		close_trigger_type = v.close_trigger_type
		close_trigger_param = v.close_trigger_param
		if is_open then
			if close_trigger_type == GuideTriggerType.LevelUp then
				if nil ~= close_trigger_param and close_trigger_param ~= "" and role_level >= close_trigger_param then
					is_open = false
				end

			elseif close_trigger_type == GuideTriggerType.OpenSeverDay then
				if nil ~= close_trigger_param and close_trigger_param ~= "" and server_day >= close_trigger_param then
					is_open = false
				end

			elseif close_trigger_type == GuideTriggerType.CommitTask then
				if TaskWGData.Instance:GetTaskIsCompleted(close_trigger_param) then
					is_open = false
				end

			elseif close_trigger_type == GuideTriggerType.AcceptTask then
				if (TaskWGData.Instance:GetTaskIsAccepted(close_trigger_param) or TaskWGData.Instance:GetTaskIsCompleted(close_trigger_param)) then
					is_open = false
				end
			elseif close_trigger_type == GuideTriggerType.CanCommintTask then
				if (TaskWGData.Instance:GetTaskIsCanCommint(close_trigger_param) or TaskWGData.Instance:GetTaskIsCompleted(close_trigger_param)) then
					is_open = false
				end
			end
		end

		if is_open then
			self.opened_list[v.name] = 1		--标记功能已开启
			self:DoOpenFun(v, true)
		end
	end

	GlobalEventSystem:Fire(OpenFunEventType.OPEN_TRIGGER, true)
end

-- 是否初始化检查功能开启
function FunOpen:GetAllFunChecked()
	return self.all_fun_checked
end

-- 功能开启操作
function FunOpen:DoOpenFun(fun_cfg, is_init)
	if fun_cfg == nil then
		return
	end

	local fun_name = fun_cfg.name
 	-- 功能开启刷新功能绑定红点
	local is_opened, reason_string = self:GetFunIsOpened(fun_name, true)
	if is_opened and self.remind_fun_t[fun_name] then 
		for k,v in pairs(self.remind_fun_t[fun_name]) do
			RemindManager.Instance:Fire(v)
		end
	end

    -- 有些功能要未开启的时候也要有红点，功能开启了红点要消失
	if is_opened and LISTEN_OPEN_REMIND_TAB[fun_name] ~= nil then
		RemindManager.Instance:Fire(LISTEN_OPEN_REMIND_TAB[fun_name])
	end

	if is_opened then
		--通知观察者
		for k,v in pairs(self.open_notify_callback_list) do
			v(fun_name)
		end

		self:FunOpenNotifyEvent(fun_cfg)
	end

    self:DoUIVisible(fun_cfg, is_opened, reason_string ,is_init)
    if not is_init then
        self:DoUIFlyTween(fun_cfg, is_opened, reason_string)
    end

    --生长（如用于场景上对象的出现）
    --[[
	if fun_cfg.open_type == FunOpenType.Born then
		self:ShowSceneEffectOnOpen(fun_cfg, is_init)
	end
    ]]

	local ad_cfg = self:GetAdvanceNoticeCfg(fun_name)
	if not is_opened and ad_cfg and not self.avdance_open_list[fun_name] then
		if self:CheckFunIsNeedAdvanceShow(fun_name) then
			self.avdance_open_list[fun_name] = true
			self:DoUIVisible(fun_cfg, is_opened, reason_string)
			if not is_init then
				self:DoUIFlyTween(fun_cfg, is_opened, reason_string, true)
			end
		end
	end
end

local fp_ui_act_color = Color.New(1, 1, 1, 1)
local fp_ui_lock_color = Color.New(1, 1, 1, 0.5)
function FunOpen:DoUIVisible(fun_cfg, is_opened, reason_string, is_init)
	if fun_cfg == nil then
		return
	end

	local open_type = fun_cfg.open_type
	local fun_name = fun_cfg.name
	local normal_ui = self.funui_list[fun_name]
	local fp_ui = self.fp_funui_list[fun_name]
	local fp_sp_ui = self.fp_funui_sp_list[fun_name]

	local tab_index = 1
	if open_type == FunOpenType.TabOpen or open_type == FunOpenType.FlyTabOpen then
		local open_param_t = self:GetOpenParam(fun_cfg.open_param)
		if open_param_t and TabIndex[open_param_t[1]] then
			tab_index = math.floor(TabIndex[open_param_t[1]])
		end
	end

	if normal_ui ~= nil then
		if normal_ui.SetToggleVisible then
			normal_ui:SetToggleVisible(tab_index, is_opened, reason_string, not is_init)
		elseif normal_ui.SetActive then
			normal_ui:SetActive(is_opened)
		end
	end

	-- 相同部分
	local zjm_fp_btn_hide = fun_cfg.zjm_fp_btn_hide == 1
	local fp_ui_obj = fp_ui or fp_sp_ui
	if fp_ui_obj ~= nil then
		if fp_ui_obj.lock and fp_ui_obj.lock.gameObject ~= nil then
			if zjm_fp_btn_hide then
				fp_ui_obj.lock.gameObject:SetActive(false)
			else
				fp_ui_obj.lock.gameObject:SetActive(not is_opened)
			end
		end
	end

	if fp_sp_ui ~= nil then
		if fp_sp_ui.Icon then
			if zjm_fp_btn_hide then
				fp_sp_ui:CustomSetActive(is_opened)
			end
		end
	end

	if fp_ui ~= nil then
		if fp_ui.Icon then
			if zjm_fp_btn_hide then
				fp_ui:CustomSetActive(is_opened)
			else
				fp_ui.Icon.image.color = is_opened and fp_ui_act_color or fp_ui_lock_color
			end
		end

		if fp_ui.fun_name and fp_ui.lock_name and fp_ui.fun_name.gameObject ~= nil and fp_ui.lock_name.gameObject ~= nil then
			if zjm_fp_btn_hide then
				fp_ui.fun_name.gameObject:SetActive(is_opened)
				fp_ui.lock_name.gameObject:SetActive(false)
			else
				fp_ui.fun_name.gameObject:SetActive(is_opened)
				fp_ui.lock_name.gameObject:SetActive(not is_opened)
			end
		end

		if fp_ui.bg and fp_ui.bg.gameObject ~= nil and fp_ui.bg_lock and fp_ui.bg_lock.gameObject ~= nil then
			if zjm_fp_btn_hide then
				fp_ui.bg.gameObject:SetActive(is_opened)
				fp_ui.bg_lock.gameObject:SetActive(false)
			else
				fp_ui.bg.gameObject:SetActive(is_opened)
				fp_ui.bg_lock.gameObject:SetActive(not is_opened)
			end
		end

		if fp_ui.name_bg and fp_ui.name_bg.gameObject ~= nil and fp_ui.name_bg_lock and fp_ui.name_bg_lock.gameObject ~= nil then
			if zjm_fp_btn_hide then
				fp_ui.name_bg.gameObject:SetActive(is_opened)
				fp_ui.name_bg_lock.gameObject:SetActive(false)
			else
				fp_ui.name_bg.gameObject:SetActive(is_opened)
				fp_ui.name_bg_lock.gameObject:SetActive(not is_opened)
			end
		end
	end
end

function FunOpen:DoUIFlyTween(fun_cfg, is_opened, reason_string, play_fly)
	if fun_cfg == nil then
		return
	end

	local open_type = fun_cfg.open_type
	local fun_name = fun_cfg.name

    -- 直接可视
	if open_type == FunOpenType.Visible then
		--这个地方会跟设置那边自动收起的冲突，这里加个时间限制
		local lock_time = 0.6
		MainuiWGCtrl.Instance:PlayTopButtonTween(true, lock_time)
		--等主界面按钮展开之后再让这个按钮出现
		GlobalTimerQuest:AddDelayTimer(function ()
			self:DoUIVisible(fun_cfg, is_opened, reason_string)
		end, lock_time)

	-- 功能飞行出现
	elseif is_opened and (open_type == FunOpenType.Fly or open_type == FunOpenType.FlyTabOpen or play_fly) then
		local mian_view = MainuiWGCtrl.Instance:GetView()
		if mian_view then
			local target_obj = mian_view:GetMainUICanFlyButtonObj(fun_cfg)
			if target_obj then
				self:DelayShowOpenFunFlyView(fun_cfg, target_obj)
			end
		end

	-- 弹出面板 按钮显示
	elseif is_opened and open_type == FunOpenType.OpenView then
		local open_param_t = self:GetOpenParam(fun_cfg.open_param)
		-- 天神功能开启时 做特殊处理
		if fun_name == "TianShenView" then
			GuajiWGCtrl.Instance:ClearGuajiCache()
			GuajiWGCtrl.Instance:StopGuaji(true, true)
			ViewManager.Instance:Open(GuideModuleName.FirstTSGetNewView)
		else
			if open_param_t ~= nil then
				local view_name = open_param_t[2] or open_param_t[1]
				local view_index = open_param_t[2] and open_param_t[1] or nil
				GlobalTimerQuest:AddDelayTimer(function ()
					ViewManager.Instance:Open(view_name, view_index)
				end, 0)
			end
		end
	end
end

--功能开启类型20 子功能其中一个开启 显示
function FunOpen:FunOpenNotifyEvent(fun_cfg)
	if fun_cfg and fun_cfg.parent_fun ~= "" then
		local parent_cfg = self:GetFunByName(fun_cfg.parent_fun)
		if not parent_cfg then
			return
		end

		local parent_open = self:GetFunIsOpened(parent_cfg.name)
		if parent_open then
			return
		end

		if parent_cfg.child_fun ~= "" and parent_cfg.trigger_type == GuideTriggerType.ChildFunOpen then
			local child_list = Split(parent_cfg.child_fun, "|")
			for k,v in pairs(child_list) do
				local is_open = self:GetFunIsOpened(v)
				if is_open then
					self:OpenFun(parent_cfg, false)
					return
				end
			end
		end
	end
end

-- 处理部分任务后功能开启需求 先弹出恭喜获得，新功能开启再飞
function FunOpen:DelayShowOpenFunFlyView(fun_cfg, target_obj)
	if fun_cfg == nil or target_obj == nil then
		return
	end

	local callback = function()
		TipWGCtrl.Instance:ShowOpenFunFlyView(fun_cfg, target_obj)
	end
	table.insert(self.delay_cache_callback_list, callback)

	if CountDownManager.Instance:HasCountDown("openfun_fly_dealy") then
		if #self.delay_cache_callback_list > 1 then
			self:DoShowOpenFunFlyView()
		end
		CountDownManager.Instance:RemoveCountDown("openfun_fly_dealy")
	end

	local total_time = 10
	local interval = 0.2
	local wait_time = 1.4 -- 等待时间
	local can_do_key = true
	local trigger_type = fun_cfg.trigger_type
	-- 需要骚操作
	if fun_cfg.with_param == 1 and trigger_type ~= GuideTriggerType.OutFb then
		CountDownManager.Instance:AddCountDown("openfun_fly_dealy",
		function(elapse_time, total_time)
			local is_open_getnew = AppearanceWGData.Instance:GetOpenGetNewViewState()
			if elapse_time >= wait_time and (not is_open_getnew) and can_do_key then
				self:DoShowOpenFunFlyView()
				can_do_key = false
			end
		end,
		nil, nil, total_time, interval)
	else
		self:DoShowOpenFunFlyView()
	end
end

function FunOpen:DoShowOpenFunFlyView()
	local callback = self.delay_cache_callback_list[1]
	if callback then
		callback()
		table.remove(self.delay_cache_callback_list, 1)
	end
end

-- 检测功能是不是需要提前显示，用于预告
function FunOpen:CheckFunIsNeedAdvanceShow(fun_name)
	if self.avdance_open_list[fun_name] then
		return true
	end

	local is_show = false
	if fun_name == nil then
		return is_show
	end

	local data = self:GetAdvanceNoticeCfg(fun_name)
	if data then
		local role_lv = RoleWGData.Instance:GetRoleLevel()
		if data.level ~= "" and data.level > role_lv then
			return is_show
		end

		if data.open_type == FUNOPEN_UNOPEN_REASON.OPEN_DAY then
			local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			if data.open_param ~= nil and data.open_param <= cur_open_day then
				is_show = true
			end
		end
	end

	return is_show
end

--强制开启某个功能，忽略配置限制
function FunOpen:ForceOpenFunByName(fun_name)
	local fun_cfg = self:GetFunByName(fun_name)
	if fun_cfg == nil then
		return
	end

	self.close_list[fun_cfg.name] = nil
	self:OpenFun(fun_cfg, true)
end

--检测某个功能开启
function FunOpen:CheckFunOpenByFunName(fun_name)
	local fun_cfg = self:GetFunByName(fun_name)
	if nil == fun_cfg then
		return
	end

	self:OpenFun(fun_cfg, false)
end

-- 删除功能开启配置
function FunOpen:DelOpenFunCfgByName(fun_name)
	self:ForceCloseFunByName(fun_name)
	self.fun_list[fun_name] = nil
end

--强制关闭某个功能   need_set_close 需要设置close_list为1
function FunOpen:ForceCloseFunByName(fun_name, need_set_close)
	local fun_cfg = self:GetFunByName(fun_name)
	self:CloseFun(fun_cfg, true, need_set_close)
end


function FunOpen:OpenFun(fun_cfg, is_force, is_init)
	if fun_cfg == nil then
		return
	end

	if self.close_list[fun_cfg.name] == 1 then
		return
	end

	if self:GetFunIsOpened(fun_cfg.name) and not is_force then
		return
	end

	self.opened_list[fun_cfg.name] = 1	--标记功能已开启
	GlobalEventSystem:Fire(OpenFunEventType.OPEN_TRIGGER, false, fun_cfg.name, true)
	self:DoOpenFun(fun_cfg, is_init or false)
end

--need_set_close 需要设置close_list| is_enforce_close 是否是后台强制关闭
function FunOpen:CloseFun(fun_cfg, force, need_set_close, is_enforce_close)
	if fun_cfg == nil then
		return
	end

	if not self:GetFunIsOpened(fun_cfg.name) and not force then
		return
	end

	-- OpenFun里会判断close_list，设置成1除非强开就一直打不开
	if not is_enforce_close and (self.opened_list[fun_cfg.name] ~= nil or need_set_close) then
		self.close_list[fun_cfg.name] = 1
	end

	if not is_enforce_close then
		self.opened_list[fun_cfg.name] = nil	--标记功能已关闭
	end
	
	GlobalEventSystem:Fire(OpenFunEventType.OPEN_TRIGGER, false, fun_cfg.name, false)
	-- 功能开启刷新功能绑定红点
	if self.remind_fun_t[fun_cfg.name] then 
		for k,v in pairs(self.remind_fun_t[fun_cfg.name]) do
		   RemindManager.Instance:Fire(v)
		end
   	end

	-- 强制关闭界面
	-- 单独写到上面是因为很多界面 ui是空 走不到下面 关不掉界面
	local open_type = fun_cfg.open_type
	if open_type == FunOpenType.Visible then
		ViewManager.Instance:Close(fun_cfg.open_param)
	end
	
	local ui = self.funui_list[fun_cfg.name]
	if ui == nil or (ui.Equals ~= nil and ui:Equals(nil)) then
		return
	end



	local is_opened, reason_string = self:GetFunIsOpened(fun_cfg.name, true)


	--生长（如用于场景上对象的出现）
	--[[
	if fun_cfg.open_type == FunOpenType.Born then
		local t = Split(fun_cfg.name, "_")
		local fun_name = t[1] .. "_"
		if fun_name == FunName.Gather then
			Scene.Instance:CheckGatherObj()
			MainuiWGCtrl.Instance:SetTargetObj(nil)
			MapWGCtrl.Instance:OnSceneObjFunOpenOrClose()

		elseif fun_name == FunName.Npc then
			MainuiWGCtrl.Instance:SetTargetObj(nil)
			local npc_id = tonumber(t[2]) or 0
			Scene.Instance:DeleteClientObjById(SceneObjType.Npc, npc_id)
			MapWGCtrl.Instance:OnSceneObjFunOpenOrClose()

		elseif fun_name == FunName.Decoration then
			local decoration_id = tonumber(t[3]) or 0
			Scene.Instance:DeleteClientObjById(SceneObjType.Decoration, decoration_id)
			MapWGCtrl.Instance:OnSceneObjFunOpenOrClose()
		end
		self:ShowSceneEffectOnClose(fun_cfg)
	else
	]]

	local open_param_t = self:GetOpenParam(fun_cfg.open_param)
	local open_type = fun_cfg.open_type
	if open_type == FunOpenType.Visible then
		ui:SetActive(is_opened)
		MainuiWGCtrl.Instance:UpdateFunuiPos(false)
	elseif open_type == FunOpenType.TabOpen then
		if open_param_t ~= nil then
			if open_param_t[1] == nil or TabIndex[open_param_t[1]] == nil then
				print_error("---功能开启配置异常---", fun_cfg.name)
				return
			end
			-- 为什么要除以1000啊
			local tab_index = math.floor(TabIndex[open_param_t[1]] / 1000)
			ui:SetToggleVisible(tab_index, is_opened, reason_string)
			MainuiWGCtrl.Instance:UpdateFunuiPos(false)
		end
	elseif open_param_t ~= nil then
		if TabIndex[open_param_t[1]] then
			local tab_index = math.floor(TabIndex[open_param_t[1]] / 1000)
			ui:SetToggleVisible(tab_index, is_opened)
		else
			ui:SetActive(is_opened)
		end
	end

	local tab_name
	if open_param_t ~= nil then
		tab_name = open_param_t[1]
	end

	-- 通知
	for k,v in pairs(self.close_notify_callback_list) do
		v(fun_cfg.name, tab_name)
	end
end

--通过自定义，模仿功能开启效果
--这里直接指定图标路径，因为往往跟配置的功能开启图标所在目录不同。
function FunOpen:OpenFunByDefine(fun_name, icon_path, ui)
	if self:GetFunByName(fun_name) ~= nil then
		return
	end

	local cfg = {
        name = fun_name,
        icon_path = icon_path,
        trigger_type = 0,
        trigger_param = "",
        with_param = "",
        vip_lev = "",
        open_type = FunOpenType.Fly,
        open_param = "",
        task_level = 0,
		skill_simulate_open = true,
        }

    self.fun_list[fun_name] = cfg
    self:RegisterFunUi(fun_name, ui)
    self:OpenFun(cfg)
end

--通过自定义，模仿功能开启效果
function FunOpen:OpenFunByDefine2(fun_name, icon, open_type, zjm_fp_btn_name, zjm_zp_btn_name, fly_pos)
	if self:GetFunByName(fun_name) ~= nil then
		return
	end

	local cfg = {
        name = fun_name,
        icon = icon,
        trigger_type = 0,
        trigger_param = "",
        with_param = "",
        vip_lev = "",
        open_type = open_type or FunOpenType.Visible,
        open_param = "",
        task_level = 0,
		zjm_zp_btn_name = zjm_zp_btn_name or "",
		zjm_fp_btn_name = zjm_fp_btn_name or "",
		fly_pos = fly_pos
        }

	self.fun_list[fun_name] = cfg
    self:OpenFun(cfg)
end


----------------
--- 获取开启 ----
----------------
-- 获得某功能是否已开启
function FunOpen:GetFunIsOpened(name, is_need_reason)
	-- 未开启充值
	if not RechargeWGData.IsOpenRecharge then
		if RECHARGE_FUNC[name] then
			return false, ""
		end
	end

	-- 审核服判断
	if IS_AUDIT_VERSION then
		-- 审核服需要关闭的功能
		if AUDIT_VERSION_CLOSE_FUNC[name] then
			return false, Language.Common.FunLock
		end

		-- 审核服默认永远开启的功能
		if AUDIT_VERSION_OPEN_FUNC[name] then
			return true
		end
	end

	local fun_cfg = self:GetFunByName(name)
	if fun_cfg == nil then
		return true
	end

	-- 是否后台强制关闭
	if self:IsEnforceClose(fun_cfg.funopen_seq) then
		if is_need_reason then
			return false, self:GetFunUnOpenCommonTip(fun_cfg)
		else
			return false	
		end
		
	end

	-- 先处理特殊的
	local flag, unopen_tip = self:GetFunIsOpenBySpecialFun(fun_cfg) --先处理特殊的
	if not flag then
		return false, unopen_tip
	end

	-- 再来个特殊处理(额外开启)
	local special_flag = self:GetFunIsOpenBySpecialFunTwo(fun_cfg)
	if special_flag then
		return true
	end

	if self.opened_list[name] == 1 then
		return true
	end

	if is_need_reason then
		return false, self:GetFunUnOpenCommonTip(fun_cfg)
	else
		return false
	end
end

-- 一些功能还需要考虑其他条件，在这里统一处理（注意，外部不调用，不全）
function FunOpen:GetFunIsOpenBySpecialFun(fun_cfg)
	if fun_cfg == nil or fun_cfg.name == nil then
		return false
	end

	if fun_cfg.name == FunName.DailyRecharge then
		if self.close_list[fun_cfg.name] == 1 then
			return false, Language.DailyRecharge.NoOpen
        end
    elseif fun_cfg.name == FunName.TianShenJuexing then
        if TianShenJuexingWGData.Instance:GetMainTianShenAwakenBtnState() then
            return false, Language.ShenBingAwaken.FunClose
        end
    -- elseif fun_cfg.name == FunName.NewAppearanceUpgradeLingChong then --角色神兵特殊处理
	-- 	if TianShenJuexingWGData.Instance:GetShenWuFuncIsOpen() then
    --         return true
    --     else
    --         return false, Language.Common.FunOpenTip
    --     end

    elseif fun_cfg.name == FunName.KF1V1 then
    	if self:GetFunIsOpened(FunName.WorldsNO1View) then 	-- 开了天下第一，则关闭跨服1v1
    		return false
    	end
  --   elseif fun_cfg.name == FunName.RechargeWeekcard then
  --   	local recharge_store_card_open = RechargeWGData.Instance:GetTZCardOpenState(INVEST_CARD_TYPE.StorehouseCard)
		-- local recharge_month_card_open = RechargeWGData.Instance:GetTZCardOpenState(INVEST_CARD_TYPE.MonthCard)
		-- local week_card_open = recharge_store_card_open and recharge_month_card_open
		-- return week_card_open
	-- elseif SystemForceWGData.Instance:GetForceshowIsHaveFunNameCfg(fun_cfg.name) then	--系统预告的功能特殊处理
	-- 	local is_open = SystemForceWGData.Instance:GetForceshowFunIsOpen(fun_cfg.name)
	-- 	if not is_open then
	-- 		return false, Language.DailyRecharge.NoOpen
	-- 	end
	else
		local param_tab = self:GetOpenParam(fun_cfg.open_param)
		if param_tab ~= nil then
			if param_tab[2] == FunName.Guild then -- 仙盟相关的功能,没加入仙盟认为没开启
				if RoleWGData.Instance.role_vo.guild_id == 0 then
					return false, Language.Common.PleaseJoinGuild
				end
			end
		end
	end

	return true
end

function FunOpen:GetFunIsOpenBySpecialFunTwo(fun_cfg)
	if fun_cfg == nil or fun_cfg.name == nil then
		return false
	end

	local fun_name = fun_cfg.name

	if fun_name == FunName.CustomizedSuitView then ---这里加个定制条件，激活了所有套装则先开启，不管表中配置的条件
		return CustomizedSuitWGData.Instance:GetOneSuitIsActive()
	elseif fun_name == FunName.sky_curtain then
		return BackgroundWGData.Instance:GetOneBackGroundIsActive()
	elseif fun_name == FunName.MechaView or fun_name == FunName.mecha_fighter_plane
		or fun_name == FunName.mecha_weapon or fun_name == FunName.mecha_wing
		or fun_name == FunName.mecha_to_fight or fun_name == FunName.mecha_equip then
		return MechaWGData.Instance:GetMechaActIsOpen()
	end

	-- 获得道具的强制开启
	local is_open = self:GetItemOpenSyaytem(fun_cfg)
	if is_open then
		return true
	end

	return false
end

-- 获得道具的强制开启
function FunOpen:GetItemOpenSyaytem(fun_cfg)
	if fun_cfg == nil or fun_cfg.name == nil then
		return false
	end

	local fun_name = fun_cfg.name
	if fun_name == FunName.HolyBeastsView then --创世圣兽
		local is_open = ControlBeastsWGData.Instance:CheckHolyBeastsIsCanOpen()
		if is_open then
			return true
		end
	end

	return false
end

-- 开启限制提示
function FunOpen:GetFunUnOpenCommonTip(fun_cfg)
	-- 默认
	if fun_cfg == nil then
		return Language.Common.FunOpenTip
	end
	-- 是否后台强制关闭
	if self:IsEnforceClose(fun_cfg.funopen_seq) then
		return Language.Common.FunOpenTip
	end
	-- 点击Ui触发
	if fun_cfg.trigger_type == GuideTriggerType.ClickUi then
		return Language.Common.FunOpenTip
	--等级限制
	elseif fun_cfg.trigger_type == GuideTriggerType.LevelUp then
		local limit_lv = tonumber(fun_cfg.trigger_param) or 0
		local role_level = RoleWGData.Instance:GetRoleLevel()
		if limit_lv > RoleWGData.GetRoleMaxLevel() then
			return string.format(Language.Common.FunOpenTip)
		else
			if limit_lv > role_level then
				return string.format(Language.Common.FunOpenRoleLevelLimit, RoleWGData.GetLevelString(limit_lv))
			end
		end
	-- 转职
	elseif fun_cfg.trigger_type == GuideTriggerType.ZhuanZhi then
		local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
		if role_zhuan < fun_cfg.trigger_param then
			return string.format(Language.Common.FunOpenZhuanZhiLevelLimit, fun_cfg.trigger_param, fun_cfg.show_name)
		end
	-- 任务限制
	elseif fun_cfg.trigger_type == GuideTriggerType.CommitTask then
		if not TaskWGData.Instance:GetTaskIsCompleted(fun_cfg.trigger_param) then
			local task_cfg = TaskWGData.Instance:GetTaskConfig(fun_cfg.trigger_param)
			if task_cfg then
				return string.format(Language.Common.FunOpenTaskLevelLimitCommit, RoleWGData.GetLevelString(tonumber(task_cfg.min_level) or 0), task_cfg.task_name)
			end
		end
	-- 跨服进度 and 等级
	elseif fun_cfg.trigger_type == GuideTriggerType.KFAndRoleLevelUp then
		local kf_world_level = BiZuoWGData.Instance and CrossServerWGData.Instance:GetServerMeanWorldLevel()
		local role_level = RoleWGData.Instance:GetRoleLevel()
		if (fun_cfg.cross_gs_level and fun_cfg.cross_gs_level > 0 and fun_cfg.cross_gs_level > kf_world_level) then
			local limit_lv = tonumber(fun_cfg.cross_gs_level) or 0
			return string.format(Language.Common.FunOpenKFLevel, RoleWGData.GetLevelString(limit_lv))
		end

		if fun_cfg.trigger_param and fun_cfg.trigger_param > 0 and fun_cfg.trigger_param > role_level then
			local limit_lv = tonumber(fun_cfg.trigger_param) or 0
			return string.format(Language.Common.FunOpenRoleLevelLimit, RoleWGData.GetLevelString(limit_lv))
		end
	-- 开服天数 abd 等级
	elseif fun_cfg.trigger_type == GuideTriggerType.OpenSeverDay then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

		if nil ~= fun_cfg.trigger_param and fun_cfg.trigger_param ~= "" and cur_open_day < fun_cfg.trigger_param then
			return string.format(Language.Common.FunOpenDayLimit, fun_cfg.trigger_param)
		else
			if nil ~= fun_cfg.trigger_param2 and fun_cfg.trigger_param2 ~= "" and role_level < fun_cfg.trigger_param2 then
				local is_dianfeng, level = RoleWGData.Instance:GetDianFengLevel(fun_cfg.trigger_param2)
				local level_str = is_dianfeng and string.format(Language.Common.LevelFeiXian, level) or level
				return string.format(Language.Common.FunOpenDayLimitAndLevel, fun_cfg.trigger_param, level_str)
			end
		end
	--系统预告限制
	elseif fun_cfg.trigger_type == GuideTriggerType.DayAndSpecialCondition then
		return Language.Common.FunOpenSystemForceLimit
	--任务限制
	elseif fun_cfg.trigger_type == GuideTriggerType.AcceptTask then
		local task_cfg = TaskWGData.Instance:GetTaskConfig(fun_cfg.trigger_param)
		if task_cfg then
			return string.format(Language.Common.FunOpenTaskLevelLimitAccept, RoleWGData.GetLevelString(tonumber(task_cfg.min_level) or 0), task_cfg.task_name)
		end
	--vip等级限制
	elseif fun_cfg.trigger_type == GuideTriggerType.LevelAndViplevel then
		return string.format(Language.Common.FunOpenLimitVipAndLevel, fun_cfg.trigger_param, RoleWGData.GetLevelString(fun_cfg.trigger_param2))
	end

	return Language.Common.FunOpenTip
end

-- 根据模块名 判定功能是否开启
function FunOpen:GetFunIsOpenedByMouduleName(module_name)
	local fun_name = self:GetFunNameByViewName(module_name)
	if fun_name ~= nil then
		return self:GetFunIsOpened(fun_name)
	end

	return true
end

-- 根据标签名 获得某个标签功能是否开启
function FunOpen:GetFunIsOpenedByTabName(tab_name, is_need_reason)
	if "" == tab_name or tab_name == nil then
		return true
	end

	local fun_name = self:GetFunNameByTabName(tab_name)
	return self:GetFunIsOpened(fun_name, is_need_reason)
end

-- 根据模块名，数字下标 获得某个标签功能是否开启
function FunOpen:GetFunIsOpenByViewNameAndNumberIndex(view_name, number_index)
	local fun_name = self:GetFunNameByViewNameAndNumberIndex(view_name, number_index)
	return self:GetFunIsOpened(fun_name, true)
end

-- 根据模块名字获得该模块下的 开启的，未开启的 名字列表
function FunOpen:GetTabNameListtByModuleName(module_name)
	local open_list = {}
	local unopen_list = {}
	if self:GetFunIsOpenedByMouduleName(module_name) then
		local list = self.module_tab_map[module_name] or {}
		for k,v in pairs(list) do
			if self:GetFunIsOpenedByTabName(v) then
				open_list[v] = 1
			else
				unopen_list[v] = 1
			end
		end
	end

	return open_list, unopen_list
end



--------------------
---   打开界面   ----
--------------------

--通过指定配置打开面板。一般用于直接用策划配置的链接
--配置如role#role_bag#op=1,uin=cell
function FunOpen:OpenViewNameByCfg(cfg, item_id)
	if cfg == nil then
		return false
	end

	local t = Split(cfg, "#")
	local view_name = t[1]
	local tab_index = t[2]
	local param_t = {
		open_param = nil,			--打开面板参数
		sub_view_name = nil,		--打开二级面板
		to_ui_name = 0,				--跳转ui
		to_ui_param = 0,			--跳转ui参数
	}

	if t[3] ~= nil then
		local key_value_list = Split(t[3], ",")
		for k,v in pairs(key_value_list) do
			local key_value_t = Split(v, "=")
			local key = key_value_t[1]
			local value = key_value_t[2]

			if key == "sub" then
				param_t.sub_view_name = value
			elseif key == "op" then
				param_t.open_param = value
			elseif key == "uin" then
				param_t.to_ui_name = value
			elseif key == "uip" then
				param_t.to_ui_param = value
			end
		end
	end

	if t[4] ~= nil then
		local key_value_t = Split(t[4], "=")
		local key = key_value_t[1]
		local value = key_value_t[2]
		if key == "sel_index" then
			param_t.sel_index = value
		elseif key == "item_id" then
			param_t.item_id = tonumber(value)
		end
	end

	if item_id then
		param_t.item_id = item_id
	end

	return self:OpenViewByName(view_name, tab_index, param_t)
end

-- 判断配置的跳转路径 对应的功能是否开启
-- cfg: view_name#tab_index
function FunOpen:GetCfgPathIsOpen(cfg)
    if cfg == nil then
		return false, ""
	end

    local t = Split(cfg, "#")
    local view_name = t[1]
    local tab_index = t[2]

    local fun_name = self:GetFunNameByViewName(view_name)
    local is_funopen, tip = self:GetFunIsOpened(fun_name, true)
    if not is_funopen then
        return false, tip
    end

    if tab_index and TabIndex[tab_index] ~= nil and TabIndex[tab_index] ~= "" then
		local is_tab_open, tip = self:GetFunIsOpenedByTabName(tab_index, true)
		if not is_tab_open then
			return false, tip
        end
    end

    if tab_index and type(tab_index) == 'number' then
        local is_tab_open, tip = self:GetFunIsOpenByViewNameAndNumberIndex(view_name, tab_index)
        if not is_tab_open then
            return false, tip
        end
    end

    return true, ""
end


--打开某个模块面板
--view_name 用guide_config中的GuideModuleName
--view_name 用guide_config中的TabIndex 不是定点打开的请不要设置
--param_t 参数 可通过调用OpenViewNameByCfg解析配置生成
function FunOpen:OpenViewByName(view_name, tab_index, param_t)
	if view_name == nil then
		return false
	end

	local fun_name = nil
	if view_name == GuideModuleName.Task and param_t and param_t.open_param ~= nil then --任务类型的特殊处理
		fun_name = FunOpen.Instance:GetFunNameByTaskType(tonumber(param_t.open_param))
	else
		fun_name = FunOpen.Instance:GetFunNameByViewName(view_name)
	end

	local tab_index_type = type(tab_index)
	--需要子功能其中一个开启才显示
	--优先判断子功能是否有开启，使用子功能的开启提示
	local fun_cfg = FunOpen.Instance:GetFunByName(fun_name)
	if fun_cfg and fun_cfg.trigger_type == GuideTriggerType.ChildFunOpen then
		if TabIndex[tab_index] ~= nil and TabIndex[tab_index] ~= "" then
		local is_tab_fun_open, tip = self:GetFunIsOpenedByTabName(tab_index, true)
		if is_tab_fun_open ~= true then --标签功能未开启
				SysMsgWGCtrl.Instance:ErrorRemind(tip)
				return false
	        end
	    end

		if tab_index and tab_index_type == 'number' then
			local is_open_2, tip_2 = self:GetFunIsOpenByViewNameAndNumberIndex(view_name, tab_index)
			if not is_open_2 then --标签功能未开启
				SysMsgWGCtrl.Instance:ErrorRemind(tip_2)
				return false
			end
		end
	else
		local is_funopen, tip = FunOpen.Instance:GetFunIsOpened(fun_name, true)
		if not is_funopen then --模块功能未开启
			if self.advance_notice_list[fun_name] ~= nil and self:CheckFunIsNeedAdvanceShow(fun_name) then
				self:DoOpenAdvanceView(fun_name, view_name, tab_index, param_t)
				return false
			end

			SysMsgWGCtrl.Instance:ErrorRemind(tip)
			return false
		end

		if TabIndex[tab_index] ~= nil and TabIndex[tab_index] ~= "" then
			local is_tab_fun_open, tip = self:GetFunIsOpenedByTabName(tab_index, true)
			if is_tab_fun_open ~= true then --标签功能未开启
				SysMsgWGCtrl.Instance:ErrorRemind(tip)
				return false
	        end
	    end

		if tab_index and tab_index_type == 'number' then
			local is_open_2, tip_2 = self:GetFunIsOpenByViewNameAndNumberIndex(view_name, tab_index)
			if not is_open_2 then --标签功能未开启
				SysMsgWGCtrl.Instance:ErrorRemind(tip_2)
				return false
			end
		end
	end

    if tonumber(tab_index) == nil and tab_index_type == 'string' then
    	if view_name == GuideModuleName.Shop then
    		tab_index = ShopWGData.ShowTypeToIndex[tab_index]
    	else
       		tab_index = TabIndex[tab_index]
       	end
    else
        tab_index = tonumber(tab_index)
    end

    local is_limit = self:DoLimitOpenViewByName(view_name, tab_index, param_t)
    if is_limit then
    	return false
    end

	self:DoOpenViewByName(view_name, tab_index, param_t)
	return true
end

--外部不要调，打开预告
function FunOpen:DoOpenAdvanceView(fun_name, view_name, tab_index, param_t)

end

-- 外部不要调，请调用OpenViewByName (打开界面限制别往这加)
function FunOpen:DoOpenViewByName(view_name, tab_index, param_t)
	-- 界面特殊开启 条件判断
	local is_special_open = self:DoSpecialOpenViewByName(view_name, tab_index, param_t)
	if is_special_open then
		return
	end

	--天神面板,检测天神宝匣界面开启状态
	if view_name == GuideModuleName.TianShenView then
		if TianShenWGCtrl.Instance:GetTianShenBaoXiaViewIsOpen() then
			TianShenWGCtrl.Instance:CloseTianShenBaoXiaView()
		end
	end

	local view = ViewManager.Instance:GetView(view_name)
	if view then
		ViewManager.Instance:Open(view_name, tab_index, nil, param_t)
	else
		-- 打开用了模块名，又不用模块名new的功能
		self:DoOpenViewNoNewByModule(view_name, tab_index, param_t)
	end
end

-- 打开界面限制
function FunOpen:DoLimitOpenViewByName(view_name, tab_index, param_t)
		for k,v in pairs(ActivityModuleName) do
		if k == view_name then
			if ActivityWGData.Instance:GetActivityIsOpen(v) then
				break
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
				return true
			end
		end
	end

	-- 动态运营活动判断
	if view_name == GuideModuleName.OperationActivityView and TabIndex[tab_index] then
		if not OperationActivityWGCtrl.Instance:GetActivityIsOpen(tab_index) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
			return true
		end

    -- 合服活动判断
	elseif view_name == GuideModuleName.MergeActivityView and TabIndex[tab_index] then
		if not MergeActivityWGCtrl.Instance:GetActivityIsOpen(tab_index) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
			return true
		end

     -- 特惠直购判断
    elseif view_name == GuideModuleName.LimitedTimeOffer then
        if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.LIMITEDTIMEOFFER) then
            if LimitedTimeOfferWGCtrl.Instance:CheckAllRewardIsGet() then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivityIsEnd)
                return true
            end

			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
            return true
        else
            local limit_lv = ActivityWGData.Instance:GetActivityLimitLevelById(ACTIVITY_TYPE.LIMITEDTIMEOFFER)
            local role_lv = RoleWGData.Instance:GetRoleLevel()
            if limit_lv > role_lv then --等级不够
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LimitLevel, RoleWGData.GetLevelString(limit_lv)))
                return true
            end
		end

    -- 节日活动判断
	elseif view_name == GuideModuleName.FestivalActivityView and TabIndex[tab_index] then
		if not FestivalActivityWGCtrl.Instance:GetActivityIsOpen(tab_index) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
			return true
		end

    -- 返利活动判断
    elseif view_name == GuideModuleName.RebateActivityView and TabIndex[tab_index] then
        local role_lv = RoleWGData.Instance:GetRoleLevel()
        local limit_lv = ActivityWGData.Instance:GetActivityLimitLevelById(ACTIVITY_TYPE.REBATE_ACTIVITY)
        local activity_type = RebateActivityWGData.Instance:GetCurSelectActivityType(TabIndex[tab_index])
        if not ActivityWGData.Instance:GetActivityIsOpen(activity_type) then --活动没开
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
            return true
        end
        if not RebateActivityWGCtrl.Instance:GetActivityIsOpen(tab_index) then --标签没开
            local tab_open_lv = RebateActivityWGData.Instance:GetTabOpenLevelByAct(activity_type) --标签开启等级
            local lv = math.max(limit_lv, tab_open_lv)
            if lv > role_lv then --等级不够
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.LimitLevel, lv))
                return true
            else--等级够，一般是奖励都领取完了
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.RewardAllGet)
                return true
            end
        end

	-- 天神之路
	elseif view_name == GuideModuleName.TianShenRoadPanel and TabIndex[tab_index] then
		local activity_open = TianshenRoadWGData.Instance:OpenActivityIsOpen()
		if not activity_open or not TianshenRoadWGData.Instance:GetActivityState(TabIndex[tab_index]) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
			return true
		end

	-- 全民备战
	elseif view_name == GuideModuleName.QuanMinBeiZhanView and TabIndex[tab_index] then
		local activity_open = QuanMinBeiZhanWGData.Instance:OpenActivityIsOpen()
		if not activity_open or not QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex[tab_index]) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
			return true
		end

	-- 仙器解封
	elseif view_name == GuideModuleName.ActXianQiJieFengView and TabIndex[tab_index] then
		local activity_open = ActXianQiJieFengWGData.Instance:OpenActivityIsOpen()
		if not activity_open or not ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex[tab_index]) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
			return true
		end

	-- 超值必买
	elseif view_name == GuideModuleName.MustBuy then
		local item_data = MustBuyWGData.Instance:GetItemList()
		local length = #item_data
		if length == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.MustBuy.NotBuy)
			return true
		end

	-- 充值、首充、每日累充、再充、每日首充、三充、月卡
	elseif view_name == GuideModuleName.DailyTotalRecharge or view_name == GuideModuleName.DailyRecharge
		or view_name == GuideModuleName.RechargeThird or view_name == GuideModuleName.RechargeAgain
		or view_name == GuideModuleName.Recharge
		or view_name == GuideModuleName.FirstCharge then
		if IS_FREE_VERSION then
			return true
		elseif not RechargeWGData.IsOpenRecharge then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.NotOpenCloseBeta)
			return true
		end

	--限时直购
	elseif view_name == GuideModuleName.ActivityLimitBuyView then
		if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY) then --活动没开
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
            return true
        end

	--烟花抽奖活动
	elseif view_name == GuideModuleName.RebateFireWorksView then
		if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW) then --活动没开
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
            return true
        end

	--特惠卖场
	elseif view_name == GuideModuleName.CheapShopPurchaseView then
		if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_SHOP) then --活动没开
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
            return true
        end
	--充值立减活动
	elseif view_name == GuideModuleName.RebateDiscountView then
		if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS) then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
            return true
        end
	--龙战于野
	elseif view_name == GuideModuleName.DragonTempleWildBuyView then
		if not DragonTempleWGData.Instance:ShowWildFunBtn() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewFightMount.NotBuy)
			return true
		end
	--[[ 随机活动
	elseif view_name == GuideModuleName.Activity then
		if not MainuiWGCtrl.Instance:GetUiIcon(ACTIVITY_TYPE.RAND_ACT) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
			return true
		end

		local open_act_cfg = ServerActivityWGData.Instance:GetShowOpenActList(true)
		if #open_act_cfg == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKai)
			return true
		end
	]]
	
	end

	return false
end

-- -- 界面特殊开启 条件判断  （打开界面限制别往这加）
function FunOpen:DoSpecialOpenViewByName(view_name, tab_index, param_t)
	-- 仙盟建设处理
	if view_name == GuideModuleName.guild_task then
		local is_have_guild = GuildWGData.Instance:IsHaveGuild()
		if is_have_guild == 1 then
			GuildWGCtrl.Instance:Open(TabIndex.guild_info)
			return true
		end
	-- 开服活动
	elseif view_name == GuideModuleName.OpenActivity or view_name == GuideModuleName.ServerActivityTabView then
		ServerActivityWGCtrl.Instance:OpenOpenserverCompetition("open_server", tab_index, param_t)
		return true
	elseif view_name == GuideModuleName.KfActivityView then
		ServerActivityWGCtrl.Instance:OpenOpenserverCompetition("kf_act", tab_index, param_t)
		return true
	-- 运营活动
	elseif view_name == GuideModuleName.OperationActivityView then
		OperationActivityWGCtrl.Instance:Open(tab_index, param_t)
		return true

	-- 商店
	elseif view_name == GuideModuleName.Shop then
		if param_t and param_t.open_param then
 			if tab_index and tab_index ~= "" then
 				ShopWGCtrl.Instance:ShopJumpToItemByIDAndTabIndex(tonumber(param_t.open_param), tab_index)
 			else
 				ShopWGCtrl.Instance:ShopJumpToItemByID(tonumber(param_t.open_param))
 			end
			return true
 		end
	end

	return false
end

-- 打开用了模块名，又不用模块名new的功能 (打开界面限制别往这加)
-- MMP写的那么嗨烂
function FunOpen:DoOpenViewNoNewByModule(view_name, tab_index, param_t)
	if view_name == GuideModuleName.GodGetReward then -- 丰收夺宝(原天神夺宝)
		GodGetRewardWGCtrl.Instance:OpenView(tab_index, nil, param_t)
	elseif view_name == GuideModuleName.Task then					--任务认为是人跑过去
		if param_t ~= nil and param_t.open_param ~= nil then
			local task_cfg = TaskWGData.Instance:GetOneActiveTask(tonumber(param_t.open_param))
			TaskWGCtrl.Instance:OperateFollowTask(task_cfg)
		end
	elseif view_name == GuideModuleName.GuajiMap then			--挂机地图是计算后飞过去
        local guaji_data = TaskWGData.Instance:GetVirtualGuajiTask()
        if guaji_data then
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Monster)
		    GuajiWGCtrl.Instance:FlyToScenePos(guaji_data.scene_id, guaji_data.x, guaji_data.y, false)
        end

	elseif view_name == GuideModuleName.MainUIView then			-- 主界面
		MainuiWGCtrl.Instance:ShowMainuiMenu()
	elseif view_name == GuideModuleName.Husong then				-- 护送界面
		YunbiaoWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.ContinueLevelup then	-- 继续升级界面
		if not IS_AUDIT_VERSION then
			GuideWGCtrl.Instance:OpenContinueLevelupView(tab_index, param_t)
		end
	elseif view_name == GuideModuleName.QuintupleExp then 		-- 五倍面板
		ActIvityHallWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.FuBenPanel then			-- 副本界面
		FuBenPanelWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.ActivityMainPanel then			-- 所有活动界面
		ActivityMainWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.ShenEquip then			-- 神装界面
		--EquipmentShenWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.EquipOld then			-- 装备界面
		EquipmentWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.RoleView then			-- 个人信息界面
		RoleWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.Appearance then			-- 外观面板
		AppearanceWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.Guild then				-- 仙盟界面
		GuildWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.Compose then			-- 炼炉界面
		ComposeWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.Daily then				-- 日常界面
		BiZuoWGCtrl.Instance:Open(BiZuoView.TabIndex.BB)
	elseif view_name == GuideModuleName.DayActivity then		-- 活动界面
		ActivityWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.ActIvityHall then		-- 活动大厅界面
		BiZuoWGCtrl.Instance:Open(BiZuoView.TabIndex.BAH)
	elseif view_name == GuideModuleName.CombineSociety then		-- 综合社交界面
		--ActHallWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.Setting then			-- 设置界面
		SettingWGCtrl.Instance:Open(tab_index)
	elseif view_name == GuideModuleName.Rank then				-- 排行榜界面
		RankWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.Vip then				-- VIP界面
		RechargeWGCtrl.Instance:Open(TabIndex.recharge_vip)
 	elseif view_name == GuideModuleName.Shop then				-- 商城界面
		ShopWGCtrl.Instance:Open(tab_index)
	elseif view_name == GuideModuleName.Chat then				-- 聊天界面
		ChatWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.CommonActivity then		-- 公共活动模板界面
		if type(param_t) == "table" then
			ActivityWGCtrl.Instance:OpenPopView(tonumber(param_t.open_param))
		else
			ActivityWGCtrl.Instance:OpenPopView(param_t)
		end
	elseif view_name == GuideModuleName.Activity then			-- 积分福袋界面
		ServerActivityWGCtrl.Instance:Open(param_t)
	elseif view_name == GuideModuleName.FirstCharge then
		ServerActivityWGCtrl.Instance:OpenFirstRechargeView(param_t)
	elseif view_name == GuideModuleName.Flower then				-- 送花界面
		FlowerWGCtrl.Instance:Open(param_t)
	elseif view_name == GuideModuleName.Recharge then			-- 充值界面
		local open_tab_index = TabIndex.recharge_cz
		if tab_index ~= nil and tab_index > 0 then
			open_tab_index = tab_index
		end
		RechargeWGCtrl.Instance:Open(open_tab_index)
	elseif view_name == GuideModuleName.MarryFB then 			--打开结婚副本
		MarryWGCtrl.Instance:Open(TabIndex.marry_fb, param_t)
	elseif view_name == GuideModuleName.Award then 				--打开活跃度奖励
		MainuiWGCtrl.Instance:AwardOpen()
	elseif view_name == GuideModuleName.Jinyinta then 			--打开金银塔
		JinyintaWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.OpenServer then		    --打开开服活动
		ServerActivityWGCtrl.Instance:OpenOpenserverView(tab_index, param_t)
	elseif view_name == GuideModuleName.CombineActivity then	-- 打开合服活动
		ServerActivityWGCtrl.Instance:OpenOpenserverView(tab_index, param_t, 2)
	elseif view_name == GuideModuleName.BanBenActivity then		-- 打开版本活动
		ServerActivityWGCtrl.Instance:OpenBanBenView(tab_index)
	elseif view_name == GuideModuleName.FcActivity then			--打开封测活动
		ServerActivityWGCtrl.Instance:OpenCloseBetaView()
	elseif view_name == GuideModuleName.UpdateAffiche then 		--打开更新公告
		WelfareWGCtrl.Instance:Open(WELFARE_TYPE.GONGGAO)
	elseif view_name == GuideModuleName.RechargeAgain then		--打开再充界面
		RechargeRewardWGCtrl.Instance:OpenAgainRechargeView()
	elseif view_name == GuideModuleName.RechargeThird then		--打开三充界面
		RechargeRewardWGCtrl.Instance:OpenThirdRechargeView()
	elseif view_name == GuideModuleName.DailyRecharge then		--打开每日首充界面
		RechargeRewardWGCtrl.Instance:OpenDailyRechargeView()
	elseif view_name == GuideModuleName.DailyTotalRecharge then	 --打开每日累充界面
		RechargeRewardWGCtrl.Instance:OpenTotalRechargeView()
	elseif view_name == GuideModuleName.Map then
		MapWGCtrl.Instance:Open()										-- 地图界面
		-- RechargeWGCtrl.Instance:Open(RechargeView.TABINDEX.TZJH)
	elseif view_name == GuideModuleName.ActivityBipin then
		CompetitionWGCtrl.Instance:Open()								-- 比拼活动
	elseif view_name == GuideModuleName.AgentPromote then 				-- 平台推广
		OtherWGCtrl.Instance:OpenAgentPromoteView()
	elseif view_name == GuideModuleName.Boss then 						-- boss界面
		if IS_ON_CROSSSERVER then
			if self:GetFunIsOpened(FunName.KuafuBoss) then
				BossWGCtrl.Instance:Open(BossViewIndex.KFBoss, param_t)
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OnCrossServerTip)
			end
		else
			BossWGCtrl.Instance:Open(tab_index or BossViewIndex.WorldBoss, param_t)
		end
	elseif view_name == GuideModuleName.TimeLimitFeedback then 			-- 限时回馈

	elseif view_name == GuideModuleName.MiJingTaoBaoTwo then 			-- 密境淘宝2

	elseif view_name == GuideModuleName.PerfectLover then 				-- 完美情人
		PerfectLoverWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.SingleRechargeOne then 			-- 冲战新星1
		ServerActivityWGCtrl.Instance:OpenRushRecharge()
	elseif view_name == GuideModuleName.SingleRechargeTwo then 			-- 我要冲战2
		ServerActivityWGCtrl.Instance:OpenRushRechargeTwo()
	elseif view_name == GuideModuleName.SingleRechargeThree then 		-- 急速冲战3
		ServerActivityWGCtrl.Instance:OpenRushRechargeThree()
	elseif view_name == GuideModuleName.SingleRechargeFour then 		-- 战榜提名4
		ServerActivityWGCtrl.Instance:OpenRushRechargeFour()
	elseif view_name == GuideModuleName.LotteryTree then 				-- 摇钱树
		LotteryTreeWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.ActMaze then 					-- 迷宫寻宝

	elseif view_name == GuideModuleName.ConsumeDiscount then 			--连消特惠
		ConsumeDiscountWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.FriendsBlessing then 			--好友祝福
		SocietyWGCtrl.Instance:OpenFriendblessPanel()
	elseif view_name == GuideModuleName.FriendsReturn then 				--好友回赠
		SocietyWGCtrl.Instance:OpenFriendReceivePanel()
	elseif view_name == GuideModuleName.Blessing then 					--祝福
		SocietyWGCtrl.Instance:OpenBlessingView()
	elseif view_name == GuideModuleName.KuafuHonorhalls then 			--跨服修罗塔
		KuafuHonorhallWGCtrl.Instance:EnterXiuLuoTower()
	elseif view_name == GuideModuleName.KuafuYeZhanWangCheng then 		--夜战王城
		KuafuYeZhanWangChengWGCtrl.Instance:SendNightFightEnterReq()
	elseif view_name == GuideModuleName.KuafuOneVsN then 				--跨服1vN
	elseif view_name == GuideModuleName.BiZuo then 						--打开每日必做界面（新）
		BiZuoWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.FlowerRank then 				--情人鲜花榜
		FlowerRankWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.BlackMarketAuction then			--黑市拍卖

	elseif view_name == GuideModuleName.KuafuConsume then				--跨服消费排行榜
		KuafuConsumptionRankWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.WeGetMarried then				--跨服充值排行榜
		WeGetMarriedWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.TeShuFace then					-- 特殊表情(单个激活的动态表情)
	elseif view_name == GuideModuleName.NoticeTwo then 					-- 功能预告2面板
		GuideWGCtrl.Instance:NoticeTwoOpen(tab_index, param_t)
	elseif view_name == GuideModuleName.KuafuOneVsOne then				-- 打开跨服1v1界面
		KuafuOnevoneWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.KuafuPVP then					-- 打开跨服pvp界面
		KuafuPVPWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.Answer then					    -- 打开答题界面
		ActivityWGCtrl.Instance:SendActivityEnterReq(ACTIVITY_TYPE.ANSWER)
	elseif view_name == GuideModuleName.TOTAL_CHONGZHI then 			-- 累计充值
		TotalChargeWGCtrl.Instance:Open()
	-- elseif view_name == GuideModuleName.ChatWinView then 				-- 个性聊天
	-- 	ChatWinWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.Welkin then 					-- 天仙阁
		FuBenPanelWGCtrl.Instance:Open(TabIndex.fubenpanel_welkin)
	elseif view_name == GuideModuleName.MarryParty then 					-- 婚宴
		local id = WeddingWGData.Instance:GetScenceId()
		WeddingWGCtrl.Instance:SendJoinHunyan(id)
	elseif view_name == GuideModuleName.WeddingDeMand then                     -- 婚宴请柬
		WeddingWGCtrl.Instance:WeddingDeMandViewOpen()
	elseif view_name == GuideModuleName.ZhuZaiShenDian then 				-- 主宰神殿
		ZhuZaiShenDianWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.QIFU then 							-- 祈福
		QiFuWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.SevenDay then 							-- 七天
		SevenDayWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.ShenShou then 						-- 神兽
		ShenShouWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.Achievement then 					-- 成就
		AchievementWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.ZhuanSheng then 						-- 转职界面
		TransFerWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.NewTeam then                   -- 组队
		NewTeamWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.GuildAnswer then                     -- 帮派答题
		if RoleWGData.Instance.role_vo.guild_id == 0 then
			GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist)
		else
			GuildAnswerWGCtrl.Instance:SendGuildQuestionEnterReq()
		end
	elseif view_name == GuideModuleName.GuildJiuSheDaoFB then                     -- 帮派副本
		if RoleWGData.Instance.role_vo.guild_id == 0 then
			GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist)
		else
			GuildWGCtrl.Instance:SendJiuSheDaoFBEnter()
		end
	elseif view_name == GuideModuleName.OpenServerRank then
		ServerActivityWGCtrl.Instance:OpenRechargeRankView(tab_index, param_t)

	elseif view_name == GuideModuleName.CallBoss then
		BossWGCtrl.Instance:OpenCallBossView()
	elseif view_name == GuideModuleName.ComposeTicket then  --打开任务合成门票，策划要求
		ComposeWGData.Instance:SetComeFromTask(true)
		ComposeWGCtrl.Instance:Open(11)
	elseif view_name == GuideModuleName.ChargeRepayment2 then
		ChargeRepayment2WGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.HappyConsume then
		HappyConsumeWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.DailyConsume then 				-- 每日累消
		DailyConsumeWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.WishingPool then
		ActwishingWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.TreasureAct then
		ActTreasureWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.Huanlezadan then
		HuanlezadanWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.HuanlezadanRank then
		HuanlezadanWGCtrl.Instance:OpenQuanFuView()
	elseif view_name == GuideModuleName.ConsumeRankThree then 			-- 本服消费排行
		ConsumeRankThreeWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.TeHuiShop then
		TehuiShopWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.dayact_zhuxie then--诛邪战场
		ActivityWGCtrl.Instance:OpenZhuXieFollow()
	elseif view_name == GuideModuleName.Unlock then
		SettingWGCtrl.Instance:OpenUnLockView()
	elseif view_name == GuideModuleName.EveryDayOneLove then
		EveryDayOneLoveWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.XiuZhenRoadView then
		XiuZhenRoadWGCtrl.Instance:Open()
	elseif view_name == GuideModuleName.TianShenRoadPanel then
		TianshenRoadWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.TIANSHEN_JIANLI then
		TianshenRoadWGCtrl.Instance:GotoShiLian()
	elseif view_name == GuideModuleName.QuanMinBeiZhanView then
		QuanMinBeiZhanWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.KuafuBossMH then
		WorldServerWGCtrl.Instance:Open(TabIndex.worserv_boss_mh)
	elseif view_name == GuideModuleName.ActXianQiJieFengView then--仙器解封
		ActXianQiJieFengWGCtrl.Instance:Open(tab_index, param_t)
	elseif view_name == GuideModuleName.UltimateBattlefieldView then	-- 终极战场
		if UltimateBattlefieldWGData.Instance:CheckActIsOpen() then
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.UltimateBattlefield.ActivityNotEnter)
		end
	end
end

----------------
---   废弃   ----
----------------
function FunOpen:ShowSceneEffectOnOpen(fun_cfg, is_init)
	if fun_cfg == nil then
		return
	end

	if is_init then
		Scene.Instance:CheckClientObj()
		return
	end

	if fun_cfg.name == "decoration_104_27" then 	--触发时效果:仙he飞出，然后消失再出一群仙he
			self.is_scene_effect_opening = false
			Scene.Instance:CheckClientObj()
	elseif fun_cfg.name == "decoration_102_31" then --触发时效果：狗跑出来到处跑，然后消失，再出一个休闲r狗
			self.is_scene_effect_opening = false
			Scene.Instance:CheckClientObj()
			Scene.Instance:CheckGatherObj()
	-- elseif fun_cfg.name == "ziti_texiao1" then
	-- 	local eff = RenderUnit.CreateEffect(150, nil, nil, FrameTime.Effect * 3, 1, HandleRenderUnit:GetWidth() - 150, HandleRenderUnit:GetHeight() - 300)
	-- 	HandleRenderUnit:GetCoreScene():addChildToRenderGroup(eff, GRQ_UI_UP)
	-- elseif fun_cfg.name == "ziti_texiao2" then
	-- 	local eff = RenderUnit.CreateEffect(151, nil, nil, FrameTime.Effect * 3, 1, HandleRenderUnit:GetWidth() - 150, HandleRenderUnit:GetHeight() - 300)
	-- 	HandleRenderUnit:GetCoreScene():addChildToRenderGroup(eff, GRQ_UI_UP)
	-- elseif fun_cfg.name == "ziti_texiao3" then
	-- 	local eff = RenderUnit.CreateEffect(152, nil, nil, FrameTime.Effect * 3, 1, HandleRenderUnit:GetWidth() - 150, HandleRenderUnit:GetHeight() - 300)
	-- 	HandleRenderUnit:GetCoreScene():addChildToRenderGroup(eff, GRQ_UI_UP)

	elseif fun_cfg.name == "yinshen" then			-- 隐身
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:SetOpacityByTask(true)
		end
	elseif fun_cfg.name == "end_yinshen" then		-- 结束隐身
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:SetOpacityByTask(false)
		end
	elseif fun_cfg.name == "scale_big" then			-- 变大形象
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:SetScaleByTask(true, fun_cfg.with_param2, 1)
		end
	elseif fun_cfg.name == "end_scale_big" then		-- 恢复形象
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:SetScaleByTask(false, 0, 2)
		end
	elseif fun_cfg.name == "scale_small" then		-- 变小形象
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:SetScaleByTask(true, fun_cfg.with_param2, 1)
		end
	elseif fun_cfg.name == "end_scale_small" then	-- 恢复形象
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:SetScaleByTask(false, 0, 2)
		end
	elseif fun_cfg.name == "bianguai" then			-- 变怪
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:UpdateSpecialAppearanceByTask(true, fun_cfg.with_param2, 0)
		end
	elseif fun_cfg.name == "end_bianguai" then		-- 变怪后恢复形象
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:UpdateSpecialAppearanceByTask(false, 0, 0)
		end
	elseif fun_cfg.name == "bianpet" then			-- 变宠物
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:UpdateSpecialAppearanceByTask(true, 0, fun_cfg.with_param2)
		end
	elseif fun_cfg.name == "end_bianpet" then		-- 变宠物后恢复形象
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local obj = Scene.Instance:GetObjectByObjId(mainrole_vo.obj_id)
		if obj ~= nil and obj:IsRole() then
			obj:UpdateSpecialAppearanceByTask(false, 0, 0)
		end
	else
		Scene.Instance:CheckClientObj()
	end
end

function FunOpen:ShowSceneEffectOnClose(fun_cfg)
	if fun_cfg == nil then
		return
	end

	if fun_cfg.name == "gather_103_136" then --触发时效果：在某处放烟花
		local function fire_end_callback()
			self.is_scene_effect_opening = false
		end
		self.is_scene_effect_opening = true
		local w_pos_x, w_pos_y = HandleRenderUnit:LogicToWorldXY(283, 188) --无奈写死它
		local function dofire()
			local effect = Scene.Instance:CreateOnceEffect(3089, w_pos_x + math.random(-300, 300), w_pos_y + 30 + math.random(80,220), nil, fire_end_callback)
			effect:setScale(math.random(1.4, 1.6))
		end

		for i=1,30 do
			local time = math.random(0.1, 0.3) * i
			if time > 2 then time = 2 end
			GlobalTimerQuest:AddDelayTimer(dofire, time)
		end
	end
end

function FunOpen:GetSceneEffectOpening()
	return self.is_scene_effect_opening
end