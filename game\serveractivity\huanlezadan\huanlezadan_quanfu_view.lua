Hu<PERSON>lezadanQuanFuView = Hu<PERSON><PERSON>zadanQuanFuView or BaseClass(SafeBaseView)
function Hu<PERSON>lezadanQuanFuView:__init()
	self:SetMaskBg(false, false)

	self:AddViewResource(0, "uis/view/huanlezadan_ui_prefab", "layout_egg_rank")
end

function HuanlezadanQuanFuView:__delete()

end

function HuanlezadanQuanFuView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("activity_remaining_time_rank") then
		CountDownManager.Instance:RemoveCountDown("activity_remaining_time_rank")
	end
	if self.list_reward then
		self.list_reward:DeleteMe()
		self.list_reward = nil
	end
end

function HuanlezadanQuanFuView:OpenCallBack()
	-- 视图一打开就发送请求给服务器
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG) then
		return
	end
	HuanlezadanWGCtrl.Instance:SendReq(RA_CS_CROSS_RA_SMASHED_EGG_INFO.CS_CROSS_RA_SMASHED_EGG_INFO)
	RankWGCtrl.Instance:SendCrossGetRankListReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG)
end

function HuanlezadanQuanFuView:LoadCallBack()
	-- 设置砸蛋榜列表
	self:CreateSmashEggRankList()

	self.node_list.btn_model.button:AddClickListener(BindTool.Bind(self.OnClickModel, self))
end

function HuanlezadanQuanFuView:OnClickModel()
	local rank_award = HuanlezadanWGData.Instance:GetRankRewardCfg(1)
	TipWGCtrl.Instance:OpenItem(rank_award.reward_item[0], self.item_tip_from or ItemTip.FROM_NORMAL, nil)
end

function HuanlezadanQuanFuView:ShowIndexCallBack(index)
	-- 设置一个定时器记录活动剩余的时间
	if CountDownManager.Instance:HasCountDown("activity_remaining_time_rank") then
		CountDownManager.Instance:RemoveCountDown("activity_remaining_time_rank")
	end
	-- 根据ACTIVITY_TYPE来获取活动状态
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG) or
	{}
	-- 判断活动是否开启
	if act_info.status == ACTIVITY_STATUS.OPEN then
		-- 如果活动开启则倒计时
		local next_time = act_info.end_time or 0
		self:UpdateRemainTimeCallBack(TimeWGCtrl.Instance:GetServerTime(), next_time)
		CountDownManager.Instance:AddCountDown("activity_remaining_time_rank",
			BindTool.Bind1(self.UpdateRemainTimeCallBack, self), BindTool.Bind1(self.CompleteCallBack, self), next_time,
			nil, 1)
	else
		-- 否则活动剩余时间设置为0
		self:CompleteCallBack()
	end
end

-- 剩余时间回调
function HuanlezadanQuanFuView:UpdateRemainTimeCallBack(elapse_time, next_time)
	-- -- elapse_time等于TimeWGCtrl.Instance:GetServerTime()
	-- -- 下一次时间减去当前时间就是活动剩余时间
	local time = next_time - elapse_time
	if self.node_list.label_time then
		if time > 0 then
			local format_time = TimeUtil.FormatSecond(time)
			self.node_list.label_time.text.text = (format_time)
		end
	end
end

-- 剩余时间结束回调
function HuanlezadanQuanFuView:CompleteCallBack()
	if self.node_list.label_time then
		self.node_list.label_time.text.text = ("00:00:00")
	end
end

function HuanlezadanQuanFuView:OnFlush()
	local times = HuanlezadanWGData.Instance:GetSCCrossRASmashedEggInfo().cur_total_times
	self.node_list.label_egg_times.text.text = (times)

	self.list_reward:SetDataList(HuanlezadanWGData.Instance:GetSmashEggRankInfo() or {})
end

function HuanlezadanQuanFuView:CreateSmashEggRankList()
	-- 创建一个ListView
	self.list_reward = AsyncListView.New(QuanFuHuanlezadanRender, self.node_list.ph_rank)
end

--------------------------------------------
--QuanFuHuanlezadanRender
--------------------------------------------

QuanFuHuanlezadanRender = QuanFuHuanlezadanRender or BaseClass(BaseRender)
function QuanFuHuanlezadanRender:__init()
end

function QuanFuHuanlezadanRender:__delete()
end

function QuanFuHuanlezadanRender:LoadCallBack()
	-- BaseRender.CreateChild(self)
end

function QuanFuHuanlezadanRender:OnFlush()
	if nil == self.data then return end
	self.node_list.label_rank_index.text.text = (self.data.rank_index)
	self.node_list.label_server_id.text.text = (HuanlezadanWGData.Instance:GetServerName(self.data))
	self.node_list.label_user_name.text.text = (self.data.user_name)
	if self.data.rank_index > 0 and self.data.rank_index <= 3 then
		self:CreateRankMedal(self.data.rank_index)
	else
		self.node_list.rank_img:SetActive(false)
	end

	if self.data.rank_index > 0 and self.data.rank_index <= 2 then
		self.node_list.label_rank_value.text.text = (Language.Huanlezadan.BaoMi)
	else
		self.node_list.label_rank_value.text.text = (self.data.rank_value)
	end
end

function QuanFuHuanlezadanRender:CreateRankMedal(rank)
	self.node_list.rank_img:SetActive(true)
	self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonOthers("rank_num_" .. rank))
end

function QuanFuHuanlezadanRender:CreateSelectEffect()
end
