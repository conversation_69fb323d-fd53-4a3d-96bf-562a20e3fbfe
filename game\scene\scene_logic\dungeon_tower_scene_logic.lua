-- require("game/dungeon/dungeon_scene/dungeon_tower_left")
-- require("game/dungeon/dungeon_scene/dungeon_tower_down")

DungeonTowerSceneLogic = DungeonTowerSceneLogic or BaseClass(CommonFbLogic)

function DungeonTowerSceneLogic:__init()

end

function DungeonTowerSceneLogic:__delete()

end

function DungeonTowerSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- if MainuiWGCtrl.Instance:GetMenuIsShow() then
	-- 	DungeonWGCtrl.Instance:CloseTowerLeftView()
	-- 	DungeonWGCtrl.Instance:CloseTowerDownView()
	-- else
	-- 	DungeonWGCtrl.Instance:OpenTowerLeftView()
	-- 	DungeonWGCtrl.Instance:OpenTowerDownView()
	-- end
	-- DungeonWGCtrl.Instance:ReSetTowerLeftPanel()
end

function DungeonTowerSceneLogic:Out()
	CommonFbLogic.Out(self)
	
	-- DungeonWGCtrl.Instance:CloseTowerDownView()
	-- DungeonWGCtrl.Instance:CloseTowerLeftView()
	-- DungeonWGCtrl.Instance.dungeon_finish_two:CloseFinish()
	
	-- local role_level = RoleWGData.Instance.role_vo.level
	-- if DungeonData.Instance:GetTowerCanEnterTimes() > 0 and role_level >= DUNGEON_OPEN_WIN_LEVEL then
	-- 	FunOpen.Instance:OpenViewByName(GuideModuleName.Fuben, TabIndex.fb_fangju)
	-- end
end

function DungeonTowerSceneLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
	-- DungeonWGCtrl.Instance:ShowTowerLeftAction(is_show)
end

-- 是否需要回到原点
function DungeonTowerSceneLogic:IsNeedToOrigin()
	return true
end

-- 是否是挂机打怪的敌人
function DungeonTowerSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end