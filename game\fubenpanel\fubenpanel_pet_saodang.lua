PetSaoDangView = PetSaoDangView or BaseClass(SafeBaseView)

function PetSaoDangView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_pet_saodang")

	self.saodang_index = -1
	self.item_data_change_callback = BindTool.Bind1(self.ShowIndexCallBack, self)
end

function PetSaoDangView:__delete()

end

function PetSaoDangView:ReleaseCallBack()
	if self.saodang_cell then
		self.saodang_cell:DeleteMe()
		self.saodang_cell = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function PetSaoDangView:ShowIndexCallBack()
	self:Flush()
end

function PetSaoDangView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.FuBenPanel.TianShenSaoDangXiaoHao
	self:SetSecondView(nil, self.node_list["size"])
	local ph = self.node_list["ph_cell"]
	self.saodang_cell = ItemCell.New(ph)
	self.node_list["btn_ok"].button:AddClickListener(BindTool.Bind(self.OnClinkOkHandler, self))
	XUI.AddClickEventListener(self.node_list.btn__pet_saodang_cancel, BindTool.Bind1(self.PetSaoDangViewClose, self))
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function PetSaoDangView:ShowIndexCallBack()
	self:Flush()
end

function PetSaoDangView:PetSaoDangViewClose()
	self:Close()
end

function PetSaoDangView:OnFlush()
	--local star_num = FuBenPanelWGData.Instance:SetPetStarDisplay(self.saodang_index)
	--local str = ""
	--if star_num and star_num >= 0 then
	--	str = string.format(Language.FuBenPanel.CopperSaoDangTips, star_num, star_num)
	--end
	--self.node_list["rich_saodang_des"].text.text = str
	local other_cfg = FuBenPanelWGData.Instance:GetPetOtherCfg()
	if self.saodang_cell then
		self.saodang_cell:SetData({item_id = other_cfg.sweep_mark_id})
	end
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_mark_id)
	if item_num < other_cfg.sweep_mark_num then
		self.node_list["lbl_item_num"].text.text = "<color=#ff0000>"..item_num.."</color>" .. "/" .. other_cfg.sweep_mark_num
	else
		self.node_list["lbl_item_num"].text.text = item_num .. "/" .. other_cfg.sweep_mark_num
	end
end

function PetSaoDangView:SetPetIndex(index)
	self.saodang_index = index
	self:Open()
end

function PetSaoDangView:OnClinkOkHandler()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyPetCfg()
	local pet_info = FuBenPanelWGData.Instance:GetPetAllInfo()
	local buy_times = vip_buy_cfg["param_" .. role_vip] - pet_info.buy_times
	local other_cfg = FuBenPanelWGData.Instance:GetPetOtherCfg()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_mark_id)
	if item_num < other_cfg.sweep_mark_num then
		GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, other_cfg.sweep_mark_id, other_cfg.sweep_mark_num, other_cfg.seq)
		return
	end

	if buy_times > 0 and not FuBenPanelWGData.Instance:CheckPetCount() then
		FuBenPanelWGCtrl.Instance:OpenPetBuy(true)
	else
		FuBenPanelWGData.Instance:SetSaoDangMark(true)
		FuBenPanelWGCtrl.Instance:SendChongWuFbOperate(NEW_PETFB_REQ_TYPE.NEW_PETFB_REQ_TYPE_AUTO_FB, self.saodang_index - 1)
	end
end