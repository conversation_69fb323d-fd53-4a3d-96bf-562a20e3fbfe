NewFestivalPrayerWGData = NewFestivalPrayerWGData or BaseClass()

NewFestivalPrayerTaskType = {
	Daily = 0,    -- 每日任务
	Jrhd = 1,     -- 节日任务
}

function NewFestivalPrayerWGData:__init()
	if NewFestivalPrayerWGData.Instance then
		ErrorLog("[NewFestivalPrayerWGData] Attemp to create a singleton twice !")
	end

	NewFestivalPrayerWGData.Instance = self
	RemindManager.Instance:Register(RemindName.NewFestivalPrayer, BindTool.Bind(self.IsShowPrayerRedPoint, self))

	self:InitCfg()

	self.prayer_count = 0
	self.advanced_prayer_flag = 0
	self.prayer_buy_limit = {}
	self.reward_flag = {}
	self.prayer_task_process = {}
	self.pray_task_flag = {}
	self.grade = 1
	self.show_all_task_list = {}
end

function NewFestivalPrayerWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("new_festival_activity_prayer_config_auto")
	self.open_day_cfg = ListToMap(cfg.open_day, "grade")
	self.prayer_reward_cfg = ListToMap(cfg.prayer_reward, "grade", "seq")
	self.prayer_buy_cfg = ListToMap(cfg.prayer_buy, "grade", "seq")
	self.task_list_cfg = ListToMap(cfg.task_list, "grade", "task_id")
	self.model_show_cfg = ListToMap(cfg.model_show, "grade")
end

function NewFestivalPrayerWGData:__delete()
    NewFestivalPrayerWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.NewFestivalPrayer)
end

--全部数据
function NewFestivalPrayerWGData:SetAllPrayerAllInfo(protocol)
	self.grade = protocol.grade
	self.prayer_count = protocol.prayer_count
	self.advanced_prayer_flag = protocol.advanced_prayer_flag
	self.prayer_buy_limit = protocol.prayer_buy_limit
	self.reward_flag = protocol.reward_flag
	self.prayer_task_process = protocol.prayer_task_process
	self.pray_task_flag = protocol.pray_task_flag

	self:UpdateRewardShowInfo()
	self:UpdatAllShowTaskList()
end

function NewFestivalPrayerWGData:SetAllPrayerInfoUpdate(protocol)
	self.prayer_count = protocol.prayer_count
	self.advanced_prayer_flag = protocol.advanced_prayer_flag
	self.prayer_buy_limit = protocol.prayer_buy_limit
	self.reward_flag = protocol.reward_flag

	self:UpdateRewardShowInfo()
end

function NewFestivalPrayerWGData:SetPrayerTaskUpdate(protocol)
	self.pray_task_flag = protocol.pray_task_flag
	local data = protocol.change_data
	if self.prayer_task_process[data.task_id] then
		self.prayer_task_process[data.task_id] = data.one_task_process
		self:UpdatSingleShowTaskList(data.task_id)
	end
end

function NewFestivalPrayerWGData:GetGradeOpenDayCfg()
	return self.open_day_cfg[self.grade]
end

function NewFestivalPrayerWGData:GetGradeRewardCfg()
	return self.prayer_reward_cfg[self.grade]
end

function NewFestivalPrayerWGData:GetGradeTaskCfg()
	return self.task_list_cfg[self.grade]
end

function NewFestivalPrayerWGData:GetModelInfo()
	return self.model_show_cfg[self.grade]
end

function NewFestivalPrayerWGData:GetPrayerCount()
	return self.prayer_count
end

function NewFestivalPrayerWGData:GetIsBuyAdvancedFlag()
	return self.advanced_prayer_flag == 1
end

function NewFestivalPrayerWGData:GetRewardFlagBySeq(seq)
	return self.reward_flag[seq] and self.reward_flag[seq]== 1
end

function NewFestivalPrayerWGData:GetAllRewardFlag()
	return self.reward_flag
end

function NewFestivalPrayerWGData:GetPrayerButTimes(seq)
	return self.prayer_buy_limit[seq]
end

function NewFestivalPrayerWGData:GetTaskProcessById(task_id)
	return self.prayer_task_process[task_id]
end

function NewFestivalPrayerWGData:GetRewardShowInfo()
	if not self.prayer_reward_list then
		self:UpdateRewardShowInfo()
	end

	return self.prayer_reward_list
end

function NewFestivalPrayerWGData:UpdateRewardShowInfo()
	self.prayer_reward_list = {}
	local cfg = self:GetGradeRewardCfg()
	if IsEmptyTable(cfg) then
		return self.prayer_reward_list
	end

	local is_buy_advanced = self:GetIsBuyAdvancedFlag()
	for i = 0, #cfg do
		local cur_info = cfg[i]
		local data = {}
		data.cfg = cur_info
		data.seq = cur_info.seq
		data.reward_flag = self.reward_flag[cur_info.seq] or 0
		data.next_prayer_count = cfg[i + 1] and cfg[i].guding_mark ~= 1 and cfg[i + 1].prayer_count or 0
		local is_can_get = false
		if self.prayer_count >= cfg[i].prayer_count then
			is_can_get = is_buy_advanced and data.reward_flag < NEWYEAR_PRAYER_REWARD_FLAG.GET_ADVANCED or 
						data.reward_flag < NEWYEAR_PRAYER_REWARD_FLAG.GET_NORMAL
		end

		data.can_get_remind = is_can_get
		table.insert(self.prayer_reward_list, data)
	end
end

function NewFestivalPrayerWGData:GetGuDingReward()
	for k, v in ipairs(self.prayer_reward_list) do
		if v.cfg and v.cfg.guding_mark == 1 then
			return v
		end
	end

	return {}
end

function NewFestivalPrayerWGData:UpdatAllShowTaskList()
	local task_list = self:GetGradeTaskCfg()
	if not task_list then
		return
	end

	for k, v in pairs(task_list) do
		local task_id = v.task_id
		local data = self:GetOneTaskDataBySeq(task_id)
		if data then
			self.show_all_task_list[task_id] = data
		end
	end
end

function NewFestivalPrayerWGData:UpdatSingleShowTaskList(task_id)
	local data = self:GetOneTaskDataBySeq(task_id)
	if data then
		self.show_all_task_list[task_id] = data
	end
end

function NewFestivalPrayerWGData:GetOneTaskDataBySeq(task_id)
	local task_list = self:GetGradeTaskCfg()
	if not task_list then
		return
	end

	local data = {}
	local cfg = task_list[task_id]
	if not cfg then
		return nil
	end

	data.task_id = task_id
	data.cfg = cfg
	data.task_process = self:GetTaskProcessById(task_id) or 0
	data.task_flag = self.pray_task_flag[task_id] or 0
	if data.task_flag == 1 then
		data.task_status = REWARD_STATE_TYPE.FINISH
		data.sort_index = 1000
	elseif data.task_flag == 0 and data.task_process >= cfg.param1 then	
		data.task_status = REWARD_STATE_TYPE.CAN_FETCH
		data.sort_index = 10
	else
		data.task_status = REWARD_STATE_TYPE.UNDONE
		data.sort_index = 100
	end

	return data
end

function NewFestivalPrayerWGData:GetTaskListByType(reset_type)
	local cur_reset_type = reset_type or NewFestivalPrayerTaskType.Daily
	local show_task_list = {}
	for k, v in pairs(self.show_all_task_list) do
		if v.cfg and v.cfg.reset_type == cur_reset_type then
			table.insert(show_task_list, v)
		end
	end

	if not IsEmptyTable(show_task_list) then
		table.sort(show_task_list, SortTools.KeyLowerSorters("sort_index", "task_id"))
	end

	return show_task_list
end

function NewFestivalPrayerWGData:GetTaskTypeIsCanFetch(reset_type)
	if not reset_type then
		return false
	end

	local task_list = self:GetTaskListByType(reset_type)
	for k, v in pairs(task_list) do
		if v.task_status == REWARD_STATE_TYPE.CAN_FETCH then
			return true
		end
	end

	return false
end

function NewFestivalPrayerWGData:GetPrayerBuyList()
	local buy_list = {}
	local prayer_buy_cfg = self.prayer_buy_cfg[self.grade]
	if IsEmptyTable(prayer_buy_cfg) then
		return buy_list
	end

	for i = 0, #prayer_buy_cfg do
		local data = {}
		local cfg = prayer_buy_cfg[i]
		data.cfg = cfg
		data.seq = cfg.seq
		data.buy_times = self:GetPrayerButTimes(data.seq)
		data.is_tuijian = i == #prayer_buy_cfg
		table.insert(buy_list, data)
	end

	return buy_list
end

function NewFestivalPrayerWGData:GetAllPrayerReward()
	if self.normal_reward_list == nil or self.advanced_reward_list == nil then
		self.normal_reward_list = {}
		self.advanced_reward_list = {}

		local cfg = self:GetGradeRewardCfg()
		if IsEmptyTable(cfg) then
			return self.prayer_reward_list
		end

		for k, v in pairs(cfg) do
			if v.normal_reward[0] then
				table.insert(self.normal_reward_list, v.normal_reward[0])
			end
	
			for index, info in pairs(v.advanced_reward) do
				table.insert(self.advanced_reward_list, info)
			end
		end
	end

	return self.normal_reward_list, self.advanced_reward_list
end

--------------红点-----------------------
function NewFestivalPrayerWGData:IsShowPrayerRedPoint()
	if self:GetPrayerTaskRed() then
		return 1
	elseif self:GetPrayerRewardRed() then
		return 1
	end

	return 0
end

function NewFestivalPrayerWGData:GetPrayerRewardRed()
	if IsEmptyTable(self.prayer_reward_list) then
		return false
	end

	for k, v in ipairs(self.prayer_reward_list) do
		if v.can_get_remind then
			return true
		end
	end

	return false
end

function NewFestivalPrayerWGData:GetPrayerTaskRed()
	for k, v in pairs(self.show_all_task_list) do
		if v.task_status == REWARD_STATE_TYPE.CAN_FETCH then
			return true
		end
	end

	return false
end