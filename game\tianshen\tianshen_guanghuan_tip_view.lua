TianShenGuangHuanTipView = TianShenGuangHuanTipView or BaseClass(SafeBaseView)

local JuanZhouOpenWidth = 375
local JuanZhouCloseWidth = 10

function TianShenGuangHuanTipView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_ts_guanghuan_tip")
end

function TianShenGuangHuanTipView:ReleaseCallBack()
	if self.ts_head_list then
		self.ts_head_list:DeleteMe()
		self.ts_head_list = nil
	end

	self:TSGHTipDelTimer()
end

function TianShenGuangHuanTipView:LoadCallBack()
	if not self.ts_head_list then
		self.ts_head_list = AsyncListView.New(TianShenGuangHuanTipRender, self.node_list["ts_head_list"])
	end
end

function TianShenGuangHuanTipView:CloseCallBack()
end

function TianShenGuangHuanTipView:SetShowInfo(union_id_list)
	self.union_id_list = union_id_list or {}
end

function TianShenGuangHuanTipView:OnFlush(prarm_t)
	local union_id = table.remove(self.union_id_list, 1)
	if not union_id or union_id < 0 then
		self:CheckNeedClose()
		return
	end

	local ts_index_info = TianShenWGData.Instance:GetUnionSkillTSList(union_id)
	if IsEmptyTable(ts_index_info) then
		self:CheckNeedClose()
		return
	end
	self:TSGHTipResetAnim()
	self:TSGHTipDelTimer()
	self.auto_close_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.ViewAnimation,self), 3)--5秒自动关闭
    self.auto_tail_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.ShowTailEffect,self), 2.8)--5秒自动关闭

	self.ts_head_list:SetDataList(ts_index_info)
	self.node_list["light_effect"]:SetActive(true)
end

function TianShenGuangHuanTipView:TSGHTipResetAnim()
	self.node_list["root"]:SetActive(true)
	self.node_list["tail_effect"]:SetActive(false)
	self.node_list["light_effect"]:SetActive(false)
	self.node_list["root"].canvas_group.alpha = 1
	self.node_list["juanzhou_bg"].transform.localPosition = Vector3(-1500, 20, 0)
	self.node_list["ts_head_list"].canvas_group.alpha = 1
	self.node_list.juanzhou_bg.rect.sizeDelta = Vector2(1332, 396)
	self.node_list.title.transform.localScale = Vector3(0, 0, 0)

	local bg_tween = self.node_list.juanzhou_bg.rect:DOAnchorPosX(0, 0.25)
	bg_tween:SetEase(DG.Tweening.Ease.OutExpo)
	local title_tween = self.node_list.title.rect:DOScale(Vector3(1, 1, 1), 0.5)
	title_tween:SetEase(DG.Tweening.Ease.OutBack)
end

function TianShenGuangHuanTipView:TSGHTipDelTimer()
	if self.auto_close_timer then
        GlobalTimerQuest:CancelQuest(self.auto_close_timer)
        self.auto_close_timer = nil
    end

    if self.auto_tail_timer then
        GlobalTimerQuest:CancelQuest(self.auto_tail_timer)
        self.auto_tail_timer = nil
    end
end

function TianShenGuangHuanTipView:ShowTailEffect()
	self.node_list["tail_effect"]:SetActive(true)
end

function TianShenGuangHuanTipView:ViewAnimation()
	if not self:IsLoaded() then
		return
	end
	self.node_list["light_effect"]:SetActive(false)

	self:CheckNeedClose()
end

function TianShenGuangHuanTipView:CheckNeedClose()
	--检测是否有下一个激活信息
	if not IsEmptyTable(self.union_id_list) then
		if self.node_list["root"] then
			self.node_list["root"]:SetActive(false)
		end
		self:Flush()
	else
		self:Close()
	end
end

--天神头像---------------------------TianShenGuangHuanTipRender start
TianShenGuangHuanTipRender = TianShenGuangHuanTipRender or BaseClass(BaseRender)
function TianShenGuangHuanTipRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	local base_cfg = TianShenWGData.Instance:GetTianShenCfg(data.ts_index)
	local ts_item_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(data.ts_index)
	if IsEmptyTable(base_cfg) or IsEmptyTable(ts_item_cfg) then
		return
	end
	
	local bundle, asset = ResPath.GetNoPackPNG("ts_" .. data.ts_index)
	self.node_list.head_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.head_icon.image:SetNativeSize()
 	end)

	self.node_list["head_bg"].image:LoadSprite(ResPath.GetCommonImages(TianShenWGData.TianShenQualityImg[base_cfg.series]))

	local color = ItemWGData.Instance:GetItemColor(ts_item_cfg.act_item_id)
	self.node_list["name"].text.text = ToColorStr(base_cfg.bianshen_name or "", color)
end

-----------------------------TianShenGuangHuanTipRender end