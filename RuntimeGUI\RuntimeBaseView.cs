﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RuntimeBaseView
{
    private bool isOpening = false;
    protected string viewName;
    protected bool isBlock = true;
    protected bool isHadCloseBtn = true;
    protected int windowsId = 0;

    public RuntimeBaseView(string viewName)
    {
        this.viewName = viewName;
        RuntimeViewMgr.Instance.RegisterView(viewName, this);
    }

    public void Open()
    {
        if (!isOpening)
        {
            isOpening = true;
            this.OpenCallback();
        }
    }

    public void Close()
    {
        if (isOpening)
        {
            isOpening = false;
            this.CloseCallback();
        }
    }

    public bool IsOpening()
    {
        return isOpening;
    }

    public void OnGUI()
    {
        if (!isOpening)return;

        var windowRect = this.GetWindowRect();
       GUI.Window(windowsId, windowRect, OnWindowsFun, this.viewName);
        if (isBlock)
        {
            RuntimeGUIMgr.Instance.GetGUIBlock().ShowRect(windowRect, 1);
        }
    }

    virtual protected Rect GetWindowRect()
    {
        return new Rect((Screen.width - 700) * 0.5f, (Screen.height - 300) * 0.5f, 700, 400);
    }

    private void OnWindowsFun(int windowid)
    {
        this.OnReapintWindow(windowid);

        if (isHadCloseBtn)
        {
            if (GUILayout.Button("关闭窗口", GUILayout.MinWidth(0), GUILayout.MaxHeight(50)))
            {
                RuntimeViewMgr.Instance.CloseView(this.viewName);
            }
        }
    }

    virtual protected void OnReapintWindow(int windowid)
    {

    }

    virtual protected void OpenCallback()
    {

    }

    virtual protected void CloseCallback()
    {

    }

    virtual public void OnGameStop()
    {

    }

    virtual public void OnApplicationQuit()
    {

    }
}
