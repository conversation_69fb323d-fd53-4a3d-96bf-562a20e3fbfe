HmGodWGData = HmGodWGData or BaseClass()

function HmGodWGData:__init()
	if HmGodWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[HmGodWGData] attempt to create singleton twice!")
		return
	end

	HmGodWGData.Instance = self
	RemindManager.Instance:Register(RemindName.HmGodView, BindTool.Bind(self.GetRemindAndSuit, self))
end

function HmGodWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.HmGodView)
	HmGodWGData.Instance = nil
end

-- 展示列表
function HmGodWGData:UpdateSuitShowList()
	self.item_show_list = {}

	local all_list = WardrobeWGData.Instance:GetSuitAllList()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if all_list then
		for suit, suit_data in pairs(all_list) do
			local is_show = WardrobeWGData.Instance:IsShowSuitList(suit)
			if server_day >= suit_data.open_day or is_show then
				if suit_data.theme_type == WARDROBE_THEME_TYPE.HMGOD then
					self:InsertOtherData(suit, suit_data)
					self.item_show_list[#self.item_show_list + 1] = suit_data
				end
			end
		end
	end

	if not IsEmptyTable(self.item_show_list) then
		table.sort(self.item_show_list, SortTools.KeyLowerSorter("sort_index"))
	end
end

function HmGodWGData:InsertOtherData(suit, suit_data)
	--插一個总体数据
	local suit_one_data = { 
		suit_seq = suit,
		type = 0,
		show_item_id = 0,
		is_extra = true,
	}

	if not suit_data.hm_is_insert_data then
		table.insert(suit_data.part_list, 0, suit_one_data)
		suit_data.hm_is_insert_data = true
	end

	-- if suit_data.part_list[0] then
	-- 	local part_data = suit_data.part_list[0]
	-- 	if part_data.type ~= 0 and part_data.show_item_id ~= 0 then
	-- 		table.insert(suit_data.part_list, 0, suit_one_data)
	-- 	end
	-- else
	-- 	table.insert(suit_data.part_list, 0, suit_one_data)
	-- end
end

function HmGodWGData:GetSuitShowList()
	-- if not self.item_show_list or #self.item_show_list <= 0 then
	-- 	self:UpdateSuitShowList()
	-- end
	self:UpdateSuitShowList()
	return self.item_show_list
end

function HmGodWGData:GetHmSuitInfoBySuit(suit)
	local show_suit_info = {}
	for i, v in ipairs(self.item_show_list) do
		if v.suit == suit then
			show_suit_info = v
			-- data_index  = i
			break
		end
	end

	return show_suit_info
end


function HmGodWGData:GetJumpHmGodRemindIndex()
    local group_index = 1
    local item_index = 0
    local cfg = self:GetSuitShowList()
    for i, v in ipairs(cfg) do
    	if v.can_act then
    		for k, t in ipairs(v.part_list) do
    			if t.state ~= REWARD_STATE_TYPE.UNDONE then
    				item_index = k
    				break
    			end
    		end

    		group_index = i
    		break
    	end
    end
 
    return group_index, item_index
end

function HmGodWGData:GetRemindAndSuit()
	local cfg = self:GetSuitShowList()
	local length = #cfg
	for i = 1, length do
		if cfg[i].can_act then
			return 1
		end
	end

	return 0
end