﻿using LuaInterface;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ToLuaReRegister
{
    public static void Register(LuaState luaState)
    {
        luaState.BeginModule(null);  //start global

        luaState.BeginModule("UnityEngine");
        UnityEngine_ObjectReWrap.Register(luaState);
        luaState.EndModule();

        ToLuaProfileReWrap.Register(luaState);

        luaState.EndModule(); //end global
    }
}
