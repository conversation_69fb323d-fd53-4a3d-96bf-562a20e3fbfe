MengLingView = MengLingView or BaseClass(SafeBaseView)

function MengLingView:__init()
    self.full_screen = true
    self.is_big_view = true
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
    self.default_index = TabIndex.mengling_bag

    local mengling_bundle_name = "uis/view/mengling_ui_prefab"
    local common_bundle_name = "uis/view/common_panel_prefab"
    self:AddViewResource(0, common_bundle_name, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.mengling_bag, mengling_bundle_name, "layout_mengling_bag_view")
    self:AddViewResource(TabIndex.mengling_strong, mengling_bundle_name, "layout_mengling_strong_view")
    self:AddViewResource(TabIndex.mengling_suit, mengling_bundle_name, "layout_mengling_suit_view")
	self:AddViewResource({TabIndex.mengling_bag, TabIndex.mengling_strong, TabIndex.mengling_suit}, mengling_bundle_name, "layout_mengling_common_left_view")
	self:AddViewResource(TabIndex.mengling_compose, mengling_bundle_name, "layout_mengling_compose_view")
	self:AddViewResource(0, common_bundle_name, "VerticalTabbar")
    self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")

    self.tab_sub = {}

	self.remind_tab = {
        {RemindName.MengLingBag}, 
		{RemindName.MengLingStrong},
		{RemindName.MengLingSuit},
		{RemindName.MengLingCompose},
	}
end

function MengLingView:LoadCallBack()
    if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.MengLing.TabGrop, self.tab_sub, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true, show_bind_gold = true,
			show_coin = true, show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	local bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg4")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.MengLingView, self.tabbar)
end

function MengLingView:LoadIndexCallBack(index)
	if index == TabIndex.mengling_bag then
		self:InitMengLIngCommonLeftView()
		self:InitMengLingBagPanel()
    elseif index == TabIndex.mengling_strong then
		self:InitMengLIngCommonLeftView()
		self:InitMengLingStrengthPanel()
    elseif index == TabIndex.mengling_suit then
		self:InitMengLIngCommonLeftView()
		self:InitMengLingSuitPanel()
	elseif index == TabIndex.mengling_compose then
		self:InitMengLingComposeView()
	end
end

function MengLingView:ReleaseCallBack()
    if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:ReleaseCommonLeftViewCallBack()
	self:DeleteMengLingBagPanel()
	self:ReleaseMengLingSuitPanel()
	self:DeleteMengLingStrengthPanel()
	self:ReleaseMengLingComposeView()
end

function MengLingView:ShowIndexCallBack(index)
	self:CheckShowResolveBtn(index)
end

function MengLingView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
            if index == TabIndex.mengling_bag then
				self:OnFlushMengLingCommonView()
				self:FlushMengLingBagPanel()
            elseif index == TabIndex.mengling_strong then
				self:OnFlushMengLingCommonView()
				self:FlushMengLingStrengthPanel()
            elseif index == TabIndex.mengling_suit then
				self:OnFlushMengLingCommonView()
				self:OnFlushMengLingSuitPanel()
			elseif index == TabIndex.mengling_compose then
				self:OnFlushMengLingComposeView()
            end
		elseif "view_operate_effect" then
			if index == TabIndex.mengling_bag then
				self:OnFlushMengLingCommonView()
				self:FlushMengLingBagPanel()
			end

			if v.active_skill then
				self:DoUnLockSkillEffect()
			end

			if v.active_data then
				self:DoUnLockSuitEffect(v.active_data)
			end
		end
	end
end

function MengLingView:OnClickGoToBtn()
    ViewManager.Instance:Open(GuideModuleName.PositionalWarfareView)
end
