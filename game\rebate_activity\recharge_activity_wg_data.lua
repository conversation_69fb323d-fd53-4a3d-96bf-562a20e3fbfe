RechargeActivityWGData = RechargeActivityWGData or BaseClass()

function RechargeActivityWGData:__init()
    if nil ~= RechargeActivityWGData.Instance then
        ErrorLog("[RechargeActivityWGData]:Attempt to create singleton twice!")
    end
    RechargeActivityWGData.Instance = self
    self.recharge_value = 0
    self.reward_flag = {}
    -- self:InitConfig()
    --[[
    RemindManager.Instance:Register(RemindName.Rebate_ZhiGou, BindTool.Bind(self.IsShowRebateZhiGouRedPoint, self))
    RebateActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.REBATE_ZHIGOU_FANLI, {[1] = MERGE_EVENT_TYPE.LEVEL},
    	BindTool.Bind(self.GetActIsOpen, self))
    self.global_day_change = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind1(self.RechargeActivityDay<PERSON>hange, self))
    --]]
end

function RechargeActivityWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.Rebate_ZhiGou)
    if self.global_day_change then
        GlobalEventSystem:UnBind(self.global_day_change)
        self.global_day_change = nil
    end

    RechargeActivityWGData.Instance = nil
end

function RechargeActivityWGData:InitConfig()
    self.zhichong_cfg = ConfigManager.Instance:GetAutoConfig("zhichong_cfg_auto")
    self.reward_day_cfg = ListToMap(self.zhichong_cfg.reward_cfg, "day_id")
    self.zhichong_other_cfg = self.zhichong_cfg.other_cfg[1]
end

function RechargeActivityWGData:CheckNeedCloseTab()
    if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.REBATE_ZHIGOU_FANLI) then
        if self:CheckAllRebateRewardIsGet() then
            ActivityWGData.Instance:SetRebateActivityActState(ACTIVITY_TYPE.REBATE_ZHIGOU_FANLI, ACTIVITY_STATUS.CLOSE)
        end
    end
end

function RechargeActivityWGData:GetActIsOpen()
    if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.REBATE_ZHIGOU_FANLI) then
        local role_level = GameVoManager.Instance:GetMainRoleVo().level
        local level = RebateActivityWGData.Instance:GetTabOpenLevelByAct(ACTIVITY_TYPE.REBATE_ZHIGOU_FANLI) --标签开启等级
        local open_level = self.zhichong_other_cfg.open_level
        if open_level and role_level >= open_level and role_level >= level then
            if self:CheckAllRebateRewardIsGet() then
                return false
            end
            return true
        end
        return false
    end
    return false
end

function RechargeActivityWGData:GetRebateListData()
    return self.reward_day_cfg and self.reward_day_cfg or {}
end

function RechargeActivityWGData:IsShowRebateZhiGouRedPoint()
    if not self:GetActIsOpen() then
        return 0
    end
    if not self.reward_day_cfg or IsEmptyTable(self.reward_day_cfg) then 
        return 0
    end

    for k,v in pairs(self.reward_day_cfg) do
       if self:GetThisDayCanGet(v.day_id) == 1 then
           return 1
       end
    end
    return 0
end

function RechargeActivityWGData:OnSCActZhiChongInfo(protocol)
   self.recharge_value = protocol.recharge_value
   self.reward_flag = bit:d2b(protocol.reward_flag)
end

function RechargeActivityWGData:GetNeedRechargeCount()
    return self.zhichong_other_cfg.recharge_value or 0
end

function RechargeActivityWGData:GetIsEnoughRecharge()
    local need_count = self:GetNeedRechargeCount()
    if need_count > 0 then
        return self.recharge_value >= need_count
    end
    return false
end

-- -1充值不足  0天数不足  1可领取  2已领取
function RechargeActivityWGData:GetThisDayCanGet(day_id)
    if not self:GetIsEnoughRecharge() then
        return -1
    end
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    if cur_day >= day_id then
        day_id = day_id - 1
        if self.reward_flag[32-day_id] and self.reward_flag[32-day_id] == 0 then
            return 1
        else
            return 2
        end
    else
        return 0
    end
end

function RechargeActivityWGData:GetAlreadyRecharge()
    return self.recharge_value or 0
end

function RechargeActivityWGData:GetRebateRechargeRule()
    return self.zhichong_other_cfg.message or "" 
end

function RechargeActivityWGData:RechargeActivityDayChange()
    RemindManager.Instance:Fire(RemindName.Rebate_ZhiGou)
end

function RechargeActivityWGData:CheckAllRebateRewardIsGet()
    for k,v in pairs(self.reward_day_cfg) do
       if self:GetThisDayCanGet(v.day_id) ~= 2 then
           return false
       end
    end
    return true
end