CheckMem = {}

local WhiteList = {}
local Start = false
local Step = 0.016
local GlobalObjHashSet = {}
local ObjChildrenHashSet = {}
local ModuleObjHashSet = {}
local UpValueHashSet = {}
local ConfigList = {}
local obj_info_list = {}
local new_global_obj_list = {}
local last_time = 0

local IgnoreGlobalCheckKeys = {
    ["GlobalEventSystem"] = true,
    ["GlobalTimerQuest"] = true,
    ["Play"] = true,
    ["IS_MERGE_SERVER"] = true,
    ["MainCameraSnapshot"] = true,
    ["MainCameraFollow"] = true,
    ["GlobalLocalRoleId"] = true,
    ["GlobalLocalRoleName"] = true,
    ["IS_ON_CROSSSERVER"] = true,
    ["IS_AUDIT_VERSION"] = true,
	["MainCamera"] = true,
}

-- 初始化全局变量
function CheckMem:InitGlobal()
	for k,v in pairs(_G) do
		GlobalObjHashSet[k] = true
	end
end

function CheckMem:Start()
	for k,v in pairs(_G) do
		if v ~= _G and k ~= "Play" and k ~= "ListToMapConfig" and k ~= "GlobalClassType" then
			if type(v) == "table" then
				table.insert(ObjChildrenHashSet, {k = k, v = v})
			end
		end
	end

	for k,v in pairs(_G["Play"].module_list) do
		local class_name = GetClassName(v._class_type)
		if class_name == "ModulesWGCtrl" then
			for k2,v2 in pairs(v.ctrl_list) do
				table.insert(ModuleObjHashSet, {k = k2, v = v2})
			end
		elseif class_name == "ConfigManager" then
			table.insert(ConfigList, v.cfg_list)
			table.insert(ConfigList, v.persistent_cfg_list)
		else
			table.insert(ObjChildrenHashSet, {k = class_name, v = v})
		end
	end

	local hash_set = {}
	for k,v in pairs(_G["GlobalClassType"]) do
		local class_name = GetClassName(k)
		table.insert(ObjChildrenHashSet, {k = class_name or tostring(k), v = v})

		for _, v2 in pairs(v) do
			if type(v2) == "function" then
				for i = 1, 999 do
					local upname, upvalue = debug.getupvalue(v2, i)
					if nil == upname or nil == upvalue or _G == upvalue then
						break
					end

					if type(upvalue) == "table" then
						if not hash_set[upvalue] then
							hash_set[upvalue] = true
							local info = debug.getinfo(v2, "Su")
							-- 配置表文件排除
							if "config/config_manager" == info.short_src then
								break
							end

							local str_name = string.format("[%s]@file:%s 中的局部变量[%s]", class_name, info.short_src, upname)
							table.insert(UpValueHashSet, {k = str_name or tostring(k), v = upvalue})
						end
					end
				end
			end
		end
	end

	for k,v in pairs(_G) do
		if v ~= _G and k ~= "Play" and k ~= "ListToMapConfig" and k ~= "GlobalClassType" then
			if type(v) == "table" then
				for k2, v2 in pairs(v) do
					if type(v2) == "function" then
						for i = 1, 999 do
							local upname, upvalue = debug.getupvalue(v2, i)
							if nil == upname or nil == upvalue or _G == upvalue then
								break
							end

							if type(upvalue) == "table" then
								if not hash_set[upvalue] then
									hash_set[upvalue] = true
									local info = debug.getinfo(v2, "Su")
									-- 配置表文件排除
									if "config/config_manager" ~= info.short_src and "systool/baseclass" ~= info.short_src
										and not string.find(info.short_src, "editor") then
										local str_name = string.format("[%s]@file:%s 中的局部变量[%s]", k, info.short_src, upname)
										table.insert(UpValueHashSet, {k = str_name or tostring(k), v = upvalue})
									end
								end
							end
						end
					end
				end
			end
		end
	end

	Start = true
end

function InitWhiteList()
	local list = require("editor.check_mem_white_list")
	for k,v in pairs(list) do
		local splits = Split(v, "%.")
		local path = nil
		for k2,v2 in ipairs(splits) do
			path = nil == path and v2 or path .. " => " .. v2
		end
		table.insert(WhiteList, path)
	end
end

local index = 0
local global_obj_checked_list = {}
local global_obj_index = 1
local module_obj_index = 1
local module_obj_checked_list = {}
local upvalue_obj_index = 1
local upvalue_obj_checked_list = {}
local last_report_time_stamp = 0

function CheckMem:Update(now_time, elapse_time)
	if not Start then
		return
	end

	if now_time - last_time < Step then
		return
	end
	last_time = now_time
	index = index + 1

	if index % 3 == 0 then
		-- 检查global table泄漏
		for i = 1, 10 do
			-- self:CheckGlobalObjChildrenCount()
		end
	elseif index % 3 == 1 then
		-- 检查module table泄漏
		-- self:CheckModuleObjChildrenCount()
	elseif index % 3 == 2 then
		-- 检查upvalue泄漏
		-- self:CheckUpValueObjChildrenCount()
	end

	if index % 64 == 0 then
		-- 检查全局变量
		self:CheckGlobal(now_time)
	end

	-- if now_time >= last_report_time_stamp then
	-- 	last_report_time_stamp = now_time + 10
	-- 	local msg = string.format("[CheckMem] GlobalObj:%0.1f ModuleObj:%0.1f UpValueObj:%0.1f",
	-- 		global_obj_index / #ObjChildrenHashSet * 100, module_obj_index / #ModuleObjHashSet * 100, upvalue_obj_index / #UpValueHashSet * 100)
	-- 	table.insert(new_global_obj_list, msg)
	-- end

	self:OutputReport()
end

local last_check_global_time = 0
function CheckMem:CheckGlobal(now_time)
	if now_time - last_check_global_time < 1 then
		return
	end
	last_check_global_time = now_time

	for k,v in pairs(_G) do
		if nil == GlobalObjHashSet[k] then
			if not IgnoreGlobalCheckKeys[k] then
				local msg
				if "function" == type(v) then
					local cDInfo = debug.getinfo(v, "Su")
					local cCombinedName = "  @file:" .. cDInfo.short_src .. "[line:" .. tostring(cDInfo.linedefined) .. "]"
					msg = "不要定义全局函数!请使用loacl定义  函数名：" .. k .. cCombinedName
				else
					msg = "禁止声明全局变量!请使用loacl声明  变量名：" .. k
				end
				table.insert(new_global_obj_list, msg)
			end
			GlobalObjHashSet[k] = true
		end
	end
end

local function CheckBigTable(info, str_name)
	local children_count = info[str_name].children_count
	if children_count > 100 then
		local msg = string.format("[%s]的长度太长: %s", str_name, children_count)
		table.insert(new_global_obj_list, msg)
	end
	for k,v in pairs(info[str_name].children) do
		if v ~= "" then
			CheckBigTable(info, v)
		end
	end
end

function CheckMem:CheckGlobalObjChildrenCount()
	local obj = ObjChildrenHashSet[global_obj_index]
	if nil ~= obj then
		local new_info = {}
		local class_name = obj.k
		self:GetChildrenCount(obj.v, new_info, class_name, global_obj_checked_list)
		obj_info_list[obj.v] = obj_info_list[obj.v] or {}
		local alert_list = {}
		self:CompareMemInfo(obj_info_list[obj.v], new_info, class_name, alert_list)
		self:SaveMemInfo(obj_info_list[obj.v], new_info, class_name)
		global_obj_index = global_obj_index + 1

		if #alert_list > 0 then
			local msg = string.format("[%s]可能存在内存泄漏风险!!", class_name)
			table.insert(new_global_obj_list, msg)
			for k,v in ipairs(alert_list) do
				table.insert(new_global_obj_list, v)
				-- 只显示最近的3条
				if k > 3 then
					break
				end
			end
		end
	else
		global_obj_index = 1
		global_obj_checked_list = {}
	end
end

function CheckMem:CheckModuleObjChildrenCount()
	local obj = ModuleObjHashSet[module_obj_index]
	if nil ~= obj then
		local new_info = {}
		local class_name = GetClassName(obj.v._class_type)
		local total_count = self:GetChildrenCount(obj.v, new_info, class_name, module_obj_checked_list)
		-- if total_count > 10000 then
		-- 	print_error(class_name, total_count)
		-- 	CheckBigTable(new_info, class_name)
		-- end
		obj_info_list[obj.v] = obj_info_list[obj.v] or {}
		local alert_list = {}
		self:CompareMemInfo(obj_info_list[obj.v], new_info, class_name, alert_list)
		self:SaveMemInfo(obj_info_list[obj.v], new_info, class_name)
		module_obj_index = module_obj_index + 1

		if #alert_list > 0 then
			local msg = string.format("[%s]可能存在内存泄漏风险!!", class_name)
			table.insert(new_global_obj_list, msg)
			for k,v in ipairs(alert_list) do
				table.insert(new_global_obj_list, v)
				-- 只显示最近的3条
				if k > 3 then
					break
				end
			end
		end
	else
		module_obj_index = 1
		module_obj_checked_list = {}
	end
end

function CheckMem:CheckUpValueObjChildrenCount()
	local obj = UpValueHashSet[upvalue_obj_index]
	if nil ~= obj then
		local new_info = {}
		local class_name = obj.k
		local total_count = self:GetChildrenCount(obj.v, new_info, class_name, upvalue_obj_checked_list)

		obj_info_list[obj.v] = obj_info_list[obj.v] or {}
		local alert_list = {}
		self:CompareMemInfo(obj_info_list[obj.v], new_info, class_name, alert_list)
		self:SaveMemInfo(obj_info_list[obj.v], new_info, class_name)
		upvalue_obj_index = upvalue_obj_index + 1

		if #alert_list > 0 then
			local msg = string.format("[%s]可能存在内存泄漏风险!!", class_name)
			table.insert(new_global_obj_list, msg)
			for k,v in ipairs(alert_list) do
				table.insert(new_global_obj_list, v)
				-- 只显示最近的3条
				if k > 3 then
					break
				end
			end
		end
	else
		upvalue_obj_index = 1
		upvalue_obj_checked_list = {}
	end
end

local ConfigHashSet = {}
local function BuildConfig()
	for _, list in pairs(ConfigList) do
		for k,v in pairs(list) do
			if nil == ConfigHashSet[v] then
				local list = {v}
				local index = 1
				while index <= #list do
					local cfg = list[index]
					ConfigHashSet[cfg] = true
					for k2,v2 in pairs(cfg) do
						if type(v2) == "table" then
							table.insert(list, v2)
						end
					end
					index = index + 1
				end
			end
		end
	end

	for k,v in pairs(ListToMapConfig) do
		ConfigHashSet[v] = true
	end
end

local function GetIsConfig(tbl)
	if nil == ConfigHashSet[tbl] then
		local metable = getmetatable(tbl)
		if metable then
			if rawget(metable, "__get_key_values") then
				ConfigHashSet[tbl] = true
			end
		end
	end

	if nil == ConfigHashSet[tbl] then
		BuildConfig()
	end

	if nil == ConfigHashSet[tbl] then
		ConfigHashSet[tbl] = false
	end

	return ConfigHashSet[tbl]
end

local WhiteListHashSet = {}
local function GetIsWhiteList(name, root)
	if nil ~= WhiteListHashSet[root] then
		return WhiteListHashSet[root]
	end

	if nil == WhiteListHashSet[name] then
		for k,v in pairs(WhiteList) do
			if 1 == string.find(name, v) then
				WhiteListHashSet[name] = true
				WhiteListHashSet[root] = true
				break
			end
		end
	end

	if nil == WhiteListHashSet[name] then
		WhiteListHashSet[name] = false
	end

	return WhiteListHashSet[name]
end

function CheckMem:GetChildrenCount(root, mem_info, str_name, hash_set)
	if type(root) ~= "table" or GetIsConfig(root) then
		return 1
	end

	hash_set[root] = true
	mem_info[str_name] = {children_count = 0, children = {}}
	if GetIsWhiteList(str_name, root) then
		return 0
	end

	local total_count = 0
	for k,v in pairs(root) do
		if nil == hash_set[v] then
			local name = ""
			local _type = type(v)
			if _type == "table" and not GetIsConfig(v) then
				name = string.format("%s => %s", str_name, k)
			end
			mem_info[str_name].children[k] = {name = name, _type = _type}
			mem_info[str_name].children_count = mem_info[str_name].children_count + 1
			total_count = total_count + self:GetChildrenCount(v, mem_info, name, hash_set)
		end
		total_count = total_count + 1
	end
	return total_count
end

local compare_list = {}
local function ShouldAlert(old_info, new_info, str_name)
	compare_list[str_name] = compare_list[str_name] or 0
	if old_info[str_name].children_count < new_info[str_name].children_count then
		compare_list[str_name] = compare_list[str_name] + 1
	elseif old_info[str_name].children_count > new_info[str_name].children_count then
		compare_list[str_name] = 0
	end

	-- 最近5次都是增加，且数组类型一致，则弹出警告
	if compare_list[str_name] >= 5 then
		compare_list[str_name] = 0

		local last_type = nil
		local is_same_type = true
		for k,v in pairs(new_info[str_name].children) do
			local _type = v._type
			last_type = last_type or _type
			if last_type ~= _type then
				is_same_type = false
				break
			end
		end

		if is_same_type then
			return true
		end
	end
	return false
end

function CheckMem:CompareMemInfo(old_info, new_info, str_name, alert_list)
	if nil ~= old_info[str_name] then
		local need_alert = ShouldAlert(old_info, new_info, str_name)
		local flag = false
		for k, v in pairs(new_info[str_name].children) do
			if nil == old_info[str_name].children[k] then
				if need_alert then
					if not flag then
						local msg = string.format("[%s]的长度一直在增长!!! 最新长度：%s", str_name, new_info[str_name].children_count)
						table.insert(alert_list, msg)
						flag = true
					end
					local msg = string.format("新增的key：%s => %s", str_name, k)
					table.insert(alert_list, msg)
				end
			elseif v.name ~= "" then
				self:CompareMemInfo(old_info, new_info, v.name, alert_list)
			end
		end
	end
end

function CheckMem:SaveMemInfo(old_info, new_info, str_name)
	if str_name == nil then
		return
	end
	if nil == old_info[str_name] then
		old_info[str_name] = {children_count = 0, children = {}}
	end
	old_info[str_name].children_count = math.max(old_info[str_name].children_count, new_info[str_name].children_count)
	for k,v in pairs(new_info[str_name].children) do
		old_info[str_name].children[k] = v
		if v.name ~= "" then
			self:SaveMemInfo(old_info, new_info, v.name)
		end
	end
end

function CheckMem:OutputReport()
	if #new_global_obj_list > 0 then
		for _, msg in pairs(new_global_obj_list) do
			UnityEngine.Debug.LogError(msg)
		end

		new_global_obj_list = {}
	end
end

InitWhiteList()

return CheckMem