-- F-副本-组队通用BOSS.xls
local item_table={
[1]={item_id=65588,num=60,is_bind=0},
[2]={item_id=22532,num=1,is_bind=1},
[3]={item_id=36437,num=60,is_bind=1},
[4]={item_id=36437,num=90,is_bind=1},
[5]={item_id=36437,num=120,is_bind=1},
[6]={item_id=36437,num=150,is_bind=1},
[7]={item_id=63008,num=1,is_bind=1},
[8]={item_id=63006,num=1,is_bind=1},
[9]={item_id=63005,num=3,is_bind=1},
[10]={item_id=26393,num=1,is_bind=1},
[11]={item_id=26392,num=4,is_bind=1},
[12]={item_id=26391,num=11,is_bind=1},
[13]={item_id=30808,num=1,is_bind=1},
[14]={item_id=30795,num=1,is_bind=1},
[15]={item_id=30447,num=2,is_bind=1},
[16]={item_id=30443,num=5,is_bind=1},
[17]={item_id=22532,num=3,is_bind=1},
[18]={item_id=22531,num=3,is_bind=1},
[19]={item_id=90050,num=1,is_bind=0},
[20]={item_id=63006,num=2,is_bind=1},
[21]={item_id=63005,num=4,is_bind=1},
[22]={item_id=26393,num=2,is_bind=1},
[23]={item_id=26392,num=5,is_bind=1},
[24]={item_id=26391,num=15,is_bind=1},
[25]={item_id=26391,num=12,is_bind=1},
[26]={item_id=63005,num=2,is_bind=1},
[27]={item_id=26392,num=3,is_bind=1},
[28]={item_id=26391,num=8,is_bind=1},
[29]={item_id=30795,num=2,is_bind=1},
[30]={item_id=30447,num=5,is_bind=1},
[31]={item_id=30443,num=10,is_bind=1},
[32]={item_id=22532,num=5,is_bind=1},
[33]={item_id=22531,num=5,is_bind=1},
[34]={item_id=30447,num=4,is_bind=1},
[35]={item_id=30443,num=8,is_bind=1},
[36]={item_id=22532,num=4,is_bind=1},
[37]={item_id=22531,num=4,is_bind=1},
[38]={item_id=30443,num=7,is_bind=1},
[39]={item_id=30447,num=3,is_bind=1},
[40]={item_id=44510,num=1,is_bind=1},
[41]={item_id=65588,num=30,is_bind=0},
[42]={item_id=30530,num=1,is_bind=1},
[43]={item_id=36437,num=30,is_bind=1},
[44]={item_id=36437,num=50,is_bind=1},
[45]={item_id=36437,num=80,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
fb={
{leader_show_reward_item={[0]=item_table[1],[1]=item_table[2]},leader_pass_reward_item={[0]=item_table[3],[1]=item_table[4],[2]=item_table[5],[3]=item_table[6]},},
{seq=1100,team_type=11,level_limit=210,cap_limit=10000000,scene_id=8053,show_reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11],[5]=item_table[12]},title_desc="妖魔鬼怪，魑魅魍魉，历经百难终成神",fb_desc="<color=#FFFFFF00>空格</color>临仙城神女，昔日为大战魔族不惜以身试道，祭出一双神目窥见一丝天机。此后奉圣主之神息，祈临仙之福。千年后误入临仙城民突现狂躁之症，喃喃呓语...真相扑朔。<color=#DDB085>",task_added_reward_item={[0]=item_table[7]},},
{seq=1200,team_type=12,level_limit=300,open_day_limit=10,cap_limit=15000000,scene_id=8054,show_reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[16],[4]=item_table[17],[5]=item_table[18]},title_desc="山岳藏金木，瀚海蕴水火",fb_desc="<color=#FFFFFF00>空格</color>上古山海五行失衡，异兽肆虐。唯有身负武魂传承之人，可引动金锋、巨木、玄水、离火、厚土之力，深入秘境降伏妖兽，重定五行，唤醒沉睡的远古武魂之力。<color=#DDB085>",task_added_reward_item={[0]=item_table[13]},}
},

fb_meta_table_map={
},
team_type={
{},
{team_type=11,},
{team_type=12,}
},

team_type_meta_table_map={
},
stage={
{fb_seq=1000,},
{fb_seq=1000,stage=2,time=90,param0=102,},
{stage=3,param0=103,},
{fb_seq=1000,stage=4,time=120,param0=104,},
{stage=5,time=-1,type=2,desc="打开玉虚宝藏",},
{type=3,param0=52000,param1=0,desc="了解临仙详情",},
{stage=2,param0=201,},
{stage=3,param0=52001,desc="调查奇怪的人",},
{stage=4,param0=202,},
{stage=5,time=90,param0=203,},
{stage=6,time=-1,type=2,desc="拾取女神之心",},
{stage=7,time=-1,param0=52002,desc="击败所有怪物进入下一阶段",},
{stage=8,time=120,param0=204,},
{stage=9,param0=52003,desc="找到清醒的女神",},
{fb_seq=1200,type=3,param0=53000,param1=0,},
{fb_seq=1200,param0=301,},
{fb_seq=1200,param0=302,},
{stage=4,time=120,param0=53001,},
{stage=5,param0=301,},
{stage=6,type=2,param0=302,},
{fb_seq=1200,stage=7,time=120,param0=303,},
{stage=8,type=2,}
},

stage_meta_table_map={
[3]=1,	-- depth:1
[17]=3,	-- depth:2
[16]=2,	-- depth:1
[20]=21,	-- depth:1
[19]=20,	-- depth:2
[11]=9,	-- depth:1
[5]=1,	-- depth:1
[14]=6,	-- depth:1
[22]=21,	-- depth:1
[12]=6,	-- depth:1
[18]=15,	-- depth:1
[8]=12,	-- depth:2
},
monster={
{},
{seq=102,monster_id=11000,monster_num=1,},
{seq=103,monster_id=11003,},
{seq=104,monster_id=11002,},
{seq=201,monster_id=7000,},
{seq=202,monster_id=7001,},
{seq=203,monster_id=7002,},
{seq=204,monster_id=7003,},
{seq=301,monster_id=7004,},
{seq=302,monster_id=7005,},
{seq=303,monster_id=7006,}
},

monster_meta_table_map={
[4]=2,	-- depth:1
[7]=2,	-- depth:1
[8]=2,	-- depth:1
[11]=2,	-- depth:1
},
gather={
{seq=101,},
{seq=102,},
{},
{index=1,},
{seq=202,gather_id=3000,},
{seq=301,gather_id=3001,},
{seq=302,gather_id=3002,},
{seq=303,gather_id=3003,}
},

gather_meta_table_map={
},
pass_reward={
{},
{pass_time=120,desc="<color=#99FFBB>%s</color>秒内完成所有阶段可达成SS通关",star_num=2,},
{pass_time=180,desc="<color=#99FFBB>%s</color>秒内完成所有阶段可达成S通关",star_num=1,},
{pass_time=300,desc="<color=#99FFBB>%s</color>秒内完成所有阶段可达成A通关",star_num=0,},
{fb_seq=1100,pass_reward_item={[0]=item_table[19],[1]=item_table[7],[2]=item_table[20],[3]=item_table[21],[4]=item_table[22],[5]=item_table[23],[6]=item_table[24]},leader_pass_reward_item={[0]=item_table[7],[1]=item_table[20],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23],[5]=item_table[24]},help_reward_item={[0]=item_table[7],[1]=item_table[20],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23],[5]=item_table[24]},},
{fb_seq=1100,pass_reward_item={[0]=item_table[19],[1]=item_table[7],[2]=item_table[20],[3]=item_table[9],[4]=item_table[22],[5]=item_table[11],[6]=item_table[25]},leader_pass_reward_item={[0]=item_table[7],[1]=item_table[20],[2]=item_table[9],[3]=item_table[22],[4]=item_table[11],[5]=item_table[25]},help_reward_item={[0]=item_table[7],[1]=item_table[20],[2]=item_table[9],[3]=item_table[22],[4]=item_table[11],[5]=item_table[25]},},
{fb_seq=1100,pass_reward_item={[0]=item_table[19],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11],[6]=item_table[12]},leader_pass_reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11],[5]=item_table[12]},help_reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11],[5]=item_table[12]},},
{fb_seq=1100,pass_reward_item={[0]=item_table[19],[1]=item_table[8],[2]=item_table[26],[3]=item_table[10],[4]=item_table[27],[5]=item_table[28]},leader_pass_reward_item={[0]=item_table[8],[1]=item_table[26],[2]=item_table[10],[3]=item_table[27],[4]=item_table[28]},help_reward_item={[0]=item_table[8],[1]=item_table[26],[2]=item_table[10],[3]=item_table[27],[4]=item_table[28]},},
{fb_seq=1200,pass_time=150,pass_reward_item={[0]=item_table[19],[1]=item_table[13],[2]=item_table[29],[3]=item_table[30],[4]=item_table[31],[5]=item_table[32],[6]=item_table[33]},leader_pass_reward_item={[0]=item_table[13],[1]=item_table[29],[2]=item_table[30],[3]=item_table[31],[4]=item_table[32],[5]=item_table[33]},help_reward_item={[0]=item_table[13],[1]=item_table[29],[2]=item_table[30],[3]=item_table[31],[4]=item_table[32],[5]=item_table[33]},},
{fb_seq=1200,pass_time=210,pass_reward_item={[0]=item_table[19],[1]=item_table[13],[2]=item_table[29],[3]=item_table[34],[4]=item_table[35],[5]=item_table[36],[6]=item_table[37]},leader_pass_reward_item={[0]=item_table[13],[1]=item_table[29],[2]=item_table[34],[3]=item_table[35],[4]=item_table[36],[5]=item_table[37]},help_reward_item={[0]=item_table[13],[1]=item_table[29],[2]=item_table[34],[3]=item_table[35],[4]=item_table[36],[5]=item_table[37]},desc="<color=#99FFBB>%s</color>秒内完成所有阶段可达成SS通关",star_num=2,},
{fb_seq=1200,pass_time=330,pass_reward_item={[0]=item_table[19],[1]=item_table[13],[2]=item_table[14],[3]=item_table[34],[4]=item_table[38],[5]=item_table[36],[6]=item_table[37]},leader_pass_reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[34],[3]=item_table[38],[4]=item_table[36],[5]=item_table[37]},help_reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[34],[3]=item_table[38],[4]=item_table[36],[5]=item_table[37]},desc="<color=#99FFBB>%s</color>秒内完成所有阶段可达成S通关",star_num=1,},
{fb_seq=1200,pass_time=500,pass_reward_item={[0]=item_table[19],[1]=item_table[13],[2]=item_table[14],[3]=item_table[39],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18]},leader_pass_reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[39],[3]=item_table[16],[4]=item_table[17],[5]=item_table[18]},help_reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[39],[3]=item_table[16],[4]=item_table[17],[5]=item_table[18]},desc="<color=#99FFBB>%s</color>秒内完成所有阶段可达成A通关",star_num=0,}
},

pass_reward_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
npc_task={
{},
{npc_id=10162,},
{npc_id=52000,task_talk_str="（n）我仅在外围略作探查，似乎有不少人有了狂乱之症，但要究其根本还需你深入镇中再行调查。||（p）待我一探究竟，你法力甚弱担心受到影响还是不太过靠近。",other_talk_str="听说这里的人信仰古怪，也不知道这疯病是否有关。",},
{npc_id=52001,task_talk_str="（n）我等俗人，贪暖思物，祈巫祝之法，涤我俗尘，得羽化登仙...||（p）快清醒一点！（抓住摇晃）",other_talk_str="成神...成神...巫祝大人...",},
{npc_id=52002,task_talk_str="（n）止步！惊扰神女者，巫祝——必将降下神罚。||（p）巫祝已经挨打逃跑了，劝你也赶紧离开！",other_talk_str="永久的守护神女是我的职责，速速退下！",},
{npc_id=52003,task_talk_str="（n）我沉睡已久，不曾想是巫祝将我困于此地。他想利用我的神力铸造他的傀偶...||（p）附近的居民已深受影响，神行癫狂之状。||（n）无妨，待我恢复一下元气后降下纯净之雪即可。",other_talk_str="临仙百神...已不复存在",},
{npc_id=53000,task_talk_str="（n）此劫乃关乎山海，五行失衡。且需你去寻找离火、玄水和厚土之力。||（n）秘境内危险重重你且小心。",other_talk_str="五行之力内藏洪荒",},
{npc_id=53001,task_talk_str="（n）五行之力？我似在前方右所见过，但。。后土之力有武魂镇守，需要你越过它才可获取。||（p）为山海稳固，此番不试不行。",other_talk_str="附近的异兽越来越多了",}
},

npc_task_meta_table_map={
},
monster_attr={
{},
{min_level=2,max_level=2,},
{min_level=3,max_level=3,},
{min_level=4,max_level=4,},
{min_level=5,max_level=5,},
{min_level=6,max_level=6,},
{min_level=7,max_level=7,},
{min_level=8,max_level=8,},
{min_level=9,max_level=9,},
{min_level=10,max_level=10,},
{min_level=11,max_level=11,},
{min_level=12,max_level=12,},
{min_level=13,max_level=13,},
{min_level=14,max_level=14,},
{min_level=15,max_level=15,},
{min_level=16,max_level=16,},
{min_level=17,max_level=17,},
{min_level=18,max_level=18,},
{min_level=19,max_level=19,},
{min_level=20,max_level=20,},
{min_level=21,max_level=21,},
{min_level=22,max_level=22,},
{min_level=23,max_level=23,},
{min_level=24,max_level=24,},
{min_level=25,max_level=25,},
{min_level=26,max_level=26,},
{min_level=27,max_level=27,},
{min_level=28,max_level=28,},
{min_level=29,max_level=29,},
{min_level=30,max_level=30,},
{min_level=31,max_level=31,},
{min_level=32,max_level=32,},
{min_level=33,max_level=33,},
{min_level=34,max_level=34,},
{min_level=35,max_level=35,},
{min_level=36,max_level=36,},
{min_level=37,max_level=37,},
{min_level=38,max_level=38,},
{min_level=39,max_level=39,},
{min_level=40,max_level=40,},
{min_level=41,max_level=41,},
{min_level=42,max_level=42,},
{min_level=43,max_level=43,},
{min_level=44,max_level=44,},
{min_level=45,max_level=45,},
{min_level=46,max_level=46,},
{min_level=47,max_level=47,},
{min_level=48,max_level=48,},
{min_level=49,max_level=49,},
{min_level=50,max_level=50,},
{min_level=51,max_level=51,},
{min_level=52,max_level=52,},
{min_level=53,max_level=53,},
{min_level=54,max_level=54,},
{min_level=55,max_level=55,},
{min_level=56,max_level=56,},
{min_level=57,max_level=57,},
{min_level=58,max_level=58,},
{min_level=59,max_level=59,},
{min_level=60,max_level=60,},
{min_level=61,max_level=61,},
{min_level=62,max_level=62,},
{min_level=63,max_level=63,},
{min_level=64,max_level=64,},
{min_level=65,max_level=65,},
{min_level=66,max_level=66,},
{min_level=67,max_level=67,},
{min_level=68,max_level=68,},
{min_level=69,max_level=69,},
{min_level=70,max_level=70,},
{min_level=71,max_level=71,},
{min_level=72,max_level=72,},
{min_level=73,max_level=73,},
{min_level=74,max_level=74,},
{min_level=75,max_level=75,},
{min_level=76,max_level=76,},
{min_level=77,max_level=77,},
{min_level=78,max_level=78,},
{min_level=79,max_level=79,},
{min_level=80,max_level=80,},
{min_level=81,max_level=81,},
{min_level=82,max_level=82,},
{min_level=83,max_level=83,},
{min_level=84,max_level=84,},
{min_level=85,max_level=85,},
{min_level=86,max_level=86,},
{min_level=87,max_level=87,},
{min_level=88,max_level=88,},
{min_level=89,max_level=89,},
{min_level=90,max_level=90,},
{min_level=91,max_level=91,},
{min_level=92,max_level=92,},
{min_level=93,max_level=93,},
{min_level=94,max_level=94,},
{min_level=95,max_level=95,},
{min_level=96,max_level=96,},
{min_level=97,max_level=97,},
{min_level=98,max_level=98,},
{min_level=99,max_level=99,},
{min_level=100,max_level=100,},
{min_level=101,max_level=101,},
{min_level=102,max_level=102,},
{min_level=103,max_level=103,},
{min_level=104,max_level=104,},
{min_level=105,max_level=105,},
{min_level=106,max_level=106,},
{min_level=107,max_level=107,},
{min_level=108,max_level=108,},
{min_level=109,max_level=109,},
{min_level=110,max_level=110,},
{min_level=111,max_level=111,},
{min_level=112,max_level=112,},
{min_level=113,max_level=113,},
{min_level=114,max_level=114,},
{min_level=115,max_level=115,},
{min_level=116,max_level=116,},
{min_level=117,max_level=117,},
{min_level=118,max_level=118,},
{min_level=119,max_level=119,},
{min_level=120,max_level=120,},
{min_level=121,max_level=121,},
{min_level=122,max_level=122,},
{min_level=123,max_level=123,},
{min_level=124,max_level=124,},
{min_level=125,max_level=125,},
{min_level=126,max_level=126,},
{min_level=127,max_level=127,},
{min_level=128,max_level=128,},
{min_level=129,max_level=129,},
{min_level=130,max_level=130,},
{min_level=131,max_level=131,},
{min_level=132,max_level=132,},
{min_level=133,max_level=133,},
{min_level=134,max_level=134,},
{min_level=135,max_level=135,},
{min_level=136,max_level=136,},
{min_level=137,max_level=137,},
{min_level=138,max_level=138,},
{min_level=139,max_level=139,},
{min_level=140,max_level=140,},
{min_level=141,max_level=141,},
{min_level=142,max_level=142,},
{min_level=143,max_level=143,},
{min_level=144,max_level=144,},
{min_level=145,max_level=145,},
{min_level=146,max_level=146,},
{min_level=147,max_level=147,},
{min_level=148,max_level=148,},
{min_level=149,max_level=149,},
{min_level=150,max_level=150,},
{min_level=151,max_level=151,},
{min_level=152,max_level=152,},
{min_level=153,max_level=153,},
{min_level=154,max_level=154,},
{min_level=155,max_level=155,},
{min_level=156,max_level=156,},
{min_level=157,max_level=157,},
{min_level=158,max_level=158,},
{min_level=159,max_level=159,},
{min_level=160,max_level=160,},
{min_level=161,max_level=161,},
{min_level=162,max_level=162,},
{min_level=163,max_level=163,},
{min_level=164,max_level=164,},
{min_level=165,max_level=165,},
{min_level=166,max_level=166,},
{min_level=167,max_level=167,},
{min_level=168,max_level=168,},
{min_level=169,max_level=169,},
{min_level=170,max_level=170,},
{min_level=171,max_level=171,},
{min_level=172,max_level=172,},
{min_level=173,max_level=173,},
{min_level=174,max_level=174,},
{min_level=175,max_level=175,},
{min_level=176,max_level=176,},
{min_level=177,max_level=177,},
{min_level=178,max_level=178,},
{min_level=179,max_level=179,},
{min_level=180,max_level=180,},
{min_level=181,max_level=181,},
{min_level=182,max_level=182,},
{min_level=183,max_level=183,},
{min_level=184,max_level=184,},
{min_level=185,max_level=185,},
{min_level=186,max_level=186,},
{min_level=187,max_level=187,},
{min_level=188,max_level=188,},
{min_level=189,max_level=189,},
{min_level=190,max_level=190,},
{min_level=191,max_level=191,},
{min_level=192,max_level=192,},
{min_level=193,max_level=193,},
{min_level=194,max_level=194,},
{min_level=195,max_level=195,},
{min_level=196,max_level=196,},
{min_level=197,max_level=197,},
{min_level=198,max_level=198,},
{min_level=199,max_level=199,},
{min_level=200,max_level=200,},
{min_level=201,max_level=201,},
{min_level=202,max_level=202,},
{min_level=203,max_level=203,},
{min_level=204,max_level=204,},
{min_level=205,max_level=205,},
{min_level=206,max_level=206,},
{min_level=207,max_level=207,},
{min_level=208,max_level=208,},
{min_level=209,max_level=209,},
{min_level=210,max_level=210,},
{min_level=211,max_level=211,},
{min_level=212,max_level=212,},
{min_level=213,max_level=213,},
{min_level=214,max_level=214,},
{min_level=215,max_level=215,},
{min_level=216,max_level=216,},
{min_level=217,max_level=217,},
{min_level=218,max_level=218,},
{min_level=219,max_level=219,},
{min_level=220,max_level=220,},
{min_level=221,max_level=221,},
{min_level=222,max_level=222,},
{min_level=223,max_level=223,},
{min_level=224,max_level=224,},
{min_level=225,max_level=225,},
{min_level=226,max_level=226,},
{min_level=227,max_level=227,},
{min_level=228,max_level=228,},
{min_level=229,max_level=229,},
{min_level=230,max_level=230,},
{min_level=231,max_level=231,},
{min_level=232,max_level=232,},
{min_level=233,max_level=233,},
{min_level=234,max_level=234,},
{min_level=235,max_level=235,},
{min_level=236,max_level=236,},
{min_level=237,max_level=237,},
{min_level=238,max_level=238,},
{min_level=239,max_level=239,},
{min_level=240,max_level=240,},
{min_level=241,max_level=241,},
{min_level=242,max_level=242,},
{min_level=243,max_level=243,},
{min_level=244,max_level=244,},
{min_level=245,max_level=245,},
{min_level=246,max_level=246,},
{min_level=247,max_level=247,},
{min_level=248,max_level=248,},
{min_level=249,max_level=249,},
{min_level=250,max_level=250,},
{min_level=251,max_level=251,},
{min_level=252,max_level=252,},
{min_level=253,max_level=253,},
{min_level=254,max_level=254,},
{min_level=255,max_level=255,},
{min_level=256,max_level=256,},
{min_level=257,max_level=257,},
{min_level=258,max_level=258,},
{min_level=259,max_level=259,},
{min_level=260,max_level=260,},
{min_level=261,max_level=261,},
{min_level=262,max_level=262,},
{min_level=263,max_level=263,},
{min_level=264,max_level=264,},
{min_level=265,max_level=265,},
{min_level=266,max_level=266,},
{min_level=267,max_level=267,},
{min_level=268,max_level=268,},
{min_level=269,max_level=269,},
{min_level=270,max_level=270,},
{min_level=271,max_level=271,},
{min_level=272,max_level=272,},
{min_level=273,max_level=273,},
{min_level=274,max_level=274,},
{min_level=275,max_level=275,},
{min_level=276,max_level=276,},
{min_level=277,max_level=277,},
{min_level=278,max_level=278,},
{min_level=279,max_level=279,},
{min_level=280,max_level=280,},
{min_level=281,max_level=281,},
{min_level=282,max_level=282,},
{min_level=283,max_level=283,},
{min_level=284,max_level=284,},
{min_level=285,max_level=285,},
{min_level=286,max_level=286,},
{min_level=287,max_level=287,},
{min_level=288,max_level=288,},
{min_level=289,max_level=289,},
{min_level=290,max_level=290,},
{min_level=291,max_level=291,},
{min_level=292,max_level=292,},
{min_level=293,max_level=293,},
{min_level=294,max_level=294,},
{min_level=295,max_level=295,},
{min_level=296,max_level=296,},
{min_level=297,max_level=297,},
{min_level=298,max_level=298,},
{min_level=299,max_level=299,},
{min_level=300,max_level=300,},
{min_level=301,max_level=301,},
{min_level=302,max_level=302,},
{min_level=303,max_level=303,},
{min_level=304,max_level=304,},
{min_level=305,max_level=305,},
{min_level=306,max_level=306,},
{min_level=307,max_level=307,},
{min_level=308,max_level=308,},
{min_level=309,max_level=309,},
{min_level=310,max_level=310,},
{min_level=311,max_level=311,},
{min_level=312,max_level=312,},
{min_level=313,max_level=313,},
{min_level=314,max_level=314,},
{min_level=315,max_level=315,},
{min_level=316,max_level=316,},
{min_level=317,max_level=317,},
{min_level=318,max_level=318,},
{min_level=319,max_level=319,},
{min_level=320,max_level=320,},
{min_level=321,max_level=321,},
{min_level=322,max_level=322,},
{min_level=323,max_level=323,},
{min_level=324,max_level=324,},
{min_level=325,max_level=325,},
{min_level=326,max_level=326,},
{min_level=327,max_level=327,},
{min_level=328,max_level=328,},
{min_level=329,max_level=329,},
{min_level=330,max_level=330,},
{min_level=331,max_level=331,},
{min_level=332,max_level=332,},
{min_level=333,max_level=333,},
{min_level=334,max_level=334,},
{min_level=335,max_level=335,},
{min_level=336,max_level=336,},
{min_level=337,max_level=337,},
{min_level=338,max_level=338,},
{min_level=339,max_level=339,},
{min_level=340,max_level=340,},
{min_level=341,max_level=341,},
{min_level=342,max_level=342,},
{min_level=343,max_level=343,},
{min_level=344,max_level=344,},
{min_level=345,max_level=345,},
{min_level=346,max_level=346,},
{min_level=347,max_level=347,},
{min_level=348,max_level=348,},
{min_level=349,max_level=349,},
{min_level=350,max_level=350,},
{min_level=351,max_level=351,},
{min_level=352,max_level=352,},
{min_level=353,max_level=353,},
{min_level=354,max_level=354,},
{min_level=355,max_level=355,},
{min_level=356,max_level=356,},
{min_level=357,max_level=357,},
{min_level=358,max_level=358,},
{min_level=359,max_level=359,},
{min_level=360,max_level=360,},
{min_level=361,max_level=361,},
{min_level=362,max_level=362,},
{min_level=363,max_level=363,},
{min_level=364,max_level=364,},
{min_level=365,max_level=365,},
{min_level=366,max_level=366,},
{min_level=367,max_level=367,},
{min_level=368,max_level=368,},
{min_level=369,max_level=369,},
{min_level=370,max_level=370,},
{min_level=371,max_level=371,},
{min_level=372,max_level=372,},
{min_level=373,max_level=373,},
{min_level=374,max_level=374,},
{min_level=375,max_level=375,},
{min_level=376,max_level=376,},
{min_level=377,max_level=377,},
{min_level=378,max_level=378,},
{min_level=379,max_level=379,},
{min_level=380,max_level=380,},
{min_level=381,max_level=381,},
{min_level=382,max_level=382,},
{min_level=383,max_level=383,},
{min_level=384,max_level=384,},
{min_level=385,max_level=385,},
{min_level=386,max_level=386,},
{min_level=387,max_level=387,},
{min_level=388,max_level=388,},
{min_level=389,max_level=389,},
{min_level=390,max_level=390,},
{min_level=391,max_level=391,},
{min_level=392,max_level=392,},
{min_level=393,max_level=393,},
{min_level=394,max_level=394,},
{min_level=395,max_level=395,},
{min_level=396,max_level=396,},
{min_level=397,max_level=397,},
{min_level=398,max_level=398,},
{min_level=399,max_level=399,},
{min_level=400,max_level=400,},
{min_level=401,max_level=401,},
{min_level=402,max_level=402,},
{min_level=403,max_level=403,},
{min_level=404,max_level=404,},
{min_level=405,max_level=405,},
{min_level=406,max_level=406,},
{min_level=407,max_level=407,},
{min_level=408,max_level=408,},
{min_level=409,max_level=409,},
{min_level=410,max_level=410,},
{min_level=411,max_level=411,},
{min_level=412,max_level=412,},
{min_level=413,max_level=413,},
{min_level=414,max_level=414,},
{min_level=415,max_level=415,},
{min_level=416,max_level=416,},
{min_level=417,max_level=417,},
{min_level=418,max_level=418,},
{min_level=419,max_level=419,},
{min_level=420,max_level=420,},
{min_level=421,max_level=421,},
{min_level=422,max_level=422,},
{min_level=423,max_level=423,},
{min_level=424,max_level=424,},
{min_level=425,max_level=425,},
{min_level=426,max_level=426,},
{min_level=427,max_level=427,},
{min_level=428,max_level=428,},
{min_level=429,max_level=429,},
{min_level=430,max_level=430,},
{min_level=431,max_level=431,},
{min_level=432,max_level=432,},
{min_level=433,max_level=433,},
{min_level=434,max_level=434,},
{min_level=435,max_level=435,},
{min_level=436,max_level=436,},
{min_level=437,max_level=437,},
{min_level=438,max_level=438,},
{min_level=439,max_level=439,},
{min_level=440,max_level=440,},
{min_level=441,max_level=441,},
{min_level=442,max_level=442,},
{min_level=443,max_level=443,},
{min_level=444,max_level=444,},
{min_level=445,max_level=445,},
{min_level=446,max_level=446,},
{min_level=447,max_level=447,},
{min_level=448,max_level=448,},
{min_level=449,max_level=449,},
{min_level=450,max_level=450,},
{min_level=451,max_level=451,},
{min_level=452,max_level=452,},
{min_level=453,max_level=453,},
{min_level=454,max_level=454,},
{min_level=455,max_level=455,},
{min_level=456,max_level=456,},
{min_level=457,max_level=457,},
{min_level=458,max_level=458,},
{min_level=459,max_level=459,},
{min_level=460,max_level=460,},
{min_level=461,max_level=461,},
{min_level=462,max_level=462,},
{min_level=463,max_level=463,},
{min_level=464,max_level=464,},
{min_level=465,max_level=465,},
{min_level=466,max_level=466,},
{min_level=467,max_level=467,},
{min_level=468,max_level=468,},
{min_level=469,max_level=469,},
{min_level=470,max_level=470,},
{min_level=471,max_level=471,},
{min_level=472,max_level=472,},
{min_level=473,max_level=473,},
{min_level=474,max_level=474,},
{min_level=475,max_level=475,},
{min_level=476,max_level=476,},
{min_level=477,max_level=477,},
{min_level=478,max_level=478,},
{min_level=479,max_level=479,},
{min_level=480,max_level=480,},
{min_level=481,max_level=481,},
{min_level=482,max_level=482,},
{min_level=483,max_level=483,},
{min_level=484,max_level=484,},
{min_level=485,max_level=485,},
{min_level=486,max_level=486,},
{min_level=487,max_level=487,},
{min_level=488,max_level=488,},
{min_level=489,max_level=489,},
{min_level=490,max_level=490,},
{min_level=491,max_level=491,},
{min_level=492,max_level=492,},
{min_level=493,max_level=493,},
{min_level=494,max_level=494,},
{min_level=495,max_level=495,},
{min_level=496,max_level=496,},
{min_level=497,max_level=497,},
{min_level=498,max_level=498,},
{min_level=499,max_level=499,},
{min_level=500,max_level=500,},
{min_level=501,max_level=501,},
{min_level=502,max_level=502,},
{min_level=503,max_level=503,},
{min_level=504,max_level=504,},
{min_level=505,max_level=505,},
{min_level=506,max_level=506,},
{min_level=507,max_level=507,},
{min_level=508,max_level=508,},
{min_level=509,max_level=509,},
{min_level=510,max_level=510,},
{min_level=511,max_level=511,},
{min_level=512,max_level=512,},
{min_level=513,max_level=513,},
{min_level=514,max_level=514,},
{min_level=515,max_level=515,},
{min_level=516,max_level=516,},
{min_level=517,max_level=517,},
{min_level=518,max_level=518,},
{min_level=519,max_level=519,},
{min_level=520,max_level=520,},
{min_level=521,max_level=521,},
{min_level=522,max_level=522,},
{min_level=523,max_level=523,},
{min_level=524,max_level=524,},
{min_level=525,max_level=525,},
{min_level=526,max_level=526,},
{min_level=527,max_level=527,},
{min_level=528,max_level=528,},
{min_level=529,max_level=529,},
{min_level=530,max_level=530,},
{min_level=531,max_level=531,},
{min_level=532,max_level=532,},
{min_level=533,max_level=533,},
{min_level=534,max_level=534,},
{min_level=535,max_level=535,},
{min_level=536,max_level=536,},
{min_level=537,max_level=537,},
{min_level=538,max_level=538,},
{min_level=539,max_level=539,},
{min_level=540,max_level=540,},
{min_level=541,max_level=541,},
{min_level=542,max_level=542,},
{min_level=543,max_level=543,},
{min_level=544,max_level=544,},
{min_level=545,max_level=545,},
{min_level=546,max_level=546,},
{min_level=547,max_level=547,},
{min_level=548,max_level=548,},
{min_level=549,max_level=549,},
{min_level=550,max_level=550,},
{min_level=551,max_level=551,},
{min_level=552,max_level=552,},
{min_level=553,max_level=553,},
{min_level=554,max_level=554,},
{min_level=555,max_level=555,},
{min_level=556,max_level=556,},
{min_level=557,max_level=557,},
{min_level=558,max_level=558,},
{min_level=559,max_level=559,},
{min_level=560,max_level=560,},
{min_level=561,max_level=561,},
{min_level=562,max_level=562,},
{min_level=563,max_level=563,},
{min_level=564,max_level=564,},
{min_level=565,max_level=565,},
{min_level=566,max_level=566,},
{min_level=567,max_level=567,},
{min_level=568,max_level=568,},
{min_level=569,max_level=569,},
{min_level=570,max_level=570,},
{min_level=571,max_level=571,},
{min_level=572,max_level=572,},
{min_level=573,max_level=573,},
{min_level=574,max_level=574,},
{min_level=575,max_level=575,},
{min_level=576,max_level=576,},
{min_level=577,max_level=577,},
{min_level=578,max_level=578,},
{min_level=579,max_level=579,},
{min_level=580,max_level=580,},
{min_level=581,max_level=581,},
{min_level=582,max_level=582,},
{min_level=583,max_level=583,},
{min_level=584,max_level=584,},
{min_level=585,max_level=585,},
{min_level=586,max_level=586,},
{min_level=587,max_level=587,},
{min_level=588,max_level=588,},
{min_level=589,max_level=589,},
{min_level=590,max_level=590,},
{min_level=591,max_level=591,},
{min_level=592,max_level=592,},
{min_level=593,max_level=593,},
{min_level=594,max_level=594,},
{min_level=595,max_level=595,},
{min_level=596,max_level=596,},
{min_level=597,max_level=597,},
{min_level=598,max_level=598,},
{min_level=599,max_level=599,},
{min_level=600,max_level=600,},
{min_level=601,max_level=601,},
{min_level=602,max_level=602,},
{min_level=603,max_level=603,},
{min_level=604,max_level=604,},
{min_level=605,max_level=605,},
{min_level=606,max_level=606,},
{min_level=607,max_level=607,},
{min_level=608,max_level=608,},
{min_level=609,max_level=609,},
{min_level=610,max_level=610,},
{min_level=611,max_level=611,},
{min_level=612,max_level=612,},
{min_level=613,max_level=613,},
{min_level=614,max_level=614,},
{min_level=615,max_level=615,},
{min_level=616,max_level=616,},
{min_level=617,max_level=617,},
{min_level=618,max_level=618,},
{min_level=619,max_level=619,},
{min_level=620,max_level=620,},
{min_level=621,max_level=621,},
{min_level=622,max_level=622,},
{min_level=623,max_level=623,},
{min_level=624,max_level=624,},
{min_level=625,max_level=625,},
{min_level=626,max_level=626,},
{min_level=627,max_level=627,},
{min_level=628,max_level=628,},
{min_level=629,max_level=629,},
{min_level=630,max_level=630,},
{min_level=631,max_level=631,},
{min_level=632,max_level=632,},
{min_level=633,max_level=633,},
{min_level=634,max_level=634,},
{min_level=635,max_level=635,},
{min_level=636,max_level=636,},
{min_level=637,max_level=637,},
{min_level=638,max_level=638,},
{min_level=639,max_level=639,},
{min_level=640,max_level=640,},
{min_level=641,max_level=641,},
{min_level=642,max_level=642,},
{min_level=643,max_level=643,},
{min_level=644,max_level=644,},
{min_level=645,max_level=645,},
{min_level=646,max_level=646,},
{min_level=647,max_level=647,},
{min_level=648,max_level=648,},
{min_level=649,max_level=649,},
{min_level=650,max_level=650,},
{min_level=651,max_level=651,},
{min_level=652,max_level=652,},
{min_level=653,max_level=653,},
{min_level=654,max_level=654,},
{min_level=655,max_level=655,},
{min_level=656,max_level=656,},
{min_level=657,max_level=657,},
{min_level=658,max_level=658,},
{min_level=659,max_level=659,},
{min_level=660,max_level=660,},
{min_level=661,max_level=661,},
{min_level=662,max_level=662,},
{min_level=663,max_level=663,},
{min_level=664,max_level=664,},
{min_level=665,max_level=665,},
{min_level=666,max_level=666,},
{min_level=667,max_level=667,},
{min_level=668,max_level=668,},
{min_level=669,max_level=669,},
{min_level=670,max_level=670,},
{min_level=671,max_level=671,},
{min_level=672,max_level=672,},
{min_level=673,max_level=673,},
{min_level=674,max_level=674,},
{min_level=675,max_level=675,},
{min_level=676,max_level=676,},
{min_level=677,max_level=677,},
{min_level=678,max_level=678,},
{min_level=679,max_level=679,},
{min_level=680,max_level=680,},
{min_level=681,max_level=681,},
{min_level=682,max_level=682,},
{min_level=683,max_level=683,},
{min_level=684,max_level=684,},
{min_level=685,max_level=685,},
{min_level=686,max_level=686,},
{min_level=687,max_level=687,},
{min_level=688,max_level=688,},
{min_level=689,max_level=689,},
{min_level=690,max_level=690,},
{min_level=691,max_level=691,},
{min_level=692,max_level=692,},
{min_level=693,max_level=693,},
{min_level=694,max_level=694,},
{min_level=695,max_level=695,},
{min_level=696,max_level=696,},
{min_level=697,max_level=697,},
{min_level=698,max_level=698,},
{min_level=699,max_level=699,},
{min_level=700,max_level=700,},
{min_level=701,max_level=701,},
{min_level=702,max_level=702,},
{min_level=703,max_level=703,},
{min_level=704,max_level=704,},
{min_level=705,max_level=705,},
{min_level=706,max_level=706,},
{min_level=707,max_level=707,},
{min_level=708,max_level=708,},
{min_level=709,max_level=709,},
{min_level=710,max_level=710,},
{min_level=711,max_level=711,},
{min_level=712,max_level=712,},
{min_level=713,max_level=713,},
{min_level=714,max_level=714,},
{min_level=715,max_level=715,},
{min_level=716,max_level=716,},
{min_level=717,max_level=717,},
{min_level=718,max_level=718,},
{min_level=719,max_level=719,},
{min_level=720,max_level=720,},
{min_level=721,max_level=721,},
{min_level=722,max_level=722,},
{min_level=723,max_level=723,},
{min_level=724,max_level=724,},
{min_level=725,max_level=725,},
{min_level=726,max_level=726,},
{min_level=727,max_level=727,},
{min_level=728,max_level=728,},
{min_level=729,max_level=729,},
{min_level=730,max_level=730,},
{min_level=731,max_level=731,},
{min_level=732,max_level=732,},
{min_level=733,max_level=733,},
{min_level=734,max_level=734,},
{min_level=735,max_level=735,},
{min_level=736,max_level=736,},
{min_level=737,max_level=737,},
{min_level=738,max_level=738,},
{min_level=739,max_level=739,},
{min_level=740,max_level=740,},
{min_level=741,max_level=741,},
{min_level=742,max_level=742,},
{min_level=743,max_level=743,},
{min_level=744,max_level=744,},
{min_level=745,max_level=745,},
{min_level=746,max_level=746,},
{min_level=747,max_level=747,},
{min_level=748,max_level=748,},
{min_level=749,max_level=749,},
{min_level=750,max_level=750,},
{min_level=751,max_level=751,},
{min_level=752,max_level=752,},
{min_level=753,max_level=753,},
{min_level=754,max_level=754,},
{min_level=755,max_level=755,},
{min_level=756,max_level=756,},
{min_level=757,max_level=757,},
{min_level=758,max_level=758,},
{min_level=759,max_level=759,},
{min_level=760,max_level=760,},
{min_level=761,max_level=761,},
{min_level=762,max_level=762,},
{min_level=763,max_level=763,},
{min_level=764,max_level=764,},
{min_level=765,max_level=765,},
{min_level=766,max_level=766,},
{min_level=767,max_level=767,},
{min_level=768,max_level=768,},
{min_level=769,max_level=769,},
{min_level=770,max_level=770,},
{min_level=771,max_level=771,},
{min_level=772,max_level=772,},
{min_level=773,max_level=773,},
{min_level=774,max_level=774,},
{min_level=775,max_level=775,},
{min_level=776,max_level=776,},
{min_level=777,max_level=777,},
{min_level=778,max_level=778,},
{min_level=779,max_level=779,},
{min_level=780,max_level=780,},
{min_level=781,max_level=781,},
{min_level=782,max_level=782,},
{min_level=783,max_level=783,},
{min_level=784,max_level=784,},
{min_level=785,max_level=785,},
{min_level=786,max_level=786,},
{min_level=787,max_level=787,},
{min_level=788,max_level=788,},
{min_level=789,max_level=789,},
{min_level=790,max_level=790,},
{min_level=791,max_level=791,},
{min_level=792,max_level=792,},
{min_level=793,max_level=793,},
{min_level=794,max_level=794,},
{min_level=795,max_level=795,},
{min_level=796,max_level=796,},
{min_level=797,max_level=797,},
{min_level=798,max_level=798,},
{min_level=799,max_level=799,},
{min_level=800,max_level=800,},
{min_level=801,max_level=801,},
{min_level=802,max_level=802,},
{min_level=803,max_level=803,},
{min_level=804,max_level=804,},
{min_level=805,max_level=805,},
{min_level=806,max_level=806,},
{min_level=807,max_level=807,},
{min_level=808,max_level=808,},
{min_level=809,max_level=809,},
{min_level=810,max_level=810,},
{min_level=811,max_level=811,},
{min_level=812,max_level=812,},
{min_level=813,max_level=813,},
{min_level=814,max_level=814,},
{min_level=815,max_level=815,},
{min_level=816,max_level=816,},
{min_level=817,max_level=817,},
{min_level=818,max_level=818,},
{min_level=819,max_level=819,},
{min_level=820,max_level=820,},
{min_level=821,max_level=821,},
{min_level=822,max_level=822,},
{min_level=823,max_level=823,},
{min_level=824,max_level=824,},
{min_level=825,max_level=825,},
{min_level=826,max_level=826,},
{min_level=827,max_level=827,},
{min_level=828,max_level=828,},
{min_level=829,max_level=829,},
{min_level=830,max_level=830,},
{min_level=831,max_level=831,},
{min_level=832,max_level=832,},
{min_level=833,max_level=833,},
{min_level=834,max_level=834,},
{min_level=835,max_level=835,},
{min_level=836,max_level=836,},
{min_level=837,max_level=837,},
{min_level=838,max_level=838,},
{min_level=839,max_level=839,},
{min_level=840,max_level=840,},
{min_level=841,max_level=841,},
{min_level=842,max_level=842,},
{min_level=843,max_level=843,},
{min_level=844,max_level=844,},
{min_level=845,max_level=845,},
{min_level=846,max_level=846,},
{min_level=847,max_level=847,},
{min_level=848,max_level=848,},
{min_level=849,max_level=849,},
{min_level=850,max_level=850,},
{min_level=851,max_level=851,},
{min_level=852,max_level=852,},
{min_level=853,max_level=853,},
{min_level=854,max_level=854,},
{min_level=855,max_level=855,},
{min_level=856,max_level=856,},
{min_level=857,max_level=857,},
{min_level=858,max_level=858,},
{min_level=859,max_level=859,},
{min_level=860,max_level=860,},
{min_level=861,max_level=861,},
{min_level=862,max_level=862,},
{min_level=863,max_level=863,},
{min_level=864,max_level=864,},
{min_level=865,max_level=865,},
{min_level=866,max_level=866,},
{min_level=867,max_level=867,},
{min_level=868,max_level=868,},
{min_level=869,max_level=869,},
{min_level=870,max_level=870,},
{min_level=871,max_level=871,},
{min_level=872,max_level=872,},
{min_level=873,max_level=873,},
{min_level=874,max_level=874,},
{min_level=875,max_level=875,},
{min_level=876,max_level=876,},
{min_level=877,max_level=877,},
{min_level=878,max_level=878,},
{min_level=879,max_level=879,},
{min_level=880,max_level=880,},
{min_level=881,max_level=881,},
{min_level=882,max_level=882,},
{min_level=883,max_level=883,},
{min_level=884,max_level=884,},
{min_level=885,max_level=885,},
{min_level=886,max_level=886,},
{min_level=887,max_level=887,},
{min_level=888,max_level=888,},
{min_level=889,max_level=889,},
{min_level=890,max_level=890,},
{min_level=891,max_level=891,},
{min_level=892,max_level=892,},
{min_level=893,max_level=893,},
{min_level=894,max_level=894,},
{min_level=895,max_level=895,},
{min_level=896,max_level=896,},
{min_level=897,max_level=897,},
{min_level=898,max_level=898,},
{min_level=899,max_level=899,},
{min_level=900,max_level=900,},
{min_level=901,max_level=901,},
{min_level=902,max_level=902,},
{min_level=903,max_level=903,},
{min_level=904,max_level=904,},
{min_level=905,max_level=905,},
{min_level=906,max_level=906,},
{min_level=907,max_level=907,},
{min_level=908,max_level=908,},
{min_level=909,max_level=909,},
{min_level=910,max_level=910,},
{min_level=911,max_level=911,},
{min_level=912,max_level=912,},
{min_level=913,max_level=913,},
{min_level=914,max_level=914,},
{min_level=915,max_level=915,},
{min_level=916,max_level=916,},
{min_level=917,max_level=917,},
{min_level=918,max_level=918,},
{min_level=919,max_level=919,},
{min_level=920,max_level=920,},
{min_level=921,max_level=921,},
{min_level=922,max_level=922,},
{min_level=923,max_level=923,},
{min_level=924,max_level=924,},
{min_level=925,max_level=925,},
{min_level=926,max_level=926,},
{min_level=927,max_level=927,},
{min_level=928,max_level=928,},
{min_level=929,max_level=929,},
{min_level=930,max_level=930,},
{min_level=931,max_level=931,},
{min_level=932,max_level=932,},
{min_level=933,max_level=933,},
{min_level=934,max_level=934,},
{min_level=935,max_level=935,},
{min_level=936,max_level=936,},
{min_level=937,max_level=937,},
{min_level=938,max_level=938,},
{min_level=939,max_level=939,},
{min_level=940,max_level=940,},
{min_level=941,max_level=941,},
{min_level=942,max_level=942,},
{min_level=943,max_level=943,},
{min_level=944,max_level=944,},
{min_level=945,max_level=945,},
{min_level=946,max_level=946,},
{min_level=947,max_level=947,},
{min_level=948,max_level=948,},
{min_level=949,max_level=949,},
{min_level=950,max_level=950,},
{min_level=951,max_level=951,},
{min_level=952,max_level=952,},
{min_level=953,max_level=953,},
{min_level=954,max_level=954,},
{min_level=955,max_level=955,},
{min_level=956,max_level=956,},
{min_level=957,max_level=957,},
{min_level=958,max_level=958,},
{min_level=959,max_level=959,},
{min_level=960,max_level=960,},
{min_level=961,max_level=961,},
{min_level=962,max_level=962,},
{min_level=963,max_level=963,},
{min_level=964,max_level=964,},
{min_level=965,max_level=965,},
{min_level=966,max_level=966,},
{min_level=967,max_level=967,},
{min_level=968,max_level=968,},
{min_level=969,max_level=969,},
{min_level=970,max_level=970,},
{min_level=971,max_level=971,},
{min_level=972,max_level=972,},
{min_level=973,max_level=973,},
{min_level=974,max_level=974,},
{min_level=975,max_level=975,},
{min_level=976,max_level=976,},
{min_level=977,max_level=977,},
{min_level=978,max_level=978,},
{min_level=979,max_level=979,},
{min_level=980,max_level=980,},
{min_level=981,max_level=981,},
{min_level=982,max_level=982,},
{min_level=983,max_level=983,},
{min_level=984,max_level=984,},
{min_level=985,max_level=985,},
{min_level=986,max_level=986,},
{min_level=987,max_level=987,},
{min_level=988,max_level=988,},
{min_level=989,max_level=989,},
{min_level=990,max_level=990,},
{min_level=991,max_level=991,},
{min_level=992,max_level=992,},
{min_level=993,max_level=993,},
{min_level=994,max_level=994,},
{min_level=995,max_level=995,},
{min_level=996,max_level=996,},
{min_level=997,max_level=997,},
{min_level=998,max_level=998,},
{min_level=999,max_level=999,},
{min_level=1000,max_level=1000,},
{min_level=1001,max_level=1001,},
{min_level=1002,max_level=1002,},
{min_level=1003,max_level=1003,},
{min_level=1004,max_level=1004,},
{min_level=1005,max_level=1005,},
{min_level=1006,max_level=1006,},
{min_level=1007,max_level=1007,},
{min_level=1008,max_level=1008,},
{min_level=1009,max_level=1009,},
{min_level=1010,max_level=1010,},
{min_level=1011,max_level=1011,},
{min_level=1012,max_level=1012,},
{min_level=1013,max_level=1013,},
{min_level=1014,max_level=1014,},
{min_level=1015,max_level=1015,},
{min_level=1016,max_level=1016,},
{min_level=1017,max_level=1017,},
{min_level=1018,max_level=1018,},
{min_level=1019,max_level=1019,},
{min_level=1020,max_level=1020,},
{min_level=1021,max_level=1021,},
{min_level=1022,max_level=1022,},
{min_level=1023,max_level=1023,},
{min_level=1024,max_level=1024,},
{min_level=1025,max_level=1025,},
{min_level=1026,max_level=1026,},
{min_level=1027,max_level=1027,},
{min_level=1028,max_level=1028,},
{min_level=1029,max_level=1029,},
{min_level=1030,max_level=1030,},
{min_level=1031,max_level=1031,},
{min_level=1032,max_level=1032,},
{min_level=1033,max_level=1033,},
{min_level=1034,max_level=1034,},
{min_level=1035,max_level=1035,},
{min_level=1036,max_level=1036,},
{min_level=1037,max_level=1037,},
{min_level=1038,max_level=1038,},
{min_level=1039,max_level=1039,},
{min_level=1040,max_level=1040,},
{min_level=1041,max_level=1041,},
{min_level=1042,max_level=1042,},
{min_level=1043,max_level=1043,},
{min_level=1044,max_level=1044,},
{min_level=1045,max_level=1045,},
{min_level=1046,max_level=1046,},
{min_level=1047,max_level=1047,},
{min_level=1048,max_level=1048,},
{min_level=1049,max_level=1049,},
{min_level=1050,max_level=1050,},
{min_level=1051,max_level=1051,},
{min_level=1052,max_level=1052,},
{min_level=1053,max_level=1053,},
{min_level=1054,max_level=1054,},
{min_level=1055,max_level=1055,},
{min_level=1056,max_level=1056,},
{min_level=1057,max_level=1057,},
{min_level=1058,max_level=1058,},
{min_level=1059,max_level=1059,},
{min_level=1060,max_level=1060,},
{min_level=1061,max_level=1061,},
{min_level=1062,max_level=1062,},
{min_level=1063,max_level=1063,},
{min_level=1064,max_level=1064,},
{min_level=1065,max_level=1065,},
{min_level=1066,max_level=1066,},
{min_level=1067,max_level=1067,},
{min_level=1068,max_level=1068,},
{min_level=1069,max_level=1069,},
{min_level=1070,max_level=1070,},
{min_level=1071,max_level=1071,},
{min_level=1072,max_level=1072,},
{min_level=1073,max_level=1073,},
{min_level=1074,max_level=1074,},
{min_level=1075,max_level=1075,},
{min_level=1076,max_level=1076,},
{min_level=1077,max_level=1077,},
{min_level=1078,max_level=1078,},
{min_level=1079,max_level=1079,},
{min_level=1080,max_level=1080,},
{min_level=1081,max_level=1081,},
{min_level=1082,max_level=1082,},
{min_level=1083,max_level=1083,},
{min_level=1084,max_level=1084,},
{min_level=1085,max_level=1085,},
{min_level=1086,max_level=1086,},
{min_level=1087,max_level=1087,},
{min_level=1088,max_level=1088,},
{min_level=1089,max_level=1089,},
{min_level=1090,max_level=1090,},
{min_level=1091,max_level=1091,},
{min_level=1092,max_level=1092,},
{min_level=1093,max_level=1093,},
{min_level=1094,max_level=1094,},
{min_level=1095,max_level=1095,},
{min_level=1096,max_level=1096,},
{min_level=1097,max_level=1097,},
{min_level=1098,max_level=1098,},
{min_level=1099,max_level=1099,},
{min_level=1100,max_level=1100,},
{min_level=1101,max_level=1101,},
{min_level=1102,max_level=1102,},
{min_level=1103,max_level=1103,},
{min_level=1104,max_level=1104,},
{min_level=1105,max_level=1105,},
{min_level=1106,max_level=1106,},
{min_level=1107,max_level=1107,},
{min_level=1108,max_level=1108,},
{min_level=1109,max_level=1109,},
{min_level=1110,max_level=1110,},
{min_level=1111,max_level=1111,},
{min_level=1112,max_level=1112,},
{min_level=1113,max_level=1113,},
{min_level=1114,max_level=1114,},
{min_level=1115,max_level=1115,},
{min_level=1116,max_level=1116,},
{min_level=1117,max_level=1117,},
{min_level=1118,max_level=1118,},
{min_level=1119,max_level=1119,},
{min_level=1120,max_level=1120,},
{min_level=1121,max_level=1121,},
{min_level=1122,max_level=1122,},
{min_level=1123,max_level=1123,},
{min_level=1124,max_level=1124,},
{min_level=1125,max_level=1125,},
{min_level=1126,max_level=1126,},
{min_level=1127,max_level=1127,},
{min_level=1128,max_level=1128,},
{min_level=1129,max_level=1129,},
{min_level=1130,max_level=1130,},
{min_level=1131,max_level=1131,},
{min_level=1132,max_level=1132,},
{min_level=1133,max_level=1133,},
{min_level=1134,max_level=1134,},
{min_level=1135,max_level=1135,},
{min_level=1136,max_level=1136,},
{min_level=1137,max_level=1137,},
{min_level=1138,max_level=1138,},
{min_level=1139,max_level=1139,},
{min_level=1140,max_level=1140,},
{min_level=1141,max_level=1141,},
{min_level=1142,max_level=1142,},
{min_level=1143,max_level=1143,},
{min_level=1144,max_level=1144,},
{min_level=1145,max_level=1145,},
{min_level=1146,max_level=1146,},
{min_level=1147,max_level=1147,},
{min_level=1148,max_level=1148,},
{min_level=1149,max_level=1149,},
{min_level=1150,max_level=1150,},
{min_level=1151,max_level=1151,},
{min_level=1152,max_level=1152,},
{min_level=1153,max_level=1153,},
{min_level=1154,max_level=1154,},
{min_level=1155,max_level=1155,},
{min_level=1156,max_level=1156,},
{min_level=1157,max_level=1157,},
{min_level=1158,max_level=1158,},
{min_level=1159,max_level=1159,},
{min_level=1160,max_level=1160,},
{min_level=1161,max_level=1161,},
{min_level=1162,max_level=1162,},
{min_level=1163,max_level=1163,},
{min_level=1164,max_level=1164,},
{min_level=1165,max_level=1165,},
{min_level=1166,max_level=1166,},
{min_level=1167,max_level=1167,},
{min_level=1168,max_level=1168,},
{min_level=1169,max_level=1169,},
{min_level=1170,max_level=1170,},
{min_level=1171,max_level=1171,},
{min_level=1172,max_level=1172,},
{min_level=1173,max_level=1173,},
{min_level=1174,max_level=1174,},
{min_level=1175,max_level=1175,},
{min_level=1176,max_level=1176,},
{min_level=1177,max_level=1177,},
{min_level=1178,max_level=1178,},
{min_level=1179,max_level=1179,},
{min_level=1180,max_level=1180,},
{min_level=1181,max_level=1181,},
{min_level=1182,max_level=1182,},
{min_level=1183,max_level=1183,},
{min_level=1184,max_level=1184,},
{min_level=1185,max_level=1185,},
{min_level=1186,max_level=1186,},
{min_level=1187,max_level=1187,},
{min_level=1188,max_level=1188,},
{min_level=1189,max_level=1189,},
{min_level=1190,max_level=1190,},
{min_level=1191,max_level=1191,},
{min_level=1192,max_level=1192,},
{min_level=1193,max_level=1193,},
{min_level=1194,max_level=1194,},
{min_level=1195,max_level=1195,},
{min_level=1196,max_level=1196,},
{min_level=1197,max_level=1197,},
{min_level=1198,max_level=1198,},
{min_level=1199,max_level=1199,},
{min_level=1200,max_level=1200,},
{min_level=1201,max_level=1201,},
{min_level=1202,max_level=1202,},
{min_level=1203,max_level=1203,},
{min_level=1204,max_level=1204,},
{min_level=1205,max_level=1205,},
{min_level=1206,max_level=1206,},
{min_level=1207,max_level=1207,},
{min_level=1208,max_level=1208,},
{min_level=1209,max_level=1209,},
{min_level=1210,max_level=1210,},
{min_level=1211,max_level=1211,},
{min_level=1212,max_level=1212,},
{min_level=1213,max_level=1213,},
{min_level=1214,max_level=1214,},
{min_level=1215,max_level=1215,},
{min_level=1216,max_level=1216,},
{min_level=1217,max_level=1217,},
{min_level=1218,max_level=1218,},
{min_level=1219,max_level=1219,},
{min_level=1220,max_level=1220,},
{min_level=1221,max_level=1221,},
{min_level=1222,max_level=1222,},
{min_level=1223,max_level=1223,},
{min_level=1224,max_level=1224,},
{min_level=1225,max_level=1225,},
{min_level=1226,max_level=1226,},
{min_level=1227,max_level=1227,},
{min_level=1228,max_level=1228,},
{min_level=1229,max_level=1229,},
{min_level=1230,max_level=1230,},
{min_level=1231,max_level=1231,},
{min_level=1232,max_level=1232,},
{min_level=1233,max_level=1233,},
{min_level=1234,max_level=1234,},
{min_level=1235,max_level=1235,},
{min_level=1236,max_level=1236,},
{min_level=1237,max_level=1237,},
{min_level=1238,max_level=1238,},
{min_level=1239,max_level=1239,},
{min_level=1240,max_level=1240,},
{min_level=1241,max_level=1241,},
{min_level=1242,max_level=1242,},
{min_level=1243,max_level=1243,},
{min_level=1244,max_level=1244,},
{min_level=1245,max_level=1245,},
{min_level=1246,max_level=1246,},
{min_level=1247,max_level=1247,},
{min_level=1248,max_level=1248,},
{min_level=1249,max_level=1249,},
{min_level=1250,max_level=1250,},
{min_level=1251,max_level=1251,},
{min_level=1252,max_level=1252,},
{min_level=1253,max_level=1253,},
{min_level=1254,max_level=1254,},
{min_level=1255,max_level=1255,},
{min_level=1256,max_level=1256,},
{min_level=1257,max_level=1257,},
{min_level=1258,max_level=1258,},
{min_level=1259,max_level=1259,},
{min_level=1260,max_level=1260,},
{min_level=1261,max_level=1261,},
{min_level=1262,max_level=1262,},
{min_level=1263,max_level=1263,},
{min_level=1264,max_level=1264,},
{min_level=1265,max_level=1265,},
{min_level=1266,max_level=1266,},
{min_level=1267,max_level=1267,},
{min_level=1268,max_level=1268,},
{min_level=1269,max_level=1269,},
{min_level=1270,max_level=1270,},
{min_level=1271,max_level=1271,},
{min_level=1272,max_level=1272,},
{min_level=1273,max_level=1273,},
{min_level=1274,max_level=1274,},
{min_level=1275,max_level=1275,},
{min_level=1276,max_level=1276,},
{min_level=1277,max_level=1277,},
{min_level=1278,max_level=1278,},
{min_level=1279,max_level=1279,},
{min_level=1280,max_level=1280,},
{min_level=1281,max_level=1281,},
{min_level=1282,max_level=1282,},
{min_level=1283,max_level=1283,},
{min_level=1284,max_level=1284,},
{min_level=1285,max_level=1285,},
{min_level=1286,max_level=1286,},
{min_level=1287,max_level=1287,},
{min_level=1288,max_level=1288,},
{min_level=1289,max_level=1289,},
{min_level=1290,max_level=1290,},
{min_level=1291,max_level=1291,},
{min_level=1292,max_level=1292,},
{min_level=1293,max_level=1293,},
{min_level=1294,max_level=1294,},
{min_level=1295,max_level=1295,},
{min_level=1296,max_level=1296,},
{min_level=1297,max_level=1297,},
{min_level=1298,max_level=1298,},
{min_level=1299,max_level=1299,},
{min_level=1300,max_level=1300,},
{min_level=1301,max_level=1301,},
{min_level=1302,max_level=1302,},
{min_level=1303,max_level=1303,},
{min_level=1304,max_level=1304,},
{min_level=1305,max_level=1305,},
{min_level=1306,max_level=1306,},
{min_level=1307,max_level=1307,},
{min_level=1308,max_level=1308,},
{min_level=1309,max_level=1309,},
{min_level=1310,max_level=1310,},
{min_level=1311,max_level=1311,},
{min_level=1312,max_level=1312,},
{min_level=1313,max_level=1313,},
{min_level=1314,max_level=1314,},
{min_level=1315,max_level=1315,},
{min_level=1316,max_level=1316,},
{min_level=1317,max_level=1317,},
{min_level=1318,max_level=1318,},
{min_level=1319,max_level=1319,},
{min_level=1320,max_level=1320,},
{min_level=1321,max_level=1321,},
{min_level=1322,max_level=1322,},
{min_level=1323,max_level=1323,},
{min_level=1324,max_level=1324,},
{min_level=1325,max_level=1325,},
{min_level=1326,max_level=1326,},
{min_level=1327,max_level=1327,},
{min_level=1328,max_level=1328,},
{min_level=1329,max_level=1329,},
{min_level=1330,max_level=1330,},
{min_level=1331,max_level=1331,},
{min_level=1332,max_level=1332,},
{min_level=1333,max_level=1333,},
{min_level=1334,max_level=1334,},
{min_level=1335,max_level=1335,},
{min_level=1336,max_level=1336,},
{min_level=1337,max_level=1337,},
{min_level=1338,max_level=1338,},
{min_level=1339,max_level=1339,},
{min_level=1340,max_level=1340,},
{min_level=1341,max_level=1341,},
{min_level=1342,max_level=1342,},
{min_level=1343,max_level=1343,},
{min_level=1344,max_level=1344,},
{min_level=1345,max_level=1345,},
{min_level=1346,max_level=1346,},
{min_level=1347,max_level=1347,},
{min_level=1348,max_level=1348,},
{min_level=1349,max_level=1349,},
{min_level=1350,max_level=1350,},
{min_level=1351,max_level=1351,},
{min_level=1352,max_level=1352,},
{min_level=1353,max_level=1353,},
{min_level=1354,max_level=1354,},
{min_level=1355,max_level=1355,},
{min_level=1356,max_level=1356,},
{min_level=1357,max_level=1357,},
{min_level=1358,max_level=1358,},
{min_level=1359,max_level=1359,},
{min_level=1360,max_level=1360,},
{min_level=1361,max_level=1361,},
{min_level=1362,max_level=1362,},
{min_level=1363,max_level=1363,},
{min_level=1364,max_level=1364,},
{min_level=1365,max_level=1365,},
{min_level=1366,max_level=1366,},
{min_level=1367,max_level=1367,},
{min_level=1368,max_level=1368,},
{min_level=1369,max_level=1369,},
{min_level=1370,max_level=1370,},
{min_level=1371,max_level=1371,},
{min_level=1372,max_level=1372,},
{min_level=1373,max_level=1373,},
{min_level=1374,max_level=1374,},
{min_level=1375,max_level=1375,},
{min_level=1376,max_level=1376,},
{min_level=1377,max_level=1377,},
{min_level=1378,max_level=1378,},
{min_level=1379,max_level=1379,},
{min_level=1380,max_level=1380,},
{min_level=1381,max_level=1381,},
{min_level=1382,max_level=1382,},
{min_level=1383,max_level=1383,},
{min_level=1384,max_level=1384,},
{min_level=1385,max_level=1385,},
{min_level=1386,max_level=1386,},
{min_level=1387,max_level=1387,},
{min_level=1388,max_level=1388,},
{min_level=1389,max_level=1389,},
{min_level=1390,max_level=1390,},
{min_level=1391,max_level=1391,},
{min_level=1392,max_level=1392,},
{min_level=1393,max_level=1393,},
{min_level=1394,max_level=1394,},
{min_level=1395,max_level=1395,},
{min_level=1396,max_level=1396,},
{min_level=1397,max_level=1397,},
{min_level=1398,max_level=1398,},
{min_level=1399,max_level=1399,},
{min_level=1400,max_level=1400,},
{min_level=1401,max_level=1401,},
{min_level=1402,max_level=1402,},
{min_level=1403,max_level=1403,},
{min_level=1404,max_level=1404,},
{min_level=1405,max_level=1405,},
{min_level=1406,max_level=1406,},
{min_level=1407,max_level=1407,},
{min_level=1408,max_level=1408,},
{min_level=1409,max_level=1409,},
{min_level=1410,max_level=1410,},
{min_level=1411,max_level=1411,},
{min_level=1412,max_level=1412,},
{min_level=1413,max_level=1413,},
{min_level=1414,max_level=1414,},
{min_level=1415,max_level=1415,},
{min_level=1416,max_level=1416,},
{min_level=1417,max_level=1417,},
{min_level=1418,max_level=1418,},
{min_level=1419,max_level=1419,},
{min_level=1420,max_level=1420,},
{min_level=1421,max_level=1421,},
{min_level=1422,max_level=1422,},
{min_level=1423,max_level=1423,},
{min_level=1424,max_level=1424,},
{min_level=1425,max_level=1425,},
{min_level=1426,max_level=1426,},
{min_level=1427,max_level=1427,},
{min_level=1428,max_level=1428,},
{min_level=1429,max_level=1429,},
{min_level=1430,max_level=1430,},
{min_level=1431,max_level=1431,},
{min_level=1432,max_level=1432,},
{min_level=1433,max_level=1433,},
{min_level=1434,max_level=1434,},
{min_level=1435,max_level=1435,},
{min_level=1436,max_level=1436,},
{min_level=1437,max_level=1437,},
{min_level=1438,max_level=1438,},
{min_level=1439,max_level=1439,},
{min_level=1440,max_level=1440,},
{min_level=1441,max_level=1441,},
{min_level=1442,max_level=1442,},
{min_level=1443,max_level=1443,},
{min_level=1444,max_level=1444,},
{min_level=1445,max_level=1445,},
{min_level=1446,max_level=1446,},
{min_level=1447,max_level=1447,},
{min_level=1448,max_level=1448,},
{min_level=1449,max_level=1449,},
{min_level=1450,max_level=1450,},
{min_level=1451,max_level=1451,},
{min_level=1452,max_level=1452,},
{min_level=1453,max_level=1453,},
{min_level=1454,max_level=1454,},
{min_level=1455,max_level=1455,},
{min_level=1456,max_level=1456,},
{min_level=1457,max_level=1457,},
{min_level=1458,max_level=1458,},
{min_level=1459,max_level=1459,},
{min_level=1460,max_level=1460,},
{min_level=1461,max_level=1461,},
{min_level=1462,max_level=1462,},
{min_level=1463,max_level=1463,},
{min_level=1464,max_level=1464,},
{min_level=1465,max_level=1465,},
{min_level=1466,max_level=1466,},
{min_level=1467,max_level=1467,},
{min_level=1468,max_level=1468,},
{min_level=1469,max_level=1469,},
{min_level=1470,max_level=1470,},
{min_level=1471,max_level=1471,},
{min_level=1472,max_level=1472,},
{min_level=1473,max_level=1473,},
{min_level=1474,max_level=1474,},
{min_level=1475,max_level=1475,},
{min_level=1476,max_level=1476,},
{min_level=1477,max_level=1477,},
{min_level=1478,max_level=1478,},
{min_level=1479,max_level=1479,},
{min_level=1480,max_level=1480,},
{min_level=1481,max_level=1481,},
{min_level=1482,max_level=1482,},
{min_level=1483,max_level=1483,},
{min_level=1484,max_level=1484,},
{min_level=1485,max_level=1485,},
{min_level=1486,max_level=1486,},
{min_level=1487,max_level=1487,},
{min_level=1488,max_level=1488,},
{min_level=1489,max_level=1489,},
{min_level=1490,max_level=1490,},
{min_level=1491,max_level=1491,},
{min_level=1492,max_level=1492,},
{min_level=1493,max_level=1493,},
{min_level=1494,max_level=1494,},
{min_level=1495,max_level=1495,},
{min_level=1496,max_level=1496,},
{min_level=1497,max_level=1497,},
{min_level=1498,max_level=1498,},
{min_level=1499,max_level=1499,},
{min_level=1500,max_level=1500,},
{min_level=1501,max_level=1501,},
{min_level=1502,max_level=1502,},
{min_level=1503,max_level=1503,},
{min_level=1504,max_level=1504,},
{min_level=1505,max_level=1505,},
{min_level=1506,max_level=1506,},
{min_level=1507,max_level=1507,},
{min_level=1508,max_level=1508,},
{min_level=1509,max_level=1509,},
{min_level=1510,max_level=1510,},
{min_level=1511,max_level=1511,},
{min_level=1512,max_level=1512,},
{min_level=1513,max_level=1513,},
{min_level=1514,max_level=1514,},
{min_level=1515,max_level=1515,},
{min_level=1516,max_level=1516,},
{min_level=1517,max_level=1517,},
{min_level=1518,max_level=1518,},
{min_level=1519,max_level=1519,},
{min_level=1520,max_level=1520,},
{min_level=1521,max_level=1521,},
{min_level=1522,max_level=1522,},
{min_level=1523,max_level=1523,},
{min_level=1524,max_level=1524,},
{min_level=1525,max_level=1525,},
{min_level=1526,max_level=1526,},
{min_level=1527,max_level=1527,},
{min_level=1528,max_level=1528,},
{min_level=1529,max_level=1529,},
{min_level=1530,max_level=1530,},
{min_level=1531,max_level=1531,},
{min_level=1532,max_level=1532,},
{min_level=1533,max_level=1533,},
{min_level=1534,max_level=1534,},
{min_level=1535,max_level=1535,},
{min_level=1536,max_level=1536,},
{min_level=1537,max_level=1537,},
{min_level=1538,max_level=1538,},
{min_level=1539,max_level=1539,},
{min_level=1540,max_level=1540,},
{min_level=1541,max_level=1541,},
{min_level=1542,max_level=1542,},
{min_level=1543,max_level=1543,},
{min_level=1544,max_level=1544,},
{min_level=1545,max_level=1545,},
{min_level=1546,max_level=1546,},
{min_level=1547,max_level=1547,},
{min_level=1548,max_level=1548,},
{min_level=1549,max_level=1549,},
{min_level=1550,max_level=1550,},
{min_level=1551,max_level=1551,},
{min_level=1552,max_level=1552,},
{min_level=1553,max_level=1553,},
{min_level=1554,max_level=1554,},
{min_level=1555,max_level=1555,},
{min_level=1556,max_level=1556,},
{min_level=1557,max_level=1557,},
{min_level=1558,max_level=1558,},
{min_level=1559,max_level=1559,},
{min_level=1560,max_level=1560,},
{min_level=1561,max_level=1561,},
{min_level=1562,max_level=1562,},
{min_level=1563,max_level=1563,},
{min_level=1564,max_level=1564,},
{min_level=1565,max_level=1565,},
{min_level=1566,max_level=1566,},
{min_level=1567,max_level=1567,},
{min_level=1568,max_level=1568,},
{min_level=1569,max_level=1569,},
{min_level=1570,max_level=1570,},
{min_level=1571,max_level=1571,},
{min_level=1572,max_level=1572,},
{min_level=1573,max_level=1573,},
{min_level=1574,max_level=1574,},
{min_level=1575,max_level=1575,},
{min_level=1576,max_level=1576,},
{min_level=1577,max_level=1577,},
{min_level=1578,max_level=1578,},
{min_level=1579,max_level=1579,},
{min_level=1580,max_level=1580,},
{min_level=1581,max_level=1581,},
{min_level=1582,max_level=1582,},
{min_level=1583,max_level=1583,},
{min_level=1584,max_level=1584,},
{min_level=1585,max_level=1585,},
{min_level=1586,max_level=1586,},
{min_level=1587,max_level=1587,},
{min_level=1588,max_level=1588,},
{min_level=1589,max_level=1589,},
{min_level=1590,max_level=1590,},
{min_level=1591,max_level=1591,},
{min_level=1592,max_level=1592,},
{min_level=1593,max_level=1593,},
{min_level=1594,max_level=1594,},
{min_level=1595,max_level=1595,},
{min_level=1596,max_level=1596,},
{min_level=1597,max_level=1597,},
{min_level=1598,max_level=1598,},
{min_level=1599,max_level=1599,},
{min_level=1600,max_level=1600,},
{min_level=1601,max_level=1601,},
{min_level=1602,max_level=1602,},
{min_level=1603,max_level=1603,},
{min_level=1604,max_level=1604,},
{min_level=1605,max_level=1605,},
{min_level=1606,max_level=1606,},
{min_level=1607,max_level=1607,},
{min_level=1608,max_level=1608,},
{min_level=1609,max_level=1609,},
{min_level=1610,max_level=1610,},
{min_level=1611,max_level=1611,},
{min_level=1612,max_level=1612,},
{min_level=1613,max_level=1613,},
{min_level=1614,max_level=1614,},
{min_level=1615,max_level=1615,},
{min_level=1616,max_level=1616,},
{min_level=1617,max_level=1617,},
{min_level=1618,max_level=1618,},
{min_level=1619,max_level=1619,},
{min_level=1620,max_level=1620,},
{min_level=1621,max_level=1621,},
{min_level=1622,max_level=1622,},
{min_level=1623,max_level=1623,},
{min_level=1624,max_level=1624,},
{min_level=1625,max_level=1625,},
{min_level=1626,max_level=1626,},
{min_level=1627,max_level=1627,},
{min_level=1628,max_level=1628,},
{min_level=1629,max_level=1629,},
{min_level=1630,max_level=1630,},
{min_level=1631,max_level=1631,},
{min_level=1632,max_level=1632,},
{min_level=1633,max_level=1633,},
{min_level=1634,max_level=1634,},
{min_level=1635,max_level=1635,},
{min_level=1636,max_level=1636,},
{min_level=1637,max_level=1637,},
{min_level=1638,max_level=1638,},
{min_level=1639,max_level=1639,},
{min_level=1640,max_level=1640,},
{min_level=1641,max_level=1641,},
{min_level=1642,max_level=1642,},
{min_level=1643,max_level=1643,},
{min_level=1644,max_level=1644,},
{min_level=1645,max_level=1645,},
{min_level=1646,max_level=1646,},
{min_level=1647,max_level=1647,},
{min_level=1648,max_level=1648,},
{min_level=1649,max_level=1649,},
{min_level=1650,max_level=1650,},
{min_level=1651,max_level=1651,},
{min_level=1652,max_level=1652,},
{min_level=1653,max_level=1653,},
{min_level=1654,max_level=1654,},
{min_level=1655,max_level=1655,},
{min_level=1656,max_level=1656,},
{min_level=1657,max_level=1657,},
{min_level=1658,max_level=1658,},
{min_level=1659,max_level=1659,},
{min_level=1660,max_level=1660,},
{min_level=1661,max_level=1661,},
{min_level=1662,max_level=1662,},
{min_level=1663,max_level=1663,},
{min_level=1664,max_level=1664,},
{min_level=1665,max_level=1665,},
{min_level=1666,max_level=1666,},
{min_level=1667,max_level=1667,},
{min_level=1668,max_level=1668,},
{min_level=1669,max_level=1669,},
{min_level=1670,max_level=1670,},
{min_level=1671,max_level=1671,},
{min_level=1672,max_level=1672,},
{min_level=1673,max_level=1673,},
{min_level=1674,max_level=1674,},
{min_level=1675,max_level=1675,},
{min_level=1676,max_level=1676,},
{min_level=1677,max_level=1677,},
{min_level=1678,max_level=1678,},
{min_level=1679,max_level=1679,},
{min_level=1680,max_level=1680,},
{min_level=1681,max_level=1681,},
{min_level=1682,max_level=1682,},
{min_level=1683,max_level=1683,},
{min_level=1684,max_level=1684,},
{min_level=1685,max_level=1685,},
{min_level=1686,max_level=1686,},
{min_level=1687,max_level=1687,},
{min_level=1688,max_level=1688,},
{min_level=1689,max_level=1689,},
{min_level=1690,max_level=1690,},
{min_level=1691,max_level=1691,},
{min_level=1692,max_level=1692,},
{min_level=1693,max_level=1693,},
{min_level=1694,max_level=1694,},
{min_level=1695,max_level=1695,},
{min_level=1696,max_level=1696,},
{min_level=1697,max_level=1697,},
{min_level=1698,max_level=1698,},
{min_level=1699,max_level=1699,},
{min_level=1700,max_level=1700,},
{min_level=1701,max_level=1701,},
{min_level=1702,max_level=1702,},
{min_level=1703,max_level=1703,},
{min_level=1704,max_level=1704,},
{min_level=1705,max_level=1705,},
{min_level=1706,max_level=1706,},
{min_level=1707,max_level=1707,},
{min_level=1708,max_level=1708,},
{min_level=1709,max_level=1709,},
{min_level=1710,max_level=1710,},
{min_level=1711,max_level=1711,},
{min_level=1712,max_level=1712,},
{min_level=1713,max_level=1713,},
{min_level=1714,max_level=1714,},
{min_level=1715,max_level=1715,},
{min_level=1716,max_level=1716,},
{min_level=1717,max_level=1717,},
{min_level=1718,max_level=1718,},
{min_level=1719,max_level=1719,},
{min_level=1720,max_level=1720,},
{min_level=1721,max_level=1721,},
{min_level=1722,max_level=1722,},
{min_level=1723,max_level=1723,},
{min_level=1724,max_level=1724,},
{min_level=1725,max_level=1725,},
{min_level=1726,max_level=1726,},
{min_level=1727,max_level=1727,},
{min_level=1728,max_level=1728,},
{min_level=1729,max_level=1729,},
{min_level=1730,max_level=1730,},
{min_level=1731,max_level=1731,},
{min_level=1732,max_level=1732,},
{min_level=1733,max_level=1733,},
{min_level=1734,max_level=1734,},
{min_level=1735,max_level=1735,},
{min_level=1736,max_level=1736,},
{min_level=1737,max_level=1737,},
{min_level=1738,max_level=1738,},
{min_level=1739,max_level=1739,},
{min_level=1740,max_level=1740,},
{min_level=1741,max_level=1741,},
{min_level=1742,max_level=1742,},
{min_level=1743,max_level=1743,},
{min_level=1744,max_level=1744,},
{min_level=1745,max_level=1745,},
{min_level=1746,max_level=1746,},
{min_level=1747,max_level=1747,},
{min_level=1748,max_level=1748,},
{min_level=1749,max_level=1749,},
{min_level=1750,max_level=1750,},
{min_level=1751,max_level=1751,},
{min_level=1752,max_level=1752,},
{min_level=1753,max_level=1753,},
{min_level=1754,max_level=1754,},
{min_level=1755,max_level=1755,},
{min_level=1756,max_level=1756,},
{min_level=1757,max_level=1757,},
{min_level=1758,max_level=1758,},
{min_level=1759,max_level=1759,},
{min_level=1760,max_level=1760,},
{min_level=1761,max_level=1761,},
{min_level=1762,max_level=1762,},
{min_level=1763,max_level=1763,},
{min_level=1764,max_level=1764,},
{min_level=1765,max_level=1765,},
{min_level=1766,max_level=1766,},
{min_level=1767,max_level=1767,},
{min_level=1768,max_level=1768,},
{min_level=1769,max_level=1769,},
{min_level=1770,max_level=1770,},
{min_level=1771,max_level=1771,},
{min_level=1772,max_level=1772,},
{min_level=1773,max_level=1773,},
{min_level=1774,max_level=1774,},
{min_level=1775,max_level=1775,},
{min_level=1776,max_level=1776,},
{min_level=1777,max_level=1777,},
{min_level=1778,max_level=1778,},
{min_level=1779,max_level=1779,},
{min_level=1780,max_level=1780,},
{min_level=1781,max_level=1781,},
{min_level=1782,max_level=1782,},
{min_level=1783,max_level=1783,},
{min_level=1784,max_level=1784,},
{min_level=1785,max_level=1785,},
{min_level=1786,max_level=1786,},
{min_level=1787,max_level=1787,},
{min_level=1788,max_level=1788,},
{min_level=1789,max_level=1789,},
{min_level=1790,max_level=1790,},
{min_level=1791,max_level=1791,},
{min_level=1792,max_level=1792,},
{min_level=1793,max_level=1793,},
{min_level=1794,max_level=1794,},
{min_level=1795,max_level=1795,},
{min_level=1796,max_level=1796,},
{min_level=1797,max_level=1797,},
{min_level=1798,max_level=1798,},
{min_level=1799,max_level=1799,},
{min_level=1800,max_level=1800,},
{min_level=1801,max_level=1801,},
{min_level=1802,max_level=1802,},
{min_level=1803,max_level=1803,},
{min_level=1804,max_level=1804,},
{min_level=1805,max_level=1805,},
{min_level=1806,max_level=1806,},
{min_level=1807,max_level=1807,},
{min_level=1808,max_level=1808,},
{min_level=1809,max_level=1809,},
{min_level=1810,max_level=1810,},
{min_level=1811,max_level=1811,},
{min_level=1812,max_level=1812,},
{min_level=1813,max_level=1813,},
{min_level=1814,max_level=1814,},
{min_level=1815,max_level=1815,},
{min_level=1816,max_level=1816,},
{min_level=1817,max_level=1817,},
{min_level=1818,max_level=1818,},
{min_level=1819,max_level=1819,},
{min_level=1820,max_level=1820,},
{min_level=1821,max_level=1821,},
{min_level=1822,max_level=1822,},
{min_level=1823,max_level=1823,},
{min_level=1824,max_level=1824,},
{min_level=1825,max_level=1825,},
{min_level=1826,max_level=1826,},
{min_level=1827,max_level=1827,},
{min_level=1828,max_level=1828,},
{min_level=1829,max_level=1829,},
{min_level=1830,max_level=1830,},
{min_level=1831,max_level=1831,},
{min_level=1832,max_level=1832,},
{min_level=1833,max_level=1833,},
{min_level=1834,max_level=1834,},
{min_level=1835,max_level=1835,},
{min_level=1836,max_level=1836,},
{min_level=1837,max_level=1837,},
{min_level=1838,max_level=1838,},
{min_level=1839,max_level=1839,},
{min_level=1840,max_level=1840,},
{min_level=1841,max_level=1841,},
{min_level=1842,max_level=1842,},
{min_level=1843,max_level=1843,},
{min_level=1844,max_level=1844,},
{min_level=1845,max_level=1845,},
{min_level=1846,max_level=1846,},
{min_level=1847,max_level=1847,},
{min_level=1848,max_level=1848,},
{min_level=1849,max_level=1849,},
{min_level=1850,max_level=1850,},
{min_level=1851,max_level=1851,},
{min_level=1852,max_level=1852,},
{min_level=1853,max_level=1853,},
{min_level=1854,max_level=1854,},
{min_level=1855,max_level=1855,},
{min_level=1856,max_level=1856,},
{min_level=1857,max_level=1857,},
{min_level=1858,max_level=1858,},
{min_level=1859,max_level=1859,},
{min_level=1860,max_level=1860,},
{min_level=1861,max_level=1861,},
{min_level=1862,max_level=1862,},
{min_level=1863,max_level=1863,},
{min_level=1864,max_level=1864,},
{min_level=1865,max_level=1865,},
{min_level=1866,max_level=1866,},
{min_level=1867,max_level=1867,},
{min_level=1868,max_level=1868,},
{min_level=1869,max_level=1869,},
{min_level=1870,max_level=1870,},
{min_level=1871,max_level=1871,},
{min_level=1872,max_level=1872,},
{min_level=1873,max_level=1873,},
{min_level=1874,max_level=1874,},
{min_level=1875,max_level=1875,},
{min_level=1876,max_level=1876,},
{min_level=1877,max_level=1877,},
{min_level=1878,max_level=1878,},
{min_level=1879,max_level=1879,},
{min_level=1880,max_level=1880,},
{min_level=1881,max_level=1881,},
{min_level=1882,max_level=1882,},
{min_level=1883,max_level=1883,},
{min_level=1884,max_level=1884,},
{min_level=1885,max_level=1885,},
{min_level=1886,max_level=1886,},
{min_level=1887,max_level=1887,},
{min_level=1888,max_level=1888,},
{min_level=1889,max_level=1889,},
{min_level=1890,max_level=1890,},
{min_level=1891,max_level=1891,},
{min_level=1892,max_level=1892,},
{min_level=1893,max_level=1893,},
{min_level=1894,max_level=1894,},
{min_level=1895,max_level=1895,},
{min_level=1896,max_level=1896,},
{min_level=1897,max_level=1897,},
{min_level=1898,max_level=1898,},
{min_level=1899,max_level=1899,},
{min_level=1900,max_level=1900,},
{min_level=1901,max_level=1901,},
{min_level=1902,max_level=1902,},
{min_level=1903,max_level=1903,},
{min_level=1904,max_level=1904,},
{min_level=1905,max_level=1905,},
{min_level=1906,max_level=1906,},
{min_level=1907,max_level=1907,},
{min_level=1908,max_level=1908,},
{min_level=1909,max_level=1909,},
{min_level=1910,max_level=1910,},
{min_level=1911,max_level=1911,},
{min_level=1912,max_level=1912,},
{min_level=1913,max_level=1913,},
{min_level=1914,max_level=1914,},
{min_level=1915,max_level=1915,},
{min_level=1916,max_level=1916,},
{min_level=1917,max_level=1917,},
{min_level=1918,max_level=1918,},
{min_level=1919,max_level=1919,},
{min_level=1920,max_level=1920,},
{min_level=1921,max_level=1921,},
{min_level=1922,max_level=1922,},
{min_level=1923,max_level=1923,},
{min_level=1924,max_level=1924,},
{min_level=1925,max_level=1925,},
{min_level=1926,max_level=1926,},
{min_level=1927,max_level=1927,},
{min_level=1928,max_level=1928,},
{min_level=1929,max_level=1929,},
{min_level=1930,max_level=1930,},
{min_level=1931,max_level=1931,},
{min_level=1932,max_level=1932,},
{min_level=1933,max_level=1933,},
{min_level=1934,max_level=1934,},
{min_level=1935,max_level=1935,},
{min_level=1936,max_level=1936,},
{min_level=1937,max_level=1937,},
{min_level=1938,max_level=1938,},
{min_level=1939,max_level=1939,},
{min_level=1940,max_level=1940,},
{min_level=1941,max_level=1941,},
{min_level=1942,max_level=1942,},
{min_level=1943,max_level=1943,},
{min_level=1944,max_level=1944,},
{min_level=1945,max_level=1945,},
{min_level=1946,max_level=1946,},
{min_level=1947,max_level=1947,},
{min_level=1948,max_level=1948,},
{min_level=1949,max_level=1949,},
{min_level=1950,max_level=1950,},
{min_level=1951,max_level=1951,},
{min_level=1952,max_level=1952,},
{min_level=1953,max_level=1953,},
{min_level=1954,max_level=1954,},
{min_level=1955,max_level=1955,},
{min_level=1956,max_level=1956,},
{min_level=1957,max_level=1957,},
{min_level=1958,max_level=1958,},
{min_level=1959,max_level=1959,},
{min_level=1960,max_level=1960,},
{min_level=1961,max_level=1961,},
{min_level=1962,max_level=1962,},
{min_level=1963,max_level=1963,},
{min_level=1964,max_level=1964,},
{min_level=1965,max_level=1965,},
{min_level=1966,max_level=1966,},
{min_level=1967,max_level=1967,},
{min_level=1968,max_level=1968,},
{min_level=1969,max_level=1969,},
{min_level=1970,max_level=1970,},
{min_level=1971,max_level=1971,},
{min_level=1972,max_level=1972,},
{min_level=1973,max_level=1973,},
{min_level=1974,max_level=1974,},
{min_level=1975,max_level=1975,},
{min_level=1976,max_level=1976,},
{min_level=1977,max_level=1977,},
{min_level=1978,max_level=1978,},
{min_level=1979,max_level=1979,},
{min_level=1980,max_level=1980,},
{min_level=1981,max_level=1981,},
{min_level=1982,max_level=1982,},
{min_level=1983,max_level=1983,},
{min_level=1984,max_level=1984,},
{min_level=1985,max_level=1985,},
{min_level=1986,max_level=1986,},
{min_level=1987,max_level=1987,},
{min_level=1988,max_level=1988,},
{min_level=1989,max_level=1989,},
{min_level=1990,max_level=1990,},
{min_level=1991,max_level=1991,},
{min_level=1992,max_level=1992,},
{min_level=1993,max_level=1993,},
{min_level=1994,max_level=1994,},
{min_level=1995,max_level=1995,},
{min_level=1996,max_level=1996,},
{min_level=1997,max_level=1997,},
{min_level=1998,max_level=1998,},
{min_level=1999,max_level=1999,},
{min_level=2000,max_level=2000,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,}
},

monster_attr_meta_table_map={
[6002]=2,	-- depth:1
[6027]=27,	-- depth:1
[6122]=122,	-- depth:1
[6000]=2000,	-- depth:1
[5941]=1941,	-- depth:1
[5999]=1999,	-- depth:1
[5989]=1989,	-- depth:1
[5996]=1996,	-- depth:1
[5997]=1997,	-- depth:1
[5995]=1995,	-- depth:1
[5994]=1994,	-- depth:1
[5993]=1993,	-- depth:1
[5992]=1992,	-- depth:1
[6003]=3,	-- depth:1
[5990]=1990,	-- depth:1
[5991]=1991,	-- depth:1
[5998]=1998,	-- depth:1
[6004]=4,	-- depth:1
[6016]=16,	-- depth:1
[6006]=6,	-- depth:1
[6026]=26,	-- depth:1
[6025]=25,	-- depth:1
[5988]=1988,	-- depth:1
[6024]=24,	-- depth:1
[6023]=23,	-- depth:1
[6022]=22,	-- depth:1
[6021]=21,	-- depth:1
[6020]=20,	-- depth:1
[6019]=19,	-- depth:1
[6005]=5,	-- depth:1
[6018]=18,	-- depth:1
[6015]=15,	-- depth:1
[6014]=14,	-- depth:1
[6013]=13,	-- depth:1
[6012]=12,	-- depth:1
[6011]=11,	-- depth:1
[6010]=10,	-- depth:1
[6009]=9,	-- depth:1
[6008]=8,	-- depth:1
[6007]=7,	-- depth:1
[6017]=17,	-- depth:1
[5987]=1987,	-- depth:1
[5960]=1960,	-- depth:1
[5985]=1985,	-- depth:1
[5959]=1959,	-- depth:1
[5958]=1958,	-- depth:1
[5957]=1957,	-- depth:1
[5956]=1956,	-- depth:1
[5955]=1955,	-- depth:1
[5954]=1954,	-- depth:1
[5953]=1953,	-- depth:1
[5952]=1952,	-- depth:1
[5951]=1951,	-- depth:1
[5950]=1950,	-- depth:1
[5949]=1949,	-- depth:1
[5948]=1948,	-- depth:1
[5947]=1947,	-- depth:1
[5946]=1946,	-- depth:1
[5945]=1945,	-- depth:1
[5944]=1944,	-- depth:1
[6028]=28,	-- depth:1
[5943]=1943,	-- depth:1
[5942]=1942,	-- depth:1
[5961]=1961,	-- depth:1
[5962]=1962,	-- depth:1
[5963]=1963,	-- depth:1
[5964]=1964,	-- depth:1
[5984]=1984,	-- depth:1
[5983]=1983,	-- depth:1
[5982]=1982,	-- depth:1
[5981]=1981,	-- depth:1
[5980]=1980,	-- depth:1
[5979]=1979,	-- depth:1
[5978]=1978,	-- depth:1
[5977]=1977,	-- depth:1
[5976]=1976,	-- depth:1
[5986]=1986,	-- depth:1
[5975]=1975,	-- depth:1
[5973]=1973,	-- depth:1
[5972]=1972,	-- depth:1
[5971]=1971,	-- depth:1
[5970]=1970,	-- depth:1
[5969]=1969,	-- depth:1
[5968]=1968,	-- depth:1
[5967]=1967,	-- depth:1
[5966]=1966,	-- depth:1
[5965]=1965,	-- depth:1
[5974]=1974,	-- depth:1
[6029]=29,	-- depth:1
[6088]=88,	-- depth:1
[6031]=31,	-- depth:1
[6098]=98,	-- depth:1
[6097]=97,	-- depth:1
[6096]=96,	-- depth:1
[6095]=95,	-- depth:1
[6094]=94,	-- depth:1
[6093]=93,	-- depth:1
[6092]=92,	-- depth:1
[6091]=91,	-- depth:1
[6090]=90,	-- depth:1
[6089]=89,	-- depth:1
[6087]=87,	-- depth:1
[6086]=86,	-- depth:1
[6085]=85,	-- depth:1
[6084]=84,	-- depth:1
[6083]=83,	-- depth:1
[6082]=82,	-- depth:1
[6081]=81,	-- depth:1
[6080]=80,	-- depth:1
[6079]=79,	-- depth:1
[6099]=99,	-- depth:1
[6100]=100,	-- depth:1
[6101]=101,	-- depth:1
[6102]=102,	-- depth:1
[6121]=121,	-- depth:1
[6120]=120,	-- depth:1
[5940]=1940,	-- depth:1
[6119]=119,	-- depth:1
[6118]=118,	-- depth:1
[6117]=117,	-- depth:1
[6116]=116,	-- depth:1
[6115]=115,	-- depth:1
[6114]=114,	-- depth:1
[6078]=78,	-- depth:1
[6113]=113,	-- depth:1
[6111]=111,	-- depth:1
[6110]=110,	-- depth:1
[6109]=109,	-- depth:1
[6108]=108,	-- depth:1
[6107]=107,	-- depth:1
[6106]=106,	-- depth:1
[6105]=105,	-- depth:1
[6104]=104,	-- depth:1
[6103]=103,	-- depth:1
[6112]=112,	-- depth:1
[6030]=30,	-- depth:1
[6077]=77,	-- depth:1
[6075]=75,	-- depth:1
[6050]=50,	-- depth:1
[6049]=49,	-- depth:1
[6048]=48,	-- depth:1
[6047]=47,	-- depth:1
[6046]=46,	-- depth:1
[6045]=45,	-- depth:1
[6044]=44,	-- depth:1
[6043]=43,	-- depth:1
[6042]=42,	-- depth:1
[6041]=41,	-- depth:1
[6040]=40,	-- depth:1
[6039]=39,	-- depth:1
[6038]=38,	-- depth:1
[6037]=37,	-- depth:1
[6036]=36,	-- depth:1
[6035]=35,	-- depth:1
[6034]=34,	-- depth:1
[6033]=33,	-- depth:1
[6032]=32,	-- depth:1
[6051]=51,	-- depth:1
[6052]=52,	-- depth:1
[6053]=53,	-- depth:1
[6054]=54,	-- depth:1
[6074]=74,	-- depth:1
[6073]=73,	-- depth:1
[6072]=72,	-- depth:1
[6071]=71,	-- depth:1
[6070]=70,	-- depth:1
[6069]=69,	-- depth:1
[6068]=68,	-- depth:1
[6067]=67,	-- depth:1
[6066]=66,	-- depth:1
[6076]=76,	-- depth:1
[6065]=65,	-- depth:1
[6063]=63,	-- depth:1
[6062]=62,	-- depth:1
[6061]=61,	-- depth:1
[6060]=60,	-- depth:1
[6059]=59,	-- depth:1
[6058]=58,	-- depth:1
[6057]=57,	-- depth:1
[6056]=56,	-- depth:1
[6055]=55,	-- depth:1
[6064]=64,	-- depth:1
[5939]=1939,	-- depth:1
[5817]=1817,	-- depth:1
[5937]=1937,	-- depth:1
[5818]=1818,	-- depth:1
[5816]=1816,	-- depth:1
[5815]=1815,	-- depth:1
[5814]=1814,	-- depth:1
[5813]=1813,	-- depth:1
[5812]=1812,	-- depth:1
[5811]=1811,	-- depth:1
[5810]=1810,	-- depth:1
[5809]=1809,	-- depth:1
[5808]=1808,	-- depth:1
[5807]=1807,	-- depth:1
[5806]=1806,	-- depth:1
[5805]=1805,	-- depth:1
[5804]=1804,	-- depth:1
[5803]=1803,	-- depth:1
[5802]=1802,	-- depth:1
[5801]=1801,	-- depth:1
[5800]=1800,	-- depth:1
[5799]=1799,	-- depth:1
[5819]=1819,	-- depth:1
[5820]=1820,	-- depth:1
[5821]=1821,	-- depth:1
[5822]=1822,	-- depth:1
[5842]=1842,	-- depth:1
[5841]=1841,	-- depth:1
[5840]=1840,	-- depth:1
[5839]=1839,	-- depth:1
[5838]=1838,	-- depth:1
[5837]=1837,	-- depth:1
[5836]=1836,	-- depth:1
[5835]=1835,	-- depth:1
[5834]=1834,	-- depth:1
[5798]=1798,	-- depth:1
[5833]=1833,	-- depth:1
[5831]=1831,	-- depth:1
[5830]=1830,	-- depth:1
[5829]=1829,	-- depth:1
[5828]=1828,	-- depth:1
[5827]=1827,	-- depth:1
[5826]=1826,	-- depth:1
[5825]=1825,	-- depth:1
[5824]=1824,	-- depth:1
[5823]=1823,	-- depth:1
[5832]=1832,	-- depth:1
[5843]=1843,	-- depth:1
[5797]=1797,	-- depth:1
[5795]=1795,	-- depth:1
[5770]=1770,	-- depth:1
[5769]=1769,	-- depth:1
[5768]=1768,	-- depth:1
[5767]=1767,	-- depth:1
[5766]=1766,	-- depth:1
[5765]=1765,	-- depth:1
[5764]=1764,	-- depth:1
[5763]=1763,	-- depth:1
[5762]=1762,	-- depth:1
[5761]=1761,	-- depth:1
[5760]=1760,	-- depth:1
[5759]=1759,	-- depth:1
[5758]=1758,	-- depth:1
[5757]=1757,	-- depth:1
[5756]=1756,	-- depth:1
[5755]=1755,	-- depth:1
[6123]=123,	-- depth:1
[5754]=1754,	-- depth:1
[5753]=1753,	-- depth:1
[5771]=1771,	-- depth:1
[5772]=1772,	-- depth:1
[5773]=1773,	-- depth:1
[5774]=1774,	-- depth:1
[5794]=1794,	-- depth:1
[5793]=1793,	-- depth:1
[5792]=1792,	-- depth:1
[5791]=1791,	-- depth:1
[5790]=1790,	-- depth:1
[5789]=1789,	-- depth:1
[5788]=1788,	-- depth:1
[5787]=1787,	-- depth:1
[5786]=1786,	-- depth:1
[5796]=1796,	-- depth:1
[5785]=1785,	-- depth:1
[5783]=1783,	-- depth:1
[5782]=1782,	-- depth:1
[5781]=1781,	-- depth:1
[5780]=1780,	-- depth:1
[5779]=1779,	-- depth:1
[5778]=1778,	-- depth:1
[5777]=1777,	-- depth:1
[5776]=1776,	-- depth:1
[5775]=1775,	-- depth:1
[5784]=1784,	-- depth:1
[5938]=1938,	-- depth:1
[5844]=1844,	-- depth:1
[5846]=1846,	-- depth:1
[5912]=1912,	-- depth:1
[5911]=1911,	-- depth:1
[5910]=1910,	-- depth:1
[5909]=1909,	-- depth:1
[5908]=1908,	-- depth:1
[5907]=1907,	-- depth:1
[5906]=1906,	-- depth:1
[5905]=1905,	-- depth:1
[5904]=1904,	-- depth:1
[5903]=1903,	-- depth:1
[5902]=1902,	-- depth:1
[5901]=1901,	-- depth:1
[5900]=1900,	-- depth:1
[5899]=1899,	-- depth:1
[5898]=1898,	-- depth:1
[5897]=1897,	-- depth:1
[5896]=1896,	-- depth:1
[5895]=1895,	-- depth:1
[5894]=1894,	-- depth:1
[5913]=1913,	-- depth:1
[5914]=1914,	-- depth:1
[5915]=1915,	-- depth:1
[5916]=1916,	-- depth:1
[5936]=1936,	-- depth:1
[5935]=1935,	-- depth:1
[5934]=1934,	-- depth:1
[5933]=1933,	-- depth:1
[5932]=1932,	-- depth:1
[5931]=1931,	-- depth:1
[5930]=1930,	-- depth:1
[5929]=1929,	-- depth:1
[5928]=1928,	-- depth:1
[5893]=1893,	-- depth:1
[5927]=1927,	-- depth:1
[5925]=1925,	-- depth:1
[5924]=1924,	-- depth:1
[5923]=1923,	-- depth:1
[5922]=1922,	-- depth:1
[5921]=1921,	-- depth:1
[5920]=1920,	-- depth:1
[5919]=1919,	-- depth:1
[5918]=1918,	-- depth:1
[5917]=1917,	-- depth:1
[5926]=1926,	-- depth:1
[5845]=1845,	-- depth:1
[5892]=1892,	-- depth:1
[5890]=1890,	-- depth:1
[5865]=1865,	-- depth:1
[5864]=1864,	-- depth:1
[5863]=1863,	-- depth:1
[5862]=1862,	-- depth:1
[5861]=1861,	-- depth:1
[5860]=1860,	-- depth:1
[5859]=1859,	-- depth:1
[5858]=1858,	-- depth:1
[5857]=1857,	-- depth:1
[5856]=1856,	-- depth:1
[5855]=1855,	-- depth:1
[5854]=1854,	-- depth:1
[5853]=1853,	-- depth:1
[5852]=1852,	-- depth:1
[5851]=1851,	-- depth:1
[5850]=1850,	-- depth:1
[5849]=1849,	-- depth:1
[5848]=1848,	-- depth:1
[5847]=1847,	-- depth:1
[5866]=1866,	-- depth:1
[5867]=1867,	-- depth:1
[5868]=1868,	-- depth:1
[5869]=1869,	-- depth:1
[5889]=1889,	-- depth:1
[5888]=1888,	-- depth:1
[5887]=1887,	-- depth:1
[5886]=1886,	-- depth:1
[5885]=1885,	-- depth:1
[5884]=1884,	-- depth:1
[5883]=1883,	-- depth:1
[5882]=1882,	-- depth:1
[5881]=1881,	-- depth:1
[5891]=1891,	-- depth:1
[5880]=1880,	-- depth:1
[5878]=1878,	-- depth:1
[5877]=1877,	-- depth:1
[5876]=1876,	-- depth:1
[5875]=1875,	-- depth:1
[5874]=1874,	-- depth:1
[5873]=1873,	-- depth:1
[5872]=1872,	-- depth:1
[5871]=1871,	-- depth:1
[5870]=1870,	-- depth:1
[5879]=1879,	-- depth:1
[6124]=124,	-- depth:1
[6372]=372,	-- depth:1
[6126]=126,	-- depth:1
[6381]=381,	-- depth:1
[6380]=380,	-- depth:1
[6379]=379,	-- depth:1
[6378]=378,	-- depth:1
[6377]=377,	-- depth:1
[6376]=376,	-- depth:1
[6375]=375,	-- depth:1
[6374]=374,	-- depth:1
[6373]=373,	-- depth:1
[6371]=371,	-- depth:1
[6370]=370,	-- depth:1
[6369]=369,	-- depth:1
[6368]=368,	-- depth:1
[6367]=367,	-- depth:1
[6366]=366,	-- depth:1
[6365]=365,	-- depth:1
[6364]=364,	-- depth:1
[6363]=363,	-- depth:1
[6362]=362,	-- depth:1
[6382]=382,	-- depth:1
[6361]=361,	-- depth:1
[6383]=383,	-- depth:1
[6385]=385,	-- depth:1
[6404]=404,	-- depth:1
[6403]=403,	-- depth:1
[6402]=402,	-- depth:1
[6401]=401,	-- depth:1
[6400]=400,	-- depth:1
[6399]=399,	-- depth:1
[6398]=398,	-- depth:1
[6397]=397,	-- depth:1
[6396]=396,	-- depth:1
[6395]=395,	-- depth:1
[6394]=394,	-- depth:1
[6393]=393,	-- depth:1
[6392]=392,	-- depth:1
[6391]=391,	-- depth:1
[6390]=390,	-- depth:1
[6389]=389,	-- depth:1
[6388]=388,	-- depth:1
[6387]=387,	-- depth:1
[6386]=386,	-- depth:1
[6384]=384,	-- depth:1
[6360]=360,	-- depth:1
[6359]=359,	-- depth:1
[6358]=358,	-- depth:1
[6333]=333,	-- depth:1
[6332]=332,	-- depth:1
[6331]=331,	-- depth:1
[6330]=330,	-- depth:1
[6329]=329,	-- depth:1
[6328]=328,	-- depth:1
[6327]=327,	-- depth:1
[6326]=326,	-- depth:1
[6325]=325,	-- depth:1
[6324]=324,	-- depth:1
[6323]=323,	-- depth:1
[6322]=322,	-- depth:1
[6321]=321,	-- depth:1
[6320]=320,	-- depth:1
[6319]=319,	-- depth:1
[6318]=318,	-- depth:1
[6317]=317,	-- depth:1
[6316]=316,	-- depth:1
[6315]=315,	-- depth:1
[6334]=334,	-- depth:1
[6335]=335,	-- depth:1
[6336]=336,	-- depth:1
[6337]=337,	-- depth:1
[6357]=357,	-- depth:1
[6356]=356,	-- depth:1
[6355]=355,	-- depth:1
[6354]=354,	-- depth:1
[6353]=353,	-- depth:1
[6352]=352,	-- depth:1
[6351]=351,	-- depth:1
[6350]=350,	-- depth:1
[6349]=349,	-- depth:1
[6405]=405,	-- depth:1
[6348]=348,	-- depth:1
[6346]=346,	-- depth:1
[6345]=345,	-- depth:1
[6344]=344,	-- depth:1
[6343]=343,	-- depth:1
[6342]=342,	-- depth:1
[6341]=341,	-- depth:1
[6340]=340,	-- depth:1
[6339]=339,	-- depth:1
[6338]=338,	-- depth:1
[6347]=347,	-- depth:1
[6406]=406,	-- depth:1
[6407]=407,	-- depth:1
[6408]=408,	-- depth:1
[6474]=474,	-- depth:1
[6473]=473,	-- depth:1
[6472]=472,	-- depth:1
[6471]=471,	-- depth:1
[6470]=470,	-- depth:1
[6469]=469,	-- depth:1
[6468]=468,	-- depth:1
[6467]=467,	-- depth:1
[6466]=466,	-- depth:1
[6465]=465,	-- depth:1
[6464]=464,	-- depth:1
[6463]=463,	-- depth:1
[6462]=462,	-- depth:1
[6461]=461,	-- depth:1
[6460]=460,	-- depth:1
[6459]=459,	-- depth:1
[6458]=458,	-- depth:1
[6457]=457,	-- depth:1
[6456]=456,	-- depth:1
[6475]=475,	-- depth:1
[6476]=476,	-- depth:1
[6477]=477,	-- depth:1
[6478]=478,	-- depth:1
[6497]=497,	-- depth:1
[5752]=1752,	-- depth:1
[6496]=496,	-- depth:1
[6495]=495,	-- depth:1
[6494]=494,	-- depth:1
[6493]=493,	-- depth:1
[6492]=492,	-- depth:1
[6491]=491,	-- depth:1
[6490]=490,	-- depth:1
[6455]=455,	-- depth:1
[6489]=489,	-- depth:1
[6487]=487,	-- depth:1
[6486]=486,	-- depth:1
[6485]=485,	-- depth:1
[6484]=484,	-- depth:1
[6483]=483,	-- depth:1
[6482]=482,	-- depth:1
[6481]=481,	-- depth:1
[6480]=480,	-- depth:1
[6479]=479,	-- depth:1
[6488]=488,	-- depth:1
[6314]=314,	-- depth:1
[6454]=454,	-- depth:1
[6452]=452,	-- depth:1
[6427]=427,	-- depth:1
[6426]=426,	-- depth:1
[6425]=425,	-- depth:1
[6424]=424,	-- depth:1
[6423]=423,	-- depth:1
[6422]=422,	-- depth:1
[6421]=421,	-- depth:1
[6420]=420,	-- depth:1
[6419]=419,	-- depth:1
[6418]=418,	-- depth:1
[6417]=417,	-- depth:1
[6416]=416,	-- depth:1
[6415]=415,	-- depth:1
[6414]=414,	-- depth:1
[6413]=413,	-- depth:1
[6412]=412,	-- depth:1
[6411]=411,	-- depth:1
[6410]=410,	-- depth:1
[6409]=409,	-- depth:1
[6428]=428,	-- depth:1
[6429]=429,	-- depth:1
[6430]=430,	-- depth:1
[6431]=431,	-- depth:1
[6451]=451,	-- depth:1
[6450]=450,	-- depth:1
[6449]=449,	-- depth:1
[6448]=448,	-- depth:1
[6447]=447,	-- depth:1
[6446]=446,	-- depth:1
[6445]=445,	-- depth:1
[6444]=444,	-- depth:1
[6443]=443,	-- depth:1
[6453]=453,	-- depth:1
[6442]=442,	-- depth:1
[6440]=440,	-- depth:1
[6439]=439,	-- depth:1
[6438]=438,	-- depth:1
[6437]=437,	-- depth:1
[6436]=436,	-- depth:1
[6435]=435,	-- depth:1
[6434]=434,	-- depth:1
[6433]=433,	-- depth:1
[6432]=432,	-- depth:1
[6441]=441,	-- depth:1
[6313]=313,	-- depth:1
[6312]=312,	-- depth:1
[6311]=311,	-- depth:1
[6192]=192,	-- depth:1
[6191]=191,	-- depth:1
[6190]=190,	-- depth:1
[6189]=189,	-- depth:1
[6188]=188,	-- depth:1
[6187]=187,	-- depth:1
[6186]=186,	-- depth:1
[6185]=185,	-- depth:1
[6184]=184,	-- depth:1
[6183]=183,	-- depth:1
[6182]=182,	-- depth:1
[6181]=181,	-- depth:1
[6180]=180,	-- depth:1
[6179]=179,	-- depth:1
[6178]=178,	-- depth:1
[6177]=177,	-- depth:1
[6176]=176,	-- depth:1
[6175]=175,	-- depth:1
[6174]=174,	-- depth:1
[6193]=193,	-- depth:1
[6194]=194,	-- depth:1
[6195]=195,	-- depth:1
[6196]=196,	-- depth:1
[6216]=216,	-- depth:1
[6215]=215,	-- depth:1
[6214]=214,	-- depth:1
[6213]=213,	-- depth:1
[6212]=212,	-- depth:1
[6211]=211,	-- depth:1
[6210]=210,	-- depth:1
[6209]=209,	-- depth:1
[6208]=208,	-- depth:1
[6173]=173,	-- depth:1
[6207]=207,	-- depth:1
[6205]=205,	-- depth:1
[6204]=204,	-- depth:1
[6203]=203,	-- depth:1
[6202]=202,	-- depth:1
[6201]=201,	-- depth:1
[6200]=200,	-- depth:1
[6199]=199,	-- depth:1
[6198]=198,	-- depth:1
[6197]=197,	-- depth:1
[6206]=206,	-- depth:1
[6217]=217,	-- depth:1
[6172]=172,	-- depth:1
[6170]=170,	-- depth:1
[6145]=145,	-- depth:1
[6144]=144,	-- depth:1
[6143]=143,	-- depth:1
[6142]=142,	-- depth:1
[6141]=141,	-- depth:1
[6140]=140,	-- depth:1
[6139]=139,	-- depth:1
[6138]=138,	-- depth:1
[6137]=137,	-- depth:1
[6136]=136,	-- depth:1
[6135]=135,	-- depth:1
[6134]=134,	-- depth:1
[6133]=133,	-- depth:1
[6132]=132,	-- depth:1
[6131]=131,	-- depth:1
[6130]=130,	-- depth:1
[6129]=129,	-- depth:1
[6128]=128,	-- depth:1
[6127]=127,	-- depth:1
[6146]=146,	-- depth:1
[6147]=147,	-- depth:1
[6148]=148,	-- depth:1
[6149]=149,	-- depth:1
[6169]=169,	-- depth:1
[6168]=168,	-- depth:1
[6167]=167,	-- depth:1
[6166]=166,	-- depth:1
[6165]=165,	-- depth:1
[6164]=164,	-- depth:1
[6163]=163,	-- depth:1
[6162]=162,	-- depth:1
[6161]=161,	-- depth:1
[6171]=171,	-- depth:1
[6160]=160,	-- depth:1
[6158]=158,	-- depth:1
[6157]=157,	-- depth:1
[6156]=156,	-- depth:1
[6155]=155,	-- depth:1
[6154]=154,	-- depth:1
[6153]=153,	-- depth:1
[6152]=152,	-- depth:1
[6151]=151,	-- depth:1
[6150]=150,	-- depth:1
[6159]=159,	-- depth:1
[6125]=125,	-- depth:1
[6218]=218,	-- depth:1
[6220]=220,	-- depth:1
[6286]=286,	-- depth:1
[6285]=285,	-- depth:1
[6284]=284,	-- depth:1
[6283]=283,	-- depth:1
[6282]=282,	-- depth:1
[6281]=281,	-- depth:1
[6280]=280,	-- depth:1
[6279]=279,	-- depth:1
[6278]=278,	-- depth:1
[6277]=277,	-- depth:1
[6276]=276,	-- depth:1
[6275]=275,	-- depth:1
[6274]=274,	-- depth:1
[6273]=273,	-- depth:1
[6272]=272,	-- depth:1
[6271]=271,	-- depth:1
[6270]=270,	-- depth:1
[6269]=269,	-- depth:1
[6268]=268,	-- depth:1
[6287]=287,	-- depth:1
[6288]=288,	-- depth:1
[6289]=289,	-- depth:1
[6290]=290,	-- depth:1
[6310]=310,	-- depth:1
[6309]=309,	-- depth:1
[6308]=308,	-- depth:1
[6307]=307,	-- depth:1
[6306]=306,	-- depth:1
[6305]=305,	-- depth:1
[6304]=304,	-- depth:1
[6303]=303,	-- depth:1
[6302]=302,	-- depth:1
[6267]=267,	-- depth:1
[6301]=301,	-- depth:1
[6299]=299,	-- depth:1
[6298]=298,	-- depth:1
[6297]=297,	-- depth:1
[6296]=296,	-- depth:1
[6295]=295,	-- depth:1
[6294]=294,	-- depth:1
[6293]=293,	-- depth:1
[6292]=292,	-- depth:1
[6291]=291,	-- depth:1
[6300]=300,	-- depth:1
[6219]=219,	-- depth:1
[6266]=266,	-- depth:1
[6264]=264,	-- depth:1
[6239]=239,	-- depth:1
[6238]=238,	-- depth:1
[6237]=237,	-- depth:1
[6236]=236,	-- depth:1
[6235]=235,	-- depth:1
[6234]=234,	-- depth:1
[6233]=233,	-- depth:1
[6232]=232,	-- depth:1
[6231]=231,	-- depth:1
[6230]=230,	-- depth:1
[6229]=229,	-- depth:1
[6228]=228,	-- depth:1
[6227]=227,	-- depth:1
[6226]=226,	-- depth:1
[6225]=225,	-- depth:1
[6224]=224,	-- depth:1
[6223]=223,	-- depth:1
[6222]=222,	-- depth:1
[6221]=221,	-- depth:1
[6240]=240,	-- depth:1
[6241]=241,	-- depth:1
[6242]=242,	-- depth:1
[6243]=243,	-- depth:1
[6263]=263,	-- depth:1
[6262]=262,	-- depth:1
[6261]=261,	-- depth:1
[6260]=260,	-- depth:1
[6259]=259,	-- depth:1
[6258]=258,	-- depth:1
[6257]=257,	-- depth:1
[6256]=256,	-- depth:1
[6255]=255,	-- depth:1
[6265]=265,	-- depth:1
[6254]=254,	-- depth:1
[6252]=252,	-- depth:1
[6251]=251,	-- depth:1
[6250]=250,	-- depth:1
[6249]=249,	-- depth:1
[6248]=248,	-- depth:1
[6247]=247,	-- depth:1
[6246]=246,	-- depth:1
[6245]=245,	-- depth:1
[6244]=244,	-- depth:1
[6253]=253,	-- depth:1
[5751]=1751,	-- depth:1
[5252]=1252,	-- depth:1
[5749]=1749,	-- depth:1
[5256]=1256,	-- depth:1
[5255]=1255,	-- depth:1
[5254]=1254,	-- depth:1
[5253]=1253,	-- depth:1
[5251]=1251,	-- depth:1
[5250]=1250,	-- depth:1
[5249]=1249,	-- depth:1
[5248]=1248,	-- depth:1
[5247]=1247,	-- depth:1
[5246]=1246,	-- depth:1
[5245]=1245,	-- depth:1
[5244]=1244,	-- depth:1
[5243]=1243,	-- depth:1
[5242]=1242,	-- depth:1
[5241]=1241,	-- depth:1
[5240]=1240,	-- depth:1
[5239]=1239,	-- depth:1
[5238]=1238,	-- depth:1
[5237]=1237,	-- depth:1
[5257]=1257,	-- depth:1
[5236]=1236,	-- depth:1
[5258]=1258,	-- depth:1
[5260]=1260,	-- depth:1
[5279]=1279,	-- depth:1
[5278]=1278,	-- depth:1
[5277]=1277,	-- depth:1
[5276]=1276,	-- depth:1
[5275]=1275,	-- depth:1
[5274]=1274,	-- depth:1
[5273]=1273,	-- depth:1
[5272]=1272,	-- depth:1
[5271]=1271,	-- depth:1
[5270]=1270,	-- depth:1
[5269]=1269,	-- depth:1
[5268]=1268,	-- depth:1
[5267]=1267,	-- depth:1
[5266]=1266,	-- depth:1
[5265]=1265,	-- depth:1
[5264]=1264,	-- depth:1
[5263]=1263,	-- depth:1
[5262]=1262,	-- depth:1
[5261]=1261,	-- depth:1
[5259]=1259,	-- depth:1
[5235]=1235,	-- depth:1
[5234]=1234,	-- depth:1
[5233]=1233,	-- depth:1
[5208]=1208,	-- depth:1
[5207]=1207,	-- depth:1
[5206]=1206,	-- depth:1
[5205]=1205,	-- depth:1
[5204]=1204,	-- depth:1
[5203]=1203,	-- depth:1
[5202]=1202,	-- depth:1
[5201]=1201,	-- depth:1
[5200]=1200,	-- depth:1
[5199]=1199,	-- depth:1
[5198]=1198,	-- depth:1
[5197]=1197,	-- depth:1
[5196]=1196,	-- depth:1
[5195]=1195,	-- depth:1
[5194]=1194,	-- depth:1
[5193]=1193,	-- depth:1
[5192]=1192,	-- depth:1
[5191]=1191,	-- depth:1
[5190]=1190,	-- depth:1
[5209]=1209,	-- depth:1
[5210]=1210,	-- depth:1
[5211]=1211,	-- depth:1
[5212]=1212,	-- depth:1
[5232]=1232,	-- depth:1
[5231]=1231,	-- depth:1
[5230]=1230,	-- depth:1
[5229]=1229,	-- depth:1
[5228]=1228,	-- depth:1
[5227]=1227,	-- depth:1
[5226]=1226,	-- depth:1
[5225]=1225,	-- depth:1
[5224]=1224,	-- depth:1
[5280]=1280,	-- depth:1
[5223]=1223,	-- depth:1
[5221]=1221,	-- depth:1
[5220]=1220,	-- depth:1
[5219]=1219,	-- depth:1
[5218]=1218,	-- depth:1
[5217]=1217,	-- depth:1
[5216]=1216,	-- depth:1
[5215]=1215,	-- depth:1
[5214]=1214,	-- depth:1
[5213]=1213,	-- depth:1
[5222]=1222,	-- depth:1
[5281]=1281,	-- depth:1
[5282]=1282,	-- depth:1
[5283]=1283,	-- depth:1
[5349]=1349,	-- depth:1
[5348]=1348,	-- depth:1
[5347]=1347,	-- depth:1
[5346]=1346,	-- depth:1
[5345]=1345,	-- depth:1
[5344]=1344,	-- depth:1
[5343]=1343,	-- depth:1
[5342]=1342,	-- depth:1
[5341]=1341,	-- depth:1
[5340]=1340,	-- depth:1
[5339]=1339,	-- depth:1
[5338]=1338,	-- depth:1
[5337]=1337,	-- depth:1
[5336]=1336,	-- depth:1
[5335]=1335,	-- depth:1
[5334]=1334,	-- depth:1
[5333]=1333,	-- depth:1
[5332]=1332,	-- depth:1
[5331]=1331,	-- depth:1
[5350]=1350,	-- depth:1
[5351]=1351,	-- depth:1
[5352]=1352,	-- depth:1
[5353]=1353,	-- depth:1
[5373]=1373,	-- depth:1
[5372]=1372,	-- depth:1
[5371]=1371,	-- depth:1
[5370]=1370,	-- depth:1
[5369]=1369,	-- depth:1
[5368]=1368,	-- depth:1
[5367]=1367,	-- depth:1
[5366]=1366,	-- depth:1
[5365]=1365,	-- depth:1
[5330]=1330,	-- depth:1
[5364]=1364,	-- depth:1
[5362]=1362,	-- depth:1
[5361]=1361,	-- depth:1
[5360]=1360,	-- depth:1
[5359]=1359,	-- depth:1
[5358]=1358,	-- depth:1
[5357]=1357,	-- depth:1
[5356]=1356,	-- depth:1
[5355]=1355,	-- depth:1
[5354]=1354,	-- depth:1
[5363]=1363,	-- depth:1
[5189]=1189,	-- depth:1
[5329]=1329,	-- depth:1
[5327]=1327,	-- depth:1
[5302]=1302,	-- depth:1
[5301]=1301,	-- depth:1
[5300]=1300,	-- depth:1
[5299]=1299,	-- depth:1
[5298]=1298,	-- depth:1
[5297]=1297,	-- depth:1
[5296]=1296,	-- depth:1
[5295]=1295,	-- depth:1
[5294]=1294,	-- depth:1
[5293]=1293,	-- depth:1
[5292]=1292,	-- depth:1
[5291]=1291,	-- depth:1
[5290]=1290,	-- depth:1
[5289]=1289,	-- depth:1
[5288]=1288,	-- depth:1
[5287]=1287,	-- depth:1
[5286]=1286,	-- depth:1
[5285]=1285,	-- depth:1
[5284]=1284,	-- depth:1
[5303]=1303,	-- depth:1
[5304]=1304,	-- depth:1
[5305]=1305,	-- depth:1
[5306]=1306,	-- depth:1
[5326]=1326,	-- depth:1
[5325]=1325,	-- depth:1
[5324]=1324,	-- depth:1
[5323]=1323,	-- depth:1
[5322]=1322,	-- depth:1
[5321]=1321,	-- depth:1
[5320]=1320,	-- depth:1
[5319]=1319,	-- depth:1
[5318]=1318,	-- depth:1
[5328]=1328,	-- depth:1
[5317]=1317,	-- depth:1
[5315]=1315,	-- depth:1
[5314]=1314,	-- depth:1
[5313]=1313,	-- depth:1
[5312]=1312,	-- depth:1
[5311]=1311,	-- depth:1
[5310]=1310,	-- depth:1
[5309]=1309,	-- depth:1
[5308]=1308,	-- depth:1
[5307]=1307,	-- depth:1
[5316]=1316,	-- depth:1
[5188]=1188,	-- depth:1
[5187]=1187,	-- depth:1
[5186]=1186,	-- depth:1
[5067]=1067,	-- depth:1
[5066]=1066,	-- depth:1
[5065]=1065,	-- depth:1
[5064]=1064,	-- depth:1
[5063]=1063,	-- depth:1
[5062]=1062,	-- depth:1
[5061]=1061,	-- depth:1
[5060]=1060,	-- depth:1
[5059]=1059,	-- depth:1
[5058]=1058,	-- depth:1
[5057]=1057,	-- depth:1
[5056]=1056,	-- depth:1
[5055]=1055,	-- depth:1
[5054]=1054,	-- depth:1
[5053]=1053,	-- depth:1
[5052]=1052,	-- depth:1
[5051]=1051,	-- depth:1
[5050]=1050,	-- depth:1
[5049]=1049,	-- depth:1
[5068]=1068,	-- depth:1
[5069]=1069,	-- depth:1
[5070]=1070,	-- depth:1
[5071]=1071,	-- depth:1
[5091]=1091,	-- depth:1
[5090]=1090,	-- depth:1
[5089]=1089,	-- depth:1
[5088]=1088,	-- depth:1
[5087]=1087,	-- depth:1
[5086]=1086,	-- depth:1
[5085]=1085,	-- depth:1
[5084]=1084,	-- depth:1
[5083]=1083,	-- depth:1
[5048]=1048,	-- depth:1
[5082]=1082,	-- depth:1
[5080]=1080,	-- depth:1
[5079]=1079,	-- depth:1
[5078]=1078,	-- depth:1
[5077]=1077,	-- depth:1
[5076]=1076,	-- depth:1
[5075]=1075,	-- depth:1
[5074]=1074,	-- depth:1
[5073]=1073,	-- depth:1
[5072]=1072,	-- depth:1
[5081]=1081,	-- depth:1
[5092]=1092,	-- depth:1
[5047]=1047,	-- depth:1
[5045]=1045,	-- depth:1
[5020]=1020,	-- depth:1
[5019]=1019,	-- depth:1
[5018]=1018,	-- depth:1
[5017]=1017,	-- depth:1
[5016]=1016,	-- depth:1
[5015]=1015,	-- depth:1
[5014]=1014,	-- depth:1
[5013]=1013,	-- depth:1
[5012]=1012,	-- depth:1
[5011]=1011,	-- depth:1
[5010]=1010,	-- depth:1
[5009]=1009,	-- depth:1
[5008]=1008,	-- depth:1
[5007]=1007,	-- depth:1
[5006]=1006,	-- depth:1
[5005]=1005,	-- depth:1
[5004]=1004,	-- depth:1
[5003]=1003,	-- depth:1
[6498]=498,	-- depth:1
[5021]=1021,	-- depth:1
[5022]=1022,	-- depth:1
[5023]=1023,	-- depth:1
[5024]=1024,	-- depth:1
[5044]=1044,	-- depth:1
[5043]=1043,	-- depth:1
[5042]=1042,	-- depth:1
[5041]=1041,	-- depth:1
[5040]=1040,	-- depth:1
[5039]=1039,	-- depth:1
[5038]=1038,	-- depth:1
[5037]=1037,	-- depth:1
[5036]=1036,	-- depth:1
[5046]=1046,	-- depth:1
[5035]=1035,	-- depth:1
[5033]=1033,	-- depth:1
[5032]=1032,	-- depth:1
[5031]=1031,	-- depth:1
[5030]=1030,	-- depth:1
[5029]=1029,	-- depth:1
[5028]=1028,	-- depth:1
[5027]=1027,	-- depth:1
[5026]=1026,	-- depth:1
[5025]=1025,	-- depth:1
[5034]=1034,	-- depth:1
[5374]=1374,	-- depth:1
[5093]=1093,	-- depth:1
[5095]=1095,	-- depth:1
[5161]=1161,	-- depth:1
[5160]=1160,	-- depth:1
[5159]=1159,	-- depth:1
[5158]=1158,	-- depth:1
[5157]=1157,	-- depth:1
[5156]=1156,	-- depth:1
[5155]=1155,	-- depth:1
[5154]=1154,	-- depth:1
[5153]=1153,	-- depth:1
[5152]=1152,	-- depth:1
[5151]=1151,	-- depth:1
[5150]=1150,	-- depth:1
[5149]=1149,	-- depth:1
[5148]=1148,	-- depth:1
[5147]=1147,	-- depth:1
[5146]=1146,	-- depth:1
[5145]=1145,	-- depth:1
[5144]=1144,	-- depth:1
[5143]=1143,	-- depth:1
[5162]=1162,	-- depth:1
[5163]=1163,	-- depth:1
[5164]=1164,	-- depth:1
[5165]=1165,	-- depth:1
[5185]=1185,	-- depth:1
[5184]=1184,	-- depth:1
[5183]=1183,	-- depth:1
[5182]=1182,	-- depth:1
[5181]=1181,	-- depth:1
[5180]=1180,	-- depth:1
[5179]=1179,	-- depth:1
[5178]=1178,	-- depth:1
[5177]=1177,	-- depth:1
[5142]=1142,	-- depth:1
[5176]=1176,	-- depth:1
[5174]=1174,	-- depth:1
[5173]=1173,	-- depth:1
[5172]=1172,	-- depth:1
[5171]=1171,	-- depth:1
[5170]=1170,	-- depth:1
[5169]=1169,	-- depth:1
[5168]=1168,	-- depth:1
[5167]=1167,	-- depth:1
[5166]=1166,	-- depth:1
[5175]=1175,	-- depth:1
[5094]=1094,	-- depth:1
[5141]=1141,	-- depth:1
[5139]=1139,	-- depth:1
[5114]=1114,	-- depth:1
[5113]=1113,	-- depth:1
[5112]=1112,	-- depth:1
[5111]=1111,	-- depth:1
[5110]=1110,	-- depth:1
[5109]=1109,	-- depth:1
[5108]=1108,	-- depth:1
[5107]=1107,	-- depth:1
[5106]=1106,	-- depth:1
[5105]=1105,	-- depth:1
[5104]=1104,	-- depth:1
[5103]=1103,	-- depth:1
[5102]=1102,	-- depth:1
[5101]=1101,	-- depth:1
[5100]=1100,	-- depth:1
[5099]=1099,	-- depth:1
[5098]=1098,	-- depth:1
[5097]=1097,	-- depth:1
[5096]=1096,	-- depth:1
[5115]=1115,	-- depth:1
[5116]=1116,	-- depth:1
[5117]=1117,	-- depth:1
[5118]=1118,	-- depth:1
[5138]=1138,	-- depth:1
[5137]=1137,	-- depth:1
[5136]=1136,	-- depth:1
[5135]=1135,	-- depth:1
[5134]=1134,	-- depth:1
[5133]=1133,	-- depth:1
[5132]=1132,	-- depth:1
[5131]=1131,	-- depth:1
[5130]=1130,	-- depth:1
[5140]=1140,	-- depth:1
[5129]=1129,	-- depth:1
[5127]=1127,	-- depth:1
[5126]=1126,	-- depth:1
[5125]=1125,	-- depth:1
[5124]=1124,	-- depth:1
[5123]=1123,	-- depth:1
[5122]=1122,	-- depth:1
[5121]=1121,	-- depth:1
[5120]=1120,	-- depth:1
[5119]=1119,	-- depth:1
[5128]=1128,	-- depth:1
[5750]=1750,	-- depth:1
[5375]=1375,	-- depth:1
[5377]=1377,	-- depth:1
[5631]=1631,	-- depth:1
[5630]=1630,	-- depth:1
[5629]=1629,	-- depth:1
[5628]=1628,	-- depth:1
[5627]=1627,	-- depth:1
[5626]=1626,	-- depth:1
[5625]=1625,	-- depth:1
[5624]=1624,	-- depth:1
[5623]=1623,	-- depth:1
[5622]=1622,	-- depth:1
[5621]=1621,	-- depth:1
[5620]=1620,	-- depth:1
[5619]=1619,	-- depth:1
[5618]=1618,	-- depth:1
[5617]=1617,	-- depth:1
[5616]=1616,	-- depth:1
[5615]=1615,	-- depth:1
[5614]=1614,	-- depth:1
[5613]=1613,	-- depth:1
[5632]=1632,	-- depth:1
[5612]=1612,	-- depth:1
[5633]=1633,	-- depth:1
[5635]=1635,	-- depth:1
[5654]=1654,	-- depth:1
[5653]=1653,	-- depth:1
[5652]=1652,	-- depth:1
[5651]=1651,	-- depth:1
[5650]=1650,	-- depth:1
[5649]=1649,	-- depth:1
[5648]=1648,	-- depth:1
[5647]=1647,	-- depth:1
[5646]=1646,	-- depth:1
[5645]=1645,	-- depth:1
[5644]=1644,	-- depth:1
[5643]=1643,	-- depth:1
[5642]=1642,	-- depth:1
[5641]=1641,	-- depth:1
[5640]=1640,	-- depth:1
[5639]=1639,	-- depth:1
[5638]=1638,	-- depth:1
[5637]=1637,	-- depth:1
[5636]=1636,	-- depth:1
[5634]=1634,	-- depth:1
[5611]=1611,	-- depth:1
[5610]=1610,	-- depth:1
[5609]=1609,	-- depth:1
[5584]=1584,	-- depth:1
[5583]=1583,	-- depth:1
[5582]=1582,	-- depth:1
[5581]=1581,	-- depth:1
[5580]=1580,	-- depth:1
[5579]=1579,	-- depth:1
[5578]=1578,	-- depth:1
[5577]=1577,	-- depth:1
[5576]=1576,	-- depth:1
[5575]=1575,	-- depth:1
[5574]=1574,	-- depth:1
[5573]=1573,	-- depth:1
[5572]=1572,	-- depth:1
[5571]=1571,	-- depth:1
[5570]=1570,	-- depth:1
[5569]=1569,	-- depth:1
[5568]=1568,	-- depth:1
[5567]=1567,	-- depth:1
[5566]=1566,	-- depth:1
[5585]=1585,	-- depth:1
[5586]=1586,	-- depth:1
[5587]=1587,	-- depth:1
[5588]=1588,	-- depth:1
[5608]=1608,	-- depth:1
[5607]=1607,	-- depth:1
[5606]=1606,	-- depth:1
[5605]=1605,	-- depth:1
[5604]=1604,	-- depth:1
[5603]=1603,	-- depth:1
[5602]=1602,	-- depth:1
[5601]=1601,	-- depth:1
[5600]=1600,	-- depth:1
[5655]=1655,	-- depth:1
[5599]=1599,	-- depth:1
[5597]=1597,	-- depth:1
[5596]=1596,	-- depth:1
[5595]=1595,	-- depth:1
[5594]=1594,	-- depth:1
[5593]=1593,	-- depth:1
[5592]=1592,	-- depth:1
[5591]=1591,	-- depth:1
[5590]=1590,	-- depth:1
[5589]=1589,	-- depth:1
[5598]=1598,	-- depth:1
[5656]=1656,	-- depth:1
[5657]=1657,	-- depth:1
[5658]=1658,	-- depth:1
[5724]=1724,	-- depth:1
[5723]=1723,	-- depth:1
[5722]=1722,	-- depth:1
[5721]=1721,	-- depth:1
[5720]=1720,	-- depth:1
[5719]=1719,	-- depth:1
[5718]=1718,	-- depth:1
[5717]=1717,	-- depth:1
[5716]=1716,	-- depth:1
[5715]=1715,	-- depth:1
[5714]=1714,	-- depth:1
[5713]=1713,	-- depth:1
[5712]=1712,	-- depth:1
[5711]=1711,	-- depth:1
[5710]=1710,	-- depth:1
[5709]=1709,	-- depth:1
[5708]=1708,	-- depth:1
[5707]=1707,	-- depth:1
[5706]=1706,	-- depth:1
[5725]=1725,	-- depth:1
[5726]=1726,	-- depth:1
[5727]=1727,	-- depth:1
[5728]=1728,	-- depth:1
[5748]=1748,	-- depth:1
[5747]=1747,	-- depth:1
[5746]=1746,	-- depth:1
[5745]=1745,	-- depth:1
[5744]=1744,	-- depth:1
[5743]=1743,	-- depth:1
[5742]=1742,	-- depth:1
[5741]=1741,	-- depth:1
[5740]=1740,	-- depth:1
[5705]=1705,	-- depth:1
[5739]=1739,	-- depth:1
[5737]=1737,	-- depth:1
[5736]=1736,	-- depth:1
[5735]=1735,	-- depth:1
[5734]=1734,	-- depth:1
[5733]=1733,	-- depth:1
[5732]=1732,	-- depth:1
[5731]=1731,	-- depth:1
[5730]=1730,	-- depth:1
[5729]=1729,	-- depth:1
[5738]=1738,	-- depth:1
[5565]=1565,	-- depth:1
[5704]=1704,	-- depth:1
[5702]=1702,	-- depth:1
[5677]=1677,	-- depth:1
[5676]=1676,	-- depth:1
[5675]=1675,	-- depth:1
[5674]=1674,	-- depth:1
[5673]=1673,	-- depth:1
[5672]=1672,	-- depth:1
[5671]=1671,	-- depth:1
[5670]=1670,	-- depth:1
[5669]=1669,	-- depth:1
[5668]=1668,	-- depth:1
[5667]=1667,	-- depth:1
[5666]=1666,	-- depth:1
[5665]=1665,	-- depth:1
[5664]=1664,	-- depth:1
[5663]=1663,	-- depth:1
[5662]=1662,	-- depth:1
[5661]=1661,	-- depth:1
[5660]=1660,	-- depth:1
[5659]=1659,	-- depth:1
[5678]=1678,	-- depth:1
[5679]=1679,	-- depth:1
[5680]=1680,	-- depth:1
[5681]=1681,	-- depth:1
[5701]=1701,	-- depth:1
[5700]=1700,	-- depth:1
[5699]=1699,	-- depth:1
[5698]=1698,	-- depth:1
[5697]=1697,	-- depth:1
[5696]=1696,	-- depth:1
[5695]=1695,	-- depth:1
[5694]=1694,	-- depth:1
[5693]=1693,	-- depth:1
[5703]=1703,	-- depth:1
[5692]=1692,	-- depth:1
[5690]=1690,	-- depth:1
[5689]=1689,	-- depth:1
[5688]=1688,	-- depth:1
[5687]=1687,	-- depth:1
[5686]=1686,	-- depth:1
[5685]=1685,	-- depth:1
[5684]=1684,	-- depth:1
[5683]=1683,	-- depth:1
[5682]=1682,	-- depth:1
[5691]=1691,	-- depth:1
[5564]=1564,	-- depth:1
[5563]=1563,	-- depth:1
[5562]=1562,	-- depth:1
[5443]=1443,	-- depth:1
[5442]=1442,	-- depth:1
[5441]=1441,	-- depth:1
[5440]=1440,	-- depth:1
[5439]=1439,	-- depth:1
[5438]=1438,	-- depth:1
[5437]=1437,	-- depth:1
[5436]=1436,	-- depth:1
[5435]=1435,	-- depth:1
[5434]=1434,	-- depth:1
[5433]=1433,	-- depth:1
[5432]=1432,	-- depth:1
[5431]=1431,	-- depth:1
[5430]=1430,	-- depth:1
[5429]=1429,	-- depth:1
[5428]=1428,	-- depth:1
[5427]=1427,	-- depth:1
[5426]=1426,	-- depth:1
[5425]=1425,	-- depth:1
[5444]=1444,	-- depth:1
[5445]=1445,	-- depth:1
[5446]=1446,	-- depth:1
[5447]=1447,	-- depth:1
[5467]=1467,	-- depth:1
[5466]=1466,	-- depth:1
[5465]=1465,	-- depth:1
[5464]=1464,	-- depth:1
[5463]=1463,	-- depth:1
[5462]=1462,	-- depth:1
[5461]=1461,	-- depth:1
[5460]=1460,	-- depth:1
[5459]=1459,	-- depth:1
[5424]=1424,	-- depth:1
[5458]=1458,	-- depth:1
[5456]=1456,	-- depth:1
[5455]=1455,	-- depth:1
[5454]=1454,	-- depth:1
[5453]=1453,	-- depth:1
[5452]=1452,	-- depth:1
[5451]=1451,	-- depth:1
[5450]=1450,	-- depth:1
[5449]=1449,	-- depth:1
[5448]=1448,	-- depth:1
[5457]=1457,	-- depth:1
[5468]=1468,	-- depth:1
[5423]=1423,	-- depth:1
[5421]=1421,	-- depth:1
[5396]=1396,	-- depth:1
[5395]=1395,	-- depth:1
[5394]=1394,	-- depth:1
[5393]=1393,	-- depth:1
[5392]=1392,	-- depth:1
[5391]=1391,	-- depth:1
[5390]=1390,	-- depth:1
[5389]=1389,	-- depth:1
[5388]=1388,	-- depth:1
[5387]=1387,	-- depth:1
[5386]=1386,	-- depth:1
[5385]=1385,	-- depth:1
[5384]=1384,	-- depth:1
[5383]=1383,	-- depth:1
[5382]=1382,	-- depth:1
[5381]=1381,	-- depth:1
[5380]=1380,	-- depth:1
[5379]=1379,	-- depth:1
[5378]=1378,	-- depth:1
[5397]=1397,	-- depth:1
[5398]=1398,	-- depth:1
[5399]=1399,	-- depth:1
[5400]=1400,	-- depth:1
[5420]=1420,	-- depth:1
[5419]=1419,	-- depth:1
[5418]=1418,	-- depth:1
[5417]=1417,	-- depth:1
[5416]=1416,	-- depth:1
[5415]=1415,	-- depth:1
[5414]=1414,	-- depth:1
[5413]=1413,	-- depth:1
[5412]=1412,	-- depth:1
[5422]=1422,	-- depth:1
[5411]=1411,	-- depth:1
[5409]=1409,	-- depth:1
[5408]=1408,	-- depth:1
[5407]=1407,	-- depth:1
[5406]=1406,	-- depth:1
[5405]=1405,	-- depth:1
[5404]=1404,	-- depth:1
[5403]=1403,	-- depth:1
[5402]=1402,	-- depth:1
[5401]=1401,	-- depth:1
[5410]=1410,	-- depth:1
[5376]=1376,	-- depth:1
[5469]=1469,	-- depth:1
[5471]=1471,	-- depth:1
[5537]=1537,	-- depth:1
[5536]=1536,	-- depth:1
[5535]=1535,	-- depth:1
[5534]=1534,	-- depth:1
[5533]=1533,	-- depth:1
[5532]=1532,	-- depth:1
[5531]=1531,	-- depth:1
[5530]=1530,	-- depth:1
[5529]=1529,	-- depth:1
[5528]=1528,	-- depth:1
[5527]=1527,	-- depth:1
[5526]=1526,	-- depth:1
[5525]=1525,	-- depth:1
[5524]=1524,	-- depth:1
[5523]=1523,	-- depth:1
[5522]=1522,	-- depth:1
[5521]=1521,	-- depth:1
[5520]=1520,	-- depth:1
[5519]=1519,	-- depth:1
[5538]=1538,	-- depth:1
[5539]=1539,	-- depth:1
[5540]=1540,	-- depth:1
[5541]=1541,	-- depth:1
[5561]=1561,	-- depth:1
[5560]=1560,	-- depth:1
[5559]=1559,	-- depth:1
[5558]=1558,	-- depth:1
[5557]=1557,	-- depth:1
[5556]=1556,	-- depth:1
[5555]=1555,	-- depth:1
[5554]=1554,	-- depth:1
[5553]=1553,	-- depth:1
[5518]=1518,	-- depth:1
[5552]=1552,	-- depth:1
[5550]=1550,	-- depth:1
[5549]=1549,	-- depth:1
[5548]=1548,	-- depth:1
[5547]=1547,	-- depth:1
[5546]=1546,	-- depth:1
[5545]=1545,	-- depth:1
[5544]=1544,	-- depth:1
[5543]=1543,	-- depth:1
[5542]=1542,	-- depth:1
[5551]=1551,	-- depth:1
[5470]=1470,	-- depth:1
[5517]=1517,	-- depth:1
[5515]=1515,	-- depth:1
[5490]=1490,	-- depth:1
[5489]=1489,	-- depth:1
[5488]=1488,	-- depth:1
[5487]=1487,	-- depth:1
[5486]=1486,	-- depth:1
[5485]=1485,	-- depth:1
[5484]=1484,	-- depth:1
[5483]=1483,	-- depth:1
[5482]=1482,	-- depth:1
[5481]=1481,	-- depth:1
[5480]=1480,	-- depth:1
[5479]=1479,	-- depth:1
[5478]=1478,	-- depth:1
[5477]=1477,	-- depth:1
[5476]=1476,	-- depth:1
[5475]=1475,	-- depth:1
[5474]=1474,	-- depth:1
[5473]=1473,	-- depth:1
[5472]=1472,	-- depth:1
[5491]=1491,	-- depth:1
[5492]=1492,	-- depth:1
[5493]=1493,	-- depth:1
[5494]=1494,	-- depth:1
[5514]=1514,	-- depth:1
[5513]=1513,	-- depth:1
[5512]=1512,	-- depth:1
[5511]=1511,	-- depth:1
[5510]=1510,	-- depth:1
[5509]=1509,	-- depth:1
[5508]=1508,	-- depth:1
[5507]=1507,	-- depth:1
[5506]=1506,	-- depth:1
[5516]=1516,	-- depth:1
[5505]=1505,	-- depth:1
[5503]=1503,	-- depth:1
[5502]=1502,	-- depth:1
[5501]=1501,	-- depth:1
[5500]=1500,	-- depth:1
[5499]=1499,	-- depth:1
[5498]=1498,	-- depth:1
[5497]=1497,	-- depth:1
[5496]=1496,	-- depth:1
[5495]=1495,	-- depth:1
[5504]=1504,	-- depth:1
[6499]=499,	-- depth:1
[7500]=5500,	-- depth:2
[6501]=501,	-- depth:1
[7506]=5506,	-- depth:2
[7505]=5505,	-- depth:2
[7504]=5504,	-- depth:2
[7503]=5503,	-- depth:2
[7502]=5502,	-- depth:2
[7501]=5501,	-- depth:2
[5002]=1002,	-- depth:1
[7499]=5499,	-- depth:2
[7498]=5498,	-- depth:2
[7497]=5497,	-- depth:2
[7496]=5496,	-- depth:2
[7495]=5495,	-- depth:2
[7494]=5494,	-- depth:2
[7493]=5493,	-- depth:2
[7492]=5492,	-- depth:2
[7491]=5491,	-- depth:2
[7490]=5490,	-- depth:2
[7489]=5489,	-- depth:2
[7488]=5488,	-- depth:2
[7507]=5507,	-- depth:2
[7487]=5487,	-- depth:2
[7508]=5508,	-- depth:2
[7510]=5510,	-- depth:2
[7529]=5529,	-- depth:2
[7528]=5528,	-- depth:2
[7527]=5527,	-- depth:2
[7526]=5526,	-- depth:2
[7525]=5525,	-- depth:2
[7524]=5524,	-- depth:2
[7523]=5523,	-- depth:2
[7522]=5522,	-- depth:2
[7521]=5521,	-- depth:2
[7520]=5520,	-- depth:2
[7519]=5519,	-- depth:2
[7518]=5518,	-- depth:2
[7517]=5517,	-- depth:2
[7516]=5516,	-- depth:2
[7515]=5515,	-- depth:2
[7514]=5514,	-- depth:2
[7513]=5513,	-- depth:2
[7512]=5512,	-- depth:2
[7511]=5511,	-- depth:2
[7509]=5509,	-- depth:2
[7486]=5486,	-- depth:2
[7485]=5485,	-- depth:2
[7484]=5484,	-- depth:2
[7459]=5459,	-- depth:2
[7458]=5458,	-- depth:2
[7457]=5457,	-- depth:2
[7456]=5456,	-- depth:2
[7455]=5455,	-- depth:2
[7454]=5454,	-- depth:2
[7453]=5453,	-- depth:2
[7452]=5452,	-- depth:2
[7451]=5451,	-- depth:2
[7450]=5450,	-- depth:2
[7449]=5449,	-- depth:2
[7448]=5448,	-- depth:2
[7447]=5447,	-- depth:2
[7446]=5446,	-- depth:2
[7445]=5445,	-- depth:2
[7444]=5444,	-- depth:2
[7443]=5443,	-- depth:2
[7442]=5442,	-- depth:2
[7441]=5441,	-- depth:2
[7460]=5460,	-- depth:2
[7461]=5461,	-- depth:2
[7462]=5462,	-- depth:2
[7463]=5463,	-- depth:2
[7483]=5483,	-- depth:2
[7482]=5482,	-- depth:2
[7481]=5481,	-- depth:2
[7480]=5480,	-- depth:2
[7479]=5479,	-- depth:2
[7478]=5478,	-- depth:2
[7477]=5477,	-- depth:2
[7476]=5476,	-- depth:2
[7475]=5475,	-- depth:2
[7530]=5530,	-- depth:2
[7474]=5474,	-- depth:2
[7472]=5472,	-- depth:2
[7471]=5471,	-- depth:2
[7470]=5470,	-- depth:2
[7469]=5469,	-- depth:2
[7468]=5468,	-- depth:2
[7467]=5467,	-- depth:2
[7466]=5466,	-- depth:2
[7465]=5465,	-- depth:2
[7464]=5464,	-- depth:2
[7473]=5473,	-- depth:2
[7531]=5531,	-- depth:2
[7532]=5532,	-- depth:2
[7533]=5533,	-- depth:2
[7599]=5599,	-- depth:2
[7598]=5598,	-- depth:2
[7597]=5597,	-- depth:2
[7596]=5596,	-- depth:2
[7595]=5595,	-- depth:2
[7594]=5594,	-- depth:2
[7593]=5593,	-- depth:2
[7592]=5592,	-- depth:2
[7591]=5591,	-- depth:2
[7590]=5590,	-- depth:2
[7589]=5589,	-- depth:2
[7588]=5588,	-- depth:2
[7587]=5587,	-- depth:2
[7586]=5586,	-- depth:2
[7585]=5585,	-- depth:2
[7584]=5584,	-- depth:2
[7583]=5583,	-- depth:2
[7582]=5582,	-- depth:2
[7581]=5581,	-- depth:2
[7600]=5600,	-- depth:2
[7601]=5601,	-- depth:2
[7602]=5602,	-- depth:2
[7603]=5603,	-- depth:2
[7623]=5623,	-- depth:2
[7622]=5622,	-- depth:2
[7621]=5621,	-- depth:2
[7620]=5620,	-- depth:2
[7619]=5619,	-- depth:2
[7618]=5618,	-- depth:2
[7617]=5617,	-- depth:2
[7616]=5616,	-- depth:2
[7615]=5615,	-- depth:2
[7580]=5580,	-- depth:2
[7614]=5614,	-- depth:2
[7612]=5612,	-- depth:2
[7611]=5611,	-- depth:2
[7610]=5610,	-- depth:2
[7609]=5609,	-- depth:2
[7608]=5608,	-- depth:2
[7607]=5607,	-- depth:2
[7606]=5606,	-- depth:2
[7605]=5605,	-- depth:2
[7604]=5604,	-- depth:2
[7613]=5613,	-- depth:2
[7440]=5440,	-- depth:2
[7579]=5579,	-- depth:2
[7577]=5577,	-- depth:2
[7552]=5552,	-- depth:2
[7551]=5551,	-- depth:2
[7550]=5550,	-- depth:2
[7549]=5549,	-- depth:2
[7548]=5548,	-- depth:2
[7547]=5547,	-- depth:2
[7546]=5546,	-- depth:2
[7545]=5545,	-- depth:2
[7544]=5544,	-- depth:2
[7543]=5543,	-- depth:2
[7542]=5542,	-- depth:2
[7541]=5541,	-- depth:2
[7540]=5540,	-- depth:2
[7539]=5539,	-- depth:2
[7538]=5538,	-- depth:2
[7537]=5537,	-- depth:2
[7536]=5536,	-- depth:2
[7535]=5535,	-- depth:2
[7534]=5534,	-- depth:2
[7553]=5553,	-- depth:2
[7554]=5554,	-- depth:2
[7555]=5555,	-- depth:2
[7556]=5556,	-- depth:2
[7576]=5576,	-- depth:2
[7575]=5575,	-- depth:2
[7574]=5574,	-- depth:2
[7573]=5573,	-- depth:2
[7572]=5572,	-- depth:2
[7571]=5571,	-- depth:2
[7570]=5570,	-- depth:2
[7569]=5569,	-- depth:2
[7568]=5568,	-- depth:2
[7578]=5578,	-- depth:2
[7567]=5567,	-- depth:2
[7565]=5565,	-- depth:2
[7564]=5564,	-- depth:2
[7563]=5563,	-- depth:2
[7562]=5562,	-- depth:2
[7561]=5561,	-- depth:2
[7560]=5560,	-- depth:2
[7559]=5559,	-- depth:2
[7558]=5558,	-- depth:2
[7557]=5557,	-- depth:2
[7566]=5566,	-- depth:2
[7439]=5439,	-- depth:2
[7438]=5438,	-- depth:2
[7437]=5437,	-- depth:2
[7318]=5318,	-- depth:2
[7317]=5317,	-- depth:2
[7316]=5316,	-- depth:2
[7315]=5315,	-- depth:2
[7314]=5314,	-- depth:2
[7313]=5313,	-- depth:2
[7312]=5312,	-- depth:2
[7311]=5311,	-- depth:2
[7310]=5310,	-- depth:2
[7309]=5309,	-- depth:2
[7308]=5308,	-- depth:2
[7307]=5307,	-- depth:2
[7306]=5306,	-- depth:2
[7305]=5305,	-- depth:2
[7304]=5304,	-- depth:2
[7303]=5303,	-- depth:2
[7302]=5302,	-- depth:2
[7301]=5301,	-- depth:2
[7300]=5300,	-- depth:2
[7319]=5319,	-- depth:2
[7320]=5320,	-- depth:2
[7321]=5321,	-- depth:2
[7322]=5322,	-- depth:2
[7342]=5342,	-- depth:2
[7341]=5341,	-- depth:2
[7340]=5340,	-- depth:2
[7339]=5339,	-- depth:2
[7338]=5338,	-- depth:2
[7337]=5337,	-- depth:2
[7336]=5336,	-- depth:2
[7335]=5335,	-- depth:2
[7334]=5334,	-- depth:2
[7299]=5299,	-- depth:2
[7333]=5333,	-- depth:2
[7331]=5331,	-- depth:2
[7330]=5330,	-- depth:2
[7329]=5329,	-- depth:2
[7328]=5328,	-- depth:2
[7327]=5327,	-- depth:2
[7326]=5326,	-- depth:2
[7325]=5325,	-- depth:2
[7324]=5324,	-- depth:2
[7323]=5323,	-- depth:2
[7332]=5332,	-- depth:2
[7343]=5343,	-- depth:2
[7298]=5298,	-- depth:2
[7296]=5296,	-- depth:2
[7271]=5271,	-- depth:2
[7270]=5270,	-- depth:2
[7269]=5269,	-- depth:2
[7268]=5268,	-- depth:2
[7267]=5267,	-- depth:2
[7266]=5266,	-- depth:2
[7265]=5265,	-- depth:2
[7264]=5264,	-- depth:2
[7263]=5263,	-- depth:2
[7262]=5262,	-- depth:2
[7261]=5261,	-- depth:2
[7260]=5260,	-- depth:2
[7259]=5259,	-- depth:2
[7258]=5258,	-- depth:2
[7257]=5257,	-- depth:2
[7256]=5256,	-- depth:2
[7255]=5255,	-- depth:2
[7254]=5254,	-- depth:2
[7253]=5253,	-- depth:2
[7272]=5272,	-- depth:2
[7273]=5273,	-- depth:2
[7274]=5274,	-- depth:2
[7275]=5275,	-- depth:2
[7295]=5295,	-- depth:2
[7294]=5294,	-- depth:2
[7293]=5293,	-- depth:2
[7292]=5292,	-- depth:2
[7291]=5291,	-- depth:2
[7290]=5290,	-- depth:2
[7289]=5289,	-- depth:2
[7288]=5288,	-- depth:2
[7287]=5287,	-- depth:2
[7297]=5297,	-- depth:2
[7286]=5286,	-- depth:2
[7284]=5284,	-- depth:2
[7283]=5283,	-- depth:2
[7282]=5282,	-- depth:2
[7281]=5281,	-- depth:2
[7280]=5280,	-- depth:2
[7279]=5279,	-- depth:2
[7278]=5278,	-- depth:2
[7277]=5277,	-- depth:2
[7276]=5276,	-- depth:2
[7285]=5285,	-- depth:2
[7624]=5624,	-- depth:2
[7344]=5344,	-- depth:2
[7346]=5346,	-- depth:2
[7412]=5412,	-- depth:2
[7411]=5411,	-- depth:2
[7410]=5410,	-- depth:2
[7409]=5409,	-- depth:2
[7408]=5408,	-- depth:2
[7407]=5407,	-- depth:2
[7406]=5406,	-- depth:2
[7405]=5405,	-- depth:2
[7404]=5404,	-- depth:2
[7403]=5403,	-- depth:2
[7402]=5402,	-- depth:2
[7401]=5401,	-- depth:2
[7400]=5400,	-- depth:2
[7399]=5399,	-- depth:2
[7398]=5398,	-- depth:2
[7397]=5397,	-- depth:2
[7396]=5396,	-- depth:2
[7395]=5395,	-- depth:2
[7394]=5394,	-- depth:2
[7413]=5413,	-- depth:2
[7414]=5414,	-- depth:2
[7415]=5415,	-- depth:2
[7416]=5416,	-- depth:2
[7436]=5436,	-- depth:2
[7435]=5435,	-- depth:2
[7434]=5434,	-- depth:2
[7433]=5433,	-- depth:2
[7432]=5432,	-- depth:2
[7431]=5431,	-- depth:2
[7430]=5430,	-- depth:2
[7429]=5429,	-- depth:2
[7428]=5428,	-- depth:2
[7393]=5393,	-- depth:2
[7427]=5427,	-- depth:2
[7425]=5425,	-- depth:2
[7424]=5424,	-- depth:2
[7423]=5423,	-- depth:2
[7422]=5422,	-- depth:2
[7421]=5421,	-- depth:2
[7420]=5420,	-- depth:2
[7419]=5419,	-- depth:2
[7418]=5418,	-- depth:2
[7417]=5417,	-- depth:2
[7426]=5426,	-- depth:2
[7345]=5345,	-- depth:2
[7392]=5392,	-- depth:2
[7390]=5390,	-- depth:2
[7365]=5365,	-- depth:2
[7364]=5364,	-- depth:2
[7363]=5363,	-- depth:2
[7362]=5362,	-- depth:2
[7361]=5361,	-- depth:2
[7360]=5360,	-- depth:2
[7359]=5359,	-- depth:2
[7358]=5358,	-- depth:2
[7357]=5357,	-- depth:2
[7356]=5356,	-- depth:2
[7355]=5355,	-- depth:2
[7354]=5354,	-- depth:2
[7353]=5353,	-- depth:2
[7352]=5352,	-- depth:2
[7351]=5351,	-- depth:2
[7350]=5350,	-- depth:2
[7349]=5349,	-- depth:2
[7348]=5348,	-- depth:2
[7347]=5347,	-- depth:2
[7366]=5366,	-- depth:2
[7367]=5367,	-- depth:2
[7368]=5368,	-- depth:2
[7369]=5369,	-- depth:2
[7389]=5389,	-- depth:2
[7388]=5388,	-- depth:2
[7387]=5387,	-- depth:2
[7386]=5386,	-- depth:2
[7385]=5385,	-- depth:2
[7384]=5384,	-- depth:2
[7383]=5383,	-- depth:2
[7382]=5382,	-- depth:2
[7381]=5381,	-- depth:2
[7391]=5391,	-- depth:2
[7380]=5380,	-- depth:2
[7378]=5378,	-- depth:2
[7377]=5377,	-- depth:2
[7376]=5376,	-- depth:2
[7375]=5375,	-- depth:2
[7374]=5374,	-- depth:2
[7373]=5373,	-- depth:2
[7372]=5372,	-- depth:2
[7371]=5371,	-- depth:2
[7370]=5370,	-- depth:2
[7379]=5379,	-- depth:2
[7252]=5252,	-- depth:2
[7625]=5625,	-- depth:2
[7627]=5627,	-- depth:2
[7881]=5881,	-- depth:2
[7880]=5880,	-- depth:2
[7879]=5879,	-- depth:2
[7878]=5878,	-- depth:2
[7877]=5877,	-- depth:2
[7876]=5876,	-- depth:2
[7875]=5875,	-- depth:2
[7874]=5874,	-- depth:2
[7873]=5873,	-- depth:2
[7872]=5872,	-- depth:2
[7871]=5871,	-- depth:2
[7870]=5870,	-- depth:2
[7869]=5869,	-- depth:2
[7868]=5868,	-- depth:2
[7867]=5867,	-- depth:2
[7866]=5866,	-- depth:2
[7865]=5865,	-- depth:2
[7864]=5864,	-- depth:2
[7863]=5863,	-- depth:2
[7882]=5882,	-- depth:2
[7862]=5862,	-- depth:2
[7883]=5883,	-- depth:2
[7885]=5885,	-- depth:2
[7904]=5904,	-- depth:2
[7903]=5903,	-- depth:2
[7902]=5902,	-- depth:2
[7901]=5901,	-- depth:2
[7900]=5900,	-- depth:2
[7899]=5899,	-- depth:2
[7898]=5898,	-- depth:2
[7897]=5897,	-- depth:2
[7896]=5896,	-- depth:2
[7895]=5895,	-- depth:2
[7894]=5894,	-- depth:2
[7893]=5893,	-- depth:2
[7892]=5892,	-- depth:2
[7891]=5891,	-- depth:2
[7890]=5890,	-- depth:2
[7889]=5889,	-- depth:2
[7888]=5888,	-- depth:2
[7887]=5887,	-- depth:2
[7886]=5886,	-- depth:2
[7884]=5884,	-- depth:2
[7861]=5861,	-- depth:2
[7860]=5860,	-- depth:2
[7859]=5859,	-- depth:2
[7834]=5834,	-- depth:2
[7833]=5833,	-- depth:2
[7832]=5832,	-- depth:2
[7831]=5831,	-- depth:2
[7830]=5830,	-- depth:2
[7829]=5829,	-- depth:2
[7828]=5828,	-- depth:2
[7827]=5827,	-- depth:2
[7826]=5826,	-- depth:2
[7825]=5825,	-- depth:2
[7824]=5824,	-- depth:2
[7823]=5823,	-- depth:2
[7822]=5822,	-- depth:2
[7821]=5821,	-- depth:2
[7820]=5820,	-- depth:2
[7819]=5819,	-- depth:2
[7818]=5818,	-- depth:2
[7817]=5817,	-- depth:2
[7816]=5816,	-- depth:2
[7835]=5835,	-- depth:2
[7836]=5836,	-- depth:2
[7837]=5837,	-- depth:2
[7838]=5838,	-- depth:2
[7858]=5858,	-- depth:2
[7857]=5857,	-- depth:2
[7856]=5856,	-- depth:2
[7855]=5855,	-- depth:2
[7854]=5854,	-- depth:2
[7853]=5853,	-- depth:2
[7852]=5852,	-- depth:2
[7851]=5851,	-- depth:2
[7850]=5850,	-- depth:2
[7905]=5905,	-- depth:2
[7849]=5849,	-- depth:2
[7847]=5847,	-- depth:2
[7846]=5846,	-- depth:2
[7845]=5845,	-- depth:2
[7844]=5844,	-- depth:2
[7843]=5843,	-- depth:2
[7842]=5842,	-- depth:2
[7841]=5841,	-- depth:2
[7840]=5840,	-- depth:2
[7839]=5839,	-- depth:2
[7848]=5848,	-- depth:2
[7906]=5906,	-- depth:2
[7907]=5907,	-- depth:2
[7908]=5908,	-- depth:2
[7974]=5974,	-- depth:2
[7973]=5973,	-- depth:2
[7972]=5972,	-- depth:2
[7971]=5971,	-- depth:2
[7970]=5970,	-- depth:2
[7969]=5969,	-- depth:2
[7968]=5968,	-- depth:2
[7967]=5967,	-- depth:2
[7966]=5966,	-- depth:2
[7965]=5965,	-- depth:2
[7964]=5964,	-- depth:2
[7963]=5963,	-- depth:2
[7962]=5962,	-- depth:2
[7961]=5961,	-- depth:2
[7960]=5960,	-- depth:2
[7959]=5959,	-- depth:2
[7958]=5958,	-- depth:2
[7957]=5957,	-- depth:2
[7956]=5956,	-- depth:2
[7975]=5975,	-- depth:2
[7976]=5976,	-- depth:2
[7977]=5977,	-- depth:2
[7978]=5978,	-- depth:2
[7998]=5998,	-- depth:2
[7997]=5997,	-- depth:2
[7996]=5996,	-- depth:2
[7995]=5995,	-- depth:2
[7994]=5994,	-- depth:2
[7993]=5993,	-- depth:2
[7992]=5992,	-- depth:2
[7991]=5991,	-- depth:2
[7990]=5990,	-- depth:2
[7955]=5955,	-- depth:2
[7989]=5989,	-- depth:2
[7987]=5987,	-- depth:2
[7986]=5986,	-- depth:2
[7985]=5985,	-- depth:2
[7984]=5984,	-- depth:2
[7983]=5983,	-- depth:2
[7982]=5982,	-- depth:2
[7981]=5981,	-- depth:2
[7980]=5980,	-- depth:2
[7979]=5979,	-- depth:2
[7988]=5988,	-- depth:2
[7815]=5815,	-- depth:2
[7954]=5954,	-- depth:2
[7952]=5952,	-- depth:2
[7927]=5927,	-- depth:2
[7926]=5926,	-- depth:2
[7925]=5925,	-- depth:2
[7924]=5924,	-- depth:2
[7923]=5923,	-- depth:2
[7922]=5922,	-- depth:2
[7921]=5921,	-- depth:2
[7920]=5920,	-- depth:2
[7919]=5919,	-- depth:2
[7918]=5918,	-- depth:2
[7917]=5917,	-- depth:2
[7916]=5916,	-- depth:2
[7915]=5915,	-- depth:2
[7914]=5914,	-- depth:2
[7913]=5913,	-- depth:2
[7912]=5912,	-- depth:2
[7911]=5911,	-- depth:2
[7910]=5910,	-- depth:2
[7909]=5909,	-- depth:2
[7928]=5928,	-- depth:2
[7929]=5929,	-- depth:2
[7930]=5930,	-- depth:2
[7931]=5931,	-- depth:2
[7951]=5951,	-- depth:2
[7950]=5950,	-- depth:2
[7949]=5949,	-- depth:2
[7948]=5948,	-- depth:2
[7947]=5947,	-- depth:2
[7946]=5946,	-- depth:2
[7945]=5945,	-- depth:2
[7944]=5944,	-- depth:2
[7943]=5943,	-- depth:2
[7953]=5953,	-- depth:2
[7942]=5942,	-- depth:2
[7940]=5940,	-- depth:2
[7939]=5939,	-- depth:2
[7938]=5938,	-- depth:2
[7937]=5937,	-- depth:2
[7936]=5936,	-- depth:2
[7935]=5935,	-- depth:2
[7934]=5934,	-- depth:2
[7933]=5933,	-- depth:2
[7932]=5932,	-- depth:2
[7941]=5941,	-- depth:2
[7814]=5814,	-- depth:2
[7813]=5813,	-- depth:2
[7812]=5812,	-- depth:2
[7693]=5693,	-- depth:2
[7692]=5692,	-- depth:2
[7691]=5691,	-- depth:2
[7690]=5690,	-- depth:2
[7689]=5689,	-- depth:2
[7688]=5688,	-- depth:2
[7687]=5687,	-- depth:2
[7686]=5686,	-- depth:2
[7685]=5685,	-- depth:2
[7684]=5684,	-- depth:2
[7683]=5683,	-- depth:2
[7682]=5682,	-- depth:2
[7681]=5681,	-- depth:2
[7680]=5680,	-- depth:2
[7679]=5679,	-- depth:2
[7678]=5678,	-- depth:2
[7677]=5677,	-- depth:2
[7676]=5676,	-- depth:2
[7675]=5675,	-- depth:2
[7694]=5694,	-- depth:2
[7695]=5695,	-- depth:2
[7696]=5696,	-- depth:2
[7697]=5697,	-- depth:2
[7717]=5717,	-- depth:2
[7716]=5716,	-- depth:2
[7715]=5715,	-- depth:2
[7714]=5714,	-- depth:2
[7713]=5713,	-- depth:2
[7712]=5712,	-- depth:2
[7711]=5711,	-- depth:2
[7710]=5710,	-- depth:2
[7709]=5709,	-- depth:2
[7674]=5674,	-- depth:2
[7708]=5708,	-- depth:2
[7706]=5706,	-- depth:2
[7705]=5705,	-- depth:2
[7704]=5704,	-- depth:2
[7703]=5703,	-- depth:2
[7702]=5702,	-- depth:2
[7701]=5701,	-- depth:2
[7700]=5700,	-- depth:2
[7699]=5699,	-- depth:2
[7698]=5698,	-- depth:2
[7707]=5707,	-- depth:2
[7718]=5718,	-- depth:2
[7673]=5673,	-- depth:2
[7671]=5671,	-- depth:2
[7646]=5646,	-- depth:2
[7645]=5645,	-- depth:2
[7644]=5644,	-- depth:2
[7643]=5643,	-- depth:2
[7642]=5642,	-- depth:2
[7641]=5641,	-- depth:2
[7640]=5640,	-- depth:2
[7639]=5639,	-- depth:2
[7638]=5638,	-- depth:2
[7637]=5637,	-- depth:2
[7636]=5636,	-- depth:2
[7635]=5635,	-- depth:2
[7634]=5634,	-- depth:2
[7633]=5633,	-- depth:2
[7632]=5632,	-- depth:2
[7631]=5631,	-- depth:2
[7630]=5630,	-- depth:2
[7629]=5629,	-- depth:2
[7628]=5628,	-- depth:2
[7647]=5647,	-- depth:2
[7648]=5648,	-- depth:2
[7649]=5649,	-- depth:2
[7650]=5650,	-- depth:2
[7670]=5670,	-- depth:2
[7669]=5669,	-- depth:2
[7668]=5668,	-- depth:2
[7667]=5667,	-- depth:2
[7666]=5666,	-- depth:2
[7665]=5665,	-- depth:2
[7664]=5664,	-- depth:2
[7663]=5663,	-- depth:2
[7662]=5662,	-- depth:2
[7672]=5672,	-- depth:2
[7661]=5661,	-- depth:2
[7659]=5659,	-- depth:2
[7658]=5658,	-- depth:2
[7657]=5657,	-- depth:2
[7656]=5656,	-- depth:2
[7655]=5655,	-- depth:2
[7654]=5654,	-- depth:2
[7653]=5653,	-- depth:2
[7652]=5652,	-- depth:2
[7651]=5651,	-- depth:2
[7660]=5660,	-- depth:2
[7626]=5626,	-- depth:2
[7719]=5719,	-- depth:2
[7721]=5721,	-- depth:2
[7787]=5787,	-- depth:2
[7786]=5786,	-- depth:2
[7785]=5785,	-- depth:2
[7784]=5784,	-- depth:2
[7783]=5783,	-- depth:2
[7782]=5782,	-- depth:2
[7781]=5781,	-- depth:2
[7780]=5780,	-- depth:2
[7779]=5779,	-- depth:2
[7778]=5778,	-- depth:2
[7777]=5777,	-- depth:2
[7776]=5776,	-- depth:2
[7775]=5775,	-- depth:2
[7774]=5774,	-- depth:2
[7773]=5773,	-- depth:2
[7772]=5772,	-- depth:2
[7771]=5771,	-- depth:2
[7770]=5770,	-- depth:2
[7769]=5769,	-- depth:2
[7788]=5788,	-- depth:2
[7789]=5789,	-- depth:2
[7790]=5790,	-- depth:2
[7791]=5791,	-- depth:2
[7811]=5811,	-- depth:2
[7810]=5810,	-- depth:2
[7809]=5809,	-- depth:2
[7808]=5808,	-- depth:2
[7807]=5807,	-- depth:2
[7806]=5806,	-- depth:2
[7805]=5805,	-- depth:2
[7804]=5804,	-- depth:2
[7803]=5803,	-- depth:2
[7768]=5768,	-- depth:2
[7802]=5802,	-- depth:2
[7800]=5800,	-- depth:2
[7799]=5799,	-- depth:2
[7798]=5798,	-- depth:2
[7797]=5797,	-- depth:2
[7796]=5796,	-- depth:2
[7795]=5795,	-- depth:2
[7794]=5794,	-- depth:2
[7793]=5793,	-- depth:2
[7792]=5792,	-- depth:2
[7801]=5801,	-- depth:2
[7720]=5720,	-- depth:2
[7767]=5767,	-- depth:2
[7765]=5765,	-- depth:2
[7740]=5740,	-- depth:2
[7739]=5739,	-- depth:2
[7738]=5738,	-- depth:2
[7737]=5737,	-- depth:2
[7736]=5736,	-- depth:2
[7735]=5735,	-- depth:2
[7734]=5734,	-- depth:2
[7733]=5733,	-- depth:2
[7732]=5732,	-- depth:2
[7731]=5731,	-- depth:2
[7730]=5730,	-- depth:2
[7729]=5729,	-- depth:2
[7728]=5728,	-- depth:2
[7727]=5727,	-- depth:2
[7726]=5726,	-- depth:2
[7725]=5725,	-- depth:2
[7724]=5724,	-- depth:2
[7723]=5723,	-- depth:2
[7722]=5722,	-- depth:2
[7741]=5741,	-- depth:2
[7742]=5742,	-- depth:2
[7743]=5743,	-- depth:2
[7744]=5744,	-- depth:2
[7764]=5764,	-- depth:2
[7763]=5763,	-- depth:2
[7762]=5762,	-- depth:2
[7761]=5761,	-- depth:2
[7760]=5760,	-- depth:2
[7759]=5759,	-- depth:2
[7758]=5758,	-- depth:2
[7757]=5757,	-- depth:2
[7756]=5756,	-- depth:2
[7766]=5766,	-- depth:2
[7755]=5755,	-- depth:2
[7753]=5753,	-- depth:2
[7752]=5752,	-- depth:2
[7751]=5751,	-- depth:2
[7750]=5750,	-- depth:2
[7749]=5749,	-- depth:2
[7748]=5748,	-- depth:2
[7747]=5747,	-- depth:2
[7746]=5746,	-- depth:2
[7745]=5745,	-- depth:2
[7754]=5754,	-- depth:2
[7251]=5251,	-- depth:2
[7250]=5250,	-- depth:2
[7249]=5249,	-- depth:2
[6755]=755,	-- depth:1
[6754]=754,	-- depth:1
[6753]=753,	-- depth:1
[6752]=752,	-- depth:1
[6751]=751,	-- depth:1
[6750]=750,	-- depth:1
[6749]=749,	-- depth:1
[6748]=748,	-- depth:1
[6747]=747,	-- depth:1
[6746]=746,	-- depth:1
[6745]=745,	-- depth:1
[6744]=744,	-- depth:1
[6743]=743,	-- depth:1
[6742]=742,	-- depth:1
[6741]=741,	-- depth:1
[6740]=740,	-- depth:1
[6739]=739,	-- depth:1
[6738]=738,	-- depth:1
[6737]=737,	-- depth:1
[6756]=756,	-- depth:1
[6736]=736,	-- depth:1
[6757]=757,	-- depth:1
[6759]=759,	-- depth:1
[6778]=778,	-- depth:1
[6777]=777,	-- depth:1
[6776]=776,	-- depth:1
[6775]=775,	-- depth:1
[6774]=774,	-- depth:1
[6773]=773,	-- depth:1
[6772]=772,	-- depth:1
[6771]=771,	-- depth:1
[6770]=770,	-- depth:1
[6769]=769,	-- depth:1
[6768]=768,	-- depth:1
[6767]=767,	-- depth:1
[6766]=766,	-- depth:1
[6765]=765,	-- depth:1
[6764]=764,	-- depth:1
[6763]=763,	-- depth:1
[6762]=762,	-- depth:1
[6761]=761,	-- depth:1
[6760]=760,	-- depth:1
[6758]=758,	-- depth:1
[6735]=735,	-- depth:1
[6734]=734,	-- depth:1
[6733]=733,	-- depth:1
[6708]=708,	-- depth:1
[6707]=707,	-- depth:1
[6706]=706,	-- depth:1
[6705]=705,	-- depth:1
[6704]=704,	-- depth:1
[6703]=703,	-- depth:1
[6702]=702,	-- depth:1
[6701]=701,	-- depth:1
[6700]=700,	-- depth:1
[6699]=699,	-- depth:1
[6698]=698,	-- depth:1
[6697]=697,	-- depth:1
[6696]=696,	-- depth:1
[6695]=695,	-- depth:1
[6694]=694,	-- depth:1
[6693]=693,	-- depth:1
[6692]=692,	-- depth:1
[6691]=691,	-- depth:1
[6690]=690,	-- depth:1
[6709]=709,	-- depth:1
[6710]=710,	-- depth:1
[6711]=711,	-- depth:1
[6712]=712,	-- depth:1
[6732]=732,	-- depth:1
[6731]=731,	-- depth:1
[6730]=730,	-- depth:1
[6729]=729,	-- depth:1
[6728]=728,	-- depth:1
[6727]=727,	-- depth:1
[6726]=726,	-- depth:1
[6725]=725,	-- depth:1
[6724]=724,	-- depth:1
[6779]=779,	-- depth:1
[6723]=723,	-- depth:1
[6721]=721,	-- depth:1
[6720]=720,	-- depth:1
[6719]=719,	-- depth:1
[6718]=718,	-- depth:1
[6717]=717,	-- depth:1
[6716]=716,	-- depth:1
[6715]=715,	-- depth:1
[6714]=714,	-- depth:1
[6713]=713,	-- depth:1
[6722]=722,	-- depth:1
[6780]=780,	-- depth:1
[6781]=781,	-- depth:1
[6782]=782,	-- depth:1
[6848]=848,	-- depth:1
[6847]=847,	-- depth:1
[6846]=846,	-- depth:1
[6845]=845,	-- depth:1
[6844]=844,	-- depth:1
[6843]=843,	-- depth:1
[6842]=842,	-- depth:1
[6841]=841,	-- depth:1
[6840]=840,	-- depth:1
[6839]=839,	-- depth:1
[6838]=838,	-- depth:1
[6837]=837,	-- depth:1
[6836]=836,	-- depth:1
[6835]=835,	-- depth:1
[6834]=834,	-- depth:1
[6833]=833,	-- depth:1
[6832]=832,	-- depth:1
[6831]=831,	-- depth:1
[6830]=830,	-- depth:1
[6849]=849,	-- depth:1
[6850]=850,	-- depth:1
[6851]=851,	-- depth:1
[6852]=852,	-- depth:1
[6872]=872,	-- depth:1
[6871]=871,	-- depth:1
[6870]=870,	-- depth:1
[6869]=869,	-- depth:1
[6868]=868,	-- depth:1
[6867]=867,	-- depth:1
[6866]=866,	-- depth:1
[6865]=865,	-- depth:1
[6864]=864,	-- depth:1
[6829]=829,	-- depth:1
[6863]=863,	-- depth:1
[6861]=861,	-- depth:1
[6860]=860,	-- depth:1
[6859]=859,	-- depth:1
[6858]=858,	-- depth:1
[6857]=857,	-- depth:1
[6856]=856,	-- depth:1
[6855]=855,	-- depth:1
[6854]=854,	-- depth:1
[6853]=853,	-- depth:1
[6862]=862,	-- depth:1
[6689]=689,	-- depth:1
[6828]=828,	-- depth:1
[6826]=826,	-- depth:1
[6801]=801,	-- depth:1
[6800]=800,	-- depth:1
[6799]=799,	-- depth:1
[6798]=798,	-- depth:1
[6797]=797,	-- depth:1
[6796]=796,	-- depth:1
[6795]=795,	-- depth:1
[6794]=794,	-- depth:1
[6793]=793,	-- depth:1
[6792]=792,	-- depth:1
[6791]=791,	-- depth:1
[6790]=790,	-- depth:1
[6789]=789,	-- depth:1
[6788]=788,	-- depth:1
[6787]=787,	-- depth:1
[6786]=786,	-- depth:1
[6785]=785,	-- depth:1
[6784]=784,	-- depth:1
[6783]=783,	-- depth:1
[6802]=802,	-- depth:1
[6803]=803,	-- depth:1
[6804]=804,	-- depth:1
[6805]=805,	-- depth:1
[6825]=825,	-- depth:1
[6824]=824,	-- depth:1
[6823]=823,	-- depth:1
[6822]=822,	-- depth:1
[6821]=821,	-- depth:1
[6820]=820,	-- depth:1
[6819]=819,	-- depth:1
[6818]=818,	-- depth:1
[6817]=817,	-- depth:1
[6827]=827,	-- depth:1
[6816]=816,	-- depth:1
[6814]=814,	-- depth:1
[6813]=813,	-- depth:1
[6812]=812,	-- depth:1
[6811]=811,	-- depth:1
[6810]=810,	-- depth:1
[6809]=809,	-- depth:1
[6808]=808,	-- depth:1
[6807]=807,	-- depth:1
[6806]=806,	-- depth:1
[6815]=815,	-- depth:1
[6688]=688,	-- depth:1
[6687]=687,	-- depth:1
[6686]=686,	-- depth:1
[6567]=567,	-- depth:1
[6566]=566,	-- depth:1
[6565]=565,	-- depth:1
[6564]=564,	-- depth:1
[6563]=563,	-- depth:1
[6562]=562,	-- depth:1
[6561]=561,	-- depth:1
[6560]=560,	-- depth:1
[6559]=559,	-- depth:1
[6558]=558,	-- depth:1
[6557]=557,	-- depth:1
[6556]=556,	-- depth:1
[6555]=555,	-- depth:1
[6554]=554,	-- depth:1
[6553]=553,	-- depth:1
[6552]=552,	-- depth:1
[6551]=551,	-- depth:1
[6550]=550,	-- depth:1
[6549]=549,	-- depth:1
[6568]=568,	-- depth:1
[6569]=569,	-- depth:1
[6570]=570,	-- depth:1
[6571]=571,	-- depth:1
[6591]=591,	-- depth:1
[6590]=590,	-- depth:1
[6589]=589,	-- depth:1
[6588]=588,	-- depth:1
[6587]=587,	-- depth:1
[6586]=586,	-- depth:1
[6585]=585,	-- depth:1
[6584]=584,	-- depth:1
[6583]=583,	-- depth:1
[6548]=548,	-- depth:1
[6582]=582,	-- depth:1
[6580]=580,	-- depth:1
[6579]=579,	-- depth:1
[6578]=578,	-- depth:1
[6577]=577,	-- depth:1
[6576]=576,	-- depth:1
[6575]=575,	-- depth:1
[6574]=574,	-- depth:1
[6573]=573,	-- depth:1
[6572]=572,	-- depth:1
[6581]=581,	-- depth:1
[6592]=592,	-- depth:1
[6547]=547,	-- depth:1
[6545]=545,	-- depth:1
[6520]=520,	-- depth:1
[6519]=519,	-- depth:1
[6518]=518,	-- depth:1
[6517]=517,	-- depth:1
[6516]=516,	-- depth:1
[6515]=515,	-- depth:1
[6514]=514,	-- depth:1
[6513]=513,	-- depth:1
[6512]=512,	-- depth:1
[6511]=511,	-- depth:1
[6510]=510,	-- depth:1
[6509]=509,	-- depth:1
[6508]=508,	-- depth:1
[6507]=507,	-- depth:1
[6506]=506,	-- depth:1
[6505]=505,	-- depth:1
[6504]=504,	-- depth:1
[6503]=503,	-- depth:1
[6502]=502,	-- depth:1
[6521]=521,	-- depth:1
[6522]=522,	-- depth:1
[6523]=523,	-- depth:1
[6524]=524,	-- depth:1
[6544]=544,	-- depth:1
[6543]=543,	-- depth:1
[6542]=542,	-- depth:1
[6541]=541,	-- depth:1
[6540]=540,	-- depth:1
[6539]=539,	-- depth:1
[6538]=538,	-- depth:1
[6537]=537,	-- depth:1
[6536]=536,	-- depth:1
[6546]=546,	-- depth:1
[6535]=535,	-- depth:1
[6533]=533,	-- depth:1
[6532]=532,	-- depth:1
[6531]=531,	-- depth:1
[6530]=530,	-- depth:1
[6529]=529,	-- depth:1
[6528]=528,	-- depth:1
[6527]=527,	-- depth:1
[6526]=526,	-- depth:1
[6525]=525,	-- depth:1
[6534]=534,	-- depth:1
[6873]=873,	-- depth:1
[6593]=593,	-- depth:1
[6595]=595,	-- depth:1
[6661]=661,	-- depth:1
[6660]=660,	-- depth:1
[6659]=659,	-- depth:1
[6658]=658,	-- depth:1
[6657]=657,	-- depth:1
[6656]=656,	-- depth:1
[6655]=655,	-- depth:1
[6654]=654,	-- depth:1
[6653]=653,	-- depth:1
[6652]=652,	-- depth:1
[6651]=651,	-- depth:1
[6650]=650,	-- depth:1
[6649]=649,	-- depth:1
[6648]=648,	-- depth:1
[6647]=647,	-- depth:1
[6646]=646,	-- depth:1
[6645]=645,	-- depth:1
[6644]=644,	-- depth:1
[6643]=643,	-- depth:1
[6662]=662,	-- depth:1
[6663]=663,	-- depth:1
[6664]=664,	-- depth:1
[6665]=665,	-- depth:1
[6685]=685,	-- depth:1
[6684]=684,	-- depth:1
[6683]=683,	-- depth:1
[6682]=682,	-- depth:1
[6681]=681,	-- depth:1
[6680]=680,	-- depth:1
[6679]=679,	-- depth:1
[6678]=678,	-- depth:1
[6677]=677,	-- depth:1
[6642]=642,	-- depth:1
[6676]=676,	-- depth:1
[6674]=674,	-- depth:1
[6673]=673,	-- depth:1
[6672]=672,	-- depth:1
[6671]=671,	-- depth:1
[6670]=670,	-- depth:1
[6669]=669,	-- depth:1
[6668]=668,	-- depth:1
[6667]=667,	-- depth:1
[6666]=666,	-- depth:1
[6675]=675,	-- depth:1
[6594]=594,	-- depth:1
[6641]=641,	-- depth:1
[6639]=639,	-- depth:1
[6614]=614,	-- depth:1
[6613]=613,	-- depth:1
[6612]=612,	-- depth:1
[6611]=611,	-- depth:1
[6610]=610,	-- depth:1
[6609]=609,	-- depth:1
[6608]=608,	-- depth:1
[6607]=607,	-- depth:1
[6606]=606,	-- depth:1
[6605]=605,	-- depth:1
[6604]=604,	-- depth:1
[6603]=603,	-- depth:1
[6602]=602,	-- depth:1
[6601]=601,	-- depth:1
[6600]=600,	-- depth:1
[6599]=599,	-- depth:1
[6598]=598,	-- depth:1
[6597]=597,	-- depth:1
[6596]=596,	-- depth:1
[6615]=615,	-- depth:1
[6616]=616,	-- depth:1
[6617]=617,	-- depth:1
[6618]=618,	-- depth:1
[6638]=638,	-- depth:1
[6637]=637,	-- depth:1
[6636]=636,	-- depth:1
[6635]=635,	-- depth:1
[6634]=634,	-- depth:1
[6633]=633,	-- depth:1
[6632]=632,	-- depth:1
[6631]=631,	-- depth:1
[6630]=630,	-- depth:1
[6640]=640,	-- depth:1
[6629]=629,	-- depth:1
[6627]=627,	-- depth:1
[6626]=626,	-- depth:1
[6625]=625,	-- depth:1
[6624]=624,	-- depth:1
[6623]=623,	-- depth:1
[6622]=622,	-- depth:1
[6621]=621,	-- depth:1
[6620]=620,	-- depth:1
[6619]=619,	-- depth:1
[6628]=628,	-- depth:1
[6874]=874,	-- depth:1
[6875]=875,	-- depth:1
[6876]=876,	-- depth:1
[7130]=5130,	-- depth:2
[7129]=5129,	-- depth:2
[7128]=5128,	-- depth:2
[7127]=5127,	-- depth:2
[7126]=5126,	-- depth:2
[7125]=5125,	-- depth:2
[7124]=5124,	-- depth:2
[7123]=5123,	-- depth:2
[7122]=5122,	-- depth:2
[7121]=5121,	-- depth:2
[7120]=5120,	-- depth:2
[7119]=5119,	-- depth:2
[7118]=5118,	-- depth:2
[7117]=5117,	-- depth:2
[7116]=5116,	-- depth:2
[7115]=5115,	-- depth:2
[7114]=5114,	-- depth:2
[7113]=5113,	-- depth:2
[7112]=5112,	-- depth:2
[7131]=5131,	-- depth:2
[7132]=5132,	-- depth:2
[7133]=5133,	-- depth:2
[7134]=5134,	-- depth:2
[7154]=5154,	-- depth:2
[7153]=5153,	-- depth:2
[7152]=5152,	-- depth:2
[7151]=5151,	-- depth:2
[7150]=5150,	-- depth:2
[7149]=5149,	-- depth:2
[7148]=5148,	-- depth:2
[7147]=5147,	-- depth:2
[7146]=5146,	-- depth:2
[7111]=5111,	-- depth:2
[7145]=5145,	-- depth:2
[7143]=5143,	-- depth:2
[7142]=5142,	-- depth:2
[7141]=5141,	-- depth:2
[7140]=5140,	-- depth:2
[7139]=5139,	-- depth:2
[7138]=5138,	-- depth:2
[7137]=5137,	-- depth:2
[7136]=5136,	-- depth:2
[7135]=5135,	-- depth:2
[7144]=5144,	-- depth:2
[7155]=5155,	-- depth:2
[7110]=5110,	-- depth:2
[7108]=5108,	-- depth:2
[7083]=5083,	-- depth:2
[7082]=5082,	-- depth:2
[7081]=5081,	-- depth:2
[7080]=5080,	-- depth:2
[7079]=5079,	-- depth:2
[7078]=5078,	-- depth:2
[7077]=5077,	-- depth:2
[7076]=5076,	-- depth:2
[7075]=5075,	-- depth:2
[7074]=5074,	-- depth:2
[7073]=5073,	-- depth:2
[7072]=5072,	-- depth:2
[7071]=5071,	-- depth:2
[7070]=5070,	-- depth:2
[7069]=5069,	-- depth:2
[7068]=5068,	-- depth:2
[7067]=5067,	-- depth:2
[7066]=5066,	-- depth:2
[7065]=5065,	-- depth:2
[7084]=5084,	-- depth:2
[7085]=5085,	-- depth:2
[7086]=5086,	-- depth:2
[7087]=5087,	-- depth:2
[7107]=5107,	-- depth:2
[7106]=5106,	-- depth:2
[7105]=5105,	-- depth:2
[7104]=5104,	-- depth:2
[7103]=5103,	-- depth:2
[7102]=5102,	-- depth:2
[7101]=5101,	-- depth:2
[7100]=5100,	-- depth:2
[7099]=5099,	-- depth:2
[7109]=5109,	-- depth:2
[7098]=5098,	-- depth:2
[7096]=5096,	-- depth:2
[7095]=5095,	-- depth:2
[7094]=5094,	-- depth:2
[7093]=5093,	-- depth:2
[7092]=5092,	-- depth:2
[7091]=5091,	-- depth:2
[7090]=5090,	-- depth:2
[7089]=5089,	-- depth:2
[7088]=5088,	-- depth:2
[7097]=5097,	-- depth:2
[7064]=5064,	-- depth:2
[7156]=5156,	-- depth:2
[7158]=5158,	-- depth:2
[7224]=5224,	-- depth:2
[7223]=5223,	-- depth:2
[7222]=5222,	-- depth:2
[7221]=5221,	-- depth:2
[7220]=5220,	-- depth:2
[7219]=5219,	-- depth:2
[7218]=5218,	-- depth:2
[7217]=5217,	-- depth:2
[7216]=5216,	-- depth:2
[7215]=5215,	-- depth:2
[7214]=5214,	-- depth:2
[7213]=5213,	-- depth:2
[7212]=5212,	-- depth:2
[7211]=5211,	-- depth:2
[7210]=5210,	-- depth:2
[7209]=5209,	-- depth:2
[7208]=5208,	-- depth:2
[7207]=5207,	-- depth:2
[7206]=5206,	-- depth:2
[7225]=5225,	-- depth:2
[7226]=5226,	-- depth:2
[7227]=5227,	-- depth:2
[7228]=5228,	-- depth:2
[7248]=5248,	-- depth:2
[7247]=5247,	-- depth:2
[7246]=5246,	-- depth:2
[7245]=5245,	-- depth:2
[7244]=5244,	-- depth:2
[7243]=5243,	-- depth:2
[7242]=5242,	-- depth:2
[7241]=5241,	-- depth:2
[7240]=5240,	-- depth:2
[7205]=5205,	-- depth:2
[7239]=5239,	-- depth:2
[7237]=5237,	-- depth:2
[7236]=5236,	-- depth:2
[7235]=5235,	-- depth:2
[7234]=5234,	-- depth:2
[7233]=5233,	-- depth:2
[7232]=5232,	-- depth:2
[7231]=5231,	-- depth:2
[7230]=5230,	-- depth:2
[7229]=5229,	-- depth:2
[7238]=5238,	-- depth:2
[7157]=5157,	-- depth:2
[7204]=5204,	-- depth:2
[7202]=5202,	-- depth:2
[7177]=5177,	-- depth:2
[7176]=5176,	-- depth:2
[7175]=5175,	-- depth:2
[7174]=5174,	-- depth:2
[7173]=5173,	-- depth:2
[7172]=5172,	-- depth:2
[7171]=5171,	-- depth:2
[7170]=5170,	-- depth:2
[7169]=5169,	-- depth:2
[7168]=5168,	-- depth:2
[7167]=5167,	-- depth:2
[7166]=5166,	-- depth:2
[7165]=5165,	-- depth:2
[7164]=5164,	-- depth:2
[7163]=5163,	-- depth:2
[7162]=5162,	-- depth:2
[7161]=5161,	-- depth:2
[7160]=5160,	-- depth:2
[7159]=5159,	-- depth:2
[7178]=5178,	-- depth:2
[7179]=5179,	-- depth:2
[7180]=5180,	-- depth:2
[7181]=5181,	-- depth:2
[7201]=5201,	-- depth:2
[7200]=5200,	-- depth:2
[7199]=5199,	-- depth:2
[7198]=5198,	-- depth:2
[7197]=5197,	-- depth:2
[7196]=5196,	-- depth:2
[7195]=5195,	-- depth:2
[7194]=5194,	-- depth:2
[7193]=5193,	-- depth:2
[7203]=5203,	-- depth:2
[7192]=5192,	-- depth:2
[7190]=5190,	-- depth:2
[7189]=5189,	-- depth:2
[7188]=5188,	-- depth:2
[7187]=5187,	-- depth:2
[7186]=5186,	-- depth:2
[7185]=5185,	-- depth:2
[7184]=5184,	-- depth:2
[7183]=5183,	-- depth:2
[7182]=5182,	-- depth:2
[7191]=5191,	-- depth:2
[6500]=500,	-- depth:1
[7063]=5063,	-- depth:2
[7061]=5061,	-- depth:2
[6942]=942,	-- depth:1
[6941]=941,	-- depth:1
[6940]=940,	-- depth:1
[6939]=939,	-- depth:1
[6938]=938,	-- depth:1
[6937]=937,	-- depth:1
[6936]=936,	-- depth:1
[6935]=935,	-- depth:1
[6934]=934,	-- depth:1
[6933]=933,	-- depth:1
[6932]=932,	-- depth:1
[6931]=931,	-- depth:1
[6930]=930,	-- depth:1
[6929]=929,	-- depth:1
[6928]=928,	-- depth:1
[6927]=927,	-- depth:1
[6926]=926,	-- depth:1
[6925]=925,	-- depth:1
[6924]=924,	-- depth:1
[6943]=943,	-- depth:1
[6944]=944,	-- depth:1
[6945]=945,	-- depth:1
[6946]=946,	-- depth:1
[6966]=966,	-- depth:1
[6965]=965,	-- depth:1
[6964]=964,	-- depth:1
[6963]=963,	-- depth:1
[6962]=962,	-- depth:1
[6961]=961,	-- depth:1
[6960]=960,	-- depth:1
[6959]=959,	-- depth:1
[6958]=958,	-- depth:1
[6923]=923,	-- depth:1
[6957]=957,	-- depth:1
[6955]=955,	-- depth:1
[6954]=954,	-- depth:1
[6953]=953,	-- depth:1
[6952]=952,	-- depth:1
[6951]=951,	-- depth:1
[6950]=950,	-- depth:1
[6949]=949,	-- depth:1
[6948]=948,	-- depth:1
[6947]=947,	-- depth:1
[6956]=956,	-- depth:1
[6967]=967,	-- depth:1
[6922]=922,	-- depth:1
[6920]=920,	-- depth:1
[6895]=895,	-- depth:1
[6894]=894,	-- depth:1
[6893]=893,	-- depth:1
[6892]=892,	-- depth:1
[6891]=891,	-- depth:1
[6890]=890,	-- depth:1
[6889]=889,	-- depth:1
[6888]=888,	-- depth:1
[6887]=887,	-- depth:1
[6886]=886,	-- depth:1
[6885]=885,	-- depth:1
[6884]=884,	-- depth:1
[6883]=883,	-- depth:1
[6882]=882,	-- depth:1
[6881]=881,	-- depth:1
[6880]=880,	-- depth:1
[6879]=879,	-- depth:1
[6878]=878,	-- depth:1
[6877]=877,	-- depth:1
[6896]=896,	-- depth:1
[6897]=897,	-- depth:1
[6898]=898,	-- depth:1
[6899]=899,	-- depth:1
[6919]=919,	-- depth:1
[6918]=918,	-- depth:1
[6917]=917,	-- depth:1
[6916]=916,	-- depth:1
[6915]=915,	-- depth:1
[6914]=914,	-- depth:1
[6913]=913,	-- depth:1
[6912]=912,	-- depth:1
[6911]=911,	-- depth:1
[6921]=921,	-- depth:1
[6910]=910,	-- depth:1
[6908]=908,	-- depth:1
[6907]=907,	-- depth:1
[6906]=906,	-- depth:1
[6905]=905,	-- depth:1
[6904]=904,	-- depth:1
[6903]=903,	-- depth:1
[6902]=902,	-- depth:1
[6901]=901,	-- depth:1
[6900]=900,	-- depth:1
[6909]=909,	-- depth:1
[7062]=5062,	-- depth:2
[6968]=968,	-- depth:1
[6970]=970,	-- depth:1
[7036]=5036,	-- depth:2
[7035]=5035,	-- depth:2
[7034]=5034,	-- depth:2
[7033]=5033,	-- depth:2
[7032]=5032,	-- depth:2
[7031]=5031,	-- depth:2
[7030]=5030,	-- depth:2
[7029]=5029,	-- depth:2
[7028]=5028,	-- depth:2
[7027]=5027,	-- depth:2
[7026]=5026,	-- depth:2
[7025]=5025,	-- depth:2
[7024]=5024,	-- depth:2
[7023]=5023,	-- depth:2
[7022]=5022,	-- depth:2
[7021]=5021,	-- depth:2
[7020]=5020,	-- depth:2
[7019]=5019,	-- depth:2
[7018]=5018,	-- depth:2
[7037]=5037,	-- depth:2
[7038]=5038,	-- depth:2
[7039]=5039,	-- depth:2
[7040]=5040,	-- depth:2
[7060]=5060,	-- depth:2
[7059]=5059,	-- depth:2
[7058]=5058,	-- depth:2
[7057]=5057,	-- depth:2
[7056]=5056,	-- depth:2
[7055]=5055,	-- depth:2
[7054]=5054,	-- depth:2
[7053]=5053,	-- depth:2
[7052]=5052,	-- depth:2
[7017]=5017,	-- depth:2
[7051]=5051,	-- depth:2
[7049]=5049,	-- depth:2
[7048]=5048,	-- depth:2
[7047]=5047,	-- depth:2
[7046]=5046,	-- depth:2
[7045]=5045,	-- depth:2
[7044]=5044,	-- depth:2
[7043]=5043,	-- depth:2
[7042]=5042,	-- depth:2
[7041]=5041,	-- depth:2
[7050]=5050,	-- depth:2
[6969]=969,	-- depth:1
[7016]=5016,	-- depth:2
[7014]=5014,	-- depth:2
[6989]=989,	-- depth:1
[6988]=988,	-- depth:1
[6987]=987,	-- depth:1
[6986]=986,	-- depth:1
[6985]=985,	-- depth:1
[6984]=984,	-- depth:1
[6983]=983,	-- depth:1
[6982]=982,	-- depth:1
[6981]=981,	-- depth:1
[6980]=980,	-- depth:1
[6979]=979,	-- depth:1
[6978]=978,	-- depth:1
[6977]=977,	-- depth:1
[6976]=976,	-- depth:1
[6975]=975,	-- depth:1
[6974]=974,	-- depth:1
[6973]=973,	-- depth:1
[6972]=972,	-- depth:1
[6971]=971,	-- depth:1
[6990]=990,	-- depth:1
[6991]=991,	-- depth:1
[6992]=992,	-- depth:1
[6993]=993,	-- depth:1
[7013]=5013,	-- depth:2
[7012]=5012,	-- depth:2
[7011]=5011,	-- depth:2
[7010]=5010,	-- depth:2
[7009]=5009,	-- depth:2
[7008]=5008,	-- depth:2
[7007]=5007,	-- depth:2
[7006]=5006,	-- depth:2
[7005]=5005,	-- depth:2
[7015]=5015,	-- depth:2
[7004]=5004,	-- depth:2
[7002]=5002,	-- depth:2
[7001]=1001,	-- depth:1
[7000]=1000,	-- depth:1
[6999]=999,	-- depth:1
[6998]=998,	-- depth:1
[6997]=997,	-- depth:1
[6996]=996,	-- depth:1
[6995]=995,	-- depth:1
[6994]=994,	-- depth:1
[7003]=5003,	-- depth:2
[5001]=7001,	-- depth:2
[4000]=6000,	-- depth:2
[4999]=6999,	-- depth:2
[3005]=7005,	-- depth:3
[3004]=7004,	-- depth:3
[3003]=7003,	-- depth:3
[3002]=7002,	-- depth:3
[3001]=5001,	-- depth:3
[3000]=7000,	-- depth:2
[2999]=4999,	-- depth:3
[2998]=6998,	-- depth:2
[2997]=6997,	-- depth:2
[2996]=6996,	-- depth:2
[2995]=6995,	-- depth:2
[2994]=6994,	-- depth:2
[2993]=6993,	-- depth:2
[2992]=6992,	-- depth:2
[2991]=6991,	-- depth:2
[2990]=6990,	-- depth:2
[2989]=6989,	-- depth:2
[2988]=6988,	-- depth:2
[2987]=6987,	-- depth:2
[3006]=7006,	-- depth:3
[2986]=6986,	-- depth:2
[3007]=7007,	-- depth:3
[3009]=7009,	-- depth:3
[3028]=7028,	-- depth:3
[3027]=7027,	-- depth:3
[3026]=7026,	-- depth:3
[3025]=7025,	-- depth:3
[3024]=7024,	-- depth:3
[3023]=7023,	-- depth:3
[3022]=7022,	-- depth:3
[3021]=7021,	-- depth:3
[3020]=7020,	-- depth:3
[3019]=7019,	-- depth:3
[3018]=7018,	-- depth:3
[3017]=7017,	-- depth:3
[3016]=7016,	-- depth:3
[3015]=7015,	-- depth:3
[3014]=7014,	-- depth:3
[3013]=7013,	-- depth:3
[3012]=7012,	-- depth:3
[3011]=7011,	-- depth:3
[3010]=7010,	-- depth:3
[3008]=7008,	-- depth:3
[2985]=6985,	-- depth:2
[2984]=6984,	-- depth:2
[2983]=6983,	-- depth:2
[2958]=6958,	-- depth:2
[2957]=6957,	-- depth:2
[2956]=6956,	-- depth:2
[2955]=6955,	-- depth:2
[2954]=6954,	-- depth:2
[2953]=6953,	-- depth:2
[2952]=6952,	-- depth:2
[2951]=6951,	-- depth:2
[2950]=6950,	-- depth:2
[2949]=6949,	-- depth:2
[2948]=6948,	-- depth:2
[2947]=6947,	-- depth:2
[2946]=6946,	-- depth:2
[2945]=6945,	-- depth:2
[2944]=6944,	-- depth:2
[2943]=6943,	-- depth:2
[2942]=6942,	-- depth:2
[2941]=6941,	-- depth:2
[2940]=6940,	-- depth:2
[2959]=6959,	-- depth:2
[2960]=6960,	-- depth:2
[2961]=6961,	-- depth:2
[2962]=6962,	-- depth:2
[2982]=6982,	-- depth:2
[2981]=6981,	-- depth:2
[2980]=6980,	-- depth:2
[2979]=6979,	-- depth:2
[2978]=6978,	-- depth:2
[2977]=6977,	-- depth:2
[2976]=6976,	-- depth:2
[2975]=6975,	-- depth:2
[2974]=6974,	-- depth:2
[3029]=7029,	-- depth:3
[2973]=6973,	-- depth:2
[2971]=6971,	-- depth:2
[2970]=6970,	-- depth:2
[2969]=6969,	-- depth:2
[2968]=6968,	-- depth:2
[2967]=6967,	-- depth:2
[2966]=6966,	-- depth:2
[2965]=6965,	-- depth:2
[2964]=6964,	-- depth:2
[2963]=6963,	-- depth:2
[2972]=6972,	-- depth:2
[3030]=7030,	-- depth:3
[3031]=7031,	-- depth:3
[3032]=7032,	-- depth:3
[3098]=7098,	-- depth:3
[3097]=7097,	-- depth:3
[3096]=7096,	-- depth:3
[3095]=7095,	-- depth:3
[3094]=7094,	-- depth:3
[3093]=7093,	-- depth:3
[3092]=7092,	-- depth:3
[3091]=7091,	-- depth:3
[3090]=7090,	-- depth:3
[3089]=7089,	-- depth:3
[3088]=7088,	-- depth:3
[3087]=7087,	-- depth:3
[3086]=7086,	-- depth:3
[3085]=7085,	-- depth:3
[3084]=7084,	-- depth:3
[3083]=7083,	-- depth:3
[3082]=7082,	-- depth:3
[3081]=7081,	-- depth:3
[3080]=7080,	-- depth:3
[3099]=7099,	-- depth:3
[3100]=7100,	-- depth:3
[3101]=7101,	-- depth:3
[3102]=7102,	-- depth:3
[3122]=7122,	-- depth:3
[3121]=7121,	-- depth:3
[3120]=7120,	-- depth:3
[3119]=7119,	-- depth:3
[3118]=7118,	-- depth:3
[3117]=7117,	-- depth:3
[3116]=7116,	-- depth:3
[3115]=7115,	-- depth:3
[3114]=7114,	-- depth:3
[3079]=7079,	-- depth:3
[3113]=7113,	-- depth:3
[3111]=7111,	-- depth:3
[3110]=7110,	-- depth:3
[3109]=7109,	-- depth:3
[3108]=7108,	-- depth:3
[3107]=7107,	-- depth:3
[3106]=7106,	-- depth:3
[3105]=7105,	-- depth:3
[3104]=7104,	-- depth:3
[3103]=7103,	-- depth:3
[3112]=7112,	-- depth:3
[2939]=6939,	-- depth:2
[3078]=7078,	-- depth:3
[3076]=7076,	-- depth:3
[3051]=7051,	-- depth:3
[3050]=7050,	-- depth:3
[3049]=7049,	-- depth:3
[3048]=7048,	-- depth:3
[3047]=7047,	-- depth:3
[3046]=7046,	-- depth:3
[3045]=7045,	-- depth:3
[3044]=7044,	-- depth:3
[3043]=7043,	-- depth:3
[3042]=7042,	-- depth:3
[3041]=7041,	-- depth:3
[3040]=7040,	-- depth:3
[3039]=7039,	-- depth:3
[3038]=7038,	-- depth:3
[3037]=7037,	-- depth:3
[3036]=7036,	-- depth:3
[3035]=7035,	-- depth:3
[3034]=7034,	-- depth:3
[3033]=7033,	-- depth:3
[3052]=7052,	-- depth:3
[3053]=7053,	-- depth:3
[3054]=7054,	-- depth:3
[3055]=7055,	-- depth:3
[3075]=7075,	-- depth:3
[3074]=7074,	-- depth:3
[3073]=7073,	-- depth:3
[3072]=7072,	-- depth:3
[3071]=7071,	-- depth:3
[3070]=7070,	-- depth:3
[3069]=7069,	-- depth:3
[3068]=7068,	-- depth:3
[3067]=7067,	-- depth:3
[3077]=7077,	-- depth:3
[3066]=7066,	-- depth:3
[3064]=7064,	-- depth:3
[3063]=7063,	-- depth:3
[3062]=7062,	-- depth:3
[3061]=7061,	-- depth:3
[3060]=7060,	-- depth:3
[3059]=7059,	-- depth:3
[3058]=7058,	-- depth:3
[3057]=7057,	-- depth:3
[3056]=7056,	-- depth:3
[3065]=7065,	-- depth:3
[2938]=6938,	-- depth:2
[2937]=6937,	-- depth:2
[2936]=6936,	-- depth:2
[2817]=6817,	-- depth:2
[2816]=6816,	-- depth:2
[2815]=6815,	-- depth:2
[2814]=6814,	-- depth:2
[2813]=6813,	-- depth:2
[2812]=6812,	-- depth:2
[2811]=6811,	-- depth:2
[2810]=6810,	-- depth:2
[2809]=6809,	-- depth:2
[2808]=6808,	-- depth:2
[2807]=6807,	-- depth:2
[2806]=6806,	-- depth:2
[2805]=6805,	-- depth:2
[2804]=6804,	-- depth:2
[2803]=6803,	-- depth:2
[2802]=6802,	-- depth:2
[2801]=6801,	-- depth:2
[2800]=6800,	-- depth:2
[2799]=6799,	-- depth:2
[2818]=6818,	-- depth:2
[2819]=6819,	-- depth:2
[2820]=6820,	-- depth:2
[2821]=6821,	-- depth:2
[2841]=6841,	-- depth:2
[2840]=6840,	-- depth:2
[2839]=6839,	-- depth:2
[2838]=6838,	-- depth:2
[2837]=6837,	-- depth:2
[2836]=6836,	-- depth:2
[2835]=6835,	-- depth:2
[2834]=6834,	-- depth:2
[2833]=6833,	-- depth:2
[2798]=6798,	-- depth:2
[2832]=6832,	-- depth:2
[2830]=6830,	-- depth:2
[2829]=6829,	-- depth:2
[2828]=6828,	-- depth:2
[2827]=6827,	-- depth:2
[2826]=6826,	-- depth:2
[2825]=6825,	-- depth:2
[2824]=6824,	-- depth:2
[2823]=6823,	-- depth:2
[2822]=6822,	-- depth:2
[2831]=6831,	-- depth:2
[2842]=6842,	-- depth:2
[2797]=6797,	-- depth:2
[2795]=6795,	-- depth:2
[2770]=6770,	-- depth:2
[2769]=6769,	-- depth:2
[2768]=6768,	-- depth:2
[2767]=6767,	-- depth:2
[2766]=6766,	-- depth:2
[2765]=6765,	-- depth:2
[2764]=6764,	-- depth:2
[2763]=6763,	-- depth:2
[2762]=6762,	-- depth:2
[2761]=6761,	-- depth:2
[2760]=6760,	-- depth:2
[2759]=6759,	-- depth:2
[2758]=6758,	-- depth:2
[2757]=6757,	-- depth:2
[2756]=6756,	-- depth:2
[2755]=6755,	-- depth:2
[2754]=6754,	-- depth:2
[2753]=6753,	-- depth:2
[2752]=6752,	-- depth:2
[2771]=6771,	-- depth:2
[2772]=6772,	-- depth:2
[2773]=6773,	-- depth:2
[2774]=6774,	-- depth:2
[2794]=6794,	-- depth:2
[2793]=6793,	-- depth:2
[2792]=6792,	-- depth:2
[2791]=6791,	-- depth:2
[2790]=6790,	-- depth:2
[2789]=6789,	-- depth:2
[2788]=6788,	-- depth:2
[2787]=6787,	-- depth:2
[2786]=6786,	-- depth:2
[2796]=6796,	-- depth:2
[2785]=6785,	-- depth:2
[2783]=6783,	-- depth:2
[2782]=6782,	-- depth:2
[2781]=6781,	-- depth:2
[2780]=6780,	-- depth:2
[2779]=6779,	-- depth:2
[2778]=6778,	-- depth:2
[2777]=6777,	-- depth:2
[2776]=6776,	-- depth:2
[2775]=6775,	-- depth:2
[2784]=6784,	-- depth:2
[3123]=7123,	-- depth:3
[2843]=6843,	-- depth:2
[2845]=6845,	-- depth:2
[2911]=6911,	-- depth:2
[2910]=6910,	-- depth:2
[2909]=6909,	-- depth:2
[2908]=6908,	-- depth:2
[2907]=6907,	-- depth:2
[2906]=6906,	-- depth:2
[2905]=6905,	-- depth:2
[2904]=6904,	-- depth:2
[2903]=6903,	-- depth:2
[2902]=6902,	-- depth:2
[2901]=6901,	-- depth:2
[2900]=6900,	-- depth:2
[2899]=6899,	-- depth:2
[2898]=6898,	-- depth:2
[2897]=6897,	-- depth:2
[2896]=6896,	-- depth:2
[2895]=6895,	-- depth:2
[2894]=6894,	-- depth:2
[2893]=6893,	-- depth:2
[2912]=6912,	-- depth:2
[2913]=6913,	-- depth:2
[2914]=6914,	-- depth:2
[2915]=6915,	-- depth:2
[2935]=6935,	-- depth:2
[2934]=6934,	-- depth:2
[2933]=6933,	-- depth:2
[2932]=6932,	-- depth:2
[2931]=6931,	-- depth:2
[2930]=6930,	-- depth:2
[2929]=6929,	-- depth:2
[2928]=6928,	-- depth:2
[2927]=6927,	-- depth:2
[2892]=6892,	-- depth:2
[2926]=6926,	-- depth:2
[2924]=6924,	-- depth:2
[2923]=6923,	-- depth:2
[2922]=6922,	-- depth:2
[2921]=6921,	-- depth:2
[2920]=6920,	-- depth:2
[2919]=6919,	-- depth:2
[2918]=6918,	-- depth:2
[2917]=6917,	-- depth:2
[2916]=6916,	-- depth:2
[2925]=6925,	-- depth:2
[2844]=6844,	-- depth:2
[2891]=6891,	-- depth:2
[2889]=6889,	-- depth:2
[2864]=6864,	-- depth:2
[2863]=6863,	-- depth:2
[2862]=6862,	-- depth:2
[2861]=6861,	-- depth:2
[2860]=6860,	-- depth:2
[2859]=6859,	-- depth:2
[2858]=6858,	-- depth:2
[2857]=6857,	-- depth:2
[2856]=6856,	-- depth:2
[2855]=6855,	-- depth:2
[2854]=6854,	-- depth:2
[2853]=6853,	-- depth:2
[2852]=6852,	-- depth:2
[2851]=6851,	-- depth:2
[2850]=6850,	-- depth:2
[2849]=6849,	-- depth:2
[2848]=6848,	-- depth:2
[2847]=6847,	-- depth:2
[2846]=6846,	-- depth:2
[2865]=6865,	-- depth:2
[2866]=6866,	-- depth:2
[2867]=6867,	-- depth:2
[2868]=6868,	-- depth:2
[2888]=6888,	-- depth:2
[2887]=6887,	-- depth:2
[2886]=6886,	-- depth:2
[2885]=6885,	-- depth:2
[2884]=6884,	-- depth:2
[2883]=6883,	-- depth:2
[2882]=6882,	-- depth:2
[2881]=6881,	-- depth:2
[2880]=6880,	-- depth:2
[2890]=6890,	-- depth:2
[2879]=6879,	-- depth:2
[2877]=6877,	-- depth:2
[2876]=6876,	-- depth:2
[2875]=6875,	-- depth:2
[2874]=6874,	-- depth:2
[2873]=6873,	-- depth:2
[2872]=6872,	-- depth:2
[2871]=6871,	-- depth:2
[2870]=6870,	-- depth:2
[2869]=6869,	-- depth:2
[2878]=6878,	-- depth:2
[2751]=6751,	-- depth:2
[3124]=7124,	-- depth:3
[3126]=7126,	-- depth:3
[3380]=7380,	-- depth:3
[3379]=7379,	-- depth:3
[3378]=7378,	-- depth:3
[3377]=7377,	-- depth:3
[3376]=7376,	-- depth:3
[3375]=7375,	-- depth:3
[3374]=7374,	-- depth:3
[3373]=7373,	-- depth:3
[3372]=7372,	-- depth:3
[3371]=7371,	-- depth:3
[3370]=7370,	-- depth:3
[3369]=7369,	-- depth:3
[3368]=7368,	-- depth:3
[3367]=7367,	-- depth:3
[3366]=7366,	-- depth:3
[3365]=7365,	-- depth:3
[3364]=7364,	-- depth:3
[3363]=7363,	-- depth:3
[3362]=7362,	-- depth:3
[3381]=7381,	-- depth:3
[3361]=7361,	-- depth:3
[3382]=7382,	-- depth:3
[3384]=7384,	-- depth:3
[3403]=7403,	-- depth:3
[3402]=7402,	-- depth:3
[3401]=7401,	-- depth:3
[3400]=7400,	-- depth:3
[3399]=7399,	-- depth:3
[3398]=7398,	-- depth:3
[3397]=7397,	-- depth:3
[3396]=7396,	-- depth:3
[3395]=7395,	-- depth:3
[3394]=7394,	-- depth:3
[3393]=7393,	-- depth:3
[3392]=7392,	-- depth:3
[3391]=7391,	-- depth:3
[3390]=7390,	-- depth:3
[3389]=7389,	-- depth:3
[3388]=7388,	-- depth:3
[3387]=7387,	-- depth:3
[3386]=7386,	-- depth:3
[3385]=7385,	-- depth:3
[3383]=7383,	-- depth:3
[3360]=7360,	-- depth:3
[3359]=7359,	-- depth:3
[3358]=7358,	-- depth:3
[3333]=7333,	-- depth:3
[3332]=7332,	-- depth:3
[3331]=7331,	-- depth:3
[3330]=7330,	-- depth:3
[3329]=7329,	-- depth:3
[3328]=7328,	-- depth:3
[3327]=7327,	-- depth:3
[3326]=7326,	-- depth:3
[3325]=7325,	-- depth:3
[3324]=7324,	-- depth:3
[3323]=7323,	-- depth:3
[3322]=7322,	-- depth:3
[3321]=7321,	-- depth:3
[3320]=7320,	-- depth:3
[3319]=7319,	-- depth:3
[3318]=7318,	-- depth:3
[3317]=7317,	-- depth:3
[3316]=7316,	-- depth:3
[3315]=7315,	-- depth:3
[3334]=7334,	-- depth:3
[3335]=7335,	-- depth:3
[3336]=7336,	-- depth:3
[3337]=7337,	-- depth:3
[3357]=7357,	-- depth:3
[3356]=7356,	-- depth:3
[3355]=7355,	-- depth:3
[3354]=7354,	-- depth:3
[3353]=7353,	-- depth:3
[3352]=7352,	-- depth:3
[3351]=7351,	-- depth:3
[3350]=7350,	-- depth:3
[3349]=7349,	-- depth:3
[3404]=7404,	-- depth:3
[3348]=7348,	-- depth:3
[3346]=7346,	-- depth:3
[3345]=7345,	-- depth:3
[3344]=7344,	-- depth:3
[3343]=7343,	-- depth:3
[3342]=7342,	-- depth:3
[3341]=7341,	-- depth:3
[3340]=7340,	-- depth:3
[3339]=7339,	-- depth:3
[3338]=7338,	-- depth:3
[3347]=7347,	-- depth:3
[3405]=7405,	-- depth:3
[3406]=7406,	-- depth:3
[3407]=7407,	-- depth:3
[3473]=7473,	-- depth:3
[3472]=7472,	-- depth:3
[3471]=7471,	-- depth:3
[3470]=7470,	-- depth:3
[3469]=7469,	-- depth:3
[3468]=7468,	-- depth:3
[3467]=7467,	-- depth:3
[3466]=7466,	-- depth:3
[3465]=7465,	-- depth:3
[3464]=7464,	-- depth:3
[3463]=7463,	-- depth:3
[3462]=7462,	-- depth:3
[3461]=7461,	-- depth:3
[3460]=7460,	-- depth:3
[3459]=7459,	-- depth:3
[3458]=7458,	-- depth:3
[3457]=7457,	-- depth:3
[3456]=7456,	-- depth:3
[3455]=7455,	-- depth:3
[3474]=7474,	-- depth:3
[3475]=7475,	-- depth:3
[3476]=7476,	-- depth:3
[3477]=7477,	-- depth:3
[3497]=7497,	-- depth:3
[3496]=7496,	-- depth:3
[3495]=7495,	-- depth:3
[3494]=7494,	-- depth:3
[3493]=7493,	-- depth:3
[3492]=7492,	-- depth:3
[3491]=7491,	-- depth:3
[3490]=7490,	-- depth:3
[3489]=7489,	-- depth:3
[3454]=7454,	-- depth:3
[3488]=7488,	-- depth:3
[3486]=7486,	-- depth:3
[3485]=7485,	-- depth:3
[3484]=7484,	-- depth:3
[3483]=7483,	-- depth:3
[3482]=7482,	-- depth:3
[3481]=7481,	-- depth:3
[3480]=7480,	-- depth:3
[3479]=7479,	-- depth:3
[3478]=7478,	-- depth:3
[3487]=7487,	-- depth:3
[3314]=7314,	-- depth:3
[3453]=7453,	-- depth:3
[3451]=7451,	-- depth:3
[3426]=7426,	-- depth:3
[3425]=7425,	-- depth:3
[3424]=7424,	-- depth:3
[3423]=7423,	-- depth:3
[3422]=7422,	-- depth:3
[3421]=7421,	-- depth:3
[3420]=7420,	-- depth:3
[3419]=7419,	-- depth:3
[3418]=7418,	-- depth:3
[3417]=7417,	-- depth:3
[3416]=7416,	-- depth:3
[3415]=7415,	-- depth:3
[3414]=7414,	-- depth:3
[3413]=7413,	-- depth:3
[3412]=7412,	-- depth:3
[3411]=7411,	-- depth:3
[3410]=7410,	-- depth:3
[3409]=7409,	-- depth:3
[3408]=7408,	-- depth:3
[3427]=7427,	-- depth:3
[3428]=7428,	-- depth:3
[3429]=7429,	-- depth:3
[3430]=7430,	-- depth:3
[3450]=7450,	-- depth:3
[3449]=7449,	-- depth:3
[3448]=7448,	-- depth:3
[3447]=7447,	-- depth:3
[3446]=7446,	-- depth:3
[3445]=7445,	-- depth:3
[3444]=7444,	-- depth:3
[3443]=7443,	-- depth:3
[3442]=7442,	-- depth:3
[3452]=7452,	-- depth:3
[3441]=7441,	-- depth:3
[3439]=7439,	-- depth:3
[3438]=7438,	-- depth:3
[3437]=7437,	-- depth:3
[3436]=7436,	-- depth:3
[3435]=7435,	-- depth:3
[3434]=7434,	-- depth:3
[3433]=7433,	-- depth:3
[3432]=7432,	-- depth:3
[3431]=7431,	-- depth:3
[3440]=7440,	-- depth:3
[3313]=7313,	-- depth:3
[3312]=7312,	-- depth:3
[3311]=7311,	-- depth:3
[3192]=7192,	-- depth:3
[3191]=7191,	-- depth:3
[3190]=7190,	-- depth:3
[3189]=7189,	-- depth:3
[3188]=7188,	-- depth:3
[3187]=7187,	-- depth:3
[3186]=7186,	-- depth:3
[3185]=7185,	-- depth:3
[3184]=7184,	-- depth:3
[3183]=7183,	-- depth:3
[3182]=7182,	-- depth:3
[3181]=7181,	-- depth:3
[3180]=7180,	-- depth:3
[3179]=7179,	-- depth:3
[3178]=7178,	-- depth:3
[3177]=7177,	-- depth:3
[3176]=7176,	-- depth:3
[3175]=7175,	-- depth:3
[3174]=7174,	-- depth:3
[3193]=7193,	-- depth:3
[3194]=7194,	-- depth:3
[3195]=7195,	-- depth:3
[3196]=7196,	-- depth:3
[3216]=7216,	-- depth:3
[3215]=7215,	-- depth:3
[3214]=7214,	-- depth:3
[3213]=7213,	-- depth:3
[3212]=7212,	-- depth:3
[3211]=7211,	-- depth:3
[3210]=7210,	-- depth:3
[3209]=7209,	-- depth:3
[3208]=7208,	-- depth:3
[3173]=7173,	-- depth:3
[3207]=7207,	-- depth:3
[3205]=7205,	-- depth:3
[3204]=7204,	-- depth:3
[3203]=7203,	-- depth:3
[3202]=7202,	-- depth:3
[3201]=7201,	-- depth:3
[3200]=7200,	-- depth:3
[3199]=7199,	-- depth:3
[3198]=7198,	-- depth:3
[3197]=7197,	-- depth:3
[3206]=7206,	-- depth:3
[3217]=7217,	-- depth:3
[3172]=7172,	-- depth:3
[3170]=7170,	-- depth:3
[3145]=7145,	-- depth:3
[3144]=7144,	-- depth:3
[3143]=7143,	-- depth:3
[3142]=7142,	-- depth:3
[3141]=7141,	-- depth:3
[3140]=7140,	-- depth:3
[3139]=7139,	-- depth:3
[3138]=7138,	-- depth:3
[3137]=7137,	-- depth:3
[3136]=7136,	-- depth:3
[3135]=7135,	-- depth:3
[3134]=7134,	-- depth:3
[3133]=7133,	-- depth:3
[3132]=7132,	-- depth:3
[3131]=7131,	-- depth:3
[3130]=7130,	-- depth:3
[3129]=7129,	-- depth:3
[3128]=7128,	-- depth:3
[3127]=7127,	-- depth:3
[3146]=7146,	-- depth:3
[3147]=7147,	-- depth:3
[3148]=7148,	-- depth:3
[3149]=7149,	-- depth:3
[3169]=7169,	-- depth:3
[3168]=7168,	-- depth:3
[3167]=7167,	-- depth:3
[3166]=7166,	-- depth:3
[3165]=7165,	-- depth:3
[3164]=7164,	-- depth:3
[3163]=7163,	-- depth:3
[3162]=7162,	-- depth:3
[3161]=7161,	-- depth:3
[3171]=7171,	-- depth:3
[3160]=7160,	-- depth:3
[3158]=7158,	-- depth:3
[3157]=7157,	-- depth:3
[3156]=7156,	-- depth:3
[3155]=7155,	-- depth:3
[3154]=7154,	-- depth:3
[3153]=7153,	-- depth:3
[3152]=7152,	-- depth:3
[3151]=7151,	-- depth:3
[3150]=7150,	-- depth:3
[3159]=7159,	-- depth:3
[3125]=7125,	-- depth:3
[3218]=7218,	-- depth:3
[3220]=7220,	-- depth:3
[3286]=7286,	-- depth:3
[3285]=7285,	-- depth:3
[3284]=7284,	-- depth:3
[3283]=7283,	-- depth:3
[3282]=7282,	-- depth:3
[3281]=7281,	-- depth:3
[3280]=7280,	-- depth:3
[3279]=7279,	-- depth:3
[3278]=7278,	-- depth:3
[3277]=7277,	-- depth:3
[3276]=7276,	-- depth:3
[3275]=7275,	-- depth:3
[3274]=7274,	-- depth:3
[3273]=7273,	-- depth:3
[3272]=7272,	-- depth:3
[3271]=7271,	-- depth:3
[3270]=7270,	-- depth:3
[3269]=7269,	-- depth:3
[3268]=7268,	-- depth:3
[3287]=7287,	-- depth:3
[3288]=7288,	-- depth:3
[3289]=7289,	-- depth:3
[3290]=7290,	-- depth:3
[3310]=7310,	-- depth:3
[3309]=7309,	-- depth:3
[3308]=7308,	-- depth:3
[3307]=7307,	-- depth:3
[3306]=7306,	-- depth:3
[3305]=7305,	-- depth:3
[3304]=7304,	-- depth:3
[3303]=7303,	-- depth:3
[3302]=7302,	-- depth:3
[3267]=7267,	-- depth:3
[3301]=7301,	-- depth:3
[3299]=7299,	-- depth:3
[3298]=7298,	-- depth:3
[3297]=7297,	-- depth:3
[3296]=7296,	-- depth:3
[3295]=7295,	-- depth:3
[3294]=7294,	-- depth:3
[3293]=7293,	-- depth:3
[3292]=7292,	-- depth:3
[3291]=7291,	-- depth:3
[3300]=7300,	-- depth:3
[3219]=7219,	-- depth:3
[3266]=7266,	-- depth:3
[3264]=7264,	-- depth:3
[3239]=7239,	-- depth:3
[3238]=7238,	-- depth:3
[3237]=7237,	-- depth:3
[3236]=7236,	-- depth:3
[3235]=7235,	-- depth:3
[3234]=7234,	-- depth:3
[3233]=7233,	-- depth:3
[3232]=7232,	-- depth:3
[3231]=7231,	-- depth:3
[3230]=7230,	-- depth:3
[3229]=7229,	-- depth:3
[3228]=7228,	-- depth:3
[3227]=7227,	-- depth:3
[3226]=7226,	-- depth:3
[3225]=7225,	-- depth:3
[3224]=7224,	-- depth:3
[3223]=7223,	-- depth:3
[3222]=7222,	-- depth:3
[3221]=7221,	-- depth:3
[3240]=7240,	-- depth:3
[3241]=7241,	-- depth:3
[3242]=7242,	-- depth:3
[3243]=7243,	-- depth:3
[3263]=7263,	-- depth:3
[3262]=7262,	-- depth:3
[3261]=7261,	-- depth:3
[3260]=7260,	-- depth:3
[3259]=7259,	-- depth:3
[3258]=7258,	-- depth:3
[3257]=7257,	-- depth:3
[3256]=7256,	-- depth:3
[3255]=7255,	-- depth:3
[3265]=7265,	-- depth:3
[3254]=7254,	-- depth:3
[3252]=7252,	-- depth:3
[3251]=7251,	-- depth:3
[3250]=7250,	-- depth:3
[3249]=7249,	-- depth:3
[3248]=7248,	-- depth:3
[3247]=7247,	-- depth:3
[3246]=7246,	-- depth:3
[3245]=7245,	-- depth:3
[3244]=7244,	-- depth:3
[3253]=7253,	-- depth:3
[3498]=7498,	-- depth:3
[2750]=6750,	-- depth:2
[2748]=6748,	-- depth:2
[2255]=6255,	-- depth:2
[2254]=6254,	-- depth:2
[2253]=6253,	-- depth:2
[2252]=6252,	-- depth:2
[2251]=6251,	-- depth:2
[2250]=6250,	-- depth:2
[2249]=6249,	-- depth:2
[2248]=6248,	-- depth:2
[2247]=6247,	-- depth:2
[2246]=6246,	-- depth:2
[2245]=6245,	-- depth:2
[2244]=6244,	-- depth:2
[2243]=6243,	-- depth:2
[2242]=6242,	-- depth:2
[2241]=6241,	-- depth:2
[2240]=6240,	-- depth:2
[2239]=6239,	-- depth:2
[2238]=6238,	-- depth:2
[2237]=6237,	-- depth:2
[2256]=6256,	-- depth:2
[2236]=6236,	-- depth:2
[2257]=6257,	-- depth:2
[2259]=6259,	-- depth:2
[2278]=6278,	-- depth:2
[2277]=6277,	-- depth:2
[2276]=6276,	-- depth:2
[2275]=6275,	-- depth:2
[2274]=6274,	-- depth:2
[2273]=6273,	-- depth:2
[2272]=6272,	-- depth:2
[2271]=6271,	-- depth:2
[2270]=6270,	-- depth:2
[2269]=6269,	-- depth:2
[2268]=6268,	-- depth:2
[2267]=6267,	-- depth:2
[2266]=6266,	-- depth:2
[2265]=6265,	-- depth:2
[2264]=6264,	-- depth:2
[2263]=6263,	-- depth:2
[2262]=6262,	-- depth:2
[2261]=6261,	-- depth:2
[2260]=6260,	-- depth:2
[2258]=6258,	-- depth:2
[2235]=6235,	-- depth:2
[2234]=6234,	-- depth:2
[2233]=6233,	-- depth:2
[2208]=6208,	-- depth:2
[2207]=6207,	-- depth:2
[2206]=6206,	-- depth:2
[2205]=6205,	-- depth:2
[2204]=6204,	-- depth:2
[2203]=6203,	-- depth:2
[2202]=6202,	-- depth:2
[2201]=6201,	-- depth:2
[2200]=6200,	-- depth:2
[2199]=6199,	-- depth:2
[2198]=6198,	-- depth:2
[2197]=6197,	-- depth:2
[2196]=6196,	-- depth:2
[2195]=6195,	-- depth:2
[2194]=6194,	-- depth:2
[2193]=6193,	-- depth:2
[2192]=6192,	-- depth:2
[2191]=6191,	-- depth:2
[2190]=6190,	-- depth:2
[2209]=6209,	-- depth:2
[2210]=6210,	-- depth:2
[2211]=6211,	-- depth:2
[2212]=6212,	-- depth:2
[2232]=6232,	-- depth:2
[2231]=6231,	-- depth:2
[2230]=6230,	-- depth:2
[2229]=6229,	-- depth:2
[2228]=6228,	-- depth:2
[2227]=6227,	-- depth:2
[2226]=6226,	-- depth:2
[2225]=6225,	-- depth:2
[2224]=6224,	-- depth:2
[2279]=6279,	-- depth:2
[2223]=6223,	-- depth:2
[2221]=6221,	-- depth:2
[2220]=6220,	-- depth:2
[2219]=6219,	-- depth:2
[2218]=6218,	-- depth:2
[2217]=6217,	-- depth:2
[2216]=6216,	-- depth:2
[2215]=6215,	-- depth:2
[2214]=6214,	-- depth:2
[2213]=6213,	-- depth:2
[2222]=6222,	-- depth:2
[2280]=6280,	-- depth:2
[2281]=6281,	-- depth:2
[2282]=6282,	-- depth:2
[2348]=6348,	-- depth:2
[2347]=6347,	-- depth:2
[2346]=6346,	-- depth:2
[2345]=6345,	-- depth:2
[2344]=6344,	-- depth:2
[2343]=6343,	-- depth:2
[2342]=6342,	-- depth:2
[2341]=6341,	-- depth:2
[2340]=6340,	-- depth:2
[2339]=6339,	-- depth:2
[2338]=6338,	-- depth:2
[2337]=6337,	-- depth:2
[2336]=6336,	-- depth:2
[2335]=6335,	-- depth:2
[2334]=6334,	-- depth:2
[2333]=6333,	-- depth:2
[2332]=6332,	-- depth:2
[2331]=6331,	-- depth:2
[2330]=6330,	-- depth:2
[2349]=6349,	-- depth:2
[2350]=6350,	-- depth:2
[2351]=6351,	-- depth:2
[2352]=6352,	-- depth:2
[2372]=6372,	-- depth:2
[2371]=6371,	-- depth:2
[2370]=6370,	-- depth:2
[2369]=6369,	-- depth:2
[2368]=6368,	-- depth:2
[2367]=6367,	-- depth:2
[2366]=6366,	-- depth:2
[2365]=6365,	-- depth:2
[2364]=6364,	-- depth:2
[2329]=6329,	-- depth:2
[2363]=6363,	-- depth:2
[2361]=6361,	-- depth:2
[2360]=6360,	-- depth:2
[2359]=6359,	-- depth:2
[2358]=6358,	-- depth:2
[2357]=6357,	-- depth:2
[2356]=6356,	-- depth:2
[2355]=6355,	-- depth:2
[2354]=6354,	-- depth:2
[2353]=6353,	-- depth:2
[2362]=6362,	-- depth:2
[2189]=6189,	-- depth:2
[2328]=6328,	-- depth:2
[2326]=6326,	-- depth:2
[2301]=6301,	-- depth:2
[2300]=6300,	-- depth:2
[2299]=6299,	-- depth:2
[2298]=6298,	-- depth:2
[2297]=6297,	-- depth:2
[2296]=6296,	-- depth:2
[2295]=6295,	-- depth:2
[2294]=6294,	-- depth:2
[2293]=6293,	-- depth:2
[2292]=6292,	-- depth:2
[2291]=6291,	-- depth:2
[2290]=6290,	-- depth:2
[2289]=6289,	-- depth:2
[2288]=6288,	-- depth:2
[2287]=6287,	-- depth:2
[2286]=6286,	-- depth:2
[2285]=6285,	-- depth:2
[2284]=6284,	-- depth:2
[2283]=6283,	-- depth:2
[2302]=6302,	-- depth:2
[2303]=6303,	-- depth:2
[2304]=6304,	-- depth:2
[2305]=6305,	-- depth:2
[2325]=6325,	-- depth:2
[2324]=6324,	-- depth:2
[2323]=6323,	-- depth:2
[2322]=6322,	-- depth:2
[2321]=6321,	-- depth:2
[2320]=6320,	-- depth:2
[2319]=6319,	-- depth:2
[2318]=6318,	-- depth:2
[2317]=6317,	-- depth:2
[2327]=6327,	-- depth:2
[2316]=6316,	-- depth:2
[2314]=6314,	-- depth:2
[2313]=6313,	-- depth:2
[2312]=6312,	-- depth:2
[2311]=6311,	-- depth:2
[2310]=6310,	-- depth:2
[2309]=6309,	-- depth:2
[2308]=6308,	-- depth:2
[2307]=6307,	-- depth:2
[2306]=6306,	-- depth:2
[2315]=6315,	-- depth:2
[2188]=6188,	-- depth:2
[2187]=6187,	-- depth:2
[2186]=6186,	-- depth:2
[2067]=6067,	-- depth:2
[2066]=6066,	-- depth:2
[2065]=6065,	-- depth:2
[2064]=6064,	-- depth:2
[2063]=6063,	-- depth:2
[2062]=6062,	-- depth:2
[2061]=6061,	-- depth:2
[2060]=6060,	-- depth:2
[2059]=6059,	-- depth:2
[2058]=6058,	-- depth:2
[2057]=6057,	-- depth:2
[2056]=6056,	-- depth:2
[2055]=6055,	-- depth:2
[2054]=6054,	-- depth:2
[2053]=6053,	-- depth:2
[2052]=6052,	-- depth:2
[2051]=6051,	-- depth:2
[2050]=6050,	-- depth:2
[2049]=6049,	-- depth:2
[2068]=6068,	-- depth:2
[2069]=6069,	-- depth:2
[2070]=6070,	-- depth:2
[2071]=6071,	-- depth:2
[2091]=6091,	-- depth:2
[2090]=6090,	-- depth:2
[2089]=6089,	-- depth:2
[2088]=6088,	-- depth:2
[2087]=6087,	-- depth:2
[2086]=6086,	-- depth:2
[2085]=6085,	-- depth:2
[2084]=6084,	-- depth:2
[2083]=6083,	-- depth:2
[2048]=6048,	-- depth:2
[2082]=6082,	-- depth:2
[2080]=6080,	-- depth:2
[2079]=6079,	-- depth:2
[2078]=6078,	-- depth:2
[2077]=6077,	-- depth:2
[2076]=6076,	-- depth:2
[2075]=6075,	-- depth:2
[2074]=6074,	-- depth:2
[2073]=6073,	-- depth:2
[2072]=6072,	-- depth:2
[2081]=6081,	-- depth:2
[2092]=6092,	-- depth:2
[2047]=6047,	-- depth:2
[2045]=6045,	-- depth:2
[2020]=6020,	-- depth:2
[2019]=6019,	-- depth:2
[2018]=6018,	-- depth:2
[2017]=6017,	-- depth:2
[2016]=6016,	-- depth:2
[2015]=6015,	-- depth:2
[2014]=6014,	-- depth:2
[2013]=6013,	-- depth:2
[2012]=6012,	-- depth:2
[2011]=6011,	-- depth:2
[2010]=6010,	-- depth:2
[2009]=6009,	-- depth:2
[2008]=6008,	-- depth:2
[2007]=6007,	-- depth:2
[2006]=6006,	-- depth:2
[2005]=6005,	-- depth:2
[2004]=6004,	-- depth:2
[2003]=6003,	-- depth:2
[2002]=6002,	-- depth:2
[2021]=6021,	-- depth:2
[2022]=6022,	-- depth:2
[2023]=6023,	-- depth:2
[2024]=6024,	-- depth:2
[2044]=6044,	-- depth:2
[2043]=6043,	-- depth:2
[2042]=6042,	-- depth:2
[2041]=6041,	-- depth:2
[2040]=6040,	-- depth:2
[2039]=6039,	-- depth:2
[2038]=6038,	-- depth:2
[2037]=6037,	-- depth:2
[2036]=6036,	-- depth:2
[2046]=6046,	-- depth:2
[2035]=6035,	-- depth:2
[2033]=6033,	-- depth:2
[2032]=6032,	-- depth:2
[2031]=6031,	-- depth:2
[2030]=6030,	-- depth:2
[2029]=6029,	-- depth:2
[2028]=6028,	-- depth:2
[2027]=6027,	-- depth:2
[2026]=6026,	-- depth:2
[2025]=6025,	-- depth:2
[2034]=6034,	-- depth:2
[2373]=6373,	-- depth:2
[2093]=6093,	-- depth:2
[2095]=6095,	-- depth:2
[2161]=6161,	-- depth:2
[2160]=6160,	-- depth:2
[2159]=6159,	-- depth:2
[2158]=6158,	-- depth:2
[2157]=6157,	-- depth:2
[2156]=6156,	-- depth:2
[2155]=6155,	-- depth:2
[2154]=6154,	-- depth:2
[2153]=6153,	-- depth:2
[2152]=6152,	-- depth:2
[2151]=6151,	-- depth:2
[2150]=6150,	-- depth:2
[2149]=6149,	-- depth:2
[2148]=6148,	-- depth:2
[2147]=6147,	-- depth:2
[2146]=6146,	-- depth:2
[2145]=6145,	-- depth:2
[2144]=6144,	-- depth:2
[2143]=6143,	-- depth:2
[2162]=6162,	-- depth:2
[2163]=6163,	-- depth:2
[2164]=6164,	-- depth:2
[2165]=6165,	-- depth:2
[2185]=6185,	-- depth:2
[2184]=6184,	-- depth:2
[2183]=6183,	-- depth:2
[2182]=6182,	-- depth:2
[2181]=6181,	-- depth:2
[2180]=6180,	-- depth:2
[2179]=6179,	-- depth:2
[2178]=6178,	-- depth:2
[2177]=6177,	-- depth:2
[2142]=6142,	-- depth:2
[2176]=6176,	-- depth:2
[2174]=6174,	-- depth:2
[2173]=6173,	-- depth:2
[2172]=6172,	-- depth:2
[2171]=6171,	-- depth:2
[2170]=6170,	-- depth:2
[2169]=6169,	-- depth:2
[2168]=6168,	-- depth:2
[2167]=6167,	-- depth:2
[2166]=6166,	-- depth:2
[2175]=6175,	-- depth:2
[2094]=6094,	-- depth:2
[2141]=6141,	-- depth:2
[2139]=6139,	-- depth:2
[2114]=6114,	-- depth:2
[2113]=6113,	-- depth:2
[2112]=6112,	-- depth:2
[2111]=6111,	-- depth:2
[2110]=6110,	-- depth:2
[2109]=6109,	-- depth:2
[2108]=6108,	-- depth:2
[2107]=6107,	-- depth:2
[2106]=6106,	-- depth:2
[2105]=6105,	-- depth:2
[2104]=6104,	-- depth:2
[2103]=6103,	-- depth:2
[2102]=6102,	-- depth:2
[2101]=6101,	-- depth:2
[2100]=6100,	-- depth:2
[2099]=6099,	-- depth:2
[2098]=6098,	-- depth:2
[2097]=6097,	-- depth:2
[2096]=6096,	-- depth:2
[2115]=6115,	-- depth:2
[2116]=6116,	-- depth:2
[2117]=6117,	-- depth:2
[2118]=6118,	-- depth:2
[2138]=6138,	-- depth:2
[2137]=6137,	-- depth:2
[2136]=6136,	-- depth:2
[2135]=6135,	-- depth:2
[2134]=6134,	-- depth:2
[2133]=6133,	-- depth:2
[2132]=6132,	-- depth:2
[2131]=6131,	-- depth:2
[2130]=6130,	-- depth:2
[2140]=6140,	-- depth:2
[2129]=6129,	-- depth:2
[2127]=6127,	-- depth:2
[2126]=6126,	-- depth:2
[2125]=6125,	-- depth:2
[2124]=6124,	-- depth:2
[2123]=6123,	-- depth:2
[2122]=6122,	-- depth:2
[2121]=6121,	-- depth:2
[2120]=6120,	-- depth:2
[2119]=6119,	-- depth:2
[2128]=6128,	-- depth:2
[2749]=6749,	-- depth:2
[2374]=6374,	-- depth:2
[2376]=6376,	-- depth:2
[2630]=6630,	-- depth:2
[2629]=6629,	-- depth:2
[2628]=6628,	-- depth:2
[2627]=6627,	-- depth:2
[2626]=6626,	-- depth:2
[2625]=6625,	-- depth:2
[2624]=6624,	-- depth:2
[2623]=6623,	-- depth:2
[2622]=6622,	-- depth:2
[2621]=6621,	-- depth:2
[2620]=6620,	-- depth:2
[2619]=6619,	-- depth:2
[2618]=6618,	-- depth:2
[2617]=6617,	-- depth:2
[2616]=6616,	-- depth:2
[2615]=6615,	-- depth:2
[2614]=6614,	-- depth:2
[2613]=6613,	-- depth:2
[2612]=6612,	-- depth:2
[2631]=6631,	-- depth:2
[2611]=6611,	-- depth:2
[2632]=6632,	-- depth:2
[2634]=6634,	-- depth:2
[2653]=6653,	-- depth:2
[2652]=6652,	-- depth:2
[2651]=6651,	-- depth:2
[2650]=6650,	-- depth:2
[2649]=6649,	-- depth:2
[2648]=6648,	-- depth:2
[2647]=6647,	-- depth:2
[2646]=6646,	-- depth:2
[2645]=6645,	-- depth:2
[2644]=6644,	-- depth:2
[2643]=6643,	-- depth:2
[2642]=6642,	-- depth:2
[2641]=6641,	-- depth:2
[2640]=6640,	-- depth:2
[2639]=6639,	-- depth:2
[2638]=6638,	-- depth:2
[2637]=6637,	-- depth:2
[2636]=6636,	-- depth:2
[2635]=6635,	-- depth:2
[2633]=6633,	-- depth:2
[2610]=6610,	-- depth:2
[2609]=6609,	-- depth:2
[2608]=6608,	-- depth:2
[2583]=6583,	-- depth:2
[2582]=6582,	-- depth:2
[2581]=6581,	-- depth:2
[2580]=6580,	-- depth:2
[2579]=6579,	-- depth:2
[2578]=6578,	-- depth:2
[2577]=6577,	-- depth:2
[2576]=6576,	-- depth:2
[2575]=6575,	-- depth:2
[2574]=6574,	-- depth:2
[2573]=6573,	-- depth:2
[2572]=6572,	-- depth:2
[2571]=6571,	-- depth:2
[2570]=6570,	-- depth:2
[2569]=6569,	-- depth:2
[2568]=6568,	-- depth:2
[2567]=6567,	-- depth:2
[2566]=6566,	-- depth:2
[2565]=6565,	-- depth:2
[2584]=6584,	-- depth:2
[2585]=6585,	-- depth:2
[2586]=6586,	-- depth:2
[2587]=6587,	-- depth:2
[2607]=6607,	-- depth:2
[2606]=6606,	-- depth:2
[2605]=6605,	-- depth:2
[2604]=6604,	-- depth:2
[2603]=6603,	-- depth:2
[2602]=6602,	-- depth:2
[2601]=6601,	-- depth:2
[2600]=6600,	-- depth:2
[2599]=6599,	-- depth:2
[2654]=6654,	-- depth:2
[2598]=6598,	-- depth:2
[2596]=6596,	-- depth:2
[2595]=6595,	-- depth:2
[2594]=6594,	-- depth:2
[2593]=6593,	-- depth:2
[2592]=6592,	-- depth:2
[2591]=6591,	-- depth:2
[2590]=6590,	-- depth:2
[2589]=6589,	-- depth:2
[2588]=6588,	-- depth:2
[2597]=6597,	-- depth:2
[2655]=6655,	-- depth:2
[2656]=6656,	-- depth:2
[2657]=6657,	-- depth:2
[2723]=6723,	-- depth:2
[2722]=6722,	-- depth:2
[2721]=6721,	-- depth:2
[2720]=6720,	-- depth:2
[2719]=6719,	-- depth:2
[2718]=6718,	-- depth:2
[2717]=6717,	-- depth:2
[2716]=6716,	-- depth:2
[2715]=6715,	-- depth:2
[2714]=6714,	-- depth:2
[2713]=6713,	-- depth:2
[2712]=6712,	-- depth:2
[2711]=6711,	-- depth:2
[2710]=6710,	-- depth:2
[2709]=6709,	-- depth:2
[2708]=6708,	-- depth:2
[2707]=6707,	-- depth:2
[2706]=6706,	-- depth:2
[2705]=6705,	-- depth:2
[2724]=6724,	-- depth:2
[2725]=6725,	-- depth:2
[2726]=6726,	-- depth:2
[2727]=6727,	-- depth:2
[2747]=6747,	-- depth:2
[2746]=6746,	-- depth:2
[2745]=6745,	-- depth:2
[2744]=6744,	-- depth:2
[2743]=6743,	-- depth:2
[2742]=6742,	-- depth:2
[2741]=6741,	-- depth:2
[2740]=6740,	-- depth:2
[2739]=6739,	-- depth:2
[2704]=6704,	-- depth:2
[2738]=6738,	-- depth:2
[2736]=6736,	-- depth:2
[2735]=6735,	-- depth:2
[2734]=6734,	-- depth:2
[2733]=6733,	-- depth:2
[2732]=6732,	-- depth:2
[2731]=6731,	-- depth:2
[2730]=6730,	-- depth:2
[2729]=6729,	-- depth:2
[2728]=6728,	-- depth:2
[2737]=6737,	-- depth:2
[2564]=6564,	-- depth:2
[2703]=6703,	-- depth:2
[2701]=6701,	-- depth:2
[2676]=6676,	-- depth:2
[2675]=6675,	-- depth:2
[2674]=6674,	-- depth:2
[2673]=6673,	-- depth:2
[2672]=6672,	-- depth:2
[2671]=6671,	-- depth:2
[2670]=6670,	-- depth:2
[2669]=6669,	-- depth:2
[2668]=6668,	-- depth:2
[2667]=6667,	-- depth:2
[2666]=6666,	-- depth:2
[2665]=6665,	-- depth:2
[2664]=6664,	-- depth:2
[2663]=6663,	-- depth:2
[2662]=6662,	-- depth:2
[2661]=6661,	-- depth:2
[2660]=6660,	-- depth:2
[2659]=6659,	-- depth:2
[2658]=6658,	-- depth:2
[2677]=6677,	-- depth:2
[2678]=6678,	-- depth:2
[2679]=6679,	-- depth:2
[2680]=6680,	-- depth:2
[2700]=6700,	-- depth:2
[2699]=6699,	-- depth:2
[2698]=6698,	-- depth:2
[2697]=6697,	-- depth:2
[2696]=6696,	-- depth:2
[2695]=6695,	-- depth:2
[2694]=6694,	-- depth:2
[2693]=6693,	-- depth:2
[2692]=6692,	-- depth:2
[2702]=6702,	-- depth:2
[2691]=6691,	-- depth:2
[2689]=6689,	-- depth:2
[2688]=6688,	-- depth:2
[2687]=6687,	-- depth:2
[2686]=6686,	-- depth:2
[2685]=6685,	-- depth:2
[2684]=6684,	-- depth:2
[2683]=6683,	-- depth:2
[2682]=6682,	-- depth:2
[2681]=6681,	-- depth:2
[2690]=6690,	-- depth:2
[2563]=6563,	-- depth:2
[2562]=6562,	-- depth:2
[2561]=6561,	-- depth:2
[2442]=6442,	-- depth:2
[2441]=6441,	-- depth:2
[2440]=6440,	-- depth:2
[2439]=6439,	-- depth:2
[2438]=6438,	-- depth:2
[2437]=6437,	-- depth:2
[2436]=6436,	-- depth:2
[2435]=6435,	-- depth:2
[2434]=6434,	-- depth:2
[2433]=6433,	-- depth:2
[2432]=6432,	-- depth:2
[2431]=6431,	-- depth:2
[2430]=6430,	-- depth:2
[2429]=6429,	-- depth:2
[2428]=6428,	-- depth:2
[2427]=6427,	-- depth:2
[2426]=6426,	-- depth:2
[2425]=6425,	-- depth:2
[2424]=6424,	-- depth:2
[2443]=6443,	-- depth:2
[2444]=6444,	-- depth:2
[2445]=6445,	-- depth:2
[2446]=6446,	-- depth:2
[2466]=6466,	-- depth:2
[2465]=6465,	-- depth:2
[2464]=6464,	-- depth:2
[2463]=6463,	-- depth:2
[2462]=6462,	-- depth:2
[2461]=6461,	-- depth:2
[2460]=6460,	-- depth:2
[2459]=6459,	-- depth:2
[2458]=6458,	-- depth:2
[2423]=6423,	-- depth:2
[2457]=6457,	-- depth:2
[2455]=6455,	-- depth:2
[2454]=6454,	-- depth:2
[2453]=6453,	-- depth:2
[2452]=6452,	-- depth:2
[2451]=6451,	-- depth:2
[2450]=6450,	-- depth:2
[2449]=6449,	-- depth:2
[2448]=6448,	-- depth:2
[2447]=6447,	-- depth:2
[2456]=6456,	-- depth:2
[2467]=6467,	-- depth:2
[2422]=6422,	-- depth:2
[2420]=6420,	-- depth:2
[2395]=6395,	-- depth:2
[2394]=6394,	-- depth:2
[2393]=6393,	-- depth:2
[2392]=6392,	-- depth:2
[2391]=6391,	-- depth:2
[2390]=6390,	-- depth:2
[2389]=6389,	-- depth:2
[2388]=6388,	-- depth:2
[2387]=6387,	-- depth:2
[2386]=6386,	-- depth:2
[2385]=6385,	-- depth:2
[2384]=6384,	-- depth:2
[2383]=6383,	-- depth:2
[2382]=6382,	-- depth:2
[2381]=6381,	-- depth:2
[2380]=6380,	-- depth:2
[2379]=6379,	-- depth:2
[2378]=6378,	-- depth:2
[2377]=6377,	-- depth:2
[2396]=6396,	-- depth:2
[2397]=6397,	-- depth:2
[2398]=6398,	-- depth:2
[2399]=6399,	-- depth:2
[2419]=6419,	-- depth:2
[2418]=6418,	-- depth:2
[2417]=6417,	-- depth:2
[2416]=6416,	-- depth:2
[2415]=6415,	-- depth:2
[2414]=6414,	-- depth:2
[2413]=6413,	-- depth:2
[2412]=6412,	-- depth:2
[2411]=6411,	-- depth:2
[2421]=6421,	-- depth:2
[2410]=6410,	-- depth:2
[2408]=6408,	-- depth:2
[2407]=6407,	-- depth:2
[2406]=6406,	-- depth:2
[2405]=6405,	-- depth:2
[2404]=6404,	-- depth:2
[2403]=6403,	-- depth:2
[2402]=6402,	-- depth:2
[2401]=6401,	-- depth:2
[2400]=6400,	-- depth:2
[2409]=6409,	-- depth:2
[2375]=6375,	-- depth:2
[2468]=6468,	-- depth:2
[2470]=6470,	-- depth:2
[2536]=6536,	-- depth:2
[2535]=6535,	-- depth:2
[2534]=6534,	-- depth:2
[2533]=6533,	-- depth:2
[2532]=6532,	-- depth:2
[2531]=6531,	-- depth:2
[2530]=6530,	-- depth:2
[2529]=6529,	-- depth:2
[2528]=6528,	-- depth:2
[2527]=6527,	-- depth:2
[2526]=6526,	-- depth:2
[2525]=6525,	-- depth:2
[2524]=6524,	-- depth:2
[2523]=6523,	-- depth:2
[2522]=6522,	-- depth:2
[2521]=6521,	-- depth:2
[2520]=6520,	-- depth:2
[2519]=6519,	-- depth:2
[2518]=6518,	-- depth:2
[2537]=6537,	-- depth:2
[2538]=6538,	-- depth:2
[2539]=6539,	-- depth:2
[2540]=6540,	-- depth:2
[2560]=6560,	-- depth:2
[2559]=6559,	-- depth:2
[2558]=6558,	-- depth:2
[2557]=6557,	-- depth:2
[2556]=6556,	-- depth:2
[2555]=6555,	-- depth:2
[2554]=6554,	-- depth:2
[2553]=6553,	-- depth:2
[2552]=6552,	-- depth:2
[2517]=6517,	-- depth:2
[2551]=6551,	-- depth:2
[2549]=6549,	-- depth:2
[2548]=6548,	-- depth:2
[2547]=6547,	-- depth:2
[2546]=6546,	-- depth:2
[2545]=6545,	-- depth:2
[2544]=6544,	-- depth:2
[2543]=6543,	-- depth:2
[2542]=6542,	-- depth:2
[2541]=6541,	-- depth:2
[2550]=6550,	-- depth:2
[2469]=6469,	-- depth:2
[2516]=6516,	-- depth:2
[2514]=6514,	-- depth:2
[2489]=6489,	-- depth:2
[2488]=6488,	-- depth:2
[2487]=6487,	-- depth:2
[2486]=6486,	-- depth:2
[2485]=6485,	-- depth:2
[2484]=6484,	-- depth:2
[2483]=6483,	-- depth:2
[2482]=6482,	-- depth:2
[2481]=6481,	-- depth:2
[2480]=6480,	-- depth:2
[2479]=6479,	-- depth:2
[2478]=6478,	-- depth:2
[2477]=6477,	-- depth:2
[2476]=6476,	-- depth:2
[2475]=6475,	-- depth:2
[2474]=6474,	-- depth:2
[2473]=6473,	-- depth:2
[2472]=6472,	-- depth:2
[2471]=6471,	-- depth:2
[2490]=6490,	-- depth:2
[2491]=6491,	-- depth:2
[2492]=6492,	-- depth:2
[2493]=6493,	-- depth:2
[2513]=6513,	-- depth:2
[2512]=6512,	-- depth:2
[2511]=6511,	-- depth:2
[2510]=6510,	-- depth:2
[2509]=6509,	-- depth:2
[2508]=6508,	-- depth:2
[2507]=6507,	-- depth:2
[2506]=6506,	-- depth:2
[2505]=6505,	-- depth:2
[2515]=6515,	-- depth:2
[2504]=6504,	-- depth:2
[2502]=6502,	-- depth:2
[2501]=6501,	-- depth:2
[2500]=6500,	-- depth:2
[2499]=6499,	-- depth:2
[2498]=6498,	-- depth:2
[2497]=6497,	-- depth:2
[2496]=6496,	-- depth:2
[2495]=6495,	-- depth:2
[2494]=6494,	-- depth:2
[2503]=6503,	-- depth:2
[5000]=3000,	-- depth:3
[3499]=7499,	-- depth:3
[3501]=7501,	-- depth:3
[4506]=2506,	-- depth:3
[4505]=2505,	-- depth:3
[4504]=2504,	-- depth:3
[4503]=2503,	-- depth:3
[4502]=2502,	-- depth:3
[4501]=2501,	-- depth:3
[4500]=2500,	-- depth:3
[4499]=2499,	-- depth:3
[4498]=2498,	-- depth:3
[4497]=2497,	-- depth:3
[4496]=2496,	-- depth:3
[4495]=2495,	-- depth:3
[4494]=2494,	-- depth:3
[4493]=2493,	-- depth:3
[4492]=2492,	-- depth:3
[4491]=2491,	-- depth:3
[4490]=2490,	-- depth:3
[4489]=2489,	-- depth:3
[4488]=2488,	-- depth:3
[4507]=2507,	-- depth:3
[4487]=2487,	-- depth:3
[4508]=2508,	-- depth:3
[4510]=2510,	-- depth:3
[4529]=2529,	-- depth:3
[4528]=2528,	-- depth:3
[4527]=2527,	-- depth:3
[4526]=2526,	-- depth:3
[4525]=2525,	-- depth:3
[4524]=2524,	-- depth:3
[4523]=2523,	-- depth:3
[4522]=2522,	-- depth:3
[4521]=2521,	-- depth:3
[4520]=2520,	-- depth:3
[4519]=2519,	-- depth:3
[4518]=2518,	-- depth:3
[4517]=2517,	-- depth:3
[4516]=2516,	-- depth:3
[4515]=2515,	-- depth:3
[4514]=2514,	-- depth:3
[4513]=2513,	-- depth:3
[4512]=2512,	-- depth:3
[4511]=2511,	-- depth:3
[4509]=2509,	-- depth:3
[4486]=2486,	-- depth:3
[4485]=2485,	-- depth:3
[4484]=2484,	-- depth:3
[4459]=2459,	-- depth:3
[4458]=2458,	-- depth:3
[4457]=2457,	-- depth:3
[4456]=2456,	-- depth:3
[4455]=2455,	-- depth:3
[4454]=2454,	-- depth:3
[4453]=2453,	-- depth:3
[4452]=2452,	-- depth:3
[4451]=2451,	-- depth:3
[4450]=2450,	-- depth:3
[4449]=2449,	-- depth:3
[4448]=2448,	-- depth:3
[4447]=2447,	-- depth:3
[4446]=2446,	-- depth:3
[4445]=2445,	-- depth:3
[4444]=2444,	-- depth:3
[4443]=2443,	-- depth:3
[4442]=2442,	-- depth:3
[4441]=2441,	-- depth:3
[4460]=2460,	-- depth:3
[4461]=2461,	-- depth:3
[4462]=2462,	-- depth:3
[4463]=2463,	-- depth:3
[4483]=2483,	-- depth:3
[4482]=2482,	-- depth:3
[4481]=2481,	-- depth:3
[4480]=2480,	-- depth:3
[4479]=2479,	-- depth:3
[4478]=2478,	-- depth:3
[4477]=2477,	-- depth:3
[4476]=2476,	-- depth:3
[4475]=2475,	-- depth:3
[4530]=2530,	-- depth:3
[4474]=2474,	-- depth:3
[4472]=2472,	-- depth:3
[4471]=2471,	-- depth:3
[4470]=2470,	-- depth:3
[4469]=2469,	-- depth:3
[4468]=2468,	-- depth:3
[4467]=2467,	-- depth:3
[4466]=2466,	-- depth:3
[4465]=2465,	-- depth:3
[4464]=2464,	-- depth:3
[4473]=2473,	-- depth:3
[4531]=2531,	-- depth:3
[4532]=2532,	-- depth:3
[4533]=2533,	-- depth:3
[4599]=2599,	-- depth:3
[4598]=2598,	-- depth:3
[4597]=2597,	-- depth:3
[4596]=2596,	-- depth:3
[4595]=2595,	-- depth:3
[4594]=2594,	-- depth:3
[4593]=2593,	-- depth:3
[4592]=2592,	-- depth:3
[4591]=2591,	-- depth:3
[4590]=2590,	-- depth:3
[4589]=2589,	-- depth:3
[4588]=2588,	-- depth:3
[4587]=2587,	-- depth:3
[4586]=2586,	-- depth:3
[4585]=2585,	-- depth:3
[4584]=2584,	-- depth:3
[4583]=2583,	-- depth:3
[4582]=2582,	-- depth:3
[4581]=2581,	-- depth:3
[4600]=2600,	-- depth:3
[4601]=2601,	-- depth:3
[4602]=2602,	-- depth:3
[4603]=2603,	-- depth:3
[4623]=2623,	-- depth:3
[4622]=2622,	-- depth:3
[4621]=2621,	-- depth:3
[4620]=2620,	-- depth:3
[4619]=2619,	-- depth:3
[4618]=2618,	-- depth:3
[4617]=2617,	-- depth:3
[4616]=2616,	-- depth:3
[4615]=2615,	-- depth:3
[4580]=2580,	-- depth:3
[4614]=2614,	-- depth:3
[4612]=2612,	-- depth:3
[4611]=2611,	-- depth:3
[4610]=2610,	-- depth:3
[4609]=2609,	-- depth:3
[4608]=2608,	-- depth:3
[4607]=2607,	-- depth:3
[4606]=2606,	-- depth:3
[4605]=2605,	-- depth:3
[4604]=2604,	-- depth:3
[4613]=2613,	-- depth:3
[4440]=2440,	-- depth:3
[4579]=2579,	-- depth:3
[4577]=2577,	-- depth:3
[4552]=2552,	-- depth:3
[4551]=2551,	-- depth:3
[4550]=2550,	-- depth:3
[4549]=2549,	-- depth:3
[4548]=2548,	-- depth:3
[4547]=2547,	-- depth:3
[4546]=2546,	-- depth:3
[4545]=2545,	-- depth:3
[4544]=2544,	-- depth:3
[4543]=2543,	-- depth:3
[4542]=2542,	-- depth:3
[4541]=2541,	-- depth:3
[4540]=2540,	-- depth:3
[4539]=2539,	-- depth:3
[4538]=2538,	-- depth:3
[4537]=2537,	-- depth:3
[4536]=2536,	-- depth:3
[4535]=2535,	-- depth:3
[4534]=2534,	-- depth:3
[4553]=2553,	-- depth:3
[4554]=2554,	-- depth:3
[4555]=2555,	-- depth:3
[4556]=2556,	-- depth:3
[4576]=2576,	-- depth:3
[4575]=2575,	-- depth:3
[4574]=2574,	-- depth:3
[4573]=2573,	-- depth:3
[4572]=2572,	-- depth:3
[4571]=2571,	-- depth:3
[4570]=2570,	-- depth:3
[4569]=2569,	-- depth:3
[4568]=2568,	-- depth:3
[4578]=2578,	-- depth:3
[4567]=2567,	-- depth:3
[4565]=2565,	-- depth:3
[4564]=2564,	-- depth:3
[4563]=2563,	-- depth:3
[4562]=2562,	-- depth:3
[4561]=2561,	-- depth:3
[4560]=2560,	-- depth:3
[4559]=2559,	-- depth:3
[4558]=2558,	-- depth:3
[4557]=2557,	-- depth:3
[4566]=2566,	-- depth:3
[4439]=2439,	-- depth:3
[4438]=2438,	-- depth:3
[4437]=2437,	-- depth:3
[4318]=2318,	-- depth:3
[4317]=2317,	-- depth:3
[4316]=2316,	-- depth:3
[4315]=2315,	-- depth:3
[4314]=2314,	-- depth:3
[4313]=2313,	-- depth:3
[4312]=2312,	-- depth:3
[4311]=2311,	-- depth:3
[4310]=2310,	-- depth:3
[4309]=2309,	-- depth:3
[4308]=2308,	-- depth:3
[4307]=2307,	-- depth:3
[4306]=2306,	-- depth:3
[4305]=2305,	-- depth:3
[4304]=2304,	-- depth:3
[4303]=2303,	-- depth:3
[4302]=2302,	-- depth:3
[4301]=2301,	-- depth:3
[4300]=2300,	-- depth:3
[4319]=2319,	-- depth:3
[4320]=2320,	-- depth:3
[4321]=2321,	-- depth:3
[4322]=2322,	-- depth:3
[4342]=2342,	-- depth:3
[4341]=2341,	-- depth:3
[4340]=2340,	-- depth:3
[4339]=2339,	-- depth:3
[4338]=2338,	-- depth:3
[4337]=2337,	-- depth:3
[4336]=2336,	-- depth:3
[4335]=2335,	-- depth:3
[4334]=2334,	-- depth:3
[4299]=2299,	-- depth:3
[4333]=2333,	-- depth:3
[4331]=2331,	-- depth:3
[4330]=2330,	-- depth:3
[4329]=2329,	-- depth:3
[4328]=2328,	-- depth:3
[4327]=2327,	-- depth:3
[4326]=2326,	-- depth:3
[4325]=2325,	-- depth:3
[4324]=2324,	-- depth:3
[4323]=2323,	-- depth:3
[4332]=2332,	-- depth:3
[4343]=2343,	-- depth:3
[4298]=2298,	-- depth:3
[4296]=2296,	-- depth:3
[4271]=2271,	-- depth:3
[4270]=2270,	-- depth:3
[4269]=2269,	-- depth:3
[4268]=2268,	-- depth:3
[4267]=2267,	-- depth:3
[4266]=2266,	-- depth:3
[4265]=2265,	-- depth:3
[4264]=2264,	-- depth:3
[4263]=2263,	-- depth:3
[4262]=2262,	-- depth:3
[4261]=2261,	-- depth:3
[4260]=2260,	-- depth:3
[4259]=2259,	-- depth:3
[4258]=2258,	-- depth:3
[4257]=2257,	-- depth:3
[4256]=2256,	-- depth:3
[4255]=2255,	-- depth:3
[4254]=2254,	-- depth:3
[4253]=2253,	-- depth:3
[4272]=2272,	-- depth:3
[4273]=2273,	-- depth:3
[4274]=2274,	-- depth:3
[4275]=2275,	-- depth:3
[4295]=2295,	-- depth:3
[4294]=2294,	-- depth:3
[4293]=2293,	-- depth:3
[4292]=2292,	-- depth:3
[4291]=2291,	-- depth:3
[4290]=2290,	-- depth:3
[4289]=2289,	-- depth:3
[4288]=2288,	-- depth:3
[4287]=2287,	-- depth:3
[4297]=2297,	-- depth:3
[4286]=2286,	-- depth:3
[4284]=2284,	-- depth:3
[4283]=2283,	-- depth:3
[4282]=2282,	-- depth:3
[4281]=2281,	-- depth:3
[4280]=2280,	-- depth:3
[4279]=2279,	-- depth:3
[4278]=2278,	-- depth:3
[4277]=2277,	-- depth:3
[4276]=2276,	-- depth:3
[4285]=2285,	-- depth:3
[4624]=2624,	-- depth:3
[4344]=2344,	-- depth:3
[4346]=2346,	-- depth:3
[4412]=2412,	-- depth:3
[4411]=2411,	-- depth:3
[4410]=2410,	-- depth:3
[4409]=2409,	-- depth:3
[4408]=2408,	-- depth:3
[4407]=2407,	-- depth:3
[4406]=2406,	-- depth:3
[4405]=2405,	-- depth:3
[4404]=2404,	-- depth:3
[4403]=2403,	-- depth:3
[4402]=2402,	-- depth:3
[4401]=2401,	-- depth:3
[4400]=2400,	-- depth:3
[4399]=2399,	-- depth:3
[4398]=2398,	-- depth:3
[4397]=2397,	-- depth:3
[4396]=2396,	-- depth:3
[4395]=2395,	-- depth:3
[4394]=2394,	-- depth:3
[4413]=2413,	-- depth:3
[4414]=2414,	-- depth:3
[4415]=2415,	-- depth:3
[4416]=2416,	-- depth:3
[4436]=2436,	-- depth:3
[4435]=2435,	-- depth:3
[4434]=2434,	-- depth:3
[4433]=2433,	-- depth:3
[4432]=2432,	-- depth:3
[4431]=2431,	-- depth:3
[4430]=2430,	-- depth:3
[4429]=2429,	-- depth:3
[4428]=2428,	-- depth:3
[4393]=2393,	-- depth:3
[4427]=2427,	-- depth:3
[4425]=2425,	-- depth:3
[4424]=2424,	-- depth:3
[4423]=2423,	-- depth:3
[4422]=2422,	-- depth:3
[4421]=2421,	-- depth:3
[4420]=2420,	-- depth:3
[4419]=2419,	-- depth:3
[4418]=2418,	-- depth:3
[4417]=2417,	-- depth:3
[4426]=2426,	-- depth:3
[4345]=2345,	-- depth:3
[4392]=2392,	-- depth:3
[4390]=2390,	-- depth:3
[4365]=2365,	-- depth:3
[4364]=2364,	-- depth:3
[4363]=2363,	-- depth:3
[4362]=2362,	-- depth:3
[4361]=2361,	-- depth:3
[4360]=2360,	-- depth:3
[4359]=2359,	-- depth:3
[4358]=2358,	-- depth:3
[4357]=2357,	-- depth:3
[4356]=2356,	-- depth:3
[4355]=2355,	-- depth:3
[4354]=2354,	-- depth:3
[4353]=2353,	-- depth:3
[4352]=2352,	-- depth:3
[4351]=2351,	-- depth:3
[4350]=2350,	-- depth:3
[4349]=2349,	-- depth:3
[4348]=2348,	-- depth:3
[4347]=2347,	-- depth:3
[4366]=2366,	-- depth:3
[4367]=2367,	-- depth:3
[4368]=2368,	-- depth:3
[4369]=2369,	-- depth:3
[4389]=2389,	-- depth:3
[4388]=2388,	-- depth:3
[4387]=2387,	-- depth:3
[4386]=2386,	-- depth:3
[4385]=2385,	-- depth:3
[4384]=2384,	-- depth:3
[4383]=2383,	-- depth:3
[4382]=2382,	-- depth:3
[4381]=2381,	-- depth:3
[4391]=2391,	-- depth:3
[4380]=2380,	-- depth:3
[4378]=2378,	-- depth:3
[4377]=2377,	-- depth:3
[4376]=2376,	-- depth:3
[4375]=2375,	-- depth:3
[4374]=2374,	-- depth:3
[4373]=2373,	-- depth:3
[4372]=2372,	-- depth:3
[4371]=2371,	-- depth:3
[4370]=2370,	-- depth:3
[4379]=2379,	-- depth:3
[4252]=2252,	-- depth:3
[4625]=2625,	-- depth:3
[4627]=2627,	-- depth:3
[4881]=2881,	-- depth:3
[4880]=2880,	-- depth:3
[4879]=2879,	-- depth:3
[4878]=2878,	-- depth:3
[4877]=2877,	-- depth:3
[4876]=2876,	-- depth:3
[4875]=2875,	-- depth:3
[4874]=2874,	-- depth:3
[4873]=2873,	-- depth:3
[4872]=2872,	-- depth:3
[4871]=2871,	-- depth:3
[4870]=2870,	-- depth:3
[4869]=2869,	-- depth:3
[4868]=2868,	-- depth:3
[4867]=2867,	-- depth:3
[4866]=2866,	-- depth:3
[4865]=2865,	-- depth:3
[4864]=2864,	-- depth:3
[4863]=2863,	-- depth:3
[4882]=2882,	-- depth:3
[4862]=2862,	-- depth:3
[4883]=2883,	-- depth:3
[4885]=2885,	-- depth:3
[4904]=2904,	-- depth:3
[4903]=2903,	-- depth:3
[4902]=2902,	-- depth:3
[4901]=2901,	-- depth:3
[4900]=2900,	-- depth:3
[4899]=2899,	-- depth:3
[4898]=2898,	-- depth:3
[4897]=2897,	-- depth:3
[4896]=2896,	-- depth:3
[4895]=2895,	-- depth:3
[4894]=2894,	-- depth:3
[4893]=2893,	-- depth:3
[4892]=2892,	-- depth:3
[4891]=2891,	-- depth:3
[4890]=2890,	-- depth:3
[4889]=2889,	-- depth:3
[4888]=2888,	-- depth:3
[4887]=2887,	-- depth:3
[4886]=2886,	-- depth:3
[4884]=2884,	-- depth:3
[4861]=2861,	-- depth:3
[4860]=2860,	-- depth:3
[4859]=2859,	-- depth:3
[4834]=2834,	-- depth:3
[4833]=2833,	-- depth:3
[4832]=2832,	-- depth:3
[4831]=2831,	-- depth:3
[4830]=2830,	-- depth:3
[4829]=2829,	-- depth:3
[4828]=2828,	-- depth:3
[4827]=2827,	-- depth:3
[4826]=2826,	-- depth:3
[4825]=2825,	-- depth:3
[4824]=2824,	-- depth:3
[4823]=2823,	-- depth:3
[4822]=2822,	-- depth:3
[4821]=2821,	-- depth:3
[4820]=2820,	-- depth:3
[4819]=2819,	-- depth:3
[4818]=2818,	-- depth:3
[4817]=2817,	-- depth:3
[4816]=2816,	-- depth:3
[4835]=2835,	-- depth:3
[4836]=2836,	-- depth:3
[4837]=2837,	-- depth:3
[4838]=2838,	-- depth:3
[4858]=2858,	-- depth:3
[4857]=2857,	-- depth:3
[4856]=2856,	-- depth:3
[4855]=2855,	-- depth:3
[4854]=2854,	-- depth:3
[4853]=2853,	-- depth:3
[4852]=2852,	-- depth:3
[4851]=2851,	-- depth:3
[4850]=2850,	-- depth:3
[4905]=2905,	-- depth:3
[4849]=2849,	-- depth:3
[4847]=2847,	-- depth:3
[4846]=2846,	-- depth:3
[4845]=2845,	-- depth:3
[4844]=2844,	-- depth:3
[4843]=2843,	-- depth:3
[4842]=2842,	-- depth:3
[4841]=2841,	-- depth:3
[4840]=2840,	-- depth:3
[4839]=2839,	-- depth:3
[4848]=2848,	-- depth:3
[4906]=2906,	-- depth:3
[4907]=2907,	-- depth:3
[4908]=2908,	-- depth:3
[4974]=2974,	-- depth:3
[4973]=2973,	-- depth:3
[4972]=2972,	-- depth:3
[4971]=2971,	-- depth:3
[4970]=2970,	-- depth:3
[4969]=2969,	-- depth:3
[4968]=2968,	-- depth:3
[4967]=2967,	-- depth:3
[4966]=2966,	-- depth:3
[4965]=2965,	-- depth:3
[4964]=2964,	-- depth:3
[4963]=2963,	-- depth:3
[4962]=2962,	-- depth:3
[4961]=2961,	-- depth:3
[4960]=2960,	-- depth:3
[4959]=2959,	-- depth:3
[4958]=2958,	-- depth:3
[4957]=2957,	-- depth:3
[4956]=2956,	-- depth:3
[4975]=2975,	-- depth:3
[4976]=2976,	-- depth:3
[4977]=2977,	-- depth:3
[4978]=2978,	-- depth:3
[4998]=2998,	-- depth:3
[4997]=2997,	-- depth:3
[4996]=2996,	-- depth:3
[4995]=2995,	-- depth:3
[4994]=2994,	-- depth:3
[4993]=2993,	-- depth:3
[4992]=2992,	-- depth:3
[4991]=2991,	-- depth:3
[4990]=2990,	-- depth:3
[4955]=2955,	-- depth:3
[4989]=2989,	-- depth:3
[4987]=2987,	-- depth:3
[4986]=2986,	-- depth:3
[4985]=2985,	-- depth:3
[4984]=2984,	-- depth:3
[4983]=2983,	-- depth:3
[4982]=2982,	-- depth:3
[4981]=2981,	-- depth:3
[4980]=2980,	-- depth:3
[4979]=2979,	-- depth:3
[4988]=2988,	-- depth:3
[4815]=2815,	-- depth:3
[4954]=2954,	-- depth:3
[4952]=2952,	-- depth:3
[4927]=2927,	-- depth:3
[4926]=2926,	-- depth:3
[4925]=2925,	-- depth:3
[4924]=2924,	-- depth:3
[4923]=2923,	-- depth:3
[4922]=2922,	-- depth:3
[4921]=2921,	-- depth:3
[4920]=2920,	-- depth:3
[4919]=2919,	-- depth:3
[4918]=2918,	-- depth:3
[4917]=2917,	-- depth:3
[4916]=2916,	-- depth:3
[4915]=2915,	-- depth:3
[4914]=2914,	-- depth:3
[4913]=2913,	-- depth:3
[4912]=2912,	-- depth:3
[4911]=2911,	-- depth:3
[4910]=2910,	-- depth:3
[4909]=2909,	-- depth:3
[4928]=2928,	-- depth:3
[4929]=2929,	-- depth:3
[4930]=2930,	-- depth:3
[4931]=2931,	-- depth:3
[4951]=2951,	-- depth:3
[4950]=2950,	-- depth:3
[4949]=2949,	-- depth:3
[4948]=2948,	-- depth:3
[4947]=2947,	-- depth:3
[4946]=2946,	-- depth:3
[4945]=2945,	-- depth:3
[4944]=2944,	-- depth:3
[4943]=2943,	-- depth:3
[4953]=2953,	-- depth:3
[4942]=2942,	-- depth:3
[4940]=2940,	-- depth:3
[4939]=2939,	-- depth:3
[4938]=2938,	-- depth:3
[4937]=2937,	-- depth:3
[4936]=2936,	-- depth:3
[4935]=2935,	-- depth:3
[4934]=2934,	-- depth:3
[4933]=2933,	-- depth:3
[4932]=2932,	-- depth:3
[4941]=2941,	-- depth:3
[4814]=2814,	-- depth:3
[4813]=2813,	-- depth:3
[4812]=2812,	-- depth:3
[4693]=2693,	-- depth:3
[4692]=2692,	-- depth:3
[4691]=2691,	-- depth:3
[4690]=2690,	-- depth:3
[4689]=2689,	-- depth:3
[4688]=2688,	-- depth:3
[4687]=2687,	-- depth:3
[4686]=2686,	-- depth:3
[4685]=2685,	-- depth:3
[4684]=2684,	-- depth:3
[4683]=2683,	-- depth:3
[4682]=2682,	-- depth:3
[4681]=2681,	-- depth:3
[4680]=2680,	-- depth:3
[4679]=2679,	-- depth:3
[4678]=2678,	-- depth:3
[4677]=2677,	-- depth:3
[4676]=2676,	-- depth:3
[4675]=2675,	-- depth:3
[4694]=2694,	-- depth:3
[4695]=2695,	-- depth:3
[4696]=2696,	-- depth:3
[4697]=2697,	-- depth:3
[4717]=2717,	-- depth:3
[4716]=2716,	-- depth:3
[4715]=2715,	-- depth:3
[4714]=2714,	-- depth:3
[4713]=2713,	-- depth:3
[4712]=2712,	-- depth:3
[4711]=2711,	-- depth:3
[4710]=2710,	-- depth:3
[4709]=2709,	-- depth:3
[4674]=2674,	-- depth:3
[4708]=2708,	-- depth:3
[4706]=2706,	-- depth:3
[4705]=2705,	-- depth:3
[4704]=2704,	-- depth:3
[4703]=2703,	-- depth:3
[4702]=2702,	-- depth:3
[4701]=2701,	-- depth:3
[4700]=2700,	-- depth:3
[4699]=2699,	-- depth:3
[4698]=2698,	-- depth:3
[4707]=2707,	-- depth:3
[4718]=2718,	-- depth:3
[4673]=2673,	-- depth:3
[4671]=2671,	-- depth:3
[4646]=2646,	-- depth:3
[4645]=2645,	-- depth:3
[4644]=2644,	-- depth:3
[4643]=2643,	-- depth:3
[4642]=2642,	-- depth:3
[4641]=2641,	-- depth:3
[4640]=2640,	-- depth:3
[4639]=2639,	-- depth:3
[4638]=2638,	-- depth:3
[4637]=2637,	-- depth:3
[4636]=2636,	-- depth:3
[4635]=2635,	-- depth:3
[4634]=2634,	-- depth:3
[4633]=2633,	-- depth:3
[4632]=2632,	-- depth:3
[4631]=2631,	-- depth:3
[4630]=2630,	-- depth:3
[4629]=2629,	-- depth:3
[4628]=2628,	-- depth:3
[4647]=2647,	-- depth:3
[4648]=2648,	-- depth:3
[4649]=2649,	-- depth:3
[4650]=2650,	-- depth:3
[4670]=2670,	-- depth:3
[4669]=2669,	-- depth:3
[4668]=2668,	-- depth:3
[4667]=2667,	-- depth:3
[4666]=2666,	-- depth:3
[4665]=2665,	-- depth:3
[4664]=2664,	-- depth:3
[4663]=2663,	-- depth:3
[4662]=2662,	-- depth:3
[4672]=2672,	-- depth:3
[4661]=2661,	-- depth:3
[4659]=2659,	-- depth:3
[4658]=2658,	-- depth:3
[4657]=2657,	-- depth:3
[4656]=2656,	-- depth:3
[4655]=2655,	-- depth:3
[4654]=2654,	-- depth:3
[4653]=2653,	-- depth:3
[4652]=2652,	-- depth:3
[4651]=2651,	-- depth:3
[4660]=2660,	-- depth:3
[4626]=2626,	-- depth:3
[4719]=2719,	-- depth:3
[4721]=2721,	-- depth:3
[4787]=2787,	-- depth:3
[4786]=2786,	-- depth:3
[4785]=2785,	-- depth:3
[4784]=2784,	-- depth:3
[4783]=2783,	-- depth:3
[4782]=2782,	-- depth:3
[4781]=2781,	-- depth:3
[4780]=2780,	-- depth:3
[4779]=2779,	-- depth:3
[4778]=2778,	-- depth:3
[4777]=2777,	-- depth:3
[4776]=2776,	-- depth:3
[4775]=2775,	-- depth:3
[4774]=2774,	-- depth:3
[4773]=2773,	-- depth:3
[4772]=2772,	-- depth:3
[4771]=2771,	-- depth:3
[4770]=2770,	-- depth:3
[4769]=2769,	-- depth:3
[4788]=2788,	-- depth:3
[4789]=2789,	-- depth:3
[4790]=2790,	-- depth:3
[4791]=2791,	-- depth:3
[4811]=2811,	-- depth:3
[4810]=2810,	-- depth:3
[4809]=2809,	-- depth:3
[4808]=2808,	-- depth:3
[4807]=2807,	-- depth:3
[4806]=2806,	-- depth:3
[4805]=2805,	-- depth:3
[4804]=2804,	-- depth:3
[4803]=2803,	-- depth:3
[4768]=2768,	-- depth:3
[4802]=2802,	-- depth:3
[4800]=2800,	-- depth:3
[4799]=2799,	-- depth:3
[4798]=2798,	-- depth:3
[4797]=2797,	-- depth:3
[4796]=2796,	-- depth:3
[4795]=2795,	-- depth:3
[4794]=2794,	-- depth:3
[4793]=2793,	-- depth:3
[4792]=2792,	-- depth:3
[4801]=2801,	-- depth:3
[4720]=2720,	-- depth:3
[4767]=2767,	-- depth:3
[4765]=2765,	-- depth:3
[4740]=2740,	-- depth:3
[4739]=2739,	-- depth:3
[4738]=2738,	-- depth:3
[4737]=2737,	-- depth:3
[4736]=2736,	-- depth:3
[4735]=2735,	-- depth:3
[4734]=2734,	-- depth:3
[4733]=2733,	-- depth:3
[4732]=2732,	-- depth:3
[4731]=2731,	-- depth:3
[4730]=2730,	-- depth:3
[4729]=2729,	-- depth:3
[4728]=2728,	-- depth:3
[4727]=2727,	-- depth:3
[4726]=2726,	-- depth:3
[4725]=2725,	-- depth:3
[4724]=2724,	-- depth:3
[4723]=2723,	-- depth:3
[4722]=2722,	-- depth:3
[4741]=2741,	-- depth:3
[4742]=2742,	-- depth:3
[4743]=2743,	-- depth:3
[4744]=2744,	-- depth:3
[4764]=2764,	-- depth:3
[4763]=2763,	-- depth:3
[4762]=2762,	-- depth:3
[4761]=2761,	-- depth:3
[4760]=2760,	-- depth:3
[4759]=2759,	-- depth:3
[4758]=2758,	-- depth:3
[4757]=2757,	-- depth:3
[4756]=2756,	-- depth:3
[4766]=2766,	-- depth:3
[4755]=2755,	-- depth:3
[4753]=2753,	-- depth:3
[4752]=2752,	-- depth:3
[4751]=2751,	-- depth:3
[4750]=2750,	-- depth:3
[4749]=2749,	-- depth:3
[4748]=2748,	-- depth:3
[4747]=2747,	-- depth:3
[4746]=2746,	-- depth:3
[4745]=2745,	-- depth:3
[4754]=2754,	-- depth:3
[3500]=7500,	-- depth:3
[4251]=2251,	-- depth:3
[4249]=2249,	-- depth:3
[3755]=7755,	-- depth:3
[3754]=7754,	-- depth:3
[3753]=7753,	-- depth:3
[3752]=7752,	-- depth:3
[3751]=7751,	-- depth:3
[3750]=7750,	-- depth:3
[3749]=7749,	-- depth:3
[3748]=7748,	-- depth:3
[3747]=7747,	-- depth:3
[3746]=7746,	-- depth:3
[3745]=7745,	-- depth:3
[3744]=7744,	-- depth:3
[3743]=7743,	-- depth:3
[3742]=7742,	-- depth:3
[3741]=7741,	-- depth:3
[3740]=7740,	-- depth:3
[3739]=7739,	-- depth:3
[3738]=7738,	-- depth:3
[3737]=7737,	-- depth:3
[3756]=7756,	-- depth:3
[3736]=7736,	-- depth:3
[3757]=7757,	-- depth:3
[3759]=7759,	-- depth:3
[3778]=7778,	-- depth:3
[3777]=7777,	-- depth:3
[3776]=7776,	-- depth:3
[3775]=7775,	-- depth:3
[3774]=7774,	-- depth:3
[3773]=7773,	-- depth:3
[3772]=7772,	-- depth:3
[3771]=7771,	-- depth:3
[3770]=7770,	-- depth:3
[3769]=7769,	-- depth:3
[3768]=7768,	-- depth:3
[3767]=7767,	-- depth:3
[3766]=7766,	-- depth:3
[3765]=7765,	-- depth:3
[3764]=7764,	-- depth:3
[3763]=7763,	-- depth:3
[3762]=7762,	-- depth:3
[3761]=7761,	-- depth:3
[3760]=7760,	-- depth:3
[3758]=7758,	-- depth:3
[3735]=7735,	-- depth:3
[3734]=7734,	-- depth:3
[3733]=7733,	-- depth:3
[3708]=7708,	-- depth:3
[3707]=7707,	-- depth:3
[3706]=7706,	-- depth:3
[3705]=7705,	-- depth:3
[3704]=7704,	-- depth:3
[3703]=7703,	-- depth:3
[3702]=7702,	-- depth:3
[3701]=7701,	-- depth:3
[3700]=7700,	-- depth:3
[3699]=7699,	-- depth:3
[3698]=7698,	-- depth:3
[3697]=7697,	-- depth:3
[3696]=7696,	-- depth:3
[3695]=7695,	-- depth:3
[3694]=7694,	-- depth:3
[3693]=7693,	-- depth:3
[3692]=7692,	-- depth:3
[3691]=7691,	-- depth:3
[3690]=7690,	-- depth:3
[3709]=7709,	-- depth:3
[3710]=7710,	-- depth:3
[3711]=7711,	-- depth:3
[3712]=7712,	-- depth:3
[3732]=7732,	-- depth:3
[3731]=7731,	-- depth:3
[3730]=7730,	-- depth:3
[3729]=7729,	-- depth:3
[3728]=7728,	-- depth:3
[3727]=7727,	-- depth:3
[3726]=7726,	-- depth:3
[3725]=7725,	-- depth:3
[3724]=7724,	-- depth:3
[3779]=7779,	-- depth:3
[3723]=7723,	-- depth:3
[3721]=7721,	-- depth:3
[3720]=7720,	-- depth:3
[3719]=7719,	-- depth:3
[3718]=7718,	-- depth:3
[3717]=7717,	-- depth:3
[3716]=7716,	-- depth:3
[3715]=7715,	-- depth:3
[3714]=7714,	-- depth:3
[3713]=7713,	-- depth:3
[3722]=7722,	-- depth:3
[3780]=7780,	-- depth:3
[3781]=7781,	-- depth:3
[3782]=7782,	-- depth:3
[3848]=7848,	-- depth:3
[3847]=7847,	-- depth:3
[3846]=7846,	-- depth:3
[3845]=7845,	-- depth:3
[3844]=7844,	-- depth:3
[3843]=7843,	-- depth:3
[3842]=7842,	-- depth:3
[3841]=7841,	-- depth:3
[3840]=7840,	-- depth:3
[3839]=7839,	-- depth:3
[3838]=7838,	-- depth:3
[3837]=7837,	-- depth:3
[3836]=7836,	-- depth:3
[3835]=7835,	-- depth:3
[3834]=7834,	-- depth:3
[3833]=7833,	-- depth:3
[3832]=7832,	-- depth:3
[3831]=7831,	-- depth:3
[3830]=7830,	-- depth:3
[3849]=7849,	-- depth:3
[3850]=7850,	-- depth:3
[3851]=7851,	-- depth:3
[3852]=7852,	-- depth:3
[3872]=7872,	-- depth:3
[3871]=7871,	-- depth:3
[3870]=7870,	-- depth:3
[3869]=7869,	-- depth:3
[3868]=7868,	-- depth:3
[3867]=7867,	-- depth:3
[3866]=7866,	-- depth:3
[3865]=7865,	-- depth:3
[3864]=7864,	-- depth:3
[3829]=7829,	-- depth:3
[3863]=7863,	-- depth:3
[3861]=7861,	-- depth:3
[3860]=7860,	-- depth:3
[3859]=7859,	-- depth:3
[3858]=7858,	-- depth:3
[3857]=7857,	-- depth:3
[3856]=7856,	-- depth:3
[3855]=7855,	-- depth:3
[3854]=7854,	-- depth:3
[3853]=7853,	-- depth:3
[3862]=7862,	-- depth:3
[3689]=7689,	-- depth:3
[3828]=7828,	-- depth:3
[3826]=7826,	-- depth:3
[3801]=7801,	-- depth:3
[3800]=7800,	-- depth:3
[3799]=7799,	-- depth:3
[3798]=7798,	-- depth:3
[3797]=7797,	-- depth:3
[3796]=7796,	-- depth:3
[3795]=7795,	-- depth:3
[3794]=7794,	-- depth:3
[3793]=7793,	-- depth:3
[3792]=7792,	-- depth:3
[3791]=7791,	-- depth:3
[3790]=7790,	-- depth:3
[3789]=7789,	-- depth:3
[3788]=7788,	-- depth:3
[3787]=7787,	-- depth:3
[3786]=7786,	-- depth:3
[3785]=7785,	-- depth:3
[3784]=7784,	-- depth:3
[3783]=7783,	-- depth:3
[3802]=7802,	-- depth:3
[3803]=7803,	-- depth:3
[3804]=7804,	-- depth:3
[3805]=7805,	-- depth:3
[3825]=7825,	-- depth:3
[3824]=7824,	-- depth:3
[3823]=7823,	-- depth:3
[3822]=7822,	-- depth:3
[3821]=7821,	-- depth:3
[3820]=7820,	-- depth:3
[3819]=7819,	-- depth:3
[3818]=7818,	-- depth:3
[3817]=7817,	-- depth:3
[3827]=7827,	-- depth:3
[3816]=7816,	-- depth:3
[3814]=7814,	-- depth:3
[3813]=7813,	-- depth:3
[3812]=7812,	-- depth:3
[3811]=7811,	-- depth:3
[3810]=7810,	-- depth:3
[3809]=7809,	-- depth:3
[3808]=7808,	-- depth:3
[3807]=7807,	-- depth:3
[3806]=7806,	-- depth:3
[3815]=7815,	-- depth:3
[3688]=7688,	-- depth:3
[3687]=7687,	-- depth:3
[3686]=7686,	-- depth:3
[3567]=7567,	-- depth:3
[3566]=7566,	-- depth:3
[3565]=7565,	-- depth:3
[3564]=7564,	-- depth:3
[3563]=7563,	-- depth:3
[3562]=7562,	-- depth:3
[3561]=7561,	-- depth:3
[3560]=7560,	-- depth:3
[3559]=7559,	-- depth:3
[3558]=7558,	-- depth:3
[3557]=7557,	-- depth:3
[3556]=7556,	-- depth:3
[3555]=7555,	-- depth:3
[3554]=7554,	-- depth:3
[3553]=7553,	-- depth:3
[3552]=7552,	-- depth:3
[3551]=7551,	-- depth:3
[3550]=7550,	-- depth:3
[3549]=7549,	-- depth:3
[3568]=7568,	-- depth:3
[3569]=7569,	-- depth:3
[3570]=7570,	-- depth:3
[3571]=7571,	-- depth:3
[3591]=7591,	-- depth:3
[3590]=7590,	-- depth:3
[3589]=7589,	-- depth:3
[3588]=7588,	-- depth:3
[3587]=7587,	-- depth:3
[3586]=7586,	-- depth:3
[3585]=7585,	-- depth:3
[3584]=7584,	-- depth:3
[3583]=7583,	-- depth:3
[3548]=7548,	-- depth:3
[3582]=7582,	-- depth:3
[3580]=7580,	-- depth:3
[3579]=7579,	-- depth:3
[3578]=7578,	-- depth:3
[3577]=7577,	-- depth:3
[3576]=7576,	-- depth:3
[3575]=7575,	-- depth:3
[3574]=7574,	-- depth:3
[3573]=7573,	-- depth:3
[3572]=7572,	-- depth:3
[3581]=7581,	-- depth:3
[3592]=7592,	-- depth:3
[3547]=7547,	-- depth:3
[3545]=7545,	-- depth:3
[3520]=7520,	-- depth:3
[3519]=7519,	-- depth:3
[3518]=7518,	-- depth:3
[3517]=7517,	-- depth:3
[3516]=7516,	-- depth:3
[3515]=7515,	-- depth:3
[3514]=7514,	-- depth:3
[3513]=7513,	-- depth:3
[3512]=7512,	-- depth:3
[3511]=7511,	-- depth:3
[3510]=7510,	-- depth:3
[3509]=7509,	-- depth:3
[3508]=7508,	-- depth:3
[3507]=7507,	-- depth:3
[3506]=7506,	-- depth:3
[3505]=7505,	-- depth:3
[3504]=7504,	-- depth:3
[3503]=7503,	-- depth:3
[3502]=7502,	-- depth:3
[3521]=7521,	-- depth:3
[3522]=7522,	-- depth:3
[3523]=7523,	-- depth:3
[3524]=7524,	-- depth:3
[3544]=7544,	-- depth:3
[3543]=7543,	-- depth:3
[3542]=7542,	-- depth:3
[3541]=7541,	-- depth:3
[3540]=7540,	-- depth:3
[3539]=7539,	-- depth:3
[3538]=7538,	-- depth:3
[3537]=7537,	-- depth:3
[3536]=7536,	-- depth:3
[3546]=7546,	-- depth:3
[3535]=7535,	-- depth:3
[3533]=7533,	-- depth:3
[3532]=7532,	-- depth:3
[3531]=7531,	-- depth:3
[3530]=7530,	-- depth:3
[3529]=7529,	-- depth:3
[3528]=7528,	-- depth:3
[3527]=7527,	-- depth:3
[3526]=7526,	-- depth:3
[3525]=7525,	-- depth:3
[3534]=7534,	-- depth:3
[3873]=7873,	-- depth:3
[3593]=7593,	-- depth:3
[3595]=7595,	-- depth:3
[3661]=7661,	-- depth:3
[3660]=7660,	-- depth:3
[3659]=7659,	-- depth:3
[3658]=7658,	-- depth:3
[3657]=7657,	-- depth:3
[3656]=7656,	-- depth:3
[3655]=7655,	-- depth:3
[3654]=7654,	-- depth:3
[3653]=7653,	-- depth:3
[3652]=7652,	-- depth:3
[3651]=7651,	-- depth:3
[3650]=7650,	-- depth:3
[3649]=7649,	-- depth:3
[3648]=7648,	-- depth:3
[3647]=7647,	-- depth:3
[3646]=7646,	-- depth:3
[3645]=7645,	-- depth:3
[3644]=7644,	-- depth:3
[3643]=7643,	-- depth:3
[3662]=7662,	-- depth:3
[3663]=7663,	-- depth:3
[3664]=7664,	-- depth:3
[3665]=7665,	-- depth:3
[3685]=7685,	-- depth:3
[3684]=7684,	-- depth:3
[3683]=7683,	-- depth:3
[3682]=7682,	-- depth:3
[3681]=7681,	-- depth:3
[3680]=7680,	-- depth:3
[3679]=7679,	-- depth:3
[3678]=7678,	-- depth:3
[3677]=7677,	-- depth:3
[3642]=7642,	-- depth:3
[3676]=7676,	-- depth:3
[3674]=7674,	-- depth:3
[3673]=7673,	-- depth:3
[3672]=7672,	-- depth:3
[3671]=7671,	-- depth:3
[3670]=7670,	-- depth:3
[3669]=7669,	-- depth:3
[3668]=7668,	-- depth:3
[3667]=7667,	-- depth:3
[3666]=7666,	-- depth:3
[3675]=7675,	-- depth:3
[3594]=7594,	-- depth:3
[3641]=7641,	-- depth:3
[3639]=7639,	-- depth:3
[3614]=7614,	-- depth:3
[3613]=7613,	-- depth:3
[3612]=7612,	-- depth:3
[3611]=7611,	-- depth:3
[3610]=7610,	-- depth:3
[3609]=7609,	-- depth:3
[3608]=7608,	-- depth:3
[3607]=7607,	-- depth:3
[3606]=7606,	-- depth:3
[3605]=7605,	-- depth:3
[3604]=7604,	-- depth:3
[3603]=7603,	-- depth:3
[3602]=7602,	-- depth:3
[3601]=7601,	-- depth:3
[3600]=7600,	-- depth:3
[3599]=7599,	-- depth:3
[3598]=7598,	-- depth:3
[3597]=7597,	-- depth:3
[3596]=7596,	-- depth:3
[3615]=7615,	-- depth:3
[3616]=7616,	-- depth:3
[3617]=7617,	-- depth:3
[3618]=7618,	-- depth:3
[3638]=7638,	-- depth:3
[3637]=7637,	-- depth:3
[3636]=7636,	-- depth:3
[3635]=7635,	-- depth:3
[3634]=7634,	-- depth:3
[3633]=7633,	-- depth:3
[3632]=7632,	-- depth:3
[3631]=7631,	-- depth:3
[3630]=7630,	-- depth:3
[3640]=7640,	-- depth:3
[3629]=7629,	-- depth:3
[3627]=7627,	-- depth:3
[3626]=7626,	-- depth:3
[3625]=7625,	-- depth:3
[3624]=7624,	-- depth:3
[3623]=7623,	-- depth:3
[3622]=7622,	-- depth:3
[3621]=7621,	-- depth:3
[3620]=7620,	-- depth:3
[3619]=7619,	-- depth:3
[3628]=7628,	-- depth:3
[4250]=2250,	-- depth:3
[3874]=7874,	-- depth:3
[3876]=7876,	-- depth:3
[4131]=2131,	-- depth:3
[4130]=2130,	-- depth:3
[4129]=2129,	-- depth:3
[4128]=2128,	-- depth:3
[4127]=2127,	-- depth:3
[4126]=2126,	-- depth:3
[4125]=2125,	-- depth:3
[4124]=2124,	-- depth:3
[4123]=2123,	-- depth:3
[4122]=2122,	-- depth:3
[4121]=2121,	-- depth:3
[4120]=2120,	-- depth:3
[4119]=2119,	-- depth:3
[4118]=2118,	-- depth:3
[4117]=2117,	-- depth:3
[4116]=2116,	-- depth:3
[4115]=2115,	-- depth:3
[4114]=2114,	-- depth:3
[4113]=2113,	-- depth:3
[4132]=2132,	-- depth:3
[4112]=2112,	-- depth:3
[4133]=2133,	-- depth:3
[4135]=2135,	-- depth:3
[4154]=2154,	-- depth:3
[4153]=2153,	-- depth:3
[4152]=2152,	-- depth:3
[4151]=2151,	-- depth:3
[4150]=2150,	-- depth:3
[4149]=2149,	-- depth:3
[4148]=2148,	-- depth:3
[4147]=2147,	-- depth:3
[4146]=2146,	-- depth:3
[4145]=2145,	-- depth:3
[4144]=2144,	-- depth:3
[4143]=2143,	-- depth:3
[4142]=2142,	-- depth:3
[4141]=2141,	-- depth:3
[4140]=2140,	-- depth:3
[4139]=2139,	-- depth:3
[4138]=2138,	-- depth:3
[4137]=2137,	-- depth:3
[4136]=2136,	-- depth:3
[4134]=2134,	-- depth:3
[4111]=2111,	-- depth:3
[4110]=2110,	-- depth:3
[4109]=2109,	-- depth:3
[4084]=2084,	-- depth:3
[4083]=2083,	-- depth:3
[4082]=2082,	-- depth:3
[4081]=2081,	-- depth:3
[4080]=2080,	-- depth:3
[4079]=2079,	-- depth:3
[4078]=2078,	-- depth:3
[4077]=2077,	-- depth:3
[4076]=2076,	-- depth:3
[4075]=2075,	-- depth:3
[4074]=2074,	-- depth:3
[4073]=2073,	-- depth:3
[4072]=2072,	-- depth:3
[4071]=2071,	-- depth:3
[4070]=2070,	-- depth:3
[4069]=2069,	-- depth:3
[4068]=2068,	-- depth:3
[4067]=2067,	-- depth:3
[4066]=2066,	-- depth:3
[4085]=2085,	-- depth:3
[4086]=2086,	-- depth:3
[4087]=2087,	-- depth:3
[4088]=2088,	-- depth:3
[4108]=2108,	-- depth:3
[4107]=2107,	-- depth:3
[4106]=2106,	-- depth:3
[4105]=2105,	-- depth:3
[4104]=2104,	-- depth:3
[4103]=2103,	-- depth:3
[4102]=2102,	-- depth:3
[4101]=2101,	-- depth:3
[4100]=2100,	-- depth:3
[4155]=2155,	-- depth:3
[4099]=2099,	-- depth:3
[4097]=2097,	-- depth:3
[4096]=2096,	-- depth:3
[4095]=2095,	-- depth:3
[4094]=2094,	-- depth:3
[4093]=2093,	-- depth:3
[4092]=2092,	-- depth:3
[4091]=2091,	-- depth:3
[4090]=2090,	-- depth:3
[4089]=2089,	-- depth:3
[4098]=2098,	-- depth:3
[4156]=2156,	-- depth:3
[4157]=2157,	-- depth:3
[4158]=2158,	-- depth:3
[4224]=2224,	-- depth:3
[4223]=2223,	-- depth:3
[4222]=2222,	-- depth:3
[4221]=2221,	-- depth:3
[4220]=2220,	-- depth:3
[4219]=2219,	-- depth:3
[4218]=2218,	-- depth:3
[4217]=2217,	-- depth:3
[4216]=2216,	-- depth:3
[4215]=2215,	-- depth:3
[4214]=2214,	-- depth:3
[4213]=2213,	-- depth:3
[4212]=2212,	-- depth:3
[4211]=2211,	-- depth:3
[4210]=2210,	-- depth:3
[4209]=2209,	-- depth:3
[4208]=2208,	-- depth:3
[4207]=2207,	-- depth:3
[4206]=2206,	-- depth:3
[4225]=2225,	-- depth:3
[4226]=2226,	-- depth:3
[4227]=2227,	-- depth:3
[4228]=2228,	-- depth:3
[4248]=2248,	-- depth:3
[4247]=2247,	-- depth:3
[4246]=2246,	-- depth:3
[4245]=2245,	-- depth:3
[4244]=2244,	-- depth:3
[4243]=2243,	-- depth:3
[4242]=2242,	-- depth:3
[4241]=2241,	-- depth:3
[4240]=2240,	-- depth:3
[4205]=2205,	-- depth:3
[4239]=2239,	-- depth:3
[4237]=2237,	-- depth:3
[4236]=2236,	-- depth:3
[4235]=2235,	-- depth:3
[4234]=2234,	-- depth:3
[4233]=2233,	-- depth:3
[4232]=2232,	-- depth:3
[4231]=2231,	-- depth:3
[4230]=2230,	-- depth:3
[4229]=2229,	-- depth:3
[4238]=2238,	-- depth:3
[4065]=2065,	-- depth:3
[4204]=2204,	-- depth:3
[4202]=2202,	-- depth:3
[4177]=2177,	-- depth:3
[4176]=2176,	-- depth:3
[4175]=2175,	-- depth:3
[4174]=2174,	-- depth:3
[4173]=2173,	-- depth:3
[4172]=2172,	-- depth:3
[4171]=2171,	-- depth:3
[4170]=2170,	-- depth:3
[4169]=2169,	-- depth:3
[4168]=2168,	-- depth:3
[4167]=2167,	-- depth:3
[4166]=2166,	-- depth:3
[4165]=2165,	-- depth:3
[4164]=2164,	-- depth:3
[4163]=2163,	-- depth:3
[4162]=2162,	-- depth:3
[4161]=2161,	-- depth:3
[4160]=2160,	-- depth:3
[4159]=2159,	-- depth:3
[4178]=2178,	-- depth:3
[4179]=2179,	-- depth:3
[4180]=2180,	-- depth:3
[4181]=2181,	-- depth:3
[4201]=2201,	-- depth:3
[4200]=2200,	-- depth:3
[4199]=2199,	-- depth:3
[4198]=2198,	-- depth:3
[4197]=2197,	-- depth:3
[4196]=2196,	-- depth:3
[4195]=2195,	-- depth:3
[4194]=2194,	-- depth:3
[4193]=2193,	-- depth:3
[4203]=2203,	-- depth:3
[4192]=2192,	-- depth:3
[4190]=2190,	-- depth:3
[4189]=2189,	-- depth:3
[4188]=2188,	-- depth:3
[4187]=2187,	-- depth:3
[4186]=2186,	-- depth:3
[4185]=2185,	-- depth:3
[4184]=2184,	-- depth:3
[4183]=2183,	-- depth:3
[4182]=2182,	-- depth:3
[4191]=2191,	-- depth:3
[4064]=2064,	-- depth:3
[4063]=2063,	-- depth:3
[4062]=2062,	-- depth:3
[3942]=7942,	-- depth:3
[3941]=7941,	-- depth:3
[3940]=7940,	-- depth:3
[3939]=7939,	-- depth:3
[3938]=7938,	-- depth:3
[3937]=7937,	-- depth:3
[3936]=7936,	-- depth:3
[3935]=7935,	-- depth:3
[3934]=7934,	-- depth:3
[3933]=7933,	-- depth:3
[3932]=7932,	-- depth:3
[3931]=7931,	-- depth:3
[3930]=7930,	-- depth:3
[3929]=7929,	-- depth:3
[3928]=7928,	-- depth:3
[3927]=7927,	-- depth:3
[3926]=7926,	-- depth:3
[3925]=7925,	-- depth:3
[3924]=7924,	-- depth:3
[3943]=7943,	-- depth:3
[3944]=7944,	-- depth:3
[3945]=7945,	-- depth:3
[3946]=7946,	-- depth:3
[3966]=7966,	-- depth:3
[3965]=7965,	-- depth:3
[3964]=7964,	-- depth:3
[3963]=7963,	-- depth:3
[3962]=7962,	-- depth:3
[3961]=7961,	-- depth:3
[3960]=7960,	-- depth:3
[3959]=7959,	-- depth:3
[3958]=7958,	-- depth:3
[3923]=7923,	-- depth:3
[3957]=7957,	-- depth:3
[3955]=7955,	-- depth:3
[3954]=7954,	-- depth:3
[3953]=7953,	-- depth:3
[3952]=7952,	-- depth:3
[3951]=7951,	-- depth:3
[3950]=7950,	-- depth:3
[3949]=7949,	-- depth:3
[3948]=7948,	-- depth:3
[3947]=7947,	-- depth:3
[3956]=7956,	-- depth:3
[3967]=7967,	-- depth:3
[3922]=7922,	-- depth:3
[3920]=7920,	-- depth:3
[3895]=7895,	-- depth:3
[3894]=7894,	-- depth:3
[3893]=7893,	-- depth:3
[3892]=7892,	-- depth:3
[3891]=7891,	-- depth:3
[3890]=7890,	-- depth:3
[3889]=7889,	-- depth:3
[3888]=7888,	-- depth:3
[3887]=7887,	-- depth:3
[3886]=7886,	-- depth:3
[3885]=7885,	-- depth:3
[3884]=7884,	-- depth:3
[3883]=7883,	-- depth:3
[3882]=7882,	-- depth:3
[3881]=7881,	-- depth:3
[3880]=7880,	-- depth:3
[3879]=7879,	-- depth:3
[3878]=7878,	-- depth:3
[3877]=7877,	-- depth:3
[3896]=7896,	-- depth:3
[3897]=7897,	-- depth:3
[3898]=7898,	-- depth:3
[3899]=7899,	-- depth:3
[3919]=7919,	-- depth:3
[3918]=7918,	-- depth:3
[3917]=7917,	-- depth:3
[3916]=7916,	-- depth:3
[3915]=7915,	-- depth:3
[3914]=7914,	-- depth:3
[3913]=7913,	-- depth:3
[3912]=7912,	-- depth:3
[3911]=7911,	-- depth:3
[3921]=7921,	-- depth:3
[3910]=7910,	-- depth:3
[3908]=7908,	-- depth:3
[3907]=7907,	-- depth:3
[3906]=7906,	-- depth:3
[3905]=7905,	-- depth:3
[3904]=7904,	-- depth:3
[3903]=7903,	-- depth:3
[3902]=7902,	-- depth:3
[3901]=7901,	-- depth:3
[3900]=7900,	-- depth:3
[3909]=7909,	-- depth:3
[3875]=7875,	-- depth:3
[3968]=7968,	-- depth:3
[3970]=7970,	-- depth:3
[4037]=2037,	-- depth:3
[4036]=2036,	-- depth:3
[4035]=2035,	-- depth:3
[4034]=2034,	-- depth:3
[4033]=2033,	-- depth:3
[4032]=2032,	-- depth:3
[4031]=2031,	-- depth:3
[4030]=2030,	-- depth:3
[4029]=2029,	-- depth:3
[4028]=2028,	-- depth:3
[4027]=2027,	-- depth:3
[4026]=2026,	-- depth:3
[4025]=2025,	-- depth:3
[4024]=2024,	-- depth:3
[4023]=2023,	-- depth:3
[4022]=2022,	-- depth:3
[4021]=2021,	-- depth:3
[4020]=2020,	-- depth:3
[4019]=2019,	-- depth:3
[4038]=2038,	-- depth:3
[4039]=2039,	-- depth:3
[4040]=2040,	-- depth:3
[4041]=2041,	-- depth:3
[4061]=2061,	-- depth:3
[4060]=2060,	-- depth:3
[4059]=2059,	-- depth:3
[4058]=2058,	-- depth:3
[4057]=2057,	-- depth:3
[4056]=2056,	-- depth:3
[4055]=2055,	-- depth:3
[4054]=2054,	-- depth:3
[4053]=2053,	-- depth:3
[4018]=2018,	-- depth:3
[4052]=2052,	-- depth:3
[4050]=2050,	-- depth:3
[4049]=2049,	-- depth:3
[4048]=2048,	-- depth:3
[4047]=2047,	-- depth:3
[4046]=2046,	-- depth:3
[4045]=2045,	-- depth:3
[4044]=2044,	-- depth:3
[4043]=2043,	-- depth:3
[4042]=2042,	-- depth:3
[4051]=2051,	-- depth:3
[3969]=7969,	-- depth:3
[4017]=2017,	-- depth:3
[4015]=2015,	-- depth:3
[3989]=7989,	-- depth:3
[3988]=7988,	-- depth:3
[3987]=7987,	-- depth:3
[3986]=7986,	-- depth:3
[3985]=7985,	-- depth:3
[3984]=7984,	-- depth:3
[3983]=7983,	-- depth:3
[3982]=7982,	-- depth:3
[3981]=7981,	-- depth:3
[3980]=7980,	-- depth:3
[3979]=7979,	-- depth:3
[3978]=7978,	-- depth:3
[3977]=7977,	-- depth:3
[3976]=7976,	-- depth:3
[3975]=7975,	-- depth:3
[3974]=7974,	-- depth:3
[3973]=7973,	-- depth:3
[3972]=7972,	-- depth:3
[3971]=7971,	-- depth:3
[3990]=7990,	-- depth:3
[3991]=7991,	-- depth:3
[3992]=7992,	-- depth:3
[3993]=7993,	-- depth:3
[4014]=2014,	-- depth:3
[4013]=2013,	-- depth:3
[4012]=2012,	-- depth:3
[4011]=2011,	-- depth:3
[4010]=2010,	-- depth:3
[4009]=2009,	-- depth:3
[4008]=2008,	-- depth:3
[4007]=2007,	-- depth:3
[4006]=2006,	-- depth:3
[4016]=2016,	-- depth:3
[4005]=2005,	-- depth:3
[4003]=2003,	-- depth:3
[4002]=2002,	-- depth:3
[7999]=5999,	-- depth:2
[3999]=7999,	-- depth:3
[3998]=7998,	-- depth:3
[3997]=7997,	-- depth:3
[3996]=7996,	-- depth:3
[3995]=7995,	-- depth:3
[3994]=7994,	-- depth:3
[4004]=2004,	-- depth:3
[8000]=4000,	-- depth:3
},
like={
{},
{type=2,add_charm=20,notice_desc="赠送鲜花成功",}
},

like_meta_table_map={
},
other_default_table={alert_time=20,spe_task_reward_id_1=30530,},

fb_default_table={seq=1000,team_type=10,team_mode=0,level_limit=85,open_day_limit=1,cap_limit=5000000,scene_id=8052,show_reward_item={[0]=item_table[40],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15],[4]=item_table[16],[5]=item_table[17]},leader_show_reward_item={[0]=item_table[41],[1]=item_table[2]},title_desc="昆仑玉虚，万仙之首，化形成仙，就在其中",fb_desc="<color=#FFFFFF00>空格</color>传闻昆仑玉虚宫内藏着传说中的琼浆玉液，小葵能否再进一步就靠这件宝物了，于是她带着你准备悄悄潜入这里。<color=#FFFFFF>\n\n                 昆仑雪岭入云端，玉虚仙宫紫雾漫\n\n                 灵鹤翔空仙韵绕，道心自此破尘难</color>",activity_limit=0,task_limit=0,task_added_reward_item={[0]=item_table[42]},leader_pass_reward_item={[0]=item_table[43],[1]=item_table[44],[2]=item_table[45],[3]=item_table[5]},},

team_type_default_table={team_type=10,max_times=2,},

stage_default_table={fb_seq=1100,stage=1,time=60,type=1,param0=101,param1=1,param2=0,pass_reward_item={},desc="<color=#99FFBB>%s</color>秒内未击败怪物副本结束",},

monster_default_table={seq=101,monster_id=11001,monster_num=10,},

gather_default_table={seq=201,index=0,gather_id=2071,gather_times=50,gather_time=3,duration_time=300,gather_reward_item={},drop_id=0,},

pass_reward_default_table={fb_seq=1000,pass_time=90,pass_reward_item={[0]=item_table[19],[1]=item_table[40],[2]=item_table[13],[3]=item_table[14],[4]=item_table[39],[5]=item_table[16],[6]=item_table[17]},leader_pass_reward_item={[0]=item_table[40],[1]=item_table[13],[2]=item_table[14],[3]=item_table[39],[4]=item_table[16],[5]=item_table[17]},help_reward_item={[0]=item_table[40],[1]=item_table[13],[2]=item_table[14],[3]=item_table[39],[4]=item_table[16],[5]=item_table[17]},drop_id=0,star_exp=60,desc="<color=#99FFBB>%s</color>秒内完成所有阶段可达成SSS通关",star_num=3,},

npc_task_default_table={npc_id=10163,task_talk_str="（n）噢？居然有人类敢只身前来魔界||（p）你是...咳...你是谁....咳咳咳...好浓厚的瘴气，让人难以呼吸。||（n）看你有眼缘，本大爷就大发慈悲帮你驱逐瘴气吧。",other_talk_str="啧啧啧啧",},

monster_attr_default_table={grade=1,min_level=1,max_level=1,drop_id=0,},

like_default_table={type=1,reward_item={},add_charm=0,notice_desc="点赞成功",}

}

