--合服活动子标签
FestivalTabButton = FestivalTabButton or BaseClass(BaseRender)
function FestivalTabButton:LoadCallBack()

end
function FestivalTabButton:__delete()

end
function FestivalTabButton:OnFlush(param_t)
    for k, v in pairs(param_t or {"all"}) do
        if k == "FlushSelectState" then
            self:_FlushSelect(v[1] == self.sub_index)
        end
    end
end

function FestivalTabButton:SetSubIndex(index)
    self.sub_index = index
end

function FestivalTabButton:_FlushSelect(is_select)
    self.node_list.img_hl:SetActive(is_select)
    self.node_list.img_normal:SetActive(not is_select)
    self.node_list.text_hl:SetActive(is_select)
    self.node_list.text_normal:SetActive(not is_select)
end