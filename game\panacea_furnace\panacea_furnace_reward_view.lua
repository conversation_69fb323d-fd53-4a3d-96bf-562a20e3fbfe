PanaceaFurnaceRewardView = PanaceaFurnaceRewardView or BaseClass(SafeBaseView)
local ani_flag_t = {}
local ani_ten_flag_t = {}
local reword_count = 0 				--该次抽奖奖励物品个数
local ANI_SPEED = 0.1
local ani_count = 1
local ani_ten_count = 1
local scroll_verticalNormalizedPosition = 1
local col_num = 10 							 	--每行多少个
local row_num = 1								--显示3行
local lerp = 0.015    --1 / (50 - (col_num * row_num)*0)* 0.5		--每次减少多少

function PanaceaFurnaceRewardView:__init()
    self.view_style = ViewStyle.Half
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/panacea_furnace_ui_prefab", "layout_panacea_furnace_reward")
end

function PanaceaFurnaceRewardView:ReleaseCallBack()
	if nil ~= self.reward_cell_list then
		self.reward_cell_list:DeleteMe()
		self.reward_cell_list = nil
	end

	if nil ~= self.reward_ten_cell_list then
		self.reward_ten_cell_list:DeleteMe()
		self.reward_ten_cell_list = nil
	end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end

	if self.ten_time_quest then
		GlobalTimerQuest:CancelQuest(self.ten_time_quest)
		self.ten_time_quest = nil
	end

	if self.move_scroll_quest then
		GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
		self.move_scroll_quest = nil
	end

    if self.baodi_item then
        self.baodi_item:DeleteMe()
        self.baodi_item = nil
    end
end

function PanaceaFurnaceRewardView:OpenCallBack()
	self:Flush()
end

function PanaceaFurnaceRewardView:LoadCallBack()
	self:InitRewardList()
	self:RegisterEvent()

    self.baodi_item = ItemCell.New(self.node_list["baodi_cell"])
end


function PanaceaFurnaceRewardView:InitRewardList()
	self.reward_cell_list = PanaceaFurnaceRewardGrid.New()
    self.reward_cell_list:CreateCells({col = col_num, change_cells_num = 1 , list_view = self.node_list["reward_cell_list"],
    itemRender = PanaceaFurnaceRewardCell})
    self.reward_cell_list:SetStartZeroIndex(false)

	self.reward_ten_cell_list = PanaceaFurnaceRewardGrid.New()
    self.reward_ten_cell_list:CreateCells({col = 5, change_cells_num = 1 , list_view = self.node_list["reward_ten_cell_list"],
    itemRender = TenPanaceaFurnaceRewardCell})
    self.reward_ten_cell_list:SetStartZeroIndex(false)
end

-- 注册按钮事件
function PanaceaFurnaceRewardView:RegisterEvent()
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind1(self.Close, self))	--进入仓库
	XUI.AddClickEventListener(self.node_list.btn_one_more, BindTool.Bind1(self.OnClickAgain, self))		--再来一次
	XUI.AddClickEventListener(self.node_list.skip_anim_toggle, BindTool.Bind(self.OnClickSkipAnim, self)) --动画标记
	XUI.AddClickEventListener(self.node_list.stuff_icon, BindTool.Bind(self.ShowStuffItemTips, self))
end

function PanaceaFurnaceRewardView:SetDataType(mode, cur_count)
	self.mode = mode
    self.cur_count = cur_count
end

-- 再来一次
function PanaceaFurnaceRewardView:OnClickAgain()
	PanaceaFurnaceWGCtrl.Instance:ClickUse(self.mode, true)
end

--动画标记
function PanaceaFurnaceRewardView:OnClickSkipAnim(is_on)
    PanaceaFurnaceWGData.Instance:SetRewardAnimToggleData(not is_on)
end

function PanaceaFurnaceRewardView:ChangeState(is_ten)
	self.node_list["reward_cell_list"]:SetActive(not is_ten)
	self.node_list["reward_ten_cell_list"]:SetActive(is_ten)
end

function PanaceaFurnaceRewardView:ShowStuffItemTips()
	local other_cfg = PanaceaFurnaceWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if not IsEmptyTable(item_cfg) then
    	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_cfg.icon_id})
    end
end

--刷新数据
function PanaceaFurnaceRewardView:OnFlush()
	local other_cfg = PanaceaFurnaceWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if IsEmptyTable(item_cfg) then
    	return
    end

    self.node_list["stuff_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    local item_count = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id)
    local mode_cfg = PanaceaFurnaceWGData.Instance:GetModeCfgByMode(self.mode)
	local color = item_count >= mode_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
	local num_text = item_count .. "/" .. mode_cfg.cost_item_num
	self.node_list["stuff_num"].text.text = ToColorStr(num_text, color)
    self.node_list.btn_again_txt.text.text = Language.PanaceaFurnace.BtnText[self.mode]

    self.baodi_item:SetData({}) --数据清一下
    self:AdjustPosition()
    self:ResetItemData()
end

function PanaceaFurnaceRewardView:AdjustPosition()
    local pos_x = 0
    local rect = self.node_list["reward_cell_list"].rect --一抽 50抽
    local rect_ten = self.node_list["reward_ten_cell_list"].rect
    local rect_bg = self.node_list["ts_rawimg_bg"].rect --背景
    local baodi_container_rect = self.node_list["baodi_container"].rect --保底
    local container_rect = self.node_list["container"].rect --按钮

    local baodi_item = PanaceaFurnaceWGData.Instance:GetBaodItem()
    self.has_baodi = baodi_item.item_id > 0

    self.node_list.baodi_container:SetActive(self.has_baodi)
    if not self.has_baodi then
        self.node_list["reward_cell_list"].image.enabled = false
        if self.cur_count == 1 then   		--寻宝1次
            pos_x = 396
            self.node_list["reward_cell_list"].mask2d.enabled = false
            self.node_list["reward_cell_list"].scroll_rect.enabled = false
            rect.anchoredPosition = Vector2(pos_x, -56)
            self:ChangeState(false)
            rect_bg.anchoredPosition = Vector2(0, -10)
            rect_bg.sizeDelta = Vector2(1380, 364)
            container_rect.anchoredPosition = Vector2(0, 210)
        elseif self.cur_count == 10 then 		--寻宝10次
            self.node_list["reward_cell_list"].mask2d.enabled = true
            pos_x = -16
            self.node_list["reward_ten_cell_list"].scroll_rect.enabled = false
            rect_ten.anchoredPosition = Vector2(pos_x, 69)
            self:ChangeState(true)
            rect_bg.sizeDelta = Vector2(1380, 400)
            rect_bg.anchoredPosition = Vector2(0, 10)
            container_rect.anchoredPosition = Vector2(0, 210)
        elseif self.cur_count == 50 then		--寻宝50次
            pos_x = -16
            self.node_list["reward_cell_list"].scroll_rect.enabled = true
            self.node_list["reward_cell_list"].image.enabled = true
            self:ChangeState(false)
            rect.anchoredPosition = Vector2(pos_x, 21)
            self.node_list["reward_cell_list"].mask2d.enabled = true
            self.is_mask = nil
            rect_bg.anchoredPosition = Vector2(0, -20)
            rect_bg.sizeDelta = Vector2(1380, 480)
            container_rect.anchoredPosition = Vector2(0, 140)
        end
    else
        if self.cur_count == 1 then   		--寻宝1次
            pos_x = 396
            self.node_list["reward_cell_list"].mask2d.enabled = false
            self.node_list["reward_cell_list"].scroll_rect.enabled = false
            rect.anchoredPosition = Vector2(pos_x, -30)
            self:ChangeState(false)
            rect_bg.anchoredPosition = Vector2(0, -50)
            rect_bg.sizeDelta = Vector2(1380, 440)
            baodi_container_rect.anchoredPosition = Vector2(0, -70)
            container_rect.anchoredPosition = Vector2(0, 130)
        elseif self.cur_count == 10 then 		--寻宝10次
            self.node_list["reward_cell_list"].mask2d.enabled = true
            pos_x = -16
            self.node_list["reward_ten_cell_list"].scroll_rect.enabled = false
            rect_ten.anchoredPosition = Vector2(pos_x, 70)
            rect.anchoredPosition = Vector2(pos_x, 0)
            self:ChangeState(true)
            rect_bg.sizeDelta = Vector2(1380, 530)
            rect_bg.anchoredPosition = Vector2(0, -50)
            baodi_container_rect.anchoredPosition = Vector2(0, -110)
            container_rect.anchoredPosition = Vector2(0, 90)
        elseif self.cur_count == 50 then		--寻宝50次
            pos_x = -16
            self.node_list["reward_cell_list"].scroll_rect.enabled = true
            self.node_list["reward_cell_list"].image.enabled = true
            self:ChangeState(false)
            rect.anchoredPosition = Vector2(pos_x, 80)
            self.node_list["reward_cell_list"].mask2d.enabled = true
            self.is_mask = nil
            rect_bg.anchoredPosition = Vector2(0, -20)
            rect_bg.sizeDelta = Vector2(1380, 580)
            baodi_container_rect.anchoredPosition = Vector2(0, -130)
            container_rect.anchoredPosition = Vector2(0, 80)
        end
    end
end

function PanaceaFurnaceRewardView:ResetItemData()
	local is_not_skip = PanaceaFurnaceWGData.Instance:GetRewardAnimToggleData()
    self.node_list.skip_anim_toggle.toggle.isOn = not is_not_skip
    self.node_list.reward_cell_list.scroll_rect.enabled = true

    local baodi_item = PanaceaFurnaceWGData.Instance:GetBaodItem()
    if baodi_item.item_id > 0 then
        self.baodi_item:SetData(baodi_item)
        self:DoBaodiCellAnim(true)
    else
        self:DoBaodiCellAnim(false)
        self.baodi_item:SetData({})
    end

	local reward_list = PanaceaFurnaceWGData.Instance:GetResultRewardList()
    reword_count = #reward_list
	if self.cur_count == 10 then
		if nil ~= self.reward_ten_cell_list then
            ani_ten_flag_t = {}
			self.reward_ten_cell_list:SetDataList(reward_list)
			if self.ten_time_quest then
				GlobalTimerQuest:CancelQuest(self.ten_time_quest)
				self.ten_time_quest = nil
			end

            ani_ten_count = 1
			if is_not_skip then
				self.ten_time_quest = GlobalTimerQuest:AddTimesTimer(function()
					self:DoCellAnimTen(true)
					end, ANI_SPEED, reword_count )
			else
				for i = 1, reword_count do
					self:DoCellAnimTen(false)
				end
			end
		end
    else
		if nil ~= self.reward_cell_list then
            ani_flag_t = {}
			self.reward_cell_list:SetDataList(reward_list)
            self.reward_cell_list:JumptToPrecent(1)
			if self.time_quest then
				GlobalTimerQuest:CancelQuest(self.time_quest)
				self.time_quest = nil
			end

			ani_count = 1
            scroll_verticalNormalizedPosition = 1
            if is_not_skip then
                self.time_quest = GlobalTimerQuest:AddTimesTimer(function()
                    self:DoCellAnim(true)
                    end, ANI_SPEED, reword_count)
            else
                for i = 1, reword_count do
                    self:DoCellAnim(false)
                end
            end

			if self.move_scroll_quest then
				GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
				self.move_scroll_quest = nil
			end

			if reword_count > 30 and is_not_skip then
				self.move_scroll_quest = GlobalTimerQuest:AddTimesTimer(function()
					self:MoveScroll()
					if scroll_verticalNormalizedPosition <= 0 then
						scroll_verticalNormalizedPosition = 0
						if self.move_scroll_quest then
							GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
							self.move_scroll_quest = nil
						end
					end
				end, 0.03, 999999)
			end
		end
    end
end


function PanaceaFurnaceRewardView:DoCellAnimTen(do_tween)
    local cell = self.reward_ten_cell_list:GetCell(ani_ten_count)

    ani_ten_flag_t[ani_ten_count] = true
	ani_ten_count = ani_ten_count + 1
	if cell ~= nil and cell:GetData() ~= nil then
		if do_tween then
			cell.view.transform.localScale = Vector3(2.5, 2.5, 2.5)
			cell.view.transform:DOScale(Vector3(1, 1, 1), 0.2)
		else
			cell.view.transform.localScale = Vector3(1, 1, 1)
		end
		--特效
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0), nil, nil)
		cell:SetActive(true)
    end
end

function PanaceaFurnaceRewardView:DoCellAnim(do_tween)
    local cell = self.reward_cell_list:GetCell(ani_count)
    ani_flag_t[ani_count] = true
    ani_count = ani_count + 1
    if cell ~= nil and cell:GetData() ~= nil then
        cell:SetActive(true)
        if do_tween then
            cell.view.transform.localScale = Vector3(2.5, 2.5, 2.5)
            cell.view.transform:DOScale(Vector3(1, 1, 1), 0.15)
        else
            cell.view.transform.localScale = Vector3(1, 1, 1)
        end
        --特效
        local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
        EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0), nil, nil)
    end
end

function PanaceaFurnaceRewardView:DoBaodiCellAnim(do_tween)
    if do_tween then
       self.node_list["baodi_cell"].transform.localScale = Vector3(2.5,2.5,2.5)
        self.node_list["baodi_cell"].transform:DOScale(Vector3(1,1,1), 0.8)
    else
        self.node_list["baodi_cell"].transform.localScale = Vector3(1, 1, 1)
    end
    --特效
    local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["baodi_cell"].transform, 0.28, Vector3(0, 0, 0),nil,nil)
end

function PanaceaFurnaceRewardView:MoveScroll()
	if ani_count > (col_num * row_num) then
		scroll_verticalNormalizedPosition = scroll_verticalNormalizedPosition - lerp
	else
		scroll_verticalNormalizedPosition = 1
	end
	if self.node_list.reward_cell_list then
		self.node_list.reward_cell_list.scroll_rect.verticalNormalizedPosition = scroll_verticalNormalizedPosition
	end
end

--------------PanaceaFurnaceRewardGrid
PanaceaFurnaceRewardGrid = PanaceaFurnaceRewardGrid or BaseClass(AsyncBaseGrid)

-- 获得指定的格子
function PanaceaFurnaceRewardGrid:GetCell(index)
    for k, v in pairs(self.cell_list) do
        local row = math.floor((index - 1) / self.columns)
        if row == v:GetRows() and v:GetActive()  then
            for k1, v1 in pairs(v:GetAllCell()) do
                if v1:GetIndex() == index then
                    return v1
                end
            end
        end
    end

	return nil
end

------PanaceaFurnaceRewardCell
PanaceaFurnaceRewardCell = PanaceaFurnaceRewardCell or BaseClass(ItemCell)

function PanaceaFurnaceRewardCell:OnFlush()
    self:SetActive(ani_flag_t[self.index] == true)
    self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)
    for k,v in pairs(BaseCell_Ui_Circle_Effect) do
        self:SetEffectEnable(false, v)
    end

    ItemCell.OnFlush(self)
end

function PanaceaFurnaceRewardCell:SetActive(value)
	ItemCell.SetVisible(self, value and (self.index == nil or ani_flag_t[self.index]))
end

-------TenPanaceaFurnaceRewardCell
TenPanaceaFurnaceRewardCell = TenPanaceaFurnaceRewardCell or BaseClass(ItemCell)
function TenPanaceaFurnaceRewardCell:OnFlush()
	self:SetActive(ani_ten_flag_t[self.index] == true)
    self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)
    for k,v in pairs(BaseCell_Ui_Circle_Effect) do
        self:SetEffectEnable(false, v)
    end

    ItemCell.OnFlush(self)
end

function TenPanaceaFurnaceRewardCell:SetActive(value)
	ItemCell.SetActive(self, value and (self.index == nil or ani_ten_flag_t[self.index]))
end