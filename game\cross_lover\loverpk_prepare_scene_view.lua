LoverPkPrepareSceneView = LoverPkPrepareSceneView or BaseClass(SafeBaseView)

function LoverPkPrepareSceneView:__init()
    self:AddViewResource(0, "uis/view/cross_lover_prefab", "layout_loverpk_prepare_scene_view")
end

function LoverPkPrepareSceneView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.match_reward_btn, BindTool.Bind(self.OnClickMatchRewardBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_danren_match, BindTool.Bind(self.OnClickDanrenMatch, self))
    XUI.AddClickEventListener(self.node_list.btn_zhandui_match, BindTool.Bind(self.ClickZhanduiMatch, self))
	XUI.AddClickEventListener(self.node_list.btn_one_key_team, BindTool.Bind(self.ClickOne<PERSON>eyTeam, self))
	XUI.AddClickEventListener(self.node_list.jingcai_btn, BindTool.Bind(self.Click<PERSON>ingCai, self))
	XUI.AddClickEventListener(self.node_list.btn_guess_huaqiu, BindTool.Bind(self.ClickGuessHuaQiu, self))
	XUI.AddClickEventListener(self.node_list.btn_guess_jingcai, BindTool.Bind(self.ClickGuessJIngCai, self))

	self.marry_love_event = GlobalEventSystem:Bind(OtherEventType.ROLE_ONLINE_CHANGE,BindTool.Bind(self.OnOtherRoleOnlineChange, self))
	self.knockout_state_cache = LoverPkWGData.KNOCKOUT_STATE.WAIT_START
	MainuiWGCtrl.Instance:AddBtnToGuajiLine(self.node_list["jingcai"])

	self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
	local remind_list = {
		RemindName.LoverPkQCDJ,
		RemindName.LoverPKDZB,
	}

	for k,v in pairs(remind_list) do
		RemindManager.Instance:Bind(self.remind_callback, v)
	end

	self.node_list.desc_reward_box_tip.text.text = Language.LoverPK.RewardBoxCreateTip

	self:SendLoverInfo()
end

function LoverPkPrepareSceneView:ShowIndexCallBack()
    self:RefreshBtnTime()
end

function LoverPkPrepareSceneView:CloseCallBack()
	if self.node_list["jingcai"] then
		self.node_list["jingcai"].gameObject.transform:SetParent(self.root_node_transform, false)
	end
end

function LoverPkPrepareSceneView:ReleaseCallBack()
	if self.marry_love_event then
		GlobalEventSystem:UnBind(self.marry_love_event)
		self.marry_love_event = nil
	end

	if CountDownManager.Instance:HasCountDown("loverpk_knockout_state_time") then
		CountDownManager.Instance:RemoveCountDown("loverpk_knockout_state_time")
	end

	if self.reward_box_create_event then
        GlobalTimerQuest:CancelQuest(self.reward_box_create_event)
        self.reward_box_create_event = nil
    end

	RemindManager.Instance:UnBind(self.remind_callback)
end

function LoverPkPrepareSceneView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushView()
		elseif k == "reward_box_create" then
			self:OnCreateRewardBoxTip()
        end
    end
end

function LoverPkPrepareSceneView:FlushView()
    local combat_phase = LoverPkWGData.Instance:GetMatchType()

	-- 匹配赛
	if combat_phase == CROSS_COUPLE_2V2_ATCH_TYPE.MATCH then
		local my_score, lover_score = LoverPkWGData.Instance:GetTotalCoupleScore()
		self.node_list.match_score.text.text = (my_score + lover_score) .. "(" .. my_score ..")"

		local get_score_limit = LoverPkWGData.Instance:GetOtherCfgDataByAttrName("get_score_limit")
		local match_count = LoverPkWGData.Instance:GetMatchCount()
		local color = match_count >= get_score_limit and COLOR3B.D_RED or COLOR3B.D_GREEN
		local count = get_score_limit - match_count
		count = count > 0 and count or 0
		self.node_list.match_residue_value.text.text = ToColorStr(count .. "/" .. get_score_limit, color)
		self.node_list.match_name_1.text.text = RoleWGData.Instance:GetRoleVo().role_name

		local lover_info = MarryWGData.Instance:GetLoverInfo2()
		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
		local has_lover = lover_info ~= nil and lover_id > 0
		self.node_list.match_name_2:CustomSetActive(has_lover)

		if has_lover then
			self.node_list.match_name_2.text.text = RoleWGData.Instance.role_vo.lover_name
			local is_on_line = lover_info ~= nil and lover_id > 0 and lover_info.is_online == 1
			local color = is_on_line and COLOR3B.D_GREEN or COLOR3B.GRAY
			local on_line_str = is_on_line and Language.Common.OnLine or Language.Common.OutLine
			self.node_list.match_on_line_2.text.text = ToColorStr(on_line_str, color)
		end

		self.node_list["jingcai"]:CustomSetActive(false)
	else
		local target_knockout_round = LoverPkWGData.Instance:GetCurKnockoutRound()  -- 当前所在轮次
		local knockout_round = target_knockout_round >= 0 and target_knockout_round or 0
		local is_lose, rank_round = LoverPkWGData.Instance:GetKnockoutRoundIsLose()
		local is_bye = LoverPkWGData.Instance:GetKnockoutRoundIsBye(knockout_round)
		local can_join = LoverPkWGData.Instance:CanJoinKnockout()
		local is_winner = LoverPkWGData.Instance:IsKnockoutWinner()
		self.node_list.guess_out_flag:CustomSetActive(can_join and is_lose)
		self.node_list["jingcai"]:CustomSetActive(true)
		-- self.node_list["jingcai"]:CustomSetActive(knockout_round < 3)
		local guess_info_str = can_join and ((is_lose or knockout_round == 4) and "" or is_bye and Language.LoverPK.LunKongTip or "") or ""

		-- self.node_list.guess_pro_title.text.text = Language.LoverPK.TaoTaiGrade[knockout_round]
		-- self.node_list.guess_pro_content.text.text = string.format(Language.LoverPK.TaoTaiGradeKnockout[knockout_round], guess_info_str)

		local guess_rank_title_str, guess_rank_value = "", ""
		if not can_join then
			guess_rank_title_str = Language.LoverPK.RankInfoTip
			guess_rank_value = Language.LoverPK.CanNotJoinKnockout
		elseif can_join and is_lose then
			guess_rank_title_str = Language.LoverPK.RankInfoTip
			local lose_rank_round = rank_round <= 0 and 0 or rank_round >= 3 and 3 or rank_round
			guess_rank_value = string.format(Language.LoverPK.KnockoutLoseTip, LoverPkWGData.COMPETIITION_SYSTEM_ORDER[lose_rank_round])
		elseif knockout_round == 4 and is_winner then
			guess_rank_title_str = Language.LoverPK.RankInfoTip
			guess_rank_value = Language.LoverPK.KnockoutWinner
		end

		self.node_list.guess_rank_title.text.text = guess_rank_title_str
		self.node_list.guess_rank_value.text.text = guess_rank_value
		self.node_list.guess_btn:CustomSetActive(not can_join or is_lose)
		self.node_list.guess_role_info:CustomSetActive(can_join and not is_lose)

		local knockout_state_end_time = LoverPkWGData.Instance:GetKnockoutStateEndTime()
		if CountDownManager.Instance:HasCountDown("loverpk_knockout_state_time") then
			CountDownManager.Instance:RemoveCountDown("loverpk_knockout_state_time")
		end

		local knockout_state = LoverPkWGData.Instance:GetKnockoutState()
		if knockout_state == LoverPkWGData.KNOCKOUT_STATE.WAIT_START or knockout_state == LoverPkWGData.KNOCKOUT_STATE.IN_COMBAT then
			self.node_list.guess_time.text.text = TimeUtil.FormatSecond(knockout_state_end_time, 2)
			local jingcai_time_str = knockout_state == LoverPkWGData.KNOCKOUT_STATE.IS_END and "" or TimeUtil.FormatSecond(knockout_state_end_time, 2)
			self.node_list.jingcai_time_text.text.text = string.format(Language.LoverPK.JingCaiState[knockout_state], jingcai_time_str)

			CountDownManager.Instance:AddCountDown("loverpk_knockout_state_time",
			function (elapse_time, total_time)
				if self.node_list.guess_time then
					local now_knockout_state = LoverPkWGData.Instance:GetKnockoutState()

					if self.knockout_state_cache == LoverPkWGData.KNOCKOUT_STATE.IN_COMBAT then
						self.node_list.guess_pro_title.text.text = Language.LoverPK.TaoTaiGrade[knockout_round]
						self.node_list.guess_pro_content.text.text = string.format(Language.LoverPK.TaoTaiGradeKnockout[knockout_round], guess_info_str)
					elseif now_knockout_state == LoverPkWGData.KNOCKOUT_STATE.WAIT_START then
						self.node_list.guess_pro_title.text.text = Language.LoverPK.TaoTaiGrade[target_knockout_round + 1]
						self.node_list.guess_pro_content.text.text = string.format(Language.LoverPK.TaoTaiGradeKnockout[target_knockout_round + 1], "")
					end

					if self.knockout_state_cache == LoverPkWGData.KNOCKOUT_STATE.IN_COMBAT and now_knockout_state == LoverPkWGData.KNOCKOUT_STATE.WAIT_START then
						LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.QUERY_KNOCKOUT_MATCH_INFO)
					end

					self.knockout_state_cache = now_knockout_state
					self.node_list.guess_state.text.text = Language.LoverPK.TaoTaiState[now_knockout_state] or ""
					local jingcai_time_desc = Language.LoverPK.JingCaiState[now_knockout_state]
					-- local jingcai_time_str = now_knockout_state == LoverPkWGData.KNOCKOUT_STATE.IS_END and "" or TimeUtil.FormatSecond(knockout_state_end_time, 2)

					if target_knockout_round < 0 then
						local time = TimeUtil.FormatSecond(total_time - elapse_time, 2)
						self.node_list.guess_time.text.text = time
						self.node_list.jingcai_time_text.text.text = string.format(jingcai_time_desc, time)
					else
						local knockout_ready_time = LoverPkWGData.Instance:GetKnockoutCfgByAttrName("knockout_ready_time") or 0   -- 准备时间  30s
						local fight_time = LoverPkWGData.Instance:GetKnockoutCfgByAttrName("fight_time") or 0                       --战斗时间 180

						if now_knockout_state == LoverPkWGData.KNOCKOUT_STATE.IN_COMBAT then
							local time = TimeUtil.FormatSecond(total_time - elapse_time - knockout_ready_time, 2)
							self.node_list.guess_time.text.text = time
							self.node_list.jingcai_time_text.text.text = string.format(jingcai_time_desc, time)
						else
							if target_knockout_round >= 3 then
								self.node_list.guess_state.text.text = Language.LoverPK.TaoTaiState[2]
								self.node_list.jingcai_time_text.text.text = string.format(Language.LoverPK.JingCaiState[2], "")
								self.node_list.guess_time.text.text = ""
							else
								local time = TimeUtil.FormatSecond(total_time - elapse_time, 2)
								self.node_list.guess_time.text.text = time
								self.node_list.jingcai_time_text.text.text = string.format(jingcai_time_desc, time)
							end
						end
					end
				end
			end,
			function ()
				if self.node_list.guess_time then
					self.node_list.guess_time.text.text = ""
				end
			end, nil, knockout_state_end_time)
		else
			self.node_list.guess_state.text.text = Language.LoverPK.TaoTaiState[2]
			self.node_list.jingcai_time_text.text.text = string.format(Language.LoverPK.JingCaiState[2], "")
			self.node_list.guess_time.text.text = ""
			self.node_list.guess_pro_title.text.text = Language.LoverPK.TaoTaiGrade[4]
			self.node_list.guess_pro_content.text.text = string.format(Language.LoverPK.TaoTaiGradeKnockout[4], "")
		end

		self.node_list.guess_name_1.text.text = RoleWGData.Instance:GetRoleVo().role_name
		local lover_info = MarryWGData.Instance:GetLoverInfo2()
		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
		local has_lover_info = lover_info ~= nil and lover_id > 0
		self.node_list.guess_name_2:CustomSetActive(has_lover_info)

		if has_lover_info then
			self.node_list.guess_name_2.text.text = RoleWGData.Instance.role_vo.lover_name
			local is_on_line = has_lover_info and lover_info.is_online == 1
			local color = is_on_line and COLOR3B.D_GREEN or COLOR3B.GRAY
			local on_line_str = is_on_line and Language.Common.OnLine or Language.Common.OutLine
			self.node_list.guess_on_line_2.text.text = ToColorStr(on_line_str, color)
		end
	end

    self.node_list.prepare_match_btn:CustomSetActive(combat_phase == CROSS_COUPLE_2V2_ATCH_TYPE.MATCH)
    self.node_list.match_info:CustomSetActive(combat_phase == CROSS_COUPLE_2V2_ATCH_TYPE.MATCH)
    self.node_list.guess_info:CustomSetActive(combat_phase == CROSS_COUPLE_2V2_ATCH_TYPE.KNOCKOUT)

	-- 当前在情侣匹配状态 如果情侣下线，则推出匹配 提示
	local match_team_type = LoverPkWGData.Instance:GetMatchingTeamType()
	if LoverPkWGData.Instance:GetIsMatching() and match_team_type == CROSS_COUBLE_2V2_MATCH_TYPE.COUPLE then
		local lover_info = MarryWGData.Instance:GetLoverInfo2()
		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
		if nil == lover_info or lover_id == 0 then
			return
		end

		if lover_info.is_online ~= 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.LoverIsNotOnlion)
			LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.CANCEL_MATCH)
		end
	end
end

function LoverPkPrepareSceneView:OnClickDanrenMatch()
    if LoverPkWGData.Instance:GetIsMatching() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.IsMatchingNow)
        LoverPkWGCtrl.Instance:OpenLoverPKMatchingView()
    else
        local ok_fun = function()
			LoverPkWGCtrl.Instance:ReqStartMatch(CROSS_COUBLE_2V2_MATCH_TYPE.SINGLE_PERSON)
        end

        TipWGCtrl.Instance:OpenAlertTips(Language.LoverPK.SingleMatchTip, ok_fun, nil, nil, nil, nil, nil, Language.LoverPK.SingleMatch, Language.LoverPK.CancleMeatch)
    end
end

function LoverPkPrepareSceneView:ClickZhanduiMatch()
	local lover_info = MarryWGData.Instance:GetLoverInfo2()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	if nil == lover_info or lover_id == 0 then
		return
	end

	if lover_info.is_online ~= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.LoverIsNotOnlion)
		if LoverPkWGData.Instance:GetIsMatching() then
			LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.CANCEL_MATCH)
		end

		return
	end

    if LoverPkWGData.Instance:GetIsMatching() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.IsMatchingNow)
        LoverPkWGCtrl.Instance:OpenLoverPKMatchingView()
    else
        if SocietyWGData.Instance:GetIsInTeam() == 1 then
            if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
                if SocietyWGData.Instance:GetTeamMemberCount() == 1 then
                    self:HandleMemberCountOne()
                else
                    self:HandleOtherMemberCanNot()
                end
            else
                self:HandleMemberClickMatch()
            end
        else
            self:HandleNotTeamClickZhanDuiMatch()
        end
    end
end

function LoverPkPrepareSceneView:HandleNotTeamClickZhanDuiMatch()
	TipWGCtrl.Instance:OpenAlertTips(Language.LoverPK.ZhanDuiMatch, 
	function ()
		ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
		local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
		NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
		NewTeamWGCtrl.Instance:SendInviteUser(lover_id, 0 , 1)
	end, 
	function ()
		LoverPkWGCtrl.Instance:ReqStartMatch(CROSS_COUBLE_2V2_MATCH_TYPE.SINGLE_PERSON)
	end, nil, nil, nil, nil, Language.LoverPK.LoverMatch, Language.LoverPK.SingleMatch)
end

function LoverPkPrepareSceneView:HandleMemberClickMatch()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.OnlyTeamLeaderCanMatch)
end

function LoverPkPrepareSceneView:HandleMemberCountOne()
	TipWGCtrl.Instance:OpenAlertTips(Language.LoverPK.ZhanDuiMatch, function ()
		ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
		local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
		NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
		NewTeamWGCtrl.Instance:SendInviteUser(lover_id, 0 , 1)
	end, 
	function ()
		LoverPkWGCtrl.Instance:ReqStartMatch(CROSS_COUBLE_2V2_MATCH_TYPE.SINGLE_PERSON)
	end, nil, nil, nil, nil, Language.LoverPK.LoverMatch, Language.LoverPK.SingleMatch)
end

-- 判断仙侣是否在队伍中， 且在当前场景
function LoverPkPrepareSceneView:HandleOtherMemberCanNot()
	local other_member_list = SocietyWGData.Instance:GetTeamOtherMemberList()
	local scene_id = Scene.Instance:GetSceneId()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local is_lover_in_team = false
	local lover_not_in_scene = false
	local lover_data = {}

	local other_scene_member_list = {}
	local offline_member_list = {}
	for i, v in ipairs(other_member_list) do
		if v.scene_id ~= scene_id then
			table.insert(other_scene_member_list, v)
		end
		
		if v.scene_id == 0 then
			table.insert(offline_member_list, v)
		end

		if v.role_id == lover_id then
			is_lover_in_team = true
			lover_not_in_scene = v.scene_id ~= scene_id
			lover_data = v
		end
	end

	if not is_lover_in_team then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.LoverIsNotInTeam)
		return
	end

	if SocietyWGData.Instance:GetTeamMemberCount() > 2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.TeamMenberNumOut)
		return
	end

	local lover_info = MarryWGData.Instance:GetLoverInfo2()
	if nil == lover_info or lover_id == 0 then
		return
	end

	if lover_info.is_online ~= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.LoverIsNotOnlion)
		return
	end

	if lover_not_in_scene then
		local desc_content = string.format(Language.LoverPK.LoverMatchLoverNotInScene, lover_data.name)
		TipWGCtrl.Instance:OpenAlertTips(desc_content, function ()
			CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteReq, EnumInviteStartCrossReason.LoverPKMatch, lover_data.orgin_role_id)
		end, function ()
			
		end)
	else
		LoverPkWGCtrl.Instance:ReqStartMatch(CROSS_COUBLE_2V2_MATCH_TYPE.COUPLE)
	end
end

function LoverPkPrepareSceneView:OnOtherRoleOnlineChange(other_role_id, is_online)
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0

	if lover_id <= 0 or lover_id ~= other_role_id then
		return
	end

	self:SendLoverInfo()
end

function LoverPkPrepareSceneView:SendLoverInfo()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	BrowseWGCtrl.Instance:BrowRoelInfo(lover_id, BindTool.Bind1(self.SetLoverShowInfo, self))
end

function LoverPkPrepareSceneView:SetLoverShowInfo(protocol)
	if not protocol then
		return
	end

	MarryWGData.Instance:SetLoverInfo2(protocol)
	self:Flush()
end

function LoverPkPrepareSceneView:ClickOneKeyTeam()
	ViewManager.Instance:Open(GuideModuleName.NewTeamView)
end

function LoverPkPrepareSceneView:OnClickMatchRewardBtn()
	ViewManager.Instance:Open(GuideModuleName.LoverPkView, TabIndex.lover_pk_qcdj)
end

function LoverPkPrepareSceneView:RefreshBtnTime(is_force_hide)
	if LoverPkWGData.Instance:GetIsMatching() and not is_force_hide then
		local matching_type = LoverPkWGData.Instance:GetMatchingTeamType()
		self.node_list.match_danren:CustomSetActive(matching_type == CROSS_COUBLE_2V2_MATCH_TYPE.SINGLE_PERSON)
		self.node_list.match_zhandui:SetActive(matching_type == CROSS_COUBLE_2V2_MATCH_TYPE.COUPLE)
		self.node_list.duanwei_icon:SetActive(false)
		self.node_list.wait_time:SetActive(true)
		self.node_list.wait_time.text.text = string.format(Language.Common.TimeMiao, LoverPkWGData.Instance:GetMatchTime())
	else
		self.node_list.duanwei_icon:SetActive(true)
		self.node_list.wait_time:SetActive(false)
		self.node_list.match_danren:SetActive(false)
		self.node_list.match_zhandui:SetActive(false)
	end
end

function LoverPkPrepareSceneView:ClickGuessHuaQiu()
	local cfg = LoverPkWGData.Instance:GetKnockoutGuardMonstersCfg()
	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()

	if scene_type ~= SceneType.CROSS_PK_LOVER_READY then
		return
	end

	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	GuajiWGCtrl.Instance:MoveToPos(scene_id, cfg.pos_x, cfg.pos_y, 3)
end

function LoverPkPrepareSceneView:ClickGuessJIngCai()
	LoverPkWGCtrl.Instance:OpenLoverPKMsgView(TabIndex.lover_pk_msg_dzb)
end

function LoverPkPrepareSceneView:ClickJingCai()
	LoverPkWGCtrl.Instance:OpenLoverPKMsgView(TabIndex.lover_pk_msg_dzb)
end

function LoverPkPrepareSceneView:OnRemindChange(remind_name, num)
	if remind_name == RemindName.LoverPkQCDJ then
		if self.node_list.match_reward_remind then
			self.node_list.match_reward_remind:SetActive(num > 0)
		end
	elseif remind_name == RemindName.LoverPKDZB then
		if self.node_list.jingcai_btn_red then
			self.node_list.jingcai_btn_red:SetActive(num > 0)
		end
	end
end

function LoverPkPrepareSceneView:OnCreateRewardBoxTip()
	self.node_list.reward_box_tip.transform.localScale = Vector3(0,0,0)
	self.node_list.reward_box_tip:CustomSetActive(true)
	self.node_list.reward_box_tip.rect:DOScale(Vector3(1, 1, 1), 0.3)

    if self.reward_box_create_event then
        GlobalTimerQuest:CancelQuest(self.reward_box_create_event)
        self.reward_box_create_event = nil
    end

	self.reward_box_create_event = GlobalTimerQuest:AddDelayTimer(function ()
		self.node_list.reward_box_tip:CustomSetActive(false)
	end, 5)
end