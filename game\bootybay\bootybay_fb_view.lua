BootyBayFBView = BootyBayFBView or BaseClass(SafeBaseView)

function BootyBayFBView:__init()
	self.view_layer = UiLayer.MainUILow
	self:AddViewResource(0, "uis/view/bootybay_ui_prefab", "layout_bootybay_fb_follow_view")
	self.active_close = false
end

function BootyBayFBView:__delete()
end

function BootyBayFBView:LoadCallBack()
	self.reward_list = {}
	self.obj = self.node_list["panel"].gameObject
	self.obj:SetActive(true)

	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
	local reward_list = other_cfg.danrenreward
	--local str = Language.BootyBay.PersonageLevelDesc
	if self.boody_item_list == nil then
	self.boody_item_list = AsyncListView.New(BoodyCellRender,self.node_list["reward_list"])
	end
	
	self.data = {}
	for k,v in pairs(reward_list) do
		table.insert(self.data,v)
	end
	self.node_list.guaji_btn.button:AddClickListener(BindTool.Bind(self.OnClickGuaJiBtn,self))
end

-- 切换标签调用
function BootyBayFBView:ShowIndexCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil,function ()
		MainuiWGCtrl.Instance:GetTaskMaskRootNode(function ()
			local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
            if self.obj then
                self.obj:SetActive(true)
                BootyBayWGData.Instance:SetWaBaoType(WABAO_TYPE.WABAO_TYPE_BAOTU) --再次挑战时设置一下，
				self.obj.transform:SetParent(parent.gameObject.transform, false)
			end
		end)
	end)

	self:Flush()
	self.boody_item_list:SetDataList(self.data)
end

function BootyBayFBView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
	end

	if self.reward_list then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end
	
	if self.boody_item_list then
		self.boody_item_list:DeleteMe()
		self.boody_item_list = nil
	end
end

function BootyBayFBView:OnFlush()
	local kill_boss_count = BootyBayWGData.Instance:GetPersonKillBossNum()
	self.node_list.task_desc.text.text = string.format(Language.BootyBay.FBTaskDesc,kill_boss_count)
	self.node_list.task_desc2:SetActive(false)

end

-- 关闭前调用
function BootyBayFBView:CloseCallBack()
	if self.obj then
		self.obj:SetActive(false)
	end
end

function BootyBayFBView:OpenCallBack()

end

function BootyBayFBView:OnClickGuaJiBtn()
	local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
	if not guaji_state then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)  --挂机
	end
end