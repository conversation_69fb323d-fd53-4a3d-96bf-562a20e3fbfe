-- 预加载
PreloadManager = PreloadManager or BaseClass()
local TypeUnityMaterial = typeof(UnityEngine.Material)
local TypeRuntimeAnimatorController = typeof(UnityEngine.RuntimeAnimatorController)
local TypeVideoClip = typeof(UnityEngine.Video.VideoClip)
local TypeRenderTexture = typeof(UnityEngine.RenderTexture)


function PreloadManager:__init()
	if PreloadManager.Instance ~= nil then
		print_error("PreloadManager to create singleton twice!")
	end
	PreloadManager.Instance = self

	self.complete = false
	self.loadcfg = {
		{"misc/material", "GuideMask", TypeUnityMaterial},
		{"misc/material", "UI_NormalGrey", TypeUnityMaterial},
		{"misc/material", "UI_SHJGrey", TypeUnityMaterial},
		{"misc/projector_prefab", "UI3DShadowProjetor"},					-- 3D影子投影器
		{"scenes_prefab", "CameraFollow"},
		{"scenes_prefab","CameraBossFollow"},
		{"misc_prefab", "SkillRangObj"},
		{"uis/view/login_ui/videos", "login_main_bg_video", TypeVideoClip},
		{"uis/view/login_ui/videos", "login_main_bg_video_rt", TypeRenderTexture},
		
		{"uis/view/miscpre_load_prefab", "BaseView"},
		{"uis/view/miscpre_load_prefab", "SceneLoadingView"}, 				--预加SceneLoadingView
		{"uis/view/miscpre_load_prefab", "RichButton"},						-- 聊天有点击事件的按钮
		{"uis/view/miscpre_load_prefab", "RichButtonNotTarget"},			-- 聊天无点击事件的按钮
		{"uis/view/miscpre_load_prefab", "RichImage"},						-- 聊天
		{"uis/view/miscpre_load_prefab", "ChatChannel"},					-- 聊天频道
		{"uis/view/miscpre_load_prefab", "VioceButtonLeft"},				-- 语音左按钮
		{"uis/view/miscpre_load_prefab", "VioceButtonRight"},				-- 语音右按钮
		{"uis/view/miscpre_load_prefab", "LeisureBubble"},					-- 预加载场景框
		{"uis/view/miscpre_load_prefab", "ContentLeft"},					-- 预加载聊天框
		{"uis/view/miscpre_load_prefab", "ContentRight"},					-- 预加载聊天框
		{"uis/view/miscpre_load_prefab", "SysContentLeft"},				 	-- 预加载聊天框
		{"uis/view/miscpre_load_prefab", "SystemContentRight"},				-- 预加载聊天框
		{"uis/view/miscpre_load_prefab", "ContentLeft_F"},					-- 预加载聊天框
		{"uis/view/miscpre_load_prefab", "ContentRight_F"},					-- 预加载聊天框
		{"uis/view/miscpre_load_prefab", "TeamContentCenter"},				-- 预加载聊天框
		{"uis/view/miscpre_load_prefab", "hudong_content_center"},			-- 预加载聊天框
		{"uis/view/miscpre_load_prefab", "layout_attribute_cell"},			-- 预加载属性格子
		{"uis/view/miscpre_load_prefab", "layout_attribute_cell2"},			-- 预加载属性格子2
		{"uis/view/miscpre_load_prefab", "layout_attribute_cell3"},			-- 预加载属性格子3
		{"uis/view/miscpre_load_prefab", "ItemCell"},
		{"uis/view/miscpre_load_prefab", "DiamondItemCell"},
		{"uis/view/miscpre_load_prefab", "MainuiIcon"},						-- 预加载主界面Icon
		{"uis/view/miscpre_load_prefab", "MoneyBar"},						-- 预加载金钱bar
		{"uis/view/miscpre_load_prefab", "BaseHeadCell"},					-- 头像框
		{"uis/view/miscpre_load_prefab", "MapPoint"},
		{"uis/view/miscpre_load_prefab", "SpeakerNoticeView"},				--喇叭
		{"uis/view/miscpre_load_prefab", "layout_bottom"},					-- npc对话
		{"uis/view/miscpre_load_prefab", "SystemNumberView"},
		{"uis/view/miscpre_load_prefab", "SystemTipsView"},
		{"uis/view/miscpre_load_prefab", "SystemImgTipsView"},
		{"uis/view/miscpre_load_prefab", "ViewLoading"},
		{"uis/view/miscpre_load_prefab", "btn_view_close"},

		{"uis/view/common_ui_style_prefab", "common_capability"},				-- 通用样式-战力
		{"uis/view/common_ui_style_prefab", "common_capability_max"},			-- 通用样式-满级战力

		{"uis/view/common_panel_prefab", "VerticalTabbar"},
		{"uis/view/common_panel_prefab", "HorizontalTabbar"},
		{"uis/view/common_panel_prefab", "layout_commmon_panel3_2"},

		{"uis/view/main_ui_prefab", "ActivityButtonView"},					--活动按钮
		{"uis/view/main_ui_prefab", "BtnMainuiFunction"},					--活动按钮
		{"uis/view/main_ui_prefab",	"ChatCell"},							-- 主界面ChatCell
		{"uis/view/main_ui_prefab",	"ChatCell2"},							-- 主界面ChatCell

		{"misc/overridecontroller", "1101_Controller", TypeRuntimeAnimatorController},
		-- {"misc/overridecontroller", "1102_Controller", TypeRuntimeAnimatorController},
		{"misc/overridecontroller", "1103_Controller", TypeRuntimeAnimatorController},
		-- {"misc/overridecontroller", "1104_Controller", TypeRuntimeAnimatorController},
		{"misc/overridecontroller", "3101_Controller", TypeRuntimeAnimatorController},
		{"misc/overridecontroller", "3102_Controller", TypeRuntimeAnimatorController},
		{"misc/overridecontroller", "3103_Controller", TypeRuntimeAnimatorController},
		-- {"misc/overridecontroller", "3104_Controller", TypeRuntimeAnimatorController},

		{"uis/view/modeldisplay_prefab", "UIModelDisplay"},
		{"uis/view/ming_wen_ui_prefab", "mingwen_item_cell"},
	}

	self.load_index = 0
	self.complete_index = 0
	self.loaded_list = {}
	self.loaded_callback = nil
end

function PreloadManager:__delete()
	if self.main_open_event then
		GlobalEventSystem:UnBind(self.main_open_event)
		self.main_open_event = nil
	end
	self.loaded_callback = nil
	PreloadManager.Instance = nil
end

function PreloadManager:GetLoadListCfg()
	return self.loadcfg
end

function PreloadManager:Start()
	self.complete = false
	self.total_count = #self.loadcfg
	PushCtrl(self)
end

function PreloadManager:WaitComplete(loaded_callback)
	if self.complete then
		loaded_callback(1)
	else
		self.loaded_callback = loaded_callback
	end
end

function PreloadManager:Update()
	if self.load_index < #self.loadcfg then
		for i = 1, 10 do
			self.load_index = self.load_index + 1
			if self.load_index <= #self.loadcfg then
				local cfg = self.loadcfg[self.load_index]

				if cfg[3] == TypeUnityMaterial then
					ResPoolMgr:GetMaterial(cfg[1], cfg[2], BindTool.Bind(self.OnLoadComplete, self, self.load_index), false)
				elseif cfg[3] == TypeRuntimeAnimatorController then
					ResPoolMgr:GetAnimatorController(cfg[1], cfg[2], BindTool.Bind(self.OnLoadComplete, self, self.load_index), false)
				elseif cfg[3] == TypeVideoClip then
					ResPoolMgr:GetVideoClip(cfg[1], cfg[2], BindTool.Bind(self.OnLoadComplete, self, self.load_index), false)
				elseif cfg[3] == TypeRenderTexture then
					ResPoolMgr:GetRenderTexture(cfg[1], cfg[2], BindTool.Bind(self.OnLoadComplete, self, self.load_index), false)
				else
					ResPoolMgr:GetPrefab(cfg[1], cfg[2], BindTool.Bind(self.OnLoadComplete, self, self.load_index), false)
				end
			end
		end
	else
		PopCtrl(self)
	end
end

function PreloadManager:OnLoadComplete(load_index, prefab)
	self.complete_index = self.complete_index + 1

	if nil ~= self.loaded_callback then
		self.loaded_callback(self.complete_index / self.total_count)
	end

	if self.complete_index >= self.total_count then
		self.complete = true
		self.loaded_callback = nil
	end
end
