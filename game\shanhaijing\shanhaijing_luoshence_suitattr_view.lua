ShanHaiJingLuoShenCeView = ShanHaiJingLuoShenCeView or BaseClass(SafeBaseView)

function ShanHaiJingLuoShenCeView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/shj_ui_prefab", "layout_luoshence_suitattr_view")
end

function ShanHaiJingLuoShenCeView:SetData(type, drawings_id)
	self.type = type
	self.drawings_id = drawings_id
end

function ShanHaiJingLuoShenCeView:ReleaseCallBack()
	self.type = nil
	self.drawings_id = nil

	if self.attr_list then
		for k, v in pairs(self.attr_list) do
			v:DeleteMe()
		end

		self.attr_list = nil
	end
end

function ShanHaiJingLuoShenCeView:LoadCallBack()
	if not self.attr_list then
		self.attr_list = {}

		for i = 1, 3 do
			self.attr_list[i] = ShanHaiJingSuitItemRender.New(self.node_list["attr_" .. i])
		end
	end
end

function ShanHaiJingLuoShenCeView:OnFlush()
	if nil == self.type or nil == self.drawings_id then
		return
	end

	local cfg = ShanHaiJingLSCWGData.Instance:GetLuoShenCeCardGroupCellCfg(self.type, self.drawings_id)
	if not IsEmptyTable(cfg) then
		self.node_list.title.text.text = ShanHaiJingLSCWGData.Instance:GetLuoShenCeSuitAttrTitle(self.type, self.drawings_id)

		local data_list = ShanHaiJingLSCWGData.Instance:GetLuoShenCeSuitAttrDataList(self.type, self.drawings_id)
		for i = 1, 3 do
			self.attr_list[i]:SetData(data_list[i])
		end
	end
end

-----------------------------ShanHaiJingSuitItemRender--------------------------------
ShanHaiJingSuitItemRender = ShanHaiJingSuitItemRender or BaseClass(BaseRender)

function ShanHaiJingSuitItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local suit_num = self.data.suit_num
	local active_num = self.data.active_num
	local name_str =  NumberToChinaNumber(suit_num)
	local active = active_num >= suit_num
	local color = active and COLOR3B.D_GREEN or COLOR3B.D_RED
	local num = active_num > suit_num and suit_num or active_num
	self.node_list.now_level.text.text = string.format(Language.LuoShenCe.SuitCellName, name_str, color, num, suit_num)

	local data_list = {}
	for i = 1, 2 do
		local attr_id = self.data.cfg["attr_id" .. i]
		local attr_value = self.data.cfg["attr_value" .. i]

		if attr_id > 0 and attr_value > 0 then
			local data = {}
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
			data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str)
			data.attr_value = AttributeMgr.PerAttrValue(attr_str, attr_value)
			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			table.insert(data_list, data)
		end
	end

	if not IsEmptyTable(data_list) then
		table.sort(data_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	if self.data.cfg.attr_per_name ~= "" and self.data.cfg.attr_per > 0 then
		local data = {}
		data.attr_name = self.data.cfg.attr_per_name
		data.attr_value = string.format("%s%%", self.data.cfg.attr_per / 100)
		table.insert(data_list, data)
	end
	
	for i = 1, 3 do
		XUI.SetGraphicGrey(self.node_list["rich_cur_" .. i], not active)
		self.node_list["rich_cur_name" .. i].text.text = (data_list[i] or {}).attr_name or ""
		self.node_list["rich_cur_value" .. i].text.text = (data_list[i] or {}).attr_value or ""
	end
end