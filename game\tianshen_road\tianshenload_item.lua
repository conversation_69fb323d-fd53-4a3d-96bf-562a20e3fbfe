TianShenRoadRewardState = 
{
	BKL = 0,
	KLQ = 1,
	YLQ = 2,
}

TianShenLoadDayItem = TianShenLoadDayItem or BaseClass(BaseRender)
function TianShenLoadDayItem:__init(instance)
	-- self.is_use_objpool = false
	if nil == self.root_node then
		local bundle, asset = ResPath.GetWidgets("day_item")
        local u3dobj = U3DObject(ResPoolMgr:TryGetGameObject(bundle, asset))
        if self.instance_parent then
            u3dobj.transform:SetParent(self.instance_parent)
        end

		self:SetInstance(u3dobj)
		self.is_use_objpool = true
	end

	if instance then
		self:SetInstanceParent(instance)
	end

	self.num_str = {"一","二","三","四","五","六","七","八","九","十","十一","十二"}
end

function TianShenLoadDayItem:__delete()
	if self.is_use_objpool and not self:IsNil() then
		self.btn_yilingqu = nil
		self.select_bg = nil
		self.day_num = nil
		self.btn_item = nil
		self.cur_day_num = nil
		self.vip_tip = nil
		ResPoolMgr:Release(self.view.gameObject)
	end
end

function TianShenLoadDayItem:SetData(data,is_select)
	BaseRender.SetData(self, data)
	self.btn_yilingqu = self.node_list.btn_yilingqu
	self.select_bg = self.node_list.select_bg
	self.day_num = self.node_list.day_num
	self.btn_item = self.node_list.btn_item
	self.vip_tip = self.node_list.vip_tip
	self.cur_day_num = self.node_list.cur_day_num

	self.day_num.text.text = string.format(Language.TianShenRoad.LoginStr1, self.num_str[data.index])
	self.cur_day_num.text.text = string.format(Language.TianShenRoad.LoginStr1, self.num_str[data.index])

	local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
	if data.vip_level > 0 then
		self.btn_yilingqu:SetActive(data.common_gift_state == TianShenRoadRewardState.YLQ and data.special_gift_state == TianShenRoadRewardState.YLQ)
		self.vip_tip:SetActive(data.common_gift_state == TianShenRoadRewardState.YLQ and cur_vip_level < data.vip_level)
		self.vip_tip.text.text = string.format(Language.TianShenRoad.LoginStr2, data.vip_level)
	else
		self.btn_yilingqu:SetActive(data.common_gift_state == TianShenRoadRewardState.YLQ)
	end

	self.btn_item.button:AddClickListener(BindTool.Bind1(self.OnBtnItemClickHnadler, self))

	if is_select then
		self.select_bg:SetActive(true)
		self.btn_item.transform.localScale = Vector3(1.2,1.2,1.2)
		TianshenRoadWGData.Instance:SetSelectDayItem(self.btn_item, self.select_bg)
	else
		self.btn_item.transform.localScale = Vector3(1,1,1)
		self.select_bg:SetActive(false)
	end

	local cur_login_day = TianshenRoadWGData.Instance:GetLoginDyaIndex()
	self.day_num:SetActive(cur_login_day ~= data.index)
	self.cur_day_num:SetActive(cur_login_day == data.index)
	self.node_list.cur_reward_bg:SetActive((data.special_gift_state == TianShenRoadRewardState.KLQ or data.common_gift_state == TianShenRoadRewardState.KLQ))
end

function TianShenLoadDayItem:SetSelectBg(is_select)
	if is_select then
		self.select_bg:SetActive(true)
		self.btn_item.transform.localScale = Vector3(1.2,1.2,1.2)
		TianshenRoadWGData.Instance:SetSelectDayItem(self.btn_item, self.select_bg)
	else
		self.btn_item.transform.localScale = Vector3(1,1,1)
		self.select_bg:SetActive(false)
	end
end

function TianShenLoadDayItem:OnBtnItemClickHnadler(sender)
	local old_item, old_select_bg = TianshenRoadWGData.Instance:GetDyaItemSelect()
	if old_item ~= nil and old_select_bg ~= nil then
		old_select_bg:SetActive(false)
		old_item.transform.localScale = Vector3(1,1,1)
	end

	self.select_bg:SetActive(true)
	self.btn_item.transform.localScale = Vector3(1.2,1.2,1.2)
	TianshenRoadWGData.Instance:SetSelectDayItem(self.btn_item, self.select_bg)
	TianshenRoadWGCtrl.Instance:ShowLoginDayReward(self.data.index)
end


TianShenRoadLoginDayRender = TianShenRoadLoginDayRender or BaseClass(BaseRender)
function TianShenRoadLoginDayRender:__init()
	
end

function TianShenRoadLoginDayRender:LoadCallBack()
	


end

function TianShenRoadLoginDayRender:__delete()
	self.btn_yilingqu = nil
	self.select_bg = nil
	self.day_num = nil
end

function TianShenRoadLoginDayRender:OnFlush()
	if not self.data then
		return
	end
	self.day_num.text.text = string.format(Language.TianShenRoad.LoginStr1, self.num_str[self.data.index])
	self.btn_yilingqu:SetActive(self.data.state > 0)
end

function TianShenRoadLoginDayRender:OnSelectChange(is_select)
	if nil ~= self.select_bg then
		self.select_bg:SetActive(is_select)
	end
end


TianShenRoadCommonItemRender = TianShenRoadCommonItemRender or BaseClass(BaseRender)
function TianShenRoadCommonItemRender:__init()
	
end

function TianShenRoadCommonItemRender:LoadCallBack()
	self.cell = nil
end

function TianShenRoadCommonItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function TianShenRoadCommonItemRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function TianShenRoadCommonItemRender:OnFlush()
	if not self.data then
		return
	end 
	if self.cell == nil then
		self.cell = ItemCell.New(self.node_list["pivot"])
	end
	
	self.cell:SetData(self.data)
end

---------------------------------------------------- 拼图 ---------------------------------------------
TianShenLoadBXItem = TianShenLoadBXItem or BaseClass(BaseRender)

function TianShenLoadBXItem:__init(instance)
	local bundle, asset = "uis/view/tianshenroad_ui_prefab", "bx_list_item"
	self:LoadAsset(bundle, asset, instance.transform)

	self.is_empty = true
end

function TianShenLoadBXItem:LoadCallBack()
end

function TianShenLoadBXItem:OnFlush()
	local data = self:GetData()
	if not data or IsEmptyTable(self.node_list) then
		return
	end

	-- 引导用
	if not self.set_name then
		self.set_name = true
		self.view.gameObject.name = string.format("%s_%s", self.view.gameObject.name, data.index + 1)
	end
	-- self.node_list.score_label.text.text = data.id

	-- 拼图块命名规则：第一套的第一块碎片命名格式为：a3_ptlj_pt1_01;第二块命名为：a3_ptlj_pt1_02
	self.is_empty = data.id == -1
	self:CustomSetActive(not self.is_empty)
	if not self.is_empty then
		local res = string.format("a3_ptlj_pt1_%02d", data.id + 1)
		self.node_list.baoxaing_open.image:LoadSprite(ResPath.GetTianShenRoadImg(res))
	
		local is_right = true
		if data.index then
			is_right = TianshenRoadWGData.Instance:IsRightJigsaw(data.index + 1)
		end
		XUI.SetGraphicGrey(self.node_list.baoxaing_open, not is_right)
	end
end

function TianShenLoadBXItem:SetSelect(status)
	if self.node_list.select then
		local data = self:GetData()
		local is_right = data.index == data.id
		self.node_list.select:CustomSetActive(not is_right and status)
	end
end

function TianShenLoadBXItem:CustomSetActive(status)
	status = not self:IsEmpty() and status
	if self.node_list.show_node then
		self.node_list.show_node:CustomSetActive(status)
	end
end

function TianShenLoadBXItem:ShowEffect()
	if self.node_list.effect then
		-- print_error("TODO: ",self:GetData(), " 播放激活特效")
		self.node_list.effect:CustomSetActive(true)
		local data = self:GetData()
		ReDelayCall(self, function()
			self.node_list.effect:CustomSetActive(false)
		end, 1, "TianShenLoadBXItem" .. data.index)
	end
end

function TianShenLoadBXItem:SetEmpty()
	self.is_empty = true
end

function TianShenLoadBXItem:IsEmpty()
	return self.is_empty
end

-------------------------------------- 进度奖励 ---------------------------------------------
TianShenLoadJigsawAwardItem = TianShenLoadJigsawAwardItem or BaseClass(BaseRender)

function TianShenLoadJigsawAwardItem:__init(instance)
	local bundle, asset = "uis/view/tianshenroad_ui_prefab", "jigsaw_award_item"
	self:LoadAsset(bundle, asset, instance.transform)
end

function TianShenLoadJigsawAwardItem:ReleaseCallBack()
	-- body
end

function TianShenLoadJigsawAwardItem:LoadCallBack()
	if IsEmptyTable(self.node_list) then
		return
	end
	XUI.AddClickEventListener(self.node_list.award, BindTool.Bind(self.OnClick, self))
end

function TianShenLoadJigsawAwardItem:OnFlush()
	local data = self:GetData()
	if not data or IsEmptyTable(self.node_list) then
		return
	end
	self.node_list.target_val.text.text = data.jigsaw_num
	-- 0 未完成 1 已完成 2 已领取  BKL = 0,KLQ = 1,YLQ = 2,
	local cur_state = TianshenRoadWGData.Instance:GetBXState(data.ID)
	self.node_list.red_point:SetActive(cur_state == ActivityRewardState.KLQ)
	self.node_list.icon_dark:SetActive(cur_state == ActivityRewardState.YLQ)
	self.node_list.icon_light:SetActive(cur_state ~= ActivityRewardState.YLQ)
	self.node_list.got:SetActive(cur_state == ActivityRewardState.YLQ)
end

function TianShenLoadJigsawAwardItem:OnClick()
	local state = TianshenRoadWGData.Instance:GetBXState(self.data.ID)
	if state == TianShenRoadRewardState.KLQ then
		TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_WANT_SHENQI,WOYAOSHENQI_OP_TYPE.DANGWEI_REWARD, self.data.ID)
	else
		-- TianshenRoadWGCtrl.Instance:OpenShenQiRewardTips()
		self:ShowReward()
	end
end


function TianShenLoadJigsawAwardItem:ShowReward()
	local data_list = TianshenRoadWGData.Instance:GetSQRewardList()
	if not data_list then
		return
	end

	local data =
	{
		view_type = RewardShowViewType.Title,
		title_reward_item_data = data_list
	}
	RewardShowViewWGCtrl.Instance:SetRewardShowData(data)
end