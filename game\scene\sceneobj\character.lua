Character = Character or BaseClass(SceneObj)
local Time = UnityEngine.Time
local Action_Time = 0.2
local TypeUnityTexture = typeof(UnityEngine.Texture)

-- 上升过程百分比
STRIKE_FLY_UP_PRO = 0.2
-- 下降过程百分比
STRIKE_FLY_DOWN_PRO = 0.5
-- 浮空过程百分比
STRIKE_FLY_KEEP_PRO = 0.3
-- 开始触发角度变化时间
STRIKE_ROTATIE_BEGIN_TIME = 0.1
-- 触发抖动的时间，必须要浮空时间比这个大，才会抖动
STRIKE_SHAKE_TIME = 0.3
-- 击飞角度变化
STRIKE_ROTATIE = 20
-- 击飞过程最大高度
STRIKE_FLY = 4.5
-- 浮空高度
STRIKE_SHAKE_HIGH = 0.1

local ZeroPos = Vector3(0, 0, 0)

function Character:__init()
    self.followui_class = CharacterFollow
    self.show_hp = 0-- 表现hp

    -- 攻击相关
    self.attack_skill_id = 0
    self.next_skill_id = 0
    self.attack_target_pos_x = 0
    self.attack_target_pos_y = 0
    self.attack_target_obj = nil
    self.attack_use_type = ATTACK_USE_TYPE.GUAJI
    self.attack_skill_type = ATTACK_SKILL_TYPE.NONE

    -- 受击相关
    self.hit_skill_id = 0

    self.attack_is_playing = false-- 攻击动作是否在播放中
    self.attack_is_playing_invalid_time = 0 -- 一段时间后attack_is_playing恢复为false

    self.fight_state_end_time = 0-- 战斗状态结束时间
    self.fight_by_role_end_time = 0-- 由与人物战斗状态结束时间
    self.fight_continue_time = COMMON_CONSTS.FIGHT_STATE_TIME
    self.floating_texts = {}-- 飘字队列

    -- Move状态相关变量
    self.server_pre_pos = u3d.vec2(0, 0) -- 记录服务器上一个坐标
    self.move_end_pos = u3d.vec2(0, 0)
    self.move_dir = u3d.vec2(0, 0)-- 移动方向(单位向量)
    self.move_total_distance = 0.0-- 移动总距离
    self.move_pass_distance = 0.0-- 移动距离
    self.is_special_move = false-- 是否特殊移动
    self.special_speed = 0-- 特殊移动附加速度
    self.delay_end_move_time = 0-- 延迟结束移动状态(防止摇杆贴边行走时快速切换移动、站立)

    self.flying_process = 0 -- 飞行过程（1,上升 2,最高处 3,下降）

    self.rotate_to_angle = nil -- 旋转到指定角度
    self.anim_name = ""-- 当前的动作
    self.attack_index = 1-- 当前攻击序列
    self.animator_handle_t = {}

    self.buff_effect_disabled = false

    self.select_effect = nil
    self.select_effect_gameobj = nil
    self.buff_effect_list = {}
    self.buff_list = {}
    self.other_effect_list = {}
    self.buff_type_list = {}
    self._moveList = nil

    self.last_bink_time = 0
    self.old_show_hp = nil
    self.last_hit_audio_time = 0

    self.last_attacker_pos_x = 0
    self.last_attacker_pos_y = 0

    --发起攻击者
    self.deliverer = nil
    -- 每个点移动完成 防止C#走完，lua还在走
    self.is_move_over_pos = false

    self.is_enter_state_dead = false

    self.move_call_back = BindTool.Bind(self.MoveCallback, self)

    self.state_machine = StateMachine.New(self)

    self.state_machine:SetIsMainRole(self:IsMainRole())
    --Stand
    self.state_machine:SetStateFunc(SceneObjState.Stand, self.EnterStateStand, self.UpdateStateStand, self.QuitStateStand)
    --Move
    self.state_machine:SetStateFunc(SceneObjState.Move, self.EnterStateMove, self.UpdateStateMove, self.QuitStateMove)
    --Attack
    self.state_machine:SetStateFunc(SceneObjState.Atk, self.EnterStateAttack, self.UpdateStateAttack, self.QuitStateAttack)
    --Dead
    self.state_machine:SetStateFunc(SceneObjState.Dead, self.EnterStateDead, self.UpdateStateDead, self.QuitStateDead)
    --Hurt
    self.state_machine:SetStateFunc(SceneObjState.Hurt, self.EnterStateHurt, self.UpdateStateHurt, self.QuitStateHurt)
    -- 互动
    self.state_machine:SetStateFunc(SceneObjState.Interactive, self.EnterStateInteractive, self.UpdateStateInteractive, self.QuitStateInteractive)

    self:ClearActionData()

    -- 是否处于跟随状态
    self.is_follow_state = false
    self.sever_follow_state = false
    self.obj_follow_type = nil
    -- 跟随自己移动的对象列表
    self.follow_me_obj_list = {}
    self.follow_target = nil
    self.follow_move_dir = u3d.vec2(0, 0)

    self.move_reason = nil
    self.is_vip_boat = false

    self.obj_special_state = OBJ_SPECIAL_STATE.NONE
    self.special_state_end_time = nil
    self.special_state_fly_param = nil
    self.special_state_collide_param = nil
    self.collide_rotating_state = false
    self.is_set_rotating_call = false

    self.is_fighting = false

    self.reset_show_type = RESET_SPECIAL_TYPE.NORMAL
    self.reset_dis = 0
    self.reset_dir = 0
    self.reset_pass_time = 0
    self.reset_end_time = 0
    self.reset_step = 14
    self.reset_pos_skill = nil

    self.is_imeprial_city_appe_changging = false
    self.wait_load_buff_list = {}
    self.hit_show_cache = nil
    self.hit_show_timer = nil
    self.is_aim_select = false
    self.is_use_clash_time = false

    self.line_effect_cache = {}
    self.save_fear_pos = nil
end

function Character:__delete()
    if self.state_machine then
        self.state_machine:DeleteMe()
        self.state_machine = nil
    end

    for _, v in pairs(self.animator_handle_t) do
        v:Dispose()
    end
    self.animator_handle_t = {}

    if nil ~= self.select_effect then
    self.select_effect:DeleteMe()
    self.select_effect = nil
    end
    self.select_effect_gameobj = nil

    for k, v in pairs(self.buff_effect_list) do
        v:Destroy()
        v:DeleteMe()
    end
    self.buff_effect_list = {}
    self:RemoveDelayTime()
    GlobalTimerQuest:CancelQuest(self.dead_timer)
    GlobalTimerQuest:CancelQuest(self.say_end_timer)
    GlobalTimerQuest:CancelQuest(self.dizzy2_delay_timer)
    self.dizzy2_delay_timer = nil
    self.uicamera = nil
    self.attack_target_obj = nil
    self.buff_list = {}
    self.buff_type_list = {}
    self.floating_texts = nil
    self.deliverer = nil
    self._moveList = nil

    if self.buff_shield_handle then
        ReuseableHandleManager.Instance:ReleaseShieldHandle(self.buff_shield_handle)
        self.buff_shield_handle = nil
    end

    -- 清除跟随数据
    self:ResetFollowList()

    self.obj_special_state = OBJ_SPECIAL_STATE.NONE
    self.special_state_end_time = nil
    self.special_state_fly_param = nil
    self.special_state_collide_param = nil
    self.is_imeprial_city_appe_changging = false
    self.wait_load_buff_list = {}

    self.hit_show_cache = nil
    if self.hit_show_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.hit_show_timer)
        self.hit_show_timer = nil
    end

    self.is_aim_select = false

    self:ClearAllLineEffect()

    if self.play_effect_timer then
        GlobalTimerQuest:CancelQuest(self.play_effect_timer)
        self.play_effect_timer = nil
    end

    if self.play_longzhu_skill_effect_timer then
        GlobalTimerQuest:CancelQuest(self.play_longzhu_skill_effect_timer)
        self.play_longzhu_skill_effect_timer = nil
    end

    self:ClearLongZhuSkillLoader()

    if self.spectial_effect_delay ~= nil then
        GlobalTimerQuest:CancelQuest(self.spectial_effect_delay)
        self.spectial_effect_delay = nil
    end
end

function Character:ClearActionData()
    --攻击动作相关变量
    self.has_action_hit = false
    self.has_action_end = false
    self.cur_action_time = 0
    self.action_time_record = nil
    self.can_cut_down_time = 0
    self.has_do_cut_down = false
end

function Character:InitInfo()
    SceneObj.InitInfo(self)
    self.show_hp = self.vo.hp

    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    local is_main_role = self:IsMainRole()
    main_part:SetMainRole(is_main_role)
    -- main_part:ListenEvent(
    -- "jump/start", BindTool.Bind(self.OnJumpStart, self))
    -- main_part:ListenEvent(
    -- "jump/end", BindTool.Bind(self.OnJumpEnd, self))

    self.buff_shield_handle = ReuseableHandleManager.Instance:GetShieldHandle(BuffEffectShieldHandle, self)
    self.buff_shield_handle:SetQualityLevelOffset(self:IsMainRole() and 3 or 0)
    self.buff_shield_handle:CreateShieldHandle()
end

local reset_pos_time = 0
local mathceil = math.ceil
local mathfloor = math.floor
function Character:Update(now_time, elapse_time)
    SceneObj.Update(self, now_time, elapse_time)

    if not self:IsDead() then
        if self:IsMainRole() then
            self:UpdateMove(now_time, elapse_time)
        end

        if not self:IsRole() then
            self:UpdateFollowMove(true)
        end
    end

    self.state_machine:UpdateState(elapse_time)

    if self.fight_state_end_time > 0 and now_time >= self.fight_state_end_time and (not self:IgnoreFightContinueTime()) then
        self:LeaveFightState()
    end

    -- 因为某种未知原因，导致主角位移到障碍区，这里加个容错
    if self.reset_x and self.reset_y and self:IsMainRole() then
        if AStarFindWay:IsBlock(self.reset_x, self.reset_y, true) then
            -- local scene_id = Scene.Instance:GetSceneId()
            self:ResetSkillResetInfo()
        end
    end

    reset_pos_time = reset_pos_time + Time.deltaTime
    if self.reset_show_type == nil or self.reset_show_type == RESET_SPECIAL_TYPE.NORMAL then
        if self.reset_x and self.reset_y and reset_pos_time > 0.05 and not self.is_special_move then
            reset_pos_time = 0
            if self.logic_pos.x == self.reset_x and self.logic_pos.y == self.reset_y then
                self:ResetSkillResetInfo()
            else
                local reset_x, reset_y = 0, 0
                if self.logic_pos.x ~= self.reset_x then
                    reset_x = (self.reset_x - self.logic_pos.x) / 2
                    reset_x = reset_x > 0 and mathceil(reset_x) or mathfloor(reset_x)
                end
                if self.logic_pos.y ~= self.reset_y then
                    reset_y = (self.reset_y - self.logic_pos.y) / 2
                    reset_y = reset_y > 0 and mathceil(reset_y) or mathfloor(reset_y)
                end
                self:SetLogicPos(self.logic_pos.x + reset_x, self.logic_pos.y + reset_y)
            end
        end
    elseif self.reset_show_type ~= nil and self.reset_show_type == RESET_SPECIAL_TYPE.SPEED then
        if self.reset_x and self.reset_y and not self.is_special_move then
            if (self.logic_pos.x == self.reset_x and self.logic_pos.y == self.reset_y) or self.reset_pass_time <= 0 or self.reset_dis <= 0 then
                self:ResetSkillResetInfo(true)
            else
                self:UpdateResetPosBySpeed(now_time, elapse_time)
            end
        end
    end

    if self.attack_is_playing_invalid_time > 0 and now_time >= self.attack_is_playing_invalid_time then
        self.attack_is_playing_invalid_time = 0
        self.attack_is_playing = false
    end

    if ClientCmdWGCtrl.Instance.is_show_pos and self:GetFollowUi() then
        local name_str = string.format(self.vo.name .. "( %d , %d ) %d", self.logic_pos.x, self.logic_pos.y, self.vo.obj_id)
        self:GetFollowUi():SetName(name_str)
    end

    for k, v in pairs(self.other_effect_list) do
        if v.time < now_time then
            v.eff:Destroy()
            v.eff:DeleteMe()
            self.other_effect_list[k] = nil
        end
    end

    self:ShowFloatingText()
    self:UpdateSpecialState(now_time, elapse_time)

    -- 连线更新
    for k,v in pairs(self.line_effect_cache) do
        self:CheckUpdateLineEffectPos(k)
    end

    -- 容错：强制更新击退状态
    if self.force_fix_is_repelmoving_time and now_time > self.force_fix_is_repelmoving_time then
        self.force_fix_is_repelmoving_time = nil
        if self:IsRepelMoving() then
            self.is_repelmoving = false
        end
    end
end

-- 信息
Character.cbdata_list = {}
function Character.GetCBData()
    local cbdata = table.remove(Character.cbdata_list)
    if nil == cbdata then
        cbdata = {true, true, true, true, true, true, true}
    end

    return cbdata
end

function Character.ReleaseCBData(cbdata)
    cbdata[1] = true
    cbdata[2] = true
    cbdata[3] = true
    cbdata[4] = true
    cbdata[5] = true
    cbdata[6] = true
    cbdata[7] = true
    cbdata[8] = true

    table.insert(Character.cbdata_list, cbdata)
end

function Character:IsCharacter()
    return true
end

-- 行为发出者
function Character:Deliverer(deliverer)
    if nil ~= deliverer then
        self.deliverer = deliverer
    end

    return self.deliverer
end

-- 机器人标记 设置
function Character:SetRobot(value)
    self.is_robot = value
end

-- 机器人标记 是否机器人
function Character:IsRobot()
    return self.is_robot == true
end

-- 是否翅膀飞行
function Character:IsWingFly()
    return false
end

-- 是否系统变身（天神）
function Character:IsTianShenAppearance()
    if self.vo and self.vo.special_appearance then
        return SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == self.vo.special_appearance
    end

    return false
end

-- 是否高达
function Character:IsGundam()
    if self.vo and self.vo.special_appearance then
        return SPECIAL_APPEARANCE_TYPE.GUNDAM == self.vo.special_appearance
    end

    return false
end

-- 是否是怒气变身
function Character:IsXiuWeiBianShen()
    if self.vo and self.vo.special_appearance then
        return SPECIAL_APPEARANCE_TYPE.XIUWEIBIANSHEN == self.vo.special_appearance
    end

    return false
end

-- 是否游泳中
function Character:GetIsSwimming()
    local scene_type = Scene.Instance:GetSceneType()
    return (scene_type == SceneType.KF_HotSpring or scene_type == SceneType.KF_DUCK_RACE) and self:IsWaterWay()
end

-- 是否在温泉
function Character:GetIsInSpring(scene_type)
    scene_type = scene_type or Scene.Instance:GetSceneType()
    return scene_type == SceneType.KF_HotSpring or scene_type == SceneType.KF_DUCK_RACE
end

-- 是否在护送中
function Character:GetIsInHuSong()
    return YunbiaoWGData.Instance:GetIsHuShong()
end

-- 获得天神五行类型
function Character:GetWuXingType()
    if self:IsRole() then
        if self.vo and self.vo.special_appearance and self:IsTianShenAppearance() then
            return TianShenWGData.Instance:GetTianShenWuXingByImageId(self.vo.appearance_param)
        end
        return 0
    end

    return 0
end

function Character:OnClicked(param)
    if BossCamera.Instance:BossFollowCaneraShowStatus() then
        return 
    end

	if not self.is_can_click then return end
	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove() then
		return
	end

    if not self:IsDeleted() and not self:IsDead() then
        GlobalEventSystem:Fire(ObjectEventType.ON_CLICKED, self)
        GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, self, "scene")
    end
end

-- 点击
function Character:OnClick()
    SceneObj.OnClick(self)
    if self:IsDeleted() then
        return
    end

    if nil == self.select_effect then
        self.select_effect = AllocAsyncLoader(self, "select_effect")
        self.select_effect:SetIsUseObjPool(true)
        self.select_effect:SetParent(self.draw_obj:GetRoot().transform)
        local bundle, asset = ResPath.GetEnvironmentCommonEffect("eff_xuanzhong")
        self.select_effect:Load(bundle, asset, function(gameobj)
            gameobj.transform.localScale = Vector3(1, 1, 1)
            self.select_effect_gameobj = gameobj
            self:ChangeXuanZhong()
        end)
    end

    self.select_effect:SetActive(true)
end

-- 取消选择
function Character:CancelSelect()
    SceneObj.CancelSelect(self)
    if nil ~= self.select_effect then
        self.select_effect:SetActive(false)
    end

    if self.is_aim_select then
        self.is_aim_select = false
        local follow_ui = self:GetUnderFollowUi()
        if follow_ui ~= nil then
            follow_ui:ShowAimEffect(false)
        end
    end
end

-- 选中特效
function Character:ChangeXuanZhong()
    if not IsNil(self.select_effect_gameobj) then
        self.select_effect_gameobj.transform.localScale = Vector3.one
        self.select_effect_gameobj.transform.localPosition = self:GetXuanZhongUIPos()
    end
end

-- 可见性 改变
function Character:VisibleChanged(visible)
    SceneObj.VisibleChanged(self, visible)
    if not visible then
        self:ClearAllLineEffect()
    end
end

-- 设置方向 by xy
function Character:SetDirectionByXY(x, y, is_comefrom_joystick, is_force, speed)
    if (self.dic and self.dic.ignoreCollision == 1) and not is_force then
        return
    end

    SceneObj.SetDirectionByXY(self, x, y, speed)
end

-- 旋转
function Character:RotateTo(rotate_to_angle)
    self.rotate_to_angle = rotate_to_angle
    self:GetDrawObj():Rotate(0, rotate_to_angle, 0)
end

-- 行为
function Character:ChangeState(state)
    self:TryClearInteractiveEffectsAndSounds()
    self.state_machine:ChangeState(state)
end

function Character:IsStand()
    return self.state_machine and self.state_machine:IsInState(SceneObjState.Stand)
end

-- 移动
function Character:IsMove()
    return self.state_machine and self.state_machine:IsInState(SceneObjState.Move)
end

function Character:IsAtk()
    return self.state_machine and self.state_machine:IsInState(SceneObjState.Atk)
end

function Character:IsHurt()
    return self.state_machine and self.state_machine:IsInState(SceneObjState.Hurt)
end

function Character:IsInteractive()
    return self.state_machine and self.state_machine:IsInState(SceneObjState.Interactive)
end

function Character:ReEnterState()
    self.state_machine:ReEnterState()
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    站立     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 站立 do
function Character:DoStand()
    self.move_reason = nil
    if self:IsDeleted() or self:IsStand() or self:IsClashState() then
        return
    end

    self:SetIsMoving(false)
    self:ChangeState(SceneObjState.Stand)
end

-- 站立 进入状态
function Character:EnterStateStand()
    self.is_move_over_pos = false

    if self.is_gather_state then
        self:CrossToCaiji()
        return
    end

    local scene_type = Scene.Instance:GetSceneType()
    if self:IsMarryObj() then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        local main_part_obj = part:GetObj()
        if main_part_obj then
            local children = main_part_obj.gameObject:GetComponentsInChildren(typeof(UnityEngine.Animator))
            for i = 0, children.Length - 1 do
                children[i]:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)
            end
        end
        return
    end

    if scene_type == SceneType.HotSpring or scene_type == SceneType.KF_HotSpring or scene_type == SceneType.KF_DUCK_RACE then
        self:SetHotSpringStand()
        return
    end

    if self.vo.hold_beauty_npcid and self.vo.hold_beauty_npcid > 0 then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Hug)
        local holdbeauty_part = self.draw_obj:GetPart(SceneObjPart.HoldBeauty)
        if holdbeauty_part then
            holdbeauty_part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Hug)
        end
        return
    end

    if self:IsRiding() then
        self:CrossAction(SceneObjPart.Main, self:SetRidingActionIdelParam())
        if not self:IsLockMountAnimInIdle() then
            self:CrossAction(SceneObjPart.Mount, SceneObjAnimator.Idle)
            -- self:CrossAction(SceneObjPart.FightMount, SceneObjAnimator.Idle)
        end
        return
    end

    if self:IsMitsurugi() then
        return
    end

    self:CrossAction(SceneObjPart.Main, self:CurIdleAni())
    if self:IsRole() then
        local mantle_part = self.draw_obj:GetPart(SceneObjPart.Mantle)
        if mantle_part then
            mantle_part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)
        end
    end
end

-- 站立 更新状态
function Character:UpdateStateStand(elapse_time)
    if self.is_special_move then
        self:SpecialMoveUpdate(elapse_time)
    end
end

-- 站立 退出状态
function Character:QuitStateStand()
end

-- 站立
function Character:FlushStande()
    if self:IsStand() then
        if self:IsMitsurugi() then
            return
        end

        self:CrossAction(SceneObjPart.Main, self:CurIdleAni())
    end
end

-- 变为正常状态
function Character:ChangeToCommonState(is_init)
    if (self.show_hp and self.show_hp > 0) or (self.vo and self.vo.hp > 0) then
        self:DoStand()
    else
        if not self:IsDead() then
            if self:IsMainRole() then
                if self.dead_timer == nil then
                    self.dead_timer = GlobalTimerQuest:AddDelayTimer(function ()
                        if self.show_hp <= 0 and not self:IsDead() then
                            self:DoDead()
                        end
                        self.dead_timer = nil
                    end, 0.1)
                end
            else
                self:DoDead()
            end
        end
    end
end

-- 采集 
function Character:CrossToCaiji(time)
    local scene_type = Scene.Instance:GetSceneType()
    local anim = nil
    if SceneType.HotSpring == scene_type or SceneType.KF_HotSpring == scene_type or scene_type == SceneType.KF_DUCK_RACE then
        if not self:IsWaterWay() then
            anim = SceneObjAnimator.CaiJi
        end
        --anim = SceneObjAnimator.CaiJi--在水里策划不要采集动作
    else
        if self.caiji_type == CaijiType.Stand then
            anim = SceneObjAnimator.CaiJi2
        elseif self.caiji_type == CaijiType.Fish then
            anim = SceneObjAnimator.Fish
        else
            anim = SceneObjAnimator.CaiJi
        end
    end

    if anim then
        self:CrossAction(SceneObjPart.Main, anim, nil, time)
    end
end

-- 打坐
function Character:GetIsInSit()
    return false
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    移动     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
function Character:ServerPrePos(pos_x, pos_y)
    -- body
    if not pos_x then
        return self.server_pre_pos
    end
    self.server_pre_pos.x = pos_x
    self.server_pre_pos.y = pos_y
end

function Character:DoUpdateMove(len, elapse_time)
    self.isSkillMove = true
    local curSpeed = self._moveList[1].speed
    local near_pos = self._moveList[1].near_pos
    for i,v in ipairs(self._moveList) do
        if Time.time < v.time then
            break
        end

        if i == len then
            self._moveList = nil
            --结束移动
            local main_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
            if main_obj then
                main_obj.transform.localPosition = u3dpool.vec3(0, 0, 0)
            end
            return true
        else
            curSpeed = self._moveList[i+1].speed
            near_pos = self._moveList[i+1].near_pos
            table.remove(self._moveList, i)
            break
        end
    end

    --更新位置
    local real_x, real_y
    if near_pos then
        real_x, real_y = near_pos.x, near_pos.z
    else
        local forward = Transform.GetForwardOnce(self.draw_obj.root_transform)
        local right = Transform.GetRightOnce(self.draw_obj.root_transform)
        local offset = u3dpool.v3Add(u3dpool.v3Mul(forward, curSpeed.z * elapse_time), u3dpool.v3Mul(right, curSpeed.x * elapse_time))
        real_x, real_y = self.draw_obj.curPos.x + offset.x, self.draw_obj.curPos.z + offset.z
    end

    local logic_x, logic_y = GameMapHelper.WorldToLogic(real_x, real_y)
    if AStarFindWay:IsBlock(logic_x, logic_y) then
        self._moveList = nil
        return false
    end

    if self:IsMainRole() then
        local limit_x, limit_y = self:GetMovePosByLimit(logic_x, logic_y)
        if limit_x ~= logic_x or limit_y ~= logic_y then
            self._moveList = nil
            return false
        end
    end

    self.draw_obj:SetPosition(real_x, real_y)
    self:SetRealPos(real_x, real_y)
    local main_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
    if main_obj then
        local localPosition = Transform.GetLocalPositionOnce(main_obj.transform)
        local up = Transform.GetUpOnce(main_obj.transform)
        local pos = u3dpool.v3Add(localPosition, u3dpool.v3Mul(up, curSpeed.y * elapse_time))
        if pos.y < 0 then
            pos.y = 0
        end
        main_obj.transform.localPosition = pos
    end

    return true
end

--更新位移
function Character:UpdateMove(now_time, elapse_time)
    local len = self._moveList and #self._moveList or 0
    if len == 0 then
        self.isSkillMove = false
        return false
    end

    return self:DoUpdateMove(len, elapse_time)
end

function Character:UpdateFollowMove(is_move, pos_x, pos_y)
    if not self:IsMove() and not self:IsRole() then
        return
    end

    pos_x = pos_x or self.logic_pos.x
    pos_y = pos_y or self.logic_pos.y

    if self.follow_me_obj_list ~= nil and next(self.follow_me_obj_list) ~= nil then
        local follow_list = {}
        for k,v in pairs(self.follow_me_obj_list) do
            if k:IsCharacter() and k ~= self then
                if not k:IsDeleted() and k:IsFollowState() then
                    local follow_obj_pos_x, follow_obj_pos_y= k:GetLogicPos()
                    local dis = GameMath.GetDistance(self.logic_pos.x, self.logic_pos.y, follow_obj_pos_x, follow_obj_pos_y, false)
                    if dis > v.follow_dis * v.follow_dis then
                        local follow_x = math.floor(pos_x - (self.move_dir.x * v.follow_dis))
                        local follow_y = math.floor(pos_y - (self.move_dir.y * v.follow_dis))
                        k:OnFollowCallBack(follow_x, follow_y)
                    end

                    follow_list[k] = v
                else
                    self:UnFollowMe(k)
                end
            end
        end

        self.follow_me_obj_list = follow_list
    end
end

-- 移动
--is_comefrom_joystick 是否由摇杆调用过来的（MainRole专用）
function Character:DoMove(pos_x, pos_y, move_reason, is_comefrom_joystick, height)
    -- print_error("玩家移动", pos_x, pos_y, move_reason, is_comefrom_joystick, height, self:GetMoveSpeed())
    if height == nil or height == 0 then
        height = self.draw_obj.root_transform.localPosition.y
    else
        height = height + self.draw_obj.root_transform.localPosition.y
    end

    if self:IsDead() and not self:IsGhost() then
        return
    end

    self.delay_end_move_time = 0
    self.is_special_move = false
    self.is_move_over_pos = false
    self.special_speed = 0
    self.move_reason = move_reason
    
    self:CalcMoveInfo(pos_x, pos_y, is_comefrom_joystick)
    if GLOBAL_CHECK_NO_MOVE_PRINT then
        if self:IsMainRole() then
            print_error("----draw_obj do MoveTo----", pos_x, pos_y, self.move_end_pos.x, self.move_end_pos.y, self:GetMoveSpeed(), height)
            local move_obj = self.draw_obj and self.draw_obj.root and self.draw_obj.root.move_obj
            print_error("----是否move_obj的特殊状态 是否在地上-----", move_obj and move_obj.IsOnGround)
            print_error("----是否move_obj的特殊状态 是否开启轻功-----", move_obj and move_obj.enableQingGong)
        end
    end
    self.draw_obj:MoveTo(self.move_end_pos.x, self.move_end_pos.y, self:GetMoveSpeed(), height)
    -- if self:IsRole() then
    --     self:UpdateMainRoleMoveSpeed()
    -- end
    
    if self:IsMainRole() then
        self.draw_obj:SetMoveCallback(self.move_call_back)
    end

    --如果当前不在移动状态则切换至移动状态
    if not self:IsMove() then
        self:ChangeState(SceneObjState.Move)
    end

    if self:IsRole() then
        self:UpdateFollowMove(true, pos_x, pos_y)
    end
end

-- 设置位置（角色）
function Character:SetLogicPos(pos_x, pos_y, pos_z)
    if pos_z == nil or pos_z == 0 then
        pos_z = self.draw_obj.root_transform.localPosition.y
    else
        pos_z = pos_z + self.draw_obj.root_transform.localPosition.y
    end

    SceneObj.SetLogicPos(self, pos_x, pos_y, pos_z)
end

-- 移动 获取移动原因
function Character:GetMoveReason()
    return self.move_reason
end

-- 击退
function Character:RepelMove(pos_x, pos_y)
    self.is_repelmoving = true
    self.force_fix_is_repelmoving_time = Status.NowTime + 2.5
    local x, y = GameMapHelper.LogicToWorld(pos_x, pos_y)
    self.draw_obj:SetMoveCallback(function ()
        self.is_repelmoving = false
        if self.draw_obj then
            self.draw_obj:SetMoveCallback(nil)
        end

        if self:IsRealDead() then
            return
        end

        self:SetLogicPos(pos_x, pos_y)
    end)

    self.draw_obj:MoveTo(x, y, self:GetMoveSpeed() * 4)
end

function Character:IsRepelMoving()
    return self.is_repelmoving
end

-- 移动 回调
function Character:MoveCallback(suc)
    -- body
    self.is_move_over_pos = suc == 1
end

function Character:SetIsMoveOverPos(bool)
    self.is_move_over_pos = bool
end

-- 移动 当移动时是否需要改变方向
function Character:IsNeedChangeDirOnDoMove(pos_x, pos_y)
    return true
end

-- 移动 进入状态
function Character:EnterStateMove()
    self.is_move_over_pos = false
    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    local mantle_part = self.draw_obj:GetPart(SceneObjPart.Mantle)
    local move_action = self:CurRunAni()
    if self:IsJump() or self:IsJumping() then
        return
    end

    -- 抱美人
    if self.vo.hold_beauty_npcid and self.vo.hold_beauty_npcid > 0 then
        part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.HugRun)
        local holdbeauty_part = self.draw_obj:GetPart(SceneObjPart.HoldBeauty)
        if holdbeauty_part then
            holdbeauty_part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.HugRun)
        end
        return
    end

    if self:IsMarryObj() then
        local main_part_obj = part:GetObj()
        if main_part_obj then
            local children = main_part_obj.gameObject:GetComponentsInChildren(typeof(UnityEngine.Animator))
            for i = 0, children.Length - 1 do
                children[i]:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Run)
            end
        end
        return
    end

    if self:IsMitsurugi() then
        return
    end

    self:CrossAction(SceneObjPart.Main, move_action)
    if self:IsRiding() then
        if not self:IsLockMountAnimInIdle() then
            self:CrossAction(SceneObjPart.Mount, SceneObjAnimator.Move)
            -- self:CrossAction(SceneObjPart.FightMount, move_action)
        end
    end

    if self:IsRole() and mantle_part then
        mantle_part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Run)
    end
end

-- 移动 更新状态
local temp_move_dir = u3d.vec3(0, 0, 0)
function Character:UpdateStateMove(elapse_time)
    if self.delay_end_move_time > 0 then
        if Status.NowTime >= self.delay_end_move_time then
            self.delay_end_move_time = 0
            self:ChangeToCommonState()
        end
        return
    end

    if self.draw_obj then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        if self.vo.hold_beauty_npcid and self.vo.hold_beauty_npcid > 0 then
            part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.HugRun)
            local holdbeauty_part = self.draw_obj:GetPart(SceneObjPart.HoldBeauty)
            if holdbeauty_part then
                holdbeauty_part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.HugRun)
            end
        elseif self:IsJump() then
        elseif self:IsJumping() then
        elseif self:IsMitsurugi() then
        elseif self:IsRiding() then
            self:CrossAction(SceneObjPart.Main, self:SetRidingActionRunParam())
            if not self:IsLockMountAnimInIdle() then
                self:CrossAction(SceneObjPart.Mount, SceneObjAnimator.Move)
                -- self:CrossAction(SceneObjPart.FightMount, SceneObjAnimator.Move)
            end
        elseif self:IsGhost() then
            self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Ghost_Run)
        else
            self:CrossAction(SceneObjPart.Main, self:CurRunAni())
        end

        self:ChangeHotSpringState()

        --移动状态更新
        local distance = elapse_time * self:GetMoveSpeed()
        self.move_pass_distance = self.move_pass_distance + distance

        if self.move_pass_distance >= self.move_total_distance or self.is_move_over_pos then
            self.is_move_over_pos = false
            self.is_special_move = false
            self.special_speed = 0
            self:SetRealPos(self.move_end_pos.x, self.move_end_pos.y)

            if self:MoveEnd() then
                self.move_pass_distance = 0
                self.move_total_distance = 0
                if self:IsMainRole() then
                    self.delay_end_move_time = Status.NowTime + 0.05
                elseif self:IsSpirit() then
                    self.delay_end_move_time = Status.NowTime + 0.02
                else
                    self.delay_end_move_time = Status.NowTime + 0.2
                end
            end
        else
            local mov_dir = u3dpool.v2Mul(self.move_dir, distance, temp_move_dir)
			self:SetRealPos(self.real_pos.x + mov_dir.x, self.real_pos.y + mov_dir.y)
        end
    end
end

-- 移动 退出状态
function Character:QuitStateMove(is_not_stop)
    self.move_reason = nil

    if not is_not_stop then
        self.draw_obj:StopMove()
    end

    if self:IsJump() then
        self:OnJumpEnd()
    end

    if self:IsRole() and not self:IsDead() then
        self:UpdateFollowMove(false)
    end
end

-- 是否正在骑乘
function Character:IsRiding()
    return false
end

function Character:GetCurRidingResId()
    return 0
end

-- 是否乘骑 但不是乘骑战斗坐骑
function Character:IsRidingNoFightMount()
    return false
end

function Character:IsRidingFightMount()
    return false
end

-- 设置坐姿（TM其实是Get）
-- 1 骑马动作 SceneObjAnimator.Mount_Idle  2 站立动作Idle  3 盘腿动作 SceneObjAnimator.Kun_Idle  4 摩托车动作 MoTuoIdle
function Character:SetRidingActionIdelParam()
    return SceneObjAnimator.Mount_Idle
end

-- 设置坐骑跑的时候 人物动作（TM其实是Get）
-- 1 骑马动作 SceneObjAnimator.Mount_Idle  2 站立动作Idle  3 盘腿动作 SceneObjAnimator.Kun_Idle  4 摩托车动作 MoTuoIdle
function Character:SetRidingActionRunParam()
    return SceneObjAnimator.Mount_Run
end

-- 移动 当有飞行器要搞骚操作的计时器移除
function Character:RemoveDelayTime()
    if self.timer_quest then
        GlobalTimerQuest:CancelQuest(self.timer_quest)
        self.timer_quest = nil
    end
end

-- 移动 结束
function Character:MoveEnd()
    return true
end

-- 移动剩余距离
function Character:GetMoveRemainDistance()
    if not self:IsMove() then
        return 0
    end

    return self.move_total_distance - self.move_pass_distance
end

-- 移动 根据目的地计算移动信息
function Character:CalcMoveInfo(pos_x, pos_y, is_comefrom_joystick)
    if self:IsNeedChangeDirOnDoMove(pos_x, pos_y) then
        self:SetDirectionByXY(pos_x, pos_y, is_comefrom_joystick)
    end

    self.move_end_pos.x, self.move_end_pos.y = GameMapHelper.LogicToWorld(pos_x, pos_y)
    -- print_error("CalcMoveInfo", self.move_end_pos.x, self.move_end_pos.y)
    local delta_pos = u3d.v2Sub(self.move_end_pos, self.real_pos)

    self.move_total_distance = u3d.v2Length(delta_pos)
    self:SetMoveDir(u3d.v2Normalize(delta_pos))
    self.move_pass_distance = 0.0
end

-- 移动 结束点
function Character:GetMoveEndPos()
    return self.move_end_pos
end

-- 移动 设置方向
function Character:SetMoveDir(move_dir)
    self.move_dir = move_dir
end

-- 特殊移动，冲锋、击退等
function Character:SpecialMoveUpdate(elapse_time)
    local distance = elapse_time * self:GetMoveSpeed()
    local mov_dir = u3d.v2Mul(self.move_dir, distance)
    local now_pos = u3d.v2Add(self.real_pos, mov_dir)
    self:SetRealPos(now_pos.x, now_pos.y)

    local delta_pos = u3d.v2Sub(self.move_end_pos, now_pos)
    local residue = u3d.v2Length(delta_pos)

    local cur_dir = u3d.v2Normalize(delta_pos)
    if residue <= 2 or (cur_dir.x * self.move_dir.x < 0 or cur_dir.y * self.move_dir.y < 0) then
        self:OnSpecialMoveEndHandle()
    end
end

-- 特殊移动
function Character:OnSpecialMoveEndHandle()
    -- print_error("OnSpecialMoveEndHandle.........")
    self.move_pass_distance = 0
    self.move_total_distance = 0
    self.is_special_move = false
    self.special_speed = 0
    self:SetRealPos(self.move_end_pos.x, self.move_end_pos.y)

    if nil ~= self.special_move_end_callback then
        self.special_move_end_callback(self)
    end

    self:OnSpecialMoveEnd()
end

---- 特殊移动 派生类重写
function Character:OnSpecialMoveEnd()
end

-- 特殊移动
function Character:SetStatusChongFeng()
    -- print_error("SetStatusChongFeng..........")
    if self.draw_obj == nil then return end
    self:CrossAction(SceneObjPart.Main, SceneObjAnimator.ChongFeng)
    self:SetSpecialJumping(true)
end

-- 特殊移动
function Character:SetIsSpecialMove(is_special_move)
    self.is_special_move = is_special_move
end

-- 特殊移动
function Character:GetIsSpecialMove()
    return self.is_special_move
end

-- 特殊移动
function Character:SetSpeicalMoveSpeed(speed)
    self.special_speed = speed
end

-- 特殊移动
function Character:SetSpeicalMoveCallBack(call_back)
    self.special_move_end_callback = call_back
end

-- 移动 获取移速
function Character:GetMoveSpeed()
    local move_speed = self.vo.move_speed
    if self:IsFear() then -- 服务器那边说恐惧时写死速度
        move_speed = 100
    end

    local speed = Scene.ServerSpeedToClient(move_speed + self.special_speed)
    if self:IsJump() then
        if self.vo.jump_factor then
            speed = self.vo.jump_factor * speed
        else
            speed = 1.8 * speed
        end
    end

    return speed
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    进入战斗     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 进入战斗
function Character:EnterFight(target_type)
    local system_event = false
    if 0 == self.fight_state_end_time then
        self:EnterFightState()
        system_event = true
    end

    local fight_continue_time = self.fight_continue_time
    if self:IsXMZCar() then
        fight_continue_time = 0
    elseif self:IsMainRole() and self:IsXiuWeiBianShen() then
        local nuqi = CultivationWGData.Instance:GetRoleCurrNuqi()
        fight_continue_time = math.max(fight_continue_time, nuqi / 5)
    end

    self.fight_state_end_time = Status.NowTime + fight_continue_time

    if self:IsMainRole() then
        if system_event then
            GlobalEventSystem:Fire(ObjectEventType.ENTER_FIGHT)
        end
        self:CheckQingGong()
    end

    if target_type == SceneObjType.Role or target_type == SceneObjType.MainRole then
        self.fight_by_role_end_time = Status.NowTime + fight_continue_time
    end
end

-- 战斗状态 进入
function Character:EnterFightState()
    if self.draw_obj == nil then return end
    self:SyncShowHp()

    if self:IsMainRole() then
        GlobalEventSystem:Fire(ObjectEventType.ENTER_FIGHT)
    end

    self.is_fighting = true
end

-- 战斗状态 离开
function Character:LeaveFightState()
    self.fight_state_end_time = 0
    self.fight_by_role_end_time = 0

    if self:IsMainRole() then
        GlobalEventSystem:Fire(ObjectEventType.EXIT_FIGHT)
        self:CheckQingGong()
    end

    self.is_fighting = false
end

-- 是否忽略离开战斗时间计算
function Character:IgnoreFightContinueTime()
    return false
end

-- 战斗状态 是否在战斗状态
function Character:IsFightState()
    return self.fight_state_end_time > Status.NowTime
end

-- 战斗 是否在由人物引起的战斗
function Character:IsFightStateByRole()
    return self.fight_by_role_end_time > Status.NowTime
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    攻击     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
local frame_move_cfg = {}
function Character.GetFrameMoveCfg(frameMoveId)
    local dic = frame_move_cfg[frameMoveId]
    if nil == dic then
        local cfg = ConfigManager.Instance:GetAutoConfig('roleskill_auto').frame_move[frameMoveId]
        if cfg then
            dic = {}
            frame_move_cfg[frameMoveId] = dic

            dic.moveZArr = {}
            local moveZArr = Split(cfg.offZ, ",")
            for i,v in ipairs(moveZArr) do
                dic.moveZArr[i] = tonumber(v) or 0
            end

            dic.moveYArr = {}
            local moveYArr = Split(cfg.offY, ",")
            for i,v in ipairs(moveYArr) do
                dic.moveYArr[i] = tonumber(v) or 0
            end

            dic.moveXArr = {}
            local moveXArr = Split(cfg.offX, ",")
            for i,v in ipairs(moveXArr) do
                dic.moveXArr[i] = tonumber(v) or 0
            end

            dic.rotateArr = {}
            local rotateArr = Split(cfg.rotate, ",")
            for i,v in ipairs(rotateArr) do
                dic.rotateArr[i] = tonumber(v) or 0
            end

            dic.moveXArrEmpty = true
            dic.moveYArrEmpty = true
            dic.moveRArrEmpty = true
            for i,v in ipairs(moveZArr) do
                dic.moveYArr[i] = dic.moveYArr[i] or 0
                dic.moveXArr[i] = dic.moveXArr[i] or 0
                dic.rotateArr[i] = dic.rotateArr[i] or 0

                if dic.moveYArr[i] ~= 0 then
                    dic.moveYArrEmpty = false
                end

                if dic.moveXArr[i] ~= 0 then
                    dic.moveXArrEmpty = false
                end

                if dic.rotateArr[i] ~= 0 then
                    dic.moveRArrEmpty = false
                end
            end
        else
            frame_move_cfg[frameMoveId] = false
        end
    elseif false == dic then
        return nil
    end

    return dic
end

--添加帧移点
--@para frameMoveId int 帧移ID
--@para delayT float 延时帧移时间
--@para targetPos Vector3 目标位置,位移不能超过这个位置
--@para target Creature 目标
function Character:AddFrameMove(frameMoveId, delayT, targetPos)
    if not self:IsMainRole() then
        return
    end

    local targetModleR = 2
    if self.attack_target_obj and self.attack_target_obj.checks and self.attack_target_obj.obj_scale then
        targetModleR = 1 + (self.attack_target_obj.checks * self.attack_target_obj.obj_scale / 2)
    end

    self.frameMoveStartT = Time.time

    local dic = Character.GetFrameMoveCfg(frameMoveId)
    if not dic then
        print_error("在表格中找不到帧移id:"..frameMoveId)
        return
    end

    self._moveList = nil
    local moveZArr = dic.moveZArr
    local moveYArr = dic.moveYArr
    local moveXArr = dic.moveXArr
    local rotateArr = dic.rotateArr
    local stopXZ = false
    local near_pos = nil

    local transform_up = Transform.GetUpOnce(self.draw_obj.root_transform)
    local transform_forward = Transform.GetForwardOnce(self.draw_obj.root_transform)
    local transform_right = Transform.GetRightOnce(self.draw_obj.root_transform)

    for i,v in ipairs(moveZArr) do
        --位移不合法只移动Y轴
        local pos
        if stopXZ then
            pos = u3dpool.v3Add(self.draw_obj:GetPosition(), u3dpool.v3Mul(transform_up, moveYArr[i]))
        else
            pos = u3dpool.v3Add(self.draw_obj:GetPosition(), u3dpool.v3Add(u3dpool.v3Mul(transform_forward, v),
                u3dpool.v3Add(u3dpool.v3Mul(transform_up, moveYArr[i]), u3dpool.v3Mul(transform_right, moveXArr[i]))))
            --位置不合法就不移动XZ轴
            local logic_x, logic_y = GameMapHelper.WorldToLogic(pos.x, pos.z)
            if not stopXZ and AStarFindWay:IsBlock(logic_x, logic_y) then
                pos = u3dpool.v3Add(self.draw_obj:GetPosition(), u3dpool.v3Mul(transform_up, moveYArr[i]))

                stopXZ = true
                -- 优化:Y坐标与ratition位移列表为空则break
                if dic.moveYArrEmpty and dic.moveRArrEmpty then
                    break
                end
            end
        end

        if delayT <= 0 then
            --与被攻击者保持一定距离
            if targetPos then
                local dis
                if not near_pos then
                    local m_pos = self.draw_obj:GetPosition()
                    dis = u3dpool.v3Sub(targetPos, m_pos)
                    dis.y = 0
                    near_pos = u3d.v3Sub(targetPos, u3dpool.v3Mul(u3dpool.v3Normalize(dis), (1 + targetModleR)))
                end
                dis = u3dpool.v3Sub(pos, self.draw_obj:GetPosition())
                dis.y = 0
                local dis2 = u3dpool.v3Sub(near_pos, self.draw_obj:GetPosition())
                dis2.y = 0
                -- 帧移距离大于碰撞距离
                if u3dpool.v3Length(dis, false) >= u3dpool.v3Length(dis2, false) then
                    stopXZ = true
                    self._moveList = self._moveList or {}
                    if #self._moveList > 0 then
                        self._moveList[#self._moveList].near_pos = near_pos
                    end

                    -- 优化:Y坐标与ratition位移列表为空则break
                    if dic.moveYArrEmpty and dic.moveRArrEmpty then
                        break
                    end
                -- 帧移距离 < 距离怪物的距离+怪物半径才检测碰撞
                -- 跟怪物保持1m距离
                else
                    dis = u3dpool.v3Sub(pos, targetPos)
                    dis.y = 0
                    if u3dpool.v3Length(dis, false) < (1 + targetModleR) * (1 + targetModleR) then
                        stopXZ = true
                        -- 优化:Y坐标与ratition位移列表为空则break
                        if dic.moveYArrEmpty and dic.moveRArrEmpty then
                            break
                        end
                    end
                end
            end
        else
            if u3dpool.v3Length(v, false) >= u3dpool.v3Length(targetPos, false) then
                stopXZ = true
                -- 优化:Y坐标与ratition位移列表为空则break
                if dic.moveYArrEmpty and dic.moveRArrEmpty then
                    break
                end
            end
        end

        local t = {}
        t.time = i * 0.03333 - delayT--+ self.frameMoveStartT
        if t.time >= 0 then
            t.time = t.time + Time.time
            t.pos = pos
            if moveZArr[i + 1] then
                moveXArr[i + 1] = moveXArr[i + 1] or 0
                moveYArr[i + 1] = moveYArr[i + 1] or 0
                if stopXZ then
                    t.speed = u3d.v3Mul(u3dpool.vec3(0, moveYArr[i+1] - moveYArr[i], 0), 30)
                else
                    t.speed = u3d.v3Mul(u3dpool.vec3(moveXArr[i+1] - moveXArr[i], moveYArr[i+1] - moveYArr[i], moveZArr[i+1] - v), 30)
                end
            else
                t.speed = u3d.vec3(0, 0, 0)
            end

            self._moveList = self._moveList or {}
            table.insert(self._moveList, t)
        end
    end
end

-- 攻击 获取攻击目标
function Character:CharacterAnimatorEvent(param, state_info, anim_name, is_imperial_hit)
    local actor_trigger = self:GetActorTrigger()
    if not actor_trigger then
        return
    end

    -- 获取源对象和目标对象
    local source = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
    local target = nil
    if self.attack_target_obj and self.attack_target_obj.draw_obj then
        target = self.attack_target_obj.draw_obj:GetRoot()
    end

    -- 觉醒技能标记
    if self.normal_skill_awake_level then
        state_info = state_info or {}
        state_info.awake_skill_level = self.normal_skill_awake_level
    end

    -- 处理灵童偏移量
    if self:IsSoulBoy() then
        state_info = {}
        if self:IsRiding() then
            state_info.off_vec = Vector3(0, 2.2, 0)
        else
            state_info.off_vec = Vector3(0, 1.2, 0)
        end
    end

    -- 处理攻击技能相关逻辑
    if self.attack_skill_id ~= nil then
        local cfg = SkillWGData.Instance:GetBuffLayerCfg(self.attack_skill_id)
        if cfg ~= nil and cfg.effect_buff_type == SKILL_BUFF_SHOW.SHAKE then
            state_info = state_info or {}
            state_info.is_ignore_shake = true
        end
        
        self:PlayAfterSkillEfffect(anim_name)
    end

    -- 处理位置信息
    if self:IsMonster() and self.attack_skill_id then
        local skill_cfg = SkillWGData.GetMonsterSkillConfig(self.attack_skill_id)
        if skill_cfg and skill_cfg.is_force_set_position == 1 and self.attack_target_pos_x and self.attack_target_pos_y then
            local pos = self:GetLuaPosition()
            state_info = {}
            local real_x, real_y = GameMapHelper.LogicToWorld(self.attack_target_pos_x, self.attack_target_pos_y)
            state_info.play_pos = Vector3(real_x, pos.y + 0.1, real_y)
        end
    else
        -- 处理非怪物的方向位置
        if self.attack_target_pos_x ~= nil and self.attack_target_pos_x ~= 0 
            and self.attack_target_pos_y ~= nil and self.attack_target_pos_y ~= 0 then
            if self.attack_target_pos_x ~= self.logic_pos.x and self.attack_target_pos_y ~= self.logic_pos.y then
                local t_x, t_y = GameMapHelper.LogicToWorld(self.attack_target_pos_x, self.attack_target_pos_y)
                local pos = Vector3(t_x, 0, t_y)
                
                if state_info ~= nil then
                    if state_info.dir_pos == nil then
                        state_info.dir_pos = pos
                    end
                else
                    state_info = {dir_pos = pos}
                end
            end
        end
    end

    -- 处理怪物蓄力多范围技能
    if self:IsMonster() and not IsEmptyTable(self.attack_sub_zone) 
        and self.attack_skill_id ~= nil and anim_name ~= nil then
        local begin = string.find(anim_name, "magic[1-4]_3/begin")
        if begin ~= nil then
            local skill_cfg = SkillWGData.GetMonsterSkillConfig(self.attack_skill_id)
            if skill_cfg ~= nil and skill_cfg.RandZoneCount ~= "" and skill_cfg.RandZoneCount > 0 then
                local pos = self:GetLuaPosition()
                local pos_table = {}
                local info = {pos_table = pos_table}
                for k, v in pairs(self.attack_sub_zone) do
                    local real_x, real_y = GameMapHelper.LogicToWorld(v.pos_x, v.pos_y)
                    local start_pos = Vector3(real_x, pos.y + 0.1, real_y)
                    table.insert(pos_table, start_pos)
                end

                actor_trigger:OnAnimatorEvent(param, info, source, nil, anim_name)
                return
            end
        end
    end

    local final_target = is_imperial_hit and source or target
    actor_trigger:OnAnimatorEvent(param, state_info, source, final_target, anim_name)
end

-- 
function Character:TryPlayNoActorEffect(bundle, asset, play_pos, dir_pos, show_state_info, extra_effect_data)
    if self:IsDeleted() then
        return
    end

    local actor_trigger = self:GetActorTrigger()
    if actor_trigger ~= nil then
        local source = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
        local target = nil
        if self.attack_target_obj and self.attack_target_obj.draw_obj then
            target = self.attack_target_obj.draw_obj:GetRoot()
        end

        local state_info = show_state_info or {dir_pos = dir_pos, play_pos = play_pos, no_actor = true}
        actor_trigger:TryPlayNoActorEffect(bundle, asset, nil, state_info, source, target, extra_effect_data)
    end
end


function Character:PlayAfterSkillEfffect(anim_name, skill_id)
    skill_id = skill_id or self.attack_skill_id
    if not skill_id then
        return
    end

    local after_cfg = SkillWGData.Instance:GetAfterSkillCfgById(skill_id)
    if not after_cfg then
        return
    end

    local is_hit, is_begin, is_black = nil, nil, nil
    if anim_name then
        -- is_hit = string.find(anim_name, "/hit")
        is_begin = string.find(anim_name, "/begin")
        -- is_black = string.find(anim_name, "/end")
    end

    -- 屏幕UI特效
    if is_begin and after_cfg.screen_effect_type ~= "" and after_cfg.screen_effect_type > 0 then
        local can_play_screen_effect = false
        local effect_target = after_cfg.screen_effect_target
        if (effect_target == SCREEN_EFFECT_TARGET_TYPE.SELF or effect_target == SCREEN_EFFECT_TARGET_TYPE.BOTH)
        and (self:IsMainRole() or self:IsSkillShower()) then
            can_play_screen_effect = true
        elseif (effect_target == SCREEN_EFFECT_TARGET_TYPE.OTHER or effect_target == SCREEN_EFFECT_TARGET_TYPE.BOTH)
        and self.attack_target_obj ~= nil and not self.attack_target_obj:IsDeleted() and self.attack_target_obj:IsMainRole() then
            can_play_screen_effect = true
        end

        if can_play_screen_effect then
            TipWGCtrl.Instance:PlaySrceenEffect(after_cfg.screen_effect_type, after_cfg.screen_effect_time)
        end
    end
end

-- 获取动作时间
function Character:GetActionTimeRecord(anim_name)
    -- body
end

function Character:SetIsAtkPlaying(attack_is_playing)
    -- if self:IsMainRole() then
    --     print_error("SetIsAtkPlaying",attack_is_playing,self.anim_name)
    -- end
    local old_attack_is_playing = self.attack_is_playing
    self.attack_is_playing = attack_is_playing

    if attack_is_playing then
        self.attack_is_playing_invalid_time = Status.NowTime + 4
    else
        self.attack_is_playing_invalid_time = 0
    end

    if true == old_attack_is_playing and false == attack_is_playing then
        self:OnAttackPlayEnd()
    end
end

function Character:IsAtkPlaying()
    return self.attack_is_playing
end

function Character:OnAnimatorBegin(param, state_info, is_ignore_action)
    if self:IsRole() then
        local skill_id
        skill_id = self.attack_skill_id
        
        local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
        if skill_cfg and skill_cfg.frame_move and skill_cfg.frame_move ~= "" then
            if self.attack_target_obj and self.dic and self.dic.ignoreCollision ~= 1 then
                self.draw_obj.root_transform:LookAt(self.attack_target_obj.draw_obj.root_transform)
            end

            if self:IsMainRole() then
                local target_pos = (self.attack_target_obj and self.dic and self.dic.ignoreCollision ~= 1) and self.attack_target_obj.draw_obj.root_transform.position or self.draw_obj.root_transform.forward * 1000
                self:AddFrameMove(skill_cfg.frame_move, 0, target_pos)
            end
        end
    end

    self.dic = nil
    self.cur_action_time = is_ignore_action and self.cur_action_time or 0
    self:SetIsAtkPlaying(true)
    local anim_name = self.anim_name
    if self:IsSoulBoy() or self:IsPet() then
        anim_name = SceneObjAnimator.Atk1
    end

    self:CharacterAnimatorEvent(param, state_info, anim_name.."/begin")
end

local function PlayProjectileCallBack(cbdata)
    local self = cbdata[1]
    local attack_target_obj = cbdata[2]
    local attack_skill_id = cbdata[3]
    Character.ReleaseCBData(cbdata)

    if not self:IsDeleted() and attack_target_obj ~= nil then
        self:OnAttackHit(attack_skill_id, attack_target_obj)
    end
end

function Character:OnAnimatorHit(param, state_info)
    if self:IsDeleted() then
        self.attack_skill_id = self.attack_skill_id or 0
        return
    end

    if self:IsAtk() then
        local do_attack_part
        -- 战斗坐骑
        if self:IsRidingFightMount() then
            do_attack_part = self.draw_obj:GetPart(SceneObjPart.Mount)
        else
            do_attack_part = self.draw_obj:GetPart(SceneObjPart.Main)
        end

        local obj = do_attack_part:GetObj()
        if nil == obj or IsNil(obj.gameObject) then
            return
        end

        if self.attack_target_obj == nil then
            return
        end
        local target_draw_obj = self.attack_target_obj.draw_obj
        if target_draw_obj == nil then
            return
        end

        self:CharacterAnimatorEvent(param, state_info, self.anim_name.."/hit")
        --[[
        if self.has_set_prefab_data then
            --新实现
            local root = target_draw_obj:GetRoot()
            local hurt_point = target_draw_obj:GetAttachPoint(AttachPoint.Hurt)
            local attack_skill_id = self.attack_skill_id
            local attack_target_obj = self.attack_target_obj
            local actor_ctrl = self:GetActorWGCtrl()
            if actor_ctrl ~= nil then
                local cbdata = Character.GetCBData()
                cbdata[1] = self
                cbdata[2] = attack_target_obj
                cbdata[3] = attack_skill_id
                actor_ctrl:PlayProjectile(obj, self.anim_name, root, hurt_point, PlayProjectileCallBack, cbdata)
            end
        else
            local actor_ctrl = obj.actor_ctrl
            if actor_ctrl ~= nil and
                self.attack_target_obj ~= nil and
                self.attack_target_obj.draw_obj ~= nil then
                local root = self.attack_target_obj.draw_obj:GetRoot().transform
                local hurt_point = self.attack_target_obj.draw_obj:GetAttachPoint(AttachPoint.Hurt)
                local attack_skill_id = self.attack_skill_id
                local attack_target_obj = self.attack_target_obj
                local cbdata = Character.GetCBData()
                cbdata[1] = self
                cbdata[2] = attack_target_obj
                cbdata[3] = attack_skill_id
                actor_ctrl:PlayProjectile(obj, self.anim_name, root, hurt_point, PlayProjectileCallBack, cbdata)
            end
        end
        ]]
    end
    
    if self.next_skill_id and self.attack_skill_id then
        if self.next_skill_id ~= 0 and self.attack_skill_id > 0 then
            self.attack_skill_id = self.attack_skill_id or 0
            self:DoAttack(
                self.next_skill_id,
                self.next_target_x,
                self.next_target_y,
                self.next_target_obj_id,
            self.next_target_type)
            self.next_skill_id = 0
        else
            self.next_skill_id = 0
            self.attack_skill_id = self.attack_skill_id or 0
        end
    end
end

function Character:OnAnimatorEnd(param, state_info)
    self:SetIsAtkPlaying(false)
    if self:IsAtk() then
        self:AttackActionEndHandle()
        -- self:ClearLineEffect()
    end
    self:CharacterAnimatorEvent(param, state_info, self.anim_name.."/end")
end

function Character:AttackActionEndHandle()
end

function Character:OnAttackHit(attack_skill_id, attack_target_obj)
    FightWGData.Instance:OnHitTrigger(self, attack_target_obj)
    -- if attack_skill_id == HotSpringWGData.XUEQIU_SKILL then
    --     attack_target_obj:playHotSpringAction(SceneObjAnimator.Dizzy)
    -- else
    if attack_skill_id == HotSpringWGData.SHUIQIU_SKILL then
        attack_target_obj:playHotSpringAction(SceneObjAnimator.Dizzy2)
    end
end

function Character:GetAttackTarget()
    return self.attack_target_obj
end

-- 攻击 设置攻击目标
function Character:SetAttackTarget(value)
    self.attack_target_obj = value
end

-- 攻击
-- @awake_level 废弃
function Character:DoAttack(skill_id, target_x, target_y, target_obj_id, target_type, awake_level, use_type, skill_type)
    -- awake_level 这个有值, 说明是MainRole那边传过来的就不再做处理
    if not awake_level then
        self.normal_skill_awake_level = nil
        if self:IsRole() then
            -- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
            local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(skill_id)
            self.normal_skill_awake_level = normal_skill and 1 or nil
            skill_id = normal_skill or skill_id
        end
    end

    self.dic = nil
    self.attack_skill_id = skill_id
    self.attack_target_pos_x = target_x
    self.attack_target_pos_y = target_y
    self.attack_use_type = use_type or ATTACK_USE_TYPE.GUAJI
    self.attack_skill_type = skill_type or ATTACK_SKILL_TYPE.NONE

    self:SetAttackTarget(Scene.Instance:GetObj(target_obj_id))

    if not SkillWGData.IsBuffSkill(skill_id) and target_x and target_y then
        local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.attack_skill_id)
        if skill_cfg then
            self.dic = ConfigManager.Instance:GetAutoConfig('roleskill_auto').frame_move[skill_cfg.frame_move]
        end

        self:SetDirectionByXY(target_x, target_y, 999)
    end

    self:EnterFight(target_type)

    --龙珠技能 不走技能编辑器  直接在指示器的位置播放技能特效
    --屏蔽他人龙珠技能特效
    local is_shield_other = SettingWGData.Instance:GetSettingData(SETTING_TYPE.SKILL_EFFECT)
    local is_shield_longzhu_skill = is_shield_other and not self:IsMainRole()
    if self:IsRole() and SkillWGData.Instance:GetIsLongZhuSkill(self.attack_skill_id)
        and (not self.longzhu_skill_cache_timer or self.longzhu_skill_cache_timer <= Status.NowTime) and not is_shield_longzhu_skill then
        local t_x, t_y = GameMapHelper.LogicToWorld(self.attack_target_pos_x, self.attack_target_pos_y)
        local effect_position = Transform.GetPositionOnce(self:GetRoot().transform)
        local pos = Vector3(t_x, effect_position.y, t_y)
        -- local bundle_name,asset_name = ResPath.GetMiscEffect("Effect_shui_qinglong_zi")
        local bundle_name, asset_name = LongZhuWGData.Instance:GetLongZhuSkillEffect(self.vo.longzhu_skill_level)--ResPath.GetEnvironmentMiscEffect("eff_mgxj")
        self:TryPlayNoActorEffect(bundle_name, asset_name, pos)
        self.longzhu_skill_cache_timer = Status.NowTime + 5
    end

    self:ChangeState(SceneObjState.Atk)
    
    -- -- 宠物技能
    -- if self:IsRole() then
    --     -- local mingjiang_obj = self:GetMingjaingObj()
    --     -- if mingjiang_obj then
    --     --     mingjiang_obj:DoAttack(skill_id, target_x, target_y, target_obj_id, target_type)
    --     -- end

    --     local pet_obj_list = self:GetPetObjList()
    --     if pet_obj_list then
    --         for i=1,#pet_obj_list do
    --             pet_obj_list[i]:TryUsePetSkill(target_x, target_y, target_obj_id)
    --         end
    --     end
    -- end
end

-- 攻击 获取攻击技能
function Character:GetAttackSkillId()
    return self.attack_skill_id
end

-- 攻击
-- @awake_level 废弃
function Character:DoAttackForNoTarget(skill_id, awake_level, x, y, obj)
    self.attack_skill_id = skill_id
    self.attack_target_pos_x = x or 0
    self.attack_target_pos_y = y or 0
    self.attack_target_obj = obj
    self.attack_use_type = ATTACK_USE_TYPE.GUAJI
    self.attack_skill_type = ATTACK_SKILL_TYPE.NONE

    self:EnterFight()
    self:ChangeState(SceneObjState.Atk)
end

-- 攻击
function Character:EndSkillReading(skill_id)

end

-- 是否是忽略动作的技能
function Character:GetIsIgnoreAnimSkill(check_skill_id)
    local skill_id = check_skill_id or self.attack_skill_id
    local sd_instance = SkillWGData.Instance
    local is_sixiang_skill = sd_instance:GetIsSiXiangSkill(skill_id)
    local is_xmzcar_skill = sd_instance:GetIsXMZCarSkill(skill_id)
    local is_longzhu_skill = sd_instance:GetIsLongZhuSkill(skill_id)
    local is_wuxing_skill = sd_instance:GetIsWuXingSkill(skill_id)
    local is_halo_skill = sd_instance:GetIsHaloSkill(skill_id)

    if is_sixiang_skill or is_xmzcar_skill or is_longzhu_skill or is_wuxing_skill or is_halo_skill then
        return true
    end

    -- 只是角色模型做了动作的技能
    local is_tianshen_bianshen = self:IsTianShenAppearance()
    local is_xiuwei_bianshen = self:IsXiuWeiBianShen()
    local is_ride_fight_mount = self:IsRidingFightMount()
    local is_gundam = self:IsGundam()
    local is_tianshen_heji_skill = sd_instance:GetIsTianShenHeJiSkill(skill_id)
    local is_customized_skill = sd_instance:GetIsCustomizedSkill(skill_id)
    if (is_tianshen_bianshen or is_ride_fight_mount or is_gundam or is_xiuwei_bianshen) and (is_tianshen_heji_skill or is_customized_skill) then
        return true
    end

    return false
end

-- 攻击
function Character:EnterStateAttack(anim_name)
    self.is_move_over_pos = false
    self:ClearActionData()
    -- self:ClearLineEffect()

    ----[[搞特殊的
    if self.attack_skill_id == HotSpringWGData.XUEQIU_SKILL then
        anim_name = SceneObjAnimator.TouZhi
    elseif self.attack_skill_id == HotSpringWGData.SHUIQIU_SKILL then
        anim_name = SceneObjAnimator.TouZhi2
    elseif self.attack_skill_id == SkillWGData.Skill_Id_11304 then
        anim_name = SceneObjAnimator.Hug_Throw
    end
    --]]

    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    local part_obj = part:GetObj()
    if part_obj == nil or IsNil(part_obj.gameObject) then
        return
    end

    -- 没有动作的技能
    if anim_name == SceneObjAnimator.Atk0 then
        anim_name = SceneObjAnimator.Idle
    end

    -- 没有动作的技能不播动作
    local ignore_do_ani = self:GetIsIgnoreAnimSkill()
    if ignore_do_ani then
        if self:GetIsInSit() then
            anim_name = SceneObjAnimator.Idle
        else
            anim_name = ""
        end
    end

    if (self:IsMainRole() or self:IsRole() or self:IsMonster()
        or self:IsSoulBoy() or self:IsPet() or self:IsFollowMingJiang() or self:IsBeast() or self:IsShuangShengTianShen())
        or self:IsTaskCallInfo() and not ignore_do_ani then
        self.action_time_record = self:GetActionTimeRecord(anim_name)

        if self:IsMainRole() and self.action_time_record and self.action_time_record.back_time then
            local back_time = self.action_time_record.back_time
            SkillWGData.Instance:SeSkillResetTime(self.action_time_record.time + back_time)
        end
    end

    if self.action_time_record ~= nil then
        self.can_cut_down_time = self.action_time_record.time
        if self.action_time_record.speed then
             self.can_cut_down_time = self.action_time_record.time / self.action_time_record.speed
        end
    end

    if anim_name and anim_name ~= "" then
        local is_ride_fight_mount = self:IsRidingFightMount()
        if is_ride_fight_mount then
            self:CrossAction(SceneObjPart.Mount, anim_name, false)
        else
            self:CrossAction(SceneObjPart.Main, anim_name, false)
        end
    end

    self.anim_name = anim_name

    if not (self:IsRole() and self.action_time_record == nil and ignore_do_ani) then
        self:OnAnimatorBegin(nil, nil, true)
    -- 没有动作的技能不播动作
    elseif (self.attack_skill_id ~= nil and self:IsMainRole()) and ignore_do_ani then
    -- and ((is_tianshen_bianshen and TianShenWGData.Instance:IsJiBanSkill(self.attack_skill_id)) or ignore_do_ani) then
        local is_sixiang_skill = SkillWGData.Instance:GetIsSiXiangSkill(self.attack_skill_id)
        local is_wuxing_skill = SkillWGData.Instance:GetIsWuXingSkill(self.attack_skill_id)
        if is_sixiang_skill or is_wuxing_skill then
            self:TryCameraShake("sixiang_shake")
        end

        self:SendFight()
    end

    ----[[主角无技能动作，需要播放音效
    if self:IsMainRole() then
        local sound_bundle, sound_asset
        local is_tianshen_heji_skill = SkillWGData.Instance:GetIsTianShenHeJiSkill(self.attack_skill_id)
        if is_tianshen_heji_skill then
            local cfg = TianShenWGData.Instance:GetHejiCfgBySkillId(self.attack_skill_id)
            if cfg then
                sound_bundle = cfg.sound_bundle
                sound_asset = cfg.sound_asset
            end
        end

        if sound_bundle and sound_asset then
            local main_draw_obj = self:GetDrawObj()
            if main_draw_obj ~= nil and not IsNil(main_draw_obj:GetTransfrom()) then
                local play_pos = main_draw_obj:GetTransfrom().position
                AudioManager.PlayAndForget(sound_bundle, sound_asset, nil, play_pos.transform)
            end
        end
    end
    --]]
    
    local is_tianshen_bianshen = self:IsTianShenAppearance()
    -- 天神某个解控技能
    if self:IsRole() and is_tianshen_bianshen then
        local cfg = SkillWGData.Instance:GetBeforeSkillCfg(self.attack_skill_id)
        if cfg ~= nil and cfg.skill_type == FRONT_SKILL_TYPE.RELIEVE and self:HasCantAttackBuff() then
            local bundle_name, asset_name = ResPath.GeBufftEffect("effect_Buff_jinghua_001")
            EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, self.draw_obj:GetTransfrom().position)
        end
    end

    if self:IsSoulBoy() then
        self:CrossAction(SceneObjPart.Mount, SceneObjAnimator.Idle)
    end
end

-- 攻击
function Character:UpdateStateAttack(elapse_time)
    if self.is_special_move then
        self:SpecialMoveUpdate(elapse_time)
    end

    self.cur_action_time = self.cur_action_time + elapse_time
    -- if self:IsMainRole() and self.action_time_record ~= nil then
    --     print_error("self.cur_action_time",self.cur_action_time,self.can_cut_down_time)
    -- end
    -- if self:IsMonster() and self.action_time_record ~= nil then
    --     print_error("self.cur_action_time",self.cur_action_time,self.can_cut_down_time,self.anim_name)
    -- end
    if self.action_time_record ~= nil then
        if self.has_action_hit == false then
            -- 触发受击
            if self.action_time_record.hit_time and self.cur_action_time >= self.action_time_record.hit_time then
                self.has_action_hit = true
                self:OnAnimatorHit()
            end
        end
        --被打断处理
        if self.has_do_cut_down == false then
            if self.can_cut_down_time > 0 then
                if self.cur_action_time >= self.can_cut_down_time then
                    self:SetIsAtkPlaying(false)
                    self.has_do_cut_down = true
                end
            end
        end

        if self.state_machine:IsInState(SceneObjState.Atk) and self.has_action_end == false then
            -- 触发动作完成
            if self.cur_action_time >= self.action_time_record.time then
                self.has_action_end = true
                self:OnAnimatorEnd()
            end
        end
    end
end

-- 攻击
function Character:QuitStateAttack(attack_skill_id)
    self:SetIsAtkPlaying(false)
    self.is_special_move = false
    self.special_speed = 0
    self:ClearActionData()
    self._moveList = nil
    --结束移动
    local main_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
    if main_obj then
        main_obj.transform.localPosition = Vector3.zero
    end
end

-- 攻击 表现完成后做的事(退出攻击动作QuitStateAttack不可靠)
function Character:OnAttackPlayEnd()
    -- body
end


-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    被打/受击     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 在这里添加的施法对象是玩家的附属物，非玩家本身，但是伤害判定为玩家。需要根据附属物来技能特效配置做相关操作
-- 通过skill_id 获取特效配置 skill_id
-- obj 为scene_obj一类
function Character:GetPrefabDataByInfo(skill_id, obj)
    if SkillWGData.Instance:GetBeastsSkillById(skill_id) then
        if obj ~= nil then
            local res_id
            local beast_skin = obj.vo.beast_skin
            local beast_id = obj.vo.beast_id
            if beast_skin and beast_skin >= 0 then
                -- beast_skin = skin_seq * 1000 + skin_level
                local skin_seq = math.floor(beast_skin / 1000)
                res_id = ControlBeastsWGData.Instance:GetBeastModelSkinResId(skin_seq)
            else
                res_id = ControlBeastsWGData.Instance:GetBeastModelResId(beast_id)
            end

            if res_id then
                return ConfigManager.Instance:GetPrefabDataAutoConfig("Yushou", res_id)
            end
        end

    elseif SkillWGData.Instance:GetIsWuHunSkill(skill_id) then
        local wuhun_data = WuHunWGData.Instance:GetWuHunSkillActiveCfg(skill_id)
        if wuhun_data then
            local res_lv = wuhun_data.appe_image_id
            local cfg = WuHunWGData.Instance:GetWuhunBreachResByIdLv(wuhun_data.wuhun_id, obj.vo.wuhun_lv)
            if cfg then
                res_lv = cfg.appe_image_id
            end

            return ConfigManager.Instance:GetPrefabDataAutoConfig("WuHun", res_lv)
        end
    end

	return nil
end

-- 被打 播放受伤回调
local function PlayHurtCallBack(p, cbdata, is_not_release_data)
    local self = cbdata[1]
    local real_blood = cbdata[2]
    local blood = cbdata[3]
    local deliverer = cbdata[4]
    local fighttype = cbdata[5]
    local text_type = cbdata[6]
    local deliverer_wuxing_type = cbdata[7]
    if not is_not_release_data then
        Character.ReleaseCBData(cbdata)
    end
    
    local real_blood_p = math.floor(real_blood * p)
    local blood_p = math.floor(blood * p)
    self:DoBeHitFloatingText(deliverer, real_blood_p, blood_p, fighttype, text_type,deliverer_wuxing_type)
end

-- 被打(有数据) deliverer：发起对象
function Character:DoBeHit(deliverer, skill_id, real_blood, blood, fighttype, text_type, deliverer_wuxing_type)
    self:Deliverer(deliverer)
    if nil == deliverer then
        self.last_attacker_pos_x = 0
        self.last_attacker_pos_y = 0
        local real_blood_p = math.floor(real_blood)
        local blood_p = math.floor(blood)
        self:DoBeHitFloatingText(deliverer, real_blood_p, blood_p, fighttype, text_type,deliverer_wuxing_type)
        return
    end

    self.last_attacker_pos_x, self.last_attacker_pos_y = deliverer:GetRealPos()

    -- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
	local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(skill_id)
    skill_id = normal_skill or skill_id

    local skill_action = SkillWGData.GetSkillActionStr(deliverer.obj_type, skill_id, deliverer.attack_index)
    self:TryDoHitShowCacha(skill_id)

    -- 同步血量
    self:SyncShowHp()
    if skill_action == nil or skill_action == "" then
        local real_blood_p = math.floor(real_blood)
        local blood_p = math.floor(blood)
        self:DoBeHitFloatingText(deliverer, real_blood_p, blood_p, fighttype, text_type,deliverer_wuxing_type)
        return
    end

    if deliverer.draw_obj then
        local deliverer_main = deliverer.draw_obj:GetPart(SceneObjPart.Main)
        local deliverer_obj = deliverer_main:GetObj()
        if deliverer_obj == nil then
            if nil ~= deliverer then
                local real_blood_p = math.floor(real_blood * 1)
                local blood_p = math.floor(blood * 1)
                self:DoBeHitFloatingText(deliverer, real_blood_p, blood_p, fighttype, text_type,deliverer_wuxing_type)
                self:OnBeHit(real_blood, deliverer, skill_id)
            end
            return
        end

        -- 实现受击飘字
        if deliverer.has_set_prefab_data then
            local attacker_actor_ctrl = deliverer:GetActorWGCtrl()
            if attacker_actor_ctrl then
                local cbdata = Character.GetCBData()
                cbdata[1] = self
                cbdata[2] = real_blood
                cbdata[3] = blood
                cbdata[4] = deliverer
                cbdata[5] = fighttype
                cbdata[6] = text_type
                cbdata[7] = deliverer_wuxing_type
                local prefab_data = self:GetPrefabDataByInfo(skill_id, deliverer)
                attacker_actor_ctrl:PlayHurt(skill_action, prefab_data, PlayHurtCallBack, cbdata)
            end
        else
            local attacker_actor_ctrl = deliverer_obj.actor_ctrl
            if attacker_actor_ctrl == nil then
                attacker_actor_ctrl = deliverer:GetActorWGCtrl()
            end

            if attacker_actor_ctrl then
                local cbdata = Character.GetCBData()
                cbdata[1] = self
                cbdata[2] = real_blood
                cbdata[3] = blood
                cbdata[4] = deliverer
                cbdata[5] = fighttype
                cbdata[6] = text_type
                cbdata[7] = deliverer_wuxing_type
                local prefab_data = self:GetPrefabDataByInfo(skill_id, deliverer)
                attacker_actor_ctrl:PlayHurt(skill_action, prefab_data, PlayHurtCallBack, cbdata)
            end
        end
    end

    if 0 < self.vo.hp then
        self:OnBeHit(real_blood, deliverer, skill_id)
    end

    -- if self.vo.hp <= 0 and self:IsMonster() and deliverer.IsMainRole() then
    --     TipWGCtrl.Instance:UpdateDoubleHitNum()
    -- end
end

-- 被打（受击）deliverer：发起对象
function Character:OnBeHit(real_blood, deliverer, skill_id)
    if SkillWGData.XingMo_Skill_Id == skill_id and deliverer and deliverer:IsRole() then
        local actor_trigger = deliverer:GetActorTrigger()
        if actor_trigger ~= nil then
            local source = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
            actor_trigger:OnAnimatorEvent(nil, nil, source, source, "magic1_1/begin")
        end
    end
end

-- 被打（受击） deliverer：发起对象
function Character:DoBeHitFloatingText(deliverer, real_blood, blood, fighttype, text_type, deliverer_wuxing_type)
    if self:IsDeleted() then
        return
    end

    -- 屏蔽与自己无关的飘字
    if not self:IsMainRole() and (not deliverer or not deliverer:IsMainRole()) and (not deliverer or not deliverer:IsPet()) then
        return
    end

    if fighttype ~= nil and fighttype == FIGHT_TYPE.FIGHT_TYPE_MAX then
        return
    end

    -- 飘字
    self:Deliverer(deliverer)
    local is_main_role = false
    local is_left = true
    local is_top = true
    if nil ~= deliverer then
        is_main_role = deliverer:IsMainRole()
    --     local root = deliverer:GetRoot()
    --     if root ~= nil and not IsNil(root.gameObject) and not IsNil(MainCamera) then
    --         local attacker = root.transform
    --         local screen_pos_1 = UnityEngine.RectTransformUtility.WorldToScreenPoint(MainCamera, self:GetRoot().transform.position)
    --         local screen_pos_2 = UnityEngine.RectTransformUtility.WorldToScreenPoint(MainCamera, attacker.position)
    --         is_left = screen_pos_1.x > screen_pos_2.x
    --         is_top = screen_pos_1.y < screen_pos_2.y
    --     end
    end

    local floating_data = Character.GetFloatingData()
    floating_data.is_main_role = is_main_role
    floating_data.deliverer = deliverer
    floating_data.blood = blood
    floating_data.fighttype = fighttype
    floating_data.pos.is_left = is_left
    floating_data.pos.is_top = is_top
    floating_data.text_type = text_type
    floating_data.deliverer_wuxing_type = deliverer_wuxing_type

    if self:IsDead() then
        self:PlayFloatingText(floating_data)
    else
        -- 飘字队列过长，直接丢弃
        if #self.floating_texts < 30 * 4 then
            table.insert(self.floating_texts, floating_data)
        end
    end
end

-- 被打(客户端纯表现) deliverer：发起对象
function Character:DoBeHitShow(deliverer, skill_id, target_obj_id)
    self:Deliverer(deliverer)
    if nil == deliverer then return end

    if not self:IsRealDead() then
        self:EnterFight(deliverer:GetType())
    end
    
    -- 仅保留发起者是主角的效果
    if not deliverer:IsMainRole() then
        return
    end

    -- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
	local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(skill_id)
    skill_id = normal_skill or skill_id

    local skill_action = SkillWGData.GetSkillActionStr(deliverer.obj_type, skill_id)

    -- 主目标和其他目标之间的受击增加以下随机间隔
    if self.vo.obj_id == target_obj_id then
        self:DoBeHitShowImpl(skill_action, deliverer, skill_id)
    else
        local delay_time = 0.5 * math.random()
        GlobalTimerQuest:AddDelayTimer(function()
            self:DoBeHitShowImpl(skill_action, deliverer, skill_id)
        end, delay_time)
    end
end

-- 被打特效（为毛是掉落物触发的）
function Character:DoSpecialEffect(skill_action, deliverer, key)
    skill_action = skill_action or "jinbi_lizi"

    if not self.draw_obj then return end
    local main_part = deliverer.draw_obj:GetPart(SceneObjPart.Main)
    local obj = main_part:GetObj()
    if not obj or IsNil(obj.gameObject) then
        return
    end

    local root = self:GetRoot()
    local hurt_point = self.draw_obj:GetAttachPoint(AttachPoint.Hurt)
    local offset_pos = u3d.v3Sub(root.transform.position, deliverer:GetRoot().transform.position)
    local attack_target_obj = self.attack_target_obj
    local actor_ctrl = self:GetActorWGCtrl()
    if actor_ctrl then
        actor_ctrl:PlayProjectile(root, skill_action, root, hurt_point, nil, nil, offset_pos, key)
    end
end

-- 被打 回调
local function DoBeHitShowImplCallBack(cbdata, is_not_release_data)
    -- local self = cbdata[1]
    -- local deliverer = cbdata[2]
    -- local skill_action = cbdata[3]

    if not is_not_release_data then
        Character.ReleaseCBData(cbdata)
    end
end

-- 被打 deliverer：发起对象
function Character:DoBeHitShowImpl(skill_action, deliverer, skill_id)
    self:Deliverer(deliverer)
    if self:IsDeleted() or nil == deliverer or deliverer:IsDeleted() or skill_action == "" then
        return
    end

    local deliverer_main = deliverer.draw_obj:GetPart(SceneObjPart.Main)
    local deliverer_obj = deliverer_main:GetObj()
    if deliverer_obj == nil then
        return
    end

    if deliverer.has_set_prefab_data then
        local attacker_actor_ctrl = deliverer:GetActorWGCtrl()
        local hurt_point = self.draw_obj:GetAttachPoint(AttachPoint.Hurt)
        if nil ~= attacker_actor_ctrl and nil ~= hurt_point then
            local root = self.draw_obj:GetRoot()
            local cbdata = Character.GetCBData()
            cbdata[1] = self
            cbdata[2] = deliverer
            cbdata[3] = skill_action

            local prefab_data = self:GetPrefabDataByInfo(skill_id, deliverer)
            attacker_actor_ctrl:PlayHurtShow(skill_action, root.transform, hurt_point, prefab_data, DoBeHitShowImplCallBack, cbdata)
        end
    else
        local attacker_actor_ctrl = deliverer_obj.actor_ctrl
        local hurt_point = self.draw_obj:GetAttachPoint(AttachPoint.Hurt)
        if nil ~= attacker_actor_ctrl and nil ~= hurt_point then
            local root = self.draw_obj:GetRoot()
            local cbdata = Character.GetCBData()
            cbdata[1] = self
            cbdata[2] = deliverer
            cbdata[3] = skill_action

            local prefab_data = self:GetPrefabDataByInfo(skill_id, deliverer)
            attacker_actor_ctrl:PlayHurtShow(skill_action, root.transform, hurt_point, prefab_data, DoBeHitShowImplCallBack, cbdata)
        end
    end
end

-- 被打 闪光 deliverer：发起对象
function Character:DoBeHitShowAction(deliverer, skill_action)
    if self:IsDeleted() then
        return
    end

    -- 获取角色对象
    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    if nil ~= part then
        -- 闪光
        self.actor_ctrl:Blink(part.obj)
    end
end

-- 被打 显示缓存
function Character:ClearHitShowCache()
    -- if self.hit_show_timer ~= nil then
    --     GlobalTimerQuest:CancelQuest(self.hit_show_timer)
    --     self.hit_show_timer = nil
    -- end

    -- self.hit_show_cache = nil
end

-- 被打 显示缓存
function Character:SaveHitShowCache(skill_id, hit_type, param)
    -- if skill_id == nil or hit_type == nil or param == nil then
    --     return
    -- end

    -- if self.hit_show_timer ~= nil then
    --     GlobalTimerQuest:CancelQuest(self.hit_show_timer)
    --     self.hit_show_timer = nil
    -- end

    -- self.hit_show_cache = {
    --     skill_id = skill_id,
    --     hit_type = hit_type,
    --     param = param,
    -- }    
    -- self.hit_show_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.TryDoHitShowCacha, self, skill_id), 1)
end

-- 被打 显示缓存
function Character:TryDoHitShowCacha(skill_id)
    -- if self:IsDeleted() then
    --     self.hit_show_cache = nil
    --     return
    -- end

    -- if self.hit_show_cache ~= nil and skill_id ~= nil then
    --     if self.hit_show_cache.skill_id ~= nil and self.hit_show_cache.skill_id == skill_id then
    --         local draw_obj = self:GetDrawObj()
    --         if draw_obj ~= nil then
    --             local point = draw_obj:GetRealAttachPoint(AttachPoint.BuffMiddle)
    --             if point ~= nil then
    --                 EffectManager.Instance:PlayControlEffect(self, "effects2/prefab/mingjiang/10023_prefab", "10023_Mingjiang_xin_02", point.position)
    --             end
    --         end

    --         self.hit_show_cache = nil
    --     end
    -- end
end

-- 受击
function Character:DoHurt()
    if self:IsDeleted() or self:IsHurt() then
        return
    end
    self:SetIsMoving(false)
    self:ChangeState(SceneObjState.Hurt)
end

-- 受击
function Character:EnterStateHurt()
    self.is_move_over_pos = false
    self.action_time_record = nil
    self.has_action_hit = false
    self.cur_action_time = 0
    local anim_name = SceneObjAnimator.Hurt

    if self:IsMonster() then
        self.action_time_record = self:GetActionTimeRecord(anim_name)
    end

    self:CrossAction(SceneObjPart.Main, anim_name)
end

-- 受击
function Character:UpdateStateHurt(elapse_time)
    self.cur_action_time = self.cur_action_time + elapse_time
    if self.action_time_record ~= nil then
        if self.has_action_hit == false then
            -- 触发受击
            if self.cur_action_time >= self.action_time_record.time then
                self.has_action_hit = true
                self:DoStand()
            end
        end
    end
end

-- 受击
function Character:QuitStateHurt()
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    死亡     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 死亡
-- 是否死亡 进入死亡状态
function Character:IsDead()
    return self.is_enter_state_dead
end

-- 是否死亡 真实死亡
function Character:IsRealDead()
    if self.vo == nil then
        return true
    end
    return self.vo.hp <= 0
end

function Character:DoDead(is_init)
    self.is_init_dead = is_init
    self:SetSpecialJumping(false)
    self:ChangeState(SceneObjState.Dead)
    self:OnDie()
end

-- 受击的技能id
function Character:SetHitSkillId(skill_id)
    -- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
	local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(skill_id)
    self.hit_skill_id = normal_skill or skill_id
end

-- 死亡
function Character:EnterStateDead()
    self:ClearHitShowCache()
    self.is_enter_state_dead = true

    if self:IsMainRole() then
        Scene.Instance:ForceCancleSkillRang()
    end

    self.is_move_over_pos = false
    self:RemoveModel(SceneObjPart.Mount)
    -- self:RemoveModel(SceneObjPart.FightMount)

    if self.is_init_dead then
        self.is_init_dead = nil
        local dead_ani = self:CurDeadAni()
        self:CrossAction(SceneObjPart.Main, dead_ani, nil, 0)
    else
        self:PlayDieAudio()
        if self:IsMonster() and not self:IsBoss() and not self:IsTower() then
            local skill_cfg = nil
            if 0 ~= self.hit_skill_id then
                skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.hit_skill_id)
            end

            if skill_cfg and 0 == skill_cfg.is_fly then
                local die_ani = self:CurDieAni()
                self:CrossAction(SceneObjPart.Main, die_ani)
                self.hit_skill_id = 0
            elseif self.deliverer and self.deliverer:GetRoot() and self:GetRoot() then
                local deliverer_vec3 = self.deliverer:GetRoot().transform.localPosition
                local deliverer_vec2 = u3d.vec2(deliverer_vec3.x, deliverer_vec3.z)
                local self_vec3 = self:GetRoot().transform.localPosition
                local self_vec2 = u3d.vec2(self_vec3.x, self_vec3.z)
                local sub_vec = u3d.v2Sub(self_vec2, deliverer_vec2)
                local normalize = u3d.v2Normalize(sub_vec)
                normalize = u3d.v2Mul(normalize, 5)

                local now_pos = u3d.v2Add(self_vec2, normalize)
                local target_pos = Vector3(now_pos.x, self_vec3.y, now_pos.y)
                local mid_vec = u3d.v2Mid(now_pos, self_vec2)
                mid_vec = Vector3(mid_vec.x, self_vec3.y + 3, mid_vec.y)

                if not AStarFindWay:IsBlock(GameMapHelper.WorldToLogic(target_pos.x, target_pos.z)) then
                    self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Dead, nil, 0.5)
                    local path = {mid_vec,target_pos}
                    normalize = u3d.v2Normalize(sub_vec)
                    normalize = u3d.v2Mul(normalize, 6)
                    local now_pos_old = now_pos
                    now_pos = u3d.v2Add(self_vec2, normalize)
                    target_pos = Vector3(now_pos.x, self_vec3.y, now_pos.y)
                    mid_vec = u3d.v2Mid(now_pos, now_pos_old)
                    mid_vec = Vector3(mid_vec.x, self_vec3.y + 0.8, mid_vec.y)
                    path[#path + 1] = mid_vec
                    path[#path + 1] = target_pos

                    normalize = u3d.v2Normalize(sub_vec)
                    normalize = u3d.v2Mul(normalize, 7)
                    now_pos = u3d.v2Add(self_vec2, normalize)
                    target_pos = Vector3(now_pos.x, self_vec3.y, now_pos.y)
                    path[#path + 1] = target_pos

                    local tweener = self:GetRoot().gameObject.transform:DOPath(
                        path,
                        0.6,
                        DG.Tweening.PathType.Linear,
                        DG.Tweening.PathMode.Full3D,
                        1,
                        nil)
                    tweener:SetEase(DG.Tweening.Ease.InQuad)
                    tweener:SetLoops(0)
                else
                    self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Die)
                end
            else
                self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Die)
            end
        else
            if self:IsGhost() then
                self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Ghost_Idle)
            else
                self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Die)
            end
        end
    end

    self:SetIsAtkPlaying(false)
    GlobalEventSystem:Fire(ObjectEventType.OBJ_DEAD, self)
    self:RemoveAllBuff()

    if self:IsMainRole() then
        GuajiWGCtrl.Instance:TrySaveFollowInfoByDie()
        GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
    end

    --self:ResetFollowList(Language.FollowState.LeaderDie)
    self:ResetServerFollowState()
    self:ResetSkillResetInfo()
end

-- 死亡
function Character:UpdateStateDead(elapse_time)
    if self.is_special_move then
        self:SpecialMoveUpdate(elapse_time)
    end

    if self.draw_obj then
        if self:IsGhost() then
            self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Ghost_Idle)
        end
    end
end

-- 死亡
function Character:QuitStateDead()
    self.is_enter_state_dead = false
end

-- 死亡 播放音效
function Character:PlayDieAudio()
    local vo = self.vo
    if not vo then
        return
    end

    local str, flag = nil, nil
    if self:IsMonster() then
        local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.vo.monster_id]
        if not cfg then return end
        str = cfg.audio_name
        flag = true
    elseif self:IsRole() then
        str = vo.sex == GameEnum.FEMALE and AudioUrl.FemaleDeath or AudioUrl.MaleDeath
    end

    if not str or "" == str then return end
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(str, flag))
end

-- 当死亡
function Character:OnDie()
end

-- 当重生
function Character:OnRealive()
    self:ResetSpecialStateInfo()
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    互动     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
function Character:DoInteractive(anim_name)
    if self:IsDeleted() or self:IsClashState() then
        return
    end

    self:SetIsMoving(false)
    self:ClearActionData()
    self.interactive_anim_name = anim_name

    self:TryClearInteractiveEffectsAndSounds()
    if self:IsStand() then
        self:ChangeState(SceneObjState.Interactive)
    elseif self:IsInteractive() then
        self:EnterStateInteractive()
    end
end

function Character:EnterStateInteractive()
    self.action_time_record = self:GetActionTimeRecord(self.interactive_anim_name)
    self:CrossAction(SceneObjPart.Main, self.interactive_anim_name, false, 0, true)
    self:CharacterAnimatorEvent(nil, nil, self.interactive_anim_name .. "/begin")
end

function Character:UpdateStateInteractive(elapse_time)
    self.cur_action_time = self.cur_action_time + elapse_time
    if self.action_time_record ~= nil then
        if self.has_action_end == false and self.cur_action_time >= self.action_time_record.time then
            self.has_action_end = true
            -- self:CharacterAnimatorEvent(nil, nil, self.interactive_anim_name .. "/end")
            self:ChangeToCommonState()
        end
    end
end

function Character:QuitStateInteractive()
    self.interactive_anim_name = nil
end

function Character:TryClearInteractiveEffectsAndSounds()
    if self:IsInteractive() then
        local actor_trigger = self:GetActorTrigger()
        if actor_trigger ~= nil then
            actor_trigger:RemoveEffectsAndSounds()
        end
    end
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    跳跃     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 跳跃
function Character:DoJump(move_mode_param)
    move_mode_param = move_mode_param or 0
    if move_mode_param == 0 then
        local jump_anim
        if self.vo.jump_act == 2 then
            jump_anim = SceneObjAnimator.Jump2
        elseif self.vo.jump_act == 3 then
            jump_anim = SceneObjAnimator.Jump3
        elseif self.vo.jump_act == 4 then
            jump_anim = SceneObjAnimator.Jump4
        else
            jump_anim = SceneObjAnimator.Jump
        end

        self:CrossAction(SceneObjPart.Main, jump_anim)
        self:OnJumpStart()
    else
        self:DoAirCraftMove(move_mode_param)
    end
end

-- 跳跃
function Character:IsJumping()
    return false
end

-- 飞行器移动
function Character:DoAirCraftMove(craft_id)
    self:EnterStateMove()
end

-- 跳跃
function Character:OnJumpStart()
    self:ChangeShadowVisible(false)
end

-- 跳跃
function Character:OnJumpEnd()
    self:UpdateShadowVisib()
end

-- 跳跃
function Character:IsJump()
    return self.is_jump or false
end

-- 跳跃
function Character:SetJump(state)
    self.is_jump = state
end

-- 御剑
function Character:IsMitsurugi()
    return self.is_mitsurugi or false
end

-- 御剑
function Character:SetMitsurugi(state)
    self.is_mitsurugi = state
end

-- 特殊跳跃状态
-- 九蛇岛副本
function Character:SetSpecialJumping(state)
end

-- 特殊跳跃状态
-- 九蛇岛副本
function Character:ResetSpecialState()
    self:SetSpecialJumping(false)
end


-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    跟随     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
function Character:IsFollowState()
    return self.is_follow_state
end

-- 跟随
function Character:IsTeamFollowState()
    return self:IsFollowState() and (self.obj_follow_type ~= nil and self.obj_follow_type == OBJ_FOLLOW_TYPE.TEAM)
end

-- 跟随
function Character:IsInFollowList(obj)
    local is_in = false
    if obj ~= nil and self.follow_me_obj_list[obj] ~= nil then
        is_in = true
    end

    return is_in
end

-- 跟随
-- is_follow_obj_del 用于在跟随的目标不再视野范围内的时候，检测是否需要重新跟随
-- is_keep_cache 刺探的跟随是会进入挂机状态的，之前为了避免标志请不掉，在设置挂机的时候会清除跟随状态
-- 但是这个会把缓存用来挂机结束后恢复跟随的缓存清了，所以如果是刺探的跟随并且是因为跟随的目标进入战斗而引起的挂机，就不清除参数
function Character:SetIsFollowState(value, obj, dis, follow_type, str, is_follow_obj_del, is_ignore_tip, is_keep_cache, is_ignore_again)
    if value and follow_type == nil then
        print_error("SetIsFollowState param is error")
        return
    end

    if value and obj ~= nil then
        if obj:IsDeleted() or not obj:IsCharacter() then
            value = false
        end

        if self:IsInFollowList(obj) then
            self.follow_me_obj_list[obj] = nil
        end

        if not value then
            self:SetFollowTarget(nil)
        end

    elseif not value then
        self:SetFollowTarget(nil)
    end

    local old_value = self.is_follow_state
    self.is_follow_state = value
    local old_follow_type = self.obj_follow_type
    local scene_logic = nil 

    if Scene and Scene.Instance then
        scene_logic =Scene.Instance:GetSceneLogic()
    end
    
    if value then
        self.obj_follow_type = follow_type
         self.sever_follow_state = false

        if scene_logic ~= nil then
            scene_logic:ResetTrackRoleInfo()
        end
    else
        self.obj_follow_type = nil
    end

    if old_follow_type ~= nil and old_follow_type == OBJ_FOLLOW_TYPE.TEAM and str ~= nil and str ~= "" then
        MainuiWGCtrl.Instance:FlushXunLuStates()
        SysMsgWGCtrl.Instance:ErrorRemind(str)
    end

    local track_role_uuid = nil
    if scene_logic ~= nil then
        local tarck_type, check_track_role_uuid = scene_logic:GetTrackRoleInfo()
        if tarck_type ~= nil and tarck_type == OBJ_FOLLOW_TYPE.TEAM then
            track_role_uuid = check_track_role_uuid
        end
    end

    local is_change = false
    if self:IsMainRole() and value ~= old_value then
        local agree = value and 1 or 0
        if SocietyWGData.Instance and SocietyWGData.Instance:GetIsInTeam() == 1 then
            SocietyWGCtrl.Instance:SendTeamMemberFollow(agree)
        end

        local status = value and OPERATION_TASK_CHAIN_FOLLOW_STATUS.FOLLOW or OPERATION_TASK_CHAIN_FOLLOW_STATUS.NO

        if OperationTaskChainWGData.Instance and OperationTaskChainWGData.Instance:GetTaskChainIsOpen() and OperationTaskChainWGData.Instance:IsTaskChainScene() then
            OperationTaskChainWGCtrl.Instance:SendTaskChainFollowStatus(status, dis == nil and 0 or dis)
        else
            if status == OPERATION_TASK_CHAIN_FOLLOW_STATUS.NO and track_role_uuid ~= nil then
                self.sever_follow_state = true
            else
                -- Scene.SendChangeFollowState(status)
            end
        end

        is_change = true
    end

    if self:IsMainRole() and self.sever_follow_state then
        if track_role_uuid == nil then
            -- Scene.SendChangeFollowState(OPERATION_TASK_CHAIN_FOLLOW_STATUS.NO)
            self.sever_follow_state = false
        end
    end

    if obj and value then
        self:SetFollowTarget(obj)
    end

    if GuajiWGCtrl.Instance ~= nil then
        if not is_keep_cache then
            if (is_change or is_follow_obj_del) and self:IsMainRole() then
                GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
            else
                if is_change then
                    GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
                end
            end
        end
    end
end

function Character:OnFollowCallBack(x, y)
    local m_x, m_y = self:GetLogicPos()
    if x == nil or y == nil then
        return
    end

    if self:IsMainRole() then
        GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), x, y, 1, nil, nil, nil, nil, nil, CLIENT_MOVE_REASON.FOLLOW)
    end
end

-- 跟随 设置跟随目标，如果之前已经跟随了目标，那么先取消
function Character:SetFollowTarget(obj)
    if self.follow_target ~= nil then
        self.follow_target:UnFollowMe(self)
    end

    self.follow_target = obj
end

function Character:GetFollowObjUuid()
    if not self:GetFollowObjIsVaild() then
        return nil
    end

    if not self.follow_target:IsRole() then
        return nil
    end

    local vo = self.follow_target:GetVo()
    return vo.uuid
end

-- 跟随
function Character:GetFollowObjIsVaild()
    return self.follow_target ~= nil and not self.follow_target:IsDeleted()
end

-- 跟随
function Character:FollowMe(obj, follow_dis, follow_type)
    if obj == nil or follow_dis == nil or follow_type == nil then
        return
    end

    if follow_type == OBJ_FOLLOW_TYPE.TEAM then
        if not obj:IsRole() then
            return
        end

        if SocietyWGData.Instance:GetIsInTeam() == 0 then
            return
        end
    end

    -- 已有跟随目标
    if obj:GetFollowObjIsVaild() then
        return
    end

    -- if self:IsFollowState() then
    --     if self.follow_target ~= nil and not self.follow_target:IsDeleted() then
    --         self.follow_target:FollowMe(obj, follow_dis)
    --     end

    --     return
    -- end

    -- 已经跟随了目标
    if obj == self then
        return
    end

    -- 要跟随自己的目标已经无效了，挪出跟随列表
    if obj:IsDeleted() or not obj:IsCharacter() then
        if self.follow_me_obj_list[obj] then
            self.follow_me_obj_list[obj] = nil
        end

        return
    end

    -- 防止相互跟随
    if obj:IsInFollowList(self) then
        return
    end

    -- 先停止跟随，暂时先不考虑跟随列表合并
    obj:ResetFollowList()
    if obj:IsMainRole() and not OperationTaskChainWGData.Instance:IsTaskChainScene() then
        GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true)
    end

    local function call()
        -- 跟随移动的对象如果离目标很远，并且目标没有移动，那么先移动过来
        if self:IsStand() then
            if obj:IsMainRole() then
                local follow_obj_pos_x, follow_obj_pos_y= obj:GetLogicPos()
                local dis = GameMath.GetDistance(self.logic_pos.x, self.logic_pos.y, follow_obj_pos_x, follow_obj_pos_y, false)
                if dis > follow_dis * follow_dis then
                    local follow_x = self.logic_pos.x - (self.follow_move_dir.x * follow_dis)
                    local follow_y = self.logic_pos.y - (self.follow_move_dir.y * follow_dis)

                    GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), follow_x, follow_y, 1, nil, nil, nil, nil, nil, CLIENT_MOVE_REASON.FOLLOW)
                end
            end
        end
    end

    if self.follow_me_obj_list[obj] ~= nil then
        if not obj:IsFollowState() then
            self.follow_me_obj_list[obj] = nil
        else
            -- 跟随移动的对象如果离目标很远，并且目标没有移动，那么先移动过来
            call()
        end

        return
    end

    obj:SetIsFollowState(false)
    self.follow_me_obj_list[obj] = {follow_dis = follow_dis, follow_type = follow_type}
    obj:SetIsFollowState(true, self, follow_dis, follow_type)

    -- 跟随移动的对象如果离目标很远，并且目标没有移动，那么先移动过来
    call()

end

-- 跟随
function Character:UnFollowMe(obj)
    if obj == nil then
        return
    end

    self.follow_me_obj_list[obj] = nil
end

-- 跟随
function Character:ResetFollowList(str)
    if self.follow_me_obj_list ~= nil and next(self.follow_me_obj_list) ~= nil then
        for k,v in pairs(self.follow_me_obj_list) do
            if k ~= nil and not k:IsDeleted() and k:IsCharacter() and k ~= self then
                k:SetIsFollowState(false, nil, nil, nil, str, true)
            end
        end
    end

    self.follow_me_obj_list = {}
    self:SetIsFollowState(false)
end

-- 跟随
function Character:CheckTrackRoleFollow()
    local track_role_id = nil
    local track_follow_type = nil
    if self.follow_me_obj_list ~= nil and next(self.follow_me_obj_list) ~= nil then
        for k,v in pairs(self.follow_me_obj_list) do
            if k ~= nil and not k:IsDeleted() and k:IsCharacter() and k ~= self then
                if self:IsRole() and k:IsMainRole() and k:IsFollowState() then
                    if v.follow_type ~= nil and v.follow_type ~= OBJ_FOLLOW_TYPE.TASK_CHAIN and self:GetVo() ~= nil then
                        track_role_id = self:GetVo().uuid
                        track_follow_type = v.follow_type
                        break
                    end
                end
            end
        end
    end

    -- 在角色销毁的时候，检测一下需不需要恢复跟随
    if not self:IsMainRole() and (track_role_id == nil or track_follow_type == nil) then
        local data = GuajiWGCtrl:GetRecoveryFollowCache()
        if data ~= nil then
            if GuajiWGCtrl.Instance:CheckIsRecoveryFollowVaild() then
                track_role_id = data.uuid
                track_follow_type = data.follow_type
            end
        end
    end

    if track_role_id ~= nil and track_follow_type ~= nil then
        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic ~= nil then
            scene_logic:SetTrackRoleData(track_follow_type, track_role_id)
            Scene.SendGetRoleMoveInfo(track_role_id, track_follow_type)
        end
    end
end

-- 跟随
function Character:CheckFollowStateIsVaild(follow_type)
    local is_vaild = true
    if self.obj_follow_type ~= nil and (follow_type == nil or follow_type == self.obj_follow_type) then
        if self.obj_follow_type == OBJ_FOLLOW_TYPE.TEAM then
            if SocietyWGData.Instance:GetIsInTeam() == 0 then
                self:SetIsFollowState(false)
                is_vaild = false
            end
        end
    end

    return is_vaild
end

-- 跟随
function Character:CheckFollowState(leader_change, follow_type)
    if not self:IsFollowState() then
        return
    end

    if self.obj_follow_type ~= nil and (follow_type == nil or follow_type == self.obj_follow_type) then
        if self.obj_follow_type == OBJ_FOLLOW_TYPE.TEAM then
            if leader_change or SocietyWGData.Instance:GetIsInTeam() == 0 then
                self:SetIsFollowState(false)
            end
        end
    end
end

-- 跟随 服务端那边会存一个跟随状态，客户端的跟随为了避免清不掉在移动的时候都是直接清除的
function Character:IsFollowStateByServer()
    if self:IsMainRole() then
        return self.sever_follow_state or self:IsFollowState()
    end

    return false
end

-- 跟随
function Character:ResetServerFollowState()
    if self.sever_follow_state then
        -- Scene.SendChangeFollowState(OPERATION_TASK_CHAIN_FOLLOW_STATUS.NO)
    end

    self.sever_follow_state = false
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    温泉     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
--温泉 站立设置
function Character:SetHotSpringStand()
    if self:GetAnmoState() == ANMO_TYPE.IS_ANMO then
        return
    end

    if self:IsWaterWay() then
        if self:ShuangXiuState() == SHUANGXIU_TYPE.IS_SHUANGXIU then
            self:playHotSpringAction(self.is_vip_boat and SceneObjAnimator.Pass or SceneObjAnimator.SHUANG_XIU)
        else
            self:playHotSpringAction(SceneObjAnimator.Idle_YouYong)
        end
    else
        self:playHotSpringAction(self:CurIdleAni())
    end

    self:ChangeHotSpringState()
end

--温泉 播放动画
--@内部调用
function Character:playHotSpringAction(anim_name, is_init)
    local scene_type = Scene.Instance:GetSceneType()
    if SceneType.HotSpring ~= scene_type and SceneType.KF_HotSpring ~= scene_type and SceneType.KF_DUCK_RACE ~= scene_type then
        return
    end

    if self:IsDeleted() or not self.draw_obj then
        return
    end

    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    if main_part ~= nil then
        if self:ShuangXiuState() == SHUANGXIU_TYPE.IS_SHUANGXIU then
            anim_name = self.is_vip_boat and SceneObjAnimator.Pass or SceneObjAnimator.SHUANG_XIU
        end

        if main_part.obj then
            local click_trans = main_part.obj.transform:FindByName("Clickable")
            if click_trans then
                if anim_name == SceneObjAnimator.Idle_YouYong then
                    click_trans.localPosition = Vector3(0,-2,0)
                else
                    click_trans.localPosition = Vector3(0,0,0)
                end
            end
        end

        if is_init then
            self:CrossAction(SceneObjPart.Main, anim_name, nil, 0)
        else
            self:CrossAction(SceneObjPart.Main, anim_name)
        end
        if anim_name == SceneObjAnimator.SHUANG_XIU or self.is_vip_boat then
            self:UpdateBoat()
            main_part:ReSetOffsetY()
        end

        if anim_name == SceneObjAnimator.Dizzy2 then
            GlobalTimerQuest:CancelQuest(self.dizzy2_delay_timer)
            self.dizzy2_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
                if self:IsStand() and not self:IsDeleted() then
                    self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle_YouYong)
                end
                GlobalTimerQuest:CancelQuest(self.dizzy2_delay_timer)
                self.dizzy2_delay_timer = nil
            end, HotSpringActionConfig[0].dizzy2)
        end
    end
end

--温泉 按摩对象  set
function Character:SetAnmoPartnerId(obj_id)
    self.vo.anmo_partner_obj = obj_id
end

--温泉 按摩对象  get
function Character:GetAnmoPartnerId()
    return self.vo.anmo_partner_obj
end

--温泉 按摩状态  set
function Character:SetAnmoState(state)
    self.vo.anmo_state = state
end

--温泉 按摩状态  get
function Character:GetAnmoState()
    return self.vo.anmo_state
end

--温泉 是否按摩发起人  set
function Character:SetIsAnmoSender(bo)
    self.vo.is_anmo_sender = bo
end

--温泉 是否按摩发起人  get
function Character:GetIsAnmoSender()
   return self.vo.is_anmo_sender
end

--温泉 双修对象  get set
function Character:SetShuangxiuPartner(obj_id)
    -- print_error("双修对象  obj_id == ",obj_id,self.vo.shuangxiu_partner_obj,self.vo.name)
    if obj_id == nil then
        return self.vo.shuangxiu_partner_obj
    end
    self.vo.shuangxiu_partner_obj = obj_id
end

--双修状态 get set
--@ 1 == 正在双修
--@ 0 == 没有双修
function Character:ShuangXiuState( state )
    -- if self.vo.name ~= "龙绮玉_s5" then
    --     print_error("双修状态  state ==",state,self.vo.shuangxiu_state,self.vo.name)
    -- end
    if state == nil then
        if self.vo == nil then
            return
        end
        return self.vo.shuangxiu_state
    end
    self.vo.shuangxiu_state = state
end

--双修主从 get set
--@ 1 == 发起者
--@ 0 == 接收者
function Character:ShuangXiuIdentity( identity )
    -- print_error("双修主从  identity ==",identity,self.vo.shuangxiu_identity,self.vo.name)
    if identity == nil then
        return self.vo.shuangxiu_identity
    end
    self.vo.shuangxiu_identity = identity
end

function Character:ChangeHotSpringState()
    if Scene.Instance:GetSceneType() ~= SceneType.HotSpring then
        return
    end
    if self:IsWaterWay() then
        if self:IsMainRole() then
            self:GetDrawObj():GetLookAtPoint(0, 0.8, 0)
        end
    else
        if self:IsMainRole() then
            self:GetDrawObj():GetLookAtPoint(0, 2.8, 0)
        end
    end
end

-- 温泉
function Character:SetIsVipBoat(is_vip_boat)
    self.is_vip_boat = is_vip_boat
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    飞行     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 飞行
function Character:GetFlyingProcess()
    return self.flying_process
end

-- 飞行
function Character:GetIsFlying()
    return self.flying_process ~= FLYING_PROCESS_TYPE.NONE_FLYING
end

-- 飞行
function Character:StartFlyingUp()
    if self.flying_process == FLYING_PROCESS_TYPE.FLYING_UP then return end
    -- self.model:SetZorderOffest(20000)
    self.flying_process = FLYING_PROCESS_TYPE.FLYING_UP
    -- self:ShadowChange(false)
    -- if self.jindouyun == nil then self:CreateJinDouYun(self.jindouyun_resid) end
end


-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    技能效果     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
function Character:GetBuffTime(buff_type)
    local time = 0
    local is_other = false
    if buff_type == nil then
        return time
    end

    if self:IsDeleted() then
        return time
    end

    local effect_list = nil
    if self:IsMainRole() then
        effect_list = FightWGData.Instance:GetMainRoleShowEffect()
    else
        effect_list = FightWGData.Instance:GetOtherRoleShowEffect(self:GetObjId())
        is_other = true
    end

    if effect_list ~= nil then
        for i = 1, #effect_list do
            local data = effect_list[i].info
            if data.buff_type == buff_type then
                time = data.cd_time
                if is_other then
                    time = data.cd_time - Status.NowTime
                end

                if data.param_list[1] ~= nil and time < data.param_list[1] * 0.001 then
                    time = 0
                end
                break
            end
        end
    end

    return time
end

function Character:GetIsInSpecialState(state)
    local is_state = false
    if state ~= nil and self.obj_special_state ~= nil and self.obj_special_state == state then
        if self.special_state_end_time ~= nil and self.special_state_end_time >= Status.NowTime then
            is_state = true
        end
    end

    return is_state
end

function Character:GetIsSpecialState(ignore_state)
    local is_state = false
    if self.obj_special_state ~= nil and self.obj_special_state ~= OBJ_SPECIAL_STATE.NONE then
        if self.special_state_end_time ~= nil and self.special_state_end_time >= Status.NowTime
        and (ignore_state == nil or (ignore_state ~= nil and self.obj_special_state ~= ignore_state)) then
            is_state = true
        end
    end

    return is_state
end

function Character:ResetTwinkleState()
    if self.obj_special_state == OBJ_SPECIAL_STATE.TWINKLE then
        self.obj_special_state = OBJ_SPECIAL_STATE.NONE
        self.special_state_end_time = 0

        local draw_obj = self:GetDrawObj()
        if draw_obj ~= nil then
            local bundle_name, asset_name = ResPath.GeBufftEffect("effect_Buff_shanxian_002")
            EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, draw_obj:GetTransfrom().position)
        end
    end
end

function Character:ResetAccumulationState()
    if self.obj_special_state == OBJ_SPECIAL_STATE.ACCUMULATION then
        self.obj_special_state = OBJ_SPECIAL_STATE.NONE
        self.special_state_end_time = 0
    end
end

function Character:ResetBackstapState()
    if self.obj_special_state == OBJ_SPECIAL_STATE.SKILL_RESET_POS_TYPE_BACKSTAB or self.obj_special_state == OBJ_SPECIAL_STATE.SKILL_RESET_POS_TYPE_BLACK_BACKSTAB then
        self.obj_special_state = OBJ_SPECIAL_STATE.NONE
        self.special_state_end_time = 0
    end
end

function Character:SetSpecialStateInfo(state, time)
    if self:GetIsSpecialState() then
        return
    end

    self.obj_special_state = state
    self.special_state_end_time = Status.NowTime + time
end

-- time为测试参数,正常逻辑不能传
function Character:SetSpecailStateInfoByBuff(buff_type, is_remove, time)
    if buff_type ~= BUFF_TYPE.EBT_KNOCK_FLY and buff_type ~= BUFF_TYPE.EBT_TYPE_CLASH and buff_type ~= BUFF_TYPE.EBT_TYPE_CHARGE then
        return
    end

    local old_state = self.obj_special_state
    if not is_remove then
        if self:IsKnockFly() or (time ~= nil and buff_type == BUFF_TYPE.EBT_KNOCK_FLY) then
            self.obj_special_state = OBJ_SPECIAL_STATE.STRIKE_FLY
            self.special_buff_time = time or self:GetBuffTime(BUFF_TYPE.EBT_KNOCK_FLY)
            self.special_state_end_time = self.special_buff_time + Status.NowTime
        elseif self:IsClash() then
            self.obj_special_state = OBJ_SPECIAL_STATE.COLLIDE
            self.special_state_end_time = self:GetBuffTime(BUFF_TYPE.EBT_TYPE_CLASH) + Status.NowTime
        elseif self:IsCharge() then
            self.obj_special_state = OBJ_SPECIAL_STATE.ACCUMULATION
            self.special_state_end_time = self:GetBuffTime(BUFF_TYPE.EBT_TYPE_CHARGE) + Status.NowTime
        else
            self.obj_special_state = OBJ_SPECIAL_STATE.NONE
            self.special_state_end_time = 0
        end
    else
        self.obj_special_state = OBJ_SPECIAL_STATE.NONE
        self.special_state_end_time = 0
    end

    if (old_state == nil or old_state == OBJ_SPECIAL_STATE.NONE) and self.obj_special_state ~= OBJ_SPECIAL_STATE.NONE then
        if self:IsMainRole() and self:IsRidingNoFightMount() then
            MountWGCtrl.Instance:SendMountGoonReq(0)
        end

        self:TryLeaveSit()
    end

    if self.special_state_fly_param ~= nil then
        -- 这里处理在击飞过程中被再次击飞和击飞过程中需要立刻掉下来，如击飞过程中死亡，这个时候服务端会立刻移除击飞BUFF
        if self.obj_special_state == OBJ_SPECIAL_STATE.STRIKE_FLY or (self.obj_special_state == OBJ_SPECIAL_STATE.NONE and not self.special_state_fly_param.is_end) then
            local value = self.special_state_fly_param.value
            local now_rotate = self.special_state_fly_param.now_rotate
            -- local has_time = math.min(self.special_state_fly_param.all_time, self.special_state_fly_param.fly_time)
            local cur_type = self.special_state_fly_param.state
            local fly_time = self.special_state_fly_param.fly_time

            local time = 0
            local keep_time = 0
            local cross_time = fly_time
            local state = STRIKE_FLY_STATE.UP
            local step_time = self.special_buff_time * 0.5
            local shake_time = self.special_state_fly_param.shake_time

            if self.obj_special_state == OBJ_SPECIAL_STATE.NONE then
                step_time = self.special_buff_time * STRIKE_FLY_DOWN_PRO
                time = step_time - self.special_state_fly_param.fly_time + Status.NowTime
                state = STRIKE_FLY_STATE.DOWN
            else
                if self.special_state_fly_param.state == STRIKE_FLY_STATE.UP then
                    step_time = self.special_buff_time * STRIKE_FLY_UP_PRO
                    time = self.special_state_fly_param.time
                    keep_time = self.special_state_fly_param.fly_time + self.special_buff_time * STRIKE_FLY_KEEP_PRO
                elseif self.special_state_fly_param.state == STRIKE_FLY_STATE.DOWN then
                    step_time = self.special_buff_time * STRIKE_FLY_DOWN_PRO
                    cross_time = step_time - self.special_state_fly_param.fly_time
                    -- keep_time = cross_time + self.special_buff_time * STRIKE_FLY_KEEP_PRO
                    fly_time = cross_time
                    local up_value = self.special_buff_time * STRIKE_FLY_UP_PRO
                    if self.special_state_fly_param.fly_time >= up_value then
                        time = up_value + Status.NowTime
                        keep_time = self.special_buff_time * STRIKE_FLY_KEEP_PRO
                    else
                        time = self.special_state_fly_param.fly_time + Status.NowTime
                        keep_time = self.special_buff_time * STRIKE_FLY_KEEP_PRO + (up_value - self.special_state_fly_param.fly_time)
                    end
                elseif self.special_state_fly_param.state == STRIKE_FLY_STATE.KEEP then
                    step_time = self.special_buff_time * (STRIKE_FLY_KEEP_PRO + STRIKE_FLY_UP_PRO)
                    keep_time = step_time
                    cross_time = step_time
                    time = step_time + Status.NowTime
                    state = STRIKE_FLY_STATE.KEEP
                end
            end

             self.special_state_fly_param = {
                state_type = OBJ_SPECIAL_STATE.STRIKE_FLY,
                state = state,
                value = value,
                rotate = STRIKE_ROTATIE,
                time = time,
                all_time = step_time,
                now_rotate = now_rotate,
                fly_time = fly_time,
                cross_time = cross_time,
                keep_time = keep_time,
                shake_time = shake_time,
            }
        else
            self.special_state_fly_param = nil
            local part = self.draw_obj:GetPart(SceneObjPart.Main)
            if part ~= nil then
                part:CheckIsNeedResetY()
                part:CheckIsNeedResetRoatateX()
            end
        end
    end
end

function Character:ResetSpecialStateInfo()
    self.obj_special_state = OBJ_SPECIAL_STATE.NONE
    self.special_state_end_time = 0
    self.special_state_fly_param = nil
    self.special_state_collide_param = nil
    self.collide_rotating_state = false
    if self.is_set_rotating_call then
        if self.draw_obj ~= nil then
            self.draw_obj:SetRotatingCallback(nil)
        end
    end

    self.is_set_rotating_call = false
    if self.draw_obj ~= nil then
        local part = self.draw_obj:TryGetPart(SceneObjPart.Main)
        if part ~= nil then
            part:CheckIsNeedResetY()
            part:CheckIsNeedResetRoatateX()
        end
    end
end

function Character:UpdateSpecialState(now_time, elapse_time)
    if nil ~= self.special_state_end_time then
        if self.special_state_end_time >= Status.NowTime then
            if self.obj_special_state == OBJ_SPECIAL_STATE.STRIKE_FLY then
                self:UpdateStrikeFly(now_time, elapse_time)
            elseif self.obj_special_state == OBJ_SPECIAL_STATE.ACCUMULATION then
            elseif self.obj_special_state == OBJ_SPECIAL_STATE.COLLIDE then
                if self:IsMainRole() then
                    self:UpdateCollide(now_time, elapse_time)
                end
            end
        else
            self:ResetSpecialStateInfo()
        end
    end
end

function Character:UpdateStrikeFly(now_time, elapse_time)
    if self.special_state_fly_param == nil then
        self.special_state_fly_param = {
            state_type = OBJ_SPECIAL_STATE.STRIKE_FLY,
            state = STRIKE_FLY_STATE.UP,
            value = 0,
            rotate = STRIKE_ROTATIE,
            time = Status.NowTime + self.special_buff_time * STRIKE_FLY_UP_PRO,
            all_time = self.special_buff_time * STRIKE_FLY_UP_PRO,
            now_rotate = 0,
            is_end = false,
            fly_time = 0,
            cross_time = 0,
            keep_time = self.special_buff_time * STRIKE_FLY_KEEP_PRO,
            shake_time = 0,
        }
    end

    local begin_rotate_time = STRIKE_ROTATIE_BEGIN_TIME * self.special_buff_time
    local fly_dir = self.special_state_fly_param.state == STRIKE_FLY_STATE.UP and 1 or -1
    local t =  self.special_state_fly_param.all_time
    local g = (STRIKE_FLY * 2) / (t * t)
    local speed = g * t
    local rotate_all_time = t - begin_rotate_time
    local rotate_speed = rotate_all_time <= 0 and 0 or STRIKE_ROTATIE / rotate_all_time

    local c_t = self.special_state_fly_param.cross_time
    local cur_s = self.special_state_fly_param.value

    local rotate_value = self.special_state_fly_param.now_rotate
    local need_set_rotate = false

    if self.special_state_fly_param.state ~= STRIKE_FLY_STATE.KEEP then
        cur_s = speed * c_t - 0.5 * g * c_t * c_t

        if fly_dir > 0 then
            need_set_rotate = c_t >= begin_rotate_time
            -- rotate_value = c_t / t * STRIKE_ROTATIE
            rotate_value = (c_t - begin_rotate_time) * rotate_speed
        else
            need_set_rotate = c_t - t < begin_rotate_time
            -- rotate_value = (1 - (c_t - t) / t) * STRIKE_ROTATIE
            rotate_value = (rotate_all_time - (c_t - t)) * rotate_speed
        end
    else
        local shake_limit_time = STRIKE_SHAKE_TIME * self.special_buff_time
        if self.special_state_fly_param.keep_time > shake_limit_time and shake_limit_time > 0 then
            local shake_speed = 360 / shake_limit_time
            self.special_state_fly_param.shake_time = self.special_state_fly_param.shake_time + elapse_time
            local shake_value = (self.special_state_fly_param.shake_time * shake_speed) % 360
            cur_s = cur_s + math.cos(shake_value) * STRIKE_SHAKE_HIGH
        end
    end

    self.special_state_fly_param.cross_time = self.special_state_fly_param.cross_time + elapse_time
    self.special_state_fly_param.fly_time = self.special_state_fly_param.fly_time + elapse_time
    self.special_state_fly_param.value = cur_s

    self.special_state_fly_param.now_rotate = not need_set_rotate and self.special_state_fly_param.now_rotate or rotate_value

    if self.special_state_fly_param.time <= Status.NowTime then
        if self.special_state_fly_param.state == STRIKE_FLY_STATE.UP then
            -- self.special_state_fly_param.value = accumulation_hight
            self.special_state_fly_param.state = STRIKE_FLY_STATE.DOWN
            -- self.special_state_fly_param.now_rotate = self.special_state_fly_param.rotate
            if self.special_state_fly_param.keep_time > 0 then
                self.special_state_fly_param.state = STRIKE_FLY_STATE.KEEP
                self.special_state_fly_param.shake_time = 0
            else
                self.special_state_fly_param.all_time = STRIKE_FLY_DOWN_PRO * self.special_buff_time
            end
        -- elseif self.special_state_fly_param.state = STRIKE_FLY_STATE.KEEP then
        --     self.special_state_fly_param.cross_time = self.special_state_fly_param.cross_time - self.special_state_fly_param.keep_time
        elseif self.special_state_fly_param.state == STRIKE_FLY_STATE.KEEP then
            self.special_state_fly_param.state = STRIKE_FLY_STATE.DOWN
             self.special_state_fly_param.all_time = STRIKE_FLY_DOWN_PRO * self.special_buff_time
             self.special_state_fly_param.cross_time = self.special_buff_time * (STRIKE_FLY_UP_PRO + STRIKE_FLY_KEEP_PRO)
        else
            self.special_state_fly_param.value = 0
            self.special_state_fly_param.state = STRIKE_FLY_STATE.UP
            self.special_state_fly_param.now_rotate = 0
            self.special_state_fly_param.all_time = STRIKE_FLY_UP_PRO * self.special_buff_time
            self.special_state_fly_param.is_end = true
            self.special_state_fly_param.keep_time = 0
            self.special_state_fly_param.shake_time = nil
        end

        self.special_state_fly_param.fly_time = 0
        -- self.special_state_fly_param.all_time = self.special_buff_time * 0.5
        if self.special_state_fly_param.state == STRIKE_FLY_STATE.KEEP then
            self.special_state_fly_param.time = Status.NowTime + self.special_state_fly_param.keep_time
        else
            -- self.special_state_fly_param.time = Status.NowTime + self.special_buff_time * 0.5
            local state_pro = self.special_state_fly_param.state == STRIKE_FLY_STATE.DOWN and STRIKE_FLY_DOWN_PRO or STRIKE_FLY_UP_PRO
            self.special_state_fly_param.time = Status.NowTime + self.special_buff_time * state_pro
        end
        -- self.special_state_fly_param.cross_time = 0
    end

    self.special_state_fly_param.value = self.special_state_fly_param.value < 0 and 0 or self.special_state_fly_param.value
    self.special_state_fly_param.now_rotate = self.special_state_fly_param.now_rotate < 0 and 0 or self.special_state_fly_param.now_rotate

    local y_off = self.special_state_fly_param.value

    local draw_obj = self:GetDrawObj()
    if draw_obj ~= nil then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        part:SetOffsetY(y_off)
        part:SetRotateX(self.special_state_fly_param.now_rotate * -1)
    end
end

function Character:RotatingCallBack(succ)
    if succ ~= nil then
        if succ == 0 then
            self.collide_rotating_state = false
            self:ResetCollideParam()
        else
            self.collide_rotating_state = true
        end
    end
end

function Character:UpdateCollide(now_time, elapse_time)
    if self.special_state_collide_param == nil then
        self.special_state_collide_param = {
            last_x = self.logic_pos.x,
            last_y = self.logic_pos.y,
        }
    end

    if not self.collide_rotating_state then
        local len = u3dpool.v2Length(u3dpool.v2Sub(Vector2(self.special_state_collide_param.last_x, self.special_state_collide_param.last_y), Vector2(self.logic_pos.x, self.logic_pos.y)), false)
        if len > 1 then
            return
        end
    end

    if self:IsMainRole() then
        self:SendMoveReq()
    end

    local forward = self.draw_obj:GetTransfrom().forward
    local dir = Vector2(forward.x, forward.z)
    local target_x, target_y = AStarFindWay:GetLineEndXY(self.logic_pos.x, self.logic_pos.y, self.logic_pos.x + dir.x * 8, self.logic_pos.y + dir.y * 8, false)

    if self.collide_rotating_state then
        self.special_state_collide_param = nil
    else
        self.special_state_collide_param = {
            last_x = target_x,
            last_y = target_y,
        }
    end

    self:DoMove(target_x, target_y)
end

function Character:ResetCollideParam()
    self.special_state_collide_param = nil
end

function Character:SetCollideDirPos(x, y)
    self:ResetCollideParam()
end

-- 特效 播放特殊特效
function Character:PlaySpecialEffect(bundle_name, asset_name, pos, point, delay_time, no_effect_ctrl, live_time, scale)
    if self:IsDeleted() or bundle_name == nil or asset_name == nil then
        return
    end

    if self.spectial_effect_delay ~= nil then
        GlobalTimerQuest:CancelQuest(self.spectial_effect_delay)
        self.spectial_effect_delay = nil
    end

    if delay_time == nil or delay_time <= 0 then
        self:TryShowSpecialEffect(bundle_name, asset_name, pos, point, no_effect_ctrl, live_time, scale)
    else
        self.spectial_effect_delay = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.TryShowSpecialEffect, self, bundle_name, asset_name, pos, point, no_effect_ctrl, live_time, scale), delay_time)
    end
end

-- 特效 try显示特殊特效
function Character:TryShowSpecialEffect(bundle_name, asset_name, pos, point, no_effect_ctrl, live_time, scale)
    if self:IsDeleted() or bundle_name == nil or asset_name == nil then
        return
    end

    local draw_obj = self:GetDrawObj()
    if draw_obj ~= nil then
        if no_effect_ctrl then
            if point ~= nil then
                local point_t = draw_obj:GetAttachPoint(point)
                EffectManager.Instance:PlayEffect(self, bundle_name, asset_name, point_t, ZeroPos, scale, nil, live_time)
            else
                pos = pos or draw_obj:GetTransfrom().position
                EffectManager.Instance:PlayEffect(self, bundle_name, asset_name, nil, pos, scale, nil, live_time)
            end
        else
            if point ~= nil then
                local point_t = draw_obj:GetAttachPoint(point)
                EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, nil, nil, point_t)
            else
                pos = pos or draw_obj:GetTransfrom().position
                EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, pos)
            end
        end
    end 
end

-- 属性连线
function Character:CheckAttrLine(buff_type, reason)
    if reason == BUFF_FLUSH_REASON.ADD then
        local effect_list = {}
        if self:IsMainRole() then
            effect_list = FightWGData.Instance:GetMainRoleEffectList()
        else
            effect_list = FightWGData.Instance:GetOtherRoleEffectList(self.vo.obj_id)
        end

        local effect_data
        for k,v in pairs(effect_list) do
            if v.buff_type == buff_type then
                effect_data = v
                break
            end
        end

        if not effect_data then return end
        local plat_type = effect_data.param_list[1]
        local role_id = effect_data.param_list[2]
        local link_uuid = MsgAdapter.ReadUUIDByValue(role_id or 0, plat_type or 0)
        self:AddShowLineEffect(buff_type, self.vo.uuid, link_uuid)
    elseif reason == BUFF_FLUSH_REASON.REMOVE then
        self:ClearAllLineEffect(buff_type)
    end
end

-- 特效 连线特效 role to role
function Character:AddShowLineEffectR2R(buff_type, side1_uuid, side2_uuid)
    -- 已存在相连的线
    for k,v in pairs(self.line_effect_cache) do
        if buff_type == v.line_key
            and (v.side1_objid == side1_uuid or v.side2_objid == side1_uuid)
            and (v.side1_objid == side2_uuid or v.side2_objid == side2_uuid) then
            return
        end
    end

    local main_role_uuid = RoleWGData.Instance:GetUUid()
    local side1_obj, side2_obj
    if side1_uuid == main_role_uuid then
        side1_obj = Scene.Instance:GetMainRole()
    else
        side1_obj = Scene.Instance:GetRoleByUUID(side1_uuid)
    end

    if side2_uuid == main_role_uuid then
        side2_obj = Scene.Instance:GetMainRole()
    else
        side2_obj = Scene.Instance:GetRoleByUUID(side2_uuid)
    end

    if side1_obj == nil or side1_obj:IsDeleted() or side1_obj:IsDead() or side1_obj:IsRealDead() then
        return
    end

    if side2_obj == nil or side2_obj:IsDeleted() or side2_obj:IsDead() or side2_obj:IsRealDead() then
        return
    end

    local link_data = {}
    link_data.side1_objid = side1_uuid
    link_data.side2_objid = side2_uuid
    link_data.side1_obj = side1_obj
    link_data.side2_obj = side2_obj
    link_data.line_key = buff_type

    self:CreateLineEffectLoder(link_data)
end

function Character:CreateLineEffectLoder(link_data)
    if not link_data then return end

    local delay_time = 0
    local end_time = nil
    local bundle, asset
    if link_data.line_key == BUFF_TYPE.EBT_ATTR_LINE then
        bundle = "effects2/prefab/dingzhi_prefab"
        asset = "dingzhi_attack06_xian"
        end_time = 10
    elseif link_data.is_select_line then
        bundle, asset = ResPath.GetEnvironmentCommonEffect("eff_hongxian")
    end

    local data_index = #self.line_effect_cache + 1
    local loader = AllocAsyncLoader(self, "line_effect_loader" .. data_index)

    link_data.loader = loader
    link_data.is_finish = false
    self.line_effect_cache[data_index] = link_data

    loader:SetIsUseObjPool(true)
    loader:SetObjAliveTime(end_time)
    local side1_draw_obj = link_data.side1_obj:GetDrawObj()
    if side1_draw_obj == nil then
        return
    end

    loader:SetParent(G_SceneObjLayer)

    loader:Load(bundle, asset, function(gameobj)
        local load_info = self.line_effect_cache[data_index]
        if load_info == nil then
            if not IsNil(gameobj) then
                loader:Destroy()
            end
            return
        end

        local side1_obj = load_info.side1_obj
        local side2_obj = load_info.side2_obj
        if (load_info.end_time ~= nil and load_info.end_time <= Status.NowTime)
            or (side1_obj == nil or side1_obj:IsDeleted() or (side1_obj.IsDead ~= nil and side1_obj:IsDead()) or (side1_obj.IsRealDead ~= nil and side1_obj:IsRealDead()))
            or (side2_obj == nil or side2_obj:IsDeleted() or (side2_obj.IsDead ~= nil and side2_obj:IsDead()) or (side2_obj.IsRealDead ~= nil and side2_obj:IsRealDead())) then
            if not IsNil(gameobj) then
                loader:Destroy()
            end
            
            self.line_effect_cache[data_index] = nil
            return
        end

        local line_render = gameobj:GetComponent(typeof(UnityEngine.LineRenderer))
        if IsNil(line_render) then
            if not IsNil(gameobj) then
                loader:Destroy()
            end

            self.line_effect_cache[data_index] = nil
            return
        end

        local is_show = delay_time <= 0
        gameobj.transform.localPosition = Vector3(0, 0, 0)
        load_info.obj = gameobj
        load_info.is_finish = true
        load_info.is_show = is_show
        load_info.line_render = line_render
        if end_time then
            load_info.end_time = Status.NowTime + end_time
        end
        load_info.delay_time = Status.NowTime + delay_time
        gameobj:SetActive(is_show)
    end)
end

-- 特效 更新连线特效位置
function Character:CheckUpdateLineEffectPos(index)
    if not self.line_effect_cache[index] then
        return
    end

    local link_info = self.line_effect_cache[index]
    if not link_info.is_finish then
        return
    end

    local side1_obj = link_info.side1_obj
    local side2_obj = link_info.side2_obj
    if (side1_obj == nil or side1_obj:IsDeleted() or (side1_obj.IsDead ~= nil and side1_obj:IsDead()) or (side1_obj.IsRealDead ~= nil and side1_obj:IsRealDead()))
    or (side2_obj == nil or side2_obj:IsDeleted() or (side2_obj.IsDead ~= nil and side2_obj:IsDead()) or (side2_obj.IsRealDead ~= nil and side2_obj:IsRealDead())) then
        self:ClearSingleLineEffect(index)
        return
    end

    if link_info.obj == nil or IsNil(link_info.obj) == nil then
        self:ClearSingleLineEffect(index)
        return
    end

    if link_info.delay_time == nil then
        self:ClearSingleLineEffect(index)
        return
    end

    if link_info.delay_time > Status.NowTime then
        return
    end

    if link_info.end_time ~= nil and link_info.end_time <= Status.NowTime then
        self:ClearSingleLineEffect(index)
        return
    end

    if link_info.line_render == nil or IsNil(link_info.line_render) then
        self:ClearSingleLineEffect(index)
        return
    end

    local side1_draw_obj = link_info.side1_obj:GetDrawObj()
    if side1_draw_obj == nil then
        self:ClearSingleLineEffect(index)
        return
    end

    local side2_draw_obj = link_info.side2_obj:GetDrawObj()
    if side2_draw_obj == nil then
        self:ClearSingleLineEffect(index)
        return
    end
    
    local pos1
    local point = side1_draw_obj:GetAttachPoint(AttachPoint.BuffMiddle)
    if point then
        pos1 = point.position
    else
        pos1 = side1_draw_obj:GetRootPosition()
    end

    if not link_info.is_show then
        link_info.is_show = true
        link_info.obj:SetActive(true)
    end
    
    local line_renderer = link_info.line_render
    line_renderer:SetPosition(0, u3dpool.vec3(pos1.x, pos1.y, pos1.z))

    local pos2
    point = side2_draw_obj:GetAttachPoint(AttachPoint.BuffMiddle)
    if point then
        pos2 = point.position
    else
        pos2 = side2_draw_obj:GetRootPosition()
    end
    
    -- 更新选中框
    -- 减少获取逻辑，所以写在这
    if link_info.is_select_line then
        FightWGCtrl.Instance:SetSelectRectObjUIPos(pos2)
    end

    line_renderer:SetPosition(1, u3dpool.vec3(pos2.x, pos2.y, pos2.z))
end

-- 特效 连线特效清除
function Character:ClearAllLineEffect(line_key)
    for k,v in pairs(self.line_effect_cache) do
        if (not line_key or v.line_key == line_key) and v.loader ~= nil then
            v.loader:DeleteMe()
        end
    end

    if not line_key or line_key == OBJ_LINR_SPECIAL_KEY.SELECT then
        if FightWGCtrl.Instance ~= nil then
	        FightWGCtrl.Instance:SetSelectRectObjActive(false)
        end
    end

    self.line_effect_cache = {}
end

function Character:ClearSingleLineEffect(index)
    if not self.line_effect_cache[index] then
        return
    end

    if self.line_effect_cache[index].is_select_line then
        FightWGCtrl.Instance:SetSelectRectObjActive(false)
    end

    self.line_effect_cache[index].loader:DeleteMe()
    self.line_effect_cache[index] = nil
end

-- BUFF特效可见性
function Character:SetIsDisableBuffEffect(disable)
    self.buff_effect_disabled = disable
    for k,v in pairs(self.buff_effect_list) do
        v:SetActive(not disable)
    end

    for k,v in pairs(self.other_effect_list) do
        v.eff:SetActive(not disable)
    end
end

-- buff_type_list是倒过来解析的
function Character:SetBuffList(buff_type_list, is_init)
    for k, v in pairs(buff_type_list) do
        if v == 0 then
            if not is_init then
                self:RemoveBuff(k)
            end
        else
            self:AddBuff(k)
        end
    end
end

function Character:FlushBuffInfo(buff_type)
	self:UpdateBuffSpecialOpera(buff_type, BUFF_FLUSH_REASON.FLUSH)
end

-- Buff 特殊操作
function Character:UpdateBuffSpecialOpera(buff_type, reason)
    if reason == BUFF_FLUSH_REASON.FLUSH then
        return
    end

    -- 玩家可以拥有多条连线
    if buff_type == BUFF_TYPE.EBT_ATTR_LINE then
        self:CheckAttrLine(buff_type, reason)
    end

    local old_state = self.buff_type_list[buff_type]
    local new_state = false
    if reason == BUFF_FLUSH_REASON.ADD then
        new_state = true
    elseif reason == BUFF_FLUSH_REASON.REMOVE then
        new_state = false
    end

    if new_state == old_state then
        return
    end

    self.buff_type_list[buff_type] = new_state

    -- xw:屏蔽伤痕印记显示 2021/11/22
	-- self:UpdateBleedShow(buff_type)

    if self:IsRole() then
        if buff_type == BUFF_TYPE.EBT_TYPE_CHARGE then
        	self:CheckAccumulation(buff_type, reason)
        elseif buff_type == BUFF_TYPE.EBT_TYPE_BIGGER then
        	self:CheckBiggerBuff(buff_type)
        elseif buff_type == BUFF_TYPE.EBT_TYPE_FROZON then
            self:CheckFrozen(reason)
        elseif buff_type == BUFF_TYPE.EBT_INVISIBLE then
            self:CheckInvisible(reason)
        --elseif buff_type == BUFF_TYPE.EBT_IMPRINT_BOMB then
            --self:UpdateImprintbombShow(reason)
        elseif buff_type == BUFF_TYPE.EBT_BLANK_SCREEN then
            self:CheckBlankScreen(reason)
        elseif buff_type == BUFF_TYPE.EBT_TYPE_DRUNK then
            self:CheckDrunk(reason)
        elseif buff_type == BUFF_TYPE.EBT_FEAR then
            self:CheckFear(reason)
        elseif buff_type == BUFF_TYPE.EBT_SURRENDER then
            self:CheckSurrender(reason)
        end
    end

    if buff_type == BUFF_TYPE.EBT_MABI then
        self:CheckMaBi(reason)
    end
end

-- 隐藏模型的时候清除所有BUFF效果，但是不清除隐身
function Character:RemoveAllBuff(is_visib_change)
    if not self.buff_effect_list then return end
    for k,v in pairs(self.buff_effect_list) do
        if not (is_visib_change and k == BUFF_TYPE.EBT_INVISIBLE) then
            self:RemoveBuff(k)
        end
    end
end

-- 添加BUFF
-- 策划子鹏骚需求，需要增加buff表字段client_type来获取配置
function Character:AddBuff(buff_type, product_id, client_type)
    -- if self:IsMainRole() then
    --     print_error("-----添加BUFF---", buff_type, product_id, client_type)
    -- end

    self:SetSpecailStateInfoByBuff(buff_type)
    self:UpdateBuffSpecialOpera(buff_type, BUFF_FLUSH_REASON.ADD)

    if product_id ~= nil and product_id > 0 then
        local skill_buff_config = ConfigManager.Instance:GetAutoConfig("buff_desc_auto").skill_buff[product_id]
        if skill_buff_config ~= nil then
            buff_type = skill_buff_config.buff_key
        end

        if product_id == PRODUCT_ID_TRIGGER.PRODUCT_ID_NEW_PLAYER_WUDI then     -- 新手副本CG的无敌buff不需要特效
            return
        end
    end

    local buff_effect_loader = self.buff_effect_list[buff_type]
    if nil == buff_effect_loader then
        local buff_config = FightWGData.Instance:GetBuffEffectCfg(buff_type, product_id)
        if client_type and not buff_config then
            buff_config = FightWGData.Instance:GetBuffEffectCfg(buff_type, client_type)
        end

        if buff_config ~= nil and buff_config.attach_index ~= "" and buff_config.bundle ~= "" then
            local draw_obj = self.draw_obj
            -- 女神buff则显示在女神身上
            if buff_config.buff_character == BUFF_CHARACTER.GODDESS then
                if self:IsRole() then
                    local soul_obj = self:GetSoulBoyObj()
                    if soul_obj then
                        draw_obj = soul_obj:GetDrawObj()
                    end
                end
            end

            local attach_obj = draw_obj:GetPart(SceneObjPart.Main):GetAttachPoint(buff_config.attach_index)
            if attach_obj then
                self.wait_load_buff_list[buff_type] = nil
                buff_effect_loader = AllocAsyncLoader(self, string.format("buff_effect_loader%s_%s", self:GetObjId(), buff_type))
                if buff_effect_loader then
                    buff_effect_loader:SetParent(attach_obj)
                    buff_effect_loader:SetIsUseObjPool(true)
                    buff_effect_loader:SetIsInQueueLoad(true)
                    buff_effect_loader:SetActive(not self.buff_effect_disabled)
                    self.buff_effect_list[buff_type] = buff_effect_loader
                    self.wait_load_buff_list[buff_type] = nil
                    local effect_asset = buff_config.asset
                    if buff_type == BUFF_TYPE.EBT_WUDI_PROTECT then
                        effect_asset = self.vo.sex == GameEnum.MALE and "1203001_attack5_L_lanse" or "1201_attack_05_L"
                    end

                    local is_show_effect = true
                    if Scene.Instance:GetSceneType() == SceneType.Kf_PVP then
                        if self.vo.obj_type ~= SceneObjType.Role then
                             is_show_effect = false
                        end
                    end

                    if is_show_effect then
                        local bundle, asset = ResPath.GetBuffEffect(buff_config.bundle, effect_asset)
                        local monster_id = self.vo.monster_id
                        local lossyScale = attach_obj.lossyScale
                        buff_effect_loader:Load(bundle, asset, function (obj)
                            if nil == obj then
                                return
                            end

                            if self:IsMonster() then
                                local monster_cfg = BossWGData.Instance:GetMonsterInfo(monster_id)
                                if monster_cfg then
                                    local bundle1, asset1 = ResPath.GetMonsterModel(monster_cfg.resid)
                                    if bundle1 and asset1 then
                                        local actor_bounds_cfg = BossWGData.Instance:GetActorBoundsCfg(bundle1.."/"..asset1)
                                        if actor_bounds_cfg then
                                            local scale = actor_bounds_cfg.scale
                                            local scale_x = lossyScale.x > 0 and scale / lossyScale.x or scale
                                            local scale_y = lossyScale.y > 0 and scale / lossyScale.y or scale
                                            local scale_z = lossyScale.z > 0 and scale / lossyScale.z or scale
                                            obj.transform.localScale = Vector3(scale_x, scale_y, scale_z)
                                        end
                                    end
                                end
                            else
                                if buff_type == BUFF_TYPE.EBT_IMPRINT_BOMB then
                                    local scale = Vector3(1, 1, 1)
                                    local scale_x = lossyScale.x > 1 and scale.x / lossyScale.x or scale.x
                                    local scale_y = lossyScale.y > 1 and scale.y / lossyScale.y or scale.y
                                    local scale_z = lossyScale.z > 1 and scale.z / lossyScale.z or scale.z
                                    
                                    obj.transform.localScale = Vector3(scale_x, scale_y, scale_z)
                                end
                            end
                        end)
                    end
                end
            else
                self.wait_load_buff_list[buff_type] = {product_id = product_id, client_type = client_type}
            end
        else
            -- print_error("No such buff:::", buff_type)
            self.buff_effect_list[buff_type] = AllocAsyncLoader(self, string.format("buff_effect_loader%s_%s", self:GetObjId(), buff_type))
            self.wait_load_buff_list[buff_type] = nil
        end
    else
        -- 重新加载buff
        if FightWGData.Instance:GetBuffEffectCfg(buff_type, product_id) then
            self:RemoveBuff(buff_type,product_id)
            self:AddBuff(buff_type, product_id, client_type)
        end
    end

    if self:IsMainRole() and buff_type == BUFF_TYPE.EBT_CHENMO then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.ChenMo)
    end

    if self:HasCantAttackBuff() and not self:HasCantMoveBuffButCanShowMove() then
        self:ChangeToCommonState()
    end

    -- if Scene.Instance:GetSceneType() == SceneType.HotSpring then
    --     if buff_type == BUFF_TYPE.EBT_XUANYUN then
    --         -- 如果没有在皮艇上
    --         if nil == Scene.Instance:GetBoatByRole(self:GetObjId()) then
    --             local part = self.draw_obj:GetPart(SceneObjPart.Main)
    --             part:SetBool(ANIMATOR_PARAM.HURT, true)
    --         end
    --     end
    -- end

    -- if self:IsRole() then
    --     if buff_type == BUFF_TYPE.EBT_MOHUA then
    --         self.draw_obj:GetRoot().transform:DOScale(Vector3(1.3, 1.3, 1.3), 0.3)
    --     elseif buff_type == BUFF_TYPE.EBT_INVISIBLE then
    --         self:SetVisible()
    --     elseif buff_type == BUFF_TYPE.EBT_TALENT_WING_SKILL then
    --         self:SetHunLuanState(true)
    --     end
    -- end

    -- if buff_type == BUFF_TYPE.EBT_PINK_EQUIP_NARROW then
    --     local scale = self.obj_scale or 1.0
    --     self.draw_obj:GetRoot().transform:DOScale(Vector3(scale - 0.3, scale - 0.3, scale - 0.3), 0.3)
    -- end

    -- if buff_type == BUFF_TYPE.EBT_TALENT_MOUNT_SKILL then
    --     if not self.substitutes_obj then
    --         self.substitutes_obj = Scene.Instance:CreateSubstitutesObjByCharacter(self)
    --     end
    -- end
end

-- 移除BUFF
function Character:RemoveBuff(buff_type, product_id)
    -- local last_can_move = false
    -- if self:IsMainRole() then
    --     last_can_move = self:CanDoMove()
    -- end

    -- if self:IsMainRole() then
    --     print_error("-----移除BUFF---", buff_type, product_id)
    -- end

    self.wait_load_buff_list[buff_type] = nil
    self:SetSpecailStateInfoByBuff(buff_type, true)
    self:UpdateBuffSpecialOpera(buff_type, BUFF_FLUSH_REASON.REMOVE)

    if nil ~= self.buff_effect_list[buff_type] then
        self.buff_effect_list[buff_type]:Destroy()
        self.buff_effect_list[buff_type]:DeleteMe()
        self.buff_effect_list[buff_type] = nil
    end

    if product_id ~= nil and product_id > 0 then
        local skill_buff_config = ConfigManager.Instance:GetAutoConfig("buff_desc_auto").skill_buff[product_id]
        if skill_buff_config ~= nil then
            buff_type = skill_buff_config.buff_key
        end
    end

    self.buff_list[buff_type] = nil
    if self:HasCantAttackBuff() then
        if self:IsAtk() then
            self:DoStand()
        end

        if self:IsXuanYun() and (Scene.Instance:GetSceneType() == SceneType.HotSpring or SceneType.KF_HotSpring == Scene.Instance:GetSceneType()) then
            self:SetHotSpringStand()
        end
    end

    local buff_param = ConfigManager.Instance:GetAutoConfig("buff_desc_auto").buff_param or {}
    if buff_param[buff_type] then
        if buff_param[buff_type].zoom_scale > 1 then
            self:CheckModleScale(1)
        end
    end

    -- print_error("RemoveBuff:::", buff_type,self.buff_effect_list[buff_type] ~= nil)
    -- 检查是否要重新触发恐惧移动
    -- if self:IsMainRole() and self:IsFear() then
    --     local cur_can_move = self:CanDoMove()
    --     if not last_can_move and cur_can_move then
    --         self:CheckFearMove()
    --     else
    --         self:StopMove(false)
    --     end
    -- end
end

-- 震屏
function Character:TryCameraShake(anim_name)
    local actor_trigger = self:GetActorTrigger()
    if actor_trigger ~= nil then
        local source = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
        local target = nil
        if self.attack_target_obj and self.attack_target_obj.draw_obj then
            target = self.attack_target_obj.draw_obj:GetRoot()
        end

        actor_trigger:OnAnimatorEvent(nil, nil, source, target, anim_name)
    end
end

-- 不能攻击BUFF
function Character:HasCantAttackBuff(skill_id)
    local flag = false

    if self:IsMainRole() then
        local cfg = SkillWGData.Instance:GetBeforeSkillCfg(skill_id)
        if cfg ~= nil and cfg.skill_type == FRONT_SKILL_TYPE.RELIEVE then
            return false
        end
    end

    if self:IsChenMo() then
        if skill_id then
            flag = SkillWGData.GetSkillBigType(skill_id) ~= SKILL_BIG_TYPE.NORMAL
        else
            flag = true
        end
    end

    if SkillWGData.Instance:IsChongCiSkill(skill_id) then
        flag = self:IsDingShen()
    end

    if SkillWGData.Instance:IsPurifySkill(skill_id) then    ---如果是净化技能不检测减益Buff
        return flag
    end

    return flag or self:IsXuanYun() or self:IsMabi() or self:IsBingDong()
            or self:IsBianxingFool() or self:IsFear() or self:IsSurrender()
            or self:IsRepelMoving()
end

-- 不能移动BUFF
function Character:HasCantMoveBuff()
    return self:IsXuanYun() or self:IsDingShen() or self:IsMabi() or self:IsBingDong() or self:IsBianxingFool() or self:IsSurrender()
end

-- 不能移动, 但要做移动表现BUFF
function Character:HasCantMoveBuffButCanShowMove()
    return self:IsFear() or self:IsRepelMoving()
end

-- 是否麻痹
function Character:IsMabi()
    return self.buff_type_list[BUFF_TYPE.EBT_MABI]
end

-- 是否眩晕
function Character:IsXuanYun()
    return self.buff_type_list[BUFF_TYPE.EBT_XUANYUN]
end

-- 是否冰冻
function Character:IsBingDong()
    return self.buff_type_list[BUFF_TYPE.EBT_BINGDONG]
end

-- 是否定身
function Character:IsDingShen()
    return self.buff_type_list[BUFF_TYPE.EBT_DINGSHEN]
end

-- 是否醉酒
function Character:IsDrunk()
    return self.buff_type_list[BUFF_TYPE.EBT_TYPE_DRUNK]
end

-- 是否沉默 效果：只限制主动技能
function Character:IsChenMo()
    return self.buff_type_list[BUFF_TYPE.EBT_CHENMO]
end

-- 是否变形不可攻击
function Character:IsBianxingFool()
    return self.buff_type_list[BUFF_TYPE.EBT_BIANXING_FOOL]
end

-- 是否迟缓
function Character:IsChiHuan()
    return self.buff_type_list[BUFF_TYPE.EBT_CHIHUAN]
end

-- 是否有护盾
function Character:IsHudun()
    return self.buff_type_list[BUFF_TYPE.EBT_HPSTORE]
end

-- 是否恐惧
function Character:IsFear()
    return self.buff_type_list[BUFF_TYPE.EBT_FEAR]
end

-- 是否击飞
function Character:IsKnockFly()
    return self.buff_type_list[BUFF_TYPE.EBT_KNOCK_FLY]
end

-- 是否冲撞
function Character:IsClash()
    return self.buff_type_list[BUFF_TYPE.EBT_TYPE_CLASH]
end

-- 是否蓄力
function Character:IsCharge()
    return self.buff_type_list[BUFF_TYPE.EBT_TYPE_CHARGE]
end

-- 是否变大
function Character:IsBigger()
    return self.buff_type_list[BUFF_TYPE.EBT_TYPE_BIGGER]
end

-- 是否割裂
function Character:IsSplit()
    return self.buff_type_list[BUFF_TYPE.EBT_SPLIT]
end

-- 是否隐身
function Character:IsInvisible()
    return self.buff_type_list[BUFF_TYPE.EBT_INVISIBLE]
end

-- 是否限制角色移动范围
function Character:IsCircleLimit()
    return self.buff_type_list[BUFF_TYPE.EBT_CIRCLE_LIMIT]
end

-- 是否黑屏
function Character:IsBlankScreen()
    return self.buff_type_list[BUFF_TYPE.EBT_BLANK_SCREEN]
end

-- 是否无敌
function Character:IsWuDi()
   return self.buff_type_list[BUFF_TYPE.EBT_WUDI]
end

-- 是否臣服（跪下）
function Character:IsSurrender()
    return self.buff_type_list[BUFF_TYPE.EBT_SURRENDER]
 end

-- 变大
function Character:CheckBiggerBuff(buff_type)
end

-- 击退、冲锋、拉人等技能产生的效果
function Character:OnSkillResetPos(skill_id, reset_pos_type, pos_x, pos_y, speed)
    if self.logic_pos.x == pos_x and self.logic_pos.y == pos_y then
        return
    end

    self.reset_pos_skill = skill_id
    self.reset_x = pos_x
    self.reset_y = pos_y
    self.reset_show_type = RESET_SPECIAL_TYPE.NORMAL
    self.reset_dis = 0
    self.reset_dir = 0
    self.reset_pass_time = 0
    self.reset_end_time = 0
    self.reset_step = speed or 14
    local after_cfg = SkillWGData.Instance:GetAfterSkillCfgById(skill_id)
    -- 拉人
    if after_cfg ~= nil and after_cfg.skill_type == BLACK_SKILL_TYPE.PULL then
        self.reset_step = after_cfg.param1 or 14
        self.reset_step = self.reset_step <= 0 and 14 or self.reset_step
    end

    if reset_pos_type == SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_CLASH then
        local w_x, w_y = GameMapHelper.LogicToWorld(pos_x, pos_y)
        local delta_pos = u3dpool.v2Sub({x = w_x, y = w_y}, self.real_pos)
        self.reset_dis = u3dpool.v2Length(delta_pos, true)
        local nor = u3dpool.v2Normalize(delta_pos)
        self.reset_dir = {x = nor.x, y = nor.y}
        self.reset_pass_time = self.reset_dis / self.reset_step
        self.reset_end_time = self.reset_pass_time + Status.NowTime
        self.reset_show_type = RESET_SPECIAL_TYPE.SPEED
    end
end

function Character:ResetSkillResetInfo(is_finish)
    local old_skill = self.reset_pos_skill
    self.reset_pos_skill = nil
    self.reset_x = nil
    self.reset_y = nil
    self.reset_show_type = RESET_SPECIAL_TYPE.NORMAL
    self.reset_dis = 0
    self.reset_dir = 0
    self.reset_pass_time = 0
    self.reset_end_time = 0
    self.reset_step = 14

    -- 冲锋结束释放技能
    if AtkCache.skill_type == ATTACK_SKILL_TYPE.BEFOTR_SKILL_CHARGE
    and AtkCache.skill_id > 0
    and AtkCache.target_obj and not AtkCache.target_obj:IsDeleted() then
        AtkCache.x, AtkCache.y = AtkCache.target_obj:GetLogicPos()
        GuajiWGCtrl.Instance:DoAttack()
    end

    if is_finish and old_skill ~= nil then
        -- 连线断了生成特效
        -- local after_cfg = SkillWGData.Instance:GetAfterSkillCfgById(old_skill)
        -- if after_cfg ~= nil and after_cfg.skill_type == BLACK_SKILL_TYPE.PULL then
        --     if not self:IsDeleted() then
        --         local draw_obj = self:GetDrawObj()
        --         if draw_obj ~= nil then
        --             local bundle = "effects2/prefab/mingjiang/10032_prefab"
        --             local asset = "10032_Mingjiang_attack1_dilie"
        --             self:PlaySpecialEffect(bundle, asset, draw_obj:GetTransfrom().position)
        --         end
        --     end
        -- end

        if self:IsClashState() then
            self:SetClashState(false)
            self:DoStand()
        end
    end
end

-- 冲撞 需要根据冲撞时间改变Magic1_3的动画时间
function Character:SetClashState(_bool)
    self.is_use_clash_time = _bool
end

-- 冲撞
function Character:IsClashState()
    return self.is_use_clash_time
end

function Character:GetResetPassTime()
    return self.reset_pass_time
end

function Character:IsInResetPosState()
    return self.reset_end_time ~= nil and self.reset_end_time >= Status.NowTime
end

function Character:UpdateResetPosBySpeed(now_time, elapse_time)
    if self.reset_end_time ~= nil and self.reset_end_time <= Status.NowTime then
        self:ResetSkillResetInfo(true)
        return
    end

    if self.reset_x == nil or self.reset_y == nil then
        self:ResetSkillResetInfo()
        return
    end

    if self.reset_dis <= 0 or self.reset_pass_time <= 0 then
        self:ResetSkillResetInfo()
        return
    end

    local move_dir = elapse_time * self.reset_step
    self.reset_dis = self.reset_dis - math.abs(move_dir)
    self.reset_pass_time = self.reset_pass_time - elapse_time

    if self.reset_dis <= 0 or self.reset_pass_time <= 0 then
        self:SetLogicPos(self.reset_x, self.reset_y)
        self:ResetSkillResetInfo(true)
        return
    end

    local add_pos = {x = self.reset_dir.x * move_dir, y = self.reset_dir.y * move_dir}
    local now_pos = u3dpool.v2Add(self.real_pos, add_pos)
    self:SetSpecialRealPos(now_pos.x, now_pos.y)
end





-- 蓄力技能
function Character:CheckAccumulation(buff_type, reason)
	if self:IsRole() then
		if self:IsDeleted() then
			self:ResetAccumulationState()
			return
		end

		if not self:IsTianShenAppearance() then
			self:ResetAccumulationState()
			return
		end

        if reason == BUFF_FLUSH_REASON.ADD then
            self:ChangeToCommonState()
            if self.attack_target_pos_x ~= nil and self.attack_target_pos_y ~= nil then
                self:SetDirectionByXY(self.attack_target_pos_x, self.attack_target_pos_y)
            end

            self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Front_Begin, false)
            self:CharacterAnimatorEvent(nil, nil, SceneObjAnimator.Front_Begin.."/begin")
            -- AudioManager.PlayAndForget("audios/sfxs/roleskill/tianshen_jiuxiaoxihe", "MingJiangSkill_14_1")
        end
	end
end

-- 蓄力技能
function Character:CheckAccumulationCanSend(reason_type, skill_id)
    if self:IsRole() then
        if self:IsDeleted() then
            self:ResetAccumulationState()
            return
        end

        if not self:IsTianShenAppearance() then
            self:ResetAccumulationState()
            return
        end
    end

    self:ResetAccumulationState()
    if self.attack_target_pos_x ~= nil and self.attack_target_pos_y ~= nil and skill_id ~= nil and skill_id ~= 0 and reason_type == BUFF_REMOVE_REASON.EFFECT_REMOVE_TIMEOUT then
        self:ChangeToCommonState()
        if self:IsRidingNoFightMount() then
            MountWGCtrl.Instance:SendMountGoonReq(0)
        end
        FightWGCtrl.Instance:TryUseRoleSkill(skill_id, GuajiCache.target_obj, self.attack_target_pos_x, self.attack_target_pos_y, ATTACK_SKILL_TYPE.ACCUMULATION_BACK)
    end
end

-- 星辰印记爆开
function Character:ShowBurstBuffRemoveShow(reason_type, skill_id)
    if reason_type ~= nil and reason_type == BUFF_REMOVE_REASON.EFFECT_REMOVE_BURST then
        -- if self.hit_show_cache ~= nil and self.hit_show_cache.hit_type == HIT_SHOW_TYPE.REMOVE_BUFF 
        --     and self.hit_show_cache.param ~= nil and self.hit_show_cache.param == BUFF_TYPE.EBT_TYPE_STAR2 then
        --     local draw_obj = self:GetDrawObj()
        --     if draw_obj ~= nil then
        --         local point = draw_obj:GetRealAttachPoint(AttachPoint.BuffMiddle)
        --         if point ~= nil then
        --             EffectManager.Instance:PlayControlEffect(self, "effects2/prefab/mingjiang/10023_prefab", "10023_Mingjiang_xin_02", point.position)
        --         end
        --     end
        -- end
        local draw_obj = self:GetDrawObj()
        if draw_obj ~= nil then
            local point = draw_obj:GetRealAttachPoint(AttachPoint.BuffMiddle)
            if point ~= nil then
                EffectManager.Instance:PlayControlEffect(self, "effects2/prefab/mingjiang/10023_prefab", "10023_Mingjiang_xin_02", point.position)
            end
        end

        -- self:SaveHitShowCache(skill_id, HIT_SHOW_TYPE.REMOVE_BUFF, BUFF_TYPE.EBT_TYPE_STAR2)
    end
end

-- 隐身
function Character:CheckInvisible(reason)
    if self:IsMainRole() then
        if self.draw_obj ~= nil then
            local part = self.draw_obj:GetPart(SceneObjPart.Main)
            local weapon_part = self.draw_obj:GetPart(SceneObjPart.Weapon)

            if reason == BUFF_FLUSH_REASON.ADD then
                if part ~= nil then
                    part:PlayFade(true, 0.25)
                end

                if weapon_part ~= nil then
                    weapon_part:PlayFade(true, 0.25)
                end


            else
                if part ~= nil then
                    part:PlayFade(false)
                end

                if weapon_part ~= nil then
                    weapon_part:PlayFade(false)
                end
            end
        end
    else
        if reason == BUFF_FLUSH_REASON.ADD then
            self:ForceSetVisible(false)
            local follow_ui = self:GetFollowUi()
            if follow_ui ~= nil then
                follow_ui:ForceSetVisible(false)
            end
            self:ForceShadowVisible(false)
            -- 隐身取消选中
            GuajiWGCtrl.Instance:OnObjInvalid(self)
            if self:IsRole() then
                local role_id = self:GetOriginId()
                RevengeWGData.Instance:ClearRoleInList(role_id)
            end

            self:DoStand()
        elseif reason == BUFF_FLUSH_REASON.REMOVE then
            self:CancelForceSetVisible()
            local follow_ui = self:GetFollowUi()
            if follow_ui ~= nil then
                follow_ui:CancelForceSetVisible()
            end
            self:ForceShadowVisible(true)

            -- if self:IsRole() then
            --     self:ResetPlayIdle()
            -- end
        end
    end 

end

-- 醉酒
function Character:CheckDrunk(reason)
    if self:IsMainRole() then
        if reason == BUFF_FLUSH_REASON.ADD then
            local time = self:GetBuffTime(BUFF_TYPE.EBT_TYPE_DRUNK)
            if time ~= nil and time > 0 then
                TipWGCtrl.Instance:PlaySrceenEffect(SKILL_UI_EFFECT_TYPE.DRUNK, time)
            end
        end
        self:ReloadDrunkLabel()
    end
end

-- 冰冻
function Character:CheckFrozen(reason)
    if self.draw_obj ~= nil then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        if part ~= nil then
            if reason == BUFF_FLUSH_REASON.REMOVE then
                part:TryResetMainTexture()
            elseif reason == BUFF_FLUSH_REASON.ADD then
                local loader = AllocResSyncLoader(self, "img_frozon")
                loader:Load("misc/texture", "FrozonTex.png", TypeUnityTexture,
                    function(texture)
                        if part ~= nil and texture ~= nil then
                            part:SetMainTexture(texture)
                        end
                 end)
            end
        end
    end
end

-- 麻痹
function Character:CheckMaBi(reason)
    if self:IsDeleted() then
        return
    end

    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    local Weapon_part
    if self:IsWeaponOwnAnim() then
        Weapon_part = self.draw_obj:GetPart(SceneObjPart.Weapon)
    end

    if reason == BUFF_FLUSH_REASON.ADD then
        self.draw_obj:SetIsGray(true)
        --[[
        local loader = AllocResSyncLoader(self, "img_mabi")
        loader:Load("misc/texture", "MarbleTex.png", TypeUnityTexture,
            function(texture)
                if texture ~= nil then
                    if main_part then
                        main_part:SetMainTexture(texture)
                    end

                    if Weapon_part then
                        Weapon_part:SetMainTexture(texture)
                    end
                end
            end
        )
        --]]
        if main_part then
            main_part:SetAnimatorSpeed(0)
        end

        if Weapon_part then
            Weapon_part:SetAnimatorSpeed(0)
        end
    elseif reason == BUFF_FLUSH_REASON.REMOVE then
        self.draw_obj:SetIsGray(false)
        if main_part then
            -- main_part:TryResetMainTexture()
            main_part:SetAnimatorSpeed(1)
        end

        if Weapon_part then
            -- Weapon_part:TryResetMainTexture()
            Weapon_part:SetAnimatorSpeed(1)
        end

        self:ChangeToCommonState()
    end
end

-- 臣服技能
function Character:CheckSurrender(reason)
    if self:IsDeleted() then
        return
    end

    if not self:IsRole() then
        return
    end

    if reason == BUFF_FLUSH_REASON.ADD then
        self:RemoveWuHun()
        self:RemoveModel(SceneObjPart.Weapon)
        if self:IsMainRole() then
            GlobalEventSystem:Fire(OtherEventType.ROLE_WUHUNZHENSHEN_CHANGE, false)
            GuajiWGCtrl.Instance:StopGuaji()
        end
        
        self:SetIsAtkPlaying(false)
        self:ChangeToCommonState()

        local is_tianshen_bianshen = self:IsTianShenAppearance()
        local is_xiuwei_bianshen = self:IsXiuWeiBianShen()
        local is_ride_fight_mount = self:IsRidingFightMount()
        local is_gundam = self:IsGundam()

        if is_tianshen_bianshen or is_xiuwei_bianshen or is_ride_fight_mount or is_gundam then
            return
        end

        self:CrossAction(SceneObjPart.Main, SceneObjAnimator.CustomizedHit5)
        self:CharacterAnimatorEvent(nil, nil, SceneObjAnimator.CustomizedHit5.."/begin")
    elseif reason == BUFF_FLUSH_REASON.REMOVE then
        self:CrossAction(SceneObjPart.Main, self:CurIdleAni())
        self:ChangeWuHun()
        self:EquipDataChangeListen()
        self:ChangeToCommonState()

        if self:IsMainRole() then
            GuajiWGCtrl.Instance:ResetGuaJi()
        end
    end
end

-- 黑屏技能
function Character:CheckBlankScreen(reason)

end









--------------------------------------------------- 恐惧-------------------------------------------------------
-- 恐惧
function Character:CheckFear(reason)
    if self:IsMainRole() then
        -- print_error("---玩家恐惧buff变化---", reason == BUFF_FLUSH_REASON.ADD)

        if reason == BUFF_FLUSH_REASON.ADD then
            local x, y = self:GetLogicPos()
            self.save_fear_pos = {x = x, y = y}
            self:StopMove(false)
            self:CheckFearMove()
        elseif reason == BUFF_FLUSH_REASON.REMOVE then
            self.save_fear_pos = nil
            self:StopMove(false)
        end
    end
end

-- 恐惧移动
function Character:CheckFearMove()
    if self:IsDeleted() then
        return
    end

    if not self:IsFear() then
        return
    end

    self:DoFear()
end

-- 恐惧移动
function Character:DoFear()
    local limit = 5
    if self.save_fear_pos == nil then
        return
    end

    local m_x, m_y = AStarFindWay:GetRandomVaildXY(self.save_fear_pos.x, self.save_fear_pos.y, limit)
    if m_x == nil or m_y == nil then
        return
    end

    local scene_id = Scene.Instance:GetSceneId()
    GuajiWGCtrl.Instance:SetMoveToPosCallBack(BindTool.Bind(self.CheckFearMove, self))
    GuajiWGCtrl.Instance:MoveToPos(scene_id, m_x, m_y, 3)
end

-- 伤痕印记
function Character:UpdateBleedShow(buff_type)
    if self.follow_ui ~= nil and buff_type == BUFF_TYPE.EBT_SHANGHENG then
        local effect_list = nil
        local is_show_bleed = false
        if self:IsMainRole() then
            effect_list = FightWGData.Instance:GetMainRoleEffectList()
        else
            effect_list = FightWGData.Instance:GetOtherRoleEffectList(self:GetObjId())
        end

        if effect_list ~= nil then
            for i = 1, #effect_list do
                local data = effect_list[i]
                if data.buff_type == buff_type then
                    local buff_cfg = SkillWGData.Instance:GetBuffCfgById(data.effect_id)
                    if buff_cfg ~= nil and buff_cfg.merge_layer == data.merge_layer then
                        is_show_bleed = true
                    end
                    break
                end
            end
        end

        self.follow_ui:UpdateBleedShow(is_show_bleed)
    end
end

-- 印记炸弹
function Character:UpdateImprintbombShow(reason)
    if self.follow_ui ~= nil and reason == BUFF_FLUSH_REASON.REMOVE then
        self:TrySetImprintBombValue(0)
    end
end

-- 印记炸弹
function Character:TrySetImprintBombValue(value)
    if self.follow_ui ~= nil and value ~= nil then
        self.follow_ui:SetShowTimer(Language.Common.CrossAnecdoteBossTimer, value, self:GetObjId())
    end
end

-- 玩家身上添加特效
function Character:AddEffect(res, attach_index)
    if self.other_effect_list[res] then return end
    attach_index = attach_index or 0
    local attach_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetAttachPoint(attach_index)
    if attach_obj then
        local async_loader = AllocAsyncLoader(self, string.format("other_effect_loader%s_%s", self:GetObjId(), res))
        async_loader:SetParent(attach_obj)
        self.other_effect_list[res] = {eff = async_loader, time = Status.NowTime + 2}
        self.other_effect_list[res].eff:SetActive(not self.buff_effect_disabled)
        self.other_effect_list[res].eff:Load(ResPath.GetBuffEffect("effects2/prefab/buff_prefab", res))
    end
end

--------------------------------------------------- 龙珠技能 -------------------------------------------------------
-- 龙珠技能特效
function Character:ShowLongZhuSkillEffect()
    if self:IsDeleted() then
        return
    end
    if nil == self.longzhu_skill_effect_loader then
        self.longzhu_skill_effect_loader = AllocAsyncLoader(self, "long_zhu_skill_effect_loader")
        local point = self.draw_obj:GetAttachPoint(AttachPoint.HurtRoot)
        if point then
            self.longzhu_skill_effect_loader:SetParent(point)
            self.longzhu_skill_effect_loader:SetIsUseObjPool(true)
            self.longzhu_skill_effect_loader:SetObjAliveTime(3)
            local bunble = "effects2/prefab/misc/effect_hczy_dian_attack02_prefab"
            local asset = "effect_hczy_dian_attack02"
            self.longzhu_skill_effect_loader:Load(bunble, asset, function(obj)
                if IsNil(obj) then
                    self:ClearLongZhuSkillLoader()
                    return
                end
                obj.transform.localScale = Vector2(0.4, 0.4, 0.4)
            end)
        end
    end

    self.play_longzhu_skill_effect_timer = GlobalTimerQuest:AddDelayTimer(function()
        self:ClearLongZhuSkillLoader()
    end, 3)
end

-- 龙珠技能
function Character:ClearLongZhuSkillLoader()
    if self.longzhu_skill_effect_loader then
        self.longzhu_skill_effect_loader:Destroy()
        self.longzhu_skill_effect_loader = nil
    end
end

-- 法宝攻击，特殊处理
function Character:OnBaoJuFight()
    -- body
    if self.has_eff_flag then return end
    local part = self.draw_obj:GetPart(SceneObjPart.BaoJu)
    if not part or not part:GetObj() then return end
    local transform = part:GetObj().gameObject.transform:Find("Bone001")
    if not transform then return end

    local target = nil
    if self.attack_target_obj and self.attack_target_obj.draw_obj then
        target = self.attack_target_obj.draw_obj:GetRoot()
    end
    if not target then return end

    self.has_eff_flag = true
    local bundle_name, asset_name = ResPath.GetMiscEffect("fabao_zidan")
    EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, transform.position, target.transform.position,
        nil, nil ,nil, function()
            self.has_eff_flag = false
        end)
end



-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------   模型     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
function Character:OnModelLoaded(part, obj, obj_class)
    SceneObj.OnModelLoaded(self, part, obj, obj_class)
    if part == SceneObjPart.Main then
        for k, v in pairs(self.buff_list) do
            if nil == self.buff_effect_list[k] then
                local buff_cfg = FightWGData.Instance:GetBuffEffectCfg(k)
                if buff_cfg ~= nil then
                    local attach_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetAttachPoint(buff_cfg.attach_index)
                    if attach_obj then
                        self.buff_effect_list[k] = AllocAsyncLoader(self, string.format("buff_effect_loader%s_%s", self:GetObjId(), k))
                        self.buff_effect_list[k]:SetParent(attach_obj)
                        self.buff_effect_list[k]:SetIsUseObjPool(true)
                        self.buff_effect_list[k]:Load(ResPath.GetBuffEffect(buff_cfg.bundle, buff_cfg.asset))
                    end
                end
            end
        end
        self.buff_list = {}

        local wait_list = {}
        for k,v in pairs(self.wait_load_buff_list) do
            wait_list[k] = v
        end

        self.wait_load_buff_list = {}
        for k, v in pairs(wait_list) do
            if nil == self.buff_effect_list[k] and v ~= nil then
                self:AddBuff(k, v.product_id, v.client_type)
            end
        end
    end
end

function Character:PlayMainAni(name)
    if name == nil or name == "" then
        return
    end

    local draw_obj = self:GetDrawObj()
    if draw_obj == nil then
        return
    end

    local main_part = draw_obj:GetPart(SceneObjPart.Main)
    if main_part == nil then
        return
    end
    main_part:CrossFade(name)
end

function Character:CurIdleAni(is_ignore_sit)
    if self:GetIsInSit() and not is_ignore_sit then
        return SceneObjAnimator.Sit_Idle
    elseif self:IsWingFly() and not self:IsTianShenAppearance() and not self:IsGundam() then
        return SceneObjAnimator.Fly_Idle
    elseif self:IsRole() and self:IsEmtrace() then
        return SceneObjAnimator.Hug_Idle
    elseif self:IsRole() and self:IsWaterWay() then
        return SceneObjAnimator.Idle_YouYong
    elseif self:IsRole() and self:IsRiding() then
        return self:SetRidingActionIdelParam()
    else
        return SceneObjAnimator.Idle
    end
end

function Character:CurRunAni()
    if self:IsRiding() then
        return self:SetRidingActionRunParam()
    elseif self:IsWingFly() and not self:IsTianShenAppearance() and not self:IsGundam() then
        return SceneObjAnimator.Fly_Run
    elseif self:IsRole() and self:IsXMZCar() then
        return SceneObjAnimator.Move
    elseif self:IsRole() and self:IsEmtrace() then
        return SceneObjAnimator.Hug_Run
    -- elseif self:IsMainRole() and self:IsFightState() then
    --     return SceneObjAnimator.FightMove
    elseif self:IsRole() and self:GetIsInSpring() then
        if self:IsWaterWay() then
            return SceneObjAnimator.Run_YouYong
        else
            return SceneObjAnimator.Move
        end
    elseif self.IsKunRiding and self:IsKunRiding() then
        return SceneObjAnimator.Kun_Idle
    elseif self:IsGhost() then
        return SceneObjAnimator.Ghost_Run
    elseif self:IsRole() and self:GetIsInHuSong() then
        return SceneObjAnimator.Walk
    else
        return SceneObjAnimator.Move
    end
end

function Character:CurDeadAni()
    if self:IsRole() and self:IsEmtrace() then
        return SceneObjAnimator.Hug_Dead
    else
        return SceneObjAnimator.Dead
    end
end

function Character:CurDieAni()
    if self:IsRole() and self:IsEmtrace() then
        return SceneObjAnimator.Hug_Die
    else
        return SceneObjAnimator.Die
    end
end

-- 设置大小尺寸
function Character:CheckModleScale(scale)
    self:GetDrawObj():SetScale(scale, scale, scale)
end

-- 2022.06 坐骑特殊处理，在场景循环播放idle by LJH
function Character:IsLockMountAnimInIdle()
    local mount_appeid = self:GetCurRidingResId()
    if mount_appeid <= 0 then
        return true
    end

    if self:IsRole() then
        return NewAppearanceWGData.Instance:GetIsOnlyPlayIdelAniInScene(mount_appeid) or MultiMountWGData.Instance:GetIsOnlyPlayIdelAniInScene(self.vo.obj_id)
    end

    return true
end


-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------   场景UI     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 选中UI 位置
function Character:GetXuanZhongUIPos()
    return Vector3(0, 0, 0)
end

-- 血量百分比
function Character:GetHpPercent()
    local value = 0
    if self.vo ~= nil and self.vo.hp ~= nil and self.vo.max_hp ~= nil and self.vo.max_hp ~= 0 then
        value = self.vo.hp / self.vo.max_hp
    end

    return value
end

-- 气泡
function Character:Say(content, say_time)
    if nil == self.follow_ui then
        return
    end

    self.follow_ui:HideBubble()
    GlobalTimerQuest:CancelQuest(self.say_end_timer)

    self.follow_ui:ChangeBubble(content)
    self.follow_ui:ShowBubble()
    self.say_end_timer = GlobalTimerQuest:AddDelayTimer(function ()
        self.say_end_timer = nil
        self.follow_ui:HideBubble()
    end, say_time)
end

function Character:CreateFollowUi(...)
    SceneObj.CreateFollowUi(self, ...)
    self:SyncShowHp()
end

-- 血量 同步表现血量
function Character:SyncShowHp(is_init, param)
    if nil == self.vo then
        return
    end

    --回血绿字
    if self.show_hp < self.vo.hp and self.show_hp ~= 0 then
        if self:IsMainRole() then
            -- 回血暴击、装备仙戒技能回血  器魂羽翼回血 器魂剑灵回血 有特殊表现 因为FIGHT_TYPE有特殊飘字
            if not (param ~= nil and (param == ATTR_NOTIFY_REASON.ATTR_NOTIFY_REASON_RECOVER_HP
                or param == ATTR_NOTIFY_REASON.ATTR_NOTIFY_REASON_RECOVER_HP_EQUIP_XIANJIE_SKILL
                or param == ATTR_NOTIFY_REASON.ATTR_NOTIFY_REASON_QIHUN_WING_HUIXUE
                or param == ATTR_NOTIFY_REASON.ATTR_NOTIFY_REASON_QIHUN_JIANLING_HUIXUE)) then
                -- local floating_point = self.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)

                local data = {}
                data.fighttype = FIGHT_TYPE.HUIXUE
                data.blood = self.vo.hp - self.show_hp
                HUDManager.Instance:ShowHurtEnter(self, data)
            end
        end
    end

    --防止报错影响原逻辑处理
    local temp_show_hp = self.show_hp
    local temp_vo_hp = self.vo.hp

    self.show_hp = self.vo.hp
    if self.show_hp > 0 then
        if self:IsMainRole() then
            if self:IsDead() then
                self:DoStand()
            end
            if self.old_show_hp and self.old_show_hp <= 0 then
                GlobalTimerQuest:CancelQuest(self.dead_timer)
                self.dead_timer = nil
                self:OnRealive()
            end
        else
            if self.old_show_hp and self.old_show_hp <= 0 then
                self:OnRealive()
            end
        end
    elseif self.dead_timer == nil and self.show_hp <= 0 and not self:IsDead() then
        if self:IsMainRole() then
            self.dead_timer = GlobalTimerQuest:AddDelayTimer(function ()
                if self.show_hp <= 0 and not self:IsDead() then
                    self:DoDead(is_init)
                end
                self.dead_timer = nil
            end, 0.1)
        else
            self:DoDead(is_init)
        end
    end

    if self.vo.max_hp and 0 ~= self.vo.max_hp then
        self:GetFollowUi():SetHpPercent(self.show_hp / self.vo.max_hp,self)
    end

    if self == GuajiCache.target_obj or self == GuajiWGCtrl.Instance:GetCurSelectTargetObj() then
        local scene_type = Scene.Instance:GetSceneType()
        --仙盟争霸定级赛 boss血条客户端做假显示  不走通用逻辑
        if scene_type == SceneType.Guild_Invite then
            local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ACTIVITY_TYPE_GUILD_INVITE)
            local other_cfg = GuildInviteWGData.Instance:GetOtherCfg()
            local monster_id = other_cfg and other_cfg.boss_id or 0
            if act_is_open and monster_id == self.vo.monster_id then
                 self.old_show_hp = self.show_hp
                 return
            end
        end
        GlobalEventSystem:Fire(ObjectEventType.TARGET_HP_CHANGE, self)
    end

    self.old_show_hp = self.show_hp
end

Character.floating_data_list = {}
function Character.GetFloatingData()
    local data = table.remove(Character.floating_data_list)
    if nil == data then
        data = {pos = {}}
    end

    return data
end

function Character.ReleaseFloatingData(data)
    data.deliverer = nil
    table.insert(Character.floating_data_list, data)
end

function Character:PlayFloatingText(data)
    if self:IsDeleted() then
        Character.ReleaseFloatingData(data)
        return
    end

    HUDManager.Instance:ShowHurtEnter(self, data)
    Character.ReleaseFloatingData(data)
end

function Character:ShowFloatingText()
    if #self.floating_texts > 0 then
        local text = table.remove(self.floating_texts, 1)
        self:PlayFloatingText(text)
    end
end

-- 护盾飘字
function Character:OnFightSpecialFloat(float_value)
    -- 飘蓝色数字
    local data = {}
    data.fighttype = FIGHT_TYPE.HPSTORE
    data.blood = float_value
    HUDManager.Instance:ShowHurtEnter(self, data)
end

function Character:PlayFloatTextEffect(fighttype)
    if fighttype == nil then
        return
    end

    if self:IsDeleted() then
        return
    end

    local draw_obj = self:GetDrawObj()
    if draw_obj == nil then
        return
    end

    local point = draw_obj:GetAttachPoint(AttachPoint.Hurt)
    local asset = nil
    local bundle = nil
    if point then
        if fighttype == FIGHT_TYPE.FIGHT_TYPE_BACK or fighttype == FIGHT_TYPE.FIGHT_TYPE_INVISIBLE_ATTACK then
            bundle = "effects2/prefab/mingjiang/10031_prefab"
            asset = "10031_Mingjiang_hit"
        elseif fighttype == FIGHT_TYPE.FIGHT_TYPE_DRUNK_FIRE then
            point = draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
            bundle = "effects2/prefab/mingjiang/10032_prefab"
            asset = "10032_Mingjiang_attack2_hit"            
        end
    end

    if bundle ~= nil and asset ~= nil then
        EffectManager.Instance:PlayControlEffect(self, bundle, asset, nil, nil, point)
    end
end

-- 归属 图标
function Character:SetAscriptionIcon(value)
    self:GetFollowUi():SetAscriptionIcon(value)
end

-- 疲劳 图标
function Character:SetTiredIcon(value)
    self:GetFollowUi():SetTiredIcon(value)
end

-- 仙界装备
function Character:UpdataXianJieEquipImg()

end


-------------------------------------------BuffEffectShieldHandle-----------------------------------------------

BuffEffectShieldHandle = BuffEffectShieldHandle or BaseClass(ReuseableShieldHandle)
function BuffEffectShieldHandle:__init(character_obj)
    self:Init(character_obj)
    self.shield_obj_type = ShieldObjType.BuffEffect
    self.scene_appear_priority = SceneAppearPriority.High
end

function BuffEffectShieldHandle:__delete()
    self:Clear()
end

function BuffEffectShieldHandle:Init(character_obj)
    self.character_obj = character_obj
end

function BuffEffectShieldHandle:Clear()
    self.character_obj = nil
end

function BuffEffectShieldHandle:VisibleChanged(visible)
    if self.character_obj then
        self.character_obj:SetIsDisableBuffEffect(not visible)
    end
end
