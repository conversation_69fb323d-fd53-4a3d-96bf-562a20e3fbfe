require("game/openserver_assist/openserver_assist_wg_data")
require("game/openserver_assist/openserver_assist_view")
require("game/openserver_assist/openserver_assist_luxury_gift_panel")
require("game/openserver_assist/openserver_assist_high_point_panel")
require("game/openserver_assist/openserver_assist_gudao_jizhan_panel")

-- 开服助力
OpenServerAssistWGCtrl = OpenServerAssistWGCtrl or BaseClass(BaseWGCtrl)

function OpenServerAssistWGCtrl:__init()
	if nil ~= OpenServerAssistWGCtrl.Instance then
		ErrorLog("[OpenServerAssistWGCtrl] attempt to create singleton twice!")
		return
	end
	OpenServerAssistWGCtrl.Instance = self

    self.data = OpenServerAssistWGData.New()
	self.view = OpenServerAssistView.New(GuideModuleName.OpenServerAssistView)

	self:RegisterAllProtocols()
	-- 活动监听
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

	-- 等级监听
	self.role_data_change_callback = BindTool.Bind(self.RoleDataChangeCallback, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"level"})
end

function OpenServerAssistWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	if self.role_data_change_callback then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
		self.role_data_change_callback = nil
	end

	OpenServerAssistWGCtrl.Instance = nil
end

-- 注册协议
function OpenServerAssistWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRALoginLuxuryGiftInfo, "OnSCRALoginLuxuryGiftInfo")
	self:RegisterProtocol(SCRAHaiDianInfo, "OnSCRAHaiDianInfo")
end

-- 开服助力入口显示控制
function OpenServerAssistWGCtrl:ShowOrHideOpenActvityIcon()
	local status = ACTIVITY_STATUS.CLOSE
    local act_list = self.data:GetServerAssistActivityList()
	for _, v in ipairs (act_list) do
        local can_show = self.data:GetRandActivityCanShowByType(v.activity_type)
		if can_show then
			status = ACTIVITY_STATUS.OPEN
			break
		end
	end

	-- print_error("-----开服助力入口显示控制-----", ACTIVITY_TYPE.OPEN_SERVER_ASSIST, status)
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER_ASSIST, status)
end

-- 开服助力活动 符合要求  请求信息
function OpenServerAssistWGCtrl:RequestAllCanShowActivityInfo()
    local act_list = self.data:GetServerAssistActivityList()
	for _, v in ipairs (act_list) do
        local can_show = self.data:GetRandActivityCanShowByType(v.activity_type)
		if can_show then
			self:RequestActivityInfo(v.activity_type)
		end
	end
end

function OpenServerAssistWGCtrl:RequestActivityInfo(activity_type)
	if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOGIN_LUXURY_GIFT or
		activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT then
		self:SendRandActivityRequest(activity_type, LOGIN_LUXURY_GIFT_OP.ALL_INFO)
	end
end

-- 随机活动请求
function OpenServerAssistWGCtrl:SendRandActivityRequest(activity_type, opera_type, param1, param2, param3)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = activity_type or 0
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param1 or 0
 	protocol.param_2 = param2 or 0
 	protocol.param_3 = param3 or 0
 	protocol:EncodeAndSend()
end

function OpenServerAssistWGCtrl:OnSCRALoginLuxuryGiftInfo(protocol)
	self.data:SetLuxuryGiftInfo(protocol)
	RemindManager.Instance:Fire(RemindName.OSA_Luxury_Gift)
	self.view:Flush(TabIndex.luxury_gift)
end

function OpenServerAssistWGCtrl:OnSCRAHaiDianInfo(protocol)
	self.data:SetHighPointInfo(protocol)
	RemindManager.Instance:Fire(RemindName.OSA_High_Point)
	ViewManager.Instance:FlushView(GuideModuleName.ServerActivityTabView, TabIndex.act_highpoint)
	ServerActivityWGCtrl.Instance:FlushServerHighPointRewardView()
	self.view:Flush(TabIndex.high_point)
end

function OpenServerAssistWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	local is_openserver_act = self.data:IsServerAssistActivity(activity_type)
	if is_openserver_act then
		self:ShowOrHideOpenActvityIcon()
		self.view:ActivityChangeFlushView()
		if status == ACTIVITY_STATUS.OPEN and activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN then
			self.data:SetGuDaoClickStatus(false)
		end
	end
end

function OpenServerAssistWGCtrl:RoleDataChangeCallback(attr_name, value, old_value)
	if attr_name == "level" then
		self:ShowOrHideOpenActvityIcon()
		self.view:ActivityChangeFlushView()
		self:RequestAllCanShowActivityInfo()
	end
end
