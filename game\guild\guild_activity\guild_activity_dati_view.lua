GuildView = GuildView or BaseClass()

function GuildView:InitGuildActDatiView()
	if self.node_list.dati_tip_btn then
		XUI.AddClickEventListener(self.node_list["dati_tip_btn"], BindTool.Bind(self.OnClickGuildActDatiTipBtn, self))
	end

	if self.node_list.dati_reward_list then
		self.dati_reward_list = AsyncListView.New(ItemCell, self.node_list.dati_reward_list)
	end

	if self.node_list.dati_goto_btn then
		XUI.AddClickEventListener(self.node_list["dati_goto_btn"], BindTool.Bind(self.GuildActDaTiGoFunc, self))
	end
end

function GuildView:DeleteGuildActDatiView()
	if self.dati_reward_list then
		self.dati_reward_list:DeleteMe()
		self.dati_reward_list = nil
	end
end

function GuildView:ShowGuildActDatiCallBack()

end

function GuildView:OnFlushGuildActDatiView(param_t, index)
	local data = GuildActivityWGData.Instance:GetActDataByType(GuildActivityWGData.Act_Type.DaTi)
	if not data or not data.cfg then
		return 
	end
	local str = string.format("%s %s-%s", data.act_hall_cfg.open_tips, data.act_hall_cfg.open_time, data.act_hall_cfg.close_time)
	str = ToColorStr(str, COLOR3B.D_GREEN)
	self.node_list.dati_kaiqi_label.text.text = string.format(Language.GuildBoss.OpenTimeStr, str)
	local reward_list = data.reward_list
	self.dati_reward_list:SetDataList(reward_list)
	local act_is_open = false
	act_is_open = ActivityWGData.Instance:GetActivityIsOpen(data.cfg.act_id)
	self.node_list.dati_remind:SetActive(act_is_open)
end

function GuildView:OnClickGuildActDatiTipBtn()
	local data = GuildActivityWGData.Instance:GetActDataByType(GuildActivityWGData.Act_Type.DaTi)
	if not data or not data.cfg then
		return 
	end
	local cfg = data.cfg
	local tips_content = cfg.act_rule
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.Activity.HuoDongShuoMing)
	role_tip:SetContent(tips_content)
end

function GuildView:GuildActDaTiGoFunc()
	if RoleWGData.Instance.role_vo.guild_id == 0 then
		GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist)
	else
		FunOpen.Instance:OpenViewNameByCfg("guildanswer")
	end
end

