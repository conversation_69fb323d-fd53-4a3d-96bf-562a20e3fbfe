TianShenWGData = TianShenWGData or BaseClass()

TIANSHEN_CAO_MAX = 8 -- 最大槽数
TIANSHEN_STAR_MAX = 9 -- 最大星数
TIANSHEN_MAX = 20 -- 所有天神数量
TIANSHEN_MAX_EQUIP = 4 -- 天神最大装备数
TIANSHEN_EQUIP_JIE_MAX = 11 --天神装备最大阶数
TIANSHEN_PASSSKILL_COUNT = 7
TIANSHEN_SHENSHI_REFINE_MA = 15	-- 天神神格洗练

FROM_TIANSHEN_UPGRADE = 1
FROM_TIANSHEN_SHENSHI = 2
FROM_TIANSHEN_SHENQI = 4
FROM_NORMAL_VIEW = 3

TIANSHEN_SHENQI_STAR_MAX = 10 -- 天神神器最大星数

TianShenWGData.Equip_Pos = {
	[GameEnum.EQUIP_TYPE_TIANSHEN0] = 0,						-- 天神神戒
	[GameEnum.EQUIP_TYPE_TIANSHEN1] = 1,						-- 天神神镯
	[GameEnum.EQUIP_TYPE_TIANSHEN2] = 2,						-- 天神神符
	[GameEnum.EQUIP_TYPE_TIANSHEN3] = 3						-- 天神神坠
}

-- 升星属性面板固定需要显示的属性
TianShenWGData.FixedAttr = {
	gongji = 1,
	shengming_max = 1,
	fangyu = 1,
	pojia = 1,
	tianshen_att_per = 1,
}

-- 继承镶嵌孔index
TianShenWGData.SLOT_INDEX = {
	NONE = 0,
	LEFT = 1,
	RIGHT = 2,
}

--神饰抽奖类型
BAOXIA_SEND_GET_AWARD_TYPE = {
	One = 1,			--一次抽奖1条
	Two = 2,			--一次抽奖2条
	N_Max = 10,			--一次抽奖10条,最大上限(原先为50)
}

BX_Box_Tip_Show_Type = {
	Cur = 1,	--显示 当前宝匣Tip
	Next = 2,	--显示 下一阶宝匣Tip
}

TianShenWGData.TianShenQualityImg = {
	[1] = "a3_ty_wpk_3",
	[2] = "a3_ty_wpk_4",
	[3] = "a3_ty_wpk_5",
	[4] = "a3_ty_wpk_6",
	[5] = "a3_ty_wpk_7",
	-- [1] = "a2_xl_zhiping",
	-- [2] = "a2_xl_cheng",
	-- [3] = "a2_xl_hongping",
	-- [4] = "a2_xl_fense",
	-- [5] = "a2_xl_jingping",
}

TianShenWGData.TianShenBGcolor = {
	[1] = "a3_ty_wpk_3",
	[2] = "a3_ty_wpk_4",
	[3] = "a3_ty_wpk_5",
	[4] = "a3_ty_wpk_6",
	[5] = "a3_ty_wpk_7",
	-- [6] = "a2_ty_wpk_8",  			-- 策划那边暂时没有配置，有配置再打开
}

local shenqi_shuxing_jc_per = 28 -- 神器基础属性加成
function TianShenWGData:__init()
	if TianShenWGData.Instance ~= nil then
		error("[TianShenWGData] attempt to create singleton twice!")
		return
	end

	TianShenWGData.Instance = self
	self.tianshen_info = {level = 0, revert_us = 0, level_exp = 0, jinghua = 0, shuxingdan_use_count = {}, passskill_level = {}, tianshen_heji_skill_flag = {}, tianshen_heji_use_skill_list = {}, tianshen_jiban_flag = {}, active_image_flag = {}, upgrade_list = {}, tianshen_chuzhan_slot_active_flags = {}}
	self.old_tianshen_iinfo = {level = 0, revert_us = 0, level_exp = 0, jinghua = 0, shuxingdan_use_count = {}, passskill_level = {}, tianshen_heji_skill_flag = {}, tianshen_heji_use_skill_list = {}, tianshen_jiban_flag = {}, active_image_flag = {}, upgrade_list = {}, tianshen_chuzhan_slot_active_flags = {}}
	self.tianshen_bag_info = {}
	self.tianshen_shenqi_info = {}
	self.tianshen_shenqi_old_info = {}
	self.tianshen_skill = {}
	self.series_magic_list = {}
	self.max_special_skill_cfg_cache = {} 											-- 所需激活等级最大的技能配置缓存
	self.tsbox_reward_tip_info_list = {} 											-- 天神宝匣奖励信息缓存
	self.sort_func = BindTool.Bind(self.SortFunc, self)
	self.sort_bag_func = BindTool.Bind(self.SortBag, self)
	self.shenshi_series = 1
	self.select_shenshi_index = 1
	self.tianshen_jiban_info = nil
	self.had_click_shenshi_stronger = false

	self.tianshen_cfg_auto = ConfigManager.Instance:GetAutoConfig("tianshen_cfg_auto")
	self.magic_image_cfg = ListToMap(self.tianshen_cfg_auto.magic_image, "index")
	self.magic_image_id_cfg = ListToMap(self.tianshen_cfg_auto.magic_image, "appe_image_id")

	self.dan_limit_cfg = ListToMapList(self.tianshen_cfg_auto.dan_limit, "dan_index")
	self.magic_image_series_cfg = ListToMapList(self.tianshen_cfg_auto.magic_image, "series")
	self.magic_image_jiban_cfg = ListToMapList(self.tianshen_cfg_auto.magic_image, "jiban_skill")

	self.special_image_pasv_skill = ListToMap(self.tianshen_cfg_auto.special_image_pasv_skill, "image_id", "skill_level", "skill_id")

	self.special_image_upgrade_cfg = ListToMap(self.tianshen_cfg_auto.upgrade, "index", "grade")
	self.dan_item_limit = ListToMap(self.tianshen_cfg_auto.dan_item_limit, "dan_index_1")

	self.special_image_star_cfg = ListToMap(self.tianshen_cfg_auto.star, "index", "star")--, "cao")
	self.act_item_cfg = ListToMap(self.tianshen_cfg_auto.item, "index")
	self.tianshen_act_item_cfg = ListToMap(self.tianshen_cfg_auto.item, "act_item_id")
	--print_error("self.tianshen_act_item_cfg = ", self.tianshen_act_item_cfg)
	self.tianshen_upstar_item_cfg = ListToMap(self.tianshen_cfg_auto.item, "star_item_id")
	self.tianshen_jiban_cfg = ListToMap(self.tianshen_cfg_auto.jiban_des, "jiban_id")
	if self.special_image_star_cfg[0] then
		TIANSHEN_STAR_MAX = #self.special_image_star_cfg[0]
	end

	--神通相关
	self.m_shentong_cfg = ListToMapList(self.tianshen_cfg_auto.shentong_cfg, "skill_type")
	self.m_shentong_cfgs = self.tianshen_cfg_auto.shentong_cfg
	self.m_shentong_id_cfg = ListToMap(self.tianshen_cfg_auto.shentong_cfg, "skill_id")
	self.m_shentong_actid_cfg = ListToMap(self.tianshen_cfg_auto.shentong_cfg, "active_item_id")
	self.m_shentong_skill_cfg = ListToMap(self.tianshen_cfg_auto.shentong_skill, "skill_id", "skill_level")
	-- 神饰
	-- self.equip_basics_attr = ListToMap(self.tianshen_cfg_auto.equip_basics_attr, "shenshi_id", "star")
	-- self.shenshi_suit_cfg = ListToMap(self.tianshen_cfg_auto.shenshi_suit, "index", "colour", "part_star")
	-- self.shenshi_equip_strange_cfg = ListToMap(self.tianshen_cfg_auto.equip_strange, "level")
	-- self.equip_strength_attr_cfg = ListToMap(self.tianshen_cfg_auto.equip_strength_attr, "sub_type", "strength_level")
	-- self.equip_upgrade_attr_cfg = ListToMap(self.tianshen_cfg_auto.equip_upgrade_attr, "sub_type", "color", "star_level", "upgrade_level")
	-- self.equip_fenjie_cfg = ListToMap(self.tianshen_cfg_auto.equip_fenjie, "color", "star")
	-- self.equip_upgrade_acura_attr_cfg = ListToMap(self.tianshen_cfg_auto.equip_upgrade_acura_attr, "sub_type", "color", "star_level", "upgrade_level")

	-- self.equip_random_attr_cfg = ListToMap(self.tianshen_cfg_auto.equip_random_attr, "sub_type", "star_level", "color", "attr_id")

	self.special_skill_level_cfg = ListToMap(self.tianshen_cfg_auto.special_skill_level, "skill_id", "skill_level")
	--self.equip_star_attr_cfg = ListToMap(self.tianshen_cfg_auto.equip_star_attr, "color")

	self.tianshen_shenqi_cfg_auto = ConfigManager.Instance:GetAutoConfig("tianshen_shenqi_cfg_auto")
	self.shenqi_cfg = ListToMapList(self.tianshen_shenqi_cfg_auto.shenqi, "series")
	self.shenqi_cfg2 = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi, "index")
	self.shenqi_cfg3 = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi, "tianshen_index")
	self.shenqi_cfg4 = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi, "stuff_id")
	self.shenqi_strength_cfg = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi_strength, "index", "level")
	self.shenqi_star_cfg = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi_star, "index", "star_lv")
	self.shenqi_spirit_cfg = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi_spirit, "index")
	self.shenqi_fenjie_cfg = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi_fenjie, "item_id")
	self.shenqi_waiguan_cfg = ListToMapList(self.tianshen_shenqi_cfg_auto.shenqi_waiguan, "shenqi_index")
	self.shenqi_waiguan_cfg2 = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi_waiguan, "waiguan_id")
	self.shenqi_waiguan_star = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi_waiguan_star, "waiguan_id", "star_lv")
	self.shenqi_skill_cfg = ListToMap(self.tianshen_shenqi_cfg_auto.shenqi_skill,"skill","skill_level")
	self.attr_table_name_cfg = ListToMap(self.tianshen_shenqi_cfg_auto.attr_table_name, "index")
	self.tianshen_union_skill_cfg =  ListToMap(self.tianshen_cfg_auto.tianshen_union_skill, "skill_id") --skill_id是索引
	self.tianshen_union_skill_item_cfg = ListToMap(self.tianshen_cfg_auto.tianshen_union_skill, "heji_item_id")
	self.tianshen_heji_skill_cfg = ListToMap(self.tianshen_cfg_auto.tianshen_union_skill, "tianshen_heji_skill_id")

	------------------------------------------------F2神饰-----------------------------
	self.shenshi_base_attr = ListToMap(self.tianshen_cfg_auto.shenshi_base_attr,"shenshi_id")
	self.shenshi_equip_condition = ListToMap(self.tianshen_cfg_auto.shenshi_base_attr,"part","color_limit")
	self.shenshi_compose_id_table = ListToMap(self.tianshen_cfg_auto.shenshi_com,"item_id")
	self.tianshen_rank_name_tab = ListToMap(self.tianshen_cfg_auto.shenshi_jinsheng,"index","level")
	self.tianshen_star_skill = ListToMap(self.tianshen_cfg_auto.star_skill,"index","star_level")
	self.shenshi_refine_cfg = ListToMapList(self.tianshen_cfg_auto.shenshi_refine, "index")
	-------------------------------------------------------------------------------------

	-------------------------天神宝匣 神格抽奖 start ------------------------------------------------------------

	-- 星命之门的表不用了 改成了神格抽奖
	self.godhood_draw_cfg = ConfigManager.Instance:GetAutoConfig("godhood_draw_cfg_auto")
	self.godhood_draw_other_cfg = ConfigManager.Instance:GetAutoConfig("godhood_draw_cfg_auto").other
	self.godhood_draw_show_reward_cfg = ListToMap(self.godhood_draw_cfg.show_reward, "grade") --展示奖励
	self.godhood_draw_grade_cfg = ListToMap(self.godhood_draw_cfg.grade, "grade") --档次
	self.godhood_draw_prop_cfg = ListToMapList(self.godhood_draw_cfg.item_random_desc, "grade") --抽奖概率
	
	self.is_can_uplevel = 0 --是否可以升品.
	self.ts_box_total_draw_count = 0 --总抽奖次数
	self.ts_box_draw_key_count = 0  --剩余抽奖材料数量
	self.ts_box_old_level = nil
	self.ts_box_is_max_level = nil


	self.godhood_draw_grade = 1
	self.godhood_draw_times = 0
	self.godhood_draw_lucky = 0
	self.godhood_score = 0
	self.godhood_draw_score = 0  --剩余抽奖材料数量
	self.times_reward_seq = 0

	-------------------------天神宝匣 神格抽奖 end ------------------------------------------------------------

	-------------------------五行属性----------------------------
	local wuxing_cfg_auto = ConfigManager.Instance:GetAutoConfig("wuxing_attr_auto")
	self.tianshen_wuxing_cfg = ListToMap(wuxing_cfg_auto.tianshen_wuxing,"image_id")
	self.monster_wuxing_cfg = ListToMap(wuxing_cfg_auto.monster_wuxing,"monster_id")
	-------------------------五行end-----------------------------

	local virtual_item_id = self.tianshen_cfg_auto.other[1].virtual_item_id
	self.virtual_item_id = Split(virtual_item_id, "|")
	for i=1,#self.virtual_item_id do
		self.virtual_item_id[i] = tonumber(self.virtual_item_id[i])
	end

	self.need_hecheng_btn = StrToTable(self.tianshen_cfg_auto.other[1].need_hecheng_btn)

	-- 红点注册
	local remindmanager_instance = RemindManager.Instance
	remindmanager_instance:Register(RemindName.TianShen_ST, BindTool.Bind(self.GetSTAllRemind,self))
	remindmanager_instance:Register(RemindName.TianShen_Huanhua, BindTool.Bind(self.SpecialTianshenRemind,self))
	remindmanager_instance:Register(RemindName.TianShen_ShenShi, BindTool.Bind(self.TianShenShenShiRemind, self))
	remindmanager_instance:Register(RemindName.TianShen_ShenQi, BindTool.Bind(self.TianShenShenQiRemind, self))
	remindmanager_instance:Register(RemindName.TianShen_Battle, BindTool.Bind(self.GetTianShenBattleRemind, self))
	remindmanager_instance:Register(RemindName.TianShenHeJi, BindTool.Bind(self.GetTianShenHaJiRemind, self))

	--天神宝匣红点
	remindmanager_instance:Register(RemindName.TianShenBaoXia, BindTool.Bind(self.CheckTianShenBoxRemind, self))
	--remindmanager_instance:Register(RemindName.ShenShiQiangHua, BindTool.Bind(self.ShenShiQiangHuaRemind, self)) 			-- 神饰强化
	--remindmanager_instance:Register(RemindName.ShenShiTaoZhuang, BindTool.Bind(self.ShenShiTaoZhuangRemind, self)) 			-- 神饰套装

	-- 红点回调绑定
	--[[self.tianshen_battle_remind_callback = BindTool.Bind(self.TianShenBattleRemindCallBack, self) 							-- 天神出战
	remindmanager_instance:Bind(self.tianshen_battle_remind_callback, RemindName.TianShen_Battle)]]

	self:RegisterActivationRemindInBag(RemindName.TianShen_Huanhua)
	--self:RegisterShenShiRemindInBag(RemindName.TianShen_ShenShi)
	self:RegisterShenQiRemindInBag(RemindName.TianShen_ShenQi)
	self:RegisterSTRemindInBag(RemindName.TianShen_ST)

	if nil == self.item_data_event then
		self.item_data_event = BindTool.Bind1(self.OnItemDataChange,self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	end
	
	if nil == self.role_level_change_event then
		self.role_level_change_event = BindTool.Bind1(self.OnRoleLevelChange,self)  
		RoleWGData.Instance:NotifyAttrChange(self.role_level_change_event, {"level"})
	end

	self:InitJiBanSkill()
	self.multi_skill_group = {}
	self.multi_skill_dic = {}
	self.normal_skill_group = {}
	self.normal_skill_dic = {}
	self.tianshen_info_data_list = {}
	self.tianshen_shenqi_data_list = {}

	for k,v in pairs(self.tianshen_cfg_auto.magic_image) do
		local index = 1
		if v.is_multi_wuxing ~= nil and v.is_multi_wuxing ~= "" and v.is_multi_wuxing == 1 then
			self.normal_skill_group[v.appe_image_id] = {}
			local data = self.normal_skill_group[v.appe_image_id]
			local multi_skill_data = Split(v.multi_normal_skill,",",true)
			for key, value in pairs(multi_skill_data) do
				data[index] = {}
				local part_skill_cfg = Split(value, "|", true)
				for k1, v1 in pairs(part_skill_cfg) do
					local skill_id = tonumber(v1)
					
					if skill_id then
						data[index][k1] = skill_id
						self.normal_skill_dic[skill_id] = index
					end
				end
				index = index + 1
			end			
		else
			self.normal_skill_group[v.appe_image_id] = {}
			local noraml_list = Split(v.normal_skill,"|", false)
			local normal_skill_list = {}
			for i = 1, #noraml_list do
				normal_skill_list[i] = tonumber(noraml_list[i])
			end

			self.normal_skill_group[v.appe_image_id][index] = normal_skill_list
			for k1, v1 in pairs(noraml_list) do
				self.normal_skill_dic[tonumber(v1)] = index
			end
		end

		local skill_index = 1
		if v.is_multi_wuxing ~= nil and v.is_multi_wuxing ~= "" and v.is_multi_wuxing == 1 then
			self.multi_skill_group[v.appe_image_id] = {}
			local data = self.multi_skill_group[v.appe_image_id]
			local multi_skill_data = Split(v.multi_skill,",", false)
			for key, value in pairs(multi_skill_data) do
				data[skill_index] = {}
				local part_skill_cfg = Split(value, "|", false)
				for k1, v1 in pairs(part_skill_cfg) do
					local skill_id = tonumber(v1)
					if skill_id then
						data[skill_index][k1] = skill_id
						self.multi_skill_dic[skill_id] = skill_index
					end
				end
				skill_index = skill_index + 1
			end			
		else
			self.multi_skill_group[v.appe_image_id] = {}
			local noraml_list = Split(v.skill,"|", false)
			local normal_skill_list = {}
			for i = 1, #noraml_list do
				normal_skill_list[i] = tonumber(noraml_list[i])
			end

			self.multi_skill_group[v.appe_image_id][skill_index] = normal_skill_list
			for k1, v1 in pairs(noraml_list) do
				self.multi_skill_dic[tonumber(v1)] = skill_index
			end
		end	
	end

	self.activation_select_ts_index = -1
	self.shenshi_select_ts_index = -1
	self.shenqi_select_ts_index = -1
	self.ts_info_select_index = -1
	self:SplitTianShenSkillLvStar()
end

function TianShenWGData:__delete()
	TianShenWGData.Instance = nil
	self.tianshen_info = nil
	self.old_tianshen_iinfo = nil
	self.tianshen_bag_info = nil
	self.series_magic_list = nil
	self.sort_func = nil
	self.sort_bag_func = nil
	self.tianshen_skill = nil
	self.tianshen_shenqi_info = nil
	self.tianshen_shenqi_old_info = nil
	self.max_special_skill_cfg_cache = nil
	self.tsbox_reward_tip_info_list = nil
	self:RemoveTimeQuest()
	self:CancleShenShiDelay()
	local remindmanager_instance = RemindManager.Instance
	remindmanager_instance:UnRegister(RemindName.TianShen_ST)
	remindmanager_instance:UnRegister(RemindName.TianShen_Huanhua)
	remindmanager_instance:UnRegister(RemindName.TianShen_ShenShi)
	remindmanager_instance:UnRegister(RemindName.TianShen_ShenQi)
	remindmanager_instance:UnRegister(RemindName.TianShen_Battle)
	remindmanager_instance:UnRegister(RemindName.TianShenHeJi)

	--RemindManager.Instance:UnBind(self.tianshen_battle_remind_callback)
	RemindManager.Instance:UnBind(self.shenshi_qianghua_remind_callback)
	RemindManager.Instance:UnBind(self.shenshi_taozhuang_remind_callback)
	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	if self.role_level_change_event then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_level_change_event)
		self.role_level_change_event = nil
	end
	self.already_recacular = nil
	self.ts_union_page_cfg = nil
	self.ts_union_skill_info = nil
	self.ts_union_old_skill_info = nil
	self.tianshen_info_data_list = nil
	self.tianshen_shenqi_data_list = nil
	self.image_list = nil
	self.tianshen_skill_lv_star = nil
end

function TianShenWGData:GetTianShenOtherCfg()
	return self.tianshen_cfg_auto.other[1]
end

function TianShenWGData:RegisterActivationRemindInBag(...)
    local map = {}

    for k,v in pairs(self.tianshen_cfg_auto.dan_item_limit) do
		map[v.item_id] = true
	end

	for k,v in pairs(self.tianshen_cfg_auto.special_image_pasv_skill) do
		map[v.uplevel_item_id] = true
	end

	for k,v in pairs(self.tianshen_cfg_auto.item) do
		map[v.act_item_id] = true
		map[v.cao_item_id] = true
		map[v.star_item_id] = true
		map[v.upgrade_item_id1] = true
		map[v.upgrade_item_id2] = true
		map[v.upgrade_item_id3] = true
	end

	for k,v in pairs(self.tianshen_cfg_auto.special_skill_level) do
		map[v.item_id] = true
	end

	for k,v in pairs(self.tianshen_cfg_auto.zhan_sit) do
		map[v.item_id] = true
	end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    local remind_name_list = {...}
	for _, remind_name in pairs(remind_name_list) do
		BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
	end
end

function TianShenWGData:RegisterShenQiRemindInBag(remind_name)
    local map = {}

    for k,v in pairs(self.tianshen_shenqi_cfg_auto.shenqi) do
    	map[v.stuff_id] = true
    end

    for k,v in pairs(self.tianshen_shenqi_cfg_auto.shenqi_star) do
    	map[v.stuff_id] = true
    end

    for k,v in pairs(self.tianshen_shenqi_cfg_auto.shenqi_strength) do
    	map[v.strength_stuff_id] = true
    end

    for k,v in pairs(self.tianshen_shenqi_cfg_auto.shenqi_spirit) do
    	map[v.stuff_id] = true
    end

    for k,v in pairs(self.tianshen_shenqi_cfg_auto.shenqi_waiguan) do
    	map[v.stuff_id] = true
    end

    for k,v in pairs(self.tianshen_shenqi_cfg_auto.shenqi_waiguan_star) do
    	map[v.stuff_id] = true
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function TianShenWGData:RegisterSTRemindInBag(remind_name)
    local map = {}

    for k,v in pairs(self.tianshen_cfg_auto.shentong_skill) do
    	map[v.uplevel_item_id] = true
    end

    for k,v in pairs(self.tianshen_cfg_auto.shentong_cfg) do
    	map[v.active_item_id] = true
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function TianShenWGData:GetTianShenAttrNum(color)
	-- if self.equip_star_attr_cfg[color] then
	-- 	return self.equip_star_attr_cfg[color].rand_attr_num
	-- end
	return 0
end

function TianShenWGData:TianShenShenQiOldInfo(value)
	if nil == value then
		return self.tianshen_shenqi_old_info
	end
	self.tianshen_shenqi_old_info = __TableCopy(value)
end

function TianShenWGData:GetTianShenShenQiOldInfoByIndex(index)
	return self.tianshen_shenqi_old_info[index]
end

function TianShenWGData:TianShenShenQiInfo(value)
	if nil == value then
		return self.tianshen_shenqi_info
	end
	self.tianshen_shenqi_info = value
	local count = 0
	for k,v in pairs(self.tianshen_shenqi_info) do
		v.total_zhanli = self:GetShenQiZhanLi(k)
	end

	self:TianShenShenQiOldInfo(self.tianshen_shenqi_info)
end

function TianShenWGData:SetTianShenShenQiInfoOne(value)
	self:SetOldTianShenShenQiInfoOne(value.shenqi_id)
	self.tianshen_shenqi_info[value.shenqi_id] = value
	value.total_zhanli = self:GetShenQiZhanLi(value.shenqi_id)
end

function TianShenWGData:SetOldTianShenShenQiInfoOne(shenqi_id)
	self.tianshen_shenqi_old_info[shenqi_id] = self.tianshen_shenqi_info[shenqi_id]
end

function TianShenWGData:GetTianShenShenQiInfo(index)
	if self.tianshen_shenqi_info ~= nil then
		return self.tianshen_shenqi_info[index]
	end

	return nil
end

function TianShenWGData:GetShenQiListInfo()
	return self.tianshen_shenqi_info
end

function TianShenWGData:TianShenSkill(value)
	if nil == value then
		return self.tianshen_skill
	end

	local skill_list = value.skill_list
	for i=1,#skill_list do
		self.tianshen_skill[skill_list[i].index] = skill_list[i].skill_info_list
	end
end

function TianShenWGData:GetTianShensMainkill(index, skill_id)
	skill_id = tonumber(skill_id)
	local skill_list = self.tianshen_skill[index]
	if not skill_list then return nil end

	for k,v in pairs(skill_list) do
		if v.id == skill_id then
			return v
		end
	end
	return nil
end

function TianShenWGData:TianshenBagInfo(value)
	-- if nil == value then
	-- 	return self.tianshen_bag_info
	-- end
	-- self.tianshen_bag_info = value
	-- self.is_flush_ts_back_packinfo = true
	-- for k,v in pairs(self.tianshen_bag_info.bag_item_list) do
	-- 	v.param = CommonStruct.ItemParamData() --物品上架需要星级做处理
 -- 		v.index = v.bag_index
 -- 		v.num = 1
 -- 		v.param.star_level = v.star_level
	-- 	-- self:SetPingFen(v)
	-- end
	-- self:SortBag()
end

function TianShenWGData:SetTianshenOneBagInfo(value)
	-- local has_item = false
	-- for k,v in pairs(self.tianshen_bag_info.bag_item_list) do
	-- 	if value.bag_item.bag_index == v.bag_index then
	-- 		value.bag_item.param = CommonStruct.ItemParamData()--物品上架需要星级做处理
	--  		value.bag_item.index = v.bag_index
	--  		value.bag_item.num = 1
	--  		value.bag_item.param.star_level = value.bag_item.star_level or 0
	-- 		self.tianshen_bag_info.bag_item_list[k] = value.bag_item
	-- 		has_item = true
	-- 		break
	-- 	end
	-- end
	-- if not has_item then
	-- 	value.bag_item.param = CommonStruct.ItemParamData()--物品上架需要星级做处理
 -- 		value.bag_item.index = value.bag_index
 -- 		value.bag_item.num = 1
 -- 		value.bag_item.param.star_level = value.bag_item.star_level or 0
	-- 	table.insert(self.tianshen_bag_info.bag_item_list, value.bag_item)
	-- end
	-- self.is_flush_ts_back_packinfo = true
	-- -- self:SetPingFen(value.bag_item)

	-- -- self:SortBag()
	-- self:OneBagToFlush()
end

function TianShenWGData:GetBagInfoByIndex(bag_index)
	if not self.tianshen_bag_info then return nil end

	for k,v in pairs(self.tianshen_bag_info.bag_item_list) do
		if bag_index == v.bag_index then
			return v
		end
	end

	return nil
end

-- 返回神兽背包信息
function TianShenWGData:GetTSBackpackInfo()
	if self.is_flush_ts_back_packinfo then
		if nil == self.tianshen_bag_info then
			return {}
		end

		local grid_list = {}
		for k,v in pairs(self.tianshen_bag_info.bag_item_list) do
			if 0 == v.stren_level then
				local vo = {}
				vo.index = v.bag_index
				vo.item_id = v.item_id
				vo.stren_level = v.stren_level
				vo.grade_level = v.grade_level
				vo.star_count = v.star_level
				vo.is_bind = v.bind
				vo.xianpin_list = v.xianpin_list
				local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
				if item_cfg then
					vo.quality = item_cfg.color
				end
				grid_list[#grid_list + 1] = vo
			end
		end
		self.shen_shou_back_packinfo = grid_list
		self.is_flush_ts_back_packinfo = false
	end
	return self.shen_shou_back_packinfo
end

-- 筛选装备
function TianShenWGData:FilterTSEq(quality, star)
	local list = {}
	local i = 1
	local bag_cfg = self:GetTSBackpackInfo()
	if star == 0 then
		if quality == -1 then
			table.sort(bag_cfg, self:SortList("quality", "star_count"))
			return bag_cfg
		else
			for k,v in ipairs(bag_cfg) do
				if quality == v.quality then
					list[i] = v
					i = i + 1
				end
			end
			table.sort(list, self:SortList("quality", "star_count"))
			return list
		end
	else
		if quality == -1 then
			for k,v in ipairs(bag_cfg) do
				if star == v.star_count then
					list[i] = v
					i = i + 1
				end
			end
			table.sort(list, self:SortList("quality", "star_count"))
			return list
		else
			for k,v in ipairs(bag_cfg) do
				if quality == v.quality and star == v.star_count then
					list[i] = v
					i = i + 1
				end
			end
			table.sort(list, self:SortList("quality", "star_count"))
			return list
		end
	end
end

function TianShenWGData:SortList(sort_key_name1, sort_key_name2, sort_key_name3,slot_index)
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		if a[sort_key_name1] > b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] < b[sort_key_name1] then
			order_b = order_b + 10000
		end

		if nil == sort_key_name2 then  return order_a > order_b end

		if a[sort_key_name2] > b[sort_key_name2] then
			order_a = order_a + 1000
		elseif a[sort_key_name2] < b[sort_key_name2] then
			order_b = order_b + 1000
		end

		if nil == sort_key_name3 then  return order_a > order_b end

		if a[sort_key_name3] > b[sort_key_name3] then
			order_a = order_a + 100
		elseif a[sort_key_name3] < b[sort_key_name3] then
			order_b = order_b + 100
		end

		if nil == slot_index then  return order_a > order_b end

		if a[slot_index] < b[slot_index] then
			order_a = order_a + 10
		elseif a[slot_index] > b[slot_index] then
			order_b = order_b + 10
		end

		return order_a > order_b
	end
end

function TianShenWGData:SortFunc(info1, info2)
	local item_cfg1 = ItemWGData.Instance:GetItemConfig(info1.item_id)
	local item_cfg2 = ItemWGData.Instance:GetItemConfig(info2.item_id)

	if item_cfg1 and item_cfg2 and item_cfg1.color ~= item_cfg2.color then
		return item_cfg1.color > item_cfg2.color
	elseif item_cfg1 and item_cfg2 and item_cfg1.sub_type ~= item_cfg2.sub_type then
		return item_cfg1.sub_type < item_cfg2.sub_type
	elseif info1.grade_level ~= info2.grade_level then
		return info1.grade_level > info2.grade_level
	elseif info1.stren_level ~= info2.stren_level then
		return info1.stren_level > info2.stren_level
	elseif info1.pingfen ~= info2.pingfen then
		return info1.pingfen > info2.pingfen
	end
	return false
end

function TianShenWGData:RemoveTimeQuest()
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end
end

-- 单个背包数据会返回多条，这里做下延时
function TianShenWGData:OneBagToFlush()
	self:RemoveTimeQuest()
	self.timer_quest = GlobalTimerQuest:AddDelayTimer(self.sort_bag_func, 0.04)
end

function TianShenWGData:SortBag()
	-- 清理掉item_id = 0的格子
	local list = {}
	local index = 1
	for k,v in pairs(self.tianshen_bag_info.bag_item_list) do
		if 0 < v.item_id then
			list[index] = v
			index = index + 1
		end
	end
	self.tianshen_bag_info.bag_item_list = list
	table.sort(self.tianshen_bag_info.bag_item_list, self.sort_func)
end

function TianShenWGData:TianShenOtherInfo(value)
	self.old_tianshen_iinfo.active_image_flag = self.tianshen_info.active_image_flag
	self.old_tianshen_iinfo.level = self.tianshen_info.level
	self.old_tianshen_iinfo.revert_us = self.tianshen_info.revert_us
	self.old_tianshen_iinfo.level_exp = self.tianshen_info.level_exp
	self.old_tianshen_iinfo.jinghua = self.tianshen_info.jinghua
	self.old_tianshen_iinfo.shuxingdan_use_count = __TableCopy(self.tianshen_info.shuxingdan_use_count)
	self.old_tianshen_iinfo.tianshen_chuzhan_slot_active_flags = self.tianshen_info.tianshen_chuzhan_slot_active_flags
	self.old_tianshen_iinfo.passskill_level = __TableCopy(self.tianshen_info.passskill_level)
	self.old_tianshen_iinfo.tianshen_heji_skill_flag = self.tianshen_info.tianshen_heji_skill_flag
	self.old_tianshen_iinfo.tianshen_heji_use_skill_list = self.tianshen_info.tianshen_heji_use_skill_list
	self.old_tianshen_iinfo.tianshen_jiban_flag = self.tianshen_info.tianshen_jiban_flag


	-- self.tianshen_info.active_image_flag = value.active_image_flag
	self.tianshen_info.level = value.level
	self.tianshen_info.revert_us = value.revert_us
	self.tianshen_info.level_exp = value.level_exp
	self.tianshen_info.jinghua = value.jinghua
	self.tianshen_info.shuxingdan_use_count = value.shuxingdan_use_count
	self.tianshen_info.tianshen_chuzhan_slot_active_flags = value.tianshen_chuzhan_slot_active_flags
	self.tianshen_info.passskill_level = value.passskill_level
	self.tianshen_info.tianshen_heji_skill_flag = value.tianshen_heji_skill_flag
	self.tianshen_info.tianshen_heji_use_skill_list = value.tianshen_heji_use_skill_list
	self.tianshen_info.tianshen_jiban_flag = value.tianshen_jiban_flag
	self:CheckChuZhan()
end

function TianShenWGData:TianShenInfo(value)
	if nil == value then
		return self.tianshen_info
	end

	self.tianshen_info.active_image_flag = value.active_image_flag
	self.tianshen_info.upgrade_list = value.upgrade_list
	for k,v in pairs(self.tianshen_info.upgrade_list) do
		v.zhanli = self:GetActivationZhanLi(k, true)
		for t,q in pairs(v.shenshi_part) do
			q.zhanli = self:GetShenShiZhanLi(q.item_id)
			v.zhanli2 = v.zhanli2 + q.zhanli
		end
	end
	self:ResetOldTianShenInfo()
	self:CheckChuZhan()
	self:SetShenShiItemUpFlagList()
	self:UpdateTianShenInfoList()
end

function TianShenWGData:UpdateTianShenInfoList(is_update_single, index)
	if not is_update_single then
		self.tianshen_info_data_list = {}
	end

	if IsEmptyTable(self.tianshen_info_data_list) then
		for k,v in pairs(self.magic_image_cfg) do
			local info = {}
			info.active_status = self.tianshen_info.active_image_flag[v.index]
			if info.active_status == 0 then
				local active_item_own_count = ItemWGData.Instance:GetItemNumInBagById(self.act_item_cfg[k].act_item_id)
				info.can_active_status = active_item_own_count >= self.act_item_cfg[k].act_item_cost and 1 or 0
			else
				info.can_active_status = 0
			end

			local upgrade_data = self.tianshen_info.upgrade_list[k]
			info.battle_status = upgrade_data and upgrade_data.zhan_index or -1
			info.level = upgrade_data.level
			info.bianshen_name = v.bianshen_name
			info.index = v.index
			info.series = v.series
			info.sort_index = v.sort_index
			info.is_default_show = v.is_default_show
			info.condition_type = v.condition_type
			info.skynumber_show = v.skynumber_show
			info.level_show = v.level_show
			table.insert(self.tianshen_info_data_list, info)
		end
	end

	if is_update_single and index then
		for k,v in pairs(self.tianshen_info_data_list) do
			if v.index == index then
				local active_image_flag = self.tianshen_info.active_image_flag[index]
				local can_active_status = 0
				if active_image_flag == 0 then
					local active_item_own_count = ItemWGData.Instance:GetItemNumInBagById(self.act_item_cfg[index].act_item_id) or 0
					can_active_status = active_item_own_count >= self.act_item_cfg[index].act_item_cost and 1 or 0
				else
					can_active_status = 0
				end

				v.can_active_status = can_active_status
				v.active_status = active_image_flag		-- 未激活 0， 激活 1
				-- 未出战 -1， 出战 大于等于0
				local upgrade_data = self.tianshen_info.upgrade_list[index]
				v.battle_status = upgrade_data and upgrade_data.zhan_index or -1
				v.level = upgrade_data.level
			end
		end
	end
	self:SortTianShenInfoDataList()
end

function TianShenWGData:GetTianShenSTDataList()
	return self.m_shentong_cfgs
end

--[[
function TianShenWGData:GetTianShenInfoDataList(tianshen_tab_type)
	self:SortTianShenInfoDataList(tianshen_tab_type)

	local show_tianshen_data_info = {}

	for k, v in pairs(self.tianshen_info_data_list) do
		if self:GetTianShenItemIsCanShow(v.index) then
			local series = v.series
			if not show_tianshen_data_info[series] then
                show_tianshen_data_info[series] = {}
            end
			table.insert(show_tianshen_data_info[series], v)
		end
	end


	return show_tianshen_data_info
	-- return self.tianshen_info_data_list
end
]]

-- 获取品质对应的列表
function TianShenWGData:GetTianShenInfoAccordionTable(tianshen_tab_type)
	if not self.accordion_table then
		self.accordion_table = {}
		self:SortTianShenInfoDataList(tianshen_tab_type)

		for k, v in pairs(self.tianshen_info_data_list) do
			if not self.accordion_table[v.series] then
				self.accordion_table[v.series] = {}
			end

			table.insert(self.accordion_table[v.series], v)
		end
	end
	
	return self.accordion_table
end

function TianShenWGData:GetTianShenInfoDataList(tianshen_tab_type)
	self:SortTianShenInfoDataList(tianshen_tab_type)

	local show_tianshen_data_info = {}

	for k, v in pairs(self.tianshen_info_data_list) do
		if self:GetTianShenItemIsCanShow(v.index) then
			table.insert(show_tianshen_data_info, v)
		end
	end
	return show_tianshen_data_info
end

function TianShenWGData:GetTianShenAllLevel()
	local all_level = 0
	local show_tianshen_data_info = self:GetTianShenInfoDataList()
	for k, v in pairs(show_tianshen_data_info) do
		if v.active_status == 1 then
			all_level = all_level + v.level
		end
	end

	return all_level
end

function TianShenWGData:GetShenQiBySeries(series)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0
	local cfg_map = {}
	if series == 0 then  -- 全部
		cfg_map = self.tianshen_shenqi_cfg_auto.shenqi or {}
	else
		cfg_map = self.shenqi_cfg[series] or {}
	end
	local cfg_list = {}
	local temp_day = 0
	local temp_level = 0
	local can_active = false
	local active_status = 0
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	for _,cfg in ipairs(cfg_map) do
		temp_day = cfg.skynumber_show or 0
		temp_level = cfg.show_level or 0
		active_status = self:IsShenQiActivation(cfg.index) and 1 or 0
		can_active = self:GetShenQiActivationRemind(cfg.index) == 1
		if (open_day >= temp_day and role_level >= temp_level) or can_active or active_status == 1 then
			cfg_list[#cfg_list+1] = cfg
		end
	end

	self.tianshen_shenqi_data_list = {}
	if IsEmptyTable(self.tianshen_shenqi_data_list) then
		for k,v in pairs(cfg_list) do
			local info = {}
			info.active_status = self:IsShenQiActivation(v.index) and 1 or 0
			if info.active_status == 0 then
				local flag = TianShenWGData.Instance:GetShenQiActivationRemind(v.index)
				info.can_active_status = flag
			else
				info.can_active_status = 0
			end
			info.index = v.index
			info.series = v.series
			info.stuff_id = v.stuff_id
			info.stuff_num = v.stuff_num
			info.name = v.name
			info.icon = v.icon
			info.ts_res_idui = v.ts_res_idui
			info.sort_index = v.sort_index
			info.is_default_show = v.is_default_show
			info.sq_index = v.tianshen_index
			info.condition_type = v.condition_type
			info.skynumber_show = v.skynumber_show
			info.show_level = v.show_level
			table.insert(self.tianshen_shenqi_data_list, info)
		end
	end

	local show_tianshen_shenqi_data_list = {}

	for k, v in pairs(self.tianshen_shenqi_data_list) do
		if self:GetShenQiItemIsCanShow(v) then
			table.insert(show_tianshen_shenqi_data_list, v)
		end
	end

	return show_tianshen_shenqi_data_list

end

-- 天神显示限制
function TianShenWGData:GetTianShenItemIsCanShow(index)
	if self:IsActivation(index) then
		return true
	end
	
	local act_cfg = self:GetTianshenItemCfgByIndex(index)
	if not act_cfg then
		return false
	end

	local had_num = ItemWGData.Instance:GetItemNumInBagById(act_cfg.act_item_id) or 0
	if had_num >= act_cfg.act_item_cost then
		return true
	end

	local cfg = self:GetTianShenCfg(index)
	if not cfg then
		return false
	end

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()
    if cfg.is_default_show == 1 then
        if cfg.condition_type == 2 then
			return open_day >= cfg.skynumber_show and role_level >= cfg.level_show
		else
			return open_day >= cfg.skynumber_show or role_level >= cfg.level_show
		end
    end

    return false
end

--天神技能展示限制
function TianShenWGData:GetTianShenSkillInfoDataList()
	local show_tianshenskill_data_info = {}

	for k, v in pairs(self.magic_image_cfg) do
		if self:GetTianShenItemIsCanShow(v.index) then
			table.insert(show_tianshenskill_data_info, v)
		end
	end

	return show_tianshenskill_data_info
end

function TianShenWGData:SortTianShenInfoDataList(tianshen_tab_type)
	if not ViewManager.Instance:IsOpen(GuideModuleName.TianShenView) then
		return
	end

	local sort_func
	local view = ViewManager.Instance:GetView(GuideModuleName.TianShenView)
	tianshen_tab_type = TianShenView.TabIndex.Activation -- tianshen_tab_type or (view and view:GetShowIndex() or TianShenView.TabIndex.Activation)
	--天神激活界面排序需求 可激活>已出战>已激活未出战>未激活，同类型里边按sort_index从小到大排序
	if tianshen_tab_type == TianShenView.TabIndex.Activation then
		sort_func = function(a, b)
			if a.can_active_status == 1 and b.can_active_status == 1 or a.can_active_status == 0 and b.can_active_status == 0 then
				if a.active_status == 1 and b.active_status == 1 then
					if a.battle_status ~= -1 and b.battle_status ~= -1 then
						return a.battle_status < b.battle_status
					elseif a.battle_status == -1 and b.battle_status == -1 then
						return a.sort_index < b.sort_index
					elseif a.battle_status == -1 or b.battle_status == -1 then
						return a.battle_status > b.battle_status
					end
				elseif a.active_status == 1 or b.active_status == 1 then
					return a.active_status > b.active_status
				end
			else
				return a.can_active_status > b.can_active_status
			end
			return a.sort_index < b.sort_index
		end
	-- else
	-- 	--天神神饰界面排序 已出战>已激活未出战>未激活，同类型里边按ID从小到大排序
	-- 	sort_func = function(a, b)
	-- 		if a.active_status == 1 and b.active_status == 1 then
	-- 			if a.battle_status ~= -1 and b.battle_status ~= -1 then
	-- 				return a.battle_status < b.battle_status
	-- 			elseif a.battle_status == -1 and b.battle_status == -1 then
	-- 				return a.index < b.index
	-- 			elseif a.battle_status == -1 or b.battle_status == -1 then
	-- 				return a.battle_status > b.battle_status
	-- 			end
	-- 		elseif a.active_status == 1 or b.active_status == 1 then
	-- 			return a.active_status > b.active_status
	-- 		end
	-- 		return a.index < b.index
	-- 	end
	end

	table.sort(self.tianshen_info_data_list, sort_func)
end

function TianShenWGData:GetTianshenInfoStatus(tianshen_id)
	if (not tianshen_id) or (not self.tianshen_info_data_list) then
		return 0
	end
	for k,v in pairs(self.tianshen_info_data_list) do
		if v.index == tianshen_id then
			return v.active_status
		end
	end
	return 0
end

function TianShenWGData:ReCacularShenShiPartCap()
	if not self.tianshen_info or not self.tianshen_info.upgrade_list then
		return
	end

	if self.already_recacular then
		return
	end
	self.already_recacular = true
	for k,v in pairs(self.tianshen_info.upgrade_list) do
		v.zhanli2 = 0
		for t,q in pairs(v.shenshi_part) do
			q.zhanli = self:GetShenShiZhanLi(q.item_id)
			v.zhanli2 = v.zhanli2 + q.zhanli
		end
	end
end

function TianShenWGData:GetTianShenLevel()
	return self.tianshen_info and self.tianshen_info.level or 0
end

function TianShenWGData:GetShuxingdanUseCount(index)
	if nil == self.tianshen_info then
		return 0
	end
	return self.tianshen_info.shuxingdan_use_count[index + 1] or 0
end

function TianShenWGData:ResetOldTianShenInfo()
	local value = __TableCopy(self.tianshen_info)
	self:OldTianShenInfo(value)
end

function TianShenWGData:OldTianShenInfo(value)
	if nil == value then
		return self.old_tianshen_iinfo
	end

	self.old_tianshen_iinfo = value
end

function TianShenWGData:SetTianShenInfoOne(value)
	--print_error("SetTianShenInfoOne", value)
	local old_info = self.tianshen_info.upgrade_list[value.index]
	local old_active_image_flag = self.tianshen_info.active_image_flag

	local attr_per = 0
	for i, v in ipairs(value.upgrade_item_info.shenshi_refine_value_list) do
		attr_per = attr_per + v
	end

	attr_per = (attr_per / 10000) + 1
	self.old_tianshen_iinfo.upgrade_list[value.index] = __TableCopy(old_info)
	self.old_tianshen_iinfo.active_image_flag = old_active_image_flag

	for k,v in pairs(value.upgrade_item_info.shenshi_part) do
		v.zhanli = self:GetShenShiZhanLi(v.item_id, attr_per)
		value.upgrade_item_info.zhanli2 = value.upgrade_item_info.zhanli2 + v.zhanli
	end
	self.tianshen_info.upgrade_list[value.index] = value.upgrade_item_info
	self.tianshen_info.active_image_flag = value.active_image_flag

	local had_new_act = false
	for i=0 ,#self.magic_image_cfg do
		if old_active_image_flag[i] == 0 and self.tianshen_info.active_image_flag[i] == 1 then
			self:CacularJiban(i)
			had_new_act = true
		end
	end

	-- 有新的激活
	if had_new_act then
		TianShenLingHeWGCtrl.Instance:NewTianShenActDo()
	end

	--判断单个改变神饰晋级
	if self.old_tianshen_iinfo.upgrade_list[value.index] and self.old_tianshen_iinfo.upgrade_list[value.index].shenshi_part then
		if self.old_tianshen_iinfo.upgrade_list[value.index].jingshen < value.upgrade_item_info.jingshen then
			 TianShenWGCtrl.Instance:OpenShenShiUpGradeTip(value.index)
		end
	end
	self:CheckChuZhan()
	self:SetShenShiItemUpFlagList()
	self:UpdateTianShenInfoList(true, value.index)
end

-- 获取天神出战信息 供主界面调用
function TianShenWGData:GetTianShenSkillInfoByMainView()
	local data_list = {}
	local data_amount = 0
	if self.tianshen_info then
		for k,v in pairs(self.tianshen_info.upgrade_list) do
			if v.zhan_index >= 0 then
				v.image_index = k
				data_list[v.zhan_index] = v
				data_amount = data_amount + 1
			end
		end
	end
	return data_list, data_amount
end

function TianShenWGData:GetTianShenZJMShowIndex(data_list)
	local zhan_index = 0
	if IsEmptyTable(data_list) then
		return zhan_index
	end

	local end_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	for i = 0, 3 do
		local data = data_list[i]
		if data then
			if data.is_bianshen == 1 then
				return data.zhan_index
			end

			local data_time = data.next_bianshen_time - server_time
			data_time = data_time > 0 and data_time or 0
			if end_time == nil or (data_time < end_time) then
				end_time = data_time
				zhan_index = data.zhan_index
			end
		end
	end

	return zhan_index
end



-- 背包筛选
-- pingjie 0 全部，其余是品阶
-- color 1 全部，其余是颜色
-- buwei -1全部，其余是部位
-- 不传参也返回全部
function TianShenWGData:GetBagScreen(pingjie, color, buwei)
	local bag_list = self.tianshen_bag_info.bag_item_list
	local list = {}
	local item_cfg = nil
	local index = 0

	for i=1,#bag_list do
		item_cfg = ItemWGData.Instance:GetItemConfig(bag_list[i] and bag_list[i].item_id)
		if item_cfg and (not pingjie or 0 == pingjie or bag_list[i].grade_level == pingjie) and
			(not color or 1 == color or item_cfg.color == color) and
			(not buwei or -1 == buwei or TianShenWGData.Equip_Pos[item_cfg.sub_type] == buwei) then
			list[index] = bag_list[i]
			index = index + 1
		end
	end

	return list
end

--根据天神索引判断天神是否出战
function TianShenWGData:GetTianShenIsChuZhanByIndex(index)
	local ts_info = self:GetTianShenInfoByIndex(index)
	if not IsEmptyTable(ts_info) then
		return (ts_info.zhan_index or -1) >= 0
	end

	return false
end

--根据天神索引判断天神是否处于CD中 true:冷却中
function TianShenWGData:CheckTianShenIsCDTime(index)
	local ts_info = self:GetTianShenInfoByIndex(index)
	if not IsEmptyTable(ts_info) then
		return (ts_info.next_bianshen_time or 0) > TimeWGCtrl.Instance:GetServerTime()
	end

	return false
end

function TianShenWGData:GetTianShenInfoByIndex(index)
	if IsEmptyTable(self.tianshen_info) or not self.tianshen_info.upgrade_list then
		return nil
	end
	if self.tianshen_info then
		return self.tianshen_info.upgrade_list[index]
	end
end

function TianShenWGData:GetOldTianShenInfoByIndex(index)
	if self.old_tianshen_iinfo then
		return self.old_tianshen_iinfo.upgrade_list[index]
	end
end

function TianShenWGData:GetDanMaxCount(dan_index)
	if nil == self.dan_limit_cfg[dan_index] then
		return 0
	end
	local cfg = self.dan_limit_cfg[dan_index]
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local index = #cfg
	for k,v in ipairs(cfg) do
		if v.role_level > role_level then
			index = k - 1
			break
		end
	end
	return cfg[index] and cfg[index].count or 0
end

function TianShenWGData:GetTianShenSXDanNextLimit(dan_index)
	if nil == self.dan_limit_cfg[dan_index] then
		return
	end
	local cfg = self.dan_limit_cfg[dan_index]
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	for k,v in ipairs(cfg) do
		if v.role_level > role_level then
			return v.role_level
		end
	end
end

function TianShenWGData:GetDanItem(dan_index)
	return self.dan_item_limit[dan_index+1]
end

function TianShenWGData:IsTianShenEquip(sub_type)
	for k,v in pairs(TianShenWGData.Equip_Pos) do
		if k == sub_type then
			return true
		end
	end
	return false
end

function TianShenWGData:CheckChuZhan()
	local flag_list = self.tianshen_info.active_image_flag
	local old_flag_list = self.old_tianshen_iinfo.active_image_flag
	local len = TIANSHEN_MAX - 1
	local image_cfg
	for i=0,len do
		if flag_list[i] == 1 and old_flag_list[i] == 0 then
			image_cfg = self.magic_image_cfg[i]
			if image_cfg then
				AppearanceWGCtrl.Instance:OnGetNewAppearance({appe_type = ROLE_APPE_TYPE.BIANSHEN, appe_image_id = image_cfg.appe_image_id}) --模拟服务器下发
			end
			break
		end
	end
end

-- 展示新形象
function TianShenWGData:ShowGetNewAppearance(tianshen_index)
	local image_cfg = self.magic_image_cfg[tianshen_index]
	if image_cfg then
		AppearanceWGCtrl.Instance:OnGetNewAppearance({appe_type = ROLE_APPE_TYPE.BIANSHEN, appe_image_id = image_cfg.appe_image_id}) --模拟服务器下发
	end
end

function TianShenWGData:GetTianShenSitCfgByIndex(index)
	local cfg = self.tianshen_cfg_auto.zhan_sit
	return cfg and cfg[index]
end

function TianShenWGData:CanActivationCell()
	local zhan_sit_list = self.tianshen_cfg_auto.zhan_sit
	for i=1,#zhan_sit_list do
		local flag = self:CanActivationIndex(i)
		if flag then
			return true
		end
	end

	return false
end

function TianShenWGData:CanActivationIndex(index)
	local zhan_sit = self.tianshen_cfg_auto.zhan_sit[index]
	if 0 == zhan_sit.item_id then return false end

    if self:IsChuZhanIndexOpen(zhan_sit.sit_index) then
        return false
    else
        local item_count = ItemWGData.Instance:GetItemNumInBagById(zhan_sit.item_id)
        return 0 < item_count
    end
end

--出战提醒
function TianShenWGData:SpecialTianshenCanChuZhan()
	local flag = 0
	local zhan_sit_list = self.tianshen_cfg_auto.zhan_sit
	for i=1,#zhan_sit_list do
		flag = self:SingleChuZhanRemind(zhan_sit_list[i])
		if flag then
			return true
		end
	end
	return false
end

-- 单个天神出战
function TianShenWGData:SingleChuZhanRemind(zhan_sit)
	local flag = not self:IsChuZhanIndexOpen(zhan_sit.sit_index)
	local prop_num = ItemWGData.Instance:GetItemNumInBagById(zhan_sit.item_id)
	if flag then
		return prop_num > 0
	end

	flag = self:HasChuZhan(zhan_sit.sit_index)
	if flag then
		return false
	end

	local list = TianShenWGData.Instance:GetFightTisnShen(false)
	flag = 0 ~= #list
	return flag
end

--幻化提醒
function TianShenWGData:SpecialTianshenRemind()
	--幻化
	for k,v in pairs(self.magic_image_series_cfg) do
		local flag, ret_param = self:SpecialTianshenSeriesCanRemind(k)
		if flag then
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_UPGRADE, 1, function(callback_param)
					TianShenWGCtrl.Instance:Open(TabIndex.tianshen_activation)
					return true
				end, ret_param)
			return 1, ret_param
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_UPGRADE, 0, function()
		return true
	end)
	return 0, nil
end

--幻化类型是否可操作
function TianShenWGData:SpecialTianshenSeriesCanRemind(series)
	local ret_param, cfg = nil, nil
	for k,v in ipairs(self.magic_image_series_cfg[series] or {}) do
		if self:SpecialTianshenCanActive(v.index) then
			ret_param = {type = EnumTianShenHuanHuaOperate.Active, cfg = v}
			return true, ret_param
		end
		if self:SpecialTianshenCanUpStar(v.index) then
			ret_param = {type = EnumTianShenHuanHuaOperate.UpStar, cfg = v}
			return true, ret_param
		end
		if self:SpecialTianshenCanUpGrade(v.index) then
			ret_param = {type = EnumTianShenHuanHuaOperate.UpGrade, cfg = v}
			return true, ret_param
		end
	end
	return false, ret_param
end

-- 获取所有天神升阶红点
function TianShenWGData:GetUpgradeRemind()
	for k,v in pairs(self.magic_image_series_cfg) do
		local flag, cfg = self:GetUpgradeRemindBySeries(k)
		if flag > 0 then
			return flag, cfg
		end
	end
	return 0, nil
end

-- 获得单类型天神升阶红点
function TianShenWGData:GetUpgradeRemindBySeries(series)
	for k,v in ipairs(self.magic_image_series_cfg[series] or {}) do
		if self:SpecialTianshenCanUpGrade(v.index) then
			return 1, v
		end
	end
	return 0, nil
end

-- 获取所有天神升星红点
function TianShenWGData:GetUpStarRemind()
	for k,v in pairs(self.magic_image_series_cfg) do
		local flag, cfg = self:GetUpStarRemindBySeries(k)
		if flag > 0 then
			return flag, cfg
		end
	end
	return 0, nil
end

-- 获得单类型天神升星红点
function TianShenWGData:GetUpStarRemindBySeries(series)
	for k,v in ipairs(self.magic_image_series_cfg[series] or {}) do
		if self:SpecialTianshenCanUpStar(v.index) then
			return 1, v
		end
	end
	return 0, nil
end

function TianShenWGData:SpecialZhuSkillListCanUpgrade(index)
	if not self:IsActivation(index) then return false end
	local zhu_skill = self:GetTianShenZhuSkill(index)
	for k,v in pairs(zhu_skill) do
		if self:SpecialZhuSkillCanUpgrade(index, v) then
			return true
		end
	end
	return false
end

function TianShenWGData:SpecialZhuSkillCanUpgrade(index, skill_id)
	if not self:IsActivation(index) then return false end
	skill_id = tonumber(skill_id)
	local skill_info = self:GetTianShensMainkill(index, skill_id)
	local skill_level = skill_info and skill_info.level or 1
	local next_skill_cfg = SkillWGData.Instance:GetTianShenSkillCfg(skill_id, skill_level + 1)
	local skill_cfg = self:GetSpecialSkill(skill_id, skill_level)
	if skill_cfg and next_skill_cfg then
		local item_id = skill_cfg.item_id
		local bag_have_item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
		if bag_have_item_num >= skill_cfg.item_num then
			return true
		end
	end
	return false
end

--幻化是否可激活
function TianShenWGData:SpecialTianshenCanActive(index)
	local is_active = self:IsActivation(index)
	local item = self.tianshen_cfg_auto.item[index+ 1]
	if not is_active and item then
		local item_id = item.act_item_id
		local need_count = item.act_item_cost
		local item_count = ItemWGData.Instance:GetItemNumInBagById(item_id)
		return item_count >= need_count
	end
	return false
end

--幻化是否可升星
function TianShenWGData:SpecialTianshenCanUpStar(index)
	local is_active = self:IsActivation(index)
	local tianshen_info = self:GetTianShenInfoByIndex(index)
	if not is_active or not tianshen_info or tianshen_info.star >= TIANSHEN_STAR_MAX then
		return false
	end
	local item = self.tianshen_cfg_auto.item[index+ 1]
	local star = self:GetTianShenStar1(index, tianshen_info.star)--, tianshen_info.cao)
	if item and star then
		local item_id, need_count
		--[[if tianshen_info.cao < TIANSHEN_CAO_MAX then
			-- 升槽
			item_id = item.cao_item_id
			need_count = star.cao_cost
		else--]]
			-- 升星
			item_id = item.star_item_id
			need_count = star.star_cost
		--end
		local item_count = ItemWGData.Instance:GetItemNumInBagById(item_id)
		return item_count >= need_count
	end
	return false
end

--幻化是否可升阶
--策划需求:材料满足升一级才显示红点
function TianShenWGData:SpecialTianshenCanUpGrade(index)
	if not FunOpen.Instance:GetFunIsOpened(FunName.TianshenHuanhuaJinjie) then
		return false, 0
	end

	local is_active = self:IsActivation(index)
	local tianshen_info = self:GetTianShenInfoByIndex(index)
	if not tianshen_info then return false end
	local cur_upgrade_cfg = TianShenWGData.Instance:GetTianShenUpgrade(index, tianshen_info.level)
	local next_upgrade_cfg = TianShenWGData.Instance:GetTianShenUpgrade(index, tianshen_info.level + 1)
	if not is_active or not next_upgrade_cfg then
		return false
	end
	local item = self.tianshen_cfg_auto.item[index+ 1]

	if item then
		local item_count1 = ItemWGData.Instance:GetItemNumInBagById(item.upgrade_item_id1) or 0
		local item_count2 = ItemWGData.Instance:GetItemNumInBagById(item.upgrade_item_id2) or 0
		local item_count3 = ItemWGData.Instance:GetItemNumInBagById(item.upgrade_item_id3) or 0
		local total_add_exp = (item.upgrade_exp_add1 * item_count1) + (item.upgrade_exp_add2 * item_count2) + (item.upgrade_exp_add3 * item_count3)
		local up_level_need_exp = cur_upgrade_cfg.upgrade_need_exp - tianshen_info.uplevel_exp_val

		return total_add_exp >= up_level_need_exp
	end
	return false
end

--是否开启出战格子
function TianShenWGData:IsChuZhanIndexOpen(index)
	local is_open = false
	local zhan_sit = self.tianshen_cfg_auto.zhan_sit[index + 1]
	if zhan_sit == nil then
		return is_open
	end

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()

	if zhan_sit.level_limit > main_role_vo.level then
		is_open = false
	elseif zhan_sit.vip_limit > main_role_vo.vip_level then
		is_open = false
	elseif zhan_sit.zhuan_limit > role_zhuan then
		is_open = false
	elseif 0 ~= zhan_sit.item_id then
		is_open = 1 == self.tianshen_info.tianshen_chuzhan_slot_active_flags[zhan_sit.sit_index]
	else
		is_open = true
	end

	-- if zhan_sit.level_limit <= main_role_vo.level and zhan_sit.vip_limit <= main_role_vo.vip_level and zhan_sit.zhuan_limit <= role_zhuan then
	-- 	return true
	-- end
	return is_open
end

--已解锁出战位置总数
function TianShenWGData:GetActiveZhanSitNum()
	local num = 0
	for i=1,#self.tianshen_cfg_auto.zhan_sit do
		if self:IsChuZhanIndexOpen(self.tianshen_cfg_auto.zhan_sit[i].sit_index) then
			num = num + 1
		end
	end
	return num
end

function TianShenWGData:IsActivation(index)
	return self.tianshen_info and 1 == self.tianshen_info.active_image_flag[index]
end

-- function TianShenWGData:GetEquipBasicsAttr(shenshi_id, star)
-- 	return self.equip_basics_attr[shenshi_id] and self.equip_basics_attr[shenshi_id][star]
-- end

-- function TianShenWGData:GetEquipStrangeAttr(sub_type, strength_level)
-- 	return self.equip_strength_attr_cfg[sub_type] and self.equip_strength_attr_cfg[sub_type][strength_level]
-- end

-- function TianShenWGData:GetEquipUpgradeAttr(sub_type, color, star_level, upgrade_level)
-- 	return self.equip_upgrade_attr_cfg[sub_type] and self.equip_upgrade_attr_cfg[sub_type][color] and
-- 			self.equip_upgrade_attr_cfg[sub_type][color][star_level] and self.equip_upgrade_attr_cfg[sub_type][color][star_level][upgrade_level]
-- end

-- function TianShenWGData:GetEquipUpgradeAcuraAttr(sub_type, color, star_level, upgrade_level)
-- 	return self.equip_upgrade_acura_attr_cfg[sub_type] and
-- 		self.equip_upgrade_acura_attr_cfg[sub_type][color] and
-- 		self.equip_upgrade_acura_attr_cfg[sub_type][color][star_level] and
-- 		self.equip_upgrade_acura_attr_cfg[sub_type][color][star_level][upgrade_level]
-- end

function TianShenWGData:GetEquipRecommendAttr(sub_type, star_level, color, count, attr_list)
	local list = {}

	if color == 3 then
		star_level = 0
	end

	if not attr_list then
		local attr_list_cfg = self.equip_random_attr_cfg[sub_type] and self.equip_random_attr_cfg[sub_type][star_level] and self.equip_random_attr_cfg[sub_type][star_level][color]
		if attr_list_cfg then
			for k,v in pairs(attr_list_cfg) do
				if #list < count then
					table.insert(list, {attr_id = v.attr_id, is_star = v.is_star_attr, attr_value = v.attr_val})
				end
			end
		end
		return list
	end

	for k,v in pairs(attr_list) do
		local attr_list_cfg = self.equip_random_attr_cfg[sub_type] and self.equip_random_attr_cfg[sub_type][star_level] and self.equip_random_attr_cfg[sub_type][star_level][color][v]
		if attr_list_cfg then
			table.insert(list, {attr_id = attr_list_cfg[v].attr_id, is_star = attr_list_cfg[v].is_star_attr, attr_value = attr_list_cfg[v].attr_val})
		end
	end

	return list
end

function TianShenWGData:GetAttrListByCfg(attr_cfg, color)
	local list = {}
	local is_per = false
	if not IsEmptyTable(attr_cfg) then
		for k, v in pairs(attr_cfg) do
			local tab = {}
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
			if Language.Common.AttrName[k] then
				tab.attr_type = k
				tab.attr_value = v

				if is_per and 0 < v then
					tab.attr_value = (tab.attr_value/100) .. "%"
				end
                tab.attr_name = Language.Common.TipsAttrNameList[tab.attr_type].."："
				if v ~= 0 then
					table.insert(list, tab)
				end
			end
		end
	end

	list = self:SortAttr(list)
	return list
end

-- 升星，升阶属性展示
function TianShenWGData:GetUpgradeAttribute(upgrade_type, index, need_next, tianshen_level)
	local list = {}
	local tianshen_info = self:GetTianShenInfoByIndex(index)
	if not tianshen_info then return list end
	local is_act = self:IsActivation(index)
	tianshen_level = tianshen_level or tianshen_info.level

	local up_star_cfg = self:GetTianShenStar1(index, tianshen_info.star)--, tianshen_info.cao)
	local up_upgrade_cfg = self:GetTianShenUpgrade(index, tianshen_level)
	local shenqi_attr_add_per = 0
	local shenqi_cfg = self:GetShenQiByTianShenIndex(index)
	if shenqi_cfg and shenqi_cfg.index then
		local shenqi_active = TianShenWGData.Instance:IsShenQiActivation(shenqi_cfg.index)
		if shenqi_active then
			local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(shenqi_cfg.index)
    		local star = shenqi_info and shenqi_info.star or 0
			local shenqi_attr_cfg = TianShenWGData.Instance:GetShenQiStarCfg(shenqi_cfg.index, star)
			shenqi_attr_add_per = shenqi_attr_cfg["tianshen_att_per"] or 0
			shenqi_attr_add_per = shenqi_attr_add_per / 10000 
		end
	end

	-- 天道石影响属性百分比,不影响百分比 因为tianshen_att_per也不影响百分比 所以直接加上
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.TIANSHEN)
	charm_rate = charm_rate / 10000
	shenqi_attr_add_per = shenqi_attr_add_per + charm_rate

	local next_cfg = nil
	local is_shengjie = upgrade_type == TianShen_Upgrade_Type.ShengJie and is_act
	if is_shengjie then
		next_cfg = self:GetTianShenUpgrade(index, tianshen_level + 1)
	elseif upgrade_type == TianShen_Upgrade_Type.ShengXing then
		--[[if tianshen_info.cao < TIANSHEN_CAO_MAX then
			next_cfg = self:GetTianShenStar(index, tianshen_info.star, tianshen_info.cao + 1)
		elseif tianshen_info.star < TIANSHEN_STAR_MAX then
			next_cfg = self:GetTianShenStar(index, tianshen_info.star + 1, 0)
		end--]]
		if tianshen_info.star < TIANSHEN_STAR_MAX then
			next_cfg = self:GetTianShenStar1(index, tianshen_info.star + 1)--, 0)
		end
	elseif upgrade_type == TianShen_Upgrade_Type.MoHua then	---摆烂写法
		---这里检测是否激活了魔化
		next_cfg = nil	
	end

	if not up_star_cfg and not up_upgrade_cfg and not next_cfg then
		return list
	end

	-- 添加被动加成
	local pasv_attr_list = self:GetPasvAttr(index)
	local cur_cfg = is_shengjie and up_upgrade_cfg or up_star_cfg or {}
	local is_show = false
	local is_per = false

	local cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_cfg)

	---添加魔化属性
	-- print_error("此处添加魔化属性", cfg_map_attr_list)

	for k,v in pairs(cfg_map_attr_list) do
		local tab = {}
		is_show = false
		is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
			tab.attr_type = v
			
			local attr_value = cur_cfg[k] or 0
			tab.attr_value = attr_value
			if not is_per then
				if pasv_attr_list[v] then
					tab.attr_value = math.floor(tab.attr_value * (pasv_attr_list[v] + shenqi_attr_add_per) + 0.5)
				elseif tab.attr_value and tab.attr_value > 0 then
					tab.attr_value = math.floor(tab.attr_value * (1 + shenqi_attr_add_per) + 0.5)
				end
			end

			if is_per and 0 < attr_value then
				tab.attr_value = (tab.attr_value/100) .. "%"
			end

			if is_act and next_cfg then
				if is_shengjie then
					tab.attr_add = next_cfg[k] - (up_upgrade_cfg and up_upgrade_cfg[k] or 0)
				elseif upgrade_type == TianShen_Upgrade_Type.ShengXing then
					tab.attr_add = next_cfg[k] - (up_star_cfg and up_star_cfg[k] or 0)
				end

				if not is_per then
					if pasv_attr_list[v] then
						tab.attr_add = math.floor(tab.attr_add * (pasv_attr_list[v] + shenqi_attr_add_per) + 0.5)
					elseif tab.attr_add and tab.attr_add > 0 then
						tab.attr_add = math.floor(tab.attr_add * (1 + shenqi_attr_add_per) + 0.5)
					end
				end

				if is_per and 0 < tab.attr_add then
					tab.attr_add = (tab.attr_add/100) .. "%"
				end
			end

			is_show = TianShenWGData.FixedAttr[k] ~= nil or 0 ~= attr_value or (next_cfg ~= nil and 0 ~= next_cfg[k])
			if is_show then
				table.insert(list, tab)
			end
	end

	list = self:SortAttr(list)

	-- 加入神灵护盾属性
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(index)
	if tianshen_cfg and tianshen_cfg.bianshen_hp_percent then
		local tab = {}
		tab.attr_value = string.format(Language.TianShen.TSShieldAttrName, tianshen_cfg.bianshen_hp_percent / 100)
		tab.attr_type = "tianshen_shield"
		table.insert(list, tab)
	end

	return list
end

function TianShenWGData:SortAttr(list)
	local attr_sort_index_list = AttributeMgr.GetAttrSortIndexList()
	local function sort_func(a, b)
		local a_attr_type = AttributeMgr.GetAttributteKey(a.attr_type)
		local b_attr_type = AttributeMgr.GetAttributteKey(b.attr_type)
		if not attr_sort_index_list[a_attr_type] or not attr_sort_index_list[b_attr_type] then
			return false
		end
		
		return attr_sort_index_list[a_attr_type] < attr_sort_index_list[b_attr_type]
	end

	table.sort(list, sort_func)
	return list
end

function TianShenWGData:GetEquipZhanLi(index, buwei, is_next)
	-- local capability = AttributeMgr.GetCapability(attr_struct)
	return 0
end

function TianShenWGData:GetActivationZhanLi(index, get_act)
	local list = {}
	local tianshen_info = self:GetTianShenInfoByIndex(index)
	if not tianshen_info then return 0 end
	local up_star_cfg = self:GetTianShenStar1(index, tianshen_info.star)--, tianshen_info.cao)
	local up_upgrade_cfg = self:GetTianShenUpgrade(index, tianshen_info.level)

	-- if get_act then
	-- 	local is_act = self:IsActivation(index)
	-- 	if not is_act then
	-- 		up_upgrade_cfg = self:GetTianShenUpgrade(index, tianshen_info.level + 1)
	-- 	end
	-- end

	if not up_star_cfg and not up_upgrade_cfg then
		return 0
	end

	local attr_star_list = AttributeMgr.GetAttributteByClass(up_star_cfg)
	local attr_upgrade_list = AttributeMgr.GetAttributteByClass(up_upgrade_cfg)
	local shenqi_attr_add_per = 0
	local shenqi_cfg = self:GetShenQiByTianShenIndex(index)
	if shenqi_cfg and shenqi_cfg.index then
		local shenqi_active = TianShenWGData.Instance:IsShenQiActivation(shenqi_cfg.index)
		if shenqi_active then
			local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(shenqi_cfg.index)
    		local star = shenqi_info and shenqi_info.star or 0
			local shenqi_attr_cfg = TianShenWGData.Instance:GetShenQiStarCfg(shenqi_cfg.index, star)
			shenqi_attr_add_per = shenqi_attr_cfg["tianshen_att_per"] or 0
			shenqi_attr_add_per = shenqi_attr_add_per / 10000 
		end
	end

	-- 天道石影响属性百分比,不影响百分比 因为tianshen_att_per也不影响百分比 所以直接加上
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.TIANSHEN)
	charm_rate = charm_rate / 10000
	shenqi_attr_add_per = shenqi_attr_add_per + charm_rate

	-- 添加被动加成
	local pasv_attr_list = self:GetPasvAttr(index, true)
	for k,v in pairs(attr_star_list) do
		if pasv_attr_list[k] then
			attr_star_list[k] = attr_star_list[k] * (pasv_attr_list[k] + shenqi_attr_add_per)
		else
			attr_star_list[k] = attr_star_list[k] * (1 + shenqi_attr_add_per)
		end
	end

	for t,q in pairs(attr_star_list) do
		if pasv_attr_list[t] then
			attr_upgrade_list[t] = attr_upgrade_list[t] * (pasv_attr_list[t] + shenqi_attr_add_per)
		else
			attr_upgrade_list[t] = attr_upgrade_list[t] * (1 + shenqi_attr_add_per)
		end
	end

	local zhanli = AttributeMgr.GetCapability(attr_star_list) + AttributeMgr.GetCapability(attr_upgrade_list)
	return zhanli
end

function TianShenWGData:GetXiuZhenZhanLi(item_id)
	local list = {}
	local index = 0
	for k,v in pairs(self.act_item_cfg) do
		if v.act_item_id == item_id then
			index = v.index
		end
	end

	local tianshen_info = self:GetTianShenInfoByIndex(index)
	if not tianshen_info then return 0 end
	local up_star_cfg = self:GetTianShenStar1(index, 0)--, tianshen_info.cao)
	local up_upgrade_cfg = self:GetTianShenUpgrade(index, 0)

	-- if get_act then
	-- 	local is_act = self:IsActivation(index)
	-- 	if not is_act then
	-- 		up_upgrade_cfg = self:GetTianShenUpgrade(index, tianshen_info.level + 1)
	-- 	end
	-- end

	if not up_star_cfg and not up_upgrade_cfg then
		return 0
	end

	local attr_star_list = AttributeMgr.GetAttributteByClass(up_star_cfg)
	local attr_upgrade_list = AttributeMgr.GetAttributteByClass(up_upgrade_cfg)
	-- 添加被动加成
	local pasv_attr_list = self:GetPasvAttr(index, true)
	for k,v in pairs(pasv_attr_list) do
		if attr_star_list[k] then
			attr_star_list[k] = attr_star_list[k] * v
		end

		if attr_upgrade_list[k] then
			attr_upgrade_list[k] = attr_upgrade_list[k] * v
		end
	end

	local zhanli = AttributeMgr.GetCapability(attr_star_list) + AttributeMgr.GetCapability(attr_upgrade_list)
	return zhanli
end

function TianShenWGData:GetPasvAttr(index, change_name, show_max_grade)
	local pasv_attr_list = {}
	local pasv_skill = self:GetSpecialImagePasvSkillCfg(index)
	local tianshen_info = self:GetTianShenInfoByIndex(index)
	if not tianshen_info or not pasv_skill then return pasv_attr_list end

	local attr_type = nil
	for k,v in pairs(pasv_skill) do
		if v.active_grade <= tianshen_info.level or show_max_grade then
			attr_type = EquipmentWGData.Instance:GetAttrStrByAttrId(v.param1)
			if attr_type then
				if pasv_attr_list[attr_type] then
					pasv_attr_list[attr_type] = pasv_attr_list[attr_type] + v.param2/10000
				else
					pasv_attr_list[attr_type] = v.param2/10000 + 1
				end
			end
		end
	end

	return pasv_attr_list
end

function TianShenWGData:GetTianShenImageCfg()
	return self.magic_image_cfg
end

function TianShenWGData:GetTianShenCfg(index)
	return self.magic_image_cfg[index]
end

function TianShenWGData:GetTianShenActItemCfgByActId(act_item_id)
	return self.tianshen_act_item_cfg[act_item_id]
end

function TianShenWGData:GetAppeImage(act_item_id)
	local act_item_cfg = self:GetTianShenActItemCfgByActId(act_item_id)
	if not act_item_cfg then
		return nil
	end
	return self.magic_image_cfg[act_item_cfg.index]
end

function TianShenWGData:GetTianShenZhuSkill(index)
	local cfg = self.magic_image_cfg[index]
	if cfg then
		return self:GetTianshenSkillList(cfg)
	end
	return {}
end

function TianShenWGData:GetTianShenZhuSkillByItemid(act_item_id)
	local cfg = self:GetTianShenActItemCfgByActId(act_item_id)

	if cfg then
		return self:GetTianShenZhuSkill(cfg.index)
	end

	return {}
end

function TianShenWGData:GetTianShenStar(index, star_count, cao_count)
	local star = self.special_image_star_cfg
	if star and star[index] and star[index][star_count] then
		return star[index][star_count][cao_count]
	end
	return nil
end

function TianShenWGData:GetTianShenStar1(index, star_count)
	local star = self.special_image_star_cfg
	if star and star[index] and star[index][star_count] then
		return star[index][star_count]
	end
	return nil
end

function TianShenWGData:GetTianShenUpgrade(index, level)
	if self.special_image_upgrade_cfg[index] then
		return self.special_image_upgrade_cfg[index][level]
	end
	return nil
end

-- f是否出战了
function TianShenWGData:HasChuZhan(index)
	for k,v in pairs(self.tianshen_info.upgrade_list) do
		if v.zhan_index == index then
			return true
		end
	end
	return false
end

-- 获取技能等级
function TianShenWGData:GetTianShenPassiveSkill(index, skill_id)
	return self.tianshen_info.passskill_level[skill_id + 1] or 0
end

-- flag true己出战 false未出战
function TianShenWGData:GetFightTisnShen(flag)
	local list = {}
	local flag_list = self.tianshen_info.active_image_flag
	local len = TIANSHEN_MAX - 1
	for i = 0, len do
		if self.tianshen_info.upgrade_list[i] and 1 == flag_list[i] then
			if flag then
				if -1 ~= self.tianshen_info.upgrade_list[i].zhan_index then
					list[self.tianshen_info.upgrade_list[i].zhan_index] = self.magic_image_cfg[i]
				end
			else
				if -1 == self.tianshen_info.upgrade_list[i].zhan_index then
					table.insert(list, self.magic_image_cfg[i])
				end
			end
		end
	end

	if not flag then
		local sort_func = function (a, b)
			if not a.series or not b.series then
				return true
			end

			if a.series ~= b.series then
				return a.series > b.series
			end
			return a.index > b.index
			-- local zhanli_1 = self:GetActivationZhanLi(a.index, true)
			-- local zhanli_2 = self:GetActivationZhanLi(b.index, true)
			-- return zhanli_1 > zhanli_2
		end
		table.sort(list, sort_func)
	end
	return list
end

function TianShenWGData:ChangeColor(str, is_star)
	local str = "<color=#7544ce>" .. str .. "</color>"
	return str
end

--默认模型
function TianShenWGData:GetCurUseImageToggleIndex()
	-- --默认模型
	return 1,1
end

function TianShenWGData:GetShenShiCurUseImageToggleIndex()
	return self.shenshi_series,self.select_shenshi_index
end

--获取天神幻化被动技能
function TianShenWGData:GetSpecialImagePasvSkillCfg(image_id)
	if not image_id or not self.special_image_pasv_skill[image_id] then return {} end
	return self.special_image_pasv_skill[image_id][1] or {}
end

-- 获取所需激活等级最大的技能配置
function TianShenWGData:GetMaxSpecialImagePasvSkill(image_id)
	if not self.max_special_skill_cfg_cache[image_id] then
		local skill_cfg = self:GetSpecialImagePasvSkillCfg(image_id)
		if not skill_cfg then
			return nil
		end
		local max_active_level = 0
		local max_skill_cfg = nil
		for i, v in ipairs(skill_cfg) do
			if v.active_level > max_active_level then
				max_active_level = v.active_level
				max_skill_cfg = v
			end
		end
		self.max_special_skill_cfg_cache[image_id] = max_skill_cfg
	end
	return self.max_special_skill_cfg_cache[image_id]
end

-- 所有被动技能是否已经激活
function TianShenWGData:AllSpecialPasvSkillIsActive()
	local tianshen_level = self:GetTianShenLevel()
	local max_skill_cfg = self:GetMaxSpecialImagePasvSkill(-1) 		-- 最高激活等级的技能配置
	return tianshen_level >= max_skill_cfg.active_level
end

--通过道具id获取天神幻化被动技能
function TianShenWGData:GetSpecialImagePasvSkillCfgByItemId(item_id)

	for k,v in pairs(ConfigManager.Instance:GetAutoConfig("tianshen_cfg_auto").item) do
		if v.act_item_id == item_id then
			return self:GetSpecialImagePasvSkillCfg(v.index)
		end
	end
	return nil
end

--获取天神幻化被动技能
function TianShenWGData:GetSpecialImagePasvSkillLevelCfg(image_id, skill_id, skill_level)
	if not image_id then return {} end
	if self.special_image_pasv_skill[image_id] and self.special_image_pasv_skill[image_id][skill_level] then
		return self.special_image_pasv_skill[image_id][skill_level][skill_id]
	end
	return nil
end

--获取幻装
function TianShenWGData:GetMagicImageListCfg(series)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0
	local cfg_list = {}
	local cfg_map = self.magic_image_series_cfg[series] or {}
	local temp_day = 0
	local temp_level = 0
	local can_active = false
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	for _,cfg in ipairs(cfg_map) do
		temp_day = cfg.skynumber_show or 0
		temp_level = cfg.show_level or 0
		can_active = self:SpecialTianshenCanActive(cfg.index)
		if (open_day >= temp_day and role_level >= temp_level) or can_active then
			cfg_list[#cfg_list+1] = cfg
		end
	end
	return cfg_list
end

function TianShenWGData:GetDefaultMagicImage()
	return self.tianshen_cfg_auto.magic_image[1]
end

----------------------------神通---------------------------------------------------------
-- 获取神通配置
function TianShenWGData:GetSTcfgByType(skill_type)
	return self.m_shentong_cfg[skill_type]
end

-- 获取神通配置
function TianShenWGData:GetSTcfgById(skill_id)
	return self.m_shentong_id_cfg[skill_id]
end

function TianShenWGData:GetSTCfgByActId(act_item_id)
	return self.m_shentong_actid_cfg[act_item_id]
end

-- 获取神通等级配置
function TianShenWGData:GetSTLevelcfg(skill_id,skill_level)
	return self.m_shentong_skill_cfg[skill_id][skill_level]
end

function TianShenWGData:GetSTMaxLevelcfg(skill_id)
	local st_cfg = self.m_shentong_skill_cfg[skill_id]

	if st_cfg then
		return st_cfg[#st_cfg]
	end

	return {}
end

-- 通过幻化形象id获取配置
-- is_back_tianshen 是否返回到天神配置（这个形象可能给到的是化魔形象，需要转换为天神配置，
-- 如果不需要转换，则返回化魔配置, 不传入字段则不检测化魔, 返回多一个参数，是否是化魔返回）
function TianShenWGData:GetImageModelByAppeId(appe_image_id, is_back_tianshen)
	for i,v in ipairs(self.tianshen_cfg_auto.magic_image) do
		if v.appe_image_id == appe_image_id then
			return v, false
		end
	end

	if is_back_tianshen ~= nil then	---这里检测是否需要化魔检测
		local tianshen_huamo_cfg = TianShenHuamoWGData.Instance:GetHuaMoAppeImageByImageId(appe_image_id)
		if tianshen_huamo_cfg then
			--- 检测是否还原原来天神表
			if is_back_tianshen then	---这里判断是否需要还原天神表
				return self:GetTianShenCfg(tianshen_huamo_cfg.index), true
			end
			return tianshen_huamo_cfg, true
		end
	end

	return nil, false
end

-- 通过幻化形象id获取名字
function TianShenWGData:GetTianshennameByAppeId(appe_image_id)
	for i,v in ipairs(self.tianshen_cfg_auto.magic_image) do
		if v.appe_image_id == appe_image_id then
			return v.bianshen_name
		end
	end
	return ""
end

-- 通过幻化形象id获取多段攻击的skillid
function TianShenWGData:GetSkillByAttackSeq(attack_index, is_calc_multi)
	local appe_image_id = RoleWGData.Instance:GetAttr("appearance_param")
	local tianshen_cfg = self:GetImageModelByAppeId(appe_image_id, true)
    local skill_cfg = self:GetNormalSkillList(tianshen_cfg, is_calc_multi)
    return tonumber(skill_cfg[attack_index])
end

-- 获取当前天神形象的主动技能
function TianShenWGData:GetTianShenSkill()
	local appe_image_id = RoleWGData.Instance:GetAttr("appearance_param")
	local tianshen_cfg = self:GetImageModelByAppeId(appe_image_id, true)

    local skill_cfg = self:GetTianshenSkillList(tianshen_cfg, true)
    local real_use_cfg = {}
    for i = 1, 4 do
    	if skill_cfg[i] ~= nil then
    		real_use_cfg[i] = skill_cfg[i]
    	end
    end
	-- 这块按需求写死挂机自动释放天神技能标识
	return real_use_cfg,{1,1,1,1,1,1,1,1,1,1}
end

-- 获取可以出战的天神
function TianShenWGData:GetTianShenAutoChuZhan()
	-- 已出战天神信息
	local skill_info = TianShenWGData.Instance:GetTianShenSkillInfoByMainView()
	for i=0,3 do
		if skill_info[i] then
			local bianshen_end_time = skill_info[i].bianshen_end_time
			local bianshen_cfg = self:GetTianShenCfg(skill_info[i].image_index)
			-- 天神冷卻時間戳
			bianshen_end_time = skill_info[i].next_bianshen_time
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			if server_time >= bianshen_end_time then
				skill_info[i].image_seq = bianshen_cfg.index
				return skill_info[i]
			end
		end
	end
end

-- 获取是否是天神出场技能
function TianShenWGData:GetTianShenChuChangSkill(skill_id)
	for k,v in pairs(self.tianshen_cfg_auto.magic_image) do
		if v.change_skill == skill_id then
			return true
		end
	end
	return false
end

-- 获取是否是天神普攻
function TianShenWGData:GetIsTianShenNormalSkill(skill_id)
	return self.normal_skill_dic[skill_id] ~= nil
end

function TianShenWGData:GetIsTianShenNormalSkillWuXingIndex(skill_id)
	return self.normal_skill_dic[skill_id]
end

-- 缓存神通数据
function TianShenWGData:SetSTServerInfo(protocol)
	if not self.st_server_info then
		self.st_server_info = {}
		for k,v in pairs(self.m_shentong_id_cfg) do
			self.st_server_info[v.skill_id] = {level = 0 ,skill_id = v.skill_id}
		end
	end
	for k,v in pairs(protocol.shentong_list) do
		self.st_server_info[v.skill_id] = v
	end

end

-- 获取神通数据
function TianShenWGData:GetSTServerInfo()
	 return self.st_server_info or {}
end

-- 获取神通数据
function TianShenWGData:GetSTServerInfoBySkillId(skill_id)
	return self.st_server_info and self.st_server_info[skill_id] or nil
end

-- 神通红点，忽略是否提醒变强检查
function TianShenWGData:GetSTRemindWithoutInvateTip(skill_id)
	if not skill_id then return false end
	local data = self:GetSTServerInfoBySkillId(skill_id)
	if not data then return false end

	local skill_cfg = TianShenWGData.Instance:GetSTcfgById(data.skill_id)
	local max_flag = data.level >= skill_cfg.max_level
	if max_flag then return false end

	local role_level = GameVoManager.Instance:GetMainRoleVo().level

	local is_active = data.level > 0
	-- 已激活
	if is_active then
		local up_skill_cfg = TianShenWGData.Instance:GetSTLevelcfg(data.skill_id,data.level)
		if role_level < up_skill_cfg.uplevel_condition then
			return false
		end
		local has_num = ItemWGData.Instance:GetItemNumInBagById(up_skill_cfg.uplevel_item_id)
		local item_cfg = ItemWGData.Instance:GetItemConfig(up_skill_cfg.uplevel_item_id)

		if has_num >= up_skill_cfg.uplevel_item_num and item_cfg.limit_level <= role_level then
			return true
		end
	else
		local had_active_image = self:IsActivation(skill_cfg.active_seq)
		had_active_image = skill_cfg.active_seq == -1
		local has_num = ItemWGData.Instance:GetItemNumInBagById(skill_cfg.active_item_id)
		local item_cfg = ItemWGData.Instance:GetItemConfig(skill_cfg.active_item_id)
		if had_active_image and has_num >= skill_cfg.active_item_num and item_cfg.limit_level <= role_level then
			return true
		end
	end
	return false
end

-- 神通红点
function TianShenWGData:GetSTRemind(skill_id)
	if not skill_id then return false end
	local data = self:GetSTServerInfoBySkillId(skill_id)
	if not data then return false end

	local skill_cfg = TianShenWGData.Instance:GetSTcfgById(data.skill_id)
	local max_flag = data.level >= skill_cfg.max_level
	if max_flag then return false end

	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local is_active = data.level > 0
	-- 已激活
	if is_active then
		local up_skill_cfg = TianShenWGData.Instance:GetSTLevelcfg(data.skill_id,data.level)
		if role_level < up_skill_cfg.uplevel_condition then
			return false
		end
		local has_num = ItemWGData.Instance:GetItemNumInBagById(up_skill_cfg.uplevel_item_id)
		local item_cfg = ItemWGData.Instance:GetItemConfig(up_skill_cfg.uplevel_item_id)
		if has_num >= up_skill_cfg.uplevel_item_num and item_cfg.limit_level <= role_level then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENTONG, 1, function ()
				ViewManager.Instance:Open(GuideModuleName.TianShenShenTongView, nil, "all", {operate_param = data})
				return true
			end)
			return true
		end
	else
		local had_active_image = self:IsActivation(skill_cfg.active_seq)
		had_active_image = skill_cfg.active_seq == -1
		local has_num = ItemWGData.Instance:GetItemNumInBagById(skill_cfg.active_item_id)
		local item_cfg = ItemWGData.Instance:GetItemConfig(skill_cfg.active_item_id)
		if had_active_image and has_num >= skill_cfg.active_item_num and item_cfg.limit_level <= role_level then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENTONG, 1, function ()
				ViewManager.Instance:Open(GuideModuleName.TianShenShenTongView, nil, "all", {operate_param = data})
				return true
			end)
			return true
		end
	end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENTONG, 0, function ()
		return true
	end)
	return false
end

-- 神通红点
function TianShenWGData:GetSTAllRemind()
	local st_server_info = self:GetSTServerInfo()
	for k,v in pairs(st_server_info) do
		if self:GetSTRemind(v.skill_id) then
			return 1
		end
	end
	return 0
end

-- 神饰红点
function TianShenWGData:TianShenShenShiRemind()
	--宝匣红点绑定神饰红点--特殊处理
	local all_red = 0
	local shenshi_red = 0
	if self:CheckTianShenBoxRemind() > 0 then
		all_red = 1
	end

	if self:GetTianShenShenShiAllRefineRed() > 0 then
		all_red = 1
	end

	for k,v in pairs(self.magic_image_cfg) do
		if self:CheckEquipShenShiRed(k) == 1 then
			local data_cache = TianShenWGData.Instance:GetMagicImageListCfg(v.series)
			self.select_shenshi_index = 1
			for t,q in pairs(data_cache) do
 				if q.index == v.index then
            		self.select_shenshi_index = t
            		break
        		end
        	end
			self.shenshi_series = v.series
			if not self.had_click_shenshi_stronger then
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENSHI,1,function ()
					ViewManager.Instance:Open(GuideModuleName.TianShenView,TabIndex.tianshen_shenshi)
					self.had_click_shenshi_stronger = true
					MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENSHI,0,nil)
				end)
			end
			all_red = 1
			shenshi_red = 1
		end
	end

	if shenshi_red == 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENSHI,0,nil)
	end

	return all_red
end

-- 检测分类天神红点
function TianShenWGData:CheckTotalSeries()
	local list = {}
	local flag = 0
	for k,v in pairs(self.magic_image_series_cfg) do
		flag = self:CheckSeries(k)
		list[k] = flag
	end

	return list
end

-- 检测分类天神红点
function TianShenWGData:CheckSeries(series)
	local cfg_list = self:GetMagicImageListCfg(series)
	local tianshen_info = nil
	local series_list = nil

	local flag = 0
	for k, v in pairs(cfg_list) do
		if self:IsActivation(v.index) then
			if 1 == self:CheckEquipShenShiRed(v.index) then
				return 1
			end
		end
	end

	return 0
end

-- 跳转页签，先写死
function TianShenWGData:JumpIndex(equip_info)
	local list = {to_ui_name = 0, to_ui_param = 1, open_param = 1}
	if not equip_info then return list end
	local item_cfg = ItemWGData.Instance:GetItemConfig(equip_info.item_id)
	if not item_cfg then return list end
	if item_cfg.color == GameEnum.ITEM_COLOR_RED then
		if equip_info.star_level >= 3 then
			list.open_param = 2
		elseif equip_info.star_level >= 2 then
			list.open_param = 1
			list.to_ui_param = 2
		end
	end
	return list
end

-- 是否显示合成按钮
function TianShenWGData:NeedHeChengBtn(equip_info)
	if 0 < equip_info.stren_level then
		return false
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(equip_info.item_id)
	if "" == item_cfg.open_panel then
		return false
	end

	local list = self.need_hecheng_btn[item_cfg.color]
	if not list then
		return false
	end

	return nil ~= list[equip_info.star_level]
end

function TianShenWGData:GetSpecialSkill(skill_id, skill_level)
	skill_id = tonumber(skill_id)
	return self.special_skill_level_cfg[skill_id] and self.special_skill_level_cfg[skill_id][skill_level]
end

function TianShenWGData:GetShenQiDataList()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0
	local cfg_map = {}
	cfg_map = self.tianshen_shenqi_cfg_auto.shenqi or {}
	local cfg_list = {}
	local temp_day = 0
	local temp_level = 0
	local can_active = false
	local active_status = 0
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	for _,cfg in ipairs(cfg_map) do
		temp_day = cfg.skynumber_show or 0
		temp_level = cfg.show_level or 0
		active_status = self:IsShenQiActivation(cfg.index) and 1 or 0
		can_active = self:GetShenQiActivationRemind(cfg.index) == 1
		if (open_day >= temp_day and role_level >= temp_level) or can_active or active_status == 1 then
			cfg_list[#cfg_list+1] = cfg
		end
	end

	self.tianshen_shenqi_data_list = {}
	if IsEmptyTable(self.tianshen_shenqi_data_list) then
		for k,v in pairs(cfg_list) do
			local info = {}
			info.active_status = self:IsShenQiActivation(v.index) and 1 or 0
			if info.active_status == 0 then
				local flag = TianShenWGData.Instance:GetShenQiActivationRemind(v.index)
				info.can_active_status = flag
			else
				info.can_active_status = 0
			end
			info.index = v.index
			info.series = v.series
			info.stuff_id = v.stuff_id
			info.stuff_num = v.stuff_num
			info.name = v.name
			info.icon = v.icon
			info.ts_res_idui = v.ts_res_idui
			info.sort_index = v.sort_index
			info.is_default_show = v.is_default_show
			info.sq_index = v.tianshen_index
			info.condition_type = v.condition_type
			info.skynumber_show = v.skynumber_show
			info.show_level = v.show_level
			table.insert(self.tianshen_shenqi_data_list, info)
		end
	end

	table.sort(self.tianshen_shenqi_data_list, function ( a, b )
						if a.can_active_status == 1 and b.can_active_status == 1 or a.can_active_status == 0 and b.can_active_status == 0 then
							if a.active_status == 1 and b.active_status == 1 then
								return a.sort_index < b.sort_index
							elseif a.active_status == 1 or b.active_status == 1 then
								return a.active_status > b.active_status
							end
						else
							return a.can_active_status > b.can_active_status
						end
						return a.sort_index < b.sort_index
					end)
	

	local show_tianshen_shenqi_data_list = {}

	for k, v in pairs(self.tianshen_shenqi_data_list) do
        if self:GetShenQiItemIsCanShow(v) then
            table.insert(show_tianshen_shenqi_data_list, v)
        end
    end

	return show_tianshen_shenqi_data_list
	-- return self.tianshen_shenqi_data_list
	--return cfg_list
end

function TianShenWGData:GetShenQiItemIsCanShow(data)
	if not data then
		return false
	end

	if data.active_status == 1 or data.can_active_status == 1 then
		return true
	end

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()

    if data.is_default_show == 1 then
        if data.condition_type == 2 then
			return open_day >= data.skynumber_show and role_level >= data.show_level
		else
			return open_day >= data.skynumber_show or role_level >= data.show_level
		end
    end

    return false
end

function TianShenWGData:GetShenQiByIndex(index)
	index = tonumber(index)
	return self.shenqi_cfg2[index]
end

function TianShenWGData:GetShenQiByTianShenIndex(index)
	return self.shenqi_cfg3[index]
end

function TianShenWGData:GetShenQiCfgByActItemId(act_item_id)
	return self.shenqi_cfg4[act_item_id]
end

function TianShenWGData:GetShenQiByStuffId(item_id)
	for k,v in pairs(self.shenqi_cfg2) do
		if v.stuff_id == item_id then
			return v
		end
	end

	return nil
end

--默认模型
function TianShenWGData:SetCurShenQiIndex(series, sub_index)
	-- --默认模型
	self.default_shenqi_series = series
	self.default_shenqi_sub_index = sub_index
end
function TianShenWGData:GetCurShenQiIndex()
	-- --默认模型
	return self.default_shenqi_series or 1, self.default_shenqi_sub_index or 1
end

function TianShenWGData:GetShenQiOther()
	return self.tianshen_shenqi_cfg_auto.other[1]
end

function TianShenWGData:GetShenQiStrengthCfg(index, level)
	return self.shenqi_strength_cfg[index] and self.shenqi_strength_cfg[index][level]
end

function TianShenWGData:GetShenQiStarCfg(index, star)
	return self.shenqi_star_cfg[index] and self.shenqi_star_cfg[index][star]
end

-- 获取神器面板强化属性
function TianShenWGData:GetShenQiStarAttr(index, star, need_next)
	local list = {}
	local list2 = {}
	local attr_cfg = self:GetShenQiStarCfg(index, star)

	local next_attr_cfg = nil
	if need_next then
		next_attr_cfg = self:GetShenQiStarCfg(index, star + 1)
	end
	local have_next = false
	if not attr_cfg then return list end
	local attr_type = nil
	local tab = nil
	local is_per = false
	local is_show = false

	local skill_info = self:GetShenQiSkillInfo(index, star)
	local skill_cfg = {}
	local is_active = self:IsShenQiActivation(index)
	local other_add = 0
	for k,v in pairs(attr_cfg) do
		is_show = false
		if Language.Common.TipsAttrNameList[k] then
			--strength_max_lv	spirit_max
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
			tab = {}
			tab.attr_name = "<color=#A29FAAFF>" .. Language.Common.TipsAttrNameList[k] .. "：</color>"
			other_add = 0
			-- for t,q in pairs(skill_info) do
			-- 	if q.is_active then
			-- 		skill_cfg = self:GetShenQiSkillCfg(q.skill,attr_cfg.skill_level)
			-- 		if  skill_cfg[k] and skill_cfg[k] > 0 then
			-- 			other_add = other_add + skill_cfg[k]
			-- 		end
			-- 	end
			-- end
			if is_per and 0 ~= v then
				tab.attr_value = "<color=#FFFFFF>" .. ((v + other_add)/100) .. "%</color>"
			--elseif "strength_max_lv" == k then
			--	tab.attr_value = "<color=#FFFFFF>" .. v .. "</color>"
			--elseif "spirit_max" == k then
			--	tab.attr_value = "<color=#FFFFFF>" .. (v/100) .. "%</color>"
			else
				tab.attr_value = "<color=#FFFFFF>" .. (v + other_add) .. "</color>"
			end

			tab.attr_type = k
			if need_next and next_attr_cfg and is_active then
				have_next = true
				if is_per then
					tab.attr_add = ((next_attr_cfg[k] - attr_cfg[k])/100) .. "%"
			--	elseif "strength_max_lv" == k then
			--		tab.attr_add = next_attr_cfg[k]
			--	elseif "spirit_max" == k then
			--		tab.attr_add = (next_attr_cfg[k]/100) .. "%"
				else
					tab.attr_add = next_attr_cfg[k] - attr_cfg[k]
				end
			end

			is_show = TianShenWGData.FixedAttr[k]
			if is_show then
				-- if "strength_max_lv" == k then
				-- 	list2[1] = tab
				-- elseif "spirit_max" == k then
				-- 	list2[2] = tab
				-- else
					table.insert(list, tab)
				--end
			end
		end
	end



	list = self:SortAttr(list)
	-- if list2[1] then
	-- 	table.insert(list, list2[1])
	-- end
	-- if list2[2] then
	-- 	table.insert(list, list2[2])
	-- end
	-- SortTools.SortAsc(list, "sort_type")
--{1 = {attr_name = "攻 击", attr_value = 0, attr_type = shengming_max, attr_add = 667}}
	return list,have_next
end

function TianShenWGData:GetShenQiSkillInfo(index, star)
	if not self.shenqi_skill_info then
		self.shenqi_skill_info = {}
	end
	local state = self:IsShenQiActivation(index)
	if not self.shenqi_skill_info[index] then
		local attr_cfg = {}
		self.shenqi_skill_info[index] = {}
		local skill_num = 0
		for i=0,5 do
			attr_cfg = self:GetShenQiStarCfg(index, i)
			if attr_cfg and attr_cfg.skill > 0 then
				skill_num = skill_num + 1
				self.shenqi_skill_info[index][skill_num] = {}
				self.shenqi_skill_info[index][skill_num].star = attr_cfg.star_lv
				self.shenqi_skill_info[index][skill_num].skill = attr_cfg.skill
			end
		end
	end
	for k ,v in pairs(self.shenqi_skill_info[index]) do
		self.shenqi_skill_info[index][k].is_active = self.shenqi_skill_info[index][k].star <= star and state
	end
	return self.shenqi_skill_info[index]
end

function TianShenWGData:GetShenQiSkillCfg(id,level)
	level = level or 1
	if self.shenqi_skill_cfg and self.shenqi_skill_cfg[id] then
		return self.shenqi_skill_cfg[id][level] or {}
	end
	return {}
end

function TianShenWGData:GetShenQiSkillCfgById(id)
	return (self.shenqi_skill_cfg or {})[id]
end

function TianShenWGData:GetWaiGuanList(index)
	return self.shenqi_waiguan_cfg[index]
end

function TianShenWGData:GetWaiGuanCfg(index)
	return self.shenqi_waiguan_cfg2[index]
end

function TianShenWGData:GetWaiGuanStarListCfg(index)
	return self.shenqi_waiguan_star[index]
end

function TianShenWGData:GetWaiGuanStarCfg(index, star)
	return self.shenqi_waiguan_star[index] and self.shenqi_waiguan_star[index][star]
end

function TianShenWGData:GetWaiGuanCfgByStuff(stuff_id)
	for k,v in pairs(self.shenqi_waiguan_cfg2) do
		if v.stuff_id == stuff_id then
			return v
		end
	end
	return nil
end

function TianShenWGData:GetWaiGuanInfo(waiguan_id)
	local waiguan_cfg = self:GetWaiGuanCfg(waiguan_id)
	if not waiguan_cfg then return nil end

	local shenqi_info = self:GetTianShenShenQiInfo(waiguan_cfg.shenqi_index)
	if not shenqi_info then return nil end

	for i=1,#shenqi_info.active_huandhua_ids do
		if waiguan_id == shenqi_info.active_huandhua_ids[i].huandhua_ids then
			return shenqi_info.active_huandhua_ids[i]
		end
	end

	return nil
end

function TianShenWGData:IsShenQiActivation(index)
	local shenqi_info = self:GetTianShenShenQiInfo(index)
	return shenqi_info and 1 == shenqi_info.is_active or false
end

function TianShenWGData:IsWaiGuanHuanHua(index, waiguan_id)
	local shenqi_info = self:GetTianShenShenQiInfo(index)
	if not shenqi_info then return false end

	return shenqi_info.cur_huanhua_id == waiguan_id
end

function TianShenWGData:GetWaiGuanHuanHua(index)
	local shenqi_info = self:GetTianShenShenQiInfo(index)
	if not shenqi_info then return -1 end

	return shenqi_info.cur_huanhua_id
end

-- 获取神器面板强化属性
function TianShenWGData:GetShenQiWaiGuanAttr(index)
	local list = {}
	local waiguan_info = self:GetWaiGuanInfo(index)
	local star_lv = waiguan_info and waiguan_info.star_lv or 0
	local star_cfg = self:GetWaiGuanStarCfg(index, star_lv)
	local next_cfg = waiguan_info and self:GetWaiGuanStarCfg(index, star_lv + 1) or star_cfg

	if not star_cfg then return list end
	local attr_type = nil
	local tab = nil
	local is_show = false
	local is_per = false
	for k,v in pairs(star_cfg) do
		if Language.Common.AttrName[k] then
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
			tab = {}
			tab.attr_name = Language.Common.AttrName[k]
			tab.attr_type = k
			local attr_value = waiguan_info and v or 0
			if is_per and attr_value > 0 then
				tab.attr_value = (attr_value / 100) .. "%"
			else
				tab.attr_value = attr_value
			end

			if next_cfg then
				tab.attr_add = next_cfg[k] - attr_value
				if is_per and 0 < tab.attr_add then
					tab.attr_add = ((tab.attr_add) / 100) .. "%"
				end
			end

			is_show = TianShenWGData.FixedAttr[k] or 0 ~= v or (next_cfg and 0 ~= next_cfg[k])
			if is_show then
				table.insert(list, tab)
			end
		end
	end
	list = self:SortAttr(list)
--{1 = {attr_name = "攻 击：", attr_value = 0, attr_type = shengming_max, attr_add = 667}}
	return list
end

-- 转换神器配置的属性
function TianShenWGData:ExplainAttr(cfg)
	local attr_list = {}
	if not cfg then return attr_list end
	local attr_cfg = nil
	for i=1,10 do
		if cfg["attr_type_" .. i] then
			attr_cfg = self.attr_table_name_cfg[cfg["attr_type_" .. i]]
			if attr_cfg then
				attr_list[attr_cfg.attr_var] = cfg["attr_value_" .. i]
			end
		end
	end

	return attr_list
end

function TianShenWGData:GetWaiGuanZhanLi(index)
	local list = {}
	local cfg = self:GetWaiGuanCfg(index)
	if not cfg then return 0 end

	local attr_list = AttributeMgr.GetAttributteByClass(cfg)
	local zhanli = AttributeMgr.GetCapability(attr_list)

	local waiguan_info = self:GetWaiGuanInfo(index)
	if waiguan_info then
		cfg = self:GetWaiGuanStarCfg(index, waiguan_info.star_lv)
		attr_list = AttributeMgr.GetAttributteByClass(cfg)
		zhanli = zhanli + AttributeMgr.GetCapability(attr_list)
	end

	return zhanli
end

-- 神器总战力
function TianShenWGData:GetShenQiZhanLi(index)
	local shenqi_info = self:GetTianShenShenQiInfo(index)
	local zhanli = 0

	if not shenqi_info or not self:IsShenQiActivation(index) then
		return zhanli
	end

	-- 基础
	local attr_cfg = {}
	local attr_list = {}

	-- 升星战力
	if 0 <= shenqi_info.star then
		attr_cfg = self:GetShenQiStarCfg(index, shenqi_info.star)
		attr_list = AttributeMgr.GetAttributteByClass(attr_cfg)
		zhanli = zhanli + AttributeMgr.GetCapability(attr_list)
	end

	return math.floor(zhanli)
end

--获取预览战力
function TianShenWGData:GetPreViewCap(index)
	local zhanli = 0
	local attr_cfg = nil
	local attr_list = nil

	-- 基础
	attr_cfg = self:GetShenQiStarCfg(index, 0)
	attr_list = AttributeMgr.GetAttributteByClass(attr_cfg)
	zhanli = AttributeMgr.GetCapability(attr_list)
	return zhanli
end

-- 获取武器
-- index 天神索引
-- waiguan_id 可以指定外观
-- appe_image_id 优先化魔的属性
function TianShenWGData:GetTianShenWeapon(index, waiguan_id, is_show_waiguan, appe_image_id)
    local shenqi_cfg = self:GetShenQiByTianShenIndex(index)
	local cur_huanhua_id = -1
	if is_show_waiguan then
		if waiguan_id then
			cur_huanhua_id = waiguan_id
		elseif shenqi_cfg then
			cur_huanhua_id = self:GetWaiGuanHuanHua(shenqi_cfg.index)
		end
	end
	
	local res_id = 0
	local is_weapon_anim = false  --天神武器是否自带动作
	if 0 == cur_huanhua_id then
		if shenqi_cfg then
			res_id = shenqi_cfg.ts_res_id1
			is_weapon_anim = shenqi_cfg.is_weapon_anim == 1
		end
    elseif -1 == cur_huanhua_id then
		if shenqi_cfg then
			res_id = shenqi_cfg.ts_res_id
			is_weapon_anim = shenqi_cfg.is_weapon_anim == 1
		end
    else
        local waiguan_cfg = self:GetWaiGuanCfg(cur_huanhua_id)
		if waiguan_cfg then
			res_id = waiguan_cfg.facade_res_id
			is_weapon_anim = waiguan_cfg.is_weapon_anim == 1
		end
    end

	---这里加一下化魔的武器属性吧
	local tianshen_huamo_cfg = TianShenHuamoWGData.Instance:GetHuaMoAppeImageByImageId(appe_image_id)
	if tianshen_huamo_cfg then
		res_id = tianshen_huamo_cfg.ts_res_id
	end

    local bundle, asset = ResPath.GetTianShenShenQiPath(res_id)
    return bundle, asset, is_weapon_anim
end

function TianShenWGData:TianShenShenQiRemind()
	local flag = 0
	flag = self:TianShenShenQiSeriesListFirstRemind()
	if flag == 1 then
		return 1
	end

	flag = self:TianShenShenQiWaiGuanFirstRemind()
	if flag == 1 then
		return 1
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENQI, 0, function()
		return true
	end)
	return 0
end

--外面调用，判断格子是否有红点，不能使用含有InvateTip的红点方法判断，使用了有可能会导致提示消失
function TianShenWGData:TianShenShenQiSeriesListRemindWithoutInvateTip()
	local list = {}
	for k,v in pairs(self.shenqi_cfg) do
		list[k] = self:GetShenQiSeriesRemindWithoutInvateTip(k)
	end
	return list
end

--新增这个方法，用来判断红点的话， 只需要有一个可操作立刻返回1就可以
function TianShenWGData:TianShenShenQiSeriesListFirstRemind()
	for k,v in pairs(self.shenqi_cfg) do
		local flag = self:GetShenQiSeriesRemind(k)
		if flag == 1 then return 1 end
	end
	return 0
end

function TianShenWGData:GetShenQiSeriesRemind(series)
	local series_list = self.shenqi_cfg[series]
	local flag = 0
	for k,v in pairs(series_list) do
		flag = self:GetShenQiOneRemind(v.index)
		if 1 == flag then return 1 end
	end
	return 0
end

function TianShenWGData:GetShenQiSeriesRemindWithoutInvateTip(series)
	local series_list = self.shenqi_cfg[series]
	local flag = 0
	for k,v in pairs(series_list) do
		flag = self:GetShenQiOneRemindWithoutInvateTip(v.index)
		if 1 == flag then return 1 end
	end
	return 0
end

function TianShenWGData:GetShenQiOneRemindWithoutInvateTip(index)
	local flag = self:GetShenQiActivationRemind(index)
	if 1 == flag then
		return 1
	end

	flag = self:GetShenQiShenXingRemind(index)
	if 1 == flag then
		return 1
	end

	flag = self:GetShenQiStrengthRemind(index)
	if 1 == flag then
		return 1
	end

	flag = self:GetShenQiSprirtRemind(index)
	if 1 == flag then
		return 1
	end
	return 0
end

function TianShenWGData:GetShenQiOneRemind(index)
	local flag = self:GetShenQiActivationRemind(index)
	if 1 == flag then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENQI, 1, function ()
			TianShenWGCtrl.Instance:Open(TabIndex.tianshen_shenQi, {to_ui_param = index})
			return true
		end)
		return 1
	end

	flag = self:GetShenQiShenXingRemind(index)
	if 1 == flag then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENQI, 1, function ()
			TianShenWGCtrl.Instance:Open(TabIndex.tianshen_shenQi, {to_ui_param = index, open_shengxing = true})
			return true
		end)
		return 1
	end

	flag = self:GetShenQiStrengthRemind(index)
	if 1 == flag then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENQI, 1, function ()
			TianShenWGCtrl.Instance:Open(TabIndex.tianshen_shenQi, {to_ui_param = index, open_strength = true})
			return true
		end)
		return 1
	end

	flag = self:GetShenQiSprirtRemind(index)
	if 1 == flag then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENQI, 1, function ()
			TianShenWGCtrl.Instance:Open(TabIndex.tianshen_shenQi, {to_ui_param = index, open_sprirt = true})
			return true
		end)
		return 1
	end

	return 0
end

function TianShenWGData:GetShenQiActivationRemind(index)
	local state = self:IsShenQiActivation(index)
	if state then return 0 end

	local shenqi_cfg = self.shenqi_cfg2[index]
	if not shenqi_cfg then return 0 end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(shenqi_cfg.stuff_id)
	if item_num >= shenqi_cfg.stuff_num then
		return 1
	end

	return 0
end

function TianShenWGData:GetShenQiShenXingRemind(index)
	local state = self:IsShenQiActivation(index)
	if not state then return 0 end

	local shenqi_info = self:GetTianShenShenQiInfo(index)
	if not shenqi_info then return 0 end

	local next_cfg = self:GetShenQiStarCfg(index, shenqi_info.star + 1)
	if not next_cfg then return 0 end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(next_cfg.stuff_id)
	if item_num >= next_cfg.stuff_num then
		return 1
	end

	return 0
end

function TianShenWGData:GetShenQiStrengthRemind(index)
	local state = self:IsShenQiActivation(index)
	if not state then return 0 end

	local shenqi_info = self:GetTianShenShenQiInfo(index)
	if not shenqi_info then return 0 end
	local star_cfg = self:GetShenQiStarCfg(index, shenqi_info.star)
	if not star_cfg then return 0 end

	local next_cfg = self:GetShenQiStrengthCfg(index, shenqi_info.level + 1)
	if not next_cfg then return 0 end
	if star_cfg.strength_max_lv <= shenqi_info.level then return 0 end


	local item_num = ItemWGData.Instance:GetItemNumInBagById(next_cfg.strength_stuff_id)
	if item_num >= next_cfg.strength_stuff_num then
		return 1
	end

	return 0
end

function TianShenWGData:GetShenQiSprirtRemind(index)
	return 0
end

--新增这个方法，用来判断红点的话， 只需要有一个可操作立刻返回1就可以
function TianShenWGData:TianShenShenQiWaiGuanFirstRemind()
	for k,v in pairs(self.shenqi_cfg) do
		local flag = self:GetShenQiWaiGuanSeriesRemind(k)
		if flag == 1 then return 1 end
	end
	return 0
end


function TianShenWGData:TianShenShenQiWaiGuanRemind()
	local list = {}
	for k,v in pairs(self.shenqi_cfg) do
		list[k] = self:GetShenQiWaiGuanSeriesRemind(k)
	end

	return list
end

function TianShenWGData:GetShenQiWaiGuanSeriesRemind(series)
	local series_list = self.shenqi_cfg[series]
	local flag = 0
	for k,v in pairs(series_list) do
		flag = self:GetShenQiWaiGuanRemind(v.index)
		if 1 == flag then return 1 end
	end

	return 0
end

function TianShenWGData:GetShenQiWaiGuanRemind(index)
	local waiguan_list = self:GetWaiGuanList(index)
	if not waiguan_list then return 0 end
	local flag = 0
	for k,v in pairs(waiguan_list) do
		flag = self:GetShenQiWaiGuanOneRemind(v.waiguan_id)
		if 1 == flag then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_SHENQI, 1, function ()
				TianShenWGCtrl.Instance:Open(TabIndex.tianshen_shenQi, {to_ui_param = index, open_fashion = true, shenqi_index = v.shenqi_index})
				return true
			end)
			return 1
		end
	end

	return 0
end

--神兵外观判断是否激活
function TianShenWGData:GetShenQiWaiGuanOneAct(waiguan_id)
	local waiguan_info = self:GetWaiGuanInfo(waiguan_id)
	if not waiguan_info then
		return false
	end
	return true
end

function TianShenWGData:GetShenQiWaiGuanOneRemind(waiguan_id)
	local item_num = 0
	local waiguan_info = self:GetWaiGuanInfo(waiguan_id)
	if not waiguan_info then
		local waiguan_cfg = self:GetWaiGuanCfg(waiguan_id)
		item_num = ItemWGData.Instance:GetItemNumInBagById(waiguan_cfg.stuff_id)
		if item_num >= waiguan_cfg.stuff_num then
			return 1
		end
	else
		local next_cfg = self:GetWaiGuanStarCfg(waiguan_id, waiguan_info.star_lv + 1)
		if next_cfg then
			item_num = ItemWGData.Instance:GetItemNumInBagById(next_cfg.stuff_id)
			if item_num >= next_cfg.stuff_num then
				return 1
			end
		end
	end

	return 0
end

-- 天神出战红点
function TianShenWGData:GetTianShenBattleRemind()
	local remind = 0
	remind = remind + (self:SpecialTianshenCanChuZhan() and 1 or 0) 		-- 是否有天神可以出战
	remind = remind + (self:CanActivationCell() and 1 or 0) 				-- 是否可激活出战位
	return remind
end

-- 天神出战红点回调，提示变强
function TianShenWGData:TianShenBattleRemindCallBack(remind_name, num)
	if num > 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BATTLE, 1, function()
			ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_battle)
			return true
		end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TIAN_SHEN_BATTLE, 0)
	end
end

function TianShenWGData:GetTianShenSkillData(data)
	local data_list = {}
	if not data or IsEmptyTable(data) then
		return data_list
	end
	local title_img_list = {"a3_sl_bq_zd", "a3_sl_bq_jj", "a3_sl_bq_bd"}
	data_list.data_list_1 = {}
	data_list.data_list_2 = {}
	data_list.data_list_3 = {}

	--主动技能配置信息
	local zhu_cfg = TianShenWGData.Instance:GetTianShenZhuSkillByItemid(data.item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local tianshen_info = self:GetTianShenActItemCfgByActId(data.item_id)
	local tianshen_index = tianshen_info and tianshen_info.index or 0
	if zhu_cfg and item_cfg then
		if #zhu_cfg > 1 then
			data_list.tab_title_name_1 = Language.Common.ZhuDong
			data_list.data_list_1.title_img = title_img_list[1]
		end
		
		local tianshen_shenshi_data = TianShenWGData.Instance:GetShenShiEquipInfo(tianshen_index)
		for i=1,#zhu_cfg do
			local skill_id = tonumber(zhu_cfg[i])

			-- local skill_level = TianShenWGData.Instance:GetTianShenSkillLv(tianshen_index, shenshi_rank, skill_id) or 1
			--策划需求:Tips的主动技能等级按最大级配置显示
			local skill_level = SkillWGData.Instance:GetSkillClientMaxLv(skill_id)

			local cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
			local cfg2 = SkillWGData.Instance:GetTianShenSkillDes(skill_id,skill_level,true)
			if cfg then
				data_list.data_list_1[i] = {}
				local bundle, asset = ResPath.GetSkillIconById(cfg.icon_resource)
				data_list.data_list_1[i].bundle = bundle
				data_list.data_list_1[i].asset = asset
				data_list.data_list_1[i].color = item_cfg.color
				data_list.data_list_1[i].click_func = BindTool.Bind(self.ClickSkillFunc, self, cfg,cfg2, tonumber(zhu_cfg[i]), data.item_id,skill_level, tianshen_index)
			end
		end
	end

	-- --组合技能配置信息
	-- local ts_image_cfg = self:GetTianShenCfg(tianshen_index)
	-- if ts_image_cfg and ts_image_cfg.ts_union_skills and item_cfg then
	-- 	local temp_cfg, temp_data_list_2, bundle, asset
	-- 	local temp_idx_list = Split(ts_image_cfg.ts_union_skills or "", "|")
	-- 	for i, idx in ipairs(temp_idx_list) do
	-- 		temp_cfg = self:GetUnionSkillCfgById(tonumber(idx))
	-- 		if not IsEmptyTable(temp_cfg) then
	-- 			temp_data_list_2 = {}
	-- 			bundle, asset = ResPath.GetSkillIconById(temp_cfg.skill_icon)--技能图标
	-- 			temp_data_list_2.bundle = bundle
	-- 			temp_data_list_2.asset = asset
	-- 			temp_data_list_2.color = item_cfg.color
	-- 			temp_data_list_2.click_func = BindTool.Bind(self.ClickUnionSkillFunc, self, temp_cfg)
	-- 			table.insert(data_list.data_list_2, temp_data_list_2)
	-- 		end
	-- 	end
	--	data_list.data_list_2.title_img = title_img_list[2]
	-- end

	--被动技能配置信息(策划要求屏蔽)
	
	local bei_cfg = TianShenWGData.Instance:GetSpecialImagePasvSkillCfgByItemId(data.item_id)
	if bei_cfg and item_cfg then
		if #bei_cfg + 1 > 0 then
			data_list.tab_title_name_3 = Language.Common.BeiDong
		end
		for i = 0, #bei_cfg + 1 do
			if bei_cfg[i] then
				data_list.data_list_3[i + 1] = {}
				local bundle, asset = ResPath.GetSkillIconById(bei_cfg[i].skill_icon)
				data_list.data_list_3[i + 1].bundle = bundle
				data_list.data_list_3[i + 1].asset = asset
				data_list.data_list_3[i + 1].color = item_cfg.color
				data_list.data_list_3[i + 1].click_func = BindTool.Bind(self.ClickBeiDongSkillFunc, self, bei_cfg[i])
			end
		end
		data_list.data_list_3.title_img = title_img_list[3]
	end
	

	return data_list
end

function TianShenWGData:ClickUnionSkillFunc(data)
	if data == nil  then
		return
	end

	local capability = self:GetTianShenUnionSkillCap(data)
	local show_data = {
		icon = data.skill_icon,
		top_text = data.skill_name, -- ToColorStr(, TIANSHEN_DARK_COLOR3B[data.skill_color or 1]),
		body_text = data.skill_describe,
		limit_text = nil,
		x = 0,
		y = 0,
		set_pos2 = true,

		capability = capability,
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function TianShenWGData:ClickBeiDongSkillFunc(data)
	if data == nil  then
		return
	end

	local capability = self:GetTianShenSpecialSkillCap(data)
	local show_data = {
		icon = data.skill_icon,
		top_text = data.skill_name,
		body_text = data.skill_describe,
		limit_text = nil,
		x = 0,
		y = 0,
		set_pos2 = true,
		skill_id = data.skill_id,
		skill_box_type = SKILL_BOX_TYPE.TIANSHEN_PASSSKILL,
		is_active_skill = false,
		tianshen_index = data.image_id,
		hide_next = true,

		capability = capability,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function TianShenWGData:ClickSkillFunc(cfg,cfg2, data, item_id,skill_level, tianshen_index)
	if nil == cfg then
		return
	end

	local skill_name = ""
	local ts_skill_cfg = SkillWGData.Instance:GetTianShenSkillCfg(data)
	if ts_skill_cfg then
		skill_name = ts_skill_cfg.skill_name
	end

	local body_text = cfg2.description
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if DisplayItemTip.Display_type.TIANSHEN == item_cfg.is_display_role then
		body_text = SkillWGData.Instance:GetTianShenSkillDes(data, skill_level,true)
	end

	local show_data = {
		icon = cfg.icon_resource,
		top_text = skill_name,
		body_text = body_text,
		skill_id = ts_skill_cfg.skill_id,
		x = 0,
		y = 0,
		set_pos2 = true,
		tianshen_index = tianshen_index,
		skill_box_type = ts_skill_cfg.skill_type == SKILL_TYPE.SKILL_4 and SKILL_BOX_TYPE.TIANSHEN_SKILL or SKILL_BOX_TYPE.TIANSHEN_PASSSKILL,
		is_active_skill = ts_skill_cfg.skill_type == SKILL_TYPE.SKILL_4,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function TianShenWGData:GetTianshenItemCfgByIndex(index)
	return self.act_item_cfg[index]
end

function TianShenWGData:GetTianShenSpecialSkillCap(data)
	local cap = 0
	if IsEmptyTable(data) then
		return cap
	end

	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end

		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end

	if data.type == TS_ST_PASS_TYPE.TSSTPASSTYPE_ATTR_ADD or
		data.type == TS_ST_PASS_TYPE.TSSTPASSTYPE_ATTR_PER_ADD or
		data.type == TS_ST_PASS_TYPE.TSSTPASSTYPE_ALLATTR_PER_ADD then

		local ts_info = self:GetTianShenInfoByIndex(data.image_id)
		local is_act = self:IsActivation(data.image_id)

		local level = ts_info.level or 0
		local star = is_act and ts_info.star or -1
		local upgrade_cfg = self:GetTianShenUpgrade(data.image_id, level)
		local star_cfg = self:GetTianShenStar1(data.image_id, star)
		local upgrade_attribute = AttributeMgr.GetAttributteValueByClass(upgrade_cfg)
		local star_attribute = AttributeMgr.GetAttributteValueByClass(star_cfg)
		local all_attribute = AttributeMgr.AddAttributeAttr(upgrade_attribute, star_attribute)

		-- 固定属性
		if data.type == TS_ST_PASS_TYPE.TSSTPASSTYPE_ATTR_ADD then
			local attr_id = data.param1 or 0
			local attr_value = data.param2 or 0
			local add_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			if add_str and attr_value > 0 then
				add_tab(add_str, attr_value)
			end

		-- 单属性
		elseif data.type == TS_ST_PASS_TYPE.TSSTPASSTYPE_ATTR_PER_ADD then
			local attr_id = data.param1 or 0
			local add_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			if add_str then
				local have_value = all_attribute[add_str] or 0
				local add_per = data.param2 and (data.param2 * 0.0001) or 0
				add_tab(add_str, have_value * add_per)
			end
		-- 全属性
		elseif data.type == TS_ST_PASS_TYPE.TSSTPASSTYPE_ALLATTR_PER_ADD then
			local add_per = data.param1 and (data.param1 * 0.0001) or 0
			for k, v in pairs(all_attribute) do
				if v > 0 then
					add_tab(k, v * add_per)
				end
			end
		end
	end

	local skill_inc = {
		attack_power = data.attack_power,
		defence_power = data.defence_power,
		capability_inc = data.capability_inc,
	}

	cap = AttributeMgr.GetCapability(attribute, skill_inc)
	return cap
end
------------------------------------------------------------------------
-------------------------------F2天神神饰--------------------------------
function TianShenWGData:DelayFlushShenShiRed(time)
	local flush_func = function ( )
		self:CacularTianShenShenShiType(true)
		RemindManager.Instance:Fire(RemindName.TianShen_ShenShi)
		TianShenWGCtrl.Instance:FlushTianShenBaoXiaView()
		ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_shenshi)
		self:CancleShenShiDelay()
	end
	
	self:CancleShenShiDelay()
	if time == 0 then
		flush_func()
	else
		self.delay_shenshi_red = GlobalTimerQuest:AddDelayTimer(function ()
			flush_func()
		end, time)
	end
end

function TianShenWGData:CancleShenShiDelay()
	if self.delay_shenshi_red then
		GlobalTimerQuest:CancelQuest(self.delay_shenshi_red)
		self.delay_shenshi_red = nil
	end
end

function TianShenWGData:OnRoleLevelChange()
	RemindManager.Instance:Fire(RemindName.TianShen_Battle)
end

--物品改变维护最新天神神饰背包
function TianShenWGData:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.delay_shenshi_red then
		self:DelayFlushShenShiRed(0.4)
		return
	end

	if self:CheckIsTianShenAcitveItem(change_item_id) then
		self:UpdateTianShenInfoList(true, self.tianshen_act_item_cfg[change_item_id].index)
		if ViewManager.Instance:IsOpenByIndex(GuideModuleName.TianShenView, TianShenView.TabIndex.Activation) then
			ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TianShenView.TabIndex.Activation)
		elseif ViewManager.Instance:IsOpenByIndex(GuideModuleName.TianShenView, TianShenView.TabIndex.ShenShi) then
			ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TianShenView.TabIndex.ShenShi)
		end
	elseif self:CheckIsTianShenUpstarItem(change_item_id) then
		self:UpdateTianShenInfoList(true, self.tianshen_upstar_item_cfg[change_item_id].index)
		if ViewManager.Instance:IsOpenByIndex(GuideModuleName.TianShenView, TianShenView.TabIndex.Activation) then
			ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TianShenView.TabIndex.Activation)
		elseif ViewManager.Instance:IsOpenByIndex(GuideModuleName.TianShenView, TianShenView.TabIndex.ShenShi) then
			ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TianShenView.TabIndex.ShenShi)
		end
	elseif self:CheckIsTianShenHaJiItem(change_item_id) then
		--ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TianShenView.TabIndex.HeJi)
		--RemindManager.Instance:Fire(RemindName.TianShenHeJi)
	end

	if self:CheckIsTianShenEquip(change_item_id) then
		-- if nil == self.tian_shen_equip_list then
		-- 	self:GetAllShenShiEquip()
			self:CacularTianShenShenShiType(true)
		-- else
		-- 	if self.tian_shen_equip_list[change_item_index] then
		-- 		if new_num == 0 then
		-- 			self.tian_shen_equip_list[change_item_index] = nil
		-- 			self.tian_shen_equip_num = self.tian_shen_equip_num - 1
		-- 		else
		-- 			self.tian_shen_equip_list[change_item_index].item_id = change_item_id
		-- 			self.tian_shen_equip_list[change_item_index].bag_index = change_item_index
		-- 			self.tian_shen_equip_list[change_item_index].num = new_num
		-- 		end
		-- 	else
		-- 		self.tian_shen_equip_list[change_item_index] = {}
		-- 		self.tian_shen_equip_list[change_item_index].item_id = change_item_id
		-- 		self.tian_shen_equip_list[change_item_index].bag_index = change_item_index
		-- 		self.tian_shen_equip_list[change_item_index].num = new_num
		-- 		self.tian_shen_equip_num = self.tian_shen_equip_num - 1
		-- 	end
		-- 	self:CacularTianShenShenShiType()
		-- end
		RemindManager.Instance:Fire(RemindName.TianShen_ShenShi)
	end

	if TSShenLingDianData.Instance:CheckIsUpItemId(change_item_id) then
		RemindManager.Instance:Fire(RemindName.TianShen_Temple)
	end
end

function TianShenWGData:CacularTianShenShenShiType(force)
	self.shenshi_equip_id_tab = {}
	--if not self.tian_shen_equip_list or IsEmptyTable(self.tian_shen_equip_list) then
		self:GetAllShenShiEquip(force)
	--end

	for k,v in pairs(self.tian_shen_equip_list) do
		if not self.shenshi_equip_id_tab[v.item_id] then
			self.shenshi_equip_id_tab[v.item_id] = {}
			self.shenshi_equip_id_tab[v.item_id].num = v.num
			self.shenshi_equip_id_tab[v.item_id].bag_index = v.bag_index
			self.shenshi_equip_id_tab[v.item_id].item_id = v.item_id
		else
			self.shenshi_equip_id_tab[v.item_id].num = self.shenshi_equip_id_tab[v.item_id].num + v.num
		end
	end
end

--是否天神激活道具
function TianShenWGData:CheckIsTianShenAcitveItem(item_id)
	local act_item_cfg = self.tianshen_act_item_cfg[item_id]
	if nil ~= act_item_cfg and not self:IsActivation(act_item_cfg.index) then
		return true
	end
	return false
end

--是否天神升星道具
function TianShenWGData:CheckIsTianShenUpstarItem(item_id)
	local frag_item_cfg = self.tianshen_upstar_item_cfg[item_id]
	if nil ~= frag_item_cfg then
		return true
	end
	return false
end

--检查是否是天神神饰
function TianShenWGData:CheckIsTianShenEquip(item_id)
	if self.shenshi_base_attr and self.shenshi_base_attr[item_id] then
		return true
	end
	return false
end

--获取背包所有天神神饰
-- TianShenWGData.Instance:GetAllShenShiEquip(true)
function TianShenWGData:GetAllShenShiEquip(force)
	if nil == self.tian_shen_equip_list or force then
		local cl_bag = ItemWGData.Instance:GetStuffStorgeItemData()
		self.tian_shen_equip_list = {}
		self.tian_shen_equip_num = 0
		for k,v in pairs(cl_bag) do
			if v and v.item_id and self:CheckIsTianShenEquip(v.item_id) then
				self.tian_shen_equip_num = self.tian_shen_equip_num + 1
				self.tian_shen_equip_list[v.index] = {}
				self.tian_shen_equip_list[v.index].item_id = v.item_id
				self.tian_shen_equip_list[v.index].bag_index = v.index
				self.tian_shen_equip_list[v.index].num = v.num
				self.tian_shen_equip_list[v.index].is_bind = v.is_bind
			end
		end
	end

	return self.tian_shen_equip_list
end

--获得单个天神装备信息
function TianShenWGData:GetShenShiEquipInfo(index)
	if not self.tianshen_info.upgrade_list then return {} end
	return self.tianshen_info.upgrade_list[index] or {}
end

function TianShenWGData:GetTianshenItemInfo(item_id)
	if self.shenshi_base_attr then
		return self.shenshi_base_attr[item_id] or {}
	else
		return {}
	end
end
--天神神饰属性 -- tips展示用
function TianShenWGData:GetTianShenAttributeTab(item_id, attr_per)
	local cfg = self:GetTianshenItemInfo(item_id)
	if IsEmptyTable(cfg) then return {} end
	attr_per = attr_per or 1

	local attr_list_cfg = {}
	local attr_type = nil
	for k,v in pairs(cfg) do
		attr_type = AttributeMgr.GetAttributteKey(k)
		attr_list_cfg[attr_type] = v * attr_per
	end

	return attr_list_cfg
end

function TianShenWGData:CheckEquipShenShiRed(tianshen_index)
	local tianshen_data = self:GetShenShiEquipInfo(tianshen_index)
	if IsEmptyTable(tianshen_data) then return 0,0 end

	local is_active = self:IsActivation(tianshen_index)
	if not is_active then return 0,0 end

	local is_full_equip = true
	for i, v in ipairs(tianshen_data.shenshi_part) do
		if v.item_id == 0 then
			is_full_equip = false
			break
		end
	end

	if self:ShenShiIsMaxRank(tianshen_index,tianshen_data.jingshen) and is_full_equip then
		return 0,0
	end
	local can_upgrade = 1
	local can_equip = 0
	local can_compose = false
	for i= 1,4 do
		if tianshen_data.shenshi_part[i] and tianshen_data.shenshi_part[i].item_id == 0 then
			can_upgrade = 0
			local need_item = self:CheckNeedShenShiItem(tianshen_index,i)
			can_equip = self:GetShenShiEquipInBag(need_item).num or 0
			can_compose = can_compose or self:CheckWayofComposeID(need_item)
			if can_equip > 0 then
				return 1,0
			end
		elseif not tianshen_data.shenshi_part[i] then
			return 0,0
		end
	end

	if can_compose then
		return 1,1
	end

	return can_upgrade,0
end

function TianShenWGData:CheckNeedShenShiItem(tianshen_index,equip_index)
	local tianshen_info = self:GetShenShiEquipInfo(tianshen_index)
	local color = tianshen_info.jingshen or -1
	color = color + 1
	local equip_cfg = self.shenshi_equip_condition[equip_index-1][color] or {}
	return equip_cfg.shenshi_id or -1
end

function TianShenWGData:GetShenShiEquipInBag(item_id)
	if not self.shenshi_equip_id_tab then
		self:CacularTianShenShenShiType()
	end
	return self.shenshi_equip_id_tab[item_id] or {}
end

function TianShenWGData:GetComposeInfoByID(item_id)
	return self.shenshi_compose_id_table[item_id] or {}
end

--判断能否合成以及合成路径
function TianShenWGData:CheckWayofComposeID(item_id)
	local cost_tab = {}
	local compose_cfg = {}
	local have_num = 0
	local need_num = 1
	local next_target = item_id
	local way_num = 0
	for i=1,8 do
		compose_cfg = self:GetComposeInfoByID(next_target)
		if IsEmptyTable(compose_cfg) then
			return false,{}
		end
			have_num = TianShenWGData.Instance:GetShenShiEquipInBag(compose_cfg.cost_item_id).num or 0
		if compose_cfg.cost * need_num <= have_num then
			way_num = way_num + 1
			cost_tab[way_num] = {}
			cost_tab[way_num].item_id = compose_cfg.cost_item_id
			cost_tab[way_num].cost_num = compose_cfg.cost * need_num
			cost_tab[way_num].have_num = have_num
			return true,cost_tab
		else
			if have_num > 0 then
				way_num = way_num + 1
				cost_tab[way_num] = {}
				cost_tab[way_num].item_id = compose_cfg.cost_item_id
				cost_tab[way_num].cost_num = have_num
				cost_tab[way_num].have_num = have_num
			end
			need_num = compose_cfg.cost * need_num - have_num
			next_target = compose_cfg.cost_item_id
		end
	end
	return false ,{}
end

function TianShenWGData:GetShenShiJinShengDataByLevel(tianshen_index, level)
	return (self.tianshen_rank_name_tab[tianshen_index] or {})[level] or {}
end

function TianShenWGData:GetShenShiJinShengData(tianshen_index, level)
	return self.tianshen_rank_name_tab[tianshen_index] or {}
end

function TianShenWGData:ShenShiIsMaxRank(tianshi_index,rank)
	return rank ~= nil and rank >= 7
	-- if self.tianshen_rank_name_tab[tianshi_index] and self.tianshen_rank_name_tab[rank+1] then
	-- 	return false
	-- end
	-- return true
end

--神饰对技能的提升是否已满
function TianShenWGData:CheckSkillIsUpGradeMax()
	return false
end

function TianShenWGData:GetShenShiZhanLi(item_id, attr_per)
	if item_id == 0 then return 0 end
	local attr, zhanli = ItemShowWGData.Instance:GetShenShiAttrByData(item_id, attr_per)
	return zhanli, attr
end

function TianShenWGData:GetTianShenStarSkillData(tianshen_index)
	return self.tianshen_star_skill[tianshen_index] or {}
end

--之前吃掉的属性
function TianShenWGData:GetColorLimitAttrTab(color_limit)
	if not self.all_color_limit_attr_tab or nil == self.all_color_limit_attr_tab[color_limit] then
		self:CacularColorLimitTab()
	end
	return __TableCopy(self.all_color_limit_attr_tab[color_limit]) 
end

function TianShenWGData:GetSingleLimitAttrTab(color_limit)
	if not self.color_limit_attr_single_tab or nil == self.color_limit_attr_single_tab[color_limit] then
		self:CacularColorLimitTab()
	end
	return self.color_limit_attr_single_tab[color_limit] or {}
end

function TianShenWGData:CacularColorLimitTab()
	self.all_color_limit_attr_tab = {}
	local shenshi_jinsheng_attr = self.tianshen_cfg_auto.shenshi_jinsheng_attr
	for k,v in pairs(shenshi_jinsheng_attr) do
		self.all_color_limit_attr_tab[v.level] = {}
		self.all_color_limit_attr_tab[v.level].gongji = tonumber(v.gongji) or 0
		self.all_color_limit_attr_tab[v.level].maxhp = tonumber(v.maxhp) or 0
		self.all_color_limit_attr_tab[v.level].pojia = tonumber(v.pojia) or 0
		self.all_color_limit_attr_tab[v.level].fangyu = tonumber(v.fangyu) or 0
		self.all_color_limit_attr_tab[v.level].yuansu_sh = tonumber(v.yuansu_sh) or 0
		self.all_color_limit_attr_tab[v.level].yuansu_hj = tonumber(v.yuansu_hj) or 0
	end

	self.color_limit_attr_single_tab = {}
	for k,v in pairs(self.shenshi_base_attr) do
		if nil == self.color_limit_attr_single_tab[v.color_limit] then
			self.color_limit_attr_single_tab[v.color_limit] = {}
			self.color_limit_attr_single_tab[v.color_limit].gongji = tonumber(v.gongji) or 0
			self.color_limit_attr_single_tab[v.color_limit].maxhp = tonumber(v.maxhp) or 0
			self.color_limit_attr_single_tab[v.color_limit].pojia = tonumber(v.pojia) or 0
			self.color_limit_attr_single_tab[v.color_limit].fangyu = tonumber(v.fangyu) or 0
			self.color_limit_attr_single_tab[v.color_limit].yuansu_sh = tonumber(v.yuansu_sh) or 0
			self.color_limit_attr_single_tab[v.color_limit].yuansu_hj = tonumber(v.yuansu_hj) or 0
		else
			self.color_limit_attr_single_tab[v.color_limit].gongji = self.color_limit_attr_single_tab[v.color_limit].gongji + tonumber(v.gongji)
			self.color_limit_attr_single_tab[v.color_limit].maxhp = self.color_limit_attr_single_tab[v.color_limit].maxhp + tonumber(v.maxhp)
			self.color_limit_attr_single_tab[v.color_limit].pojia = self.color_limit_attr_single_tab[v.color_limit].pojia + tonumber(v.pojia)
			self.color_limit_attr_single_tab[v.color_limit].fangyu = self.color_limit_attr_single_tab[v.color_limit].fangyu + tonumber(v.fangyu)
			self.color_limit_attr_single_tab[v.color_limit].yuansu_sh = self.color_limit_attr_single_tab[v.color_limit].yuansu_sh + tonumber(v.yuansu_sh)
			self.color_limit_attr_single_tab[v.color_limit].yuansu_hj = self.color_limit_attr_single_tab[v.color_limit].yuansu_hj + tonumber(v.yuansu_hj)
		end
	end

	for i = 1,7 do
		self.color_limit_attr_single_tab[i].gongji = self.all_color_limit_attr_tab[i].gongji - self.color_limit_attr_single_tab[i].gongji
		self.color_limit_attr_single_tab[i].maxhp = self.all_color_limit_attr_tab[i].maxhp - self.color_limit_attr_single_tab[i].maxhp
		self.color_limit_attr_single_tab[i].pojia = self.all_color_limit_attr_tab[i].pojia - self.color_limit_attr_single_tab[i].pojia
		self.color_limit_attr_single_tab[i].fangyu = self.all_color_limit_attr_tab[i].fangyu - self.color_limit_attr_single_tab[i].fangyu
		self.color_limit_attr_single_tab[i].yuansu_sh = self.all_color_limit_attr_tab[i].yuansu_sh - self.color_limit_attr_single_tab[i].yuansu_sh
		self.color_limit_attr_single_tab[i].yuansu_hj = self.all_color_limit_attr_tab[i].yuansu_hj - self.color_limit_attr_single_tab[i].yuansu_hj
	end
end
-----------------------------F2天神神饰end-------------------------------
------------------------------------------------------------------------


-----------------------------天神宝匣 start -----------------------------
function TianShenWGData:SetTianShenBaoXiaBoxInfo(info)
	self.is_can_uplevel = info.is_can_uplevel	--是否可以升品.
	self.ts_box_total_draw_count = info.total_draw_count --总抽奖次数
	self.ts_box_draw_key_count = info.draw_key_count --剩余抽奖材料数量
	RemindManager.Instance:Fire(RemindName.TianShenBaoXia)
	RemindManager.Instance:Fire(RemindName.TianShen_ShenShi)
end

--是否可以升品.
function TianShenWGData:GetTianShenBaoXiaBoxIsCanUpLevel()
	return self.is_can_uplevel == 1
end

--天神宝匣 总抽奖次数
function TianShenWGData:GetTianShenBaoXiaBoxTotlaCount()
	return self.ts_box_total_draw_count or 0
end

--天神宝匣 剩余材料
function TianShenWGData:GetTianShenBaoXiaBoxLeftMat()
	return self.ts_box_draw_key_count or 0
end

function TianShenWGData:GetBaoXiaOldLevel()
	return self.ts_box_old_level
end

--获取天神宝匣背包信息
--s_part :部位筛选  s_color :品质筛选
--不传参返回全部信息
function TianShenWGData:GetTianShenBoxBagInfo(s_part, s_color)
	self.tianshen_box_bag_list = {}
	local max_line = 50
	for i = 1, max_line do
		self.tianshen_box_bag_list[i] = {}
	end
	local num = 0
	local a,b = 1,1
	local temp_data = {}
	local shenshi_mat_bag_list = self:GetAllShenShiEquip()
	local base_cfg = {}
	if s_part or s_color then--有筛选条件
		for k,v in pairs(shenshi_mat_bag_list) do
			base_cfg = self:GetTianshenItemInfo(v.item_id)
			if not IsEmptyTable(base_cfg) then
				--部位匹配
				--品质匹配
				if s_part and s_color then--两个条件同时满足
					if s_part == base_cfg.part and s_color == base_cfg.color_limit then
						num = num + 1
						temp_data[num] = v
					end
				else--满足任一条件
					if s_part == base_cfg.part or s_color == base_cfg.color_limit then
						num = num + 1
						temp_data[num] = v
					end
				end
			end
		end
	else--无筛选条件
		for k,v in pairs(shenshi_mat_bag_list) do
			num = num + 1
			temp_data[num] = v
		end
	end

	if IsEmptyTable(temp_data) then
		--空背包
		return self.tianshen_box_bag_list
	end

	table.sort(temp_data, function(a, b)
        local c_str1, color1 = ItemWGData.Instance:GetItemColor(a.item_id)
        local c_str2, color2 = ItemWGData.Instance:GetItemColor(b.item_id)
        return color1 > color2
    end)

	num = 0
	local max_col = 3
	for t,q in pairs(temp_data) do
		num = num + 1
		a = math.ceil(num / max_col)
		b = num % max_col
		b = b == 0 and max_col or b
		if not self.tianshen_box_bag_list[a] then
			self.tianshen_box_bag_list[a] = {}
		end
		self.tianshen_box_bag_list[a][b] = q
	end
	return self.tianshen_box_bag_list
end

function TianShenWGData:CheckTianShenBoxRemind()
	local is_fun_open = TianShenWGCtrl.Instance:GetTianShenBoxIsOpen()
	if not is_fun_open then
		return 0
	end

	local other_cfg = self:GetGodHoodDrawOtherCfg()
	if not other_cfg then
		print_error("天神宝匣 红点判断 not other_cfg")
		return 0
	end

	local my_mat_count = self:GetGodHoodDrawLeftMat()
	local is_one_mat_enough = my_mat_count >= other_cfg.draw_cost
	if is_one_mat_enough then--满足一次就亮红点
		return 1
	end

	local is_can_uplevel = self:GetGodHoodDrawIsCanUpLevel()
	if is_can_uplevel then
		return 1
	end

	return 0
end

-- 天神宝匣背包 上升箭头物品列表
--TianShenWGData.Instance:SetShenShiItemUpFlagList()
function TianShenWGData:SetShenShiItemUpFlagList()
	self.shenshi_up_need_item_tb = {}
	local tianshen_active = false
	local tianshen_data = nil
	local is_tianshen_max_rank = false
	for tianshen_index, v in pairs(self.magic_image_cfg) do
		if self:IsActivation(tianshen_index) then--天神是否激活
			tianshen_data = self:GetShenShiEquipInfo(tianshen_index)
			is_tianshen_max_rank = self:ShenShiIsMaxRank(tianshen_index, tianshen_data.jingshen)

			if not IsEmptyTable(tianshen_data) and not is_tianshen_max_rank then

				local data_cache = TianShenWGData.Instance:GetMagicImageListCfg(v.series)
				local shenshi_series = v.series
				local select_shenshi_index = 1
				for t,q in pairs(data_cache) do
	 				if q.index == v.index then
	            		select_shenshi_index = t
	            		break
	        		end
	        	end

				for i= 1, 4 do
					if tianshen_data.shenshi_part[i] and tianshen_data.shenshi_part[i].item_id == 0 then
						local need_item = self:CheckNeedShenShiItem(tianshen_index, i)
						if IsEmptyTable(self.shenshi_up_need_item_tb[need_item]) then
							self.shenshi_up_need_item_tb[need_item] = {}
							self.shenshi_up_need_item_tb[need_item].is_need = true
							self.shenshi_up_need_item_tb[need_item].shenshi_series = shenshi_series
							self.shenshi_up_need_item_tb[need_item].select_shenshi_index = select_shenshi_index
						end
					end
				end
			end
		end
	end
end

--当前物品是否为神饰升级所需判断
function TianShenWGData:CheckShenShiItemUpFlag(item_id)
	if self.shenshi_up_need_item_tb and self.shenshi_up_need_item_tb[item_id] then
		return self.shenshi_up_need_item_tb[item_id].is_need == true
	end
	return false
end

--当前物品是否为神饰升级所需--跳转
function TianShenWGData:CheckShenShiItemJump(item_id)
	local shenshi_series, select_shenshi_index = 0, 0
	if self.shenshi_up_need_item_tb and self.shenshi_up_need_item_tb[item_id] then
		shenshi_series = self.shenshi_up_need_item_tb[item_id].shenshi_series
		select_shenshi_index = self.shenshi_up_need_item_tb[item_id].select_shenshi_index
	end
	return shenshi_series, select_shenshi_index
end

function TianShenWGData:SetTSBoxRewardTipCache(tip_info)
	table.insert(self.tsbox_reward_tip_info_list, tip_info)
end

function TianShenWGData:ShowTSBoxRewardTip()
	if not IsEmptyTable(self.tsbox_reward_tip_info_list) then
		for i = #self.tsbox_reward_tip_info_list, 1, -1 do
			local t = table.remove(self.tsbox_reward_tip_info_list, i)
			ItemWGData.Instance:NoticeOneItemChange(t.change_item_id, t.change_item_index, t.change_reason, t.put_reason, t.old_num, t.new_num)
		end
	end
end

--设置天神宝匣是否跳过动画
function TianShenWGData:SetTSBoxSkipAnimFalg(falg)
	self.tianshen_baoxia_is_skipanim = falg
end

function TianShenWGData:GetTSBoxIsSkipAnim()
	return self.tianshen_baoxia_is_skipanim == true
end
-----------------------------天神宝匣 end -----------------------------

function TianShenWGData:GetTianShenWuXingByImageId(image_id)
	if self.tianshen_wuxing_cfg and self.tianshen_wuxing_cfg[image_id] then
		return self.tianshen_wuxing_cfg[image_id].wuxing_type or 0
	end
	return 0
end

function TianShenWGData:GetTianShenWuXingByIndex(index)
	if self.magic_image_cfg and self.magic_image_cfg[index] then
		return self:GetTianShenWuXingByImageId(self.magic_image_cfg[index].appe_image_id)
	end
	return 0
end

-- 查看羁绊技能激活所需要的条件
function TianShenWGData:GetTianShenJiBanSkillAct(tianshen_index, level, jiban_id )
	if tianshen_index and level and jiban_id then
		local rank_name_tab = self:GetTianShenStarSkillData(tianshen_index)[level]
		for i=0,3 do
			if rank_name_tab["skill_id" .. i]  == jiban_id then
				return rank_name_tab["image_index" .. i]
			end
		end
	end
	return nil
end
-- 当前技能等级
function TianShenWGData:GetTianShenSkillLv(tianshen_index, level, skill_id )
	if tianshen_index and level and skill_id then
		local rank_name_tab = self:GetTianShenStarSkillData(tianshen_index)[level] or {}
		for i=0,3 do
			if rank_name_tab["skill_id" .. i]  == skill_id then
				return rank_name_tab["skill_level" .. i]
			end
		end
	end
	return nil
end

--技能当前等级升下一级所需的神饰等级
function TianShenWGData:GetTianShenSkillNextLv(tianshen_index, level, skill_id )
	if tianshen_index and level and skill_id then
		local rank_name_tab = self:GetTianShenStarSkillData(tianshen_index)
		for i = level, 6 do
			if rank_name_tab[i+1].skill_id == skill_id then
				return rank_name_tab[i+ 1].lv_name
			end
		end
	end
	return nil
end

--天神激活后查看羁绊天神是否被激活
function TianShenWGData:CacularJiban(index)
	local cur_tianshen_jiban_info = nil
	local jiban_act_falg = false
	if index and self.tianshen_jiban_cfg then
		for k,v in pairs(self.tianshen_jiban_cfg) do
			if index == v.index1 or index == v.index2 then
				cur_tianshen_jiban_info = v
			end
		end
	end
	if cur_tianshen_jiban_info then
		jiban_act_falg = self:IsActivation(cur_tianshen_jiban_info.index1) and self:IsActivation(cur_tianshen_jiban_info.index2) or false
	end
	if jiban_act_falg then
		self.tianshen_jiban_info = cur_tianshen_jiban_info
	else
		self.tianshen_jiban_info = nil
	end
end
--羁绊天神激活信息
function TianShenWGData:GetTianShenJiBanAct()
	return self.tianshen_jiban_info
end

-- 获取所有羁绊信息
function TianShenWGData:GetTianShenAllJiBan()
	return self.tianshen_jiban_cfg
end

function TianShenWGData:GetCallJiBanTsIndexBySkillId(jiban_skill_id)
	if nil == self.tianshen_jiban_skill_cfg then
		self.tianshen_jiban_skill_cfg = {}
		for k,v in pairs(self.tianshen_cfg_auto.jiban_des) do
			self.tianshen_jiban_skill_cfg[v.jiban_id1] = tonumber(v.index2)
			self.tianshen_jiban_skill_cfg[v.jiban_id2] = tonumber(v.index1)
		end
	end

	return self.tianshen_jiban_skill_cfg[jiban_skill_id]
end

function TianShenWGData:InitJiBanSkill()
	self.jiban_skill_list = {}
	for k,v in pairs(self.magic_image_cfg) do
		self.jiban_skill_list[v.jiban_skill] = 1
	end
end

function TianShenWGData:IsJiBanSkill(skill_id)
	local is_jiban_skill = false
	if skill_id ~= nil and self.jiban_skill_list ~= nil and self.jiban_skill_list[skill_id] ~= nil then
		is_jiban_skill = true
	end

	return is_jiban_skill
end

function TianShenWGData:GetMonsterWuxingByID(monster_id)
	if self.monster_wuxing_cfg and self.monster_wuxing_cfg[monster_id] then
		return self.monster_wuxing_cfg[monster_id].wuxing_type or 0
	end
	return 0
end

function TianShenWGData:GetDefaultOpenActiveSelect()
	local tianshen_info = {}
	local min_tianshen_info = {}
	local min_tianshen_level = -99

	for k,v in pairs(self.magic_image_cfg) do
		if self:IsActivation(v.index) then
			tianshen_info = self:GetTianShenInfoByIndex(v.index)
			if tianshen_info and tianshen_info.level then
				if min_tianshen_level == - 99 then
					min_tianshen_level = tianshen_info.level
					min_tianshen_info = v
				elseif min_tianshen_level > tianshen_info.level then
					min_tianshen_level = tianshen_info.level
					min_tianshen_info = v
				end
			end
		end
	end
	if self:SpecialTianshenCanUpGrade(min_tianshen_info.index) then
		return min_tianshen_info
	else
		return {}
	end
end

function TianShenWGData:GetJiBanTianShenBySkillID(skill_id)
	if self.magic_image_jiban_cfg and self.magic_image_jiban_cfg[skill_id] then
		return self.magic_image_jiban_cfg[skill_id][1] or {}
	end
	return {}
end

function TianShenWGData:GetTianShenGetWayData(index)
	if not self.tianshen_get_way_tab then
		self:CacularTianShenGetWayTab()
	end
	return self.tianshen_get_way_tab[index] or {}
end

function TianShenWGData:CacularTianShenGetWayTab()
	self.tianshen_get_way_tab = {}
	local num = 0
	for k,v in pairs(self.magic_image_cfg) do
		num = 0
		self.tianshen_get_way_tab[v.index] = {}
		for i = 1,3 do
			if v["get_way"..i] and v["get_way"..i] ~= "" then
				num = num + 1
				self.tianshen_get_way_tab[v.index][num] = {}
				self.tianshen_get_way_tab[v.index][num].get_way_name = v["get_way"..i]
				self.tianshen_get_way_tab[v.index][num].jump_way = v["jump_way"..i]
				self.tianshen_get_way_tab[v.index][num].is_activity = 0
			end
		end

		for i = 1,3 do
			if v["jump_opid"..i] and v["jump_opid"..i] ~= "" then
				local activity_cfg = ActivityWGData.Instance:GetActivityCfgByType(tonumber(v["jump_opid"..i]))
				if activity_cfg then
					num = num + 1
					self.tianshen_get_way_tab[v.index][num] = {}
					self.tianshen_get_way_tab[v.index][num].get_way_name = activity_cfg.name
					self.tianshen_get_way_tab[v.index][num].jump_way = activity_cfg.open_panel_name
					self.tianshen_get_way_tab[v.index][num].is_activity = v["jump_opid"..i]
				end
			end
		end
	end
end

function TianShenWGData:SetBattleDrugStatus(value)
	self.tianshen_battle_druging = value
end

function TianShenWGData:GetBattleDrugStatus()
	return self.tianshen_battle_druging or false
end

function TianShenWGData:GetTianShenDingWeiByImageId(image_id)
	local cfg = self:GetImageModelByAppeId(image_id)
	if cfg and cfg.tianshen_location then
		return cfg.tianshen_location
	end
	return 0
end

function TianShenWGData:GetTianShenDingWeiText(index)
	if self.magic_image_cfg and self.magic_image_cfg[index] then
		return self.magic_image_cfg[index].tianshen_location
	end
	return 0
end

-- 获取单个天神获取途径
function TianShenWGData:GetTianShenGetWayList(tianshen_index)
	local is_activate = TianShenWGData.Instance:IsActivation(tianshen_index)
    local tianshen_get_way_tab = TianShenWGData.Instance:GetTianShenGetWayData(tianshen_index)
    local avaliable_num = 0
    local avaliable_get_way = {}
    if not IsEmptyTable(tianshen_get_way_tab) and not is_activate then
    	local init_data = function(i)
    		local data = {}
			data = {}
			data.name = tianshen_get_way_tab[i].get_way_name 		-- 途径名称
			data.jump_way = tianshen_get_way_tab[i].jump_way 		-- 目标面板
			data.is_open = false
			return data
    	end

    	for i = 1, 6 do
    		if tianshen_get_way_tab[i] then
 				if tianshen_get_way_tab[i].is_activity ~= 0 then
 					local is_open = ActivityWGData.Instance:GetActivityIsOpen(tianshen_get_way_tab[i].is_activity)
 					if is_open then
 						avaliable_num = avaliable_num + 1
 						avaliable_get_way[avaliable_num] = init_data(i)
 						avaliable_get_way[avaliable_num].is_open = is_open
 					end
 				else
 					local t = Split(tianshen_get_way_tab[i].jump_way, "#")
 					if "LayoutZeroBuyView" == t[1] then -- tianshen_get_way_tab[i].jump_way then --0元购特殊处理
						local view_name = t[1]
						avaliable_num = avaliable_num + 1
 						avaliable_get_way[avaliable_num] = init_data(i)
 						avaliable_get_way[avaliable_num].is_open = false
						local tianshen_cfg = self:GetTianshenItemCfgByIndex(tianshen_index)
						for k,v in pairs(ActivityModuleName) do
							if k == view_name then
								if ActivityWGData.Instance:GetActivityIsOpen(v) or ActivityWGData.Instance:GetActivityIsOpen(v) then
									if tianshen_cfg and tianshen_cfg.act_item_id then
										local zero_id = LayoutZeroBuyWGData.Instance:GetItemInWhichID(tianshen_cfg.act_item_id)
										if LayoutZeroBuyWGData.Instance:GetZeroBuyDataOpenByID(zero_id) then
											avaliable_get_way[avaliable_num].is_open = true
										else
											avaliable_get_way[avaliable_num].is_open = false
											avaliable_get_way[avaliable_num].special_tip = Language.TianShen.TianShenGetJumpTip[2]
 										end
 										
 									end
								end
							end
						end
					elseif "XiuZhenRoadView" == tianshen_get_way_tab[i].jump_way then
						local is_open = XiuZhenRoadWGData.Instance:XiuZhenRoadIsOpen()
						--if is_open then
							avaliable_num = avaliable_num + 1
 							avaliable_get_way[avaliable_num] = init_data(i)
 							avaliable_get_way[avaliable_num].is_open = is_open
						--end
					elseif "EquipTargetView" == t[1] then
						local is_show = FunOpen.Instance:GetFunIsOpened(GuideModuleName.EquipTargetView)
						avaliable_num = avaliable_num + 1
 						avaliable_get_way[avaliable_num] = init_data(i)
 						avaliable_get_way[avaliable_num].is_open = false
						if is_show and t[3] then
							local type_data = EquipTargetWGData.Instance:GetEquipBigType()
							local uip_tab = Split(t[3], "=")
							if uip_tab[2] and tonumber(uip_tab[2]) <= #type_data then
								avaliable_get_way[avaliable_num].is_open = true
							else
								avaliable_get_way[avaliable_num].is_open = false
								avaliable_get_way[avaliable_num].special_tip = Language.TianShen.TianShenGetJumpTip[2]
							end
						end
					elseif "shop" == t[1] then
						avaliable_num = avaliable_num + 1
 						avaliable_get_way[avaliable_num] = init_data(i)
 						avaliable_get_way[avaliable_num].is_open = false
						if FunOpen.Instance:GetFunIsOpenedByMouduleName(t[1]) then
							avaliable_get_way[avaliable_num].is_open = true
						end
					elseif "EveryDayRechargeView" == t[1] then
						avaliable_num = avaliable_num + 1
 						avaliable_get_way[avaliable_num] = init_data(i)
 						avaliable_get_way[avaliable_num].is_open = false
						if RechargeWGData.Instance:GetHistoryRechargeCount() > 0 then
							avaliable_get_way[avaliable_num].is_open = true
						end
					elseif t[1] and "fubenpanel" == t[1] and t[2] then
						avaliable_num = avaliable_num + 1
 						avaliable_get_way[avaliable_num] = init_data(i)
 						avaliable_get_way[avaliable_num].is_open = false
						if FunOpen.Instance:GetFunIsOpenedByTabName(t[2]) then
							avaliable_get_way[avaliable_num].is_open = true
						end
					else
						if t[2] then
							avaliable_num = avaliable_num + 1
 							avaliable_get_way[avaliable_num] = init_data(i)
 							avaliable_get_way[avaliable_num].is_open = false
							if FunOpen.Instance:GetFunIsOpenedByTabName(t[2]) then
								avaliable_get_way[avaliable_num].is_open = true
							end
						else
							avaliable_num = avaliable_num + 1
 							avaliable_get_way[avaliable_num] = init_data(i)
 							avaliable_get_way[avaliable_num].is_open = false
							if FunOpen.Instance:GetFunIsOpenedByMouduleName(t[1]) then
								avaliable_get_way[avaliable_num].is_open = true
 							end
 						end
 					end
 					
 				end
    		end
    	end
    end
    return avaliable_get_way
end

function TianShenWGData:GetTianShenMaxGrade()
	if not self.tianshen_max_grade then
		self.tianshen_max_grade = 0
		for k,v in pairs(self.special_image_upgrade_cfg[1]) do
			if v.grade > self.tianshen_max_grade then
				self.tianshen_max_grade = v.grade
			end
		end
	end
	return self.tianshen_max_grade
end

function TianShenWGData:GetMaxShenShiLimiteList()
	if not self.shenshi_equip_condition then
		return {}
	end

	local cfg_list = {}
	for i = 0,3 do
		if self.shenshi_equip_condition[i] then
			cfg_list[i+1] = self.shenshi_equip_condition[i][8] or {}
		end
	end
	return cfg_list
end

-- 天神养成完成度
function TianShenWGData:SetCompleteRateList(protocol)
	self.tianshen_complete_rate_list = protocol.tianshen_complete_rate_list
end

-- 获得天神养成完成度
function TianShenWGData:GetCompleteRateList()
	return self.tianshen_complete_rate_list or {}
end

-- 获得对应天神的养成完成度（0~100）
function TianShenWGData:GetCompleteRate(tianshen_index)
	return self:GetCompleteRateList()[tianshen_index] or 0
end

-------------天神组合技能 start--------------------------------

function TianShenWGData:OnSCTianShenUnionSkillInfo(protocol)
	self.ts_union_skill_info = protocol.union_skill_list
	-- print_error("FFFFF===== self.ts_union_skill_info", self.ts_union_skill_info)
	if self.ts_union_old_skill_info then
		local change_info = {}
		local new_info = self.ts_union_skill_info or {}
		local old_info = self.ts_union_old_skill_info or {}
		if not IsEmptyTable(new_info) then
			local is_new_add--是否为新增技能id
			for new_k, new_v in pairs(new_info) do
				is_new_add = true
				for old_k, old_v in pairs(old_info) do
					if new_v.union_skill_id == old_v.union_skill_id then
						is_new_add = false
						break
					end
				end

				if is_new_add then
					table.insert(change_info, new_v.union_skill_id)
				end
			end
		end
		-- print_error("FFFF===== change_info", change_info)
		if not IsEmptyTable(change_info) then
			--TianShenWGCtrl.Instance:OpenTSGuangHuanTipView(change_info)
		end
	end
	self.ts_union_old_skill_info = self.ts_union_skill_info
end

function TianShenWGData:GetTianShenUnionSkillInfo()
	return self.ts_union_skill_info
end

function TianShenWGData:CheckTSUnionSkillActiveById(union_id)
	local info = self:GetTianShenUnionSkillInfo()
	if not IsEmptyTable(info) then
		for k, v in pairs(info) do
			if union_id == v.union_skill_id then
				return true
			end
		end
	end

	return false
end

function TianShenWGData:GetUnionSkillTSList(union_id)
	local union_cfg = self:GetUnionSkillCfgById(union_id)
	local data_list = {}
	if not IsEmptyTable(union_cfg) then
		local temp_t = Split(union_cfg.tianshen_list or "", "|")
		for i, v in ipairs(temp_t) do
			data_list[i] = {}
			data_list[i].ts_index = tonumber(v)
		end
	end

	return data_list
end

--天神被动技能展示(插入组合技能)
function TianShenWGData:GetBeSkillShowCfgList(tianshen_index)
	local show_cfg = {}
	local ts_cfg = self:GetTianShenCfg(tianshen_index)
	local base_beskill = self:GetSpecialImagePasvSkillCfg(tianshen_index)

	-- --先插入组合技能
	-- if not IsEmptyTable(ts_cfg) then
	-- 	local temp_cfg
	-- 	local temp_idx_list = Split(ts_cfg.ts_union_skills or "", "|")
	-- 	for i, idx in ipairs(temp_idx_list) do
	-- 		temp_cfg = self:GetUnionSkillCfgById(tonumber(idx))
	-- 		if not IsEmptyTable(temp_cfg) then
	-- 			table.insert(show_cfg, temp_cfg)
	-- 		end
	-- 	end
	-- end

	--再插入被动技能--策划需求:不需要展示被动技能图标
	if not IsEmptyTable(base_beskill) then
		for i = 0, #base_beskill  do--技能id从0开始
			table.insert(show_cfg, base_beskill[i])
		end
	end
	

	return show_cfg
end

--获取天神组合技能战力
function TianShenWGData:GetTianShenUnionSkillCap(data)
	local cap = 0
	local attr_num = 2--属性字段数量,从0开始
	if IsEmptyTable(data) then
		return cap
	end

	local attribute = AttributePool.AllocAttribute()
	local attr_id, attr_value, add_str
	for i = 0, attr_num - 1 do
		attr_id = data["attr_type_" .. i] or 0
		attr_value = data["attr_value_" .. i] or 0
		add_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
		if attribute[add_str] and attr_value > 0 then
			attribute[add_str] = attr_value
		end
	end

	cap = AttributeMgr.GetCapability(attribute)
	return cap
end

--设置用于辨别 天神激活/天神神饰 界面的天神选中索引
function TianShenWGData:SetCurSelectTianShenIndex(index)
	self.cur_select_tianshen_index = index
end

function TianShenWGData:GetCurSelectTianShenIndex()
	return self.cur_select_tianshen_index
end

function TianShenWGData:GetUnionSkillPageShowCfg()
	local show_cfg, temp_big_data, temp_small_data = {}, {}, {}
	local all_page_cfg = self:GetUnionSkillPageCfg()
	if not IsEmptyTable(all_page_cfg) then
		for _, page_cfg in ipairs(all_page_cfg) do
			temp_big_data = {}
			for i = 1, #page_cfg do--拆分重组大组里的数据
				temp_small_data = {}
				--每两个作为一组,上下排布
				if i % 2 ~= 0 then
					temp_small_data[1] = page_cfg[i]
					if page_cfg[i + 1] then
						temp_small_data[2] = page_cfg[i + 1]
					end
					table.insert(temp_big_data, temp_small_data)
				end
			end
			table.insert(show_cfg, temp_big_data)
		end
	end
	return show_cfg
end

function TianShenWGData:GetUnionSkillPageCfg()
	if not self.ts_union_page_cfg then
		local cfg = self:GetUnionSkillCfg()
		self.ts_union_page_cfg = {}
		if not IsEmptyTable(cfg) then
			for i, v in ipairs(cfg) do
				if not self.ts_union_page_cfg[v.page_group] then
					self.ts_union_page_cfg[v.page_group] = {}
				end
				table.insert(self.ts_union_page_cfg[v.page_group], v)
			end
		end
	end
	return self.ts_union_page_cfg
end
-------------天神组合技能 end--------------------------------

function TianShenWGData:GetAllFightPosData()
	local fight_data, fight_num = self:GetTianShenSkillInfoByMainView()--已出战天神和位置信息
	local true_pos_data = {}
	for pos = 0 , 3 do
		true_pos_data[pos] = {}
		true_pos_data[pos].image_index = -1--代表空槽
		true_pos_data[pos].is_in_cd = false--CD状态
		true_pos_data[pos].is_pos_act = self:IsChuZhanIndexOpen(pos)--位置解锁状态

		if fight_data[pos] then
			true_pos_data[pos].image_index = fight_data[pos].image_index--位置所在的天神
			true_pos_data[pos].is_in_cd = self:CheckTianShenIsCDTime(fight_data[pos].image_index)
		end
	end

	return true_pos_data
end

function TianShenWGData:GetTianshenSkillList(cfg, is_calc_multi)
	local skill_cfg = {}
	if cfg == nil then
		return skill_cfg
	end

	if cfg.is_multi_wuxing == 1 and is_calc_multi then
		if self.multi_skill_group[cfg.appe_image_id] ~= nil then
			local group_data = self.multi_skill_group[cfg.appe_image_id]
			-- print_error("GetTianshenSkillList", group_data)
			local role_skill = SkillWGData.Instance:GetSkillList()
			if role_skill ~= nil then
				for k,v in pairs(role_skill) do
					local group_index = self.multi_skill_dic[v.skill_id]
					-- print_error("~~~~GetTianshenSkillList~~~", v.skill_id, group_index)
					if group_index ~= nil and group_data[group_index] ~= nil then
						skill_cfg = group_data[group_index]
						break
					end
				end
			end
		else
			if self.multi_skill_group[cfg.appe_image_id] ~= nil then
				skill_cfg = self.multi_skill_group[cfg.appe_image_id][1]
			end
		end
	else
		if self.multi_skill_group[cfg.appe_image_id] ~= nil then
			skill_cfg = self.multi_skill_group[cfg.appe_image_id][1]
			--策划要求:有的天神配置了多个主动技能,但要求不显示出来,因此根据配置个数显示
			if skill_cfg and cfg.show_zhu_skill_num and #skill_cfg > cfg.show_zhu_skill_num then
				for i = #skill_cfg, cfg.show_zhu_skill_num + 1, -1 do
					table.remove(skill_cfg, i)
				end
			end
		end
	end

	return skill_cfg
end

function TianShenWGData:GetNormalSkillList(cfg, is_calc_multi)
	local skill_cfg = {}
	if cfg == nil then
		return skill_cfg
	end

	if cfg.is_multi_wuxing == 1 and is_calc_multi then
		if self.normal_skill_group[cfg.appe_image_id] ~= nil then
			local group_data = self.normal_skill_group[cfg.appe_image_id]
			local role_skill = SkillWGData.Instance:GetSkillList()
			local has_skill = false
			if role_skill ~= nil then
				for k,v in pairs(role_skill) do
					local group_index = self.normal_skill_dic[v.skill_id]
					if group_index ~= nil and group_data[group_index] ~= nil then
						skill_cfg = group_data[group_index]
						has_skill = true
						break
					end
				end
			end

			if not has_skill then
				skill_cfg = group_data[1]
			end
		else
			if self.normal_skill_group[cfg.appe_image_id] ~= nil then
				skill_cfg = self.normal_skill_group[cfg.appe_image_id][1]
			end
		end
	else
		if self.normal_skill_group[cfg.appe_image_id] ~= nil then
			skill_cfg = self.normal_skill_group[cfg.appe_image_id][1]
		end
	end

	return skill_cfg
end

function TianShenWGData:GetMultiSkillUseIndex()
	local use_index = nil
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local mian_role = Scene.Instance:GetMainRole()
	local is_system_bianshen = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance
	if not is_system_bianshen then
		return use_index
	end	

	local appe_image_id = RoleWGData.Instance:GetAttr("appearance_param")
	local tianshen_cfg = self:GetImageModelByAppeId(appe_image_id, true)

	if tianshen_cfg == nil or tianshen_cfg.is_multi_wuxing == "" or tianshen_cfg.is_multi_wuxing ~= 1 then
		return use_index
	end

	use_index = 1
	if self.multi_skill_group[tianshen_cfg.appe_image_id] == nil or self.normal_skill_group[tianshen_cfg.appe_image_id] == nil then
		return use_index
	end

	local group_normal_data = self.normal_skill_group[tianshen_cfg.appe_image_id]
	local group_skill_data = self.multi_skill_group[tianshen_cfg.appe_image_id]
	local role_skill = SkillWGData.Instance:GetSkillList()
	for k,v in pairs(role_skill) do
		local group_normal_index = self.normal_skill_dic[v.skill_id]
		if group_normal_index ~= nil and group_normal_data[group_normal_index] ~= nil then
			use_index = group_normal_index
			break
		end

		local group_skill_index = self.multi_skill_dic[v.skill_id]
		if group_skill_index ~= nil and group_skill_data[group_skill_index] ~= nil then
			use_index = group_skill_index
			break
		end 
	end

	return use_index
end

function TianShenWGData:GetChangeMultiSendList(change_index)
	local remove_list = nil
	local add_list = nil
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local is_system_bianshen = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance
	if not is_system_bianshen or change_index == nil then
		return remove_list, add_list
	end

	local appe_image_id = RoleWGData.Instance:GetAttr("appearance_param")
	local tianshen_cfg = self:GetImageModelByAppeId(appe_image_id, true)
	if tianshen_cfg == nil or tianshen_cfg.is_multi_wuxing == "" or tianshen_cfg.is_multi_wuxing ~= 1 then
		return remove_list, add_list
	end

	if self.multi_skill_group[tianshen_cfg.appe_image_id] == nil or self.normal_skill_group[tianshen_cfg.appe_image_id] == nil then
		return remove_list, add_list
	end

	local group_skill_data = self.multi_skill_group[tianshen_cfg.appe_image_id]
	local group_normal_data = self.normal_skill_group[tianshen_cfg.appe_image_id]
	local cur_use_wuxing = nil
	local role_skill = SkillWGData.Instance:GetSkillList()
	for k,v in pairs(role_skill) do
		local group_normal_index = self.normal_skill_dic[v.skill_id]
		if group_normal_index ~= nil and group_normal_data[group_normal_index] ~= nil then
			cur_use_wuxing = group_normal_index
			break
		end

		local group_skill_index = self.multi_skill_dic[v.skill_id]
		if group_skill_index ~= nil and group_skill_data[group_skill_index] ~= nil then
			use_index = group_skill_index
			break
		end 
	end

	if cur_use_wuxing == nil or group_skill_data[cur_use_wuxing] == nil or group_normal_data[cur_use_wuxing] == nil
	or group_skill_data[change_index] == nil or group_normal_data[change_index] == nil then
		return remove_list, add_list
	end

	remove_list = {}
	add_list = {}
	for k,v in pairs(group_skill_data[cur_use_wuxing]) do
		table.insert(remove_list, v)
	end

	for k,v in pairs(group_normal_data[cur_use_wuxing]) do
		table.insert(remove_list, v)
	end

	for k,v in pairs(group_skill_data[change_index]) do
		table.insert(add_list, v)
	end

	for k,v in pairs(group_normal_data[change_index]) do
		table.insert(add_list, v)
	end

	return remove_list, add_list
end

function TianShenWGData:GetNormalSkillListByIndex(appe_image_id, index)
	return (self.normal_skill_group[appe_image_id] or {})[index]
end

function TianShenWGData:GetMultiSkillListByIndex(appe_image_id, index)
	return (self.multi_skill_group[appe_image_id] or {})[index]
end

function TianShenWGData:GetMultiSkillGroup(appe_image_id)
	return self.multi_skill_group[appe_image_id]
end

function TianShenWGData:ActivationTsSelectIndex(ts_select_index)
	if ts_select_index == nil then
		return self.activation_select_ts_index
	end
	self.activation_select_ts_index = ts_select_index
end

function TianShenWGData:ShenShiTsSelectIndex(ts_select_index)
	if ts_select_index == nil then
		return self.shenshi_select_ts_index
	end
	self.shenshi_select_ts_index = ts_select_index
end

function TianShenWGData:ShenQiTsSelectIndex(ts_select_index)
	if ts_select_index == nil then
		return self.shenqi_select_ts_index
	end
	self.shenqi_select_ts_index = ts_select_index
end

function TianShenWGData:InfoTsSelectIndex(ts_select_index)
	if ts_select_index == nil then
		return self.ts_info_select_index
	end

	self.ts_info_select_index = ts_select_index
end

function TianShenWGData:GetWeaponIndexByAppeImageId(appe_image_id)
	return (self.magic_image_id_cfg[appe_image_id] or {}).index
end

function TianShenWGData:GetUplevelNum(select_data)
	local data = self:GetSTServerInfoBySkillId(select_data.skill_id)
	local level = data.level > 0 and data.level or 1
	local cur_skill_cfg = self:GetSTLevelcfg(data.skill_id, level) --当前等级配置
	local uplevel_has_num = ItemWGData.Instance:GetItemNumInBagById(cur_skill_cfg.uplevel_item_id)
	local num = 0
	local skill_cfg = self.m_shentong_skill_cfg[data.skill_id]
	local max_st_cfg = self:GetSTMaxLevelcfg(data.skill_id)
	for k,v in pairs(skill_cfg) do
		if level <= v.skill_level and uplevel_has_num >= v.uplevel_item_num then
			num = num + 1
 			uplevel_has_num = uplevel_has_num - v.uplevel_item_num
		end
	end
	
	if num + level >= max_st_cfg.skill_level then
		num = max_st_cfg.skill_level - level
	end
	
	return num
end

-------------------天神羁绊----------------

function TianShenWGData:GetUnionSkillCfg()
	return self.tianshen_union_skill_cfg
end

--根据组合技能索引取对应配置
function TianShenWGData:GetUnionSkillCfgById(union_id)
	return self.tianshen_union_skill_cfg[union_id]
end

function TianShenWGData:GetHejiCfgBySkillId(heji_skill_id)
	return self.tianshen_heji_skill_cfg[heji_skill_id]
end

function TianShenWGData:GetUnionSkillActiveDesById(union_id)
	local show_des = Language.TianShen.TSUnionSkillActiveDes
	local union_cfg = self:GetUnionSkillCfgById(union_id)
	if not IsEmptyTable(union_cfg)  then
		local ts_idx_list = Split(union_cfg.tianshen_list or "", "|")
		local ts_idx, ts_cfg, des_color
		for i, idx in ipairs(ts_idx_list) do
			ts_idx = tonumber(idx)
			ts_cfg = self:GetTianShenCfg(ts_idx)
			des_color = self:GetTianShenIsChuZhanByIndex(ts_idx) and COLOR3B.D_GREEN or COLOR3B.GRAY
			show_des = show_des .. ToColorStr(ts_cfg.bianshen_name or "", des_color)
			if i ~= #ts_idx_list then
				show_des = show_des .. "\n"
			end
		end
	end

	return show_des
end

function TianShenWGData:GetUnionSkillNumTianShenIndex(ts_idx)
	local ts_cfg = self:GetTianShenCfg(ts_idx)
	if not IsEmptyTable(ts_cfg)  then
		local ts_idx_list = Split(ts_cfg.ts_union_skills or "", "|")
		return #ts_idx_list
	end
	return 0
end

function TianShenWGData:GetTianShenHjSkillFlag(seq)
	return self.tianshen_info.tianshen_heji_skill_flag[seq] == 1
end

function TianShenWGData:GetTianShenHjIsUseSkill(seq)
	for k, v in pairs(self.tianshen_info.tianshen_heji_use_skill_list) do
		if v == seq then
			return true
		end
	end

	return false
end

function TianShenWGData:GetTianShenJibanFlag(seq)
	return self.tianshen_info.tianshen_jiban_flag[seq] == 1
end

function TianShenWGData:GetTianShenHeJiList(need_sort)
	local cfg = self:GetUnionSkillCfg()
	local show_list = {}
	for k, v in pairs(cfg) do
		local data = {}
		data.seq = v.skill_id -- 这个是索引
		data.tianshen_list = v.tianshen_list
		data.skill_icon = v.skill_icon
		data.skill_name = v.skill_name
		data.skill_describe = v.skill_describe
		data.tianshen_heji_skill_id = v.tianshen_heji_skill_id
		data.heji_item_id = v.heji_item_id
		data.need_item_num = v.need_item_num
		data.tianshen_list_scale = v.tianshen_list_scale
		data.pos_ui_x = v.pos_ui_x
		data.pos_ui_y = v.pos_ui_y
		data.pos_model_x = v.pos_model_x
		data.pos_model_y = v.pos_model_y
		data.pos_model_z = v.pos_model_z

		local skill_flag = self:GetTianShenHjSkillFlag(data.seq)
		local is_use = self:GetTianShenHjIsUseSkill(data.seq)
		local jiban_flag = self:GetTianShenJibanFlag(data.seq)
		data.skill_flag = skill_flag
		data.is_use = is_use
		data.jiban_flag = jiban_flag
		data.sort = 1
		--策划需求  已出战 > 已激活 > 未激活
		if is_use then
			data.sort = 1
		elseif skill_flag then
			data.sort = 2
		else
			data.sort = 3
		end
			
		table.insert(show_list, data)
	end
	
	if need_sort and not IsEmptyTable(show_list) then
		table.sort(show_list, SortTools.KeyLowerSorters("sort", "seq"))
	end

	return show_list
end


function TianShenWGData:CheckIsTianShenHaJiItem(item_id)
	return self.tianshen_union_skill_item_cfg[item_id] ~= nil
end

-- 天神合击红点
function TianShenWGData:GetTianShenHaJiRemind()
	local list_info = TianShenWGData.Instance:GetTianShenHeJiList()
	for k, v in pairs(list_info) do
		if self:GetTianShenHaJiOneRemind(v.seq) then
			return 1
		end
	end

	return 0
end

function TianShenWGData:GetTianShenHaJiOneRemind(seq)
	if self:GetTianShenHjSkillFlag(seq) then
		return false
	end

	local union_cfg = self:GetUnionSkillCfgById(seq)
	if not union_cfg then
		return false
	end
	
	local active_num = 0
	local ts_idx_list = {}
	local temp_data = Split(union_cfg.tianshen_list or "", "|")
	if not IsEmptyTable(temp_data) then
		for i, v in ipairs(temp_data) do
			ts_idx_list[i] = {}
			ts_idx_list[i].ts_index = tonumber(v)

			if TianShenWGData.Instance:IsActivation(tonumber(v)) then
				active_num = active_num + 1
			end
		end
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(union_cfg.heji_item_id)
	if active_num >= #temp_data and item_num >= union_cfg.need_item_num then
		return true
	end

	return false
end


function TianShenWGData:CheckHaveShuangShengTianShen(ts_index)
    local shuangsheng_show_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarData(ts_index)
    if not shuangsheng_show_data then
        return false, nil
    end

	if shuangsheng_show_data.curr_aura_id == -1 then
		return false, nil
	end

	local app_image_id_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(shuangsheng_show_data.curr_aura_id)
	if app_image_id_data then
		return true, app_image_id_data
	end

	return false, nil
end

function TianShenWGData:GetTianShenImageText(ts_index)
	if IsEmptyTable(self.image_list) then 
		self.image_list = {}
		for k,v in pairs(self.magic_image_cfg) do
			if not self.image_list[k] then
                self.image_list[k] = {}
            end

			local info = {}
			info.id_list = Split(v.image_id, "|")
			info.text_list = Split(v.image_text, "|")
			table.insert(self.image_list[k], info)
		end
	end

	return self.image_list[ts_index]
end

--判断当前技能是否需要展示
function TianShenWGData:GetTianShenSkillInfoNeedShow(ts_index, star_level)
	if ts_index and star_level then
		local rank_name_tab = self:GetTianShenStarSkillData(ts_index)
		local zhu_skill = self:GetTianShenZhuSkill(ts_index)
		for k, v in pairs(zhu_skill) do
			local skill_id = v
			if rank_name_tab[star_level + 1] and rank_name_tab[star_level + 1].skill_id == skill_id then
				return skill_id
			end
		end
	end

	return nil
end

---------------------------------------------------------------------------------------------------------------
---拆分每个技能等级解锁的星级
function TianShenWGData:SplitTianShenSkillLvStar()
	self.tianshen_skill_lv_star = {}

	for index, data in pairs(self.tianshen_star_skill) do
		if self.tianshen_skill_lv_star[index] == nil then
			self.tianshen_skill_lv_star[index] = {}
		end
	
		for _, skill_data in pairs(data) do
			self:SplitTianShenSkillLvStarKey(index, "skill_id0", "skill_level0", skill_data)
			self:SplitTianShenSkillLvStarKey(index, "skill_id1", "skill_level1", skill_data)
			self:SplitTianShenSkillLvStarKey(index, "skill_id2", "skill_level2", skill_data)
			self:SplitTianShenSkillLvStarKey(index, "skill_id3", "skill_level3", skill_data)
		end
	end
end

---拆分每个技能等级解锁的星级
function TianShenWGData:SplitTianShenSkillLvStarKey(index, key_str, level_key, cfg)
	local skill_id = cfg[key_str]

	if self.tianshen_skill_lv_star[index][skill_id] == nil then
		self.tianshen_skill_lv_star[index][skill_id] = {}

		local skill_data = {}
		skill_data.skill_id = cfg[key_str]
		skill_data.skill_lv = cfg[level_key]
		skill_data[level_key] = cfg[level_key]
		skill_data.star_level = cfg.star_level
		table.insert(self.tianshen_skill_lv_star[index][skill_id], skill_data)
		return
	end

	local length = #self.tianshen_skill_lv_star[index][skill_id]
	local final_data = self.tianshen_skill_lv_star[index][skill_id][length]
	if cfg[level_key] > final_data[level_key] then
		local skill_data = {}
		skill_data.skill_id = cfg[key_str]
		skill_data.skill_lv = cfg[level_key]
		skill_data[level_key] = cfg[level_key]
		skill_data.star_level = cfg.star_level
		table.insert(self.tianshen_skill_lv_star[index][skill_id], skill_data)
	end
end

--- 获取一个技能的所有配置
function TianShenWGData:GetTianShenSkillLvStarBySkill(index, key_str)
	local empty = {}
	return ((self.tianshen_skill_lv_star or {})[index] or {})[key_str]
end

-- 获取神饰数据列表
function TianShenWGData:GetTianShenShenShiRefineList(index)
	if (not self.shenshi_refine_cfg) or (not self.shenshi_refine_cfg[index]) then
		return
	end

	local return_list = {}
	local list = self.shenshi_refine_cfg[index]
	local tianshen_data = self:GetShenShiEquipInfo(index)
	local now_jingshen = tianshen_data.jingshen or 1

	for k, v in pairs(list) do
		if v and v.index then
			local data = {}
			data.is_unlock = now_jingshen >= v.need_level
			local per_value = tianshen_data and tianshen_data.shenshi_refine_value_list and tianshen_data.shenshi_refine_value_list[v.hole + 1] or 0
			data.bonus_value = per_value / 100
			data.cfg = v
			table.insert(return_list, data)
		end
	end

	return return_list
end

-- 获取某个天神灵韵红点
function TianShenWGData:GetTianShenShenShiRefineRed(index)
	if (not self.shenshi_refine_cfg) or (not self.shenshi_refine_cfg[index]) then
		return
	end

	local return_list = {}
	local list = self.shenshi_refine_cfg[index]
	local tianshen_data = self:GetShenShiEquipInfo(index)
	local now_jingshen = tianshen_data.jingshen or 1

	for k, v in pairs(list) do
		if v and v.index and now_jingshen >= v.need_level then
			local per_value = tianshen_data and tianshen_data.shenshi_refine_value_list and tianshen_data.shenshi_refine_value_list[v.hole + 1] or 0

			if per_value <= 0 then
				return true
			end

			local now_value =  per_value / 100
			local base_data, baodi_data = self:GetTianShenShenShiRefineInterval(v)
			local baodi_min_value = (baodi_data.min_value or 0) / 100
			local item_num = ItemWGData.Instance:GetItemNumInBagById(v.baodi_cost_item_id)

			if now_value < baodi_min_value and item_num >= v.baodi_cost_item_num then
				return true
			end
		end
	end

	return false
end

function TianShenWGData:GetTianShenShenShiRefineInterval(cfg)
	if not cfg then
		return nil
	end

	local get_per_value = function(str)
		if str == nil or str == "" then
			return nil
		end

		local flair_str_list = Split(str, "|")
		local min_str = flair_str_list[1]
		local max_str = flair_str_list[2]

		local data = {}
		if min_str then
			local min_str_list = Split(min_str, ",")
			data.min_value = tonumber(min_str_list[1]) or 0
		end

		if max_str then
			local min_str_list = Split(max_str, ",")
			data.max_value = tonumber(min_str_list[2]) or 0
		end

		return data
	end

	local baodi_data = get_per_value(cfg.baodi_weight)
	local base_data = get_per_value(cfg.base_weight)
	return base_data, baodi_data
end

-- 获取所有天神是否存在灵韵红点
function TianShenWGData:GetTianShenShenShiAllRefineRed()
	if not self.shenshi_refine_cfg then
		return 0
	end

	for k, v in pairs(self.shenshi_refine_cfg) do
		if self:GetTianShenShenShiRefineRed(k) then
			return 1
		end
	end

	return 0
end

-- 获取神饰数据列表
function TianShenWGData:GetTianShenShenShiRefineShowList(index)
	local cfg_list = (self.shenshi_refine_cfg or {})[index]

	if cfg_list == nil then
		return {}
	end

	local aim_list = {}

	for k, v in pairs(cfg_list) do
		if aim_list[v.need_level] == nil then
			aim_list[v.need_level] = {}
		end

		table.insert(aim_list[v.need_level], v)
	end

	return aim_list
end

-- 获取天神数据
function TianShenWGData:GetNowTianShenInfo()
	return self.tianshen_info	
end

-----------------------------------神格抽奖-----------------------------------------

-- 获取最大挡位
function TianShenWGData:GetGodHoodDrawMaxGrade()
	return self.godhood_draw_grade_cfg and #self.godhood_draw_grade_cfg or 0
end

--是否可以升品.
function TianShenWGData:GetGodHoodDrawIsCanUpLevel()
	local max_grade = self:GetGodHoodDrawMaxGrade()
	if self.godhood_draw_grade >= max_grade then
		return false
	end
	local cfg = self:GetGodHoodDrawGradeCfgByGrade(self.godhood_draw_grade)
	return self.godhood_draw_times >= cfg.max_draw_count - 1
end

-- 获取挡位cfg
function TianShenWGData:GetGodHoodDrawGradeCfgByGrade(grade)
	return self.godhood_draw_grade_cfg[grade]
end

-- 获取展示奖励cfg
function TianShenWGData:GetGodHoodDrawShowRewardCfgByGrade(grade)
	return self.godhood_draw_show_reward_cfg[grade]
end

-- other_cfg
function TianShenWGData:GetGodHoodDrawOtherCfg()
	return self.godhood_draw_other_cfg[1]
end

--神格抽奖 当前档位配置
function TianShenWGData:GetGodHoodDrawGradeCfg()
	return self:GetGodHoodDrawGradeCfgByGrade(self.godhood_draw_grade)
end

--神格抽奖 相关信息
function TianShenWGData:SetGodHoodDrawInfo(protocol)
	self.godhood_draw_grade = protocol.item.grade
	self.godhood_draw_times = protocol.item.draw_times      --总抽奖次数
	self.godhood_draw_lucky = protocol.item.lucky
	self.times_reward_seq = protocol.item.times_reward_seq  --次数奖励领取标记
	self.godhood_score = protocol.item.score
	self.godhood_draw_score = protocol.item.draw_score      -- 剩余抽奖积分(用来抽奖)
	self.convert_times_list = protocol.item.convert_times_list

	RemindManager.Instance:Fire(RemindName.TianShenBaoXia)
	RemindManager.Instance:Fire(RemindName.TianShen_ShenShi)
end

function TianShenWGData:GetGodHoodDrawTimes()
	return self.godhood_draw_times
end

--天神宝匣 剩余材料
function TianShenWGData:GetGodHoodDrawLeftMat()
	return self.godhood_draw_score or 0
end

--概率展示
function TianShenWGData:GetTianShenBaoxiaProbabilityInfo()
	-- local draw_prop_list  = self.godhood_draw_prop_cfg[self.godhood_draw_grade] or {}
	-- if not IsEmptyTable(draw_prop_list) then
	-- 	table.sort(draw_prop_list, SortTools.KeyLowerSorter("random_count"))
	-- end

	if IsEmptyTable(self.probability_list) then
		self.probability_list = {}
		-- 跳转目标档位
		self.probability_list.target_grade = self.godhood_draw_grade
		-- 档位列表
		self.probability_list.group_list = {}

		-- 遍历配置
		for k, draw_prop_list in ipairs(self.godhood_draw_prop_cfg) do
			local data = {}
			-- 类型列表
			data.quality_list = {}
			-- 档位名字
			data.group_name = string.format(Language.TianShen.BaoxiaGroupName, k)
			-- 概率排序
			table.sort(draw_prop_list, SortTools.KeyLowerSorter("random_count"))
			-- 遍历配置，
			for _, v in ipairs(draw_prop_list) do
				-- 判断是否有对应的品质列表table，没有就创建
				if IsEmptyTable(data.quality_list[v.show_type]) then
					data.quality_list[v.show_type] = {
						title = Language.TianShen.BaoxiaProbabilityTitle[v.show_type],
						probability_list = {}
					}
				end
				-- 把数据加入到对应类型的列表
				table.insert(data.quality_list[v.show_type].probability_list, v)

			end
			table.insert(self.probability_list.group_list, data)
		end

	else
		self.probability_list.target_grade = self.godhood_draw_grade
	end

	return self.probability_list
end

--神格抽奖 当前档位配置
-- function TianShenWGData:GetGodHoodDrawGradeCfg()
-- 	local max_cfg_index = self:GetGodHoodDrawMaxGrade()
-- 	if self.godhood_draw_grade then
-- 		local max_cfg = self.godhood_draw_grade_cfg[max_cfg_index]
-- 		self.godhood_draw_grade = max_cfg.grade
-- 		return max_cfg
-- 	end

-- 	local total_count = self:GetTianShenBaoXiaBoxTotlaCount()
-- 	if total_count <= 0 then
-- 		return self.godhood_draw_grade_cfg[1]
-- 	end
-- 	for k, v in pairs(self.godhood_draw_grade_cfg) do
-- 		if total_count >= v.min_draw_count and total_count <= v.max_draw_count then
-- 			if total_count == v.max_draw_count then
-- 				local next_cfg = self:GetGodHoodDrawShowRewardCfgByGrade(v.grade + 1)
-- 				if next_cfg then
-- 					local is_can_uplevel = TianShenWGData.Instance:GetTianShenBaoXiaBoxIsCanUpLevel()
-- 					if is_can_uplevel then
-- 						self.ts_box_old_level = v.grade or 1
-- 						return v
-- 					else
-- 						self.ts_box_old_level = next_cfg.grade
-- 						return next_cfg
-- 					end
-- 				else
-- 					self.ts_box_is_max_level = true
-- 					self.ts_box_old_level = self.godhood_draw_grade_cfg[max_cfg_index].grade
-- 					return self.godhood_draw_grade_cfg[max_cfg_index]
-- 				end
-- 			else
-- 				self.ts_box_old_level = v.grade or 1
-- 				return v
-- 			end
-- 		end
-- 	end

-- 	self.ts_box_is_max_level = true
-- 	self.ts_box_old_level = self.godhood_draw_grade_cfg[max_cfg_index].grade
-- 	return self.godhood_draw_grade_cfg[max_cfg_index]
-- end