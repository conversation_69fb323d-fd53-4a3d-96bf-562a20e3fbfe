WardrobeCastingUpgradeView = WardrobeCastingUpgradeView or BaseClass(SafeBaseView)
function WardrobeCastingUpgradeView:__init()
	self:SetMaskBg(true)
	self:LoadConfig()
end

function WardrobeCastingUpgradeView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 486)})
	self:AddViewResource(0, "uis/view/wardrobe_new_ui_prefab", "layout_casting_upgrade_tips")
end

function WardrobeCastingUpgradeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Wardrobe.ViewCastingUpgradeName

	if not self.casting_item_left then
		self.casting_item_left = WardrobeCastingItemRender.New(self.node_list.casting_item_left)
	end

	if not self.casting_item_right then
		self.casting_item_right = WardrobeCastingItemRender.New(self.node_list.casting_item_right)
	end

	if not self.have_stone_list then
		self.have_stone_list = AsyncListView.New(ItemCell, self.node_list.have_stone_list)
	end

	XUI.AddClickEventListener(self.node_list.btn_upgrade, BindTool.Bind(self.OnClickBtnUpgrade, self))           		-- 成功率提升
end

function WardrobeCastingUpgradeView:ReleaseCallBack()
	if self.casting_item_left then
		self.casting_item_left:DeleteMe()
		self.casting_item_left = nil
	end

	if self.casting_item_right then
		self.casting_item_right:DeleteMe()
		self.casting_item_right = nil
	end

	if self.have_stone_list then
		self.have_stone_list:DeleteMe()
		self.have_stone_list = nil
	end

	self.show_data = nil
end

function WardrobeCastingUpgradeView:ShowIndexCallBack()
end

function WardrobeCastingUpgradeView:SetShowData(show_data)
	self.show_data = show_data
end

function WardrobeCastingUpgradeView:GetNeedLastNumFunc(need_item, need_num)
	local have_num = ItemWGData.Instance:GetItemNumInBagById(need_item)
	local last_num = need_num - have_num
	local com_cfg = WardrobeWGData.Instance:GetStoneComposeInverseByItemId(need_item)

	if last_num <= 0 then
		last_num = 0
	end

	if com_cfg == nil or last_num == 0 then
		return need_item, last_num
	else
		return self:GetNeedLastNumFunc(com_cfg.stone_item_id, com_cfg.consume_num * last_num)
	end
end

function WardrobeCastingUpgradeView:OnFlush()
	if not self.show_data then
		return
	end

	self.casting_item_left:SetData(self.show_data.now_seq)
	self.casting_item_right:SetData(self.show_data.next_seq)
	local stone_next_cfg = WardrobeWGData.Instance:GetStoneCfgBySeq(self.show_data.next_seq)
	local next_quality = stone_next_cfg and stone_next_cfg.stone_quality
	local cfg = WardrobeWGData.Instance:GetStoneCfgByQualityPart(next_quality, self.show_data.stone_part)

	local com_cfg = WardrobeWGData.Instance:GetStoneComposeInverseByItemId(cfg.stone_item_id)
	-- 合成表，最后算出需要多少个最低级的, 返回最后配置和个数
	local last_item_id, need_last_num = self:GetNeedLastNumFunc(com_cfg.stone_item_id, com_cfg.consume_num)
	local last_com_cfg = WardrobeWGData.Instance:GetStoneComposeByItemId(last_item_id)
	self.enough_price = last_com_cfg.price * need_last_num
	
	-- 展示剩下的石头
	local aim_quality = next_quality - 1
	local stone_list = {}

	for i = 1, aim_quality do
		local cfg = WardrobeWGData.Instance:GetStoneCfgByQualityPart(i, self.show_data.stone_part)
		if cfg and cfg.stone_item_id then
			local have_num = ItemWGData.Instance:GetItemNumInBagById(cfg.stone_item_id)
			if have_num > 0 then
				table.insert(stone_list, {item_id = cfg.stone_item_id, num = have_num})
			end
		end
	end

	self.node_list.no_have_stone_tips:CustomSetActive(IsEmptyTable(stone_list))
	self.have_stone_list:SetDataList(stone_list)
	self.node_list.price_text.text.text = self.enough_price
end

function WardrobeCastingUpgradeView:OnClickBtnUpgrade()
	if not self.show_data then
		return
	end

	if not RoleWGData.Instance:GetIsEnoughUseGold(self.enough_price) then
		VipWGCtrl.Instance:OpenTipNoGold()
		return
	end

	WardrobeWGCtrl.Instance:SendShiZhuangForgeCpmposeStone(self.show_data.server_seq, self.show_data.stone_part)
	self:Close()
end