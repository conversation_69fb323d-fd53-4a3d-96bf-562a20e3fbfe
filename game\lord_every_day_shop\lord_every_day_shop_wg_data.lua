LordEveryDayShopWGData = LordEveryDayShopWGData or BaseClass()
function LordEveryDayShopWGData:__init()
    if LordEveryDayShopWGData.Instance ~= nil then
		print_error("[LordEveryDayShopWGData] attempt to create singleton twice!")
		return
	end
	LordEveryDayShopWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("every_day_shop_cfg_auto")
    self.other_cfg = cfg.other[1]
    self.shop_cfg = ListToMap(cfg.shop, "seq", "grade")
	self.open_grade_cfg = cfg.config_param

	self.cur_grade = 0
	self.every_day_lingzhu_shop_buy = {}
	self.every_day_lingzhu_shop_choose = {}

	RemindManager.Instance:Register(RemindName.LoadEveryDayShop, BindTool.Bind(self.MainLoadShopRemind, self))
end

function LordEveryDayShopWGData:__delete()
	LordEveryDayShopWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.LoadEveryDayShop)
end

function LordEveryDayShopWGData:SetAllInfo(protocol)
	self.cur_grade = protocol.grade
	self.every_day_lingzhu_shop_buy = protocol.every_day_lingzhu_shop_buy
	self.every_day_lingzhu_shop_choose = protocol.every_day_lingzhu_shop_choose
end

function LordEveryDayShopWGData:GetShopCfgBySeq(seq)
	local grade = self:GetCurShopGrade()
	return (self.shop_cfg[seq] or {})[grade]
end

function LordEveryDayShopWGData:GetOtherCfg()
	return self.other_cfg
end

function LordEveryDayShopWGData:GetCurShopGrade()
	return self.cur_grade
end

function LordEveryDayShopWGData:GetShowShopInfo()
	local show_shop_list = {}
	if IsEmptyTable(self.every_day_lingzhu_shop_choose) then
		return show_shop_list
	end

	for k, v in pairs(self.every_day_lingzhu_shop_choose) do
		local shop_cfg = self:GetShopCfgBySeq(k)
		if v == 1 and shop_cfg then
			local data = {}
			data.seq = shop_cfg.seq
			data.cfg = shop_cfg
			data.buy_time = self.every_day_lingzhu_shop_buy[k] or 0
			-- data.all_buy = data.buy_time >= data.cfg.buy_limit and 1 or 0
			table.insert(show_shop_list, data)
		end
	end

	if not IsEmptyTable(show_shop_list) then
		table.sort(show_shop_list, SortTools.KeyLowerSorters("seq")) -- "all_buy", 
	end

	return show_shop_list
end

function LordEveryDayShopWGData:SetRechargeRedPoint()
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	PlayerPrefsUtil.SetInt(role_id .."lord_every_day_shop" .. cur_day, 1)
end

function LordEveryDayShopWGData:GetRechargeRedPoint()
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local flag = PlayerPrefsUtil.GetInt(role_id .."lord_every_day_shop" .. cur_day) == 0
	return flag
end

function LordEveryDayShopWGData:MainLoadShopRemind()
	if not FunOpen.Instance:GetFunIsOpened("LordEveryDayShopView") then
		return 0
	end

	if self:GetRechargeRedPoint() then
		return 1
	else
		return 0
	end
end
