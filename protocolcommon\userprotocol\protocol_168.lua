----------------------------寻宝-雷法抽奖 start ----------------------------
CSThunderDrawOperate = CSThunderDrawOperate or BaseClass(BaseProtocolStruct)
function CSThunderDrawOperate:__init()
    self.msg_type = 16800
end

function CSThunderDrawOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCThunderDrawInfo = SCThunderDrawInfo or BaseClass(BaseProtocolStruct)
function SCThunderDrawInfo:__init()
	self.msg_type = 16801
end

function SCThunderDrawInfo:Decode()
    self.level = MsgAdapter.ReadInt()
    self.draw_times = MsgAdapter.ReadInt()
    self.score = MsgAdapter.ReadInt()
    self.count_reward_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)

    self.shop_item_list = {}
    for i = 0, 79 do
        local buy_times = MsgAdapter.ReadInt()
        self.shop_item_list[i] = buy_times
    end
end

-- // 抽奖更新信息
SCThunderDrawUpdate = SCThunderDrawUpdate or BaseClass(BaseProtocolStruct)
function SCThunderDrawUpdate:__init()
	self.msg_type = 16802
end

function SCThunderDrawUpdate:Decode()
	local data = {}
	data.level = MsgAdapter.ReadInt()
	data.draw_times = MsgAdapter.ReadInt()
    data.score = MsgAdapter.ReadInt()
    data.count_reward_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)
    self.change_data = data
end

-- 兑换商店更新信息
SCThunderDrawShopUpdate = SCThunderDrawShopUpdate or BaseClass(BaseProtocolStruct)
function SCThunderDrawShopUpdate:__init()
	self.msg_type = 16803
end

function SCThunderDrawShopUpdate:Decode()
	local data = {}
	data.seq = MsgAdapter.ReadInt()
	data.buy_times = MsgAdapter.ReadInt()
	self.change_data = data
end

-- 抽奖结果
SCThunderDrawRewardResult = SCThunderDrawRewardResult or BaseClass(BaseProtocolStruct)
function SCThunderDrawRewardResult:__init()
	self.msg_type = 16804
end

function SCThunderDrawRewardResult:Decode()
    --大奖数量 
    self.mode_type = MsgAdapter.ReadInt()
	self.big_reward_count = MsgAdapter.ReadInt()
    self.reward_list = {}

    local list_count = MsgAdapter.ReadInt()
    for i = 1, list_count do
		local data = {}
		data.item_id = MsgAdapter.ReadUShort()
		data.reversh = MsgAdapter.ReadChar()
		data.is_bind = MsgAdapter.ReadChar()
		data.num = MsgAdapter.ReadInt()
        self.reward_list[i] = data
	end
end

-- 个人记录
SCThunderDrawPersonalRecord = SCThunderDrawPersonalRecord or BaseClass(BaseProtocolStruct)
function SCThunderDrawPersonalRecord:__init()
	self.msg_type = 16805
end

function SCThunderDrawPersonalRecord:Decode()
    local count = MsgAdapter.ReadInt()
	self.record_list = {}
	for i = 1, count do 
        local vo = {}
        vo.consume_time = MsgAdapter.ReadInt()
        vo.item_data = ProtocolStruct.ReadItemDataWrapper()
        self.record_list[i] = vo
	end
end

---全服记录
SCThunderDrawWorldRecord = SCThunderDrawWorldRecord or BaseClass(BaseProtocolStruct)
function SCThunderDrawWorldRecord:__init()
	self.msg_type = 16806
end

function SCThunderDrawWorldRecord:Decode()
    local count = MsgAdapter.ReadInt()
    self.record_list = {}
	for i = 1, count do
        local vo = {}
        vo.role_id = MsgAdapter.ReadInt()
        vo.role_name = MsgAdapter.ReadStrN(32)
        vo.consume_time = MsgAdapter.ReadUInt()
        vo.item_data = ProtocolStruct.ReadItemDataWrapper()
        self.record_list[i] = vo	
	end
end

SCThunderDrawStorgeInfo =  SCThunderDrawStorgeInfo or BaseClass(BaseProtocolStruct)
function SCThunderDrawStorgeInfo:__init()
	self.msg_type = 16807
end

function SCThunderDrawStorgeInfo:Decode()
	local count = MsgAdapter.ReadInt()
	self.item_list = {}
	for i = 1, count do
		self.item_list[i] = ProtocolStruct.ReadItemDataWrapper()
	end
end

----------------------------寻宝-雷法抽奖 end ----------------------------

----------------------------仙盟任务 start ----------------------------

-- 仙盟任务-请求操作
CSGuildActiveTaskOperate = CSGuildActiveTaskOperate or BaseClass(BaseProtocolStruct)
function CSGuildActiveTaskOperate:__init()
    self.msg_type = 16812
end

function CSGuildActiveTaskOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

-- 仙盟活跃任务信息
SCGuildActiveTaskInfo = SCGuildActiveTaskInfo or BaseClass(BaseProtocolStruct)
function SCGuildActiveTaskInfo:__init()
	self.msg_type = 16813
end

function SCGuildActiveTaskInfo:Decode()
	self.personal_task_list = {}
	self.guild_task_list = {}
	for i = 0, 14 do
		self.personal_task_list[i] = ProtocolStruct.ReadGuildTaskInfo()		-- 个人任务
	end
	for i = 0, 14 do
		self.guild_task_list[i] = ProtocolStruct.ReadGuildTaskInfo()		-- 仙盟任务
	end
end

-- 仙盟活跃任务更新
SCGuildActiveTaskUpdate = SCGuildActiveTaskUpdate or BaseClass(BaseProtocolStruct)
function SCGuildActiveTaskUpdate:__init()
	self.msg_type = 16814
end

function SCGuildActiveTaskUpdate:Decode()
	self.task_type = MsgAdapter.ReadInt()					-- 任务类型
	self.seq = MsgAdapter.ReadInt()							-- 任务索引
	self.task_item = ProtocolStruct.ReadGuildTaskInfo()
end

-- 个人活跃积分信息
SCGuildPersonActiveScoreInfo = SCGuildPersonActiveScoreInfo or BaseClass(BaseProtocolStruct)
function SCGuildPersonActiveScoreInfo:__init()
	self.msg_type = 16815
end

function SCGuildPersonActiveScoreInfo:Decode()
	self.active_score = MsgAdapter.ReadInt()					-- 自己的活跃积分
	self.active_score_reward_flag = MsgAdapter.ReadInt()		-- 活跃奖励领取标记
end

-- 仙盟活跃排行信息
SCGuildActiveScoreRankInfo = SCGuildActiveScoreRankInfo or BaseClass(BaseProtocolStruct)
function SCGuildActiveScoreRankInfo:__init()
	self.msg_type = 16816
end

function SCGuildActiveScoreRankInfo:Decode()
	local count = MsgAdapter.ReadInt()
	self.rank_list = {}
	for i = 1, count do
		self.rank_list[i] = ProtocolStruct.ReadGuildActiveScoreRankInfo()
	end
end

-- 仙盟活跃积分信息
SCGuildActiveScoreInfo = SCGuildActiveScoreInfo or BaseClass(BaseProtocolStruct)
function SCGuildActiveScoreInfo:__init()
	self.msg_type = 16817
end

function SCGuildActiveScoreInfo:Decode()
	self.guild_active_score = MsgAdapter.ReadInt()					-- 仙盟的活跃积分
end

----------------------------仙盟任务 end ----------------------------