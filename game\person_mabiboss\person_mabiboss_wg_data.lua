PersonMaBiBossWGData = PersonMaBiBossWGData or BaseClass()

function PersonMaBiBossWGData:__init()
	if PersonMaBiBossWGData.Instance then
		print_error("[PersonMaBiBossWGData] Attempt to create singleton twice!")
		return
	end

	PersonMaBiBossWGData.Instance = self
	local cfg = ConfigManager.Instance:GetAutoConfig("special_logic_fb_auto")
	self.fb_cfg = cfg.fb[1]
	self.logic_cfg = cfg.logic[1]

	self.entered_palsy_experience_fb_times = 0
	self.is_pass = 0
	self.act_info = {}
	self:InitBossMaBiActInfo()
end

function PersonMaBiBossWGData:__delete()
	PersonMaBiBossWGData.Instance = nil
	self.is_pass = nil
	self.act_info = nil
end

-- 策划说只让进一次
function PersonMaBiBossWGData:GetCanEnterFBFlag()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local day_limit = cur_day >= self.fb_cfg.start_day and cur_day <= self.fb_cfg.end_day
	local enter_time_limit = self.entered_palsy_experience_fb_times < self:GetCanEnterFbTime()
	return day_limit and enter_time_limit
end

function PersonMaBiBossWGData:SetPalsyExperienceInfo(protocol)
	self.entered_palsy_experience_fb_times = protocol.entered_palsy_experience_fb_times
end

function PersonMaBiBossWGData:GetPersonFBCfg()
	return self.fb_cfg
end

function PersonMaBiBossWGData:GetRewardDataList()
	return self.fb_cfg.pass_item or {}
end

function PersonMaBiBossWGData:GetCanEnterFbTime()
	return self.logic_cfg.param3 or 0
end

function PersonMaBiBossWGData:GetFBIsPass()
	return self.is_pass == 1
end

function PersonMaBiBossWGData:SetPalsyExperienceFbInfo(protocol)
	self.time_out_stamp = protocol.time_out_stamp
	self.kick_timestamp = protocol.kick_timestamp
	self.scene_type = protocol.scene_type
	self.is_finish =  protocol.is_finish
	self.is_pass = protocol.is_pass
end

function PersonMaBiBossWGData:InitBossMaBiActInfo()
	local daily_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(ACTIVITY_TYPE.BOSS_MABI_FB)
	self.act_info = daily_cfg or {}
end

function PersonMaBiBossWGData:GetBossMaBiActInfo()
	return self.act_info
end