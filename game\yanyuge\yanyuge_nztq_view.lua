function YanYuGePrivilegeView:NZTQLoadCallBack()
    if not self.natq_privilege_desc_list then
        self.natq_privilege_desc_list = AsyncListView.New(NZTQPrivilegeDescListItemCellRender, self.node_list.natq_privilege_desc_list)
    end

    if not self.natq_reward_list then
        self.natq_reward_list = AsyncListView.New(ItemCell, self.node_list.natq_reward_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_natq_buy, BindTool.Bind(self.OnClickNZTQBuyBtn, self))
end

function YanYuGePrivilegeView:NZTQShowIndexCallBack()
    
end

function YanYuGePrivilegeView:NZTQReleaseCallBack()
    if self.natq_privilege_desc_list then
        self.natq_privilege_desc_list:DeleteMe()
        self.natq_privilege_desc_list = nil
    end

    if self.natq_reward_list then
        self.natq_reward_list:DeleteMe()
        self.natq_reward_list = nil
    end
end

function YanYuGePrivilegeView:NZTQOnFlush(param_t)
    local tequan_cfg = YanYuGeWGData.Instance:GetNeZhaTeQuanCfg()

    if IsEmptyTable(tequan_cfg) then
        return  
    end

    local is_get_privilege = YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(tequan_cfg.seq)
    local is_get_privilege_reward = YanYuGeWGData.Instance:IsGetTeQuanEveryDayReward(tequan_cfg.seq)
    local is_has_day_reward = YanYuGeWGData.Instance:IsTequanHasDayReward(tequan_cfg.seq)
    local is_get_tequan_reward = YanYuGeWGData.Instance:IsGetTequanReward(self.data.seq)

    -- self.node_list.flag_get_privilege:CustomSetActive(is_get_privilege and is_get_privilege_reward)
    -- self.node_list.btn_natq_buy:CustomSetActive(not is_get_privilege or (is_get_privilege and not is_get_privilege_reward))
    -- self.node_list.btn_natq_buy_remind:CustomSetActive(is_get_privilege and not is_get_privilege_reward)
    self.node_list.flag_get_privilege:CustomSetActive(is_get_privilege and is_get_tequan_reward and (not is_has_day_reward or (is_has_day_reward and is_get_privilege_reward)))
    self.node_list.btn_natq_buy:CustomSetActive(not is_get_privilege or not is_get_tequan_reward or (is_has_day_reward and not is_get_privilege_reward))
    self.node_list.btn_natq_buy_remind:CustomSetActive(is_get_privilege and (not is_get_privilege_reward or (is_has_day_reward and not is_get_privilege_reward)))

    if not is_get_privilege then
        local desc_buy_str = ""

        if tequan_cfg.buy_type == 1 then
            desc_buy_str = RoleWGData.GetPayMoneyStr(tequan_cfg.price, tequan_cfg.rmb_type, tequan_cfg.rmb_seq)
        elseif tequan_cfg.buy_type == 2 then
            desc_buy_str = string.format(Language.YanYuGe.ScoreStr, tequan_cfg.price)
        elseif tequan_cfg.buy_type == 3 then
            desc_buy_str = string.format(Language.YanYuGe.RealRechargeNumStr, RoleWGData.GetPayMoneyStr(tequan_cfg.price, tequan_cfg.rmb_type, tequan_cfg.rmb_seq))
        end
    
        self.node_list.desc_nztq_buy.tmp.text = desc_buy_str
        self.node_list.desc_nztq_buy_tip.tmp.text = tequan_cfg.reward_str

        local score = YanYuGeWGData.Instance:GetCurScore()
        -- self.node_list.btn_natq_buy_remind:CustomSetActive(tequan_cfg.buy_type == 2 and score >= tequan_cfg.price)
    else
        if not is_get_privilege_reward then
            self.node_list.desc_nztq_buy.tmp.text = Language.YanYuGe.TeQuanCanLingQuStr
        end

        self.node_list.desc_nztq_buy_tip.tmp.text = ""
    end

    local privilege_lable = string.split(tequan_cfg.privilege_lable, "|")
    local desc_tab = {}
    if not IsEmptyTable(privilege_lable) then
        for k, v in pairs(privilege_lable) do
            if "" ~= v then
                table.insert(desc_tab, {desc = v})        
            end
        end
    end

    self.natq_privilege_desc_list:SetDataList(desc_tab)
    self.natq_reward_list:SetDataList(tequan_cfg.reward_item)
end

function YanYuGePrivilegeView:OnClickNZTQBuyBtn()
    local tequan_cfg = YanYuGeWGData.Instance:GetNeZhaTeQuanCfg()

    if IsEmptyTable(tequan_cfg) then
        return  
    end

    if tequan_cfg.last_seq >= 0 then
        if not YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(tequan_cfg.last_seq) then
            local last_tequan_cfg = YanYuGeWGData.Instance:GetTeQuanCfgBySeq(tequan_cfg.last_seq)
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YanYuGe.TeQuanUnLockLastTipStr, last_tequan_cfg.name))
            return
        end
    end

    local is_get_privilege = YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(tequan_cfg.seq)
    local is_get_privilege_reward = YanYuGeWGData.Instance:IsGetTeQuanEveryDayReward(tequan_cfg.seq)

    if not is_get_privilege then
        if tequan_cfg.buy_type == 1 then
            RechargeWGCtrl.Instance:Recharge(tequan_cfg.price, tequan_cfg.rmb_type, tequan_cfg.rmb_seq)
        elseif tequan_cfg.buy_type == 2 then
            local score = YanYuGeWGData.Instance:GetCurScore()
            if score >= self.data.price then
                YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.BUY_TEQUAN, tequan_cfg.seq)
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.NoEnoughScore)
            end
        elseif tequan_cfg.buy_type == 3 then
            local real_recharge_num = YanYuGeWGData.Instance:GetRealRechargeNum()

            if real_recharge_num < self.data.price then
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YanYuGe.RealRechargeNumNotEnoughStr, RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)))
                ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
            end
        end
    else
        local is_get_tequan_reward = YanYuGeWGData.Instance:IsGetTequanReward(tequan_cfg.seq)
        if not is_get_tequan_reward then
            YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_TEQUAN_REWARD, tequan_cfg.seq)
        else
            local is_has_day_reward = YanYuGeWGData.Instance:IsTequanHasDayReward(tequan_cfg.seq)
            local is_get_privilege_reward = YanYuGeWGData.Instance:IsGetTeQuanEveryDayReward(tequan_cfg.seq)
            if is_has_day_reward and not is_get_privilege_reward then
                YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_EVERY_DAY_REWARD, tequan_cfg.seq)
            end
        end

        -- if not is_get_privilege_reward then
        --     YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_EVERY_DAY_REWARD, tequan_cfg.seq)
        -- end
    end
end

------------------------------------NZTQPrivilegeDescListItemCellRender------------------------------------
NZTQPrivilegeDescListItemCellRender = NZTQPrivilegeDescListItemCellRender or BaseClass(BaseRender)

function NZTQPrivilegeDescListItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc_content.tmp.text = self.data.desc
end