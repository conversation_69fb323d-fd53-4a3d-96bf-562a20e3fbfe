WelfareFriend = WelfareFriend or BaseClass(SafeBaseView)

function WelfareFriend:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/welfare_ui_prefab", "layout_welfarefriendlist")
	self:SetMaskBg()
	self.view_layer = UiLayer.Pop
	self.m_choose_user_info = nil
	self.default_select_index = 1 --默认选择第一个
end

function WelfareFriend:__delete()

end

function WelfareFriend:ReleaseCallBack()
	if nil ~= self.friend_list then
		self.friend_list:DeleteMe()
		self.friend_list = nil
	end
end

function WelfareFriend:LoadCallBack()	
	XUI.AddClickEventListener(self.node_list["btn_chooseOK"], BindTool.Bind1(self.OnClickOK, self))
	XUI.AddClickEventListener(self.node_list["btn_oneclick"], BindTool.Bind1(self.OnClickOKALL, self))
	self:CreateFriendList()
end

function WelfareFriend:CloseCallBack()
	
end

function WelfareFriend:SetData(index)
	self.index = index
	self:Open()
end

function WelfareFriend:OpenCallBack()
	SocietyWGCtrl.Instance:SendFriendInfoReq()
end

function WelfareFriend:ShowIndexCallBack()
	self:Flush()
end

function WelfareFriend:OnClickOK()
		local item = self.friend_list:GetDataList()
		if nil ~= item and next(item) ~= nil then  
			WelfareWGCtrl.Instance:SendFriendinviteOperate(FRIENDINVITE_TYPE.FRIENDINVITE_REQ_INVITE, item[1].user_id)
		end
	self:Close()
end

function WelfareFriend:OnClickOKALL()
		local item = self.friend_list:GetDataList()
		for k, v in pairs(item) do   
			if v.is_online == 1 then 
				WelfareWGCtrl.Instance:SendFriendinviteOperate(FRIENDINVITE_TYPE.FRIENDINVITE_REQ_INVITE, item[k].user_id)
			end
		end
	self:Close()
end

function WelfareFriend:CreateFriendList()
	local ph = self.node_list["ph_friend_list"]
	self.friend_list = AsyncListView.New(SocietyFriendListRender, ph)
	self.friend_list:SetDefaultSelectIndex(self.default_select_index) 
	self.friend_list:SetSelectCallBack(BindTool.Bind1(self.FriendListSelectCallBack, self))	
end

--好友列表行选中事件
function WelfareFriend:FriendListSelectCallBack(item_data)
	if nil ~= item_data and nil ~= item_data.data then
		self.m_choose_user_info = item_data.data
	end
end

--刷新好友面板
function WelfareFriend:OnFlush()
	local data_list = {}
	local role_list = Scene.Instance:GetRoleList()

	if self.index == 1 then
		local data = SocietyWGData.Instance:GetFriendList()
		local onil_list = {}
		if nil ~= data and nil ~= self.friend_list then
			for k,v in ipairs(data) do 
				if v.is_online == 1 then
					table.insert(onil_list, v)
				end
			end
			self.friend_list:SetDataList(onil_list)
			self.friend_list:JumpToTop(true)
		end
		self:SetViewName(Language.Society.FriendlistTip)
	else
		for k, v in pairs(role_list) do
			if Scene.Instance:IsEnemy(v) and v:GetVo().is_shadow ~= 1 then
				local role_vo = {}
				role_vo.user_id = v:GetVo().role_id
				role_vo.gamename = v:GetVo().name
				role_vo.intimacy =v:GetVo().intimacy
				role_vo.camp =v:GetVo().camp
				role_vo.sex =v:GetVo().sex
				role_vo.prof =v:GetVo().prof
				role_vo.is_online = 1
				role_vo.level = v:GetVo().level
				role_vo.capability = v:GetVo().capability
				role_vo.avatar_key_big = v:GetVo().avatar_key_big
				role_vo.avatar_key_small = v:GetVo().avatar_key_small
				role_vo.last_logout_timestamp = 0
				table.insert(data_list, role_vo)
			end
		end
		if nil ~= data_list and nil ~= self.friend_list then
			self.friend_list:SetDataList(data_list)
			self.friend_list:JumpToTop(true)
		end
		self:SetViewName(Language.Society.NearInviety)
	end

end