﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_PlayModeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(UnityEngine.PlayMode));
		<PERSON><PERSON>("StopSameLayer", get_StopSameLayer, null);
		<PERSON><PERSON>("StopAll", get_StopAll, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UnityEngine.PlayMode>.Check = CheckType;
		StackTraits<UnityEngine.PlayMode>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.PlayMode arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.PlayMode), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_StopSameLayer(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.PlayMode.StopSameLayer);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_StopAll(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.PlayMode.StopAll);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.PlayMode o = (UnityEngine.PlayMode)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

