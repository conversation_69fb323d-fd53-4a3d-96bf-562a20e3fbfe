----------------------- 仙器物品 ----------------------------------

XianQiZhenLianItem = XianQiZhenLianItem or BaseClass(BaseRender)

function XianQiZhenLianItem:__init()
    self.save_pos_x = 0
    self.save_pos_y = 0
    self.save_item_id = 0
    self.save_item_star = nil
    self.is_five_point_star = false
end

function XianQiZhenLianItem:DoLoad(parent)
    self:SetIsFivePointStar(true)
    self:LoadAsset("uis/view/shenji_tianci_ui_prefab", "xianqi_zhenlian_render", parent.transform)
end

function XianQiZhenLianItem:SavePos(x, y)
    self.save_pos_x = x
    self.save_pos_y = y
end

function XianQiZhenLianItem:GetPos()
    return self.save_pos_x, self.save_pos_y
end

function XianQiZhenLianItem:SetItemID(item_id)
    self.save_item_id = item_id or 0
end

function XianQiZhenLianItem:GetItemID()
    return self.save_item_id
end

function XianQiZhenLianItem:SetItemStar(star_num)
    self.save_item_star = star_num
end

function XianQiZhenLianItem:GetItemStar()
    return self.save_item_star
end

function XianQiZhenLianItem:SetIsFivePointStar(_bool)
    self.is_five_point_star = _bool
end

function XianQiZhenLianItem:LoadCallBack()
    self:SetAnchoredPosition(self.save_pos_x, self.save_pos_y)
    self:InitSummonItem()
end

function XianQiZhenLianItem:InitSummonItem()
    local star_list = {}
    local star_root_list = {}
    for i=1,5 do
        star_list[i] = self.node_list["star_" .. i]
        star_root_list[i] = self.node_list["star_root_" .. i]
    end
    self.star_list = star_list
    self.star_root_list = star_root_list

    if self.node_list.item_bg then
        XUI.AddClickEventListener(self.node_list.item_bg, BindTool.Bind1(self.OnClickSummonItem, self))
    end
end

function XianQiZhenLianItem:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end
    self:SetItemID(data.item_id)
   -- self:FlushItemBg()
   -- self:FlushItemIcon()
    self:FlushItemStar()
end

--[[ 
function XianQiZhenLianItem:FlushItemBg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.save_item_id)
    local color = item_cfg and item_cfg.color or 1
    if self.node_list.item_bg then
        local bundle,asset = ResPath.GetCommon("a1_item_round_bg_" .. color)
        self.node_list.item_bg.image:LoadSprite(bundle, asset)
    end 
    if self.node_list.item_effect then
        local asset_name = BaseCell_Ui_Circle_Effect[color]
        local bundle,asset = ResPath.GetWuPinKuangEffectUi(asset_name)
        self.node_list.item_effect:ChangeAsset(bundle, asset)
    end
end ]]

function XianQiZhenLianItem:FlushItemStar()
    local star_num = self:GetItemStar()
    if not star_num then
        local xianqi_cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(self.save_item_id)
        star_num = xianqi_cfg and xianqi_cfg.base_star or 0
    end
    
   if self.is_five_point_star then
        local star_root_list = self.star_root_list
        for i=1,#star_root_list do
            star_root_list[i]:SetActive(i <= star_num)
        end
    else 
        local star_list = self.star_list
        for i=1,#star_list do
            star_list[i]:SetActive(i <= star_num)
        end
    end
end

function XianQiZhenLianItem:FlushItemName()
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.save_item_id)
    if item_cfg and self.node_list.name_label then
        self.node_list.name_label.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    end
end

function XianQiZhenLianItem:OnClickSummonItem()
    local item_id = self:GetItemID()
    local item_star = self:GetItemStar()
    local xianqi_cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(item_id)
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if xianqi_cfg and item_cfg then
        local item_data = SiXiangCallWGData.Instance:GetXianQiZhenLianItemData()
        item_data.item_id = item_id
        item_data.star = item_star or xianqi_cfg.base_star or 0
        item_data.color = item_cfg.color or 1
        item_data.fight_soul_type = xianqi_cfg.sixiang_type or 1
        item_data.is_bind = item_cfg.isbind or 1
        TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_NORMAL, nil)
    end
end

function XianQiZhenLianItem:OnSelectChange(is_select)
    self.node_list.select_image:SetActive(is_select)
end