OperationActivityWGData = OperationActivityWGData or BaseClass()

function OperationActivityWGData:__init()
	if OperationActivityWGData.Instance then
		ErrorLog("[OperationActivityWGData] Attemp to create a singleton twice !")
	end

	OperationActivityWGData.Instance = self

	self.activity_open_callback = {} --运用活动开启回调
	self.alearday_activity_open = {} -- 已经开启的运营活动（仅限于服务器做的通知，不包括客户端的自身拦截）
	self.can_activity_open = {} -- 已经开启的运营活动（真正意义上的开启所有条件都满足）
	self.rember_activity_event_type = {} --记录的监听事件类型
	self.remind_callback = {} --红点回调方法判断
	self.index_activity_cfg = {}--索引当做键值，活动号是value
	self.need_monitor_item = {}--需要被监听的物品
	self.btn_one_remind = {}--按钮1的红点状态
	self.btn_two_remind = {}--按钮2的红点状态

	self:SetTabIndex()

	RemindManager.Instance:Register(RemindName.OperationActivity, BindTool.Bind(self.IsShowMainViewRedPoint, self))
	RemindManager.Instance:Register(RemindName.OperationActivity_Two, BindTool.Bind(self.IsShowMainViewTwoRedPoint, self))

	--角色等级改变
	self.role_level_change = GlobalEventSystem:Bind(OtherEventType.ROLE_LEVEL_UP, BindTool.Bind(self.RoleLevelChange, self))

	--背包物品改变监听
	-- self.item_data_event = BindTool.Bind1(self.BagItemDataChange, self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)

	self.day_pass_change = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind(self.DayPassChange, self))

	--人物信息下发监听打开（打开后对所有活动进行一次开启判断，因为有些开启参数在活动协议后下发导致判断的开启不准确所以游戏完全登陆后在全面进行一次判断）
	--这个监听也许需要根据实际情况考究
	self.main_role_info = GlobalEventSystem:Bind(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind(self.OnLoadingComplete, self, OPERATION_EVENT_TYPE.LEVEL))
	--背包物品首次下发
	self.bag_info = GlobalEventSystem:Bind(OtherEventType.BAG_FIRST_INFO, BindTool.Bind(self.OnLoadingComplete, self, OPERATION_EVENT_TYPE.ITEM))

	self.btn_one_remind_change = BindTool.Bind(self.BtnOneRemindChangeCallBack, self)
	self.btn_two_remind_change = BindTool.Bind(self.BtnTwoRemindChangeCallBack, self)

	self:SetRemindEventList()
end

function OperationActivityWGData:__delete()
	OperationActivityWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.OperationActivity)
	RemindManager.Instance:UnRegister(RemindName.OperationActivity_Two)

	self.activity_open_callback = nil
	self.alearday_activity_open = nil
	self.can_activity_open = nil
	self.rember_activity_event_type = nil
	self.remind_callback = nil
	self.index_activity_cfg = nil
	self.need_monitor_item = nil

	if self.role_level_change then
		GlobalEventSystem:UnBind(self.role_level_change)
		self.role_level_change = nil
	end

	-- if self.item_data_event then
	-- 	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	-- 	self.item_data_event = nil
	-- end

	if self.main_role_info then
		GlobalEventSystem:UnBind(self.main_role_info)
		self.main_role_info = nil
	end

	if self.bag_info then
		GlobalEventSystem:UnBind(self.bag_info)
		self.bag_info = nil
	end

	if self.day_pass_change then
		GlobalEventSystem:UnBind(self.day_pass_change)
		self.day_pass_change = nil
	end

	RemindManager.Instance:UnBind(self.btn_one_remind_change)
	RemindManager.Instance:UnBind(self.btn_two_remind_change)
end

----------------------------------------------------------

--设置界面信息
function OperationActivityWGData:SetTabIndex()
	self.tab_index_name = {}
	self.remind_tab = {}

	local activity_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_config_auto").operation_activity_dec
	table.sort(activity_cfg, SortTools.KeyLowerSorter("rank_id"))

	for k,v in ipairs(activity_cfg) do
		local tab_index = v.rank_id * 10
		--定义格子界面tabindex
		if v.activity_type == ACTIVITY_TYPE.OPERA_ACT_CTN_RECHARGE then
			TabIndex.operation_act_ctn_recharge = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN then
			TabIndex.operation_act_task_chain = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA then
			TabIndex.operation_act_xianshi_miaosha = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERATION_FIRST_RECHARGE then
			TabIndex.operation_act_first_recharge = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_TOTAL_RECHARGE then
			TabIndex.operation_act_total_recharge = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_MOWANG_YOULI then
			TabIndex.operation_act_mowang_youli = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS then
			TabIndex.operation_act_watering_flowers = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_QUANFU_JUANXIAN then
			TabIndex.operation_act_quanfu_juanxian = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_TURNTABLE then
			TabIndex.operation_act_turntable = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_FISH then
			TabIndex.operation_act_fish = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_EXCHANGE then
			TabIndex.operation_act_exchange = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_DUOBEI_YOULI then
			TabIndex.operation_act_duobei = tab_index
		-- elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_CHUSHEN then
		-- 	TabIndex.operation_act_chushen = tab_index 
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN then
			TabIndex.operation_act_mowu_jianglin = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_LOGIN_REWARD then
			TabIndex.operation_act_login_reward = tab_index 
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN then
			TabIndex.operation_act_happy_kuanghuan = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_FENGZHENG then
			TabIndex.operation_act_fengzheng = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.OPERA_ACT_IMAGE_SHOW then
			TabIndex.operation_act_image_show = tab_index
		elseif v.activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_RANK_2 then
			TabIndex.operation_act_recharge_rank = tab_index
		end

		table.insert( self.tab_index_name, v.active_name)
		self.index_activity_cfg[tab_index] = v.activity_type

		--红点收集(就算不需要红点也要占一个坑位)
		local tab = {}
		if nil ~= v.remind_name or "" ~= v.remind_name then
			table.insert( tab, v.remind_name)
		end
		table.insert( self.remind_tab, tab)
	end
end

function OperationActivityWGData:GetOperationViewInfo()
	return self.tab_index_name or {}, self.remind_tab or {}
end

--根据活动号读取活动名字
function OperationActivityWGData:GetActivityNameByActivityNum(activity_type)
	if nil == activity_type then
		return
	end

	local cfg = ActivityWGData.Instance:GetCanLookActivityInfo(activity_type)
	if cfg then
		return cfg.active_name
	end
end

--获取活动是否开启
function OperationActivityWGData:GetActivityState(activity_type)
	return self.can_activity_open[activity_type]
end

--获取活动的结束时间
function OperationActivityWGData:GetActivityInValidTime(activity_type)
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(activity_type)
	if activity_data ~= nil then
		return activity_data.end_time, activity_data.start_time
	end
	return 0, 0
end


function OperationActivityWGData:IsActivityLastDay(tab_index)
	local end_time = self:GetActivityInValidTime(tab_index)
	local end_date = os.date("*t", end_time)
	local cur_date = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	if cur_date.month == end_date.month and cur_date.day == end_date.day then
		return true
	end
	return false
end

--获取第一个开启的功能(如果有红点红点优先)
function OperationActivityWGData:GetOneOpenTabIndex()
	local activity_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_config_auto").operation_activity_dec
	table.sort(activity_cfg, SortTools.KeyLowerSorter("rank_id"))

	local first_index = 0
	for k,v in ipairs(activity_cfg) do
		if self:GetActivityState(v.activity_type) then
			if 0 == first_index then
				first_index = v.rank_id * 10
			end

			if self.remind_callback[v.activity_type] and 1 == self.remind_callback[v.activity_type]() then
				return v.rank_id * 10
			end	
		end
	end

	return first_index
end

--收集各个活动的开启条件(event_type,是个表，考虑到多种条件，但是NONE一定不能与其他条件同时出现)
function OperationActivityWGData:SetActivityOpenCallBack(activity, event_type, call_back, remind_callback)
	self.rember_activity_event_type[activity] = event_type
	self.remind_callback[activity] = remind_callback

	--类型0不需要做记录
	for k,v in pairs(event_type) do
		if v == OPERATION_EVENT_TYPE.NONE then
			return
		end
	end

	for k,v in pairs(event_type) do
		if nil == self.activity_open_callback[v]then
			self.activity_open_callback[v] = {}
		end

		self.activity_open_callback[v][activity] = call_back
	end
end

--人物角色监听
function OperationActivityWGData:RoleLevelChange()
	local alearday_activity_open = self.alearday_activity_open
	local type = OPERATION_EVENT_TYPE.LEVEL --监听类型

	if alearday_activity_open and self.can_activity_open then
		for k,v in pairs(alearday_activity_open) do
			--活动已经开启了还在监听移除掉
			if self.can_activity_open[k] then
				self.alearday_activity_open[k] = nil

			--属于等级监听并且开启记录活动状态开启
			elseif self.activity_open_callback[type] and self.activity_open_callback[type][k] and self.activity_open_callback[type][k]()then
				self.alearday_activity_open[k] = nil
				self.can_activity_open[k] = true
				ActivityWGData.Instance:CreakMainViewActivityBtn()
				self:SetRemindHint(k)
			end
		end
	end
end

--背包物品发生改变
function OperationActivityWGData:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if not self.need_monitor_item[change_item_id] then
		return
	end

	local alearday_activity_open = self.alearday_activity_open
	local type = OPERATION_EVENT_TYPE.ITEM --监听类型

	if alearday_activity_open and self.can_activity_open then
		for k,v in pairs(alearday_activity_open) do
			--活动已经开启了还在监听移除掉
			if self.can_activity_open[k] then
				self.alearday_activity_open[k] = nil

			--属于等级监听并且开启记录活动状态开启
			elseif self.activity_open_callback[type] and self.activity_open_callback[type][k] and self.activity_open_callback[type][k]() then
				self.alearday_activity_open[k] = nil
				self.can_activity_open[k] = true
				ActivityWGData.Instance:CreakMainViewActivityBtn()
				self:SetRemindHint(k)
			end
		end
	end
end

--设置已经开启的运营活动并且需要做监听(只是服务器的一个状态不包含客户端的判断)
function OperationActivityWGData:SetAleardayOpenActivity(activity, status)
	GlobalEventSystem:Fire(OPERATION_ACTIVITY.ACT_STATE_CHANGE, activity, status)
	--服务器开启客户端还未开，记录做监听
	if status == ACTIVITY_STATUS.OPEN and not self.can_activity_open[activity] then
		--这一步是为了防止活动已经开了但还没有做记录
		local event_type_list = self.rember_activity_event_type[activity]
		local event_type = event_type_list and event_type_list[1] --取一个回调就可以
		if event_type and self.activity_open_callback[event_type] and self.activity_open_callback[event_type][activity]
			and self.activity_open_callback[event_type][activity]() then
			self.can_activity_open[activity] = true
			self:SetRemindHint(activity)
			return
		--类型0只受服务的控制
		elseif event_type and event_type == OPERATION_EVENT_TYPE.NONE then
			self.can_activity_open[activity] = true
			self:SetRemindHint(activity)
			return
		end
		self.alearday_activity_open[activity] = true

	--两端都开的是时候记录活动真实的状态
	elseif status == ACTIVITY_STATUS.OPEN and self.can_activity_open[activity] then
			self.can_activity_open[activity] = true
			self:SetRemindHint(activity)

	--不管那种情况只要活动关闭那就直接关闭
	elseif status == ACTIVITY_STATUS.CLOSE then
		self.alearday_activity_open[activity] = nil
		self.can_activity_open[activity] = nil
		OperationActivityWGCtrl.Instance:ChangeSelectIndex()	
		self:SetActivityCloseRemindHint(activity)
	end
end

--获取已经开启的活动列表
function OperationActivityWGData:GetOpenActivityList()
	return self.can_activity_open
end

--获取当前索引对应的活动号
function OperationActivityWGData:GetCurSelectActivityType(index)
	if nil == index then
		return 0
	end
	return self.index_activity_cfg[index] or 0
end

--当功能开启的时候需要触发一次红点（断线时有时活动协议下发比活动开启协议还要早）
function OperationActivityWGData:SetRemindHint(activity_type)
	if nil == activity_type then
		return
	end

	local cfg = ActivityWGData.Instance:GetCanLookActivityInfo(activity_type)
	if cfg and cfg.remind_name and "" ~= cfg.remind_name then
		RemindManager.Instance:Fire(cfg.remind_name)
	end
end

--当功能关闭的的时候需要触发一次红点用来关闭主界面的红底那
function OperationActivityWGData:SetActivityCloseRemindHint(activity_type)
	if nil == activity_type then
		return
	end

	local cfg = ActivityWGData.Instance:GetCanLookActivityInfo(activity_type)
	if cfg and cfg.remind_name and "" ~= cfg.remind_name then
		if cfg.btn_type == OPERATION_ACTIVITY_BTN_TYPE.ONE then
			self:BtnOneRemindChangeCallBack(cfg.remind_name, 0)
		elseif cfg.btn_type == OPERATION_ACTIVITY_BTN_TYPE.TWO then
			self:BtnTwoRemindChangeCallBack(cfg.remind_name, 0)
		end
	end
end


------------------------- 各活动红点数据-------------------------

--主界面红点提示按钮1
function OperationActivityWGData:BtnOneRemindChangeCallBack(remind_name, num)
	if self.btn_one_remind[remind_name] then
		self.btn_one_remind[remind_name] = num
	end

	RemindManager.Instance:Fire(RemindName.OperationActivity)
end

--主界面红点提示按钮2
function OperationActivityWGData:BtnTwoRemindChangeCallBack(remind_name, num)
	if self.btn_two_remind[remind_name] then
		self.btn_two_remind[remind_name] = num
	end

	RemindManager.Instance:Fire(RemindName.OperationActivity_Two)
end

--主界面红点提示按钮1
function OperationActivityWGData:IsShowMainViewRedPoint()
	for k,v in pairs(self.btn_one_remind) do
		if v > 0 then
			return 1
		end
	end

	return 0
end

--主界面红点提示按钮2
function OperationActivityWGData:IsShowMainViewTwoRedPoint()
	for k,v in pairs(self.btn_two_remind) do
		if v > 0 then
			return 1
		end
	end
	 --风筝夺宝
	if ShowRedPoint.SHOW_RED_POINT == FZGetRewardWGData.Instance:ShowRemind() then
		return 1
	end

	return 0
end


--活动开启开服天数
function OperationActivityWGData:GetOpenDayByAct(act_type)
	local day = 0
	if act_type == nil then
		return day
	end
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if act_info == nil then
		return day
	end

	if act_info.status == ACTIVITY_STATUS.CLOSE then
		return day
	end

	local day_value = 60 * 60 * 24
	local open_start_time = TimeWGCtrl.Instance:GetServerRealStartTime()
	local time_tab = os.date("*t", open_start_time)
	local open_time = open_start_time - time_tab.hour * 3600 - time_tab.min * 60 - time_tab.sec
	day = math.floor((act_info.start_time - open_time) / day_value) + 1
	return day
end

--活动开启时间
function OperationActivityWGData:GetOpenTimeByAct(act_type)
	local time = 0
	if act_type == nil then
		return time
	end
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if act_info == nil then
		return time
	end

	if act_info.status == ACTIVITY_STATUS.CLOSE then
		return time
	end
    time = act_info.start_time
	return time
end

--活动已经开启的时间
function OperationActivityWGData:GetActOpenDay(act_type)
	local day = 0
	if act_type == nil then
		return day
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if act_info == nil then
		return day
	end

	if act_info.status == ACTIVITY_STATUS.CLOSE then
		return day
	end

	local day_value = 60 * 60 * 24
	local time_tab = os.date("*t", act_info.start_time)
	local open_time = act_info.start_time - time_tab.hour * 3600 - time_tab.min * 60 - time_tab.sec
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	day = math.floor((server_time - open_time) / day_value) + 1

	return day
end

--活动可以开启的天数（向上取整，天）
function OperationActivityWGData:GetActCanOpenDay(act_type)
	local day = 0
	if act_type == nil then
		return day
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if act_info == nil then
		return day
	end

	if act_info.status == ACTIVITY_STATUS.CLOSE then
		return day
	end

	local day_value = 60 * 60 * 24
	local open_time_tab = os.date("*t", act_info.start_time)
	local open_time = act_info.start_time - open_time_tab.hour * 3600 - open_time_tab.min * 60 - open_time_tab.sec
	local end_time_tab = os.date("*t", act_info.end_time)
	local end_time = act_info.end_time - end_time_tab.hour * 3600 - end_time_tab.min * 60 - end_time_tab.sec

	day = math.ceil((end_time - open_time) / day_value) + 1
	return day
end

--主界面加载完堆当前收集的开启信息做开启判断
function OperationActivityWGData:OnLoadingComplete(event_type)
	local alearday_activity_open = self.alearday_activity_open

	if alearday_activity_open and self.can_activity_open then
		for k,v in pairs(alearday_activity_open) do
			--活动已经开启了还在监听移除掉
			if self.can_activity_open[k] then
				self.alearday_activity_open[k] = nil
			--属于等级监听并且开启记录活动状态开启
			else
				local is_can_open = self:GetCurActivityOpenEvent(event_type, k)
				if is_can_open then
					self.alearday_activity_open[k] = nil
					self.can_activity_open[k] = true
					ActivityWGData.Instance:CreakMainViewActivityBtn()
					self:SetRemindHint(k)
				end
			end
		end
	end
end

--根据监听事件来判断当前活动是否开启
function OperationActivityWGData:GetCurActivityOpenEvent(event_type, activity_type)
	if self.activity_open_callback[event_type] and self.activity_open_callback[event_type][activity_type] and self.activity_open_callback[event_type][activity_type]() then
		return true
	end

	return false
end

--过滤传闻，返回活动类型和str
function OperationActivityWGData:FilterChuanWen(protocol)
	local content = protocol.content
	if protocol.content == nil then
		return nil, nil
	end

	local i, j = 0, 0
	local last_pos = 1
	local act_type
	local str
	for loop_count = 1, 100 do
		i, j = string.find(content, "({.-})", j + 1)-- 匹配规则{face;20} {item;26000}
		if nil == i or nil == j then
			break
		else
			last_pos = j + 1
			str = string.sub(content, i, j)
			if not act_type then
				str = string.sub(str,2,-2)
				str = Split(str, ";")
				if str[1] == "activity" then
					act_type = tonumber(str[2])
					return act_type, protocol.content
				end
			end
		end
	end

	return nil, nil
end

--自己活动中需要被监听的物品列表（当做功能开启依据的物品）
function OperationActivityWGData:SetNeedBeMonitorItemList(item_list)
	if not item_list then
		return
	end

	for k,v in pairs(item_list) do
		self.need_monitor_item[v.item_id] = true
	end
end

--记录当前打开的界面类型
function OperationActivityWGData:RemberCurOpenViewType(_type)
	self.rember_cur_view_type = _type
end

function OperationActivityWGData:GetCurOpenViewType()
	-- return self.rember_cur_view_type or 1
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	open_day = open_day - 1 -- 运营活动算的开启天数没算上今天所以减1
	local cfg_list = ConfigManager.Instance:GetAutoConfig("operation_activity_config_auto").main_btn_effect
	for i=1,#cfg_list do
		if open_day >= cfg_list[i].start_server_day and open_day < cfg_list[i].end_server_day then
			return cfg_list[i].theme_type
		end
	end
	return 1
end

function OperationActivityWGData:GetCurThemeType()
	local cfg_list = ConfigManager.Instance:GetAutoConfig("operation_activity_config_auto").main_btn_effect
	local open_type = self:GetCurOpenViewType()
	local theme_cfg = cfg_list[open_type]
	local cur_zhuti = theme_cfg and theme_cfg.theme_type or 1
	return cur_zhuti, theme_cfg
end

function OperationActivityWGData:GetCurThemeColor()
	local cur_zhuti = self:GetCurThemeType()
	if cur_zhuti == 2 then
		return COLOR3B.WHITE
	else
		return COLOR3B.DEFAULT
	end
end

-- 获取当前主题图片资源名字
function OperationActivityWGData:GetCurTypeImgName(name_str)
	--local cur_zhuti = self:GetCurThemeType()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local week = TimeUtil.FormatSecond3MYHM1(server_time)
	local cur_zhuti = 2
	if week == 2 or week == 3 then
		cur_zhuti = 1
	end
	local img_name = name_str .. "_" .. cur_zhuti
	return img_name
end

-- 获取当前主题id
function OperationActivityWGData:GetCurTypeId()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local week = TimeUtil.FormatSecond3MYHM1(server_time)
	local cur_zhuti = 2
	if week == 2 or week == 3 then
		cur_zhuti = 1
	end

	return cur_zhuti
end

--对监听的红点进行分类监听
function OperationActivityWGData:SetRemindEventList()
	local activity_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_config_auto").operation_activity_dec
	table.sort(activity_cfg, SortTools.KeyLowerSorter("rank_id"))

	for k,v in pairs(activity_cfg) do
		if v.btn_type == OPERATION_ACTIVITY_BTN_TYPE.ONE and v.remind_name and "" ~= v.remind_name then
			self.btn_one_remind[v.remind_name] = 0
		elseif v.btn_type == OPERATION_ACTIVITY_BTN_TYPE.TWO and v.remind_name and "" ~= v.remind_name then
			self.btn_two_remind[v.remind_name] = 0
		end
	end

	for k,v in pairs(self.btn_one_remind) do
		RemindManager.Instance:Bind(self.btn_one_remind_change, k)
	end

	for k,v in pairs(self.btn_two_remind) do
		RemindManager.Instance:Bind(self.btn_two_remind_change, k)
	end
end

function OperationActivityWGData:DayPassChange()
	local alearday_activity_open = self.alearday_activity_open
	local type = OPERATION_EVENT_TYPE.DAY --监听类型
	local is_reset = false

	if alearday_activity_open and self.can_activity_open then
		for k,v in pairs(alearday_activity_open) do

			--活动已经开启了如果切天了，重新检测是否开启
			if self.can_activity_open[k] then
				if self.activity_open_callback[type] and self.activity_open_callback[type][k] and not self.activity_open_callback[type][k]() then
					self.can_activity_open[k] = nil
					self:SetActivityCloseRemindHint(k)
					is_reset = true
				else
					self.alearday_activity_open[k] = nil
				end
			elseif self.activity_open_callback[type] and self.activity_open_callback[type][k] and self.activity_open_callback[type][k]()then
				self.alearday_activity_open[k] = nil
				self.can_activity_open[k] = true
				ActivityWGData.Instance:CreakMainViewActivityBtn()
				self:SetRemindHint(k)
			end
		end
	end

	local tab = {}
	if self.can_activity_open then
		for k,v in pairs(self.can_activity_open) do
			if self.activity_open_callback[type] and self.activity_open_callback[type][k] and not self.activity_open_callback[type][k]() then
				table.insert(tab, k)
			end
		end
	end

	if self.can_activity_open ~= nil and self.alearday_activity_open ~= nil then
		for k,v in pairs(tab) do
			self.can_activity_open[v] = nil
			self.alearday_activity_open[v] = true
			is_reset = true
		end
	end

	if is_reset then
		OperationActivityWGCtrl.Instance:ChangeSelectIndex()
		ActivityWGData.Instance:CreakMainViewActivityBtn()
	end
end

------------------------------------------------------------------------

-- 根据物品的品质排序,同品质的按照配置顺序排
function OperationActivityWGData:SortDataByItemColor(item_list, not_up)
	local data_list = {}
	if IsEmptyTable(item_list) then
		return data_list
	end
	
	local item_cfg = nil
	local big_type = nil
	local is_show_zhengui = false
	local zhengui_effect_flag = 0
	local color_flag = 1
	local star_level = 0

	for k,v in pairs(item_list) do
		item_cfg,big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg then
			is_show_zhengui = ItemWGData.Instance:CheckIsShowZhenGuiEffect(item_cfg.zhengui_effect or 1)
			zhengui_effect_flag = is_show_zhengui and 100000 or 0
			color_flag = item_cfg.color or 1

			star_level = v.star_level or 0
			if v.param and v.param.star_level then
				star_level = v.param.star_level
			end

			data_list[zhengui_effect_flag + color_flag * 10000 + star_level * 1000 - k] = v
		end
	end

	if not_up then
		return SortTableKey(data_list)
	else
		return SortTableKey(data_list, true)
	end
end

function OperationActivityWGData:GetWeekLoopOpenCfgByActid(act_id)
	local tmp_cfg = ConfigManager.Instance:GetAutoConfig("randactivityopencfg_auto")
	local open_limit_day = -1
	if not IsEmptyTable(tmp_cfg) and not IsEmptyTable(tmp_cfg.week_loop_open_cfg) then
		for k, v in pairs(tmp_cfg.week_loop_open_cfg) do
			if v.activity_type == act_id then
				open_limit_day = v.open_day_limit or open_limit_day
				break
			end
		end
	end
	return open_limit_day
end