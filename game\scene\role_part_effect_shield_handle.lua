RolePartEffectShieldHandle = RolePartEffectShieldHandle or BaseClass(ReuseableShieldHandle)

function RolePartEffectShieldHandle:__init(role, shield_obj_type, part)
	self:Init(role, shield_obj_type, part)
end

function RolePartEffectShieldHandle:__delete()
	self.role = nil
end

function RolePartEffectShieldHandle:Init(role, shield_obj_type, part)
	self.role = role
	self.shield_obj_type = shield_obj_type
	self.part = part
end

function RolePartEffectShieldHandle:Clear()
	self.role = nil
end

function RolePartEffectShieldHandle:VisibleChanged(visible)
	if self.role and not self.role:IsDeleted() then
		self.role:PartEffectVisibleChanged(self.part, visible)
	end
end