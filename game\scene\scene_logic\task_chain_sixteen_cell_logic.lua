TaskChainSixteenCellLogic = TaskChainSixteenCellLogic or BaseClass(CommonFbLogic)
function TaskChainSixteenCellLogic:__init()
end

function TaskChainSixteenCellLogic:__delete()
end

function TaskChainSixteenCellLogic:Enter(old_scene_type, new_scene_type)
	-- 该玩法重要特效，进入场景预先加载
	ResPoolMgr:GetPrefab("effects2/prefab/misc/effect_kunlun_hl_prefab", "effect_kunlun_hl", function ()
		-- body
	end, false)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetSkillShowState(false)
		MainuiWGCtrl.Instance:SetButtonModeClick(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		ViewManager.Instance:Open(GuideModuleName.OperationTaskChainFbInfoView)
	end)

	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainScheduleView)
end

function TaskChainSixteenCellLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	
	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)

	MainuiWGCtrl.Instance:SetSkillShowState(true)
	MainuiWGCtrl.Instance:SetButtonModeClick(true)
	UiInstanceMgr.Instance:ColseFBStartDown()
	OperationTaskChainWGCtrl.Instance:ResetInfoView()
	OperationTaskChainWGData.Instance:ResetSixTeenCellInfo()
	
	FuBenPanelCountDown.Instance:CloseViewHandler()

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		main_role:SetUnderShowInfo(nil)
	end
end

function TaskChainSixteenCellLogic:IsEnemy(target_obj, main_role, ignore_table, test)
	return false
end

function TaskChainSixteenCellLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function TaskChainSixteenCellLogic:GetGuajiCharacter()
	local is_need_stop = true
	return nil, nil, is_need_stop
end

-- 怪物是否是敌人
function TaskChainSixteenCellLogic:IsMonsterEnemy(target_obj, main_role)
	return false
end

function TaskChainSixteenCellLogic:GetGuajiPos()
end

function TaskChainSixteenCellLogic:CanGetMoveObj()
	return false
end