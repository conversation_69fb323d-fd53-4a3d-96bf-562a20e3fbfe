-----------------------------------------
--赏金任务-昆仑试炼
-----------------------------------------
FuBenBountyKunLunLogic = FuBenBountyKunLunLogic or BaseClass(CommonFbLogic)

function FuBenBountyKunLunLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function FuBenBountyKunLunLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function FuBenBountyKunLunLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenPanelWGCtrl.Instance:OpenBountyTaskView()
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

end

function FuBenBountyKunLunLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	local monster_id = 0
	local monster_list = Scene.Instance:GetMonsterList()
	for k, v in pairs(monster_list) do
		if v:GetVo().monster_id == 11258 then
			monster_id = v:GetVo().monster_id
			break
		end
	end
	FuBenPanelWGCtrl.Instance:VistWordImg(monster_id)
	BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
end

function FuBenBountyKunLunLogic:Out()
	CommonFbLogic.Out(self)
	FuBenPanelWGCtrl.Instance:VistWordImg(0)	
	FuBenPanelWGCtrl.Instance:CloseBountyTaskView()
    GuajiWGCtrl.Instance:StopGuaji()
end