
require("game/everything_under_the_sun/everything_under_the_sun_view")
require("game/everything_under_the_sun/everything_under_the_sun_wg_data")

EverythingUnderTheSunWGCtrl = EverythingUnderTheSunWGCtrl or BaseClass(BaseWGCtrl)
function EverythingUnderTheSunWGCtrl:__init()
	if EverythingUnderTheSunWGCtrl.Instance then
		error("[EverythingUnderTheSunWGCtrl]:Attempt to create singleton twice!")
	end

    EverythingUnderTheSunWGCtrl.Instance = self

    self.data = EverythingUnderTheSunWGData.New()
    self.view = EverythingUnderTheSunView.New(GuideModuleName.EverythingUnderTheSun)

    self.enter_open_flag = false
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
	self.open_view_fun = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.<PERSON><PERSON>iewO<PERSON>, self))
	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))

end

function EverythingUnderTheSunWGCtrl:__delete()
    if self.open_view_fun then
		GlobalEventSystem:UnBind(self.open_view_fun)
		self.open_view_fun = nil
	end

	if self.open_fun_change then
		GlobalEventSystem:UnBind(self.open_fun_change)
		self.open_fun_change = nil
	end

    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    EverythingUnderTheSunWGCtrl.Instance = nil
end

function EverythingUnderTheSunWGCtrl:OnDayChange()
	if not self:CheckNeedCloseFun() then
		self.data:ResetRecordList()
	end
end

function EverythingUnderTheSunWGCtrl:CheckViewOpen()
	if not self.enter_open_flag then
		self.enter_open_flag = true
		if self:CheckNeedCloseFun() then
			return
		end

        local is_open = FunOpen.Instance:GetFunIsOpened(FunName.EverythingUnderTheSun)
		if is_open then
			if self.view and not self.view:IsOpen() then
				self.view:Open()
			end
		end
	end
end

function EverythingUnderTheSunWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.EverythingUnderTheSun then
		self:CheckNeedCloseFun()
    end
end

function EverythingUnderTheSunWGCtrl:CheckNeedCloseFun()
	local max_open_day = self.data:GetMaxShowDay()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.EverythingUnderTheSun)
	if open_day > max_open_day and is_open then
		FunOpen.Instance:ForceCloseFunByName(FunName.EverythingUnderTheSun, true)
		self:CloseView()
		return true
	end

	return false
end

function EverythingUnderTheSunWGCtrl:CloseView()
	if self.view:IsOpen() then
		self.view:Close()
	end
end