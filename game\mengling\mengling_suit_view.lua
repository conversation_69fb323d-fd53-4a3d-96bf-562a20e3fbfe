
MengLingSuitView = MengLingSuitView or BaseClass(SafeBaseView)

function MengLingSuitView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/mengling_ui_prefab", "layout_mengling_suit_attr")
end

function MengLingSuitView:SetDataAndOpen(seq)
    self.select_seq = seq

    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function MengLingSuitView:LoadCallBack()
    if not self.mengling_suit_list then
        self.mengling_suit_list = AsyncListView.New(MengLingSuitItemRender, self.node_list.mengling_suit_list)
        self.mengling_suit_list:SetIsDelayFlush(false)
    end

    for i = 1, 2 do
        self.node_list["suit_tog" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSuitTogHandler, self, i))

        local suit_cfg = MengLingWGData.Instance:GetMengLingSuitCfgBySuit(i)
        local suit_name_str = string.format(Language.MengLing.MengLingSuitNameDesc, suit_cfg.suit_name)
        self.node_list["suit_tog_text" .. i].text.text = suit_name_str
        self.node_list["suit_tog_text_hl" .. i].text.text = suit_name_str
    end

    self.select_suit_index = 1
    self.node_list["suit_tog1"].toggle.isOn = true
end

function MengLingSuitView:ReleaseCallBack()
    if self.mengling_suit_list then
        self.mengling_suit_list:DeleteMe()
        self.mengling_suit_list = nil
    end

    self.select_seq = nil
    self.select_suit_index = nil
end

function MengLingSuitView:OnFlush()
    local suit_data_list = MengLingWGData.Instance:GetSuitPreviewSeqList(self.select_seq)
    local target_data_list = suit_data_list[self.select_suit_index]

    local data_list = {}

    if not IsEmptyTable(target_data_list) then
        for k, v in pairs(target_data_list) do
           table.insert(data_list, v)
        end
    end

    self.mengling_suit_list:SetDataList(data_list)
end

function MengLingSuitView:OnClickSuitTogHandler(index, isOn)
    if isOn and self.select_suit_index ~= index then
        self.select_suit_index = index
        self:Flush()
    end
end

---------------------------------------------MengLingSuitItemRender--------------------------------------------------
MengLingSuitItemRender = MengLingSuitItemRender or BaseClass(BaseRender)

function MengLingSuitItemRender:LoadCallBack()
    if not self.suit_list then
        self.suit_list = AsyncListView.New(MengLingSuitItemAttrRender, self.node_list.suit_list)
        self.suit_list:SetStartZeroIndex(false)
        self.suit_list:SetIsDelayFlush(false)
    end
end

function MengLingSuitItemRender:__delete()
    if self.suit_list then
        self.suit_list:DeleteMe()
        self.suit_list = nil
    end
end

function MengLingSuitItemRender:OnFlush()
    if not self.data then
        return
    end

    local color = (self.data[1] or {}).color or GameEnum.ITEM_COLOR_WHITE
    local seq = (self.data[1] or {}).seq or 0

    if color > GameEnum.ITEM_COLOR_WHITE then
        -- self.node_list.name.text.text = Language.Common.ColorName[color]
        self.node_list.name.text.text = string.format(Language.MengLing.MengLingSuitActiveName, (seq + 1), Language.Common.ColorName4[color])

        local bundle, asset = ResPath.GetNoPackPNG("a3_lz_tcxbdi" .. color)
        self.node_list.bg.image:LoadSprite(bundle, asset, function ()
            -- self.node_list.bg.image:SetNativeSize()
        end)
    end

    self.suit_list:SetDataList(self.data)
end

----------------------------MengLingSuitItemAttrRender-----------------------------
MengLingSuitItemAttrRender = MengLingSuitItemAttrRender or BaseClass(BaseRender)

function MengLingSuitItemAttrRender:LoadCallBack()
    if not self.attr_list then
        self.attr_list = AsyncListView.New(MengLingSuitItemAttrCellRender, self.node_list.attr_list)
        self.attr_list:SetStartZeroIndex(false)
        self.attr_list:SetIsDelayFlush(false)
    end
end

function MengLingSuitItemAttrRender:__delete()
    if self.attr_list then
        self.attr_list:DeleteMe()
        self.attr_list = nil
    end
end

function MengLingSuitItemAttrRender:OnFlush()
    if not self.data then
        return
    end

    local is_unlock = MengLingWGData.Instance:IsMengLingSuitUnLock(self.data.seq, self.data.suit ,self.data.color, self.data.need_num)
    self.node_list.nor_arrow:CustomSetActive(not is_unlock)
    self.node_list.hl_arrow:CustomSetActive(is_unlock)
    XUI.SetGraphicGrey(self.node_list.mengling_suit_cell_item, not is_unlock)
    local color = is_unlock and COLOR3B.C2 or COLOR3B.C24
    local name_str = string.format(Language.MengLing.SuitNumCompany, self.data.need_num)
    self.node_list.nor_name.text.text = name_str
    self.node_list.hl_name.text.text = name_str
    local data_list = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(self.data, "attr_id", "attr_value")

    for k, v in pairs(data_list) do
        v.attr_name = ToColorStr(v.attr_name, color)
        v.value_str = ToColorStr("+" .. v.value_str, color)
    end

    self.attr_list:SetDataList(data_list)
    self.node_list.active_bg:CustomSetActive(is_unlock)
end

----------------------------MengLingSuitItemAttrCellRender-----------------------------
MengLingSuitItemAttrCellRender = MengLingSuitItemAttrCellRender or BaseClass(BaseRender)

function MengLingSuitItemAttrCellRender:OnFlush()
    if not self.data then
        return
    end 

    self.node_list.attr_name.text.text = self.data.attr_name
    self.node_list.attr_value.text.text = self.data.value_str
end