TianShen3v3SeasonRankView = TianShen3v3SeasonRankView or BaseClass(SafeBaseView)
-- 天神3v3赛季排名面板
function TianShen3v3SeasonRankView:__init()
    self:SetMaskBg()
	self:LoadConfig()
    self.default_index = TabIndex.kf_pvp_zhandui_rank_cur
end

function TianShen3v3SeasonRankView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "VerticalTabbar")
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_season_rank")
	self.is_modal = false
end

function TianShen3v3SeasonRankView:ReleaseCallBack()
    if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
    if self.my_rank then
		self.my_rank:DeleteMe()
		self.my_rank = nil
	end
	if self.cur_rank_list then
		self.cur_rank_list:DeleteMe()
		self.cur_rank_list = nil
	end
end

function TianShen3v3SeasonRankView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.TianShen3v3.SeasonRank
	self:SetSecondView(nil, self.node_list["size"])
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.TianShen3v3.SeasonRankTabGroup, nil, "uis/view/tianshen_3v3_ui_prefab")
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:SetInstanceParent(self.node_list.TabbarContainer)
		self.tabbar:SetLocalPosition(0, -8, 0)
	end
	self.cur_rank_list = AsyncListView.New(TianShen3v3SeasonRankItem, self.node_list["cur_rank_list"])
	self.my_rank = TianShen3v3SeasonRankItem.New(self.node_list["my_rank_item"])
end

function TianShen3v3SeasonRankView:OpenCallBack()
	TianShen3v3WGCtrl.Instance:SendRequestSeasonRank()
	TianShen3v3WGCtrl.Instance:SendRequestSelfInfo()
end

function TianShen3v3SeasonRankView:ShowIndexCallBack(index)
	self:Flush(index)
end


function TianShen3v3SeasonRankView:OnFlush()
	local index = self:GetShowIndex()
	local data_list = {}
	if index == TabIndex.tianshen_3v3_season_rank then
		data_list = TianShen3v3WGData.Instance:GetSeasonRankInfoList()
		self.cur_rank_list:SetDataList(data_list)
	end

	self.node_list["empty_tips"]:SetActive(#data_list <= 0)

	-- 我的排名信息
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local info = {}
	info.rank = TianShen3v3WGData.Instance:GetMySeasonRank()
	info.role_name = main_role_vo.name
	info.server_id = RoleWGData.Instance:GetMergeServerId()
	info.score = TianShen3v3WGData.Instance:GetSeasonScore()
	info.capability = RoleWGData.Instance:GetMainRoleCap()
	self.my_rank:SetIndex(1)
	self.my_rank:SetData(info)

	
	 -- 领取奖励所需参加次数
	local need_join_count = TianShen3v3WGData.Instance:GetOtherCfg().enter_list_need_join_times
	local join_time = TianShen3v3WGData.Instance:GetJoinTimes()
	local color = join_time > 0 and COLOR3B.RED or COLOR3B.GREEN
	local str = ToColorStr(join_time .. "/" .. need_join_count, color)
	self.node_list["rank_condition"].text.text = string.format(Language.TianShen3v3.RankNeedJoinCondition, str)
end


TianShen3v3SeasonRankItem = TianShen3v3SeasonRankItem or BaseClass(BaseRender)
function TianShen3v3SeasonRankItem:__init()

end

function TianShen3v3SeasonRankItem:OnFlush()
	-- 背景
	self.node_list["bg"]:SetActive(self.index % 2 == 1)

	-- 玩家名称
	self.node_list["role_name"].text.text = self.data.role_name

	-- 排名
	if self.data.rank <= 0 then
		self.node_list["rank_img"]:SetActive(false)
		self.node_list["rank"]:SetActive(true)
		self.node_list["rank"].text.text = Language.TianShen3v3.NotOnList
	else
		self.node_list["rank_img"]:SetActive(self.data.rank <= 3)
		self.node_list["rank"]:SetActive(self.data.rank > 3)
		if self.data.rank <= 3 then
			self.node_list["rank_img"].image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.data.rank))
		end
		self.node_list["rank"].text.text = self.data.rank
	end

	-- 区服名称
    local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id)		--内网无法请求PHP，先设置默认名字
    self.node_list["server_name"].text.text = temp_name
	-- 积分
	self.node_list["sorce"].text.text = self.data.score

	-- 段位名称
	local grade_cfg, next_grade_cfg = TianShen3v3WGData.Instance:GetGradeCfgByScore(self.data.score)
	self.node_list["duanwei"].text.text = grade_cfg.grade_name
	ChangeToQualityText(self.node_list["duanwei"].text, RankGradeEnum[grade_cfg.grade])

	-- 战力
	self.node_list["capability"].text.text = self.data.capability
end

