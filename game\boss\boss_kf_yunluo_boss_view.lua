function WorldServerView:DeleteShenYunBossView()
	if self.sylayer_btn_list ~= nil then
		self.sylayer_btn_list:DeleteMe()
		self.sylayer_btn_list = nil
	end
	if nil ~= self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	if self.sy_boss_list ~= nil then
		self.sy_boss_list:DeleteMe()
		self.sy_boss_list = nil
	end

	if self.sy_cell_list ~= nil then
		for k, v in pairs(self.sy_cell_list) do
			v:DeleteMe()
		end
	end
	self.sy_cell_list = {}

	if self.syboss_info_list ~= nil then
		self.syboss_info_list:DeleteMe()
		self.syboss_info_list = nil
	end

	self.sy_boss_anim = nil
	self.layout_syrare_fall_item = nil
	self.layout_syboss_info = nil


	if self.common_kfcountdown_timer then
		GlobalTimerQuest:CancelQuest(self.common_kfcountdown_timer)
		self.common_kfcountdown_timer = nil
	end

	if CountDownManager.Instance:HasCountDown("syboss_fushtime") then
		CountDownManager.Instance:RemoveCountDown("syboss_fushtime")
	end
	self.kf_yunluo_boss_select_data = nil
end

function WorldServerView:InitKfShenYunView()
	self.kf_yunluo_boss_select_data = nil
	-- if self.layout_syboss_info ~= nil then return end
	self.layout_syboss_info = self.node_list.layout_syboss_info
	self.layout_syrare_fall_item = self.node_list.layout_syrare_fall_item

	self.layout_syboss_info:SetActive(true)
	self.cur_sylayer = 1
	self.cur_kfboss_id = 1
	XUI.AddClickEventListener(self.node_list.btn_sy_info, BindTool.Bind1(self.BrowseSYKillRecord, self))     -- 击杀记录
	XUI.AddClickEventListener(self.node_list.btn_syboss_fall, BindTool.Bind1(self.BrowsesyRareFallRecord, self)) -- 珍稀掉落
	XUI.AddClickEventListener(self.node_list.btn_sygoto_sykill, BindTool.Bind1(self.GoToKillsyBoss, self))         -- 前往击杀
	XUI.AddClickEventListener(self.node_list.btn_syboss_info, BindTool.Bind1(self.BrowsesyBossInfo, self))       -- boss信息
	XUI.AddClickEventListener(self.node_list.btn_play_kfdes_sy, BindTool.Bind1(self.BrowseSYPlayInfo, self))
	self:CreateLayerShenYunBtnList()
	self:CreateShenYunBossList()
	self:CreateShenYunRareFallItem()
	self:CreateShenYunBossInfoList()
end

-- 层按钮
function WorldServerView:CreateLayerShenYunBtnList()
	if nil == self.sylayer_btn_list then
		-- self.sylayer_btn_list = AsyncListView.New(KfBossLayerBtnRender,self.node_list.ph_kfsybtn_list)
		-- self.sylayer_btn_list:SetSelectCallBack(BindTool.Bind1(self.SYBossLayerBtnSelectCallBack,self))
		-- local btn_list = BossWGData.Instance:GetBossLayerCfg(self.show_index)
		-- self.sylayer_btn_list:SetDataList(btn_list, 3)
		-- self.sylayer_btn_list:SetDataList(Language.Boss.BtnKfYLSub,3)
	end
end
--boss列表
function WorldServerView:CreateShenYunBossList()
	self.sy_boss_list = AsyncListView.New(SYBossItemRender,self.node_list.ph_sy_boss_list)
	self.sy_boss_list:SetSelectCallBack(BindTool.Bind1(self.SYBossLsitSelectCallBack,self))
	self.sylayer_btn_list:SelectIndex(1)
end

function WorldServerView:CreateShenYunRareFallItem()
	self.sy_cell_list = {}
	for i = 1, 8 do
		local cell = BossRewardCell.New(self.node_list["ph_kfsyrareitem_"..i])
		cell.root_node.rect.localScale = Vector3(0.8,0.8,0.8)
		 cell:SetItemTipFrom(ItemTip.FROM_SHENGYIN_NOT_USE)
		table.insert(self.sy_cell_list, cell)
	end
end

function WorldServerView:CreateShenYunBossInfoList()

end



function WorldServerView:OnFlushSYBossView()
	self:refreshSYBossList()
	self:OnFlushSYBossInfo()
	-----------------------------------------------------------------------------------
	self.node_list.layout_sy_sgenter:SetActive(self:GetShowIndex() == BossViewIndex.SYBOSS)
	self.node_list.text_sy_today_crystal:SetActive(self:GetShowIndex() == BossViewIndex.SYBOSS)
	local tire_value, max_tire_value = BossWGData.Instance:GetSgBossTire()
	local role_vip_level = RoleWGData.Instance.role_vo.vip_level
	local max_num = BossWGData.Instance:SetShenYunTicket(role_vip_level)
	local fb_num  = max_num - BossWGData.Instance:GetShenYinFbDayCount()
	local color = fb_num > 0 and COLOR3B.GREEN or COLOR3B.D_RED
	local num_str = ToColorStr(tostring(fb_num), color)
	self.node_list.text_sy_today_crystal.text.text = string.format(Language.Boss.SYTireValue, num_str)


end

function WorldServerView:refreshSYBossList()
	local list_data = BossWGData.Instance:GetShenYunBylayer(self.cur_sylayer)
	if next(list_data) == nil then return end

	self.sy_boss_list:SetDataList(list_data,0)
	--self.sy_boss_list:JumpToTop(true)
	self.node_list["ph_sy_boss_list"].scroller:ReloadData(0)
	--self.kf_boss_list:SelectIndex(1)
	self.sy_boss_list:SelectIndex(1)

end

function WorldServerView:OnFlushSYBossInfo()
	if self.layout_syboss_info == nil then return end
	self:refreshSYBossInfo()
	self:refreshsyRareFall()

	if CountDownManager.Instance:HasCountDown("syboss_fushtime") then
		CountDownManager.Instance:RemoveCountDown("syboss_fushtime")
	end

	local sy_bosstime = BossWGData.Instance:GetSYFulshTime()
	local sy_status = BossWGData.Instance:GetSYTiemStatus()
	local mul_time = sy_bosstime - TimeWGCtrl.Instance:GetServerTime()
	if sy_status == 0 then
		self.node_list.lbl_sy_sgenter_num.text.text = Language.Boss.TeTengXiaoShi
	else
		self.node_list.lbl_sy_sgenter_num.text.text = Language.Boss.TuTengShuaXin
	end

	if mul_time > 0 then
		self:UpdatesyBOSSDownTime(1, mul_time)
		CountDownManager.Instance:AddCountDown("syboss_fushtime", BindTool.Bind1(self.UpdatesyBOSSDownTime, self), BindTool.Bind1(self.CompletesyDownTime, self), nil, mul_time, 1)
	else
		self:CompletesyDownTime()
	end
end

function WorldServerView:UpdatesyBOSSDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		local sy_status = BossWGData.Instance:GetSYTiemStatus()
		if sy_status == 0 then
			self.node_list.lbl_sgenter_time.text.text = string.format(Language.Boss.DarkGreenColor,TimeUtil.FormatSecond(total_time - elapse_time, 3))
		else
			self.node_list.lbl_sgenter_time.text.text = string.format(Language.Boss.RedColor,TimeUtil.FormatSecond(total_time - elapse_time, 3))
		end
	end
end

function WorldServerView:CompletesyDownTime()
	BossWGCtrl.Instance:SendSYXuShiBossReq(GameEnum.SHENGYINFB_TYPE_GET_REFRESH_TIME)
end


function WorldServerView:refreshSYBossInfo()
	if self.syboss_info_list == nil then return end
	local list_index = self.sy_boss_list:GetSelectIndex()
	local boss_info = self.sy_boss_list:GetItemAt(list_index):GetData()
	if boss_info == nil then return end
	local info_list = {}
	if self.cur_sylayer == 1 then
		 info_list = {
			{"shengming", boss_info.shengming},
			{"gongji", boss_info.gongji},
			{"yaolichuantou", boss_info.yaolichuantou},
			{"yaolidikang", boss_info.yaolidikang},
		}
	elseif self.cur_sylayer == 2 then
		 info_list = {
			{"shengming", boss_info.shengming},
			{"gongji", boss_info.gongji},
			{"molichuantou", boss_info.molichuantou},
			{"molidikang", boss_info.molidikang},
		}
	elseif self.cur_sylayer == 3 then
		 info_list = {
			{"shengming", boss_info.shengming},
			{"gongji", boss_info.gongji},
			{"shenlichuantou", boss_info.shenlichuantou},
			{"shenlidikang", boss_info.shenlidikang},
		}
	end


	if self.node_list.layout_syboss_info:GetActive() then
		self.node_list.challenge_tips.text.text =Language.Boss.DressupTip
		self.node_list.rich_pick_crystal.text.text = Language.Boss.TiaoZhan
		if self.cur_sylayer == 1 then
			if  info_list[3][2] > 0 and info_list[4][2] > 0 then
				self.node_list.rich_have_crystal.text.text = string.format(Language.Boss.ChuanDai1, info_list[3][2], info_list[4][2])
			else
				self.node_list.rich_have_crystal.text.text = string.format(Language.Boss.ChuanDai4)
			end
		elseif self.cur_sylayer == 2 then
			self.node_list.rich_have_crystal.text.text = string.format(Language.Boss.ChuanDai2, info_list[3][2], info_list[4][2])
		elseif  self.cur_sylayer == 3 then
			self.node_list.rich_have_crystal.text.text = string.format(Language.Boss.ChuanDai3, info_list[3][2], info_list[4][2])
		end

	end
	self.syboss_info_list:SetDataList(info_list,3)
end

function WorldServerView:refreshsyRareFall()
	if self.sy_cell_list == nil then return end
	local list_index = self.sy_boss_list:GetSelectIndex()
	local boss_info = self.sy_boss_list:GetItemAt(list_index):GetData()

	--local boss_info = self.sy_boss_list:GetSelectItem():GetData()
	if boss_info == nil then return end
	local item_list = boss_info.drop_item_list
	local color_count = 0
	for i = 1, #self.sy_cell_list do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(tonumber(item_list[i]))
		color_count = item_cfg and item_cfg.color > GameEnum.ITEM_COLOR_ORANGE and color_count + 1 or color_count
		local param_t = {}
		param_t.star_level = 2
		if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			self.sy_cell_list[i]:SetData({item_id = tonumber(item_list[i]), param = param_t})
		else
			self.sy_cell_list[i]:SetData({item_id = tonumber(item_list[i])})
		end
	end
	if self.sy_cell_list[7] then
		self.sy_cell_list[7]:SetItemTipFrom(ItemTip.FROM_EQUIPBROWSE)
	end
	if self.sy_cell_list[8] then
		self.sy_cell_list[8]:SetItemTipFrom(ItemTip.FROM_EQUIPBROWSE)
	end
	if color_count > 3 then
		for k,v in pairs(self.sy_cell_list) do
			v:SetDefaultEffectColor(GameEnum.ITEM_COLOR_RED)
		end
	else
		for k,v in pairs(self.sy_cell_list) do
			v:SetDefaultEffectColor(GameEnum.ITEM_COLOR_ORANGE)
		end
	end
end

function WorldServerView:SYBossLayerBtnSelectCallBack(btnself)
	self.cur_sylayer = btnself.index
	self:refreshSYBossList()
end

function WorldServerView:SYBossLsitSelectCallBack(item)
	self:freshSYSmallElementByIndex(item.index)
	self.cur_kfboss_id = item.data.boss_id
	self.kf_yunluo_boss_select_data = item.data
	BossWGData.Instance:SetCurSelectBossID(self:GetShowIndex() , self.cur_sylayer, self.cur_kfboss_id)
	self:OnFlushSYBossInfo()
	local boss_data = BossWGData.Instance:GetCrossBossInfoByBossId(self.cur_kfboss_id)
	if boss_data == nil then return end
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	self.node_list.text_level_limit_sy:SetActive(role_level - boss_data.boss_level >= boss_data.max_delta_level)
end

-- 界面上显示的小部件单独一个函数来操作
function WorldServerView:freshSYSmallElementByIndex(index)
	local list_index = self.sy_boss_list:GetSelectIndex()
	local boss_data = self.sy_boss_list:GetItemAt(list_index):GetData()
	--local boss_data = self.sy_boss_list:GetSelectItem():GetData()
	-- self.layout_syrare_fall_item:SetActive(not self.layout_syboss_info:GetActive())
	--self.node_list.layout_kfcheck_hook:SetActive(boss_data.type == BossWGData.MonsterType.Boss)
	self.node_list.layout_sy_sgenter:SetActive(self:GetShowIndex() == BossViewIndex.SYBOSS)
	self.node_list.lbl_hide_sy_boss_num:SetActive(boss_data.type == BossWGData.MonsterType.HideBoss)
	if boss_data == nil then return end
end


-- 珍稀掉落
function WorldServerView:BrowsesyRareFallRecord()
	-- self.layout_syrare_fall_item:SetActive(true)
	-- self.layout_syboss_info:SetActive(false)
	self:OnFlushSYBossInfo()
end

-- boss信息
function WorldServerView:BrowsesyBossInfo()
	-- self.layout_syrare_fall_item:SetActive(false)
	-- self.layout_syboss_info:SetActive(true)
	self:OnFlushSYBossInfo()
end

function WorldServerView:BrowseSYPlayInfo()
	local title = Language.Boss.PlayTitle
	local des = self:GetShowIndex() == BossViewIndex.SYBOSS and Language.Boss.SYPlayDes or ""
	-- if self:GetShowIndex() == BossViewIndex.SYBOSS then
	-- 	des = Language.Boss.SYPlayDes
	-- end
	--DescTip.Instance:SetContent(des, title)
	RuleTip.Instance:SetContent(des, title)
end

function WorldServerView:BrowseSYKillRecord()
	-- if self:GetShowIndex() == BossViewIndex.SGBOSS then
	-- 	BossWGCtrl.Instance:OpenKillRecordView()
	-- 	return
	-- end
	-- BossWGCtrl.Instance:SendCrossBossReq(BossView.KfReqType.KILLRECORD, self.cur_sylayer, self.cur_kfboss_id)
	BossWGCtrl.Instance:OpenSYAttInfo()
end

-- 前往击杀
function WorldServerView:GoToKillsyBoss()
		if nil == self.alert_window then
			self.alert_window = Alert.New()
		end
		-- local list_index = self.sy_boss_list:GetSelectIndex()
		-- local boss_data = self.sy_boss_list:GetItemAt(list_index):GetData()
		local boss_data = self.kf_yunluo_boss_select_data
		--local boss_data = self.sy_boss_list:GetSelectItem():GetData()
		local is_false = false
		local data_info  = BossWGData.Instance:GetSYBossRender(boss_data)
		if boss_data.layer == 1 then
			if data_info.yaolidikang >= 0 and data_info.yaolichuantou >=0 then
				is_false = true
			end
		elseif boss_data.layer == 2  then
			if data_info.molichuantou >= 0 and data_info.molidikang >= 0 then
				is_false = true
			end
		elseif boss_data.layer ==  3 then
			if data_info.shenlichuantou >= 0 and data_info.shenlidikang >= 0 then
				is_false = true
			end
		end

		local role_vip_level = RoleWGData.Instance.role_vo.vip_level
		local fb_num  = BossWGData.Instance:SetShenYunTicket(role_vip_level) - BossWGData.Instance:GetShenYinFbDayCount()
		if is_false  then
			if fb_num > 0 then
				self:OpenSYticket()
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NoPerBossEnter)
			end
		else
			self.alert_window:SetLableString(string.format(Language.Boss.LevelKillMoster))
			self.alert_window:SetOkFunc(function ()
				if fb_num > 0 then
					self:OpenSYticket()
				else
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NoPerBossEnter)
				end
			end)
			self.alert_window:Open()
		end
	-- if self:GetShowIndex() == BossViewIndex.SGBOSS then
	-- 	if Scene.Instance:GetSceneType() == SceneType.SG_BOSS then
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OutFubenTip)
	-- 		return
	-- 	end


	-- 	BossWGCtrl.Instance:SetEnterBossComsunData(tiky_id, enter_comsun, Language.Boss.EnterSGBoss, Language.Boss.EnterBossConsum, function()
	-- 		BossWGCtrl.Instance:SendShangGuBossReq(BossView.SgReqType.ENTER, self.cur_sylayer - 1, self.cur_kfboss_id)
	-- 	end)
	-- 	return
	-- end

	-- if Scene.Instance:GetSceneType() == SceneType.KF_BOSS then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OutFubenTip)
	-- 	return
	-- end
	-- CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_BOSS, self.cur_sylayer)
end

function WorldServerView:OpenSYticket()
	local enter_comsun = BossWGData.Instance:GetTicketNeed()
	local tiky_id = BossWGData.Instance:GetShenYunTicketID()
	local is_xushi = 0

	BossWGCtrl.Instance:SetSYBossComsunData(is_xushi,tiky_id, enter_comsun.need_ticket,Language.Boss.EnterSYBoss, Language.Boss.EnterBossConsum,function()
		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_SHENYIN, self.cur_sylayer)
	end)
end


function WorldServerView:FlushCommonSYListTime()
	local all_item = self.sy_boss_list:GetAllItems()
	for k, v in pairs(all_item) do
		v:RefreshRemainTime()
	end
end

------------------------------------------------------
-- boss属性render
------------------------------------------------------
SyBossInfoRender = SyBossInfoRender or BaseClass(BaseRender)
function SyBossInfoRender:__init()
end

function SyBossInfoRender:__delete()
end

function SyBossInfoRender:CreateChild()
	BaseRender.CreateChild(self)
end

function SyBossInfoRender:OnFlush()
	--self.node_list.rich_boss_vo.text.text = Language.Boss.SYAttrName[self.data[1]]..self.data[2]
	self.node_list["rich_boss_vo"].text.text = Language.Boss.SYAttrName[self.data[1]]
	self.node_list["attr_num"].text.text =self.data[2]
end
function SyBossInfoRender:CreateSelectEffect()

end
----------------------------------------------------
-- boss列表ItemRender
----------------------------------------------------
SYBossItemRender = SYBossItemRender or BaseClass(BaseRender)
function SYBossItemRender:__init()
end

function SYBossItemRender:__delete()

end

function SYBossItemRender:OnFlush()
		self.node_list.img_kfboss.image:LoadSprite(ResPath.GetBossIcon("wrod_boss_"..self.data.big_icon))--暂时屏蔽
		self.node_list.lbl_kfboss_time:SetActive(false)
		self.node_list.rich_sgboss_flush:SetActive(false)
		self.node_list.lbl_sgboss_name:SetActive(false)
		self.node_list.img_jieshu:SetActive(false)

		self.node_list.text_kfboss_name:SetActive(false)
		self.node_list.lbl_yunluoboss_name:SetActive(true)
		self.node_list.lbl_yunluoboss_name.text.text = self.data.name
		self.node_list.text_kfboss_name_hl.text.text = self.data.name
		local is_false = false
		local data_info  = BossWGData.Instance:GetSYBossRender(self.data)
		if self.data.layer == 1 then
				if data_info.yaolidikang >= 0 and data_info.yaolichuantou >=0 then
					is_false = true
				end
		elseif self.data.layer == 2  then
			if data_info.molichuantou >= 0 and data_info.molidikang >= 0 then
					is_false = true
			end
		elseif self.data.layer ==  3 then
				if data_info.shenlichuantou >= 0 and data_info.shenlidikang >= 0 then
					is_false = true
				end
		end
	self:RefreshConcernTag(is_false)
end
function SYBossItemRender:OnSelectChange(is_select)
	self.node_list.select_image:SetActive(is_select)
end


function SYBossItemRender:RefreshConcernTag(vis)
	self.node_list.img_can_fight:SetActive(vis)
end

--如果要创建不同的选中特效可以重写这个函数
function SYBossItemRender:CreateSelectEffect()

end

function SYBossItemRender:onClickBossNoRewardTip()
	BossWGCtrl.Instance:OpenNoRewardView()
end
-- ------------------------------------------------------
-- -- boss层按钮
-- ------------------------------------------------------
-- KfBossLayerBtnRenderYunLuo = KfBossLayerBtnRenderYunLuo or BaseClass(BaseRender)
-- function KfBossLayerBtnRenderYunLuo:__init()
-- end

-- function KfBossLayerBtnRenderYunLuo:__delete()
-- end

-- function KfBossLayerBtnRenderYunLuo:OnFlush()

-- end

-- function KfBossLayerBtnRenderYunLuo:OnSelectChange(is_select)
-- 	if nil == self.node_list["img_btn_render_bg"] then
-- 		return
-- 	end
-- 	if is_select then
-- 		self.node_list["img_btn_render_bg"].image:LoadSprite(ResPath.GetCommonButtonToggle_atlas("btn_small_common_01"))
-- 		--self.node_list["lbl_btn_render_text"].text.text = ToColorStr(self.node_list["lbl_btn_render_text"].text.text, COLOR3B.WHITE)
-- 		self.node_list["lbl_btn_render_text"].text.text = string.format(Language.Boss.BossDownToggleName,self.data)

-- 	else
-- 		self.node_list["img_btn_render_bg"].image:LoadSprite(ResPath.GetCommonButtonToggle_atlas("btn_small_common_02"))
-- 		--self.node_list["lbl_btn_render_text"].text.text = ToColorStr(self.node_list["lbl_btn_render_text"].text.text, COLOR3B.L_ORANGE)
-- 		self.node_list["lbl_btn_render_text"].text.text = self.data
-- 	end
-- end

-- function KfBossLayerBtnRenderYunLuo:CreateSelectEffect()

-- end

--属性弹窗
SYAttView = SYAttView or BaseClass(SafeBaseView)

function SYAttView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_yunluo_boss_attr_tip")
end

function SYAttView:ReleaseCallBack()
end

function SYAttView:LoadCallBack()
end

function SYAttView:ShowIndexCallBack()
	self:Flush()
end

function SYAttView:OnFlush()
	local boss_cfg = BossWGData.Instance:SetSYBossRender()
	self.node_list.lbl_yaolict.text.text = Language.Boss.SYAttrName1["yaolichuantou"].."			+"..ToColorStr(boss_cfg.yaolichuantou, COLOR3B.DEFAULT)
	self.node_list.lbl_yaolidk.text.text = Language.Boss.SYAttrName1["yaolidikang"].."			+"..ToColorStr(boss_cfg.yaolidikang, COLOR3B.DEFAULT)
	self.node_list.lbl_molict.text.text = Language.Boss.SYAttrName1["molichuantou"].."			+"..ToColorStr(boss_cfg.molichuantou, COLOR3B.DEFAULT)
	self.node_list.lbl_molidk.text.text = Language.Boss.SYAttrName1["molidikang"].."			+"..ToColorStr(boss_cfg.molidikang, COLOR3B.DEFAULT)
	self.node_list.lbl_shenlict.text.text = Language.Boss.SYAttrName1["shenlichuantou"].."			+"..ToColorStr(boss_cfg.shenlichuantou, COLOR3B.DEFAULT)
	self.node_list.lbl_shenlidk.text.text = Language.Boss.SYAttrName1["shenlidikang"].."			+"..ToColorStr(boss_cfg.shenlidikang, COLOR3B.DEFAULT)
end

--------------------------------购买入场券------------------------------------

SYTicketView = SYTicketView or BaseClass(SafeBaseView)

function SYTicketView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_enter_dabao")
end

function SYTicketView:ReleaseCallBack()
	if nil ~=  self.comsun_cell then
		self.comsun_cell:DeleteMe()
		self.comsun_cell = nil
	end

	self.tiky_item_id = nil
	self.enter_comsun = nil
	self.map_tip = nil
	self.consum_tip = nil
	self.ok_func = nil
end

function SYTicketView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_enter_dabao, BindTool.Bind1(self.OnBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_cancle, BindTool.Bind1(self.Close, self))
	if nil ==  self.comsun_cell then
		self.comsun_cell = ItemCell.New(self.node_list.ph_cell)
	end
end

function SYTicketView:ShowIndexCallBack()
	self:Flush()
end

function SYTicketView:OnFlush()


	local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(self.tiky_item_id)
	self.comsun_cell:SetData({item_id = self.tiky_item_id})
	local color = has_tiky_num >= self.enter_comsun and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.comsun_cell:SetRightBottomColorText(has_tiky_num.."/"..self.enter_comsun, color)
	self.comsun_cell:SetRightBottomTextVisible(true)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.tiky_item_id)
	if item_cfg == nil then return end
	local name = item_cfg.name
	if self.is_xushi == 1 then
		local init_jingli = BossWGData.Instance:GetShenYunOther()[1].init_jingli
		self.node_list.text_dabao_comsun.text.text = string.format(self.map_tip, init_jingli, name, self.enter_comsun)
	else
		self.node_list.label_title.text.text = Language.Boss.XuShi
		self.node_list.text_dabao_comsun.text.text = string.format(self.map_tip, name, self.enter_comsun)
	end
	-- self.node_list.text_dabao_comsun.text.text = string.format(self.map_tip, init_jingli, name, self.enter_comsun)
	local item_price = ShopWGData.GetItemPrice(self.tiky_item_id)
	if has_tiky_num < self.enter_comsun then
		self.enter_comsun = self.enter_comsun - has_tiky_num
	end
	local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
    local cost_text = Language.Common.GoldText

    if bind_gold_num >= self.enter_comsun * item_price.bind_gold then
        cost_text = Language.Common.BindGoldText
    end

	self.node_list.lbl_consum_money:SetActive(has_tiky_num < self.enter_comsun)
	self.node_list.lbl_consum_money.text.text = string.format(self.consum_tip, cost_text, self.enter_comsun * item_price.bind_gold)

end

function SYTicketView:SetSYBossComsunData(is_xushi,tiky_item_id, enter_comsun, map_tip, consum_tip, ok_func)
	self.is_xushi = is_xushi
	self.tiky_item_id = tiky_item_id
	self.enter_comsun = enter_comsun
	self.map_tip = map_tip
	self.consum_tip = consum_tip
	self.ok_func = ok_func
	self:Open()
end

function SYTicketView:OnBtnClick()
	self.ok_func()
	self:Close()
end

---------------------------陨落之地续时---------------------------------------------------------
SYTicketTimeView = SYTicketTimeView or BaseClass(SafeBaseView)

function SYTicketTimeView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third_panel")
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_xushi_tip")
end

function SYTicketTimeView:ReleaseCallBack()
	if nil ~=  self.comsun_cell then
		self.comsun_cell:DeleteMe()
		self.comsun_cell = nil
	end

	self.tiky_item_id = nil
	self.enter_comsun = nil
	self.map_tip = nil
	self.consum_tip = nil
	self.ok_func = nil
end

function SYTicketTimeView:LoadCallBack()
	self.node_list["layout_commmon_third_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_third_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	XUI.AddClickEventListener(self.node_list.btn_enter_dabao, BindTool.Bind1(self.OnBtnClick, self))
	if nil ==  self.comsun_cell then
		self.comsun_cell = ItemCell.New(self.node_list.ph_cell)
	end
end

function SYTicketTimeView:ShowIndexCallBack()
	self:Flush()
end

function SYTicketTimeView:OnFlush()
	local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
    local cost_text = Language.Common.GoldText
	local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(self.tiky_item_id)
	self.comsun_cell:SetData({item_id = self.tiky_item_id})
	local color = has_tiky_num >= self.enter_comsun and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.comsun_cell:SetRightBottomColorText(has_tiky_num.."/"..self.enter_comsun, color)
	self.comsun_cell:SetRightBottomTextVisible(true)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.tiky_item_id)
	if item_cfg == nil then return end
	local name = item_cfg.name
	if self.is_xushi == 1 then
		local init_jingli = BossWGData.Instance:GetShenYunOther()[1].init_jingli
		self.node_list.text_dabao_comsun.text.text = string.format(self.map_tip, init_jingli, name, self.enter_comsun)
	else
		self.node_list.label_title.text.text = Language.Boss.XuShi
		self.node_list.text_dabao_comsun.text.text = string.format(self.map_tip, name, self.enter_comsun)
	end
	-- self.node_list.text_dabao_comsun.text.text = string.format(self.map_tip, init_jingli, name, self.enter_comsun)
	local item_price = ShopWGData.GetItemPrice(self.tiky_item_id)
	if has_tiky_num < self.enter_comsun then
		self.enter_comsun = self.enter_comsun - has_tiky_num
	end

	if bind_gold_num >= self.enter_comsun * item_price.bind_gold then
        cost_text = Language.Common.BindGoldText
    end
	self.node_list.lbl_consum_money:SetActive(has_tiky_num < self.enter_comsun)
	self.node_list.lbl_consum_money.text.text = string.format(self.consum_tip, cost_text, self.enter_comsun * item_price.bind_gold)

end

function SYTicketTimeView:SetSYBossComsunData(is_xushi,tiky_item_id, enter_comsun, map_tip, consum_tip, ok_func)
	self.is_xushi = is_xushi
	self.tiky_item_id = tiky_item_id
	self.enter_comsun = enter_comsun
	self.map_tip = map_tip
	self.consum_tip = consum_tip
	self.ok_func = ok_func
	self:Open()
end

function SYTicketTimeView:OnBtnClick()
	self.ok_func()
	self:Close()
end
