--Vip体验界面
RechargeVipTyView = RechargeVipTyView or BaseClass(SafeBaseView)
VipTytype = {
	vipone = 1,
	viptwo = 2,
}
function RechargeVipTyView:__init()
	self:SetMaskBg(true,true,nil,BindTool.Bind(self.BindOnClickTry,self))
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_vip_experience")
	self.active_close = false
end

function RechargeVipTyView:__delete()

end

function RechargeVipTyView:ReleaseCallBack()
	if self.vipty_show_list then
		self.vipty_show_list:DeleteMe()
		self.vipty_show_list = nil
	end
end

function RechargeVipTyView:LoadCallBack()
	self.node_list.btn_vip_ty.button:AddClickListener(BindTool.Bind1(self.OnClickVipTy, self))
	self:CreateVipTyShow()
	self:GetStateShow()
	-- self.node_list.btn_close.button:AddClickListener(BindTool.Bind1(self.OnclickClose, self))
	local vip_type = VipWGData.Instance:GetVipTimeCardType()
	if vip_type == VIP_TYPE_NUM.TYPE_ZERO then
		self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind1(self.OnClickVipTy, self))
		-- self.node_list.btn_close.button:AddClickListener(BindTool.Bind1(self.OnClickVipTy, self))
	end
	-- if VipTyWGCtrl.Instance.is_two_day_ty then
	-- 	self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind1(self.OnClickVipTy, self))
	-- 	-- self.node_list.btn_close.button:AddClickListener(BindTool.Bind1(self.OnClickVipTy, self))
	-- end
end

function RechargeVipTyView:BindOnClickTry()
	local vip_type = VipWGData.Instance:GetVipTimeCardType()
	if vip_type == VIP_TYPE_NUM.TYPE_ZERO then
		self:OnClickVipTy()
	end
	self:Close()
end

--vip体验文字展示
function RechargeVipTyView:CreateVipTyShow()
	self.vipty_show_list = AsyncListView.New(RechargVipTyItemWhiteRender, self.node_list.ph_list)
end

function RechargeVipTyView:GetStateShow()--获取展示内容
	local split_result = {}
	local vip_desc = ""
	if VipTyWGCtrl.Instance.VipType == VipTytype.vipone then
		vip_desc = RechargeWGData.Instance:GetRechargeVipLevelDesc(1)--只需要展示Vip1的内容
		self.node_list.vip_stata.text.text = Language.Recharge.Vip_tips_num
	elseif VipTyWGCtrl.Instance.VipType == VipTytype.viptwo then
		vip_desc = RechargeWGData.Instance:GetRechargeVipLevelDesc(2)--只需要展示Vip2的内容
		self.node_list.vip_stata.text.text = Language.Recharge.Vip_tips_num2
	end
	split_result = Split(vip_desc,"\n")
	vip_desc = ""
	for k,v in pairs(split_result) do
		--表示至少有一个汉字
		if #string.gsub(v, " ", "") < 3 then
			table.remove(split_result,k)
		end
	end
	if self.vipty_show_list~=nil then
		self.vipty_show_list:SetDataList(split_result,0)
	end
end

function RechargeVipTyView:OnFlush()
end

function RechargeVipTyView:OnclickClose()
	self:Close()
end

function RechargeVipTyView:OnClickVipTy()--请求体验卡
	if VipTyWGCtrl.Instance.VipType == VipTytype.vipone then
		local card_cfg = VipWGData.Instance:GetVipCardCfg()
		local card_type = card_cfg[5].card_type
		VipWGCtrl.Instance:SendBuyVipTimeCard(card_type)
		self:Close()
	end
end



--------------------------------------------------RechargeWorldShowRender--------------------------------------------------------------------------------
RechargVipTyItemRender = RechargVipTyItemRender or BaseClass(BaseRender)
function RechargVipTyItemRender:__init()

end

function RechargVipTyItemRender:__delete()
	-- self.choice_effect = nil
end
function RechargVipTyItemRender:OnFlush()
	if self.data == nil then return end
	self.node_list.showstate_text.text.text = self.data
end

--------------------------------------------------RechargeWorldShowRender--------------------------------------------------------------------------------
RechargVipTyItemWhiteRender = RechargVipTyItemWhiteRender or BaseClass(BaseRender)
function RechargVipTyItemWhiteRender:__init()

end

function RechargVipTyItemWhiteRender:__delete()
	-- self.choice_effect = nil
end
function RechargVipTyItemWhiteRender:OnFlush()
	if self.data == nil then return end
	self.data = string.sub(self.data, 3)
	self.node_list.showstate_text.text.text = string.gsub(self.data, "'#.-'","'#7cffb7'")
end