WorldTreasureWGData = WorldTreasureWGData or BaseClass()

function WorldTreasureWGData:__init()
	if WorldTreasureWGData.Instance then
		error("[WorldTreasureWGData] Attempt to create singleton twice!")
		return
	end
	WorldTreasureWGData.Instance = self

    self.theme_cfg_list = {}
	self.login_day_data = {}
	self.leichong_data_list = {}
	self.leichong_reward_flag = nil
	self.shouchong_reward_flag = -1
	self.grade = -1
	self.hunzhen_shop_count = {}
	self.cur_xianyu = 0
	self.buy_libao_cnt = -1
	self.reward_flag = -1
	self.de_exp = 0
	self.de_level = 0
	self.de_reward_flag = 0
	self.rmb_shop_buy_flag = -1
	self.daily_reward_flag = -1
	self.is_boss_dead = false

	self.upstar_grade = -1
	self.free_reward_list = {}
	self.pay_reward_flag = {}
	self.upstar_gift_rmb_buy_flag = 0
	--self:DuoBeiSceneConfig()
	self.treasure_cfg = ConfigManager.Instance:GetAutoConfig("treasure_auto")
	self.shouchong_reward_cfg = ListToMap(self.treasure_cfg.shouchong_reward, "grade")
	self.leichong_reward_cfg = ListToMap(self.treasure_cfg.leichong_reward, "grade", "ID")
	self.leichong_model_cfg = ListToMap(self.treasure_cfg.leichong_model, "grade")
	self.grade_cfg = ListToMap(self.treasure_cfg.grade, "grade")

	self.pintu_task_cfg = ListToMap(self.treasure_cfg.puzzle_task, "grade", "ID")
	self.pintu_gift_cfg = ListToMap(self.treasure_cfg.puzzle_gift, "grade", "ID")
	self.pintu_model_cfg = ListToMap(self.treasure_cfg.puzzle_model, "grade")
	self.pintu_reward_cfg = ListToMap(self.treasure_cfg.puzzle_reward, "grade")

	self.limit_buy_cfg = ListToMap(self.treasure_cfg.limit_buy, "grade", "seq")
	self.shop_model_cfg = ListToMapList(self.treasure_cfg.shop_model, "grade")
	self.dragon_egg_cfg = ListToMap(self.treasure_cfg.dragon_egg, "grade", "level")

	self.upstar_gift_grade_cfg = ListToMap(self.treasure_cfg.upstar_gift_grade, "grade")
	self.upstar_gift_cfg = ListToMap(self.treasure_cfg.upstar_gift, "grade", "seq")

	-- 试炼副本
	self.boss_cfg = ListToMapList(self.treasure_cfg.boss_cfg, "grade")
	self.refresh_time_cfg = ListToMapList(self.treasure_cfg.refresh_time, "grade")
	self.reward_cfg = ListToMapList(self.treasure_cfg.reward_config, "grade")
	self.monster_drop_cfg = ListToMap(self.treasure_cfg.monster_drop_cfg, "grade")
	self.boss_drop_cfg = ListToMap(self.treasure_cfg.boss_drop_cfg, "grade")

	-- 通用客户端描述
	self.client_desc_cfg = ListToMap(self.treasure_cfg.client_desc,"grade", "tab")

	-- 额外掉落
	self.extra_drop_model_cfg = ListToMap(self.treasure_cfg.extra_drop_model, "grade")

	-- 打开界面任务
	self.open_task_cfg = ListToMap(self.treasure_cfg.open_task, "ID")

	self.walk_together_reward_cfg = ListToMap(self.treasure_cfg.walk_together_reward, "grade", "seq") -- 携手同行奖励配置
	self.convert_shop_cfg = ListToMap(self.treasure_cfg.convert_shop, "grade", "seq") -- 专享商店
	self.convert_shop_show_cfg = ListToMap(self.treasure_cfg.convert_shop_show, "grade", "index") -- 专享商店模型展示
	self.convert_shop_stuff_map = ListToMap(self.treasure_cfg.convert_shop, "stuff_id") -- 兑换道具map
	self.scheme_cfg = ListToMap(self.treasure_cfg.scheme, "grade") -- 奕者谋定
	self.first_page_cfg = ListToMap(self.treasure_cfg.first_page, "grade", "seq") -- 首页跳转
	self.flowing_cfg = ListToMap(self.treasure_cfg.flowing, "grade", "seq") -- 川流不息
	self.pursuit_cfg = ListToMap(self.treasure_cfg.pursuit, "grade") -- 迷影寻踪
	self.pursuit_turn_cfg = ListToMap(self.treasure_cfg.pursuit_turn, "grade", "turn") -- 迷影寻踪轮次
	self.pursuit_task_cfg = ListToMap(self.treasure_cfg.pursuit_task, "grade", "seq") -- 迷影寻踪任务

	RemindManager.Instance:Register(RemindName.WorldTreasure, BindTool.Bind(self.GetWorldTreasureRed, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_Login, BindTool.Bind(self.IsLoginRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_FirstRecharge, BindTool.Bind(self.IsFirstRechargeRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_TotalReCharge, BindTool.Bind(self.IsTotalReChargeRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_ReceiveAward, BindTool.Bind(self.IsReceiveAwardRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_LimitBuy, BindTool.Bind(self.IsLimitBuyRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_RaiseStarGift, BindTool.Bind(self.IsRaiseStarGiftRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_JiangLin, BindTool.Bind(self.IsOpenShilian, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_Together, BindTool.Bind(self.IsTogetherRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_PremiumShop, BindTool.Bind(self.IsPremiumShopRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_Scheme, BindTool.Bind(self.IsSchemeRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_Flowing, BindTool.Bind(self.IsFlowingRedPoint, self))
	RemindManager.Instance:Register(RemindName.WorldTreasure_Pursuit, BindTool.Bind(self.IsPursuitRedPoint, self))

	if self.item_data_change_callback == nil then
		self.item_data_change_callback = BindTool.Bind1(self.ItemDataChange, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
	end

	self.together_invite_info_list = {}
end

function WorldTreasureWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure)
    RemindManager.Instance:UnRegister(RemindName.WorldTreasure_Login)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_FirstRecharge)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_TotalReCharge)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_ReceiveAward)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_LimitBuy)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_RaiseStarGift)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_JiangLin)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_Together)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_PremiumShop)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_Scheme)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_Flowing)
	RemindManager.Instance:UnRegister(RemindName.WorldTreasure_Pursuit)

	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	self.together_invite_info_list = nil
	self.is_open_effect_show = nil
	WorldTreasureWGData.Instance = nil
end

function WorldTreasureWGData:GetActivityThemeCfg()
	return self.treasure_cfg.tianshen_theme_desc
end

function WorldTreasureWGData:GetActivityThemeOtherCfg()
	return self.treasure_cfg.tianshen_theme_other
end

function WorldTreasureWGData:GetOtherCfg(key)
	local cfg_list = self.treasure_cfg.other
	if cfg_list and cfg_list[1] then
		return cfg_list[1][key]
	end
end

function WorldTreasureWGData:SetThemeCfgByTabIndex(tab_index, cfg)
	self.theme_cfg_list[tab_index] = cfg
end

function WorldTreasureWGData:GetThemeCfgByTabIndex(tab_index)
	return self.theme_cfg_list[tab_index]
end

function WorldTreasureWGData:GetActivityName(real_id)
	local cfg = self:GetActivityThemeCfg()
	for k,v in pairs(cfg) do
		if v.real_id == real_id then
			return v.active_name
		end
	end
	return ""
end

function WorldTreasureWGData:SetTreasureBaseInfo(protocol)
	self.grade = protocol.grade
	self.cur_grade_end_time = protocol.cur_grade_end_time
end

function WorldTreasureWGData:GetTreasureCurGrade()
	return self.grade
end

function WorldTreasureWGData:GetCurGradeEndTime()
	return self.cur_grade_end_time or 0
end

function WorldTreasureWGData:GetResPath()
	local grade_cfg = self.grade_cfg[self.grade] or {}
	local res_path = grade_cfg.res_path
	return res_path or ""
end

function WorldTreasureWGData:GetCurGradeCfg()
	return self.grade_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetGradeCfgBuGrade(grade)
	return self.grade_cfg[grade] or {}
end

function WorldTreasureWGData:GetGradeIndexIsOpen(index)
	if not self.open_tab_index_map then
		self.open_tab_index_map = {}
		for grade, cfg in pairs(self.grade_cfg) do
			local str_list = Split(cfg.open_index, "|")
			local open_index_list = {}
			for i, v in ipairs(str_list) do
				local open_index = tonumber(v)
				if open_index then
					open_index_list[open_index] = true
				end
			end
			self.open_tab_index_map[grade] = open_index_list
		end
	end
	
	return (self.open_tab_index_map[self.grade] or {})[index]
end

function WorldTreasureWGData:GetCurGradeName()
	local grade_cfg = self:GetCurGradeCfg()
	return grade_cfg.grade_name
end

function WorldTreasureWGData:GetActivityIsOpen()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local combine_day = TimeWGCtrl.Instance:GetServerRealCombineDay()
	local day_is_open = false
	for k, v in pairs(self.grade_cfg) do
		if v.time_open_type == 1 then
			if cur_day >= v.start_day and cur_day <= v.end_day then
				day_is_open = true
				break
			end
		else
			if combine_day >= v.start_day and combine_day <= v.end_day then
				day_is_open = true
				break
			end
		end
	end

	return FunOpen.Instance:GetFunIsOpened(GuideModuleName.WorldTreasureView) and day_is_open
end

--获取当前显示的图标
function WorldTreasureWGData:GetCurShowActiveIcon()
	return (self.grade_cfg[self.grade] or {}).entrance_icon
end

--物品变化
function WorldTreasureWGData:ItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local cfg = self:GetCurGradeCfg()
	if cfg.consume_item_id == change_item_id and self:GetActivityIsOpen() then
		WorldTreasureWGCtrl.Instance:FlushLoginView()
		RemindManager.Instance:Fire(RemindName.WorldTreasure_Login)
	end

	if self.convert_shop_stuff_map[change_item_id] ~= nil and self:GetActivityIsOpen() then
		ViewManager.Instance:FlushView(GuideModuleName.WorldTreasureView, TabIndex.tcdb_premium_shop)
		RemindManager.Instance:Fire(RemindName.WorldTreasure_PremiumShop)
	end
end

------------------------------------  登录奖励和龙蛋信息 -------------------------------------------
function WorldTreasureWGData:SetTreasureDragonEggInfo(protocol)
	self.de_exp = protocol.exp
	self.de_level = protocol.level
	self.de_reward_flag = protocol.reward_flag
end

-- 获取龙蛋等级
function WorldTreasureWGData:GetDragonEggLevel()
	return self.de_level or 0
end

-- 获取龙蛋经验
function WorldTreasureWGData:GetDragonEggExp()
	return self.de_exp or 0
end

-- 获取龙蛋奖励领取状态
function WorldTreasureWGData:GetDragonEggRewardFlag()
	return self.de_reward_flag or 0
end

-- 获取龙蛋是否达到最高级
function WorldTreasureWGData:GetDragonEggIsMaxLevel()
	local max_cfg = self:GetDragonEggMaxLevelCfg()
	return max_cfg.level and self.de_level >= tonumber(max_cfg.level) or false
end

function WorldTreasureWGData:GetDragonEggCfg()
	return self.dragon_egg_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetDragonEggMaxLevelCfg()
	local cfg = self:GetDragonEggCfg()
	return cfg[#cfg] or {}
end

function WorldTreasureWGData:GetDragonEggCurLevelCfg()
	return (self.dragon_egg_cfg[self.grade] or {})[self.de_level] or {}
end

function WorldTreasureWGData:GetDragonEggInjectIsRemind()
	if self.de_reward_flag == 0 and not self:GetDragonEggIsMaxLevel() then
		local cfg = self:GetCurGradeCfg()
		local cost_itemid = cfg.consume_item_id
		local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_itemid)
		if have_num > 0 then
			return true
		end
	end

	return false
end

function WorldTreasureWGData:GetReceiveEggIsRemind()
	if self.de_reward_flag == 0 and self:GetDragonEggIsMaxLevel() then
		return true
	end

	return false
end

function WorldTreasureWGData:GetLoginRewardCfg()
	local reward_cfg = self.treasure_cfg.login_reward
	local show_cfgs = {}
	for k_1, v_1 in pairs(reward_cfg) do
		if v_1.grade == self.grade then
			table.insert(show_cfgs, v_1)
		end
	end
	return show_cfgs
end

-- 获取登录奖励配置
function WorldTreasureWGData:GetLoginRewardCfgByDay(day)
	local cfg = self:GetLoginRewardCfg()
	for k,v in pairs(cfg) do
		if day == v.day_index then
			return v
		end
	end
end

--获取活动提示
function WorldTreasureWGData:GetActivityTip(index)
	local cfg = self:GetThemeCfgByTabIndex(index)
	if cfg ~= nil then
		return cfg.tab_name, cfg.rule_desc
	end
end

function WorldTreasureWGData:SetLoginRewardInfo(protocol)
	local login_day_num = protocol.login_day_num
	self.login_day_data.activity_day_index = login_day_num

	local day_list = {}
	local cfg = self:GetLoginRewardCfg()
	local login_reward_flag = protocol.login_reward_flag
	for k,v in pairs(cfg) do
		if v.grade == self.grade then
			local data = {}
			data.day_index = v.day_index
			data.reward_name = v.reward_name
			data.grade = v.grade
			data.state = TREASURE_OPERATE_TYPE.NOT_OVER--未完成

			if login_reward_flag[v.day_index] == 1 then
				data.state = TREASURE_OPERATE_TYPE.HAS_LINGQU --已领取
			elseif login_day_num == v.day_index then --可领取
				data.state = TREASURE_OPERATE_TYPE.CAN_LINGQU--可领取
			end
			data.is_receive = login_reward_flag[v.day_index] == 0 and 0 or 1	--是否领取
			data.reward_item = v.reward_item
			table.insert(day_list, data)
		end
	end
	-- table.sort(day_list, function (a, b)
	-- 	if a.state == b.state then
	-- 		return a.day_index < b.day_index
	-- 	end

	-- 	return a.state < b.state
	-- end)

	self.login_day_data.day_list = day_list

	-- RemindManager.Instance:Fire(RemindName.TianShenRoad_Login)
	-- RemindManager.Instance:Fire(RemindName.TianShenRoad)
end

function WorldTreasureWGData:GetLoginDayData()
	return self.login_day_data
end

function WorldTreasureWGData:GetLoginDayIndex()
	if self.login_day_data == nil or self.login_day_data.activity_day_index == nil then
		return 0
	end  
	return self.login_day_data.activity_day_index
end

------------------------------------限时抢购-------------------------------------
function WorldTreasureWGData:SetTreasureLimitBuyAllInfo(protocol)
	self.rmb_shop_buy_flag = protocol.rmb_shop_buy_flag
	self.daily_reward_flag = protocol.daily_reward_flag
	self.hunzhen_shop_count = protocol.hunzhen_shop_count
end

function WorldTreasureWGData:SetTreasureLimitBuyInfo(protocol)
	if self.hunzhen_shop_count[protocol.seq] then
		self.hunzhen_shop_count[protocol.seq] = protocol.count
	end
	self.rmb_shop_buy_flag = protocol.rmb_shop_buy_flag
end

function WorldTreasureWGData:GetLimitBuyCfg(grade, seq)
	return (self.limit_buy_cfg[grade] or {})[seq]
end

function WorldTreasureWGData:GetShopModelCfg()
	return self.shop_model_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetLimitBuyDiscountTime()
	local grade_cfg = self:GetCurGradeCfg()
	if grade_cfg.rmb_shop_discount_time > 0 then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() -- 当前开服天数
		local start_day = grade_cfg.start_day
		local now_time = TimeWGCtrl.Instance:GetServerTime()
		local format_time = os.date("*t", now_time)
		local combine_day = TimeWGCtrl.Instance:GetServerRealCombineDay()

		local day_time = grade_cfg.time_open_type == 1 and start_day or combine_day

		local end_time = os.time({
			year = format_time.year,
			month = format_time.month,
			day = format_time.day + day_time - open_day,   
			hour = 0,
			min = 0,
			sec = 0
		})

		end_time = end_time + grade_cfg.rmb_shop_discount_time
		return end_time - now_time
	end

	return 0
end

function WorldTreasureWGData:GetLimitBuyListData()
	local list_data = {}
	local cfgs = self.limit_buy_cfg[self.grade] or {}
	local discount_time = self:GetLimitBuyDiscountTime()
	for k_1, v_1 in pairs(cfgs) do
		--local buy_count = self:GetShopBuyNum(v_1.seq)
		if v_1.shop == 1 then
			-- if discount_time > 0 and v_1.discount == 1 then
			-- 	table.insert(list_data, v_1)
			-- elseif discount_time <= 0 and v_1.discount == 0 then
			table.insert(list_data, v_1)
			--end
		end
	end

	return list_data
end

function WorldTreasureWGData:GetShopListData()
	local list_data = {}
	local cfgs = self.limit_buy_cfg[self.grade] or {}
	for k_1, v_1 in pairs(cfgs) do
		--local buy_count = self:GetShopBuyNum(v_1.seq)
		if v_1.shop == 2 then
			table.insert(list_data, v_1)
		end
	end
	table.sort(list_data, function (a, b)
		local num1 = self:GetShopBuyNum(a.seq)
		local num2 = self:GetShopBuyNum(b.seq)
		local a_state = a.buy_count_limit > num1 and 1 or 0
		local b_state = b.buy_count_limit > num2 and 1 or 0
		if a_state == b_state then
			if a.type == b.type then
				--if a.price == b.price then
					return a.seq < b.seq
				--end
				--return a.price > b.price
			end
			return a.type > b.type
		end

		return a_state > b_state
	end)
	return list_data
end

function WorldTreasureWGData:GetShopBuyNum(seq)
	return self.hunzhen_shop_count[seq] or 0
end

function WorldTreasureWGData:GetRmbShopBuyFlag()
	return self.rmb_shop_buy_flag
end

function WorldTreasureWGData:GetDailyRewardFlag()
	return self.daily_reward_flag
end

------------------------------------  升星赠礼 -------------------------------------------
function WorldTreasureWGData:SetTreasureUpstarGiftInfo(protocol)
	self.upstar_grade = protocol.grade
	-- 0: 不可领取 1: 可领取 2: 已领取
	self.free_reward_list = protocol.free_reward_list
	-- 0: 没领取 1: 已领取
	self.pay_reward_flag = protocol.pay_reward_flag
	-- 升星赠礼-直购标记
	self.upstar_gift_rmb_buy_flag = protocol.upstar_gift_rmb_buy_flag
end

function WorldTreasureWGData:IsUpstarGiftOpen()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local combine_day = TimeWGCtrl.Instance:GetServerRealCombineDay()
	local day_is_open = false
	for k, v in pairs(self.upstar_gift_grade_cfg) do
		-- 策划要求开启用前面挡位得
		local grade_cfg = self:GetGradeCfgBuGrade(v.grade)

		if not IsEmptyTable(grade_cfg) then
			if grade_cfg.time_open_type == 1 then
				-- 开服时间
				if cur_day >= grade_cfg.start_day and cur_day <= grade_cfg.end_day then
					day_is_open = true
					break
				end
			else
				-- 合服时间
				if combine_day >= grade_cfg.start_day and combine_day <= grade_cfg.end_day then
					day_is_open = true
					break
				end
			end
		end
	end

	return FunOpen.Instance:GetFunIsOpened(GuideModuleName.WorldTreasureView) and day_is_open
end

function WorldTreasureWGData:GetUpstarGiftGradeCfg()
	return self.upstar_gift_grade_cfg[self.upstar_grade]
end

function WorldTreasureWGData:GetUpstarGiftCfg()
	return self.upstar_gift_cfg[self.upstar_grade]
end

function WorldTreasureWGData:GetUpstarGiftFreeRewardState(seq)
	return self.free_reward_list[seq]
end

function WorldTreasureWGData:GetUpstarGiftPayRewardFlag(seq)
	return self.pay_reward_flag[seq]
end

function WorldTreasureWGData:GetUpstarGiftRmbBuyFlag()
	return self.upstar_gift_rmb_buy_flag
end

function WorldTreasureWGData:GetJumpRewardIndex()
	local index = 0
	local is_find = false
	local cfg = self:GetUpstarGiftCfg()
	if not IsEmptyTable(cfg) then
		for k, v in pairs(cfg) do
			if self:GetUpstarGiftFreeRewardState(k) == 1 then
				return k
			end

			if self:GetUpstarGiftPayRewardFlag(k) == 0 and not is_find then
				is_find = true
				index = k
			end
		end
	end

	return index
end

------------------------------------  首充奖励 -------------------------------------------
function WorldTreasureWGData:GetShouChongRewardCfg()
	return self.shouchong_reward_cfg[self.grade]
end

function WorldTreasureWGData:SetTreasureShouChongInfo(protocol)
	self.shouchong_reward_flag = protocol.shouchong_reward_flag
end

function WorldTreasureWGData:GetTreasureShouChongRewardFlag()
	return self.shouchong_reward_flag
end

------------------------------------  累充奖励 -------------------------------------------
function WorldTreasureWGData:GetLeiChongCfg()
	return self.leichong_reward_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetLeiChongModelCfg()
	return self.leichong_model_cfg[self.grade]
end

function WorldTreasureWGData:SetTreasureLeiChongInfo(protocol)
	self.cur_xianyu = protocol.cur_xianyu

	local leichong_data_list = {}
	local cfg = self:GetLeiChongCfg()
	local leichong_reward_flag = protocol.leichong_reward_flag

	self.leichong_reward_flag = leichong_reward_flag
	for k,v in pairs(cfg) do
		local data = {}
		data.ID = v.ID
		data.reward_item = v.reward_item
		data.stage_value = v.stage_value
		data.grade = v.grade
		data.state = TREASURE_OPERATE_TYPE.NOT_OVER--未完成
		if leichong_reward_flag[v.ID] == 1 then
			data.state = TREASURE_OPERATE_TYPE.HAS_LINGQU --已领取
		elseif self.cur_xianyu >= v.stage_value then
			data.state = TREASURE_OPERATE_TYPE.CAN_LINGQU--可领取
		end

		leichong_data_list[v.ID + 1] = data
	end

	table.sort(leichong_data_list, SortTools.KeyLowerSorter("state", "ID"))
	self.leichong_data_list = leichong_data_list
end

function WorldTreasureWGData:GetLeiChongDataList()
	return self.leichong_data_list
end

function WorldTreasureWGData:GetLeiChongCurXianYu()
	return self.cur_xianyu
end

function WorldTreasureWGData:GetLeiChongBGRes()
	return (self.leichong_model_cfg[self.grade] or {}).bg_res_name
end


------------------------------------  拼图 -------------------------------------------
function WorldTreasureWGData:GetPinTuTaskCfg()
	return self.pintu_task_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetPinTuGiftCfg(index)
	return (self.pintu_gift_cfg[self.grade] or {})[index] or {}
end

function WorldTreasureWGData:GetPinTuRewardCfg()
	return self.pintu_reward_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetPinTuModelCfg()
	return self.pintu_model_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetPinTuOpenTaskCfg(id)
	return self.open_task_cfg[id] or {}
end

function WorldTreasureWGData:GetPinTuOpenTaskData()
	local open_task_list = {}
	local task_cfg = self:GetPinTuTaskCfg()
	local is_has_open_task = false

	for k, v in pairs(task_cfg) do
		if v.task_condition_id == 26 then
			local data = self:GetPinTuOpenTaskCfg(v.task_param1)
			is_has_open_task = true
			table.insert(open_task_list, data)
		end
	end
	return is_has_open_task, open_task_list
end

function WorldTreasureWGData:SetPinTuProtocolInfo(protocol)
	self.buy_libao_cnt = protocol.buy_libao_cnt
	self.reward_flag = protocol.reward_flag

	local pingtu_task_list = {}
	local task_cfg = self:GetPinTuTaskCfg()

	for k, v in pairs(task_cfg) do
		local data = {}
		--local reward_list = {}
		data.grade = v.grade
		data.ID = v.ID
		--table.move(v.reward_item, 1, #v.reward_item, 1, reward_list)
		data.special_reward = v.reward_item[0]
		data.reward_list = v.reward_item	--普通奖励列表从1开始
		data.cfg = v
		data.task_data = protocol.task_list[v.ID + 1]
		local state = protocol.task_list[v.ID + 1].gift_state
		data.sort = (state == 2 and 10000 or (state == 0 and 1000 or 0)) + v.ID
		table.insert(pingtu_task_list, data)
	end

	table.sort(pingtu_task_list, SortTools.KeyLowerSorter("sort"))
	self.pingtu_task_list = pingtu_task_list
end

function WorldTreasureWGData:GetPinTuTaskList()
	return self.pingtu_task_list or {}
end

-- 拼图界面礼包展示
function WorldTreasureWGData:GetCurShowPinTuGiftCfg()
	if self.buy_libao_cnt < 0 then
		return {}, false
	end
	local is_buy_all = false
	local cur_gift_cfg = self:GetPinTuGiftCfg(self.buy_libao_cnt)
	if IsEmptyTable(cur_gift_cfg) then
		is_buy_all = true
		cur_gift_cfg = self:GetPinTuGiftCfg(self.buy_libao_cnt - 1)
	end
	return cur_gift_cfg, is_buy_all
end

-- 最终奖励领取状态
function WorldTreasureWGData:GetPunTuFinalRewardState()
	local task_list = self:GetPinTuTaskList()
	local is_can_receive = true
	local cur_value = 0
	local delay_time = self:GetPunTuFinalRewardReceiveDelayTime()
	for k, v in pairs(task_list) do
		if v.task_data.gift_state <= 0 then
			is_can_receive = false
		else
			cur_value = cur_value + 1
		end
	end

	if delay_time > 0 then
		is_can_receive = false
	end

	local reward_state = TREASURE_OPERATE_TYPE.NOT_OVER
	if self.reward_flag == 1 then
		reward_state = TREASURE_OPERATE_TYPE.HAS_LINGQU
	elseif is_can_receive then
		reward_state = TREASURE_OPERATE_TYPE.CAN_LINGQU
	else
		reward_state = TREASURE_OPERATE_TYPE.NOT_OVER
	end

	return reward_state, cur_value, #task_list
end

-- 最终奖励领取延迟时间
function WorldTreasureWGData:GetPunTuFinalRewardReceiveDelayTime()
	local grade_cfg = self:GetCurGradeCfg()
	local reward_cfg = self:GetPinTuRewardCfg()
	if IsEmptyTable(grade_cfg) or IsEmptyTable(reward_cfg) then
		return 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local start_day = grade_cfg.start_day
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local format_time = os.date("*t", now_time)
	local combine_day = TimeWGCtrl.Instance:GetServerRealCombineDay()

	local day_time = grade_cfg.time_open_type == 1 and start_day or combine_day

	local end_time = os.time({
		year = format_time.year,
		month = format_time.month,
		day = format_time.day + day_time - open_day,
		hour = 0,
		min = 0,
		sec = 0
	})

	end_time = end_time + reward_cfg.delay_get_time
	return end_time - now_time
end

--天财地宝总红点
function WorldTreasureWGData:GetWorldTreasureRed()
	if not FunOpen.Instance:GetFunIsOpened(FunName.WorldTreasureView) then
		return 0
	end

	if self:IsLoginRedPoint() == 1 then
		return 1
	end

	if self:IsFirstRechargeRedPoint() == 1 then
		return 1
	end

	if self:IsTotalReChargeRedPoint() == 1 then
		return 1
	end

	if self:IsReceiveAwardRedPoint() == 1 then
		return 1
	end

	if self:IsLimitBuyRedPoint() == 1 then
		return 1
	end

	if self:IsRaiseStarGiftRedPoint() == 1 then
		return 1
	end

	if self:IsOpenShilian() == 1 then
		return 1
	end

	return 0
end


function WorldTreasureWGData:IsLoginRedPoint()
	if not FunOpen.Instance:GetFunIsOpened(FunName.tcdb_login_gift) or not self:GetGradeIndexIsOpen(TabIndex.tcdb_login_gift) then
		return 0
	end

	for k_1, v_1 in pairs(self.login_day_data.day_list or {}) do
		if v_1.state == TREASURE_OPERATE_TYPE.CAN_LINGQU then
			return 1
		end
	end

	if self:GetDragonEggInjectIsRemind() or self:GetReceiveEggIsRemind() then
		return 1
	end

	return 0
end

function WorldTreasureWGData:IsFirstRechargeRedPoint()
	if not FunOpen.Instance:GetFunIsOpened(FunName.tcdb_first_recharge) or not self:GetGradeIndexIsOpen(TabIndex.tcdb_first_recharge) then
		return 0
	end

	return self.shouchong_reward_flag == 1 and 1 or 0
end

function WorldTreasureWGData:IsTotalReChargeRedPoint()
	if not FunOpen.Instance:GetFunIsOpened(FunName.tcdb_total_recharge) or not self:GetGradeIndexIsOpen(TabIndex.tcdb_total_recharge) then
		return 0
	end

	for k_1, v_1 in pairs(self.leichong_data_list or {}) do
		if v_1.state == TREASURE_OPERATE_TYPE.CAN_LINGQU then
			return 1
		end
	end

	return 0
end

function WorldTreasureWGData:IsReceiveAwardRedPoint()
	if not FunOpen.Instance:GetFunIsOpened(FunName.tcdb_jigsaw) or not self:GetGradeIndexIsOpen(TabIndex.tcdb_jigsaw) then
		return 0
	end
	local task_list = self:GetPinTuTaskList()
	for k, v in pairs(task_list) do
		if v.task_data.gift_state == 1 then
			return 1
		end
	end

	local reward_state = self:GetPunTuFinalRewardState()
	if reward_state == TREASURE_OPERATE_TYPE.CAN_LINGQU then
		return 1
	end
	return 0
end

function WorldTreasureWGData:IsLimitBuyRedPoint()
	if not FunOpen.Instance:GetFunIsOpened(FunName.tcdb_flash_sale1) or not self:GetGradeIndexIsOpen(TabIndex.tcdb_flash_sale1) then
		return 0
	end

	return self.daily_reward_flag == 1 and 0 or 1
end

function WorldTreasureWGData:IsRaiseStarGiftRedPoint()
	local is_open = self:IsUpstarGiftOpen()
	if not is_open then --or not self:GetGradeIndexIsOpen(TabIndex.tcdb_raise_star_gift) then
		return 0
	end

	local cfg = self:GetUpstarGiftCfg()
    local buy_flag = self:GetUpstarGiftRmbBuyFlag()
	if not IsEmptyTable(cfg) then
		for k, v in pairs(cfg) do
			if self:GetUpstarGiftFreeRewardState(k) == 1 then
				return 1
			end

			if buy_flag == 1 and self:GetUpstarGiftPayRewardFlag(k) == 0 and self:GetUpstarGiftFreeRewardState(k) > 0 then
				return 1
			end
		end
	end

	return 0
end

function WorldTreasureWGData:IsOpenShilian()
	local is_show_tab = self:GetGradeIndexIsOpen(TabIndex.tcdb_jianglin)
	if not is_show_tab then
		return 0
	end
	local is_in_open = self:IsInShiLianActivity()
	return (not self.is_boss_dead and is_in_open) and 1 or 0
end

function WorldTreasureWGData:GetRedShow(name)
	if name == "tcdb_login_gift" then
		return self:IsLoginRedPoint()
	elseif name == "tcdb_first_recharge" then
		return self:IsFirstRechargeRedPoint()
	elseif name == "tcdb_total_recharge" then
		return self:IsTotalReChargeRedPoint()
	elseif name == "tcdb_jigsaw" then
		return self:IsReceiveAwardRedPoint()
	elseif name == "tcdb_flash_sale" then
		return self:IsLimitBuyRedPoint()
	elseif name == "tcdb_jianglin" then
		return self:IsOpenShilian()
	end

	return 0
end


function WorldTreasureWGData:GetClientDesc(tab_index)
	if self.client_desc_cfg[self.grade] then
		return self.client_desc_cfg[self.grade][tab_index] or {}
	end
	return {}
end

------------------------试炼副本------------------------


function WorldTreasureWGData:GetBossDropCfg()
	return self.boss_drop_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetJiangLinBossDropCfg()
	return ConfigManager.Instance:GetAutoConfig("randact_tianshenjianglin_cfg_auto").boss_drop_cfg
end

function WorldTreasureWGData:GetJiangLinFlushTimeCfg()
	return self.refresh_time_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetJiangLinBossCfg()
	return self.boss_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetJiangLinReward()
	local world_level = RankWGData.Instance:GetWordLevel() or 0
	local reward_cfg = self.reward_cfg[self.grade] or {}
	for _,v in pairs(reward_cfg) do
		if world_level >= v.min_lv and world_level <= v.max_lv then
			return v
		end
	end
end

--获得当前bossid
function WorldTreasureWGData:GetBossId()
	local cfg_list = self:GetJiangLinBossCfg()
	if not IsEmptyTable(cfg_list) then
		local world_level = RankWGData.Instance:GetWordLevel() or 0
		local boss_id = cfg_list[1].boss_id
		local boss_cfg = cfg_list[1]
		for i=1,#cfg_list do
			if world_level > cfg_list[i].limit_lv then
				boss_id = cfg_list[i].boss_id
				boss_cfg = cfg_list[i]
			else
				break
			end
		end
		return boss_id, boss_cfg
	end
end



function WorldTreasureWGData:SetTrialFbFinishInfo(protocol)
	self.partipate_list = protocol.partipate_list
	self.fall_item_list = protocol.fall_item_list
end

-- 设置试炼副本boss刷新时间范围
function WorldTreasureWGData:SetShiLianRefreshTime()
	self.refresh_time_list = {}
	local boss_time = self:GetOtherCfg("boss_time")
	local cfg = self:GetJiangLinFlushTimeCfg()
	for _,v in pairs(cfg) do
		local refresh_time = {}
		
		refresh_time.start_timestemp = TimeUtil.FormatCfgTimestamp(v.refresh_time)
		refresh_time.end_timestemp = refresh_time.start_timestemp + boss_time
		table.insert(self.refresh_time_list, refresh_time)
	end
	table.sort(self.refresh_time_list, function (a,b)
		return a.start_timestemp < b.start_timestemp
	end)

	WorldTreasureWGCtrl.Instance:SetShilLianTimer()
	
end


-- 获取试炼副本boss刷新时间范围
function WorldTreasureWGData:GetShiLianRefreshTime()
	if nil == self.refresh_time_list then
		self:SetShiLianRefreshTime()
	end
	return self.refresh_time_list
end

function WorldTreasureWGData:SetTSJLFinishInfo(protocol)
	local finish_info = {}
	local reward_list = {}
	local partipate_list = protocol.partipate_list
	for i=1,#partipate_list do
		if partipate_list[i].item_id > 0 then
			reward_list[#reward_list + 1] = partipate_list[i]
		end
	end

	finish_info.partipate_list = reward_list
	finish_info.fall_item_list = protocol.fall_item_list

	self.tsjl_finish_info = finish_info
end

function WorldTreasureWGData:GetTSJLFinishInfo()
	-- print_error("self.tsjl_finish_info:",self.tsjl_finish_info)
	return self.tsjl_finish_info
end

function WorldTreasureWGData:SetShiLianBossState(is_boss_dead)
	self.is_boss_dead = is_boss_dead == 1
end

function WorldTreasureWGData:GetShiLianBossState()
	return self.is_boss_dead
end

------------------------额外掉落------------------------

function WorldTreasureWGData:SetExtraDropInfo(protocol)
	self.ExtraDropCountList = protocol.drop_count_list
end

function WorldTreasureWGData:GetExtraDropModelCfg()
	return self.extra_drop_model_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetExtraDropCountList()
	local drop_count_list = self.ExtraDropCountList or {}
	local data_list = self.ExtraDropCountDataList
	
	if not data_list then
		data_list = {}
		local cur_grade_cfg = self:GetCurGradeCfg()
		-- local mw_drop_item_str = self:GetOtherCfg("drop_item")
		-- local max_drop_count_str = self:GetOtherCfg("drop_item_limilt")
		if cur_grade_cfg.drop_item and cur_grade_cfg.drop_item_limilt then
			local drop_item_list = Split(cur_grade_cfg.drop_item, "|")
			local max_drop_count_list = Split(cur_grade_cfg.drop_item_limilt, "|")
			for i=1,#drop_item_list do
				data_list[i] = {item_id = tonumber(drop_item_list[i]), max_count = max_drop_count_list[i] or 0, now_count = 0}
			end
		end
		self.BossDropCountDataList = data_list
	end

	for i=1,#data_list do
		data_list[i].now_count = drop_count_list[i] or 0
	end
	
	return data_list
end

--------------------------是否在试炼副本活动期间内----------------------- 
--有效时间倒计时
function WorldTreasureWGData:IsInShiLianActivity()
	if not self:GetGradeIndexIsOpen(TabIndex.tcdb_jianglin) then
		return false, nil
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()

	local refresh_time_list = self:GetShiLianRefreshTime()
	for i = 1, #refresh_time_list do
		if server_time >= refresh_time_list[i].start_timestemp and server_time < refresh_time_list[i].end_timestemp then
			return true, refresh_time_list[i]
		end
	end

	for i = 1, #refresh_time_list do
		if server_time < refresh_time_list[i].start_timestemp then
			return false, refresh_time_list[i]
		end
	end
	-- return true
	return false, nil
end

-- 获取下次刷新时间
function WorldTreasureWGData:GetNextRefreshTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	local is_next = false
	local refresh_time_list = self:GetShiLianRefreshTime()
	for i = 1, #refresh_time_list do
		if is_next then
			return refresh_time_list[i]
		end
		if server_time >= refresh_time_list[i].start_timestemp and server_time < refresh_time_list[i].end_timestemp then
			return 
		end
	end
	for i = 1, #refresh_time_list do
		if server_time < refresh_time_list[i].start_timestemp then
			return refresh_time_list[i]
		end
	end
	return
end

------------------------------首页跳转-----------------------

function WorldTreasureWGData:GetFirstPageJumpCfgList()
	return self.first_page_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetFirstPageJumpPath(seq)
	local empty = {}
	return ((self.first_page_cfg[self.grade] or empty)[seq] or empty).jump_path
end

------------------------------携手同行-----------------------

function WorldTreasureWGData:SetTreasureWalkTogetherInfo(protocol)
	self.use_item_count = protocol.use_item_count --使用道具数量
	self.together_reward_flag = protocol.reward_flag -- 奖励标记
	self.together_member_list = protocol.team_uid_list --组队队员信息
end

function WorldTreasureWGData:SetTogetherInviteFriendList(protocol)
	self.together_invite_friend_list = protocol.friend_list
end

-- 可邀请好友列表
function WorldTreasureWGData:GetTogetherInviteFriendList()
	return self.together_invite_friend_list or {}
end

function WorldTreasureWGData:GetTogetherUseItemCount()
	return self.use_item_count or 0
end

function WorldTreasureWGData:GetTogetherTaskRewardFlag(seq)
	return (self.together_reward_flag or {})[seq] or 0
end

function WorldTreasureWGData:GetTogetherMemberList()
	return self.together_member_list or {}
end

function WorldTreasureWGData:GetTogetherTaskCfgList()
	return self.walk_together_reward_cfg[self.grade] or {}
end

-- 获取任务数据
function WorldTreasureWGData:GetTogetherTaskDataList()
	local cfg_list = self:GetTogetherTaskCfgList()
	local task_data_list = {}
	for k, v in pairs(cfg_list) do
		local task_data = {}
		task_data.cfg = v
		task_data.seq = v.seq
		task_data.reward_flag = self:GetTogetherTaskRewardFlag(v.seq)
		table.insert(task_data_list, task_data)
	end
	table.sort(task_data_list, SortTools.KeyLowerSorter("reward_flag", "seq"))
	return task_data_list
end

function WorldTreasureWGData:SetTogetherInviteInfo(invite_info)
	table.insert(self.together_invite_info_list, invite_info)
end

function WorldTreasureWGData:GetTogetherInviteTopInfo()
	return not IsEmptyTable(self.together_invite_info_list) and table.remove(self.together_invite_info_list, 1) or nil
end


-- 缓存邀请人员的cd列表
function WorldTreasureWGData:AddCacheCDList(role_id, time)
    if not self.cache_invite_cd_list then
        self.cache_invite_cd_list = {}
    end
    if CountDownManager.Instance:HasCountDown("together_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("together_invite_cd_time")
    end
    CountDownManager.Instance:AddCountDown("together_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
    BindTool.Bind(self.CheckInviteTime, self), 40 + TimeWGCtrl.Instance:GetServerTime())
    time = time or 30
    self.cache_invite_cd_list[role_id] = time
    WorldTreasureWGCtrl.Instance:FlushTextInvite()
end

function WorldTreasureWGData:GetCacheCDByRoleid(role_id)
    if not self.cache_invite_cd_list then
        return 0
    end
    return self.cache_invite_cd_list[role_id] or 0
end

function WorldTreasureWGData:CheckInviteTime()
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("together_invite_cd_time")
    else
        CountDownManager.Instance:AddCountDown("together_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
        BindTool.Bind(self.CheckInviteTime,self), 40 + TimeWGCtrl.Instance:GetServerTime())
    end
end

-- 缓存邀请人员的cd列表
function WorldTreasureWGData:UpdateInviteTime(elapse_time, total_time)
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("together_invite_cd_time")
        return
    end

    for k, v in pairs(self.cache_invite_cd_list) do
        self.cache_invite_cd_list[k] = v - 1
        if v <= 0 then
            self.cache_invite_cd_list[k] = nil
            table.remove(self.cache_invite_cd_list, k)
        end
        WorldTreasureWGCtrl.Instance:FlushTextInvite()
    end
end

function WorldTreasureWGData:IsTogetherRedPoint()
	if not FunOpen.Instance:GetFunIsOpenedByTabName("tcdb_together") or not self:GetGradeIndexIsOpen(TabIndex.tcdb_together) then
		return 0
	end
	
	local task_list = self:GetTogetherTaskCfgList()
	local use_item_count = self:GetTogetherUseItemCount()
	for k, v in pairs(task_list) do
		local reward_flag = self:GetTogetherTaskRewardFlag(v.seq)
		if reward_flag == 0 and use_item_count >= v.target then
			return 1
		end
	end
	return 0
end

------------------------------专享商店------------------------------

function WorldTreasureWGData:SetTreasureConvertShopInfo(protocol)
	self.convert_flag_list = protocol.convert_flag_list
end

function WorldTreasureWGData:GetPremiumShopBuyCount(seq)
	return (self.convert_flag_list or {})[seq] or 0
end

function WorldTreasureWGData:GetPremiumShopCfgList()
	return self.convert_shop_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetPremiumShopList()
	local cfg_list = self:GetPremiumShopCfgList()
	local shop_list = {}
	for k, cfg in pairs(cfg_list) do
		local data = {}
		data.seq = cfg.seq
		data.item = cfg.item
		data.limit_num = cfg.limit_num
		data.stuff_count = cfg.stuff_count
		data.stuff_id = cfg.stuff_id
		local buy_count = self:GetPremiumShopBuyCount(cfg.seq)

		local can_exchange_times = cfg.limit_num - buy_count
		data.can_exchange_times = can_exchange_times
		data.sort = cfg.seq + (can_exchange_times > 0 and 0 or 10000)
		table.insert(shop_list, data)
	end
	table.sort(shop_list, SortTools.KeyLowerSorter("sort"))

	return shop_list
end

-- 模型展示配置
function WorldTreasureWGData:GetPremiumShopShowCfg()
	return self.convert_shop_show_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetPremiumShopShowCfgByIndex(index)
	return (self.convert_shop_show_cfg[self.grade] or {})[index]
end

function WorldTreasureWGData:IsPremiumShopRedPoint()
	if not FunOpen.Instance:GetFunIsOpenedByTabName("tcdb_premium_shop") or not self:GetGradeIndexIsOpen(TabIndex.tcdb_premium_shop) then
		return 0
	end

	local cfg_list = self:GetPremiumShopCfgList()
	for k, v in pairs(cfg_list) do
		local buy_count = self:GetPremiumShopBuyCount(v.seq)
		local can_exchange_times = v.limit_num - buy_count
		if can_exchange_times > 0 then
			local num = ItemWGData.Instance:GetItemNumInBagById(v.stuff_id)
			if num >= v.stuff_count then
				return 1
			end
		end
	end
	return 0
end

------------------------------奕者谋定------------------------------

function WorldTreasureWGData:GetSchemeRewardList(protocol)
	return (self.scheme_cfg[self.grade] or {}).reward_item or {}
end

function WorldTreasureWGData:SetTreasureSchemeInfo(protocol)
	self.scheme_protocol_data = protocol
end

-- 活动状态。 0准备 1开启 2结算
function WorldTreasureWGData:GetSchemeStatus()
	return (self.scheme_protocol_data or {}).status or 0
end

-- 自己阵营
function WorldTreasureWGData:GetSchemeMyTeamSeq()
	return (self.scheme_protocol_data or {}).team or 0
end

-- 阵营人数
function WorldTreasureWGData:GetSchemeMemberNumBySeq(seq)
	local empty = {}
	return ((self.scheme_protocol_data or empty).team_member_num or empty)[seq] or 0
end

-- 是否获胜
function WorldTreasureWGData:GetSchemeTeamIsWinBySeq(seq)
	if not self.scheme_protocol_data then
		return false
	end
	local num = self:GetSchemeMemberNumBySeq(seq)
	local min_num
	for k, v in pairs(self.scheme_protocol_data.team_member_num) do
		if not min_num then
			min_num = v
		else
			min_num = min_num <= v and min_num or v
		end
	end
	return num == min_num
end

function WorldTreasureWGData:IsSchemeRedPoint()
	if not FunOpen.Instance:GetFunIsOpenedByTabName("tcdb_scheme") or not self:GetGradeIndexIsOpen(TabIndex.tcdb_scheme) then
		return 0
	end
	local status = self:GetSchemeStatus()
	local my_team = self:GetSchemeMyTeamSeq()
	if status == 1 and my_team == -1 then
		return 1
	end
	return 0
end

------------------------------川流不息------------------------------

function WorldTreasureWGData:GetFlowingCfgList()
	return self.flowing_cfg[self.grade] or {}
end

function WorldTreasureWGData:SetTreasureFlowingInfo(protocol)
	self.flowing_reward_flag = protocol.reward_flag
end

function WorldTreasureWGData:GetFlowingDataList()
	local cfg_list = self:GetFlowingCfgList()
	if IsEmptyTable(cfg_list) then
		return {}
	end
	local data_list = {}
	for i = 0, #cfg_list do
		local data, cfg  = {}, cfg_list[i]
		data.cfg = cfg
		data.seq = cfg.seq
		data.type = cfg.type
		data.rmb_type = cfg.rmb_type
		data.price = cfg.price
		data.rmb_seq = cfg.rmb_seq
		data.reward_item = cfg.reward_item
		data.reward_flag = self:GetFlowingRewardFlag(cfg.seq) == 1
		data.is_unlock = cfg.seq == 0 or self:GetFlowingRewardFlag(cfg.seq - 1) == 1 -- 上一个已解锁
		data.is_last = cfg.seq == #cfg_list
		table.insert(data_list, data)
	end

	local function swap(table)
		for i = 4, #table, 6 do
			if i + 2 <= #table then
				table[i], table[i + 2] = table[i + 2], table[i]
			else
				for j = i, i + 2 do
					if not table[j] then
						table[j] = {}
					end
				end
				table[i], table[i + 2] = table[i + 2], table[i]
			end
		end
	end

	swap(data_list)
	self.flowing_data_list = data_list
	return self.flowing_data_list
end

function WorldTreasureWGData:GetFlowingRewardFlag(seq)
	return (self.flowing_reward_flag or {})[seq] or 0
end

function WorldTreasureWGData:IsFlowingRedPoint()
	if not FunOpen.Instance:GetFunIsOpenedByTabName("tcdb_flowing") or not self:GetGradeIndexIsOpen(TabIndex.tcdb_flowing) then
		return 0
	end
	local cfg_list = self:GetFlowingCfgList()
	if IsEmptyTable(cfg_list) then
		return 0
	end
	for k, v in pairs(cfg_list) do
		local is_received = self:GetFlowingRewardFlag(v.seq) == 1
		local is_unlock = v.seq == 0 or self:GetFlowingRewardFlag(v.seq - 1) == 1 -- 上一个已解锁
		if v.type == 0 and is_unlock and not is_received then
			return 1
		end
	end
	return 0
end

------------------------------迷影寻踪------------------------------
function WorldTreasureWGData:IsPursuitRewardPreview()
	return (self.pursuit_cfg[self.grade] or {}).reward_preview_item or {}
end

function WorldTreasureWGData:GetPursuitTaskCfgList()
	return self.pursuit_task_cfg[self.grade] or {}
end

function WorldTreasureWGData:GetPursuitTurnCfgList()
	return self.pursuit_turn_cfg[self.grade] or {}
end

function WorldTreasureWGData:SetTreasurePursuitTaskInfo(protocol)
	self.pursuit_task_list = protocol.task_list
end

function WorldTreasureWGData:GetPursuitTaskData(seq)
	return (self.pursuit_task_list or {})[seq] or {}
end

function WorldTreasureWGData:SetTreasurePursuitTaskUpdateInfo(protocol)
	if self.pursuit_task_list then
		self.pursuit_task_list[protocol.seq] = protocol.task_item
	end
end

function WorldTreasureWGData:SetTreasurePursuitGridInfo(protocol)
	self.dart_num = protocol.dart_num						-- 飞镖数量
	self.badge_num = protocol.badge_num						-- 徽章数
	self.grid_list = protocol.grid_list						-- 格子信息
end

function WorldTreasureWGData:SetTreasurePursuitInfo(protocol)
	self.cur_turn = protocol.turn								-- 轮次
	self.turn_reward_flag = protocol.reward_flag				-- 奖励标记
end

-- 轮次奖励
function WorldTreasureWGData:GetPursuitGameTurnRewardFlag(turn)
	return (self.turn_reward_flag or {})[turn] or 0
end

-- 已过轮次
function WorldTreasureWGData:GetPursuitGameTurn()
	return self.cur_turn or 0
end

-- 飞镖数量
function WorldTreasureWGData:GetPursuitDartNum()
	return self.dart_num or 0
end

-- 当前已获得徽章
function WorldTreasureWGData:GetCurBadgeNum()
	return self.badge_num or 0
end

-- 所有格子信息
function WorldTreasureWGData:GetPursuitGridDataList()
	return self.grid_list or {}
end

-- 格子信息
function WorldTreasureWGData:GetPursuitGridDataByIndex(index)
	return (self.grid_list or {})[index]
end

-- 格子信息
function WorldTreasureWGData:GetPursuitGridData(x, y)
	if x > 4 or x < 1 or y > 4 or y < 1 then
		return {}
	end
	local index = ((x - 1) * 4) + y
	return (self.grid_list or {})[index] or {}
end


-- 迷影寻踪任务
function WorldTreasureWGData:GetPursuitTaskList()
	local cfg_list = self:GetPursuitTaskCfgList()
	local task_data_list = {}
	for k, v in pairs(cfg_list) do
		local data = {}
		data.cfg = v
		data.seq = v.seq
		local protocol_data = self:GetPursuitTaskData(v.seq)
		data.process = protocol_data.process
		local is_received = protocol_data.fetch_flag == 1
		data.is_received = is_received
		local can_receive = not is_received and protocol_data.process >= v.task_param1
		data.can_receive = can_receive
		data.sort = (can_receive and 0 or 1000) + (is_received and 100000 or 0) + v.seq
		table.insert(task_data_list, data)
	end
	table.sort(task_data_list, SortTools.KeyLowerSorter("sort"))
	return task_data_list
end

-- 迷影寻踪轮次
function WorldTreasureWGData:GetPursuitTurnDataList()
	local cfg_list = self:GetPursuitTurnCfgList()
	local data_list = {}
	for k, v in pairs(cfg_list) do
		local data = {}
		data.cfg = v
		data.turn = v.turn
		data.reward_item = v.reward_item
		data.is_received = self:GetPursuitGameTurnRewardFlag(v.turn) == 1
		data.sort = data.is_received and 1000 or 0
		table.insert(data_list, data)
	end
	table.sort(data_list, SortTools.KeyLowerSorter("sort", "turn"))
	return data_list
end

function WorldTreasureWGData:IsPursuitRedPoint()
	if not FunOpen.Instance:GetFunIsOpenedByTabName("tcdb_pursuit") or not self:GetGradeIndexIsOpen(TabIndex.tcdb_pursuit) then
		return 0
	end
	if self:IsPursuitTaskRedPoint() then
		return 1
	end
	if self:IsPursuitGameRedPoint() then
		return 1
	end
	return 0
end

function WorldTreasureWGData:IsPursuitTaskRedPoint()
	local cfg_list = self:GetPursuitTaskCfgList()
	for k, v in pairs(cfg_list) do
		local protocol_data = self:GetPursuitTaskData(v.seq)
		local can_receive = protocol_data.fetch_flag ~= 1 and protocol_data.process >= v.task_param1
		if can_receive then
			return true
		end
	end
	return false
end

function WorldTreasureWGData:IsPursuitGameRedPoint()
	local dart_num = self:GetPursuitDartNum()
	return dart_num > 0
end

function WorldTreasureWGData:GetBreakGridIndex()
	return self.pursuit_break_index_cache
end

function WorldTreasureWGData:SetBreakGridIndex(index)
	self.pursuit_break_index_cache = index
end

function WorldTreasureWGData:GetPursuitClickBlock()
	return self.pursuit_game_is_block
end

function WorldTreasureWGData:SetPursuitClickBlock(is_block)
	self.pursuit_game_is_block = is_block
end

function WorldTreasureWGData:FormatGradeImage(name)
	local image_name = name .. (self.grade or 1)
	local bundle, asset = ResPath.GetWorldTreasureImg(image_name)
	return bundle, asset
end

function WorldTreasureWGData:FormatGradeRawImage(name)
	local image_name = name .. (self.grade or 1)
	local bundle, asset = ResPath.GetRawImagesPNG(image_name)
	return bundle, asset
end

function WorldTreasureWGData:GetOpenEffectIsShow()
	return self.is_open_effect_show
end

function WorldTreasureWGData:SetOpenEffectIsShow(is_show)
	self.is_open_effect_show = is_show
end