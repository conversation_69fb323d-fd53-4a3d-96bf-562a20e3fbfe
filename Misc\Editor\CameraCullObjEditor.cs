﻿using UnityEngine;
using UnityEditor;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;

namespace Game
{
    [CustomEditor(typeof(CameraCullObj))]
    public class CameraCullObjEditor : Editor
    {
        string signDis = "";
        string signDisState = "";

        public override void OnInspectorGUI()
        {
            CameraCullObj cullObj = (CameraCullObj)this.target;

            if (Application.isPlaying)
            {
                EditorGUI.BeginChangeCheck();
                cullObj.CullDistance = EditorGUILayout.IntField("剔除距离", cullObj.CullDistance);
                if (EditorGUI.EndChangeCheck())
                {
                    PlayerPrefs.SetInt(GetSignDis(), cullObj.CullDistance);
                }

                EditorGUI.BeginChangeCheck();
                cullObj.IsOnlyCullDistance = EditorGUILayout.Toggle("只依据距离判断剔除", cullObj.IsOnlyCullDistance);
                if (EditorGUI.EndChangeCheck())
                {
                    PlayerPrefs.SetInt(GetSignDisState(), cullObj.IsOnlyCullDistance ? 1 : 0);
                }
            }
            else
            {
                if (PlayerPrefs.HasKey(GetSignDis()))
                {
                    cullObj.CullDistance = PlayerPrefs.GetInt(GetSignDis());
                    PlayerPrefs.DeleteKey(GetSignDis());
                }
                else
                {
                    cullObj.CullDistance = EditorGUILayout.IntField("剔除距离", cullObj.CullDistance);
                }

                if (PlayerPrefs.HasKey(GetSignDisState()))
                {
                    cullObj.IsOnlyCullDistance = PlayerPrefs.GetInt(GetSignDisState()) == 1 ? true : false;
                    PlayerPrefs.DeleteKey(GetSignDisState());
                }
                else
                {
                    cullObj.IsOnlyCullDistance = EditorGUILayout.Toggle("只依据距离判断剔除", cullObj.IsOnlyCullDistance);
                }
            }
        }

        string GetSignDis()
        {
            return string.IsNullOrEmpty(signDis) ? ((CameraCullObj)this.target).gameObject.name + "_" + ((CameraCullObj)this.target).gameObject.GetInstanceID() : signDis;
        }

        string GetSignDisState()
        {
            return string.IsNullOrEmpty(signDisState) ? ((CameraCullObj)this.target).gameObject.name + "_" + ((CameraCullObj)this.target).gameObject.GetInstanceID() + "_IsCull" : signDisState;
        }

    }
}
