XianShiMiaoShaTips = XianShiMiaoShaTips or BaseClass(SafeBaseView)

function XianShiMiaoShaTips:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	local assetbundle = "uis/view/operation_xianshi_miaosha_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(740, 384)})
	self:AddViewResource(0, assetbundle, "Xianshi_Miaoshao_Tips")
end

function XianShiMiaoShaTips:__delete()
	-- body
end

function XianShiMiaoShaTips:OpenCallBack()
	-- body
end

function XianShiMiaoShaTips:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Common.AlertTitile
	self.node_list.yes_btn.button:AddClickListener(BindTool.Bind(self.OnClickYesBtn, self))
	self.node_list.no_btn.button:AddClickListener(BindTool.Bind(self.OnClickNoBtn, self))
	self.node_list.show_toggle.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickShowToggle, self))
end

function XianShiMiaoShaTips:ReleaseCallBack()
	self:ClearCountDown()
end

function XianShiMiaoShaTips:SetData(desc, time, yes_callback, no_callback)
	self.desc = desc
	self.time = time
	self.yes_callback = yes_callback
	self.no_callback = no_callback
end

function XianShiMiaoShaTips:OnFlush()
	self:ClearCountDown()

	if self.desc then
		self.node_list.desc.text.text = self.desc
	end

	if not self.time or self.time <= 0 then
		return
	end

	CountDownManager.Instance:AddCountDown("xianshi_miaosha_tips", BindTool.Bind1(self.ChangeTime, self), BindTool.Bind1(self.CompleteTime, self), nil, self.time, 1)
	
end

function XianShiMiaoShaTips:ChangeTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)

	if time > 0 then
		if self.node_list.countdown_desc then
			self.node_list.countdown_desc.text.text = string.format(Language.OpertionAcitvity.XianShiMiaoSha.ShopRefreshCountDown, time) 
		end
	end
end

function XianShiMiaoShaTips:CompleteTime()
	self:OnClickYesBtn()
	self:Close()
end

function XianShiMiaoShaTips:ClearCountDown()
	if CountDownManager.Instance:HasCountDown("xianshi_miaosha_tips") then
		CountDownManager.Instance:RemoveCountDown("xianshi_miaosha_tips")
	end
end

function XianShiMiaoShaTips:OnClickYesBtn()
	if self.yes_callback then
		self.yes_callback()
	end
	self:Close()
end

function XianShiMiaoShaTips:OnClickNoBtn()
	if self.no_callback then
		self.no_callback()
	end
	self:Close()
end

function XianShiMiaoShaTips:OnClickShowToggle(is_on)
	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local value = is_on == true and 1 or 0
	PlayerPrefsUtil.SetInt("miaosha_tips_view_" .. role_id .. cur_day , value)
end