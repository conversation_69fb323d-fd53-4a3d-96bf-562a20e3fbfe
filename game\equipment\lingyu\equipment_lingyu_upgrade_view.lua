-------------------------------------------------------------------------------------------------------------------
--升级灵玉提示
-------------------------------------------------------------------------------------------------------------------
EquipmentLingYuUpgradeView = EquipmentLingYuUpgradeView or BaseClass(SafeBaseView)
function EquipmentLingYuUpgradeView:__init()
	self:SetMaskBg(true)

	self:LoadConfig()
end

function EquipmentLingYuUpgradeView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 486)})
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_lingyu_upgrade_tips")
end

function EquipmentLingYuUpgradeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.EquipLingYu.GoToUpGradeLingYu
	self.left_cell = ItemCell.New(self.node_list.ph_cell_1)
	self.right_cell = ItemCell.New(self.node_list.ph_cell_2)

	self.lingyu_list_view = AsyncListView.New(EquipBlockLingYuRender, self.node_list["have_lingyu_list"])
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.Close, self))
	XUI.AddClickEventListener(self.node_list.btn_upgrade, BindTool.Bind(self.OnUpgradeClick,self))
	EquipmentWGCtrl.Instance:InitLingShiUpGradeAlertTips(BindTool.Bind(self.AlertOkFnc, self))
end

function EquipmentLingYuUpgradeView:ReleaseCallBack()
	if self.left_cell then
		self.left_cell:DeleteMe()
		self.left_cell = nil
	end

	if self.right_cell then
		self.right_cell:DeleteMe()
		self.right_cell = nil
	end

	if self.lingyu_list_view then
		self.lingyu_list_view:DeleteMe()
		self.lingyu_list_view = nil
	end

	self.enough_price = nil
	self.still_need_price = nil
	self.need_lingyu_desc = nil
end

function EquipmentLingYuUpgradeView:ShowIndexCallBack()
end

function EquipmentLingYuUpgradeView:AlertOkFnc()
	if self.enough_price then
		EquipmentWGCtrl.Instance:SendLingYuOperate(LINGYU_OPERA_TYPE.LINGYU_LEVEL_UP, self.item_index, self.select_index, 1)
		self:Close()
	else
		self:Close()
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

function EquipmentLingYuUpgradeView:SetData(item_index,select_index)
	self.item_index = item_index
	self.select_index = select_index
end

--设置价格
function EquipmentLingYuUpgradeView:SetPrice( price )
	self.node_list.lbl_price.text.text = price
end

function EquipmentLingYuUpgradeView:SetLeftCellData( item_id )
	local data_instance = EquipmentLingYuWGData.Instance
	local name_str, attr_str = data_instance:GetLingYuNatrue(item_id)
	self.node_list.lbl_name_1.text.text = name_str
	self.node_list.lbl_attr_1.text.text = attr_str
	self.left_cell:SetData({item_id = item_id})
end

function EquipmentLingYuUpgradeView:SetRightCellData( item_id )
	local data_instance = EquipmentLingYuWGData.Instance
	local name_str, attr_str = data_instance:GetLingYuNatrue(item_id)
	self.node_list.lbl_name_2.text.text = name_str
	self.node_list.lbl_attr_2.text.text = attr_str
	self.right_cell:SetData({item_id = item_id})
end

function EquipmentLingYuUpgradeView:OnFlush()
	local equipdata_instance = EquipmentLingYuWGData.Instance

	local old_item_id = equipdata_instance:GetLingYuItemIdBySelectIndex(self.item_index, self.select_index)
	local new_item_id = equipdata_instance:GetLingYuLevelUpCfgByOldId(old_item_id).new_item_id
	self.still_need_price = equipdata_instance:GetLingYuUpgradePrice(self.item_index, self.select_index)
	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
	local bind_gold = 0--GameVoManager.Instance:GetMainRoleVo().bind_gold
	self.enough_price = role_gold + bind_gold >= self.still_need_price

	local need_lingyu_desc = equipdata_instance:CalcUpgradeNeedLingYuStr(old_item_id)
	local cost_lingyu = ""
	if need_lingyu_desc then
		local and_str = self.still_need_price > 0 and Language.Equip.And or ""
		cost_lingyu = and_str .. need_lingyu_desc
	end

	local tips_str = ""
	if self.still_need_price > 0 then
		tips_str = string.format(Language.EquipLingYu.TipsContent1, self.still_need_price, cost_lingyu)
	else
		tips_str = string.format(Language.EquipLingYu.TipsContent0, cost_lingyu)
	end
	EquipmentWGCtrl.Instance:SetLingShiUpGradeAlertTips(self.still_need_price > 0, tips_str)

	self:SetPrice(self.still_need_price)
	self:SetLeftCellData(old_item_id)
	self:SetRightCellData(new_item_id)

	local lingyu_list = EquipmentLingYuWGData.Instance:GetInBagSameTypeLingYuList(self.item_index, self.select_index)
	if self.lingyu_list_view then
		self.lingyu_list_view:SetDataList(lingyu_list)
		self.lingyu_list_view:CancelSelect()
	end

	self.node_list.lingyu_tips:SetActive(IsEmptyTable(lingyu_list))
end

function EquipmentLingYuUpgradeView:OnUpgradeClick()
	EquipmentWGCtrl.Instance:OpenLingShiUpGradeAlertTips(self.still_need_price > 0)
end

-------------------------------------------------------------------------------------------------------------------
EquipBlockLingYuRender = EquipBlockLingYuRender or BaseClass(BaseRender)
function EquipBlockLingYuRender:__init()
	self.show_item = ItemCell.New(self.node_list.item_node)
end

function EquipBlockLingYuRender:__delete()
	if self.show_item then
		self.show_item:DeleteMe()
		self.show_item = nil
	end
end

function EquipBlockLingYuRender:OnFlush()
	self.show_item:SetData(self.data)
end
