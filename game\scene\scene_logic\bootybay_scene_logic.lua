BootyBayFBSceneLogic = BootyBayFBSceneLogic or BaseClass(CommonFbLogic)

function BootyBayFBSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function BootyBayFBSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function BootyBayFBSceneLogic:Enter(old_scene_type, new_scene_type)
	self.is_first = true
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
	if not guaji_state then
	  GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)  --¹Ò»ú
	end

	local ctrl = MainuiWGCtrl.Instance
    ctrl:AddInitCallBack(nil, function()
       	ctrl:PlayTopButtonTween(false)
        ctrl:SetMianUITopButtonStateTwo(true)
		ctrl.view:SetBtnLevel(true)
		GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
		-- ctrl:SetTaskActive(true)	--ÈÎÎñÀ¸
		ctrl:SetTaskContents(false)
	    ctrl:SetOtherContents(true)

        ctrl:SetTeamBtnState(false)
        ctrl:SetFBNameState(true, Language.BootyBay.LevelName)
    end)

	BootyBayWGCtrl.Instance:CloseBootybayFollowView()
	BootyBayWGCtrl.Instance:OpenBootyBayFBView()
    ViewManager.Instance:Close(GuideModuleName.BootyBayFBReadyView)
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BOOTYBAY_READY_VIEW, 0)

end


function BootyBayFBSceneLogic:Out()
	CommonFbLogic.Out(self)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
    BootyBayWGCtrl.Instance:CloseBootyBayFBView()
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	-- MainuiWGCtrl.Instance:PlayTopButtonTween(true)
    BootyBayWGData.Instance:SetWaBaoType(0)
    MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetFBNameState(false)

    --MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        -- GlobalTimerQuest:AddDelayTimer(function ()
        --     GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.AutoWaBao)
        --     BootyBayWGCtrl.Instance:SendBaoTuWaBao({item_id = 27860,quality = 2 })
        -- end, 3)
   -- end)
    --切换成无目标
    NewTeamWGCtrl.Instance:ChangNotGoalWhenOutFromOtherFb()
end

function BootyBayFBSceneLogic:GetGuajiPos()
    local target_x = nil
    local target_y = nil

    local x, y = Scene.Instance:GetMainRole():GetLogicPos()
    local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()
    --其次判断是否怪物
    for k, v in pairs(obj_move_info_list) do
        local vo = v:GetVo()
        if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
            local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
            if self.x == nil and self.y == nil  then
                self.x , self.y = GuajiWGCtrl.Instance:GetGuiJiMonsterPos()
                return
            elseif self.x ~= vo.pos_x or self.y ~= vo.pos_y then
                target_x = vo.pos_x
                target_y = vo.pos_y
                self.x = target_x
                self.y = target_y
                return target_x, target_y
            end
        end
    end
end

function BootyBayFBSceneLogic:CanGetMoveObj()
    return true
end
