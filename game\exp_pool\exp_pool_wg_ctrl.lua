require("game/exp_pool/exp_pool_view")
require("game/exp_pool/exp_pool_wg_data")

ExpPoolWGCtrl = ExpPoolWGCtrl or BaseClass(BaseWGCtrl)

function ExpPoolWGCtrl:__init()
	if ExpPoolWGCtrl.Instance then
		error("[ExpPoolWGCtrl]:Attempt to create singleton twice!")
	end
	ExpPoolWGCtrl.Instance = self

	self.data = ExpPoolWGData.New()
	self.view = ExpPoolView.New(GuideModuleName.ExpPoolView)

	self.role_data_change_callback = BindTool.Bind1(self.RoleDataChangeCallback, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"vip_level", "vip_extra_role_exp"})

	self:RegisterProtocol(CSGetVipRoleExpPool)    -- 客户端请求 8869
	self:RegisterProtocol(SCVipRoleExpInfo, "OnSCVipRoleExpInfo")
end

function ExpPoolWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
	self.role_data_change_callback = nil

	ExpPoolWGCtrl.Instance = nil
end

--请求领取
function ExpPoolWGCtrl:CSGetVipRoleExpPool()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetVipRoleExpPool)
	protocol:EncodeAndSend()
end

function ExpPoolWGCtrl:OnSCVipRoleExpInfo(protocol)
	self.data:SetSCVipRoleExpInfo(protocol)

	RemindManager.Instance:Fire(RemindName.ExpPoolView)
	MainuiWGCtrl.Instance:FlushView(0, "vip_extra_role_exp")

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
end

function ExpPoolWGCtrl:RoleDataChangeCallback(attr_name)
	if attr_name == "vip_level" or attr_name == "vip_extra_role_exp" then
		RemindManager.Instance:Fire(RemindName.ExpPoolView)
		MainuiWGCtrl.Instance:FlushView(0, "vip_extra_role_exp")

		if self.view and self.view:IsOpen() then
			self.view:Flush()
		end
	end
end

-- MainuiWGCtrl.Instance:FlushView(0, "vip_extra_role_exp")

-- --用来算经验可升多少级
-- function ExpPoolWGCtrl:PoolExpCanUpLevel(pool_exp)
-- 	--角色信息
-- 	local vo = GameVoManager.Instance:GetMainRoleVo()
-- 	--角色可用来升级的经验
-- 	local sum_pool = pool_exp + vo.exp
-- 	--当前等级角色升级所需经验
-- 	local max_exp
-- 	--可升级别
-- 	local can_up_exp
-- 	for i = 0, RoleWGData.GetRoleMaxLevel() do
-- 		local level = vo.level + i

-- 		if level > RoleWGData.GetRoleMaxLevel() then
-- 			level = RoleWGData.GetRoleMaxLevel()
-- 		end

-- 		local cfg = RoleWGData.Instance.GetRoleExpCfgByLv(level)

-- 		if cfg then
-- 			max_exp = cfg.exp
-- 			can_up_exp = sum_pool/max_exp
-- 			if sum_pool - max_exp >= 0 then
-- 				sum_pool = sum_pool - max_exp 
-- 			else
-- 				return i, can_up_exp
-- 			end
-- 		else
-- 			break
-- 		end
-- 	end

-- 	return 0, 0 
-- end
