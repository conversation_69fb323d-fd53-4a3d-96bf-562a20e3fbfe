using SrpEngine.Modules;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
public class SrpFork : MonoBehaviour
{

    private void Update()
    {
        for (int i = ForkMgr.Instance.startRoutineList.Count - 1; i >= 0; i--)
        {
            IEnumerator routine = ForkMgr.Instance.startRoutineList[i];
            StartCoroutine(routine);
            ForkMgr.Instance.startRoutineList.RemoveAt(i);
        }

        for (int i = ForkMgr.Instance.stopRoutineList.Count - 1; i >= 0; i--)
        {
            IEnumerator routine = ForkMgr.Instance.stopRoutineList[i];
            StopCoroutine(routine);
            ForkMgr.Instance.stopRoutineList.RemoveAt(i);
        }

        if (ForkMgr.Instance.isStopAllRoutines)
        {
            StopAllCoroutines();
            ForkMgr.Instance.isStopAllRoutines = false;
        }

        SRPScheduler.Instance.Update();
    }

   

}
