ShenJiTianCiMainBtn = ShenJiTianCiMainBtn or BaseClass(BaseRender)

local shenji_tianci_mainbtn_key = "shenji_tianci_mainbtn_key"

function ShenJiTianCiMainBtn:LoadCallBack()
    
end

function ShenJiTianCiMainBtn:__delete()
    self:BLLHRemoveCountDown()
end

function ShenJiTianCiMainBtn:OnFlush()
    --倒计时
    self:BLLHRemoveCountDown()
    local time
    local is_open_tedain = XianQiTeDianWGData.Instance:GetIsOpenTeDian()
    if is_open_tedain then --开启中
        self.node_list["tedian_open"]:SetActive(true)
        local end_time = XianQiTeDianWGData.Instance:GetTeDianEndTimeStamp()
        time = end_time
    else
        self.node_list["tedian_open"]:SetActive(false)
    end

    if time then
        local tedian_act_time = time - TimeWGCtrl.Instance:GetServerTime()
        if tedian_act_time > 0 then
            self:UpdateTime(0, tedian_act_time)
            CountDownManager.Instance:AddCountDown(shenji_tianci_mainbtn_key, BindTool.Bind(self.UpdateTime, self),
                                                    BindTool.Bind(self.CompleteTime, self), nil, tedian_act_time, 1)
        end
    end
end

function ShenJiTianCiMainBtn:UpdateTime(elapse_time, total_time)
    self:SetActTimeDes(TimeUtil.FormatSecondDHM5(total_time - elapse_time))
end

function ShenJiTianCiMainBtn:CompleteTime()
    self:SetActTimeDes()
end


function ShenJiTianCiMainBtn:SetActTimeDes(txt)
    if self.node_list and self.node_list["sjtc_call_time"] then
        self.node_list["sjtc_call_time"].text.text = txt or ""
    end
end

function ShenJiTianCiMainBtn:BLLHRemoveCountDown()
    if CountDownManager.Instance:HasCountDown(shenji_tianci_mainbtn_key) then
        CountDownManager.Instance:RemoveCountDown(shenji_tianci_mainbtn_key)
    end
    self:SetActTimeDes()
end
