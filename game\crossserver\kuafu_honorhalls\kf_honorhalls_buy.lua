--跨服荣誉殿堂购买buff
KuafuHonorhallBuy = KuafuHonorhallBuy or BaseClass(SafeBaseView)

function KuafuHonorhallBuy:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/kuafu_honorhalls_ui_prefab", "layout_xiuluota")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a1_commmon_panel_adorn")
	self.view_layer = UiLayer.PopTop
	self.item_slot = {}
end

function KuafuHonorhallBuy:__delete()
	for k,v in pairs(self.item_slot) do
		v:DeleteMe()
	end
	self.item_slot = nil
end

function KuafuHonorhallBuy:ReleaseCallBack()

end

function KuafuHonorhallBuy:LoadCallBack(index, loaded_times)
	self.node_list.title_view_name.text.text = Language.Activity.DailyActTips11
	self:CreateItemSlot()
	self.node_list.btn_action.button:AddClickListener(BindTool.Bind1(self.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	self.node_list.btn_question.button:AddClickListener(BindTool.Bind1(self.OnBtnTipsClickHandler, self))
end

function KuafuHonorhallBuy:OpenCallBack()
end

function KuafuHonorhallBuy:CloseCallBack()
end

function KuafuHonorhallBuy:OnFlush(param_list, index)
end

function KuafuHonorhallBuy:CreateItemSlot()
	local xiuluota_item = KuafuHonorhallWGData.Instance:GetKuaFuActivityCfg(ACTIVITY_TYPE.KF_HONORHALLS)
	local xiuluota_cfg = xiuluota_item.reward_item
	for i = 0, #xiuluota_cfg do
		self.item_slot[i] = ItemCell.New()
		self.item_slot[i]:SetInstanceParent(self.node_list["ph_item_cell_"..i])
	end
	if xiuluota_item and xiuluota_item.reward_item then
		for k,v in pairs(self.item_slot) do
			v:SetData(xiuluota_item.reward_item[k])
		end
	end
end

function KuafuHonorhallBuy:ShowIndexCallBack(index)
	local act_cfg = KuafuHonorhallWGData.Instance:GetKuaFuActivityCfg(ACTIVITY_TYPE.KF_HONORHALLS)
	if act_cfg then
		local string =Language.Honorhalls.OpenTip
		self.node_list.rich_show_time.text.text = string
	end
end

--进入修罗塔本服
function KuafuHonorhallBuy:OnClickHandler()--sss
	local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local other_cfg = DailyWGData.Instance:GetDailyOtherConfig()
	local fb_info = KuafuHonorhallWGData.Instance:GetAttrInfo()

	local is_kuafu = KuafuHonorhallWGData.Instance:IsKuaFu()
	if not is_kuafu then
		if fb_info.is_pass_all_layer == 0 then
			KuafuHonorhallWGCtrl.Instance:SendCSXiuluoTowerEnterReq()--空协议
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Daily.MaxTiaoZhanLevel)--已通关最高层
		end
	else
		self:EnterXiuLuoTower()
	end
end

--进入修罗塔防跨服
function KuafuHonorhallBuy:EnterXiuLuoTower()
	local fb_info = KuafuHonorhallWGData.Instance:GetAttrInfo()
	if fb_info.is_pass_all_layer == 0 then
		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_HONORHALLS)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Daily.MaxTiaoZhanLevel)--已通关最高层
	end
end

function KuafuHonorhallBuy:OnBtnTipsClickHandler()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Honorhalls.HonorhallTips)
		role_tip:SetContent(Language.Honorhalls.HonorhallAfterTips)
	else
		print_error("KuafuHonorhallBuy:OnBtnTipsClickHandler()","OnClickBtnMLImageChongTip() can not find the get way!")
	end
end