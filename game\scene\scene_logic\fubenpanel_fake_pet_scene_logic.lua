 FuBenPanelFakePetLogic = FuBenPanelFakePetLogic or BaseClass(CommonFbLogic)
local PET_STATE = {
	NONE = 0,
	STATE_DEFAULT_POINT = 1,
	STATE_TO_AUTO_FIGHT = 2,
}
function FuBenPanelFakePetLogic:__init()
	self.old_scene = nil
	self.new_scene = nil

	self.record_time = 0

	self.cur_state = PET_STATE.NONE

end

function FuBenPanelFakePetLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end



function FuBenPanelFakePetLogic:Enter(old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
		ViewManager.Instance:CloseAll()
		local scene_id  = Scene.Instance:GetSceneId()
		local first_level_scene_id = ConfigManager.Instance:GetAutoConfig("shouhushengling_cfg_auto").level[1].scene_id
		if scene_id == first_level_scene_id then
			-- CgManager.Instance:Play(BaseCg.New("cg/cg_xiannv/cg_xiannv_4132001_prefab", "CG_xiannv_4132001"),function()
			-- 	FuBenWGCtrl.Instance:CSFBPlayedCG()
			-- 	local main_role = Scene.Instance:GetMainRole()
			-- 	main_role:GetDrawObj():SetVisible(true)

			-- 	local guard_obj = main_role:GetGuardObj()
			-- 	if guard_obj then
			-- 		guard_obj:GetDrawObj():SetVisible(true)
			-- 	end

			-- 	local pet_obj_list = main_role:GetPetObjList()
			-- 	if not IsEmptyTable(pet_obj_list) then
			-- 		for k,v in pairs(pet_obj_list) do
			-- 			if v then
			-- 				v:GetDrawObj():SetVisible(true)
			-- 			end
			-- 		end
			-- 	end
			-- end,
			-- function()
			-- 	local main_role = Scene.Instance:GetMainRole()
			-- 	main_role:GetDrawObj():SetVisible(false)

			-- 	local guard_obj = main_role:GetGuardObj()
			-- 	if guard_obj then
			-- 		guard_obj:GetDrawObj():SetVisible(false)
			-- 	end

			-- 	local pet_obj_list = main_role:GetPetObjList()
			-- 	if not IsEmptyTable(pet_obj_list) then
			-- 		for k,v in pairs(pet_obj_list) do
			-- 			if v then
			-- 				v:GetDrawObj():SetVisible(false)
			-- 			end
			-- 		end
			-- 	end
            -- end, nil)
            FuBenWGCtrl.Instance:CSFBPlayedCG()
		else
			FuBenWGCtrl.Instance:CSFBPlayedCG()
		end
	end
	self.main_role = Scene.Instance:GetMainRole()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.FbName.XianLingShengDian)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		FuBenPanelWGCtrl.Instance:OpenPetTaskView()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		local scene_id = Scene.Instance:GetSceneId()
		local param_t = {scene_id = scene_id}
		Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, nil, nil, 11, param_t)
	end)

	-- local cfg = FuBenPanelWGData.Instance:GetPetLevelCfg()
	-- if cfg and not IsEmptyTable(cfg) then
	-- 	FuBenPanelWGCtrl.Instance:OpenStarAniView({time3 = cfg.three_star_time, time2 = cfg.two_star_time, time1 = cfg.one_star_time, time0 = cfg.zero_star_time,
	-- 		per0 = cfg.star0, per1 =cfg.star1, per2 = cfg.star2, per3 = cfg.star3, str = Language.Boss.StarAniStr,})
	-- end	FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.FBCT_PETBEN)
	FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.FBCT_PETBEN)
	if FuBenWGData.Instance:DefFBState() and FuBenWGData.Instance:GetCurPetIndex() then
		FuBenWGData.Instance:DefFBState(false)
	end
		-- 属性事件处理
	self.attr_handlers = {
		hp = BindTool.Bind1(self.OnHpChanged, self),
	}

	self.goddess_obj = nil

	self:GetGoddessObj()

	if not self.monster_enter_event1 then
		self.monster_enter_event1 = GlobalEventSystem:Bind(ObjectEventType.VISIBLE_OBJ_ENTER_MONSTER,BindTool.Bind(self.ProGetGoddessObj,self))
	end
	if not self.goddess_hurt_event then
		self.goddess_hurt_event = GlobalEventSystem:Bind(OtherEventType.GODDESS_HURT_FLOAT_TEXT,BindTool.Bind(self.OnGoddessHurt,self))
	end
end

function FuBenPanelFakePetLogic:GetGoddessObj(  )
	local pet_scence_info = FuBenPanelWGData.Instance:GetPetScenceAllInfo()
	if pet_scence_info and not IsEmptyTable(pet_scence_info) then
		local pet_fb_cfg = FuBenPanelWGData.Instance:GetPetItemByLayer(pet_scence_info.level)
		if pet_fb_cfg then
			self.monster_id = pet_fb_cfg.xiannv_id
		end
	end
	self:GetGoddessObjByMonsterId(self.monster_id)
end
function FuBenPanelFakePetLogic:ProGetGoddessObj(monster_vo)
	if monster_vo.monster_id == self.monster_id then
		self:GetGoddessObjByMonsterId(self.monster_id)
	end
end

function FuBenPanelFakePetLogic:GetGoddessObjByMonsterId(monster_id)
	-- if self.goddess_obj == nil then
		self.goddess_obj = Scene.Instance:GetMonstObjByMonstID(monster_id)
	-- end

end

function FuBenPanelFakePetLogic:OnGoddessHurt(blood)
	if self.goddess_obj == nil then
		return
	end
	if self.goddess_obj.draw_obj then
		local bottom_point = self.goddess_obj.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)--BuffBottom)
		if not IsNil(bottom_point) then
			-- FightText.Instance:ShowBeHurt(blood ,nil, bottom_point)
			local data = {}
			data.fighttype = FIGHT_TYPE.NORMAL
			data.blood = blood
			HUDManager.Instance:ShowHurtEnter(self.goddess_obj, data)
		end
	end
	local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
	for i, v in pairs(fall_item_list) do
		return
	end

	if GuajiCache.guaji_type == GuajiType.Auto then
		local pos_x, pos_y = FuBenPanelWGData.Instance:GetPetPeriPos()
		local gcl = GuajiWGCtrl.Instance
	    gcl:SetMoveToPosCallBack(function ()
	    	gcl:ClearAllOperate()
	    	gcl:ClearGuajiCache()
	        gcl:SetGuajiType(GuajiType.Auto)
	    end)
        gcl:MoveToPos(Scene.Instance:GetSceneId(), pos_x, pos_y + 4) --仙女前方两个格子
	end
end

function FuBenPanelFakePetLogic:PlayerDataChangeCallback(attr_name, value, old_value)
	local handler = self.attr_handlers[attr_name]
	if handler ~= nil then
		handler()
	end
end
function FuBenPanelFakePetLogic:OnHpChanged()

end

function FuBenPanelFakePetLogic:Out(old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		CommonFbLogic.Out(self)
		MainuiWGCtrl.Instance:SetTaskContents(true)
		-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
		MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(true)
		MainuiWGCtrl.Instance:SetOtherContents(false)
		FuBenPanelWGCtrl.Instance:ClosePetTaskView()
		FuBenPanelCountDown.Instance:CloseViewHandler()
	end
	if self.monster_enter_event1 then
		GlobalEventSystem:UnBind(self.monster_enter_event1)
		self.monster_enter_event1 = nil
	end
	if self.goddess_hurt_event then
		GlobalEventSystem:UnBind(self.goddess_hurt_event)
		self.goddess_hurt_event = nil
	end
	-- GuajiWGCtrl.Instance:SetGuajiRange(nil)

	local is_false =  FuBenPanelWGData.Instance:GetPetFBReward()
	if is_false.is_pass ==  0 then
		GlobalTimerQuest:AddDelayTimer(function ()
			FuBenWGCtrl.Instance:OpenFuBenLoseView()
			-- MainuiWGCtrl.Instance:SetTaskActive(true)
			MainuiWGCtrl.Instance:SetFBNameState(false)
			MainuiWGCtrl.Instance:SetTeamBtnState(true)
		end, 1.5)

	end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
	if self.state_machine then
		self.state_machine:DeleteMe()
		self.state_machine = nil
	end
	if self.goddess_obj then
		self.goddess_obj = nil
	end
	if self.main_role then
		self.main_role = nil
	end
	FuBenPanelWGCtrl.Instance.is_pet_sceneani_move = false
	FuBenWGCtrl.Instance:CheckLeaveOpenView(old_scene_type, TabIndex.fubenpanel_pet)

	-- FuBenPanelWGCtrl.Instance:CloseStarAniView()
end

function FuBenPanelFakePetLogic:OpenFbSceneCd()

end
function FuBenPanelFakePetLogic:Update(now_time, elapse_time)
	BaseSceneLogic.Update(self, now_time, elapse_time)
end

-- 获取挂机打怪的敌人
function FuBenPanelFakePetLogic:GetGuajiCharacter()
	return self:SelectObjHelper(Scene.Instance:GetMonsterList())
end

function FuBenPanelFakePetLogic:SelectObjHelper(obj_list)
	local target_obj = nil
	local target_distance = 10000
	local point_list = FuBenPanelWGData.Instance:GetGuaJiPoint()
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return target_obj, target_distance
	end

	local m_pox_x , m_pos_y = main_role:GetLogicPos()
	local g_pos_x, g_pos_y = m_pox_x , m_pos_y
	if self.goddess_obj then
		g_pos_x, g_pos_y = self.goddess_obj:GetLogicPos()
	end

	local my_is_in_polygon = GameMath.IsInPolygon(point_list, u3d.vec2(m_pox_x, m_pos_y))
	local skill_info = SkillWGData.Instance:GetFirstCommonSkillInfo()
	local skill_cfg = nil
	if skill_info ~= nil and skill_info.skill_id ~= 0 then
		skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_info.skill_id, 1)
	end

	local skill_dis_pow2 = 0
	if skill_cfg then
		skill_dis_pow2 = skill_cfg.distance * skill_cfg.distance
	end

	--找到距离goddess最近的
	for _, v in pairs(obj_list) do
		if v:IsCharacter() or v:GetModel():IsVisible() then
			local can_select = Scene.Instance:IsEnemy(v, main_role)
			local target_x, target_y = v:GetLogicPos()
			if can_select then
				can_select = GameMath.IsInPolygon(point_list, u3d.vec2(target_x, target_y))
				-- 策划需求，如果怪在范围外，人在挂机范围内，这个时候如果怪在普攻的范围内，要去打怪
				if not can_select and my_is_in_polygon and skill_cfg ~= nil then
					if GameMath.GetDistance(m_pox_x , m_pos_y, target_x, target_y, false) <= skill_dis_pow2 then
						can_select = true
					end
				end
			end

			if can_select then
				local distance = GameMath.GetDistance(g_pos_x, g_pos_y, target_x, target_y, false)
				if distance < target_distance then
					if v:IsInBlock() then
						if nil == target_obj then
							target_obj = v
						end
					else
						target_obj = v
						target_distance = distance
					end
				end
			end
		end
	end

	return target_obj, target_distance
end

function FuBenPanelFakePetLogic:SpecialSelectEnemy()
	if GuajiCache.guaji_type == GuajiType.Auto then
		return true
	end
	return false
end

-- 尝试去采集
function FuBenPanelFakePetLogic:TryToGather()
	local info = FuBenPanelWGData.Instance:GetPetScenceAllInfo()
	if info and not IsEmptyTable(info) and info.is_pass == 0 and info.status == 1 then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("shouhushengling_cfg_auto").other[1]
		MoveCache.SetEndType(MoveEndType.GatherById)
		MoveCache.param1 = other_cfg.id
		GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), other_cfg.Gather_x, other_cfg.Gather_y, 4)
		return true
	end
	return false
end


-- 获取挂机打怪的位置(不去主动找怪)
function FuBenPanelFakePetLogic:GetGuiJiMonsterPos()
	return nil
end

function FuBenPanelFakePetLogic:IsMonsterEnemy(target_obj, main_role)
	if target_obj and self.goddess_obj then
		if target_obj.vo.monster_id == self.goddess_obj.vo.monster_id then
			return false
		end
	end

	return BaseSceneLogic.IsMonsterEnemy(self, target_obj, main_role)
end

-- 是否达到指定条件搜索全地图怪
function FuBenPanelFakePetLogic:ConditionScanMonster(vo)
	--宠物本需要判断挂机范围
	local point_list = FuBenPanelWGData.Instance:GetGuaJiPoint()
	return GameMath.IsInPolygon(point_list, u3d.vec2(vo.pos_x, vo.pos_y))
end

function FuBenPanelFakePetLogic:GetGuajiPos()
	local is_auto = FuBenPanelWGCtrl.Instance:SetPetFBPrepareTime()
	if is_auto or FuBenPanelWGData.Instance:GetPetScenceStatus() == 1 then
		return nil, nil
	end
	return FuBenPanelWGData.Instance:GetPetGuaJiPos()
end

function FuBenPanelFakePetLogic:GetGuaJiDir()
	return FuBenPanelWGData.Instance:GetPetGuaJiDir()
end