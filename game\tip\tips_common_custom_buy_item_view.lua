TipsCustomBuyItemView = TipsCustomBuyItemView or BaseClass(SafeBaseView)

function TipsCustomBuyItemView:__init(view_name)
	self.view_name = "TipsCustomBuyItemView"
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_custom_buy_item_tips_panel")
end

function TipsCustomBuyItemView:LoadCallBack()
	self.node_list.title_view_name.text.text = self.data.title_view_name or Language.Common.BuyItemTipsTitle

	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.ListenCancelCallBack, self))
	XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind1(self.ListenOkCallBack, self))
	XUI.AddClickEventListener(self.node_list.add_btn, BindTool.Bind1(self.OnClickBuyAddBtn, self))
	XUI.AddClickEventListener(self.node_list.sub_btn, BindTool.Bind1(self.OnClickBuySubBtn, self))
	XUI.AddClickEventListener(self.node_list.max_btn, BindTool.Bind1(self.OnClickOneKeyMaxBtn, self))
	XUI.AddClickEventListener(self.node_list.custom_btn, BindTool.Bind1(self.OnClickBuyCount, self))

	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function TipsCustomBuyItemView:ReleaseCallBack()
	if self.ok_cb then
		self.ok_cb = nil
	end

	if self.cancel_cb then
		self.cancel_cb = nil
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.data = nil
end

function TipsCustomBuyItemView:OpenCallBack()
	self.cur_buy_num = 1
end

function TipsCustomBuyItemView:SetData(info, Ok_CB, Cancel_CB)
	self.data = info
	self.ok_cb = Ok_CB
	self.cancel_cb = Cancel_CB

	self:CheckOpenTips()
end

-- 检测提示框
function TipsCustomBuyItemView:CheckOpenTips()
	if self.data == nil then
		return
	end

	local need_num = self.data.expend_item_num or 0
	-- 已拥有物品个数
	local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.expend_item_id) --拥有的材料数量

	if self.data.expend_item_id == COMMON_CONSTS.VIRTUAL_ITEM_GOLD then
		local money_type = MoneyType.XianYu
		has_num = RoleWGData.Instance:GetMoney(money_type)
	elseif self.data.expend_item_id == COMMON_CONSTS.ITEM_GUILD_COIN then
		has_num = GuildWGData.Instance:GetGuildCoin()
	end

	local is_enough = has_num >= need_num

	if is_enough then
		self:Open()
	else
		local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(self.data.expend_item_id)
		local item_name = item_cfg and item_cfg.name or ""
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Common.ItemNoEnough, item_name))
		TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = self.data.expend_item_id })
	end
end

function TipsCustomBuyItemView:ExecuteOkFunc()
	if self.ok_cb then
		self.ok_cb(self.cur_buy_num)
	end
end

function TipsCustomBuyItemView:ExecuteCancelFunc()
	if self.cancel_cb then
		self.cancel_cb()
	end
end

--[[
local tips_data = {
	title_view_name,		-- 弹窗标题（默认-购买提示）
	item_id,				-- 物品ID
	expend_item_id,			-- 需要消耗的物品ID
	expend_item_num,		-- 需要消耗的物品数量（单价）
	max_buy_count,			-- 限购数量（最大上限）
	is_show_limit,			-- 是否显示限购
}
--]]
function TipsCustomBuyItemView:OnFlush()
	if self.data == nil then
		return
	end

	-- 物品配置
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local item_name = item_cfg and item_cfg.name or ""
	local item_color = item_cfg and item_cfg.color or ""
	local name_str = ToColorStr(item_name, ITEM_COLOR[item_color])
	self.node_list.item_name.text.text = name_str

	local item_icon = ItemWGData.Instance:GetItemIconByItemId(self.data.expend_item_id)
	if item_icon then
		local bundle, asset = ResPath.GetItem(item_icon)
		self.node_list.need_icon.image:LoadSprite(bundle, asset, function()
			self.node_list.need_icon.image:SetNativeSize()
		end)
		self.node_list.have_icon.image:LoadSprite(bundle, asset, function()
			self.node_list.have_icon.image:SetNativeSize()
		end)
	end

	self.item_cell:SetData({ item_id = self.data.item_id })

	self:FlushBuyItemPrice()
end

------------------------------------------------------------------------------------------
-- 取消点击
function TipsCustomBuyItemView:ListenCancelCallBack()
	self:ExecuteCancelFunc()
	self:Close()
end

-- 确认点击
function TipsCustomBuyItemView:ListenOkCallBack()
	self:ExecuteOkFunc()
	self:Close()
end

function TipsCustomBuyItemView:OnClickBuyAddBtn()
	if self.cur_buy_num < self:GetBuyItemMaxNum() then
		self.cur_buy_num = self.cur_buy_num + 1

		self:FlushBuyItemPrice()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
	end
end

function TipsCustomBuyItemView:OnClickBuySubBtn()
	if self.cur_buy_num > 1 then
		self.cur_buy_num = self.cur_buy_num - 1
	
		self:FlushBuyItemPrice()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MinValue1)
	end
end

function TipsCustomBuyItemView:OnClickOneKeyMaxBtn()
	self.cur_buy_num = self:GetBuyItemMaxNum()
	self:FlushBuyItemPrice()
end

function TipsCustomBuyItemView:OnClickBuyCount()
	local pop_num_view = TipWGCtrl.Instance:GetPopNumView()
	pop_num_view:Open()
	pop_num_view:SetText(1)
	pop_num_view:SetMinValue(1)
	pop_num_view:SetMaxValue(self:GetBuyItemMaxNum())
	pop_num_view:SetOkCallBack(function (num)
		self.cur_buy_num = num
		self:FlushBuyItemPrice()
	end)
end

function TipsCustomBuyItemView:FlushBuyItemPrice()
	self.node_list.custom_num.text.text = self.cur_buy_num

	-- 物品个数
	local need_num = self.data.expend_item_num or 0
	self.node_list.need_num.text.text = need_num * self.cur_buy_num

	local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.expend_item_id) --拥有的材料数量
	if self.data.expend_item_id == COMMON_CONSTS.VIRTUAL_ITEM_GOLD then
		local money_type = MoneyType.XianYu
		has_num = RoleWGData.Instance:GetMoney(money_type)
	elseif self.data.expend_item_id == COMMON_CONSTS.ITEM_GUILD_COIN then
		has_num = GuildWGData.Instance:GetGuildCoin()
	end
	self.node_list.have_num.text.text = CommonDataManager.ConverMoneyByThousand(has_num)

	self.node_list.can_buy_num_text:SetActive(self.data.is_show_limit)
	if self.data.is_show_limit then
		local temp_max_num = self.data.max_buy_count or 999
		self.node_list.can_buy_num_text.text.text = string.format(Language.Common.LimitBuyItemTipsDesc, temp_max_num)
	end
end

function TipsCustomBuyItemView:GetBuyItemMaxNum()
	-- 物品个数
	local need_num = self.data.expend_item_num or 0
	-- 已拥有物品个数
	local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.expend_item_id) --拥有的材料数量
	if self.data.expend_item_id == COMMON_CONSTS.VIRTUAL_ITEM_GOLD then
		local money_type = MoneyType.XianYu
		has_num = RoleWGData.Instance:GetMoney(money_type)
	elseif self.data.expend_item_id == COMMON_CONSTS.ITEM_GUILD_COIN then
		has_num = GuildWGData.Instance:GetGuildCoin()
	end
	local max_num = math.floor(has_num / need_num)
	local temp_max_num = self.data.max_buy_count or 999
	if max_num > temp_max_num then
		max_num = temp_max_num
	end
	return max_num
end