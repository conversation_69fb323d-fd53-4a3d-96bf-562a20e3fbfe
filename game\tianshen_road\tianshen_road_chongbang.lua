
function TianshenRoadView:InitCBView()
	self.cb_late_id = -1

	--XUI.AddClickEventListener(self.node_list.cb_btn_tip, BindTool.Bind(self.OnCBBtn<PERSON>ip<PERSON><PERSON><PERSON><PERSON><PERSON>,self))
	XUI.AddClickEventListener(self.node_list.cb_btn_bang, BindTool.Bind(self.OnCBBtnRankClickHnadler,self))

	-- local theme_cfg = TianshenRoadWGData.Instance:GetThemeCfgByTabIndex(TabIndex.tianshenroad_chongbang)
	-- if theme_cfg ~= nil then
	-- 	self.node_list.cb_tip_label.text.text = theme_cfg.rule_tip
	-- end

	--self.cb_fir_reward_list = AsyncListView.New(ItemCell, self.node_list.cb_first_list)
	self.cb_reward_list = AsyncListView.New(CBRewardRender, self.node_list.cb_reward_list)

	-- local rewards,min_cap = TianshenRoadWGData.Instance:GetCBFirstReward()
	-- if rewards then
	-- 	self.cb_fir_reward_list:SetDataList(rewards)
	-- end
	-- self.node_list.cb_cap_value.text.text = min_cap or 0

	TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_CHONGBANG, TIANSHEN_THEME_CHONGBANG_OP_TYPE.TYPE_RANK)

	self:InitCBModelReward()

	self:CBTimeCountDown()
	self:FlushCBView()
end

function TianshenRoadView:ReleaseCBView()
	-- if self.cb_fir_reward_list then
	-- 	self.cb_fir_reward_list:DeleteMe()
	-- 	self.cb_fir_reward_list = nil
	-- end

	if self.cb_reward_list then
		self.cb_reward_list:DeleteMe()
		self.cb_reward_list = nil
	end

	if self.cb_model then
		self.cb_model:DeleteMe()
		self.cb_model = nil
	end

	self.late_id = nil

	CountDownManager.Instance:RemoveCountDown("tianshenroad_chongbang_count_down")
end

function TianshenRoadView:TRCBShowIndexCallBack()
	self:DoTRCBAnim()
	self:SetModelData()
end


function TianshenRoadView:FlushCBView()
	self:FlushCBReward()
	self:FlushMyRankInfo()
	self:SetModelData()
end

function TianshenRoadView:InitCBModelReward()
	if not self.cb_model then
		self.cb_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["cb_model_root"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}
		
		self.cb_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.cb_model)
	end
end

--设置模型数据
function TianshenRoadView:SetModelData()
	local top_role_item = TianshenRoadWGData.Instance:GetTopRoleItemInfo()
	local have_top_role = nil ~= top_role_item and top_role_item.role_id > 0

	local name_str = ""
	if have_top_role and self.cb_model then
		if self.late_id ~= top_role_item.role_id then
			self.late_id = top_role_item.role_id
			local special_status_table = {ignore_wing = true, ignore_fazhen = true, ignore_jianzhen = true, ignore_halo = true}
			self.cb_model:SetModelResInfo(top_role_item, special_status_table)
		end
		self.cb_model:PlayLastAction()

		name_str = top_role_item.name
	else
		name_str = Language.TianShenRoad.NotTopRank
	end

	self.node_list.cb_name_text.text.text = name_str

	self.node_list.cb_model_root:SetActive(have_top_role)
	self.node_list.not_model_img:SetActive(not have_top_role)
end

function TianshenRoadView:FlushCBReward()
	local list = TianshenRoadWGData.Instance:GetCBReward()
	if list then
		self.cb_reward_list:SetDataList(list)
	end
end

function TianshenRoadView:FlushMyRankInfo()
	local rank, cap = TianshenRoadWGData.Instance:GetMyRankAndCap()
	self.node_list.cb_rank.text.text = rank > 0 and string.format(Language.TianShenRoad.RankStr1, rank) or Language.TianShenRoad.RankStr2
	self.node_list.cb_my_cap.text.text = string.format(Language.TianShenRoad.RankStr5, cap)
end

-- function TianshenRoadView:OnCBBtnTipClickHnadler()
-- 	local role_tip = RuleTip.Instance
-- 	if role_tip then
-- 		local title,desc = TianshenRoadWGData.Instance:GetActivityTip(TabIndex.tianshenroad_chongbang)
-- 		if title ~= nil and desc ~= nil then
-- 			role_tip:SetTitle(title)
-- 			role_tip:SetContent(desc)
-- 		end
-- 	end
-- end

function TianshenRoadView:OnCBBtnRankClickHnadler()
	-- TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_CHONGBANG, TIANSHEN_THEME_CHONGBANG_OP_TYPE.TYPE_RANK)
	TianshenRoadWGCtrl.Instance:OpenTianShenCapRankView()
end

--有效时间倒计时
function TianshenRoadView:CBTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("tianshenroad_chongbang_count_down")
	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_chongbang)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.cb_time_label.text.text = string.format(Language.TianShenRoad.CountDownText, TimeUtil.FormatSecondDHM2(invalid_time - server_time))
		CountDownManager.Instance:AddCountDown("tianshenroad_chongbang_count_down", BindTool.Bind1(self.UpdateCBCountDown, self), BindTool.Bind1(self.CBTimeCountDown, self), invalid_time, nil, 1)
	else
		self.node_list.cb_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
	end
end

function TianshenRoadView:UpdateCBCountDown(elapse_time, total_time)
	self.node_list.cb_time_label.text.text = string.format(Language.TianShenRoad.CountDownText, TimeUtil.FormatSecondDHM2(total_time - elapse_time))
end

function TianshenRoadView:DoTRCBAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView

	self:DoTRCBCellsAnim()
end

function TianshenRoadView:DoTRCBCellsAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView.ListCellRender
	self.node_list["cb_reward_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["cb_reward_list"]:SetActive(true)
        local list =  self.cb_reward_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyTRCBItemAnim(count)
        end
    end, tween_info.DelayDoTime, "TRCB_Cell_Tween")
end

-----------------------------------------------------------------------------------------------------

CBRewardRender = CBRewardRender or BaseClass(BaseRender)

function CBRewardRender:LoadCallBack()
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnReceiveClick, self))
	self.cb_reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
end

function CBRewardRender:__delete()
	if self.cb_reward_item_list then
		self.cb_reward_item_list:DeleteMe()
		self.cb_reward_item_list = nil
	end
end

function CBRewardRender:OnFlush()
	if not self.data then
		return
	end 
	
	local reward_list = SortTableKey(self.data.data.reward_item)
	self.cb_reward_item_list:SetDataList(reward_list)

	XUI.SetButtonEnabled(self.node_list.btn, self.data.state == TianShenRoadRewardState.KLQ)
	self.node_list.yilingqu:SetActive(self.data.state == TianShenRoadRewardState.YLQ)
	self.node_list.btn:SetActive(not self.data.is_rank and self.data.state ~= TianShenRoadRewardState.YLQ)
	self.node_list.djs:SetActive(self.data.is_rank)
	self.node_list.desc.text.text = self.data.data.condition_desc
	self.node_list.red_point:SetActive(self.data.state == TianShenRoadRewardState.KLQ)
 end

function CBRewardRender:OnBtnReceiveClick()
	if self.data.state == TianShenRoadRewardState.KLQ then
		TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_CHONGBANG,TIANSHEN_THEME_CHONGBANG_OP_TYPE.TYPE_ZHANLI_REWARD, self.data.data.ID)
	else
		TipWGCtrl.Instance:ShowSystemMsg(self.data.data.condition_desc)
	end
end

function CBRewardRender:PalyTRCBItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.TianshenRoadView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay2 * wait_index, "tscb_item_" .. wait_index)
end