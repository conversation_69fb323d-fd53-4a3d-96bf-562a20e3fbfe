--
TreasureBaodiTips1 = TreasureBaodiTips1 or BaseClass(SafeBaseView)

function TreasureBaodiTips1:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "TreasureBaodiTips1"
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_treasure_baodi_tips")
    self:SetMaskBg(true, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function TreasureBaodiTips1:ReleaseCallBack()
    if self.reward_item1 then
        self.reward_item1:DeleteMe()
        self.reward_item1 = nil
    end

    if self.reward_item2 then
        self.reward_item2:DeleteMe()
        self.reward_item2 = nil
    end
end

function TreasureBaodiTips1:LoadCallBack()
    self.reward_item1 = BaoDiRewardItem.New(self.node_list["reward_item1"])
    self.reward_item2 = BaoDiRewardItem.New(self.node_list["reward_item2"])
end

function TreasureBaodiTips1:OnFlush()
    local treasure_type = TreasureHuntWGCtrl.Instance:GetTreasureType()
    local need_times, cfg = TreasureHuntWGData.Instance:GetBaodiTimesAndCfg(treasure_type)
    local data_list = {}
    if not IsEmptyTable(cfg) and  not IsEmptyTable(cfg.reward_item) then
        for i=0,#cfg.reward_item do
            table.insert(data_list,cfg.reward_item[i])
        end
    end
    if #data_list > 0 then
        self.reward_item1:SetData(data_list[1])
        self.node_list["buttom_img"].transform.anchoredPosition = Vector2(-200,-242)
        self.node_list["buttom_img"].transform.localScale = Vector3(0.8, 0.8, 0.8)
        self.node_list.baodi_des_txt.text.text = string.format(Language.TreasureHunt.NeedText2, need_times) 
        if #data_list > 1 then
            self.reward_item2:SetData(data_list[2])
            self.node_list["buttom_img"].transform.anchoredPosition = Vector2(0,-242)
            self.node_list["buttom_img"].transform.localScale = Vector3(1, 1, 1)
            self.node_list.baodi_des_txt.text.text = string.format(Language.TreasureHunt.NeedText1, need_times) 
        end
    end
    
    self.node_list["reward_item1"]:SetActive(true)
    self.node_list["reward_item2"]:SetActive(#data_list > 1)

end


----------------BaoDiRewardItem--------------------------
BaoDiRewardItem = BaoDiRewardItem or BaseClass(BaseRender)

function BaoDiRewardItem:__init()
    self.item_cell = ItemCell.New(self.node_list["cell_pos"])
    self.item_cell:SetIsShowTips(false)

    self.role_model = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["role_model"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.S,
        can_drag = true,
    }
    
    self.role_model:SetRenderTexUI3DModel(display_data)
    -- local event_trigger = self.node_list["role_model"].event_trigger_listener
    -- self.role_model:SetUI3DModel(self.node_list["role_model"].transform, event_trigger, 1, nil, MODEL_CAMERA_TYPE.BASE)

    self.base_attr_list = {}
    local base_attr_obj = self.node_list["base_attribute"].transform
    for i=1,6 do
        local attr = {}
        attr.name = U3DObject(base_attr_obj:Find("label_"..i).gameObject,base_attr_obj:Find("label_"..i),self)
        attr.value = U3DObject(base_attr_obj:Find("label_"..i.."/label_right").gameObject,base_attr_obj:Find("label_"..i.."/label_right"),self)
        self.base_attr_list[i] = attr
    end

    self.xianpin_list = {}
    local xianpin_obj = self.node_list["xianpin_attribute"].transform
    for i=1,6 do
        local attr = {}
        attr.obj = U3DObject(xianpin_obj:Find("label_"..i).gameObject,xianpin_obj:Find("label_"..i),self)
        attr.value = U3DObject(xianpin_obj:Find("label_"..i.."/xianpin_label_"..i).gameObject,xianpin_obj:Find("label_"..i.."/xianpin_label_"..i),self)
        self.xianpin_list[i] = attr
    end
end

function BaoDiRewardItem:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    if self.role_model then
        self.role_model:DeleteMe()
        self.role_model = nil
    end

    self:CancelWeaponTween()
end

function BaoDiRewardItem:OnFlush()
    if not self.data then return end
    self.item_cell:SetData(self.data)

    -- 部分道具(随机礼包里的) 根据限制改变显示数据
    local is_special_gift = ItemWGData.Instance:IsSpecialRandGift(self.data.item_id)
    if is_special_gift then
        local old_itemid = self.data.item_id
        local change_data = ItemWGData.Instance:GetGiftItemConvertibleData(self.data)
        if change_data and old_itemid ~= change_data.item_id then
            self.data = change_data
        end
    end

    local item_cfg,big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg then
        self:ParseTopMsg(item_cfg,big_type)
        self:ParseBaseAttr(item_cfg,big_type)
        self:ParseButtomMsg(item_cfg,big_type)
        self:SetRoleModel(item_cfg,big_type)
    end
end

function BaoDiRewardItem:ParseTopMsg(item_cfg,big_type)
    local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    self.node_list["item_name"].text.text = name

    local leixing_str = ""
    local limit_prof_str = ""
    local sex_color = TIPS_COLOR.SOCRE
    local prof_color = TIPS_COLOR.SOCRE
    local display_type = item_cfg.is_display_role
    local limit_sex = item_cfg.limit_sex or 0
    local rich_type_text = Language.Common.ProfName[limit_sex][item_cfg.limit_prof]
    if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
        local rich_part_text = ""
        local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
        local sex_str = Language.Common.SexName[limit_sex] or ""
        rich_part_text = string.format(Language.Tip.Order, item_cfg.order) .. sex_str .. Language.Stone[equip_part]
        leixing_str = string.format(Language.Tip.ZhuangBeiLeiXing_1, sex_color, rich_part_text)

        local mix_limit_prof = EquipWGData.GetEquipProfLimit(equip_part, item_cfg.order)
        local is_limit_zhuanzhi_prof = item_cfg.is_limit_zhuanzhi_prof and tonumber(item_cfg.is_limit_zhuanzhi_prof) or -1
        if item_cfg.is_zhizun == 1 then
            rich_type_text = Language.Common.ProfName[limit_sex][item_cfg.limit_prof]
        elseif mix_limit_prof > 0 and item_cfg.limit_prof ~= 5 and is_limit_zhuanzhi_prof ~= 0 then
            rich_type_text = Language.Common.ProfName[limit_sex][mix_limit_prof * 10 + item_cfg.limit_prof]
        elseif mix_limit_prof > 0 and item_cfg.limit_prof == 5 and is_limit_zhuanzhi_prof == 1 then
            rich_type_text = rich_type_text .. string.format(Language.F2Tip.Zhuan, CommonDataManager.GetDaXie(mix_limit_prof))
        end
    elseif display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI then                        --神兵
        local display_type_str = Language.F2Tip.DisplayType[display_type] or ""
        leixing_str = string.format(Language.Tip.ZhuangBeiLeiXing_1, sex_color, display_type_str)
    end

    limit_prof_str = string.format(Language.Tip.ZhuangBeiProf, prof_color, rich_type_text)
    self.node_list["leixing"].text.text = leixing_str
    self.node_list["prof_text"].text.text = limit_prof_str
end

function BaoDiRewardItem:ParseBaseAttr(item_cfg,big_type)
    local base_attr = {}
    local display_type = item_cfg.is_display_role
    if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
        base_attr = AttributeMgr.GetAttributteByClass(item_cfg) --根据职业获取属性
     elseif display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI then                        --神兵
        local cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(item_cfg.id)
        local attr_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(cfg.part_type, cfg.index, 1)
        base_attr = AttributeMgr.GetAttributteByClass(attr_cfg)
    end
    local num = 1
    local sort_attribute = AttributeMgr.SortAttribute()
    for k,v in ipairs(sort_attribute) do
        if base_attr[v] and base_attr[v] > 0 then
            if self.base_attr_list[num] then
                self.base_attr_list[num].name:SetActive(true)
                local name = Language.Common.AttrName[v]
                self.base_attr_list[num].name.text.text = name
                if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
                    self.base_attr_list[num].value.text.text = base_attr[v] / 100 .. "%"
                else
                    self.base_attr_list[num].value.text.text = base_attr[v]
                end
                num = num + 1
            end
        end
    end
    if num <= #self.base_attr_list then
        for i = num,#self.base_attr_list do
            if self.base_attr_list[i] then
                self.base_attr_list[i].name:SetActive(false)
            end
        end
    end
    local cap_value = 0
    if not IsEmptyTable(base_attr) then
        cap_value = AttributeMgr.GetCapability(base_attr)
    end
    self.node_list["cap_value"].text.text = cap_value
end

function BaoDiRewardItem:ParseButtomMsg(item_cfg,big_type)
    self:HideButtomPanel()
    local display_type = item_cfg.is_display_role
    if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
        self:ParseXianPin()
    elseif display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI then                        --神兵
        self:ParseShenBingSkill(item_cfg)
    end
end

function BaoDiRewardItem:HideButtomPanel()
    self.node_list["xianpin_attribute"]:SetActive(false)
    self.node_list["skill_panel"]:SetActive(false)
end

function BaoDiRewardItem:ParseXianPin()
    local legend_attr_list = {}
    local legend_num = 0
    legend_num = self.data.param.star_level or 0
    legend_attr_list = EquipWGData.GetPreviewLegendAttr(self.data, legend_num)
    if legend_num < 1 then
        return
    end

    local info_list = {}
    local attr_list = {}
    for i,v in ipairs(legend_attr_list) do
        local temp = {}
        local per_value = 1
        if v.is_per and v.is_per ~= "" and v.is_per == 1 then
            per_value = 0.01
        end

        local desc1 = v.must_get and Language.Role.EquipDesc3 or Language.Role.EquipDesc1
        local desc1_color = v.must_get and COLOR3B.D_GLOD or COLOR3B.D_BLUE
        local desc2 = string.format(v.desc, v.value * per_value)
        temp.label = ToColorStr(desc1, desc1_color) .. ToColorStr(desc2, COLOR3B.WHITE)
        info_list.title_name = string.format(Language.Tip.RandXianShuXing, legend_num)
        attr_list[i] = temp
    end
    self.node_list["xianpin_attribute"]:SetActive(#attr_list > 0)
    if #attr_list > 0 then
        self.node_list["xianpin_dec"].text.text = info_list.title_name
        info_list.attr_list = attr_list
        for i=1,#self.xianpin_list do
            if attr_list[i] then
                self.xianpin_list[i].obj:SetActive(true)
                self.xianpin_list[i].value.text.text = attr_list[i].label
            else
                self.xianpin_list[i].obj:SetActive(false)
            end
        end
    end
end

function BaoDiRewardItem:ParseShenBingSkill(item_cfg)
    local cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(item_cfg.id)
    if cfg then
        local skill_cfg = NewAppearanceWGData.Instance:GetFashionSkillCfg(cfg.part_type, cfg.index, 1)
        if skill_cfg then
            self.node_list["skill_panel"]:SetActive(true)
            local bundle,asset = ResPath.GetSkillIconById(skill_cfg.skill_icon)
            self.node_list["skill_dec"].text.text = skill_cfg.skill_describe
            self.node_list["skill_icon"].image:LoadSprite(bundle, asset)
        end
    end
end

function BaoDiRewardItem:SetRoleModel(item_cfg,big_type)
    local display_type = item_cfg.is_display_role
    self:CancelWeaponTween()
    if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
        local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(item_cfg.id, true)
        self.node_list["role_model"]:SetActive(false)

        if bundle and asset then
            self.node_list["img_model"].raw_image:LoadSpriteAsync(bundle, asset, function ()
                self.node_list["img_model"]:SetActive(true)
            end)
        end

    elseif display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI then
        self.node_list["role_model"]:SetActive(true)
        self.node_list["img_model"]:SetActive(false)
        local attr_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(item_cfg.id)
        local weapon_res_index = attr_cfg.resouce
        local weapon_res_id, weapon_res_id_2 = AppearanceWGData.GetFashionWeaponIdByResViewId(weapon_res_index)
        local bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)
        self.role_model:SetMainAsset(bundle, asset)
        -- self:PlayWeaponTween()
    end

end

function BaoDiRewardItem:PlayWeaponTween()
    if not self.tween_weapon then
        local tween_root = self.node_list["role_model"].rect
        self.role_model_pos_x = tween_root.anchoredPosition.x
        self.tween_weapon = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 50, 1)
        self.tween_weapon:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
    else
        self.tween_weapon:Restart()
    end
end

function BaoDiRewardItem:CancelWeaponTween()
    if self.tween_weapon then
        self.tween_weapon:Kill()
        self.tween_weapon = nil
        local tween_root = self.node_list["role_model"].rect
        if tween_root and self.role_model_pos_x then
            tween_root.anchoredPosition = Vector2(self.role_model_pos_x, 0)
        end
    end
end