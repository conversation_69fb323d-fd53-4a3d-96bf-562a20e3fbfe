﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_PhysicsWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>gin<PERSON>taticLibs("Physics");
		<PERSON><PERSON>RegFunction("IgnoreCollision", IgnoreCollision);
		<PERSON><PERSON>unction("IgnoreLayerCollision", IgnoreLayerCollision);
		<PERSON><PERSON>ction("GetIgnoreLayerCollision", GetIgnoreLayerCollision);
		<PERSON><PERSON>unction("GetIgnoreCollision", GetIgnoreCollision);
		<PERSON><PERSON>Function("Raycast", Raycast);
		<PERSON><PERSON>RegFunction("Linecast", Linecast);
		<PERSON><PERSON>Function("CapsuleCast", CapsuleCast);
		<PERSON><PERSON>ction("SphereCast", SphereCast);
		L.RegFunction("BoxCast", BoxCast);
		<PERSON>.RegFunction("RaycastAll", RaycastAll);
		<PERSON><PERSON>Function("RaycastNonAlloc", RaycastNonAlloc);
		<PERSON><PERSON>unction("CapsuleCastAll", CapsuleCastAll);
		<PERSON><PERSON>("SphereCastAll", SphereCastAll);
		<PERSON><PERSON>("OverlapCapsule", OverlapCapsule);
		L.RegFunction("OverlapSphere", OverlapSphere);
		L.RegFunction("Simulate", Simulate);
		L.RegFunction("SyncTransforms", SyncTransforms);
		L.RegFunction("ComputePenetration", ComputePenetration);
		L.RegFunction("ClosestPoint", ClosestPoint);
		L.RegFunction("OverlapSphereNonAlloc", OverlapSphereNonAlloc);
		L.RegFunction("CheckSphere", CheckSphere);
		L.RegFunction("CapsuleCastNonAlloc", CapsuleCastNonAlloc);
		L.RegFunction("SphereCastNonAlloc", SphereCastNonAlloc);
		L.RegFunction("CheckCapsule", CheckCapsule);
		L.RegFunction("CheckBox", CheckBox);
		L.RegFunction("OverlapBox", OverlapBox);
		L.RegFunction("OverlapBoxNonAlloc", OverlapBoxNonAlloc);
		L.RegFunction("BoxCastNonAlloc", BoxCastNonAlloc);
		L.RegFunction("BoxCastAll", BoxCastAll);
		L.RegFunction("OverlapCapsuleNonAlloc", OverlapCapsuleNonAlloc);
		L.RegFunction("RebuildBroadphaseRegions", RebuildBroadphaseRegions);
		L.RegFunction("BakeMesh", BakeMesh);
		L.RegConstant("IgnoreRaycastLayer", 4);
		L.RegConstant("DefaultRaycastLayers", -5);
		L.RegConstant("AllLayers", -1);
		L.RegVar("gravity", get_gravity, set_gravity);
		L.RegVar("defaultContactOffset", get_defaultContactOffset, set_defaultContactOffset);
		L.RegVar("sleepThreshold", get_sleepThreshold, set_sleepThreshold);
		L.RegVar("queriesHitTriggers", get_queriesHitTriggers, set_queriesHitTriggers);
		L.RegVar("queriesHitBackfaces", get_queriesHitBackfaces, set_queriesHitBackfaces);
		L.RegVar("bounceThreshold", get_bounceThreshold, set_bounceThreshold);
		L.RegVar("defaultMaxDepenetrationVelocity", get_defaultMaxDepenetrationVelocity, set_defaultMaxDepenetrationVelocity);
		L.RegVar("defaultSolverIterations", get_defaultSolverIterations, set_defaultSolverIterations);
		L.RegVar("defaultSolverVelocityIterations", get_defaultSolverVelocityIterations, set_defaultSolverVelocityIterations);
		L.RegVar("defaultMaxAngularSpeed", get_defaultMaxAngularSpeed, set_defaultMaxAngularSpeed);
		L.RegVar("improvedPatchFriction", get_improvedPatchFriction, set_improvedPatchFriction);
		L.RegVar("defaultPhysicsScene", get_defaultPhysicsScene, null);
		L.RegVar("autoSimulation", get_autoSimulation, set_autoSimulation);
		L.RegVar("autoSyncTransforms", get_autoSyncTransforms, set_autoSyncTransforms);
		L.RegVar("reuseCollisionCallbacks", get_reuseCollisionCallbacks, set_reuseCollisionCallbacks);
		L.RegVar("interCollisionDistance", get_interCollisionDistance, set_interCollisionDistance);
		L.RegVar("interCollisionStiffness", get_interCollisionStiffness, set_interCollisionStiffness);
		L.RegVar("interCollisionSettingsToggle", get_interCollisionSettingsToggle, set_interCollisionSettingsToggle);
		L.RegVar("clothGravity", get_clothGravity, set_clothGravity);
		L.RegVar("ContactModifyEvent", get_ContactModifyEvent, set_ContactModifyEvent);
		L.RegVar("ContactModifyEventCCD", get_ContactModifyEventCCD, set_ContactModifyEventCCD);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IgnoreCollision(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Collider arg0 = (UnityEngine.Collider)ToLua.CheckObject<UnityEngine.Collider>(L, 1);
				UnityEngine.Collider arg1 = (UnityEngine.Collider)ToLua.CheckObject<UnityEngine.Collider>(L, 2);
				UnityEngine.Physics.IgnoreCollision(arg0, arg1);
				return 0;
			}
			else if (count == 3)
			{
				UnityEngine.Collider arg0 = (UnityEngine.Collider)ToLua.CheckObject<UnityEngine.Collider>(L, 1);
				UnityEngine.Collider arg1 = (UnityEngine.Collider)ToLua.CheckObject<UnityEngine.Collider>(L, 2);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				UnityEngine.Physics.IgnoreCollision(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.IgnoreCollision");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IgnoreLayerCollision(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Physics.IgnoreLayerCollision(arg0, arg1);
				return 0;
			}
			else if (count == 3)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				UnityEngine.Physics.IgnoreLayerCollision(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.IgnoreLayerCollision");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetIgnoreLayerCollision(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			bool o = UnityEngine.Physics.GetIgnoreLayerCollision(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetIgnoreCollision(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Collider arg0 = (UnityEngine.Collider)ToLua.CheckObject<UnityEngine.Collider>(L, 1);
			UnityEngine.Collider arg1 = (UnityEngine.Collider)ToLua.CheckObject<UnityEngine.Collider>(L, 2);
			bool o = UnityEngine.Physics.GetIgnoreCollision(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Raycast(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				bool o = UnityEngine.Physics.Raycast(arg0);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<UnityEngine.Ray, float>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				bool o = UnityEngine.Physics.Raycast(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				bool o = UnityEngine.Physics.Raycast(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<UnityEngine.Ray, LuaInterface.LuaOut<UnityEngine.RaycastHit>>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				UnityEngine.RaycastHit arg1;
				bool o = UnityEngine.Physics.Raycast(arg0, out arg1);
				LuaDLL.lua_pushboolean(L, o);
				if (o) ToLua.Push(L, arg1); else LuaDLL.lua_pushnil(L);
				return 2;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Ray, float, int>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				int arg2 = (int)LuaDLL.lua_tonumber(L, 3);
				bool o = UnityEngine.Physics.Raycast(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, float>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				bool o = UnityEngine.Physics.Raycast(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Ray, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				UnityEngine.RaycastHit arg1;
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				bool o = UnityEngine.Physics.Raycast(arg0, out arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				if (o) ToLua.Push(L, arg1); else LuaDLL.lua_pushnil(L);
				return 2;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, LuaInterface.LuaOut<UnityEngine.RaycastHit>>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit arg2;
				bool o = UnityEngine.Physics.Raycast(arg0, arg1, out arg2);
				LuaDLL.lua_pushboolean(L, o);
				if (o) ToLua.Push(L, arg2); else LuaDLL.lua_pushnil(L);
				return 2;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Ray, float, int, UnityEngine.QueryTriggerInteraction>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				int arg2 = (int)LuaDLL.lua_tonumber(L, 3);
				UnityEngine.QueryTriggerInteraction arg3 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 4);
				bool o = UnityEngine.Physics.Raycast(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, float, int>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				bool o = UnityEngine.Physics.Raycast(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Ray, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float, int>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				UnityEngine.RaycastHit arg1;
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				bool o = UnityEngine.Physics.Raycast(arg0, out arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				if (o) ToLua.Push(L, arg1); else LuaDLL.lua_pushnil(L);
				return 2;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit arg2;
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				bool o = UnityEngine.Physics.Raycast(arg0, arg1, out arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				if (o) ToLua.Push(L, arg2); else LuaDLL.lua_pushnil(L);
				return 2;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, float, int, UnityEngine.QueryTriggerInteraction>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 5);
				bool o = UnityEngine.Physics.Raycast(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Ray, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float, int, UnityEngine.QueryTriggerInteraction>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				UnityEngine.RaycastHit arg1;
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 5);
				bool o = UnityEngine.Physics.Raycast(arg0, out arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				if (o) ToLua.Push(L, arg1); else LuaDLL.lua_pushnil(L);
				return 2;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float, int>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit arg2;
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				bool o = UnityEngine.Physics.Raycast(arg0, arg1, out arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				if (o) ToLua.Push(L, arg2); else LuaDLL.lua_pushnil(L);
				return 2;
			}
			else if (count == 6)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit arg2;
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.QueryTriggerInteraction arg5 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 6, typeof(UnityEngine.QueryTriggerInteraction));
				bool o = UnityEngine.Physics.Raycast(arg0, arg1, out arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushboolean(L, o);
				if (o) ToLua.Push(L, arg2); else LuaDLL.lua_pushnil(L);
				return 2;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.Raycast");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Linecast(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				bool o = UnityEngine.Physics.Linecast(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<int>(L, 3))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				int arg2 = (int)LuaDLL.lua_tonumber(L, 3);
				bool o = UnityEngine.Physics.Linecast(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>>(L, 3))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit arg2;
				bool o = UnityEngine.Physics.Linecast(arg0, arg1, out arg2);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg2);
				return 2;
			}
			else if (count == 4 && TypeChecker.CheckTypes<int, UnityEngine.QueryTriggerInteraction>(L, 3))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				int arg2 = (int)LuaDLL.lua_tonumber(L, 3);
				UnityEngine.QueryTriggerInteraction arg3 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 4);
				bool o = UnityEngine.Physics.Linecast(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>, int>(L, 3))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit arg2;
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				bool o = UnityEngine.Physics.Linecast(arg0, arg1, out arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg2);
				return 2;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit arg2;
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 5, typeof(UnityEngine.QueryTriggerInteraction));
				bool o = UnityEngine.Physics.Linecast(arg0, arg1, out arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg2);
				return 2;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.Linecast");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CapsuleCast(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				bool o = UnityEngine.Physics.CapsuleCast(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<float>(L, 5))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				bool o = UnityEngine.Physics.CapsuleCast(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>>(L, 5))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				UnityEngine.RaycastHit arg4;
				bool o = UnityEngine.Physics.CapsuleCast(arg0, arg1, arg2, arg3, out arg4);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg4);
				return 2;
			}
			else if (count == 6 && TypeChecker.CheckTypes<float, int>(L, 5))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				int arg5 = (int)LuaDLL.lua_tonumber(L, 6);
				bool o = UnityEngine.Physics.CapsuleCast(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 6 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>, float>(L, 5))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				UnityEngine.RaycastHit arg4;
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				bool o = UnityEngine.Physics.CapsuleCast(arg0, arg1, arg2, arg3, out arg4, arg5);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg4);
				return 2;
			}
			else if (count == 7 && TypeChecker.CheckTypes<float, int, UnityEngine.QueryTriggerInteraction>(L, 5))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				int arg5 = (int)LuaDLL.lua_tonumber(L, 6);
				UnityEngine.QueryTriggerInteraction arg6 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 7);
				bool o = UnityEngine.Physics.CapsuleCast(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 7 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>, float, int>(L, 5))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				UnityEngine.RaycastHit arg4;
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				int arg6 = (int)LuaDLL.lua_tonumber(L, 7);
				bool o = UnityEngine.Physics.CapsuleCast(arg0, arg1, arg2, arg3, out arg4, arg5, arg6);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg4);
				return 2;
			}
			else if (count == 8)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				UnityEngine.RaycastHit arg4;
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				int arg6 = (int)LuaDLL.luaL_checknumber(L, 7);
				UnityEngine.QueryTriggerInteraction arg7 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 8, typeof(UnityEngine.QueryTriggerInteraction));
				bool o = UnityEngine.Physics.CapsuleCast(arg0, arg1, arg2, arg3, out arg4, arg5, arg6, arg7);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg4);
				return 2;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.CapsuleCast");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SphereCast(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<float>(L, 3))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>>(L, 3))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.RaycastHit arg2;
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, out arg2);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg2);
				return 2;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Ray, float, float, int>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Ray, float, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.RaycastHit arg2;
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, out arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg2);
				return 2;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, UnityEngine.Vector3, LuaInterface.LuaOut<UnityEngine.RaycastHit>>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit arg3;
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, arg2, out arg3);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg3);
				return 2;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Ray, float, float, int, UnityEngine.QueryTriggerInteraction>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 5);
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Ray, float, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float, int>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.RaycastHit arg2;
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, out arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg2);
				return 2;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, UnityEngine.Vector3, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit arg3;
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, arg2, out arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg3);
				return 2;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UnityEngine.Ray, float, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float, int, UnityEngine.QueryTriggerInteraction>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.RaycastHit arg2;
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				UnityEngine.QueryTriggerInteraction arg5 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 6);
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, out arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg2);
				return 2;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, UnityEngine.Vector3, LuaInterface.LuaOut<UnityEngine.RaycastHit>, float, int>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit arg3;
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				int arg5 = (int)LuaDLL.lua_tonumber(L, 6);
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, arg2, out arg3, arg4, arg5);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg3);
				return 2;
			}
			else if (count == 7)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit arg3;
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				int arg5 = (int)LuaDLL.luaL_checknumber(L, 6);
				UnityEngine.QueryTriggerInteraction arg6 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 7, typeof(UnityEngine.QueryTriggerInteraction));
				bool o = UnityEngine.Physics.SphereCast(arg0, arg1, arg2, out arg3, arg4, arg5, arg6);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg3);
				return 2;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.SphereCast");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BoxCast(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Quaternion>(L, 4))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>>(L, 4))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit arg3;
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2, out arg3);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg3);
				return 2;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Quaternion, float>(L, 4))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>, UnityEngine.Quaternion>(L, 4))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit arg3;
				UnityEngine.Quaternion arg4 = ToLua.ToQuaternion(L, 5);
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2, out arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg3);
				return 2;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UnityEngine.Quaternion, float, int>(L, 4))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				int arg5 = (int)LuaDLL.lua_tonumber(L, 6);
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 6 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>, UnityEngine.Quaternion, float>(L, 4))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit arg3;
				UnityEngine.Quaternion arg4 = ToLua.ToQuaternion(L, 5);
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2, out arg3, arg4, arg5);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg3);
				return 2;
			}
			else if (count == 7 && TypeChecker.CheckTypes<UnityEngine.Quaternion, float, int, UnityEngine.QueryTriggerInteraction>(L, 4))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				int arg5 = (int)LuaDLL.lua_tonumber(L, 6);
				UnityEngine.QueryTriggerInteraction arg6 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 7);
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 7 && TypeChecker.CheckTypes<LuaInterface.LuaOut<UnityEngine.RaycastHit>, UnityEngine.Quaternion, float, int>(L, 4))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit arg3;
				UnityEngine.Quaternion arg4 = ToLua.ToQuaternion(L, 5);
				float arg5 = (float)LuaDLL.lua_tonumber(L, 6);
				int arg6 = (int)LuaDLL.lua_tonumber(L, 7);
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2, out arg3, arg4, arg5, arg6);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg3);
				return 2;
			}
			else if (count == 8)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit arg3;
				UnityEngine.Quaternion arg4 = ToLua.ToQuaternion(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				int arg6 = (int)LuaDLL.luaL_checknumber(L, 7);
				UnityEngine.QueryTriggerInteraction arg7 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 8, typeof(UnityEngine.QueryTriggerInteraction));
				bool o = UnityEngine.Physics.BoxCast(arg0, arg1, arg2, out arg3, arg4, arg5, arg6, arg7);
				LuaDLL.lua_pushboolean(L, o);
				ToLua.Push(L, arg3);
				return 2;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.BoxCast");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RaycastAll(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.RaycastAll(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<UnityEngine.Ray, float>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.RaycastAll(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.RaycastAll(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Ray, float, int>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				int arg2 = (int)LuaDLL.lua_tonumber(L, 3);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.RaycastAll(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, float>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.RaycastAll(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Ray, float, int, UnityEngine.QueryTriggerInteraction>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				int arg2 = (int)LuaDLL.lua_tonumber(L, 3);
				UnityEngine.QueryTriggerInteraction arg3 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 4);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.RaycastAll(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, float, int>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.RaycastAll(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 5, typeof(UnityEngine.QueryTriggerInteraction));
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.RaycastAll(arg0, arg1, arg2, arg3, arg4);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.RaycastAll");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RaycastNonAlloc(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				UnityEngine.RaycastHit[] arg1 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 2);
				int o = UnityEngine.Physics.RaycastNonAlloc(arg0, arg1);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Ray, UnityEngine.RaycastHit[], float>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				UnityEngine.RaycastHit[] arg1 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int o = UnityEngine.Physics.RaycastNonAlloc(arg0, arg1, arg2);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, UnityEngine.RaycastHit[]>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit[] arg2 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 3);
				int o = UnityEngine.Physics.RaycastNonAlloc(arg0, arg1, arg2);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Ray, UnityEngine.RaycastHit[], float, int>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				UnityEngine.RaycastHit[] arg1 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				int o = UnityEngine.Physics.RaycastNonAlloc(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, UnityEngine.RaycastHit[], float>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit[] arg2 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int o = UnityEngine.Physics.RaycastNonAlloc(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Ray, UnityEngine.RaycastHit[], float, int, UnityEngine.QueryTriggerInteraction>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				UnityEngine.RaycastHit[] arg1 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 5);
				int o = UnityEngine.Physics.RaycastNonAlloc(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Vector3, UnityEngine.Vector3, UnityEngine.RaycastHit[], float, int>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit[] arg2 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				int o = UnityEngine.Physics.RaycastNonAlloc(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 6)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.RaycastHit[] arg2 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.QueryTriggerInteraction arg5 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 6, typeof(UnityEngine.QueryTriggerInteraction));
				int o = UnityEngine.Physics.RaycastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.RaycastNonAlloc");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CapsuleCastAll(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.CapsuleCastAll(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.CapsuleCastAll(arg0, arg1, arg2, arg3, arg4);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 6)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				int arg5 = (int)LuaDLL.luaL_checknumber(L, 6);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.CapsuleCastAll(arg0, arg1, arg2, arg3, arg4, arg5);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 7)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				int arg5 = (int)LuaDLL.luaL_checknumber(L, 6);
				UnityEngine.QueryTriggerInteraction arg6 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 7, typeof(UnityEngine.QueryTriggerInteraction));
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.CapsuleCastAll(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.CapsuleCastAll");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SphereCastAll(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.SphereCastAll(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Ray, float, float>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.SphereCastAll(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, UnityEngine.Vector3>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.SphereCastAll(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Ray, float, float, int>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.SphereCastAll(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, UnityEngine.Vector3, float>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.SphereCastAll(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Ray, float, float, int, UnityEngine.QueryTriggerInteraction>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				float arg2 = (float)LuaDLL.lua_tonumber(L, 3);
				int arg3 = (int)LuaDLL.lua_tonumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 5);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.SphereCastAll(arg0, arg1, arg2, arg3, arg4);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, UnityEngine.Vector3, float, int>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.SphereCastAll(arg0, arg1, arg2, arg3, arg4);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 6)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.QueryTriggerInteraction arg5 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 6, typeof(UnityEngine.QueryTriggerInteraction));
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.SphereCastAll(arg0, arg1, arg2, arg3, arg4, arg5);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.SphereCastAll");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OverlapCapsule(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapCapsule(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapCapsule(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 5, typeof(UnityEngine.QueryTriggerInteraction));
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapCapsule(arg0, arg1, arg2, arg3, arg4);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.OverlapCapsule");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OverlapSphere(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapSphere(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapSphere(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.QueryTriggerInteraction arg3 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 4, typeof(UnityEngine.QueryTriggerInteraction));
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapSphere(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.OverlapSphere");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Simulate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			UnityEngine.Physics.Simulate(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SyncTransforms(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			UnityEngine.Physics.SyncTransforms();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ComputePenetration(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 8);
			UnityEngine.Collider arg0 = (UnityEngine.Collider)ToLua.CheckObject<UnityEngine.Collider>(L, 1);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
			UnityEngine.Quaternion arg2 = ToLua.ToQuaternion(L, 3);
			UnityEngine.Collider arg3 = (UnityEngine.Collider)ToLua.CheckObject<UnityEngine.Collider>(L, 4);
			UnityEngine.Vector3 arg4 = ToLua.ToVector3(L, 5);
			UnityEngine.Quaternion arg5 = ToLua.ToQuaternion(L, 6);
			UnityEngine.Vector3 arg6;
			float arg7;
			bool o = UnityEngine.Physics.ComputePenetration(arg0, arg1, arg2, arg3, arg4, arg5, out arg6, out arg7);
			LuaDLL.lua_pushboolean(L, o);
			ToLua.Push(L, arg6);
			LuaDLL.lua_pushnumber(L, arg7);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClosestPoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
			UnityEngine.Collider arg1 = (UnityEngine.Collider)ToLua.CheckObject<UnityEngine.Collider>(L, 2);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
			UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
			UnityEngine.Vector3 o = UnityEngine.Physics.ClosestPoint(arg0, arg1, arg2, arg3);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OverlapSphereNonAlloc(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Collider[] arg2 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 3);
				int o = UnityEngine.Physics.OverlapSphereNonAlloc(arg0, arg1, arg2);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Collider[] arg2 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				int o = UnityEngine.Physics.OverlapSphereNonAlloc(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Collider[] arg2 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 5, typeof(UnityEngine.QueryTriggerInteraction));
				int o = UnityEngine.Physics.OverlapSphereNonAlloc(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.OverlapSphereNonAlloc");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CheckSphere(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				bool o = UnityEngine.Physics.CheckSphere(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 3);
				bool o = UnityEngine.Physics.CheckSphere(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.QueryTriggerInteraction arg3 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 4, typeof(UnityEngine.QueryTriggerInteraction));
				bool o = UnityEngine.Physics.CheckSphere(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.CheckSphere");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CapsuleCastNonAlloc(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				UnityEngine.RaycastHit[] arg4 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 5);
				int o = UnityEngine.Physics.CapsuleCastNonAlloc(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 6)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				UnityEngine.RaycastHit[] arg4 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				int o = UnityEngine.Physics.CapsuleCastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 7)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				UnityEngine.RaycastHit[] arg4 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				int arg6 = (int)LuaDLL.luaL_checknumber(L, 7);
				int o = UnityEngine.Physics.CapsuleCastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 8)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 4);
				UnityEngine.RaycastHit[] arg4 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				int arg6 = (int)LuaDLL.luaL_checknumber(L, 7);
				UnityEngine.QueryTriggerInteraction arg7 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 8, typeof(UnityEngine.QueryTriggerInteraction));
				int o = UnityEngine.Physics.CapsuleCastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.CapsuleCastNonAlloc");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SphereCastNonAlloc(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.RaycastHit[] arg2 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 3);
				int o = UnityEngine.Physics.SphereCastNonAlloc(arg0, arg1, arg2);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Ray, float, UnityEngine.RaycastHit[], float>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.RaycastHit[] arg2 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int o = UnityEngine.Physics.SphereCastNonAlloc(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 4 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, UnityEngine.Vector3, UnityEngine.RaycastHit[]>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] arg3 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 4);
				int o = UnityEngine.Physics.SphereCastNonAlloc(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Ray, float, UnityEngine.RaycastHit[], float, int>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.RaycastHit[] arg2 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				int o = UnityEngine.Physics.SphereCastNonAlloc(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 5 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, UnityEngine.Vector3, UnityEngine.RaycastHit[], float>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] arg3 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 4);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				int o = UnityEngine.Physics.SphereCastNonAlloc(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UnityEngine.Ray, float, UnityEngine.RaycastHit[], float, int, UnityEngine.QueryTriggerInteraction>(L, 1))
			{
				UnityEngine.Ray arg0 = ToLua.ToRay(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.RaycastHit[] arg2 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 3);
				float arg3 = (float)LuaDLL.lua_tonumber(L, 4);
				int arg4 = (int)LuaDLL.lua_tonumber(L, 5);
				UnityEngine.QueryTriggerInteraction arg5 = (UnityEngine.QueryTriggerInteraction)ToLua.ToObject(L, 6);
				int o = UnityEngine.Physics.SphereCastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UnityEngine.Vector3, float, UnityEngine.Vector3, UnityEngine.RaycastHit[], float, int>(L, 1))
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] arg3 = ToLua.ToStructArray<UnityEngine.RaycastHit>(L, 4);
				float arg4 = (float)LuaDLL.lua_tonumber(L, 5);
				int arg5 = (int)LuaDLL.lua_tonumber(L, 6);
				int o = UnityEngine.Physics.SphereCastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 7)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] arg3 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 4);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				int arg5 = (int)LuaDLL.luaL_checknumber(L, 6);
				UnityEngine.QueryTriggerInteraction arg6 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 7, typeof(UnityEngine.QueryTriggerInteraction));
				int o = UnityEngine.Physics.SphereCastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.SphereCastNonAlloc");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CheckCapsule(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				bool o = UnityEngine.Physics.CheckCapsule(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				bool o = UnityEngine.Physics.CheckCapsule(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 5, typeof(UnityEngine.QueryTriggerInteraction));
				bool o = UnityEngine.Physics.CheckCapsule(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.CheckCapsule");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CheckBox(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				bool o = UnityEngine.Physics.CheckBox(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Quaternion arg2 = ToLua.ToQuaternion(L, 3);
				bool o = UnityEngine.Physics.CheckBox(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Quaternion arg2 = ToLua.ToQuaternion(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				bool o = UnityEngine.Physics.CheckBox(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Quaternion arg2 = ToLua.ToQuaternion(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 5, typeof(UnityEngine.QueryTriggerInteraction));
				bool o = UnityEngine.Physics.CheckBox(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.CheckBox");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OverlapBox(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapBox(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Quaternion arg2 = ToLua.ToQuaternion(L, 3);
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapBox(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Quaternion arg2 = ToLua.ToQuaternion(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapBox(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Quaternion arg2 = ToLua.ToQuaternion(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				UnityEngine.QueryTriggerInteraction arg4 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 5, typeof(UnityEngine.QueryTriggerInteraction));
				UnityEngine.Collider[] o = UnityEngine.Physics.OverlapBox(arg0, arg1, arg2, arg3, arg4);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.OverlapBox");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OverlapBoxNonAlloc(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Collider[] arg2 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 3);
				int o = UnityEngine.Physics.OverlapBoxNonAlloc(arg0, arg1, arg2);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Collider[] arg2 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				int o = UnityEngine.Physics.OverlapBoxNonAlloc(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Collider[] arg2 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				int o = UnityEngine.Physics.OverlapBoxNonAlloc(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 6)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Collider[] arg2 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.QueryTriggerInteraction arg5 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 6, typeof(UnityEngine.QueryTriggerInteraction));
				int o = UnityEngine.Physics.OverlapBoxNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.OverlapBoxNonAlloc");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BoxCastNonAlloc(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] arg3 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 4);
				int o = UnityEngine.Physics.BoxCastNonAlloc(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] arg3 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 4);
				UnityEngine.Quaternion arg4 = ToLua.ToQuaternion(L, 5);
				int o = UnityEngine.Physics.BoxCastNonAlloc(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 6)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] arg3 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 4);
				UnityEngine.Quaternion arg4 = ToLua.ToQuaternion(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				int o = UnityEngine.Physics.BoxCastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 7)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] arg3 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 4);
				UnityEngine.Quaternion arg4 = ToLua.ToQuaternion(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				int arg6 = (int)LuaDLL.luaL_checknumber(L, 7);
				int o = UnityEngine.Physics.BoxCastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 8)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] arg3 = ToLua.CheckStructArray<UnityEngine.RaycastHit>(L, 4);
				UnityEngine.Quaternion arg4 = ToLua.ToQuaternion(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				int arg6 = (int)LuaDLL.luaL_checknumber(L, 7);
				UnityEngine.QueryTriggerInteraction arg7 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 8, typeof(UnityEngine.QueryTriggerInteraction));
				int o = UnityEngine.Physics.BoxCastNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.BoxCastNonAlloc");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BoxCastAll(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.BoxCastAll(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.BoxCastAll(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.BoxCastAll(arg0, arg1, arg2, arg3, arg4);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 6)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				int arg5 = (int)LuaDLL.luaL_checknumber(L, 6);
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.BoxCastAll(arg0, arg1, arg2, arg3, arg4, arg5);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 7)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				UnityEngine.Quaternion arg3 = ToLua.ToQuaternion(L, 4);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				int arg5 = (int)LuaDLL.luaL_checknumber(L, 6);
				UnityEngine.QueryTriggerInteraction arg6 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 7, typeof(UnityEngine.QueryTriggerInteraction));
				UnityEngine.RaycastHit[] o = UnityEngine.Physics.BoxCastAll(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.BoxCastAll");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OverlapCapsuleNonAlloc(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Collider[] arg3 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 4);
				int o = UnityEngine.Physics.OverlapCapsuleNonAlloc(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 5)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Collider[] arg3 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				int o = UnityEngine.Physics.OverlapCapsuleNonAlloc(arg0, arg1, arg2, arg3, arg4);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 6)
			{
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Collider[] arg3 = ToLua.CheckObjectArray<UnityEngine.Collider>(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.QueryTriggerInteraction arg5 = (UnityEngine.QueryTriggerInteraction)ToLua.CheckObject(L, 6, typeof(UnityEngine.QueryTriggerInteraction));
				int o = UnityEngine.Physics.OverlapCapsuleNonAlloc(arg0, arg1, arg2, arg3, arg4, arg5);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Physics.OverlapCapsuleNonAlloc");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RebuildBroadphaseRegions(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Bounds arg0 = ToLua.ToBounds(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.RebuildBroadphaseRegions(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BakeMesh(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Physics.BakeMesh(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_gravity(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UnityEngine.Physics.gravity);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultContactOffset(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Physics.defaultContactOffset);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_sleepThreshold(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Physics.sleepThreshold);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_queriesHitTriggers(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Physics.queriesHitTriggers);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_queriesHitBackfaces(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Physics.queriesHitBackfaces);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bounceThreshold(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Physics.bounceThreshold);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultMaxDepenetrationVelocity(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Physics.defaultMaxDepenetrationVelocity);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultSolverIterations(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.Physics.defaultSolverIterations);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultSolverVelocityIterations(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.Physics.defaultSolverVelocityIterations);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultMaxAngularSpeed(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Physics.defaultMaxAngularSpeed);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_improvedPatchFriction(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Physics.improvedPatchFriction);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultPhysicsScene(IntPtr L)
	{
		try
		{
			ToLua.PushValue(L, UnityEngine.Physics.defaultPhysicsScene);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoSimulation(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Physics.autoSimulation);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoSyncTransforms(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Physics.autoSyncTransforms);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_reuseCollisionCallbacks(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Physics.reuseCollisionCallbacks);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_interCollisionDistance(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Physics.interCollisionDistance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_interCollisionStiffness(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Physics.interCollisionStiffness);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_interCollisionSettingsToggle(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, UnityEngine.Physics.interCollisionSettingsToggle);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_clothGravity(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UnityEngine.Physics.clothGravity);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ContactModifyEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ContactModifyEventCCD(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_gravity(IntPtr L)
	{
		try
		{
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Physics.gravity = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultContactOffset(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.defaultContactOffset = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_sleepThreshold(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.sleepThreshold = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_queriesHitTriggers(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Physics.queriesHitTriggers = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_queriesHitBackfaces(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Physics.queriesHitBackfaces = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_bounceThreshold(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.bounceThreshold = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultMaxDepenetrationVelocity(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.defaultMaxDepenetrationVelocity = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultSolverIterations(IntPtr L)
	{
		try
		{
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.defaultSolverIterations = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultSolverVelocityIterations(IntPtr L)
	{
		try
		{
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.defaultSolverVelocityIterations = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultMaxAngularSpeed(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.defaultMaxAngularSpeed = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_improvedPatchFriction(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Physics.improvedPatchFriction = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoSimulation(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Physics.autoSimulation = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoSyncTransforms(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Physics.autoSyncTransforms = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_reuseCollisionCallbacks(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Physics.reuseCollisionCallbacks = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_interCollisionDistance(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.interCollisionDistance = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_interCollisionStiffness(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Physics.interCollisionStiffness = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_interCollisionSettingsToggle(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.Physics.interCollisionSettingsToggle = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_clothGravity(IntPtr L)
	{
		try
		{
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Physics.clothGravity = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ContactModifyEvent(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Physics.ContactModifyEvent' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Physics'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>> ev = (System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>)arg0.func;
				UnityEngine.Physics.ContactModifyEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>> ev = (System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>)arg0.func;
				UnityEngine.Physics.ContactModifyEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ContactModifyEventCCD(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Physics.ContactModifyEventCCD' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Physics'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>> ev = (System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>)arg0.func;
				UnityEngine.Physics.ContactModifyEventCCD += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>> ev = (System.Action<UnityEngine.PhysicsScene,Unity.Collections.NativeArray<UnityEngine.ModifiableContactPair>>)arg0.func;
				UnityEngine.Physics.ContactModifyEventCCD -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

