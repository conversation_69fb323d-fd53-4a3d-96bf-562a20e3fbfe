
MainuiIcon = MainuiIcon or BaseClass(BaseRender)

function MainuiIcon:__init(instance)
	if nil == self.root_node then
		local bundle, asset = ResPath.GetWidgets("MainuiIcon")
		local u3dobj = U3DObject(ResPoolMgr:TryGetGameObject(bundle, asset))
        if self.instance_parent then
            u3dobj.transform:SetParent(self.instance_parent)
        end
		self:SetInstance(u3dobj)
		self.is_use_objpool = true
	end

	if instance then
		self:SetInstanceParent(instance)
	end

	self.data = {}
	self.handler = nil

	self.img_icon = self.node_list["ImgIcon"]
	self.button_icon = self.node_list["MainUiIcon"]
	self.button_icon.button:AddClickListener(BindTool.Bind(self.OnClick, self))

	self.is_hide_pop = false
end

function MainuiIcon:__delete()
	self.data = {}
	self.handler = nil
	self.img_icon = nil
	self.button_icon = nil
	self.is_show_answer_tip = nil
	self.is_click_any = nil
	self.is_hide_pop = false

	if self.is_use_objpool and not self:IsNil() then
		ResPoolMgr:Release(self.view.gameObject)
	end
end

-- 改变点击回调
function MainuiIcon:SetSelectCallBack(handler)
	self.handler = handler
end

function MainuiIcon:OnClick()
	if nil == next(self.data) then
		return
	end
	
	local flag = false
	if nil ~= self.data.callback then
		flag = self.data.callback(self.data.callback_param)
	end

	if not flag then
		MainuiWGCtrl.Instance:RemoveTipIconByIconObj(self.data.tip_type)
	end
	if self.data.tip_type == MAINUI_TIP_TYPE.CHAT_GUILD then
		self.node_list.num_bg:SetActive(false)
		self.node_list["qipao_root"]:SetActive(false)
		self.is_click_any = true
	elseif self.data.tip_type == MAINUI_TIP_TYPE.Market_Auction then
		self.node_list["qipao_root"]:SetActive(false)
		MarketWGData.Instance:SetAuctionUpQiPaoActive(false)
	elseif self.data.tip_type == MAINUI_TIP_TYPE.Market_Auction_Beyond then
		self.node_list["qipao_root"]:SetActive(false)
		self.is_hide_pop = true
	elseif self.data.tip_type == MAINUI_TIP_TYPE.BOSS_XIEZHU then
		self.node_list["qipao_root"]:SetActive(false)
	end
	self:SetQiPaoMoveAnimtion()
end

function MainuiIcon:SetData(data)
	BaseRender.SetData(self, data)
end

function MainuiIcon:OnFlush()
	if nil == Language.MainUIIcon[self.data.tip_type] and self.data.tip_type ~= MAINUI_TIP_TYPE.LIAOYILIAO 
		and self.data.tip_type ~= MAINUI_TIP_TYPE.FRIEND_LIAOYILIAO then
		print_error("界面提示icon找不到对应类型",self.data.tip_type)
	end
	

	local show_bg = true
	local bg_asset = "a3_zjm_btn_bg"
	if self.data.tip_type == MAINUI_TIP_TYPE.MAIL then
		self.node_list["effect"]:SetActive(false)
		self.node_list["ImgIcon"]:SetActive(false)
		self.node_list["text_name"]:SetActive(true)
		self.node_list["text_name"].text.text = Language.MainUIIcon[self.data.tip_type]
		-- local bundle, asset = ResPath.GetF2MainUIImage("chat_tips_" .. self.data.tip_type)
		-- self.node_list["ImgIcon"].image:LoadSprite(bundle, asset ,function ()
		-- 	self.node_list["ImgIcon"].image:SetNativeSize()
		-- 	end)
	elseif self.data.tip_type == MAINUI_TIP_TYPE.CHAT_GUILD then
		self.node_list["effect"]:SetActive(false)
		self.node_list["ImgIcon"]:SetActive(false)
		self.node_list["text_name"]:SetActive(true)
		self.node_list["text_name"].text.text = Language.MainUIIcon[self.data.tip_type]
	elseif self.data.tip_type == MAINUI_TIP_TYPE.LIAOYILIAO then
		show_bg = false
		self.node_list["text_name"]:SetActive(false)
		self.node_list["effect"]:SetActive(false)
		self.node_list["num_bg"]:SetActive(false)
		local bundle, asset = ResPath.GetF2MainUIImage("jh_wyjhyuanfen")
		self.img_icon.image:LoadSprite(bundle, asset, function()
			self.img_icon.image:SetNativeSize()
		end)
		self.img_icon:SetActive(true)
	elseif self.data.tip_type == MAINUI_TIP_TYPE.FRIEND_LIAOYILIAO then
		show_bg = false
		self.node_list["text_name"]:SetActive(false)
		self.node_list["effect"]:SetActive(false)
		self.node_list["num_bg"]:SetActive(false)
		self.node_list["ImgIcon"]:SetActive(false)
		local bundle, asset = ResPath.GetF2MainUIImage("jh_wyjhyliao")
		self.img_icon.image:LoadSprite(bundle, asset, function()
			self.img_icon.image:SetNativeSize()
		end)
		self.img_icon:SetActive(true)
	else
		self.node_list["ImgIcon"]:SetActive(false)
		self.node_list["text_name"]:SetActive(true)
		self.node_list["text_name"].text.text = Language.MainUIIcon[self.data.tip_type]
		self.node_list["effect"]:SetActive(true)
	end

	self.node_list["bg"]:SetActive(show_bg)
	local bundle, asset = ResPath.GetF2MainUIImage(bg_asset)
	self.node_list["bg"].image:LoadSprite(bundle, asset, function()
		self.node_list["bg"].image:SetNativeSize()
	end)

	self:FlushGuildAnswer()
	self.is_hide_pop = false
	self:SetNumber()
end

function MainuiIcon:SetIsShowAnswerTip(is_show_answer_tip)
	self.is_show_answer_tip = is_show_answer_tip
	self.is_click_any = not is_show_answer_tip
end

function MainuiIcon:FlushGuildAnswer()
	if self.data.tip_type == MAINUI_TIP_TYPE.CHAT_GUILD then
		if ChatWGCtrl.Instance:CheckIsShowAnswer() and not self.is_click_any then
			self:SetQiPaoUpText(Language.MainUIIcon.Guild_Answer_Begin)
			local show_condition = self.is_show_answer_tip or ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER)
			self.node_list["qipao_root"]:SetActive(show_condition)
		else
			self.node_list["qipao_root"]:SetActive(false)
		end
	elseif self.data.tip_type == MAINUI_TIP_TYPE.Market_Auction then--拍卖
		local auction_show_state = MarketWGData.Instance:GetAuctionUpQiPaoActive()
		if auction_show_state then
			self:SetQiPaoUpText(Language.MainUIIcon.Market_Auction_Begin)
			self.node_list["qipao_root"]:SetActive(true)
		else
			self.node_list["qipao_root"]:SetActive(false)
		end
	elseif self.data.tip_type == MAINUI_TIP_TYPE.Market_Auction_Beyond then--拍卖-竞价被超越
		self:SetQiPaoUpText(Language.MainUIIcon.Market_Auction_Beyond)
		self.node_list["qipao_root"]:SetActive(true)
	elseif self.data.tip_type == MAINUI_TIP_TYPE.BOSS_XIEZHU then  	--boss协助 有新的协助提示
		local flag = BossXiezhuWGData.Instance:GetAddNewInvokeFlag()
		if flag then
			self:SetQiPaoUpText(Language.MainUIIcon.AddNewInvoke)
			self.node_list["qipao_root"]:SetActive(true)
			BossXiezhuWGData.Instance:SetAddNewInvokeFlag(false)
		else
			self.node_list["qipao_root"]:SetActive(false)
		end
	else
		self.node_list["qipao_root"]:SetActive(false)
	end

	self:SetQiPaoMoveAnimtion()
end

function MainuiIcon:SetNumber()
	if not self:IsCanXianShiNum() then self.node_list.num_bg:SetActive(false) return end
	self.node_list.num_bg:SetActive(true)
	local num = self.data.repetition_num
	if num <= 0 then
		num = ""
	elseif num > 99 then
		num = "99+"
	end
	
	self.node_list.Text_num.text.text = num
end
function MainuiIcon:IsCanXianShiNum()
	-- self.data.tip_type == MAINUI_TIP_TYPE.MAIL or self.data.tip_type == MAINUI_TIP_TYPE.FRIEND_CHAT
	if self.data.tip_type == MAINUI_TIP_TYPE.REDPOCKET then 
		return false
    end

	if self.data.tip_type ~= MAINUI_TIP_TYPE.NATIONAL_PREVIEW and self.index >= Tips_Index.Tips_Function_1 and self.index <= Tips_Index.Tips_Function_2 then
		return false
	end
	if self.data.tip_type == MAINUI_TIP_TYPE.FRIEND or self.data.tip_type == MAINUI_TIP_TYPE.TEAM_APPLY then
		return false
	end
	--仙盟不显示红点
	if self.data.tip_type == MAINUI_TIP_TYPE.CHAT_GUILD then
		return false
	end
	if self.data.repetition_num <= 0 then
		return false
	end
    if self.data.tip_type == MAINUI_TIP_TYPE.NATIONAL_PREVIEW then
		return self.data.repetition_num > 1
	end
	return true
end

function MainuiIcon:SetQiPaoUpText(text_str)
	if self.node_list["text_tip_qipao"] and self.node_list["qipao_root"] then
		self.node_list["text_tip_qipao"].text.text = text_str
	end
end

function MainuiIcon:SetQiPaoMoveAnimtion()
	if not self.node_list["qipao_root"] then
		return
	end
	
	local show_anim = self.node_list["qipao_root"].gameObject.activeSelf
	if show_anim then
		UITween.MoveLoop(self.node_list["qipao_root"].gameObject, u3dpool.vec3(6, 21, 0), u3dpool.vec3(6, 30, 0), 1)
	else
		UITween.KillMoveLoop(self.node_list["qipao_root"].gameObject)
	end
end
