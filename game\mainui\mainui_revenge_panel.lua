function MainUIView:AddRevenge(role)
end

function MainUIView:InitRevenge()
	self.touch_move_event = GlobalEventSystem:Bind(LayerEventType.TOUCH_MOVED, BindTool.Bind(self.StopAutoRevenge, self))
	self.obj_create_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE,BindTool.Bind(self.ObjRevengeCreate,self))
	self.obj_delete_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DELETE,BindTool.Bind(self.ObjRevengeDelete,self))
	self.check_on_attack = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.CheckOnAttack, self), 1)
	self:InitListRevenge()

	self.revenge_flush_limit_timer = 0
	self.revenge_flush_need_flush = false
end

function MainUIView:CheckOnAttack()
	if self.revenge_cell_list ~= nil then
		for k, v in pairs(self.revenge_cell_list) do
			if v ~= nil and v:GetData() then
				local data = v:GetData()
				if data.deliverer_role_id ~= nil then
					if RevengeWGData.Instance:IsOnAttack(data.deliverer_role_id) then
						v:ShowOnAttack(true)
					else
						v:ShowOnAttack(false)
					end
					if RevengeWGData.Instance:IsNeedClearOnAttackList(data.deliverer_role_id) then
						RevengeWGData.Instance:ClearRoleInList(data.deliverer_role_id)
						self:RevengeChange()
					end
				end
			end
		end
	end
end

function MainUIView:DeleteListRevenge()
	self.revenge_list = nil
	if self.revenge_cell_list then
		for k,v in pairs(self.revenge_cell_list) do
			v:DeleteMe()
		end
		self.revenge_cell_list = nil
	end
	if self.check_on_attack then
		GlobalTimerQuest:CancelQuest(self.check_on_attack)
		self.check_on_attack = nil
	end

	if self.obj_create_event then
		GlobalEventSystem:UnBind(self.obj_create_event)
		self.obj_create_event = nil
	end

	if RevengeWGData and RevengeWGData.Instance then
		RevengeWGData.Instance:SetWantRevengeRole(nil)
	end

	self.revenge_flush_limit_timer = 0
	self.revenge_flush_need_flush = false
end

function MainUIView:InitListRevenge()
	self.revenge_cell_list = {}
	self.revenge_list = self.node_list.revenge_list
	local list_delegate = self.revenge_list.list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCellRevenge, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshCellRevenge, self)
end
 
function MainUIView:GetNumberOfCellRevenge()
	return RevengeWGData.Instance:GetRevengeNum()
end

function MainUIView:GetDataListRevenge()
	return RevengeWGData.Instance:GetRevengeList()
end

function MainUIView:RefreshCellRevenge(cell, cell_index)
	cell_index = cell_index + 1
	local item_cell = self.revenge_cell_list[cell]
	local data_list = self:GetDataListRevenge()
	if not item_cell then
		item_cell = RevengeCell.New(cell.gameObject)
		self.revenge_cell_list[cell] = item_cell
	end

	item_cell:SetIndex(cell_index)
	if data_list[cell_index] then
		item_cell:SetData(data_list[cell_index])
		item_cell:SetClickCallBack(function (data)
			self:OnClickItemRevenge(data)
		end)
	end
	item_cell:FlushHL()
end

function MainUIView:OnClickItemRevenge(revenge_cell)
	if revenge_cell == nil or revenge_cell:GetData() == nil then
		return
	end

	local data = revenge_cell:GetData()
	if data == nil or data.deliverer_role_id == nil then
		return
	end

	local role_id = data.deliverer_role_id
	local merge_server_id = data.merge_server_id or -1
	local origin_plat_type = data.origin_plat_type or -1
	local revenge_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
	if revenge_role_id ~= nil and revenge_role_id == role_id then
		self:StopAutoRevenge()
		self:FlushRevengeState(0)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.CancelRevenge)
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		return
	end

	self.stop_renvenge = false
	self:OnRevenge(role_id, merge_server_id, origin_plat_type)
end

function MainUIView:CancleRevengeQuest()
	if self.revenge_quest then
		GlobalTimerQuest:CancelQuest(self.revenge_quest)
		self.revenge_quest = nil
	end
end

function MainUIView:ObjRevengeCreate(obj)
	if obj == nil or obj:IsDeleted() or not obj:IsRole() then
		return
	end
	local role_id
    if IS_ON_CROSSSERVER then
        role_id = obj:GetOriginId()
    else
        role_id = obj:GetRoleId()
    end

	if self.find_role_id and role_id == self.find_role_id then
		self:StopAutoRevenge()
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		if self.revenge_cell_list ~= nil then
			for k,v in pairs(self.revenge_cell_list) do
				if v ~= nil and v:GetData() ~= nil then
					local data = v:GetData()
					if data.deliverer_role_id ~= nil and data.deliverer_role_id == role_id then
						self:OnClickItemRevenge(v)
						break					
					end
				end
			end
		end
	end
end

function MainUIView:ObjRevengeDelete(obj)
	if RevengeWGData.Instance ~= nil and obj ~= nil and not obj:IsDeleted() and obj:IsRole() and obj:GetRoleId() then
		local role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
		if role_id ~= nil and obj:GetRoleId() == role_id then
			if RevengeWGData.Instance:GetRevengeIsVaild(role_id) then
				MoveCache.end_type = MoveEndType.AttackTarget
				self.find_role_id = role_id
				local pos_x, pos_y = obj:GetLogicPos()
				GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), pos_x, pos_y, 1)
				return
			end

			self:StopAutoRevenge()
			self:FlushRevengeState(0)
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)	
		end
	end
end

function MainUIView:OnRevenge(role_id, merge_server_id, origin_plat_type)
	TianShen3v3WGData.Instance:SetIsAutoFight(false)

	self.want_revenge_role = nil
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() or main_role:IsRealDead() or main_role:IsGhost() then
		return
	end

	local r_x, r_y = RevengeWGData.Instance:GetRevengeRolePos(role_id)
	if r_x ~= nil and r_y ~= nil then
		if AStarFindWay:IsBlock(r_x, r_y, false) then
			self:StopAutoRevenge()
			self:FlushRevengeState(0)
			return
		end

        local scene_id = Scene.Instance:GetSceneId()
        local is_same_server = not RoleWGData.Instance:IsSameServer({merge_server_id = merge_server_id, origin_plat_type = origin_plat_type})
        local obj
        if IS_ON_CROSSSERVER or is_same_server then
            obj = Scene.Instance:GetObjByOriId(role_id)
        else
            obj = Scene.Instance:GetRoleByRoleId(role_id)
        end

		if obj ~= nil and not obj:IsDeleted() then
			if obj:IsRealDead() then
				self:StopAutoRevenge()
				self:FlushRevengeState(0)
			else
				local target_vo = obj:GetVo()
				local my_vo = main_role:GetVo()
				local target_server_id = target_vo.origin_server_id
				local my_server_id = my_vo.origin_server_id
				local target_attack_mode = target_vo.attack_mode
				local my_attack_mode = my_vo.attack_mode
				local target_is_ally = self:IsAlly(obj)

				if Scene.Instance:GetSceneType() == SceneType.Common then
					local is_can_attack = false
					if my_attack_mode == ATTACK_MODE.PEACE and target_vo.name_color ~= EvilColorList.NAME_COLOR_WHITE then
						is_can_attack = true
					elseif my_attack_mode == ATTACK_MODE.GUILD and not target_is_ally then
						is_can_attack = true
					elseif my_attack_mode == ATTACK_MODE.ALL then
						is_can_attack = true
					elseif my_attack_mode == ATTACK_MODE.NAMECOLOR and target_server_id ~= my_server_id then
						is_can_attack = true
					end

					if not is_can_attack then
						if target_server_id ~= my_server_id then
							MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.NAMECOLOR, 1)
						elseif not target_is_ally then
							MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.GUILD, 1)
						elseif target_is_ally then
							local function ok_fun()
								MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.ALL, 1)
							end

							local function cancel_fun()
								if RevengeWGData and RevengeWGData.Instance then
									RevengeWGData.Instance:SetWantRevengeRole(nil)
								end
							end

							TipWGCtrl.Instance:OpenAlertTips(Language.Fight.AllyTip, ok_fun, cancel_fun, nil, cancel_fun)
						end

						if RevengeWGData and RevengeWGData.Instance then
							RevengeWGData.Instance:SetWantRevengeRole(role_id)
						end

						return
					end
				end


				-- local revenge_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
				GuajiWGCtrl.Instance:ClearTemporary()
				self:StopAutoRevenge()
				if GuajiCache.guaji_type == GuajiType.None then
					GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				end

				local scene_logic = Scene.Instance:GetSceneLogic()
				if scene_logic ~= nil then
					scene_logic:ClearGuaJiInfo()
				end
				
				RevengeWGData.Instance:SetCurRevengeRoleId(role_id)
				self:FlushRevengeState(role_id)

                BossWGCtrl.Instance:CancelSelectXianJieEnemy()

				GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
				if main_role:IsInSafeArea() or obj:IsInSafeArea() then
					if obj:IsInSafeArea() then
						SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.TargetIsSafe)
					end

					GuajiWGCtrl.Instance:MoveToPos(scene_id, r_x, r_y, 1)
				end
				-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.ALL)
				
				-- GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		else
			GuajiWGCtrl.Instance:ClearTemporary()
			MoveCache.end_type = MoveEndType.AttackTarget
			self.find_role_id = role_id
			RevengeWGData.Instance:SetCurRevengeRoleId(role_id)
			self:FlushRevengeState(role_id)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, r_x, r_y, 1)
		end
	else
		self:StopAutoRevenge()
		self:FlushRevengeState(0)
	end
end

function MainUIView:StopAutoRevenge(is_check)
	RevengeWGData.Instance:SetCurRevengeRoleId(nil)
	RevengeWGData.Instance:SetWantRevengeRole(nil)
	
	self.stop_renvenge = true
	self.find_role_id = nil
	self:CancleRevengeQuest()
	local state = self.on_revenge
	self.on_revenge = false
	self.want_revenge_role = nil
end

-- 策划要反击的那个人移动了，自己也要追着
function MainUIView:FlushFindRole()
	-- 反击目标离开场景
	local check_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
	local r_x, r_y = RevengeWGData.Instance:GetRevengeRolePos(check_role_id)
	if r_x == nil or r_y == nil then
		self:StopAutoRevenge()
		self:FlushRevengeState(0)
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.RevengeObjLeave)
		return
	end

	if self.find_role_id == nil then

		-- 自己在安全区，目标离开安全区的情况下，要追出去
		if check_role_id ~= nil then
			local main_role = Scene.Instance:GetMainRole()
			if main_role ~= nil and main_role:IsInSafeArea() then
				if r_x ~= nil or r_y ~= nil then
					if not AStarFindWay:IsInSafeArea(r_x, r_y) then
						if main_role:IsStand() and not main_role:IsAtkPlaying() and RevengeWGData.Instance:GetCheckTargetSafeTime() < Status.NowTime then
							GuajiWGCtrl.Instance:ClearTemporary()
							RevengeWGData.Instance:SetCheckTargetSafeTime(Status.NowTime + 2)
							MoveCache.end_type = MoveEndType.AttackTarget
							GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), r_x, r_y, 1)
						end
					end
				end
			end
		end

		return
	end

	-- RevengeWGData.Instance:SetCurRevengeRoleId(nil)

	if r_x == nil or r_y == nil then
		self:StopAutoRevenge()
		self:FlushRevengeState(0)
		return
	else
		local pos_x = r_x
		local pos_y = r_y
		local scene_id = Scene.Instance:GetSceneId()
		if MoveCache.end_type ~= nil and MoveCache.end_type == MoveEndType.AttackTarget and MoveCache.scene_id ~= nil and MoveCache.scene_id == MoveCache.scene_id and (MoveCache.x ~= pos_x or MoveCache.y ~= pos_y) then
			GuajiWGCtrl.Instance:ClearTemporary()
			MoveCache.end_type = MoveEndType.AttackTarget
			GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, 1)			
		end
	end
end

--刷新反击中状态
function MainUIView:FlushRevengeState(role_id)
	role_id = role_id or 0

	if self.revenge_cell_list ~= nil then
		for k, v in pairs(self.revenge_cell_list) do
			if v ~= nil then
				v:FlushHL()
			end
		end
	end
end

function MainUIView:RevengeChange()
	if self.revenge_flush_limit_timer > Status.NowTime then
		self.revenge_flush_need_flush = true
		return
	else
		self.revenge_flush_limit_timer = Status.NowTime + 2
		self.revenge_flush_need_flush = false
	end

	if self.revenge_list ~= nil and not IsNil(self.revenge_list.scroller) then
		self.revenge_list.scroller:RefreshAndReloadActiveCellViews(false)
	end
end

function MainUIView:UpdateRevenge()
	if self.revenge_flush_need_flush and self.revenge_flush_limit_timer <= Status.NowTime then
		self.revenge_flush_need_flush = false
		self.revenge_flush_limit_timer = 0
		self:RevengeChange()
	end
end

function MainUIView:OnMainRoleDead(obj)
	if not obj or not obj:IsMainRole() then
		return
	end

	self:FlushRevengeState(0)
end

function MainUIView:OnRoleObjDead(role_id)
	local check_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
	if check_role_id ~= nil and check_role_id == role_id then
		self:StopAutoRevenge()
		self:FlushRevengeState(0)
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.RevengeObjDie)
	end
end

function MainUIView:TryRevengeRole(check_role_id)
	if check_role_id == nil then
		return
	end

	local list = RevengeWGData.Instance:GetRevengeList()
	if list == nil or next(list) == nil then
		return
	end

	if self.revenge_cell_list ~= nil then
		for k,v in pairs(self.revenge_cell_list) do
			if v ~= nil and v:GetData() ~= nil then
				local data = v:GetData()
				if data.deliverer_role_id ~= nil and data.deliverer_role_id == check_role_id then
					self:OnClickItemRevenge(v)
					break					
				end
			end
		end
	end
end

function MainUIView:IsAlly(obj)
	local is_ally = false
	if obj == nil or obj:IsDeleted() then
		return is_ally
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() then
		return is_ally
	end
    local b1
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        b1 = not CrossTeamWGData.Instance:GetTargetIsTeamMember(obj:GetUUID())
    else
        b1 = not SocietyWGData.Instance:IsTeamMember(obj:GetOriginId())
    end
	local b2 = main_role:GetVo().guild_id == 0 or main_role:GetVo().guild_id ~= obj:GetVo().guild_id

	-- 同盟模式非仙盟战伴侣不为敌人
	local lover_id = main_role:GetVo().lover_uid or 0
	local is_lover = lover_id ~= 0 and lover_id == obj:GetVo().role_id
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= nil and scene_type == SceneType.XianMengzhan then
		is_lover = false
	end

	if is_lover then
		is_ally = true
	end

	if b1 and b2 and not is_lover then
		is_ally = false
	end

	if not b1 then
		is_ally = true
	end

	if not b2 then
		is_ally = true
	end
	
	return is_ally
end
-----------------------RevengeCell-----------------------------------
RevengeCell = RevengeCell or BaseClass(BaseRender)

function RevengeCell:__init()
    XUI.AddClickEventListener(self.node_list.click_obj, BindTool.Bind(self.OnClick, self))
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.OnClose, self))
    self.head_cell = BaseHeadCell.New(self.node_list.HeadCell)
    self.head_cell:SetIsNeedCheckSame(true)
    self.name_pos_y = self.node_list.name.transform.localPosition.y
    self.node_list.img_select:SetActive(false)
    self.node_list["gongjizhong"]:SetActive(false)
    self.close_call = nil
end

function RevengeCell:ReleaseCallBack()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end

	self.clock_call = nil
end

function RevengeCell:SetCloseCallback(call)
	self.close_call = call
end

function RevengeCell:OnFlush()
	if self.data == nil then
		self:SetActive(false)
		return
	end
	if not self.data.is_data_init then
		self:SetActive(false)
		return
	end
	self:SetActive(true)

	self.node_list["SpecialHeadIcon"]:SetActive(false)
	self.node_list["HeadCell"]:SetActive(false)
	if Scene.Instance:GetSceneType() == SceneType.TianShen3v3 then
	    local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.tianshen_3v3_tianshen_index)
        if tianshen_cfg then
            local bundle, asset = ResPath.GetItem(tianshen_cfg.head_id)
			self.node_list["SpecialHeadIcon"].image:LoadSpriteAsync(bundle, asset, function()
				if self.node_list["SpecialHeadIcon"] then
					self.node_list["SpecialHeadIcon"].image:SetNativeSize()
					self.node_list["SpecialHeadIcon"]:SetActive(true)
				end
			end)
		else
			print_error("没有取到天神配置请检查, tianshen_3v3_tianshen_index：", self.data.tianshen_3v3_tianshen_index, self.data.role_id, self.data.role_name, self.data)
		end
	else
		self.node_list["HeadCell"]:SetActive(true)
		self.head_cell:SetData({role_id = self.data.deliverer_key_id, prof = self.data.deliverer_prof, sex = self.data.sex})
	end

	self.node_list.name.text.text = self.data.role_name
	self.node_list.name.transform.localPosition.y = self.name_pos_y
end

function RevengeCell:SetClickCallBack(event)
    self.event = event
end

function RevengeCell:OnClick()
    if self.event then
        self.event(self)
    end
end

function RevengeCell:OnClose()
	if self.data == nil or self.data.deliverer_role_id == nil then
		return
	end

	local revenge_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
	RevengeWGData.Instance:ClearRoleInList(self.data.deliverer_role_id)
	if revenge_role_id == self.data.deliverer_role_id then
		MainuiWGCtrl.Instance:StopAutoRevenge(true, nil, revenge_role_id)
	else
		MainuiWGCtrl.Instance:RevengeChange()
	end
end

--反击中
function RevengeCell:FlushHL()
	if self.data == nil or self.node_list == nil then
		return
	end

	local revenge_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
	-- print_error("-----------RevengeCell:FlushHL---------", revenge_role_id, self.data.deliverer_role_id)
	local is_active = revenge_role_id ~= nil and revenge_role_id == self.data.deliverer_role_id
	if self.node_list.img_select ~= nil then
		self.node_list.img_select:SetActive(is_active)
	end
	
	if self.node_list["gongjizhong"] ~= nil then
		self.node_list["gongjizhong"]:SetActive(is_active)
	end
 
end

--玩家正在攻击你
function RevengeCell:ShowOnAttack(is_show)
	if self.node_list ~= nil and self.node_list.onattack ~= nil then
		self.node_list.onattack:SetActive(is_show)
	end
end