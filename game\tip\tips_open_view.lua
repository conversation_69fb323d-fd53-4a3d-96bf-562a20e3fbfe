TipsOpenView = TipsOpenView or BaseClass(SafeBaseView)

function TipsOpenView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(600, 424)})
	self:AddViewResource(0, "uis/view/role_ui_prefab", "tips_open_view")
end

function TipsOpenView:ReleaseCallBack()
	if self.stuff_item then
		self.stuff_item:DeleteMe()
		self.stuff_item = nil
	end

	self.callback = nil
	self.data = nil
	self.btn_text = nil
	self.title_text = nil
	self.item_desc = nil
end

function TipsOpenView:SetData(data,title_text,btn_text,callback,item_desc)
	if not data then return end
	self.data = data
	self.btn_text = btn_text
	self.title_text = title_text
	self.callback = callback
	self.item_desc = item_desc
	self:Open()
end

function TipsOpenView:LoadCallBack()
	self.stuff_item = ItemCell.New(self.node_list.item_pos)
	self.node_list.title_view_name.text.text = Language.Pet.ViewName_7
	XUI.AddClickEventListener(self.node_list.btn_open,BindTool.Bind(self.ClickOpen,self))
end

function TipsOpenView:ShowIndexCallBack(index)
	self:Flush()
end

function TipsOpenView:OnFlush(param_t, index)
	if not self.data then return end
	local data = self.data
	self.node_list.btn_text.text.text = self.btn_text
	self.node_list.title.text.text = self.title_text
	self.stuff_item:SetData(data)

	if self.item_desc then
		self.stuff_item:SetRightBottomTextVisible(true)
		self.stuff_item:SetRightBottomText(self.item_desc)
	else
		self.stuff_item:SetRightBottomTextVisible(false)
	end
end

function TipsOpenView:ClickOpen()
	if self.callback then
		self.callback()
	end
	self:Close()
end