local SelectState = {
    ActRule     = 1,    --活动规则
    GuildRank   = 2,    --仙盟排名
    PersonRank  = 3,    --个人排名
}
local RewardListPosWidth = {
    [1] = 100,--{150, 100}
    [2] = 200,--{150, 200}
    [3] = 220,--{150, 220}
}

function GuildView:InitGuildShouHu()
    --Toggle活动规则相关
    self.node_list["sh_title_text"].text.text = Language.Guild.SHTitle
    self.node_list["sh_act_time_text"].text.text = Language.Guild.SHActTime
    self.node_list["sh_act_desc_text"].text.text = Language.Guild.SHActDesc
    self.act_reward_preview_list = AsyncBaseGrid.New()
    self.act_reward_preview_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list.sh_reward_preview_list,
                                                assetBundle = "uis/view/guild_shouhu_ui_prefab",
                                                assetName = "shouhu_reward_render",
                                                itemRender = ConditionPaiPinItemRender, })
    self.act_reward_preview_list:SetStartZeroIndex(false)
    --Toggle仙盟排名相关
    self.guild_reward_list = AsyncListView.New(SHGuildRankRender, self.node_list.sh_guild_rank_list)
    --Toggle个人排名相关
    self.person_reward_list = AsyncListView.New(SHPersonRankRender, self.node_list.sh_geren_rank_list)

    for i = 1, 3 do
        self.node_list["left_toggle_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnSHSelectToggle, self, i))
    end



    XUI.AddClickEventListener(self.node_list.sh_btn_goto, BindTool.Bind(self.OnClickGoTo,self))
    XUI.AddClickEventListener(self.node_list["btn_shouhu_xianmeng_tip"], BindTool.Bind1(self.OpenShouHuXianMengTip, self))

    --模型
    local boss_config = GuildWGData.Instance:GetSHBossCfg()
    self.sh_boss_model = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["RoleDisplay"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = true,
    }
    
    self.sh_boss_model:SetRenderTexUI3DModel(display_data)
    -- self.sh_boss_model:SetUI3DModel(self.node_list["RoleDisplay"].transform, self.node_list["ModelEvent"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    self:AddUiRoleModel(self.sh_boss_model)
    if not boss_config then return end
    self.sh_boss_model:PlayMonsterAction()
    self.sh_boss_model:SetMainAsset(ResPath.GetMonsterModel(boss_config.resid))

    self.node_list.sh_boss_name.text.text = boss_config.name --策划说不显示等级
end
function GuildView:OpenShouHuXianMengTip()
    RuleTip.Instance:SetContent(Language.GuildAnswer.DatiTipContent_1, Language.GuildAnswer.DatiTipTitle_1)
end
function GuildView:DeleteGuildShouHu()
    if self.act_reward_preview_list then
         self.act_reward_preview_list:DeleteMe()
        self.act_reward_preview_list = nil
    end
    if self.guild_reward_list then
         self.guild_reward_list:DeleteMe()
        self.guild_reward_list = nil
    end
    if self.person_reward_list then
         self.person_reward_list:DeleteMe()
        self.person_reward_list = nil
    end

    if self.sh_boss_model then
        self.sh_boss_model:DeleteMe()
        self.sh_boss_model = nil
    end

    self.old_state = nil
    self.special_toggle_param = nil
end
function GuildView:ShowIndexGuildShouHu()
    self:ReqGuildFbRankInfo()
    if GuildWGData.Instance:GetIsEnterCrossBiPing() then
        self.node_list.hl_text.text.text = Language.Guild.SHCrossGuildRankToggle
        self.node_list.nor_text.text.text = Language.Guild.SHCrossGuildRankToggle
    else
        self.node_list.hl_text.text.text = Language.Guild.SHGuildRankToggle
        self.node_list.nor_text.text.text = Language.Guild.SHGuildRankToggle
    end

    if self.special_toggle_param then
        self.node_list["left_toggle_" .. self.special_toggle_param].toggle.isOn = true
        self.special_toggle_param = nil
    else
        self.node_list["left_toggle_1"].toggle.isOn = true
    end
    self:SHFlushGoToBtn()
end

function GuildView:SetSpecialParam(toggle_index)
    self.special_toggle_param = toggle_index
end

function GuildView:SHFlushGoToBtn()
    --self.node_list.sh_btn_goto

    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_FB)
    if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
        XUI.SetGraphicGrey(self.node_list.sh_btn_goto, true)
    else
        XUI.SetGraphicGrey(self.node_list.sh_btn_goto, false)
    end
end

function GuildView:ReqGuildFbRankInfo()
    if GuildWGData.Instance:GetIsEnterCrossBiPing() then
        GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GUILD_FB_OPERA_TYPE_RANK_CROSS)
    else
        GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GUILD_FB_OPERA_TYPE_RANK)
    end
    GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GUILD_FB_OPERA_TYPE_DETAIL_INFO)
end

function GuildView:OnFlushGuildShouHu(param_t)
    if not self:IsLoadedIndex(TabIndex.guild_shouhu) then
        return
    end
    --self:FlushTitleInfo()
    for k, v in pairs(param_t) do
        if k == "all" then
            if v['title_info_person_rank'] then
                self:FlushPersonRank()
                self:FlushTitleInfo()
			end
            if v['guild_rank'] then
                self:FlushGuildRank()
			end
        end
    end
end

--前往活动
function GuildView:OnClickGoTo()
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_FB)
    if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
        return
    end
    GuildWGCtrl.Instance:SendGuildFbEnterReq()
end

function GuildView:OnSHSelectToggle(index, is_on)
    if is_on then
        if self.old_state == index then
            return
        end
        self:SetContentActive(index, true)
        self:SHChangeState(index)
        self.old_state = index
    end
end

function GuildView:SetContentActive(index, active)
    for i = 1, 3 do
        if i == index then
            self.node_list["left_content"..i]:SetActive(active)
        else
            self.node_list["left_content"..i]:SetActive(not active)
        end
    end
end

--改变页面状态
function GuildView:SHChangeState(index)
    if index == SelectState.ActRule then
        local data_list = GuildWGData.Instance:GetActRewardPreviewItemList()
        if data_list then
            self.act_reward_preview_list:SetDataList(data_list, 3)
        end
    elseif index == SelectState.GuildRank then
        self:FlushGuildRank()
    elseif index == SelectState.PersonRank then
        self:FlushPersonRank()
    end
end

--刷新个人排行
function GuildView:FlushPersonRank()
    local data_list = GuildWGData.Instance:GetGeRenRankDataList()

    if not data_list or IsEmptyTable(data_list) then
        data_list = GuildWGData.Instance:GetDefaultPersonRankDataList() --如果没有数据，获取一个默认数据
    end
    self.person_reward_list:SetDataList(data_list, 3)
end

--刷新帮派排行
function GuildView:FlushGuildRank()
    local data_list = GuildWGData.Instance:GetGuildRankDataList()
    if not data_list or IsEmptyTable(data_list) then
        data_list = GuildWGData.Instance:GetDefaultGuildRankDataList() --如果没有数据，获取一个默认数据
    end

    self.guild_reward_list:SetDataList(data_list, 3)
end

--刷新标题信息
function GuildView:FlushTitleInfo()
    local total_hurt, person_rank = GuildWGData.Instance:GetPersonHurtRank()
    local pass_time, pass_wave, guild_rank = GuildWGData.Instance:GetGuildPassTimeWaveRank()
    if not person_rank or not guild_rank then return end


    if pass_wave == -1 then --仙盟内所有人未参与
        self.node_list["sh_pass_time_left"].text.text = Language.Guild.SHPassState
        self.node_list["sh_pass_time_text"].text.text = Language.Guild.SHWeiCanYu
        self.node_list["sh_guild_rank_text"].text.text = Language.Guild.SHNotRank
    else
        if guild_rank == -1 then --未通关
            --显示通关状态，第几关, 仙盟排名,未上榜
            self.node_list["sh_pass_time_left"].text.text = Language.Guild.SHPassState
            self.node_list["sh_pass_time_text"].text.text = string.format(Language.Guild.SHWaveStr, pass_wave)
            self.node_list["sh_guild_rank_text"].text.text = Language.Guild.SHNotRank
        else
            --显示通关时间
            self.node_list["sh_pass_time_left"].text.text = Language.Guild.SHPassTime
            self.node_list["sh_pass_time_text"].text.text = TimeUtil.MSTime(pass_time or 0)
            self.node_list["sh_guild_rank_text"].text.text = string.format(Language.Guild.SHJieSuanRankStr, guild_rank)
        end
    end

    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_FB)
    if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
        self.node_list["sh_geren_damage_text"].text.text = Language.Guild.SHWeiCanYu
        self.node_list["sh_geren_rank_text"].text.text = Language.Guild.SHNotRank
    else
        if person_rank == -1 then --未参与
            self.node_list["sh_geren_damage_text"].text.text = Language.Guild.SHWeiCanYu
            self.node_list["sh_geren_rank_text"].text.text = Language.Guild.SHNotRank
        else
            self.node_list["sh_geren_damage_text"].text.text = CommonDataManager.ConverExp(BigNumFormat(total_hurt))
            self.node_list["sh_geren_rank_text"].text.text = string.format(Language.Guild.SHJieSuanRankStr, person_rank)
        end
    end
    --print_error("通关时间：",pass_time,"通关波数：", pass_wave,"帮派排名：", guild_rank, "个人排名：",person_rank)
end


-------------SHRankRenderBase--------------
--仙盟守护排行格子基类
SHRankRenderBase = SHRankRenderBase or BaseClass(BaseRender)
function SHRankRenderBase:__init()
    self.reward_list = AsyncListView.New(self:GetListRender(), self.node_list["reward_list"])
end

--子类重写需要显示格子
function SHRankRenderBase:GetListRender()
    return ShouHuRewardItemRender
end

function SHRankRenderBase:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function SHRankRenderBase:OnFlush()
    --前3名做一些操作
    if self.index <= 3 then
        local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_"..self.index)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset)
        local bundle, asset = ResPath.GetCommonBackGround("guild_rank_bg_"..self.index)
        self.node_list["special_bg"].image:LoadSprite(bundle, asset)
    else
        self.node_list["rank_text"].text.text = self.data.rank_str or self.index
    end
    self.node_list["rank_img"]:SetActive(self.index <= 3)
    self.node_list["special_bg"]:SetActive(self.index <= 3)
    self.node_list["rank_text"]:SetActive(self.index > 3)

    --物品奖励格子
    local data_list = self:GetRewardDataList(self.index - 1)
    -- if self.node_list["wu"] then
    --     self.node_list["wu"]:SetActive(#data_list == 0)
    -- end

    self.reward_list:SetDataList(data_list, 3)
    --根据物品数量改变大小
    if #data_list > 2 then
        local rect_width = RewardListPosWidth[3]
        if self.node_list["reward_list"].rect.sizeDelta.x ~= rect_width then
            self.node_list["reward_list"].rect.sizeDelta = Vector2(rect_width , self.node_list["reward_list"].rect.sizeDelta.y)
            self.node_list["reward_list"].scroll_rect.enabled = true
        end
    else
        --小于两个不让滑动
        local rect_width = RewardListPosWidth[#data_list]
        if self.node_list["reward_list"].rect.sizeDelta.x ~= rect_width then
            self.node_list["reward_list"].rect.sizeDelta = Vector2(rect_width , self.node_list["reward_list"].rect.sizeDelta.y)
            self.node_list["reward_list"].scroll_rect.enabled = false
        end
    end
end

--子类重写获取数据方式
function SHRankRenderBase:GetRewardDataList(index)
    return nil
end


-------------SHGuildRankRender--------------
--帮派排行格子
SHGuildRankRender = SHGuildRankRender or BaseClass(SHRankRenderBase)

function SHGuildRankRender:OnFlush()
    SHRankRenderBase.OnFlush(self)

    if self.data.guild_id and self.data.guild_id > 0 then
        if self.data.pass_time and self.data.pass_time > 0 then
            self.node_list["pass_time"].text.text = TimeUtil.MSTime(self.data.pass_time)
        end
        self.node_list["pass_time"]:CustomSetActive(true)
        self.node_list["guild_name"]:CustomSetActive(true)
        --如果进入跨服比拼，显示服信息
        if GuildWGData.Instance:GetIsEnterCrossBiPing() then
            local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id)
            self.node_list["guild_name"].text.text = string.format(Language.Guild.SHGuildName, self.data.guild_name, temp_name)
        else
            self.node_list["guild_name"].text.text = self.data.guild_name
        end
    else
        if self.index > 10 then
            self.node_list["pass_time"]:CustomSetActive(false)
            self.node_list["guild_name"]:CustomSetActive(false)
        else
            self.node_list["pass_time"]:CustomSetActive(true)
            self.node_list["guild_name"]:CustomSetActive(true)
            self.node_list["pass_time"].text.text = Language.Guild.SHJingQingQiDai
            self.node_list["guild_name"].text.text = Language.Guild.SHJingQingQiDai
        end
    end
end

--获取帮派排行奖励格子数据
function SHGuildRankRender:GetRewardDataList(index)
    local index = index + 1
    if index > 10 then
        return GuildWGData.Instance:GetGuildAfterTenRankItemRewardList(index)
    end
    if self.data.guild_id and self.data.guild_id > 0 then
        return GuildWGData.Instance:GetBestFiveItemList(self.data.item_list)
    end
    return GuildWGData.Instance:GetGuildItemRewardByRank(index)
end

-------------SHPersonRankRender--------------
---个人排行格子
SHPersonRankRender = SHPersonRankRender or BaseClass(SHRankRenderBase)
function SHPersonRankRender:__init()

end
function SHPersonRankRender:__delete()

end

--获取个人排行奖励格子数据
function SHPersonRankRender:GetRewardDataList(index)
    if index > 10 then
        return GuildWGData.Instance:GetPersonAfterTenRankItemRewardList(index)
    end
    if self.data.guild_id and self.data.guild_id > 0 then
        return GuildWGData.Instance:GetBestFiveItemList(self.data.item_list)
    end
    return GuildWGData.Instance:GetPersonItemRewardByRank(index)
end

function SHPersonRankRender:OnFlush()
    SHRankRenderBase.OnFlush(self)
    if self.data.uid and self.data.uid ~= 0 then
        if self.data.total_hurt ~= nil then
            self.node_list["damage"].text.text = CommonDataManager.ConverExp(BigNumFormat(self.data.total_hurt))
        end
        self.node_list["damage"]:CustomSetActive(true)
        self.node_list["role_name"]:CustomSetActive(true)
        self.node_list["role_name"].text.text = self.data.name
    else
        if self.index > 10 then
            self.node_list["damage"]:CustomSetActive(false)
            self.node_list["role_name"]:CustomSetActive(false)
        else
            self.node_list["damage"]:CustomSetActive(true)
            self.node_list["role_name"]:CustomSetActive(true)
            self.node_list["damage"].text.text = Language.Guild.SHJingQingQiDai
            self.node_list["role_name"].text.text = Language.Guild.SHJingQingQiDai
        end
    end
end

------------------------ShouHuRewardItemRender----------------- -------------
--含有拍品标记的物品格子
ShouHuRewardItemRender = ShouHuRewardItemRender or BaseClass(BaseRender)
function ShouHuRewardItemRender:__init()
end

function ShouHuRewardItemRender:__delete()
	if self.item_cell ~= nil then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ShouHuRewardItemRender:OnFlush()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.pos)
    end
	if not self.data then return end
	self.item_cell:SetData(self.data)
    self.item_cell:SetItemTipFrom(ItemTip.FROM_GUILDBOSS)
    self.node_list.paiping:SetActive(self:GetIsShowPaiPin())
    --八卦迷阵借用
    if self.data.baguamizhen_random then
        local is_random = self.data.baguamizhen_random == 1
        self.node_list.random_bg:SetActive(is_random)
    else
        self.node_list.random_bg:SetActive(false)
    end
end

--子类重写
function ShouHuRewardItemRender:GetIsShowPaiPin()
    return false
end

------------------------PaiPinItemRender------------------------------
--必须显示拍品标记的奖励格子
PaiPinItemRender = PaiPinItemRender or BaseClass(ShouHuRewardItemRender)
function PaiPinItemRender:__init()
end
function PaiPinItemRender:GetIsShowPaiPin()
    return true
end

------------------------ConditionPaiPinItemRender------------------------------
--按条件显示拍品标记的格子
ConditionPaiPinItemRender = ConditionPaiPinItemRender or BaseClass(ShouHuRewardItemRender)
function ConditionPaiPinItemRender:__init()
end

function ConditionPaiPinItemRender:GetIsShowPaiPin()
    return self:GetCondition()
end

--获取条件是否成立
function ConditionPaiPinItemRender:GetCondition()
    return GuildWGData.Instance:GetContainPaiPin(self.data.item_id)
end
