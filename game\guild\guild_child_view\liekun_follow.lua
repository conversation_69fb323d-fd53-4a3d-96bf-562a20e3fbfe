-- require("scripts/game/task/task_follow_render")
--------------------------------------------------------------
--猎鲲场景UI
--------------------------------------------------------------
LieKunFollow = LieKunFollow or BaseClass(SafeBaseView)

function LieKunFollow:__init()
	self.view_layer = UiLayer.MainUILow
	self:AddViewResource(0, "uis/view/liekun_ui_prefab", "layout_liekun_follow")
	
	self.view_layer = UiLayer.MainUILow
end

function LieKunFollow:ReleaseCallBack()
	if self.liekun_boss_list then
		self.liekun_boss_list:DeleteMe()
		self.liekun_boss_list = nil
	end
	if self.liekun_smallmonster_list then
		self.liekun_smallmonster_list:DeleteMe()
		self.liekun_smallmonster_list = nil
	end

	if self.score_reward then
		for k,v in pairs(self.score_reward) do
			v:DeleteMe()
		end
		self.score_reward = nil
	end

	self.target_obj = nil
	self.arrow_ison = nil
	if CountDownManager.Instance:HasCountDown("liekun_countdown") then
		CountDownManager.Instance:RemoveCountDown("liekun_countdown")
	end
    Runner.Instance:RemoveRunObj(self)
	if self.arrow_click_event then
		GlobalEventSystem:UnBind(self.arrow_click_event)
		self.arrow_click_event = nil
	end
end

function LieKunFollow:LoadCallBack()
	self.is_show_btn = false
	self.show_boss = true
    Runner.Instance:AddRunObj(self, 8)
    XUI.AddClickEventListener(self.node_list.btn_rank_output,BindTool.Bind(self.ShowRankOutPutView,self))
    XUI.AddClickEventListener(self.node_list.btn_rank_single,BindTool.Bind(self.ShowRankSingleView,self))
    XUI.AddClickEventListener(self.node_list.btn_change,BindTool.Bind(self.ClickChangeBtn,self))
    -- XUI.AddClickEventListener(self.node_list.btn_gongnue,BindTool.Bind(self.ClickGongNveBtn,self))
	self.liekun_boss_list = AsyncListView.New(LieKunFbBossItemRender, self.node_list.boss_list)
	self.liekun_smallmonster_list = AsyncListView.New(LieKunFbSmallMonsterItemRender, self.node_list.SmallMonsterList)
	self.arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK,BindTool.Bind(self.ArrowTopChange,self))
	self:FlushBossInfo()
	self.node_list.rich_desc.text.text = Language.Guild.LieKunTips_2
	self:FlushScoreInfo()
end

function LieKunFollow:ClickGongNveBtn()
	RuleTip.Instance:SetContent(Language.Guild.LieKunTips_1, Language.RepeatRecharge.TipsTitle)
end

function LieKunFollow:ArrowTopChange(isOn)
	self.arrow_ison = isOn
	-- self.node_list.btn_gongnue:SetActive(not isOn)
end

function LieKunFollow:Update()
	local target_obj = GuajiCache.target_obj
	local falg = false
	if not target_obj or not target_obj.vo or target_obj:IsRole() then
		falg = false
	else
		local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto")
		if target_obj:IsMonster() and
		cfg.monster_list[target_obj:GetVo().monster_id] and
		cfg.monster_list[target_obj:GetVo().monster_id].type == MONSTER_TYPE.BOSS then
			falg = true
		end
	end
	if self.arrow_ison then
		falg = false
	end
	if falg ~= self.is_show_btn then
		self.is_show_btn = falg
		self.target_obj = target_obj
		self.node_list.btn_rank_output:SetActive(falg)
		-- self.node_list.btn_rank_single:SetActive(falg)
	end
end

function LieKunFollow:ShowRankOutPutView()
	if self.target_obj then
		local select_index
		local select_boss_id = self.target_obj.vo.monster_id
		-- local info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
		-- if info and info.boss_id then
		-- 	for k,v in pairs(info.boss_id) do
		-- 		if v.boss_id == select_boss_id then
		-- 			select_index = k
		-- 			break
		-- 		end
		-- 	end
		-- end
		if select_boss_id then
			GuildWGCtrl.Instance:OpenLkGuildRankView(select_boss_id,self.target_obj.vo.max_hp)
		end
	end
end

function LieKunFollow:ShowRankSingleView()
	if self.target_obj then
		local select_index
		local select_boss_id = self.target_obj.vo.monster_id
		-- local info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
		-- if info and info.boss_id then
		-- 	for k,v in pairs(info.boss_id) do
		-- 		if v.boss_id == select_boss_id then
		-- 			select_index = k
		-- 			break
		-- 		end
		-- 	end
		-- end
		if select_boss_id then
			GuildWGCtrl.Instance:OpenLkSingleRankView(select_boss_id,self.target_obj.vo.max_hp)
		end
	end
end

function LieKunFollow:ShowCountDownView(cross_lieKun_info)
	if cross_lieKun_info and cross_lieKun_info.zone < 0 then
		return
	end
	self.node_list.countdown_bg.image:LoadSprite(ResPath.GetFishModelId(cross_lieKun_info.zone))
	if CountDownManager.Instance:HasCountDown("liekun_countdown") then
		CountDownManager.Instance:RemoveCountDown("liekun_countdown")
	end

	local time
	CountDownManager.Instance:AddCountDown("liekun_countdown",
		function (elapse_time, total_time)
			time = total_time - elapse_time
			if time < 11 then
				if not self.node_list.countdown:GetActive() then
					self.node_list.countdown:SetActive(true)
				end
				self.node_list.countdown.text.text = string.format(Language.Guild.GuildCountDown,math.floor(time))
			end
		end,
		function ()
			self.node_list.countdown:SetActive(false)
		end,
		cross_lieKun_info.boss_next_flush_timestamp[1] , nil, 0.2)

end

function LieKunFollow:ShowMsgView(msg)
	if not self:IsOpen() then return end
	self.node_list.msg_tip:SetActive(true)
	self.node_list.msg_txt.text.text = msg
	GlobalTimerQuest:AddDelayTimer(function ()
		self.node_list.msg_tip:SetActive(false)
	end,3)
end

function LieKunFollow:FlushBossInfo()
	if self.liekun_boss_list then
		local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
		if #self.liekun_boss_list:GetDataList() > 0 then
			self.liekun_boss_list:SetDataList(cross_lieKun_info.boss_id,2)
		else
			self.liekun_boss_list:SetDataList(cross_lieKun_info.boss_id,3)
		end
		self:ShowCountDownView(cross_lieKun_info)
	end
end

function LieKunFollow:ClickChangeBtn()
	self.show_boss = not self.show_boss
	if not self.show_boss then
		self.node_list.SmallMonsterList:SetActive(true)
		local cross_lieKun_info = GuildWGData.Instance:GetLieKunSmallMonsterListCfg()
		self.liekun_smallmonster_list:SetDataList(cross_lieKun_info,3)
		self.node_list.boss_list:SetActive(false)
	else
		self.node_list.boss_list:SetActive(true)
		self.node_list.SmallMonsterList:SetActive(false)
	end
end

function LieKunFollow:FlushScoreInfo()
	if not self:IsOpen() then return end
	local score = GuildWGData.Instance:GetCrossLieKunScore()
	local cur_info,next_info,is_max = GuildWGData.Instance:GetCrossLieKunScoreInfo()
	if not cur_info then
		self.node_list.singlescore_title.text.text = Language.Guild.LieKunTitle_1 .. ToColorStr(string.format("[%d/%d]",score,next_info.score),"#7cffb7")
	elseif not next_info then
		self.node_list.singlescore_title.text.text = Language.Guild.LieKunTitle_1 .. ToColorStr(string.format("[%d/%d]",score,cur_info.score),"#7cffb7")
		next_info = cur_info
	else
		self.node_list.singlescore_title.text.text = Language.Guild.LieKunTitle_1 .. ToColorStr(string.format("[%d/%d]",score,cur_info.score),"#7cffb7")
	end
	self.node_list.has_lingqu:SetActive(is_max == true)

	if not self.score_reward then
		self.score_reward = {}
		local cell
		for i=0,1 do
			cell = ItemCell.New(self.node_list['box_pos_' .. (i + 1)])
			self.score_reward[i] = cell
		end
	end

	for i=0,1 do
		if next_info.reward_item[i] then
			self.score_reward[i]:SetActive(true)
			self.score_reward[i]:SetData(next_info.reward_item[i])
		else
			self.score_reward[i]:SetActive(false)
		end
	end
end

 --------------------------bossitem----------------------------------------------
LieKunFbBossItemRender = LieKunFbBossItemRender or BaseClass(BaseRender)
function LieKunFbBossItemRender:__init()
	self:CreateChild()
end

function LieKunFbBossItemRender:CreateChild()
	self.name_txt = self.node_list.name_txt
	self.time_txt = self.node_list.time_txt
	XUI.AddClickEventListener(self.node_list.BtnSelf,BindTool.Bind(self.OnClickBossRender,self))
end

function LieKunFbBossItemRender:__delete()
	self.name_txt = nil
	self.time_txt = nil
	if CountDownManager.Instance:HasCountDown("act_nextflush_" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("act_nextflush_" .. self.index)
	end
end

function LieKunFbBossItemRender:OnFlush()
	if not self.data then
		return
	end
	local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
	if self.data.boss_id == 0 then
		local boss_cfg = GuildWGData.Instance:GetOneBosscfgByZone(cross_lieKun_info.zone)
		if boss_cfg then
			local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_cfg.boss_id_0]
			if cfg then
				self.node_list.TextDesc.text.text = cfg.name
				self.node_list.LevelDesc.text.text = "LV." .. (cfg.level + cross_lieKun_info.boss_extra_level)
			end
		end
	else
		local boss_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id]
		if boss_cfg then
			self.node_list.TextDesc.text.text = boss_cfg.name
			self.node_list.LevelDesc.text.text = "LV." .. (boss_cfg.level + cross_lieKun_info.boss_extra_level)
		end
	end
	self:UpdateNextFlushTime()
end

function LieKunFbBossItemRender:UpdateNextFlushTime()
	local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
	if CountDownManager.Instance:HasCountDown("act_nextflush_" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("act_nextflush_" .. self.index)
	end
	local next_time = cross_lieKun_info.boss_next_flush_timestamp[self.index] or 0
	if CountDownManager.Instance:HasCountDown("act_nextflush_" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("act_nextflush_" .. self.index)
	end
	self:UpdataLieKunTime(TimeWGCtrl.Instance:GetServerTime(), next_time)
	CountDownManager.Instance:AddCountDown("act_nextflush_" .. self.index, BindTool.Bind1(self.UpdataLieKunTime, self), BindTool.Bind1(self.CompleteLieKunTime, self), next_time, nil, 1)
end

function LieKunFbBossItemRender:UpdataLieKunTime(elapse_time, total_time)
	local time = total_time - elapse_time
	if time > 0 then
		local format_time = TimeUtil.Format2TableDHMS(time)
		local time_str = string.format("%02d:%02d", format_time.min, format_time.s)
		self.node_list.TimeDesc.text.text = ToColorStr(time_str,COLOR3B.RED)
	else
		self:CompleteLieKunTime()
	end
end

function LieKunFbBossItemRender:CompleteLieKunTime()
	local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
	if self.data.index == 1 then
		if cross_lieKun_info.is_main_live_flag == 0 then
			self.node_list.TimeDesc.text.text = ToColorStr(Language.Boss.BossDied,COLOR3B.RED)
		else
			self.node_list.TimeDesc.text.text = Language.Boss.BossRefresh
		end
	else
		self.node_list.TimeDesc.text.text = Language.Boss.BossRefresh
	end
end

-- 点击开始寻路
function LieKunFbBossItemRender:OnClickBossRender()
	if nil ~= self.data then
		local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
		local boss_cfg = GuildWGData.Instance:GetOneBosscfgByZone(cross_lieKun_info.zone)
		if boss_cfg then
			local pos_list = Split(boss_cfg["boss_pos_" .. (self.data.index - 1)], ",")
	        local sence_id = Scene.Instance:GetSceneId()
      		local role = Scene.Instance:GetMainRole()
			local role_x,role_y = role:GetLogicPos()
	        if  role_x == tonumber(pos_list[1]) and role_y == tonumber(pos_list[2]) then
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			else
				GuajiWGCtrl.Instance:StopGuaji()
				MoveCache.SetEndType(MoveEndType.FightByMonsterId)
				GuajiCache.monster_id = self.data.boss_id
				MoveCache.param1 = self.data.boss_id

		        GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
		        	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		        end)

				local range = BossWGData.Instance:GetMonsterRangeByid(self.data.boss_id)
				GuajiWGCtrl.Instance:MoveToPos(sence_id, tonumber(pos_list[1]), tonumber(pos_list[2]), range)
			end
		end
	end
end

LieKunFbSmallMonsterItemRender = LieKunFbSmallMonsterItemRender or BaseClass(BaseRender)
function LieKunFbSmallMonsterItemRender:__init()
	XUI.AddClickEventListener(self.node_list.BtnSelf,BindTool.Bind(self.OnClickBossRender,self))
	self.node_list.TimeDesc.text.text = ""
end

function LieKunFbSmallMonsterItemRender:__delete()
end

function LieKunFbSmallMonsterItemRender:OnFlush()
	if not self.data then
		return
	end
	local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
	local boss_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id]
	if boss_cfg then
		self.node_list.TextDesc.text.text = self.data.name
		self.node_list.LevelDesc.text.text = "LV." .. (boss_cfg.level + cross_lieKun_info.boss_extra_level)
	end
end

-- 点击开始寻路
function LieKunFbSmallMonsterItemRender:OnClickBossRender()
	if nil ~= self.data then
        local sence_id = Scene.Instance:GetSceneId()
  		local role = Scene.Instance:GetMainRole()
		local role_x,role_y = role:GetLogicPos()
        if  role_x == self.data.x_pos and role_y == self.data.y_pos then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		else
			GuajiWGCtrl.Instance:StopGuaji()
			MoveCache.SetEndType(MoveEndType.FightByMonsterId)
			GuajiCache.monster_id = self.data.boss_id
			MoveCache.param1 = self.data.boss_id

	        GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
	        	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	        end)

			local range = BossWGData.Instance:GetMonsterRangeByid(self.data.boss_id)
			GuajiWGCtrl.Instance:MoveToPos(sence_id ,self.data.x_pos,self.data.y_pos, range)
		end
	end
end

---------------------------------------ranktip-------------------------------------------------------
LieKunOutPutTips = LieKunOutPutTips or BaseClass(SafeBaseView)
function LieKunOutPutTips:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/liekun_ui_prefab", "layout_guildrank_liekun")
end

function LieKunOutPutTips:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
	self.select_boss_id = nil
	self.bossmaxhp = nil
end

function LieKunOutPutTips:LoadCallBack()
	self.mask_bg.image.color = Color.New(0, 0, 0, 0)
end

function LieKunOutPutTips:SetData(select_boss_id,bossmaxhp)
	self.select_boss_id = select_boss_id
	self.bossmaxhp = bossmaxhp
	self:Flush()
end

function LieKunOutPutTips:OnFlush()
	if not self.select_boss_id then return end
	local rank_info = GuildWGData.Instance:GetCrossLieKunHurtInfo(self.select_boss_id)
	-- print_error('rank_info---------',rank_info)
	if not rank_info then
	    if self.list_view then
	    	self.list_view:SetDataList({},3)
	    end
	    return
	end
	local data_list = {}
	for k,v in ipairs(rank_info.guild_hurt_list) do
		if v.name ~= "" then
			v.bossmaxhp = self.bossmaxhp
			table.insert(data_list,v)
		end
	end

	if self.list_view then
		self.list_view:SetDataList(data_list,3)
	else
		self.list_view = AsyncListView.New(LieKunGuildOutPutItem, self.node_list.rank_list)
		self.list_view:SetDataList(data_list,3)
	end

end

-- 玩家个人排行
LieKunSingleOutPutTips = LieKunSingleOutPutTips or BaseClass(SafeBaseView)

function LieKunSingleOutPutTips:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/liekun_ui_prefab", "layout_rank_liekun")
end

function LieKunSingleOutPutTips:LoadCallBack()
	self.itemcell = ItemCell.New(self.node_list.item_pos)
	self.mask_bg.image.color = Color.New(0, 0, 0, 0)
end

function LieKunSingleOutPutTips:ReleaseCallBack()
	if self.itemcell then
		self.itemcell:DeleteMe()
		self.itemcell = nil
	end
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
	self.select_boss_id = nil
	self.bossmaxhp = nil
end

function LieKunSingleOutPutTips:SetData(select_boss_id,bossmaxhp)
	self.select_boss_id = select_boss_id
	self.bossmaxhp = bossmaxhp
	self:Flush()
end

function LieKunSingleOutPutTips:OnFlush()
	if not self.select_boss_id then return end
	local role = Scene.Instance:GetMainRole()
	local myname = role.vo.name
	local my_data
	local my_rank
	local rank_info = GuildWGData.Instance:GetCrossLieKunHurtInfo(self.select_boss_id)
	if not rank_info then
		self.node_list.shangbang:SetActive(false)
		self.node_list.weishangbang:SetActive(true)
		return
	end
	local data_list = {}
	for k,v in ipairs(rank_info.hurt_list) do
		if v.name ~= "" then
			v.bossmaxhp = self.bossmaxhp
			v.boss_id = self.select_boss_id
			table.insert(data_list,v)
		end
		if myname == v.name then
			my_data = v
			my_rank = k
		end
	end
	self.node_list.shangbang:SetActive(my_data ~= nil)
	self.node_list.weishangbang:SetActive(my_data == nil)
	if my_data then
		self.node_list.myname.text.text = my_data.name
		local num = my_data.hurt/self.bossmaxhp
		self.node_list.mySlider.slider.value = num
		self.node_list.myslider_num.text.text = math.floor(num*100) .. "%"
		if my_rank == 1 then
			self.itemcell:SetVisible(true)
			local item_id = GuildWGData.Instance:GetLieKunBossRankReward(self.select_boss_id)
			self.itemcell:SetData({item_id = item_id})
		else
			self.itemcell:SetVisible(false)
		end
	end
	if self.list_view then
		self.list_view:SetDataList(data_list,3)
	else
		self.list_view = AsyncListView.New(LieKunSingleOutPutItem, self.node_list.rank_list)
		self.list_view:SetDataList(data_list,3)
	end

end

local RankColor = {
	COLOR3B.RED,
	COLOR3B.PURPLE,
	COLOR3B.ORANGE,
}
LieKunSingleOutPutItem = LieKunSingleOutPutItem or BaseClass(BaseRender)
function LieKunSingleOutPutItem:LoadCallBack()
	self.itemcell = ItemCell.New(self.node_list.item_pos)
end

function LieKunSingleOutPutItem:ReleaseCallBack()
	if self.itemcell then
		self.itemcell:DeleteMe()
		self.itemcell = nil
	end
end

function LieKunSingleOutPutItem:OnFlush()
	if not self.data then return end
	self.node_list.myname.text.text = self.data.name
	local num = self.data.hurt/self.data.bossmaxhp
	self.node_list.mySlider.slider.value = num
	self.node_list.myslider_num.text.text = math.floor(num*100) .. "%"
	if self.index == 1 then
		self.itemcell:SetVisible(true)
		local item_id = GuildWGData.Instance:GetLieKunBossRankReward(self.data.boss_id)
		self.itemcell:SetData({item_id = item_id})
	else
		self.itemcell:SetVisible(false)
	end
end

LieKunGuildOutPutItem = LieKunGuildOutPutItem or BaseClass(BaseRender)

function LieKunGuildOutPutItem:OnFlush()
	if not self.data then return end
	self.node_list.myname.text.text = self.data.name
	local num = self.data.hurt/self.data.bossmaxhp
	self.node_list.mySlider.slider.value = num
	self.node_list.myslider_num.text.text = math.floor(num*100) .. "%"
end
