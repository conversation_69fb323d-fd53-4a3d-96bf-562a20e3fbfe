﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_WrapModeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>(typeof(UnityEngine.WrapMode));
		<PERSON><PERSON>("Once", get_Once, null);
		<PERSON><PERSON>("Loop", get_Loop, null);
		<PERSON><PERSON>("PingPong", get_PingPong, null);
		<PERSON><PERSON>("Default", get_Default, null);
		<PERSON><PERSON>("ClampForever", get_ClampForever, null);
		<PERSON><PERSON>("Clamp", get_Clamp, null);
		<PERSON><PERSON>RegFunction("IntToEnum", IntToEnum);
		<PERSON>.EndEnum();
		TypeTraits<UnityEngine.WrapMode>.Check = CheckType;
		StackTraits<UnityEngine.WrapMode>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.WrapMode arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.WrapMode), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Once(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.WrapMode.Once);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Loop(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.WrapMode.Loop);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PingPong(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.WrapMode.PingPong);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Default(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.WrapMode.Default);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ClampForever(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.WrapMode.ClampForever);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Clamp(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.WrapMode.Clamp);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.WrapMode o = (UnityEngine.WrapMode)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

