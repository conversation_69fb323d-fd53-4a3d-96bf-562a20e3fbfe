CapabilityUpGiftWGData = CapabilityUpGiftWGData or BaseClass()

function CapabilityUpGiftWGData:__init()
	if CapabilityUpGiftWGData.Instance then
		error("[CapabilityUpGiftWGData] Attempt to create singleton twice!")
		return
	end

    CapabilityUpGiftWGData.Instance = self

    self:InitParam()
    self:InitConfig()
end

function CapabilityUpGiftWGData:__delete()
    CapabilityUpGiftWGData.Instance = nil
end

function CapabilityUpGiftWGData:InitParam()
    self.act_state = ACTIVITY_STATUS.CLOSE
    self.cur_seq = -1
    self.end_sceond = -1
    self.buy_flag_list = {}
end

function CapabilityUpGiftWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("capability_up_gift_cfg_auto")
    self.gift_cfg = cfg.gift
end

function CapabilityUpGiftWGData:GetCurGiftCfg()
    return self.gift_cfg[self.cur_seq] or {}
end

function CapabilityUpGiftWGData:SetCapUpGiftInfo(protocol)
    self.cur_seq = protocol.seq
    self.end_sceond = protocol.end_second
	self.buy_flag_list = protocol.gift_buy_flag
end

function CapabilityUpGiftWGData:GetCurSeq()
    return self.cur_seq
end

function CapabilityUpGiftWGData:GetGiftCurIsBuyDone()
    local cur_cfg = self:GetCurGiftCfg()
    if not cur_cfg then
        return false
    end

    local buy_flag = self.buy_flag_list[self.cur_seq] or 1
    local buy_limit = cur_cfg.buy_limit or 1
    return buy_flag >= buy_limit
end

function CapabilityUpGiftWGData:GetCurGiftEndTime()
    return self.end_sceond or 0
end

function CapabilityUpGiftWGData:SetActState(act_state)
    self.act_state = act_state
end

function CapabilityUpGiftWGData:GetActState()
    return self.act_state
end