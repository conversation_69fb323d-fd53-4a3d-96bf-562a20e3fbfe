FightSoulBoneSelectView = FightSoulBoneSelectView or BaseClass(SafeBaseView)
function FightSoulBoneSelectView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function FightSoulBoneSelectView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(788, 460)})
	self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_bone_select_view")
end

function FightSoulBoneSelectView:__delete()

end

function FightSoulBoneSelectView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

    self.meet_list = nil
	self.select_bone = nil
	self.fight_soul_slot = nil
	self.bone_part = nil
end

function FightSoulBoneSelectView:LoadCallBack()
	self.select_bone = nil
    self.node_list.title_view_name.text.text = Language.FightSoul.SelectStuffTitle

    self.list_view = AsyncListView.New(FSBoneSelectItem, self.node_list["list_view"])
    self.list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectItemCB, self))

    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.WearEquip, self))
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))
end

function FightSoulBoneSelectView:SetDataAndOpen(meet_list, fight_soul_slot, bone_part)
    self.meet_list = meet_list
	self.fight_soul_slot = fight_soul_slot
	self.bone_part = bone_part
    self:Open()
end

function FightSoulBoneSelectView:OnFlush()
    if IsEmptyTable(self.meet_list) then
        self:ChangeShowContent(false)
        return
    end

	self:ChangeShowContent(true)
	self.list_view:SetDataList(self.meet_list)
	self.list_view:JumpToIndex(1)
end

function FightSoulBoneSelectView:ChangeShowContent(had_data)
    self.node_list.no_data:SetActive(not had_data)
    self.node_list.had_data:SetActive(had_data)
end

function FightSoulBoneSelectView:WearEquip()
	if self.select_bone == nil then
		return
	end

	FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.BONE_WEAR, self.fight_soul_slot,
											self.bone_part, self.select_bone.bag_index)
	self:Close()
end

-- 选择列表项回调
function FightSoulBoneSelectView:OnSelectItemCB(item, cell_index, is_default, is_click)
	if nil == item or nil == item.data or nil == item.data.bone_data then
		return
	end

	self.select_bone = item.data.bone_data
end
