 ------------------------------------------------------------
LinZhiAchView = LinZhiAchView or BaseClass(SafeBaseView)
local ViewType = {
	XL = 1,    -- 修炼
	SJ = 2,    -- 外观
}

local PART_TYPE = {
	[LINGZHI_SKILL_TYPE.WING] = SHIZHUANG_TYPE.WING,
	[LINGZHI_SKILL_TYPE.FABAO] = SHIZHUANG_TYPE.FABAO,
	[LINGZHI_SKILL_TYPE.JIANZHEN] = SHIZHUANG_TYPE.JIANZHEN,
}

function LinZhiAchView:__init()
	self:SetMaskBg()
	self:LoadConfig()
end

function LinZhiAchView:__delete()

end

function LinZhiAchView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/lingzhi_prefab", "layout_lingzhi_ach")
end

function LinZhiAchView:ReleaseCallBack()
	if self.ach_list then
		self.ach_list:DeleteMe()
		self.ach_list = nil
	end
end


function LinZhiAchView:SetData(data)
	if not data then
		return
	end
	self.data = data

	self:Open()
	self:Flush()
end

function LinZhiAchView:CloseCallBack()
end

function LinZhiAchView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["size"])
	self:SetViewName(Language.LingZhi.AchName)

	for i=1,2 do
		XUI.AddClickEventListener(self.node_list['type_btn_' .. i], BindTool.Bind(self.OnClickBtn, self, i))
	end

	-- 成就列表
	if not self.ach_list then
		self.ach_list = AsyncListView.New(LingZhiAchRender, self.node_list["ach_list"])
	end

	local lingzhi_type = self.data.lingzhi_type or 0
	local remind1 = LingZhiSkillWGData.Instance:GetCJSingleRemind(lingzhi_type, 1)
	local remind2 = LingZhiSkillWGData.Instance:GetCJSingleRemind(lingzhi_type, 2)
	local select_index = 1
	if remind1 then
		select_index = 1
	elseif remind2 then
		select_index = 2
	end
	self:OnClickBtn(select_index,true)
	
end

function LinZhiAchView:OnClickBtn(index,need_reset)
	self.view_type = index
	for i=1,2 do
		self.node_list['type_btn_high_' .. i]:SetActive(index == i)
	end

	self:FlushList(need_reset)
end

function LinZhiAchView:FlushList(need_reset)
	if not self.view_type then
		return
	end

	local lingzhi_type = self.data.lingzhi_type or 0
	local data_list = LingZhiSkillWGData.Instance:GetAchShowList(lingzhi_type, self.view_type)
	self.ach_list:SetDataList(data_list)
	if need_reset then
		self.ach_list:JumpToTop()
	end
end

function LinZhiAchView:FlushOther()
	local lingzhi_type = self.data.lingzhi_type or 0
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	local history_lingzhi = server_info.history_lingzhi or 0

	self.node_list.get_lingzhi_num.text.text = string.format(Language.LingZhi.Tip2, history_lingzhi , Language.LingZhi.NameList[lingzhi_type]) 

	-- 进度
	local xl_num, xl_total_num = LingZhiSkillWGData.Instance:GetAchLZNum(lingzhi_type, ViewType.XL)
	local sj_num, sj_total_num = LingZhiSkillWGData.Instance:GetAchLZNum(lingzhi_type, ViewType.SJ)
	self.node_list.xl_progress.slider.value = xl_num / xl_total_num
	self.node_list.sj_progress.slider.value = sj_num / sj_total_num
	self.node_list.xl_get_num.text.text = xl_num .. "/" .. xl_total_num
	self.node_list.sj_get_num.text.text = sj_num .. "/" .. sj_total_num

	local dan_num = history_lingzhi - xl_num - sj_num
	self.node_list.lz_get_num.text.text = dan_num


	self.node_list.sj_title_txt.text.text = string.format(Language.LingZhi.BtnName_4, Language.LingZhi.NameList[lingzhi_type])
	self.node_list.type_btn_name_2.text.text = string.format(Language.LingZhi.BtnName_4, Language.LingZhi.NameList[lingzhi_type])
	self.node_list.type_btn_name_2_hl.text.text = string.format(Language.LingZhi.BtnName_4, Language.LingZhi.NameList[lingzhi_type])

	self.node_list.type_btn_remind_1:SetActive(LingZhiSkillWGData.Instance:GetCJSingleRemind(lingzhi_type, 1))
	self.node_list.type_btn_remind_2:SetActive(LingZhiSkillWGData.Instance:GetCJSingleRemind(lingzhi_type, 2))
end

function LinZhiAchView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "xiulian_eff" then
			self:PlayEff(self.node_list.xl_progress)
		elseif k == "waiguan_eff" then
			self:PlayEff(self.node_list.sj_progress)
		end
	end

	self:FlushOther()
	local lingzhi_type = self.data.lingzhi_type or 0
	local remind1 = LingZhiSkillWGData.Instance:GetCJSingleRemind(lingzhi_type, 1)
	local remind2 = LingZhiSkillWGData.Instance:GetCJSingleRemind(lingzhi_type, 2)
	if self.view_type == 1 and not remind1 then
		if remind2 then
			self:OnClickBtn(2)
			return
		end
	elseif self.view_type == 2 and not remind2 then
		if remind1 then
			self:OnClickBtn(1)
			return
		end
	end
	self:FlushList()
end

function LinZhiAchView:PlayEff(node)
    local effect_url2 = Ui_Effect.UI_fulingjindutiao
    local bundle_name, asset_name = ResPath.GetEffectUi(effect_url2)
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, node.transform, 1
        ,Vector3(2,0,0),nil,Vector3(2,1,0.45) )
end

LingZhiAchRender = LingZhiAchRender or BaseClass(BaseRender)
function LingZhiAchRender:__init()
end

function LingZhiAchRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function LingZhiAchRender:LoadCallBack()
	self.item = ItemCell.New(self.node_list.ph_item_cell_1)
	self.item:SetNeedItemGetWay(true)
	XUI.AddClickEventListener(self.node_list.btn_item_get,BindTool.Bind(self.BtnGet,self))
end

function LingZhiAchRender:BtnGet()
	local data = self.data 
	if not data then
		return
	end

	local view_type = data.view_type
	local lingzhi_type = data.lingzhi_type
	local index = data.cfg.index

	if view_type == ViewType.XL then
		LingZhiWGCtrl.Instance:CSLingZhiSkillReq(LINGZHI_SKILL_OPERA_TYPE.LINGZHI_SKILL_FETCH_GRADE_CHENGJIU,
			lingzhi_type, index)
	else
		LingZhiWGCtrl.Instance:CSLingZhiSkillReq(LINGZHI_SKILL_OPERA_TYPE.LINGZHI_SKILL_FETCH_APPERANCE_CHENGJIU,
			lingzhi_type, index)
	end
end

function LingZhiAchRender:OnFlush()
	local data = self.data 
	if not data then
		return
	end

	local lingzhi_type = data.lingzhi_type
	local view_type = data.view_type
	local index = data.cfg.index
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return
	end


	local flag_list
	local remind = false
	if view_type == ViewType.XL then
		flag_list = server_info.grade_chengjiu_flag
	else
		flag_list = server_info.appe_chengjiu_flag
	end

	local is_act = flag_list[index] == 1

	if view_type == ViewType.XL then 
		local need_grade = data.cfg.grade
		local has_grade = server_info.xiulian_grade

		remind = has_grade >= need_grade and not is_act
	else
		local sz_is_act = LingZhiSkillWGData.Instance:GetWGHasAct(data.cfg, lingzhi_type)
		remind = sz_is_act and not is_act
	end

	self.item:SetData({item_id = LINGZHI_SKILL_XUNI_ITEM[lingzhi_type], num = data.cfg.add_lingzhi})

	local chengjiu_dec = ""
	if view_type == ViewType.XL then
		local xiulian_cfg = LingZhiSkillWGData.Instance:GetXiuLianCfg(lingzhi_type, data.cfg.grade)
		local chengjiu_dec_str = Language.LingZhi.XLChengJiuDesc[lingzhi_type]
		local color = ITEM_COLOR[xiulian_cfg.color]
		chengjiu_dec = string.format(chengjiu_dec_str,color,xiulian_cfg.grade_name,data.cfg.grade)
	else
		local waiguan_id = data.cfg.waiguan_id
		local waiguan_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(waiguan_id)
		if not IsEmptyTable(waiguan_cfg) then
			local waiguan_item_cfg = ItemWGData.Instance:GetItemConfig(waiguan_id)
			local is_special = Language.LingZhi.ChengJiuSpecial[waiguan_cfg.is_special]
			local color = ITEM_COLOR[waiguan_item_cfg.color]
			local chengjiu_dec_str = Language.LingZhi.SJChengJiuDesc[lingzhi_type]
			chengjiu_dec = string.format(chengjiu_dec_str,is_special,color,waiguan_cfg.name)
			
		end
	end
	self.node_list.rich_item_desc.text.text = chengjiu_dec

	self.node_list.btn_item_get:SetActive(remind)
	self.node_list.no_act:SetActive(not is_act and not remind)
	self.node_list.has_act:SetActive(is_act)
end



