function FightSoulView:InitBoneView()
    self.bone_equip_list = {}
    for bone_part = 0, FIGHT_SOUL_BONE_TYPE.MAX - 1 do
        self.bone_equip_list[bone_part] = FSBoneEquipItem.New(self.node_list.b_equip_list:FindObj("item_" .. bone_part))
        self.bone_equip_list[bone_part]:SetIndex(bone_part)
        self.bone_equip_list[bone_part]:SetClickCallBack(BindTool.Bind(self.OnClickBonePartCB, self))
    end

    self.bone_attr_list = {}
    for i = 1, 4 do
        self.bone_attr_list[i] = self.node_list.b_attr_part:FindObj("b_attr_" .. i)
    end
    --[[
    self.bone_suit_btn_list = {}
    for i = 1, FIGHT_SOUL_NUM.MAX_ACT_SUIT do
        self.bone_suit_btn_list[i] = FSBoneSuitBtnItem.New(self.node_list.b_suit_btn_part:FindObj("suit_" .. i))
        self.bone_suit_btn_list[i]:SetIndex(i)
        self.bone_suit_btn_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickBoneSuitCB, self))
    end
    --]]

    self.suit_skill_desc_list = {}
    for i = 1, FIGHT_SOUL_NUM.MAX_ACT_SUIT do
        self.suit_skill_desc_list[i] = FSSuitSkillDescItem.New(self.node_list.b_suit_skill_desc_list:FindObj("suit_skill_" .. i))
        self.suit_skill_desc_list[i]:SetIndex(i)
    end

    if self.bone_suit_list == nil then
        self.bone_suit_list = AsyncListView.New(FSSuitActItem, self.node_list["b_suit_list"])
        self.node_list["b_suit_list"].scroll_rect.vertical = false
    end

    XUI.AddClickEventListener(self.node_list.b_btn_strength, BindTool.Bind(self.OpenBoneStrength, self))
    XUI.AddClickEventListener(self.node_list.b_btn_compose, BindTool.Bind(self.OpenBoneCompose, self))
    XUI.AddClickEventListener(self.node_list.b_btn_call, BindTool.Bind(self.OpenSiXiangCall, self))
    XUI.AddClickEventListener(self.node_list.b_skill_part, BindTool.Bind(self.ClickTrainSkill, self))
    XUI.AddClickEventListener(self.node_list.btn_suit_tip, BindTool.Bind(self.OpenSuitTips, self))

    self.node_list.b_btn_call:SetActive(FunOpen.Instance:GetFunIsOpenedByTabName("sixiang_call_sx"))
    self.node_list.b_btn_call_remind:SetActive(RemindManager.Instance:GetRemind(RemindName.SiXiangCall_SXSM) > 0)

    -- self:InitBoneTweenPos()
end

function FightSoulView:DeleteBoneView()
    if self.bone_equip_list ~= nil then
        for k,v in pairs(self.bone_equip_list) do
            v:DeleteMe()
        end
        self.bone_equip_list = nil
    end

    --[[
    if self.bone_suit_btn_list ~= nil then
        for k,v in pairs(self.bone_suit_btn_list) do
            v:DeleteMe()
        end
        self.bone_suit_btn_list = nil
    end
    --]]


    if self.suit_skill_desc_list ~= nil then
        for k,v in pairs(self.suit_skill_desc_list) do
            v:DeleteMe()
        end
        self.suit_skill_desc_list = nil
    end

    if self.bone_suit_list ~= nil then
        self.bone_suit_list:DeleteMe()
        self.bone_suit_list = nil
    end

    self:CleanBoneCDTime()
    self.cache_b_fs_type = nil
    self.cache_b_show_part = nil
end

function FightSoulView:OnClickBonePartCB(cell)
    if cell == nil or cell.data == nil then
        return
    end

    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) then
        return
    end

    local is_wear = fs_data:GetIsWear()
    if not is_wear then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.UpLevelLimitDesc1)
        return
    end

    local no_wear = IsEmptyTable(cell.data.bone_data)
    local fs_type = fs_data:GetType()
    local bone_part = cell.data.bone_part
    local meet_list = FightSoulWGData.Instance:GetBonePartCanWearList(fs_type, bone_part)
    if no_wear and IsEmptyTable(meet_list) then
        -- 获取途径
        local show_id = FightSoulWGData.Instance:GetBonePartPreviewId(bone_part, fs_type)
        local item_data = SiXiangCallWGData.Instance:GetSummonItemDataByItemId(show_id)
        TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_NORMAL, nil)
        return
    end

    if #meet_list > 0 then
        local have_better = FightSoulWGData.Instance:GetPartHaveBetterBone(fs_type, bone_part)
        if no_wear or have_better then
            -- 装备列表
            FightSoulWGCtrl.Instance:OpenBoneSelectView(meet_list, fs_data:GetSlotIndex(), bone_part)
            return
        end
    end

    -- 物品提示框
    TipWGCtrl.Instance:OpenItem(cell.data.bone_data, ItemTip.FROM_FIGHT_SOUL_BONE_WEAR)
end

function FightSoulView:OnClickBoneSuitCB(cell)
    if cell == nil or cell.data == nil then
        return
    end

    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) then
        return
    end

    local suit_data = FightSoulWGData.Instance:GetBoneTotalSuitData(fs_data:GetType())
    FightSoulWGCtrl.Instance:OpenSuitTips(suit_data)
end

function FightSoulView:FlushBoneView()
    -- self:DoBoneTween()
    self:FlushBoneEquipList()
    self:FlushBoneSuitList()
    self:FlushBoneStrengthBtn()
    self:FlushBoneComposeBtn()
end

function FightSoulView:InitBoneTweenPos()
    RectTransform.SetAnchoredPositionXY(self.node_list.b_skill_desc_part.rect, -82, -140)
    RectTransform.SetAnchoredPositionXY(self.node_list.b_right_part.rect, 320, 0)
end

function FightSoulView:DoBoneTween()
    if not self.do_bone_tween_flag then
        return
    end
    self.do_bone_tween_flag = false

    UITween.CleanAllTween(GuideModuleName.FightSoulView)
    UITween.FakeHideShow(self.node_list.b_right_tween_node)
    -- self:InitBoneTweenPos()
    -- self:InitSlotListTween()
    -- self:DoBoneTweenMove()
end

function FightSoulView:CleanBoneCDTime()
    if self.Bone_tween_quest ~= nil then
        GlobalTimerQuest:CancelQuest(self.Bone_tween_quest)
        self.Bone_tween_quest = nil
    end
end

function FightSoulView:DoBoneTweenMove()
    if not self.list_load_over then
        if self.Bone_tween_quest == nil then
            self.Bone_tween_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.DoBoneTweenMove, self), 0.1)
        end
        return
    else
        self:CleanBoneCDTime()
    end

    self:MoveSlotListTween()
    local tween_info = UITween_CONSTS.SiXiangSys
    self.node_list.b_right_part.rect:DOAnchorPos(Vector2(-10, 0), tween_info.MoveTime)
    self.node_list.b_skill_desc_part.rect:DOAnchorPos(Vector2(-82, 11), tween_info.MoveTime)
    ReDelayCall(self, function()
        UITween.AlphaShow(GuideModuleName.FightSoulView, self.node_list.b_right_tween_node, 0, 1, tween_info.AlphaTime)
    end, tween_info.MoveTime, "fs_bone_tween")
end

-- 刷新魂骨装备列表
function FightSoulView:FlushBoneEquipList()
    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) or self.bone_equip_list == nil then
        return
    end

    -- 魂骨装备
    local equip_list = fs_data:GetBoneEquipList()
    for k,v in pairs(self.bone_equip_list) do
        v:SetData(equip_list[k], fs_data:GetType())
    end

    -- 属性列表
    local attr_list = fs_data:GetBoneEquipAttr()
    local attr_data, is_per, per_desc, name_str, value_str
    for k,v in pairs(self.bone_attr_list) do
        attr_data = attr_list[k]
        if attr_data then
            is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_data.attr_name)
            per_desc = is_per and "%" or ""
            name_str = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_data.attr_name)
            value_str = is_per and attr_data.attr_value / 100 or attr_data.attr_value
            value_str = value_str .. per_desc
            v.text.text = string.format("%s   %s", name_str, ToColorStr(value_str, COLOR3B.BLUE_TITLE))
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end

    local is_wear = fs_data:GetIsWear()
    local had_wear_equip = fs_data:GetHaveWearEquip()
    self.node_list.b_attr_no_show_desc:SetActive(not is_wear or not had_wear_equip)
    if not is_wear then
        self.node_list.b_attr_no_show_desc.text.text = Language.FightSoul.UpLevelLimitDesc1
    elseif not had_wear_equip then
        self.node_list.b_attr_no_show_desc.text.text = Language.FightSoul.UpLevelLimitDesc4
    end

     -- 战力
    self:FlushSlotCapability()
end

function FightSoulView:FlushSlotCapability()
    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) then
        return
    end

    self.node_list.b_cap_value.text.text = FightSoulWGData.Instance:GetSlotBoneCapability(fs_data:GetSlotIndex())
end

-- 刷新套装按钮逻辑
function FightSoulView:FlushBoneSuitList()
    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) then
        return
    end
    --[[
    local suit_list = fs_data:GetBoneSuitList()
    for k,v in pairs(self.bone_suit_btn_list) do
        v:SetData(suit_list[k], fs_data:GetType())
    end
    --]]

    -- 技能
    local is_wear = fs_data:GetIsWear()
    local slot = fs_data:GetSlotIndex()
    self.node_list.b_skill_no_show_desc:SetActive(not is_wear)
    self.node_list.b_skill_part:SetActive(is_wear)
    self.node_list.b_skill_desc_scroll_view:SetActive(is_wear)
    if is_wear then
        local skill_id, skill_level = fs_data:GetSkillIdAndLevel()
        --self.node_list.b_skill_level.text.text = skill_level

        local s_bundle, s_asset = FightSoulWGData.Instance:GetSkillIconResByItemId(fs_data:GetItemId())
        self.node_list.b_skill_icon:SetActive(false)
        self.node_list.b_skill_icon.image:LoadSprite(s_bundle, s_asset, function()
            self.node_list.b_skill_icon:SetActive(true)
            self.node_list.b_skill_icon.image:SetNativeSize()
        end)
    end

    if not is_wear then
        self.node_list.b_no_act_suit:SetActive(true)
        self.node_list.b_suit_list:SetActive(false)
        return
    end

    -- 主动技能激活状态
    --[[
    local suit_data = FightSoulWGData.Instance:GetAutoSkillActSuitDataBySlot(slot)
    local auto_skill_level = suit_data and suit_data.suit_type or 0
    for i = 1, FIGHT_SOUL_NUM.MAX_SUIT_NUM do
        local icon_node = self.node_list["b_suit_skill_level" .. i]
        if icon_node then
            local res = auto_skill_level >= i and "sx_gai_jingsezhu" or "sx_gai_huisezhu"
            local bundle, asset = ResPath.GetFightSoulImg(res)
            icon_node.image:LoadSprite(bundle, asset, function()
                icon_node.image:SetNativeSize()
            end)
        end
    end
    ]]

    -- 主动技能名
    local skill_name = FightSoulWGData.Instance:GetSuitAutoSkillNameBySlot(slot)
    self.node_list.b_skill_name.text.text = skill_name
    local skill_desc_list = FightSoulWGData.Instance:GetSuitAutoSkillActDescBySlot(slot)
    if skill_desc_list[0] then
        self.node_list.b_main_skill_desc.text.text = skill_desc_list[0].desc
    end

    for k,v in ipairs(self.suit_skill_desc_list) do
        v:SetData(skill_desc_list[k])
    end

    local suit_act_list = FightSoulWGData.Instance:GetNewBoneShowSuitInfoBySlot(slot)
    if IsEmptyTable(suit_act_list) then
        self.node_list.b_no_act_suit:SetActive(true)
        self.node_list.b_suit_list:SetActive(false)
    else
        self.node_list.b_no_act_suit:SetActive(false)
        self.node_list.b_suit_list:SetActive(true)
        self.bone_suit_list:SetDataList(suit_act_list)
    end
end

-- 刷新强化按钮逻辑
function FightSoulView:FlushBoneStrengthBtn()
    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) or fs_data:IsLock() or not fs_data:GetIsWear() then
        self.node_list.b_btn_strength_remind:SetActive(false)
    	return
    end

    local remind = FightSoulWGData.Instance:GetBoneSlotStrengthRemind(fs_data:GetSlotIndex())
    self.node_list.b_btn_strength_remind:SetActive(remind)
end

-- 刷新合成按钮逻辑
function FightSoulView:FlushBoneComposeBtn()
    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) or fs_data:IsLock() or not fs_data:GetIsWear() then
        self.node_list.b_btn_compose_remind:SetActive(false)
    	return
    end

    local remind = FightSoulWGData.Instance:GetFSTypeBoneComposeRemind(fs_data:GetType())
    self.node_list.b_btn_compose_remind:SetActive(remind)
end

-- 强化按钮点击
function FightSoulView:OpenBoneStrength()
    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) then
    	return
    end

    if not fs_data:GetIsWear() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.UpLevelLimitDesc1)
        return
    end

    if not fs_data:GetHaveWearEquip() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.UpLevelLimitDesc4)
        return
    end

    FightSoulWGCtrl.Instance:OpenBoneStrengthView(fs_data:GetSlotIndex())
end

-- 融合按钮点击
function FightSoulView:OpenBoneCompose()
    local fs_data = self:GetCurFightSoulSlot()
    if IsEmptyTable(fs_data) then
    	return
    end

    if not fs_data:GetIsWear() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.UpLevelLimitDesc1)
        return
    end

    local bag_list = FightSoulWGData.Instance:GetFSTypeBoneSortBagListByType(fs_data:GetType())
    if IsEmptyTable(bag_list) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.BagNoBoneLimitDesc)
        return
    end

    FightSoulWGCtrl.Instance:OpenBoneComposeView(fs_data:GetType(), bag_list)
end

function FightSoulView:OpenSuitTips()
    local rule_tip = RuleTip.Instance
    rule_tip:SetTitle(Language.FightSoul.SuitTipsTitle)
    rule_tip:SetContent(Language.FightSoul.SuitTipsContent, nil, nil, nil, true)
end
