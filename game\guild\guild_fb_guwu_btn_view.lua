GuildFbGuWuBtnView = GuildFbGuWuBtnView or BaseClass(SafeBaseView)

function GuildFbGuWuBtnView:__init()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_shouhu_guwu_btn_view")
end

function GuildFbGuWuBtnView:__delete()
end

function GuildFbGuWuBtnView:ReleaseCallBack()
	if self.mainui_open_complete_handle then
	 	GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
	end
	self.mainui_open_complete_handle = nil	

	if self.main_top_arrow_event then
		GlobalEventSystem:UnBind(self.main_top_arrow_event)
		self.main_top_arrow_event = nil
	end 
end

function GuildFbGuWuBtnView:LoadCallBack()
end

function GuildFbGuWuBtnView:ShowIndexCallBack()
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
	 	self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
end

function GuildFbGuWuBtnView:InitCallBack()
	XUI.AddClickEventListener(self.node_list.btn_inspire, BindTool.Bind1(self.OnClickOpenGuWuView, self))
	MainuiWGCtrl.Instance:AddBtnToFbIconGroup2Line(self.node_list.btn_inspire)
end

function GuildFbGuWuBtnView:OpenCallBack()
	if not self.main_top_arrow_event then
		self.main_top_arrow_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.TopPanelAni, self))
	end
end

function GuildFbGuWuBtnView:TopPanelAni( ison )
	local max_move ,min_move = 0 , -173
	local move_vaule = ison == true and max_move or min_move
	if nil == self.node_list.root_layout_inspire then
		return 
	end
	local tween = self.node_list.root_layout_inspire.rect:DOAnchorPosY(move_vaule, 1)
	self.node_list.root_layout_inspire.canvas_group.alpha = 0
	self.node_list.root_layout_inspire.canvas_group:DoAlpha(0, 1, 1)
	-- tween:SetEase(DG.Tweening.Ease.OutBack)
	-- tween:OnUpdate(function()
	-- 	self.node_list.root_layout_inspire.canvas_group.alpha = ((max_move - min_move) - (self.node_list.root_layout_inspire.rect.anchoredPosition.y - min_move)) / (max_move - min_move)
	-- end)
end

function GuildFbGuWuBtnView:CloseCallBack()
	if self.main_top_arrow_event then
		GlobalEventSystem:UnBind(self.main_top_arrow_event)
		self.main_top_arrow_event = nil
	end

	self.node_list.btn_inspire.transform:SetParent(self.root_node_transform,false)
end

function GuildFbGuWuBtnView:OnFlush()
	local total_ef = GuildWGData.Instance:GetCurGuWuTotalEffect()
	self.node_list.guwu_text.text.text = string.format(Language.Guild.SHMainBtnText, total_ef or 0) 
end

function GuildFbGuWuBtnView:OnClickOpenGuWuView()
	GuildWGCtrl.Instance:OpenGuWuView()
end