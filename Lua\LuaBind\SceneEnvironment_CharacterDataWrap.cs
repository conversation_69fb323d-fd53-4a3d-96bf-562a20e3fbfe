﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class SceneEnvironment_CharacterDataWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(SceneEnvironment.CharacterData), typeof(UnityEngine.ScriptableObject));
		<PERSON><PERSON>Function("New", _CreateSceneEnvironment_CharacterData);
		<PERSON><PERSON>unction("__eq", op_Equality);
		L<PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("avatarLightDirType", get_avatarLightDirType, set_avatarLightDirType);
		<PERSON><PERSON>("avatarLightDirWithCamera", get_avatarLightDirWithCamera, set_avatarLightDirWithCamera);
		<PERSON><PERSON>("avatarLightColor", get_avatarLightColor, set_avatarLightColor);
		<PERSON><PERSON>("avatarSkinLightColor", get_avatarSkinLightColor, set_avatarSkinLightColor);
		<PERSON><PERSON>("avatarHairLightColor", get_avatarHairLightColor, set_avatarHairLightColor);
		L.RegVar("avatarLightIntensity", get_avatarLightIntensity, set_avatarLightIntensity);
		L.RegVar("avatarReflectTex", get_avatarReflectTex, set_avatarReflectTex);
		L.RegVar("avatarAmbientColor", get_avatarAmbientColor, set_avatarAmbientColor);
		L.RegVar("avatarReflectColor", get_avatarReflectColor, set_avatarReflectColor);
		L.RegVar("avatarHairAmbientColor", get_avatarHairAmbientColor, set_avatarHairAmbientColor);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateSceneEnvironment_CharacterData(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				SceneEnvironment.CharacterData obj = new SceneEnvironment.CharacterData();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: SceneEnvironment.CharacterData.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarLightDirType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			SceneEnvironment.AvatarLightDirType ret = obj.avatarLightDirType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarLightDirType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarLightDirWithCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Vector3 ret = obj.avatarLightDirWithCamera;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarLightDirWithCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color ret = obj.avatarLightColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarSkinLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color ret = obj.avatarSkinLightColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarSkinLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarHairLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color ret = obj.avatarHairLightColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarHairLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarLightIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			float ret = obj.avatarLightIntensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarLightIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarReflectTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Cubemap ret = obj.avatarReflectTex;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarReflectTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarAmbientColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color ret = obj.avatarAmbientColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarAmbientColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarReflectColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color ret = obj.avatarReflectColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarReflectColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_avatarHairAmbientColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color ret = obj.avatarHairAmbientColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarHairAmbientColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarLightDirType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			SceneEnvironment.AvatarLightDirType arg0 = (SceneEnvironment.AvatarLightDirType)ToLua.CheckObject(L, 2, typeof(SceneEnvironment.AvatarLightDirType));
			obj.avatarLightDirType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarLightDirType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarLightDirWithCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.avatarLightDirWithCamera = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarLightDirWithCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.avatarLightColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarSkinLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.avatarSkinLightColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarSkinLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarHairLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.avatarHairLightColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarHairLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarLightIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.avatarLightIntensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarLightIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarReflectTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Cubemap arg0 = (UnityEngine.Cubemap)ToLua.CheckObject(L, 2, typeof(UnityEngine.Cubemap));
			obj.avatarReflectTex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarReflectTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarAmbientColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.avatarAmbientColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarAmbientColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarReflectColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.avatarReflectColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarReflectColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_avatarHairAmbientColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.CharacterData obj = (SceneEnvironment.CharacterData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.avatarHairAmbientColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index avatarHairAmbientColor on a nil value");
		}
	}
}

