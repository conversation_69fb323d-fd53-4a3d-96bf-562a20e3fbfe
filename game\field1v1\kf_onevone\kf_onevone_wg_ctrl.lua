--新 跨服1v1
require("game/field1v1/kf_onevone/field1v1_kf_onevone_view")
require("game/field1v1/kf_onevone/kf_onevone_wg_data")
require("game/field1v1/kf_onevone/kf_onevone_end_view")
require("game/field1v1/kf_onevone/kf_onevone_head_view")
require("game/field1v1/kf_onevone/kf_onevone_jingcai_view")
require("game/field1v1/kf_onevone/kf_onevone_matching_view")
require("game/field1v1/kf_onevone/kf_onevone_duizhen_view")
require("game/field1v1/kf_onevone/kf_onevone_reward_view")
require("game/field1v1/kf_onevone/kf_onevone_season_reward")
require("game/field1v1/kf_onevone/kf_onevone_task_view")
require("game/field1v1/kf_onevone/kf_onevone_result_view")

Field1v1WGCtrl = Field1v1WGCtrl or BaseClass(BaseWGCtrl)



function Field1v1WGCtrl:InitKFOneVOneWGCtrl()
	--新 跨服1v1
	self.kf_onevone_end_view = KFOneVOneEndView.New(GuideModuleName.KFOneVOneEndView)
	self.kf_onevone_head_view = KFOneVOneHeadView.New(GuideModuleName.KFOneVOneHeadView)
	self.kf_onevone_jingcai_view = KFOneVOneJingCaiView.New(GuideModuleName.KFOneVOneJingCaiView)
	self.kf_onevone_matching_view = KFOneVOneMatchingView.New(GuideModuleName.KFOneVOneMatchingView)
	self.kf_onevone_duizhen_view = KFOneVOneDuiZhenView.New(GuideModuleName.KFOneVOneDuiZhenView)
	self.kf_onevone_reward_view = KFOneVOneRewardView.New(GuideModuleName.KFOneVOneRewardView)
	self.kf_onevone_season_reward = KFOneVOneSeasonReward.New(GuideModuleName.KFOneVOneSeasonReward)
	self.kf_onevone_task_view = KFOneVOneTaskView.New(GuideModuleName.KFOneVOneTaskView)
	self.kf_onevone_result_view = KFOneVOneResultView.New(GuideModuleName.KFOneVOneResultView)

	self.kf_onevone_data = KFOneVOneWGData.New()

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

	-- self.scene_change_complete_event = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneChangeComplete, self))
	
	self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind(self.OnFlyDownEnd,self))


	self:RegisterKFOneVOneProtocols()
end

function Field1v1WGCtrl:DeleteKFOneVOneWGCtrl()
	if self.kf_onevone_end_view then
		self.kf_onevone_end_view:DeleteMe()
		self.kf_onevone_end_view  = nil
	end

	if self.kf_onevone_head_view then
		self.kf_onevone_head_view:DeleteMe()
		self.kf_onevone_head_view  = nil
	end

	if self.kf_onevone_jingcai_view then
		self.kf_onevone_jingcai_view:DeleteMe()
		self.kf_onevone_jingcai_view  = nil
	end

	if self.kf_onevone_matching_view then
		self.kf_onevone_matching_view:DeleteMe()
		self.kf_onevone_matching_view  = nil
	end

	if self.kf_onevone_duizhen_view then
		self.kf_onevone_duizhen_view:DeleteMe()
		self.kf_onevone_duizhen_view  = nil
	end

	if self.kf_onevone_reward_view then
		self.kf_onevone_reward_view:DeleteMe()
		self.kf_onevone_reward_view  = nil
	end

	if self.kf_onevone_season_reward then
		self.kf_onevone_season_reward:DeleteMe()
		self.kf_onevone_season_reward  = nil
	end

	if self.kf_onevone_task_view then
		self.kf_onevone_task_view:DeleteMe()
		self.kf_onevone_task_view  = nil
	end

	if self.kf_onevone_result_view then
		self.kf_onevone_result_view:DeleteMe()
		self.kf_onevone_result_view  = nil
	end

	if self.kf_onevone_data then
		self.kf_onevone_data:DeleteMe()
		self.kf_onevone_data  = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	-- GlobalEventSystem:UnBind(self.scene_change_complete_event)

	GlobalEventSystem:UnBind(self.fly_down_end_event)
	self.fly_down_end_event = nil

	if CountDownManager.Instance:HasCountDown("open_onevone_matching") then
		CountDownManager.Instance:RemoveCountDown("open_onevone_matching")
	end

end

function Field1v1WGCtrl:RegisterKFOneVOneProtocols()
	self:RegisterProtocol(CSCross1V1Opera)
	self:RegisterProtocol(SCCross1V1MatchSucc, "OnSCCross1V1MatchSucc")
	self:RegisterProtocol(SCCross1V1KnockoutMatchInfo, "OnSCCross1V1KnockoutMatchInfo")
	self:RegisterProtocol(SCCross1V1ScoreRank, "OnSCCross1V1ScoreRank")
	self:RegisterProtocol(SCCross1V1PersonInfo, "OnSCCross1V1PersonInfo")
	self:RegisterProtocol(SCCross1V1MatchInfo, "OnSCCross1V1MatchInfo")
	self:RegisterProtocol(SCCross1V1FightResult, "OnSCCross1V1FightResult")

	self:RegisterProtocol(SCCross1v1FightStart, "OnCross1v1FightStart")
	self:RegisterProtocol(SCCross1V1WinnerInfo, "OnSCCross1V1WinnerInfo")
end

function Field1v1WGCtrl:SendCSCross1V1Opera(opera_type,param1,param2,param3)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCross1V1Opera)
	send_protocol.opera_type = opera_type
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol.param3 = param3 or 0
	send_protocol:EncodeAndSend()
end

function Field1v1WGCtrl:OnSCCross1V1MatchSucc(protocol)
	--匹配成功后 请求进入PK场景
	local enter_fun = function ()
		local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_ENTER_PK_SCENE
		Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type)
	end
	self.kf_onevone_data:SetSCCross1V1MatchSucc(protocol)
	self:OpenOneVOneMatchingView()

	if CountDownManager.Instance:HasCountDown("open_onevone_matching") then
		CountDownManager.Instance:RemoveCountDown("open_onevone_matching")
	end
	CountDownManager.Instance:AddCountDown("open_onevone_matching", BindTool.Bind1(self.OnevoneMatchingUpdate, self), BindTool.Bind1(self.OnevoneMatchingComplete, self), nil, 2, 1)

end

function Field1v1WGCtrl:OnevoneMatchingUpdate(elapse_time, total_time)

end

function Field1v1WGCtrl:OnevoneMatchingComplete()
	--匹配成功后 请求进入PK场景
	local prematch_round = KFOneVOneWGData.Instance:GetPermatchRound()
	local other_cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
	local prematch_max_round = other_cfg.prematch_max_round
	if prematch_round == prematch_max_round then
		self.enter_prematch_max_round = true
	end
	local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_ENTER_PK_SCENE
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type)
end

function Field1v1WGCtrl:OnSCCross1V1KnockoutMatchInfo(protocol)
	self.kf_onevone_data:SetSCCross1V1KnockoutMatchInfo(protocol)
	if self.kf_onevone_duizhen_view:IsOpen() then
		self.kf_onevone_duizhen_view:Flush()
	end

	if self.kf_onevone_reward_view:IsOpen() then
		self.kf_onevone_reward_view:Flush()
	end
	-- if self.kf_onevone_jingcai_view:IsOpen() then
	-- 	self.kf_onevone_jingcai_view:Flush()
	-- end
	if self.kf_onevone_task_view:IsOpen() then
		self.kf_onevone_task_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.ActOneVSOnebArena)
--	MainuiWGCtrl.Instance:FlushKfPKRenPoint()

	--播放晋级动画
	local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local enter_scene_knockout = KFOneVOneWGData.Instance:GetEnterSceneKnockout()
	if match_state == KFOneVOneWGData.MatchState.TaoTai and enter_scene_knockout then
		Field1v1WGCtrl.Instance:PlayJingJiTips(match_state,match_state,enter_scene_knockout)
	end

	local scene_type = Scene.Instance:GetSceneType()
	local is_main_role_fly_down = KFOneVOneWGData.Instance:GetMainRoleFlyDownFalg()
	if scene_type == SceneType.Kf_OneVOne_Prepare and not self.open_onevone_result and is_main_role_fly_down then
		local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
		local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
		if match_state == KFOneVOneWGData.MatchState.TaoTai and knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then
			ViewManager.Instance:Open(GuideModuleName.KFOneVOneResultView)
			self.open_onevone_result = true
		end
	end
end

function Field1v1WGCtrl:OnSCCross1V1ScoreRank(protocol)
	self.kf_onevone_data:SetSCross1V1ScoreRank(protocol)
	if self.view:IsOpen() then
		self.view:Flush(KUAFU_TAB_TYPE.ONEVSONE)
	end

	if self.kf_onevone_duizhen_view:IsOpen() then
		self.kf_onevone_duizhen_view:Flush()
	end

	if self.kf_onevone_reward_view:IsOpen() then
		self.kf_onevone_reward_view:Flush()
	end

	if self.kf_onevone_task_view:IsOpen() then
		self.kf_onevone_task_view:Flush()
	end

	local can_paly_16_qiang_anim = KFOneVOneWGData.Instance:GetCanPlay16QiangAnim()
	if self.yuxuan_in_yuxuan_end and can_paly_16_qiang_anim then
		local is_main_role_fly_down = KFOneVOneWGData.Instance:GetMainRoleFlyDownFalg()
		if is_main_role_fly_down then
			if can_paly_16_qiang_anim and self.enter_prematch_max_round then
				Field1v1WGCtrl.Instance:PlayJingJiTips(KFOneVOneWGData.MatchState.YuXuan,KFOneVOneWGData.MatchState.YuXuanEnd)
			end
		else
			self.score_rank_paly_16qiang_anim = true
		end
	end

end

function Field1v1WGCtrl:OnSCCross1V1PersonInfo(protocol)
	self.kf_onevone_data:SetSCross1V1PersonInfo(protocol)
	if self.kf_onevone_task_view:IsOpen() then
		self.kf_onevone_task_view:Flush()
	end

	if self.view:IsOpen() then
		self.view:Flush(KUAFU_TAB_TYPE.ONEVSONE)
	end

	if self.kf_onevone_duizhen_view:IsOpen() then
		self.kf_onevone_duizhen_view:Flush()
	end

	if self.kf_onevone_reward_view:IsOpen() then
		self.kf_onevone_reward_view:Flush()
	end
	-- if self.kf_onevone_jingcai_view:IsOpen() then
	-- 	self.kf_onevone_jingcai_view:Flush()
	-- end
	if self.kf_onevone_season_reward:IsOpen() then
		self.kf_onevone_season_reward:Flush()
	end
	RemindManager.Instance:Fire(RemindName.ActOneVSOnebArena)
	--MainuiWGCtrl.Instance:FlushKfPKRenPoint()
end

function Field1v1WGCtrl:OnSCCross1V1MatchInfo(protocol)
	local old_knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local old_match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	self.kf_onevone_data:SetSCross1V1MatchInfo(protocol)
	local new_knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local new_match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()

	local is_prematch_all_matched = protocol.is_prematch_all_matched

	if self.kf_onevone_task_view:IsOpen() then
		self.kf_onevone_task_view:Flush()
	end

	local is_over = KFOneVOneWGData.Instance:GetMyInKnockoutIsOver()
	if new_match_state == KFOneVOneWGData.MatchState.TaoTai and not is_over and old_knockout_state ~= new_knockout_state then
		KFOneVOneWGData.Instance:SetEnterSceneKnockout(new_knockout_state)
	end

	if old_match_state == KFOneVOneWGData.MatchState.YuXuan and new_match_state == KFOneVOneWGData.MatchState.YuXuanEnd then
		self.yuxuan_in_yuxuan_end = true
		local prematch_round = protocol.prematch_round
		local other_cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
		local prematch_max_round = other_cfg.prematch_max_round
		if prematch_round == prematch_max_round and is_prematch_all_matched == 1 then
			self.enter_prematch_max_round = true
			KFOneVOneWGData.Instance:SetCanPlay16QiangAnim(true)
		end

		Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_SCORE_RANK)

	end

	if new_match_state == KFOneVOneWGData.MatchState.TaoTai and new_knockout_state == KFOneVOneWGData.KnockoutState.Index16To8 then
		self.enter_prematch_max_round = true
		KFOneVOneWGData.Instance:SetCanPlay16QiangAnim(true)
	end

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)
	local can_show = new_match_state == KFOneVOneWGData.MatchState.TaoTai and is_open   -- 判断1v1活动是否开启
	if can_show then
		Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_KNOCKOUT_MATCH_INFO)
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.OneVOne_JingCai, 1,function ()
			local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
			if match_state == KFOneVOneWGData.MatchState.TaoTai then
				Field1v1WGCtrl.Instance:OpenJingCai()
			else	
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiOpenTip)
			end
			return true
		end)
	else
		MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.OneVOne_JingCai)
	end

end

function Field1v1WGCtrl:OnSCCross1V1FightResult(protocol)
	local person_info = KFOneVOneWGData.Instance:GetOneVOnePersonInfo()
	local new_score = person_info and person_info.score or 0
	local other_cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
	local add_score = protocol.is_win == 1 and other_cfg.winner_score or other_cfg.loser_score
	local reward_data = KFOneVOneWGData.Instance:GetResultRewardItemCfg(protocol.is_win == 1)
	local data = {result = protocol.is_win,award_score = new_score,add_score = add_score,reward_data = reward_data}
	if self.kf_onevone_end_view then
		self.kf_onevone_end_view:SetEndData(data)
		self.kf_onevone_end_view:Open()
	end
end

-- 跨服1V1战斗开始
function Field1v1WGCtrl:OnCross1v1FightStart(protocol)
	self.kf_onevone_data:SetCross1v1FightStart(protocol)

	if protocol.timestamp_type == KUAFUONEVONE_STATUS.AWAIT  then
		-- UiInstanceMgr.Instance:ShowFBStartDown2(protocol.fight_start_timestmap)
	elseif protocol.timestamp_type == KUAFUONEVONE_STATUS.PREPARE then
		if self.kf_onevone_head_view then
			self.kf_onevone_head_view:StartFight(protocol.fight_start_timestmap)
		end
	end
	GlobalEventSystem:Fire(KFONEVONE1v1Type.KF_STATUS_CHANGE)
end

function Field1v1WGCtrl:OnSCCross1V1WinnerInfo(protocol)
	self.kf_onevone_data:SetSCCross1V1WinnerInfo(protocol)
	self.view:Flush(KUAFU_TAB_TYPE.ONEVSONE,"flush_onevone_model")
	if self.kf_onevone_result_view:IsOpen() then
		self.kf_onevone_result_view:Flush()
	end
end

function Field1v1WGCtrl:OpenOneVOneHeadView()
	if self.kf_onevone_head_view then
		self.kf_onevone_head_view:Open()
	end
end

function Field1v1WGCtrl:CloseOneVOneHeadView()
	if self.kf_onevone_head_view then
		self.kf_onevone_head_view:Close()
	end
end

function Field1v1WGCtrl:OpenOneVOneTaskView()
	if self.kf_onevone_task_view then
		self.kf_onevone_task_view:Open()
	end
end

function Field1v1WGCtrl:OpenOneVOneMatchingView()
	if self.kf_onevone_matching_view then
		self.kf_onevone_matching_view:Open()
	end
end

function Field1v1WGCtrl:CloseOneVOneMatchingView()
	if self.kf_onevone_matching_view:IsOpen() then
		self.kf_onevone_matching_view:Close()
	end
end

function Field1v1WGCtrl:CloseOneVOneTaskView()
	if self.kf_onevone_task_view then
		self.kf_onevone_task_view:Close()
	end
end

function Field1v1WGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KF_ONEVONE and status == ACTIVITY_STATUS.CLOSE then
		self.score_rank_paly_16qiang_anim = nil
		self.yuxuan_in_yuxuan_end = nil
		self.enter_prematch_max_round = nil
		self.open_onevone_result = nil
		KFOneVOneWGData.Instance:ClearLocalActInfo()
		MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.OneVOne_JingCai)
	elseif activity_type == ACTIVITY_TYPE.KF_ONEVONE and status == ACTIVITY_STATUS.OPEN then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if activity_info and activity_info.next_time and activity_info.next_time > server_time and Scene.Instance:GetSceneType() == SceneType.Kf_OneVOne then
			MainuiWGCtrl.Instance:SetFbIconEndCountDown( activity_info.next_time )
		end
	end
end

function Field1v1WGCtrl:GetOpenOneVOneResult()
	return self.open_onevone_result
end

function Field1v1WGCtrl:SetOpenOneVOneResult(flag)
	self.open_onevone_result = flag
end

function Field1v1WGCtrl:OnSceneChangeComplete(old_scene_type, new_scene_type)
	local scene_type = Scene.Instance:GetSceneType()
	if old_scene_type == SceneType.Kf_OneVOne and new_scene_type == SceneType.Kf_OneVOne_Prepare and not self.open_onevone_result then
		local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
		local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
		if match_state == KFOneVOneWGData.MatchState.TaoTai and knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then
			ViewManager.Instance:Open(GuideModuleName.KFOneVOneResultView)
			self.open_onevone_result = true
		end
	end
end

function Field1v1WGCtrl:OpenJingCai()
	if self.kf_onevone_duizhen_view:IsOpen() then
		self.kf_onevone_duizhen_view:Flush()
	else
		self.kf_onevone_duizhen_view:Flush(nil,"open_jingcai")
		self.kf_onevone_duizhen_view:Open()
	end
end

function Field1v1WGCtrl:OpenPerson()
	if self.kf_onevone_reward_view:IsOpen() then
		self.kf_onevone_reward_view:Flush()
	else
		self.kf_onevone_reward_view:Open()
	end
end

function Field1v1WGCtrl:PlayJingJiTips(old_match_state,new_match_state,knockout)
	self.kf_onevone_task_view:PlayJingJiTips(old_match_state,new_match_state,knockout)
end

function Field1v1WGCtrl:OneVOneStartTween()
	self.kf_onevone_task_view:StartTween()
end

function Field1v1WGCtrl:OnFlyDownEnd()	

end

function Field1v1WGCtrl:GetEnterPrematchMaxRound()
	return self.enter_prematch_max_round
end

function Field1v1WGCtrl:GetScoreRankPlay16QiangAnim()
	return self.score_rank_paly_16qiang_anim
end

function Field1v1WGCtrl:GetOnevOneTaskView()
	return self.kf_onevone_task_view
end