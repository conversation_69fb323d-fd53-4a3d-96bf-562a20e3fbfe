FuBenCommonBossLogic= FuBenCommonBossLogic or BaseClass(CommonFbLogic)

function FuBenCommonBossLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.update_elaspe_time = 0
end

function FuBenCommonBossLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

local scene_type_name = {
	[SceneType.TEAM_COMMON_BOSS_FB_1] = 1,
	[SceneType.TEAM_COMMON_BOSS_FB_2] = 2,
	[SceneType.TEAM_COMMON_BOSS_FB_3] = 3,
}

function FuBenCommonBossLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	self.update_elaspe_time = 0
	self.lm_scene_enter_complete = true
	self.lm_check_auto_status = true
	self.scene_info_stage = nil

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		local fb_name_seq = scene_type_name[new_scene_type]
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.FuBenTeamCommonBoss.FBName[fb_name_seq])

		FuBenTeamCommonBossWGCtrl.Instance:OpenFBTaskView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	self.lm_check_auto_status = false
	self.guaji_change_event = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, BindTool.Bind(self.BindGuaJiFireEvent,self))

	local view = FuBenTeamCommonBossWGCtrl.Instance:GetFBTaskView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIRightTopChangeList(view)

	MainuiWGCtrl.Instance:FlushSpeFbSendFlowerMsgHead()
end


function FuBenCommonBossLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self, old_scene_type, new_scene_type)

	self.lm_scene_enter_complete = false
	self.lm_check_auto_status = false
	self.scene_info_stage = nil

    MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	FuBenTeamCommonBossWGCtrl.Instance:CloseFBTaskView()
	local view = FuBenTeamCommonBossWGCtrl.Instance:GetFBTaskView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
	MainuiWGCtrl.Instance:SetMianUITargetPos(0, 0)

	if self.guaji_change_event then
		GlobalEventSystem:UnBind(self.guaji_change_event)
		self.guaji_change_event = nil
   end

   FuBenTeamCommonBossWGData.Instance:CleanSpeSendFlowerList()
   MainuiWGCtrl.Instance:FlushSpeFbSendFlowerMsgHead()
end

function FuBenCommonBossLogic:BindGuaJiFireEvent(guaji_type)
	if guaji_type == GuajiType.None then
	elseif guaji_type == GuajiType.Auto then
		self.lm_check_auto_status = true
	end
end

function FuBenCommonBossLogic:Update(now_time, elapse_time)
	if not self.lm_scene_enter_complete then
		return
	end

	BaseSceneLogic.Update(self, now_time, elapse_time)

	if self.update_elaspe_time + 1 > now_time then
		return
	end

	self.update_elaspe_time = now_time

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and main_role:IsDeleted() then
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	local scene_fuben_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenSceneCfg(scene_id)
	if not scene_fuben_cfg then
		return
	end

	local fb_seq = scene_fuben_cfg.seq
	local fb_scene_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonBossFBInfo(fb_seq)
	if not fb_scene_info then
		return
	end

	local stage_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenStageCfg(fb_seq, fb_scene_info.stage)
    if not stage_cfg then
        return nil
    end

	if self.scene_info_stage ~= fb_scene_info.stage then
		self.scene_info_stage = fb_scene_info.stage
		self.lm_check_auto_status = true
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end

	if GuajiCache.guaji_type == GuajiType.Auto then
		-- 拾取
		local obj_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
		if not IsEmptyTable(obj_list) then
			local obj_id_list = {}

			for k, v in pairs(obj_list)do
				table.insert(obj_id_list, v:GetObjId())
			end

			if not IsEmptyTable(obj_id_list) then
				GlobalTimerQuest:AddTimesTimer(function ()
					Scene.ScenePickItem(obj_id_list)
				end, 1, 1)
			end
		end
	end

	if self.lm_check_auto_status and GuajiCache.guaji_type == GuajiType.Auto then
		if stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Monster then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			self.lm_check_auto_status = false
		elseif stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Gather then
			local is_gather_state = main_role:GetIsGatherState()
			if not is_gather_state then
				local gather_obj = FuBenTeamCommonBossWGData.Instance:SelectRandGatherObj()
				if gather_obj then
					self.lm_check_auto_status = false
					self:StartGather(gather_obj)
				end
			end
		elseif stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Npc then
			self.lm_check_auto_status = false
			self:FindTaskNpc()
		end
	end
end

function FuBenCommonBossLogic:StartGather(gather_obj)
	if gather_obj then
		GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), gather_obj.vo.pos_x, gather_obj.vo.pos_y, self:MoveToGatherRange(), nil, nil, nil, function()
			self.is_not_goto_gather = true
			if nil ~= gather_obj and not gather_obj:IsDeleted() then
				gather_obj:OnClick()
				GuajiWGCtrl.Instance:CheckCanGather(gather_obj)
				self.lm_check_auto_status = true
			end
		end)
	end
end

function FuBenCommonBossLogic:MoveToGatherRange()
	return 3.5
end

function FuBenCommonBossLogic:FindTaskNpc()
    FuBenTeamCommonBossWGCtrl.Instance:FindTaskNpc()
end

function FuBenCommonBossLogic:GetNpcTaskNeedAloneLogic(cur_npc_id)
	local task_tab = {}
	local npc_task_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenNpcTaskTCfg(cur_npc_id)
	if not npc_task_cfg then
		return nil
	end

	local scene_id = Scene.Instance:GetSceneId()
	local scene_fuben_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenSceneCfg(scene_id)
	if not scene_fuben_cfg then
		return nil
	end

	local fb_seq = scene_fuben_cfg.seq
	local fb_scene_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonBossFBInfo(fb_seq)
	if not fb_scene_info then
		return nil
	end

    local stage_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenStageCfg(fb_seq, fb_scene_info.stage)
    if not stage_cfg then
        return nil
    end

	if stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Npc and stage_cfg.param0 == cur_npc_id then
		task_tab.hide_btn = 1
		task_tab.talk_text = npc_task_cfg.task_talk_str
		local fun = function ()
			RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_NPC_TALK, cur_npc_id)
		end

		task_tab.callback = fun
		task_tab.npc_id = cur_npc_id
	else
		task_tab.hide_btn = 1
		task_tab.talk_text = npc_task_cfg.other_talk_str
		local fun = function ()

		end
		task_tab.callback = fun
		task_tab.npc_id = cur_npc_id
	end


	return task_tab
end

function FuBenCommonBossLogic:IsRoleEnemy(target_obj, main_role)
	local target_vo = target_obj:GetVo() or {}
	if target_vo.is_shadow == 1 then
		return false
	end

	return BaseSceneLogic.IsRoleEnemy(self, target_obj, main_role)
end