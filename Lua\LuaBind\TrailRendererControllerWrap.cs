﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TrailRendererControllerWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(TrailRendererController), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("RefreshTrailRender", RefreshTrailRender);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshTrailRender(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TrailRendererController obj = (TrailRendererController)ToLua.CheckObject<TrailRendererController>(L, 1);
			obj.RefreshTrailRender();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

