PositionalWarfareView = PositionalWarfareView or BaseClass(SafeBaseView)

-- 策划暂定三种地图
local MAP_CITY_INFO = {
    [1] = {map_id = 1, base_city_num = 3, normal_city_num = 4},  -- 本服
    [2] = {map_id = 2, base_city_num = 2, normal_city_num = 3},  -- 2跨服
    [3] = {map_id = 3, base_city_num = 4, normal_city_num = 9},  -- 4跨服及以上 后续扩展修改
}

-- 连线编号   阶段 + 前一领地seq + 后一领地seq = 线编号
local LINE_TABLE = {
    -- 1阶段
    [1] = {
        [0] = {[0] = 1, [3] = 2},
        [1] = {[1] = 1, [3] = 2},
        [2] = {[2] = 1, [3] = 2},
    },

    -- 2阶段
    [2] = {
        [0] = {[0] = 1, [1] = 2, [2] = 3},
        [1] = {[0] = 2, [1] = 1, [2] = 3},
    },

    -- 3阶段
    [3] = {
        [0] = {[0] = 1, [4] = 3, [7] = 5},
        [1] = {[1] = 2, [5] = 4, [4] = 6},
        [2] = {[2] = 1, [6] = 3, [5] = 5},
        [3] = {[3] = 2, [7] = 4, [6] = 6},
        [4] = {[8] = 8},
        [5] = {[8] = 7},
        [6] = {[8] = 8},
        [7] = {[8] = 7},
    }
}

function PositionalWarfareView:__init()
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.open_source_view = "BtnPositionalWarfare"
    self:SetMaskBg(false)
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_view")
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_top_panel")
end

function PositionalWarfareView:LoadCallBack()
    self.time_cache = 0
    self.load_complete = false
    self.is_in_fight_state = false

    self:InitMapList()
    self:UpdateView()
    self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateView,self), 1)

    XUI.AddClickEventListener(self.node_list.btn_add_battle_slaughter_value, BindTool.Bind(self.OnClickAddBattleSlaughterValueBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_zhanling, BindTool.Bind(self.OnClickZhanLingBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_rank, BindTool.Bind(self.OnClickRankBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_treasure, BindTool.Bind(self.OnClickTreasureBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_mall, BindTool.Bind(self.OnClickMallBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_war_situation, BindTool.Bind(self.OnClickWarSituationBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_camp_list, BindTool.Bind(self.OnClickCampListBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_gameplay_description, BindTool.Bind(self.OnClickGamePlayDescriptionBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_score_reward, BindTool.Bind(self.OnClickScoreRewardBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickTipBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_stage_info, BindTool.Bind(self.OnClickStageInfoBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_jump_to_mengling, BindTool.Bind(self.OnClickJumpToMengLingBtn, self))
end

function PositionalWarfareView:ShowIndexCallBack()
    local stage_type = PositionalWarfareWGData.Instance:GetMapId()
    local map_content_id = -1

    for k, v in pairs(MAP_CITY_INFO) do
        if v.map_id == stage_type then
            map_content_id = k
            break
        end
    end

    if map_content_id >= 0 and self.node_list["map_content" .. map_content_id] then
        local camp = PositionalWarfareWGData.Instance:GetMyCamp()

        if camp >= 0 then
            -- 获取
            local camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(camp)

            if not IsEmptyTable(camp_cfg) then
                if self.map_show_pos_tween then
                    self.map_show_pos_tween:Kill()
                    self.map_show_pos_tween = nil
                end
                
                local camp_show_x, camp_show_y = camp_cfg.camp_show_x, camp_cfg.camp_show_y
                self.map_show_pos_tween = self.node_list["map_content" .. map_content_id].rect:DOAnchorPos(Vector2(camp_show_x, camp_show_y), 2)
            end
        end
    end

    self:CheckShowExplainView()
end

function PositionalWarfareView:CheckShowExplainView()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid
	if PlayerPrefsUtil.GetInt("PositionalWarfareViewExplainTip" .. main_role_id, 0) == 0 then
		PlayerPrefsUtil.SetInt("PositionalWarfareViewExplainTip" .. main_role_id, 1)
		PositionalWarfareWGCtrl.Instance:OpenExplainView()
	end
end

function PositionalWarfareView:UpdateView()
    if self.node_list.desc_act_time then
        local time_str, is_in_fight_time = PositionalWarfareWGData.Instance:GetActivityTimeInfo()
        self.node_list.desc_act_time.text.text = time_str

        if self.is_in_fight_state and not is_in_fight_time then
            RemindManager.Instance:Fire(RemindName.PositionalWarfare)
			self:Flush()
		end
		
		self.is_in_fight_state = is_in_fight_time
    end

    self.time_cache = self.time_cache + 1
    if self.time_cache >= 10 then
        self.time_cache = 0
        self:AutoSendRequest()
    end
end

function PositionalWarfareView:AutoSendRequest()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.ROOM_INFO)
    PositionalWarfareWGCtrl.Instance:SendInformationRequest()
end

function PositionalWarfareView:ReleaseCallBack()
    if self.map_list then
        for k, v in pairs(self.map_list) do
            if v.base_city_map then
                for i, u in pairs(v.base_city_map) do
                    u:DeleteMe()
                end

                v.base_city_map = nil
            end

            if v.normal_city_num then
                for i, u in pairs(v.normal_city_num) do
                    u:DeleteMe()
                end 

                v.normal_city_num = nil
            end

            v = nil
        end

        self.map_list = nil
    end

	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end

    self.load_complete = nil

    if self.map_show_pos_tween then
        self.map_show_pos_tween:Kill()
        self.map_show_pos_tween = nil
    end
end

function PositionalWarfareView:InitMapList()
    if not self.map_list then
        self.map_list = {}

        local res_async_loader = AllocResAsyncLoader(self, "positional_warfare_city_item")
        if res_async_loader then
            res_async_loader:Load("uis/view/positional_warfare_ui_prefab", "pw_city_item", nil, function(obj)
                for k, v in pairs(MAP_CITY_INFO) do
                    self.map_list[v.map_id] = self.map_list[v.map_id] or {}                  

                    local base_city_map = {}
                    for i = 0, v.base_city_num - 1 do
                        local root = ResMgr:Instantiate(obj)
                        base_city_map[i] = PWCityCellItemRender.New(root)
                        base_city_map[i]:SetInstanceParent(self.node_list["base_point".. v.map_id .."_" .. i].transform)
                    end

                    self.map_list[v.map_id].base_city_map = base_city_map

                    local nor_city_map = {}
                    for i = 0, v.normal_city_num - 1 do
                        local root = ResMgr:Instantiate(obj)
                        nor_city_map[i] = PWCityCellItemRender.New(root)
                        nor_city_map[i]:SetInstanceParent(self.node_list["nor_point".. v.map_id .."_" .. i].transform)
                    end

                    self.map_list[v.map_id].nor_city_map = nor_city_map
                end
                
                self.load_complete = true
                self:Flush()
            end)
        end
    end
end

function PositionalWarfareView:OnFlush()
    if not self.load_complete then
        return
    end

    self:FlushStageInfo()
    self:FlushMap()
end

function PositionalWarfareView:FlushStageInfo()
    local devote_remind = PositionalWarfareWGData.Instance:GetDevoteRewardRemind() == 1
    self.node_list.btn_score_reward_remind:CustomSetActive(devote_remind)

    local mall_remind = PositionalWarfareWGData.Instance:GetMallExchangeRemind() == 1
    self.node_list.btn_mall_remind:CustomSetActive(mall_remind)

    local max_tired = PositionalWarfareWGData.Instance:GetOtherAttrValue("max_tired")
    local tired = PositionalWarfareWGData.Instance:GetTired()
    local color = tired < max_tired and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.desc_battle_slaughter_value.text.text = string.format(Language.PositionalWarfare.TiredValue, color, tired, max_tired)

    local open_day = PositionalWarfareWGData.Instance:GetMainServerOpenTime()
    self.node_list.desc_kf_time.text.text = string.format(Language.PositionalWarfare.MainServerOpenDay, open_day)

    local stage_cfg = PositionalWarfareWGData.Instance:GetCurGroupCfg()

    if stage_cfg then
        self.node_list.desc_stage_description.text.text = stage_cfg.stage_desc or ""

        local bundle, asset = ResPath.GetPositionalWarfareImg(stage_cfg.stage_icon)
        self.node_list.difficulity_icon.image:LoadSprite(bundle, asset, function ()
            self.node_list.difficulity_icon.image:SetNativeSize()
        end)
    end

    local zhanling_remind = PositionalWarfareWGData.Instance:GetZhanLingRemind() == 1
    self.node_list.btn_zhanling_remind:CustomSetActive(zhanling_remind)
end

function PositionalWarfareView:FlushMap()
    local stage_type = PositionalWarfareWGData.Instance:GetMapId()

    self.node_list.btn_score_reward:CustomSetActive(stage_type == 1)
    self.node_list.btn_zhanling:CustomSetActive(stage_type ~= 1)

    for k, v in pairs(MAP_CITY_INFO) do
        self.node_list["positional_warfare_type" .. k]:CustomSetActive(v.map_id == stage_type)
    end

    self:SetMapData(stage_type)
end

-- 设置阶段地图数据   只关注本门派连线，其他门派均为 1
-- 未占领 不能攻击   1 灰色虚线
-- 占领后           2 黄实线
-- 未战领 能攻击     3 灰色实线
function PositionalWarfareView:SetMapData(stage_type)
    local line_table = {}
    local base_city_data, nor_city_data = PositionalWarfareWGData.Instance:GetCityCfgDataList()

    if not IsEmptyTable(base_city_data) then
        for k, v in pairs(base_city_data) do
            self.map_list[stage_type].base_city_map[v.land_seq]:SetData(v)
        end
    end

    local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()

    if not IsEmptyTable(nor_city_data) then
        for k, v in pairs(nor_city_data) do
            local cache_data = PositionalWarfareWGData.Instance:GetLandDataCache(v.group_index, v.seq)
            local connect_camp_data = cache_data.connect_camp
            local cur_city_info = PositionalWarfareWGData.Instance:GetLandInfoBySeq(v.seq)

            -- 归属 有临时占领时候优先临时状态
            local owner_camp = cur_city_info and cur_city_info.owner_camp or -1
            local tem_owner_camp = cur_city_info and cur_city_info.tem_owner_camp or -1
            local cur_owner_camp = tem_owner_camp >= 0 and tem_owner_camp or (owner_camp >= 0 and owner_camp or -1)

            -----------------连接营地start-------------------
            if not IsEmptyTable(connect_camp_data) then
                for i, u in pairs(connect_camp_data) do
                    local index = tonumber(u)
                    if index >= 0 then
                        line_table[index] = line_table[index] or {}

                        if my_camp == index then
                            if cur_owner_camp == my_camp then
                                line_table[index][v.seq] = 2
                            else
                                line_table[index][v.seq] = 1 -- 3
                            end
                        else
                            line_table[index][v.seq] = 3   -- 1
                        end
                    end
                end
            end 
            ---------------------连接营地end-----------------

            -----------------------有前置领地Start---------------------
            local pre_land_data = cache_data.pre_land
            if not IsEmptyTable(pre_land_data) then
                for i, u in pairs(pre_land_data) do
                    local index = tonumber(u)
                    if index >= 0 then
                        local pre_city_info = PositionalWarfareWGData.Instance:GetLandInfoBySeq(index)
                        local pre_owner_camp = (pre_city_info or {}).owner_camp or -1
                        local pre_tem_owner_camp = (pre_city_info or {}).tem_owner_camp or -1
                        local cur_pre_owner_camp = pre_tem_owner_camp >= 0 and pre_tem_owner_camp or (pre_owner_camp >= 0 and pre_owner_camp or -1)

                        local pre_is_occupy = cur_pre_owner_camp >= 0
                        line_table[index] = line_table[index] or {}

                        if pre_is_occupy then
                            if cur_pre_owner_camp == my_camp then
                                if cur_owner_camp == my_camp then
                                    line_table[index][v.seq] = 2
                                else
                                    line_table[index][v.seq] =1 -- 3
                                end
                            else
                                line_table[index][v.seq] =3 --1
                            end
                        else
                            line_table[index][v.seq] =3 -- 1
                        end
                    end
                end
            end
            -----------------------有前置领地End---------------------

            self.map_list[stage_type].nor_city_map[v.seq]:SetData(v)
        end
    end

    local key = ""
    local attack_key = ""
    local load_line_bundle, load_line_asset

    -- line_table = {[前一领地seq] = {[后一领地seq] = value}}
    for k, v in pairs(line_table) do
        for k1, v1 in pairs(v) do
            local line_index = LINE_TABLE[stage_type][k][k1]

            -- key = string.format("line_%s_%s_%s", stage_type, k, k1)
            -- local asset_name = ((MAP_LINE_CFG[stage_type] or {})[key] or {})[v1]

            key = string.format("line_%s_%s_%s", stage_type, k, k1)
            attack_key = string.format("line_%s_%s_%s_s", stage_type, k, k1)

            local asset_name

            if v1 == 3 or v1 == 1 then
                -- 未占领能攻击 统一的灰色实线 不要区分颜色
                -- 序号 + 阶段 + 状态  
                asset_name = string.format("a3_zdz_line_%s_%s_%s", line_index, stage_type, v1)
            elseif v1 == 2 then
                -- 已占领 区分颜色的实线
                -- 序号 + 阶段 + 颜色 + 状态  
                local camp_cfg = PositionalWarfareWGData.Instance:GetCampCfgByCamp(my_camp)
                asset_name = string.format("a3_zdz_line_%s_%s_%s_%s", line_index, stage_type, camp_cfg.camp_sign, v1)
            end

            -- if v1 == 3 then
            --     -- 序号 + 阶段 + 状态  
            --     asset_name = string.format("a3_zdz_line_%s_%s_%s", line_index, stage_type, 1)
            -- else
            --     -- 序号 + 阶段 + 颜色 + 状态  
            --     local camp_cfg = PositionalWarfareWGData.Instance:GetCampCfgByCamp(my_camp)
            --     asset_name = string.format("a3_zdz_line_%s_%s_%s_%s", line_index, stage_type, camp_cfg.camp_sign, v1)
            -- end

            -- if self.node_list[attack_key] then
            --     self.node_list[attack_key]:CustomSetActive(v1 == 1)
            -- end

            -- if self.node_list[key] then
            --     self.node_list[key]:CustomSetActive(v1 ~= 1)

            --     -- 序号 + 阶段 + 颜色 + 状态
            --     if nil ~= asset_name and v1 ~= 1 then
            --         load_line_bundle, load_line_asset = ResPath.GetRawImagesPNG(asset_name)
            --         self.node_list[key].raw_image:LoadSprite(load_line_bundle, load_line_asset)
            --     end
            -- end

            -- 序号 + 阶段 + 颜色 + 状态
            if self.node_list[key] and nil ~= asset_name then
                load_line_bundle, load_line_asset = ResPath.GetRawImagesPNG(asset_name)
                self.node_list[key].raw_image:LoadSprite(load_line_bundle, load_line_asset)
            end
        end
    end
end

function PositionalWarfareView:OnClickAddBattleSlaughterValueBtn()
    PositionalWarfareWGCtrl.Instance:OpenNuQiTip()

    -- local tired_drug = PositionalWarfareWGData.Instance:GetOtherAttrValue("tired_drug")
	-- local item_num = ItemWGData.Instance:GetItemNumInBagById(tired_drug)

    -- if item_num >= 1 then
    --     OfflineRestWGCtrl.Instance:OpenUserOfflineView(tired_drug)
    -- else
    --     TipWGCtrl.Instance:OpenItem({item_id = tired_drug})
    -- end

    -- local index = ItemWGData.Instance:GetItemIndex(guaji_card_2)
    -- BagWGCtrl.Instance:SendUseItem(index, 1, 0, 0)
end

function PositionalWarfareView:OnClickZhanLingBtn()
    PositionalWarfareWGCtrl.Instance:OpenZhanLingView()
end

function PositionalWarfareView:OnClickRankBtn()
    PositionalWarfareWGCtrl.Instance:OpenRankView(true)
end

function PositionalWarfareView:OnClickTreasureBtn()
    PositionalWarfareWGCtrl.Instance:OpenTreasureView()
end

function PositionalWarfareView:OnClickMallBtn()
    PositionalWarfareWGCtrl.Instance:OpenMallView()
end

function PositionalWarfareView:OnClickWarSituationBtn()
    PositionalWarfareWGCtrl.Instance:OpenWarSituationView()
end

function PositionalWarfareView:OnClickCampListBtn()
    PositionalWarfareWGCtrl.Instance:OpenCampListView()
end

function PositionalWarfareView:OnClickGamePlayDescriptionBtn()
    -- PositionalWarfareWGCtrl.Instance:OpenGamePlayDescriptionView()
    PositionalWarfareWGCtrl.Instance:OpenExplainView()
end

function PositionalWarfareView:OnClickScoreRewardBtn()
    PositionalWarfareWGCtrl.Instance:OpenScoreRewardView()
end

function PositionalWarfareView:OnClickTipBtn()
    RuleTip.Instance:SetContent(Language.PositionalWarfare.RuleContent, Language.PositionalWarfare.RuleTitle)
end

function PositionalWarfareView:OnClickStageInfoBtn()
    PositionalWarfareWGCtrl.Instance:OpenStageDescriptionView()
end

function PositionalWarfareView:OnClickJumpToMengLingBtn()
    ViewManager.Instance:Open(GuideModuleName.MengLingView)
end

------------------------------PWCityCellItemRender---------------------------
PWCityCellItemRender = PWCityCellItemRender or BaseClass(BaseRender)

function PWCityCellItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.country_click, BindTool.Bind(self.OnClickCountry, self))
    XUI.AddClickEventListener(self.node_list.btn_reward, BindTool.Bind(self.OnClickReward, self))
end

function PWCityCellItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_camp_point = nil ~= self.data.camp
    local point_bundle, point_asset
    local is_my_camp_city = false
    local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()

    if is_camp_point then
        local sign_bundle, sign_asset = ResPath.GetPositionalWarfareImg("a3_zdz_tt1_" .. self.data.camp_sign)
        self.node_list.country_sign.image:LoadSprite(sign_bundle, sign_asset, function()
            self.node_list.country_sign.image:SetNativeSize() 
        end)

        -- point_bundle, point_asset = ResPath.GetRawImagesPNG("a3_zdz_jz_1")
        point_bundle, point_asset = ResPath.GetRawImagesPNG("a3_zdz_ct_bg" .. self.data.camp_sign)
        self.node_list.desc_country_name.text.text = self.data.camp_name

        self.node_list.flag_zhaoji:CustomSetActive(false)
        self.node_list.camp_point_str:CustomSetActive(self.data.camp == my_camp)
        is_my_camp_city = (self.data.camp == my_camp)
        self.node_list.flag_fight:CustomSetActive(false)
    else
        self.node_list.camp_point_str:CustomSetActive(false)
        point_bundle, point_asset = ResPath.GetRawImagesPNG("a3_zdz_jz_" .. self.data.land_icon)
        self.node_list.desc_country_name.text.text = self.data.land_name
        local city_info = PositionalWarfareWGData.Instance:GetLandInfoBySeq(self.data.seq)
        local total_boss_cfg = PositionalWarfareWGData.Instance:GetCurMonsterListCfg(self.data.seq)

        local num = 0
        if not IsEmptyTable(total_boss_cfg) then
            for k, v in pairs(total_boss_cfg) do
                local active = PositionalWarfareWGData.Instance:GetBossActive(self.data.seq, v.seq)
    
                if active then
                    num = num + 1
                end
            end
        end

        self.node_list.desc_server_name.text.text = string.format(Language.PositionalWarfare.NumberOfBoss, num)

        local owner_camp = city_info and city_info.owner_camp or -1
        local tem_owner_camp = city_info and city_info.tem_owner_camp or -1
        local target_owner_camp = tem_owner_camp >= 0 and tem_owner_camp or (owner_camp >= 0 and owner_camp or -1)
        local has_owner = target_owner_camp >= 0
        is_my_camp_city = (my_camp == target_owner_camp)

        if has_owner then
           local camp_data = PositionalWarfareWGData.Instance:GetCampCfgByCamp(target_owner_camp)

           if not IsEmptyTable(camp_data) then
                self.node_list.desc_city_owner_name.text.text = camp_data.camp_name

                local bundle, asset = ResPath.GetPositionalWarfareImg("a3_zdz_tt_" .. camp_data.camp_sign)
                self.node_list.city_owner_icon.image:LoadSprite(bundle, asset, function ()
                    self.node_list.city_owner_icon.image:SetNativeSize()
                end)
           end
        end

        self.node_list.city_owner_bg:CustomSetActive(has_owner)

        local call_land_seq = PositionalWarfareWGData.Instance:GetCallInfo()
        self.node_list.flag_zhaoji:CustomSetActive(call_land_seq == self.data.seq)


        local fight_role_num = PositionalWarfareWGData.Instance:GetCityEnterTotalNum(self.data.seq)
        self.node_list.flag_fight:CustomSetActive(fight_role_num > 0)
    end

    self.node_list.country_icon.raw_image:LoadSprite(point_bundle, point_asset, function()
        self.node_list.country_icon.raw_image:SetNativeSize()
    end)
    self.node_list.country_sign:CustomSetActive(is_camp_point)
    self.node_list.info:CustomSetActive(not is_camp_point)

    self.node_list.flag_my_team:CustomSetActive(is_my_camp_city)

    local can_get_reward = PositionalWarfareWGData.Instance:CanGetLandCaptureRewrd(self.data.group_index, self.data.seq)
    self.node_list.btn_reward:CustomSetActive(can_get_reward)
end

function PWCityCellItemRender:OnClickCountry()
    if IsEmptyTable(self.data) then
        return
    end

    local is_camp_point = nil ~= self.data.camp

    if is_camp_point then
        PositionalWarfareWGCtrl.Instance:OpenCampShowView()
        return
    end

    PositionalWarfareWGCtrl.Instance:OpenInformationView(self.data)
end

function PWCityCellItemRender:OnClickReward()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.FETCH_CAPTURE_REWARD, self.data.seq)
end