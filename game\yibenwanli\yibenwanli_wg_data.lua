YiBenWanLiWGData = YiBenWanLiWGData or BaseClass()

function YiBenWanLiWGData:__init()
	if YiBenWanLiWGData.Instance then
		print_error("[YiBenWanLiWGData] attempt to create singleton twice!")
		return
	end

	YiBenWanLiWGData.Instance = self
	local yibenwanli_cfg = ConfigManager.Instance:GetAutoConfig("yibenwanli_cfg_auto")
	self.daily_gift_cfg = yibenwanli_cfg.daily
	self.lianchong_gift_cfg = yibenwanli_cfg.recharge
	self.other_cfg = yibenwanli_cfg.other[1]
	self.yibenwanli_lianchong_gift_info = {}
	self.yibenwanli_daily_gift_info = {}
	RemindManager.Instance:Register(RemindName.YiBenWanLi, BindTool.Bind(self.YiBenWanLiRmind, self))
	RemindManager.Instance:Register(RemindName.YiBenWanLi_DailyGift, BindTool.Bind(self.DailyGiftRmind, self))
	RemindManager.Instance:Register(RemindName.YiBenWanLi_LianChongGift, BindTool.Bind(self.LianChongGiftRmind, self))
end

function YiBenWanLiWGData:__delete()
	YiBenWanLiWGData.Instance = nil
	self.yibenwanli_lianchong_gift_info = nil
	self.yibenwanli_daily_gift_info = nil
	self.daily_gift_cfg = nil
	self.lianchong_gift_cfg = nil

	RemindManager.Instance:UnRegister(RemindName.YiBenWanLi)
	RemindManager.Instance:UnRegister(RemindName.YiBenWanLi_DailyGift)
	RemindManager.Instance:UnRegister(RemindName.YiBenWanLi_LianChongGift)
end

function YiBenWanLiWGData:SetYiBenWanLiPortocol(protocol)
	--一本万利 连充大礼
	self.yibenwanli_lianchong_gift_info.recharge_period = protocol.recharge_period			-- 连续充值当前周期
	self.yibenwanli_lianchong_gift_info.day = protocol.day									-- 连续充值累计天数
	self.yibenwanli_lianchong_gift_info.day_state = protocol.day_state						-- 连续充值领取标记
	self.yibenwanli_lianchong_gift_info.chongzhi_gold = protocol.chongzhi_gold				-- 当天充值金额
	self.yibenwanli_lianchong_gift_info.accrued_state = protocol.accrued_state				-- 累计领取标记
	--一本万利 每日礼包
	self.yibenwanli_daily_gift_info.daily_period = protocol.daily_period					-- 每日礼包当前周期
	self.yibenwanli_daily_gift_info.daily_data_count = protocol.daily_data_count			-- 礼包索引
	self.yibenwanli_daily_gift_info.daily_list = protocol.daily_list						-- 礼包标记，0 不可领取，1 可领取， 2 已领取

end

function YiBenWanLiWGData:GetOtherCfg()
	return self.other_cfg
end

function YiBenWanLiWGData:GetDailyGiftCfgByPeriod(period)
	local cfg_list = {}
	if not period then return cfg_list end
	for k,v in pairs(self.daily_gift_cfg) do
		if period == v.period then
			table.insert(cfg_list,v)
		end
	end
	return cfg_list
end

function YiBenWanLiWGData:GetLianChongGiftByPeriod(period)
	local cfg_list = {}

	for k,v in pairs(self.lianchong_gift_cfg) do
		if period == v.period then
			table.insert(cfg_list,v)
		end
	end
	return cfg_list
end

function YiBenWanLiWGData:GetLianChongGiftModel()
	local cur_period = self.yibenwanli_lianchong_gift_info.recharge_period or 0
	local cur_day = self.yibenwanli_lianchong_gift_info.day or 0
	local cfg = self:GetLianChongGiftByPeriod(cur_period)
	cur_day = cur_day == 0 and 1 or cur_day
	if cfg[cur_day] then
		return cfg[cur_day]
	end
end

function YiBenWanLiWGData:GetDailyGiftListViewData()
	local data_list = {}
	local cfg = self:GetDailyGiftCfgByPeriod(self.yibenwanli_daily_gift_info.daily_period)
	if IsEmptyTable(cfg) then return data_list end
	for i,v in ipairs(cfg) do
		local daily_data = self.yibenwanli_daily_gift_info.daily_list[i]
		if daily_data.seq == v.seq then
			local temp_list = {}
			-- local temp_list = __TableCopy(v)
			temp_list.cfg = v
			temp_list.state = daily_data.state
			temp_list.index = daily_data.state == 2 and 1 or 0
			temp_list.seq = v.seq
			table.insert(data_list,temp_list)
		end
	end
	table.sort(data_list, SortTools.KeyLowerSorter("index","seq"))
	return data_list
end

function YiBenWanLiWGData:IsAllRewardReceived()
	local is_all_received = true
	local cfg = self:GetDailyGiftCfgByPeriod(self.yibenwanli_daily_gift_info.daily_period)
	for i,v in ipairs(cfg) do
		local daily_data = self.yibenwanli_daily_gift_info.daily_list[i]
		if daily_data.seq == v.seq then
			if daily_data.state ~= 2 then
				is_all_received = false
				break
			end
		end
	end

	return is_all_received
end

function YiBenWanLiWGData:GetLianChongGiftCellData()
	local data_list = {}
	local cur_period = self.yibenwanli_lianchong_gift_info.recharge_period or 0
	local cur_day = self.yibenwanli_lianchong_gift_info.day or 0
	local cfg = self:GetLianChongGiftByPeriod(cur_period)
	local accrued_state = self.yibenwanli_lianchong_gift_info.accrued_state
	local index,target = 0,0

	if IsEmptyTable(cfg) then
		return data_list
	end

	local flag_state = false	-- cur_day 是否加1
	if accrued_state == 0 and (cur_day == 0 or cur_day == 7) then
		cur_day = cur_day + 1
		flag_state = true
	end

	if cur_day <= 7 then
		index,target = 1,7
	else
		index,target = 8,14
	end
	for i = index,target do
		if cfg[i] and cfg[i].accrued_item and not IsEmptyTable(cfg[i].accrued_item) then
			local temp_list = {}
			temp_list.cfg = cfg[i]
			local state = 0
			if cfg[i].day < cur_day then
				state = 2 			--已领取
			elseif cfg[i].day == cur_day then
				if accrued_state == 0 and not flag_state then
					state = 2
				else
					state = accrued_state 			--未领取
				end
			else
				state = 0 			--不可领取
			end
			temp_list.state = state
			table.insert(data_list,temp_list)
		end
	end
	return data_list
end

function YiBenWanLiWGData:GetLianChongGiftData()
	local cur_period = self.yibenwanli_lianchong_gift_info.recharge_period or 0
	local cur_day = self.yibenwanli_lianchong_gift_info.day or 0
	local cfg = self:GetLianChongGiftByPeriod(cur_period)
	local fetch_flag = self.yibenwanli_lianchong_gift_info.day_state

	cur_day = (fetch_flag == 0 and (cur_day == 0 or cur_day == 7)) and cur_day + 1 or cur_day

	if IsEmptyTable(cfg) then return end
	for k,v in pairs(cfg) do
		if v.day == cur_day then
			return v
		end
	end
end

function YiBenWanLiWGData:GetLianChongGiftInfo()
	return self.yibenwanli_lianchong_gift_info
end

function YiBenWanLiWGData:GetLianChongGiftAccruedDay()
	return self.yibenwanli_lianchong_gift_info.day or 0
end

function YiBenWanLiWGData:GetLianChongGiftCurReCharge()
	return self.yibenwanli_lianchong_gift_info.chongzhi_gold or 0
end

function YiBenWanLiWGData:YiBenWanLiRmind()
	if self:DailyGiftRmind() == 1 then
		return 1 
	end

	if self:LianChongGiftRmind() == 1 then
		return 1 
	end

	return 0
end

function YiBenWanLiWGData:DailyGiftRmind()
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()

	local is_red = 0
	local data = self:GetDailyGiftListViewData()

	if open_server_day < self.other_cfg.limit1 then
		return is_red
	end

	if role_level < self.other_cfg.limit4 then
		return is_red
	end

	if not data then
		return is_red
	end

	-- 礼包标记，0 不可领取，1 可领取， 2 已领取
	for k,v in pairs(data) do
		if v.state == 1 then
			return is_red + 1
		end
	end
	return is_red
end

function YiBenWanLiWGData:LianChongGiftRmind()
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local is_red = 0
	local data = self:GetLianChongGiftCellData()

	if open_server_day < self.other_cfg.limit2 then
		return is_red
	end

	if role_level < self.other_cfg.limit5 then
		return is_red
	end

	if not data then
		return is_red
	end

	for k,v in pairs(data) do
		if v.state == 1 then
			return is_red + 1
		end
	end

	if self.yibenwanli_lianchong_gift_info.day_state == 1 then
		return is_red + 1
	end

	return is_red
end


function YiBenWanLiWGData:GetLianChongGiftSlideValue()
	local data = self:GetLianChongGiftCellData()
	local cur_day = self.yibenwanli_lianchong_gift_info.day or 0
	local accrued_state = self.yibenwanli_lianchong_gift_info.accrued_state

	cur_day = (accrued_state == 0 and (cur_day == 0 or cur_day == 7)) and cur_day + 1 or cur_day
	if cur_day == 0 then return 0 end
	if cur_day == data[1].cfg.day then
		return 0
	elseif cur_day > data[1].cfg.day and cur_day <= data[2].cfg.day then
		return 0.115 * (cur_day > 7 and cur_day - 7 or cur_day)
	elseif cur_day > data[2].cfg.day and cur_day <= data[3].cfg.day then
		return 0.35 + ((0.33 / (data[3].cfg.day - data[2].cfg.day))*(cur_day - data[2].cfg.day))
	elseif cur_day > data[3].cfg.day and cur_day <= data[4].cfg.day then
		return 0.68 + ((0.32 / (data[4].cfg.day - data[3].cfg.day))*(cur_day - data[3].cfg.day))
	else
		return 1
	end
end



