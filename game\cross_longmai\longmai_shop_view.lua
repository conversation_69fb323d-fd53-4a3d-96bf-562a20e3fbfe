LongMaiShopView = LongMaiShopView or BaseClass(SafeBaseView)

function LongMaiShopView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/longmai_ui_prefab", "layout_longmai_shop_view")
end

function LongMaiShopView:__delete()
end

function LongMaiShopView:ReleaseCallBack()
	if self.longmai_shop_list then
		self.longmai_shop_list:DeleteMe()
		self.longmai_shop_list = nil
	end

	self.role_attr_notify = nil
	self.data_notify = nil
end

function LongMaiShopView:CloseCallBack()
	self.role_attr_notify = false
	self.data_notify = false
	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)
end

function LongMaiShopView:LoadCallBack()
	self.node_list["flush_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFlushShop,self))
	self.node_list["tj_btn"].button:AddClickListener(BindTool.Bind(self.OnClickOpenTjView,self))

	self.longmai_shop_list = AsyncListView.New(LongMaiShopCell, self.node_list["longmai_shop_list"])

	CrossLongMaiWGCtrl.Instance:SendLongMaiShopReq(LONGMAI_OPERA_TYPE.SHOP_INFO)

	self.role_data_change_callback = BindTool.Bind1(self.OnRoleDataChange, self)
	self.datachange_callback = BindTool.Bind1(self.OnItemDataChange, self)

	self:BindMoneyEvents()
end

function LongMaiShopView:ShowIndexCallBack()
	if not self.role_attr_notify then
		RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"gold"})
		self.role_attr_notify = true
    end

    if not self.data_notify then
		ItemWGData.Instance:NotifyDataChangeCallBack(self.datachange_callback)
		self.data_notify = true
    end
end

function LongMaiShopView:DelayClickAble()
	self.is_playing_turning = true
	GlobalTimerQuest:AddDelayTimer(function ()
		self.is_playing_turning = false
	end,1)
end

function LongMaiShopView:OnFlush(prarm_t)
	local cell_should_anim = 0
	for k,v in pairs(prarm_t) do
		if k == "flush_cell_anim" then
			cell_should_anim = 1
			self:DelayClickAble()
		end
	end
	local shop_list = CrossLongMaiWGData.Instance:GetShopInfo()
	local list_data = {}
	local num = 0
	local a, b = 0, 0
	for k, v in pairs (shop_list) do
		num = num + 1
		a = math.ceil(num / 2)
		b = num % 2
		b = b == 0 and 2 or b
		if not list_data[a] then
			list_data[a] = {}
		end
		list_data[a][b] = v
		list_data[a].should_anim = cell_should_anim
	end
	self.longmai_shop_list:SetDataList(list_data)

	local flush_time, day_times = CrossLongMaiWGData.Instance:GetShopFlushTimesInfo()
	flush_time = flush_time > 0 and flush_time or 0
	if flush_time > 0 then
		self.node_list.time_count.text.text = string.format(Language.CrossLongMai.FlushTime, flush_time)
		self.node_list.price_img:SetActive(false)
	else
		local flush_money = CrossLongMaiWGData.Instance:GetShopFlushPrice(day_times + 1)
		self.node_list.time_count.text.text = string.format(Language.CrossLongMai.FlushPrice, flush_money)
		self.node_list.price_img:SetActive(true)
	end
end


function LongMaiShopView:BindMoneyEvents()
	XUI.AddClickEventListener(self.node_list.Btngold, BindTool.Bind(self.OnClickTopBtn, self, COMMON_CONSTS.VIRTUAL_ITEM_GOLD))
	for i = 1, 5 do
		 XUI.AddClickEventListener(self.node_list["Btnkey_" .. i], BindTool.Bind(self.OnClickKey, self, i))
	end

    self:SetMoneyUI()
    self:FlushMoneyIcon()
end

function LongMaiShopView:SetMoneyUI()
	local role_data_info = RoleWGData.Instance:GetRoleInfo()
	self:SetGold(role_data_info.gold)
end

function LongMaiShopView:OnRoleDataChange(attr_name, value, old_value)
    if attr_name == "gold" then
		self:SetGold()
	end
end

function LongMaiShopView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if CrossLongMaiWGData.Instance:GetIsShopStuff(change_item_id) then
		self:ChangeKey(new_num, old_num, change_item_id)
		self:Flush()
	end
end

function LongMaiShopView:OnClickTopBtn(item_id)
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function LongMaiShopView:OnClickKey(index)
	local cfg = CrossLongMaiWGData.Instance:GetDeadDropCfg()
	local key_id = cfg[index].item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = key_id})
end

function LongMaiShopView:SetGold(value)
	local gold = value or RoleWGData.Instance:GetRoleInfo().gold or 0
	local old_money_value = RoleWGData.Instance:GetOldMoney(MONNEY_TYPE_1[GameEnum.MONEY_BAR.GOLD])
	local show_text_num, show_txt = CommonDataManager.ConverMoneyBarNew(gold)
	local change_value, change_str = CommonDataManager.ConverMoneyBarNew(gold)
	if old_money_value ~= gold then
		self.node_list["gold_icon"].transform.localScale = Vector3(1, 1, 1)
		self.node_list["gold_icon"].transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)

		local bundle_name, asset_name = ResPath.GetEffectUi("UI_qifu_shouji")
		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["gold"].transform, 1,nil, nil, nil, nil)
		RoleWGData.Instance:SetBeforeLastMoney(gold, nil, nil, nil, nil, nil, nil, true)

		local complete_fun = function()
					self.node_list["gold_num"].text.text = (change_value) .. change_str
					self.node_list["gold_icon"].transform.localScale = Vector3(1, 1, 1)
				end
		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["gold_num"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["gold_num"].text.text = (value) .. postfix_name
			end
		end

		UITween.DONumberTo(self.node_list["gold_num"].text, old_money_value, gold, 1.5, update_fun, complete_fun)
	else
		if self.node_list["gold_num"] then
			self.node_list["gold_num"].text.text = show_text_num .. show_txt
		end
	end
end

function LongMaiShopView:FlushMoneyIcon()
	local cfg = CrossLongMaiWGData.Instance:GetDeadDropCfg()
	for i = 1, 5 do
		local consume_id = cfg[i].item_id
		self.node_list["key_num_" .. i].text.text = ItemWGData.Instance:GetItemNumInBagById(consume_id)
		local item_cfg = ItemWGData.Instance:GetItemConfig(consume_id)
		self.node_list["key_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	end
end

function LongMaiShopView:ChangeKey(new_num, old_num, change_item_id)
	local index = 0
	local cfg = CrossLongMaiWGData.Instance:GetDeadDropCfg()
	for i, v in ipairs(cfg) do
		if change_item_id == v.item_id then
			index = i
		end
	end

	if index == 0 then
		return
	end

	local keys_num = new_num
	local old_money_value = old_num or new_num
	local num = ItemWGData.Instance:GetItemNumInBagById(change_item_id)
	local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
	if postfix_name == "" then
		self.node_list["key_num_" .. index].text.text = (string.format("%.0f", value)) .. postfix_name
	else
		self.node_list["key_num_" .. index].text.text = (value) .. postfix_name
	end
end

function LongMaiShopView:OnClickFlushShop()
	if self.is_playing_turning  or self.just_send_and_wait then
		TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.IsPlayingTrun)
		return
	end

	local flush_time, day_times = CrossLongMaiWGData.Instance:GetShopFlushTimesInfo()
	if flush_time > 0 then
		self.just_send_and_wait = true
		CrossLongMaiWGCtrl.Instance:SendLongMaiShopReq(LONGMAI_OPERA_TYPE.FLUSH_SHOP)

		GlobalTimerQuest:AddDelayTimer(function ()
			self.just_send_and_wait = false
		end,1)
	else
		local flush_money = CrossLongMaiWGData.Instance:GetShopFlushPrice(day_times + 1)
		local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(flush_money)
		local str = string.format(Language.CrossLongMai.LongMaiShopFlushConfirm, flush_money)
		local ok_func = function ()
			if have_enough then
				self.just_send_and_wait = true
				CrossLongMaiWGCtrl.Instance:SendLongMaiShopReq(LONGMAI_OPERA_TYPE.FLUSH_SHOP)
				GlobalTimerQuest:AddDelayTimer(function ()
					self.just_send_and_wait = false
				end,1)
			else
				TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.CrossLongMai.NeedEnoughToFlush,flush_money))
				UiInstanceMgr.Instance:ShowChongZhiView()
			end
		end
		TipWGCtrl.Instance:OpenAlertTips(str, ok_func)
	end
end

function LongMaiShopView:OnClickOpenTjView()
	CrossLongMaiWGCtrl.Instance:OpenLongMaiLibrary()
end


----------LongMaiShopCell--------
LongMaiShopCell = LongMaiShopCell or BaseClass(BaseRender)

function LongMaiShopCell:__init()
	self.shop_item = {}

	for i =1, 2 do
		self.shop_item[i] = ItemCell.New(self.node_list["item_cell" .. i])
		self.node_list["buy_btn" .. i].button:AddClickListener(BindTool.Bind(self.OnClickBuy, self, i))
		for k = 1, 5 do
		 	self.node_list["icon_" .. i .. "_" .. k].button:AddClickListener(BindTool.Bind(self.ShowItemTips, self, i, k))
		end
	end
end

function LongMaiShopCell:ReleaseCallBack()
	if self.shop_item then
		for k,v in pairs(self.shop_item) do
			v:DeleteMe()
		end
	end

	for i = 1, 2 do
		if self.scale_anim and self.scale_anim[i] then
			self.scale_anim[i]:Kill()
			self.scale_anim[i] = nil
		end
	end

	self.scale_anim = nil
	self:StopAnimDelay()
end

function LongMaiShopCell:OnFlush()
	if IsEmptyTable(self.data) then
		return 
	end

	for i = 1, 2 do
		self.node_list["normal_bg" .. i]:SetActive(true)
	end

	local shop_item_cfg
	local item_cfg
	local stuff_cfg = {}
	if nil == self.old_data then
		self.old_data = self.data
		for i = 1, 2 do
			self.node_list["sell_anim_panel" .. i]:SetActive(false)
			self.node_list["sell_flag" .. i].transform.localScale = Vector2(2,2,2)
			if self.data[i].is_buy == 1 then
				self:JustShowBuy(i)
			end
		end
	else
		for i = 1, 2 do
			if self.old_data[i].is_buy == 0 and self.data[i].is_buy == 1
				and self.old_data[i].id == self.data[i].id then
				self:PlayItemBuyAnim(i)
			elseif self.data[i].is_buy == 1 then
				self:JustShowBuy(i)
			end
		end
		self.old_data = self.data
	end

	if self.data.should_anim == 1 then
		self.data.should_anim = 0
		self:PlayTurnAnim()
	end

	for i = 1, 2 do 
		if self.data and self.data[i] then
			stuff_cfg[i] = {}
			shop_item_cfg = CrossLongMaiWGData.Instance:GetShopByIdInfo(self.data[i].id)
			if IsEmptyTable(shop_item_cfg) then
				break
			end
			self.shop_item[i]:SetData({item_id = shop_item_cfg.product_id,num = 1})
			item_cfg = ItemWGData.Instance:GetItemConfig(shop_item_cfg.product_id)
			if item_cfg and item_cfg.name then
				self.node_list["name" .. i].text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			end
			if self.data[i].is_buy == 1 then
				self.node_list["buy_btn" .. i]:SetActive(false)
			else
				self.node_list["buy_btn" .. i]:SetActive(true)
			end

			for k = 1, 5 do
				if shop_item_cfg["stuff_id_" .. k] > 0 then
					table.insert(stuff_cfg[i], {stuff_id = shop_item_cfg["stuff_id_" .. k], stuff_count = shop_item_cfg["stuff_count_" .. k]})
				end
				self.node_list["price_bg_" .. i .. "_" .. k]:SetActive(false)
			end

			self.node_list["group_" .. i .. "_1"]:SetActive(#stuff_cfg[i] > 0)
			self.node_list["group_" .. i .. "_2"]:SetActive(#stuff_cfg[i] > 3)
			local stuff_item_cfg
			local num
			local color

			for t, v in ipairs(stuff_cfg[i]) do
				self.node_list["price_bg_" .. i .. "_" .. t]:SetActive(true)
				num = ItemWGData.Instance:GetItemNumInBagById(v.stuff_id)
				color = num >= v.stuff_count and COLOR3B.DEFAULT_NUM or COLOR3B.RED
				self.node_list["stuff_count_" .. i .. "_" .. t].text.text = ToColorStr(v.stuff_count, color) 
				stuff_item_cfg = ItemWGData.Instance:GetItemConfig(v.stuff_id)
				self.node_list["icon_" .. i .. "_" .. t].image:LoadSprite(ResPath.GetItem(stuff_item_cfg.icon_id))
			end
		end
	end
end

function LongMaiShopCell:OnClickBuy(index)
	if self.data == nil or self.data[index] == nil then
		return
	end

	local act_is_close = ActivityWGData.Instance:GetActivityIsClose(ACTIVITY_TYPE.KF_LONGMAI)
	if not act_is_close then
		TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.NoBuyStr)
		return
	end

	if self.data[index].is_buy == 1 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.LongMaiShopSellAll)
		return
	end

	local stuff_cfg = {}
	local shop_item_cfg = {}

	if self.data and self.data[index] then
		shop_item_cfg = CrossLongMaiWGData.Instance:GetShopByIdInfo(self.data[index].id)
		for i = 1, 5 do
			if shop_item_cfg["stuff_id_" .. i] > 0 then
				table.insert(stuff_cfg, {stuff_id = shop_item_cfg["stuff_id_" .. i], stuff_count = shop_item_cfg["stuff_count_" .. i]})
			end
		end
	end

	local can_buy = true
	for i, v in ipairs(stuff_cfg) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.stuff_id)
		if num < v.stuff_count then
			can_buy = false
			break
		end
	end
	if can_buy then
		CrossLongMaiWGCtrl.Instance:SendLongMaiShopReq(LONGMAI_OPERA_TYPE.BUY_REWARD, self.data[index].id)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.CrossLongMai.StuffNotEnough)
	end
end

function LongMaiShopCell:ShowItemTips(index, count)
	local stuff_cfg = {}
	local shop_item_cfg = {}

	if self.data and self.data[index] then
		shop_item_cfg = CrossLongMaiWGData.Instance:GetShopByIdInfo(self.data[index].id)
		for i = 1, 5 do
			if shop_item_cfg["stuff_id_" .. i] > 0 then
				table.insert(stuff_cfg, {stuff_id = shop_item_cfg["stuff_id_" .. i], stuff_count = shop_item_cfg["stuff_count_" .. i]})
			end
		end

		if stuff_cfg[count] then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_cfg[count].stuff_id})
		end
	end
end

function LongMaiShopCell:JustShowBuy(index)
	self.node_list["sell_anim_panel" .. index]:SetActive(true)
	if self.scale_anim and self.scale_anim[index] then
		self.scale_anim[index]:Kill()
		self.scale_anim[index] = nil
	end

	self.node_list["sell_flag" .. index].transform.localScale = Vector3(1, 1, 1)
end

function LongMaiShopCell:PlayTurnAnim()
	if self.playing_truning then
		return
	end

	self.playing_truning = true
	for i = 1, 2 do
		self.node_list["normal_bg" .. i].canvas_group.alpha = 0
		self.node_list["normal_bg" .. i].transform.rotation = Quaternion.Euler(0, 180, 0)
		self.node_list["hight_line_bg" .. i].transform.rotation = Quaternion.Euler(0, 0, 0)
		self.node_list["hight_line_bg" .. i].transform.localScale = Vector3(1, 1, 1)
		self.node_list["hight_line_bg" .. i]:SetActive(false)
		self.node_list["sell_anim_panel" .. i]:SetActive(false)
	end

	local base_time = 0
	local time1 = 0.15
	local time2 = 0.15

	self.turning_anim1 =  GlobalTimerQuest:AddDelayTimer(function ()
		for i = 1, 2 do
			self.node_list["hight_line_bg" .. i]:SetActive(true)
			self.node_list["hight_line_bg" .. i].canvas_group.alpha = 1
			self.node_list["normal_bg" .. i].rect:DORotate(Vector3(0, 90, 0), time1, DG.Tweening.RotateMode.FastBeyond360)
			self.node_list["hight_line_bg" .. i].rect:DORotate(Vector3(0, -90, 0), time1, DG.Tweening.RotateMode.FastBeyond360)
			self.node_list["normal_bg" .. i].rect:DOScale(Vector3(1.1, 1.1, 1.1), time1)
			self.node_list["hight_line_bg" .. i].rect:DOScale(Vector3(1.1, 1.1, 1.1), time1)
			self.node_list["normal_bg" .. i].canvas_group:DoAlpha(0, 1, time1)

		end
	end, base_time)

	self.turning_anim2 = GlobalTimerQuest:AddDelayTimer(function ()
		for i = 1, 2 do
			self.node_list["hight_line_bg" .. i]:SetActive(false)
			self.node_list["normal_bg" .. i].rect:DORotate(Vector3(0, 0, 0), time2, DG.Tweening.RotateMode.FastBeyond360)
			self.node_list["normal_bg" .. i].rect:DOScale(Vector3(1, 1, 1), time2)
			self.node_list["hight_line_bg" .. i].rect:DORotate(Vector3(0, -180, 0), time1, DG.Tweening.RotateMode.FastBeyond360)
			self.node_list["hight_line_bg" .. i].canvas_group:DoAlpha(1, 0, 0.2)
		end
	end, base_time + time1)

	self.turning_anim3 = GlobalTimerQuest:AddDelayTimer(function ()
		self.playing_truning = false
		self:ReInitPosition()
	end, base_time + time1 + time2)
end

function LongMaiShopCell:StopAnimDelay()
	if self.turning_anim1 then
		GlobalTimerQuest:CancelQuest(self.turning_anim1)
		self.turning_anim1 = nil
	end

	if self.turning_anim2 then
		GlobalTimerQuest:CancelQuest(self.turning_anim2)
		self.turning_anim2 = nil
	end

	if self.turning_anim3 then
		GlobalTimerQuest:CancelQuest(self.turning_anim3)
		self.turning_anim3 = nil
	end
end

function LongMaiShopCell:ReInitPosition()
	for i = 1, 2 do
 		self.node_list["normal_bg" .. i].canvas_group.alpha = 1
		self.node_list["normal_bg" .. i].transform.rotation = Quaternion.Euler(0, 0, 0)
		self.node_list["normal_bg" .. i].transform.localScale = Vector3(1, 1, 1)
		self.node_list["hight_line_bg" .. i]:SetActive(false)
	end

	for i = 1, 2 do
		if self.data[i].is_buy == 1 then
			self:JustShowBuy(i)
		end
	end
 end

 function LongMaiShopCell:PlayItemBuyAnim(index)
	self.node_list["sell_anim_panel" .. index]:SetActive(true)
	self.node_list["sell_flag" .. index].transform.localScale = Vector3(2, 2, 2)
	if nil == self.scale_anim then
		self.scale_anim = {}
	end

	if self.scale_anim[index] then
		self.scale_anim[index]:Kill()
		self.scale_anim[index] = nil
	end

	self.scale_anim[index] = self.node_list["sell_flag" .. index].rect:DOScale(Vector3(1, 1, 1), 0.4)
	self.scale_anim[index]:SetEase(DG.Tweening.Ease.OutCubic)
end
