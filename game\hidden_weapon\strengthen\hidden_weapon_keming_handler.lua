-- 封装 刻铭处理

HiddenWeaponKemingHandler = HiddenWeaponKemingHandler or BaseClass()

function HiddenWeaponKemingHandler:__init(parent_view)
    self.parent_view = parent_view
    self.node_list = parent_view.node_list
    self.is_auto_keming = false
    self.is_auto_dispose = false
end

function HiddenWeaponKemingHandler:__delete()
    self:ClearTimer()

    self.node_list = nil
    self.parent_view = nil
    self.slider_attr_objs = nil
    if self.jj_cost_item then
        self.jj_cost_item:DeleteMe()
        self.jj_cost_item = nil
    end

    if not IsEmptyTable(self.tweener_up_list) then
        for k,v in pairs(self.tweener_up_list) do
            v:Kill(true)
        end
        self.tweener_up_list = {}
    end

    -- if not IsEmptyTable(self.tweener_down_list) then
    --     for k,v in pairs(self.tweener_down_list) do
    --         v:Kill(true)
    --     end
    --     self.tweener_down_list = {}
    -- end
end

function HiddenWeaponKemingHandler:CloseCallBack()
    self.is_auto_keming = false
    self.is_auto_dispose = false
end

function HiddenWeaponKemingHandler:InscriptionLoadUI()
    self.node_list["txt_keming_break_num"].text.text =
        string.format(Language.ShenJiEquip.KMNext_2, HiddenWeaponWGData.Instance:GetRandBreakNum())
    XUI.AddClickEventListener(
        self.node_list["btn_zkeming"],
        BindTool.Bind1(
            self.OnClickKeMing,
            self
        )
    )
    XUI.AddClickEventListener(
        self.node_list["btn_huanyuan"],
        BindTool.Bind1(
            function()
                HiddenWeaponRequest:ReqRand(self.parent_view:GetWeaponType(), 1)
            end,
            self
        )
    )

    XUI.AddClickEventListener(self.node_list["btn_km_tihuan"], BindTool.Bind1(self.OnClickKeMingTiHuan, self))
    XUI.AddClickEventListener(
        self.node_list["btn_tupo"],
        BindTool.Bind1(
            function()
                HiddenWeaponRequest:ReqRandLimit(self.parent_view:GetWeaponType())
            end,
            self
        )
    )

    XUI.AddClickEventListener(
        self.node_list["btn_auto_keming"],
        BindTool.Bind1(
            self.OnClickAutoKeMing,
            self
        )
    )

    self.slider_attr_objs = {}
    self.tweener_up_list = {}
    self.tweener_down_list = {}
    for index = 1, 6 do
        self.slider_attr_objs[index] =
            U3DNodeList(self.node_list["slider_shuxing_" .. index].uiname_table, self.parent_view)

        self.tweener_up_list[index] =
            self.slider_attr_objs[index]["icon_arrow_up"].transform:DOLocalMoveY(5, 0.8):SetEase(DG.Tweening.Ease.Linear):SetLoops(
            -1,
            DG.Tweening.LoopType.Yoyo
        )

        -- self.tweener_down_list[index] =
        --     self.slider_attr_objs[index]["icon_arrow_down"].transform:DOLocalMoveY(5, 0.8):SetEase(DG.Tweening.Ease.Linear):SetLoops(
        --     -1,
        --     DG.Tweening.LoopType.Yoyo
        -- )
    end
    -- 材料消耗
    self.jj_cost_item = ItemCell.New(self.node_list["keming_item_pos"])
    self.jj_cost_item:SetRightBottomTextVisible(true)
end

function HiddenWeaponKemingHandler:OnClickKeMingTiHuan()
    local ok_func = function(...)
        HiddenWeaponRequest:ReqRand(self.parent_view:GetWeaponType(), 2)
    end

    if self.capability_change and self.capability_change < 0 then
        TipWGCtrl.Instance:OpenAlertTips(Language.ShenJiEquip.KMNext_3, ok_func,nil,nil,nil,nil,nil,nil,nil,Language.ShenJiEquip.KMAlterTitle)
    else
        ok_func()
    end
end

function HiddenWeaponKemingHandler:OnClickKeMing()
    if not self.is_can_km then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.km_consume_cfg.consume_item})
        return
    end
    HiddenWeaponRequest:ReqRand(self.parent_view:GetWeaponType(), 0)
end

function HiddenWeaponKemingHandler:OnClickAutoKeMing()

    self.is_auto_keming = not self.is_auto_keming

    local ok_func = function()
        if self.is_auto_keming and self.is_can_km then
            HiddenWeaponRequest:ReqRand(self.parent_view:GetWeaponType(), 0)
        else
            self.is_auto_keming = false
            self.is_auto_dispose = false
            self:ClearTimer()
            self:Refresh()
        end
    end

    if self.is_auto_keming and self.is_can_km then
        HiddenWeaponWGCtrl.Instance:OpnHWAlertView(ok_func)
    else
        self.is_auto_dispose = false
        self.is_auto_keming = false
        self:ClearTimer()
        self:Refresh()
    end
end

function HiddenWeaponKemingHandler:IsInExchange(protocol_item)
    for index, value in ipairs(protocol_item.next_rand_attr_list) do
        if value > 0 or value < 0 then
            return true
        end
    end
end

function HiddenWeaponKemingHandler:UpdateSliders(protocol_item)
    local weapon_type = self.parent_view:GetWeaponType()
    local attrs_index = HiddenWeaponWGData.Instance:GetColorAttrIndex(weapon_type)

    local star_attr = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type).color_attr

    -- 设置三种状态共有的内容
    local max_break = HiddenWeaponWGData.Instance:GetMaxRandBreak(protocol_item.max_rand_index)
    local min_value = 0
    if protocol_item.max_rand_index > 0 then
        min_value = HiddenWeaponWGData.Instance:GetMaxRandBreak(protocol_item.max_rand_index - 1)
    end
    for index, attr_node in ipairs(self.slider_attr_objs) do
        -- 设置上下限（使数值变化明显一些）
        attr_node["slider_green"].slider.minValue = min_value
        attr_node["slider_yellow"].slider.minValue = min_value
        attr_node["slider_red"].slider.minValue = min_value
        attr_node["slider_green"].slider.maxValue = max_break
        attr_node["slider_yellow"].slider.maxValue = max_break
        attr_node["slider_red"].slider.maxValue = max_break

        local curr = protocol_item.rand_attr_list[index] or 0
        local curr_next = protocol_item.next_rand_attr_list[index] or 0
        -- 设置属性值
        local attr_value = star_attr[attrs_index[index]] or 0
        attr_node["slider_shuxing_1"]:SetActive(attr_value > 0)
        if attrs_index[index] == "base_attr_per" then
            local attr_name = Language.Common.AttrNameList2[attrs_index[index]] or ""
            attr_node["txt_base_attr_1"].text.text = string.format(Language.HiddenWeapon.KeMingAttrTxt, attr_name, tostring(math.ceil(attr_value * (curr / 10000))))
        else
            local attr_name = Language.Common.AttrNameList2[attrs_index[index]] or ""
            attr_node["txt_base_attr_1"].text.text = string.format(Language.HiddenWeapon.KeMingAttrTxt2, attr_name, tostring(math.ceil(attr_value * (curr / 10000))))
        end

        attr_node["txt_percent_main"].text.text = "<color=#FFFFFFFF>" .. tostring(curr / 100) .. "%" .. "</color>"
        -- 通用状态隐藏变化的东西
        -- attr_node["txt_plus"]:SetActive(false)
        attr_node["icon_arrow_up"]:SetActive(false)
        attr_node["icon_arrow_down"]:SetActive(false)
        attr_node["txt_percent1"]:SetActive(false)
        attr_node["txt_percent2"]:SetActive(false)
        -- 只有黄条
        attr_node["slider_red"].slider.value = 0
        attr_node["slider_yellow"].slider.value = curr
        attr_node["slider_green"].slider.value = 0
        if attrs_index[index] == "base_attr_per" then
            attr_node["txt_plus"].text.text = ""
                -- "<color=#9DF5A7>+" .. tostring(math.ceil(attr_value * (curr / 1000000))) .. "%</color>"
        else
            attr_node["txt_plus"].text.text = ""
                -- "<color=#9DF5A7>+" .. tostring(math.ceil(attr_value * (curr / 10000))) .. "</color>"
        end
        attr_node["txt_plus"]:SetActive(true)
    end
    if self:IsInExchange(protocol_item) then
        -- 替换中，红黄绿进度都有
        for index, attr_node in ipairs(self.slider_attr_objs) do
            local curr = protocol_item.rand_attr_list[index] or 0
            local next = protocol_item.next_rand_attr_list[index] or 0
            local change = next - curr

            -- 设置属性值变化
            local attr_value = star_attr[attrs_index[index]] or 0
            attr_node["txt_percent1"].text.text = tostring(change / 100) .. "%"
            attr_node["txt_percent2"].text.text = tostring(change / 100) .. "%"

            if change > 0 then
                attr_node["txt_percent_main"].text.text = tostring(next / 100) .. "%"
                attr_node["icon_arrow_up"]:SetActive(true)
                attr_node["icon_arrow_down"]:SetActive(false)
                attr_node["txt_percent1"]:SetActive(true)
                attr_node["txt_percent2"]:SetActive(false)
                -- attr_node["slider_red"].slider.value = 0
                -- attr_node["slider_yellow"].slider.value = curr
                -- attr_node["slider_green"].slider.value = next
                attr_node["slider_red"].slider.value = 0
                attr_node["slider_yellow"].slider:DOValue(curr,0.5)
                attr_node["slider_green"].slider:DOValue(next,0.5)
                -- local cur_value = math.ceil(attr_value * (next / 1000000))
                -- local next_value = math.ceil(attr_value * (next / 1000000))
                if attrs_index[index] == "base_attr_per" then
                    attr_node["txt_plus"].text.text = "+" .. tostring(math.ceil(attr_value * (change / 1000000)) .. "%")
                else
                    attr_node["txt_plus"].text.text = "+" .. tostring(math.ceil(attr_value * (change / 10000)))
                end
            elseif change < 0 then
                -- 百分比下降
                attr_node["txt_percent_main"].text.text =
                    "<color=#FF9797>" .. tostring(next / 100) .. "%" .. "</color>"
                if attrs_index[index] == "base_attr_per" then
                    attr_node["txt_plus"].text.text =
                        "<color=#FF9797>" .. tostring(math.ceil(attr_value * (change / 1000000))) .. "%</color>"
                else
                    attr_node["txt_plus"].text.text =
                        "<color=#FF9797>" .. tostring(math.ceil(attr_value * (change / 10000))) .. "</color>"
                end
                attr_node["icon_arrow_up"]:SetActive(false)
                attr_node["icon_arrow_down"]:SetActive(true)
                attr_node["txt_percent1"]:SetActive(false)
                attr_node["txt_percent2"]:SetActive(true)
                -- attr_node["slider_red"].slider.value = curr
                -- attr_node["slider_yellow"].slider.value = next
                -- attr_node["slider_green"].slider.value = 0
                attr_node["slider_red"].slider.value = curr
                attr_node["slider_yellow"].slider:DOValue(next, 0.5)
                attr_node["slider_green"].slider.value = 0
            else
                attr_node["txt_plus"].text.text = ""
            end
        end
    end
end

function HiddenWeaponKemingHandler:Refresh()
    self.node_list["km_max_img"]:SetActive(false)
    local weapon_type = self.parent_view:GetWeaponType()
    local equip_list = HiddenWeaponWGData.Instance:GetSCShenJiEquipGrid()
    local data = equip_list[weapon_type]
    if not data or not data.equip then
        return
    end

    -- 判断是否能刻铭
    if not HiddenWeaponWGData.Instance:CanEquipKm(data) then
        self.node_list["empty_img"]:SetActive(true)
        self.node_list["layout_qianghua_keming_root"]:SetActive(false)
        self.node_list["no_tips_qh"].text.text = Language.ShenJiEquip.NOT_KEMING_TIPS[weapon_type]
        return
    end

    self.node_list["layout_qianghua_keming_root"]:SetActive(true)
    self.node_list["empty_img"]:SetActive(false)

    local protocol_item = HiddenWeaponWGData.Instance:GetZLInfo(weapon_type)
    -- 材料消耗
    local consume_cfg = HiddenWeaponWGData.Instance:GetKemingConsumeCfg(weapon_type)
    self.jj_cost_item:SetData({item_id = consume_cfg.consume_item})
    local has_num = ItemWGData.Instance:GetItemNumInBagById(consume_cfg.consume_item)
    local color = has_num >= consume_cfg.consume_num and "#72eba9" or "#FF9797"

    self.is_can_km = has_num >= consume_cfg.consume_num
    self.km_consume_cfg = consume_cfg

    self.jj_cost_item:SetRightBottomTextVisible(true)
    self.jj_cost_item:SetRightBottomText(
        "<color=" .. color .. ">" .. has_num .. "/" .. consume_cfg.consume_num .. "</color>"
    )

    if protocol_item == nil then
        return
    end

    self:UpdateSliders(protocol_item)
    local is_need_tupo = self:isNeedTupo(protocol_item)
    self:UpdateAutoBtnTxt()

    -- 是否达到刻铭上限
    if HiddenWeaponWGData.Instance:isKmMax(protocol_item) == true then
        self.node_list["type_keming"]:SetActive(false)
        self.node_list["type_keming_tihuan"]:SetActive(false)
        self.node_list["type_keming_tupo"]:SetActive(false)
        self.node_list["km_max_img"]:SetActive(true)

        self.is_auto_keming = false
        self.is_auto_dispose = false
        return
    end

    -- 是否是自动洗髓
    if self.is_auto_keming then
        self:PlayTuPoEffect(is_need_tupo)
        self:AutoKeMing()
        return
    end

    -- 是否在替换中
    self.capability_change = nil
    if self:IsInExchange(protocol_item) then
        self.node_list["type_keming"]:SetActive(false)
        self.node_list["type_keming_tihuan"]:SetActive(true)
        self.node_list["type_keming_tupo"]:SetActive(false)
        local capability_change = protocol_item.next_capability
        self.capability_change = capability_change
        if capability_change < 0 then
            self.node_list["txt_keming_next"].text.text =
                string.format(Language.ShenJiEquip.KMNext_1, "<color=#FF9797>" .. capability_change .. "</color>")
        else
            self.node_list["txt_keming_next"].text.text =
                string.format(Language.ShenJiEquip.KMNext_1, "<color=#9DF5A7>+" .. capability_change .. "</color>")
        end
        return
    end

    self:PlayTuPoEffect(is_need_tupo)

    -- 是否在突破
    if is_need_tupo then
        self.node_list["type_keming"]:SetActive(false)
        self.node_list["type_keming_tihuan"]:SetActive(false)
        self.node_list["type_keming_tupo"]:SetActive(true)
        return
    end

    -- 在刻铭
    self.node_list["type_keming"]:SetActive(true)
    self.node_list["type_keming_tihuan"]:SetActive(false)
    self.node_list["type_keming_tupo"]:SetActive(false)
end

function HiddenWeaponKemingHandler:PlayTuPoEffect(is_need_tupo)
    if not self.old_is_need_tupo then
        self.old_is_need_tupo = is_need_tupo
    end

    if self.old_is_need_tupo ~= is_need_tupo and not is_need_tupo then
        local bundle, asset = ResPath.GetEffectUi("UI_shenpin_baodian_cheng")
        EffectManager.Instance:PlayAtTransform(bundle, asset,self.node_list["tupo_effect"].transform)
    end
    self.old_is_need_tupo = is_need_tupo
end

function HiddenWeaponKemingHandler:isNeedTupo(protocol_item)
    local break_num = HiddenWeaponWGData.Instance:GetRandBreakNum()
    local max_break = HiddenWeaponWGData.Instance:GetMaxRandBreak(protocol_item.max_rand_index)
    local next_cfg = HiddenWeaponWGData.Instance:GetRandBreakCfg(protocol_item.max_rand_index + 1)
    -- 没有下一级，说明当前已是最高进度，直接让玩家刻铭到满
    if next_cfg == nil then
        return false
    end
    local break_count = 0
    for index, value in ipairs(protocol_item.rand_attr_list) do
        if value >= max_break then
            break_count = break_count + 1
        end
    end
    if break_count >= break_num then
        return true
    else
        return false
    end
end

function HiddenWeaponKemingHandler:AutoKeMing()
    self:ClearTimer()

    self.node_list["type_keming"]:SetActive(true)
    self.node_list["type_keming_tihuan"]:SetActive(false)
    self.node_list["type_keming_tupo"]:SetActive(false)

    local function auto_keming()
        if not self.is_can_km then
            self.is_auto_keming = false
            self.is_auto_dispose = false
            self:UpdateAutoBtnTxt()
            return
        end

        HiddenWeaponRequest:ReqRand(self.parent_view:GetWeaponType(), 0)
    end

    if self.is_auto_dispose then
        self.is_auto_dispose = false
        auto_keming()
        return
    end

    self.auto_keming_timer = GlobalTimerQuest:AddDelayTimer(function ()
        if self.is_auto_keming and self.parent_view and self.km_consume_cfg then
            if self.old_is_need_tupo then
                HiddenWeaponRequest:ReqRandLimit(self.parent_view:GetWeaponType())
                self.is_auto_dispose = true
                return
            end

            local weapon_type = self.parent_view:GetWeaponType()
            local protocol_item = HiddenWeaponWGData.Instance:GetZLInfo(weapon_type)
            self.capability_change = nil
            if self:IsInExchange(protocol_item) then
                local capability_change = protocol_item.next_capability
                self.capability_change = capability_change
                local change_type = self.capability_change > 0 and 2 or 1
                HiddenWeaponRequest:ReqRand(self.parent_view:GetWeaponType(), change_type)
                self.is_auto_dispose = true
                return
            end

            auto_keming()
        end
    end, 0.5)
end

function HiddenWeaponKemingHandler:ClearTimer()
    if self.auto_keming_timer then
		GlobalTimerQuest:CancelQuest(self.auto_keming_timer)
		self.auto_keming_timer = nil
	end
end

function HiddenWeaponKemingHandler:ClearAutoFlag()
    self.is_auto_keming = false
    self.is_auto_dispose = false
end

function HiddenWeaponKemingHandler:UpdateAutoBtnTxt()
    if self.node_list and self.node_list.auto_keming_txt then
        self.node_list.auto_keming_txt.text.text = self.is_auto_keming and self.is_can_km and Language.ShenJiEquip.AutoKeMingBtnStr[1] or Language.ShenJiEquip.AutoKeMingBtnStr[2]
    end
end