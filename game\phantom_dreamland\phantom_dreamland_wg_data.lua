PhantomDreamlandWGData = PhantomDreamlandWGData or BaseClass()

function PhantomDreamlandWGData:__init()
	if PhantomDreamlandWGData.Instance ~= nil then
		ErrorLog("[PhantomDreamlandWGData] Attemp to create a singleton twice !")
	end
	PhantomDreamlandWGData.Instance = self

	local auto_cfg = ConfigManager.Instance:GetAutoConfig("fb_dream_secret_auto")
	if auto_cfg then
        self.base_cfg = auto_cfg.other[1]
		self.grade_cfg = auto_cfg.grade[1]
		self.level_cfg = auto_cfg.level
		self.wave_cfg = ListToMap(auto_cfg.wave, "grade", "wave")
		self.wave_card_cfg = ListToMap(auto_cfg.card, "index", "seq")
		self.rank_reward_cfg = ListToMapList(auto_cfg.rank_reward, "grade")
		self.monster_cfg = ListToMapList(auto_cfg.monster, "grade", "wave")
	end

	self.base_info = {}
	RemindManager.Instance:Register(RemindName.PhantomDreamland, BindTool.Bind(self.GetDreamlandRemind, self))
end

function PhantomDreamlandWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.PhantomDreamland)
	self.base_info = nil

	PhantomDreamlandWGData.Instance = nil
end

---------------------------------------------------------------------------------
--获取基础配置表
function PhantomDreamlandWGData:GetBaseCfg()
	return self.base_cfg
end

-- --获取所有的
-- function PhantomDreamlandWGData:GetLevelList()
-- 	return self.level_cfg
-- end

--获取关卡表
function PhantomDreamlandWGData:GetLevelCfg()
	local grade = self:GetCurGrade()
	return self.level_cfg[grade]
end

-- 获取进入消耗灵玉数量
function PhantomDreamlandWGData:GetConsumeLingYu()
	return (self.grade_cfg or {}).consume_lingyu or 0
end

-- --获取关卡表根据关卡
-- function PhantomDreamlandWGData:GetLevelCfgByLevel(level)
-- 	local empty = {}
-- 	return (self.level_cfg or empty)[level]
-- end

--获取所有的波次
function PhantomDreamlandWGData:GetWaveCfgList()
	local grade = self:GetCurGrade()
	return self.wave_cfg[grade] or {}
end

-- 波次总数
function PhantomDreamlandWGData:GetTotalWaveNum()
	local wave_list = self:GetWaveCfgList()
	return #wave_list
end

--获取关卡某个波次配置
function PhantomDreamlandWGData:GetWaveCfgByWave(wave)
	local grade = self:GetCurGrade()
	return (self.wave_cfg[grade] or empty)[wave]
end

--获取下标所有卡牌配置
function PhantomDreamlandWGData:GetCardListByIndex(index)
	local empty = {}
	return (self.wave_card_cfg or empty)[index]
end

--获取下标某个卡牌配置
function PhantomDreamlandWGData:GetCardCfgByIndexSeq(index, seq)
	local empty = {}
	return ((self.wave_card_cfg or empty)[index] or empty)[seq]
end

--获取排名奖励
function PhantomDreamlandWGData:GetRankRewardCfgList()
    local empty = {}
	local grade = self:GetCurGrade()
	return (self.rank_reward_cfg or empty)[grade] or empty
end

function PhantomDreamlandWGData:GetMonsterCfgByWave(wave)
    local empty = {}
	local grade = self:GetCurGrade()
	return ((self.monster_cfg or empty)[grade] or empty)[wave] or empty
end

---------------------------------------------------------------------------------
-- 副本基础信息
function PhantomDreamlandWGData:SetDreamSecretBaseInfo(protocol)
	self.base_info = {}
    self.base_info.grade = protocol.grade
	self.base_info.max_pass_wave = protocol.max_pass_wave
	self.base_info.buy_time_count = protocol.buy_time_count
	self.base_info.wave_pass_reward_flag = protocol.wave_pass_reward_flag
	self.base_info.harm_value = protocol.harm_value
end

-- 获取当前档次
function PhantomDreamlandWGData:GetCurGrade()
	return self.base_info.grade or 1
end

-- 获取最高伤害记录
function PhantomDreamlandWGData:GetBestHarmValue()
	return self.base_info.harm_value or 0
end

-- 获取当前波次
function PhantomDreamlandWGData:GetCurWave()
	return self.base_info.max_pass_wave or 4
end

-- 获取波次奖励是否领取
function PhantomDreamlandWGData:GetWaveRewardFetchFlag(wave)
	return (self.base_info.wave_pass_reward_flag or {})[wave] or 0
end

--[[
-- 获取当前的等级
function PhantomDreamlandWGData:GetCurrExpWestPassLevel()
	local empty = {}
	local level = (self.base_info or empty).level or 1
	return level
end


-- 获取当前的波数
function PhantomDreamlandWGData:GetCurrExpWestWave()
    local wave_list = self:GetCurrDreamlandAllWavePass()
    -- 加一个i过滤掉0号位置
    for i, v in ipairs(wave_list) do
        if v == 0 then
            return i
        end
    end

    return 1
end
]]
-- 获取当前所有的波数
function PhantomDreamlandWGData:GetCurrDreamlandAllWavePass()
	local empty = {}
	return (self.base_info or empty).wave_pass_reward_flag
end

-- 场景信息
function PhantomDreamlandWGData:SetDreamlandSceneInfo(protocol)
	self.scene_info = protocol
end

-- 获取场景信息
function PhantomDreamlandWGData:GetDreamlandSceneInfo()
	return self.scene_info
end

-- 排行信息
function PhantomDreamlandWGData:SetDreamSecretRankInfo(protocol)
	self.rank_info = protocol.rank_item_list
	self.self_rank = protocol.self_rank
end

-- 获取排行信息
function PhantomDreamlandWGData:GetRankList()
	return self.rank_info or {}
end

-- 获取排行信息
function PhantomDreamlandWGData:GetSelfRank()
	return self.self_rank or 0
end

-- 卡片信息
function PhantomDreamlandWGData:SetDreamlandCardInfo(protocol)
	self.card_item_list = protocol.card_item_list
end

-- 获取某一波的卡牌信息
function PhantomDreamlandWGData:GetCardInfoByWave(wave)
	local empty = {}
	return (self.card_item_list or empty)[wave]
end

-- 获取所有的卡牌信息
function PhantomDreamlandWGData:GetAllCardInfo()
	return self.card_item_list
end

-- 设置技能能量
function PhantomDreamlandWGData:SetDreamlandSkillInfo(protocol)
	self.skill_power = protocol.skill_power
end

-- 获取技能能量
function PhantomDreamlandWGData:GetDreamlandSkillPower()
    return self.skill_power or 0
end

-- 设置购买时间toggle的状态
function PhantomDreamlandWGData:SetBuyTimesCheckState(is_on)
	self.is_all_buy = is_on
end

-- 获取购买时间toggle的状态
function PhantomDreamlandWGData:GetBuyTimesCheckState()
    return self.is_all_buy or false
end

-- 是否有可领取奖励
function PhantomDreamlandWGData:GetRewardRemind()
	if IsEmptyTable(self.base_info) then
		return false, 1
	end
	
	local cur_wave = self.base_info.max_pass_wave
	for i = 1, cur_wave do
		if self:GetWaveRewardFetchFlag(i) ~= 1 then
			return true, i
		end
	end
	
	return false, 1
end

-- 获取红点
function PhantomDreamlandWGData:GetDreamlandRemind()
	if self:GetRewardRemind() then
		return 1
	end
	return 0
end