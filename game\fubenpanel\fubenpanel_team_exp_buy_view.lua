
TeamExpBuy = TeamExpBuy or BaseClass(SafeBaseView)
-- 多人经验本购买次数
function TeamExpBuy:__init()
	self.is_modal = true
	self:LoadConfig()
	self.fb_type = 0
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
end

function TeamExpBuy:__delete()

end

function TeamExpBuy:ReleaseCallBack()
	if self.lbl_now_buy_count then
		self.lbl_now_buy_count:DeleteMe()
		self.lbl_now_buy_count = nil
	end

	if self.lbl_next_can_buy then
		self.lbl_next_can_buy:DeleteMe()
		self.lbl_next_can_buy = nil
	end
end

-- 加载配置
function TeamExpBuy:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/team_exp_fb_prefab", "layout_vip_count")
end

function TeamExpBuy:LoadCallBack()
	--self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(589,412)
	self:SetSecondView(nil, self.node_list["size"])
	self.node_list["title_view_name"].text.text = Language.FuBenPanel.SecondViewNameAddTimes
	self.node_list["btn_buy"].button:AddClickListener(BindTool.Bind(self.BuyCount, self))
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClickCancel, self))
end

function TeamExpBuy:OpenCallBack()

end

function TeamExpBuy:ShowIndexCallBack(index)
	self:Flush(index)
end

function TeamExpBuy:OnClickCancel()
	self:Close()
end

function TeamExpBuy:BuyCount()
	if self.fb_type == FUBEN_TYPE.FBCT_WUJINJITAN  then
		FuBenWGCtrl.Instance:SendWuJinJiTanReq(TEAM_EXP_TYPE.BUY_ENTER_COUNT)
		--self:Close()
	elseif self.fb_type == FUBEN_TYPE.LINGHUNGUANGCHANG  then
			FuBenWGCtrl.Instance:SendWuJinJiTanReq(TEAM_EXP_TYPE.LINGHUNSQUA_REQ_TYPE_BUY_ONCE)

	end
end

function TeamExpBuy:CloseCallBack()

end

function TeamExpBuy:SetFbType(fb_type)
	self.fb_type = fb_type
end

function TeamExpBuy:OnFlush(param_t, index)
	local str = string.format(Language.FuBenPanel.CopperBuyNum, WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1].buy_once_need_gold)

	self.node_list["rich_buy_desc"].text.text = str

	local role_info = GameVoManager.Instance:GetMainRoleVo()
	self.node_list["lbl_now_buy_count"].text.text = "V"..role_info.vip_level


	local now_count = 0
	local yet_buy_time = 0
	local next_level = 0
	local next_count = 0
	if self.fb_type == FUBEN_TYPE.FBCT_WUJINJITAN then
		now_count = VipPower.Instance:GetParam(VipPowerId.exp_fb_buy_times)
		yet_buy_time = WuJinJiTanWGData.Instance:GetTeamFbBuyTimes()
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.exp_fb_buy_times ,now_count) 
	elseif self.fb_type == FUBEN_TYPE.FBCT_TONGBIBEN then
		now_count = VipPower.Instance:GetParam(VipPowerId.coin_fb_buy_times)
		local copper_info = FuBenPanelWGData.Instance:GetTongBiInfo()
		yet_buy_time = copper_info.day_buy_times
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.coin_fb_buy_times ,now_count)
	elseif self.fb_type == FUBEN_TYPE.FBCT_PETBEN then
		now_count = VipPower.Instance:GetParam(VipPowerId.pet_fb_buy_times)
		local pet_info = FuBenPanelWGData.Instance:GetPetAllInfo()
		yet_buy_time = pet_info.buy_times
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.pet_fb_buy_times ,now_count)
	elseif self.fb_type == FUBEN_TYPE.FBCT_TAFANG then
		now_count = VipPower.Instance:GetParam(VipPowerId.tafang_fb_buy_times)
		yet_buy_time = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.tafang_fb_buy_times ,now_count)
	elseif self.fb_type == FUBEN_TYPE.LINGHUNGUANGCHANG then
		--暂时先用经验副本的
		now_count = VipPower.Instance:GetParam(VipPowerId.exp_fb_buy_times)
		yet_buy_time = WuJinJiTanWGData.Instance:GetTeamFbBuyTimes()
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.exp_fb_buy_times ,now_count)
	end

	self.node_list["lbl_next_can_buy"].text.text = "V"..next_level
	local des1
	local temp_str = now_count - yet_buy_time
	if temp_str > 0 then
    	des1 = string.format(Language.FuBenPanel.CopperBuyTips, now_count - yet_buy_time,now_count)
    else
    	des1 = string.format(Language.FuBenPanel.CopperBuyTipsNotnough, now_count - yet_buy_time, now_count)
    end
	--local des1 = string.format(Language.FuBenPanel.CopperBuyTips, now_count - yet_buy_time, now_count)
	local des2 = string.format(Language.FuBenPanel.CopperBuyTips2, next_count)
	if role_info.vip_level >= next_level then
		des2 = Language.FuBenPanel.CopperBuyTips3
	end
	self.node_list["rich_now_can_buy"].text.text = des1

	self.node_list["rich_next_can_buy"].text.text = des2

	if next_level == RoleWGData.Instance.role_vo.vip_level then
		
		self.node_list["layout_next_vip"]:SetActive(true)
		self.node_list["img_arrow"]:SetActive(true)
	else
		
		self.node_list["layout_next_vip"]:SetActive(true)
		self.node_list["img_arrow"]:SetActive(true)
	end
	
	if param_t then
		for k,v in pairs(param_t) do
			if "buy_count" == k then
				if now_count - yet_buy_time <= 0 then
					--self:Close()
				end
			end
		end
	end
end
