ChessBoardTreasueRewardView = ChessBoardTreasueRewardView or BaseClass(SafeBaseView)

function ChessBoardTreasueRewardView:__init()
    self.view_layer = UiLayer.Pop
    self.view_style = ViewStyle.Window

    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/chessboard_treasue_ui_prefab", "layout_chessboard_treasures_reward")
end

function ChessBoardTreasueRewardView:LoadCallBack()
	if not self.preview_grid then
        self.preview_grid = AsyncBaseGrid.New()
        self.preview_grid:CreateCells({
			col = 5, 
			list_view = self.node_list.preview_grid, 
			itemRender = ChessBoardPreviewGridItem,
			change_cells_num = 1,
			assetBundle = "uis/view/chessboard_treasue_ui_prefab",
			assetName = "preview_grid_Item",
		})
		self.preview_grid:SetStartZeroIndex(false)
	end

	if not self.preview_type_list then
		self.preview_type_list = AsyncListView.New(ChessBoardPreviewTypeItemRender, self.node_list.preview_type_list)
		self.preview_type_list:SetStartZeroIndex(true)
		self.preview_type_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectPreviewTypeCB, self))
	end
end

function ChessBoardTreasueRewardView:ReleaseCallBack()
	if self.preview_type_list then
		self.preview_type_list:DeleteMe()
		self.preview_type_list = nil
	end

	if self.preview_grid then
		self.preview_grid:DeleteMe()
		self.preview_grid = nil
	end

	self.curr_type_index = nil
end


function ChessBoardTreasueRewardView:OnSelectPreviewTypeCB(type_item, cell_index, is_default, is_click)
    if nil == type_item or nil == type_item.data then
		return
	end

	local item_index = type_item:GetIndex()
	local data = type_item.data
	if self.curr_type_index == item_index then
        return
	end

	self.curr_type_index = item_index
	self:FlushPreviewGrid(data)
end

function ChessBoardTreasueRewardView:FlushPreviewGrid(data)
	if IsEmptyTable(data) then
		return
	end

	self.preview_grid:SetDataList(data)
end

function ChessBoardTreasueRewardView:OnFlush(param_t)
	local type_list = ChessBoardTreasueData.Instance:GetCurRoundDataList()
	self.preview_type_list:SetDataList(type_list)

    local exchange_type, rate = ChessBoardTreasueData.Instance:GetCurDayExchangeTypeAndRate()
    local str = ""
    if exchange_type == 0 then
        str = Language.ChessboardField.ChessBoardAdsTip1
    elseif exchange_type == 1 then
        str = Language.ChessboardField.ChessBoardAdsTip2
    end
    self.node_list.text_tips.text.text = string.format(str, rate)
end

---------------------------ChessBoardPreviewGridItem------------------------------------
ChessBoardPreviewGridItem = ChessBoardPreviewGridItem or BaseClass(BaseRender)

function ChessBoardPreviewGridItem:LoadCallBack()
    if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_pos)
	end
end

function ChessBoardPreviewGridItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ChessBoardPreviewGridItem:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData(self.data.item)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item.item_id)
	local item_color = item_cfg and item_cfg.color or 0
	local item_name = item_cfg and item_cfg.name or ""
	self.node_list.item_name.text.text = ToColorStr(item_name, ITEM_COLOR[item_color])
end

---------------------ChessBoardPreviewTypeItemRender-----------------------------
ChessBoardPreviewTypeItemRender = ChessBoardPreviewTypeItemRender or BaseClass(BaseRender)

function ChessBoardPreviewTypeItemRender:OnFlush()
	if not self.data then
		return
	end

	local str = string.format(Language.ChessboardField.ChessCurRound, self.index + 1)
	self.node_list.normal_text.text.text = str
	self.node_list.select_text.text.text = str
end

function ChessBoardPreviewTypeItemRender:OnSelectChange(is_select)
	self.node_list.normal:CustomSetActive(not is_select)
	self.node_list.select:CustomSetActive(is_select)
end