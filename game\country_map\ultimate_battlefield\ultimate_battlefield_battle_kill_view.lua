UltimateBattleKillView = UltimateBattleKillView or BaseClass(SafeBaseView)

local TIME_DOWN = 3

function UltimateBattleKillView:__init()
	self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_ultimate_fail")
	self:SetMaskBg(true, true)
end

function UltimateBattleKillView:LoadCallBack()
end

function UltimateBattleKillView:ReleaseCallBack()
    self:CleanTimeDown()
    self.killer_name = nil
end

function UltimateBattleKillView:OpenCallBack()
	
end

function UltimateBattleKillView:CloseCallBack()

end

function UltimateBattleKillView:SetKillerName(killer_name, killer_level, is_role, plat_type, server_id, uid)
    self.killer_name = killer_name
	self:FlushBeKilltext()
end

function UltimateBattleKillView:OnFlush()
	self:FlushBeKilltext()
    -- 触发选择天赋
    self:FlushTimeCountDownEnter()
end

function UltimateBattleKillView:FlushBeKilltext()
	if self.node_list and self.node_list.text_bekilled then
		self.node_list.text_bekilled.text.text = string.format(Language.UltimateBattlefield.FailedTips, self.killer_name or "")
	end
end

-----------------活动时间倒计时-------------------
function UltimateBattleKillView:FlushTimeCountDownEnter()
    -- 这里减1秒是防止误差，这边关了界面和协议打开在同一秒
    self:FlushTimeCountDown(TIME_DOWN)
end

function UltimateBattleKillView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("ultimate_battle_kill_down") then
		CountDownManager.Instance:RemoveCountDown("ultimate_battle_kill_down")
	end
end

function UltimateBattleKillView:FlushTimeCountDown(end_time)
    self:CleanTimeDown()

    self.node_list.close_txt.text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, end_time)
    CountDownManager.Instance:AddCountDown("ultimate_battle_kill_down",
    BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), nil, end_time , 1)
end

function UltimateBattleKillView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.close_txt.text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, valid_time)
	end
end

function UltimateBattleKillView:OnComplete()
    self.node_list.close_txt.text.text = ""
	self:Close()
end

