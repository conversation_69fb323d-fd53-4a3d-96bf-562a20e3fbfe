﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_AudioItemWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>ginClass(typeof(Nirvana.AudioItem), typeof(UnityEngine.ScriptableObject));
		<PERSON><PERSON>RegFunction("Reset", Reset);
		L.RegFunction("Play", Play);
		L.<PERSON>Function("IsValid", IsValid);
		<PERSON><PERSON>unction("New", _CreateNirvana_AudioItem);
		L<PERSON>RegFunction("__eq", op_Equality);
		L<PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Delay", get_Delay, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateNirvana_AudioItem(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.AudioItem obj = new Nirvana.AudioItem();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Nirvana.AudioItem.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Reset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.AudioItem obj = (Nirvana.AudioItem)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioItem));
			obj.Reset();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.AudioItem obj = (Nirvana.AudioItem)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioItem));
			Nirvana.AudioSourcePool arg0 = (Nirvana.AudioSourcePool)ToLua.CheckObject(L, 2, typeof(Nirvana.AudioSourcePool));
			Nirvana.IAudioController o = obj.Play(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsValid(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.AudioItem obj = (Nirvana.AudioItem)ToLua.CheckObject(L, 1, typeof(Nirvana.AudioItem));
			bool o = obj.IsValid();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Delay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.AudioItem obj = (Nirvana.AudioItem)o;
			float ret = obj.Delay;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Delay on a nil value");
		}
	}
}

