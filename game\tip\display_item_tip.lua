------------------------------------------------------------
--物品tip
------------------------------------------------------------
DisplayItemTip = DisplayItemTip or BaseClass(ItemTip)

local TypeGameObjectAttach = typeof(Game.GameObjectAttach)

DisplayItemTip.Display_type = {
	MOUNT_LINGCHONG = 1,					--坐骑
	FASHION = 2,							--时装
	FABAO = 3,								--法宝
	BABY = 4,								--宝宝
	LINGGONG = 7,                           --灵弓
	CHENGHAO = 8,							--称号
	PKMOUNT = 9,							--战骑
	LINGQI = 10,							--灵骑
	LINGYI = 11,							--灵翼
    WING = 12,								--羽翼
    LINGCHONG = 13,                         --灵宠
	HALO = 14,                         		--光环
	XIAOGUI = 15,							--守护(守护仙女,经验熊猫)
	BUBBLE = 16, 							--气泡
	PHOTOFRAME = 17,						--相框
	TIANSHEN = 18,							--天神
	KUN = 19,								--鲲
	TIANSHEN_SHENSHIWAIGUAN = 20,			--天神神饰外观
	LIANSHI = 21,							--脸饰
	YAOSHI = 22,							--腰饰
	WEIBA = 23,							    --尾巴
	SHOUHUAN = 24,							--手环
	JIANZHEN = 25,							--剑阵
	SHENBING = 26,							--神兵
	WUQI = 27,								--武器
	FOOT = 28,								--足迹
	SHENQI = 29,							--天神神器
	FIGHTSOUL = 30,							--战魂（四象）
	XIANQI = 31,							--仙器
	BACKGROUND = 32,						--背景
	WUHUNZHENSHEN = 33,						--武魂真身
	NEWFIGHTMOUNT = 34,                     --战斗坐骑（大龙）
	MINGQI = 35,                     		--命器
	ESOTERICA = 36,							--秘笈
	BEASTS = 37,							--灵兽
	Mecha = 38,                             --机甲
	TIANSHENSHUANGSHENG = 39,				--双生天神
	ANGER_APPE_IMAGE = 40,					--怒气形象
	MINGWEN = 41,							--妖魂形象（云篆）
	LNGYU = 42,                             --领域
	WEAPON = 43,							--装备
	BEASTS_SKIN = 44,						--幻兽皮肤
	BOSS = 45,								--怪兽
	MULTI_MOUNT = 46,                       -- 多人坐骑
}

local IS_IMAGE = { --是否为图片
	[DisplayItemTip.Display_type.CHENGHAO] = true,
	[DisplayItemTip.Display_type.BUBBLE] = true,
	[DisplayItemTip.Display_type.PHOTOFRAME] = true,
	[DisplayItemTip.Display_type.BACKGROUND] = true,
	[DisplayItemTip.Display_type.FIGHTSOUL] = true,
	[DisplayItemTip.Display_type.WEAPON] = true,
}

local SKILL_TITLE_NAME = {--技能标题
	[DisplayItemTip.Display_type.WING] = Language.F2Tip.WingSkillTitle,
	[DisplayItemTip.Display_type.SHENBING] = Language.F2Tip.ShenBingTitle,
	[DisplayItemTip.Display_type.WUQI] = Language.F2Tip.ShenBingTitle,
	[DisplayItemTip.Display_type.SHENQI] = Language.F2Tip.ShenQiTitle,
}

function DisplayItemTip:__init()
end

function DisplayItemTip:__delete()
end

function DisplayItemTip:ReleaseCallBack()
	if self.suit_item_list then
		self.suit_item_list:DeleteMe()
		self.suit_item_list = nil
	end

	self:ReleaseTipsPanel()
end

function DisplayItemTip:LoadCallBack()
	self:InitTipsPanel()
end

function DisplayItemTip:ShowIndexCallBack()
	self:FlushView()
	self:Flush()
	self:FlushGetWayView()
	self:FlushQuickBuyPanel()
end

function DisplayItemTip:CloseCallBack()
	-- self.is_foot_view = false
	TipWGCtrl.Instance:TryOpenWaitItemTips()
end

function DisplayItemTip:OnFlush(param_t)
	self:ShowUpStarLevel()
	self:ShowDisplay()
	self:ShowSkillObj()
	self:ShowSpecialSkillObj()
	self:ShowWardrobeSuit()
	self:ShowSuitSpecialPanel()
end

function DisplayItemTip:ShowWardrobeSuit()
     --如果是材料，需要显示合成的目标装备的属性
    local item_id = ItemWGData.Instance:GetComposeProductid(self.data.item_id)
	local suit = WardrobeWGData.Instance:IsWardrobeSuitByItemId(item_id)
	if not suit then
		return
	end

	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(suit)
	if IsEmptyTable(suit_data) then
		return
	end

	local info = {}
	info.suit_title = string.format(Language.F2Tip.WardrobeTitle, suit_data.suit_name, suit_data.act_part_num, suit_data.total_part_num)
	local theme_cfg = WardrobeWGData.Instance:GetThemeCfgBySuit(suit)
	info.theme_type = theme_cfg and theme_cfg.theme_type or 1
	info.attr_list = WardrobeWGData.Instance:GetAttrBySuit(suit)
	self.base_tips:SetWardrobeSuitAttribute(info)
end

-- 神兵技能
-- 羽翼技能
function DisplayItemTip:ShowSpecialSkillObj()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if not item_cfg then
		return
	end
    --如果是材料，需要显示合成的目标装备的技能，
    local revert_item_id = ItemWGData.Instance:GetComposeProductid(self.data.item_id)

	local display_type = item_cfg.is_display_role
	if nil == display_type or display_type == 0 or display_type == "" then
		return
	end

	if display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI
        or display_type == DisplayItemTip.Display_type.WING then
        local cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(revert_item_id)
		if cfg then
			local skill_cfg = NewAppearanceWGData.Instance:GetFashionSkillCfg(cfg.part_type, cfg.index, 1)
			if skill_cfg then
				local bundle,asset = ResPath.GetSkillIconById(skill_cfg.skill_icon)
				local info_list = {}
				info_list.title = SKILL_TITLE_NAME[display_type]
				info_list.skill_icon_bundle = bundle
				info_list.skill_icon_asset = asset
				info_list.skill_name = skill_cfg.skill_name
				info_list.skill_desc = skill_cfg.skill_describe
				self.base_tips:SetShenBingSkill(info_list)
			end
		end
	end

	if display_type == DisplayItemTip.Display_type.SHENQI then

		local shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(item_cfg.id)
		if not shenqi_cfg then
			local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
			if compose_cfg then
				shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(compose_cfg.product_id)
			end
			if not shenqi_cfg then
				return
			end
		end
		local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(shenqi_cfg.index)
		local now_shenqi_cfg = TianShenWGData.Instance:GetShenQiStarCfg(shenqi_cfg.index, 0) --henqi_info and shenqi_info.star or 0)
		local shenqi_zhu_skill = TianShenWGData.Instance:GetShenQiSkillInfo(shenqi_cfg.index,0)--,shenqi_info and shenqi_info.star or 0)
    	local skill_cfg = TianShenWGData.Instance:GetShenQiSkillCfg(shenqi_zhu_skill[1].skill,now_shenqi_cfg.skill_level)
    	if skill_cfg then
        	local bundle, asset = ResPath.GetSkillIconById(skill_cfg.skill_icon)
        	local info_list = {}
			info_list.title = SKILL_TITLE_NAME[display_type]
			info_list.skill_icon_bundle = bundle
			info_list.skill_icon_asset = asset
			info_list.skill_name = skill_cfg.skill_name
			if shenqi_zhu_skill[1].is_active then
            	info_list.skill_desc =  skill_cfg.skill_des
       		else
            	info_list.skill_desc = skill_cfg.skill_des --.."\n".. ToColorStr(Language.TianShen.ShenQiActiveOpen,COLOR3B.D_RED)
        	end
			self.base_tips:SetShenBingSkill(info_list)
		end
	end
end

function DisplayItemTip:ShowSkillObj()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if not item_cfg then
		return
	end

	local display_type = item_cfg.is_display_role
	if nil == display_type or display_type == 0 or display_type == "" then
		return
    end
    --如果是材料，需要显示合成的目标装备的技能，
    local revert_item_id = ItemWGData.Instance:GetComposeProductid(self.data.item_id)
    local temp_data = {item_id = revert_item_id}
	local data = nil
	local other_name = nil

	if display_type == DisplayItemTip.Display_type.BABY then --宝宝
		data = MarryWGData.Instance:GetBabySkillData(temp_data)
	elseif display_type == DisplayItemTip.Display_type.TIANSHEN then --天神
		data = TianShenWGData.Instance:GetTianShenSkillData(temp_data)
		other_name = Language.F2Tip.TianShenSkill
	elseif display_type == DisplayItemTip.Display_type.KUN then --鲲
		data = NewAppearanceWGData.Instance:GetKunTipsSkillData(temp_data)
	elseif display_type == DisplayItemTip.Display_type.MOUNT_LINGCHONG then --坐骑
        data = NewAppearanceWGData.Instance:GetQiChongSkillData(temp_data, MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
    elseif display_type == DisplayItemTip.Display_type.LINGCHONG then --灵宠
		data = NewAppearanceWGData.Instance:GetQiChongSkillData(temp_data, MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG)
	end

	if data then
		self:FlushSkillObj(data, other_name)
	end
end

function DisplayItemTip:FlushSkillObj(data, other_name)
	local info_list = {}
	info_list.title = other_name or Language.F2Tip.WaiGuanSkill
	info_list.skill_list_1 = data.data_list_1	--主动技能
	info_list.skill_list_2 = data.data_list_2	--专属技能
	info_list.skill_list_3 = data.data_list_3	--被动技能
	for i=1,3 do
		if not IsEmptyTable(info_list["skill_list_" .. i]) then
			self.base_tips:SetItemSkill(info_list)
			break
		end
	end
end

-- 模型展示
function DisplayItemTip:ShowDisplay()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		print_error("DisplayItemTip","ShowDisplay() item_cfg is a nil value: item_id = ",self.data.item_id)
		return
	end

	if item_cfg.id and item_cfg.id > 0 then
		item_cfg = ItemWGData.Instance:GetItemConfig(item_cfg.id)
		if item_cfg == nil then
			print_error("DisplayItemTip","ShowDisplay() item_cfg is a nil value: id = ",item_cfg.id)
			return
		end
	end

	local display_type = item_cfg.is_display_role
	if nil == display_type or item_cfg.is_display_role == 0 or item_cfg.is_display_role == "" then
		return
	end

    self.model_display = self.base_tips:GetModle(true, MODEL_CAMERA_TYPE.BASE)
	self.model_display:SetCameraOffsetType(nil)
	self.model_display:SetRTAdjustmentRootLocalPosition(0, 0, 0)
	self.model_display:SetRTAdjustmentRootLocalScale(1)
    self.base_tips:CancelWeaponTween()
	-- self:ClearFootEff()

	self.lingyu_model_display = self.base_tips:GetLingYuModle(MODEL_CAMERA_TYPE.BASE)
	self.lingyu_model_display:SetCameraOffsetType(nil)

	if MechaWGData.Instance:IsMechaSysItem(self.data.item_id) then
		self:FlushMechaModel(item_cfg)
		self.base_tips:FixModelCameraPos()
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
		self:SetMechaUseBtn(item_num > 0)
		return
	end

	local path, bundle, asset, res_id, image_type, attr_cfg, animation_type, name, part_type

	if display_type == DisplayItemTip.Display_type.MOUNT_LINGCHONG then 			--坐骑
        res_id, attr_cfg = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_cfg.id)
		local item_id
		if item_cfg.param2 then
			item_id = string.split(item_cfg.param2, "|")
		end
		res_id = res_id or item_id[1]
		path = ResPath.GetMountModel
        animation_type = "mount"
	elseif display_type == DisplayItemTip.Display_type.MULTI_MOUNT then 			-- 多人坐骑
		local multi_mount_cfg = MultiMountWGData.Instance:GetItemShowCfgByItemId(item_cfg.id)

		if multi_mount_cfg and multi_mount_cfg.image_id then
			res_id = multi_mount_cfg.image_id
		end
		
		path = ResPath.GetMountModel
		animation_type = "mount"
    elseif display_type == DisplayItemTip.Display_type.LINGCHONG then 			--灵宠
        res_id, attr_cfg = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_cfg.id)
        local item_id
        if item_cfg.param2 then
            item_id = string.split(item_cfg.param2, "|")
        end
        res_id = res_id or item_id[1]
        path = ResPath.GetPetModel
        animation_type = "lingjian"

	elseif display_type == DisplayItemTip.Display_type.KUN then 				--鲲
		path = ResPath.GetMountModel
		local kun_base_cfg = NewAppearanceWGData.Instance:GetKunActCfgByItemId(item_cfg.id)
		if kun_base_cfg then
			res_id = kun_base_cfg.active_id
		end
	elseif display_type == DisplayItemTip.Display_type.FASHION or display_type == DisplayItemTip.Display_type.HALO then
		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		part_type = part_type or item_cfg.param1
		path, res_id, animation_type = self:GetFashionModlePathFun(res_id, part_type, item_cfg)

		if part_type == SHIZHUANG_TYPE.HALO then
			self.model_display:SetMainAsset(ResPath.GetHaloModel(res_id))
		else
			self:ShowRoleModel(res_id)
			local _, weapon_res_id, __ = AppearanceWGData.Instance:GetRoleResId()

			self.model_display:SetWeaponResid(weapon_res_id)
		end

		return
	elseif display_type == DisplayItemTip.Display_type.LIANSHI or
		display_type == DisplayItemTip.Display_type.YAOSHI or
		display_type == DisplayItemTip.Display_type.WEIBA or
		display_type == DisplayItemTip.Display_type.SHOUHUAN or
		display_type == DisplayItemTip.Display_type.FOOT then 				--时装   + 默认的剑-- +当前武器模型

		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		part_type = part_type or item_cfg.param1

		if res_id and part_type then
			path, res_id, animation_type = self:GetFashionModlePathFun(res_id, part_type, item_cfg)
		end

		if part_type == SHIZHUANG_TYPE.FOOT then--足迹不展示武器
			-- self.model_display:SetRotation(MODEL_ROTATION_TYPE.FOOT)
			-- self.model_display:PlayRoleAction(SceneObjAnimator.Move)
			-- self.model_display:SetFootTrailModel(res_id)
		else
			local _,weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
			self.model_display:SetWeaponResid(weapon_res_id)
		end
	elseif display_type == DisplayItemTip.Display_type.WING then 						--羽翼
		--1.获取翅膀模型
		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		local item_id
		if item_cfg.param2 then
			item_id = string.split(item_cfg.param2, "|")
		end
		res_id = res_id or item_id[1]
		path = ResPath.GetWingModel
		animation_type = "wing"

	elseif display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI then 						--神兵
        res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = attr_cfg and attr_cfg.resouce or item_cfg.param2

        -- self.base_tips:PlayWeaponTween()
		if res_id then
			self:ShowWeapon(res_id)
		end
		return
	elseif display_type == DisplayItemTip.Display_type.FABAO then 						--法宝
		res_id, image_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		path = ResPath.GetFaBaoModel
	elseif display_type == DisplayItemTip.Display_type.JIANZHEN then 					--剑阵
		res_id, image_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		path = ResPath.GetJianZhenModel

	elseif display_type == DisplayItemTip.Display_type.BABY then 					--宝宝
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		res_id, attr_cfg = MarryWGData.Instance:GetBabyResByItemId(show_id)
		if attr_cfg then
			path = ResPath.GetHaiZiModel
			animation_type = "soul"
		end
	elseif display_type == DisplayItemTip.Display_type.CHENGHAO then 				--称号
		attr_cfg = TitleWGData.Instance:GetConfig(item_cfg.param1)
		local title_id = item_cfg.param1
		local asset, bundle = ResPath.GetTitleModel(title_id)
		self.base_tips:SetTitleCell(title_id, asset, bundle)
		return
	elseif display_type == DisplayItemTip.Display_type.ESOTERICA then					-- 秘笈
		local slot_cfg
		
		if 1 == item_cfg.is_chip then
			slot_cfg = CultivationWGData.Instance:GetEsotericaCfg(item_cfg.param1)
		else
			local part_item_cfg = CultivationWGData.Instance:GetEsotericaPartItemCfg(item_cfg.id)

			if part_item_cfg then
				slot_cfg = CultivationWGData.Instance:GetEsotericaCfg(part_item_cfg.seq)
			end
		end

		if slot_cfg then
			local pos_x, pos_y = 0, 0
			if slot_cfg.item_show_img_pos and slot_cfg.item_show_img_pos ~= "" then
				local pos_list = string.split(slot_cfg.item_show_img_pos, "|")
				pos_x = tonumber(pos_list[1]) or pos_x
				pos_y = tonumber(pos_list[2]) or pos_y
			end

			local bundle1, asset1 = ResPath.GetRawImagesPNG("a3_xf_rwlh" .. slot_cfg.img_id)
			local img_info = {
				bundle = bundle1,
				asset = asset1,
				scale = slot_cfg.item_show_img_scale,
				pos_x = pos_x,
				pos_y = pos_y,
			}

			local bundle2, asset2 = ResPath.GetA2Effect(slot_cfg.ui_effect_asset)
			local effect_info = {
				bundle = bundle2,
				asset = asset2,
				scale = slot_cfg.item_show_img_scale,
				pos_x = pos_x,
				pos_y = pos_y,
			}

			self.base_tips:SetImgAndEffect(img_info, effect_info)
		end

		return
	elseif display_type == DisplayItemTip.Display_type.XIAOGUI then 					--小鬼
		path = ResPath.GetGuardModel
		-- attr_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		if xiaogui_cfg then
			res_id = xiaogui_cfg.appe_image_id
		end
	elseif display_type == DisplayItemTip.Display_type.BUBBLE then 					--气泡
		local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(self.data.item_id)
		if not fashion_cfg or IsEmptyTable(fashion_cfg) then return end
		local bubble_id = fashion_cfg.resouce or 0

		local asset, bundle = ResPath.ChatBigBubbleBig(bubble_id)
		self.base_tips:SetBubbleCell(asset, bundle)
		return
	elseif display_type == DisplayItemTip.Display_type.PHOTOFRAME then 				--相框
		local photoframe_id = item_cfg.param2
		local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
		if not IsEmptyTable(compose_cfg) then
			local item_cfg2 = ItemWGData.Instance:GetItemConfig(compose_cfg.product_id)
			photoframe_id = item_cfg2.param2
		end
		local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(SHIZHUANG_TYPE.PHOTOFRAME, photoframe_id)
		photoframe_id = cfg and cfg.resouce or photoframe_id
		local data = {fashion_photoframe = photoframe_id}
		local head_cell = self.base_tips:GetHeadCell()
		head_cell:SetImgBg(true)
		head_cell:SetData(data)
		head_cell:SetBgActive(false)
		return
	elseif display_type == DisplayItemTip.Display_type.TIANSHEN then 				--天神
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		local magic_image = TianShenWGData.Instance:GetAppeImage(show_id)
		if not magic_image then
			magic_image = TianShenHuamoWGData.Instance:GetHuaMoAttributeCfgByItem(show_id)
		end
		if not magic_image then
			show_id = item_cfg.param2	--虚拟道具要显示模型时
			magic_image = TianShenWGData.Instance:GetAppeImage(show_id)
			if not magic_image then
				return
			end
		end
		-- if magic_image.tianshen_location then
		-- 	self.base_tips:SetDingWeiInfo(magic_image.tianshen_location)
		-- end
		self.model_display:SetTianShenModel(magic_image.appe_image_id, magic_image.index, false)

	elseif display_type == DisplayItemTip.Display_type.TIANSHEN_SHENSHIWAIGUAN then 				--天神神饰外观
		local shenqi_waiguan_cfg = TianShenWGData.Instance:GetWaiGuanCfgByStuff(item_cfg.id)
		if not shenqi_waiguan_cfg then
			return
		end

		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(shenqi_waiguan_cfg.index)
		if not tianshen_cfg then
			return
		end

		local b, a = ResPath.GetBianShenModel(tianshen_cfg.appe_image_id)
		self.model_display:SetMainAsset(b, a)
		bundle, asset = TianShenWGData.Instance:GetTianShenWeapon(tianshen_cfg.index, shenqi_waiguan_cfg.waiguan_id)
		self.model_display:SetWeaponModel(bundle, asset)
	elseif display_type == DisplayItemTip.Display_type.SHENQI then --天神神器

		local shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(item_cfg.id)
		if not shenqi_cfg then
			local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
			if compose_cfg then
				shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(compose_cfg.product_id)
			end
			if not shenqi_cfg then
				return
			end
		end

		local b, a = ResPath.GetTianShenShenQiPath(shenqi_cfg.ts_res_idui)
		self.model_display:SetMainAsset(b, a)
		-- self.base_tips:PlayWeaponTween()
	elseif display_type == DisplayItemTip.Display_type.FIGHTSOUL then -- 战魂（四象）
		self.model_display:SetCameraOffsetType(MODEL_OFFSET_TYPE.NORMALIZE)
		local fs_cfg = FightSoulWGData.Instance:GetFightSoulItemCfg(self.data.item_id)
		if fs_cfg then
			res_id = fs_cfg.sixiang_type
			bundle, asset = ResPath.GetFightSoulShowUI(res_id)
			self.base_tips:SetSpineCell(bundle, asset)
		end
		return
	elseif display_type == DisplayItemTip.Display_type.BACKGROUND then	--背景
		local res_id, weapon_res_id, __ = AppearanceWGData.Instance:GetRoleResId()
		self:ShowRoleModel(res_id)
		self.model_display:SetWeaponResid(weapon_res_id)
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		self:SetBackgroundCellLoader(show_id)
		-- local asset, bundle = ResPath.BackgroundItem(item_cfg.id)
		-- self.base_tips:SetBackgroundCell(asset, bundle)
		return
	elseif display_type == DisplayItemTip.Display_type.WUHUNZHENSHEN then
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		local wuhun_active_cfg = WuHunWGData.Instance:GetWuhunResByItemId(show_id)
		
		if wuhun_active_cfg then
			local bundle, asset = ResPath.GetWuHunModel(wuhun_active_cfg.appe_image_id)
			self.model_display:SetMainAsset(bundle, asset)
			self.model_display:PlayRoleAction()
		end
	elseif display_type == DisplayItemTip.Display_type.NEWFIGHTMOUNT then
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		local cfg = NewFightMountWGData.Instance:GetUpgradeCostCfg(show_id)
		local mount_seq = cfg and cfg.mount_seq or 0
		local mount_type_cfg = NewFightMountWGData.Instance:GetMountTypeCfgBySeq(mount_seq)
		res_id = mount_type_cfg.appe_image_id
		path = ResPath.GetMountModel
        animation_type = "mount"
	elseif display_type == DisplayItemTip.Display_type.MINGQI then
		self.model_display:SetCameraOffsetType(MODEL_OFFSET_TYPE.NORMALIZE)
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		res_id = ArtifactWGData.Instance:GetArtifactCfgByItemId(show_id).model_id
		bundle, asset = ResPath.GetShuangXiuTipUI(res_id)
		self.base_tips:SetSpineCell(bundle, asset)
		self.base_tips:SetLeftShowModelDiVisible(false)
		return
	elseif display_type == DisplayItemTip.Display_type.BEASTS then
		path = ResPath.GetBeastsModel
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		local beasts_id = show_id
		if ControlBeastsWGData.Instance:CheckIsHolyBeastUnlockItemId(item_cfg.id) then
			beasts_id = item_cfg.param2
		end
		res_id = ControlBeastsWGData.Instance:GetBeastModelResId(beasts_id)
		res_id = (res_id and res_id ~= 0) and res_id or item_cfg.param2
		animation_type = "beast"
	elseif display_type == DisplayItemTip.Display_type.BEASTS_SKIN then
		path = ResPath.GetBeastsModel
		res_id = ControlBeastsWGData.Instance:GetBeastModelSkinResIdByItemId(item_cfg.id)
		animation_type = "beast"
	elseif display_type == DisplayItemTip.Display_type.TIANSHENSHUANGSHENG then
		path = ResPath.GetShuangShengModel
		local cfg = TianShenShuangShengWGData.Instance:GetShuangShengTianShenAttrByItemId(item_cfg.id)
		if cfg then
			local app_image_id_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(cfg.avatar_id)
			if app_image_id_data then
				res_id = app_image_id_data.appe_image_id
				animation_type = "shaungshengtianshen"
			end
		end
	elseif display_type == DisplayItemTip.Display_type.ANGER_APPE_IMAGE then
		local show_nuqi_type = item_cfg.param1
		local show_nuqi_lv = CultivationWGData.Instance:GetAngerLevel(show_nuqi_type)
	
		local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(show_nuqi_type, show_nuqi_lv)
		if cfg ~= nil then
			local role_vo = GameVoManager.Instance:GetMainRoleVo()
			local special_status_table = {ignore_halo = true, ignore_wing = true}
			self.model_display:SetModelResInfo(role_vo, special_status_table)
			self.model_display:SetAngerImage(show_nuqi_type, true, cfg.default_body, cfg.default_face, cfg.default_hair)
		end
	elseif display_type == DisplayItemTip.Display_type.MINGWEN then
		local mingwen_cell = self.base_tips:GetMingWenCell()
		mingwen_cell:SetIsShowItemTip(false)
		mingwen_cell:SetData({item_id = self.data.item_id})
	elseif display_type == DisplayItemTip.Display_type.LNGYU then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true}
		self.model_display:SetModelResInfo(role_vo, special_status_table)
		self.model_display:SetRTAdjustmentRootLocalPosition(0, 0, 0)
		self.model_display:SetRTAdjustmentRootLocalScale(0.6)

		local lingyu_type = SupremeFieldsWGData.Instance:GetFootLightTypeByItemId(item_cfg.id)
		self.lingyu_model_display:SetMainAsset(ResPath.GetSkillFaZhenModel(lingyu_type))
		-- self.lingyu_model_display:SetRTAdjustmentRootLocalPosition(0, 2.8, 0)
		-- self.lingyu_model_display:SetRTAdjustmentRootLocalScale(1.8)

		-- self.model_display:SetLingYuModel(SupremeFieldsWGData.Instance:GetFootLightTypeByItemId(item_cfg.id))
		-- path = ResPath.GetSkillFaZhenModel
		-- 
	elseif display_type == DisplayItemTip.Display_type.WEAPON then 					--装备
		local res_str = item_cfg.resouce
		if nil == res_str or "" == res_str then
			return
		end
		local bundle, asset = ResPath.GetRawImagesPNG(res_str)
		self.base_tips:GetWeaponCell(asset, bundle)
		return
	elseif display_type == DisplayItemTip.Display_type.BOSS then 					--Boss
		res_id = res_id or item_cfg.param2
		path = ResPath.GetMonsterModel
	end

	-- local capability = 0
	-- if attr_cfg then
	-- 	local attr = AttributeMgr.GetAttributteByClass(attr_cfg)
	-- 	capability = AttributeMgr.GetCapability(attr)
	-- end
	-- if capability > 0 then
	-- 	self.base_tips:SetCapabilityPanel({capability = capability})
	-- end

	if display_type ~= DisplayItemTip.Display_type.FOOT then
		self:FlushModel(path, res_id, animation_type)

		if display_type ~= DisplayItemTip.Display_type.BEASTS or display_type ~= DisplayItemTip.Display_type.BEASTS_SKIN then
			self.base_tips:FixModelCameraPos()
		end
	end
end

function DisplayItemTip:CustomDisplayPositionAndRotation(position, rotation, scale)
	self.model_display:CustomDisplayPositionAndRotation(nil, rotation, nil)
end

function DisplayItemTip:FlushModel(path_fun, res_id, animation_type, other_asset)
	if path_fun == nil or res_id == nil then
		return
	end

	if self.model_display then
		local bundle, asset = path_fun(res_id)
		self.model_display:SetMainAsset(bundle, asset)
	end

	if animation_type and animation_type ~= "" then
		if animation_type == "mount" then 			--坐骑
			self.model_display:PlayMountAction()
		elseif animation_type == "wing" then 		--羽翼
			self.model_display:PlayWingAction()
		elseif animation_type == "soul" then 		--灵童
			self.model_display:PlaySoulAction()
		elseif animation_type == "soul_no_do_idle" then --灵童, 无需再播待机动作
			self.model_display:PlaySoulAction()
		elseif animation_type == "lingjian" then
			self.model_display:PlayLingJianAction()
		elseif animation_type == "shaungshengtianshen" then
			self.model_display:PlayRoleAction(SceneObjAnimator.Rest, nil, true)
		elseif animation_type == "beast" then
			self.model_display:PlayRoleAction(SceneObjAnimator.Rest, nil, true)
		end
	end
end

function DisplayItemTip:GetFashionModlePathFun(res_id, part_type, item_cfg)
	if part_type == nil then
		print_error("DisplayItemTip:GetFashionModlePathFun() can not found the type!! id:::", item_cfg and item_cfg.id)
		return nil
	end

	local path = nil
	local animation_type = nil
	-- self.is_foot_view = false

	if part_type == SHIZHUANG_TYPE.BODY then							--时装
		path = ResPath.GetRoleModel
		local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(item_cfg.id)
		if fashion_cfg then
			local prof = GameVoManager.Instance:GetMainRoleVo().prof
			res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
		end
	elseif part_type == SHIZHUANG_TYPE.FOOT then							--足迹
		-- self.is_foot_view = true
		path = nil
		-- self.foot_effect_id = res_id
		-- if not self.use_update then
		-- 	Runner.Instance:AddRunObj(self, 8)
		-- 	self.use_update = true
		-- end

		self:ShowRoleModel()
		self.model_display:SetRotation(MODEL_ROTATION_TYPE.FOOT)
		self.model_display:PlayRoleAction(SceneObjAnimator.Move)
		self.model_display:SetFootTrailModel(res_id)
	elseif part_type == SHIZHUANG_TYPE.HALO then						--光环
		path = ResPath.GetHaloModel
	elseif part_type == SHIZHUANG_TYPE.LINGGONG then						--灵弓
		path = ResPath.GetSoulBoyWeaponModel2
	elseif part_type == SHIZHUANG_TYPE.MASK then 							--脸饰
		self:ShowRoleModel()
		self.model_display:SetMaskResid(res_id)
	elseif part_type == SHIZHUANG_TYPE.BELT then 							--腰饰
		self:ShowRoleModel()
		self.model_display:SetWaistResid(res_id)
	elseif part_type == SHIZHUANG_TYPE.WEIBA then 							--尾巴
		self:ShowRoleModel()
		self.model_display:SetTailResid(res_id)
		self.model_display:SetRotation(MODEL_ROTATION_TYPE.WEIBA)
	elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then 						--手环
		self:ShowRoleModel()
		self.model_display:SetShouHuanResid(res_id)
	else

	end

	return path, res_id, animation_type
end

function DisplayItemTip:ShowWeapon(weapon_res_index)
    local weapon_res_id, weapon_res_id_2 = AppearanceWGData.GetFashionWeaponIdByResViewId(weapon_res_index)
    local bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)
    self.model_display:SetMainAsset(bundle, asset)
end

--显示当前玩家模型
function DisplayItemTip:ShowRoleModel(role_res_id, ani_name)
	ani_name = ani_name or SceneObjAnimator.UiIdle

	if role_res_id then
		local extra_role_model_data = {
			animation_name = ani_name,
		}
		self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	else
		role_res_id = AppearanceWGData.Instance:GetRoleResId()
		local main_role = Scene.Instance:GetMainRole()
		local vo = main_role and main_role:GetVo()
		local d_body_res, d_hair_res, d_face_res
		if vo and vo.appearance then
			if vo.appearance.fashion_body == 0 then
				d_body_res = vo.appearance.default_body_res_id
				d_hair_res = vo.appearance.default_hair_res_id
				d_face_res = vo.appearance.default_face_res_id
			end
		end

		local extra_role_model_data = {
			d_face_res = d_face_res,
			d_hair_res = d_hair_res,
			d_body_res = d_body_res,
			animation_name = ani_name,
		}
		self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	end
end

--显示当前模型激活状态
function DisplayItemTip:ShowUpStarLevel()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		return
	end

	if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG then
		return
	end

	local check_item_id = self.data.item_id
	-- print_error("item_cfg.is_chip", item_cfg.is_chip, check_item_id)
	--判断是否为碎片类型
	if item_cfg.is_chip == 1 then
		local comp_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
		if not IsEmptyTable(comp_cfg) and comp_cfg.product_id then--合成--特殊--化形
			check_item_id = comp_cfg.product_id
		end
	end

	local part_type = NewAppearanceWGData.Instance:GetFashionTypeByItemId(check_item_id)	
	local fashion_level = NewAppearanceWGData.Instance:GetFashionLevelByItemId(check_item_id)
	-- print_error("FFFF==== part_type, fashion_level", part_type, fashion_level)
	local desc = ""
	if part_type and part_type >= 0 then
		if fashion_level > 0 then
			local max_level = NewAppearanceWGData.Instance:GetFashionMaxLevelById(check_item_id) or 0
			local is_not_max_level = fashion_level < max_level
            if is_not_max_level then
                if fashion_level - 1 == 0 then
                    desc = Language.Tip.FashionState[1] --"状态：已激活"
                else
                    desc = string.format(Language.Tip.FashionState[4], fashion_level - 1)
                end
			elseif IS_IMAGE[item_cfg.is_display_role] then
				desc = Language.Tip.FashionState[1] --"状态：已激活"
			else
				desc = Language.Tip.FashionState[3] --"状态：已满星"
			end
		else
			desc = Language.Tip.FashionState[2] --"状态：未激活"
		end
	else
		local is_active = false
		local title_cfg = TitleWGData.Instance:GetTitleConfigByItemId(check_item_id) -- 是否为称号
		local back_cfg = BackgroundWGData.Instance:GetBackgroundCfgByItemId(check_item_id)
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenActItemCfgByActId(check_item_id) --是否为天神
		local shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(check_item_id)
		local tianshen_huamo_cfg = TianShenHuamoWGData.Instance:GetHuaMoAttributeCfgByItem(check_item_id)
		local wuhun_cfg = WuHunWGData.Instance:GetWuhunResByItemId(check_item_id)
		local tianshenshuangsheng_cfg = TianShenShuangShengWGData.Instance:GetShuangShengTianShenAttrByItemId(check_item_id)

        if part_type == -1 then
            if MarryWGData.Instance:IsBabyActiveCard(check_item_id) then
                is_active = MarryWGData.Instance:GetBabyActiveByItemId(check_item_id)
           	elseif title_cfg and not IsEmptyTable(title_cfg) then -- 判断称号是否激活
           		is_active = TitleWGData.Instance:IsThisTitleActive(title_cfg.title_id)
			elseif back_cfg and not IsEmptyTable(back_cfg) then	-- 判断背景是否激活
				is_active = BackgroundWGData.Instance:IsActivation(back_cfg.background_id)
           	elseif tianshen_cfg and not IsEmptyTable(tianshen_cfg) then -- 判断天神是否激活
           		is_active = TianShenWGData.Instance:IsActivation(tianshen_cfg.index)
           	elseif shenqi_cfg and not IsEmptyTable(shenqi_cfg) then	-- 判断神器是否激活
           		is_active = TianShenWGData.Instance:IsShenQiActivation(shenqi_cfg.index)
			elseif tianshen_huamo_cfg and not IsEmptyTable(tianshen_huamo_cfg) then	-- 判断天神化魔是否激活
				is_active = TianShenHuamoWGData.Instance:IsHuaMoActive(tianshen_huamo_cfg)
			elseif wuhun_cfg and not IsEmptyTable(wuhun_cfg) then	-- 判断武魂是否激活
				is_active = WuHunWGData.Instance:IsWuHunActive(wuhun_cfg)
			elseif tianshenshuangsheng_cfg and not IsEmptyTable(tianshenshuangsheng_cfg) then	-- 判断武魂是否激活
				is_active = TianShenShuangShengWGData.Instance:IsShuangshengActive(tianshenshuangsheng_cfg)
            else
                is_active = NewAppearanceWGData.Instance:GetQiChongIsActByItemId(check_item_id)
            end
	  	else
	  		is_active = NewAppearanceWGData.Instance:GetFashionIsActByItemId(check_item_id)
	  	end

		if is_active then
			desc = Language.Tip.FashionState[1]
		else
			desc = Language.Tip.FashionState[2] --"状态：未激活"
		end
	end

	local display_type = item_cfg.is_display_role
	if display_type == DisplayItemTip.Display_type.BEASTS then
		local rich_level = ""
		if ControlBeastsWGData.Instance:CheckIsHolyBeastUnlockItemId(check_item_id) then
			desc = string.format(Language.Tip.ZhuangBeiLeiXing_1, F2_TIPS_COLOR.SOCRE, Language.ShowItemType[item_cfg.goods_type])
			rich_level = string.format(Language.Tip.ShiYongDengJiNew, F2_TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
		else
			desc = string.format(Language.Tip.ZhuangBeiLeiXing, TIPS_COLOR.SOCRE, Language.Tip.BeastsType)
			local lev_str = ""
			if not self.data.is_beast then
				lev_str = Language.Tip.BeastsNotIncubate
			else
				local beast_client_data = ControlBeastsWGData.Instance:GetBeastDataById(self.data.bag_id)
				if beast_client_data and beast_client_data.server_data then
					lev_str = beast_client_data.server_data.beast_level
				end
			end
			rich_level = string.format(Language.Tip.DengJiNew, TIPS_COLOR.SOCRE, lev_str)
		end
		self.base_tips:SetSyntheticalSocre(rich_level)
	elseif display_type == DisplayItemTip.Display_type.Mecha then
		local active = MechaWGData.Instance:GetMechaItemActiveState(check_item_id)
		desc = active and Language.Tip.FashionState[1] or Language.Tip.FashionState[2]
	elseif display_type == DisplayItemTip.Display_type.MINGQI then
		local active = ArtifactWGData.Instance:MingQiIsActiveById(check_item_id)
		desc = active and Language.Tip.FashionState[1] or Language.Tip.FashionState[2]
	elseif display_type == DisplayItemTip.Display_type.NEWFIGHTMOUNT then
		local active = NewFightMountWGData.Instance:IsActiveByItemId(check_item_id)
		desc = active and Language.Tip.FashionState[1] or Language.Tip.FashionState[2]
	elseif display_type == DisplayItemTip.Display_type.WEAPON then
		local rich_type_text = ""
		desc, rich_type_text = self:GetEquipTitleNormalInfo()
		self.base_tips:SetSyntheticalSocre(rich_type_text)
	elseif display_type == DisplayItemTip.Display_type.BEASTS_SKIN then
		local active = ControlBeastsWGData.Instance:BeastSkinIsActiveById(check_item_id)
		desc = active and Language.Tip.FashionState[1] or Language.Tip.FashionState[2]
	elseif display_type == DisplayItemTip.Display_type.MINGWEN then
		local active = MingWenWGData.Instance:IsActive(check_item_id)
		desc = active and Language.Tip.FashionState[1] or Language.Tip.FashionState[2]
	elseif display_type == DisplayItemTip.Display_type.ESOTERICA then
		local active = CultivationWGData.Instance:GetEsotericaIsActive(check_item_id)
		desc = active and Language.Tip.FashionState[1] or Language.Tip.FashionState[2]
	elseif display_type == DisplayItemTip.Display_type.MULTI_MOUNT then
		local active = MultiMountWGData.Instance:GetMultiMountIsActive(check_item_id)
		desc = active and Language.Tip.FashionState[1] or Language.Tip.FashionState[2]
	end

	if item_cfg.goods_type == GameEnum.E_TYPE_JIE_LING then
		desc = string.format(Language.Tip.ZhuangBeiLeiXing, TIPS_COLOR.SOCRE, Language.Tip.JieLingType)
		local rich_level = ""
		rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
		self.base_tips:SetSyntheticalSocre(rich_level)
	end

	self.base_tips:SetEquipSocre(desc)
end

function DisplayItemTip:GetEquipTitleNormalInfo()
	local data = self.data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then
		return
	end

	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local sex = RoleWGData.Instance:GetRoleSex()
	local prof = RoleWGData.Instance:GetRoleProf()

	local rich_part_text = ""

	if TianShenWGData.Instance:IsTianShenEquip(item_cfg.sub_type) then
		if data.grade_level then
			rich_part_text = string.format(Language.Tip.Order, data.grade_level) .. Language.TianShen.EquipNameList[TianShenWGData.Equip_Pos[item_cfg.sub_type]]
		else
			rich_part_text = Language.TianShen.TianShenShenShi
		end
	elseif HiddenWeaponWGData.IsHiddenWeapon(item_cfg.sub_type) then
		rich_part_text = HiddenWeaponWGData.Instance:GetEquipPartStr(item_cfg.id)
	else
		local sex_str = Language.Common.SexName[item_cfg.limit_sex] or ""
		local order = item_cfg.order or ""
		local Stone = Language.Stone[equip_part] or ""
		local prefix_text = ""
		if EquipBodyWGData.Instance:GetEquipIsSpecailOrder(item_cfg.order) then
			prefix_text = EquipBodyWGData.Instance:GetEquipSpecailOrderSignStr(item_cfg.order)
			--prefix_text = Language.Equip.HighOrderPrefix[item_cfg.order]
		else
			prefix_text = string.format(Language.Tip.Order, item_cfg.order)
		end

		rich_part_text = prefix_text .. sex_str .. Stone
	end

	local mix_limit_prof = EquipWGData.GetEquipProfLimit(equip_part, item_cfg.order)
	local limit_sex = item_cfg.limit_sex or 0
	local rich_type_text = Language.Common.ProfName[limit_sex][item_cfg.limit_prof]
	local is_limit_zhuanzhi_prof = item_cfg.is_limit_zhuanzhi_prof and tonumber(item_cfg.is_limit_zhuanzhi_prof) or 0
	local zhuan_str
	if item_cfg.is_zhizun == 1 then
		rich_type_text = Language.Common.ProfName[limit_sex][item_cfg.limit_prof]
	elseif mix_limit_prof > 0 and item_cfg.limit_prof ~= 5 and is_limit_zhuanzhi_prof == 1 then
		zhuan_str = string.format(Language.F2Tip.Zhuan, CommonDataManager.GetDaXie(mix_limit_prof))
		rich_type_text = Language.Common.ProfName[limit_sex][mix_limit_prof * 10 + item_cfg.limit_prof] .. zhuan_str
	elseif mix_limit_prof > 0 and item_cfg.limit_prof == 5 and is_limit_zhuanzhi_prof == 1 then
		zhuan_str = string.format(Language.F2Tip.Zhuan, CommonDataManager.GetDaXie(mix_limit_prof))
		rich_type_text = rich_type_text .. zhuan_str
	end

	local sex_color = TIPS_COLOR.SOCRE
	local prof_color = TIPS_COLOR.SOCRE
	if self.from_view ~= ItemTip.FROME_BROWSE_ROLE and self.from_view ~= ItemTip.FORM_ROBOT_SHOW_INFO then
		local zhuanzhi_num = RoleWGData.Instance:GetZhuanZhiNumber()
		local sex_limit = item_cfg.limit_sex ~= sex and item_cfg.limit_sex ~= GameEnum.SEX_NOLIMIT
		local prof_limit = item_cfg.limit_prof ~= prof and item_cfg.limit_prof ~= GameEnum.ROLE_PROF_NOLIMIT
		local prof_level_limit = zhuanzhi_num < mix_limit_prof
		sex_color = sex_limit and COLOR3B.D_RED or sex_color
		prof_color = (prof_limit or (prof_level_limit and is_limit_zhuanzhi_prof == 1)) and COLOR3B.D_RED or prof_color
	end

	local desc = string.format(Language.Tip.ZhuangBeiLeiXing_1, sex_color, rich_part_text)
	local rich_type_text = string.format(Language.Tip.ZhuangBeiProf, prof_color, rich_type_text)
	return desc, rich_type_text
end

-- function DisplayItemTip:Update(now_time, elapse_time)
-- 	if not self.is_foot_view then
-- 		return
-- 	end
-- 	if self.next_create_footprint_time == 0 then
--         self:CreateFootPrint()
--         self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
--     end
--     if self.next_create_footprint_time == nil then --初生时也是位置改变，不播
--         self.next_create_footprint_time = 0
--     end
--     if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
--         self.next_create_footprint_time = 0
--     end
--     self:UpdateFootprintPos()
-- end

-- function DisplayItemTip:CreateFootPrint()
-- 	if nil == self.foot_effect_id then
-- 		return
-- 	end
-- 	if nil == self.footprint_eff_t then
-- 		self.footprint_eff_t = {}
-- 	end
--     local pos = self.model_display.draw_obj:GetRoot().transform
--     local bundle, asset = ResPath.GetUIFootEffect(self.foot_effect_id)
-- 	EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.position.x , pos.position.y, pos.position.z), nil, pos, nil, function(obj)
-- 		if obj then
-- 			if nil ~= obj then
-- 				if self.model_display then
-- 					obj.transform.localPosition = Vector3.zero
-- 					obj:SetActive(false)
-- 					obj:SetActive(true)
-- 					table.insert(self.footprint_eff_t, {obj = obj, role_model = self.model_display})
-- 					self.model_display:OnAddGameobject(obj)
-- 				else
-- 					ResPoolMgr:Release(obj)
-- 				end
-- 			end
-- 		end
--    	end)
-- 	if #self.footprint_eff_t > 2 then
-- 		local obj = table.remove(self.footprint_eff_t, 1)
-- 		obj.role_model:OnRemoveGameObject(obj.obj)
-- 		if not IsNil(obj.obj) then
-- 			obj.obj:SetActive(false)
-- 		end
-- 	end
-- end

-- function DisplayItemTip:UpdateFootprintPos()
-- 	if nil == self.footprint_eff_t then
-- 		return
-- 	end
-- 	for k,v in pairs(self.footprint_eff_t) do
-- 		if not IsNil(v.obj) then
-- 			local pos = v.obj.transform.localPosition
-- 			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 1.5)
-- 		end
-- 	end
-- end

-- function DisplayItemTip:ClearFootEff()
-- 	if self.footprint_eff_t ~= nil then
-- 		for k,v in pairs(self.footprint_eff_t) do
-- 			if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
-- 				-- ResPoolMgr:Release(v.obj)
-- 				v.role_model:OnRemoveGameObject(v.obj)
-- 				v.obj:SetActive(false)
-- 			end
-- 		end
-- 	end

-- 	self.footprint_eff_t = {}
-- end

function DisplayItemTip:FlushMechaModel(item_cfg)
	local item_id = item_cfg.id
	local mecha_seq = -1
	local mecha_part_cfg = {}
	local is_mecha_act_item = MechaWGData.Instance:IsMechaActivePartItem(item_id)
	
	if is_mecha_act_item then
		local mecha_cfg = MechaWGData.Instance:GetMechaActiveItemCfgByItemId(item_id)
		if not IsEmptyTable(mecha_cfg) then
			mecha_seq = mecha_cfg.seq
		end
	end

	local is_mecha_part_item = MechaWGData.Instance:IsMechaPartItem(item_id)
	if is_mecha_part_item then
		mecha_part_cfg = MechaWGData.Instance:GetMechaPartCfgByItemId(item_cfg.id)

		if not IsEmptyTable(mecha_part_cfg) then
			mecha_seq = mecha_part_cfg.mechan_seq
		end
	end

	if mecha_seq >= 0 then
		local part_list = {}
		local base_part = MechaWGData.Instance:GetMechaBasePartListByMechaSeq(mecha_seq)
		for k, v in pairs(base_part) do
			local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
			part_list[part_cfg.part] = part_cfg.res_id
		end

		if not IsEmptyTable(mecha_part_cfg) then
			part_list[mecha_part_cfg.part] = mecha_part_cfg.res_id
		end

		local part_info = {
			gundam_seq = mecha_seq,
			gundam_body_res = part_list[MECHA_PART_TYPE.BODY] or 0,
			gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON] or 0,
			gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND] or 0,
			gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND] or 0,
			gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT] or 0,
			gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
			gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING] or 0,
			gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING] or 0,
		}
	
		self.model_display:SetGundamModel(part_info)
		-- self.model_display:SetGundamSingleWeaponResId(mecha_seq, mecha_part_cfg.res_id)
	end
end

function DisplayItemTip:SetMechaUseBtn(is_show)
	if not is_show then
		return
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	
	if num > 0 then
		local btn_info = {btn_name = Language.Tip.ButtonLabel[2], btn_click = BindTool.Bind(self.OnClickMechaUse, self)}
		self.base_tips:AddBtnsClick(btn_info)
	end
end

function DisplayItemTip:OnClickMechaUse()
	local mecha_item_cfg = MechaWGData.Instance:GetMechaActiveItemCfgByItemId(self.data.item_id)
		
	if not IsEmptyTable(mecha_item_cfg) then
		MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.USE_ACTIVE_ITEM, mecha_item_cfg.seq, 1)
	end
	self:Close()
	ViewManager.Instance:Open(GuideModuleName.MechaView)
end

-- 特殊套装展示
function DisplayItemTip:ShowSuitSpecialPanel()
	self.node_list.suit_special_panel:SetActive(false)
	self.node_list.rawimage_special_bg:SetActive(false)
	self.node_list.btn_close_window_suit:SetActive(false)
	local base_tip_root_y = self.node_list.base_tip_root.rect.anchoredPosition.y
	RectTransform.SetAnchoredPositionXY(self.node_list.base_tip_root.rect, 0, base_tip_root_y)
	--self.base_tips:SetornamentPanelBgPos(false)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.base_tips:SetLeftShowModelMiddle(false)
	if not item_cfg or not item_cfg.grade_tips_type or item_cfg.grade_tips_type == "" or item_cfg.grade_tips_type < 1 then
		return
	end

	self.base_tips:SetLeftShowModelMiddle(true)
	self.node_list.btn_close_window_suit:SetActive(true)
	if self.get_way_view_is_on then
		RectTransform.SetAnchoredPositionXY(self.node_list.rawimage_special_bg.rect, -135, self.node_list.rawimage_special_bg.rect.anchoredPosition.y)
		RectTransform.SetAnchoredPositionXY(self.node_list.suit_special_panel.rect, -314, self.node_list.suit_special_panel.rect.anchoredPosition.y)
		RectTransform.SetAnchoredPositionXY(self.node_list.btn_close_window_suit.rect, 410, self.node_list.btn_close_window_suit.rect.anchoredPosition.y)
		RectTransform.SetAnchoredPositionXY(self.node_list.base_tip_root.rect, 81, base_tip_root_y)
	else
		RectTransform.SetAnchoredPositionXY(self.node_list.rawimage_special_bg.rect, 0, self.node_list.rawimage_special_bg.rect.anchoredPosition.y)
		RectTransform.SetAnchoredPositionXY(self.node_list.suit_special_panel.rect, -179, self.node_list.suit_special_panel.rect.anchoredPosition.y)
		RectTransform.SetAnchoredPositionXY(self.node_list.btn_close_window_suit.rect, 543, self.node_list.btn_close_window_suit.rect.anchoredPosition.y)
		RectTransform.SetAnchoredPositionXY(self.node_list.base_tip_root.rect, 65, base_tip_root_y)
	end

	self.base_tips:SetOrnamentImage(item_cfg.color, 1)
	self.node_list.suit_special_panel:SetActive(true)
	self.node_list.rawimage_special_bg:SetActive(true)
	local show_ids = ItemWGData.Instance:GetGradeTipsTypeData(item_cfg.grade_tips_type)
	local list_data = {}
	for k_1, v_1 in ipairs(show_ids) do
		local data = {}
		data.item_id = v_1
		data.is_self = v_1 == self.data.item_id and 1 or 0
		table.insert(list_data, data)
	end
	table.sort(list_data, function (a, b)
		return a.item_id < b.item_id
	end)

	local init_index = 1
	for k_2, v_2 in ipairs(list_data) do
		if v_2.is_self == 1 then
			init_index = k_2
			break
		end
	end

	local into_flag = false
	if not self.suit_item_list then
		self.suit_item_list = AsyncListView.New(ItemCellSuitTipRoot, self.node_list.suit_item_list)
		self.suit_item_list:SetSelectCallBack(BindTool.Bind(self.OnSelectSuitRender, self))
		self.suit_item_list:SetDefaultSelectIndex(init_index)
		into_flag = true
	end

	self.suit_item_list:SetDataList(list_data)
	self.suit_item_list:JumpToIndex(init_index)
end

function DisplayItemTip:OnSelectSuitRender(item, cell_index)
	if not item or not item:GetData() then
		return
	end

	local data = item:GetData()
	if self.data.item_id == data.item_id then
		return
	end

	self.data.item_id = data.item_id
	self:FlushView()
	self:Flush()
	self:FlushGetWayView()
	self:FlushQuickBuyPanel()
end

ItemCellSuitTipRoot = ItemCellSuitTipRoot or BaseClass(BaseRender)

function ItemCellSuitTipRoot:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_parent)
	end
end

function ItemCellSuitTipRoot:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ItemCellSuitTipRoot:OnFlush()
	if not self.data then
		return
	end

	local item_d = {}
	item_d.item_id = self.data.item_id
	self.item_cell:SetData(item_d)
end

function ItemCellSuitTipRoot:OnSelectChange(is_select)
	self.node_list.image_slt:SetActive(is_select)
end

