TipsSystemView = TipsSystemView or BaseClass(BaseRender)

function TipsSystemView:__init()
	self.messge = ""
	self.anim_speed = 1
	self.convertion = -1
	self.index = 0

	self.system_tips = self.node_list["SystemTips2"]
	self.system_tips2 = self.node_list["SystemTips1"]
	self.system_tips3 = self.node_list["SystemTips3"]

	self.anim = self.system_tips.animator
	self.canvas_group = self.system_tips.canvas_group
	self.begin_pos = self.system_tips.transform.localPosition
	self.has_finished = false
end

function TipsSystemView:__delete()
	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end

	if self.tween then
		self.tween:Kill()
	end
end

function TipsSystemView:SystemTipsVis()
	return self.canvas_group and self.canvas_group.alpha > 0
end

function TipsSystemView:Show(msg, speed, index)
	self:SetActive(true)
	self.has_finished = false

	if self.begin_pos then
		self.system_tips.transform.localPosition = self.begin_pos
	end

	self.index = index or 0
	self.convertion = -1
	self.area_tips_con = 0
	speed = speed or 1
	self.anim_speed = speed
	self.convertion = nil

	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end

	self.close_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CloseTips, self), 5)
	self.messge = msg

	self.system_tips:SetActive(true)
	self.system_tips2:SetActive(false)
	self.system_tips3:SetActive(false)
	self.node_list["SystemTips1"]:SetActive(false)
	self.node_list["SystemTips3"]:SetActive(false)

	self:FlushView()
end

-- convertion 对应 SceneConvertionArea  正常的都走Show()  Show2()处理进入/离开安全区
function TipsSystemView:Show2(convertion, msg, speed)
	self:SetActive(true)
	self.has_finished = false
	self.convertion = convertion
	speed = speed or 1
	self.anim_speed = speed

	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end
	self.close_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CloseTips, self), 5)

	self.messge = msg

	self.node_list["SystemTips2"]:SetActive(false)
	if self.convertion == 1 then
		self.node_list["SystemTips1"]:SetActive(true)
		self.node_list["SystemTips3"]:SetActive(false)
	else
		self.node_list["SystemTips1"]:SetActive(false)
		self.node_list["SystemTips3"]:SetActive(true)
	end

	self:FlushView()
end

function TipsSystemView:ChangeSpeed(speed)
	self.anim_speed = speed
	self:DoMove()
end

function TipsSystemView:AddIndex()
	self.index = self.index + 1
end

function TipsSystemView:DoMove()
	if nil == self.begin_pos or nil == self.system_tips then
		return
	end

	if self.tween then
		self.tween:Kill()
	end

	self.system_tips.transform.localPosition = Vector3(self.begin_pos.x, self.begin_pos.y + 35 * self.index, self.begin_pos.z)
	self.tween = self.system_tips.transform:DOLocalMoveY(self.begin_pos.y + 35 * (self.index + 1), 1 / self.anim_speed)
	self.tween:SetEase(DG.Tweening.Ease.Linear)
end

function TipsSystemView:CloseTips()
	self.has_finished = true
	self:SetActive(false)

	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end

	if self.tween then
		self.tween:Kill()
	end

	if self.system_tips and self.begin_pos then
		self.system_tips.transform.localPosition = self.begin_pos
	end
end

function TipsSystemView:FlushView(param_list)
	self:DoMove()

	self.node_list["system_text2"].text.text = self.messge
	if self.convertion == 0 then
		self.node_list["system_text3"].text.text = self.messge
	elseif self.convertion == 1 then
		self.node_list["system_text1"].text.text = self.messge
	end
end

function TipsSystemView:GetAnimSpeed()
	return self.anim_speed
end

function TipsSystemView:IsOpen()
	return not self.has_finished
end