GuildBossTipsView = GuildBossTipsView or BaseClass(SafeBaseView)

function GuildBossTipsView:__init()
	self:AddViewResource(0, "uis/view/guild_boss_prefab", "layout_guild_boss_tips")
	self.active_close = false
	
end

function GuildBossTipsView:OpenCallBack()
	self:Flush()
end

function GuildBossTipsView:__delete()

end

function GuildBossTipsView:LoadCallBack()
	
end

function GuildBossTipsView:OnFlush()
	local boss_info = GuildBossWGData.Instance:GetGuildBossHurtWarnInfo()
	if boss_info then
		local is_wudi = boss_info.boss_state == 1 
		self.node_list["boss_hp_tips"]:SetActive(is_wudi)
	end
end