local BOSS_REBIRTH_REMIND_INTERVAL = 30  -- s

WorldBossSceneLogic = WorldBossSceneLogic or BaseClass(CommonFbLogic)

function WorldBossSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.last_select_monster = nil
	self.select_monster_event = nil

	self.create_obj_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
end

function WorldBossSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
	self.has_get_team_target = nil
	self.last_select_monster = nil

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
	end

	if self.select_monster_event then
		GlobalEventSystem:UnBind(self.select_monster_event)
		self.select_monster_event = nil
	end
end

function WorldBossSceneLogic:Enter(old_scene_type, new_scene_type)
	self.has_get_team_target = -1
	BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.ALLINFO)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	BossWGCtrl.Instance:Close()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		-- MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		MainuiWGCtrl.Instance:SetFBNameState(false)
		--MainuiWGCtrl.Instance:SetTeamBtnState(false)
		-- MainuiWGCtrl.Instance:SetTaskButtonTrue()
		GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE, true)
		BossWGCtrl.Instance:EnterSceneCallback()
		BossWGData.Instance:SetBossEnterFlag(true)
	end)
	
	self.check_tire = nil
    BaseFbLogic.SetLeaveFbTip(true)
    if BossWGData.Instance:IsInWorldBossTiredState() then --如果处于疲劳状态，模拟发一下
        local info = BossWGData.Instance:GetWorldBossRoleRealiveInfo()
        BossWGCtrl.Instance:OnSCWorldBossRoleRealiveInfo(info)
    end

    FuhuoWGCtrl.Instance:SetFuhuoMustCallback(BindTool.Bind(self.FuHuoCallBack, self))
    self.select_monster_event = GlobalEventSystem:Bind(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObjCallBack, self))
	self.main_role_move_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.OnMainRoleMove, self))
	self.refresh_boss_list = GlobalEventSystem:Bind(OtherEventType.BOSS_CHANGE, BindTool.Bind(self.CheckUseBossReflushCard, self))


	BossPrivilegeWGCtrl.Instance:EnterBossPrivilegeSceneCallBack()
	self.boss_rebirth_boss_cfg = {}
	self.last_remind_time = 0
end

function WorldBossSceneLogic:OnSelectObjCallBack(obj)
	if obj ~= nil and not obj:IsDeleted() and obj:IsBoss() then
		self.last_select_monster = obj:GetMonsterId()
	end
end

function WorldBossSceneLogic:FuHuoCallBack(use_type)
	local tarck_type, track_role_uuid = self:GetTrackRoleInfo()
	if tarck_type == OBJ_FOLLOW_TYPE.TEAM then
		return
	end
	
	use_type = use_type or FuHuoType.Common
	if use_type == FuHuoType.Common then
		self:CommonMoveCallBack()
	else
		self:HereFuHuoCallBack()
	end
end

function WorldBossSceneLogic:CommonMoveCallBack()
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)

	if self.last_select_monster ~= nil and self.last_select_monster ~= 0 then
		if self.last_select_monster == GameEnum.TeamInvite then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			return
		end

		local scene_id = Scene.Instance:GetSceneId()
		local data = BossWGData.Instance:GetWorldBossPos(self.last_select_monster)
		if data ~= nil then
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)

			MoveCache.end_type = MoveEndType.FightByMonsterId
			GuajiCache.monster_id = self.last_select_monster
			local range = BossWGData.Instance:GetMonsterRangeByid(self.last_select_monster)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, data.x_pos, data.y_pos, range)
		else
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	else
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

function WorldBossSceneLogic:HereFuHuoCallBack()
	local select_obj = nil
	if GuajiCache.target_obj then
		select_obj = GuajiCache.target_obj
	end

	if select_obj and not select_obj:IsDeleted() and Scene.Instance:IsEnemy(select_obj) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiCache.target_obj = select_obj
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		self:CommonMoveCallBack()
	end

end

function WorldBossSceneLogic:OnObjCreate(obj)
	if obj and not SceneObj.select_obj and self:IsEnemy(obj) and GuajiCache.guaji_type ~= GuajiType.None then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
	end
	
	if obj and obj:GetType() == SceneObjType.FallItem then
		local cur_zhu_task_cfg = TaskWGData.Instance:GetTaskTypeZhuCfg()
		if cur_zhu_task_cfg and cur_zhu_task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_77 then
        	Scene.ScenePickItem({obj:GetObjId()})
		end
	end
end

--是否能改变模式
function WorldBossSceneLogic:CanChangeAttackMode()
    if BossWGData.Instance:GetWorldBossSceneIdByIndex(1) == Scene.Instance:GetSceneId() then
        return false
    end
    return true
end

function WorldBossSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	if self:RolePickUpFallItem() then
		return true
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
		local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
		target_obj = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
		if target_obj ~= nil and self:IsRoleEnemy(target_obj, main_role) then
			MainuiWGCtrl.Instance:SetTargetObj(target_obj)
		end
	end

	local tire_value, max_tire_value = BossWGData.Instance:GetWorldBossTire()
	local target_obj = MainuiWGData.Instance:GetTargetObj()

	local mosnter_type = nil
	if target_obj ~= nil and target_obj:GetType() == SceneObjType.Monster then
		mosnter_type = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[target_obj:GetVo().monster_id].type
	end
	local search_boss = true
	if tire_value and tire_value >= max_tire_value and (target_obj ~= nil and mosnter_type == MONSTER_TYPE.BOSS) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OutTire)
		Scene.Instance:ClearAllOperate()
		MainuiWGCtrl.Instance:SetTargetObj(nil)
		search_boss = false
	end

	target_obj = MainuiWGData.Instance:GetTargetObj()
	if (tire_value < max_tire_value or ((target_obj == nil and search_boss) or (target_obj ~= nil and mosnter_type ~= MONSTER_TYPE.BOSS))) then
		BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
		return
	end
end

-- 获取挂机打怪的位置
function WorldBossSceneLogic:GetGuiJiMonsterPos()
	MainuiWGCtrl.Instance:ResetLightBoss()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = nil
    local target_y = nil
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end

-- 是否是挂机打怪的敌人
function WorldBossSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

function WorldBossSceneLogic:Out(old_scene_type, new_scene_type)
	self.last_select_monster = nil
	self.has_get_team_target = -1
	CommonFbLogic.Out(self)
	BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.ALLINFO)
	BossWGCtrl.Instance:OutSceneCallback(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    self.check_tire = nil
    MainuiWGCtrl.Instance:ResetTaskPanel()
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
	--BossWGCtrl.Instance:CloseSceneLogicView()
	FuhuoWGCtrl.Instance:ClearFuhuoMustCallback()
	FuBenWGCtrl.Instance:CloseTeamExpCheerView()
	if BossWGData.Instance:GetIsEnterInScene() then
		BossWGCtrl.Instance:OpenBossViewByScene(old_scene_type, new_scene_type)
	end

	if self.main_role_move_event then
		GlobalEventSystem:UnBind(self.main_role_move_event)
		self.main_role_move_event = nil
   	end

   if self.refresh_boss_list then
		GlobalEventSystem:UnBind(self.refresh_boss_list)
		self.refresh_boss_list = nil
	end

	BossPrivilegeWGCtrl.Instance:OutBossPrivilegeSceneCallBack()

	self.boss_rebirth_boss_cfg = nil
	self.last_remind_time = nil

	if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function WorldBossSceneLogic:OpenFbSceneCd()

end

-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function WorldBossSceneLogic:GetGuajiCharacter()
	local is_need_stop = false

	local target_obj = self:GetMonster()
	if target_obj ~= nil then
		is_need_stop = true
		return target_obj, nil, is_need_stop
	end

	if target_obj == nil then
		target_obj, is_need_stop = self:GetNormalRole()
		return target_obj, nil, is_need_stop
	end
end

function WorldBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local info = self:GetGuaJiInfo()
	local is_stop = info ~= nil

	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if info ~= nil then
				if not v:IsDeleted() then
					local pos_x, pos_y = v:GetLogicPos()
					local dis = GameMath.GetDistance(info.x, info.y, pos_x, pos_y, false)
					if dis <= info.aoi_range * info.aoi_range then
						GuajiCache.target_obj = v
						return v, is_stop
					end
				end
			else
				GuajiCache.target_obj = v
				return v, is_stop
			end
		end
	end

	return nil, is_stop
end

function WorldBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()

		local info = self:GetGuaJiInfo()
		if info ~= nil then
			distance_limit = info.aoi_range * info.aoi_range
			x = info.x
			y = info.y
		end	

		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function WorldBossSceneLogic:GetNextSelectTargetId()
	-- 切换目标时，挂机模式需要变成自动
	if GuajiCache.guaji_type == GuajiType.Monster then
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
	return BaseSceneLogic.GetNextSelectTargetId(self)
end

function WorldBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function WorldBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

-- function WorldBossSceneLogic:AtkTeamLeaderTarget()
-- 	local team_leader_info = SocietyWGData.Instance:GetTeamLeader() or {}
-- 	local leader = Scene.Instance:GetRoleByRoleId(team_leader_info.role_id)
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

-- 	if not leader then
-- 		return
-- 	end

-- 	self.has_get_team_target = leader:GetVo() and leader:GetVo().obj_id or -1

-- 	if not leader:IsAtkPlaying() then
-- 		return
-- 	end

-- 	local target_obj
-- 	if leader:IsAtkPlaying() then
-- 		target_obj = leader:GetAttackTarget()
-- 		if not target_obj then
-- 			return
-- 		end
-- 	else
-- 		self.has_get_team_target = -1
-- 		return
-- 	end

-- 	if self:IsEnemy(target_obj) then
-- 		self.has_get_team_target = -1
-- 		GuajiWGCtrl.Instance:StopGuaji()
-- 		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
-- 	end
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
-- end

function WorldBossSceneLogic:GetTeamTargetFlag()
	return self.has_get_team_target or -1
end

-- 此场景优先保证单位数量
function WorldBossSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function WorldBossSceneLogic:IsEnemyVisiblePriortiy()
	return true
end

function WorldBossSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	self:CheckGuaJiPosMove()
end

-- 主角移动事件
function WorldBossSceneLogic:OnMainRoleMove()
	-- self:CheckUseBossReflushCard()
end

function WorldBossSceneLogic:CheckUseBossReflushCard()
	-- 是否今日不再提醒
	-- if self:TodayIgnoreBossReflushRemind() then
	-- 	return
	-- end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local tire_value, max_tire_value = BossWGData.Instance:GetWorldBossTire()
	if tire_value == nil then
		return
	end

	if tire_value >= max_tire_value then
		return
	end

	local boss_list = self:GetFbSceneMonsterBossCfg()
	if IsEmptyTable(boss_list) then
		return
	end

	local pos_x, pos_y = main_role:GetLogicPos()
	local target_distance = 1000 * 1000
	local boss_rebirth_cfg = MainuiWGData.Instance:GetBossRebirthCfg()
	local rebirth_range = boss_rebirth_cfg.rebirth_range
	local rebirth_dis = rebirth_range * rebirth_range
	local boss_cfg

	for k, v in pairs(boss_list) do
		local dis = GameMath.GetDistance(v.x, v.y, pos_x, pos_y, false)
		if dis < rebirth_dis and dis < target_distance then
			boss_cfg = v
			target_distance = dis
		end
	end

	if boss_cfg then
		local boss_state = BossWGData.Instance:GetBossStatusByBossId(boss_cfg.boss_id)
		if boss_state == 0 then
			local server_time = TimeWGCtrl.Instance:GetServerTime()

			local need_remind = false
			if IsEmptyTable(self.boss_rebirth_boss_cfg) then
				need_remind = true
			else
				local cache_boss_cfg = self.boss_rebirth_boss_cfg
				local is_same_boss_cfg = cache_boss_cfg.boss_id == boss_cfg.boss_id and cache_boss_cfg.x == boss_cfg.x and cache_boss_cfg.y == boss_cfg.y
				if not is_same_boss_cfg or (is_same_boss_cfg and (self.last_remind_time + BOSS_REBIRTH_REMIND_INTERVAL) < server_time) then
					need_remind = true
				end
			end

			if need_remind then
				self.boss_rebirth_boss_cfg = boss_cfg
				local rebirth_item, has_num = MainuiWGData.Instance:GetBossRefreshItemIdStuff()
				if rebirth_item then
					if self:TodayIgnoreBossReflushRemind() then
						BossWGCtrl.Instance:DoOperaBossRefresh(rebirth_item)
					else
						if not self:TodayNoTips() and BossWGData.Instance:IsBossQuickRebirthNotCountDown() then
							BossWGCtrl.Instance:OpenBossQuickRebirthShow()
						end
					end
				end
			end
		else
			self.boss_rebirth_boss_cfg = {}
		end
	end
end

function WorldBossSceneLogic:TodayIgnoreBossReflushRemind()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local rebirth_reflush_status = PlayerPrefsUtil.GetInt("boss_quick_rebirth_reflush" .. main_role_id)
	return rebirth_reflush_status == 1
end

function WorldBossSceneLogic:TodayNoTips()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local has_today_flag = PlayerPrefsUtil.GetInt("boss_quick_rebirth_tips" .. main_role_id) == open_day
	return has_today_flag
end