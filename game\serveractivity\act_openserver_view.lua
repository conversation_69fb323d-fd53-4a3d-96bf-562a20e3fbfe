--开服活动
ActOpenServerView = ActOpenServerView or BaseClass(SafeBaseView)

function ActOpenServerView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/serveractivity_ui_prefab", "layout_open_activity_panel2")
	self.sub_view_list = {}
	self.btns_change_flag = false
	self.select_index = 1
	self.data_list_classify = {}
	self.classify = 1
end

function ActOpenServerView:__delete()

end

function ActOpenServerView:ReleaseCallBack()
	if nil ~= self.sub_view_list then
		for k, v in pairs(self.sub_view_list) do
			v:DeleteMe()
		end
	end

	if self.btn_select_list then
		self.btn_select_list:DeleteMe()
		self.btn_select_list = nil
	end
	self.close_view = nil
	self.data_list_classify = {}
	self.sub_view_list = {}
	self.cur_subview = nil
	self.select_index = 1
	self.user_select_index = nil
end
--自定义按钮索引
function ActOpenServerView:OnClickBntIndex(index)
	self.user_select_index = index
end


function ActOpenServerView:LoadCallBack()
	if ActivityWGCtrl.Instance:IsOpenServerOpen() then
		self:CreateOpenServerBtnList()
		-- self.btn_select_list:SelectIndex(self.select_index)
	end
	-- if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.COMBINE_SERVER) then
	-- 	RankWGCtrl.Instance:SendRankListReq(RankClientType.PERSON_RANK_TYPE_CSA_RECHARGE_TOTAL) --请求 充值排行榜信息
	-- end
	XUI.AddClickEventListener(self.node_list.btn_close_window,BindTool.Bind(self.Close,self))
end

function ActOpenServerView:SelectHandler(index)
	self.select_index = index
end

function ActOpenServerView:CreateOpenServerBtnList()
	if self.node_list.ph_btn_select_list == nil then return end
	self:FlushDataList()
	if not self.btn_select_list then
		self.btn_select_list = AsyncListView.New(VersionBtnOpenItem,self.node_list.ph_btn_select_list)
		self.btn_select_list:SetSelectCallBack(BindTool.Bind1(self.OnOpenViewType, self))
		self.btn_select_list.SelectIndex = function(btn_select_list, index)
			local item_cell = btn_select_list:GetItemAt(index)
			if item_cell ~= nil and item_cell:GetReason() ~= nil then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.OpenServer.NextOpenSprintActName, item_cell:GetReason()))
				return
			end
			AsyncListView.SelectIndex(btn_select_list, index)
		end
	end

 	self.btn_select_list:SetDefaultSelectIndex(self.select_index)
	self.btn_select_list:SetDataList(self.data_list_classify)    --刷新列表

end

function ActOpenServerView:FlushDataList()
	local open_act_cfg = ServerActivityWGData.Instance:GetShowOpenActList(true, true)
	local classify_count = #open_act_cfg
	local k = 0
	self.data_list_classify = {}
	for i=1,classify_count do
		if open_act_cfg[i].classify == self.classify then
			k = k + 1
			self.data_list_classify[k] = open_act_cfg[i]
		end
	end
	table.insert(self.data_list_classify,1,self.data_list_classify[#self.data_list_classify])
	self.data_list_classify[#self.data_list_classify] = nil
end
function ActOpenServerView:UpdateBtnList()
	if not self:IsOpen() then return end
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()   -- 开服天数
	if open_day > 7 and not self.close_view then
		self.close_view = true
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.RollActivityEnd)
		self:Close()
		return
	end
	self:FlushDataList()
 	self.btn_select_list:SetDefaultSelectIndex(self.select_index)
	self.btn_select_list:SetDataList(self.data_list_classify,3)    --刷新列表
end

function ActOpenServerView:OpenCallBack()
	if next(self.data_list_classify) ~= nil and self.btn_select_list then
	 	self.btn_select_list:SetDefaultSelectIndex(1)
		self.btn_select_list:SetDataList(self.data_list_classify)    --刷新列表
		return
	end
	if self.cur_subview then
		self.cur_subview:Open()
	end
end

function ActOpenServerView:ShowIndexCallBack(index)

	ServerActivityWGCtrl.Instance:SendOpenGameActivityInfoReq()
	ServerActivityWGCtrl.Instance:SendRollMoneyOperaReq(RollMoneyEnum.ROLL_MONEY_OPERA_TYPE_QUERY)

	self:RefreshView()
end

function ActOpenServerView:CloseCallBack()
	ServerActivityWGData.Instance:CleanUpFinishAct()
	ServerActivityWGData.Instance:CleanActCloseType()
	self.btns_change_flag = true
	if self.cur_subview then
		self.cur_subview:Close()
	end
end

function ActOpenServerView:OnOpenViewType(item, index)
	if nil == self.btn_select_list or nil == item then
		return
	end

	ServerActivityWGCtrl.Instance:SendOpenGameActivityInfoReq()    --请求活动信息
	local item = self.btn_select_list:GetItemAt(self.btn_select_list:GetSelectIndex())
	if item == nil then return end
	local data = item:GetData()

	local open_act_cfg = data

	local act_id = open_act_cfg.id
	local view_type = open_act_cfg.view_type

	if self.cur_subview ~= self.sub_view_list[act_id] then
		self.cur_subview:Close()
	end
	self.select_index = index
	self.sub_view_list[act_id] = self.sub_view_list[act_id] or ActivitySubViewCfg[view_type].New(act_id)
	self.cur_subview = self.sub_view_list[act_id]
	self.cur_subview:Open()
	self.cur_subview:RefreshView({flush_param = "change_index"})

end

function ActOpenServerView:RefreshView(param_t)
	if nil ~= self.cur_subview then
		self.cur_subview:RefreshView(param_t)
	end
	if self.btn_select_list and self.btn_select_list.list_view then
		self.btn_select_list:RefreshActiveCellViews()
	end
end

function ActOpenServerView:GetCurView()
	return self.cur_subview
end
