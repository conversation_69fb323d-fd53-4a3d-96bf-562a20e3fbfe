ShenJiTianCiRecordView = ShenJiTianCiRecordView or BaseClass(SafeBaseView)

function ShenJiTianCiRecordView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(1098, 590)})
    self:AddViewResource(0, "uis/view/shenji_tianci_ui_prefab", "layout_shenji_tianci_record")
end

function ShenJiTianCiRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function ShenJiTianCiRecordView:LoadCallBack()
    self.toggle_index = 1

    self.node_list.txt_tip2.text.text = Language.SiXiangCall.RecordTip

    self.node_list.btn_all.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, SiXiangRecordType.ALL))
    self.node_list.btn_self.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, SiXiangRecordType.SELF))

    self.record_list = AsyncListView.New(ShenJiTianCiRecordRender, self.node_list["role_list"])
end

function ShenJiTianCiRecordView:ShowIndexCallBack()
    self.node_list["btn_all"].toggle.isOn = true
end

function ShenJiTianCiRecordView:OnClickSwitch(state)
    self.toggle_index = state
    if state == SiXiangRecordType.SELF then
        SiXiangCallWGCtrl.Instance:SendMachineOp(MACHINE_OP_TYPE.INFO)
    end
    self:FlushRecordList()
end

function ShenJiTianCiRecordView:OnFlush(param_t)
    self:FlushRecordList()
    self:FlushTitle()
end

function ShenJiTianCiRecordView:FlushRecordList()
    local data_list = SiXiangCallWGData.Instance:GetShenJiTianCiRecordData(self.toggle_index)
    local is_show_list = not IsEmptyTable(data_list)
    if is_show_list then
        self.record_list:SetDataList(data_list)
    end
    self.node_list["role_list"]:SetActive(is_show_list)
    self.node_list["no_invite"]:SetActive(not is_show_list)
end

function ShenJiTianCiRecordView:FlushTitle()
    self.node_list.title_view_name.text.text = Language.SiXiangCall.XQZLRecordTitle
end

-------------------------------------------------------------------------------------------------------

ShenJiTianCiRecordRender = ShenJiTianCiRecordRender or BaseClass(BaseRender)

function ShenJiTianCiRecordRender:LoadCallBack()
    self.node_list["txt_btn"].button:AddClickListener(BindTool.Bind(self.OnclickItem, self))
end

function ShenJiTianCiRecordRender:OnFlush()
    local data = self:GetData()
    local index = self:GetIndex()

    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not item_cfg then
        return
    end

    -- local color = mark and ITEM_COLOR[item_cfg.color] or ITEM_COLOR[item_cfg.color]
    local color = ITEM_COLOR[item_cfg.color]
    local role_name = data.role_name or RoleWGData.Instance:GetAttr("name")
    local str1 = string.format(Language.SiXiangCall.TxtRecord3, role_name)
    local name = string.format(Language.SiXiangCall.TxtRecord1_2, color, item_cfg.name)
    local num = string.format(Language.SiXiangCall.TxtRecord1_3, color, data.num)

    self.node_list["desc"].text.text = str1
    self.node_list["txt_btn"].text.text = name
    self.node_list["num"].text.text = num
    self.node_list["time"].text.text = os.date("%m-%d  %X", data.draw_time)
end

function ShenJiTianCiRecordRender:OnclickItem()
    local data = self:GetData()
    if data == nil then
        return
    end

    TipWGCtrl.Instance:OpenItem({item_id = data.item_id})
end