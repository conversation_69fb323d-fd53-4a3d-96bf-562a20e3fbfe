NewFestivalCollectCardInfoTip = NewFestivalCollectCardInfoTip or BaseClass(SafeBaseView)

local PanelType = {
    Friend = 1,
    Request = 2,
}

local PlayerNameColor = "#FFFFFF"
local RequestItemColor = "#FFFFFF"

function NewFestivalCollectCardInfoTip:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/new_festival_activity_ui_prefab", "new_fes_jfhd_info_tip")
end

function NewFestivalCollectCardInfoTip:OpenCallBack()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.NEW_JRHD_JFHD, OA_COLLECT_CARD_OP_TYPE.INFO)
end

function NewFestivalCollectCardInfoTip:LoadCallBack()
    if not self.jf_info_friend_list then
        self.jf_info_friend_list = NFACollectCardInfoList.New(NFACollectCardInfoRender, self.node_list.jf_info_friend_list)
        self.jf_info_friend_list:SetCreateCellCallBack(BindTool.Bind(self.CreateCellCallBack, self, PanelType.Friend))
    end

    if not self.jf_info_request_list then
        self.jf_info_request_list = NFACollectCardInfoList.New(NFACollectCardInfoRender, self.node_list.jf_info_request_list)
        self.jf_info_request_list:SetCreateCellCallBack(BindTool.Bind(self.CreateCellCallBack, self, PanelType.Request))
    end

    XUI.AddClickEventListener(self.node_list.jf_info_friend_btn, BindTool.Bind(self.SwitchShowPanel, self, PanelType.Friend))
    XUI.AddClickEventListener(self.node_list.jf_info_request_btn, BindTool.Bind(self.SwitchShowPanel, self, PanelType.Request))

    self:InitNFACollectCardTipImgAndText()
end

function NewFestivalCollectCardInfoTip:ReleaseCallBack()
    if self.jf_info_friend_list then
        self.jf_info_friend_list:DeleteMe()
        self.jf_info_friend_list = nil
    end

    if self.jf_info_request_list then
        self.jf_info_request_list:DeleteMe()
        self.jf_info_request_list = nil
    end
end

function NewFestivalCollectCardInfoTip:ShowIndexCallBack()
    if not self.cur_show_panel then
        return
    end

    if PanelType.Friend == self.cur_show_panel then
        self.node_list.jf_info_friend_btn.toggle.isOn = true
        self.jf_info_friend_list:SetAllCellOtherData(self.cur_show_panel, self.cur_select_item)
    elseif PanelType.Request == self.cur_show_panel then
        self.jf_info_request_list:SetAllCellOtherData(self.cur_show_panel, self.cur_select_item)
        self.node_list.jf_info_request_btn.toggle.isOn = true
    end
end

function NewFestivalCollectCardInfoTip:OnFlush()
    local collect_card_cfg = NewFestivalCollectCardWGData.Instance:GetCurActivityCfg()
    if not self.cur_show_panel or IsEmptyTable(collect_card_cfg) then
        return
    end

    local request_list = NewFestivalCollectCardWGData.Instance:GetRequestList()
    local is_show_nodata_panel = IsEmptyTable(request_list)
    local can_give_count = NewFestivalCollectCardWGData.Instance:GetTimes()
    self.node_list.jf_info_give_count.text.text = string.format(Language.CollectCard.EveryDayGiveCount, can_give_count, collect_card_cfg.times)
    self.node_list.jf_info_request_remind:CustomSetActive(not is_show_nodata_panel)

    if self.cur_show_panel == PanelType.Friend then
        local friend_list = NewFestivalCollectCardWGData.Instance:GetFriendList()
        is_show_nodata_panel = IsEmptyTable(friend_list)
        self.jf_info_friend_list:SetDataList(friend_list)
        self.node_list.jf_info_friend_list:CustomSetActive(not is_show_nodata_panel)
        self.node_list.jf_info_nodata_panel:CustomSetActive(is_show_nodata_panel)
        self.node_list.jf_info_nodata_txt.text.text = Language.CollectCard.NoFriends
    elseif self.cur_show_panel == PanelType.Request then
        self.jf_info_request_list:SetDataList(request_list)
        self.node_list.jf_info_request_list:CustomSetActive(not is_show_nodata_panel)
        self.node_list.jf_info_nodata_panel:CustomSetActive(is_show_nodata_panel)
        self.node_list.jf_info_nodata_txt.text.text = Language.CollectCard.NoRequest
    end
end

function NewFestivalCollectCardInfoTip:SwitchShowPanel(show_panel_type)
    self.cur_show_panel = show_panel_type
    self.node_list.jf_info_friend_list:CustomSetActive(self.cur_show_panel == PanelType.Friend)
    self.node_list.jf_info_request_list:CustomSetActive(self.cur_show_panel == PanelType.Request)
    self:Flush()
end

function NewFestivalCollectCardInfoTip:CreateCellCallBack(panel_type, cell)
    cell:SetOtherData(panel_type, self.cur_select_item)
end

function NewFestivalCollectCardInfoTip:SetData(show_panel_type, item_id)
    self.cur_show_panel = show_panel_type
    self.cur_select_item = item_id
end

function NewFestivalCollectCardInfoTip:InitNFACollectCardTipImgAndText()
    local tip_bg_bundle, tip_bg_asset = ResPath.GetNewFestivalRawImages("jf_tip_bg")
    self.node_list.jf_info_bg.raw_image:LoadSprite(tip_bg_bundle, tip_bg_asset, function ()
        self.node_list.jf_info_bg.raw_image:SetNativeSize()
    end)

    local close_btn_bundle, close_btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_close")
    self.node_list.jf_close_btn_img.image:LoadSprite(close_btn_bundle, close_btn_asset, function ()
        self.node_list.jf_close_btn_img.image:SetNativeSize()
    end)

    local group_bg_bundle, group_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jf_btn1")
    self.node_list.jf_toggle_group_bg.image:LoadSprite(group_bg_bundle, group_bg_asset, function ()
    end)

    local hl_bundle, hl_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jf_btn1_hl")
    self.node_list.jf_friend_btn_hl_img.image:LoadSprite(hl_bundle, hl_asset, function ()
        self.node_list.jf_friend_btn_hl_img.image:SetNativeSize()
    end)

    self.node_list.jf_request_btn_hl_img.image:LoadSprite(hl_bundle, hl_asset, function ()
        self.node_list.jf_request_btn_hl_img.image:SetNativeSize()
    end)

    local client_show_cfg = NewFestivalActivityWGData.Instance:GetCollectCardOtherCfg()
    PlayerNameColor = client_show_cfg.name_color
    RequestItemColor = client_show_cfg.item_color

    self.node_list.jf_info_give_count.text.color = Str2C3b(client_show_cfg.count_color)
    self.node_list.jf_friend_btn_nor_txt.text.color = Str2C3b(client_show_cfg.tip_btn_color)
    self.node_list.jf_friend_btn_hl_txt.text.color = Str2C3b(client_show_cfg.tip_btn_hl_color)
    self.node_list.jf_request_btn_nor_txt.text.color = Str2C3b(client_show_cfg.tip_btn_color)
    self.node_list.jf_request_btn_hl_txt.text.color = Str2C3b(client_show_cfg.tip_btn_hl_color)
end

------------------------------NFACollectCardInfoList新集福活动信息列表
NFACollectCardInfoList = NFACollectCardInfoList or BaseClass(AsyncListView)
function NFACollectCardInfoList:SetAllCellOtherData(panel_type, req_item_id)
    for k, v in pairs(self.cell_list) do
        v:SetOtherData(panel_type, req_item_id)
    end
end

------------------------------NFACollectCardInfoRender新集福活动信息列表render
NFACollectCardInfoRender = NFACollectCardInfoRender or BaseClass(BaseRender)
function NFACollectCardInfoRender:LoadCallBack()
    self.panel_type = 0
    self.req_item_id = 0

    local bg_bundle, bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jf_di3")
    self.node_list.bg.image:LoadSprite(bg_bundle, bg_asset)

    local client_show_cfg = NewFestivalActivityWGData.Instance:GetCollectCardOtherCfg()
    self.node_list.msg.text.color = Str2C3b(client_show_cfg.tip_text_color)
    self.node_list.name.text.color = Str2C3b(client_show_cfg.tip_text_color)

    local btn_bundle, btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jf_btn2")
    for i = 1, 2 do
        XUI.AddClickEventListener(self.node_list["btn" .. i], BindTool.Bind(self.OnClickBtn, self, i))
        local text = self.node_list["btn_txt" .. i].text
        text.color = Str2C3b(client_show_cfg.tip_btn_color2)

        local image = self.node_list["btn_img" .. i].image
        image:LoadSprite(btn_bundle, btn_asset, function()
            image:SetNativeSize()
        end)
    end
end

function NFACollectCardInfoRender:OnFlush()
    if not self.data then
        return
    end

    if self.panel_type == PanelType.Friend then
        self.node_list.name.text.text = self.data.name
        local flag = self.data.flag_list[self.req_item_id] or 0
        self.node_list.btn1:CustomSetActive(flag == 0)
        self.node_list.request_flag:CustomSetActive(flag == 1)
    elseif self.panel_type == PanelType.Request then
        local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
        self.node_list.msg.text.text = string.format(Language.CollectCard.RequestItem, PlayerNameColor, self.data.name, RequestItemColor, item_name)
    end
end

function NFACollectCardInfoRender:OnClickBtn(index)
    if not self.data then
        return
    end

    local op_type = 0
    local item_id = 0
    if self.panel_type == PanelType.Friend then
        item_id = self.req_item_id
        if index == 1 then          -- 索要
            local flag = self.data.flag_list[self.req_item_id] or 0
            if flag > 0 then
                TipWGCtrl.Instance:ShowSystemMsg(Language.CollectCard.HasRequest)
                return
            end

            op_type = OA_COLLECT_CARD_OP_TYPE.GAIN
        elseif index == 2 then      -- 赠送
            local can_give_count = NewFestivalCollectCardWGData.Instance:GetTimes()
            local max_give_count = NewFestivalCollectCardWGData.Instance:GetCurActivityCfg().times or 0
            if can_give_count >= max_give_count then
                TipWGCtrl.Instance:ShowSystemMsg(Language.CollectCard.SendCountNotEnough)
                return
            end

            op_type = OA_COLLECT_CARD_OP_TYPE.SEND
        end
    elseif self.panel_type == PanelType.Request then
        item_id = self.data.item_id
        if index == 1 then          -- 同意
            local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
            if item_num <= 0 then
                TipWGCtrl.Instance:ShowSystemMsg(Language.CollectCard.ItemNotEnough)
                return
            end

            op_type = OA_COLLECT_CARD_OP_TYPE.SEND
        elseif index == 2 then      -- 拒绝
            op_type = OA_COLLECT_CARD_OP_TYPE.REFUSE
        end
    end

    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.NEW_JRHD_JFHD, op_type, self.data.uuid, item_id)
end

function NFACollectCardInfoRender:SetOtherData(panel_type, req_item_id)
    self.panel_type = panel_type
    self.req_item_id = req_item_id
    self.node_list.btn_txt1.text.text = self.panel_type == PanelType.Friend and Language.CollectCard.BtnTxt1 or Language.CollectCard.BtnTxt3
    self.node_list.btn_txt2.text.text = self.panel_type == PanelType.Friend and Language.CollectCard.BtnTxt2 or Language.CollectCard.BtnTxt4
    self.node_list.name:CustomSetActive(self.panel_type == PanelType.Friend)
    self.node_list.msg:CustomSetActive(self.panel_type == PanelType.Request)
end