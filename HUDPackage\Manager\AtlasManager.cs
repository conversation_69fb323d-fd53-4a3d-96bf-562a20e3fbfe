﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.IO;
using System;
using System.Text;
using System.Runtime.InteropServices;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace HUDProgramme
{

    public class AtlasManager
    {
        protected Dictionary<string, UISpriteInfo> m_AllSprite = new Dictionary<string, UISpriteInfo>();    // 所有的精灵对象
        protected Dictionary<string, UITexAtlas> m_TexAtlas = new Dictionary<string, UITexAtlas>();         // 材质对象
        protected CMyArray<int> m_NeedReleaseAtlas = new CMyArray<int>();
        protected CMyArray<UISpriteInfo> m_SpritePtr = new CMyArray<UISpriteInfo>();
        protected CMyArray<UITexAtlas> m_AtlasPtr = new CMyArray<UITexAtlas>();
        protected Dictionary<int, UITexAtlas> m_QueryAtlas = new Dictionary<int, UITexAtlas>();
        protected UITexAtlas m_defTexAltas = new UITexAtlas();
        protected UISpriteInfo m_defSprite = new UISpriteInfo();
  


        protected int m_nFileVersion = 0;  // 文件的版本号，每次保存加1 
        protected float m_fNextUpdateTime = 0.0f;
        static int s_nLowQuality = 0; // 是不是使用低画质

        #region 配置序列化
        void SerializeIterator(CSerialize ar, ref string key, ref UISpriteInfo value)
        {
            if (key == null) key = string.Empty;
            if (value == null) value = new UISpriteInfo();
            ar.ReadWriteValue(ref key);
            value.Serailize(ref ar);
        }
        void SerializeIterator(CSerialize ar, ref string key, ref UITexAtlas value)
        {
            if (key == null) key = string.Empty;
            if (value == null) value = new UITexAtlas();
            ar.ReadWriteValue(ref key);
            value.Serailize(ref ar);
        }
        void SerializeIterator(SerializeText ar, ref string key, ref UISpriteInfo value)
        {
            if (key == null) key = string.Empty;
            if (value == null) value = new UISpriteInfo();
            ar.ReadWriteValue("Sprite", ref key);
            value.SerializeToTxt(ref ar);
        }
        void SerializeIterator(SerializeText ar, ref string key, ref UITexAtlas value)
        {
            if (key == null) key = string.Empty;
            if (value == null) value = new UITexAtlas();
            ar.ReadWriteValue("Atlas", ref key);
            value.SerializeToTxt(ref ar);
        }
        protected void Serialize(CSerialize ar)
        {
            byte yVersion = 4;
            ar.ReadWriteValue(ref yVersion);
            ar.SetVersion(yVersion);
            if (yVersion > 2)
                ar.ReadWriteValue(ref m_nFileVersion);
            ar.SerializeDictionary<string, UISpriteInfo>(ref m_AllSprite, SerializeIterator);
            ar.SerializeDictionary<string, UITexAtlas>(ref m_TexAtlas, SerializeIterator);
        }
        protected void SerializeToTxt(SerializeText ar)
        {
            byte yVersion = 4;
            ar.ReadWriteValue("Version", ref yVersion);
            if (yVersion > 2)
                ar.ReadWriteValue("SaveCount", ref m_nFileVersion);
            ar.SetVersion(yVersion);
            ar.SerializeDictionary<string, UISpriteInfo>("AllSprite", ref m_AllSprite, SerializeIterator);
            ar.SerializeDictionary<string, UITexAtlas>("AllAtlas", ref m_TexAtlas, SerializeIterator);
        }
        #endregion


        //单例对象
        static AtlasManager s_pAltasMng;
        public static AtlasManager Instance
        {
            get
            {
                if (s_pAltasMng == null)
                {
                    s_pAltasMng = new AtlasManager();
                    //s_pAltasMng.InitAltasCfg();
                }
                
                return s_pAltasMng;
            }
        }

        public void InitAltasCfg()
        {
            TextAsset text = HUDAssetsManager.Instance.get_HUDTextAsset;
            LoadAtlasCfgData(false, text);
        }

        public void InitAltasCfg(TextAsset text)
        {
            LoadAtlasCfgData(false, text);
        }

        //尝试加载配置
        bool TryLoadUICfgData(bool bReload, TextAsset text)
        {
            if (text == null)
            {
                Debug.LogError("UI cfg text load failed");
            }
            else
            {
                if (bReload)
                    ReloadCfg(text.bytes);
                else
                    InitCfg(text.bytes);
            }
            return true;
        }

        // 这个是第二次加载噢
        public void ReloadCfg(byte[] fileData)
        {
            if (fileData != null)
            {
                Dictionary<string, UISpriteInfo> AllSprite = new Dictionary<string, UISpriteInfo>();   // 所有的精灵对象    
                Dictionary<string, UITexAtlas> TexAtlas = new Dictionary<string, UITexAtlas>();    // 材质对象

                CSerialize arRead = new CSerialize(SerializeType.read, fileData, fileData.Length);

                int nFileVersion = 0;

                byte yVersion = 3;
                arRead.ReadWriteValue(ref yVersion);
                if (yVersion > 2)
                    arRead.ReadWriteValue(ref nFileVersion);

                // 文件没有修改
                if (nFileVersion <= m_nFileVersion)
                {
                    return;
                }
                arRead.SetVersion(yVersion);
                arRead.SerializeDictionary<string, UISpriteInfo>(ref AllSprite, SerializeIterator);
                arRead.SerializeDictionary<string, UITexAtlas>(ref TexAtlas, SerializeIterator);
                arRead.Close();

                // 插入吧
                Dictionary<string, UITexAtlas>.Enumerator itAtlas = TexAtlas.GetEnumerator();
                while (itAtlas.MoveNext())
                {
                    string szAtlas = itAtlas.Current.Key;
                    UITexAtlas atlas = null;
                    if (m_TexAtlas.TryGetValue(szAtlas, out atlas))
                    {
                        UITexAtlas newAtlas = itAtlas.Current.Value;
                        atlas.AdjustAtlas(newAtlas);
                        if (atlas.m_material != null)
                        {
                            // 需要重新加载纹理噢, 其实这里一般不会发生的
                            StartLoadAtlas(atlas.m_nAtlasID, true);
                        }
                    }
                    else
                        m_TexAtlas[szAtlas] = itAtlas.Current.Value;
                }
                // 原有的精灵也释放吧
                m_AllSprite = AllSprite;
                MakeSpriteAtlasID();
            }
        }

        //初始化配置
        public void InitCfg(byte[] fileData)
        {
            if (fileData != null && m_TexAtlas.Count == 0)
            {
                SerializeText ar = new SerializeText(SerializeType.read, fileData, fileData.Length);
                SerializeToTxt(ar);
                MakeSpriteAtlasID();
            }
        }

        protected bool MakeSpriteAtlasID()
        {
            m_SpritePtr.Clear();
            m_AtlasPtr.Clear();
            m_SpritePtr.reserve(m_AllSprite.Count);
            m_AtlasPtr.reserve(m_TexAtlas.Count);

            bool bDirty = false;
            CMyArray<UITexAtlas> newAtlas = new CMyArray<UITexAtlas>();

            Dictionary<string, UITexAtlas>.Enumerator itAtlas = m_TexAtlas.GetEnumerator();
            int nMaxAtlasID = m_TexAtlas.Count + 1;
            while (itAtlas.MoveNext())
            {
                UITexAtlas atlas = itAtlas.Current.Value;
                if (atlas.m_nAtlasID > 0 && atlas.m_nAtlasID <= nMaxAtlasID)
                {
                    if (m_AtlasPtr.IsValid(atlas.m_nAtlasID - 1) && m_AtlasPtr[atlas.m_nAtlasID - 1] != null)
                    {
                        newAtlas.push_back(atlas);
                    }
                    else
                    {
                        m_AtlasPtr.GrowSet(atlas.m_nAtlasID - 1, atlas);
                    }
                }
                else
                {
                    newAtlas.push_back(atlas);
                }
            }
            if (newAtlas.size() > 0)
                bDirty = true;
            int nStartPos = m_AtlasPtr.FindNextNull(0);
            for (int i = 0; i < newAtlas.size(); ++i)
            {
                UITexAtlas atlas = newAtlas[i];
                atlas.m_nAtlasID = m_AtlasPtr.FindNextNull(nStartPos) + 1;
                nStartPos = atlas.m_nAtlasID;
                m_AtlasPtr.GrowSet(atlas.m_nAtlasID - 1, atlas);
            }

            CMyArray<UISpriteInfo> newSprite = new CMyArray<UISpriteInfo>();

            Dictionary<string, UISpriteInfo>.Enumerator itSprite = m_AllSprite.GetEnumerator();
            int nMaxID = m_AllSprite.Count + 1;
            while (itSprite.MoveNext())
            {
                UISpriteInfo sp = itSprite.Current.Value;
                sp.m_nAtlasID = AtlasNameToID(sp.m_szAtlasName);
                if (sp.m_nNameID > 0 && sp.m_nNameID <= nMaxID)
                {
                    if (m_SpritePtr.IsValid(sp.m_nNameID - 1) && m_SpritePtr[sp.m_nNameID - 1] != null)
                    {
                        // 重复的ID
                        newSprite.push_back(sp);
                    }
                    else
                    {
                        m_SpritePtr.GrowSet(sp.m_nNameID - 1, sp);
                    }
                }
                else
                {
                    newSprite.push_back(sp);
                }
            }
            nStartPos = m_SpritePtr.FindNextNull(0);
            int nNewSpriteCount = newSprite.size();
            for (int i = 0; i < nNewSpriteCount; ++i)
            {
                UISpriteInfo sp = newSprite[i];
                sp.m_nNameID = m_SpritePtr.FindNextNull(nStartPos) + 1;
                nStartPos = sp.m_nNameID;
                m_SpritePtr.GrowSet(sp.m_nNameID - 1, sp);
            }
            if (newSprite.size() > 0)
                bDirty = true;
            return bDirty;
        }

        //加载图集配置数据
        void LoadAtlasCfgData(bool bReLoad, TextAsset text)
        {
            if (!TryLoadUICfgData(bReLoad, text))
                Debug.LogError("UI cfg load failed");
        }

        // 开始加载纹理
        public void StartLoadAtlas(int nAtlasID, bool bReload)
        {
            LoadAtlasMaterial(nAtlasID, bReload);
        }

        // 加载材质
        void LoadAtlasMaterial(int nAtlasID, bool bReload)
        {
            UITexAtlas atlas = GetAtlasByID(nAtlasID);
            OnQueryAtlasTex(atlas, bReload);
        }

        // 功能：得到精灵的材质名称
        public string GetAtlasNameBySpriteName(string szSpriteName)
        {
            if (string.IsNullOrEmpty(szSpriteName))
                return null;
            UISpriteInfo sprite = null;
            if (m_AllSprite.TryGetValue(szSpriteName, out sprite))
            {
                return sprite.m_szAtlasName;
            }
            return null;
        }

        #region 外部使用方法
        public UITexAtlas GetAtlasByID(int nAtlasID)
        {
            if (m_AtlasPtr.IsValid(nAtlasID - 1))
                return m_AtlasPtr[nAtlasID - 1];
            return null;
        }

        public void OnQueryAtlasTex(UITexAtlas atlas, bool bReload)
        {
            bool bHaveMainAlpha = atlas.m_szShaderName == "HUDProgramme/TransparentColoredMainAlpha";
            string szResName = atlas.m_szAtlasName;

            szResName = szResName.ToLower();
            string szResNameAlpha = string.Empty;
            if (bHaveMainAlpha)
            {
                szResNameAlpha = szResName + "_alpha";
                szResNameAlpha = szResNameAlpha.ToLower();
            }

            Texture texMain = null;
            Texture texAlpha = null;

            if (HUDAssetsManager.Instance != null)
                texMain = HUDAssetsManager.Instance.GetAtlasTexture(szResName);
            else
                texMain = this.GetAtlasTexture(szResName);

            if (HUDAssetsManager.Instance != null)
                texAlpha = HUDAssetsManager.Instance.GetAtlasTexture(szResNameAlpha);
            else
                texAlpha = this.GetAtlasTexture(szResNameAlpha);

            Texture oldAlpha = null;
            atlas.m_MainAlpha = texAlpha;
            Texture oldTex = null;

            if (atlas.m_material != null && texAlpha != null)
            {
                oldAlpha = atlas.m_material.GetTexture("_MainAlpha");
                atlas.m_material.SetTexture("_MainAlpha", texAlpha);
            }

            if (atlas.m_material != null && texMain != null)
            {
                oldTex = atlas.m_material.mainTexture;
                atlas.m_material.mainTexture = texMain;
            }
            if (oldAlpha == oldTex)
                oldAlpha = null;
            if (oldTex != null && oldTex != texMain)
            {
                DestroyTexture(oldTex, false);
            }
        }

        void DestroyTexture(Texture tex, bool bFromAssets)
        {
            if (tex != null)
            {
                if (bFromAssets)
                    UnityEngine.Object.DestroyImmediate(tex, true);
                else
                    UnityEngine.Object.Destroy(tex);
            }
        }

        public int AtlasNameToID(string szAtlasName)
        {
            if (string.IsNullOrEmpty(szAtlasName))
                return 0;
            UITexAtlas atlas = null;
            if (m_TexAtlas.TryGetValue(szAtlasName, out atlas))
            {
                return atlas.m_nAtlasID;
            }
            return 0;
        }

        public UISpriteInfo GetSafeSpriteByID(int nSpriteID)
        {
            UISpriteInfo sprite = GetSpriteByID(nSpriteID);
            if (sprite != null)
                return sprite;
            return m_defSprite;
        }

        public UISpriteInfo GetSpriteByID(int nSpriteID)
        {
            if (m_SpritePtr.IsValid(nSpriteID - 1))
                return m_SpritePtr[nSpriteID - 1];
            return null;
        }

        public void ReleaseAtlasByID(int nAtlasID)
        {
            UITexAtlas atlas = GetAtlasByID(nAtlasID);
            RelaaseByAtlas(atlas);
        }

        private void RelaaseByAtlas(UITexAtlas atlas)
        {
            if (atlas != null)
            {
                atlas.m_nRef--;
                if (atlas.m_nRef == 0)
                {
                    // 从前面添加
                    PushReleaseAtlasID(atlas.m_nAtlasID);

                    // 这里只做标记，延迟释放吧
                    atlas.m_fReleaseTime = Time.time;

                    atlas.m_nVersion++;

                    m_QueryAtlas.Remove(atlas.m_nAtlasID);

                    if (IsLowQuality() && m_NeedReleaseAtlas.size() > 5)
                    {
                        m_fNextUpdateTime = 0.0f;
                    }
                }
            }
        }

        void PushReleaseAtlasID(int nAtlasID)
        {
            for (int i = 0, iLen = m_NeedReleaseAtlas.size(); i < iLen; ++i)
            {
                if (m_NeedReleaseAtlas[i] == nAtlasID)
                    return;
            }
            m_NeedReleaseAtlas.push_front(nAtlasID);
        }

        public void QueryAtlasByID(int nAtlasID)
        {
            UITexAtlas atlas = GetAtlasByID(nAtlasID);
            QueryByAtlas(atlas);
        }

        public void QueryAtlasBySpriteID(int nSpriteID)
        {
            UITexAtlas atlas = FastGetAtlasBySpriteID(nSpriteID);
            QueryByAtlas(atlas);
        }

        public UITexAtlas FastGetAtlasBySpriteID(int nSpriteID)
        {
            if (m_SpritePtr.IsValid(nSpriteID - 1))
            {
                UISpriteInfo sprite = m_SpritePtr[nSpriteID - 1];
                if (sprite != null)
                {
                    if (m_AtlasPtr.IsValid(sprite.m_nAtlasID - 1))
                        return m_AtlasPtr[sprite.m_nAtlasID - 1];
                }
            }
            return null;
        }

        private void QueryByAtlas(UITexAtlas atlas)
        {
            if (atlas != null)
            {
                if (atlas.m_material == null || atlas.m_material.mainTexture == null || atlas.m_bLoading)
                    QueryAltasTex(atlas);
                atlas.m_nRef++;
                m_QueryAtlas[atlas.m_nAtlasID] = atlas;
            }
        }

        public int SpriteNameToID(string szSpriteName)
        {
            if (string.IsNullOrEmpty(szSpriteName))
                return 0;
            UISpriteInfo sprite = null;
            if (m_AllSprite.TryGetValue(szSpriteName, out sprite))
            {
                return sprite.m_nNameID;
            }
            return 0;
        }

        public int GetAtlasIDBySpriteID(int nSpriteID)
        {
            UISpriteInfo sprite = GetSpriteByID(nSpriteID);
            if (sprite != null)
                return sprite.m_nAtlasID;
            return 0;
        }

        public UITexAtlas GetAltasBySpriteID(int nSpriteID)
        {
            UISpriteInfo sprite = GetSpriteByID(nSpriteID);
            if (sprite != null)
            {
                UITexAtlas atlas = GetAtlasByID(sprite.m_nAtlasID);
                if (atlas.m_material == null)
                    QueryAltasTex(atlas);
                return atlas;
            }
            return null;
        }

        public UISpriteInfo GetSprite(string szSpriteName)
        {
            if (string.IsNullOrEmpty(szSpriteName))
                return null;
            UISpriteInfo sprite = null;
            if (m_AllSprite.TryGetValue(szSpriteName, out sprite))
            {
                return sprite;
            }
            return null;
        }

        // 功能：通过精灵的名字得到材质
        public UITexAtlas GetAltasBySpriteName(string szSpriteName)
        {
            if (string.IsNullOrEmpty(szSpriteName))
                return null;
            UISpriteInfo sprite = null;
            if (m_AllSprite.TryGetValue(szSpriteName, out sprite))
            {
                UITexAtlas atlas = null;
                if (m_TexAtlas.TryGetValue(sprite.m_szAtlasName, out atlas))
                {
                    // 加载纹理吧
                    if (atlas.m_material == null)
                        QueryAltasTex(atlas);
                    return atlas;
                }
            }
            return null;
        }

        public UITexAtlas GetAltas(string szAtlasName)
        {
            if (string.IsNullOrEmpty(szAtlasName))
                return null;
            UITexAtlas atlas = null;
            if (m_TexAtlas.TryGetValue(szAtlasName, out atlas))
            {
            }
            return atlas;
        }
        #endregion

        protected bool QueryAltasTex(UITexAtlas atlas)
        {
            Texture tex = null;//HUDAssetsManager.Instance.GetAtlasTexture(atlas.m_szAtlasName);

            if (HUDAssetsManager.Instance != null)
                tex = HUDAssetsManager.Instance.GetAtlasTexture(atlas.m_szAtlasName);
            else
                tex = this.GetAtlasTexture(atlas.m_szAtlasName);

            if (atlas.m_material == null)
            {
                string szShaderName = atlas.m_szShaderName;
                bool bHaveMainAlpha = atlas.m_szShaderName == "HUDProgramme/TransparentColoredMainAlpha";

                Shader shader = Shader.Find(szShaderName);
                if (shader == null)
                {
                    shader = Shader.Find("HUDProgramme/TransparentColored");
                }
                atlas.m_material = new Material(shader);
                atlas.m_material.name = atlas.m_szAtlasName;
                if (bHaveMainAlpha)
                {
                    Texture mainAlpha = null;//HUDAssetsManager.Instance.GetAtlasTexture(atlas.m_szAtlasName + "_alpha");

                    if (HUDAssetsManager.Instance != null)
                        mainAlpha = HUDAssetsManager.Instance.GetAtlasTexture(atlas.m_szAtlasName + "_alpha");
                    else
                        mainAlpha = this.GetAtlasTexture(atlas.m_szAtlasName + "_alpha");

                    atlas.m_MainAlpha = mainAlpha;
                    atlas.m_material.SetTexture("_MainAlpha", mainAlpha);
                }
            }
            atlas.m_material.mainTexture = tex;
            if (tex != null)
                atlas.m_nVersion++;

            return true;
        }

        // 功能：是不是低画质
        static public bool IsLowQuality()
        {
            if (0 == s_nLowQuality)
                s_nLowQuality = SystemInfo.systemMemorySize <= 1024 ? 1 : 2; // 如果内存小于1G，就认定是低画质
            return s_nLowQuality == 1;
        }

        public bool IsEditorMode()
        {
            //return false;
            return true;
        }


#if UNITY_EDITOR

        public class CPngHelp
        {
            public static Texture2D ReadPng(string szPathname)
            {
                string szDataPath = Application.dataPath;
                string szAssetsPathName = szPathname.Substring(szDataPath.Length - 6);
                return ReadPngByUnity(szAssetsPathName);
            }
            public static Texture2D ReadAssetPng(string szAssetPathName)
            {
                string szDataPath = Application.dataPath;
                szDataPath = szDataPath.Substring(0, szDataPath.Length - 6);
                return ReadPng(szDataPath + szAssetPathName);
            }
            static string m_szCurReadTexturePath = string.Empty;

            public static string curReadTextureAssetsPathName
            {
                get { return m_szCurReadTexturePath; }
            }

            static string GetMetaPathname(string szAssetsPathName)
            {
                string szDataPath = Application.dataPath;
                szDataPath = szDataPath.Substring(0, szDataPath.Length - 6);
                return szDataPath + szAssetsPathName + ".meta";
            }
            public static Texture2D ReadPngByUnity(string szAssetsPathName)
            {
                if (string.IsNullOrEmpty(szAssetsPathName)) return null;
                TextureImporter ti = AssetImporter.GetAtPath(szAssetsPathName) as TextureImporter;
                if (ti == null) return null;

                m_szCurReadTexturePath = szAssetsPathName;

                string szPlatName = "Standalone";
#if UNITY_ANDROID
        szPlatName = "Android";
#elif UNITY_IPHONE
        szPlatName = "iPhone";
#endif

                TextureImporterPlatformSettings oldSettings = ti.GetPlatformTextureSettings(szPlatName);
                TextureImporterPlatformSettings settings = new TextureImporterPlatformSettings();
                oldSettings.CopyTo(settings);
                TextureImporterPlatformSettings defaultSet = ti.GetPlatformTextureSettings("Default");

                bool mipmapEnabled = ti.mipmapEnabled;
                bool isReadable = ti.isReadable;
                FilterMode filterMode = ti.filterMode;
                TextureWrapMode wrapMode = ti.wrapMode;
                TextureImporterNPOTScale npotScale = ti.npotScale;
                TextureImporterType textureType = ti.textureType;

                string szMetaPathName = GetMetaPathname(szAssetsPathName);

                bool bDirty = false;
                if (ti.mipmapEnabled ||
                    !ti.isReadable ||
                    ti.npotScale != TextureImporterNPOTScale.None ||
                    ti.textureType != TextureImporterType.GUI ||
                    settings.format != TextureImporterFormat.RGBA32 ||
                    settings.textureCompression != TextureImporterCompression.Uncompressed)
                {
                    ti.mipmapEnabled = false;
                    ti.isReadable = true;
                    ti.textureType = TextureImporterType.GUI;
                    ti.npotScale = TextureImporterNPOTScale.None;

                    settings.maxTextureSize = 4096;
                    settings.format = TextureImporterFormat.RGBA32;
                    settings.textureCompression = TextureImporterCompression.Uncompressed;
                    settings.overridden = true;

                    ti.SetPlatformTextureSettings(settings);
                    ti.SaveAndReimport();
                    AssetDatabase.Refresh();
                    bDirty = true;
                }
                Texture2D tex = AssetDatabase.LoadAssetAtPath(szAssetsPathName, typeof(Texture2D)) as Texture2D;

                //if (tex != null)
                //{
                //    Texture2D texNew = new Texture2D(tex.width, tex.height, tex.format, tex.mipmapCount > 1);
                //    Color[] pixel = tex.GetPixels();
                //    texNew.SetPixels(pixel);
                //    texNew.Apply();
                //    tex = texNew;
                //}

                //m_szCurReadTexturePath = string.Empty;

                //if (bDirty)
                //{
                //    ti.mipmapEnabled = mipmapEnabled;
                //    ti.isReadable = isReadable;
                //    ti.filterMode = filterMode;
                //    ti.wrapMode = wrapMode;
                //    ti.npotScale = npotScale;
                //    ti.textureType = textureType;

                //    ti.SetPlatformTextureSettings(oldSettings);
                //    ti.SaveAndReimport();
                //    AssetDatabase.ImportAsset(szAssetsPathName, ImportAssetOptions.ForceUpdate | ImportAssetOptions.DontDownloadFromCacheServer);
                //    //File.WriteAllBytes(szMetaPathName, metaFileData);
                //    AssetDatabase.Refresh();
                //}
                return tex;
            }
        };
        public struct SelecctTexInfo
        {
            public Texture2D m_tex;        // 纹理对象
            public string m_szSpriteName;        // 节点的名字(文件名，没有扩展名)
            public string m_szAssetsName;  // Assets相对文件目录名
            public string m_szAtlasName;   // 所在的材质
            public UISpriteInfo m_sprite;  // 精灵信息
        };

        public class UpdateTexNameList
        {
            public List<SelecctTexInfo> nameList = new List<SelecctTexInfo>();
            public void push_back(SelecctTexInfo node)
            {
                nameList.Add(node);
            }
        };

        public bool IsCanSave()
        {
            if (!IsEditorMode())
                return false;

            string szCfgPathName = HUDConfig.szCfgPathName;
            if (File.Exists(szCfgPathName))
            {
                SerializeText ar = new SerializeText(SerializeType.read, szCfgPathName);

                int nFileVersion = m_nFileVersion;
                byte yVersion = 3;
                ar.ReadWriteValue("Version", ref yVersion);
                if (yVersion > 2)
                    ar.ReadWriteValue("SaveCount", ref nFileVersion);
                bool bDirty = nFileVersion != m_nFileVersion;

                if (bDirty)
                {
                    EditorUtility.DisplayDialog("警告", "你的材质有更新。\n请确认Atlas目录资源全部是最新的。\n为保证图集的正确性，请重启Unity。\n记得撤消你本地的修改，再拉取最新的资源。", "记得重启噢");
                }
                return !bDirty;
            }
            return true;
        }

        Texture2D LoadTextureByAssetsName(string szAssetsName)
        {
            return LoadCanWriteTextureByAssetsName(szAssetsName);
        }

        // 功能：加载一个可以读写的纹理
        Texture2D LoadCanWriteTextureByAssetsName(string szAssetsPathName)
        {
            string szDataPath = Application.dataPath;
            // Assets
            szDataPath = szDataPath.Substring(0, szDataPath.Length - 6);

            string szPathName = szDataPath + szAssetsPathName;
            Texture2D tex = CPngHelp.ReadPng(szPathName);
            return tex;
        }
        Dictionary<string, UpdateTexNameList> GetSelectedTexInfo(List<SUpdateTexInfo> nameList)
        {
            Dictionary<string, UpdateTexNameList> selectList = new Dictionary<string, UpdateTexNameList>();
            int nCount = nameList.Count;
            foreach (SUpdateTexInfo o in nameList)
            {
                SelecctTexInfo info = new SelecctTexInfo();
                info.m_szAssetsName = o.m_szAssetsName;
                info.m_szSpriteName = o.m_szSpriteName;
                info.m_szAtlasName = o.m_szAtlasName;
                info.m_sprite = GetSprite(o.m_szSpriteName);
                info.m_tex = LoadTextureByAssetsName(o.m_szAssetsName);

                if (selectList.ContainsKey(o.m_szAtlasName))
                    selectList[o.m_szAtlasName].push_back(info);
                else
                {
                    UpdateTexNameList sList = new UpdateTexNameList();
                    sList.push_back(info);
                    selectList[o.m_szAtlasName] = sList;
                }
            }
            return selectList;
        }

        // 功能：新建材质
        bool NewAtlas(string szAtlasName, string szShaderName, bool bCanLOD)
        {
            Shader shader = Shader.Find(szShaderName);
            UITexAtlas atlas = new UITexAtlas();
            atlas.m_szAtlasName = szAtlasName;
            atlas.m_szTexName = szAtlasName + ".png";
            atlas.m_material = new Material(shader);
            atlas.m_szShaderName = szShaderName;// bAtlasPMA ? "Unlit/Premultiplied Colored" : "Unlit/Transparent Colored";
            atlas.SetLODFlag(bCanLOD);
            atlas.SetTextureSizeByMaterial(atlas.m_material);
            m_TexAtlas[szAtlasName] = atlas;

            return true;
        }

        void WriteIconPixels(ref Color32[] atlasPixels, int nAtlasW, int nAtlasH, int x, int y, int w, int h, Color32[] iconPixels)
        {
            // 左下角是(0, 0), 坐标是反的噢
            for (int nY = 0; nY < h; ++nY)
            {
                for (int nX = 0; nX < w; ++nX)
                {
                    int nIndex = (nAtlasH - nY - y - 1) * nAtlasW + (nX + x);
                    int nSrcIndex = (h - nY - 1) * w + nX;
                    atlasPixels[nIndex] = iconPixels[nSrcIndex];
                }
            }
        }

        void SaveMainAlpha(Texture2D atlasTex, string szPathName)
        {
            if (atlasTex == null)
                return;
            Texture2D mainAlpha = new Texture2D(atlasTex.width, atlasTex.height, TextureFormat.ARGB32, false);
            Color32[] pixel = atlasTex.GetPixels32();
            if (pixel != null)
            {
                for (int i = 0; i < pixel.Length; ++i)
                {
                    pixel[i].r = pixel[i].g = pixel[i].b = pixel[i].a;
                }
            }
            mainAlpha.SetPixels32(pixel);
            mainAlpha.Apply();
            string szAlphaPathName = szPathName;
            if (szAlphaPathName.IndexOf("_alpha.") == -1)
                szAlphaPathName = szPathName.Replace(".png", "_alpha.png");
            bool bFindFile = false;
            if (System.IO.File.Exists(szAlphaPathName))
            {
                bFindFile = true;
                System.IO.FileAttributes newPathAttrs = System.IO.File.GetAttributes(szAlphaPathName);
                newPathAttrs &= ~System.IO.FileAttributes.ReadOnly;
                System.IO.File.SetAttributes(szAlphaPathName, newPathAttrs);
            }
            byte[] bytes = mainAlpha.EncodeToPNG();
            if (bytes == null)
            {
                return;
            }
            System.IO.File.WriteAllBytes(szAlphaPathName, bytes);
            bytes = null;
            if (!bFindFile)
            {
                AssetDatabase.Refresh();
                ChangeTextureFormat(szAlphaPathName);
            }
        }

        public void ChangeAssetTextureFormat(string szAssetsPath, TextureImporterType nImType, bool bRefresh = true)
        {
            TextureImporter texImp = TextureImporter.GetAtPath(szAssetsPath) as TextureImporter;
            if (texImp == null)
            {
                AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
                texImp = TextureImporter.GetAtPath(szAssetsPath) as TextureImporter;
            }
            if (texImp != null)
            {
                texImp.textureType = nImType;
                if (nImType == TextureImporterType.GUI)
                {
                    texImp.textureType = TextureImporterType.GUI;
                    texImp.mipmapEnabled = false;
                    texImp.anisoLevel = 0;
                }
                texImp.isReadable = false;
                texImp.filterMode = FilterMode.Bilinear;
                int maxsize = 2048;


                TextureImporterPlatformSettings androidSet = texImp.GetPlatformTextureSettings("Android");
                TextureImporterPlatformSettings iosSet = texImp.GetPlatformTextureSettings("iPhone");
                TextureImporterPlatformSettings pcSet = texImp.GetPlatformTextureSettings("Standalone");

                androidSet.maxTextureSize = maxsize;
                androidSet.format = TextureImporterFormat.ETC_RGB4;

                iosSet.maxTextureSize = maxsize;
                iosSet.format = TextureImporterFormat.PVRTC_RGB4;

                pcSet.maxTextureSize = maxsize;
                pcSet.format = TextureImporterFormat.RGBA32;

                if (szAssetsPath.IndexOf("_alpha.png") != -1
                    || szAssetsPath.IndexOf("_alpha_L.png") != -1)
                {
                    pcSet.format = TextureImporterFormat.Alpha8;
                }

                texImp.SetPlatformTextureSettings(androidSet);
                texImp.SetPlatformTextureSettings(iosSet);
                texImp.SetPlatformTextureSettings(pcSet);
                texImp.SaveAndReimport();
                if (bRefresh)
                {
                    AssetDatabase.ImportAsset(szAssetsPath, ImportAssetOptions.ForceUpdate);
                    AssetDatabase.Refresh();
                }
            }
        }

        public void ChangeTextureFormat(string szPathName)
        {
            szPathName = szPathName.Replace('\\', '/');
            int nPos = szPathName.IndexOf("/Assets/Atlas/");
            string szAssetsPath = szPathName.Substring(nPos + 1);
            ChangeAssetTextureFormat(szAssetsPath, TextureImporterType.GUI);
        }

        // 功能：尝试只是原位置替换图片
        bool TryRepaceLocalPosition(ref List<SelecctTexInfo> aSelectTex, string szAtlasName, bool bTrimAlpha)
        {
            UITexAtlas atlas = GetAltas(szAtlasName);
            if (atlas == null)
                return false;
            // 检查一下所有选择对象，必须是原来存在的，并且大小相同
            foreach (SelecctTexInfo selTex in aSelectTex)
            {
                UISpriteInfo sp = GetSprite(selTex.m_szSpriteName);
                if (null == sp)
                    return false;
                if (selTex.m_tex == null)
                    return false;
                RectInt rcOut = new RectInt(sp.outer);
                if (rcOut.width != selTex.m_tex.width
                    || rcOut.height != selTex.m_tex.height)
                    return false;
            }
            string szAtlasPathName = string.Format(HUDConfig.szAtlasPathName, szAtlasName);
            Texture2D atlasTex = LoadCanWriteTextureByAssetsName(szAtlasPathName);
            if (atlasTex == null)
                return false;

            Color32[] atlasPixels = atlasTex.GetPixels32();
            atlasTex = new Texture2D(atlasTex.width, atlasTex.height, TextureFormat.ARGB32, false);
            foreach (SelecctTexInfo selTex in aSelectTex)
            {
                UISpriteInfo sp = GetSprite(selTex.m_szSpriteName);
                RectInt rcOut = new RectInt(sp.outer);
                Color32[] iconPixels = selTex.m_tex.GetPixels32();
                WriteIconPixels(ref atlasPixels, atlasTex.width, atlasTex.height, rcOut.left, rcOut.top, rcOut.width, rcOut.height, iconPixels);
            }
            atlasTex.SetPixels32(atlasPixels);
            atlasTex.Apply();
            // 先去只读吧

            string szDataPath = Application.dataPath;
            // Assets
            szDataPath = szDataPath.Substring(0, szDataPath.Length - 6);

            string newPath = szDataPath + szAtlasPathName;
            if (System.IO.File.Exists(newPath))
            {
                System.IO.FileAttributes newPathAttrs = System.IO.File.GetAttributes(newPath);
                newPathAttrs &= ~System.IO.FileAttributes.ReadOnly;
                System.IO.File.SetAttributes(newPath, newPathAttrs);
                //System.IO.File.Delete(newPath);
            }
            byte[] bytes = atlasTex.EncodeToPNG();
            if (bytes == null)
            {
                return false;
            }
            System.IO.File.WriteAllBytes(newPath, bytes);
            bytes = null;

            SaveMainAlpha(atlasTex, newPath);

            UnityEngine.Object.DestroyImmediate(atlasTex);

            return true;
        }

        // 功能：得到材质的所有精灵对象
        List<UISpriteInfo> GetAllSpriteByAtlas(string szAtlasName)
        {
            List<UISpriteInfo> aSprite = new List<UISpriteInfo>();
            Dictionary<string, UISpriteInfo>.Enumerator it = m_AllSprite.GetEnumerator();
            while (it.MoveNext())
            {
                if (it.Current.Value.m_szAtlasName == szAtlasName)
                {
                    aSprite.Add(it.Current.Value);
                }
            }
            return aSprite;
        }

        static public Color ApplyPMA(Color c)
        {
            if (c.a != 1f)
            {
                c.r *= c.a;
                c.g *= c.a;
                c.b *= c.a;
            }
            return c;
        }

        // 功能：去除多余的像素
        Color32[] TrimAlpha(ref Color32[] pixels, ref RectInt rc)
        {
            Color32[] oldPixels = pixels;
            RectInt oldRc = rc;
            RectInt newRc = rc;
            newRc.swap();
            int nW = rc.width, nH = rc.height;
            int nPixelNumb = 0, newIndex = 0;
            for (int y = 0; y < nH; ++y)
            {
                for (int x = 0; x < nW; ++x, ++newIndex)
                {
                    if (pixels[newIndex].a != 0)
                    {
                        ++nPixelNumb;
                        newRc.unitPoint(x, y);
                    }
                }
            }
            if (newRc == oldRc)
                return pixels;
            // 空的图
            if (0 == nPixelNumb)
            {
                rc.SetRect(0, 0, 1, 1);
                pixels = new Color32[1];
                pixels[0] = new Color32(0, 0, 0, 0);
                return pixels;
            }
            pixels = new Color32[newRc.width * newRc.height];
            newIndex = 0;
            int oldIndex = 0;
            for (int y = newRc.top; y < newRc.bottom; ++y)
            {
                for (int x = newRc.left; x < newRc.right; ++x, ++newIndex)
                {
                    oldIndex = y * nW + x;
                    pixels[newIndex] = oldPixels[oldIndex];
                }
            }
            rc = newRc;
            //rc.SetRect(0, 0, newRc.width, newRc.height);
            return pixels;
        }

        // 功能：从主材质中拷贝精灵的纹理
        // 参数：pAtlasTex - 主材质
        //       sprite - 精灵对象
        //       bPMA - 是不是将颜色预乘以alpha
        Texture2D GetSpriteTex(Texture2D pAtlasTex, UISpriteInfo sprite, bool bPMA, bool bTrimAlpha)
        {
            RectInt rcAtlas = new RectInt(0, 0, pAtlasTex.width, pAtlasTex.height);
            RectInt rcSprite = new RectInt(sprite.outer);
            rcSprite = RectInt.clipRect(rcAtlas, rcSprite);

            int newWidth = rcSprite.width;
            int newHeight = rcSprite.height;
            int oldWidth = rcAtlas.width;
            int oldHeight = rcAtlas.height;

            if (newWidth <= 0 || newHeight <= 0)
                return null;
            Color32[] pixels = pAtlasTex.GetPixels32();

            Color32[] newPixels = new Color32[newWidth * newHeight];

            int newIndex = 0, oldIndex = 0;
            int nTop = oldHeight - rcSprite.bottom;
            int nBottom = nTop + rcSprite.height;     // 这图像是倒过来的
            for (int y = nTop; y < nBottom; ++y)
            {
                for (int x = rcSprite.left; x < rcSprite.right; ++x, ++newIndex)
                {
                    oldIndex = y * oldWidth + x;
                    // 如果是PMA属性  NGUISettings.atlasPMA
                    if (bPMA && pixels[oldIndex].a != 1) // NGUISettings.atlasPMA)
                        newPixels[newIndex] = ApplyPMA(pixels[oldIndex]);
                    else
                        newPixels[newIndex] = pixels[oldIndex];
                }
            }

            // 去掉多余的像素(一般来说，从主图中获取时不需要做alpha测试)，但这里还是加上吧
            rcSprite = new RectInt(0, 0, rcSprite.width, rcSprite.height);
            RectInt rcOld = rcSprite;
            if (bTrimAlpha)
            {
                newPixels = TrimAlpha(ref newPixels, ref rcSprite);
                if (rcSprite != rcOld)
                {
                    int nPadingLeft = Mathf.RoundToInt(sprite.paddingLeft * sprite.outer.width);
                    int nPadingRight = Mathf.RoundToInt(sprite.paddingRight * sprite.outer.width);
                    int nPadingTop = Mathf.RoundToInt(sprite.paddingTop * sprite.outer.height);
                    int nPadingBottom = Mathf.RoundToInt(sprite.paddingBottom * sprite.outer.height);

                    nPadingLeft += rcSprite.left;
                    nPadingRight += rcOld.right - rcSprite.right;
                    nPadingTop += rcSprite.top;
                    nPadingBottom += rcOld.bottom - rcSprite.bottom;

                    float fNewW = (float)rcSprite.width;
                    float fNewH = (float)rcSprite.height;
                    sprite.paddingLeft = (float)nPadingLeft / fNewW;
                    sprite.paddingRight = (float)nPadingRight / fNewW;
                    sprite.paddingTop = (float)nPadingTop / fNewH;
                    sprite.paddingBottom = (float)nPadingBottom / fNewH;
                }
            }

            Texture2D tex = null;
            try
            {
                tex = new Texture2D(rcSprite.width, rcSprite.height, TextureFormat.ARGB32, false);
                tex.SetPixels32(newPixels);
                tex.Apply();
            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
            }

            return tex;
        }

        // 功能：得到纹理对象AssetsPath
        static public string GetAssetPathByTexture(Texture tex)
        {
            if (tex != null)
            {
                string path = AssetDatabase.GetAssetPath(tex.GetInstanceID());
                return path;
            }
            return "";
        }

        // 功能：更新选中的对象
        bool UpdateAltasBySelect(ref List<SelecctTexInfo> aSelectTex, string szAtlasName, bool bTrimAlpha, ref bool bReplace)
        {
            // 尝试原位置替换
            bReplace = true;
            if (TryRepaceLocalPosition(ref aSelectTex, szAtlasName, bTrimAlpha))
                return true;

            bReplace = false;
            UITexAtlas atlas = GetAltas(szAtlasName);
            string szAtlasPathName = string.Format(HUDConfig.szAtlasPathName, szAtlasName);

            Texture2D atlasTex = LoadCanWriteTextureByAssetsName(szAtlasPathName);

            Dictionary<string, SelecctTexInfo> aAllSpriteInfo = new Dictionary<string, SelecctTexInfo>();

            List<Texture2D> aTex = new List<Texture2D>();
            int nCount = aSelectTex.Count;
            for (int i = 0; i < nCount; ++i)
            {
                aAllSpriteInfo[aSelectTex[i].m_szSpriteName] = aSelectTex[i];
            }

            bool bPMA = atlas != null ? atlas.premultipliedAlpha : false;

            // 先得到旧的吧
            List<SelecctTexInfo> aNewSelect = new List<SelecctTexInfo>();
            List<UISpriteInfo> aSprite = GetAllSpriteByAtlas(szAtlasName);
            nCount = aSprite.Count;
            for (int i = 0; i < nCount; ++i)
            {
                if (!aAllSpriteInfo.ContainsKey(aSprite[i].name))
                {
                    Texture2D tex = GetSpriteTex(atlasTex, aSprite[i], bPMA, bTrimAlpha);
                    SelecctTexInfo info = new SelecctTexInfo();
                    info.m_sprite = aSprite[i];
                    info.m_szAssetsName = GetAssetPathByTexture(tex);
                    info.m_szAtlasName = szAtlasName;
                    info.m_szSpriteName = aSprite[i].name;
                    info.m_tex = tex;
                    aNewSelect.Add(info);
                }
            }

            nCount = aSelectTex.Count;
            for (int i = 0; i < nCount; ++i)
            {
                aNewSelect.Add(aSelectTex[i]);
            }
            aSelectTex = aNewSelect;
            nCount = aNewSelect.Count;
            for (int i = 0; i < nCount; ++i)
            {
                aTex.Add(aNewSelect[i].m_tex);
            }
            return UpdateAltasBySelect(aNewSelect, aTex, atlas, atlasTex, szAtlasPathName);
        }

        // 功能：更新材质

        bool UpdateAltasBySelect(List<SelecctTexInfo> aSelectTex, List<Texture2D> aTex, UITexAtlas atlas, Texture2D atlasTex, string szAtlasPathName)
        {
            int nPadding = 1;
            int nMaxSize = 4096;
            if (atlas != null)
            {
                nPadding = atlas.pixelSize;
            }
            if (aTex.Count == 1)
                nPadding = 0;
            bool bNeedSaveAssets = false;
            if (atlasTex == null)
            {
                bNeedSaveAssets = true;
                atlasTex = new Texture2D(1, 1, TextureFormat.ARGB32, false);
            }

            Rect[] aSpriteRect = atlasTex.PackTextures(aTex.ToArray(), nPadding, nMaxSize);
            if (aSpriteRect.Length != aSelectTex.Count)
            {
                // 失败了

                return false;
            }

            // 检查是不是变小了
            for (int i = 0; i < aSelectTex.Count; ++i)
            {
                UISpriteInfo sp = aSelectTex[i].m_sprite;
                if (sp != null)
                {
                    Rect rect = ConvertToPixels(aSpriteRect[i], atlasTex.width, atlasTex.height, true);
                    RectInt rcOut = new RectInt(sp.outer);
                    RectInt rcNew = new RectInt(rect);
                    if (aTex[i].width != rcNew.width
                        || aTex[i].height != rcNew.height)
                    {
                        return false;
                    }
                }
            }

            // 先去只读吧
            string szDataPath = Application.dataPath;
            // Assets
            szDataPath = szDataPath.Substring(0, szDataPath.Length - 6);

            string newPath = szDataPath + szAtlasPathName;
            bool bFindAltasFile = false;
            if (System.IO.File.Exists(newPath))
            {
                bFindAltasFile = true;
                System.IO.FileAttributes newPathAttrs = System.IO.File.GetAttributes(newPath);
                newPathAttrs &= ~System.IO.FileAttributes.ReadOnly;
                System.IO.File.SetAttributes(newPath, newPathAttrs);
            }
            byte[] bytes = atlasTex.EncodeToPNG();
            if (bytes == null)
            {
                return false;
            }
            System.IO.File.WriteAllBytes(newPath, bytes);
            bytes = null;

            int nCount = aSelectTex.Count;
            bool bNew = false;
            for (int i = 0; i < nCount; ++i)
            {
                UISpriteInfo sp = aSelectTex[i].m_sprite;
                bNew = (sp == null);

                Rect rect = ConvertToPixels(aSpriteRect[i], atlasTex.width, atlasTex.height, true);
                if (sp == null)
                {
                    SelecctTexInfo sObj = aSelectTex[i];
                    sObj.m_sprite = sp = new UISpriteInfo();
                    sp.m_szAtlasName = sObj.m_szAtlasName;
                    sp.name = sObj.m_szSpriteName;
                    aSelectTex[i] = sObj;

                    // 这是新的
                    sp.outer = rect;
                    sp.inner = rect;

                    sp.paddingLeft = 0.0f;
                    sp.paddingTop = 0.0f;
                    sp.paddingRight = 0.0f;
                    sp.paddingBottom = 0.0f;
                }
                else
                {
                    RectInt rcOut = new RectInt(sp.outer);
                    RectInt rcIn = new RectInt(sp.inner);
                    sp.outer = rect;

                    rcIn.left -= rcOut.left;
                    rcIn.right -= rcOut.right;
                    rcIn.top -= rcOut.top;
                    rcIn.bottom -= rcOut.bottom;

                    rcOut.SetRect(rect);
                    rcOut.left += rcIn.left;
                    rcOut.top += rcIn.top;
                    rcOut.right += rcIn.right;
                    rcOut.bottom += rcIn.bottom;
                    sp.inner = new Rect(rcOut.left, rcOut.top, rcOut.width, rcOut.height);
                }
                float width = Mathf.Max(1f, sp.outer.width);
                float height = Mathf.Max(1f, sp.outer.height);

                // Sprite's padding values are relative to width and height
            }
            atlas.SetTextureSizeByTexture(atlasTex);
            atlas.m_nVersion++;
            atlas.m_bDirty = true;
            if (atlas.m_nRef > 0)
            {
                if (atlas.m_material != null)
                    atlas.m_material.mainTexture = atlasTex;
            }

            if (!bFindAltasFile)
            {
                ChangeTextureFormat(newPath);
            }
            SaveMainAlpha(atlasTex, newPath);

            if (atlas.IsCanLOD())
            {
                string szAssetPath = HUDConfig.szAtlasPath;
                string szAtlasName = atlas.m_szAtlasName;
                MakeLODTexture(szAssetPath, szAtlasName);
                MakeLODTexture(szAssetPath, szAtlasName + "_alpha");
            }

            return true;
        }

        public void MakeLODTexture(string szAssetPath, string szAtlasName, bool bChangeFormat = true, int nMinSize = 256)
        {
            string szAtlasPathName = szAssetPath + szAtlasName + ".png";
            Texture2D atlasTex = LoadCanWriteTextureByAssetsName(szAtlasPathName);
            int nWidth = atlasTex.width;
            int nHeight = atlasTex.height;
            Color[] PixelOld = atlasTex.GetPixels();
            if (nWidth <= nMinSize && nHeight <= nMinSize)
                return;

            //int nLodW = nWidth > nMinSize ? GetTextureExSize(nWidth): nWidth;
            //int nLodH = nHeight > nMinSize ? GetTextureExSize(nHeight) : nHeight;        
            int nLodW = nWidth > nMinSize ? nWidth / 2 : nWidth;
            int nLodH = nHeight > nMinSize ? nHeight / 2 : nHeight;
            int nScaleW = nWidth / nLodW;
            int nScaleH = nHeight / nLodH;
            Texture2D texLOD = new Texture2D(nLodW, nLodH);
            Color[] PixelNew = new Color[nLodW * nLodH];
            int nSrcI = 0;
            int nDesI = 0;
            for (int nRow = 0; nRow < nLodH; ++nRow)
            {
                for (int nCol = 0; nCol < nLodW; ++nCol, ++nDesI)
                {
                    //nSrcI = nRow * nWidth * nHeight / nLodH + nCol * nWidth / nLodW;
                    nSrcI = nRow * nWidth * nScaleH + nCol * nScaleW;
                    PixelNew[nDesI] = PixelOld[nSrcI];
                }
            }
            texLOD.SetPixels(PixelNew);
            texLOD.Apply();

            string szLodAtlasPathName = szAssetPath + szAtlasName + "_L.png";

            bool bFindFile = false;
            if (System.IO.File.Exists(szLodAtlasPathName))
            {
                bFindFile = true;
                System.IO.FileAttributes newPathAttrs = System.IO.File.GetAttributes(szLodAtlasPathName);
                newPathAttrs &= ~System.IO.FileAttributes.ReadOnly;
                System.IO.File.SetAttributes(szLodAtlasPathName, newPathAttrs);
            }
            byte[] bytes = texLOD.EncodeToPNG();
            if (bytes == null)
            {
                return;
            }
            System.IO.File.WriteAllBytes(szLodAtlasPathName, bytes);
            bytes = null;
            if (!bFindFile && bChangeFormat)
            {
                AssetDatabase.Refresh();
                ChangeTextureFormat(szLodAtlasPathName);
            }
        }

        /// <summary>
        /// Convert from bottom-left based UV coordinates to top-left based pixel coordinates.
        /// </summary>

        static public Rect ConvertToPixels(Rect rect, int width, int height, bool round)
        {
            Rect final = rect;

            if (round)
            {
                final.xMin = Mathf.RoundToInt(rect.xMin * width);
                final.xMax = Mathf.RoundToInt(rect.xMax * width);
                final.yMin = Mathf.RoundToInt((1f - rect.yMax) * height);
                final.yMax = Mathf.RoundToInt((1f - rect.yMin) * height);
            }
            else
            {
                final.xMin = rect.xMin * width;
                final.xMax = rect.xMax * width;
                final.yMin = (1f - rect.yMax) * height;
                final.yMax = (1f - rect.yMin) * height;
            }
            return final;
        }

        // 功能：添加或更新当前选择精灵对象
        // 参数：nameList - 要更新的精灵对象
        public bool AddOrUpdateSelectSprite(List<SUpdateTexInfo> nameList, bool bTrimAlpha, bool bAtlasPMA, bool bCanLOD)
        {
            //if (!IsCanSave())
            //    return false;

            Dictionary<string, UpdateTexNameList> aSelectTex = GetSelectedTexInfo(nameList);

            Dictionary<string, int> aDirtyAtlas = new Dictionary<string, int>();

            Dictionary<string, UpdateTexNameList>.Enumerator it = aSelectTex.GetEnumerator();

            // 先修改精灵材质
            while (it.MoveNext())
            {
                string szAtlasName = it.Current.Key;
                List<SelecctTexInfo> selectList = it.Current.Value.nameList;

                aDirtyAtlas[szAtlasName] = 1;

                foreach (SelecctTexInfo o in selectList)
                {
                    if (o.m_sprite != null && szAtlasName != o.m_sprite.m_szAtlasName)
                    {
                        o.m_sprite.m_szAtlasName = szAtlasName;
                    }
                }
            }
            string szShaderName = "HUDProgramme/TransparentColoredMainAlpha";

            bool bCfgDirty = false;
            it = aSelectTex.GetEnumerator();
            while (it.MoveNext())
            {
                string szAtlasName = it.Current.Key;
                List<SelecctTexInfo> selectList = it.Current.Value.nameList;

                // 如果当前材质不存在，就新建一个材质吧
                if (GetAltas(szAtlasName) == null)
                {
                    NewAtlas(szAtlasName, szShaderName, bCanLOD);
                }

                // 再更新当前的吧
                bool bReplace = false;
                if (UpdateAltasBySelect(ref selectList, szAtlasName, bTrimAlpha, ref bReplace))
                {
                    if (!bReplace)
                        bCfgDirty = true;
                    // 添加对象吧
                    int nCount = selectList.Count;
                    for (int i = 0; i < nCount; ++i)
                    {
                        m_AllSprite[selectList[i].m_szSpriteName] = selectList[i].m_sprite;
                    }
                    if (aDirtyAtlas.ContainsKey(szAtlasName))
                        aDirtyAtlas.Remove(szAtlasName);
                }
            }

            Dictionary<string, int>.Enumerator itDirty = aDirtyAtlas.GetEnumerator();
            while (itDirty.MoveNext())
            {
                // 更新材质吧
                if (itDirty.Current.Value == 1)
                    UpdateAtlasTexture(itDirty.Current.Key, false, bTrimAlpha);
            }
            if (bCfgDirty)
                SaveAltasCfg();
            return true;
        }

        // 功能：仅仅是更新材质贴图
        bool UpdateAtlasTexture(string szAtlasName, bool bForceNew = false, bool bTrimAlpha = false)
        {
            UITexAtlas atlas = GetAltas(szAtlasName);
            List<UISpriteInfo> aSprite = GetAllSpriteByAtlas(szAtlasName);
            string szAtlasPathName = string.Format(HUDConfig.szAtlasPathName, szAtlasName);
            Texture2D atlasTex = LoadCanWriteTextureByAssetsName(szAtlasPathName);

            // 创建一个空的吧
            if (aSprite == null || aSprite.Count == 0)
            {
                Color32[] newPixels = new Color32[1];
                newPixels[0] = new Color32(0, 0, 0, 0);
                atlasTex = new Texture2D(1, 1);
                atlasTex.SetPixels32(newPixels);
                atlasTex.Apply();
                return true;
            }
            if (atlasTex == null)
            {
                //atlasTex = new Texture2D;
                // 搜索所有的文件, 这是一个错误, 就不搜了
                return false;
            }
            else
            {
                List<SelecctTexInfo> aSelectTex = new List<SelecctTexInfo>();
                List<Texture2D> aTex = new List<Texture2D>();
                foreach (UISpriteInfo sp in aSprite)
                {
                    Texture2D tex = GetSpriteTex(atlasTex, sp, true, bTrimAlpha);
                    SelecctTexInfo info = new SelecctTexInfo();
                    info.m_sprite = sp;
                    info.m_szAssetsName = szAtlasName;
                    info.m_szSpriteName = sp.name;
                    info.m_tex = tex;
                    aSelectTex.Add(info);
                    aTex.Add(tex);
                }
                if (bForceNew)
                    return UpdateAltasBySelect(aSelectTex, aTex, atlas, null, szAtlasPathName);
                else
                    return UpdateAltasBySelect(aSelectTex, aTex, atlas, atlasTex, szAtlasPathName);
            }
            return true;
        }

        // 功能：保存配置在编辑器模式下
        public void SaveAltasCfg()
        {
            ++m_nFileVersion;
            MakeSpriteAtlasID();
            string szCfgPathName = HUDConfig.szCfgPathName;//Application.dataPath + "/Atlas/assets_all.txt";
            SerializeText ar = new SerializeText(SerializeType.write, szCfgPathName);
            SerializeToTxt(ar);
            ar.Close();
            string szBinPathName = HUDConfig.szCfgBytePathName;//Application.dataPath + "/Atlas/assets_all.bytes";
            CSerialize arBin = new CSerialize(SerializeType.write, szBinPathName);
            Serialize(arBin);
            arBin.Close();
        }
#endif
        public Texture GetAtlasTexture(string tex_name)
        {
            Texture texture = null;
#if UNITY_EDITOR
            string path = string.Format(HUDConfig.szAtlasPathName, tex_name);
            texture = AssetDatabase.LoadAssetAtPath<Texture>(path);
#endif
            return texture;
        }

    }
}



