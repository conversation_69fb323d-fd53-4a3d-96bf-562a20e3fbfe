DUJIE_VIEW_STATE = 
{
    MAIN = 0,
    DUJIE = 1,
    <PERSON><PERSON><PERSON>IE_ING = 2,
    ANGER_SKILL = 3,
}

local STAGE_TYPE_MAX  = 7
local CG_STAGE_TYPE = 
{
    MAIN_IDLE = 1,
    MAIN_TO_DUJIE = 2,
    <PERSON><PERSON><PERSON>IE_IDLE = 6,
    <PERSON><PERSON><PERSON><PERSON>_TO_MAIN = 7,
    MAIN_TO_ANGER_SKILL = 3,
    ANGER_SKILL_IDLE = 4,
    ANGER_SKILL_TO_MAIN = 5,
}

local WARNING_MAX  = 2
local THUNDER_MAX  = 3
local HIT_MAX  = 2
local SKILL_MAX  = 3
local SPIRIT_MAX  = 3
local MAIN_TO_ANGER_SKILL_TIME = 2


DujieView = DujieView or BaseClass(SafeBaseView)
function DujieView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg()
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self.view_cache_time = 0
    self.full_screen = false
    self.is_safe_area_adapter = true

    self.is_cg_loaded = false

	self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_main_view")
	self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_view")
    self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_spirit_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
    -- 功能引导绑定事件
    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)

    self:ChangeStage(DUJIE_VIEW_STATE.MAIN)

    local data = {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.CG}
    self:SetTabShowUIScene(0, data)
end

function DujieView:SetInitViewState(view_state)
    self.init_view_state = view_state
end

function DujieView:__delete()
    -- do
end

function DujieView:OpenCallBack()
    DujieWGCtrl.Instance:StopBgMusic()
end

function DujieView:StartDuJieCg()

	local bundle = "cg/a3_cg_dujie/new_system_prefab"
	local target_asset = "A3_CG_DJSystem_Nan"
    local role_sex, role_prof = RoleWGData.Instance:GetRoleSexProf()
    if role_sex == GameEnum.FEMALE then
        target_asset = "A3_CG_DJSystem_Nv"
    end
    self.dujie_cg = UICg.New(bundle, target_asset)
    
    self:PlayDujieCG()
    -- 登录后端会推
    -- DujieWGCtrl.Instance:GetDujieBaseInfo()
end

function DujieView:CloseCallBack()
    self.init_view_state = nil
    self.cur_cg_stage = nil
    self.view_state = DUJIE_VIEW_STATE.MAIN
    self:CleanAllTimer()
    self:CleanDujieingTimer()
    DujieWGCtrl.Instance:RePlayMusic()

    if self.dujie_cg then
        self.dujie_cg:OnPlayEnd()
        self.dujie_cg = nil
        self.is_cg_loaded = false
    end
    
    -- CgManager.Instance:OnPlayEnd()
end

function DujieView:LoadCallBack()

    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickBtnClose, self))
    XUI.AddClickEventListener(self.node_list.btn_body, BindTool.Bind(self.OnClickBtnBody, self))
    XUI.AddClickEventListener(self.node_list.btn_goto_dujie, BindTool.Bind(self.OnClickBtnGotoDujie, self))
    XUI.AddClickEventListener(self.node_list.btn_anger_skill, BindTool.Bind(self.OnClickBtnAngerSkill, self))
    -- XUI.AddClickEventListener(self.node_list.btn_rule_tips, BindTool.Bind(self.OnClickBtnTips, self))

    
    self:StartDuJieCg()
    -- 注册功能引导
    -- FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.BiZuo, self.get_guide_ui_event)

    self:LoadDujieCallBack()
    self:LoadSpiritCallBack()

    
end

function DujieView:ReleaseCallBack()


    -- 注销功能引导
    -- FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.XXX, self.get_guide_ui_event)
    self:ReleaseDujieView()
    self:ReleaseSpiritView()
    self:RemoveCommonTopDelayTimer()
    -- if self.dujie_cg then
    --     self.dujie_cg:DeleteMe()
    -- end
end

function DujieView:LoadIndexCallBack(index)

end

function DujieView:OnClickRuleTips()
    if self.view_state == DUJIE_VIEW_STATE.ANGER_SKILL then
        local cfg = ViewRuleWGData.Instance:GetViewRuleCfg(GuideModuleName.DujieView,10)
        RuleTip.Instance:SetContent(cfg.rule_content, cfg.rule_title)
    else
        local cfg = ViewRuleWGData.Instance:GetViewRuleCfg(GuideModuleName.DujieView,0)

        RuleTip.Instance:SetContent(cfg.rule_content, cfg.rule_title)
    end
    
end


function DujieView:ShowIndexCallBack(index)
    DujieWGData.Instance:SetIsNeedOpenDujie(false)
    self.node_list.mainLayer:SetActive(true)
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Dujie_Hong, true, true))
    self.node_list.dujieLayer:SetActive(false)
    self.node_list.btn_rule_tips:SetActive(true)
    self.node_list.dujie_spirit_root:CustomSetActive(false)

    if self.init_view_state == DUJIE_VIEW_STATE.DUJIE then
        self:ChangeStage(DUJIE_VIEW_STATE.DUJIE)
        self.node_list.dujieLayer:SetActive(false)
        self.node_list.mainLayer:SetActive(false)
        -- self:HideMainBtn(0)
        -- self:HideDujie(0)

        self:SetStageShow(CG_STAGE_TYPE.DUJIE_IDLE)
        self.init_view_state = nil
    end
end

function DujieView:OnFlush(param_t, index)

    for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushAllView(param_t)
            self:FlushRemind()

            if v.open_param ~= nil and v.item_id ~= nil then
                if CultivationWGData.Instance:GetIsRoleNuqiActiveItem(v.item_id) then
                    self:OnClickBtnAngerSkill(true)
                    self:DuJieSpiritFlush(v.open_param)
                end
            end
        elseif k == "flush_hp" then

        elseif k == "start_dujie" then
            self:StartDujie()
        elseif k == "end_dujie" then
            -- 渡劫结束
            self:CleanDujieingTimer()
            self:ChangeStage(DUJIE_VIEW_STATE.DUJIE)
            self:FlushDujieView()


        elseif k == "flush_dujie_ing" then
            self:FlushDujieingView()
            
        elseif k == "show_thunder" then
            -- print_error("雷电出现")
            if self.next_thunder_type then
                self:SetThunderEffectShow(self.next_thunder_type)
            end
            if v.harm_num > 0 then
                self:HarmAnim(v.harm_num)
            end
            

            -- local old_data = self.data:GetOldOrdealInfo()
            -- self.next_thunder_index = old_data.next_thunder_index
        elseif k == "show_skill" then
            local ordeal_info = DujieWGData.Instance:GetOrdealInfo()
            local ordeal_base_info = DujieWGData.Instance:GetOrdealBaseInfo()
            
            self:CleanSkillShowTimer()

            local server_time = TimeWGCtrl.Instance:GetServerTime()
            local skill_cfg = DujieWGData.Instance:GetSkillCfg(ordeal_base_info.level + 1, ordeal_info.skill_seq)
            if nil == skill_cfg then
                return
            end
            local duration_time = ordeal_info.skill_active_time + (skill_cfg.duration_ms/1000) - server_time

            if duration_time > 0 then
                self.skill_show_timer = GlobalTimerQuest:AddTimesTimer(function ()
                    -- print_error("技能结束")
                    self:SetSkillEffectShow(nil,false)
                end, duration_time , 1)
                self:SetSkillEffectShow(ordeal_info.skill_seq + 1, true)
                -- print_error("技能剩余时间",duration_time)
                -- print_error("seq",ordeal_info.skill_seq + 1)
            end

        elseif k == "flush_spirit" then
            self:DuJieSpiritFlush(param_t)
            self:FlushRemind()
        elseif k == "flush_remind" then
            self:FlushRemind()
        elseif k == "open_dujie" then
            self:HideMainBtn(0)
            self:ChangeStage(DUJIE_VIEW_STATE.DUJIE)
            self:ShowDujieLayer(2)
            self:SetStageShow(CG_STAGE_TYPE.DUJIE_IDLE)
            self:FlushDujieView()

        end
    end
end

function DujieView:FlushAllView(param_t)
    if self.view_state == DUJIE_VIEW_STATE.DUJIE then
        self:FlushDujieView()
        
    elseif self.view_state == DUJIE_VIEW_STATE.MAIN then
        self:FlushMainDujieBar()
    elseif self.view_state == DUJIE_VIEW_STATE.ANGER_SKILL then
        self:DuJieSpiritFlush(param_t)
    end
end


function DujieView:PlayDujieCG()

    self.dujie_cg:Play(function ()
        self.dujie_cg:DeleteMe()
        self.dujie_cg = nil
        Scene.Instance:UpdateSceneCullingFactor()
        -- Scene.Instance:SetWeatherState(true)
        Scene.Instance:UpdateSceneQuality(SCENE_QUALITY_TYPE.WEATHER, true)

        if self.cg_end_call_back then
            self.cg_end_call_back()
            self.cg_end_call_back = nil
        end
    end, 
    function ()
        Scene.Instance:UpdateSceneQuality(SCENE_QUALITY_TYPE.WEATHER, false)

    end, nil, nil, true, true,
    function()
        self.is_cg_loaded = true
        self.node_list.layout_a3_common_top_panel:SetActive(true)
        if self.cur_cg_stage == CG_STAGE_TYPE.MAIN_TO_DUJIE then
            self:SetStageShow(CG_STAGE_TYPE.DUJIE_IDLE)
        elseif self.cur_cg_stage == CG_STAGE_TYPE.DUJIE_TO_MAIN then
            self:SetStageShow(CG_STAGE_TYPE.MAIN_IDLE)
        elseif self.cur_cg_stage == CG_STAGE_TYPE.MAIN_TO_ANGER_SKILL then
            self:SetStageShow(CG_STAGE_TYPE.ANGER_SKILL_IDLE)        
        elseif self.cur_cg_stage == CG_STAGE_TYPE.ANGER_SKILL_TO_MAIN then
            self:SetStageShow(CG_STAGE_TYPE.MAIN_IDLE)
        end
    end, 
    self.node_list.cg_node)
end



function DujieView:HideMainBtn(time)
    local init_value = 750
    self.node_list.mainLayer.canvas_group:DoAlpha(1, 0, time or 0.5)
    self.node_list.mainLayer.canvas_group.blocksRaycasts = false
end

function DujieView:ShowMainBtn(time)
    self.node_list.mainLayer.canvas_group:DoAlpha(0, 1, time or 0.5)
    self.node_list.mainLayer.canvas_group.blocksRaycasts = true
end

function DujieView:HideDujieLayer(time)
    self.node_list.dujieLayer.canvas_group:DoAlpha(1, 0, time or 0.5)
    self.node_list.dujieLayer.canvas_group.blocksRaycasts = false
end

function DujieView:ShowDujieLayer(time)
    self.node_list.dujieLayer.canvas_group:DoAlpha(0, 1, time or 0.5)
    self.node_list.dujieLayer.canvas_group.blocksRaycasts = true
    self.node_list.dujieLayer:SetActive(true)
end

function DujieView:FlushRemind()
    self.node_list.img_remind_body:SetActive(DujieWGData.Instance:GetBodyRemind() > 0)
    self.node_list.img_remind_spirit:SetActive(CultivationWGData.Instance:GetAngerSpiritRemind() > 0)
    
    -- 锁图标放大了1.2倍 秦亮要求的 说过不听
    self.node_list.img_lock_anger_skill:SetActive( not CultivationWGData.Instance:IsOpenNuQi())
    self.node_list.img_lock_body:SetActive( not DujieWGData.Instance:IsActiveBody())
    -- 不用渐变是和多个动画冲突了
    -- self.node_list.btn_anger_skill.canvas_group.alpha = DragonTrialWGData.Instance:IsCanActivate() and 1 or 0.5
    -- self.node_list.btn_body.canvas_group.alpha = DujieWGData.Instance:IsActiveBody() and 1 or 0.5

    self.node_list.img_remind_dujie:SetActive(DujieWGData.Instance:IsCanDujie())
end


function DujieView:OnClickBtnGotoDujie()
    if self:IsToStage(self.cur_cg_stage) then
        return
    end

    self:HideMainBtn()
    self:ChangeStage(DUJIE_VIEW_STATE.DUJIE)
    self.node_list.dujieLayer:SetActive(true)
    -- self.node_list.mainLayer:SetActive(false)

    -- 播放特效
    if self.is_cg_loaded then
        self.dujie_cg:SetNodeVisible("shandian_1",false)
        self.dujie_cg:SetNodeVisible("shandian_1",true)
    end

    -- 切换状态
    self:SetStageShow(CG_STAGE_TYPE.MAIN_TO_DUJIE)
    self:FlushDujieView()
end

function DujieView:OnClickBtnBody()
    if DujieWGData.Instance:IsActiveBody() then 
        DujieWGCtrl.Instance:OpenDujieBodyView()

    else
        -- local limit_level = DujieWGData.Instance:GetOtherCfg("body_limit_level")

        -- local level_cfg = DujieWGData.Instance:GetLevelCfg(tonumber(limit_level))
        -- local type_str = DujieWGData.Instance:GetTypeStr(level_cfg.type)

        -- local limit_str = type_str..DujieWGData.Instance:GetLevelIndexStr(level_cfg.level)
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.SpiritLimitStr)
        return
    end

    
end

function DujieView:OnClickBtnAngerSkill(is_open_init)
    if not CultivationWGData.Instance:IsOpenNuQi() then
        ViewManager.Instance:Open(GuideModuleName.DragonTrialView)
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.UnlockAngerSkill)
        return
    end

    if self:IsToStage(self.cur_cg_stage) then
        return
    end
    self:HideMainBtn()
    self:ChangeStage(DUJIE_VIEW_STATE.ANGER_SKILL)
    -- self.node_list.mainLayer:SetActive(false)
    if is_open_init and not self.is_cg_loaded then -- 处理跳转时cg来不及加载完
        self.cur_cg_stage = CG_STAGE_TYPE.MAIN_TO_ANGER_SKILL
    else
        self:SetStageShow(CG_STAGE_TYPE.MAIN_TO_ANGER_SKILL)
    end
    self.node_list.dujie_spirit_root:CustomSetActive(true)
    self:DuJieSpiritFlush()
end

-- 点击返回
function DujieView:OnClickBtnClose()
    -- 主要界面
    if self.view_state == DUJIE_VIEW_STATE.MAIN then
        self:Close()
    -- 渡劫界面
    elseif self.view_state == DUJIE_VIEW_STATE.DUJIE then
        self:ChangeStage(DUJIE_VIEW_STATE.MAIN)
        self.node_list.dujieLayer:SetActive(false)
        self.node_list.mainLayer:SetActive(true)
        self:SetStageShow(CG_STAGE_TYPE.DUJIE_TO_MAIN)
        self:FlushMainDujieBar()
    -- 怒气界面
    elseif self.view_state == DUJIE_VIEW_STATE.ANGER_SKILL then
        self:ChangeStage(DUJIE_VIEW_STATE.MAIN)
        self.node_list.dujie_spirit_root:CustomSetActive(false)
        self.node_list.mainLayer:SetActive(true)
        self:SetSpiritEffectShow(nil, false)
        self:SetStageShow(CG_STAGE_TYPE.ANGER_SKILL_TO_MAIN)
        self:DuJieSpiritClose()
        self:FlushMainDujieBar()
    -- 渡劫中 弹出提示
    elseif self.view_state == DUJIE_VIEW_STATE.DUJIE_ING then
        local next_cfg = DujieWGData.Instance:GetNextDujieCfg()
        local time_show_str = TimeUtil.FormatSecondDHM7(next_cfg.fail_damage_time)
        TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Dujie.CannelTips, time_show_str or 0), function ()
            DujieWGCtrl.Instance:DujieCannel()
        end)
    end
end

function DujieView:ChangeStage(view_state)
    self.view_state = view_state
    if view_state ~= DUJIE_VIEW_STATE.DUJIE then
        self:CleanFailTimer()
    elseif view_state ~= DUJIE_VIEW_STATE.DUJIE_ING then
        self:CleanDujieStarTimer()
        if self.node_list.thunder_group then
            self.node_list.thunder_group:SetActive(false)
        end
    end
end

function DujieView:IsToStage(index)
    if (index == CG_STAGE_TYPE.ANGER_SKILL_TO_MAIN  or index == CG_STAGE_TYPE.DUJIE_TO_MAIN) then
        return true
    end
    return false
end

function DujieView:FlushMainDujieBar()
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()
    if IsEmptyTable(base_info) then
        return
    end 
    local index = DujieWGData.Instance:GetLevelIndex(base_info.level)
    local cur_level_cfg = DujieWGData.Instance:GetLevelCfg(base_info.level)
    local index_max = DujieWGData.Instance:GetTypeMax(cur_level_cfg.type, cur_level_cfg.type_seq)

    -- 阶段图片
    local stage_asset_1, jinjie_asset_1 = ResPath.GetRawImagesPNG("a3_dujie_stage_"..cur_level_cfg.type)
    self.node_list.img_level_dujie_main.raw_image:LoadSprite(stage_asset_1, jinjie_asset_1, function ()
        self.node_list.img_level_dujie_main.raw_image:SetNativeSize()
    end)
    local stage_asset_2, jinjie_asset_2 = ResPath.GetRawImagesPNG("a3_dujie_stage_"..(cur_level_cfg.type + 1))
    self.node_list.img_next_level_dujie_main.raw_image:LoadSprite(stage_asset_2, jinjie_asset_2, function ()
        self.node_list.img_next_level_dujie_main.raw_image:SetNativeSize()
    end)

    -- if cur_level_cfg.type_seq == 0 then
    --     self.node_list.text_level_dujie_main.text.text = cur_level_cfg.type
    --     -- self.node_list.img_level_dujie_main:SetActive(true)
    -- else
    --     -- self.node_list.img_level_dujie_main:SetActive(false)
    --     self.node_list.text_level_dujie_main.text.text = cur_level_cfg.type.."·"..NumberToChinaNumber(cur_level_cfg.type_seq)
    -- end

    local next_level_cfg = DujieWGData.Instance:GetLevelCfg(base_info.level + 1 )
    if IsEmptyTable(next_level_cfg)  then
        next_level_cfg = cur_level_cfg
    end
    if cur_level_cfg.type == next_level_cfg.type then
        self.node_list.text_next_level_dujie_main.text.text = cur_level_cfg.type.."·"..NumberToChinaNumber(cur_level_cfg.type_seq +1)
    else
        self.node_list.text_next_level_dujie_main.text.text = next_level_cfg.type.."·"..NumberToChinaNumber(next_level_cfg.type_seq)
    end
    
    

    self.node_list.img_node_end_main:SetActive(index == index_max)

    -- local target_fillAmount = index/index_max
    local is_can_dujie = DujieWGData.Instance:IsCanDujie()
    self.node_list.img_bar_dujie_main.image.fillAmount = is_can_dujie and 1 or 0 

    -- 多段显示
    -- local type_max = DujieWGData.Instance:GetTypeMax(cur_level_cfg.type, cur_level_cfg.type_seq) 
    -- local index = DujieWGData.Instance:GetLevelIndex(base_info.level)
    -- local type_str = DujieWGData.Instance:GetTypeStr(cur_level_cfg.type)
    -- local type_seq_str = NumberToChinaNumber(cur_level_cfg.type_seq == 0 and 1 or cur_level_cfg.type_seq)
    -- self.node_list.text_dujie_level_main.tmp.text = string.format(Language.Dujie.SkyLevleStr2, type_str,type_seq_str, index, type_max)
    -- 一段显示
    local type_str = DujieWGData.Instance:GetTypeStr(cur_level_cfg.type)
    self.node_list.text_dujie_level_main.tmp.text = string.format(Language.Dujie.SkyLevleStr3, type_str)
    
    local desc = DujieWGData.Instance:GetNextOpenFuncDesc(base_info.level)
    self.node_list.text_next_unlock_main.tmp.text = desc
    self.node_list.text_next_unlock_main:SetActive(desc ~= "")

    self.node_list.img_level_main.rect.sizeDelta = Vector2(452, desc == "" and 30 or 64 )
end

-------------------------操作cg-----------------------
-- 显示cg状态
function DujieView:SetStageShow(index)
    if self:IsToStage(index) and self:IsToStage(self.cur_cg_stage) then
        return
    end

    if self:IsToStage(index) or index == CG_STAGE_TYPE.MAIN_TO_ANGER_SKILL or index == CG_STAGE_TYPE.MAIN_TO_DUJIE then
        self.node_list.layout_a3_common_top_panel:SetActive(false)

        if index == CG_STAGE_TYPE.MAIN_TO_ANGER_SKILL or index == CG_STAGE_TYPE.MAIN_TO_DUJIE then
            self:RemoveCommonTopDelayTimer()
            self.show_common_top_timer = GlobalTimerQuest:AddDelayTimer(function ()
                self.node_list.layout_a3_common_top_panel:SetActive(true)
            end, MAIN_TO_ANGER_SKILL_TIME)
        end
    end
    self.cur_cg_stage = index
    
    if index == CG_STAGE_TYPE.MAIN_TO_ANGER_SKILL or index == CG_STAGE_TYPE.ANGER_SKILL_TO_MAIN or index == CG_STAGE_TYPE.ANGER_SKILL_IDLE then
        self:FlushSpiritCGModelTrack()
    end

    if index == CG_STAGE_TYPE.MAIN_IDLE then
        self:ShowMainBtn()
    end

    if self.is_cg_loaded then
        for i = 1, STAGE_TYPE_MAX do
            self.dujie_cg:SetNodeVisible("stage"..i,index==i)
        end
        
        self.dujie_cg:SetNodeVisible("guang",index~=CG_STAGE_TYPE.MAIN_TO_ANGER_SKILL and index ~= CG_STAGE_TYPE.ANGER_SKILL_IDLE and index ~= CG_STAGE_TYPE.ANGER_SKILL_TO_MAIN)
        self.dujie_cg:SetCurTimePoint(0)
    end


    -- self.node_list.btn_rule_tips:SetActive(index==CG_STAGE_TYPE.MAIN_IDLE or index == CG_STAGE_TYPE.DUJIE_IDLE)
end

--移除回调
function DujieView:RemoveCommonTopDelayTimer()
    if self.show_common_top_timer then
        GlobalTimerQuest:CancelQuest(self.show_common_top_timer)
        self.show_common_top_timer = nil
    end
end

-- 显示预警
function DujieView:SetWarningEffectShow(type)
    if not self.is_cg_loaded then
        return
    end
    for i = 1, WARNING_MAX do
        self.dujie_cg:SetNodeVisible("yujing_"..i, false)
    end
    if type >= 3 then
        type = 2
    else
        type = 1
    end
    self.dujie_cg:SetNodeVisible("yujing_"..type, true)
end

-- 显示雷电
function DujieView:SetThunderEffectShow(index)
    if not self.is_cg_loaded then
        return
    end
    for i = 1, THUNDER_MAX do
        self.dujie_cg:SetNodeVisible("shandian_"..i, false)
    end
    for i = 1, HIT_MAX do
        self.dujie_cg:SetNodeVisible("hit_"..i, false)
    end

    if index == 4 then
        index = 3
    end
    
    self.dujie_cg:SetNodeVisible("shandian_"..index, true)
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(index %2 == 1 and AudioUrl.Dujie_Jin or AudioUrl.Dujie_Hong, true, true))

    -- local hit_index = index %2 == 0 and 2 or 1
    -- self.dujie_cg:SetNodeVisible("hit_"..hit_index, true)

end

-- 显示技能
function DujieView:SetSkillEffectShow(index,is_show)
    if not self.is_cg_loaded then
        return
    end
    for i = 1, SKILL_MAX do
        self.dujie_cg:SetNodeVisible("hudun_"..i, false)
    end
    if is_show then
        self.dujie_cg:SetNodeVisible("hudun_"..index, true)
    end
end

-- 显示元神特效
function DujieView:SetSpiritEffectShow(index,is_show)
    if not self.is_cg_loaded then
        return
    end
    for i = 0, SPIRIT_MAX - 1 do
        self.dujie_cg:SetNodeVisible("spirit_"..i, false)
    end
    if is_show then
        self.dujie_cg:SetNodeVisible("spirit_"..index, true)
    end
    
end


-------------------- 倒计时--------------------------------

function DujieView:CleanAllTimer()
    self:CleanFailTimer()
    self:CleanFailReduceTimer()
    self:CleanDujieStarTimer()
    self:CleanWarningTimer()
    self:CleanThunderTimer()
end

function DujieView:CleanDujieingTimer()
    self:CleanDujieStarTimer()
    self:CleanWarningTimer()
    self:CleanThunderTimer()
    self:CleanSkillShowTimer()
    self:CleanSkillCDTimer()
    self:CleanHpAnimTimer()
end

function DujieView:CleanFailTimer()
    if self.fail_timer and CountDown.Instance:HasCountDown(self.fail_timer) then
        CountDown.Instance:RemoveCountDown(self.fail_timer)
        self.fail_timer = nil
    end
end

function DujieView:CleanFailReduceTimer()
    if self.fail_reduce_timer and CountDown.Instance:HasCountDown(self.fail_reduce_timer) then
        CountDown.Instance:RemoveCountDown(self.fail_reduce_timer)
        self.fail_reduce_timer = nil
    end
end



function DujieView:CleanDujieStarTimer()
    if self.dujie_star_timer and CountDown.Instance:HasCountDown(self.dujie_star_timer) then
        CountDown.Instance:RemoveCountDown(self.dujie_star_timer)
        self.dujie_star_timer = nil
    end
end

function DujieView:CleanWarningTimer()
    if self.warning_thunder_timer then
        GlobalTimerQuest:CancelQuest(self.warning_thunder_timer)
        self.warning_thunder_timer = nil
    end
end

function DujieView:CleanThunderTimer()
    if self.thunder_timer then
        GlobalTimerQuest:CancelQuest(self.thunder_timer)
        self.thunder_timer = nil
    end
end

function DujieView:CleanSkillShowTimer()
    if self.skill_show_timer then
        GlobalTimerQuest:CancelQuest(self.skill_show_timer)
        self.skill_show_timer = nil
    end
    self:SetSkillEffectShow(nil,false)
end

function DujieView:CleanSkillCDTimer(index)
    if nil == index then
        for i = 1, 3 do
            if self.skill_cd_timer_list[i] and CountDown.Instance:HasCountDown(self.skill_cd_timer_list[i]) then
                CountDown.Instance:RemoveCountDown(self.skill_cd_timer_list[i])
                self.skill_cd_timer_list[i] = nil
            end
        end
    else
        
        if self.skill_cd_timer_list[index]and CountDown.Instance:HasCountDown(self.skill_cd_timer_list[index]) then
            CountDown.Instance:RemoveCountDown(self.skill_cd_timer_list[index])
            self.skill_cd_timer_list[index] = nil
        end
    end
end

function DujieView:CleanHpAnimTimer()
    if self.hp_anim_timer and CountDown.Instance:HasCountDown(self.hp_anim_timer) then
        CountDown.Instance:RemoveCountDown(self.hp_anim_timer)
        self.hp_anim_timer = nil
    end
end

function DujieView:OnClickBtnTips()
    RuleTip.Instance:SetContent(Language.Dujie.RuleContent, Language.Dujie.RuleTitle)
end

-------------------- 倒计时 end--------------------------------


