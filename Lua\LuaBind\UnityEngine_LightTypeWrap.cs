﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_LightTypeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>(typeof(UnityEngine.LightType));
		<PERSON><PERSON>("Spot", get_Spot, null);
		<PERSON><PERSON>("Directional", get_Directional, null);
		<PERSON><PERSON>("Point", get_Point, null);
		<PERSON><PERSON>("Area", get_Area, null);
		<PERSON><PERSON>("Rectangle", get_Rectangle, null);
		<PERSON><PERSON>("Disc", get_Disc, null);
		<PERSON><PERSON>Function("IntToEnum", IntToEnum);
		<PERSON><PERSON>();
		TypeTraits<UnityEngine.LightType>.Check = CheckType;
		StackTraits<UnityEngine.LightType>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.LightType arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.LightType), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Spot(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LightType.Spot);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Directional(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LightType.Directional);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Point(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LightType.Point);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Area(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LightType.Area);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Rectangle(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LightType.Rectangle);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Disc(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LightType.Disc);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.LightType o = (UnityEngine.LightType)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

