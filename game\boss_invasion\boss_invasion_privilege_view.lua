BOSSInvasionPrivilegeView = BOSSInvasionPrivilegeView or BaseClass(SafeBaseView)

function BOSSInvasionPrivilegeView:__init()
	self:SetMaskBg()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self:AddViewResource(0, "uis/view/boss_invasion_ui_prefab", "layout_boss_invasion_privilege_view")
end

function BOSSInvasionPrivilegeView:LoadCallBack()
	local private_data_list = BOSSInvasionWGData.Instance:GetPrivilegeDisplayCfg()

	for i = 1, 8 do
		local data = private_data_list[i]

		if IsEmptyTable(data) then
			self.node_list["skill_" .. i]:CustomSetActive(false)
		else
			self.node_list["skill_" .. i]:CustomSetActive(true)

			local bundle, asset = ResPath.GetSkillIconById(data.icon)
			self.node_list["skill_icon_" .. i].image:LoadSprite(bundle, asset, function ()
				self.node_list["skill_icon_" .. i].image:SetNativeSize()
			end)

			self.node_list["desc_skill_" .. i].text.text = data.description
			self.node_list["desc_skill_name_" .. i].text.text = data.name
		end
	end

    -- XUI.AddClickEventListener(self.node_list["btn_skill_show"], BindTool.Bind(self.ClickShowSKillBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_buy_privilege"], BindTool.Bind(self.ClickBuyPrivilegeBtn, self))

	if not self.attr_list then
		self.attr_list = {}

		for i = 1, 5 do
			self.attr_list[i] = CommonAttrRender.New(self.node_list["attr_" .. i])
		end
	end

	local other_cfg = BOSSInvasionWGData.Instance:GetOtherCfg()
	local attr_tab = string.split(other_cfg.rmb_attr, "|")
	local target_attr_tab = {}

	for k, v in pairs(attr_tab) do
		local attr_info = string.split(v, ",")
		table.insert(target_attr_tab, {attr_str = tonumber(attr_info[1]), attr_value = tonumber(attr_info[2])})
	end

	local attr_data = EquipWGData.GetSortAttrListByCfg(target_attr_tab)

	for i = 1, 5 do
		self.attr_list[i]:SetData(target_attr_tab[i])
	end

	if not self.reward_data_list then
		self.reward_data_list = AsyncListView.New(ItemCell, self.node_list.reward_data_list)
		self.reward_data_list:SetStartZeroIndex(true)
	end
end

function BOSSInvasionPrivilegeView:ReleaseCallBack()
	if self.attr_list then
		for k, v in pairs(self.attr_list) do
			v:DeleteMe()
		end

		self.attr_list = nil
	end

	if self.reward_data_list then
		self.reward_data_list:DeleteMe()
		self.reward_data_list = nil
	end
end

function BOSSInvasionPrivilegeView:OnFlush()
	local is_get_privilege = BOSSInvasionWGData.Instance:IsGetPrivilege()

	local other_cfg = BOSSInvasionWGData.Instance:GetOtherCfg()
	local price_str = RoleWGData.GetPayMoneyStr(other_cfg.rmb_price, other_cfg.rmb_type, other_cfg.rmb_seq)
    self.node_list.desc_bug_privilege.text.text = price_str

	self.node_list["btn_buy_privilege"]:CustomSetActive(not is_get_privilege)
	self.node_list["flag_get_privilege"]:CustomSetActive(is_get_privilege)

	self.reward_data_list:SetDataList(other_cfg.rmb_reward_item)
end

-- function BOSSInvasionPrivilegeView:ClickShowSKillBtn()
-- 	BOSSInvasionWGCtrl.Instance:OpenPrivilegeSkillShowView()
-- end

function BOSSInvasionPrivilegeView:ClickBuyPrivilegeBtn()
	-- 直购
	local is_get_privilege = BOSSInvasionWGData.Instance:IsGetPrivilege()
	local other_cfg = BOSSInvasionWGData.Instance:GetOtherCfg()

	if not is_get_privilege then
		local rmb_price, rmb_type, rmb_seq = other_cfg.rmb_price, other_cfg.rmb_type, other_cfg.rmb_seq
		RechargeWGCtrl.Instance:Recharge(rmb_price, rmb_type, rmb_seq)
	end
end