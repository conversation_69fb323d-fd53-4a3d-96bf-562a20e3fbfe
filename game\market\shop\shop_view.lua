--商店系统

------ 外部跳转示例 --------
-- 根据seq跳转商城                                              ShopWGCtrl:ShopJumpToItemBySeq(seq)
-- 根据item_id跳转商城（注意重复id跳转优先选择开启的靠前标签）   ShopWGCtrl:ShopJumpToItemByID(target_item_id)
-- 重复商品，用id和tabindex唯一定位                             ShopWGCtrl:ShopJumpToItemByIDAndTabIndex(target_item_id,tab_index)
-- 根据tabIndex跳转以及传参                                     ShopWGCtrl:OpenShopJumpToTabIndex(tab_index, key, values)

------ 外部跳转示例 --------
local GUIDE_CD = 0
local COL = 4
local MAX_VISIABLE_ROW = 2 --最多可视区域行数
function MarketView:MarketShopInit()
	self.is_modal = true

	self.grid_list = nil
	self.jump_percent = nil
	self.ver_tabbar_list = nil
	self.ver_toggle_list = nil
	self.select_ver_index = 0

	self.datachange_callback = BindTool.Bind1(self.OnItemDataChange, self)
	self.role_data_change_callback =  BindTool.Bind1(self.OnRoleDataChange, self)
end

function MarketView:SetDefaultJumpIndex(index)
	self.default_index = index
end

function MarketView:MarketShopReleaseCallBack()
	self.select_ver_index = 0
	self.ver_toggle_list = nil
	if self.ver_tabbar_list then
		for _,list in pairs(self.ver_tabbar_list) do
			for _,v in pairs(list) do
				v:DeleteMe()
			end
		end
		self.ver_tabbar_list = nil
	end

	if nil ~= self.grid_list then
		self.grid_list:DeleteMe()
		self.grid_list = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)
	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Market, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	self.role_attr_notify = nil
	self.data_notify = nil
	self.grid_scroller_fun = nil
end

function MarketView:MarketShopLoadCallBack()
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Market, self.get_guide_ui_event)
end

function MarketView:OnClickTopIcon(item_id)
	TipWGCtrl.Instance:OpenItem({item_id = item_id})
end

-- 背包数据发生变化通知
function MarketView:OnItemDataChange()

end

function MarketView:OnRoleDataChange(attr_name, value, old_value)
    if attr_name == "level" then
		self:Flush()
	end
end

function MarketView:UpdateChangeTab(index, is_flush, refresh)
	local data_list = ShopWGData.Instance:GetItemDataList(index % 10, is_flush)

	if self.grid_list == nil then
		local bundle = "uis/view/shop_ui_prefab"
		local asset = "cel_shop_item"

		self.grid_list = AsyncBaseGrid.New()
		self.grid_list:CreateCells({col = COL, change_cells_num = 1, list_view = self.node_list["ph_grid_list"],
				assetBundle = bundle, assetName = asset, itemRender = ShopItemRender})
		self.grid_list:SetStartZeroIndex(false)
		self.grid_list:SetDataList(data_list, 0)
	else
		refresh = refresh or 0
		self.grid_list:CancleAllSelectCell()
		self.grid_list:SetDataList(data_list)
	end

	self.grid_list:JumpToIndex(0)

	self.node_list["img_no"]:SetActive(0 == #data_list)
	if self.node_list["img_no"].gameObject.activeInHierarchy then
		local tab_cfg = ShopWGData.Instance:GetPageType(index)
		if tab_cfg then
			self.node_list["text_des"].text.text = string.format(Language.Shop.NoShopItem, tab_cfg.min_show_level)
		end
	end
end

--设置选中，因为当未生成完全的时候做选中会出现问题，并且有可能能受其他地方刷新界面导致选中问题
function MarketView:SetSelectItem()
	if nil == self.grid_list or nil == self.item_index then
		return
    end
    self.grid_list:SetSelectCellIndex(self.item_index)
    local cur = math.ceil(self.item_index / COL)
    local list = self.grid_list:GetDataList()
    local total = math.ceil(#list / COL)
    if total >= MAX_VISIABLE_ROW then
        local percent = 1 - (cur - MAX_VISIABLE_ROW) / (total - MAX_VISIABLE_ROW)
        percent = percent > 0 and percent or 0
        percent = percent < 1 and percent or 1
        self.node_list.ph_grid_list.scroll_rect.verticalNormalizedPosition = percent
    else
        self.node_list.ph_grid_list.scroll_rect.verticalNormalizedPosition = 1
    end
	self.item_index = nil
end

function MarketView:ItemIndex(value)
	if nil == value then
		return self.item_index
	end

	self.item_index = value
end

function MarketView:ClearItemIndex()
	self.item_index = nil
end

function MarketView:ClearJumpToPercent()
	self.jump_percent = nil
end

function MarketView:CalcShowIndex()
	self.default_index = ShopWGData.Instance:GetDefaultIndex()
	return SafeBaseView.CalcShowIndex(self)
end

function MarketView:MarketShopOpenCallBack()
	if RoleWGCtrl.Instance:GetRenameViewOpen() then
		self.mark_rename_view = true
		RoleWGCtrl.Instance:SetRenameViewActive(false)
	end
end

function MarketView:MarketShopLoadIndexCallBack(index)
	self:FlushRechargeRemind()

	if not self.role_attr_notify then
		RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"gold", "bind_gold", "coin", "shengwang", "level", "chivalrous"})
		self.role_attr_notify = true
	end
	if not self.data_notify then
		ItemWGData.Instance:NotifyDataChangeCallBack(self.datachange_callback)
		self.data_notify = true
	end

	if not self.grid_scroller_fun then
		self.grid_scroller_fun = BindTool.Bind(self.ShopScrollerEndScrolled, self)
		self.node_list.ph_grid_list.scroller.scrollerEndScrolled = self.grid_scroller_fun
	end
end

--tab页签的箭头显示.
function MarketView:ShopScrollerEndScrolled()
	if not self.node_list or not self.node_list.ph_grid_list or not self.grid_list or not self.node_list.shop_more_img then
		return
	end

	local val = self.node_list.ph_grid_list.scroll_rect.verticalNormalizedPosition
	local cell_row = self.grid_list:GetListViewNumbers()
	self.node_list.shop_more_img:SetActive(val ~= 0 and cell_row > MAX_VISIABLE_ROW and val > 0.1)
end

function MarketView:MarketShopShowIndexCallBack(index)
	ShopWGData.Instance:SetCurIndex(index)
	self:ClearItemIndex()
	self:SendShopItemInfo(index)
	self:SetRedPoint(index)
	self:CheckVerTabbar()
end

function MarketView:MarketShopCloseCallBack()
	self.to_ui_param = nil
	if self.mark_rename_view then
		self.mark_rename_view = false
		RoleWGCtrl.Instance:SetRenameViewActive(true)
	end
	self:ClearJumpToPercent()
	self:ClearItemIndex()
	GUIDE_CD = 0
	self:SetDefaultJumpIndex(ShopWGData.Instance:GetDefaultIndex())
	-- ShopWGData.Instance:SetCurIndex(ShopWGData.Instance:GetDefaultIndex())
end

function MarketView:MarketShopOnFlush(param_t, index)
	for i, v in pairs(param_t) do
		if i == "all" then
			local wait_index = ShopWGData.Instance:GetCurIndex()
			if wait_index ~= -1 then
				index = wait_index
			end
			self:UpdateChangeTab(index, true)
			self:ClearJumpToPercent()

		elseif i == "OnShopItemInfo" then
			self:UpdateChangeTab(self.show_index)
			self:ClearJumpToPercent()

			if self.check_jump_index_func then
				self.check_jump_index_func()
				self.check_jump_index_func = nil
			end 
			self:SetSelectItem()

		elseif i == "OnShopBuy" then
			self:ClearJumpToPercent()
			self:UpdateChangeTab(self.show_index, true, 2)

		elseif i == "select_item_id" then
			self.check_jump_index_func = function ()
				local list = self.grid_list:GetDataList()
				for t=1,#list do
					if v.item_id == list[t].itemid then
						self:ItemIndex(t)
						break
					end
				end
			end
		end
	end
end

function MarketView:CheckVerTabbar()
	local index = self:GetShowIndex()
	if self.select_ver_index ~= index and self.ver_toggle_list then
		local toggle_index = math.floor(index / 10)
		local tabbar_index = index % 10
		for _,v in ipairs(self.ver_toggle_list) do
			v.accordion_element.isOn = false
		end
		self.ver_toggle_list[toggle_index].accordion_element.isOn = true
		local tabbar_list = self.ver_tabbar_list[toggle_index]
		if tabbar_list and tabbar_list[tabbar_index] then
			tabbar_list[tabbar_index]:SetToggleState(true)
		end
	end
end

function MarketView:SendShopItemInfo(index)
	local shop_type = math.floor(index % 10)
	ShopWGCtrl.Instance:SendReqShopItemInfo(shop_type)
end

function MarketView:SetRedPoint(index)
	for k,v in pairs(ShopWGData.Instance.shop_hor_mind) do
		RemindManager.Instance:Fire(k)
	end
end

function MarketView:GetShopItem(item_id)
	if nil == self.grid_list then
		return nil, nil
	end
	for k,v in pairs(self.grid_list:GetAllCell()) do
		if v.data and v.data.itemid == item_id then
			if v.index > 7 then
				self.grid_list:ReloadData(1)
			end
			return v, BindTool.Bind(v.OnClick, v)
		end
	end
	return nil, nil
end

function MarketView:MarketShopGetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.ver_tabbar_list then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return NextGuideStepFlag
		end
	elseif ui_name == GuideUIName.ShopFunOpenItem then
		local tab_index = ShopWGData.Instance:GetShopTabNumIndex(tonumber(ui_param))
		if tab_index ~= self:GetShowIndex() then
			self:ChangeToIndex(tab_index)
			return
		end
		if GUIDE_CD < 10 then
			GUIDE_CD = GUIDE_CD + 1
			return
		end
		local shop_item, click_callback = self:GetShopItem(tonumber(ui_param))
		if shop_item ~= nil then
			return shop_item:GetView(), click_callback, shop_item.index % 2
		end
	elseif ui_name == GuideUIName.ShopCelShopItem then
		if TabIndex.shop_limit ~= self:GetShowIndex() then
			self:ChangeToIndex(TabIndex.shop_limit)
			return
		end
		-- if GUIDE_CD < 10 then
		-- 	GUIDE_CD = GUIDE_CD + 1
		-- 	return
		-- end
		local shop_item, click_callback = self:GetShopItem(tonumber(ui_param))
		if shop_item ~= nil then
			return shop_item:GetView(), click_callback, shop_item.index % 2
		end
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function MarketView:FlushRechargeRemind()
	if nil == self.node_list["recharge_btn_remind"] then
		return
	end
	self.node_list["recharge_btn_remind"]:SetActive(ShopWGData.Instance:IsShowRechargeRedPoint())
end