require("game/shanhaijing/shanhaijing_luoshence_wg_data")
require("game/shanhaijing/shanhaijing_luoshence_view")
require("game/shanhaijing/shanhaijing_luoshence_suitattr_view")

ShanHaiJingLSCWGCtrl = ShanHaiJingLSCWGCtrl or BaseClass(BaseWGCtrl)

function ShanHaiJingLSCWGCtrl:__init()
    if not self.luoshence_suitattr_view then
        self.luoshence_suitattr_view = ShanHaiJingLuoShenCeView.New()
    end

    ShanHaiJingLSCWGCtrl.Instance = self
    self.data = ShanHaiJingLSCWGData.New()
	self.shj_luoshence_view = ShanHaiJingLSCView.New(GuideModuleName.ShanHaiJingLSCView)
    self:RegisterProtocol(CSDrawingsComposeOperate)
	self:RegisterProtocol(SCDrawingsComposeInfo, "OnSCDrawingsComposeInfo")
    self:RegisterProtocol(SCDrawingsComposeUpdate, "OnSCDrawingsComposeUpdate")
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function ShanHaiJingLSCWGCtrl:__delete()
    if self.luoshence_suitattr_view then
        self.luoshence_suitattr_view:DeleteMe()
        self.luoshence_suitattr_view = nil
    end

    if self.shj_luoshence_view then
        self.shj_luoshence_view:DeleteMe()
        self.shj_luoshence_view = nil
    end

    ShanHaiJingLSCWGCtrl.Instance = nil
end

function ShanHaiJingLSCWGCtrl:SendCSDrawingsComposeOperate(operate_type, param1, param2, param3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSDrawingsComposeOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
    protocol:EncodeAndSend()
end

function ShanHaiJingLSCWGCtrl:OnSCDrawingsComposeInfo(protocol)
    self.data:SetLuoShenCeInfo(protocol)
    if self.shj_luoshence_view:IsOpen() then
		self.shj_luoshence_view:Flush()
	end
    RemindManager.Instance:Fire(RemindName.ShanHaiJing_LuoShenCe)
    ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView)
end

function ShanHaiJingLSCWGCtrl:OnSCDrawingsComposeUpdate(protocol)
    local is_act, is_up = self.data:UpdateLuoShenCeInfo(protocol)
    RemindManager.Instance:Fire(RemindName.ShanHaiJing_LuoShenCe)

    if is_act then
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0)})
    elseif is_up then
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengxing, is_success = true, pos = Vector2(0, 0)})
    end

    ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingLSCView)
    ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView)
end

function ShanHaiJingLSCWGCtrl:SetLSCSuitAttrDataAndOpen(type, drawings_id)
    if self.luoshence_suitattr_view then
        self.luoshence_suitattr_view:SetData(type, drawings_id)

        if not self.luoshence_suitattr_view:IsOpen() then
            self.luoshence_suitattr_view:Open()
        else
            self.luoshence_suitattr_view:Flush()
        end
    end
end

function ShanHaiJingLSCWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:GetLuoShenCeItemCfgByItemId(change_item_id) then
			ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingLSCView)
            ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView)
			RemindManager.Instance:Fire(RemindName.ShanHaiJing_LuoShenCe)
            self.data:CalculationLuoShenCeRemind()
		end
	end

    if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num < old_num) then
        if self.data:GetLuoShenCeItemCfgByItemId(change_item_id) then
			ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingLSCView)
            ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingView)
			RemindManager.Instance:Fire(RemindName.ShanHaiJing_LuoShenCe)
            self.data:CalculationLuoShenCeRemind()
		end
    end
end
