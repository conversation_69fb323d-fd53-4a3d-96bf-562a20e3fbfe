function TreasureHuntView:BindMoneyEvents()
    self:SetKeysItemData()
	-- XUI.AddClickEventListener(self.node_list.btn_pay, BindTool.Bind(self.OnClickTopBtn, self, COMMON_CONSTS.VIRTUAL_ITEM_GOLD))
	-- XUI.AddClickEventListener(self.node_list.btn_bind, BindTool.Bind(self.OnClickTopBtn, self, COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD))
    -- XUI.AddClickEventListener(self.node_list.btn_coin, BindTool.Bind(self.OnClickTopBtn, self, COMMON_CONSTS.VIRTUAL_ITEM_COIN))
    -- XUI.AddClickEventListener(self.node_list.btn_key, BindTool.Bind(self.OnClickKey, self))
    --self:SetMoneyUI()
end

function TreasureHuntView:SetKeysItemData()
    self.keys_list = {}
    for i = 1, 3 do
        local treasure_cfg = TreasureHuntWGData.Instance:GetTreasureCfgByType(i)
        self.keys_list[i] = treasure_cfg[1].stuff_id
    end
    local mingwen_mode = TreasureHuntWGData.Instance:GetMingwenModeCfg()
    self.keys_list[4] =  mingwen_mode[1].stuff_id
end

-- function TreasureHuntView:DeleteMoneyEvents()
--     RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
--     ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)
-- end

function TreasureHuntView:OnClickTopBtn(item_id)
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function TreasureHuntView:OnClickKey()
    if self.show_index == TreasureHuntView.TabIndex.Fuwen then
        local mingwen_mode = TreasureHuntWGData.Instance:GetMingwenModeCfg()
        local consume_id = mingwen_mode[1].stuff_id
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = consume_id})
    else
        local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
        if treasure_type == nil then
            return
        end
        local key_id = self.keys_list[treasure_type]
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = key_id})
    end
end

function TreasureHuntView:FlushMoneyBar()
    if self.show_index == TreasureHuntView.TabIndex.Fuwen then
        local mingwen_mode = TreasureHuntWGData.Instance:GetMingwenModeCfg()
        local consume_id = mingwen_mode[1].stuff_id
        self.node_list.key_num.text.text = ItemWGData.Instance:GetItemNumInBagById(consume_id)
        local item_cfg = ItemWGData.Instance:GetItemConfig(consume_id)
        self.node_list["key_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    else
        local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
        if treasure_type == nil then
            return
        end
        local key_id = self.keys_list[treasure_type]
        self.node_list.key_icon.image:LoadSprite(ResPath.GetItem(key_id))
        self.node_list.key_num.text.text = ItemWGData.Instance:GetItemNumInBagById(key_id)
    end
end

function TreasureHuntView:OnRoleDataChange(attr_name, value, old_value)
    if attr_name == "gold" then
		--self:SetGold()
	end
	if attr_name == "bind_gold" then
		--self:SetBindGold()
    end
    if attr_name == "coin" then
		--self:SetCoin()
    end
end

-- function TreasureHuntView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    -- if (change_item_id == self.keys_list[1] and self.show_index == TreasureHuntView.TabIndex.Equipment) or
    -- (change_item_id == self.keys_list[2] and self.show_index == TreasureHuntView.TabIndex.DianFeng) or
    -- (change_item_id == self.keys_list[3] and self.show_index == TreasureHuntView.TabIndex.ZhiZun ) then
    --     self:ChangeKey(new_num, old_num)
    --     RemindManager.Instance:Fire(RemindName.TreasureHunt_DianFeng)
    --     RemindManager.Instance:Fire(RemindName.TreasureHunt_Equip)
    --     RemindManager.Instance:Fire(RemindName.TreasureHunt_Zhizun)
    --     --self:FlushBtnRemind()
    --     self:Flush()
    -- elseif (change_item_id == self.keys_list[4] and self.show_index == TreasureHuntView.TabIndex.Fuwen ) then
    --     RemindManager.Instance:Fire(RemindName.TreasureHunt_FuWen)
    --     self:ChangeKey(new_num, old_num)
    --     --self:FlushMingwenBtnRemind(2)
    --     self:Flush()
	-- end
-- end


function TreasureHuntView:SetMoneyUI(index)
	local role_data_info = RoleWGData.Instance:GetRoleInfo()
	--self:SetGold(role_data_info.gold)
	--self:SetBindGold(role_data_info.bind_gold)
    --self:SetCoin(role_data_info.coin)
end

function TreasureHuntView:ChangeKey(new_num, old_num)
	local keys_num = new_num
	local old_money_value = old_num or new_num
	local show_text_num, show_txt = CommonDataManager.ConverMoneyBarNew(keys_num)
	local change_value, change_str = CommonDataManager.ConverMoneyBarNew(keys_num)
	if old_money_value ~= keys_num then
		self.node_list["key_icon"].transform.localScale = Vector3(1, 1, 1)
		self.node_list["key_icon"].transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)

		local bundle_name, asset_name = ResPath.GetEffectUi("UI_qifu_shouji")
		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["key"].transform, 1,nil, nil, nil, nil) -- , "money1"
		RoleWGData.Instance:SetBeforeLastMoney(keys_num, nil, nil, nil, nil, nil, nil, true)

		local complete_fun = function()
					self.node_list["key_num"].text.text = (change_value) .. change_str
					self.node_list["tongqian_icon"].transform.localScale = Vector3(1,1,1)
				end
		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["key_num"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["key_num"].text.text = (value) .. postfix_name
			end
		end

		UITween.DONumberTo(self.node_list["key_num"].text, old_money_value, keys_num, 1.5, update_fun, complete_fun)
	else
		if self.node_list["key_num"] then
			self.node_list["key_num"].text.text = show_text_num..show_txt
		end
	end
end

function TreasureHuntView:SetCoin(value)
	local coin = value or RoleWGData.Instance:GetRoleInfo().coin or 0
    local old_money_value = RoleWGData.Instance:GetOldMoney(MONNEY_TYPE_1[GameEnum.MONEY_BAR.COIN])
    local show_text_num, show_text = CommonDataManager.ConverMoneyBarNew(coin)
	local change_value, change_str = CommonDataManager.ConverMoneyBarNew(coin)
	if old_money_value ~= coin then
		self.node_list["tongqian_icon"].transform.localScale = Vector3(1, 1, 1)
		self.node_list["tongqian_icon"].transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)

		local bundle_name, asset_name = ResPath.GetEffectUi("UI_qifu_shouji")
		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["coin"].transform, 1,nil, nil, nil, nil) -- , "money1"
		RoleWGData.Instance:SetBeforeLastMoney(coin, nil, nil, nil, nil, nil, nil, true)

		local complete_fun = function()
					self.node_list["coin_num"].text.text = (change_value) .. change_str
					self.node_list["tongqian_icon"].transform.localScale = Vector3(1,1,1)
				end
		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["coin_num"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["coin_num"].text.text = (value) .. postfix_name
			end
		end

		UITween.DONumberTo(self.node_list["coin_num"].text, old_money_value, coin, 1.5, update_fun, complete_fun)
	else
		if self.node_list["coin_num"] then
			self.node_list["coin_num"].text.text = show_text_num..show_text
		end
	end
end

function TreasureHuntView:SetGold(value)
	local gold = value or RoleWGData.Instance:GetRoleInfo().gold or 0
	local old_money_value = RoleWGData.Instance:GetOldMoney(MONNEY_TYPE_1[GameEnum.MONEY_BAR.GOLD])
	local show_text_num, show_txt = CommonDataManager.ConverMoneyBarNew(gold)
	local change_value, change_str = CommonDataManager.ConverMoneyBarNew(gold)
	if old_money_value ~= gold then
		self.node_list["money_icon"].transform.localScale = Vector3(1, 1, 1)
		self.node_list["money_icon"].transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)

		local bundle_name, asset_name = ResPath.GetEffectUi("UI_qifu_shouji")
		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["gold"].transform, 1,nil, nil, nil, nil)
		RoleWGData.Instance:SetBeforeLastMoney(gold, nil, nil, nil, nil, nil, nil, true)

		local complete_fun = function()
					self.node_list["gold_num"].text.text = (change_value) .. change_str
					self.node_list["money_icon"].transform.localScale = Vector3(1,1,1)
				end
		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["gold_num"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["gold_num"].text.text = (value) .. postfix_name
			end
		end

		UITween.DONumberTo(self.node_list["gold_num"].text, old_money_value, gold, 1.5, update_fun, complete_fun)
	else
		if self.node_list["gold_num"] then
			self.node_list["gold_num"].text.text = show_text_num..show_txt
		end
	end
end

function TreasureHuntView:SetBindGold(value)
	local bind_gold = value or RoleWGData.Instance:GetRoleInfo().bind_gold or 0
	local old_money_value = RoleWGData.Instance:GetOldMoney(MONNEY_TYPE_1[GameEnum.MONEY_BAR.BIND_GOLD])
	local show_text_num, show_txt = CommonDataManager.ConverMoneyBarNew(bind_gold)
	local change_value, change_str = CommonDataManager.ConverMoneyBarNew(bind_gold)
	if old_money_value ~= bind_gold then
		self.node_list["bangyu_icon"].transform.localScale = Vector3(1, 1, 1)
		self.node_list["bangyu_icon"].transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)

		local bundle_name, asset_name = ResPath.GetEffectUi("UI_qifu_shouji")
		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["bing_gold"].transform, 1,nil, nil, nil, nil) --, "money2"
		RoleWGData.Instance:SetBeforeLastMoney(nil, bind_gold, nil, nil, nil, nil, nil, true)

		local complete_fun = function()
				self.node_list["bindgold_num"].text.text = (change_value) .. change_str
				self.node_list["bangyu_icon"].transform.localScale = Vector3(1,1,1)
			end
		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["bindgold_num"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["bindgold_num"].text.text = (value) .. postfix_name
			end
		end

		UITween.DONumberTo(self.node_list["bindgold_num"].text, old_money_value, bind_gold, 1.5, update_fun, complete_fun)
	else
		if self.node_list["bindgold_num"] then
			self.node_list["bindgold_num"].text.text = show_text_num..show_txt
		end
	end
end
