------------------------------------------------------------
--物品tip
------------------------------------------------------------
ItemTipGoods = ItemTipGoods or BaseClass(ItemTip)

function ItemTipGoods:__init()
end

ItemTipGoods.ShowType = {
	Type1 = 1, -- 显示购买道具
	Type2 = 2, -- 显示路径
}

function ItemTipGoods:ShowIndexCallBack()
	self:FlushView()
	self:FlushGetWayView()
	self:FlushQuickBuyPanel()
	self:SendReqShopItemInfo()
end

function ItemTipGoods:OnFlush(param_t)
	if param_t then
		for k,v in pairs(param_t) do
			if k == "FlushQuickBuyPanel" and self.send_shop_info_times then
				self.send_shop_info_times = self.send_shop_info_times - 1
				if self.send_shop_info_times <= 0 then
					self:FlushQuickBuyPanel()
				end
			end
		end
	end
end

function ItemTipGoods:ShowTipContent()
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		return
	end

	self.item_cell:SetData(self.data)
	local name = ItemWGData.Instance:GetItemConstName(item_cfg, self.data.num)

	-- 出场特效
	local tip_show_effect_id = tonumber(item_cfg.tip_show_effect)
	if tip_show_effect_id and tip_show_effect_id > 0 then
		self.base_tips:SetShowCCEffect(tip_show_effect_id)
	end

	if self.from_view and self.from_view == ItemTip.FROM_MINGWEN_BAG then
		local level = self.data.level or 1
		local base_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(self.data.item_id)
		if base_cfg and base_cfg.is_uplevel and base_cfg.is_uplevel == 1 then
			name = name.."Lv."..level
		end
	end

	self.base_tips:SetItemName(ToColorStr(name, ITEM_COLOR[item_cfg.color]))
	self.base_tips:SetMarketPanel({price = self.data.total_price, item_id = self.data.item_id}) 				-- 设置市场售价

	if item_cfg.is_stuff == KNAPSACK_TYPE.FIVE_ELEMENTS then
		self:ParseFiveElements(item_cfg, self.data)
	elseif SwornWGData.Instance:IsJinLanEquip(self.data.item_id) then
		self:ParseSwornEquip(item_cfg, self.data)
	elseif item_cfg.is_stuff == KNAPSACK_TYPE.RELIC_LIGHT or item_cfg.is_stuff == KNAPSACK_TYPE.RELIC_DRAK then
		self:ParseRelicEquip(item_cfg, self.data)
	elseif ShiTianSuitWGData.Instance:IsShiTianEquip(self.data.item_id) then
		self:ParseShiTianEquip(item_cfg, self.data)
		self:FlushShiTianZuoQiDisplayModel(item_cfg, self.data)
	elseif CultivationWGData.Instance:IsCharmEquip(self.data.item_id) then
		self:ParseCharmEquip(item_cfg, self.data)
	elseif MultiFunctionWGData.Instance:IsDaoHangEquip(self.data.item_id) then
		self:ParseDaoHangEquip(item_cfg, self.data)
	elseif MengLingWGData.Instance:IsMengLingEquip(self.data.item_id) then
		self:ParseMengLingEquip(item_cfg, self.data)
	elseif NewAppearanceWGData.Instance:IsAttrStoreSXD(self.data.item_id) then
		self:ParseAttrStoreSXDProp(item_cfg, big_type)
	elseif WuHunFrontWGData.Instance:GetIsFrontGemByItemId(self.data.item_id) then
		self:ParseWuHunFrontGemTip(item_cfg, self.data)
	elseif DrawGiftWGData.Instance:IsDrawGift(self.data.item_id) then
		self:ParseDrawGift(item_cfg, self.data)
		self:FlushDrawGiftDisplayModel(item_cfg, self.data)
	elseif ShanHaiJingWGData.Instance:GetTJResolveShowCfg(self.data.item_id) then -- 山海经
		self:ParseShanhaijingZuhe(item_cfg, self.data)
	elseif CangJinShopWGData.Instance:IsTeQuanScore(self.data.item_id) then
		self:ParseTeQuanSuit(item_cfg, self.data)
	elseif self.data.item_id == COMMON_CONSTS.VIRTUAL_ITEM_HORNOR then
		self:ParseHornor(item_cfg, self.data)
	else
		self:ParseProp(item_cfg, big_type)
	end

	--仙界虚拟装备判断
	local force_show_top_bg = false
	local fl_holy_equip_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipVirtualCfgByItemId(self.data.item_id)
	if fl_holy_equip_cfg then
		force_show_top_bg = FairyLandEquipmentWGData.Instance:GetIsSpecialType(fl_holy_equip_cfg.part)
	end
	self:SetEquipColorBg(item_cfg.color, force_show_top_bg)
	self.base_tips:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)
	if force_show_top_bg then
		self:SetEquipTopLongEffectShow(item_cfg.color or 0, true)
	end

	-- tip面板特效
	local tips_effect_name = item_cfg.tips_effect_name
	local tips_buttom_effect_name = item_cfg.tips_buttom_effect_name

	if tips_effect_name and tips_effect_name ~= "" then
		local bundle_name, asset_name = ResPath.GetEffectUi(tips_effect_name)
		self:SetEquipTipsPanelEffectShow(bundle_name, asset_name)
	elseif item_cfg.color >= GameEnum.ITEM_COLOR_SHINING_GOLD then
		local bundle_name, asset_name = ResPath.GetWuPinKuangEffectUi(TIPS_KUANG_QUALITY_EFFECT[item_cfg.color])
		self:SetEquipTipsPanelEffectShow(bundle_name, asset_name)
	end

	if tips_buttom_effect_name and tips_buttom_effect_name ~= "" then
		local bundle_name, asset_name = ResPath.GetEffectUi(tips_buttom_effect_name)
		self:SetEquipTipsPanelButtomEffectShow(bundle_name, asset_name)
	end

	if RoleInfoView.xianqi_virtul_item_id and RoleInfoView.xianqi_virtul_item_id[self.data.item_id] then --仙器虚拟物品
		local skill_cfg = EquipmentWGData.Instance:GetEquipKillCfgByItemId(item_cfg.drop_icon)
		if IsEmptyTable(skill_cfg) then
			return
		end
		local next_need_order = 0
		local next_skill_desc = ""
		local next_desc_tilte = ""
		local next_skill_cfg = EquipmentWGData.Instance:GetEquipKillCfgByItemId(skill_cfg.next_equip_id)
		if next_skill_cfg then
			next_skill_desc = next_skill_cfg.desc
			local next_item_cfg = ItemWGData.Instance:GetItemConfig(skill_cfg.next_equip_id)
			if next_item_cfg then
				next_need_order = next_item_cfg.order
				next_desc_tilte = string.format(Language.F2Tip.EquipSkillTitleDesc, next_need_order, Language.Common.ColorName4[next_item_cfg.color])
			end
		end

		local info = {skill_name = skill_cfg.name,
						skill_icon_id = skill_cfg.icon,
						cur_skill_desc = skill_cfg.desc,
						have_next = next_need_order > 0,
						next_desc_tilte = next_desc_tilte,
						next_skill_desc = next_skill_desc,
						show_special_kuang = false}

		self.base_tips:SetEquipSkill(info)
	end

	self:ShowKeYinFuAttr(item_cfg.id)
	self:ParseShowItemListInfo()
end

function ItemTipGoods:ShowKeYinFuAttr(item_id)
	local keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(item_id)
	if not keyinfu_cfg then
		return
	end

	local attr_name_list = Language.Common.TipsAttrNameList
	local attr_list = AttributeMgr.GetAttributteByClass(keyinfu_cfg)
	local sort_list = AttributeMgr.SortAttribute()

	local title_str = Language.NewEquipYinJi.YinJiTipsDesc1
	local info_list = {}
	for i,v in ipairs(sort_list) do
		if attr_list[v] ~= 0 then
			local value = attr_list[v]
			if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
				value = value / 100 .. "%"
			end

			local info = {attr_name = "", attr_value = ""}
			info.attr_name = attr_name_list[v]
			info.attr_value = ToColorStr(value, COLOR3B.D_GREEN)
			info_list[#info_list + 1] = info
		end
	end

	self.base_tips:SetYinFuAttribute({title = title_str, info_list = info_list})
end

function ItemTipGoods:FlushGetWayView()
    if self.data.special_buy_info then
    	self:ShowMoneyBar(true, {self.data.special_buy_info.price_type})
    	self:SetSpecialBuy()
    else
    	ItemTip.FlushGetWayView(self)
    end
end

function ItemTipGoods:OnClickGoToGet(open_panel)
	if open_panel == GuideModuleName.Compose then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, nil, {is_stuff = false, item_id = self.data.item_id})
	elseif open_panel == "guaji" then  						--挂机按钮点击去挂机
		TaskWGData.Instance:GuaJiGetMonster()
	else
		FunOpen.Instance:OpenViewNameByCfg(open_panel)
	end
	self:Close()
end

--显示限时物品时间
function ItemTipGoods:ShowLimitTime()
	if not self.data then
		return
	end
	if self.data.invalid_time and self.data.invalid_time > 0  then
		self:ClearTimeCD()
		local timer = self.data.invalid_time - TimeWGCtrl.Instance:GetServerTime()
		self.time_countdown = GlobalTimerQuest:AddTimesTimer(BindTool.Bind(self.UpDateFun,self), 1, timer)
		self:UpDateFun()
	end
end

function ItemTipGoods:UpDateFun(elapse_time, total_time)
	local time = math.max(0, total_time - elapse_time)
	if time > 0 then
		local time_tab = TimeUtil.FormatSecondDHM6(time)
		self.time_desc.text.text = Language.Tip.SurplusTime .. time_tab
		self.time_desc:SetActive(true)
	else
		self:ClearTimeCD()
		self.time_desc.text.text = ""
		self.time_desc:SetActive(false)
	end
end

function ItemTipGoods:SetSpecialBuy()
	local buy_list = TipWGData.Instance:GetShopBuyList(self.data.item_id)
	if IsEmptyTable(buy_list) and self.data.special_buy_info then --商城有的物品走另一套
		self.base_tips:SetBuyPanel(self.data.special_buy_info)
		local btn_info = {btn_name = Language.F2Tip.Buy, btn_click = BindTool.Bind(self.OnClickSpecialBuy, self)}
		self.base_tips:AddBtnsClick(btn_info)
	end
end

function ItemTipGoods:SpecialRealSendBuy()
	self:Close()
	if self.data.special_buy_call_back then
		self.data.special_buy_call_back(self.base_tips:GetBuyCount())
	end
end

function ItemTipGoods:OnClickSpecialBuy()
	--你策划要求仙玉>=100就弹多一个二次确认框
	local buy_info = self.base_tips:GetBuyInfo()
	local buy_count = self.base_tips:GetBuyCount()
	local price = buy_info and buy_info.price or 0
	local all_price = buy_count * price
	local price_type = buy_info.price_type
	if price_type and all_price >= 100 and price_type == Shop_Money_Type.Type1 then
		local item_config = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if item_config then
			local name_str = ToColorStr(item_config.name, ITEM_COLOR[item_config.color])
			TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat1, all_price, name_str, buy_count), BindTool.Bind(self.SpecialRealSendBuy, self))
		end
	else
		self:SpecialRealSendBuy()
	end
end

function ItemTipGoods:SendReqShopItemInfo()
	self.send_shop_info_times = 0
	if self.data and self.data.item_id > 0 then
		local list_data = TipWGData.Instance:GetShopBuyList(self.data.item_id)
		local temp_list = {}
		for _,v in ipairs(list_data) do
			local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(v.seq)
			if shop_cfg then
				temp_list[#temp_list + 1] = {shop_type = shop_cfg.shop_type, page_type = shop_cfg.page_type}
			end
		end
		self.send_shop_info_times = #temp_list
		for i=1,#temp_list do
			ShopWGCtrl.Instance:SendReqShopItemInfo(temp_list[i].shop_type, temp_list[i].page_type)
		end
	end
end

-- 新增货币兑换展示
function ItemTipGoods:ParseShowItemListInfo()
	local item_list_info = nil

	if FashionExchangeShopWGData.Instance:CheckIsMoneyExchangeItem(self.data.item_id) then
		local reward_list, title = FashionExchangeShopWGData.Instance:GetMoneyExchangeShowList(self.data.item_id)
		if reward_list ~= nil and title ~= nil and title ~= "" then
			item_list_info = {title = title, data = reward_list}
		end
	elseif ShopWGData.Instance:CheckIsMoneyExchangeItem(self.data.item_id) then
		local reward_list, title = ShopWGData.Instance:GetMoneyExchangeShowList(self.data.item_id)
		if reward_list ~= nil and title ~= nil and title ~= "" then
			item_list_info = {title = title, data = reward_list}
		end
	end

	if item_list_info ~= nil then
		self.base_tips:SetShowItemListInfo(item_list_info)
	end
end