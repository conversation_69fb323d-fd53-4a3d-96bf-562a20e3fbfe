InfiniteHellSceneLogic = InfiniteHellSceneLogic or BaseClass(CommonFbLogic)

function InfiniteHellSceneLogic:__init()
	
end

function InfiniteHellSceneLogic:__delete()

end

function InfiniteHellSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- XuiBaseView.CloseAllView()
	-- InfiniteHellWGCtrl.Instance:OpenInfiniteHellTask()
end

function InfiniteHellSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function InfiniteHellSceneLogic:Out()
	CommonFbLogic.Out(self)
	-- InfiniteHellWGCtrl.Instance:CloseInfiniteHellTask()
end