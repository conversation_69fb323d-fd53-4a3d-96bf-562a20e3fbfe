RoleFollow = RoleFollow or BaseClass(CharacterFollow)

local side_res = {
	[1] = "a3_tjmc_bq_s",
	[2] = "a3_tjmc_bq_m",
	[3] = "a3_tjmc_bq_x",
}

function RoleFollow:__init(vo)
	self.shield_obj_type = ShieldObjType.RoleFollowUI
	self.is_main_role = false

	self.follow_name_prefab_name = "SceneRoleObjName"
	self.follow_hp_prefab_name = "RoleHP"
end

function RoleFollow:__delete()
	self.scene_obj = nil
	self.icon_list = nil
end

function RoleFollow:IsMainRole(is_main_role)
	self.is_main_role = is_main_role
	if is_main_role then
		self.shield_obj_type = ShieldObjType.MainRoleFollowUI
	end
end

function RoleFollow:SetRoleScore(score)
	self.namebar:SetScoreStr(score or "")
end

function RoleFollow:SetRedSideIcon(bundle_name, asset_name)
	self.namebar:SetRedSideIcon(bundle_name, asset_name)
end

function RoleFollow:SetRoleServerInfo(server_info)
	self.namebar:SetRoleServerInfo(server_info)
end

function RoleFollow:SetYZWCIco(res_id)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.YEZHANWANGCHENGFUBEN and scene_type ~= SceneType.XianMengzhan then
		self:SetRoleScore()
		return
	end

	if 0 == res_id then
		self:SetRedSideIcon()
		return
	end
	--local asset, bundle = ResPath.GetCommonIcon("pvp_flag_"..res_id)
	local asset, bundle = ResPath.GetCommonIcon(side_res[res_id])
	self:SetRedSideIcon(asset, bundle)
	if scene_type ~= SceneType.XianMengzhan then
		self:SetGuildName("")
	end
	self:SetLoverName("")
	--local info = KuafuYeZhanWangChengWGData.Instance:GetScoreByObjId(scene_obj.vo.obj_id)
	--self:SetRoleScore(info.score or 0)
end

function RoleFollow:SetGuildName(str)
	self.namebar:SetGuildName(str)
end

function RoleFollow:SetZhanDuiName(zhandui3v3_name)
	self.namebar:SetZhanDuiName(zhandui3v3_name)
end

function RoleFollow:SetZhanDuiZhanLingId(zhandui3v3_lingpai_id)
	self.namebar:SetZhanDuiZhanLingId(zhandui3v3_lingpai_id)
end

function RoleFollow:SetZhanDuiZhanLingText(zhandui3v3_lingpai_name)
	self.namebar:SetZhanDuiZhanLingText(zhandui3v3_lingpai_name)
end

function RoleFollow:SetBuffList(data)
	self.hpbar:SetBuffList(data)
end

function RoleFollow:SetLoverName(str)
	self.namebar:SetLoverName(str)
end

function RoleFollow:SetRoleVip(vip_level,is_active_baozhu,is_hide_vip)
	self.namebar:SetRoleVip(vip_level,is_active_baozhu,is_hide_vip)
end

function RoleFollow:SetIconActive(is_active)
	self.namebar:SetIconActive(is_active)
end

function RoleFollow:SetAscriptionIcon(value)
	local bundle,asset = ResPath.GetF2MainUIImage("a3_zjm_gsjl2")
	self:SetSpecialImage(value == 1, bundle, asset)
end

function RoleFollow:SetTiredIcon(value)
    local is_show = value == 1
	self:SetSpecialImage(is_show, ResPath.GetF2MainUIImage("tired_max_icon"))
end

function RoleFollow:SetZhuanzhiIconVisible(value)
	self.namebar:SetZhuanzhiIconVisible(value)
end

function RoleFollow:GetFaceInonObj()
	return self.namebar:GetFaceIconObj()
end

function RoleFollow:SetEternalNightEquipList(equip_data)
	self.namebar:SetEternalNightEquipList(equip_data)
end

function RoleFollow:SetComboKillNum(kill_num)
	self.namebar:SetComboKillNum(kill_num)
end

function RoleFollow:SetXiezhuIngVisible(flag)
	self.namebar:SetXiezhuIngVisible(flag)
end