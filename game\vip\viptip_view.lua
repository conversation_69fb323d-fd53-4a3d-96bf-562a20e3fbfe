------------------------------------------------------
--vip不足提示
--<AUTHOR>
------------------------------------------------------
VipTipView = VipTipView or BaseClass(SafeBaseView)

function VipTipView:__init(view_name)
	self.view_layer = UiLayer.PopTop
 	self:SetMaskBg(true, true)
 	self:AddViewResource(0, "uis/view/vip_ui_prefab", "layout_vip_tips")
 
end

function VipTipView:__delete()
	
end

function VipTipView:ReleaseCallBack()
	self.rich_remind = nil
	self.btn_gotopay = nil
end

function VipTipView:LoadCallBack()
 	self.rich_remind = self.node_list.rich_remind
 	self.btn_gotopay = self.node_list.btn_gotopay

	XUI.AddClickEventListener(self.node_list.btn_xufei, BindTool.Bind1(self.ClickHandlerVIP, self))
	XUI.AddClickEventListener(self.btn_gotopay, BindTool.Bind1(self.ClickHandler, self))
	XUI.AddClickEventListener(self.node_list.btn_cancle, BindTool.Bind1(self.Close, self))
	if IS_FREE_VERSION then
		self.btn_gotopay:SetActive(false)
	end
end

function VipTipView:OnFlush(param)
	for k,v in pairs(param) do
		if k == "no_gold" or k == "no_score" then
			self.node_list["btn_gotopay"]:SetActive(true)
			self.node_list["btn_xufei"]:SetActive(false)
			self.node_list.vip_img:SetActive(false)
			self.node_list.cz_img:SetActive(true)
			local tip_str = Language.Vip.RechargeNoGold
			if k == "no_score" then
				tip_str = Language.Vip.RechargeNoScore
			end
			self.rich_remind.text.text = tip_str
		elseif k == "set_need_vip" then
			self.node_list.vip_img:SetActive(true)
			self.node_list.cz_img:SetActive(false)
			local need_gold = VipWGData.Instance:GetNeedGoldToVip(v.need_vip)
			local tip = Language.Common.VipPowerTip
			
			local power_str = ""
			local power_cfg = VipPower.Instance:GetPowerCfg(v.power_id)
			if power_cfg ~= nil then --正常权限显示
				if v.tip_remain_times == nil or v.tip_remain_times == 0 then
					power_str = power_cfg.power_desc
					if param ~= nil then
						power_str = string.format(power_str, param)
					end
				else  --显示剩余次数
					power_str = power_cfg.remain_times_desc
					power_str = string.format(power_str, v.tip_remain_times)
				end
			end

			if param ~= nil then
				power_str = string.format(power_str, param)
			end

			tip = string.format(tip, need_gold, v.need_vip, power_str)
			self.rich_remind.text.text = tip
		elseif k == "is_xufei" then
			self:Close()
			VipWGCtrl.Instance:OpenvipTipXuFeiView(v)
		end
	end
end

function VipTipView:ClickHandler()
	ViewManager.Instance:CloseAll()
	RechargeWGCtrl.Instance:Open(TabIndex.recharge_cz)
end

function VipTipView:ClickHandlerVIP()
	self:Close()
	-- VipWGCtrl.Instance:OpenVipRenewView()
	ViewManager.Instance:Open(GuideModuleName.Vip,TabIndex.recharge_vip)
end

function VipTipView:CloseCallBack()
	TaskGuide.Instance:SpecialConditions(true, 5)
end