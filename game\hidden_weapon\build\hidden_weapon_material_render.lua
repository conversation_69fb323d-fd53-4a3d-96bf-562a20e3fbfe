-- 暗器打造-材料选择框
HWMaterialRender = HWMaterialRender or BaseClass(BaseRender)

function HWMaterialRender:__init(node, parent)
    self.node = node
    self.parent = parent
    self:AddClickEventListener(self.OnNodeClick)
    self.is_select_material = false
end

function HWMaterialRender:__delete()
    self.node = nil
    self.parent = nil
    if self.item_cell then
        self.item_cell:DeleteMe()
    end
    self.item_cell = nil
    if self.item_cell_preview then
        self.item_cell_preview:DeleteMe()
    end
    self.item_cell_preview = nil
    self.is_select_material = nil
end

function HWMaterialRender:SetPreview(data, show_icon,show_node_type)
    if data and data.equip then
        self.item_cell_preview:SetData(data)
        self.item_cell_preview:SetActive(true)
        if show_icon == true then
            self.item_cell_preview:SetItemIconValue(true)
            self.item_cell_preview:Nodes("img_rare"):SetActive(data.equip.special_flag ~= 0)
            -- if show_node_type then
                -- local xq_icon_buule,xq_icon_name = ResPath.GetShenJiImage("a1_sjitem_icon_" .. data.equip.big_type .."_" ..  data.equip.node_type)
                -- self.item_cell_preview:SetItemIcon(xq_icon_buule, xq_icon_name)
                -- self.item_cell_preview:ResetXqTypeImg()
                -- self.item_cell_preview:Nodes("img_rare"):SetActive(false)
            -- end
        else
            self.item_cell_preview:SetItemIconValue(false)
            self.item_cell_preview:Nodes("img_rare"):SetActive(false)
            self.item_cell_preview:ResetXqTypeImg()
        end
    else
        self.item_cell_preview:SetData(nil)
        self.item_cell_preview:SetActive(false)
    end
end

function HWMaterialRender:SetTarget(item_index, big_type, color, star, item_id, has_remind_info,node_type)
    self.target = {
        item_index = item_index,
        big_type = big_type,
        item_id = item_id,
        color = color,
        star = star,
        node_type = node_type,
    }
    self.has_remind_info = has_remind_info == true
    self:ClearSelect()
end

function HWMaterialRender:ClearSelect()
    self.item_cell:SetData(nil)
    self.node_list["img_plus"]:SetActive(true)
    self.is_select_material = false
    self.current_select_index = nil
    if self.has_remind_info == true then
        self.node_list["remind"]:SetActive(true)
    else
        self.node_list["remind"]:SetActive(false)
    end
end

function HWMaterialRender:GetSelect()
    return self.current_select_index
end

function HWMaterialRender:LoadCallBack(a)
    self.item_cell = ItemCell.New(self.node_list["item_pos"])
    self.item_cell:SetUseButton(false)
    self.item_cell:SetCellBgEnabled(false)
    self.item_cell:SetData(nil)

    self.item_cell_preview = ItemCell.New(self.node_list["item_preview"])
    self.item_cell_preview:SetUseButton(false)
    self.item_cell_preview:SetData(nil)
end

function HWMaterialRender:OnNodeClick()
    if self.is_select_material == true then
        self:ClearSelect()
        if self.parent and self.parent.RefreshBuildBtnState then
            self.parent:RefreshBuildBtnState()
        end
    else
        local selected = self.parent:GetSelectedMaterial()
        HiddenWeaponWGCtrl.Instance:OpenEquipSelect(
            BindTool.Bind1(self.OnMaterialSelect, self),
            self.target or {},
            selected
        )
    end
end

function HWMaterialRender:OnMaterialSelect(index)
    local data = HiddenWeaponWGData.Instance:GetDataByIndex(index)
    if data and data.equip then
        self.is_select_material = true
        self.node_list["img_plus"]:SetActive(false)
        self.item_cell:SetData(data)
        self.current_select_index = index
        self.node_list["remind"]:SetActive(false)
    end
    if self.parent and self.parent.RefreshBuildBtnState then
        self.parent:RefreshBuildBtnState()
    end
end

function HWMaterialRender:OnFlush()
    if not self.data then
        return
    end
    local data = self.data
end
