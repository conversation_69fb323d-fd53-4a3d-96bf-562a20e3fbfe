SocietyTeamInviteReq = SocietyTeamInviteReq or BaseClass(SafeBaseView)

function SocietyTeamInviteReq:__init()
	self.view_layer = UiLayer.Pop
	
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(1078, 608)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_team_req")
end

function SocietyTeamInviteReq:LoadCallBack()
	--self:SetSecondView(nil, self.node_list["size"])
	self:CreateList()
	XUI.AddClickEventListener(self.node_list.btn_auto_refuse, BindTool.Bind(self.OnClickAutoRefuse, self))
    XUI.AddClickEventListener(self.node_list.btn_today_refuse_check, BindTool.Bind(self.OnClickChangeTodaySelectState, self))

    --打开界面默认不选择拒绝（拒绝后今日不再接受该玩家入队邀请）
    self:SetSelectState(false)
end

function SocietyTeamInviteReq:SetSelectState(is_select)
    self.default_select_state = is_select
    self.node_list["btn_today_refuse_select"]:SetActive(is_select)
end

function SocietyTeamInviteReq:ReleaseCallBack()
	if nil ~= self.list then
		self.list:DeleteMe()
		self.list = nil
	end
	if self.role_head_cell ~= nil then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

function SocietyTeamInviteReq:CloseCallBack()
	 if 0 == #SocietyWGData.Instance:GetInviteList()then
	 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, 0)
	 end
	--self:RefuseAllReq()
end

function SocietyTeamInviteReq:ShowIndexCallBack(index)
	self:Flush()
	self.node_list["btn_today_refuse_select"]:SetActive(false)
end

function SocietyTeamInviteReq:CreateList()
	self.list = AsyncListView.New(SocietyTeamInviteReqRender, self.node_list["ph_req_list"])
end

function SocietyTeamInviteReq:OpenCallBack()
	-- self.root_node:setPositionX(HandleRenderUnit:GetWidth() / 2)
	-- self.root_node:setPositionY(HandleRenderUnit:GetHeight() / 2)
end

function SocietyTeamInviteReq:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
	if self.role_head_cell == nil then
		self.role_head_cell = RoleHeadCell.New(false)
	end
	NewTeamWGCtrl.Instance:QueryTeamInfo(role_id, function(protocol)
		if self:IsLoaded() and self:IsOpen() then
			local main_role = Scene.Instance:GetMainRole()
			local main_role_vo = main_role.vo
			local role_info = {
				role_id = role_id,
				role_name = role_name,
				prof = prof,
				sex = sex,
				is_online = is_online,
				team_index = protocol.team_index,
				team_type = TEAM_INVITE_TYPE.CHAT,
				plat_type = main_role_vo.plat_type,
				plat_name = main_role_vo.plat_name,
				server_id = main_role_vo.merge_server_id,
			}
			self.role_head_cell:SetRoleInfo(role_info)
			self.role_head_cell:OpenMenu(node)
		end
	end)
end

--一键拒绝
function SocietyTeamInviteReq:OnClickAutoRefuse()
    --SocietyWGCtrl.Instance:RefuseInviteTeam(self.data.req_role_id)
    local invite_list = SocietyWGData.Instance:GetInviteList()
    for i, v in pairs(invite_list) do
        SocietyWGCtrl.Instance:RefuseInviteTeam(v.req_role_id)
		if self:GetTodayCheckActive() then
			NewTeamWGCtrl.Instance:SendNoLongerOperateReq(LOGIN_NO_LONGER_TYPE.LOGIN_NO_LONGER_TYPE_INVITE_ME_TEAM, v.req_role_id, 0, NO_LONGER_RECORD_TYPE.LOGIN)
		end
    end
end

function SocietyTeamInviteReq:OnClickChangeTodaySelectState()
	local is_select = self.node_list["btn_today_refuse_select"].gameObject.activeSelf
	self.node_list["btn_today_refuse_select"]:SetActive(not is_select)
    --if self.default_select_state == nil then
    --    self.default_select_state = false
    --end
    --self:SetSelectState(not self.default_select_state)
end

function SocietyTeamInviteReq:GetTodayCheckActive()
	if not self.node_list["btn_today_refuse_select"] then
		return false
	end
	return self.node_list["btn_today_refuse_select"].gameObject.activeSelf
end

function SocietyTeamInviteReq:OnFlush()
	if self.list and nil ~= SocietyWGData.Instance:GetInviteList() then
		self.list:SetDataList(SocietyWGData.Instance:GetInviteList())
		self.node_list["ph_req_list"].scroller:ReloadData(0)
	end
end

function SocietyTeamInviteReq:OnRefuseAllReq()
	self:RefuseAllReq()
	self:Close()
end

function SocietyTeamInviteReq:RefuseAllReq()
	local allInviteReq = SocietyWGData.Instance:GetInviteList()
	if nil == allInviteReq or #allInviteReq <= 0 then
		return
	end
	for k, v in pairs(allInviteReq) do
		SocietyWGCtrl.Instance:SendInviteUserTransmitRet(v.req_role_id, 1)
	end
	SocietyWGData.Instance:TeamInviteReqClear()
	self:Flush()
end

function SocietyTeamInviteReq:OnClose()
	self:Close()
end

function SocietyTeamInviteReq:DeleteReq(role_id)
	if nil == role_id then
		return
	end
	SocietyWGData.Instance:RemoveTeamInviteReq(role_id)
	self:Flush()

	if SocietyWGData.Instance:GetInviteListSize() <= 0 then
		self:Close()
	end
end