WardrobeCastingSkillView = WardrobeCastingSkillView or BaseClass(SafeBaseView)
function WardrobeCastingSkillView:__init()
	self:SetMaskBg(true, true)

	local bundle_name = "uis/view/wardrobe_new_ui_prefab"
	self:AddViewResource(0, bundle_name, "layout_wardrobe_casting_skill")
end

function WardrobeCastingSkillView:LoadCallBack()
	-- 初始化9技能
	if not self.casting_skill_list then
		self.casting_skill_list = {}

		for j = 1, 9 do
			local cell_obj = self.node_list.casting_skill_list:FindObj(string.format("casting_skill_0%d", j))
			if cell_obj then
				local cell = WardrobeCastingSkillRender.New(cell_obj)
				cell:SetClickCallBack(BindTool.Bind1(self.OnSelectCastingSkillCB, self))
				cell:SetIndex(j)
				self.casting_skill_list[j] = cell
			end
		end
	end
end

function WardrobeCastingSkillView:ReleaseCallBack()
	if self.casting_skill_list and #self.casting_skill_list > 0 then
		for i, v in ipairs(self.casting_skill_list) do
			v:DeleteMe()
			v = nil
		end

		self.casting_skill_list = nil
	end

	self.casting_skill_index = nil
	self.show_data = nil
	self.casting_skill_data = nil
end

-- 设置当前预览的缓存数据
function WardrobeCastingSkillView:SetNowCacheData(cache_data)
	self.show_data = cache_data
end

-- 方案切换
function WardrobeCastingSkillView:OnSelectCastingSkillCB(cell)
	if cell == nil or cell.data == nil then
		return
	end

	local index = cell.index
	if self.casting_skill_index == index then
		return
	end

	self.casting_skill_index = index
	self.casting_skill_data = cell.data
	self:FlsuhSelectSkill()
end

-- 刷新
function WardrobeCastingSkillView:OnFlush(param_t)
	if not self.show_data then
		return
	end

	if self.casting_skill_index == nil then
		self.casting_skill_index = 1
	end

	local list = WardrobeWGData.Instance:GetForgeEffectCfgByPartIndex(self.show_data.fashion_part_type, self.show_data.fashion_index)
	if not list then
		return
	end

	local forge_data = WardrobeWGData.Instance:GetShizhuangForgeInfoByPartType(self.show_data.fashion_part_type, self.show_data.fashion_index)
	local grade_level = forge_data and forge_data.grade_level or 0

	for i, v in ipairs(self.casting_skill_list) do
		v:SetVisible(list[i] ~= nil)

		if list[i] ~= nil then
			v:SetGradeLevel(grade_level)
			v:SetData(list[i])
		end
	end

	self.casting_skill_data = list[self.casting_skill_index]
	self:FlsuhSelectSkill()
end

function WardrobeCastingSkillView:FlsuhSelectSkill()
	for i, v in ipairs(self.casting_skill_list) do
		v:OnSelectChange(i == self.casting_skill_index)
	end

	if not self.casting_skill_data then
		return
	end

	self.node_list.casting_slot_name.tmp.text = self.casting_skill_data.skill_name
	self.node_list.skill_show_icon.image:LoadSprite(ResPath.GetSkillIconById(self.casting_skill_data.skill_icon))
	self.node_list.skill_show_desc_tips.tmp.text = self.casting_skill_data.skill_desc
end

----------------------------------方案item-----------------------
WardrobeCastingSkillRender = WardrobeCastingSkillRender or BaseClass(BaseRender)
function WardrobeCastingSkillRender:__delete()
    self.grade_level = nil
end

function WardrobeCastingSkillRender:SetGradeLevel(grade_level)
	self.grade_level = grade_level
end

function WardrobeCastingSkillRender:OnFlush()
    if not self.data then
        return
    end

	self.node_list.skill_name_txt.tmp.text = self.data.skill_name
	self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))

	local real_grade_lv = self.grade_level or 0
	self.node_list.lock:CustomSetActive(self.data.grade_level > real_grade_lv)
end

function WardrobeCastingSkillRender:OnSelectChange(is_select)
    self.node_list.select:SetActive(is_select)
end

