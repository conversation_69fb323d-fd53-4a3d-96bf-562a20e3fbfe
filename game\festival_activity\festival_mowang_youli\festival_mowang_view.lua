--活动_魔王有礼
function FestivalActivityView:LoadIndexCallBackMoWang()
    FestivalMoWangYouliWGCtrl.Instance:ReqCOAMoWangYouLiDropLimitItemInfo()
	XUI.AddClickEventListener(self.node_list.mw_btn_duobao, BindTool.Bind(self.OnClickDuoBaoBtnMoWang,self))

    self:FlushMoWangViewInfo()
end

function FestivalActivityView:ReleaseCallBackMoWang()
    if self.mw_reward_list then
		self.mw_reward_list:DeleteMe()
		self.mw_reward_list = nil
	end

	-- if self.mw_grid then
	-- 	self.mw_grid:DeleteMe()
	-- 	self.mw_grid = nil
	-- end
end

function FestivalActivityView:OnBossTimesChange(boss_type, enter_times, max_times)
    if not self:IsLoadedIndex(TabIndex.festival_activity_2267) then
		return
    end
    -- if self.mw_grid then
    --     for k,v in pairs(self.mw_grid:GetAllCell()) do
    --         if v then
    --             v:Flush("times_change", {boss_type, enter_times, max_times})
    --         end
    --     end
    -- end
end

function FestivalActivityView:OnFlushMoWang(param_t)
	local param_cfg = FestivalMoWangYouliWGData.Instance:GetParamCfgByServerOpenDay()
	if not IsEmptyTable(param_cfg) and param_cfg.grade then
		--self:FlushParamCfgViewElementMoWang(param_cfg)
		--local view_cfg = FestivalMoWangYouliWGData.Instance:GetViewCfg(param_cfg.grade)
		--self:FlushViewElementMoWang(view_cfg)
		--self:FlushBossModelMoWang(param_cfg)

		local view_cfg = FestivalMoWangYouliWGData.Instance:GetViewCfg(param_cfg.grade)
		if not view_cfg then
			return
		end

		self:SetOutsideRuleTips(view_cfg.mw_tip_label)
		self:SetRuleInfo(view_cfg.rule_desc, view_cfg.btn_rule_title)
	end
    self:FlushPreviewRewardMoWang()
    self:FlushTaskListMoWang()
end

function FestivalActivityView:FlushMoWangViewListInfo()
	if not self:IsLoadedIndex(TabIndex.festival_activity_2267) then
		return
    end
    local cfg = FestivalMoWangYouliWGData.Instance:GetMWTaskList()
end

function FestivalActivityView:FlushMoWangViewInfo()
	if not self:IsLoadedIndex(TabIndex.festival_activity_2267) then
		return
	end
	local param_cfg = FestivalMoWangYouliWGData.Instance:GetParamCfgByServerOpenDay()
	if not IsEmptyTable(param_cfg) and param_cfg.grade then
		self:FlushParamCfgViewElementMoWang(param_cfg)
		local view_cfg = FestivalMoWangYouliWGData.Instance:GetViewCfg(param_cfg.grade)
		self:FlushViewElementMoWang(view_cfg)
		self:FlushBossModelMoWang(param_cfg)
	end
	self:FlushTaskListMoWang()
end

function FestivalActivityView:SetTextStrMoWang(node_name, str)
	if self.node_list[node_name] then
		self.node_list[node_name].text.text = str
	end
end

function FestivalActivityView:SetImageMoWang(node_name, res_path, asset_name, is_async, not_set_native_size)
	local node = self.node_list[node_name]
	if node then
		local b, a = res_path(asset_name)
		if is_async then
			node.image:LoadSpriteAsync(b, a, function()
				if not not_set_native_size then
					XUI.ImageSetNativeSize(node)
				end
			end)
		else
			node.image:LoadSprite(b, a, function()
				if not not_set_native_size then
					XUI.ImageSetNativeSize(node)
				end
			end)
		end
	end
end

function FestivalActivityView:SetRawImageMoWang(node_name, res_path, asset_name, is_async)
	local node = self.node_list[node_name]
	if node then
		local b, a = res_path(asset_name)
		if is_async then
			node.raw_image:LoadSpriteAsync(b, a, function()
				node.raw_image:SetNativeSize()
			end)
		else
			node.raw_image:LoadSprite(b, a, function()
				node.raw_image:SetNativeSize()
			end)
		end
	end
end

--刷新魔王有礼界面元素(参数配置)
function FestivalActivityView:FlushParamCfgViewElementMoWang(param_cfg)

	--击杀条件
	self:SetTextStrMoWang("mw_condition", param_cfg.mw_kill_condition)
end

--刷新魔王有礼界面元素(界面显示配置)
function FestivalActivityView:FlushViewElementMoWang(view_cfg, param_cfg)
	self:SetOutsideRuleTips(view_cfg.mw_tip_label)

	local bundle, asset = ResPath.GetFestivalRawImages("devil")
	self.node_list["right_raw_mw_bg"].raw_image:LoadSprite(bundle, asset, function ()
	self.node_list["right_raw_mw_bg"].raw_image:SetNativeSize()
	end)

	local bundle1, asset1 = ResPath.GetFestivalRawImages("desc")
	self.node_list["commonact_desc"].raw_image:LoadSprite(bundle1, asset1, function ()
	self.node_list["commonact_desc"].raw_image:SetNativeSize()
	end)


	local bundle2, asset2 = ResPath.GetFestivalActImages("a2_jrkh_mwyl_an")
	self.node_list["mw_btn_duobao"].image:LoadSprite(bundle2, asset2, function ()
	self.node_list["mw_btn_duobao"].image:SetNativeSize()
	end)

    local reward_color = view_cfg.reward_color

    self.node_list["mw_btn_duobao"]:SetActive(view_cfg.mw_is_show_go_btn == 1)
    self.node_list["mw_btn_go_text"].text.text = view_cfg.mw_btn_go_text
    
end

function FestivalActivityView:FlushPreviewRewardMoWang()
    if not self:IsLoadedIndex(TabIndex.festival_activity_2267) then
		return
	end
	local rewards = FestivalMoWangYouliWGData.Instance:GetMWPreViewReward()
	if rewards ~= nil then
		if not self.mw_reward_list then
			self.mw_reward_list = AsyncListView.New(FestivalMoWangItemRender, self.node_list.mw_reward_list)
        end
		self.mw_reward_list:SetDataList(rewards)
	end
end

function FestivalActivityView:FlushTaskListMoWang()
	local cfg = FestivalMoWangYouliWGData.Instance:GetMWTaskList()
end

function FestivalActivityView:FlushBossModelMoWang(param_cfg)
	if param_cfg ~= nil then
		local param_cfg = FestivalMoWangYouliWGData.Instance:GetParamCfgByServerOpenDay()
		if IsEmptyTable(param_cfg) or not param_cfg.grade then
			return
		end
		local data = {}
		data.render_type = param_cfg.render_type
		if param_cfg.render_int_param1 and param_cfg.render_int_param1 > 0 then
			data.item_id = param_cfg.render_int_param1
		elseif param_cfg.render_string_param1 and param_cfg.render_string_param1 ~= ""
				and param_cfg.render_string_param2 and param_cfg.render_string_param2 ~= "" then
			data.bundle_name = param_cfg.render_string_param1
			data.asset_name = param_cfg.render_string_param2
		end

		if param_cfg.mw_position ~= nil and param_cfg.mw_position ~= "" then
			local pos = Split(param_cfg.mw_position, "|")
			data.position = Vector3(tonumber(pos[1]), tonumber(pos[2]), tonumber(pos[3]))
		end

		if param_cfg.mw_scale ~= nil and param_cfg.mw_scale ~= "" then
			local scale = param_cfg.mw_scale
			data.scale = Vector3(scale, scale, scale)
		end

		if param_cfg.mw_rotation ~= nil and param_cfg.mw_rotation ~= "" then
			local rota = Split(param_cfg.mw_rotation, "|")
			data.rotation = Quaternion.Euler(tonumber(rota[1]), tonumber(rota[2]), tonumber(rota[3]))
		end
		
	end
end

function FestivalActivityView:OnClickBtnTipMoWang()
	local param_cfg = FestivalMoWangYouliWGData.Instance:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return
	end
	local view_cfg = FestivalMoWangYouliWGData.Instance:GetViewCfg(param_cfg.grade)
	if not view_cfg then
		return
	end

	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(view_cfg.btn_rule_title)
		role_tip:SetContent(view_cfg.rule_desc)
	end
end

function FestivalActivityView:OnClickDuoBaoBtnMoWang()
	local param_cfg = FestivalMoWangYouliWGData.Instance:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return
	end
	local view_cfg = FestivalMoWangYouliWGData.Instance:GetViewCfg(param_cfg.grade)
	if not view_cfg then
		return
	end

	if view_cfg.mw_is_show_go_btn == 0 then
		return
	end

	if view_cfg.btn_param_1 == OPERA_ACT_MOWANG_BTN_PARAM.ACT then --跳转到活动面板
		local is_open = ActivityWGData.Instance:GetActivityIsOpen(view_cfg.btn_param_2)
		if not is_open then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.MoWangYouLi.ActDuoBaoNotOpen)
			return
		end
	end

	local tab =  Split(view_cfg.left_btn_jump, "#")
	local view_name = tab[1]
	local tab_index = tab[2]

	self:SetTabbarMoveFlag()
	FunOpen.Instance:OpenViewByName(view_name, tab_index)
end

----有效时间倒计时
--function FestivalActivityView:AddCountDownMoWang()
--	if CountDownManager.Instance:HasCountDown("opera_mw_count_down") then
--		CountDownManager.Instance:RemoveCountDown("opera_mw_count_down")
--	end
--	local invalid_time = FestivalMoWangYouliWGData.Instance:GetActivityEndTime()
--	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
--		self.node_list.mw_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
--		CountDownManager.Instance:AddCountDown("opera_mw_count_down", BindTool.Bind1(self.UpdateCountDownMoWang, self), BindTool.Bind1(self.OnCompleteCountDownMoWang, self), invalid_time, nil, 1)
--	end
--end
--
--function FestivalActivityView:UpdateCountDownMoWang(elapse_time, total_time)
--	local valid_time = total_time - elapse_time
--	if valid_time > 0 then
--		if self.node_list.mw_time_label then
--			self.node_list.mw_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
--		end
--	end
--end
--
--function FestivalActivityView:OnCompleteCountDownMoWang()
--	if self.node_list.mw_time_label then
--		self.node_list.mw_time_label.text.text = Language.Activity.TianshenRoadLoginTime
--	end
--end

----------------------------------------------------------------------------

FestivalMWTaskItemRender = FestivalMWTaskItemRender or BaseClass(BaseRender)

function FestivalMWTaskItemRender:LoadCallBack()
    self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnClickGo, self))
    self.node_list.item_bg.image:LoadSprite(ResPath.GetFestivalMowangYouliUIImages("hs_tongyon002"))
    self.node_list.task_item_icon_bg.image:LoadSprite(ResPath.GetFestivalMowangYouliUIImages("touxiang_kuan0"))
end

function FestivalMWTaskItemRender:OnFlush(param_t)
	if not self.data then
		return
	end

	for k,v in pairs(param_t or {"all"}) do
		if k == "times_change" then
			self:FlushTimes()
		else
			self:FlushAll()
		end
	end
end

function FestivalMWTaskItemRender:FlushAll()
	local color =FestivalActivityWGData.Instance:GetCommonColor()
	local bundle, asset = ResPath.GetF2MainUIImage(self.data.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset)
	self.node_list.name.text.text = ToColorStr(self.data.task_name, color)
    
	self.node_list.btn.image:LoadSprite(ResPath.GetF2CommonButtonToggle(self.data.task_item_btn_bg))
	self.node_list.task_item_btn_text.text.text = self.data.task_item_btn_text
	self.node_list.btn_ylq.image:LoadSprite(ResPath.GetF2CommonImages(self.data.complete_flag))
	self.node_list.complete_text.text.text = self.data.complete_text
	self.node_list.finish_left.text.text = ToColorStr(Language.OpertionAcitvity.MoWangYouLi.KillConditionLeft, color)

	if self.data.panel ~= "" then
		XUI.SetButtonEnabled(self.node_list.btn, true)
	else
		XUI.SetButtonEnabled(self.node_list.btn, false)
	end
	local is_show_condition = self.data.is_show_condition == 1
	self.node_list.number_txt:SetActive(is_show_condition)

	self:FlushTimes()
end

function FestivalMWTaskItemRender:FlushTimes()
    if not self.node_list.btn then
        return
    end

    local left_times, max_times = BossWGData.Instance:GetBossTimesInfo(self.data.ID)
    if left_times == -1 then
        self.node_list.finish_right.text.text = Language.OpertionAcitvity.MoWangYouLi.Infinite
        self.node_list.btn:SetActive(true)
        if self.data.ID == BOSS_TASK_TYPE.VIP then
	    	self.node_list.btn_ylq:SetActive(false)
    	else
    		self.node_list.btn_ylq:SetActive(true)
	    end
    else
        local can_kill_boss = left_times > 0
        local color = can_kill_boss and "<color=#009621>" or "<color=#ff0000>"
        self.node_list.btn:SetActive(can_kill_boss)
        self.node_list.btn_ylq:SetActive(not can_kill_boss)
        self.node_list.finish_right.text.text = string.format(Language.OpertionAcitvity.MoWangYouLi.KillConditionRight, color, left_times, max_times)
    end
end

function FestivalMWTaskItemRender:OnBossTimesChange(boss_type, enter_times, max_times)
	self:Flush("times_change", {boss_type, enter_times, max_times})
end

function FestivalMWTaskItemRender:OnClickGo()
	if self.data.panel then
		local param = string.split(self.data.panel,"#")
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end

-------------------------------------------------------------------------------------------

FestivalMoWangItemRender = FestivalMoWangItemRender or BaseClass(BaseRender)

function FestivalMoWangItemRender:__init()
	self.item = ItemCell.New(self.node_list.cell_pos)
end

function FestivalMoWangItemRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
    end
    
end

function FestivalMoWangItemRender:OnFlush()
    self.item:SetData(self.data)
    local remain = self.data.activity_person_day_limit - self.data.cur_num
    local color = remain <= 0 and COLOR3B.RED or COLOR3B.GREEN
    self.node_list.limit_text.text.text = ToColorStr(string.format("%s/%s", self.data.cur_num,self.data.activity_person_day_limit) , COLOR3B.WHITE)
    --string.format(Language.OpertionAcitvity.MoWangYouLi.TodayLimitNum,color, self.data.cur_num, self.data.activity_person_day_limit)
end