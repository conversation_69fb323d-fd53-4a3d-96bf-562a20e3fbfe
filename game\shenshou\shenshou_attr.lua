ShenShouView = ShenShouView or BaseClass(SafeBaseView)
local DELAY_TIME = 0.7
local ONE_LINE_SKILL_COUNT = 4  --一行技能的最大数量

function ShenShouView:InitShenShouAttr()
	self.select_shou_id = 1
	self.quality = -1
	self.star = 0
	self.idx_cache = 1
	self.zz_time = 0
	self.max_series = 0
	self.load_cell_complete_count = 0
	self.role_level = 0
	self.is_first = true
	self.equip_list ={}
	self.equip_list_data ={}
	self.chuzhanwei_list = {}
	self.ss_cell_list = {}
	self.is_move = false

	self.series = 0
	self.cur_select_shenshou_series = 0


	for i = 1, MAX_CHU_ZHAN_SLOT_NUM do
		self.chuzhanwei_list[i] = ShouChuZhanWeiRender.New(self.node_list['zhuzhanwei_' .. i])
		self.chuzhanwei_list[i]:SetIndex(i)
		self.chuzhanwei_list[i].parent = self
	end

	self.pop_alert = Alert.New()
	self.pop_alert:SetLableString(Language.ShenShou.NoRightEquip)
	self.pop_alert:SetOkString(Language.ShenShou.GainEquip)


	self.node_list["btn_zhuzhan"].button:AddClickListener(BindTool.Bind(self.ShenShouZhuZhan, self))



	self.flush_appearance = GlobalEventSystem:Bind(
		ShenShouEventType.SHEN_SHOU_EXTRA_COUNT,
		BindTool.Bind(self.FlushShenShouAttr,self))

	self.skill_state = {false, false, false, false, false, false, false, false}
	self:CreateShenShouCells()
	self:CreateShenShouList()
	self.cache_model = nil
	self.node_list["rich_shenshou_des"].text.text = string.format(Language.ShenShou.EquipTips)

	if nil == self.boss_display_model then
		self.boss_display_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ss_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.boss_display_model:SetRenderTexUI3DModel(display_data)
		-- self.boss_display_model:SetUI3DModel(self.node_list["ss_model"].transform, self.node_list["ss_model"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end


end

function ShenShouView:ReleaseShenShouAttr()
	self.is_move = nil
	self.is_first = nil
	self.shenshou_skill_list_view = nil
	self.ss_open_first_shou_id = nil
	self.ss_open_first = nil
	self.zz_time = nil
	self.max_series = 0
	self.role_level = 0

	if CountDownManager.Instance:HasCountDown("move_delay") then
        CountDownManager.Instance:RemoveCountDown("move_delay")
    end

    if self.move_tween then
        self.move_tween:Kill()
        self.move_tween = nil
    end

	if self.ss_cell_list then
		for k,v in pairs(self.ss_cell_list) do
			for m,n in pairs(v) do
				n:DeleteMe()
			end
		end
	end
	self.ss_cell_list = {}

	if self.shenshou_cells then
		for k,v in pairs(self.shenshou_cells) do
			v:DeleteMe()
		end
	 end
	self.shenshou_cells = {}

	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end

	self.one_skill_list = {}

	if self.take_off_alert then
		self.take_off_alert:DeleteMe()
		self.take_off_alert = nil
	end

	if self.flush_appearance then
		GlobalEventSystem:UnBind(self.flush_appearance)
		self.flush_appearance = nil
	end

	if self.chuzhanwei_list then
		for i,v in ipairs(self.chuzhanwei_list) do
			v:DeleteMe()
		end
		self.chuzhanwei_list = nil
	end



	self.cache_model = nil
	self.cache_resid = nil
	if self.boss_display_model then
		self.boss_display_model:DeleteMe()
		self.boss_display_model = nil
	end


	self.cur_select_shenshou_series = 0
	self.jump_shenshou_id = nil


end

function ShenShouView:OpenShenShouCallBack()
	self.ss_open_first = false
end

function ShenShouView:CloseShenShouCallBack()
	self.ss_open_first = false
	if self.playing_ainm_delay then
		GlobalTimerQuest:CancelQuest(self.playing_ainm_delay)
		self.playing_ainm_delay = nil
	end

	if self.delay_calc_scroll_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_calc_scroll_quest)
		self.delay_calc_scroll_quest = nil
	end
	self:ReSetEffectPosition()
end

function ShenShouView:ShowIndexShenShou(jump_id)
	if self.ss_open_first then
		return
	end
	self.ss_open_first = true
	local shou_id1 = 1
	local shou_id2 = 1
	local list = ShenShouWGData.Instance:GetShenShouZhuZhanList()
	for k,v in pairs(list) do
		if v > shou_id1 then
			shou_id1 = v
		end
	end

	local red_list = ShenShouWGData.Instance:GetShenShouLimitRed()
	if not jump_id and IsEmptyTable(red_list) then
		shou_id2 = shou_id1
	else
		shou_id2 = jump_id and jump_id or red_list[1]
	end

	self.ss_open_first_shou_id = shou_id2

	self:FlushChuZhanWei()

	-- 开启特效关闭
	if self.chuzhanwei_list then
		for i,v in ipairs(self.chuzhanwei_list) do
			v:WGCtrlEffect(false)
		end
	end


end

function ShenShouView:CreateShenShouCells()		---创建神兽装备格子
	self.shenshou_cells = {}
	for i=1, GameEnum.SHENSHOU_MAX_EQUIP_COUNT do
		self.shenshou_cells[i] = ShenShouEquipCell.New(self.node_list["ph_cell_" .. i])
		self.shenshou_cells[i]:SetItemTipFrom(ShenShouEquipTip.FROM_SHENSHOU)
		self.shenshou_cells[i]:AddClickEventListener(BindTool.Bind(self.OnClickShenShouCell, self, i), true)
	end
end

function ShenShouView:CreateShenShouList()
	self.ss_accordion_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	self.role_level = role_level
	for i = 1, Language.ShenShou.SS_SERIES do
	    local is_active = ShenShouWGData.Instance:GetShenShouTypeIsActive(i, role_level)
		if is_active then
			self.max_series = self.max_series + 1
		end
	end

	for i = 1, Language.ShenShou.SS_SERIES do
		if i <= self.max_series then
			self.ss_accordion_list[i] = {}
			self.node_list["Content"]:FindObj("SelectBtn" .. i):SetActive(true)
			self.ss_accordion_list[i].btn = self.node_list["Content"]:FindObj("SelectBtn" .. i)
			self.ss_accordion_list[i].list = self.node_list["Content"]:FindObj("List" .. i)
			self.ss_accordion_list[i].btn.accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickExpandHandler, self, i))

			local name = ShenShouWGData.Instance:GetSeriesName(i)
			if name then
				self.node_list["text_btn" .. i].text.text = name
			end

			self:InstantSSItem(i)
		else
			self.node_list["Content"]:FindObj("SelectBtn" .. i):SetActive(false)
		end
	end
end

function ShenShouView:InstantSSItem(index)
	local ss_list = ShenShouWGData.Instance:GetShenShouDataBySeries(index, self.role_level)
	if not self.ss_cell_list[index] then
		local res_async_loader = AllocResAsyncLoader(self, "ph_eq_item" .. index)

	 	res_async_loader:Load("uis/view/shenshou_ui_prefab", "ph_eq_item", nil,
	 		function(new_obj)
			    local item_vo = {}
			    for k,v in pairs(ss_list) do
			        local obj = ResMgr:Instantiate(new_obj)
			        local obj_transform = obj.transform
			        obj_transform:SetParent(self.ss_accordion_list[index].list.transform, false)
			        local item_render = ShenShouItemRender.New(obj)
					item_render.is_shenshou = true
			        item_render.parent_view = self
			        item_render:SetIndex(k)
			        item_render.view.button:AddClickListener(BindTool.Bind(self.SelectShenShouCellCallBack, self, item_render))
			        item_render:SetData(v)
			        item_vo[k] = item_render
			        if k == #ss_list then
						self.ss_cell_list[index] = item_vo
			      		self.load_cell_complete_count = self.load_cell_complete_count + 1
			   		end
			    end

			    if self.load_cell_complete_count == self.max_series then
					-- 第一次打开界面进行跳转
					for k,v in ipairs(self.ss_accordion_list) do
						if (ShenShouWGData.Instance:GetShenShouRemindBySeries(k)) then
							self.ss_accordion_list[k].btn.toggle.isOn = true
							return
						end
					end
					self.ss_accordion_list[1].btn.toggle.isOn = true
				end
			end)
	else
		self:ShowFlushCell(index, ss_list)
	end
end

function ShenShouView:ShowFlushCell(index, ss_list)
	if self.load_cell_complete_count < self.max_series then
		return
	end

	for k, v in pairs(self.ss_cell_list[index]) do
		if nil == ss_list[k] then
			break
		end
		v:SetData(ss_list[k])
	end
end

function ShenShouView:CreateShenShenShouSkill()
	self.one_skill_list = ShenShouWGData.Instance:GetOneShouSkill(self.select_shou_id)
	local buf

	self.node_list.ph_grid_skill2:SetActive(#self.one_skill_list > ONE_LINE_SKILL_COUNT)

	for k, v in pairs(self.ss_skill_list) do
		buf = self.one_skill_list[k] ~= nil
		if self.skill_state[k] ~= buf then
			self.skill_state[k] = buf
			self.ss_skill_list[k]:SetActive(buf)
		end

		if self.one_skill_list[k] then
			v:SetData(self.one_skill_list[k])
		end
	end
end

--数据发生改变时的刷新
function ShenShouView:FlushShenShouAttr()
	if not self:IsOpen() then
		return
	end

	self:InstantSSItem(self.idx_cache)
	self:FlushEquipList()
	self:FlushSbBigBtnRed()
	self:CreateShenShenShouSkill()

	local zhuzhan_num = ShenShouWGData.Instance:GetZhuZhanNum()
	local has_weizi_num = ShenShouWGData.Instance:GetCurOpenZhuZhanCount()
	self.node_list["zhuzhan_num"].text.text = zhuzhan_num .. "/" .. has_weizi_num

	if ShenShouWGData.Instance:GetShenShouShowEffect() then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_zhuzhan, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["effect"]})
	end
	if not self.is_first then
		self:FlushChuZhanWei()
	end
	self.is_first = false
end

function ShenShouView:FlushEquipList()
	-- local bundle, asset = ResPath.GetShenShouIcon(self.select_shou_id)
	-- self.node_list["img_shenshou_state"].image:LoadSprite(bundle, "shenshou_"..asset)		---设置图片
	self:SetShenShouImage()
	-- 是否穿戴齐5件装备
	local is_active  = ShenShouWGData.Instance:IsShenShouActive(self.select_shou_id)
	-- 当前神兽是否出战
	local is_zhuzhan = ShenShouWGData.Instance:IsShenShouZhuZhan(self.select_shou_id)
	--
	local is_show_red = ShenShouWGData.Instance:IsShowShenShouRenderRed(self.select_shou_id)

	--XUI.SetButtonEnabled(self.node_list.btn_zhuzhan, is_active)
	self.node_list.btn_ss_red:SetActive(ShenShouWGData.Instance:GetShenShouQiangHuaRemind() > 0)
	self.node_list.btn_assit_red:SetActive(is_show_red and is_active and not is_zhuzhan)
	local str = Language.ShenShou.ZhuZhan
	if is_show_red and is_active and not is_zhuzhan then
		str = Language.ShenShou.ZhuZhan
	elseif is_zhuzhan then
		str = Language.ShenShou.ZhaoHui
	end
	self.node_list.zz_txt.text.text = str
	if is_show_red and is_active and not is_zhuzhan then
		XUI.SetGraphicGrey(self.node_list.btn_assit_red,false)
	end

	self:FlushZhuZhanJiaCheng()
	local shenshou_list = ShenShouWGData.Instance:GetShenshouList(self.select_shou_id)
	local cell_cfg = ShenShouWGData.Instance:GetQualityRequirement(self.select_shou_id)

	for i = 1,6 do
		self.node_list["shenshou_color_effect"..i]:SetActive(i-1 == self.series and is_zhuzhan)
	end

	if shenshou_list then
		local limit_cfg
	 	for k, v in pairs(shenshou_list.equip_list) do
			self.shenshou_cells[k]:SetData(v)
			if v.item_id == 0 then
				local alpha_color = Color.New(1, 1, 1, 0.7)
				self.shenshou_cells[k]:SetItemIcon(ResPath.GetF2SHJImgPath("equip_" .. k - 1))
				self.shenshou_cells[k]:SetItemIconAlpha(alpha_color)
				self.shenshou_cells[k]:SetCellBgEnabled(false)
				self.node_list["ph_cell_" .. k].image:LoadSprite(ResPath.GetCommonImages("a3_ty_wpk_"..cell_cfg[k].slot_need_quality+1))
			end

			self.shenshou_cells[k]:SetLeftBottomColorText(Language.ShenShou.ZhuangBeiLeiXing[k - 1])
			self.shenshou_cells[k]:SetLeftBottomColorTextVisible(true)
			if v.item_id == 0 then
				if limit_cfg == nil then
					limit_cfg = ShenShouWGData.Instance:GetQualityRequirement(self.select_shou_id)
				end
				self.shenshou_cells[k]:SetLeftTopImg(limit_cfg[k].slot_need_star)
			end
		end
	 else
		for k,v in pairs(self.shenshou_cells) do
			local limit_quality= ShenShouWGData.Instance:GetQualityRequirementCfg(self.select_shou_id, k-1)
			self.shenshou_cells[k]:ClearAllParts()
			self.shenshou_cells[k]:SetData(nil)
			self.shenshou_cells[k]:SetLeftTopImg(limit_quality.slot_need_star)
			self.node_list["ph_cell_" .. k].image:LoadSprite(ResPath.GetCommonImages("a3_ty_wpk_" .. cell_cfg[k].slot_need_quality+1))
			self.shenshou_cells[k]:SetItemIcon(ResPath.GetF2SHJImgPath("equip_" .. k - 1))
			self.shenshou_cells[k]:SetButtonComp(true)
			if limit_quality.slot_need_star > 0 then
				self.shenshou_cells[k]:SetLeftTopImg(limit_quality.slot_need_star)
			end
		end
	end

	--头像刷新
	-- local bundle, asset = ResPath.GetBossIconName(self.select_shou_id)
	-- self.node_list.ss_head.raw_image:LoadSprite(bundle, asset, function ()
	-- 	self.node_list.ss_head.raw_image:SetNativeSize()
	-- end)
	self:FlushCellRemind()
	local model_info = ShenShouWGData.Instance:GetShenShouCfg(self.select_shou_id)
	self.node_list.shenshou_name.text.text = model_info.name
	if nil ~= model_info and self.boss_display_model then
		if self.cache_resid == model_info.icon_id and self.boss_display_model then
			if self.boss_display_model:GetDrawObj() ~= nil then
				return
			end
		end

		self.boss_display_model:SetMainAsset(ResPath.GetDivineSoldierModel(model_info.icon_id))

		local scale = model_info["scale"]
    	Transform.SetLocalScaleXYZ(self.node_list["ss_model"].transform, scale, scale, scale)

		self.cache_resid = model_info.icon_id
		self.boss_display_model:PlayMonsterAction(false)
	end
end

function ShenShouView:FlushZhuZhanJiaCheng()
	if self.attr_list == nil then
		return
	end

	-- 天道石影响属性百分比,不影响百分比
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.SHENSHOU)
	charm_rate = charm_rate / 10000

	local shou_cfg = ShenShouWGData.Instance:GetShenShouCfg(self.select_shou_id)
	local shenshou_base_struct = AttributeMgr.GetAttributteByClass(shou_cfg)
	shenshou_base_struct = AttributeMgr.MulAttributeRemovePer(shenshou_base_struct,charm_rate + 1)
	local eq_struct = ShenShouWGData.Instance:GetOneShenShouAttr(self.select_shou_id)
	eq_struct = AttributeMgr.MulAttributeRemovePer(eq_struct,charm_rate + 1)


	local attr_list = EquipWGData.GetSortAttrListHaveAddByCfg(shenshou_base_struct, eq_struct)
	for k,v in ipairs(self.attr_list) do
		v:SetData(attr_list[k])
	end
	--EquipWGData.GetSortAttrListByCfg()  
	local capability = 0
	capability = AttributeMgr.GetCapability(eq_struct)
	capability = capability + AttributeMgr.GetCapability(shenshou_base_struct)
	self.node_list.cap_value.text.text = capability
end

function ShenShouView:FlushCellRemind()
	local flag = false
	local btn_flag = false
	local num = 0
	local is_visible = ShenShouWGData.Instance:IsShowShenShouRenderRed(self.select_shou_id)
	for k,v in pairs(self.shenshou_cells) do
		local cell_data = ShenShouWGData.Instance:GetOneSlotData(self.select_shou_id, k - 1)
		flag = ShenShouWGData.Instance:GetHasBetterShenShouEquip(cell_data, self.select_shou_id, k)
		--执行cell里的红点方法
		self.shenshou_cells[k]:SetCanOperateIconVisible(is_visible and flag)

		if is_visible and flag then
			num = num + 1
		end
		btn_flag = btn_flag or (is_visible and flag)
	end

	self.node_list.btn_sseq_red:SetActive(btn_flag)
end

-- 刷新出站位
function ShenShouView:FlushChuZhanWei()
	if not self.chuzhanwei_list then return end
	for i,v in ipairs(self.chuzhanwei_list) do
		v:SetData({})
	end

	if CountDownManager.Instance:HasCountDown("move_delay") then
        CountDownManager.Instance:RemoveCountDown("move_delay")
    end
    if self.zz_time > 0 then
    	CountDownManager.Instance:AddCountDown("move_delay", BindTool.Bind(self.UpdataTime, self), BindTool.Bind(self.TimeCompleteCallBack, self), nil, self.zz_time, 1)
    else
    	self:TimeCompleteCallBack()
    end
end

function ShenShouView:FlushSbBigBtnRed()
	for k,v in ipairs(self.ss_accordion_list) do
		if v.btn:FindObj("img_read") then
			v.btn:FindObj("img_read"):SetActive(ShenShouWGData.Instance:GetShenShouRemindBySeries(k))
		end
	end
end

--点击大按钮后刷新小按钮并且跳转到对应的小按钮位置
function ShenShouView:FlushShenShouList(is_delay)
	if self.delay_calc_scroll_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_calc_scroll_quest)
		self.delay_calc_scroll_quest = nil
	end

	local ss_list = ShenShouWGData.Instance:GetShenShouConfigBySeries(self.idx_cache, self.role_level)
	self:InstantSSItem(self.idx_cache)
	local default_select_cell_index = 1
	if self.jump_shenshou_id then
		for k,v in pairs(ss_list) do
			if v.shou_id == self.jump_shenshou_id then
				default_select_cell_index = k
				break
			end
		end
		self.jump_shenshou_id = nil
	else
		for k1,v1 in pairs(ss_list) do
			if ShenShouWGData.Instance:IsShowShenShouRenderRed(v1.shou_id) then
				default_select_cell_index = k1
				break
			end
		end
	end

	self:SelectShenShouCellCallBack(self.ss_cell_list[self.idx_cache][default_select_cell_index])
	--当跳转的索引超过6时会超出屏幕显示位置需要移动位置
	if(default_select_cell_index > 6 or is_delay) then
		self.delay_calc_scroll_quest = GlobalTimerQuest:AddDelayTimer(function()
			self:CalcScroll(self.ss_cell_list[self.idx_cache][default_select_cell_index])
		end, DELAY_TIME)
	end
end

function ShenShouView:FlushShenShouCellHighLight()
	for k, v in ipairs(self.ss_cell_list[self.idx_cache]) do
		v:OnSelectChange(v.data.shou_id == self.select_shou_id)
	end
end

function ShenShouView:FlushShenShouActive()
	local shenshou_list = ShenShouWGData.Instance:GetShenshouList(self.select_shou_id)
	self.big_effect_flag = true
	if shenshou_list then
		for k,v in pairs(shenshou_list.equip_list) do
			if v.item_id == 0 then
				self.big_effect_flag = false
				break
			end
		end
	else
		self.big_effect_flag = false
	end
	--self.node_list.shenshoujihuo_effect:SetActive(self.big_effect_flag)
end

function ShenShouView:OnClickExpandHandler(idx, is_on)
	self.idx_cache = idx

	if not is_on then
		return
	end

	if IsEmptyTable(((self.ss_cell_list or {})[idx] or {})[1] or {}) then
		return
	end

	self:FlushShenShouList()
end

function ShenShouView:CalcScroll(cell)
	local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.canvas.worldCamera,
		cell.view.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
	self.node_list.Content.rect, screen_pos_tbl, UICamera, Vector2(0, 0))
	local per
	local content_size_y = self.node_list.Content.rect.sizeDelta.y
	local togglesv_size_y = self.node_list.ToggleSV.rect.sizeDelta.y
	if content_size_y + local_position_tbl.y < togglesv_size_y then
		per = 0
	else
		local rect = content_size_y - self.node_list.ToggleSV.rect.sizeDelta.y
		per = (content_size_y + local_position_tbl.y - togglesv_size_y + 26) / rect
	end

	per = math.min(per, 1)
	self.node_list.ToggleSV.scroll_rect.verticalNormalizedPosition = per
end

-- 选择神兽回调
function ShenShouView:SelectShenShouCellCallBack(cell)
	if nil == cell or nil == cell.data then return end
	self.select_shou_id = cell.data.shou_id
	self.series = cell.data.series
	self:FlushEquipList()
	self:CreateShenShenShouSkill()
	self:FlushShenShouCellHighLight()
	--self:HandleEquipShenShouOneKey(self.select_shou_id)
	--self:PlayModelEffect(self.select_shou_id) --特效模型加载
end

function ShenShouView:OnClickShenShouCell(cell)
	local cell_data = self.shenshou_cells[cell]:GetData()
	--没有东西的时候,打开背包
	if cell_data == nil or cell_data.item_id == 0 then
		ShenShouWGCtrl.Instance:OpenShenShouBag(self.select_shou_id, self.series)
	end
end

function ShenShouView:HandleEquipShenShouOneKey()
	ShenShouWGData.Instance:HandleEquipShenShouOneKey(self.select_shou_id)
end

-- 跳转到对应的页签
function ShenShouView:JumpToSSItem(series, shou_id)
	self.jump_shenshou_id = shou_id

	if(self.ss_accordion_list[series].btn.toggle.isOn ~= true) then
		self.ss_accordion_list[series].btn.toggle.isOn = true
	else
		self:FlushShenShouList(true)
	end
end

function ShenShouView:UpdataTime(elapse_time, total_time)
end

function ShenShouView:TimeCompleteCallBack()
	local sort_list = ShenShouWGData.Instance:GetZhuZhanSortList()
	for i = 1, MAX_CHU_ZHAN_SLOT_NUM do
		local node = self.node_list["zhuzhanwei_" .. i]
		if node then
			local sort_index = sort_list[i] or MAX_CHU_ZHAN_SLOT_NUM
			node.transform:SetSiblingIndex(sort_index - 1)
		end
	end

	self.is_move = false
	self.node_list["zhuzhan_scoll"].scroll_rect.enabled = true
end



function ShenShouView:OpenShenShouBag()
	ShenShouWGCtrl.Instance:OpenShenShouBag(self.select_shou_id, self.series)
end

function ShenShouView:ShenShouZhuZhan()
	if self.playing_ainm_delay then return end
	local is_active  = ShenShouWGData.Instance:IsShenShouActive(self.select_shou_id)
	if not is_active then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.ZhuZhan_Tip1)
		return
	end

	if ShenShouWGData.Instance:IsShenShouZhuZhan(self.select_shou_id) then
		self.zz_time = 0
		self.is_move = true
		ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ZHUZHAN, self.select_shou_id)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.Cancel_ZhuZhan)
		return
	end
	self.zz_time = 0
	if ShenShouWGData.Instance:IsFullZhuZhan() then
		local list = ShenShouWGData.Instance:GetShenShouZhuZhanList()
		local min_shou_id = self.select_shou_id
		for k,v in pairs(list) do
			if v < min_shou_id then
				min_shou_id = v
			end
		end
		if min_shou_id ~= self.select_shou_id then
			local call_back = function ()
				ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ZHUZHAN, min_shou_id)
				ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ZHUZHAN, self.select_shou_id)
				SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.ExtraTip5)
			end
			self:PlanShenShouZhuZhanAnim(call_back)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.ZhuZhan_Tip)
		end
	else
		local call_back = function ()
			ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ZHUZHAN, self.select_shou_id)
		end
		local list = ShenShouWGData.Instance:GetShenShouZhuZhanList()
		for k,v in pairs(list) do
			if v == self.select_shou_id then
				call_back()
				return
			end
		end
		self:PlanShenShouZhuZhanAnim(call_back)
	end
	self:DoMoveToZhanWei()
end

function ShenShouView:DoMoveToZhanWei()
	if not self.node_list["zhuzhan_scoll"] then
		return
    end

   	local zz_num = ShenShouWGData.Instance:GetZhuZhanNum()
    if self.move_tween then
        self.move_tween:Kill()
        self.move_tween = nil
    end

	for i = 1, MAX_CHU_ZHAN_SLOT_NUM do
		local chuzhan_info = ShenShouWGData.Instance:GetChuZhanInfoByIndex(i)
		if chuzhan_info == nil then
			zz_num = i
			break
		end
	end

    local from = self.node_list["zhuzhan_scoll"].scroll_rect.horizontalNormalizedPosition
    local to = (zz_num + 1) - 5
    to = to > 0 and to or 0
    to = to < 1 and to or 1
    self.move_tween = self.node_list["zhuzhan_scoll"].scroll_rect:DoHorizontalPosition(from, to, 0, nil)
end

function ShenShouView:PlanShenShouZhuZhanAnim(call_back)
	self:ReSetEffectPosition()
	self.node_list["zhuzhan_scoll"].scroll_rect.enabled = false
	self.node_list["shenshou_zhuzhan_effect"]:SetActive(true)
	if call_back then
		call_back()
	end
	-- self.playing_ainm_delay =  GlobalTimerQuest:AddDelayTimer(function ()
	-- 	if call_back then
	-- 		call_back()
	-- 	end
	-- 	GlobalTimerQuest:CancelQuest(self.playing_ainm_delay)
	-- 	self.playing_ainm_delay = nil
	-- end,1)
end

function ShenShouView:ReSetEffectPosition()
	if self.node_list and self.node_list.shenshou_zhuzhan_effect then
		--self.node_list.shenshou_zhuzhan_effect.transform.localPosition = Vector3(572, 144, 0)
		self.node_list.shenshou_zhuzhan_effect:SetActive(false)
	end
end

function ShenShouView:GetSeries()
	return self.series
end

function ShenShouView:SetShenShouImage()
	local actor_num = ShenShouWGData.Instance:GetActorNum(self.select_shou_id)

	if self.cache_model == actor_num then
		return
	end
	self.cache_model = actor_num
end

function ShenShouView:PlayModelEffect(shou_id)
	-- local model_info = ShenShouWGData.Instance:GetShenShouCfg(shou_id)
	-- local bundle_name, asset_name = ResPath.GetMonsterModel(model_info.icon_id)
	-- self.node_list.ss_model:ChangeAsset(bundle_name, asset_name, false)
end

-----------------------------------------------------------------

--------------------------------------------------------------------------
ShouChuZhanWeiRender = ShouChuZhanWeiRender or BaseClass(BaseRender)

function ShouChuZhanWeiRender:__delete()
	self.parent = nil

	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
		self.delay_time = nil
	end

	--self:ClearIconEffect()
end

function ShouChuZhanWeiRender:LoadCallBack()
	self:SetClickCallBack(BindTool.Bind(self.OnClick,self))
end

local end_pos = Vector2(0, 0) --结束位置
local scale1 = Vector3(0.9, 0.9, 0.9) --大小0.9
local scale2 = Vector3(0.9, 0.9, 0.9) --大小1

function ShouChuZhanWeiRender:DoMoveTween(eff_level)
	local star_pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera,self.parent.node_list.ss_head.rect.position)--999999
    local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.view.rect, star_pos, UICamera, Vector2(0, 0))

    self.node_list.icon:SetActive(true)
    --self.node_list.icon.rect.anchoredPosition = local_pos_tbl
    self.node_list.icon.rect.localScale = scale1
    local move_tween_1 = nil

    -- if self.parent and self.parent.node_list.shenshou_zhuzhan_effect then
    -- 	local index = self:GetIndex()
    -- 	local fly_pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera,self.parent.node_list['zhuzhanwei_' .. index].rect.position)
    -- 	local _, local_pos_tbl2 = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.parent.node_list.shenshou_effect_view.rect, fly_pos, UICamera, Vector2(0, 0))
   	-- 	move_tween_1 = self.parent.node_list.shenshou_zhuzhan_effect.rect:DOAnchorPos(local_pos_tbl2,0.8)
   	-- 	move_tween_1:SetEase(DG.Tweening.Ease.OutQuad)
   	-- end

    -- local move_tween_1 = self.node_list.icon.rect:DOAnchorPos(end_pos,0.6)
    -- move_tween_1:SetEase(DG.Tweening.Ease.InOutCirc)

    -- local scale_tween_1 = self.node_list.icon.rect:DOScale(scale1,0.3)
    -- scale_tween_1:SetEase(DG.Tweening.Ease.InQuad)

    local scale_tween_2 = self.node_list.icon.rect:DOScale(scale2,0.3)
    scale_tween_2:SetEase(DG.Tweening.Ease.InQuad)

    -- local rotate_tween_1 = self.node_list["icon"].rect:DORotate(Vector3(0, 0, -360 * 2), 1, DG.Tweening.RotateMode.FastBeyond360)
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end
    self.sequence = DG.Tweening.DOTween.Sequence()

    --self.sequence:Append(scale_tween_1)
    -- self.sequence:AppendInterval(0.5)
    if move_tween_1 then
    	self.sequence:Append(move_tween_1)
    	self.sequence:AppendInterval(0.6)
    end

    GlobalTimerQuest:AddDelayTimer(function ()
    	self.parent:ReSetEffectPosition()
    end,0.8)

    self.sequence:Append(scale_tween_2)
    self.sequence:AppendCallback(function ()
		ShenShouWGCtrl.Instance:ShakeRoot()
		if eff_level and eff_level > 0 then
	        local bundle = "effects2/prefab/ui/kapaiza/ui_kapai_za3_prefab"
	        local asset = "UI_kapai_za3"
	        EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list.effect_doudong.transform, 0.4,
	                nil, nil, Vector3(20,20,20))
	    end
	   	--self:PlayIconAppearEffect()
    end)
    -- self.sequence:AppendInterval(0.1)
end

function ShouChuZhanWeiRender:PlayIconAppearEffect()
	self:ClearIconEffect()
	self.node_list.icon_appear_effect:SetActive(true)
	self.icon_appear_delay =  GlobalTimerQuest:AddDelayTimer(function ()
		self.node_list.icon_appear_effect:SetActive(false)
		GlobalTimerQuest:CancelQuest(self.icon_appear_delay)
		self.icon_appear_delay = nil
	end,1.2)
end

function ShouChuZhanWeiRender:ClearIconEffect()
	if self.icon_appear_delay then
		GlobalTimerQuest:CancelQuest(self.icon_appear_delay)
		self.icon_appear_delay = nil
		self.node_list.icon_appear_effect:SetActive(false)
	end
end

function ShouChuZhanWeiRender:OnClick()
	if self.act_flag then
		if self.has_chuzhan then
			-- 跳转
			self.parent:JumpToSSItem(self.series, self.chuzhan_shou_id)
		else
			local is_has = ShenShouWGData.Instance:IsShenShouActive1()
			if not is_has then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.OpenLimit_3)
				return
			end

			ShanHaiJingWGCtrl.Instance:OpenShenShouSZ()
		end
	-- 未激活
	else
		if self.open_info and self.open_info.need_show_tip then
			SysMsgWGCtrl.Instance:ErrorRemind(self.open_info.des)
			return
		elseif self.open_info and self.open_info.need_show_view then
			ShenShouWGCtrl.Instance:OpenExtraZhuZhanTip(self.index,function (enable)
				if self.parent and self.parent.zz_time then
					self.parent.zz_time = 0
				end
				self:WGCtrlEffect(enable)
			end)
			return
		elseif self.open_info and self.open_info.need_show_item_tips then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.open_info.need_show_item_tips, is_bind = 1})
			return
		else
			if self.open_info then
				if self.parent and self.parent.zz_time then
					self.parent.zz_time = 0
				end
				self:WGCtrlEffect(self.open_info.remind)
			end
			-- 请求开启槽位
			ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ADD_ZHUZHAN,self.index)
		end
	end
end

function ShouChuZhanWeiRender:WGCtrlEffect(enable)
	if enable then
		self.node_list.img_add:SetActive(false)
		self.delay_time = GlobalTimerQuest:AddDelayTimer(function ()
			self.node_list.img_add:SetActive(true)
			self:Flush()
			if self.delay_time then
				GlobalTimerQuest:CancelQuest(self.delay_time)
				self.delay_time = nil
			end
		end,0)
	end
end

function ShouChuZhanWeiRender:OnFlush()
	-- 槽位激活标识
	local act_flag_list = ShenShouWGData.Instance:GetExtraZhuZhanInfo()
	if not act_flag_list then
		return
	end

	local act_flag = act_flag_list[self.index - 1] == 1
	-- 出战位信息
	local chuzhan_info = ShenShouWGData.Instance:GetChuZhanInfoByIndex(self.index)
	local has_info = chuzhan_info ~= nil
	self.node_list.icon:SetActive(act_flag and chuzhan_info ~= nil)
	self.node_list.img_add:SetActive(act_flag and chuzhan_info == nil and self.delay_time == nil)
	self.node_list.img_lock:SetActive(not act_flag)
	self.node_list.des:SetActive(not act_flag or chuzhan_info ~= nil)
	self.node_list.remind:SetActive(false)

	self.act_flag = act_flag
	self.need_do_tween = self.has_chuzhan == false and chuzhan_info ~= nil
	self.has_chuzhan = chuzhan_info ~= nil
	-- 已激活
	if act_flag then
		if chuzhan_info then
			self.chuzhan_shou_id = chuzhan_info.shou_id
			local shou_cfg = ShenShouWGData.Instance:GetShenShouCfg(chuzhan_info.shou_id)
			if shou_cfg then
				self.series = shou_cfg.series
				local bundle, asset = ResPath.GetF2SHJImgPath(shou_cfg.icon_head_circle)
				self.node_list.icon.image:LoadSprite(bundle, "icon_" .. asset,function ()
					self.node_list.icon.image:SetNativeSize()
					if self.need_do_tween then
						self.node_list.icon:SetActive(false)
						self:DoMoveTween(shou_cfg.series-1)
					end
				end)

				-- 品质底
				self.node_list.icon_bg.image:LoadSprite(ResPath.GetCommonImages('a3_ty_wpk_' .. shou_cfg.series + 1))
				self.node_list.des.text.text = ToColorStr(shou_cfg.name,ITEM_COLOR[shou_cfg.series + 1])
			end
		elseif self.delay_time == nil then
			self.node_list.icon_bg.image:LoadSprite(ResPath.GetCommonImages('a3_ty_wpk_0'))
		end
	else
		self.open_info = ShenShouWGData.Instance:GetChuZhanOpenLimitByIndex(self.index)
		self.node_list.des.text.text = self.open_info.des
		self.node_list.remind:SetActive(self.open_info.remind)
		self.node_list.icon_bg.image:LoadSprite(ResPath.GetCommonImages('a3_ty_wpk_0'))
	end
end

---------------------------------------- ShenShouQualityTypeItem ----------------------------------------
ShenShouQualityTypeItem = ShenShouQualityTypeItem or BaseClass(BaseRender)

function ShenShouQualityTypeItem:OnFlush()
	if self.data == nil then return end
	self.node_list.quality_name.text.text = self.data.name
	local red_flag = ShenShouWGData.Instance:GetShenShouRemindBySeries(self.data.series)
	self.node_list.quality_red:SetActive(red_flag)
end

function ShenShouQualityTypeItem:SetSelect(is_select)
	self.node_list.img_hl:SetActive(is_select)
end