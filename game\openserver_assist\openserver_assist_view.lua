OpenServerAssistView = OpenServerAssistView or BaseClass(SafeBaseView)

function OpenServerAssistView:__init()
	self.view_style = ViewStyle.Window
	self:SetMaskBg(false)
	--self.default_index = TabIndex.luxury_gift

    self:AddViewResource(0, "uis/view/openserver_assist_ui_prefab", "layout_openserver_assist")
    self:AddViewResource(TabIndex.luxury_gift, "uis/view/openserver_assist_ui_prefab", "layout_luxury_gift")
	-- self:AddViewResource(TabIndex.high_point, "uis/view/openserver_assist_ui_prefab", "layout_high_point")
	self:AddViewResource(TabIndex.gudao_jizhan, "uis/view/openserver_assist_ui_prefab", "layout_gudao_jizhan")
    self:AddViewResource(0, "uis/view/openserver_assist_ui_prefab", "VerticalTabbar")
end

function OpenServerAssistView:__delete()

end

function OpenServerAssistView:OpenCallBack()
    if nil ~= self.money_bar then
		self.money_bar:AddAllListen()
	end

	if nil ~= self.tabbar then
		self.tabbar:AddAllListen()
	end
end

function OpenServerAssistView:CloseCallBack()
    if nil ~= self.money_bar then
		self.money_bar:UnAllListen()
	end

	if nil ~= self.tabbar then
		self.tabbar:UnAllListen()
	end
end

function OpenServerAssistView:LoadCallBack()
    self:InitTabbar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function OpenServerAssistView:ReleaseCallBack()
	self:ReleaseLuxuryGift()
	self:ReleaseHighPoint()

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end
end

function OpenServerAssistView:LoadIndexCallBack(index, loaded_times)
    if index == TabIndex.luxury_gift then
		self:InitLuxuryGift()
  --   elseif index == TabIndex.high_point then
		-- self:InitHighPoint()
	elseif index == TabIndex.gudao_jizhan then
		self:InitGuDaoJiZhan()
    end
end

function OpenServerAssistView:InitTabbar()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
        local ver_path = "uis/view/openserver_assist_ui_prefab"
        local name_list, remind_list = OpenServerAssistWGData.Instance:GetActivityRemindList()

        self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
        self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.FlushTabVisible, self))
		self.tabbar:Init(name_list, nil, ver_path, nil, remind_list)
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.OpenServerAssistView, self.tabbar)
	end
end

function OpenServerAssistView:FlushTabVisible()
    if nil == self.tabbar then
		return
	end

    local act_list = OpenServerAssistWGData.Instance:GetServerAssistActivityList()
    if IsEmptyTable(act_list) then
        return
    end

    for k, v in ipairs(act_list) do
        local can_show = OpenServerAssistWGData.Instance:GetRandActivityCanShowByType(v.activity_type)
        self.tabbar:SetVerToggleVisble(v.tab_index, can_show)
    end
end

function OpenServerAssistView:ActivityChangeFlushView()
	self:FlushTabVisible()

	local act_list = OpenServerAssistWGData.Instance:GetServerAssistActivityList()
	if IsEmptyTable(act_list) then
		return
	end

	for k, v in ipairs(act_list) do
		if self.show_index == v.tab_index then
			local can_show = OpenServerAssistWGData.Instance:GetRandActivityCanShowByType(v.activity_type)
			if can_show then
				return
			end
		end
	end

	for k, v in ipairs(act_list) do
		local can_show = OpenServerAssistWGData.Instance:GetRandActivityCanShowByType(v.activity_type)
		if can_show then
			self.default_index = v.tab_index
			self:ChangeToIndex(v.tab_index)
			return
		end
	end

	self:Close()
end

function OpenServerAssistView:ShowIndexCallBack()

end

function OpenServerAssistView:OnFlush(param_t, index)
	if self.default_index == 0 then
		self.default_index = OpenServerAssistWGData.Instance:GetViewDefaultIndex()
		self:ChangeToIndex(self.default_index)
		return
	end

	for k,v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.luxury_gift then
				self:FlushLuxuryGift()
			-- elseif index == TabIndex.high_point then
			-- 	self:FlushHighPointPanel()
			end
        end
    end
end
