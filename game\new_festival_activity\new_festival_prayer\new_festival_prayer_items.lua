NewFestivalPrayerRewardRender = NewFestivalPrayerRewardRender or BaseClass(BaseRender)
function NewFestivalPrayerRewardRender:__init()
    self.normal_item = NewFestivalPrayerRewardItem.New(self.node_list.normal_item)

    self.high_item_list = {}
    for i = 1, 2 do
        self.high_item_list[i] = NewFestivalPrayerRewardItem.New(self.node_list["high_item_" .. i])
        self.high_item_list[i]:SetIsAdvancedItem(true)
    end  

    self:SetViewInfo()

    XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBtn, self))
end

function NewFestivalPrayerRewardRender:__delete()
    if self.normal_item then
        self.normal_item:DeleteMe()
        self.normal_item = nil
    end

    if self.high_item_list then
        for k, v in pairs(self.high_item_list) do
            v:DeleteMe()
        end
        self.high_item_list = nil
    end
end

function NewFestivalPrayerRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.prayer_count.text.text = self.data.cfg.prayer_count
    self.normal_item:SetData({reward_data = self.data.cfg.normal_reward[0], reward_flag = self.data.reward_flag, 
                            prayer_count = self.data.cfg.prayer_count, can_get_remind = self.data.can_get_remind})
    for k, v in ipairs(self.high_item_list) do
        v:SetData({reward_data = self.data.cfg.advanced_reward[k - 1], reward_flag = self.data.reward_flag, 
                prayer_count = self.data.cfg.prayer_count, can_get_remind = self.data.can_get_remind})
    end

    local prayer_count = NewFestivalPrayerWGData.Instance:GetPrayerCount()
    self.node_list["left_line_1"]:SetActive(self.index ~= 1 and prayer_count < self.data.cfg.prayer_count)
    self.node_list["left_line_2"]:SetActive(self.index ~= 1 and prayer_count >= self.data.cfg.prayer_count)
    self.node_list["right_line_1"]:SetActive(self.data.next_prayer_count > 0 and prayer_count < self.data.next_prayer_count)
    self.node_list["right_line_2"]:SetActive(self.data.next_prayer_count > 0 and prayer_count >= self.data.next_prayer_count)

    self.node_list["dian_img_1"]:SetActive(prayer_count < self.data.cfg.prayer_count)
    self.node_list["dian_img_2"]:SetActive(prayer_count >= self.data.cfg.prayer_count)
    self.node_list.get_btn:SetActive(self.data.can_get_remind)
end

function NewFestivalPrayerRewardRender:SetViewInfo()
    local bg_bundle, bg_asset = ResPath.GetNewFestivalRawImages("qy_di_3")
	self.node_list["bg"].raw_image:LoadSprite(bg_bundle, bg_asset, function ()
		self.node_list["bg"].raw_image:SetNativeSize()
	end)

    local count_bg_bundle, count_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_di_3")
	self.node_list["prayer_count_bg"].image:LoadSprite(count_bg_bundle, count_bg_asset, function ()
		self.node_list["prayer_count_bg"].image:SetNativeSize()
	end)

    local line1_bg, line1_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_slider_3")
    self.node_list["left_line_1"].image:LoadSprite(line1_bg, line1_asset)
    self.node_list["right_line_1"].image:LoadSprite(line1_bg, line1_asset)

    local line2_bg, line2_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_slider_4")
    self.node_list["left_line_2"].image:LoadSprite(line2_bg, line2_asset)
    self.node_list["right_line_2"].image:LoadSprite(line2_bg, line2_asset)

    local dian_img1_bundle, dian_img1_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_dian_1")
	self.node_list["dian_img_1"].image:LoadSprite(dian_img1_bundle, dian_img1_asset, function ()
		self.node_list["dian_img_1"].image:SetNativeSize()
	end)

    local dian_img2_bundle, dian_img2_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_dian_2")
	self.node_list["dian_img_2"].image:LoadSprite(dian_img2_bundle, dian_img2_asset, function ()
		self.node_list["dian_img_2"].image:SetNativeSize()
	end)
end

function NewFestivalPrayerRewardRender:OnClickGetBtn()
    if IsEmptyTable(self.data) then
        return
    end
    
    if self.data.can_get_remind then
        NewFestivalPrayerWGCtrl.Instance:ReqNewFestivalPrayerInfo(OA_NEWYEAR_PRAYER_OPERATE_TYPE.GET_REWARD, self.data.cfg.seq)
    end
end
-------------------NewFestivalPrayerRewardItem--------
NewFestivalPrayerRewardItem = NewFestivalPrayerRewardItem or BaseClass(BaseRender)
function NewFestivalPrayerRewardItem:__init()
    self.reward_item = ItemCell.New(self.node_list.item_node)
end

function NewFestivalPrayerRewardItem:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end

    self.is_advanced_item = nil
end

function NewFestivalPrayerRewardItem:SetIsAdvancedItem(bool)
    self.is_advanced_item = bool
end

function NewFestivalPrayerRewardItem:OnFlush()
    if IsEmptyTable(self.data.reward_data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
    end
    
    self.reward_item:SetData(self.data.reward_data)
    local is_buy_advanced = NewFestivalPrayerWGData.Instance:GetIsBuyAdvancedFlag()
    if not self.is_advanced_item then
        self.node_list.item_lock:SetActive(false)
        self.node_list.item_isget:SetActive(self.data.reward_flag == NEWYEAR_PRAYER_REWARD_FLAG.GET_NORMAL or 
                                            self.data.reward_flag == NEWYEAR_PRAYER_REWARD_FLAG.GET_ADVANCED)
        self.node_list.red_img:SetActive(self.data.can_get_remind and self.data.reward_flag == NEWYEAR_PRAYER_REWARD_FLAG.NOT_GET)                                   
    else
        self.node_list.item_lock:SetActive(not is_buy_advanced)
        self.node_list.item_isget:SetActive(self.data.reward_flag == NEWYEAR_PRAYER_REWARD_FLAG.GET_ADVANCED)
        self.node_list.red_img:SetActive(is_buy_advanced and self.data.can_get_remind)
    end
end

------------------NfaPrayerGuDingRewardItem----------
NfaPrayerGuDingRewardItem = NfaPrayerGuDingRewardItem or BaseClass(BaseRender)
function NfaPrayerGuDingRewardItem:__init()
    self.reward_item = ItemCell.New(self.node_list.item_node)
end

function NfaPrayerGuDingRewardItem:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end

    self.is_advanced_item = nil
end

function NfaPrayerGuDingRewardItem:SetIsAdvancedItem(bool)
    self.is_advanced_item = bool
end

function NfaPrayerGuDingRewardItem:OnFlush()
    if IsEmptyTable(self.data.reward_data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
    end
    
    self.reward_item:SetData(self.data.reward_data)
    local is_buy_advanced = NewFestivalPrayerWGData.Instance:GetIsBuyAdvancedFlag()
    if not self.is_advanced_item then
        self.node_list.item_lock:SetActive(false)
        self.node_list.item_isget:SetActive(self.data.reward_flag == NEWYEAR_PRAYER_REWARD_FLAG.GET_NORMAL or 
                                            self.data.reward_flag == NEWYEAR_PRAYER_REWARD_FLAG.GET_ADVANCED)
    else
        self.node_list.item_lock:SetActive(not is_buy_advanced)
        self.node_list.item_isget:SetActive(self.data.reward_flag == NEWYEAR_PRAYER_REWARD_FLAG.GET_ADVANCED)
    end
end

------------------------NfaPrayerTaskItem------------
NfaPrayerTaskItem = NfaPrayerTaskItem or BaseClass(BaseRender)
function NfaPrayerTaskItem:__init()
    self.reward_item = ItemCell.New(self.node_list.item_pos)

    XUI.AddClickEventListener(self.node_list["jump_btn"], BindTool.Bind(self.OnClickJumpView, self))
    XUI.AddClickEventListener(self.node_list["get_btn"], BindTool.Bind(self.OnClickGetReward, self))
end

function NfaPrayerTaskItem:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function NfaPrayerTaskItem:OnFlush()
    if not self.data then
        return
    end

    local cfg = self.data.cfg
    self.reward_item:SetData(cfg.reward_list[0])
    -- local color = cfg.desc_color or "#ffffff"
    -- local task_description = ToColorStr(cfg.task_description, color)

    local show_str = string.format("(%s/%s)", self.data.task_process, cfg.param1)
    local prog_color = self.data.task_process >= cfg.param1 and COLOR3B.L_GREEN or COLOR3B.L_RED
    show_str = ToColorStr(show_str, prog_color)
    self.node_list.task_desc.text.text = string.format(Language.NewFestivalActivity.PrayerTaskPro, cfg.task_description, show_str)
    
    self.node_list.task_slider.slider.value = self.data.task_process / cfg.param1
    
    self.node_list.jump_btn:SetActive(self.data.task_status == REWARD_STATE_TYPE.UNDONE)
    self.node_list.get_btn:SetActive(self.data.task_status == REWARD_STATE_TYPE.CAN_FETCH)
    self.node_list.is_get:SetActive(self.data.task_status == REWARD_STATE_TYPE.FINISH)

    self:ImgAsset()
end

function NfaPrayerTaskItem:ImgAsset()
    local prayer_info_cfg = NewFestivalActivityWGData.Instance:GetPrayerInfoCfg()
    self.node_list["jump_text"].text.color = Str2C3b(prayer_info_cfg.text_color_9)
    self.node_list["get_text"].text.color = Str2C3b(prayer_info_cfg.text_color_10)
    self.node_list["task_desc"].text.color = Str2C3b(prayer_info_cfg.task_desc_color)
    local bg_bundle, bg_asset = ResPath.GetNewFestivalRawImages("qy_di_4")
	self.node_list["bg"].raw_image:LoadSprite(bg_bundle, bg_asset, function ()
		self.node_list["bg"].raw_image:SetNativeSize()
	end)

    local slider_bg_bundle, slider_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_slider_1")
	self.node_list["slider_bg"].image:LoadSprite(slider_bg_bundle, slider_bg_asset)

    local slider_fill_bundle, slider_fill_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_slider_2")
	self.node_list["slider_fill"].image:LoadSprite(slider_fill_bundle, slider_fill_asset)

    local jump_btn_bundle, jump_btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jrsc_btn2")
	self.node_list["jump_btn"].image:LoadSprite(jump_btn_bundle, jump_btn_asset, function ()
		self.node_list["jump_btn"].image:SetNativeSize()
	end)

    local get_btn_bundle, get_btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jrsc_btn1")
	self.node_list["get_btn"].image:LoadSprite(get_btn_bundle, get_btn_asset, function ()
		self.node_list["get_btn"].image:SetNativeSize()
	end)

    local is_get_bundle, is_get_asset = ResPath.GetNewFestivalActImages("a3_jrhd_jrsc_btn1")
	self.node_list["is_get"].image:LoadSprite(is_get_bundle, is_get_asset, function ()
		self.node_list["is_get"].image:SetNativeSize()
	end)
end

function NfaPrayerTaskItem:OnClickJumpView()
    if not self.data then
        return
    end

    local cfg = self.data.cfg

    if cfg.open_panel ~= "" then
        if cfg.act_type == "" then --非活动
            FunOpen.Instance:OpenViewNameByCfg(cfg.open_panel)
        else
            local is_act_open = ActivityWGData.Instance:GetActivityIsOpen(cfg.act_type) --活动是否开启
            if is_act_open then
                FunOpen.Instance:OpenViewNameByCfg(cfg.open_panel)
            else
                TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ActivateNoOpen)
            end
        end
    end
end

function NfaPrayerTaskItem:OnClickGetReward()
    if not self.data then
        return
    end

    if self.data.task_status == REWARD_STATE_TYPE.CAN_FETCH then
        NewFestivalPrayerWGCtrl.Instance:ReqNewFestivalPrayerInfo(OA_NEWYEAR_PRAYER_OPERATE_TYPE.GET_TASK_REWARD, self.data.task_id)
    end
end


------------------------NfaPrayerBuyItem------------
NfaPrayerBuyItem = NfaPrayerBuyItem or BaseClass(BaseRender)
function NfaPrayerBuyItem:__init()
    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuyBtn, self))
end

function NfaPrayerBuyItem:__delete()

end

function NfaPrayerBuyItem:OnFlush()
    if not self.data then
        return
    end

    local cfg = self.data.cfg
    self.node_list.add_paryer.text.text = cfg.prayer_num
    self.node_list.tuijian:SetActive(self.data.is_tuijian)
    self.node_list.buy_desc.text.text = string.format(Language.NewFestivalActivity.PrayerBuyDesc1, cfg.prayer_num, 
                                                        self.data.buy_times, cfg.limit_num)
    self.node_list.zhekou_text.text.text = string.format(Language.NewFestivalActivity.PrayerBuyDiscount, cfg.discount)
    self.node_list.old_price_text.text.text = cfg.lingyu_num
    self.node_list.now_price_text.text.text = math.floor(cfg.lingyu_num * cfg.discount / 10)
    XUI.SetButtonEnabled(self.node_list["btn_buy"], self.data.buy_times < cfg.limit_num)
end

function NfaPrayerBuyItem:OnClickBuyBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    if self.data.buy_times >= cfg.limit_num then
        return
    end

    TipWGCtrl.Instance:OpenAlertTips(string.format(Language.NewFestivalActivity.PrayerBuyTips, math.floor(cfg.lingyu_num * cfg.discount / 10)), function ()
        NewFestivalPrayerWGCtrl.Instance:ReqNewFestivalPrayerInfo(OA_NEWYEAR_PRAYER_OPERATE_TYPE.BUY_PRAYER, self.data.seq)
    end)
end