ShenyuanBossSceneLogic = ShenyuanBossSceneLogic or BaseClass(CommonFbLogic)

function ShenyuanBossSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil

	self.create_obj_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
end

function ShenyuanBossSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
	self.has_get_team_target = nil

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
    end
    
    if self.guaji_change_event then
        GlobalEventSystem:UnBind(self.guaji_change_event)
        self.guaji_change_event = nil
    end
    if self.complete_gather then
        GlobalEventSystem:UnBind(self.complete_gather)
        self.complete_gather = nil
    end
    if self.start_gather then
        GlobalEventSystem:UnBind(self.start_gather)
        self.start_gather = nil
    end
end

function ShenyuanBossSceneLogic:Enter(old_scene_type, new_scene_type)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
    ViewManager.Instance:Close(GuideModuleName.WorldServer)
    GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
    MainuiWGCtrl.Instance:SetBtnLevel(true)
    BaseFbLogic.SetLeaveFbTip(true)
    BossWGCtrl.Instance:EnterSceneCallback()
    BossWGData.Instance:SetBossEnterFlag(true)
    BossWGCtrl.Instance:OpenShenYuanHurtView()

    FuhuoWGCtrl.Instance:SetFuhuoMustCallback(BindTool.Bind(self.FuHuoCallBack, self))
    self.start_gather = GlobalEventSystem:Bind(ObjectEventType.START_GATHER,BindTool.Bind(self.GatherPickObjStart, self))
    self.complete_gather = GlobalEventSystem:Bind(ObjectEventType.COMPLETE_GATHER,BindTool.Bind(self.GatherPickObjEnd, self))
    self.guaji_change_event = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, BindTool.Bind(self.BindGuaJiFireEvent,self))

    local view = BossWGCtrl.Instance:GetHurtView()
    ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIRightTopChangeList(view)
end


function ShenyuanBossSceneLogic:BindGuaJiFireEvent(guaji_type)
	if guaji_type == GuajiType.Auto then
        local pick_obj = self:GetPickedObj()
        if pick_obj then
            self:DoGatherPickObj()
        else
            self:SetCurGatherState(false)
        end
	end
end

function ShenyuanBossSceneLogic:FuHuoCallBack(use_type)
    local tarck_type, track_role_uuid = self:GetTrackRoleInfo()
	if tarck_type == OBJ_FOLLOW_TYPE.TEAM then
		return
	end
    
	use_type = use_type or FuHuoType.Common
	if use_type == FuHuoType.Common then
		self:CommonMoveCallBack()
	else
		self:HereFuHuoCallBack()
	end
end

function ShenyuanBossSceneLogic:CommonMoveCallBack()
    local scene_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
    local role_x,role_y = role:GetLogicPos()
    local boss_data = BossWGData.Instance:GetShenYuanBossInfoBySceneId(scene_id)
    if IsEmptyTable(boss_data) then
        return
    end

    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
    self:ResetRoleAndCameraDir()
    local pos = Split(boss_data.boss_pos, ",") 
    if role_x == tonumber(pos[1]) and role_y == tonumber(pos[2]) then
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    else
        local call_back = function()
            local pick_obj = self:GetPickedObj()
            if pick_obj then
                self:DoGatherPickObj()
            else
                GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
            end
        end
        GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)

        MoveCache.SetEndType(MoveEndType.FightByMonsterId)
        GuajiCache.monster_id = boss_data.boss_id
        MoveCache.param1 = boss_data.boss_id
        local range = BossWGData.Instance:GetMonsterRangeByid(boss_data.boss_id)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, tonumber(pos[1]), tonumber(pos[2]), range)
    end
end

function ShenyuanBossSceneLogic:HereFuHuoCallBack()
    local pick_obj = self:GetPickedObj()
    if pick_obj then
        self:DoGatherPickObj()
        return
    end

	local select_obj = nil
	if GuajiCache.target_obj then
		select_obj = GuajiCache.target_obj
	end

	if select_obj and not select_obj:IsDeleted() and Scene.Instance:IsEnemy(select_obj) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiCache.target_obj = select_obj
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		self:CommonMoveCallBack()
	end

end

function ShenyuanBossSceneLogic:OnObjCreate(obj)
    if obj and obj:GetType() == SceneObjType.GatherObj then
        local pick_obj = self:GetPickedObj()
        if pick_obj then
            self:DoGatherPickObj()
            return
        end
    end

    local target_obj = MainuiWGData.Instance:GetTargetObj()
    if obj and not target_obj and not SceneObj.select_obj and self:IsEnemy(obj) and not GuajiCache.target_obj
    and obj:GetType() == SceneObjType.Monster then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
	end
end


function ShenyuanBossSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	if self:RolePickUpFallItem() then
		return true
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
        local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
        target_obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
		if target_obj ~= nil then
            MainuiWGCtrl.Instance:SetTargetObj(target_obj)
        else
            target_obj = Scene.Instance:SelectObjHelper(SceneObjType.Role, x, y, distance_limit, SelectType.Enemy)
            if target_obj ~= nil and self:IsRoleEnemy(target_obj, main_role) then
                MainuiWGCtrl.Instance:SetTargetObj(target_obj)
            end
		end
	end
	local target_obj = MainuiWGData.Instance:GetTargetObj()
	local mosnter_type = nil
	if target_obj ~= nil and target_obj:GetType() == SceneObjType.Monster then
		mosnter_type = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[target_obj:GetVo().monster_id].type
	end
	target_obj = MainuiWGData.Instance:GetTargetObj()
	if (target_obj == nil  or (target_obj ~= nil and mosnter_type ~= MONSTER_TYPE.BOSS)) then
		BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
		return
	end
end

-- 获取是否可以采集
function ShenyuanBossSceneLogic:GetPickedObj()
    local index = nil
	local server_id = RoleWGData.Instance:GetOriginServerId()
	local plat_type = RoleWGData.Instance:GetPlatType()
	if server_id ~= nil then
        index = CrossServerWGData.Instance:GetCrossServerData(server_id, plat_type)
    end

    if index == nil then
        return nil
    end
    
    local gather_list = Scene.Instance:GetObjListByType(SceneObjType.GatherObj)
    if IsEmptyTable(gather_list) then
        return nil
    end

    for k, obj in pairs(gather_list) do
        if obj and not obj:IsDeleted() and obj:GetVo() and obj:GetVo().param == index then
            if WorldServerWGData.Instance:GetCanPickBox() then
                return obj
            end
        end
    end

    return nil
end

-- 获取挂机打怪的位置
function ShenyuanBossSceneLogic:GetGuajiPos()
    local pick_obj = self:GetPickedObj()
    if pick_obj then
        return pick_obj:GetVo().pos_x, pick_obj:GetVo().pos_y
    end

	local target_x = nil
    local target_y = nil
    local scene_id = Scene.Instance:GetSceneId()
    local cfg = BossWGData.Instance:GetShenYuanBossCfgSceneId(scene_id)
    if cfg ~= nil and cfg[1] then
    	local t = Split(cfg[1].boss_pos, ",")
    	if t ~= nil and #t == 2 then
    		target_x = tonumber(t[1])
    		target_y = tonumber(t[2])
        end
        local monster_id = cfg[1].boss_id
        local obj = Scene.Instance:GetMonstObjByMonstID(monster_id)
        if obj and not obj:IsDeleted() then
            return target_x, target_y
        end
    end

	return target_x, target_y
end

-- 获取挂机打怪的位置
function ShenyuanBossSceneLogic:GetGuiJiMonsterPos()
	MainuiWGCtrl.Instance:ResetLightBoss()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = nil
    local target_y = nil
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end

-- 是否是挂机打怪的敌人
function ShenyuanBossSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

function ShenyuanBossSceneLogic:Out()
    CommonFbLogic.Out(self)
    GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
    BossWGCtrl.Instance:CloseHurtView()
	if BossWGData.Instance:GetIsEnterInScene() then
		BossWGCtrl.Instance:OpenBossViewByScene()
    end
    FuhuoWGCtrl.Instance:ClearFuhuoMustCallback()
    WorldServerWGData.Instance:ClearPickState()
    self:SetCurGatherState(false)
    if self.guaji_change_event then
        GlobalEventSystem:UnBind(self.guaji_change_event)
        self.guaji_change_event = nil
    end
    if self.complete_gather then
        GlobalEventSystem:UnBind(self.complete_gather)
        self.complete_gather = nil
    end
    if self.start_gather then
        GlobalEventSystem:UnBind(self.start_gather)
        self.start_gather = nil
    end

    local view = BossWGCtrl.Instance:GetHurtView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
end

-- 获取挂机打怪的敌人(优先级： 优先打怪，如果点击前往击杀则优先打BOSS)
function ShenyuanBossSceneLogic:GetGuajiCharacter()
	local is_need_stop = false

    local target_obj = self:GetMonster()
	if target_obj ~= nil then
        is_need_stop = true
		return target_obj, nil, is_need_stop
	end

	if target_obj == nil then
		target_obj, is_need_stop = self:GetNormalRole()
		return target_obj, nil, is_need_stop
	end
end

function ShenyuanBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local info = self:GetGuaJiInfo()
	local is_stop = info ~= nil

	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if info ~= nil then
				if not v:IsDeleted() then
					local pos_x, pos_y = v:GetLogicPos()
					local dis = GameMath.GetDistance(info.x, info.y, pos_x, pos_y, false)
					if dis <= info.aoi_range * info.aoi_range then
						GuajiCache.target_obj = v
						return v, is_stop
					end
				end
			else
				GuajiCache.target_obj = v
				return v, is_stop
			end
		end
	end

	return nil, is_stop
end

function ShenyuanBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()

		local info = self:GetGuaJiInfo()
		if info ~= nil then
			distance_limit = info.aoi_range * info.aoi_range
			x = info.x
			y = info.y
		end	

		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function ShenyuanBossSceneLogic:GetNextSelectTargetId()
	-- 切换目标时，挂机模式需要变成自动
	if GuajiCache.guaji_type == GuajiType.Monster then
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
	return BaseSceneLogic.GetNextSelectTargetId(self)
end

function ShenyuanBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function ShenyuanBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

-- function ShenyuanBossSceneLogic:AtkTeamLeaderTarget()
-- 	local team_leader_info = SocietyWGData.Instance:GetTeamLeader() or {}
-- 	local leader = Scene.Instance:GetRoleByRoleId(team_leader_info.role_id)
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

-- 	if not leader then
-- 		return
-- 	end

-- 	self.has_get_team_target = leader:GetVo() and leader:GetVo().obj_id or -1

-- 	if not leader:IsAtkPlaying() then
-- 		return
-- 	end

-- 	local target_obj
-- 	if leader:IsAtkPlaying() then
-- 		target_obj = leader:GetAttackTarget()
-- 		if not target_obj then
-- 			return
-- 		end
-- 	else
-- 		self.has_get_team_target = -1
-- 		return
-- 	end

-- 	if self:IsEnemy(target_obj) then
-- 		self.has_get_team_target = -1
-- 		GuajiWGCtrl.Instance:StopGuaji()
-- 		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
-- 	end
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
-- end

function ShenyuanBossSceneLogic:Update(now_time, elapse_time)
    CommonFbLogic.Update(self, now_time, elapse_time)
    if not self.is_gather_state then
        self:CheckGuaJiPosMove()
    end
end

function ShenyuanBossSceneLogic:DoGatherPickObj()
    local pick_obj = self:GetPickedObj()
    local main_role = Scene.Instance and Scene.Instance:GetMainRole()
    if pick_obj and main_role and not main_role:IsRealDead() and not main_role:GetIsGatherState() then
        self:SetCurGatherState(true)
        GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
        MoveCache.SetEndType(MoveEndType.Gather)
        MoveCache.target_obj = pick_obj
        GuajiWGCtrl.Instance:MoveToObj(pick_obj, 1)
    end
end


function ShenyuanBossSceneLogic:GatherPickObjEnd()
    self:SetCurGatherState(false)
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function ShenyuanBossSceneLogic:SetCurGatherState(enable)
    self.is_gather_state = enable
end

function ShenyuanBossSceneLogic:GatherPickObjStart()
    self:SetCurGatherState(true)
end

