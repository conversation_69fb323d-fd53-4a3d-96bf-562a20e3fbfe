XianQiTeDianTeHuiView = XianQiTeDianTeHuiView or BaseClass(SafeBaseView)

function XianQiTeDianTeHuiView:__init(view_name)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/sixiang_call_prefab", "xqzl_tehui_view")
	self:SetMaskBg(true, true)
end

function XianQiTeDianTeHuiView:LoadCallBack()
	self:InitPanel()
end

function XianQiTeDianTeHuiView:ReleaseCallBack()
	if self.tehui_item_list then
		for k,v in pairs(self.tehui_item_list) do
			v:DeleteMe()
		end
		self.tehui_item_list = nil
	end
	CountDownManager.Instance:RemoveCountDown("xianqi_tedian_tehui")
end

function XianQiTeDianTeHuiView:ShowIndexCallBack()
	UITween.CleanAllTween(GuideModuleName.XianQiTeHui)
	self:DoCellListAnimation()
end

function XianQiTeDianTeHuiView:OnFlush(param_t)
	self:RefreshView()
end

function XianQiTeDianTeHuiView:InitPanel()
	local res_async_loader = AllocResAsyncLoader(self, "xqzl_tehui_item")
	res_async_loader:Load("uis/view/sixiang_call_prefab", "xqzl_tehui_item", nil,
		function(new_obj)
			if IsNil(new_obj) then
				return
			end
			local tehui_item_list = {}
			for i=1,3 do
				local item_root = self.node_list["tehui_root_" .. i]
				if item_root then
					local obj = ResMgr:Instantiate(new_obj)
					obj.transform:SetParent(item_root.transform, false)
					tehui_item_list[i] = XianQiTeHuiItem.New(obj)
					tehui_item_list[i]:SetIndex(i)
				end
			end
			self.tehui_item_list = tehui_item_list
			self:FlushTeHuiItemList()
			self:DoCellListAnimation()
		end)
end

function XianQiTeDianTeHuiView:DoCellListAnimation()
	if IsEmptyTable(self.tehui_item_list) then
		return
	end
    local count = 0
    for i, v in ipairs(self.tehui_item_list) do
        if 0 ~= v.index then
            count = count + 1
        end
        v:PalyXianQiTeHuiItemAnim(count)
    end
end

function XianQiTeDianTeHuiView:RefreshView()
	self:FlushActTime()
	self:FlushTeHuiItemList()
end

function XianQiTeDianTeHuiView:FlushTeHuiItemList()
	local cfg_list = XianQiTeDianWGData.Instance:GetActSaleCfg(XianQiTeDianSubActId.TeHuiZhaoHuan)
	if not IsEmptyTable(cfg_list) and not IsEmptyTable(self.tehui_item_list) then
		local item_list = self.tehui_item_list
		for i=1,#item_list do
			item_list[i]:SetData(cfg_list[i])
		end
	end
end

---[[ 活动结束倒计时
function XianQiTeDianTeHuiView:FlushActTime()
	CountDownManager.Instance:RemoveCountDown("xianqi_tedian_tehui")
    local sever_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = XianQiTeDianWGData.Instance:GetTeDianEndTimeStamp()
    if end_time <= sever_time then
        self.node_list.time_label.text.text = ""
        return
    end

    self:UpdateCountDown(sever_time, end_time)

    CountDownManager.Instance:AddCountDown(
        "xianqi_tedian_tehui",
        BindTool.Bind(self.UpdateCountDown, self),
        BindTool.Bind(self.FlushActTime, self),
        end_time,
        nil,
        1
    )
end

function XianQiTeDianTeHuiView:UpdateCountDown(elapse_time, total_time)
	local time_str = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
	self.node_list.time_label.text.text = string.format(Language.SiXiangCall.CloseTimeDesc, time_str)
end
--]]

----------------------------------------------------------------

XianQiTeHuiItem = XianQiTeHuiItem or BaseClass(BaseRender)

function XianQiTeHuiItem:__delete()
	if self.item_list then
		for k,v in pairs(self.item_list) do
			v:DeleteMe()
		end
		self.item_list = nil
	end
end

function XianQiTeHuiItem:LoadCallBack()
	self.save_act_mark = -1

	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind1(self.OnClickBuyBtn, self))

	local cell_root = self.node_list.item_cell_root
	local item_list = {}
	for i=1,4 do
		item_list[i] = ItemCell.New(cell_root)
	end
	self.item_list = item_list
end

function XianQiTeHuiItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	self:FlushItem()
	self:FlushBuyNum()
end

function XianQiTeHuiItem:FlushBuyNum()
	local data = self:GetData()
	local act_info = XianQiTeDianWGData.Instance:GetSubActSaleInfo(XianQiTeDianSubActId.TeHuiZhaoHuan, data.product_id)
	local buy_num = act_info and act_info.buy_num or 0
	local limit_num = data.limit_buy_times or 0
	if limit_num > buy_num then
		self.node_list.limit_lbl.text.text = string.format(Language.SiXiangCall.TeHuiLimitText, limit_num - buy_num, limit_num)
	end
	self.node_list.limit_lbl:SetActive(limit_num > buy_num)
	self.node_list.all_sell_img:SetActive(buy_num >= limit_num)
	self.node_list.buy_btn:SetActive(buy_num < limit_num)
end

function XianQiTeHuiItem:FlushItem()
	local data = self:GetData()
	local mark = data.cycle * 100 + data.product_id
	if mark == self.save_act_mark then
		return
	end
	self.save_act_mark = mark

	local reward_list = SortTableKey(data.item)
	local item_list = self.item_list
	for i=1,#item_list do
		if reward_list[i] then
			local item_id = reward_list[i].item_id or 0
			local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
			local item_data = {}
			item_data.item_id = reward_list[i].item_id
			item_data.num = reward_list[i].num
			item_data.is_bind = reward_list[i].is_bind
			if item_cfg.use_type == Item_Use_Type.XianYu2 then
				item_data.num = item_cfg.param1
			end
			item_list[i]:SetData(item_data)
			item_list[i]:SetVisible(true)
		else
			item_list[i]:SetVisible(false)
		end
	end
	
	self.node_list.title_lable.text.text = data.name
	self.node_list.price_lbl.text.text = data.special_sale_price
	self.node_list.dicount_lbl.text.text = data.discount
end

function XianQiTeHuiItem:OnClickBuyBtn()
	local data = self:GetData()
	if not IsEmptyTable(data) then
		XianQiTeDianWGCtrl.Instance:BuyXianQiTeDian(data.subactivity_id, data.product_id)
	end
end

function XianQiTeHuiItem:PalyXianQiTeHuiItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.ShenJiNotice.TeHui

    RectTransform.SetSizeDeltaXY(self.node_list["root_bg"].rect, 308, 0)
    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
    	self.node_list["root_bg"].rect:DOSizeDelta(Vector2(308, 568), tween_info.MoveTime)
        if self.node_list and self.node_list["tween_root"] then
            UITween.AlphaShow(GuideModuleName.XianQiTeHui, self.node_list["tween_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime)
        end
    end, tween_info.NextDoDelay * wait_index, "XianQiTeHui_Anim" .. wait_index)
end
