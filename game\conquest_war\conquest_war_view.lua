-- 征战
ConquestWarView = ConquestWarView or BaseClass(SafeBaseView)
local ACTIVITY_ID_LIST = {
	ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF,
	ACTIVITY_TYPE.KF_HONORHALLS,
	ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG,
	ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD,
	ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION,
	ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_AIR_WAR,
}  --征战包含的活动id列表

function ConquestWarView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg()
	self.default_index = TabIndex.conquest_war_zxzc

	self.remind_tab = {
		nil,
		nil,
		{ RemindName.YeZhanWangCheng },
		{ RemindName.CrossFGB },
		nil,
		nil,
	}

	local bundle_name = "uis/view/conquest_war_ui_prefab"
	local common_bundle = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.country_map_flag_grabbing_battlefield, bundle_name, "layout_flag_grabbing_battlefield_view")
	self:AddViewResource(TabIndex.conquest_war_tjmc, bundle_name, "layout_kf_tjmc_view")
	self:AddViewResource(TabIndex.honorhalls_tower, bundle_name, "layout_kf_yyhd_view")
	self:AddViewResource(TabIndex.conquest_war_zxzc, bundle_name, "layout_kf_zxzc_view")
	self:AddViewResource(TabIndex.boss_invasion, bundle_name, "layout_kf_zllx_view")
	self:AddViewResource(TabIndex.conquest_war_kfkz, bundle_name, "layout_kf_kfkz_view")
	self:AddViewResource(0, bundle_name, "VerticalTabbar")
	self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")
end

function ConquestWarView:OpenCallBack()

end

function ConquestWarView:CloseCallBack()
end

function ConquestWarView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FlagGrabbingBattlefield.Title
	if nil == self.tabbar then
		local ver_path = "uis/view/conquest_war_ui_prefab"
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.FlagGrabbingBattlefield.NewTableGroup, nil, ver_path, nil, self.remind_tab, VerConquestWarItemRender)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.FlushTabbarInfo, self))
	end

	self.all_activity_change_callback = BindTool.Bind(self.OnAllActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.all_activity_change_callback)
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.ConquestWarView, self.tabbar)
end

function ConquestWarView:LoadIndexCallBack(index)
	if index == TabIndex.country_map_flag_grabbing_battlefield then
		self:LoadIndexCallBackFGBView()
	elseif index == TabIndex.conquest_war_tjmc then
		self:LoadIndexCallBackTJMCView()
	elseif index == TabIndex.honorhalls_tower then
		self:LoadIndexCallBackYYHDView()
	elseif index == TabIndex.conquest_war_zxzc then
		self:LoadIndexCallBackZXZCiew()
	elseif index == TabIndex.boss_invasion then
		self:LoadIndexCallBackZLLXView()
	elseif index == TabIndex.conquest_war_kfkz then
		self:LoadIndexCallBackKFKZView()
	end
end

function ConquestWarView:ReleaseCallBack()
	self:ReleaseFGBView()
	self:ReleaseTJMCView()
	self:ReleaseYYHDView()
	self:ReleaseZXZCView()
	self:ReleaseZLLXView()
	self:ReleaseKFKZView()

	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.all_activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.all_activity_change_callback)
		self.all_activity_change_callback = nil
	end
end

function ConquestWarView:ShowIndexCallBack(index)
	local bundle, asset = ResPath.GetRawImagesJPG("a3_gjzz_bg")
	if index == TabIndex.country_map_flag_grabbing_battlefield then
		self:FGBShowIndexCallBack()
	elseif index == TabIndex.conquest_war_tjmc then
		bundle, asset = ResPath.GetRawImagesPNG("a3_zz_tjmc_bg")
		self:TJMCShowIndexCallBack()
	elseif index == TabIndex.honorhalls_tower then
		bundle, asset = ResPath.GetRawImagesPNG("honorhalls_tower_bg")
		self:TJMCShowIndexCallBack()
	elseif index == TabIndex.conquest_war_zxzc then
		bundle, asset = ResPath.GetRawImagesPNG("a3_zxzc_bj")
		self:ZXZCShowIndexCallBack()
	elseif index == TabIndex.boss_invasion then
		bundle, asset = ResPath.GetRawImagesJPG("a3_boss_lx_bj")
		self:ZLLXShowIndexCallBack()
	elseif index == TabIndex.conquest_war_kfkz then
		bundle, asset = ResPath.GetRawImagesPNG("a3_zz_dntg_bg")
		self:KFKZShowIndexCallBack()
	end

	-- local title = Language.CountryMap.NewTableGroup[index / 10]
	-- if self.node_list["title_view_name"] then
	-- 	self.node_list["title_view_name"].text.text = title
	-- end

	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function ConquestWarView:OnFlush(param_t, index)
	if index == TabIndex.country_map_flag_grabbing_battlefield then
		self:OnFlushFGBView(param_t, index)
	elseif index == TabIndex.conquest_war_tjmc then
		self:OnFlushTJMCView(param_t, index)
	elseif index == TabIndex.honorhalls_tower then
		self:OnFlushYYHDView(param_t, index)
	elseif index == TabIndex.conquest_war_zxzc then
		self:OnFlushZXZCView(param_t, index)
	elseif index == TabIndex.boss_invasion then
		self:OnFlushZLLXView(param_t, index)
	elseif index == TabIndex.conquest_war_kfkz then
		self:OnFlushKFKZView(param_t, index)
	end

	self:FlushTabbarInfo()
end

function ConquestWarView:FlushTabbarInfo()
	local ver_cell_list = self.tabbar:GetVerCellList()
    if nil == ver_cell_list then
        return
    end

	for k, v in pairs(ver_cell_list) do
		if v:GetView() then
			v:FlushInfo(ACTIVITY_ID_LIST[k])
		end
	end
end

function ConquestWarView:OnAllActChange(activity_type, status, next_time, open_type)
	for k, v in pairs(ACTIVITY_ID_LIST) do
		if activity_type == v then
			self:Flush()
			return
		end
	end
end

------------------------------------
-- 活动TabbarItem
------------------------------------
VerConquestWarItemRender = VerConquestWarItemRender or BaseClass(VerItemRender)

function VerConquestWarItemRender:FlushInfo(id)
	local data = ConquestWarWGData.Instance:GetConquestWarActivityInfoById(id)
    local info = ActivityWGData.Instance:GetActivityCfgByType(id)

	if IsEmptyTable(data) or IsEmptyTable(info) then
		return
	end

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(id)
	local str
	local is_finish =  ActivityWGData.Instance:GetActDoubleSideFBIsFinish(id)
	if not ActivityWGData.Instance:CheckActOpenByRoleLevel(id) then
		str = string.format(Language.FlagGrabbingBattlefield.LimitLevel, info.limit_level)
	elseif not is_open then
		str = string.format(Language.FlagGrabbingBattlefield.ActivityTime2, data.time_2)
	elseif is_finish then
		str = Language.FlagGrabbingBattlefield.Finish
	elseif is_open then
		str = Language.FlagGrabbingBattlefield.Doing
	end

	self.node_list.info.text.text = str
end