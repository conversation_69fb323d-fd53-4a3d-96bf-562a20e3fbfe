FairyLandGodBody = FairyLandGodBody or BaseClass()
function FairyLandGodBody:__init()
    self:DefaultData()
end

function FairyLandGodBody:__delete()

end

function FairyLandGodBody:DefaultData()
    self.body_slot = -1
    self.body_state = GOODS_STATE_TYPE.NORMAL
    self.right_buy_seq = -1

    self:DefaultGodBodyData()
end

-- 神体部分数据
function FairyLandGodBody:DefaultGodBodyData()
    self.is_act = false
    self.is_max_level = false
    self.is_upgrade_stage = false
    self.grade = 0
    self.level = 0
    self.bless_value = 0
    self.uplevel_end_time = 0
    self.page_gather_num = 0
    self.uplevel_need_time = 1
    self.page_act_flag = {}
end

function FairyLandGodBody:SetGodBodyData(info)
    if IsEmptyTable(info) then
    	self:DefaultGodBodyData()
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    self.grade = info.grade
    self.level = info.level
    self.bless_value = info.bless_value
    self.uplevel_end_time = info.uplevel_end_time
    self.is_max_level = self.level >= fle_data:GetGBMaxLevel(self.body_slot)
    local upgrade_cfg = fle_data:GetGBUpGradeCfg(self.body_slot, self.grade)
    if upgrade_cfg then
        self.is_upgrade_stage = self.level == upgrade_cfg.up_max_level
    end

    local uplevel_time = 1
    local uplevel_cfg = fle_data:GetGBUpLevelCfg(self.body_slot, self.level)
    if uplevel_cfg then
        local real_per = 1
        local right_cfg = fle_data:GetGBUplevelRightCfg(self.right_buy_seq)
        local is_buy_right = self:GetIsBuyUplevelRight()
        if right_cfg and is_buy_right then
            real_per = 1 - right_cfg.up_time_per * 0.0001
        end
        uplevel_time = uplevel_cfg.uplevel_time * real_per
    end
    self.uplevel_need_time = uplevel_time

    self.page_act_flag = {}
    local is_act = false
    local page_gather_num = 0
    local page_act_flag = info.page_act_flag
    for page = 0, GOD_BODY_ENUM.MAX_PAGE_COUNT - 1 do
        is_act = page_act_flag[32 - page] == 1
        page_gather_num = is_act and page_gather_num + 1 or page_gather_num
        self.page_act_flag[page] = is_act
    end

    self.page_gather_num = page_gather_num
    self.is_act = self.grade >= 0
end

-- 神体槽
function FairyLandGodBody:SetSlotIndex(slot)
    self.body_slot = slot

    local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(slot)
    local buy_seq = slot_cfg and slot_cfg.buy_seq or -1
    self.right_buy_seq = buy_seq
end

function FairyLandGodBody:GetSlotIndex()
    return self.body_slot
end

function FairyLandGodBody:SetGodBodyState(state)
    self.body_state = state
end

function FairyLandGodBody:GetGodBodyState()
    return self.body_state
end

-- 特权索引
function FairyLandGodBody:GetRightBuySeq()
    return self.right_buy_seq
end

-- 修炼特权是否购买
function FairyLandGodBody:GetIsBuyUplevelRight()
    return FairyLandEquipmentWGData.Instance:GetGodBodyUplevelRightIsBuy(self.right_buy_seq)
end

-- 渡劫特权是否购买
function FairyLandGodBody:GetIsBuyUpgradeRight()
    return FairyLandEquipmentWGData.Instance:GetGodBodyUpgradeRightIsBuy(self.right_buy_seq)
end

-- 处于渡劫阶段
function FairyLandGodBody:GetIsUpgradeStage()
    return self.is_upgrade_stage
end

-- 神体是否激活
function FairyLandGodBody:GetIsAct()
    return self.is_act
end

-- 书页是否激活
function FairyLandGodBody:GetPageIsAct(page)
    return self.page_act_flag[page]
end

-- 书页收集量
function FairyLandGodBody:GetPageGatherNum()
    return self.page_gather_num
end

-- 当前神体是否收集全所有书页
function FairyLandGodBody:IsGetGatherEnoughPage()
    local need_num = FairyLandEquipmentWGData.Instance:GetActBodyNeedNum()
    return need_num <= self.page_gather_num
end

-- 境界
function FairyLandGodBody:GetGrade()
    return self.grade
end

-- 等级
function FairyLandGodBody:GetLevel()
    return self.level
end

function FairyLandGodBody:GetIsMaxLevel()
    return self.is_max_level
end

-- 祝福值
function FairyLandGodBody:GetBlessValue()
    return self.bless_value
end

-- 修炼结束时间
function FairyLandGodBody:GetUplevelEndTime()
    return self.uplevel_end_time
end

function FairyLandGodBody:GetUplevelNeedTime()
    return self.uplevel_need_time
end
