﻿using System;
using UnityEditor;
using UnityEngine;
using System.IO;
using System.Text;
using System.Collections.Generic;

class SceneModelImporter
{
    static string exploreSceneFile = Application.dataPath + "/Game/Scenes/Map/F2_BS_ShiJieMoWang_01/F2_BS_ShiJieMoWang_01_Main.unity";
    static string exploreSceneObjPath = "Assets/Game/Environments/F2_BS_ShiJieMoWang_01/Objects/Prefabs/";
    static string exploreSceneGroundPath = "Assets/Game/Environments/F2_BS_ShiJieMoWang_01/Grounds/";

    [MenuItem("自定义工具/场景导出专用/导出场景Models信息")]
    public static void ExploeSceneModelInfo()
    {
        UnityEngine.SceneManagement.Scene curScene = UnityEditor.SceneManagement.EditorSceneManager.OpenScene(exploreSceneFile);
        if (curScene != null)
        {
            GameObject mainObj = GameObject.Find("Main");
            if (null == mainObj)
            {
                Debug.LogError("错误：找不到Main！");
                return;
            }

            GameObject modelsObj = mainObj.transform.Find("Models").gameObject;
            modelsObj = modelsObj == null ? mainObj.transform.Find("Model").gameObject : modelsObj;
            Transform[] models = modelsObj.GetComponentsInChildren<Transform>();
            StringBuilder builder = new StringBuilder();
            foreach (var go in models)
            {
                if (go.name.Equals("Models") || go.name.Equals("Model"))
                {
                    continue;
                }

                builder.AppendFormat("{0},{1}|{2}|{3},{4}|{5}|{6},{7}|{8}|{9},{10}", go.name
                    , go.transform.localPosition.x, go.transform.localPosition.y, go.transform.localPosition.z
                    , go.transform.localEulerAngles.x, go.transform.localEulerAngles.y, go.transform.localEulerAngles.z
                    , go.transform.localScale.x, go.transform.localScale.y, go.transform.localScale.z
                    , go.transform.parent.name);
                builder.Append("\n");
            }
            File.WriteAllText(Application.dataPath + "/../AssetsCheck/SceneModelExploerList.txt", builder.ToString());
        }
    }

    [MenuItem("自定义工具/场景导出专用/导入场景Models信息")]
    public static void ImportSceneModelInfo()
    {
        UnityEngine.SceneManagement.Scene curScene = UnityEditor.SceneManagement.EditorSceneManager.OpenScene(exploreSceneFile);
        if (curScene != null)
        {
            GameObject mainObj = curScene.GetRootGameObjects()[0];
            GameObject modelsObj = mainObj.transform.Find("Models").gameObject;
            modelsObj = modelsObj == null ? mainObj.transform.Find("Model").gameObject : modelsObj;

            string filePath = Application.dataPath + "/../AssetsCheck/SceneModelExploerList.txt";
            if (!File.Exists(filePath))
            {
                Debug.LogError("不存在文件 " + filePath);
                return;
            }

            StreamReader sr = File.OpenText(filePath);
            List<string> sr_list = new List<string>();

            string item;
            while ((item = sr.ReadLine()) != null)
            {
                string[] split = item.Split(',');
                string modelName = split[0].Replace(" ", "");
                int index = modelName.IndexOf("(");
                int len = index != -1 ? index : modelName.Length;
                modelName = modelName.Substring(0, len);

                string[] modelPosStrs = split[1].Split('|');
                Vector3 modelPos = new Vector3(Convert.ToSingle(modelPosStrs[0]), Convert.ToSingle(modelPosStrs[1]), Convert.ToSingle(modelPosStrs[2]));
                string[] modelRotStrs = split[2].Split('|');
                Vector3 modelRot = new Vector3(Convert.ToSingle(modelRotStrs[0]), Convert.ToSingle(modelRotStrs[1]), Convert.ToSingle(modelRotStrs[2]));
                string[] modelScaleStrs = split[3].Split('|');
                Vector3 modelScale = new Vector3(Convert.ToSingle(modelScaleStrs[0]), Convert.ToSingle(modelScaleStrs[1]), Convert.ToSingle(modelScaleStrs[2]));

                string modelParentName = split[4];

                string modelPrefabPath = exploreSceneObjPath + modelName + ".prefab";
                GameObject prefab = AssetDatabase.LoadAssetAtPath(modelPrefabPath, typeof(GameObject)) as GameObject;
                if (prefab == null)
                {
					modelPrefabPath = exploreSceneGroundPath + modelName + ".prefab";
					prefab = AssetDatabase.LoadAssetAtPath(modelPrefabPath, typeof(GameObject)) as GameObject;
					if (prefab == null)
					{
						Debug.LogErrorFormat("找不到预制体{0}，请直接使用对应的FBX文件", modelPrefabPath);
						continue;
					}
                }

                GameObject model = PrefabUtility.InstantiatePrefab(prefab) as GameObject;//GameObject.Instantiate(prefab);
                model.name = split[0];

                if (modelParentName.Equals("Models") || modelParentName.Equals("Model"))
                {
                    try
                    {
                        model.transform.parent = modelsObj.transform;
                    }
                    catch (Exception e)
                    {
                        Debug.LogError(e.ToString());
                    }
                }
                else
                {
                    Transform parent = modelsObj.transform.Find(modelParentName);
                    if (parent != null)
                    {
                        model.transform.parent = parent;
                    }
                    else
                    {
                        model.transform.parent = modelsObj.transform;
                    }
                }

                model.transform.localEulerAngles = modelRot;
                model.transform.localPosition  = modelPos;
                model.transform.localScale = modelScale;
            }
            sr.Close();
            sr = null;

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }
}
