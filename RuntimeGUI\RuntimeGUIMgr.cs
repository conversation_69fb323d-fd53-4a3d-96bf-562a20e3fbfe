﻿using LuaInterface;
using Nirvana;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Networking;

public class RuntimeGUIMgr : Nirvana.Singleton<RuntimeGUIMgr>
{
    /// <summary>
    /// 外网PC包不显示GM操作UI
    /// </summary>
    public static bool isShowGMGui = true;
    private bool isForceOpenGui = false;
    private RuntimeLoginView loginView = new RuntimeLoginView();
    private RuntimeToolView toolView = new RuntimeToolView();
    private RuntimeGmView gmView = new RuntimeGmView();
    private RuntimeSceneView sceneView = new RuntimeSceneView();
    private RuntimeLuaProfilerView luaProfilerView = new RuntimeLuaProfilerView();

    private GameRuntimeGUI gameRuntimeGUI = new GameRuntimeGUI();
    private ResourceRecorderRuntimeGUI resourceRecorderRumtimeGUI = new ResourceRecorderRuntimeGUI();
    private DebugRuntimeGUI debugRuntimeGUI = new DebugRuntimeGUI();
    private RuntimeGUIBlock guiBlockRuntimeGUI = new RuntimeGUIBlock();

    private UnityAction<string, string, Object> loadAssetCallback;

    public bool IsGUIOpening()
    {
        if (isForceOpenGui) return true;

#if !UNITY_EDITOR && UNITY_STANDALONE
        return true;
#endif
        return false;
    }

    private static bool _isAndroidGM = false;
#if UNITY_EDITOR
    [NoToLua]
    public static void SetIsAndroidGM(bool isGM)
    {
        _isAndroidGM = isGM;
    }
#endif

    public bool IsAndroidGM()
    {
        return _isAndroidGM;
    }

    public void Init()
    {
        if (!IsAndroidGM())
        {
            debugRuntimeGUI.Init();
            guiBlockRuntimeGUI.Init();
        }
    }

    public void OnGameStop()
    {
        debugRuntimeGUI.Destroy();
        RuntimeViewMgr.Instance.OnGameStop();
    }

    public void OnApplicationQuit()
    {
        RuntimeViewMgr.Instance.OnApplicationQuit();
    }

    public void ForceOpenGUI()
    {
        if (!isForceOpenGui)
        {
            isForceOpenGui = true;
            RuntimeGUIStyle.Init();
        }
    }

    //获得工程的dataPath,因为 exe 在client\u3d_proj\Build\windows目录下
    public string GetDataPath()
    {
#if UNITY_EDITOR
        return Application.dataPath;
#else
        return string.Format("{0}/../../../../Assets", Application.streamingAssetsPath);
#endif
    }

    public bool IsUseLocalLuaFile()
    {
        return loginView.IsUseLocalLuaFile();
    }

    public bool IsDebugLuaAB()
    {
        return loginView.IsDebugLuaAB();
    }

    public string GetCurUseSVNTime()
    {
        return loginView.GetCurUseSVNTime();
    }

    public string GetNewSVNTime()
    {
        return loginView.GetNewSvnTime();
    }

    public string GetCurWindowsExeSvnTime()
    {
        return loginView.GetCurWindowsExeSvnTime();
    }

    public string GetNewWindowsExeSvnTime()
    {
        return loginView.GetNewWindowsExeSvnTime();
    }

    public int GetGameSpeed()
    {
        return (int)loginView.GetGameSpeed();
    }

    public void TryOpenLuaProfiler()
    {
        luaProfilerView.TryOpenLuaProfiler();
    }

    public void ShowLoginWindow(UnityAction playGameCallBack)
    {
        RuntimeGUIStyle.Init();
        loginView.SetPlayGameCallback(playGameCallBack);
        RuntimeViewMgr.Instance.OpenView(RuntimeViewName.LOGIN);
    }

    public void SetLoadObjectCallback(UnityAction<string, string, Object> loadAssetCallback)
    {
        this.loadAssetCallback = loadAssetCallback;
    }
    
    public void OnLoadObject(string bundleName, string assetName, Object asset)
    {
        if (null != this.loadAssetCallback)
        {
            this.loadAssetCallback(bundleName, assetName, asset);
        }
    }

    public void ShowResourceRecorderWindow()
    {
        resourceRecorderRumtimeGUI.ShowGUI();
    }

    public DebugRuntimeGUI GetDebugGUI()
    {
        return debugRuntimeGUI;
    }
    
    public RuntimeGUIBlock GetGUIBlock()
    {
        return guiBlockRuntimeGUI;
    }

    public void DelAllCache()
    {
        UnityEngine.PlayerPrefs.DeleteKey("a3_fanli_is_local_windows_debug_exe_quick_login");
        UnityEngine.PlayerPrefs.DeleteKey("a3_fanli_is_local_windows_debug_exe_lua");
        UnityEngine.PlayerPrefs.DeleteKey("a3_fanli_local_windows_debug_exe_net_speed");
        UnityEngine.PlayerPrefs.DeleteKey("a3_fanli_local_windows_debug_exe_query_url");
        UnityEngine.PlayerPrefs.DeleteKey("a3_fanli_local_windows_debug_exe_agent_id");
        UnityEngine.PlayerPrefs.DeleteKey("a3_fanli_is_local_windows_debug_exe_lua_ab");
        UnityEngine.PlayerPrefs.DeleteKey("a3_fanli_is_local_windows_debug_check");
    }

    public void OnGUI()
    {
        if (!IsGUIOpening() && !IsAndroidGM()) return;

        gameRuntimeGUI.OnGUI();
        if (!IsAndroidGM())
        {
            debugRuntimeGUI.OnGUI();
        }
        RuntimeViewMgr.Instance.OnGUI();
    }

    public void Update()
    {
        if (!IsGUIOpening() && !IsAndroidGM()) return;

        loginView.Update();
        if (!IsAndroidGM())
        {
            debugRuntimeGUI.Update();
        }
    }

    private void HideLoadingView()
    {
        if (GameObject.Find("Loading"))
        {
            GameObject.Find("Loading").SetActive(false);
        }
    }
}
