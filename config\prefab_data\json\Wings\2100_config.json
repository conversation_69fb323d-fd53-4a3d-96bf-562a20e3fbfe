{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "eff2100_rest", "effectAsset": {"BundleName": "model/wings/2100_prefab", "AssetName": "eff2100_rest", "AssetGUID": "a1fc3106ba2a7ec4b9532d9cf0251339", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root/eff2100_rest(<PERSON>lone)/A3_beishi_35@skin/root", "isAttach": true, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "restchu", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}], "sounds": [], "cameraShakes": [], "radialBlurs": []}}