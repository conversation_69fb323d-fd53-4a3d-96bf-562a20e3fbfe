-- /jy_cmd playuicg cg/a2_cg_dujie_prefab A2_CG_DuJie_Yes
-- /jy_cmd playuicg cg/a2_cg_dujie_prefab A2_CG_DuJie_No

local RoleTrackName = {
	[0] = { [1] = 3101, [2] = 3102, [3] = 3103, },
	[1] = { [1] = 1101, [2] = 1102, [3] = 1103, },
}

local SpiritTrackName = {
	[0] = "old",
	[1] = "mo",
}

local SPECIAL_SKYBOX_ASSET = {
	["A3_CG_DuJie_No"] = true,
	["A3_CG_DuJie_Yes"] = true,
}

local CG_NEED_ADD_MAIN_ROLE = {
	["A3_CG_DuJie_No"] = true,
	["A3_CG_DuJie_Yes"] = true,
	["A3_CG_DJSystem_Nan"] = true,
	["A3_CG_DJSystem_Nv"] = true,
	["A3_CG_DJSystem_Nan1"] = true,
	["A3_CG_DJSystem_Nv1"] = true,
	["A3_CG_Skill"] = true,
}

UICg = UICg or BaseClass()
function UICg:__init(bundle_name, asset_name)
    self.is_ui_cg = true

	self.bundle_name = bundle_name
	self.asset_name = asset_name
    self.end_callback = nil
	self.start_callback = nil
    self.is_deleted = false
    self.cg_obj = nil
	self.need_check_occlusion = false
	self.role_model_list = {}
end

function UICg:__delete()
    self.is_deleted = true
    self.end_callback = nil
	self.start_callback = nil
    self.cg_ctrl = nil

	self:RemoveModifyTrackDelayTime()
	if self.role_draw_obj then
		self.role_draw_obj:DeleteMe()
		self.role_draw_obj = nil
	end

	if self.role_model_list then
		for i, role_model in pairs(self.role_model_list) do
			role_model:DeleteMe()
			role_model = nil
		end

		self.role_model_list = nil
	end

    self:DestoryCg()
end

function UICg:DestoryCg()
	if nil ~= self.cg_obj then
		ResMgr:Destroy(self.cg_obj, ResPoolReleasePolicy.DestroyQuick)
		self.cg_obj = nil
	end
end

function UICg:Play(end_callback, start_callback, is_jump_cg, skip_callback, show_ui, not_auto_end,change_callback, parent_node)
	self.end_callback = end_callback
	self.start_callback = start_callback
	self.change_callback = change_callback

	if not show_ui then
		SafeBaseView.SetAllUICameraEnable(false)
	end
	ResMgr:LoadGameobjSync(self.bundle_name, self.asset_name, function(obj)
		if self.is_deleted then
			if nil ~= obj then
				ResMgr:Destroy(obj, ResPoolReleasePolicy.DestroyQuick)
			end
			SafeBaseView.SetAllUICameraEnable(true)
			return
		end

		if nil == obj then
			print_error("CgManager Play obj is nil", self.bundle_name, self.asset_name)
			self:OnPlayEnd()
			SafeBaseView.SetAllUICameraEnable(true)
			return
		end

		self.node_list = {}
		local name_table = obj:GetComponent(typeof(UINameTable))
		self.node_list = U3DNodeList(name_table, self)
		self.cg_obj = obj
		local parent_trans = parent_node and parent_node.transform or SceneObjLayer.transform
		self.cg_obj.transform:SetParent(parent_trans)
		self.cg_ctrl = obj:GetComponent(typeof(CGController))
		if self.cg_ctrl == nil then
			print_error("CgManager Play not exist CGController")
			self:DestoryCg()
			self:OnPlayEnd()
			SafeBaseView.SetAllUICameraEnable(true)
			return
		end

		if not not_auto_end then
			self.cg_ctrl:SetPlayEndCallback(BindTool.Bind(self.OnPlayEnd, self))
		end
		if self.change_callback then
			self.change_callback() -- 主动调一次，标识加载完成
			self.cg_ctrl:SetChangeCallback(BindTool.Bind(self.change_callback, self))
		end

		local track_change_call_back = function ()
			self:OnPlayStart()
			-- self.cg_ctrl:Stop()
       		self.cg_ctrl:Play()
		end
		
		self:ModifyTrack(track_change_call_back)
    end)
end

function UICg:ResetModifyTrack()
	local track_change_call_back = function ()
		self:OnPlayStart()
		-- self.cg_ctrl:Stop()
		self.cg_ctrl:Play()
	end
	
	self:ModifyTrack(track_change_call_back)
end

function UICg:SetNodeVisible(name, visible)
	if not self.node_list then
		return
	end

	if self.node_list[name] then
		if self.node_list[name].playable_director and visible then
			self.cg_ctrl:SetPlayableDirector(self.node_list[name].playable_director)
			self:ResetModifyTrack()
		end
		self.node_list[name]:SetActive(visible)
	else
		print_error("cg中设置的节点不存在",name)
	end
end

function UICg:OnPlayStart()
	-- self:ModifyTrack()

	if self.start_callback then
		self.start_callback(self.cg_obj)
	end
end

function UICg:OnPlayEnd()
	if self.sky_box then
		self.sky_box.gameObject:SetActive(true)
		self.sky_box = nil
	end

    self:DestoryCg()
    self.cg_ctrl = nil
	SafeBaseView.SetAllUICameraEnable(true)
	GlobalEventSystem:Fire(ObjectEventType.CG_EVENT_END, self.bundle_name, self.asset_name)

    if self.end_callback then
		self.end_callback()
	end
end

function UICg:Stop()
	if nil ~= self.cg_ctrl then
		self.cg_ctrl:Stop()
		self:DestoryCg()
		self.cg_ctrl = nil
	end
end

function UICg:SetOperaBtnCallback(btn_index, callback)
    if not self.cg_ctrl then
        return
    end

	if btn_index and callback then
		self.cg_ctrl:SetOperaBtnCallback(btn_index, callback)
	end
end

function UICg:SetCurTimePoint(time)
	if not self.cg_ctrl then
        return
    end

    local director = self.cg_ctrl:GetPlayableDirector()
    if director and not IsNil(director) then
        director.time = director.duration >= time and time or director.duration
    end
end

function UICg:IsCanMoveCG()
    return true
end

--移除回调
function UICg:RemoveModifyTrackDelayTime()
    if self.track_change_timer then
        GlobalTimerQuest:CancelQuest(self.track_change_timer)
        self.track_change_timer = nil
    end
end

function UICg:ModifyTrackPart(call_back)
	local role = self:GetCGRoleDrawObj(self.cg_obj.transform)
	local role_obj = role:GetPart(SceneObjPart.Main):GetObj()

	if (not role_obj) then
		self:RemoveModifyTrackDelayTime()
		self.track_change_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self:ModifyTrackPart(call_back)
		end, 0.1)
		return
	end

	if self.node_list.Character then
		-- self.node_list.Character:SetActive(true)
		role_obj.gameObject.transform:SetParent(self.node_list.Character.transform)
		role_obj.gameObject.transform.localPosition = Vector3(0,0,0)
		role_obj.gameObject.transform.localRotation = Quaternion.Euler(0, 0, 0)
	end
	
	local succ1 = self.cg_ctrl:AddActor(role_obj.gameObject, "MainRoleActTrack")
	local succ2 = self.cg_ctrl:AddActor(role:GetRoot().gameObject, "MainRoleTrack")
	self.is_main_role_join = succ1 or succ2

	if self.is_main_role_join then
		if self.cg_obj and not IsNil(self.cg_obj.gameObject) then
			if SPECIAL_SKYBOX_ASSET[self.asset_name] then
				self.cg_ctrl.gameObject.transform.localPosition = Vector3.New(1500, 1500, 0)

				self.sky_box = GameObject.Find("Main/SkyBox")
				if self.sky_box then
					self.sky_box.gameObject:SetActive(false)
				end
			end
		end
	
		local sex, prof = RoleWGData.Instance:GetRoleSexProf()
		local act_track_name = RoleTrackName[sex][prof]
		for sex, v1 in pairs(RoleTrackName) do
			for prof, v2 in pairs(v1) do
				local track_name = v2
				self.cg_ctrl:SetTrackMute(track_name, act_track_name ~= track_name)
			end
		end
	end

	self:OnPlayStart()
	self.cg_ctrl:Play()

end

function UICg:SetSpiritTrackName(is_mo)
	self.cg_ctrl:SetTrackMute(SpiritTrackName[0], is_mo)
	self.cg_ctrl:SetTrackMute(SpiritTrackName[1], not is_mo)

	self.cg_ctrl:RebuildPlay()
end
---------------------主角加入-----------------------
function UICg:ModifyTrack(track_change_call_back)
	if not self.cg_ctrl or not CG_NEED_ADD_MAIN_ROLE[self.asset_name] then
		if track_change_call_back then
			track_change_call_back()
		end

		return
	end

	local role = self:GetCGRoleDrawObj(self.cg_obj.transform)
	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local bundle, asset = ResPath.GetRoleModel(RoleWGData.GetJobModelId(sex, prof))
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local appe_data = role_vo and role_vo.appearance
	local body_res, face_res, hair_res = RoleWGData.GetShowRoleSkinPartRes(sex, prof, appe_data.fashion_body,
    appe_data.default_body_res_id, appe_data.default_face_res_id, appe_data.default_hair_res_id)
    local extra_model_data = {
        role_body_res = body_res,
        role_face_res = face_res,
        role_hair_res = hair_res,
    }

	role:ChangeModel(SceneObjPart.Main, bundle, asset, BindTool.Bind(self.ModifyTrackPart,self), DRAW_MODEL_TYPE.ROLE, extra_model_data, function(skin_type)
		self:FlushPartColor(skin_type, appe_data, body_res, face_res, hair_res)
	end)
end

function UICg:FlushPartColor(skin_type, appe_data, body_res, face_res, hair_res)
    if not appe_data then
        return
    end

    local part_color = appe_data.part_color
    if part_color == nil or IsEmptyTable(part_color) then
        return
    end

    -- 获取染色配置
    local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.BODY, appe_data.shizhuang_body)
    if not image_cfg then
        return
    end

    local fashion_dye_cfg = NewAppearanceDyeWGData.Instance:GetConsumeCfgByIndex(image_cfg.index, image_cfg.part_type)
    local main_part = self:GetCGRoleDrawObj(self.cg_obj.transform):GetPart(SceneObjPart.Main)
    if (not fashion_dye_cfg) or (main_part == nil) then
        return
    end

    local skin = main_part:GetChangeSkinComponent()
    if skin == nil then
        return
    end

    local server_project_index = appe_data.shizhuang_project_id
    local use_project_index = server_project_index + 1

    if not self.project_cache_data then
		self.project_cache_data = {}
	end

    local change_body_dye_color_fun = function()
        local dye_color_table = {}
        for show_part, color_data in ipairs(part_color) do
            local dye_index_list_data = NewAppearanceDyeWGData.Instance:GetDyeIndexListBySeqPart(fashion_dye_cfg.seq, show_part)
            local index_list = dye_index_list_data and dye_index_list_data.dye_index_list
    
            local color = nil
            if color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0 then
                color = Color.New(color_data.r / 255, color_data.g / 255, color_data.b / 255, color_data.a / 255)
            end
    
            if show_part ~= HAIR_PART then
                if index_list and color then
                    for _, dye_index in ipairs(index_list) do
                        local dye_color_data = {}
                        dye_color_data.dye_index = dye_index
                        dye_color_data.r = color.r
                        dye_color_data.g = color.g
                        dye_color_data.b = color.b
                        dye_color_data.a = color.a
                        table.insert(dye_color_table, dye_color_data)
                    end
                end
            end
        end
    
        if not IsEmptyTable(dye_color_table) then
            skin:BatchSetPartDyeColor(cjson.encode({dye_list = dye_color_table}))
        end
    end

    local change_hair_dye_color_fun = function()
        local color_data = part_color[HAIR_PART]
        if color_data and (color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0) then
            main_part:UpdateChangeExtraModelData(HeadCustomizationType.HairColor, color_data)
            main_part:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, color_data)
        end
    end

    if skin_type == ROLE_SKIN_TYPE.BODY then
        local body_material_id = NewAppearanceDyeWGData.Instance:GetProjectBodyIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
        if body_material_id ~= 0 and body_res ~= appe_data.default_body_res_id then
            main_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.BODY, body_res, body_material_id, change_body_dye_color_fun)
            self.project_cache_data.body_id = body_material_id
        else
            if self.project_cache_data.body_id and self.project_cache_data.body_id ~= 0 then		-- 有发生改变才会重置
                self.project_cache_data.body_id = 0
                main_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.BODY)
            end
            change_body_dye_color_fun()
        end
    elseif skin_type == ROLE_SKIN_TYPE.FACE then
        local hair_material_id = NewAppearanceDyeWGData.Instance:GetProjectHairIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
        if hair_material_id ~= 0 and hair_res ~= appe_data.default_hair_res_id then
            main_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.HAIR, hair_res, hair_material_id)
            self.project_cache_data.hair_id = hair_material_id
        else
            if self.project_cache_data.hair_id and self.project_cache_data.hair_id ~= 0 then		-- 有发生改变才会重置
                self.project_cache_data.hair_id = 0
                main_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.HAIR)
            end
        end
    elseif skin_type == ROLE_SKIN_TYPE.HAIR then
        local face_material_id = NewAppearanceDyeWGData.Instance:GetProjectFaceIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
        if face_material_id ~= 0 and face_res ~= appe_data.default_face_res_id then
            main_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.FACE, face_res, face_material_id, change_hair_dye_color_fun)
            self.project_cache_data.face_id = face_material_id
        else
            if self.project_cache_data.face_id and self.project_cache_data.face_id ~= 0 then		-- 有发生改变才会重置
                self.project_cache_data.face_id = 0
                main_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.FACE)
            end

            change_hair_dye_color_fun()
        end
    end
end

-- 将一个RoleModelJia加入到轨道
function UICg:GetRoleModelByNodeName(node_name)
	if self.role_model_list[node_name] then
		return self.role_model_list[node_name], false
	end

	if not self.node_list then
		return nil, false
	end

	local node = self.node_list[node_name]
	if node then
		local role_draw = DrawObj.New(self, node.transform)
        role_draw:SetIsNeedSyncAnim(true)
        role_draw:ChangeWaitSyncAnimType(SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE)
		role_draw:SetIsInQueueLoad(false)
		role_draw:SetIsEnterObjPool(false)
		role_draw:SetIsUseObjPool(true)
        role_draw:SetIsCanWeaponPointAttach(false)
		self.role_model_list[node_name] = role_draw
	end

	return self.role_model_list[node_name], true
end

-- 将一个RoleModelJia加入到轨道
function UICg:AddRoleModelToActor(track_name, obj)
	local succ1 = self.cg_ctrl:AddActorObj(obj, track_name)
end

function UICg:GetCGRoleDrawObj(trans)
	if nil == self.role_draw_obj then
		self.role_draw_obj = DrawObj.New(self, trans)
        self.role_draw_obj:SetIsNeedSyncAnim(true)
        self.role_draw_obj:ChangeWaitSyncAnimType(SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE)

		self.role_draw_obj:SetIsInQueueLoad(false)
		self.role_draw_obj:SetIsEnterObjPool(false)
		self.role_draw_obj:SetIsUseObjPool(true)
        self.role_draw_obj:SetIsCanWeaponPointAttach(false)
	end

	return self.role_draw_obj
end