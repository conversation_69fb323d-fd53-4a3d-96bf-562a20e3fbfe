AsyncFancyAnimView = AsyncFancyAnimView or BaseClass(AsyncFancyListView)

-- 设置数据源(异步刷新数据 )
function AsyncFancyAnimView:SetDataList(data_list)
	if nil == self.data_list then
		self.list_view.anim_scroll_view.CellRefreshDel = BindTool.Bind(self.__RefreshListViewCells, self)
		self.list_view.anim_scroll_view.CellSelectDel = BindTool.Bind(self.OnSelectIndex, self)
	end
	self.data_list = data_list or {}
	self.list_view.anim_scroll_view:SetDataCount(self:GetListViewNumbers())
	-- self:__Flush(1, {0, 0})
end

-- 选择上一个
function AsyncFancyAnimView:SelectPrevCell()
	self.list_view.anim_scroll_view:SelectPrevCell()
end

-- 选择下一个
function AsyncFancyAnimView:SelectNextCell()
	self.list_view.anim_scroll_view:SelectNextCell()
end

-- 跳转到指定index
function AsyncFancyAnimView:SelectCell(index)
	local trans_index = self.start_zero and index or index - 1
	-- local cur_select_index = self.start_zero and self.cur_select_index - 1 or self.cur_select_index
	if self.cur_select_index == index then
		self:SelectIndex(index)
	end
	self.list_view.anim_scroll_view:SelectCell(trans_index)
end