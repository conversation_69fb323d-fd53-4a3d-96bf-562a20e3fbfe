COIN_TYPE = {
	[COMMON_CONSTS.VIRTUAL_ITEM_HORNOR] = true,						--虚拟物品 声望
	[COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD] = true,					--虚拟物品 绑玉
	[COMMON_CONSTS.VIRTUAL_ITEM_GOLD] = true,						--虚拟物品 仙玉
	[COMMON_CONSTS.VIRTUAL_ITEM_YUANBAO] = true,					--虚拟物品 元宝
	[COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN] = true,                  --虚拟物品 金币
}

TipWGData = TipWGData or BaseClass()
function TipWGData:__init()
	if nil ~= TipWGData.Instance then
		ErrorLog("[TipWGData]:Attempt to create singleton twice!")
	end
	TipWGData.Instance = self
	self.equipment_score_cfg = ConfigManager.Instance:GetAutoConfig("equip_score_cfg_auto")
	self.score_cfg = ListToMap(self.equipment_score_cfg.score, "attr_type")
	self.special_score_cfg = ListToMap(self.equipment_score_cfg.special_score, "attr_type")
	self.change_forge_type_cfg = ListToMap(self.equipment_score_cfg.change_forge_type, "view_id", "forge_attr_type")
	self.equip_drop_show_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").best_equipment_tips

	local scene_kill_msg_cfg = ConfigManager.Instance:GetAutoConfig("scene_kill_msg_auto")
	self.scene_kill_msg_cfg = ListToMapList(scene_kill_msg_cfg.kill_msg, "scene_type")

	self.item_resolve_cfg = ConfigManager.Instance:GetAutoConfig("itemother_auto").item_resolve

	local tips_big_reward_item_cfg = ConfigManager.Instance:GetAutoConfig("tips_big_reward_item_auto")
	self.tips_big_reward_item_cfg = ListToMap(tips_big_reward_item_cfg.display_cfg, "model_show_itemid")

	self.wait_ani_tab = {}
	self.level_pecial_attributes = {}
	self.cur_login_no_remind_list = {}
end

function TipWGData:__delete()
	self.level_pecial_attributes = {}
	TipWGData.Instance = nil
end

function TipWGData:IsShowContrast(data, fromView)
    if ItemWGData.Instance:GetIsQingyuanEquip(data.item_id) then --同心锁不显示对比
        return false
    end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
	if nil == item_cfg then return false end

	-- if TianShenWGData.Instance:IsTianShenEquip(item_cfg.sub_type) then
	-- 	-- local equip_data = TianShenWGData.Instance:GetTianShenEquip(nil, TianShenWGData.Equip_Pos[item_cfg.sub_type])
	-- 	-- if equip_data and 0 < equip_data.item_id and fromView == ItemTip.FROM_TIANSHEN_SHENSHI_BAG then
	-- 	-- 	return true
	-- 	-- end
	-- end

	if fromView == ItemTip.BEAST_ALCHEMY_EQUIP_BAG and data.is_bag_equip then
		if ControlBeastsCultivateWGData.Instance:IsHasBeastEquipByFightSlot(data.fight_slot, data.equip_slot) then
			return true
		end
	end

	if big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT
		or item_cfg.sub_type == GameEnum.EQUIP_TYPE_JINGLING then
		return false
	end

	-- 四象魂装
	if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then
		return fromView == ItemTip.FROM_FIGHT_SOUL_BONE_CONTRAST
	end

	-- 圣装
	if item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then
		return fromView == ItemTip.FROM_HOLY_EQUIP_CONTRAST
	end

	local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
	local equip_data = EquipWGData.Instance:GetGridData(equip_body_index)
	if equip_data == nil then
		return false
	end

	if fromView == ItemTip.FROM_BAG or fromView == ItemTip.FROM_NO_GUOQI
		or fromView == ItemTip.FROM_ACT_NEED_CONTRAST or fromView == ItemTip.FROM_EQUIMENT_HECHENG
		and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return true
	end

	if fromView == ItemTip.FROME_MARKET_GOUMAI or fromView == ItemTip.FROM_STORGE_ON_GUILD_STORGE
		or fromView == ItemTip.FROM_PANIC_ITEM or fromView == ItemTip.FROM_TEHUI_SHOP  then
		return true
	end

	if fromView == ItemTip.FROME_BROWSE_ROLE or fromView == ItemTip.FORM_ROBOT_SHOW_INFO then
		return true
	end
	return false
end

function TipWGData:GetOperationLabelByType(data, fromView)
    local t = {}
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id or data.id)
	local prof = RoleWGData.Instance:GetRoleProf()
	local sex = RoleWGData.Instance:GetRoleSex()
	local is_limit_zhuanzhi_prof = item_cfg.is_limit_zhuanzhi_prof and tonumber(item_cfg.is_limit_zhuanzhi_prof) or -1
	if item_cfg == nil then
		print_error("item_cfg is a nil value! Please check the auto.lua is right!")
		return nil
	end

	if QUICK_ADDITEM then
		t[#t+1] = ItemTip.QUICK_ADDITEM
    end

	-- 四象魂装特殊处理
	if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then
		if fromView == ItemTip.FROM_FIGHT_SOUL_BONE_WEAR then
			t[#t + 1] = ItemTip.HANDLE_TAKEOFF
			local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(data.item_id)
			local can_wear_list = FightSoulWGData.Instance:GetBonePartCanWearCache(fight_soul_type, bone_part)
			local num = 0
			for k,v in pairs(can_wear_list) do
				num = num + 1
				if num > 1 then
					t[#t + 1] = ItemTip.HANDLE_REPACE
					break
				end
			end
		end
		return t
	-- 四象特殊处理
	elseif item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG then
		local is_wear = FightSoulWGData.Instance:GetBagIndexIsWear(data.index)
		if not is_wear and ((data.level and data.level > 0) or (data.grade and data.grade > 0)) then
			t[#t + 1] = ItemTip.HANDLE_FIGHT_SOUL_RESTORE
		end
		return t
	end

	-- 圣装特殊处理
	if item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then
		return t
	end

    if big_type == GameEnum.ITEM_BIGTYPE_VIRTUAL then
		if fromView == ItemTip.FROM_BAG and data.item_id == MARRY_OTHER_TYPE.VIRTUAL_TXS_ID then
			t[#t+1] = ItemTip.TXS_ACTIVE
		elseif item_cfg.click_use ~= "" and item_cfg.click_use == 1 and item_cfg.open_panel ~= "" and item_cfg.open_panel ~= 0 then
			t[#t+1] = ItemTip.HANDLE_USE
		end

		return t
	end

    if MainuiWGData.Instance:IsBossRefreshOrInvokeCard(data.item_id) then --boss刷新卡
        t[#t+1] = ItemTip.BOSS_REFRESH
    end

    if MarryWGData.Instance:IsCanShowDecomposeBabyCard(data.item_id) then --仙娃分解
        t[#t+1] = ItemTip.HANDLE_BABY_DECOMPOSE
    end

	if fromView == ItemTip.FROM_TIANSHEN_SHENSHI_EQUIP or fromView == ItemTip.FROM_TIANSHEN_SHENSHI_BAG then
		-- 天神不需要这里的按钮

	elseif fromView == ItemTip.FROM_BAG or fromView == ItemTip.FROM_NO_GUOQI or fromView == ItemTip.FROM_PREVIEW_SHOW_ITEM then		--在背包界面中（没有打开仓库和出售）
		local legend_num = data.param and data.param.star_level or 0
		local is_ts_shenshi_equip = TianShenWGData.Instance:CheckIsTianShenEquip(data.item_id)
		if data then
			if item_cfg.largess_type == 1 and data.is_bind == 0 then
				t[#t+1] = ItemTip.HANDLE_PRESENT_SEND
			end

			local is_long_zhu = LongZhuWGData.Instance:IsLongZhuStuff(data.item_id)
			if is_long_zhu then
				local is_active = LongZhuWGData.Instance:IsActiveByItemId(data.item_id)
				t[#t+1] = is_active and ItemTip.HANDLE_UP_LEVEL or ItemTip.HANDLE_ACTIVE
			end

			if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT or ItemWGData.GetIsFabao(data.item_id) then	--装备类型
				-- 装备跳转合成逻辑
				if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
					local is_can_compose = EquipmentWGData.Instance:GetEquipIsCanCompose(data)
					-- local is_xianqi_compose_stuff = ComposeWGData.Instance:GetIsXQSCStuff(data) -- 仙器现在已经放入普通装备合成
					-- if is_can_compose or is_xianqi_compose_stuff then
					if is_can_compose then
						t[#t+1] = ItemTip.HANDLE_COMPOSE
					end
				end

				if item_cfg.recycltype == 1 then
					t[#t+1] = ItemTip.HANDLE_RECOVER
				elseif item_cfg.recycltype == 2 then -- 装备熔炼
					t[#t+1] = ItemTip.HANDLE_RONGLIAN
				end

				-- 仙盟捐献(橙色三星以上)
				local guild_can_donate_type = item_cfg.sub_type and (item_cfg.sub_type >= GameEnum.EQUIP_TYPE_TOUKUI and item_cfg.sub_type <= GameEnum.EQUIP_TYPE_XIANFU)
				if guild_can_donate_type and item_cfg.color >= 4 and legend_num >= 3 then
					local can_donate = GuildCangKuWGData.Instance:GetIsCanDonate(item_cfg.order, item_cfg.color, legend_num)
					if can_donate and data.is_bind == 0 then
						t[#t+1] = ItemTip.HANDLE_GONGXIAN
					end
				end

				if item_cfg.limit_prof == GameEnum.ROLE_PROF_NOLIMIT or is_limit_zhuanzhi_prof == 0 or item_cfg.limit_prof == prof and item_cfg.limit_sex == sex then
					t[#t + 1] = ItemTip.HANDLE_EQUIP
				end

				if item_cfg.cansell == 1 then
					t[#t+1] = ItemTip.HANDLE_SALE
                end
			else
				if item_cfg.click_use >= 1 or (item_cfg.click_use == 0 and item_cfg.open_panel ~= "" and item_cfg.open_panel ~= 0) then
                    if MarryWGData.Instance:IsCanShowDecomposeBabyCard(data.item_id) --仙娃可分解，不显示使用
                    	or is_ts_shenshi_equip then 	--天神神饰装备，不显示使用
                    else
                        t[#t+1] = ItemTip.HANDLE_USE
                    end
				end
			
				if item_cfg.cansell == 1 then
					t[#t+1] = ItemTip.HANDLE_SALE
                end
			end
		end

		if ItemWGData.IsCompose(data.item_id) then 		 	--可合成
			t[#t+1] = ItemTip.HANDLE_COMPOSE
		end

		--穿戴(替换"使用"")
		if is_ts_shenshi_equip then --神饰装备
	    	t[#t+1] = ItemTip.HANDLE_DRESS
	    end

		if EquipmentWGData.Instance:GetBaoShiCfgByItemId(data.item_id) ~= nil then 		 	--宝石 镶嵌
			t[#t+1] = ItemTip.HANDLE_BAOSHI_INLAY
		end

		if EquipmentLingYuWGData.Instance:GetLingYuCfgByItemId(data.item_id) ~= nil then 		 	--灵玉 镶嵌
			t[#t+1] = ItemTip.HANDLE_LINGYU_INLAY
		end

		-- 拆解
		local _, is_can_decompos = EquipmentWGData.Instance:GetEquipDecomposeByID(item_cfg.id, legend_num)
		if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and is_can_decompos then
			t[#t+1] = ItemTip.HANDLE_EQUIP_DECOMPOSE
		end

		--物品分解
		if self:GetHasItemResolve(data.item_id) then
			t[#t+1] = ItemTip.HANDLE_ITEM_RESOVLE
		end

	elseif fromView == ItemTip.FROM_BAG_ON_BAG_STORGE then			--打开仓库界面时，来自背包
		t[#t+1] = ItemTip.HANDLE_STORGE
		if big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT and item_cfg.sellprice > 0 then
			-- t[#t+1] = ItemTip.HANDLE_SALE
		end

	elseif fromView == ItemTip.FROM_STORGE_ON_BAG_STORGE then		--打开仓库界面时，来自仓库
		t[#t+1] = ItemTip.HANDLE_BACK_BAG

	elseif fromView == ItemTip.FROM_BAG_ON_BAG_SALE then--打开售卖界面时，来自背包
		if item_cfg.recycltype ~= 0 and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			t[#t+1] = ItemTip.HANDLE_RECOVER
		end

	elseif fromView == ItemTip.FROM_BAOXIANG then					--打开宝箱界面时，来自宝箱
		t[#t+1] = ItemTip.BAOXIANG_QUCHU
	elseif fromView == ItemTip.FROM_HUNT_THUNDER_STORGE then	    
		t[#t+1] = ItemTip.HUNT_THUNDER_BAOXIANG_QUCHU
	elseif fromView == ItemTip.FROM_JINGLING_BAOXIANG then			--打开宝箱界面时，来自精灵寻宝
		t[#t+1] = ItemTip.JINGLING_BAOXIANG_QUCHU

	elseif fromView == ItemTip.FROM_MARKET_JISHOU then
		t[#t + 1] = ItemTip.SHICHANG_CHEHUI

	elseif fromView == ItemTip.FROME_MARKET_GOUMAI then
		t[#t + 1] = ItemTip.SHICHANG_GOUMAI

	elseif fromView == ItemTip.FROM_MARKET_SHANGJIA then 		--来自市场上架
		t[#t + 1] = ItemTip.SHICHANG_WORLD_SELL

	elseif fromView == ItemTip.FROM_SJ_JC_ON then 				--来自情缘背包
		t[#t + 1] = ItemTip.HANDLE_TAKEON

	elseif fromView == ItemTip.FROM_BAG_ON_GUILD_STORGE then 	--来自仙盟背包
		t[#t + 1] = ItemTip.HANDLE_GONGXIAN

	elseif fromView == ItemTip.FROM_STORGE_ON_GUILD_STORGE then --来自仙盟仓库
		t[#t + 1] = ItemTip.HANDLE_EXCHANGE

	elseif fromView == ItemTip.FROM_BAG_XIAOGUI then
		-- t[#t + 1] = ItemTip.HANDLE_XUFEI_PUTON
		t[#t + 1] = ItemTip.HANDLE_TAKEOFF

	elseif fromView == ItemTip.FROM_PUTON_XIAOGUI then 				-- 已穿戴小鬼合成
		if item_cfg.id ~= 10600 and item_cfg.id ~= 10500 and item_cfg.id ~= 10101 then
			t[#t + 1] = ItemTip.HANDLE_HECHENG_PUTON
		end
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		local appe_image_id = xiaogui_cfg.appe_image_id
		if appe_image_id ~= GameVoManager.Instance:GetMainRoleVo().guard_id then
			t[#t + 1] = ItemTip.HANDLE_HUANHUA
		end
		t[#t + 1] = ItemTip.HANDLE_TAKEOFF

	elseif fromView == ItemTip.FROM_NO_GUOQI then 				-- 未过期
		t[#t + 1] = ItemTip.HANDLE_SALE
		t[#t + 1] = ItemTip.HANDLE_EQUIP

	elseif fromView == ItemTip.FROM_QiChong then
		t[#t + 1] = ItemTip.HANDLE_EQUIP
		-- t[#t + 1] = ItemTip.HANDLE_SALE

	elseif fromView == ItemTip.FROM_GUOQI then 					-- 过期
		t[#t + 1] = ItemTip.HANDLE_XUFEI_INBAG
		t[#t + 1] = ItemTip.HANDLE_SALE

	elseif fromView == ItemTip.FROM_PANIC_ITEM then
		t[#t + 1] = ItemTip.SHICHANG_GOUMAI

	elseif fromView == ItemTip.FROM_TEHUI_SHOP then
		t[#t + 1] = ItemTip.SHICHANG_GOUMAI

	elseif fromView == ItemTip.FROM_SHOP_GOLD then
		t[#t + 1] = ItemTip.SHOP_BUY

	elseif fromView == ItemTip.FROM_SHOP_BIND_GOLD then
		t[#t + 1] = ItemTip.SHOP_BUY

	elseif fromView == ItemTip.FROM_SHOP_EQUIP then
		t[#t + 1] = ItemTip.SHOP_BUY

	elseif fromView == ItemTip.FROM_BAG_EQUIP then
		t[#t+1] = ItemTip.HANDLE_TAKEOFF
		t[#t+1] = ItemTip.HANDLE_FORGE
		-- 装备跳转合成逻辑
		if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			-- local is_can_compose = false
			local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
			local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
			
			-- if equip_part == GameEnum.EQUIP_INDEX_XIANJIE or equip_part == GameEnum.EQUIP_INDEX_XIANZHUO then
			-- 	is_can_compose = EquipmentWGData.Instance:GetWearEquipCanXQCompose(equip_body_index)
			-- elseif equip_part <= GameEnum.EQUIP_INDEX_XIANFU then
			-- 	is_can_compose = EquipmentWGData.Instance:GetEquipIsCanCompose(data)
			-- end

			local is_can_compose = EquipmentWGData.Instance:GetEquipIsCanCompose(data)

			if is_can_compose then
				t[#t+1] = ItemTip.HANDLE_COMPOSE
			end
		end
	end

	return t
end

function TipWGData:SetWaitAniTabState(ModuleName, state)
	if state == "add" and nil == self.wait_ani_tab[ModuleName] then
		self.wait_ani_tab[ModuleName] = state
	elseif state == "sub" then
		self.wait_ani_tab[ModuleName] = nil
	elseif state == "playing" then
		self.wait_ani_tab[ModuleName] = state
	end
end

function TipWGData:GetWaitAniTab()
	return self.wait_ani_tab
end

-- value = attr_str
function TipWGData:GetCommonPingFenCfgByIndex(value)
	if not value then
		return 0
	end

	value = AttributeMgr.GetAttributteKey(value)
	local attr_type = EquipmentWGData.Instance:GetAttrIdByAttrStr(value)

	if attr_type and self.score_cfg[attr_type] then
		return self.score_cfg[attr_type].score or 0
	end

	if self.special_score_cfg[value] then
		return self.special_score_cfg[value].score or 0
	end

	return 0
end

function TipWGData:GetCommonPingFenByAttrList(attr_list)
	if IsEmptyTable(attr_list) then
		return 0
	end

	local common_pingfen_num = 0
	for k,v in pairs(attr_list) do
		if type(v) == "number" and v > 0 then
			local base_num = self:GetCommonPingFenCfgByIndex(k) or 0
			common_pingfen_num = common_pingfen_num + base_num * v
		end
	end
	return common_pingfen_num
end

-- 优化后的 获取道具配置基础评分
function TipWGData:GetCfgBasePingFenByAttrList(item_cfg)
	local pingfen = 0
	local base_cfg_key = EquipmentWGData.Instance:GetBaseCfgAttrKey()
	if item_cfg == nil or base_cfg_key == nil then
		return pingfen
	end

	local value = 0
	for k,v in pairs(base_cfg_key) do
		value = item_cfg[k]
		if value and value > 0 then
			pingfen = pingfen + self:GetXianPingSpecialAttrByOrder(v.attr_id, value)
		end
	end

	return pingfen
end

-- view_id 功能id 1、玩家装备， 2、神兽装备
function TipWGData:GetSpecialPingFenCfgByOrder(order, attr_type, attr_val, is_star_attr, view_id)
	view_id = view_id or 1
	local attr_cfg = (self.change_forge_type_cfg[view_id] or {})[attr_type]
	if not attr_cfg or not attr_val then
		return 0
	end

	local cfg_attr_type = attr_cfg.attr_type
	local cfg_attr_type_str = attr_cfg.attr_type_str
	if not self.score_cfg[cfg_attr_type] and not self.special_score_cfg[cfg_attr_type_str] then
		return 0
	end

	if 0 == attr_cfg.level then
		if self.score_cfg[cfg_attr_type] then
			return self.score_cfg[cfg_attr_type].score * attr_val
		elseif self.special_score_cfg[cfg_attr_type_str] then
			return self.special_score_cfg[cfg_attr_type_str].score * attr_val
		end
	else
		local role_level = RoleWGData.Instance.role_vo.level
		if self.score_cfg[cfg_attr_type] then
			return self.score_cfg[cfg_attr_type].score * (role_level / attr_cfg.level * attr_val)
		elseif self.special_score_cfg[cfg_attr_type_str] then
			return self.special_score_cfg[cfg_attr_type_str].score * (role_level / attr_cfg.level * attr_val)
		end
	end

	return 0
end

function TipWGData:GetXianPingSpecialAttrByOrder(attr_type, attr_val)
	attr_val = attr_val or 0
	if self.score_cfg[attr_type] then
		return self.score_cfg[attr_type].score * attr_val
	end

	if self.special_score_cfg[attr_type] then
		return self.special_score_cfg[attr_type].score * attr_val
	end

	return 0
end

function TipWGData:GetPingFenList( order )
	local level = GameVoManager.Instance:GetMainRoleVo().level
	if self.level_pecial_attributes[level] then
		return self.level_pecial_attributes[level]
	end

	return {}
end

function TipWGData:GetCurLoginNoRemindListByKey(key)
	return self.cur_login_no_remind_list[key]
end

function TipWGData:SetCurLoginNoRemindListByKey(key, no_remind)
	if not self.cur_login_no_remind_list[key] then
		self.cur_login_no_remind_list[key] = no_remind
	end
end

function TipWGData:GetEquipGetWayData(item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	local get_way_cfg = ConfigManager.Instance:GetAutoConfig("getway_auto").get_way
	local role_level = RoleWGData.Instance.role_vo.level
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local data = {}
	if item_cfg then
		local flag_num = item_cfg.recommend_number or 0
		local get_way_list = Split(item_cfg.get_way,",")
		if #get_way_list > 0 then
			for k,v in ipairs(get_way_list) do
				if get_way_cfg[tonumber(v)] and role_level >= get_way_cfg[tonumber(v)].limit_level and open_day >= get_way_cfg[tonumber(v)].open_day then
					local temp_lsit = __TableCopy(get_way_cfg[tonumber(v)])
					temp_lsit.flag = flag_num >= k
					temp_lsit.stuff_item_id = item_id
					table.insert(data,temp_lsit)
				end
			end
		end
	end
	return data
end

function TipWGData:GetGetWayDataByList(get_way_id_list)
	get_way_id_list = get_way_id_list or ""
	local get_way_list = Split(get_way_id_list, ",")

	local list = {}
	local get_way_cfg = ConfigManager.Instance:GetAutoConfig("getway_auto").get_way
	local role_level = RoleWGData.Instance.role_vo.level

	local way_id = 0
	for k,v in ipairs(get_way_list) do
		way_id = tonumber(v)
		local data = get_way_cfg[way_id]
		if data and role_level >= data.limit_level then
			table.insert(list, data)
		end
	end

	return list
end

-- 获取途径开启情况
function TipWGData:GetGetWayOpenFlag(cfg)
	if not cfg then
		return false
	end
	local t = Split(cfg.open_panel, "#")
	local view_name = t[1]
	local tab_index = t[2]

	local open_flag_1 = FunOpen.Instance:GetFunIsOpened(view_name)
	local open_flag_2 = FunOpen.Instance:GetFunIsOpened(tab_index or view_name)
	if cfg.act_id == 0 and not (open_flag_1 and open_flag_2) then
		return false
	end

	-- 关联活动判断
	-- ps:act_id是活动类型(act_type)
	if cfg.act_id ~= 0 then
		local flag = ActivityWGData.Instance:GetActivityIsOpen(cfg.act_id)
		return flag
	end

	return true
end

function TipWGData:GetShopBuyList(item_id)
	if not item_id then
		return {}
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	local shop_list = {}
	local data_list = {}

	if item_cfg.buy_get_way then
		shop_list = ShopWGData.Instance:GetShopList(item_id, item_cfg.buy_get_way)
	elseif item_cfg.sub_type == GameEnum.EQUIP_TYPE_XIAOGUI then
		local cfg = EquipmentWGData.Instance:GetGuardCfgByItemID(item_id)
		local buy_get_way = cfg and cfg.buy_get_way or 0
		shop_list = ShopWGData.Instance:GetShopList(item_id, buy_get_way)
	end

	if not IsEmptyTable(shop_list) then
		for _,v in ipairs(shop_list) do
			if v.shop_type ~= SHOP_BIG_TYPE.SHOP_TYPE_9 then
				if ShopWGData.Instance:IsCanCell(v.seq) and ShopWGData.Instance:CheckItemCondition(v.seq) and not ShopWGData.Instance:ItemEmpty(v.seq) then
					data_list[#data_list + 1] = v
				end
			end
		end
	end

	---[[ 只显示绑玉和仙玉
	if not IsEmptyTable(data_list) then
		local show_list = {Shop_Money_Type.Type2, Shop_Money_Type.Type1}
		local temp_lsit = {}
		for i=1,#show_list do
			for j=1,#data_list do
				if data_list[j].price_type == show_list[i] then
					temp_lsit[#temp_lsit + 1] = data_list[j]
				end
			end
		end
		data_list = temp_lsit
	end
	--]]

	return data_list
end

function TipWGData:GetGetWayList(item_id)
	if not item_id then
		return {}
	end

	local list_data = {}
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg or not item_cfg.get_way or 0 == string.len(item_cfg.get_way) then
		return list_data
	end

	local role_level = RoleWGData.Instance.role_vo.level

	local is_vip = VipWGData.Instance:IsVip()
	local vip_level = is_vip and RoleWGData.Instance.role_vo.vip_level or 0

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	local get_way_list = Split(item_cfg.get_way, ",")

	for k,v in pairs(get_way_list) do
		local way_info = Split(v, "|")
		local way = tonumber(way_info[1])
		local special_bg = way_info[2] and tonumber(way_info[2]) or 0
		if way == 0 then
			way = 1000
		end  --转为1000，与快速购买兼容

		local get_way_cfg = ConfigManager.Instance:GetAutoConfig("getway_auto").get_way[way]
		local open_flag = self:GetGetWayOpenFlag(get_way_cfg)

		if open_flag then
			local flag = true
			--无奈写死id,七天奖励途径如果不在七天奖励内不显示
			if get_way_cfg ~= nil and get_way_cfg.id == 115 and not WelfareWGData.Instance:GetInInSevenDay() then
				flag = false
			end

			-- 每周必买特殊判断
			if flag and get_way_cfg ~= nil and get_way_cfg.open_panel ~= nil and get_way_cfg.open_panel ~= "" then
				local t = Split(get_way_cfg.open_panel, "#")
				local view_name = t[1]
				local tab_index = t[2]

				if view_name ~= nil and view_name ~= "" and tab_index ~= nil and tab_index ~= "" then
					if view_name == GuideModuleName.Vip and TabIndex[tab_index] ~= nil and TabIndex[tab_index] ~= "" and TabIndex[tab_index] == TabIndex.recharge_week_buy then
						local is_open = FunOpen.Instance:GetFunIsOpened(FunName.recharge_week_buy)
						local open_day = RechargeWGData.Instance:GetWeekBuyOpenDay()
						local cur_open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
						if not is_open or cur_open_server_day < open_day then
							flag = false
						end
					end
				end
			end
			-- 货币类型特殊处理，途径满足规则
			if flag and get_way_cfg ~= nil and get_way_cfg.open_panel ~= nil and get_way_cfg.open_panel ~= "" then
				if COIN_TYPE[item_id] then
					flag = self:FiltrationCoinGetWay(item_id, get_way_cfg)
				end
			end

			local task_status = GameEnum.TASK_STATUS_FINISH
			if get_way_cfg.task_id ~= 0 then
				task_status = TaskWGData.Instance:GetTaskStatus(get_way_cfg.task_id)
			end

			if flag and role_level >= get_way_cfg.limit_level and vip_level >= get_way_cfg.vip_level and
				open_day >= get_way_cfg.open_day and task_status == GameEnum.TASK_STATUS_FINISH then

				table.insert(list_data,{ShowType = ItemTipGoods.ShowType.Type2, cfg = get_way_cfg, item_id = item_id, special_bg = special_bg})
			end
		end
	end

	return list_data
end

function TipWGData:FiltrationCoinGetWay(item_id, get_way_cfg)
	local t = Split(get_way_cfg.open_panel, "#")
	local view_name = t[1]
	local tab_index = t[2]
	if item_id == COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD then        --绑玉
		--投资特权
		if view_name == GuideModuleName.Vip and TabIndex[tab_index] ~= nil and TabIndex[tab_index] ~= "" and TabIndex[tab_index] == TabIndex.recharge_tqtz then
			return not RechargeWGData.Instance:IsBuyAllTouZiPlan()
		end
		--每周必买
		if view_name == GuideModuleName.Vip and TabIndex[tab_index] ~= nil and TabIndex[tab_index] ~= "" and TabIndex[tab_index] == TabIndex.recharge_week_buy then
			return RechargeWGData.Instance:IsBuyWeekBuy()
		end
	elseif item_id == COMMON_CONSTS.VIRTUAL_ITEM_GOLD or item_id == COMMON_CONSTS.VIRTUAL_ITEM_YUANBAO then         --虚拟物品 仙玉、元宝
		--策划需求。元宝特殊处理
		if item_id == COMMON_CONSTS.VIRTUAL_ITEM_YUANBAO then
			--一本万利-每日礼包全部购买并且领取，不再显示智能跳转
			if view_name == GuideModuleName.YiBenWanLiView
				and TabIndex[tab_index] ~= nil
				and TabIndex[tab_index] ~= ""
				and TabIndex[tab_index] == TabIndex.daily_gift then
				return not YiBenWanLiWGData.Instance:IsAllRewardReceived()

			--贵族礼包全部购买，不再显示智能跳转
			elseif view_name == GuideModuleName.Vip
				and TabIndex[tab_index] ~= nil
				and TabIndex[tab_index] ~= ""
				and TabIndex[tab_index] == TabIndex.recharge_vip then
				return not VipWGData.Instance:HasVipBagAllBuy()
			end
		end
	end
	return true
end

function TipWGData:CheckCanShowData(item_data)
	if IsEmptyTable(item_data) then
		return false
	end

	local scene_cfg =  Scene.Instance:GetCurFbSceneCfg()
	if scene_cfg.bestdrop_item_show ~= 1 then
		return false
	end
	local level = RoleWGData.Instance.role_vo.level
	if not self.drop_show_quality_list then
		self.drop_show_quality_list = {}
		self.drop_show_star = {}
	end
	local target_level = -1
	for k,v in pairs(self.equip_drop_show_cfg) do
		if v.min_level <= level and v.max_level >= level and level >= v.open_level then
			if nil == self.drop_show_quality_list[v.open_level] then
				self.drop_show_quality_list[v.open_level] = {}
				self.drop_show_star[v.open_level] = {}
				self.drop_show_quality_list[v.open_level] = Split(v.quality,"|")
				self.drop_show_star[v.open_level] = Split(v.star,"|")
				target_level = v.open_level
				break
			else
				target_level = v.open_level
				break
			end
		end
	end

	if target_level == -1 then
		return false
	else
		local equip_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
		local equip_color = equip_cfg.color
		local equip_star_level = item_data.param.star_level
		local have_color = 0
		if self.drop_show_quality_list[target_level] and self.drop_show_star[target_level] then
			for k,v in pairs(self.drop_show_quality_list[target_level]) do
				if tonumber(v) == equip_color then
					have_color = have_color + 1
					break
				end
			end

			for t,q in pairs(self.drop_show_star[target_level]) do
				if tonumber(q) == equip_star_level then
					have_color = have_color + 1
					break
				end
			end
			return have_color == 2
		end
		return false
	end
end

--设置默认显示购买的数量
function TipWGData:SetDefShowBuyCount(num)
    self.tips_def_buy_count = num
end

function TipWGData:GetDefShowBuyCount()
    return self.tips_def_buy_count
end

--获取不同场景类型的击杀显示信息
function TipWGData:GetSceneKillMsgCfg(kill_count)
	-- TipWGData.Instance:GetSceneKillMsgCfg(1)
	local scene_type = Scene.Instance:GetSceneType()
	local cfg = self.scene_kill_msg_cfg[scene_type]
	if IsEmptyTable(cfg) then
		cfg = self.scene_kill_msg_cfg[0]   --没有 取通用的
	end
	for k,v in pairs(cfg) do
		if kill_count >= v.kill_count_min and kill_count <= v.kill_count_max then
			return v
		end
	end
end

function TipWGData:CurCanShowKillHeadMsg(info)
	local flag = false
	local kill_count = info and info.param or 0
	local scene_type = Scene.Instance:GetSceneType()
	local cfg_list = self.scene_kill_msg_cfg[scene_type]
	if IsEmptyTable(cfg_list) then
		return true
	end
	for k,v in pairs(cfg_list) do
		if kill_count >= v.kill_count_min and kill_count <= v.kill_count_max then
			return true
		end
	end
	return false
end

--获取该物品是否可以分解
function TipWGData:GetHasItemResolve(item_id)
	local role_level = RoleWGData.Instance.role_vo.level
	local cfg = self.item_resolve_cfg[item_id]
	if not IsEmptyTable(cfg) and role_level >= cfg.show_level then
		return true
	end
	return false
end

--获取物品是否为神品大奖
function TipWGData:GetBigRewardCfgByItemId(item_id)
	return self.tips_big_reward_item_cfg[item_id]
end