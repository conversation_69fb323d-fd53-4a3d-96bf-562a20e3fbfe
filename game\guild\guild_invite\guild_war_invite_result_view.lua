GuildWarInviteResultView = GuildWarInviteResultView or BaseClass(SafeBaseView)

function GuildWarInviteResultView:__init()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_war_invite_result")
end

function GuildWarInviteResultView:__delete()
end

function GuildWarInviteResultView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if self.result_item then
		self.result_item:DeleteMe()
		self.result_item = nil
	end

	if CountDownManager.Instance:HasCountDown("guild_invite_result_count_down") then
		CountDownManager.Instance:RemoveCountDown("guild_invite_result_count_down")
	end
end

function GuildWarInviteResultView:OpenCallBack()
	ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleRankInfoReq(GUILD_BATTLE_INFO_REQ_TYPE.GUILD_BATTLE_INFO_REQ_TYPE_RANK)
end

function GuildWarInviteResultView:LoadCallBack()
	-- self.list_view = AsyncListView.New(InviteResultItem,self.node_list["list_view"])
	self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])

	self.result_item = InviteResultItem.New(self.node_list["my_item"])

	XUI.AddClickEventListener(self.node_list["ok_btn"],BindTool.Bind(self.ClickOkBtn,self))
end

function GuildWarInviteResultView:OnFlush()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	local rank,my_data = GuildWGData.Instance:GetCurZoneGuildData(guild_id)
	self.result_item:SetIndex(rank)
	self.result_item:SetData(my_data)
	local reward_data = GuildInviteWGData.Instance:GetJoinReward(rank)
	self.reward_list:SetDataList(reward_data)

	if CountDownManager.Instance:HasCountDown("guild_invite_result_count_down") then
		CountDownManager.Instance:RemoveCountDown("guild_invite_result_count_down")
	end
	self:UpdateInviteCallBack(0,30)
	local time = TimeWGCtrl.Instance:GetServerTime() + 30
	CountDownManager.Instance:AddCountDown("guild_invite_result_count_down", BindTool.Bind1(self.UpdateInviteCallBack, self), BindTool.Bind1(self.UpdateInviteComplete, self), time, time,1)
end

function GuildWarInviteResultView:UpdateInviteCallBack(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	self.node_list["close_text"].text.text = string.format(Language.Guild.GuideWarExitTime,time)
end

function GuildWarInviteResultView:UpdateInviteComplete()
	self:Close()
end

function GuildWarInviteResultView:ClickOkBtn()
	self:Close()
end

function GuildWarInviteResultView:CloseCallBack()
	FuBenWGCtrl.Instance:SendLeaveFB()
end

----------------------InviteResultItem -----------

InviteResultItem = InviteResultItem or BaseClass(BaseRender)

function InviteResultItem:__init()
end

function InviteResultItem:__delete()
end

function InviteResultItem:OnFlush()
	if self.data == nil then return end
	if self.index then
		self.node_list["zone_img"].image:LoadSprite(ResPath.GetGuildSystemImage("rank_render_"..self.index))
		self.node_list["zone_img"].image:SetNativeSize()
	end
	for i=1,4 do
		local data = self.data[i]
		if data then
			if data.guild_name and data.guild_name ~= "" then
				local guild_id = RoleWGData.Instance.role_vo.guild_id
				if data.guild_id == guild_id then
					self.node_list["lbl_guildname"..i].text.text = ToColorStr(data.guild_name,COLOR3B.D_GREEN)
				else
					self.node_list["lbl_guildname"..i].text.text = data.guild_name
				end
				
			else
				self.node_list["lbl_guildname"..i].text.text = Language.Guild.NotGuildOnRank
			end
		else
			self.node_list["lbl_guildname"..i].text.text = Language.Guild.NotGuildOnRank
		end
	end
end
