GuDaoJIZhanResultView = GuDaoJIZhanResultView or BaseClass(SafeBaseView)

function GuDaoJIZhanResultView:__init()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
end

function GuDaoJIZhanResultView:__delete()

end

function GuDaoJIZhanResultView:ReleaseCallBack()
	if self.my_rank_cell then
		self.my_rank_cell:DeleteMe()
		self.my_rank_cell = nil
	end

	if self.result_list then
		self.result_list:DeleteMe()
		self.result_list = nil
	end
end

-- 加载配置
function GuDaoJIZhanResultView:LoadConfig()
	self:AddViewResource(0,"uis/view/gudao_jizhan_prefab","layout_gudao_result")
end

function GuDaoJIZhanResultView:LoadCallBack(index, loaded_times)
	self.node_list["closebtn"].button:AddClickListener(BindTool.Bind(self.Close,self))
	self.result_list = AsyncListView.New(GuDaoResultCell, self.node_list.ranklist)
	self.my_rank_cell = GuDaoResultCell.New(self.node_list["my_rank_cell"].gameObject)

end

	-- for i = 1, self.count do
	-- 	self.battle_info_list[i] = {}
	-- 	self.battle_info_list[i].role_name = MsgAdapter.ReadStrN(32)
	-- 	self.battle_info_list[i].hurt_value = MsgAdapter.ReadLL()
	-- 	self.battle_info_list[i].box_count = MsgAdapter.ReadInt()
	-- end


function GuDaoJIZhanResultView:OnFlush()
	local all_data = GuDaoFuBenWGData.Instance:GetAllResultData()
	self.result_list:SetDataList(all_data)
	local my_name = GameVoManager.Instance:GetMainRoleVo().name
	for k,v in pairs(all_data) do
		if v.role_name == my_name then
			self.my_rank_cell:SetData(v)
			break
		end
	end

end

GuDaoResultCell = GuDaoResultCell or BaseClass(BaseRender)

function GuDaoResultCell:__init()

end


function GuDaoResultCell:LoadCallBack()
	self.rewared_list = AsyncListView.New(GuDaoResultSmallCell, self.node_list.rewared_list)
end

function GuDaoResultCell:__delete()
	if self.rewared_list then
		self.rewared_list:DeleteMe()
		self.rewared_list = nil
	end

end

function GuDaoResultCell:OnFlush()
	if not IsEmptyTable(self.data) then
		-- self.node_list["rank_icon"]
		if self.data.index <= 3 then
			self.node_list["rank_icon"]:SetActive(true)
			local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_"..self.data.index)
 			self.node_list["rank_icon"].image:LoadSpriteAsync(bundle, asset,function ()
				self.node_list["rank_icon"].image:SetNativeSize()
			end)
			self.node_list["my_rank"].text.text = ""
		else
			self.node_list["my_rank"].text.text = self.data.index
			self.node_list["rank_icon"]:SetActive(false)
		end

		self.node_list["mvp1"]:SetActive(self.data.index == 1)
		self.node_list["mvp2"]:SetActive(self.data.index == 2)

		self.node_list["my_name"].text.text = self.data.role_name
		self.node_list["my_damage"].text.text = CommonDataManager.ConverMoneyByThousand(self.data.hurt_value)
		self.node_list["box_num"].text.text = self.data.box_count
		local item_data_table = GuDaoFuBenWGData.Instance:GetPaimingRewared(self.data.index)
		self.rewared_list:SetDataList(item_data_table.reward_item)
	else

	end
end

GuDaoResultSmallCell = GuDaoResultSmallCell or BaseClass(BaseRender)

function GuDaoResultSmallCell:__init()

end

function GuDaoResultSmallCell:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["item_pos"])
end

function GuDaoResultSmallCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GuDaoResultSmallCell:OnFlush()
	if self.data then
		self.item_cell:SetData(self.data)
	end
end
