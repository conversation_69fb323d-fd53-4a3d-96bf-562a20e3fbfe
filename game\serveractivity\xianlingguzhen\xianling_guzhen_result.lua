XianLingGuZhenResult = XianLingGuZhenResult or BaseClass(SafeBaseView)
function XianLingGuZhenResult:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true,true)
    self.view_style = ViewStyle.Half
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
    self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_guzhen_reward")
end

function XianLingGuZhenResult:__delete()

end

function XianLingGuZhenResult:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function XianLingGuZhenResult:LoadCallBack()
	-- if not self.reward_list then
	-- 	self.reward_list = AsyncListView.New(ItemCell,self.node_list.reward_list)	
	-- end

	self.reward_list = AsyncBaseGrid.New()
    self.reward_list:CreateCells({col = 7, change_cells_num = 1 , list_view = self.node_list["reward_list"],
    itemRender = ItemCell})
    self.reward_list:SetStartZeroIndex(false)
end

function XianLingGuZhenResult:OnFlush()
	local data_list = XianLingGuZhenWGData.Instance:GetXianLingResultRewardDataList()
	local pool_seq = XianLingGuZhenWGData.Instance:GetMarkRewardPoolSeq()

	local number_str = XianLingGuZhenWGData.Instance:GetDrawNumberStr(1, pool_seq)
	if not IsEmptyTable(data_list) then
		self.reward_list:SetDataList(data_list)
		self.node_list.number_text.text.text = number_str
	end
end
