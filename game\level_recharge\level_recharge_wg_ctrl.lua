require("game/level_recharge/level_recharge_view")
require("game/level_recharge/level_recharge_wg_data")

LevelRechargeWGCtrl = LevelRechargeWGCtrl or BaseClass(BaseWGCtrl)

function LevelRechargeWGCtrl:__init()
	if LevelRechargeWGCtrl.Instance then
		ErrorL<PERSON>("[LevelRechargeWGCtrl] attempt to create singleton twice!")
		return
	end

	LevelRechargeWGCtrl.Instance = self
	self.data = LevelRechargeWGData.New()
    self.view = LevelRechargeView.New(GuideModuleName.LevelRechargeView)

	self:RegiaterLevelChange()
end

function LevelRechargeWGCtrl:__delete()
	LevelRechargeWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil	
	end

	if self.data then
	    self.data:DeleteMe()
		self.data = nil	
	end

	self:UnRegiaterLevelChange()
end

function LevelRechargeWGCtrl:RegiaterLevelChange()
	if self.data:GetIsShowTip() then
		self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
		RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
	end 
end

function LevelRechargeWGCtrl:UnRegiaterLevelChange()
	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

-- 角色属性改变
function LevelRechargeWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
     	MainuiWGCtrl.Instance:FlushView(0, "flush_level_recharge_tip")
	end

	if not self.data:GetIsShowTip() then
		self:UnRegiaterLevelChange()
	end
end