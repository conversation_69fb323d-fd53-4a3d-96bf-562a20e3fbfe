require("game/scene/follow_ui/follow_under_bar")
require("game/scene/follow_ui/follow_aim")
UnderFollowUi = UnderFollowUi or BaseClass(VisibleObj)

function UnderFollowUi:__init(vo)
	self.vo = vo
	self.shield_obj_type = ShieldObjType.UnderFollowUI

	self.follow_target = nil
	self.bundle = nil
	self.is_root_created = false

	self.is_root_visible = true
	self.need_create_root = false
	self.name_off_y = 0

	self.is_enter_scene = false
	self.aim_eff_state = false
	self.aim_obj = nil

	self.under_bar = FollowUnderBar.New()
end

function UnderFollowUi:__delete()
	if nil ~= self.view then
		self:ResumeDefaultFollowPos()

		self.view.uifollow_target.Canvas = nil
		self.view.uifollow_target.Target = nil
		self.view.uifollow_distance.TargetTransform = nil
	end

	self.bundle = nil

	self.follow = nil
	self.follow_target_attach_point = nil

	if nil ~= self.under_bar then
		self.under_bar:DeleteMe()
		self.under_bar = nil
	end

	if self.aim_obj ~= nil then
		self.aim_obj:DeleteMe()
		self.aim_obj = nil
	end

	self.aim_eff_state = false
	self.aim_obj = nil
end

function UnderFollowUi:OnEnterScene(is_enter_scene)
	self.is_enter_scene = is_enter_scene
	if is_enter_scene and self.obj_type then
		self:Create(self.obj_type)
	end
end

function UnderFollowUi:Create(obj_type)
	self.obj_type = obj_type
	self:CreateShieldHandle()
	if not self.is_root_visible then
		self.need_create_root = true
		return
	end

	if self.is_root_created or not self.is_enter_scene then
		return
	end

	self.is_root_created = true

	if obj_type then
		self.obj_type = obj_type
	end

	local async_loader = AllocAsyncLoader(self, "root_loader")
	async_loader:SetIsUseObjPool(true)
	async_loader:SetLoadPriority(ResLoadPriority.low)
	local canvas = FollowUi.GetFloatingCanvas()
	async_loader:SetParent(canvas.transform, false)

	local asset_name = "FollowUi"
	-- if nil ~= UIAttachName then
	-- 	asset_name = "NewFollowUi"
	-- end
	async_loader:Load("uis/view/miscpre_load_prefab", asset_name, function (gameobj)
		if IsNil(gameobj) then
			return
		end

		self.view = U3DObject(gameobj)
		local name_table = gameobj:GetComponent(typeof(UINameTable))
		self.node_list = U3DNodeList(name_table, self)

		self.under_bar:SetFollowParent(self.obj_type, gameobj)

		self:UpdateFollowTarget()
		self:UpdateRootNodeVisible()
		self:UpdateFollowPos()
		self:OnRootCreateCompleteCallback(gameobj)
		if self.aim_eff_state then
			self:ShowAimEffect(self.aim_eff_state)
		end
	end)
end

function UnderFollowUi:OnRootCreateCompleteCallback(gameobj)
	-- override
end

function UnderFollowUi:SetFollowTarget(attach_point, follow_target_name)
	self.follow_target_attach_point = attach_point
	self.follow_target_name = follow_target_name
	self:UpdateFollowTarget()
end

function UnderFollowUi:UpdateFollowTarget()
	if nil ~= self.follow_target_attach_point and nil ~= self.view then
		local follow_target_com = self.view:GetComponent(typeof(UIFollowTarget))
		local canvas = FollowUi.GetFloatingCanvas()
		follow_target_com.Canvas = canvas
		follow_target_com.Target = self.follow_target_attach_point

		self.view.uifollow_distance.TargetTransform = self.follow_target_attach_point

		self.view.gameObject.name = string.format("follow_ui(%s)", self.follow_target_name or "")
	end
end

function UnderFollowUi:SetLocalUI(x,y,z)
	self.follow_pos = {x = x, y = y}
	self:UpdateFollowPos()
end

function UnderFollowUi:GetLocalUi()
	-- body
	return self.follow_pos
end

function UnderFollowUi:UpdateFollowPos()
	if nil ~= self.follow_pos and nil ~= self.view then
		if nil == self.default_follow_pos then
			self.default_follow_pos = self.node_list["Follow"].transform.localPosition
		end
		self.node_list["Follow"]:SetLocalPosition(self.follow_pos.x, self.follow_pos.y, 0)
	end
end

function UnderFollowUi:ResumeDefaultFollowPos()
	if nil ~= self.default_follow_pos and nil ~= self.node_list["Follow"] then
		self.node_list["Follow"].transform.localPosition = self.default_follow_pos
	end
end

function UnderFollowUi:VisibleChanged(visible)
	if visible then
		self:Show()
	else
		self:Hide()
	end
end

function UnderFollowUi:Show()
	if self.is_root_visible then
		return
	end

	self.is_root_visible = true
	if self.need_create_root then
		self:Create(self.obj_type)
		self.need_create_root = false
	end
	self:UpdateRootNodeVisible()
end

function UnderFollowUi:Hide()
	if not self.is_root_visible then
		return
	end

	self.is_root_visible = false
	self:UpdateRootNodeVisible()
end

function UnderFollowUi:UpdateRootNodeVisible()
	if nil ~= self.is_root_visible and nil ~= self.view then
		self.view:SetActive(self.is_root_visible)
	end
end

function UnderFollowUi:SetUnderBarVisiable(value)
	self.under_bar:SetVisible(value)
end

function UnderFollowUi:GetUnderBarVisiable()
	return self.under_bar:UnderBarIsVisiable()
end

function UnderFollowUi:SetUnderBarShowInfo(show_type, param, timer_str, call_back)
	self.under_bar:SetShowInfo(show_type, param, timer_str, call_back)
end

function UnderFollowUi:ShowAimEffect(value)
	self.aim_eff_state = value
	if self.node_list == nil then
		return
	end

	if self.aim_obj == nil then
		self.aim_obj = FollowAim.New()
		self.aim_obj:SetFollowParent(self.obj_type, self.node_list["Follow"].gameObject)
	end

	self.aim_obj:SetIsShow(self.aim_eff_state)
end