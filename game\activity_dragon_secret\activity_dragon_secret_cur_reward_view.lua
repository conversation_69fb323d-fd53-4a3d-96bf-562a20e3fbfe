ActicityCurRewardView = ActicityCurRewardView or BaseClass(SafeBaseView)

function ActicityCurRewardView:__init()
    self:SetMaskBg()
    self.view_layer = UiLayer.Normal

    self:AddViewResource(0, "uis/view/activity_dragon_secret_ui_prefab", "layout_current_list")
end

function ActicityCurRewardView:ReleaseCallBack()
    if self.conver_list then
        self.conver_list:DeleteMe()
        self.conver_list = nil
    end

    if self.big_reward_cell then
        self.big_reward_cell:DeleteMe()
        self.big_reward_cell = nil
    end
end

function ActicityCurRewardView:LoadCallBack()
    --退出按钮
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))

    --初始化列表
    if self.conver_list == nil then
        local bundle, asset = "uis/view/activity_dragon_secret_ui_prefab", "prize_items_cell"
        self.conver_list = AsyncBaseGrid.New()
		self.conver_list:CreateCells(
            {col = 2,
            change_cells_num = 1,
            list_view = self.node_list.current_jackpot_list,
			assetBundle = bundle,
            assetName = asset,
            itemRender = CurRewardConvertRender}
        )
        self.conver_list:SetStartZeroIndex(false)
    end

    --大奖栏信息
    self.big_reward_cell = ItemCell.New(self.node_list["item_box_cell"])
end

function ActicityCurRewardView:OnFlush()
    --更新当前轮次道具数量
    self:FlushItemNum()

    --更新当前轮次大奖信息
    self:FlushPrizeInformation()
end

function ActicityCurRewardView:FlushItemNum()
    local prize_cfg_list = ActivityDragonSecretWGData.Instance:GetSmallPrizeList()
  --  print_error("prize_cfg_list", prize_cfg_list)
    self.conver_list:SetDataList(prize_cfg_list)
end

function ActicityCurRewardView:FlushPrizeInformation()
    local grade = ActivityDragonSecretWGData.Instance:GetActDraGrade()
    local round = ActivityDragonSecretWGData.Instance:GetActDraRound()
    local cfg = ActivityDragonSecretWGData.Instance:GetCurrentList(grade, round)
    if not cfg[1] then
        return
    end

    self.big_reward_cell:SetData(cfg[1].item)

    local prize_name = ItemWGData.Instance:GetItemName(cfg[1].item.item_id)
    --拿到大奖中的最大数量
    local reward_max_times = cfg[1].max_times
    --当前大奖中抽中的次数
    local reward_smoking = ActivityDragonSecretWGData.Instance:GetRewardPool(cfg[1].seq)
    --奖池中剩余次数
    local reward_reside_num = reward_max_times - reward_smoking
   -- print_error(reward_max_times)
    local color = reward_reside_num <= 0 and COLOR3B.D_RED or COLOR3B.D_GREEN
    self.node_list["jackpot_txt"].text.text = prize_name .. ToColorStr(string.format("%s/%s", reward_reside_num, reward_max_times), color)
end






-----列表格子
CurRewardConvertRender = CurRewardConvertRender or BaseClass(BaseRender)
function CurRewardConvertRender:LoadCallBack()
   self.item_cell = ItemCell.New(self.node_list["prize_item"])
end

function CurRewardConvertRender:ReleaseCallBack()
     if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
     end
end

function CurRewardConvertRender:OnFlush()
 --   print_error("self.data", self.data.item.item_id)
   if self.data == nil then
       return
   end
   
   --小奖信息
   self.item_cell:SetData(self.data.item)

   local small_prize_name = ItemWGData.Instance:GetItemName(self.data.item.item_id)
   self.node_list["prize_name_txt"].text.text = small_prize_name

    --拿到大奖中的最大数量
    local small_reward_max_times = self.data.max_times
    --当前小奖中抽中的次数
    local small_reward_smoking = ActivityDragonSecretWGData.Instance:GetRewardPool(self.data.seq)
    --奖池中剩余次数
    local small_reward_reside_num = small_reward_max_times - small_reward_smoking
  --  print_error(small_reward_reside_num)
    local color = small_reward_reside_num <= 0 and COLOR3B.D_RED or COLOR3B.D_GREEN
    self.node_list["prize_num_txt"].text.text = ToColorStr(string.format("%s/%s", small_reward_reside_num, small_reward_max_times), color)
end




