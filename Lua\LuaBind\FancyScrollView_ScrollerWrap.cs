﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FancyScrollView_ScrollerWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(FancyScrollView.Scroller), typeof(UnityEngine.EventSystems.UIBehaviour));
		<PERSON><PERSON>Function("SetViewport", SetViewport);
		<PERSON><PERSON>Function("OnValueChanged", OnValueChanged);
		<PERSON><PERSON>unction("OnSelectionChanged", OnSelectionChanged);
		<PERSON><PERSON>RegFunction("SetTotalCount", SetTotalCount);
		<PERSON><PERSON>RegFunction("ScrollTo", ScrollTo);
		<PERSON><PERSON>RegFunction("JumpTo", JumpTo);
		<PERSON><PERSON>Function("GetMovementDirection", GetMovementDirection);
		<PERSON>.RegFunction("UpdatePosition", UpdatePosition);
		<PERSON><PERSON>RegFunction("RefreshActiveCellViews", RefreshActiveCellViews);
		<PERSON><PERSON>unction("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("ViewportSize", get_ViewportSize, null);
		L.RegVar("ScrollDirection", get_ScrollDirection, null);
		L.RegVar("MovementType", get_MovementType, set_MovementType);
		L.RegVar("Elasticity", get_Elasticity, set_Elasticity);
		L.RegVar("ScrollSensitivity", get_ScrollSensitivity, set_ScrollSensitivity);
		L.RegVar("Inertia", get_Inertia, set_Inertia);
		L.RegVar("DecelerationRate", get_DecelerationRate, set_DecelerationRate);
		L.RegVar("SnapEnabled", get_SnapEnabled, set_SnapEnabled);
		L.RegVar("Draggable", get_Draggable, set_Draggable);
		L.RegVar("Scrollbar", get_Scrollbar, null);
		L.RegVar("Position", get_Position, set_Position);
		L.RegVar("Dragging", get_Dragging, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetViewport(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
			obj.SetViewport(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnValueChanged(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
			System.Action<float> arg0 = (System.Action<float>)ToLua.CheckDelegate<System.Action<float>>(L, 2);
			obj.OnValueChanged(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnSelectionChanged(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
			System.Action<int> arg0 = (System.Action<int>)ToLua.CheckDelegate<System.Action<int>>(L, 2);
			obj.OnSelectionChanged(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTotalCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetTotalCount(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ScrollTo(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.ScrollTo(arg0, arg1);
				return 0;
			}
			else if (count == 4 && TypeChecker.CheckTypes<System.Action>(L, 4))
			{
				FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				System.Action arg2 = (System.Action)ToLua.ToObject(L, 4);
				obj.ScrollTo(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 4 && TypeChecker.CheckTypes<EasingCore.Ease>(L, 4))
			{
				FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				EasingCore.Ease arg2 = (EasingCore.Ease)ToLua.ToObject(L, 4);
				obj.ScrollTo(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 4 && TypeChecker.CheckTypes<EasingCore.EasingFunction>(L, 4))
			{
				FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				EasingCore.EasingFunction arg2 = (EasingCore.EasingFunction)ToLua.ToObject(L, 4);
				obj.ScrollTo(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5 && TypeChecker.CheckTypes<EasingCore.Ease, System.Action>(L, 4))
			{
				FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				EasingCore.Ease arg2 = (EasingCore.Ease)ToLua.ToObject(L, 4);
				System.Action arg3 = (System.Action)ToLua.ToObject(L, 5);
				obj.ScrollTo(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 5 && TypeChecker.CheckTypes<EasingCore.EasingFunction, System.Action>(L, 4))
			{
				FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				EasingCore.EasingFunction arg2 = (EasingCore.EasingFunction)ToLua.ToObject(L, 4);
				System.Action arg3 = (System.Action)ToLua.ToObject(L, 5);
				obj.ScrollTo(arg0, arg1, arg2, arg3);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FancyScrollView.Scroller.ScrollTo");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int JumpTo(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.JumpTo(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMovementDirection(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			FancyScrollView.MovementDirection o = obj.GetMovementDirection(arg0, arg1);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdatePosition(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				obj.UpdatePosition(arg0);
				return 0;
			}
			else if (count == 3)
			{
				FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				obj.UpdatePosition(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FancyScrollView.Scroller.UpdatePosition");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshActiveCellViews(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)ToLua.CheckObject<FancyScrollView.Scroller>(L, 1);
			obj.RefreshActiveCellViews();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ViewportSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			float ret = obj.ViewportSize;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ViewportSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ScrollDirection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			FancyScrollView.ScrollDirection ret = obj.ScrollDirection;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollDirection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MovementType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			FancyScrollView.MovementType ret = obj.MovementType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MovementType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Elasticity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			float ret = obj.Elasticity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Elasticity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ScrollSensitivity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			float ret = obj.ScrollSensitivity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollSensitivity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Inertia(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			bool ret = obj.Inertia;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Inertia on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DecelerationRate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			float ret = obj.DecelerationRate;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DecelerationRate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SnapEnabled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			bool ret = obj.SnapEnabled;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SnapEnabled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Draggable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			bool ret = obj.Draggable;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Draggable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Scrollbar(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			UnityEngine.UI.Scrollbar ret = obj.Scrollbar;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Scrollbar on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Position(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			float ret = obj.Position;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Position on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Dragging(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			bool ret = obj.Dragging;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Dragging on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MovementType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			FancyScrollView.MovementType arg0 = (FancyScrollView.MovementType)ToLua.CheckObject(L, 2, typeof(FancyScrollView.MovementType));
			obj.MovementType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MovementType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Elasticity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Elasticity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Elasticity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ScrollSensitivity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.ScrollSensitivity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollSensitivity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Inertia(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.Inertia = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Inertia on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DecelerationRate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.DecelerationRate = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DecelerationRate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SnapEnabled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SnapEnabled = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SnapEnabled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Draggable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.Draggable = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Draggable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Position(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.Scroller obj = (FancyScrollView.Scroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Position = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Position on a nil value");
		}
	}
}

