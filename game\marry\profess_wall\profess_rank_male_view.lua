ProfessRankMaleView = ProfessRankMaleView or BaseClass(BaseRender)
local activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK

function ProfessRankMaleView:__init()
	self:LoadCallBack()
end

function ProfessRankMaleView:ReleaseCallBack()
	for k,v in pairs(self.rank_item_list) do
		v:DeleteMe()
	end
	self.rank_item_list = nil
	self.model = nil

	if self.my_rank then
		self.my_rank:DeleteMe()
		self.my_rank = nil
	end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function ProfessRankMaleView:LoadCallBack()
		--排行榜
	self.rank_item_list = {}
	local rank_list_delegate = self.node_list["RankListView"].list_simple_delegate
	rank_list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfRank<PERSON><PERSON><PERSON>, self)
	rank_list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshRankCell, self)

	--我的信息
	self.my_rank = ProfessRankRender.New(self.node_list["ph_profess_wall"])
	self.my_rank:SetIndex(0)
end

function ProfessRankMaleView:SetModel( model )
	self.model = model
end

function ProfessRankMaleView:ShowIndexCallBack(index)
	self.tab_index = index
	self.rank_list = ProfessWallWGData.Instance:GetProfessRankInfoByRankType(self.tab_index)
	ProfessWallWGCtrl.Instance:SendGetKaifuActivityInfo(activity_type)
	if self.tab_index == TabIndex.profess_wall_rank_male then
		RankWGCtrl.Instance:SendGetPersonRankListReq(PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_MALE)
	else
		RankWGCtrl.Instance:SendGetPersonRankListReq(PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_FEMALE)
	end
	self:FlushMyRankInfo()
	self:Flush()
end

function ProfessRankMaleView:RankCallBack(rank_type, list)

end

function ProfessRankMaleView:FlushPersonModel()
	local reward_item = ProfessWallWGData.Instance:GetProfessFashionImage()
	if reward_item == nil then
		return
	end
	
	local res_id, _, cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(reward_item.item_id)
	self.model:SetRoleResid(res_id)
	--战力

	local fashion_level        = 0
	-- local level_vo             = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(cfg.part_type, cfg.index, fashion_level)
	-- local next_level_vo        = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(cfg.part_type, cfg.index, fashion_level + 1)
	local level_vo             = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(cfg.part_type, cfg.index, fashion_level)
	local next_level_vo        = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(cfg.part_type, cfg.index, fashion_level + 1)
	local level_attribute      = AttributeMgr.GetAttributteByClass(level_vo)
	local next_level_attribute = AttributeMgr.GetAttributteByClass(next_level_vo)
	local add_attribute        = AttributeMgr.LerpAttributeAttr(level_attribute, next_level_attribute)
	add_attribute              = AttributeMgr.AttributeFloorRounding(add_attribute)
	self.node_list["FightPower"].text.text       = "+" .. AttributeMgr.GetCapability(add_attribute)
end

function ProfessRankMaleView:FlushMyRankInfo()
	local my_info_data = ProfessWallWGData.Instance:GetSpecialProfessRankInfo()
	self.my_rank:SetData(my_info_data[1])
	local score = my_info_data[1].limit_score
	self.node_list["integral_desc"].text.text = score > 0 and string.format(Language.ProfessWall.rank_tips, score) or ""
end

-- 榜单Item
function ProfessRankMaleView:GetNumberOfRankCells()
	local rank_num = math.min(#self.rank_list, 20)
	return rank_num
end

function ProfessRankMaleView:RefreshRankCell(cell, data_index)
	data_index = data_index + 1
	local rank_cell = self.rank_item_list[cell]
	if not rank_cell then
		rank_cell = ProfessRankRender.New(cell.gameObject)
		self.rank_item_list[cell] = rank_cell
	end
	rank_cell:SetIndex(data_index)
	local data = self.rank_list[data_index]
	rank_cell:SetData(data)
end

function ProfessRankMaleView:OnFlush(param_t)
	self:FlushMyRankInfo()
	self:FlushPersonModel()
	self.rank_list = ProfessWallWGData.Instance:GetProfessRankInfoByRankType(self.tab_index)
	self.node_list["RankListView"].scroller:ReloadData(0)
	--刷新时间
	if self.time_quest == nil then
		self.time_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushNextTime, self), 1)
		self:FlushNextTime()
	end
end

function ProfessRankMaleView:FlushNextTime()
	local time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK)
	if time <= 0 then
		if self.time_quest then
			GlobalTimerQuest:CancelQuest(self.time_quest)
			self.time_quest = nil
		end
	end

	local time_str = ""
	if time > 3600 then
		time_str = TimeUtil.FormatSecond(time, 6)
	else
		time_str = TimeUtil.FormatSecond(time, 2)
	end

	self.node_list["Time"].text.text = Language.OpenServer.ActRemainTime .. ToColorStr(time_str, COLOR3B.GREEN)
end

----------------------------------- 跨服排行榜榜单Item -----------------------------------
ProfessRankRender = ProfessRankRender or BaseClass(BaseRender)

function ProfessRankRender:__init()
	-- self.limit_score = self.node_list["LimitScore"] 	--最高积分（表白积分达到%s可领取奖励）
	self.reward_item = ItemCell.New(self.node_list["reward_item"])
end

function ProfessRankRender:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function ProfessRankRender:SetIndex(index)
	self.index = index
end

function ProfessRankRender:SetData(data)
	if data == nil then
		return
	end
	self.data = data
	-- 名次
	local rank_index = self.index == 0 and ProfessWallWGData.Instance:GetSelfRankNum() or self.index
	self.node_list["rank_img"]:SetActive(rank_index < 4 and rank_index > 0)
	if rank_index > 3 then
		self.node_list["rank_num"].text.text = rank_index
	elseif rank_index == 0 then
		self.node_list["rank_num"].text.text = Language.Common.No
	else
		self.node_list["rank_num"].text.text = ""
		local bundle, asset = ResPath.GetRankImage("rank_num_" .. rank_index)
		self.node_list["rank_img"].image:LoadSprite(bundle, asset)
		-- print_error("前三排名")
	end
	self.node_list["name"].text.text = self.data.user_name
	-- 表白信息
	--print_error(data)
	self.node_list["to"].text.text = string.format(Language.ProfessWall.rank_list_desc_to_times, data.profess_to_num or 0)
	self.node_list["from"].text.text = string.format(Language.ProfessWall.rank_list_desc_from_times, data.profess_from_num or 0)
	self.node_list["integral_num"].text.text = string.format(Language.ProfessWall.rank_list_desc_score, data.rank_value or 0)  --表白积分

	-- self.limit_score.text.text = string.format(Language.PrefessWall.LimitScore, data.limit_score or 0) --表白积分达到%s可领取奖励

	-- 设置姓名头像信息
	self:SetIconImage()

	-- 奖励
	local reward_item_id = self.data.item_id
	if reward_item_id ~= nil and reward_item_id ~= 0 then
		self.node_list["reward_item"]:SetActive(true)
		self.reward_item:SetData(reward_item_id)
	else
		self.node_list["reward_item"]:SetActive(false)
	end
end

function ProfessRankRender:SetIconImage()
	local role_id = self.data.uid
	XUI.UpdateRoleHead(self.node_list["ph_lover_head"], self.node_list.custom_lover_head, role_id, self.data.sex,self.data.prof,nil,true)
end
