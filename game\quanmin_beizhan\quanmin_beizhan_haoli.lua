function QuanMinBeiZhanView:InitHaoLiView(index)
	local theme_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeCfg(TabIndex.quanmin_beizhan_haoli)
	self.node_list.hl_tip_label.text.text = theme_cfg.rule_tip
	self.node_list.hl_xuanchuantu.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(theme_cfg.xuanchuantu))

	self:HaoLiTimeCountDown()
	XUI.AddClickEventListener(self.node_list.hl_tip, BindTool.Bind(self.OnHLBtnTipClickHnadler, self))
	XUI.AddClickEventListener(self.node_list.hl_btn_libao, BindTool.Bind(self.OnHLLBBtnClickHnadler, self))
	for i = 1, 5 do
		XUI.AddClickEventListener(self.node_list["hl_tab_btn" .. i], function()
			TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.QuanMinBeiZhan.HaoLiStr1, i))
		end)
	end
	self:FlusnHaoLiBtns()
end

function QuanMinBeiZhanView:FlusnHaoLiBtns()
	local cur_day = QuanMinBeiZhanWGData.Instance:GetHaoLiDayNum()
	local theme_count = QuanMinBeiZhanWGData.Instance:GetThemeCount()
	for i = 1, theme_count do
		if i > 5 then
			break
		end

		if i >= cur_day + 1 then
			self.node_list["hl_tab_btn" .. i]:SetActive(true)
		else
			self.node_list["hl_tab_btn" .. i]:SetActive(false)
		end
	end
end

function QuanMinBeiZhanView:ReleaseHaoLiView()
	if self.hl_reward_list then
		self.hl_reward_list:DeleteMe()
		self.hl_reward_list = nil
	end

	if self.haoli_count_down and CountDownManager.Instance:HasCountDown(self.haoli_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.haoli_count_down)
	end
end

function QuanMinBeiZhanView:FlushHaoLiView(index)
	--self.node_list.hl_btn_libao:SetActive(QuanMinBeiZhanWGData.Instance:IsShowHaoLiRedPoint() > 0)
	self.node_list.hl_btn_libao:SetActive(false)

	if index ~= nil then
		self.index = index
	end

	local list = QuanMinBeiZhanWGData.Instance:GetHaoLigift(self.index)
	if list ~= nil then
		if not self.hl_reward_list then
			self.hl_reward_list = AsyncListView.New(BZHLItemRender, self.node_list.hl_reward_list)
		end
		self.hl_reward_list:SetDataList(list)
	end
end

function QuanMinBeiZhanView:OnHLBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = QuanMinBeiZhanWGData.Instance:GetActivityTip(TabIndex.quanmin_beizhan_haoli)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

function QuanMinBeiZhanView:OnHLLBBtnClickHnadler()
	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_BEIZHANHAOLI, BEIZHANHAOLI_OP_TYPE.TYPE_LIBAO)
end

--有效时间倒计时
function QuanMinBeiZhanView:HaoLiTimeCountDown()
	self.haoli_count_down = "haoli_count_down"
	local invalid_time = QuanMinBeiZhanWGData.Instance:GetActivityInValidTime(TabIndex.quanmin_beizhan_haoli)
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.hl_time.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown(self.haoli_count_down, BindTool.Bind1(self.UpdateHLCountDown, self), BindTool.Bind1(self.OnHLComplete, self), invalid_time, nil, 1)
	end
end

function QuanMinBeiZhanView:UpdateHLCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.hl_time.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function QuanMinBeiZhanView:OnHLComplete()
	self.node_list.hl_time.text.text = Language.QuanMinBeiZhan.ActivityStr1
end

------------------------------------------------------------------------------------------------------

BZHLItemRender = BZHLItemRender or BaseClass(BaseRender)

function BZHLItemRender:LoadCallBack()
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnClick, self))
	self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
end

function BZHLItemRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function BZHLItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	self.node_list.name.text.text = data.gift_name
	self.node_list.old_price.text.text = data.old_price
	self.node_list.new_price.text.text = data.price
	self.node_list.old_icon.image:LoadSprite(ResPath.GetF2CommonIcon(ResPath.GetF2MoneyIcon(data.price_type)))
	self.node_list.new_icon.image:LoadSprite(ResPath.GetF2CommonIcon(ResPath.GetF2MoneyIcon(data.price_type)))

	local gift_buy_count = QuanMinBeiZhanWGData.Instance:GetBeiZanHaoliBuyCount(data.theme, data.gift_index)
	if gift_buy_count >= data.role_buy_limit then
		self.node_list.btn_label.text.text = Language.QuanMinBeiZhan.LiBaoStr1
		self.node_list.btn:SetActive(false)
		self.node_list.yilingqu:SetActive(true)
	else
		self.node_list.btn_label.text.text = Language.QuanMinBeiZhan.LiBaoStr2
		self.node_list.btn:SetActive(true)
		self.node_list.yilingqu:SetActive(false)
	end

	self.item_list:SetDataList(SortTableKey(data.reward_item))
end

function BZHLItemRender:OnBtnClick()
	local data = self:GetData()
	if not data then
		return
	end
	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_BEIZHANHAOLI, BEIZHANHAOLI_OP_TYPE.TYPE_BUY, data.theme, data.gift_index)
end