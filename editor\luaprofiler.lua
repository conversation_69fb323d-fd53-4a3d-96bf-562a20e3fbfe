LuaProfiler = {}
LuaProfiler.call_stack = {}
LuaProfiler.summary_stats = {}
LuaProfiler.is_profiling = false

-- 从统计列表中排除
local exclude_file_list = {
	"^editor/.*",
	"^systool/.*",
	"^game/common/.*",
}

function LuaProfiler:Start()
	if LuaProfiler.is_profiling then
		return
	end

	LuaProfiler.is_profiling = true
	debug.sethook(function (hooktype)
		local info = debug.getinfo(2, 'nS')
	    if hooktype == "call" then
	       	self:OnMethodCall(info.short_src, info.linedefined, info.name)
	    elseif hooktype == "return" then
	    	self:OnMethodReturn(info.short_src, info.linedefined, info.name)
	    end
	end, 'cr', 0)
end

function LuaProfiler:Stop()
	if not LuaProfiler.is_profiling then
		return
	end

	LuaProfiler.is_profiling = false
	debug.sethook(nil)
end

function LuaProfiler:IsFilter(file_name, fun_name)
	if file_name == "[C]" or nil == fun_name then
		return true
	end

	if "play" == file_name 
		or "update" == file_name
		or "main" == file_name then
		return true
	end

	-- for _, v in ipairs(exclude_file_list) do
	-- 	if string.match(file_name, v) then
	-- 		return true
	-- 	end
	-- end

	return false
end

function LuaProfiler:OnMethodCall(file_name, line, fun_name)
	if self:IsFilter(file_name, fun_name) then
		return
	end

	local t = {name = string.format("%s[%d]:%s", file_name, line, fun_name),
				start_time = XCommon:getHighPrecisionTime()}
	table.insert(self.call_stack, t)
end

function LuaProfiler:OnMethodReturn(file_name, line, fun_name)
	if self:IsFilter(file_name, fun_name) then
		return
	end

	local name = string.format("%s[%s]:%s", file_name, line, fun_name)
	local now_time = XCommon:getHighPrecisionTime()
	while #self.call_stack > 0 do
		local t = table.remove(self.call_stack, #self.call_stack)
		if t.name == name then
			if now_time - t.start_time > 0.03 then

				local call_stats_list = self.summary_stats[name]
				if nil == call_stats_list then
					call_stats_list = {}
					self.summary_stats[name] = call_stats_list
				end

				table.insert(call_stats_list, now_time - t.start_time)

				-- print("【LuaProfiler】", t.name, now_time - t.start_time)
			end
			
			break
		end
	end
end

function LuaProfiler:Save()
	local output = "Name,Count,Total(ms),AvgTime(ms),MaxTime(ms)\n"
	for k,list in pairs(self.summary_stats) do
		local total_time = 0
		local count = #list
		local max_time = 0
		for _, v in ipairs(list) do
			total_time = total_time + v
			if v > max_time then
				max_time = v
			end
		end

		output = output .. string.format("%s,%d,%f,%f,%f\n",
				k, count, total_time, total_time / count, max_time
			)
	end

	local file=io.output("profiler.csv")
	io.write(output)
	io.flush()
	io.close()

	print("保存成功")
end