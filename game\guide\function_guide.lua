------------------------------------------------------
--功能引导
------------------------------------------------------
FunctionGuide = FunctionGuide or BaseClass()
FunctionGuide.GUIDE_JUMP_TARGET = nil					-- 引导跳跃的目标
NextGuideStepFlag = "NextGuideStepFlag"
EndGuideFlag = "EndGuideFlag"
LastGuideStepFlag = "LastGuideStepFlag"
GuideWait = "GuideWait"
GuidePause = "GuidePause"
FunctionGuide.shangjin_id = 12 		-- 赏金引导id
--下次可自动引导时间
NEXT_CAN_AUTO_GUIDE_TIME = 0

function FunctionGuide:__init()
	if FunctionGuide.Instance ~= nil then
		ErrorLog("[FunctionGuide] attempt to create singleton twice!")
		return
	end
	FunctionGuide.Instance = self
	-- 初始化默认打开自动引导
	self:InitGuideCfg()
	self:InitParam()
	self:InitGuidePanel()
	self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.OnLoadingComplete, self))
	self:SetOpenFunGuide(true)

	self:ResetZhiShengDanCache()
end

-- 初始化引导配置
function FunctionGuide:InitGuideCfg()
	self.guide_list_cfg = ConfigManager.Instance:GetAutoConfig("function_guide_auto").guide_list
	self.guide_step_list_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("function_guide_auto").step_list, "step_list_id")
end

-- 获取当前的引导步骤
function FunctionGuide:GetGuideStepListCfg(guide_cfg)
	if (not guide_cfg) or (not guide_cfg.step_list_id) then
		return nil
	end

	return (self.guide_step_list_cfg or {})[guide_cfg.step_list_id]
end

-- 获取当前的引导步骤
function FunctionGuide:GetGuideStepCfg(guide_cfg, step)
	if (not guide_cfg) or (not guide_cfg.step_list_id) then
		return nil
	end

	return ((self.guide_step_list_cfg or {})[guide_cfg.step_list_id] or {})[step]
end

function FunctionGuide:InitParam()
	self.is_open_fun_guide = true					-- 是否开启整个功能引导
	self.not_find_ui_time = 5						-- 查找ui时间（找不到则停止引导）
	self.is_guiding = false 						-- 是否引导中
	self.is_pausing = false							-- 是否暂停引导（只是界面）
	self.is_stop_task = false						-- 是否强制停止任务
	self.stop_guaji_cache = nil						-- 停止挂机缓存
	self.keyequip_guide_levelimit = 0				-- 一键装备等级限制

	self.cur_guide_cfg = nil						-- 当前引导配置
	self.cur_guide_step = 0							-- 当前引导步骤
	self.get_guide_ui_callback_list = {}			-- 回调列表集合	
	self.wait_guide_list = {}						-- 等待引导的列表
	self.is_guide_list = {}							-- 已经引导过的列表（步骤列表）
	self.check_callback_map = {}					-- 回调集合
	self.check_gap_time = 0.03						-- 检测时间间隔
	self.prve_time = 0								-- 当前的时间
	self.try_times = 0								-- 尝试引导的次数
	self.market_pop_use_time = 0					-- 上架市场的时间
	self.is_close_all = false 						-- 本次引导是否已经执行过一次关闭所有面板
	self.guide_view = ""							-- 当前引导的界面名称
	self.clickCallback = nil						-- 点击事件回调
	self.guide_end_callback = nil					-- 引导结束回调
	self.last_guide_id = nil						-- 最后的引导id
end

function FunctionGuide:DelectParam()
	self.is_open_fun_guide = nil					-- 是否开启整个功能引导
	self.not_find_ui_time = nil						-- 查找ui时间（找不到则停止引导）
	self.is_guiding = nil 							-- 是否引导中
	self.is_pausing = nil							-- 是否暂停引导（只是界面）
	self.is_stop_task = nil							-- 是否强制停止任务
	self.stop_guaji_cache = nil						-- 停止挂机缓存
	self.keyequip_guide_levelimit = nil				-- 一键装备等级限制

	self.cur_guide_cfg = nil						-- 当前引导配置
	self.cur_guide_step = nil						-- 当前引导步骤
	self.get_guide_ui_callback_list = nil			-- 回调列表集合	
	self.wait_guide_list = nil						-- 等待引导的列表
	self.is_guide_list = nil						-- 已经引导过的列表（步骤列表）
	self.check_callback_map = nil					-- 回调集合
	self.check_gap_time = nil						-- 检测时间间隔
	self.prve_time = nil							-- 当前的时间
	self.try_times = nil							-- 尝试引导的次数
	self.market_pop_use_time = nil					-- 上架市场的时间
	self.is_close_all = nil 						-- 本次引导是否已经执行过一次关闭所有面板
	self.guide_view = nil							-- 当前引导的界面名称
	self.clickCallback = nil						-- 点击事件回调
	self.guide_end_callback = nil					-- 引导结束回调
	self.last_guide_id = nil						-- 最后的引导id
	self.cur_step_cfg = nil
end

function FunctionGuide:InitGuidePanel()
	self.normal_guide_view = NormalGuideView.New(GuideModuleName.NormalGuideView)						-- 正常引导
	self.guide_disable_click_view = GuideDisableClickView.New(GuideModuleName.GuideDisableClickView)	-- 不能点击遮罩
	self.automatic_use_equipment_view = AutomaticUseEquipmentView.New()									-- 自动装备武器引导
	self.key_use_view = KeyUseView.New()																-- 自动使用物品引导
	self.market_pop_use_view = MarketPopUseView.New()													-- 提示上架引导

	-- 以下界面迭代未知
	self.offline_guaji_look = OfflineGuJiUseView.New()
	self.girl_guide_view = GirlGuideView.New()															
	self.fade_view = FadeView.New()
	self.fun_mowang_view = FunMoWangView.New(GuideModuleName.GuideMoWang)
	self.normal_area_guild_view = NormalAreaGuideView.New()
end

function FunctionGuide:DelectGuidePanel()
	if self.normal_guide_view then
		self.normal_guide_view:DeleteMe()
		self.normal_guide_view = nil
	end

	if self.guide_disable_click_view then
		self.guide_disable_click_view:DeleteMe()
		self.guide_disable_click_view  = nil
	end

	if self.automatic_use_equipment_view then
		self.automatic_use_equipment_view:DeleteMe()
		self.automatic_use_equipment_view  = nil
	end

	if self.key_use_view then
		self.key_use_view:DeleteMe()
		self.key_use_view  = nil
	end

	if self.market_pop_use_view then
		self.market_pop_use_view:DeleteMe()
		self.market_pop_use_view = nil
	end

	if self.offline_guaji_look then
		self.offline_guaji_look:DeleteMe()
		self.offline_guaji_look = nil
	end

	if self.girl_guide_view then
		self.girl_guide_view:DeleteMe()
		self.girl_guide_view = nil
	end

	if self.fade_view then
		self.fade_view:DeleteMe()
		self.fade_view = nil
	end

	if self.fun_mowang_view then
		self.fun_mowang_view:DeleteMe()
		self.fun_mowang_view = nil
	end

	if self.normal_area_guild_view then
		self.normal_area_guild_view:DeleteMe()
		self.normal_area_guild_view = nil
	end
end

function FunctionGuide:UnBindAllListen()
	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end

	if self.task_change_handle then
		GlobalEventSystem:UnBind(self.task_change_handle)
		self.task_change_handle = nil
	end

	if self.scene_loading_state_quit then
		GlobalEventSystem:UnBind(self.scene_loading_state_quit)
		self.scene_loading_state_quit = nil
	end

	if self.finished_open_fun then
		GlobalEventSystem:UnBind(self.finished_open_fun)
		self.finished_open_fun = nil
	end

	if self.view_open_event then
		GlobalEventSystem:UnBind(self.view_open_event)
		self.view_open_event = nil
	end

	if self.view_close_event then
		GlobalEventSystem:UnBind(self.view_close_event)
		self.view_close_event = nil
	end

	if self.camera_handle_change then
		GlobalEventSystem:UnBind(self.camera_handle_change)
		self.camera_handle_change = nil
	end
end

-- 清理屏蔽规则
function FunctionGuide:ClearnShieldRule()
	-- 轻功引导屏蔽其他玩家
    if self.qinggong_shield_rule then
        self.qinggong_shield_rule:DeleteMe()
        self.qinggong_shield_rule = nil
    end
    -- 轻功引导屏蔽其他玩家影子
    if self.qinggong_shadow_shield_rule then
        self.qinggong_shadow_shield_rule:DeleteMe()
        self.qinggong_shadow_shield_rule = nil
    end
    -- 轻功引导屏蔽其他玩家名字
    if self.qinggong_followui_rule then
        self.qinggong_followui_rule:DeleteMe()
        self.qinggong_followui_rule = nil
    end
end

-- 设置引导功能总开关
function FunctionGuide:SetOpenFunGuide(value)
	if not GLOBAL_AUTO_RUN_TASK_SWITCH then --编辑器控制自动任务
		self.is_open_fun_guide = false
		return
	end

	self.is_open_fun_guide = value
end

function FunctionGuide:__delete()
	self:DelectParam()
	self:DelectGuidePanel()
	self:UnBindAllListen()
	self:ClearnShieldRule()
	self:RemoveDelayExcuteTimer()

	-- Runner.Instance:RemoveRunObj(self)
	FunctionGuide.Instance = nil
end

-- 主界面打开完成
function FunctionGuide:OnLoadingComplete()
	GlobalTimerQuest:AddDelayTimer(function()
		RoleWGData.Instance:NotifyAttrChange(BindTool.Bind1(self.OnRoleAttrValueChange, self), {"level"})
		ItemWGData.Instance:NotifyDataChangeCallBack(BindTool.Bind1(self.OnItemDataChange, self))
		self.task_change_handle = GlobalEventSystem:Bind(OtherEventType.TASK_CHANGE, BindTool.Bind(self.OnTaskChange, self))
		self.scene_loading_state_quit = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneLoadComplete, self))
		self.finished_open_fun = GlobalEventSystem:Bind(FinishedOpenFun, BindTool.Bind(self.FinishedOpenFun, self))
		self.view_open_event = GlobalEventSystem:Bind(OtherEventType.VIEW_OPEN, BindTool.Bind(self.ViewOpenHandler, self))
		self.view_close_event = GlobalEventSystem:Bind(OtherEventType.VIEW_CLOSE, BindTool.Bind(self.ViewCloseHandler, self))
		self.camera_handle_change = GlobalEventSystem:Bind(MainUIEventType.CAMERA_HANDLE_CHANGE, BindTool.Bind(self.CameraHandleChange, self))

		-- Runner.Instance:AddRunObj(self, 8)
		self:CheckLastGuide()
	end, 2)
end

-------------------------------------------------------------------------------------------------
-------------------------------------------------------------------------------------------------
-- 角色等级发生变化
function FunctionGuide:OnRoleAttrValueChange(key, new_value, old_value)
	if not self.is_open_fun_guide then return end
	local guide_cfg = nil
	if key == "level" then
		guide_cfg = self:GetGuideCfgByTrigger(GuideTriggerType.LevelUp, new_value)

		self:CheckZhiShengDanKeyUse()
	end

	if guide_cfg then
		self:SetCurrentGuideCfg(guide_cfg)
	end
end

-- 检查背包直升丹快速使用
local zhi_sheng_dan_level_map = nil
function FunctionGuide:CheckZhiShengDanKeyUse()
	if zhi_sheng_dan_level_map == nil then
		zhi_sheng_dan_level_map = {}
		local expense_cfg = ItemWGData.Instance:GetExpenseCfg()
		for k,v in pairs(expense_cfg) do
			if v.use_type == Item_Use_Type.ZHI_SHENG_DAN then
				-- 按等级建立索引
				local target_level = v.param1 - 1
				if not zhi_sheng_dan_level_map[target_level] then
					zhi_sheng_dan_level_map[target_level] = {}
				end
				table.insert(zhi_sheng_dan_level_map[target_level], v)
			end
		end
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local level_configs = zhi_sheng_dan_level_map[role_level]
	if level_configs then
		for i, v in ipairs(level_configs) do
			local item_index = ItemWGData.Instance:GetItemIndex(v.id)
			if item_index >= 0 then
				self.key_use_view:AddItem(v.id, item_index)
			end
		end
	end
end


function FunctionGuide:ResetZhiShengDanCache()
	zhi_sheng_dan_level_map = nil
end
-------------------------------------------------------------------------------------------------
-- 物品发生变化引导快速使用
function FunctionGuide:OnItemDataChange(item_id, index, reason, put_reason, old_num, new_num)
	if index >= 999 or (put_reason and put_reason == PUT_REASON_TYPE.PUT_REASON_NO_NOTICE) then
		return
	end

	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil or item_type == -1 or (reason == GameEnum.DATALIST_CHANGE_REASON_CHECK and item_type == GameEnum.ITEM_BIGTYPE_GIF and item_cfg.max_open_num and item_cfg.max_open_num == 1) then
		return
	end

	local is_get = false
	new_num = new_num or 0
	old_num = old_num or 0

	if reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
		is_get = true
	elseif reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then
		if new_num > old_num then
			is_get = true
		elseif item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			is_get = true
		end
	elseif reason == GameEnum.DATALIST_CHANGE_REASON_CHECK then
		is_get = true
	end

	local need_fire_bag = item_cfg.choose_use == 1
	if not is_get and need_fire_bag then
		RemindManager.Instance:Fire(RemindName.BagBag)
	end

	if is_get then
		if Scene.Instance:GetSceneId() == 405 and item_id == 22011 then --经验药直接使用,不提示
			return
		end

		local item_data = ItemWGData.Instance:GetGridData(index)
		if item_data == nil then
			return
		end

		--需要消耗仙玉的礼包，要显示红点 一天只显示一次
		if need_fire_bag or ItemWGData.Instance:GetItemGiftShowRed(item_id) then
			RemindManager.Instance:Fire(RemindName.BagBag)
		end

		--装备拍卖弹窗
		self:NoticeEquipGoMarket(item_id, index)

		--功能开启限制
		local fun_open_name = item_cfg.funopen_limit or ""
		local show_fun_limit = false
		if fun_open_name ~= "" then
			local is_open = FunOpen.Instance:GetFunIsOpened(fun_open_name)
			show_fun_limit = not is_open
		end

		if show_fun_limit then
			return
		end

		-- 钱契判断
		if MoneyTicket[item_id] then
			return
		end

		--离线挂机卡特殊拦截(id写死)
		local remain_offline_rest_time = OfflineRestWGData.Instance.remain_offline_rest_time
		local hour = math.floor(remain_offline_rest_time / 3600)
		if (item_id == SPECIAL_GUAJICARD.IDONE or  item_id == SPECIAL_GUAJICARD.IDTWO) and hour >= 20 then
			return
		end

		local flag = true
		local sex, prof = RoleWGData.Instance:GetRoleSexProf()
		if item_cfg.limit_prof ~= GameEnum.ROLE_PROF_NOLIMIT and prof ~= item_cfg.limit_prof then
			flag = false
		end

		if item_cfg.limit_sex ~= GameEnum.SEX_NOLIMIT and sex ~= item_cfg.limit_sex then
			flag = false
		end

		if RoleWGData.Instance.role_vo.level < ItemWGData.Instance:GetEquipLimitLevel(item_cfg) then
			flag = false
		end

		if item_cfg.sub_type == GameEnum.EQUIP_TYPE_JINGLING then
			flag = false
		end

		if flag and item_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			flag = item_cfg.is_tip_use == 1
		end

		if flag and item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			local is_treasure = EquipWGData.CheckIsTreasure(item_cfg.sub_type)
			flag = (item_cfg.click_use == 1 and not is_treasure)
		end

		--时装类
		if flag then
			local part_type = NewAppearanceWGData.Instance:GetFashionTypeByItemId(item_id)
			if part_type ~= -1 then
				flag = self:CheckCanOpenFashionKeyUse(part_type, item_id)
			end
		end

		--骑宠类
		if flag then
			local qc_type = NewAppearanceWGData.Instance:GetQiChongTypeByItemId(item_id)
			if qc_type >= 0 then
				flag = NewAppearanceWGData.Instance:CheckQiChongCanQuickUes(item_id, index)
			end
		end

		--称号判断 --已经激活的称号不再提示
		if flag then
			local title_cfg = TitleWGData.Instance:GetTitleConfigByItemId(item_id)
			if title_cfg and not IsEmptyTable(title_cfg) then
				local is_title_active = TitleWGData.Instance:IsThisTitleActive(title_cfg.title_id)
				flag = not is_title_active
			end
		end

		--  情缘装备同心锁不显示
		if flag and ItemWGData.Instance:GetIsQingyuanEquip(item_id) then
			--if MarryWGData.Instance:GetIsBatterEquip(item_cfg.id) then
				flag = false
			--end
		end

		-- 角色装备
		if flag and item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_HUNJIE
			and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIAOGUI then
			flag = EquipWGData.Instance:GetIsBetterEquip(item_data)
		end

		local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
		if flag then
			if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then		--装备类型
				local xiaogui_info = EquipWGData.Instance:GetmpGuardInfo()
				if equip_index == GameEnum.EQUIP_TYPE_XIAOGUI and xiaogui_info.item_wrapper.item_id ~= 0
					and xiaogui_info.item_wrapper.item_id == item_id then
					return
				end

				-- 婚戒
				if item_cfg.sub_type == GameEnum.EQUIP_TYPE_HUNJIE then
					-- self.key_use_view:Open()
					self.key_use_view:AddItem(item_id, index, function()
							BagWGCtrl.Instance:SendUseItem(index, 1, nil, item_cfg.need_gold)
						end, RoleWGData.Instance.role_vo.level <= self.keyequip_guide_levelimit + 280)

				elseif RoleWGData.Instance.role_vo.level <= self.keyequip_guide_levelimit then
					self:OpenAutoMatic(index, item_data, equip_index)

				else
					-- 小鬼 （一大坨）
					if equip_index == GameEnum.EQUIP_TYPE_XIAOGUI then
						if not self.xiaogui_info then
							self.xiaogui_info = item_data
						else
							local old_cfg = EquipmentWGData.GetXiaoGuiCfg(self.xiaogui_info.item_id)
							local cur_cfg = EquipmentWGData.GetXiaoGuiCfg(item_data.item_id)
							if not old_cfg or not cur_cfg then
								return
							end

							if old_cfg.color > cur_cfg.color then
								return
							else
								self.xiaogui_info = item_data
							end
						end

						local guard_info = EquipWGData.Instance:GetmpGuardInfo()
						local equip_list = guard_info.item_wrapper

						local imp_guard_info1 = guard_info["used_imp_type_" .. 1]
						local imp_guard_info2 = guard_info["used_imp_type_" .. 2]
						local imp_equip_data1 = equip_list[1]
						local imp_equip_data2 = equip_list[2]
						if not imp_guard_info1 or not imp_guard_info2 or not imp_equip_data1 or not imp_equip_data2 then
							return
						end

						-- 限时小鬼
						if item_data.item_id == 10101 then
							local time = math.max(item_data.invalid_time - TimeWGCtrl.Instance:GetServerTime(), 0)
							if time <= 0 then
								return
							end
						end

						local is_show_xiaogui_list = {}
						is_show_xiaogui_list[1] = false
						is_show_xiaogui_list[2] = false

						--第一个小鬼格子
						if imp_guard_info1 >= 0 and imp_equip_data1.item_id > 0 then
							local xiaogui_item_cfg = ItemWGData.Instance:GetItemConfig(imp_equip_data1.item_id)
							--角色身上小鬼
							local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(imp_equip_data1.item_id)

							--背包内小鬼
							local xiaogui2_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
							if xiaogui_cfg and xiaogui2_cfg and xiaogui_cfg.impguard_type == xiaogui2_cfg.impguard_type then
								if item_data.item_id ~= 10101 then
									if xiaogui_item_cfg and item_cfg.color > xiaogui_item_cfg.color then
										is_show_xiaogui_list[1] = true
									end
									if imp_equip_data1.item_id == 10101 then
										is_show_xiaogui_list[1] = true
									end
								end
							end
						elseif imp_guard_info1 >= 0 and imp_equip_data1.item_id <= 0 then

							if imp_guard_info2 >= 0 and imp_equip_data2.item_id > 0 then
								local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(imp_equip_data2.item_id)
								local xiaogui2_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
								if xiaogui_cfg and xiaogui2_cfg and xiaogui_cfg.impguard_type ~= xiaogui2_cfg.impguard_type then
									is_show_xiaogui_list[1] = true
								end
							else
								is_show_xiaogui_list[1] = true
							end
						end

						--第二个小鬼格子
						if imp_guard_info2 >= 0 and imp_equip_data2.item_id > 0 then
							local xiaogui_item_cfg = ItemWGData.Instance:GetItemConfig(imp_equip_data2.item_id)
							--角色身上小鬼
							local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(imp_equip_data2.item_id)

							--背包内小鬼
							local xiaogui2_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)

							if xiaogui_cfg and xiaogui2_cfg and xiaogui_cfg.impguard_type == xiaogui2_cfg.impguard_type then
								if item_data.item_id ~= 10101 then
									if xiaogui_item_cfg and item_cfg.color > xiaogui_item_cfg.color then
										is_show_xiaogui_list[2] = true
									end

									if imp_equip_data1.item_id == 10101 then
										is_show_xiaogui_list[1] = true
									end
								end
							end
						elseif imp_guard_info2 >= 0 and imp_equip_data2.item_id <= 0 then
							local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(imp_equip_data1.item_id)
							local xiaogui2_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
							if xiaogui_cfg and xiaogui2_cfg and xiaogui_cfg.impguard_type ~= xiaogui2_cfg.impguard_type then
								is_show_xiaogui_list[2] = true
							end
						end

						if not is_show_xiaogui_list[2] and not is_show_xiaogui_list[1] then
							return
						end
					end

					-- 有更好的装备的时候提示
					self.key_use_view:AddItem(item_id, index, function ()
					end, RoleWGData.Instance.role_vo.level <= self.keyequip_guide_levelimit + 280)
				end
			-- 直升丹
			elseif item_cfg.use_type == Item_Use_Type.ZHI_SHENG_DAN then
				if RoleWGData.Instance:GetRoleLevel() == item_cfg.param1 - 1 then
					self.key_use_view:AddItem(item_id, index)
				end
			elseif EquipWGData.Instance:CheckIsXiaoGuiComposeItem(item_data.item_id) then
					EquipWGData.Instance:NeedShowXiaoGuiQuickUse(item_data.item_id,index)
					--self.key_use_view:AddItem(item_id, index)
			elseif ControlBeastsWGData.Instance:CheckIsHolyBeastUnlockItemId(item_id) then
				local cfg = ControlBeastsWGData.Instance:GetHolyBeastCfgByUnlockItemId(item_id)
				local holy_beast_data = ControlBeastsWGData.Instance:GetHolyBeastData(cfg.beast_type)
				if holy_beast_data and not holy_beast_data.is_unlock then
					local num = ItemWGData.Instance:GetItemNumInBagById(item_id)
					if num >= cfg.call_cost_item_num then
						self.key_use_view:AddItem(item_id, index)
					end
				end
			else
				--快速使用-财富-未制作
				local auto_use = ActTreasureWGData.Instance:GetAutoUseGiftFlag()
				if auto_use then
					return
				end

				local part_type = NewAppearanceWGData.Instance:GetFashionTypeByItemId(item_id)
				if part_type == -1 then
					local is_active = false
					local baby_type, baby_id = MarryWGData.Instance:GetBabyTypeItemId(item_id)
					if baby_type ~= -1 then
						local baby_flag = MarryWGData.Instance:GetBabyListFlag(baby_type)
						is_active = (baby_flag[32 - baby_id] == 1)
					end

					if is_active then
						return
					end
				end

				self.key_use_view:AddItem(item_id, index)
			end
		end
	end
end

function FunctionGuide:NoticeEquipGoMarket(item_id, index)
	if index >= COMMON_CONSTS.MAX_BAG_COUNT then
    	return
   	end

	local data = ItemWGData.Instance:GetGridData(index)
	if not data or not data.item_id then
		return
	end

	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil or item_type == -1 then
		return
	end

	local flag = false
	if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT
		and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_HUNJIE
		and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIAOGUI then
			flag = true
	end

	if not flag then
		return
	end

	local is_market = MarketWGData.Instance:CheckIsCanMarket(data)
	-- 不能上市不弹
	if not is_market then
		return
	end

	local prof = RoleWGData.Instance:GetRoleProf()
	local sex = RoleWGData.Instance:GetRoleSex()
	local prof_meet = item_cfg.limit_prof == prof or item_cfg.limit_prof == GameEnum.ROLE_PROF_NOLIMIT
	local sex_meet = item_cfg.limit_sex == sex or item_cfg.limit_sex == GameEnum.SEX_NOLIMIT
	local is_sure_prof = prof_meet and sex_meet

	-- 符合本职业装备不弹
	if is_sure_prof then
		return 
	end

	local other_cfg = MarketWGData.Instance:GetOtherCfg()
	-- 等级不足不弹
	local role_level = RoleWGData.Instance:GetAttr('level')
	if other_cfg.equip_shelves_open_level > role_level then
		return
	end

	if Status.NowTime >= self.market_pop_use_time then
		MainuiWGCtrl.Instance:AddInitCallBack(nil,function()
			local can_sell_count = MarketWGData.Instance:CanSellAmount()
			local cur_list_num = MarketWGData.Instance:GetMyGoodsListAmount()
			if can_sell_count - cur_list_num <= 0 then
				return
			end

			self.market_pop_use_view:AddItemShow(item_id, index)
		end)
	end
end

--如果该物品是和时装相关的话，特殊处理
function FunctionGuide:CheckCanOpenFashionKeyUse( part_type, item_id )
	local cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(item_id)
	if nil == cfg then
		return true
	end

	local fashion_level = NewAppearanceWGData.Instance:GetFashionLevelByItemId(cfg.active_stuff_id)
	if fashion_level == 0 or cfg.shizhuang_type == 2 then
		return true
	end

	local max_level = NewAppearanceWGData.Instance:GetFashionMaxLevelById(cfg.active_stuff_id)
	if fashion_level >= max_level then --已满级
		return false
	end

	local upgrade_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfgById(cfg.active_stuff_id)
	local upgrade_stuff_count = NewAppearanceWGData.Instance:GetFashionUpLevelConsumeCfg(upgrade_cfg.up_star_index, fashion_level)

	local num = ItemWGData.Instance:GetItemNumInBagById(upgrade_cfg.stuff_id)
	if num >= upgrade_stuff_count then
		return true
	end

	return false
end

-- 打开自动装备面板
function FunctionGuide:OpenAutoMatic(index, item_data, equip_index, start_pos)
	if self.automatic_use_equipment_view and index ~= nil and item_data ~= nil and equip_index ~= nil then
		local item_cfg = ConfigManager.Instance:GetAutoItemConfig("equipment_auto")[item_data.item_id]
		if item_cfg and next(item_cfg) ~= nil then
			--新增未达到使用等级要求的物品、装备不提示快捷使用规则；需要达到使用等级要求后才会提示快捷使用
			local vo = GameVoManager.Instance:GetMainRoleVo()
			if vo.level < ItemWGData.Instance:GetEquipLimitLevel(item_cfg) then
				return
			end

			if EquipWGData.CheckIsTreasure(item_cfg.sub_type) == false then
				self.automatic_use_equipment_view:AddItem(index, item_data, equip_index, start_pos)
			end
		end
	end
end

-------------------------------------------------------------------------------------------------
-- 任务发生改变
function FunctionGuide:OnTaskChange(task_event_type, task_id)
	if not self.is_open_fun_guide then
		return
	end

	-- 任务相机
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if task_cfg and task_cfg.camera and task_cfg.camera ~= "" then
		local camera_t = Split(task_cfg.camera, "|")
		local camera_c_t, camera_condition,camera_x, camera_y, camera_dis
		for k,v in pairs(camera_t) do
			camera_c_t = Split(v, "##")
			camera_condition, camera_x, camera_y, camera_dis = tonumber(camera_c_t[1]), tonumber(camera_c_t[2]), tonumber(camera_c_t[3]), tonumber(camera_c_t[4])
			if camera_condition == 1 and task_event_type ==TASK_EVENT_TYPE.ACCEPTED
				or  camera_condition == 2 and task_event_type ==TASK_EVENT_TYPE.CAN_COMMIT
				or camera_condition == 3 and task_event_type ==TASK_EVENT_TYPE.COMPLETED
			then
				if IsNil(MainCameraFollow) then
					return
				end

				if camera_x or camera_y or camera_dis then
					local param_t = {task_id = task_id, task_state = camera_condition, not_reset = true}
					Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.TASK, camera_x, camera_y, camera_dis, param_t)
				end
				break
			end
		end
	end

	local guide_cfg = nil
	if task_event_type == TASK_EVENT_TYPE.ACCEPTED then
		guide_cfg = self:GetGuideCfgByTrigger(GuideTriggerType.AcceptTask, task_id)
	elseif task_event_type == TASK_EVENT_TYPE.COMPLETED then
		guide_cfg = self:GetGuideCfgByTrigger(GuideTriggerType.CommitTask, task_id)
	elseif task_event_type == TASK_EVENT_TYPE.CAN_COMMIT then
		guide_cfg = self:GetGuideCfgByTrigger(GuideTriggerType.CanCommintTask, task_id)
	end

	if guide_cfg then
		self:SetCurrentGuideCfg(guide_cfg)
	end
end

-------------------------------------------------------------------------------------------------
-- 一个场景加载完成
function FunctionGuide:OnSceneLoadComplete(old_scene_type, new_scene_type)
	local scene_id = Scene.Instance:GetSceneId()
	if scene_id then
		local guide_cfg = self:GetGuideCfgByTrigger(GuideTriggerType.EnterScene, scene_id)
		if guide_cfg then
			self:SetCurrentGuideCfg(guide_cfg)
		end
	end
end

-------------------------------------------------------------------------------------------------
-- 完成一个功能开启
function FunctionGuide:FinishedOpenFun(state, only_change)
	-- self.is_pausing = state
	-- if only_change then
	-- 	return
	-- end

	-- if state then
	-- 	if self.cur_guide_cfg then
	-- 		if self.cur_guide_step == 1 then
	-- 			PlayerPrefsUtil.SetInt(FunctionGuide.GetGuideLocalKey(), self.cur_guide_cfg.id)
	-- 		end
	-- 		self:EndGuide(true)
	-- 	end
	-- else
	-- 	self:CheckLastGuide()
	-- end
end

-------------------------------------------------------------------------------------------------
-- 打开一个界面
function FunctionGuide:ViewOpenHandler(view, index)
	if view and view.view_name then
		local view_name = view.view_name
		if self:StopGuideViewTable()[view_name] then
			self:FinishedOpenFun(true)
		end

		local old_times, new_times
		for k,v in pairs(self.guide_list_cfg) do
			local can_guide = false
			if v.trigger_type == GuideTriggerType.FuBenPanelResult then
				if view_name == v.guide_main_module then
					if v.trigger_param == "" then
						can_guide = self:GetIsCanDoGuide(v.id)
						if view_name == GuideModuleName.DragonTrialTaskView then -- 五气朝元特殊处理
							can_guide = DragonTrialWGData.Instance:GetCurrentPassSeq() <= 0
						end
					elseif v.trigger_param ~= nil and v.trigger_param ~= "" and v.trigger_param == index then
						-- print_error("触发引导", v.id, v.trigger_param, view_name, self:GetIsCanDoGuide(v.id))
						can_guide = self:GetIsCanDoGuide(v.id)
					end
				end
			elseif v.trigger_type == GuideTriggerType.OpenViewOnTimes then
				if view_name == v.guide_main_module and v.with_param ~= nil and v.with_param == index then
					if self:GetIsCanDoGuide(v.id) then
						if not old_times then
							local key = string.format("OpenViewOnTimes_%s_%s_%s", view_name, index, RoleWGData.Instance:GetOriginUid())
							old_times = PlayerPrefsUtil.GetInt(key, 0)
							new_times = old_times + 1
							PlayerPrefsUtil.SetInt(key, new_times)
						end

						-- print_error("触发引导", new_times, v.trigger_param, view_name)
						if new_times == v.trigger_param then
							can_guide = true
						end
					end
				end
			end

			if can_guide then
				self:SetCurrentGuideCfg(v)
				return
			end
		end
	end
end

-- 打开界面，暂停引导的界面
local STOP_GUIDE_VIEW_T = nil
function FunctionGuide:StopGuideViewTable()
	if STOP_GUIDE_VIEW_T == nil then
		STOP_GUIDE_VIEW_T = {
			[GuideModuleName.AppearanceGetNew] = true,
			[GuideModuleName.AppearanceMount] = true,
			[GuideModuleName.NewAppearanceWGView] = true,
			[GuideModuleName.OpenFunFlyView] = true,
			[GuideModuleName.TipsShowChapterView] = true,
			[GuideModuleName.GuideMoWang] = true,
			[GuideModuleName.FirstRechargeView] = true,
		}
	end
	return STOP_GUIDE_VIEW_T
end

-------------------------------------------------------------------------------------------------
-- 关闭一个界面
function FunctionGuide:ViewCloseHandler(view)
	if STOP_GUIDE_VIEW_T and view and view.view_name and STOP_GUIDE_VIEW_T[view.view_name] then
		local can_continue = true
		for k,v in pairs(STOP_GUIDE_VIEW_T) do
			if ViewManager.Instance:IsOpen(k) then
				can_continue = false
				break
			end
		end
		if can_continue then
			self:FinishedOpenFun(false)
		end
	end
end

-------------------------------------------------------------------------------------------------
-- 摄像机发生变化
function FunctionGuide:CameraHandleChange()
	if CAMERA_TYPE == CameraType.Fixed then
		if self.fightstate_camera_param then
			self.fightstate_camera_param.y = nil
		end
	else
		if self.fightstate_camera_param then
			self.fightstate_camera_param.x = nil
			self.fightstate_camera_param.y = nil
		end
	end
end

-------------------------------------------------------------------------------------------------
---------------------------------------引导部分--------------------------------------------------
--循环检测是否有等待的引导
-- function FunctionGuide:Update(now_time, elapse_time)
-- 	if not self:CheckCanDoGuide() then
-- 		return
-- 	end

-- 	if not self.is_open_fun_guide or self.is_pausing or not self.cur_guide_cfg then
-- 		return
-- 	end

-- 	if now_time - self.prve_time < self.check_gap_time then
-- 		return
-- 	end

-- 	if self.is_delay_time then
-- 		return
-- 	end

-- 	self.prve_time = now_time
-- 	self:CheckConfigGuide()
-- 	-- if not self.is_guiding then
-- 	-- 	for k, v in pairs(self.wait_guide_list) do
-- 	-- 		self:SetCurrentGuideCfg(v)
-- 	-- 		if self.is_guiding then
-- 	-- 			break
-- 	-- 		end
-- 	-- 	end
-- 	-- end
-- end

-- 检测上一个缓存的引导并设置继续这个引导
function FunctionGuide:CheckLastGuide()
	if not IS_ON_CROSSSERVER then
		local cache_guide_id = PlayerPrefsUtil.GetInt(FunctionGuide.GetGuideLocalKey())
		PlayerPrefsUtil.DeleteKey(FunctionGuide.GetGuideLocalKey())
		if cache_guide_id and cache_guide_id > 0 and RoleWGData.Instance.role_vo.level > 1 then
			local guide_cfg = self.guide_list_cfg[tonumber(cache_guide_id)]
			if guide_cfg then
				self:SetCurrentGuideCfg(guide_cfg)
			end
		end
	end
end

-- 获取到当前保存的引导的id的Key
function FunctionGuide.GetGuideLocalKey()
	local role_uuid = RoleWGData.Instance:GetUUid()
	local role_id = role_uuid.temp_low .. role_uuid.temp_high
	local key = "cache_guide_id" .. role_id
	return key
end

-- 获取常规的引导配置
function FunctionGuide:GetGuideCfgByTrigger(trigger_type, trigger_param)
	for k,v in pairs(self.guide_list_cfg) do
		if v.trigger_type == trigger_type then
			if v.trigger_type == GuideTriggerType.AcceptTask
			or v.trigger_type == GuideTriggerType.CommitTask
			or v.trigger_type == GuideTriggerType.AcceptTask_NoAuto
			or v.trigger_type == GuideTriggerType.CanCommintTask then
				local task_id_list = Split(v.trigger_param, "|") or {}
				for _, id in pairs(task_id_list) do
					if trigger_param == tonumber(id) then
						return v
					end
				end
			elseif v.trigger_param == trigger_param then
				return v
			end
		end
	end
	return nil
end

-- 设置当前的引导
function FunctionGuide:SetCurrentGuideCfg(guide_cfg)
	-- print_error("设置当前引导", guide_cfg.id, self:GetIsCanDoGuide(guide_cfg))
	if not self:GetIsCanDoGuide(guide_cfg) then
		return
	end

	if self.cur_guide_cfg ~= guide_cfg or (guide_cfg and self.wait_guide_list[guide_cfg.guide_name]) then
		if guide_cfg ~= nil then
			if guide_cfg.guide_main_module == GuideModuleName.MainUIView then
				-- 主界面的引导触发都需要先等0.6秒，让主界面的动画播完
				self:RemoveDelayExcuteTimer()
				self.delay_excute_timer = GlobalTimerQuest:AddDelayTimer(function ( ... )
					self:EndGuide()
					self:StartGuide(guide_cfg)
				end, 0.6)
			else
				self:EndGuide()
				self:StartGuide(guide_cfg)
			end
		else
			self:EndGuide()
		end
	end
end

-- 结束引导
function FunctionGuide:EndGuide(is_pausing)
	-- print_error("结束引导-------------->", self.cur_guide_cfg and self.cur_guide_cfg.guide_name)
	TaskGuide.Instance:SideTOStopTask(false)
	self.is_close_all = false
	local is_can_auto_task = false

	if self.normal_guide_view then
		self.normal_guide_view:ClearOldAudioId()
	end

	if not is_pausing then
		PlayerPrefsUtil.DeleteKey(FunctionGuide.GetGuideLocalKey())
	end
	-- if self.cur_guide_cfg then
	-- 	self.wait_guide_list[self.cur_guide_cfg.guide_name] = nil
	-- end

	is_can_auto_task = TaskGuide.Instance:NoviceCheckTask()
	if self.is_stop_task or is_can_auto_task then
		self.is_stop_task = false
		TaskGuide.Instance:CanAutoAllTask(true)
	end


	self.is_guiding = false
	local guide_id = 0
	if self.cur_guide_cfg then
		guide_id = self.cur_guide_cfg.id
		if self.cur_guide_cfg.guide_name == "qinggong" then
			Scene.Instance:ChangeToDefCameraAngel()
			self.guide_disable_click_view:Close()
		end
	end

	self.cur_guide_cfg = nil
	self.cur_guide_step = 0
	if self.normal_guide_view and self.normal_guide_view:IsOpen() then
		self.normal_guide_view:Close()
	end

	self.clickCallback = nil
	UnityEngine.Time.timeScale = 1
	FunctionGuide.GUIDE_JUMP_TARGET = nil
	if self.guide_end_callback then
		self.guide_end_callback()
		self.guide_end_callback = nil
	end

	local guide_cfg = self:GetGuideCfgByTrigger(GuideTriggerType.GuideEnd, guide_id)
	if guide_cfg then
		self:SetCurrentGuideCfg(guide_cfg)
	end
	NEXT_CAN_AUTO_GUIDE_TIME = Status.NowTime + 30

	self:ClearnShieldRule()
	if not is_can_auto_task and not TaskGuide.Instance:CheckIsCanAutoTask() and self.stop_guaji_cache ~= nil then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		if self.stop_guaji_cache.type == GuajiType.Monster then
			GuajiCache.monster_id = self.stop_guaji_cache.monster_id
		end

		GuajiWGCtrl.Instance:SetGuajiType(self.stop_guaji_cache.type)
	end

	self.stop_guaji_cache = nil
end

-- 不能执行的引导需要缓存到等待列表
function FunctionGuide:CanNotDoGuide(guide_cfg)
	if guide_cfg == nil then
		return true
	end

	-- 解决升级后引导 在特殊条件、特殊场景 不需要引导 而缓存起来
	if not self:CheckCanDoGuide() and guide_cfg.trigger_type == GuideTriggerType.LevelUp then
		self:AddWaitGuideList(guide_cfg)
		PlayerPrefsUtil.DeleteKey(FunctionGuide.GetGuideLocalKey())
		return true
	end

	return false
end

-- 开始引导
local MaxDistance = 11
function FunctionGuide:StartGuide(guide_cfg)
	if guide_cfg == nil or self.is_pausing or self:CanNotDoGuide(guide_cfg) then
		self.guide_view = ""
		return
	end

	self.cur_guide_cfg = guide_cfg
	self.cur_guide_step = 0
	self.last_guide_id = self.cur_guide_cfg.id
	self.is_guiding = true

	-- print_error("开始引导", guide_cfg.guide_name, guide_cfg.finish_step, guide_cfg.step_list_id)
	self:InitIsGuideList()
	self:StartNextStep()
	TaskGuide.Instance:SideTOStopTask(true)
	PlayerPrefsUtil.DeleteKey(FunctionGuide.GetGuideLocalKey())
	GlobalEventSystem:Fire(MainUIEventType.CHNAGE_FIGHT_STATE_BTN, false)

	-- 轻功特殊操作
	if guide_cfg.guide_name == "qinggong" then
		-- 强制显示御剑按钮
		GlobalEventSystem:Fire(OtherEventType.ENABLE_QING_GONG_CHANGE, true)
		-- -- 轻功不可点击
		-- self.guide_disable_click_view:Open()
		-- --轻功引导固定摄像机视角
		-- local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
		-- local target_x, target_y = GameMapHelper.LogicToWorld(other_cfg.qg_guide_end_x, other_cfg.qg_guide_end_y)
		-- FunctionGuide.GUIDE_JUMP_TARGET = Vector3(target_x, 0, target_y)

		-- -- 轻功引导屏蔽其他玩家
        -- if not self.qinggong_shield_rule then
        --     self.qinggong_shield_rule = self:RegisterRule(SettingRule, ShieldObjType.Role, ShieldRuleWeight.Max)
        -- end
        -- -- 轻功引导屏蔽其他玩家影子
        -- if not self.qinggong_shadow_shield_rule then
        --     self.qinggong_shadow_shield_rule = self:RegisterRule(SettingRule, ShieldObjType.Shadow, ShieldRuleWeight.Max)
        -- end
        -- -- 轻功引导屏蔽其他玩家名字
        -- if not self.qinggong_followui_rule then
        --     self.qinggong_followui_rule = self:RegisterRule(SettingRule, ShieldObjType.Shadow, ShieldRuleWeight.Max)
        -- end
	end
end

--初始化是否进行过引导的表
function FunctionGuide:InitIsGuideList()
	self.is_guide_list = {}
	local guide_cfg = self.cur_guide_cfg or {}
	local step_list = guide_cfg.step_list or {}
	for i = 1, #step_list do
		self.is_guide_list[i] = false
	end
end

-- 开始下一步
function FunctionGuide:StartNextStep()
	-- print_error("开始下一步", self.cur_guide_step)
	if not self.cur_guide_cfg then
		return
	end

	self.cur_guide_step = self.cur_guide_step + 1
	self:SetGuideCacheId()

	if self.cur_guide_step ~= 1 then
		self:EndCurStep()
	end

	if self.cur_guide_cfg and self.cur_guide_cfg.finish_step ~= "" and self.cur_guide_step > self.cur_guide_cfg.finish_step then
		FunctionGuide.SetIsAlreadyGuide(self.cur_guide_cfg.id)
	end

	local step_cfg = self:GetGuideStepCfg(self.cur_guide_cfg, self.cur_guide_step)
	self.cur_step_cfg = step_cfg
	if step_cfg == nil then
		self:EndGuide()
		return
	end

	-- 无用的步骤直接下一步
	if step_cfg.unuseful == 1 then
		self:StartNextStep()
	else
		if step_cfg.delay_time ~= nil and step_cfg.delay_time ~= 0 then
			self.is_delay_time = true
			self:RemoveDelayExcuteTimer()
			self.delay_excute_timer = GlobalTimerQuest:AddDelayTimer(function ( ... )
				self.is_delay_time = false
				self:CheckConfigGuide(step_cfg)
			end, step_cfg.delay_time)
		else
			self.is_delay_time = false
			self:CheckConfigGuide(step_cfg)
		end
	end
end

-- 获取下一步信息
function FunctionGuide:GetNextStepGuide()
	return self:GetGuideStepCfg(self.cur_guide_cfg, self.cur_guide_step + 1)
end

-- 暂停当前步骤引导
function FunctionGuide:EndCurStep()
	self.normal_guide_view:Close()
	if self.cur_guide_cfg then
		GlobalEventSystem:Fire(OtherEventType.GUIDE_STEP_CHANGE, self.cur_guide_cfg, self.cur_guide_step)
	end
end

-- 设置当前步骤缓存
function FunctionGuide:SetGuideCacheId(cur_guide_step)
	cur_guide_step = cur_guide_step or self.cur_guide_step
	if self.cur_guide_cfg and self.cur_guide_cfg.finish_step ~= "" and cur_guide_step < self.cur_guide_cfg.finish_step then
		PlayerPrefsUtil.SetInt(FunctionGuide.GetGuideLocalKey(), self.cur_guide_cfg.id)
	else
		PlayerPrefsUtil.DeleteKey(FunctionGuide.GetGuideLocalKey())
	end
end

function FunctionGuide.IsGuideStepType(step_type)
	if step_type == GuideStepType.GirlGuide or FunctionGuide.IsSpriteGuideType(step_type) or step_type == GuideStepType.TextGuide then
		return true
	end

	return false
end

function FunctionGuide.IsSpriteGuideType(step_type)
	if step_type == GuideStepType.SpriteGuide
	or step_type == GuideStepType.SpriteAnger
	or step_type == GuideStepType.SpriteClap
	or step_type == GuideStepType.SpriteExpect then
		return true
	end
	return false
end

-- 获取引导
function FunctionGuide:GetGuideUi(module_name, ui_name, ui_param)
	local list = self.get_guide_ui_callback_list[module_name]
	local ui = nil
	local clickCallback
	local arrow = nil
	local next_step
	if list ~= nil then
		for i=#list, 1, -1 do
			ui, clickCallback, arrow, next_step = list[i](ui_name, ui_param, self.try_times, self.cur_guide_cfg)
			if ui ~= nil then
				break
			end
		end
	end
	return ui, clickCallback, arrow, next_step
end

-- 是否已经引导过了
function FunctionGuide.GetIsAlreadyGuide(guide_id)
	local temp_id, key = FunctionGuide.GetAlreadyGuideKey(guide_id)
	local guide_flag_list = SettingWGData.Instance:GetSettingDataListByKey(key)
	local flag = guide_flag_list.item_id or 0
	local guide_id_list = bit:d2b(flag)
	if guide_id_list[32 - temp_id] == 1 then				--等于1则表示已经引导过了
		return true
	end
	return false
end


-- 是否可引导
--@param guide_cfg 引导配置 or 引导id
-- 如果引导配置有guided_not_trigger_id，则判断guided_not_trigger_id是否已经引导过，如果引导过则不可引导
function FunctionGuide:GetIsCanDoGuide(guide_cfg)
	if guide_cfg == nil then
		return false
	end

	if type(guide_cfg) == "number" then
		guide_cfg = self.guide_list_cfg[guide_cfg]
	end

	if guide_cfg == nil then
		return false
	end

	if type(guide_cfg.guided_not_trigger_id) == "number" then
		local pre_guide_cfg = self.guide_list_cfg[guide_cfg.guided_not_trigger_id]
		if pre_guide_cfg then
			return not self.GetIsAlreadyGuide(pre_guide_cfg.id)
		end
	end

	return not self.GetIsAlreadyGuide(guide_cfg.id)
end

-- 检查当前触发的引导步骤，并执行
local guide_text_time = 0
function FunctionGuide:CheckConfigGuide(step_cfg)
	local step_cfg = step_cfg or self.cur_step_cfg
	if (not self.is_open_fun_guide) or (not step_cfg) then
		return
	end

	-- print_error("执行引导", self.cur_guide_step, step_cfg.step_type, step_cfg.module_name)
	local function stop_guaji()
		if self.cur_guide_step == 1 and step_cfg.step_type ~= GuideStepType.FindUi then
			--如果第一步不是找ui的话就停止任务关闭所有界面
			local is_auto_stop_task = Scene.Instance:GetSceneLogic():IsAutoStopTaskOnGuide()
			if is_auto_stop_task then
				if GuajiCache.guaji_type ~= GuajiType.None then
					self.stop_guaji_cache = {}
					self.stop_guaji_cache.type = GuajiCache.guaji_type
					self.stop_guaji_cache.monster_id = GuajiCache.monster_id
				end

				GuajiWGCtrl.Instance:StopGuaji(false)
				-----------------------------------------------------------------
				TaskGuide.Instance:CanAutoAllTask(false)			--停止接受任务
				-----------------------------------------------------------------
				self.is_stop_task = true
			end

			--只关闭一次界面
			if not self.is_close_all and 
				(self.cur_guide_cfg.trigger_type ~= GuideTriggerType.FuBenPanelResult 
				and self.cur_guide_cfg.trigger_type ~= GuideTriggerType.OpenViewOnTimes
				and self.cur_guide_cfg.trigger_type ~= GuideTriggerType.LandWarFbPersonStageOne 
				and self.cur_guide_cfg.trigger_type ~= GuideTriggerType.LandWarFbPersonStageTwo
				and self.cur_guide_cfg.trigger_type ~= GuideTriggerType.LandWarFbPersonStageThree
				and self.cur_guide_cfg.trigger_type ~= GuideTriggerType.LandWarFbPersonStageFour
				and self.cur_guide_cfg.trigger_type ~= GuideTriggerType.MergeFireworksFirstEnter
				and self.cur_guide_cfg.trigger_type ~= GuideTriggerType.ArtifactViewActive) then
				self.is_close_all = true
				ViewManager.Instance:CloseAll()
				GlobalEventSystem:Fire(MainUIEventType.CHNAGE_FIGHT_STATE_BTN, false)
				GlobalEventSystem:Fire(MainUIEventType.PORTRAIT_TOGGLE_CHANGE, false, false, true)
			end
		end
	end

	if step_cfg.step_type == GuideStepType.FindUi then
		--查找ui是否存在（找到后才开始真正的引导）
		local ui = self:GetGuideUi(step_cfg.module_name, step_cfg.ui_name, step_cfg.ui_param)
		if nil ~= ui then				--找到直接开始下一步
			self.is_guide_list[self.cur_guide_step] = true
			self:StartNextStep()
		end
	elseif step_cfg.step_type == GuideStepType.AutoOpenView then
		stop_guaji()
		--自动打开面板
		self:AutoOpenView(step_cfg.module_name, step_cfg.ui_name, step_cfg.ui_param)
		self.is_guide_list[self.cur_guide_step] = true
	elseif step_cfg.step_type == GuideStepType.AutoCloseView then
		--自动关闭面板
		self:AutoCloseView(step_cfg.module_name, step_cfg.ui_name, step_cfg.ui_param)
		self.is_guide_list[self.cur_guide_step] = true
	elseif FunctionGuide.IsGuideStepType(step_cfg.step_type) and step_cfg.ui_name == "" then --精灵介绍
		if guide_text_time <= Status.NowTime then
			stop_guaji()

			guide_text_time = Status.NowTime + 0.1
			self.girl_guide_view:SetStepCfg(step_cfg)
			self.normal_guide_view:SetStepCfg(step_cfg)
			self.normal_guide_view:SetGuideCfg(self.cur_guide_cfg)
			self.normal_guide_view:SetIsFrist(true)
			if not self.normal_guide_view:IsOpen() then
				self.normal_guide_view:Open()
			end
			self.normal_guide_view:Flush()
		end
		self.is_guide_list[self.cur_guide_step] = true
	elseif step_cfg.step_type == GuideStepType.Arrow or FunctionGuide.IsGuideStepType(step_cfg.step_type) then --指引方式为找到ui
		local ui, callback, arrow, next_step = self:GetGuideUi(step_cfg.module_name, step_cfg.ui_name, step_cfg.ui_param)
		-- print_error("执行箭头引导", step_cfg.module_name, step_cfg.ui_name)

		if ui == NextGuideStepFlag then		--直接跳至下一引导的标记
			self:StartNextStep()
		elseif ui == LastGuideStepFlag then		--直接跳至最后一步
			local step_list = self:GetGuideStepListCfg(self.cur_guide_cfg)
			local last_step = step_list and #step_list or 0

			if last_step ~= 0 then
				for i = self.cur_guide_step, last_step - 1 do
					self.is_guide_list[i] = true
				end

				self.cur_guide_step = last_step - 1
			end
		
			self:StartNextStep()
		elseif ui == EndGuideFlag then		--结束引导
			self:EndGuide()
		elseif ui == GuidePause then
			if self.normal_guide_view and self.normal_guide_view:IsOpen() then
				self.normal_guide_view:Close()
			end
			return
		else
			if ui and ui ~= GuideWait then
				self.is_guide_list[self.cur_guide_step] = true
				if self.not_find_ui_countdown then
					CountDown.Instance:RemoveCountDown(self.not_find_ui_countdown)
					self.not_find_ui_countdown = nil
				end

				if step_cfg ~= nil and step_cfg.is_modal ~= 0 then
					stop_guaji()
				end

				-- print_error("执行箭头引导", step_cfg.module_name, step_cfg.ui_name)
				self:UpdateGuideStep(ui, callback, step_cfg, arrow)
			else
				-- print_error("执行箭头引导 ui是空的")
				if self.normal_guide_view and self.normal_guide_view:IsOpen() then
					self.normal_guide_view:Close()
				end

				--当前引导为弱指引的话直接停止整个引导
				if self.is_guide_list[self.cur_guide_step] and step_cfg.is_modal == 0 then
					self:EndGuide()
					return
				end

				if not self.not_find_ui_countdown then
					local function timer_func(elapse_time, total_time)
						-- print_error("查找ui时间（找不到则停止引导）", elapse_time, total_time)
						if self.cur_guide_cfg
						and (self.cur_guide_cfg.trigger_type == GuideTriggerType.FuBenPanelResult or self.cur_guide_cfg.trigger_type == GuideTriggerType.OpenViewOnTimes)
						and self.cur_guide_step == 1 then
							CountDown.Instance:RemoveCountDown(self.not_find_ui_countdown)
							self.not_find_ui_countdown = nil
							self:StartNextStep()
						end

						if elapse_time >= total_time then
							CountDown.Instance:RemoveCountDown(self.not_find_ui_countdown)
							self.not_find_ui_countdown = nil
							-- print("停止引导++++++++++++++++++")
							self:EndGuide()
							return
						else
							-- 重新查找
							self:CheckConfigGuide()
						end
					end
					--只找5秒，找不到直接停止引导
					self.not_find_ui_countdown = CountDown.Instance:AddCountDown(self.not_find_ui_time, 0.03, timer_func)
				end
			end
		end
	elseif step_cfg.step_type == GuideStepType.CloseModuleView then
		if ViewManager.Instance:IsOpen(step_cfg.module_name) then
			stop_guaji()
		end

		self:CloseAllModuleView(step_cfg.module_name)
		self:StartNextStep()
	elseif step_cfg.step_type == GuideStepType.OpenView then
		stop_guaji()
		ViewManager.Instance:Open(step_cfg.module_name)
		ViewManager.Instance:FlushView(step_cfg.module_name, nil, nil, {ui_param = step_cfg.ui_param})
		self:StartNextStep()
	elseif step_cfg.step_type == GuideStepType.WaitCloseModuleView then
		if not ViewManager.Instance:IsOpen(step_cfg.module_name) then
			self:StartNextStep()
		else
			stop_guaji()
		end
	elseif step_cfg.step_type == GuideStepType.CloseAllView then --关闭所有面板
		if ViewManager.Instance:HasOpenView() then
			stop_guaji()
		end
		ViewManager.Instance:CloseAll()
		self:StartNextStep()
	elseif step_cfg.step_type == GuideStepType.ChangeCamera then  -- 调整摄像机
		stop_guaji()
		local splits = Split(step_cfg.step_param, "|")
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None, true)
		Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.TASK, tonumber(splits[1]), tonumber(splits[2]), MaxDistance)
		self:StartNextStep()
	elseif step_cfg.step_type == GuideStepType.MoveTo then  -- 移动 
		local splits = Split(step_cfg.step_param, "|")
		local main_role = Scene.Instance:GetMainRole()
		local pos_x = tonumber(splits[1]) or 0
		local pos_y = tonumber(splits[2]) or 0

		if main_role then
			GlobalTimerQuest:AddDelayTimer(function()
				main_role:DoMove(pos_x, pos_y, CLIENT_MOVE_REQ_PARAM.NORMAL, false)
			end, 0.5)
		end
		self:StartNextStep()
	elseif step_cfg.step_type == GuideStepType.SprintMoveTo then  -- 冲刺 
		local splits = Split(step_cfg.step_param, "|")
		local main_role = Scene.Instance:GetMainRole()
		local pos_x = tonumber(splits[1]) or 0
		local pos_y = tonumber(splits[2]) or 0
		local height = tonumber(splits[3]) or 234

		if main_role then
			main_role:DoSwordSprint(true, pos_x, pos_y, height)
		end
		self:StartNextStep()
	elseif step_cfg.step_type == GuideStepType.Husong then
		YunbiaoWGCtrl.Instance:SendRefreshHusongTask()
		self:StartNextStep()
	elseif step_cfg.step_type == GuideStepType.CloseMenu then
		MainuiWGCtrl.Instance:SetMenuIconIsOn(false)
		self:StartNextStep()
	elseif step_cfg.step_type == GuideStepType.OpenNovicePopDialog then
		local boss_born_notice = NovicePopDialogWGData.Instance:GetConfigById(step_cfg.step_param)
		-- print_error("触发剧情弹窗", step_cfg.step_param, boss_born_notice)
		ViewManager.Instance:Open(GuideModuleName.NovicePopDialogView, nil, "all", {data = boss_born_notice})  -- 功能引导表
	end
end

function FunctionGuide:UpdateGuideStep(ui, callback, step_cfg, arrow)
	if not self.normal_guide_view:IsOpen() then
		self.normal_guide_view:SetBtnObj(ui)
		self.normal_guide_view:SetClickCallBack(callback, step_cfg.is_trigger_action)
		self.normal_guide_view:SetStepCfg(step_cfg)
		self.normal_guide_view:SetGuideCfg(self.cur_guide_cfg)
		self.normal_guide_view:SetIsFrist(true)
		self.normal_guide_view:SetArrow(arrow)
		self.normal_guide_view:Open()
	else
		self.normal_guide_view:SetIsFrist(false)
		self.normal_guide_view:SetBtnObj(ui)
		self.normal_guide_view:SetClickCallBack(callback, step_cfg.is_trigger_action)
		self.normal_guide_view:Flush()
	end
end
-------------------------------------------------------------------------------------------------
-------------------------------------------------------------------------------------------------
-- 根据任务id获取一个引导
function FunctionGuide:GetGuideCfgByTaskID(task_id)
	for k,v in pairs(self.guide_list_cfg) do
		if (v.trigger_type == GuideTriggerType.AcceptTask
		or v.trigger_type == GuideTriggerType.CommitTask
		or v.trigger_type == GuideTriggerType.AcceptTask_NoAuto)
		and task_id == v.trigger_param then
			return v
		end
	end

	return nil
end

-- 打开物品使用界面
function FunctionGuide:OpenKeyUseView(item_id,index)
	self.key_use_view:AddItem(item_id, index, function ()
		BagWGCtrl.Instance:SendUseItem(index, 1)
	end)
end

function FunctionGuide:OpenKeyUseViewHasCallback(item_id, index, callback)
	self.key_use_view:AddItem(item_id, index, callback)
end

function FunctionGuide:OpenHolyEquipKeyUseView(data)
	self.key_use_view:AddHolyEquipItem(data)
end

function FunctionGuide:OpenEsotericaKeyUseView(data)
	self.key_use_view:AddEsotericaItem(data)
end

function FunctionGuide:OpenWuHunKeyUseView(data)
	self.key_use_view:AddWuHunItem(data)
end

function FunctionGuide:OpenMengLingeyUseView(data)
	self.key_use_view:AddMengLingItem(data)
end

function FunctionGuide:OpenThunderEquipKeyUseView(data)
	self.key_use_view:AddThunderEquipItem(data)
end

function FunctionGuide:OpenFadeView()
	self.fade_view:Open()
end

function FunctionGuide:UpdateShowKeyUseView()
	if self.key_use_view and self.key_use_view:IsOpen() then
		self.key_use_view:UpdateShow()
	end
end

-- 是否等待引导中
function FunctionGuide:GetIsWaitGuide()
	return self.is_wait_guiding
end

--获取是否在引导中
function FunctionGuide:GetIsGuide()
	return self.is_guiding
end

function FunctionGuide:AddWaitGuideList(guide_cfg)
	if IsEmptyTable(guide_cfg) then
		return
	end
	self.wait_guide_list[guide_cfg.guide_name] = guide_cfg
end

function FunctionGuide:DelWaitGuideListByName(guide_name)
	self.wait_guide_list[guide_name] = nil
end

function FunctionGuide:GetLastGuideid()
	return self.last_guide_id or 0
end

function FunctionGuide:RegisterRule(rule_class, shield_obj_type, rule_weight)
    local rule = rule_class.New(shield_obj_type, rule_weight)
    rule:Register()
    return rule
end

function FunctionGuide:GetGuideViewIsOpen()
	for k,v in pairs(self:StopGuideViewTable()) do
		if ViewManager.Instance:IsOpen(k) then
			return true
		end
	end
	return self.normal_guide_view:IsOpen()
end

function FunctionGuide:OnXiuXianShiLianInfoChange()
	if not self.is_open_fun_guide then return end
	local chapter_id = XiuXianShiLianWGData.Instance:GetSingleCanActiveSkillChapterId()
	if chapter_id > 0 then
		local guide_cfg = self:GetGuideCfgByTrigger(GuideTriggerType.XiuXianShiLianSkillCanActive, chapter_id)
		if self:GetIsCanDoGuide(guide_cfg) then
			self:SetCurrentGuideCfg(guide_cfg)
			return
		end
	end
end

-- 触发修仙试炼战力条件任务的引导
function FunctionGuide:TriggerXiuXianShiLianCapGoGuide()
	local guide_cfg = self:GetGuideCfgByTrigger(GuideTriggerType.XiuXianClickCapConditionGoBtn, "")
	if self:GetIsCanDoGuide(guide_cfg) then
		self:SetCurrentGuideCfg(guide_cfg)
		return true
	end
	return false
end

--阵地战个人副本引导触发
function FunctionGuide:TriggerLandWarFbPersonGoGuide(stage, spe_trigger_type)
	if not self.is_open_fun_guide then 
		return 
	end

	local trigger_type = spe_trigger_type
	if not trigger_type or trigger_type < 0 then
		if stage == LandWarFbPersonWGData.STAGE.ONE then
			trigger_type = GuideTriggerType.LandWarFbPersonStageOne
		elseif stage == LandWarFbPersonWGData.STAGE.TWO then
			trigger_type = GuideTriggerType.LandWarFbPersonStageTwo
		elseif stage == LandWarFbPersonWGData.STAGE.THREE then
			trigger_type = GuideTriggerType.LandWarFbPersonStageThree
		end
	end

	if trigger_type < 0 then
		return
	end

	local guide_cfg = self:GetGuideCfgByTrigger(trigger_type, "")
	if self:GetIsCanDoGuide(guide_cfg) then
		self:SetCurrentGuideCfg(guide_cfg)
		return true
	end

	return false
end

-- 触发合服活动-星辰陨落首次进入的引导
function FunctionGuide:TriggerMergeFireworksGoGuide()
	if not self.is_open_fun_guide then 
		return 
	end
	local guide_cfg = self:GetGuideCfgByTrigger(GuideTriggerType.MergeFireworksFirstEnter, "")
	if self:GetIsCanDoGuide(guide_cfg) then
		self:SetCurrentGuideCfg(guide_cfg)
		return true
	end
	return false
end

function FunctionGuide:ReSetXiaoGuiFlag()
	self.xiaogui_info = nil
end

function FunctionGuide:TriggerGuideByGuideName(guide_name)
	for k,v in pairs(self.guide_list_cfg) do
		if v.guide_name == guide_name then
			self:SetCurrentGuideCfg(v)
			break
		end
	end
end

function FunctionGuide:TriggerGuideById(guide_id, end_callback, need_trigger_param)
	guide_id = tonumber(guide_id)
	local cfg = (self.guide_list_cfg or {})[guide_id]
	if not cfg then
		return
	end

	if need_trigger_param == nil then
		self.guide_end_callback = end_callback
		self:SetCurrentGuideCfg(cfg)
	elseif need_trigger_param ~= nil and need_trigger_param ~= "" and cfg.trigger_param == need_trigger_param then
		self.guide_end_callback = end_callback
		self:SetCurrentGuideCfg(cfg)
	end
end

function FunctionGuide:CloseAllModuleView(module_name)
	local list = self.get_guide_ui_callback_list[module_name]
	local ui = nil
	local clickCallback
	local param_1 = nil
	if list ~= nil then
		for i=#list, 1, -1 do
			ui, clickCallback = list[i](GuideUIName.CloseBtn, "", nil, self.cur_guide_cfg)
			if ui ~= nil then
				clickCallback()
			end
		end
	end
end

--各个模块注册引导取ui的方法
--get_guide_ui_callback。方法提供ui_name，ui_param参数，用于获取实际ui对象
function FunctionGuide:RegisteGetGuideUi(module_name, get_guide_ui_callback)
	if module_name == nil or get_guide_ui_callback == nil then
		return
	end

	self.check_callback_map[get_guide_ui_callback] = get_guide_ui_callback
	if self.get_guide_ui_callback_list[module_name] == nil then
		self.get_guide_ui_callback_list[module_name] = {}
	end

	local list = self.get_guide_ui_callback_list[module_name]
	for _,v in ipairs(list) do
		if v == get_guide_ui_callback then
			return
		end
	end
	table.insert(list, get_guide_ui_callback)
end

function FunctionGuide:UnRegiseGetGuideUiByFun(module_name, get_guide_ui_callback)
	if nil ~= get_guide_ui_callback then
		self.check_callback_map[get_guide_ui_callback] = nil
	end

	local list = self.get_guide_ui_callback_list[module_name]
	if list then
		for k,v in ipairs(list) do
			if v == get_guide_ui_callback then
				table.remove(list, k)
				break
			end
		end
	end
end

function FunctionGuide:IsExistsListen(callback)
	return nil ~= self.check_callback_map[callback]
end

function FunctionGuide:GetCurrentGuideName()
	if self.cur_guide_cfg then
		return self.cur_guide_cfg.guide_name
	end
	return nil
end

function FunctionGuide:CheckCanDoGuide()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Field1v1 then
		return false
	end

	return true
end

function FunctionGuide:AutoMain(ui_name, state)
	local list = self.get_guide_ui_callback_list[GuideModuleName.MainUIView]
	if list then
		for i=#list,1, -1 do
			list[i](ui_name, state, nil, self.cur_guide_cfg)
		end
	end
end

function FunctionGuide:AutoOpenView(module_name, ui_name, ui_param)
	if module_name == nil or module_name == "" then
		return
	end

	if module_name == GuideModuleName.MainUIView then
		self:AutoMain(ui_name, 1)
	else
		local t = Split(ui_param, "#")
		local tab_index = t[1]

		local param_t = {
			open_param = nil,			--打开面板参数
			sub_view_name = nil,		--打开二级面板
			to_ui_name = 0,				--跳转ui
			to_ui_param = 0,			--跳转ui参数
		}
	
		if t[2] ~= nil then
			local key_value_list = Split(t[2], ",")
			for k,v in pairs(key_value_list) do
				local key_value_t = Split(v, "=")
				local key = key_value_t[1]
				local value = key_value_t[2]
	
				if key == "sub" then
					param_t.sub_view_name = value
				elseif key == "op" then
					param_t.open_param = value
				elseif key == "uin" then
					param_t.to_ui_name = value
				elseif key == "uip" then
					param_t.to_ui_param = value
				end
			end
		end
	
		if t[3] ~= nil then
			local key_value_t = Split(t[3], "=")
			local key = key_value_t[1]
			local value = key_value_t[2]
			if key == "sel_index" then
				param_t.sel_index = value
			elseif key == "item_id" then
				param_t.item_id = tonumber(value)
			end
		end
		ViewManager.Instance:Open(module_name, tab_index, "all", param_t)
	end

	self:StartNextStep()
end

function FunctionGuide:AutoCloseView(module_name, ui_name, ui_param)
	if module_name == nil or module_name == "" then
		return
	end
	if module_name == GuideModuleName.MainUIView then
		self:AutoMain(ui_name, 2)
	else
		ViewManager.Instance:Close(module_name)
	end
	self:StartNextStep()
end

function FunctionGuide:RemoveDelayExcuteTimer()
	if self.delay_excute_timer then
        GlobalTimerQuest:CancelQuest(self.delay_excute_timer)
        self.delay_excute_timer = nil
    end
end

function FunctionGuide:ClearViewOpenTimesCache()
	for k,v in pairs(self.guide_list_cfg) do
		if v.trigger_type == GuideTriggerType.OpenViewOnTimes then
			local key = string.format("OpenViewOnTimes_%s_%s_%s", v.guide_main_module, v.with_param, RoleWGData.Instance:GetOriginUid())
			PlayerPrefsUtil.SetInt(key, 0)
		end
	end
end

function FunctionGuide.GetAlreadyGuideKey(guide_id)
	local temp_id = guide_id
	local key = HOT_KEY.GUIDE_KEY_FLAG1
	if temp_id > 32 then
		temp_id = temp_id - 32
		key = HOT_KEY.GUIDE_KEY_FLAG2
	elseif temp_id > 64 then
		temp_id = temp_id - 64
		key = HOT_KEY.GUIDE_KEY_FLAG3
	elseif temp_id > 96 then
		temp_id = temp_id - 96
		key = HOT_KEY.GUIDE_KEY_FLAG4
	end
	return temp_id, key
end

-- 把引到过的id保存到服务端
function FunctionGuide.SetIsAlreadyGuide(guide_id)
	local temp_id, key = FunctionGuide.GetAlreadyGuideKey(guide_id)
	local guide_flag_list = SettingWGData.Instance:GetSettingDataListByKey(key)
	local flag = guide_flag_list.item_id
	if flag then
		local guide_id_list = bit:d2b(flag)												--转换为32位表
		guide_id_list[32-temp_id] = 1 													--标记已引导过了
		flag = bit:b2d(guide_id_list)													--重新转换为number
		guide_flag_list.item_id = flag													--保存到内存
		SettingWGCtrl.Instance:SendChangeHotkeyReq({[1] = {key, flag}})					--发送给服务器保存
	end
end

function FunctionGuide:OpneOfflineGuaJiLook()
	local limint_level = OfflineRestWGData.Instance:GetOfflineRestLimitLevel() or 0
	local role_level = RoleWGData.Instance:GetRoleLevel() or 0
	if limint_level > role_level then
		return
	end
	self.offline_guaji_look:Open()
end

function FunctionGuide:CloseOfflineGuaJiLook()
	if self.offline_guaji_look:IsOpen() then
		self.offline_guaji_look:Close()
	end
end

function FunctionGuide:IsSomeViewOpen()
	return self.offline_guaji_look:IsOpen() or
		self.normal_guide_view:IsOpen()
end

function FunctionGuide:OpenFunMoWang(task_id,reason)
	if self.fun_mowang_view then
		self.fun_mowang_view:SetData(task_id,reason)
		self.fun_mowang_view:Open()
	end
end

function FunctionGuide:SetMoWangGuideFlag(is_sure)
	self.is_sure = is_sure
end

function FunctionGuide:GetMoWangGuideFlag()
	return self.is_sure
end

function FunctionGuide:GetGuideInfo()
	return self.cur_guide_cfg, self.cur_guide_step
end

function FunctionGuide:OnBossNoticeEventOpen(cur_grade)
	if not self.is_open_fun_guide then return end
	for k,v in pairs(self.guide_list_cfg) do
		if v.trigger_type == GuideTriggerType.BossNoticeOpen
			and v.trigger_param == cur_grade
			and self:GetIsCanDoGuide(v)
			then
			ViewManager.Instance:CloseAll()
			GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
			self:SetCurrentGuideCfg(v)
			return
		end
	end
end

function FunctionGuide:SetMarketPopUseTime()
	self.market_pop_use_time = Status.NowTime + 10 
end

-- 修为 jxw要用这个引导得底得效果，但是又不走正常引导功能
function FunctionGuide:ShowNormalAreaGuide(module_name, click_obj)
	if self.normal_area_guild_view then
		self.normal_area_guild_view:SetDataAndOpen(module_name, click_obj)
	end
end