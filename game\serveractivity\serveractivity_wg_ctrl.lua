require("game/serveractivity/act_perfertqingren/act_perfertqingren_wg_ctrl")
require("game/serveractivity/serveractivity_wg_data")
require("game/serveractivity/activity_collectcard_wg_data")
require("game/serveractivity/act_subview/activity_require_list")
require("game/serveractivity/serveractivity_tab_cfg")
-- require("game/serveractivity/privilege_view")
-- require("game/serveractivity/serveractivity_ui_cfg")
-- require("game/serveractivity/act_discount/act_discount_wg_data")
-- require("game/serveractivity/act_discount/act_discount_view")
-- require("game/serveractivity/act_closebeta_wg_data")
-- require("game/serveractivity/act_combine_wg_data")
require("game/serveractivity/act_openserver_view")
require("game/serveractivity/act_openserver2_view")
-- require("game/serveractivity/act_closebeta_view")
require("game/serveractivity/firstrecharge_view")
require("game/serveractivity/firstrecharge_rebate_view")
-- require("game/serveractivity/pearl_invaild_view")
require("game/serveractivity/fireworks/act_fireworks_view")
-- require("game/serveractivity/rechargeagain_view")
-- require("game/serveractivity/dailyrecharge_view")
-- require("game/serveractivity/rush_recharge/rush_recharge_view")
require("game/serveractivity/rush_recharge/rush_recharge_view2")
require("game/serveractivity/rush_recharge/rush_recharge_view3")
-- require("game/serveractivity/singlerecharge_four/singlerecharge_four_view")
--require("game/serveractivity/act_iwg_view")
require("game/serveractivity/serveractivity_banben_view")
-- require("game/serveractivity/act_subview/act_lottery_draw_view")
-- require("game/serveractivity/next_tehui_view")
-- require("game/serveractivity/act_subview/act_tehuimiaosha_view")
require("game/serveractivity/act_subview/act_gobal_xunbao_reward_tip")
require("game/serveractivity/boss_fight/boss_fight_view")
require("game/serveractivity/boss_fight/boss_fight_reward_view")
require("game/serveractivity/boss_fight/act_boss_fight_scene_view")
-- require("game/serveractivity/dayday_fanli_view")
-- require("game/serveractivity/dayday_fanli_wg_data")
require("game/serveractivity/serveractivity_view")
require("game/serveractivity/act_dalian_view")
require("game/serveractivity/act_subview/act_xunyulu_view")
require("game/serveractivity/everydayrecharge_view")
require("game/serveractivity/everyday_lianxu_recharge_view")
require("game/serveractivity/everyday_lianxu_rechage_wg_data")
require("game/serveractivity/everyday_xiaofei_recharge_view")
require("game/serveractivity/everyday_yinji_turntable_wg_data")
require("game/serveractivity/everyday_yinji_turntable_view")
require("game/serveractivity/everyday_rapidly_recharge_view")
require("game/serveractivity/everyday_rapidly_recharge_record")

require("game/serveractivity/everyday_gift_recharge_view")
require("game/serveractivity/yinji_turntable_record")

require("game/serveractivity/lingyaomaihuo/lingyaomaihuo_view")
require("game/serveractivity/act_subview/act_profess_love_view")
require("game/serveractivity/act_subview/act_city_love_view")
require("game/serveractivity/act_subview/act_slider_num_view")
require("game/serveractivity/act_subview/act_open_server_bipin_tip_view")
require("game/serveractivity/act_subview/act_fly_wing_to_wing")
require("game/serveractivity/act_subview/act_reward_list_show_view")

require("game/serveractivity/kf_activity_reward_view")
-- require("game/serveractivity/firstrecharge_rebate_wg_data")
require("game/serveractivity/everyday_recharge_rebate/everyday_recharge_rebate_wg_data")
require("game/serveractivity/everyday_recharge_rebate/everyday_recharge_rebate_view")
require("game/serveractivity/act_subview/act_guild_contend_view")

require("game/serveractivity/act_oga_extend_wg_data")
require("game/serveractivity/act_subview/act_oga_extend_lottery_view")
require("game/serveractivity/act_subview/act_oga_extend_daily_recharge_view")
require("game/serveractivity/act_subview/act_oga_extend_cloud_buy_view")

require("game/serveractivity/act_subview/act_oga_extend_purchase_view")
require("game/serveractivity/act_subview/act_open_server_bipin_reward_tips")
require("game/serveractivity/act_subview/act_collect_card_task_view")
require("game/serveractivity/act_subview/act_collect_card_operate_view")


ServerActivityWGCtrl = ServerActivityWGCtrl or BaseClass(BaseWGCtrl)

function ServerActivityWGCtrl:__init()
	if ServerActivityWGCtrl.Instance ~= nil then
		print("[ServerActivityWGCtrl]error:create a singleton twice")
	end
	ServerActivityWGCtrl.Instance = self

    
    self:InitQingyuanActivity()
	self.act_lvlimit_icon_list = {}

	self.lingyaomaihuo_view = LingYaoMaiHuoView.New(GuideModuleName.LingYaoMaiHuo) -- F2灵妖卖货
	self.openbosshunter_rank_view = OpenBossHunterRankView.New(GuideModuleName.BossHunterRank)	-- 开服BOSS首杀积分榜

	self.server_banben_activity_view = ServerBanBenActivityView.New(GuideModuleName.BanBenActivity)

	self.server_activity_data = ServerActivityWGData.New()
	self.activity_collect_card_data = ActivityCollectCardWGData.New()
	-- self.act_discount_data = ActDiscountData.New()
	-- self.act_combine_data = ActCombineData.New()
	-- self.firstrecharge_rebate_data = FirstRechargeRebateData.New()
	self.everyday_recharge_rebate_data = EveryDayRechargeRebateWGData.New()
	self.everyday_yinji_turntable_data = EveryDayYinJiTurnTableWGData.New()
	self.yinji_turntable_record = YinJiTurnTableRecordTip.New()

	self.act_fireworks = ActYanHuaQingDianView.New(GuideModuleName.Fireworks) -- 烟花庆典

	self.serveract_tab_view = ServerActivityTabView.New(GuideModuleName.ServerActivityTabView)
	self.xianmeng_zhegnba_view = XianMengZhengBaView.New(GuideModuleName.XianMengZhengBaView)
	self.kf_activity_view = KfActivityView.New(GuideModuleName.KfActivityView)
	self.kf_activity_reward_view = KfRewardActivityView.New(GuideModuleName.KfRewardActivityView)
	self.act_openserver_view = ActOpenServerView.New(GuideModuleName.ActOpenServerView)
	self.kf_bipin_tip_view = OpenServerBiPinTipView.New()

	-- self.act_openserver2_view = ActOpenServertwoView.New(GuideModuleName.ActOpenServertwoView)
	-- self.act_closebeta_view = ActCloseBetaView.New()
	self.firstrecharge_view = FirstRechargeView.New(GuideModuleName.FirstRechargeView)
	self.everydayrecharge_view = EveryDayRechargeView.New(GuideModuleName.EveryDayRechargeView)
	self.everydayrecharge_record = EveryDayRechargeViewRecordTip.New()
	self.everyday_lianxu_rechage_data = EveryDayLianXuRechargeWGData.New()
	-- self.rechargeagain_view = RechargeAgainView.New()
	-- self.dailyrecharge_view = DailyRechargeView.New()
	-- self.actiwantgold_view = ActIWantGoldView.New()

	-- self.rush_recharge_view = RushRechargeView.New()           -- 冲战新星
	self.rush_recharge_view2 = RushRechargeViewTwo.New()	   -- 我要冲战
	self.rush_recharge_view3 = RushRechargeViewThree.New()	   -- 急速冲战
	-- self.rush_recharge_view4 = SingleRechargeFourView.New()	   -- 战榜提名

	-- self.recharge_rank_view = OpenServerCompetitionRankView.New()    -- 开服活动 充值排行
	self.marry_schedule_view =  MarryScheduleView.New()              -- 开服活动 完美情人进度

	-- self.act_rank_get_list_view =  ActRankGetListView.New()           -- 消费排行领取名单

	-- self.privilege_view = PrivilegeView.New()
	self.confirm_dialog = Alert.New()
	ActivityWGData.Instance:NotifyActChangeCallback(BindTool.Bind(self.ActivityChangeCallBack,self))
	self.gobal_xunbao_tip = ActGobalXunBaoReWardTip.New()

	self.celebration_reward_tip = ActGobalXunbaoRewardTipNew.New()

	-- self.act_discount_view = ActDisCountView.New(GuideModuleName.ActDiscount)

	self.boss_fight_reward_view = ActBossFightRewardView.New(GuideModuleName.BossFightRewardView)
	self.boss_fight_scene_view = ActBossFightSceneView.New(GuideModuleName.BossFightSceneView)

	-- self.dayday_fanli_view = DayDayFanLiView.New(GuideModuleName.DayDayFanLiView) --天天返利
	-- self.dayday_fanli_data = DayDayFanLiData.New() --天天返利

	self.act_slider_num_view = ActSliderNumView.New()
	self.act_reward_list_show_view = ACTRewardListShowView.New()


	self.xj_reward_tip = SerActQUJXTipView.New()
	self.xunyulu_view = ActXunYuLuView.New()
	self.serveract_dl_view = ServerActivityDaLianView.New()

	self.server_high_point_reward_view = ServerHighPointRewardView.New()
	self.open_srver_reward_tips = OpenServerRewardTips.New()
	self.act_collect_card_task_view = ActCollectCardTaskView.New()
	self.act_collect_card_operate_view = ActCollectCardOperateView.New()
	


	self:RegisterAllProtocols()
	self.show_shouchong_icon = false

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.DayChange, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.DayChangeOrOpen, self))
	self:BindGlobalEvent(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DaycountChange, self))
	self:BindGlobalEvent(MainUIEventType.CHANGE_MAINUI_BUTTON, BindTool.Bind(self.MainUIButtonCreate, self))

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
	RoleWGData.Instance:NotifyAttrChange(BindTool.Bind1(self.RoleDataChangeCallBack, self), {"gold", "level"})
end

function ServerActivityWGCtrl:__delete()
    self:DeleteQingyuanActivity()
	self.act_lvlimit_icon_list = {}
	if self.openbosshunter_rank_view then
		self.openbosshunter_rank_view:DeleteMe()
		self.openbosshunter_rank_view = nil
	end
	if nil ~= self.server_banben_activity_view then
		self.server_banben_activity_view:DeleteMe()
		self.server_banben_activity_view = nil
	end
	if self.lingyaomaihuo_view then
		self.lingyaomaihuo_view:DeleteMe()
		self.lingyaomaihuo_view = nil
	end
	if nil ~= self.celebration_reward_tip then
		self.celebration_reward_tip:DeleteMe()
		self.celebration_reward_tip = nil
	end
	if nil ~= self.server_activity_data then
		self.server_activity_data:DeleteMe()
	end
	if nil ~= self.firstrecharge_view then
		self.firstrecharge_view:DeleteMe()
	end
	if nil ~= self.confirm_dialog then
		self.confirm_dialog:DeleteMe()
	end
	if nil ~= self.act_openserver_view then
		self.act_openserver_view:DeleteMe()
		self.act_openserver_view = nil
	end
	if self.kf_bipin_tip_view then
		self.kf_bipin_tip_view:DeleteMe()
		self.kf_bipin_tip_view = nil
	end
	if nil ~= self.serveract_tab_view then
		self.serveract_tab_view:DeleteMe()
		self.serveract_tab_view = nil
	end
	if nil ~= self.xianmeng_zhegnba_view then
		self.xianmeng_zhegnba_view:DeleteMe()
		self.xianmeng_zhegnba_view = nil
	end
	if self.kf_activity_view then
		self.kf_activity_view:DeleteMe()
		self.kf_activity_view = nil
	end
	if nil ~= self.act_openserver2_view then
		self.act_openserver2_view:DeleteMe()
		self.act_openserver2_view = nil
	end
	if nil ~= self.act_fireworks then
		self.act_fireworks:DeleteMe()
		self.act_fireworks = nil
	end
	if nil ~= self.boss_fight_reward_view then
		self.boss_fight_reward_view:DeleteMe()
		self.boss_fight_reward_view = nil
	end

	if nil ~= self.boss_fight_scene_view then
		self.boss_fight_scene_view:DeleteMe()
		self.boss_fight_scene_view = nil
	end

	if nil ~= self.rush_recharge_view2 then
		self.rush_recharge_view2:DeleteMe()
	end
	if nil ~= self.rush_recharge_view3 then
		self.rush_recharge_view3:DeleteMe()
	end

	if self.act_slider_num_view then
		self.act_slider_num_view:DeleteMe()
		self.act_slider_num_view = nil
	end

	if nil ~= self.marry_schedule_view then
		self.marry_schedule_view:DeleteMe()
		self.marry_schedule_view = nil
	end

	if self.gobal_xunbao_tip then
		self.gobal_xunbao_tip:DeleteMe()
		self.gobal_xunbao_tip = nil
	end

	if self.mainui_open_create_time then
		GlobalTimerQuest:CancelQuest(self.mainui_open_create_time)
		self.mainui_open_create_time = nil
	end

	if self.rand_act_opera_delay then
		GlobalTimerQuest:CancelQuest(self.rand_act_opera_delay)
		self.rand_act_opera_delay = nil
	end

	if self.mainui_bipin_rank_info_timer then
		GlobalTimerQuest:CancelQuest(self.mainui_bipin_rank_info_timer)
		self.mainui_bipin_rank_info_timer = nil
	end

	if self.xj_reward_tip ~= nil then
		self.xj_reward_tip:DeleteMe()
		self.xj_reward_tip = nil
	end

	if self.xunyulu_view then
		self.xunyulu_view:DeleteMe()
		self.xunyulu_view = nil
	end

	if self.serveract_dl_view ~= nil then
		self.serveract_dl_view:DeleteMe()
		self.serveract_dl_view = nil
	end

	if self.kf_activity_reward_view ~= nil then
		self.kf_activity_reward_view:DeleteMe()
		self.kf_activity_reward_view = nil
	end

	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	-- if self.firstrecharge_rebate_data then
	-- 	self.firstrecharge_rebate_data:DeleteMe()
	-- 	self.firstrecharge_rebate_data = nil
	-- end

	if self.everyday_recharge_rebate_data then
		self.everyday_recharge_rebate_data:DeleteMe()
		self.everyday_recharge_rebate_data = nil
	end

	if self.everyday_yinji_turntable_data then
		self.everyday_yinji_turntable_data:DeleteMe()
		self.everyday_yinji_turntable_data = nil
	end

	if self.everyday_lianxu_rechage_data then
		self.everyday_lianxu_rechage_data:DeleteMe()
		self.everyday_lianxu_rechage_data = nil
	end

	if self.everydayrecharge_view ~= nil then
		self.everydayrecharge_view:DeleteMe()
		self.everydayrecharge_view = nil
	end

	if self.everydayrecharge_record ~= nil then
		self.everydayrecharge_record:DeleteMe()
		self.everydayrecharge_record = nil
	end

	if self.yinji_turntable_record then
		self.yinji_turntable_record:DeleteMe()
		self.yinji_turntable_record = nil
	end

	if self.act_reward_list_show_view then
		self.act_reward_list_show_view:DeleteMe()
		self.act_reward_list_show_view = nil
	end

	if nil ~= self.server_high_point_reward_view then
		self.server_high_point_reward_view:DeleteMe()
		self.server_high_point_reward_view = nil
	end

	if self.open_srver_reward_tips then
		self.open_srver_reward_tips:DeleteMe()
		self.open_srver_reward_tips = nil
	end

	if self.activity_collect_card_data then
		self.activity_collect_card_data:DeleteMe()
		self.activity_collect_card_data = nil
	end

	if self.act_collect_card_task_view then
		self.act_collect_card_task_view:DeleteMe()
		self.act_collect_card_task_view = nil
	end

	if self.act_collect_card_operate_view then
		self.act_collect_card_operate_view:DeleteMe()
		self.act_collect_card_operate_view = nil
	end

	ServerActivityWGCtrl.Instance = nil
	self.init_dayday_fanli = nil
end

function ServerActivityWGCtrl:RoleDataChangeCallBack(key, value, old_value)
	if key == "level" then
		local tab = self.act_lvlimit_icon_list
		self.act_lvlimit_icon_list = {}
		for k,v in pairs(tab) do
			if v.act_type == ACTIVITY_TYPE.DayDayFanLi then
				local info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.DayDayFanLi)
				self:AddDayDayMainBtn(true, nil, info.status)
			end
		end

		self:ShowOrHideOpenActvityIcon()
		--开服冲榜的气泡，玩家等级200级后默认勾选，如果玩家手动点掉了那就不显示，200级前默认不勾选
		if old_value < 200 and value >= 200 then
			local is_show = ServerActivityWGData.Instance:GetMainUiIsShowBiPinRank()
			if not is_show then
				if self.kf_activity_view:IsOpen() and self.kf_activity_view:IsLoadedIndex(TabIndex.act_bipin) then
					self.kf_activity_view:SetBinPinMainuiShowRankToggle(true)
				else
					ServerActivityWGData.Instance:SetMainUiIsShowBiPinRank(1)
				end
			end
		end
	end
end

function ServerActivityWGCtrl:RegisterAllProtocols()
	--总登录天数
	self:RegisterProtocol(SCTotalLoginDays, "OnTotalLoginDays")
	--开服活动
	self:RegisterProtocol(CSOpenGameActivityFetchReward)
	self:RegisterProtocol(CSOpenGameActivityInfoReq)
	self:RegisterProtocol(SCOpenGameActivityInfo, "OnOpenGameActivityInfo")
	self:RegisterProtocol(CSOpenGameBossHunterReq)
	self:RegisterProtocol(SCOpenGameBossHunterRsp, "OnOpenGameBossHunterRsp")       --开服活动 BOSS猎人
	self:RegisterProtocol(SCOGABossHunterSelfGuildRank, "OnSCOGABossHunterSelfGuildRank")       --开服活动 BOSS猎人 盟内排行

	--领取充值奖励
	self:RegisterProtocol(CSChongzhiFetchReward)
	--领取再充领取奖励
	self:RegisterProtocol(CSZaiChongzhiFetchReward)

	--活动兑换
	self:RegisterProtocol(CSConvertActivityRewardReq)
	self:RegisterProtocol(SCActivityConvertInfo, "OnActivityConvertInfo")

	--幸运抽奖
	self:RegisterProtocol(SCRALuckydrawInfo, "OnRALuckydrawInfo")

	--一折抢购
	self:RegisterProtocol(CSDiscountBuyGetInfo)
	self:RegisterProtocol(CSDiscountBuyReqBuy)
	self:RegisterProtocol(SCDiscountBuyInfo, "OnDiscountBuyInfo")
	--滚动抽奖(老虎机)
	self:RegisterProtocol(CSRollMoneyOperaReq)
	self:RegisterProtocol(SCRollMoneyInfo, "OnRollMoneyInfo")
	--封测活动
	self:RegisterProtocol(CSCloseBetaActivityOperaReq)
	self:RegisterProtocol(SCCloseBetaActivityInfo, "OnCloseBetaActivityInfo")
	-- 投资计划
	self:RegisterProtocol(SCTouZiJiHuaInfo, "OnSCTouZiJiHuaInfo")
	self:RegisterProtocol(CSFetchTouZiJiHuaReward)
	self:RegisterProtocol(CSTouzijihuaActive)
	-- 折扣礼包
	self:RegisterProtocol(SCAdiscountGiftInfo, "OnSCAdiscountGiftInfo")
	self:RegisterProtocol(CSAdiscountGiftOpera)

	-- 名人争夺
	self:RegisterProtocol(SCTitleOwnerInfo, "OnSCTitleOwnerInfo")
	self:RegisterProtocol(CSTitleOwnerInfoReq)

	-- F2开服比拼
	self:RegisterProtocol(SCOGARushRankInfo, "OnSCOGARushRankInfo")
	self:RegisterProtocol(CSOGARushRankOp)

	-- 仙盟封榜称号变更
	self:RegisterProtocol(SCKaiZongLiPaiTitleChangeInfo, "OnSCKaiZongLiPaiTitleChangeInfo")

	---------------随机活动--------------------------------------------
	self:RegisterProtocol(CSRandActivityOperaReq)

	self:RegisterProtocol(SCRABuddhaBagInfo, "OnSCRABuddhaBagInfo")					--F2妖灵卖货
	self:RegisterProtocol(CSOgaTehuilibaoOpera)										--F2开服礼包请求
	self:RegisterProtocol(SCOgaTehuilibaoInfo, "OnSCOgaTehuilibaoInfo")					--F2开服礼包

	self:RegisterProtocol(SCRAServerPanicBuyInfo, "OnRAServerPanicBuyInfo")					--全民疯抢
	self:RegisterProtocol(SCRAChongzhiRankInfo, "OnRAChongzhiRankInfo")						--充值排行
	self:RegisterProtocol(SCRAConsumeGoldRankInfo, "OnRAConsumeGoldRankInfo")				--每日消费排行
	self:RegisterProtocol(SCRAConsumeGoldFanliInfo, "OnRAConsumeGoldFanliInfo")				--消费返利
	self:RegisterProtocol(SCRADayConsumeGoldInfo, "OnRADayConsumeGoldInfo")					--每日消费

	self:RegisterProtocol(SCQitianXiaofeiFanliInfo, "OnSCQitianXiaofeiFanliInfo")			--消费返利

	self:RegisterProtocol(SCRADayActiveDegreeInfo, "OnRADayActiveDegreeInfo")				--每日活跃度信息
	self:RegisterProtocol(SCRAKillBossInfo, "OnRAKillBossInfo")								--副本掉落双倍
	self:RegisterProtocol(SCRAChestshopInfo, "OnRAChestshopInfo")							--奇珍异宝
	self:RegisterProtocol(SCRAStoneUplevelInfo, "OnRAStoneUplevelInfo")						--宝石升级
	self:RegisterProtocol(SCRAXiannvChanmianUplevelInfo, "OnRAXiannvChanmianUplevelInfo")	--仙女缠绵
	self:RegisterProtocol(SCRAMountUpgradeInfo, "OnRAMountUpgradeInfo")						--坐骑进阶
	self:RegisterProtocol(SCRAQibingUpgradeInfo, "OnRAQibingUpgradeInfo")					--骑兵进阶
	self:RegisterProtocol(SCRAMentalityUplevelInfo, "OnRAMentalityUplevelInfo")				--根骨全身等级
	self:RegisterProtocol(SCRAWingUpgradeInfo, "OnRAWingUpgradeInfo")						--羽翼进化
	self:RegisterProtocol(SCRAMountChargeInfo, "OnRAMountChargeInfo")						--随机活动-坐骑充值信息
	self:RegisterProtocol(SCRAWingChargeInfo, "OnRAWingChargeInfo")							--随机活动-羽翼充值信息
	self:RegisterProtocol(SCRAShenwuChargeInfo, "OnRAShenwuChargeInfo")						--随机活动-神武充值信息
	self:RegisterProtocol(SCRAFabaoChargeInfo, "OnRAFabaoChargeInfo")						--随机活动-法宝充值信息
	self:RegisterProtocol(SCRALingchongChargeInfo, "OnRALingchongChargeInfo")				--随机活动-灵宠充值信息
	self:RegisterProtocol(SCRALingqiChargeInfo, "OnRALingqiChargeInfo")						--随机活动-灵骑充值信息
	self:RegisterProtocol(SCRALinggongChargeInfo, "OnRALinggongChargeInfo")						--随机活动-灵弓充值信息
	self:RegisterProtocol(SCRALingyiChargeInfo, "OnRALingyiChargeInfo")						--随机活动-灵翼充值信息
	self:RegisterProtocol(SCRAMountShopInfo, "OnRAMountShopInfo")							--随机活动-坐骑抢购信息
	self:RegisterProtocol(SCRAWingShopInfo, "OnRAWingShopInfo")							 	--随机活动-羽翼抢购信息
	self:RegisterProtocol(SCRAFabaoShopInfo, "OnRAFabaoShopInfo")							--随机活动-法宝抢购
	self:RegisterProtocol(SCRAShenwuShopInfo, "OnRAShenwuShopInfo")					--随机活动-神武抢购
	self:RegisterProtocol(SCRALingchongShopInfo, "OnRALingchongShopInfo")						--随机活动-灵宠抢购
	self:RegisterProtocol(SCRALingqiShopInfo, "OnRALingqiShopInfo")					--随机活动-灵骑抢购
	self:RegisterProtocol(SCRALinggongShopInfo, "OnRALinggongShopInfo")							--随机活动-灵弓抢购
	self:RegisterProtocol(SCRALingyiShopInfo, "OnRALingyiShopInfo")							--随机活动-灵翼抢购
	self:RegisterProtocol(SCRAFabaoUpgradeInfo, "OnRAFabaoUpgradeInfo")							--随机活动-法宝进阶
	self:RegisterProtocol(SCRAShenwuUpgradeInfo, "OnRAShenwuUpgradeInfo")					--随机活动-神武进阶
	self:RegisterProtocol(SCRALingchongUpgradeInfo, "OnRALingchongUpgradeInfo")						--随机活动-灵宠进阶
	self:RegisterProtocol(SCRALingqiUpgradeInfo, "OnRALingqiUpgradeInfo")					--随机活动-灵骑进阶
	self:RegisterProtocol(SCRALinggongUpgradeInfo, "OnRALinggongUpgradeInfo")							--随机活动-灵弓进阶
	self:RegisterProtocol(SCRALingyiUpgradeInfo, "OnRALingyiUpgradeInfo")							--随机活动-灵翼进阶
	self:RegisterProtocol(SCRAQuanminQifuInfo, "OnRAQuanminQifuInfo")						--全民祈福
	self:RegisterProtocol(SCRAShouYouYuXiangInfo, "OnRAShouYouYuXiangInfo")					--手留余香
	self:RegisterProtocol(SCRAXianMengBiPinInfo, "OnRAXianMengBiPinInfo")					--仙盟比拼
	self:RegisterProtocol(SCRAXianMengJueQiInfo, "OnRAXianMengJueQiInfo")					--仙盟崛起
	self:RegisterProtocol(SCChargeRewardInfo, "OnChargeRewardInfo")							--充值回馈

	self:RegisterProtocol(SCRADanbiChongzhiInfo, "OnRADanbiChongzhiInfo")					--随机活动每日单笔充值
	self:RegisterProtocol(SCRATomorrowRewardInfo, "OnRATomorrowRewardInfo")					--随机活动次日福利
	self:RegisterProtocol(SCRATimeLimitExchangeEquiInfo, "OnRATimeLimitExchangeEquiInfo")	--随机活动装备兑换
	self:RegisterProtocol(SCRATimeLimitExchangeJLInfo, "OnRATimeLimitExchangeJLInfo")		--随机活动精灵兑换
	self:RegisterProtocol(SCRADailyOnlineLotteryInfo, "OnRADailyOnlineLotteryInfo")			--随机活动每日在线抽奖信息
	self:RegisterProtocol(SCRADailyOnlineLotteryFetchResult, "OnRADailyOnlineLotteryFetchResult")	-- 随机活动每日在线抽奖结果

	self:RegisterProtocol(SCRASpecialAppearanceInfo, "OnRASpecialAppearanceInfo")			--随机活动整蛊专家活动
	self:RegisterProtocol(SCRASpecialAppearancePassiveInfo, "OnRASpecialAppearanceInfo")	--随机活动被整达人活动

	self:RegisterProtocol(SCRAZhanchangzhenbaInfo, "OnRAZhanchangzhenbaInfo")				--随机活动战场争霸信息

	self:RegisterProtocol(SCRALoginGiftInfo, "OnRALoginGiftInfo")							--登录送礼
	self:RegisterProtocol(SCConvertCrazyInfo, "OnSCConvertCrazyInfo")				        --随机活动兑换狂欢
	self:RegisterProtocol(SCRATotalCharge2Info, "OnRATotalCharge2Info")						--随机活动累计充值
	self:RegisterProtocol(SCRATotalConsumeGold2Info, "OnRATotalConsumeGold2Info")			--随机活动累计消费
	self:RegisterProtocol(SCRAChongZhiYouLiInfo, "OnRAChongZhiYouLiInfo")		     	    --随机活动充值有礼
	self:RegisterProtocol(SCRAHighRebateInfo, "OnRAHighRebateInfo")                         --随机活动高倍返利

	self:RegisterProtocol(SCActivitySettleNotice, "OnSCActivitySettleNotice")

	-- self:RegisterProtocol(RAPanicBuyOperaReq)					--随机活动特惠秒杀请求
	-- self:RegisterProtocol(RAPanicBuyInfo, "OnRAPanicBuyInfo")   --随机活动特惠秒杀

	-- 首充续充
	self:RegisterProtocol(SCSCXCActInfo, "OnSCSCXCActInfo")
	self:RegisterProtocol(SCSCXCActReceive)												--合服活动角色操作请求

	--每日累充
	self:RegisterProtocol(EveryDayRechargeInfo, "OnEveryDayRechargeInfo")
	self:RegisterProtocol(EveryDayRechargeSend)

	--每日消费
	self:RegisterProtocol(CSEveryDayExpenseInfo)
	self:RegisterProtocol(SCEveryDayExpenseInfo, "OnEveryDayExpenseInfo")
	self:RegisterProtocol(CSEveryDayExpenseSend)

	--每日累充--秒杀.
	self:RegisterProtocol(SCRADailyLeiChongRapidlyGiftInfo, "OnSCRADailyLeiChongRapidlyGiftInfo")
	--每日累充--秒杀礼包记录信息.
	self:RegisterProtocol(SCRapidlyGiftRecordInfo, "OnSCRapidlyGiftRecordInfo")

	--每日充值-每日礼包
	self:RegisterProtocol(SCRADailyLeiChongDailyGiftInfo, "OnSCRADailyLeiChongDailyGiftInfo")

	--随机活动聚宝盆
	self:RegisterProtocol(SCCornucopiaFetchInfo, "OnCornucopiaFetchInfo")
	self:RegisterProtocol(SCCornucopiaFetchRewadr, "OnCornucopiaFetchRewadr")

	--我要元宝(变元宝)
	self:RegisterProtocol(SCTotalChongzhiWantMoneyFetchInfo, "OnTotalChongzhiWantMoneyFetchInfo")
	self:RegisterProtocol(CSTotalChongzhiWantMoneyFetchInfo)
	self:RegisterProtocol(SCTotalChongzhiWantMoneyFetchReward, "OnTotalChongzhiWantMoneyFetchReward")
	self:RegisterProtocol(CSTotalChongzhiWantMoneyFetchReward)

	--合服活动
	-- self:RegisterProtocol(SCCSASubActivityState, "OnCSASubActivityState")					--合服活动子活动状态
	self:RegisterProtocol(SCCSAActivityInfo, "OnCSAActivityInfo")							--合服活动信息
	self:RegisterProtocol(SCCSARoleInfo, "OnCSARoleInfo")									--合服活动角色信息
	self:RegisterProtocol(SCCSARollResult, "OnCSARollResult")								--合服活动摇奖结果

	self:RegisterProtocol(CSCSAQueryActivityInfo)											--查询合服信息
	self:RegisterProtocol(CSCSARoleOperaReq)												--合服活动角色操作请求

	--周末boss
	self:RegisterProtocol(SCWeekendBossInfoAck, "OnWeekendBossInfoAck")
	self:RegisterProtocol(SCWeekendBossActSubStatus, "OnWeekendBossActSubStatus")
	self:RegisterProtocol(CSWeekendBossInfoReq)

	self:RegisterProtocol(SCChestshopCarnivalInfo, "OnChestshopCarnivalInfo")				--版本活动 寻宝狂欢
	self:RegisterProtocol(SCNationalCelebrationInfo, "OnSCNationalCelebrationInfo")			--全民庆典

	self:RegisterProtocol(SCRAYanhuaCelebrationResult, "OnSCRAYanhuaCelebrationResult")		--烟花庆典十连返回

	--boss乱斗
	self:RegisterProtocol(SCCrossActBossMessBattleUserInfo,"OnSCCrossActBossMessBattleUserInfo")
	self:RegisterProtocol(SCCrossActBossMessBattleRankInfo,"OnSCCrossActBossMessBattleRankInfo")
	self:RegisterProtocol(SCCBossMessMonsterFlushTimeInfo,"OnSCCBossMessMonsterFlushTimeInfo")
	self:RegisterProtocol(SCActivitySubStatus,"OnSCActivitySubStatus")

	--全民捐献
	self:RegisterProtocol(SCServerActQFJX, "OnSCServerActQFJX")

	self:RegisterProtocol(SCXunYuLuInfo, "OnSCXunYuLuInfo")

	
	self:RegisterProtocol(CSRoleZhiChonReq)
	
	self:RegisterProtocol(CSRoleLianXuChonZhiReq)
	self:RegisterProtocol(SCLianXuChongZhiInfo, "OnSCLianXuChongZhiInfo")--每日累充--连续充值
	self:RegisterProtocol(SCActZhiChongInfo, "OnSCActZhiChongInfo")--每日累充--直购返利

	---------------------------------印记转盘协议 star------------------------

	self:RegisterProtocol(CSOpYinJiZhuanPan)
	self:RegisterProtocol(SCSYinJiZhuanPanInfo, "OnSCSYinJiZhuanPanInfo")
	self:RegisterProtocol(SCYinJiZhuanPanDrawReward,"OnSCYinJiZhuanPanDrawReward")
	self:RegisterProtocol(SCYinJiZhuanPanDailyReward, "OnSCYinJiZhuanPanDailyReward")
	self:RegisterProtocol(SCYinJiZhuanPanLeiChouReward, "OnSCYinJiZhuanPanLeiChouReward")
	self:RegisterProtocol(SCYinJiZhuanPanNewServerLog, "OnSCYinJiZhuanPanNewServerLog")
	self:RegisterProtocol(SCYinJiZhuanPanDailyRecharge, "OnSCYinJiZhuanPanDailyRecharge")
	self:RegisterProtocol(SCYinJiZhuanPanDailyLoginReward, "OnSCYinJiZhuanPanDailyLoginReward")

	---------------------------------印记转盘协议 end-------------------------

	self:RegisterProtocol(SCOGAExtendTotalInfo, "OnSCOGAExtendTotalInfo")
	self:RegisterProtocol(SCOGAExtendRmbBuyInfoUpdate, "OnSCOGAExtendRmbBuyInfoUpdate")
	self:RegisterProtocol(SCOGAExtendTaskProcessUpdate, "OnSCOGAExtendTaskProcessUpdate")
	self:RegisterProtocol(SCOGAExtendTaskFetchFlagUpdate, "OnSCOGAExtendTaskFetchFlagUpdate")
	self:RegisterProtocol(SCOGAExtendCumulateRechargeInfoUpdate, "OnSCOGAExtendCumulateRechargeInfoUpdate")
	self:RegisterProtocol(CSOGAExtendReqOperate)

	---------------------------------开服集卡协议 star------------------------
	self:RegisterProtocol(SCAOCollectCardInfo,"OnSCAOCollectCardInfo")
	self:RegisterProtocol(SCAOCollectCardTaskInfo, "OnSCAOCollectCardTaskInfo")
	self:RegisterProtocol(SCAOCollectCardTaskUpdateInfo, "OnSCAOCollectCardTaskUpdateInfo")
	self:RegisterProtocol(SCAOCollectCardSelfInfo, "OnSCAOCollectCardSelfInfo")
	self:RegisterProtocol(SCAOCollectCardUpdateInfo, "OnSCAOCollectCardUpdateInfo")
	self:RegisterProtocol(SCAOCollectCardFriendInfo, "OnSCAOCollectCardFriendInfo")
	self:RegisterProtocol(SCAOCollectCardRecordInfo, "OnSCAOCollectCardRecordInfo")
	self:RegisterProtocol(SCAOCollectCardRecordUpdateInfo, "OnSCAOCollectCardRecordUpdateInfo")

	---------------------------------开服集卡协议 end-------------------------

	ActivityWGData.Instance:NotifyActChangeCallback(BindTool.Bind1(self.OnActivityDataChangeCallback, self))
	-- Remind.Instance:NotifyOneRemindChange(BindTool.Bind1(self.OneRemindChange, self))

	self.mainui_open_create_time = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.MainuiOpenCreate, self), 300) --每10分钟请求一次开服活动信息
end

function ServerActivityWGCtrl:SendCloseBetaActivityOperaReq(opera_param)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCloseBetaActivityOperaReq)
	if nil ~= protocol then
		protocol.opera_type = opera_param.opera_type
		protocol.param_1 = opera_param.param_1 or 0
		protocol.param_2 = opera_param.param_2 or 0
		protocol.param_3 = opera_param.param_3 or 0
		protocol:EncodeAndSend()
	end
end

function ServerActivityWGCtrl:OnCloseBetaActivityInfo(protocol)
	ActCloseBetaData.Instance:SetCloseBetaData(protocol)
	local act_cfg = ConfigManager.Instance:GetAutoConfig("client_activity_auto").client_activity_list
	for k, v in pairs(act_cfg) do
		local a, b = string.find(v.remind_id, "fc_")
		if "" ~= v.remind_id and a ~= nil and b ~= nil then
			---- Remind.Instance:DoRemind(v.remind_id)
		end
	end
	-- if self.act_closebeta_view:IsOpen() then
	-- 	self.act_closebeta_view:RefreshView()
	-- 	self.act_closebeta_view:RefreshButtonRemind()
	-- end
end

--同步总共登录天数
function ServerActivityWGCtrl:OnTotalLoginDays(protocol)
	self.server_activity_data:OnTotalLoginDays(protocol)
end

function ServerActivityWGCtrl:MainuiOpenCreateCallBack()
	LayoutZeroBuyWGCtrl.Instance:SendCSZeroBuyReq(0, 0)
	YiBenWanLiWGCtrl.Instance:SendYiBenWanLiWGCtrl(0,0)
	self:MainuiOpenCreate()
	self.server_activity_data:CheckNewKfGiftTip()
	ServerActivityWGCtrl.Instance:SendYinJiTurnTableOpera(OP_YIN_JI_ZHUAN_PAN_TYPE.OP_YIN_JI_ZHUAN_PAN_TYPE_INFO)
end

function ServerActivityWGCtrl:MainuiOpenCreate()
	if not MainuiWGCtrl.Instance:IsLoadMainUiView() then
		return
	end

	self:SendOpenGameActivityInfoReq()
	self:SendDiscountBuyGetInfo()
	self:SendRollMoneyOperaReq(RollMoneyEnum.ROLL_MONEY_OPERA_TYPE_QUERY)
	self:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_REWARD_TYPE_GOAL_FETCH)
	self:SendCSAQueryActivityInfo()
	self:SendTotalChongzhiWantMoneyFetchInfo()

	self:SendEveryDayExpenseInfo()
	self:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_LEICHONG_INFO)

	if not IS_AUDIT_VERSION and ServerActivityWGData.Instance:GetChaojiVipBool() then
		ServerActivityWGData.Instance:AddActOpeningType(ACTIVITY_TIME_TYPE.RAND_CHAOJI_VIP)
	else
		ServerActivityWGData.Instance:DeleteActOpeningType(ACTIVITY_TIME_TYPE.RAND_CHAOJI_VIP)
	end

	if not IS_AUDIT_VERSION and AgentAdapter.IsShoweValuateBotton and AgentAdapter:IsShoweValuateBotton() then
		ServerActivityWGData.Instance:AddActOpeningType(ACTIVITY_TIME_TYPE.RAND_EVALUATE)
	else
		ServerActivityWGData.Instance:DeleteActOpeningType(ACTIVITY_TIME_TYPE.RAND_EVALUATE)
	end

	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_CORNUCOPIA) then
		self:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_CORNUCOPIA, opera_type = RendActOperaType.QUERY_INFO})
	end

	self:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.DayDayFanLi, opera_type = DAYDAYFANLI.RA_CHONGZHI_DAY_REWARD_OPERA_TYPE_QUERY_INFO})
	self:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACT_LINGYAOMAIHUO, opera_type = CS_RA_BUDDHABAG_OPERA_TYPE.CS_RA_BUDDHABAG_OPERA_TYPE_INFO}) -- F2妖灵卖货
	self:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DENGLUYOULI, opera_type = TIANSHEN_THEME_LOGIN_GIFT_OP_TYPE.INFO}) -- 全民备战 登录奖励
	self:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DUOBEILAIXI}) -- 全民备战 多倍奖励

	self:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DENGLUYOULI, opera_type = TIANSHEN_THEME_LOGIN_GIFT_OP_TYPE.INFO}) -- 仙器解封 登录奖励
	self:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DUOBEILAIXI}) -- 仙器解封 多倍奖励
end

function ServerActivityWGCtrl:OpenCloseBetaView()
	-- self.act_closebeta_view:Open()
end

-- 屏蔽旧活动
function ServerActivityWGCtrl:Open(param_t)
	-- if param_t and param_t.open_param and param_t.open_param.open_act_id  then
	-- 	self:OpenByActId(ServerActClientId.BUY_MONTH_CARD)
	-- 	return
	-- end

	-- self.server_activity_view:Open()
end

function ServerActivityWGCtrl:OpenBanBenView(param_t)
	if param_t and param_t.open_param then
		if type(param_t.open_param) == "table" and param_t.open_param.open_act_id then
			self:OpenByActId(param_t.open_param.open_act_id)
			return
		else
			self:OpenByActId(tonumber(param_t.open_param))
			return
		end
	end

	self.server_banben_activity_view:Open()
end

function ServerActivityWGCtrl:OpenOpenserverCompetition(key, table_index, param_t)
	if key == "open_server" then
		self.serveract_tab_view:OpenByKey(table_index, param_t)
		self:FlushServerHighPointRewardView()
	elseif key == "kf_act" then
		self.kf_activity_view:OpenByKey(table_index, param_t)
	end
end

function ServerActivityWGCtrl:OpenOpenserverCompetitionByUniqueKey(uniquekey, params)
	for _,v in pairs(ServerActivityTabCfg) do
		if v.uniquekey and v.uniquekey == uniquekey then
			self:OpenOpenserverCompetition(v.key, v.table_index, params)
			break
		end
	end
end

-- 打开开服活动
function ServerActivityWGCtrl:OpenOpenserverView(tab_index, param_t ,btn_select_index)
	if param_t and param_t.open_param then
		if type(param_t.open_param) == "table" and param_t.open_param.open_act_id then
			self:OpenByActId(param_t.open_param.open_act_id, true)
			return
		else
			self:OpenByActId(tonumber(param_t.open_param), true)
			return
		end
	end
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SEVENDAY) then
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SEVENDAY ,opera_type = RA_QITIAN_XIAOFEI_FANLI_OPE_TYPE.QITIAN_XIAOFEI_FANLI_REQ_INFO })
	end
	-- self.act_openserver_view:GetOpenServerBtnIndex(btn_select_index)
	if btn_select_index == 2 then
		self.act_openserver_view:Open(tab_index)
	else
		self:OpenOpenserverCompetition(tab_index, param_t)
	end
end

function ServerActivityWGCtrl:CloseOpenServer2View()
	-- self.act_openserver2_view:Close()
	self.serveract_tab_view:Close()
end

function ServerActivityWGCtrl:OpenServerHighPointRewardView()
	self.server_high_point_reward_view:Open()
end

function ServerActivityWGCtrl:FlushServerHighPointRewardView()
	if self.server_high_point_reward_view:IsOpen() then
		self.server_high_point_reward_view:Flush()
	end
end


function ServerActivityWGCtrl:CloseActBanbenServerView(tab_index, param_t ,btn_select_index)
	self.server_banben_activity_view:Close()
end

--此接口用于外部打开特定的控件，前提是控件存在，在使用此接口请务必确定相应模块已经存在(填写活动ID)
function ServerActivityWGCtrl:OpenByActId(act_id, is_nor)
	if not ActivityWGCtrl.Instance:IsOpenServerOpen() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.RollActivityEnd)
		return
	end

	local idx = nil
	-- local is_nor = ServerActivityWGData.IsNormalActId(act_id)
	local open_act_cfg = ServerActivityWGData.Instance:GetShowOpenActList(true, is_nor)
	for k,v in pairs(open_act_cfg) do
		if v.id then
			if v.id == act_id then
				idx = k
				break
			end
		end
	end
	self:OpenByIndex(idx, nil, nil, is_nor)
end

--此接口用于外部打开特定的控件，前提是控件存在，在使用此接口请务必确定相应模块已经存在
function ServerActivityWGCtrl:OpenByIndex(index, param, zodaer, is_nor)
	local view = is_nor and self.act_openserver_view or self.server_banben_activity_view
	view:SelectHandler(index)
	view:Open()
	if param then
		local cur_view = view:GetCurView()
		if cur_view == nil then
			return
		end
		if param == "gaojitouzi" and cur_view.OnInvestTwo then
			cur_view:OnInvestTwo()
		end
		if param == "chujitouzi" and cur_view.OnInvestOne then
			cur_view:OnInvestOne()
		end
	end
end

function ServerActivityWGCtrl:RefreshViewButtonList()
	-- self.server_activity_view:UpdateBtnList()
	-- self.act_closebeta_view:UpdateBtnList()
	self.act_openserver_view:UpdateBtnList()
	self.server_banben_activity_view:UpdateBtnList()
end

function ServerActivityWGCtrl:SendDiscountBuyGetInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSDiscountBuyGetInfo)
	if nil ~= protocol then
		protocol:EncodeAndSend()
	end
end

--一折抢购请求
function ServerActivityWGCtrl:DiscountBuyReq(data)
	if nil == data then
		return
	end
	if data.price ~= 0 then
		self.confirm_dialog:SetOkString(Language.Common.Confirm)
		self.confirm_dialog:SetCancelString(Language.Common.Cancel)
		local reward_item = data.reward_item
		local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
		if item_cfg == nil then
			print_error("配置为空，item_id为",reward_item.item_id)
			return
		end
		local item_color = ITEM_COLOR[item_cfg.color]
		local item_name_str = string.format("<color=#%s>%s</color>", item_color, item_cfg.name)
		self.confirm_dialog:SetLableString(string.format(Language.OpenServer.DiscountBuy, data.price, reward_item.num, item_name_str))
		self.confirm_dialog:SetOkFunc(BindTool.Bind2(self.SendDiscountBuyReqBuy, self, data.seq))
		self.confirm_dialog:SetShowCheckBox(true)
		self.confirm_dialog:Open()
	else
		self:SendDiscountBuyReqBuy(data.seq)
	end
end

--一折抢购请求
function ServerActivityWGCtrl:SendDiscountBuyReqBuy(seq)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDiscountBuyReqBuy)
	if nil ~= protocol then
		protocol.seq = seq
		protocol:EncodeAndSend()
	end
end

function ServerActivityWGCtrl:SendRollMoneyOperaReq(opera_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRollMoneyOperaReq)
	if nil ~= protocol then
		protocol.opera_type = opera_type
		protocol:EncodeAndSend()
	end
end

function ServerActivityWGCtrl:OnRollMoneyInfo(protocol)
end

--一折抢购信息返回
function ServerActivityWGCtrl:OnDiscountBuyInfo(protocol)
	ActDiscountData.Instance:SetDiscountInfo(protocol)
	-- MainuiWGCtrl.Instance:FlushActDiscount()
	if ViewManager.Instance:IsOpen(GuideModuleName.ActDiscount) then
		ViewManager.Instance:FlushView(GuideModuleName.ActDiscount)
	end
end

--购买投资计划--现在改为用元宝来买了
function ServerActivityWGCtrl:ClickBuyTouZiJiHua(is_chuji_plan)
	local money = 0
	local plan_type = 0
	local other_config = ConfigManager.Instance:GetAutoConfig("touzijihua_auto").other[1]
	if is_chuji_plan then
		plan_type = 0
		money = other_config.plan_0_price
	else
		plan_type = 1
		money = other_config.plan_1_price
	end

	self.confirm_dialog:SetOkString(Language.Common.Confirm)
	self.confirm_dialog:SetCancelString(Language.Common.Cancel)
	self.confirm_dialog:SetLableString(string.format(Language.OpenServer.SureToTouzi, money))
	self.confirm_dialog:SetOkFunc(function() self:SendTouzijihuaActive(plan_type) end)
	self.confirm_dialog:Open()
end

--打开首充
function ServerActivityWGCtrl:OpenFirstRechargeView(param_t)
	local flag = self:GetShouChongShowFlag()
	if flag then
		self.firstrecharge_view:Open()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.ShouChongTip)
	end
end

function ServerActivityWGCtrl:ZhiGouCellAnimCallBack()
	if self.firstrecharge_view:IsOpen() then
		self.firstrecharge_view:ZhiGouCellAnimCallBack()
	end
end

-- --刷新再充值
-- function ServerActivityWGCtrl:RefreshRechargeAgainView()

-- end

--
function ServerActivityWGCtrl:EDZhiGouCellAnimCallBack()
	if self.everydayrecharge_view:IsOpen() then
		self.everydayrecharge_view:EDZhiGouCellAnimCallBack()
	end
end

--打开一折抢购面板(特惠)
function ServerActivityWGCtrl:OpenPrivilegeView()
	-- self.privilege_view:Open()
end

--刷新一折抢购面板(特惠)
function ServerActivityWGCtrl:FlushPrivilegeView()
	-- self.privilege_view:Flush()
end

function ServerActivityWGCtrl:ShowOrHideOpenActvityIcon()
	local status = ACTIVITY_STATUS.CLOSE
	for _,v in pairs (ServerActivityTabCfg) do
		if v.key == "open_server" and v.can_open_func() then
			status = ACTIVITY_STATUS.OPEN
			break
		end
	end
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER, status)

	status = ACTIVITY_STATUS.CLOSE
	for _,v in pairs (ServerActivityTabCfg) do
		if v.key == "kf_act" and v.can_open_func() then
			status = ACTIVITY_STATUS.OPEN
			break
		end
	end
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.KF_ACTIVITY, status)
end

function ServerActivityWGCtrl:DayChange()
	self.server_banben_activity_view:UpdateBtnList()
	if self.server_banben_activity_view:IsOpen() then
		self.server_banben_activity_view:RefreshView()
	end

	self.act_openserver_view:UpdateBtnList()
	if self.act_openserver_view:IsOpen() then
		self.act_openserver_view:RefreshView('daychange')
	end
	self:FlushServerActTabView(0, "daychange", {"daychange"})

	-- region 界面调整使用事件更新
	GlobalEventSystem:Fire(OPENSERVER_ACTIVITY_COMPETITION.UPDATE_BTN_LIST)
	GlobalEventSystem:Fire(GLOBELEVENT_OPENSERVER_ACTIVITY.UPDATE_BTN_LIST)
	-- endregion

	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GOD_XUNBAO) then
		GodGetRewardWGCtrl.Instance:SendAllLayerRequire()
	end
end

function ServerActivityWGCtrl:UpdateBtnListActTwo()
	-- region 界面调整使用事件更新
	GlobalEventSystem:Fire(OPENSERVER_ACTIVITY_COMPETITION.REFRESH_BTN_LIST)
	-- self.act_openserver2_view:RefershBtnList()
	-- endregion
end

function ServerActivityWGCtrl:GetViewIsOpen()
	return self.act_openserver_view:IsOpen() or self.server_banben_activity_view:IsOpen()
end

--运营活动开启关闭通知
function ServerActivityWGCtrl:OnActivityDataChangeCallback(activity_type, status, next_time)
	if nil == ShowSmallActCfg[activity_type] then
		return
	end
	if status == ACTIVITY_STATUS.OPEN then
		if activity_type > 2048 then
			self:SendRandActivityOperaReq({rand_activity_type = activity_type, opera_type = RendActOperaType.QUERY_INFO})
		end
		if activity_type == ACTIVITY_TYPE.CLOSE_BETA then
			ActCloseBetaData.Instance:SetIsCloseBetaOpen(true)
		else
			if not (activity_type == ACTIVITY_TYPE.OPEN_SERVER and not ActivityWGCtrl.Instance:IsOpenServerOpen()) then
				--版本活动走的这里
				self.server_activity_data:AddActOpeningType(activity_type)
			end
		end
	end

	if status == ACTIVITY_STATUS.CLOSE then
		if activity_type == ACTIVITY_TYPE.CLOSE_BETA then
			ActCloseBetaData.Instance:SetIsCloseBetaOpen(false)
		end
		self.server_activity_data:DeleteActOpeningType(activity_type)
	end
	if activity_type == ACTIVITY_TYPE.OPEN_SERVER then
		self:UpdataMaiuiIcon()
	end
	self.server_banben_activity_view:UpdateBtnList()
	self.act_openserver_view:UpdateBtnList()
	self:UpdataViewAndRemind()
end

--充值相关的主界面图标显示隐藏刷新
function ServerActivityWGCtrl:UpdataMaiuiIcon()
	if IS_FREE_VERSION then
		FunOpen.Instance:ForceCloseFunByName(FunName.SuperGift)
		return
	end

	self.server_activity_data:UpdataMainUiIcons()
end

--请求领取奖励。总接口
function ServerActivityWGCtrl:SendGetReward(act_id, param_t)
	local time_type = ServerActivityWGData.Instance:GetActTimeType(act_id)
	local client_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if client_cfg == nil then return end

	if time_type == ACTIVITY_TYPE.OPEN_SERVER then		--开服活动
		if act_id == ServerActClientId.EQUIP_GIFT then
			RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_ACT_BUY_EQUIPMENT_GIFT, param_t.reward_seq)
		else
			self:SendOpenGameActivityFetchReward(client_cfg.get_type, param_t.reward_seq)
		end
	elseif time_type == ACTIVITY_TYPE.RAND_DOUBLE_XUNBAO_JIFEN then --双倍寻宝积分
		local op_cfg = {[0] = "xunBao", "sprite##op=liequ"}
		FunOpen.Instance:OpenViewNameByCfg(op_cfg[param_t.reward_seq])

	elseif time_type == ACTIVITY_TIME_TYPE.NORMAL or    --充值活动
		time_type == ACTIVITY_TIME_TYPE.OPEN_SERVER_END_7 or
		time_type == ACTIVITY_TIME_TYPE.CHONGZHI_END_3 then

		self:SendChongzhiFetchReward(client_cfg.get_type, param_t.reward_seq)
	elseif time_type > ACTIVITY_TYPE.Act_Roller then
		local param_table = {rand_activity_type = time_type, opera_type = RendActOperaType.GET_ITEM, param_1 = param_t.reward_seq}
		self:SendRandActivityOperaReq(param_table)
	end
end

------------------------------------------------
--开服活动
------------------------------------------------
--请求活动信息
function ServerActivityWGCtrl:SendOpenGameActivityInfoReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSOpenGameActivityInfoReq)
	if nil ~= protocol then
		protocol:EncodeAndSend()
	end
end

--开服活动信息返回
function ServerActivityWGCtrl:OnOpenGameActivityInfo(protocol)
	self.server_activity_data:SetOpenServerData(protocol)
	self:FlushServerActTabView(0, "act_info", {protocol_id = 2718})

	RemindManager.Instance:Fire(RemindName.OpenServer)
	RemindManager.Instance:Fire(RemindName.KFSevendayRecharge)
	RemindManager.Instance:Fire(RemindName.KFKaiZongLiPai)
	RemindManager.Instance:Fire(RemindName.KFXianMengZhengBa)

	self:UpdataMaiuiIcon()
	self:RuningRequesFlushBiPinRankInfo()

	if ViewManager.Instance:IsOpen(GuideModuleName.QunXiongZhuLu) then
		ViewManager.Instance:FlushView(GuideModuleName.QunXiongZhuLu)
	end
end

-- boss猎人
function ServerActivityWGCtrl:OnOpenGameBossHunterRsp(protocol)
	self.server_activity_data:SetBossHunterInfo(protocol)
	self:FlushServerActTabView(0, "act_info", {protocol_id = 2730})
end

-- boss猎人 盟内排行
function ServerActivityWGCtrl:OnSCOGABossHunterSelfGuildRank(protocol)
	self.server_activity_data:SetBossHunterMyGuidRankInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BossHunterRank, 0)
end

--是否已经购买过该商品
function ServerActivityWGCtrl:GifIsBeBuy(seq)
	if self.oga_cool_suit_crazy_buy_flag == nil then
		return false
	end
	local num = self.oga_cool_suit_crazy_buy_flag[32-seq]
	if num == 1 then
		return true
	end
	return false
end
--将排行榜数据写入表，仙盟等级
function ServerActivityWGCtrl:OnGuildLevelRankList(rank_type, rank_list)
	local guild_act_cfg = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").guild_level_rank_cfg
	for i = 0, table.maxn(guild_act_cfg) do
		local v = guild_act_cfg[i]
		if nil ~= v and nil ~= rank_list[i + 1] then
			local camp = rank_list[i + 1].camp
			local color3b = CAMP_COLOR3B[camp]
			local color_str = C3b2Str(color3b)
			v.guild_name = string.format("<color=#%s>%s</color>", color_str, rank_list[i + 1].guild_name)
			v.rank_value = Language.OpenServer.GuildLevel .. rank_list[i + 1].rank_value
		else
			v.guild_name = Language.OpenServer.NotGuildOnRank
			v.rank_value = ""
		end
	end

	--最强仙盟
	self.server_activity_data:CacheActRewardData(ServerActClientId.GUILD_LEVEL_RANK, bit:d2b(0), {self.server_activity_data:GetMyGuildRank()})
	if self.server_banben_activity_view:IsOpen() then
		self.server_banben_activity_view:RefreshView()
	end
end

--将排行榜数据写入表，仙盟击杀boss数量
function ServerActivityWGCtrl:OnGuildKillBossRankList(rank_type, rank_list)
	local write_data_func = function(guild_act_cfg)
		for i = 0, table.maxn(guild_act_cfg) do
			local v = guild_act_cfg[i]
			if nil ~= v and nil ~= rank_list[i + 1] then
				local camp = rank_list[i + 1].camp
				local color3b = CAMP_COLOR3B[camp]
				local color_str = C3b2Str(color3b)
				v.guild_name = string.format("<color=#%s>%s</color>", color_str, rank_list[i + 1].guild_name)
				v.rank_value = Language.OpenServer.GuildKillBoss .. rank_list[i + 1].rank_value
			else
				v.guild_name = Language.OpenServer.NotGuildOnRank
				v.rank_value = ""
			end
		end
	end
	--开服活动--仙盟狩猎
	local world_boss_rank_cfg = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").world_boss_rank_cfg
	write_data_func(world_boss_rank_cfg)

	if self.server_banben_activity_view:IsOpen() then
		self.server_banben_activity_view:RefreshView()
	end
end

--将排行榜数据写入表，仙盟击杀boss增加增量
function ServerActivityWGCtrl:OnGuildUpKillBossRankList(rank_type, rank_list)
	local write_data_func = function(guild_act_cfg)
		for i = 0, table.maxn(guild_act_cfg) do
			local v = guild_act_cfg[i]
			if nil ~= v and nil ~= rank_list[i + 1] then
				local camp = rank_list[i + 1].camp
				local color3b = CAMP_COLOR3B[camp]
				local color_str = C3b2Str(color3b)
				v.guild_name = string.format("<color=#%s>%s</color>", color_str, rank_list[i + 1].guild_name)
				v.rank_value = Language.OpenServer.GuildKillBoss .. rank_list[i + 1].rank_value
			else
				v.guild_name = Language.OpenServer.NotGuildOnRank
				v.rank_value = ""
			end
		end
	end

	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local xianmeng_bipin = config.xianmeng_bipin
	-- write_data_func(xianmeng_bipin)

	if self.server_banben_activity_view:IsOpen() then
		self.server_banben_activity_view:RefreshView()
	end
end

--将排行榜数据写入表，仙盟战力提升
function ServerActivityWGCtrl:OnGuildKillUpZhanliRankList(rank_type, rank_list)
	if self.server_banben_activity_view:IsOpen() then
		self.server_banben_activity_view:RefreshView()
	end
end


--请求开服活动的领奖（仅限于开服活动）
--@reward_type 子活动类型
--@reward_seq 第几个奖励
function ServerActivityWGCtrl:SendOpenGameActivityFetchReward(reward_type, reward_seq, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOpenGameActivityFetchReward)
	if nil ~= protocol then
		protocol.reward_type = reward_type
		protocol.reward_seq = reward_seq
		protocol.param_1 = param1 or 0
		protocol.param_2 = param2 or 0
		protocol:EncodeAndSend()
	end
end

--boss猎人请求信息
function ServerActivityWGCtrl:SendOpenGameBossHunterReq(req, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOpenGameBossHunterReq)
	protocol.req = req
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

------------------------------------------------
--充值活动
------------------------------------------------
--请求充值的奖励
--@type：  0,-- 特殊首充 1,-- 日常首充 2,-- 日常累充
--@param 如在累充中，第几个
function ServerActivityWGCtrl:SendChongzhiFetchReward(type, param, select_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChongzhiFetchReward)
	if nil ~= protocol then
		protocol.type = type
		protocol.param = param or 0
		protocol.select_type = select_type or 0
		protocol:EncodeAndSend()
	end
end

--领取再充奖励
function ServerActivityWGCtrl:SendZaiChongzhiFetchReward(is_third)
	local protocol = ProtocolPool.Instance:GetProtocol(CSZaiChongzhiFetchReward)
	if nil ~= protocol then
		protocol.is_third = is_third or 0
		protocol:EncodeAndSend()
	end
end

------------------------------------------------
--提醒
------------------------------------------------

--提醒数字改变通知
function ServerActivityWGCtrl:OneRemindChange(remind_id, num)
end

function ServerActivityWGCtrl:CheckOpenServerRemind(remind_id)

	return 0
end

function ServerActivityWGCtrl:UpdataViewAndRemind(param_t)
	if self.server_banben_activity_view:IsOpen() then
		self.server_banben_activity_view:RefreshView(param_t)
		self.server_banben_activity_view:RefreshButtonRemind()
	end

	if self.act_openserver_view:IsOpen() then
		self.act_openserver_view:RefreshView()
		-- self.act_openserver_view:RefreshButtonRemind()
	end
end
------------------------------------------------
-- 折扣礼包
------------------------------------------------
function ServerActivityWGCtrl:OnSCAdiscountGiftInfo(protocol)
	ServerActivityWGData.Instance:SetZheKouInfo(protocol)
	GlobalEventSystem:Fire(ActEventType.FLUSH_ADISCOUNTGIFT)
end
function ServerActivityWGCtrl:AdiscountGiftInfoReq(opera_type,param)
	local protocol = ProtocolPool.Instance:GetProtocol(CSAdiscountGiftOpera)
	protocol.opera_type = opera_type
	protocol.param = param
	protocol:EncodeAndSend()
end

------------------------------------------------
-- 投资计划
------------------------------------------------
function ServerActivityWGCtrl:OnSCTouZiJiHuaInfo(protocol)
	RechargeWGData.Instance:SetTouZiPlanSerData(protocol)
	RechargeWGCtrl.Instance:Flush(TabIndex.recharge_tqtz)
	-- RemindManager.Instance:Fire(RemindName.Vip)--VIP主界面按钮
    RemindManager.Instance:Fire(RemindName.Vip_TQTZ)--VIP特权计划
    ViewManager.Instance:FlushView(GuideModuleName.XianLingGuZhen)
end

function ServerActivityWGCtrl:SetFetchTouZiJiHuaReward(plan_type, seq)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFetchTouZiJiHuaReward)
	protocol.plan_type = plan_type
	protocol.seq = seq
	protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:SendTouzijihuaActive(plan_type, plan_grade)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTouzijihuaActive)
	protocol.plan_type = plan_type
	protocol.plan_grade = plan_grade
	protocol:EncodeAndSend()
end


------------------------------------------------
-- 名人争夺(称号)
------------------------------------------------
function ServerActivityWGCtrl:OnSCTitleOwnerInfo(protocol)
	ServerActivityWGData.Instance:SetTitleOwnerInfo(protocol)
end

function ServerActivityWGCtrl:SetTitleOwnerInfoReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSTitleOwnerInfoReq)
	protocol:EncodeAndSend()
end

--活动兑换
function ServerActivityWGCtrl:SendConvertActivityRewardReq(param_t)
	local protocol = ProtocolPool.Instance:GetProtocol(CSConvertActivityRewardReq)
	protocol.seq = param_t
	protocol:EncodeAndSend()
end

--——————————————————————————————————————————————
----------------- 随机活动----------------------
------------------------------------------------
RendActOperaType = {
	QUERY_INFO = 0,
	GET_ITEM = 1,
	ONLINE_TIME_INFO = 2,
}
RendActLoginGiftOperaType = {
	FETCH_COMMON_REWARD = 1,
	FETCH_VIP_REWARD = 2,
	FETCH_ACCUMULATE_REWARD = 3,
}

--随机活动拉数据，领取奖励等操作
local param_t_cacha = {}
function ServerActivityWGCtrl:SendRandActivityOperaReq(param_t)
	table.insert(param_t_cacha, param_t)
	if nil == self.rand_act_opera_delay then
		self:SendRandActivityOperaReqReal()
	end
end

--随机活动拉数据，领取奖励等操作
function ServerActivityWGCtrl:SendRandActivityOperaReqReal()
	-- if IS_ON_CROSSSERVER then return end
	local param_t = table.remove(param_t_cacha, 1)
	if nil == param_t then
		self.rand_act_opera_delay = nil
		return
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = param_t.rand_activity_type
	protocol.opera_type = param_t.opera_type or 0
	protocol.param_1 = param_t.param_1 or 0
	protocol.param_2 = param_t.param_2 or 0
	protocol.param_3 = param_t.param_3 or 0
	protocol:EncodeAndSend()
	if not self.rand_act_opera_delay and param_t_cacha[1] then
		self.rand_act_opera_delay = GlobalTimerQuest:AddDelayTimer(function ()
			self.rand_act_opera_delay = nil
			self:SendRandActivityOperaReqReal()
		end, 0)
	end
end

--随机活动-全民疯抢
function ServerActivityWGCtrl:OnRAServerPanicBuyInfo(protocol)
	self.server_activity_data:OnRAServerPanicBuyInfo(protocol)
	self:UpdataViewAndRemind()
end

--抢购打开二次确认框
function ServerActivityWGCtrl:GoToPanicBuy(data)
	self.confirm_dialog:SetOkString(Language.OpenServer.Buy)
	self.confirm_dialog:SetCancelString(Language.Common.Cancel)
	local reward_item = data.reward_item
	local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
	local item_color = C3b2Str(ITEM_COLOR[item_cfg.color])
	local item_name_str = string.format("<color=#%s>%s</color>", item_color, item_cfg.name)
	self.confirm_dialog:SetLableString(string.format(Language.OpenServer.DiscountBuy, data.gold_price, reward_item.num, item_name_str))
	self.confirm_dialog:SetOkFunc(BindTool.Bind1(data.get_callback, self))
	self.confirm_dialog:Open()
end

--随机活动-充值排行
function ServerActivityWGCtrl:OnRAChongzhiRankInfo(protocol)
	self.server_activity_data:OnRAChongzhiRankInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-消费排行
function ServerActivityWGCtrl:OnRAConsumeGoldRankInfo(protocol)
	self.server_activity_data:OnRAConsumeGoldRankInfo(protocol)
	self:UpdataViewAndRemind()

end
--随机活动-消费返利
function ServerActivityWGCtrl:OnRAConsumeGoldFanliInfo(protocol)
	self.server_activity_data:OnRAConsumeGoldFanliInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-每日消费
function ServerActivityWGCtrl:OnRADayConsumeGoldInfo(protocol)
	self.server_activity_data:OnRADayConsumeGoldInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-每日活跃度信息
function ServerActivityWGCtrl:OnRADayActiveDegreeInfo(protocol)
	self.server_activity_data:OnRADayActiveDegreeInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-击杀Boss
function ServerActivityWGCtrl:OnRAKillBossInfo(protocol)
	self.server_activity_data:OnRAKillBossInfo(protocol)
	self:UpdataViewAndRemind()
end
-- 随机活动-奇珍异宝
function ServerActivityWGCtrl:OnRAChestshopInfo(protocol)
	self.server_activity_data:OnRAChestshopInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-宝石升级
function ServerActivityWGCtrl:OnRAStoneUplevelInfo(protocol)
	self.server_activity_data:OnRAStoneUplevelInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-仙女缠绵
function ServerActivityWGCtrl:OnRAXiannvChanmianUplevelInfo(protocol)
	self.server_activity_data:OnRAXiannvChanmianUplevelInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-坐骑进阶
function ServerActivityWGCtrl:OnRAMountUpgradeInfo(protocol)
	self.server_activity_data:OnRAMountUpgradeInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-骑兵进阶
function ServerActivityWGCtrl:OnRAQibingUpgradeInfo(protocol)
	self.server_activity_data:OnRAQibingUpgradeInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-根骨全身等级
function ServerActivityWGCtrl:OnRAMentalityUplevelInfo(protocol)
	self.server_activity_data:OnRAMentalityUplevelInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-全民祈福
function ServerActivityWGCtrl:OnRAQuanminQifuInfo(protocol)
	self.server_activity_data:OnRAQuanminQifuInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-手留余香
function ServerActivityWGCtrl:OnRAShouYouYuXiangInfo(protocol)
	self.server_activity_data:OnRAShouYouYuXiangInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-登录送礼
function ServerActivityWGCtrl:OnRALoginGiftInfo(protocol)
	self.server_activity_data:OnRALoginGiftInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动-仙盟比拼
function ServerActivityWGCtrl:OnRAXianMengBiPinInfo(protocol)
	self.server_activity_data:OnRAXianMengBiPinInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-仙盟崛起
function ServerActivityWGCtrl:OnRAXianMengJueQiInfo(protocol)
	self.server_activity_data:OnRAXianMengJueQiInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-充值回馈
function ServerActivityWGCtrl:OnChargeRewardInfo(protocol)
	self.server_activity_data:OnChargeRewardInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-每日单笔充值
function ServerActivityWGCtrl:OnRADanbiChongzhiInfo(protocol)
	self.server_activity_data:OnRADanbiChongzhiInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-次日福利
function ServerActivityWGCtrl:OnRATomorrowRewardInfo(protocol)
	self.server_activity_data:OnRATomorrowRewardInfo(protocol)
	self:UpdataViewAndRemind()
	if 0 == protocol.reward_index then return end
	if self.server_banben_activity_view:GetCurView().TurnToIndex then
		self.server_banben_activity_view:GetCurView():TurnToIndex(protocol.reward_index)
	end
end
--随机活动-装备兑换
function ServerActivityWGCtrl:OnRATimeLimitExchangeEquiInfo(protocol)
	self.server_activity_data:OnRATimeLimitExchangeEquiInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-精灵兑换
function ServerActivityWGCtrl:OnRATimeLimitExchangeJLInfo(protocol)
	self.server_activity_data:OnRATimeLimitExchangeJLInfo(protocol)
	self:UpdataViewAndRemind()
end
--随机活动-每日在线抽奖信息
function ServerActivityWGCtrl:OnRADailyOnlineLotteryInfo(protocol)
	self.server_activity_data:SetRADailyOnlineLotteryInfo(protocol)
	self:UpdataViewAndRemind()
end
-- 随机活动每日在线抽奖结果
function ServerActivityWGCtrl:OnRADailyOnlineLotteryFetchResult(protocol)
	self.server_activity_data:SetRADailyOnlineLotteryFetchResult(protocol)
	self:UpdataViewAndRemind()
end

-----------------------
-- 我要元宝(变元宝)
-----------------------

function ServerActivityWGCtrl:OpenIWantGoldView()
	-- self.actiwantgold_view:Open()
end
-- 返回阶段和充值数
function ServerActivityWGCtrl:OnTotalChongzhiWantMoneyFetchInfo(protocol)
	self.server_activity_data:SetTotalChongzhiWantMoneyFetchInfo(protocol)
	self:UpdataViewAndRemind()
end

-- 请求阶段和充值数
function ServerActivityWGCtrl:SendTotalChongzhiWantMoneyFetchInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSTotalChongzhiWantMoneyFetchInfo)
	protocol:EncodeAndSend()
end

-- 返回元宝数
function ServerActivityWGCtrl:OnTotalChongzhiWantMoneyFetchReward(protocol)
	self.server_activity_data:SetTotalChongzhiWantMoneyFetchReward(protocol)
	if self.server_banben_activity_view:GetCurView().LotteryResult then
		self.server_banben_activity_view:GetCurView():LotteryResult()
	end
end

-- 请求得到的元宝数
function ServerActivityWGCtrl:SendTotalChongzhiWantMoneyFetchReward()
	local protocol = ProtocolPool.Instance:GetProtocol(CSTotalChongzhiWantMoneyFetchReward)
	protocol:EncodeAndSend()
end

-----------------------
-- 聚宝盆
-----------------------

-- 聚宝盆信息
function ServerActivityWGCtrl:OnCornucopiaFetchInfo(protocol)
	self.server_activity_data:SetIWGInfo(protocol)
	RechargeWGCtrl.Instance.recharge_all_view:Flush(RechargeView.TABINDEX.JBP)
end

-- 聚宝盆活动元宝百分比
function ServerActivityWGCtrl:OnCornucopiaFetchRewadr(protocol)
	local index = self.server_activity_data:GetIwgIndexByRate(protocol.get_reward_rate)
	if index > 0 then
		RechargeWGCtrl.Instance.recharge_all_view:Flush(RechargeView.TABINDEX.JBP, "roll_result", {value = index})
	end
end

-----------------------------------------------------------
--合服活动
------------------------------------------------------------
--合服活动子活动状态
function ServerActivityWGCtrl:OnCSASubActivityState(protocol)
	-- self.act_combine_data:SetCombineSubActivityState(protocol)
	self:UpdataViewAndRemind()
	self:UpdataMaiuiIcon()
	if self.act_openserver_view:IsOpen() then
		self.act_openserver_view:RefreshView()
		-- self.act_openserver_view:RefreshButtonRemind()
	end
end

--合服活动信息
function ServerActivityWGCtrl:OnCSAActivityInfo(protocol)
	-- self.act_combine_data:SetCombineActivityInfo(protocol)
	self:UpdataViewAndRemind()
end

--合服活动角色信息
function ServerActivityWGCtrl:OnCSARoleInfo(protocol)
	-- self.act_combine_data:SetCombineRoleInfo(protocol)
	self:UpdataViewAndRemind()
	local flush_param = {roll_result = true, value = protocol.csa_recharge_draw_hit_index}
	self.act_openserver_view:RefreshView(flush_param)
end

--合服活动摇奖结果
function ServerActivityWGCtrl:OnCSARollResult(protocol)
	-- self.act_combine_data:SetCombineRollResult(protocol)
	self:UpdataViewAndRemind()
end

--查询合服信息
function ServerActivityWGCtrl:SendCSAQueryActivityInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSCSAQueryActivityInfo)
	protocol:EncodeAndSend()
end

--合服活动角色操作请求
function ServerActivityWGCtrl:SendCSARoleOperaReq(sub_type, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCSARoleOperaReq)
	protocol.sub_type = sub_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end


--------------------
--急速冲战1

function ServerActivityWGCtrl:OpenRushRecharge()
end

function ServerActivityWGCtrl:FlushRushRecharge(index, param_t)
end

--------------------------
--急速冲战2

function ServerActivityWGCtrl:OpenRushRechargeTwo()
	self.rush_recharge_view2:Open()
end

--------------------------
--急速冲战3

function ServerActivityWGCtrl:OpenRushRechargeThree()
	self.rush_recharge_view3:Open()
end

--------------------------
--急速冲战4

function ServerActivityWGCtrl:OpenRushRechargeFour()
	-- self.rush_recharge_view4:Open()
end


function ServerActivityWGCtrl:OpenMarryScheduleView()
	self.marry_schedule_view:Open()
end

function ServerActivityWGCtrl:SetRecordFlag(flag)
	self.marry_schedule_view:SetRecordFlag(flag)
end

function ServerActivityWGCtrl:OpenActRankGetListView(act_id, min_rank, max_rank)
end



--------------------------------------------------------------------------------
-- 整蛊专家活动, 被整达人活动
function ServerActivityWGCtrl:OnRASpecialAppearanceInfo(protocol)
	self.server_activity_data:SetSpecialAppearanceInfo(protocol)

	local is_open_act = false
	if self.act_openserver_view:IsOpen() then
		is_open_act = true
	end
	local open_act_list = ServerActivityWGData.Instance:GetShowOpenActList(true, is_open_act)

	for k, v in pairs(open_act_list) do
		if v.view_type == 30 then
			if nil ~= self.act_openserver_view.sub_view_list[v.id] then
				local sub_view = self.act_openserver_view.sub_view_list[v.id]
				sub_view:FlushAllInfo()
			end
			if nil ~= self.server_banben_activity_view.sub_view_list[v.id] then
				local sub_view= self.server_banben_activity_view.sub_view_list[v.id]
				sub_view:FlushAllInfo()
			end
		end
	end
end

--------------------------------------------------------------------------------
-- 随机活动战场争霸信息
function ServerActivityWGCtrl:OnRAZhanchangzhenbaInfo(protocol)
	self.server_activity_data:SetZhanchangzhenbaInfo(protocol)
	self:UpdataViewAndRemind()
end

function ServerActivityWGCtrl:OnSCConvertCrazyInfo(protocol)
	self.server_activity_data:SetConvertCrazyInfo(protocol)
	self:UpdataViewAndRemind()

	if nil == self.act_banben_item_is_remind then
		self.act_banben_item_is_remind = true
		local item_list = ServerActivityWGData.Instance:GetExchagneCrazyItemList()
	end
end

function ServerActivityWGCtrl:OnRATotalCharge2Info(protocol)
	self.server_activity_data:SetRATotalChargeDay2Info(protocol)
	self:UpdataViewAndRemind()
end

function ServerActivityWGCtrl:OnRATotalConsumeGold2Info(protocol)
	self.server_activity_data:SetRATotalConsumeGold2Info(protocol)
	self:UpdataViewAndRemind()
end

function ServerActivityWGCtrl:OnRAChongZhiYouLiInfo(protocol)
	self.server_activity_data:SetRAChongZhiYouLiInfo(protocol)
	self:UpdataViewAndRemind()
end

--幸运抽奖
function ServerActivityWGCtrl:OnRALuckydrawInfo(protocol)
	self.server_activity_data:OnRALuckydrawInfo(protocol)
	self:UpdataViewAndRemind()
	local cur_view = self.act_openserver_view:GetCurView()
	if cur_view and cur_view.act_id == ServerActClientId.RAND_LOTTERY_DRAW and protocol.hit_index ~= -1 then
		cur_view:SendTurningHandler(protocol.hit_index + 1)
	end
end

--活动兑换
function ServerActivityWGCtrl:OnActivityConvertInfo(protocol)
	self.server_activity_data:OnActivityConvertInfo(protocol)
	self:UpdataViewAndRemind()
end

--------------------------------------------------------------------------------
--随机活动 坐骑充值信息
function ServerActivityWGCtrl:OnRAMountChargeInfo(protocol)
	self.server_activity_data:OnRAMountChargeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 羽翼充值信息
function ServerActivityWGCtrl:OnRAWingChargeInfo(protocol)
	self.server_activity_data:OnRAWingChargeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 神武充值信息
function ServerActivityWGCtrl:OnRAShenwuChargeInfo(protocol)
	self.server_activity_data:OnRAShenwuChargeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 法宝充值信息
function ServerActivityWGCtrl:OnRAFabaoChargeInfo(protocol)
	self.server_activity_data:OnRAFabaoChargeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 灵宠充值信息
function ServerActivityWGCtrl:OnRALingchongChargeInfo(protocol)
	self.server_activity_data:OnRALingchongChargeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 灵骑充值信息
function ServerActivityWGCtrl:OnRALingqiChargeInfo(protocol)
	self.server_activity_data:OnRALingqiChargeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 灵弓充值信息
function ServerActivityWGCtrl:OnRALinggongChargeInfo(protocol)
	self.server_activity_data:OnRALinggongChargeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 灵翼充值信息
function ServerActivityWGCtrl:OnRALingyiChargeInfo(protocol)
	self.server_activity_data:OnRALingyiChargeInfo(protocol)
	self:UpdataViewAndRemind()
end

----------------------------------------------------------------
--随机活动 坐骑抢购信息
function ServerActivityWGCtrl:OnRAMountShopInfo(protocol)
	self.server_activity_data:OnRAMountShopInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 羽翼抢购信息
function ServerActivityWGCtrl:OnRAWingShopInfo(protocol)
	self.server_activity_data:OnRAWingShopInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 法宝抢购信息
function ServerActivityWGCtrl:OnRAFabaoShopInfo(protocol)
	self.server_activity_data:OnRAFabaoShopInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 神武抢购信息
function ServerActivityWGCtrl:OnRAShenwuShopInfo(protocol)
	self.server_activity_data:OnRAShenwuShopInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 灵宠抢购信息
function ServerActivityWGCtrl:OnRALingchongShopInfo(protocol)
	self.server_activity_data:OnRALingchongShopInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 灵骑抢购信息
function ServerActivityWGCtrl:OnRALingqiShopInfo(protocol)
	self.server_activity_data:OnRALingqiShopInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 .灵弓抢购信息
function ServerActivityWGCtrl:OnRALinggongShopInfo(protocol)
	self.server_activity_data:OnRALinggongShopInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动 灵翼抢购信息
function ServerActivityWGCtrl:OnRALingyiShopInfo(protocol)
	self.server_activity_data:OnRALingyiShopInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动-神武进阶
function ServerActivityWGCtrl:OnRAShenwuUpgradeInfo(protocol)
	self.server_activity_data:OnRAShenwuUpgradeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动-法宝进阶
function ServerActivityWGCtrl:OnRAFabaoUpgradeInfo(protocol)
	self.server_activity_data:OnRAFabaoUpgradeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动-羽翼进阶
function ServerActivityWGCtrl:OnRAWingUpgradeInfo(protocol)
	self.server_activity_data:OnRAWingUpgradeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动-灵宠进阶
function ServerActivityWGCtrl:OnRALingchongUpgradeInfo(protocol)
	self.server_activity_data:OnRALingchongUpgradeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动-灵骑进阶
function ServerActivityWGCtrl:OnRALingqiUpgradeInfo(protocol)
	self.server_activity_data:OnRALingqiUpgradeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动-灵弓进阶
function ServerActivityWGCtrl:OnRALinggongUpgradeInfo(protocol)
	self.server_activity_data:OnRALinggongUpgradeInfo(protocol)
	self:UpdataViewAndRemind()
end

--随机活动-灵翼进阶
function ServerActivityWGCtrl:OnRALingyiUpgradeInfo(protocol)
	self.server_activity_data:OnRALingyiUpgradeInfo(protocol)
	self:UpdataViewAndRemind()
end

function ServerActivityWGCtrl:OnSCQitianXiaofeiFanliInfo(protocol)
	self.server_activity_data:OnSCQitianXiaofeiFanliInfo(protocol)
	self:UpdataViewAndRemind()
end

--周末boss
function ServerActivityWGCtrl:SendWeekendBossReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSWeekendBossInfoReq)
	protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:OnWeekendBossActSubStatus(protocol)
	if protocol.rand_act_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_XINGTIANLAIXI then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.XINGTIANLAIXI, protocol.status, protocol.next_status_switch_time, 0, 0, 1)
		QuanMinBeiZhanWGData.Instance:XingTianLaiXiActivityState(protocol.status, protocol.next_status_switch_time)
		QuanMinBeiZhanWGCtrl.Instance:XingTianLaiXiCountDown()
		BiZuoWGData.Instance:SetActivityHallCfg()
	elseif protocol.rand_act_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI then--仙器解封--刑天来袭
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD, protocol.status, protocol.next_status_switch_time, 0, 0, 1)
		ActXianQiJieFengWGData.Instance:XingTianLaiXiActivityState(protocol.status, protocol.next_status_switch_time)
		ActXianQiJieFengWGCtrl.Instance:XingTianLaiXiCountDown()
		BiZuoWGData.Instance:SetActivityHallCfg()
	elseif protocol.rand_act_type == ACTIVITY_TYPE.GOD_JIANGLIN then
		-- ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.TIANSHENJIANLIN, protocol.status, protocol.next_status_switch_time, 0, 0, 1)
		TianshenRoadWGData.Instance:TianShenJianLinActivityState(protocol.status, protocol.next_status_switch_time)
		TianshenRoadWGCtrl.Instance:CheckTianShenJianLinActIcon()
		TianshenRoadWGCtrl.Instance:TianShenJianLinCountDown()
		BiZuoWGData.Instance:SetActivityHallCfg()
	elseif protocol.rand_act_type == ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.CLIENT_MOWUJINGLIN, protocol.status, protocol.next_status_switch_time, 0, 0, 1)
		MoWuJiangLinWGData.Instance:MoWuJianLinActivityState(protocol.status, protocol.next_status_switch_time)
		MoWuJiangLinWGCtrl.Instance:MWJLCountDown()
	end

	-- 经验双倍或副本双倍掉落 主界面活动按钮
	local banben_activity_cfg = FunOpen.Instance:GetFunCfgList().banben_activity 		 -- banben_activity功能开启配置
	local is_show_icon = banben_activity_cfg and RoleWGData.Instance.role_vo.level >= banben_activity_cfg.task_level or false	--是否达到等级
	if protocol.rand_act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE or protocol.rand_act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP then
		if protocol.status == ACTIVITY_STATUS.OPEN and is_show_icon then
			MainuiWGCtrl.Instance:AddTempActIcon(protocol.rand_act_type, protocol.next_status_switch_time)
		else
			if not is_show_icon then
				-- print_error("111")
				MainuiWGCtrl.Instance:DelTempActIcon(protocol.rand_act_type)
			elseif protocol.status == ACTIVITY_STATUS.CLOSE then
				local act_statu = ActivityWGData.Instance:GetActivityStatuByType(protocol.rand_act_type)
				-- print_error("222", act_statu == nil)
				if act_statu == nil or act_statu.status == ACTIVITY_STATUS.CLOSE then
					MainuiWGCtrl.Instance:DelTempActIcon(protocol.rand_act_type)
				end
			end

		end
	end

	self.server_activity_data:SetVersionActivityNowStatus(protocol.rand_act_type, protocol.status, protocol.next_status_switch_time)
	self.server_banben_activity_view:RefreshView()

	if protocol.rand_act_type == ACTIVITY_TYPE.RAND_WEEKEND_BOSS then
		local activity_cfg = DailyWGData.Instance:GetActivityConfig(protocol.rand_act_type)
		--周末Boss
		local ct
		if ACTIVITY_STATUS.CLOSE == protocol.status then
			ct = "{showpos;2}周末Boss"..Language.Activity.HuoDongYiGuanBi
			MainuiWGCtrl.Instance:DelTempActIcon(protocol.rand_act_type)
		elseif ACTIVITY_STATUS.STANDY == protocol.status then
			--print_error("周末BOSS  STANDY")
			ct = "{showpos;2}周末Boss"..Language.Activity.ActivityPrepare
			MainuiWGCtrl.Instance:AddTempActIcon(protocol.rand_act_type, protocol.next_status_switch_time, activity_cfg)
		elseif ACTIVITY_STATUS.OPEN == protocol.status then
			--print_error("周末BOSS  OPEN", protocol.next_status_switch_time, protocol.next_status_switch_time - TimeWGCtrl.Instance:GetServerTime())
			ct = "{showpos;2}周末Boss"..Language.Activity.ActivityStart
			MainuiWGCtrl.Instance:AddTempActIcon(protocol.rand_act_type, protocol.next_status_switch_time, activity_cfg)
		end
		TipWGCtrl.Instance:ShowSystemScroll(ct)
		ChatWGCtrl.Instance:AddRumorMsg(ct)
	elseif protocol.rand_act_type == ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN then
		OperationTaskChainWGCtrl.Instance:SetTaskChainActStatus(protocol)
	end
end

function ServerActivityWGCtrl:OnWeekendBossInfoAck(protocol)
	self.server_activity_data:SetWeekendBossData(protocol)
	-- print_error(protocol.scene_item_list)
	self.server_banben_activity_view:RefreshView()
end

-- 发送传闻，包括界面上滚动的字和聊天窗口
function ServerActivityWGCtrl:SendWorldMessage(act_id, act_status, message_type, next_status_flush_time, flag)
	local content = ""
	local activity_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if activity_cfg == nil then return end
	local name = activity_cfg.act_name
	if act_status == ACTIVITY_STATUS.STANDY then
		if act_id == ACTIVITY_TYPE.HUNYAN then
			content = "{showpos;2}" .. name..Language.Activity.ActivityPrepareTen
		else
			content = "{showpos;2}" .. name..Language.Activity.ActivityPrepare
		end
	elseif act_status == ACTIVITY_STATUS.OPEN then
		content = "{showpos;2}" .. name..Language.Activity.ActivityStart
		local cur_timestamp = TimeWGCtrl.Instance:GetServerTime()
	elseif act_status == ACTIVITY_STATUS.CLOSE then
		content = "{showpos;2}" .. name..Language.Activity.HuoDongYiGuanBi
	end
	if flag and content ~= "" then
		TipWGCtrl.Instance:ShowSystemNotice(content)
		ChatWGCtrl.Instance:AddRumorMsg(content)
	end
end

function ServerActivityWGCtrl:OnChestshopCarnivalInfo(protocol)
	self.server_activity_data:OnChestshopCarnivalInfo(protocol)
end

function ServerActivityWGCtrl:OnSCNationalCelebrationInfo(protocol)
	self.server_activity_data:OnSCNationalCelebrationInfo(protocol)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local act_statu = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE)
	if protocol.is_fetch_reward and protocol.is_fetch_reward ~= 0 then
		local now_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local temp = math.ceil(now_day/7)
		local kaifu_day = temp * 7
		local item_name = ""
		if kaifu_day > 49 then
			kaifu_day = 999999
		end
		local data1 = ServerActivityWGData.Instance:GetQuaMinQingDianReward()
		for k,v in pairs(data1) do
			if v.opengame_day == kaifu_day then
				if v.national_celebration_server_reward_item.item_id then
					GuildWGCtrl.Instance:OpenGuildDayRewardViewOther(v.national_celebration_server_reward_item.item_id)
				end
			end
		end
	end
	if act_statu and protocol.double_exp_end_timestamp > act_statu.next_time then
		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE, protocol.double_exp_end_timestamp)
		DOUBLE_EXP_FLAG_BY_GOBALXUNBAO = true
	elseif act_statu == nil and protocol.double_exp_end_timestamp > server_time then
		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE, protocol.double_exp_end_timestamp)
		DOUBLE_EXP_FLAG_BY_GOBALXUNBAO = true
	else
		DOUBLE_EXP_FLAG_BY_GOBALXUNBAO = false
		if act_statu then
			local tmp_activity = ServerActivityWGData.Instance:GetVersionActivityNowStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE) --临时开启
			local boo = true
			if tmp_activity == nil then
				boo = true
			else
				if tmp_activity.status ~= ACTIVITY_STATUS.CLOSE then
					boo = false
				end
			end
			if boo and act_statu.status == ACTIVITY_STATUS.CLOSE then
				MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE)
			end
		end
	end


	local act_statu_two = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP)
	if act_statu_two and act_statu_two.status ~= ACTIVITY_STATUS.CLOSE and protocol.double_fb_end_timestamp > act_statu_two.next_time then
		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP, protocol.double_fb_end_timestamp)
		DOUBLE_DROP_FLAG_BY_GOBALXUNBAO = true
	elseif act_statu_two == nil and protocol.double_fb_end_timestamp > server_time then
		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP, protocol.double_fb_end_timestamp)
		DOUBLE_DROP_FLAG_BY_GOBALXUNBAO = true
	else
		DOUBLE_DROP_FLAG_BY_GOBALXUNBAO = false
		local tmp_activity = ServerActivityWGData.Instance:GetVersionActivityNowStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP) --临时开启
		local boo = true
		if tmp_activity == nil then
			boo = true
		else
			if tmp_activity.status ~= ACTIVITY_STATUS.CLOSE then
				boo = false
			end
		end
		if act_statu_two then
			if boo and act_statu_two.status == ACTIVITY_STATUS.CLOSE then
				MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP)
			end
		end
	end
end

function ServerActivityWGCtrl:ActivityChangeCallBack(act_type, status, end_time, open_type)
	if status == ACTIVITY_STATUS.CLOSE and act_type == 2172 then
		DOUBLE_EXP_FLAG_BY_GOBALXUNBAO = false
		local act_statu_exp = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE)
		if act_statu_exp then
			local tmp_activity = ServerActivityWGData.Instance:GetVersionActivityNowStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE) --临时开启
			local boo = true
			if tmp_activity == nil then
				boo = true
			else
				if tmp_activity.status ~= ACTIVITY_STATUS.CLOSE then
					boo = false
				end
			end
			if boo and act_statu_exp.status == ACTIVITY_STATUS.CLOSE then
				MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE)
			end
		else
			MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE)
		end

		DOUBLE_DROP_FLAG_BY_GOBALXUNBAO = false
		local act_statu_double = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP)
		if act_statu_double then
			local tmp_activity = ServerActivityWGData.Instance:GetVersionActivityNowStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP) --临时开启
			local boo = true
			if tmp_activity == nil then
				boo = true
			else
				if tmp_activity.status ~= ACTIVITY_STATUS.CLOSE then
					boo = false
				end
			end
			if boo and act_statu_double.status == ACTIVITY_STATUS.CLOSE then
				MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP)
			end
		else
			MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE)
		end
	elseif act_type == ACTIVITY_TYPE.KF_BOSS_FIGHT then
		if ACTIVITY_STATUS.CLOSE == status then
			local activity_cfg = DailyWGData.Instance:GetActivityConfig(act_type)
			local str = Language.ActBossFight.ShowStatusInfo
			str = str .. Language.Activity.HuoDongYiGuanBi
			MainuiWGCtrl.Instance:DelTempActIcon(act_type)
			TipWGCtrl.Instance:ShowSystemScroll(str)
			ChatWGCtrl.Instance:AddRumorMsg(str)
		end
	elseif act_type == ACTIVITY_TYPE.DayDayFanLi then
		if ACTIVITY_STATUS.OPEN == status then
			self:AddDayDayMainBtn(true, nil, status)
		else
			self:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.DayDayFanLi, opera_type = DAYDAYFANLI.RA_CHONGZHI_DAY_REWARD_OPERA_TYPE_QUERY_INFO})
		end
	end
end

function ServerActivityWGCtrl:OpenNationalCelebrationRewardTip(data)
	local str = ""
	if data.pic_name == "btn_ychd" then
		local now_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local temp = math.ceil(now_day/7)
		local kaifu_day = temp * 7
		local item_name = ""
		if kaifu_day > 49 then
			kaifu_day = 999999
		end
		local data1 = ServerActivityWGData.Instance:GetQuaMinQingDianReward()
		for k,v in pairs(data1) do
			if v.opengame_day == kaifu_day then
				local cfg = ItemWGData.Instance:GetItemConfig(v.national_celebration_server_reward_item.item_id)
				item_name = cfg.name.."x"..v.national_celebration_server_reward_item.num
				str = string.format(Language.CelebrationDesc[data.pic_name],data.need_progress_var,item_name)
			end
		end
	else
		str = string.format(Language.CelebrationDesc[data.pic_name],data.need_progress_var) --Language.Common.ActDesc ..
	end
	RuleTip.Instance:SetContent(str, Language.XunbaoIconName[data.pic_name])
	-- self.celebration_reward_tip:Open()
	-- self.celebration_reward_tip:SetContent(data)
end

---------------------------高倍返利------------------------
function ServerActivityWGCtrl:OnRAHighRebateInfo(protocol)
end

-------------特惠秒杀购买请求---------------------
function ServerActivityWGCtrl:SendRAPanicBuyOperaReq(opera_type, param_1)
	local protocol = ProtocolPool.Instance:GetProtocol(RAPanicBuyOperaReq)
	if nil ~= protocol then
		protocol.opera_type = opera_type
		protocol.param_1 = param_1 or 0
		protocol:EncodeAndSend()
	end
end

-- function ServerActivityWGCtrl:OnRAPanicBuyInfo(protocol)
-- 	self.server_activity_data:SetRAPanicBuyInfo(protocol)
-- 	-- self.tehui_shop:Flush()
-- end
-------------------节日庆典奖励二级界面----------------------------

function ServerActivityWGCtrl:OpenGobalXunbaoTip()
	self.gobal_xunbao_tip:Open()
end

function ServerActivityWGCtrl:CloseGobalXunbaoTip()
	self.gobal_xunbao_tip:Close()
end


----------------------天天返利-------------------------------------------
--添加/删除按钮
function ServerActivityWGCtrl:AddDayDayMainBtn( enable, end_time, status)
	if self.init_dayday_fanli == enable then
		return
	end
	self.init_dayday_fanli = enable
	local act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.DayDayFanLi)
	local is_level = false --等级是否达到开启条件
	local level = GameVoManager.Instance:GetMainRoleVo().level
	if level >= act_cfg.level and level < act_cfg.level_max then
		is_level = true
	end
	if enable then
		if is_level then
			MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.DayDayFanLi, nil, act_cfg)
		else
			MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.DayDayFanLi) --等级不足
			self.init_dayday_fanli = nil
			self.act_lvlimit_icon_list[ACTIVITY_TYPE.DayDayFanLi] = {act_type = ACTIVITY_TYPE.DayDayFanLi}
		end
	else
		MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.DayDayFanLi)
	end
end

function ServerActivityWGCtrl:SetChangeToView(index, callback)
    self.server_banben_activity_view:SetChangeToView(index, callback)
end

function ServerActivityWGCtrl:ChangeToView(index, callback)
	self.server_banben_activity_view:ChangeToView(index, callback)
end

function ServerActivityWGCtrl:OnSCRAYanhuaCelebrationResult( protocol )
	TipWGCtrl.Instance:OpenActYanHuaRewradView(protocol.reward_index)
end

function ServerActivityWGCtrl:OnSCCrossActBossMessBattleUserInfo( protocol )
	self.server_activity_data:SetBossFightUserInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BossFightSceneView)
end

function ServerActivityWGCtrl:OnSCCrossActBossMessBattleRankInfo( protocol )
	-- print_error("rank_count",protocol.rank_count)
	self.server_activity_data:SetBossFightRankInfo(protocol.rank_item)
	ViewManager.Instance:FlushView(GuideModuleName.BossFightSceneView)
end

function ServerActivityWGCtrl:OnSCCBossMessMonsterFlushTimeInfo( protocol )
	self.server_activity_data:SetBossFightSceneInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BossFightSceneView)
end

function ServerActivityWGCtrl:OnSCActivitySubStatus( protocol )
	self.server_activity_data:SetActivitySubStatus(protocol.activity_type,protocol.next_status_switch_time,protocol.status)
end

function ServerActivityWGCtrl:DaycountChange()
	ViewManager.Instance:FlushView(GuideModuleName.KfActivityView, TabIndex.act_bipin, "daychange")
	ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView)
	if self.kf_activity_view:IsOpen() then
		self.kf_activity_view:Flush()
	end
	--ViewManager.Instance:FlushView(GuideModuleName.KfActivityView, TabIndex.act_bipin)
end

function ServerActivityWGCtrl:DayChangeOrOpen()
	self:ShowOrHideOpenActvityIcon()
end

function ServerActivityWGCtrl:SendQueryFirstKillInfo()
	local t = {}
	t.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FIRST_KILL
	t.opera_type = RA_FIERCE_FIGHTING_OPERA_TYPE.RA_QUERY_INFO
	self:SendRandActivityOperaReq(t)
end

function ServerActivityWGCtrl:SendFirstKillRewardReq(id)
	local t = {}
	t.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FIRST_KILL
	t.opera_type = RA_FIERCE_FIGHTING_OPERA_TYPE.KILL_REWARD
	t.param_1 = id
	self:SendRandActivityOperaReq(t)
end

function ServerActivityWGCtrl:SendFirstKillRedPackReq(id)
	local t = {}
	t.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FIRST_KILL
	t.opera_type = RA_FIERCE_FIGHTING_OPERA_TYPE.RED_BAG
	t.param_1 = id
	self:SendRandActivityOperaReq(t)
end

------------------------每日充值----------------------------
function ServerActivityWGCtrl:OnEveryDayRechargeInfo(protocol)
	self.server_activity_data:SetEveryDayRechargeInfo(protocol)
	self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_leichong, "ser_info", {protocol_id = 8941})
	ViewManager.Instance:FlushView(GuideModuleName.Vip, TabIndex.recharge_leichong, "ser_info", {protocol_id = 8941})
	RemindManager.Instance:Fire(RemindName.DailyRecharge_LeiChong)
end

function ServerActivityWGCtrl:SendEveryDayRecharge(req_type, param1)   --领取每日充值奖励
	local protocol = ProtocolPool.Instance:GetProtocol(EveryDayRechargeSend)
	protocol.req_type = req_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

-----------------------每日礼包-----------------------------
function ServerActivityWGCtrl:OnSCRADailyLeiChongDailyGiftInfo(protocol)
	local old_free = -1
	local old_data = self.server_activity_data:GetEveryDayGiftInfo()
	local cur_free = protocol.free_reward_flag
	if not IsEmptyTable(old_data) then
		old_free = old_data.free_reward_flag
		if cur_free == 1 and old_free == 0 then -- 1 已领取 0 未领取
			self:OnGetEveryDayLiBaoReward()
		end
	end

	self.server_activity_data:SetEveryDayLeiChongDailyGiftInfo(protocol)
	self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_dailygift)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_dailygift)
	RemindManager.Instance:Fire(RemindName.DailyRecharge_Libao)
end

function ServerActivityWGCtrl:OnGetEveryDayLiBaoReward() --每日礼包领取奖励  打开恭喜获得
	local reward_list = ServerActivityWGData.Instance:GetLeiChongOtherCfg("daily_item")
	if not IsEmptyTable(reward_list) then
		local item_list = {}
		for _,v in pairs(reward_list) do
			table.insert(item_list, v)
		end
		TipWGCtrl.Instance:ShowGetReward(nil, item_list, nil, nil, nil, true)
	end
end

------------------------每日消费----------------------------
--请求获取数据
function ServerActivityWGCtrl:SendEveryDayExpenseInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSEveryDayExpenseInfo)
	protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:OnEveryDayExpenseInfo(protocol)
	local old_everyday_expense_info = self.server_activity_data:GetEveryDayRewardInfo() or {}
	local cur_consume_reward_record = protocol.daily_consume_reward_record 
	local old_consume_reward_record = old_everyday_expense_info.daily_consume_reward_record or {}
	if (not IsEmptyTable(cur_consume_reward_record)) and (not IsEmptyTable(old_consume_reward_record)) then
		for k,v in pairs(cur_consume_reward_record) do
			if v == 1 and old_consume_reward_record[k] == 0 then -- 1 已领取 0 未领取
				self:OnGetEveryDayExpenseReward(32 - k)
			end
		end
	end
	self.server_activity_data:SetEveryDayExpenseInfo(protocol)
	self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_xiaofei)
	ViewManager.Instance:FlushView(GuideModuleName.Vip, TabIndex.recharge_xiaofei)
	RemindManager.Instance:Fire(RemindName.DailyRecharge_XiaoFei)
end

--点击领取
function ServerActivityWGCtrl:SendEveryDayExpense(param)   --领取每日消费奖励
	local protocol = ProtocolPool.Instance:GetProtocol(CSEveryDayExpenseSend)
	protocol.param = param or 0
	protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:OnGetEveryDayExpenseReward(seq) --每日消费领取奖励  打开恭喜获得
	if seq == nil then
		return
	end
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local data_list = ServerActivityWGData.Instance:GetXiaoFeiCfg(open_day)
	local reward_list = {}
	if data_list then
		for _,v in pairs(data_list) do
			if v.id == seq then
				for k, item in pairs(v.reward_item) do
					table.insert(reward_list, item)
				end
				break
			end
		end
	end
	if not IsEmptyTable(reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end
end

function ServerActivityWGCtrl:OnGetEveryDayRechargeReward(protocol)   --领取每日充值奖励返回(用于奖励展示)
	-- print_error("FFF==== 领取每日充值奖励返回(用于奖励展示)", protocol)
	if IsEmptyTable(protocol) then
		return
	end
	local reward_type = protocol.param1 or 0
	local reward_flag = protocol.param2 or 0
	if reward_type == 0 or reward_flag == 0 then--登陆奖励为礼包类型,不用再展示
		return
	end
	local data_list = {}
	local reward_list = {}
	local temp_t = nil
	if reward_type == 1 then--充值奖励--分每一档领取
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		data_list = ServerActivityWGData.Instance:GetLeiChongCfg(open_day, reward_flag)
		temp_t = data_list and data_list.reward_item
		if not IsEmptyTable(temp_t) then
			for k, item in pairs(temp_t) do
				table.insert(reward_list, item)
			end
		end
	elseif reward_type == 2 then--累计奖励--存在一键领取多个
		local count = ServerActivityWGData.Instance:GetEveryDayCurCycleCount()
		local get_flag_t = bit:d2b_two(reward_flag)
		data_list = ServerActivityWGData.Instance:GetLeiChongReward(count)
		if not IsEmptyTable(data_list) then
			for i, v in ipairs(data_list) do
				if get_flag_t[i-1] > 0 then--领取的档位
					temp_t = v.reward_item
					if not IsEmptyTable(temp_t) then
						for k, item in pairs(temp_t) do
							table.insert(reward_list, item)
						end
					end
				end
			end
		end
	end
	
	if not IsEmptyTable(reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end
end

-----------------------每日充值end--------------------------

------------------------每日累充-秒杀----------------------------
function ServerActivityWGCtrl:OnSCRADailyLeiChongRapidlyGiftInfo(protocol)
	local gift_info = self.server_activity_data:GetEveryDayRapidlyGiftInfo()
	if not IsEmptyTable(gift_info) then
		local can_get = self.server_activity_data:CanGetEveryDayRapidlyGiftFreeBox()
		if protocol.free_reward_flag == 1 and can_get then -- 1 已领取 0 未领取
			local reward_list = ServerActivityWGData.Instance:GetLeiChongOtherCfg("rapidly_item")
			if not IsEmptyTable(reward_list) then
				reward_list = ListIndexFromZeroToOne(reward_list)
				TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
			end
		end

		if gift_info.super_reward_flag and gift_info.super_reward_flag ~= protocol.super_reward_flag and protocol.super_reward_flag == 1 then
			local reward_list = ServerActivityWGData.Instance:GetLeiChongOtherCfg("gift_item")
			if not IsEmptyTable(reward_list) then
				reward_list = ListIndexFromZeroToOne(reward_list)
				TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
			end
		end

		local rapidly_reward_list = {}
		local old_rapidly_gift_flag = gift_info.rapidly_gift_flag
		local new_rapidly_gift_flag = bit:d2b_two(protocol.rapidly_gift_flag)
		local rapidly_cfg = ServerActivityWGData.Instance:GetRapidlyGiftCfg()
		if old_rapidly_gift_flag and new_rapidly_gift_flag and rapidly_cfg then
			for key, value in pairs(old_rapidly_gift_flag) do
				if value ~= new_rapidly_gift_flag[key] and new_rapidly_gift_flag[key] == 1 then
					if rapidly_cfg[key] then
						for k, item in pairs(rapidly_cfg[key].reward_item) do
							table.insert(rapidly_reward_list, item)
						end
					end
				end
			end

			if not IsEmptyTable(rapidly_reward_list) then
				TipWGCtrl.Instance:ShowGetReward(nil, rapidly_reward_list, nil, nil, nil, true)
			end
		end
	end

	self.server_activity_data:SetEveryDayRapidlyGiftInfo(protocol)
	self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_rapidly)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_rapidly)
	RemindManager.Instance:Fire(RemindName.DailyRecharge_Rapidly)
end

function ServerActivityWGCtrl:OnSCRapidlyGiftRecordInfo(protocol)
	self.server_activity_data:SetEveryDayRapidlyGiftRecordInfo(protocol)
	ServerActivityWGCtrl.Instance:OpenEveryDayRapidlyGiftRecordTip()
end

function ServerActivityWGCtrl:OpenEveryDayRapidlyGiftRecordTip()
	if self.everydayrecharge_record:IsOpen() then
		self.everydayrecharge_record:Flush()
	else
		self.everydayrecharge_record:Open()
	end
end
-----------------------每日累充-秒杀 end--------------------------

----------------------- 首充续充-----------------------------
function ServerActivityWGCtrl:GetShouChongShowFlag()
	return self.show_shouchong_icon
end

function ServerActivityWGCtrl:SetShouChongShowFlag(flag)
	self.show_shouchong_icon = flag
end

function ServerActivityWGCtrl:OnSCSCXCActInfo(protocol)
	self.server_activity_data:SetSCXCServerInfo(protocol)
	if self.firstrecharge_view:IsOpen() then
		self.firstrecharge_view:Flush(nil, "act_info")
	end
	RemindManager.Instance:Fire(RemindName.FirstCharge_Tab_ShouChong)
	MainuiWGCtrl.Instance:FlushView(0, "flush_ser_rec_tip")

	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.FIRST_CHONGZHI) then
		local sc_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.FIRST_CHONGZHI)
		local sc_act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.FIRST_CHONGZHI)
		if sc_btn and sc_act_cfg then
			sc_btn:Flush("SetSprite", {sc_act_cfg.res_name, sc_act_cfg.act_name_res, sc_act_cfg.bg_res})
		end
	end
end

--领取奖励
function ServerActivityWGCtrl:SendSCSCXCActReceive(param_1, param_2)
	-- print_error('SendSCSCXCActReceive----',param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(SCSCXCActReceive)
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

------------------------------------------- 全服捐献 -------------------------------
function ServerActivityWGCtrl:OnSCServerActQFJX(protocol)
	self.server_activity_data:SetQuanFuJuanXianInfo(protocol)
	self:FlushServerActTabView(0, "juanxian", {"juanxian"})
end

function ServerActivityWGCtrl:FlushServerActTabView(index, key, value)
	if self.serveract_tab_view and self.serveract_tab_view:IsOpen() then
		if index == 0 then
			index = self.serveract_tab_view:GetShowIndex()
		end
		self.serveract_tab_view:Flush(index, key, value)
		self:FlushServerHighPointRewardView()
		if self.xianmeng_zhegnba_view and self.xianmeng_zhegnba_view:IsOpen() then
			self.xianmeng_zhegnba_view:Flush()
		end
	elseif self.kf_activity_view and self.kf_activity_view:IsOpen() then
		if index == 0 then
			index = self.kf_activity_view:GetShowIndex()
		end
		if self.kf_activity_view:IsOpen() then
			self.kf_activity_view:Flush()
		end
		ViewManager.Instance:FlushView(GuideModuleName.KfActivityView, nil, key, value)
		--self.kf_activity_view:Flush(index, key, value)
	end

	if self.open_srver_reward_tips:IsOpen() then
		self.open_srver_reward_tips:Flush()
	end
end

function ServerActivityWGCtrl:OpenJXRewardTip(data)
	self.xj_reward_tip:Open()
	self.xj_reward_tip:SetData(data)
end

function ServerActivityWGCtrl:SendActivityRewardOp(activity_type, opera_type,param1, param2, param3)
 	--print_error(activity_type, opera_type,param1, param2, param3)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = activity_type
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param1 or 0
 	protocol.param_2 = param2 or 0
 	protocol.param_3 = param3 or 0
 	protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:OpenXunYuLuView()
	if self.xunyulu_view:IsOpen() then
		self.xunyulu_view:Flush()
	else
		self.xunyulu_view:Open()
	end
end

function ServerActivityWGCtrl:OnSCXunYuLuInfo(protocol)
	self.server_activity_data:SetXunYuLuData(protocol)
	if self.xunyulu_view:IsOpen() then
		self.xunyulu_view:Flush()
	end
end

function ServerActivityWGCtrl:OnSCActivitySettleNotice(protocol)
	
end

function ServerActivityWGCtrl:SetFirstRechargeMainUiIconVisible()
	self.server_activity_data:SetFirstRechargeMainUiIconVisible()
end

---[[ F2妖灵卖货
function ServerActivityWGCtrl:OnSCRABuddhaBagInfo(protocol)
	self.server_activity_data:SetLingYaoMaiHuoInfo(protocol)
	self.lingyaomaihuo_view:Flush()
	if ServerActivityWGData.Instance:GetLingYaoMaiHuoIsBuyAllGift() then
		ActivityWGData.Instance:ChangeActivityStatus(ACTIVITY_TYPE.RAND_ACT_LINGYAOMAIHUO, "status", ACTIVITY_STATUS.CLOSE, true)
	end
end
--]]

---[[ F2开服礼包
function ServerActivityWGCtrl:SendCSOgaTehuilibaoOpera(opera_type, param_0)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOgaTehuilibaoOpera)
	protocol.opera_type = opera_type or 0
	protocol.param_0 = param_0 or 0
	protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:OnSCOgaTehuilibaoInfo(protocol)
	if IS_AUDIT_VERSION then
		return
	end
	self:CheckSpecialRewardShowGet(protocol.gift_info_list)
	self.server_activity_data:SetActKfGiftInfoList(protocol)
	self:FlushServerActTabView(TabIndex.act_kf_gift, "kf_gift", {"kf_gift"})
	RemindManager.Instance:Fire(RemindName.KaiFuGiftRed)
end

function ServerActivityWGCtrl:CheckSpecialRewardShowGet(new_gift_list)
	local old_gift_list = self.server_activity_data:GetActKfGiftInfoList()
	if IsEmptyTable(old_gift_list) then
		return
	end

	local gift_cfg = nil
	for k,v in pairs(new_gift_list) do
		if old_gift_list[k] and old_gift_list[k].buy_num < v.buy_num then
			gift_cfg = self.server_activity_data:GetActKfGiftCfg(k)
			break
		end
	end

	if gift_cfg and gift_cfg.is_tuijian == 1 then
		local reward = SortTableKey(gift_cfg.reward)
		TipWGCtrl.Instance:ShowGetReward(nil, reward)
	end
end
--]]

---[[ F2开发比拼
function ServerActivityWGCtrl:OnSCOGARushRankInfo(protocol)
	self.server_activity_data:SetOpenServerCompetitonInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.MountLingChongView, nil, "bipin")
	self:FlushServerActTabView(TabIndex.act_bipin, "bipin", {"bipin"})
	RemindManager.Instance:Fire(RemindName.KFCompetition)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_Upstar)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_Upgrade)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Upgrade_Wing)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Upgrade_Mount)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Upgrade_LingChong)
end

function ServerActivityWGCtrl:SendCSOGARushRankOp(op_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOGARushRankOp)
	protocol.op_type = op_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol:EncodeAndSend()
end
--]]

---[[ 开服比拼主界面实时刷新显示排行数据
function ServerActivityWGCtrl:RuningRequesFlushBiPinRankInfo()
	if not ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.OPENSERVER_COMPETITION) then
		if self.mainui_bipin_rank_info_timer then
			GlobalTimerQuest:CancelQuest(self.mainui_bipin_rank_info_timer)
			self.mainui_bipin_rank_info_timer = nil
		end
		return
	end
	local is_show = self.server_activity_data:GetMainUiIsShowBiPinRank()
	if is_show then
		if not self.mainui_bipin_rank_info_timer then
			self.mainui_bipin_rank_info_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.RequesFlushBiPinRankInfo, self), 60)
		end
	elseif self.mainui_bipin_rank_info_timer then
		GlobalTimerQuest:CancelQuest(self.mainui_bipin_rank_info_timer)
		self.mainui_bipin_rank_info_timer = nil
	end
	self:FlushMainUiBiPinRankInfo()
end

function ServerActivityWGCtrl:RequesFlushBiPinRankInfo()
	local rank_cfg = self.server_activity_data:GetTodayBipinRankCfg()
    if not IsEmptyTable(rank_cfg) then
        for k, v in pairs(rank_cfg) do
            RankWGCtrl.Instance:SendActRankListReq(v.rank_type)
        end
	end
end

function ServerActivityWGCtrl:FlushMainUiBiPinRankInfo()
	local is_show = self.server_activity_data:GetMainUiIsShowBiPinRank()
	local bipin_act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.KF_ACTIVITY)
	if bipin_act_btn then
		if is_show then
            local rank_cfg = self.server_activity_data:GetTodayBipinRankCfg()
            local data_info = {}
            for k, v in pairs(rank_cfg) do
                local rank_info = RankWGData.Instance:GetActMyRank(v.rank_type)
                if rank_info then
                    local my_rank = rank_info.my_rank or 0
					local rank_str = my_rank > 0 and ToColorStr(my_rank, "#3c8552") or ToColorStr(Language.OpenServer.NotRank, "#a93c3c")
                    -- local rank_label = string.format(Language.OpenServer.RankStr, v.ranking_title, rank_str)
					local rank_label = string.format(Language.OpenServer.RankStr, v.bubble_title, rank_str)
                    local data = {is_show = is_show, rank_label = rank_label}
                    data_info[#data_info + 1] = data
                end
            end
			if not IsEmptyTable(data_info) then
				bipin_act_btn:Flush("SetRankInfo", data_info)
			end
        else
            local data_info = {}
            local data = {is_show = is_show}
            data_info[#data_info + 1] = data
			bipin_act_btn:Flush("SetRankInfo", data_info)
		end
	end
end

function ServerActivityWGCtrl:FlushMainUiFirstRechargeTip()
	local is_show = self:IsMainUiFirstRechargeTipCanOpen()
	local bipin_act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.FIRST_CHONGZHI)

	if bipin_act_btn then
		if is_show then
			--bipin_act_btn:Flush("SetFirstRechargeTip")
			bipin_act_btn:SetFirstRechargeTip()
		else
			bipin_act_btn:SetFirstRechargeTipClose()
		end
	end
end

function ServerActivityWGCtrl:MainUIButtonCreate(act_type, status)
	if act_type == ACTIVITY_TYPE.FIRST_CHONGZHI and status then
		self:FlushMainUiFirstRechargeTip()
	end

	-- local bipin_act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.FIRST_CHONGZHI)
	-- if bipin_act_btn and bipin_act_btn.node_list then
	-- 	bipin_act_btn.node_list.ActivityButtonView.transform:SetSiblingIndex(1)
	-- end
end

function ServerActivityWGCtrl:IsMainUiFirstRechargeTipCanOpen()
	-- 配置开关控制
	if not MainuiWGData.Instance:GetFirstRechargeTimeTipState() then
		return false
	end

	---[[ 在线30分钟内显示
	local end_time = ServerActivityWGData.Instance:GetSCTipsEndTime()
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	if now_time > end_time then
		return false
	end
	--]]

	local scene_type = Scene.Instance:GetSceneType()					-- 普通场景才显示
	local recharge_num = RechargeWGData.Instance:GetHistoryRecharge()		-- 充过钱数
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.ShouChong)	-- 首充是否开启
	local all_rec = ServerActivityWGData.Instance:IsAllGradeRecharge()	-- 所有首充档位都充值过
	local has_remind = ServerActivityWGData.Instance:GetSCXCRemind()		-- 有可以领的奖励
	if IS_AUDIT_VERSION or scene_type ~= SceneType.Common or (recharge_num > 0 and is_open) or (all_rec and not is_open and has_remind == 0) then
		return false
	end

	return true
end
--]]

-- 开服BOSS首杀积分榜
function ServerActivityWGCtrl:OpenOpenBossHunterRankView()
	self.openbosshunter_rank_view:Open()
end

---[[ 仙盟封榜活动称号弹提示
function ServerActivityWGCtrl:OnSCKaiZongLiPaiTitleChangeInfo(protocol)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local tip_limit_level = ServerActivityWGData.Instance:GetOpenSerActOtherCfg("xianmeng_title_tip_level")
	if tip_limit_level and role_level < tip_limit_level then
		return
	end
	if self.has_xianmeng_title and self.has_xianmeng_title > 0 and protocol.title_flag <= 0 then
		local act_name = Language.OpenServer.ZuiqiangXianmeng
		local is_forever = protocol.title_flag == -1
		local content = not is_forever and string.format(Language.OpenServer.ActTitleFailureTis, act_name) or string.format(Language.OpenServer.ActTitleFailureTis2, act_name)
		TipWGCtrl.Instance:OpenConfirmAlertTips(content, function ()
				if not is_forever then
					ViewManager.Instance:Open(GuideModuleName.QunXiongZhuLu, 0, "select_index", {select_index = 2})
				end
			end, not is_forever and Language.OpenServer.ActOkBtnName or Language.Common.BtnOK)
	end
	self.has_xianmeng_title = protocol.title_flag
end
--]]

---[[ 开服集字合成多个滑动选择
function ServerActivityWGCtrl:OpenActSliderNumView(info_list)
	self.act_slider_num_view:Open()
	self.act_slider_num_view:Flush(0, "info_list", info_list)
end

function ServerActivityWGCtrl:CloseActSliderNumView()
	self.act_slider_num_view:Close()
end
--]]

----[[每日累充(原首充)--直购返利
function ServerActivityWGCtrl:SendSCZhiGouOpera(opera_type, param1, param2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRoleZhiChonReq)
    protocol.opera_type = opera_type
    protocol.param_1 = param1 or 0
    protocol.param_2 = param2 or 0
    protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:OnSCActZhiChongInfo(protocol)
    self.everyday_recharge_rebate_data:OnEDActZhiChongInfo(protocol)
    self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_zhigou)
    RemindManager.Instance:Fire(RemindName.DailyRecharge_ZhiGou)
end
--]]

----[[每日累充--连续充值
function ServerActivityWGCtrl:SendLianXuChongZhiReq(btn_type, reward_type, reward_index)
	-- print_error("FFFF====== 每日累充--连续充值 SendLianXuChongZhiReq", btn_type, reward_type, reward_index)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRoleLianXuChonZhiReq)
    protocol.opera_type = 1
    protocol.param_1 = btn_type or 0
    protocol.param_2 = reward_type or 0
    protocol.param_3 = reward_index or 0
    protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:OnSCLianXuChongZhiInfo(protocol)
    self.everyday_lianxu_rechage_data:SaveData(protocol)
    self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_lianchong)
    RemindManager.Instance:Fire(RemindName.DailyRecharge_LianChong)
end
--]]

-- 开服比拼冲榜提示界面
function ServerActivityWGCtrl:OpenKfBinPinTipView(rush_type)
    if rush_type and rush_type > 0 then
        self.kf_bipin_tip_view:SetBiPinRushType(rush_type)
        self.kf_bipin_tip_view:Open()
    end
end

function ServerActivityWGCtrl:ShowActRewardList(title, reward_list)
	if self.act_reward_list_show_view then
		self.act_reward_list_show_view:SetData(title, reward_list)

		if not self.act_reward_list_show_view:IsOpen() then
			self.act_reward_list_show_view:Open()
		end
	end
end

---------------印记转盘协议 --------------

function ServerActivityWGCtrl:OnSCSYinJiZhuanPanInfo(protocol) --转盘信息
	--print_error("转盘信息",protocol)
	self.everyday_yinji_turntable_data:OnSCSYinJiZhuanPanInfo(protocol)
	if self.everydayrecharge_view:IsOpen() then
		self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_yingji)
	end
	RemindManager.Instance:Fire(RemindName.DailyRecharge_YinJI)
end

function ServerActivityWGCtrl:OnSCYinJiZhuanPanDrawReward(protocol)
	--print_error("OnSCYinJiZhuanPanDrawReward",protocol)
	self.everyday_yinji_turntable_data:OnSCYinJiZhuanPanDrawReward(protocol)
	if self.everydayrecharge_view:IsOpen() then
		self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_yingji, "draw_reward",{true})
		--self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_yingji)
	end
	RemindManager.Instance:Fire(RemindName.DailyRecharge_YinJI)
end

function ServerActivityWGCtrl:OnSCYinJiZhuanPanDailyReward(protocol) --日常奖励物品id
	--print_error("日常奖励物品id",protocol)
	self.everyday_yinji_turntable_data:OnSCYinJiZhuanPanDailyReward(protocol)
	if self.everydayrecharge_view:IsOpen() then
		self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_yingji)
	end
	RemindManager.Instance:Fire(RemindName.DailyRecharge_YinJI)
end

function ServerActivityWGCtrl:OnSCYinJiZhuanPanLeiChouReward(protocol) -- 抽取次数奖励
	--print_error("抽取次数奖励",protocol)
	self.everyday_yinji_turntable_data:OnSCYinJiZhuanPanLeiChouReward(protocol)
	if self.everydayrecharge_view:IsOpen() then
		self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_yingji)
	end
	RemindManager.Instance:Fire(RemindName.DailyRecharge_YinJI)
end

function ServerActivityWGCtrl:OnSCYinJiZhuanPanNewServerLog(protocol) --新增服务器记录
	--print_error("新增服务器记录",protocol)
	self.everyday_yinji_turntable_data:OnSCYinJiZhuanPanNewServerLog(protocol)
	if self.everydayrecharge_view:IsOpen() then
		self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_yingji)
	end
	RemindManager.Instance:Fire(RemindName.DailyRecharge_YinJI)
end

function ServerActivityWGCtrl:OnSCYinJiZhuanPanDailyRecharge(protocol) -- 充值数量
	--print_error("充值数量",protocol)
	self.everyday_yinji_turntable_data:OnSCYinJiZhuanPanDailyRecharge(protocol)
	if self.everydayrecharge_view:IsOpen() then
		self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_yingji)
	end
	RemindManager.Instance:Fire(RemindName.DailyRecharge_YinJI)
end

function ServerActivityWGCtrl:OnSCYinJiZhuanPanDailyLoginReward(protocol)
	self.everyday_yinji_turntable_data:OnSCYinJiZhuanPanDailyLoginReward(protocol)
	if self.everydayrecharge_view:IsOpen() then
		self.everydayrecharge_view:Flush(TabIndex.everyday_recharge_yingji)
	end
	RemindManager.Instance:Fire(RemindName.DailyRecharge_YinJI)
end

function ServerActivityWGCtrl:SendYinJiTurnTableOpera(op_type,param1)
	--print_error("SendYinJiTurnTableOpera",op_type,param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOpYinJiZhuanPan)
	protocol.op_type = op_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:OpenYinJiTurnTableRecordView()
	if self.yinji_turntable_record then
		self.yinji_turntable_record:Open()
		self.yinji_turntable_record:Flush()
	end
end
--------------印记转盘协议end---------------------

-------------------- 比拼的直购、累充、云购、抽奖协议 start --------------------

function ServerActivityWGCtrl:OnSCOGAExtendTotalInfo(protocol)
	self.server_activity_data:SetOGAExtendAllInfo(protocol)
	if self.kf_activity_view:IsOpen() then
		self.kf_activity_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.KFCompetition)
end


function ServerActivityWGCtrl:OnSCOGAExtendRmbBuyInfoUpdate(protocol)
	self.server_activity_data:UpdateOGARmbBuyInfo(protocol)
	if self.kf_activity_view:IsOpen() then
		self.kf_activity_view:Flush()
	end
end


function ServerActivityWGCtrl:OnSCOGAExtendTaskProcessUpdate(protocol)
	self.server_activity_data:UpdateOGATaskProcessInfo(protocol)
	if self.kf_activity_view:IsOpen() then
		self.kf_activity_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.KFCompetition)
end


function ServerActivityWGCtrl:OnSCOGAExtendTaskFetchFlagUpdate(protocol)
	self.server_activity_data:UpdateOGATaskFetchFlagInfo(protocol)
	if self.kf_activity_view:IsOpen() then
		self.kf_activity_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.KFCompetition)
end


function ServerActivityWGCtrl:OnSCOGAExtendCumulateRechargeInfoUpdate(protocol)
	self.server_activity_data:UpdateOGACumulateRechargeInfo(protocol)
	if self.kf_activity_view:IsOpen() then
		self.kf_activity_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.KFCompetition)
end

-- -- 开服活动扩展 - 操作 OGA_EXTEND_OPERATE_TYPE_ENUM
function ServerActivityWGCtrl:SendOGAExtendReqOperate(operate_type, param1, param2)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSOGAExtendReqOperate)
	send_protocol.operate_type = operate_type
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol:EncodeAndSend()
end

-- -- 开服活动扩展 - 抽奖奖励
function ServerActivityWGCtrl:OnGetOGALotteryReward(result, param1)
	if result == 1 then
		self:FlushOGALotteryView(param1)
	end
end

-- -- 开服活动扩展 - 抽奖动画刷新
function ServerActivityWGCtrl:FlushOGALotteryView(index)
	if self.kf_activity_view:IsOpen() then
		local show_index = self.kf_activity_view:GetShowIndex()
		if show_index == TabIndex.act_equip_lottery then
			self.kf_activity_view:OGALotteryPlayRollAnim(index)
		end
	end
end

-------------------- 比拼的直购、累充、云购、抽奖协议  end  --------------------

-----------------开服集卡 start---------------------
-- 操作
function ServerActivityWGCtrl:SendAOCollectCardOpera(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_COLLECT_CARD
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

-- 请求信息
function ServerActivityWGCtrl:RequestAOCollectCardInfo()
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_TYPE_INFO)
end

-- 请求抽卡
function ServerActivityWGCtrl:RequestAOCollectCardDraw(mode)
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_DRAW, mode)
end

-- 请求领取任务奖励
function ServerActivityWGCtrl:RequestAOCollectCardTaskReward(task_id)
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_FETCH_TASK_REWARD, task_id)
end

-- 请求领取收集奖励
function ServerActivityWGCtrl:RequestAOCollectCardFetchReward(seq)
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_FETCH_REWARD, seq)
end

-- 请求合成卡片
function ServerActivityWGCtrl:RequestAOCollectCardCompose(seq, count)
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_COMPOSE, seq, count)
end

-- 向某人发送道具
function ServerActivityWGCtrl:RequestAOCollectCardSend(uid, seq)
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_SEND, uid, seq)
end

-- 向某人索要道具
function ServerActivityWGCtrl:RequestAOCollectCardGain(uid, seq)
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_GAIN, uid, seq)
end

-- 请求列表信息
function ServerActivityWGCtrl:RequestAOCollectCardTypeListInfo()
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_LIST_INFO)
end

-- 拒绝别人的索要
function ServerActivityWGCtrl:RequestAOCollectCardRefus(uid, seq)
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_REFUS, uid, seq)
end

-- 领取收集种类个数奖励
function ServerActivityWGCtrl:RequestAOCollectCardFetchCollectReward(seq)
	self:SendAOCollectCardOpera(ACTIVITY_COLLECT_CARD_TYPE.AO_COLLECT_CARD_OPERATE_TYPE_FETCH_COLLECT_REWARD, seq)
end

----集卡活动卡牌信息
function ServerActivityWGCtrl:OnSCAOCollectCardInfo(protocol)
	-- print_error("集卡活动卡牌信息", protocol)
	self.activity_collect_card_data:SetCollectCardInfo(protocol)
	self:FlushServerActTabView(TabIndex.server_collect_card, "flush_card")
	RemindManager.Instance:Fire(RemindName.CollectCardRed)
	RemindManager.Instance:Fire(RemindName.OpenServer)
end

----集卡活动任务信息
function ServerActivityWGCtrl:OnSCAOCollectCardTaskInfo(protocol)
	-- print_error("集卡活动任务信息", protocol)
	self.activity_collect_card_data:SetCollectCardTaskInfo(protocol)
	self:FlushCollectCardTaskView()
	self:FlushServerActTabView(TabIndex.server_collect_card, "card_task")
	RemindManager.Instance:Fire(RemindName.CollectCardRed)
	RemindManager.Instance:Fire(RemindName.OpenServer)
end

--集卡活动任务信息更新
function ServerActivityWGCtrl:OnSCAOCollectCardTaskUpdateInfo(protocol)
	-- print_error("集卡活动任务信息更新", protocol)
	self.activity_collect_card_data:SetCollectCardTaskUpdateInfo(protocol)
	self:FlushCollectCardTaskView()
	self:FlushServerActTabView(TabIndex.server_collect_card, "card_task")
	RemindManager.Instance:Fire(RemindName.CollectCardRed)
	RemindManager.Instance:Fire(RemindName.OpenServer)
end

--集卡活动索取信息
function ServerActivityWGCtrl:OnSCAOCollectCardSelfInfo(protocol)
	-- print_error("集卡活动索取信息", protocol)
	self.activity_collect_card_data:SetCollectCardSelfInfo(protocol)
	self:FlushCollectCardOperateView()
	self:FlushServerActTabView(TabIndex.server_collect_card, "card_self")
end

--集卡活动索取信息更新
function ServerActivityWGCtrl:OnSCAOCollectCardUpdateInfo(protocol)
	-- print_error("集卡活动索取信息更新", protocol)
	self.activity_collect_card_data:SetCollectCardInfoUpdateInfo(protocol)
	self:FlushCollectCardOperateView()
	self:FlushServerActTabView(TabIndex.server_collect_card, "card_self")
end

--集卡活动朋友索要信息
function ServerActivityWGCtrl:OnSCAOCollectCardFriendInfo(protocol)
	-- print_error("集卡活动朋友索要信息", protocol)
	self.activity_collect_card_data:SetCollectCardFriendInfo(protocol)
	self:FlushCollectCardOperateView()
	self:FlushServerActTabView(TabIndex.server_collect_card, "card_friend")
end

--集卡活动抽卡记录
function ServerActivityWGCtrl:OnSCAOCollectCardRecordInfo(protocol)
	self.activity_collect_card_data:SetCollectCardRecordInfo(protocol)
end

--集卡活动抽卡更新
function ServerActivityWGCtrl:OnSCAOCollectCardRecordUpdateInfo(protocol)
	self.activity_collect_card_data:SetCollectCardRecordUpdateInfo(protocol)
end

--集卡活动赠送反馈
function ServerActivityWGCtrl:OnSCAOCollectCardSendCardBack(protocol)
	if protocol.result == 1 then --成功
		local cfg = ActivityCollectCardWGData.Instance:GetCardBySeq(protocol.param2)
		local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item_id or 0)
		if item_cfg then
			-- 新获得提示
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.OpenServer.SevenCollectCardSendSuc, string.format("%sx%s", item_name, 1))
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end
end

--集卡活动打开任务
function ServerActivityWGCtrl:OpenCollectCardTaskView()
	if not self.act_collect_card_task_view:IsOpen() then
		self.act_collect_card_task_view:Open()
	else
		self.act_collect_card_task_view:Flush()
	end
end

--集卡活动刷新任务
function ServerActivityWGCtrl:FlushCollectCardTaskView()
	if self.act_collect_card_task_view:IsOpen() then
		self.act_collect_card_task_view:Flush()
	end
end

--集卡活动打开索取
function ServerActivityWGCtrl:OpenCollectCardOperateView(operate_seq)
	self.act_collect_card_operate_view:SetCurrOperateSeq(operate_seq)
	if not self.act_collect_card_operate_view:IsOpen() then
		self.act_collect_card_operate_view:Open()
	else
		self.act_collect_card_operate_view:Flush()
	end
end


--集卡活动刷新索取
function ServerActivityWGCtrl:FlushCollectCardOperateView()
	if self.act_collect_card_operate_view:IsOpen() then
		self.act_collect_card_operate_view:Flush()
	end
end
-----------------开服集卡 end---------------------

function ServerActivityWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE or change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then
		if change_item_id == self.server_activity_data:GetOGADrawCostItemId() then
			if self.kf_activity_view:IsOpen() then
				self.kf_activity_view:Flush()
			end
			RemindManager.Instance:Fire(RemindName.KFCompetition)
		elseif ActivityCollectCardWGData.Instance:IsCollectCardChipItemId(change_item_id) then
			if self.serveract_tab_view:IsOpen() then
				self.serveract_tab_view:CollectCardChipItemChange()
			end
		end
	end
end

function ServerActivityWGCtrl:OpenKFRewardTips(rush_type)
	self.open_srver_reward_tips:Flush(0, "all", {rush_type = rush_type})
	if not self.open_srver_reward_tips:IsOpen() then
		self.open_srver_reward_tips:Open()
	end
end