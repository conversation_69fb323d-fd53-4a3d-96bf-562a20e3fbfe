﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using CharacterShadows;

namespace Nirvana
{
    using System;
    using UnityEngine;

    /// <summary>
    /// The quality config for a quality level.
    /// </summary>
    [Serializable]
    public sealed class QualityLevel
    {
        [SerializeField]
        private string name;

        [SerializeField]
        [Tooltip("Number of pixel lights to use.")]
        private int pixelLightCount = 1;
        
        [SerializeField]
        [Tooltip("Screen Resolution")]
        private int screenResolution = 1080;

        [SerializeField]
        [Tooltip("Base texture level.")]
        [EnumInt("Full Res", 0)]
        [EnumInt("Half Res", 1)]
        [EnumInt("Quarter Res", 2)]
        [EnumInt("Eighth Res", 3)]
        private int masterTextureLimit = 0;

        [SerializeField]
        [Tooltip("When to enable anisotropic texturing.")]
        private AnisotropicFiltering anisotropicFiltering =
            AnisotropicFiltering.Enable;

        [SerializeField]
        [Tooltip("Screen anti aliasing.")]
        [EnumInt("Disabled", 0)]
        [EnumInt("2x Multi Sampling", 2)]
        [EnumInt("4x Multi Sampling", 4)]
        [EnumInt("8x Multi Sampling", 8)]
        private int antiAliasing = 0;

        [SerializeField]
        [Tooltip("Use soft blending for particles?")]
        private bool softParticles = false;

        [SerializeField]
        [Tooltip("Use soft blending for vegetation when using terrain?")]
        private bool softVegetation = false;

        [SerializeField]
        [Tooltip("Allow real-time rendering of Reflection Probes?")]
        private bool realtimeReflectionProbes = false;

        [SerializeField]
        [Tooltip("Make billboards face towards camera position. Otherwise they face towards camera plane. This makes billboards look nicer when camera rotates but is more expensive to render.")]
        private bool billboardsFaceCameraPosition = true;
        
        [SerializeField]
        [Tooltip("Shadow resolution")]
        private ShadowResolution shadowResolution = ShadowResolution.VeryHigh;
        [SerializeField]
        [Tooltip("Shadow count limit")]
        [Range(0, 16)]
        private int shadowCountLimit = 16;
        [SerializeField]
        [Tooltip("Shadow soft radius")]
        private float shadowSoftRadius = 0.02f;
        [SerializeField]
        [Tooltip("Shadow soft Iteration")]
        [Range(1, 12)]
        private int shadowSoftIteration = 4;
        [SerializeField]
        [Tooltip("Shadow threshold")]
        [Range(0, 5)]
        private int shadowThreshold = 2;
        [SerializeField]
        [Tooltip("Shadow distance.")]
        private float shadowDistance = 20;
        
        [SerializeField]
        [Tooltip("Bone count for mesh skinning.")]
        private SkinWeights blendWeights = SkinWeights.FourBones;

        [SerializeField]
        [Tooltip("Limit refresh rate to avoid tearing.")]
        [EnumInt("Don't Sync", 0)]
        [EnumInt("Every V Blank", 1)]
        [EnumInt("Every Second V Black", 2)]
        private int vSyncCount = 1;

        [SerializeField]
        private float lodBias = 1;

        [SerializeField]
        private int maximumLODLevel = 0;

        [SerializeField]
        [Tooltip("Number of rays to cast for approximate world collisions.")]
        private int particleRaycastBudget = 4096;

        [SerializeField]
        private int maxQueuedFrames = 2;

        [SerializeField]
        [Tooltip("Async Upload TimeSlice in Milliseconds.")]
        private int asyncUploadTimeSlice = 2;

        [SerializeField]
        [Tooltip("Async Upload Ring Buffer Size in MB.")]
        private int asyncUploadBufferSize = 4;

        [SerializeField]
        private int shaderMaximumLOD = 0;

        public static bool ShadowDisabled = false;

        public static int ScreenResolutionLimitForced = 1080;
        private static int originalScreenWidth;
        private static int originalScreenHeight;

        /// <summary>
        /// Gets the name of this quality level.
        /// </summary>
        public string Name
        {
            get { return this.name; }
        }

        /// <summary>
        /// Active this quality config.
        /// </summary>
        public void Active()
        {
            // Rendering
            QualitySettings.pixelLightCount = this.pixelLightCount;
            QualitySettings.masterTextureLimit = this.masterTextureLimit;
            QualitySettings.anisotropicFiltering = this.anisotropicFiltering;
            QualitySettings.antiAliasing = this.antiAliasing;
            QualitySettings.softParticles = this.softParticles;
            QualitySettings.softVegetation = this.softVegetation;
            QualitySettings.realtimeReflectionProbes =
                this.realtimeReflectionProbes;
            QualitySettings.billboardsFaceCameraPosition =
                this.billboardsFaceCameraPosition;

            // Shadows
            CharacterShadowFeature feature = CharacterShadowManager.Instance?.Feature;
            if (feature)
            {
                CharacterShadowManager.Instance.gameObject.SetActive(!ShadowDisabled);
                int resolution;
                switch (shadowResolution)
                {
                    case ShadowResolution.VeryHigh:
                        resolution = 2048;
                        break;
                    case ShadowResolution.High:
                        resolution = 1024;
                        break;
                    case ShadowResolution.Medium:
                        resolution = 512;
                        break;
                    default:
                        resolution = 256;
                        break;
                }

                feature.shadowDecalSize = resolution;
                feature.maxAvailableShadowCount = shadowCountLimit;
                feature.softRadius = shadowSoftRadius;
                feature.availableThreshold = shadowThreshold;
                feature.MaxDrawDistance = shadowDistance;
                feature.softIteration = shadowSoftIteration;
            }

            // Other
            QualitySettings.skinWeights = this.blendWeights;
            QualitySettings.vSyncCount = this.vSyncCount;
            QualitySettings.lodBias = this.lodBias;
            QualitySettings.maximumLODLevel = this.maximumLODLevel;
            QualitySettings.particleRaycastBudget = this.particleRaycastBudget;
            QualitySettings.maxQueuedFrames = this.maxQueuedFrames;
            QualitySettings.asyncUploadBufferSize = this.asyncUploadBufferSize;
            QualitySettings.asyncUploadTimeSlice = this.asyncUploadTimeSlice;

            // Shader
            Shader.globalMaximumLOD = shaderMaximumLOD;
            
            //Resolution
            SetScreenResolution(screenResolution);
        }

        private void SetScreenResolution(int res)
        {
            res = Mathf.Min(ScreenResolutionLimitForced, res);
            
            if (originalScreenWidth == 0)
                originalScreenWidth = Screen.width;
            if (originalScreenHeight == 0)
                originalScreenHeight = Screen.height;

            if (originalScreenWidth <= 0 || originalScreenHeight <= 0)
                return;

            var isFullscreen = UnityEngine.Application.platform != UnityEngine.RuntimePlatform.WindowsPlayer;
            if (originalScreenWidth > originalScreenHeight)
            {
                if (originalScreenHeight > res)
                {
                    var radio = (float)originalScreenWidth / originalScreenHeight;
                    Screen.SetResolution(Mathf.FloorToInt(res * radio), res, isFullscreen);
                }
                else
                    Screen.SetResolution(originalScreenWidth, originalScreenHeight, isFullscreen);
            }
            else
            {
                if (originalScreenWidth > res)
                {
                    var radio = (float)originalScreenWidth / originalScreenHeight;
                    Screen.SetResolution(res, Mathf.FloorToInt(res * radio), isFullscreen);
                }
                else
                {
                    Screen.SetResolution(originalScreenWidth, originalScreenHeight, isFullscreen);
                }
            }
        }
    }
}
