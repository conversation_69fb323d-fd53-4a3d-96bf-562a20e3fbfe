TablePool = TablePool or {}
local auto_die_cache = {}
local died_cache = {}
local living_cache = {}

function TablePool.Get(is_living)
	local t = next(died_cache)
	if nil ~= t then
		died_cache[t] = nil
	else
		t = {}
	end

	if is_living then
		living_cache[t] = t
	else
		auto_die_cache[t] = t
	end

	return t
end

function TablePool.Release(t)
	if nil ~= t and nil ~= living_cache[t] then
		died_cache[t] = t
		living_cache[t] = nil
	end
end

function TablePool.Update(now_time, elapse_time)
	for k,v in pairs(auto_die_cache) do
		died_cache[k] = v
		auto_die_cache[k] = nil
	end
end
