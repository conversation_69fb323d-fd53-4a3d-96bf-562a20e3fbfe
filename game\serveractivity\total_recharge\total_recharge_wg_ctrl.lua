require("game/serveractivity/total_recharge/total_recharge_wg_data")

--累计充值
TotalChargeWGCtrl = TotalChargeWGCtrl or BaseClass(BaseWGCtrl)

function TotalChargeWGCtrl:__init()
	if TotalChargeWGCtrl.Instance ~= nil then
		print("[TotalChargeWGCtrl]error:create a singleton twice")
	end
	TotalChargeWGCtrl.Instance = self

	self.data = TotalChargeWGData.New()

	self:RegisterAllProtocols()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	-- Remind.Instance:RegisterOneRemind(RemindId.act_hall_total_recharge, BindTool.Bind1(self.CheckTotalCharge, self))
end

function TotalChargeWGCtrl:__delete()

	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	TotalChargeWGCtrl.Instance = nil
end

function TotalChargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRATotalChargeInfo, "OnRATotalChargeInfo")						--随机累计充值
end

function TotalChargeWGCtrl:OnRATotalChargeInfo(protocol)
	self.data:SetRATotalChargeDayInfo(protocol)
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
end

function TotalChargeWGCtrl:CheckTotalCharge()
	return self.data:RemindTotalCharge()
end

function TotalChargeWGCtrl:MainuiOpenCreate()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_TOTAL_CHONGZHI) then
		local param_t = {
			rand_activity_type = ACTIVITY_TYPE.RAND_TOTAL_CHONGZHI,
			opera_type = RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE.RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_QUERY_INFO,
		}
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	end
end

