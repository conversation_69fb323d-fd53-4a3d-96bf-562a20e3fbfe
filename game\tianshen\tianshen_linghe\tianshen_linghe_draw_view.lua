local tween_lhbg_scale_startpos = Vector3(0.9, 0.9, 0.9)
local tween_lhbg_scale_endpos = Vector3(1.15, 1.15, 1.15)
local tween_lhbg_rotatepos = Vector3(0, 0, 360)
local tween_lhbg2_rotatepos = Vector3(0, 0, -360)

TianShenLingHeDrawView = TianShenLingHeDrawView or BaseClass(SafeBaseView)

function TianShenLingHeDrawView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(true)
    self.view_name = GuideModuleName.TianShenLingHeDrawView
    self:AddViewResource(0, "uis/view/tianshen_linghe_ui_prefab", "layout_linghe_draw_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function TianShenLingHeDrawView:ReleaseCallBack()
    if self.linghe_fanli_list then
        self.linghe_fanli_list:DeleteMe()
        self.linghe_fanli_list = nil
    end

    if self.show_model then
		self.show_model:DeleteMe()
		self.show_model = nil
	end

    -- if self.linghe_show_cell then
    --     for k, v in pairs(self.linghe_show_cell) do
    --         v:DeleteMe()
    --     end
    --     self.linghe_show_cell = nil
    -- end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.linghe_item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.linghe_item_data_change)
        self.linghe_item_data_change = nil
    end

    if self.linghe_shop_count_down and CountDownManager.Instance:HasCountDown(self.linghe_shop_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.linghe_shop_count_down)
        self.linghe_shop_count_down = nil
	end

 --    if self.remind_change then
	-- 	RemindManager.Instance:UnBind(self.remind_change)
	--     self.remind_change = nil
	-- end
    -- self.is_play_niudan_ani = false

    -- if self.tween_lh_bg then
    --     self.tween_lh_bg:Kill()
    --     self.tween_lh_bg = nil
    -- end

    -- if self.tween_lh_bg2 then
    --     self.tween_lh_bg2:Kill()
    --     self.tween_lh_bg2 = nil
    -- end

    -- if self.tween_lh_bg3 then
    --     self.tween_lh_bg3:Kill()
    --     self.tween_lh_bg3 = nil
    -- end
end

function TianShenLingHeDrawView:LoadCallBack()
	self.linghe_shop_count_down = "linghe_shop_count_down"
    if nil == self.linghe_fanli_list then
		--local bundle, asset = "uis/view/tianshen_linghe_ui_prefab", "linghe_draw_fanli_cell"
		-- self.linghe_fanli_list = AsyncBaseGrid.New()
		-- self.linghe_fanli_list:CreateCells({col = 1, itemRender = TianShenLingHeDrawCell,
		-- 	list_view = self.node_list.linghe_fanli_list, assetBundle = bundle, assetName = asset, change_cells_num = 1})
        self.linghe_fanli_list = AsyncListView.New(TianShenLingHeDrawCell, self.node_list.linghe_fanli_list)
		self.linghe_fanli_list:SetStartZeroIndex(false)
	end

    if not self.show_model then
		self.show_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["model_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		self.show_model:SetRenderTexUI3DModel(display_data)
	end

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

    -- 绑定红点
	-- self.remind_change = BindTool.Bind(self.LingHeRemindCallBack, self)
	-- RemindManager.Instance:Bind(self.remind_change, RemindName.TianShenLingHe)

    for i = 1, 2 do
        self.node_list["linghe_btn_draw_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickLHRecord, self, i))
        self.node_list["linghe_btn_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowDrawItemTips, self, i))
    end

	self.node_list["linghe_btn_reward_show"].button:AddClickListener(BindTool.Bind1(self.OnClickRewardShow, self)) --奖励展示
	--self.node_list["linghe_btn_reward_record"].button:AddClickListener(BindTool.Bind1(self.OnClickRewardRecord, self)) --抽奖记录
	self.node_list["btn_goto_linghe"].button:AddClickListener(BindTool.Bind1(self.OnClickGotoLingHe, self)) --前往灵核
	self.node_list["linghe_btn_range"].button:AddClickListener(BindTool.Bind1(self.OnClickOpenRange, self)) --抽奖概率

    self.linghe_item_data_change = BindTool.Bind(self.OnDrawItemChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.linghe_item_data_change)

    -- if nil == self.linghe_show_cell then
    --     self.linghe_show_cell = {}
    --     for i = 1, 8 do
    --         self.linghe_show_cell[i] = ItemCell.New(self.node_list["item_cell" .. i])
    --     end
    -- end
    
    self:TianShenLingHeAnimation()
    self.node_list.title_view_name.text.text = Language.TianShenLingHe.TitleName
    self:FlushModel()
end

function TianShenLingHeDrawView:CloseCallBack()
    self.is_anim_playing = false
    self:ClearTimer()
end

function TianShenLingHeDrawView:ClearTimer()
	if self.anim_timer then
		GlobalTimerQuest.CancelQuest(self.anim_timer)
		self.anim_timer = nil
	end
end


function TianShenLingHeDrawView:OnFlush(param)
    self:FlushDrawViewShow()
    self:LoginTimeCountDown()
end

function TianShenLingHeDrawView:FlushModel()
    if self.show_model then
        local bundle, asset = ResPath.GetOtherUIModel("13")
        self.show_model:SetMainAsset(bundle, asset)
        self.show_model:SetRTAdjustmentRootLocalScale(0.2)
        self.show_model:SetRTAdjustmentRootLocalPosition(0, -1.22, 0)
    end
end

function TianShenLingHeDrawView:OnClickLHRecord(draw_type) --抽奖
	if self.is_anim_playing then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenBaoXia.BaoXia_Anim_Is_Playing)
		return
	end

    local cfg = TianShenLingHeWGData.Instance:GetDrawConsumeCfg()
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.stuff_id)

    local send_reward_fun = function()
        self.is_anim_playing = false
        TianShenLingHeWGData.Instance:CacheOrGetDrawIndex(draw_type)
        TianShenLingHeWGCtrl.Instance:SendLingHeDrawReq(TianShenLingHeWGData.DRAW_TYPE.LUCK_DRAW, cfg.mode)
    end

    if num >= cfg.stuff_num then
        self.is_anim_playing = true
        local bundle, asset = ResPath.GetEffectUi("UI_tdww_choujiang")
        EffectManager.Instance:PlaySingleAtTransform(bundle, asset, self.node_list["effect_root"].transform, 3, nil, nil, Vector3(1, 1, 1))
        self.anim_timer = GlobalTimerQuest:AddDelayTimer(send_reward_fun, 3)
    else
        TianShenLingHeWGCtrl.Instance:ClickUseLingHeDrawItem(draw_type, function ()
            self:OnClickDrawBuy(draw_type)
        end)
    end
end

--点击购买
function TianShenLingHeDrawView:OnClickDrawBuy(draw_type)
    local cfg = TianShenLingHeWGData.Instance:GetDrawConsumeCfg()
    local cur_cfg = cfg[draw_type]
    if cur_cfg == nil then
        return
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.stuff_id)
    local consume = (cur_cfg.cost_gold / cur_cfg.stuff_num) * (cur_cfg.stuff_num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)

    local send_reward_fun = function()
        self.is_anim_playing = false
        TianShenLingHeWGData.Instance:CacheOrGetDrawIndex(draw_type)
        TianShenLingHeWGCtrl.Instance:SendLingHeDrawReq(TianShenLingHeWGData.DRAW_TYPE.LUCK_DRAW, cur_cfg.mode)
    end

	--足够购买，不足弹窗
    if enough then
        self.is_anim_playing = true
        local bundle, asset = ResPath.GetEffectUi("UI_tdww_choujiang")
        EffectManager.Instance:PlaySingleAtTransform(bundle, asset, self.node_list["effect_root"].transform, 3, nil, nil, Vector3(1, 1, 1))
        self.anim_timer = GlobalTimerQuest:AddDelayTimer(send_reward_fun, 3)
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

--物品监听刷新按钮显示
function TianShenLingHeDrawView:OnDrawItemChange(item_id)
    local check_list = TianShenLingHeWGData.Instance:GetItemDataChangeList()
    if check_list ~= nil then
        for i, v in pairs(check_list) do
            if v == item_id then
                self:FlushDrawBtnShow(true)
                return
            end
        end
    end
end

function TianShenLingHeDrawView:LingHeRemindCallBack(remind_name, num)
	if remind_name == RemindName.TianShenLingHe then
		self.node_list["goto_linghe_red"]:SetActive(num > 0)
	end
end

function TianShenLingHeDrawView:OnClickRewardShow() --奖励展示
    TianShenLingHeWGCtrl.Instance:OpenLingHeLibrayView()
end

function TianShenLingHeDrawView:OnClickRewardRecord() --抽奖记录
    TianShenLingHeWGCtrl.Instance:OpenLingHeRecordView()
end

function TianShenLingHeDrawView:OnClickOpenRange() --抽奖概率
    TianShenLingHeWGCtrl.Instance:OpenLingHeGailvView()
end

function TianShenLingHeDrawView:OnClickGotoLingHe() --前往灵核
    ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_linghe_uplevel)
end

----------------------------------------------------商店开启时间倒计时--------------------------------------
function TianShenLingHeDrawView:LoginTimeCountDown()
    local open_time, end_time, state = TianShenLingHeWGData.Instance:GetLingHeShopOpenTime()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    -- print_error("open_time", TimeUtil.FormatYMDHM(open_time))
    -- print_error("server_time",TimeUtil.FormatYMDHM(server_time))
    -- print_error("end_time",TimeUtil.FormatYMDHM(end_time))
    -- print_error("state",state)
    self.node_list["fanli_time_root"]:SetActive(state ~= 0)
    if state == 0 then --活动未开启
        if open_time ~= nil then
            local time = open_time - server_time
            if time > 0 then
                if CountDownManager.Instance:HasCountDown(self.linghe_shop_count_down) then
                    CountDownManager.Instance:RemoveCountDown(self.linghe_shop_count_down)
                end

                CountDownManager.Instance:AddCountDown(self.linghe_shop_count_down,
                                                        BindTool.Bind1(self.UpdateCountDown, self),
                                                        BindTool.Bind1(self.OnComplete, self),
                                                        nil, time, 1)
            end
        end
        
    else --活动开启
        if end_time ~= nil then
            local time = end_time - server_time
            if time > 0 then
                if CountDownManager.Instance:HasCountDown(self.linghe_shop_count_down) then
                    CountDownManager.Instance:RemoveCountDown(self.linghe_shop_count_down)
                end
 
                CountDownManager.Instance:AddCountDown(self.linghe_shop_count_down,
                                                        BindTool.Bind1(self.UpdateFanLiCountDown, self),
                                                        BindTool.Bind1(self.FanLiOnComplete, self),
                                                        nil, time, 1)
            end
        end
        
    end

end

function TianShenLingHeDrawView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.shop_time_label.text.text = TimeUtil.FormatSecondDHM8(valid_time)
	end
end

function TianShenLingHeDrawView:OnComplete()
	self.node_list.shop_time_label.text.text = ""
end

function TianShenLingHeDrawView:UpdateFanLiCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.linghe_fanli_time.text.text = TimeUtil.FormatSecondDHM8(valid_time)
	end
end

function TianShenLingHeDrawView:FanLiOnComplete()
	self.node_list.linghe_fanli_time.text.text = ""
end

-------------------------------------------------------------------------------------------------------------

function TianShenLingHeDrawView:FlushDrawViewShow()
    self:FlushDrawBtnShow()
    -- local big_show_list = TianShenLingHeWGData.Instance:GetBigShowCfg()
    -- for k, v in pairs(big_show_list) do
    --     self.linghe_show_cell[k]:SetIsUseRoundQualityBg(true)
    --     self.linghe_show_cell[k]:SetData({item_id = v.show_best_item})
    --     self.linghe_show_cell[k]:SetCellBgEnabled(false)
    --     self.linghe_show_cell[k]:SetEffectRootEnable(false)
    -- end

    local state = TianShenLingHeWGData.Instance:GetLingHeShopOpenState()
    self.node_list["linghe_shop_ison"]:SetActive(state == 0) --活动未开启
    self.node_list["linghe_fanli_list"]:SetActive(state ~= 0)
     self.node_list.list_bg:SetActive(state ~= 0)
    if state ~= 0 then --活动开启才显示
        local shop_item_list = TianShenLingHeWGData.Instance:GetDrawShopListCfg()
        if shop_item_list ~= nil then
            self.linghe_fanli_list:SetDataList(shop_item_list)
        end
    end
end

function TianShenLingHeDrawView:FlushDrawBtnShow(is_flush_num) --刷新抽奖次数
    local cfg = TianShenLingHeWGData.Instance:GetDrawConsumeCfg()
    if cfg == nil then
        return
    end

    local item_cfg
    local count, asset
    for i = 1, 2 do
        if cfg[i] then
            local item_id = cfg[i].stuff_id
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not IsEmptyTable(item_cfg) then
                    --道具图标
                    self.node_list["linghe_btn_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
                end
                --按钮价格
                self.node_list["linghe_txt_buy_" .. i].text.text = string.format(Language.TianShenLingHe.DrawBtnDesc, cfg[i].count)
                -- --折扣
                local is_zhekou = cfg[i].count ~= cfg[i].stuff_num
                self.node_list["btn_discount_" .. i]:SetActive(is_zhekou)
                if is_zhekou then
                    self.node_list["txt_lh_discount_" .. i].text.text = string.format(Language.TianShenLingHe.Discount, NumberToChinaNumber(cfg[i].stuff_num))
                end
            end
            --道具红点数量
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["linghe_btn_red_" .. i]:SetActive(count >= cfg[i].stuff_num)
            local color = count >= cfg[i].stuff_num and COLOR3B.GREEN or COLOR3B.RED
            local left_str = ToColorStr(count, color) 
            self.node_list["linghe_btn_num_" .. i].text.text = left_str .."/".. cfg[i].stuff_num
            self.node_list["linghe_btn_num_"..i].text.color = Str2C3b(color)
        end
    end
end

function TianShenLingHeDrawView:ShowDrawItemTips(item_type)
    local cfg = TianShenLingHeWGData.Instance:GetDrawConsumeCfg()
    local item_id = cfg[item_type].stuff_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function TianShenLingHeDrawView:TianShenLingHeAnimation()
    -- if self.tween_lh_bg3 then
    --     self.tween_lh_bg3:Restart()
    -- else
    --     self.tween_lh_bg3 = self.node_list.lh_bg3.transform:DORotate(tween_lhbg_rotatepos, 60, DG.Tweening.RotateMode.FastBeyond360)
    --     self.tween_lh_bg3:SetEase(DG.Tweening.Ease.Linear)
    --     self.tween_lh_bg3:SetLoops(-1)
    -- end

    -- if self.tween_lh_bg then
    --     self.tween_lh_bg:Restart()
    -- else
    --     self.tween_lh_bg = self.node_list.lh_bg.transform:DORotate(tween_lhbg_rotatepos, 6, DG.Tweening.RotateMode.FastBeyond360)
    --     self.tween_lh_bg:SetEase(DG.Tweening.Ease.Linear)
    --     self.tween_lh_bg:SetLoops(-1)
    -- end

    -- if self.tween_lh_bg2 then
    --     self.tween_lh_bg2:Restart()
    -- else
    --     self.tween_lh_bg2 = self.node_list.lh_bg2.transform:DORotate(tween_lhbg2_rotatepos, 8, DG.Tweening.RotateMode.FastBeyond360)
    --     self.tween_lh_bg2:SetEase(DG.Tweening.Ease.Linear)
    --     self.tween_lh_bg2:SetLoops(-1)
    -- end
end
TianShenLingHeDrawCell = TianShenLingHeDrawCell or BaseClass(BaseRender)
function TianShenLingHeDrawCell:__init()
    self.price_type = nil
end

function TianShenLingHeDrawCell:LoadCallBack()
    if not self.item_show then
        self.item_show = ItemCell.New(self.node_list["linghe_fanli_item"])
    end

    self.node_list["fanli_btn_buy"].button:AddClickListener(BindTool.Bind(self.OnClickBuy, self))
end

function TianShenLingHeDrawCell:ReleaseCallBack()
    if self.item_show then
        self.item_show:DeleteMe()
        self.item_show = nil
    end
end

function TianShenLingHeDrawCell:OnFlush()
    if self.data == nil then
        return
    end

    self.price_type = self.data.price_type --价格类型
    self.node_list["item_name"].text.text = self.data.sale_name
    self.node_list["rmb_num"]:SetActive(self.price_type == 3)
    self.node_list["xianyu_root"]:SetActive(self.price_type ~= 3)
    if self.price_type ~= 3 then --非直购
        local bundle, asset = ResPath.GetCommonIcon(ResPath.GetMoneyIcon(self.price_type + 1))
        self.node_list["huobi_icon"].image:LoadSprite(bundle, asset, function ()
            self.node_list["huobi_icon"].image:SetNativeSize()
        end)
        self.node_list["xianyu_num"].text.text = self.data.special_sale_price
    elseif self.price_type == 3 then --直购
        local price = RoleWGData.GetPayMoneyStr(self.data.rmb_price, self.data.rmb_type, 0)
        self.node_list["rmb_num"].text.text = price
    end

    --限购数量
    local limit_buy_num = self.data.limit_buy_times - self.data.buy_num
    local color = limit_buy_num <=0 and COLOR3B.RED or COLOR3B.DEFAULT_NUM
    local str = ToColorStr(limit_buy_num, color)
    self.node_list["limit_buy"].text.text = string.format(Language.TianShenLingHe.TimeLimit_buy, str)

    self.node_list["is_sellout"]:SetActive(limit_buy_num <= 0)
    self.node_list["fanli_btn_buy"]:SetActive(limit_buy_num > 0)
    self.item_show:SetData(self.data.item_list)
end

function TianShenLingHeDrawCell:OnClickBuy()
    local text_dec = ""
    local money --消耗的钱
    if self.price_type == 0 then --仙玉类型
        text_dec = string.format(Language.TianShenLingHe.DrawShopBuyTips1, self.data.special_sale_price)
        money = RoleWGData.Instance:GetIsEnoughUseGold(self.data.special_sale_price)
    elseif self.price_type == 1 then --元宝类型
        text_dec = string.format(Language.TianShenLingHe.DrawShopBuyTips2, self.data.special_sale_price)
        money = RoleWGData.Instance:GetIsEnoughBindGold(self.data.special_sale_price)
    else                            --直购类型
        local price = RoleWGData.GetPayMoneyStr(self.data.rmb_price, self.data.rmb_type, 0)
		text_dec = string.format(Language.TianShenLingHe.DrawShopBuyTips3, price)
    end
    local ok_func = function ()
        local state = TianShenLingHeWGData.Instance:GetLingHeShopOpenState()
        if state == 1 then
            if self.price_type == 3 then
                RechargeWGCtrl.Instance:Recharge(self.data.rmb_price, self.data.rmb_type)
            else
                if money then
                    TianShenLingHeWGCtrl.Instance:SendLingHeDrawReq(TianShenLingHeWGData.DRAW_TYPE.BUY, self.data.type)
                else
                    VipWGCtrl.Instance:OpenTipNoGold()
                end
            end
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenLingHe.DrawShopBuyError)
        end
    end
    TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
end