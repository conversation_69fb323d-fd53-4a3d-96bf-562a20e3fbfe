--------------------------------------------------
-- 仙界boss复仇
--------------------------------------------------
XianJieBossRevengeView = XianJieBossRevengeView or BaseClass(SafeBaseView)

function XianJieBossRevengeView:__init()
	self.is_modal = false
	self.is_any_click_close = false
    self.is_safe_area_adapter = true
    self.view_name = "XianJieBossRevengeView"
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_xianjie_revenge")
    
    self.view_layer = UiLayer.MainUILow
end

function XianJieBossRevengeView:__delete()
end

function XianJieBossRevengeView:ReleaseCallBack()
    self.show_list = nil

    if nil ~= self.scene_enemy_list then
		self.scene_enemy_list:Delete<PERSON>e()
		self.scene_enemy_list = nil
    end
    self:CancelTween()
end

function XianJieBossRevengeView:CloseCallBack()
end

function XianJieBossRevengeView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_revenge, BindTool.Bind2(self.ShowOrHideList, self))
    XUI.AddClickEventListener(self.node_list.btn_shrink, BindTool.Bind2(self.ShowList, self, false))
    self.show_list = true
    if nil == self.scene_enemy_list then
        self.scene_enemy_list = AsyncListView.New(XianJieSceneEnemyRender, self.node_list["ph_enemys_list"])
        self.scene_enemy_list:SetSelectCallBack(BindTool.Bind1(self.SelectEnemyCallBack,self))
        self.scene_enemy_list:SetDefaultSelectIndex(nil)
    end
end

function XianJieBossRevengeView:CancelSelectEnemy()
    if self:IsOpen() and self:IsLoaded() and self.scene_enemy_list then
        self.scene_enemy_list:CancelSelect()
    end
end

function XianJieBossRevengeView:SelectEnemyCallBack(cell, _, _, is_click)
    if cell and cell:GetData() then
        local enemy_info = cell:GetData()
        BossWGCtrl.Instance:SelectEnemyCallBack(enemy_info)
    end
end

function XianJieBossRevengeView:CancelTween()
    if self.enter_play_tween then
        self.enter_play_tween:Kill()
        self.enter_play_tween = nil
    end
end
function XianJieBossRevengeView:ShowOrHideList()
    self:ShowList(not self.show_list)
end

function XianJieBossRevengeView:ShowList(enable)
    if self.show_list ~= enable then
        self:CancelTween()
        self.enter_play_tween = DG.Tweening.DOTween.Sequence()
        local from = enable and 0 or 1
        local to = enable and 1 or 0
        local canvas_group = self.node_list.list_container.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
        --self.node_list.list_container.transform.localScale = Vector3(1, from, 1)
        canvas_group.alpha = from
        local tween_alpha = canvas_group:DoAlpha(from, to, 0.5)
        self.show_list = enable
        self.enter_play_tween:Append(tween_alpha)
        if enable then
            self.node_list.list_container:SetActive(enable)
        end

        if not enable then --收缩
            self.enter_play_tween:OnComplete(function()
                self.node_list.list_container:SetActive(false)
                self.enter_play_tween = nil
            end)
        else --展开
            self.enter_play_tween:OnComplete(function()
                self.enter_play_tween = nil
            end)
        end
        self.enter_play_tween:Play()
    end
end

--展开天书碎片界面，需要向左移动，
function XianJieBossRevengeView:MoveHorizontal(is_left)
    -- if self:IsOpen() and self:IsLoaded() then
    --     local pos = is_left and pos_tb[2] or pos_tb[1]
    --     local tween = self.node_list.container.rect:DOAnchorPosX(pos, 0.3)
    --     tween:SetEase(DG.Tweening.Ease.Linear)
    -- end
end

function XianJieBossRevengeView:OnFlush()
    local data_list = XianJieBossWGData.Instance:GetXianJieEnemyInfo()
    self.scene_enemy_list:SetDataList(data_list)
    if #data_list == 0 then
        self.show_list = false
        self.node_list["btn_revenge"]:SetActive(false)
        self.node_list.list_container:SetActive(false)
    else
        self.node_list["btn_revenge"]:SetActive(true)
        self:ShowList(true)
    end
end


XianJieSceneEnemyRender = XianJieSceneEnemyRender or BaseClass(BaseRender)
function XianJieSceneEnemyRender:__init()

end

function XianJieSceneEnemyRender:__delete()

end

function XianJieSceneEnemyRender:OnFlush()
    if self.data == nil then return end
    self.node_list.name_txt.text.text = self.data.role_name
end

function XianJieSceneEnemyRender:OnSelectChange(is_select)
	self.node_list["selectd_img"]:SetActive(is_select)
end