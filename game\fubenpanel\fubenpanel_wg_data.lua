WEIKIN ={
	MAX_LAYER = 32,   				--天仙阁符文解锁最大层数
	TONGGUAN_MAX = 100 ,			--天仙阁最大通关层数
}

SERVER_FB_TYPE = {
	TEAM_FB = 0,
	TOWER = 1,
}

DOUBLE_DROP_FB_TYPE = {       -- 双倍副本掉落
	EQUIP_FB = 0,
	COPPER_FB = 1,
	PET_FB = 2,
	TAFANG_FB = 3,
}

FUBEN_ENTER_TYPE = {
	ALONE = 0,
	TEAM = 1
}

local FuBenListData = {
    {BtnRemind = RemindName.TianXianFuBen},--副本-摘星阁
    {BtnRemind = RemindName.LongWangBaoZang},--副本-藏金宝库
    {BtnRemind = RemindName.XianLingShengDian},--副本-云泽秘境
    {BtnRemind = RemindName.BaGuaMiZhenFb},--副本-神灵圣殿
    --{BtnRemind = RemindName.ZhuoGuiFuBen},	--副本-抓鬼
    {BtnRemind = RemindName.LianYuFuBen},--副本-无尽试炼
    {BtnRemind = RemindName.YuanGuXianDian},--副本-异界宝墟
    {BtnRemind = RemindName.Bootybay},--副本-上古遗迹
    {BtnRemind = RemindName.TianShenFb},-- 副本 封神台
    {BtnRemind = "nil2"},
}

local FuBenListSingleData = {
	{BtnRemind = RemindName.TianXianFuBen},--副本-摘星阁
	{BtnRemind = RemindName.LianYuFuBen},--副本-无尽试炼
    {BtnRemind = RemindName.XianLingShengDian},--副本-云泽秘境
    {BtnRemind = RemindName.BaGuaMiZhenFb},--副本-神灵圣殿
	-- {BtnRemind = RemindName.TianShenFb},-- 副本 封神台
    {BtnRemind = RemindName.LongWangBaoZang},--副本-藏金宝库
}
local FuBenListTeamData = {
    {BtnRemind = RemindName.YuanGuXianDian},--副本-异界宝墟
	{BtnRemind = RemindName.FbBeauty},--副本-女神
	{BtnRemind = RemindName.FbControlBeasts},--副本-幻兽
	{BtnRemind = RemindName.FbWuHun}, --副本-武魂
	{BtnRemind = RemindName.FbRuneTower}, --副本-符文塔
	--{BtnRemind = RemindName.Bootybay},--副本-上古遗迹
}

DOUBLE_DROP_FLAG_BY_GOBALXUNBAO = false 				--双倍副本开启标志，来自全民狂欢
DOUBLE_EXP_FLAG_BY_GOBALXUNBAO = false				--双倍经验开启标志，来自全民狂欢

WELKIN_GUIDE_MAX_TIMES = 3 								-- 新手引导进入打了三次则不在继续挑战

FuBenPanelWGData = FuBenPanelWGData or BaseClass()

function FuBenPanelWGData:__init()
	if FuBenPanelWGData.Instance then
		error("[FuBenPanelWGData] Attempt to create singleton twice!")
		return
	end
	FuBenPanelWGData.Instance = self

	--self.copper_msg_info = {
	--	max_pass_layer = 0,
	--	day_has_enter_times = 0,
	--	day_buy_times = 0,
    --    day_star_num = 0,
	--	--star_num_list = {},
	--}

	--self.copper_scence_info = {}
	self.pet_scence_info = {}
	self.pet_msg_info = {
		pass_fb_max_level = 0,
		day_times = 0,
		buy_times = 0,
		pass_petfb_star = {},
	}
	self.pet_reward_info = {}
	self.fuben_task_data = {}
	self.weikin_scene_info = {}
	self.enter_type = 1        -- 经验本是单人进入还是组队

	self.guide_finish_times = 0 -- 天仙阁引导完成副本的次数

	self.fm_fb_enter_times = 0
	self.fm_fb_buy_times = 0
	self.fm_fb_pass_flag = {}
	self.star_count = {}
	self.zhushenta_data_list = {} --诛神塔数据
	self.rank_data_list = {} --天仙阁排行榜
	self.layout_star_list = {} --诛神塔星级表
	self.zhushenta_count = 0
	self.rune_system_cfg_auto = ConfigManager.Instance:GetAutoConfig("rune_system_cfg_auto")
	self.tianxiangefbcfg_auto = ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto")
	self.coinfb_cfg_auto = ConfigManager.Instance:GetAutoConfig("coinfb_cfg_auto")
	self.vip_auto = ConfigManager.Instance:GetAutoConfig("vip_auto")
	self.killgod_fb_cfg_auto = ConfigManager.Instance:GetAutoConfig("killgod_fb_cfg_auto")
	self.equip_fumo_cfg_auto = ConfigManager.Instance:GetAutoConfig("equip_fumo_cfg_auto")
	self.new_petfb_cfg_auto = ConfigManager.Instance:GetAutoConfig("new_petfb_cfg_auto")
	self.fake_petfb_cfg_auto = ConfigManager.Instance:GetAutoConfig("shouhushengling_cfg_auto")

	self.zhuhenta_cfg = ConfigManager.Instance:GetAutoConfig("killgod_fb_cfg_auto")

	self.zhushenta_level_cfg = ListToMap(self.zhuhenta_cfg.level,"level")

	self:InitZhuoGuiData()

	RemindManager.Instance:Register(RemindName.FuBenPanel, BindTool.Bind(self.IsShowFubenRedPoint, self))			          -- 主界面图标红点
	RemindManager.Instance:Register(RemindName.TianXianFuBen, BindTool.Bind(self.IsShowTianXianRedPoint, self))		          -- 天仙阁红点
	RemindManager.Instance:Register(RemindName.XianLingShengDian, BindTool.Bind(self.IsShowXianLingShengDianRedPoint, self))  -- 仙灵圣殿红点
	RemindManager.Instance:Register(RemindName.TianShenFb, BindTool.Bind(self.IsShowTianShenFbRedPoint, self))  			  -- 天神副本红点
	RemindManager.Instance:Register(RemindName.BaGuaMiZhenFb, BindTool.Bind(self.IsShowBaGuaMiZhenFbRedPoint, self))  		  -- 八卦迷阵副本红点
	RemindManager.Instance:Register(RemindName.LongWangBaoZang, BindTool.Bind(self.IsShowLongWangBaoZangRedPoint, self))	  -- 龙王宝藏红点
	RemindManager.Instance:Register(RemindName.LiuDaoLunHui, BindTool.Bind(self.IsShowLiuDaoLunHuiRedPoint, self))			  -- 六道轮回红点
	RemindManager.Instance:Register(RemindName.LianYuFuBen, BindTool.Bind(self.IsShowLianYuRedPoint, self))			          -- 炼狱洞窟红点
	RemindManager.Instance:Register(RemindName.YuanGuXianDian, BindTool.Bind(self.IsShowYuanGuXianDianRedPoint, self))		  -- 远古仙殿红点
	RemindManager.Instance:Register(RemindName.ManHungGuDian, BindTool.Bind(self.IsShowManHuangGuDianRedPoint, self))		  -- 蛮荒古殿红点
	--RemindManager.Instance:Register(RemindName.ZhuShenTa, BindTool.Bind(self.IsShowZhuShenTaRedPoint, self))			      -- 诛神塔红点
	RemindManager.Instance:Register(RemindName.Bootybay, BindTool.Bind(self.IsShowBootybayRedPoint, self))  				  -- 藏宝湾副本红点
	RemindManager.Instance:Register(RemindName.FbControlBeasts, BindTool.Bind(self.IsShowControlBeastsRedPoint, self))  	  -- 幻兽副本红点
	RemindManager.Instance:Register(RemindName.FbBeauty, BindTool.Bind(self.IsShowBeautyRedPoint, self))  	                  -- 女神副本红点
	RemindManager.Instance:Register(RemindName.FbWuHun, BindTool.Bind(self.IsShowWuHunRedPoint, self))  	                  -- 武魂副本红点
	RemindManager.Instance:Register(RemindName.FbRuneTower, BindTool.Bind(self.IsShowRuneTowerRedPoint, self))  	          -- 符文塔副本红点

	--章节关卡层数map
	self.fanren_xiuzhen_level_map = ListToMap(self.tianxiangefbcfg_auto.level_cfg, "level")
	self.fanren_xiuzhen_map = ListToMap(self.tianxiangefbcfg_auto.level_cfg, "chapter", "level")
	self.fanren_chapter_reward_map = self.tianxiangefbcfg_auto.chapter_reward and ListToMap(self.tianxiangefbcfg_auto.chapter_reward, "chapter") or {}
	self.fanren_level_chapter_reward = self.tianxiangefbcfg_auto.chapter_reward and ListToMap(self.tianxiangefbcfg_auto.chapter_reward, "level") or {}
	self.fanren_xiuzhen_level_boss_map = ListToMap(self.tianxiangefbcfg_auto.level_cfg,"boss_id")
	self.fanren_xiuzhen_level_reward = ListToMap(self.tianxiangefbcfg_auto.level_reward,"seq")
	self.fanren_xiuzhen_level_reward_level = ListToMap(self.tianxiangefbcfg_auto.level_reward,"level")
	self.reward_preview = ListToMap(self.tianxiangefbcfg_auto.reward_preview,"seq")
	self.xiuzhen_level_fetch_list = {}
	self.xiuzhen_chapter_fetch_list = {}

	--初始化天神副本配置
	self:InitTianShenConfig()
	--初始化八卦迷阵副本配置
	self:InitBaGuaMiZhenConfig()
	--初始化宠物本配置
    self:InitPetConfig()
    self.role_data_change = BindTool.Bind1(self.OnRoleAttrValueChange, self)
    RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function FuBenPanelWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.FuBenPanel)
	RemindManager.Instance:UnRegister(RemindName.TianXianFuBen)
	RemindManager.Instance:UnRegister(RemindName.LianYuFuBen)
	RemindManager.Instance:UnRegister(RemindName.XianLingShengDian)
	RemindManager.Instance:UnRegister(RemindName.TianShenFb)
	RemindManager.Instance:UnRegister(RemindName.LongWangBaoZang)
	RemindManager.Instance:UnRegister(RemindName.LiuDaoLunHui)
	RemindManager.Instance:UnRegister(RemindName.YuanGuXianDian)
	RemindManager.Instance:UnRegister(RemindName.ZhuShenTa)
	RemindManager.Instance:UnRegister(RemindName.Bootybay)
	RemindManager.Instance:UnRegister(RemindName.ManHungGuDian)
	RemindManager.Instance:UnRegister(RemindName.BaGuaMiZhenFb)
	RemindManager.Instance:UnRegister(RemindName.FbControlBeasts)
	RemindManager.Instance:UnRegister(RemindName.FbBeauty)
	RemindManager.Instance:UnRegister(RemindName.FbWuHun)
    RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)

	self:DeleteZhuoGuiData()
    self.role_data_change = nil
	self.cache_first_can_fatch_chapter = nil
	self.cache_first_can_fatch_sublevel = nil
	FuBenPanelWGData.Instance = nil

	if nil ~= self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function FuBenPanelWGData:OnRoleAttrValueChange(attr_name, value)
	if attr_name == "level" then
		RemindManager.Instance:Fire(RemindName.LianYuFuBen)
	end
end

----天仙阁--------------
function FuBenPanelWGData:CloseCallBack()
	if self.role_display then
		self.role_display:GetRootNode():stopAllActions()
		self.is_play_anim = false
	end
end
function FuBenPanelWGData:GetFuWenCao(index)
	local ceg = self.rune_system_cfg_auto.rune_slot_open
	for k,v in pairs(ceg) do
		if v.need_pass_layer == index then
		return true,v.groove_icon
		end
	end
	return false ,nil
end

-- 设置当前通过层数
function FuBenPanelWGData:SetPassLevel(level)
	RankWGCtrl.Instance:SendRankListReq(PersonRankType.TianXianGeRank, BindTool.Bind1(self.SetTianXianGeRankList, self))

	self.welkin_pass_level = level
	self.cache_first_can_fatch_chapter = nil
	self.cache_first_can_fatch_sublevel = nil
	--print_error("天仙阁层数数据",level)
end

--修真路检测推荐挑战
function FuBenPanelWGData:GetFBTuiJianChallengeData()
	if self.tianxiangefbcfg_auto and self.tianxiangefbcfg_auto.other[1] then
		return self.tianxiangefbcfg_auto.other[1].tuijian_check_time ,self.tianxiangefbcfg_auto.other[1].tuijian_level,self.tianxiangefbcfg_auto.other[1].tuijian_awake_time
	end
	return -1,9999,-1
end



function FuBenPanelWGData:BindPersonalReqCallBack()
	RankWGCtrl.Instance:SendRankListReq(PersonRankType.TianXianGeRank, BindTool.Bind1(self.SetTianXianGeRankList, self))
end
function FuBenPanelWGData:GetMaxCengShu(level)
	if level >= WEIKIN.MAX_LAYER then
		return 1
	end
	return 0
end

function FuBenPanelWGData:SceneTXGInfo(protocol)
	self.weikin_scene_info.level = protocol.level
	self.weikin_scene_info.monster_flush_time = protocol.monster_flush_time
	self.weikin_scene_info.time_out_stamp = protocol.time_out_stamp
	self.weikin_scene_info.delay_kickout_timestamp = protocol.delay_kickout_timestamp
	self.weikin_scene_info.is_finish = protocol.is_finish
	self.weikin_scene_info.is_pass = protocol.is_pass
	self.weikin_scene_info.pass_time_s = protocol.pass_time_s
	self.weikin_scene_info.param_1 = protocol.param_1
	self.weikin_scene_info.hunjing_num = protocol.hunjing_num
	self.weikin_scene_info.reward_count = protocol.reward_count
	self.weikin_scene_info.layer_seq = protocol.layer_seq
	self.weikin_scene_info.reward_item = protocol.reward_item
end

function FuBenPanelWGData:GetWelkinInfo()
	return self.weikin_scene_info
end

function FuBenPanelWGData:SetTXGSeq(seq)
	self.layer_seq = seq
end

function FuBenPanelWGData:GetFuWenJieSuo()
	local t_config = {}
	local layer_max_config = self.rune_system_cfg_auto.rune_fetch or {}
	for k, v in pairs(layer_max_config) do
		if self.layer_seq == v.seq and v.pandect == 2 and self.welkin_pass_level == v.in_layer_open then
			table.insert(t_config, v)
		end
	end
	return t_config
end

function FuBenPanelWGData:GetConfigByStep()
	local layer_seq = FuBenPanelWGData.Instance:SetNextcfgMax()
	if layer_seq == nil then return end
	if layer_seq < 21 then
		local layer_reward_config = self.rune_system_cfg_auto.rune_fetch or {}
		for k,v in pairs(layer_reward_config) do
			if layer_seq + 1  == v.seq and v.pandect == 2 then
				return v
			end
		end
	end
	return nil
end


function FuBenPanelWGData:GetConfigFuWen()
	local layer_seq = FuBenPanelWGData.Instance:SetNextcfgMax()
	local t_config = {}
	if layer_seq <= 17 then
		local layer_reward_config = self.rune_system_cfg_auto.rune_fetch or {}
		for k,v in pairs(layer_reward_config) do
			if layer_seq + 1 == v.seq and v.pandect == 2 then
				table.insert(t_config, v)
			end
		end
		return t_config
	end
	return {}
end


-- 获取当前通过层数
function FuBenPanelWGData:GetPassLevel()
	if nil == self.welkin_pass_level or self.welkin_pass_level < 0
		or self.welkin_pass_level > #self.tianxiangefbcfg_auto.level_cfg then
		return -1
	end
	return self.welkin_pass_level
end

-- 设置下一层通过层数
function FuBenPanelWGData:SetPassFetchLevel(fetch_level)
	self.welkin_pass_fetch_level = fetch_level
end

-- 设置阶段奖励标识
function FuBenPanelWGData:SetLevelRewardFlag(flag)
	self.welkin_level_reward_flag = flag
end

function FuBenPanelWGData:GetLevelRewardFlag(seq)
	return self.welkin_level_reward_flag[seq] == 1
end

--显示下一阶段奖励


-- 获取下一层通过层数
function FuBenPanelWGData:GetPassFetchLevel()
	return self.welkin_pass_fetch_level
end

-- 获取配置上最大等级
function FuBenPanelWGData:GetCfgMaxLevel()
	return #self.tianxiangefbcfg_auto.level_cfg
end
-- 获取天仙阁配置
function FuBenPanelWGData:GetCfgLevelInfo()
	return self.tianxiangefbcfg_auto.reward_show
end
-- 获取天仙阁解锁符文配置
function FuBenPanelWGData:GetCfgFuWenInfo()
	return self.tianxiangefbcfg_auto.broadcast
end
-- 是否达到顶层
function FuBenPanelWGData:IsReachMaxLevel()
	return self.welkin_pass_level == #self.tianxiangefbcfg_auto.level_cfg
end
function FuBenPanelWGData:SetNextcfgMax()
	local next_cfg = self.tianxiangefbcfg_auto.layer_seq
	if nil == self.welkin_pass_level then return end
	for i = 1, #next_cfg do
		if next_cfg[i].layer_max >= self.welkin_pass_level  and  next_cfg[i].layer_min <= self.welkin_pass_level then
			return next_cfg[i].seq
		end
	end
	return  nil
end


-- 根据boss_id获取boss大小位置信息
function FuBenPanelWGData:GetBossInfoByBossID(boss_id)
	--local cfg = self.tianxiangefbcfg_auto.level_cfg
	--for k,v in pairs(cfg) do
	return	self.fanren_xiuzhen_level_boss_map[boss_id]

	--	if v.boss_id == boss_id then
	--		return v
	--	end
	--end
	--return  nil
end
-- 根据等级获取试炼称号
function FuBenPanelWGData:GetCurTitleName(level)
	local cur_level = level or self.welkin_pass_level
	if nil == self.tianxiangefbcfg_auto.level_cfg[cur_level] then return "" end
	return self.tianxiangefbcfg_auto.level_cfg[cur_level].title_name
end

-- 根据等级获取当前站台信息
function FuBenPanelWGData:GetTerraceListCfgByLevel(level)
	local terrace_list = {}
	if self:IsReachMaxLevel() then		-- 满级特殊处理
		local first_terrace_level = math.floor(level / 3) * 3 + 1
		for i = 0, 2 do
			table.insert(terrace_list, self.tianxiangefbcfg_auto.level_cfg[first_terrace_level + i])
		end
		return terrace_list
	end

	local first_terrace_level = math.floor(level / 3) * 3 + 1
	for i = 0, 2 do
		table.insert(terrace_list, self.tianxiangefbcfg_auto.level_cfg[first_terrace_level + i])
	end
	return terrace_list
end

-- 根据等级获取当前站台奖励
function FuBenPanelWGData:GetTerraceRewardByLevel(level)
	--print_error(self.tianxiangefbcfg_auto.reward_show)
	for k,v in pairs(self.tianxiangefbcfg_auto.reward_show) do
		if v.level == level then
			return v
		end
	end
	return nil
end

function FuBenPanelWGData:GetTerraceRewardRemind()
	local reward_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_TIANXIANGEFB_DAY_REWARD)
	return self.welkin_pass_level > 0 and reward_num == 0 and 1 or 0
end

-- 根据等级获取配置信息
function FuBenPanelWGData:GetCfgByLevel(level)
	return self.tianxiangefbcfg_auto.level_cfg[level]
end

-- 根据等级拿boss名字和推荐战力
function FuBenPanelWGData:GetNameAndZhanLiByLevel(level)
	local cfg_list = self.tianxiangefbcfg_auto.level_cfg
	for k, v in pairs(cfg_list) do
		if v.level == level then
			return v.capability ,v.boss_id
		end
	end
end


-- 根据当前等级获取天宫试炼增加的总战力
function FuBenPanelWGData:GetTotalZhanliByLevel(level)
	local cfg = self:GetCfgByLevel(level)
	if nil == cfg then return 0 end
	local attribute = AttributeMgr.GetAttributteByClass(cfg)
	local total_zhanli = AttributeMgr.GetCapability(attribute)
	return total_zhanli
end

-- 根据当前等级获取下一级增加的战力
function FuBenPanelWGData:GetNextZhanliByLevel(level)
	local next_level = level + 1
	if next_level > self:GetCfgMaxLevel() then return 0 end		--达到最大值

	local cur_cfg = self:GetCfgByLevel(level)
	local cur_attribute = AttributeMgr.GetAttributteByClass(cur_cfg)
	local next_cfg = self:GetCfgByLevel(next_level)
	local next_attribute = AttributeMgr.GetAttributteByClass(next_cfg)

	local m_attribute = AttributeMgr.LerpAttributeAttr(cur_attribute, next_attribute)
	local add_zhanli = AttributeMgr.GetCapability(m_attribute)
	return add_zhanli
end

---------------------------------------------------------------------------------------------------
function FuBenPanelWGData:GetCopperTitleCfg()
	return self.coinfb_cfg_auto.level_limit
end

function FuBenPanelWGData:SetTongBiInfo(protocol)
	--print_error("龙王宝藏副本返回信息",protocol)
	self.copper_msg_info = {}
	self.copper_msg_info.max_pass_layer = protocol.max_pass_layer
	self.copper_msg_info.day_has_enter_times = protocol.day_has_enter_times
	self.copper_msg_info.day_buy_times = protocol.day_buy_times
	self.copper_msg_info.day_star_num = protocol.day_star_num
end

function FuBenPanelWGData:GetTongBiInfo()
	return self.copper_msg_info
end

function FuBenPanelWGData:GetTongBiBenOtherCfg()
	local ohter_cfg = self.coinfb_cfg_auto.other[1]
	return ohter_cfg
end

function FuBenPanelWGData:SetStarDisplay(index)
	if self.copper_msg_info.star_num_list then
		return self.copper_msg_info.star_num_list[index - 1]
	end
end
function FuBenPanelWGData:GetTongBiStarNum()
	return self.copper_msg_info.day_star_num
end

function FuBenPanelWGData:SetTblevelInfo(index)
	local fb_level = self.coinfb_cfg_auto.level_limit
	for k,v in pairs(fb_level) do
		if v.layer == index then
			return v
		end
	end
end

function FuBenPanelWGData:GetFieldCountCfg()
	local vip_level_cfg = self.vip_auto.level[6] --竞技场可购买次数
	return vip_level_cfg
end

function FuBenPanelWGData:GetFieldNextBuyCount(vip_level)
	local vip_level_cfg = self:GetFieldCountCfg()
	for i = 0, 12 do
		if vip_level_cfg["param_" .. vip_level] < vip_level_cfg["param_" .. i] then
			return i
		elseif vip_level_cfg["param_" .. vip_level] == vip_level_cfg["param_" .. 12] then
			return vip_level
		end
	end
	return -1
end

function FuBenPanelWGData:GetVipBuyCountCfg()
	local vip_level_cfg = self.vip_auto.level[5]
	return vip_level_cfg
end

function FuBenPanelWGData:GetVipNextBuyCount(vip_level)
	local vip_level_cfg = self:GetVipBuyCountCfg()
	for i = 0, 12 do
		if vip_level_cfg["param_" .. vip_level] < vip_level_cfg["param_" .. i] then
			return i
		elseif vip_level_cfg["param_" .. vip_level] == vip_level_cfg["param_" .. 12] then
			return vip_level
		end
	end
	return -1
end

function FuBenPanelWGData:GetVipCfg(fb_type)
	local cfg = nil
	if fb_type == FUBEN_TYPE.FBCT_TONGBIBEN then			--铜币本
		cfg = self:GetVipBuyCopperCfg()
	elseif fb_type == FUBEN_TYPE.FBCT_PETBEN then			--宠物本
		cfg = self:GetVipBuyPetCfg()
	elseif fb_type == FUBEN_TYPE.FBCT_TIANSHEN_FB then		--天神本
		cfg = self:GetVipBuyTianShenCfg()
	elseif fb_type == FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB then 	--八卦迷阵
		cfg = self:GetVipBuyBaGuaZhenCfg()
	elseif fb_type == FUBEN_TYPE.FBCT_WUJINJITAN then		--经验本
		cfg = self:GetVipBuyWuJinJiTanCfg()
	elseif fb_type == FUBEN_TYPE.LINGHUNGUANGCHANG then		--经验本(灵魂广场)
		cfg = self:GetVipBuyLingHunGuangChangCfg()
	elseif fb_type == FUBEN_TYPE.HIGH_TEAM_EQUIP then 		--装备本
		cfg = self:GetVipBuyTeamEquipCfg()
	elseif fb_type == FUBEN_TYPE.FB_MANHUANG_GUDIAN_FB then 	--蛮荒古殿
		cfg = self:GetVipBuyManHuangCfg()
	end
	return cfg
end

function FuBenPanelWGData:GetNextVipParam(fb_type)
	local now_count, yet_buy_time, next_level, next_count = 0, 0, 0, 0
	if fb_type == FUBEN_TYPE.FBCT_WUJINJITAN then
		now_count = VipPower.Instance:GetParam(VipPowerId.exp_fb_buy_times)
		yet_buy_time = WuJinJiTanWGData.Instance:GetTeamFbBuyTimes()
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.exp_fb_buy_times ,now_count)
	elseif fb_type == FUBEN_TYPE.LINGHUNGUANGCHANG then
		now_count = VipPower.Instance:GetParam(VipPowerId.exp_fb_buy_times)
		yet_buy_time = WuJinJiTanWGData.Instance:GetTeamFbBuyTimes()
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.exp_fb_buy_times ,now_count)
	elseif fb_type == FUBEN_TYPE.FBCT_TONGBIBEN then
		now_count = VipPower.Instance:GetParam(VipPowerId.coin_fb_buy_times)
		local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
		yet_buy_time = copper_info.day_buy_times
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.coin_fb_buy_times ,now_count)
	elseif fb_type == FUBEN_TYPE.FBCT_PETBEN then
		now_count = VipPower.Instance:GetParam(VipPowerId.pet_fb_buy_times)
		local pet_info = FuBenPanelWGData.Instance:GetPetAllInfo()
		yet_buy_time = pet_info.buy_times
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.pet_fb_buy_times ,now_count)
	elseif fb_type == FUBEN_TYPE.FBCT_TIANSHEN_FB then
		now_count = VipPower.Instance:GetParam(VipPowerId.reward_tianshen_fb_buy_times)
		yet_buy_time = FuBenPanelWGData.Instance:GetTianShenBuyTimes()
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.reward_tianshen_fb_buy_times ,now_count)
	elseif fb_type == FUBEN_TYPE.FBCT_TAFANG then
		now_count = VipPower.Instance:GetParam(VipPowerId.tafang_fb_buy_times)
		yet_buy_time = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.tafang_fb_buy_times ,now_count)
	elseif fb_type == FUBEN_TYPE.LINGHUNGUANGCHANG then
		now_count = VipPower.Instance:GetParam(VipPowerId.exp_fb_buy_times)
		yet_buy_time = WuJinJiTanWGData.Instance:GetTeamFbBuyTimes()
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.exp_fb_buy_times ,now_count)
	elseif fb_type == FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB then
		now_count = VipPower.Instance:GetParam(VipPowerId.reward_baguamizhen_fb_buy_times)
		yet_buy_time =  DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BAGUAMIZHEN_FB_VIP_BUY_TIMES) or 0
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.reward_baguamizhen_fb_buy_times ,now_count)
	elseif fb_type == FUBEN_TYPE.HIGH_TEAM_EQUIP then 		--装备本
		now_count = VipPower.Instance:GetParam(VipPowerId.team_equip_buy_times)
		yet_buy_time = TeamEquipFbWGData.Instance:GetHTEFbVipBuyTimes()
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.team_equip_buy_times ,now_count)
	elseif fb_type == FUBEN_TYPE.FB_MANHUANG_GUDIAN_FB then 		--蛮荒古殿
		now_count = VipPower.Instance:GetParam(VipPowerId.reward_manhuanggudian_fb_buy_times)
		yet_buy_time =  DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_VIP_BUY_TIMES) or 0
		next_level, next_count = VipPower.Instance:GetNextBigParam(VipPowerId.reward_manhuanggudian_fb_buy_times ,now_count)
	end
	return now_count, yet_buy_time, next_level, next_count
end

function FuBenPanelWGData:GetVipBuyCopperCfg()
	local vip_level_cfg = self.vip_auto.level[5]
	return vip_level_cfg
end

function FuBenPanelWGData:GetVipBuyPetCfg()
	local vip_level_cfg = self.vip_auto.level[15]
	return vip_level_cfg
end
function FuBenPanelWGData:GetVipBuyWuJinJiTanCfg()
	local vip_level_cfg = self.vip_auto.level[12]
	return vip_level_cfg
end
function FuBenPanelWGData:GetVipBuyLingHunGuangChangCfg()
	local vip_level_cfg = self.vip_auto.level[21]
	return vip_level_cfg
end
function FuBenPanelWGData:GetVipBuyTeamEquipCfg()
	local vip_level_cfg = self.vip_auto.level[17]
	return vip_level_cfg
end

function FuBenPanelWGData:GetVipBuyTianShenCfg()
	local vip_level_cfg = self.vip_auto.level[26]
	return vip_level_cfg
end

--八卦阵购买次数
function FuBenPanelWGData:GetVipBuyBaGuaZhenCfg()
	local vip_level_cfg = self.vip_auto.level[27]
	return vip_level_cfg
end

function FuBenPanelWGData:GetVipBuyManHuangCfg()
	local vip_level_cfg = self.vip_auto.level[28]
	return vip_level_cfg
end

function FuBenPanelWGData:GetVipNextPetCount(vip_level)
	local vip_level_cfg = self:GetVipBuyPetCfg()
	for i = 0, 12 do
		if vip_level_cfg["param_" .. vip_level] < vip_level_cfg["param_" .. i] then
			return i
		elseif vip_level_cfg["param_" .. vip_level] == vip_level_cfg["param_" .. 12] then
			return vip_level
		end
	end
	return -1
end

function FuBenPanelWGData:GetVipNextTianShenCount(vip_level)
	local vip_level_cfg = self:GetVipBuyTianShenCfg()
	for i = 0, 12 do
		if vip_level_cfg["param_" .. vip_level] < vip_level_cfg["param_" .. i] then
			return i
		elseif vip_level_cfg["param_" .. vip_level] == vip_level_cfg["param_" .. 12] then
			return vip_level
		end
	end
	return -1
end

function FuBenPanelWGData:GetVipNextBaGuaMiZhenCount(vip_level)
	local vip_level_cfg = self:GetVipBuyBaGuaZhenCfg()
	for i = 0, 12 do
		if vip_level_cfg["param_" .. vip_level] < vip_level_cfg["param_" .. i] then
			return i
		elseif vip_level_cfg["param_" .. vip_level] == vip_level_cfg["param_" .. 12] then
			return vip_level
		end
	end
	return -1
end

function FuBenPanelWGData:GetVipNextManHuangCount(vip_level)
	local vip_level_cfg = self:GetVipBuyManHuangCfg()
	for i = 0, 12 do
		if vip_level_cfg["param_" .. vip_level] < vip_level_cfg["param_" .. i] then
			return i
		elseif vip_level_cfg["param_" .. vip_level] == vip_level_cfg["param_" .. 12] then
			return vip_level
		end
	end
	return -1
end



function FuBenPanelWGData:SetOldSceneType(scene_type)
	-- 弹出经验本结算
	local scene_type = scene_type
	self.exp_timer_flag = -1
	if scene_type == SceneType.FUBEN_EXP or scene_type == SceneType.FUBEN_TIANMING then
		if self.exp_result_items then
			local scene_type1 = Scene.Instance:GetSceneType()
			if scene_type1 == SceneType.FUBEN_EXP  or scene_type1 == SceneType.FUBEN_TIANMING then
				return
			end

			-- 天命副本，通关后在场景里会弹面板，则退出场景后不再弹
			if scene_type == SceneType.FUBEN_TIANMING and 0 == (self.tianming_protocol_data.day_pass_layer) % 30 then
				return
			end

			if self.exp_result_items[1] ~= nil then
				self.exp_paran1 = 1
			else
				self.exp_paran1 = 2
			end
			FuBenPanelWGCtrl.Instance:OutExpScene(self.exp_result_items, self.exp_paran1, scene_type)
			self.exp_result_items = {}
			return
		end
	end

	if scene_type >= SceneType.FUBEN_MOUNT and scene_type <= SceneType.FUBEN_JIANGHU and self.is_initiative_out == true and self.info_data and self.info_data.param1 ~= 1 then
		FuBenPanelWGCtrl.Instance:Open()
		self.is_initiative_out = false
	end

	if scene_type == SceneType.FUBEN_JUQING or scene_type == SceneType.FUBEN_VIP or scene_type == SceneType.FUBEN_TIANMING then
		if self.is_initiative_out == true and self.info_data and self.info_data.param1 ~= 1 then
			FuBenPanelWGCtrl.Instance:Open()
			FuBenPanelWGCtrl.Instance:OpenResultPanel(nil, 2, 1, false)
			self.is_initiative_out = false
		end
	end
end

function FuBenPanelWGData:SetCopperScenceInfo(protocol)
	self.copper_scence_info = {}
	self.copper_scence_info.is_leave_fb = protocol.is_leave_fb
	self.copper_scence_info.cur_wave = protocol.cur_wave
	self.copper_scence_info.is_pass = protocol.is_pass
	self.copper_scence_info.cur_star_num = protocol.cur_star_num
	self.copper_scence_info.cur_layer = protocol.cur_layer
	self.copper_scence_info.is_sweep = protocol.is_sweep
	self.copper_scence_info.get_coin = protocol.get_coin
	self.copper_scence_info.get_item_num = protocol.get_item_num
	self.copper_scence_info.star_reward_coin = protocol.star_reward_coin
	self.copper_scence_info.get_exp = protocol.get_exp
	self.copper_scence_info.time_out_timestamp = protocol.time_out_timestamp
	self.copper_scence_info.next_star_timestamp = protocol.next_star_timestamp
	self.copper_scence_info.scene_timeout_timestamp = protocol.scene_timeout_timestamp
end

function FuBenPanelWGData:GetCopperScenceInfo()
	return self.copper_scence_info
end

function FuBenPanelWGData:GetCopperItemByLayer(layer, star_num)
	local coinfb_cfg = self.coinfb_cfg_auto.layer
	if not star_num then
		for k,v in pairs(coinfb_cfg) do
			if v.layer == layer then
				return v
			end
		end
	else
		for k,v in pairs(coinfb_cfg) do
			if v.layer == layer and star_num == v.star_num then
				return v
			end
		end
	end
end


function FuBenPanelWGData:GetVipBuyTfCountCfg()
	local vip_level_cfg = self.vip_auto.level[13]
	return vip_level_cfg
end


function FuBenPanelWGData:GetVipBuyTfCount(vip_level)
	local vip_level_cfg = self:GetVipBuyTfCountCfg()
	for i = 0, 12 do
		if vip_level_cfg["param_" .. vip_level] < vip_level_cfg["param_" .. i] then
			return i
		elseif vip_level_cfg["param_" .. vip_level] == vip_level_cfg["param_" .. 12] then
			return vip_level
		end
	end
	return -1
end

--设置副本离开时间
function FuBenPanelWGData:SetOutTimer( time_out )
	self.fuben_panel_time_out = time_out
end

--获取副本离开时间
function FuBenPanelWGData:GetOutTimer()
	if self.fuben_panel_time_out then
		return self.fuben_panel_time_out
	end
	return 0
end

--------------------



--判断进入副本前是否就选择了鼓舞
function FuBenPanelWGData:SetIsSelect(bool)
	self.is_select_encourage = bool
--	print_error("判断进入副本前是否就选择了鼓舞",self.is_select_encourage)
end
function FuBenPanelWGData:GetetIsSelect()
	return self.is_select_encourage or false
end



function FuBenPanelWGData:SetFubenPanelData(protocol)
	self.fuben_task_data = protocol
end

function FuBenPanelWGData:GetXinMoFBSkillName(seq)
	local skill_cfg = ConfigManager.Instance:GetAutoConfig("xinmo_fb_cfg_auto").pass_skill
	for k,v in pairs(skill_cfg) do
		if seq == v.skill_seq then
			return v
		end
	end
end

function FuBenPanelWGData:GetXinMoMonNum()
	local layer_cfg = ConfigManager.Instance:GetAutoConfig("xinmo_fb_cfg_auto").layer
	local scene_id = Scene.Instance:GetSceneId()
	local layer = nil
	for k,v in pairs(layer_cfg) do
		if scene_id == v.scene_id then
			layer = v.layer
			break
		end
	end

	if not layer then
		return 0, nil
	end

	local role_prof = RoleWGData.Instance:GetRoleProf()
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("xinmo_fb_cfg_auto").role_shadow
	local num = 0
	local monster_id = 0
	for k,v in pairs(monster_cfg) do
		if layer == v.layer then
			num = v.monster_num
			monster_id = v["monster_id".. role_prof]
			break
		end
	end
	
	return num, monster_id
end


-- mon_type 怪物表type 0：小怪， 1：boss
function FuBenPanelWGData:GetTaskFBNum(mon_type)
	local scene_id = Scene.Instance:GetSceneType()
	local fb_cfg = {}
	if scene_id == SceneType.GAINT_FB then
		fb_cfg = FuBenWGData.Instance:GetTaskFBCfg().shizhu_fb
	elseif scene_id == SceneType.DEVILCOME_FB then
		fb_cfg = FuBenWGData.Instance:GetTaskFBCfg().buff_fb
	elseif scene_id == SceneType.KUNLUNTRIAL_FB then
		fb_cfg = FuBenWGData.Instance:GetTaskFBCfg().zibao_fb
	end
	local num = 0
	for k,v in pairs(fb_cfg) do
		local mon_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v.monster_id]
		if mon_cfg and mon_cfg.type == mon_type then
			num = num + v.monster_num
		end
	end
	return num
end

function FuBenPanelWGData:GetFubenPanelData()
	-- body
	return self.fuben_task_data
end

-- function FuBenPanelWGData:GetFubenPanelData(index)
-- 	if nil == next(self.fuben_task_data) then return {} end
-- 	if index > #self.fuben_task_data then return {} end
-- 	return self.fuben_task_data[index]
-- end

function FuBenPanelWGData:SetXinMoFBRoleInfo(protocol)
	self.xinmo_fb_pass_flag = protocol.xinmo_fb_pass_flag
end

function FuBenPanelWGData:GetXinMoFBRoleIsOpen(layer)

	local xinmo_fb_pass_flag = self.xinmo_fb_pass_flag or 0
	local open_flag = bit:d2b(xinmo_fb_pass_flag)
	return open_flag[32 - layer] == 1
end

function FuBenPanelWGData:SetFuBenJiangHuGuideFlag(task_id)
	self.fb_jianghu_guide_task = task_id
end

function FuBenPanelWGData:GetFuBenJiangHuGuideFlag()
	local task_id = self.fb_jianghu_guide_task
	self.fb_jianghu_guide_task = nil
	return task_id
end

function FuBenPanelWGData:GetFuBenJiangHuGuideFlagUnClear()
	return self.fb_jianghu_guide_task
end

function FuBenPanelWGData:ExistFuBenJiangHuGuideFlag()
	return self.fb_jianghu_guide_task ~= nil
end



function FuBenPanelWGData:GetCopperFubenProbability()
	if not next(self.copper_scence_info) then
		return
	end
	local cfg = self:GetTongBiBenOtherCfg()
	local star = self.copper_scence_info.cur_star_num
	local str = {[0] = "zero", [1] = "one", [2] = "two", [3] = "three", }
	return cfg[str[star] .. "_star_Prob"]
end

-- 是否双倍掉落
function FuBenPanelWGData:GetIsDoubleDrop(fb_type)
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP) then
		local act_status = ServerActivityWGData.Instance:GetVersionActivityNowStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP)
		if act_status and act_status.status == ACTIVITY_STATUS.OPEN then
			local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_KILL_BOSS)
			local open_list = info.cur_value_t.fb_open_list
			if 1 == open_list[fb_type] then
				return true
			end
		end
	end
	if DOUBLE_DROP_FLAG_BY_GOBALXUNBAO then
		return true
	end
	return false
end

function FuBenPanelWGData:SetEnterType(type)
	self.enter_type = type
end

function FuBenPanelWGData:GetEnterType()
	return self.enter_type
end


function FuBenPanelWGData:SetTaFanSaoDanBossNum(boss_num)
	self.ta_fan_sao_dan_boss = boss_num
end

function FuBenPanelWGData:GetTaFanSaoDanBossNum()
	return self.ta_fan_sao_dan_boss or 0
end

function FuBenPanelWGData:SetTaFanIsNologer(is_nologer)
	self.ta_fan_is_nolonger = is_nologer
end

function FuBenPanelWGData:GetTaFanIsNologer()
	return self.ta_fan_is_nolonger or false
end

function FuBenPanelWGData:SetTianXianGeRankList(rank_type,data_list)
	if nil == data_list then return end

	self.rank_data_list = data_list
	table.sort( self.rank_data_list, SortTools.KeyUpperSorter("rank_value"))
	local view = FuBenPanelWGCtrl.Instance:GetFuBenPanemView()
--	print_error(data_list)
	--print_error(FuBenPanelWGCtrl.Instance.view)
--	print_error(view:IsOpen())
-- 	if view:IsOpen() then
-- 		view:OnFlushWelkinPanel()
-- --		print_error("返回了排行榜信息，是否刷新？",rank_type,data_list)
-- 	end
end
--获取天仙阁排行榜（只留前50名）
function FuBenPanelWGData:GetTianXianGeRankList()
	local rank_list = {}
	for i,v in pairs(self.rank_data_list) do
		if v.rank_index <= 50 then
			table.insert(rank_list,v)
		end
	end
	return rank_list
end


-------------------------诛神塔-------------------------------------------
--设置诛神塔数据
function FuBenPanelWGData:SetZhuShenTaData(protocol)
	--print_error(protocol)
	self.zhushenta_data_list = protocol
end

--根据层级获取诛神塔层级奖励
function FuBenPanelWGData:GetZhuShenTaLayoutReaward(layout_level)
	if self.killgod_fb_cfg_auto.level ~= nil then
		for i,v in pairs(self.killgod_fb_cfg_auto.level) do
			if v.level == layout_level then
				return v.show_list,v.reward_item,v.equip_part
			end
		end
	end
	return {},{},0
end

--设置诛神塔副本每日已进入次数
function FuBenPanelWGData:SetZhuShenTaFbDayCount(fb_count)
	self.zhushenta_count = fb_count or 0
end
--获取诛神塔副本每日已进入次数
function FuBenPanelWGData:GetZhuShenTaFbDayEnterTimes()
	return self.zhushenta_count
end
--获取诛神塔副本每日总次数
function FuBenPanelWGData:GetZhuShenTaFbDayTotalCount()
	return self.killgod_fb_cfg_auto.other[1].everyday_times
end

-- 获取诛神塔副本挑战次数
function FuBenPanelWGData:GetZhuShenTaFBCount()
	local total_count = self.killgod_fb_cfg_auto.other[1].everyday_times
	local cur_count =  total_count - self.zhushenta_count
	if cur_count < 0 then
		cur_count = 0
	end
	return cur_count , total_count
end

-- 获取诛神塔所有怪物配置
function FuBenPanelWGData:GetZhuShenTaTotalMonsterData()
	return self.killgod_fb_cfg_auto.level
end

--根据当前层数获取解锁部位阶数
function FuBenPanelWGData:GetJieSuoPartByLayout(layout_level)
	for i,v in pairs(self.equip_fumo_cfg_auto.fumo_special_attr)do
		if v.floor == layout_level then
			return v.part,v.order
		end
	end
	return self.equip_fumo_cfg_auto.fumo_special_attr[1].part,self.equip_fumo_cfg_auto.fumo_special_attr[1].order
	--return 0,0
end

--获取诛神塔最大通关层级
function FuBenPanelWGData:GetZhuShenTaPassMaxLevel()
	return self.zhushenta_data_list.max_pass_level or 0
end

-- 根据当前层级获取星数
function FuBenPanelWGData:GetStartCountByLayout(layout_level)
	local star_num = 0
	if next(self.zhushenta_data_list) == nil then
		return star_num
	end
	--print_error(self.zhushenta_data_list.kill_god_tower_fb_star_num)
	for i, v in pairs(self.zhushenta_data_list.kill_god_tower_fb_star_num) do
		if i == layout_level then
			star_num = v
		end
	end
	return star_num
end

--获取助战荣誉和最高层数
function FuBenPanelWGData:GetHelpHonorAndMaxLevel()
	local other_list = self.killgod_fb_cfg_auto.other[1]
	return other_list.honor_value ,other_list.cengshu_max
end

--function FuBenPanelWGData:GetCoinLevelFlag()
--	local copper_scence_info = FuBenPanelWGData.Instance:GetCopperScenceInfo()
--	local copper_info = self:GetTongBiInfo()
--	local other_cfg = self:GetTongBiBenOtherCfg()
--	local enter_tb_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
--	local limit_cfg = self:GetCopperTitleCfg()
--	local enter_next_flag = 0
--	if not next(self.copper_scence_info) then return enter_next_flag end
--
--	local next_info = nil
--	for k,v in ipairs(limit_cfg) do
--		if v.layer == self.copper_scence_info.cur_layer then
--			next_info = limit_cfg[k + 1]
--			break
--		end
--	end
--
--	if not next_info then return enter_next_flag end
--	if next_info.level_limit <= RoleWGData.Instance.role_vo.level and self.copper_scence_info.cur_star_num >= 3 and enter_tb_times > 0 then
--		enter_next_flag = 1 							--可以挑战下一关
--	elseif self.copper_scence_info.cur_star_num < 3 and enter_tb_times > 0 then
--		enter_next_flag = 2 							--只能挑战当前关卡
--	end
--	return enter_next_flag
--end

-- 主界面红点提示
function FuBenPanelWGData:IsShowFubenRedPoint()
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowTianXianRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowXianLingShengDianRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowTianShenFbRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowBaGuaMiZhenFbRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLongWangBaoZangRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLiuDaoLunHuiRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLianYuRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowYuanGuXianDianRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowManHuangGuDianRedPoint() then
		return 1
	end
	--if ShowRedPoint.SHOW_RED_POINT == self:IsShowZhuShenTaRedPoint() then
	--	return 1
	--end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowBootybayRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowControlBeastsRedPoint() then
		return 1
	end
	
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowBeautyRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowWuHunRedPoint() then
		return 1
	end
	
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowRuneTowerRedPoint() then
		return 1
	end

	return 0
end
--天仙阁(凡人修真)
function FuBenPanelWGData:IsShowTianXianRedPoint()
	--战力大于当前可挑战的层数时红点提示,并且没打满
	if self:CheckRedPointTianXianGe()then
		return 1
	end
	--阶段奖励红点提示
	if self:CheckLevelReward() then
		return 1
	end
	--章节奖励红点提示
	-- if self:CheckChapter() then
	-- 	return 1
	-- end
	--宝箱可领取红点提示
	-- if self:CheckBox() then
	-- 	return 1
	-- end
	return 0
end

function FuBenPanelWGData:GetChapterRewardRemindList()
	local remind_chapter_list = {}
	local max_chapter_count = self:GetMaxChapterCount()
	for i= 1, max_chapter_count do
		local can_fetch = self:GetChapterCanFetch(i)
		if can_fetch then
			remind_chapter_list[i] = true
		end
	end
	return remind_chapter_list
end

function FuBenPanelWGData:GetLevelRewardRemindList()
	local remind_level_list = {}
	local cfg = self.fanren_xiuzhen_map
	for k1,v1 in pairs(cfg) do
		for k2,v2 in pairs(v1) do
			if v2.box == 1 and self:GetLevelCanFetch(v2.level) then
				remind_level_list[k1] = true
				break
			end
		end
	end
	return remind_level_list
end

function FuBenPanelWGData:CheckChapter()
	local max_chapter_count = self:GetMaxChapterCount()
	for i= 1, max_chapter_count do
		local can_fetch = self:GetChapterCanFetch(i)
		if can_fetch then
			return true
		end
	end
	return false
end

-- 计算凡人修真阶段奖励分组红点
function FuBenPanelWGData:CountFanrenxiuzhenRewardRemind(group_index)
	local pass_level = self:GetPassLevel()
	-- 计算指定group_index
	if group_index then
		local cfg = self:GetFanrenxiuzhenRewardPreviewBySeq(group_index)
		local level_cfg_list= self:GetLevelRewardCfgList(cfg.level_origin, cfg.level_end)
		local have_remind = false
		local remind_index = #level_cfg_list
		for i, v in ipairs(level_cfg_list) do
			local is_receive = FuBenPanelWGData.Instance:GetLevelRewardFlag(v.seq)
			local is_pass = pass_level >= v.level
			if is_pass and not is_receive then
				remind_index = i
				have_remind = true
			end
		end
		if have_remind then
			return group_index, remind_index
		end
		return 0,#level_cfg_list
	end

end

-- 获取凡人修真阶段奖励
function FuBenPanelWGData:CheckLevelReward()
	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	local seq = 0;
	local end_is_pass = false
	for i = 0, #self.fanren_xiuzhen_level_reward do
		local value = self.fanren_xiuzhen_level_reward[i]
		local is_receive = FuBenPanelWGData.Instance:GetLevelRewardFlag(value.seq)
		local is_pass = pass_level >= value.level
		if is_pass then
			seq = i
			end_is_pass = true
		end
		if is_pass and not is_receive then
			return true, seq
		end

	end
	if end_is_pass then
		return false, seq + 1
	else
		return false, seq 
	end
end

-- 根据阶段奖励seq 获取对应的group_seq
function FuBenPanelWGData:GetFanrenxiuzhenRewardGroupSeq(seq)
	for i, v in ipairs(self.reward_preview) do
		if seq>= v.level_origin and seq<=v.level_end then
			return v.seq
		end
	end
	return 1
end

-- 获取凡人修真奖励总览数据
function FuBenPanelWGData:GetFanrenxiuzhenRewardPreviewBySeq(seq)
	return self.reward_preview[seq] or {}
end

-- 获取凡人修真奖励总览数据
function FuBenPanelWGData:GetFanrenxiuzhenRewardPreview()
	return self.reward_preview
end

function FuBenPanelWGData:CheckBox()
	local cfg = self.fanren_xiuzhen_map
	for k1,v1 in pairs(cfg) do
		for k2,v2 in pairs(v1) do
			if v2.box == 1 and self:GetLevelCanFetch(v2.level) then
				return true
			end
		end
	end
	return false
end

function FuBenPanelWGData:GetFirstCanFetchBox()
	if nil ~= self.cache_first_can_fatch_chapter and nil ~= self.cache_first_can_fatch_sublevel then
		return self.cache_first_can_fatch_chapter, self.cache_first_can_fatch_sublevel
	end

	local cfg = self.fanren_xiuzhen_level_map
	for i, v in ipairs(cfg) do
		if v.box == 1 then
			self.cache_first_can_fatch_chapter = v.chapter
			self.cache_first_can_fatch_sublevel = v.sub_level
			return v.chapter, v.sub_level
		end
	end

	return -1, -1
end

function FuBenPanelWGData:CheckRedPointTianXianGe()
	local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
	local pass_level = self:GetPassLevel()
	pass_level = pass_level <= 0 and 1 or pass_level
    local level_cfg = self:GetCfgByLevel(pass_level + 1)
    local role_lv = RoleWGData.Instance:GetRoleLevel()
 	if level_cfg and level_cfg.capability <= role_cap and level_cfg.open_level <= role_lv then
 		return true
	end
	return false
end

function FuBenPanelWGData:SetIsShowTianXianGePoint(show)
	self.IsShowTianXianGePoint = show
end

function FuBenPanelWGData:GetIsShowTianXianGePoint()
	return self.IsShowTianXianGePoint
end

--仙灵圣殿
function FuBenPanelWGData:IsShowXianLingShengDianRedPoint()
	local is_show_point = self:CheckPetCount()
	if is_show_point then
		return 1
	else
		return 0
	end
end


--天神
function FuBenPanelWGData:IsShowTianShenFbRedPoint()
	local is_show_point = self:CheckTianShenCount()

	if is_show_point then
		return 1
	else
		return 0
	end
end

--八卦迷阵
function FuBenPanelWGData:IsShowBaGuaMiZhenFbRedPoint()
	local is_show_point = self:CheckBaGuaMiZhenCount()
	if is_show_point then
		return 1
	else
		return 0
	end
end

--蓬莱探宝
function FuBenPanelWGData:IsShowBootybayRedPoint()
	local is_accepted = BootyBayWGData.Instance:GetIsAcceptWaBaoTask()
	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()

	if not is_accepted then
		return 1
	else
		local wabao_times = BootyBayWGData.Instance:GettDailyWaBaoRemainCount()
		if other_cfg.task_wabao_times - wabao_times > 0 then
			return 1
		end
	end
	return 0
end

--幻兽副本
function FuBenPanelWGData:IsShowControlBeastsRedPoint()
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbControlBeastsType)
	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbControlBeastsType)
	local can_enter_times = team_type_cfg and team_type_cfg.max_times or 0
	if can_enter_times - enter_times  > 0 then
		return 1
	end 

	return 0
end

--女神副本
function FuBenPanelWGData:IsShowBeautyRedPoint()
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbBeautyType)
	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbBeautyType)
	local can_enter_times = team_type_cfg and team_type_cfg.max_times or 0
	if can_enter_times - enter_times  > 0 then
		return 1
	end 

	return 0
end

--武魂副本
function FuBenPanelWGData:IsShowWuHunRedPoint()
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbWuHunType)
	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbWuHunType)
	local can_enter_times = team_type_cfg and team_type_cfg.max_times or 0
	if can_enter_times - enter_times  > 0 then
		return 1
	end 

	return 0
end

-- 符文塔
function FuBenPanelWGData:IsShowRuneTowerRedPoint()
	local not_day_limit = NewTeamWGData.Instance:CheckIsOpenDayLimit(GoalTeamType.FbRuneTowerType)
	if not not_day_limit then
		return 0
	end

	local enter_times = FuBenTeamCommonTowerWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbRuneTowerType)
	local team_type_cfg =  FuBenTeamCommonTowerWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbRuneTowerType)
	local can_enter_times = team_type_cfg and team_type_cfg.max_times or 0
	if can_enter_times - enter_times  > 0 then
		return 1
	end 

	return 0
end

--金蟾宝库
function FuBenPanelWGData:IsShowLongWangBaoZangRedPoint()
	local is_show_point = CopperFbWGData.Instance:CheckRemainTimes()
	if is_show_point then
		return 1
	else
		return 0
	end
end

--六道轮回
function FuBenPanelWGData:IsShowLiuDaoLunHuiRedPoint()
	local is_show_point = FuBenPanelWGCtrl.Instance.view:CheckTaFangCount()
	if is_show_point then
		return 1
	else
		return 0
	end
end

--炼狱洞窟
function FuBenPanelWGData:IsShowLianYuRedPoint()
    local is_show_point = FuBenPanelWGCtrl.Instance.view:CheckTeamExpCount()
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local limit_lv = WuJinJiTanWGData.Instance:GetTeamExpRedlimitLv()
	if is_show_point and role_lv >= limit_lv then
		return 1
	else
		return 0
	end
end

--远古仙殿
function FuBenPanelWGData:IsShowYuanGuXianDianRedPoint()
	local is_show_point = TeamEquipFbWGData.Instance:CheckYuanGuXianDianCount()
	if is_show_point then
		return 1
	else
		return 0
	end
end
--蛮荒古殿
function FuBenPanelWGData:IsShowManHuangGuDianRedPoint()
	local is_show_point = ManHuangGuDianWGData.Instance:GetIsShowRedPoint()
	if is_show_point then
		return 1
	else
		return 0
	end
end

--诛神塔
function FuBenPanelWGData:IsShowZhuShenTaRedPoint()
	local cur_count = self:GetZhuShenTaFBCount()
	local is_show_point = cur_count > 0
	if is_show_point then
		return 1
	else
		return 0
	end
end

function FuBenPanelWGData:SetSaoDangMark(is_saodang_mark)
	self.is_saodang_mark = is_saodang_mark
end

function FuBenPanelWGData:GetSaoDangMark()
	return self.is_saodang_mark
end


---------------------凡人修真-----------------------------


--获取当前章节
function FuBenPanelWGData:GetCurChapter(chapter)
	if nil == self.fanren_xiuzhen_map or nil == chapter then
		return nil
	end
	return self.fanren_xiuzhen_map[chapter]
end

--获取配置
function FuBenPanelWGData:GetXiuXianCfg(chapter, level)
	return self.fanren_xiuzhen_map[chapter][level]
end

--获取当前配置
function FuBenPanelWGData:GetCurXiuXianCfg(level)
	if level > self:GetCfgMaxLevel() then
		return nil
	end
	level = level == 0 and 1 or level
	return self.fanren_xiuzhen_level_map[level]
end

-- 获取层数
function FuBenPanelWGData:GetFanrenxiuzhenMaxLevel()
	return #self.fanren_xiuzhen_level_map
end

--获取当前能达到的最高层数
function FuBenPanelWGData:GetCurCanQuickFinishLevel(level)
	local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
	local role_lv = RoleWGData.Instance:GetRoleLevel()
	local temp_level = level
	local is_level_limit = 0

	while(true) do
		if not role_cap then
			break
		end

		temp_level = temp_level + 1
		local cfg = self:GetCurXiuXianCfg(temp_level)
		if not cfg then
			break
		end

		if cfg.capability > role_cap  then
			temp_level = temp_level - 1
			break
		end

		if role_lv < cfg.open_level then
			temp_level = temp_level - 1
			is_level_limit = cfg.open_level
			break
		end
	end

	return temp_level, is_level_limit
end

--获取最大章节数
function FuBenPanelWGData:GetMaxChapterCount()
	return #self.tianxiangefbcfg_auto.chapter_reward --12
end
--获取每章多少关
function FuBenPanelWGData:GetOnceChapterLevelCount()
	return 10
end

function FuBenPanelWGData:GetFanRenXiuZhenCfgDataList()
	local chapter = self:GetChapter()
	-- print_error("获取章节 >>>>>> ", chapter)
	local data_list = {}
	for i,v in pairs(self.fanren_xiuzhen_map[chapter]) do
		--table.insert(data_list, v)
		data_list[v.level - (chapter -1) * 10] = v
	end
	return data_list
end

function FuBenPanelWGData:GetChapter()
	local level = self:GetPassLevel()
	if level == 0 then
		level = 1
	end
	local cfg = self.fanren_xiuzhen_level_map[level + 1]
	if cfg then
		return cfg.chapter
	end
	local max_cfg = self.tianxiangefbcfg_auto.level_cfg[#self.tianxiangefbcfg_auto.level_cfg]
	if max_cfg then
		return max_cfg.chapter
	end
	return self.fanren_xiuzhen_level_map[1].chapter
end

--获取关卡概率掉落物品列表 --is_next  获取下一关的
function FuBenPanelWGData:GetFanRenXiuZhenRewardItemList(is_next)
	local level = self:GetPassLevel()
	local max_level = self:GetCfgMaxLevel()
	if is_next then
		if level < max_level then
			level = level + 1
		else
			return {}
		end
	end
	level = level <= 0 and 1 or level
	local cfg = self:GetTerraceRewardByLevel(level)
	if cfg then
		local data_list = {}
		for k,v in pairs(cfg.actiion_reward_item) do
			local data = {}
			data.item_id = v.item_id
			data.is_bind = v.is_bind
			data.num = v.num
			data.show_duobei = true
			data.task_type = RATSDUOBEI_TASK.FANRENXIUXIAN
			table.insert(data_list, data)
        end
        table.sort(data_list, function (a, b)
            if a == nil or b == nil or a == b then
                return false
            end
            local item_cfg1, big_type = ItemWGData.Instance:GetItemConfig(a.item_id)
            local item_cfg2, big_type = ItemWGData.Instance:GetItemConfig(b.item_id)
            return item_cfg1.color > item_cfg2.color
            end)
		return data_list
	end
	return {}
end

--获取关卡结算掉落物品列表
function FuBenPanelWGData:GetFanRenXiuZhenPassRewardItemList()
	local pass_cfg = self:GetWelkinInfo()
	if pass_cfg then
		local data_list = {}
		for k,v in pairs(pass_cfg.reward_item) do
			if v.item_id and v.item_id ~= 0 then
				local data = {}
				data.item_id = v.item_id
				data.is_bind = v.is_bind
				data.num = v.num
				data.show_duobei = true
				data.task_type = RATSDUOBEI_TASK.FANRENXIUXIAN
				table.insert(data_list, data)
			end
		end

		local level = self:GetPassLevel()
		level = level <= 0 and 1 or level
		local cfg = self:GetTerraceRewardByLevel(level)
		if cfg then
			for k,v in pairs(cfg.actiion_reward_item) do
				if k > cfg.drop_item_num -1 then
					local data = {}
					data.item_id = v.item_id
					data.is_bind = v.is_bind
					data.num = v.num
					data.show_duobei = true
					data.task_type = RATSDUOBEI_TASK.FANRENXIUXIAN
					table.insert(data_list, data)
				end
			end
		end
		return data_list
	end
	return nil
end

-- 获取指定范围的阶段奖励配置
function FuBenPanelWGData:GetLevelRewardCfgList(min_seq,max_seq)
	local cfg_list = {}
	for i = max_seq, min_seq, -1 do
		if self.fanren_xiuzhen_level_reward[i] then
			table.insert(cfg_list, self.fanren_xiuzhen_level_reward[i])
		end
	end
	return cfg_list
end	

-- 获取章节奖励配置
function FuBenPanelWGData:GetChapterCfg(chapter)
	return self.fanren_chapter_reward_map[chapter]
end	


--获取章节奖励物品列表--副本内
function FuBenPanelWGData:GetChapterRewardItemList(chapter,is_fb)
	local max = self:GetMaxChapterCount()
	local cfg = nil

	local level = self:GetPassLevel()
	local next_level = -1
	for k,v in pairs(self.fanren_level_chapter_reward) do
		if k > level then
			if next_level == -1 then
				next_level = k
			elseif next_level > k then
				next_level = k
			end
		end
	end
	if next_level == -1 then
		cfg = self.fanren_chapter_reward_map[chapter]
	else
		cfg = self.fanren_level_chapter_reward[next_level]
	end
	-- for i = chapter,max do
	-- 	cfg = self.fanren_chapter_reward_map[chapter]
	-- 	if cfg.client_chapter_item ~= 0 then
	-- 		break
	-- 	end
	-- 	chapter = chapter+1
	-- end
	local data_list = {}
	if IsEmptyTable(cfg) then
		return data_list
	end

	for k,v in pairs(cfg.client_chapter_item) do
		local data = {}
		data.level =cfg.level
		data.chapter = chapter
		data.item_id = v.item_id
		data.is_bind = v.is_bind
		data.num = v.num
		data.show_duobei = true
		data.task_type = RATSDUOBEI_TASK.FANRENXIUXIAN
		data.open_slot = cfg.open_slot
		data.index = k + 1
		data.is_fb = is_fb or false
		table.insert(data_list, data)
	end

	if IsEmptyTable(data_list) then
		for i=1,3 do
			local data = {}
			data.level =cfg.level
			data.chapter = chapter
			data.item_id = 0
			data.is_bind = 0
			data.num = 0
			data.show_duobei = false
			data.task_type = RATSDUOBEI_TASK.FANRENXIUXIAN
			data.open_slot = 0
			data.index = i
			data.is_fb = is_fb or false
			table.insert(data_list, data)
		end
    end
  	return data_list
end

function FuBenPanelWGData:GetLevelChapterRewardItem(level)
	if level and self.fanren_level_chapter_reward[level] then
		return true,self.fanren_level_chapter_reward[level].client_chapter_item
	else
		return false, {}
	end
end

function FuBenPanelWGData:GetLevelChapterOpenSlot(level)
	if level and self.fanren_level_chapter_reward[level] then
		return self.fanren_level_chapter_reward[level].open_slot
	else
		return nil
	end
end

-- 获取凡人修真阶段奖励
function FuBenPanelWGData:GetFanrenxiuzhenLevelRwawrdData()
	return self.fanren_xiuzhen_level_reward or {}
end

-- 获取凡人修真阶段奖励数量 从0开始
function FuBenPanelWGData:GetFanrenxiuzhenLevelRwawrdCount()
	return #self.fanren_xiuzhen_level_reward+1 or 0
end

-- 获取凡人修真阶段奖励 by seq
function FuBenPanelWGData:GetFanrenxiuzhenLevelRwawrdDataBySeq(seq)
	return self.fanren_xiuzhen_level_reward[seq]
end

-- 获取凡人修真阶段奖励 by seq
function FuBenPanelWGData:GetFanrenxiuzhenLevelRwawrdDataByLevel(level)
	return self.fanren_xiuzhen_level_reward_level[level]
end

-- 获取凡人修真章节奖励 by level
function FuBenPanelWGData:GetFanrenxiuzhenChapterCfgByLevel(level)
	return self.fanren_level_chapter_reward[level]
end

--获取宝箱奖励物品列表
function FuBenPanelWGData:GetBoxRewardItemList(chapter, level)
	local cfg = self:GetCfgByLevel(level)
	local data_list = {}
	for k,v in pairs(cfg.client_box_show) do
		table.insert(data_list, v)
	end
  	return data_list
end

function FuBenPanelWGData:GetRankStr(level)
	local once_chapter_count = self:GetOnceChapterLevelCount()
	local chapter = math.ceil(level / once_chapter_count)
	chapter = chapter <= 0 and 1 or chapter
	local layer = level % once_chapter_count
	if layer == 0 and level >= once_chapter_count then
		--打到当前章节的最后一层
		layer = once_chapter_count
	end

	local cfg = self:GetCurXiuXianCfg(level)
	if not cfg then
		--print_error("获取不到配置", chapter , level)
		return Language.FanRenXiuZhen.RankValueEmpty
	end
	return string.format(Language.FanRenXiuZhen.RankValue, cfg.title_name, layer)
end

function FuBenPanelWGData:GetKillMonsterStr(level_param)
	local level = level_param or self:GetPassLevel()
	local str = self:GetRankStr(level)
	local monster_name = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self:GetCfgByLevel(level).boss_id].name
	return string.format(Language.FanRenXiuZhen.SuccessKillMonster, str, monster_name)
end


function FuBenPanelWGData:GetHistoryTitleNameAndLayer()
	local level = self:GetPassLevel()
	local once_chapter_count = self:GetOnceChapterLevelCount()
	local chapter = math.ceil(level / once_chapter_count)
	chapter = chapter <= 0 and 1 or chapter
	local layer = level % once_chapter_count
	if layer == 0 and level >= once_chapter_count then
		--打到当前章节的最后一层
		layer = once_chapter_count
	end

	local map = self:GetCurChapter(chapter)
	level = level <= 0 and 1 or level
	if map and map[level] then
		local level_capability = map[level].capability or 0
		return map[level].title_name ,layer,level_capability
	end

	return "", 0
end

function FuBenPanelWGData:GetXiuXianLayer(level)
	local once_chapter_count = self:GetOnceChapterLevelCount()
	local layer = level % once_chapter_count
	if layer == 0 and level >= once_chapter_count then
		--打到当前章节的最后一层
		layer = once_chapter_count
	end
	return layer
end

function FuBenPanelWGData:SetRewardInfo(level_info, chapter_info)
	self.xiuzhen_level_fetch_list = level_info
	self.xiuzhen_chapter_fetch_list = chapter_info
	-- print_error(self.xiuzhen_level_fetch_list)
	-- print_error(self.xiuzhen_chapter_fetch_list)
end

function FuBenPanelWGData:GetLevelCanFetch(level)
 	 -- print_error(level, level <= self:GetPassLevel(),  self.xiuzhen_level_fetch_list[level])
	return self.xiuzhen_level_fetch_list[level] == 0 and level <= self:GetPassLevel()
end

function FuBenPanelWGData:GetLevelRewardIsFetch(level)
	return self.xiuzhen_level_fetch_list[level] == 1
end

function FuBenPanelWGData:GetChapterCanFetch(chapter_param)
	if not chapter_param then return false end
	local level = self:GetPassLevel()
	local once_chapter_count = self:GetOnceChapterLevelCount()
	--真实达到
	local chapter = math.floor(level / once_chapter_count)
	if chapter_param <= chapter then
		 --print_error("=======   ", self.xiuzhen_chapter_fetch_list[chapter], chapter, chapter_param)
		if self.xiuzhen_chapter_fetch_list[chapter_param] == 0 then
			return true
		end
	end
	return false
end

function FuBenPanelWGData:GetChapterRewardIsFetch(chapter)
	-- print_error(self.xiuzhen_chapter_fetch_list[chapter])
	return self.xiuzhen_chapter_fetch_list[chapter] == 1
end

function FuBenPanelWGData:GetZhuShenTaCfgByLevel(level)
	return self.zhushenta_level_cfg[level] or {}
end

---------------------凡人修真end--------------------------

---------------------天神副本start--------------------------


--初始化天神副本配置
function FuBenPanelWGData:InitTianShenConfig()
	local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_fb_auto")
	self.tianshen_map = ListToMap(cfg.scene_layer_cfg, "layer")

	--三星必掉落列表  key：[layer][item_id]
	self.three_drop_list = {}
	local count = 0
	for i, v in pairs(self.tianshen_map) do
		local split_tab = Split(v.item, "|")
		if not self.three_drop_list[i] then
			self.three_drop_list[i] = {}
		end
		for k1, v1 in pairs(split_tab) do
			self.three_drop_list[i][tonumber(v1)] = true
		end
		count = count + 1
	end
	--最大层数
	self.tianshen_fb_max_layer = count
	--额外次数表
	self.extra_times_map = {}
	count = 0
	for i, v in pairs(cfg.extra_time_cfg) do
		count = count + 1
		self.extra_times_map[v.extra_times] = v
	end
	self.max_extra_times = count
end

--获取天神数据
function FuBenPanelWGData:GetTianShenDataList()
	local level = RoleWGData.Instance:GetAttr("level")
	local data_list = {}
	for i = 1, 30 do
		if self.tianshen_map[i] and level >= self.tianshen_map[i].number_show then
			table.insert(data_list, self.tianshen_map[i])
		else
			break
		end
	end

	local reverse_tab = {}
	for i = 1, #data_list do
		reverse_tab[i] = table.remove(data_list)
	end

	return reverse_tab
end

function FuBenPanelWGData:GetTianShenBossPosRotationScale(layer)
	local cfg = self.tianshen_map[layer]
	if cfg then
		local pos = Split(cfg.huanhua_position, "|")
		local rotation = Split(cfg.huanhua_rotation, "|")
		local scale = cfg.huanhua_scale
		return pos, rotation, scale
	end
	return nil,nil,nil
end

--获取天神副本三星必掉落
function FuBenPanelWGData:GetTianShenThreeStarMustDrop(layer)
	local cfg = self.tianshen_map[layer]
	if cfg then
		local data_list = Split(cfg.item, "|")
		return data_list
	end
	--print_error("获取天神副本三星必掉落， 找不到配置 ：", layer)
	return {}
end

--获取天神副本概率掉落物品列表
function FuBenPanelWGData:GetTianShenDropList(layer)
	local cfg = self.tianshen_map[layer]
	if cfg then
		local data_list = {}
		for i = 0, 20 do
			if cfg.show_reward_item[i] then
				--三星必掉落加标记
				if self:_CheckIsThreeDrop(layer, cfg.show_reward_item[i].item_id) then
					cfg.show_reward_item[i].is_three_must_drop = true
				end
				cfg.show_reward_item[i].show_duobei = true
				cfg.show_reward_item[i].task_type = RATSDUOBEI_TASK.FENGSHENDIAN
				table.insert(data_list, cfg.show_reward_item[i])
			else
				break
			end
		end
		return data_list
	end
	return {}
end

--检测是否三星必掉落
function FuBenPanelWGData:_CheckIsThreeDrop(layer, item_id)
	if self.three_drop_list[layer] then
		return self.three_drop_list[layer][item_id]
	end
	return false
end

--获取天神副本名字（横）
function FuBenPanelWGData:GetTianShenFbName(layer)
	local cfg = self.tianshen_map[layer]
	if cfg then
		return cfg.fb_name
	end
	--print_error("获取天神副本名字， 找不到配置 ：", layer)
	return ""
end

--获取天神副本名字（竖）
function FuBenPanelWGData:GetTianShenFbVerName(layer)
	local cfg = self.tianshen_map[layer]
	if cfg then
		return cfg.copy_fb_name, cfg.fb_name
	end
	--print_error("获取天神副本名字， 找不到配置 ：", layer)
	return "", ""
end

--获取天神副本BOSS配置
function FuBenPanelWGData:GetTianShenFbBossCfg(layer)
	local cfg = self.tianshen_map[layer]
	if cfg then
		local boss_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[cfg.boss_id]
		return boss_config
	end
	--print_error("获取天神副本BOSS配置， 找不到配置 ：", layer)
	return ""
end

--获取天神副本所需等级
function FuBenPanelWGData:GetTianShenFbNeedLevel(layer)
	local cfg = self.tianshen_map[layer]
	if cfg then
		return cfg.need_level
	end
	--print_error("获取天神副本所需等级， 找不到配置 ：", layer)
	return ""
end

function FuBenPanelWGData:GetTianShenFbCfg(layer)
	return self.tianshen_map[layer]
end

--获取天神副本最大层数
function FuBenPanelWGData:GetTianShenMaxLayerCount()
	return self.tianshen_fb_max_layer
end

--获取天神副本每日进入免费次数
function FuBenPanelWGData:GetTianShenDayFreeTimes()
	local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_fb_auto")
	return cfg.other[1].day_enter_free_times
end

--获取天神副本扫荡等级限制
function FuBenPanelWGData:GetTianShenSaoDangLevelLimit(fb_type)
	local info = self:GetSaoDangLevelLimit(fb_type)
	if info then
		return info.level_limit
	end
	return 0
end

function FuBenPanelWGData:GetSaoDangLevelLimit(fb_type)
	local cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto").fb_sweep
	if cfg then
		for k,v in pairs(cfg) do
			if v.type == fb_type then
				return v
			end
		end
	end

	return nil
end

--获取天神副本扫荡按钮显示等级限制
function FuBenPanelWGData:GetTianShenSaoDangShowLevelLimit()
	local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_fb_auto")
	return cfg.other[1].sweep_show_low
end


function FuBenPanelWGData:GetTianShenBuyComsumeGold(times)
	if self.extra_times_map[times] then
		return self.extra_times_map[times].cost_gold
	end
	if times >= self.max_extra_times then
		return self.extra_times_map[self.max_extra_times].cost_gold
	end
	--print_error("天神副本，获取次数配置错误，传入不存在的key：", times)
	return nil
end

function FuBenPanelWGData:GetTianShenItemId()
	local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_fb_auto")
	return cfg.other[1].fb_add_times_item_id
end

function FuBenPanelWGData:GetTianShenOtherCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_fb_auto")
	return cfg.other[1]
end

function FuBenPanelWGData:GetTianShenSceneLogicCfg()
	if Scene.Instance:GetSceneType() == SceneType.TIAN_SHEN_FB then
		local scene_id = Scene.Instance:GetSceneId()
		for i, v in pairs(self.tianshen_map) do
			if v.scene_id == scene_id then
				return v
			end
		end
	end
	return nil
end

function FuBenPanelWGData:GetTianShenFirstLayerSceneLogicCfg()
	return self.tianshen_map[1]
end

--获取天神减少CD万分比
function FuBenPanelWGData:GetTianShenDecreaseCD()
	local other = self:GetTianShenOtherCfg()
	return other.buff_decrease_cd
end

-- 天神副本协议数据start

--设置三星通关层数
function FuBenPanelWGData:SetTianShenPassLayer(layer)
	self.tianshen_pass_layer = layer
	--设置完，记录已经获得过的物品标记
	self:RecordHasGetItemList(layer)
end
--获取三星通关层数
function FuBenPanelWGData:GetTianShenPassLayer()
	return self.tianshen_pass_layer - 1 or 0
end

function FuBenPanelWGData:GetTianShenCurLayer()
	return self.tianshen_pass_layer or 1
end

--设置当前层数星数
function FuBenPanelWGData:SetTianShenStarCount(count)
	self.cur_layer_star_count = count
end
--获取当前层数星数
function FuBenPanelWGData:GetTianShenStarCount()
	return self.cur_layer_star_count or -1
end

--记录之前关卡所有可能获得的物品
function FuBenPanelWGData:RecordHasGetItemList(layer)
	if not self.has_get_item_list then
		self.has_get_item_list = {}
	end
	if layer <= 1 then
		return
	end
	local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_fb_auto")
	for i, v in ipairs(cfg.scene_layer_cfg) do
		--三星通关的记录物品id
		if self:GetStarCountByLayer(i) == 3 then
			for k1, v1 in pairs(v.show_reward_item) do
				if not self.has_get_item_list[v1.item_id] then
					self.has_get_item_list[v1.item_id] = true
				end
			end
		end
	end
end

--是否新标记
function FuBenPanelWGData:GetIsNew(item_id)
	if not self.has_get_item_list then
		return
	end
	return not self.has_get_item_list[item_id]
end

function FuBenPanelWGData:GetStarCountByLayer(layer)
	if layer == self.tianshen_pass_layer then
		return self:GetTianShenStarCount()
	end
	if layer < self.tianshen_pass_layer then
		return 3
	end
	if layer > self.tianshen_pass_layer then
		return -1
	end
end

function FuBenPanelWGData:SetTianShenFbDayCount(type, times)
	if type == DAY_COUNT.DAYCOUNT_ID_TIANSHEN_FB_ENTER_TIMES then
		self.tianshen_fb_enter_times = times
	elseif type == DAY_COUNT.DAYCOUNT_ID_TIANSHEN_FB_VIP_BUY_TIMES then
		self.tianshen_fb_buy_times = times
	elseif type == DAY_COUNT.DAYCOUNT_ID_TIANSHEN_FB_ITEM_ADD_TIMES then
		self.tianshen_fb_comsume_item_times = times
	end
end

--获取天神副本进入次数
function FuBenPanelWGData:GetTianShenEnterTimes()
	return self.tianshen_fb_enter_times
end
--获取天神副本VIP购买次数
function FuBenPanelWGData:GetTianShenBuyTimes()
	return self.tianshen_fb_buy_times
end
--获取天神副本消耗物品增加次数
function FuBenPanelWGData:GetTianShenComsumeItemTimes()
	return self.tianshen_fb_comsume_item_times
end
--获取天神副本剩余可进入次数
function FuBenPanelWGData:GetTianShenCanEnterTimes()
	local day_free_times = self:GetTianShenDayFreeTimes()
	local day_enter_times = self:GetTianShenEnterTimes()
	local day_buy_times = self:GetTianShenBuyTimes()
	local day_comsume_item_times = self:GetTianShenComsumeItemTimes()
	if day_free_times and day_enter_times and day_buy_times and day_comsume_item_times then
		return day_free_times + day_buy_times + day_comsume_item_times - day_enter_times
	end
	return 0
end
--检测天神副本红点
function FuBenPanelWGData:CheckTianShenCount()
	if self:GetTianShenCanEnterTimes() > 0 then
		return true
	end
	return false
end

--检测天神副本加号特效
function FuBenPanelWGData:CheckTianShenEffect()
	if not self:CheckVipCondition() then
		return false
	end
	if self:GetTianShenCanEnterTimes() <= 0 then
		local param = VipPower.Instance:GetParam(VipPowerId.reward_tianshen_fb_buy_times)
		local day_buy_times = self:GetTianShenBuyTimes()
		if param > day_buy_times then
			return true
		end
	end
	return false
end

-- 天神副本协议数据end

---------------------天神副本end----------------------------

---------------------八卦迷阵start-------------------------------
--初始化八卦迷阵副本配置
function FuBenPanelWGData:InitBaGuaMiZhenConfig()
	local baguazhen_cfg = ConfigManager.Instance:GetAutoConfig("baguamizhenfb_cfg_auto")
	--额外次数表,不同的次数有不同的价格
	self.baguamizhen_extra_num= {}
	local count = 0
	for i, v in pairs(baguazhen_cfg.buy_price) do
		count = count + 1
		self.baguamizhen_extra_num[v.buy_time] = v
	end
	self.max_baguamizhen_extra_times = count
end

function FuBenPanelWGData:SetBaGuaMiZhenFbDayCount(type, times)
	if type == DAY_COUNT.DAYCOUNT_ID_BAGUAMIZHEN_FB_ENTER_TIMES then
		self.bagaumizhen_fb_enter_times = times
	elseif type == DAY_COUNT.DAYCOUNT_ID_BAGUAMIZHEN_FB_VIP_BUY_TIMES then
		self.baguamizhen_fb_buy_times = times
	end
end

--获取八卦迷阵副本今日进入次数
function FuBenPanelWGData:GetBaGuaMiZhenEnterTimes()
	return self.bagaumizhen_fb_enter_times
end


--获取八卦迷阵副本免费次数
function FuBenPanelWGData:GetBaGuaMiZhenDayFreeTimes()
	local cfg = ConfigManager.Instance:GetAutoConfig("baguamizhenfb_cfg_auto")
	return cfg.other[1].free_times
end

--获取八卦迷阵副本今日VIP购买次数
function FuBenPanelWGData:GetBaGuaMiZhenBuyTimes()
	return self.baguamizhen_fb_buy_times
end

--可用次数
function FuBenPanelWGData:GetBaGuaMiZhenCanEnterTimes()
	local day_free_times = self:GetBaGuaMiZhenDayFreeTimes()
	local day_enter_times = self:GetBaGuaMiZhenEnterTimes()
	local day_buy_times = self:GetBaGuaMiZhenBuyTimes()
	--可用 = 免费 + 买的 - 用的
	if day_free_times and day_enter_times and day_buy_times then
		return day_free_times + day_buy_times - day_enter_times
	end

	return 0
end


function FuBenPanelWGData:GetBaGuaMiZhenBuyComsumeGold(times)
	if self.baguamizhen_extra_num[times] then
		return self.baguamizhen_extra_num[times].price
	end
	if times >= self.max_baguamizhen_extra_times then
		return self.baguamizhen_extra_num[self.max_baguamizhen_extra_times].price
	end
	return nil
end
--检测八卦迷阵副本红点
function FuBenPanelWGData:CheckBaGuaMiZhenCount()
	if self:GetBaGuaMiZhenCanEnterTimes() > 0 then
		return true
	end
	return false
end

--检测八卦迷阵副本加号特效
function FuBenPanelWGData:CheckBaGuaMiZhenEffect()
	if not self:CheckVipCondition() then
		return false
	end
	if self:GetBaGuaMiZhenCanEnterTimes() <= 0 then
		local param = VipPower.Instance:GetParam(VipPowerId.reward_baguamizhen_fb_buy_times)
		local day_buy_times = self:GetBaGuaMiZhenBuyTimes()
		if param > day_buy_times then
			return true
		end
	end
	return false
end

---------------------八卦迷阵副本end----------------------------
---------------------宠物本start-------------------------
function FuBenPanelWGData:InitPetConfig()
	--关卡map
	self.new_pet_level_map = ListToMap(self.new_petfb_cfg_auto.level, "level")
	--三星必掉落列表  key：[layer][item_id]
	self.pet_three_drop_list = {}
	local count = 0
	for i, v in pairs(self.new_pet_level_map) do
		local split_tab = Split(v.item, "|")
		if not self.pet_three_drop_list[i] then
			self.pet_three_drop_list[i] = {}
		end
		for k1, v1 in pairs(split_tab) do
			self.pet_three_drop_list[i][tonumber(v1)] = true
		end
		count = count + 1
	end
	--最大层数
	self.pet_fb_max_layer = count

    self.pet_buy_times_cfg = ListToMap(self.new_petfb_cfg_auto.buy_times, "buy_times")
    local count = 0
	for i, v in pairs(self.new_petfb_cfg_auto.buy_times) do
		count = count + 1
	end
	self.pet_max_buy_times = count
end

function FuBenPanelWGData:GetPetLevelCfg()
	local scene_id = Scene.Instance:GetSceneId()
	local is_fake = Scene.Instance:GetSceneType() == SceneType.FakePetFb
	if is_fake then
		for i, v in pairs(self.fake_petfb_cfg_auto.level) do
			if v.scene_id == scene_id then
				return v
			end
		end
	end

	for i, v in pairs(self.new_pet_level_map) do
		if v.scene_id == scene_id then
			return v
		end
	end
	return {}
end

--获取宠物本数据
function FuBenPanelWGData:GetPetFbDataList()
	local data_list = {}
	local has_fake = TaskGuide.Instance:CheckShowFakeSlFb()
	if has_fake then
		table.insert(data_list, {cfg = self.fake_petfb_cfg_auto.level[1], is_fake = true})
	end

	local level = RoleWGData.Instance:GetAttr("level")
	for i = 0, 30 do
		if self.new_pet_level_map[i] then
			if (level >= self.new_pet_level_map[i].number_show and (i > 0 or not has_fake))  then
				table.insert(data_list, {cfg = self.new_pet_level_map[i], has_fake = has_fake})
			end
		else
			break
		end
	end
	return data_list
end

--获取宠物本概率掉落物品列表
function FuBenPanelWGData:GetPetDropList(level)
	local cfg = self.new_pet_level_map[level]
	if cfg then
		local data_list = {}
		for i, v in pairs(cfg.reward_item) do
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			if item_cfg then
				v.sort = item_cfg.color
			end
			--三星必掉落加标记
			if self:_CheckPetIsThreeDrop(level, v.item_id) then
				v.is_three_must_drop = true
				v.sort = 9999
			end
			v.show_duobei = true
			v.task_type = RATSDUOBEI_TASK.SHOUHUXIANLING
			table.insert(data_list, v)
		end
		table.sort(data_list, SortTools.KeyUpperSorter("sort"))
		return data_list
	end
	--print_error("获取宠物概率掉落物品列表， 找不到配置 ：", level)
	return {}
end

--获取宠物本概率掉落物品列表
function FuBenPanelWGData:GetFakePetDropList()
	local cfg = self.fake_petfb_cfg_auto.level[1]
	if cfg then
		local data_list = {}
		for i, v in pairs(cfg.reward_item) do
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			if item_cfg then
				v.sort = item_cfg.color
			end
			--三星必掉落加标记
			if self:_CheckPetIsThreeDrop(0, v.item_id) then
				v.is_three_must_drop = true
				v.sort = 9999
			end
			v.show_duobei = true
			v.task_type = RATSDUOBEI_TASK.SHOUHUXIANLING
			table.insert(data_list, v)
		end
		table.sort(data_list, SortTools.KeyUpperSorter("sort"))
		return data_list
	end
	--print_error("获取宠物概率掉落物品列表， 找不到配置 ：", level)
	return {}
end

--检测宠物本是否三星必掉落
function FuBenPanelWGData:_CheckPetIsThreeDrop(layer, item_id)
	if self.pet_three_drop_list[layer] then
		return self.pet_three_drop_list[layer][item_id]
	end
	return false
end

function FuBenPanelWGData:GetPetComsumeGold(will_buy_times)
    if self.pet_buy_times_cfg[will_buy_times] then
		return self.pet_buy_times_cfg[will_buy_times].price
	end
	if will_buy_times > self.pet_max_buy_times then
		return self.pet_buy_times_cfg[self.pet_max_buy_times].price
	end
	return self.pet_buy_times_cfg[1].price
end

function FuBenPanelWGData:CheckPetCount()
	local pet_info = self:GetPetAllInfo()
	local other_cfg = self:GetPetOtherCfg()
	local role_level = RoleWGData.Instance.role_vo.level
	if role_level < 100 then
		return false
	end
	local enter_cw_times = other_cfg.free_times + pet_info.buy_times - pet_info.day_times
	return enter_cw_times > 0
end

function FuBenPanelWGData:GetPetCanEnterTimes()
	local pet_info = self:GetPetAllInfo()
	local other_cfg = self:GetPetOtherCfg()
	local enter_cw_times = other_cfg.free_times + pet_info.buy_times - pet_info.day_times
	return enter_cw_times
end

function FuBenPanelWGData:GetPetFubenProbability()
	if not next(self.pet_scence_info) then
		return
	end
	local cfg = self:GetPetTitleCfg()
	local layer = self.pet_scence_info.level
	local star = self.pet_scence_info.cur_star_num
	local str = {[0] = "zero", [1] = "one", [2] = "two", [3] = "three", }
	return cfg[layer + 1][str[star] .. "_star_Prob"]
end

function FuBenPanelWGData:GetPetFBCfgByLevel(level)
	return self.new_pet_level_map[level]
end

function FuBenPanelWGData:GetPetDifficulty(level)
	local pet_cfg =  self.new_petfb_cfg_auto.monster
	local config = {}
	for k,v in pairs(pet_cfg) do
		 if  level == v.level then
		 	table.insert(config,v)
		 end
	end
	 return config
end

function FuBenPanelWGData:GetPetBossId(cfg,num)
	for k,v in pairs(cfg) do
		 if  num-1 == v.wave and v.boss_id ~= 0 then
		 	return 1
		 end
	 end
	 return 0
end

--获取宠物本仙女配置
function FuBenPanelWGData:GetPetFbXianNvCfg(layer)
	local cfg = self.new_pet_level_map[layer]
	if cfg then
		local xiannv_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[cfg.xiannv_id]
		return xiannv_config
	end
	--print_error("获取天神副本BOSS配置， 找不到配置 ：", layer)
	return nil
end

--获取宠物本仙女配置
function FuBenPanelWGData:GetFakePetFbXianNvCfg()
	local cfg = self.fake_petfb_cfg_auto.level[1]
	if cfg then
		local xiannv_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[cfg.xiannv_id]
		return xiannv_config
	end
	--print_error("获取天神副本BOSS配置， 找不到配置 ：", layer)
	return ""
end



function FuBenPanelWGData:SetPetLevel(level)
	self.level = level
end

function FuBenPanelWGData:GetPetLevel(level)
	return	self.level or 0
end

function FuBenPanelWGData:GetPetItemByLayer(level, is_fake)
	if is_fake then
		return self.fake_petfb_cfg_auto.level[1]
	end
	return self.new_pet_level_map[level]
end

function FuBenPanelWGData:GetPetGoddessMaxHpByLevel(level)
 	local cur_goddess_id = -1
	local pet_cfg = self.new_petfb_cfg_auto.level
	if pet_cfg then
		for k,v in pairs(pet_cfg) do
			if v.level == level then
				cur_goddess_id = v.xiannv_id
				break
			end
		end
	end
	if cur_goddess_id == -1 then return -1 end
	local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto")
	if cfg then
		return cfg.monster_list[cur_goddess_id].shengming_max
	end
	return -1
end

function FuBenPanelWGData:GetPetOtherCfg()
	local ohter_cfg = self.new_petfb_cfg_auto.other[1]
	return ohter_cfg
end

function FuBenPanelWGData:SetPetFBReward(protocol)
	self.pet_reward_info.level = protocol.level
	self.pet_reward_info.is_sweep_fb = protocol.is_sweep_fb
	self.pet_reward_info.is_pass = protocol.is_pass
	self.pet_reward_info.get_exp = protocol.get_exp
	self.pet_reward_info.get_coin = protocol.get_coin
	self.pet_reward_info.star_num = protocol.star_num
	self.pet_reward_info.item_count = protocol.item_count
	self.pet_reward_info.item_list = protocol.item_list
end
function FuBenPanelWGData:GetPetFBReward()
	return self.pet_reward_info
end

--设置宠物本通关信息
function FuBenPanelWGData:SetPetAllInfo(protocol)
	self.pet_msg_info.pass_fb_max_level = protocol.pass_fb_max_level --当前通关的最大关卡等级
	self.pet_msg_info.day_times = protocol.day_times 				 --每天的已进入次数
	self.pet_msg_info.buy_times = protocol.buy_times 				 --每天vip购买次数
	self.pet_msg_info.pass_petfb_star = protocol.pass_petfb_star 	 --7个关卡 的星数
end

--当前通关的最大关卡等级
function FuBenPanelWGData:GetPetPassLayer()
	return self.pet_msg_info.pass_fb_max_level or 0
end

--获取宠物本跳转层数
function FuBenPanelWGData:GetPetCurLayer()
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	local ret_level = self.pet_msg_info.pass_fb_max_level or 1
	if self.new_pet_level_map[self.pet_msg_info.pass_fb_max_level + 1] then --尝试取下一关
		local cfg = self.new_pet_level_map[self.pet_msg_info.pass_fb_max_level + 1]
		--print_error(self.pet_msg_info.pass_fb_max_level + 1, self:SetPetStarDisplay(self.pet_msg_info.pass_fb_max_level + 1))
		if main_vo.level >= cfg.role_level and self:SetPetStarDisplay(self.pet_msg_info.pass_fb_max_level + 1) == 3 then
			ret_level = self.pet_msg_info.pass_fb_max_level + 1
		else
			ret_level = self.pet_msg_info.pass_fb_max_level
		end
	end
	return ret_level
end

--获取扫荡限制等级
function FuBenPanelWGData:GetPetSaoDangLevelLimit(fb_type)
	local info = self:GetSaoDangLevelLimit(fb_type)
	if info then
		return info.level_limit
	end
	return 0
end

--获取扫荡显示限制等级
function FuBenPanelWGData:GetPetSaoDangShowLevelLimit()
	local cfg = self.new_petfb_cfg_auto.other[1]
	return cfg.sweep_show_low
end

--获取宠物本关卡总数
function FuBenPanelWGData:GetPetMaxLayerCount()
	return self.pet_fb_max_layer
end

--获取宠物本通关信息
function FuBenPanelWGData:GetPetAllInfo()
	return self.pet_msg_info
end

function FuBenPanelWGData:SetPetStarDisplay(index, is_fake)
	if is_fake then
		return 0
	end
	if self.pet_msg_info.pass_petfb_star then
		return self.pet_msg_info.pass_petfb_star[index - 1]
	end
end


function FuBenPanelWGData:SetPetScenceInfo(protocol)
	self.pet_scence_info = {}
	self.pet_scence_info.level = protocol.level
	self.pet_scence_info.is_finish = protocol.is_finish
	self.pet_scence_info.is_pass = protocol.is_pass
	self.pet_scence_info.curr_wave_index = protocol.curr_wave_index
	self.pet_scence_info.max_wave_count = protocol.max_wave_count
	self.pet_scence_info.get_exp = protocol.get_exp
	self.pet_scence_info.get_coin = protocol.get_coin
	self.pet_scence_info.pass_time_s = protocol.pass_time_s
	self.pet_scence_info.cur_star_num = protocol.cur_star_num
	self.pet_scence_info.cur_monster_num = protocol.cur_monster_num
	self.pet_scence_info.kick_out_timestamp = protocol.kick_out_timestamp
	self.pet_scence_info.next_wave_refresh_timestamp = protocol.next_wave_refresh_timestamp
	self.pet_scence_info.prepare_end_timestamp = protocol.prepare_end_timestamp
	self.pet_scence_info.finish_timestamp = protocol.finish_timestamp
	self.pet_scence_info.next_star_timestamp = protocol.next_star_timestamp
	self.pet_scence_info.goddess_hp = protocol.goddess_hp
	self.pet_scence_info.status = protocol.status

	self:SetPetLevel(self.pet_scence_info.level)
end
function FuBenPanelWGData:GetPetScenceAllInfo()
	return self.pet_scence_info
end

function FuBenPanelWGData:GetPetScenceStatus()
	return self.pet_scence_info.status
end

function FuBenPanelWGData:GetPetTitleCfg()
	return self.new_petfb_cfg_auto.level
end

function FuBenPanelWGData:GetXianNvPos()
	local cfg = self:GetPetTitleCfg()
	if self.pet_scence_info.level and cfg then
		local lv = self.pet_scence_info.level
		if cfg[lv+1] then
			return cfg[lv+1].xiannv_x,cfg[lv+1].xiannv_y
		end
	end
	return nil,nil
end

function FuBenPanelWGData:GetPetPeriPos()
	local level_cfg = self.new_petfb_cfg_auto.level[self.pet_scence_info.level + 1]
	return level_cfg.xiannv_x, level_cfg.xiannv_y
end

function FuBenPanelWGData:GetPetGuaJiPos()
	local cfg = self.new_petfb_cfg_auto.other[1]
	return cfg.guaji_x, cfg.guaji_y
end

function FuBenPanelWGData:GetPetGuaJiDir()
	local x = nil
	local y = nil

	local cfg = self.new_petfb_cfg_auto.other[1]
	if cfg.guaji_range_faceto ~= nil and cfg.guaji_range_faceto ~= "" then
		local pos = Split(cfg.guaji_range_faceto, "|")
		if pos ~= nil and #pos == 2 then
			x = tonumber(pos[1])
			y = tonumber(pos[2])
		end
	end

	return x, y
end

function FuBenPanelWGData:GetPetXianNvPosRotationScale(layer)
	local cfg = self.new_pet_level_map[layer]
	if cfg then
		local pos = Split(cfg.huanhua_position, "|")
		local rotation = Split(cfg.huanhua_rotation, "|")
		local scale = cfg.huanhua_scale
		return pos, rotation, scale
	end
	return nil,nil,nil
end

function FuBenPanelWGData:GetFakePetXianNvPosRotationScale()
	local cfg = self.fake_petfb_cfg_auto.level[1]
	if cfg then
		local pos = Split(cfg.huanhua_position, "|")
		local rotation = Split(cfg.huanhua_rotation, "|")
		local scale = cfg.huanhua_scale
		return pos, rotation, scale
	end
	return nil,nil,nil
end

--获取天神副本所需等级
function FuBenPanelWGData:GetPetFbNeedLevel(layer)
	local cfg = self.new_pet_level_map[layer]
	if cfg then
		return cfg.role_level
	end
	--print_error("获取天神副本所需等级， 找不到配置 ：", layer)
	return ""
end

--检测宠物本副本加号特效
function FuBenPanelWGData:CheckPetEffect()
	if not self:CheckVipCondition() then
		return false
	end
	if self:GetPetCanEnterTimes() <= 0 then
		local param = VipPower.Instance:GetParam(VipPowerId.pet_fb_buy_times)
		local day_buy_times = self.pet_msg_info.buy_times
		if param > day_buy_times then
			return true
		end
	end
	return false
end

--获取挂机范围点
function FuBenPanelWGData:GetGuaJiPoint()
	if self.ret_point_list then
		return self.ret_point_list
	end
	local other_cfg = self:GetPetOtherCfg()
	local split_str = other_cfg.guaji_range
	local str_point_list = Split(split_str , "|")

	self.ret_point_list = {}
	for i, v in ipairs(str_point_list) do
		local point_str = Split(v, "#")
		table.insert(self.ret_point_list, u3d.vec2(point_str[1], point_str[2]))
	end
	return self.ret_point_list
end

---------------------宠物本end--------------------------


function FuBenPanelWGData:GetDemonsResId()
	local cfg = ConfigManager.Instance:GetAutoConfig("xinmo_fb_cfg_auto").other[1]
	local base_prof = RoleWGData.Instance:GetRoleProf()
	return cfg["role_shadow_res_" .. base_prof]
end

function FuBenPanelWGData:SetDemonsInfo(protocol)
	self.demons_info = protocol
end
function FuBenPanelWGData:GetDemonsInfo()
	return self.demons_info
end

--检测Vip是否满足购买次数条件。
function FuBenPanelWGData:CheckVipCondition()
	return VipWGData.Instance:IsVip()
end

function FuBenPanelWGData:GetXiuZhenRank()
	local id = RoleWGData.Instance:InCrossGetOriginUid()
    local rank_data = RankWGData.Instance:GetRankData(RankKind.Person, PersonRankType.TianXianGeRank)
	for k,v in pairs(rank_data) do
        if v.user_id == id then
			return v.rank_index
		end
	end
	return -1
end

function FuBenPanelWGData:SetFubenStarNum(num)
	self.star_num_record = num
end

function FuBenPanelWGData:GetFubenStarNum()
	return self.star_num_record or 0
end

-- 引导进入的次数
function FuBenPanelWGData:GetGuideFinishTimes()
	return self.guide_finish_times
end

function FuBenPanelWGData:AddGuideFinishTimes()
	self.guide_finish_times = self.guide_finish_times + 1
end

function FuBenPanelWGData:IsTongBiRemaindTimes()
	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
	local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
    local remain_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
    return remain_times > 0
end

function FuBenPanelWGData:IsFubenBaGuaRemaindTimes()
	--每天免费次数
    local day_free_times = self:GetBaGuaMiZhenDayFreeTimes()
    --今日进入次数
    local day_enter_times = self:GetBaGuaMiZhenEnterTimes()
    --今日购买次数
    local day_buy_times = self:GetBaGuaMiZhenBuyTimes()
    --可进入次数
    local remain_times = day_free_times + day_buy_times - day_enter_times
    return remain_times > 0
end

--记录当前进入层数
function FuBenPanelWGData:SetBaGuaCurEnterLayer(layer)
	self.cur_bagua_layer = layer
end

function FuBenPanelWGData:GetBaGuaCurEnterLayer()
	return self.cur_bagua_layer
end

--返回是否还能继续挑战，是否能挑战下一关
function FuBenPanelWGData:IsHighTeamEquipHasTimes()
	local remain_times, total_times = TeamEquipFbWGData.Instance:GetHTEFbTimes()
    local is_all_robot = NewTeamWGData.Instance:IsRoomMemberAllRobot()
    local data_list = TeamEquipFbWGData.Instance:GetEquipFbList()
    local scene_id = Scene.Instance:GetSceneId()
    local cur_layer = 0
    for k, v in pairs(data_list) do
        if scene_id == v.scene_id then
            cur_layer = k
            break
        end
    end
    local is_can_enter_next = false
    if cur_layer ~= 0 and data_list[cur_layer + 1] and data_list[cur_layer + 1].need_role_level <= RoleWGData.Instance:GetRoleLevel() then
        is_can_enter_next = true
    end
    return remain_times > 0 and is_all_robot, is_can_enter_next
end


function FuBenPanelWGData:SetXiuZhenRoadGuideFlag(flag)
	self.xiuzhen_road_guide = flag
end

function FuBenPanelWGData:GetXiuZhenRoadGuideFlag()
	return self.xiuzhen_road_guide
end

function FuBenPanelWGData:GetFBExpShowLv()
	if self.tianxiangefbcfg_auto and self.tianxiangefbcfg_auto.other[1] then
		return self.tianxiangefbcfg_auto.other[1].biaoqian_level or 1
	end
	return 1
end

function FuBenPanelWGData:GetEnterXZLLevel()
	return self.enter_xzl_lv
end

function FuBenPanelWGData:SetEnterXZLLevel(lv)
	self.enter_xzl_lv = lv
end

function FuBenPanelWGData:SetExpFubenTimes(times)
	self.enter_exp_times = times
end

function FuBenPanelWGData:GetExpFubenTimes()
	return self.enter_exp_times
end

function FuBenPanelWGData:GetXiuZhenLuOtherCfg()
	if self.tianxiangefbcfg_auto and self.tianxiangefbcfg_auto.other then
		return self.tianxiangefbcfg_auto.other[1] or {}
	end
	return {}
end

function FuBenPanelWGData:GetBaGuaMiZhenOtherCfg()
	if not self.bagua_mizhen_other_cfg then
		local cfg = ConfigManager.Instance:GetAutoConfig("baguamizhenfb_cfg_auto")
		self.bagua_mizhen_other_cfg = cfg.other[1]
	end
	return self.bagua_mizhen_other_cfg or {} 
end

function FuBenPanelWGData:GetSingleFuBen()
	return FuBenListSingleData
end

function FuBenPanelWGData:GetTeamFuBen()
	return FuBenListTeamData
end

function FuBenPanelWGData:GetFuBenCfg(name)
	local fun_name = RemindFunName[name] 
	-- 单人
	if fun_name == FunName.FubenpanelWelkin then
		return self:GetWelkinCfg()
	elseif fun_name == FunName.FubenpanelCopper then
		return self:GetCopperCfg()
	elseif fun_name == FunName.FubenpanelPet then
		return self:GetPetCfg()
	elseif fun_name == FunName.fubenpanel_bagua then
		return self:GetBaguaCfg()
	elseif fun_name == FunName.FubenpanelTianShen then
		return self:GetTianShenCfg()
	--组队
	elseif fun_name == FunName.FuBenPanelExp then
		return self:GetTeamExpCfg()
	elseif fun_name == FunName.fubenpanel_equip_high then
		return self:GetTeamEquipCfg()
	elseif fun_name == FunName.fubenpanel_bootybay then
		return self:GetTeamBootybayCfg()
	elseif fun_name == FunName.fubenpanel_control_beasts then
		return self:GetTeamControlBeastsCfg()
	elseif fun_name == FunName.fubenpanel_beauty then
		return self:GetTeamBeautyCfg()
	elseif fun_name == FunName.fubenpanel_wuhun then
		return self:GetTeaWuhunCfg()
	elseif fun_name == FunName.fubenpanel_rune_tower then
		return self:GetTeamRuneTowerCfg()
	end

	local list = {}
	list.no_data = true
	list.is_open  = false
	list.sort_num = 9999

	return list
end

-- 获取数据 百炼成神(天仙阁，凡人修真,登神长阶)
function FuBenPanelWGData:GetWelkinCfg()
	local cur_capability = GameVoManager.Instance:GetMainRoleVo().capability

	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.TianXianFuBen])
	list.is_open  = is_open
	list.fb_index = 1
	list.tab_index = TabIndex.fubenpanel_welkin

	local cur_level = self:GetPassLevel()
	local cfg = self:GetCurXiuXianCfg(cur_level + 1)
	
	if cfg == nil then
		cfg = self:GetCurXiuXianCfg(cur_level)
	end
	

	
	-- 两个界面显示不同 其他副本的应该也要改
	-- local color = COLOR3B.DEFAULT_NUM
	-- if not IsEmptyTable(cfg) then
	-- 	color = cur_capability < cfg.capability and COLOR3B.RED or color
	-- end
	-- string.format(Language.FanRenXiuZhen.MyCapability1, color, cur_capability)
	list.cur_level = cur_level
	list.cur_capability = cur_capability
	list.is_reach_capability = cur_capability >= cfg.capability

	list.tuijian_capability = IsEmptyTable(cfg) 
												and string.format(Language.FanRenXiuZhen.TuiJianCapability, "--") 
												or string.format(Language.FanRenXiuZhen.TuiJianCapability, cfg.capability)
	list.has_second = true
	list.is_team = false
	list.task_type = RATSDUOBEI_TASK.FANRENXIUXIAN
	list.act_num,list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_44)
	list.remind_name = RemindName.TianXianFuBen
	list.btn_call_back = function() 
		local max_level = #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").reward_show
		if cur_level >= max_level then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FbWushuang.MaxLevelTips)
			return
		end

	    local cfg = self:GetCurXiuXianCfg(cur_level + 1)
	    if IsEmptyTable(cfg) then return end
	    local role_lv = RoleWGData.Instance:GetRoleLevel()
	    if cfg.open_level > role_lv then
	        local str = RoleWGData.GetLevelString(cfg.open_level)
	        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FanRenXiuZhen.NotOpen, str))
	        return
	    end

		if GameVoManager.Instance:GetMainRoleVo().capability < cfg.capability then
			local data = {}
			data.level = cur_level
			data.capability = cfg.capability
			FuBenPanelWGCtrl.Instance:OpenChallengeTip(data)
		else
			local function callback()
				FuBenWGData.Instance:SetTXGEnter(true)
				FuBenWGCtrl.Instance:SendEnterWelkinFb(false)
			end
			OperateFrequency.Operate(callback, EnumOperateFrequency.EnterFB_FanRenXiuZhen, 3)
		end	
	end

	return list
end

-- 获取数据  熔火之心（铜币，荒古神冢，龙王宝藏）
function FuBenPanelWGData:GetCopperCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.LongWangBaoZang])
	list.is_open  = is_open
	list.fb_index = 5
	list.tab_index = TabIndex.fubenpanel_copper

	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
	local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
	local remain_times = 0
	list.all_time_num = 0
	if copper_info then
		remain_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
		list.all_time_num = other_cfg.fb_day_free_times + copper_info.day_buy_times
	end
	list.act_num,list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_8)
	list.has_time_num = remain_times
	list.has_second = false
	list.is_team = false
	list.task_type = RATSDUOBEI_TASK.JINCHANBAOKU
	list.remind_name = RemindName.LongWangBaoZang
	list.btn_call_back = function() 
		if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
			return
		end

		local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
		local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
		local remain_times = 0
		if copper_info then
			remain_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
		end
		 
		if remain_times > 0 then
			FuBenWGData.Instance:SetTXGEnter(true)
			FuBenWGCtrl.Instance:SendTongBiFb(0)
			ViewManager.Instance:CloseAll()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
			FuBenPanelWGCtrl.Instance:OpenCopperBuy()
		end
	end
	return list
end

-- 获取数据 暗翼之巢穴 (宠物本，蓬莱仙境，仙灵圣殿)
function FuBenPanelWGData:GetPetCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.XianLingShengDian])
	list.is_open  = is_open
	list.fb_index = 3
	list.tab_index = TabIndex.fubenpanel_pet

	local pet_info = self:GetPetAllInfo()
	local other_cfg = self:GetPetOtherCfg()
	local enter_cw_times = other_cfg.free_times + pet_info.buy_times - pet_info.day_times
	list.has_time_num = enter_cw_times
	list.all_time_num = other_cfg.free_times + pet_info.buy_times
	list.act_num,list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_9)
	list.has_second = true
	list.is_team = false
	list.task_type = RATSDUOBEI_TASK.SHOUHUXIANLING
	list.remind_name = RemindName.XianLingShengDian

	return list
end


-- 获取数据 神灵仙岛(八卦迷阵，神练宝塔 )
function FuBenPanelWGData:GetBaguaCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.BaGuaMiZhenFb])
	list.is_open  = is_open
	list.fb_index = 4
	list.tab_index = TabIndex.fubenpanel_bagua

	local day_free_times = self:GetBaGuaMiZhenDayFreeTimes()
	local day_enter_times = self:GetBaGuaMiZhenEnterTimes()
	local day_buy_times = self:GetBaGuaMiZhenBuyTimes()
	local remain_times = day_free_times + day_buy_times - day_enter_times
	local total_times = day_free_times + day_buy_times
	list.has_time_num = remain_times
	list.all_time_num = total_times
	list.act_num,list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_36)
	list.has_second = true
	list.is_team = false
	list.task_type = RATSDUOBEI_TASK.FUWENFUBEN
	list.remind_name = RemindName.BaGuaMiZhenFb

	return list
end

-- 获取数据 (组队经验本，外域裂缝)   2024/7/23 策划要求将组队经验本改为单人本
function FuBenPanelWGData:GetTeamExpCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.LianYuFuBen])
	list.is_open  = is_open
	list.fb_index = 2
	list.tab_index = TabIndex.fubenpanel_exp

	local other_cfg = WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
	local yet_enter_time = WuJinJiTanWGData.GetFBEnterTimes()
	local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
	local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
	local enter_dj_times = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
	list.has_time_num = enter_dj_times
    list.all_time_num = other_cfg.everyday_times + yet_buy_time + user_ticket_time
    list.has_second = false
    list.is_team = false
    list.sort_num = is_open and 0 or 999
    list.task_type = RATSDUOBEI_TASK.FUMOZHANCHUAN
	list.act_num, list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_14)
    list.remind_name = RemindName.LianYuFuBen
    list.btn_call_back = function() 
		if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
			return
		end

		local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
		local team_type = is_linghunguangchang_fb and GoalTeamType.Exp_FuMoZhanChuan or GoalTeamType.Exp_DuJieXianZhou
		local fb_mode =  0
		local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
		local remain_count = total_count - cur_enter_count

		 if remain_count == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
			ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, nil, "change_index", { tab_index = TabIndex.fubenpanel_exp })
			return
		end

		FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.LINGHUNGUANGCHANG)

    	-- if NewTeamWGData.Instance:GetIsInRoomScene() then
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.InFbStateTip)
		-- 	return
		-- end

		-- local is_match = NewTeamWGData.Instance:GetIsMatching()
		-- local operate = is_match and 1 or 0
		-- local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
		-- local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
		-- if is_match and (now_team_type == GoalTeamType.Exp_DuJieXianZhou or now_team_type == GoalTeamType.Exp_FuMoZhanChuan) then
		-- 	if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
		-- 		if not self.alert then
		-- 			self.alert = Alert.New()
		-- 		end

		-- 		self.alert:SetLableString(Language.FuBenPanel.AlertCancelMatch)
		-- 		self.alert:SetOkFunc(function()
		-- 			if not is_linghunguangchang_fb then
		-- 				NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, GoalTeamType.Exp_DuJieXianZhou, 0)
		-- 			else
		-- 				NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, GoalTeamType.Exp_FuMoZhanChuan, 0)
		-- 			end
		-- 		end)

		-- 		self.alert:Open()
		-- 	else
		-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.OnlyLeaderCanDo)
		-- 	end
		-- else
		-- 	if not is_linghunguangchang_fb then
		-- 		NewTeamWGData.Instance:SetTeamTypeAndMode(GoalTeamType.Exp_DuJieXianZhou, 0)
		-- 	else
		-- 		NewTeamWGData.Instance:SetTeamTypeAndMode(GoalTeamType.Exp_FuMoZhanChuan, 0)
		-- 	end
			
		-- 	ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
		-- end
	end

	return list
end

-- 获取数据 (蛮荒妖谷)
function FuBenPanelWGData:GetTeamEquipCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.YuanGuXianDian])
	list.is_open  = is_open
	list.fb_index = 6
	list.tab_index = TabIndex.fubenpanel_equip_high


	local remain_times, total_times = TeamEquipFbWGData.Instance:GetHTEFbTimes()
	list.has_time_num = remain_times
    list.all_time_num = total_times
	list.has_second = true
	list.is_team = true
	list.sort_num = is_open and list.fb_index or 999
	list.task_type = RATSDUOBEI_TASK.HAIDIFEIXU
	list.act_num,list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_13)
	list.remind_name = RemindName.YuanGuXianDian

	return list
end

-- 获取数据 (仙君遗迹)
function FuBenPanelWGData:GetTeamBootybayCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.Bootybay])
	list.is_open  = is_open
	list.fb_index = 12
	list.tab_index = TabIndex.fubenpanel_bootybay


	local wabao_times = BootyBayWGData.Instance:GettDailyWaBaoRemainCount()
	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
	local is_accepted = BootyBayWGData.Instance:GetIsAcceptWaBaoTask()
	local times = is_accepted and other_cfg.task_wabao_times - wabao_times or other_cfg.task_wabao_times
	local enter_teamfb_times = BootyBayWGData.Instance:GetEnterTeamFBTimes() or 0
	local remain_fb_times = other_cfg.team_fb_enter_times_limit - enter_teamfb_times
	list.has_time_num = times
    list.all_time_num = other_cfg.task_wabao_times
    list.has_week_num = remain_fb_times
    list.all_week_num = other_cfg.team_fb_enter_times_limit
	list.has_second = false
	list.is_team = true
	list.sort_num = is_open and list.fb_index or 999
	list.task_type = RATSDUOBEI_TASK.WABAO
	list.remind_name = RemindName.Bootybay
	list.act_num,list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_31)
	
	list.btn_call_back = function()
		if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
			return
		end

		local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
		BootyBayWGData.Instance:SetWaBaoType(WABAO_TYPE.WABAO_TYPE_TASK)
		local scene_id = Scene.Instance:GetSceneId()
		local bootybay_other_cfg = BootyBayWGData.Instance:GetOtherConfig()
		local role_level = RoleWGData.Instance.role_vo.level
		local scene_type = Scene.Instance:GetSceneType()
		if bootybay_other_cfg.open_level > role_level then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.CaveBossTips)
			return
		end

		if scene_id ~= bootybay_scene_id then
			local scene_logic = Scene.Instance:GetSceneLogic()
	   		local x, y = scene_logic:GetTargetScenePos(bootybay_scene_id)
	   		TaskWGCtrl.Instance:SendFlyByShoe(bootybay_scene_id, x or 0, y or 0, -1, false)
		else
			BootyBayWGCtrl.Instance:SendTaskWaBao()
		end

		ViewManager.Instance:CloseAll()
	end

	return list
end

-- 获取数据 (幻兽副本)
function FuBenPanelWGData:GetTeamControlBeastsCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.FbControlBeasts])
	list.is_open  = is_open
	list.fb_index = 9
	list.tab_index = TabIndex.fubenpanel_control_beasts

	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbControlBeastsType)
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbControlBeastsType)

    local max_count = team_type_cfg.max_times or 0
    local cur_count = max_count - enter_times
    list.has_time_num = cur_count
    list.all_time_num = max_count
    list.has_second = false
    list.is_team = true
    list.sort_num = is_open and list.fb_index or 999
    list.task_type = -1
    list.act_num, list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_73)
    list.remind_name = RemindName.FbControlBeasts

	return list
end

--女神副本
function FuBenPanelWGData:GetTeamBeautyCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.FbBeauty])
	list.is_open  = is_open
	list.fb_index = 7
	list.tab_index = TabIndex.fubenpanel_beauty

	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbBeautyType)
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbBeautyType)

    local max_count = team_type_cfg.max_times or 0
    local cur_count = max_count - enter_times
    list.has_time_num = cur_count
    list.all_time_num = max_count
    list.has_second = false
    list.is_team = true
    list.sort_num = is_open and list.fb_index or 999
    list.task_type = -1
    list.act_num, list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_74)
    list.remind_name = RemindName.FbBeauty

	return list
end

--武魂副本
function FuBenPanelWGData:GetTeaWuhunCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.FbWuHun])
	list.is_open  = is_open
	list.fb_index = 10
	list.tab_index = TabIndex.fubenpanel_wuhun

	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbWuHunType)
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbWuHunType)

    local max_count = team_type_cfg.max_times or 0
    local cur_count = max_count - enter_times
    list.has_time_num = cur_count
    list.all_time_num = max_count
    list.has_second = false
    list.is_team = true
    list.sort_num = is_open and list.fb_index or 999
    list.task_type = -1
    list.act_num, list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_75)
    list.remind_name = RemindName.FbWuHun
	return list
end

--符文塔
function FuBenPanelWGData:GetTeamRuneTowerCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[RemindName.FbRuneTower])
	list.is_open  = is_open
	list.fb_index = 11
	list.tab_index = TabIndex.fubenpanel_rune_tower

	local team_type_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbRuneTowerType)
	local enter_times = FuBenTeamCommonTowerWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbRuneTowerType)
    local max_count = team_type_cfg.max_times or 0
    local cur_count = max_count - enter_times
    list.has_time_num = cur_count
    list.all_time_num = max_count
    list.has_second = false
    list.is_team = true
    list.sort_num = is_open and list.fb_index or 999
    list.task_type = -1
    list.act_num, list.act_max_num = BiZuoWGData.Instance:GetActNumForFuBenByFunName(BiZuoType.Type_76)
	list.need_day_limit_type = GoalTeamType.FbRuneTowerType
    list.remind_name = RemindName.FbRuneTower
	return list
end

-- 获取数据 (封神台)
function FuBenPanelWGData:GetTianShenCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[v.TianShenFb])
	list.is_open  = is_open
	list.fb_index = 8
	list.tab_index = TabIndex.fubenpanel_tianshen

	local day_enter_time = self:GetTianShenEnterTimes()
	local can_enter_time = self:GetTianShenCanEnterTimes()
	list.has_time_num = can_enter_time
    list.all_time_num = can_enter_time + day_enter_time
    list.has_second = false
    list.is_team = false
    list.sort_num = is_open and list.fb_index or 999
    list.task_type = RATSDUOBEI_TASK.FENGSHENDIAN
    list.act_num = ""
    list.remind_name = RemindName.TianShenFb
	list.btn_call_back = function()
		ViewManager.Instance:Open(GuideModuleName.FuBenPanelView)
	end

	return list
end

-- 获取数据  (抓鬼)  暂时无用 保留一下
function FuBenPanelWGData:GetZhuaGuiCfg()
	local list = {}
	local is_open = FunOpen.Instance:GetFunIsOpened(RemindFunName[v.ZhuoGuiFuBen])
	list.is_open  = is_open
	list.fb_index = 0
	list.tab_index = TabIndex.fubenpanel_zhuogui

	local other_cfg = self:GetZhuoGuiOtherCfg()
    local max_count = other_cfg.max_refresh_event_times
    local cur_count = max_count - self:GetZhuoGuiEventTimes()
    list.has_time_num = cur_count
    list.all_time_num = max_count
    list.has_second = false
    list.is_team = false
    list.sort_num = is_open and list.fb_index or 999
    list.task_type = -1
    list.act_num = ""
    list.remind_name = RemindName.ZhuoGuiFuBen
    list.btn_call_back = function()
    	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_GHOST_GLOBAL_FB)
    end

	return list
end

function FuBenPanelWGData:GetLimitFuBenLimitStr(fun_name)
	local fun_cfg = FunOpen.Instance:GetFunByName(fun_name)
	if fun_cfg == nil then
		return Language.Common.FunOpenTip
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if fun_cfg.trigger_type == GuideTriggerType.LevelUp then
		local limit_lv = tonumber(fun_cfg.trigger_param) or 0
		if limit_lv > RoleWGData.GetRoleMaxLevel() then
			return string.format(Language.Common.FunOpenTip)
		else
			if limit_lv > role_level then
				return string.format(Language.FuBenPanel.TabbarLimitStr1, RoleWGData.GetLevelString(limit_lv))
			else
				if fun_cfg.cross_gs_count and fun_cfg.cross_gs_count > 1 then
					return string.format(Language.Common.FunOpenCrossCount, fun_cfg.cross_gs_count)
				else
					return string.format(Language.FuBenPanel.TabbarLimitStr1, RoleWGData.GetLevelString(limit_lv))
				end
			end
		end
	elseif fun_cfg.trigger_type == GuideTriggerType.CommitTask or fun_cfg.trigger_type == GuideTriggerType.AcceptTask then
		if TaskWGData.Instance:GetTaskIsCompleted(fun_cfg.trigger_param) then
			if fun_cfg.cross_gs_count and fun_cfg.cross_gs_count > 1 then
				return string.format(Language.Common.FunOpenCrossCount, fun_cfg.cross_gs_count)
			else
				return string.format(Language.FuBenPanel.TabbarLimitStr2, RoleWGData.GetLevelString(tonumber(fun_cfg.task_level) or 0))
			end
		else
			return string.format(Language.FuBenPanel.TabbarLimitStr2, RoleWGData.GetLevelString(tonumber(fun_cfg.task_level) or 0))
		end
	elseif fun_cfg.trigger_type == GuideTriggerType.OpenSeverDay then
		local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if nil ~= fun_cfg.trigger_param and fun_cfg.trigger_param ~= "" and cur_open_day < fun_cfg.trigger_param then
			return string.format(Language.Common.FunOpenDayLimit, fun_cfg.trigger_param)
		else
			if nil ~= fun_cfg.trigger_param2 and fun_cfg.trigger_param2 ~= "" and role_level < fun_cfg.trigger_param2 then
				local is_dianfeng, level = RoleWGData.Instance:GetDianFengLevel(fun_cfg.trigger_param2)
				local level_str = is_dianfeng and string.format(Language.Common.LevelFeiXian, level) or level
				return string.format(Language.Common.FunOpenDayLimitAndLevel, fun_cfg.trigger_param, level_str)
			end
		end
	end

	return Language.Common.FunOpenTip
end
