local math = math
local living_cache = {}
local die_cache = {}

u3dpool = u3dpool or {}

local vector_meta = {}
vector_meta.__index = vector_meta

function vector_meta:set(x, y, z)
    self.x = x or 0
    self.y = y or 0
    self.z = z or 0
end

function vector_meta:reset()
    self.x, self.y, self.z = 0, 0, 0
end

local function vector_length(v, is_sqrt)
    local length_squared = v.x * v.x + v.y * v.y + (v.z or 0) * (v.z or 0)
    if is_sqrt ~= false then
        return math.sqrt(length_squared)
    end

    return length_squared
end

local function vector_normalize(v)
    local length = vector_length(v)
    if length == 0 then
        return v
    end

    return {x = v.x / length, y = v.y / length, z = (v.z or 0) / length}
end

function u3dpool.__alloc(x, y, z)
    local t = next(die_cache)
    if t then
        die_cache[t] = nil
        t:set(x, y, z)
    else
        t = setmetatable({ x = x, y = y, z = z }, vector_meta)
    end
    living_cache[t] = t
    return t
end

function u3dpool.Update()
    for k in pairs(living_cache) do
        die_cache[k] = k
        living_cache[k] = nil
    end
end

function u3dpool.reset(data)
	data.x = 0
	data.y = 0
	data.z = 0
end

function u3dpool.set(data, _x, _y, _z)
	data.x = _x
	data.y = _y
	data.z = _z
end

local function ensure_vector(dest, x, y, z)
    if dest then
        dest.x = x or dest.x
        dest.y = y or dest.y
        dest.z = z or dest.z
    else
        dest = u3dpool.__alloc(x, y, z)
    end
    return dest
end

function u3dpool.vec2(x, y)
    return u3dpool.__alloc(x, y)
end

-- 加
function u3dpool.v2Add(v2a, v2b, dest)
    return ensure_vector(dest, v2a.x + v2b.x, v2a.y + v2b.y)
end

-- 减
function u3dpool.v2Sub(v2a, v2b, dest)
    return ensure_vector(dest, v2a.x - v2b.x, v2a.y - v2b.y)
end

-- 乘一个数
function u3dpool.v2Mul(v2a, factor, dest)
    return ensure_vector(dest, v2a.x * factor, v2a.y * factor)
end

-- 中点
function u3dpool.v2Mid(v2a, v2b, dest)
    return ensure_vector(dest, (v2a.x + v2b.x) / 2, (v2a.y + v2b.y) / 2)
end

-- 角度转向量
function u3dpool.v2ForAngle(a, dest)
    local cos_a = math.cos(a)
    local sin_a = math.sin(a)
    return ensure_vector(dest, cos_a, sin_a)
end

-- 向量转角度
function u3dpool.v2Angle(v2)
    return math.atan2(v2.y, v2.x)
end

-- 长度
function u3dpool.v2Length(v2, is_sqrt)
	return vector_length(v2, is_sqrt)
end

-- 单位化
function u3dpool.v2Normalize(v2, dest)
    local normalized = vector_normalize(v2)
    return ensure_vector(dest, normalized.x, normalized.y)
end

-- 二维向量旋转a度（-1向左旋转，1向右旋转）
function u3dpool.v2Rotate(v2, a, dir, dest)
	dir = dir or 1
    local rad_a = math.rad(a * dir)
    local cos_a = math.cos(rad_a)
    local sin_a = math.sin(rad_a)
    return ensure_vector(dest, v2.x * cos_a - v2.y * sin_a, v2.x * sin_a + v2.y * cos_a)
end


function u3dpool.vec3(x, y, z)
    return u3dpool.__alloc(x, y, z)
end

function u3dpool.v3Add(v3a, v3b, dest)
    return ensure_vector(dest, v3a.x + v3b.x, v3a.y + v3b.y, v3a.z + v3b.z)
end

function u3dpool.v3Sub(v3a, v3b, dest)
    return ensure_vector(dest, v3a.x - v3b.x, v3a.y - v3b.y, v3a.z - v3b.z)
end

function u3dpool.v3Mul(v3, factor, dest)
    return ensure_vector(dest, v3.x * factor, v3.y * factor, v3.z * factor)
end

function u3dpool.v3Dot(v3a, v3b)
    return v3a.x * v3b.x + v3a.y * v3b.y + v3a.z * v3b.z
end

function u3dpool.Cross(lhs, rhs, dest)
    local x = lhs.y * rhs.z - lhs.z * rhs.y
    local y = lhs.z * rhs.x - lhs.x * rhs.z
    local z = lhs.x * rhs.y - lhs.y * rhs.x
    return ensure_vector(dest, x, y, z)
end

function u3dpool.v3Length(v3, is_sqrt)
	return vector_length(v3, is_sqrt)
end

function u3dpool.v3Normalize(v3, dest)
    local normalized = vector_normalize(v3)
    return ensure_vector(dest, normalized.x, normalized.y, normalized.z)
end



local _up = Vector3.up
local _next = { 2, 3, 1 }

function u3dpool.LookRotation(forward, up)
	local mag = vector_length(forward)
	if mag < 1e-6 then
		error("error input forward to Quaternion.LookRotation"..tostring(forward))
		return nil
	end

	forward = vector_normalize(forward)
	up = up or _up
    local right = u3dpool.Cross(up, forward)
    right = vector_normalize(right)
    up = u3dpool.Cross(forward, right)
    right = u3dpool.Cross(up, forward)

	local rot = {
        {right.x, up.x, forward.x},
        {right.y, up.y, forward.y},
        {right.z, up.z, forward.z}
    }

    local trace = right.x + up.y + forward.z
    local q = {0, 0, 0}

	if trace > 0 then
		local s = 0.5 / math.sqrt(trace + 1)
        local w = s * (trace + 1)
        q[1] = (up.z - forward.y) * s
        q[2] = (forward.x - right.z) * s
        q[3] = (right.y - up.x) * s

        local ret = Quaternion.New(q[1], q[2], q[3], w)
        ret:SetNormalize()
        return ret
	else
		local i = 1
        if up.y > right.x then
            i = 2
        end
		
        if forward.z > rot[i][i] then
            i = 3
        end

        local j = _next[i]
        local k = _next[j]

        local t = rot[i][i] - rot[j][j] - rot[k][k] + 1
        local s = 0.5 / math.sqrt(t)
        q[i] = s * t
        local w = (rot[k][j] - rot[j][k]) * s
        q[j] = (rot[j][i] + rot[i][j]) * s
        q[k] = (rot[k][i] + rot[i][k]) * s

        local ret = Quaternion.New(q[1], q[2], q[3], w)
        ret:SetNormalize()
        return ret
	end
end

-- for i=1,10000 do
-- 	local t = {x = 0, y = 0, z = 0}
-- 	die_cache[t] = t
-- 	-- u3dpool.__alloc(0, 0, 0)
-- end