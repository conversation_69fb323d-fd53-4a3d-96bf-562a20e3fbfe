RewardShowView = RewardShowView or BaseClass(SafeBaseView)

function RewardShowView:__init(color_type)
	self.color_type = color_type

	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
end

function RewardShowView:__delete()

end

function RewardShowView:ReleaseCallBack()
	self.list_is_from_zero = false
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

	if self.normal_probability_list_view then
		self.normal_probability_list_view:DeleteMe()
		self.normal_probability_list_view = nil
	end

	if self.title_list_view then
		self.title_list_view:DeleteMe()
		self.title_list_view = nil
	end

	if self.title_probability_list_view then
		self.title_probability_list_view:DeleteMe()
		self.title_probability_list_view = nil
	end

	if self.list_view_center then
		self.list_view_center:DeleteMe()
		self.list_view_center = nil
	end

	if self.title_probability_list_view_2 then
		self.title_probability_list_view_2:DeleteMe()
		self.title_probability_list_view_2 = nil
	end
end

function RewardShowView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_reward_show_ui_prefab", "common_reward_show_view_" .. self.color_type)
	self:AddViewResource(0, "uis/view/common_reward_show_ui_prefab", "common_reward_show_content")
end

function RewardShowView:LoadCallBack()
	if not self.list_view then
		self.list_view = AsyncBaseGrid.New()
		self.list_view:CreateCells({
			col = 6,
			change_cells_num = 1,
			list_view = self.node_list["listview_1"],
			itemRender = ItemCell
		})
		self.list_view:SetStartZeroIndex(self.list_is_from_zero)
	end

	if not self.normal_probability_list_view then
		local bundle_name, asset_name = ResPath.GetRewardShowTipPrefab("probability_item_cell_" .. self.color_type)
		self.normal_probability_list_view = AsyncBaseGrid.New()
		self.normal_probability_list_view:CreateCells({
			col = 6,
			change_cells_num = 1,
			list_view = self.node_list["listview_2"],
			itemRender = RewardShowProbabilityItemRender,
			assetBundle = bundle_name,
			assetName = asset_name
		})
		self.normal_probability_list_view:SetStartZeroIndex(false)
	end

	if not self.title_list_view then
		self.title_list_view = RewardShowGrid.New()
		self.title_list_view:CreateCells({
			columns = 6,
			list_view = self.node_list["listview_3"],
			itemRender = ItemCell,
			content_type = self.content_type,
			color_type = self.color_type
		})
	end

	if not self.title_probability_list_view then
		local bundle_name, asset_name = ResPath.GetRewardShowTipPrefab("probability_item_cell_" .. self.color_type)
		self.title_probability_list_view = RewardShowGrid.New()
		self.title_probability_list_view:CreateCells({
			columns = 6,
			list_view = self.node_list["listview_4"],
			itemRender = RewardShowProbabilityItemRender,
			assetBundle = bundle_name,
			assetName = asset_name,
			content_type = self.content_type,
			color_type = self.color_type
		})
	end

	if not self.list_view_center then
		self.list_view_center = AsyncBaseGrid.New()
		self.list_view_center:CreateCells({
			col = 6,
			change_cells_num = 1,
			list_view = self.node_list["listview_5"],
			itemRender = ItemCell
		})
		self.list_view_center:SetStartZeroIndex(self.list_is_from_zero)
	end

	if not self.title_probability_list_view_2 then
		local bundle_name, asset_name = ResPath.GetRewardShowTipPrefab("probability_item_cell_" .. self.color_type)
		self.title_probability_list_view_2 = RewardShowGrid.New()
		self.title_probability_list_view_2:CreateCells({
			columns = 5,
			list_view = self.node_list["listview_6"],
			itemRender = RewardShowProbabilityItemRender,
			assetBundle = bundle_name,
			assetName = asset_name,
			content_type = self.content_type,
			color_type = self.color_type
		})
	end
end

function RewardShowView:ShowIndexCallBack()
	self:Flush(0)
end

function RewardShowView:OnFlush()
	for k, v in pairs(RewardShowViewType) do
		self.node_list["content_" .. v]:SetActive(self.content_type == v)
	end

	if self.content_type == RewardShowViewType.Normal then
		self.list_view:SetDataList(self.reward_item_list)
	elseif self.content_type == RewardShowViewType.Normal_Probability then
		self.normal_probability_list_view:SetDataList(self.normal_probability_reward_item_data)
	elseif self.content_type == RewardShowViewType.Title then
		self.title_list_view:SetDataList(self.title_reward_item_data)
	elseif self.content_type == RewardShowViewType.Title_Probability then
		self.title_probability_list_view:SetDataList(self.title_probability_reward_item_data)
	elseif self.content_type == RewardShowViewType.Normal_Center then
		self.list_view_center:SetDataList(self.reward_item_list)
	elseif self.content_type == RewardShowViewType.Title_Probability_2 then
		self.title_probability_list_view_2:SetDataList(self.title_probability_reward_item_data)
	end
	self.node_list["txt_tips"].text.text = self.other_tips or ""
end

--[[
local reward_data = {
	view_type,									--类型，默认RewardShowViewType.Normal.
	view_color,									--颜色，默认RewardShowViewColor.Orange.
	reward_item_list,							--Normal类型：奖励列表.
	normal_probability_reward_item_data =		--Normal_Probability类型：Normal类型 + 奖励列表与概率的数据.
	{
		{
			item,						--道具.
			probability_text			--概率.
		},
	},
	title_reward_item_data =					--Title类型：包含标题与奖励列表的数据.
	{
		{
			title_text,							--标题.
			reward_item_list =					--奖励列表.
			{
				item,
			}
		},
	},
	title_probability_reward_item_data =		--Title_Probability类型：Title类型 + 奖励列表与概率的数据.
	{
		{
			title_text,							--标题.
			reward_item_list =					--包含概率的奖励列表数据.
			{
				{
					item,						--道具.
					probability_text			--概率.
				},
			}
		},
	}
	other_tips,									--提示字符串
}
--]]
function RewardShowView:SetData(reward_data)
	self.content_type = reward_data.view_type or RewardShowViewType.Normal
	self.reward_item_list = reward_data.reward_item_list or {}
	self.list_is_from_zero = self.reward_item_list[0] ~= nil
	self.normal_probability_reward_item_data = reward_data.normal_probability_reward_item_data or {}
	self.title_reward_item_data = reward_data.title_reward_item_data or {}
	self.title_probability_reward_item_data = reward_data.title_probability_reward_item_data or {}
	self.other_tips = reward_data.other_tips
	self:Open()
end
