--ArtifactBattleView = ArtifactBattleView or BaseClass(SafeBaseView)

local BATTLE_POS_COUNT = 3 --出战位

--[[
function ArtifactBattleView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self.is_safe_area_adapter = true

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/artifact_ui_prefab", "layout_artifact_battle")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function ArtifactBattleView:__delete()

end
]]

function ArtifactView:BattleLoadIndexCallBack()
	-- self.node_list.title_view_name.text.text = Language.Artifact.BattleView
	local bundle, asset = ResPath.GetRawImagesPNG("a3_sx_czbg")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
	if not self.battle_item_list then
		self.battle_item_list = {}
		for i = 1, BATTLE_POS_COUNT do
			self.battle_item_list[i] = ArtifactBattleItem.New(self.node_list["ss_battle_item_" .. i])
			self.battle_item_list[i]:SetIndex(i)
		end
	end
end

function ArtifactView:BattleReleaseCallBack()
	if self.battle_item_list then
		for k, v in pairs(self.battle_item_list) do
			v:DeleteMe()
		end
		self.battle_item_list = nil
	end
end

function ArtifactView:BattleCloseCallBack()

end

function ArtifactView:BattleShowIndexCallBack()
	if self:IsAutoUpLevel() then
		self:StopLevelOperator()
	end
end

function ArtifactView:BattleOnFlush(param_t)
	local apply_list = ArtifactWGData.Instance:GetArtifactBattleInfo()
	if self.battle_item_list then
		for i = 1, BATTLE_POS_COUNT do
			self.battle_item_list[i]:SetData(apply_list[i])
		end
	end

	-- 出战计数
	local battle_count_list = ArtifactWGData.Instance:GetBattleTypeCountList()
	for i = 0, 3 do
		local count_str = (battle_count_list[i] or 0) .. "/1"
		self.node_list["txt_battle_count_" .. i].text.text = count_str
	end
end

--------------------------------
-- 出战位ItemRender
--------------------------------
ArtifactBattleItem = ArtifactBattleItem or BaseClass(BaseRender)

function ArtifactBattleItem:LoadCallBack()
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list["model_root"])
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	XUI.AddClickEventListener(self.node_list["btn_select"], BindTool.Bind(self.OnClickSelectBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_remove"], BindTool.Bind(self.OnClickRemoveBtn, self))
end

function ArtifactBattleItem:ReleaseCallBack()
	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	self.model_id = nil
end

function ArtifactBattleItem:OnFlush()
	if not self.data then
		return
	end
	local data = self.data
	local battle_cfg = ArtifactWGData.Instance:GetArtifactApplyCfgBySeq(data.pos_seq)
	
	local has_battle = data.seq >= 0
	local is_show_info = data.is_unlock == 1 and has_battle
	self.node_list["had_data"]:SetActive(is_show_info)
	self.node_list["img_outline"]:SetActive(not is_show_info)
	self.node_list["img_btn_bg"]:SetActive(not is_show_info)
	self.node_list["img_locked"]:SetActive(data.is_unlock ~= 1)
	self.node_list["img_add"]:SetActive(data.is_unlock == 1 and not has_battle)
	self.node_list["txt_locked_des"].text.text = string.format(Language.Artifact.BattleOpen, battle_cfg.zhuanzhi_level)
	
	if is_show_info then
		-- 信息
		local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(data.seq)
		local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(data.seq)
		local artifact_name = ArtifactWGData.Instance:GetArtifactAwakeName(data.seq, cur_artifact_data.awake_level)
		self.node_list["txt_artifact_name"].text.text = artifact_name
		
		local star_res_list = GetTwenTyStarImgResByStar(cur_artifact_data.star_level)
		for i = 1, GameEnum.ITEM_MAX_STAR do
			self.node_list["star_" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
		end

		local bundle, asset = ResPath.GetArtifactImg("a3_sx_mzt" .. artifact_cfg.battle_type)
		self.node_list["icon_type"].image:LoadSprite(bundle, asset)

		-- 技能
		local skill_cfg = ArtifactWGData.Instance:GetArtifactSkillCfg(data.seq, cur_artifact_data.star_level)
		if skill_cfg then
			local skill_bundle, skill_asset = ResPath.GetSkillIconById(skill_cfg.skill_id)
			self.node_list["skill_icon"].image:LoadSprite(skill_bundle, skill_asset)
			self.node_list["skill_name"].text.text = skill_cfg.skill_name
			self.node_list["txt_skill_des"].text.text = skill_cfg.skill_desc
		end

		-- spine
		if not self.model_id or self.model_id ~= artifact_cfg.model_id then
			local bundle, asset = ResPath.GetShuangXiuCZUI(artifact_cfg.model_id)
			local display_data = {}
			display_data.bundle_name = bundle
			display_data.asset_name = asset
			display_data.render_type = OARenderType.Prefab
			self.model_display:SetData(display_data)
		end
		self.model_id = artifact_cfg.model_id
	end

	local remind = ArtifactWGData.Instance:GetCanBattleRed(self.data.pos_seq)
	self.node_list["img_red"]:SetActive(remind)
end

function ArtifactBattleItem:OnClickSelectBtn()
	if self.data.is_unlock == 1 then
		ArtifactWGData.Instance:GetSetSelectedPosSeq(self.data.pos_seq)
		ArtifactWGCtrl.Instance:OpenBattleSelectView()
	else
		local battle_cfg = ArtifactWGData.Instance:GetArtifactApplyCfgBySeq(self.data.pos_seq)
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Artifact.BattleOpen, battle_cfg.zhuanzhi_level))
	end
end

function ArtifactBattleItem:OnClickRemoveBtn()
	ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.UNAPPLY, self.data.pos_seq)
end