SupremeFieldsActivityView = SupremeFieldsActivityView or BaseClass(SafeBaseView)

REWARD_MAX = 5
function SupremeFieldsActivityView:__init()
	self.view_name = "SupremeFieldsActivityView"
	self.view_layer = UiLayer.Pop

	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/supreme_fields_ui_prefab", "layout_supreme_fields_activity")
end

function SupremeFieldsActivityView:ReleaseCallBack()
	if self.reward_part then
		for key, value in pairs(self.reward_part) do
			value:DeleteMe()
			value = nil
		end
		self.reward_part = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.fz_display then
		self.fz_display:DeleteMe()
		self.fz_display = nil
	end

	if CountDownManager.Instance:HasCountDown("zhi_zun_time") then
		CountDownManager.Instance:RemoveCountDown("zhi_zun_time")
	end
end

function SupremeFieldsActivityView:ShowIndexCallBack()
	self:FlushTimeCount()
end

function SupremeFieldsActivityView:CloseCallBack()

end

function SupremeFieldsActivityView:OpenCallBack()
	SupremeFieldsWGCtrl.Instance:ReqActivityFootLightInfo(OA_FOOT_LIGHT_OPERATE_TYPE.INFO)
end

function SupremeFieldsActivityView:LoadCallBack()
	if not self.reward_part then
		self.reward_part = {}
		for idx = 1, REWARD_MAX do
			self.reward_part[idx] = SupremeFieldsActivityRewardItem.New(self.node_list.reward_part:FindObj("item_cell_" .. idx))
		end
	end

	if not self.model_display then
		self.model_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.model_display:SetRenderTexUI3DModel(display_data)
		-- self.model_display:SetUI3DModel(self.node_list["display_model"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.model_display)
	end

	if not self.fz_display then
		self.fz_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["fz_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.XL,
			can_drag = false,
		}
		
		self.fz_display:SetRenderTexUI3DModel(display_data)
		-- self.fz_display:SetUI3DModel(self.node_list["fz_model"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.fz_display)
	end

	XUI.AddClickEventListener(self.node_list["free_gift"], BindTool.Bind(self.OnBtnfreeBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_open"], BindTool.Bind(self.OnBtnOpenView, self))
	XUI.AddClickEventListener(self.node_list["btn_sell"], BindTool.Bind(self.OnBtnSellBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_skill_show"], BindTool.Bind(self.OnBtnShowSkill, self))
end

function SupremeFieldsActivityView:OnFlush(param_t)
	local grade, cfg = SupremeFieldsWGData.Instance:GetCurShopCfg()
	if not IsEmptyTable(cfg) then
		local capability = SupremeFieldsWGData.Instance:FootLightGetNewCap(grade)
		local price = RoleWGData.GetPayMoneyStr(cfg.price, cfg.rmb_type, cfg.rmb_seq)
		local buy_tmb_count, is_buy_free = SupremeFieldsWGData.Instance:GetShopIsBuyFlag()

		for key, value in pairs(self.reward_part) do
			--奖励数据的index从0开始.
			if cfg.reward_item[key - 1] then
				value:SetData(cfg.reward_item[key - 1])
			end
		end

		self.node_list.subtitle.text.text = string.format(Language.SupremeFields.SubTitle, cfg.subtitle)

		self.node_list.btn_text.text.text = price
		self.node_list.orifinal_price.text.text = string.format(Language.PierreDirectPurchase.OldPriceDesc, price)
		self.node_list.cap_value.text.text = capability
		self:FlushModel(cfg)

		self.node_list.btn_red:SetActive(not is_buy_free)
		self.node_list["free_gift"]:SetActive(not is_buy_free)
		local quota_text = cfg.buy_count_limit
		self.node_list.quota_text.text.text = string.format(Language.OpertionAcitvity.XianShiMiaoSha.BuyLimitDesc,
			buy_tmb_count, quota_text)
		XUI.SetGraphicGrey(self.node_list.btn_sell, quota_text == buy_tmb_count)
		local bundle, asset = ResPath.GetSupremeFieldsActTxt(grade)
		local skill_id = SupremeFieldsWGData.Instance:GetSkillIDList(grade, 1)[1] or 0

		local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(skill_id)
		if not skill_cfg then
			return
		end

		bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon)
		self.node_list.skill_icon.image:LoadSpriteAsync(bundle, asset, function()
			self.node_list.skill_icon.image:SetNativeSize()
		end)
	end
end

function SupremeFieldsActivityView:FlushModel(shop_info)
	local grade = SupremeFieldsWGData.Instance:GetCurShopCfg()
	self.model_display:RemoveAllModel()
	self.fz_display:RemoveAllModel()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.model_display:SetModelResInfo(role_vo, nil, function()
		self.model_display:PlayRoleShowAction()
	end)
	self.model_display:FixToOrthographic(self.root_node_transform)
	local bundle, asset = ResPath.GetSkillFaZhenModel(grade)
	self.fz_display:SetMainAsset(bundle, asset)
end

function SupremeFieldsActivityView:FlushTimeCount()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	local time
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	else
		time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
	end

	if time > 0 then
		if CountDownManager.Instance:HasCountDown("zhi_zun_time") then
			CountDownManager.Instance:RemoveCountDown("zhi_zun_time")
		end

		CountDownManager.Instance:AddCountDown("zhi_zun_time",
			BindTool.Bind(self.FinalUpdateTimeCallBack, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	else
		self:OnComplete()
	end
end

function SupremeFieldsActivityView:FinalUpdateTimeCallBack(now_time, total_time)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		self.node_list["time_str"].text.text = string.format(Language.SupremeFields.ActivityTime, time_str)
	else
		self.node_list["time_str"].text.text = string.format(Language.SupremeFields.OpenActivityTime, time_str)
	end
end

function SupremeFieldsActivityView:OnComplete()
	self.node_list.time_str.text.text = ""
end

function SupremeFieldsActivityView:OnBtnOpenView()
	local grade = SupremeFieldsWGData.Instance:GetCurShopCfg()
	ViewManager.Instance:Open(GuideModuleName.SupremeFieldsWGView, nil, "jump", { type = grade })
	self:Close()
end

function SupremeFieldsActivityView:OnBtnfreeBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local _, is_buy_free = SupremeFieldsWGData.Instance:GetShopIsBuyFlag()
		if is_buy_free then
			TipWGCtrl.Instance:ShowSystemMsg(Language.SupremeFields.AllFreeShopBuy)
			return
		end

		SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.Activity_Sell)
	end
end

function SupremeFieldsActivityView:OnBtnSellBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local buy_tmb_count = SupremeFieldsWGData.Instance:GetShopIsBuyFlag()
		local _, cur_grade_cfg = SupremeFieldsWGData.Instance:GetCurShopCfg()

		if buy_tmb_count >= cur_grade_cfg.buy_count_limit then
			TipWGCtrl.Instance:ShowSystemMsg(Language.SupremeFields.AllShopBuy)
			return
		end

		if not IsEmptyTable(cur_grade_cfg) then
			RechargeWGCtrl.Instance:Recharge(cur_grade_cfg.price, cur_grade_cfg.rmb_type, cur_grade_cfg.rmb_seq)
		end
	end
end

function SupremeFieldsActivityView:OnBtnShowSkill()
	local grade = SupremeFieldsWGData.Instance:GetCurShopCfg()
	local data = SupremeFieldsWGData.Instance:SkillShowCfgList(grade, 1)
	CommonSkillShowCtrl.Instance:SetSupremeFieldsSkillViewDataAndOpen(data)
	self:Close()
end

---------------------------------------SupremeFieldsActivityRewardItem----------------------------------
SupremeFieldsActivityRewardItem = SupremeFieldsActivityRewardItem or BaseClass(BaseRender)

function SupremeFieldsActivityRewardItem:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.icon)
end

function SupremeFieldsActivityRewardItem:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function SupremeFieldsActivityRewardItem:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData(self.data)
	local name = ItemWGData.Instance:GetItemName(self.data.item_id)
	self.node_list.name.text.text = name
end
