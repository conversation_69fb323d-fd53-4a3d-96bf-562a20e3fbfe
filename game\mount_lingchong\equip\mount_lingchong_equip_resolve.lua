local index_map_color_list = {
	5, 	--红色
	4, 	--橙色
	3,	--紫色
}
local MAX_COLOR = 3
--骑宠装备凝品
MountLingChongEquipView = MountLingChongEquipView or BaseClass(SafeBaseView)

function MountLingChongEquipView:InitResolveView()
    if not self.np_equip_list then
        self.np_equip_list = {}
        for i = 0, 5 do
            self.np_equip_list[i] = MountEquipResolveItem.New(self.node_list["np_equip_cell"..i], self)
            self.np_equip_list[i]:SetIndex(i)
            self.np_equip_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickEquipResolveItem, self))
        end
    end

    if not self.resolve_bag_grid then
        self.resolve_bag_grid = TuTengBagResolveGrid.New()
        self.resolve_bag_grid:SetStartZeroIndex(false)
        self.resolve_bag_grid:SetIsMultiSelect(true)
        self.resolve_bag_grid:CreateCells({
            col = 4,
            cell_count = 50,
            list_view = self.node_list["np_eq_bag_grid"],
            itemRender = TuTengResolveRender,
            change_cells_num = 2,
        })
        self.resolve_bag_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectItem, self))
    end

    if self.ph_resolve_list == nil then
		self.ph_resolve_list = {}
		local node_num = self.node_list.ph_resolve_list.transform.childCount
        for i = 1, node_num do
        	self.ph_resolve_list[i] = CommonAddAttrRender.New(self.node_list.ph_resolve_list:FindObj("Attr" .. i))
            self.ph_resolve_list[i]:SetAttrNameNeedSpace(true)
        end
	end

    if not self.target_resolve_item then
        self.target_resolve_item = ItemCell.New(self.node_list.target_resolve_item)
    end

    for i = 1, MAX_COLOR do
        XUI.AddClickEventListener(self.node_list.color_list:FindObj("ph_pinzhi_item" .. i), BindTool.Bind(self.SelectColorCallBack, self, i)) --开始是橙色
    end
    --self.color_list_view = AsyncListView.New(TuTengResolveColorListRender, self.node_list["color_list"])
    --self.color_list_view:SetSelectCallBack(BindTool.Bind(self.SelectColorCallBack, self))
    self.cur_select_color_index = RoleWGData.GetRolePlayerPrefsInt("tuteng_select_pinzhi_color")
	if self.cur_select_color_index < 1 then
		self.cur_select_color_index = 2 -- 默认橙色
		RoleWGData.SetRolePlayerPrefsInt("tuteng_select_pinzhi_color", self.cur_select_color_index)
	end

    if not self.model_display_resolve then
		self.model_display_resolve = OperationActRender.New(self.node_list.model_display_resolve)
		self.model_display_resolve:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	--self.color_list_view:SetDefaultSelectIndex(self.cur_select_color_index)
	--self.color_list_view:SetDataList(Language.Bag.NameList2)
	self.node_list["cur_color_text"].text.text = Language.Bag.NameList2[self.cur_select_color_index]

    self.is_show_color_part = true
    self:OnClickSelectColor()
	-- XUI.AddClickEventListener(self.node_list["btn_select_color"], BindTool.Bind(self.OnClickSelectColor, self))
	XUI.AddClickEventListener(self.node_list["close_color_list_part"], BindTool.Bind(self.OnClickSelectColor, self))
    XUI.AddClickEventListener(self.node_list["btn_select_color"], BindTool.Bind(self.OnClickSelectColor, self))
    XUI.AddClickEventListener(self.node_list["btn_qichong_equip_resolve"], BindTool.Bind(self.OnClickEuiupResolve, self))
    XUI.AddClickEventListener(self.node_list["resolve_rule_btn"], BindTool.Bind(self.OnClickResolveRuleBtn, self))

    local model_cfg = MountLingChongEquipWGData.Instance:GetModelCfgByType(self.cur_show_type)
    self:FlushModel(self.model_display_resolve, self.node_list.model_display_resolve, model_cfg)
end

function MountLingChongEquipView:ShowResolveCallBack()

end

function MountLingChongEquipView:DeleteResolveView()
    if self.np_equip_list then
		for k,v in pairs(self.np_equip_list) do
			v:DeleteMe()
		end
		self.np_equip_list = nil
	end

    if nil ~= self.resolve_bag_grid then
		self.resolve_bag_grid:DeleteMe()
		self.resolve_bag_grid = nil
    end

    if self.target_resolve_item then
        self.target_resolve_item:DeleteMe()
        self.target_resolve_item = nil
    end

    -- if self.color_list_view then
	-- 	self.color_list_view:DeleteMe()
	-- 	self.color_list_view = nil
	-- end

    if self.ph_resolve_list then
		for k,v in pairs(self.ph_resolve_list) do
			v:DeleteMe()
		end
		self.ph_resolve_list = nil
	end

    if self.model_display_resolve then
		self.model_display_resolve:DeleteMe()
		self.model_display_resolve = nil
	end

    self.cur_select_color_index = nil
    self.cur_select_resolve_part = nil
    self.cur_select_resolve_part_data = nil
end

function MountLingChongEquipView:FlushResolveView() 
    self:FlushResolveLeftPanel()
    self:FlushResolveBagView()
    self:FlushResolveSelectEquipView()

    if not self.cur_select_resolve_part_data then
        local select_index = 0
        for i = 0, 5 do
            local cur_equip_info = MountLingChongEquipWGData.Instance:GetEquipResolveEquipByPart(self.cur_show_type, i)
            if  cur_equip_info  == nil then
                return
            end
            local is_can = MountLingChongEquipWGData.Instance:GetEquipCanNingPin(self.cur_show_type, i, cur_equip_info.resolve_level)
            if is_can then
                select_index = i
                break
            end
        end

        local cell = self.np_equip_list[select_index] or self.np_equip_list[0]
        self:OnClickEquipResolveItem(cell)
    end
end

function MountLingChongEquipView:FlushResolveLeftPanel()
    for k, v in pairs(self.np_equip_list) do
        local data = MountLingChongEquipWGData.Instance:GetEquipInfoByPart(self.cur_show_type, k) or {}
        v:SetData(data)
    end

    -- self.node_list.resolve_tuteng_zuoqi:SetActive(self.cur_show_type == MOUNT_PET_EQUIP_TYPE.PET)
    -- self.node_list.resolve_tuteng_chongwu:SetActive(self.cur_show_type == MOUNT_PET_EQUIP_TYPE.MOUNT)
    -- self.node_list.resolve_tuteng_huakun:SetActive(self.cur_show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN)

    for i = 0, 5 do
        local cur_equip_info = MountLingChongEquipWGData.Instance:GetEquipResolveEquipByPart(self.cur_show_type, i)
        if  cur_equip_info  == nil then
            return
        end
        self.node_list["qichong_equip_level".. i].text.text = cur_equip_info.resolve_level
    end
end

function MountLingChongEquipView:FlushResolveBagView()
    self.resolve_bag_grid:CancleAllSelectCell()
    local resolve_bag_data_list = MountLingChongEquipWGData.Instance:GetResolveBagItemDataList(self.cur_show_type)
    self.resolve_bag_grid:SetDataList(resolve_bag_data_list)
end

function MountLingChongEquipView:FlushResolveSilder()
    local select_list = self.resolve_bag_grid:GetAllSelectCell()
    local cur_equip_info = MountLingChongEquipWGData.Instance:GetEquipResolveEquipByPart(self.cur_show_type, self.cur_select_resolve_part)
    if  cur_equip_info  == nil then
        return
    end
    local is_max_level = MountLingChongEquipWGData.Instance:IsNingPinMax(self.cur_show_type, self.cur_select_resolve_part, cur_equip_info.resolve_level)
    local cur_level_exp = MountLingChongEquipWGData.Instance:GetEquipNingLevelExp(self.cur_show_type, self.cur_select_resolve_part, cur_equip_info.resolve_level)
    local exp = 0
    local cur_select_solt_exp = cur_equip_info.resolve_exp

    for k, v in pairs(select_list) do
        local select_equip_exp = MountLingChongEquipWGData.Instance:GetMountEquipNingPinExp(v.item_id)
        exp = exp + select_equip_exp
    end

    if exp >= cur_level_exp then
        self.node_list.pre_progress_bar.slider.value = 1
    else
        self.node_list.pre_progress_bar.slider.value = (exp + cur_select_solt_exp) / cur_level_exp
    end

    if cur_select_solt_exp == cur_level_exp or cur_equip_info.resolve_level == 0 then
        self.node_list.progress_bar.slider.value = 0
    else
        self.node_list.progress_bar.slider.value = cur_select_solt_exp / cur_level_exp
    end
    if is_max_level then
        self.node_list.progress_bar.slider.value = 0
        self.node_list.pre_progress_bar.slider.value = 0
        self.node_list.progress_txt.text.text = "--/--"
    else
        self.node_list.progress_txt.text.text = exp .."/" .. cur_level_exp
    end
end

function MountLingChongEquipView:FlushResolveSelectEquipView()
    if not self.cur_select_resolve_part_data then
        return
    end

    local data = {}
    data.item_id = self.cur_select_resolve_part_data.item_id
    self.target_resolve_item:SetData(data)
    self:OneKeySelect()
    local cur_equip_info = MountLingChongEquipWGData.Instance:GetEquipResolveEquipByPart(self.cur_show_type, self.cur_select_resolve_part)
    if  cur_equip_info  == nil then
        return
    end
    local cur_level_cfg = MountLingChongEquipWGData.Instance:GetEquipNingPinAttr(self.cur_show_type, self.cur_select_resolve_part, cur_equip_info.resolve_level)
    local next_level_cfg = MountLingChongEquipWGData.Instance:GetEquipNingPinAttr(self.cur_show_type, self.cur_select_resolve_part, cur_equip_info.resolve_level + 1)
    local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_type", "attr_value", 1, 5)
    for k,v in ipairs(self.ph_resolve_list) do
        v:SetData(attr_list[k])
    end

    local is_max_level = MountLingChongEquipWGData.Instance:IsNingPinMax(self.cur_show_type, self.cur_select_resolve_part, cur_equip_info.resolve_level)
    self.node_list.cur_equip_level.text.text = cur_equip_info.resolve_level
    self.node_list.target_resolve_item_level.text.text = cur_equip_info.resolve_level

    if not is_max_level then
        self.node_list.next_equip_level.text.text = cur_equip_info.resolve_level + 1
    end

    local ningpin_capability = MountLingChongEquipWGData.Instance:GetNingPinAllCapability(self.cur_show_type)
    self.node_list.resolve_cap_value.text.text = ningpin_capability
    self.node_list.next_equip_level:SetActive(not is_max_level)
    self.node_list.np_max_state:SetActive(is_max_level)
    self.node_list.btn_select_color:SetActive(not is_max_level)
    self.node_list.np_eq_bag_grid:SetActive(not is_max_level)
    self.node_list.progress_bar:SetActive(not is_max_level)
    self.node_list.btn_qichong_equip_resolve:SetActive(not is_max_level)
    self.node_list.qichong_equip_resolve_arrow:SetActive(not is_max_level)
    local is_can_up = MountLingChongEquipWGData.Instance:GetEquipNingPinPartRemind(self.cur_show_type, self.cur_select_resolve_part, cur_equip_info.resolve_level)
    self.node_list.btn_qichong_equip_resolve_red:SetActive(is_can_up == 1)
    self:FlushResolveSilder()
    self:FlushResolvePartRed()
end

function MountLingChongEquipView:FlushResolvePartRed()
    for i = 0, 5 do
        local cur_equip_info = MountLingChongEquipWGData.Instance:GetEquipResolveEquipByPart(self.cur_show_type, i)
        if  cur_equip_info  == nil then
            return
        end
        local is_can_up = MountLingChongEquipWGData.Instance:GetEquipNingPinPartRemind(self.cur_show_type, i, cur_equip_info.resolve_level)
        self.node_list["resolve_remind_"..i]:SetActive(is_can_up == 1)
    end
end

function MountLingChongEquipView:OnClickEquipResolveItem(item)
    if not item or not item:GetData() then
        return
    end

    for k, v in pairs(self.np_equip_list) do
        v:SetSelectEffect(false)
    end

    item:SetSelectEffect(true)
    self.cur_select_resolve_part = item:GetIndex()
    self.cur_select_resolve_part_data = item:GetData()
    self:FlushResolveSelectEquipView()
    self:FlushResolveLeftPanel()
end

function MountLingChongEquipView:GetCurSelectResolveData()
    return MountLingChongEquipWGData.Instance:GetEquipResolveEquipByPart(self.cur_show_type, self.cur_select_resolve_part) or {}
end

function MountLingChongEquipView:GetCurSelectResolvePart()
    return self.cur_select_resolve_part
end

-- 选择品质回调
function MountLingChongEquipView:SelectColorCallBack(index)
	-- local data = cell and cell:GetData()
	-- if data == nil then
	-- 	return
	-- end

	--local index = cell:GetIndex()
	if index == self.cur_select_color_index then
		return
	end

	self.cur_select_color_index = index
	self.node_list["cur_color_text"].text.text = Language.Bag.NameList2[self.cur_select_color_index]
	self:OnClickSelectColor()
	self:OneKeySelect()
end

-- 一键选择
function MountLingChongEquipView:OneKeySelect()
	local color = index_map_color_list[self.cur_select_color_index] or 0
    self.resolve_bag_grid:SetTuTengOneKeySelcet(color)
end

-- 展开品质筛选列表
function MountLingChongEquipView:OnClickSelectColor()
	self.is_show_color_part = not self.is_show_color_part
	self.node_list["color_list_part"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_down"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_up"]:SetActive(not self.is_show_color_part)
end

--凝品
function MountLingChongEquipView:OnClickEuiupResolve()
    local select_list = self.resolve_bag_grid:GetAllSelectCell()
    local index = 1
    local resolve_grid_index_list = {}
    for k, v in pairs(select_list) do
        resolve_grid_index_list[index] = v.grid_index
        index = index + 1
    end

    if IsEmptyTable(resolve_grid_index_list) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MountLingChong.NingPinResolveError)
        return
    end

    MountLingChongEquipWGData.Instance:SaveNingpinSelectList(resolve_grid_index_list)
    local np_select_list = MountLingChongEquipWGData.Instance:GetNingpinSelectList()
    MountLingchongEquipWGCtrl.Instance:SendMountPetEquipNingPinReq(self.cur_show_type, self.cur_select_resolve_part, np_select_list)
end

function MountLingChongEquipView:OnBagSelectItem()
    self:FlushResolveSilder()
end

function MountLingChongEquipView:OnClickResolveRuleBtn()
    local role_tip = RuleTip.Instance
    local str_name = Language.MountPetEquip.NameStrTable[self.cur_show_type]
    local equip_part_name = Language.MountPetEquip.EquipPartNameStrTable[self.cur_show_type]
    local PlayDes = string.gsub(Language.MountPetEquip.PlayDes, "name", str_name)
    PlayDes = string.format(PlayDes, equip_part_name)
	if role_tip then
		role_tip:SetContent(PlayDes, Language.MountPetEquip.RuleTitle)
	end
end






------------------------穿戴的装备----------------------------------------------------------------------
MountEquipResolveItem = MountEquipResolveItem or BaseClass(ItemCell)

function MountEquipResolveItem:__init(instance, parent_view)
    self.parent_view = parent_view
    ItemCell.__init(self, instance)
end

function MountEquipResolveItem:LoadCallBack()

end

function MountEquipResolveItem:__delete()
    self.parent_view = nil
end


function MountEquipResolveItem:OnFlush()
    if IsEmptyTable(self.data) or self.data.item_id == nil or self.data.item_id == 0 then
        self:SetVisible(false)
    else
        self:SetVisible(true)
    end
    ItemCell.OnFlush(self)
    local bundle, asset = ResPath.GetCommonImages("a3_ty_xz1")
    --local size = {width = 110, height = 110,}
    self:SetSelectEffectImageRes(bundle, asset)
end

-- 点击格子
function MountEquipResolveItem:OnClick()
	if self.tip_callback ~= nil then
		local is_black = self.tip_callback()
		if is_black == true then
			return
		end
	end

    if self.is_showtip then
        if self.parent_view and (self.parent_view:GetCurSelectResolvePart() == self:GetIndex()) then
            if self.data then
                local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
                if nil == item_cfg then return end
                TipWGData.Instance:SetDefShowBuyCount(self.show_buy_num or 1)
                TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_MOUNTEQUIP_EQUIP, nil)
            end
        end
    end
	BaseRender.OnClick(self)
end






TuTengBagResolveGrid = TuTengBagResolveGrid or BaseClass(AsyncBaseGrid)

function TuTengBagResolveGrid:SetTuTengOneKeySelcet(color)
	self.select_tab[1] = {}
	self.cur_multi_select_num = 0

	for i = 1, self.has_data_max_index do
		local data = self.cell_data_list[i]
        local _, equip_color = ItemWGData.Instance:GetItemColor(data.item_id)
		if not IsEmptyTable(data) then
			if equip_color <= color then
                if self.cur_multi_select_num < 50 then
                    self.cur_multi_select_num = self.cur_multi_select_num + 1
                    self.select_tab[1][i] = true
                else
                    break
                end
            end
		end
	end

	self:__DoRefreshSelectState()
end

function TuTengBagResolveGrid:IsSelectMultiNumLimit(cell_index)
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= 50 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.EquipTarget.ResloveLimit)
            return true
        end
    end

    return false
end




TuTengResolveColorListRender = TuTengResolveColorListRender or BaseClass(BaseRender)
function TuTengResolveColorListRender:OnFlush()
	self.node_list.lbl_pinzhi_name.text.text = self.data
	--self.node_list.select_pinzhi_bg:SetActive(self.is_select)
	--self.node_list.line:SetActive(not self.is_select)
end

function TuTengResolveColorListRender:OnSelectChange(is_select)
	if self.node_list.select_pinzhi_bg then
		--self.node_list.select_pinzhi_bg:SetActive(is_select)
		--self.node_list.line:SetActive(not is_select)
	end
end


--熔炼格子
TuTengResolveRender = TuTengResolveRender or BaseClass(ItemCell)
function TuTengResolveRender:__init()
	self:SetIsShowTips(false)
	self:UseNewSelectEffect(true)
end

function TuTengResolveRender:SetSelect(is_select)
	self:SetSelectEffect(is_select)
end