BagCleanView = BagCleanView or BaseClass(SafeBaseView)
local FuHuoType = {
    None = 0,
	Common = 1,
	Here = 2,
}
function BagCleanView:__init()
	self.view_layer = UiLayer.PopTop
	self:SetMaskBg(false,false)

	self.is_modal = true
	self.is_any_click_close = false
	self.killer_name = ""
	self.killer_level = ""
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/dialog_ui_prefab", "layout_bag_clean")
	self.fuhuo_type = FuHuoType.None
end

function BagCleanView:__delete()
end

function BagCleanView:ReleaseCallBack()
	self.bag_type = nil
end

function BagCleanView:SetDataAndOpen(bag_type)
	self.bag_type = bag_type
    self:Open()
end

function BagCleanView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Common.AlertTitile
	self.node_list["btn_go_clean"].button:AddClickListener(BindTool.Bind(self.OnClickGoClean, self))
	self.node_list["btn_cancle"].button:AddClickListener(BindTool.Bind(self.OnClickCancle, self))
	self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.OnClickCancle, self))
end

function BagCleanView:OnFlush()
	-- print_error("self.bag_type",self.bag_type)
	if self.bag_type and Language.Bag.KnapsackTypeName[self.bag_type] then
		self.node_list.text_bekilled_1.text.text = string.format(Language.Bag.BagCountIsMaxStr2,Language.Bag.KnapsackTypeName[self.bag_type])
	else
		self.node_list.text_bekilled_1.text.text = Language.Bag.BagCountIsMaxStr
	end
end

local BagFunName = {
	-- 0   普通背包
	[KNAPSACK_TYPE.INVALID] = {view_name = GuideModuleName.Bag, tab_index = TabIndex.rolebag_bag_all},
	-- 1   普通背包或材料背包
	[KNAPSACK_TYPE.NORMAL] = {view_name = GuideModuleName.Bag, tab_index = TabIndex.rolebag_bag_all},
	-- 2   魂环背包
	[KNAPSACK_TYPE.SHENSHOU] = {view_name = GuideModuleName.ShenShou, key = "open_shenshou_bag", param_t = {0}},
	-- 4   灵宠背包
	[KNAPSACK_TYPE.LINGCHONG] = {view_name = GuideModuleName.LingChongEquipView, tab_index = TabIndex.pet_equip_bag},
	-- 5   坐骑背包
	[KNAPSACK_TYPE.MOUNT] = {view_name = GuideModuleName.MountEquipView, tab_index = TabIndex.mount_equip_bag},
	-- 16  铭文背包
	[KNAPSACK_TYPE.MingWen] = {view_name = GuideModuleName.MingWenView, tab_index = TabIndex.ming_wen_fen_jie},
	-- 21  神机装备
	[KNAPSACK_TYPE.SHENJI_EQUIP] = { view_name = GuideModuleName.HiddenWeaponView, tab_index = TabIndex.shenji_equip_detail },
	-- 22  鲲装备
	[KNAPSACK_TYPE.KUN] = {view_name = GuideModuleName.HuaKunEquipView, tab_index = TabIndex.huakun_equip_bag},
	-- 22  天神灵核背包
	[KNAPSACK_TYPE.TIANSHEN_LINGHE] = {view_name = GuideModuleName.TianShenView, tab_index = TabIndex.tianshen_linghe_uplevel},
	-- 24  五行背包
	[KNAPSACK_TYPE.FIVE_ELEMENTS] = {view_name = GuideModuleName.FiveElementsView, tab_index = TabIndex.five_elements_knapsack},
	-- 27  龙神殿背包
	[KNAPSACK_TYPE.DRAGON_TEMPLE] = {view_name = GuideModuleName.DragonTempleView },
	-- 32  驭兽背包
	[KNAPSACK_TYPE.BAG_WAY_BEAST] = { view_name = GuideModuleName.ControlBeastsView, tab_index = TabIndex.beasts_compose},
	-- 33  机甲背包
	[KNAPSACK_TYPE.BAG_WAY_MECHAN] = { view_name = GuideModuleName.MechaView, tab_index = TabIndex.mecha_equip},
	-- 36  雷法背包
	[KNAPSACK_TYPE.BAG_WAY_THUDNER_MANA] = { view_name = GuideModuleName.ThunderManaSelectView},

	-- [KNAPSACK_TYPE.SEAL] = 				"圣印背包",
	-- [KNAPSACK_TYPE.GOD_DEVIL] = 		"神魔背包",
	-- [KNAPSACK_TYPE.ZODIAC] = 			"生肖背包",
	-- [KNAPSACK_TYPE.TIANSHEN] = 			"天神背包",
	-- [KNAPSACK_TYPE.HUAKUN] = 			"化坤背包",
	-- [KNAPSACK_TYPE.XIANJIE_EQUIP_BAG] = "仙界装备背包",
	-- [KNAPSACK_TYPE.DRAGON_TEMPLE] = 	"龙神殿背包",
	-- [KNAPSACK_TYPE.CHARM] = 			"天印背包",
	-- [KNAPSACK_TYPE.DAOHANG] = 			"道行背包",
	-- [KNAPSACK_TYPE.BAG_WAY_ESOTERICA] = "秘籍背包", -- 有背包，没入口，实际投放没那么多
}

function BagCleanView:OnClickGoClean()
	if self.bag_type and BagFunName[self.bag_type] then
		local info = BagFunName[self.bag_type]
		ViewManager.Instance:Open(info.view_name, info.tab_index, info.key, info.param_t)

		if info.view_name == GuideModuleName.ControlBeastsView then-- 幻兽跳转需要关掉这两界面，不然幻兽那边不刷新
			ControlBeastsWGCtrl.Instance:CloseBeastsPrizeDraw()
			ControlBeastsWGCtrl.Instance:CloseControlBeastsDrawResultView()
		end
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.Bag, "rolebag_bag_all")
	end

	self:Close()
end

function BagCleanView:OnClickCancle()
	self:Close()
end
