QuanMinBeiZhanView = QuanMinBeiZhanView or BaseClass(SafeBaseView)

function QuanMinBeiZhanView:__init()
	self.view_style = ViewStyle.Half

	self:SetMaskBg(true, false)
	self.default_index = 10
	self.view_name = GuideModuleName.QuanMinBeiZhanView

	local assetbundle = "uis/view/quanmin_beizhan_ui_prefab"
	self:AddViewResource(0, assetbundle, "layout_quanmin_beizhan_panel")
	self:AddViewResource(TabIndex.quanmin_beizhan_login, assetbundle, "layout_login")

	self:AddViewResource(TabIndex.quanmin_beizhan_shouchong, assetbundle, "layout_first_recharge")
	self:AddViewResource(TabIndex.quanmin_beizhan_leichong, assetbundle, "layout_total_recharge")

	self:AddViewResource(TabIndex.quanmin_beizhan_duobei, assetbundle, "layout_duobei")
	self:AddViewResource(TabIndex.quanmin_beizhan_longhun, assetbundle, "layout_longhun")
	self:AddViewResource(TabIndex.quanmin_beizhan_laixi, assetbundle, "layout_xingtian_laixi")
	self:AddViewResource(TabIndex.quanmin_beizhan_juanxian, assetbundle, "layout_juanxian")

	self:AddViewResource(TabIndex.quanmin_beizhan_haoli, assetbundle, "layout_haoli")
	self:AddViewResource(TabIndex.quanmin_beizhan_haoli2, assetbundle, "layout_haoli")
	self:AddViewResource(TabIndex.quanmin_beizhan_haoli3, assetbundle, "layout_haoli")
	self:AddViewResource(TabIndex.quanmin_beizhan_haoli4, assetbundle, "layout_haoli")

	--self:AddViewResource(TabIndex.quanmin_beizhan_cap, assetbundle, "layout_cap_bipin_common2")
	--self:AddViewResource(TabIndex.quanmin_beizhan_cap2, assetbundle, "layout_cap_bipin_common2")
	--self:AddViewResource(TabIndex.quanmin_beizhan_cap3, assetbundle, "layout_cap_bipin_common2")
	self:AddViewResource(TabIndex.quanmin_beizhan_cap, assetbundle, "layout_cap_bipin_common")
	self:AddViewResource(TabIndex.quanmin_beizhan_cap2, assetbundle, "layout_cap_bipin_common")
	self:AddViewResource(TabIndex.quanmin_beizhan_cap3, assetbundle, "layout_cap_bipin_common")

	self:AddViewResource(TabIndex.quanmin_beizhan_turntable, assetbundle, "layout_bz_turntable")

	self:AddViewResource(TabIndex.quanmin_beizhan_cap, assetbundle, "layout_cap_bipin_role")
	self:AddViewResource(TabIndex.quanmin_beizhan_cap2, assetbundle, "layout_cap_bipin_server")
	self:AddViewResource(TabIndex.quanmin_beizhan_cap3, assetbundle, "layout_cap_bipin_kajia")

	self:AddViewResource(0, assetbundle, "HorizontalTabbar")
	self:AddViewResource(0, assetbundle, "VerticalTabbar")
	--self:AddViewResource(0, assetbundle, "layout_effect")

	self:AddViewResource(TabIndex.quanmin_beizhan_haoli, assetbundle, "haoli_tab_grid")
	self:AddViewResource(TabIndex.quanmin_beizhan_haoli2, assetbundle, "haoli_tab_grid")
	self:AddViewResource(TabIndex.quanmin_beizhan_haoli3, assetbundle, "haoli_tab_grid")
	self:AddViewResource(TabIndex.quanmin_beizhan_haoli4, assetbundle, "haoli_tab_grid")

	--龙魂冲榜
	self:AddViewResource(TabIndex.quanmin_beizhan_longhun_rank, assetbundle, "layout_longhun_rank")
	self.first_open_recharge = true
end

function QuanMinBeiZhanView:ReleaseCallBack()
	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:ReleaseLoginView()
	self:ReleaseShouChongView()
	self:ReleaseRechargeView()
	self:ReleaseDBView()
	self:ReleaseXingTianLaiXiView()
	self:ReleaseLongHunView()
	self:ReleaseJuanXianView()
	self:ReleaseHaoLiView()
	self:ReleaseCapView()
	self:ReleaseTurnTableView()
	self:ReleaseLongHunRankView()

	if self.tab_count_down and CountDownManager.Instance:HasCountDown(self.tab_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.tab_count_down)
	end
	self.first_open_recharge = true
end

function QuanMinBeiZhanView:LoadCallBack()
	if not self.tabbar then
		self:SetTabIndex()
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabSate, self))
		self.tabbar:Init(self.tab_name_list, self.sub_tab_name_list, "uis/view/quanmin_beizhan_ui_prefab",
			"uis/view/quanmin_beizhan_ui_prefab", self.remind_name_list)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)
	end
end

function QuanMinBeiZhanView:SetLoadTabIndex()
	self:SetTabIndex()
	self.tabbar:SetHorTabData(self.sub_tab_name_list)
	self:FlusnHaoLiBtns()
	self.BZHLdelay_timer = nil
end

function QuanMinBeiZhanView:LoadIndexCallBack(index)
	if index == TabIndex.quanmin_beizhan_login then
		self:InitLoginRewarView()
	elseif index == TabIndex.quanmin_beizhan_shouchong then
		self:InitShouChongView()
	elseif index == TabIndex.quanmin_beizhan_leichong then
		self:InitLeiChongView()
	elseif index == TabIndex.quanmin_beizhan_duobei then
		self:InitDBView()
	elseif index == TabIndex.quanmin_beizhan_longhun then
		self:InitLongHunView()
	elseif index == TabIndex.quanmin_beizhan_juanxian then
		self:InitJuanXianView()
	elseif index == TabIndex.quanmin_beizhan_cap
		or index == TabIndex.quanmin_beizhan_cap2
		or index == TabIndex.quanmin_beizhan_cap3 then
		self:InitCapView(index)
	elseif index == TabIndex.quanmin_beizhan_haoli
		or index == TabIndex.quanmin_beizhan_haoli2
		or index == TabIndex.quanmin_beizhan_haoli3
		or index == TabIndex.quanmin_beizhan_haoli4 then
		self:InitHaoLiView()
	elseif index == TabIndex.quanmin_beizhan_turntable then
		self:LoadTurnTableView()
	elseif index == TabIndex.quanmin_beizhan_laixi then
		self:InitXingTianLaiXiView()
	elseif index == TabIndex.quanmin_beizhan_longhun_rank then
		--龙魂冲榜
		self:InitLongHunRankView()
	end
end

function QuanMinBeiZhanView:ShowIndexCallBack(index)
	if index == TabIndex.quanmin_beizhan_juanxian then
		self:JXGotoLastProgress()
	elseif index == TabIndex.quanmin_beizhan_turntable then
		self:ShowIndexTurnTable()
	elseif index == TabIndex.quanmin_beizhan_longhun then
		self:ShenLongHunIndexCallBack()
	elseif index == TabIndex.quanmin_beizhan_login then
		self:LoginShowIndexCallBack()
	elseif index == TabIndex.quanmin_beizhan_cap
		or index == TabIndex.quanmin_beizhan_cap2
		or index == TabIndex.quanmin_beizhan_cap3 then
		self.node_list.HorizontalTabbarContent.rect.anchoredPosition = Vector2(0, 0)
	elseif index == TabIndex.quanmin_beizhan_haoli
		or index == TabIndex.quanmin_beizhan_haoli2
		or index == TabIndex.quanmin_beizhan_haoli3
		or index == TabIndex.quanmin_beizhan_haoli4 then
		self.node_list.HorizontalTabbarContent.rect.anchoredPosition = Vector2(324, 0)
	elseif index == TabIndex.quanmin_beizhan_shouchong then
		self:ShowShouChongView()
	elseif index == TabIndex.quanmin_beizhan_leichong then
		self:ShowLeiChongView()
	elseif index == TabIndex.quanmin_beizhan_laixi then
		QuanMinBeiZhanWGCtrl.Instance:CSRandActXTLXInfoReq()
	elseif index == TabIndex.quanmin_beizhan_longhun_rank then
		--龙魂冲榜
		self:ShenLongHunRankIndexCallBack()
	end
end

function QuanMinBeiZhanView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.quanmin_beizhan_shouchong then
				self:FlushShouChongView()
			elseif index == TabIndex.quanmin_beizhan_leichong then
				self:FlushLeiChongView()
			elseif index == TabIndex.quanmin_beizhan_haoli
				or index == TabIndex.quanmin_beizhan_haoli2
				or index == TabIndex.quanmin_beizhan_haoli3
				or index == TabIndex.quanmin_beizhan_haoli4 then
				self:FlushHaoLiView(index % 10)
			elseif index == TabIndex.quanmin_beizhan_longhun_rank then
				self:FlushLongHunRankView()
			end
		elseif "login_reward" == k then
			local day_index = v[1] or 0
			self:ShowLoginReward(day_index)
		elseif "login_view" == k then
			self:FlushLoginView()
			--self:FlushLoginSelectDay()
		elseif "first_recharge_view" == k then
			self:FlushShouChongView()
		elseif "leichong_recharge_view" == k then
			self:FlushLeiChongView()
		elseif "duobei_view" == k then
			self:FlushDBView()
		elseif "longhun_view" == k then
			self:FlushLHView()
		elseif "xingtian_laixi" == k then
			self:FlushJiangLinView()
		elseif "juanxian" == k then
			self:FlushJuanXianView()
			self:FlushJXRewardState()
		elseif "haoli" == k then
			self:FlushHaoLiView()
		elseif "role_cap" == k then
			self:FluahRoleCap()
		elseif "turntable" == k then
			self:FlushTurnTableView(v)
		elseif "server_cap" == k then
			self:FluahServerCap()
		elseif "kanjia" == k then
			self:FluahKanJia()
		elseif "change_day_flush" == k then
			self:SetLoadTabIndex()
		end
	end
end

function QuanMinBeiZhanView:SetTabIndex()
	local tb = ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").beizhan_theme_dec
	local remind_data_map = QuanMinBeiZhanWGData.Instance:GetRemindNameMap()
	self.tab_name_list = {}
	self.sub_tab_name_list = {}
	self.remind_name_list = {}

	for k, v in ipairs(tb) do
		self.tab_name_list[v.rank_id] = v.tab_name
		self.remind_name_list[v.rank_id] = {}
		table.insert(self.remind_name_list[v.rank_id], remind_data_map[v.real_id])

		if v.real_id == 71 then
			self.sub_tab_name_list[v.rank_id] = Language.QuanMinBeiZhan.CatTabName
			local len = #self.sub_tab_name_list[v.rank_id]
			if len > 1 then
				for i = 1, len - 1 do
					table.insert(self.remind_name_list[v.rank_id], remind_data_map[v.real_id + i])
				end
			end
		elseif v.real_id == 81 then
			self.sub_tab_name_list[v.rank_id] = QuanMinBeiZhanWGData.Instance:GetHaoLiTabName()
			local len = #self.sub_tab_name_list[v.rank_id]
			if len > 1 then
				for i = 1, len - 1 do
					table.insert(self.remind_name_list[v.rank_id], remind_data_map[v.real_id + i])
				end
			end
		end
	end
end

function QuanMinBeiZhanView:SetHaoLiTabState()
	CountDownManager.Instance:RemoveCountDown(self.tab_count_down)
	local cur_day = QuanMinBeiZhanWGData.Instance:GetHaoLiDayNum()
	local theme_count = QuanMinBeiZhanWGData.Instance:GetThemeCount()
	for i = cur_day, theme_count - 1 do
		self.tabbar:SetToggleEnable(TabIndex.quanmin_beizhan_haoli + i, false)
	end
end

function QuanMinBeiZhanView:SetTabSate()
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_login,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_login))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_shouchong,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_shouchong))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_leichong,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_leichong))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_duobei,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_duobei))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_longhun,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_longhun))
	-- self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_juanxian, QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_juanxian))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_juanxian, false)
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_turntable,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_turntable))

	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_cap,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_cap))
	--self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_cap2, QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_cap))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_cap2, false)
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_cap3,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_cap))

	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_haoli,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_haoli))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_haoli2,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_haoli))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_haoli3,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_haoli))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_haoli4,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_haoli))
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_laixi,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_laixi))

	--龙魂冲榜
	self.tabbar:SetToggleVisible(TabIndex.quanmin_beizhan_longhun_rank,
		QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_longhun_rank))

	self.tabbar:SetHorToggleSelect(QuanMinBeiZhanWGData.Instance:GetCanBuyGiftTabIndex())
end

function QuanMinBeiZhanView:TabTimeCountDown()
	self.tab_count_down = "tab_count_down"
	CountDownManager.Instance:AddCountDown(self.tab_count_down, nil, BindTool.Bind1(self.SetHaoLiTabState, self),
		TimeWGCtrl.Instance:GetServerTime() + 1, nil, 1)
end
