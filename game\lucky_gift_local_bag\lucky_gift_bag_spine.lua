LuckyGiftBagSpine = LuckyGiftBagSpine or BaseClass(SafeBaseView)

local grade_reward_item_width = 352

function LuckyGiftBagSpine:__init()
    self:SetMaskBg(false, false)
    self.view_layer = UiLayer.Pop
    self.view_name = "LuckyGiftBagSpine"
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "lucky_gift_bag_spine")
end

function LuckyGiftBagSpine:ReleaseCallBack()
    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

    if self.call_back then
        self.call_back = nil
    end
	
	if self.operate_show_tween then
		self.operate_show_tween:Kill()
		self.operate_show_tween = nil
	end
end

function LuckyGiftBagSpine:CloseCallBack()
    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

	if self.operate_show_tween then
		self.operate_show_tween:Kill()
		self.operate_show_tween = nil
	end

	if self.call_back then
        self.call_back = nil
    end

	self.node_list["effect_root"]:SetActive(false)
end

function LuckyGiftBagSpine:OpenCallBack()
end

function LuckyGiftBagSpine:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_click"], BindTool.Bind(self.OnClickClose, self))

end

function LuckyGiftBagSpine:OnFlush(param_t, index)
	self:PlaySpineAnim()
end

function LuckyGiftBagSpine:PlaySpineAnim()
    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

	self.node_list["effect_root"]:SetActive(true)
	self.delay_play_draw_idle = GlobalTimerQuest:AddDelayTimer(function()

		if self.node_list and self.node_list.bg_root then
			self:ShakeNodeAnimiBurst(self.node_list.bg_root, self.operate_show_tween)
		end

		if self.node_list and self.node_list.effect_root then
			self:ShakeNodeAnimiBurst(self.node_list.effect_root, self.operate_show_tween)
		end

		self.delay_play_draw_idle2 = GlobalTimerQuest:AddDelayTimer(function()
			if self.call_back then
				self.call_back()
				self.call_back = nil
			end
			TryDelayCall(self, function()
				self:Close()
			end, 0.5, "DelayLuckyGiftClose")
		end, 0.5)
	end, 1.5)
end

function LuckyGiftBagSpine:OnClickClose()
    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.call_back then
        self.call_back()
        self.call_back = nil
    end

    TryDelayCall(self, function()
        self:Close()
    end, 0.5, "DelayLuckyGiftClose")
end

-- 震动
function LuckyGiftBagSpine:ShakeNodeAnimiBurst(trans, sequence)
	sequence = sequence or DG.Tweening.DOTween.Sequence()
	local pos = trans.transform.anchoredPosition
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(30, 30, 0), 0.01)) 	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-30, 30, 0), 0.02))	--左5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-30, -30, 0), 0.02))	--下5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(30, -30, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos, 0.02))	--恢复 0
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-30, 30, 0), 0.01))	--左5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(30, 30, 0), 0.02)) 	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-30, -30, 0), 0.02))	--下5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(30, -30, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos, 0.02))	--恢复 0
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-30, -30, 0), 0.01))	--下5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(30, 30, 0), 0.02)) 	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-30, 30, 0), 0.02))	--左5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(30, -30, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos, 0.02))	--恢复 0
    sequence:SetEase(DG.Tweening.Ease.Linear)
end


function LuckyGiftBagSpine:SetCallBackAndOpen(call_back)
	self.call_back = call_back
    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end