WuHunSelectBaoShiView = WuHunSelectBaoShiView or BaseClass(SafeBaseView)

function WuHunSelectBaoShiView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function WuHunSelectBaoShiView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{sizeDelta = Vector2(300, 540)})
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_select_baoshi")
end

function WuHunSelectBaoShiView:__delete()

end

function WuHunSelectBaoShiView:ReleaseCallBack()
	if self.stone_list_view then
		self.stone_list_view:DeleteMe()
		self.stone_list_view = nil
	end
	self.select_equip_list = nil
end

function WuHunSelectBaoShiView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.WuHunZhenShen.WuhunFrontBaoshiTxt8  --选择镶嵌宝石 文字
	self:CreateEquipBaoShiListView()
end

function WuHunSelectBaoShiView:SetData(data)
	self.select_equip_list = data.list
	self.data = data
end

function WuHunSelectBaoShiView:OnFlush()
	self:FlushStrengthListDataSource()
end

-- 创建列表
function WuHunSelectBaoShiView:CreateEquipBaoShiListView()
	self.stone_list_view = AsyncListView.New(WuHunSelectBaoShiRender, self.node_list["ph_select_baoshi_list_view"])
	self.stone_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectStrengthItemHandler, self))
end

--设置移除按钮是否显示
function WuHunSelectBaoShiView:SetRemoveBtnState(boo)
	XUI.SetButtonEnabled(self.node_list.btn_remove, boo)
end

-- 刷列表数据源
function WuHunSelectBaoShiView:FlushStrengthListDataSource()
    local itemid = WuHunFrontWGData.Instance:GetFrontGemEngraveItemId(self.data.wuhun_id, 
        self.data.hunzhen_index, 
		self.data.front_gem_index, 
		self.data.engrave_index
	)

	if itemid ~= 0 then
		self.node_list.title_view_name.text.text = Language.WuHunZhenShen.WuhunFrontBaoshiTxt9
	else
		self.node_list.title_view_name.text.text = Language.WuHunZhenShen.WuhunFrontBaoshiTxt8
	end

	if nil ~= self.stone_list_view then
		self.stone_list_view:SetDataList(self.select_equip_list)
		self.stone_list_view:CancelSelect()
	end
end

-- 选择列表项回调
function WuHunSelectBaoShiView:OnSelectStrengthItemHandler(item, cell_index, is_default, is_click)
	if not is_click then
		return
	end

	if nil == item or nil == item.data then
		return
	end

	local p_data = {}
	for i = 1, 6 do
		if i == self.data.engrave_index then
			p_data[i] = item.data.item_id
		else
			p_data[i] = self.data.engrave_list[i] or 0
		end
	end

	WuHunWGCtrl.Instance:SendWuHunFrontOperate(
        WUHUN_FRONT_OPERATE_TYPE.ENGRAVE_SET, 
        self.data.wuhun_id, 
        self.data.hunzhen_index, 
        self.data.front_gem_index, 
        0, 
        p_data
	)
	self:Close()
end

-----------------------------------------------------------------------------
WuHunSelectBaoShiRender = WuHunSelectBaoShiRender or BaseClass(BaseRender)
function WuHunSelectBaoShiRender:__init()
	self:CreateChild()

	if not self.arrow_tweener then
		self.arrow = self.node_list["img_remind"]
		RectTransform.SetAnchoredPositionXY(self.arrow.rect, -12, -5)
		self.arrow_tweener = self.arrow.gameObject.transform:DOAnchorPosY(0, 0.45)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end
end

function WuHunSelectBaoShiRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
	self.node_list["lbl_name"] = nil
end

function WuHunSelectBaoShiRender:CreateChild()
	self.item_cell = ItemCell.New(self.node_list["ph_item"])

	--XUI.AddClickEventListener(self.view, BindTool.Bind1(self.OnClick, self))
	self:SetRemind(false)
end


function WuHunSelectBaoShiRender:OnFlush()
	if nil == self.data then
		return
	end

	if self.item_cell then
		self.item_cell:SetData(self.data)
		self.item_cell:SetItemTipFrom(ItemTip.FROM_BAOSHI)
	end

	local name_str, attr_str = WuHunFrontWGData.Instance:GetBaoShiNatrue(self.data.item_id)
	self.node_list["lbl_name"].text.text = name_str
	self.node_list["lbl_attr"].text.text = attr_str
	self:SetRemind(true)
end

--红点
function WuHunSelectBaoShiRender:SetRemind(enable)
	self.node_list.img_remind:SetActive(enable)
end
