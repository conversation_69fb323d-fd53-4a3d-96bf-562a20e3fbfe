ShenShouItemRender = ShenShouItemRender or BaseClass(BaseRender)
function ShenShouItemRender:__init()
	self.is_load = false
    self.is_bag = false
	self.is_shenshou = false
end

function ShenShouItemRender:__delete()
	self.is_load = false
	self.is_fight = nil
	self.remind = nil
	self.select_cache = nil
	self.img_cache = nil
	self.parent_view = nil
	self.is_bag = false
	self.is_shenshou = false
end

function ShenShouItemRender:LoadCallBack()
	self.shenshou_icon = self.node_list["ph_shenshou_cell_icon"]
	self.shenshou_bg = self.node_list["ph_shenshou_cell_bg"]
	self.is_fight = self.node_list["img_zhuzhan"]
	self.remind = self.node_list["remind"]
	self.bg = self.node_list["img9_item_hl"]
	self.sb_name = self.node_list["lbl_eq_name"]
	self.select_cache = nil
	self.ima_cache = nil
	self.active_cache = nil
end

function ShenShouItemRender:OnFlush()
	if not self.data then return end

	if not self.is_load then
		local zonghe_pingfen = ShenShouWGData.Instance:GetShenShouBaseScore(self.data.shou_id)
		self.node_list["rich_pingfen"].text.text = string.format(Language.ShenShou.PingFen1, zonghe_pingfen)
		self.is_load = true
	end

	local bundle, asset = ResPath.GetF2SHJImgPath(self.data.icon_head)
	self.shenshou_icon.image:LoadSprite(bundle, "icon_" .. asset, function()
		self.shenshou_icon.image:SetNativeSize()
	end)

	local is_active = ShenShouWGData.Instance:IsShenShouActive(self.data.shou_id)
	if self.img_cache ~= self.data.has_zhuzhan or self.active_cache ~= is_active then
		self.img_cache = self.data.has_zhuzhan
		self.active_cache = is_active
		self.is_fight:SetActive(self.data.has_zhuzhan)
	end

	self.sb_name.text.text = self.data.name
    if(self.is_bag) then
        self.remind:SetActive(ShenShouWGData.Instance:GetEquipBagRemind(self.data.shou_id))
	elseif(self.is_shenshou) then
		self.remind:SetActive(ShenShouWGData.Instance:IsShowShenShouRenderRed(self.data.shou_id))
	else
        self.remind:SetActive(ShenShouWGData.Instance:GetEqRemind(self.data.shou_id))
    end

	self.shenshou_bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_wpk_" .. self.data.series + 1))
end

function ShenShouItemRender:OnSelectChange(is_select)
	if nil == self.bg then
		return
	end
	if is_select ~= self.select_cache then
		self.select_cache = is_select
		self.bg:SetActive(is_select)
	end
end