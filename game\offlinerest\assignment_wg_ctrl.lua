require("game/offlinerest/assignment_wg_data")
require("game/offlinerest/assignment_view")
require("game/guild/guild_my_rent_log_view")
require("game/guild/guild_my_rent_view")
require("game/guild/guild_rent_view")
require("game/guild/guild_shelves_view")

-- 委托任务
AssignmentWGCtrl = AssignmentWGCtrl or BaseClass(BaseWGCtrl)

function AssignmentWGCtrl:__init()
	if AssignmentWGCtrl.Instance ~= nil then
		ErrorLog("[AssignmentWGCtrl] Attemp to create a singleton twice !")
	end
	AssignmentWGCtrl.Instance = self

	self.view = AssignmentView.New(GuideModuleName.AssignmentView)
	self.data = AssignmentWGData.New()

	self.guild_shelves_view = GuildShelvesView.New()
	self.my_rent_log_view = GuildMyRentLogView.New()

	self:RegisterAllProtocols()


    self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
end

function AssignmentWGCtrl:__delete()
	self:CleanTimer()

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end

	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	AssignmentWGCtrl.Instance = nil
end

function AssignmentWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCConsignationTaskExp, "OnSCConsignationTaskExp")	  --历练值更新
	self:RegisterProtocol(SCConsignationTaskSignleTaskInfo, "OnSCConsignationTaskSignleTaskInfo")	  --单个任务更新
	self:RegisterProtocol(SCConsignationTaskRefreshInfo, "OnSCConsignationTaskRefreshInfo")	  --刷新任务信息
	self:RegisterProtocol(SCConsignationTaskAllInfo, "OnSCConsignationTaskAllInfo")	  --委托任务总信息
	self:RegisterProtocol(SCConsignationTaskSingleRentExteriorInfo, "OnSCConsignationTaskSingleRentExteriorInfo")--单个上架信息
	self:RegisterProtocol(CSConsignationTaskClientReq)--委托任务-客户端操作
	self:RegisterProtocol(CSGuildExteriorClientReq)--委托任务-客户端上架外形操作
	self:RegisterProtocol(SCConsignationTaskSelfRentExteriorInfo, "OnSCConsignationTaskSelfRentExteriorInfo")--自己的上架信息
	self:RegisterProtocol(SCConsignationTaskAllRentExteriorInfo, "OnSCConsignationTaskAllRentExteriorInfo")--仙盟上架信息
	self:RegisterProtocol(SCConsignationTaskPlayerRentAndBorrowRecord, "OnSCConsignationTaskPlayerRentAndBorrowRecord")--玩家雇佣记录
	self:RegisterProtocol(SCConsignationTaskAllRentAndBorrowRecord, "OnSCConsignationTaskAllRentAndBorrowRecord")--仙盟上架雇佣记录
	self:RegisterProtocol(SCConsignationTaskFetchRewardInfo, "OnSCConsignationTaskFetchRewardInfo")--委托任务获取奖励下发
end


--------------------------协议返回信息 start--------------------------

function AssignmentWGCtrl:OnSCConsignationTaskExp(protocol)
	self.data:UpdateTaskExp(protocol)

	if self.view:IsOpen() then
		self.view:Flush(0, "FlushExp")
	end

	RemindManager.Instance:Fire(RemindName.RemindAssigment)
	ViewManager.Instance:FlushView(GuideModuleName.TianShenLiLianView, 0, "flush_rent")
end

function AssignmentWGCtrl:OnSCConsignationTaskSignleTaskInfo(protocol)
	self.data:UpdateSingleTaskInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush(0, "AllTask")
	end
end

function AssignmentWGCtrl:OnSCConsignationTaskRefreshInfo(protocol)
	self.data:UpdateTaskInfoList(protocol)

	if self.view:IsOpen() then
		self.view:Flush(0, "AllTask")
	end
end

function AssignmentWGCtrl:OnSCConsignationTaskAllInfo(protocol)
	self.data:UpdateAllTaskInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush(0, "AllTask")
	end
	ViewManager.Instance:FlushView(GuideModuleName.TianShenLiLianView, 0, "flush_rent")


	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ASSIGN_COMPLIT, self.data:GetAssignRemind(),
		function ()
			ViewManager.Instance:Open(GuideModuleName.AssignmentView)
			return true
		end
	)
	RemindManager.Instance:Fire(RemindName.RemindAssigment)
	GlobalEventSystem:Fire(OtherEventType.Task_Status_Change)

	self:StarTaskTime()
end


function AssignmentWGCtrl:OnSCConsignationTaskSingleRentExteriorInfo(protocol)
	self.data:UpdateSingleRentInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_assign, "flush_Rent")
end

function AssignmentWGCtrl:OnSCConsignationTaskSelfRentExteriorInfo(protocol)
	self.data:UpdateMyRentInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush(0, "AllRent")
	end

	if self.guild_shelves_view:IsOpen() then
		self.guild_shelves_view:Flush()
	end

	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_assign, "flush_Rent")
	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_my_assign)
end

function AssignmentWGCtrl:OnSCConsignationTaskAllRentExteriorInfo(protocol)
	self.data:UpdateGuildRentInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush(0, "AllRent")
	end

	if self.guild_shelves_view:IsOpen() then
		self.guild_shelves_view:Flush()
	end

	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_assign, "flush_Rent")
end

function AssignmentWGCtrl:OnSCConsignationTaskPlayerRentAndBorrowRecord(protocol)
	self.data:UpdateSelfRecordList(protocol)
end

function AssignmentWGCtrl:OnSCConsignationTaskAllRentAndBorrowRecord(protocol)
	self.data:UpdateGuildRecordList(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_assign, "flush_log")
end

function AssignmentWGCtrl:OnSCConsignationTaskFetchRewardInfo(protocol)
	--print_error(protocol.reward_list)
	local other_info = nil
	if #protocol.borrow_reward_id_list > 0 then
		other_info = {}
		other_info.best_data = {}
		other_info.best_data.best_text = Language.Assignment.AssignRewardTitle
		other_info.best_data.best_des = ""
		other_info.best_data.item_ids = protocol.borrow_reward_id_list
	end
	TipWGCtrl.Instance:ShowGetCommonReward(protocol.reward_list, nil, other_info)
end
--------------------------协议返回信息 end--------------------------

function AssignmentWGCtrl:SendTaskReq(operate_type, param1, param_list1, param_list2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSConsignationTaskClientReq)
 	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
	protocol.param_list1 = param_list1 or {}
	protocol.param_list2 = param_list2 or {}
 	protocol:EncodeAndSend()
    --print_error("SendReq", protocol)
end

function AssignmentWGCtrl:SendRentReq(operate_type, param1, param2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSGuildExteriorClientReq)
 	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol:EncodeAndSend()
    --print_error("SendReq", protocol)
end

function AssignmentWGCtrl:OpenGuildShelvesView()
    self.guild_shelves_view:Open()
end

function AssignmentWGCtrl:OpenMyRentLogView()
    self.my_rent_log_view:Open()
end

function AssignmentWGCtrl:OpenRentlert(callback, str, show_check_box)
    if not self.alert then
        self.alert = Alert.New()
    end
    self.alert:ClearCheckHook()
    self.alert:SetShowCheckBox(show_check_box == true, "GuildAssign")
    self.alert:SetCheckBoxDefaultSelect(false)
    self.alert:SetLableString(str)
    self.alert:SetOkFunc(function ()
        callback()
    end)
    self.alert:Open()
end

function AssignmentWGCtrl:OnItemDataChange(change_item_id)
    if self.data:IsRefreshConsumeId(change_item_id) and self.view:IsOpen() then
        self.view:Flush(0, "RefershItem")
    end
end



-- 任务完成倒计时 任务完成时主动向服务端请求新数据 服务端不会主动推送
function AssignmentWGCtrl:StarTaskTime()
	self:CleanTimer()
	local data_list = self.data:GetTaskCfgList() or {}
	local min_time = 0
	for key, data in pairs(data_list) do
		local state = self.data:GetTaskState(data.id)
		if state == AssignmentWGData.TASK_STATUS.ASSIGN then
			local total_time = self.data:GetTaskRestTime(data.id)
			if total_time > 0 and (min_time == 0 or total_time < min_time) then
				min_time = total_time
			end
		end
	end
	if min_time > 0 then
		self.timer = GlobalTimerQuest:AddDelayTimer(function()
			AssignmentWGCtrl.Instance:SendTaskReq(ASSIGN_TASK_OPERATE_TASK.INFO)
		end, min_time)
	end
end

function AssignmentWGCtrl:CleanTimer()
	if self.timer then
		GlobalTimerQuest:CancelQuest(self.timer)
		self.timer = nil
	end
end