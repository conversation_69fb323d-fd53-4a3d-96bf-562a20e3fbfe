WuHunTowerSceneLogic = WuHunTowerSceneLogic or BaseClass(CommonFbLogic)

function WuHunTowerSceneLogic:__init()
	WuHunTowerSceneLogic.Instance = self
	self.open_view = false
	self.is_pass = 1
end

function WuHunTowerSceneLogic:__delete()
end

function WuHunTowerSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	WuHunWGCtrl.Instance:OpenWuHunTowerTaskView()

	TaskWGCtrl.Instance:AddFlyUpList(function ()
		if GuajiCache.guaji_type ~= GuajiType.Auto then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		if Scene.Instance:GetSceneType() == SceneType.WUHUN_TOWER_FB then
			MainuiWGCtrl.Instance:SetTaskContents(false)
			MainuiWGCtrl.Instance:SetOtherContents(true)
			MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
			MainuiWGCtrl.Instance:SetTeamBtnState(false)
		end
	end)
end

function WuHunTowerSceneLogic:CloseLoadingCallBack()

end

function WuHunTowerSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	
	WuHunWGCtrl.Instance:CloseWuHunTowerTaskView()

	MainuiWGCtrl.Instance:SetTaskContents(true)
    -- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
end

function WuHunTowerSceneLogic:OpenFbSceneCd()

end
