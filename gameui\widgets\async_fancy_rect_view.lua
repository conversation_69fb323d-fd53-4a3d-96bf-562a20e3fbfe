AsyncFancyRectView = AsyncFancyRectView or BaseClass(AsyncFancyListView)

-- 设置数据源(异步刷新数据 )
function AsyncFancyRectView:SetDataList(data_list)
	if nil == self.data_list then
		self.list_view.rect_scroll_view.CellRefreshDel = BindTool.Bind(self.__RefreshListViewCells, self)
		self.list_view.rect_scroll_view.CellSelectDel = BindTool.Bind(self.OnSelectIndex, self)
	end
	self.data_list = data_list or {}
	self.list_view.rect_scroll_view:SetDataCount(self:GetListViewNumbers())
	self:__Flush(1, {0, 0})
end

-- 选择上一个
function AsyncFancyRectView:SelectPrevCell()
	self.list_view.rect_scroll_view:SelectPrevCell()
end

-- 选择下一个
function AsyncFancyRectView:SelectNextCell()
	self.list_view.rect_scroll_view:SelectNextCell()
end

-- 跳转到指定index
function AsyncFancyRectView:SelectCell(index)
	self.list_view.rect_scroll_view:SelectCell(index)
end