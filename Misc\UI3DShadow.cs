﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UI3DShadow : MonoBehaviour
{
    private ShadowProjector shadowProjector;

    private void Awake()
    {
        shadowProjector = this.GetComponentInChildren<ShadowProjector>();
        shadowProjector.SetLightForward(GetComponentInChildren<Light>(true).transform.forward);
        shadowProjector.SetCullingMasks(new string[] { "UI3D" });
        shadowProjector.SetIsReplaceShadow(false);
    }

    private void Update()
    {
        this.TryUpdateApplyLight();
    }

    public void SetTarget(Transform target)
    {
        if (null == shadowProjector) return;

        shadowProjector.SetTarget(target);
    }

    private void TryUpdateApplyLight()
    {
#if UNITY_EDITOR
        if (null != shadowProjector)
        {
            shadowProjector.SetLightForward(GetComponentInChildren<Light>(true).transform.forward);
        }
#endif
    }
}
