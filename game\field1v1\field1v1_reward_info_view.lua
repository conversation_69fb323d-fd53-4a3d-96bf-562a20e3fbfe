local TabType =
{
	Tab_1 = 1, --排行榜前三奖励（结算奖励）.
	Tab_2 = 2, --冲榜奖励.
	Tab_3 = 3, --挑战次数奖励.
	Tab_4 = 4, --挑战奖励.
}

Field1v1RewardInfoView = Field1v1RewardInfoView or BaseClass(SafeBaseView)

function Field1v1RewardInfoView:__init()
	self.view_name = GuideModuleName.ActJjc_yulan_reward
	self.is_modal = true
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
		{ sizeDelta = Vector2(812, 574) })
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_reward")
end

function Field1v1RewardInfoView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
	if self.list_reward_view then
		self.list_reward_view:DeleteMe()
		self.list_reward_view = nil
	end
	self.btn_index = nil
end

function Field1v1RewardInfoView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ZhuZaiShenDian.ViewNameLianSheng
	self.list_reward_view = AsyncListView.New(Field1v1RewardInfoItem, self.node_list["ph_item_list"])
	self.list_reward_view:SetStartZeroIndex(true)

	for i = 1, 4 do
		XUI.AddClickEventListener(self.node_list["tab_toggle_" .. i],
			BindTool.Bind(self.SelectBtnType, self, TabType["Tab_" .. i]))

		self.node_list["tab_text_" .. i].text.text = Language.Field1v1.RewardTabGrop[i]
		self.node_list["tab_select_text_" .. i].text.text = Language.Field1v1.RewardTabGrop[i]
	end

	self.node_list["tab_toggle_" .. 3]:SetActive(false)		--屏蔽。策划 玮麟 做完的功能挪到功能界面里了.

	self.node_list.tab_toggle_1.toggle.isOn = true
	self.btn_index = 1
end

function Field1v1RewardInfoView:SelectBtnType(index, is_on)
	if is_on then
		self.btn_index = index
		self:Flush()
	end
end

function Field1v1RewardInfoView:ShowIndexCallBack()
	local init_btn_index_2 = Field1v1WGData.Instance:GetChallengeFieldRankRewardRed()
	--local init_btn_index_3 = Field1v1WGData.Instance:GetChallengeFieldCountRewardRemind()
	if init_btn_index_2 then
		self.btn_index = TabType.Tab_2
	--elseif init_btn_index_3 then
		--self.btn_index = TabType.Tab_3
	else
		self.btn_index = TabType.Tab_1
	end

	self:SelectBtnType(self.btn_index, true)
	self.node_list["tab_toggle_" .. self.btn_index].toggle.isOn = true
end

function Field1v1RewardInfoView:OnFlush()
	if nil == self.btn_index then
		return
	end

	local init_btn_index_2, pos_2 = Field1v1WGData.Instance:GetChallengeFieldRankRewardRed()
	--local init_btn_index_3, pos_3 = Field1v1WGData.Instance:GetChallengeFieldCountRewardRemind()

	self.node_list.tab_btn_remind_2:SetActive(init_btn_index_2)
	--self.node_list.tab_btn_remind_3:SetActive(init_btn_index_3)

	self.node_list.condition_text.text.text = Language.Field1v1.RewardConditionText[self.btn_index]

	local str = ""
	local reward_data = {}
	local jump_pos = 0
	if self.btn_index == TabType.Tab_1 then
		reward_data = Field1v1WGData.Instance:GetChallengeFieldRankEndRewardCfg()
		self.list_reward_view:SetStartZeroIndex(false)
	elseif self.btn_index == TabType.Tab_2 then
		reward_data = Field1v1WGData.Instance:GetRankRewardCfb()
		self.list_reward_view:SetStartZeroIndex(true)
		jump_pos = pos_2
	-- elseif self.btn_index == TabType.Tab_3 then
	-- 	reward_data = Field1v1WGData.Instance:GetChallengeFieldCountRewardCfgBySpSort()
	-- 	self.list_reward_view:SetStartZeroIndex(false)
	-- 	jump_pos = pos_3

	-- 	local battle_count = Field1v1WGData.Instance:GetChallengeFieldBattleCount()
	-- 	str = string.format(Language.Field1v1.RankRewardDes2, battle_count)
	elseif self.btn_index == TabType.Tab_4 then
		self.list_reward_view:SetStartZeroIndex(false)
		reward_data = Field1v1WGData.Instance:GetChallengeFieldRewardInfo()
	end

	self.list_reward_view:SetDataList(reward_data)
	self.list_reward_view:JumpToIndex(jump_pos, 2)

	if self.btn_index ~= TabType.Tab_3 then
		local jjc_info = Field1v1WGData.Instance:GetUserinfo()
		if jjc_info then
			str = string.format(Language.Field1v1.RankRewardDes, jjc_info.rank, jjc_info.best_rank_pos)
		end
		self.node_list.tip_desc.text.text = Language.Field1v1.RankRewardDes3
	end
	self.node_list.tip_bg:SetActive(self.btn_index ~= TabType.Tab_3)

	self.node_list.my_rank_desc.text.text = str
end

function Field1v1RewardInfoView:GetField1v1RewardTabType()
	return self.btn_index
end

-----------------------------------------------------
-- Field1v1RewardInfoItem
-----------------------------------------------------
Field1v1RewardInfoItem = Field1v1RewardInfoItem or BaseClass(BaseRender)
function Field1v1RewardInfoItem:__init()
	self:CreateChild()
end

function Field1v1RewardInfoItem:__delete()
	for i, v in ipairs(self.item_list) do
		v:DeleteMe()
	end

	self.item_list = {}
	if self.reward_rank_list then
		self.reward_rank_list:DeleteMe()
		self.reward_rank_list = nil
	end
end

function Field1v1RewardInfoItem:CreateChild()
	self.item_list = {}
	self.reward_rank_list = AsyncListView.New(RewardPreviousCellNew, self.node_list.ph_reward_rank_list)
end

function Field1v1RewardInfoItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.get_reward, BindTool.Bind(self.ClickGetBtn, self))
	self.type = 1
end

function Field1v1RewardInfoItem:ClickGetBtn()
	if self.type == TabType.Tab_2 then
		Field1v1WGCtrl.Instance:SendChallengeField(CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_RANK_REWARD,
			self.data.index)
	elseif self.type == TabType.Tab_3 then
		Field1v1WGCtrl.Instance:SendChallengeField(CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_COUNT_REWARD,
			self.data.index)
	end
end

function Field1v1RewardInfoItem:OnFlush()
	if not self.data then
		return
	end

	self.node_list.yi_lingqu_image:SetActive(false)
	--self.node_list.yi_lingqu_image_weidacheng:SetActive(false)

	self.type = Field1v1WGCtrl.Instance:GetField1v1RewardTabType()

	local reward_data = {}
	local function InsertReward(data)
		for i = 0, #data do
			if data[i] then
				table.insert(reward_data, data[i])
			end
		end
	end

	if self.data.reward_coin and self.data.reward_coin > 0 then
		table.insert(reward_data,
			{ item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN, num = self.data.reward_coin, is_bind = 1 })
	end

	if self.data.reward_shengwang and self.data.reward_shengwang > 0 then
		local item_shengwang = { item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR, num = self.data.reward_shengwang }
		table.insert(reward_data, item_shengwang)
	end

	local str = ""
	local is_can_click = false
	--local is_not_reach = true
	local is_ylq = false
	if self.type == TabType.Tab_1 then
		str = string.format(Language.Field1v1.RankPosDes, self.data.rank_pos + 1)
		--is_not_reach = false
		InsertReward(self.data.rank_end_reward)
	elseif self.type == TabType.Tab_2 then
		if self.data.min_rank_pos == self.data.max_rank_pos then
			str = string.format(Language.Field1v1.RankPosDes, self.data.min_rank_pos + 1)
		elseif self.data.min_rank_pos >= 100 then
			str = string.format(Language.Field1v1.RankLastPosDes, self.data.min_rank_pos + 1)
		else
			str = string.format(Language.Field1v1.RankRandPosDes, self.data.min_rank_pos + 1, self.data.max_rank_pos + 1)
		end

		local cur_rank = 0
		local jjc_info = Field1v1WGData.Instance:GetUserinfo()
		if jjc_info and jjc_info.rank then
			cur_rank = jjc_info.rank
		end

		local rank_reward_flag = Field1v1WGData.Instance:GetChallengeFieldRankRewardByIndex(self.data.index)
		is_can_click = cur_rank <= self.data.max_rank_pos and rank_reward_flag == 0
		is_ylq = rank_reward_flag == 1
		--is_not_reach = not is_ylq and not is_can_click

		InsertReward(self.data.reward_item)
	elseif self.type == TabType.Tab_3 then
		str = self.data.battle_count

		local battle_count = Field1v1WGData.Instance:GetChallengeFieldBattleCount()
		local count_reward_flag = Field1v1WGData.Instance:GetChallengeFieldCountRewardByIndex(self.data.index)

		is_can_click = battle_count >= self.data.battle_count and count_reward_flag == 0
		is_ylq = count_reward_flag == 1
		--is_not_reach = not is_ylq and not is_can_click

		InsertReward(self.data.count_reward)
	elseif self.type == TabType.Tab_4 then
		str = Language.Field1v1.ChallengeFieldRewardType[self.data.challenge_type]
		InsertReward(self.data.challenge_reward)
	end

	self.node_list.lbl_rank.text.text = str
	self.node_list.get_reward:SetActive(is_can_click)
	self.node_list.yi_lingqu_image:SetActive(is_ylq)
	--self.node_list.yi_lingqu_image_weidacheng:SetActive(is_not_reach)

	self.reward_rank_list:SetDataList(reward_data, 0)
end

-----------------------------------------------------
-- RewardPreviousCellNew
-----------------------------------------------------
RewardPreviousCellNew = RewardPreviousCellNew or BaseClass(BaseRender)

function RewardPreviousCellNew:__init()

end

function RewardPreviousCellNew:LoadCallBack()
	self.base_cell = ItemCell.New(self.node_list["pos"])
end

function RewardPreviousCellNew:ReleaseCallBack()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end

function RewardPreviousCellNew:OnFlush()
	if nil == self.data then return end
	self.base_cell:SetData(self.data)
	local txt1
	if self.data.num < 10000 then
		txt1 = self.data.num
	else
		txt1 = string.format("%.1f", tonumber(self.data.num) / 10000) .. Language.Common.Wan
	end
	self.base_cell:SetRightBottomText(txt1)
end
