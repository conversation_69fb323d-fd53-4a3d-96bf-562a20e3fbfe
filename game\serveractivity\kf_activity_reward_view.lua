------------------------------------------------------------------------
--	F2开服活动奖励弹窗
------------------------------------------------------------------------
KfRewardActivityView = KfRewardActivityView or BaseClass(SafeBaseView)

function KfRewardActivityView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_kfserver_reward")
end

function KfRewardActivityView:LoadCallBack()
	self.bipin_select_index = 1
	self.reward_item_list = AsyncListView.New(OpenServerBiPinRewardItem, self.node_list.reward_item_list)
	self.node_list.btn_close_windows.button:AddClickListener(BindTool.Bind(self.Close, self))
end

function KfRewardActivityView:ReleaseCallBack()
	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
	self.bipin_select_index = 1
end

function KfRewardActivityView:OnFlush()
	local rush_type = ServerActivityWGData.Instance:BiPinGetSelectRushType()
	if self.reward_item_list then
		local rank_reward_data_list = ServerActivityWGData.Instance:GetOpenServerActivityRewardConfig(rush_type)
		self.reward_item_list:SetDataList(rank_reward_data_list)
	end
end



-------------------------------------------------------------------------------------------------------

OpenServerBiPinRewardItem = OpenServerBiPinRewardItem or BaseClass(BaseRender)

function OpenServerBiPinRewardItem:__delete()
	for k,v in pairs(self.item_cell_list) do
		v:DeleteMe()
	end
	self.item_cell_list = nil
end

function OpenServerBiPinRewardItem:LoadCallBack()
	self.item_cell_list = {}
	self.node_list.get_reward_btn.button:AddClickListener(BindTool.Bind(self.OnClickGetRewardBtn, self))
end

function OpenServerBiPinRewardItem:OnFlush()
	local data = self:GetData()

	self:SetItemCellList(data.reward_item)

	if ServerActivityWGData.Instance:IsSpecialRank(data.rush_type) then
		self:FlsuhSpecialDesc()
	else
		self:FlsuhDesc()
	end

	self:FlushBtn()
end

function OpenServerBiPinRewardItem:FlsuhDesc()
	local data = self:GetData()
	local rank_name = Language.OpenServer.LingQuLimit4[data.rush_type] or ""
	local rank_unit = Language.OpenServer.Unit[data.rush_type] or ""

	local rank_count = tostring(data.min_rank)
	if data.min_rank ~= data.max_rank then
		rank_count = rank_count .. "-" .. data.max_rank
	end

	if data.rush_type == 10 then -- 战力提升榜
		self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinZhanliDesc, rank_count)
		self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinZhanliCondition, data.reach_value)
	else
		self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRankDesc, rank_name, rank_count)
		self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinRankConditionDesc, rank_name, data.reach_value, rank_unit)
	end
end

function OpenServerBiPinRewardItem:FlsuhSpecialDesc()
	local data = self:GetData()
	local body_show_type = data.rush_type == 3 and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
	local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, data.reach_value) or {}
	local rank_name = Language.OpenServer.LingQuLimit2[data.rush_type] or ""
	local rank_count = tostring(data.min_rank)
	if data.min_rank ~= data.max_rank then
		rank_count = rank_count .. "-" .. data.max_rank
	end
	self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRankDesc, rank_name, rank_count)
	self.node_list.lbl_condition.text.text = string.format(Language.OpenServer.BiPinRankConditionDesc2, rank_name, rank_count, cfg.grade_num or 0, cfg.star_num or 0)
end

function OpenServerBiPinRewardItem:SetItemCellList(reward_data)
	local reward_list = SortTableKey(reward_data)
	local item_cell_list = self.item_cell_list
	if #item_cell_list < #reward_list then
		local cell_parent = self.node_list.reward_content
		for i = #item_cell_list + 1, #reward_list do
			item_cell_list[i] = ItemCell.New(cell_parent)
		end
		self.item_cell_list = item_cell_list
	end

	for i,cell in ipairs(item_cell_list) do
		if reward_list[i] then
			cell:SetData(reward_list[i])
			cell:SetActive(true)
		else
			cell:SetActive(false)
		end
	end
end

function OpenServerBiPinRewardItem:FlushBtn()
	local data = self:GetData()
	--local rank_reward_rank = ServerActivityWGData.Instance:GetOpenServerBiPinRankRewardFlag(data.rush_type)
	local rank_reward_fetch = ServerActivityWGData.Instance:GetOpenServerBiPinRankRewardFetch(data.rush_type)
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(data.rush_type)
	local rank_data = RankWGData.Instance:GetActMyRank(opengame_cfg and opengame_cfg.rank_type)
	--local is_show = rank_data and rank_data.my_rank >= data.min_rank and rank_data.my_rank <= data.max_rank
	self.node_list.pass_day_img:SetActive(act_is_over and rank_reward_fetch == 0)
	if rank_reward_fetch >= data.min_rank and rank_reward_fetch <= data.max_rank then
		self.node_list.get_reward_img:SetActive(true)
	else
		self.node_list.get_reward_img:SetActive(false)
	end
end

function OpenServerBiPinRewardItem:OnClickGetRewardBtn()
	ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(
			OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_REWARD_TYPE_RANK_FETCH,
			self.data.rush_type,
			self:GetIndex() - 1
		)
end

function OpenServerBiPinRewardItem:PalyOSBPRewardItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.KfActivityView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.KfActivityView, self.node_list["tween_root"], tween_info)
        end

    end, tween_info.NextDoDelay * wait_index, "osbp_reward_item_" .. wait_index)
end