local KEYIN_BTN_TYPE = {
    KE_YIN = 0;         -- 刻印
    TI_HUAN = 1;        -- 替换
    HE_CHENG = 2;       -- 合成
    OPEN_TIPS = 3;      -- 提示
}
--刻印孔特效
-- local KEYINKONG_EFFECT = {
--     "UI_guanguan_zs0",
--     "UI_guanguan_cs0",
--     "UI_guanguan_hs0",
--     "UI_guanguan_fs0",
-- }


local KEYINFU_MAX_TYPE = 4 --印记最高级类型

local Toggle_Type = {
    Bag = 1,
    Attr = 2
}

EquipmentMarkView = EquipmentMarkView or BaseClass(SafeBaseView)

function EquipmentMarkView:__init()
	self.view_style = ViewStyle.Full
    self.view_name = "EquipmentMarkView"
    self.is_safe_area_adapter = true
	self:SetMaskBg(false)
    self:AddViewResource(0, "uis/view/equipment_mark_ui_prefab", "layout_equipment_mark")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")
end

function EquipmentMarkView:__delete()

end

function EquipmentMarkView:ReleaseCallBack()
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    self.yinji_active_attr_item_list = nil

	if self.yinji_hc_attr_list then
        for k, v in pairs(self.yinji_hc_attr_list) do
            v:DeleteMe()
        end
        self.yinji_hc_attr_list = nil
    end

    self.curr_data_list = nil

    self.is_frist_open = 0
    self.select_kongwei_data = nil
    self.equip_keyinfu_line_map = nil
    self.is_select_equip = false
    self.old_select_equip = nil
    self.is_keyin_succ = false
    self.cur_select_kongwei_index = nil

    if self.yinji_hecheng_cell then
        self.yinji_hecheng_cell:DeleteMe()
        self.yinji_hecheng_cell = nil
    end

    if self.yinji_now_hecheng_cell then
        self.yinji_now_hecheng_cell:DeleteMe()
        self.yinji_now_hecheng_cell = nil
    end
    
    if self.yinji_target_cell then
        self.yinji_target_cell:DeleteMe()
        self.yinji_target_cell = nil
    end

    if self.equip_yinji_list_view then
        self.equip_yinji_list_view:DeleteMe()
        self.equip_yinji_list_view = nil
    end

    if self.yinji_bag_list then
        self.yinji_bag_list:DeleteMe()
        self.yinji_bag_list = nil
    end

    if self.equip_keyinfu_list then
        for k,v in pairs(self.equip_keyinfu_list) do
            v:DeleteMe()
        end
        self.equip_keyinfu_list = nil
    end

    if self.equip_keyinfu_line_list then
        for i,v in ipairs(self.equip_keyinfu_line_list) do
            for ii,vv in ipairs(v) do
                vv:DeleteMe()
            end
        end
        self.equip_keyinfu_line_list = nil
    end

    if self.hecheng_spend_list then
        for i,v in ipairs(self.hecheng_spend_list) do
            for ii,vv in ipairs(v) do
                vv:DeleteMe()
            end
        end
        self.hecheng_spend_list = nil
        self.hecheng_spend_list_root = nil
    end

    if self.yinji_active_attr_item_list then
        for _,v in pairs(self.yinji_active_attr_item_list) do
            v:DeleteMe()
        end
        self.yinji_active_attr_item_list = nil
    end

    if self.yinji_alert then
		self.yinji_alert:DeleteMe()
		self.yinji_alert = nil
	end
end

function EquipmentMarkView:LoadCallBack()
	if not self.money_bar then
	    self.money_bar = MoneyBar.New()
	    local bundle, asset = ResPath.GetWidgets("MoneyBar")
	    local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	    self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

    self:InitParamsNewYinJi()
    self:InitListenerNewYinJi()
    self:InitAllAttrListNewYinJi()
    self:InitYinJiActiveAttrList()

    self.node_list.title_view_name.text.text = Language.ViewName.zhanxing
	self.equip_yinji_list_view = AsyncListView.New(EquipmentMarkYinJiRender, self.node_list.equip_yinji_list_view)
	self.equip_yinji_list_view:SetSelectCallBack(BindTool.Bind(self.OnYinJiSelectCallBack, self))
	self.equip_yinji_list_view:SetDefaultSelectIndex(1)

    self.yinji_bag_list = AsyncListView.New(NewKeYinFuBagRender, self.node_list.yinji_bag_list)

    self.yinji_hecheng_cell = ItemCell.New(self.node_list.yinji_hecheng_cell)
    self.yinji_now_hecheng_cell = ItemCell.New(self.node_list.yinji_now_hecheng_cell)
    self.yinji_target_cell = ItemCell.New(self.node_list.yinji_target_cell)

    if not self.hecheng_spend_list then
        self.hecheng_spend_list = {}
        self.hecheng_spend_list_root = {}
        local node_num = self.node_list.yinji_hecheng_spend_root.transform.childCount
        for i = 1, node_num do
            self.hecheng_spend_list_root[i] = self.node_list.yinji_hecheng_spend_root:FindObj("yinji_hecheng_spend_" .. i)
            self.hecheng_spend_list[i] = ItemCell.New(self.hecheng_spend_list_root[i])
        end
    end

    self.yinji_alert = Alert.New()
    self.yinji_alert:SetShowCheckBox(false)

    self.node_list.new_yinji_bag_attr_root:SetActive(true)
    self.node_list.new_yinji_hecheng_root:SetActive(false)
    self.node_list.yinji_bag_info:SetActive(true)
    self.node_list.yinji_attr_info:SetActive(false)

    self:CreateKeYinFuCellList()
end

function EquipmentMarkView:InitParamsNewYinJi()
    self.yj_select_data = nil
    self.yj_select_index = -1
    self.cur_keyi_btn_type = 0
    self.select_kongwei_index = 0
    self.select_kongwei_data = nil
    self.is_frist_open = 0
    self.select_right_toggle = 0
    self.keyin_btn_type = 0
    self.is_frist_load = false
    self.click_cell_times = 0
    self.is_select_equip = false
    self.old_select_equip = nil
    self.is_keyin_succ = false
    self.cur_select_kongwei_index = 0
    self.ky_save_show_num = 0
    self.keyin_hecheng_show = false
end

function EquipmentMarkView:InitListenerNewYinJi()
    self.node_list.jump_btn.button:AddClickListener(BindTool.Bind(self.OnClickJumpBtn, self))
    self.node_list.all_attr_btn.button:AddClickListener(BindTool.Bind(self.OnClickAllAttrBtn, self))
    self.node_list.empty_bag_bg.button:AddClickListener(BindTool.Bind(self.OnClickJumpBtn, self))
    self.node_list.yinji_tips_btn.button:AddClickListener(BindTool.Bind(self.OnClickTipsBtn, self))
    self.node_list.yinji_hecheng_back_btn.button:AddClickListener(BindTool.Bind(self.OnClickYinJiHeChengBackBtn, self))
    self.node_list.yinji_hecheng_btn.button:AddClickListener(BindTool.Bind(self.OnClickYinJiHeChengBtn, self))

    self.node_list.yinji_attr_btn.toggle:AddClickListener(BindTool.Bind(self.OnClickYinJiToggle, self, Toggle_Type.Attr))
    self.node_list.yinji_bag_btn.toggle:AddClickListener(BindTool.Bind(self.OnClickYinJiToggle, self, Toggle_Type.Bag))

    self.node_list.keyin_btn.button:AddClickListener(BindTool.Bind(self.OnClickKeYinBtn, self))

    self.node_list.yinji_mask.event_trigger_listener:AddEndDragListener(BindTool.Bind(self.OnYinjiEndDrag, self))
end

function EquipmentMarkView:InitAllAttrListNewYinJi()
    if self.yinji_hc_attr_list == nil then
        self.yinji_hc_attr_list = {}
        local node_num = self.node_list.yinji_hecheng_attr_group.transform.childCount
        for i = 1, node_num do
            self.yinji_hc_attr_list[i] = CommonAddAttrRender.New(self.node_list.yinji_hecheng_attr_group:FindObj("attr_" .. i))
            self.yinji_hc_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end
end

function EquipmentMarkView:InitYinJiActiveAttrList()
    local res_async_loader = AllocResAsyncLoader(self, "equipment_mark_yinji_attr_render")
    res_async_loader:Load("uis/view/equipment_mark_ui_prefab", "equipment_mark_yinji_attr_render", nil,
        function(new_obj)
            if IsNil(new_obj) then
                return
            end
            local item_parent = self.node_list.yinji_attr_content.transform
            local item_list = {}
            for i=1,4 do
                local obj = ResMgr:Instantiate(new_obj)
                obj.transform:SetParent(item_parent, false)
                item_list[i] = NewYinJiAttrRender.New(obj)
                item_list[i]:SetIndex(i)
                item_list[i]:SetUnLockCallBack(BindTool.Bind(self.OnClickYinJiActiveBtn, self))
            end
            self.yinji_active_attr_item_list = item_list
        end)
end

function EquipmentMarkView:ShowIndexCallBack(index)
    self.is_frist_open = 0
    self.is_select_equip = false
    self.is_keyin_succ = false
    self.cur_select_kongwei_index = 0
    EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_INFO) -- 请求更新一次全部信息
end

function EquipmentMarkView:CreateKeYinFuCellList()
	if not self.equip_keyinfu_list then
        self.equip_keyinfu_list = {}
        self.equip_keyinfu_line_list = {}
		self.equip_keyinfu_line_map = {}

		local obj_name = "equipment_mark_yinji_cell"
		local res_async_loader = AllocResAsyncLoader(self, obj_name)
		res_async_loader:Load("uis/view/equipment_mark_ui_prefab", obj_name, nil, function(new_obj)
            local pos_list_cfg = NewYinJiJiChengWGData.Instance:GetKongWeiMapCfg()
            if IsNil(new_obj) or IsEmptyTable(pos_list_cfg) then
                return
            end

            local keyinfu_parent_node = self.node_list.keyinfu_cell_parent.rect
            local line_parent_node = self.node_list.line_parent.rect
            local kongwei_pos_cfg = nil
            local equip_keyinfu_list = {}
            for i=1,#pos_list_cfg do
                kongwei_pos_cfg = NewYinJiJiChengWGData.Instance:GetKongWiePosCfgByKongWeiPosID(pos_list_cfg[i] and pos_list_cfg[i].kongwei_pos_id)
                if not kongwei_pos_cfg then
                    break
                end

				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(keyinfu_parent_node, false)
                -- obj.name = obj.name .. i

                local equip_yinji = KeYinFuCell.New(obj)
                equip_yinji.parent_view = self
                equip_yinji:SetIndex(i)
                equip_yinji:SetClickCallBack(BindTool.Bind(self.OnSelectKeYinFuCell, self, i, true))
                equip_yinji:SetPos(kongwei_pos_cfg.x, kongwei_pos_cfg.y)
                equip_keyinfu_list[i] = equip_yinji
            end
            self.equip_keyinfu_list = equip_keyinfu_list

            local equip_keyinfu_line_map = {}
            for i=1,#pos_list_cfg do
                if not equip_keyinfu_list[i] then
                    break
                end

                local temp_list = Split(pos_list_cfg[i].lianxian, ",")
                for j=1,#temp_list do
                    temp_list[j] = tonumber(temp_list[j])
                end

                local index, line_render_list = equip_keyinfu_list[i]:CreateLine(temp_list)
                self.equip_keyinfu_line_list[index] = line_render_list

                for i,v in ipairs(line_render_list) do
                    equip_keyinfu_line_map[index] = equip_keyinfu_line_map[index] or {}
                    equip_keyinfu_line_map[temp_list[i]] = equip_keyinfu_line_map[temp_list[i]] or {}
                    table.insert(equip_keyinfu_line_map[index], v)
                    table.insert(equip_keyinfu_line_map[temp_list[i]], v)
                    v:SetLineParent(line_parent_node)
                end
            end
            self.equip_keyinfu_line_map = equip_keyinfu_line_map

            self.is_frist_load = true
            self:FlushNewjiChengInfo()
		end)
	end
end

function EquipmentMarkView:SetKeYinFuRenderVisable(index, enabled, show_num)
    local render = self.equip_keyinfu_list and self.equip_keyinfu_list[index]
    if not render then
        return
    end
    render:SetCellVisable(enabled)

    local line_list = self.equip_keyinfu_line_map and self.equip_keyinfu_line_map[index]
    if line_list then
        for k,v in pairs(line_list) do
            if enabled then
                local start_index, end_index = v.start_index, v.end_index
                if show_num >= end_index then
                    v:SetLineVisable(true)
                else
                    v:SetLineVisable(false)
                end
            else
                v:SetLineVisable(enabled)
            end
        end
    end
end

-- 设置拖动范围
function EquipmentMarkView:FlushKeYinFuDrawSize(show_num)
    if IsEmptyTable(self.equip_keyinfu_list) then
        return
    end
    if self.ky_save_show_num == show_num then
        return
    end
    self.ky_save_show_num = show_num

    if show_num <= 6 then
        local mask_size = RectTransform.GetSizeDelta(self.node_list.equip_yinji_info_part.rect)
        RectTransform.SetSizeDeltaXY(self.node_list.keyinfu_cell_parent.rect, mask_size.x, mask_size.y)
        RectTransform.SetSizeDeltaXY(self.node_list.line_parent.rect, mask_size.x, mask_size.y)
        RectTransform.SetSizeDeltaXY(self.node_list.equip_yinji_render_parent.rect, mask_size.x, mask_size.y)
        return
    end

    local ex_left_x = 0
    local ex_right_x = 0
    local ex_top_y = 0
    local ex_bottom_y = 0

    local pos_list_cfg = NewYinJiJiChengWGData.Instance:GetKongWeiMapCfg()
    local kongwei_pos_cfg = nil
    for i=1,show_num do
        kongwei_pos_cfg = NewYinJiJiChengWGData.Instance:GetKongWiePosCfgByKongWeiPosID(pos_list_cfg[i] and pos_list_cfg[i].kongwei_pos_id)
        if not kongwei_pos_cfg then
            break
        end

        if kongwei_pos_cfg.x < 0 then
            if ex_left_x > kongwei_pos_cfg.x then
                ex_left_x = kongwei_pos_cfg.x
            end
        elseif kongwei_pos_cfg.x > 0 then
            if ex_right_x < kongwei_pos_cfg.x then
                ex_right_x = kongwei_pos_cfg.x
            end
        end

        if kongwei_pos_cfg.y > 0 then
            if ex_top_y < kongwei_pos_cfg.y then
                ex_top_y = kongwei_pos_cfg.y
            end
        elseif kongwei_pos_cfg.y < 0 then
            if ex_bottom_y > kongwei_pos_cfg.y then
                ex_bottom_y = kongwei_pos_cfg.y
            end
        end
    end

    local size_x = math.max(math.abs(ex_left_x), ex_right_x)
    local size_y = math.max(ex_top_y, math.abs(ex_bottom_y))
    size_x = size_x * 2 + 500
    size_y = size_y * 2 + 500

    RectTransform.SetSizeDeltaXY(self.node_list.keyinfu_cell_parent.rect, size_x, size_y)
    RectTransform.SetSizeDeltaXY(self.node_list.line_parent.rect, size_x, size_y)
    RectTransform.SetSizeDeltaXY(self.node_list.equip_yinji_render_parent.rect, size_x, size_y)
end

function EquipmentMarkView:GetKeYinFuPosByIndex(index, is_v2)
    if not index or IsEmptyTable(self.equip_keyinfu_list) then
        return
    end

    local render = self.equip_keyinfu_list[index]
    if render then
        if is_v2 then
            return render:GetV2Pos()
        else
            return render:GetPos()
        end
    end
end

--刷新
function EquipmentMarkView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
        if  k == "all"  then
            self:FlushNewYinJiView()
        elseif k == "keyin_success" then
            self:OnYinJiKeYinSuccess()
        elseif k == "yinji_active_success" then
			self:OnYinJiActiveSuccess()
        elseif k == "keyinfu_uplevel_success" then
            self:FlushNewjiChengInfo()
			self:OnKeYinFuUpLevelSuccess()
        end
    end
end

--刷新
function EquipmentMarkView:FlushNewYinJiView(param_list)
    self:FlushNewJiChengEquipList()
    self:FlushNewjiChengInfo()
    self:FlushNewjiChengBagList()
    self:FlushNewjiChengAttrInfo()
end

--刷新装备列表
function EquipmentMarkView:FlushNewJiChengEquipList()
    local equip_data = NewYinJiJiChengWGData.Instance:GetEquipYinJiEquipList()
    if not equip_data then
        return
    end

    self.equip_yinji_list_view:SetDataList(equip_data)

    if self.is_frist_open == 0 then
        self.is_frist_open = 1
        local jump_to_index = self:GetYinjiJumpIndex()
        self.equip_yinji_list_view:JumpToIndex(jump_to_index or 1)
    end
end

function EquipmentMarkView:GetYinjiJumpIndex()
    -- 可激活
    local data_list = self.equip_yinji_list_view:GetDataList()
    if not IsEmptyTable(data_list) then
        for i=1,#data_list do
            if NewYinJiJiChengWGData.Instance:GetCurPartIsCanActiveYinJiAttr(data_list[i]) == 1 then
                return i
            end
        end
    end

    -- 可镶嵌
    local equip_index, kongwei_id = NewYinJiJiChengWGData.Instance:GetFristCanInPutMinKongwei()
    if kongwei_id > 0 then
        return equip_index
    end

    --可替换
    equip_index, kongwei_id = NewYinJiJiChengWGData.Instance:GetFirstCanTiHuanKongWei()
    if kongwei_id > 0 then
        return equip_index
    end

    --可合成
    equip_index, kongwei_id = NewYinJiJiChengWGData.Instance:GetFirstCanComposeKongWei()
    if kongwei_id > 0 then
       return equip_index
    end
end

--刷新中间信息
function EquipmentMarkView:FlushNewjiChengInfo()
    if not self.yj_select_data then
        return
    end

    if not self.equip_keyinfu_list then
        return
    end

    local data_list = self.yj_select_data.data_list
    if not data_list then
        return
    end

    local show_num = 0
    if data_list.has_act_max_kongwei_id <= 0 then
        local other_cfg = NewYinJiJiChengWGData.Instance:GetEquipYinJiOtherCfg()
        show_num = other_cfg.moren_kongwei
    else
        local _,xianshi_count = NewYinJiJiChengWGData.Instance:GetCurEquipMaxKongWeiID(self.yj_select_data)
        if data_list.has_act_max_kongwei_id >= xianshi_count then
            show_num = data_list.has_act_max_kongwei_id
        else
            show_num = xianshi_count
        end
    end

    local equip_keyinfu_list = self.equip_keyinfu_list
    for i=1,#equip_keyinfu_list do
        if show_num >= i then
            if equip_keyinfu_list[i] and data_list.kong_wei_info_sc[i] then
                equip_keyinfu_list[i]:SetData(data_list.kong_wei_info_sc[i])
                self:SetKeYinFuRenderVisable(i, true, show_num)
            else
                equip_keyinfu_list[i]:ShowOriginalColor()
            end
        else
            if equip_keyinfu_list[i] then
                equip_keyinfu_list[i]:ShowOriginalColor()
            end
            self:SetKeYinFuRenderVisable(i, false, show_num)
        end
    end

    self:FlushKeYinFuDrawSize(show_num)

    local line_list = NewYinJiJiChengWGData.Instance:GetKongWeiMapCfg()
    for i,v in ipairs(line_list) do
        if self.equip_keyinfu_line_list[i] then
            local kongwei_id_list = Split(v.lianxian, ",")
            for j=1,#kongwei_id_list do
                local kongwei_1 = data_list.kong_wei_info_sc[v.kongwei_id]
                local kongwei_2 = data_list.kong_wei_info_sc[tonumber(kongwei_id_list[j])]
                --两个孔位都镶嵌了刻印符  and   两个孔位都满足镶嵌条件
                if kongwei_1 and kongwei_2 and kongwei_1.item_id > 0 and kongwei_2.item_id > 0 then

                    local kongweiis_enough_limit_1 = NewYinJiJiChengWGData.Instance:GetYinJiCellIsEnoughLimitCondition(self.yj_select_data, kongwei_1.kongwei_id)
                    local kongweiis_enough_limit_2 = NewYinJiJiChengWGData.Instance:GetYinJiCellIsEnoughLimitCondition(self.yj_select_data, kongwei_2.kongwei_id)

                    if kongweiis_enough_limit_1 and kongweiis_enough_limit_2 then
                        for ii,vv in ipairs(self.equip_keyinfu_line_list[i]) do
                            if vv:GetIsLineStarIndexAndEndIndex(kongwei_1.kongwei_id, kongwei_2.kongwei_id) then
                                vv:SetHighLight(true)
                            end
                        end
                    else
                        for ii,vv in ipairs(self.equip_keyinfu_line_list[i]) do
                            if vv:GetIsLineStarIndexAndEndIndex(kongwei_1.kongwei_id, kongwei_2.kongwei_id) then
                                vv:SetHighLight(false)
                            end
                        end
                    end
                else
                    for ii,vv in ipairs(self.equip_keyinfu_line_list[i]) do
                        if kongwei_1 and kongwei_2 and vv:GetIsLineStarIndexAndEndIndex(kongwei_1.kongwei_id, kongwei_2.kongwei_id) then
                            vv:SetHighLight(false)
                        end
                    end
                end
            end

        end
    end

    self.yinji_target_cell:SetData(self.yj_select_data)
    self.yinji_target_cell:SetItemTipFrom(ItemTip.FROM_NEW_YINJI)

    local is_can_active, yinji_type = NewYinJiJiChengWGData.Instance:GetCurPartIsCanActiveYinJiAttr(self.yj_select_data)

    self.node_list.yinji_attr_red_point:SetActive(is_can_active == 1)

    --选中装备自动跳转到一个有红点得孔位 or 首次加载选中有红点孔位 or 刻印成功跳转下个可镶嵌孔位
    if self.is_select_equip or self.is_frist_load or self.is_keyin_succ then
        self.is_select_equip = false

        local select_kongwei_index = 1

        --可刻印
        local kongwei_id = NewYinJiJiChengWGData.Instance:GetFristCanInPutMinKongweiByPart(self.yj_select_data.index)
        if kongwei_id > 0 then
            select_kongwei_index = kongwei_id
        else
            --可替换
            kongwei_id = NewYinJiJiChengWGData.Instance:GetFirstCanTiHuanKongWeiByPart(self.yj_select_data.index)
            if kongwei_id > 0 then
                select_kongwei_index = kongwei_id
            else
                --可合成
                kongwei_id = NewYinJiJiChengWGData.Instance:GetFirstCanComposeKongWeiByPart(self.yj_select_data.index)
                if kongwei_id > 0 then
                    select_kongwei_index = kongwei_id
                end
            end
        end

        if self.is_keyin_succ and not (self.is_select_equip or self.is_frist_load) then
            self.is_keyin_succ = false
            self.click_cell_times = 0
        end

        self:OnSelectKeYinFuCell(select_kongwei_index)
    end
end

--刷新背包信息
function EquipmentMarkView:FlushNewjiChengBagList()
    if not self.select_kongwei_data or not self.yj_select_data then
        return
    end

    local kongwei_cfg = NewYinJiJiChengWGData.Instance:GetPutKongWeiCfgByEquipPart(self.yj_select_data.index, self.select_kongwei_index)
    if not kongwei_cfg then
        return
    end


    local bag_keyinfu_list = NewYinJiJiChengWGData.Instance:GetSortBagKeYinFuListByFightType(kongwei_cfg.fight_type)
    local is_empty_bag = IsEmptyTable(bag_keyinfu_list)

    self.node_list.yinji_bag_list:SetActive(not is_empty_bag)
    self.node_list.empty_bag_bg:SetActive(is_empty_bag)

    if not is_empty_bag then
        self.node_list.keyin_btn:SetActive(true)  
        self.yinji_bag_list:SetDataList(bag_keyinfu_list)
        --镶嵌
        if self.select_kongwei_data.item_id <= 0 then
            self.keyin_btn_type = KEYIN_BTN_TYPE.KE_YIN
            local is_caninput = NewYinJiJiChengWGData.Instance:GetCurKeYinFuIsCanInPut(self.yj_select_data.index, self.select_kongwei_index)
            self.node_list.keyin_btn_remind:SetActive(is_caninput == 1)
        elseif self.select_kongwei_data.item_id > 0 then
            local kongwei_id = self.select_kongwei_data.kongwei_id
            local equip_part =  self.select_kongwei_data.equip_part
            local item_id = NewYinJiJiChengWGData.Instance:GetKongWeoInfo(equip_part,kongwei_id).item_id
            local keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(item_id)
            local target_keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(bag_keyinfu_list[1].item_id)
            if target_keyinfu_cfg.keyinfu_type > keyinfu_cfg.keyinfu_type or keyinfu_cfg.keyinfu_type == KEYINFU_MAX_TYPE then
                --替换
                self.keyin_btn_type = KEYIN_BTN_TYPE.TI_HUAN
                self.node_list.keyin_btn_remind:SetActive(keyinfu_cfg.keyinfu_type ~= KEYINFU_MAX_TYPE)
            else
                --合成
                self.keyin_btn_type = KEYIN_BTN_TYPE.HE_CHENG
                local is_can_compose = NewYinJiJiChengWGData.Instance:GetCurKeYinFuIsCanCompse(self.yj_select_data.index, self.select_kongwei_index)
                self.node_list.keyin_btn_remind:SetActive(is_can_compose == 1)
            end
        end
    else
        --提示
        self.node_list.keyin_btn:SetActive(false)
        self.node_list.keyin_btn_remind:SetActive(false)
        self.keyin_btn_type = KEYIN_BTN_TYPE.OPEN_TIPS
        -- local fake_bag_keyinfu_list = NewYinJiJiChengWGData.Instance:GetFakeKeYinFuBagListByFightType(kongwei_cfg.fight_type)
        -- local tips_item_id = 0
        -- if fake_bag_keyinfu_list then
        --     tips_item_id = fake_bag_keyinfu_list[1].item_id
        -- end
        -- local str = string.format(Language.NewEquipYinJi.KongWeiGetWayDesc, tips_item_id)
        -- self.node_list.yinji_emoji_text.emoji_text = str
    end
    self.node_list.keyin_btn_desc.text.text = Language.NewEquipYinJi.KeYinBtnDesc[self.keyin_btn_type]
end

--刷新印记属性信息
function EquipmentMarkView:FlushNewjiChengAttrInfo()
    if not self.select_kongwei_data or not self.yj_select_data then
        return
    end

    local yj_select_data = self.yj_select_data
    local item_cfg = ItemWGData.Instance:GetItemConfig(yj_select_data.item_id)

    if not item_cfg then
        return
    end

    local yinji_act_attr_cfg = NewYinJiJiChengWGData.Instance:GetYinJiActAttrCfgByType(yj_select_data.index, item_cfg.order, item_cfg.color)

    --当前装备不满足条件显示最低条件属性
    if not yinji_act_attr_cfg then
        local other_cfg = NewYinJiJiChengWGData.Instance:GetEquipYinJiOtherCfg()
        yinji_act_attr_cfg = NewYinJiJiChengWGData.Instance:GetYinJiActAttrCfgByType(yj_select_data.index, other_cfg.order, other_cfg.color) or {}
    end

    local item_list = self.yinji_active_attr_item_list
    if item_list and #item_list > 0 then
        for i = 1,#item_list do
            if yinji_act_attr_cfg and yinji_act_attr_cfg[i] then
                item_list[i]:SetSelectData(self.yj_select_data)
                item_list[i]:SetData(yinji_act_attr_cfg[i])
            end
        end
    end

    -- 总属性描述显示
    local attr_list, keyinfu_attr = NewYinJiJiChengWGData.Instance:GetCurPartAllKeYinFuAttrStr(yj_select_data.index)
    self.curr_data_list = attr_list

    -- 总属性战力
    local total_attr = NewYinJiJiChengWGData.Instance:GetCurPartKeYinFuAndYinJiAttrAllAttr(yj_select_data.index, keyinfu_attr)
    local capability = AttributeMgr.GetCapability(total_attr)
    self.node_list.yinji_cap_num.text.text = capability
end

--刷新合成信息
function EquipmentMarkView:FlushNewjiChengHeChengInfo()
    self.node_list.yinji_hecheng_remind:SetActive(false)
    if not self.select_kongwei_data then
        return
    end

    local keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(self.select_kongwei_data.item_id)
    if not keyinfu_cfg then
        return
    end

    local keyinfu_compose_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuComposeByItemID(self.select_kongwei_data.item_id)
    local is_max_keyinfu = keyinfu_cfg.keyinfu_type >= KEYINFU_MAX_TYPE

    local item_cfg = nil
    local cur_keyinfu_cfg = nil

    if is_max_keyinfu then
        item_cfg = ItemWGData.Instance:GetItemConfig(self.select_kongwei_data.item_id)
        cur_keyinfu_cfg = keyinfu_cfg
    else
        item_cfg = ItemWGData.Instance:GetItemConfig(keyinfu_compose_cfg.target_item_id)
        local target_keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(keyinfu_compose_cfg.target_item_id)
        cur_keyinfu_cfg = target_keyinfu_cfg
    end

    if item_cfg then
        self.yinji_hecheng_cell:SetData({item_id = item_cfg.id})
        self.yinji_now_hecheng_cell:SetData({item_id = self.select_kongwei_data.item_id})
    end

    -- 按钮
    self.node_list.yinji_hecheng_max_flag:SetActive(is_max_keyinfu)
    self.node_list.yinji_hecheng_btn:SetActive(not is_max_keyinfu)

    ---[[ 进度条
    if not is_max_keyinfu then
        if cur_keyinfu_cfg and cur_keyinfu_cfg.item_id > 0 then
            local need_num = NewYinJiJiChengWGData.Instance:GetKeYinFuConversionTableByid(cur_keyinfu_cfg.item_id).num
            local cur_keyinfu_num = NewYinJiJiChengWGData.Instance:GetKeYinFuConversionTableByid(keyinfu_cfg.item_id)
            local item_list, last_num = NewYinJiJiChengWGData.Instance:GetKeYinSpendFuListByFightType(cur_keyinfu_cfg.fight_type, need_num - cur_keyinfu_num.num)

            for i, cell in ipairs(self.hecheng_spend_list) do
                if item_list[i] then
                    self.hecheng_spend_list_root[i]:CustomSetActive(true)
                    cell:SetData(item_list[i])
                else
                    self.hecheng_spend_list_root[i]:CustomSetActive(false)
                end
            end

            -- local bag_has_num = NewYinJiJiChengWGData.Instance:GetKeYinFuConversionTableInBagByFightType(keyinfu_cfg.fight_type) or 0
            -- local has_num = cur_keyinfu_num.num + bag_has_num

            -- self.node_list.yinji_hecheng_slider.slider.value = has_num / need_num
            -- self.node_list.yinji_hecheng_slider_desc.text.text = string.format("%s/%s", has_num, need_num)
            -- local lack_num = (need_num - has_num > 0) and need_num - has_num or 0
     
            self.node_list.yinji_hecheng_remind:SetActive(last_num <= 0)
            self.node_list.yinji_hecheng_limit_root:SetActive(last_num > 0)

            self.node_list.yinji_hecheng_lack_num.text.text = string.format(Language.NewEquipYinJi.YinJiSpendNotEnough, last_num)
        end
    else
        -- self.node_list.yinji_hecheng_slider.value = 1
        -- self.node_list.yinji_hecheng_slider_desc.text.text = "-/-"
        self.node_list.yinji_hecheng_lack_num.text.text = 0
    end
    --]]
    self:FlushYinJiHeChengAttr(keyinfu_cfg, cur_keyinfu_cfg) -- 刻印符属性
end

function EquipmentMarkView:FlushYinJiHeChengAttr(cur_keyinfu_cfg, next_keyinfu_cfg)
    if not cur_keyinfu_cfg then
        return
    end

    local attr_name_list = Language.Common.TipsAttrNameList
    local attr_list = AttributeMgr.GetAttributteByClass(cur_keyinfu_cfg)
    local attr_next_list = AttributeMgr.GetAttributteByClass(next_keyinfu_cfg)
    local sort_list = AttributeMgr.SortAttribute()

    local data_list = {}
    for i,v in ipairs(sort_list) do
        if attr_next_list[v] > 0 then
            local temp = {}
            temp.attr_str = v

            if attr_list[v] > 0 then
                temp.attr_value = attr_list[v]
            else
                temp.attr_value = 0
            end

            temp.add_value = attr_next_list[v] - temp.attr_value
            data_list[#data_list + 1] = temp
        end
    end

    local attr_obj_list = self.yinji_hc_attr_list
    for i = 1, #attr_obj_list do
        if data_list[i] then
            attr_obj_list[i]:SetVisible(true)
            attr_obj_list[i]:SetData(data_list[i])
        else
            attr_obj_list[i]:SetVisible(false)
        end
    end
end

function EquipmentMarkView:FlushYinJiBagRemind(is_click)
    if not self.yj_select_data or not self.select_kongwei_data then
        return
    end

    local is_bag_remind = 0
    is_bag_remind = NewYinJiJiChengWGData.Instance:GetCurKeYinFuIsCanInPut(self.yj_select_data.index, self.select_kongwei_data.kongwei_id)

    if is_bag_remind ~= 1 then
        is_bag_remind = NewYinJiJiChengWGData.Instance:GetCurKeYinFuIsCanTiHuan(self.yj_select_data.index, self.select_kongwei_data.kongwei_id)
    end

    if is_bag_remind ~= 1 then
        is_bag_remind = NewYinJiJiChengWGData.Instance:GetCurKeYinFuIsCanCompse(self.yj_select_data.index, self.select_kongwei_data.kongwei_id)
    end

    self.node_list.yinji_bag_remind:SetActive(is_bag_remind == 1)

    if not is_click and is_bag_remind == 1 then
        self.node_list.yinji_bag_btn.toggle.isOn = true
    end
end

--装备选中
function EquipmentMarkView:OnYinJiSelectCallBack(cell, cell_index, is_default, is_click)
    local select_data = cell:GetData()
    if not select_data then
        return
    end

    self.click_cell_times = 0

    if self.yj_select_index == cell_index then
        self:OnSelectKeYinFuCell(self.select_kongwei_index)
        return
    end

    NewYinJiJiChengWGData.Instance:SetCurSelectEquipPart(select_data.index)
    self.cur_select_kongwei_index = 0
    self.yj_select_index = cell_index
    self.yj_select_data = select_data
    self.is_select_equip = true

    self:FlushNewjiChengInfo()
end

-- 刻印符选中
function EquipmentMarkView:OnSelectKeYinFuCell(kongwei_id, is_click)
    if not self.equip_keyinfu_list or not self.equip_keyinfu_list[kongwei_id] then
        return
    end

    local cell = self.equip_keyinfu_list[kongwei_id]
    local cell_data = cell:GetData()
    if not cell_data then
        return
    end

    if not self.is_frist_load then
        if self.select_kongwei_index == kongwei_id and is_click then
            self.click_cell_times = self.click_cell_times + 1
        else
            self.click_cell_times = 1
        end
    end

    if self.click_cell_times > 1 then
        if cell_data.item_id > 0 then
            TipWGCtrl.Instance:OpenItem({item_id = cell_data.item_id})
        end
        return
    end

    self.cur_select_kongwei_index = 0
    self.is_frist_load = false
    self.select_kongwei_index = kongwei_id
    self.select_kongwei_data = cell_data
    NewYinJiJiChengWGData.Instance:SetCurSelectKongWei(kongwei_id)

    self:OnClickYinJiHeChengBackBtn()
    -- 刷新背包信息
    self:FlushNewjiChengBagList()
    -- 刷背包红点
    self:FlushYinJiBagRemind(is_click)
    -- 刷新属性信息
    self:FlushNewjiChengAttrInfo()
    -- 刷新合成信息
    -- self:FlushNewjiChengHeChengInfo()
    -- 跳转到孔位位置
    self:JumpKeYinFuRenderPos(kongwei_id)

    if self.equip_keyinfu_list then
        for i,v in pairs(self.equip_keyinfu_list) do
            v:SetSelectHLEnable(self.select_kongwei_index == i)
        end
    end
end

--抽奖跳转
function EquipmentMarkView:OnClickJumpBtn()
    FunOpen.Instance:OpenViewNameByCfg("EveryDayRechargeView#everyday_recharge_yingji")
end

--抽奖跳转
function EquipmentMarkView:OnClickAllAttrBtn()
    if not self.curr_data_list then
        return
    end

    local tips_data = {
        title_text = Language.NewEquipYinJi.YinJiAllAttrTitle,
        attr_data = self.curr_data_list,
    }
    TipWGCtrl.Instance:OpenTipsAttrView(tips_data)
end

--提示
function EquipmentMarkView:OnClickTipsBtn()
    TipWGCtrl.Instance:SetRuleContent(Language.NewEquipYinJi.YinJiTipsContent, Language.NewEquipYinJi.YinJiTipsTitle)
end

--印记合成返回按钮
function EquipmentMarkView:OnClickYinJiHeChengBackBtn(is_show)
    self.node_list.new_yinji_hecheng_root:SetActive(is_show)
    self.node_list.new_yinji_bag_attr_root:SetActive(not is_show)
    if is_show then
        self:FlushNewjiChengHeChengInfo()
    end
end

--印记合成按钮
function EquipmentMarkView:OnClickYinJiHeChengBtn()
    if not self.yj_select_data or not self.select_kongwei_data then
        return
    end

    if self.select_kongwei_data.item_id <= 0 then
        return
    end

    local keyinfu_compose_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuComposeByItemID(self.select_kongwei_data.item_id)
    if not keyinfu_compose_cfg then
        return
    end

    EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_COMPOSE, self.yj_select_data.index, self.select_kongwei_data.kongwei_id)
end

function EquipmentMarkView:OnClickYinJiToggle(index)
    self:OnClickYinJiHeChengBackBtn()
    self.select_right_toggle = index
    if index == Toggle_Type.Attr then
        --刷新属性信息
        self:FlushNewjiChengAttrInfo()
    elseif index == Toggle_Type.Bag then
         --刷新背包信息
        self:FlushNewjiChengBagList()
    end
    self.node_list.yinji_attr_info:SetActive(index == Toggle_Type.Attr)
    self.node_list.yinji_bag_info:SetActive(index == Toggle_Type.Bag)
end


--印记激活
function EquipmentMarkView:OnClickYinJiActiveBtn()
    if not self.yj_select_data then
        return
    end
    local is_can_active, yinji_type = NewYinJiJiChengWGData.Instance:GetCurPartIsCanActiveYinJiAttr(self.yj_select_data)

    if not is_can_active == 1 then
        return
    end

    EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_ACT, self.yj_select_data.index, yinji_type)
end

--刻印
function EquipmentMarkView:OnClickKeYinBtn()
    if not self.yj_select_data or not self.select_kongwei_data then
        return
    end

    local kongwei_cfg = NewYinJiJiChengWGData.Instance:GetPutKongWeiCfgByEquipPart(self.yj_select_data.index, self.select_kongwei_index)
    if not kongwei_cfg then
        return
    end

    local bag_keyinfu_list = NewYinJiJiChengWGData.Instance:GetSortBagKeYinFuListByFightType(kongwei_cfg.fight_type)
    local is_empty_bag = false
    if IsEmptyTable(bag_keyinfu_list) then
        bag_keyinfu_list = NewYinJiJiChengWGData.Instance:GetFakeKeYinFuBagListByFightType(kongwei_cfg.fight_type)
        is_empty_bag = true
    end

    if not bag_keyinfu_list then
        return
    end

    if self.keyin_btn_type == KEYIN_BTN_TYPE.KE_YIN then
        if not is_empty_bag and self.select_kongwei_data.item_id <= 0 then

            local is_enough_condition, limit_log_type, limit_cfg = NewYinJiJiChengWGData.Instance:GetYinJiCellIsEnoughLimitCondition(self.yj_select_data, self.select_kongwei_data.kongwei_id)
            --不满足条件
            if not is_enough_condition and limit_log_type ~= 0 and limit_cfg ~= nil then
                local limit1 = ""
                local limit2 = ""
                local limit3 = ""

                if limit_cfg.order > 0 then
                    limit1 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc2, limit_cfg.order)
                end

                if limit_cfg.color > 0 then
                    limit3 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc3, Language.Common.ItemQualityColor[limit_cfg.color])
                end

                if limit_cfg.star > 0 then
                    limit2 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc4, limit_cfg.star)
                end

                TipWGCtrl:ShowSystemMsg(string.format(Language.NewEquipYinJi.KongWeiLimitDesc1, limit1, limit2, limit3))
                return
            end
            EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_PUT, self.yj_select_data.index, self.select_kongwei_data.kongwei_id, bag_keyinfu_list[1].item_id)
        end
    elseif self.keyin_btn_type == KEYIN_BTN_TYPE.TI_HUAN then
        local is_enough_condition, limit_log_type, limit_cfg = NewYinJiJiChengWGData.Instance:GetYinJiCellIsEnoughLimitCondition(self.yj_select_data, self.select_kongwei_data.kongwei_id)
        --不满足条件
        if not is_enough_condition and limit_log_type ~= 0 and limit_cfg ~= nil then
            local limit1 = ""
            local limit2 = ""
            local limit3 = ""

            if limit_cfg.order > 0 then
                limit1 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc2, limit_cfg.order)
            end

            if limit_cfg.color > 0 then
                limit3 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc3, Language.Common.ItemQualityColor[limit_cfg.color])
            end

            if limit_cfg.star > 0 then
                limit2 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc4, limit_cfg.star)
            end

            TipWGCtrl:ShowSystemMsg(string.format(Language.NewEquipYinJi.KongWeiLimitDesc1, limit1, limit2, limit3))
            return
        end

        if not is_empty_bag and self.select_kongwei_data.item_id > 0 then
            local target_keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(bag_keyinfu_list[1].item_id)
            local keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(self.select_kongwei_data.item_id)
            if target_keyinfu_cfg.keyinfu_type < self.yj_select_data.data_list.has_act_yinji_type then
                if self.yinji_alert then
                    self.yinji_alert:SetLableString(Language.NewEquipYinJi.YinJiAlertDesc2)
                    self.yinji_alert:SetOkFunc(function ()
                        EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_PUT, self.yj_select_data.index, self.select_kongwei_data.kongwei_id, bag_keyinfu_list[1].item_id)
                    end)
                    self.yinji_alert:Open()
                end
                return
            elseif keyinfu_cfg.keyinfu_type >= KEYINFU_MAX_TYPE then
                if self.yinji_alert then
                    self.yinji_alert:SetLableString(Language.NewEquipYinJi.YinJiAlertDesc)
                    self.yinji_alert:SetOkFunc(function ()
                        EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_PUT, self.yj_select_data.index, self.select_kongwei_data.kongwei_id, bag_keyinfu_list[1].item_id)
                    end)
                    self.yinji_alert:Open()
                end
                return
            end
            EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_PUT, self.yj_select_data.index, self.select_kongwei_data.kongwei_id, bag_keyinfu_list[1].item_id)
        end
    elseif self.keyin_btn_type == KEYIN_BTN_TYPE.HE_CHENG then
        self:OnClickYinJiHeChengBackBtn(true)
    elseif self.keyin_btn_type == KEYIN_BTN_TYPE.OPEN_TIPS then
        if is_empty_bag then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = bag_keyinfu_list[1].item_id})
        end
    end
end

--刻印或替换成功
function EquipmentMarkView:OnYinJiKeYinSuccess()
    --播放特效
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_xiangqian, is_success = true, parent_node = self.node_list.yinji_effect_root})
    self:FlushNewYinJiView()
    -- 当前部位是否可以激活印记属性
    local is_can_active = NewYinJiJiChengWGData.Instance:GetCurPartIsCanActiveYinJiAttr(self.yj_select_data)
    if is_can_active == 1 then
        self.click_cell_times = 0
        self:OnSelectKeYinFuCell(self.select_kongwei_index)
        return
    end

    -- 当前装备有可镶嵌孔位优先选中当前装备得孔位
    local curpart_caninput_kongwei = NewYinJiJiChengWGData.Instance:GetFristCanInPutMinKongweiByPart(self.yj_select_data.index)
    if curpart_caninput_kongwei > 0 then
        self.click_cell_times = 0
        self:OnSelectKeYinFuCell(curpart_caninput_kongwei)
        return
    end

    --当前装备有可替换孔位优先选中当前装备得孔位
    local curpart_cantihuan_kongwei = NewYinJiJiChengWGData.Instance:GetFirstCanTiHuanKongWeiByPart(self.yj_select_data.index)
    if curpart_cantihuan_kongwei > 0 then
        self.click_cell_times = 0
        self:OnSelectKeYinFuCell(curpart_cantihuan_kongwei)
        return
    end

    local jump_index = self:GetYinjiJumpIndex()
    if jump_index then
        self.is_keyin_succ = true
        self.equip_yinji_list_view:JumpToIndex(jump_index)
    else
        self.click_cell_times = 0
        self:OnSelectKeYinFuCell(self.select_kongwei_index)
    end
end

--印记属性激活成功
function EquipmentMarkView:OnYinJiActiveSuccess()
    -- self.node_list.equip_yinji_render_parent.rect:DOAnchorPos(Vector2(0, 0), 0.5)
    self:FlushNewYinJiView()
    --播放特效
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, parent_node = self.node_list.yinji_effect_root})
end

function EquipmentMarkView:OnYinjiEndDrag(pos)
    if self.ky_save_show_num <= 6 then
        return
    end

    local cell_size = {x = 110, y = 110}
    local mask_size = RectTransform.GetSizeDelta(self.node_list.equip_yinji_info_part.rect)
    local center_pos_x, center_pos_y = RectTransform.GetAnchoredPositionXY(self.node_list.equip_yinji_render_parent.rect)
    local cur_pos = self:GetKeYinFuPosByIndex(self.cur_select_kongwei_index)

    -- 左下角
    local rect_pos_x = -center_pos_x + mask_size.x / 2
    local rect_pos_y = -center_pos_y + mask_size.y / 2


    if GameMath.IsInRect(cur_pos.x, cur_pos.y, rect_pos_x, rect_pos_y, mask_size.x - cell_size.x, mask_size.y - cell_size.y) then
        return
    end

    local keyin_list = self.equip_keyinfu_list
    if not keyin_list or #keyin_list <= 6 then
        return
    end

    local cell_pos = nil
    local dis = 0
    local min_dis = {dis = -1, index = 0}
    local for_num = math.min(self.ky_save_show_num, #keyin_list)
    for i=1,for_num do
        cell_pos = keyin_list[i]:GetPos()
        dis = GameMath.GetDistance(cell_pos.x, cell_pos.y, -center_pos_x, -center_pos_y)
        if min_dis.dis > dis or min_dis.dis == -1 then
            min_dis.dis = dis
            min_dis.index = i
        end
    end

    if min_dis.index > 0 then
        self:OnSelectKeYinFuCell(min_dis.index)
    end
end

function EquipmentMarkView:JumpKeYinFuRenderPos(index)
    local pos = self:GetKeYinFuPosByIndex(index)
    if not pos then
        return
    end

    if index <= 6 then
        self.node_list.equip_yinji_render_parent.rect:DOAnchorPos(Vector2(0, 0), 0.5)
    else
        self.node_list.equip_yinji_render_parent.rect:DOAnchorPos(Vector2(-pos.x, -pos.y), 0.5)
    end
    self.cur_select_kongwei_index = index
end

--刻印符升级成功
function EquipmentMarkView:OnKeYinFuUpLevelSuccess()
    --播放特效
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng, is_success = true, parent_node = self.node_list.yinji_effect_root})

    -- 当前部位是否可以激活印记属性
    local is_can_active = NewYinJiJiChengWGData.Instance:GetCurPartIsCanActiveYinJiAttr(self.yj_select_data)
    if is_can_active == 1 then
        self.click_cell_times = 0
        self:OnSelectKeYinFuCell(self.select_kongwei_index)
        return
    end

    --当前装备有可合成孔位优先选中当前装备得孔位
    local curpart_cantihuan_kongwei = NewYinJiJiChengWGData.Instance:GetFirstCanComposeKongWeiByPart(self.yj_select_data.index)
    if curpart_cantihuan_kongwei > 0 then
        self.click_cell_times = 0
        self:OnSelectKeYinFuCell(curpart_cantihuan_kongwei)
        return
    end

    local jump_index = self:GetYinjiJumpIndex()
    if jump_index then
        self.is_keyin_succ = true
        self.equip_yinji_list_view:JumpToIndex(jump_index)
    else
        self.click_cell_times = 0
        self:OnSelectKeYinFuCell(self.select_kongwei_index)
    end
end

----------------------------------------EquipmentMarkYinJiRender-----------------------------------------------------------

EquipmentMarkYinJiRender = EquipmentMarkYinJiRender or BaseClass(BaseRender)

function EquipmentMarkYinJiRender:__init()
    if self:IsSelect() then
        self:OnSelectChange(true)
    end
end

function EquipmentMarkYinJiRender:__delete()
	self.item_cell:DeleteMe()
    self.item_cell = nil
end

function EquipmentMarkYinJiRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.ph_item)
end

function EquipmentMarkYinJiRender:OnFlush()
    local data = self:GetData()
	if IsEmptyTable(data) then
		return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then
		return
    end

    self.item_cell:SetData(data)
    self.item_cell:SetItemTipFrom(ItemTip.FROM_NEW_YINJI)
    self.node_list["lbl_name"].text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])

    local is_gradient = false
	if item_cfg.color > GameEnum.EQUIP_COLOR_FULL then
        is_gradient = true
    end

    --SetTextSpecialColor(self.node_list["lbl_name"], ShenJiSpecialColor, is_gradient)
    local is_remind = NewYinJiJiChengWGData.Instance:GetEquipYinJiRemindByPart(data.index)
    self.node_list.img_remind:SetActive(is_remind == 1)

    local is_enough_limit, limit_log_type, limit_cfg = NewYinJiJiChengWGData.Instance:GetEquipIsEnoughLimitCondition(data)
    if not is_enough_limit and limit_cfg and limit_log_type ~= 0 then
        local str = ""
        if limit_log_type == 1 then
            str = string.format(Language.NewEquipYinJi.EquipLimitLog[limit_log_type], limit_cfg.role_level_limit)
        elseif limit_log_type == 2 or limit_log_type == 4 or limit_log_type == 3 then
            if limit_cfg.star > 0 then
                str = string.format(Language.NewEquipYinJi.EquipLimitLog[2], limit_cfg.order, limit_cfg.star, Language.Common.ItemQualityColor[limit_cfg.color])
            else
                str = string.format(Language.NewEquipYinJi.EquipLimitLog[4], limit_cfg.order, Language.Common.ItemQualityColor[limit_cfg.color])
            end
        end
        self.node_list.limit_label.text.text = str
        self.node_list.progress_label.text.text = ""
        self.node_list.yinji_label.text.text = ""
    else
        self.node_list.limit_label.text.text = ""
        local jindu_str,qualit_str_list = NewYinJiJiChengWGData.Instance:GetKeYinFuNumStrByEquipPart(data.index)
        self.node_list.progress_label.text.text = jindu_str
        local str = table.concat(qualit_str_list, "")
        self.node_list.yinji_label.text.text = str
    end
end

function EquipmentMarkYinJiRender:OnSelectChange(is_select)
    --self.node_list.img9_item_bg:SetActive(not is_select)
    self.node_list.img9_item_bg_hl:SetActive(is_select)
end

--------------------------------------------------------KeYinFuCell(中间的刻印符)------------------------------------------------------

KeYinFuCell = KeYinFuCell or BaseClass(BaseRender)

function KeYinFuCell:__init()

end

function KeYinFuCell:__delete()
    self.parent_view = nil
    self.m_pos = nil
    -- if self.select_hl_root_arrow_tween then
	-- 	self.select_hl_root_arrow_tween:Kill()
	-- 	self.select_hl_root_arrow_tween = nil
	-- end
end

function KeYinFuCell:SetPos(pos_x, pos_y)
    self.m_pos = {x = pos_x, y = pos_y}
    RectTransform.SetAnchoredPositionXY(self.view.rect, pos_x, pos_y)
end

function KeYinFuCell:GetPos()
    return self.m_pos
end

function KeYinFuCell:GetV2Pos()
    return RectTransform.GetAnchoredPosition(self.view.rect)
end

function KeYinFuCell:GetSizeDelta()
    return RectTransform.GetSizeDelta(self.view.rect)
end

-- 设置显隐
function KeYinFuCell:SetCellVisable(enabled)
    if self.view then
        self.view:SetActive(enabled)
    end
end

function KeYinFuCell:CreateLine(data_list)
    if not self.index or not data_list then
        return
    end

    local line_render_list = {}
    self.node_list.line:SetActive(true)

    for i,v in ipairs(data_list) do
        if v ~= self.index then
            local start_pos = self:GetV2Pos()
            local end_pos = self.parent_view:GetKeYinFuPosByIndex(v, true)
            if not end_pos then
                break
            end

            local obj = ResMgr:Instantiate(self.node_list.line.gameObject)
            obj.transform:SetParent(self.view.transform, false)

            local line_render = NewKeYinLineRender.New(obj)
            line_render:SetPosAndScale(start_pos, end_pos)
            line_render:SetLineStarIndexAndEndIndex(self.index, v)
            line_render_list[#line_render_list + 1] = line_render
        end
    end

    self.node_list.line:SetActive(false)

    return self.index, line_render_list
end

function KeYinFuCell:ShowOriginalColor()
    -- local bundle2, asset2 = ResPath.GetEquipmentIcon("yjcolor_0")
    -- self.node_list.other_bg.image:LoadSprite(bundle2, asset2, function ()
    --     self.node_list.other_bg.image:SetNativeSize()
    -- end)
end

function KeYinFuCell:OnFlush()
	if not self.data then
        self:ShowOriginalColor()
		return
    end

    local yinji_info = NewYinJiJiChengWGData.Instance:GetYinJiInfoByEquipPart(self.data.equip_part)
    local keyinfu_info = yinji_info.kong_wei_info_sc[self.data.kongwei_id]
    self.data = keyinfu_info

    -- if not self.select_hl_root_arrow_tween then
    --     self.select_hl_root_arrow_tween = self.node_list["select_hl_root"].transform:DOScale(Vector3(1.2, 1.2, 1.2), 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo):SetEase(DG.Tweening.Ease.Linear)
    -- end

    local equip_data = EquipWGData.Instance:GetGridData(self.data.equip_part)

    if not equip_data then
        self:ShowOriginalColor()
        return
    end

    local is_enough_condition, limit_log_type, limit_cfg = NewYinJiJiChengWGData.Instance:GetYinJiCellIsEnoughLimitCondition(equip_data, self.data.kongwei_id)
    --不满足条件
    if not is_enough_condition and limit_log_type ~= 0 and limit_cfg ~= nil then
        local limit1 = ""
        local limit2 = ""
        local limit3 = ""

        if limit_cfg.order > 0 then
            limit1 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc2, limit_cfg.order)
        end

        if limit_cfg.color > 0 then
            limit3 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc3, Language.Common.ItemQualityColor[limit_cfg.color])
        end

        if limit_cfg.star > 0 then
            limit2 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc4, limit_cfg.star)
        end

        self.node_list.limit_desc.text.text = string.format(Language.NewEquipYinJi.KongWeiLimitDesc1, limit1, limit2, limit3)
        self.node_list.img_can_add:SetActive(false)
        self.node_list.remind:SetActive(false)
        self.node_list.img_cant_add:SetActive(true)
        --self.node_list.mask_bg:SetActive(false)
        --self.node_list.lock_img:SetActive(true)
        --self.node_list["limite_bg"]:SetActive(true)
    else
        self.node_list.limit_desc.text.text = ""
        self.node_list.img_can_add:SetActive(true)
        self.node_list.img_cant_add:SetActive(false)
        --self.node_list["limite_bg"]:SetActive(false)
        --self.node_list.mask_bg:SetActive(false)
        --self.node_list.lock_img:SetActive(false)
    end

    -- 未镶嵌
    if self.data.item_id <= 0 then
        self.node_list.active_hl:SetActive(false)
        self.node_list.effect_root:SetActive(false)
        self.node_list.item_icon:SetActive(false)

        local is_can_can_input = NewYinJiJiChengWGData.Instance:GetCurKeYinFuIsCanInPut(self.data.equip_part, self.data.kongwei_id)

        self.node_list.remind:SetActive(is_can_can_input == 1)
        self:ShowOriginalColor()
        return
    end

    local is_can_tihuan = NewYinJiJiChengWGData.Instance:GetCurKeYinFuIsCanTiHuan(self.data.equip_part, self.data.kongwei_id)
    local is_can_compose = NewYinJiJiChengWGData.Instance:GetCurKeYinFuIsCanCompse(self.data.equip_part, self.data.kongwei_id)

    self.node_list.remind:SetActive(is_can_tihuan == 1 or is_can_compose == 1)

    self.node_list.active_hl:SetActive(is_enough_condition)
    self.node_list.effect_root:SetActive(is_enough_condition)
    self.node_list.item_icon:SetActive(true)

    --已镶嵌
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(self.data.item_id)

    if not item_cfg or not keyinfu_cfg then
        self:ShowOriginalColor()
        return
    end

    --暂时屏蔽刻孔特效  有合适特效再替换
    -- local bundle, asset = ResPath.GetUIEffect(KEYINKONG_EFFECT[keyinfu_cfg.keyinfu_type])
    -- self.node_list.effect_root:ChangeAsset(bundle, asset)

    --local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
    local bundle, asset = ResPath.GetEquipmentMarkImages(keyinfu_cfg.show_id)
    self.node_list.item_icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.item_icon.image:SetNativeSize()
    end)
    -- local bundle2, asset2 = ResPath.GetEquipmentIcon("yjcolor_"..item_cfg.color)
    -- self.node_list.other_bg.image:LoadSprite(bundle2, asset2, function ()
    --     self.node_list.other_bg.image:SetNativeSize()
    -- end)

end

function KeYinFuCell:SetSelectHLEnable(enable)
    self.node_list.select_hl:SetActive(enable)
end

function KeYinFuCell:GetKeYinFuEffectRootAndKeYinFuType()
    if self.data then
        if self.data.item_id and self.data.item_id > 0 then
            local keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(self.data.item_id)
            local equip_data = EquipWGData.Instance:GetGridData(self.data.equip_part)

            if equip_data then
                local is_enough_condition, limit_log_type, limit_cfg = NewYinJiJiChengWGData.Instance:GetYinJiCellIsEnoughLimitCondition(equip_data, self.data.kongwei_id)
                --不满足条件
                if is_enough_condition then
                    return self.node_list.effect_root,  keyinfu_cfg.keyinfu_type
                end
            end
        end
    end
end

function KeYinFuCell:GetKeYinFuPos()
    if self.node_list.effect_root then
        return self.view.rect.anchoredPosition
    end
end

--------------------------------------------------------NewYinJiAttrRender------------------------------------------------------

NewYinJiAttrRender = NewYinJiAttrRender or BaseClass(BaseRender)

function NewYinJiAttrRender:__delete()
    self.attr_desc_obj_list = nil
    self.unlock_cb = nil
    self.yj_select_data = nil
end

function NewYinJiAttrRender:LoadCallBack()
    local node_list = self.node_list
    local temp_list = {}
    for i=1,6 do
        temp_list[i] = {name = node_list["attr_name_" .. i], value = node_list["attr_value_" .. i]}
    end
    self.attr_desc_obj_list = temp_list

    XUI.AddClickEventListener(self.node_list.unlock_btn, BindTool.Bind1(self.OnClickUnLockBtn, self))
end

function NewYinJiAttrRender:OnFlush()
    local bundle, asset = ResPath.GetEquipmentMarkImages("a3_qmdj_icon" .. self.index)
    self.node_list.yinji_icon.image:LoadSprite(bundle, asset)

    local data = self:GetData()
    if IsEmptyTable(data) then
        self:SetVisible(false)
        return
    end
    self:SetVisible(true)

    self:FlushAttrTitle()
    self:FlushAttrDesc()
    self:FlushYJProgress()
end

function NewYinJiAttrRender:FlushAttrTitle()
    local data = self:GetData()
    local cur_select_equip_part = NewYinJiJiChengWGData.Instance:GetCurSelectEquipPart()
    local equip_data = EquipWGData.Instance:GetGridData(cur_select_equip_part)
    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
    if not item_cfg then
        return
    end

    local equip_name = string.sub(item_cfg.name, -6)
    local max_kongwei = NewYinJiJiChengWGData.Instance:GetCurEquipMaxKongWeiID(equip_data)

    local keyinfu_quality_list = NewYinJiJiChengWGData.Instance:GetYinJiQualitNumList(cur_select_equip_part) or {}
    local keyinfu_num = 0
    for k,v in pairs(keyinfu_quality_list) do
        if data.yinji_type <= k then
            keyinfu_num = keyinfu_num + v
        end
    end

    local color = keyinfu_num >= max_kongwei and COLOR3B.GREEN or COLOR3B.PINK

    --local title_str = Language.NewEquipYinJi.YinJiAttrDesc1[data.yinji_type]
    -- self.node_list.yinji_attr_title.text.text = string.format(title_str, equip_name, color, keyinfu_num, max_kongwei)

    local title_str = Language.NewEquipYinJi.YinJiAttrDesc11[data.yinji_type]
    self.node_list.yinji_attr_title.text.text = title_str
    self.node_list.yinji_progress.text.text = string.format(Language.NewEquipYinJi.YinJiAttrProgress, "#F5EE9D", keyinfu_num, max_kongwei)
end

function NewYinJiAttrRender:FlushAttrDesc()
    local data = self:GetData()
    local cur_select_equip_part = NewYinJiJiChengWGData.Instance:GetCurSelectEquipPart()
    local yinji_info = NewYinJiJiChengWGData.Instance:GetYinJiInfoByEquipPart(cur_select_equip_part)
    if not yinji_info then
        return
    end

    -- 属性描述显示
    local attr_name = Language.Common.TipsAttrNameList
    local attr_list = AttributeMgr.GetAttributteByClass(data)
    local sort_list = AttributeMgr.SortAttribute()
    local desc_list = {}
    for i,v in ipairs(sort_list) do
        if attr_list[v] > 0 then
            local temp = {name = attr_name[v], value = attr_list[v]}
            if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
                temp.value = temp.value / 100 .. "%"
            end
            desc_list[#desc_list + 1] = temp
        end
    end
    local is_active = yinji_info.has_act_yinji_type == data.yinji_type
    local color = is_active and COLOR3B.GREEN or COLOR3B.BLUE_TITLE
    local obj_list = self.attr_desc_obj_list
    for i=1,#obj_list do
        if desc_list[i] then
            obj_list[i].name.text.text = desc_list[i].name
            obj_list[i].value.text.text = ToColorStr(desc_list[i].value, color)
        end
        obj_list[i].name:SetActive(desc_list[i] ~= nil)
    end
    -- -- 激活 未激活 已替换
    -- local color = yinji_info.has_act_yinji_type == data.yinji_type and COLOR3B.GREEN or COLOR3B.RED
    -- local temp_str = ""
    -- if yinji_info.has_act_yinji_type == data.yinji_type then
    --     temp_str = ToColorStr(Language.NewEquipYinJi.YinJiAttrDesc3[3], color)
    -- elseif yinji_info.has_act_yinji_type > data.yinji_type then
    --     temp_str = ToColorStr(Language.NewEquipYinJi.YinJiAttrDesc3[2], color)
    -- else
    --     temp_str = ToColorStr(Language.NewEquipYinJi.YinJiAttrDesc3[1], color)
    -- end
end

function NewYinJiAttrRender:SetSelectData(yj_select_data)
    self.yj_select_data = yj_select_data
end

function NewYinJiAttrRender:FlushYJProgress()
    if self.yj_select_data then
        local data = self:GetData()
        local cur_select_equip_part = NewYinJiJiChengWGData.Instance:GetCurSelectEquipPart()
        local is_can_active, yinji_type, index = NewYinJiJiChengWGData.Instance:GetCurPartIsCanActiveYinJiAttr(self.yj_select_data)
        local yinji_info = NewYinJiJiChengWGData.Instance:GetYinJiInfoByEquipPart(cur_select_equip_part)
        local is_active = yinji_info.has_act_yinji_type == data.yinji_type
        local is_show = is_can_active and self.index == index and not is_active

        self.node_list.yinji_progress:SetActive(not is_show)
        self.node_list.unlock_btn:SetActive(is_show)
        self.node_list.img_active:SetActive(is_active)
    end
end

function NewYinJiAttrRender:OnClickUnLockBtn()
    if self.unlock_cb then
        self.unlock_cb()
    end
end

function NewYinJiAttrRender:SetUnLockCallBack(cb)
    self.unlock_cb = cb
end

-----------------------------------------------------------------NewKeYinFuBagRender-------------------------------------

NewKeYinFuBagRender = NewKeYinFuBagRender or BaseClass(BaseRender)

function NewKeYinFuBagRender:__init()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_cell)
    end

    self.node_list.onclick.button:AddClickListener(BindTool.Bind(self.OnClick, self))

    if not self.alert then
        self.alert = Alert.New()
		self.alert:SetShowCheckBox(false)
    end
end

function NewKeYinFuBagRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function NewKeYinFuBagRender:OnFlush()
    if not self.data then
        return
    end

    local cur_select_equip_part = NewYinJiJiChengWGData.Instance:GetCurSelectEquipPart()
    local equip_data = EquipWGData.Instance:GetGridData(cur_select_equip_part)
    local limit_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuPutLimitCfgByEquipPart(equip_data.index, self.data.keyinfu_type)
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(self.data.item_id)

    if not item_cfg or not keyinfu_cfg then
        return
    end

    self.item_cell:SetData({item_id = self.data.item_id, num = self.data.num})

    self.node_list.item_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])

    local is_enough_limit, limit_cfg = NewYinJiJiChengWGData.Instance:GetEquipIsEnoughKeYinFuLimit(equip_data, self.data.keyinfu_type)

    if not is_enough_limit and limit_cfg then
        local limit1 = ""
        local limit2 = ""
        local limit3 = ""

        if limit_cfg.order > 0 then
            limit1 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc2, limit_cfg.order)
        end

        if limit_cfg.color > 0 then
            limit3 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc3, Language.Common.ItemQualityColor[limit_cfg.color])
        end

        if limit_cfg.star > 0 then
            limit2 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc4, limit_cfg.star)
        end

        self.node_list.limit_desc.text.text = string.format(Language.NewEquipYinJi.KongWeiLimitDesc1, limit1, limit2, limit3)
        self.node_list.limit_desc:SetActive(false)
    else
        self.node_list.limit_desc:SetActive(false)
    end

    local attr_name_list = Language.Common.AttrNameList2
	local attr_list = AttributeMgr.GetAttributteByClass(keyinfu_cfg)
    local sort_list = AttributeMgr.SortAttribute()

    local attr_txt = ""
	local index = 0
    for i,v in ipairs(sort_list) do
        if attr_list[v] ~= 0 then
            index = index + 1
            local attr_name = string.format("%s%s", attr_txt, attr_name_list[v] )--.. "+"

            local value = attr_list[v]
            if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
                value = value / 100 .. "%"
            end
            attr_txt = string.format("%s\t%s\n", attr_name, ToColorStr(value, COLOR3B.DEFAULT_NUM))
        end
    end

    attr_txt = string.sub(attr_txt, 1, -2)
    self.node_list.attr_desc.text.text = attr_txt
end

function NewKeYinFuBagRender:OnClick()
    if not self.data then
        return
    end

    local cur_select_kongwei = NewYinJiJiChengWGData.Instance:GetCurSelectKongWei()
    local cur_select_equip_part = NewYinJiJiChengWGData.Instance:GetCurSelectEquipPart()
    local equip_data = EquipWGData.Instance:GetGridData(cur_select_equip_part)
    local yinji_info = NewYinJiJiChengWGData.Instance:GetYinJiInfoByEquipPart(cur_select_equip_part)


    if not yinji_info then
        return
    end

    local kongwei_info = NewYinJiJiChengWGData.Instance:GetKongWeoInfo(cur_select_equip_part, cur_select_kongwei)
    local keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(self.data.item_id)

    local is_enough_condition, limit_log_type, limit_cfg = NewYinJiJiChengWGData.Instance:GetYinJiCellIsEnoughLimitCondition(equip_data, cur_select_kongwei)

    --不满足条件
    if not is_enough_condition and limit_log_type ~= 0 and limit_cfg ~= nil then
        local limit1 = ""
        local limit2 = ""
        local limit3 = ""

        if limit_cfg.order > 0 then
            limit1 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc2, limit_cfg.order)
        end

        if limit_cfg.color > 0 then
            limit3 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc3, Language.Common.ItemQualityColor[limit_cfg.color])
        end

        if limit_cfg.star > 0 then
            limit2 = string.format(Language.NewEquipYinJi.KongWeiLimitDesc4, limit_cfg.star)
        end

        TipWGCtrl:ShowSystemMsg(string.format(Language.NewEquipYinJi.KongWeiLimitDesc1, limit1, limit2, limit3))
        return
    end

    if kongwei_info and kongwei_info.item_id > 0 then
        --孔位刻印符配置
        local target_keyinfu_cfg = NewYinJiJiChengWGData.Instance:GetKeYinFuByItemID(kongwei_info.item_id)

        --当前刻印符印记类型小于已激活印记类型
        if  keyinfu_cfg.keyinfu_type < yinji_info.has_act_yinji_type then
            if self.alert then
                self.alert:SetLableString(Language.NewEquipYinJi.YinJiAlertDesc2)
                self.alert:SetOkFunc(function ()
                    EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_PUT, cur_select_equip_part, cur_select_kongwei, self.data.item_id)
                end)
                self.alert:Open()
            end
        elseif target_keyinfu_cfg and target_keyinfu_cfg.keyinfu_type >= KEYINFU_MAX_TYPE then
            -- 目标孔位已刻印最高级刻印符
            if self.alert then
                self.alert:SetLableString(Language.NewEquipYinJi.YinJiAlertDesc)
                self.alert:SetOkFunc(function ()
                    EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_PUT, cur_select_equip_part, cur_select_kongwei, self.data.item_id)
                end)
                self.alert:Open()
            end
        else
            EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_PUT, cur_select_equip_part, cur_select_kongwei, self.data.item_id)
        end
    else
        EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_PUT, cur_select_equip_part, cur_select_kongwei, self.data.item_id)
    end
end

------------------------------------------NewKeYinLineRender---------------------------------------------------------------
NewKeYinLineRender = NewKeYinLineRender or BaseClass(BaseRender)

function NewKeYinLineRender:SetPosAndScale(start_pos, end_pos)
    local obj = self.view
    local dis = Vector2.Distance(start_pos, end_pos)
    local size_delta = RectTransform.GetSizeDelta(obj.transform)
    RectTransform.SetSizeDeltaXY(obj.transform, dis, size_delta.y)

    local forward_dir = Vector2(0,1)
    end_pos:Sub(start_pos)
    local angle = Vector2.Angle(forward_dir, end_pos)
    angle = angle - 90
    angle = end_pos.x < 0 and angle or 180 - angle
    obj.transform.rotation = Quaternion.Euler(0, 0, angle)
    local line_hl = obj.transform:GetChild(0)
    line_hl.transform.rotation = Quaternion.Euler(0, 0, angle)
    Transform.SetLocalScaleXYZ(line_hl.transform, dis / 174, 1, 1)
end

--策划要求不区分线的状态了，变成装饰线
function NewKeYinLineRender:SetHighLight(enabled)
    -- if self.node_list and self.node_list.yinji_line_hl_1 then
    --     self.node_list.yinji_line_hl_1:SetActive(enabled)
    -- end
end

function NewKeYinLineRender:SetLineVisable(enabled)
    if self.view then
        self.view:SetActive(enabled)
    end
end

function NewKeYinLineRender:SetLineParent(parent)
    if not parent then
        return
    end

    self.view.transform.parent = parent
    self.view.transform:SetSiblingIndex(1)
end


function NewKeYinLineRender:SetLineStarIndexAndEndIndex(start_index, end_index)
    self.start_index = start_index
    self.end_index = end_index
end


function NewKeYinLineRender:GetIsLineStarIndexAndEndIndex(start_index, end_index)
    if self.start_index == start_index and  self.end_index == end_index then
        return true
    end

    return false
end
