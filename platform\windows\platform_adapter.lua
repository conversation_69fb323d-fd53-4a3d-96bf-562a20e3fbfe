require("platform/platform_adapter")

function PlatformAdapter:GetAssetsInfo()
	return {
		version = 58,
		file_list = { path = "list.zip", size=27812 }
	}
end

function PlatformAdapter:GetLocalConfig()
	local pkg_info = self:GetPackageInfo()
	return {
		--init_url = pkg_info.config.init_url
		init_url = "http://*************:8038/init-query.php"   -- 连外网请改这里
	}
end

function PlatformAdapter:SaveLocalConfig(local_cfg)
end

function PlatformAdapter:GetNetState()
	return 2
end

function PlatformAdapter.GetListZipPath()
	return "../../version/list.zip"
end
