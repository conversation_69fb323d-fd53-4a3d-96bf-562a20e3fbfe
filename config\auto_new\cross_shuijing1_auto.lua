return {
    other={
        {
            relive_time=15,
            gather_times=30,
            kill_add_score=0,
            assist_add_score=0,
            buy_buff_cost=20,
            add_gongji_per=5,
            add_fangyu_per=5,
            add_maxhp_per=5,
            ct_res_id=101,
            tip_res_id=101,
            content="1>战场开启时间：每天18:00-18:30；\r\n\r\n2>活动期间进入战场获取指定战场宝物，可获取相应奖励；\r\n\r\n3>战场BOSS不定时刷新，完成击杀获得额外奖励；\r\n\r\n4>背靠战友，预防偷袭，击杀玩家有机会抢夺战场宝物；"
        }
    },
    layer={
        {
            layer=0,
            scene_id=750,
            need_score=0,
            driect_enter_cost_gold=0
        },
        {
            layer=1,
            scene_id=751,
            need_score=150,
            driect_enter_cost_gold=100
        }
    },
    realive_point_list={
        {},
        {}
    },
    gather={
        {
            gather_type=0,
            gather_id=184,
            gather_time=5,
            flush_time=30,
            bind_gold=0,
            shengwang=0,
            stone_id=26200,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        },
        {
            gather_type=1,
            gather_id=185,
            gather_time=10,
            flush_time=60,
            bind_gold=0,
            shengwang=0,
            stone_id=26201,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        },
        {
            gather_type=2,
            gather_id=186,
            gather_time=15,
            flush_time=120,
            bind_gold=0,
            shengwang=0,
            stone_id=26202,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        },
        {
            gather_type=3,
            gather_id=187,
            gather_time=5,
            flush_time=30,
            bind_gold=0,
            shengwang=500,
            stone_id=26203,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        },
        {
            gather_type=4,
            gather_id=188,
            gather_time=10,
            flush_time=60,
            bind_gold=0,
            shengwang=1000,
            stone_id=26204,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        },
        {
            gather_type=5,
            gather_id=189,
            gather_time=15,
            flush_time=150,
            bind_gold=0,
            shengwang=1500,
            stone_id=26205,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        },
        {
            gather_type=6,
            gather_id=190,
            gather_time=5,
            flush_time=30,
            bind_gold=15,
            shengwang=0,
            stone_id=26246,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        },
        {
            gather_type=7,
            gather_id=191,
            gather_time=10,
            flush_time=60,
            bind_gold=30,
            shengwang=0,
            stone_id=26247,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        },
        {
            gather_type=8,
            gather_id=192,
            gather_time=15,
            flush_time=180,
            bind_gold=45,
            shengwang=0,
            stone_id=26248,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        },
        {
            gather_type=9,
            gather_id=193,
            gather_time=25,
            flush_time=300,
            bind_gold=60,
            shengwang=2000,
            stone_id=26249,
            stone_num=1,
            stone_is_bind=1,
            add_score=100
        }
    },
    flush_point={
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {}
    },
    score_reward={
        {},
        {},
        {},
        {}
    }
}