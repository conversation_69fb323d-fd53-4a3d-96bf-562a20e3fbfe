﻿using UnityEngine;

public class <PERSON><PERSON><PERSON><PERSON><PERSON>elper
{
	public static void <PERSON><PERSON><PERSON>(string key)
	{
		PlayerPrefs.<PERSON><PERSON>ey(key);
	}
	
	public static void Delete<PERSON>ey(string key)
	{
		PlayerPrefs.DeleteKey(key);
	}
	
	public static bool GetBool(string key)
	{
		return PlayerPrefs.GetInt(key) != 0;
	}
	
	public static bool GetBool(string key, bool defaultValue)
	{
		return PlayerPrefs.GetInt(key, defaultValue ? 1 : 0) != 0;
	}

	public static void SetBool(string key, bool value)
	{
		PlayerPrefs.SetInt(key, value ? 1 : 0);
	}
	
	
	public static int GetInt(string key)
	{
		return PlayerPrefs.GetInt(key);
	}
	
	public static int GetInt(string key, int defaultValue)
	{
		return PlayerPrefs.GetInt(key, defaultValue);
	}

	public static void SetInt(string key, int value)
	{
		PlayerPrefs.SetInt(key, value);
	}
	
	
	public static float GetFloat(string key)
	{
		return PlayerPrefs.GetFloat(key);
	}
	
	public static float GetFloat(string key, float defaultValue)
	{
		return PlayerPrefs.GetFloat(key, defaultValue);
	}

	public static void SetFloat(string key, float value)
	{
		PlayerPrefs.SetFloat(key, value);
	}
	
	
	public static string GetString(string key)
	{
		return PlayerPrefs.GetString(key);
	}
	
	public static string GetString(string key, string defaultValue)
	{
		return PlayerPrefs.GetString(key, defaultValue);
	}

	public static void SetString(string key, string value)
	{
		PlayerPrefs.SetString(key, value);
	}
}

	
