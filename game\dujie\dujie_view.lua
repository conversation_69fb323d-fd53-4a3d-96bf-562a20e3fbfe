---------------------------------- 渡劫----------------------------------------------

local THUNDER_WARNING_TIME = 1.2

function DujieView:LoadDujieCallBack()
    self.skill_cd_timer_list = {}
    self.show_skill_list = {}
    for i = 1, 3 do
        self.show_skill_list[i] = SkillShowSkillRender.New(self.node_list["skill_icon_"..i])
        self.show_skill_list[i]:SetNeedChangeSkillBtnPos(false)
        self.show_skill_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self, i))

        self.node_list["text_skill_name_"..i].tmp.text = Language.Dujie.SkillName[i]
    end

    self.fail_cost_item_cell = ItemCell.New(self.node_list.fail_cost_node)

    if not self.dujie_attr_list then
		self.dujie_attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.dujie_attr_list)
	end

    XUI.AddClickEventListener(self.node_list.toggle_fail_all_use, BindTool.Bind(self.OnClickBtnUseAll, self))
    XUI.AddClickEventListener(self.node_list.btn_fail_use, BindTool.Bind(self.OnClickBtnFailUse, self))

    XUI.AddClickEventListener(self.node_list.btn_reduce_tips, BindTool.Bind(self.OnClickBtnReduceTips, self))
    XUI.AddClickEventListener(self.node_list.btn_start_dujie, BindTool.Bind(self.OnClickBtnStartDujie, self))
    XUI.AddClickEventListener(self.node_list.btn_goto_spirit_fb, BindTool.Bind(self.OnClickBtnSpiritFb, self))
    FunOpen.Instance:RegisterFunUi(FunName.ConquestWarKFKZ, self.node_list["conquest_war_kfkz"])  


    TMPUtil.AddClickEventListener(self.node_list.text_limit_1.text, BindTool.Bind(self.OpenZhuanzhi, self), 1)
    TMPUtil.AddClickEventListener(self.node_list.text_limit_2.text, BindTool.Bind(self.OpenXiuwei, self), 1)
end

-- 打开转生
function DujieView:OpenZhuanzhi()
    ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
end
-- 打开修为
function DujieView:OpenXiuwei()
    ViewManager.Instance:Open(GuideModuleName.XiuWeiView)
end
-- 释放
function DujieView:ReleaseDujieView()
	if self.dujie_attr_list then
		self.dujie_attr_list:DeleteMe()
		self.dujie_attr_list = nil
	end
    -- 取消红点监听
    -- RemindManager.Instance:UnBind(self.remind_callback)

    if self.harm_tweener then
		self.harm_tweener:Kill()
		self.harm_tweener = nil
	end

    if self.view_shake_tween then
		self.view_shake_tween:Kill()
		self.view_shake_tween = nil
	end

    if self.fail_cost_item_cell then
        self.fail_cost_item_cell:DeleteMe()
        self.fail_cost_item_cell = nil
    end
end




function DujieView:StartDujie()

    -- 渡劫开始
    self:ChangeStage(DUJIE_VIEW_STATE.DUJIE_ING)
    self.node_list.img_start_time:SetActive(true)
    self.node_list.img_start_time.rect.anchoredPosition = Vector2(6, -300)
    self.node_list.img_start_time.canvas_group:DoAlpha(0,1,0.5)
    self.node_list.img_start_time.rect:DOLocalMoveY(118, 0.5)
    self.node_list.text_start_time.tmp.text = 5
    self:CleanDujieStarTimer()
    self.dujie_star_timer = CountDown.Instance:AddCountDown(5, 1,
        -- 回调方法
        function(elapse_time, total_time)
            self.node_list.text_start_time.tmp.text = math.ceil(total_time - elapse_time)
        end,
        -- 倒计时完成回调方法
        function()
            self.node_list.img_start_time:SetActive(false)
            self.node_list.thunder_group:SetActive(true)
            self:FlushDujieingView()
        end
    )
    self.node_list.level_group:SetActive(false)
    self.node_list.btn_start_dujie:SetActive(false)
    self.node_list.top_group:SetActive(false)
end

function DujieView:FlushSkill()
    local skill_open_level_1 = DujieWGData.Instance:GetOtherCfg("skill_open_level_1")
    local skill_open_level_2 = DujieWGData.Instance:GetOtherCfg("skill_open_level_2")
    local skill_open_level_3 = DujieWGData.Instance:GetOtherCfg("skill_open_level_3")

    local level = DujieWGData.Instance:GetDujieLevel()

    self.node_list.skill_group:SetActive(level >= skill_open_level_1 or level >= skill_open_level_2  or level >= skill_open_level_3 )

    self.node_list.img_lock_mask_1:SetActive(level < skill_open_level_1)
    self.node_list.img_lock_mask_2:SetActive(level < skill_open_level_2)
    self.node_list.img_lock_mask_3:SetActive(level < skill_open_level_3)
end

-- 刷新未开始渡劫界面
function DujieView:FlushDujieView()
        
    -- TweenManager.Instance:SetIsOpenTween(true)

    self:CleanHpAnimTimer()
    local zhuanzhi_level = RoleWGData.Instance:GetZhuanZhiNumber()
    local cultivation_level = CultivationWGData.Instance:GetXiuWeiState()

    local god_and_demons_type = TransFerWGData.Instance:GetGodAndDemonsType()

    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()
    -- 等级
    local level = base_info and base_info.level or 0
    local level_cfg = DujieWGData.Instance:GetLevelCfg(level)
    local next_level_cfg = DujieWGData.Instance:GetLevelCfg(level + 1)
    if next_level_cfg then
        self.node_list.level_max:SetActive(false)

        local is_can_dujie,fail_type = DujieWGData.Instance:IsCanDujie()
        -- 等级限制
        if fail_type and (fail_type == DUJIE_NO_CAN_TYPE.TRANSFER or fail_type == DUJIE_NO_CAN_TYPE.CULTIVATION) then
            self.node_list.level_limit:SetActive(true)
            self:FlushLevelLimit(next_level_cfg.zhuanzhi_limit, next_level_cfg.jingjie_limit)
        else
            self.node_list.level_limit:SetActive(false)

        end

        if fail_type and fail_type == DUJIE_NO_CAN_TYPE.FAIL then
            local cur_time = TimeWGCtrl.Instance:GetServerTime()
            self.node_list.fail_limit:SetActive(true)
            self.node_list.fail_limit2:SetActive(true)
            self:FlushFail(base_info.ordeal_damage_time - cur_time)
        else
            self.node_list.fail_limit:SetActive(false)
            self.node_list.fail_limit2:SetActive(false)

        end

        -- 提示信息
        self.node_list.img_limit:SetActive(not is_can_dujie)
        self.node_list.btn_start_dujie:SetActive(is_can_dujie)

    else
        self.node_list.img_limit:SetActive(true)
        self.node_list.level_max:SetActive(true)
        self.node_list.btn_start_dujie:SetActive(false)
        self.node_list.fail_limit:SetActive(false)
        self.node_list.fail_limit2:SetActive(false)
        self.node_list.level_limit:SetActive(false)


    end

    local type_max = DujieWGData.Instance:GetTypeMax(level_cfg.type, level_cfg.type_seq)
    local index = DujieWGData.Instance:GetLevelIndex(level)
    local type_str = DujieWGData.Instance:GetTypeStr(level_cfg.type)
    local type_seq_str = NumberToChinaNumber(level_cfg.type_seq == 0 and 1 or level_cfg.type_seq)
    self.node_list.text_dujie_level.tmp.text = string.format(Language.Dujie.SkyLevleStr2, type_str, type_seq_str, index, type_max)
    -- self.node_list.level_group:SetActive(true)
    -- self.node_list.top_group:SetActive(true)
    -- 转职
    local vitality_cfg = DujieWGData.Instance:GetVitalityCfg(zhuanzhi_level)
    if vitality_cfg then
        self.node_list.text_vitality.tmp.text = string.format("%s/%s",vitality_cfg.vitality, vitality_cfg.vitality)
    else
        self.node_list.text_vitality.tmp.text = "-/-"
    end
    
    -- 
    local strength_cfg = DujieWGData.Instance:GetStrengthCfg(cultivation_level)
    if strength_cfg then
        self.node_list.text_strength.tmp.text = string.format("%s/%s",strength_cfg.strength, strength_cfg.strength)
    else
        self.node_list.text_strength.tmp.text = "-/-"
    end

    self.node_list.img_vitality_slider_down.slider.value = 1
    self.node_list.img_strength_slider_down.slider.value = 1
    self.node_list.img_vitality_slider.slider.value = 1
    self.node_list.img_strength_slider.slider.value = 1
    self.node_list.img_start_time:SetActive(false)


    -- 技能
    for i = 1, 3 do
        local dujie_skill_id = DujieWGData.Instance:GetOtherCfg("dujie_skill_"..i)
        self.show_skill_list[i]:SetData({skill_id = dujie_skill_id, skill_level = 1,skill_name = Language.Dujie.SkillName[i]})
        -- self.node_list["img_cd_mask_"..i]:SetActive(false)
        self.show_skill_list[i]:SetSkillBtnCD()
    end

    self:FlushReduce()
    
    self:FlushDujieAttr()
    self:FlushDujieBar()
    self:FlushSkill()
end

-- 渡劫保护
function DujieView:FlushReduce()
    
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()
    -- 渡劫保护
    if base_info.fail_reduce_time > 0 then
        self.node_list.img_reduce:SetActive(true)

        local server_time = TimeWGCtrl.Instance:GetServerTime()


        self.node_list.text_reduce.tmp.text = TimeUtil.FormatSecondDHM9(base_info.fail_reduce_time - server_time)

        self:CleanFailReduceTimer()
        self.fail_reduce_timer = CountDown.Instance:AddCountDown(base_info.fail_reduce_time - server_time, 1,
            -- 回调方法
            function(elapse_time, total_time)
                self.node_list.text_reduce.tmp.text = TimeUtil.FormatSecondDHM9(total_time - elapse_time)
            end,
            -- 倒计时完成回调方法
            function()
                self.node_list.img_reduce:SetActive(false)
            end
        )
    else
        self.node_list.img_reduce:SetActive(false)
    end
end

-- 刷新等级限制显示
function DujieView:FlushLevelLimit(zhuanzhi_limit, cultivation_limit)

    -- 获取等级
    local zhuanzhi_level = RoleWGData.Instance:GetZhuanZhiNumber()
    local cultivation_level = CultivationWGData.Instance:GetXiuWeiState()
    -- 获取配置
    local zhuanzhi_level_name = TransFerWGData.Instance:GetRoleProfLevelName(zhuanzhi_limit)
    local zhuanzhi_stage_count = TransFerWGData.Instance:GetStageNumByLevel(zhuanzhi_limit)
    local stage_cfg  = CultivationWGData.Instance:GetXiuWeiStageCfgByState(cultivation_limit)

    -- 拼接文本

    local limit_str_1 = string.format(Language.Dujie.TransferLimitStr, zhuanzhi_level_name, zhuanzhi_stage_count - 1)
    local limit_str_2 = string.format(Language.Dujie.CultivationLimitStr, stage_cfg.stage_title)
   
    
    local is_add_link = false
    -- 添加后缀
    if zhuanzhi_limit == 0 then
        is_add_link = TransFerWGData.Instance:GetGodAndDemonsType() == -1
        limit_str_1 = string.format("%s%s %s%s", Language.Dujie.TransferLimitStr2, is_add_link and "<link=1>" or "", 
            (is_add_link and Language.Dujie.LimitStr1 or Language.Dujie.LimitStr2), is_add_link and "</link>" or "")
    else
        local is_finish,cur_num, max_num = TransFerWGData.Instance:IsAllFinish()
        is_add_link = (zhuanzhi_level + 1  < zhuanzhi_limit) or  (zhuanzhi_level + 1  == zhuanzhi_limit and cur_num + 1 < max_num )
        limit_str_1 = string.format("%s%s %s%s", limit_str_1, is_add_link and "<link=1>" or "", 
            (is_add_link and Language.Dujie.LimitStr1 or Language.Dujie.LimitStr2), is_add_link and "</link>" or "")
    end

    
    is_add_link = not (cultivation_level >= cultivation_limit)
    limit_str_2 = string.format("%s%s %s%s", limit_str_2,is_add_link and "<link=1>" or "", 
        (cultivation_level >= cultivation_limit and Language.Dujie.LimitStr2 or Language.Dujie.LimitStr1), is_add_link and "</link>" or "")
    
    
    self.node_list.text_limit_1.tmp.text = limit_str_1
    self.node_list.text_limit_2.tmp.text = limit_str_2

end

-- 刷新受损状态显示
function DujieView:FlushFail(ordeal_damage_time)
    self.node_list.toggle_fail_all_use.toggle.isOn = PlayerPrefsUtil.GetInt("dujie_fail_all_use") == 1
    self.node_list.text_fail_time.tmp.text = TimeUtil.FormatSecondDHM9(ordeal_damage_time)

    self:CleanFailTimer()
    self.fail_timer = CountDown.Instance:AddCountDown(ordeal_damage_time, 1,
        -- 回调方法
        function(elapse_time, total_time)
            self.node_list.text_fail_time.tmp.text = TimeUtil.FormatSecondDHM9(total_time - elapse_time)
        end,
        -- 倒计时完成回调方法
        function()
            self:FlushDujieView()
        end
    )

    local item_id = DujieWGData.Instance:GetOtherCfg("fail_damage_item_id")
    local num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    self.fail_cost_item_cell:SetData({item_id = item_id, is_bind = 0, num = num})

    local str = ToColorStrByState(num.."/1", num>0)
    self.fail_cost_item_cell:SetRightBottomTextVisible(true)

    self.fail_cost_item_cell:SetRightBottomText(str)
end

-- 伤害动画
function DujieView:HarmAnim(harm_num)
	self.node_list.harm_num.rect.anchoredPosition = Vector2(133, 25)

	if self.harm_tweener then
        self.harm_tweener:Kill()
        self.harm_tweener = nil
    end
	self.node_list.harm_num.tmp.text = "-"..harm_num

	self.harm_tweener = self.node_list.harm_num.rect:DOLocalMoveY(60, 0.5):OnComplete(function()
		self.node_list.harm_num.tmp.text = ""
	end):SetEase(DG.Tweening.Ease.Linear)
    -- 添加震屏
    if self.view_shake_tween then
		self.view_shake_tween:Kill()
		self.view_shake_tween = nil
	end
    self:ShakeNodeAnimiBurst(self.node_list.dujieLayer, self.view_shake_tween)
end

-- 刷新渡劫中界面
function DujieView:FlushDujieingView()
    local zhuanzhi_level = RoleWGData.Instance:GetZhuanZhiNumber()
    -- local cultivation_level = CultivationWGData.Instance:GetXiuWeiState()
    -- 仙修
    -- local strength_cfg = DujieWGData.Instance:GetStrengthCfg(cultivation_level)
    -- 转职
    local vitality_cfg = DujieWGData.Instance:GetVitalityCfg(zhuanzhi_level)
    -- 等级
    self.node_list.img_limit:SetActive(false)
    self.node_list.btn_start_dujie:SetActive(false)

    local ordel_info = DujieWGData.Instance:GetOrdealInfo()
    -- local old_slider_value_2 = self.node_list.img_vitality_slider.slider.value
    local old_slider_value_1 = self.node_list.img_strength_slider.slider.value
    -- local slider_diff_2 = old_slider_value_2 - (ordel_info.strength / strength_cfg.strength)
    local slider_diff_1 = old_slider_value_1 - (ordel_info.vitality / vitality_cfg.vitality) 

    -- 血量先到目标值
    self.node_list.img_vitality_slider.slider.value = ordel_info.vitality / vitality_cfg.vitality
    -- self.node_list.img_strength_slider.slider.value = ordel_info.strength / strength_cfg.strength
    self:CleanHpAnimTimer()
    self.hp_anim_timer = CountDown.Instance:AddCountDown(0.5, 0.02,
        -- 回调方法
        function(elapse_time, total_time)
            local new_value_1 = old_slider_value_1 - slider_diff_1 * (elapse_time / total_time)
            -- local new_value_2 = old_slider_value_2 - slider_diff_2 * (elapse_time / total_time)
            self.node_list.img_vitality_slider_down.slider.value = new_value_1
            -- self.node_list.img_strength_slider_down.slider.value = new_value_2

            self.node_list.text_vitality.tmp.text = string.format("%s/%s", math.floor(new_value_1 * vitality_cfg.vitality), vitality_cfg.vitality)
            -- self.node_list.text_strength.tmp.text = string.format("%s/%s", math.floor(new_value_2 * strength_cfg.strength), strength_cfg.strength)
        end,
        -- 倒计时完成回调方法
        function()
            self.node_list.img_vitality_slider_down.slider.value = ordel_info.vitality / vitality_cfg.vitality
            -- self.node_list.img_strength_slider_down.slider.value = ordel_info.strength / strength_cfg.strength
            self.node_list.text_vitality.tmp.text = string.format("%s/%s", ordel_info.vitality, vitality_cfg.vitality)
            -- self.node_list.text_strength.tmp.text = string.format("%s/%s", ordel_info.strength, strength_cfg.strength)
        end
    )

    local thunder_info = DujieWGData.Instance:GetFirstThunderInfo()
    if thunder_info then
        local thunder_cfg = DujieWGData.Instance:GetThunderCfgBySeq(thunder_info.seq)

        if thunder_cfg then
            -- print_error("开始雷电倒计时:",thunder_info.time)
            self:CleanWarningTimer()
            if thunder_info.time - THUNDER_WARNING_TIME > 0 then
                -- 预警倒计时
                self.warning_thunder_timer = GlobalTimerQuest:AddTimesTimer(function ()
                    -- print_error("预警出现")
                    self:SetWarningEffectShow(thunder_cfg.type)
                    self.node_list.img_warning:SetActive(true)
                end, thunder_info.time - THUNDER_WARNING_TIME , 1)
                self.node_list.img_warning:SetActive(false)

            else
                self.node_list.img_warning:SetActive(true)
            end

            self.next_thunder_type = thunder_cfg.type
            self.node_list.text_next_thunder.tmp.text = DujieWGData.Instance:GetThunderName(thunder_cfg.type)
            -- 雷电倒计时 因为时间误差 刷新时间比客户端倒计时快，所以直接在刷新的时候播放
        else
            self.node_list.img_warning:SetActive(false)
        end
    end

    local count = DujieWGData.Instance:GetThunderCount()
    self.node_list.text_thunder_count.tmp.text = string.format(Language.Dujie.NeedThunderCount,count)

    -- 渡劫保护
    self:FlushReduce()
    
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()
    -- 技能
    for i = 1, 3 do
        local skill_cfg = DujieWGData.Instance:GetSkillCfg(base_info.level+1, i - 1)
        local cd_time = DujieWGData.Instance:GetSkillCD(i)
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        self.show_skill_list[i]:SetSkillBtnCD(math.ceil(cd_time-server_time),skill_cfg.cd_time)

        if cd_time-server_time > 0 then
            self:CleanSkillCDTimer(i)
            local time = tonumber(string.format("%.1f", cd_time-server_time))
            self.skill_cd_timer_list[i] = CountDown.Instance:AddCountDown(time, 0.02,
                -- 回调方法
                function(elapse_time, total_time)
                    local server_time = TimeWGCtrl.Instance:GetServerTime()
                    self.show_skill_list[i]:SetSkillBtnCD(math.ceil(cd_time-server_time),skill_cfg.cd_time)
                end,
                -- 倒计时完成回调方法
                function()
                    local server_time = TimeWGCtrl.Instance:GetServerTime()
                    self.show_skill_list[i]:SetSkillBtnCD(math.ceil(cd_time-server_time),skill_cfg.cd_time)
                end
            )
        end
        
    end
end

-- 开始渡劫
function DujieView:OnClickBtnStartDujie()
    -- self:ShakeNodeAnimiBurst(self.node_list.dujieLayer, self.view_shake_tween)
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.Common then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CannotFindPath)
        return
    end
    if not DujieWGData.Instance:IsInDujieArea() then
        TipWGCtrl.Instance:OpenAlertTips(Language.Dujie.DujieAreaTips,function ()
            DujieWGData.Instance:SetIsNeedOpenDujie(true)
            self:Close()
            DujieWGCtrl.Instance:GotoDujieArea()
        end,nil,nil,nil,5,nil,Language.Dujie.Join)
        return
    end

    DujieWGCtrl.Instance:OpenDujieOperateView()
    
    -- MountWGCtrl.Instance:SendMountGoonReq(0)
    -- DujieWGCtrl.Instance:DujieStart()
end

-- 受损道具是否全部使用
function DujieView:OnClickBtnUseAll(is_on)
    if is_on then
        PlayerPrefsUtil.SetInt("dujie_fail_all_use", 1)
    else
        PlayerPrefsUtil.SetInt("dujie_fail_all_use", 0)
    end
end

-- 
function DujieView:OnClickBtnFailUse()
    local item_id = DujieWGData.Instance:GetOtherCfg("fail_damage_item_id")
    local num = ItemWGData.Instance:GetItemNumInBagById(item_id)

    if num <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.Prop_No_Enough)
        return
    end
    local item_data = ItemWGData.Instance:GetItem(item_id)
    if self.node_list.toggle_fail_all_use.toggle.isOn then
        BagWGCtrl.Instance:SendUseItem(item_data.index,num)
    else
        
        BagWGCtrl.Instance:SendUseItem(item_data.index,1)
    end
    
end

-- 打开属性界面
function DujieView:OnClickBtnAttr()
    -- DujieWGCtrl.Instance:OpenAttrView()
    DujieWGCtrl.Instance:OpenDujieSuccessView()
    
end



function DujieView:OnClickBtnReduceTips()
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()
    SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Dujie.ReduceTips,base_info.fail_reduce_per/100))
end


function DujieView:OnClickSkillBtn(index)

    -- 渡劫中释放技能
    if self.view_state == DUJIE_VIEW_STATE.DUJIE_ING then
        local skill_open_level = DujieWGData.Instance:GetOtherCfg("skill_open_level_"..index)
        local level = DujieWGData.Instance:GetDujieLevel()

        if level >= skill_open_level then
            DujieWGCtrl.Instance:UseDujieSkill(index - 1)
        end

    else
        -- 非渡劫显示技能tips
        local skill_id = DujieWGData.Instance:GetOtherCfg("dujie_skill_"..index)
        local skill_desc = SkillWGData.Instance:GetSKillDescBySkillId(skill_id)
        local show_data = {
            icon = skill_id,
            top_text = Language.Dujie.SkillName[index],					-- 技能名
            body_text = skill_desc,							-- 当前等级技能描述
            x = 0,
            y = 0,
            set_pos = true,
            is_active_skill = true,
        }
    
        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)

        local skill_open_level = DujieWGData.Instance:GetOtherCfg("skill_open_level_"..index)
        local level = DujieWGData.Instance:GetDujieLevel()

        if level < skill_open_level then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Dujie.SkillLimitStr,skill_open_level - level))
        end
    end

end

function DujieView:FlushDujieAttr()
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()

    local capality = 0
    if base_info.level == 0 then
        local has_data = false
        self.node_list.attr_layer:CustomSetActive(has_data)
        self.node_list.dujie_attr_list:CustomSetActive(has_data)
    else
        local data_list = DujieWGData.Instance:GetDujieAttrList(base_info.level,true)
        capality = DujieWGData.Instance:GetDujieCapality(base_info.level)
        local has_data = not IsEmptyTable(data_list)
        self.node_list.attr_layer:CustomSetActive(has_data)
        self.node_list.dujie_attr_list:CustomSetActive(has_data)
        if has_data then
            self.dujie_attr_list:SetDataList(data_list)
        end
    end

    -- local desc = DujieWGData.Instance:GetNextOpenFuncDesc(base_info.level)
    -- self.node_list.text_next_unlock.tmp.text = desc
    -- self.node_list.img_next_unlock:SetActive(desc ~= "")

    self.node_list.text_capability.tmp.text = capality
end

function DujieView:FlushDujieBar()
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()
    local index = DujieWGData.Instance:GetLevelIndex(base_info.level)
    local cur_level_cfg = DujieWGData.Instance:GetLevelCfg(base_info.level)
    local index_max = DujieWGData.Instance:GetTypeMax(cur_level_cfg.type, cur_level_cfg.type_seq) 

    -- 阶段图片
    local stage_asset_1, jinjie_asset_1 = ResPath.GetRawImagesPNG("a3_dujie_stage_"..cur_level_cfg.type)
    self.node_list.img_level_dujie.raw_image:LoadSprite(stage_asset_1, jinjie_asset_1, function ()
        self.node_list.img_level_dujie.raw_image:SetNativeSize()
    end)
    local stage_asset_2, jinjie_asset_2 = ResPath.GetRawImagesPNG("a3_dujie_stage_"..(cur_level_cfg.type+1))
    self.node_list.img_next_level_dujie.raw_image:LoadSprite(stage_asset_2, jinjie_asset_2, function ()
        self.node_list.img_next_level_dujie.raw_image:SetNativeSize()
    end)

    -- if cur_level_cfg.type_seq == 0 then
    --     self.node_list.text_level_dujie.text.text = cur_level_cfg.type
    --     -- self.node_list.img_level_dujie:SetActive(true)
    -- else
    --     -- self.node_list.img_level_dujie:SetActive(false)
    --     self.node_list.text_level_dujie.text.text = cur_level_cfg.type.."·"..NumberToChinaNumber(cur_level_cfg.type_seq)
    -- end

    local next_level_cfg = DujieWGData.Instance:GetLevelCfg(base_info.level + 1 )
    if IsEmptyTable(next_level_cfg)  then
        next_level_cfg = cur_level_cfg
    end
    if cur_level_cfg.type == next_level_cfg.type then
        self.node_list.text_next_level_dujie.text.text = cur_level_cfg.type.."·"..NumberToChinaNumber(cur_level_cfg.type_seq +1)
    else
        self.node_list.text_next_level_dujie.text.text = next_level_cfg.type.."·"..NumberToChinaNumber(next_level_cfg.type_seq)
    end
    

    self.node_list.img_node_end:SetActive(index == index_max)

    local target_fillAmount = index/index_max
    self.node_list.img_bar_dujie.image.fillAmount = target_fillAmount
end

-- 打开元神副本，跳转到空战副本
function DujieView:OnClickBtnSpiritFb()
    ViewManager.Instance:Open(GuideModuleName.ConquestWarView, TabIndex.conquest_war_kfkz)
end

-- 震动
function DujieView:ShakeNodeAnimiBurst(trans, sequence)
	sequence = sequence or DG.Tweening.DOTween.Sequence()
	local pos = trans.transform.localPosition
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, 15, 0), 0.01)) 	--右5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, 15, 0), 0.02))	--左5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, -15, 0), 0.02))	--下5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, -15, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOLocalMove(pos, 0.02))	--恢复 0
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, 15, 0), 0.01))	--左5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, 15, 0), 0.02)) 	--右5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, -15, 0), 0.02))	--下5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, -15, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOLocalMove(pos, 0.02))	--恢复 0
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, -15, 0), 0.01))	--下5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, 15, 0), 0.02)) 	--右5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, 15, 0), 0.02))	--左5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, -15, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOLocalMove(pos, 0.02))	--恢复 0
    sequence:SetEase(DG.Tweening.Ease.Linear)
end

