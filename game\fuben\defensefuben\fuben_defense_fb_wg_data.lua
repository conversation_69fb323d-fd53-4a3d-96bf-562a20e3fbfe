DefenseFbWGData = DefenseFbWGData or BaseClass()

DefenseFbWGData.NOTIFY_REASON = {
	NOTIFY_REASON_DEFAULT = 0, 
	NOTIFY_MONSTER_WAVE = 1, 
	NOTIFY_BOSS_FLUSH = 2,
	NOTIFY_EXTRA_BOSS = 3,
	NOTIFY_FB_END = 4,
	NOTIFY_PREPARE_TIME = 10,
}

function DefenseFbWGData:__init()
	self.defense_data = {}
	self.is_auto = false
end

function DefenseFbWGData:__delete()
end

function DefenseFbWGData:SetBuildTowerFBSceneLogicInfo(protocol)
	self.defense_data = protocol.data
end

function DefenseFbWGData:GetBuildTowerFBSceneLogicInfo()
	return self.defense_data
end

function DefenseFbWGData:GetDefensePosList()
	local build_tower_fb_auto = ConfigManager.Instance:GetAutoConfig("build_tower_fb_auto")
	return build_tower_fb_auto.tower_pos
end

function DefenseFbWGData:GetDefenseTowerCfg(tower_type, tower_level)
	local build_tower_fb_auto = ConfigManager.Instance:GetAutoConfig("build_tower_fb_auto")
	local now_defense, next_defense
	tower_level = tower_level == -1 and 1 or tower_level
	for k,v in ipairs(build_tower_fb_auto.tower_config)do
		if tower_type == v.tower_type then
			if tower_level + 1 == v.tower_level then
				next_defense = v
			elseif tower_level == v.tower_level then
				now_defense = v
			end
		end
		
		if next_defense and now_defense then
			break
		end
	end
	return now_defense, next_defense
end

function DefenseFbWGData:GetTowerNotBuildNum()
	local list = self:GetDefensePosList()
	local num = 0
	for i = 1, #list do
		if self.defense_data.tower_info_list[i - 1] and self.defense_data.tower_info_list[i - 1].tower_type <= -1 then
			num = num + 1
		end
	end
	if num > 0 then
		local info = self:GetBuildTowerFBSceneLogicInfo()
		local build_tower_fb_cfg = ConfigManager.Instance:GetAutoConfig("build_tower_fb_auto").tower_config
		if info.douhun > build_tower_fb_cfg[1].need_douhun then
			return true
		end
	end
	return false
end

function DefenseFbWGData:GetTowerIsBuildByPosIndex(pos_index)
	for k,v in pairs(self.defense_data.tower_info_list) do
		if k == pos_index then
			return v.tower_type > -1 
		end
	end
	return false
end

function DefenseFbWGData:GetIsNoTowerBuild()
	for k,v in pairs(self.defense_data.tower_info_list) do
		if v.tower_type > -1 then
			return true
		end
	end
	return false
end

function DefenseFbWGData:GetDefenseTowerCanMaxLevel(tower_type, douhun)
	local build_tower_fb_auto = ConfigManager.Instance:GetAutoConfig("build_tower_fb_auto")
	local can_level = 0
	for k,v in ipairs(build_tower_fb_auto.tower_config)do
		if tower_type == v.tower_type and douhun >= v.return_douhun and v.tower_level > can_level then
			can_level = v.tower_level
		end
	end
	return can_level
end

function DefenseFbWGData:GetDefenseTowerList()
	local build_tower_fb_auto = ConfigManager.Instance:GetAutoConfig("build_tower_fb_auto")
	return build_tower_fb_auto.tower_config
end

function DefenseFbWGData:GetDefenseTowerOtherCfg()
	local build_tower_fb_auto = ConfigManager.Instance:GetAutoConfig("build_tower_fb_auto")
	return build_tower_fb_auto.other[1]
end

function DefenseFbWGData:GetDedenseTowerWaveCount()
	local build_tower_fb_auto = ConfigManager.Instance:GetAutoConfig("build_tower_fb_auto")
	return #build_tower_fb_auto.monster_flush
end

function DefenseFbWGData:SetIsAutoCallBoss(is_auto)
	self.is_auto = is_auto
end

function DefenseFbWGData:GetIsAutoCallBoss()
	return self.is_auto
end