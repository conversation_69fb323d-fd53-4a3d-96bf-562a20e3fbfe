﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class YYTemporalReprojectionState : System.IDisposable
{
    public enum TemporalReprojectionBlendMode
    {
        /// <summary>
        /// Blur smartly with neighbor pixels, this reduces image quality but eliminates artifacts with fast changing graphics or camera
        /// </summary>
        Blur = 0,

        /// <summary>
        /// Render every pixel with a jittered camera frustum. This produces a sharper, higher quality image, but can introduce artifacts
        /// if the graphics or camera move fast. The shader attempts to work-around most of this by checking changing light conditions
        /// and doing neighborhood pixel clamping.
        /// </summary>
        Sharp = 1
    }

    public int ReprojectionSize { get; private set; }

    public void Dispose()
    {

    }
}
