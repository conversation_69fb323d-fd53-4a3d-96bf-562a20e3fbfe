

function DujieWGData:InitBodyCfg()
	self.ordeal_body_cfg = ListToMap(self.ordeal_cfg.body, "seq")

	self.ordeal_body_score_cfg = ListToMap(self.ordeal_cfg.body_socre, "body_seq", "level")
	-- self.ordeal_body_score_cfg = ListToMap(self.ordeal_cfg.body_socre, "body_seq", "need_score")

	self.ordeal_body_talent_cfg = ListToMap(self.ordeal_cfg.talent, "body_seq", "seq")
	self.ordeal_body_talent_pos_cfg = ListToMap(self.ordeal_cfg.talent, "body_seq", "pos_x", "pos_y")

	self.ordeal_body_talent_score_cfg = ListToMap(self.ordeal_cfg.talent_score, "body_seq", "element_count")

	-- 根据灵根品质以及元素数量 获取天赋技能索引
	self.ordeal_body_talent_color_cfg = ListToMap(self.ordeal_cfg.talent_color, "body_seq", "seq", "color")

	-- 天赋类型 元素 品质 对应属性
	self.ordeal_body_talent_attr_cfg = ListToMap(self.ordeal_cfg.talent_attr, "talent_type", "element", "color")

	-- 天赋技能索引
	self.ordeal_body_talent_skill_cfg =  ListToMap(self.ordeal_cfg.talent_skill, "seq")

	self.bounder_num = 9 -- 边界

	self:InitOrdealBodyParam()
	
	self:RecordTalentAboutData()
	self:RecordLineAboutTalent()

	RemindManager.Instance:Register(RemindName.Dujie_Body, BindTool.Bind(self.GetBodyRemind, self))
end

function DujieWGData:DeleteBody()
	RemindManager.Instance:UnRegister(RemindName.Dujie_Body)
end

function DujieWGData:GetBounderNum()
	return self.bounder_num or 0
end

function DujieWGData:InitOrdealBodyParam()
	self.ordeal_body_talent_map = {}
	self.ordeal_body_talent_map2 = {}
	self.talent_active_count = {}

	self.ordeal_body_attr_list = {}
end

-- 记录天赋点关联的有效天赋点
function DujieWGData:RecordTalentAboutData()
	self.talent_about_data_list = {}

	local body_count = #self.ordeal_body_cfg

	local about_cfg = nil
	for i = 0, body_count do
		local about_data_list = {}
		for k, v in pairs(self.ordeal_body_talent_cfg[i]) do
			local about_list = {}
			
			-- 获取自身的坐标
			local pos_x,pos_y = v.pos_x,v.pos_y
			-- 获取四周的有效坐标
			-- 上
			about_cfg = self:GetTalentCfgByPos(i,pos_x,pos_y+1)
			if about_cfg then
				table.insert(about_list, about_cfg)
			end
			-- 下
			about_cfg = self:GetTalentCfgByPos(i,pos_x,pos_y-1)
			if about_cfg then
				table.insert(about_list, about_cfg)
			end
			-- 左
			about_cfg = self:GetTalentCfgByPos(i,pos_x-1,pos_y)
			if about_cfg then
				table.insert(about_list, about_cfg)
			end
			-- 右
			about_cfg = self:GetTalentCfgByPos(i,pos_x+1,pos_y)
			if about_cfg then
				table.insert(about_list, about_cfg)
			end

			about_data_list[v.seq] = about_list
		end
		self.talent_about_data_list[i] = about_data_list
	end
end

-- 获取天赋的关联天赋
function DujieWGData:GetAboutTalentList(body_seq, seq)
	if self.talent_about_data_list[body_seq] and self.talent_about_data_list[body_seq][seq] then
		return self.talent_about_data_list[body_seq][seq]
	end
	return {}
end

-- 记录线条的关联天赋点
function DujieWGData:RecordLineAboutTalent()
	self.line_about_data_list = {}

	local body_count = #self.ordeal_body_cfg
	for body_seq = 0, body_count do
		local line_abount_list = {}
		for pos_x = 1, self.bounder_num - 1 do
			line_abount_list[pos_x] = {}
			for pos_y = 1, self.bounder_num - 1 do
				local line_data = {}
				-- 原始
				local talent_cfg = self:GetTalentCfgByPos(body_seq, pos_x, pos_y)
				if talent_cfg then
					line_data.seq = talent_cfg.seq
					-- 上
					local up_talent_cfg = self:GetTalentCfgByPos(body_seq, pos_x, pos_y+1)
					if up_talent_cfg  then
						line_data.up_seq =up_talent_cfg.seq
					end
					-- 右
					local right_talent_cfg = self:GetTalentCfgByPos(body_seq, pos_x+1, pos_y)
					if right_talent_cfg  then
						line_data.right_seq =right_talent_cfg.seq
					end
					line_abount_list[pos_x][pos_y] = line_data
				end
			end
		end
		self.line_about_data_list[body_seq] = line_abount_list
	end
end

function DujieWGData:GetTalnetLineAboutList(body_seq)
	return self.line_about_data_list[body_seq] or {}
end

--------------------------配置相关START --------------------------------
function DujieWGData:GetBodyTalentListByBodySeq(body_seq)
	if self.ordeal_body_talent_cfg[body_seq] then
		return self.ordeal_body_talent_cfg[body_seq]
	end
	return nil
end

function DujieWGData:GetBodyCfg()
	return self.ordeal_body_cfg;
end

function DujieWGData:GetBodyCfgBySeq(seq)
	return self.ordeal_body_cfg[seq];
end


-- 获取天赋cfg
function DujieWGData:GetTalentCfgByPos(body_seq,pos_x,pos_y)
	if self.ordeal_body_talent_pos_cfg[body_seq] and self.ordeal_body_talent_pos_cfg[body_seq][pos_x] and self.ordeal_body_talent_pos_cfg[body_seq][pos_x][pos_y] then
		return self.ordeal_body_talent_pos_cfg[body_seq][pos_x][pos_y]
	end
	return nil
end

-- 获取天赋cfg
function DujieWGData:GetTalentCfgBySeq(body_seq,seq)
	if self.ordeal_body_talent_cfg[body_seq] and self.ordeal_body_talent_cfg[body_seq][seq] then
		return self.ordeal_body_talent_cfg[body_seq][seq]
	end
	return nil
end

-- 获取激活消耗
function DujieWGData:GetActiveCost(body_seq,talent_seq)
	local talent_cfg = self:GetTalentCfgBySeq(body_seq,talent_seq)
	if talent_cfg then
		local active_cost = Split(talent_cfg.active_cost, ',')
		return tonumber(active_cost[1]),tonumber(active_cost[2])
	end
	return 0,0
end

-- 获取洗练消耗
function DujieWGData:GetRefineCost(body_seq,talent_seq)
	local talent_cfg = self:GetTalentCfgBySeq(body_seq,talent_seq)
	if talent_cfg then
		local refine_cost = Split(talent_cfg.refine_cost, ',')
		return tonumber(refine_cost[1]),tonumber(refine_cost[2])
	end
	return 0,0
end

-- 根据激活状态获取天赋消耗
function DujieWGData:GetActiveOrRefineCost(body_seq, talent_seq)
	if self:IsActiveTalent(body_seq,talent_seq) then
		local type, num = DujieWGData.Instance:GetRefineCost(body_seq,talent_seq)
		return type,num

	else
		local type, num = DujieWGData.Instance:GetActiveCost(body_seq,talent_seq)
		return type,num
	end
end

-- 判断消耗是否足够
function DujieWGData:IsActiveReach(body_seq,talent_seq)
	local type, num = self:GetActiveOrRefineCost(body_seq,talent_seq)

	local has_num = self:GetMoneyNum(type)
	return has_num >= num
end

-- 判断货币是否足够
function DujieWGData:IsReachMoney(type,num)
	local has_num = self:GetMoneyNum(type)
	return has_num >= num
end

-- 根据评分计算灵根品质
function DujieWGData:GetBodyScoreCfg(body_seq, score)
	local cfg = nil
	if self.ordeal_body_score_cfg[body_seq] then
		for i, v in ipairs(self.ordeal_body_score_cfg[body_seq]) do
			if score >= v.need_score then
				cfg = v
			else
				return cfg,v
			end
		end
	end
	return cfg, nil
end

-- 获取天赋评分数据
function DujieWGData:GetBodyScoreCfgByBody(body_seq)
	if self.ordeal_body_score_cfg[body_seq] then
		return self.ordeal_body_score_cfg[body_seq]
	end
	return {}
end

-- 获取天赋属性配置
function DujieWGData:GetTalentAttrCfg(type,element,color)
	if self.ordeal_body_talent_attr_cfg[type] and self.ordeal_body_talent_attr_cfg[type][element] and self.ordeal_body_talent_attr_cfg[type][element][color] then
		return self.ordeal_body_talent_attr_cfg[type][element][color]
	end
	return nil
end

-- 获取天赋属性和战力
function DujieWGData:GetTalentAttrDataAndCap(cfg)
	local attr_list = {}
    local capability = 0
	if cfg then
		attr_list, capability = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value","attr_name","attr_value",1,3)
		return attr_list,capability
	end
	return attr_list,capability
end

-- 获取天赋技能功能配置
function DujieWGData:GetTalentSkillCfg(skill_seq)
	return self.ordeal_body_talent_skill_cfg[skill_seq] or nil
end

-- 获取技能天赋点的技能列表 获取天赋品质配置
function DujieWGData:GetTalentColorCfg(body_seq, talent_seq, color)
	if self.ordeal_body_talent_color_cfg[body_seq] and self.ordeal_body_talent_color_cfg[body_seq][talent_seq] then
		if self.ordeal_body_talent_color_cfg[body_seq][talent_seq][color] then
			return self.ordeal_body_talent_color_cfg[body_seq][talent_seq][color]
		else
			print_error("缺少配置策划请检查")
			return nil
		end
	end
	return nil
end

-- 获取技能天赋点的技能列表和消耗
function DujieWGData:GetSkillTalentSkillDataList(body_seq, talent_seq)
	-- 技能天赋点固定读品质5的配置
	local color_cfg = self:GetTalentColorCfg(body_seq, talent_seq, 5) 
	if color_cfg then
		local skill_seq_list_temp = Split(color_cfg.skill_seq,"|")
		local cost_temp = Split(color_cfg.choose_skill_cost,"|")

		if #skill_seq_list_temp ~= #skill_seq_list_temp then
			print_error("天赋品质 技能数量和消耗数量配置不同，策划请检查")
			return {}
		end
		
		local skill_data_list = {}
		for k, v in ipairs(skill_seq_list_temp) do
			local data = {}
			data.skill_seq = tonumber(v)
			data.cost_data = {}
			local cost_data = Split(cost_temp[k],",")
			data.cost_data.type = tonumber(cost_data[1])
			data.cost_data.num = tonumber(cost_data[2])
			table.insert(skill_data_list,data)
		end

		
		return skill_data_list
	else
		return {}
	end
	
end


-- 获得 属性名字param0           属性值param1
-- 当前灵根属性名字param0额外提升param1%
-- 每param1个param0元素获得 param2(属性类型) param3（属性值)
-- 当前灵根有更高几率洗髓出高品质
-- 当前灵根有更高几率洗髓出X元素（当前灵根元素）


-- 获取天赋技能描述
function DujieWGData:GetTalentSkillDesc(skill_seq, body_seq)
	local skill_cfg = self:GetTalentSkillCfg(skill_seq)
	if skill_cfg then
		return skill_cfg.skill_desc,skill_cfg.skill_name
	end
	-- if skill_cfg then
	-- 	if skill_cfg.type == 1 then
	-- 		--属性
	-- 		local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(skill_cfg.param0))
	-- 		local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str)
	-- 		local value  = AttributeMgr.PerAttrValue(attr_str, skill_cfg.param1)
	-- 		return string.format(Language.Dujie.SkillDesc[1],attr_name,value), skill_cfg.skill_name

	-- 	elseif skill_cfg.type == 2 then
	-- 		--属性
	-- 		local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(skill_cfg.param0))
	-- 		local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str)
	-- 		return string.format(Language.Dujie.SkillDesc[2],attr_name,skill_cfg.param1/100), skill_cfg.skill_name

	-- 	elseif skill_cfg.type == 3 then
	-- 		--元素名
	-- 		local element_str = DujieWGData.Instance:GetDescByTypeAndSeq(DUJIE_DESC_TYPE.Element, skill_cfg.param0)
	-- 		--属性
	-- 		local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(skill_cfg.param2))
	-- 		local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str)
	-- 		return string.format(Language.Dujie.SkillDesc[3],skill_cfg.param1, element_str, attr_name, skill_cfg.param3), skill_cfg.skill_name

	-- 	elseif skill_cfg.type == 4 then
	-- 		return Language.Dujie.SkillDesc[4], skill_cfg.skill_name

	-- 	elseif skill_cfg.type == 5 then
	-- 		local body_cfg = self:GetBodyCfgBySeq(body_seq)
	-- 		local desc = self:GetDescByTypeAndSeq(DUJIE_DESC_TYPE.Element, body_cfg.element)
	-- 		return string.format(Language.Dujie.SkillDesc[5], desc), skill_cfg.skill_name
	-- 	end

	-- end

	return "",""
end


-- 天赋点评分计算
function DujieWGData:CountTalentScore(body_seq, element, element_count)
	if self.ordeal_body_talent_score_cfg[body_seq] and self.ordeal_body_talent_score_cfg[body_seq][element_count] then
		local score_cfg = self.ordeal_body_talent_score_cfg[body_seq][element_count]

		if score_cfg["element_score"..element] then
			return score_cfg["element_score"..element]
		end
	end
	return 0
end

--------------------------配置相关END --------------------------------


------------------------ protocol part ------------------------
-- 灵根基础信息
function DujieWGData:SetAllOrdealBodyBaseInfo(protocol)
	self.ordeal_body_money_list = protocol.ordeal_body_money_list
end

-- 获取灵根货币数量
function DujieWGData:GetMoneyNum(type)
	return self.ordeal_body_money_list[type] or 0
end

-- 灵根信息
function DujieWGData:SetAllOrdealBodyInfo(protocol)
	self.ordeal_body_item_list = protocol.ordeal_body_item_list
end

-- 更新部分灵根信息
function DujieWGData:SetOrdealBodyInfo(protocol)
	local body_seq = protocol.body_seq
	if body_seq then
		self.ordeal_body_item_list[body_seq] = protocol.single_body_item_info
	end
end

-- 更新灵根属性
function DujieWGData:SetOrdealBodyAttrInfo(protocol)
	local body_seq = protocol.body_seq
	if body_seq then
		local data = {}
		data.count = protocol.count
		data.attr_list = protocol.attr_list
		self.ordeal_body_attr_list[body_seq] = data
	end
end

-- 获取灵根属性
function DujieWGData:GetordealBodyAttrInfo(body_seq)
	if self.ordeal_body_attr_list and self.ordeal_body_attr_list[body_seq] then
		return self.ordeal_body_attr_list[body_seq]
	end
	return {}
end

-- 获取天赋激活数量
function DujieWGData:GetActiveCountByBody(body_seq)
	if self.ordeal_body_item_list and  self.ordeal_body_item_list[body_seq] then
		return self.ordeal_body_item_list[body_seq].active_count
	end
	return 0
end

-- 获取天赋点显示数量
function DujieWGData:GetTalentShowCountByBody(body_seq)
	local talent_list = self:GetTalentList(body_seq)
	local count = 0
	for k, v in pairs(talent_list) do
		if self:IsShowTalent(body_seq,k) then
			count = count + 1
		end
	end
	
	return count
end

-- 获取灵根评分
function DujieWGData:GetBodyScore(body_seq)
	if self.ordeal_body_item_list and  self.ordeal_body_item_list[body_seq] then
		return self.ordeal_body_item_list[body_seq].score
	end
	return 0
end

-- 获取天赋协议信息
function DujieWGData:GetTalentInfo(body_seq,talent_seq)
	if self.ordeal_body_item_list and  self.ordeal_body_item_list[body_seq] then
		return self.ordeal_body_item_list[body_seq].talent_item_list[talent_seq] or nil
	end
	return nil
end

-- 天赋是否已激活
function DujieWGData:IsActiveTalent(body_seq,talent_seq)
	local info = self:GetTalentInfo(body_seq,talent_seq)
	if info and info.is_active > 0 then
		return true
	end
	return false
end

-- 天赋是否显示
function DujieWGData:IsShowTalent(body_seq,talent_seq)
	if talent_seq == 0 then
		return true
	end
	local cfg = self:GetTalentCfgBySeq(body_seq,talent_seq)
	if cfg and cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
		return true
	end

	local info = self:GetTalentInfo(body_seq,talent_seq)
	if info then
		if info.is_active > 0 then
			return true
		end
		local about_talent_list = self:GetAboutTalentList(body_seq,talent_seq)
		for k, v in pairs(about_talent_list) do
			if self:IsActiveTalentFlag(v.body_seq,v.seq) then
				return true
			end
		end
	end
	return false 
end

-- 前置天赋是否激活
function DujieWGData:IsActivePreTalent(body_seq,talent_seq)
	local cfg = self:GetTalentCfgBySeq(body_seq,talent_seq)
	if cfg then
		if cfg.pre_seq == -1 then
			return true
		else
			local pre_list = Split(cfg.pre_seq,"|")
            for k, v in pairs(pre_list) do
                if DujieWGData.Instance:IsActiveTalent(body_seq,tonumber(v)) then
                    return true
                end
            end
			return false
		end
	end
	return true
end

-- 获取天赋列表
function DujieWGData:GetTalentList(body_seq)
	if self.ordeal_body_item_list[body_seq] then
		return self.ordeal_body_item_list[body_seq].talent_item_list
	end
	return {};
end

-- 天赋是否激活过
function DujieWGData:IsActiveTalentFlag(body_seq,talent_seq)
	if self.ordeal_body_item_list and  self.ordeal_body_item_list[body_seq] then
		return self.ordeal_body_item_list[body_seq].active_flag[talent_seq] == 1
	end
	return nil
end

-- 灵根是否激活
function DujieWGData:IsActiveBody(body_seq)
	if self.ordeal_body_item_list and  self.ordeal_body_item_list[body_seq] then
		return self.ordeal_body_item_list[body_seq].is_active == 1
	end
	return nil
end

-- 获取技能列表
function DujieWGData:GetTalentSkillList(body_seq)
	local talent_list = self:GetTalentList(body_seq)
	local skill_list = {}
	-- 第一个特殊处理
	local first_data = {}
	first_data.is_special = true
	first_data.body_seq = body_seq
	table.insert(skill_list, first_data)

	-- 所有金点的都要显示
	local talent_cfg_list = self:GetBodyTalentListByBodySeq(body_seq)
	-- 第0个一定是普通点 直接使用ipairs保证顺序
	for i, v in ipairs(talent_cfg_list) do
		if v.type == DUJIE_BODY_TALENT_TYPE.SKILL then
			local data = {}
			data.body_seq = body_seq
			data.info = self:GetTalentInfo(body_seq,v.seq)
			data.cfg = v
			table.insert(skill_list, data)
		end
	end
	-- 把所有已获得技能添加进去
	for k, v in pairs(talent_list) do
		if v.skill_seq >= 0 then
			local talent_cfg = self:GetTalentCfgBySeq(body_seq, k)
			-- 金点的排除
			if talent_cfg.type ~= DUJIE_BODY_TALENT_TYPE.SKILL then
				local data = {}
				data.body_seq = body_seq
				data.info = v
				data.cfg = talent_cfg
				table.insert(skill_list, data)
			end
		end
	end
	return skill_list
end

function DujieWGData:GetBodyInfo(body_seq)
	if self.ordeal_body_item_list[body_seq] then
		return self.ordeal_body_item_list[body_seq]
	end
	return nil
end

-- 设置(记录)新节点显示数量
function DujieWGData:SetNewTalentNum(num)
	self.new_talent_num = num or 0
end

-- 获取(记录)新节点显示数量
function DujieWGData:GetNewTalentNum()
	return self.new_talent_num or 0
end


------------------------ protocol part ------------------------

------------------------ 红点------------------------

-- 灵根是否已开启
function DujieWGData:IsActiveBody()
	-- local level = self:GetDujieLevel()
	-- local limit_level = self:GetOtherCfg("body_limit_level")
	-- return level >= limit_level

	return false 
end

-- 灵根红点
function DujieWGData:GetBodyRemind()
	if not self:IsActiveBody() then
		return 0
	end
	-- 如果灵根可激活
	for k, v in pairs(self.ordeal_body_cfg) do
		
		if self:GetBodyRemindBySeq(v.seq) then
			return 1
		end
	end

	return 0
end

-- 灵根红点
function DujieWGData:GetBodyRemindBySeq(body_seq)
	if self:IsCanActiveBody(body_seq) then
		return true
	end
	return false
end

-- 灵根是否可激活 -- 渡劫等级达到 未激活且道具满足  
function DujieWGData:IsCanActiveBody(body_seq)
	local level = 0
	local base_info = self:GetOrdealBaseInfo()
	if base_info then
		level = base_info.level
	end

	local body_cfg = self.ordeal_body_cfg[body_seq]
	if not self:IsActiveBody(body_seq) and body_cfg and level >=  body_cfg.unlock_ordeal_level  then
		if body_cfg.unlock_item_id > 0 and body_cfg.unlock_item_num > 0 then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(body_cfg.unlock_item_id)
			return item_num >= body_cfg.unlock_item_num
		end
		return true
	end
	return false
end

------------------------ 红点------------------------
