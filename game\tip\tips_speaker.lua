 TipSpeakerView = TipSpeakerView or BaseClass(SafeBaseView)

local SPEED = 150						-- 字幕滚动的速度(像素/秒)
local CLOSE_TIME = 30

function TipSpeakerView:__init()
	self.view_layer = UiLayer.PopTop
	self:AddViewResource(0, "uis/view/miscpre_load_prefab", "SpeakerNoticeView")
    self.view_name = "TipSpeakerView"
	self.is_open = false
	self.str_list = {}
	self.current_index = 1
	self.total_count = 0
	self.calculate_time_quest = nil
	self.calculate_time_quest2 = nil

	self.msg_list = {}
	self.low_priority_show_str = {}

	self.show_str = nil
	self.msg_data = nil
	self.start_pos = nil
end

function TipSpeakerView:__delete()
	if nil ~= self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
		self.calculate_time_quest = nil
	end

	if nil ~= self.calculate_time_quest2 then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest2)
		self.calculate_time_quest2 = nil
	end

	self.is_open = false
end

function TipSpeakerView:LoadCallBack()
	self.img_horn = self.node_list["Icon"]
	self.text_trans = self.node_list["EmojiText"]:GetComponent(typeof(UnityEngine.RectTransform))
	self.mask_width = self.node_list["EmojiText"]:GetComponent(typeof(UnityEngine.RectTransform)).parent:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta.x
	self.mask_width_cost_time = self.mask_width / SPEED


	self.text_trans2 = self.node_list["EmojiText2"]:GetComponent(typeof(UnityEngine.RectTransform))
	self.mask_width2 = self.node_list["EmojiText2"]:GetComponent(typeof(UnityEngine.RectTransform)).parent:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta.x
	self.mask_width_cost_time2 = self.mask_width2 / SPEED

	self.is_open = true
	self.shield_hearsay = GlobalEventSystem:Bind(SettingEventType.CLOSE_HEARSAY, BindTool.Bind1(self.OnShieldHearsay, self))

	if self.start_pos == nil then
		self.start_pos = self.text_trans.anchoredPosition
	end

	if self.start_pos2 == nil then
		self.start_pos2 = self.text_trans2.anchoredPosition
	end
	self.node_list["close"].button:AddClickListener(BindTool.Bind(self.OnClickClose,self))
end

function TipSpeakerView:ReleaseCallBack()
	if self.shield_hearsay then
		GlobalEventSystem:UnBind(self.shield_hearsay)
		self.shield_hearsay = nil
	end

	-- 清理变量和对象
	self.text_trans = nil
	self.tweener = nil
	self.img_horn = nil

	self.priority_tween = nil

	--self.msg_list = {}
	self.cache_priority_str = nil
	self.show_str = nil
	self.msg_data = nil
	self.text_trans2 = nil
end

function TipSpeakerView:OnShieldHearsay(value)
	if value then
		self:Close()
	end
end

function TipSpeakerView:OpenCallBack()
	self.tweener = nil
	self.priority_tween = nil
end

function TipSpeakerView:CloseCallBack()
	if self.tweener then
		self.tweener:Pause()
	end

	if self.priority_tween then
		self.priority_tween:Pause()
	end

	self.cache_priority_str = nil

	if nil ~= self.colse_time_quest then
		GlobalTimerQuest:CancelQuest(self.colse_time_quest)
		self.colse_time_quest = nil
	end

	if nil ~= self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
		self.calculate_time_quest = nil
	end

	if nil ~= self.calculate_time_quest2 then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest2)
		self.calculate_time_quest2 = nil
	end
end

function TipSpeakerView:SetNotice(msg_info)
	local str
	if SPEAKER_TYPE.SPEAKER_TYPE_LOCAL == msg_info.speaker_type then
		str = string.format("{wordcolor;01ab26;%s:}", msg_info.username) .. msg_info.content
	else
		local str_content = string.format("{wordcolor;01ab26;%s:}", msg_info.username) .. msg_info.content
		str = msg_info.server_id .. Language.Login.Fu .. "-" .. str_content
	end
	
	self.show_str = str
	self.msg_data = msg_info
	if not self.is_open then
		self:Open()
	end
	self:StopShowPriorityStr()
	self:Flush()
end

function TipSpeakerView:SetLowPriorityNotice(msg_info)
	local str = msg_info.content
	if not self.low_priority_show_str_list then
		self.low_priority_show_str_list = {}
	end
	table.insert(self.low_priority_show_str_list,str)
	if not self.is_open then
		self:Open()
	end
	-- if self.show_str == nil or self.msg_data == nil then
	-- 	self:Flush()
	-- end
end

function TipSpeakerView:OnFlush()
	if nil ~= self.colse_time_quest then
		GlobalTimerQuest:CancelQuest(self.colse_time_quest)
		self.colse_time_quest = nil
	end

	if nil ~= self.calculate_time_quest then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest)
		self.calculate_time_quest = nil
	end

	if self.show_str == nil or self.msg_data == nil then
		self:CheckPriorityShow()
		return
	end

	self.node_list["close"]:SetActive(false)
	local msg_info = self.msg_data
	if SPEAKER_TYPE.SPEAKER_TYPE_LOCAL == msg_info.speaker_type then
		self.img_horn.image:LoadSprite(ResPath.GetLoadingPath("a3_lt_benfu"))
	else
		self.img_horn.image:LoadSprite(ResPath.GetLoadingPath("a3_lt_kuafu"))
	end

	local str = self.show_str
	EmojiTextUtil.ParseRichText(self.node_list.EmojiText.tmp, str, 16)
	-- 计算宽高 参数：控件，内容，最大宽度
	local bounds = TMPUtil.GetTMPBounds(self.node_list.EmojiText.tmp, self.node_list.EmojiText.tmp.text, 9999)

	-- 设置文本宽高
	self.node_list.EmojiText.rect.sizeDelta = bounds

	self.text_trans.anchoredPosition = Vector2(0, 0)

	self.calculate_time_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.Calculate, self), 0.1)
	self.colse_time_quest = GlobalTimerQuest:AddDelayTimer(function()
		self.show_str = nil
		self.msg_data = nil
		if self:IsOpen() then
			self:CheckPriorityShow()
		end

	end, CLOSE_TIME)
end

function TipSpeakerView:OnClickClose()
	self:Close()
end

function TipSpeakerView:CheckPriorityShow()
	if IsEmptyTable(self.low_priority_show_str_list) and not self.cache_priority_str then
		self:Close()
	end

	if self.cache_priority_str then
		self:ContiuteShowPriorityStr()
		return
	end

	if not IsEmptyTable(self.low_priority_show_str_list) then
		self:ShowNextPriorityStr()
	end
end

function TipSpeakerView:ShowNextPriorityStr()
	self.img_horn.image:LoadSprite(ResPath.GetLoadingPath("a3_lt_huodong"))
	self.node_list["close"]:SetActive(true)
	self.node_list.Mask.canvas_group.alpha = 0
	self.node_list.Mask2.canvas_group.alpha = 1

	self.cache_priority_str = table.remove(self.low_priority_show_str_list,1)
	local str = self.cache_priority_str
	EmojiTextUtil.ParseRichText(self.node_list.EmojiText2.emoji_text, str, 16)
	-- 计算宽高 参数：控件，内容，最大宽度
	local bounds = TMPUtil.GetTMPBounds(self.node_list.EmojiText2.tmp, self.node_list.EmojiText.tmp.text, 9999)

	-- 设置文本宽高
	self.node_list.EmojiText2.rect.sizeDelta = bounds
	self.text_trans2.anchoredPosition = Vector2(0, 0)

	if nil ~= self.colse_time_quest then
		GlobalTimerQuest:CancelQuest(self.colse_time_quest)
		self.colse_time_quest = nil
	end

	if nil ~= self.calculate_time_quest2 then
		GlobalTimerQuest:CancelQuest(self.calculate_time_quest2)
		self.calculate_time_quest2 = nil
	end

	self.calculate_time_quest2 = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.Calculate2, self), 0.1)

	self.colse_time_quest = GlobalTimerQuest:AddDelayTimer(function()
		if self:IsOpen() then
			self:CheckPriorityShow()
		end
	end, CLOSE_TIME)
end

function TipSpeakerView:StopShowPriorityStr()
	self.node_list.Mask.canvas_group.alpha = 1
	self.node_list.Mask2.canvas_group.alpha = 0
	self.node_list["close"]:SetActive(true)
	if self.priority_tween then
		self.priority_tween:Pause()
	end
end

function TipSpeakerView:ContiuteShowPriorityStr()
	if self.priority_tween then
		self.node_list.Mask.canvas_group.alpha = 0
		self.node_list.Mask2.canvas_group.alpha = 1
		self.node_list["close"]:SetActive(true)
		self.img_horn.image:LoadSprite(ResPath.GetLoadingPath("a3_lt_huodong"))
		self.priority_tween:Play()
	else
		self:Close()
	end
end


-- 计算滚动的时间的位置
function TipSpeakerView:Calculate()
	if nil == self.text_trans then
		self:Close()
		return
	end

	if self.tweener ~= nil then
		self.tweener:Kill()
		if self.start_pos ~= nil and self.text_trans ~= nil then
			self.text_trans.anchoredPosition = self.start_pos
		end
	end

	local width = self.text_trans.sizeDelta.x
	local duration = width / SPEED + self.mask_width_cost_time
	width = width + self.mask_width
	local tweener = self.text_trans:DOAnchorPosX(-width, duration, false)
	duration = duration == 0 and 1 or duration
	local loops = math.ceil(CLOSE_TIME / duration)
	self.tweener = tweener
	tweener:SetEase(DG.Tweening.Ease.Linear)
	tweener:SetLoops(loops)
end

-- 计算滚动的时间的位置
function TipSpeakerView:Calculate2()
	if nil == self.text_trans2 then
		self:Close()
		return
	end

	if self.priority_tween ~= nil then
		self.priority_tween:Kill()
		if self.start_pos2 ~= nil and self.text_trans2 ~= nil then
			self.text_trans2.anchoredPosition = self.start_pos2
		end
	end

	local width = self.text_trans2.sizeDelta.x
	local duration = width / SPEED + self.mask_width_cost_time2
	width = width + self.mask_width2
	local tweener = self.text_trans2:DOAnchorPosX(-width, duration, false)
	duration = duration == 0 and 1 or duration
	local priority_loops = 3 --math.ceil(CLOSE_TIME / duration)
	self.priority_tween = tweener
	tweener:SetEase(DG.Tweening.Ease.Linear)
	tweener:SetLoops(priority_loops)
	tweener:OnComplete(function()
		self.cache_priority_str = nil
		self:Flush()
	end)
end