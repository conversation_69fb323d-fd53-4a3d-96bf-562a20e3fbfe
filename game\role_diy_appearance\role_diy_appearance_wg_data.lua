RoleDiyAppearanceWGData = RoleDiyAppearanceWGData or BaseClass()
RoleDiyAppearanceWGData.ADD_DIY_TYPE = {
	ALL = 1,                 -- 全套数据
	RESET = 2,               -- 重置
	EYE_SHADOW_COLOR = 3,    -- 眼影颜色
	EYE_SIZE = 4,            -- 眼睛大小
	EYE_POS = 5,             -- 眼睛上下
	PUPIL_TYPE = 6,          -- 瞳孔款式
	PUPIL_SIZE = 6,          -- 瞳孔大小
	PUPIL_COLOR = 7,         -- 瞳孔颜色
	MOUTH_COLOR = 8,         -- 嘴唇颜色
	MOUTH_SIZE = 9,          -- 嘴唇大小
	MOUTH_POS = 10,          -- 嘴唇上下
	FACE_DECAL = 11,         -- 贴花
	HAIR_TYPE = 12,          -- 头发款式
	HAIR_COLOR = 13,         -- 头发颜色
}

RoleDiyAppearanceWGData.CHANGE_DIY_INDEX = {
	ADD = 1,
	MINUS = 2,
}

function RoleDiyAppearanceWGData:__init()
	if RoleDiyAppearanceWGData.Instance ~= nil then
		ErrorLog("[RoleDiyAppearanceWGData] attempt to create singleton twice!")
		return
	end

	RoleDiyAppearanceWGData.Instance = self
	self:InitCfg()

	self.record_diy_data = {}          --记录定制得数据
	self.cur_diy_select_index = 0      -- 当前数据索引
	self.record_change_data = {}       -- 记录具体改变数据
	self.max_record_num = 10           -- 最大记录10条
end

function RoleDiyAppearanceWGData:__delete()
	RoleDiyAppearanceWGData.Instance = nil
end

function RoleDiyAppearanceWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("role_diy_appearance_auto")
	self.preset_diy_cfg = ListToMap(cfg.preset_diy, "sex","prof", "preset_seq")
	self.eye_shadow_type_cfg = ListToMap(cfg.eye_shadow_type, "seq")
	self.pupil_type_cfg = ListToMap(cfg.pupil_type, "seq")
	self.pupil_type_id_cfg = ListToMap(cfg.pupil_type, "pupil_type")
	self.color_suggest_cfg = ListToMap(cfg.color_suggest, "seq")
	self.mouth_type_cfg = ListToMap(cfg.mouth_type, "seq")
	self.face_decal_cfg = ListToMap(cfg.face_decal, "seq")
	self.face_decal_id_cfg = ListToMap(cfg.face_decal, "face_decal_id")
	self.hair_diy_type_cfg = ListToMap(cfg.hair_diy_type, "sex", "prof", "seq")
	self.alpha_cfg = ListToMap(cfg.alpha, "sex","prof", "face_id")
	self.prof_cfg = ListToMap(cfg.prof, "sex","prof")
	self.pay_type_cfg = ListToMap(cfg.pay_type, "type","seq")
	self.pay_type_consume_cfg = ListToMap(cfg.pay_type, "consume_id")
	self.other_cfg = cfg.other[1]
end

function RoleDiyAppearanceWGData:GetPresetDiyCfgBySexAndProf(sex, prof, preset_seq)
	if preset_seq then
		return ((self.preset_diy_cfg[sex] or {})[prof] or {})[preset_seq]
	else
		return (self.preset_diy_cfg[sex] or {})[prof]
	end
end

function RoleDiyAppearanceWGData:GetEyeShadowTypeCfg()
	return self.eye_shadow_type_cfg
end

function RoleDiyAppearanceWGData:GetPupilTypeCfg(operate_type)
	local show_data = {}

	if operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL then
		for k, v in pairs(self.pupil_type_cfg) do
			if v.is_pay_type ~= 1 then
				table.insert(show_data, v)
			end
		end
	else
		for k, v in pairs(self.pupil_type_cfg) do
			table.insert(show_data, v)
		end
	end

	if not IsEmptyTable(show_data) then
		table.sort(show_data, SortTools.KeyLowerSorters("sort_num"))
	end

	return show_data
end

function RoleDiyAppearanceWGData:GetPupilTypeByIdCfg(pupil_type)
	return self.pupil_type_id_cfg[pupil_type]
end

function RoleDiyAppearanceWGData:GetColorSuggestCfg()
	return self.color_suggest_cfg
end

function RoleDiyAppearanceWGData:GetMouthTypeCfg()
	return self.mouth_type_cfg
end

function RoleDiyAppearanceWGData:GetFaceDecalCfg(operate_type)
	local show_data = {}

	if operate_type == ROLE_DIY_APPEARANCE_TYPE.CREATE_ROEL then
		for k, v in pairs(self.face_decal_cfg) do
			if v.is_pay_type ~= 1 then
				table.insert(show_data, v)
			end
		end
	else
		for k, v in pairs(self.face_decal_cfg) do
			table.insert(show_data, v)
		end
	end

	if not IsEmptyTable(show_data) then
		table.sort(show_data, SortTools.KeyLowerSorters("sort_num"))
	end

	return show_data
end

function RoleDiyAppearanceWGData:GetFaceDecalByIdCfg(face_decal_id)
	return self.face_decal_id_cfg[face_decal_id]
end

function RoleDiyAppearanceWGData:GetHairDiyTypeCfg(sex, prof)
	return (self.hair_diy_type_cfg[sex] or {})[prof]
end

function RoleDiyAppearanceWGData:GetAlphaCfg(sex, prof, face_id)
	return ((self.alpha_cfg[sex] or {})[prof] or {})[face_id]
end

function RoleDiyAppearanceWGData:GetOtherCfg()
	return self.other_cfg
end

function RoleDiyAppearanceWGData:CreatDiyTemplateData()
	return {body_id = 0, face_id = 0, hair_id = 0, hair_color = "FFFFFF", eye_shadow_color = "FFFFFF", eye_shadow_alpha = 255, eye_size = 0, eye_pos = 0, pupil_type = 0,
			pupil_size = 1, pupil_color = "FFFFFF", pupil_color_alpha = 255, mouth_color = "FFFFFF", mouth_alpha = 255, mouth_size = 0, mouth_pos = 0, face_decal_id = 0,
			preset_seq = 1}
end

function RoleDiyAppearanceWGData:RemoveDiyAppearaceData()
	self.record_diy_data = {}
	self.cur_diy_select_index = 0 
	self.record_change_data = {}
end

function RoleDiyAppearanceWGData:AddDiyAppearaceData(diy_type, diy_data)
	local get_new_data = function (diy_data) 
		local data = {}
		local change_data = {}

		local is_change = false
		local cur_show_diy_data = self:GetCurShowDiyData()
		if cur_show_diy_data then
			data = DeepCopy(cur_show_diy_data)
		else
			data = self:CreatDiyTemplateData()
		end

		for k, v in pairs(data) do
			if diy_data[k] then
				change_data[k] = diy_data[k]
				if v ~= diy_data[k] then
					data[k] = diy_data[k]
					is_change = true
				end
			end
		end

		return data, is_change, change_data
	end

	local updata_record = function(diy_type, new_data, change_data)
		local new_change_data = {}
		new_change_data["diy_type"] = diy_type
		new_change_data["change_info"] = change_data

		if diy_type == RoleDiyAppearanceWGData.ADD_DIY_TYPE.ALL then
			self:RemoveDiyAppearaceData()
			self.record_diy_data[1] = new_data
			self.cur_diy_select_index = 1
			self.record_change_data[1] = new_change_data
		elseif (not IsEmptyTable(self.record_diy_data)) then
			if self.cur_diy_select_index == self.max_record_num then
				--超过记录数量  移除第一条  新数据插入末尾
				table.remove(self.record_diy_data, 1)	
				self.record_diy_data[#self.record_diy_data + 1] = new_data

				table.remove(self.record_change_data, 1)	
				self.record_change_data[#self.record_change_data + 1] = new_change_data
			elseif self.cur_diy_select_index < #self.record_diy_data then
				for i = #self.record_diy_data, self.cur_diy_select_index + 1, -1 do
					self.record_diy_data[i] = nil
					self.record_change_data[i] = nil
				end

				self.record_diy_data[#self.record_diy_data + 1] = new_data
				self.cur_diy_select_index = self.cur_diy_select_index + 1
				self.record_change_data[#self.record_change_data + 1] = new_change_data
			else
				self.record_diy_data[#self.record_diy_data + 1] = new_data
				self.cur_diy_select_index = self.cur_diy_select_index + 1
				self.record_change_data[#self.record_change_data + 1] = new_change_data
			end
		end
	end


	local new_data, is_change, change_data = get_new_data(diy_data)
	if is_change then
		updata_record(diy_type, new_data, change_data)
	end
end

function RoleDiyAppearanceWGData:GetCurShowDiyData()
	return self.record_diy_data[self.cur_diy_select_index]
end

function RoleDiyAppearanceWGData:GetRecordDiyDataAndIndex()
	return self.record_diy_data, self.cur_diy_select_index, self.record_change_data
end

function RoleDiyAppearanceWGData:ChangeCurDiySelectIndex(change_type)
	local cur_record_num = #self.record_diy_data
	if IsEmptyTable(self.record_diy_data) then
		return
	end

	if change_type == RoleDiyAppearanceWGData.CHANGE_DIY_INDEX.ADD then
		local index = math.min(10, self.cur_diy_select_index + 1)
		self.cur_diy_select_index = math.min(cur_record_num, index)  
	elseif change_type == RoleDiyAppearanceWGData.CHANGE_DIY_INDEX.MINUS then
		self.cur_diy_select_index = math.max(1, self.cur_diy_select_index - 1)  
	end
end

function RoleDiyAppearanceWGData:GetDefaultPresetDiyCfgBySexAndProf(sex, prof)
	local cfg = ((self.preset_diy_cfg[sex] or {})[prof] or {})[1]
	local default_diy_data = {}
	if not cfg then
		return default_diy_data
	end

	local alpha_cfg = RoleDiyAppearanceWGData.Instance:GetAlphaCfg(sex, prof, cfg.face_id)
	if not alpha_cfg then
		return default_diy_data
	end

	local eye_shadow_color = UtilU3d.ConvertHexToColor(cfg.eye_shadow_color) 
	local cur_eye_shadow_color = {r = math.floor(eye_shadow_color.r * 255), g = math.floor(eye_shadow_color.g * 255), b = math.floor(eye_shadow_color.b * 255), a = math.floor(cfg.eye_shadow_alpha or 0)}
	
	local pupil_color = UtilU3d.ConvertHexToColor(cfg.pupil_color) 
	local cur_pupil_color = {r = math.floor(pupil_color.r * 255), g = math.floor(pupil_color.g * 255), b = math.floor(pupil_color.b * 255), a = math.floor(cfg.pupil_color_alpha or 0)}
	
	local mouth_color = UtilU3d.ConvertHexToColor(cfg.mouth_color) 
	local cur_mouth_color = {r = math.floor(mouth_color.r * 255), g = math.floor(mouth_color.g * 255), b = math.floor(mouth_color.b * 255), a = math.floor(cfg.mouth_alpha or 0)}
	
	local hair_color = UtilU3d.ConvertHexToColor(cfg.hair_color) 
	local cur_hair_color = {r = math.floor(hair_color.r * 255), g = math.floor(hair_color.g * 255), b = math.floor(hair_color.b * 255), a = math.floor(alpha_cfg.hair_alpha or 0)}

	default_diy_data.eye_size = cfg.eye_size
	default_diy_data.eye_position = cfg.eye_pos
	default_diy_data.eye_shadow_color = cur_eye_shadow_color

	default_diy_data.left_pupil_type = cfg.pupil_type
	default_diy_data.left_pupil_size = cfg.pupil_size
	default_diy_data.left_pupil_color = cur_pupil_color

	default_diy_data.right_pupil_type = cfg.pupil_type
	default_diy_data.right_pupil_size = cfg.pupil_size
	default_diy_data.right_pupil_color = cur_pupil_color

	default_diy_data.mouth_size = cfg.mouth_size
	default_diy_data.mouth_position = cfg.mouth_pos
	default_diy_data.mouth_color = cur_mouth_color

	default_diy_data.face_decal_id = cfg.face_decal_id
	default_diy_data.hair_color = cur_hair_color
	default_diy_data.preset_seq = cfg.preset_seq
	return default_diy_data
end

----------------------------------------------易容-------------------------
function RoleDiyAppearanceWGData:GetSeqCfgBySexProf(sex, prof)
	return (self.prof_cfg[sex] or {})[prof]
end

function RoleDiyAppearanceWGData:GetPayCfgByTypeSeq(type, seq)
	return (self.pay_type_cfg[type] or {})[seq]
end

function RoleDiyAppearanceWGData:IsPayTypeConsumeItemid(item_id)
	return self.pay_type_consume_cfg[item_id]
end
