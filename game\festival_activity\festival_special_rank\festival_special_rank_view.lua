require("game/festival_activity/festival_special_rank/festival_special_rank_wg_data")
require("game/festival_activity/festival_special_rank/festival_special_rank_item")
require("game/festival_activity/festival_special_rank/festival_special_rank_gold_render")
require("game/festival_activity/festival_special_rank/festival_special_rank_silver_ticket_render")


function FestivalActivityView:InitSPRank()
end

function FestivalActivityView:LoadCallBackSPRank()
    --self.sp_rank_first_item_list = AsyncListView.New(FestivalSPRankItem, self.node_list.sp_rank_item_list)
    self.sp_gold_rank_list = AsyncListView.New(FestivalSPRankGoldRender, self.node_list.sp_gold_list)
    self.sp_gold_rank_list:SetCreateCellCallBack(BindTool.Bind(self.OnGoldRankListCreateCell, self))
    --self.act_render = OperationActRender.New(self.node_list.special_rank_model)

    self:SetSpecialRankImg()
end

function FestivalActivityView:SetSpecialRankImg()
    local fa_rank_big_bg_bundle, fa_rank_big_bg_asset = ResPath.GetFestivalRawImages("xslc")
 	self.node_list["fa_rank_big_bg"].raw_image:LoadSprite(fa_rank_big_bg_bundle, fa_rank_big_bg_asset, function ()
 		self.node_list["fa_rank_big_bg"].raw_image:SetNativeSize()
  	end)

    local sp_list_bg_bundle, sp_list_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_di1")
 	self.node_list["sp_list_bg"].image:LoadSprite(sp_list_bg_bundle, sp_list_bg_asset)

    local special_rank_name_bg_bundle, special_rank_name_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_xfph_bs")
 	self.node_list["special_rank_name_bg"].image:LoadSprite(special_rank_name_bg_bundle, special_rank_name_bg_asset)

    local gold_rank_bg_img_bundle, gold_rank_bg_img_asset = ResPath.GetFestivalActImages("a2_jrkh_xfph_wdpm")
 	self.node_list["sp_my_gold_rank_bg"].image:LoadSprite(gold_rank_bg_img_bundle, gold_rank_bg_img_asset)
end

function FestivalActivityView:OnGoldRankListCreateCell(cell)
    cell:SetParentScrollRect(self.node_list.sp_gold_list.scroll_rect)
end


function FestivalActivityView:ReleaseCallBackSPRank()
    if self.sp_rank_tab_button_list then
        for i, v in ipairs(self.sp_rank_tab_button_list) do
            v:DeleteMe()
        end
        self.sp_rank_tab_button_list = nil
    end
    -- if self.sp_rank_first_item_list then
    --     self.sp_rank_first_item_list:DeleteMe()
    --     self.sp_rank_first_item_list = nil
    -- end
    if self.role_model then
        self.role_model:DeleteMe()
        self.role_model = nil
    end

    if self.sp_gold_rank_list then
        self.sp_gold_rank_list:DeleteMe()
        self.sp_gold_rank_list = nil
    end
    -- if self.act_render then
    --     self.act_render:DeleteMe()
    --     self.act_render = nil
    -- end
    self.cur_special_model_id = nil
end

function FestivalActivityView:OpenCallBackSPRank()
   
end

function FestivalActivityView:ShowIndexCallBackSPRank()
    FestivalSpecialRankWGData.Instance:CacheTodayIsReminded()
    RemindManager.Instance:Fire(RemindName.FestivalActivity_SPRank)
    FestivalSpecialRankWGCtrl.Instance:RequestRankInfo()
    local cfg = FestivalSpecialRankWGData.Instance:GetGoldViewCfg()
    if cfg and cfg.activity_des then
        self:SetRuleInfo(cfg.activity_des, Language.FestivalSPRank.RuleTipTitle)
    end
end

function FestivalActivityView:OnFlushSPRank(param_t)
    self:FlushSubIndexSPRank()

    local cfg = FestivalSpecialRankWGData.Instance:GetGoldConfigParamCfg()
    
    if cfg then
        self:SetOutsideRuleTips(string.format(Language.FestivalSPRank.RuleTipOutDesc[1], cfg.rank_limit))
    end
   

    if FestivalSpecialRankWGData.Instance:GetIsEnd() then
        self:SetActRemainTime(TabIndex.festival_activity_2269)
    end

    -- --外部传入切换页签信息
    -- if param_t and param_t.all and param_t.all.rank_type then
    --     self:ChangeSubIndexSPRank(param_t.all.rank_type)
    -- end
end

function FestivalActivityView:FlushSubIndexSPRank()
    self:FlushGoldSPRank()
end

function FestivalActivityView:FlushGoldSPRank()
    self.node_list.sp_rank_gold_root:SetActive(true)
    --策划说右边从第二个开始。
    local data_list = FestivalSpecialRankWGData.Instance:GetGoldDataList()
    -- local show_data_list = {}
    -- for i, v in ipairs(data_list) do    
    --     table.insert(show_data_list, v)
    -- end

    local show_data_list = FestivalSpecialRankWGData.Instance:GetSpecialRankReward() --拿这个数据
    self.sp_gold_rank_list:SetDataList(show_data_list)
    self.node_list.rank_left_container_root:SetActive(true)
    
    --榜首数据
    local first_data = data_list[1]
    --local is_end = FestivalSpecialRankWGData.Instance:GetIsEnd()
    if not IsEmptyTable(first_data) then
        if first_data.is_empty_value then
            --虚位以待
            local text_color = FestivalActivityWGData.Instance:GetRankTextColor()
            self.node_list.sp_rank_first_name.text.text = string.format(Language.FestivalSPRank.XuWeiYiDai, text_color)
            self.node_list.special_virtual_position:SetActive(true)
            --self.node_list.sp_rank_first_text.text.text = string.format(Language.FestivalSPRank.ConsumeGold, 0)
            --self.node_list.sp_rank_first_text.text.text = string.format(Language.FestivalSPRank.NeedConsumeGold, first_data.reach_value)
        else
            local text_color = FestivalActivityWGData.Instance:GetRankTextColor()
            self.node_list.special_virtual_position:SetActive(false)
            -- if is_end then
            --     self.node_list.sp_rank_first_name.text.text = first_data.name
            -- else
            --     self.node_list.sp_rank_first_name.text.text = ToColorStr(Language.FestivalSPRank.BaoMi, "#f9e4b2")
            -- end
            self.node_list.sp_rank_first_name.text.text = string.format(Language.FestivalSPRank.RankFirstName, text_color, first_data.name)
            --消费仙玉：%s
            --self.node_list.sp_rank_first_text.text.text = string.format(Language.FestivalSPRank.ConsumeGold, first_data.rank_value)
        end

        -- if first_data then
        --     --左侧展示格子
        --     self.sp_rank_first_item_list:SetDataList(first_data.item_list)
        -- end
    end

    local my_rank, my_rank_value, rank_max_count, is_end = FestivalSpecialRankWGData.Instance:GetGoldRankInfo()
    if my_rank <= 0 then --未上榜584B38FF  ED6127FF
        self.node_list.sp_rank_gold_my_rank_text.text.text = Language.FestivalSPRank.RankZhiHou
    elseif my_rank > rank_max_count then --多少名以外
        self.node_list.sp_rank_gold_my_rank_text.text.text = Language.FestivalSPRank.OutRank
    elseif my_rank <= rank_max_count then --多少名以内，显示具体数量
        self.node_list.sp_rank_gold_my_rank_text.text.text = string.format(Language.FestivalSPRank.MyRank, my_rank)
    end

    self.node_list.sp_rank_gold_my_rank_value.text.text = my_rank_value


    local flush_fun = function (protocol)
        if not self.node_list or not self.node_list.special_rank_model then
            return
        end
        --设置模型
        if nil == self.role_model then
            self.role_model = RoleModel.New()
            local display_data = {
                parent_node = self.node_list["special_rank_model"],
                camera_type = MODEL_CAMERA_TYPE.BASE,
                -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
                rt_scale_type = ModelRTSCaleType.M,
                can_drag = true,
            }
            
            self.role_model:SetRenderTexUI3DModel(display_data)
            -- self.role_model:SetUI3DModel(self.node_list.special_rank_model.transform, self.node_list.special_rank_model.event_trigger_listener,
            --                     1, false, MODEL_CAMERA_TYPE.BASE)
        end

        if self.role_model then
            local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true, ignore_shouhuan = true, ignore_tail = true, ignore_waist = true}
            self.role_model:SetModelResInfo(protocol, ignore_table)
        end

    end
    BrowseWGCtrl.Instance:BrowRoelInfo(first_data.userid,flush_fun)

    -- --模型
    -- local param_cfg = FestivalSpecialRankWGData.Instance:GetGoldViewCfg()
    -- if param_cfg then
    --     local data = {}
    --     data.item_id = param_cfg.model_id
    --     data.render_type = param_cfg.render_type
    --     if param_cfg.render_string_param1 and param_cfg.render_string_param1 ~= ""
    --             and param_cfg.render_string_param2 and param_cfg.render_string_param2 ~= "" then
    --         data.bundle_name = param_cfg.render_string_param1
    --         data.asset_name = param_cfg.render_string_param2
    --     end

    --     if self.cur_special_model_id == data.item_id then
    --         return
    --     end
    --     self.cur_special_model_id = data.item_id
    --     self.act_render:SetData(data)

    --     -- if data.render_type == OARenderType.RoleModel then
    --     --     -- local item_cfg = ItemWGData.Instance:GetItemConfig(param_cfg.model_id)
    --     --     -- if item_cfg then
    --     --     --     self.node_list.special_rank_item_name.text.text = item_cfg.name
    --     --     -- end
    --     -- else
    --     --     --self.node_list.special_rank_item_name.text.text = param_cfg.show_name
    --     --     self.node_list.special_rank_name_bg:SetActive(param_cfg.show_name and param_cfg.show_name ~= "")
    --     -- end
    -- end
end


