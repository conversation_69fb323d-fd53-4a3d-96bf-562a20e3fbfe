
MultiMountObj = MultiMountObj or BaseClass(Character)

function MultiMountObj:__init(multi_mount_vo)
	self.obj_type = SceneObjType.MultiMountObj
	self:SetObjId(multi_mount_vo.obj_id)
	self.vo = multi_mount_vo

	self.mount_head_model = DrawObj.New()
end

function MultiMountObj:__delete()

end

function MultiMountObj:LoadInfoFromVo()
	self:SetMoveSpeed(self.vo.move_speed)
end

function MultiMountObj:InitAnimation()
	self:InitDirNumber()

	-- 延迟初始化动画，防止创建需要屏蔽的动画
	self.state_machine:SetIsChangeing(true)
	self:ChangeToCommonState()
	self.delay_func_list[-1] = {func = function() self.state_machine:SetIsChangeing(false) end, time = 0}
end

function MultiMountObj:RefreshAnimation()
	local dir_num, is_flip_x = self:GetResDirNumAndFlipFlag()
	local mount_info = Role.GetMountResInfo(self.action_name, self.vo.mount_res_id, dir_num)

	self.model:ChangeLayerResFrameAnim(GRQ_SCENE_OBJ, InnerLayerType.Mount, mount_info.mount_path, mount_info.mount_name, 
		is_flip_x, self.delay_per_unit, nil, self.loops, self.is_pause_last_frame, 0, -100)

	self.mount_head_model:ChangeLayerResFrameAnim(GRQ_SCENE_OBJ, InnerLayerType.MountHead, mount_info.mount_up_path, mount_info.mount_up_name, 
		is_flip_x, self.delay_per_unit, nil, self.loops, self.is_pause_last_frame, 0, 100)

	self.model:SetHeightOffset(mount_info.height_offset)
	self.mount_head_model:SetHeightOffset(mount_info.height_offset)
end

function MultiMountObj:CanClick()
	return false
end

function MultiMountObj:IsCharacter()
	return false
end

function MultiMountObj:UpdateModelPos()
	-- todo:offset
	self.model:SetPos(self.real_pos.x, self.real_pos.y + 100)
	self.mount_head_model:SetPos(self.real_pos.x, self.real_pos.y - 100)

	local partner_obj = self.parent_scene:GetObjectByObjId(self.vo.partner_obj_id)
	if nil ~= partner_obj and partner_obj:IsRole() and partner_obj:GetRoleId() == self.vo.partner_role_id then
		partner_obj:SetRealPos(self.real_pos.x, self.real_pos.y)
	end
end

function MultiMountObj:SetDirNumber(dir_number)
	local old_dir_number = self.dir_number
	Character.SetDirNumber(self, dir_number)
	if old_dir_number ~= self.dir_number then
		self:RefreshAnimation()
	end

	local partner_obj = self.parent_scene:GetObjectByObjId(self.vo.partner_obj_id)
	if nil ~= partner_obj and partner_obj:IsRole() and partner_obj:GetRoleId() == self.vo.partner_role_id then
		local old_dir_number = partner_obj:GetDirNumber()
		partner_obj:SetDirNumber(dir_number)
		if old_dir_number ~= partner_obj:GetDirNumber() then
			partner_obj:RefreshAnimation()
		end
	end
end
