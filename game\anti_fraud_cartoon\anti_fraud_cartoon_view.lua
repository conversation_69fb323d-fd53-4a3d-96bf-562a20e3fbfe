-- 防骗漫画
AntiFraudCartoonView = AntiFraudCartoonView or BaseClass(SafeBaseView)
local MAX_TIME = 5
function AntiFraudCartoonView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)

	self:AddViewResource(0, "uis/view/anti_fraud_cartoon_prefab", "layout_anti_fraud_cartoon")
end

function AntiFraudCartoonView:__delete()

end

function AntiFraudCartoonView:OpenCallBack()
end

function AntiFraudCartoonView:CloseCallBack()
end

function AntiFraudCartoonView:SetData()
end

function AntiFraudCartoonView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["fetch_btn"], BindTool.Bind(self.OnClickFetch, self))
	XUI.AddClickEventListener(self.node_list["right_arrow"], BindTool.Bind(self.OnClickRightArrow, self))
	XUI.AddClickEventListener(self.node_list["left_arrow"], BindTool.Bind(self.OnClickLeftArrow, self))

	self.cartoon_list = self.node_list["cartoon_list"]
	self.page_list = {}
    local list_delegate = self.cartoon_list.page_simple_delegate
    list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
    list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshCell, self)
    self.cartoon_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnValueChanged, self))
    self.select_page = 1
    self:JumpToPageIndex(self.select_page)

    self.item_cell = ItemCell.New(self.node_list["reward_item"])
	
    self.node_list["right_arrow"].transform:DOLocalMoveX(self.node_list["right_arrow"].transform.localPosition.x + 30, 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo)
    self.node_list["left_arrow"].transform:DOLocalMoveX(self.node_list["left_arrow"].transform.localPosition.x - 30, 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo)

	-- 绑定红点回调
	self.remind_callback = BindTool.Bind(self.FlushFetchRemind, self)
	RemindManager.Instance:Bind(self.remind_callback, RemindName.AntiFraudCartoonRemind)

	self.open_view_time = TimeWGCtrl.Instance:GetServerTime()
	CountDownManager.Instance:AddCountDown("AntiFraudCartoonView", BindTool.Bind(self.FlushFetchBtn, self), BindTool.Bind(self.FlushFetchBtn, self), nil, MAX_TIME + 1, 1)
end

function AntiFraudCartoonView:ReleaseCallBack()
	self.cartoon_list = nil
	self.max_page = nil

	if self.item_cell then
		self.item_cell:DeleteMe()
	end	

	if self.page_list then
		for i,v in ipairs(self.page_list) do
			v:DeleteMe()
		end
	end
	self.page_list = nil

	RemindManager.Instance:UnBind(self.remind_callback)

	CountDownManager.Instance:RemoveCountDown("AntiFraudCartoonView")
end

function AntiFraudCartoonView:OnFlush(param_t)
	self:FlushAllView()
end

function AntiFraudCartoonView:FlushAllView()
	self.node_list["title_desc"].text.text = string.format(Language.AntiFraudCartoon.TitleDesc, self.select_page, self:GetNumberOfCells())

	local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	self.node_list["reward_desc"].text.text = string.format(Language.AntiFraudCartoon.RewardDesc, other_cfg.cartoon_item.num)

	self.item_cell:SetData({item_id = other_cfg.cartoon_item.item_id})
	self.item_cell:SetQualityIconVisible(false)
	self.item_cell:SetCellBgEnabled(false)
	self.item_cell:SetEffectRootEnable(false)

    self.cartoon_list.page_view:Reload()
    if self.max_page == nil then
	    self.cartoon_list.page_view:JumpToIndex(0)
		self.max_page = self.max_page or 1
	end

	self.node_list["right_arrow"]:SetActive(self.select_page < self:GetNumberOfCells())
	self.node_list["left_arrow"]:SetActive(self.select_page > 1)

	self:FlushFetchBtn()
end

function AntiFraudCartoonView:FlushFetchBtn()
	self.node_list["fetch_btn"]:SetActive(not AntiFraudCartoonWGData.Instance:GetIsFetched()) 		-- 领取按钮 
	self.node_list["fetched_label"]:SetActive(AntiFraudCartoonWGData.Instance:GetIsFetched()) 		-- 已领取标签
	XUI.SetGraphicGrey(self.node_list["fetch_btn"], not self:CanFetch()) 							-- 置灰
	self:FlushFetchRemind() 																		-- 红点

	-- 按钮文字
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	local delta_time = cur_time - self.open_view_time
	if delta_time < MAX_TIME then
		self.node_list["fetch_btn_text"].text.text = string.format(Language.AntiFraudCartoon.FetchBtnText1, math.floor(MAX_TIME - delta_time))
	elseif self.max_page < self:GetNumberOfCells() then
		self.node_list["fetch_btn_text"].text.text = string.format(Language.AntiFraudCartoon.FetchBtnText2, self.max_page, self:GetNumberOfCells())
	else
		self.node_list["fetch_btn_text"].text.text = Language.AntiFraudCartoon.FetchBtnText3
	end
end

function AntiFraudCartoonView:FlushFetchRemind()
	self.node_list["fetch_btn_remind"]:SetActive(RemindManager.Instance:GetRemind(RemindName.AntiFraudCartoonRemind) > 0 and self:CanFetch()) -- 可领取红点
end

function AntiFraudCartoonView:GetNumberOfCells()
	return 2
end

function AntiFraudCartoonView:RefreshCell(cell_index, cell)
	local page_cell = self.page_list[cell]

	if nil == page_cell then
		page_cell = SingleCatroonPage.New(cell.gameObject)
		self.page_list[cell] = page_cell
	end

	page_cell:SetIndex(cell_index + 1)
	page_cell:Flush()
end

function AntiFraudCartoonView:OnValueChanged(progress_vec2)
	local index = self.cartoon_list.page_view.ActiveCellsMiddleIndex
    if index < 0 or index > self:GetNumberOfCells() - 1 then
        return
    end

    local page = index + 1
    if self.select_page ~= page then
        self.select_page = page
        if self.max_page then
	        if page > self.max_page then
	        	self.max_page = page
	        end
	    end
		self:FlushAllView()
    end
end

function AntiFraudCartoonView:CanFetch()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	return self.max_page >= self:GetNumberOfCells() and server_time - self.open_view_time >= MAX_TIME
end

-- 点击领取
function AntiFraudCartoonView:OnClickFetch()
	if self:CanFetch() then
		AntiFraudCartoonWGCtrl.Instance:SendFetch()
		self:Close()
	end
end

function AntiFraudCartoonView:OnClickLeftArrow()
	local select_page = math.max(self.select_page - 1, 1)
    self:JumpToPageIndex(select_page)
end

function AntiFraudCartoonView:OnClickRightArrow()
	local select_page = math.min(self.select_page + 1, self:GetNumberOfCells())
    self:JumpToPageIndex(select_page)
end

function AntiFraudCartoonView:JumpToPageIndex(index)
    self.cartoon_list.page_view:JumpToIndex(index - 1, 0, 5)
end
--------------------单页四格漫画----------------------------
SingleCatroonPage = SingleCatroonPage or BaseClass(BaseRender)
function SingleCatroonPage:OnFlush()
	for i = 1, 4 do
		self.node_list["square_" .. i].raw_image:LoadSpriteAsync(ResPath.GetF2RawImagesPNG("anti_fraud_cartoon_" .. self.index .. "_" .. i))
	end
end