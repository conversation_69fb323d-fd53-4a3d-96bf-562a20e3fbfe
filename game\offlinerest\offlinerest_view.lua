OfflineRestView = OfflineRestView or BaseClass(SafeBaseView)
local vip_level_limit = 6 --vip等级限制

function OfflineRestView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_offlinerest")
end

function OfflineRestView:__delete()
end

function OfflineRestView:OpenCallBack(index)
	if self.quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.quest)
		self.quest = nil
	end

	self.quest = GlobalTimerQuest:AddRunQuest(function() OfflineRestWGCtrl.Instance:SendGetSitRewardInfo() end, 60)
end

function OfflineRestView:ReleaseCallBack()
	if self.grid_cell then
		self.grid_cell:DeleteMe()
		self.grid_cell = nil
	end

	if self.item_list ~= nil then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	if self.quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.quest)
		self.quest = nil
	end
	-- for k,v in pairs(self.item_cell_list) do
	-- 	v:DeleteMe()
	-- end
	-- self.item_cell_list = nil
end

function OfflineRestView:CloseCallBack()
	if self.quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.quest)
		self.quest = nil
	end

	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_OFFLINE_FETCH_REWARD, OfflineRestWGData.Instance:GetRewardNotifyReason())
	MainuiWGCtrl.Instance:OpenNextView(NEED_OPEN_TIPS_TYPE.OFF_LINEREST_TYPE)

	MainuiWGCtrl.Instance:FlushSitState()

	--离线经验找回
	OfflineRestWGCtrl.Instance:OpenOfflineZhaoHuiView()
end

function OfflineRestView:SetIsOnlineOpen(value)
	self.is_online_open = value
end

function OfflineRestView:LoadCallBack()
	--self:FlushTime()

	self.item_list = AsyncListView.New(OfflineRestCellRender, self.node_list.ph_item_list)
	self:SetSecondView()
	--XUI.AddClickEventListener(self.node_list.btn_setting, BindTool.Bind(self.OnClickSettingCallBack, self))  挂机设置屏蔽
	XUI.AddClickEventListener(self.node_list.btn_return, BindTool.Bind(self.OnClickReturnCallBack, self))
	--XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind(self.OnClickAddTime, self))
	XUI.AddClickEventListener(self.node_list.btn_look, BindTool.Bind(self.OnClickLookExp, self))
	XUI.AddClickEventListener(self.node_list.btn_topoolexp, BindTool.Bind(self.ToVipZeroBuy, self))
	XUI.AddClickEventListener(self.node_list.btn_toget, BindTool.Bind(self.ToExpPool, self))
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.Close, self))

end

function OfflineRestView:FlushTime()
	local time = OfflineRestWGData.Instance.pass_offline_rest_time or 0
	local str = TimeUtil.Format2TableDHM2(time)
	local show_str
	if str.day > 0 then
		show_str = string.format(Language.OfflineRest.Title1[1],str.day,str.hour,str.min)
	elseif str.hour > 0 then
		show_str = string.format(Language.OfflineRest.Title1[2],str.hour,str.min)
	else
		show_str = string.format(Language.OfflineRest.Title1[3],str.min)
	end

	self.node_list.offline_time.text.text = show_str
end

function OfflineRestView:ShowIndexCallBack()
end

function OfflineRestView:OnFlush()
	local off_data = OfflineRestWGData.Instance
	self:FlushTime()

	--self.node_list["bg_image1"]:SetActive(not self.is_online_open)
	--self.node_list["bg_image2"]:SetActive(self.is_online_open)
	self.node_list["offline_time_title"].text.text = self.is_online_open and Language.OfflineRest.SitlineExpTitle or Language.OfflineRest.OfflineExpTitle
	local remain_offline_rest_time = off_data.remain_offline_rest_time

	local old_level = off_data.old_level
	local new_level = off_data.new_level
	local add_exp =  off_data.add_exp

	-- if self.is_online_open then
	-- 	local role_vo = RoleWGData.Instance:GetRoleVo()
	-- 	local cur_exp = RoleWGData.Instance.GetRoleExpCfgByLv(role_vo.level).exp
	-- 	if cur_exp ~= nil and (role_vo.exp + add_exp) >= cur_exp then
	-- 		new_level = old_level + 1
	-- 	end
	-- end

	if old_level ~= new_level and new_level > 0 then
		-- self.node_list["lbl_cur_level"]:SetActive(true)
		self.node_list["lbl_level_img"]:SetActive(true)
		self.node_list["root_next_level"]:SetActive(true)
		local level_str = RoleWGData.GetLevelStringImg2(old_level)
		self.node_list["lbl_old_level"].text.text = level_str
		
		level_str = RoleWGData.GetLevelStringImg2(new_level)
		self.node_list["lbl_cur_level"].text.text = level_str
	else
		self.node_list["lbl_level_img"]:SetActive(false)
		self.node_list["root_next_level"]:SetActive(false)
		local level_str = RoleWGData.GetLevelStringImg2(old_level)
		self.node_list["lbl_old_level"].text.text = level_str
	end

	self.node_list.lbl_time.text.text = Language.OfflineRest.Min_Rate

	local other_cfg = OfflineRestWGData.Instance:GetOtherCfg()
    local hang_max_offlie_time = other_cfg.hang_max_offlie_time or 0
	local time_tab = TimeUtil.Format2TableDHMS(hang_max_offlie_time)
	local hour = time_tab.hour
	self.node_list.lbl_role_exp.text.text =   string.format(Language.OfflineRest.OfflineMaxTime, CommonDataManager.ConverExp(add_exp), hour) 
	local exp_xiaolv = 0
	local total_xiaolv = 0
    -- 经验效率
    local role_level = RoleWGData.Instance:GetAttr("level")
    --exp_xiaolv = off_data:GetLiLianExpXiaoLv()
    exp_xiaolv = ExpAdditionWGData.Instance:GetOffLineStdExp()
    total_xiaolv = total_xiaolv + exp_xiaolv
    -- 副本进度
	local fb_level = ExperienceFbWGData.Instance:GetCurrExpWestLevel()
    local wave = ExperienceFbWGData.Instance:GetCurrExpWestWave()
	local pass_wave = wave - 1

	if pass_wave <= 0 and fb_level > 1 then
		local list = ExperienceFbWGData.Instance:GetWaveListByLevel(fb_level)
		pass_wave = #list
		fb_level = fb_level - 1
	end
	
    local wave_cfg = ExperienceFbWGData.Instance:GetLevelWaveCfgByLevelWave(fb_level, pass_wave)

    exp_xiaolv = wave_cfg and wave_cfg.std_exp or 0
    total_xiaolv = total_xiaolv + exp_xiaolv

    -- 修为经验
	local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
    exp_xiaolv = cur_stage_cfg and cur_stage_cfg.reset_add_exp or 0
	total_xiaolv = total_xiaolv + exp_xiaolv
    -- 总效率
	local cur_add_exp_per = off_data.total_cur_add_exp_per
	total_xiaolv = math.floor(total_xiaolv * (1 + (cur_add_exp_per * 0.0001)))
	local exp_per = RoleWGData.Instance:GetRoleExpCorrection()
	total_xiaolv = total_xiaolv * exp_per
	
	local conver_str = CommonDataManager.NotConverExpExtend(total_xiaolv)
	self.node_list.lbl_pet_exp.text.text = string.format(Language.Skill.NumThree, conver_str)

	local exp = AssignmentWGData.Instance:GetTaskExp()
    local max_exp = AssignmentWGData.Instance:GetMaxTaskExp()
    self.node_list.lbl_lilian_num.text.text = exp .. "/" .. max_exp
	self:CreateGridCells()
end

--挂机设置屏蔽
-- function OfflineRestView:OnClickSettingCallBack()
-- 	FunOpen.Instance:OpenViewByName(GuideModuleName.Setting, "setting_guaji")
-- end

function OfflineRestView:OnClickReturnCallBack()
	local off_data = OfflineRestWGData.Instance
	local add_exp = off_data and off_data.add_exp or 0
	if add_exp <= 0 then
		self:Close()
		return
	end

	local notify = OfflineRestWGData.Instance:GetRewardNotifyReason()
	if OFFLINE_REST_INFO_NOTIFY_REASON.ONLINE_REWARD == notify then
		RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_OFFLINE_FETCH_REWARD, OfflineRestWGData.Instance:GetRewardNotifyReason())
		MainuiWGCtrl.Instance:OpenNextView(NEED_OPEN_TIPS_TYPE.OFF_LINEREST_TYPE)
		OfflineRestWGCtrl.Instance:OpenOfflinerestExpEffectView()
		self:Close()
	else
		self:Close()
	end
end

function OfflineRestView:GetTimeStr(time)
	local hour = math.floor(time / 3600)
	local minute = math.floor((time / 60) % 60)
	local second = math.floor(time % 60)
	local time_str = ""
	if hour > 0 or "" ~= time_str then
		time_str = time_str .. hour .. Language.Common.TimeList.h
	end

	if minute > 0 or "" ~= time_str then
		time_str = time_str .. minute .. Language.Common.TimeList.min
	end
	--if second >= 0 or "" ~= time_str then
		--time_str = time_str .. second .. Language.Common.TimeList.s
	--end
	time_str = string.format(Language.OfflineRest.OfflineRestTime2, time_str)
	return time_str
end

function OfflineRestView:OnClickLookExp()
	-- SkillWGCtrl.Instance:SendOfflineExpEfficiencyReq()
	RoleWGCtrl.Instance:SetExpAddititonData("offline_view")
	-- ViewManager.Instance:Open(GuideModuleName.ExpAdditionView)
end

function OfflineRestView:ToExpPool()
	ViewManager.Instance:Open(GuideModuleName.ExpPoolView)
	self:Close()
end

function OfflineRestView:ToVipZeroBuy()
	RechargeWGCtrl.Instance:Open(TabIndex.recharge_zerobuy)
	self:Close()
end

-- function OfflineRestView:OnClickAddTime()
-- 	if OfflineRestWGData.Instance:GetRemainOfflineRestTime() >= OfflineRestWGData.OfflineTotalTime then
-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.OfflineRest.OfflineTimeFull)
-- 		return
-- 	end
-- 	local other = WelfareWGData.Instance:GetOther()
-- 	local num1 = ItemWGData.Instance:GetItemNumInBagById(other.item_id)
-- 	local num2 = ItemWGData.Instance:GetItemNumInBagById(22536)
-- 	if num2 == 1 then
-- 		local index = ItemWGData.Instance:GetItemIndex(22536)
-- 		BagWGCtrl.Instance:SendUseItem(index, 1, 0, 0)
-- 		return
-- 	elseif num2 > 1 then
-- 		OfflineRestWGCtrl.Instance:OpenUserOfflineView(22536)
-- 		return
-- 	end

-- 	if num1 == 1 then
-- 		local index = ItemWGData.Instance:GetItemIndex(other.item_id)
-- 		BagWGCtrl.Instance:SendUseItem(index, 1, 0, 0)
-- 		return
-- 	elseif num1 > 1 then
-- 		OfflineRestWGCtrl.Instance:OpenUserOfflineView(other.item_id)
-- 		return
-- 	end
-- 	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other.item_id})
-- end

function OfflineRestView:CreateGridCells()
	local data_list = OfflineRestWGData.Instance:GetOfflineGuajiEquip()
	self.node_list.no_item_text:SetActive(false)
	if #data_list > 0 then
		self.node_list["get_text"].text.text = Language.OfflineRest.GetTextBtn
		self.node_list.get_red:SetActive(true)
	else
		self.node_list["get_text"].text.text = Language.OfflineRest.ConfirmTextBtn
		self.node_list.get_red:SetActive(false)
		self.node_list.no_item_text:SetActive(true)
	end

	--self.node_list.none_equip:SetActive(not data_list or IsEmptyTable(data_list))

	if self.item_list ~= nil then
		self.item_list:SetDataList(data_list)
	end
end

GuaJiExpView = GuaJiExpView or BaseClass(SafeBaseView)
function GuaJiExpView:__init()
	self:AddViewResource(0, "uis/view/team_exp_fb_prefab", "layout_exp_xiaolv")
	self.view_layer = UiLayer.MainUI
end

function GuaJiExpView:LoadCallBack()
	self.node_list.layout_exp_xiaolv_root.rect.anchoredPosition3D = Vector3(0, 255, 0)
	XUI.AddClickEventListener(self.node_list.additon_btn, BindTool.Bind(self.OnClickAdditonBtn,self))

	self.guaji_event = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE,function (guaji_type)
		if guaji_type ~= GuajiType.Auto then
			if not self.delay_close then
				self.delay_close = GlobalTimerQuest:AddDelayTimer(function ()
					self:Close()
					self.delay_close = nil
				end,0)
			end
		else
			if self.delay_close then
				GlobalTimerQuest:CancelQuest(self.delay_close)
				self.delay_close = nil
			end
		end
	end)
end

function GuaJiExpView:ReleaseCallBack()
	if self.guaji_event then
		GlobalEventSystem:UnBind(self.guaji_event)
		self.guaji_event = nil
	end

	if self.delay_close then
		GlobalTimerQuest:CancelQuest(self.delay_close)
	end
	self.data = nil
end

function GuaJiExpView:OnFlush()
	if self.data then
		self.node_list.jingyanxiaolv.text.text = CommonDataManager.ConverExpExtend(self.data)
	end
end

function GuaJiExpView:SetData(data)
	self.data = data
	if not self:IsOpen() then
		self:Open()
	end
	self:Flush()
end

function GuaJiExpView:OnClickAdditonBtn()
	ViewManager.Instance:Open(GuideModuleName.ExpAdditionView)
end

OfflineRestCellRender = OfflineRestCellRender or BaseClass(BaseRender)
function OfflineRestCellRender:LoadCallBack()
	if self.item_cell == nil then
		self.item_cell = ItemCell.New(self.node_list.item_node)
	end
end

function OfflineRestCellRender:__delete()
	if self.item_cell ~= nil then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function OfflineRestCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	self.item_cell:SetData(self.data)
end
