-- 自动跑任务机器人
AutoTaskRobert = AutoTaskRobert or BaseClass(Role)

AutoTaskRobertAI = {
	Task = 0,
	HangOut = 1,
}

-- 【职业改动修改】
local PROF_SKILL_ID_LIST = {
	[0] = { [1] = { 1001, 1002, 1003, 1004 },
			[2] = { 1701, 1702, 1703, 1704 },
	        [3] = { 1101, 1102, 1103, 1104 },
			[4] = { 1601, 1602, 1603, 1604 }},
	[1] = { [1] = { 1201, 1202, 1203, 1204 },
			[2] = { 1401, 1402, 1403, 1404 },
	        [3] = { 1301, 1302, 1303, 1304 },
			[4] = { 1501, 1502, 1503, 1504 }},
}

local RobertState = {
	None = 0,
	Wait = 1,
	Move = 2,
	Jump = 3,
	Fight = 4,
	Gather = 5,
	TalkNpc = 6,
	WaitMainRole = 7,
	Destory = 99,
}

function AutoTaskRobert:__init(vo)
	self.obj_type = SceneObjType.Role
	self.draw_obj:SetObjType(self.obj_type)
	self.is_can_click = false
	self.shadow_hide_when_self_hide = true
	self.followui_hide_when_self_hide = true

	self.ai = AutoTaskRobertAI.Task

	self.next_update_time = 0

	self.last_logic_pos_x = 0
	self.last_logic_pos_y = 0

	self.move_target_x = 0
	self.move_target_y = 0
	self.final_target_x = 0
	self.final_target_y = 0
	self.target_id = 0
	self.total_move_time = 0
	self.is_draw_obj_change_to_fast_speed = false

	self.path_pos_index = 1

	self.skill_list = PROF_SKILL_ID_LIST[vo.sex][vo.prof]
	self.skill_index = 1

	self.state = RobertState.None
	self.wait_time_stamp = 0

	self.gather_time_stamp = 0

	self.task_list = {}
	self.cur_task_id = -1

	self.attack_timer = nil
end

function AutoTaskRobert:__delete()
end

function AutoTaskRobert:SetAI(ai_type)
	self.ai = ai_type
end

function AutoTaskRobert:SetTaskID(task_id)
	self.cur_task_id = task_id
end

function AutoTaskRobert:Start(wait_main_role)
	self:ChangeState(SceneObjState.Stand)
	
	if wait_main_role then
		self.state = RobertState.WaitMainRole
	else
		self.state = RobertState.Wait
		self.wait_time_stamp = Status.NowTime + 0.5
	end
end

local MaxDistance = 100 * 100
function AutoTaskRobert:CheckDestory()
	if self.state == RobertState.Destory then
		return true
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local logic_pos_x, logic_pos_y = main_role:GetLogicPos()
		-- 超出主角视野
		local distance = MaxDistance
		local is_wait_main_role = self.state == RobertState.WaitMainRole
		if is_wait_main_role then
			distance = distance * 2
		end

		return GameMath.GetDistance(self.logic_pos.x, self.logic_pos.y, logic_pos_x, logic_pos_y, false) > MaxDistance
	else
		return true
	end
end

function AutoTaskRobert:DoNextOperate()
	local operate = nil
	if self.ai == AutoTaskRobertAI.Task then
		operate = self:GetNextTaskOperate()
	else
		operate = self:GetNextHangOutOperate()
	end

	if nil == operate then
		return
	end

	self.state = operate.state

	self.target_id = operate.target_id
	self.move_target_x = operate.x
	self.move_target_y = operate.y
	self.final_target_x = operate.x
	self.final_target_y = operate.y

	if operate.wait_time then
		self.wait_time_stamp = Status.NowTime + operate.wait_time
	end
	self.attack_target = nil
end

function AutoTaskRobert:GetNextTaskOperate()
	if #self.task_list > 0 then
		return table.remove(self.task_list, 1)
	else
		for i = 1, 1 do
			local cfg = TaskWGData.Instance:GetTaskConfig(self.cur_task_id)
			if cfg then
				local cur_scene_id = Scene.Instance:GetSceneId()
				if cfg.accept_npc and nil ~= cfg.accept_npc.scene then
					local npc_cfg = TaskWGData.Instance:GetNPCConfig(cfg.accept_npc.id)
					-- 随身npc
					if npc_cfg.task_can_see == 0 then
						table.insert(self.task_list, {wait_time = math.random(1, 5), state = RobertState.Wait})
					elseif cfg.accept_npc.scene == cur_scene_id then
						table.insert(self.task_list, {x = cfg.accept_npc.x + math.random(-1, 1), y = cfg.accept_npc.y + math.random(-1, 1), state = RobertState.Move})
						table.insert(self.task_list, {target_id = cfg.accept_npc.id, state = RobertState.TalkNpc})
						table.insert(self.task_list, {wait_time = math.random(1, 2), state = RobertState.Wait})
					else
						self:AddExitSceneOperate(self.task_list, cfg.accept_npc.scene)
						break
					end
				end

				-- 打怪或者采集任务
				if cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_1 or cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_2 then
					local target = cfg.target_obj and cfg.target_obj[1] or nil
					
					if target then
						if target.scene == cur_scene_id then
							local state = cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_1 and RobertState.Fight or RobertState.Gather
							table.insert(self.task_list, {x = target.x, y = target.y, state = RobertState.Move})
							table.insert(self.task_list, {target_id = target.id, state = state})
						else
							self:AddExitSceneOperate(self.task_list, target.scene)
							break
						end
					-- 个人任务召唤怪
					else
						if cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_1 then
							local monster_cfg = BossWGData.Instance:GetMonsterCfgByid(cfg.c_param1)
							-- 判断是否是任务创建怪
							if monster_cfg and monster_cfg.sub_type == MONSTER_SUB_TYPE.TASK then
								local scene_id = Scene.Instance:GetSceneId()
								local monster_task_cfg = TaskWGData.Instance:GetTaskMonsterCfg(cfg.task_id, cfg.c_param1)
								if monster_task_cfg then
									local pos = Split(monster_task_cfg.call_monster_pos, ",", true)
									table.insert(self.task_list, {x = pos[1] or 0, y = pos[2] or 0, state = RobertState.Move})
								end
							end
						end
					end
				-- 执行某种操作(例如引导之类的)
				elseif cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_7 then
					table.insert(self.task_list, {wait_time = math.random(5, 10), state = RobertState.Wait})
				-- 通关副本
				elseif cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 then
					table.insert(self.task_list, {wait_time = math.random(10, 20), state = RobertState.Wait})
				else
					table.insert(self.task_list, {wait_time = math.random(1, 2), state = RobertState.Wait})
				end

				if cfg.commit_npc and nil ~= cfg.commit_npc.scene then
					local npc_cfg = TaskWGData.Instance:GetNPCConfig(cfg.commit_npc.id)
					-- 随身npc
					if npc_cfg.task_can_see == 0 then
						table.insert(self.task_list, {wait_time = math.random(1, 5), state = RobertState.Wait})
					elseif cfg.commit_npc.scene == cur_scene_id then
						table.insert(self.task_list, {x = cfg.commit_npc.x + math.random(-1, 1), y = cfg.commit_npc.y + math.random(-1, 1), state = RobertState.Move})
						table.insert(self.task_list, {target_id = cfg.commit_npc.id, state = RobertState.TalkNpc})
						table.insert(self.task_list, {wait_time = math.random(1, 5), state = RobertState.Wait})
					else
						self:AddExitSceneOperate(self.task_list, cfg.commit_npc.scene)
						break
					end
				end
			end

			local next_cfg = TaskWGData.Instance:GetNextZhuTaskConfigById(self.cur_task_id)
			if next_cfg then
				self.cur_task_id = next_cfg.task_id
			else
				table.insert(self.task_list, {state = RobertState.Destory})
				break
			end
		end
	end

	if #self.task_list > 0 then
		return table.remove(self.task_list, 1)
	else
		print_error("No Task")
	end
end

function AutoTaskRobert:GetNextHangOutOperate()
	if math.random() >= 0.5 then
		return {wait_time = math.random(3, 20), state = RobertState.Wait}
	else
		local scene_cfg = Scene.Instance:GetSceneConfig()
		if scene_cfg and scene_cfg.npcs then
			for i = 1, 10 do
				local npc = scene_cfg.npcs[math.floor(math.random(1, #scene_cfg.npcs))]
				if npc.is_walking == 0 then
					return {x = npc.x, y = npc.y, state = RobertState.Move}
				end
			end
		end

		return {wait_time = math.random(3, 20), state = RobertState.Wait}
	end
end

function AutoTaskRobert:AddExitSceneOperate(operate_list, scene_id)
	local door = self:GetDoorPosition(scene_id)
	if door then
		table.insert(operate_list, {x = door.x, y = door.y, state = RobertState.Move})
	end
	table.insert(operate_list, {state = RobertState.Destory})
end

function AutoTaskRobert:GetDoorPosition(scene_id)
	local scene_cfg = Scene.Instance:GetSceneConfig()
	if nil == scene_cfg or nil == scene_cfg.doors then
		return nil
	end

	for k,v in pairs(scene_cfg.doors) do
		if v.target_scene_id == scene_id then
			return v
		end
	end
end

function AutoTaskRobert:Update(now_time, elapse_time)
	Role.Update(self, now_time, elapse_time)

	if self.state == RobertState.Wait then
		self:ProgressWaitState(now_time, elapse_time)
	elseif self.state == RobertState.Move then
		self:ProgressMoveState(now_time, elapse_time)
	elseif self.state == RobertState.Jump then
		self:ProgressJumpState(now_time, elapse_time)
	elseif self.state == RobertState.Fight then
		self:ProgressFightState(now_time, elapse_time)
	elseif self.state == RobertState.Gather then
		self:ProgressGatherState(now_time, elapse_time)
	elseif self.state == RobertState.TalkNpc then
		self:ProgressTalkNpcState(now_time, elapse_time)
	elseif self.state == RobertState.WaitMainRole then
		self:ProgressWaitMainRoleState(now_time, elapse_time)
	end
end

local temp_move_dir = u3d.vec3(0, 0, 0)
function AutoTaskRobert:UpdateStateMove(elapse_time)
    if self.delay_end_move_time > 0 then
        if Status.NowTime >= self.delay_end_move_time then
            self.delay_end_move_time = 0
            self:ChangeToCommonState()
        end
        return
    end

    if self.draw_obj then
        --移动状态更新
        local distance = elapse_time * self:GetMoveSpeed()
        self.move_pass_distance = self.move_pass_distance + distance

        if self.move_pass_distance >= self.move_total_distance or self.is_move_over_pos then
            self.is_move_over_pos = false
            self.is_special_move = false
            self.special_speed = 0
            self:SetRealPos(self.move_end_pos.x, self.move_end_pos.y)

            if self:MoveEnd() then
                self.move_pass_distance = 0
                self.move_total_distance = 0
                self.delay_end_move_time = Status.NowTime + 0.2
            end
        else
            local mov_dir = u3dpool.v2Mul(self.move_dir, distance, temp_move_dir)
			self:SetRealPos(self.real_pos.x + mov_dir.x, self.real_pos.y + mov_dir.y)
        end
    end
end

function AutoTaskRobert:ProgressWaitState(now_time, elapse_time)
	if now_time >= self.wait_time_stamp then
		self:DoNextOperate()
	end
end

function AutoTaskRobert:ProgressMoveState(now_time, elapse_time)
	if self.last_logic_pos_x ~= self.logic_pos.x or self.last_logic_pos_y ~= self.logic_pos.y then
		self.last_logic_pos_x = self.logic_pos.x
		self.last_logic_pos_y = self.logic_pos.y
		if self:CheckJump() then
			self.is_move = false
			self.path_pos_list = nil
			self.path_pos_index = 0
			-- -- 跳跃回去重新找到那个点的位置
			-- self.path_pos_index = self.path_pos_index - 1
			return
		end
	end

	if self.is_move then
		return
	end

	self:UpdateMount()

	if self:CheckArrival() then
		self.is_move = false

		-- 路径全部走完了则可以判断为到达目的地
		if self.path_pos_list ~= nil and self.path_pos_index >= #self.path_pos_list then
			self:DoNextOperate()
			self.path_pos_list = nil
			self.path_pos_index = 0
			return
		end
	end

	self.path_pos_index = self.path_pos_index + 1
	if nil == self.path_pos_list or self.path_pos_index > #self.path_pos_list then
		self.path_pos_list = self:GeneratePath(self.final_target_x, self.final_target_y, 3)
		self.path_pos_index = 1
	end

	if self.path_pos_list then
		self.is_move = true
		local pos = self.path_pos_list[self.path_pos_index]
		--这里重新设置一下目标位置，因为上面方法修改的目标点，结果和预存里面的额坐标对不上，导致AI一直在原地跑，但是一直不触发到达
		self.move_target_x = pos.x
		self.move_target_y = pos.y
		Role.DoMove(self, pos.x, pos.y)
	else
		-- 寻不到路
		self.state = RobertState.Destory
	end
end

function AutoTaskRobert:GeneratePath(x, y, range)
	local path_pos_list
	x, y = AStarFindWay:GetAroundVaildXY(x, y, range or 3)
	x, y = AStarFindWay:GetLineEndXY2(self.logic_pos.x, self.logic_pos.y, x, y)
	local move_x, move_y = x, y

	if not AStarFindWay:IsWayLine(self.logic_pos.x, self.logic_pos.y, x, y) then
		RoadFindWay.Instance:FindWay(self.logic_pos, u3d.vec2(x, y), function(succ, _path_pos_list)
			if not succ then
				return
			end
			path_pos_list = _path_pos_list
			if not path_pos_list or #path_pos_list == 0 then
				return
			end

			move_x = path_pos_list[1].x
			move_y = path_pos_list[1].y
		end)
	else
		move_x, move_y = AStarFindWay:GetTargetXY(self.logic_pos.x, self.logic_pos.y, move_x, move_y, range)
		path_pos_list = {{x = move_x, y = move_y}}
	end

	return path_pos_list
end

function AutoTaskRobert:MoveEnd()
	self.is_move = false
	return true
end

function AutoTaskRobert:CheckArrival()
	local distance = GameMath.GetDistance(self.move_target_x, self.move_target_y, self.logic_pos.x, self.logic_pos.y, false)
	return distance <= 25
end

function AutoTaskRobert:CheckJump()
	-- local position = self:GetLuaPosition()
	-- local x, y = GameMapHelper.WorldToLogic(position.x, position.z)

	local jumppoint_obj_list = Scene.Instance:FindJumpPoint(self.logic_pos.x, self.logic_pos.y)
	if #jumppoint_obj_list < 1 then
		return false
	end

	
	local jump_point = jumppoint_obj_list[1]
	local point_vo = jump_point.vo

	if point_vo.target_id == -1 then
		return false
	end

	self.target_jump_point = Scene.Instance:GetJumpPointVO(point_vo.target_id)

	if nil == self.target_jump_point then
		return false
	end

	self.vo.jump_factor = 1
	if point_vo.jump_speed and point_vo.jump_speed > 4 then
		point_vo.jump_speed = 4
	end
	self.vo.jump_factor = point_vo.jump_speed
	self.jump_time = point_vo.jump_time

	-- 临时修改, 让机器人过跳跃点不受限
	-- if point_vo.jump_type == JUMP_POINT_TYPE.JUMP then
	if point_vo.jump_type then
		local jump_act = point_vo.jump_act or 0
		if jump_act == 0 then
			if math.random() > 0.5 then
				jump_act = 1
			else
				jump_act = 2
			end
		end

		self.vo.jump_act = jump_act
	end
	self.jump_target_x = self.target_jump_point.x
	self.jump_target_y = self.target_jump_point.y

	self.state = RobertState.Jump

	return true
end

function AutoTaskRobert:ProgressJumpState(now_time, elapse_time)
	if self.is_jump then
		return
	end
	self.is_jump = true

	self:DoJump()
end

function AutoTaskRobert:OnJumpStart()
	self:RemoveModel(SceneObjPart.Mount)
	local x, y = self:GetLogicPos()
	local jump_speed_factor = 1
	local distance = u3d.v2Length({x = self.jump_target_x - x, y = self.jump_target_y - y}, true)
	local speed = self:GetMoveSpeed(true)
	if speed == 0 then
		speed = 0.01
	end

	local time = distance / speed --得到跳跃实际需要的时间
	if time == 0 then
		time = 0.01
	end

	local anim_time_cfg
	if self.vo.jump_act == 2 then
		anim_time_cfg = self:GetActionTimeRecord(SceneObjAnimator.Jump2)
	else
		anim_time_cfg = self:GetActionTimeRecord(SceneObjAnimator.Jump)
	end

	if anim_time_cfg then
		local anim_time = anim_time_cfg.time
		jump_speed_factor = anim_time / time * 0.95 --time / anim_time  -- 误差
	end

	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	local weapon_part = self.draw_obj:GetPart(SceneObjPart.Weapon)
	local value = 1
	-- 变身状态
	if self.vo.bianshen_param == BIANSHEN_EFEECT_APPEARANCE.APPEARANCE_DATI_XIAOTU then
		value = 2
	elseif self.vo.bianshen_param == BIANSHEN_EFEECT_APPEARANCE.APPEARANCE_DATI_XIAOZHU then
		value = 2.67
	end

	if main_part then
		main_part:SetFloat("jump_speed", value * jump_speed_factor)
	end

	if weapon_part then
		weapon_part:SetFloat("jump_speed", value * jump_speed_factor)
	end

	Role.DoMove(self, self.jump_target_x, self.jump_target_y)
end

function AutoTaskRobert:GetTotalMoveTime()
	return self.total_move_time
end

function AutoTaskRobert:GetMoveSpeed(is_jump)
	local speed = Scene.ServerSpeedToClient(self.vo.move_speed + self.special_speed)
	if self:IsJump() or is_jump then
		if self.jump_move_factor ~= nil then
			speed = self.jump_move_factor * speed
		else
			if self.vo.jump_factor then
				speed = self.vo.jump_factor * speed
			else
				speed = 1.8 * speed
			end

			speed = math.max(speed, 35)
		end
	end

	local is_low_speed = self:GetTotalMoveTime() < Config.SCENE_LOW_SPEED_TIME
	speed = is_low_speed and speed * Config.SCENE_LOW_SPEED_FACTOR or speed
	return speed
end

function AutoTaskRobert:OnJumpEnd()
	Role.OnJumpEnd(self)
	self.is_jump = false
	self.target_jump_point = Scene.Instance:GetJumpPointVO(self.target_jump_point.target_id)
	if nil == self.target_jump_point then
		self.state = RobertState.Move
	end
end

function AutoTaskRobert:ProgressFightState(now_time, elapse_time)
	if self.is_atk then
		return
	end
	self.is_atk = true

	if nil == self.attack_target then
		local monster_list = Scene.Instance:GetMonsterList()
		for k,v in pairs(monster_list) do
			if v:GetMonsterId() == self.target_id then
				self.attack_target = v
				break
			end
		end
	end

	self:DoAttack()
end

function AutoTaskRobert:DoAttack()
	self:RemoveModel(SceneObjPart.Mount)

	if self.attack_target and not self.attack_target:IsDeleted() then
		local target_pos_x, target_pos_y = self.attack_target:GetLogicPos()
		self.skill_index = self.skill_index > #self.skill_list and 1 or self.skill_index
		Role.DoAttack(self, self.skill_list[self.skill_index], target_pos_x, target_pos_y, self.attack_target:GetObjId(), self.attack_target:GetType())

		---这里加定时器是处理超时的问题，有的ai脱离的主角的范围可能就不会触发攻击结束，需要加一个超时判断，不然一直卡在空中
		local anim_name = SkillWGData.GetSkillActionStr(self.obj_type, self.skill_list[self.skill_index], self.attack_index)
        if nil ~= anim_name and anim_name ~= "" then
            local action_time_record = self:GetActionTimeRecord(anim_name)
			self:RemoveAttackDelayTime()
			self.attack_timer = GlobalTimerQuest:AddDelayTimer(function ()
				if self.is_atk then
					self:RemoveAttackDelayTime()
					self:OnAttackPlayEnd()
				end
			end, action_time_record and action_time_record.time or 3)
        end
	else
		self.attack_target = nil
		self.is_atk = false
		self.state = RobertState.Wait
		self.wait_time_stamp = Status.NowTime + 5
	end
end

function AutoTaskRobert:RemoveAttackDelayTime()
    if self.attack_timer then
        GlobalTimerQuest:CancelQuest(self.attack_timer)
        self.attack_timer = nil
    end
end

function AutoTaskRobert:OnAttackPlayEnd()
	Role.OnAttackPlayEnd(self)
	self.is_atk = false

	self.skill_index = self.skill_index + 1
	if self.skill_index > #self.skill_list then
		self.attack_target = nil
		self.state = RobertState.Wait
		self.wait_time_stamp = Status.NowTime + 2
	end
end

function AutoTaskRobert:ProgressGatherState(now_time, elapse_time)
	if now_time < self.gather_time_stamp then
		return
	end

	if self.is_gathering then
		self:SetIsGatherState(false)
		self.is_gathering = false
		self.state = RobertState.Wait
		self.wait_time_stamp = Status.NowTime + 0.5
		return
	end

	self.is_gathering = true

	local gather_obj = nil
	local gather_list = Scene.Instance:GetGatherList()
	for k,v in pairs(gather_list) do
		if v:GetGatherId() == self.target_id then
			gather_obj = v
			break
		end
	end

	if gather_obj and not gather_obj:IsDeleted() then
		local caiji_type = 0
		local gather_config = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[self.target_id]
		if gather_config and gather_config.action then
		    caiji_type = gather_config.action
		end

		self:RotaToTarget(gather_obj:GetRoot().transform.position)
		self:RemoveModel(SceneObjPart.Mount)
		self:SetIsGatherState(true, caiji_type)

		local time = math.random(200, 400) / 100
		self.gather_time_stamp = now_time + time + 0.5
	else
		self.state = RobertState.Wait
		self.wait_time_stamp = Status.NowTime + 3
	end
end

function AutoTaskRobert:ProgressTalkNpcState(now_time, elapse_time)
	local npc_obj = Scene.Instance:GetNpcByNpcId(self.target_id)
	if npc_obj and not npc_obj:IsDeleted() then
		self:RotaToTarget(npc_obj:GetRoot().transform.position)
	end

	self:DoNextOperate()
end

function AutoTaskRobert:ProgressWaitMainRoleState(now_time, elapse_time)
	if now_time < self.next_update_time then
		return
	end
	self.next_update_time = now_time + 0.5

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local logic_pos_x, logic_pos_y = main_role:GetLogicPos()
		if GameMath.GetDistance(self.logic_pos.x, self.logic_pos.y, logic_pos_x, logic_pos_y, false) <= 1000 then
			self.state = RobertState.Wait
			self.wait_time_stamp = Status.NowTime + math.random(0, 500) / 100
		end
	end
end

function AutoTaskRobert:OnEnterScene()
	Role.OnEnterScene(self)

	if self:IsRiding() and nil == self.do_mount_up_delay then
        self.do_mount_up_delay = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnMountUpEnd, self), 0.1)
    end
end

function AutoTaskRobert:CalculatePriortiy()
    return SceneAppearPriority.Low
end