local SXD_FUNNAME = {
    [0] = FunName.SXBX_xianyi,
    [1] = FunName.SXBX_FaBao,
    [2] = FunName.SXBX_ShenWu,
    [7] = FunName.SXBX_BeiShi,
}

local QICHONG_SXD_FUNNAME = {
    [0] = FunName.SXBX_Mount,
    [1] = FunName.SXBX_Pet,
}

NEWAPPEARANCE_SXD_TYPE = {
    UpGrade = 1,
    Other = 2,
}

function NewAppearanceWGData:InitAttrStoreCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("attribute_stone_auto")
    self.upgrade_stone_cfg = ListToMap(cfg.upgrade_stone, "type", "slot_idx")
    self.upgrade_stone_index_cfg = ListToMap(cfg.upgrade_stone, "seq")
    self.pet_stone_cfg = ListToMap(cfg.pet_stone, "slot_idx")
    self.mount_stone_cfg = ListToMap(cfg.mount_stone, "slot_idx")

    self.upgrade_stone_sxd_cfg = ListToMap(cfg.upgrade_stone, "shuxingdan_id")
    self.pet_stone_sxd_cfg = ListToMap(cfg.pet_stone, "shuxingdan_id")
    self.mount_stone_sxd_cfg = ListToMap(cfg.mount_stone, "shuxingdan_id")

    self.upgrade_stone_list = {}
    self.pet_stone_list = {}
	self.mount_stone_list = {}
end

function NewAppearanceWGData:DeleteAttrStoreData()
end

function NewAppearanceWGData:UpdateAttrStoreInfo(protocol)
    local upgrade_stone_list = {}
    
    for k, v in pairs(protocol.upgrade_stone_list) do
        local cfg = self:GetUpGradeStoreCfgBySeq(k)
        if not IsEmptyTable(cfg) then
            upgrade_stone_list[cfg.type] = upgrade_stone_list[cfg.type] or {}
            upgrade_stone_list[cfg.type][cfg.slot_idx] = v
        end
    end

    self.upgrade_stone_list = upgrade_stone_list
	self.pet_stone_list = protocol.pet_stone_list
	self.mount_stone_list = protocol.mount_stone_list
end

function NewAppearanceWGData:GetAdvancedSXDExtendUsedNum(type, slot)
    return (self.upgrade_stone_list[type] or {})[slot] or 0
end

function NewAppearanceWGData:GetPetSXDExtendUsedNum(slot)
    return self.pet_stone_list[slot] or 0
end

function NewAppearanceWGData:GetMountSXDExtendUsedNum(slot)
    return self.mount_stone_list[slot] or 0
end

function NewAppearanceWGData:GetUpGradeStoreCfgByType(type)
    return self.upgrade_stone_cfg[type] or {}
end

function NewAppearanceWGData:GetUpGradeStoreCfg()
    return self.upgrade_stone_cfg
end

function NewAppearanceWGData:GetUpGradeStoreCfgBySeq(seq)
    return self.upgrade_stone_index_cfg[seq] or {}
end

function NewAppearanceWGData:GetAdvancedSXDExtendlist(type)
    local sxd_cfg = self:GetUpGradeStoreCfgByType(type)
    local list = {}
    if IsEmptyTable(sxd_cfg) then
        return list
    end

    local level = self:GetAdvancedLevel(type)
    for slot = 0, #sxd_cfg do
        local cfg = sxd_cfg[slot]
        if cfg.is_show == 1 then
            local data = {cfg = cfg}
            data.is_open = cfg.open_level <= level
            data.used_num = self:GetAdvancedSXDExtendUsedNum(type, slot)
            data.had_num = ItemWGData.Instance:GetItemNumInBagById(cfg.shuxingdan_id)
            data.is_remind = data.had_num > 0 and data.is_open and data.used_num < cfg.use_limit_num
            data.ad_type = type
            table.insert(list, data)
        end
    end

    return list
end

function NewAppearanceWGData:GetAdvancedSXDExtendRemind(ad_type)
    local fun_name = self:GetAdvancedFunnameBuType(ad_type)
    if fun_name then
        if FunOpen.Instance:GetFunIsOpened(fun_name) then
            local sxd_list = self:GetAdvancedSXDExtendlist(ad_type)
            for k,v in pairs(sxd_list) do
                if v.is_remind then
                    return true
                end
            end
        end
    end

    return false
end

function NewAppearanceWGData:GetQiChongSXDExtendlist(qc_type)
    local sxd_cfg = self:GetQiChongAttrStoreAllCfg(qc_type)
    local list = {}
    if IsEmptyTable(sxd_cfg) then
        return list
    end

    local info = self:GetQiChongInfo(qc_type)
    if info == nil then
        return list
    end

    local upstar_cfg = self:GetQiChongBaseUpStarCfg(qc_type, info.star_level)
    if upstar_cfg == nil then
        return list
    end

    for k,v in pairs(sxd_cfg) do
        if v.is_show == 1 then
            local data = {cfg = v}
            data.is_open = v.open_grade <= upstar_cfg.grade_num
            data.used_num = self:GetQiChongSXDUseNum(qc_type, v.slot_idx)
            data.had_num = ItemWGData.Instance:GetItemNumInBagById(v.shuxingdan_id)
            data.is_remind = data.had_num > 0 and data.is_open and data.used_num < v.use_limit_num
            data.qc_type = qc_type
            table.insert(list, data)
        end
    end

    return list
end

function NewAppearanceWGData:GetQiChongAttrStoreAllCfg(qc_type)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self.mount_stone_cfg
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self.pet_stone_cfg
    end
end

function NewAppearanceWGData:GetMountStoreCfg()
    return self.mount_stone_cfg
end

function NewAppearanceWGData:GetPetStoreCfg()
    return self.pet_stone_cfg
end

function NewAppearanceWGData:GetQiChongSXDRemind(qc_type)
    local fun_name = self:GetQiChongFunnameBuType(qc_type)
    if fun_name then
        if FunOpen.Instance:GetFunIsOpened(fun_name) then
            local sxd_list = self:GetQiChongSXDExtendlist(qc_type)
            for k,v in ipairs(sxd_list) do
                if v.is_remind then
                    return true
                end
            end
        end
    end

    return false
end

function NewAppearanceWGData:GetQiChongSXDUseNum(qc_type, slot)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self:GetMountSXDExtendUsedNum(slot)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetPetSXDExtendUsedNum(slot)
    end
end

function NewAppearanceWGData:GetAdvancedSXDExtendCap(ad_type)
    local cap = 0
    local sxd_extend_list = self:GetAdvancedSXDExtendlist(ad_type)

    if sxd_extend_list then
        for k,v in ipairs(sxd_extend_list) do
            if v.is_open and v.used_num > 0 then
                local _, cell_cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(v.cfg, "attr_id", "attr_value")
                cap = cap + cell_cap * v.used_num
            end
        end
    end

    return cap
end

function NewAppearanceWGData:IsAttrStoreSXD(item_id)
    if nil ~= self.upgrade_stone_sxd_cfg[item_id] then
        return true
    end

    if nil ~= self.pet_stone_sxd_cfg[item_id] then
        return true
    end

    if nil ~= self.mount_stone_sxd_cfg[item_id] then
        return true
    end

    return false
end

function NewAppearanceWGData:GetAttrStoreSXDCfg(item_id)
    if self.upgrade_stone_sxd_cfg[item_id] then
        return self.upgrade_stone_sxd_cfg[item_id], NEWAPPEARANCE_SXD_TYPE.UpGrade, self.upgrade_stone_sxd_cfg[item_id].type
    end

    if self.pet_stone_sxd_cfg[item_id] then
        return self.pet_stone_sxd_cfg[item_id], NEWAPPEARANCE_SXD_TYPE.Other, MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
    end

    if self.mount_stone_sxd_cfg[item_id] then
        return self.mount_stone_sxd_cfg[item_id], NEWAPPEARANCE_SXD_TYPE.Other, MOUNT_LINGCHONG_APPE_TYPE.MOUNT
    end

    return {}
end

function NewAppearanceWGData:GetAdvancedFunnameBuType(ad_type)
    return SXD_FUNNAME[ad_type]
end

function NewAppearanceWGData:GetQiChongFunnameBuType(type)
    return QICHONG_SXD_FUNNAME[type]
end