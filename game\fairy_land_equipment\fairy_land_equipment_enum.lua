-- /jy_gm xianjieequipgmreq:operate_type param1 param2 param3
-- /jy_gm xianjieequipgmreq:3 0 0 0
-- operate_type = {
--     1,     -- 设置神体等级（param:slot value）
--     2,     -- 增加神体经验（param:slot value）
--     3,     -- 激活神体（param:slot）
--     4,     -- 激活书页部位（param:物品）
--     5,     -- 激活书页（param1:slot_index param2:page）
-- }

-- /jy_cmd AddHolyEquip 神体类型(from 0) 品质 数量
-- /jy_cmd AddGodBookPageChip 神体类型(from 0) 页数(from 0)

-- 神体枚举
GOD_BODY_ENUM = {
    MAX_SLOT_INFO_COUNT = 16,				   -- 仙界装备神体数量
    MAX_PAGE_COUNT = 5,
	MAX_HOLY_EQUIP_PART = 16,                  -- 仙界圣装部位
	MAX_HOLY_EQUIP_RAND_ATTR = 16,             -- 仙界圣装的进阶属性
    BAG_GRID_NUM = 400,                        -- 圣装背包格子数
    UPLEVEL_RIGHT = 1,
    UPGRADE_RIGHT = 2,
}

-- 操作类型
GOD_BODY_OP_TYPE = {
    ALL_INFO = 1,           -- 所以信息
    TAKE_OFF = 2,           -- 卸下装备			param1:slot_index param2:equip_index
    PUT_ON = 3,             -- 穿戴装备			param1:slot_index param2:bag_index
    BODY_UPLEVEL = 4,       -- 神体升级			param1:slot_index
    BODY_UPGRADE = 5,       -- 神体渡劫			param1:slot_index
    READ_PAGE_RED = 6,      -- 天书碎片红点已读     param1:slot_index param2:page param3:part
    ACT_PAGE = 7,           -- 激活书页			param1:slot_index param2:page
    ACT_BODY = 8,           -- 激活神体			param1:slot_index
    EQUIP_UP_LEVEL = 9,     -- 装备升级			param1:slot_index param2:equip_index
    EQUIP_UP_COLOR = 10,    -- 装备升品			param1:slot_index param2:equip_index param3:color
    ACT_TOTAL_LEVEL = 11,   -- 装备总等级激活    param1:slot_index param2:level
    CLEARUP_BAG = 12,       -- 整理背包->一键穿戴 param1:slot_index
    ACT_TOTAL_STAR = 13,    -- 装备总星级激活    param1:slot param2:star
    BODY_COST_UPLEVEL = 14, -- 消耗升级         param1:slot
    BREAK_JING_MAI = 15,    -- 打通经脉         param1:slot
    ONE_KEY_STRENGTHEN = 16,    -- 一键强化         param1:slot_index
}

-- 操作结果返回
GB_OPERATE_RESULT_TYPE = {
    PAGE_ACT = 1,           -- 书页激活     --p1:slot p2:page
    GOD_BODY_ACT = 2,       -- 神体激活     --p1:slot
    GOD_BODY_UPLEVEL = 3,   -- 神体升级     --p1:slot
    GOD_BODY_UPGRADE = 4,   -- 神体突破     --p1:slot
    EQUIP_UPLEVEL = 5,      -- 圣装强化     --p1:slot p2:part
    EQUIP_UPGRADE = 6,      -- 圣装进化     --p1:slot p2:part
    EQUIP_UPCOLOR = 7,      -- 圣装升品     --p1:slot p2:part p3:old_itemid p4:new_itemid
    EQUIP_TOTAL_LEVEL = 8,  -- 圣装总等级   --p1:slot
    EQUIP_TOTAL_STAR = 9,   -- 圣装总星级   --p1:slot
    BREAK_JING_MAI = 10,    -- 打通经脉     --p1:slot
    ONE_KEY_STRENGTHEN = 11 -- 一键强化     --p1:slot
}

GOD_BODY_LIST_STATE = {
    NO_ACT = 0,
    WAIT_ACT = 1,
    EQUIP_LIMIT = 2,
    ACT = 3,
}

XIANJIE_EQUIP_TYPE = {
	WUQI = 0,		--武器
	FUWU = 1,		--副武
	YIFU = 2,		--衣服
	TOUKUI = 3,		--头盔
	YAODAI = 4,		--腰带
	JIANJIA = 5,	--护臂
	KUZI = 6,		--裤子
	XIEZI = 7,		--鞋子
	TEJIE = 8,		--特戒
	JINNANG = 9,	--锦囊
	YUPEI = 10,		--玉佩
	XIANYIN = 11,	--仙印
}

XIANJIE_CHANGE_REASON = {
    UPLEVEL = 0,            -- 装备升级
    UPGRADE = 1,            -- 装备进阶
    TAOTAL_LEVEL = 2,       -- 装备总等级激活
    TAOTAL_STAR = 3,        -- 装备总星级激活
    ONE_KEY_LEVEL = 4,        -- 装备一键强化
}

XIANYIN_FORGE_TYPE = {
    STRENGTH = 1,
    EVOLVE = 2,
    UPQUALITY = 3,
}

XIANJIE_EQUIP_STRENGTHEN_MAX_STUFF = 3--不同的强化材料最大种数
EVOLVE_MAX_CONSUME_COUNT = 20

GOD_BODY_DUJIE_NUM = 9          --神体渡劫的次数

--界面装备格子特效
    --特殊部位
XJEQ_GRID_SPC_EFFECT = {
    [1] = "UI_jgg_kuang_lv",
    [2] = "UI_jgg_kuang_lan",
    [3] = "UI_jgg_kuang_zi",
    [4] = "UI_jgg_kuang_chengse_d",
    [5] = "UI_jgg_kuang_hong_d",
    [6] = "UI_jgg_kuang_fen_d",
    [7] = "UI_jgg_kuang_jinse_d",
    [8] = "UI_jgg_kuang_caise_d",
}

XJEQ_GRID_EFFECT_0 = {
    [1] = "UI_jgg_kuang_lv_0",
    [2] = "UI_jgg_kuang_lan_0",
    [3] = "UI_jgg_kuang_zi_0",
    [4] = "UI_jgg_kuang_chengse_0",
    [5] = "UI_jgg_kuang_hong_d_0",
    [6] = "UI_jgg_kuang_fen_d_0",
    [7] = "UI_jgg_kuang_jinse_d_0",
    [8] = "UI_jgg_kuang_caise_d_0",
}

XJEQ_GRID_EFFECT_1 = {
    [1] = "UI_jgg_kuang_lv_01",
    [2] = "UI_jgg_kuang_lan_01",
    [3] = "UI_jgg_kuang_zi_01",
    [4] = "UI_jgg_kuang_chengse_01",
    [5] = "UI_jgg_kuang_hong_d_01",
    [6] = "UI_jgg_kuang_fen_d_01",
    [7] = "UI_jgg_kuang_jinse_d_01",
    [8] = "UI_jgg_kuang_caise_d_01",
}

XJEQ_GRID_EFFECT_2 = {
    [1] = "UI_jgg_kuang_lv_02",
    [2] = "UI_jgg_kuang_lan_02",
    [3] = "UI_jgg_kuang_zi_02",
    [4] = "UI_jgg_kuang_chengse_02",
    [5] = "UI_jgg_kuang_hongse_d_02",
    [6] = "UI_jgg_kuang_fense_d_02",
    [7] = "UI_jgg_kuang_jinse_d_02",
    [8] = "UI_jgg_kuang_caise_d_02",
}
