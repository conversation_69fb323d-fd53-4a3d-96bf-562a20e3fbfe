-- 入队申请
--------------
CrossTeamApplyView = CrossTeamApplyView or BaseClass(SafeBaseView)
function CrossTeamApplyView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(814, 580)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_apply")
    self.select_index = 0
    self.view_name = "CrossTeamApplyView"
end

function CrossTeamApplyView:__delete()
end

function CrossTeamApplyView:ReleaseCallBack()
	if self.apply_list then
		self.apply_list:DeleteMe()
		self.apply_list = nil
	end
	self.pt_has_change_btn_gray = nil

	if self.world_talk_timecount then
		GlobalEventSystem:UnBind(self.world_talk_timecount)
		self.world_talk_timecount = nil
	end

	if self.word_talk_cd_over then
		GlobalEventSystem:UnBind(self.word_talk_cd_over)
		self.word_talk_cd_over = nil
	end
end

function CrossTeamApplyView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["size"])
    self.node_list.title_view_name.text.text = Language.NewTeam.TitleApply
	self.apply_list = AsyncListView.New(CrossTeamApplyListItem, self.node_list["ph_apply_list"])

	XUI.AddClickEventListener(self.node_list["btn_checkbg1"], BindTool.Bind1(self.OnClickCheck, self))
	XUI.AddClickEventListener(self.node_list["btn_today_refuse_check"], BindTool.Bind1(self.OnClickTodayCheck, self))
	XUI.AddClickEventListener(self.node_list["btn_auto_refuse"], BindTool.Bind1(self.OnClickAutoRefuse, self)) -- 全部拒绝
	XUI.AddClickEventListener(self.node_list["btn_apply_all"], BindTool.Bind1(self.OnClickApplyAll, self))
	XUI.AddClickEventListener(self.node_list["btn_invite"], BindTool.Bind1(self.OnClickInvate, self))
	XUI.AddClickEventListener(self.node_list["btn_speak"],BindTool.Bind1(self.OnClickSpeak, self))

    self.world_talk_timecount = GlobalEventSystem:Bind(TeamWorldTalk.NEW_TEAM_WORLD_TALK,BindTool.Bind(self.UpdateTeamWordTalkBtn,self))
    self.word_talk_cd_over = GlobalEventSystem:Bind(TeamWorldTalk.COMPLETE_CALL_BACK,BindTool.Bind(self.ComleteTeamWoldTalkCD,self))
end

function CrossTeamApplyView:OnClickInvate()
	CrossTeamWGCtrl.Instance:OpenInviteView()
end

function CrossTeamApplyView:OnClickSpeak()
    self:Close()
    local name = RoleWGData.Instance:GetRoleVo().name
	CrossTeamWGCtrl.Instance:ShowTalkView(name)
end

function CrossTeamApplyView:UpdateTeamWordTalkBtn(time)
	if self.node_list.txt_btn_word_talk then
		self.node_list.txt_btn_word_talk.text.text = string.format(Language.NewTeam.WorldTalk5_New2, time)
	end
	if not self.pt_has_change_btn_gray then
		self.pt_has_change_btn_gray = true
		if self.node_list["btn_speak"] then
			XUI.SetGraphicGrey(self.node_list["btn_speak"], true)
		end
	end
end

function CrossTeamApplyView:ComleteTeamWoldTalkCD()
	if CountDownManager.Instance:HasCountDown("team_world_talk") then
		CountDownManager.Instance:RemoveCountDown("team_world_talk")
	end
	if self.node_list.txt_btn_word_talk then
		self.node_list.txt_btn_word_talk.text.text = Language.NewTeam.WorldTalk7
	end
	self.pt_has_change_btn_gray = nil
	if self.node_list.btn_speak then
		XUI.SetGraphicGrey(self.node_list["btn_speak"],false)
	end
end

function CrossTeamApplyView:OnClickCheck()
	local must_check = CrossTeamWGData.Instance:GetTeamMustCheck()
	must_check = must_check == 0 and 1 or 0
    self.node_list["img_select1"]:SetActive(must_check == 0)
    
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_CHANGE_MUST_CHECK, must_check)
end

function CrossTeamApplyView:GetTodayCheckActive()
	if not self.node_list["btn_today_refuse_select"] then
		return false
	end
	return self.node_list["btn_today_refuse_select"].gameObject.activeSelf
end

function CrossTeamApplyView:OnClickTodayCheck()
	local is_select = self.node_list["btn_today_refuse_select"].gameObject.activeSelf
	self.node_list["btn_today_refuse_select"]:SetActive(not is_select)
end

function CrossTeamApplyView:OnClickAutoRefuse()
	local allJoinReq = CrossTeamWGData.Instance:GetReqTeamList()
	if nil == allJoinReq or #allJoinReq <= 0 then
		return
	end
    for k,v in pairs(allJoinReq) do
        CrossTeamWGCtrl.Instance:SendTeamReqJoinRet(v.req_uuid, 0)
	end
	CrossTeamWGData.Instance:TeamJoinReqClear()
	self:Flush()
	--关闭当前界面
	self:Close()
	ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)

end

-- 全部同意
function CrossTeamApplyView:OnClickApplyAll()
	local allJoinReq = CrossTeamWGData.Instance:GetReqTeamList()
	if nil == allJoinReq or #allJoinReq <= 0 then
		return
	end
    for k,v in pairs(allJoinReq) do
        CrossTeamWGCtrl.Instance:SendTeamReqJoinRet(v.req_uuid, 1)
	end
	CrossTeamWGData.Instance:TeamJoinReqClear()
	self:Flush()
	--关闭当前界面
	self:Close()
	ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)

end

function CrossTeamApplyView:ShowIndexCallBack()
	self:Flush()
	self.node_list["btn_today_refuse_select"]:SetActive(false)
end

function CrossTeamApplyView:OnFlush()
	local apply_list = CrossTeamWGData.Instance:GetReqTeamList()
	if apply_list then
		self.apply_list:SetDataList(apply_list, 0)
	end
	self.node_list.layout_blank_tip2:SetActive(#apply_list <= 0)
	--self.node_list.lbl_bg:SetActive(#apply_list > 0)
	self.node_list.bottom_btn_group1:SetActive(#apply_list > 0)
	self.node_list.bottom_btn_group2:SetActive(#apply_list <= 0)

	local must_check = CrossTeamWGData.Instance:GetTeamMustCheck()
	self.node_list["img_select1"]:SetActive(must_check == 0)
end

function CrossTeamApplyView:DeleteReq(uuid)
	if nil == uuid then
		return
	end
	CrossTeamWGData.Instance:RemoveTeamJoinReq(uuid)
	self:Flush()

	if CrossTeamWGData.Instance:GetReqTeamListSize() <= 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, 0)
		ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)
        MainuiWGCtrl.Instance:FlushView(0, "team_cell_flush")
		return--self:Close()
	end
end


------------------itemRender-----------------
CrossTeamApplyListItem = CrossTeamApplyListItem or BaseClass(BaseRender)

function CrossTeamApplyListItem:__init()
	self:CreateChild()
	XUI.AddClickEventListener(self.node_list["head_click"], BindTool.Bind1(self.OnClickHead, self))
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function CrossTeamApplyListItem:__delete()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function CrossTeamApplyListItem:CreateChild()
	self.node_list["btn_refuse"].button:AddClickListener(function()
		local req_uuid = self.data.req_uuid
		CrossTeamWGCtrl.Instance:SendTeamReqJoinRet(req_uuid, 0)

		if CrossTeamWGCtrl.Instance:GetApplayTodayCheckActive() then
			CrossTeamWGCtrl.Instance:SendNoLongerOperateReq(CROSS_TEAM_LOGIN_NO_LONGER_TYPE.CROSS_TEAM_LOGIN_NO_LONGER_TYPE_JOIN_MY_TEAM, req_uuid)
		end

		CrossTeamWGCtrl.Instance:RemoveTeamJoinReq(req_uuid)
	end)

	self.node_list["btn_agree"].button:AddClickListener(function()
		local req_uuid = self.data.req_uuid
		CrossTeamWGCtrl.Instance:SendTeamReqJoinRet(req_uuid, 1)
		CrossTeamWGCtrl.Instance:RemoveTeamJoinReq(req_uuid)
	end)
end

function CrossTeamApplyListItem:OnFlush()
	-- local str = string.format(Language.NewTeam.ApplyViewRoleName, self.data.req_role_name, self.data.req_role_level)
	-- EmojiTextUtil.ParseRichText(self.node_list["lbl_role_name"].emoji_text, str, 20, COLOR3B.DEFAULT)

	self.node_list.lbl_role_name.text.text = self.data.req_role_name
	local level_str = string.format(Language.NewTeam.PTLevel2,self.data.req_role_level)
	EmojiTextUtil.ParseRichText(self.node_list["lbl_req_role_level"].emoji_text, level_str, 24, COLOR3B.WHITE)

	self.node_list.power_right.text.text = "" --self.data.req_role_capability

	--头像框
	local data = {}
	data.role_id = self.data.req_uuid.temp_low
	data.prof = self.data.req_role_prof
	data.sex = self.data.req_role_sex
	data.fashion_photoframe = self.data.req_role_photoframe
	self.head_cell:SetImgBg(false)
	self.head_cell:SetData(data)
	-- 新增等级和关系
	local relation_flag = bit:d2b_two(self.data.req_role_relation_flag or 0)
	local index = -1
	for k,v in pairs(relation_flag) do
		if v == 1 then
			index = k
			break
		end
	end
	local relation_str = Language.NewTeam.TeamMateRelation[index + 2]
	if index + 2 == 1 then
		relation_str = ToColorStr(relation_str, COLOR3B.DEFAULT)
	end
	self.node_list.lbl_relationship.text.text = relation_str

	if self.data.req_role_vip_level and self.data.req_role_vip_level > 0 then
		self.node_list["vip_level"]:SetActive(true)
		local a,b = ResPath.GetCommonIcon("a2_vip"..self.data.req_role_vip_level)
		self.node_list["vip_level"].image:LoadSprite(a,b,function ()
			self.node_list.vip_level.image:SetNativeSize()
		end)
	else
		self.node_list["vip_level"]:SetActive(false)
    end
end

function CrossTeamApplyListItem:OnClickInvite()

end

function CrossTeamApplyListItem:OnClickHead()
	local role_id = self.data.req_uuid.temp_low
	BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(param_protocol)
		if self.view and self.view.gameObject and self.view.gameObject.activeInHierarchy == true then
			CrossTeamWGCtrl.Instance:CreateInviteRoleHeadCell(param_protocol.role_id, self.data.req_role_name,param_protocol.prof,param_protocol.is_online, self.node_list.head_click, param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
		end
	end, nil, true)
end
