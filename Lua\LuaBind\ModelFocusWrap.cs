﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ModelFocusWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ModelFocus), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("ResetToInit", ResetToInit);
		<PERSON><PERSON>Function("ModelFocusPlayForward", ModelFocusPlayForward);
		<PERSON><PERSON>Function("SetModelFocusParameter", SetModelFocusParameter);
		L.RegFunction("ModelFocusStatus", ModelFocusStatus);
		L.RegFunction("RemoveModelFocus", RemoveModelFocus);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		L.Reg<PERSON>ar("to_scale", get_to_scale, set_to_scale);
		<PERSON><PERSON>("to_pos", get_to_pos, set_to_pos);
		<PERSON><PERSON>("from_scale", get_from_scale, set_from_scale);
		<PERSON><PERSON>("from_pos", get_from_pos, set_from_pos);
		<PERSON><PERSON>("aim_trans", get_aim_trans, set_aim_trans);
		L.RegVar("is_not_pinch", get_is_not_pinch, set_is_not_pinch);
		L.RegVar("all_move_time", get_all_move_time, set_all_move_time);
		L.RegVar("pos_value", get_pos_value, set_pos_value);
		L.RegVar("scale_value", get_scale_value, set_scale_value);
		L.RegVar("amountPerDelta", get_amountPerDelta, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetToInit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ModelFocus obj = (ModelFocus)ToLua.CheckObject<ModelFocus>(L, 1);
			obj.ResetToInit();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ModelFocusPlayForward(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ModelFocus obj = (ModelFocus)ToLua.CheckObject<ModelFocus>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.ModelFocusPlayForward(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetModelFocusParameter(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 7);
			ModelFocus obj = (ModelFocus)ToLua.CheckObject<ModelFocus>(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
			UnityEngine.Vector3 arg3 = ToLua.ToVector3(L, 5);
			UnityEngine.Vector3 arg4 = ToLua.ToVector3(L, 6);
			float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
			obj.SetModelFocusParameter(arg0, arg1, arg2, arg3, arg4, arg5);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ModelFocusStatus(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				ModelFocus obj = (ModelFocus)ToLua.CheckObject<ModelFocus>(L, 1);
				obj.ModelFocusStatus();
				return 0;
			}
			else if (count == 2)
			{
				ModelFocus obj = (ModelFocus)ToLua.CheckObject<ModelFocus>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.ModelFocusStatus(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: ModelFocus.ModelFocusStatus");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveModelFocus(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ModelFocus obj = (ModelFocus)ToLua.CheckObject<ModelFocus>(L, 1);
			obj.RemoveModelFocus();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_to_scale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 ret = obj.to_scale;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index to_scale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_to_pos(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 ret = obj.to_pos;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index to_pos on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_from_scale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 ret = obj.from_scale;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index from_scale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_from_pos(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 ret = obj.from_pos;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index from_pos on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_aim_trans(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Transform ret = obj.aim_trans;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index aim_trans on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_is_not_pinch(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			bool ret = obj.is_not_pinch;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index is_not_pinch on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_all_move_time(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			float ret = obj.all_move_time;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index all_move_time on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pos_value(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 ret = obj.pos_value;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pos_value on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scale_value(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 ret = obj.scale_value;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scale_value on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_amountPerDelta(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			float ret = obj.amountPerDelta;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index amountPerDelta on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_to_scale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.to_scale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index to_scale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_to_pos(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.to_pos = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index to_pos on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_from_scale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.from_scale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index from_scale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_from_pos(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.from_pos = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index from_pos on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_aim_trans(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.aim_trans = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index aim_trans on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_is_not_pinch(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.is_not_pinch = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index is_not_pinch on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_all_move_time(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.all_move_time = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index all_move_time on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pos_value(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.pos_value = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pos_value on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_scale_value(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ModelFocus obj = (ModelFocus)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.scale_value = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scale_value on a nil value");
		}
	}
}

