LongHunShowWayView = LongHunShowWayView or BaseClass(SafeBaseView)

function LongHunShowWayView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
end

function LongHunShowWayView:__delete()

end

function LongHunShowWayView:ReleaseCallBack()
	if self.get_item then
		for i = 1,4 do
			self.get_item[i]:DeleteMe()
		end
		self.get_item = nil
	end
end

-- 加载配置
function LongHunShowWayView:LoadConfig()
	local bundle_name = "uis/view/long_hun_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_commmon_second_panel")
	self:AddViewResource(0,bundle_name,"layout_longhun_show_way")
end

function LongHunShowWayView:LoadCallBack(index, loaded_times)
	self:SetSecondView(Vector2(740,385))
	self.node_list.title_view_name.text.text = Language.LongHunView.LongHunGetWay
	self.node_list["btn_confirm"].button:AddClickListener(BindTool.Bind(self.OnClickConfirm,self))

	self.get_item = {}
	for i = 1 , 4 do 
		self.get_item[i] = ItemCell.New(self.node_list["cell"..i])
		self.node_list["cell"..i]:SetActive(false)
	end
end

function LongHunShowWayView:OnFlush(param_t)
	local show_cfg = LongHunWGData.Instance:GetShowWayCfg()[1]
	if not IsEmptyTable(show_cfg) then
		local item_table = string.split(show_cfg.show_item,"|")
		for i = 1 , 4 do 
			if item_table[i] then
				local id = tonumber(item_table[i])
				self.get_item[i]:SetData({item_id = id})
				self.node_list["cell"..i]:SetActive(true)
			else
				self.node_list["cell"..i]:SetActive(false)
			end
		end
		self.node_list["desc"].text.text = show_cfg.show_desc
	else
		for i = 1 , 4 do 
			self.node_list["cell"..i]:SetActive(false)
		end
	end
end

function LongHunShowWayView:OnClickConfirm()
	ViewManager.Instance:Close(GuideModuleName.LongHunView)
	ViewManager.Instance:Open(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_bootybay)
	self:OnClickCancle()
end

function LongHunShowWayView:OnClickCancle()
	self:Close()
end
