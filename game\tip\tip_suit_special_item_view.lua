TipSuitSpecialItemView = TipSuitSpecialItemView or BaseClass(SafeBaseView)

function TipSuitSpecialItemView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self.view_name = "TipSuitSpecialItemView"
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_suit_special_tip")
end

function TipSuitSpecialItemView:__delete()

end

function TipSuitSpecialItemView:LoadCallBack()
	self:InitTipsPanel()

	local bundle = "uis/view/itemtip_ui_prefab"
	local asset = "suit_special_tip_attr_render"
    self.base_attr_list = AsyncBaseGrid.New()
    self.base_attr_list:CreateCells({list_view = self.node_list["base_attr_list"],
            assetBundle = bundle, assetName = asset, itemRender = SuitSpecialBaseAttrRender, change_cells_num = 1, col = 2})
    self.base_attr_list:SetStartZeroIndex(false)

	-- local bundle1 = "uis/view/itemtip_ui_prefab"
	-- local asset1 = "suit_special_tip_dingzhi_attr_render"
    -- self.dingzhi_attr_list = AsyncBaseGrid.New()
    -- self.dingzhi_attr_list:CreateCells({list_view = self.node_list["dingzhi_attr_list"],
    --         assetBundle = bundle1, assetName = asset1, itemRender = SuitSpecialDingZhiAttrRender, change_cells_num = 1, col = 2})
    -- self.dingzhi_attr_list:SetStartZeroIndex(false)
	if not self.dingzhi_baseattr_list then
		self.dingzhi_baseattr_list = AsyncListView.New(SuitSpecialDingZhiAttrRender, self.node_list.dingzhi_baseattr_list)
	end

	if not self.dingzhi_specialattr_list then
		self.dingzhi_specialattr_list = AsyncListView.New(SuitSpecialDingZhiAttrRender, self.node_list.dingzhi_specialattr_list)
	end

	if not self.skill_list_scroll then
		self.skill_list_scroll = AsyncListView.New(SuitSpecialSkillRender, self.node_list.skill_list_scroll)
	end

	-- if not self.destiny_skill_list_scroll then
	-- 	self.destiny_skill_list_scroll = AsyncListView.New(SuitSpecialSkillRender, self.node_list.destiny_skill_list_scroll)
	-- end

	if not self.model_display then
		local display_node = self.node_list.model_root
		self.model_display = RoleModel.New()
		local display_data = {
			parent_node = display_node,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		self.model_display:SetRenderTexUI3DModel(display_data)
		-- self.display_model:SetUI3DModel(display_node.transform, drag_node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.model_display)
		-- local event_trigger = self.node_list["model_event"].event_trigger_listener
		-- self.model_display:SetUI3DModel(self.node_list["model_root"].transform, event_trigger, 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	XUI.AddClickEventListener(self.node_list["button_see_suit"], BindTool.Bind(self.OnBtnSeeSuit, self))
	XUI.AddClickEventListener(self.node_list["btn_use"], BindTool.Bind(self.OnBtnUseClick, self))
	XUI.AddClickEventListener(self.node_list["btn_gm"], BindTool.Bind(self.OnBtnGMClick, self))
end

function TipSuitSpecialItemView:ReleaseCallBack()
	if self.suit_item_list then
		self.suit_item_list:DeleteMe()
		self.suit_item_list = nil
	end

	if self.base_attr_list then
		self.base_attr_list:DeleteMe()
		self.base_attr_list = nil
	end

	if self.dingzhi_baseattr_list then
		self.dingzhi_baseattr_list:DeleteMe()
		self.dingzhi_baseattr_list = nil
	end

	if self.dingzhi_specialattr_list then
		self.dingzhi_specialattr_list:DeleteMe()
		self.dingzhi_specialattr_list = nil
	end

	if self.skill_list_scroll then
		self.skill_list_scroll:DeleteMe()
		self.skill_list_scroll = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end
end

function TipSuitSpecialItemView:ShowIndexCallBack()
	self:InitSetListData()
	self:FlushView()
end

function TipSuitSpecialItemView:InitSetListData()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local show_ids = ItemWGData.Instance:GetGradeTipsTypeData(item_cfg.grade_tips_type)
	local list_data = {}
	for k_1, v_1 in ipairs(show_ids) do
		local data = {}
		data.item_id = v_1
		data.is_self = v_1 == self.data.item_id and 1 or 0
		table.insert(list_data, data)
	end
	table.sort(list_data, function (a, b)
		return a.item_id < b.item_id
	end)

	local init_index = 1
	for k_2, v_2 in ipairs(list_data) do
		if v_2.is_self == 1 then
			init_index = k_2
			break
		end
	end

	if not self.suit_item_list then
		self.suit_item_list = AsyncListView.New(SuitSpecialItemCellRender, self.node_list.suit_item_list)
		self.suit_item_list:SetSelectCallBack(BindTool.Bind(self.OnSelectSuitRender, self))
		self.suit_item_list:SetDefaultSelectIndex(init_index)
	end
	self.suit_item_list:SetDataList(list_data)
	self.suit_item_list:JumpToIndex(init_index)
end

function TipSuitSpecialItemView:InitTipsPanel()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function TipSuitSpecialItemView:FlushView()
	self.node_list.btn_gm:SetActive(QUICK_ADDITEM)
	self.node_list.button_root:SetActive(QUICK_ADDITEM)
	self:ShowTipContent()
end

function TipSuitSpecialItemView:SetData(data)
	if not data then
		return
	end
	self.data = data
	if self:IsOpen() then
		self:FlushView()
	else
		self:Open()
	end
end

function TipSuitSpecialItemView:ShowTipContent()
	self:SetModelShow()

	local suit, part_id = WardrobeWGData.Instance:GetSuitPartidByItemId(self.data.item_id)
	if not suit then
		return
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	self.node_list["btn_use"]:SetActive(num > 0)
	self.node_list.button_root:SetActive(num > 0 or QUICK_ADDITEM)

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local base_attr_data = CustomizedSuitWGData.Instance:GetThemePartAttrList(suit, part_id, true)
	local dingzhi_data = CustomizedSuitWGData.Instance:GetThemePartAttrList(suit, part_id, false)
	--self.base_attr_list:SetDataList(base_attr_data)
	self.dingzhi_baseattr_list:SetDataList(base_attr_data)
	self.dingzhi_specialattr_list:SetDataList(dingzhi_data)
	self.node_list.dingzhi_attr_group:SetActive((not IsEmptyTable(base_attr_data) and not IsEmptyTable(dingzhi_data)))

	--self.dingzhi_attr_list:SetDataList(dingzhi_data)

	local _, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(self.data.item_id, item_cfg.sys_attr_cap_location)

	local _, capability, attr_list = ItemShowWGData.Instance:GetItemAttrDescAndCap(self.data, sys_type, false)
	local is_show_base_attr = not IsEmptyTable(attr_list)
	self.node_list.base_attr_group:SetActive(not IsEmptyTable(attr_list))
    for key, value in pairs(attr_list) do
        if EquipmentWGData.Instance:GetAttrIsPer(value.attr_str) then
            value.attr_name = ToColorStr(EquipmentWGData.Instance:GetAttrName(value.attr_str, false, false), COLOR3B.GLOD)
        end
    end
	self.base_attr_list:SetDataList(attr_list)

	local is_show_cap = is_show_base_attr
	if is_show_cap then
		local is_show_max_cap = false	-- 战力现在默认是基础的而已
		local img_name = is_show_max_cap and "a3_ty_zdl_4" or "a3_ty_zdl_3"
		local z_bundle, z_asset = ResPath.GetCommonImages(img_name)
		self.node_list["cap_bg"].image:LoadSprite(z_bundle, z_asset, function()
			self.node_list["cap_bg"].image:SetNativeSize()
		end)

		is_show_cap = capability > 0
		self.node_list["cap_value"].text.text = capability
	end
	self.node_list["cap_root"]:SetActive(is_show_cap)

	local cur_data = CustomizedSuitWGData.Instance:GetThemeSkillBySuit(suit)
	local skill_list = CustomizedSuitWGData.Instance:GetSelectSkillListBySuit(cur_data, suit)
	self.node_list.skill_sel_group:SetActive(not IsEmptyTable(skill_list))
	self.skill_list_scroll:SetDataList(skill_list)

	self.node_list["skill_destiny_group"]:SetActive(item_cfg.grade_tips_type > ITEM_TIANMING_TIP_VALUE)
	if item_cfg.grade_tips_type > ITEM_TIANMING_TIP_VALUE then
		local bs_skill_id = DestinyArriveWGData.Instance:GetSuitBianShenSkill(suit)
		
		if not bs_skill_id then return end
		local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(bs_skill_id, 1)
		local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(bs_skill_id, 1)

		self.node_list["destiny_skill_name"].text.text = skill_cfg.skill_name or ''
		self.node_list["destiny_skill_desc"].text.text = clien_skill_cfg.description or ''
		self.node_list["destiny_skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(clien_skill_cfg.icon_resource))
		self.node_list["destiny_desc"].text.text = Language.DestinyArrive.SuitActiveTipsHint
	end

	-- local bundle, asset = ResPath.GetF2RawImagesPNG("a2_wptc_suitbg" .. suit)
	-- self.node_list.rawimage_bg.raw_image:LoadSprite(bundle, asset)
	
	self.node_list.text_item_name.text.text = item_cfg.name

	-- local ef_bundle_name, ef_asset_name = ResPath.GetA2Effect("UI_wptc_bgeff" .. suit)
	-- self.node_list["effect"]:ChangeAsset(ef_bundle_name, ef_asset_name)
end

-- 模型展示
function TipSuitSpecialItemView:SetModelShow()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		print_error("DisplayItemTip","ShowDisplay() item_cfg is a nil value: item_id = ",self.data.item_id)
		return
	end

	if item_cfg.id and item_cfg.id > 0 then
		item_cfg = ItemWGData.Instance:GetItemConfig(item_cfg.id)
		if item_cfg == nil then
			print_error("DisplayItemTip","ShowDisplay() item_cfg is a nil value: id = ",item_cfg.id)
			return
		end
	end

	local display_type = item_cfg.is_display_role
	if nil == display_type or item_cfg.is_display_role == 0 or item_cfg.is_display_role == "" then
		return
	end

    self.model_display:RemoveWeapon()
	-- self:ClearFootEff()

	local path, bundle, asset, res_id, image_type, attr_cfg, animation_type, name, part_type

	if display_type == DisplayItemTip.Display_type.MOUNT_LINGCHONG then 			--坐骑
        res_id, attr_cfg = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_cfg.id)
		local item_id
		if item_cfg.param2 then
			item_id = string.split(item_cfg.param2, "|")
		end
		res_id = res_id or item_id[1]
		path = ResPath.GetMountModel
        animation_type = "mount"
    elseif display_type == DisplayItemTip.Display_type.LINGCHONG then 			--灵宠
        res_id, attr_cfg = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_cfg.id)
        local item_id
        if item_cfg.param2 then
            item_id = string.split(item_cfg.param2, "|")
        end
        res_id = res_id or item_id[1]
        path = ResPath.GetPetModel
        animation_type = "soul"

	elseif display_type == DisplayItemTip.Display_type.KUN then 				--鲲
		path = ResPath.GetMountModel
		local kun_base_cfg = NewAppearanceWGData.Instance:GetKunActCfgByItemId(item_cfg.id)
		if kun_base_cfg then
			res_id = kun_base_cfg.active_id
		end
	elseif display_type == DisplayItemTip.Display_type.FASHION or display_type == DisplayItemTip.Display_type.HALO then
		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		part_type = part_type or item_cfg.param1
		if part_type == SHIZHUANG_TYPE.BODY then							--时装
			path = ResPath.GetRoleModel
			local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(item_cfg.id)
			if fashion_cfg then
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
			end
		elseif part_type == SHIZHUANG_TYPE.HALO then						--光环
			path = ResPath.GetHaloModel
		end

		if part_type == SHIZHUANG_TYPE.HALO then
			self.model_display:SetMainAsset(ResPath.GetHaloModel(res_id))
		else
			self:ShowRoleModel(res_id)
			local _, weapon_res_id, __ = AppearanceWGData.Instance:GetRoleResId()

			self.model_display:SetWeaponResid(weapon_res_id)
		end
		return
	elseif display_type == DisplayItemTip.Display_type.LIANSHI or
		display_type == DisplayItemTip.Display_type.YAOSHI or
		display_type == DisplayItemTip.Display_type.WEIBA or
		display_type == DisplayItemTip.Display_type.SHOUHUAN or
		display_type == DisplayItemTip.Display_type.FOOT then 				--时装   + 默认的剑-- +当前武器模型

		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		part_type = part_type or item_cfg.param1

		if res_id and part_type then
			path, res_id, animation_type = self:GetFashionModlePathFun(res_id, part_type, item_cfg)
		end

		if part_type == SHIZHUANG_TYPE.FOOT then--足迹不展示武器
			-- self.model_display:SetRotation(MODEL_ROTATION_TYPE.FOOT)
			-- self.model_display:PlayRoleAction(SceneObjAnimator.Move)
			-- self.model_display:SetFootTrailModel(res_id)
		else
			local _,weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
			self.model_display:SetWeaponResid(weapon_res_id)
		end
	elseif display_type == DisplayItemTip.Display_type.WING then 						--羽翼
		--1.获取翅膀模型
		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		local item_id
		if item_cfg.param2 then
			item_id = string.split(item_cfg.param2, "|")
		end
		res_id = res_id or item_id[1]
		path = ResPath.GetWingModel
		animation_type = "wing"

	elseif display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI then 						--神兵
        res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = attr_cfg and attr_cfg.resouce or item_cfg.param2

        -- self.base_tips:PlayWeaponTween()
		if res_id then
			local weapon_res_id, weapon_res_id_2 = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
			bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)
			self.model_display:SetMainAsset(bundle, asset)
		end
		return
	elseif display_type == DisplayItemTip.Display_type.FABAO then 						--法宝
		res_id, image_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		path = ResPath.GetFaBaoModel
	elseif display_type == DisplayItemTip.Display_type.JIANZHEN then 					--剑阵
		res_id, image_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		res_id = res_id or item_cfg.param2
		path = ResPath.GetJianZhenModel

	elseif display_type == DisplayItemTip.Display_type.BABY then 					--宝宝
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		res_id, attr_cfg = MarryWGData.Instance:GetBabyResByItemId(show_id)
		if attr_cfg then
			path = ResPath.GetHaiZiModel
			animation_type = "soul"
		end
	elseif display_type == DisplayItemTip.Display_type.XIAOGUI then 					--小鬼
		path = ResPath.GetGuardModel
		-- attr_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		if xiaogui_cfg then
			res_id = xiaogui_cfg.appe_image_id
		end
	elseif display_type == DisplayItemTip.Display_type.TIANSHEN then 				--天神
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		local magic_image = TianShenWGData.Instance:GetAppeImage(show_id)
		if not magic_image then
			magic_image = TianShenHuamoWGData.Instance:GetHuaMoAttributeCfgByItem(show_id)
		end
		if not magic_image then
			return
		end
		-- if magic_image.tianshen_location then
		-- 	self.base_tips:SetDingWeiInfo(magic_image.tianshen_location)
		-- end
		self.model_display:SetTianShenModel(magic_image.appe_image_id, magic_image.index, false)

	elseif display_type == DisplayItemTip.Display_type.TIANSHEN_SHENSHIWAIGUAN then 				--天神神饰外观
		local shenqi_waiguan_cfg = TianShenWGData.Instance:GetWaiGuanCfgByStuff(item_cfg.id)
		if not shenqi_waiguan_cfg then
			return
		end

		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(shenqi_waiguan_cfg.index)
		if not tianshen_cfg then
			return
		end

		local b, a = ResPath.GetBianShenModel(tianshen_cfg.appe_image_id)
		self.model_display:SetMainAsset(b, a)
		bundle, asset = TianShenWGData.Instance:GetTianShenWeapon(tianshen_cfg.index, shenqi_waiguan_cfg.waiguan_id)
		self.model_display:SetWeaponModel(bundle, asset)
	elseif display_type == DisplayItemTip.Display_type.SHENQI then --天神神器

		local shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(item_cfg.id)
		if not shenqi_cfg then
			local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_cfg.id)
			if compose_cfg then
				shenqi_cfg = TianShenWGData.Instance:GetShenQiCfgByActItemId(compose_cfg.product_id)
			end
			if not shenqi_cfg then
				return
			end
		end

		local b, a = ResPath.GetTianShenShenQiPath(shenqi_cfg.ts_res_idui)
		self.model_display:SetMainAsset(b, a)
		-- self.base_tips:PlayWeaponTween()
	elseif display_type == DisplayItemTip.Display_type.WUHUNZHENSHEN then
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		local wuhun_active_cfg = WuHunWGData.Instance:GetWuhunResByItemId(show_id)
		
		if wuhun_active_cfg then
			local bundle, asset = ResPath.GetWuHunModel(wuhun_active_cfg.appe_image_id)
			self.model_display:SetMainAsset(bundle, asset)
			self.model_display:PlayRoleAction()
		end
	elseif display_type == DisplayItemTip.Display_type.NEWFIGHTMOUNT then
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		local cfg = NewFightMountWGData.Instance:GetUpgradeCostCfg(show_id)
		local mount_seq = cfg and cfg.mount_seq or 0
		local mount_type_cfg = NewFightMountWGData.Instance:GetMountTypeCfgBySeq(mount_seq)
		res_id = mount_type_cfg.appe_image_id
		path = ResPath.GetMountModel
        animation_type = "mount"
	elseif display_type == DisplayItemTip.Display_type.MINGQI then
		path = ResPath.GetMingQiModel
		res_id = ArtifactWGData.Instance:GetArtifactCfgByItemId(item_cfg.id).model_id
	elseif display_type == DisplayItemTip.Display_type.BEASTS then
		path = ResPath.GetBeastsModel
		res_id = ControlBeastsWGData.Instance:GetBeastModelResId(item_cfg.id)
		animation_type = "beast"
	elseif display_type == DisplayItemTip.Display_type.TIANSHENSHUANGSHENG then
		path = ResPath.GetShuangShengModel
		local cfg = TianShenShuangShengWGData.Instance:GetShuangShengTianShenAttrByItemId(item_cfg.id)
		if cfg then
			local app_image_id_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(cfg.avatar_id)
			if app_image_id_data then
				res_id = app_image_id_data.appe_image_id
				animation_type = "shaungshengtianshen"
			end
		end
	elseif display_type == DisplayItemTip.Display_type.ANGER_APPE_IMAGE then
		local show_nuqi_type = item_cfg.param1
		local show_nuqi_lv = CultivationWGData.Instance:GetAngerLevel(show_nuqi_type)
	
		local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(show_nuqi_type, show_nuqi_lv)
		if cfg ~= nil then
			local role_vo = GameVoManager.Instance:GetMainRoleVo()
			local special_status_table = {ignore_halo = true, ignore_wing = true}
			self.model_display:SetModelResInfo(role_vo, special_status_table)
			self.model_display:SetAngerImage(show_nuqi_type, true, cfg.default_body, cfg.default_face, cfg.default_hair)
		end
	end

	-- local capability = 0
	-- if attr_cfg then
	-- 	local attr = AttributeMgr.GetAttributteByClass(attr_cfg)
	-- 	capability = AttributeMgr.GetCapability(attr)
	-- end
	-- if capability > 0 then
	-- 	self.base_tips:SetCapabilityPanel({capability = capability})
	-- end

	self:FlushModel(path, res_id, animation_type)
	self.model_display:FixToOrthographic(self.root_node_transform)
end

--显示当前玩家模型
function TipSuitSpecialItemView:ShowRoleModel(role_res_id, ani_name)
	ani_name = ani_name or SceneObjAnimator.UiIdle

	if role_res_id then
		local extra_role_model_data = {
			animation_name = ani_name,
		}
		self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	else
		role_res_id = AppearanceWGData.Instance:GetRoleResId()
		local main_role = Scene.Instance:GetMainRole()
		local vo = main_role and main_role:GetVo()
		local d_body_res, d_hair_res, d_face_res
		if vo and vo.appearance then
			if vo.appearance.fashion_body == 0 then
				d_body_res = vo.appearance.default_body_res_id
				d_hair_res = vo.appearance.default_hair_res_id
				d_face_res = vo.appearance.default_face_res_id
			end
		end

		local extra_role_model_data = {
			d_face_res = d_face_res,
			d_hair_res = d_hair_res,
			d_body_res = d_body_res,
			animation_name = ani_name,
		}
		self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	end
end

function TipSuitSpecialItemView:FlushModel(path_fun, res_id, animation_type, other_asset)
	if path_fun == nil or res_id == nil then
		return
	end

	if self.model_display then
		local bundle, asset = path_fun(res_id)
		self.model_display:SetMainAsset(bundle, asset)
	end

	if animation_type and animation_type ~= "" then
		if animation_type == "mount" then 			--坐骑
			self.model_display:PlayMountAction()
		elseif animation_type == "wing" then 		--羽翼
			self.model_display:PlayWingAction()
		elseif animation_type == "soul" then 		--灵童
			self.model_display:PlaySoulAction()
		elseif animation_type == "soul_no_do_idle" then --灵童, 无需再播待机动作
			self.model_display:PlaySoulAction()
		elseif animation_type == "shaungshengtianshen" then
			self.model_display:PlayRoleAction(SceneObjAnimator.Rest, nil, true)
		elseif animation_type == "beast" then
			self.model_display:PlayRoleAction(SceneObjAnimator.Rest, nil, true)
		end
	end
end

function TipSuitSpecialItemView:OnSelectSuitRender(item)
	if not item or not item:GetData() or not self.data or self.data.item_id == item:GetData().item_id then
		return
	end

	self:SetData(item:GetData())
end

function TipSuitSpecialItemView:OnBtnSeeSuit()
	local suit = WardrobeWGData.Instance:GetSuitPartidByItemId(self.data.item_id)
	if not suit then
		return
	end
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg.grade_tips_type and item_cfg.grade_tips_type ~= "" then
		if item_cfg.grade_tips_type > ITEM_DINGZHI_TIP_VALUE and item_cfg.grade_tips_type < ITEM_TIANMING_TIP_VALUE then
			ViewManager.Instance:Open(GuideModuleName.CustomizedSuitView, 0, "jump_suit", {jump_suit = suit})
		else
			ViewManager.Instance:Open(GuideModuleName.DestinyArriveView, 0, "jump_suit", {jump_suit = suit})

		end
	else
		ViewManager.Instance:Open(GuideModuleName.CustomizedSuitView, 0, "jump_suit", {jump_suit = suit})
	end

	self:Close()
end

function TipSuitSpecialItemView:OnBtnUseClick()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	FunOpen.Instance:OpenViewNameByCfg(item_cfg.open_panel, self.data.item_id)
	self:Close()
end

function TipSuitSpecialItemView:OnBtnGMClick()
	if not self.data then
		return
	end

	local cur_data_conf = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local gm_additem_num = 10
	if cur_data_conf and cur_data_conf.pile_limit and cur_data_conf.pile_limit == 999 then
		gm_additem_num = 999
	end
	SysMsgWGCtrl.SendGmCommand("additem", self.data.item_id .." ".. gm_additem_num .. " " .. 0)
end

SuitSpecialItemCellRender = SuitSpecialItemCellRender or BaseClass(BaseRender)

function SuitSpecialItemCellRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_parent)
	end
end

function SuitSpecialItemCellRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function SuitSpecialItemCellRender:OnFlush()
	if not self.data then
		return
	end

	local item_d = {}
	item_d.item_id = self.data.item_id
	self.item_cell:SetData(item_d)
end

function SuitSpecialItemCellRender:OnSelectChange(is_select)
	self.node_list.image_slt:SetActive(is_select)
end



SuitSpecialBaseAttrRender = SuitSpecialBaseAttrRender or BaseClass(BaseRender)

function SuitSpecialBaseAttrRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.attr_name.text.text = self.data.attr_name
	self.node_list.attr_value.text.text = self.data.value_str
end


SuitSpecialDingZhiAttrRender = SuitSpecialDingZhiAttrRender or BaseClass(BaseRender)

function SuitSpecialDingZhiAttrRender:OnFlush()
	if not self.data then
		return
	end

	local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
	local per_desc = is_per and "%" or ""
	local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
	self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, false, false)
	self.node_list.attr_value.text.text = string.format("%s%s", value_str, per_desc)
end



SuitSpecialSkillRender = SuitSpecialSkillRender or BaseClass(BaseRender)

function SuitSpecialSkillRender:LoadCallBack()

end

function SuitSpecialSkillRender:ReleaseCallBack()

end

function SuitSpecialSkillRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.skill_nane.text.text = self.data.skill_name
	self.node_list.skill_desc.text.text = self.data.skill_desc

	self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))
end