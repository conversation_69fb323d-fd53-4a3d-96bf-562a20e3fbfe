
function RebateActivityView:InitRechargeView()
	if not self.recharge_rebate_list then
		self.recharge_rebate_list = AsyncListView.New(RechargeRebateCell,self.node_list["recharge_rebate_list"])
	end
	self.node_list["recharge_rebate_btn"].button:AddClickListener(BindTool.Bind(self.OnClickOneKeyGetRebate,self))
	self.node_list["recharge_rebate_note"].button:AddClickListener(BindTool.Bind(self.OnClickRechargeRebateNote,self))
end

function RebateActivityView:ReleseRechargeView()
	if self.recharge_rebate_list then
		self.recharge_rebate_list:DeleteMe()
		self.recharge_rebate_list = nil
	end
	self.had_load_list = false

	if self.delay_rebate_anim then
		GlobalTimerQuest:CancelQuest(self.delay_rebate_anim)
		self.delay_rebate_anim = nil
	end
	if RechargeActivityWGData.Instance then
		RechargeActivityWGData.Instance:CheckNeedCloseTab()
	end
end


function RebateActivityView:ShowIndexRechargeView()
	local content = RechargeActivityWGData.Instance:GetRebateRechargeRule()
	 self:SetRuleInfo(content, Language.RebateRecharge.Title)
	 self.can_playe_open_anim = true
	 self.node_list["recharge_rebate_list"]:SetActive(false)
end

function RebateActivityView:FlushRechargeView()
	if not self.had_load_list then
		local list_data = RechargeActivityWGData.Instance:GetRebateListData()
		self.recharge_rebate_list:SetDataList(list_data)
	else
		self.recharge_rebate_list:RefreshActiveCellViews()
	end
	if self.can_playe_open_anim then
		self.can_playe_open_anim = false
		self:PlayRechargeRebateOpen()
	end
	local money = RechargeActivityWGData.Instance:GetNeedRechargeCount()
	local already_recharge = RechargeActivityWGData.Instance:GetAlreadyRecharge()
	money = money / RECHARGE_BILI
	already_recharge = already_recharge / RECHARGE_BILI
	local color = already_recharge >= money and COLOR3B.D_GREEN or COLOR3B.D_RED
	self.node_list["recharge_rebate_text"].text.text = string.format(Language.RebateRecharge.AlreadyRecharge,color,already_recharge,money)
	self.node_list["recharge_rebate_repoint"]:SetActive(RechargeActivityWGData.Instance:IsShowRebateZhiGouRedPoint() == 1)
	self.node_list["recharge_rebate_cost_text"].text.text = money
	local a,b
	if already_recharge < money then
		--XUI.SetGraphicGrey(self.node_list.recharge_rebate_btn, true)
		a,b = ResPath.GetRebateActImage("lianchong_weidacheng")
		self.node_list.recharge_rebate_btn_text.image:LoadSprite(a,b,function ()
			self.node_list.recharge_rebate_btn_text.image:SetNativeSize()
		end)
	else
		a,b = ResPath.GetRebateActImage("lianchong_yijianlingqu")
		self.node_list.recharge_rebate_btn_text.image:LoadSprite(a,b,function ()
			self.node_list.recharge_rebate_btn_text.image:SetNativeSize()
		end)
	end
	--elseif RechargeActivityWGData.Instance:IsShowRebateZhiGouRedPoint() == 0 then
		--XUI.SetGraphicGrey(self.node_list.recharge_rebate_btn, true)
	--else
		--XUI.SetGraphicGrey(self.node_list.recharge_rebate_btn, false)
	--end
end

function RebateActivityView:OnClickOneKeyGetRebate()
	if not RechargeActivityWGData.Instance:GetIsEnoughRecharge() then
		local money = RechargeActivityWGData.Instance:GetNeedRechargeCount()
		local ok_func = function ()
			if money > 0 then
				RechargeWGCtrl.Instance:Recharge(money / RECHARGE_BILI, GET_GOLD_REASON.GET_GOLD_REASON_ZHI_GOU)
			end
		end
		local text_dec = string.format(Language.RebateRecharge.CheckAndRecharge, money / RECHARGE_BILI)
		TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)

		TipWGCtrl.Instance:ShowSystemMsg(Language.RebateRecharge.GetRebateDay[7])
		return
	end
	if RechargeActivityWGData.Instance:IsShowRebateZhiGouRedPoint() == 1 then
		RebateActivityWGCtrl.Instance:SendActZhiGouOpera(ACT_ZHICHONG_OPER_TYPE.ACT_ZHICHONG_OPER_TYPE_ONE_KEY_FETCH_ALL_REWARD)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.RebateRecharge.GetRebateDay[6])
	end
end

function RebateActivityView:OnClickRechargeRebateNote()
	self:ShowRuleContent()
end


function RebateActivityView:PlayRechargeRebateOpen()
	if self.delay_rebate_anim then
		GlobalTimerQuest:CancelQuest(self.delay_rebate_anim)
		self.delay_rebate_anim = nil
	end
	self.delay_rebate_anim = GlobalTimerQuest:AddDelayTimer(function ()
		self.node_list["recharge_rebate_list"]:SetActive(true)
		local cell_list = self.recharge_rebate_list:GetAllItems()
		local sort_list = GetSortListView(cell_list)
    	local count = 0
    	for k,v in ipairs(sort_list) do
    	    if 0 ~= v.index then
    	        count = count + 1
    	    end
    	    v.item:OnPlayOpenAnim(count)
    	end
	end,0.4)
	
end

RechargeRebateCell = RechargeRebateCell or BaseClass(BaseRender)

function RechargeRebateCell:__init()
	self.reward_cell = {}
	for i = 1,5 do
		self.reward_cell[i] = ItemCell.New(self.node_list["reard_cell"..i])
	end
	self.node_list["rebate_get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetRechargeRebate,self))
end

function RechargeRebateCell:__delete()
	if self.reward_cell then
		for i= 1,5 do
			if self.reward_cell[i] then
				self.reward_cell[i]:DeleteMe()
			end
		end
		self.reward_cell = nil
	end
end

function RechargeRebateCell:OnFlush()
	if self.data and not IsEmptyTable(self.data) then
		if self.data.reward_item then
			for i = 1, 5 do
				if self.data.reward_item[i-1] then
					self.reward_cell[i]:SetData(self.data.reward_item[i-1])
					self.node_list["reard_cell"..i]:SetActive(true)
				else
					self.node_list["reard_cell"..i]:SetActive(false)
				end
			end
		end
		self.node_list["rebate_num"].text.text = self.data.show or 1
		local day_index = self.data.day_id or 0
		self.node_list["day_text"].text.text = string.format(Language.RebateRecharge.DayNumIndex,NumberToChinaNumber(day_index))
		self.day_status = RechargeActivityWGData.Instance:GetThisDayCanGet(self.data.day_id)
		self.node_list["have_get"]:SetActive(self.day_status == 2)
		self.node_list["rebate_get_btn"]:SetActive(self.day_status ~= 2)
		self.node_list["red_point"]:SetActive(false)
		if self.day_status == 1 then
			self.node_list["get_text"].text.text = Language.RebateRecharge.GetRebateDay[1]
			self.node_list["red_point"]:SetActive(true)
		elseif self.day_status == 0 then
			local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			local need_day =  day_index - cur_day
			if need_day == 1 then
				self.node_list["get_text"].text.text = Language.RebateRecharge.GetRebateDay[2]
			elseif need_day == 2 then
				self.node_list["get_text"].text.text = Language.RebateRecharge.GetRebateDay[3]
			elseif need_day > 2 then
				self.node_list["get_text"].text.text = string.format(Language.RebateRecharge.GetRebateDay[4],NumberToChinaNumber(need_day))
			end
		elseif self.day_status == -1 then
			self.node_list["get_text"].text.text = Language.RebateRecharge.GetRebateDay[5]
		end
	end
end

function RechargeRebateCell:OnClickGetRechargeRebate()
	if self.day_status == -1 then
		self:OnClickRecharge()
	elseif self.day_status == 1 then
		RebateActivityWGCtrl.Instance:SendActZhiGouOpera(ACT_ZHICHONG_OPER_TYPE.ACT_ZHICHONG_OPER_TYPE_FETCH_REWARD,self.data.day_id)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.RebateRecharge.NeedWayToGet)
	end
end

function RechargeRebateCell:OnClickRecharge()
	local money = RechargeActivityWGData.Instance:GetNeedRechargeCount()
	local ok_func = function ()
		if money > 0 then
			RechargeWGCtrl.Instance:Recharge(money / RECHARGE_BILI, GET_GOLD_REASON.GET_GOLD_REASON_ZHI_GOU)
		end
	end
	local text_dec = string.format(Language.RebateRecharge.CheckAndRecharge,money / RECHARGE_BILI)
	TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
	TipWGCtrl.Instance:ShowSystemMsg(Language.RebateRecharge.GetRebateDay[7])
end

function RechargeRebateCell:OnPlayOpenAnim(index)
	local time = 0.5
	local start_x = 1120
	local speed = start_x / time
	start_x = start_x + 150 * index
	time = start_x / speed
	self.node_list["animal_root"].transform.localPosition = Vector3(start_x, 0, 0)
	self.node_list["animal_root"].rect:DOAnchorPos(Vector2(0, 0), time)
end