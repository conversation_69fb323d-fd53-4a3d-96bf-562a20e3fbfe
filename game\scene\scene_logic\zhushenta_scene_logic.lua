ZhuShenTaSceneLogic = ZhuShenTaSceneLogic or BaseClass(CommonFbLogic)
--诛神塔场景逻辑
function ZhuShenTaSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function ZhuShenTaSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
	-- self.zhushenta_view = nil
	if CountDownManager.Instance:HasCountDown("zhushenta_fb_next_level") then
		CountDownManager.Instance:RemoveCountDown("zhushenta_fb_next_level")
	end

	if self.start_countdown then
		self.start_countdown:DeleteMe()
		self.start_countdown = nil
	end
end

function ZhuShenTaSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		FuBenWGCtrl.Instance:OpenZhuShenTaView()
		-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
	else
		FuBenWGCtrl.Instance:FlushZhuShenTaView()
	end
	-- MainuiWGCtrl.Instance:SetTaskActive(false)
	FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.ZHUSHENTA_FB)
	self:HandleGuaJi()
	MainuiWGCtrl.Instance:SetTaskContents(false)
	MainuiWGCtrl.Instance:SetOtherContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(true, Language.FbName.ZhuShenTa)
	MainuiWGCtrl.Instance:SetTeamBtnState(false)

	if old_scene_type ~= new_scene_type then
		local time_stamp = FuBenWGData.Instance:GetZhushenTaFbInfo().prepare_end_timestamp
		if time_stamp - TimeWGCtrl.Instance:GetServerTime() <= 0 then
			FuBenWGCtrl.Instance:ZhuShenTaStarAni()
		end
	end
	--local is_open = FuBenWGCtrl.Instance:FlushZhuShenTaViewIsOpen()
	-- if is_open then
	-- 	FuBenWGCtrl.Instance:ZhuShenTaStarAni()
	-- else
	-- 	GlobalTimerQuest:AddDelayTimer(function()
	-- 		FuBenWGCtrl.Instance:ZhuShenTaStarAni()
	-- 		end,1.5)
	-- end
end

function ZhuShenTaSceneLogic:Out(old_scene_type, new_scene_type)
	MainuiWGCtrl.Instance:SetShowTimeTextState( false )
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	if self.start_countdown then
		self.start_countdown:CloseViewHandler()
	end
	if old_scene_type ~= new_scene_type then
		CommonFbLogic.Out(self)
		FuBenWGCtrl.Instance:CloseZhuShenTaView()
	end
end

function ZhuShenTaSceneLogic:HandleGuaJi()
	local info = FuBenWGData.Instance:GetZhushenTaFbInfo()
	if 0 ~= info.prepare_end_timestamp and info.prepare_end_timestamp > TimeWGCtrl.Instance:GetServerTime() then
		GuajiWGCtrl.Instance:StopGuaji()
	else
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end