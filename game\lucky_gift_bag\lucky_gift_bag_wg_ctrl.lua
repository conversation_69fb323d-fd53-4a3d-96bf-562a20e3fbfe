require("game/lucky_gift_bag/lucky_gift_bag_view")
require("game/lucky_gift_bag/lucky_gift_bag_record_view")
require("game/lucky_gift_bag/lucky_gift_bag_getreward_view")
require("game/lucky_gift_bag/lucky_gift_bag_comfirm_view")
require("game/lucky_gift_bag/lucky_gift_bag_rank_view")
require("game/lucky_gift_bag/send_lucky_value_view")
require("game/lucky_gift_bag/lucky_gift_bag_superdouble_view")
require("game/lucky_gift_bag/lucky_gift_bag_wg_data")
require("game/lucky_gift_bag/lucky_gift_bag_throughtrain_view")

LuckyGiftBagWgCtrl = LuckyGiftBagWgCtrl or BaseClass(BaseWGCtrl)

function LuckyGiftBagWgCtrl:__init()
	if LuckyGiftBagWgCtrl.Instance then
		error("[LuckyGiftBagWgCtrl]:Attempt to create singleton twice!")
	end
	LuckyGiftBagWgCtrl.Instance = self

	self.data = LuckyGiftBagWgData.New()
	self.view = LuckyGiftBagView.New(GuideModuleName.LuckyGiftBagView)
	self.get_reward_view = LuckyGiftBagGetRewardView.New()
	self.comfirm_view = LuckyGiftBagComfirmView.New()
	self.record_view = LuckyGiftBagRecordView.New()
	self.rank_view = LuckyGiftBagRankView.New()
	self.send_lucky_value_view = SendLuckyValueView.New()
	self.through_train_view = LuckyGiftBagThroughTrainView.New(GuideModuleName.LuckyGiftBagThroughTrainView)
	self.superdouble_view = LuckyGiftBagSuperDoubleView.New()
	self:RegisterAllProtocals()
end

function LuckyGiftBagWgCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil	
	end

	LuckyGiftBagWgCtrl.Instance = nil

	if self.record_view then
		self.record_view:DeleteMe()
		self.record_view = nil
	end

	if self.get_reward_view then
		self.get_reward_view:DeleteMe()
		self.get_reward_view = nil
	end

	if self.comfirm_view then
		self.comfirm_view:DeleteMe()
		self.comfirm_view = nil
	end

	if self.rank_view then
		self.rank_view:DeleteMe()
		self.rank_view = nil
	end

	if self.send_lucky_value_view then
		self.send_lucky_value_view:DeleteMe()
		self.send_lucky_value_view = nil
	end

	if self.through_train_view then
		self.through_train_view:DeleteMe()
		self.through_train_view = nil
	end

	if self.superdouble_view then
		self.superdouble_view:DeleteMe()
		self.superdouble_view = nil
	end
end

function LuckyGiftBagWgCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCCrossLuckyGiftBaseInfo, "OnSCCrossLuckyGiftBaseInfo")
	self:RegisterProtocol(SCCrossLuckyGiftRateRecordAdd, "OnSCCrossLuckyGiftRateRecordAdd")
	self:RegisterProtocol(SCCrossLuckyGiftBuyResult, "OnSCCrossLuckyGiftBuyResult")
	self:RegisterProtocol(SCCrossLuckyGiftInfo, "OnSCCrossLuckyGiftInfo")
	self:RegisterProtocol(SCCrossLuckyGiftRateRecordInfo, "OnSCCrossLuckyGiftRateRecordInfo")
	self:RegisterProtocol(SCCrossLuckyGiftRankInfo, "OnSCCrossLuckyGiftRankInfo")
end

-- 跨服幸运大礼包请求
function LuckyGiftBagWgCtrl:SendLuckyGiftBagReq(opera_type,param_1,param_2,param_3)
	local param_t = {}
	param_t.activity_type = ACTIVITY_TYPE.CROSS_ACTIVITY_LuckyGiftBag
	param_t.opera_type = opera_type or 0
	param_t.param_1 = param_1 or 0
	param_t.param_2 = param_2 or 0
	param_t.param_3 = param_3 or 0
	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end

-- 活动信息
function LuckyGiftBagWgCtrl:OnSCCrossLuckyGiftBaseInfo(protocol)
	self.data:SetLuckyGiftBaseInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

-- 单个大奖增加
function LuckyGiftBagWgCtrl:OnSCCrossLuckyGiftRateRecordAdd(protocol)
	LuckyGiftBagWgData.Instance:AddRateRecordListItem(protocol)
	if self.record_view:IsOpen() then
		self.record_view:Flush()
	end
end

-- 购买结果返回
function LuckyGiftBagWgCtrl:OnSCCrossLuckyGiftBuyResult(protocol)
	LuckyGiftBagWgData.Instance:SetResultInfo(protocol)
	if self.comfirm_view:IsOpen() then
		self.comfirm_view:Close()
	end
	if not self.get_reward_view:IsOpen() then
		self.get_reward_view:Open()
	else
		self.get_reward_view:Flush()
	end
end

-- 幸运值
function LuckyGiftBagWgCtrl:OnSCCrossLuckyGiftInfo(protocol)
	local old_daily_reward_flag = LuckyGiftBagWgData.Instance:GetDailyRewardFlag()
	self:ShowGetLuckyValue(protocol)
	self.data:SetLuckyGiftInfo(protocol)

	if protocol.daily_reward_flag ~= old_daily_reward_flag then
		RemindManager.Instance:Fire(RemindName.LuckyGiftBagDailyReward)
	end

	--每日奖励内容后端不下发，当状态由可领取编成已领取时候打开展示界面
	if old_daily_reward_flag == 0 and protocol.daily_reward_flag == 1 then
		LuckyGiftBagWgData.Instance:SetDailyRewardInfo()
		if not self.get_reward_view:IsOpen() then
			self.get_reward_view:Open()
		end
	end

	if self.view:IsOpen() then
		self.view:Flush()
		-- self.view:Flush(0,"LuckyValue")
	end

	if self.through_train_view:IsOpen() then
		self.through_train_view:Flush()
	end

	if self.superdouble_view:IsOpen() then
		self.superdouble_view:Flush()
	end

	if self.send_lucky_value_view:IsOpen() then
		self.send_lucky_value_view:Flush()
	end
end

function LuckyGiftBagWgCtrl:ShowGetLuckyValue(protocol)
	local new_value = protocol.lucky_value or 0

	if new_value > 0 then
		local old_value = self.data:GetLickyValue()

		if old_value ~= new_value then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.LuckyGiftBag.LuckyValueAdd, new_value - old_value))
		end
	end
end

-- 大奖记录
function LuckyGiftBagWgCtrl:OnSCCrossLuckyGiftRateRecordInfo(protocol)
	LuckyGiftBagWgData.Instance:SetRateRecordListItem(protocol)
	if self.record_view:IsOpen() then
		self.record_view:Flush()
	end
end

function LuckyGiftBagWgCtrl:OpenComfirmView(grade, seq, gift_name)
	LuckyGiftBagWgData.Instance:SetCurrentSelectInfo(grade, seq, gift_name)
	if not self.comfirm_view:IsOpen() then
		self.comfirm_view:Open()
	else
		self.comfirm_view:Flush()
	end
end

function LuckyGiftBagWgCtrl:OpenLuckyGiftBagRecordView()
	if not self.record_view:IsOpen() then
		self.record_view:Open()
	end
end

function LuckyGiftBagWgCtrl:BugLuckyGiftBagReq(prchase_mode)
	local grade, seq = LuckyGiftBagWgData.Instance:GetCurrentSelectInfo()
    local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
    local grade_cfg = LuckyGiftBagWgData.Instance:GetGradeCfgInfo(grade, seq)

    if not IsEmptyTable(grade_cfg) and grade_cfg[prchase_mode] and grade_cfg[prchase_mode][1] then
        local grade_data = grade_cfg[prchase_mode][1]
        local cost_value = grade_data and grade_data.need_cost or 0
        if role_gold >= cost_value then
            LuckyGiftBagWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_BUY, seq, prchase_mode)
        else
            VipWGCtrl.Instance:OpenTipNoGold()
        end
    end	
end

function LuckyGiftBagWgCtrl:OnSCCrossLuckyGiftRankInfo(protocol)
	LuckyGiftBagWgData.Instance:SetRankData(protocol)
	if self.rank_view:IsOpen() then
		self.rank_view:Flush()
	end
end

function LuckyGiftBagWgCtrl:OpenRankView()
	if not self.rank_view:IsOpen() then
		self.rank_view:Open()
	end
end

function LuckyGiftBagWgCtrl:OpenSendLuckyValueView()
	if not self.send_lucky_value_view:IsOpen() then
		self.send_lucky_value_view:Open()
	end
end

function LuckyGiftBagWgCtrl:OpenThroughTrainView()
	if not self.through_train_view:IsOpen() then
		self.through_train_view:Open()
	end
end

function LuckyGiftBagWgCtrl:OpenSuperDoubleView()
	if not self.superdouble_view:IsOpen() then
		self.superdouble_view:Open()
	end
end