GuildPassView = GuildPassView or BaseClass(SafeBaseView)
GuildPassView.STATE = {
    SEND = 1,
    ACCEPT = 2
}

function GuildPassView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 0), sizeDelta = Vector2(814, 580)})
    self:AddViewResource(0, "uis/view/guild_answer_ui_prefab", "layout_pass_invite_view")
	self:SetMaskBg()
end

function GuildPassView:ReleaseCallBack()
    if self.role_list then
        self.role_list:DeleteMe()
        self.role_list = nil
    end
end

function GuildPassView:ShowIndexCallBack()
    if GuildAnswerWGData.Instance:HasInvite() or GuildAnswerWGData.Instance:HasGetPassExp() then
        self:OnClickSwitch(GuildPassView.STATE.ACCEPT)
    else
        self:OnClickSwitch(GuildPassView.STATE.SEND)
    end
end

function GuildPassView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.GuildAnswer.PassTitle

	self.node_list["btn_send"].button:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, GuildPassView.STATE.SEND))
	self.node_list["btn_accept"].button:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, GuildPassView.STATE.ACCEPT))

    self.role_list = AsyncListView.New(GuildPassRoleRender, self.node_list["role_list"])
end

function GuildPassView:OpenCallBack()
    GuildAnswerWGCtrl.Instance:HidePassBtnAnim(false)
end

function GuildPassView:OnFlush(param)
    for i, v in pairs(param) do
        if self.choose_state == i then
            local data
            if i == GuildPassView.STATE.SEND then
                data = GuildAnswerWGData.Instance:GetSceneObjMoveInfoList()
                self.node_list["txt_no_info"].text.text = Language.GuildAnswer.NoInvite
            else
                data = GuildAnswerWGData.Instance:GetSetInviteMeInfo()
                self.node_list["txt_no_info"].text.text = Language.GuildAnswer.NoInvite
            end
            self.node_list["no_invite"]:SetActive(IsEmptyTable(data))
            self.role_list:SetDataList(data)
        end
    end

    local max_per = GuildAnswerWGData.Instance:GetMaxPassExpPer()
    max_per = max_per / 100
    self.node_list["txt_tip2"].text.text = string.format(Language.GuildAnswer.ExpDis,max_per)
    local has_get_exp = GuildAnswerWGData.Instance:HasGetPassExp()
    self.node_list["txt_tip1"].text.text = string.format(Language.GuildAnswer.HasGetExp,
    has_get_exp and COLOR3B.RED or COLOR3B.DEFAULT_NUM, has_get_exp and 1 or 0)
end

function GuildPassView:OnClickSwitch(state)
    if GuildAnswerWGData.Instance:HasGetPassExp() and state == GuildPassView.STATE.SEND then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.HasGetExpTip)
        -- GlobalTimerQuest:AddDelayTimer(function()
        --     self.node_list["btn_accept"].toggle.isOn = true
        -- end, 0)
        return
    end
    self.choose_state = state
    self.node_list["btn_accept_nor"]:SetActive(self.choose_state ~= GuildPassView.STATE.ACCEPT)
    self.node_list["btn_accept_hl"]:SetActive(self.choose_state == GuildPassView.STATE.ACCEPT)
    self.node_list["btn_send_nor"]:SetActive(self.choose_state ~= GuildPassView.STATE.SEND)
    self.node_list["btn_send_hl"]:SetActive(self.choose_state == GuildPassView.STATE.SEND)
    if state == GuildPassView.STATE.SEND then
        GuildAnswerWGData.Instance:SetSceneObjMoveInfoList()
        self:Flush(nil, GuildPassView.STATE.SEND)
    else
        self:Flush(nil, GuildPassView.STATE.ACCEPT)
    end
end
--------------------------------------------------------------------------------------
GuildPassRoleRender = GuildPassRoleRender or BaseClass(BaseRender)
function GuildPassRoleRender:__delete()
    self:CancelCountDown()
    self.count_down_key = nil

    if self.head then
        self.head:DeleteMe()
        self.head = nil
    end
end

function GuildPassRoleRender:__init()
    self.head = BaseHeadCell.New(self.node_list["head"])
end

function GuildPassRoleRender:LoadCallBack()
    self.node_list["btn_operate"].button:AddClickListener(BindTool.Bind(self.OnClickOperate, self))
end

function GuildPassRoleRender:OnFlush()
    self:CancelCountDown()
    self.count_down_key = "Guild_Pass_" .. self.data.obj_id
    self.head:SetData({role_id = self.data.role_id, prof = self.data.prof, sex = self.data.sex, fashion_photoframe = self.data.fashion_photoframe})
    self.node_list["name"].text.text = "【"..self.data.name.."】"
    local is_df, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
    self.node_list["huo"]:SetActive(is_df)
    self.node_list["level"].text.text = is_df and level or "Lv." .. level

    if self.data.type == GuildPassView.STATE.SEND then
        local cur_time = TimeWGCtrl.Instance:GetServerTime()
        local delta_time = GuildAnswerWGData.Instance:GetPassViewSendColdDownTimeStamp(self.count_down_key) - cur_time
        local in_colddown = delta_time > 0.1        -- 发送按钮是否冷却中
        if in_colddown then
            self:UpdateBtn(0, delta_time)
            CountDownManager.Instance:AddCountDown(self.count_down_key, BindTool.Bind(self.UpdateBtn, self),
                    BindTool.Bind(self.CompleteBtn, self), nil, delta_time, 0.1)
            XUI.SetButtonEnabled(self.node_list["btn_operate"], false)
        else
            XUI.SetButtonEnabled(self.node_list["btn_operate"], true)
            self.node_list["btn_text"].text.text = Language.GuildAnswer.PassSend
        end
    else
        XUI.SetButtonEnabled(self.node_list["btn_operate"], true)
        self.node_list["btn_text"].text.text = Language.GuildAnswer.PassAccept
    end

    if GuildAnswerWGData.Instance:HasGetPassExp() then
        if self.data.type == GuildPassView.STATE.SEND then
            self.node_list["desc"].text.text = Language.GuildAnswer.NoExpTip
        else
            self.node_list["desc"].text.text = Language.GuildAnswer.NoExp
        end
    else
        local exp, per = GuildAnswerWGData.Instance:GetPassExpInfo(self.data.level)
        self.node_list["desc"].text.text = string.format(Language.GuildAnswer.GetExpNum, exp, per)
    end
end

function GuildPassRoleRender:OnClickOperate()
    local main_role = Scene.Instance:GetMainRole()
    if main_role:IsInTianShenState() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.TianShenState)
        return
    end
    if self.data.type == GuildPassView.STATE.SEND then
        if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_CHUAN_GONG) then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
            return
        end
        local cur_time = TimeWGCtrl.Instance:GetServerTime()
        GuildAnswerWGData.Instance:SetPassViewSendColdDownTimeStamp(self.count_down_key, cur_time + 5)
        GuildAnswerWGCtrl.Instance:SendInviteOperate(self.data.role_id)
        SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.SendPassInvite)
        XUI.SetButtonEnabled(self.node_list["btn_operate"], false)
    else
        ViewManager.Instance:Close(GuideModuleName.GuildPass)
        local role = Scene.Instance:GetRoleByObjId(self.data.obj_id)
        local pos_x, pos_y
        if role then
            pos_x, pos_y = role:GetLogicPos()
        else
            pos_x, pos_y = self.data.pos_x, self.data.pos_y
        end

        if GuajiWGCtrl.CheckRange(pos_x, pos_y, 3) then
            GuildAnswerWGCtrl.Instance:SendAcceptOperate(self.data.role_id, 1)
        else
            GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
                if self.data and self.data.role_id then--外网报错,加判空处理
                    GuildAnswerWGCtrl.Instance:SendAcceptOperate(self.data.role_id, 1)
                end
            end)
            GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), pos_x, pos_y, 3)
        end


    end
end

function GuildPassRoleRender:UpdateBtn(elapse_time, total_time)
    if not self.node_list["btn_text"] or not self.node_list["btn_operate"] then
        return 
    end
    if self.data.type == GuildPassView.STATE.SEND then
        self.node_list["btn_text"].text.text = math.ceil(total_time - elapse_time)
        XUI.SetButtonEnabled(self.node_list["btn_operate"], false)
    end
end

function GuildPassRoleRender:CompleteBtn()
    if not self.node_list["btn_text"] or not self.node_list["btn_operate"] then
        return 
    end
    if self.data.type == GuildPassView.STATE.SEND then
        self.node_list["btn_text"].text.text = Language.GuildAnswer.PassSend
        XUI.SetButtonEnabled(self.node_list["btn_operate"], true)
    end
end

function GuildPassRoleRender:CancelCountDown()
    if self.count_down_key ~= nil and CountDownManager.Instance:HasCountDown(self.count_down_key) then
        CountDownManager.Instance:RemoveCountDown(self.count_down_key)
    end
end