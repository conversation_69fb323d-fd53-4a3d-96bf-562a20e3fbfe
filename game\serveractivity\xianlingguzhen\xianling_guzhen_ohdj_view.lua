XianLingGuZhenOHDJView = XianLingGuZhenOHDJView or BaseClass(SafeBaseView)

function XianLingGuZhenOHDJView:__init()
	self.full_screen = true
	self:SetMaskBg()
	self.is_safe_area_adapter = true
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_guzhen_ohdj")
end

function XianLingGuZhenOHDJView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesPNG("a3_jgjf_bg_2")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true, show_bind_gold = false,
			show_coin = false, show_silver_ticket = false,
			}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["moey_bar_pos"].transform)
	end

    if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end

    if not self.attr_list then
        self.attr_list = AsyncBaseGrid.New()

        local bundle = "uis/view/xianling_guzhen_prefab"
		local asset = "ph_ohdj_attr_cell"
		self.attr_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list.attr_list,
			assetBundle = bundle, assetName = asset, itemRender = OHDJAttrItemCellRender})
		self.attr_list:SetStartZeroIndex(false)
    end

	if not self.skill_list then
		self.skill_list = {}

		for i = 1, 3 do
			self.skill_list[i] = OHDJSkillItemCellRender.New(self.node_list["skill_group_" .. i])
		end 
	end

    XUI.AddClickEventListener(self.node_list.btn_to_get, BindTool.Bind(self.OnClickToGoBtn, self))
end

function XianLingGuZhenOHDJView:ReleaseCallBack()
    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end

    if self.attr_list then
        self.attr_list:DeleteMe()
        self.attr_list = nil
    end

	if self.skill_list then
		for k, v in pairs(self.skill_list) do
			v:DeleteMe()
		end

		self.skill_list = nil
	end
end

function XianLingGuZhenOHDJView:OnFlush()
    local other_cfg = XianLingGuZhenWGData.Instance:GetOtherCfg()

    if IsEmptyTable(other_cfg) then
        return
    end

    self:FlushModel(other_cfg)

    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.model_show_itemid)
    if not IsEmptyTable(item_cfg) then
        self.node_list.reward_name.text.text = item_cfg.name  
        self.node_list.right_time.text.text = item_cfg.name  
        self.node_list.name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        self.item:SetData({item_id = other_cfg.model_show_itemid})
        self.node_list.use_level.text.text = string.format(Language.Tip.ShiYongDengJiNew, F2_TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
        self.node_list.desc1.text.text = ItemWGData.Instance:GetItemConstDesc(item_cfg)

        local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(other_cfg.model_show_itemid, item_cfg.sys_attr_cap_location)
        local attr_desc, capability, attr_list = ItemShowWGData.Instance:GetItemAttrDescAndCap({item_id = other_cfg.model_show_itemid}, sys_type, false)
        self.node_list.cap_value.text.text = capability

        self.attr_list:SetDataList(attr_list)

		self:FlushSkillInfo(other_cfg.model_show_itemid, item_cfg)
    end
end

function XianLingGuZhenOHDJView:FlushSkillInfo(item_id, item_cfg)
	local display_type = item_cfg.is_display_role
	if nil == display_type or display_type == 0 or display_type == "" then
		return
    end
    --如果是材料，需要显示合成的目标装备的技能，
    local revert_item_id = ItemWGData.Instance:GetComposeProductid(item_id)
    local temp_data = {item_id = revert_item_id}
	local data = nil
	if display_type == DisplayItemTip.Display_type.BABY then --宝宝
		data = MarryWGData.Instance:GetBabySkillData(temp_data)
	elseif display_type == DisplayItemTip.Display_type.TIANSHEN then --天神
		data = TianShenWGData.Instance:GetTianShenSkillData(temp_data)
	elseif display_type == DisplayItemTip.Display_type.KUN then --鲲
		data = NewAppearanceWGData.Instance:GetKunTipsSkillData(temp_data)
	elseif display_type == DisplayItemTip.Display_type.MOUNT_LINGCHONG then --坐骑
        data = NewAppearanceWGData.Instance:GetQiChongSkillData(temp_data, MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
    elseif display_type == DisplayItemTip.Display_type.LINGCHONG then --灵宠
		data = NewAppearanceWGData.Instance:GetQiChongSkillData(temp_data, MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG)
	end

	if data then
		for i = 1, 3 do
			self.skill_list[i]:SetData(data["data_list_" .. i])
		end
	end
end

function XianLingGuZhenOHDJView:FlushModel(model_cfg)
	if IsEmptyTable(model_cfg)then
		return
	end

	-- 形象展示
	local display_data = {}
	display_data.should_ani = true
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = model_cfg.model_bundle_name
	display_data.asset_name = model_cfg.model_asset_name
	display_data.render_type = model_cfg.model_show_type - 1
	display_data.need_wp_tween = true
	display_data.hide_model_block = false
	display_data.model_click_func = function ()
        TipWGCtrl.Instance:OpenItem({item_id = model_cfg.model_show_itemid})
    end
	local pos_x, pos_y, pos_z = 0, 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
	end
	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.model.rect, pos_x, pos_y, pos_z)

	if model_cfg.model_pos and model_cfg.model_pos ~= "" then
		local pos_list = string.split(model_cfg.model_pos, "|")
		local posx = tonumber(pos_list[1]) or 0
		local posy = tonumber(pos_list[2]) or 0
		local posz = tonumber(pos_list[3]) or 0
		display_data.model_adjust_root_local_position = Vector3(posx, posy, posz)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rot_list = string.split(model_cfg.rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0
		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_cfg.model_scale and model_cfg.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_cfg.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.XL
	-- if model_cfg.rotation and model_cfg.rotation ~= "" then
	-- 	local rotation_tab = string.split(model_cfg.rotation,"|")
	-- 	self.node_list.model.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	-- end


	-- local scale = tonumber(model_cfg.model_scale) or 1
	-- self.node_list.model.transform:SetLocalScale(scale, scale, scale)

	self.model_display:SetData(display_data)

end

function XianLingGuZhenOHDJView:OnClickToGoBtn()
    ViewManager.Instance:Open(GuideModuleName.XianLingGuZhen)
    self:Close()
end

-----------------------------------------OHDJAttrItemCellRender----------------------------------
OHDJAttrItemCellRender = OHDJAttrItemCellRender or BaseClass(BaseRender)

function OHDJAttrItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    self.node_list.attr_name.text.text = self.data.attr_name
    self.node_list.attr_value.text.text = self.data.value_str
end

------------------------------------------------OHDJSkillItemCellRender-----------------------------------------------

OHDJSkillItemCellRender = OHDJSkillItemCellRender or BaseClass(BaseRender)

function OHDJSkillItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end
	
	self.view:SetActive(true)
	local bundel, asset = ResPath.GetF2TipImages(self.data.title_img)
	self.node_list["title_img"].image:LoadSprite(bundel, asset, function()
		self.node_list["title_img"].image:SetNativeSize()
	end)

	for i = 1, 5 do
		local info = self.data[i]
		local has_skill_data = not IsEmptyTable(info)

		if self.node_list["skill_" .. i] then
			local bundle, asset = ResPath.GetCommonImages("a2_jj_bg_jinengkuang")
			self.node_list["skill_" .. i].image:LoadSprite(bundle, asset, function()
				self.node_list["skill_" .. i].image:SetNativeSize()
			end)
			self.node_list["skill_" .. i]:CustomSetActive(has_skill_data)

			if has_skill_data and info.click_func then
				self.node_list["skill_" .. i].button:AddClickListener(info.click_func)
			end
		end

		if has_skill_data then
			if self.node_list["skill_icon_" .. i] then
				self.node_list["skill_icon_" .. i].image:LoadSprite(info.bundle, info.asset, function()
					self.node_list["skill_icon_" .. i].image:SetNativeSize()
				end)
			end

			if self.node_list["skill_level_" .. i] then
				self.node_list["skill_level_" .. i].text.text = info.skill_level or ""
				self.node_list["skill_level_" .. i]:CustomSetActive(info.skill_level ~= nil)

			end
		end
	end

end