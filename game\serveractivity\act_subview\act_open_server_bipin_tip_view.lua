OpenServerBiPinTipView = OpenServerBiPinTipView or BaseClass(SafeBaseView)

function OpenServerBiPinTipView:__init(view_name)
	self.view_name = "OpenServerBiPinTipView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_bipin_tip_view")
	self:SetMaskBg(true, true)
	self:InitParam()
end

function OpenServerBiPinTipView:LoadCallBack()
	self:InitPanel()
	self:InitListener()
end

function OpenServerBiPinTipView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	if self.t_model_display then
		self.t_model_display:DeleteMe()
		self.t_model_display = nil
	end
end

function OpenServerBiPinTipView:OnFlush(param_t)
	self:RefreshView()
	self:FlushRewardModel()
end

function OpenServerBiPinTipView:InitParam()
	self.m_rush_type = 0
end

function OpenServerBiPinTipView:InitPanel()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	self.t_model_display = OperationActRender.New(self.node_list.model_root)
	self.t_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
end

function OpenServerBiPinTipView:InitListener()
	XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind(self.OnClickGotoBtn, self))
end

function OpenServerBiPinTipView:SetBiPinRushType(rush_type)
	self.m_rush_type = rush_type
end

function OpenServerBiPinTipView:RefreshView()
	local rush_type = self.m_rush_type
	local rank_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
	if not rank_cfg then
		return
	end

	local target_reward_data_list = ServerActivityWGData.Instance:GetRushLastRewardItemListCfg(rush_type)
	local target_value_list = ServerActivityWGData.Instance:GetRushReachGoalList(rush_type)

	local select_index = 0
	local goal_reward_flag = 0
	local goal_reward_fetch = 0
	local can_get_reward = false
	for i = 1, #target_reward_data_list do
		goal_reward_flag = ServerActivityWGData.Instance:GetOpenServerGoalFlags(rush_type, i - 1)
		goal_reward_fetch = ServerActivityWGData.Instance:GetOpenServerGoalFetchFlags(rush_type, i - 1)
		can_get_reward = goal_reward_flag == 1 and goal_reward_fetch == 0
		if can_get_reward then
			select_index = i
			break
		elseif goal_reward_flag == 0 then
			select_index = i
			break
		end
	end

	self.node_list.red_point:SetActive(can_get_reward)

	if select_index <= 0 then
		select_index = #target_reward_data_list -- 领完了显示最后一个
		self.node_list.goto_btn_label.text.text = Language.OpenServer.BiPinTipBtnStr_3
	else
		self.node_list.goto_btn_label.text.text = can_get_reward and Language.OpenServer.BiPinTipBtnStr_2 or Language.OpenServer.BiPinTipBtnStr_1
	end

	local reward_list = SortTableKey(target_reward_data_list[select_index])
	self.reward_list:SetDataList(reward_list)

	local rank_name = Language.OpenServer.LingQuLimit2[rush_type] or ""
	local rank_unit = Language.OpenServer.Unit[rush_type] or ""
	local target_value = target_value_list[select_index - 1] or 0

	local body_show_type = rush_type == 3 and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
	local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, target_value) or {}
	local rank_name = Language.OpenServer.LingQuLimit2[rush_type] or ""
	self.node_list.desc_label.text.text = string.format(Language.OpenServer.BiPinRewardDesc_3, rank_name, cfg.grade_num or 0, cfg.star_num or 0)
end

-- 找出前三奖励的模型显示出来
function OpenServerBiPinTipView:FlushRewardModel()
	local rank_reward_data_list = ServerActivityWGData.Instance:GetOpenServerActivityRewardConfig(self.m_rush_type)
	local reward_list = rank_reward_data_list[1] or {}
	local reward_item = reward_list.reward_item
	if reward_item then
		local item_cfg = nil
		local show_item_id = 0
		for i=0,#reward_item do
			item_cfg = ItemWGData.Instance:GetItemConfig(reward_item[i].item_id)
			if item_cfg and item_cfg.is_display_role > 0 then
				show_item_id = item_cfg.id
				break
			end
		end

		if show_item_id > 0 then
			local info = {}
		    info.should_ani = false
			info.item_id = show_item_id
		    info.render_type = 0
			self.t_model_display:SetData(info)

			self.node_list.ex_desc.text.text = item_cfg.name

			local cap_value = ItemShowWGData.CalculateCapability(show_item_id)
			self.node_list.cap_value.text.text = cap_value
		end
	end
end

function OpenServerBiPinTipView:OnClickGotoBtn()
	ServerActivityWGCtrl.Instance:OpenOpenserverCompetition("kf_act", TabIndex.act_bipin, {rush_type = self.m_rush_type})
	self:Close()
end