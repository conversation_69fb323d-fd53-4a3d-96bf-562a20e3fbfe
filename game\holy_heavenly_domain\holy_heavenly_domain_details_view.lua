function HolyHeavenlyDomainView:LoadIndexCallBackDetails()
    self.detail_show_role_id_cache = {}
    self.details_show_model_list = {}
    self.node_list.desc_explain1.text.text = Language.HolyHeavenlyDomain.DetailsDesc1
    local _, _, open_time_str, close_time_str = HolyHeavenlyDomainWGData.Instance:GetDetailsSeasonOpenTime()
    self.node_list.desc_season_open_time.text.text = string.format(Language.HolyHeavenlyDomain.SeasonOpenTime, COLOR3B.L_GREEN, open_time_str, close_time_str)

    if not self.detail_show_item_list then
        self.detail_show_item_list = AsyncListView.New(ItemCell, self.node_list.detail_show_item_list)
        self.detail_show_item_list:SetStartZeroIndex(true)
    end

    local datalist = HolyHeavenlyDomainWGData.Instance:GetDetailShowItemList()
    self.detail_show_item_list:SetDataList(datalist)

    XUI.AddClickEventListener(self.node_list.btn_details_tujie, BindTool.Bind(self.OnClickDetailsTuJie, self))
    XUI.AddClickEventListener(self.node_list.btn_details_team, BindTool.Bind(self.OnClickDetailsTeam, self))
    XUI.AddClickEventListener(self.node_list.btn_details_war, BindTool.Bind(self.OnClickDetailsWar, self))
    XUI.AddClickEventListener(self.node_list.btn_details_war_situation, BindTool.Bind(self.OnClickDetailsWarSituation, self))
    XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickDetailsTips, self))
end

function HolyHeavenlyDomainView:ShowIndexCallBackDetails()
end

function HolyHeavenlyDomainView:ReleaseCallBackDetails()
    if self.details_show_model_list then
		for k, v in pairs(self.details_show_model_list) do
			v:DeleteMe()
		end

		self.details_show_model_list = nil
	end

    self.detail_show_role_id_cache = nil

    if self.detail_show_item_list then
        self.detail_show_item_list:DeleteMe()
        self.detail_show_item_list = nil
    end
end

function HolyHeavenlyDomainView:OnFlushDetails(param_t)
    local season_title, season_time = HolyHeavenlyDomainWGData.Instance:GetDetailsSeasonAndTime()
    self.node_list.desc_season_state.text.text = season_title
    self.node_list.desc_season_time.text.text = season_time

    local details_remid = HolyHeavenlyDomainWGData.Instance:GetDetailsRemind()
    self.node_list.btn_details_war_remind:CustomSetActive(details_remid > 0)

    self:FlushDetailsModel()
    -- self:FlushDetailsCrossProgress()

    local is_off_season = HolyHeavenlyDomainWGData.Instance:IsInTheOffSeason()
    self.node_list.btn_details_war_text.text.text = Language.FlagGrabbingBattlefield.DetailWarBtnText[is_off_season and 1 or 0]
end

function HolyHeavenlyDomainView:FlushDetailsModel()
    local datalist = HolyHeavenlyDomainWGData.Instance:GetPlayerScoreRankInfoByType(CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.SCORE_RANK)
    local user_id = RoleWGData.Instance:InCrossGetOriginUid()
    local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true}

    local set_model_data = function (role_vo, index)
        if self.details_show_model_list and self.details_show_model_list[index] then
            self.details_show_model_list[index]:SetModelResInfo(role_vo, ignore_table, function()
                -- self.details_show_model_list[index]:PlayRoleShowAction()
            end)

            self.node_list["role_name" .. index].text.text = role_vo.role_name
            -- self.node_list["role_server" .. index].text.text = string.format(Language.HolyHeavenlyDomain.ServerName, role_vo.server_id)
            self.details_show_model_list[index]:FixToOrthographic(self.root_node_transform)
        end
    end

	for i = 1, 3 do
        local rank_cfg = HolyHeavenlyDomainWGData.Instance:GetHofPersonScoreRankCfgByRank(i)
        local title_id = rank_cfg and rank_cfg.show_title or 0

        if title_id > 0 then
            self.node_list["role_title_" .. i]:ChangeAsset(ResPath.GetTitleModel(title_id))
        end

		local role_id = ((datalist[i] or {}).uuid or {}).temp_low or -1
        local plat_type = ((datalist[i] or {}).uuid or {}).temp_high or -1
		local has_role_data = role_id > 0

        self.node_list["xuwei_yidai" .. i]:CustomSetActive(not has_role_data)
        self.node_list["model_display" .. i]:CustomSetActive(has_role_data)

        if not has_role_data then
            self.node_list["role_name" .. i].text.text = Language.HolyHeavenlyDomain.XuWeiYiDai
        end

        local usid = (datalist[i] or {}).usid

        if nil ~= usid then
            -- local plat, server = LLStrToInt(usid)
            local camp_id = HolyHeavenlyDomainWGData.Instance:GetCountryIdByServerUsid(usid)
            local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(camp_id)
            self.node_list["role_server" .. i].text.text = camp_cfg and camp_cfg.camp_name or ""
        else
            self.node_list["role_server" .. i].text.text = ""
        end

        local role_id_cache = self.detail_show_role_id_cache[i] or -1

        if role_id_cache ~= role_id then
            self.detail_show_role_id_cache[i] = role_id

            if not self.details_show_model_list[i] then
                local node = self.node_list["model_display" .. i]
                self.details_show_model_list[i] = RoleModel.New()
                local display_data = {
                    parent_node = node,
                    camera_type = MODEL_CAMERA_TYPE.BASE,
                    -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
                    rt_scale_type = ModelRTSCaleType.S,
                    can_drag = true,
                }
                
                self.details_show_model_list[i]:SetRenderTexUI3DModel(display_data)
                -- self.details_show_model_list[i]:SetUI3DModel(node.transform, node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
            else
                self.details_show_model_list[i]:ClearModel()
            end

            if user_id == role_id then
                local role_vo = RoleWGData.Instance:GetRoleVo()
                set_model_data(role_vo, i)
            else
                BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
                    set_model_data(protocol, i)
                end, plat_type, true)
            end
        else
            if self.details_show_model_list[i] then
                self.details_show_model_list[i]:PlayLastAction()
            end
        end
	end
end

function HolyHeavenlyDomainView:OnClickDetailsTuJie()
    HolyHeavenlyDomainWGCtrl.Instance:OpenTuJieView()
end

function HolyHeavenlyDomainView:OnClickDetailsTeam()
    local is_in_the_offseason = HolyHeavenlyDomainWGData.Instance:IsInTheOffSeason()

    if is_in_the_offseason then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyHeavenlyDomain.IsIntTheOffSeasonNoew)
    else
        ViewManager.Instance:Open(GuideModuleName.HolyHeavenlyDomainCampView)
    end
end

function HolyHeavenlyDomainView:OnClickDetailsWar()
    ViewManager.Instance:Open(GuideModuleName.HolyHeavenlyDomainMapView)
end

function HolyHeavenlyDomainView:OnClickDetailsWarSituation()
    HolyHeavenlyDomainWGCtrl.Instance:OpenWarSituationView()
end

function HolyHeavenlyDomainView:OnClickDetailsTips()
    RuleTip.Instance:SetContent(Language.HolyHeavenlyDomain.MapDetailTipContent, Language.HolyHeavenlyDomain.MapDetailTipTitle)
end