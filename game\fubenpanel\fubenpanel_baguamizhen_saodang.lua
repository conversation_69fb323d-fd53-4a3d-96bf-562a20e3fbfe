BaGuaMiZhenSaoDangView = BaGuaMiZhenSaoDangView or BaseClass(SafeBaseView)

function BaGuaMiZhenSaoDangView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_baguamizhen_saodang")

	self.saodang_index = -1
	self.item_data_change_callback = BindTool.Bind1(self.ShowIndexCallBack, self)
end

function BaGuaMiZhenSaoDangView:__delete()
end

function BaGuaMiZhenSaoDangView:ReleaseCallBack()
	if self.saodang_cell then
		self.saodang_cell:DeleteMe()
		self.saodang_cell = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function BaGuaMiZhenSaoDangView:ShowIndexCallBack()
	self:Flush()
end

function BaGuaMiZhenSaoDangView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.FuBenPanel.TianShenSaoDangXiaoHao
	self:SetSecondView(nil, self.node_list["size"])
	self.saodang_cell = ItemCell.New(self.node_list["ph_cell"])
	self.node_list["btn_ok"].button:AddClickListener(BindTool.Bind(self.OnClinkOkHandler, self))
	XUI.AddClickEventListener(self.node_list.btn__pet_saodang_cancel, BindTool.Bind1(self.BaGuaMiZhenSaoDangViewClose, self))
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function BaGuaMiZhenSaoDangView:ShowIndexCallBack()
	self:Flush()
end

function BaGuaMiZhenSaoDangView:BaGuaMiZhenSaoDangViewClose()
	self:Close()
end

function BaGuaMiZhenSaoDangView:OnFlush()
	local need_item_id,need_num = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenItemId()
	if self.saodang_cell then
		self.saodang_cell:SetData({item_id = need_item_id})
	end
	local have_item_num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
	if have_item_num < need_num then
		self.node_list["lbl_item_num"].text.text = "<color=#ff0000>"..have_item_num.."</color>" .. "/" .. need_num
	else
		self.node_list["lbl_item_num"].text.text = have_item_num .. "/" .. need_num
	end
end

function BaGuaMiZhenSaoDangView:SetPetIndex(index)
	self.saodang_index = index
	self:Open()
end

function BaGuaMiZhenSaoDangView:OnClinkOkHandler()
	local can_enter_times = FuBenPanelWGData.Instance:GetBaGuaMiZhenCanEnterTimes()
	local can_enter_time = can_enter_times - 1
	--判断扫荡券
	local need_item_id,need_num,seq = BaGuaMiZhenWGData.Instance:GetBaGuaMiZhenItemId()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
	--物品不足
	if item_num < need_num then
		GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, need_item_id, need_num, seq)
		return
	end
	--如果次数足够
	if can_enter_times > 0 then
		FuBenWGCtrl.Instance:SendSwipeFB(FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB, self.saodang_index)
		if can_enter_time <= 0 then
			self:BaGuaMiZhenSaoDangViewClose()
		end
	end
end