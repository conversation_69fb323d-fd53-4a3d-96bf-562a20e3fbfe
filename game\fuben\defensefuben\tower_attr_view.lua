TowerAttrView = TowerAttrView or BaseClass(SafeBaseView)

function TowerAttrView:__init()
	-- self:SetIaAnyClickClose(true)
	-- self.is_modal = true
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/defense_fb_ui_prefab", "layout_tower_attr")
	self.one_level_tower_cfg = {}
end

function TowerAttrView:ReleaseCallBack()
end

function TowerAttrView:LoadCallBack()
	self.mask_bg.image.color = Color.New(0,0,0,0)
	local defense_tower_cfg = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerList()
	for k,v in ipairs(defense_tower_cfg)do
		if v.tower_level == 1 then
			self.one_level_tower_cfg[v.tower_type] = v
		end
	end
	XUI.AddClickEventListener(self.node_list.btn_close_window,BindTool.Bind(self.Close,self))
end

function TowerAttrView:ShowIndexCallBack(index)
	self:Flush()
end

function TowerAttrView:OnFlush(param_t, index)
	self.node_list.itemname_txt.text.text = self.one_level_tower_cfg[self.index].tower_name
	self.node_list.lbl_tower_build.text.text = self.one_level_tower_cfg[self.index].need_douhun
	self.node_list.rich_desc.text.text = self.one_level_tower_cfg[self.index].preview

	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	local str = defense_data.douhun >= self.one_level_tower_cfg[self.index].need_douhun and Language.DefenseFb.CanXiuJian or Language.DefenseFb.CanNotXiuJian
	local color = defense_data.douhun >= self.one_level_tower_cfg[self.index].need_douhun and COLOR3B.GREEN or COLOR3B.RED
	self.node_list.lbl_xiujian.text.text = ToColorStr(str,color) 
end

function TowerAttrView:SetIndex(index)
	self.index = index
	self:Open()
	self:Flush()
end
