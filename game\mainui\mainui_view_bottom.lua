--[[
    [”button的名字“] = {
        [1] = 模块名, [2] = 要跳转的标签值（没有子标签的可以不填）,
        ["col"] = 1, 字段表示这个按钮在第几列（最右边为第一列）（用于功能开启）
    }
]]
local ViewNameButton = {
	BtnRoleBagView = {GuideModuleName.Bag, "rolebag_bag_all", ["remind"] = RemindName.Bag},							-- 背包按钮

	BtnPlayerView = {GuideModuleName.RoleView, ["remind"] = RemindName.RoleView},									-- 角色面板
	BtnRoleskill = {GuideModuleName.SkillView, TabIndex.skill_upgrade, ["remind"] = RemindName.SkillView}, 			-- 技能面板
	BtnEquipmentView = {GuideModuleName.Equipment, ["remind"] = RemindName.Equipment},								-- 装备面板
	-- BtnTransFerView = {GuideModuleName.TransFer, ["remind"] = RemindName.TransFer},        			-- 转生面板

	BtnFPRoleBag = {GuideModuleName.Bag, "rolebag_bag_all", ["remind"] = RemindName.FB_Bag},						-- 背包按钮 - 副屏
	BtnBianShen = {GuideModuleName.TianShenView, ["remind"] = RemindName.TianShen, ["fp_special_fun_open"] = false},	-- 天神面板
	BtnFPFashion = {GuideModuleName.NewAppearanceWGView, ["remind"] = RemindName.NewAppearance},					-- 时装
	-- BtnHmGodView = {GuideModuleName.HmGodView, ["remind"] = RemindName.HmGodView},						            -- 鸿蒙神藏

	BtnFPChengJiu = {GuideModuleName.Achievement, ["remind"] = RemindName.AchievementParentTable},					-- 成就按钮 - 副屏
	BtnFiveElements = {GuideModuleName.FiveElementsView, ["remind"] = RemindName.FiveElements},					    -- 五行  策划雪威说用五行替换社交

	--BtnFPZhiZunView = {GuideModuleName.SupremeFieldsWGView, ["remind"] = RemindName.SupremeFields}, 				-- 至尊领域 - 副屏
	BtnMingWenView = {GuideModuleName.MingWenView, ["remind"] = RemindName.MingWen}, 								-- 铭文系统
	--BtnSworn = {GuideModuleName.SwornView, ["remind"] = RemindName.Sworn},   										-- 结义金兰
	BtnHomes = {GuideModuleName.HomesView, ["remind"] = RemindName.Homes},                                         	-- 家园
	
	BtnShanHaiJingView = {GuideModuleName.ShanHaiJingView, ["remind"] = RemindName.ShanHaiJing},					-- 山海经																				--上古神灵
	BtnyishouView = {GuideModuleName.ShenShou, ["remind"] = RemindName.ShenShouRemind},									-- 山海经异兽

	BtnGuildView = {GuideModuleName.Guild, ["remind"] = RemindName.Guild}, 											-- 仙盟面板
	BtnComposeView = {GuideModuleName.Compose, ["remind"] = RemindName.Compose},									-- 合成面板
	BtnFPRank = {GuideModuleName.Rank, ["remind"] = RemindName.Rank},												-- 排行榜	- 副屏

	btnScreenShot = {GuideModuleName.ScreenShotView},																-- 拍照		-副屏
	BtnSettingView = {GuideModuleName.Setting, "setting_screen"},													-- 设置面板	-副屏
	BtnProposeView = {GuideModuleName.FeedBackView,},																-- 建议		-副屏

	-- BtnFPFuBen = {GuideModuleName.FuBenPanel, ["remind"] = RemindName.FuBenPanel},									-- 副本面板 - 副屏

	BtnFPActJJC = {GuideModuleName.ActJjc, "arena_enter", ["remind"] = RemindName.ActJjc, ["fp_special_fun_open"] = true},	-- 战场		- 副屏
	--BtnFPFuLi = {GuideModuleName.Welfare, ["remind"] = RemindName.Welfare},											-- 福利		- 副屏
	BtnFPWH = {GuideModuleName.WuHunView, ["remind"] = RemindName.WuHunView},										-- 武魂		- 副屏
	BtnFPEsoterica = {GuideModuleName.EsotericaView, ["remind"] = RemindName.Esoterica},								-- 仙法		- 副屏
	--BtnFPBiZuo = {GuideModuleName.BiZuo, "bizuo_bizuo", ["remind"] = RemindName.BiZuo},								-- 日常面板 - 副屏

	BtnFairyLandEquipView = {GuideModuleName.FairyLandPlaneView, 0, ["remind"] = RemindName.FairyLandEquip}, 	-- 仙界装备
	--BtnFPShop = {GuideModuleName.Shop, ["remind"] = RemindName.Shop,},												-- 商店 - 副屏

	BtnFPTianShenLiLian = {GuideModuleName.TianShenLiLianView, ["remind"] = RemindName.TianShenLiLianEnter},				-- 历练
	BtnDujie = {GuideModuleName.DujieView, ["remind"] = RemindName.Dujie, ["fp_special_fun_open"] = true},					-- 渡劫
	BtnDragonTemple = {GuideModuleName.DragonTempleView, ["remind"] = RemindName.DragonTemple},						-- 龙神殿
	-- BtnFPGuiXuDream = {GuideModuleName.GuiXuDreamView, ["remind"] = RemindName.GuiXuDreamView},						-- 归墟梦演
	BtnArtifact = {GuideModuleName.ArtifactView, ["remind"] = RemindName.Artifact},						            -- 仙魔神器
	BtnFPCharm = {GuideModuleName.MultiFunctionView, ["remind"] = RemindName.MultiFunctionView},                    -- 符咒
	-- BtnCultivationRealm = {GuideModuleName.CultivationView, ["remind"] = RemindName.CultivationTotal},              -- 修为
	BtnCultivationRealm = {GuideModuleName.ControlBeastsView, ["remind"] = RemindName.ControlBeasts, ["fp_special_fun_open"] = true},
-- 
	-- BtnFPCustomized = {GuideModuleName.CustomizedSuitView, ["remind"] = RemindName.CustomizedSuitView},                        --- 定制套装
	-- BtnCangJinShop = {GuideModuleName.CangJinShopView, ["remind"] = RemindName.CangJinExchange},					-- 藏金商铺
	BtnFPMengLing = {GuideModuleName.MengLingView, ["remind"] = RemindName.MengLingView},     -- 梦灵
	BtnFPConquestWar = {GuideModuleName.ConquestWarView, ["remind"] = RemindName.ConquestWarView, ["fp_special_fun_open"] = true},	-- 征战
	BtnWardrobeView = {GuideModuleName.WardrobeView, ["remind"] = RemindName.Wardrobe},								-- 衣橱
	BtnFPThunderView = {GuideModuleName.ThunderManaSelectView, ["remind"] = RemindName.ThunderManaSelectView},      -- 雷法	
}

-- 副屏审核服屏蔽按钮
-- local FP_AUDIT_VERSION_HIDE_BTN = {
-- 	BtnFPFuLi = true,
-- 	BtnFairyLandEquipView = true,
-- 	BtnFPRank = true,
-- }

function MainUIView:BottomLoadCallBack()
	local icon_node_list = {}
	for k,v in pairs(ViewNameButton) do
		local root = self.node_list[k]
		if root then
			local icon = root.transform:FindByName("Icon")
			local remind = root.transform:FindByName("RedPoint")
			root.Icon = U3DObject(icon, nil, self)
			root.remind = remind
			root.lock = root.transform:FindByName("lock")
			root.button:AddClickListener(BindTool.Bind2(self.OnClickOpenView, self, v))
			root.fun_name = root.transform:FindByName("fun_name")
			root.lock_name = root.transform:FindByName("lock_name")
			root.bg = root.transform:FindByName("bg")
			root.bg_lock = root.transform:FindByName("bg_lock")
			root.name_bg = root.transform:FindByName("name_bg")
			root.name_bg_lock = root.transform:FindByName("name_bg_lock")

			local remind_key = v["remind"]
			if remind_key and not self.icon_remind_list[remind_key] then
				self.icon_remind_list[remind_key] = k
				self.icon_remind_list_num[remind_key] = 0
				RemindManager.Instance:Bind(self.remind_change, remind_key)
			end

			icon_node_list[k] = root
			self:SetMainIconNodeListObj(k, root)
		end
	end

	FunOpen.Instance:RegisterMianUIFPFunOpen(ViewNameButton, icon_node_list)

	-- self.fp_advance_notice = MainUIFPAdvanceNotice.New(self.node_list.fp_advance_notice)
    XUI.AddClickEventListener(self.node_list.btn_click_gather, BindTool.Bind(self.OnClickFindLatelyGather, self))
	XUI.AddClickEventListener(self.node_list.btn_fp_close, BindTool.Bind(self.OnCloseMianUIFuPing, self))
	XUI.AddClickEventListener(self.node_list.btn_fp_close_mask, BindTool.Bind(self.OnCloseMianUIFuPing, self))

	XUI.AddClickEventListener(self.node_list.btn_dizang_redpack, BindTool.Bind(self.OnClickOpenDiZangRedPack, self))
	XUI.AddClickEventListener(self.node_list.btn_open_dizang_redpack, BindTool.Bind(self.OpenDiZangRedPackView, self))
	XUI.AddClickEventListener(self.node_list.btn_close_dizang_redpack, BindTool.Bind(self.OnClickCloseDiZangRedPack, self))
	XUI.AddClickEventListener(self.node_list.fp_btn_head_icon, BindTool.Bind(self.OnClickFPRoleHead, self))
	XUI.AddClickEventListener(self.node_list.fp_btn_role_exp, BindTool.Bind(self.OnClickFPRoleExp, self))

	self.is_error_eff_bag_pos = false
	self:ChangeMoneyFlyToBagPos()
	-- if IS_AUDIT_VERSION then
	-- 	for k,v in pairs(FP_AUDIT_VERSION_HIDE_BTN) do
	-- 		local root = self.node_list[k]
	-- 		if root then
	-- 			root:SetActive(false)
	-- 		end
	-- 	end
	-- end

	self:FPChangeHeadIcon()
end

function MainUIView:ChangeMoneyFlyToBagPos()
	local t_width = UnityEngine.Screen.width
	local t_height = UnityEngine.Screen.height
	local pos_data = self.node_list.role_bag_icon.rect.position
	local is_error_pos = pos_data.x < -t_width or pos_data.x > t_width
	self.is_error_eff_bag_pos = is_error_pos
	local is_empty = self.start_eff_pos == nil
	if is_empty then
		self.start_eff_pos = {x = 0, y = 500, z = 100}
		self.end_eff_pos = {x = 38, y = 505, z = 100}
	end

	if not is_error_pos then
		self.start_eff_pos = self.node_list.get_main_task_yuanbao_pos.rect.position
		self.end_eff_pos = self.node_list.role_bag_icon.rect.position
	end
end

function MainUIView:Bottom__ReleaseCallBack()
	self:ClearMainIconNodeListObj()
	self:CleanDiZangRedPackTimer()

	-- if self.fp_advance_notice then
	-- 	self.fp_advance_notice:DeleteMe()
	-- 	self.fp_advance_notice = nil
	-- end

	if self.fp_role_head_cell then
		self.fp_role_head_cell:DeleteMe()
		self.fp_role_head_cell = nil
	end
end

function MainUIView:OnCloseMianUIFuPing()
	self.toggle_menu.toggle.isOn = not self.bottom_toggle_state
end

-- 打开对应的面板
function MainUIView:OnClickOpenView(data)
	if data == nil or next(data) == nil then
		return
	end
	
	local view_name = data[1]
	local tab_index = data[2]
	if GuideModuleName.Equipment == view_name then				-- 装备
		local data_list = EquipWGData.Instance:GetDataList() -- 无装备，不给打开装备面板
		if IsEmptyTable(data_list) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NeedWearEquip)
			return
		end
		
	elseif GuideModuleName.Compose == view_name then			-- 合成
		local flag = RoleWGData.Instance:GetRoleSex() == GameEnum.MALE
		tab_index = flag and TabIndex.other_compose_eq_hecheng_one or TabIndex.other_compose_eq_hecheng_two
	elseif GuideModuleName.ScreenShotView == view_name then
		ScreenShotWGCtrl.Instance:ChangeToScreenShotState()
		return
	end

	--系统预告相关系统特殊处理
	local fun_cfg = FunOpen.Instance:GetFunByName(view_name)
	local is_open = FunOpen.Instance:GetFunIsOpened(view_name)
	if fun_cfg and fun_cfg.trigger_type == GuideTriggerType.DayAndSpecialCondition and not is_open then
		is_open = FunOpen.Instance:GetFunIsOpened(FunName.SystemForceLongShenView)
		local is_show = SystemForceWGData.Instance:GetActIsOpenBySeq(fun_cfg.trigger_param)
		if is_open and is_show then
			ViewManager.Instance:Open(GuideModuleName.SystemForceLongShenView, 0, "jump_index", {jump_index = fun_cfg.trigger_param + 1})
			self:OnCloseMianUIFuPing()
		end
	end

	FunOpen.Instance:OpenViewByName(view_name, tab_index)
end

-- 未加仙盟，特效提醒
function MainUIView:CheckGuildEff()
	self.node_list.no_guild_eff:SetActive(RoleWGData.Instance.role_vo.guild_id == 0)
end

function MainUIView:GetBottomButton()
	return ViewNameButton
end

function MainUIView:GetOneBottomButton(key)
	return ViewNameButton[key]
end

-- 获取技能按钮
function MainUIView:GetSkillBottomButton()
	return self.node_list.BtnRoleskill
end

--------------------------
-- 【     采集按钮     】 --
--------------------------
function MainUIView:OnClickFindLatelyGather()
	Scene.Instance:TryFindLatelyGather()
end

function MainUIView:SetClickFindLatelyGatherBtnShow(value)
	if self.find_lately_gather_btn_active ~= nil and self.find_lately_gather_btn_active == value then
		return
	end

	self.find_lately_gather_btn_active = value
	self.node_list.root_lately_gather:SetActive(value)
end

function MainUIView:HideFindLatelyGatherBtn()
	self.find_lately_gather_btn_active = false
	self.node_list.root_lately_gather:SetActive(false)
end

function MainUIView:GetLatelyGatherBtnState()
	return self.find_lately_gather_btn_active
end
--------------------------
-- 【   采集按钮 end  】 --
--------------------------

-- 多倍图标
function MainUIView:FlushDuoBeiState()
	local is_show = ActivityWGData.Instance:GetHasDuoBeiInCopy()
	if self.node_list["fuben_duobei"] then
		self.node_list["fuben_duobei"]:SetActive(is_show)
	end

	if self.node_list.zjm_fuben_fanbei then
		self.node_list.zjm_fuben_fanbei:SetActive(is_show)
	end
end

-- 鸿蒙神藏
function MainUIView:FlushHmGodNotice()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local is_all_buy = GodPurchaseWGData.Instance:GetCurShopIsAllBuy()
	-- 先屏蔽一下
	-- if self.node_list["hm_act_open"] then
	-- 	self.node_list["hm_act_open"]:SetActive(activity_info and activity_info.status == ACTIVITY_STATUS.OPEN and not is_all_buy)
	-- end
end

function MainUIView:FlushFPAdvanceNotice()
	-- local data_list = MainuiWGData.Instance:GetFPAdvanceNoticeList()
	-- self.fp_advance_notice:SetData(data_list)
end

----[[ 地藏红包
function MainUIView:FlushDiZangRedPack(param_t)
	if not param_t then
		return
	end

	self:CleanDiZangRedPackTimer()
	local is_show = param_t.is_show
	self.node_list["btn_dizang_redpack"]:CustomSetActive(is_show)
	if is_show then
		self.node_list.btn_dizang_redpack_red:CustomSetActive(param_t.is_remind)
		-- if param_t.is_remind then
        --     self.node_list["btn_dizang_redpack_icon"].animator.enabled = true
        --     self.node_list["btn_dizang_redpack_icon"].animator:SetBool("is_shake", true)
        -- else
        --     self.node_list["btn_dizang_redpack_icon"].animator.enabled = false
		-- end

		-- if param_t.is_first then
		-- 	self.node_list["dizang_big_redpack"]:CustomSetActive(true)
		-- 	UITween.DoUpDownCrashTween(self.node_list["dizang_big_redpack"], {start_scale = u3dpool.vec3(0.5, 0.5, 0.5), scale_time = 0.4})
		-- 	AudioManager.PlayAndForget(ResPath.GetUisVoiceRes("red_pack_come"))
		-- end
	else
		self.node_list["dizang_big_redpack"]:CustomSetActive(false)
		return
	end

	local remain_time = DiZangRedPackWGData.Instance:GetTimeRemaining()
	self:ShowDiZangRedPackTime(remain_time)
	if remain_time > 0 then
		self.dizang_redpack_timer = CountDown.Instance:AddCountDown(remain_time, 0.5,
		function(elapse_time, total_time)
			local time = math.floor(total_time - elapse_time)
			self:ShowDiZangRedPackTime(time)
		end,
		function()
			self:ShowDiZangRedPackTime(0)
		end)
	end

	local show_tips_cd = 300
	local count = 999999999
	self:OnUpdateDiZangRedPackTips()
	self.dizang_redpack_timer2 = GlobalTimerQuest:AddTimesTimer(
		BindTool.Bind(self.OnUpdateDiZangRedPackTips, self), 
		show_tips_cd, count
	)
end

function MainUIView:CleanDiZangRedPackTimer()
    if self.dizang_redpack_timer then
        CountDown.Instance:RemoveCountDown(self.dizang_redpack_timer)
        self.dizang_redpack_timer = nil
	end
	
	if self.dizang_redpack_timer2 then
		GlobalTimerQuest:CancelQuest(self.dizang_redpack_timer2)
        self.dizang_redpack_timer2 = nil
    end
end

function MainUIView:ShowDiZangRedPackTime(time)
	local time_str = TimeUtil.FormatSecondDHM4(time)
	if self.node_list["dizang_redpack_cd1"] then
		self.node_list["dizang_redpack_cd1"].text.text = time_str
	end

	if self.node_list["dizang_redpack_cd2"] then
		self.node_list["dizang_redpack_cd2"].text.text = time_str
	end
end

function MainUIView:OnUpdateDiZangRedPackTips()
	self:ShowDiZangRedPackTips(true)
	ReDelayCall(self, function ()
		self:ShowDiZangRedPackTips(false)
	end, 5, "delay_hide_dizang_redpack_tips")
end

function MainUIView:ShowDiZangRedPackTips(status)
	if self.node_list.dizang_redpack_tips then
		self.node_list.dizang_redpack_tips:CustomSetActive(status)
	end
end

function MainUIView:OnClickOpenDiZangRedPack()
	--[[
	local key = "dzhb_first_remind" .. RoleWGData.Instance:GetOriginUid()
	local flag = PlayerPrefsUtil.GetInt(key)
	if flag ~= 1 then
		self.node_list["dizang_big_redpack"]:CustomSetActive(true)
		PlayerPrefsUtil.SetInt(key, 1)
		return
	end
	--]]
	self:ShowDiZangRedPackTips(false)
	self:OpenDiZangRedPackView()
end

function MainUIView:OpenDiZangRedPackView()
	ViewManager.Instance:Open(GuideModuleName.DiZangRedPackView)
	self:OnClickCloseDiZangRedPack()
end

function MainUIView:OnClickCloseDiZangRedPack()
	self.node_list["dizang_big_redpack"]:CustomSetActive(false)
end
--]]

function MainUIView:FPChangeHeadIcon()
	if not self.fp_role_head_cell then
		self.fp_role_head_cell = BaseHeadCell.New(self.node_list["fp_default_head_icon"])
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	data.is_show_main = true

	self.fp_role_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.fp_role_head_cell:SetData(data)
	self.fp_role_head_cell:SetFrameScale(0.9)
	self.fp_role_head_cell:SetSpineFrameScale(1)
end

function MainUIView:SetFPMainRoleCapability(value)
	self.node_list["desc_fb_role_cap"].text.text = value
end

--设置主角等级
function MainUIView:SetFPMainRoleLevel(value)
	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(value)
	self.node_list.fp_role_level_dianfeng_flag:SetActive(is_vis)
	self.node_list["fp_desc_role_level"].text.text = string.format(Language.Common.Level1, level)
end

function MainUIView:SetFPmaonRoleExp()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local cur_exp = RoleWGData.Instance.GetRoleExpCfgByLv(role_vo.level).exp

	if cur_exp ~= nil then
		self.node_list['fp_slider_role_exp'].slider.value = role_vo.exp/cur_exp
		self.node_list["fp_desc_role_exp"].text.text = CommonDataManager.ConverExpByThousand(role_vo.exp) .. " / " .. CommonDataManager.ConverExpByThousand(cur_exp)
	end
end

function MainUIView:SetFPMainRoleName()
	local role_name = RoleWGData.Instance:GetAttr("name")
	self.node_list.fp_desc_role_name.text.text = role_name
end

function MainUIView:SetFPRoleViewRemind(remind)
	if self.node_list.fp_role_view_remind then
		self.node_list.fp_role_view_remind:CustomSetActive(remind)
	end
end

function MainUIView:OnClickFPRoleHead()
	ViewManager.Instance:Open(GuideModuleName.RoleView, TabIndex.role_intro)
end

function MainUIView:OnClickFPRoleExp()
	ViewManager.Instance:Open(GuideModuleName.ExpAdditionView)
end