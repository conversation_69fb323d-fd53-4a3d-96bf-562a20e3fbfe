MarryHunYanTask = MarryHunYanTask or BaseClass(SafeBaseView)
function MarryHunYanTask:__init()
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_hunyan_task")

	
    self.active_close = false
    self.is_safe_area_adapter = true
end

function MarryHunYanTask:__delete()

end

function MarryHunYanTask:ReleaseCallBack()
	if self.hunyan_cell_1 ~= nil then
		self.hunyan_cell_1:DeleteMe()
		self.hunyan_cell_1 = nil
	end
    self:CancelTween1()
	if self.hunyan_cell_2 ~= nil then
		self.hunyan_cell_2:DeleteMe()
		self.hunyan_cell_2 = nil
	end

	self.load_finish = false
	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	if CountDownManager.Instance:HasCountDown("baitang_start_time") then
		CountDownManager.Instance:RemoveCountDown("baitang_start_time")
	end
	-- self.danmu_is_open = nil
	self.init_load_complete = nil
end

function MarryHunYanTask:CloseCallBack()
	self.init_load_complete = false

	if self.node_list.layout_hunyan_task_root then
		self.node_list.layout_hunyan_task_root.transform:SetParent(self.root_node_transform, false)
	end
end

function MarryHunYanTask:LoadCallBack()
	-- local start_time = WeddingWGData.Instance:GetMarryBaiTangStartTime()
	-- if start_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
	-- 	self:UpdataStartTime(TimeWGCtrl.Instance:GetServerTime(), start_time)
	-- 	if CountDownManager.Instance:HasCountDown("baitang_start_time") then
	-- 		CountDownManager.Instance:RemoveCountDown("baitang_start_time")
	-- 	end
	-- 	CountDownManager.Instance:AddCountDown("baitang_start_time", BindTool.Bind1(self.UpdataStartTime, self), BindTool.Bind1(self.CompleteEndCallBack, self), start_time, nil, 1)
    -- -- else
    -- --     self.node_list.time_show:SetActive(false)
    -- end

	self.node_list.txt_hunyan_promot.text.text = Language.Marry.WeadingTips
end

function MarryHunYanTask:ShowIndexCallBack()
	local callback = function ()
		if self.init_load_complete then
			return
		end
		local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
		parent:SetActive(true)
		self.node_list.layout_hunyan_task_root.transform:SetParent(parent.transform,false)
		self:InitCallBack()
		self.load_finish = true
		self.init_load_complete = true
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,callback)
end


function MarryHunYanTask:UpdataStartTime(elapse_time, total_time)
	local time = total_time - elapse_time
	local format_time = TimeUtil.Format2TableDHMS(time)
	-- print_error("服务器的时间：",elapse_time,"结束时间:",total_time,"时间差",time,"日子",format_time.day,"小时",format_time.hour,"分钟",format_time.min,format_time.hour,"秒",format_time.s)
	local end_desc = ""
	if format_time.min > 0 then
		end_desc = string.format(Language.Marry.BaiTangTips1,format_time.min,format_time.s)
	else
		end_desc = string.format(Language.Marry.BaiTangTips1_1,format_time.s)
	end
	self.node_list["lbl_baitang"].text.text = end_desc
	-- self.node_list["time_min"].text.text = format_time.min
	-- self.node_list["time_s"].text.text = format_time.s
	-- self.node_list["time_min"]:SetActive(format_time.min > 0)
end

function MarryHunYanTask:CompleteEndCallBack()
	self.node_list["lbl_baitang"].text.text = Language.Marry.BaiTangTips2
	--self.node_list["time_show"]:SetActive(false)
end

function MarryHunYanTask:InitCallBack()
    if self.hunyan_cell_1 == nil then
        self.hunyan_cell_1 = ItemCell.New()
        self.hunyan_cell_1:SetInstanceParent(self.node_list["ph_cell_1"])
    end
    if self.hunyan_cell_2 == nil then
        self.hunyan_cell_2 = ItemCell.New()
        self.hunyan_cell_2:SetInstanceParent(self.node_list["ph_cell_2"])
    end
	self.hunyan_cell_1:SetData({item_id = MARRY_OTHER_TYPE.ITEM_ID_1})
	self.hunyan_cell_2:SetData({item_id = MARRY_OTHER_TYPE.ITEM_ID_2})
    local item_num1, item_num2 = self:GetNumItem()
	local color1 = item_num1 >= 1 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    local color2 = item_num2 >= 1 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    self.hunyan_cell_1:SetRightBottomTextVisible(item_num1 ~= 0)
    self.hunyan_cell_2:SetRightBottomTextVisible(item_num2 ~= 0)
	self.hunyan_cell_1:SetRightBottomText(ToColorStr(item_num1 .. "/1", color1))
	self.hunyan_cell_2:SetRightBottomText(ToColorStr(item_num2 .. "/1", color2))


  	-- self.node_list["rich_hunyan_des"].text.text = string.format(Language.Marry.MarryDemand4,Language.Marry.MarryDemand5)  --屏蔽喜从天降说明 2019-03-04 18:20

  	if nil == self.item_data_change_callback then
  		self.item_data_change_callback = BindTool.Bind1(self.HunYanItemDataChangeCallback, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
  	end

	self.node_list["btn_buy_1"].button:AddClickListener(BindTool.Bind(self.OnClinkUseHandler, self))
	self.node_list["btn_buy_2"].button:AddClickListener(BindTool.Bind(self.OnClinkBuyHandler, self))
	-- self.node_list["btn_danmu"].button:AddClickListener(BindTool.Bind(self.OnClinkDanMu, self))

    local marry_info = MarryWGData.Instance:GetCurWeddingInfo()
	if marry_info then
		self.node_list["lbl_role_name_1"].text.text = marry_info.role_name
        self.node_list["lbl_role_name_2"].text.text = marry_info.lover_role_name
        -- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.lbl_role_name_1.rect)
        -- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.lbl_role_name_2.rect)
        -- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.name_container.rect)
        -- local need_tween1 = self.node_list.name_container.rect.sizeDelta.x > self.node_list.parent.rect.sizeDelta.x
        -- self:DoTween1(need_tween1)
	end
	-- if not self.danmu_is_open then
	-- 	self:OnClinkDanMu()
	-- end
end

-- function MarryHunYanTask:DoTween1(need_tween)
--     local length = self.node_list.name_container.rect.sizeDelta.x
--     local length1 = self.node_list.parent.rect.sizeDelta.x
--     if need_tween then
--         self:CancelTween1()
--         self.node_list.name_container.rect.anchoredPosition = Vector2(length1 / 2, 0)
--         self.tweener1 = self.node_list.name_container.rect:DOAnchorPosX(-length1 / 2 - length , 10)
--         self.tweener1:OnComplete(function()
-- 			self.node_list.name_container.rect.anchoredPosition = Vector2(length1 / 2, 0)
--         end)
--         self.tweener1:SetLoops(-1)
--     else
--         self:CancelTween1()
--         self.node_list.name_container.rect.anchoredPosition = Vector2(-length / 2, 0)
--     end
-- end

function MarryHunYanTask:CancelTween1()
    if self.tweener1 then
        self.tweener1:Kill()
        if self.node_list.name_container then
            local length = self.node_list.name_container.rect.sizeDelta.x
            self.node_list.name_container.rect.anchoredPosition = Vector2(-length / 2, 0)
        end
        self.tweener1 = nil
    end
end

function MarryHunYanTask:FlushDeskTimes()
    local weeding_info = MarryWGData.Instance:GetWeddingRoleInfo() or {}
    local red_id, red_max, jiuxi_id, jiuxi_max, today_jiuxi_num = MarryWGData.Instance:GetWeedingSceneCfg()
    local today_remain_num = 0
    if weeding_info and not IsEmptyTable(weeding_info) then
        today_remain_num = today_jiuxi_num - weeding_info.today_food_count  --今天剩余能采集的次数
        if today_remain_num <= 0 then
            self.node_list["lbl_num"].text.text = Language.Marry.ToddayIsMaxExp
            return
        end
        local this_can_get_max_num, ms_num
        if weeding_info.has_gather_num == 0 then --当场没采集过
            this_can_get_max_num = today_remain_num >= jiuxi_max and jiuxi_max or today_remain_num
        else    --当场采集过 has_gather_num = 2 today_food_count = 28  一共30 单场最高10
            local right_num = today_jiuxi_num - weeding_info.today_food_count + weeding_info.has_gather_num
            this_can_get_max_num = right_num >= jiuxi_max and jiuxi_max or right_num
        end
        ms_num = weeding_info.has_gather_num .. "/" .. this_can_get_max_num
        self.node_list["lbl_num"].text.text = ms_num
    end
end

function MarryHunYanTask:OnFlush()
	if not self.load_finish or not self.node_list["ph_cell_2"] then
		return
    end
    self:FlushDeskTimes()
	local weeding_info = MarryWGData.Instance:GetWeddingRoleInfo() or {}
	
	local role_level = RoleWGData.Instance.role_vo.level
    local can_get_maxexp = WeddingWGData.Instance:GetCanRewardExpByLevel(role_level)
    if weeding_info and not IsEmptyTable(weeding_info) then
        
		local is_max = weeding_info.total_exp == can_get_maxexp
		local left_exp = CommonDataManager.ConverExp(weeding_info.total_exp)
		local right_exp = CommonDataManager.ConverExp(can_get_maxexp)
		local exp_text = left_exp --.. "/" .. right_exp
		self.node_list["lbl_exe"].text.text =  is_max and Language.Marry.ToddayIsMaxExp or exp_text
		--self.node_list["time_show"]:SetActive(weeding_info.is_baitang == 0)
		if weeding_info.is_baitang == 0 then
			-- self.node_list["lbl_baitang"].text.text = Language.Marry.BaiTangTips1
		elseif weeding_info.is_baitang == 1 then
			self.node_list["lbl_baitang"].text.text = Language.Marry.BaiTangTips2
		else
			self.node_list["lbl_baitang"].text.text = Language.Marry.BaiTangTips3
		end

		--屏蔽喜从天降说明 2019-03-04 18:19
		-- if weeding_info.time < 1 then
		-- 	self.node_list["lbl_state"].text.text = Language.Boss.NotRefresh
		-- else
		-- 	local des = ""
		-- 	if weeding_info.wedding_liveness >= 520 and weeding_info.wedding_liveness < 1314 then
		-- 		des = string.format(Language.Marry.RedHasRefresh, 520)
		-- 	elseif weeding_info.wedding_liveness >= 1314 and weeding_info.wedding_liveness < 3344 then
		-- 		des = string.format(Language.Marry.RedHasRefresh, 1314)
		-- 	elseif weeding_info.wedding_liveness >= 3344 then
		-- 		des = string.format(Language.Marry.RedHasRefresh, 3344)
		-- 	end
		-- 	self.node_list["lbl_state"].text.text = des
		-- end
	end
	self:HunyanItemChange()
end

function MarryHunYanTask:HunyanItemChange()
	if not self.load_finish then
		return
	end
	local item_num1, item_num2 = self:GetNumItem()

	local str1 = item_num1 >= 1 and Language.QuintupleExp.Use or Language.QuintupleExp.Buy2
	local str2 = item_num2 >= 1 and Language.QuintupleExp.Use or Language.QuintupleExp.Buy2
	local color1 = item_num1 >= 1 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    local color2 = item_num2 >= 1 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    if self.hunyan_cell_1 == nil then
        self.hunyan_cell_1 = ItemCell.New()
        self.hunyan_cell_1:SetInstanceParent(self.node_list["ph_cell_1"])
    end
    if self.hunyan_cell_2 == nil then
        self.hunyan_cell_2 = ItemCell.New()
        self.hunyan_cell_2:SetInstanceParent(self.node_list["ph_cell_2"])
    end
    self.hunyan_cell_1:SetRightBottomTextVisible(item_num1 ~= 0)
    self.hunyan_cell_2:SetRightBottomTextVisible(item_num2 ~= 0)
	self.hunyan_cell_1:SetRightBottomText(ToColorStr(item_num1 .. "/1", color1))
	self.hunyan_cell_2:SetRightBottomText(ToColorStr(item_num2 .. "/1", color2))

	self.node_list.btn_text_count1.text.text = str1
	self.node_list.btn_text_count2.text.text = str2
end

function MarryHunYanTask:HunYanItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
	self:HunyanItemChange()
end

--消耗元宝
function MarryHunYanTask:OnClinkUseHandler()
	local item_num1, item_num2 = self:GetNumItem()
	if item_num1 > 0 then
		local item_index = ItemWGData.Instance:GetItemIndex(MARRY_OTHER_TYPE.ITEM_ID_1)
		-- BagWGCtrl.Instance:SendUseItem(item_index)
		WeddingWGCtrl.Instance:SendCSWeddingUseYanhua(MARRY_OTHER_TYPE.ITEM_ID_1, 1,"0")
	else
        local item_cfg = ItemWGData.Instance:GetItemConfig(MARRY_OTHER_TYPE.ITEM_ID_1)
        local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
        ShopTip.Instance:SetData(item_cfg, 1, GameEnum.SHOP, nil, other_cfg.seq_low, nil, 999)
	end
end

function MarryHunYanTask:OnClinkBuyHandler()
	local item_num1, item_num2 = self:GetNumItem()
	if item_num2 > 0 then
        local str = MarryWGData.Instance:GetSuiJiDanMu()
        WeddingWGCtrl.Instance:SendCSWeddingUseYanhua(MARRY_OTHER_TYPE.ITEM_ID_2, 1, str)
	else
        local item_cfg = ItemWGData.Instance:GetItemConfig(MARRY_OTHER_TYPE.ITEM_ID_2)
        local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
        ShopTip.Instance:SetData(item_cfg, 2, GameEnum.SHOP, nil, other_cfg.seq_tall, nil, 999)
	end
end

function MarryHunYanTask:GetNumItem()
	local item_num1 = ItemWGData.Instance:GetItemNumInBagById(MARRY_OTHER_TYPE.ITEM_ID_1)
	local item_num2 = ItemWGData.Instance:GetItemNumInBagById(MARRY_OTHER_TYPE.ITEM_ID_2)
	return item_num1, item_num2
end

-- function MarryHunYanTask:OnClinkDanMu()
-- 	self.danmu_is_open = not self.danmu_is_open
-- 	local danmu_icon = self.danmu_is_open and "jh_danmu1" or "jh_danmu2"
-- 	self.node_list["btn_danmu"].image:LoadSprite(ResPath.GetJieHunImg(danmu_icon))
-- 	if self.danmu_is_open then
-- 		ViewManager.Instance:Open(GuideModuleName.MarryDanMu)
-- 	else
-- 		ViewManager.Instance:Close(GuideModuleName.MarryDanMu)
-- 	end

-- end
