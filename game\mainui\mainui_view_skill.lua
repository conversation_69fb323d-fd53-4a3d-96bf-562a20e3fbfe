MainUIView = MainUIView or BaseClass()
MainUIView.SKILL_INDEX_1 = 1   --普攻技能
MainUIView.SKILL_INDEX_2 = 2
MainUIView.SKILL_INDEX_3 = 3
MainUIView.SKILL_INDEX_4 = 4
MainUIView.SKILL_INDEX_5 = 5

local JUMP_MAX_ENERGY = 300
local UnityEngineInput = UnityEngine.Input
function MainUIView:__initSkill()
	self.eh_duan_skill = GlobalEventSystem:Bind(MainUIEventType.ROLE_SKILL_DUAN_ATK_CHANGE, BindTool.Bind(self.UpdateDuanSkillIcon, self))
	self.bianshen_skill_change = GlobalEventSystem:Bind(MainUIEventType.BIANSHEN_SKILL_CHANGE, BindTool.Bind(self.FlushBianShenSkill, self))

	self.is_show_jump = false
	self.qinggong_index = 0
	self.cur_qinggong_time = 0.3
	self.jump_energy = JUMP_MAX_ENERGY
	self.qinggong_down_count_time = {}
	local cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	for i = 1, 4 do
		self.qinggong_down_count_time[i] = cfg["qinggong_guide_delay_" .. i] or 2
	end

	self.common_skill_list = {}
	self.rang_is_show = false
	self.is_skill_drag = false
	self.skill_pos_x = nil
	self.skill_pos_y = nil
	self.cancle_pos = nil
	self.is_cancle_skill = false
	self.is_force_cancle = false
	self.is_in_cancle_rect = false
	self.doing_order_change = false
	self.listen_buff_type_list = {}
	self.use_rang_skill = nil
	self.use_rang_index = nil
	self.save_drag_pos = nil
	self.hide_skill_rang_skill = false
	self.hide_skill_cache_pos = nil
	self.hide_skill_send_pos = nil
	self.hide_skill_send_time = 0
	self.hide_skill_need_send = false
	self.is_in_sprint_cd = false
	self.bianshen_btn_state = nil
	self.select_obj_group_list = {}

	self.fight_mount_skill_cg_play = false
end

function MainUIView:__deleteSkill()
	GlobalEventSystem:UnBind(self.eh_duan_skill)
    GlobalEventSystem:UnBind(self.bianshen_skill_change)
end

function MainUIView:Skill__ReleaseCallBack()
	self:CleanBeastSkillEffectCountDown()
	self:ClearSecKillBossSliderTween()

	-- if self.click_skill_button then
	-- 	GlobalEventSystem:UnBind(self.click_skill_button)
	-- 	self.click_skill_button = nil
	-- end

	if self.bianshen_skill_event then
		GlobalEventSystem:UnBind(self.bianshen_skill_event)
		self.bianshen_skill_event = nil
	end

	if self.enter_jump then
		GlobalEventSystem:UnBind(self.enter_jump)
		self.enter_jump = nil
	end

	if self.exit_jump then
		GlobalEventSystem:UnBind(self.exit_jump)
		self.exit_jump = nil
	end

	if self.enabled_qing_gong then
		GlobalEventSystem:UnBind(self.enabled_qing_gong)
		self.enabled_qing_gong = nil
	end

	if self.add_buff_event then
		GlobalEventSystem:UnBind(self.add_buff_event)
		self.add_buff_event = nil
	end

	if self.remove_buff_event_skill then
		GlobalEventSystem:UnBind(self.remove_buff_event_skill)
		self.remove_buff_event_skill = nil
	end

	if self.exit_fight_state then
		GlobalEventSystem:UnBind(self.exit_fight_state)
		self.exit_fight_state = nil
	end

	if self.beasts_skill_auto_flag_change then
		GlobalEventSystem:UnBind(self.beasts_skill_auto_flag_change)
		self.beasts_skill_auto_flag_change = nil
	end

	if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end

	if self.common_skill_list then
		for k, v in pairs(self.common_skill_list) do
			v:DeleteMe()
		end
		self.common_skill_list = nil
	end

	if self.bianshen_btn_list then
		for k, v in pairs(self.bianshen_btn_list) do
			v:DeleteMe()
		end
		self.bianshen_btn_list = nil
	end

	if self.bianshen_show_skill then
		self.bianshen_show_skill:DeleteMe()
		self.bianshen_show_skill = nil
	end

	if self.boss_card_cell_list then
        for k, v in pairs(self.boss_card_cell_list) do
            v:DeleteMe()
        end
		self.boss_card_cell_list = nil
    end

	if self.esoterica_skill_list then
        for k, v in pairs(self.esoterica_skill_list) do
            v:DeleteMe()
        end
		self.esoterica_skill_list = nil
    end

    if self.sixiang_skill_cell then
    	self.sixiang_skill_cell:DeleteMe()
    	self.sixiang_skill_cell = nil
    end

	if self.long_zhu_skill then
		self.long_zhu_skill:DeleteMe()
		self.long_zhu_skill = nil
	end

	if self.wuxing_skill_cell then
		self.wuxing_skill_cell:DeleteMe()
		self.wuxing_skill_cell = nil
	end

	if self.halo_skill_cell then
		self.halo_skill_cell:DeleteMe()
		self.halo_skill_cell = nil
	end

	for i = 5, 10 do
		if self.customized_skill_list[i] then
			self.customized_skill_list[i]:DeleteMe()
		end
	end
	self.customized_skill_list = nil

	if self.ts_hj_skill_cell then
		self.ts_hj_skill_cell:DeleteMe()
		self.ts_hj_skill_cell = nil
	end

	if self.beasts_skill_list then
		for k, v in pairs(self.beasts_skill_list) do
            v:DeleteMe()
        end
		self.beasts_skill_list = nil
	end
	self.beasts_skill_root_list = nil

	if self.jump_up_btn then
		self.jump_up_btn:DeleteMe()
		self.jump_up_btn = nil
	end

	if self.force_start_next_timer then
		GlobalTimerQuest:CancelQuest(self.force_start_next_timer)
		self.force_start_next_timer = nil
	end

	if self.delay_play_nuqianim then
		GlobalTimerQuest:CancelQuest(self.delay_play_nuqianim)
		self.delay_play_nuqianim = nil
	end

	if self.main_tianshen_list_tween then
		self.main_tianshen_list_tween:Kill()
		self.main_tianshen_list_tween = nil
	end

	if self.special_beasts_skill_tween then
		self.special_beasts_skill_tween:Kill()
		self.special_beasts_skill_tween = nil
	end

	self:CleanAddEnergyTimer()
	self:CleanJumpUpEnergyTimer()
	self:RemoveRangCheckTimer()
	self:RemoveXiuWeiSeqDelayTimer()

	self.rang_is_show = false
	self.is_skill_drag = false
	self.skill_pos_x = nil
	self.skill_pos_y = nil
	self.cancle_pos = nil
	self.is_cancle_skill = false
	self.is_force_cancle = false
	self.is_in_cancle_rect = false
	self.use_rang_skill = nil
	self.use_rang_index = nil
	self.save_drag_pos = nil
	self.hide_skill_rang_skill = false
	self.hide_skill_cache_pos = nil
	self.hide_skill_send_pos = nil
	self.hide_skill_send_time = 0
	self.hide_skill_need_send = false
	self.skill_rang_transform = nil
	self.rang_img_transform = nil
	self.skill_cell_root_list = nil
	self.bianshen_btn_state = nil
	self.effect_root = nil
	self.old_sixiang_skill_effect = nil
	self.select_obj_group_list = {}
	self.listen_buff_type_list = {}
end

function MainUIView:SkillLoadCallBack()
	self.skill_rang_transform = self.node_list.root_skill_rang.transform
	self.rang_img_transform = self.node_list.img_rang.transform

	-- 角色技能
	self.common_skill_list = {}
	self.skill_cell_root_list = {}
	for i = MainUIView.SKILL_INDEX_1, MainUIView.SKILL_INDEX_5 do
		local cell_noot = self.node_list["Skill" .. i]
		local cell = MainUICommonSkillRender.New(cell_noot)
		cell:SetParent(self)
		cell:SetData("cell_index", i)
		cell:SetPointerUpCallBack(BindTool.Bind(self.OnSkillUp, self))
		cell:SetDragCallBack(BindTool.Bind(self.OnDragSkill, self))
		cell:SetPointerDownCallBack(BindTool.Bind(self.OnClickRangSkill, self))
		cell:SetPointerExitCallBack(BindTool.Bind(self.CheckRangState, self))
		cell:SetClickSkillCallBack(BindTool.Bind(self.OnClickSkill, self))
		self.common_skill_list[i] = cell
		self.skill_cell_root_list[i] = cell_noot
	end

	-- 变身按钮
	self.bianshen_btn_list = {}
	for i = 1, 4 do
		self.bianshen_btn_list[i] = MainUIBianShenRender.New(self.node_list["bianshen_skill_" .. i])
	    self.bianshen_btn_list[i]:SetIndex(i)
	    self.bianshen_btn_list[i]:SetParent(self)

		-- 屏蔽拖拽逻辑
	    -- self.node_list["bianshen_skill_" .. i].event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnChangeOrderUp, self, i))
		-- self.node_list["bianshen_skill_" .. i].event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDragChangeOrder, self, i))
		-- self.node_list["bianshen_skill_" .. i].event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnClickChangeOrder, self, i))
		-- self.node_list["bianshen_skill_" .. i].event_trigger_listener:AddPointerExitListener(BindTool.Bind(self.CheckChangeOrderState, self, i))
	end

	self.bianshen_show_skill = MainUIBianShenRender.New(self.node_list.bianshen_show_skill)
	self.bianshen_show_skill:SetCDKey("zjm_tianshen_show_skill_cd")
	self.bianshen_show_skill:SetParent(self)
	self.is_tianshen_list_zhankai = true
	self.bianshen_flex_can_show = true
	self:OnClickTianShenListTween()

	-- 四象技能
	if not self.sixiang_skill_cell then
		local cell = MainUISiXiangSkillRender.New(self.node_list.btn_sixiang_skill)
		cell:SetParent(self)
		cell:SetPointerUpCallBack(BindTool.Bind(self.OnSkillUp, self))
		cell:SetDragCallBack(BindTool.Bind(self.OnDragSkill, self))
		cell:SetPointerDownCallBack(BindTool.Bind(self.OnClickRangSkill, self))
		cell:SetPointerExitCallBack(BindTool.Bind(self.CheckRangState, self))
		self.sixiang_skill_cell = cell
	end

	-- 龙珠技能
	self.long_zhu_skill = MainUiLongZhuSkill.New(self.node_list["btn_long_zhu_skill"])
	FunOpen.Instance:RegisterFunUi(FunName.MainUILongZhuSkill, self.node_list["btn_long_zhu_skill"])

	-- 五行技能
	if not self.wuxing_skill_cell then
		local cell = MainUIBaseCDSkillRender.New(self.node_list.btn_wuxing_skill)
		cell:SetParent(self)
		self.wuxing_skill_cell = cell
	end

	-- 光环技能
	if not self.halo_skill_cell then
		local cell = MainUIHaloSkillRender.New(self.node_list.btn_halo_skill)
		cell:SetParent(self)
		self.halo_skill_cell = cell
	end

	-- 定制技能
	if not self.customized_skill_list then
		self.customized_skill_list = {}
		for i = 5, 10 do
			local cell_noot = self.node_list["btn_customized_" .. i]
			local cell = MainUIBaseCDSkillRender.New(cell_noot)
			cell:SetParent(self)
			self.customized_skill_list[i] = cell
		end
	end

	-- 天神合击技能
	if not self.ts_hj_skill_cell then
		local cell = MainUIBaseCDSkillRender.New(self.node_list.btn_ts_hj_skill)
		cell:SetParent(self)
		self.ts_hj_skill_cell = cell
	end

	-- 秘笈技能
	if not self.esoterica_skill_list then
        self.esoterica_skill_list = {}
        local parent_node = self.node_list["esoterica_skill_part"]
        for i = 1, ESOTERICA_DEFINE.MAINUI_SKILL_NUM do
            local cell = MainUIEsotericaSkillRender.New(parent_node:FindObj("esoterica_skill_" .. i))
			cell:SetClickSkillCallBack(BindTool.Bind(self.OnDoSkillBySpecialFunc, self))
            self.esoterica_skill_list[i] = cell
        end
    end

	-- 御兽技能
	if not self.beasts_skill_list then
		self.beasts_skill_list = {}
		self.beasts_skill_root_list = {}
		local parent_node = self.node_list["beasts_skill_root"]
		for i = 1, BEAST_DEFINE.BEASTS_MAX_SKILL_NUM do
			local cell_node = parent_node:FindObj("beast_skill_0" .. i)
			local cell = MainUiBeastsSkill.New(cell_node)
			cell:SetParent(self)
			self.beasts_skill_list[i] = cell
			self.beasts_skill_root_list[i] = cell_node
		end
	end

	self:BeastsSkillAutoFlagChange()
	if not self.beasts_skill_auto_flag_change then
		self.beasts_skill_auto_flag_change = GlobalEventSystem:Bind(SettingEventType.AUTO_BEASTS_SKILL, BindTool.Bind(self.BeastsSkillAutoFlagChange, self))
	end

	XUI.AddClickEventListener(self.node_list["btn_beast_auto_skill"], BindTool.Bind(self.ChangeBeastAutoSkillFlag, self))

	XUI.AddClickEventListener(self.node_list["ChangeTarget"], BindTool.Bind(self.ChangeTargetObj, self))
	--这个由飞行改成御剑了
	-- XUI.AddClickEventListener(self.node_list["FlyJumpButton"], BindTool.Bind(self.OnClickLanding, self))
	XUI.AddClickEventListener(self.node_list["JumpButton"], BindTool.Bind(self.OnClickJump, self))
	XUI.AddClickEventListener(self.node_list["ButtonZuoqi"],BindTool.Bind(self.OnClickZuoQi, self))			--坐骑

	-- self.jump_up_btn = LongClickButton.New(self.node_list["JumpButtonUp"].event_trigger_listener)
	-- self.jump_up_btn:AddPointerClickListener(BindTool.Bind(self.OnClickJumpUpPoint, self))
	-- self.jump_up_btn:AddLongClickUpListener(BindTool.Bind(self.OnClickJumpUpClickUp, self))
	-- self.jump_up_btn:AddPointDownListener(BindTool.Bind(self.OnClickJumpUpClickDown, self))
	XUI.AddClickEventListener(self.node_list["LandingButtonDown"], BindTool.Bind(self.OnClickLanding, self))
	XUI.AddClickEventListener(self.node_list["JumpButtonUp"], BindTool.Bind(self.OnClickJumpSprint, self))

	XUI.AddClickEventListener(self.node_list["boss_use_card"], BindTool.Bind(self.ChangeBossRefreshBtnsState, self))
    XUI.AddClickEventListener(self.node_list["boss_card_hide_btn"], BindTool.Bind(self.HideBossRefreshBtns, self))
	XUI.AddClickEventListener(self.node_list["bianshen_skill_flex_btn"], BindTool.Bind(self.OnClickTianShenListTween, self))
	XUI.AddClickEventListener(self.node_list["boss_card_setting_btn"], BindTool.Bind(self.OnClickOpenBossCardWindow, self))
	XUI.AddClickEventListener(self.node_list["beasts_skill_effect_icon"], BindTool.Bind(self.OnClickBeastsSkillEffectIcon, self))
	XUI.AddClickEventListener(self.node_list["sec_kill_boss_btn"], BindTool.Bind(self.OnClickSecKillBossBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_anger_skill"], BindTool.Bind(self.OnClickAngerSkillBtn, self))

	self.node_list["LandingButtonDown"]:SetActive(false)
	self.node_list["JumpButtonUp"]:SetActive(false)
	self.node_list["FlyJumpButton"]:SetActive(false)
	self.node_list["JumpContentImage"]:SetActive(false)
	self.show_boss_refresh_btn = false
    self.node_list.boss_btn_container:SetActive(self.show_boss_refresh_btn)
    self.node_list.boss_card_hide_btn:SetActive(self.show_boss_refresh_btn)

	-- 技能点击监听
	-- self.click_skill_button = GlobalEventSystem:Bind(MainUIEventType.CLICK_SKILL_BUTTON, BindTool.Bind(self.PlaySkillNameFlyAnimitor, self))
	-- 变身技能
	self.bianshen_skill_event = GlobalEventSystem:Bind(MainUIEventType.ROLE_SKILL_BIANSHEN, BindTool.Bind(self.FlushBianShenSkill, self))
	-- 进入跳跃
	self.enter_jump = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_ENTER_JUMP_STATE, BindTool.Bind(self.OnMainRoleJumpState, self, true))
	-- 退出跳跃
	self.exit_jump = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_EXIT_JUMP_STATE, BindTool.Bind(self.OnMainRoleJumpState, self, false))
	-- 主角加buff
	self.add_buff_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_ADD_BUFF, BindTool.Bind(self.OnMainRoleBuffChange, self))
	-- 主角移除buff
	self.remove_buff_event_skill = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_REMOVE_BUFF, BindTool.Bind(self.OnMainRoleBuffChange, self))
	-- 主角离开战斗状态
	self.exit_fight_state = GlobalEventSystem:Bind(ObjectEventType.EXIT_FIGHT, BindTool.Bind(self.OnMainRoleExitFightState, self))
	-- 轻功改变
	self.enabled_qing_gong = GlobalEventSystem:Bind(OtherEventType.ENABLE_QING_GONG_CHANGE, function(enabled)
		self.node_list["JumpButton"]:SetActive(enabled)
	end)

	--关闭加载界面
	if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end

	self:IsShowJumpBtn()
	self:OnMainRoleBuffChange()
	self:FlushBuffLayer()
	self:UpdateQucikItemBtnStateByScene()

	if self.is_should_update_bianshen then
		self.is_should_update_bianshen = nil
		self:UpdateBianShenSkillState()
	end
end

--------------------------
-- 【     技能刷新      】 --
--------------------------
-- 刷新技能列表
function MainUIView:FlushSkillList(is_force)
	if not self.common_skill_list then
		return
	end

	for i, v in pairs(self.common_skill_list) do
		v:RemoveCountDown()
	end

	self:UpdateSkillList(is_force)
	self:FlushBianShenSkill()
	self:FlushSiXiangSkill()
	self:FlushBuffLayer(true)
end

--更新技能列表
function MainUIView:UpdateSkillList(is_force)
	if not self.common_skill_list then
		return
	end

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local main_role = Scene.Instance:GetMainRole()
	local skill_order
	local is_system_bianshen = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance
	local is_xmz_car = main_role:IsXMZCar()
	local is_riding_fight_mount = main_role:IsRidingFightMount()
	local riding_mount_appeid = main_role:GetCurRidingResId()
	local is_gundam = main_role:IsGundam()
	local is_xiuwei_bianshen = main_role:IsXiuWeiBianShen()
	local appearance_param = RoleWGData.Instance:GetAttr('appearance_param')

	if is_system_bianshen then
		skill_order = TianShenWGData.Instance:GetTianShenSkill()
    elseif is_xmz_car then
    	skill_order = GuildBattleRankedWGData.Instance:GetXMZCarSkill()
	elseif is_riding_fight_mount then
		skill_order = NewFightMountWGData.Instance:GetMainUISkillOrder(riding_mount_appeid)
	elseif is_gundam then
		skill_order = MechaWGData.Instance:GetMainUISkillOrder()
	elseif is_xiuwei_bianshen then
		local nuqi_type = math.floor(appearance_param / 1000)
		skill_order = CultivationWGData.Instance:GetMainUISkillOrder(nuqi_type)
	else
		skill_order = RoleWGData.Instance:GetSkillCustomInfo()
	end

	if IsEmptyTable(skill_order) then
		return
	end

	local skill_info_list = nil
    if is_system_bianshen then
    	local tianshen_index = TianShenWGData.Instance:GetImageModelByAppeId(appearance_param, false)
    	tianshen_index = tianshen_index and tianshen_index.index

        skill_info_list = SkillWGData.Instance:GetBianShenSkill(tianshen_index)
    elseif is_xmz_car then
    	skill_info_list = SkillWGData.Instance:GetXMZCarSkillCfgList()
	elseif is_riding_fight_mount then
		skill_info_list = NewFightMountWGData.Instance:GetMainUISkillList(riding_mount_appeid)
	elseif is_gundam then
		skill_info_list = MechaWGData.Instance:GetMainUISkillList()
	elseif is_xiuwei_bianshen then
		local nuqi_type = math.floor(appearance_param / 1000)
		skill_info_list = CultivationWGData.Instance:GetSkillListByNuqiType(nuqi_type)
    else
		skill_info_list = SkillWGData.Instance:GetCurrSkillList()
    end

	local index = 0
	local skill_data = SkillWGData.Instance

	local skill_4_list = {}
	local normal_skill_num = 0
	for k, v in pairs(skill_info_list) do
		local cell = self:GetSkillSkillIndex(index + 1)
		local skill_info, lock_skill_info = nil, nil
		if cell then
			if main_role:IsXMZCar() then
				skill_info = skill_data:GetSkillInfoById(v.skill_id)
			else
				local is_sixiang_skill = skill_data:GetIsSiXiangSkill(v.skill_id)
				if 0 == index then
					if is_riding_fight_mount or is_gundam or is_xiuwei_bianshen then
						skill_info = skill_data:GetSkillInfoById(skill_order[index])
					else
						skill_info = skill_data:GetFirstCommonSkillInfo()
					end
				elseif skill_order[index] and not is_sixiang_skill then
					skill_info = skill_data:GetSkillInfoById(skill_order[index])
				end
			end

			if skill_info then
				local data = {skill_info = skill_info}
				cell:FlushCell(data, is_force)
				if index ~= 0 then
					normal_skill_num = normal_skill_num + 1
				end

				-- 缓存玩家普通技能和普攻
				if index <= 4 then
					table.insert(skill_4_list, {skill_id = skill_info.skill_id, skill_level = skill_info.level})
				end
				
				index = index + 1
			else
				lock_skill_info = SkillWGData.Instance:GetJiBanLockInfo(skill_order[index])
				if not IsEmptyTable(lock_skill_info) then
					local data = {lock_skill_info = lock_skill_info}
					cell:FlushCell(data, is_force)
					index = index + 1
				end
			end
		end
	end

	for i = index + 1, #self.common_skill_list do
		self.common_skill_list[i]:FlushCell(nil, is_force)
	end

	-- 懒得再计算，利用技能view的一堆计算，获取玩家最小的技能攻击距离
	SkillWGData.Instance:CaclAndCacheMinAtkRange(skill_4_list)

	-- 怒气变身增加一个提前展示只需要角色到达30级
	--GameEnum.SHOW_SPIRIT_LV
	local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
	local is_preview = false
	self.node_list.btn_anger_skill_lock:CustomSetActive(curr_nuqi_type == -1)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if curr_nuqi_type == -1 and role_level >= GameEnum.SHOW_SPIRIT_LV then
		curr_nuqi_type = 0
		is_preview = true
	end

	local curr_nuqi = CultivationWGData.Instance:GetRoleCurrNuqi()
	local curr_slider_value = curr_nuqi / 100
	local is_show_nuqi_skill = curr_nuqi_type ~= -1 and main_role_vo.special_appearance == 0
	self.node_list.btn_anger_skill:CustomSetActive(is_show_nuqi_skill)
	self.node_list.btn_anger_skill_effect:CustomSetActive(is_show_nuqi_skill and curr_slider_value >= 1 and (not is_preview))
	self.node_list.btn_anger_skill_cd_mark:CustomSetActive((is_show_nuqi_skill and curr_slider_value < 1) or is_preview)

    if is_show_nuqi_skill then
		normal_skill_num = normal_skill_num + 1

		local cfg = CultivationWGData.Instance:GetActiveCfgByType(curr_nuqi_type)
		if cfg then
			local bundle, name = ResPath.GetSkillIconById(cfg.skill_icon)
			self.node_list.btn_anger_skill_icon.image:LoadSprite(bundle, name, function ()
				self.node_list.btn_anger_skill_icon.image:SetNativeSize()
			end)
		end
    end

	for k,v in pairs(self.common_skill_list) do
		v:UpdateSkillBtnPos(normal_skill_num)
	end

	self:FlushLongZhuSkill()
	self:FlushWuXingSkill()
	self:FlushHaloSkill()
	self:FlushCustomizedSkill()---刷新定制技能
	self:FlusTianShenHeJiSkill()
end

----------------------- 变身技能
-- 刷新变身信息
function MainUIView:FlushBianShenSkill()
	local main_role = Scene.Instance:GetMainRole()
	if not self.bianshen_btn_list or not main_role then
		return
	end
	
	local skill_info, chuzhan_amount = TianShenWGData.Instance:GetTianShenSkillInfoByMainView()
	self.bianshen_flex_can_show = chuzhan_amount > 1
	self:UpdateBianShenFlexBtn()
	for i = 1, 4 do
		if skill_info[i - 1] then
			self.bianshen_btn_list[i]:SetActive(true)
			self.bianshen_btn_list[i]:SetData(skill_info[i - 1])
		else
			self.bianshen_btn_list[i]:SetData({not_has_skill = true})
			self.bianshen_btn_list[i]:SetActive(false)
		end
	end

	local cur_show_index = TianShenWGData.Instance:GetTianShenZJMShowIndex(skill_info)
	self.bianshen_show_skill:SetIndex(cur_show_index + 1)
	self.bianshen_show_skill:SetData(skill_info[cur_show_index])
	self:UpdateBianShenSkillState()
end

function MainUIView:UpdateBianShenFlexBtn()
	if self.node_list.bianshen_skill_flex_btn then
		self.node_list.bianshen_skill_flex_btn:SetActive(self.bianshen_flex_can_show and not self.is_tianshen_list_zhankai)
	end
end

--变身技能
function MainUIView:UpdateBianShenSkillState()
	if not self.is_mainui_view_load then
		self.is_should_update_bianshen = true
		return
	end

	local is_xmz_car = Scene.Instance:GetMainRole():IsXMZCar()
	local is_car = is_xmz_car
	local can_show = Scene.Instance:GetSceneType() ~= SceneType.KF_DUCK_RACE
					and Scene.Instance:GetSceneType() ~= SceneType.TianShen3v3
					and Scene.Instance:GetSceneType() ~= SceneType.TianShen3v3Prepare
	local boo = FunOpen.Instance:GetFunIsOpened("TianShenView")

	local skill_info = TianShenWGData.Instance:GetTianShenSkillInfoByMainView()
	local cur_show_index = TianShenWGData.Instance:GetTianShenZJMShowIndex(skill_info)
	local has_data = not IsEmptyTable(skill_info[cur_show_index])

	self:UpdateBianShenBtnState(boo and (not is_car) and can_show and has_data)
end

--变身按钮状态
function MainUIView:UpdateBianShenBtnState(boo)
	local vas = self.node_list["bianshen_skill_tween_node"]:GetActive()
	if self.bianshen_btn_state == boo and self.bianshen_btn_state == vas then
		return
	end

	self.bianshen_btn_state = boo
	self.node_list["bianshen_skill_tween_node"]:SetActive(boo)
end

function MainUIView:GetTianshenSkillGuide(index)
	if self.bianshen_btn_list then
		local item = self.bianshen_btn_list[index]
		if item then
			return item:GetView(), BindTool.Bind(item.OnClickSkill, item)
		end
	end
end

function MainUIView:ShowTianShenScreenEffect(is_visible)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.TianShen3v3 or scene_type == SceneType.TianShen3v3Prepare then
		return
	end

	if self.node_list["tianshen_screen_effect"] then
		self.node_list["tianshen_screen_effect"]:SetActive(is_visible)
	end
end




function MainUIView:ShowYuLongScreenEffect()
	if self.node_list["yulong_screen_effect"] then
		self.node_list["yulong_screen_effect"]:SetActive(true)
	end
end

function MainUIView:ShowMechaScreenEffect()
	if self.node_list["mecha_screen_effect"] then
		local mecha_cfg = MechaWGData.Instance:GetCurBianShenMechaCfg()
		
		if mecha_cfg then
			local bundle, asset = ResPath.GetA2Effect(mecha_cfg.screen_effect)
			local attach_obj = self.node_list["mecha_screen_effect"].gameObject:GetComponent(typeof(Game.GameObjectAttach))
			attach_obj.BundleName = bundle
			attach_obj.AssetName = asset
			self.node_list["mecha_screen_effect"]:SetActive(true)
		end
	end
end

local tianshen_path_list = {
	Vector3(0, 0, 0),
	Vector3(-1, 94, 0),
	Vector3(30, 173, 0),
	Vector3(79, 243, 0),
}

function MainUIView:OnClickTianShenListTween()
	if self.is_doing_tianshen_list_tween or not self.bianshen_flex_can_show or IsEmptyTable(self.bianshen_btn_list) then
		return
	end

	local is_zhankai = not self.is_tianshen_list_zhankai
	self.is_doing_tianshen_list_tween = true
	self.is_tianshen_list_zhankai = is_zhankai

	if self.main_tianshen_list_tween then
		self.main_tianshen_list_tween:Kill()
		self.main_tianshen_list_tween = nil
	end

	self.main_tianshen_list_tween = DG.Tweening.DOTween.Sequence()
	local list_obj_canvas_group = self.node_list.bianshen_skill_list.canvas_group
	local btn_obj = self.node_list.bianshen_show_skill
	local tween_time = 0.5
	
	local list_from_alpha = is_zhankai and 0 or 1
	local list_end_alpha = is_zhankai and 1 or 0
	local btn_from_alpha = is_zhankai and 1 or 0
	local btn_end_alpha = is_zhankai and 0 or 1
	btn_obj:SetActive(true)
	btn_obj.canvas_group.alpha = btn_from_alpha
	local list_alpha = list_obj_canvas_group:DoAlpha(list_from_alpha, list_end_alpha, tween_time):OnComplete(function ()
		if list_obj_canvas_group then
			list_obj_canvas_group.interactable = is_zhankai
			list_obj_canvas_group.blocksRaycasts = is_zhankai
		end
	end)

	local btn_alpha = btn_obj.canvas_group:DoAlpha(btn_from_alpha, btn_end_alpha, tween_time):OnComplete(function ()
		if btn_obj then
			btn_obj:SetActive(not is_zhankai)
		end
	end)

	self.main_tianshen_list_tween:Append(list_alpha)
	self.main_tianshen_list_tween:Join(btn_alpha)

	local path_list = {}
	for i = 2, #tianshen_path_list do
		path_list[i] = {}
	end

	for i = 2, #self.bianshen_btn_list do
		local cell = self.bianshen_btn_list[i]
		for k, v in pairs(path_list) do
			if is_zhankai and k >= i then
				local pos = tianshen_path_list[i]
				table.insert(path_list[k], pos)
			elseif not is_zhankai and k > i - 1 then
				local pos = tianshen_path_list[i - 1]
				table.insert(path_list[k], 1, pos)
			end
		end

		local cell_move = cell.view.transform:DOLocalPath(
			path_list[i],
			tween_time,
			DG.Tweening.PathType.Linear,
			DG.Tweening.PathMode.Sidescroller2D,
			10)

		self.main_tianshen_list_tween:Join(cell_move)
	end

	self.main_tianshen_list_tween:OnComplete(function ()
		self.is_doing_tianshen_list_tween = false
		self:UpdateBianShenFlexBtn()
	end)
end



----------------------- 变身技能 end

----------------------- 四象技能
function MainUIView:FlushSiXiangSkill()
	if self.sixiang_skill_cell == nil then
		return
	end

	local mian_role = Scene.Instance:GetMainRole()
	local is_xmz = Scene.Instance:GetMainRole():IsXMZCar()
	if is_xmz then
		self.sixiang_skill_cell:SetActive(false)
		return
	end

	local skill_data
	local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
	for k,v in pairs(skill_info_list) do
		if SkillWGData.Instance:GetIsSiXiangSkill(v.skill_id) then
			skill_data = v
			break
		end
	end

	if IsEmptyTable(skill_data) then
		self.sixiang_skill_cell:SetActive(false)
		return
	end

	local skill_id = skill_data.skill_id
	local skill_level = skill_data.level
	local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(skill_id, skill_level)
	local client_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	if IsEmptyTable(skill_cfg) or IsEmptyTable(client_skill_cfg) then
		self.sixiang_skill_cell:SetActive(false)
		return
	end

	self.sixiang_skill_cell:SetData(skill_data)
	local type_cfg = FightSoulWGData.Instance:GetFightSoulTypeCfgBySkill(skill_id)
	local skill_effect_asset = type_cfg and type_cfg.skill_btn_effect
	if skill_effect_asset and (self.old_sixiang_skill_effect == nil
		or self.old_sixiang_skill_effect ~= skill_effect_asset) then
		local bundle, asset = ResPath.GetUIEffect(skill_effect_asset)
		self.sixiang_skill_cell:ChangeIconAsset(bundle, asset)
	end

	local s_bundle, s_asset = ResPath.GetSkillIconById(client_skill_cfg.icon_resource)
	self.sixiang_skill_cell:ChangeIconShow(s_bundle, s_asset)

	self:FlushSiXiangSkillProgress()
end

function MainUIView:FlushSiXiangSkillProgress()
	if self.sixiang_skill_cell == nil then
		return
	end

	if not self.sixiang_skill_cell:GetActive() then
		return
	end

	self.sixiang_skill_cell:FlushProgress()
end

function MainUIView:PlayNuQiAddEffect()
	if not self.node_list or not self.node_list.btn_sixiang_skill then
		return
	end

	if self:GetMenuButtonIsOn() then -- true不显示技能
		return
	end

	if self.delay_play_nuqianim then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	local is_xmz = Scene.Instance:GetMainRole():IsXMZCar()
	if is_xmz then
		return
	end

	if not self.sixiang_skill_cell:GetActive() then
		return
	end

	self.delay_play_nuqianim = GlobalTimerQuest:AddDelayTimer(function ()
		GlobalTimerQuest:CancelQuest(self.delay_play_nuqianim)
		self.delay_play_nuqianim = nil
	end, 0.2)

	local target_obj = main_role:GetAttackTarget()
	if not target_obj then
		target_obj = main_role
	end

	local obj_pos
	if nil ~= target_obj and nil ~= target_obj:GetRoot() and not IsNil(target_obj:GetRoot().transform) then
		obj_pos = target_obj:GetRoot().transform.position
	end

	if not obj_pos then
		return
	end

	local parent_transform = self.node_list["center_point"] and self.node_list["center_point"].transform
	local camber_power = math.random(100, 200)
	TipWGCtrl.Instance:ShowFlyEffectManager("MainUIView", "effects2/prefab/ui_x_prefab",
        "UI_sixiang_nuqi", nil, self.node_list.btn_sixiang_skill, DG.Tweening.Ease.OutCubic,
        0.3, nil, nil, math.random(1, 2), nil, nil, true, nil, nil, camber_power, obj_pos, parent_transform, 400)
end

----------------------- 四象技能 end

----------------------- 龙珠技能
-- 刷新龙珠技能
function MainUIView:FlushLongZhuSkill()
	if self.long_zhu_skill then
		self.long_zhu_skill:Flush()
	end
end

function MainUIView:SetLongZhuSkillBtnActive(is_active)
	if IS_AUDIT_VERSION then
		is_active = false
	end
	self.node_list["btn_long_zhu_skill"]:SetActive(is_active)
end
----------------------- 龙珠技能 end

----------------------- 五行技能
function MainUIView:FlushWuXingSkill()
	if self.wuxing_skill_cell == nil then
		return
	end

	local mian_role = Scene.Instance:GetMainRole()
	local is_xmz = Scene.Instance:GetMainRole():IsXMZCar()
	if is_xmz then
		self.wuxing_skill_cell:SetActive(false)
		return
	end

	local skill_data
	local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
	for k,v in pairs(skill_info_list) do
		if SkillWGData.Instance:GetIsWuXingSkill(v.skill_id) then
			skill_data = v
			break
		end
	end

	self.wuxing_skill_cell:SetData(skill_data)
end
----------------------- 五行技能 end

----------------------- 光环技能
function MainUIView:FlushHaloSkill()
	if self.halo_skill_cell == nil then
		return
	end

	local mian_role = Scene.Instance:GetMainRole()
	local is_xmz = Scene.Instance:GetMainRole():IsXMZCar()
	if is_xmz then
		self.halo_skill_cell:SetActive(false)
		return
	end

	local skill_data
	local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
	for k,v in pairs(skill_info_list) do
		if SkillWGData.Instance:GetIsHaloSkill(v.skill_id) then
			skill_data = v
			break
		end
	end

	self.halo_skill_cell:SetData(skill_data)
end

function MainUIView:FlushHaloSkillProgress()
	if self.halo_skill_cell == nil then
		return
	end

	-- if not self.halo_skill_cell:GetActive() then
	-- 	return
	-- end

	self.halo_skill_cell:FlushCountSlider()
end
----------------------- 光环技能 end


-----------------------定制技能
function MainUIView:FlushCustomizedSkill()
	if self.customized_skill_list == nil then
		return
	end

	for i = 5, 10 do
		if self.customized_skill_list[i] then
			self.customized_skill_list[i]:SetActive(false)
		end
	end

	local mian_role = Scene.Instance:GetMainRole()
	local is_xmz = mian_role:IsXMZCar()
	local is_tianshen_bianshen = mian_role:IsTianShenAppearance()
	local is_xiuwei_bianshen = mian_role:IsXiuWeiBianShen()
    local is_ride_fight_mount = mian_role:IsRidingFightMount()
	local is_gundam = mian_role:IsGundam()
	if is_xmz
		or is_tianshen_bianshen or is_ride_fight_mount or is_gundam or is_xiuwei_bianshen then
		return
	end

	local skills_data = {}
	local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
	for k,v in pairs(skill_info_list) do
		---存在定制技能插入表格
		if SkillWGData.Instance:GetIsCustomizedSkill(v.skill_id) then
			table.insert(skills_data, v)
		end
	end

	for index, skill_data in ipairs(skills_data) do
		local real_index = index + 4	---差四个技能位置

		if self.customized_skill_list[real_index] then
			self.customized_skill_list[real_index]:SetActive(true)
			self.customized_skill_list[real_index]:SetData(skill_data)
		end
	end
end

function MainUIView:FlushCustomizedProgress(index)
	if self.customized_skill_list[index]  == nil then
		return
	end

	self.customized_skill_list[index]:FlushCountSlider()
end

----------------------- 定制技能 end

----------------------- 天神合击技能
function MainUIView:FlusTianShenHeJiSkill()
	if self.ts_hj_skill_cell == nil then
		return
	end

	local mian_role = Scene.Instance:GetMainRole()
	local is_xmz = Scene.Instance:GetMainRole():IsXMZCar()
	if is_xmz then
		self.ts_hj_skill_cell:SetActive(false)
		return
	end

	local skill_data
	local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
	for k,v in pairs(skill_info_list) do
		if SkillWGData.Instance:GetIsTianShenHeJiSkill(v.skill_id) then
			skill_data = v
			break
		end
	end

	self.ts_hj_skill_cell:SetData(skill_data)
end
----------------------- 天神合击 end

----------------------- 御兽技能
function MainUIView:UpdateBeastsSkillList()
	local has_beasts_skill_data = false

	local list = ControlBeastsWGData.Instance:GetSkillInfoList()
	for i, skill_data in ipairs(list) do
		local index = skill_data.old_battle_index + 1

		-- 按之前的下标赋值
		if self.beasts_skill_list[index] then
			has_beasts_skill_data = true
			self.beasts_skill_list[index]:SetData(skill_data)
		end
	end

	-- local show_auto_use_beasts_skill = has_beasts_skill_data and FunOpen.Instance:GetFunIsOpened(GuideModuleName.ControlBeastsView)
	-- self.node_list["btn_beast_auto_skill"]:CustomSetActive(show_auto_use_beasts_skill)

	-- 重新定位
	local new_cell_list = {}
	for i, skill_data in ipairs(list) do
		local index = skill_data.old_battle_index + 1
		local new_index = skill_data.battle_index + 1
		new_cell_list[new_index] = self.beasts_skill_list[index]
	end

	self.beasts_skill_list = new_cell_list
end

----------------------- 御兽技能 end

--------------------------
-- 【     技能响应      】 --
--------------------------
-- 点击技能
function MainUIView:OnClickSkill(data, skill_x, skill_y)
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return
	end

	if MultiMountWGData.Instance:IsMyMultiMountTake() then
		local ok_func = function()
			MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.CANCLE_RIDE)
		end

		TipWGCtrl.Instance:OpenAlertTips(Language.MultiMount.CancelMultiMountTake, ok_func)
		return
	end

	GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
	local not_deleted = not main_role:IsDeleted()
	if not_deleted and main_role:IsTeamFollowState() then
		local function follow_ok_fun()
			if main_role ~= nil and not_deleted then
				main_role:SetIsFollowState(false)
				MainuiWGCtrl.Instance:FlushXunLuStates()
			end
		end

		TipWGCtrl.Instance:OpenAlertTips(Language.FollowState.RelieveTip, follow_ok_fun)
		return
	end

	local skill_info = SkillWGData.Instance:GetSkillInfoByIndex(data.skill_index)
	if not skill_info then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.XianMengzhan and main_role.vo ~= nil then
		-- 攻城车 普通攻击 特殊处理 成前往npc 上交灵石
		local is_xmz_car = Scene.Instance:GetMainRole():IsXMZCar()
		if is_xmz_car and SkillWGData.GetSkillBigType(skill_info.skill_id) == SKILL_BIG_TYPE.NORMAL then
			-- SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBattleRanked.BSStateNotAttack)
			GuildBattleRankedWGCtrl.Instance:XMZGoNpcSkill()
			return
		end
	end

	if main_role:IsGhost() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	if main_role:IsXiuWeiBianShen() then
		local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
		CultivationWGCtrl.Instance:OnCultivationSkillEffect(curr_nuqi_type, false, data.skill_id)
	end

	--当处于活动准备期间 不能走出安全区时 禁止技能释放
	local scene_id = Scene.Instance:GetSceneId()
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if act_scene then
		local act_id = act_scene.act_id
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(act_id)
		if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActCanNotDoSkill)
			return
		end
	end

	-- 仙盟答题场景
	if scene_type == SceneType.GUILD_ANSWER_FB then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoSkill)
		return
	end

	local is_sixiang_skill = SkillWGData.Instance:GetIsSiXiangSkill(data.skill_id)
	local is_xmz_car_skill = SkillWGData.Instance:GetIsXMZCarSkill(data.skill_id)
	local is_longzhu_skill = SkillWGData.Instance:GetIsLongZhuSkill(data.skill_id)

	if is_xmz_car_skill then
		--仙盟战矿车技能 去掉临时手动状态
		GuajiWGCtrl.Instance:ClearTemporaryAndGuaJiReduction()
	end

	if is_sixiang_skill then
		local cur_nuqi, need_nuqi, enough = FightSoulWGData.Instance:GetSkillNuQiData()
		if not enough then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.NuQiNoEnough)
			return
		end
	end

	if is_longzhu_skill then
		LongZhuWGCtrl.Instance:OnLongZhuSkillEffect()
	end

	-- local is_halo_skill = SkillWGData.Instance:GetIsHaloSkill(data.skill_id)
	-- if is_halo_skill then
	-- 	if not FiveElementsWGData.Instance:GetIsCanDoHaloSkill() then
	-- 		return
	-- 	end
	-- end

	local is_limit, limit_tip, limit_type = SkillWGData.Instance:CheckIsLimitUseSkill(data.skill_id)
	if not is_limit or limit_type == nil or limit_type ~= USE_SKILL_LIMIT.ACTIVE_USE_SHOW_CHANGE then
		--当手动使用指示器释放技能时 传入一个释放目标位置 可以避免技能释放出来时 取了挂机目标的位置
		GuajiWGCtrl.Instance:SelectSkillToFight(data.skill_id, skill_x, skill_y)
	end

	if SkillWGData.Instance:IsInSkillCD(data.skill_index, data.skill_id) then
		return
	end

	TaskGuide.LAST_CLICK_SKILL_TIME = Status.NowTime
	if main_role:CantPlayerDoMove(true) then
		return
	end

	if not main_role or (not main_role:CanDoMove({buff = true}) and not SkillWGData.IsPetSkill(data.skill_id)) then
		-- if is_sixiang_skill and skill_x ~= nil and skill_y ~= nil then
			-- GuajiWGCtrl.Instance:ClearTemporaryAndGuaJiReduction()
		-- end
	 	return
	end

	self:PerformSkill(data.skill_index, skill_x, skill_y)
end

--释放技能
function MainUIView:PerformSkill(index, skill_x, skill_y, no_play_cg)
	local main_role = Scene.Instance:GetMainRole()
	local skill_cfg = SkillWGData.Instance:GetSkillConfigByIndex(index)
	if nil == skill_cfg then
		return
	end

	local is_limit, limit_tip = SkillWGData.Instance:CheckIsLimitUseSkill(skill_cfg.skill_id)
	if is_limit then
		if limit_tip ~= nil then
			SysMsgWGCtrl.Instance:ErrorRemind(limit_tip)
		end
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.HotSpring or scene_type == SceneType.KF_HotSpring then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CantUseSKill)
		return
	end

	-- 手动释放 释放前播放技能CG
	local cg_bundle, cg_asset
	-- 战斗坐骑大招
	if not no_play_cg then
		local is_fm_ultimate_skill, fm_type = NewFightMountWGData.Instance:IsFightMountUltimateSkillId(skill_cfg.skill_id)
		if is_fm_ultimate_skill and self.fight_mount_skill_cg_play then
			local cfg = NewFightMountWGData.Instance:GetFightMountSkillCfgByseq(fm_type)
			if cfg then
				cg_bundle = cfg.dz_bundle
				cg_asset = cfg.dz_asset
			end
		end
	end
	
	if cg_bundle and cg_asset and cg_bundle ~= "" and cg_asset ~= "" and not CgManager.Instance:IsCgIng() then
		UN_UPDATE_CAMERA_FOLLOW_TARGET = true
		CgManager.Instance:Play(BaseCg.New(cg_bundle, cg_asset), function()
			UN_UPDATE_CAMERA_FOLLOW_TARGET = false
			self:PerformSkill(index, skill_x, skill_y, true)
		end)
		return
	end

	local is_riding_no_fight_mount = main_role:IsRidingNoFightMount()
	if SkillWGData.Instance:IsSkillToSelf(skill_cfg.skill_id) then
		if is_riding_no_fight_mount then
			MountWGCtrl.Instance:SendMountGoonReq(0)
		end

		local forward = main_role:GetRoot().gameObject.transform.forward
		local dir = math.atan2(forward.z, forward.x)
		local main_role_x, main_role_y = main_role:GetLogicPos()
        FightWGCtrl.SendPerformSkillReq(
                        SkillWGData.Instance:GetRealSkillIndex(skill_cfg.skill_id),
                        1,
                        main_role_x,
                        main_role_y,
                        GameVoManager.Instance:GetMainRoleVo().obj_id,
                        -- AtkCache.target_obj_id,
                        false,
                        main_role_x,
                        main_role_y,
                        dir)
        return
	end

	if skill_x ~= nil and skill_y ~= nil then
		TaskGuide.Instance:ClickSkillStopTask()
		local t_x = skill_x
		local t_y = skill_y
		local is_keep = false
		local target_obj = nil
		if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted()
			and Scene.Instance:IsEnemy(GuajiCache.target_obj)
		then
			target_obj = GuajiCache.target_obj
			if not Scene.Instance:GetObjIsInSkillRange(skill_cfg.skill_id, target_obj, t_x, t_y) then
				target_obj = nil
				if GuajiCache.guaji_type ~= GuajiType.None then
					is_keep = true
				end
			end
		end

		local pos_x, pos_y = t_x, t_y
		local world_x, world_y = GameMapHelper.LogicToWorld(t_x, t_y)
		local target_real_pos = Vector2(world_x, world_y)
		local real_pos_x, real_pos_y = main_role:GetRealPos()
		--有些特殊情况，客户端会设置临时的障碍区，这里做下路径判断，避免冲锋型技能直接穿越障碍区
		pos_x, pos_y = GameMapHelper.GetRealChongFengLogicPos2(Vector2(real_pos_x, real_pos_y), target_real_pos)
		if pos_x == nil or pos_y == nil then
			pos_x, pos_y = t_x, t_y
		end

		local can_use = FightWGCtrl.Instance:TryUseRoleSkill(skill_cfg.skill_id, target_obj, pos_x, pos_y, ATTACK_SKILL_TYPE.NONE, is_keep)
		if can_use and SkillWGData.Instance:GetIsSiXiangSkill(skill_cfg.skill_id) then
			GuajiWGCtrl.Instance:ClearTemporaryAndGuaJiReduction()
		end

		if is_riding_no_fight_mount then
			MountWGCtrl.Instance:SendMountGoonReq(0)
		end
		GlobalEventSystem:Fire(MainUIEventType.CLICK_SKILL_BUTTON, skill_cfg.skill_id)
	else
		if SkillWGData.GetSkillBigType(skill_cfg.skill_id) == SKILL_BIG_TYPE.NORMAL then
			if main_role:GetIsGatherState() then
				Scene.Instance:SendStopGather()
			end

			if is_riding_no_fight_mount then
				MountWGCtrl.Instance:SendMountGoonReq(0)
			end

			local target_obj = nil
			if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and Scene.Instance:IsEnemy(GuajiCache.target_obj) then
				target_obj = GuajiCache.target_obj
			end

			if target_obj == nil or target_obj:IsDeleted() then
				local skill_id, attack_index = SkillWGData.ChangeSkillID(skill_cfg.skill_id)
				main_role:SetAttackIndex(attack_index)
				main_role:DoAttackForNoTarget(skill_id)
				SkillWGCtrl.Instance:OnNoTargetCD(skill_cfg.skill_id)
				GlobalEventSystem:Fire(MainUIEventType.CLICK_SKILL_BUTTON,skill_cfg.skill_id)
				return
			else
				local t_x, t_y = target_obj:GetLogicPos()
				local main_role_x, main_role_y = main_role:GetLogicPos()
				local check_dis = skill_cfg.distance
				if target_obj:IsRole() then
					check_dis = check_dis + 1
				elseif target_obj:IsMonster() then
					local monster_size = target_obj:GetChecksModel() or 0
					check_dis = check_dis + monster_size * 0.5
				end

				if u3dpool.v2Length(u3dpool.v2Sub({x = t_x, y = t_y}, {x = main_role_x, y = main_role_y}), false) - (check_dis * check_dis) > 1 then
					local skill_id, attack_index = SkillWGData.ChangeSkillID(skill_cfg.skill_id)
					main_role:SetDirectionByXY(t_x, t_y)
					main_role:SetAttackIndex(attack_index)
					main_role:DoAttackForNoTarget(skill_id, nil, t_x, t_y)
					SkillWGCtrl.Instance:OnNoTargetCD(skill_cfg.skill_id)
					GlobalEventSystem:Fire(MainUIEventType.CLICK_SKILL_BUTTON,skill_cfg.skill_id)
					return
				else
					target_obj:OnClick()
					GuajiWGCtrl.Instance:DoFightByClick(skill_cfg.skill_id, target_obj)
					return
				end
			end
		else
			local target_obj = GuajiWGCtrl.Instance:SelectAtkTarget(true, {[SceneIgnoreStatus.MAIN_ROLE_IN_SAFE] = true,}, false)
			if target_obj then
				target_obj:OnClick()
				GuajiWGCtrl.Instance:DoFightByClick(skill_cfg.skill_id, target_obj)
				return
			end

			if main_role:GetIsGatherState() then
				Scene.Instance:SendStopGather()
			end

			if is_riding_no_fight_mount then
				MountWGCtrl.Instance:SendMountGoonReq(0)
			end

			local skill_id, attack_index = SkillWGData.ChangeSkillID(skill_cfg.skill_id)
			if SkillWGData.CanUseSkillWithoutTarget(skill_id) then
				main_role:SetAttackIndex(attack_index)
				main_role:DoAttackForNoTarget(skill_id)
				SkillWGCtrl.Instance:OnNoTargetCD(skill_cfg.skill_id)
			else
				if SkillWGData.IsPetSkill(skill_id) then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Pet.PetUseSkillTips)
				end
			end

			GlobalEventSystem:Fire(MainUIEventType.CLICK_SKILL_BUTTON, skill_cfg.skill_id)
		end
	end
end

function MainUIView:SettingChange(setting_type, switch)
    if setting_type == SETTING_TYPE.FIGHT_MOUNT_SKILL_CG_PLAY then
        self.fight_mount_skill_cg_play = switch
    end
end
---------------------- 指示器
-- 初始化范围指示器
function MainUIView:InitSkillRangShow(data)
	if data == nil or SkillWGData.Instance:IsInSkillCDEx(data.cell_index, data.skill_id) then
		return
	end


	if self.is_skill_drag then
		return
	end

	local is_sixiang_skill = SkillWGData.Instance:GetIsSiXiangSkill(data.skill_id)
	local is_longzhu_skill = SkillWGData.Instance:GetIsLongZhuSkill(data.skill_id)
	local is_wuxing_skill = SkillWGData.Instance:GetIsWuXingSkill(data.skill_id)
	local is_halo_skill = SkillWGData.Instance:GetIsHaloSkill(data.skill_id)
	local is_customized_skill = SkillWGData.Instance:GetIsCustomizedSkill(data.skill_id)
	local is_ts_hi_skill = SkillWGData.Instance:GetIsTianShenHeJiSkill(data.skill_id)
	local is_beast_skill = SkillWGData.Instance:GetIsBeastsSkill(data.skill_id)
	self.use_rang_skill = data.skill_id
	self.use_rang_index = data.cell_index
	self.is_skill_drag = true
	-- 策划要求 使用四象\龙珠\五行\光环技能 指示器 不停止挂机
	if not is_sixiang_skill and not is_longzhu_skill and not is_wuxing_skill and not is_halo_skill and not is_beast_skill then
		GuajiWGCtrl.Instance:TrySetTemporary()
	end

	local main_role = Scene.Instance:GetMainRole()
	local is_show_skill_range = true
	
	if not self.rang_is_show then
		self.rang_is_show = true
		self.node_list.root_skill_rang:SetActive(is_show_skill_range)
		if is_sixiang_skill then
			self.skill_rang_transform.position = self.node_list.btn_sixiang_skill.transform.position
		elseif is_longzhu_skill then
			self.skill_rang_transform.position = self.node_list.btn_long_zhu_skill.transform.position
		elseif is_wuxing_skill then
			self.skill_rang_transform.position = self.node_list.btn_wuxing_skill.transform.position
		elseif is_halo_skill then
			self.skill_rang_transform.position = self.node_list.btn_halo_skill.transform.position
		elseif is_beast_skill then
			if self.beasts_skill_list and #self.beasts_skill_list > 0 and self.beasts_skill_list[1] then
				self.skill_rang_transform.position = self.beasts_skill_list[1].view.transform.position
			end
		elseif is_customized_skill then
			for k, cell in pairs(self.customized_skill_list) do
				if nil ~= cell.data and cell.data.skill_id == data.skill_id then
					self.skill_rang_transform.position = cell.view.transform.position
					break
				end
			end
		elseif is_ts_hi_skill then
			self.skill_rang_transform.position = self.node_list.btn_ts_hj_skill.transform.position
		else
			if self.skill_cell_root_list[data.cell_index] then
				self.skill_rang_transform.position = self.skill_cell_root_list[data.cell_index].transform.position
				-- self.skill_clent_pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.node_list["Skill" .. data.cell_index].transform.position)
			end
		end

		self.node_list.root_cancle:SetActive(is_show_skill_range)
		local pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.node_list.root_cancle.transform.position)
		self.cancle_pos = Vector2(pos.x, pos.y)
	end

	self.rang_img_transform.localPosition = Vector3(0, 0, 0)

	local angle = 0
	local length = {x = 0, y = 0}
	local target_pos = nil
	-- local target_logic_pos = nil
	if not IsNil(MainCameraFollow) then
		local target_obj = GuajiWGCtrl.Instance:SelectAtkTarget(true, {[SceneIgnoreStatus.MAIN_ROLE_IN_SAFE] = true,}, false)
		if target_obj ~= nil and not target_obj:IsDeleted() and main_role ~= nil and not main_role:IsDeleted() then
			target_pos = target_obj:GetDrawObj():GetPosition()
			local l_x, l_y = target_obj:GetLogicPos()
			-- target_logic_pos = {x = l_x, y = l_y}
			local m_pos = main_role:GetDrawObj():GetPosition()
			local cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(data.skill_id, 1)
			if cfg ~= nil then
				if cfg.range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET then
				elseif cfg.range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET
					or cfg.range_type == ATTACK_RANGE_TYPE.SECTOR_ME
					or cfg.range_type == ATTACK_RANGE_TYPE.SQUARE_CHONG_FENG then
					local dir_pos = m_pos - target_pos
					local target_dir = u3dpool.v3Normalize(dir_pos)
					local canmera_dir = MainCameraFollow.transform.right * -1
					local c_pos = MainCameraFollow.transform.position
					local m_dir = u3dpool.v3Normalize(c_pos - m_pos)
					local dir = Vector3.Dot(Vector3(m_dir.x, m_dir.y, m_dir.z), Vector3(target_dir.x, target_dir.y, target_dir.z))
					local r_dir = Vector3(target_dir.x, target_dir.y, target_dir.z)
					angle = Vector3.Angle(r_dir, canmera_dir)
					if dir < 0 then
						angle = angle * -1
					end
				end
			end
		end
	end

	self.hide_skill_rang_skill = not is_show_skill_range
	if is_show_skill_range then
		self.hide_skill_cache_pos = nil
		self.hide_skill_send_pos = nil
		self.hide_skill_send_time = 0
		self.hide_skill_need_send = false
		Scene.Instance:UpdateSkillRang(data.skill_id, length, angle, target_pos)
	else
		local use_pos = Scene.Instance:TransformationSkillPos(data.skill_id, length, angle, target_pos)
		if use_pos ~= nil then
			self.hide_skill_cache_pos = use_pos
			self.hide_skill_send_time = Status.NowTime
			self.hide_skill_send_pos = use_pos
			self:OnClickSkill(data, use_pos.x, use_pos.y)
			if main_role ~= nil and not main_role:IsDeleted() then
				main_role:SetDirectionByXY(use_pos.x, use_pos.y, nil, true)
			end
		end
	end

	self.is_cancle_skill = false
	local bundle, asset = ResPath.GetMainUIIcon("a3_zj_qxlse")
	self.node_list.root_cancle.image:LoadSprite(bundle, asset)
end

-- 移除技能范围检测计时器
function MainUIView:RemoveRangCheckTimer()
	if self.skill_check_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.skill_check_timer)
		self.skill_check_timer = nil
	end
end

-- 当按下技能点击
function MainUIView:OnClickRangSkill(data)
	-- print_error("----OnClickRangSkill---", data)
	self:RemoveRangCheckTimer()
	if data and data.is_temp_show then
		return
	end

	local is_limit, limit_tip, limit_type = SkillWGData.Instance:CheckIsLimitUseSkill(data.skill_id)
	if data ~= nil and is_limit then
		if limit_tip ~= nil then
			SysMsgWGCtrl.Instance:ErrorRemind(limit_tip)
		end

		-- if limit_type ~= nil and limit_type == USE_SKILL_LIMIT.ACTIVE_USE_SHOW_CHANGE
		-- 	and not SkillWGData.Instance:IsInSkillCDEx(data.skill_index, data.skill_id) then
			-- ViewManager.Instance:Open(GuideModuleName.MainUiMultiSkillSelectView)
		-- end

		return
	end

	self:InitSkillRangShow(data)
end

-- 当拖住技能
function MainUIView:OnDragSkill(data, eventData)
	self:RemoveRangCheckTimer()
	if data and data.is_temp_show then
		return
	end

	if self.is_force_cancle then
		return
	end

	local is_limit, limit_tip = SkillWGData.Instance:CheckIsLimitUseSkill(data.skill_id)
	if is_limit then
		return
	end

	if SkillWGData.Instance:IsInSkillCDEx(data.cell_index, data.skill_id) then
		if not self.hide_skill_rang_skill then
			return
		else
			if not self.hide_skill_need_send then
				self.hide_skill_need_send = true
			end
		end
	else
		if self.hide_skill_rang_skill and self.hide_skill_need_send and self.hide_skill_cache_pos ~= nil then
			self:OnClickSkill(data, self.hide_skill_cache_pos.x, self.hide_skill_cache_pos.y)
		end
	end

	local x = eventData.position.x
	local y = eventData.position.y
	-- self.is_skill_drag = true
	-- if self.skill_pos_x == nil or self.skill_pos_y == nil then
	-- 	self.skill_pos_x = x
	-- 	self.skill_pos_y = y
	-- else
	-- 	if self.skill_pos_x == x or self.skill_pos_y == y then
	-- 		return
	-- 	end
	-- end

	-- if not self.rang_is_show then
	-- 	self.rang_is_show = true
	-- 	self.node_list.root_skill_rang:SetActive(true)
	-- 	if data ~= nil and self.node_list["Skill" .. data.cell_index] ~= nil then
	-- 		self.skill_rang_transform.localPosition = self.node_list["Skill" .. data.cell_index].transform.localPosition
	-- 		-- self.skill_clent_pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.node_list["Skill" .. data.cell_index].transform.position)
	-- 	end

	-- 	self.node_list.root_cancle:SetActive(true)
	-- 	local pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.node_list.root_cancle.transform.position)
	-- 	self.cancle_pos = Vector2(pos.x, pos.y)
	-- end
	local main_role = Scene.Instance:GetMainRole()
	local z = UICamera.nearClipPlane
	local cam_position = Vector3(x, y, z)
	local event_pos = eventData.position
	local _, position = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.skill_rang_transform,
														event_pos, UICamera, Vector2(0, 0))
	local dir = position.normalized
	local off_x = 0
	local off_y = 0
	if position.x * position.x + position.y * position.y > 64 * 64 then
		self.rang_img_transform.localPosition = dir * 64
		off_x = dir.x * 64 / 64
		off_y = dir.y * 64 / 64
		-- return
	else
		self.rang_img_transform.localPosition = position
		off_x = position.x / 64
		off_y = position.y / 64
	end

	off_x = off_x >= 1 and 1 or off_x
	off_x = off_x <= -1 and -1 or off_x
	off_y = off_y >= 1 and 1 or off_y
	off_y = off_y <= -1 and -1 or off_y
	local length = {x = off_x, y = off_y}

	if self.save_drag_pos == nil then
		self.save_drag_pos = length
	else
		length.x = self.save_drag_pos.x * 0.5 + length.x * 0.5
		length.y = self.save_drag_pos.y * 0.5 + length.y * 0.5
		self.save_drag_pos = length
	end

	if not self.hide_skill_rang_skill then
		Scene.Instance:UpdateSkillRang(data.skill_id, length, math.deg(u3dpool.v2Angle(dir)))
	else
		local use_pos = Scene.Instance:TransformationSkillPos(data.skill_id, length, math.deg(u3dpool.v2Angle(dir)))
		if use_pos ~= nil then
			if self.hide_skill_cache_pos ~= nil then
				-- 人物立刻转
				if use_pos.x ~= self.hide_skill_cache_pos.x or use_pos.y ~= self.hide_skill_cache_pos.y then
					self.hide_skill_cache_pos = use_pos
					if main_role ~= nil and not main_role:IsDeleted() then
						main_role:SetDirectionByXY(use_pos.x, use_pos.y, nil, true)
					end
				end

				-- 技能方向0.3同步一次
				if math.abs(use_pos.x - self.hide_skill_send_pos.x) > 1 or math.abs(use_pos.y - self.hide_skill_send_pos.y) > 1 then
					if Status.NowTime - self.hide_skill_send_time >= 0.3 then
						self.hide_skill_send_pos = use_pos
						self.hide_skill_send_time = Status.NowTime
					end
				end
			else
				self.hide_skill_cache_pos = use_pos
				self.hide_skill_send_time = Status.NowTime
				self.hide_skill_send_pos = use_pos
				if main_role ~= nil and not main_role:IsDeleted() then
					main_role:SetDirectionByXY(use_pos.x, use_pos.y, nil, true)
				end
			end
		end
	end

	if self.cancle_pos ~= nil and not self.hide_skill_rang_skill then
		local img_v_2 = Vector2(event_pos.x, event_pos.y)
		local cancle_len = u3dpool.v2Length(u3dpool.v2Sub(img_v_2, self.cancle_pos), false)
		self.is_cancle_skill = cancle_len <= 39 * 39
	end

	if self.is_in_cancle_rect ~= self.is_cancle_skill then
		self.is_in_cancle_rect = self.is_cancle_skill
		Scene.Instance:FlushSkillRangImgState(self.is_in_cancle_rect)
		local cancle_key = self.is_in_cancle_rect and "a3_zj_qxhs" or "a3_zj_qxlse"
		local bundle, asset = ResPath.GetMainUIIcon(cancle_key)
		self.node_list.root_cancle.image:LoadSprite(bundle, asset)
	end
end

-- 当松开技能点击
function MainUIView:OnSkillUp(data)
	-- print_error("----OnSkillUp---", data, self.hide_skill_rang_skill)
	local is_hide_skill_rang = self.hide_skill_rang_skill
	self.save_drag_pos = nil
	self.use_rang_skill = nil
	self.use_rang_index = nil
	self.is_in_cancle_rect = false
	self.is_force_cancle = false
	local flag = self.is_skill_drag
	self.rang_is_show = false
	self.is_skill_drag = false
	self.skill_pos_x = nil
	self.skill_pos_y = nil
	local is_cancle = self.is_cancle_skill
	self.is_cancle_skill = false
	self.hide_skill_rang_skill = false
	self.hide_skill_cache_pos = nil
	self.hide_skill_send_time = 0

	local x, y = Scene.Instance:GetSkillPos()
	Scene.Instance:HideSkillRnagTool()

	if self.node_list ~= nil and self.node_list.root_skill_rang ~= nil then
		self.node_list.root_skill_rang:SetActive(false)
	end

	if self.node_list ~= nil and self.node_list.root_cancle ~= nil then
		self.node_list.root_cancle:SetActive(false)
	end

	if data and data.is_temp_show then
		return false
	end

	if not is_hide_skill_rang then
		if not is_cancle then
			self:OnClickSkill(data, x, y)
			return true
		end
	end

	return false
end

-- 当点击技能结束
function MainUIView:CheckRangState(data)
	-- print_error("----CheckRangState---", data)
	self:RemoveRangCheckTimer()
	if UnityEngineInput.GetMouseButtonDown(0) then
		self.skill_check_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.TryResetSkillRangState, self), 0.5)--延时处理
	end
end

function MainUIView:TryResetSkillRangState()
	if self.rang_is_show == true then
		self.is_in_cancle_rect = false
		self.is_force_cancle = false
		local flag = self.is_skill_drag
		self.rang_is_show = false
		self.is_skill_drag = false
		self.skill_pos_x = nil
		self.skill_pos_y = nil
		local is_cancle = self.is_cancle_skill
		self.is_cancle_skill = false

		local x, y = Scene.Instance:GetSkillPos()
		Scene.Instance:HideSkillRnagTool()

		if self.node_list ~= nil and self.node_list.root_skill_rang ~= nil then
			self.node_list.root_skill_rang:SetActive(false)
		end

		if self.node_list ~= nil and self.node_list.root_cancle ~= nil then
			self.node_list.root_cancle:SetActive(false)
		end
	end
end

function MainUIView:CheckRangIsVaild(index, skill_id)
	if skill_id ~= nil and self.use_rang_skill ~= nil
		and self.use_rang_skill ~= skill_id
		and index ~= nil
		and self.use_rang_index ~= nil
		and index == self.use_rang_index then
		self:SetSkillForceCancle()
		Scene.Instance:HideSkillRnagTool()
	end
end

function MainUIView:SetSkillForceCancle()
	if self.is_skill_drag then
		self.is_force_cancle = true
	end

	if self.node_list ~= nil and self.node_list.root_skill_rang ~= nil then
		self.node_list.root_skill_rang:SetActive(false)
	end

	if self.node_list ~= nil and self.node_list.root_cancle ~= nil then
		self.node_list.root_cancle:SetActive(false)
	end

	self.rang_is_show = false
	self.is_skill_drag = false
	self.skill_pos_x = nil
	self.skill_pos_y = nil
	self.is_cancle_skill = false
	self.is_in_cancle_rect = false
	self.use_rang_skill = nil
	self.use_rang_index = nil
	self.save_drag_pos = nil
	self.hide_skill_rang_skill = false
	self.hide_skill_cache_pos = nil
	self.hide_skill_send_time = 0
end

function MainUIView:GetIsShowRang()
	return self.rang_is_show
end

--------------------------
-- 【   天神技能拖拽    】 --
--------------------------
function MainUIView:CacularDragTianShenIndex(position)
	local skill_width = self.node_list["bianshen_skill_1"].rect.sizeDelta.x
	local skill_height = self.node_list["bianshen_skill_1"].rect.sizeDelta.y
	local miss_rang = 0
	if (position.y - miss_rang) > (skill_height / 2) or (position.y + miss_rang) < (-skill_height / 2) then
		return -1
	end
	
	local star_x = 0
	local end_x = 0
	for i = 1, 4 do
		star_x = (i - 1) * 10 + (i - 1) * skill_width
		end_x = star_x + skill_width
		if (position.x + miss_rang) > -end_x and (position.x - miss_rang) < -star_x then
			return i
		end
	end

	return -1
end

-- 当按下天神图标
function MainUIView:OnClickChangeOrder(index)
	self.click_tianshen_order_index = index
	self.drag_tianshen_order_index = nil
	self.tianshen_drag_finally_pos = nil
	self.node_list.change_order_icon:SetActive(false)
	self.node_list.change_order_tianshen:SetActive(false)
	self.node_list.change_order_icon:SetActive(false)
end

-- 当拖拽天神图标
function MainUIView:OnDragChangeOrder(index, eventData)  --持续更新
	if self.doing_order_change then
		self.drag_tianshen_order_index = index
		local x = eventData.position.x
		local y = eventData.position.y
		local _, position = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.node_list.bianshen_skill_list.transform,
															eventData.position, UICamera, Vector2(0, 0))
		self.node_list.change_order_icon.transform.anchoredPosition = position
		self.tianshen_drag_finally_pos = position
	end
end

-- 当松开天神图标
function MainUIView:OnChangeOrderUp(index) --结束拖动
	if self.doing_order_change then
		if self.click_tianshen_order_index and self.tianshen_drag_finally_pos then
			local skill_info, chuzhan_amount = TianShenWGData.Instance:GetTianShenSkillInfoByMainView()
			local enter_tianshen_order_index = self:CacularDragTianShenIndex(self.tianshen_drag_finally_pos)
			if skill_info[self.click_tianshen_order_index - 1] and skill_info[enter_tianshen_order_index - 1] then
				local bianshen_cfg1 = TianShenWGData.Instance:GetTianShenCfg(skill_info[self.click_tianshen_order_index - 1].image_index)
				local bianshen_cfg2 = TianShenWGData.Instance:GetTianShenCfg(skill_info[enter_tianshen_order_index - 1].image_index)
				TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type20, bianshen_cfg1.index, bianshen_cfg2.index)
			end
		end
	end

	self.node_list.change_order_icon:SetActive(false)
	self.node_list.change_order_tianshen:SetActive(false)
	self.tianshen_drag_finally_pos = nil
	self.click_tianshen_order_index = nil
	self.doing_order_change = false
end

-- 拖动离开，开始显示拖动icon
function MainUIView:CheckChangeOrderState(index)
	if self.click_tianshen_order_index and self.click_tianshen_order_index == index then 
		local skill_info, chuzhan_amount = TianShenWGData.Instance:GetTianShenSkillInfoByMainView()
		if skill_info[index - 1] then
			self.node_list.change_order_icon:SetActive(true)
			self.node_list.change_order_tianshen:SetActive(true)
			local bianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(skill_info[index - 1].image_index)
			self.node_list.change_order_tianshen.image:LoadSprite(ResPath.GetSkillIconById(bianshen_cfg.bianshen_icon))
			self.doing_order_change = true
		else
			self.node_list.change_order_icon:SetActive(false)
			self.node_list.change_order_tianshen:SetActive(false)
			self.doing_order_change = false
		end
	end
end



--------------------------
-- 【     按钮事件      】 --
--------------------------
-- 切换攻击目标，依次为，敌方玩家，敌方boss，敌方小怪
function MainUIView:ChangeTargetObj()			-- 向上
	local scene_logic = Scene.Instance:GetSceneLogic()
	local obj_id = scene_logic:GetNextSelectTargetId()
	if -1 == obj_id then
		return
	end

	local select_obj = Scene.Instance:GetObjectByObjId(obj_id)
	if not select_obj then
		return
	end

	if not Scene.Instance:IsEnemy(select_obj) then
		return
	end

	MainuiWGCtrl.Instance:StopAutoRevenge()
	if not select_obj:IsDeleted() and select_obj:IsRole() then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			scene_logic:ClearGuaJiInfo()
		end
	end

	local guaji_type = GuajiCache.guaji_type
	if GuajiType.None ~= guaji_type and GuajiType.Temporary ~= guaji_type then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
		GuajiWGCtrl.Instance:SetGuajiType(guaji_type)
	end

	GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
	-- 切换攻击目标为人物
	-- self:SelectObj(SceneObjType.Role, SelectType.Alive, "dis")  --根据与主角距离选择
end

function MainUIView:SelectObj(obj_type, select_type, sort_type)
	-- 获取所有可选对象
	local obj_list = Scene.Instance:GetObjListByType(obj_type)
	if not next(obj_list) then
		return
	end

	local temp_obj_list = {}
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local target_x, target_y = 0, 0
	local can_select = true
	for k, v in pairs(obj_list) do
		can_select = true
		if SelectType.Friend == select_type then
			can_select = Scene.Instance:IsFriend(v)
		elseif SelectType.Enemy == select_type then
			can_select = Scene.Instance:IsEnemy(v, self.main_role)
		elseif SelectType.Alive == select_type then
			can_select = not v:IsRealDead() and Scene.Instance:IsEnemy(v, self.main_role)
		end

		if can_select then
			target_x, target_y = v:GetLogicPos()
			table.insert(temp_obj_list, {obj = v, hp = (v.vo.hp / v.vo.max_hp),
										dis = GameMath.GetDistance(x, y, target_x, target_y, false)})
		end
	end

	if not next(temp_obj_list) then
		return
	end
	-- table.sort(temp_obj_list, function(a, b) return a.dis < b.dis end)
	SortTools.SortAsc(temp_obj_list, sort_type)

	-- 排除已选过的
	local select_obj_list = self.select_obj_group_list[obj_type]
	if nil == select_obj_list then
		select_obj_list = {}
		self.select_obj_group_list[obj_type] = select_obj_list
	end

	-- 策划需求  只在血量最少的两个人里面来回选择
	local select_obj = nil
	for i, v in ipairs(temp_obj_list) do
		local last_select_obj = select_obj_list[v.obj:GetType()]
		if nil == last_select_obj or last_select_obj:GetObjId() ~= v.obj:GetObjId() then
			select_obj = v.obj
			break
		end
	end

	-- 如果没有选中，选第一个，并清空已选列表
	if nil == select_obj then
		select_obj = temp_obj_list[1].obj
		select_obj_list = {}
		self.select_obj_group_list[obj_type] = select_obj_list
	end

	if nil == select_obj then
		return
	end

	select_obj_list[select_obj:GetObjId()] = select_obj
	GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, "select")
	return select_obj
end

















--------------------------
-- 【       其他        】 --
--------------------------
-- 放技能弹出的技能名称
function MainUIView:PlaySkillNameFlyAnimitor(skill_id)
	local bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_effect_skill)
	local skill_cfg = MainuiWGData.Instance:GetSkillConfigByid(skill_id)
	if next(skill_cfg) then
		EffectManager.Instance:PlayAtTransform(bundle, asset, self.effect_root.transform, nil, Vector3(-300, 125, 0),
												nil, nil, BindTool.Bind(self.PlaySkillNameFlyCallBack, self, skill_cfg))
	end
end

function MainUIView:PlaySkillNameFlyCallBack(skill_cfg, obj)
    local skill_name_list = Split(skill_cfg.skill_name, "#")
    local text_1 = obj.transform:Find("GameObject/Text1")
    local text_2 = obj.transform:Find("GameObject/Text2")
    if text_1 and text_2 then
    	text_1:GetComponent(typeof(UnityEngine.UI.Text)).text = skill_name_list[1]
    	text_2:GetComponent(typeof(UnityEngine.UI.Text)).text = skill_name_list[2]
    end
end

function MainUIView:GetSkillRightOtherContent()
    return self.node_list.RightOtherContent
end

-- 获得新技能
function MainUIView:AddNewSkill(skill_index)
	local cell = self:GetSkillCellBySkillIndex(skill_index)
	if nil ~= cell then
		return true, cell:GetView()
	end

	return false, nil
end

function MainUIView:GetSkillCellBySkillIndex(index)
	local cell_index = self:GetCellIndexBySkilllIndex(index)
	return self.common_skill_list[cell_index]
end

function MainUIView:GetCellIndexBySkilllIndex(index)
	local skill_cfg = SkillWGData.Instance:GetSkillConfigByIndex(index)
	if skill_cfg == nil then
		return 0
	end

	local skill_index = SkillWGData.GetSkillBtnIndex(skill_cfg.skill_id)
	if MainUIView.SKILL_INDEX_1 <= skill_index and skill_index <= MainUIView.SKILL_INDEX_5 then
		return skill_index
	end

	return 0
end

function MainUIView:GetSkillSkillIndex(index)
	return self.common_skill_list[index]
end

-- 特殊需求: 显示和隐藏技能按钮 (如跨服答题)
function MainUIView:SetSkillShowState(enable)
	self.node_list.normal_content:SetActive(enable)
end

function MainUIView:GetSkillContent()
	return self.node_list.SkillControl
end

function MainUIView:GetSkillButtonPosition()
	return self.skill_cell_root_list
end

function MainUIView:GetBeastSkillButtonPosition()
	return self.beasts_skill_root_list
end

--------------- 轻功 ---------------
-- 点击轻功跳跃
local guide_goon = true
function MainUIView:OnClickJump(is_guide)
	if Scene.Instance:IsChangeSceneIng() then
		return false
	end

	if TaskWGCtrl.Instance:IsFly() then
		return false
	end

	if FunctionGuide.Instance:GetIsGuide() and (not is_guide) then
		return
	end

	UnityEngine.Time.timeScale = 1
	local qinggong_delay = self.qinggong_down_count_time[self.qinggong_index + 1] or 2
	if FunctionGuide.GUIDE_JUMP_TARGET and (not guide_goon and self.qinggong_index > 0) then
		return false
	end

	guide_goon = FunctionGuide.GUIDE_JUMP_TARGET == nil
	-- if self.qinggong_index < 4 and self.jump_energy < JUMP_MAX_ENERGY / 4 then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.SkillJump)
	-- 	return false
	-- end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return false
	end

	if MultiMountWGData.Instance:IsMyMultiMountTake() then
		local ok_func = function()
			MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.CANCLE_RIDE)
		end

		TipWGCtrl.Instance:OpenAlertTips(Language.MultiMount.CancelMultiMountTake, ok_func)
		return
	end

	if main_role:IsInteractive() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return false
	end

	main_role:Jump()
	self.is_show_jump = true
	-- if FunctionGuide.GUIDE_JUMP_TARGET then
	-- 	if self.force_start_next_timer then
	-- 		GlobalTimerQuest:CancelQuest(self.force_start_next_timer)
	-- 		self.force_start_next_timer = nil
	-- 	end
	-- 	FunctionGuide.Instance:FinishedOpenFun(true, true)
	-- 	if self.qinggong_index < FunctionGuide.Instance:GetCurConfigSteps() - 2 then
	-- 		self.force_start_next_timer = GlobalTimerQuest:AddDelayTimer(function()
	-- 			FunctionGuide.Instance:FinishedOpenFun(false, true)
	-- 			guide_goon = true
	-- 		end, qinggong_delay)
	-- 	end
	-- end
end

-- 点击轻功着陆
function MainUIView:OnClickLanding(is_guide)
	if FunctionGuide.Instance:GetIsGuide() and (not is_guide) then
		return
	end

	self:CleanAddEnergyTimer()
	self:CleanJumpUpEnergyTimer()
	UnityEngine.Time.timeScale = 1
	if FunctionGuide.GUIDE_JUMP_TARGET and not guide_goon then
		return
	end

	guide_goon = true
	local main_role = Scene.Instance:GetMainRole()
	main_role:Landing()
end

-- 点击轻功上升停留
function MainUIView:OnClickJumpUpPoint()
	self:CleanJumpUpEnergyTimer()
end

-- 点击轻功上升点击上升
function MainUIView:OnClickJumpUpClickUp()
	self:CleanJumpUpEnergyTimer()
end

-- 点击轻功上升点击按下
function MainUIView:OnClickJumpUpClickDown()
	self:CleanJumpUpEnergyTimer()
	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	if main_role.has_play_qinggong_land or (not main_role:IsMitsurugi()) then
		return
	end

	self:FlushJumpUpEnergyShow()
end

-- 清除倒计时上升
function MainUIView:CleanJumpUpEnergyTimer()
	if self.add_energy_up_time then
		GlobalTimerQuest:CancelQuest(self.add_energy_up_time)
		self.add_energy_up_time = nil
	end
end

function MainUIView:FlushJumpUpEnergyShow()
	self:CleanJumpUpEnergyTimer()
	self.add_energy_up_time = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushEnergyUpTime, self), 0.03)
end

function MainUIView:FlushEnergyUpTime()
	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	main_role:JumpUp()
end

-- 御剑冲刺
function MainUIView:OnClickJumpSprint(is_guide)
	if self.is_in_sprint_cd then
		return
	end

	if FunctionGuide.Instance:GetIsGuide() and (not is_guide) then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	if main_role:IsSwordSprint() then
		return
	end

	if main_role:JumpSprint() then
		self:CleanJumpUpEnergyTimer()
		--生成特效位移
		local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
		local move_sword_sprint_cd = other_cfg and other_cfg.move_sword_sprint_cd or 3000
		self.node_list.JumpButtonUpSlider:CustomSetActive(true)
		self.node_list.JumpButtonUpSlider.slider.value = 1
		self.node_list.JumpButtonUpSlider.slider:DOValue(0, move_sword_sprint_cd / 1000)
		self.is_in_sprint_cd = true
	
		self.add_energy_up_time = GlobalTimerQuest:AddDelayTimer(function()
			self.is_in_sprint_cd = false
			self.node_list.JumpButtonUpSlider:CustomSetActive(false)
		end, move_sword_sprint_cd / 1000)
	end
end

-- 是否是御剑冲刺阶段
function MainUIView:IsInSprintCd()
	return self.is_in_sprint_cd
end

function MainUIView:IsShowJumpBtn()
	if self.node_list["JumpButton"] then
		local enabled_qing_gong = false
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			enabled_qing_gong = main_role:IsCanUseQingGong()
		end

		self.node_list["JumpButton"]:SetActive(enabled_qing_gong)
	end
end

--刷新跳跃显示
function MainUIView:FlushJumpState(qinggong_index)
	if qinggong_index <= 0 or self.qinggong_index ~= 0 and self.qinggong_index == qinggong_index then
		return
	end

	self.jump_energy = JUMP_MAX_ENERGY
	self:FlushJumpEnergyShow()
	self.is_show_jump = true
	self.qinggong_index = qinggong_index
end

function MainUIView:CleanAddEnergyTimer()
	if self.add_energy_time then
		GlobalTimerQuest:CancelQuest(self.add_energy_time)
		self.add_energy_time = nil
	end
end

function MainUIView:FlushJumpEnergyShow()
	if self.jump_energy > 0 then
		self:CleanAddEnergyTimer()
		self.add_energy_time = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushEnergyTime, self), 0.1)
	end
end

function MainUIView:FlushEnergyTime()
	self.jump_energy = self.jump_energy - 0.1

	if self.node_list["CDJump"] and self.node_list["CDJump"].image then
		self.node_list["CDJump"].image.fillAmount = self.jump_energy / JUMP_MAX_ENERGY
	end

	if self.jump_energy <= 0 then
		self:CleanAddEnergyTimer()
		self:OnClickLanding()
end
end

function MainUIView:OnMainRoleJumpState(state)
	if state then
		if self.is_show_jump then
			-- self.node_list["FlyJumpButton"]:SetActive(true)
			self.node_list["LandingButtonDown"]:SetActive(true)
			self.node_list["JumpButtonUp"]:SetActive(true)
			self.node_list["JumpContentImage"]:SetActive(true)
			-- GlobalEventSystem:Fire(MainUIEventType.CHNAGE_FIGHT_STATE_BTN, true)
			MainuiWGCtrl.Instance:FlushView(0, "FlyTaskIsHide", {true})
		end
	else
		-- self.node_list["FlyJumpButton"]:SetActive(false)
		self.node_list["LandingButtonDown"]:SetActive(false)
		self.node_list["JumpButtonUp"]:SetActive(false)
		self.node_list["JumpButtonUpSlider"]:SetActive(false)
		self.node_list["JumpContentImage"]:SetActive(false)
		-- GlobalEventSystem:Fire(MainUIEventType.CHNAGE_FIGHT_STATE_BTN, false)
		MainuiWGCtrl.Instance:FlushView(0, "FlyTaskIsHide", {false})
		self.qinggong_index = 0
		self.is_show_jump = false
		self.is_in_sprint_cd = false
	end
end
--------------- 轻功 end ---------------

function MainUIView:OnClickZuoQi(isOn)
	if MultiMountWGData.Instance:IsMyMultiMountTake() then
		local ok_func = function()
			MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.CANCLE_RIDE)
		end

		TipWGCtrl.Instance:OpenAlertTips(Language.MultiMount.CancelMultiMountTake, ok_func)
		return
	end

	local isopen = FunOpen.Instance:GetFunIsOpened(FunName.MountLingMount)
	if not isopen then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.MountFunOpenTip)
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role or not main_role:CanDoMove() or main_role:IsQingGong() or main_role:HasCantMoveBuffButCanShowMove() then
		return
	end

	local is_down = main_role:GetCurRidingResId() > 0
   	if main_role:IsFightState() then
		if is_down and main_role:IsRidingFightMount() then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Fight.FightNoDismount)
			return
		elseif not is_down then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Fight.FightNoMount)
			return
		end
	end

	local flag, str = RoleWGData.Instance:CheckCanMount()
	if not flag then
        if str then
            TipWGCtrl.Instance:ShowSystemMsg(str)
        end
        return
    end

    local cur_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if cur_scene_cfg.pb_mount == 1 then
    	TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ScenePBMount)
		return
    end

	if is_down then
		MountWGCtrl.Instance:SendMountGoonReq(0)
	else
		if main_role:GetIsInSit() then
			Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
		end

		MountWGCtrl.Instance:SendMountGoonReq(1)
	end
end

function MainUIView:ChangeZuoQiState(flag)
	local is_up_now = flag == 1
	self.node_list.ButtonZuoqiDown:SetActive(is_up_now)
	self.node_list.ButtonZuoqiUp:SetActive(not is_up_now)
end

--显示隐藏 坐骑按钮
function MainUIView:SetZuoqiBtnState(enable)
	--XUI.SetButtonEnabled(self.node_list.ButtonZuoqi,enable)
	local fun_name = FunOpen.Instance:GetFunNameByViewName(GuideModuleName.NewAppearanceWGView) or GuideModuleName.NewAppearanceWGView
	local is_appearance_func_open, tips = FunOpen.Instance:GetFunIsOpened(fun_name, true)

	if enable ~= nil then
		self.node_list.ButtonZuoqi:CustomSetActive(is_appearance_func_open and enable)
	else
		self.node_list.ButtonZuoqi:CustomSetActive(is_appearance_func_open)
	end
end

function MainUIView:SetZuoqiBtnShowState(show)
	if self.node_list["zuoqi_bg"] then
		self.node_list["zuoqi_bg"]:SetActive(show)
	end
end

function MainUIView:UpdateDuanSkillIcon()
	local duan_skill_id = SkillWGData.Instance:GetProfDuanSkillId()
	local duan_atk = SkillWGData.Instance:GetNextDuanAtk()

	local cell = self:GetSkillCellBySkillId(duan_skill_id)
	if nil ~= cell then
		cell:UpdateCellData(duan_skill_id, duan_atk)
	end
end

function MainUIView:GetSkillCellBySkillId(skill_id)
	for i,v in pairs(self.common_skill_list) do
		local data = v:GetData()
		if data and data.skill_id == skill_id then
			return v
		end
	end

	return nil
end

function MainUIView:UpdateSkillVisible()
	--GuildBattleRankedWGCtrl.Instance:IsShowCanacelChangeBtn(is_xmz)
	self:FlushSkillList()
	self:UpdateBianShenSkillState()
	self:FlushBuffLayer(true)
end

-------------- 角色buff改变
function MainUIView:OnMainRoleBuffChange(buff_type)
	if not self.common_skill_list then
		return
	end
	
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local cant_attack = main_role:IsChenMo()	--只有沉默才显示禁止这个表现
		for k,v in pairs(self.common_skill_list) do
			v:FlushForbidStatus(cant_attack)
		end

		if self.wuxing_skill_cell then
			self.wuxing_skill_cell:FlushForbidStatus(cant_attack)
		end

		if self.halo_skill_cell then
			self.halo_skill_cell:FlushForbidStatus(cant_attack)
		end

		for i = 5, 10 do
			if self.customized_skill_list[i] then
				self.customized_skill_list[i]:FlushForbidStatus(cant_attack)
			end
		end
	end

	if buff_type ~= nil then
		self:TryFlushBuffLayer(buff_type)
	end
end

function MainUIView:TryFlushBuffLayer(buff_type)
	if buff_type ~= nil and self.listen_buff_type_list[buff_type] ~= nil then
		self:FlushBuffLayer()
	end
end

function MainUIView:SetBuffLayerFlag(buff_type)
	if self.listen_buff_type_list then
		self.listen_buff_type_list[buff_type] = true
	end
end

function MainUIView:FlushBuffLayer(is_init)
	if is_init then
		self.listen_buff_type_list = {}
	end

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local is_system_bianshen = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance
	local off = 2
	local skill_info_list = nil

    if is_system_bianshen then
    	local appearance_param = RoleWGData.Instance:GetAttr('appearance_param')
    	local tianshen_index = TianShenWGData.Instance:GetImageModelByAppeId(appearance_param, false)
		tianshen_index = tianshen_index and tianshen_index.index
    	
        skill_info_list = SkillWGData.Instance:GetBianShenSkill(tianshen_index, true)
        off = 0
    else
		skill_info_list = SkillWGData.Instance:GetCurrSkillList()
    end

    for k,v in ipairs(self.common_skill_list) do
    	local data = skill_info_list and skill_info_list[k + off]
    	v:FlushBuffLayer(data, is_init)
    end
end
-------------- 角色buff改变 end

-------------- 角色脱战
function MainUIView:OnMainRoleExitFightState()
	--self:ShowBeastGroupSkill(false)
end
-------------- 角色脱战 end

-------------- Boss刷新卡
function MainUIView:HideBossRefreshBtns()
    self.show_boss_refresh_btn = false
    self.node_list.boss_card_hide_btn:SetActive(self.show_boss_refresh_btn)
    self.node_list.boss_btn_container:SetActive(self.show_boss_refresh_btn)
end

function MainUIView:ChangeBossRefreshBtnsState()
    self.show_boss_refresh_btn = not self.show_boss_refresh_btn
    self.node_list.boss_card_hide_btn:SetActive(self.show_boss_refresh_btn)
    self.node_list.boss_btn_container:SetActive(self.show_boss_refresh_btn)
end

--场景改变回调
function MainUIView:UpdateQucikItemBtnStateByScene(new_scene_type)
    self.show_boss_refresh_btn = false
    self.node_list.boss_card_hide_btn:SetActive(self.show_boss_refresh_btn)
    self.node_list.boss_btn_container:SetActive(self.show_boss_refresh_btn)
    local status_tb, is_show_btn = MainuiWGData.Instance:UpdateQuickItemByScene(new_scene_type)
	local can_update = MainuiWGData.Instance:BossRefreshItemIdCanUpdate()
    self:ChangeQuickItemBtnState(is_show_btn and can_update, status_tb)
    self:RefreshBossItemCellsData()
end

--物品改变回调
function MainUIView:UpdateQucikItemBtnStateByItemId(item_id)
	local can_update = MainuiWGData.Instance:BossRefreshItemIdCanUpdate()
	if not can_update then
		return
	end
	
    if item_id and MainuiWGData.Instance:IsBossRefreshInvokeItem(item_id) then
        local status_tb, is_show_btn = MainuiWGData.Instance:UpdateQuickItemByScene()
		self:ChangeQuickItemBtnState(is_show_btn and can_update, status_tb)
        self:RefreshBossItemCellsData()
    end
end

function MainUIView:ChangeQuickItemBtnState(is_show_btn, status_tb)
    self.node_list["boss_use_card"]:SetActive(is_show_btn)
    for i = 1, 4 do
        self.node_list["boss_card_"..i]:SetActive(status_tb[i] == true)
    end
end

function MainUIView:RefreshBossItemCellsData()
    for i = 1, 4 do
        local item_id = MainuiWGData.Instance:GetBossRefreshItemId(i)
        if not self.boss_card_cell_list then
            self.boss_card_cell_list = {}
        end
        if not self.boss_card_cell_list[i] then
            self.boss_card_cell_list[i] = BossRefreshCard.New(self.node_list["boss_card_"..i])
        end

		local num = 0
		local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
		local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
		if is_in_team and is_leader and item_id then
			num = NewTeamWGData.Instance:GetSelectUseShareItemInfo(item_id)
		else
			num = ItemWGData.Instance:GetItemNumInBagById(item_id)
		end

        self.boss_card_cell_list[i]:SetData({item_id = item_id, idx = i, cell_scale = 0.9, num = num})
		self.boss_card_cell_list[i]:SetRightBottomTextVisible(true)
		self.boss_card_cell_list[i]:SetRightBottomColorText(num)
    end
end
-------------- Boss刷新卡 end

-- 秘笈技能
function MainUIView:OnFlushEsotericaSkill()
	local show_list = CultivationWGData.Instance:GetEsotericaShowSkill()
	for k,v in pairs(self.esoterica_skill_list) do
		v:SetData(show_list[k])
	end
end

-- 技能释放 不走通用技能协议那一套
function MainUIView:OnDoSkillBySpecialFunc(skill_data)
	if not skill_data then
		return
	end

	local skill_id = skill_data.skill_id
	local skill_level = skill_data.level or 1
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() then
		return
	end

	local skill_pos_x, skill_pos_y = main_role:GetLogicPos()
	local target_obj
	if SkillWGData.Instance:IsSkillToSelf(skill_id) then
		-- local forward = main_role:GetRoot().gameObject.transform.forward
		-- local dir = math.atan2(forward.z, forward.x)
	else
		if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and Scene.Instance:IsEnemy(GuajiCache.target_obj) then
			local g_x, g_y = GuajiCache.target_obj:GetLogicPos()
			local cur_dis = GameMath.GetDistance(g_x, g_y, skill_pos_x, skill_pos_y, true)
			local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, skill_level)
			if skill_cfg and cur_dis <= skill_cfg.distance then
				target_obj = GuajiCache.target_obj
			end
		end
	end

	if main_role:GetIsGatherState() then
		Scene.Instance:SendStopGather()
	end

	local skill_x, skill_y
	if not FightWGCtrl.Instance:TryUseRoleSpecialSkill(skill_id, target_obj, skill_x, skill_y, true) then
		return
	end

	local is_riding_no_fight_mount = main_role:IsRidingNoFightMount()
	if is_riding_no_fight_mount then
		MountWGCtrl.Instance:SendMountGoonReq(0)
	end
	
	local target_obj_id = target_obj and target_obj:GetObjId() or COMMON_CONSTS.INVALID_OBJID
	TaskGuide.LAST_CLICK_SKILL_TIME = Status.NowTime
	local is_esoterica_skill = SkillWGData.Instance:GetIsEsotericaSkill(skill_id)
	if is_esoterica_skill then
		CultivationWGCtrl.Instance:OnEsotericaOperate(ESOTERICA_OPERATE_TYPE.PERFORM_SKILL, skill_data.slot, target_obj_id, skill_pos_x, skill_pos_y)
		-- 天眼破盾要发qte的协议
		if skill_data.slot == 6 then
			local cur_select_target = GuajiCache.target_obj
			if cur_select_target then
				local objid = cur_select_target:GetObjId()
				if objid and objid >= 0 then
					RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_DO_MONSTER_QTE, objid)
				end
			end
		end
	end

	-- GlobalEventSystem:Fire(MainUIEventType.CLICK_SKILL_BUTTON, skill_id)
end

function MainUIView:PlayUseEsotericaSkillEffect(seq)
	local remove_index_list = CultivationWGData.Instance:GetEsotericaRemoveIndexList()
	if #remove_index_list > 0 then
		local index = remove_index_list[1]
		if self.esoterica_skill_list[index] then
			CultivationWGData.Instance:ChangeEsotericaRemoveIndexList(1)
			self.esoterica_skill_list[index]:PlayUseEsotericaSkillEffect(seq)
		end
	end
end

-- 打开boss复活卡类的管理界面
function MainUIView:OnClickOpenBossCardWindow()
	BossWGCtrl.Instance:OpenBossQuickRebirthShow()
end

------------------boss刷新卡相关-----------------
BossRefreshCard = BossRefreshCard or BaseClass(ItemCell)

function BossRefreshCard:SetData(data)
    local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
	self:GetView():SetActive(item_cfg ~= nil)
	if not item_cfg then
		return
	end
	local scale = data.cell_scale
	if scale then
		self.root_node.rect.localScale = Vector3(scale, scale, scale)
    end
    self:SetRightBottomColorText(CommonDataManager.NotConverExtend(data.num))
    ItemCell.SetData(self, data)
end

----------------------------------------------------
-- 技能释放（幻兽）
function MainUIView:OnDoSkillByBeastsFunc(skill_data, fake_skill_pos)
	local is_hide_skill_rang = self.hide_skill_rang_skill
	self.save_drag_pos = nil
	self.use_rang_skill = nil
	self.use_rang_index = nil
	self.is_in_cancle_rect = false
	self.is_force_cancle = false
	local flag = self.is_skill_drag
	self.rang_is_show = false
	self.is_skill_drag = false
	self.skill_pos_x = nil
	self.skill_pos_y = nil
	local is_cancle = self.is_cancle_skill
	self.is_cancle_skill = false
	self.hide_skill_rang_skill = false
	self.hide_skill_cache_pos = nil
	self.hide_skill_send_time = 0

	if self.node_list ~= nil and self.node_list.root_skill_rang ~= nil then
		self.node_list.root_skill_rang:SetActive(false)
	end

	if self.node_list ~= nil and self.node_list.root_cancle ~= nil then
		self.node_list.root_cancle:SetActive(false)
	end
	Scene.Instance:HideSkillRnagTool()

	if skill_data and skill_data.is_temp_show then
		return false
	end

	if not skill_data then
		return false
	end

	local skill_id = skill_data.skill_id
	local skill_level = skill_data.level or 1
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() then
		return false
	end

	if is_cancle then
		return false
	end

	if not main_role:CanDoYuLong() then	-- 使用条件和御龙一样，直接使用
		return false
	end

	local skill_pos_x, skill_pos_y = main_role:GetLogicPos()
	local target_obj

	if SkillWGData.Instance:IsSkillToSelf(skill_id) then
		-- local forward = main_role:GetRoot().gameObject.transform.forward
		-- local dir = math.atan2(forward.z, forward.x)
	else
		if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and Scene.Instance:IsEnemy(GuajiCache.target_obj) then
			local g_x, g_y = GuajiCache.target_obj:GetLogicPos()
			local cur_dis = GameMath.GetDistance(g_x, g_y, skill_pos_x, skill_pos_y, true)
			local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, skill_level)
			if skill_cfg and cur_dis <= skill_cfg.distance then
				target_obj = GuajiCache.target_obj
			end
		end
	end
	
	local target_obj_id = target_obj and target_obj:GetObjId() or COMMON_CONSTS.INVALID_OBJID
	local skill_x, skill_y = Scene.Instance:GetSkillPos()
	-- local real_skill_index = SkillWGData.Instance:GetRealSkillIndex(skill_data.skill_id, true) or 0
	local skill_id, attack_index = SkillWGData.ChangeSkillID(skill_id)
	local pos_x, pos_y = skill_pos_x, skill_pos_y

	if skill_x ~= nil and skill_y ~= nil then
		pos_x, pos_y = skill_x, skill_y
		local world_x, world_y = GameMapHelper.LogicToWorld(skill_x, skill_y)
		local target_real_pos = Vector2(world_x, world_y)
		local real_pos_x, real_pos_y = main_role:GetRealPos()
		--有些特殊情况，客户端会设置临时的障碍区，这里做下路径判断，避免冲锋型技能直接穿越障碍区
		pos_x, pos_y = GameMapHelper.GetRealChongFengLogicPos2(Vector2(real_pos_x, real_pos_y), target_real_pos)
		if pos_x == nil or pos_y == nil then
			pos_x, pos_y = skill_x, skill_y
		end
	end

	if fake_skill_pos then
		pos_x = fake_skill_pos.x and fake_skill_pos.x ~= "" and fake_skill_pos.x or pos_x
		pos_y = fake_skill_pos.y and fake_skill_pos.y ~= "" and fake_skill_pos.y or pos_y
	end

	local beast_obj = main_role:FindBeast()	--- 这个是表示位置，服务器取得0-2, 客户端要加1
	if beast_obj then
		beast_obj:TryUseBeastSkill(skill_id, pos_x, pos_y, target_obj_id, true)
	else
		local main_role_vo = main_role:GetVo()

		-- 隐藏了幻兽存在上阵幻兽，直接使用技能
		if main_role_vo and main_role_vo.beast_id and main_role_vo.beast_id > 0 then
			ControlBeastsWGCtrl.Instance:OpenBeastsSkillShowTips(main_role_vo.beast_id)
			ControlBeastsWGCtrl.Instance:SendCSBeastUseSkill(skill_id, target_obj_id, pos_x, pos_y)	
		end
	end

	return true
end

function MainUIView:PlayCommonSkillBaoDianEffect(skill_id)
	local cell = self:GetSkillCellBySkillId(skill_id)
	if cell then
		cell:PlayBaoDianEffect()
	end
end

-- 元素反应相关
function MainUIView:FlushBeastBuffIcon(is_show, element_type, remaining_time)
	self.node_list.beasts_skill_bomb_effect:CustomSetActive(false)
	self.node_list.beasts_skill_shining_effect:CustomSetActive(false)
	if is_show then
		if not element_type or not remaining_time then
			return
		end

		self.node_list.beasts_skill_effect_root:CustomSetActive(true)
		self:CleanBeastSkillEffectCountDown()
		self.node_list.beasts_skill_effect_root.slider.value = 1
		CountDownManager.Instance:AddCountDown("beast_skill_effect_count_down", BindTool.Bind(self.UpdateBeastSkillEffectCountDown, self), BindTool.Bind(self.CompleteSkillEffectCountDown, self), nil, remaining_time, 0.1)
		local img_bundle, img_asset = ResPath.GetControlBeastsImg("a3_hs_gm_type_big_" .. element_type)
		self.node_list.beasts_skill_effect_icon.image:LoadSprite(img_bundle, img_asset)

		if BOMB_BEAST_EFFECT_TYPE[element_type] then
			local bomb_effect_bundle, bomb_effect_asset = ResPath.GetEffect(BOMB_BEAST_EFFECT_TYPE[element_type])
			self.node_list.beasts_skill_bomb_effect:ChangeAsset(bomb_effect_bundle, bomb_effect_asset)
			self.node_list.beasts_skill_bomb_effect:CustomSetActive(true)
		end

		if BEAST_EFFECT_TYPE[element_type] then
			local shining_effect_bundle, shining_effect_asset = ResPath.GetEffect(BEAST_EFFECT_TYPE[element_type])
			self.node_list.beasts_skill_shining_effect:ChangeAsset(shining_effect_bundle, shining_effect_asset)
		end

		AddDelayCall(self, function ()
			self.node_list.beasts_skill_bomb_effect:CustomSetActive(false)
			self.node_list.beasts_skill_shining_effect:CustomSetActive(true)
		end, 0.5)
	else
		self.node_list.beasts_skill_bomb_effect:SetActive(true)
		AddDelayCall(self, function ()
			self.node_list.beasts_skill_bomb_effect:SetActive(false)
			self.node_list.beasts_skill_effect_root:CustomSetActive(false)
		end, 0.5)
	end
end

function MainUIView:PlayBeastComboBuffAni(combo_skill_img, element_param1, element_param2, skill_bundle, skill_asset)
	self.node_list.special_beasts_skill_effect_root:CustomSetActive(false)
	self.node_list.special_beasts_skill_effect:CustomSetActive(false)
	if not combo_skill_img then
		return
	end

	self.node_list.special_beasts_skill_effect_root:SetActive(true)
	self.node_list.beasts_combo_skill_root:CustomSetActive(element_param1 ~= nil and element_param2 ~= nil)
	if element_param1 ~= nil and element_param2 ~= nil then
		local img_bundle1, img_asset1 = ResPath.GetControlBeastsImg("a3_hs_gm_type_big_" .. element_param1)
		self.node_list.beasts_combo_skill_img1.image:LoadSprite(img_bundle1, img_asset1)
	
		local img_bundle2, img_asset2 = ResPath.GetControlBeastsImg("a3_hs_gm_type_big_" .. element_param2)
		self.node_list.beasts_combo_skill_img2.image:LoadSprite(img_bundle2, img_asset2)
	end

	if skill_bundle ~= nil and skill_asset ~= nil then
		self.node_list.special_beasts_skill_effect:ChangeAsset(skill_bundle, skill_asset)
	end

	local di_bundle, di_asset = ResPath.GetRawImagesPNG(combo_skill_img)
	self.node_list.special_beasts_skill_img.raw_image:LoadSprite(di_bundle, di_asset, function ()
		self.node_list.special_beasts_skill_img.raw_image:SetNativeSize()
		self.node_list.special_beasts_skill_img.transform.localScale = u3dpool.vec3(1.4, 1.4, 1.4)

		if self.special_beasts_skill_tween then
			self.special_beasts_skill_tween:Kill()
			self.special_beasts_skill_tween = nil
		end

		self.special_beasts_skill_tween = DG.Tweening.DOTween.Sequence()
		self.special_beasts_skill_tween:SetEase(DG.Tweening.Ease.OutQuad)
		self.special_beasts_skill_tween:InsertCallback(0.1, function ()
			self.node_list.special_beasts_skill_effect:CustomSetActive(true)
		end)
		self.special_beasts_skill_tween:Append(self.node_list.special_beasts_skill_img.rect:DOScale(Vector3(1, 1, 1), 0.2))
		self.special_beasts_skill_tween:Append(self.node_list.special_beasts_skill_img.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.3))
		self.special_beasts_skill_tween:AppendInterval(0.5)
		self.special_beasts_skill_tween:OnComplete(function()
			self.node_list.special_beasts_skill_effect_root:SetActive(false)
		end)
	end)
end

function MainUIView:CleanBeastSkillEffectCountDown()
	if CountDownManager.Instance:HasCountDown("beast_skill_effect_count_down") then
		CountDownManager.Instance:RemoveCountDown("beast_skill_effect_count_down")
	end
end

function MainUIView:UpdateBeastSkillEffectCountDown(elapse_time, total_time)
	if self.node_list and self.node_list.beasts_skill_effect_root then
		local value = 1 - elapse_time / total_time
		self.node_list.beasts_skill_effect_root.slider.value = value
	end
end

function MainUIView:CompleteSkillEffectCountDown()
	self.node_list.beasts_skill_effect_root.slider.value = 0
	self:FlushBeastBuffIcon(false)
end

function MainUIView:OnClickBeastsSkillEffectIcon()
    RuleTip.Instance:SetContent(Language.ContralBeasts.YinJiRuleContent, Language.ContralBeasts.YinJiRuleTitle)
end

-- 元素反应相关

-- 主界面御兽技能
function MainUIView:BeastsSkillAutoFlagChange()
	local beasts_auto_skill = SettingWGData.Instance:GetSettingData(SETTING_TYPE.AUTO_BEASTS_SKILL)

	self.node_list.flag_not_beast_auto_skill:CustomSetActive(not beasts_auto_skill)
	self.node_list.flag_beast_auto_skill:CustomSetActive(beasts_auto_skill)
end

function MainUIView:ChangeBeastAutoSkillFlag()
	local beasts_auto_skill = SettingWGData.Instance:GetSettingData(SETTING_TYPE.AUTO_BEASTS_SKILL)
	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.AUTO_BEASTS_SKILL, not beasts_auto_skill, true)
end

-- 主界面boss秒杀
function MainUIView:FlushSecKillBossPart()
	self:ClearSecKillBossSliderTween()
	local is_self_trigger_sec_skill = BossWGData.Instance:IsSelfTriggerSecSkill()
	local start_time, end_time = BossWGData.Instance:GetSecKillBossTimeData()
	local cur_server_time = TimeWGCtrl.Instance:GetServerTime()
	-- 做个容错开始时间减一，客户端获取的时间和服务端的时间会有零点几秒的误差
	local is_show = is_self_trigger_sec_skill and cur_server_time >= (start_time - 1) and cur_server_time < end_time
	self.node_list.sec_kill_boss_part:CustomSetActive(is_show)
	if is_show then
		local sec_kill_type = BossWGData.Instance:GetSecKillBossType()
		local other_cfg = BossWGData.Instance:GetMonsterShieldOtherCfg()
		local auto_kill_flag = BossKillEveryWGData.Instance:GetAutoKillBossFlag()

		local ani_value = 0
		local ani_time = end_time - start_time
		local is_auto_click = false					--子鹏的需求，条件满足则会自动斩杀，没有开关
		local is_auto_kill_boss = false             --自动秒杀
		if auto_kill_flag > 0 then
			is_auto_kill_boss = true
		elseif VipWGData.Instance:GetRoleVipLevel() >= other_cfg.open_auto_sec_kill_vip_level and sec_kill_type == MONSTER_SEC_KILL_TYPE.MONSTER_SEC_KILL_TYPE_ODDS then
			is_auto_click = true
			local remain_time = math.max((ani_time - other_cfg.auto_sec_kill_boss_delay_time), 1)
			ani_value = math.min((remain_time / ani_time) or 1)
			ani_time = math.min(other_cfg.auto_sec_kill_boss_delay_time, ani_time)
		end

		local effect_name = sec_kill_type == MONSTER_SEC_KILL_TYPE.MONSTER_SEC_KILL_TYPE_ODDS and "UI_zhansha" or "UI_miaosha"
		local effect_bundle, effect_asset = ResPath.GetEffect(effect_name)
		self.node_list.sec_kill_boss_effect:ChangeAsset(effect_bundle, effect_asset)

		local btn_bundle, btn_asset = ResPath.GetRawImagesPNG("a3_zjm_sec_kill_bg_" .. sec_kill_type)
		self.node_list.sec_kill_boss_btn.raw_image:LoadSprite(btn_bundle, btn_asset)

		local icon_bundle, icon_asset = ResPath.GetMainUIIcon("a3_zjm_sec_kill_icon_" .. sec_kill_type)
		self.node_list.sec_kill_boss_icon.image:LoadSprite(icon_bundle, icon_asset)

		self.node_list.sec_kill_boss_slider.image.fillAmount = 1
		self.sec_kill_boss_slider_tween = self.node_list.sec_kill_boss_slider.image:DOFillAmount(ani_value, ani_time):OnComplete(function ()
			self.node_list.sec_kill_boss_part:CustomSetActive(false)
			if is_auto_click then
				self:OnClickSecKillBossBtn()
			end
		end)

		if is_auto_kill_boss then
			self:OnClickSecKillBossBtn()
		end

		self.sec_kill_boss_slider_tween:SetEase(DG.Tweening.Ease.Linear)
	end
end

function MainUIView:CloseLoadingCallBack()
	self:ClearSecKillBossSliderTween()
	self.node_list.sec_kill_boss_part:CustomSetActive(false)
end

function MainUIView:OnClickSecKillBossBtn()
	local is_self_trigger_sec_skill = BossWGData.Instance:IsSelfTriggerSecSkill()
	local start_time, end_time = BossWGData.Instance:GetSecKillBossTimeData()
	local cur_server_time = TimeWGCtrl.Instance:GetServerTime()
	if is_self_trigger_sec_skill and cur_server_time >= (start_time - 1) and cur_server_time < end_time then
		RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_DO_MONSTER_SEC_KILL, BossWGData.Instance:GetSecKillBossObjId())
	end
end

function MainUIView:ClearSecKillBossSliderTween()
	if self.sec_kill_boss_slider_tween then
		self.sec_kill_boss_slider_tween:Kill()
		self.sec_kill_boss_slider_tween = nil
	end
end

function MainUIView:OnClickAngerSkillBtn()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local is_system_bianshen = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance
	local power_bianshen_flag = CultivationWGData.Instance:GetRoleBianshenFlag()
	local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
	local curr_nuqi = CultivationWGData.Instance:GetRoleCurrNuqi()
	local curr_slider_value = curr_nuqi / 100

	if curr_nuqi_type == -1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerSkillError)
		--FunOpen.Instance:OpenViewNameByCfg("DujieView#0#op=0", 59683)
		return
	end

	if curr_slider_value >= 1 then
		if (not is_system_bianshen) and power_bianshen_flag == 0 and curr_nuqi_type ~= -1 then
			-- 请求怒气变身
			local main_role = Scene.Instance:GetMainRole()
			if main_role ~= nil and (not main_role:IsDeleted()) then
				main_role:SetXiuWeiBianShenSeq(true)
				MountWGCtrl.Instance:SendMountGoonReq(0)
				CultivationWGCtrl.Instance:AngerBecome()
				self:RemoveXiuWeiSeqDelayTimer()
				self.clear_main_role_xiuweiseq_timer = GlobalTimerQuest:AddDelayTimer(function ()
					self:RemoveXiuWeiSeqDelayTimer()
					main_role:SetXiuWeiBianShenSeq(false)
				end, 3)
			end
			return
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerSkillCD)
		return
	end
end

--移除回调
function MainUIView:RemoveXiuWeiSeqDelayTimer()
    if self.clear_main_role_xiuweiseq_timer then
        GlobalTimerQuest:CancelQuest(self.clear_main_role_xiuweiseq_timer)
        self.clear_main_role_xiuweiseq_timer = nil
    end
end

