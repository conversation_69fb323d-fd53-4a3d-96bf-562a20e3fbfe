require("game/long_hun/long_hun_xiang_qian")
--require("game/long_hun/long_hun_he_cheng")
-- require("game/long_hun/long_hun_fen_jie")
-- require("game/long_hun/long_hun_dui_huan")

LongHunView = LongHunView or BaseClass(SafeBaseView)

function LongHunView:__init()
	self.is_safe_area_adapter = true
	self.view_style = ViewStyle.Full
	self:LoadConfig()
	self.default_index = TabIndex.long_hun_xiang_qian
	self:SetMaskBg(false, true)
	self.remind_tab = {
		{RemindName.LongHun_XiangQian},
		--{RemindName.LongHun_FenJie},
		--{RemindName.LongHun_DuiHuan},
		--{RemindName.LongHun_HeCheng},
	}
	--self.tab_sub = {Language.LongHunView.TabGrop11}
end

function LongHunView:__delete()

	--self:DeleteFenJie()
	--self:DeleteDuiHuan()
	--self:DeleteHeCheng()
	self:DeleteXiangQian()
end

function LongHunView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	-- if self.money_bar then
	-- 	self.money_bar:DeleteMe()
	-- 	self.money_bar = nil
	-- end

	--self:ReleaseFenJie()
	--self:ReleaseDuiHuan()
	--self:ReleaseHeCheng()
	self:ReleaseXiangQian()
end

-- 加载配置
function LongHunView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
	local bundle_name = "uis/view/long_hun_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	local longhun_bundle_name = "uis/view/long_hun_ui_prefab"
	self:AddViewResource(0, longhun_bundle_name, "layout_longhun_xiangqian")
	self:AddViewResource(TabIndex.long_hun_xiang_qian,bundle_name,"layout_longhun_xiangqian")
	self:AddViewResource(0, common_bundle_name, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function LongHunView:LoadCallBack(index, loaded_times)
	if not self.tabbar then
  		self.tabbar = Tabbar.New(self.node_list)
  		self.tabbar:SetVerTabbarIconStr("longhun_view")
  		self.tabbar:Init(Language.LongHunView.TabGrop, nil,nil,nil,self.remind_tab)
  		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
  	end

  	-- if not self.money_bar then
	-- 	self.money_bar = MoneyBar.New()
	-- 	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	-- 	local show_params = {
    --     show_gold = true, show_bind_gold = true,
    --     show_coin = true, show_silver_ticket = true,
    --     }
    --     self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	-- 	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	-- end

  	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.LongHunView, self.tabbar)	
	self.node_list.title_view_name.text.text = Language.LongHunView.NewViewName
end

function LongHunView:ShowIndexCallBack(index)
	if index == TabIndex.long_hun_xiang_qian then
		self:InitXiangQian()
		self:FlushXiangQian()
	-- elseif index == TabIndex.long_hun_fen_jie then
	-- 	self:InitFenJie()
	-- 	self:FlushFenJie("all")
	-- elseif index == TabIndex.long_hun_dui_huan then
	-- 	self:InitDuiHuan()
	-- elseif index == TabIndex.long_hun_he_cheng then
	-- 	self:InitHeCheng()
	-- 	self:FlushHeCheng()
	end
end

function LongHunView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
            if index == TabIndex.long_hun_xiang_qian then
                self:FlushXiangQian()
            end
		-- elseif k == "long_hun_fen_jie" then
		-- 	self:FlushFenJie()
		elseif k == "long_hun_xiang_qian" then
			self:FlushXiangQian()
		elseif k == "fenjie_red" then
			self:FlushFenjieRed()
		-- elseif k == "long_hun_dui_huan" then
		-- 	self:FlushDuiHuan()
		-- elseif k == "long_hun_he_cheng" then
		-- 	self:FlushHeCheng()
		end
	end
end

function LongHunView:FLushCurIndex()
	if self.show_index == TabIndex.long_hun_xiang_qian then
		self:FlushXiangQian()
	-- elseif self.show_index == TabIndex.long_hun_fen_jie then
	-- 	self:FlushFenJie("all")
	-- elseif self.show_index == TabIndex.long_hun_dui_huan then
	-- 	self:FlushDuiHuan()
	-- elseif self.show_index == TabIndex.long_hun_he_cheng then
	-- 	self:FlushHeCheng()
	end
end