require("game/gudaojizhanfuben/gudao_jizhan_follow_view")
require("game/gudaojizhanfuben/gudao_jizhan_follow_wg_data")
require("game/gudaojizhanfuben/gudao_jizhan_result_view")
require("game/gudaojizhanfuben/gudao_jizhan_chaun_wen")


GuDaoFuBenWGCtrl = GuDaoFuBenWGCtrl or BaseClass(BaseWGCtrl)

function GuDaoFuBenWGCtrl:__init()
	if GuDaoFuBenWGCtrl.Instance then
		print_error("GuDaoFuBenWGCtrl:Attempt to create singleton twice!")
	end
	GuDaoFuBenWGCtrl.Instance = self

	self.gudao_scene_follow_view = GuDaoSceneFollowView.New(GuideModuleName.GuDaoSceneFollowView)
	self.data = GuDaoFuBenWGData.New()
	self.gudao_result_view = GuDaoJIZhanResultView.New()
	self.gudao_chuanwen_view = GuDaoScrollView.New()
	--掉落物拾取回调
	--self.pick_item_callback = GlobalEventSystem:Bind(OtherEventType.PICK_ITEM_EVENT, BindTool.Bind(self.OnPickItemEvent,self))
	--self.touch_callback = GlobalEventSystem:Bind(LayerEventType.TOUCH_MOVED, BindTool.Bind(self.SetAutoWaBaoFlag,self,false))
	self:RegisterAllProtocals()
end

function GuDaoFuBenWGCtrl:__delete()
	self.gudao_scene_follow_view:DeleteMe()

	if self.data then
		self.data:DeleteMe()
		self.data = nil 
	end

	if self.gudao_result_view then
		self.gudao_result_view:DeleteMe()
		self.gudao_result_view = nil
	end

	if self.gudao_chuanwen_view then
		self.gudao_chuanwen_view:DeleteMe()
		self.gudao_chuanwen_view = nil
	end

	GuDaoFuBenWGCtrl.Instance = nil
end

-- 注册协议
function GuDaoFuBenWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSRoleEnterIsletFierceBattle)
	self:RegisterProtocol(SCIsletFierceBattleHurtRankInfo,"OnSCIsletFierceBattleHurtRankInfo")
	self:RegisterProtocol(SCIsletFierceBattleRoleInfo,"OnSCIsletFierceBattleRoleInfo")
	self:RegisterProtocol(SCIsletFierceBattleNews,"OnSCIsletFierceBattleNews")
	self:RegisterProtocol(SCIsletFierceBattleRoleGather,"OnSCIsletFierceBattleRoleGather")
end

function GuDaoFuBenWGCtrl:SendEnterGuDaoFB()
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleEnterIsletFierceBattle)
	protocol:EncodeAndSend()
end

function GuDaoFuBenWGCtrl:OnSCIsletFierceBattleHurtRankInfo(protocol)
	self.data:OnSCIsletFierceBattleHurtRankInfo(protocol)
	if self.gudao_scene_follow_view:IsOpen() then
		self.gudao_scene_follow_view:Flush()
	end
end

function GuDaoFuBenWGCtrl:OnSCIsletFierceBattleRoleInfo(protocol)
	self.data:OnSCIsletFierceBattleRoleInfo(protocol)
	self.result_have_come = true
	if self.have_out_fb then
		self:OpenGuDaoResultView()
	end
end

function GuDaoFuBenWGCtrl:OpenGuDaoFBFollowView()
	if self.gudao_scene_follow_view then
		self.gudao_scene_follow_view:Open()
	end
end

function GuDaoFuBenWGCtrl:OpenGuDaoResultView()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Common then
		self.result_have_come = false
		self.have_out_fb = false
		if self.gudao_result_view then
			self.gudao_result_view:Open()
		end
	else
		self.result_have_come = true
	end
end

function GuDaoFuBenWGCtrl:TryOpenGuDaoResultView()
	if self.result_have_come then
		self:OpenGuDaoResultView()
	else
		self.have_out_fb = true
	end
end

function GuDaoFuBenWGCtrl:OnSCIsletFierceBattleNews(protocol)
	
		local msg_info = ChatWGData.CreateMsgInfo()
		msg_info.from_uid = 0
		msg_info.username = ""
		msg_info.sex = 0
		msg_info.camp = 0
		msg_info.prof = 0
		msg_info.authority_type = 0
		msg_info.tuhaojin_color = 0
		msg_info.level = 0
		msg_info.vip_level = 0
		msg_info.msg_reason = CHAT_MSG_RESSON.NORMAL
		msg_info.channel_type = CHANNEL_TYPE.CHUAN_WEN
		msg_info.content =protocol.str
		msg_info.send_time_str = protocol.send_time
		msg_info.is_add_team = false
		msg_info.title_text = Language.ChannelColor2[10]
		ChatWGCtrl.Instance:AddChannelMsg(msg_info, true)
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)
		--msg_info2 = msg_info

		--msg_info2.channel_type = CHANNEL_TYPE.MAINUI
		--ChatWGCtrl.Instance:AddChannelMsg(msg_info2, true)
	

	if protocol.is_piaozi == 1 then
		if self.gudao_chuanwen_view then
			self.gudao_chuanwen_view:SetNotice(protocol.str)
		end
	end
end

function GuDaoFuBenWGCtrl:OnSCIsletFierceBattleRoleGather(protocol)
	self.data:OnSCIsletFierceBattleRoleGather(protocol)
	if self.gudao_scene_follow_view and self.gudao_scene_follow_view:IsOpen() then
		self.gudao_scene_follow_view:Flush()
	end
end