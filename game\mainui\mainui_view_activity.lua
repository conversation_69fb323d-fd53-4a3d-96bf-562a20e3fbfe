-- 右上角固定按钮
--[[
    [”button的名字“] = {
        [1] = 模块名, [2] = 要跳转的标签值（没有子标签的可以不填）,[3] = 功能开启显示（仅用于多入口时）
        ["effect_asset"] = "",  ["effect_type"] = MAIN_TOP_BTN_EFFECT_TYPE.xxx, ["effect_pos_y"] = 0,
        ["is_shake"] = false,
    }

	非按钮列表内的不要往下面加
]]
local SundryViewNameButton = {
	BtnTreasurehuntView = {GuideModuleName.TreasureHunt, ["remind"] = RemindName.TreasureHunt, ["res_name"] = "a3_zjm_icon_xb"},	-- 寻宝面板
	BtnFuBen = {GuideModuleName.FuBenPanel, ["remind"] = RemindName.FuBenPanel, ["res_name"] = "a3_zjm_icon_fb"},					-- 副本
    BtnBiZuoView = {GuideModuleName.BiZuo, "bizuo_bizuo", ["remind"] = RemindName.BiZuo, ["res_name"] = "a3_zjm_icon_rc"},			-- 日常主界面
	BtnProfessWallView = {GuideModuleName.ProfessWallView, ["res_name"] = "a3_zjm_icon_bbq"},										-- 表白墙
	BtnSeventDayView = {GuideModuleName.SevenDay, ["remind"] = RemindName.WelfareSevenServer},										-- 七天登录
	-- BtnWorldBossView = {GuideModuleName.Boss, ["remind"] = RemindName.Boss, ["res_name"] = "a3_zjm_tb_mw"},							-- BOSS面板
    BtnWorldServerView = {GuideModuleName.WorldServer, ["remind"] = RemindName.WorldServer, ["res_name"] = "a3_zjm_tb_sjf"},        -- 世界服
	BtnQiFuView = {GuideModuleName.QIFU, ["remind"] = RemindName.QiFu, ["res_name"] = "a3_zjm_icon_qf"},							-- 祈福		
	BtnWelfareView = {GuideModuleName.Welfare, ["remind"] = RemindName.Welfare, ["res_name"] = "a3_zjm_icon_fl"},                   -- 福利
	BtnShopView = {GuideModuleName.Market, "shop_limit", ["remind"] = RemindName.Shop, ["res_name"] = "a3_zjm_icon_shangcheng"}, 			-- 商店
	BtnFashionExchangeShopView = {GuideModuleName.FashionExchangeShopView, ["remind"] = RemindName.FashionExchangeShopView, ["res_name"] = "a3_zjm_icon_xjyl"},-- 时装兑换商店
    -- BtnCountryMap = {GuideModuleName.CountryMapMapView, ["remind"] = RemindName.CountryMapActShow},                                                 -- 版图
    BtnLongYunZhanLingTask = {GuideModuleName.LongYunZhanLingTaskView, ["remind"] = RemindName.LongYunZhanLingTask, ["res_name"] = "a3_btn_lyxq"},                                -- 龙云战令任务界面
	BtnSystemPreview = {GuideModuleName.SystemForceLongShenView, ["remind"] = RemindName.SystemForceLongShen, ["res_name"] = "a3_zjm_btn_zsjl"},										-- 龙神降临预告
	BtnMarryNotice = {GuideModuleName.MarryNotice, ["remind"] = RemindName.MarryNotice, ["res_name"] = "a3_zjm_icon_jyly"}, 															-- 结婚预告
	BtnAuctionTips = {GuideModuleName.AuctionTipsView, ["res_name"] = "a3_zjm_icon_xspm"}, 																							-- 拍卖提示弹窗
    BtnZhanLing = {GuideModuleName.ZhanLingView, ["remind"] = RemindName.ZhanLing, ["res_name"] = "a3_zjm_icon_zl"},                                                                 -- 战令
    BtnSubPackage = {GuideModuleName.SubPackageView, ["remind"] = RemindName.SubPackage_Download},                                                  -- 分包下载
	-- BtnShenJiNotice = {GuideModuleName.ShenJiXianShiView, ["remind"] = RemindName.ShenJiNotice,
	-- 					["effect_asset"] = Ui_Effect.UI_tubiao1, ["effect_pos_y"] = 4},																-- 仙界装备预告
    BtnSiXiangCallView = {GuideModuleName.SiXiangCallView, ["remind"] = RemindName.SiXiangCall_SX,													-- 四象召唤
                        ["effect_asset"] = Ui_Effect.UI_tubiao1, ["effect_type"] = MAIN_TOP_BTN_EFFECT_TYPE.SOMETIME,
						["effect_pos_y"] = 4},
	-- BtnEquipTarget = {GuideModuleName.Bag, "rolebag_Target", FunName.MainEquipTargetView, ["remind"] = RemindName.EquipTargetEnter},				-- 装备目标
	BtnTianShenLingHeDraw = {GuideModuleName.TianShenLingHeDrawView, ["remind"] = RemindName.TianShenLingHeDraw, ["res_name"] = "a2_zjm_icon_lhzh"},									-- 天神灵核抽奖
	BtnShenJiTianCiView = {GuideModuleName.ShenJiTianCiView, ["remind"] = RemindName.SiXiangCall_XQZL, ["res_name"] = "a2_zjm_icon_sjtc"},											    -- 神技天赐
	BtnHongHuangGoodCoremonyView = {GuideModuleName.HongHuangGoodCoremonyView, ["remind"] = RemindName.HongHuangGoodCoremony, ["res_name"] = "a3_zjm_icon_cqhl"},						-- 洪荒豪礼
	BtnHongHuangClassicView = {GuideModuleName.HongHuangClassicView, ["remind"] = RemindName.HongHuangClassic, ["res_name"] = "a3_zjm_icon_cqtd"},					                    -- 洪荒特典
	BtnChaoticVipspecial = {GuideModuleName.ChaoticVipSpecialView, ["remind"] = RemindName.ChaoticVipSpecial, ["res_name"] = "a3_zjm_icon_viphl"},					                    -- 洪荒特典
	BtnChaoticPurchase = {GuideModuleName.ChaoticPurchaseView, ["res_name"] = "a3_zjm_icon_cqzg"},					                                                                    -- 洪荒直购
	BtnFiveElementsTreasury = {GuideModuleName.FiveElementsTreasuryView, ["remind"] = RemindName.FiveElement_Treasury, ["res_name"] = "a2_zjm_icon_wxbk"},								-- 五行宝库
	-- BtnTianShenLiLian = {GuideModuleName.TianShenLiLianView, ["remind"] = RemindName.TianShenLiLian},											-- 历练
 	BtnDownLoadWeb = {GuideModuleName.DownLoadWebView, ["remind"] = RemindName.DownLoadWeb, ["res_name"] = "a3_zjm_icon_xzyl"},														-- 下载有礼
 	
	BtnHolyHeavenlyDomain = {GuideModuleName.HolyHeavenlyDomainView, ["remind"] = RemindName.HolyHeavenlyDomain},                                   -- 战令
	BtnHappyFover = {GuideModuleName.BoundlessJoyView, ["remind"] = RemindName.BoundlessJoy, ["res_name"] = "a3_zjm_icon_clwy"},														-- 长乐未央
	BtnEverythingUnderTheSun = {GuideModuleName.EverythingUnderTheSun, ["res_name"] = "a2_zjm_icon_slwx"},																				-- 森罗万象
	BtnPositionalWarfare = {GuideModuleName.PositionalWarfareView, ["remind"] = RemindName.PositionalWarfare, ["res_name"] = "a3_zjm_icon_zdz"},										-- 阵地战
	BtnBeastsContract = {GuideModuleName.ControlBeastsPrizeDrawWGView, ["remind"] = RemindName.BeastsPrizeDraw, ["res_name"] = "a3_zjm_icon_hsqy"},									-- 幻兽奇遇
	BtnLandWarFb =  {GuideModuleName.LandWarFbPersonView, ["remind"] = RemindName.LandWarFbPerson, ["res_name"] = "a3_zjm_icon_zdz"},													-- 阵地战个人副本
	BtnFeiXianShu =  {GuideModuleName.XiuXianShiLian, ["remind"] = RemindName.XiuXianShiLian, ["res_name"] = "a3_zjm_icon_xtl"},														-- 飞仙书
	BtnPrivilegeCollection = {GuideModuleName.PrivilegeCollectionView, ["remind"] = RemindName.PrivilegeCollectionView, ["res_name"] = "a3_zjm_btn_tq"},							-- 特权合集
	BtnHalfPurchase = {GuideModuleName.HalfPurchaseView, ["res_name"] = "a3_btn_wzcz"},																							-- 五折直购卡

	BtnLotteryCollect = {
		["is_collect_btn"] = true, 
		defult_icon = "a3_zjm_icon_xtxb", 
		collect_panel_name = "lottery_collect_panel", 
		["remind"] = RemindName.LotteryCollect, 
		open_fun = {
			{view_name = GuideModuleName.SiXiangCallView, act_icon = "a2_zjm_icon_hbhl", btn_name = "BtnSiXiangCallView"}, 
			{view_name = GuideModuleName.TianShenLingHeDrawView, act_icon = "a2_zjm_icon_lhzh", btn_name = "BtnTianShenLingHeDraw"}, 
			{view_name = GuideModuleName.FiveElementsTreasuryView, act_icon = "a2_zjm_icon_wxbk", btn_name = "BtnFiveElementsTreasury"}, 
			{view_name = GuideModuleName.ShenJiTianCiView, act_icon = "a2_zjm_icon_sjtc", btn_name = "BtnShenJiTianCiView"},
		}
	},

	BtnHongHuangCollect = {
		["is_collect_btn"] = true, 
		defult_icon = "a3_zjm_icon_cqtd", 
		collect_panel_name = "honghuang_collect_panel", 
		["remind"] = RemindName.WelkinGiftPack, 
		open_fun = {
			{view_name = GuideModuleName.HongHuangClassicView, act_icon = "a3_zjm_icon_cqtd", btn_name = "BtnHongHuangClassicView"}, 
			{view_name = GuideModuleName.ChaoticVipSpecialView, act_icon = "a3_zjm_icon_viphl", btn_name = "BtnChaoticVipspecial"}, 
			{view_name = GuideModuleName.HongHuangGoodCoremonyView, act_icon = "a3_zjm_icon_cqhl", btn_name = "BtnHongHuangGoodCoremonyView"}, 
	}},

	BtnQiFuCollect = {
		["is_collect_btn"] = true, 
		defult_icon = "a3_zjm_icon_fl", 
		collect_panel_name = "qifu_collect_panel", 
		["remind"] = RemindName.QiFuCollect, 
		open_fun = {
			{view_name = GuideModuleName.QIFU, act_icon = "a3_zjm_icon_qf", btn_name = "BtnQiFuView"}, 
			{view_name = GuideModuleName.Welfare, act_icon = "a3_zjm_icon_fl", btn_name = "BtnWelfareView"},
			{view_name = GuideModuleName.SevenDay, act_icon = "a3_zjm_icon_atdl", btn_name = "BtnSeventDayView"}
	}},
	BtnGameAssistant = {GuideModuleName.GameAssistant, ["remind"] = RemindName.GameAssistant, ["res_name"] = "a3_zjm_icon_bfzy"}
}

-- 右上角第二排固定按钮排序信息
-- Top2BtnSortInfoList =
-- {
-- 	BtnShenJiNotice = {sort_index = 20},		-- 仙器降临
-- }

-- 右上角第三排固定按钮排序信息（排在某些活动的后面）
-- Top3BtnSortInfoList =
-- {
-- 	BtnMarryNotice = {
-- 		left_act_id = 32,
-- 		right_act_id = 32,
-- 	},
-- }

-- ButtonGroup4特殊排序规则
local BUTTON_GROUP_BTN_SORT_TAB_4 = {
	ButtonVIP           = {sort = 10}, --VIP特权
	btn_recharge_volume = {sort = 20}, --充值卷
	btn_privilege_collection = {sort = 70}, --特权合集
	btn_billion_subsidy = {sort = 50}, -- 百亿补贴 
}

local BUTTON_GROUP_ACT_SORT_TAB_4 = {
	[ACTIVITY_TYPE.FIRST_CHONGZHI] = {sort = 60}, --首充   20000
	[ACTIVITY_TYPE.MEIRI_LEICHONG] = {sort = 30}, --每日累充 20001
	[ACTIVITY_TYPE.GOD_OF_WEALTH] = {sort = 40},  --招财进宝 20052
	[ACTIVITY_TYPE.YanYuGe] = {sort = 50} -- 烟雨阁 20064
}

-- 【不需要收起来的活动类型】
local TopNeedShowActBtn =
{
	[ACTIVITY_TYPE.FIRST_CHONGZHI] = true, 							-- 首充
	[ACTIVITY_TYPE.MEIRI_LEICHONG] = true, 							-- 每日累充
	[ACTIVITY_TYPE.ZEROBUY] = true, 								-- 零元购
	[ACTIVITY_TYPE.FengShenBang] = true, 							-- 封神榜
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY] = true, 			-- 超值必买
	[ACTIVITY_TYPE.OPENSERVER_COMPETITION] = true, 					-- 开服比拼
	[ACTIVITY_TYPE.OPEN_SERVER] = true, 							-- 开服活动
	[ACTIVITY_TYPE.KF_ACTIVITY] = true, 							-- 开服冲榜
	[ACTIVITY_TYPE.GOD_TIANSHENROAD] = true, 						-- 天神之路
	[ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN] = true, 					-- 全民备战
	[ACTIVITY_TYPE.GOD_XUNBAO] = true, 								-- 丰收夺宝
	[ACTIVITY_TYPE.OPERATIONACTIVITY] = true, 						-- 动态运营活动（第一个按钮）
    [ACTIVITY_TYPE.OPERATIONACTIVITY_TWO] = true, 					-- 动态运营活动（第二个按钮）
    [ACTIVITY_TYPE.HEFU_ACTIVITY] = true, 					        -- 合服活动
    [ACTIVITY_TYPE.REBATE_ACTIVITY] = true, 					    -- 返利活动
    [ACTIVITY_TYPE.FESTIVA_ACTIVITY] = true, 					    -- 节日活动
    [ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG] = true,	    			-- 仙器解封
    [ACTIVITY_TYPE.ACT_PIERRE_DIRECT_PURCHASE] = true,	    		-- 臻品直购
    [ACTIVITY_TYPE.XIUZHEN_ROAD] = true,							-- 大圣归来
    [ACTIVITY_TYPE.SHENBINGAWAKEN_ACTIVITY] = true,					-- 仙器觉醒
	[ACTIVITY_TYPE.CROSS_LINGYUPOOL_ACTIVE] = true,					-- 超级锦鲤活动
}

-- 【需要收起来的功能按钮】
TopNeedHideBtn =
{
	BtnFuLiView = true,
	BtnQiFuView = true,
	BtnPaiHangView = true,
    BtnZhanLing = true,
    BtnSiXiangCallView = true,
}

-- 【不需要收起来的功能按钮】
TopNotNeedHideBtn =
{
	BtnBiZuoView = true,
}

-- 基于TopNeedHideBtn
-- 在一定条件下 需要展示
local TopNeedHideAndSpecialShowBtn =
{
}

-- 【气泡图标列表】
local MainUiBubbleView = {
	--[[BtnFuLiView = {
		act_list = {
			[1] = {activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG, remind_name = RemindName.ZhouYi_YunCheng}
		},
		function_list = {
			[1] = {function_name = GuideModuleName.Welfare, remind_name = RemindName.Welfare, bg_res = "jy_di2", icon_res = "btn_fulii", name_res = "btn_fulii_name", mainui_key = "BtnFuLiView"},
			[2] = {function_name = GuideModuleName.SevenDay, remind_name = RemindName.WelfareSevenServe, bg_res = "jy_di2", icon_res = "btn_fulii", name_res = "btn_fulii_name", mainui_key = "BtnSeventDayView"},
		},
	},]]
}

-- 气泡展示图标限制 - 活动
local MainUiLimitShowAct = {
	-- [ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG] = true,
}

-- 气泡展示图标限制 - 功能
local MainUiLimitShowFunction = {
	-- ["BtnSeventDayView"] = true,
	-- ["BtnFuLiView"] = true,
}

-- 主界面顶上展开按钮 忽略红点
MainUITopShirnkIgnoreRemind = {
	[RemindName.Boss] = true,
	[RemindName.WorldServer] = true,
}

-- 主界面顶上展开按钮 忽略功能开启显隐
local MainUITopShirnkIgnoreFunOpenHide = {
	-- ["BtnWorldBossView"] = true,
	-- ["BtnWorldServerView"] = true,
}


local MOVETWEEN_COUNT = 0 		-- 正在播放动画的个数
local BTNWIDTH = 96		 		-- 按钮宽度
local BTNHIGHT = 96 			-- 按钮高度
local SORT_DIVISOR = 10 		-- 按钮排序因子
local BUTTON_GROUP1 = 1         -- 组1
local BUTTON_GROUP2 = 2 		-- 组2
local BUTTON_GROUP3 = 3 		-- 组3
local BUTTON_GROUP4 = 4 		-- 组4
local BUTTON_GROUP5 = 5 		-- 组5
local FIRST_POS = 1050			-- 第一行动画移动的坐标参照点
local SECOND_POS = 1050			-- 第二行动画移动的坐标参照点
local THIRD_POS = 1050			-- 第三行动画移动的坐标参照点



function MainUIView:GetSundryButton()
	return SundryViewNameButton
end

function MainUIView:GetOneSundryButton(key)
	return SundryViewNameButton[key]
end

function MainUIView:GetOneSundryNode(key)
	return (self.node_list or {})[key]
end

function MainUIView:__initActivity()
	self.active_load_time = 0
	self.act_lvlimit_icon_list = {}					-- 等级限制暂不开放的活动图标
	self.activity_button_list = {}
	self.mian_top_btn_effect_list = {}
	self.merge_function_visib = {}
	self.activity_btn_list_1 = {}					-- 第一组按钮排序
	self.activity_btn_list_2 = {}					-- 第二组仅活动按钮排序
	self.activity_btn_list_3 = {} 					-- 第三组按钮排序
	self.activity_btn_list_4 = {} 					-- 第四组按钮排序
	self.activity_btn_list_5 = {} 					-- 第五组按钮排序

	self.dragon_trial_act_info = nil					-- 五气朝元按钮由任务限制，若有其他的也这样处理则在表里加一套任务控制的逻辑

	self.chat_activity_btn_list = {}                -- 聊天栏面板得活动按钮

	self.activity_btn_wait_flush_list = {}

		--随机活动按钮
	self.rand_open_icon = {}						-- 开启中的版本活动（在版本活动面板中显示的）
	self.rand_open_icon[RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL] = {}	-- 正常随机活动
	self.rand_open_icon[RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_VERSION] = {}	-- 版本随机活动

	self.act_remind = {}
	self.act_remindRecord = {} 						-- 动态按钮红点记录

	-- 第二排按钮中的固定按钮的排序
	-- self.top_const_btn_sort_list_2 = {}
	-- for k,v in pairs(Top2BtnSortInfoList) do
	-- 	if v.left_act_id and v.right_act_id then
	-- 		local left_cfg = ActivityWGData.Instance:GetActivityCfgByType(v.left_act_id)
	-- 		local right_cfg = ActivityWGData.Instance:GetActivityCfgByType(v.right_act_id)
	-- 		local left_sort = left_cfg and left_cfg.sort or 2
	-- 		local right_sort = right_cfg and right_cfg.sort or 2
	-- 		self.top_const_btn_sort_list_2[k] = (left_sort + right_sort) / 2
	-- 	elseif v.sort_index then
	-- 		self.top_const_btn_sort_list_2[k] = v.sort_index
	-- 	end
	-- end

	-- 第三排按钮中的固定按钮的排序
	-- self.top_const_btn_sort_list_3 = {}
	-- for k,v in pairs(Top3BtnSortInfoList) do
	-- 	local left_cfg = ActivityWGData.Instance:GetActivityCfgByType(v.left_act_id)
	-- 	local right_cfg = ActivityWGData.Instance:GetActivityCfgByType(v.right_act_id)
	-- 	local left_sort = left_cfg and left_cfg.sort or 3
	-- 	local right_sort = right_cfg and right_cfg.sort or 3
	-- 	self.top_const_btn_sort_list_3[k] = (left_sort + right_sort) / 2
	-- end
	-- 第五排按钮中的固定按钮的排序
	-- self.top_const_btn_sort_list_5 = {}

	self.tween_list = {}
	self.whole_tween_list = {}
	self.special_interval = 0
end

function MainUIView:ActivityLoadCallBack()
	self:SetMoveDisIson(true)			-- 右上角按钮是否展开
	local icon_node_list  = {}			-- 注册功能开启的node_list
	local fun_open_list = {}			-- 注册功能开启的模块名
	local Icon
	local Text
	local Time
	for k,v in pairs(SundryViewNameButton) do
		local node_list = self.node_list[k]
		if node_list == nil then
			print_error("节点不存在：", k)
		else
			-- local cfg = DailyWGData.Instance:GetBtnSortCfgByBtnName(k)
			-- if cfg and cfg.act_pos_id then
			-- 	local parent = self.node_list["ButtonGroup" .. cfg.act_pos_id]
			-- 	if nil == parent then
			-- 		return
			-- 	end

			-- 	node_list.transform:SetParent(parent.transform, false)
			-- end

			self:SetMainTopBtnObj(k, node_list)
			node_list.remind = node_list.transform:FindByName("RedPoint")
			node_list.remind2 = node_list.transform:FindByName("RedPoint2")
			Icon = node_list.transform:FindByName("Icon")
			Text = node_list.transform:FindByName("Text")
			Time = node_list.transform:FindByName("time")
			node_list.Effect = node_list.transform:FindByName("effect")
			if Icon then
				node_list.Icon = U3DObject(Icon, nil, self)
			end

			if Text then
				node_list.Text = U3DObject(Text, nil, self)
			end

			if Time then
				node_list.Time = U3DObject(Time, nil, self)
			end

			-- 忽略功能开启显隐
			if not MainUITopShirnkIgnoreFunOpenHide[k] then
				icon_node_list[k] = node_list
			end

			fun_open_list[k] = v

			if node_list.button then
				node_list.button:AddClickListener(BindTool.Bind(self.OnClickOpenActivityView, self, v, k))
			end

			--红点
			if v["remind"] and not self.icon_remind_list_top[v["remind"]] then
				self.icon_remind_list_top[v["remind"]] = k
				RemindManager.Instance:Bind(self.remind_change, v["remind"])
			end

			--特效
			if v["effect_asset"] and v["effect_type"] ~= nil then
				local transform = self.node_list[k].transform
				local bundle_name, asset_name = ResPath.GetEffectUi(v["effect_asset"])
				EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, transform, 3600000, nil, nil, nil,
					function (obj)
						obj.transform:SetAsFirstSibling()
						local pos = obj.transform.localPosition
						obj.transform.localPosition = Vector3(pos.x, v.effect_pos_y or pos.y, pos.z)
						self.mian_top_btn_effect_list[k] = obj
					end
				)
			end

			--是否抖动
			if v["is_shake"] and node_list.Icon then
				node_list.Icon.animator:SetBool("is_shake", true)
			end
		end
	end

	self.is_activity_load = true
	self.active_load_time = Status.NowTime

	FunOpen.Instance:RegisterFunOpenUi(fun_open_list, icon_node_list)

	self.left_shrink_ison = self.node_list.ShrinkLeftButton.toggle.isOn
	XUI.AddClickEventListener(self.node_list.ShrinkButton, BindTool.Bind(self.OnShrinkBtnValueChange, self)) 			-- 右上箭头按钮
	XUI.AddClickEventListener(self.node_list.ShrinkLeftButton, BindTool.Bind(self.OnLeftShrinkBtnValueChange, self))	-- 左上箭头按钮
	XUI.AddClickEventListener(self.node_list.btn_level, BindTool.Bind(self.OnClickLevelFB, self)) 						-- 离开副本按钮
	XUI.AddClickEventListener(self.node_list.btn_inspire, BindTool.Bind(self.OnClickOpenGuWuView, self))				-- 鼓舞按钮
    XUI.AddClickEventListener( self.node_list.real_btn_strategy, BindTool.Bind(self.OnClickStrategy, self)) 			-- 规则按钮
    XUI.AddClickEventListener( self.node_list.btn_kfonevone, BindTool.Bind(self.OnClickKfPVP, self))					-- 跨服1v1
	XUI.AddClickEventListener( self.node_list.btn_func_box, BindTool.Bind(self.OnClickFuncBox, self))					-- 功能盒子

    -- XUI.AddClickEventListener(self.node_list["BtnLevelGift"], BindTool.Bind(self.OnClickLevelGiftBtn, self))
	XUI.AddClickEventListener(self.node_list["lottery_collect_close"], BindTool.Bind(self.OnClicklotteryClose, self))
	XUI.AddClickEventListener(self.node_list["honghuang_collect_close"], BindTool.Bind(self.OnClicklotteryClose, self))
	XUI.AddClickEventListener(self.node_list["qifu_collect_close"], BindTool.Bind(self.OnClicklotteryClose, self))
	XUI.AddClickEventListener(self.node_list["BtnRedPacketRain"], BindTool.Bind(self.OnClickRedPacketRainBtn, self))		--红包雨.
	XUI.AddClickEventListener(self.node_list["btn_level_gift_tips"], BindTool.Bind(self.OnClickLevelGiftBtn, self))			--等级礼包.

	self.node_list.btn_task_todayact.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTodayActTog, self))

    -- 右上角按钮点击事件
	self.main_top_arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind(self.DoLevelBtnAni, self)) 		--离开按钮显示隐藏
	-- 副本鼓舞监听
	self.fb_guwu_change = GlobalEventSystem:Bind(OtherEventType.FB_GUWU_CHANGE, BindTool.Bind(self.OnGuWuChange, self))
	self.task_change = GlobalEventSystem:Bind(OtherEventType.TASK_CHANGE,BindTool.Bind(self.OnTaskChange, self))							-- 任务改变
	
	if self.ned_set_top_btn_tween then
		self.ned_set_top_btn_tween = false
		self:PlayTopButtonTween(self.top_btn_isOn or false)
	end

	-- 收集所有hor_layout_group
	self:InitLayoutGroupList()
	--右上角按钮
	self.btn_group_obj = {}
	for i = 1, 5 do
		-- if i > 3 then
		-- 	self.btn_group_obj[i] = self.node_list["ButtonGroup" .. i + 1].gameObject
		-- else
		-- 	self.btn_group_obj[i] = self.node_list["ButtonGroup" .. i].gameObject
		-- end

		self.btn_group_obj[i] = self.node_list["ButtonGroup" .. i].gameObject
	end

	self:UpdateBtnGroup()

	--第一次加载，如果isOn，进入倒计时关闭
	if self.node_list.ShrinkButton.toggle.isOn then
		SettingWGCtrl.Instance:SetOperatTimeValid()
	end

	local scene_type = Scene.Instance:GetSceneType()
	self:ActivitySceneLoadingQuite(scene_type, scene_type)

    -- self:FlushLevelGift()
    self:FlushCalendar()
	self:FlushLevelGiftWelfareUpgrade()
	self:FlushDuoBeiState()
	self:FlushHmGodNotice()
	--self:ShenJiNoticeBtnInit() 								-- 神机预告
	self:BaiLianZhaoHuanMainBtnInit() 						-- 百炼召唤入口
	self:LingHeDrawMainBtnInit()								--灵核抽奖
	self:ShenJiTianCiMainBtnInit()								--神技天赐
	self:FlushLongXINotice()
	self:SetLotteryCollectPaneStatus(false)
	self:BeastsContractBtnInit()								-- 幻兽抽奖
end

function MainUIView:ChangeRechargeVolumeFlyToPos()
	local t_width = UnityEngine.Screen.width
	local pos_data = self.node_list.btn_recharge_volume.rect.position
	local is_error_pos = pos_data.x < -t_width or pos_data.x > t_width

	if not is_error_pos then
		--获取充值券时充值券特效飞到的目标位置
		if self.node_list.btn_recharge_volume then
			self.end_eff_pos = self.node_list.btn_recharge_volume.rect.position
		end
	end
end

function MainUIView:Activity__ReleaseCallBack()
	if self.main_top_arrow_click_event then
		GlobalEventSystem:UnBind(self.main_top_arrow_click_event)
		self.main_top_arrow_click_event = nil
	end

	if self.fb_guwu_change then
		GlobalEventSystem:UnBind(self.fb_guwu_change)
		self.fb_guwu_change = nil
	end

	if self.task_change then
		GlobalEventSystem:UnBind(self.task_change)
		self.task_change = nil
	end

	self:RemoveLevelTimeCountDown()

	if self.do_level_btn_tween then
		self.do_level_btn_tween:Kill()
		self.do_level_btn_tween = nil
	end

	if self.do_level_btn_tween_child then
		self.do_level_btn_tween_child:Kill()
		self.do_level_btn_tween_child = nil
	end

	if self.tasklist_tween then
		self.tasklist_tween:Kill()
		self.tasklist_tween = nil
	end

    -- RemindManager.Instance:UnBind(self.calendar_remind_callback)
	-- self.calendar_remind_callback = nil

	for k,v in pairs(self.act_remind) do
		RemindManager.Instance:UnBind(v)
	end
	self.act_remind = {}

	if self.task_double_pop_alert then
		self.task_double_pop_alert:DeleteMe()
		self.task_double_pop_alert = nil
	end

	if self.gudao_leave_alert then
		self.gudao_leave_alert:DeleteMe()
		self.gudao_leave_alert = nil
	end

	if self.activity_button_list then
		for k,v in pairs(self.activity_button_list) do
			v:DeleteMe()
		end
	end
	self.activity_button_list = {}

	self:ClearMainTopBtnObj()
	--self:ShenJiNoticeBtnRelease()
	self:BaiLianZhaoHuanMainBtnRelease()
	self:LingHeDrawMainBtnRelease()
	self:ShenJiTianCiMainBtnRelease()
	self:BeastsContractBtnRelease()
	self:ReleaseActivityAggregationBtn()

	for i = 1, 5 do
		self["button_group_" .. i] = {}
	end
	self.btn_group_obj = {}


	self.activity_btn_wait_flush_list = {}
	self.activity_btn_list_1 = {}
	self.activity_btn_list_2 = {}
	self.activity_btn_list_3 = {}
	self.activity_btn_list_4 = {}
	self.activity_btn_list_5 = {}
	self.chat_activity_btn_list = {}
	self.tween_list = {}
	self.whole_tween_list = {}
	self.act_remindRecord = {}
	MOVETWEEN_COUNT = 0
	self.is_activity_load = false
	self.main_gift_item_id = nil

	self.dragon_trial_act_info = nil

	-- if self.today_act_refresh_event then
	-- 	GlobalTimerQuest:CancelQuest(self.today_act_refresh_event)
	-- 	self.today_act_refresh_event = nil
	-- end

	if CountDownManager.Instance:HasCountDown("FlushFuncBoxBtnInfo") then
		CountDownManager.Instance:RemoveCountDown("FlushFuncBoxBtnInfo")
	end
end

--------------------------
-- 【      初始化       】 --
--------------------------
function MainUIView:InitLayoutGroupList()
	local group_list = {}
	local hor_group = typeof(UnityEngine.UI.HorizontalLayoutGroup)
	local grid_layout_group = typeof(UnityEngine.UI.GridLayoutGroup)
	group_list[1] = self.node_list["ButtonGroup1"]:GetComponent(hor_group)
	-- group_list[2] = self.node_list["ButtonGroup2"]:GetComponent(hor_group)
	group_list[3] = self.node_list["ButtonGroup3"]:GetComponent(hor_group)
	-- group_list[4] = self.node_list["ButtonGroup4"]:GetComponent(grid_layout_group)
	-- group_list[5] = self.node_list["ButtonGroup5"]:GetComponent(hor_group)
	self.act_mainui_hor_group_list = group_list
end

--------------------------
-- 【    按钮创建显示    】 --
--------------------------
-- 获取右上展开按钮状态
function MainUIView:GetShrinkButtonToggleIsOn()
	if not self.is_activity_load then
		return false
	end

	return self.node_list.ShrinkButton.toggle.isOn
end

-- 判断这个主界面按钮能否收起
function MainUIView:GetTopBtnNeedHideBtn(key)
	local need_hide = true
	-- if TopNeedHideBtn[key] and TopNeedHideAndSpecialShowBtn[key] then

	-- elseif TopNeedHideBtn[key] then
	-- 	need_hide = true
	-- end

	if TopNotNeedHideBtn[key] then
		return false
	end

	return need_hide
end


-- 气泡展示图标限制 - 功能
function MainUIView:CheckIsMergeFunction(btn_name, visib)
	local is_limit = false
	if MainUiLimitShowFunction[btn_name] then
		if self.merge_function_visib[btn_name] ~= visib then
			self.merge_function_visib[btn_name] = visib
			if self.main_ui_bubble_view:IsOpen() then
				self.main_ui_bubble_view:Flush(0, "func", {mainui_key = btn_name, visible = visib})
			end
		end

		is_limit = true
	end

	return is_limit
end

function MainUIView:GetMergeFunctionVisib(btn_name)
	return self.merge_function_visib[btn_name]
end

-- 缓存上方按钮显示状态
function MainUIView:SaveMainTopBtnStatus(key, enable)
	if self.main_top_btn_enable_cache == nil then
		self.main_top_btn_enable_cache = {}
	end
	
	self.main_top_btn_enable_cache[key] = enable
end

--设置按钮显示隐藏
function MainUIView:SetMainTopBtnObjEnable(key, enable, no_anim, icon_str)
    -- print_error("设置按钮显示隐藏MainUIView:SetMainTopBtnObjEnable", key, enable, "MOVETWEEN_COUNT", MOVETWEEN_COUNT, no_anim)
    if self:CheckIsMergeFunction(key, enable) then
    	return
    end

	--如果正在播放动画
	if MOVETWEEN_COUNT > 0 then
		self:SaveMainTopBtnStatus(key, enable)
		return
	end

	local blocksRaycasts = false
	local btn = self:GetMainTopBtnObj(key)
    if btn then
		btn:SetActive(enable)
		if not no_anim then
			local move_dis_ison = self:GetMoveDisIson()
			if not move_dis_ison and self:GetTopBtnNeedHideBtn(key) and not self.is_move_top_fun then --半收起来状态下收起按钮判断
				btn.canvas_group.alpha = 0
				btn.rect.sizeDelta = u3dpool.vec2(0, BTNHIGHT)
			elseif self.is_move_top_fun and not move_dis_ison and enable then 	--全收起来状态下按钮收起判断
				btn.canvas_group.alpha = 0
				btn.rect.sizeDelta = u3dpool.vec2(0, BTNHIGHT)
			else
				btn.canvas_group.alpha = 1
				btn.rect.sizeDelta = u3dpool.vec2(BTNWIDTH, BTNHIGHT)
				blocksRaycasts = true
			end
		else
			btn.canvas_group.alpha = 1
			btn.rect.sizeDelta = u3dpool.vec2(BTNWIDTH, BTNHIGHT)
			blocksRaycasts = true
		end

		btn.canvas_group.blocksRaycasts = blocksRaycasts
		self:SetChildrenBlocksRaycasts(btn, blocksRaycasts)
		-- btn.canvas_group.interactable = blocksRaycasts

		if btn and btn.Icon and icon_str ~= nil then
			local bundle, asset = ResPath.GetMainUIIcon(icon_str)
			btn.Icon.image:LoadSprite(bundle, asset)
		end
	end

	-- self:FlushBtnGroupPos(2)
end

-- 设置左上展开按钮状态
function MainUIView:SetLeftBtnGroupShow(is_show)
	---[[ 进入副本左侧判断是否展开
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.Common then
		local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
		if fb_scene_cfg and fb_scene_cfg.is_show_zuocelan_icon == 1 then
			is_show = true
		end
	end
	--]]
	self.node_list.ShrinkLeftButton.toggle.isOn = is_show
end


-- 引导绑定事件 打开对应的面板
function MainUIView:OnClickOpenActivityView(data, btn_name)
	if data == nil or next(data) == nil then
		return
	end

	if data.targer_obj then
		self:SetFakeBtnHandle(data)
		return
	end

	local view_name = data[1]
	local tab_index = data[2]
	local is_collect_btn = data["is_collect_btn"]
	local collect_panel_name = data["collect_panel_name"]

	if view_name == GuideModuleName.Shop then --商城屏蔽问题
		tab_index = tab_index or 10
		local select_index = math.floor(tab_index)
		if not ShopWGData.ShowShopType[select_index] then
			for k,v in pairs(ShopWGData.ShowShopType) do
				if v then
					tab_index = k *10
					break
				end
			end
		end
	end

	if MainUiBubbleView[btn_name] ~= nil then
		local obj = self:GetMainTopBtnObj(btn_name)
		if obj ~= nil then
			local pos = self:GetViewBtnPos(obj)
			self.main_ui_bubble_view:SetData(MainUiBubbleView[btn_name], pos.x, pos.y)
		else
			self.main_ui_bubble_view:Close()
			FunOpen.Instance:OpenViewByName(view_name, tab_index)
		end
	else
        self.main_ui_bubble_view:Close()
        if view_name == GuideModuleName.Boss or view_name == GuideModuleName.WorldServer then
            BossWGCtrl.Instance:OpenCurViewIndex(view_name)
			self:SetLotteryCollectPaneStatus(false)
		elseif is_collect_btn then
			local is_only_one_open, aim_data = self:CheckCollectPanelIsOpenOneAct(data)

			if is_only_one_open then
				FunOpen.Instance:OpenViewByName(aim_data.view_name, aim_data.tab_index)
			else
				self:SetLotteryCollectPaneStatus(true, collect_panel_name)
			end
        else
			self:SetLotteryCollectPaneStatus(false)
            FunOpen.Instance:OpenViewByName(view_name, tab_index)
        end
	end
end

function MainUIView:SetFakeBtnHandle(data)
	if not data or not data.targer_obj then
		return
	end

	local str = self:GetFakeBtnStr(data, true)
	if not str then
		return
	end

	SysMsgWGCtrl.Instance:ErrorRemind(str)
end

function MainUIView:GetFakeBtnStr(data, is_click)
	if not data or not data.targer_obj then
		return
	end

	local is_open = FunOpen.Instance:GetFunIsOpened(data.targer_obj.view_name)
	if is_open and is_click then
		print_error("请检查配置，对应功能己开启：", data.targer_obj.view_name)
		return
	end

	local fun_cfg = FunOpen.Instance:GetFunByName(data.targer_obj.view_name)
	if not fun_cfg then return end

	local str = nil
	if is_click then
		if fun_cfg.trigger_type == GuideTriggerType.ClickUi then

		elseif fun_cfg.trigger_type == GuideTriggerType.LevelUp then	-- 等级限制
			local limit_lv = tonumber(fun_cfg.trigger_param) or 0
			str = string.format(Language.Common.FunOpenRoleLevelLimitClick, RoleWGData.GetLevelString(limit_lv))
		elseif fun_cfg.trigger_type ~= 0 then   						-- 任务限制

		end
	else
		str = FunOpen.Instance:GetFunUnOpenCommonTip(fun_cfg)
	end

	return str
end

function MainUIView:GetViewBtnPos(obj)
	if self.view_root_rect == nil or obj == nil then
		return Vector2(0, 0)
	end

    local parent_rect = self.view_root_rect
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, obj.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, u3dpool.vec2(0, 0))
    x = local_position_tbl.x
    y = local_position_tbl.y
    return Vector2(x, y)
end

--------------------------
-- 【     界面动画      】 --
--------------------------
-- 更新所有
function MainUIView:UpdateBtnGroup()
	self:UpdateBtnGroupInIndex(1)
	self:UpdateBtnGroupInIndex(2)
	self:UpdateBtnGroupInIndex(3)
	self:UpdateBtnGroupInIndex(4)

	self:UpdateBtnGroupInIndex(5)
	-- self:UpdateBtnGroupInIndex(6)
	-- self:FlushBtnGroupPos(2)
end

-- 更新每行
function MainUIView:UpdateBtnGroupInIndex(group_index)
	local go = group_index and self.btn_group_obj[group_index]
	if nil == go then
		return
	end

	local count = go.transform.childCount - 1
	local trans = go.transform
	local tab = {}
	local index = 1
	for j = count, 0, -1 do
		local obj = trans:GetChild(j)
		local name = obj.name
		if self.node_list[name] then
			tab[index] = self.node_list[name]
		else
			tab[index] = U3DObject(obj.gameObject)
		end
		
		index = index + 1
	end

	self["button_group_" .. group_index] = tab
	self:SortBtnList(group_index)
end

--播放右上箭头动画
function MainUIView:PlayTopButtonTween(enable, lock_time, ignore_lock_time)
    if CLIENT_DEBUG_LOG_STATE then
    	print_error("PlayTopButtonTween", enable, lock_time, ignore_lock_time)
    end

	if not enable and (FunctionGuide.Instance:GetIsGuide() or ViewManager.Instance:IsOpen(GuideModuleName.OpenFunFlyView)) then
		return false
	end

	if self.last_lock_time and self.last_lock_time > Status.NowTime and not ignore_lock_time then
		SettingWGCtrl.Instance:SetOperatTimeValid()
		return
	end

	if lock_time then
		self.last_lock_time = Status.NowTime + lock_time
	end

	if not self.is_activity_load then
		self.ned_set_top_btn_tween = true
		return
	end

	self.top_btn_isOn = enable
	if self.node_list.ShrinkButton.toggle.isOn ~= enable then
		self.node_list.ShrinkButton.toggle.isOn = enable
	end

	-- self:OnShrinkBtnValueChange(enable)
end

function MainUIView:PlayBtnGroupWholeAni(parent_root, whole_node, ison)
	if nil == parent_root or IsEmptyTable(whole_node) then
		return
	end

	local len = GetTableLen(whole_node)
	local time = 0.5
	local hight = 96
	local width = 96
	local cell_space = 5

	local index = 0
	local active_index_tab = {}
	for i = 1, len do
		local btn_root = parent_root.transform:GetChild(i - 1)

		if btn_root and btn_root.gameObject.activeSelf then
			local sibling_index = btn_root.transform:GetSiblingIndex()
			active_index_tab[sibling_index] = index
			index = index + 1
		end
	end

	for i = 1, len do
		local btn_root = whole_node[i]
		if btn_root and not IsNil(btn_root.gameObject) and btn_root.gameObject.activeSelf then
			local sibling_index = btn_root.transform:GetSiblingIndex()
			local target_index = active_index_tab[sibling_index]
			local pos_index = math.floor(target_index / 5)
			
			local max_hight = - hight / 2  * (2 * pos_index + 1) + BTNHIGHT
			local min_hight = - hight / 2  * (2 * pos_index + 1) - pos_index * cell_space
			self:MoveObjBtn(btn_root, ison, max_hight, min_hight, time, false, width, hight)
		end
	end
end

--播放按钮组按钮的动画
function MainUIView:PlayBtnGroupAni(btn_group_list, ison, max_move, min_move, offset, special_group, btn_width)
	if nil == btn_group_list then
		return
	end

	local len = GetTableLen(btn_group_list)
	local time = 0.5
	local offset_index = 0

	for i = 1, len do
		local btn_root = btn_group_list[i]
		if btn_root and not IsNil(btn_root.gameObject) and btn_root.gameObject.activeSelf then
			offset_index = offset_index + 1
			
			if nil == min_move then
				-- 横向打开
				-- min_move = max_move - offset * offset_index
				--纵向
				min_move = offset * -1
			end

			local not_hide = false
			if ViewManager.Instance:IsOpen(GuideModuleName.OpenFunFlyView) then
				local view = ViewManager.Instance:GetView(GuideModuleName.OpenFunFlyView)
				if view:GetTargetObj() == btn_root then -- 功能开启中, 不需要显示
					not_hide = true
				end
			end

			self:MoveObjBtn(btn_root, ison, max_move, min_move, time, not_hide, btn_width)
		end
	end
end

-- 清除按钮动画
function MainUIView:ClearBtnMoveTween(btn_root)
    if self.tween_list[btn_root] then
        MOVETWEEN_COUNT = MOVETWEEN_COUNT - 1
        self.tween_list[btn_root]:Kill()
    end
end

function MainUIView:PlayBtnGroupAniCallBack(callback)
	self.ani_callback = callback
end

-- 单个按钮移动
function MainUIView:MoveObjBtn(btn_root, is_on, max_move, min_move, time, not_hide, width, hight)
	if (not IsNil(btn_root.transform)) and btn_root.transform.name and TopNotNeedHideBtn[btn_root.transform.name] then
		return
	end

	MOVETWEEN_COUNT = MOVETWEEN_COUNT + 1
	local move_vaule = is_on and min_move or max_move
    self:ClearBtnMoveTween(btn_root)

	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_move = btn_root.rect:DOAnchorPosY(move_vaule, time)
	tween_move:SetEase(DG.Tweening.Ease.Linear)
	tween_sequence:Append(tween_move)
	self.tween_list[btn_root] = tween_sequence

	if not_hide then
		btn_root.rect.sizeDelta = u3dpool.vec2((width or BTNWIDTH), (hight or BTNHIGHT))
		btn_root.canvas_group.alpha = 1
		btn_root.canvas_group.blocksRaycasts = true
		btn_root.canvas_group.interactable = true
		self:SetChildrenBlocksRaycasts(btn_root, true)
	else
		--现在是向上这个需要打开
		btn_root.rect.sizeDelta = u3dpool.vec2((width or BTNWIDTH), (hight or BTNHIGHT))
		btn_root.canvas_group.blocksRaycasts = is_on
		btn_root.canvas_group.interactable = is_on
		self:SetChildrenBlocksRaycasts(btn_root, is_on)
	end

	if not not_hide then
		local from_alpha = is_on and 0 or 1
		local to_alpha = is_on and 1 or 0
		local tween_alpha = btn_root.canvas_group:DoAlpha(from_alpha, to_alpha, time)
		tween_sequence:Join(tween_alpha)
	end

	local btn_name = (btn_root and not IsNil(btn_root.gameObject)) and btn_root.gameObject.name
	tween_sequence:OnComplete(function ()
		self.tween_list[btn_root] = nil
		MOVETWEEN_COUNT = MOVETWEEN_COUNT - 1
		if self.ani_callback then
			self.ani_callback(btn_name)
		end

		if not not_hide then
			if is_on then
				btn_root.rect.sizeDelta = u3dpool.vec2((width or BTNWIDTH), (hight or BTNHIGHT))
			else
				btn_root.rect.sizeDelta = u3dpool.vec2(0, (hight or BTNHIGHT))
			end
		end

		if MOVETWEEN_COUNT > 0 and IsEmptyTable(self.tween_list) then
			MOVETWEEN_COUNT = 0
		end

		if MOVETWEEN_COUNT == 0 then
			self:MoveObjBtnAllComplete()
		end
	end)
end

--播放一小部分按钮动画
function MainUIView:NotMoveAllBtn(btn_group_list, ison, max_move, min_move, offset, line_index)
	if nil == btn_group_list then
		return
	end

	local len = GetTableLen(btn_group_list)
	local time = 0.5
	local offset_index = 0
	local next_move = false
	local base_move = max_move
	local move_offset = 0
	for i = 1, len do
		local btn_root = btn_group_list[i]
		local btn_name = (btn_root and not IsNil(btn_root.gameObject)) and btn_root.gameObject.name
		if btn_root and not IsNil(btn_root.gameObject) and btn_root.gameObject.activeSelf then
			offset_index = offset_index + 1
			-- 横向移动将下列代码打开
			-- max_move = base_move - offset * (offset_index - 1)
			-- min_move = base_move - offset * offset_index
			if SundryViewNameButton[btn_name] then
				if next_move and self:GetTopBtnNeedHideBtn(btn_name) == false then
					-- 横向移动将下列代码打开
					-- max_move = base_move - offset * (offset_index - move_offset)
					-- min_move = base_move - offset * offset_index
					self:MoveObjBtn(btn_root, ison, max_move, min_move, time, true)
				end

				if self:GetTopBtnNeedHideBtn(btn_name) then
					next_move = true
					self:MoveObjBtn(btn_root, ison, max_move, min_move, time, false)
					move_offset = move_offset + 1
				end
			else
				local act_id = Split(btn_name, ":")[2]
				if act_id and self:IsTopNeedShowAct(tonumber(act_id)) then
					-- 横向移动将下列代码打开
					-- max_move = base_move - offset * (offset_index - move_offset)
					-- min_move = base_move - offset * offset_index
					self:MoveObjBtn(btn_root, ison, max_move, min_move, time, true)
				else
					next_move = true
					self:MoveObjBtn(btn_root, ison, max_move, min_move, time, false)
					move_offset = move_offset + 1
				end
			end
		end
	end
end

function MainUIView:SetChildrenBlocksRaycasts(btn, blocksRaycasts)
	local btn_go = btn.gameObject or btn
	local group_list = btn_go:GetComponentsInChildren(typeof(UnityEngine.CanvasGroup))
	for i = 0, group_list.Length - 1 do
		local canvas_group = group_list[i]
		if canvas_group then
			canvas_group.blocksRaycasts = blocksRaycasts
		end
	end
end

--所有的按钮动画播放完回调
function MainUIView:MoveObjBtnAllComplete()
	if self.main_top_btn_enable_cache then
		local temp = self.main_top_btn_enable_cache
		self.main_top_btn_enable_cache = nil
        for _i, _v in pairs(temp) do
			self:SetMainTopBtnObjEnable(_i, _v)
		end
	end


	GlobalEventSystem:Fire(MainUIEventType.MAIN_TOP_ARROW_ANIM_COMPLETE)
end


-- 【 * 改 * 】那两个傻逼按钮 引起的特殊逻辑
function MainUIView:PlayBtnGroupTween(group_index, is_on, is_hide_all)
	local group_list = self["button_group_" .. group_index]
	if IsEmptyTable(group_list) then
		return
	end

	local btn_name = ""
	local width = 0
	local show_pos_x = 0
	local offset_x = 0
	-- local is_special = group_index == 2
	local show_num = 0

	for i,btn in ipairs(group_list) do
		if not IsNil(btn.gameObject) and btn.gameObject.activeSelf then
			width = BTNWIDTH
			show_pos_x = show_pos_x - width / 2 + offset_x
			offset_x = 0
			-- if is_special and show_num >= 3 then
			-- 	show_pos_x = -215 - self.special_interval
			-- 	-- show_pos_x = show_pos_x - self.special_interval
			-- 	is_special = false
			-- end

			if is_on then
				self:MoveObjBtn(btn, is_on, show_pos_x , show_pos_x, 0.5, true, width)
				show_num = show_num + 1
			elseif is_hide_all then
				show_pos_x = show_pos_x + width
				self:MoveObjBtn(btn, is_on, show_pos_x , show_pos_x, 0.5, false, width)
				show_num = show_num + 1
			else
				btn_name = btn.gameObject.name
				if SundryViewNameButton[btn_name] then
					if self:GetTopBtnNeedHideBtn(btn_name) then
						show_pos_x = show_pos_x + width
						self:MoveObjBtn(btn, is_on, show_pos_x, show_pos_x, 0.5, false, width)
					else
						self:MoveObjBtn(btn, is_on, show_pos_x , show_pos_x, 0.5, true, width)
						show_num = show_num + 1
					end
				else
					local act_id = Split(btn_name, ":")[2]
					if act_id and self:IsTopNeedShowAct(tonumber(act_id)) then
						self:MoveObjBtn(btn, is_on, show_pos_x, show_pos_x, 0.5, true, width)
						show_num = show_num + 1
					else
						show_pos_x = show_pos_x + width
						self:MoveObjBtn(btn, is_on, show_pos_x, show_pos_x, 0.5, false, width)
					end
				end
			end

			show_pos_x = show_pos_x - width / 2
		end
	end
end

-- 【 * 改 * 】那两个傻逼按钮
function MainUIView:PlaySpecialBtnGroupTween(is_on, is_play)
	local temp_pos_x = -227 - self.special_interval + 83
	self:MoveObjBtn(self.node_list.BtnCountryMap, is_on, temp_pos_x, temp_pos_x, 0.5, is_on, 83, 147)
	self:MoveObjBtn(self.node_list.BtnWorldBossView, is_on, -227, -227, 0.5, is_on, 83, 147)
	self:MoveObjBtn(self.node_list.BtnWorldServerView, is_on, -227, -227, 0.5, is_on, 83, 147)
end

-- 【 * 改 * 】刷新第二排的位置
function MainUIView:FlushBtnGroupPos(group_index)
	if not self.is_activity_load then
		return
	end

	-- 在播动画中，播完还有一次刷新
	if MOVETWEEN_COUNT > 0 then
		return
	end

	local group_list = self["button_group_" .. group_index]
	if IsEmptyTable(group_list) then
		return
	end

	local is_on = self:GetMoveDisIson()
	local width = 0
	local pos_x = 0
	-- local is_offset = group_index == 2
	local show_num = 0
	local btn_name = ""
	local btn_obj = nil
	for i,btn in ipairs(group_list) do
		btn_obj = btn.gameObject
		if not IsNil(btn_obj) and btn_obj.activeSelf then
			btn_name = btn_obj.name
			show_num = show_num + 1
			width = BTNWIDTH
			pos_x = pos_x - width / 2
			-- if is_offset and show_num > 3 then
			-- 	-- pos_x = pos_x - self.special_interval
			-- 	pos_x = -215 - self.special_interval
			-- 	is_offset = false
			-- end

			if not is_on then
				if SundryViewNameButton[btn_name] and self:GetTopBtnNeedHideBtn(btn_name) then
					pos_x = pos_x + width
					show_num = show_num - 1
					btn.canvas_group.alpha = 0
					RectTransform.SetSizeDeltaXY(btn.rect, 0, BTNHIGHT)
				else
					local act_id = Split(btn_name, ":")[2]
					if act_id and not self:IsTopNeedShowAct(tonumber(act_id)) then
						pos_x = pos_x + width
						show_num = show_num - 1
						btn.canvas_group.alpha = 0
						RectTransform.SetSizeDeltaXY(btn.rect, 0, BTNHIGHT)
					end
				end
			end

			RectTransform.SetAnchorAllign(btn.rect, AnchorPresets.TopRight)
			RectTransform.SetAnchoredPositionXY(btn.rect, pos_x, -40)

			pos_x = pos_x - width / 2
		end
	end
end



--------------------------
-- 【     监听事件      】 --
--------------------------
-- 鼓舞改变
function MainUIView:OnGuWuChange(data)
	local count1, count2 = data.coin_guwu_count, data.gold_guwu_count
	local add_per1, add_per2
	if data.scene_type == SceneType.Wujinjitan or data.scene_type == SceneType.LingHunGuangChang then
		add_per1 = WuJinJiTanWGData.Instance:GetGuWuAddPer(count1)
		add_per2 = WuJinJiTanWGData.Instance:GetGuWuAddPer(count2)
	elseif data.scene_type == SceneType.WorldBoss then
		add_per1 = BossWGData.Instance:GetGuWuAddPer(count1)
		add_per2 = BossWGData.Instance:GetGuWuAddPer(count2)
	elseif data.scene_type == SceneType.Guild_Invite then
		add_per1 = GuildInviteWGData.Instance:GetGuWuAddPer(count1)
		add_per2 = GuildInviteWGData.Instance:GetGuWuAddPer(count2)
	elseif data.scene_type == SceneType.GuildMiJingFB then
		add_per1 = GuildWGData.Instance:GetCurGuWuTotalEffect()
		add_per2 = 0
	elseif data.scene_type == SceneType.BOSS_INVASION then
		add_per1 = BOSSInvasionWGData.Instance:GetCoinAddPer(count1)
		add_per2 = BOSSInvasionWGData.Instance:GetGoldAddPer(count2)
	end

	if add_per1 and add_per2 then
		local total_add = add_per1 + add_per2
		local is_add = total_add > 0
		self.node_list["btn_inspire_bg"]:SetActive(is_add)
		self.node_list["btn_inspire_can_up_img"]:SetActive(not is_add)
		self:ShakeGuwuIcon(not is_add)
		if is_add then
			self.node_list["btn_inspire_text"].text.text = string.format(Language.FuBenPanel.ExpAddHurtPer, total_add)
		end
	end
end

function MainUIView:ShakeGuwuIcon(value)
	if self.node_list["btn_inspire_icon"] then
		self.node_list["btn_inspire_icon"].animator:SetBool("shake",value)
	end
end

--角色等级发生变化，刷新按钮
function MainUIView:UpdateActLvLimitIcons()
	local level_limit_list = self.act_lvlimit_icon_list
	self.act_lvlimit_icon_list = {}

	for k,v in pairs(self.activity_button_list) do
		if v:CheckMainUIActIsLimit() then
			self:ActivitySetButtonVisible(v:GetData(), false)
		end
	end

	for k, v in pairs(level_limit_list) do
		self:activityChangeCallBack(v.act_type, v.status, v.end_time, v.open_type)
	end

	QunXiongZhuLuWGCtrl.Instance:CheckActIsOpen()
	FengShenBangWGCtrl.Instance:CheckActIsOpen()
	ShiTuXiuLiWGCtrl.Instance:CheckActIsOpen()
	LimitTimeGiftWGCtrl.Instance:CheckActIsOpen()
	OperationTaskChainWGCtrl.Instance:CheckActIsOpen()
	XiuZhenRoadWGCtrl.Instance:CheckActIsOpen()

	self:UpdateBtnGroup()
    -- self:FlushLevelGift()
	self:FlushLevelGiftWelfareUpgrade()
    self:FlushCalendar()
	self:FlushFuncBoxState()

    if self.main_ui_bubble_view ~= nil and self.main_ui_bubble_view:IsOpen() then
    	self.main_ui_bubble_view:Flush(0, "level")
    end
end

function MainUIView:RealRechargeChange()
	local level_limit_list = self.act_lvlimit_icon_list
	self.act_lvlimit_icon_list = {}
	for k, v in pairs(level_limit_list) do
		-- local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(v.act_type)
		-- if not MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg) then
			self:activityChangeCallBack(v.act_type, v.status, v.end_time, v.open_type)
		-- end
	end
end

-- 活动改变1
function MainUIView:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	local callback = function ()
		if MainUiLimitShowAct[activity_type] then
			if self.main_ui_bubble_view ~= nil and self.main_ui_bubble_view:IsOpen() then
				self.main_ui_bubble_view:Flush(0, "act", {activity_type = activity_type, status = status,
											next_time = next_time, open_type = open_type})
			end
			return
		end

		self:activityChangeCallBack(activity_type, status, next_time, open_type)
	end

	if not self.is_mainui_view_load then
		self:AddInitCallBack(activity_type, callback)
	else
		callback()
	end
end

-- 活动改变2
function MainUIView:activityChangeCallBack(act_type, status, end_time, open_type)
	-- 判断这个活动是否需要通过这个方法来显示按钮
	if self:CheckNotShowIcon(act_type) then
		return
	end

	--通过活动id获取配置
	local act_cfg, act_type = self:GetActCfgByActType(act_type, open_type)
	if act_cfg == nil then
		return
	end

	if act_cfg.timing_fold and act_cfg.timing_fold == 1 then
		self:FuncBoxActStateChangeCallBack(act_type, status, end_time, open_type, act_cfg)
	end

	if status == ACTIVITY_STATUS.CLOSE then
		if (act_type ~= ACTIVITY_TYPE.BP_CAPABILITY_WING and act_type ~= ACTIVITY_TYPE.RAND_ACT and act_type ~= ACTIVITY_TYPE.BANBEN_ACTIVITY)
			or (act_type == ACTIVITY_TYPE.RAND_ACT and self:CanCloseRandIcon(open_type))
			or (act_type == ACTIVITY_TYPE.BP_CAPABILITY_WING and self:CanCloseBipinIcon())
			or (act_type == ACTIVITY_TYPE.BANBEN_ACTIVITY and self:CanCloseRandIcon(open_type)) then
			self.act_lvlimit_icon_list[act_type] = nil
			self:ActivitySetButtonVisible(act_type, false) --移除按钮
			self:RemoveInitCallBack(act_type)
			ActivityWGCtrl.Instance:RemoveActivityTips(act_type)
		end
	else
		--特殊处理，让按钮消失
		if self:CheckNotShowIconSpecial(act_type, act_cfg, status, end_time, open_type) then
			return
		end

		--层级排序：sort == 2 则表示在第二排，sort == 3则表示在第三排
		local sort = act_cfg.sort or 30
		local act_icon
		if status == ACTIVITY_STATUS.STANDY then
			act_icon = self:SetActBtn(act_type, act_cfg, sort)
			if act_icon then
				-- act_icon:Flush("SetEndTime", {end_time, nil, nil, nil, COLOR3B.GREEN})
				---[[ 活动准备改成XX:XX时间开启不再倒计时
				local time_tab = os.date("*t", end_time)
				-- 如果后端时间戳误差1则取整
				if time_tab.min % 10 == 9 then
					time_tab.min = time_tab.min + 1
					if time_tab.min >= 60 then
					    time_tab.min = 0
					    time_tab.hour = time_tab.hour + 1
					end
				elseif time_tab.min % 10 == 1 then
					time_tab.min = time_tab.min - 1
				end

				local stand_str = Language.Activity.KaiQi
				if act_type == ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE then
					stand_str = Language.Activity.Mate
				end

				local open_time_str = string.format("%d:%02d%s", time_tab.hour, time_tab.min, stand_str)
				act_icon:Flush("SetTime", {open_time_str})
				--]]
				act_icon:Flush("SetActivityStateFlag", {act_type, status})
				if ActivityStandShowNotice[act_type] then
					ActivityWGCtrl.Instance:OpenActivityNoticeView(act_type) 
				end
			end

        elseif status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.XUNYOU then
			if end_time and act_cfg.show_time == 1 then --显示倒计时
				if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK then -- 冲榜助力显示每档的倒计时
					local cur_time = TimeWGCtrl.Instance:GetServerTime()
					end_time = TimeUtil.GetTodayRestTime(cur_time) + cur_time
				end

                if end_time - TimeWGCtrl.Instance:GetServerTime() <= 0 and self.act_lvlimit_icon_list[act_type] ~= nil then
                    self.act_lvlimit_icon_list[act_type] = nil
                else
                    act_icon = self:SetActBtn(act_type, act_cfg, sort)
                    if act_icon then
                        act_icon:Flush("SetEndTime", {end_time})
                        act_icon:Flush("SetActivityStateFlag", {act_type, status})
                        self:SetSpecialName(status, act_icon, act_cfg)
						if not ActivityStandShowNotice[act_type] then
							ActivityWGCtrl.Instance:OpenActivityNoticeView(act_type) 
						end
                    end
                end
            else
                act_icon = self:SetActBtn(act_type, act_cfg, sort)
                if act_icon then
                    act_icon:Flush("SetBottomContent", {""})
                    act_icon:RemoveCountDown()
					if not ActivityStandShowNotice[act_type] then
						ActivityWGCtrl.Instance:OpenActivityNoticeView(act_type) 
					end
                end
            end
		end

		if act_icon then
			if (status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.XUNYOU) then
				act_icon:Flush("ShowAni", {act_cfg.is_show_ani == 1})
				act_icon:Flush("ResetLiuGuangEffect")

				-- 活动按钮创建后执行等待刷新列表
				local wait_flush_list = self.activity_btn_wait_flush_list[act_type]
				if wait_flush_list then
					for key, param_t in pairs(wait_flush_list) do
						act_icon:Flush(key, param_t)
					end

					self.activity_btn_wait_flush_list[act_type] = nil
				end
			end

			act_icon:AddClickEventListener(BindTool.Bind(self.OnClickActIcon, self, act_icon))
		end
	end

	self:UpdateBtnGroup()
end

--通过活动id获取配置
function MainUIView:GetActCfgByActType(act_type, open_type)
	local act_cfg = DailyWGData.Instance:GetActivityConfig(act_type)
	if CompetitionWGData.IsBipinType(act_type) then
		act_type = ACTIVITY_TYPE.BP_CAPABILITY_WING
		act_cfg = DailyWGData.Instance:GetActivityConfig(act_type) --比拼
	elseif nil == act_cfg or act_type == ACTIVITY_TYPE.KF_BOSS_FIGHT or act_type == ACTIVITY_TYPE.RAND_WEEKEND_BOSS then
		if act_type > 2000 and act_type < 3000 or act_type == ACTIVITY_TYPE.KF_BOSS_FIGHT then
			if ActivityWGData.Instance:GetActivityIsClose(act_type) then
				if self.rand_open_icon[open_type] and self.rand_open_icon[open_type][act_type] then
					self.rand_open_icon[open_type][act_type] = nil
				end
			else
				if self.rand_open_icon[open_type] then
					self.rand_open_icon[open_type][act_type]  = true
				else
					return nil, nil
				end
			end

			if open_type == RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_VERSION then
					act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.BANBEN_ACTIVITY) --版本活动
					act_type = ACTIVITY_TYPE.BANBEN_ACTIVITY
			else
				act_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.RAND_ACT) --随机活动
				act_type = ACTIVITY_TYPE.RAND_ACT
			end
		else
			return nil, nil
		end
	end

	return act_cfg, act_type
end

function MainUIView:CanCloseRandIcon(open_type)
	local icon_table = self.rand_open_icon[open_type]
	if not icon_table then
		return true
	end
	if icon_table ~= nil then
		for k,v in pairs(icon_table) do
			return false
		end
	end
	return true
end

function MainUIView:SetActBtn(act_type, act_cfg, sort)
	if not act_type or not act_cfg then
		return
	end

	local act_icon = self:ActivitySetButtonVisible(act_type, true, sort)
	if act_cfg then
		act_icon:Flush("SetSprite", {act_cfg.res_name, act_cfg.act_name_res, act_cfg.bg_res})
		act_icon:SetActCfg(act_cfg)
		if self.active_load_time + 5 < Status.NowTime and act_cfg.is_show_ani == 2 then
			act_icon:Flush("ShowFlyAni")
		else
			act_icon:SetHasFly(true)
		end
	end

	if ActRemindList[act_type] and self.act_remind[act_type] == nil then
		self.act_remind[act_type] = BindTool.Bind(self.ActBtnRemind, self, act_icon)
		RemindManager.Instance:Bind(self.act_remind[act_type], ActRemindList[act_type])
	end

	act_icon:SetData(act_type)
	GlobalEventSystem:Fire(MainUIEventType.CHANGE_MAINUI_BUTTON, act_type, true) --创建按钮
	return act_icon
end

function MainUIView:ActBtnRemind(act_icon, remind_name, num)
	self.act_remindRecord[remind_name] = num

	if act_icon then
		act_icon:Flush("SetRedPoint", {num > 0})
		self:ActiviyAggregationActBtnRemind(act_icon:GetActId(), num)
	end
	-- self:CheckChangeTopRemind()
	-- self:CheckChangeTopLeftRemind()
end

-- 判断这个活动是否可以显示
-- @true 不可以显示
-- @false 可以显示
function MainUIView:CheckNotShowIcon(act_type)
	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_BATTLE_SOUL  -- 战场之魂图标用置状态控制
		or act_type == ACTIVITY_TYPE.ShopDazzleReturn 		-- 炫装白送
		or act_type == ACTIVITY_TYPE.DayDayFanLi then
		return true
	elseif act_type == ACTIVITY_TYPE.INVESTPLAN then 		-- 投资计划
		return true
	elseif act_type == ACTIVITY_TYPE.RAND_WEEKEND_BOSS then	-- 周末Boss特殊处理
		return true
	elseif act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SPECIAL_GIFT then
		return true
	-- elseif act_type == ACTIVITY_TYPE.MEIRI_LEICHONG then 	-- 每日充值
	-- 	return not RechargeWGData.Instance:GetIsFirstRecharge()
	elseif act_type == ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN or act_type == ACTIVITY_TYPE.SHOW_TASK_CHAIN then -- 任务链
		OperationTaskChainWGCtrl.Instance:CheckActIsOpen()
		return true
	end

	-- 判断活动提前结束条件
	if not ActivityWGData.Instance:IsActShow(act_type) then
		return true
	end

	return false
end

-- 特殊处理，让按钮消失
function MainUIView:CheckNotShowIconSpecial(act_type, act_cfg, status, end_time, open_type)
	if act_cfg.is_show_in_mainview == 0 then
		self:ActivitySetButtonVisible(act_type, false)
		return true
	end

	-- if act_cfg.parent_act_type ~= "" and act_cfg.parent_act_type > 0 then
	-- 	self:ActivitySetButtonVisible(act_type, false)
	-- 	return true
	-- end

	if act_cfg.timing_fold ~= "" and act_cfg.timing_fold == 1 then
		self:ActivitySetButtonVisible(act_type, false)
		return true
	end

	--是否需要等级上限/天数处理
	if MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg) then
		--print_error("隐藏活动按钮 >>>>> act_type:".. act_cfg.act_type, "status:".. status)
		self:ActivitySetButtonVisible(act_type, false)
		self.act_lvlimit_icon_list[act_type] = {act_type = act_type, status = status, end_time = end_time ,open_type = open_type}
		return true
	end

	-- 灵妖卖货
	if act_type == ACTIVITY_TYPE.RAND_ACT_LINGYAOMAIHUO then
		return ServerActivityWGData.Instance:GetLingYaoMaiHuoIsBuyAllGift()
	end

	-- 小鸭疾走不显示准备状态
	if act_type == ACTIVITY_TYPE.KF_DUCK_RACE and status == ACTIVITY_STATUS.STANDY then
		return true
	end

	-- 五气朝元判断功能开启表开启条件
	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DRAGON_TRIAL then
		if not FunOpen.Instance:GetFunIsOpened(FunName.DragonTrialView) then
			self.dragon_trial_act_info = {act_type = act_type, status = status, end_time = end_time ,open_type = open_type}
			return true
		end
	end

	return false
end

-- 添加删除 活动按钮
function MainUIView:ActivitySetButtonVisible(activity_type, is_show, sort)
	if not activity_type then
		return
	end

	if not is_show then
		if self.activity_button_list[activity_type] then
			if ActRemindList[activity_type] then
				RemindManager.Instance:UnBind(self.act_remind[activity_type])
				self.act_remind[activity_type] = nil
			end
			self.activity_button_list[activity_type]:DeleteMe()
			self.activity_button_list[activity_type] = nil
			self.activity_btn_list_1[activity_type] = nil
			self.activity_btn_list_2[activity_type] = nil
			self.activity_btn_list_3[activity_type] = nil
			self.activity_btn_list_4[activity_type] = nil
			self.activity_btn_list_5[activity_type] = nil
			self.chat_activity_btn_list[activity_type] = nil
			self:UpdateBtnGroup()
			-- GlobalEventSystem:Fire(MainUIEventType.CHANGE_MAINUI_BUTTON, activity_type, false) 删除按钮
		end

		self:ActivityAggregationActBtnVisibleChange(activity_type)
	else
		local button = self.activity_button_list[activity_type]
		if not button then
			local parent = nil
			sort = sort or SORT_DIVISOR --容错，默认排第一
			local index = math.floor(sort / SORT_DIVISOR)
			index = index > 0 and index or 1

			parent = self.node_list["ButtonGroup" .. index]
			if nil == parent then
				return
			end

			button = self:CreateActiviteBtn(parent, activity_type)
			if self.is_move_top_fun and not self:GetMoveDisIson() and index ~= BUTTON_GROUP4 then
				button:Flush("HideButton")
			end

			-- if index ~= BUTTON_GROUP4 and index ~= BUTTON_GROUP3 then
				-- 首充放右边第一个
				-- if activity_type == ACTIVITY_TYPE.FIRST_CHONGZHI then
				-- 	button:SetAsLastSibling()
				-- elseif activity_type == ACTIVITY_TYPE.MEIRI_LEICHONG then
				-- 	local count = #self.button_group_1
				-- 	button:RealSetSiblingIndex(count - 2)
				-- else
					-- button:RealSetSiblingIndex(0) 		-- 改成同步的，不然UpdateBtnGroup函数的结果会有误
				-- end
			-- end

			if activity_type == ACTIVITY_TYPE.LIMIT_TIME_GIFT or activity_type == ACTIVITY_TYPE.CAP_UP_GIFT then
				button:ShowLiuGuang(false)
				button:HideBg()
			end

			self.activity_button_list[activity_type] = button
			self["activity_btn_list_" .. index][activity_type] = button

			----按钮排序
			local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(activity_type)
			button:SetData(activity_type)
			button:SetActCfg(act_cfg)
			self:SortBtnList(index)

			-- if index == BUTTON_GROUP2 or index == BUTTON_GROUP3 or index == BUTTON_GROUP4 or index == BUTTON_GROUP5 then --这个要排序
			-- 	self["activity_btn_list_" .. index][activity_type] = button
			-- 	----按钮排序
			-- 	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(activity_type)
			-- 	button:SetData(activity_type)
			-- 	button:SetActCfg(act_cfg)
			-- 	if index == BUTTON_GROUP2 then
			-- 		self:SortBtnList2()
			-- 		-- self:FlushBtnGroupPos(2)
			-- 	elseif index == BUTTON_GROUP3 then
			-- 		self:SortBtnList3()
			-- 	elseif index == BUTTON_GROUP4 then
			-- 		self:SortBtnList4()
			-- 	elseif index == BUTTON_GROUP5 then
			-- 		self:SortBtnList5()
			-- 	end
			-- end

			-- -- 2023/09/18 策划zbp 说第三排按钮放不下强行要 将活动抽一部分到系统第一排， 还一定要让活动显示在最左边
			-- if index == BUTTON_GROUP1 then
			-- 	button:RealSetSiblingIndex(0)
			-- end

			-- chat面板得活动按钮
			if activity_type == ACTIVITY_TYPE.LIMIT_TIME_GIFT or activity_type == ACTIVITY_TYPE.CAP_UP_GIFT or activity_type == ACTIVITY_TYPE.LIMIT_TIME_GIFT_PURCHASE then
				self:ForceRebuildLimitGiftBtnTrans()

				self.chat_activity_btn_list[activity_type] = button
			end
		end

		button:Flush("CheckSpecialEffectBtn", {activity_type})
		self:ShowMainTopActBtnObj(activity_type)
		self:ActivityAggregationActBtnForParent(activity_type)
		self:ActivityAggregationActBtnVisibleChange(activity_type)
		return button
	end

	return nil
end

function MainUIView:SortBtnList(activity_btn_list_id)
	local sort_cfg_table = {}
	local act_list_key = "activity_btn_list_" .. activity_btn_list_id
	if self[act_list_key] then
		for k,v in pairs(self[act_list_key]) do
			-- table.insert(sort_cfg_table, {v.act_cfg})
			if not self:CheckIsActivityAggregationBtn(v.act_cfg.act_type) then		-- 聚合的按钮不做排序
				table.insert(sort_cfg_table, {act_type = v.act_cfg.act_type, sort = v.act_cfg.sort})
			end
		end
	end

	local fixed_btn_data_list = DailyWGData.Instance:GetBtnSortDataListCfg(activity_btn_list_id)
	if not IsEmptyTable(fixed_btn_data_list) then
		for k, v in pairs(fixed_btn_data_list) do
			if v.btn_name and v.btn_name ~= "" and (not self:CheckIsActivityAggregationBtn(v.btn_name)) then		-- 聚合的按钮不做排序 then
				table.insert(sort_cfg_table, {sort = v.sort, btn_name = v.btn_name})
			end
		end
	end

	local aggregation_button_list = self:GetAllActivityAggregationBtnByIndex(activity_btn_list_id)
	if aggregation_button_list then
		for i, v in ipairs(aggregation_button_list) do
			if v and v.parent_cell ~= nil then
				table.insert(sort_cfg_table, {sort = v.aggregation_sort, btn_cell = v.parent_cell})
			end
		end
	end

	table.sort(sort_cfg_table, SortTools.KeyUpperSorter("sort"))

	if not IsEmptyTable(sort_cfg_table) then
		for i, v in ipairs(sort_cfg_table) do
			if v.act_type then
				-- self["activity_btn_list_" ..activity_btn_list_id][v.act_type]:RealSetSiblingIndex(i - 1)
				self["activity_btn_list_" ..activity_btn_list_id][v.act_type]:SetAsLastSibling()
			elseif v.btn_cell ~= nil then
				local view = v.btn_cell:GetView()
				if view then
					view.transform:SetAsLastSibling()
				end
			else
				if self.node_list[v.btn_name] then
					-- self.node_list[v.btn_name].transform:SetSiblingIndex(i - 1)
					self.node_list[v.btn_name].transform:SetAsLastSibling()
				end
			end
		end
	end
end

function MainUIView:SortBtnList2()
	local sort_cfg_table = {}
	for k,v in pairs(self.activity_btn_list_2) do
		table.insert(sort_cfg_table, v.act_cfg)
	end

	for k,v in pairs(self.top_const_btn_sort_list_2) do
		table.insert(sort_cfg_table, {sort = v, btn_name = k})
	end

	table.sort(sort_cfg_table, SortTools.KeyLowerSorter("sort"))

	for i, v in ipairs(sort_cfg_table) do
		if v.act_type then
			self.activity_btn_list_2[v.act_type]:RealSetSiblingIndex(i - 1)
		else
			self.node_list[v.btn_name].transform:SetSiblingIndex(i - 1)
		end
	end
end

function MainUIView:SortBtnList3()
	local sort_cfg_table = {}
	for k,v in pairs(self.activity_btn_list_3) do
		table.insert(sort_cfg_table, v.act_cfg)
	end

	for k,v in pairs(self.top_const_btn_sort_list_3) do
		table.insert(sort_cfg_table, {sort = v, btn_name = k})
	end

	table.sort(sort_cfg_table, SortTools.KeyLowerSorter("sort"))

	for i, v in ipairs(sort_cfg_table) do
		if v.act_type then
			self.activity_btn_list_3[v.act_type]:RealSetSiblingIndex(i - 1)
		else
			self.node_list[v.btn_name].transform:SetSiblingIndex(i - 1)
		end
	end
end

-- xzp 几个按钮排序 改来改去，又要让固定按钮跟活动，自定义排序
function MainUIView:SortBtnList4()
	-- local sort_cfg_table = {}
	-- for k,v in pairs(self.activity_btn_list_4) do
	-- 	table.insert(sort_cfg_table, v.act_cfg)
	-- end

	-- table.sort(sort_cfg_table, SortTools.KeyLowerSorter("sort"))

	-- for i, v in ipairs(sort_cfg_table) do
	-- 	self.activity_btn_list_4[v.act_type]:RealSetSiblingIndex(i - 1)
	-- end

	local sort_tab = {}

	for k, v in pairs(BUTTON_GROUP_BTN_SORT_TAB_4) do
		table.insert(sort_tab, {btn_root = self.node_list[k], sort = v.sort, name = k})
	end

	for k, v in pairs(self.activity_btn_list_4) do
		local sort = (BUTTON_GROUP_ACT_SORT_TAB_4[k] or {}).sort or (v.act_cfg.sort + 60)
		table.insert(sort_tab, {act_btn_root = v, sort = sort, name = v.act_cfg.act_type})
		-- table.insert(sort_cfg_table, v.act_cfg)
	end

	table.sort(sort_tab, SortTools.KeyLowerSorter("sort"))

	for i = 1, #sort_tab do
		local data = sort_tab[i]

		if data.btn_root then
			data.btn_root.transform:SetAsLastSibling()
		elseif data.act_btn_root then
			data.act_btn_root.view.transform:SetAsLastSibling()
		end
	end
end

function MainUIView:SortBtnList5()
	local sort_cfg_table = {}
	for k,v in pairs(self.activity_btn_list_5) do
		table.insert(sort_cfg_table, v.act_cfg)
	end

	for k,v in pairs(self.top_const_btn_sort_list_5) do
		table.insert(sort_cfg_table, {sort = v, btn_name = k})
	end

	table.sort(sort_cfg_table, SortTools.KeyLowerSorter("sort"))

	for i, v in ipairs(sort_cfg_table) do
		if v.act_type then
			self.activity_btn_list_5[v.act_type]:RealSetSiblingIndex(i - 1)
		else
			self.node_list[v.btn_name].transform:SetSiblingIndex(i - 1)
		end
	end
end

--创建活动按钮
function MainUIView:CreateActiviteBtn(parent, activity_type)
	parent = parent or nil
	local act_icon
	if activity_type == ACTIVITY_TYPE.ZEROBUY then
		act_icon = ZeroBuyBtn.New()
	elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY then
		act_icon = MustBuyActivityBtn.New()
	-- elseif activity_type == ACTIVITY_TYPE.OPERATIONACTIVITY or activity_type == ACTIVITY_TYPE.OPERATIONACTIVITY_TWO then
	-- 	act_icon = OperationActivityBtn.New()
	elseif activity_type == ACTIVITY_TYPE.LIMIT_TIME_GIFT or activity_type == ACTIVITY_TYPE.CAP_UP_GIFT or activity_type == ACTIVITY_TYPE.LIMIT_TIME_GIFT_PURCHASE then
		parent = MainuiWGCtrl.Instance:GetLimitGiftBtnTrans()
		act_icon = MainActivityBtn.New()
	-- elseif activity_type == ACTIVITY_TYPE.LIMIT_TIME_GIFT_PURCHASE then
	-- 	parent = MainuiWGCtrl.Instance:GetLimitGiftBtnTrans()
	-- 	act_icon = MainActivityBtn.New()
	else
		act_icon = MainActivityBtn.New()
	end
	act_icon:SetShouQiStateFunc(BindTool.Bind(self.GetActBtnIsShouQi, self))
	act_icon:CreateBtnAsset(parent, activity_type)
	return act_icon
end

-- 获得活动按钮当前是否应该是收起状态（返回true表示是收起状态）
function MainUIView:GetActBtnIsShouQi(act_type)
	-- 如果右上角箭头是收起状态
	if self.node_list.ShrinkButton.toggle.isOn == false then
		-- 如果当前场景需要收起所有按钮
		if self.is_move_top_fun then
			return true
		elseif not self:IsTopNeedShowAct(act_type) then
			return true
		end
	end

	return false
end

-- 判断是否是右上角不收起的按钮（返回true表示不能收起）
function MainUIView:IsTopNeedShowAct(act_type)
	--[[
	if TopNeedShowActBtn[act_type] then
		return true
	end

	local act_cfg = DailyWGData.Instance:GetActivityConfig(act_type)
	if act_cfg then
		return act_cfg.show_time == 1
	end
	--]]

	return false
end

function MainUIView:ShowMainTopActBtnObj(act_id)
	local blocksRaycasts = false
	local btn = self.activity_button_list[act_id]

	if btn and btn.view then
		local move_dis_ison = self:GetMoveDisIson()
		if not move_dis_ison and not self:IsTopNeedShowAct(act_id) then
			btn.view.canvas_group.alpha = 0
			btn.view.rect.sizeDelta = u3dpool.vec2(0, BTNHIGHT)
		elseif self.is_move_top_fun and not move_dis_ison then 	--全收起来状态下按钮收起判断
			btn.view.canvas_group.alpha = 0
			btn.view.rect.sizeDelta = u3dpool.vec2(0, BTNHIGHT)
		else
			btn.view.canvas_group.alpha = 1
			btn.view.rect.sizeDelta = u3dpool.vec2(BTNWIDTH, BTNHIGHT)
			blocksRaycasts = true
		end

		btn.view.canvas_group.blocksRaycasts = blocksRaycasts
		self:SetChildrenBlocksRaycasts(btn.view, blocksRaycasts)
		-- btn.canvas_group.interactable = blocksRaycasts
	end
end

function MainUIView:SetSpecialName(status, act_icon, act_cfg)
	if status == ACTIVITY_STATUS.XUNYOU then
		if MarryWGData.Instance:GetOwnIsXunyou() then
        	act_icon:Flush("SetSprite", {act_cfg.res_name, "btn_hlxy"})
		else
			act_icon:Flush("SetSprite", {act_cfg.res_name, "btn_hlxy"})
		end
	end
end


-- 活动按钮点击事件
function MainUIView:OnClickActIcon(act_icon)
	local act_type = act_icon:GetData()
	self:ClickEnterActivity(act_type)
end

-- 活动按钮点击事件2
function MainUIView:ClickEnterActivity(act_type)
	local scene_type = Scene.Instance:GetSceneType()
	--跨服1v1进入预备场景
	if act_type == ACTIVITY_TYPE.KF_ONEVONE and scene_type ~= SceneType.Kf_OneVOne_Prepare then
		 Field1v1WGCtrl.Instance:EnterFieldPrepareScene(ACTIVITY_TYPE.KF_ONEVONE)
		return
	end

	--跨服3v3进入预备场景
	if act_type == ACTIVITY_TYPE.KF_PVP and scene_type ~= SceneType.Kf_PvP_Prepare then
		 --KF3V3WGCtrl.Instance:EnterPrepareScene()
		if scene_type ~= SceneType.Common then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.EnterPrepareLimit)
			return
		end
		
		KF3V3WGCtrl.Instance:EnterPrepareScene()
		-- KF3V3WGCtrl.Instance:GoToNpc()
		return
	end

	-- 跨服龙脉
	if act_type == ACTIVITY_TYPE.KF_LONGMAI then
		CrossLongMaiWGCtrl.Instance:FindNpc()
		return
	end

	-- 小鸭疾走
	if act_type == ACTIVITY_TYPE.KF_DUCK_RACE and scene_type ~= SceneType.KF_DUCK_RACE then
		-- 进入小鸭疾走活动场景
		DuckRaceWGCtrl.Instance:SendEnterScene()
	end

	if act_type == ACTIVITY_TYPE.OPERATIONACTIVITY or act_type == ACTIVITY_TYPE.OPERATIONACTIVITY_TWO then
		local open_type = OPERATION_ACTIVITY_OPEN_TYPE[act_type]
		OperationActivityWGCtrl.Instance:Open(nil, nil, open_type) --不要轻易改变传参
		return
	end

	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN  then
		local activity_status = ActivityWGData.Instance:GetActivityStatuByType(act_type)
		if activity_status.status == ACTIVITY_STATUS.STANDY then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi2)
			return
		end
	end

	-- 神龙红包
	if act_type == ACTIVITY_TYPE.CROSS_RED_PACKET_RAIN then

		if not YunbiaoWGData.Instance:GetIsHuShong() and not RedPacketRainWGData.Instance:IsInRedPacketArea() then
			if scene_type == SceneType.Common then
				-- 进入神龙红包场景范围
				RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ENTER)
			else
				TipWGCtrl.Instance:OpenAlertTips(Language.RedPacketRain.GoToArea,function ()
					RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ENTER)
				end,nil,nil,nil,5,nil,Language.RedPacketRain.Join)
			end
			
		end
		
	end

	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN and scene_type ~= SceneType.GuDaoJiZhan_FB then
		GuDaoFuBenWGCtrl.Instance:SendEnterGuDaoFB()
	end

	ActivityWGCtrl.Instance:OpenPopView(act_type)
end


-- 删除临时活动按钮特殊处理
function MainUIView:DelTempActIcon(act_type)
	self:ActivitySetButtonVisible(act_type, false)
end

--添加临时活动按钮特殊处理
function MainUIView:AddTempActIcon(act_type, end_time, act_cfg, color, flag, open_time)
	local act_icon_a = self:SetActBtn(act_type, act_cfg, act_cfg and act_cfg.sort or 30)
	if nil == act_icon_a then
		return
	end

	if open_time then
		act_icon_a:Flush("SetTime", {open_time})
	elseif end_time then
		act_icon_a:Flush("SetEndTime", {end_time, nil, nil, nil, color})
	else
		act_icon_a:Flush("SetBottomContent", {""})
	end

	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE then 						-- 双倍经验
		act_icon_a:Flush("SetSprite", {"btn_shungbeijingyan", "btn_shungbeijingyan_name"})
		act_icon_a:AddClickEventListener(function ()
			ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_exp")
		end)
	elseif act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP then 				-- 双倍掉落
		act_icon_a:Flush("SetSprite", {"btn_activity_hall", "btn_activity_hall_name"})
		act_icon_a:AddClickEventListener(function ()
			ViewManager.Instance:Open(GuideModuleName.FuBenPanel, "fubenpanel_welkin")
		end)
	elseif act_type == ACTIVITY_TYPE.FengShenBang then 										-- 封神榜
		act_icon_a:AddClickEventListener(function ()
			ViewManager.Instance:Open(GuideModuleName.FengShenBang)
		end)
		local status = FengShenBangWGData.Instance:GetActStatus()
		act_icon_a:Flush("ShowLiuGuang", {status = ACTIVITY_STATUS.OPEN})
		act_icon_a:Flush("SetActivityStateFlag", {act_type, status})
		act_icon_a:Flush("SetEndTime", {end_time, nil})
	elseif act_type == ACTIVITY_TYPE.QUN_XIONG_ZHU_LU then 									-- 群雄逐鹿
		act_icon_a:AddClickEventListener(function ()
			ViewManager.Instance:Open(GuideModuleName.QunXiongZhuLu)
		end)
	elseif act_type == ACTIVITY_TYPE.LIMIT_TIME_GIFT then 									-- 限时礼包
		act_icon_a:AddClickEventListener(function ()
			ViewManager.Instance:Open(GuideModuleName.LimitTimeGift)
		end)
		local complete_callback = function ()
			LimitTimeGiftWGCtrl.Instance:CheckActIsOpen()
			-- LimitTimeGiftWGCtrl.Instance:CheckGiftPassTip()
		end
		local status = LimitTimeGiftWGData.Instance:GetActStatus()
		act_icon_a:Flush("ShowLiuGuang", {status = ACTIVITY_STATUS.OPEN})
		act_icon_a:Flush("SetEndTime", {end_time, nil, complete_callback})
	elseif act_type == ACTIVITY_TYPE.CAP_UP_GIFT then 									-- 战力飙升礼包
		act_icon_a:AddClickEventListener(function ()
			ViewManager.Instance:Open(GuideModuleName.CapabilityUpGiftView)
		end)

		local complete_callback = function ()
			CapabilityUpGiftWGCtrl.Instance:CheckNeedChangeActState()
		end

		act_icon_a:Flush("ShowLiuGuang", {status = ACTIVITY_STATUS.OPEN})
		act_icon_a:Flush("SetEndTime", {end_time, nil, complete_callback})
	elseif act_type == ACTIVITY_TYPE.RAND_WEEKEND_BOSS then 								-- 周末Boss
		act_icon_a:AddClickEventListener(function ()
			ViewManager.Instance:Open(GuideModuleName.BanBenActivity)
			ServerActivityWGCtrl.Instance:SetChangeToView(ACTIVITY_TYPE.RAND_WEEKEND_BOSS, function()
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongHaiWeiKaiShi)
			end)
		end)
	elseif act_type == ACTIVITY_TYPE.XinFuTeMai then 										-- 新服特卖（目前不是活动，模拟活动配置显示按钮）
		act_icon_a:AddClickEventListener(function ()
			ViewManager.Instance:Open(GuideModuleName.XinFuTeMai, 0)
		end)
		act_icon_a:Flush("SetEndTime", {end_time, nil , function ()
			MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.XinFuTeMai)
			XinFuTeMaiWGCtrl.Instance:CheckViewClose()
		end})

	elseif act_type == ACTIVITY_TYPE.KF_BOSS_FIGHT then 									-- Boss乱斗
		act_icon_a:AddClickEventListener(function ()
			ViewManager.Instance:Open(GuideModuleName.BanBenActivity)
			ServerActivityWGCtrl.Instance:SetChangeToView(ACTIVITY_TYPE.KF_BOSS_FIGHT, function()
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongHaiWeiKaiShi)
			end)
		end)
		local info = ServerActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.KF_BOSS_FIGHT)
		local color = info.status == ACTIVITY_STATUS.STANDY and COLOR3B.GREEN
		act_icon_a:Flush("SetEndTime", {end_time, nil ,nil ,nil , color})
	elseif (act_type == ACTIVITY_TYPE.XIANMENGZHAN or act_type == ACTIVITY_TYPE.KF_GUILDBATTLE) and flag == "act_forecast" then
		act_icon_a:AddClickEventListener(function ()
			if act_type == ACTIVITY_TYPE.XIANMENGZHAN then
				ActivityWGCtrl.Instance:OpenPopView(ACTIVITY_TYPE.XIANMENGZHAN)
			else
				BiZuoWGCtrl.Instance:OpenActForecastView(act_type)
			end

			act_icon_a:Flush("ShowAni", {false})
		end)
		act_icon_a:Flush("SetBottomContent", {Language.BiZuo.DaZhanZaiJi})
	elseif act_type == ACTIVITY_TYPE.KF_XIANMENGZHAN then 									-- 跨服仙盟战
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
		local status = act_info.status
		act_icon_a:Flush("SetActivityStateFlag", {act_type, status})
	elseif act_type == ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN then 								-- 任务链
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.SHOW_TASK_CHAIN)
		if act_info then
			act_icon_a:Flush("SetActivityStateFlag", {act_type, act_info.status})
		end
		act_icon_a:AddClickEventListener(BindTool.Bind(self.OnClickActIcon, self, act_icon_a))
	elseif act_type == ACTIVITY_TYPE.TIANSHENJIANLIN then 									-- 天神降临
		local act_info = TianshenRoadWGData.Instance:GetTianShenJianLinInfo()
		local status = act_info and act_info.activity_state
		act_icon_a:Flush("SetActivityStateFlag", {act_type, status})
		act_icon_a:AddClickEventListener(function ()
			TianshenRoadWGCtrl.Instance:GotoShiLian()
		end)
	else
		act_icon_a:AddClickEventListener(BindTool.Bind(self.OnClickActIcon, self, act_icon_a))
	end

	act_icon_a:Flush("ShowAni", {act_cfg and act_cfg.is_show_ani == 1 or false})
	act_icon_a:Flush("ResetLiuGuangEffect")
	self:UpdateBtnGroup()
end



--------------------------
-- 【     特殊逻辑      】 --
--------------------------
function MainUIView:SetMianUITopButtonState(state)
	self.node_list.ShirnkButtonPanel:SetActive(state)
	self.node_list.TopButtons:SetActive(state)
	self.node_list.MapContent:SetActive(state)
end


function MainUIView:SetMianUITopButtonStateTwo(state)
	self.node_list.TopButtons:SetActive(state)
	self.node_list.ShirnkButtonPanel:SetActive(state)
end

function MainUIView:SetMianUILeftContentState(state)
	-- self.node_list.leftContent:SetActive(state)
end

-- 刷新活动流光特效
function MainUIView:ResetActBtnEffect()
	for k,v in pairs(self.activity_button_list) do
		v:Flush("ResetLiuGuangEffect")
	end
end

-- 通关活动id获取活动按钮的对象
function MainUIView:GetActivityButtonByActivityType( act_type )
	if act_type ~= nil then
		if self.activity_button_list[act_type] ~= nil then
			return self.activity_button_list[act_type]
		end
	end
	
	return nil
end



--------------------------
-- 【     按钮事件      】 --
--------------------------
-- 点击右上展开按钮
function MainUIView:OnShrinkBtnValueChange(ison)
    if CLIENT_DEBUG_LOG_STATE then
    	print_error("OnShrinkBtnValueChange", ison)
    end

	if not ison then
		if self.node_list.aggregation_collect_panel then
			self.node_list.aggregation_collect_panel:CustomSetActive(false)
		end	
	end

	self:SetLotteryCollectPaneStatus(false)
	self:SetMoveDisIson(ison)
	GlobalEventSystem:Fire(MainUIEventType.MAIN_TOP_ARROW_CLICK, ison)

	self.is_move_top_fun = false
	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId()
	local is_common_scene = scene_type == SceneType.Common and not IS_ON_CROSSSERVER
	local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId() or 0 					-- 藏宝湾的scene_id
	if (scene_id == bootybay_scene_id) then 	-- 在其他人主城或在藏宝湾地图  需要收起来
		is_common_scene = false
	end

	if is_common_scene then
		self.is_move_top_fun = self:GetMoveDisIson()
	else
		self.is_move_top_fun = true
	end

	-- 移动所有按钮（副本内一般都移动所有按钮）
	if self.is_move_top_fun then
		self:PlayBtnGroupAni(self["button_group_5"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		self:PlayBtnGroupAni(self["button_group_3"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		self:PlayBtnGroupAni(self["button_group_2"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		self:PlayBtnGroupAni(self["button_group_1"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)

		-- btngruup按钮（横向）
		-- self:PlayBtnGroupAni(self["button_group_3"], ison, THIRD_POS + BTNWIDTH, nil, BTNWIDTH)
		-- self:PlayBtnGroupAni(self["button_group_2"], ison, SECOND_POS + BTNWIDTH, nil, BTNWIDTH)
		-- self:PlayBtnGroupAni(self["button_group_1"], ison, FIRST_POS + BTNWIDTH, nil, BTNWIDTH)
		-- btngruup按钮（纵向）

		-- self:PlayBtnGroupAni(self["button_group_4"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		-- -- self:PlayBtnGroupAni(self["button_group_3"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		-- -- self:PlayBtnGroupAni(self["button_group_2"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		-- -- self:PlayBtnGroupAni(self["button_group_1"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		-- self:PlayBtnGroupTween(2, ison, true)
		-- self:PlaySpecialBtnGroupTween(ison, true)
		-- print_log("日志：移动所有按钮", ison and "展开" or "收起", "是否普通场景：" .. tostring(is_common_scene) .. ", scene_id:" .. Scene.Instance:GetSceneId())
	else
		self:NotMoveAllBtn(self["button_group_5"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		self:NotMoveAllBtn(self["button_group_1"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		self:NotMoveAllBtn(self["button_group_2"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		self:NotMoveAllBtn(self["button_group_3"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)


		-- btngruup按钮（横向）
		-- self:NotMoveAllBtn(self["button_group_1"], ison, FIRST_POS + BTNWIDTH, nil, BTNWIDTH)
		-- self:NotMoveAllBtn(self["button_group_2"], ison, SECOND_POS + BTNWIDTH, nil, BTNWIDTH)
		-- self:NotMoveAllBtn(self["button_group_3"], ison, THIRD_POS + BTNWIDTH, nil, BTNWIDTH)
		-- btngruup按钮（纵向）
		-- -- self:NotMoveAllBtn(self["button_group_5"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		-- -- -- self:NotMoveAllBtn(self["button_group_4"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		-- -- self:NotMoveAllBtn(self["button_group_1"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		-- -- self:NotMoveAllBtn(self["button_group_2"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		-- -- self:NotMoveAllBtn(self["button_group_3"], ison, BTNHIGHT / 2, nil, BTNHIGHT / 2)
		-- self:PlayBtnGroupTween(2, ison)
		-- print_log("日志：移动部分按钮", ison and "展开" or "收起", "是否普通场景：" .. tostring(is_common_scene) .. ", scene_id:" .. Scene.Instance:GetSceneId())
	end

	-- 晶晶：经验副本不收起按钮
	local ison4 = ison
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.LingHunGuangChang then
		ison4 = true
	end
	self:PlayBtnGroupWholeAni(self.node_list.ButtonGroup4, self["button_group_4"], ison4)

	self.node_list.task_btn_trans:SetActive(ison and is_common_scene)

	-- 特殊功能盒子入口
	self:MoveObjBtn(self.node_list.btn_func_box, ison, 96, -2, 0.5, false, 184, 96)

	self:SetRightTopTaskPaneStatus(ison)

	self:CheckChangeTopRemind() --主界面右上角小红点
end

-- 右上角任务列表
function MainUIView:SetRightTopTaskPaneStatus(ison)
	if self.tasklist_tween then
		self.tasklist_tween:Kill()
		self.tasklist_tween = nil
	end

	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local time = ison and 0.3 or 0.5
	local tween_move = self.node_list.tasklist_tween_root.rect:DOAnchorPosX(ison and -400 or -350,time)
	local from_alpha = ison and 0 or 1
	local to_alpha = ison and 1 or 0
	local tween_alpha = self.node_list.tasklist_tween_root.canvas_group:DoAlpha(from_alpha, to_alpha, time)
	tween_sequence:Append(tween_move)
	tween_sequence:Join(tween_alpha)
	self.tasklist_tween = tween_sequence

	self:SetChildrenBlocksRaycasts(self.node_list.tasklist_tween_root, ison)
end

-- 点击左上展开按钮
function MainUIView:OnLeftShrinkBtnValueChange(ison)
	self.left_shrink_ison = ison
	-- local rect_pos_x = self.node_list.button_group4_move_root.rect.sizeDelta.x
	-- local tween_sequence = DG.Tweening.DOTween.Sequence()
	-- local tween_move = self.node_list.button_group4_move_root.rect:DOAnchorPosX(ison and 0 or -rect_pos_x, 0.5)
	-- tween_move:SetEase(DG.Tweening.Ease.InOutBack)
	-- local from_alpha = ison and 0 or 1
	-- local to_alpha = ison and 1 or 0
	-- local tween_alpha = self.node_list.ButtonGroup4.canvas_group:DoAlpha(from_alpha, to_alpha, 0.5)
	-- tween_sequence:Append(tween_move)
	-- tween_sequence:Join(tween_alpha)
	self:CheckChangeTopLeftRemind()
end

-- 点击鼓舞
function MainUIView:OnClickOpenGuWuView()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.GuildMiJingFB then
		GuildWGCtrl.Instance:OpenGuWuView()
	elseif scene_type == SceneType.BOSS_INVASION then
		BOSSInvasionWGCtrl.Instance:OpenGuwuView()
	else
		FuBenWGCtrl.Instance:OpenTeamExpCheerView()
	end
end

-- 点击离开副本
function MainUIView:OnClickLevelFB()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    local main_role = Scene.Instance:GetMainRole()

	if main_role == nil or scene_cfg == nil then
		return
	end

    if scene_cfg.fight_cant_exit and 1 == scene_cfg.fight_cant_exit then
        if main_role:IsFightStateByRole() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.FightingCantExitFb)
            return
        end
    end

    local scene_type = Scene.Instance:GetSceneType()
    --当处于仙盟传功状态中，不让退出副本
    local is_chuan_gong_state = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_CHUAN_GONG)
	local is_chuan_gong = main_role:IsChuanGong()
	if scene_type == SceneType.GUILD_ANSWER_FB and is_chuan_gong_state and is_chuan_gong then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChunaGongNotExit)
		return
	end

	-- 孤岛激战
	if scene_type == SceneType.GuDaoJiZhan_FB then
		local fresh_time,boss_index = GuDaoFuBenWGData.Instance:GetBattleHrutRankInfo()
		if boss_index > 0 then
			if not self.gudao_leave_alert then
				self.gudao_leave_alert = Alert.New()
				self.gudao_leave_alert:SetLableString(Language.GuDaoJiZhan.NotOver)
				self.gudao_leave_alert:SetOkString(Language.Common.Confirm)
				self.gudao_leave_alert:SetCancelString(Language.Common.Cancel)
				self.gudao_leave_alert:SetOkFunc(BindTool.Bind1(self.LevelFB, self))
			end
			self.gudao_leave_alert:Open()
			return
		else
			self:LevelFB(true)
			return
		end
	end

	-- 仙侣PK
	if scene_type == SceneType.CROSS_PK_LOVER then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.IsPKNowCanNotOutFB)
		return
	end

	if scene_cfg.no_alert_quit and scene_cfg.no_alert_quit == 1 then
		self:LevelFB(true)
	else
		if not self.task_double_pop_alert then
			self.task_double_pop_alert = Alert.New()

			self.task_double_pop_alert:SetOkString(Language.Common.Confirm)
			self.task_double_pop_alert:SetCancelString(Language.Common.Cancel)
			self.task_double_pop_alert:SetOkFunc(BindTool.Bind1(self.LevelFB, self))
		end

		local str,ignore = FuBenWGData.Instance:GetLeaveFbStr()
		if not ignore and BossWGData.IsBossScene(scene_type) then
			local main_role = Scene.Instance:GetMainRole()
			if main_role and main_role:IsFightState() then
				str = str .. Language.Boss.ConfirmLevelFB1
			end
		elseif scene_type == SceneType.KF_DUCK_RACE then
			str = Language.DuckRace.LeaveTips
		elseif scene_type == SceneType.WorldsNO1Prepare then
			str = WorldsNO1WGData.Instance:GetPrepareLeaveDesc()
		elseif scene_type == SceneType.WorldsNO1 and not WorldsNO1WGData.Instance:IsObservationStatus() then
			str = Language.WorldsNO1.LeaveTips
		elseif scene_type == SceneType.WorldsNO1 and WorldsNO1WGData.Instance:IsObservationStatus() then
			str = Language.WorldsNO1.LeaveObservationTips
		elseif not RoleWGData.Instance:CheckCurServerIsOrigin() and scene_type == SceneType.Common then
			str = Language.CrossTreasure.LingzhuGatherErrorTips10
		end

		self.task_double_pop_alert:SetLableString(str)
		self.task_double_pop_alert:Open()
	end
end

-- 点击规则按钮
function MainUIView:OnClickStrategy()
	local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.Common then
    	return
	elseif scene_type == SceneType.Kf_PvP_Prepare then
		KF3V3WGCtrl.Instance:OpenTabRuleTip(30)
	elseif scene_type == SceneType.CROSS_FLAG_GRABBING_BATTLEFIELD then
		CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBExplainView(false, true)
    else
    	local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	    RuleTip.Instance:SetContent(fb_cfg.fb_desc, Language.TreasureLoft.TipsRollCardTitle)
    end
end

-- 点击跨服1v1
function MainUIView:OnClickKfPVP()
	local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.Kf_OneVOne_Prepare then
    	ViewManager.Instance:Open(GuideModuleName.ActJjc,TabIndex.arena_kf1v1)
    elseif scene_type == SceneType.Kf_PvP_Prepare then
    	--ViewManager.Instance:Open(GuideModuleName.ActJjc,TabIndex.arena_kf3v3)
		-- ViewManager.Instance:Open(GuideModuleName.KF3v3View, TabIndex.kf_pvp_3v3info)
		KF3V3WGCtrl.Instance:Open3V3View(TabIndex.kf_pvp_3v3info)
    end
end

function MainUIView:OnClickTodayActTog(isOn)
	-- if self.today_act_refresh_event then
	-- 	GlobalTimerQuest:CancelQuest(self.today_act_refresh_event)
	-- 	self.today_act_refresh_event = nil
	-- end

	if isOn then
		self:FlushTaskToDayActInfo()

		-- self.today_act_refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushTaskToDayActInfo, self), 10)

		-- self.node_list.btn_task_todayact.toggle.isOn = false
		-- ViewManager.Instance:Open(GuideModuleName.CalendarView)
	end

	CalendarWGData.Instance:SetOpened(isOn)
	RemindManager.Instance:Fire(RemindName.Calendar)
end

-- 点击等级礼包
function MainUIView:OnClickLevelGiftBtn()
	ViewManager.Instance:Open(GuideModuleName.Welfare, TabIndex.welfare_upgrade)
end



--------------------------
-- 【  特殊场景界面逻辑  】 --
--------------------------
-- 设置退出副本时间
function MainUIView:SetFbIconEndCountDown(out_fb_time, enable, title_type, not_show_end_countdown)
	--print_error("退出副本时间", out_fb_time, enable)
	self.out_fb_time = out_fb_time
	local scene_type = Scene.Instance:GetSceneType()
	self.out_fb_scene_type = scene_type
	if out_fb_time > 0 then
        self:RemoveLevelTimeCountDown()
		local prepare_time = FuBenWGData.Instance:GetSetFBPrepareTime()
		local server_time = TimeWGCtrl.Instance:GetServerTime()
        local scence_info = FuBenWGData.Instance:GetFbSceneLogicInfo()
        local flush_timestamp = scence_info.flush_timestamp or 0
        local show_time_text = server_time > prepare_time
        local show_level_time_text = false

		if SHOW_LEVEL_TIME_TYPE[scene_type] then
			show_level_time_text = server_time > prepare_time
		elseif scene_type == SceneType.BaGuaMiZhen_FB
			and server_time < flush_timestamp then
			show_time_text = false
		elseif scene_type == SceneType.HunYanFb then
			show_time_text = false
			show_level_time_text = true
		elseif scene_type == SceneType.ZHUXIE then
			show_time_text = false
		elseif scene_type == SceneType.QingYuanFB then
            show_time_text = true
        elseif scene_type == SceneType.GuildBoss then
			show_time_text = false
			show_level_time_text = true
		elseif scene_type == SceneType.Kf_Honorhalls
			or scene_type == SceneType.ETERNAL_NIGHT
			or scene_type == SceneType.ETERNAL_NIGHT_FINAL
			or scene_type == SceneType.CROSS_AIR_WAR then
			show_time_text = false
			show_level_time_text = server_time > prepare_time
		end

		self:SetShowTimeTextState(show_time_text)
		self:SetLevelBtnTimeTextState(show_level_time_text)

		if nil == self.level_time_countdown then
			local time = out_fb_time - server_time
			self:UpdateLevelTime(enable, title_type, not_show_end_countdown, 0, time)
			self.level_time_countdown = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateLevelTime, self, enable, title_type, not_show_end_countdown), 1)
		end
		self:UpdateSpecialLogic()
	else
		self:ClearTimeText()
		self:SetShowTimeTextState(false)
		self:SetLevelBtnTimeTextState(false)
		self:CloseLevelFBAlert()
	end
end

function MainUIView:CloseLevelFBAlert()
	if self.task_double_pop_alert and self.task_double_pop_alert:IsOpen() then
		self.task_double_pop_alert:Close()
	end
end

function MainUIView:IsCountDowning()
	return self.level_time_countdown ~= nil
end

function MainUIView:RemoveLevelTimeCountDown()
	GlobalTimerQuest:CancelQuest(self.level_time_countdown)
	self.level_time_countdown = nil
	self:SetLevelBtnTimeTextState(false)
end

function MainUIView:SetLevelBtnTimeTextState(state)
	if self.node_list["levelbtn_time_text"] then
		self.node_list["levelbtn_time_text"]:SetActive(state)
	end
end

-- 设置退出倒计时的显隐
function MainUIView:SetShowTimeTextState(state)
	if self.node_list.fb_time2 then
		self.node_list.fb_time2:SetActive(state)
	end
end

-- 设置退出倒计时的坐标
function MainUIView:SetFbTimePos(x,y)
	if self.node_list.fb_time2 then
		self.node_list.fb_time2.transform.anchoredPosition = u3dpool.vec2(x,y)
	end
end

function MainUIView:ClearTimeText()
	self.node_list.level_time.text.text = ""
	self.node_list["level_time_less"].text.text = ""
	self.node_list["levelbtn_time_text"].text.text = ""
end

function MainUIView:UpdateSpecialLogic()
	self.node_list["level_time_tips_top"].text.text = ""
	self.node_list["level_time_tips_top"]:SetActive(false)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ManHuangGuDian_FB then
		self.node_list["level_time_tips_top"].text.text = Language.FuBenPanel.ManHuangCountDownDesc
		self.node_list["level_time_tips_top"]:SetActive(true)
	elseif scene_type == SceneType.FengShenBang then
		self.node_list["level_time_tips_top"].text.text = Language.FuBenPanel.TimeTips
		self.node_list["level_time_tips_top"]:SetActive(true)
	end
end

function MainUIView:UpdateLevelTime(enable, title_type, not_show_end_countdown, elapse_time, total_time)
	local time = self.out_fb_time - TimeWGCtrl.Instance:GetServerTime()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	local secne_type = Scene.Instance:GetSceneType()
    local offset = (secne_type == SceneType.HotSpring or secne_type == SceneType.KF_HotSpring) and u3dpool.vec2(0, -55) or u3dpool.vec2(0, 0)
	if time < 4 and time >= 2 then
		if scene_cfg.pb_kick_out ~= 1 or not self:CanShieldCountDown() then
			if title_type then
				FuBenPanelWGCtrl.Instance:SetCountDowmType(nil, title_type)
			end
			if not not_show_end_countdown and Field1v1WGCtrl.Instance:CheckIsCanOut() then --准备场景在准备时间内
				FuBenPanelCountDown.Instance:SetTimerInfo(self.out_fb_time,function ()
					if not enable then
						self:LevelFB()
					end
				end, offset)
			end
		end
    end

    local time_str = TimeUtil.FormatSecond(math.floor(time), 2)
    if secne_type == SceneType.HUNDRED_EQUIP or scene_cfg.is_center_countdown == 1 then
        if time < 60 then
            self.node_list["level_time_less"]:CustomSetActive(true)
            self.node_list["level_time"]:CustomSetActive(false)
        else
            self.node_list["level_time"]:CustomSetActive(true)
            self.node_list["level_time_less"]:CustomSetActive(false)
        end
        self.node_list["level_time_bg"]:SetActive(true)
    else
        self.node_list["level_time"]:CustomSetActive(false)
        self.node_list["level_time_less"]:CustomSetActive(false)
        self.node_list["level_time_bg"]:SetActive(false)
    end

	self.node_list["levelbtn_time_text"].text.text = time_str
	self.node_list["level_time_less"].text.text = time_str
	self.node_list["level_time"].text.text = time_str
	if time < 1 or self.out_fb_scene_type ~= secne_type then
    	self:RemoveLevelTimeCountDown()
    end
end

function MainUIView:CanShieldCountDown()
	return true
end

-- is_click 是否主动点击离开
function MainUIView:LevelFB(is_click)
	self:RemoveLevelTimeCountDown()
	if is_click then
		-- 手动退出副本清除缓存的挂机和自动任务标记
		SceneWGData.Instance:ClearCommonSceneGuajiTypeAndAutoTask()
		FuBenWGData.Instance:SetFuBenInitiativeToLeave(Scene.Instance:GetSceneType(), true)
	end

	if Scene.Instance:GetSceneType() == SceneType.Kf_PVP
		or Scene.Instance:GetSceneType() == SceneType.TianShen3v3
		or Scene.Instance:GetSceneType() == SceneType.WorldsNO1 then
		Field1v1WGCtrl.Instance:LeaveZhanChangScene()
		return
	end

	if Scene.Instance:GetSceneType() == SceneType.ETERNAL_NIGHT or Scene.Instance:GetSceneType() == SceneType.ETERNAL_NIGHT_FINAL then
		GuajiWGCtrl.Instance:StopGuaji()
	end

	if IS_ON_CROSSSERVER then
		if Scene.Instance:GetSceneType() == SceneType.Kf_OneVOne_Prepare or
			Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare or
			Scene.Instance:GetSceneType() == SceneType.CROSS_LIEKUN or
			Scene.Instance:GetSceneType() == SceneType.CROSS_AIR_WAR or
			BossWGData.IsBossScene(Scene.Instance:GetSceneType()) then
			FuBenWGCtrl.Instance:SendCSLeaveFB()
		else
			if not RoleWGData.Instance:CheckCurServerIsOrigin() and Scene.Instance:GetSceneType() == SceneType.Common then
				CountryMapWGCtrl.Instance:SendReturnToOriginalServer(nil)
			end
		end
		CrossServerWGCtrl.ConnectBack()
	else
		self:CheckAutoTask()
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
end

function MainUIView:CheckAutoTask()
	if Scene.Instance:GetSceneType() == SceneType.Common then
		return
	end

	local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
	if fuben_data and 0 == fuben_data.is_finish then
		TaskGuide.Instance:CanAutoAllTask(false)
	end

	local story_fb_info = FuBenWGData.Instance:GetFbSceneLogicInfo()
	if not IsEmptyTable(story_fb_info) and 1 ~= story_fb_info.is_pass then
		TaskGuide.Instance:CanAutoAllTask(false)
	end
end

-- 设置离开副本按钮状态
function MainUIView:SetBtnLevel(enable)
	if not self.node_list.level_bg then
		return
	end

	self.node_list.level_bg:SetActive(enable)
	if enable then
		self.node_list.FbIcon:SetActive(true)
		self.node_list.level_bg.canvas_group.alpha = 1
		self.node_list.fb_time2.canvas_group.alpha = 1
		self.node_list.level_bg.canvas_group.blocksRaycasts = true
	end

    local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_cfg.tips_icon_show == 1 then
        self.node_list.btn_strategy:SetActive(true)
        self.node_list.btn_strategy.canvas_group.alpha = 1
        self.node_list.btn_strategy.canvas_group.blocksRaycasts = true
    else
        self.node_list.btn_strategy:SetActive(false)
    end
end

--任务发生改变（npc处于完成任务状态/npc处于可接任务状态等）
function MainUIView:OnTaskChange(task_event_type, task_id)
	self:SetZuoqiBtnState()

	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if not task_cfg then
		return
	end

	local cfg_task_id = ConfigManager.Instance:GetAutoConfig("roleexp_auto").other[1].task_id
	if task_event_type ==TASK_EVENT_TYPE.COMPLETED and task_id == cfg_task_id then
		self:TryOpenNewSerRecTip()
	end

	if task_event_type == TASK_EVENT_TYPE.COMPLETED then
		if self.dragon_trial_act_info then
			local cfg = FunOpen.Instance:GetFunByName(FunName.DragonTrialView)
			if cfg and cfg.trigger_param == task_id then
				self:activityChangeCallBack(self.dragon_trial_act_info.act_type, self.dragon_trial_act_info.status, self.dragon_trial_act_info.end_time, self.dragon_trial_act_info.open_type)
				self.dragon_trial_act_info = nil
			end
		end
	end
end

function MainUIView:AddObjToMainUI(obj, mainui_root_str, sibling_index)
	if not self.node_list or not mainui_root_str or not self.node_list[mainui_root_str] then
		return
	end

	obj.transform:SetParent(self.node_list[mainui_root_str].transform, false)
	obj.transform:SetSiblingIndex(sibling_index or 1)
end

function MainUIView:AddBtnToFbIconGroup1Line(obj, sibling_index)
	if not self.node_list or not self.node_list.FbIconGroup1 then
		return
	end

	obj.transform:SetParent(self.node_list.FbIconGroup1.transform, false)
	obj.transform:SetSiblingIndex(sibling_index or 1)
end

function MainUIView:AddBtnToFbIconGroup2Line(obj, sibling_index)
	if not self.node_list or not self.node_list.FbIconGroup2 then
		return
	end

	obj.transform:SetParent(self.node_list.FbIconGroup2.transform, false)
	obj.transform:SetSiblingIndex(sibling_index or 1)
end

function MainUIView:AddBtnToFbRightLineGroup(obj, sibling_index)
	if not self.node_list or not self.node_list.fb_right_line_group then
		return
	end

	obj.transform:SetParent(self.node_list.fb_right_line_group.transform, false)
	obj.transform:SetSiblingIndex(sibling_index or 1)
end

-- 离开按钮动画
function MainUIView:DoLevelBtnAni()
	local isOn = self:GetShrinkButtonToggleIsOn()

	if not IS_ON_CROSSSERVER
	and Scene.Instance:GetSceneType() == SceneType.Common
	and Scene.Instance:GetSceneId() ~= BootyBayWGData.Instance:GetBootyBaySceneId() then
		-- 出现了离开副本场景 副本内按钮却并没有被隐藏的情况
		self.node_list.FbIcon.canvas_group.blocksRaycasts = false
		if self.do_level_btn_tween then
			self.do_level_btn_tween:PlayBackwards()
			self.do_level_btn_tween_child:PlayBackwards()
		else
			-- 隐藏FbIcon
			self.node_list["FbIcon"].canvas_group.alpha = 0
			self.node_list.FbIcon.canvas_group.blocksRaycasts = false
			-- RectTransform.SetAnchoredPositionXY(self.node_list.FbIcon.rect, -1000, -111)
			RectTransform.SetAnchoredPositionXY(self.node_list.FbIcon.rect, 31, -111)
		end

		return
	end

	self.node_list.FbIcon.canvas_group.blocksRaycasts = not isOn
	if not self.do_level_btn_tween then
		local time = 0.5
		local tweener = DG.Tweening.DOTween.Sequence()
		-- 显示
		local fb_icon_root = self.node_list.FbIcon
		local fb_icon_time = isOn and 0.3 or 0.5
		RectTransform.SetAnchoredPositionXY(fb_icon_root.rect, 31, -111)
		local move_tween = fb_icon_root.transform:DOAnchorPos(Vector2(81, -111), fb_icon_time)
		local alpha_tween = fb_icon_root.canvas_group:DoAlpha(0, 1, fb_icon_time)

		tweener:Append(move_tween)
		tweener:Join(alpha_tween)
		tweener:SetEase(DG.Tweening.Ease.InOutBack)
		tweener:SetAutoKill(false)
		tweener:Pause()
		self.do_level_btn_tween = tweener
		

		self.do_level_btn_tween_child = self.node_list["fb_time2"].canvas_group:DoAlpha(0, 1, time)
		self.do_level_btn_tween_child:SetEase(DG.Tweening.Ease.InOutBack)
		self.do_level_btn_tween_child:SetAutoKill(false)
		self.do_level_btn_tween_child:Pause()
	end

	if self.do_level_btn_tween then
		if isOn then
			self.do_level_btn_tween:PlayBackwards()
			self.do_level_btn_tween_child:PlayBackwards()
		else
			self.do_level_btn_tween:PlayForward()
			self.do_level_btn_tween_child:PlayForward()
		end
	end
end

-- 将obj塞进挂机那一排
function MainUIView:AddBtnToGuajiLine(obj, sibling_index)
	if not self.node_list or not self.node_list.guaji_line_group then
		return
	end
	
	obj.transform:SetParent(self.node_list.guaji_line_group.transform, false)
	obj.transform:SetSiblingIndex(sibling_index or 1)
end

function MainUIView:ActivitySceneLoadingQuite(old_scene_type, new_scene_type)
	-- 策划需求，干掉日月修行的鼓舞按钮
	self.node_list.btn_inspire:SetActive(new_scene_type == SceneType.Wujinjitan
		or new_scene_type == SceneType.Guild_Invite
		or new_scene_type == SceneType.GuildMiJingFB
		or new_scene_type == SceneType.BOSS_INVASION)
end

--------------------------
-- 【单个按钮特殊显示逻辑】 --
--------------------------
----------- 分包下载 -------------
-- 设置分包下载按钮
function MainUIView:SetSubPackageIcon(is_show)
	if IS_AUDIT_VERSION then
		MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnSubPackage", false)
		return
	end

	if SubPackageWGCtrl.Instance:GetPackageCount() <= 0 then
		return
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SUB_PACKAGE, is_show and 1 or 0, function ()
		ViewManager.Instance:Open(GuideModuleName.SubPackageView)
		return true
	end)
end

-- 设置分包下载按钮气泡显示隐藏
function MainUIView:SetSubPackageQiPaoActice(is_show_qipao)
	if IS_AUDIT_VERSION then
		is_show_qipao = false
	end

	-- self.node_list.SubPackageQiPao:SetActive(is_show_qipao)
end
----------- 分包下载 end----------

----------- 七天登录 -------------
-- 设置七天登录按钮
function MainUIView:SetSevenDayStatus()
	self.seven_day_is_over = RoleWGData.GetRolePlayerPrefsInt("seven_day_over")
	if self.seven_day_is_over == 1 then
		MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnSeventDayView", false)
	end
end

-- 设置洪荒直购按钮
function MainUIView:SetChaoticPurchaseStatus()
	local is_all_buy = ChaoticPurchaseWGData.Instance:GetIsAllBuy()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("ChaoticPurchaseView")
	if is_open then
		MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnChaoticPurchase", not is_all_buy)
	end
end

-- 设置洪荒好礼按钮
function MainUIView:SetHongHuangGoodCoremonyStatus()
	local is_all_buy = HongHuangGoodCoremonyWGData.Instance:GetIsAllBuy()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("HongHuangGoodCoremonyView")
	if is_open then
		MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnHongHuangGoodCoremonyView", not is_all_buy)
	end
end

-- 设置洪荒庆典按钮
function MainUIView:SetHongHuangClassicStatus()
	local is_all_buy = HongHuangClassicWGData.Instance:GetIsAllBuy()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("HongHuangClassicView")
	if is_open then
		MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnHongHuangClassicView", not is_all_buy)
	end
end

-- 设置vip特典按钮
function MainUIView:SetChaoticVipspecialStatus()
	local is_all_buy = ChaoticVipSpecialWGData.Instance:AllBuyState()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("ChaoticVipSpecialView")
	if is_open then
		MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnChaoticVipspecial", not is_all_buy)
	end
end

-- 设置下载有礼显示按钮
function MainUIView:SetDownLoadWebStatus()
	local state = DownLoadWebWGData.Instance:GetDownLoadBtnState()
	MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnDownLoadWeb", state)
end

function MainUIView:OnClicklotteryClose()
	self:SetLotteryCollectPaneStatus(false)
end

function MainUIView:SetLotteryCollectPaneStatus(state, collect_panel_name)
	if state then
		if self.node_list[collect_panel_name] then 
			self.node_list[collect_panel_name]:SetActive(true)
		end
	else
		if self.node_list["lottery_collect_panel"] then
			self.node_list["lottery_collect_panel"]:SetActive(state)
		end

		if self.node_list["honghuang_collect_panel"] then
			self.node_list["honghuang_collect_panel"]:SetActive(state)
		end

		if self.node_list["qifu_collect_panel"] then
			self.node_list["qifu_collect_panel"]:SetActive(state)
		end
	end
end

-- 检测功能活动集合是否只开启了一个，只开启了一个
function MainUIView:CheckCollectPanelIsOpenOneAct(data)
	if data.open_fun == nil then
		return false, nil
	end

	local open_count = 0
	local open_data = nil

	for i, v in ipairs(data.open_fun) do
		local fun_name = FunOpen.Instance:GetFunNameByViewName(v.view_name)
		local is_open = FunOpen.Instance:GetFunIsOpened(fun_name)
		local other_open = self:CheckCollectPanelIsOpenOneActByOther(v.view_name)
		local btn = self:GetOneSundryNode(v.btn_name)


		if is_open and other_open then
			open_count = open_count + 1
			open_data = v

			if btn ~= nil then
				btn.canvas_group.alpha = 1
				btn.canvas_group.blocksRaycasts = true
				btn.canvas_group.interactable = true
			end
		end
	end

	return open_count == 1, open_data, open_count
end

-- 检测功能开启得其他条件
function MainUIView:CheckCollectPanelIsOpenOneActByOther(view_name)
	if view_name == GuideModuleName.HongHuangClassicView then
		return not HongHuangClassicWGData.Instance:GetIsAllBuy()	
	elseif view_name == GuideModuleName.ChaoticVipSpecialView then
		return not ChaoticVipSpecialWGData.Instance:AllBuyState()
	elseif view_name == GuideModuleName.HongHuangGoodCoremonyView then
		return not HongHuangGoodCoremonyWGData.Instance:GetIsAllBuy()
	elseif view_name == GuideModuleName.ChaoticPurchaseView then
		return not ChaoticPurchaseWGData.Instance:GetIsAllBuy()
	end

	return true
end

-- 刷新七天登录按钮
function MainUIView:FlushSevenDayIcon()
	--文本,图片(七天登录)
	local is_show_seven_effect = WelfareWGData.Instance:IsShowSevenServerRedPoint() == 1
	if self.node_list["SevenDayEff"] then
		self.node_list["SevenDayEff"]:SetActive(is_show_seven_effect)
	end

	local seven_day_obj = self.node_list["BtnSeventDayView"]
	local index1 = WelfareWGData.Instance:GetSevenDayDefaultIndex()
	local data_list = WelfareWGData.Instance:GetEightSevenDayCfg()
	local item_status
	local item_show_ID
	local item_name
	if data_list[index1] then
		item_status = data_list[index1].status
		item_show_ID = data_list[index1].show_ID2
		item_name = data_list[index1].show_name
	end

	if seven_day_obj.Effect then
		seven_day_obj.Effect.gameObject:SetActive(item_status == SEVEN_COMPLETE_STATUS.KELINGQU)
	end
end
----------- 七天登录 end-------------

----------- 日历 --------------------
function MainUIView:CanShowCalendar()
	local scene_type = Scene.Instance:GetSceneType()
	local scene_cfg = MainuiWGCtrl.Instance:GetFbSceneCfg(scene_type)
	local is_show = scene_cfg and scene_cfg.is_show_calendar == 1 and FunOpen.Instance:GetFunIsOpened(FunName.CalendarView)
	return is_show
end

-- 刷新日历
function MainUIView:FlushCalendar()
	if self.node_list.btn_task_todayact then
		local cfg = CalendarWGData.Instance:GetCalendarActivityCfg()
		local last_openning_cfg = nil
		local index = 1
		if cfg and cfg[index] and self:CanShowCalendar() then
			self.node_list.btn_task_todayact:SetActive(true)
		else
			self.node_list.btn_task_todayact:SetActive(false)
			self.node_list.btn_task_todayact.toggle.isOn = false
		end
	end
end

-- 刷新等级礼包.
function MainUIView:FlushLevelGiftWelfareUpgrade()
	local is_show, next_get_lv = WelfareWGData.Instance:CheckWelfareLevelCanGetGift()
	local can_get = WelfareWGData.Instance:IsShowWelfareLevelOrVipGiftRedPoint(GITT_LINGQU_TYPE.UPGRADE) == 1

	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	self.node_list.btn_level_gift_tips:SetActive(is_show and scene_cfg.is_show_lv_gift == 0)
	self.node_list.btn_level_gift_tips_red:SetActive(can_get)
	
	self.node_list.btn_bubble_tips_text.tmp.text = can_get and Language.Welfare.SjLevelTitle or string.format(Language.Welfare.LevelGiftOffset2, next_get_lv)
end

-- 刷新龙玺提醒
function MainUIView:FlushLongXINotice()
	local state = LongXiWGData.Instance:GetLongXINoticeFlag()
	local seal_state = FunOpen.Instance:GetFunIsOpened(FunName.SuperDragonSeal)
	local token_state = FunOpen.Instance:GetFunIsOpened(FunName.DragonKingToken) 

	if self.node_list["longxi_notice"] then
		if state and seal_state and token_state then
			self.node_list["longxi_notice"]:SetActive(true)
		else
			self.node_list["longxi_notice"]:SetActive(false)
		end
	end
end

----------- 日历 end------------------

------------ 神机 --------------------
function MainUIView:ShenJiNoticeBtnRelease()
	if self.shenji_notice_btn then
		self.shenji_notice_btn:DeleteMe()
		self.shenji_notice_btn = nil
	end
	MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnShenJiNotice", false)
end

--单独管理这个神机预告btn
function MainUIView:ShenJiNoticeBtnInit()
	if self.shenji_notice_btn == nil then
		self.shenji_notice_btn = ShenJiActBtn.New(self.node_list["BtnShenJiNotice"])
	end
end

function MainUIView:ShenJiNoticeBtnFlush()
	local is_all_task_fetched = ShenJiNoticeWGData.Instance:IsAllTaskFetched()
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.ShenJiNotice)
	local is_show = not is_all_task_fetched and is_open
	MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnShenJiNotice", is_show)
	if self.shenji_notice_btn then
		self.shenji_notice_btn:Flush()
	end
end

function MainUIView:BaiLianZhaoHuanMainBtnInit()
	if self.node_list["BtnSiXiangCallView"] then
		self.bailian_zhaohuan_mainbtn = BaiLianZhaoHuanMainBtn.New(self.node_list["BtnSiXiangCallView"])
	end
end

function MainUIView:BaiLianZhaoHuanMainBtnRelease()
	if self.bailian_zhaohuan_mainbtn then
		self.bailian_zhaohuan_mainbtn:DeleteMe()
		self.bailian_zhaohuan_mainbtn = nil
	end
end

function MainUIView:BaiLianZhaoHuanMainBtnFlush()
	if self.bailian_zhaohuan_mainbtn then
		self.bailian_zhaohuan_mainbtn:Flush()
	end
end

----------------------------灵核抽奖------------------
function MainUIView:LingHeDrawMainBtnInit()
	if self.node_list["BtnTianShenLingHeDraw"] then
		self.linghe_draw_mainbtn = LingHeDrawMainBtn.New(self.node_list["BtnTianShenLingHeDraw"])
	end
end

function MainUIView:LingHeDrawMainBtnRelease()
	if self.linghe_draw_mainbtn then
		self.linghe_draw_mainbtn:DeleteMe()
		self.linghe_draw_mainbtn = nil
	end
end

function MainUIView:LingHeDrawMainBtnFlush()
	if self.linghe_draw_mainbtn then
		self.linghe_draw_mainbtn:Flush()
	end
end

--------------------------神技天赐--------------------

function MainUIView:ShenJiTianCiMainBtnInit()
	if self.node_list["BtnShenJiTianCiView"] then
		self.shenji_tianci_mainbtn = ShenJiTianCiMainBtn.New(self.node_list["BtnShenJiTianCiView"])
	end
end

function MainUIView:ShenJiTianCiMainBtnRelease()
	if self.shenji_tianci_mainbtn then
		self.shenji_tianci_mainbtn:DeleteMe()
		self.shenji_tianci_mainbtn = nil
	end
end

function MainUIView:ShenJiTianCiMainBtnFlush()
	if self.shenji_tianci_mainbtn then
		self.shenji_tianci_mainbtn:Flush()
	end
end

------------ 神机 end------------------

-------------------------- 幻兽特惠&限定 ----------------------
function MainUIView:BeastsContractBtnInit()
	if self.node_list.BtnBeastsContract then
		self.beasts_contract_mainbtn = BeastsContractMainBtn.New(self.node_list.BtnBeastsContract)
	end
end

function MainUIView:BeastsContractBtnRelease()
	if self.beasts_contract_mainbtn then
		self.beasts_contract_mainbtn:DeleteMe()
		self.beasts_contract_mainbtn = nil
	end
end

function MainUIView:BeastsContractBtnFlush()
	if self.beasts_contract_mainbtn then
		self.beasts_contract_mainbtn:Flush()
	end
end
-------------------------- 幻兽特惠&限定end ----------------------

-- -- 等级礼包
-- function MainUIView:FlushLevelGift()
--     if self.node_list["BtnLevelGift"] == nil then
--     	return
--     end

--     local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
--     local is_show = fb_scene_cfg and fb_scene_cfg.pb_levelgiftgtn == 0
--     if is_show then
--         local can_show, offset_lv = WelfareWGData.Instance:CanShowLevelGiftBtn()
--         if can_show then
--             local first_gift, gift_item = WelfareWGData.Instance:GetLevelGiftsListFirst()
--             --self.node_list["levelegift_red_point"]:SetActive(false)
--             if first_gift then
--                 if not self.main_gift_item_id or self.main_gift_item_id ~= gift_item.item_id then
--                     self.main_gift_item_id = gift_item.item_id
--                     local icon_id = ItemWGData.Instance:GetItemIconByItemId(gift_item.item_id)
--                    --local bundle, asset = ResPath.GetItem(icon_id)
--                    -- self.node_list["level_gift_icon"].image:LoadSpriteAsync(bundle, asset)
--                 end

--                 --self.node_list["levelgift_name"].text.text = string.format(Language.Welfare.LevelGift, first_gift.level_limit)
--                 if offset_lv == 0 then
--                    -- self.node_list["levelgift_des"].text.text = Language.Welfare.LevelGiftCanGet
--                     --self.node_list["levelegift_red_point"]:SetActive(true)
--                 else
--                    -- self.node_list["levelgift_des"].text.text = string.format(Language.Welfare.LevelGiftOffset, offset_lv)
--                 end

--                 self.node_list["BtnLevelGift"]:SetActive(not IS_AUDIT_VERSION)
--                 if not self.can_show_level_gift and can_show then
--                     UITween.CleanMoveAlphaShow("main_view_level_gift")
--                     --UITween.FakeHideShow(self.node_list["BtnLevelGift"])
--                     --UITween.MoveAlphaShow("main_view_level_gift", self.node_list["BtnLevelGift"], UITween_CONSTS.MainViewLevelGiftTween)
--                     self.can_show_level_gift = can_show
--                 end

--                 return
--             end
--         end
--     end

-- 	self.can_show_level_gift = false
-- 	self.node_list["BtnLevelGift"]:SetActive(false)
-- end

-- -- 刷新版本预告
-- function MainUIView:FlushVersionsAdvanceNoticeBtn(param_t)
--     if IsEmptyTable(param_t) then
--     	return
--     end

--     MainuiWGCtrl.Instance:SetGuoJiaYuGaoStatus(param_t.is_show, param_t.red_num)
-- end

-- 拍卖提醒
function MainUIView:SetAuctionBtnInfo()
	local auction_type = 2 -- 世界拍卖
	local round = AuctionTipsWGData.Instance:CheckInPeriodBuType(auction_type)
	local old_round = AuctionTipsWGData.Instance:GetCurRound()
	if old_round ~= round then
		AuctionTipsWGData.Instance:SetCurRound(round)
		if round ~= 0 then
			ViewManager.Instance:Open(GuideModuleName.AuctionTipsView)
		end
	end
	self.node_list["BtnAuctionTips"]:SetActive(round ~= 0)
end

--红包雨.
function MainUIView:SetRedPacketRainBtnInfo()
	self.node_list["BtnRedPacketRain"]:SetActive(RedPacketRainWGData.Instance:IsOpenItemAcitivity())
end

function MainUIView:OnClickRedPacketRainBtn()
	local scene_type = Scene.Instance:GetSceneType()
	if not YunbiaoWGData.Instance:GetIsHuShong() and not RedPacketRainWGData.Instance:IsInRedPacketArea() then
		if scene_type == SceneType.Common then
			-- 进入神龙红包场景范围
			RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ENTER)
		else
			TipWGCtrl.Instance:OpenAlertTips(Language.RedPacketRain.GoToArea,function ()
				RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ENTER)
			end,nil,nil,nil,5,nil,Language.RedPacketRain.Join)
		end
		
	end
end

-- 结婚提醒
function MainUIView:SetMarryNoticeBtnInfo()
	local cur_states = MarryWGData.Instance:GetCurNoticeStates()
	if self.node_list["marry_notice_icon"] then
		-- 2021/9/24 屏蔽仙娃降临，只显示情缘预告
		local is_get_reward = MarryWGData.Instance:GetCurNoticeRewardFlag() > 0
		if (cur_states ~= QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY)
				or is_get_reward then
			self.node_list["BtnMarryNotice"]:SetActive(false)
			return
		else
			-- local icon_name = "a2_zjm_icon_tcxy"
			-- if cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY then
			-- 	icon_name = "a2_zjm_icon_tcxy"
			-- elseif cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_BABY then
			-- 	-- icon_name = "a1_btn_baby_notice"	--策划说的先用这一个图标，有问题找策划
			-- 	icon_name = "a2_zjm_icon_tcxy"
			-- end
	
			-- self.node_list["marry_notice_icon"].image:LoadSprite(ResPath.GetF2MainUIImage(icon_name))
			self.node_list["BtnMarryNotice"]:SetActive(true and not IS_AUDIT_VERSION)
		end
	end
end

--刷新跨服1v1/3v3准备场景按钮的红点
function MainUIView:FlushKfPKRenPoint()
	if not self:IsLoaded() then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	-- if scene_type == SceneType.Kf_OneVOne_Prepare then
	-- 	self.node_list.btn_kfonevone.image:LoadSprite(ResPath.GetMainUIIcon("a2_zjm_icon_dfjj"))
	-- 	local is_show_point_1 = Field1v1WGData.Instance:IsShowActOneVSOneJiFenbArenaRedPoint()
	-- 	local is_show_point_2 = KFOneVOneWGData.Instance:IsShowKFOneVOneRedPoint()
	-- 	self.node_list.btn_kf_pk_red_point:SetActive(is_show_point_1 == 1 or is_show_point_2 == 1)
	-- else
	if scene_type == SceneType.Kf_PvP_Prepare then
		local is_show_point_1 = KF3V3WGData.Instance:IsShow3V3RedPoint()
		--local is_show_point_2 = Field1v1WGData.Instance:IsShowActPVPJiFenbArenaRedPoint()

		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.KF_PVP)
		if act_cfg and act_cfg.res_name then
			self.node_list.btn_kfonevone.image:LoadSprite(ResPath.GetMainUIIcon(act_cfg.res_name))
			self.node_list.btn_kf_pk_red_point:SetActive(is_show_point_1 == 1)
		end
	end
end

-- 添加活动图标创建前刷新缓存
function MainUIView:AddWaitActivityBtnCreatFlush(act_type, key, parma_t)
	if not act_type or not key then
		return
	end

	if not self.activity_btn_wait_flush_list[act_type] then
		self.activity_btn_wait_flush_list[act_type] = {}
	end

	-- 注意值覆盖
	self.activity_btn_wait_flush_list[act_type][key] = parma_t
end

-- 设置预告按钮Icon
function MainUIView:FlushSystemForceIcon()
	local sys_force_btn = self.node_list["BtnSystemPreview"]
	local icon_node = sys_force_btn.Icon
	local icon_show_cfg = SystemForceWGData.Instance:GetCurIconShowCfg()

	if not IsEmptyTable(icon_show_cfg) then
		if icon_node then	
			local icon_bundle, icon_asset = ResPath.GetF2MainUIImage(icon_show_cfg.icon)
			icon_node.image:LoadSprite(icon_bundle, icon_asset, function()
				icon_node.image:SetNativeSize()
			 end)
		end
	
		local effect = sys_force_btn.transform:FindByName("effect")
		if effect then
			local attach = effect.gameObject:GetComponent(typeof(Game.GameObjectAttach))
			local bundle_name, asset_name = ResPath.GetA2Effect(icon_show_cfg.icon_effect)
			attach.BundleName = bundle_name
			attach.AssetName = asset_name
			effect.gameObject:SetActive(true)
		end
	end
end

-------------------------------功能盒子---------------------------------
function MainUIView:FuncBoxActStateChangeCallBack(act_type, status, end_time, open_type, act_cfg)
	if status == ACTIVITY_STATUS.CLOSE then
		MainuiWGData.Instance:RemoveFuncBoxCache(act_type)
	else
		MainuiWGData.Instance:AddFuncBoxCache(act_type, {act_type = act_type, status = status, end_time = end_time, open_type = open_type, act_cfg = act_cfg})
	end

	self:FlushFuncBoxState()
end

function MainUIView:FlushFuncBoxState()
	if self.node_list.btn_func_box then
		local mainui_func_box_cache = MainuiWGData.Instance:GetFuncBoxDataCache()
		local has_data = not IsEmptyTable(mainui_func_box_cache)
		self.node_list.btn_func_box:CustomSetActive(has_data)

		if has_data then
			self:FlushFuncBoxBtnInfo()
		else
			self:CloseMainUIFuncBoxView()
		end 
	end
end

function MainUIView:FlushFuncBoxBtnInfo()
	if CountDownManager.Instance:HasCountDown("FlushFuncBoxBtnInfo") then
		CountDownManager.Instance:RemoveCountDown("FlushFuncBoxBtnInfo")
	end

	local mainui_func_box_cache = MainuiWGData.Instance:GetFuncBoxDataCache()
	local data = mainui_func_box_cache[1]

	if not IsEmptyTable(data) then
		local act_cfg, act_type = self:GetActCfgByActType(data.act_type, data.open_type)
		if not IsEmptyTable(act_cfg) then
			self.node_list.desc_func_box_name.text.text = act_cfg.name or ""
			local state_str = Language.Activity.KaiQiZhong

			if data.status == ACTIVITY_STATUS.STANDY then
				local time_tab = os.date("*t", data.end_time)
				if time_tab.min % 10 == 9 then
					time_tab.min = time_tab.min + 1
					if time_tab.min >= 60 then
						time_tab.min = 0
						time_tab.hour = time_tab.hour + 1
					end
				elseif time_tab.min % 10 == 1 then
					time_tab.min = time_tab.min - 1
				end

				self.node_list.desc_func_box_state.text.text = string.format("%d:%02d%s", time_tab.hour, time_tab.min, Language.Activity.KaiQi)
			elseif data.status == ACTIVITY_STATUS.OPEN or data.status == ACTIVITY_STATUS.XUNYOU then
				if act_cfg.show_time == 1 and data.end_time > TimeWGCtrl.Instance:GetServerTime() then
					-- 需要开启倒计时
					CountDownManager.Instance:AddCountDown("FlushFuncBoxBtnInfo", 
					function (elapse_time, total_time)
						if self.node_list.desc_func_box_state then
							local time = CountDownManager.Instance:GetRemainTime("FlushFuncBoxBtnInfo")
							local time_str = MainuiWGData.Instance:ConvertMainTopBtnTime(time)
							self.node_list.desc_func_box_state.text.text = ToColorStr(time_str, COLOR3B.GREEN)
						end
					end, 
					function ()
						self:FlushFuncBoxBtnInfo()
					end, data.end_time, nil, 1)
				else
					self.node_list.desc_func_box_state.text.text = Language.Activity.KaiQiZhong
				end
			else
				self.node_list.desc_func_box_state.text.text = Language.Activity.KaiQiZhong
			end
		end
	end
end

function MainUIView:OnClickFuncBox()
	self:OpenMainUIFuncBoxView()
end

function MainUIView:OpenMainUIFuncBoxView()
	if self.main_ui_func_box_view and not self.main_ui_func_box_view:IsOpen() then
		self.main_ui_func_box_view:Open()
	end
end

function MainUIView:CloseMainUIFuncBoxView()
	if self.main_ui_func_box_view and self.main_ui_func_box_view:IsOpen() then
		self.main_ui_func_box_view:Close()
	end
end

function MainUIView:CheckFuncBoxBtnRemind()
	local is_show_remind = false
	
	local mainui_func_box_cache = MainuiWGData.Instance:GetFuncBoxDataCache()
	if not IsEmptyTable(mainui_func_box_cache) then
		for act_id, data in pairs(mainui_func_box_cache) do
			if ActRemindList[data.act_type] then
				local remind_num = RemindManager.Instance:GetRemind(ActRemindList[data.act_type])
				
				if remind_num > 0 then
					is_show_remind = true
					break
				end
			end
		end
	end

	if self.node_list.btn_func_box_remind then
		self.node_list.btn_func_box_remind:CustomSetActive(is_show_remind)
	end
end