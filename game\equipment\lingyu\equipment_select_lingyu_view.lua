EquipmentSelectLingYuView = EquipmentSelectLingYuView or BaseClass(SafeBaseView)

function EquipmentSelectLingYuView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function EquipmentSelectLingYuView:LoadConfig()
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_select_lingyu")
end

function EquipmentSelectLingYuView:__delete()

end

function EquipmentSelectLingYuView:ReleaseCallBack()
	if self.stone_list_view then
		self.stone_list_view:DeleteMe()
		self.stone_list_view = nil
	end
	self.select_equip_list = nil
end

function EquipmentSelectLingYuView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Equip.SelectLingYuViewTitle1  --选择镶嵌灵石 文字
	--self:SetSecondView(Vector2(370, 604))
	self:CreateEquipBaoShiListView()
end

function EquipmentSelectLingYuView:ShowIndexCallBack(index)

end

function EquipmentSelectLingYuView:OpenCallBack()

end

function EquipmentSelectLingYuView:CloseCallBack()

end

function EquipmentSelectLingYuView:SetData(list, equip_index, slot_index)
	self.select_equip_list = list
	self.select_equip_index = equip_index
	self.select_slot_index = slot_index
end

function EquipmentSelectLingYuView:OnFlush()
	self:FlushEquipStrengthListDataSource()
end

-- 创建列表
function EquipmentSelectLingYuView:CreateEquipBaoShiListView()
	self.stone_list_view = AsyncListView.New(SelectLingYuItemRender, self.node_list["ph_select_baoshi_list_view"])
	self.stone_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectEquipStrengthItemHandler, self))
end

--设置移除按钮是否显示
function EquipmentSelectLingYuView:SetRemoveBtnState(boo)
	XUI.SetButtonEnabled(self.node_list.btn_remove, boo)
end

-- 刷列表数据源
function EquipmentSelectLingYuView:FlushEquipStrengthListDataSource()
	local slot_stone_itemid = EquipmentLingYuWGData.Instance:GetLingYuItemIdBySelectIndex(self.select_equip_index, self.select_slot_index)
	if slot_stone_itemid and slot_stone_itemid > 0 then
		self.node_list.title_view_name.text.text = Language.EquipLingYu.SelectLingYuViewTitle2
	else
		self.node_list.title_view_name.text.text = Language.EquipLingYu.SelectLingYuViewTitle1
	end

	if nil ~= self.stone_list_view then
		self.stone_list_view:SetDataList(self.select_equip_list)
		self.stone_list_view:CancelSelect()
	end
end

-- 选择列表项回调
function EquipmentSelectLingYuView:OnSelectEquipStrengthItemHandler(item, cell_index, is_default, is_click)
	if not is_click then
		return
	end

	if nil == item or nil == item.data then
		return
	end

	EquipmentWGCtrl.Instance:SendLingYuOperate(LINGYU_OPERA_TYPE.LINGYU_INLAY, self.select_equip_index, self.select_slot_index, item.data.index)
	self:Close()
end

-----------------------------------------------------------------------------
SelectLingYuItemRender = SelectLingYuItemRender or BaseClass(BaseRender)
function SelectLingYuItemRender:__init()
	self:CreateChild()

	if not self.arrow_tweener then
		self.arrow = self.node_list["img_remind"]
		self.arrow_tweener = self.arrow.gameObject.transform:DOAnchorPosY(0, 0.45)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end
end

function SelectLingYuItemRender:__delete()
	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
	self.node_list["lbl_name"] = nil
end

function SelectLingYuItemRender:CreateChild()
	self.item_cell = ItemCell.New(self.node_list["ph_item"])

	--XUI.AddClickEventListener(self.view, BindTool.Bind1(self.OnClick, self))
	self:SetRemind(false)
end


function SelectLingYuItemRender:OnFlush()
	if nil == self.data then
		return
	end

	if self.item_cell then
		self.item_cell:SetData(self.data)
		self.item_cell:SetItemTipFrom(ItemTip.FROM_BAOSHI)
	end

	local data_instance = EquipmentLingYuWGData.Instance
	local name_str, attr_str = data_instance:GetLingYuNatrue(self.data.item_id)
	self.node_list["lbl_name"].text.text = name_str
	self.node_list["lbl_attr"].text.text = attr_str
	self:SetRemind(true)
end

--红点
function SelectLingYuItemRender:SetRemind(enable)
	self.node_list.img_remind:SetActive(enable)
end
