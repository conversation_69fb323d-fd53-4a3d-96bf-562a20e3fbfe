﻿using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(UGUITweenPosition))]
public class TweenPositionEditor : UITweenerEditor 
{

	public override void OnInspectorGUI ()
	{
		GUILayout.Space(6f);
		UGUITweenEditorTools.SetLabelWidth(120f);

		UGUITweenPosition tw = target as UGUITweenPosition;
		GUI.changed = false;

		Vector3 from = EditorGUILayout.Vector3Field("From", tw.from);
		Vector3 to = EditorGUILayout.Vector3Field("To", tw.to);
		bool worldSpace = EditorGUILayout.Toggle("世界坐标", tw.worldSpace);
		bool anchor = EditorGUILayout.Toggle("锚点坐标", tw.anchor);

		if (GUI.changed)
		{
			UGUITweenEditorTools.RegisterUndo("Tween Change", tw);
			tw.worldSpace = worldSpace;
			tw.anchor = anchor;
			tw.from = from;
			tw.to = to;
			UGUITweenEditorTools.SetDirty(tw);
		}

		DrawCommonProperties();
	}
}
