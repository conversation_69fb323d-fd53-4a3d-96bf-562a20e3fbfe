SystemCapRankYuShouView = SystemCapRankYuShouView or BaseClass(SafeBaseView)

function SystemCapRankYuShouView:__init()
    self.view_style = ViewStyle.Half
    self:SetMaskBg(false, true)

    self:AddViewResource(0, "uis/view/system_cap_rank_ui_prefab", "layout_system_cap_yushou_rank")
end

function SystemCapRankYuShouView:LoadCallBack()
    self:LoadModel()
    if not self.rank_reward_list then
        self.rank_reward_list = AsyncListView.New(SystemCapYuShouRankRewardListItem, self.node_list["rank_reward_list"])
    end

    if not self.rank_list then
        self.rank_list = AsyncListView.New(SystemCapYuShouRankListItem, self.node_list["rank_list"])
    end

    self.node_list.rank_reward_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnTogValueChange, self, 1))
    self.node_list.rank_list_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnTogValueChange, self, 2))

    for i = 1, 4 do
        self.node_list["btn_ys" .. i].button:AddClickListener(BindTool.Bind(self.OnClickOperaBtn, self, i))
    end

    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU)
    if cfg_type and cfg_type.model_show > 0 then
        local item_cfg = ItemWGData.Instance:GetItemConfig(cfg_type.model_show)
        self.node_list.title_txt.text.text = item_cfg.name
    end

    self.node_list.rank_reward_tog.toggle.isOn = true
    self:FlushRankRewardPanel()
    self:FlushOperaBtns()

    XUI.AddClickEventListener(self.node_list.query_btn, BindTool.Bind(self.OnClickTip, self))
end

function SystemCapRankYuShouView:LoadModel()
    if self.model_display == nil then
        self.model_display = OperationActRender.New(self.node_list["model_root"])
    end

    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU)
    if cfg_type and cfg_type.model_show > 0 then
        local data = {}
        data.item_id = cfg_type.model_show
        data.render_type = 0
        data.position = Vector3(0, 0, 0)
        data.rotation = Vector3(0, 0, 0)
        data.scale = Vector3(1, 1, 1)
        self.model_display:SetData(data)
    end
end

function SystemCapRankYuShouView:ReleaseCallBack()
    if self.rank_reward_list then
        self.rank_reward_list:DeleteMe()
        self.rank_reward_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end

    if CountDownManager.Instance:HasCountDown("system_yushou_rank_down") then
        CountDownManager.Instance:RemoveCountDown("system_yushou_rank_down")
    end
end

function SystemCapRankYuShouView:OpenCallBack()
    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU)
    if cfg_type then
        SystemCapRankWGData.Instance:SetCurActRewardType(cfg_type.type)
        RankWGCtrl.Instance:SendGetPersonRankListReq(cfg_type.rank_type, nil)
    end
end

function SystemCapRankYuShouView:OnFlush(param_t)
    for k, v in pairs(param_t) do
        if "all" == k then
            self:FlushView()
        end
    end
end

function SystemCapRankYuShouView:FlushView()
    self:LoginTimeCountDown()
    local total_zhanli = SystemCapRankWGData.Instance:GetMyRankValue()
    self.node_list["my_cap"].text.text = total_zhanli or 0

    local rank_data = SystemCapRankWGData.Instance:GetMyRankInfo()
    if rank_data ~= nil then
        self.node_list["my_rank"].text.text = string.format(Language.SystemCapRank.RechargeRankTitle2,
            rank_data.rank_index)
    else
        self.node_list["my_rank"].text.text = Language.SystemCapRank.RechargeNoRank
    end
end

function SystemCapRankYuShouView:OnTogValueChange(index, is_on)
    if is_on then
        if index == 1 then
            self:FlushRankRewardPanel()
        else
            self:FlushRankListPanel()
        end
    end
end

function SystemCapRankYuShouView:FlushRankRewardPanel()
    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU)
    if cfg_type then
        local reward_list = SystemCapRankWGData.Instance:GetActRewardCfg(cfg_type.type)
        if reward_list then
            self.rank_reward_list:SetDataList(reward_list)
        end
    end
end

function SystemCapRankYuShouView:FlushRankListPanel()
    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU)
    if cfg_type and cfg_type.rank_type then
        RankWGCtrl.Instance:SendGetPersonRankListReq(cfg_type.rank_type, nil)
        local data_list = SystemCapRankWGData.Instance:GetRankInfo()
        self.rank_list:SetDataList(data_list)
    end
end

function SystemCapRankYuShouView:FlushOperaBtns()
    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU)
    local other_cfg = SystemCapRankWGData.Instance:GetActOtherCfg(cfg_type.type)
    for i = 1, 4 do
        local bottom_name = (other_cfg or {})["bottom_name" .. i] or ""
        local open_panel = (other_cfg or {})["open_panel" .. i] or ""

        self.node_list["btn_ys" .. i]:CustomSetActive(bottom_name ~= "" and open_panel ~= "")
        self.node_list["desc_ls_btn" .. i].text.text = bottom_name
    end
end

function SystemCapRankYuShouView:OnClickOperaBtn(index)
    local cfg_type = SystemCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU)
    local other_cfg = SystemCapRankWGData.Instance:GetActOtherCfg(cfg_type.type)
    local open_panel = (other_cfg or {})["open_panel" .. index] or ""
    local act_type = (other_cfg or {})["act_type" .. index] or ""

    if open_panel ~= "" and act_type ~= "" then
        if ActivityWGData.Instance:GetActivityIsOpen(act_type) then
            FunOpen.Instance:OpenViewNameByCfg(open_panel)
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.SystemCapRank.NoOpenActTips)
        end
    elseif open_panel ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(open_panel)
    end
end

function SystemCapRankYuShouView:OnClickTip()
    RuleTip.Instance:SetContent(Language.SystemCapRank.YuShouRule, Language.SystemCapRank.YuShouRuleTitle)
end

-----------------活动时间倒计时-------------------
function SystemCapRankYuShouView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
    .RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU)
    if activity_data ~= nil then
        local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list["act_time"].text.text = TimeUtil.FormatSecondDHM6(invalid_time -
            TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("system_yushou_rank_down", BindTool.Bind1(self.UpdateCountDown, self),
                BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
    end
end

function SystemCapRankYuShouView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()
        self.node_list["act_time"].text.text = TimeUtil.FormatSecondDHM6(valid_time)
    end
end

function SystemCapRankYuShouView:OnComplete()
    self.node_list["act_time"].text.text = ""
    self:Close()
end

---------------------------------------SystemCapYuShouRankRewardListItem----------------------------------
SystemCapYuShouRankRewardListItem = SystemCapYuShouRankRewardListItem or BaseClass(BaseRender)

function SystemCapYuShouRankRewardListItem:LoadCallBack()
    if not self.rank_list then
        self.rank_list = AsyncListView.New(ItemCell, self.node_list.rank_list)
        self.rank_list:SetStartZeroIndex(true)
    end
end

function SystemCapYuShouRankRewardListItem:__delete()
    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end
end

function SystemCapYuShouRankRewardListItem:OnFlush()
    if not self.data then
        return
    end

    local desc = ""
    if self.data.min_rank == 1 then
        desc = string.format(Language.OperationActivity.RechargeRankTitle2, self.data.min_rank)
    else
        desc = string.format(Language.OperationActivity.RechargeRankTitle3, self.data.min_rank, self.data.max_rank)
    end

    self.node_list["rank_desc"].text.text = string.format(Language.SystemCapRank.SystemCapYushouRankTitle, desc,
        self.data.reach_value)

    self.rank_list:SetDataList(self.data.reward_item)
end

---------------------------------SystemCapYuShouRankListItem-----------------------------
SystemCapYuShouRankListItem = SystemCapYuShouRankListItem or BaseClass(BaseRender)

function SystemCapYuShouRankListItem:OnFlush()
    if not self.data then
        return
    end

    local is_top_3
    local player_rank
    if self.data.no_true_rank then --未上榜
        self.node_list.cap.text.text = Language.OperationActivity.BaoMi

        is_top_3 = self.data.index <= 3
        player_rank = self.data.index
    else
        self.node_list.cap.text.text = self.data.rank_data.rank_value
        is_top_3 = self.data.index <= 3
        player_rank = self.data.index
    end

    local user_name = self.data.rank_data.user_name
    if self.data.no_true_rank then
        user_name = Language.OperationActivity.XuWeiYiDai
    end

    self.node_list.name.text.text = user_name
    self.node_list.rank_icon:CustomSetActive(is_top_3)

    if not is_top_3 then
        self.node_list.rank_desc.text.text = player_rank
    else
        self.node_list.rank_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. player_rank))
        self.node_list.rank_desc.text.text = ""
    end

    local is_top_one = self.data.index == 1
    self.node_list.bg:CustomSetActive(is_top_one)

    if not is_top_one and is_top_3 then
        local bundle, asset = ResPath.GetCommonImages("a2_xt_pm" .. self.data.index)
        self.node_list.bg_oth.image:LoadSprite(bundle, asset)
    end

    self.node_list.bg_oth:CustomSetActive(not is_top_one and is_top_3)
    self.node_list.bg_zi:CustomSetActive(not is_top_3 and self.data.index % 2 ~= 0)
end
