
MainUIView.ViewState = {
	Short = 0,
	Length = 1,
}
local ChannelType = {
	[1] = {0, "a2_zj_mkfshi"},
	[2] = {4, "a2_zj_mkfzong"},
	[3] = {3, "a2_zj_mkfdui"},
}

Tips_Index = {
	Tips_Guild_Chat = 1,
	Tips_Function_1 = 2,
	Tips_Function_2 = 7,
}

local CHANGE_CHAT_ACTIVE_STATE_TIME = 5

local CHAT_ACTIVE_STATE = {
	STAND = 1,     -- 待机状态
	OBSERVE = 2,   -- 被点击，变为观察状态
	ACTIVE = 3,    -- 观察状态点击，打开变为活跃状态
}

--新需求2019-08-08 10:30，除了这些index之外，其他的都显示在变强按钮里面
local MAINUI_TIP_TYPE_IDX =
{
		[MAINUI_TIP_TYPE.FRIEND] = true,					--	添加好友
		[MAINUI_TIP_TYPE.FRIEND_CHAT] = true,				--	好友聊天
		[MAINUI_TIP_TYPE.TEAM_APPLY] = true,				--	组队邀请
		[MAINUI_TIP_TYPE.TEAM_INVITE] = true,				--	组队邀请
		[MAINUI_TIP_TYPE.TEAM_INVITE_GOTO] = true,  		--	组队邀请
		[MAINUI_TIP_TYPE.TEAM_INVITE_ALERT] = true, 		--	组队邀请弹窗
		[MAINUI_TIP_TYPE.GUILD_INVITE_HINT] = true, 		--	收到邀请进仙盟
		[MAINUI_TIP_TYPE.INVITE_START_LOVER_PK] = true, 	--收到邀请进仙侣PK
		[MAINUI_TIP_TYPE.XIEZHU] = true,					--仙盟协助
		[MAINUI_TIP_TYPE.BOOTYBAY_READY_VIEW] = true,		--藏宝湾组队准备界面
		[MAINUI_TIP_TYPE.MARRY_BEYAO] = true,				--情缘被邀提醒
		[MAINUI_TIP_TYPE.ZHAN_DUI_BE_INVITE] = true,		--战队被邀入队提醒
		[MAINUI_TIP_TYPE.ZHAN_DUI_NEW_APPLT] = true,		--战队申请入队提醒
		[MAINUI_TIP_TYPE.INVITE_START_CROSS_3V3] = true, 	--收到邀请进跨服

		[MAINUI_TIP_TYPE.TEAM_HEBING] = true, 				--队伍合并
		[MAINUI_TIP_TYPE.STUDENT_HELP] = true, 				--徒弟求助
	    [MAINUI_TIP_TYPE.GUILD] = true,						--主界面提示仙盟申请的按钮
	    [MAINUI_TIP_TYPE.ENEMY_RECORD] = true,				--主界面仇人新记录
	    [MAINUI_TIP_TYPE.BOSS_XIEZHU] = true,				--蛮荒boss协助
	    [MAINUI_TIP_TYPE.LIAOYILIAO] = true,				--情缘撩一撩
	    [MAINUI_TIP_TYPE.FRIEND_LIAOYILIAO] = true,			--撩一撩（好友申请）
	    [MAINUI_TIP_TYPE.WORLDS_NO1_BET] = true,			--天下第一竞猜
		[MAINUI_TIP_TYPE.INVITE] = true, 					--婚宴申请
		[MAINUI_TIP_TYPE.HunYan_Yuyue] = true,				--婚宴预约
	    [MAINUI_TIP_TYPE.FENGSHENBANG_REMIND] = true, 		--封神榜被抢提醒
		[MAINUI_TIP_TYPE.Guild_BaoXaing_Req] = true,	--	每日宝箱请求刷新





		[MAINUI_TIP_TYPE.LEVEL_PURCHASE] = true,			--等级限购
		[MAINUI_TIP_TYPE.CHAT_GUILD] = true,				--等级限购

	    [MAINUI_TIP_TYPE.OneVOne_JingCai] = true, 			--跨服1v1竞猜
	    [MAINUI_TIP_TYPE.Market_Auction] = true,			--市场--拍卖
	    [MAINUI_TIP_TYPE.Market_Auction_Beyond] = true,		--市场--竞价被超越
	    [MAINUI_TIP_TYPE.Guild_BaoXaing_Req] = true, 		--每日宝箱请求刷新
	    [MAINUI_TIP_TYPE.SHOU_HU_YOU_HUI] = true, 			--守护优惠
	    [MAINUI_TIP_TYPE.CHAT_AITE_ME] = true, 				--公共聊天频道@我

	    [MAINUI_TIP_TYPE.FEED_BACK] = true, 				--玩家反馈
	    [MAINUI_TIP_TYPE.SUB_PACKAGE] = true,				--分包下载
	    [MAINUI_TIP_TYPE.NATIONAL_PREVIEW] = true,			--国家预告


	    [MAINUI_TIP_TYPE.QIFU_YUNSHI_TIP] = true,			--祈福 运势
		[MAINUI_TIP_TYPE.HAMMER_PLAN] = true,           	-- 暴揍策划
		[MAINUI_TIP_TYPE.COLLECTION_WORD_TIP] = true,		-- 天师集字
		[MAINUI_TIP_TYPE.MARKET_AUCTOIN_SUS] = true,		--市场--拍卖物被拍
		[MAINUI_TIP_TYPE.EVERYDAY_RECHARGE_DAILYGIFT] = true,	--秒杀礼包-秒杀
		[MAINUI_TIP_TYPE.DUJIE_TIPS] = true,				--渡劫
		[MAINUI_TIP_TYPE.DUJIE_INVITE_TIPS] = true,        -- 渡劫邀请
}

-- chat 面板固定的功能按钮 暂用于处理收起按钮时红点
-- local CHAT_PANEL_FUNC_CFG = {
-- 	btn_customized_rumors = {["remind"] = RemindName.CustomizedRumorsView},
-- 	btn_societyt = {["remind"] = RemindName.SocietyFriends},
-- }

-- 新需求2024-06-21 从变强按钮中脱离 归入领取按钮中的tip
local MAINUI_TIP_TYPE_REWARD = {
		[MAINUI_TIP_TYPE.ACHIEVENT] = true, 			--  成就
		[MAINUI_TIP_TYPE.REDENVELOPES] = true,			--	红包提醒
		[MAINUI_TIP_TYPE.TREADURE_MAP] = true,			--	藏宝图
		[MAINUI_TIP_TYPE.SOCIETY_WISH] = true,			--	祝
		[MAINUI_TIP_TYPE.JIEZHEN] = true,				--	修
		[MAINUI_TIP_TYPE.REDPOCKET] = true,				--	仙盟红包
		[MAINUI_TIP_TYPE.SKY_REDENVELOPES] = true,		--	天降红包
		[MAINUI_TIP_TYPE.Welfare_Upgrade_Gift] = true,	--	等級礼包
		[MAINUI_TIP_TYPE.Welfare_Vip_Gift] = true,		--	vip礼包
		[MAINUI_TIP_TYPE.SIT_REARD] = true,				--	打坐奖励
		[MAINUI_TIP_TYPE.Boss_FirstKill] = true,		--	boss首杀


		[MAINUI_TIP_TYPE.XIANLI_CANGET] = true,			--	vipBoss 仙力领取
		[MAINUI_TIP_TYPE.QIFU_YUNSHI_TIP] = true,		--	祈福 运势
		[MAINUI_TIP_TYPE.ASSIGN_COMPLIT] = true,		--	委托任务完成
		[MAINUI_TIP_TYPE.OPEN_SERVER_INVEST] = true,	--	开服投资
		[MAINUI_TIP_TYPE.MAIL] = true, 					--	邮件
	    [MAINUI_TIP_TYPE.SIT_REARD] = true, 				--打坐奖励
	    [MAINUI_TIP_TYPE.XIANLI_CANGET] = true, 			--仙力领取
		[MAINUI_TIP_TYPE.GUAJI_LILIAN] = true,			-- 历练领取
		[MAINUI_TIP_TYPE.XiuZhenRoad] = true,			-- 修真之路
		[MAINUI_TIP_TYPE.CapabilityWelfare] = true,		-- 战力福利
}

function MainUIView:__initChat()
	-- body
	self.tips_list = {}
	self.tips_data = {}
	self.icon_list = {}
	self.surplus_tips_data = {} -- 缓存多余的
	self.cache_tips_data_list = {} -- 缓存数据
	self.update_icon_time = 0
	self.update_interval_time = 0.3
	self.delay_refresh_chat_view = BindTool.Bind(self.DelayRefreshChatView, self)
	self.active_cell_list = {}
	self.recycled_cell_list = {}
	self.old_cell_height_list = {}
	self.chat_cell_obj_list = {}
	self.bianqiang_update_flag = true
end

function MainUIView:ChatLoadCallBack()
	self.state = MainUIView.ViewState.Short

	for i = 1, 10 do
		self.icon_list[i] = self.node_list["Icon_" .. i]
	end

	-- self.main_btn_orgin_pos = self.node_list["mail_btn"].transform.localPosition
	XUI.AddClickEventListener(self.node_list["yulong_btn"],BindTool.Bind(self.OnClickYuLong, self))			--御龙
	XUI.AddClickEventListener(self.node_list["btn_mecha_bs"],BindTool.Bind(self.OnClickMechaBs, self))		-- 高达
	XUI.AddClickEventListener(self.node_list["BtnContent"],BindTool.Bind(self.OnClickOpenChatWindow, self, nil, true))
	XUI.AddClickEventListener(self.node_list["mainui_chuanwen_root"],BindTool.Bind(self.OpenChuanWenChatWindow, self))
	XUI.AddClickEventListener(self.node_list["BtnOpenChatWindow"],BindTool.Bind(self.OnClickOpenChatWindow, self, nil, true))

	-- XUI.AddClickEventListener(self.node_list["mainui_btn_country_yugao"],BindTool.Bind(self.OnClickCountryYuGao, self))
	-- XUI.AddClickEventListener(self.node_list["setting"],BindTool.Bind(self.OnClickOpenSettingView, self))
	XUI.AddClickEventListener(self.node_list["btn_speak_change"],BindTool.Bind(self.OnClickSpeakChange, self))
	-- XUI.AddClickEventListener(self.node_list["btn_societyt"], function()
	-- 	ViewManager.Instance:Open(GuideModuleName.Society)
	-- end)

	XUI.AddClickEventListener(self.node_list["btn_bianqiang"],BindTool.Bind(self.OpenStrongMenu, self))
	XUI.AddClickEventListener(self.node_list["btn_bianqiang_get_reward"],BindTool.Bind(self.OpenStrongGetRewardView, self))
	XUI.AddClickEventListener(self.node_list["dragon_temple_hatch_root"],BindTool.Bind(self.OpenDragonTempleHatchView, self))
	XUI.AddClickEventListener(self.node_list["btn_cancel_bianshen"],BindTool.Bind(self.OnClickCancelBianShen, self))

	-- self.chat_left_shrink_ison = self.node_list["chat_shrink_left_btn"].toggle.isOn
	-- XUI.AddClickEventListener(self.node_list["chat_shrink_left_btn"], BindTool.Bind(self.OnChatLeftShrinkBtnValueChange, self))	-- 左边箭头按钮

	self.node_list["btn_cancel_bianshen"]:SetActive(false)

	-- 聊天list
	self.chat_list_content = self.node_list["ChatListContent"]

	self.is_load_chat = true

	local event_listener = self.node_list["speak_event_trigger"].event_trigger_listener
	if event_listener ~= nil then
		event_listener:AddPointerDownListener(BindTool.Bind1(self.OnPointDown, self))		--按下
		event_listener:AddPointerUpListener(BindTool.Bind1(self.OnPointUp, self))
	end

	self.old_channel = CHANNEL_TYPE.SYSTEM
	self.mainui_channel = CHANNEL_TYPE.WORLD   --默认世界语音
	self.is_speak_limit = false
	self.item_data_change = GlobalEventSystem:Bind("MainUIViewChat_ItemDataGet",BindTool.Bind1(self.OnItemDataChange, self))
	self.tianshen_item_change = GlobalEventSystem:Bind(MainUIEventType.TianShen_Item_Change,BindTool.Bind1(self.OnTianShenItemChange, self))
	-- self.node_list["btn_act_discount"].button:AddClickListener(BindTool.Bind(self.OnClickOpenActDiscoount, self))
	--self:CameraModeChange()

	--self.camera_mode_change = GlobalEventSystem:Bind(SettingEventType.MAIN_CAMERA_MODE_CHANGE, BindTool.Bind(self.CameraModeChange, self))



	-- --邮件点击
	-- XUI.AddClickEventListener(self.node_list["mail_btn"], function ()
	-- 	if self.meail_cache_tab and self.meail_cache_tab.callback then
	-- 		local scene_logic = Scene.Instance:GetSceneLogic()
	-- 		if not scene_logic:CanOpenMail() then
	-- 			return
	-- 		end
	-- 		self.meail_cache_tab.callback()
	-- 	end
	-- end)

	--协助点击
	XUI.AddClickEventListener(self.node_list["btn_assistt"], function ( )
		local list = BossAssistWGData.Instance:GetGuildAssistCallHelpListInfo()
		local tab_index
		if #list > 0 then
			tab_index = TabIndex.boss_xiezhu
		end
		BossAssistWGCtrl.Instance:Open(tab_index)
	end)

	self.show_chat_view_event = GlobalEventSystem:Bind(MainUIEventType.SHOW_MAIN_CHAT, BindTool.Bind1(self.ShowChatView, self))
	-- self.chat_pb_change_event = GlobalEventSystem:Bind(OtherEventType.CHAT_PB_CHANGE, BindTool.Bind1(self.CheckVoiceCanShow, self))

	self:InitChanWenChatList()

	--仙盟聊天提醒不常驻  2019/8/28 17:24
	--local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	--GuildWGCtrl.Instance:OpenMainUiGuildBtn( main_role_vo.guild_id)
	self:ChatCanShowCallHelp()
	self:CheckVoiceCanShow()
	XUI.AddClickEventListener(self.node_list.btn_sit, BindTool.Bind(self.OnClickSit, self))
	RemindManager.Instance:Bind(self.remind_change, RemindName.SocietyFriends)

	self.chat_head_1 = BaseHeadCell.New(self.node_list.chat_head_1)
	self.chat_head_2 = BaseHeadCell.New(self.node_list.chat_head_2)
	self.spe_fb_send_flower_head_list = {}
	for i = 1, 4 do
		self.spe_fb_send_flower_head_list[i] = BaseHeadCell.New(self.node_list["spe_fb_send_flower_head_" .. i])
	end

	XUI.AddClickEventListener(self.node_list.chat_head_1, BindTool.Bind(self.OnClickChatPrivateUnreadMsgHead, self))
	XUI.AddClickEventListener(self.node_list.chat_head_2, BindTool.Bind(self.OnClickChatPrivateUnreadMsgHead, self))

	for i = 1, 4 do
		XUI.AddClickEventListener(self.node_list["spe_fb_send_flower_" .. i], BindTool.Bind(self.OnClickSpeFbSendFlowerInfo, self, i))
    end

	self:SetChatPrivateUnreadMsgHead()

	self.can_change_chat_state = true
	self.chat_active_state = CHAT_ACTIVE_STATE.ACTIVE                 -- 聊天栏状态
	self.last_active_chat_time = CHANGE_CHAT_ACTIVE_STATE_TIME -- 上次更新聊天栏时间
	self.last_check_time = TimeWGCtrl.Instance:GetServerTime()
end

function MainUIView:Chat__ReleaseCallBack()
	self:RemoveSpeakLongTimer()
	local key = "mianui_yulong_countdown"
	if CountDownManager.Instance:HasCountDown(key) then
		CountDownManager.Instance:RemoveCountDown(key)
	end

	local mecha_key = "mianui_mecha_bs_countdown"
	if CountDownManager.Instance:HasCountDown(mecha_key) then
		CountDownManager.Instance:RemoveCountDown(mecha_key)
	end

	local key = "mianui_bianqiang_bubble_countdown"
	if CountDownManager.Instance:HasCountDown(key) then
		CountDownManager.Instance:RemoveCountDown(key)
	end

	self.meail_cache_tab = nil
	self.tips_data = nil
	self.surplus_tips_data = nil
	self.cache_tips_data_list = {}
	self.icon_list = nil
	if self.tips_list then
		for _, v in pairs(self.tips_list) do
			v:DeleteMe()
		end
		self.tips_list = nil
	end

	self.chat_list_content = nil

	self.tweener11 = nil
	self.rember_is_show_answer_tip = nil

	-- self.main_btn_orgin_pos = nil

	if self.item_data_change then
		GlobalEventSystem:UnBind(self.item_data_change)
		self.item_data_change = nil
	end

	if self.tianshen_item_change then
		GlobalEventSystem:UnBind(self.tianshen_item_change)
		self.tianshen_item_change = nil
	end

	if self.camera_mode_change then
		GlobalEventSystem:UnBind(self.camera_mode_change)
		self.camera_mode_change = nil
	end

	if self.transmit_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.transmit_timer)
		self.transmit_timer = nil
	end

	if nil ~= self.delay_refresh_chat_view_timer then
		GlobalTimerQuest:CancelQuest(self.delay_refresh_chat_view_timer)
		self.delay_refresh_chat_view_timer = nil
	end

	if self.chat_cell_obj_list then
		for k,v in pairs(self.chat_cell_obj_list) do
			ResPoolMgr:Release(v)
		end
	end
	self.chat_cell_obj_list = nil

	if self.mainui_chuanwen_chat_obj then
		ResPoolMgr:Release(self.mainui_chuanwen_chat_obj)
	end
	self.mainui_chuanwen_chat_obj = nil

	if self.mainui_chuanwen_chat_cell then
		self.mainui_chuanwen_chat_cell:DeleteMe()
		self.mainui_chuanwen_chat_cell = nil
	end

	self.active_cell_list = {}
	self.recycled_cell_list = {}
	self.old_cell_height_list = {}
	self.is_load_chat = nil

   	if self.guaji_change_event then
   		GlobalEventSystem:UnBind(self.guaji_change_event)
   		self.guaji_change_event = nil
  	end
  	if self.container_animator ~= nil then
		self.container_animator = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)

	self.tweener11 = nil

	if self.show_chat_view_event then
		GlobalEventSystem:UnBind(self.show_chat_view_event)
		self.show_chat_view_event = nil
	end

	if self.chat_pb_change_event then
		GlobalEventSystem:UnBind(self.chat_pb_change_event)
		self.chat_pb_change_event = nil
	end

	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	if self.bianqiang_delay_timer then
		GlobalTimerQuest:CancelQuest(self.bianqiang_delay_timer)
		self.bianqiang_delay_timer = nil
	end

	if self.chat_head_1 then
		self.chat_head_1:DeleteMe()
		self.chat_head_1 = nil
	end

	if self.chat_head_2 then
		self.chat_head_2:DeleteMe()
		self.chat_head_2 = nil
	end

	if self.spe_fb_send_flower_head_list then
        for k, v in pairs(self.spe_fb_send_flower_head_list) do
            v:DeleteMe()
        end
        self.spe_fb_send_flower_head_list = nil
    end

	self.chat_active_state = nil
	self.last_active_chat_time = nil
	self.last_check_time = nil
end

function MainUIView:InitChanWenChatList()
	local obj = ResPoolMgr:TryGetGameObject("uis/view/main_ui_prefab", "ChatCell2",
							self.node_list.mainui_chuanwen_root_content.transform)
	self.mainui_chuanwen_chat_obj = obj
	self.mainui_chuanwen_chat_cell = MainUIChatCell.New(obj)
	-- self.mainui_chuanwen_chat_cell:SetAnchoredPosition(0, 50)
end

function MainUIView:ChatCanShowCallHelp()
	if self.node_list.icon_zhu ~= nil then
		self.node_list.icon_zhu:SetActive(BossAssistWGData.Instance:HasCallHelp())
		local is_showzhu = self.node_list.icon_zhu:GetActive()
		if is_showzhu then
			self.node_list["icon_zhu"].animator:SetBool("play", is_showzhu)
		end
	end
end

function MainUIView:ShowChatView()
	self:HideOrShowChatView(false)
end

function MainUIView:CheckVoiceCanShow()
	local is_shield_voice = GLOBAL_CONFIG.param_list.shield_chat_voice == 1 -- ChatWGData.IS_PB_AUDIO_CHAT
	is_shield_voice = true	--暂时去掉主界面的UI显示
	self.node_list.speak_content:SetActive(not is_shield_voice)
end

function MainUIView:OnTianShenItemChange(protocol)
	-- body
	if not protocol or not protocol.bag_item or 0 == protocol.bag_item.item_id then return end

	local item_id = protocol.bag_item.item_id
	local bag_index = protocol.bag_item.bag_index
	local config, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then return end
	local mark = "tianshen_eq"

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg then return end

	local time = TimeWGCtrl.Instance:GetServerTime()
	local str = "获得".."{" .. mark .. ";".. item_id .. ";" .. bag_index .. "}X1"
	ChatWGCtrl.Instance:AddSystemMsg(str, time)
end

-- 背包数据发生变化通知
function MainUIView:OnItemDataChange(item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if new_num == nil or old_num == nil or old_num >= new_num or item_id <= 0 then
		return
	end

	if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		return
	end

	local item_data = ItemWGData.Instance:GetGridData(change_item_index)
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	if item_cfg == nil then
		return
	end

	local mark = ""
	local param_str = ""
	if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		mark = "eq"
		local param_data_list = {}
		param_data_list[1] = "2"
		param_data_list[4] = item_data.param.star_level or 0
		if item_data.param.xianpin_type_list ~= nil then
			for k, v in ipairs(item_data.param.xianpin_type_list) do
				param_data_list[14 + k] = v
			end
		end

		for i = 1, 20 do
			local num = param_data_list[i] or 0
			param_str = param_str .. num .. ":"
		end
	else
		mark = "i"
	end

	local time = TimeWGCtrl.Instance:GetServerTime()
	-- local color = ITEM_COLOR[item_cfg.color] or COLOR3B.WHITE
	local num = "x" .. tostring(new_num - old_num) --ToColorStr("x" .. tostring(new_num - old_num), color)
	local str = string.format("获得{%s;%d;%s}%s", mark, item_data.item_id, param_str, num)
	ChatWGCtrl.Instance:AddSystemMsg(str, time)
end

function MainUIView:OnClickSpeakChange()
	if IsEmptyTable(ChannelType) then
		return
	end

	local cur_index, change_index = 1, 1
	for k,v in ipairs(ChannelType) do
		if v[1] == self.mainui_channel then
			cur_index = k
			break
		end
	end

	change_index = cur_index + 1 > #ChannelType and 1 or cur_index + 1
	self.mainui_channel = ChannelType[change_index][1]

	self:FlushCurSpeak()
end

function MainUIView:FlushCurSpeak()
	local is_limit = false
	if self.mainui_channel == CHANNEL_TYPE.GUILD then
		local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
		is_limit = main_role_vo.guild_id <= 0
	elseif self.mainui_channel == CHANNEL_TYPE.TEAM then
		is_limit = SocietyWGData.Instance:GetIsInTeam() ~= 1
	end

	local res_data = ""
	for k,v in ipairs(ChannelType) do
		if v[1] == self.mainui_channel then
			res_data = v[2]
			break
		end
	end

	self.is_speak_limit = is_limit
	self.node_list.curtextspeak.image:LoadSprite(ResPath.GetMainUIIcon(res_data))
	self.node_list.speak_limit_img:SetActive(is_limit)
end

local is_long_click = false
function MainUIView:OnPointDown(event_data)
	is_long_click = false

	if self.is_speak_limit then
		if self.mainui_channel == CHANNEL_TYPE.GUILD then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoGuild)
		elseif self.mainui_channel == CHANNEL_TYPE.TEAM then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoTeam)
		end
		return
	end

	self:RemoveSpeakLongTimer()
	self.long_time_quest = GlobalTimerQuest:AddDelayTimer(function()
		is_long_click = true
		self.old_channel = ChatWGCtrl.Instance:GetCurrSendChannel()	--记录聊天频道
		ChatWGCtrl.Instance:SetCurrSendChannel(self.mainui_channel)	--设置当前频道
		AutoVoiceWGCtrl.Instance:SetIsMainChatVoice(true)
		ChatWGCtrl.Instance:HandleVoiceStart()						--发送语音
		self.record_point_down_y = event_data.position.y
	 end, 1)
end

function MainUIView:RemoveSpeakLongTimer()
	if self.long_time_quest then
		GlobalTimerQuest:CancelQuest(self.long_time_quest)
		self.long_time_quest = nil
	end
end

function MainUIView:OnPointUp(event_data)
	self:RemoveSpeakLongTimer()
	if is_long_click then
		local delta = math.abs(self.record_point_down_y - event_data.position.y)
		local is_cancel_voice = delta > 100
		ChatWGCtrl.Instance:HandleVoiceStop(is_cancel_voice)
		ChatWGCtrl.Instance:SetCurrSendChannel(self.old_channel)
	end
end

function MainUIView:HideOrShowChatView(isOn)
	if not self.node_list["Container"] then
		return
	end

	if self.container_animator == nil then
		self.container_animator = self.node_list["Container"].animator
	end
	self.container_animator:SetBool("is_shousuo", isOn)
end

-----------------------------------------------------

function MainUIView:OnClickYuLong()
	local isopen = FunOpen.Instance:GetFunIsOpened(FunName.NewFightMountView)
	if not isopen then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.FightMountFunOpenTip)
	end

	local cur_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if cur_scene_cfg.pb_yulong == 1 then
    	TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ScenePBDragon)
		return
    end

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	if main_role:IsQingGong() or main_role:IsJump() or main_role:IsMitsurugi() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	if Scene.Instance:GetSceneType() == SceneType.GUILD_ANSWER_FB then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoBianShen2)
		return
	end

	if main_role:IsChuanGong() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.Passing)
		return
	end

	if main_role:IsGhost() or main_role:IsRidingFightMount() or main_role.vo.special_appearance > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	local flag, str = RoleWGData.Instance:CheckCanMount()
	if not flag then
        if str then
            TipWGCtrl.Instance:ShowSystemMsg(str)
        end
        return
    end
	
	local use_skill_end_time, next_use_skill_time = NewFightMountWGData.Instance:GetUseSkillTimeInfo()
	local use_skill_id = NewFightMountWGData.Instance:GetUseSkillId()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	
	local can_use_skill = server_time >= next_use_skill_time and use_skill_id >= 0 --冷却拦截
	if not can_use_skill then
		return
	end
	NewFightMountWGCtrl.Instance:SendCSNewFightMountOperateRequest(FIGHT_MOUNT2_OPERATE_TYPE.FIGHT)
end

-------------------------------------机甲start-------------------------------------------------------
function MainUIView:OnClickMechaBs()
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	if main_role:IsQingGong() or main_role:IsJump() or main_role:IsMitsurugi() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	if Scene.Instance:GetSceneType() == SceneType.GUILD_ANSWER_FB then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoBianShen)
		return
	end

	if main_role:IsChuanGong() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.Passing)
		return
	end

	if main_role:IsGhost() or main_role:IsRidingFightMount() or main_role.vo.special_appearance > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	local isopen = FunOpen.Instance:GetFunIsOpened(FunName.MechaView)
	if not isopen then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.MechaFunOpenTip)
		return
	end

	local bs_mecha_seq = MechaWGData.Instance:GetBianShenSeq()
	if bs_mecha_seq < 0 then
		return
	end

	local mecha_cfg = MechaWGData.Instance:GetMechaCfgBySeq(bs_mecha_seq)
	if IsEmptyTable(mecha_cfg) then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local bs_end_time = MechaWGData.Instance:GetBianShenEndTime()
	local can_mecha_bs = bs_end_time + mecha_cfg.bianshen_cd < server_time
	if can_mecha_bs then
		MountWGCtrl.Instance:SendMountGoonReq(0)
		MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.BIANSHEN)
	end
end

function MainUIView:ChangeMechaBsBtnState()
	local isopen = FunOpen.Instance:GetFunIsOpened(FunName.MechaView)
	if not isopen then
		self.node_list.btn_mecha_bs:CustomSetActive(false)
		return
	end

	local bs_mecha_seq = MechaWGData.Instance:GetBianShenSeq()
	if bs_mecha_seq < 0 then
		self.node_list.btn_mecha_bs:CustomSetActive(false)
		return
	end

	local mecha_cfg = MechaWGData.Instance:GetMechaCfgBySeq(bs_mecha_seq)
	if IsEmptyTable(mecha_cfg) then
		self.node_list.btn_mecha_bs:CustomSetActive(false)
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local bs_end_time = MechaWGData.Instance:GetBianShenEndTime()
	local help_fight_cd_reduce = MechaWGData.Instance:GetHelpFightCDReduce()
	local bs_restart_time = bs_end_time + mecha_cfg.bianshen_cd - help_fight_cd_reduce
	local can_mecha_bs = bs_restart_time < server_time
	self.node_list.btn_mecha_bs:CustomSetActive(true)

	if can_mecha_bs then
		local key = "mianui_mecha_bs_countdown"
		if CountDownManager.Instance:HasCountDown(key) then
			CountDownManager.Instance:RemoveCountDown(key)
		end
		
		self.node_list.mecha_bs_progress_bg:CustomSetActive(true)
		self.node_list.mecha_bs_progress_bg.slider.value = 1
		self.node_list.mecha_bs_cd_mask:CustomSetActive(false)
		self.node_list.mecha_bs_cd_text:CustomSetActive(false)
		self.node_list.mecha_bs_hight_light:CustomSetActive(false)
	else
		self:FlushMechaBsCountDown(bs_restart_time, bs_end_time)
	end

	self.node_list.mecha_bs_anniu_eff:CustomSetActive(can_mecha_bs)

	self:IsShowJumpBtn()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self:SetZuoqiBtnShowState((server_time + 1) >= bs_end_time)
end

function MainUIView:FlushMechaBsCountDown(next_mecha_bs_time, mecha_bs_end_time)
	local key = "mianui_mecha_bs_countdown"
	if CountDownManager.Instance:HasCountDown(key) then
		CountDownManager.Instance:RemoveCountDown(key)
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self:MechaBsFlushTime(0, next_mecha_bs_time - server_time)

	if next_mecha_bs_time > server_time then
		CountDownManager.Instance:AddCountDown(key,
			BindTool.Bind1(self.MechaBsFlushTime, self),
			BindTool.Bind1(self.MechaBsComPlete, self),
			next_mecha_bs_time,
			nil,
			0.1)
	end
end

function MainUIView:MechaBsFlushTime(elapse_time, total_time)
	local bs_mecha_seq = MechaWGData.Instance:GetBianShenSeq()
	local mecha_cfg = MechaWGData.Instance:GetMechaCfgBySeq(bs_mecha_seq)
	if not IsEmptyTable(mecha_cfg) then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local bs_end_time = MechaWGData.Instance:GetBianShenEndTime()
		local help_fight_cd_reduce = MechaWGData.Instance:GetHelpFightCDReduce()
		local bs_restart_time = bs_end_time + mecha_cfg.bianshen_cd - help_fight_cd_reduce
		local is_mecha_bs_now = server_time < bs_end_time
		local bianshen_start_time = MechaWGData.Instance:GetBianShenStartTime()
	
		if is_mecha_bs_now then
			self.node_list.mecha_bs_progress_bg:CustomSetActive(true)
			local value = (bs_end_time - server_time) / (bs_end_time - bianshen_start_time)
			self.node_list.mecha_bs_progress_bg.slider.value = value
			self.node_list.mecha_bs_cd_mask:CustomSetActive(false)
			self.node_list.mecha_bs_cd_text:CustomSetActive(false)
			self.node_list.mecha_bs_hight_light:CustomSetActive(true)
			self.node_list.mecha_bs_hight_light.transform.localRotation = Quaternion.Euler(0, 0, -360 * (1 - value))
		else
			self.node_list.mecha_bs_progress_bg:CustomSetActive(false)
			self.node_list.mecha_bs_hight_light:CustomSetActive(false)
			self.node_list.mecha_bs_cd_mask:CustomSetActive(true)
			self.node_list.mecha_bs_cd_text:CustomSetActive(true)

			local cd_text = bs_restart_time - server_time
			local bianshen_cd = mecha_cfg.bianshen_cd- help_fight_cd_reduce
			self.node_list.mecha_bs_cd_text.text.text = string.format("%0.1f", cd_text)
			self.node_list.mecha_bs_cd_mask.image.fillAmount = cd_text / bianshen_cd
		end
	end
end

function MainUIView:MechaBsComPlete()
	self:ChangeMechaBsBtnState()
end
---------------------------------------------机甲end-------------------------------------------------

function MainUIView:OnClickOpenChatWindow(index, need_check_state)
	if need_check_state then
		if not self:IsCanClickOpenChatWindow() then
			return
		end
	end

	local default_index = GuildWGData.Instance:HasGuild() and CHANNEL_TYPE.GUILD or CHANNEL_TYPE.WORLD
	index = index or default_index
	ChatWGCtrl.Instance:OpenChatWindow(ChatTabIndex[index])--index)

	if self.container_animator == nil then
		self.container_animator = self.node_list["Container"].animator
	end

	if self.container_animator:GetBool("is_shousuo") == true then
		return
	end
	MainuiWGCtrl.Instance:HideOrShowChatView(true)
end

function MainUIView:OpenChuanWenChatWindow()
	self:OnClickOpenChatWindow(CHANNEL_TYPE.CHUAN_WEN)
end

function MainUIView:OnClickOpenSettingView()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Setting, "setting_screen")
end

-- function MainUIView:OnClickCountryYuGao()
-- 	ViewManager.Instance:Open(GuideModuleName.VersionsAdvanceNoticeView)
-- end

function MainUIView:FulshChatView()
	if nil ~= self.delay_refresh_chat_view_timer then
		return
	end

	self.delay_refresh_chat_view_timer = GlobalTimerQuest:AddDelayTimer(self.delay_refresh_chat_view, 0)
end

function MainUIView:DelayRefreshChatView()
	self.delay_refresh_chat_view_timer = nil

	local channel_list = ChatWGData.Instance:GetChannel(CHANNEL_TYPE.MAINUI)
	local msg_list = channel_list.msg_list or {}

	local chuanwen_list = {}
	local normal_list = {}

	for i=1,#msg_list do
		-- if msg_list[i].channel_type == CHANNEL_TYPE.CHUAN_WEN then
		-- 	chuanwen_list[#chuanwen_list + 1] = msg_list[i]
		-- else
		-- 	normal_list[#normal_list + 1] = msg_list[i]
		-- end

		normal_list[#normal_list + 1] = msg_list[i]
	end

	if #chuanwen_list > 0 then
		self.mainui_chuanwen_chat_cell:SetData(chuanwen_list[#chuanwen_list])
		local old_visible = self.mainui_chuanwen_chat_cell:GetActive()
		self.mainui_chuanwen_chat_cell:SetVisible(true)
		local function to_bottom()
			if self.node_list.mainui_chuanwen_root and self.node_list.mainui_chuanwen_root.scroll_rect then
				self.node_list.mainui_chuanwen_root.scroll_rect.verticalNormalizedPosition = 0
			end
		end

		if not old_visible then
			ReDelayCall(self, to_bottom, 0.2, "main_chuanwen_tobottom")
		else
			to_bottom()
		end
	else
		self.mainui_chuanwen_chat_cell:SetVisible(false)
	end

	self:FlushNormalChatList(normal_list)
end

local cell_ver_spacing = 4
function MainUIView:FlushNormalChatList(msg_list)
	for k,v in ipairs(self.active_cell_list) do
		self:RecycleChatCell(v)
	end
	self.active_cell_list = {}

	self.cell_list_dirty = true
	self.list_height = 0
	self.cell_act_count = math.min(#msg_list, 16)				-- 最大记录16条
	for i = #msg_list - self.cell_act_count + 1, #msg_list do
		local msg = msg_list[i]
		local msg_id = msg.msg_id
		local chat_cell = self:GetChatCell(msg_id)
		if chat_cell then
			table.insert(self.active_cell_list, chat_cell)
		else
			local obj = ResPoolMgr:TryGetGameObject("uis/view/main_ui_prefab", "ChatCell", self.chat_list_content.transform)
			table.insert(self.chat_cell_obj_list, obj)
			chat_cell = MainUIChatCell.New(obj)
			table.insert(self.active_cell_list, chat_cell)
		end

		if nil ~= chat_cell then
			chat_cell:SetActive(true)
			chat_cell:SetData(msg)
			self.list_height = self.list_height + chat_cell:GetContentHeight() + cell_ver_spacing
		end
	end

	for k,v in pairs(self.recycled_cell_list) do
		v:SetActive(false)
	end

	self:RefreshLayout()
end

function MainUIView:GetChatCell(msg_id)
	local cell = self.recycled_cell_list[msg_id]
	if nil == cell then
		msg_id = next(self.recycled_cell_list)
		if msg_id then
			cell = self.recycled_cell_list[msg_id]
			self.recycled_cell_list[msg_id] = nil
		end
	else
		self.recycled_cell_list[msg_id] = nil
	end

	return cell
end

function MainUIView:RecycleChatCell(cell)
	local msg_id = cell:GetMsgID()
	if nil ~= self.recycled_cell_list[msg_id] then
		self.recycled_cell_list[{}] = cell
	else
		self.recycled_cell_list[msg_id] = cell
	end
end

function MainUIView:RefreshLayout()
	local need_layout = self.cell_list_dirty
	self.cell_list_dirty = false

	for i,v in ipairs(self.active_cell_list) do
		local now_height = v:GetContentHeight()
		if now_height ~= self.old_cell_height_list[i] then
			need_layout = true
			self.old_cell_height_list[i] = now_height
		end
	end

	if need_layout then
		local max_pos_y = 146			-- 显示区域高度
		local pos_y = 0
		local content_hight = 0

		for i = 1, self.cell_act_count do
			if self.active_cell_list[i] then
				self.active_cell_list[i]:SetAnchoredPosition(0, pos_y)
				pos_y = pos_y - cell_ver_spacing - self.old_cell_height_list[i]
				content_hight = content_hight + cell_ver_spacing + self.old_cell_height_list[i]
			end
		end

		if (self.list_height - cell_ver_spacing) > max_pos_y then
			self.chat_list_content.rect.anchorMin = Vector2(0.5, 0)
			self.chat_list_content.rect.anchorMax = Vector2(0.5, 0)

			self.chat_list_content.rect.pivot = Vector2(0.5, 0)
			self.chat_list_content.rect.anchoredPosition = Vector2(0, 0)
			self.chat_list_content.rect.sizeDelta = Vector2(332, content_hight)
		else
			self.chat_list_content.rect.anchorMin = Vector2(0.5, 1)
			self.chat_list_content.rect.anchorMax = Vector2(0.5, 1)
			self.chat_list_content.rect.pivot = Vector2(0.5, 1)
			self.chat_list_content.rect.anchoredPosition = Vector2(0, 0)
			self.chat_list_content.rect.sizeDelta = Vector2(332, max_pos_y)
		end

		-- local is_down_to_up = self.list_height >= max_pos_y

		-- if is_down_to_up then  -- 从下往上
		-- 	for i = self.cell_act_count, 1, -1 do
		-- 		if self.active_cell_list[i] then
		-- 			pos_y = pos_y + self.old_cell_height_list[i] + cell_ver_spacing
		-- 			self.active_cell_list[i]:SetAnchoredPosition(0, pos_y)
		-- 		end
		-- 	end
		-- else  -- 从上往下
		-- 	for i = 1, self.cell_act_count do
		-- 		if self.active_cell_list[i] then
		-- 			self.active_cell_list[i]:SetAnchoredPosition(0, max_pos_y - pos_y)
		-- 			pos_y = pos_y + self.old_cell_height_list[i] + cell_ver_spacing
		-- 		end
		-- 	end
		-- end
	end
end

function MainUIView:UpdateChat(now_time, elapse_time)
	self:UpdateChatActiveState(now_time, elapse_time)
	self:RefreshLayout()
	if 0 == #self.cache_tips_data_list then return end
	if self.update_icon_time > now_time then return end
	self.update_icon_time = now_time + self.update_interval_time
	self:UpdateChatIcon()
end

function MainUIView:UpdateChatActiveState(now_time, elapse_time)
	if not self.can_change_chat_state then
		self.last_active_chat_time = now_time + CHANGE_CHAT_ACTIVE_STATE_TIME
		return
	end

	if self.last_active_chat_time > now_time then
		return
	end

	self.last_active_chat_time = now_time + CHANGE_CHAT_ACTIVE_STATE_TIME
	self.last_check_time = TimeWGCtrl.Instance:GetServerTime()
	if self.chat_active_state == CHAT_ACTIVE_STATE.ACTIVE or self.chat_active_state == CHAT_ACTIVE_STATE.OBSERVE then
		self:SetChatActiveState(CHAT_ACTIVE_STATE.STAND)
	end
end

function MainUIView:SetChatActiveState(chat_active_state)
	if self.chat_active_state ~= chat_active_state then
		local need_alpha_tween = false
		local from_alpha, to_alpha = 1, 1
		local container_root_size = Vector2(474, 198)
		local chat_view_root_size = Vector2(474, 202)
		local btn_content_root_size = Vector2(474, 180)
		local chat_head_pos_y = 190

		-- 待机  -->  观察
		if self.chat_active_state == CHAT_ACTIVE_STATE.STAND and chat_active_state == CHAT_ACTIVE_STATE.OBSERVE then
			from_alpha = 0.5
			to_alpha = 1
			need_alpha_tween = true
			chat_view_root_size = Vector2(474, 202)
			container_root_size = Vector2(474, 96)
			btn_content_root_size = Vector2(474, 180)
		elseif self.chat_active_state == CHAT_ACTIVE_STATE.OBSERVE and chat_active_state == CHAT_ACTIVE_STATE.STAND then
		-- 观察  -->  待机
			from_alpha = 1
			to_alpha = 0.5
			need_alpha_tween = true
			chat_view_root_size = Vector2(474, 100)
			container_root_size = Vector2(474, 96)
			btn_content_root_size = Vector2(474, 78)
			chat_head_pos_y = 140
		elseif self.chat_active_state == CHAT_ACTIVE_STATE.ACTIVE and chat_active_state == CHAT_ACTIVE_STATE.STAND then
		-- 活跃 --> 待机
			from_alpha = 1
			to_alpha = 0.5
			need_alpha_tween = true
			chat_view_root_size = Vector2(474, 100)
			container_root_size = Vector2(474, 96)
			btn_content_root_size = Vector2(474, 78)
			chat_head_pos_y = 140
		end

		if need_alpha_tween then
			-- self.node_list.Container.canvas_group:DoAlpha(from_alpha, to_alpha, 0.3)
			self.node_list.BtnContent.canvas_group:DoAlpha(from_alpha, to_alpha, 0.3)
			self.node_list.chat_view_bg.canvas_group:DoAlpha(from_alpha, to_alpha, 0.3)
			self.node_list.Container.rect:DOSizeDelta(chat_view_root_size, 0.3)
			self.node_list.BtnContent.rect:DOSizeDelta(btn_content_root_size, 0.3)
			self.node_list.ChatViewRoot.rect:DOSizeDelta(chat_view_root_size, 0.3)
		end

		self.node_list.spe_chat_head_gruop.rect:DOAnchorPosY(chat_head_pos_y, 0.3)
		self.chat_active_state = chat_active_state
	end
end

function MainUIView:IsCanClickOpenChatWindow()
	local elapse_time = TimeWGCtrl.Instance:GetServerTime() - self.last_check_time
	self.last_active_chat_time = self.last_active_chat_time + elapse_time

	if self.chat_active_state == CHAT_ACTIVE_STATE.STAND then
		self:SetChatActiveState(CHAT_ACTIVE_STATE.OBSERVE)
		return false
	elseif self.chat_active_state == CHAT_ACTIVE_STATE.OBSERVE then
		self:SetChatActiveState(CHAT_ACTIVE_STATE.ACTIVE)
	end

	return true
end

function MainUIView:SetChangeChatState(state)
	self.can_change_chat_state = state
end

--按类型添加或删除提示图标时 ，检测是否特殊处理(好友私聊、邮件提醒)
function MainUIView:InvateCheckSpecialHandle(tip_type, repetition_num, callback, effect_id)
	--邮件提醒
	-- if tip_type == MAINUI_TIP_TYPE.MAIL then
	-- 	self:SetMeailIcon(repetition_num)
	-- 	self.meail_cache_tab = {tip_type = tip_type, callback = callback, repetition_num = repetition_num}
	-- 	return true
	-- end

	return false
end

--  按类型添加或删除提示图标
function MainUIView:InvateTip(tip_type, repetition_num, callback, effect_id, param)
	if nil == self.cache_tips_data_list then
		return
	end
	if not repetition_num then
		print_error("不允许传空数量（repetition_num）进去，提示类型：", tip_type)
		repetition_num = repetition_num or 0
	end
	local flag = false
	if MAINUI_TIP_TYPE_IDX[tip_type] then --临时，所有都放进横
		flag = MainuiWGData.Instance:CheckTheSameTipType(self.cache_tips_data_list, tip_type, repetition_num, callback, effect_id, param)
		--如果没有，缓存
		if not flag then
			local data = {tip_type = tip_type, callback = callback,repetition_num = repetition_num, effect_id = effect_id, callback_param = param}
			self.cache_tips_data_list[#self.cache_tips_data_list + 1] = data
		end
	else --放到变强按钮里面
		local strong_tip_type = nil ~= MAINUI_TIP_TYPE_REWARD[tip_type] and MAINUI_STRONG_TIP_TYPE.GET_REWARD or MAINUI_STRONG_TIP_TYPE.UP_POWER
		MainuiWGData.Instance:SetBianQiangListCache(tip_type, repetition_num, callback, effect_id, param, strong_tip_type)
		if self.bianqiang_update_flag then
			if self.bianqiang_delay_timer then
				GlobalTimerQuest:CancelQuest(self.bianqiang_delay_timer)
				self.bianqiang_delay_timer = nil
			end
			self.bianqiang_delay_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.BianQiangDelayTime,self), 1.5)--延时处理
			self.bianqiang_update_flag = false
			return
		end
		-- self:UpdateBianQiangList()
	end
end

--变强按钮
function MainUIView:UpdateBianQiangList()
	local list = MainuiWGData.Instance:GetBianQiangListCache()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local scene_type = Scene.Instance:GetSceneType()
	local is_show_streng = list[1] ~= nil and (role_level >= COMMON_CONSTS.STRENGTHEN_SHOW_LEVEL)
							and scene_type ~= SceneType.TianShen3v3
	local active = is_show_streng and not IS_AUDIT_VERSION
	self.node_list["btn_bianqiang"]:SetActive(active)
	self.node_list.btn_bianqiang_text.tmp.text = #list

	if active and self.strong_menu_view and self.strong_menu_view:IsOpen() then
		self.strong_menu_view:Flush()
	end

	-- local show_dragon_temple_hatch = false
	-- if not IsEmptyTable(list) then
	-- 	for k, v in pairs(list) do
	-- 		if v.tip_type == MAINUI_TIP_TYPE.DRAGON_TEMP_HATCH then
	-- 			show_dragon_temple_hatch = true
	-- 			break
	-- 		end
	-- 	end
	-- end

	-- self.node_list.dragon_temple_hatch_root:SetActive(show_dragon_temple_hatch)
end

--变强气泡隐藏与重置间隔显示气泡倒计时.
function MainUIView:UpdateBianQiangBubbleTips()
	--间隔显示气泡倒计时存在则不执行.
	if CountDownManager.Instance:HasCountDown("bianqiang_bubble_countdown") then
		return
	end

	--不重复执行.
	local key = "mianui_bianqiang_bubble_countdown"
	if CountDownManager.Instance:HasCountDown(key) then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	CountDownManager.Instance:AddCountDown(key,
		-- 回调方法
		function()
		end,
		-- 倒计时完成回调方法
		function()
			MainuiWGCtrl.Instance:UpdateBianQiangBubbleTips()
			self:ShowBianQiangBubbleTips(false)
		end,
		10 + server_time, nil, 1)
end

function MainUIView:ShowBianQiangBubbleTips(is_show)
	self.node_list.bq_bubble_tips:SetActive(is_show)
	local data_list = MainuiWGData.Instance:GetBianQiangListCache()
	local red_num = #data_list
	self.node_list.bq_bubble_tips_text.tmp.text = string.format(Language.MainUIIcon.BQBubbleTips, red_num)
	self:UpdateBianQiangBubbleTips()
end

function MainUIView:UpdateBianQiangGetRewardList()
	local list = MainuiWGData.Instance:GetBianQiangGetRewardListCache()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local scene_type = Scene.Instance:GetSceneType()
	local is_show_streng = not IsEmptyTable(list) and (role_level >= COMMON_CONSTS.STRENGTHEN_SHOW_LEVEL) and scene_type ~= SceneType.TianShen3v3
	local active = is_show_streng and not IS_AUDIT_VERSION

	self.node_list["btn_bianqiang_get_reward"]:SetActive(active)

	if active and self.strong_get_reward_view and self.strong_get_reward_view:IsOpen() then
		self.strong_get_reward_view:Flush()
	end
end

function MainUIView:BianQiangDelayTime()
	self.bianqiang_update_flag = true
	self:UpdateBianQiangList()
	self:UpdateBianQiangGetRewardList()
	self:UpdateBianQiangBubbleTips()
end

function MainUIView:RemoveStrongList(tip_type)
	local list = MainuiWGData.Instance:GetBianQiangList()

	local index
	for i, v in pairs(list) do
		if v.tip_type == tip_type then
			index = i
			break
		end
	end

	if not index then
		return
	end

	table.remove(list, index)

	MainuiWGData.Instance:UpdateBianQiangListCache()
	self:UpdateBianQiangList()
	self:UpdateBianQiangGetRewardList()
end

function MainUIView:UpdateChatIcon()
	if 0 == #self.cache_tips_data_list then return end
	if not self.is_load_chat then return end
	local data = nil
	for i=1,3 do
		data = table.remove(self.cache_tips_data_list, 1)
		if not data then break end
		if data.tip_type == MAINUI_TIP_TYPE.CHAT_GUILD then -- 仙盟不需要移除
			local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
			if main_role_vo.guild_id <= 0 then
				self.icon_list[Tips_Index.Tips_Guild_Chat]:SetActive(false)
				self:RemoveTipIcon(data.tip_type)
			else
				self.icon_list[Tips_Index.Tips_Guild_Chat]:SetActive(true)
				self:FulshIcon(data.tip_type, data.callback, data.repetition_num, data.effect_id, data.callback_param)
			end
		elseif data.repetition_num > 0 then
			self:FulshIcon(data.tip_type, data.callback, data.repetition_num, data.effect_id, data.callback_param)
		else
			self:RemoveTipIcon(data.tip_type)
		end
	end
	
	self:UpdateChatButtonsStatus()
end

function MainUIView:UpdateChatButtonsStatus()
	if self.node_list.ChatButtons then
		self.node_list.ChatButtons:CustomSetActive(not IsEmptyTable(self.tips_data) and not IS_AUDIT_VERSION)
	end
end

function MainUIView:FlushGuildIcon(is_show_answer_tip)
	if not self.tips_data then
		return
	end
	if is_show_answer_tip ~= nil then
		self.rember_is_show_answer_tip = is_show_answer_tip
	end
	for k,v in pairs(self.tips_data) do
		if MAINUI_TIP_TYPE.CHAT_GUILD == v.tip_type then
			if self.tips_list[k] then
				self.tips_list[k]:SetIsShowAnswerTip(self.rember_is_show_answer_tip)
				self.tips_list[k]:Flush()
				break
			end
		end
	end
end

function MainUIView:FulshIcon(tip_type, callback, repetition_num, effect_id, callback_param)
	if not self.tips_data then
		return
	end

	--如果是特殊处理的直接返回
	if self:InvateCheckSpecialHandle(tip_type, repetition_num, callback, effect_id) then
		return
	end

	for k,v in pairs(self.tips_data) do
		if tip_type == v.tip_type then
			v.repetition_num = repetition_num
			if self.tips_list[k] then
				self.tips_list[k]:SetNumber()
			end
			if callback then
				v.callback = callback
			end
			if callback_param then
				v.callback_param = callback_param
			end
			if self.tips_list[k] then
				self.tips_list[k]:SetData(v)
			end
			return
		end
	end

	for k,v in pairs(self.surplus_tips_data) do
		if tip_type == v.tip_type then
			v.repetition_num = repetition_num
			return
		end
	end

	-- 索引规则
	local index = -1

	--仙盟聊天固定索引 1
	if tip_type == MAINUI_TIP_TYPE.CHAT_GUILD then
		index = Tips_Index.Tips_Guild_Chat
	else
		local i = Tips_Index.Tips_Function_1
		local bool = false
		while(i >= Tips_Index.Tips_Function_1 and i <= Tips_Index.Tips_Function_2) do
			if not self.tips_data[i] then
				bool = true
				break
			end
			i = i + 1
		end
		--如果找到空的就获取它的索引
		if bool then
			index = i
		end
	end

	local data = {tip_type = tip_type, callback = callback,repetition_num = repetition_num, effect_id = effect_id}

	--如果没找到直接添加到剩余列表
	if index <= -1 or index > Tips_Index.Tips_Function_2 then
		--print_error("添加到剩余列表 >>>>>>>>>> ", tip_type, index)
		self.surplus_tips_data[#self.surplus_tips_data + 1] = data
		return
	else
		--找到了直接塞数据
		--print_error("找到了直接塞数据 >>>>>>>>>> ", tip_type, index)
		self.tips_data[index] = data
	end
	-- table.insert(self.tips_data, data)
	for k,v in pairs(self.tips_data) do
		if self.icon_list[k] then
			if nil == self.tips_list[k] then
				self.tips_list[k] = MainuiIcon.New(self.icon_list[k])
				self.tips_list[k]:SetIndex(tonumber(k))
				self:DoTipOpenAni(self.tips_list[k])
			end
			if not self.tips_list[k]:GetActive() then
				self:DoTipOpenAni(self.tips_list[k])
			end
			self.tips_list[k]:SetActive(true)
			self.icon_list[k]:SetActive(true)
			self.tips_list[k]:SetData(v)
		end
	end

	self:UpdateChatButtonsStatus()
end

function MainUIView:DoTipOpenAni(icon)
	icon.view.transform.localScale = Vector3(1,1,1)
	icon.view.transform:DOPunchScale(Vector3(0.2, 0.2, 0.2), 0.3):OnComplete(function()
		icon.view.transform.localScale = Vector3(1,1,1)
	end) --在 0.7 秒内在原始比例和下面比例之间，来回冲压变化
end

-- 按类型删除提示图标时检测特殊处理
function MainUIView:RemoveCheckSpecialHandle(tip_type)
	-- if tip_type == MAINUI_TIP_TYPE.MAIL then --邮件特殊处理 2019-08-08 10:57
	-- 	if self.node_list["mail_btn"] then
    --         self.node_list["mail_btn"]:SetActive(false)
    --         self.last_mail_num = -1
	-- 		RemindManager.Instance:Fire(RemindName.SocietyFriends)
	-- 		RemindManager.Instance:Fire(RemindName.SocietyFriends2)
	-- 	end

	-- 	if self.node_list.btn_societyt then
	-- 		self.node_list.btn_societyt:SetActive(true)
	-- 	end

	-- 	self.meail_cache_tab = nil
	-- 	return true
	-- end

	return false
end

-- 设置右键icon
-- function MainUIView:SetMeailIcon(num)
--     self:SetFriendRemind()
-- 	if num <= 0 then
-- 		self.node_list["mail_btn"]:SetActive(false)
-- 		self.node_list.btn_societyt:SetActive(true)
-- 	else
-- 		self.node_list["mail_btn"]:SetActive(true)
-- 		self.node_list.btn_societyt:SetActive(false)
-- 	end
-- end

-- 按类型删除提示图标时检测特殊处理
function MainUIView:SetFriendRemind()
	-- if self.node_list["society_privite_msg_remind"] then
	-- 	-- local is_show = SocietyWGData.Instance:GetFirendRedShow()
	-- 	local is_show = RemindManager.Instance:GetRemind(RemindName.SocietyFriends) > 0
	-- 	self.node_list["society_privite_msg_remind"]:SetActive(is_show)
	-- end

	if self.node_list.btn_task_mail_red then
		local is_show = RemindManager.Instance:GetRemind(RemindName.SocietyFriends) > 0
		self.node_list.btn_task_mail_red:SetActive(is_show)
	end
end

--  按类型删除提示图标
function MainUIView:RemoveTipIcon(tip_type, is_strong)
	--如果是特殊处理的，直接返回
	if self:RemoveCheckSpecialHandle(tip_type) then
		return
	end

	if is_strong then
		self:RemoveStrongList(tip_type)
		return
	end

	if not self.tips_data then return end
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if tip_type == MAINUI_TIP_TYPE.CHAT_GUILD and main_role_vo.guild_id > 0 then
		if self.tips_data[Tips_Index.Tips_Guild_Chat] then
			self.tips_list[Tips_Index.Tips_Guild_Chat]:SetActive(false)
			self.icon_list[Tips_Index.Tips_Guild_Chat]:SetActive(false)
			self.tips_data[Tips_Index.Tips_Guild_Chat] = nil
		end

		self:UpdateChatButtonsStatus()
		return
	end
	local remove_index = -1

	for k,v in pairs(self.tips_data) do
		if tip_type == v.tip_type then
			if self.tips_list[k] then
				self.tips_list[k]:SetActive(false)
				self.icon_list[k]:SetActive(false)
			end
			self.tips_data[k] = nil

			remove_index = k
			break
		end
	end
	
	self:UpdateChatButtonsStatus()

	local len = #self.surplus_tips_data
	for i=1,len do
		if self.surplus_tips_data[i] and tip_type == self.surplus_tips_data[i].tip_type then
			table.remove(self.surplus_tips_data, i)
			return
		end
	end

	if tip_type ~= MAINUI_TIP_TYPE.CHAT_GUILD then
	    -- 减少后，没有多余的重新排序
		if #self.surplus_tips_data <= 0 then
			local list = {}

			local index = Tips_Index.Tips_Function_1
			while(index >= Tips_Index.Tips_Function_1 and index <= Tips_Index.Tips_Function_2) do
				if self.tips_data[index] then
					list[#list + 1] = self.tips_data[index]
				end
				index = index + 1
			end

			index = 1
			for i=Tips_Index.Tips_Function_1,Tips_Index.Tips_Function_2 do
				self.tips_data[i] = list[index]
				index = index + 1
				if self.tips_list[i] then
					self.tips_list[i]:SetActive(nil ~= self.tips_data[i])
					self.icon_list[i]:SetActive(nil ~= self.tips_data[i])
					if self.tips_data[i] then
						self.tips_list[i]:SetData(self.tips_data[i])
					end
				end
			end
		else
			--有多余的直接添加到移除的索引
			if remove_index > -1 then
				local data = table.remove(self.surplus_tips_data, 1)
				--print_error("有多余的直接添加到移除的索引 >>>>>>>", remove_index, data.tip_type)
				self.tips_data[remove_index] = data
				if self.tips_list[remove_index] then
					self.tips_list[remove_index]:SetActive(true)
					self.icon_list[remove_index]:SetActive(true)
					self.tips_list[remove_index]:SetData(data)
				end
			end
		end

		self:UpdateChatButtonsStatus()

		---- 多余的，重新添加进去
		--if self.surplus_tips_data[1] then
		--	 print_error("RemoveTipIcon:::", tip_type, self.surplus_tips_data[1].tip_type)
		--	local data = table.remove(self.surplus_tips_data, 1)
		--	self:InvateTip(data.tip_type, data.repetition_num, data.callback, data.effect_id)
		--end
	end
end

--显示隐藏 御龙按钮
function MainUIView:ChangeYulongBtnState()
	local use_skill_end_time, next_use_skill_time, use_skill_start_time = NewFightMountWGData.Instance:GetUseSkillTimeInfo()
	local use_skill_id = NewFightMountWGData.Instance:GetUseSkillId()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self.node_list.yulong_btn:SetActive(use_skill_id >= 0 or server_time <= use_skill_end_time)
	
	local can_use_skill = server_time >= next_use_skill_time and use_skill_id >= 0
	if can_use_skill then
		self.node_list["yulong_progress_bg"]:SetActive(true)
		self.node_list["yulong_progress_bg"].slider.value = 1
		self.node_list.yulong_cd_mask:SetActive(false)
		self.node_list.yulong_cd_time:SetActive(false)
		self.node_list.yulong_hight_light:SetActive(false)
	else
		self:FlushYulongCountDown(next_use_skill_time, use_skill_end_time)
	end

	self:IsShowJumpBtn()
	self:SetZuoqiBtnShowState((server_time + 1) >= use_skill_end_time) -- 加一秒误差
	self.node_list.yulong_anniu_eff:SetActive(can_use_skill)
end

function MainUIView:FlushYulongCountDown(next_use_skill_time, use_skill_end_time)
	local key = "mianui_yulong_countdown"
	if CountDownManager.Instance:HasCountDown(key) then
		CountDownManager.Instance:RemoveCountDown(key)
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self:YulongFlushTime(0, next_use_skill_time - server_time)
	if next_use_skill_time > server_time then
		CountDownManager.Instance:AddCountDown(key, BindTool.Bind1(self.YulongFlushTime, self), BindTool.Bind1(self.YulongComPlete, self), next_use_skill_time, nil, 0.1)
	end
end

function MainUIView:YulongFlushTime(elapse_time, total_time)
	local use_skill_end_time, next_use_skill_time, use_skill_start_time = NewFightMountWGData.Instance:GetUseSkillTimeInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local yulongzhong = use_skill_end_time >= server_time

	local other_cfg = NewFightMountWGData.Instance:GetOtherCfg()
	if yulongzhong then
		self.node_list["yulong_progress_bg"]:SetActive(true)
		local value = (use_skill_end_time - server_time) / (use_skill_end_time - use_skill_start_time)
		self.node_list["yulong_progress_bg"].slider.value = value
		self.node_list.yulong_cd_mask:SetActive(false)
		self.node_list.yulong_cd_time:SetActive(false)
		self.node_list.yulong_hight_light:SetActive(true)
		self.node_list["yulong_hight_light"].transform.localRotation = Quaternion.Euler(0, 0, -360 * (1 - value))
	else
		self.node_list["yulong_progress_bg"]:SetActive(false)
		self.node_list.yulong_hight_light:SetActive(false)
		self.node_list.yulong_cd_mask:SetActive(true)
		self.node_list.yulong_cd_time:SetActive(true)
		self.node_list.yulong_cd_time.text.text = string.format("%0.1f", next_use_skill_time - server_time)
		self.node_list.yulong_cd_mask.image.fillAmount = (next_use_skill_time - server_time) / other_cfg.cool_down_time
	end
end

function MainUIView:YulongComPlete()
	self:ChangeYulongBtnState()
end

-- function MainUIView:ShowActDiscountBtn(is_show)
-- 	if self.node_list["btn_act_discount"] ~= nil then
-- 		self.node_list["btn_act_discount"]:SetActive(is_show)
-- 	end
-- end

function MainUIView:OnClickOpenActDiscoount()
	ViewManager.Instance:Open(GuideModuleName.ActDiscount)
end

function MainUIView:UpdateTransmitEffect()

end


------------------Sit-----------------------
function MainUIView:OnClickSit()
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
		return
	end

	if main_role:IsJump() or main_role:IsQingGong() or YunbiaoWGData.Instance:GetIsHuShong() or main_role:IsMitsurugi() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Sit.NoCanSit)
		return
	end

	if main_role:IsGhost() or main_role:IsRidingFightMount() or main_role.vo.special_appearance > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Sit.NoCanSit)
		return
	end

	if main_role:GetIsInSit() then
		return
	end

	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	main_role:TrySit()
end

function MainUIView:CancleSitShow()
	-- MainuiWGCtrl.Instance:PlayAutoSitAni(false)
	if self.node_list.btn_sit then
		self.node_list.btn_sit:SetActive(false)
	end

	if self.node_list.root_sitinfo then
		self.node_list.root_sitinfo:SetActive(false)
	end

	if self.node_list.root_sit then
		self.node_list.root_sit:SetActive(false)
	end
end

function MainUIView:FlushSitInfo()
	local main_role = Scene.Instance:GetMainRole()
	if not main_role or not main_role:GetIsInSit() then
		return
	end

	local lingli_xiaolv = OfflineRestWGData.Instance:GetSitLingLiXiaoLv()
	self.node_list.label_sit_exp.text.text = CommonDataManager.NotConverExpExtend(lingli_xiaolv)
end

-- 点击取消变身
function MainUIView:OnClickCancelBianShen()

end

function MainUIView:GetLimitGiftBtnTrans()
	return self.node_list["task_btn_trans"].gameObject.transform
end

function MainUIView:ForceRebuildLimitGiftBtnTrans()
	local hor_layout = self.node_list["task_btn_trans"].horizontal_layout_group
	
	if hor_layout then
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["task_btn_trans"].rect)
	end
end

function MainUIView:SetLimitGiftBtnTransActive(enable)
	return self.node_list["task_btn_trans"].gameObject:SetActive(enable and not IS_AUDIT_VERSION)
end

-- function MainUIView:SetGuoJiaYuGaoStatus(value,red)
-- 	if self.node_list and self.node_list["mainui_btn_country_yugao"] then
-- 		self.node_list["mainui_btn_country_yugao"]:SetActive(value)
-- 		self.node_list["country_yugao_red"]:SetActive(red > 0)
-- 	end
-- end

-- function MainUIView:OnChatLeftShrinkBtnValueChange(ison)
-- 	self.chat_left_shrink_ison = ison
-- 	local tween_sequence = DG.Tweening.DOTween.Sequence()
-- 	local tween_move = self.node_list.Container.rect:DOAnchorPosX(ison and 0 or -1000, 0.75)
-- 	-- tween_move:SetEase(DG.Tweening.Ease.InOutBack)
-- 	local from_alpha = ison and 0 or 1
-- 	local to_alpha = ison and 1 or 0
-- 	local tween_alpha = self.node_list.Container.canvas_group:DoAlpha(from_alpha, to_alpha, 0.75)
-- 	tween_sequence:Append(tween_move)
-- 	tween_sequence:Join(tween_alpha)

-- 	self:CheckChatLeftShrinkRemind()
-- end

-- function MainUIView:CheckChatLeftShrinkRemind()
-- 	local is_show_chat_left_remind = false
-- 	if self.chat_left_shrink_ison then
-- 		self:ChengeChatLeftShrinkBtnRemindEnable(is_show_chat_left_remind)
-- 		return
-- 	end

-- 	-- 固定功能 + 活动
-- 	if not IsEmptyTable(self.chat_activity_btn_list) then
-- 		for act_id, v in pairs(self.chat_activity_btn_list) do
-- 			if v:RedPointIsShow() then
-- 				is_show_chat_left_remind = true
-- 				break
-- 			end
-- 		end
-- 	end

-- 	-- if not is_show_chat_left_remind then
-- 	-- 	for k, v in pairs(CHAT_PANEL_FUNC_CFG) do
-- 	-- 		local is_show = RemindManager.Instance:GetRemind(v.remind) > 0
-- 	-- 		is_show_chat_left_remind = true
-- 	-- 		break
-- 	-- 	end
-- 	-- end

-- 	self:ChengeChatLeftShrinkBtnRemindEnable(is_show_chat_left_remind)
-- end

function MainUIView:ChengeChatLeftShrinkBtnRemindEnable(enable)
	if self.node_list["chat_shrink_left_btn_remind"] then
		self.node_list["chat_shrink_left_btn_remind"]:CustomSetActive(enable)
	end
end

-- 私聊头像
function MainUIView:SetChatPrivateUnreadMsgHead()
	local data_list = SocietyWGData.Instance:GetUnreadMsgRoleInfo()

	for i = 1, 2 do
		local data = data_list[i]

		if IsEmptyTable(data) then
			self.node_list["chat_head_" .. i]:CustomSetActive(false)
		else
			local fashion_photoframe = data.fashion_photoframe or 0
			local data = {role_id = data.user_id, prof = data.prof, sex = data.sex, fashion_photoframe = fashion_photoframe}
			self["chat_head_" .. i]:SetData(data)
			self["chat_head_" .. i]:SetGray(data.is_online == 0)
			self.node_list["chat_head_" .. i]:CustomSetActive(true)
		end
	end
end

function MainUIView:OnClickChatPrivateUnreadMsgHead()
	ViewManager.Instance:Open(GuideModuleName.Society, SocietyView.Tab_F)
end

function MainUIView:FlushSpeFbSendFlowerMsgHead()
	local spe_send_flower_list = FuBenTeamCommonBossWGData.Instance:GetSpeSendFlowerList()
	for i = 1, 4 do
		local flower_info = spe_send_flower_list[i]
		if not flower_info then
			self.node_list["spe_fb_send_flower_" .. i]:CustomSetActive(false)
		else
			local data = {}
			if flower_info.player_is_robot == 1 then
				data.role_id = 0
			else
				data.role_id = flower_info.player_uuid.temp_low
			end
	
			data.prof = flower_info.prof
			data.sex = flower_info.sex
			data.fashion_photoframe = flower_info.shizhuang_photoframe
			if self.spe_fb_send_flower_head_list[i] then
				self.spe_fb_send_flower_head_list[i]:SetData(data)
				self.node_list["spe_fb_send_flower_" .. i]:CustomSetActive(true)
			end
		end
	end
end

function MainUIView:OnClickSpeFbSendFlowerInfo(index)
	FuBenTeamCommonBossWGCtrl.Instance:OpenSendFlowerView(index)
end
------------------------------------------------------------------
MainUIChatCell = MainUIChatCell or BaseClass(BaseRender)

function MainUIChatCell:__init()
	self.rich_text = self.node_list["Content"]
	self.msg_id = -1
	self.is_dirty = true
end

function MainUIChatCell:LoadCallBack()
	self.rich_text = self.node_list["Content"]
	XUI.AddClickEventListener(self.node_list.ChatInfo, BindTool.Bind(self.OnClickOpenWindow, self))
end

function MainUIChatCell:__delete()
	self.voice_animator = nil
	self.discount_anictrl = nil
	self.voice_obj = nil
end

function MainUIChatCell:PlayOrStopVoice(file_name)
	ChatWGCtrl.Instance:ClearPlayVoiceList()
	ChatWGCtrl.Instance:SetStartPlayVoiceState(false)
	local call_back = BindTool.Bind(self.ChangeVoiceAni, self)
	ChatRecordMgr.Instance:PlayVoice(file_name, call_back, call_back)
end

function MainUIChatCell:PlayOrStopFeesVoice(file_id)
	ChatWGCtrl.Instance:ClearPlayVoiceList()
	ChatWGCtrl.Instance:SetStartPlayVoiceState(false)
	local call_back = BindTool.Bind(self.ChangeVoiceAni, self)
	AudioService.Instance:PlayFeesAudio(file_id, call_back)
end

function MainUIChatCell:ChangeVoiceAni(state)
	if self.voice_animator and self.voice_animator.gameObject and not IsNil(self.voice_animator.gameObject) then
		self.voice_animator:SetBool("play", state)
	end
end
--控制点击可以跳到对应的聊天界面页签
function MainUIChatCell:OnClickOpenWindow()
	MainuiWGCtrl.Instance.view:OnClickOpenChatWindow(self.data.channel_type)
end

function MainUIChatCell:GetMsgID()
	return self.msg_id
end

function MainUIChatCell:IsMonsterMsg()
    return self.data and self.data.is_monster and self.data.is_monster == true and self.data.monster_id
end

function MainUIChatCell:SetData(data)
	if data == nil then
		return
	end

	if self.msg_id ~= data.msg_id then
		self.is_dirty = true
	end
	self.msg_id = data.msg_id

	if not self.is_dirty then
		return
	end
	self.is_dirty = false

	if nil ~= self.voice_obj then
		self.voice_obj:SetActive(false)
	end

	if IsNil(self.rich_text.gameObject) then
		self.rich_text = self.node_list["Content"]
	end

	self.data = data
	
	local title_text = ""
	-- if data.channel_type == CHANNEL_TYPE.CHUAN_WEN then
	-- 	title_text = Language.ChannelColor2[100]
	-- else
	-- 	title_text = Language.ChannelColor2[data.channel_type or 0]
	-- end

	local bundle, asset = ResPath.GetChatType(CHANNEL_TYPE.WORLD)
	if CanShowChannel[data.channel_type] then
		bundle, asset = ResPath.GetChatType(data.channel_type)
	end
	self.node_list.title_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.title_icon.image:SetNativeSize()
	end)

	local content = data.content


	if self.data.msg_reason == CHAT_MSG_RESSON.HUDONG_PYP then ---拍一拍消息
		content = ChatWGData.Instance:GetPYPShowStr(data)
	else
		content = ChatWGData.Instance:TryAnalysisAiteMsg(data.content, true)
	end

	local name = data.username
	local color = COLOR3B.D_BLUE
	local str_list = Split(name, "_")
	local split_name = data.username

	if data.channel_type == CHANNEL_TYPE.CROSS then
		if #str_list > 1 then
			split_name = name
		else
			split_name = string.format("%s_s%s", name, self.data.merge_server_id or self.data.from_role_server_id)
		end
	else
		if self.data.from_uid ~= self.data.from_cross_uuid and #str_list > 1 then
			split_name = ""--跨服场景中,非跨服频道不显示服务器id

			for i=1,#str_list - 1 do
				split_name = split_name .. str_list[i]
			end
		else
			split_name = name
		end
	end

	if data.speaker_type and data.speaker_type == SPEAKER_TYPE.SPEAKER_TYPE_LOCAL then
		split_name = name
	end

	-- 第一行有表情， 频道类型图标下移
	local title_icon_pos_y = -3
	local temp_str = string.format("%s:%s", split_name, content)
	local calc_str = StringLenBySpace(temp_str, 35, true)
	-- local emoji_start_index, emoji_end_index = string.find(calc_str, "%[%d+%]")
	-- if emoji_start_index and emoji_end_index and (emoji_end_index - emoji_start_index) == 4 then
	-- 	title_icon_pos_y = -23
	-- end

	local pattern = '<sprite name="[^"]*">'
	local emoji_start_index, emoji_end_index = string.find(content, pattern)
	-- 是否有图在第一行
	if emoji_start_index and emoji_end_index and (emoji_end_index <= 56)  then
		title_icon_pos_y = -23
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.title_icon.rect, -198, title_icon_pos_y)

	if split_name and split_name ~= "" and data.username ~= Language.Channel[6] then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local str_name = ""
		if role_vo.role_id == data.from_uid or role_vo.origin_uid == data.from_uid then
			color = F2ChatTextColor[0]
		else
			color = F2ChatTextColor[11]
		end

		str_name = string.format("{wordcolor;%s;%s:}", color, split_name)
		if self.data.msg_reason == CHAT_MSG_RESSON.GUILD_TIPS then ---帮派
			content = "" .. content
		else
			content = str_name .. content
		end
	end

	--是否语音
	if data.content_type == CHAT_CONTENT_TYPE.AUDIO then
		local temp_str = data.content
		local tbl = {}
		for i = 1, 3 do
			local j, k = string.find(temp_str, "(%d+)")
			if j and k then
				local num = string.sub(temp_str, j, k)
				temp_str = string.gsub(temp_str, num, "num")
				table.insert(tbl, num)
			end
		end

		local show_name = title_text .. split_name..":"
		EmojiTextUtil.ParseRichText(self.rich_text.tmp, show_name, nil, color, true)
		local bounds = TMPUtil.GetTMPBounds(self.rich_text.tmp, show_name,382,true)
		local rect = self.rich_text.tmp:GetComponent(typeof(UnityEngine.RectTransform))
		rect.sizeDelta = bounds

		local callback = BindTool.Bind(self.PlayOrStopVoice, self)
		self:SetVoiceContent(self.node_list["ChatInfo"], tbl, callback, data.content)
		return
	elseif data.content_type == CHAT_CONTENT_TYPE.FEES_AUDIO then
		local content_t = Split(data.content, "_")
		if #content_t ~= 3 then
			return
		end

		local show_name = title_text .. split_name..":"
		EmojiTextUtil.ParseRichText(self.rich_text.tmp, show_name, nil, color, true)

		local bounds = TMPUtil.GetTMPBounds(self.rich_text.tmp, show_name,382,true)
		local rect = self.rich_text.tmp:GetComponent(typeof(UnityEngine.RectTransform))
		rect.sizeDelta = bounds

		local callback = BindTool.Bind(self.PlayOrStopFeesVoice, self)
		self:SetVoiceContent(self.node_list["ChatInfo"], content_t[3], callback, content_t[1], content_t[2])
		return
	end

	color = COLOR3B.DEFAULT
	local text = title_text .. content

	local tmp = self.rich_text:GetComponent(typeof(TMPro.TextMeshProUGUI))
	EmojiTextUtil.ParseRichText(tmp, text, nil, color, true, true, RICH_CONTENT_TYPE.MAIN_UI)
	-- 计算宽高
	local bounds = TMPUtil.GetTMPBounds(tmp, tmp.text, 382)
	-- 设置文本宽高
	local tmp_rect = self.rich_text:GetComponent(typeof(UnityEngine.RectTransform))
	tmp_rect.sizeDelta = bounds
	local heigt = self:GetContentHeight()
	self.view.rect.sizeDelta = Vector2(self.view.rect.sizeDelta.x, heigt)


end

function MainUIChatCell:GetData()
	return self.data or {}
end

function MainUIChatCell:SetIndex(index)
	self.index = index
end

function MainUIChatCell:GetIndex()
	return self.index
end

function MainUIChatCell:GetContentHeight()
	local rect = self.node_list["Content"].rect
	self.size_delta = RectTransform.GetSizeDelta(rect, self.size_delta)
	local hegiht = self.size_delta.y < 18 and 18 or self.size_delta.y
	-- 距离顶部偏移4像素
	return hegiht -- + 4
end

function MainUIChatCell:ClickCallBack(callback, file_name)
	if callback then
		callback(file_name)
	end
end

function MainUIChatCell:SetVoiceContent(obj_content, play_time, callback, file_name, str)
	if not obj_content then
		return
	end

	local btn_name = "VioceButtonLeft"
	local assetbundle = "uis/view/miscpre_load_prefab"
	if nil == self.voice_obj then
		self.voice_obj = self:CreateChatContent(assetbundle, btn_name)
		self.voice_obj.transform:SetParent(obj_content.transform, false)
	else
		self.voice_obj:SetActive(true)
	end
	local size = self.node_list["Content"].transform.sizeDelta
	self.voice_obj.rect.anchoredPosition = Vector2(size.x+79, -4)

	local time = 0
	if "table" == type(play_time) then
		time = play_time[3]
	else
		time = play_time
	end

	local name_table = self.voice_obj:GetComponent(typeof(UINameTable))
	local time_node = U3DObject(name_table:Find("TxtTime"))
	time_node.text.text = time

	local content_node = U3DObject(name_table:Find("Content"))
	local content = ""
	if nil ~= str and "" ~= str then
		str = ChatFilter.Instance:Filter(str)
		content = ChatWGData.Instance:SubStringUTF8(str, 1, 4) .. "..."
	end
	content_node.text.text = content

	local btn_node = U3DObject(name_table:Find("VioceButton"))
	btn_node.button:AddClickListener(BindTool.Bind(self.ClickCallBack, self, callback, file_name))
	self.voice_animator = self.voice_obj:GetComponent(typeof(UnityEngine.Animator))
end

function MainUIChatCell:CreateChatContent(assetbundle, prefab_name)
	local gameobj = ResPoolMgr:TryGetGameObject(assetbundle, prefab_name)
	local obj = U3DObject(
		gameobj,
		gameobj.transform,
		self
	)
	return obj
end