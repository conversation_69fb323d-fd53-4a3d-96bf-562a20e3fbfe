CultivationStageShowView = CultivationStageShowView or BaseClass(SafeBaseView)

function CultivationStageShowView:__init()
	self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(800, 500)})
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_cultivation_stage_show_view")
end

function CultivationStageShowView:LoadCallBack()
    if not self.stage_list then
        self.stage_list = AsyncBaseGrid.New()
        self.stage_list:SetStartZeroIndex(true)
        local bundle = "uis/view/cultivation_ui_prefab"
		local asset = "stage_cell_item"
		self.stage_list:CreateCells({col = 7, change_cells_num = 1, list_view = self.node_list["stage_list"],
			assetBundle = bundle, assetName = asset, itemRender = CultivationStageCellItemRender})
    end

    self.node_list.title_view_name.text.text = Language.Cultivation.StageShowTitle
end

function CultivationStageShowView:ReleaseCallBack()
    if self.stage_list then
        self.stage_list:DeleteMe()
        self.stage_list = nil
    end
end

function CultivationStageShowView:OnFlush()
    local data_list = CultivationWGData.Instance:GetXiuWeiStageCfg()
    self.stage_list:SetDataList(data_list)
end

CultivationStageCellItemRender = CultivationStageCellItemRender or BaseClass(BaseRender)

function CultivationStageCellItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset = ResPath.GetCultivationStageIcon(self.data.stage_icon)
    self.node_list.stage_icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.stage_icon.image:SetNativeSize()
    end)

    self.node_list.stage_name.text.text = self.data.stage_title
end