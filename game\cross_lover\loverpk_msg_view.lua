LoverPkMsgView = LoverPkMsgView or BaseClass(SafeBaseView)

function LoverPkMsgView:__init()
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
    self.default_index = TabIndex.lover_pk_msg_jbjl

	local view_bundle = "uis/view/cross_lover_prefab"
    self:AddViewResource(0, view_bundle, "layout_loverpk_msg_bg")
    self:AddViewResource(TabIndex.lover_pk_msg_jbjl, view_bundle, "layout_loverpk_msg_jbjl")
    self:AddViewResource(TabIndex.lover_pk_msg_fsjl, view_bundle, "layout_loverpk_msg_fsjl")
	self:AddViewResource(TabIndex.lover_pk_msg_dzb, view_bundle, "layout_loverpk_msg_dzb")
    self:AddViewResource(0, view_bundle, "LoverPKMsgVerticalTabbar")

	self.remind_tab = {
		{},
		{},
		{RemindName.LoverPKDZB},
	}
end

function LoverPkMsgView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:FsjlReleaseCallBack()
	self:JbjlReleaseCallBack()
	self:DzbReleaseCallBack()
end

function LoverPkMsgView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "LoverPKMsgVerticalTabbar")
		local view_bundle = "uis/view/cross_lover_prefab"
		self.tabbar:SetVerTabbarCellName("LoverPKMsgVerticalTabbarCell")
		self.tabbar:Init(Language.LoverPK.MsgTabGroup, nil, view_bundle, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end
end

function LoverPkMsgView:LoadIndexCallBack(index)
	if index == TabIndex.lover_pk_msg_jbjl then
		self:JbjlLoadIndexCallBack()
	elseif index == TabIndex.lover_pk_msg_fsjl then
		self:FsjlLoadIndexCallBack()
	elseif index == TabIndex.lover_pk_msg_dzb then
		self:DzbLoadIndexCallBack()
	end
end

function LoverPkMsgView:ShowIndexCallBack(index)
	if index == TabIndex.lover_pk_msg_jbjl then
		self:JbjlShowIndexCallBack()
	elseif index == TabIndex.lover_pk_msg_fsjl then
		self:FsjlShowIndexCallBack()
	elseif index == TabIndex.lover_pk_msg_dzb then
		self:DzbShowIndexCallBack()
	end
end

function LoverPkMsgView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
        if "all" == k then
			if index == TabIndex.lover_pk_msg_jbjl then
				self:JbjlOnFlush()
			elseif index == TabIndex.lover_pk_msg_fsjl then
				self:FsjlOnFlush()
			elseif index == TabIndex.lover_pk_msg_dzb then
				self:DzbOnFlush()
			end
        end
    end
end