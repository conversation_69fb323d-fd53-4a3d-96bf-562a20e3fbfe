RoleBagMeltingView = RoleBagMeltingView or BaseClass(SafeBaseView)

-- 同步 Language.Bag.NameList2
local index_map_color_list = {
	5, 	--红色
	4, 	--橙色
	3,	--紫色
}

-- 同步 Language.Bag.NameList1
-- 21 代表目前最大阶数以下
local index_map_order_list = {20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 21}

function RoleBagMeltingView:__init()
	self:SetMaskBg(true)
    self.view_name = "RoleBagMeltingView"
	self.view_layer = UiLayer.Normal

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(930, 600)})
	self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_bag_melting_view")

	self.item_data_event = BindTool.Bind(self.BagDataChange, self)
end

function RoleBagMeltingView:ReleaseCallBack()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)

	if self.item_grid then
        self.item_grid:DeleteMe()
        self.item_grid = nil
    end

	if self.color_list_view then
		self.color_list_view:DeleteMe()
		self.color_list_view = nil
	end

	if self.order_list_view then
		self.order_list_view:DeleteMe()
		self.order_list_view = nil
	end

	if self.auto_tunshi_alert then
		self.auto_tunshi_alert:DeleteMe()
		self.auto_tunshi_alert = nil
	end

	self.cur_select_color_index = nil
	self.cur_select_order_index = nil

	self:ReleaseTunShiOpa()
end

function RoleBagMeltingView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Bag.MeltingName

	if not self.item_grid then
        self.item_grid = BagMeltingGrid.New()
        self.item_grid:SetStartZeroIndex(false)
        self.item_grid:SetIsMultiSelect(true)
        self.item_grid:CreateCells({
            col = 5,
            cell_count = 45,
            list_view = self.node_list["item_grid"],
            itemRender = MeltEquipCell,
            change_cells_num = 2,
        })
        self.item_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectItemCB, self))
    end

	-- 品质
	self.color_list_view = AsyncListView.New(MeltingPinZhiListRender, self.node_list["color_list"])
	self.color_list_view:SetSelectCallBack(BindTool.Bind(self.SelectColorCallBack, self))
	self.cur_select_color_index = RoleWGData.GetRolePlayerPrefsInt("melting_select_pinzhi_color")
	if self.cur_select_color_index < 1 then
		self.cur_select_color_index = 2 -- 默认橙色
		RoleWGData.SetRolePlayerPrefsInt("melting_select_pinzhi_color", self.cur_select_color_index)
	end

	self.color_list_view:SetDefaultSelectIndex(self.cur_select_color_index)
	self.color_list_view:SetDataList(Language.Bag.NameList2)
	self.node_list["cur_color_text"].text.text = Language.Bag.NameList2[self.cur_select_color_index]

	-- 阶数
	self.order_list_view = AsyncListView.New(MeltingPinJieListRender, self.node_list["order_list"])
	self.order_list_view:SetSelectCallBack(BindTool.Bind(self.SelectOrderCallBack, self))
	local order_show_list, cur_max_order = RoleBagWGData.Instance:GetBagEquipMeltShowGradeList()

	-- 保存玩家的选项，但在跨等级段时候，按等级拿一次
	local cache_max_order = RoleWGData.GetRolePlayerPrefsInt("melting_max_pinjie_num")
	if cur_max_order ~= cache_max_order then
		RoleWGData.SetRolePlayerPrefsInt("melting_max_pinjie_num", cur_max_order)
		self.cur_select_order_index = cur_max_order
	else
		self.cur_select_order_index = RoleWGData.GetRolePlayerPrefsInt("melting_select_pinjie_num")
	end

	if self.cur_select_order_index == nil or self.cur_select_order_index < 1 then
		self.cur_select_order_index = #index_map_order_list
	end

	self.order_list_view:SetDataList(order_show_list)
	self.order_list_view:SetDefaultSelectIndex(self.cur_select_order_index)
	self.node_list["cur_order_text"].text.text = Language.Bag.NameList1[self.cur_select_order_index] or ""

	self.is_show_color_part = true
	self:OnClickSelectColor()
	XUI.AddClickEventListener(self.node_list["btn_select_color"], BindTool.Bind(self.OnClickSelectColor, self))
	XUI.AddClickEventListener(self.node_list["close_color_list_part"], BindTool.Bind(self.OnClickSelectColor, self))

	self.is_show_order_part = true
	self:OnclickSelectOrder()
	XUI.AddClickEventListener(self.node_list["btn_select_order"], BindTool.Bind(self.OnclickSelectOrder, self))
	XUI.AddClickEventListener(self.node_list["close_order_list_part"], BindTool.Bind(self.OnclickSelectOrder, self))

	XUI.AddClickEventListener(self.node_list["btn_melting"], BindTool.Bind(self.OnClickMelting, self))
	XUI.AddClickEventListener(self.node_list.button_auto_tunshi, BindTool.Bind(self.OnTunShiToggleClick, self))

	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)

	self:InitTunShiOpa()
	XUI.AddClickEventListener(self.node_list.btn_active_vip, BindTool.Bind(self.OnClickActiveVipBtn, self))
end

function RoleBagMeltingView:CloseCallBack()
	if self.cur_select_color_index then
		RoleWGData.SetRolePlayerPrefsInt("melting_select_pinzhi_color", self.cur_select_color_index)
	end

	if self.cur_select_order_index then
		RoleWGData.SetRolePlayerPrefsInt("melting_select_pinjie_num", self.cur_select_order_index)
	end
end

-- 选择道具回调
function RoleBagMeltingView:OnBagSelectItemCB(cell)
    self:CalcSelect()
end

function RoleBagMeltingView:OnFlush()
    local item_list = RoleBagWGData.Instance:GetBagEquipMeltList()
    self.item_grid:SetDataList(item_list)
	self:OneKeySelect()
	self:SetAutoTunShiSelect()
	-- self:FlushOpaView()
end

-- 一键选择
function RoleBagMeltingView:OneKeySelect()
	local color = index_map_color_list[self.cur_select_color_index] or 0
	local order = index_map_order_list[self.cur_select_order_index] or 0

    self.item_grid:SetMeltingOneKeySelcet(color, order)
	self:CalcSelect()
end

-- 选择品质回调
function RoleBagMeltingView:SelectColorCallBack(cell)
	local data = cell and cell:GetData()
	if data == nil then
		return
	end

	local index = cell:GetIndex()
	if index == self.cur_select_color_index then
		return
	end

	self.cur_select_color_index = index
	self.node_list["cur_color_text"].text.text = Language.Bag.NameList2[self.cur_select_color_index]
	self:OnClickSelectColor()
	self:OneKeySelect()
end

-- 展开品质筛选列表
function RoleBagMeltingView:OnClickSelectColor()
	self.is_show_color_part = not self.is_show_color_part
	self.node_list["color_list_part"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_down"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_up"]:SetActive(not self.is_show_color_part)
end

-- 选择阶数回调
function RoleBagMeltingView:SelectOrderCallBack(cell)
	local data = cell and cell:GetData()
	if data == nil then
		return
	end

	local data_index = data.index
	if data_index == self.cur_select_order_index then
		return
	end

	self.cur_select_order_index = data_index
	self.node_list["cur_order_text"].text.text = Language.Bag.NameList1[self.cur_select_order_index]
	self:OnclickSelectOrder()
	self:OneKeySelect()
end

-- 展开阶数筛选列表
function RoleBagMeltingView:OnclickSelectOrder()
	self.is_show_order_part = not self.is_show_order_part
	self.node_list["order_list_part"]:SetActive(self.is_show_order_part)
	self.node_list["order_arrow_down"]:SetActive(self.is_show_order_part)
	self.node_list["order_arrow_up"]:SetActive(not self.is_show_order_part)
end

-- 熔炼
function RoleBagMeltingView:OnClickMelting()
	local select_list = self.item_grid:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Bag.MeltingError)
		return
	end

	for k,v in pairs(select_list) do
		local star_level = v.param and v.param.star_level or 0
		if star_level >= 3 then
			TipWGCtrl.Instance:OpenAlertTips(Language.Bag.MeltingAlertTip, function ()
				self:OnSendMelting()
			end)

			return
		end
	end

	self:OnSendMelting()
end

function RoleBagMeltingView:OnSendMelting()
	local select_list = self.item_grid:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		return
	end

	local destroy_item_list = {}
	for k,v in pairs(select_list) do
		destroy_item_list[#destroy_item_list + 1] = {melt_index = v.index}
	end

	RoleBagWGCtrl.Instance:OnReqEquipMelt(#destroy_item_list, destroy_item_list)
	ViewManager.Instance:FlushView(GuideModuleName.RoleBagMeltingEnterView, 0, "start_do_ani")

	-- self:Close()
end

--背包物品发生改变监听
function RoleBagMeltingView:BagDataChange(item_id, index, reason, put_reason, old_num, new_num)
    local is_add = reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	                (reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num)

	if not is_add then
		return
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		return
	end

	if not (big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT or item_cfg.recycltype == 2) then
		return
	end

	self:Flush()
end

function RoleBagMeltingView:CalcSelect()
	local select_list = self.item_grid:GetAllSelectCell()
	local info = RoleBagWGData.Instance:GetEquipMeltInfo()
	local max_level = RoleBagWGData.Instance:GetEquipMeltMaxLevel()
	local cur_level = info.melt_level

	if IsEmptyTable(select_list) or cur_level >= max_level then
		ViewManager.Instance:FlushView(GuideModuleName.RoleBagMeltingEnterView, 0, "preview")
		self:FlushOpaView()
		return
	end

	local add_exp = 0
	local total_add_lev = 0
	for k,v in pairs(select_list) do
		add_exp = add_exp + v.recyclget
	end

	-- vip加成
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local other_cfg = RoleBagWGData.Instance:EquipMeltCfg().other[1]
	if other_cfg and vip_level >= other_cfg.vip_level then
		add_exp = add_exp * (1 + other_cfg.vip_up_rate / 100)
	end

	local cur_cfg = RoleBagWGData.Instance:GetEquipMeltCfgByLevel(cur_level)
	if cur_cfg == nil then
		ViewManager.Instance:FlushView(GuideModuleName.RoleBagMeltingEnterView, 0, "preview")
		self:FlushOpaView()
		return
	end

	local tab
	local cur_level_max_exp = cur_cfg.need_exp
	local will_add_exp = info.melt_exp + add_exp
	if will_add_exp >= cur_level_max_exp then
		local cfg = nil
		while(true) do
			if will_add_exp - cur_level_max_exp >= 0 then
				will_add_exp = will_add_exp - cur_level_max_exp
				 total_add_lev = total_add_lev + 1
				 cfg = RoleBagWGData.Instance:GetEquipMeltCfgByLevel(info.melt_level + total_add_lev)
				 if not cfg then
					 break
				 end

				 cur_level_max_exp = cfg.need_exp
			 else
				 break
			 end
		end
	end

	tab = {melt_level = cur_level + total_add_lev, melt_exp = will_add_exp}
	ViewManager.Instance:FlushView(GuideModuleName.RoleBagMeltingEnterView, 0, "preview", {info = tab})
	self:FlushOpaView(tab)
end

function RoleBagMeltingView:OnTunShiToggleClick()
	local flag = RoleBagWGCtrl.Instance:GetIsAutoTunShi()
	if flag then
		RoleBagWGCtrl.Instance:SetIsAutoTunShi()
		self:SetAutoTunShiSelect()
	else
		if not self.auto_tunshi_alert then
			local alert = Alert.New()
			alert:SetOkFunc(function ()
				RoleBagWGCtrl.Instance:SetIsAutoTunShi()
				self:SetAutoTunShiSelect()
				RoleBagWGCtrl.Instance:AutoTunShiEquip()
			end)
			alert:SetCancelString(Language.Equip.NoEnter)
			alert:SetOkString(Language.Equip.Enter)
			alert:SetLableString(Language.Bag.AutoTunShiTxt1)
			self.auto_tunshi_alert = alert
		end
		self.auto_tunshi_alert:Open()
	end
end

function RoleBagMeltingView:SetAutoTunShiSelect()
	local flag = RoleBagWGCtrl.Instance:GetIsAutoTunShi()
	self.node_list.image_tunshi_slt:CustomSetActive(flag)
end

--------------------------------------吞噬操作模块__START---------------------------------------------------
function RoleBagMeltingView:InitTunShiOpa()
	if nil == self.attr_list then
        self.attr_list = {}
        local attr_num = self.node_list["attr_list"].transform.childCount
        for i = 1, attr_num do
            local cell = RoleBagMeltingAttrCellRender.New(self.node_list["attr_list"]:FindObj("attr" .. i))
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            self.attr_list[i] = cell
        end
    end

	if not self.role_data_change_callback then
		self.role_data_change_callback = BindTool.Bind1(self.OnRoleDataChange, self)
		RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"vip_level"})
	end
end

function RoleBagMeltingView:ReleaseTunShiOpa()
	if self.attr_list then
        for k, v in pairs(self.attr_list) do
            v:DeleteMe()
        end
        self.attr_list = nil
    end

	if self.role_data_change_callback then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
		self.role_data_change_callback = nil
    end
end

function RoleBagMeltingView:FlushOpaView(info)
    local old_info = RoleBagWGData.Instance:GetEquipMeltInfo()
    info = info or old_info
    local cur_level = info.melt_level
    local max_level = RoleBagWGData.Instance:GetEquipMeltMaxLevel()
    self.node_list["cur_melt_level"].text.text = string.format(Language.Common.LevelNormal, cur_level)

    local attr_list = RoleBagWGData.Instance:GetEquipMelAttrListByLevel(cur_level)
    for k, v in ipairs(self.attr_list) do
        v:SetData(attr_list[k])
    end

    local is_max_level = cur_level >= max_level
    local cur_level_cfg = RoleBagWGData.Instance:GetEquipMeltCfgByLevel(cur_level)

    local cur_pro_value, need_pro_value, pro_percent = 0, 0, 1
    if not is_max_level and cur_level_cfg then
        cur_pro_value = info.melt_exp
        need_pro_value = cur_level_cfg.need_exp
        pro_percent = info.melt_exp / cur_level_cfg.need_exp
    end

    self.node_list["level_pro_text"].text.text = string.format("%s/%s", cur_pro_value, need_pro_value)
    self.node_list["level_progress"].slider.value = pro_percent
    self.node_list["up_level_arrow"]:SetActive(cur_level > old_info.melt_level)

	self:FlushVipPart()
end

function RoleBagMeltingView:OnRoleDataChange(attr_name, value, old_value)
	if attr_name == "vip_level" then
		self:FlushVipPart()
	end
end

function RoleBagMeltingView:FlushVipPart()
    local vo = GameVoManager.Instance:GetMainRoleVo()
    local role_vip = vo.vip_level or 0
    local other_cfg = RoleBagWGData.Instance:EquipMeltCfg().other[1]
    local need_vip_level = other_cfg and other_cfg.vip_level or 0
    self.node_list["btn_active_vip"]:SetActive(role_vip < need_vip_level)
    self.node_list["rich_ronglian_tip"].text.text = role_vip >= need_vip_level and Language.Bag.VipMeltingTips or
    Language.Bag.MeltingTips
end

function RoleBagMeltingView:OnClickActiveVipBtn()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip, "card_seq", { card_seq = 3 })
end
----------------------------------------吞噬操作模块__END----------------------------------------------------

---------------------------------------------------------
-- BagMeltingGrid
---------------------------------------------------------
BagMeltingGrid = BagMeltingGrid or BaseClass(AsyncBaseGrid)

function BagMeltingGrid:SetMeltingOneKeySelcet(color, order, less_than_star_level)
	less_than_star_level = less_than_star_level or 3
	self.select_tab[1] = {}
	self.cur_multi_select_num = 0

	for i = 1, self.has_data_max_index do
		local data = self.cell_data_list[i]
		if not IsEmptyTable(data) then
			if data.color <= color and data.order <= order then
				local star_level = data.param and data.param.star_level or 0
				if star_level < less_than_star_level then
					self.select_tab[1][i] = true
					self.cur_multi_select_num = self.cur_multi_select_num + 1
				end
			end
		end
	end

	self:__DoRefreshSelectState()
end

-------------------------------------------
--熔炼格子
MeltEquipCell = MeltEquipCell or BaseClass(ItemCell)
function MeltEquipCell:__init()
	self:SetIsShowTips(false)
	self:UseNewSelectEffect(true)
	self:SetItemTipFrom(ItemTip.FROM_BAG_MELTING)
end

function MeltEquipCell:SetSelect(is_select)
	self:SetSelectEffect(is_select)
end

----------------CKPinZhiListRender-------------------
MeltingPinZhiListRender = MeltingPinZhiListRender or BaseClass(BaseRender)
function MeltingPinZhiListRender:OnFlush()
	self.node_list.lbl_pinzhi_name.text.text = self.data
	self.node_list.select_pinzhi_bg:SetActive(self.is_select)
	-- self.node_list.line:SetActive(not self.is_select)
end

function MeltingPinZhiListRender:OnSelectChange(is_select)
	if self.node_list.select_pinzhi_bg then
		self.node_list.select_pinzhi_bg:SetActive(is_select)
		-- self.node_list.line:SetActive(not is_select)
	end
end

----------------CKPinJieListRender-------------------
MeltingPinJieListRender = MeltingPinJieListRender or BaseClass(BaseRender)
function MeltingPinJieListRender:OnFlush()
	self.node_list.lbl_pinjie_name.text.text = self.data.name
	self.node_list.select_pinjie_bg:SetActive(self.is_select)
	-- self.node_list.line:SetActive(not self.is_select)
end

function MeltingPinJieListRender:OnSelectChange(is_select)
	if self.node_list.select_pinjie_bg then
		self.node_list.select_pinjie_bg:SetActive(is_select)
		-- self.node_list.line:SetActive(not is_select)
	end
end