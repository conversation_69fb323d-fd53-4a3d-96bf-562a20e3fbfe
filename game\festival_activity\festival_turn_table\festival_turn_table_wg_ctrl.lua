require("game/festival_activity/festival_turn_table/festival_turn_table_wg_data")
require("game/festival_activity/festival_turn_table/festival_turn_table_select_view")
require("game/festival_activity/festival_turn_table/festival_turn_table_record")
FestivalTrunTableWGCtrl = FestivalTrunTableWGCtrl or BaseClass(BaseWGCtrl)

function FestivalTrunTableWGCtrl:__init()
	if FestivalTrunTableWGCtrl.Instance then
        error("[FestivalTrunTableWGCtrl]:Attempt to create singleton twice!")
	end
	FestivalTrunTableWGCtrl.Instance = self

	self.data = FestivalTurnTableWGData.New()
	--self.view = ZhouYiYunChengView.New(GuideModuleName.ZhouYiYunCheng)
	self.record_view = FestivalActivityTurnRecord.New()
	self.select_view = FestivalTurnTableSelectView.New()
	self:RegisterAllProtocals()

end

function FestivalTrunTableWGCtrl:__delete()
	--self.view:DeleteMe()
	self.data:DeleteMe()
	self.select_view:DeleteMe()
	self.record_view:DeleteMe()
	self.record_view = nil

	FestivalTrunTableWGCtrl.Instance = nil
end

function FestivalTrunTableWGCtrl:RegisterAllProtocals()
	-- -- 注册协议
	self:RegisterProtocol(SCFestivalHappyMondayInfo, "OnSCRAHappyMondayInfo")
	self:RegisterProtocol(SCFestivalHappyMondayDrawResult, "OnSCRAHappyMondayDrawResult")
	self:RegisterProtocol(SCFestivalHappyMondayServerRecord, "OnSCRAHappyMondayServerRecord")
	self:RegisterProtocol(SCFestivalHappyMondayPersonRecord, "OnSCRAHappyMondayPersonRecord")
end

function FestivalTrunTableWGCtrl:OnSCRAHappyMondayInfo(protocol)
	local old_draw_times = FestivalTurnTableWGData.Instance:GetLeftCanRollTimes()
	self.data:OnSCRAHappyMondayInfo(protocol)
	local new_draw_times = protocol.can_draw_times
	local lerp_times = new_draw_times - old_draw_times
	if lerp_times > 0 and old_draw_times >= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ZhouYiYunCheng.AddDrawTimerStr,lerp_times))
	end
	-- if self.view:IsOpen() then
	-- 	self.view:Flush()
	-- end
	RemindManager.Instance:Fire(RemindName.Festival_TurnTable)
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2264)
end

function FestivalTrunTableWGCtrl:OnSCRAHappyMondayDrawResult(protocol)
	self.data:OnSCRAHappyMondayDrawResult(protocol)
	-- if self.view:IsOpen() then
	-- 	self.view:PlayRollAnimal()
	-- end
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2264, "PlayRollAnimal")
end

function FestivalTrunTableWGCtrl:OnSCRAHappyMondayServerRecord(protocol)
	local delay = FestivalTurnTableWGData.Instance:GetJumpAni() and 0 or 5
	local turn_table_is_turning = FestivalTurnTableWGData.Instance:GetTurnTableIsTurning()
	local flush_fun = function ()
		self.data:OnSCRAHappyMondayServerRecord(protocol)
		-- if self.view:IsOpen() then
		-- 	self.view:FlushWorld()
		-- end
		FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2264, "TurnTableFlushWorld")
		self.record_view:Flush()
	end
	if turn_table_is_turning then
		TryDelayCall(self, flush_fun, delay, "FATurnTableFlushWorld")
	else
		flush_fun()
	end
end

function FestivalTrunTableWGCtrl:OnSCRAHappyMondayPersonRecord(protocol)
	local delay = FestivalTurnTableWGData.Instance:GetJumpAni() and 0 or 5
	local turn_table_is_turning = FestivalTurnTableWGData.Instance:GetTurnTableIsTurning()
	local flush_fun = function ()
		self.data:OnSCRAHappyMondayPersonRecord(protocol)
		-- if self.view:IsOpen() then
		-- 	self.view:FlushPersonal()
		-- end
		FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2264, "TurnTableFlushPersonal")
		self.record_view:Flush()
	end
	if turn_table_is_turning then
		TryDelayCall(self, flush_fun, delay, "FATurnTableFlushPersonal")
	else
		flush_fun()
	end
end

-- RA_HAPPY_MONDAY_OP = { 						--周一运程操作类型
-- 	RA_HAPPY_MONDAY_OP_REQ_INFO = 1,					-- 请求信息
-- 	RA_HAPPY_MONDAY_OP_FETCH_DRAW_TIMES = 2,			-- 领取抽奖次数，参数1是任务类型
-- 	RA_HAPPY_MONDAY_OP_CHOOSE_REWARD = 3,				-- 选择奖励，param1-4 分别代码四个档位，其比特位为1则表示选择其中的奖励
-- 	RA_HAPPY_MONDAY_OP_DRAW = 4,						-- 进行一次抽奖
-- }

function FestivalTrunTableWGCtrl:SendYunChengRollReq(opera_type,param_1,param_2,param_3,param_4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
  	protocol.rand_activity_type = ACTIVITY_TYPE.FESTIVAL_ACT_HAPPY_MONDAY
  	protocol.opera_type = opera_type or 0
  	protocol.param_1 = param_1 or 0
  	protocol.param_2 = param_2 or 0
  	protocol.param_3 = param_3 or 0
  	protocol.param_4 = param_4 or 0
  	protocol:EncodeAndSend()
end

function FestivalTrunTableWGCtrl:OpenRewardSelectView()
	if self.select_view then
		self.data:CreateTempSelectList()
		self.select_view:Open()
	end
end

--打开记录
function FestivalTrunTableWGCtrl:OpenRecord()
    self.record_view:Open()
end