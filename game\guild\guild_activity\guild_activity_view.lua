--废弃
--仙盟活动总预览

function GuildView:InitGuildActView()
	--[[
	self.guild_activity_grid = AsyncBaseGrid.New()
	self.guild_activity_grid:SetStartZeroIndex(false)
	local bundle = "uis/view/guild_ui_prefab"
	local asset = "guild_activity_item"
	self.guild_activity_grid:CreateCells({col = 2, cell_count = 4, list_view = self.node_list["guild_activity_grid"],
		assetBundle = bundle, assetName = asset, itemRender = GuildActItem})
	--]]

	self.select_page_index = 0

	local guild_act_page_list = {}
	guild_act_page_list[1] = GuildActShenShouPage.New(self.node_list.guild_activity_page_1)
	guild_act_page_list[2] = GuildActPartyPage.New(self.node_list.guild_activity_page_2)
	guild_act_page_list[3] = GuildActGuardPage.New(self.node_list.guild_activity_page_3)
	guild_act_page_list[4] = GuildActPage.New(self.node_list.guild_activity_page_4)
	guild_act_page_list[5] = GuildActPage.New(self.node_list.guild_activity_page_5)
	
	for i=1,#guild_act_page_list do
		guild_act_page_list[i]:SetIndex(i)
		guild_act_page_list[i]:AddClickEventListener(BindTool.Bind(self.OnClickGuildActPage, self))
	end
	self.guild_act_page_list = guild_act_page_list
end

function GuildView:DeleteGuildActView()
	-- if self.guild_activity_grid then
	-- 	self.guild_activity_grid:DeleteMe()
	-- 	self.guild_activity_grid = nil
	-- end
	if self.guild_act_page_list then
		for _,v in pairs(self.guild_act_page_list) do
			v:DeleteMe()
		end
		self.guild_act_page_list = nil
	end
end

function GuildView:ShowGuildACTCallBack()
	self.select_page_index = 0

	-- 仙盟boss
	GuildWGCtrl.Instance:SendReqBossInfo()

	-- 仙盟守卫排行数据
	if GuildWGData.Instance:GetIsEnterCrossBiPing() then
        GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GUILD_FB_OPERA_TYPE_RANK_CROSS)
    else
        GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GUILD_FB_OPERA_TYPE_RANK)
    end
end

function GuildView:OnFlushGuildActView(param_t, index)
	local data_list = GuildActivityWGData.Instance:GetActData()
	local page_list = self.guild_act_page_list
	for i=1,#page_list do
		page_list[i]:SetData(data_list[i])
	end

	---[[ 进行中的默认点开
	if self.select_page_index <= 0 then
		local select_index = 0
		for i=1,#data_list do
			if data_list[i].cfg.type == GuildActivityWGData.Act_Type.Boss then
				if GuildBossWGData.Instance:IsGuildBossOpen() then
					select_index = i
					break
				end
			elseif data_list[i].cfg.type ~= GuildActivityWGData.Act_Type.NotOpen then
				if ActivityWGData.Instance:GetActivityIsOpen(data_list[i].cfg.act_id) then
					select_index = i
					break
				end
			end
		end

		if select_index > 0 and page_list[select_index] then
			self:OnClickGuildActPage(page_list[select_index])
		end
	end
	--]]
	-- self.guild_activity_grid:SetDataList(data_list)
end

function GuildView:OnClickGuildActPage(page)
	self.select_page_index = page:GetIndex()
	self:FlushGuildActPage()
end

function GuildView:FlushGuildActPage()
	local move_x = 16
	local conten_move_x = 0
	local spacing = 12
	local page_list = self.guild_act_page_list
	local select_index = self.select_page_index

	for i=1,#page_list do
		page_list[i]:ShowInfoPanel(i == select_index)
		page_list[i]:DoMove(move_x)

		if page_list[i]:GetIsOn() then -- 打开的时候下一个不留缝(间距)
			move_x = move_x + page_list[i]:GetWidth()
		else
			move_x = move_x + page_list[i]:GetWidth() + spacing
		end
		
		if i < select_index then
			conten_move_x = move_x
		end
	end

	local rect = self.node_list.guild_activity_content_root.rect
	RectTransform.SetSizeDeltaXY(rect, move_x - spacing, 584)

	local has_on = false
	for i=1,#page_list do
		if page_list[i]:GetIsOn() then
			has_on = true
			break
		end
	end

	if not has_on then
		conten_move_x = 0
	elseif conten_move_x > 0 then
		conten_move_x = conten_move_x - spacing
	end

	rect:DOAnchorPosX(-conten_move_x, 0.5)
end

----------------------------------------------------------------------------

GuildActPage = GuildActPage or BaseClass(BaseRender)

function GuildActPage:__init()
	self.m_width = 230
	self.m_height = 584
	self.m_ex_width = 480
	self.m_ison = false
end

function GuildActPage:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function GuildActPage:InitPage()
	if self.node_list.tip_btn then
		XUI.AddClickEventListener(self.node_list["tip_btn"], BindTool.Bind(self.OnClickTipBtn, self))
	end
	if self.node_list.goto_btn then
		XUI.AddClickEventListener(self.node_list["goto_btn"], BindTool.Bind(self.OnClickGotoBtn, self))
	end
	if self.node_list.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	end
end

function GuildActPage:GetIsOn()
	return self.m_ison
end

function GuildActPage:GetWidth()
	if self.m_ison then
		return self.m_width + self.m_ex_width
	else
		return self.m_width
	end
end

function GuildActPage:ShowInfoPanel(is_show)
	self.m_ison = is_show and not self.m_ison or false
	local move_x = self.m_ison and 0 or -self.m_ex_width
	if self.node_list.info_root then
		self.node_list.info_root.rect:DOAnchorPosX(move_x, 0.5)
	end
end

function GuildActPage:DoMove(pos_x)
	local root = self:GetView()
	root.rect:DOAnchorPosX(pos_x, 0.5)
end

function GuildActPage:OnClickTipBtn()
	local data = self:GetData()
	if not data or not data.cfg then
		return 
	end

	local cfg = data.cfg
	local tips_content = cfg.act_rule
	if cfg.type == GuildActivityWGData.Act_Type.ShouHu then
		local act_hall_cfg = data.act_hall_cfg
		if act_hall_cfg then
			local str = string.format(Language.BiZuo.Act_Time_Segment_2, act_hall_cfg.open_tips, act_hall_cfg.open_time, act_hall_cfg.close_time)
			tips_content = string.format(cfg.act_rule, str)
		end
	end

	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.Activity.HuoDongShuoMing)
	role_tip:SetContent(tips_content)
end

function GuildActPage:OnClickGotoBtn()
	local data = self:GetData()
	if not data or not data.cfg then
		return 
	end

	if data.cfg.type == GuildActivityWGData.Act_Type.Boss then
		self:GuildBossGoFunc()
	elseif data.cfg.type == GuildActivityWGData.Act_Type.DaTi then
		self:GuildDaTiGoFunc()			
	elseif data.cfg.type == GuildActivityWGData.Act_Type.ShouHu then
		self:GuildShouHuGoFunc()	
	end
end

function GuildActPage:GuildBossGoFunc()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local guild_post = role_vo.guild_post
	local is_mengzhu = guild_post == GUILD_POST.FU_TUANGZHANG or guild_post == GUILD_POST.TUANGZHANG or guild_post == GUILD_POST.JiaMengZhu
	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	local is_can = guild_bosss_info.active_is_open == 1
	local is_open_act = guild_bosss_info.finish_timestamp > TimeWGCtrl.Instance:GetServerTime()
	local have_guild_money = guild_bosss_info.guild_money
	local week_num = guild_bosss_info.open_times
	local need_guild_money = GuildBossWGData.Instance:GetBossOpenNeedMoneyNum(week_num + 1)
	if is_open_act then
		GuildBossWGCtrl.SendGuildBossEnterReq()
	else
		local tody_num
		if is_open_act then
			tody_num = 1
		else
			tody_num = guild_bosss_info.today_open_num
        end
        local week_times = GuildBossWGData.Instance:GetGuildBossOpenTimes()
		local is_week_can_open = guild_bosss_info.open_times < week_times
		local money_can_open = have_guild_money >= need_guild_money
		local today_can_open = tody_num < GuildDataConst.TODAY_GUILD_MAX_BOSS
		if money_can_open then
			if is_mengzhu and is_week_can_open and today_can_open and money_can_open and is_can then
				if not self.alert_window then
					self.alert_window = Alert.New()
					self.alert_window:SetOkFunc(function()
						GuildBossWGCtrl.SendGuildBossStartReq()
					end)
				end
				self.alert_window:SetLableString(Language.GuildBoss.IsOpenTxt)
				self.alert_window:Open()
			else
				if not today_can_open then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossDayTips)
				elseif not is_week_can_open then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossWeekDayTips)
				elseif not is_mengzhu then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossMengZhuTips)
				else
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossTimeTips)
				end
			end
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossMoneyTips)
		end
	end
end

function GuildActPage:GuildDaTiGoFunc()
	if RoleWGData.Instance.role_vo.guild_id == 0 then
		GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist)
	else
		FunOpen.Instance:OpenViewNameByCfg("guildanswer")
	end
end

function GuildActPage:GuildShouHuGoFunc()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_FB)
    if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
        return
    end
    local is_finish = GuildWGData.Instance:IsFinishGuildFb()
    if is_finish then
    	SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ShouHuFinish)
    	return
    end
    GuildWGCtrl.Instance:SendGuildFbEnterReq()
end

function GuildActPage:FlushPageReward()
	local data = self:GetData()
	if data.cfg.type ~= GuildActivityWGData.Act_Type.NotOpen then
		if data.reward_list then
			self.reward_list:SetDataList(data.reward_list)
        end
	end
end

function GuildActPage:FlushPageInfo()
	local data = self:GetData()
	if not data.act_hall_cfg then
		return
	end

	-- 开启时间
	local str = string.format("%s %s", data.act_hall_cfg.open_tips, data.act_hall_cfg.open_time)
	str = ToColorStr(str, COLOR3B.GREEN)
	self.node_list.kaiqi_label.text.text = string.format(Language.GuildBoss.OpenTimeStr, str)

	if data.act_hall_cfg.open_type <= CalendarWGData.OPEN_TYPE.TODAY_OPEN then
		local time_str = string.format("%s-%s", data.act_hall_cfg.open_time, data.act_hall_cfg.close_time)
		time_str = ToColorStr(time_str, COLOR3B.D_GREEN)
		self.node_list.time_label.text.text = string.format(Language.Guild.TodayOpenTime, time_str)
	else
		self.node_list.time_label.text.text = Language.Guild.TodayNoOpen
	end

	-- 宣传语
	self.node_list.desc_label.text.text = data.act_hall_cfg.smallview_des

	-- 进行中
	local act_is_open = false
	if data.cfg.type == GuildActivityWGData.Act_Type.Boss then
		act_is_open = GuildBossWGData.Instance:IsGuildBossOpen()
	else
		act_is_open = ActivityWGData.Instance:GetActivityIsOpen(data.cfg.act_id)
	end
	self.node_list.underway_img:SetActive(act_is_open)
	self.node_list.goto_btn_effect:SetActive(act_is_open)
end

---[[ 仙盟神兽
GuildActShenShouPage = GuildActShenShouPage or BaseClass(GuildActPage)

function GuildActShenShouPage:LoadCallBack()
	self:InitPage()
end

function GuildActShenShouPage:OnFlush()
	local data = self:GetData()
	if not data or not data.cfg then
		return 
	end

	self:FlushPageReward()
	self:FlushPageInfo()
	self:FlushGuildBossInfo()
end

function GuildActShenShouPage:FlushGuildBossInfo()
	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	local week_num = guild_bosss_info.open_times
	local have_guild_money = guild_bosss_info.guild_money
	local need_guild_money = GuildBossWGData.Instance:GetBossOpenNeedMoneyNum(week_num + 1)
	local str = ToColorStr(need_guild_money, COLOR3B.GREEN)

	self.node_list.jindu_value.text.text = have_guild_money
	self.node_list.need_label.text.text = string.format(Language.GuildBoss.NeedConsume, str)
	self.node_list.jindu_label_2.text.text = need_guild_money
	local slider_value = 0.86 * (have_guild_money / need_guild_money)
	slider_value = math.max(slider_value, 0.02)
	self.node_list.jidun_slider.slider.value = slider_value
end
--]]

---[[ 仙盟晚宴
GuildActPartyPage = GuildActPartyPage or BaseClass(GuildActPage)

function GuildActPartyPage:__delete()
	if self.dati_reward_list then
		self.dati_reward_list:DeleteMe()
		self.dati_reward_list = nil
	end
	if self.chuangong_reward_list then
		self.chuangong_reward_list:DeleteMe()
		self.chuangong_reward_list = nil
	end
end

function GuildActPartyPage:LoadCallBack()
	self:InitPage()
	self:InitPartyPage()
end

function GuildActPartyPage:InitPartyPage()
	self.dati_reward_list = AsyncListView.New(ItemCell, self.node_list.dati_reward_list)
	self.chuangong_reward_list = AsyncListView.New(ItemCell, self.node_list.chuangong_reward_list)
	self.node_list.dati_desc.text.text = Language.Guild.ActDatiDesc
	self.node_list.chuangong_desc.text.text = Language.Guild.ActChuanGongDesc
end

function GuildActPartyPage:OnFlush()
	self:FlushPageInfo()
	self:FlushPartyReward()
	self:FlushPartyOpenTime()
end

function GuildActPartyPage:FlushPartyReward()
	local data = self:GetData()
	if data.cfg.type ~= GuildActivityWGData.Act_Type.NotOpen then
		local reward_list = data.reward_list
		local chuangong_reward = table.remove(reward_list, #reward_list)
		self.dati_reward_list:SetDataList(reward_list)
		self.chuangong_reward_list:SetDataList({chuangong_reward})
	end
end

function GuildActPartyPage:FlushPartyOpenTime()
	local data = self:GetData()
	if not data.act_hall_cfg then
		return
	end

	local str = string.format("%s-%s", data.act_hall_cfg.open_time, data.act_hall_cfg.close_time)
	self.node_list.dati_time_label.text.text = str
	self.node_list.chuangong_time_label.text.text = str
end
--]]

---[[ 仙盟守卫
GuildActGuardPage = GuildActGuardPage or BaseClass(GuildActPage)

function GuildActGuardPage:__delete()
	if self.rank_item_list then
		for _,v in pairs(self.rank_item_list) do
			v:DeleteMe()
		end
		self.rank_item_list = nil
	end
end

function GuildActGuardPage:LoadCallBack()
	self:InitPage()
	self:InitRankItemList()
end

function GuildActGuardPage:InitRankItemList()
	local item_list = {}
	for i=1,3 do
		item_list[i] = GuildActGuardRankItem.New(self.node_list["rank_item_" .. i])
	end
	self.rank_item_list = item_list
end

function GuildActGuardPage:OnFlush()
	self:FlushPageInfo()
	self:FlushPageReward()
	self:FlushGuardRank()
end

function GuildActGuardPage:FlushGuardRank()
	local data_list = GuildWGData.Instance:GetGuildFbRankInfo()
	if IsEmptyTable(data_list) then
		self.node_list.not_rank_img:SetActive(true)
		self.node_list.rank_root:SetActive(false)
		return
	end
	self.node_list.not_rank_img:SetActive(false)
	self.node_list.rank_root:SetActive(true)

	local item_list = self.rank_item_list
	for i=1,#item_list do
		item_list[i]:SetData(data_list[i])
	end
end

GuildActGuardRankItem = GuildActGuardRankItem or BaseClass(BaseRender)

function GuildActGuardRankItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)

	local key = string.format("xm_flag%d_%d", data.flag_color, data.flag_id)
	local asset,bundle = ResPath.GetGuildSystemImage(key)
	self.node_list.flag_img.image:LoadSprite(asset, bundle, function ()
		self.node_list.flag_img.image:SetNativeSize()
	end)
	self.node_list.flag_name.text.text = data.flag_name
	self.node_list.guide_name.text.text = data.guild_name
end
--]]






--[[ 旧代码先留个一周观察后面删
-------------------guild_activity_item----------

GuildActItem = GuildActItem or BaseClass(BaseGridRender)

function GuildActItem:__init()
end

function GuildActItem:__delete()
end

function GuildActItem:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
	XUI.AddClickEventListener(self.node_list["rank_btn"], BindTool.Bind(self.ClickRankBtn, self))
	XUI.AddClickEventListener(self.node_list["go_btn"], BindTool.Bind(self.ClickGoBtn, self))
	XUI.AddClickEventListener(self.node_list["tip_btn"], BindTool.Bind(self.ClickTipBtn, self))
end

function GuildActItem:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
end

function GuildActItem:OnFlush()
	if not self.data or not self.data.cfg then return end
	local cfg = self.data.cfg
	local act_hall_cfg = self.data.act_hall_cfg
	local is_open = cfg.type ~= GuildActivityWGData.Act_Type.NotOpen
	self.node_list["open_panel"]:SetActive(is_open)
	self.node_list["not_open_panel"]:SetActive(not is_open)
	self.node_list["act_img"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("xm_act_bg"..cfg.act_img))
	self.node_list["act_img"].raw_image:SetNativeSize()
	if is_open then
		self.node_list["name"].text.text = cfg.act_name
		if act_hall_cfg then
			local open_day_des = act_hall_cfg.open_tips
			self.node_list["desc"].text.text = string.format(Language.Guild.Act_Time_Segment, open_day_des,act_hall_cfg.open_time, 
												act_hall_cfg.close_time,cfg.act_name,act_hall_cfg.smallview_des)
		end
		if self.data.reward_list then
			self.reward_list:SetDataList(self.data.reward_list)
        end

        local role_level = RoleWGData.Instance:GetRoleLevel()
        local can_open = role_level >= self.data.act_hall_cfg.level
        local level_limit
        if not can_open then
            local level = self.data.act_hall_cfg and self.data.act_hall_cfg.level
            level_limit = string.format(Language.Guild.ActOpenLimit, level)
            self.node_list["limit_level"].text.text = level_limit
        end
		self.node_list["open_content"]:SetActive(can_open)
		self.node_list["limit_content"]:SetActive(not can_open)
		local act_is_open = false
		if cfg.type == GuildActivityWGData.Act_Type.Boss then
			self:FlushGuildBoss()
			act_is_open = GuildBossWGData.Instance:IsGuildBossOpen()
		else
			self.node_list["open_limit_text"].text.text = ""
			self.node_list["open_num_text"].text.text = ""
			self.node_list.go_btn_text.text.text = Language.Guild.EnterShouHu
			act_is_open = ActivityWGData.Instance:GetActivityIsOpen(cfg.act_id)
		end
		self.node_list["jxz_img"]:SetActive(act_is_open)
	end
	self:FlushActRemind()
end

function GuildActItem:FlushActRemind()
	if not self.data or not self.data.cfg then return end
	local cfg = self.data.cfg
	local is_remind = false
	if cfg.type == GuildActivityWGData.Act_Type.Boss then
		is_remind = GuildWGData.Instance:IsShowGuildBossRedBagRedPoint() == 1
	elseif cfg.type == GuildActivityWGData.Act_Type.DaTi then
		is_remind = GuildWGData.Instance:IsShowGuildDaTiRedPoint() == 1
	elseif cfg.type == GuildActivityWGData.Act_Type.ShouHu then
		is_remind = GuildWGData.Instance:IsShowGuildShiLianRedPoint() == 1
	end
	self.node_list["go_btn_red"]:SetActive(is_remind)
end

function GuildActItem:ClickRankBtn()
	if not self.data or not self.data.cfg then return end
	local cfg = self.data.cfg
	if cfg.open_panel_name ~= "" then
		ViewManager.Instance:Open(cfg.open_panel_name)
	end
end

function GuildActItem:ClickGoBtn()
	if not self.data or not self.data.cfg then return end
	local cfg = self.data.cfg
	if cfg.type == GuildActivityWGData.Act_Type.Boss then
		self:GuildBossGoFunc()
	elseif cfg.type == GuildActivityWGData.Act_Type.DaTi then
		self:GuildDaTiGoFunc()			
	elseif cfg.type == GuildActivityWGData.Act_Type.ShouHu then
		self:GuildShouHuGoFunc()	
	end
end

function GuildActItem:ClickTipBtn()
	if not self.data or not self.data.cfg then return end
	local cfg = self.data.cfg
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.Activity.HuoDongShuoMing)
	local tips_content = cfg.act_rule
	if cfg.type == GuildActivityWGData.Act_Type.ShouHu then
		local act_hall_cfg = self.data.act_hall_cfg
		if act_hall_cfg then
			local str = string.format(Language.BiZuo.Act_Time_Segment_2, act_hall_cfg.open_tips, act_hall_cfg.open_time, act_hall_cfg.close_time)
			tips_content = string.format(cfg.act_rule, str)
		end
	end

	role_tip:SetContent(tips_content)
end

function GuildActItem:GuildBossGoFunc()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local guild_post = role_vo.guild_post
	local is_mengzhu = guild_post == GUILD_POST.FU_TUANGZHANG or guild_post == GUILD_POST.TUANGZHANG or guild_post == GUILD_POST.JiaMengZhu
	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	local is_can = guild_bosss_info.active_is_open == 1
	local is_open_act = guild_bosss_info.finish_timestamp > TimeWGCtrl.Instance:GetServerTime()
	local have_guild_money = guild_bosss_info.guild_money
	local week_num = guild_bosss_info.open_times
	local need_guild_money = GuildBossWGData.Instance:GetBossOpenNeedMoneyNum(week_num + 1)
	if is_open_act then
		GuildBossWGCtrl.SendGuildBossEnterReq()
	else
		local tody_num
		if is_open_act then
			tody_num = 1
		else
			tody_num = guild_bosss_info.today_open_num
        end
        local week_times = GuildBossWGData.Instance:GetGuildBossOpenTimes()
		local is_week_can_open = guild_bosss_info.open_times < week_times
		local money_can_open = have_guild_money >= need_guild_money
		local today_can_open = tody_num < GuildDataConst.TODAY_GUILD_MAX_BOSS
		if money_can_open then
			if is_mengzhu and is_week_can_open and today_can_open and money_can_open and is_can then
				if not self.alert_window then
					self.alert_window = Alert.New()
					self.alert_window:SetOkFunc(function()
						GuildBossWGCtrl.SendGuildBossStartReq()
					end)
				end
				self.alert_window:SetLableString(Language.GuildBoss.IsOpenTxt)
				self.alert_window:Open()
			else
				if not today_can_open then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossDayTips)
				elseif not is_week_can_open then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossWeekDayTips)
				elseif not is_mengzhu then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossMengZhuTips)
				else
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossTimeTips)
				end
			end
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossMoneyTips)
		end
	end
end

function GuildActItem:GuildDaTiGoFunc()
	if RoleWGData.Instance.role_vo.guild_id == 0 then
		GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist)
	else
		FunOpen.Instance:OpenViewNameByCfg("guildanswer")
	end
end

function GuildActItem:GuildShouHuGoFunc()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_FB)
    if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
        return
    end
    local is_finish = GuildWGData.Instance:IsFinishGuildFb()
    if is_finish then
    	SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ShouHuFinish)
    	return
    end
    GuildWGCtrl.Instance:SendGuildFbEnterReq()
end

function GuildActItem:FlushGuildBoss()
	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	local week_num = guild_bosss_info.open_times
	local have_guild_money = guild_bosss_info.guild_money
	local need_guild_money = GuildBossWGData.Instance:GetBossOpenNeedMoneyNum(week_num + 1)
	local is_money_red = have_guild_money < need_guild_money
	local str = ToColorStr(have_guild_money .. "/" .. need_guild_money,is_money_red and COLOR3B.D_RED or COLOR3B.D_GREEN)
	self.node_list["open_limit_text"].text.text = string.format(Language.GuildBoss.EnterConsume,str)

	local tody_num
	local is_open_act = guild_bosss_info.finish_timestamp > TimeWGCtrl.Instance:GetServerTime()
	if is_open_act then
		tody_num = 1
	else
		tody_num = guild_bosss_info.today_open_num
    end
    local week_times = GuildBossWGData.Instance:GetGuildBossOpenTimes()
	local is_week_can_open = guild_bosss_info.open_times < week_times
	local today_can_open = tody_num < GuildDataConst.TODAY_GUILD_MAX_BOSS
	local color1,color2
	if is_week_can_open then
		color1 = COLOR3B.D_GREEN
	else
		color1 = COLOR3B.D_RED
	end

	local week_max_num = week_times
	local text1 = string.format(Language.GuildBoss.WeekOpenNum, color1, week_num, week_max_num)
	self.node_list["open_num_text"].text.text = text1

	local str = Language.GuildBoss.EnterBtnTxt[2]
	if not is_open_act then
		if not is_week_can_open then
			str = Language.GuildBoss.EnterBtnTxt[4]
		elseif today_can_open and not is_open_act then
			if is_mengzhu then
				str = Language.GuildBoss.EnterBtnTxt[1]
			else
				str = Language.GuildBoss.EnterBtnTxt[3]
			end
		end
	end

	self.node_list.go_btn_text.text.text = str
end
--]]