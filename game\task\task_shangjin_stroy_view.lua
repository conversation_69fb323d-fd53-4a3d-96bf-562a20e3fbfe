TaskShangJinStoryView = TaskShangJinStoryView or BaseClass(SafeBaseView)

function TaskShangJinStoryView:__init()
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/task_prefab", "layout_shangjin_task_stroy_panel")
end

function TaskShangJinStoryView:CloseCallBack()
	self.cur_story_tag_data = nil
	self.cur_story_tag_index = nil
end

function TaskShangJinStoryView:ReleaseCallBack()
	if self.tag_list_view then
		self.tag_list_view:DeleteMe()
		self.tag_list_view = nil
	end

	if self.stroy_reward_list then
		self.stroy_reward_list:DeleteMe()
		self.stroy_reward_list = nil
	end

	if self.task_stroy_list_view then
		self.task_stroy_list_view:DeleteMe()
		self.task_stroy_list_view = nil
	end
end

function TaskShangJinStoryView:LoadCallBack()
	if not self.tag_list_view then
		self.tag_list_view = AsyncListView.New(ShangJinStoryTagRender, self.node_list.tag_list_view)
		self.tag_list_view:SetStartZeroIndex(true)
		self.tag_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectStoryTagCB, self))
	end

	if not self.stroy_reward_list then
		self.stroy_reward_list = AsyncListView.New(ShangJinStoryNoteRewardCell, self.node_list.stroy_reward_list)
		self.stroy_reward_list:SetUseRenderClick(true)
		self.stroy_reward_list:SetSelectCallBack(BindTool.Bind(self.GetStroyRewardClick, self))
	end

	if not self.task_stroy_list_view then
		self.task_stroy_list_view = AsyncListView.New(ShangJinStoryMessageCell, self.node_list.task_stroy_list_view)
	end
end

function TaskShangJinStoryView:OnSelectStoryTagCB(story_tag_cell, cell_index, is_default, is_click)
    if nil == story_tag_cell or nil == story_tag_cell.data then
		return
	end

	--拿到当前选中的格子下标
	if self.cur_culture_beast_index == cell_index then
        return
	end

	self.cur_story_tag_data = story_tag_cell.data
	self.cur_story_tag_index = cell_index
	self:FlushStoryView()
end


function TaskShangJinStoryView:GetStroyRewardClick(cell, cell_index, is_default, is_click)
	if cell == nil or cell.data == nil or is_default then
		return
	end

	-- 领取奖励
	if self.cur_story_tag_data == nil then
		return
	end

	TaskWGCtrl.Instance:SendOperateTypeGetNodeReward(self.cur_story_tag_data.task_note_id)
end

function TaskShangJinStoryView:OnFlush()
	local note_list = TaskWGData.Instance:GetBountyTaskNoteList()
	self.tag_list_view:SetDataList(note_list)
	self.cur_story_tag_index = nil
	self.cur_story_tag_data = nil

	if self.cur_story_tag_index == nil then
		self.cur_story_tag_index = 0
	end

	self.tag_list_view:JumpToIndex(self.cur_story_tag_index, 5)
end

-- 刷新界面
function TaskShangJinStoryView:FlushStoryView()
	if (not self.cur_story_tag_data) or (not self.cur_story_tag_index) then
		return
	end

	self.node_list.title_txt.text.text = self.cur_story_tag_data.task_note_title
	self.node_list.task_story_txt.text.text = self.cur_story_tag_data.task_note_des
	local stroy_list, note_is_lock, note_is_active = TaskWGData.Instance:GetTaskNoteList(self.cur_story_tag_data.task_note_id)
	self.task_stroy_list_view:SetDataList(stroy_list)
	local is_flag = TaskWGData.Instance:GetBountyNoteRewardStatus(self.cur_story_tag_data.task_note_id) == 1
	local is_can_get = (not is_flag) and (not note_is_lock)
	local reward_list = {}

	for i = 0, #self.cur_story_tag_data.reward_item do
		local reward_data = {}
		local data = self.cur_story_tag_data.reward_item[i]
		reward_data.item_id = data.item_id
		reward_data.num = data.num
		reward_data.is_bind = data.is_bind
		reward_data.can_lq = is_can_get
		reward_data.is_ylq = is_flag

		table.insert(reward_list, reward_data)
	end

	self.stroy_reward_list:SetDataList(reward_list)
	self.node_list.story_status_in_progress:CustomSetActive(note_is_lock and note_is_active)
	self.node_list.story_status_not_active:CustomSetActive(note_is_lock and (not note_is_active))
	self.node_list.story_status_finish:CustomSetActive(not note_is_lock)
end

----------------------------------------------------------------------------
ShangJinStoryTagRender = ShangJinStoryTagRender or BaseClass(BaseRender)
function ShangJinStoryTagRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.normal_txt.text.text = self.data.task_note_title
	self.node_list.select_txt.text.text = self.data.task_note_title
	local _, note_is_lock = TaskWGData.Instance:GetTaskNoteList(self.data.task_note_id)
	local is_flag = TaskWGData.Instance:GetBountyNoteRewardStatus(self.data.task_note_id) == 1
	local is_can_get = (not is_flag) and (not note_is_lock)
	self.node_list.remind:CustomSetActive(is_can_get)
end

function ShangJinStoryTagRender:OnSelectChange(is_select)
	self.node_list.normal:CustomSetActive(not is_select)
	self.node_list.select:CustomSetActive(is_select)
end

ShangJinStoryMessageCell = ShangJinStoryMessageCell or BaseClass(BaseRender)
function ShangJinStoryMessageCell:LoadCallBack()
	if not self.stroy_reward_list then
		self.stroy_reward_list = AsyncListView.New(ItemCell, self.node_list.stroy_reward_list)
		self.stroy_reward_list:SetStartZeroIndex(true)
	end
end

function ShangJinStoryMessageCell:__delete()
	if self.stroy_reward_list then
		self.stroy_reward_list:DeleteMe()
		self.stroy_reward_list = nil
	end
end

function ShangJinStoryMessageCell:OnFlush()
	if not self.data then
		return
	end

	local is_lock = self.data.is_lock
	local cfg = TaskWGData.Instance:GetBountyTaskCfgByTaskId(self.data.params)
	
	if not cfg then
		return
	end

	self.node_list.lock_image:CustomSetActive(is_lock)
	self.node_list.unlock_image:CustomSetActive(not is_lock)
	self.node_list.lock_image_2:CustomSetActive(is_lock)

	local str = is_lock and cfg.task_lock_desc or cfg.task_desc
	local color = is_lock and COLOR3B.C12 or COLOR3B.C24
	local title_color = is_lock and COLOR3B.C12 or COLOR3B.C23
	self.node_list.story_desc_txt.text.text = ToColorStr(str, color)
	self.node_list.story_title_txt.text.text = ToColorStr(cfg.task_title, title_color)
	
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.story_title_txt.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.story_title_root.rect)
	-- local reward_list = nil
	-- self.stroy_reward_list:SetDataList(reward_list)
end

--------------------------------------------------------------------------------
ShangJinStoryNoteRewardCell = ShangJinStoryNoteRewardCell or BaseClass(ItemCell)
function ShangJinStoryNoteRewardCell:OnFlush()
	ItemCell.OnFlush(self)
	self:SetCanOperateIconVisible(self.data.can_lq)
end

-- 点击格子
function ShangJinStoryNoteRewardCell:OnClick()
	if self.data and (not self.data.can_lq) then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if nil == item_cfg then return end

		TipWGData.Instance:SetDefShowBuyCount(self.show_buy_num or 1)
		if self.need_item_get_way then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
		else
			TipWGCtrl.Instance:OpenItem(self.data, self.item_tip_from or ItemTip.FROM_NORMAL, nil, nil, self.item_tips_btn_click_callback)
		end
	else
		BaseRender.OnClick(self)
	end
end
