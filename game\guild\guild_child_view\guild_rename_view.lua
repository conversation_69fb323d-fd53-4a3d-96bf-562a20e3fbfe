GuildRenameView = GuildRenameView or BaseClass(SafeBaseView)
local MAX_CNAME_COUNT = 18 	--汉字最大长度
local MIN_CNAME_COUNT = 1 	--最小长度

function GuildRenameView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(600, 424)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_rename")
	self.item_change_event = BindTool.Bind(self.ItemChangeCallBack,self)
end

function GuildRenameView:ReleaseCallBack()
	if nil ~= self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
	self.input_text = nil
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_event)
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GuildRenameView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Guild.ChangeName
	--self:SetSecondView(Vector2(740,388))

	self.input_text = self.node_list["input_text"]
	self.node_list.InputField.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnInputChange, self))

	--XUI.AddClickEventListener(self.node_list["btn_windows_close"], (BindTool.Bind1(self.Close, self)))
	XUI.AddClickEventListener(self.node_list["btn_rename_confirm"], BindTool.Bind1(self.OnConfirmRenameHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_rename_cancel"], BindTool.Bind1(self.OnCancelRenameHandler, self))

	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_event)
	self.item_cell = ItemCell.New(self.node_list.item_cell)
    self.item_cell:SetNeedItemGetWay(true)
end

function GuildRenameView:ShowIndexCallBack()
	self:FlushTips()
end

function GuildRenameView:ItemChangeCallBack()
	self:FlushTips()
end

function GuildRenameView:FlushTips()
	local item_num = ItemWGData.Instance:GetItemNumInBagById( 26155)
	local color = item_num > 0 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	local str = ToColorStr(item_num .. "/1",color)
	local guild_vo = GuildDataConst.GUILDVO
	self.node_list.cur_name.text.text = guild_vo.guild_name
	-- self.node_list.tips.text.text = string.format(Language.Role.CurName)
	self.item_cell:SetData({item_id =  26155})
	self.item_cell:SetRightBottomText(str)
	self.item_cell:SetRightBottomTextVisible(true)
end

-- 发送改名申请
function GuildRenameView:OnConfirmRenameHandler()
	if nil ~= self.input_text then
		local text = self.node_list.InputField.input_field.text
		local len = string.len(text)
		if len <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Login.EditName)
			return
		end

		if string.len(text) < MIN_CNAME_COUNT then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent2)
			return
		end
		if string.len(text) > MAX_CNAME_COUNT then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent2)
			return
		end

		if ChatFilter.Instance:IsIllegal(text, true) or ChatFilter.IsEmoji(text) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
			return
		end

		local i, j = string.find(text, "*")
		if i ~= nil and j ~= nil then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
			return
		end

		local qukong_text = string.gsub(text, "%s", "")
		local qukong_text_len = string.len(qukong_text)
		--判断输入的名字是否带空格
		if qukong_text_len ~= len then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
			return
		end

		--判断改名卡是否足够
		local num = ItemWGData.Instance:GetItemNumInBagById( 26155)
		if num < 1 then
			GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM,  26155, 1, 15007)
			return
		end

		if nil == self.alert_window then
			self.alert_window = Alert.New()
		end
		local des = string.format(Language.Guild.ReNameConfirm, text)
		self.alert_window:SetLableString(des)
		self.alert_window:SetOkFunc(BindTool.Bind2(self.SendRenameHandler, self, text))
		self.alert_window:Open()
		-- self.input_text.text.text = ""
		-- self:Close()
		-- self:SendRenameHandler(text)
	end
end

function GuildRenameView:SendRenameHandler(text)
	if text and text ~= "" then
		RoleWGCtrl.Instance.SendGuildResetName(text)
		self:OnCancelRenameHandler()
	end
end

function GuildRenameView:OnInputChange()
	local role_name = self.node_list.InputField.input_field.text
	if role_name == "" then
		return
	end
	if ChatFilter.IsEmoji(role_name) then
		self.node_list.InputField.input_field.text = ""
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.IllegalContent)
	end
end

-- 取消改名申请 edit_input_4_4
function GuildRenameView:OnCancelRenameHandler()
	if nil ~= self.input_text then
		self.input_text.text.text = ""
		self:Close()
	end
end
