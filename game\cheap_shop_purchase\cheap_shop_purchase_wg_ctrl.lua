require("game/cheap_shop_purchase/cheap_shop_purchase_wg_data")
require("game/cheap_shop_purchase/cheap_shop_purchase_view")

CheapShopPurchaseWGCtrl = CheapShopPurchaseWGCtrl or BaseClass(BaseWGCtrl)

function CheapShopPurchaseWGCtrl:__init()
	if CheapShopPurchaseWGCtrl.Instance then
		print_error("[CheapShopPurchaseWGCtrl]:Attempt to create singleton twice!")
	end

	CheapShopPurchaseWGCtrl.Instance = self
    self.data = CheapShopPurchaseWGData.New()
    self.view = CheapShopPurchaseView.New(GuideModuleName.CheapShopPurchaseView)

    self:RegisterAllProtocols()
end

function CheapShopPurchaseWGCtrl:__delete()
    if self.data then
        self.data:DeleteMe()
	    self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
	    self.view = nil
    end

    CheapShopPurchaseWGCtrl.Instance = nil
end

function CheapShopPurchaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOALimitRmbShopInfo,"OnSCOALimitRmbShopInfo")

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function CheapShopPurchaseWGCtrl:OnSCOALimitRmbShopInfo(protocol)
	--print_error("===========数据============", protocol)
	self.data:SetAllBuyInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.CheapShopPurchaseView)
end

function CheapShopPurchaseWGCtrl:ReqActivityLimitShopInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_SHOP
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function CheapShopPurchaseWGCtrl:OnPassDay()
    GlobalTimerQuest:AddDelayTimer(function()
		self:ReqActivityLimitShopInfo(OA_LIMIT_RMB_SHOP_OPERATE_TYPE.INFO)
	end, 2)
end