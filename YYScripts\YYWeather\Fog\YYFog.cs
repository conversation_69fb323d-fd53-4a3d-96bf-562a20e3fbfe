﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class YYFog<T> : MonoBehaviour where T : YYFogProfile
{

    [Header("Fog profile and material")]
    [Tooltip("Fog profile")]
    public T FogProfile;

    /// <summary>Fog material</summary>
    [Tooltip("Fog material")]
    public Material FogMaterial;

    protected virtual void OnEnable()
    {
    }

    protected virtual void OnDisable()
    {
    }

}
