
-- 攻击类型
AtkType = {
	Normal = 0,											-- 普攻
	Skill = 1,											-- 技能
}

-- 攻击缓存
AtkCache = {
	is_valid = false,									-- 是否有效
	atk_type = AtkType.Normal,							-- 攻击类型
	skill_id = 0,
	x = 0,
	y = 0,
	dir = 0,
	is_specialskill = false,
	special_distance = 0,
	target_obj = nil,
	target_obj_id = COMMON_CONSTS.INVALID_OBJID,
	range = 0,
	next_sync_pos_time = 0,
	attack_index = 1,
	use_type = ATTACK_USE_TYPE.GUAJI,
	skill_type = ATTACK_SKILL_TYPE.NONE,
}

-- 移动结束类型
MoveEndType = {
	Normal = 0,
	Fight = 1,											-- 使用AtkCache战斗
	AttackTarget = 2,									-- 攻击指定目标
	ClickNpc = 3,
	NpcTask = 4,										-- npc任务
	FightByMonsterId = 5,								-- 根据怪物id找怪战斗
	Gather = 6,											-- 采集
	GatherById = 7,										-- 根据采集id采集
	PickItem = 8,										-- 拾取掉落物
	Auto = 9,											-- 自动挂机
	FollowObj = 10,										-- 寻找目标后跟随
	EventObj = 11,										-- 抓鬼
	PickAroundItem = 12,								-- 拾取周围掉落物
	EnterStoryFb = 13,									-- 进入剧情副本
	DoNothing = 14,										-- 去到某标点不做任何事
	DefenseObj = 15,									-- 防御塔
}

-- 移动类型
MoveType = {
	None = -1,
	Pos = 0,											-- 移动到某个位置
	Obj = 1,											-- 移动到某个对象
	Fly = 2,											-- 直接飞到某个位置
}

-- 移动缓存
MoveCache = {
	cant_fly = false,
	is_valid = false,
	is_move_scan = false,
	end_type = MoveEndType.Normal,
	move_type = MoveType.None,
	scene_id = 0,
	x = 0,
	y = 0,
	target_obj = nil,
	target_obj_id = COMMON_CONSTS.INVALID_OBJID,
	range = 0,											-- 目标范围
	task_id = 0,
	task_type = -1,										-- 任务类型
	param1 = 0,
	param2 = 0,
	is_ignore_auto_fight = false,						-- 是否忽略寻路自动打怪
	follow_move_reason = CLIENT_MOVE_REASON.NONE, 		-- 跟随移动类型，用于在某些情况，不清除缓存的跟随信息
}

function MoveCache.SetEndType(end_type)
	MoveCache.end_type = end_type
end

function MoveCache.GetEndType()
	return MoveCache.end_type
end

-- 挂机类型
GuajiType = {
	None = 0,											-- 非挂机
	HalfAuto = 1,										-- 半自动挂机
	Auto = 2,											-- 自动挂机
	Monster = 3,										-- 指定怪
	Follow = 4,											-- 跟随目标
	Temporary = 5,										-- 临时状态
	WaBaoXunLu = 6,										-- 挖宝寻路状态
}

-- 捡掉落缓存
PickCache = {
	last_time = 0,
	interval = 0.2,
	AutoPickEquips = {[10100] = true, [10101] = true, [10300] = true, [10500] = true,}
}

-- 挂机缓存
GuajiCache = {
	guaji_type = GuajiType.None,
	target_obj = nil,
	target_obj_id = COMMON_CONSTS.INVALID_OBJID,
	is_click_select = false,
	monster_id = 0,
	event_guaji_type = GuajiType.None,					-- 主界面上的Event事件
}


--========================================
--===============挂机缓存监听===================
--========================================
local originalGuajiCache = GuajiCache
local rawData_GJC = {}
for k, v in pairs(originalGuajiCache) do
    rawData_GJC[k] = v
end

GuajiCache = setmetatable({}, {
    __index = function(_, key)
        return rawget(rawData_GJC, key)
    end,

    __newindex = function(_, key, value)
        local oldValue = rawget(rawData_GJC, key)
        rawset(rawData_GJC, key, value)
        if oldValue ~= value then
			-- if key == "target_obj_id" or key == "monster_id" or key == "guaji_type" then
			-- 	print_error(string.format("挂机缓存缓存改变 %s  %s ->  %s", key, oldValue, value))
			-- end

			-- if key == "target_obj" then
			-- 	print_error(string.format("挂机缓存缓存改变 %s  %s", key, value ~= nil))
			-- end

			GlobalEventSystem:Fire(GlobalTableValChange.GUAJI_CACHE, key, oldValue, value)
        end
    end,
})

--========================================
--===============攻击缓存监听===================
--========================================
local originalAtkCache = AtkCache
local rawData_AC = {}
for k, v in pairs(originalAtkCache) do
    rawData_AC[k] = v
end

AtkCache = setmetatable({}, {
    __index = function(_, key)
        return rawget(rawData_AC, key)
    end,

    __newindex = function(_, key, value)
        local oldValue = rawget(rawData_AC, key)
        rawset(rawData_AC, key, value)
        if oldValue ~= value then
			-- if key == "target_obj_id" or key == "range" then
			-- 	print_error(string.format("攻击缓存缓存改变 %s  %s ->  %s", key, oldValue, value))
			-- end

			-- if key == "target_obj" then
			-- 	print_error(string.format("攻击缓存缓存改变 %s  %s", key, value ~= nil))
			-- end

			GlobalEventSystem:Fire(GlobalTableValChange.ATK_CACHE, key, oldValue, value)
        end
    end,
})


--========================================
--===============移动缓存监听===================
--========================================
local originalMoveCache = MoveCache
local rawData_MC = {}
for k, v in pairs(originalMoveCache) do
    rawData_MC[k] = v
end

MoveCache = setmetatable({}, {
    __index = function(_, key)
        return rawget(rawData_MC, key)
    end,

    __newindex = function(_, key, value)
        -- local oldValue = rawget(rawData_MC, key)
        rawset(rawData_MC, key, value)
		-- if oldValue ~= value then
		-- 	if key == "end_type" or key == "move_type" or key == "range" then
		-- 		print_error(string.format("移动缓存改变 %s  %s ->  %s", key, oldValue, value))
		-- 	end
		-- end
    end,
})
