-- 天神3v3倒计时面板
TianShen3v3CountdownView = TianShen3v3CountdownView or BaseClass(SafeBaseView)
function TianShen3v3CountdownView:__init()
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_tianshen_3v3_countdown")
end

function TianShen3v3CountdownView:__delete()
end

function TianShen3v3CountdownView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("TianShen3v3CountdownView") then
		CountDownManager.Instance:RemoveCountDown("TianShen3v3CountdownView")
    end
    self:KillTweener()
end

function TianShen3v3CountdownView:LoadCallBack()
	if self.fill_tween then
        return
    end

    self.total_time = 3

    self:KillTweener()
    self.node_list.yuanquan.image.fillAmount = 1
    self.node_list.tuowei_img.image.fillAmount = 1
    self.node_list.tuowei_img.rect.localRotation = Quaternion.Euler(0, 0, 225)
    self.fill_tween = self.node_list.yuanquan.image:DOFillAmount(0, self.total_time):OnUpdate(function()
        local value = self.node_list.yuanquan.image.fillAmount
        local rotate = - 360 * value + 225
        if rotate > 135 then
            self.node_list.tuowei_img.image.fillAmount = value * 4
        end
        self.node_list.tuowei_img.rect.localRotation = Quaternion.Euler(0, 0, - 360 * value + 225)
    end):OnComplete(function()
        self.fill_tween = nil
        self:Close()
    end)
    
	self.node_list["daojishi_text"].text.text = math.floor(self.total_time)
	CountDownManager.Instance:RemoveCountDown("TianShen3v3CountdownView")
	CountDownManager.Instance:AddCountDown("TianShen3v3CountdownView", BindTool.Bind1(self.UpdateCountDownTime, self), nil, nil, self.total_time, 0.5)
end

-- 倒计时每次循环执行的函数
function TianShen3v3CountdownView:UpdateCountDownTime(elapse_time, total_time)
	if not self.node_list["daojishi_text"] then
		return
    end
    local last_time = math.floor(total_time - elapse_time)  
	self.node_list["daojishi_text"].text.text = last_time
end

function TianShen3v3CountdownView:OnFlush()

end

function TianShen3v3CountdownView:KillTweener()
    if self.fill_tween then
        self.fill_tween:Kill()
        self.fill_tween = nil
    end
end
