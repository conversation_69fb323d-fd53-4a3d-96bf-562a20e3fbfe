﻿    Shader "Hidden/Billboard/NormalBake"
{
    Properties
    {
        _BaseMap ("Texture", 2D) = "white" {}
        _BaseColor ("_Color", Color) = (1,1,1,1)
        _NormalMap ("Normal Map", 2D) = "bump" {}
        _Cutoff("Cutoff" , Range(0,1)) = 0.3
    }
    
    CGINCLUDE
    #include "UnityCG.cginc"

    sampler2D _BaseMap;
    sampler2D _NormalMap;
    float4 _BaseColor;
    float _Cutoff;

    struct Varyings
    {
        float2 uv : TEXCOORD0;
        float4 vertex : SV_POSITION;
        float3 normal : TEXCOORD1;
        float4 tangentToWorldX : TEXCOORD2;
        float4 tangentToWorldY : TEXCOORD3;
        float4 tangentToWorldZ : TEXCOORD4;
        float depth : TEXCOORD5;
    };

    Varyings vert(float4 uv : TEXCOORD0, float4 vertex : POSITION, float3 normal : NORMAL, float4 tangent : TANGENT)
    {
        Varyings o;
        o.vertex = UnityObjectToClipPos(vertex);
        o.uv = uv;

        float3 worldPos = mul(unity_ObjectToWorld, vertex);
        float3 worldNormal = UnityObjectToWorldNormal(normal);
        float3 worldTangent = UnityObjectToWorldDir(tangent.xyz);
        float3 worldBinormal = cross(worldNormal, worldTangent) * tangent.w;

        o.tangentToWorldX = float4(worldTangent.x, worldBinormal.x, worldNormal.x, worldPos.x);
        o.tangentToWorldY = float4(worldTangent.y, worldBinormal.y, worldNormal.y, worldPos.y);
        o.tangentToWorldZ = float4(worldTangent.z, worldBinormal.z, worldNormal.z, worldPos.z);

        o.depth = -(UnityObjectToViewPos(vertex).z * _ProjectionParams.w);
        o.normal = normal;
        return o;
    }
    
    ENDCG

    SubShader
    {
        Cull Off
        
        Pass
        {
            Name "Bake Normal"
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            float4 frag(Varyings input) : SV_Target
            {
                float4 col = tex2D(_BaseMap, input.uv);
                col *= _BaseColor;
                clip(col.a - _Cutoff);
                
                float3 worldNormal = input.normal;
                
                #if UNITY_REVERSED_Z
                input.depth = 1 - input.depth;
                #endif
                
                return float4((worldNormal.rgb * 0.5 + 0.5), input.depth);
            }
            ENDCG
        }
    }

}