OpenFunWGData = OpenFunWGData or BaseClass()

function OpenFunWGData:__init()
	if OpenFunWGData.Instance then
		print_error("[OpenFunWGData] Attemp to create a singleton twice !")
	end
	OpenFunWGData.Instance = self
	local notice_new_cfg = ConfigManager.Instance:GetAutoConfig("notice_new_auto")
	self.notice_list = notice_new_cfg.advance_notice
	self.notice_group_list = notice_new_cfg.advance_notice_group

	self.cache_name_single_map = {}
	self.trailer_last_reward_id = 0
	self.day_trailer_last_reward_id = 0
	self.quick_use_item_id = -1
	self.notice_day_fectch_flag_list = {}
end

function OpenFunWGData:__delete()
	OpenFunWGData.Instance = nil
end

function OpenFunWGData:GetTrailerLastRewardId()
	return self.trailer_last_reward_id or 0
end

function OpenFunWGData:SetTrailerLastRewardId(id)
	self.trailer_last_reward_id = id
end

-- 记录当前选择的id
function OpenFunWGData:RemberCurSelectId(index)
	self.cur_select_index = index
end

function OpenFunWGData:GetCurSelectId()
	return self.cur_select_index
end

--获取最后一个可领取任务或者可显示用于主界面
function OpenFunWGData:GetFirstCanLingQu()
	local list_info = self:GetNoticeList()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local open_level = ConfigManager.Instance:GetAutoConfig("notice_new_auto").other[1].open_level
	local list_num = list_info and #list_info or 0
	if list_num == 0 or role_level < open_level then
		return
	end

	local need_cfg = nil
	local is_can_lq = false

	for i=list_num, 1, -1 do
		local is_show,is_open,is_get = self:GetNoticeIsCanShow(list_info[i].id)
		local cfg = self.notice_list[list_info[i].id]

		if cfg and is_show and is_open and not is_get then
			need_cfg = cfg
			is_can_lq = true
			break
		end
	end

	--获取一个可展示但未解锁的(如果有覆盖上边获取的)
	for i=list_num, 1, -1 do
		local is_show,is_open,is_get = self:GetNoticeIsCanShow(list_info[i].id)
		local cfg = self.notice_list[list_info[i].id]

		if cfg and is_show and not is_open then
			need_cfg = cfg
		end
	end

	return need_cfg, is_can_lq
end

--获取第一个可领取index(第一个参数选中index，第二个参数滑动index)
function OpenFunWGData:GetFirstIndex()
	--因为不确定 后边的任务是否回比前边的先完成，所以要分两次取值
	--return两个相同的值是因为策划需求改变，不想大动，再者谨防再改回去
	local list_info = self:GetNoticeList()
	if #list_info == 0 then
		return nil,nil
	end
	for i=1,#list_info do
		local is_show,is_open,is_get = self:GetNoticeIsCanShow(list_info[i].id)
		if is_open and not is_get then
			return i,i
		end
	end
	for i=1,#list_info do
		local is_show,is_open,is_get = self:GetNoticeIsCanShow(list_info[i].id)
		if is_show and not is_open then
			return i,i
		end
	end
end

function OpenFunWGData:GetNoticeList()
	local list_info = {}
	local index = 0
	local is_show,is_open,is_get = false, false, false
	for k,cfg in pairs(self.notice_list) do
		is_show,is_open,is_get = self:GetNoticeIsCanShow(k)
		if is_show and cfg then
			index = cfg.show or (#list_info + 1)
			if is_open and not is_get then			-- 能领取
				list_info[index] = cfg
			elseif not is_open then					-- 没达到条件不能领取
				list_info[index + 1000] = cfg
			else									-- 已经领取
				list_info[index + 2000] = cfg
			end
		end
	end

	list_info = SortTableKey(list_info)
	return list_info
end

function OpenFunWGData:GetNoticeMaxNum()
	local other = ConfigManager.Instance:GetAutoConfig("notice_new_auto").other[1]
	return other.max_notice_num
end

function OpenFunWGData:GetNoticeIsCanShow(id)
	local is_show = false
	local is_open = false
	local is_get = false --领取
	if self.fun_notice_info and self.fun_notice_info[id] then
		local cfg = self.fun_notice_info[id]
		if 1 == cfg.reward_flag then
			is_get = true
		end
		if 1 == cfg.open_flag then
			is_open = true
		end
		if 1 == cfg.show_flag then
			is_show = true
		end		
	end
	return is_show,is_open,is_get
end

function OpenFunWGData:SetFunNoticeInfo(protocol)
	self.fun_notice_info = {}
	local count = 0
	local flag_1 = {}
	local flag_2 = {}
	local flag_3 = {}

	for i=1,16 do
		flag_1 = bit:d2b(protocol.notice_day_fectch_flag_list[i], flag_1) --奖励标志
		flag_2 = bit:d2b(protocol.notice_open_flag[i], flag_2) --打开标志
		flag_3 = bit:d2b(protocol.notice_show_flag[i], flag_3) --打开标志
		
		for i=0,7 do
			self.fun_notice_info[count] = {}
			self.fun_notice_info[count].reward_flag = flag_1[25 + i]
			self.fun_notice_info[count].open_flag = flag_2[25 + i]
			self.fun_notice_info[count].show_flag = flag_3[25 + i]
			count = count + 1
		end
	end
end

---[[
function OpenFunWGData:GetFuncTrailerCfgData()
	if not self.notice_group_list then
		return {}
	end
	local data_list = {}
	local cfg_list = self:GetNoticeList()
	for i=1,#self.notice_group_list do
		data_list[i] = {}
	end
	for i,v in ipairs(cfg_list) do
		table.insert(data_list[v.group_id], v)
	end
	return data_list
end

function OpenFunWGData:GetPageGroupCfg(id)
	if id then
		return self.notice_group_list[id]
	end
end

function OpenFunWGData:GetPageItemMaxNum()
	if not self.notice_group_list then
		return 0
	end
	return #self.notice_group_list
end
--]]

--[[
function OpenFunWGData:SetDayTrailerLastRewardId(list)
	-- self.notice_day_fectch_flag_list = {}
	-- local count = 0
	-- for k, v in pairs(list) do
	-- 	local bit_tab = bit:d2b(v)
	-- 	for i = 0, 7 do
	-- 		self.notice_day_fectch_flag_list[count] = bit_tab[33 - 8 + i]
	-- 		count = count + 1
	-- 	end
	-- end
end

function OpenFunWGData:GetDayTrailerLastRewardId(id)
	return self.notice_day_fectch_flag_list[id] or 0
end

function OpenFunWGData:GetNowDayOpenTrailerInfo()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local today_info, tomorrow_info = self:GetDayOpenTrailerCfg()
	local trailer_info = {}
	trailer_info.info_list = {}
	local i = 0
	for k,v in pairs(today_info) do
		if main_role_vo.level >= v.level_limit and self:GetDayTrailerLastRewardId(v.id) == 0 then
			i = i + 1
			trailer_info.is_tomorrow = false
			trailer_info.num = i
			table.insert(trailer_info.info_list, v)
		end
	end

	if i == 0 then
		for k,v in pairs(tomorrow_info) do
			i = i + 1
			trailer_info.is_tomorrow = true
			trailer_info.num = i
			table.insert(trailer_info.info_list, v)
		end
	end
	return trailer_info
end
-- 获取今日明日开启列表
function OpenFunWGData:GetDayOpenTrailerCfg()
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if self.cur_day == open_server_day then
		return self.today_info, self.tomorrow_info
	end
	local today_info = {}
	local tomorrow_info = {}
	for k, v in ipairs(self.notice_day_cfg) do
		if v.open_day == open_server_day then
			table.insert(today_info, v)
		elseif v.open_day == open_server_day + 1 then
			table.insert(tomorrow_info, v)
		end
	end
	self.today_info = today_info
	self.tomorrow_info = tomorrow_info
	self.cur_day = open_server_day
	return self.today_info, self.tomorrow_info
end
--]]