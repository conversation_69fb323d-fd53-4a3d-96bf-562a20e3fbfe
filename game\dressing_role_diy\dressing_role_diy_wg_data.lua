DressingRoleDiyWGData = DressingRoleDiyWGData or BaseClass()

function DressingRoleDiyWGData:__init()
	if DressingRoleDiyWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[DressingRoleDiyWGData] attempt to create singleton twice!")
		return
	end

	DressingRoleDiyWGData.Instance = self
	
	self.dressing_diy_app_list = {}
	self.type_active_list = {}
end

function DressingRoleDiyWGData:__delete()
	DressingRoleDiyWGData.Instance = nil
end

function DressingRoleDiyWGData:SetAllDiyAppearanceInfo(protocol)
	self.dressing_diy_app_list = protocol.dressing_diy_app_list
end

function DressingRoleDiyWGData:SetUpdateDiyAppearanceInfo(protocol)
	local data = protocol.change_data

	local seq = data.seq
	local use_project_id = data.use_project_id
	local project_list = data.project_list
	if self.dressing_diy_app_list[seq] then
		self.dressing_diy_app_list[seq].use_project_id = use_project_id
		self.dressing_diy_app_list[seq].project_list = project_list
	end
end

function DressingRoleDiyWGData:SetDiyAppearanceActiveInfo(protocol)
	self.type_active_list = protocol.type_active_list
end

function DressingRoleDiyWGData:GetDiyAppearanceActiveInfo(type, seq)
	return (self.type_active_list[type] or {})[seq] or 0
end
	
function DressingRoleDiyWGData:GetDiyAppearanceInfoBySexProf(sex, prof)
	local seq_cfg = RoleDiyAppearanceWGData.Instance:GetSeqCfgBySexProf(sex, prof)
	local seq = seq_cfg.seq or -1
	return self.dressing_diy_app_list[seq]
end