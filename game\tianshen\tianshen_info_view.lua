-- 已屏蔽未执行
TianShenView = TianShenView or BaseClass(SafeBaseView)

function TianShenView:LoadTianShenInfoViewCallBack()
    if nil == self.info_skill_list then
        self.info_skill_list = {}
        local num = self.node_list.info_zhu_skill.transform.childCount
        for i = 1, num do
            local cell = TianShenSkillItem.New(self.node_list.info_zhu_skill:FindObj("info_skill_" .. i))
            cell:SetIndex(i)
            self.info_skill_list[i] = cell
        end
    end

    if not self.info_star_list then
        self.info_star_list = {}
        for i=1,5 do
            self.info_star_list[i] = self.node_list["info_icon_" .. i]
        end
    end

    if nil == self.grid_ts_list then
        self.grid_ts_list = AsyncBaseGrid.New()
        local bundle = "uis/view/tianshen_prefab"
        local asset = "ph_tianshen_item2"
        self.grid_ts_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["info_icon_list"],
            assetBundle = bundle, assetName = asset, itemRender = TianShenItem})
        self.grid_ts_list:SetSelectCallBack(BindTool.Bind1(self.OnClickCInfoItemRenderCalllBack, self))
        self.grid_ts_list:SetStartZeroIndex(false)
    end

    if not self.info_quality_type_list_view then
        self.info_quality_type_list_view = AsyncListView.New(TianShenQualityTypeItem, self.node_list.info_quality_type_list)
        self.info_quality_type_list_view:SetStartZeroIndex(true)
        self.info_quality_type_list_view:SetSelectCallBack(BindTool.Bind(self.OnClickInfoTianShenQualityTypeItem, self))
    end
    self.info_quality_type_list_view:SetDataList(self:GetTianShenQualityListVo())

    if not self.info_role_model then
        self.info_role_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["info_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.info_role_model:SetRenderTexUI3DModel(display_data)
        -- self.info_role_model:SetUI3DModel(self.node_list["info_display"].transform, self.node_list.info_block.event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.info_role_model)
    end

    self.cur_info_select_tianshen_series = 0
    self:FlushInfoTsIconList(true)
    XUI.AddClickEventListener(self.node_list["info_quality_type_btn"], BindTool.Bind(self.OnClickInfoBtnTsQuality, self))
    XUI.AddClickEventListener(self.node_list["info_huamo_type_btn"], BindTool.Bind(self.OnClickInfoBtnTsHuaMo, self))
    XUI.AddClickEventListener(self.node_list["info_quality_click_mask"], BindTool.Bind(self.OnClickInfoBtnTsQualityMask, self))
    XUI.AddClickEventListener(self.node_list["info_huamo_reset_btn"], BindTool.Bind(self.OnClickInfoResetHuaMo, self))
    XUI.AddClickEventListener(self.node_list["info_skill_yulan_btn"], BindTool.Bind(self.OnClickInfoSkillYuLanBtn, self))
    XUI.AddClickEventListener(self.node_list["ts_shuangsheng_type_btn"], BindTool.Bind(self.OnClickInfoBtnTsShuangSheng, self))
end

function TianShenView:ReleaseTianShenInfoViewCallBack()
    if nil ~= self.info_skill_list then
        for k,v in pairs(self.info_skill_list) do
            v:DeleteMe()
        end

        self.info_skill_list = nil
    end

    if self.info_quality_type_list_view then
        self.info_quality_type_list_view:DeleteMe()
        self.info_quality_type_list_view = nil
    end

    if self.click_delay_hide_info_quality_type_timer then
        GlobalTimerQuest:CancelQuest(self.click_delay_hide_info_quality_type_timer)
        self.click_delay_hide_info_quality_type_timer = nil
    end

    if self.info_role_model then
        self.info_role_model:DeleteMe()
        self.info_role_model = nil
    end

    if self.grid_ts_list then
        self.grid_ts_list:DeleteMe()
        self.grid_ts_list = nil
    end

    if self.info_quality_type_list_show_tw then
        self.info_quality_type_list_show_tw:Kill()
        self.info_quality_type_list_show_tw = nil
    end

    self.last_show_info_tianshen_id = nil
    self.info_star_list = nil
    self.info_ts_select_index = nil
    self.cur_info_select_tianshen_series = 0
end

function TianShenView:CloseTianShenInfoCallBack()
    self.tianshen_info_audio_play_t = {}
end

function TianShenView:OpenTianShenInfoCallBack()
    self.tianshen_info_audio_play_t = {}
end

function TianShenView:TianShenInfoViewOnFlush(param_t)
    for k,v in pairs(param_t) do
        if k == "all" then
            self:FlushInfoTsIconList()
        elseif k == "flush_display" then
            self:FlushInfoModelDisPlay()
            self:FlushInfoHuaMo()
            self:FlushInfoTsIconList()
            self:FlushInfoShuangSheng()
        end
    end

    self:OnFlushInfoData()
    self:FlushInfoPasvSkill()
end

function TianShenView:FlushInfoTsIconList(is_form_loaded)
    --加载天神列表
    self:RefreshAccordionData(TianShenView.TabIndex.Activation)
    local data_list = TianShenWGData.Instance:GetTianShenInfoDataList(TianShenView.TabIndex.Activation)
    local data_list_t = {}
    if self.cur_info_select_tianshen_series == 0 then
        data_list_t = data_list
    else
        for k,v in pairs(data_list) do
            if self.cur_info_select_tianshen_series == v.series then
                table.insert(data_list_t, v)
            end
        end
    end

    self.node_list["info_num"].text.text = #data_list_t
    self.grid_ts_list:SetDataList(data_list_t)
    if is_form_loaded then
        ---检测是否有可以入魔
        local is_select_rumo = false
        local default_select_cell_index = 1
        local select_ts_index = data_list_t[1].index
        for k,v in pairs(data_list_t) do
            if TianShenHuamoWGData.Instance:GetTypeIndex(v.index) == 1 then
                select_ts_index = v.index
                is_select_rumo = true
                default_select_cell_index = k
                break
            end
        end

        self.grid_ts_list:JumpToIndexAndSelect(default_select_cell_index)
        self:SetInfoTsIconSelect(select_ts_index)
        self:GetInfoCurSelectListData(TianShenWGData.Instance:GetTianShenCfg(select_ts_index))
    else
        self:SetInfoTsIconSelect(nil == self.info_ts_select_index and 0 or self.info_ts_select_index)
    end
end

function TianShenView:SetInfoTsIconSelect(select_ts_index)
    TianShenWGData.Instance:InfoTsSelectIndex(select_ts_index)
    for k,v in pairs(self.grid_ts_list:GetAllCell()) do
        v:SetTsSelect(v and v:GetData() and v:GetData().index == select_ts_index)
    end
end

function TianShenView:OnFlushInfoData()
    if not self.info_select_data then return end
    local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.info_select_data.index)
    local data = TianShenWGData.Instance:GetTianShenCfg(self.info_select_data.index)
    local is_activate = TianShenWGData.Instance:IsActivation(self.info_select_data.index)
    local tianshen_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.info_select_data.index)
    if tianshen_cfg and tianshen_cfg.act_item_id then
        local act_item_cfg = ItemWGData.Instance:GetItemConfig(tianshen_cfg.act_item_id)
        local _, huamo_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(self.info_select_data.index)
        local normal_name = act_item_cfg and act_item_cfg.name or ""
        local rumo_name =  huamo_id and huamo_id ~= 0 and Language.TianShenHuaMo.DemonName[huamo_id] or nil
        if rumo_name then
            normal_name = string.format(rumo_name, normal_name)
        end
        self.node_list["info_name"].text.text = normal_name
    end
    
    self.node_list["info_cap_value"].text.text = TianShenWGData.Instance:GetActivationZhanLi(self.info_select_data.index, true)
    self.node_list["info_level"].text.text = tianshen_info.level
    local shenshi_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.info_select_data.index)
    local bundle, asset = ResPath.GetCommon("a2_quality_text_" .. shenshi_data.jingshen + 1)
    self.node_list.info_jingshen_img.image:LoadSprite(bundle, asset, function()
        self.node_list.info_jingshen_img.image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetCommon("a2_sl_di_" .. shenshi_data.jingshen + 1)
    self.node_list.info_jingshen_bg.image:LoadSprite(bundle, asset, function()
        self.node_list.info_jingshen_bg.image:SetNativeSize()
    end)

    if tianshen_info and is_activate then
        local golden_star_num  = math.ceil(tianshen_info.star / GameEnum.ITEM_MAX_STAR)
        local silver_star_num = tianshen_info.star % GameEnum.ITEM_MAX_STAR
        if tianshen_info.star > 0 and silver_star_num == 0 then
            silver_star_num = GameEnum.ITEM_MAX_STAR
        end

        local star_res_list = GetStarImgResByStar(tianshen_info.star)
        for k,v in pairs(self.info_star_list) do
            v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
        end
    end

    self.node_list.info_go_star:SetActive(is_activate)
    self.grid_ts_list:RefreshActiveCellViews()

    local shuangsheng_show_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarData(self.info_select_data.index)
    
    if (not shuangsheng_show_data) or (shuangsheng_show_data.curr_aura_id == -1) then
        self.node_list.sssl_info_level:SetActive(false)
    else
        self.node_list.sssl_info_level:SetActive(true)
        local app_image_id_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(shuangsheng_show_data.curr_aura_id)
        if app_image_id_data then
            self.node_list.sssl_info_level.image:LoadSprite(ResPath.GetCommon(string.format("a2_sssl_level_%d", app_image_id_data.sssl_index or 1)))
        else
            self.node_list.sssl_info_level:SetActive(false)
        end
    end
end

--刷新模型展示
function TianShenView:FlushInfoModelDisPlay()
    local data = self:GetInfoCurSelectListData()
    if data then
        local audio = self.tianshen_info_audio_play_t[data.index] == nil and data.show_audio or nil
        self.tianshen_info_audio_play_t[data.index] = 1
        ---添加化魔展示
        local appe_image_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(data.index) or data.appe_image_id
        -- if self.last_show_info_tianshen_id and appe_image_id == self.last_show_info_tianshen_id then
        --     return
        -- end

        self.last_show_info_tianshen_id = appe_image_id
        local is_have_shuangsheng, app_image_id_data = TianShenWGData.Instance:CheckHaveShuangShengTianShen(data.index)
        self.info_role_model:RemoveShuangShengTianShenUI(true)
        self.info_role_model:SetTianShenModel(appe_image_id, data.index, true, audio, SceneObjAnimator.Rest, function ()
            if is_have_shuangsheng and app_image_id_data ~= nil then
                self.info_role_model:TryChangePartScale(SceneObjPart.Main, app_image_id_data.model_scale)
            end
        end, app_image_id_data and app_image_id_data.shiqi_scale or 1)

        if is_have_shuangsheng and app_image_id_data ~= nil then
            -- 这里加多一项改变大小，相同的资源未变化会导致不会回调
            self.info_role_model:TryChangePartScale(SceneObjPart.Main, app_image_id_data.model_scale)
            self.info_role_model:SetShuangShengTianShenModel(app_image_id_data.appe_image_id)
        else
            self.info_role_model:TryResetChangePartScale(SceneObjPart.Main)
            self.info_role_model:TryResetChangePartScale(SceneObjPart.Weapon)
        end
    end
end

function TianShenView:FlushInfoHuaMo()
    local is_open_func = FunOpen.Instance:GetFunIsOpened(FunName.TianShenHuamoView)
    local select_data = self:GetInfoCurSelectListData()
    if not select_data then return end
    local is_open = TianShenHuamoWGData.Instance:GetHuaMoEnterButonState(select_data.index)
    local is_remind = TianShenHuamoWGData.Instance:GetSubRemind(select_data.index)
    self.node_list["info_huamo_type_btn"]:SetActive(is_open and is_open_func)
    self.node_list["info_huamo_reset_btn"]:SetActive(is_open and is_open_func)
    self.node_list["info_huamo_remind"]:SetActive(is_remind == 1 and is_open_func)
    local bianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(select_data.index)
    if not bianshen_cfg then
        return
    end
    self.node_list.info_huamo_icon.image:LoadSprite(ResPath.GetSkillIconById(bianshen_cfg.bianshen_icon))
end


-- 刷新双生神灵
function TianShenView:FlushInfoShuangSheng()
    local is_open_func = FunOpen.Instance:GetFunIsOpened(FunName.TianShenShuangShengView)
    local select_data = self:GetInfoCurSelectListData()
    if not select_data then return end
    local is_open = TianShenShuangShengWGData.Instance:IsHaveTianShenAvatarCfg(select_data.index)
    local is_remind = TianShenShuangShengWGData.Instance:GetOneTianShenAvatarRemind(select_data.index)
    local is_active = TianShenWGData.Instance:GetTianshenInfoStatus(select_data.index) == 1
    self.node_list["ts_shuangsheng_type_btn"]:SetActive(is_open and is_open_func and is_active)
    self.node_list["ts_shuangsheng_remind"]:SetActive(is_remind and is_open_func and is_active)
end

function TianShenView:GetInfoCurSelectListData(select_data)
    if nil == select_data then
        return self.info_select_data
    end

    self.info_select_data = select_data
    self:OnFlushInfoData()
end

function TianShenView:OnClickCInfoItemRenderCalllBack(item)
    if nil == item or nil == item:GetData() then
        return
    end
    
    local data = item:GetData()
    if self.info_ts_select_index == data.index then
        return
    end

    self.info_ts_select_index = data.index
    self:SetInfoTsIconSelect(data.index)
    self:GetInfoCurSelectListData(TianShenWGData.Instance:GetTianShenCfg(data.index))
    self:FlushInfoModelDisPlay()
    self:FlushInfoPasvSkill()
    self:FlushInfoHuaMo()
    self:FlushInfoShuangSheng()
end

function TianShenView:FlushInfoPasvSkill()
    local select_data = self:GetInfoCurSelectListData()
    if not select_data then return end

    local temp_pasv_skill = TianShenWGData.Instance:GetBeSkillShowCfgList(select_data.index)
    local zhu_skill = TianShenWGData.Instance:GetTianShenZhuSkill(select_data.index)
    for k,v in pairs(self.info_skill_list) do
        local skill_id = tonumber(zhu_skill[k])
        v:SetData({skill_id = skill_id, tianshen_index = self.info_select_data.index, from_view = FROM_TIANSHEN_UPGRADE, is_open_skill = true, is_tianshen_select_view = false})
    end
end

function TianShenView:OnClickInfoResetHuaMo()
    if self.info_select_data then
        local cur_index = self.info_select_data.index
        ---添加化魔展示
        local _, huanhua_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(cur_index)
        local shuangsheng_show_data = TianShenShuangShengWGData.Instance:GetTianShenAvatarData(cur_index)
        
        -- if huanhua_id == 0 then
        --     SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenHuaMo.ResetError)
        -- end

        -- if (not shuangsheng_show_data) or shuangsheng_show_data.curr_aura_id == -1 then
        --     SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenHuaMo.ResetError)
        -- end

        if huanhua_id ~= 0 then
            TianShenHuamoWGCtrl.Instance:CSTianShenRuMoHuanHua(cur_index, 0)
        end

        if shuangsheng_show_data and shuangsheng_show_data.curr_aura_id ~= -1 then
            TianShenShuangShengWGCtrl.Instance:SendOperateChangeAvatar(cur_index, 0, -1)
        end
    end
end

function TianShenView:OnClickInfoSkillYuLanBtn()
    CommonSkillShowCtrl.Instance:SetTianShenSkillViewDataAndOpen({tianshen_index = self.info_ts_select_index})
end

function TianShenView:OnClickInfoBtnTsQuality()
    if self.info_quality_type_list_view then
        self.info_quality_type_list_view:SetDataList(self:GetTianShenQualityListVo())
    end
    
    self:OnClickInfoBtnTsQualityMask()
end

function TianShenView:OnClickInfoBtnTsHuaMo()
    TianShenHuamoWGCtrl.Instance:OpenView()
end

function TianShenView:OnClickInfoBtnTsQualityMask(is_form_select_item)
    if not is_form_select_item and self.click_delay_hide_info_quality_type_timer then
        GlobalTimerQuest:CancelQuest(self.click_delay_hide_info_quality_type_timer)
        self.click_delay_hide_info_quality_type_timer = nil
    end

    local is_active = not self.node_list.info_quality_click_mask.gameObject.activeInHierarchy
    self.node_list.info_pinzhi_down:SetActive(not is_active)
    self.node_list.info_pinzhi_up:SetActive(is_active)
    self.node_list.info_quality_click_mask:SetActive(is_active)
    self.node_list.info_quality_type_list:SetActive(is_active)

    if self.ts_quality_type_list_show_tw then
        self.ts_quality_type_list_show_tw:Kill()
    end

    if self.info_quality_type_list_show_tw then
        self.info_quality_type_list_show_tw:Kill()
        self.info_quality_type_list_show_tw = nil
    end

    self.info_quality_type_list_show_tw = DG.Tweening.DOTween.Sequence()
    self.node_list.info_quality_type_list_bg:SetActive(is_active)
    if is_active then
        self.info_quality_type_list_show_tw:Append(self.node_list.info_quality_type_list.canvas_group:DoAlpha(0, 1, 0.2))
    else
        self.node_list.info_quality_type_list.canvas_group.alpha = 0
    end
end

function TianShenView:OnClickInfoTianShenQualityTypeItem(cell, cell_index, is_default, is_click)
    if not cell or not cell:GetData() then return end
    local cur_series = cell:GetData().series
    for k,v in pairs(self.info_quality_type_list_view:GetAllItems()) do
        v:SetSelect(v:GetData().series == cur_series)
    end

    self.cur_info_select_tianshen_series = cur_series
    self:FlushInfoTsIconList()

    self.node_list["info_quality_type_txt"].text.text = cur_series == 0 and "选择品质" or string.format(Language.TianShen.TianShenSeriesColor1[cur_series], Language.TianShen.TianShenSeriesColorStr[cur_series])

    if is_click then
        if self.click_delay_hide_info_quality_type_timer then
            GlobalTimerQuest:CancelQuest(self.click_delay_hide_info_quality_type_timer)
        end
        self.click_delay_hide_info_quality_type_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnClickInfoBtnTsQualityMask, self, true), 0.1)
    end
end

function TianShenView:OnClickInfoBtnTsShuangSheng()
    TianShenShuangShengWGCtrl.Instance:OpenView()
end