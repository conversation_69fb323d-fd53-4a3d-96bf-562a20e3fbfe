BountyTaskView = BountyTaskView or BaseClass(SafeBaseView)

FUBENSCENETYPEMIN = 84

function BountyTaskView:__init()
	self.view_layer = UiLayer.MainUI
	
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_bounty_common")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "TargetContent")
end

function BountyTaskView:__delete()
end

function BountyTaskView:ReleaseCallBack()
	if self.vis_word_delay then
		GlobalTimerQuest:CancelQuest(self.vis_word_delay)
		self.vis_word_delay = nil
	end

    if self.team_info_event then
        GlobalEventSystem:UnBind(self.team_info_event)
        self.team_info_event = nil
    end

    if self.team_cell_list and #self.team_cell_list ~= 0 then
	    for _, v in pairs(self.team_cell_list) do
	        if v then
	            v.team_view = nil
	            v:DeleteMe()
	        end
	    end
	end
	self.show_add_exp = nil
    self.button_content = nil
    self.add_exp_text = nil
    self.team_cell_list = {}
    self.list_view = nil

	self.img_word = nil
	-- MainuiWGCtrl.Instance:SetTaskActive(true)

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
	end
	self.is_out_fb = nil
end

function BountyTaskView:CloseCallBack()
	-- self.node_list["TargetContent_root"].transform:SetParent(self.root_node_transform, false)
	-- if self.root_node then
	-- 	self.root_node.gameObject:SetActive(false)
	-- end

	MainuiWGCtrl.Instance:SetFBNameState(false)

	self.is_out_fb = true
	if self.obj then
		self.obj:SetActive(false)
	end
end

function BountyTaskView:OpenCallBack()
	self.is_out_fb = nil
	if self.obj then
		self.obj:SetActive(true)
	end
end

function BountyTaskView:LoadCallBack()
	self.img_word = self.node_list["img_word"]
	self.img_word:SetActive(false)
	self.node_list.img_word_tip:SetActive(false)
end

function BountyTaskView:ShowIndexCallBack()
	-- self.root_node.gameObject:SetActive(true)
    self.node_list["TargetContent_root"]:SetActive(false)
    MainuiWGCtrl.Instance:AddInitCallBack(nil, function ( )
    	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
    	parent:SetActive(true)
    	self.node_list["TargetContent_root"]:SetActive(true)
    	self.node_list["TargetContent_root"].transform:SetParent(parent.transform, false)
		self.node_list["TargetContent_root"].rect.anchoredPosition = Vector2(0, -20.75)
    end)
    self.obj = self.node_list["TargetContent_root"].gameObject

	self:Flush()
end

function BountyTaskView:CreateTeam()
    NewTeamWGCtrl.Instance:Open()
end

function BountyTaskView:ExitClick()
    SocietyWGCtrl.Instance:SendExitTeam()
end

function BountyTaskView:OnTeamChange()


    if not self.list_view then
        return
    end

    self.team_list = {}
    local data = SocietyWGData.Instance:GetTeamMemberList()
    self.team_list = data

    local is_team = next(self.team_list) ~= nil
    self.list_view:SetActive(is_team)
    self.show_add_exp:SetActive(is_team)
    self.button_content:SetActive(not is_team)

    self.list_view.scroller:ReloadData(0)
    for i = 1, 3 do
        XUI.SetGraphicGrey(self.node_list["ImgPerson" .. i], not (i <= #self.team_list and self.team_list[i].is_online == 1))
    end
    -- end
    local exp_add = NewTeamWGData.Instance:GetExpAdd() * 15
    self.add_exp_text.text.text = Language.NewTeam.AddExpTeam .. exp_add .. "%"
end
-- 刷新组队List
function BountyTaskView:RefreshTeamListView(cell, data_index)
    data_index = data_index + 1
    local menber_cell = self.team_cell_list[cell]
    if menber_cell == nil then
        menber_cell = MainUiMenberCell.New(cell.gameObject)
        menber_cell.team_view = self
        self.team_cell_list[cell] = menber_cell
    end

    menber_cell:SetIndex(data_index)
    menber_cell:SetData(self.team_list[data_index])
end

function BountyTaskView:OnFlush()
	local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	if nil == fb_cfg then
		return
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ( )
		MainuiWGCtrl.Instance:SetFBNameState(true, fb_cfg.name)
	end)
	self.fuben_index = fb_cfg.scene_type - FUBENSCENETYPEMIN

	self:FlushTopDes(self.fuben_index)
	self:FlushBottomDes(self.fuben_index)
	self.node_list.img_word_tip:SetActive(Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_TASKFB_ZHILIAO)
end

function BountyTaskView:FlushTopDes(index)
	local fb_cfg = nil
	if Scene.Instance:GetSceneType() == SceneType.GAINT_FB then
		fb_cfg = FuBenWGData.Instance:GetTaskFBCfg().shizhu_fb
	elseif Scene.Instance:GetSceneType() == SceneType.DEVILCOME_FB then
		fb_cfg = FuBenWGData.Instance:GetTaskFBCfg().buff_fb
	elseif Scene.Instance:GetSceneType() == SceneType.KUNLUNTRIAL_FB then
		fb_cfg = FuBenWGData.Instance:GetTaskFBCfg().zibao_fb
	end

	local DesList = Language.FuBenPanelDes.TopDes[index]
	local fb_data = FuBenPanelWGData.Instance:GetFubenPanelData()
	if not DesList or not fb_data then return end

	self.node_list['rich_text_1']:SetActive(nil ~= DesList.Title1)
	self.node_list['rich_text_2']:SetActive(nil ~= DesList.Label1)
	self.node_list['rich_text_3']:SetActive(nil ~= DesList.Label2)

	local text_2, text_3, mon_cfg = nil, nil, nil
	local mon_num = 0
	if index == 3 then
		text_2 = string.format(DesList.Label1, fb_data.param1)
	elseif index == 7 then -- 心魔
		local monster_id
		mon_num, monster_id = FuBenPanelWGData.Instance:GetXinMoMonNum()

		if monster_id == nil then
			return
		end

		mon_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[monster_id]

		if mon_cfg == nil then
			return
		end

		text_2 = string.format(DesList.Label1, mon_cfg.name, fb_data.kill_boss_num or 0, mon_num)
	else
		if not fb_cfg then return end
		for k,v in pairs(fb_cfg) do
			mon_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v.monster_id]
			if not mon_cfg then return end
			mon_num = FuBenPanelWGData.Instance:GetTaskFBNum(mon_cfg.type)
			if 1 == mon_cfg.type then -- boss
				text_2 = string.format(DesList.Label1, mon_cfg.name, fb_data.kill_boss_num, mon_num)
			elseif 0 == mon_cfg.type then -- 小怪
				if 1 == index then
					local mon_cfg1 = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[fb_cfg[2].monster_id]
					local mon_cfg2 = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[fb_cfg[3].monster_id]
					text_3 = string.format(DesList.Label2, mon_cfg1.name, mon_cfg2.name, fb_data.kill_allmonster_num - fb_data.kill_boss_num, mon_num)
				else
					text_3 = string.format(DesList.Label2, mon_cfg.name, fb_data.kill_allmonster_num - fb_data.kill_boss_num, mon_num)
				end
			end
		end
	end
	self.node_list['rich_text_1'].text.text = DesList.Title1
	self.node_list['rich_text_2'].text.text = text_2
	self.node_list['rich_text_3'].text.text = text_3
end

function BountyTaskView:FlushBottomDes(index)
	self.node_list.title.text.text =  Language.FuBenPanelDes.DesTitle
	local str = Language.FuBenPanelDes.Des[index] or ""
	if index == 7 then
		self.node_list["skill"]:SetActive(true)
		self.node_list["text_rich_scroll2"]:SetActive(false)
		local fb_data = FuBenPanelWGData.Instance:GetFubenPanelData()
		local xinmo_skill_cfg = FuBenPanelWGData.Instance:GetXinMoFBSkillName(fb_data.param2)
		-- str = string.format(str, skill_name)
		if xinmo_skill_cfg then
			local skill_cfg = SkillWGData.Instance:GetPassiveSkillByIndex(xinmo_skill_cfg.skill_index)
			if not skill_cfg then return end
			local bundle,assect = ResPath.GetSkillIconById(skill_cfg.icon)
			self.node_list.skill_icon.image:LoadSprite(bundle,assect)
			self.node_list.skill_name.text.text = xinmo_skill_cfg.skill_name
			self.node_list.skill_desc.text.text = skill_cfg.desc
		end
	else
		self.node_list["skill"]:SetActive(false)
		self.node_list["text_rich_scroll2"]:SetActive(true)
		self.node_list["des"].text.text = str

	end
end

function BountyTaskView:VistWordImg(monster_id)
	if self.img_word == nil then return end
	self.img_word:SetActive(monster_id > 0)
	if monster_id > 0 then
			self.img_word.image:LoadSprite(ResPath.GetFuBenPanel("word_tip_"..monster_id))
		if self.vis_word_delay then
			GlobalTimerQuest:CancelQuest(self.vis_word_delay)
			self.vis_word_delay = nil
		end
		self.vis_word_delay = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.HiddenWordImg, self), 5)
	end
end

function BountyTaskView:HiddenWordImg()
	if self.img_word ~= nil then
		self.img_word:SetActive(false)
	end
end