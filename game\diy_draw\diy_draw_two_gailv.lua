--DIY1抽奖概率展示面板
DIYTwoProbabilityView = DIYTwoProbabilityView or BaseClass(SafeBaseView)

function DIYTwoProbabilityView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(866, 516)})
    self:AddViewResource(0, "uis/view/diy_draw_ui_prefab", "diydraw_probability")
    self:SetMaskBg(true, true)
end

function DIYTwoProbabilityView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function DIYTwoProbabilityView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.RebateGiftAct.ProbabilityTitle
     if not self.probability_list then
        self.probability_list = AsyncListView.New(DIYTwoGailvItemRender, self.node_list.ph_pro_list) 
    end
end

function DIYTwoProbabilityView:OnFlush()
    local info = DIYDrawWGData.Instance:GetDrawTwoProbabilityInfo()
    if info then
        self.probability_list:SetDataList(info)
    end
end

----------------------------------------------------------------------------------
DIYTwoGailvItemRender = DIYTwoGailvItemRender or BaseClass(BaseRender)
function DIYTwoGailvItemRender:__delete()
    
end

function DIYTwoGailvItemRender:LoadCallBack()
    
end

function DIYTwoGailvItemRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.bg:SetActive(self.index % 2 == 1)
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    --local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    local item_name = self.data.item_name
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
