LotteryTreeWGData = LotteryTreeWGData or BaseClass()

function LotteryTreeWGData:__init()
	if LotteryTreeWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[LotteryTreeWGData] attempt to create singleton twice!")
		return
	end
	LotteryTreeWGData.Instance = self

	self.server_total_times = 0
	self.yaoqianshu_total_gold = 0
	self.money_tree_server_reward_fetch_flag = {}
	self.hit_item_list = {}
	self.yaoqianshu_buy_info = {
		get_gold_per = 0,
		get_gold = 0,
		hit_item_list = {},
	}
	self.lottery_type = 1
end

function LotteryTreeWGData:__delete()
	LotteryTreeWGData.Instance = nil
end

function LotteryTreeWGData:SetMoneyTreeInfo(protocol)
	self.yaoqianshu_total_gold = protocol.yaoqianshu_total_gold or 0
	self.server_total_times = protocol.server_total_times or 0
	self.money_tree_server_reward_fetch_flag = bit:d2b(protocol.yaoqianshu_fetch_flag) or {}
end

function LotteryTreeWGData:SetMoneyTreeChouResultInfo(protocol)
	self.yaoqianshu_buy_info.get_gold_per = protocol.get_gold_per or 0
	self.yaoqianshu_buy_info.get_gold = protocol.get_gold or 0
	self.yaoqianshu_buy_info.hit_item_list = protocol.hit_item_list or {}
end

function LotteryTreeWGData:GetServerMoneyTreeTimes()
	return self.server_total_times or 0
end

function LotteryTreeWGData:GetServerMoneyTreePoolGold()
	return self.yaoqianshu_total_gold or 0
end

function LotteryTreeWGData:SetLotteryType(type)
	self.lottery_type = type
end

function LotteryTreeWGData:GetLotteryType()
	return self.lottery_type
end

function LotteryTreeWGData:GetLotteryCost()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if rand_config then
		return rand_config.money_tree_mode[self.lottery_type].cost_gold
	end
end

function LotteryTreeWGData:GetGridLotteryTreeData()
	-- local act_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()

	local act_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local money_tree_cfg = act_cfg.money_tree
	-- local money_tree_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_LOTTERY_TREE)

	local data_list = {}
	local index = 0
	for k,v in pairs(money_tree_cfg) do
		if index == 0 then
			local item = {}
			item.item_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
			item.num = 1
			item.is_bind = 1
			data_list[index] = item
			index = index + 1
		elseif 1 == v.show_item then
			data_list[index] = v.reward_item
			index = index + 1
		end
	end

	return data_list
end

-- 全服奖励data
function LotteryTreeWGData:GetGridLotteryTreeAllRewardData()
	-- local act_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()

	local act_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local money_tree_reward_cfg = act_cfg.money_tree_reward
	-- local money_tree_reward_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_LOTTERY_TREE)

	local data_list = {}
	for k,v in pairs(money_tree_reward_cfg) do
		local data = {}
		data.seq = v.seq
		data.item_id = v.reward_item.item_id
		data.is_bind = v.reward_item.is_bind
		data.num = v.reward_item.num
		data.fetch_reward_flag = self.money_tree_server_reward_fetch_flag[32 - v.seq] or 0
		data.buytimes_limit = v.buytimes_limit
		data.vip_limit = v.vip_limit
		data_list[v.seq] = data
	end

	return data_list
end