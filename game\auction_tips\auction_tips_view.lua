local time_state = {
    Wait = 1,
    Ing = 2,
    End = 3,
}

local time_state_res = {
    [time_state.Wait] = "a3_pm_bq_jjkq",
    [time_state.Ing] = "a3_pm_bq_zzjx",
    [time_state.End] = "a3_pm_bq_yjs",
}
local item_max = 3
local auction_type = 2
AuctionTipsView = AuctionTipsView or BaseClass(SafeBaseView)

function AuctionTipsView:__init()
	self:SetMaskBg(false,false)
    self.view_style = ViewStyle.Window

    local view_bundle = "uis/view/auction_tips_ui_prefab"

    self:AddViewResource(0, view_bundle, "layout_auction_tips")

end

function AuctionTipsView:LoadCallBack()

    XUI.AddClickEventListener(self.node_list.btn_close,BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_go,BindTool.Bind(self.OnClickBtnGo, self))
    
    self.period_list = {}
    for i = 1, item_max do
        self.period_list[i] = AuctionTipsPeriodItemRender.New(self.node_list["count_item_"..i])
        self.period_list[i]:SetClickCallBack(BindTool.Bind(self.ChangeGroup, self))
        self.period_list[i]:SetIndex(i)
    end

    self.item_cell = ItemCell.New(self.node_list["cell_node"])
    self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)

    self.node_list.text_desc.text.text = Language.AuctionTips.Desc
end

function AuctionTipsView:OnClose()
    self:CleanTimer()
end

function AuctionTipsView:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    for i = 1, item_max do
        if self.period_list[i] then
            self.period_list[i]:DeleteMe()
            self.period_list[i] = nil
        end
    end
end

-- 清除倒计时器2
local time_key = "auction_tips_view_close"
function AuctionTipsView:CleanTimer()
	if CountDownManager.Instance:HasCountDown(time_key) then
		CountDownManager.Instance:RemoveCountDown(time_key)
	end
end

function AuctionTipsView:OnFlush()
    local round,round_time_data = AuctionTipsWGData.Instance:CheckInPeriodBuType(auction_type)
    
    self.index = 0
    self:CleanTimer()
    if round ~= 0 and round_time_data then
        for i = 1, item_max do
            local data = round_time_data.timestemp_list[i]
            self.period_list[i]:SetData(data)
            if data then
                local state = self.period_list[i]:CheckState()
                if (state == time_state.Ing or state == time_state.Wait) and self.index == 0 then
                    self.period_list[i]:OnClick()
                end
            end
        end
        
        local total_time = round_time_data.end_timestemp - TimeWGCtrl.Instance:GetServerTime()
        if total_time > 0 then
            CountDownManager.Instance:AddCountDown(time_key, nil,
                -- 倒计时完成回调方法
                function()
                    self:Close()
                end,
                nil, total_time
            )
        end
    end
end

function AuctionTipsView:OnClickBtnGo()
    ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.market_country_auction)
end

function AuctionTipsView:ChangeGroup(cell)
    self.select_item_data = cell.data
    self.index = cell.index
	
    local data_list = AuctionTipsWGData.Instance:GetGroupCfg(auction_type, self.select_item_data.round, self.select_item_data.group)

    local item_id_list = {}
    for key, value in pairs(data_list) do
        if value.special == 1 then
            self.item_cell:SetData({item_id = value.item_id})
        else
            table.insert(item_id_list, {item_id = value.item_id})
        end
    end

    self.item_list:SetDataList(item_id_list)

    for i = 1, item_max do
        if self.period_list[i] then
            self.period_list[i]:OnSelectChange(self.index == i)
        end
    end
end

-------------------------------------------------------------------------------------------------

AuctionTipsPeriodItemRender = AuctionTipsPeriodItemRender or BaseClass(BaseRender)

function AuctionTipsPeriodItemRender:LoadCallBack()

    -- XUI.AddClickEventListener(self.node_list.btn_reward,BindTool.Bind(self.OnClickBtnReward, self))
end

function AuctionTipsPeriodItemRender:__delete()
    self:CleanTimer()
end

function AuctionTipsPeriodItemRender:OnFlush()
	if not self.data then
        self.view:SetActive(false)
		return
	end 

    self.view:SetActive(true)
    
    -- 场次
    self.node_list.text_index.text.text = string.format(Language.AuctionTips.IndexStr, NumberToChinaNumber(self.index))
    self.node_list.text_index_hl.text.text = string.format(Language.AuctionTips.IndexStr, NumberToChinaNumber(self.index))
    -- 时间
    self.node_list.text_time.text.text = string.format("%s-%s",TimeUtil.FormatHM(self.data.start_timestemp), TimeUtil.FormatHM(self.data.end_timestemp))
    self.node_list.text_time_hl.text.text = string.format("%s-%s",TimeUtil.FormatHM(self.data.start_timestemp), TimeUtil.FormatHM(self.data.end_timestemp))
    self:FlushState()

   
end

function AuctionTipsPeriodItemRender:CleanTimer()
	if CountDownManager.Instance:HasCountDown("auction_round_item_"..self.index) then
		CountDownManager.Instance:RemoveCountDown("auction_round_item_"..self.index)
	end
end

function AuctionTipsPeriodItemRender:FlushState()
    -- 状态
    local state = self:CheckState()
    if nil == self.state or state~=self.state then
        local bundle, asset = ResPath.GetAuctionTipsImg(time_state_res[state])
        self.node_list.img_state.image:LoadSprite(bundle, asset, function()
            self.node_list.img_state.image:SetNativeSize()
        end)
    
        self.node_list.img_state_hl.image:LoadSprite(bundle, asset, function()
            self.node_list.img_state_hl.image:SetNativeSize()
        end)
    
        self.node_list.text_state.text.text = Language.AuctionTips.StateStr[state]
        self.node_list.text_state_hl.text.text = Language.AuctionTips.StateStr[state]

        self.state = state
    end

    self:CleanTimer()
    local total_time = self.data.start_timestemp - TimeWGCtrl.Instance:GetServerTime()
    if total_time <= 0 then
        total_time = self.data.end_timestemp - TimeWGCtrl.Instance:GetServerTime()
    end
    if total_time > 0 then
        CountDownManager.Instance:AddCountDown("auction_round_item_"..self.index,nil,
            -- 倒计时完成回调方法
            function()
                self:FlushState()
            end,
            nil,total_time)
    end
end

function AuctionTipsPeriodItemRender:CheckState()
    local server_time = TimeWGCtrl.Instance:GetServerTime()

    if server_time < self.data.start_timestemp then
        return time_state.Wait
    elseif server_time > self.data.end_timestemp then
        return time_state.End
    else
        return time_state.Ing
    end
end

function AuctionTipsPeriodItemRender:OnSelectChange(is_select)
    self.node_list.image:CustomSetActive(not is_select)
    self.node_list.image_hl:CustomSetActive(is_select)
end



