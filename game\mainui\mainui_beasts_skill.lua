local SkillAnchoredPos = {
	cell_pos = {
		[0] = Vector3(638, -48, 0),	-- 位置
		[1] = Vector3(516, -48, 0),
		[2] = Vector3(394, -48, 0),
	},
	cell_scale = {
		[0] = Vector3(1, 1, 1),		-- 大小
		[1] = Vector3(1, 1, 1),
		[2] = Vector3(1, 1, 1),
	},
}

local SkillIndex = 0		-- 技能位置

MainUiBeastsSkill = MainUiBeastsSkill or BaseClass(BaseRender)

function MainUiBeastsSkill:__init()

end

function MainUiBeastsSkill:__delete()
	self.cooling_status = nil
	self:RemoveCountDownTimer()
end


function MainUiBeastsSkill:LoadCallBack()
	local trigger_listener_cell = self.node_list.trigger_listener
	if trigger_listener_cell.event_trigger_listener then
		trigger_listener_cell.event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnSkillUp, self, self.data))
		trigger_listener_cell.event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDragSkill, self, self.data))
		trigger_listener_cell.event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnClickRangSkill, self, self.data))
		trigger_listener_cell.event_trigger_listener:AddPointerExitListener(BindTool.Bind(self.CheckRangState, self, self.data))
	end
end

function MainUiBeastsSkill:SetParent(parent)
	self.parent = parent
end

function MainUiBeastsSkill:OnSkillUp(data)
	if self:IsNotBattleOrLock() then
		local is_open, open_str = FunOpen.Instance:GetFunIsOpened(GuideModuleName.ControlBeastsView, true)
		if not is_open then
			SysMsgWGCtrl.Instance:ErrorRemind(open_str)
			return
		end

		ViewManager.Instance:Open(GuideModuleName.ControlBeastsView, TabIndex.beasts_battle)
		return
	end

	if not self:IsSkillPos() then
		if not self:IsCoolingFinish() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.BeastsChangeCool)
			return
		end
		ControlBeastsWGCtrl.Instance:SendCSChangeBeast(self.data.battle_index)
	else
		if not self:IsCoolingFinish() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.BeastsSkillCool)
			return
		end

		local view = MainuiWGCtrl.Instance:GetView()
		if view then
			view:OnDoSkillByBeastsFunc(self.data)
		end
	end
end

function MainUiBeastsSkill:OnDragSkill(data, eventData)
	if (not self:IsSkillPos()) or self:IsNotBattleOrLock() or (not self:IsCoolingFinish()) then
		return
	end

	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:OnDragSkill(self.data, eventData)
	end
end

function MainUiBeastsSkill:OnClickRangSkill(data)
	if (not self:IsSkillPos()) or self:IsNotBattleOrLock() or (not self:IsCoolingFinish()) then
		return
	end

	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:OnClickRangSkill(self.data)
	end
end

function MainUiBeastsSkill:CheckRangState(data)
	if (not self:IsSkillPos()) or self:IsNotBattleOrLock() or (not self:IsCoolingFinish()) then
		return
	end

	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:CheckRangState(self.data)
	end
end

-- 是否为当前技能位
function MainUiBeastsSkill:IsSkillPos()
	if not self.data then
		return false
	end

	return self.data.battle_index == SkillIndex
end

-- 是否冷却完成
function MainUiBeastsSkill:IsCoolingFinish()
	return self.cooling_status
end

-- 是否已上阵且存在技能
function MainUiBeastsSkill:IsNotBattleOrLock()
	if not self.data then
		return true
	end

	if self.data == nil or self.data.beast_id == 0  or self.data.skill_id == 0 then
		return true
	end

	return false
end


function MainUiBeastsSkill:OnFlush()
	local is_open, open_str = FunOpen.Instance:GetFunIsOpened(GuideModuleName.ControlBeastsView, true)
	self:SetVisible(is_open)

	if (not self.data) or (not is_open) then
		return
	end

	self.node_list.cooling:CustomSetActive(false)
	self.node_list.skill_lock:CustomSetActive(false)
	self.node_list.add_button:CustomSetActive(false)
	self.node_list.icon:CustomSetActive(false)

	if self:IsNotBattleOrLock() then
		local client_hole_id = self.data.battle_index + 1
		local battle_data = ControlBeastsWGData.Instance:GetHoleMainDataByHoleId(client_hole_id)

		if battle_data then
			self.node_list.skill_lock:CustomSetActive(battle_data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)
			self.node_list.add_button:CustomSetActive(battle_data.state == BEASTS_HOLE_STATUS.ACTIVE)
		end
	else
		if self.data.old_battle_index ~= nil and self.data.old_battle_index ~= self.data.battle_index then	-- 执行换位动画
			self:FlushTweenAnim(self.data.battle_index)
		else
			self:FlushSkillInfo()
		end
	end
end

function MainUiBeastsSkill:FlushTweenAnim(battle_index)
	self:KillShowPanelTween()
	local TWEEN_TIME = 0.3
	local pos = SkillAnchoredPos.cell_pos[battle_index] or Vector3(0, 0, 0)
	local scale = SkillAnchoredPos.cell_scale[battle_index] or Vector3(1, 1, 1)
	self.show_panel_tween = DG.Tweening.DOTween.Sequence()
	local pos_tween = self.view.transform:DOAnchorPos(pos, TWEEN_TIME)				-- 位置
	local scale_tween = self.view.transform.transform:DOScale(scale, TWEEN_TIME)				-- 大小
	self.show_panel_tween:Join(pos_tween)
	self.show_panel_tween:Join(scale_tween)

	self.show_panel_tween:OnComplete(function()
		self:FlushSkillInfo()
	end)
end

function MainUiBeastsSkill:KillShowPanelTween()
	if self.show_panel_tween ~= nil then
		self.show_panel_tween:Kill()
		self.show_panel_tween = nil
	end
end

function MainUiBeastsSkill:FlushSkillInfo()
	self:RemoveCountDownTimer()
	if not self.data then return end

	local cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.beast_id)
	local main_role = Scene.Instance:GetMainRole()
	local icon_id = string.format("a3_hs_head_%s", cfg and cfg.chang_head or 30470)
	local bundle, name = ResPath.GetControlBeastsImg(icon_id)
	local skill_reset_time = self.data.cd_finish_time - TimeWGCtrl.Instance:GetServerTime()
	local change_reset_time = self.data.cd_change_time - TimeWGCtrl.Instance:GetServerTime()

	local skill_cfg = SkillWGData.Instance:GetBeastsSkillById(self.data.skill_id)
	local skill_cd = skill_cfg and skill_cfg.cd_s or 0
	local beast_base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
	local change_cd = beast_base_cfg and beast_base_cfg.beast_change_skill_cd or 0
	local cd_time = (self:IsSkillPos() and skill_cd or change_cd) / 1000
	local reset_time = (self:IsSkillPos() and skill_reset_time or change_reset_time) + 1
	self.node_list.cooling:CustomSetActive((not self:IsSkillPos()))
	-- self.node_list.beast_skill_txt.text.text = cfg and cfg.beast_name or ""

	if self:IsSkillPos() then
		icon_id = SkillWGData.Instance:GetSkillIconId(self.data.skill_id)
		bundle, name = ResPath.GetSkillIconById(icon_id)
		-- self.node_list.beast_skill_txt.text.text = skill_cfg and skill_cfg.skill_name or ""
	end

	if not self:IsSkillPos() and main_role ~= nil then
		local beast_obj = main_role:FindBeast()
		if beast_obj and beast_obj:IsAtk() then
			local time = beast_obj:GetCurrAnimTime()

			if time then
				if reset_time < 0 then
					reset_time = time + 0.2
				else
					reset_time = reset_time + time + 0.2
				end
			end
		end
	end

	self.node_list.icon:CustomSetActive(true)
	local scale = self:IsSkillPos() and 1.4 or 1.15
	self.node_list.icon:SetLocalScale(scale, scale, scale)
	self.node_list.icon.image:LoadSprite(bundle, name, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	if reset_time > 0 then
		self.cooling_status = nil
		self.node_list.cd_mark.image.fillAmount = reset_time / cd_time
		self.node_list.cd_text.text.text = string.format("%.1f", reset_time)

		self.count_down = CountDown.Instance:AddCountDown(reset_time, 0.05,
			function (elapse_time, total_time)
				local interval_time = total_time - elapse_time
				self.node_list.cd_mark.image.fillAmount = interval_time / cd_time
				self.node_list.cd_text.text.text = string.format("%.1f", interval_time)
			end,

			function()
				self.node_list.cd_mark.image.fillAmount = 0
				self.node_list.cd_text.text.text = ""
				self.cooling_status = true
			end
		)
	else
		self.node_list.cd_mark.image.fillAmount = 0
		self.node_list.cd_text.text.text = ""
		self.cooling_status = true
	end

	-- 加2秒误差
	local skill_reset_second_time = skill_reset_time + 1
	if skill_reset_second_time > 0 then
		XUI.SetGraphicGrey(self.node_list.cooling, true)
		self.skill_cool_count_down = CountDown.Instance:AddCountDown(skill_reset_second_time, 1,
			function (elapse_time, total_time)
				local interval_time = total_time - elapse_time
			end,

			function()
				XUI.SetGraphicGrey(self.node_list.cooling, false)
			end
		)
	else
		XUI.SetGraphicGrey(self.node_list.cooling, false)
	end
end

function MainUiBeastsSkill:RemoveCountDownTimer()
	if self.count_down then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end

	if self.skill_cool_count_down then
		CountDown.Instance:RemoveCountDown(self.skill_cool_count_down)
		self.skill_cool_count_down = nil
	end
end