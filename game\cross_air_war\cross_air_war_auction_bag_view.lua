--新寻宝仓库
CrossAirWarAuctionBagView = CrossAirWarAuctionBagView or BaseClass(SafeBaseView)

function CrossAirWarAuctionBagView:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "CrossAirWarAuctionBagView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(1, -26), sizeDelta = Vector2(814, 524)})
    self:AddViewResource(0, "uis/view/cross_air_war_ui_prefab", "layout_kf_air_war_auction_bag")
    self:SetMaskBg(true, true)
end

function CrossAirWarAuctionBagView:ReleaseCallBack()
    if self.storage_grid then
        self.storage_grid:DeleteMe()
        self.storage_grid = nil
    end
end

function CrossAirWarAuctionBagView:LoadCallBack()
    self.open_num = 207
    self.node_list.title_view_name.text.text = Language.CrossAirWar.AuctionBagTitle
    if not self.storage_grid then
		self.storage_grid = AsyncBaseGrid.New()   
        self.storage_grid:SetStartZeroIndex(false)              
		self.storage_grid:CreateCells({col = 9, cell_count = self.open_num, list_view = self.node_list["ph_bag_grid"], itemRender = ItemCell})
    end

    self.node_list["btn_onekey_getout"].button:AddClickListener(BindTool.Bind(self.Close, self))
end

function CrossAirWarAuctionBagView:OnFlush(param)
	local bag_list = CrossAirWarWGData.Instance:GetAuctionBagListForGrade()
    self.storage_grid:SetDataList(bag_list)
    self.storage_grid:ReloadData(0)
end
