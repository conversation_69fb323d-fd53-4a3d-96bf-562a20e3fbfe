﻿using UnityEngine;

public class AnimatorCull : MonoBehaviour
{
    private bool hadGetAnimator = false;
    private Animator animator = null;
    private int[] defaultStateNames = null;
    private bool oldAnimatorEnabled = true;
    private AnimatorControllerParameter[] defaultParamters = null;

    private void Awake()
    {
        TryInitAnimator();
    }

    public Animator TryInitAnimator()
    {
        this.ResetAnimator();
        this.ResetDefaultStateName();
        this.ResetDefaultParameter();
        return animator;
    }

    private Animator ResetAnimator()
    {
        if (null == animator && !hadGetAnimator && null != gameObject)
        {
            hadGetAnimator = true;
            animator = gameObject.GetComponentInChildren<Animator>();
            if (null != animator)
            {
                oldAnimatorEnabled = animator.enabled;
            }
        }

        return animator;
    }

    private void ResetDefaultStateName()
    {
        if (null == defaultStateNames
            && null != animator && null != animator.runtimeAnimatorController)
        {
            defaultStateNames = new int[animator.layerCount];
            for (int i = 0; i < animator.layerCount; i++)
            {
                var info = animator.GetCurrentAnimatorStateInfo(i);
                defaultStateNames[i] = info.shortNameHash;
            }
        }
    }

    // animator.parameters触发频率高时将会导致大量GC
    private void ResetDefaultParameter()
    {
        if (null == defaultParamters
            && null != animator && null != animator.runtimeAnimatorController)
        {
            AnimatorControllerParameter[] paramters = animator.parameters;
            defaultParamters = new AnimatorControllerParameter[paramters.Length];
            for (int i = 0; i < paramters.Length; i++)
            {
                defaultParamters[i] = paramters[i];
            }
        }
    }

    // 恢复AnimatorData数据
    public void ResumeAnimatorData()
    {
        TryInitAnimator();

        if (null == animator)
        {
            return;
        }

        if (null != animator.runtimeAnimatorController
            && null != defaultStateNames && defaultStateNames.Length == animator.layerCount)
        {
            for (int i = 0; i < animator.layerCount; i++)
            {
                animator.Play(defaultStateNames[i], i);
            }
        }

        if (null != animator.runtimeAnimatorController && null != defaultParamters)
        {
            for (int i = 0; i < defaultParamters.Length; i++)
            {
                AnimatorControllerParameter parameter = defaultParamters[i];
                if (parameter.type == AnimatorControllerParameterType.Int)
                {
                    animator.SetInteger(parameter.name, parameter.defaultInt);
                }
                if (parameter.type == AnimatorControllerParameterType.Float)
                {
                    animator.SetFloat(parameter.name, parameter.defaultFloat);
                }
                if (parameter.type == AnimatorControllerParameterType.Bool)
                {
                    animator.SetBool(parameter.name, parameter.defaultBool);
                }
            }
        }
    }

    public void SetIsCulled(bool isCulled)
    {
        this.TryInitAnimator();

        if (null != animator)
        {
            if (isCulled)
            {
                oldAnimatorEnabled = animator.enabled;
                animator.enabled = false;
            }
            else
            {
                animator.enabled = oldAnimatorEnabled;
                this.ResumeAnimatorData();
            }
        }
    }
}
