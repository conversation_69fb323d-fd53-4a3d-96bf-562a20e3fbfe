function EquipmentView:InitZhuShenView()
    self.zhushen_select_equip_part = -1
	self.zhushen_select_equip_data = nil

    XUI.AddClickEventListener(self.node_list["btn_zhushen_skill_tips"], BindTool.Bind(self.OnClickOpenZhuShenSkillTips, self))
    XUI.AddClickEventListener(self.node_list["btn_zhushen_uplevel"], BindTool.Bind(self.OnClickOpenZhuShenUpLevel, self))
    XUI.AddClickEventListener(self.node_list["zhushen_btn_tips"], BindTool.Bind(self.OnClickZhuShenTipsBtn, self))
    
    if not self.equipment_zhushen_list then
		self.equipment_zhushen_list = AsyncListView.New(EquipZhuShenItemRender, self.node_list["equipment_zhushen_list"])
		self.equipment_zhushen_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectZhu<PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	end

    if not self.zhushen_cur_item then
        self.zhushen_cur_item = ItemCell.New(self.node_list["zhushen_cur_item_pos"])
    end

    if not self.zhushen_next_item then
        self.zhushen_next_item = ItemCell.New(self.node_list["zhushen_next_item_pos"])
    end

    if not self.zhushen_up_item then
        self.zhushen_up_item = ItemCell.New(self.node_list["zhushen_up_item_pos"])
    end

    if self.zhushen_cur_attr_list == nil then
        self.zhushen_cur_attr_list = {}
        local node_num = self.node_list["zhushen_cur_attr_list"].transform.childCount
        for i = 1, node_num do
            self.zhushen_cur_attr_list[i] = CommonAddAttrRender.New(self.node_list["zhushen_cur_attr_list"]:FindObj("attr_" .. i))
            self.zhushen_cur_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    if self.zhushen_next_attr_list == nil then
        self.zhushen_next_attr_list = {}
        local node_num = self.node_list["zhushen_next_attr_list"].transform.childCount
        for i = 1, node_num do
            self.zhushen_next_attr_list[i] = CommonAddAttrRender.New(self.node_list["zhushen_next_attr_list"]:FindObj("attr_" .. i))
            self.zhushen_next_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    self.zhushen_skill_item_list = {}
    for i = 1, 3 do
        self.zhushen_skill_item_list[i] = ZhuShenSkillRender.New(self.node_list["zhushen_skill_" .. i])
        self.zhushen_skill_item_list[i]:SetIndex(i)
    end
end

function EquipmentView:DeleteZhuShenView()
    self.zhushen_select_equip_part = -1
	self.zhushen_select_equip_data = nil

    if self.equipment_zhushen_list then
        self.equipment_zhushen_list:DeleteMe()
        self.equipment_zhushen_list = nil
    end

    if self.zhushen_cur_item then
        self.zhushen_cur_item:DeleteMe()
        self.zhushen_cur_item = nil
    end

    if self.zhushen_next_item then
        self.zhushen_next_item:DeleteMe()
        self.zhushen_next_item = nil
    end

    if self.zhushen_up_item then
        self.zhushen_up_item:DeleteMe()
        self.zhushen_up_item = nil
    end

    if self.zhushen_cur_attr_list then
        for k, v in pairs(self.zhushen_cur_attr_list) do
            v:DeleteMe()
        end
        self.zhushen_cur_attr_list = nil
    end

    if self.zhushen_next_attr_list then
        for k, v in pairs(self.zhushen_next_attr_list) do
            v:DeleteMe()
        end
        self.zhushen_next_attr_list = nil
    end

    if self.zhushen_skill_item_list then
        for k,v in pairs(self.zhushen_skill_item_list) do
            v:DeleteMe()
        end
        self.zhushen_skill_item_list = nil
    end
end

function EquipmentView:OnSelectZhuShenHandler(item)
    if nil == item or nil == item.data then
		return
	end

	if item.data.index == self.zhushen_select_equip_part then
		return
	end

    self.zhushen_select_equip_data = item.data
	self.zhushen_select_equip_part = item.data.index

    self:FlushEquipZhuShenMiddleView()
end

--铸魂升级
function EquipmentView:OnClickOpenZhuShenUpLevel()
    if self.zhushen_select_equip_data == nil then
        return
    end

    local cur_equip_level = EquipmentWGData.Instance:GetZhuShenEquipPartInfo(self.zhushen_select_equip_part)
    local equip_level_cfg = EquipmentWGData.Instance:GetZhuShenEquipLevelCfg(self.zhushen_select_equip_part, cur_equip_level)
	if equip_level_cfg == nil then
        return
    end

    local has_num = ItemWGData.Instance:GetItemNumInBagById(equip_level_cfg.cost_item_id)
    if has_num < equip_level_cfg.cost_item_num then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = equip_level_cfg.cost_item_id})
        return
    end

    EquipmentWGCtrl.Instance:SendEquipZhuShenOperate(CAST_SOUL_OPERATE_TYPE.LEVEL_UP, self.zhushen_select_equip_part)
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, 
                                pos = Vector2(0, 0), parent_node = self.node_list["zhushen_effect_level_root"]})
end

--技能tips
function EquipmentView:OnClickOpenZhuShenSkillTips()
    local suit_level = EquipmentWGData.Instance:GetZhuShenAllEquipNextLevel()
    local next_skill_seq = EquipmentWGData.Instance:GetZhuShenSkillSeq(suit_level)
    if self.zhushen_skill_item_list[next_skill_seq + 1] then
        self.zhushen_skill_item_list[next_skill_seq + 1]:OnZhuShenClickSkill(true)
    end
end

function EquipmentView:OnClickZhuShenTipsBtn()
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.EquipmentZhuShen.ZhuShenRuleTitle)
	role_tip:SetContent(Language.EquipmentZhuShen.ZhuShenRuleDesc)
end

function EquipmentView:FlushEquipZhuShenView()
    local data_list = EquipmentWGData.Instance:GetEquipZhuShenList()
    local jump_hole = EquipmentWGData.Instance:GetZhuShenEquipJumpHole()
    if data_list then
        self.equipment_zhushen_list:SetDataList(data_list)
        local is_red = false
        if self.zhushen_select_equip_data then
            is_red = EquipmentWGData.Instance:GetEquipZhuShenPartRedmind(self.zhushen_select_equip_data.index)
        end

        if jump_hole ~= nil and jump_hole ~= self.zhushen_select_equip_part and not is_red then
            self.equipment_zhushen_list:JumpToIndex(jump_hole)
        end
    end

    self:FlushSEquipZhuShenCommonView()
	self:FlushEquipZhuShenMiddleView()
end

function EquipmentView:FlushSEquipZhuShenCommonView()
    local cur_equip_level = EquipmentWGData.Instance:GetZhuShenOneEquipLevel()
    local suit_level = EquipmentWGData.Instance:GetZhuShenAllEquipNextLevel()
    if suit_level then
        local show_level = EquipmentWGData.Instance:GetZhuShenSkillSuitLevelCfg(suit_level)
        local skill_txt = show_level > 1 and Language.EquipmentZhuShen.ZhuHunSkillTxt2 or Language.EquipmentZhuShen.ZhuHunSkillTxt1
        self.node_list["zhushen_pro_text"].text.text = string.format(skill_txt, suit_level, cur_equip_level)
        self.node_list["zhushen_slider_skill"].slider.value = cur_equip_level / 10
    end
    
    local skill_cfg = EquipmentWGData.Instance:GetZhuShenSkillCfg()
    if skill_cfg then
        for k,v in pairs(self.zhushen_skill_item_list) do
            v:SetData(skill_cfg[k])
        end
    end
end

function EquipmentView:FlushEquipZhuShenMiddleView()
    if self.zhushen_select_equip_data == nil then
        return
    end

    local is_can_uplevel = false
    local equip_index = self.zhushen_select_equip_part
    local cur_equip_level = EquipmentWGData.Instance:GetZhuShenEquipPartInfo(equip_index)
    local equip_level_cfg = EquipmentWGData.Instance:GetZhuShenEquipLevelCfg(equip_index, cur_equip_level)
    
    if equip_level_cfg == nil then
        return
    end
    
    local max_level = EquipmentWGData.Instance:GetZhuShenEquipMaxLevelCfg(equip_index)
    local cur_attr_list = EquipmentWGData.Instance:GetZhuShenEquipAttrList(equip_index, cur_equip_level)
    local next_attr_list = EquipmentWGData.Instance:GetZhuShenEquipAttrList(equip_index, cur_equip_level + 1)

    self.node_list["zhushen_next_info"]:SetActive(cur_equip_level < max_level)
    self.node_list["btn_zhushen_uplevel"]:SetActive(cur_equip_level < max_level)
    self.node_list["zhushen_consume_item"]:SetActive(cur_equip_level < max_level)
    self.node_list["zhuanshen_is_max"]:SetActive(cur_equip_level >= max_level)

    if cur_attr_list and next_attr_list then
        for k,v in pairs(self.zhushen_cur_attr_list) do
            v:SetData(cur_attr_list[k])
        end

        for k,v in pairs(self.zhushen_next_attr_list) do
            v:SetData(next_attr_list[k])
        end
    end


    if cur_equip_level <= 0 then
        self.node_list["zhushen_cur_level"].text.text = Language.EquipmentZhuShen.NotZhuHunAct
    else
        if cur_equip_level <= max_level then
            self.node_list["zhushen_cur_level"].text.text = string.format(Language.EquipmentZhuShen.ZhuHunLevelDesc, cur_equip_level)
        end
    end

    self.node_list["zhushen_next_level"].text.text = string.format(Language.EquipmentZhuShen.ZhuHunLevelDesc, cur_equip_level + 1)

    self.zhushen_cur_item:SetData(self.zhushen_select_equip_data)
    self.zhushen_cur_item:SetItemTipFrom(ItemTip.FROM_ZHUHUN_EQUIP)

    self.zhushen_next_item:SetData(self.zhushen_select_equip_data)
    self.zhushen_next_item:SetItemTipFrom(ItemTip.FROM_ZHUHUN_EQUIP_NEXT)

    self.zhushen_up_item:SetData({item_id = equip_level_cfg.cost_item_id})
    local has_num = ItemWGData.Instance:GetItemNumInBagById(equip_level_cfg.cost_item_id)
    is_can_uplevel = has_num >= equip_level_cfg.cost_item_num
    local color = (not is_can_uplevel) and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
    local str = has_num .. "/" .. equip_level_cfg.cost_item_num
    self.zhushen_up_item:SetRightBottomColorText(str, color)
    self.zhushen_up_item:SetRightBottomTextVisible(true)

    self.node_list["btn_zhushen_uplevel_remind"]:SetActive(is_can_uplevel)
end



EquipZhuShenItemRender = EquipZhuShenItemRender or BaseClass(BaseRender)
function EquipZhuShenItemRender:__init()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["equipment_zhushen_item_item"])
		self.item_cell:SetItemTipFrom(ItemTip.FROM_ZHUHUN_EQUIP)
	end
end

function EquipZhuShenItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function EquipZhuShenItemRender:OnFlush()
    if IsEmptyTable(self.data) then
		return
	end

    self.item_cell:SetData(self.data)
    
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg then
        local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        self.node_list["equipment_zhushen_item_name"].text.text = item_name
    end

    local is_red = EquipmentWGData.Instance:GetEquipZhuShenPartRedmind(self.data.index)
	self.node_list["equipment_zhushen_item_remind"]:SetActive(is_red)
	--self.node_list["equipment_zhushen_is_up"]:SetActive(is_red)

    local equip_level = EquipmentWGData.Instance:GetZhuShenEquipPartInfo(self.data.index)
    self.node_list["equipment_zhushen_level"].text.text = string.format(Language.EquipmentZhuShen.ZhuShenTaiHoleLevel, equip_level)
end

function EquipZhuShenItemRender:OnSelectChange(is_select)
	self.node_list["equipment_zhushen_item_bg"]:SetActive(not is_select)
	self.node_list["equipment_zhushen_item_bg_hl"]:SetActive(is_select)
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg then
        local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        self.node_list["hl_equipment_zhushen_item_name"].text.text = item_name
    end
end

ZhuShenSkillRender = ZhuShenSkillRender or BaseClass(BaseRender)
function ZhuShenSkillRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnZhuShenClickSkill, self))
end

function ZhuShenSkillRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundel, asset = ResPath.GetSkillIconById(self.data.skill_icon)
    self.node_list["icon"].image:LoadSpriteAsync(bundel, asset, function ()
		self.node_list["icon"].image:SetNativeSize()
	end)

    XUI.SetGraphicGrey(self.node_list["icon"], self.data.skill_level < 1)

    local is_red = EquipmentWGData.Instance:GetEquipZhuShenSkillRedmind(self.data.skill_id)
    self.node_list["remind"]:CustomSetActive(is_red)

    --local max_level = EquipmentWGData.Instance:GetZhuShenSkillMaxLevelCfg(self.data.skill_id)
    self.node_list["level_part"]:SetActive(self.data.skill_level > 0)
    self.node_list["skill_level"].text.text = string.format(Language.EquipmentZhuShen.ZhuShenTaiHoleLevel, self.data.skill_level)
end

function ZhuShenSkillRender:OnZhuShenClickSkill(is_tips)
    if IsEmptyTable(self.data) then
        return
    end

    local show_data = {
        skill_seq = self.data.skill_id,
        icon = self.data.skill_icon,
        skill_name = self.data.skill_name,
        is_tips = is_tips,
    }

    EquipmentWGCtrl.Instance:OpenZhuShenSkillTips(show_data)
end