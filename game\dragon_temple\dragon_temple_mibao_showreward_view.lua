DragonTempleMiBaoShowRewardView = DragonTempleMiBaoShowRewardView or BaseClass(SafeBaseView)
function DragonTempleMiBaoShowRewardView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(956, 560)})
	self:AddViewResource(0, "uis/view/dragon_temple_ui_prefab", "layout_dragon_temple_mibao_showreward")
end

function DragonTempleMiBaoShowRewardView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.DragonTemple.MiBaoShowRewardTitle

	if not self.mibao_reward_list then
		local bundle = "uis/view/dragon_temple_ui_prefab"
		local asset = "mibao_reward_cell"
		self.mibao_reward_list = AsyncBaseGrid.New()
		self.mibao_reward_list:CreateCells({col = 6, change_cells_num = 1, list_view = self.node_list.mibao_reward_list,
			assetBundle = bundle, assetName = asset, itemRender = MiBaoRewardCellRender})
		self.mibao_reward_list:SetStartZeroIndex(false)
	end
end

function DragonTempleMiBaoShowRewardView:ReleaseCallBack()
	if self.mibao_reward_list then
		self.mibao_reward_list:DeleteMe()
		self.mibao_reward_list = nil
	end
end

function DragonTempleMiBaoShowRewardView:OnFlush()
	local data_list = DragonTempleWGData.Instance:GetMiBaoShowReward()
	
	if not IsEmptyTable(data_list) then
		self.mibao_reward_list:SetDataList(data_list)
	end
end

MiBaoRewardCellRender = MiBaoRewardCellRender or BaseClass(BaseRender)
function MiBaoRewardCellRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_pos)
end

function MiBaoRewardCellRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function MiBaoRewardCellRender:OnFlush()
	if not self.data then
		return
	end
	self.item_cell:SetData({item_id = self.data.item_id})
	local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list.name.text.text = ToColorStr(item_name, color) 
end