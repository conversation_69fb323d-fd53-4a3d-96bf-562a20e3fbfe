﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class MoveableObjectWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(MoveableObject), typeof(UnityEngine.MonoBehaviour));
		L.RegFunction("Reset", Reset);
		<PERSON><PERSON>RegFunction("FixToGround", FixToGround);
		<PERSON><PERSON>unction("SetRotateCallback", SetRotateCallback);
		<PERSON><PERSON>unction("SetMoveCallback", SetMoveCallback);
		<PERSON><PERSON>RegFunction("SetOffset", SetOffset);
		<PERSON>.RegFunction("GetOffsetY", GetOffsetY);
		<PERSON><PERSON>RegFunction("GetOffset", GetOffset);
		<PERSON><PERSON>Function("SetFlyOffset", SetFlyOffset);
		L<PERSON>RegFunction("SetPosition", SetPosition);
		<PERSON><PERSON>RegFunction("SetRotation", SetRotation);
		<PERSON><PERSON>unction("SetRotationOffset", SetRotationOffset);
		<PERSON><PERSON>unction("SetRotationX", SetRotationX);
		<PERSON><PERSON>unction("RotateTo", RotateTo);
		<PERSON><PERSON>unction("StopRotate", StopRotate);
		L.RegFunction("MoveTo", MoveTo);
		L.RegFunction("StopMove", StopMove);
		L.RegFunction("Height", Height);
		L.RegFunction("SetEnterWaterCallBack", SetEnterWaterCallBack);
		L.RegFunction("SetStateChangeCallBack", SetStateChangeCallBack);
		L.RegFunction("Jump", Jump);
		L.RegFunction("Mitsurugi", Mitsurugi);
		L.RegFunction("StopMitsurugi", StopMitsurugi);
		L.RegFunction("SimpleJump", SimpleJump);
		L.RegFunction("ForceLanding", ForceLanding);
		L.RegFunction("StopQinggong", StopQinggong);
		L.RegFunction("AdjustMoveMent", AdjustMoveMent);
		L.RegFunction("SetQingGongTarget", SetQingGongTarget);
		L.RegFunction("SetGravityMultiplier", SetGravityMultiplier);
		L.RegFunction("SetGridFindWay", SetGridFindWay);
		L.RegFunction("SetLogicMap", SetLogicMap);
		L.RegFunction("SetDrag", SetDrag);
		L.RegFunction("JumpFormAir", JumpFormAir);
		L.RegFunction("GetIsRotating", GetIsRotating);
		L.RegFunction("SetIsKeepGroundH", SetIsKeepGroundH);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("isWalkFree", get_isWalkFree, set_isWalkFree);
		L.RegVar("enableQingGong", get_enableQingGong, set_enableQingGong);
		L.RegVar("checkBuilding", get_checkBuilding, set_checkBuilding);
		L.RegVar("enableMitsurugi", get_enableMitsurugi, set_enableMitsurugi);
		L.RegVar("heightDifference", get_heightDifference, set_heightDifference);
		L.RegVar("mitsurugi_hight", get_mitsurugi_hight, set_mitsurugi_hight);
		L.RegVar("IsOnGround", get_IsOnGround, null);
		L.RegVar("JumpHorizonSpeed", get_JumpHorizonSpeed, set_JumpHorizonSpeed);
		L.RegVar("SetFlyHeight", get_SetFlyHeight, set_SetFlyHeight);
		L.RegVar("MinFlyHeight", get_MinFlyHeight, set_MinFlyHeight);
		L.RegVar("MaxFlyHeight", get_MaxFlyHeight, set_MaxFlyHeight);
		L.RegVar("IsFly", get_IsFly, set_IsFly);
		L.RegVar("CheckWater", get_CheckWater, set_CheckWater);
		L.RegVar("WaterHeight", get_WaterHeight, set_WaterHeight);
		L.RegVar("IsInWater", get_IsInWater, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Reset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			obj.Reset();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FixToGround(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector3 o = obj.FixToGround(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRotateCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			System.Action<int> arg0 = (System.Action<int>)ToLua.CheckDelegate<System.Action<int>>(L, 2);
			obj.SetRotateCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMoveCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			System.Action<int> arg0 = (System.Action<int>)ToLua.CheckDelegate<System.Action<int>>(L, 2);
			obj.SetMoveCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetOffset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetOffset(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetOffsetY(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			float o = obj.GetOffsetY();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetOffset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			UnityEngine.Vector3 o = obj.GetOffset();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetFlyOffset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetFlyOffset(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPosition(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
				obj.SetPosition(arg0);
				return 0;
			}
			else if (count == 4)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.SetPosition(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: MoveableObject.SetPosition");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRotation(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				UnityEngine.Quaternion arg0 = ToLua.ToQuaternion(L, 2);
				obj.SetRotation(arg0);
				return 0;
			}
			else if (count == 4)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.SetRotation(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: MoveableObject.SetRotation");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRotationOffset(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
				obj.SetRotationOffset(arg0);
				return 0;
			}
			else if (count == 4)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.SetRotationOffset(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: MoveableObject.SetRotationOffset");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRotationX(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetRotationX(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RotateTo(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.RotateTo(arg0, arg1);
				return 0;
			}
			else if (count == 5)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				obj.RotateTo(arg0, arg1, arg2, arg3);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: MoveableObject.RotateTo");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopRotate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			obj.StopRotate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MoveTo(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.MoveTo(arg0, arg1);
				return 0;
			}
			else if (count == 5)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				obj.MoveTo(arg0, arg1, arg2, arg3);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: MoveableObject.MoveTo");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopMove(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			obj.StopMove();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Height(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float o = obj.Height(arg0);
				LuaDLL.lua_pushnumber(L, o);
				return 1;
			}
			else if (count == 3)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				float o = obj.Height(arg0, arg1);
				LuaDLL.lua_pushnumber(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: MoveableObject.Height");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEnterWaterCallBack(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			System.Action<bool> arg0 = (System.Action<bool>)ToLua.CheckDelegate<System.Action<bool>>(L, 2);
			obj.SetEnterWaterCallBack(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetStateChangeCallBack(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			System.Action<QingGongState> arg0 = (System.Action<QingGongState>)ToLua.CheckDelegate<System.Action<QingGongState>>(L, 2);
			obj.SetStateChangeCallBack(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Jump(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			ActorQingGongObject arg0 = (ActorQingGongObject)ToLua.CheckObject<ActorQingGongObject>(L, 2);
			obj.Jump(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Mitsurugi(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			ActorQingGongObject arg0 = (ActorQingGongObject)ToLua.CheckObject<ActorQingGongObject>(L, 2);
			obj.Mitsurugi(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopMitsurugi(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			obj.StopMitsurugi();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SimpleJump(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				ActorQingGongObject arg0 = (ActorQingGongObject)ToLua.CheckObject<ActorQingGongObject>(L, 2);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				obj.SimpleJump(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				ActorQingGongObject arg0 = (ActorQingGongObject)ToLua.CheckObject<ActorQingGongObject>(L, 2);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				obj.SimpleJump(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: MoveableObject.SimpleJump");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceLanding(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			obj.ForceLanding();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopQinggong(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			obj.StopQinggong();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AdjustMoveMent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.AdjustMoveMent(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetQingGongTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.SetQingGongTarget(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetGravityMultiplier(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetGravityMultiplier(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetGridFindWay(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			GridFindWay arg0 = (GridFindWay)ToLua.CheckObject(L, 1, typeof(GridFindWay));
			MoveableObject.SetGridFindWay(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLogicMap(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
			MoveableObject.SetLogicMap(arg0, arg1, arg2, arg3);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int JumpFormAir(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				ActorQingGongObject arg2 = (ActorQingGongObject)ToLua.CheckObject<ActorQingGongObject>(L, 4);
				obj.JumpFormAir(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5)
			{
				MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				ActorQingGongObject arg2 = (ActorQingGongObject)ToLua.CheckObject<ActorQingGongObject>(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				obj.JumpFormAir(arg0, arg1, arg2, arg3);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: MoveableObject.JumpFormAir");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetIsRotating(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			bool o = obj.GetIsRotating();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsKeepGroundH(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MoveableObject obj = (MoveableObject)ToLua.CheckObject(L, 1, typeof(MoveableObject));
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsKeepGroundH(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isWalkFree(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool ret = obj.isWalkFree;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isWalkFree on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableQingGong(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool ret = obj.enableQingGong;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableQingGong on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_checkBuilding(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool ret = obj.checkBuilding;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index checkBuilding on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableMitsurugi(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool ret = obj.enableMitsurugi;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableMitsurugi on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_heightDifference(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, MoveableObject.heightDifference);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mitsurugi_hight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float ret = obj.mitsurugi_hight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mitsurugi_hight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsOnGround(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool ret = obj.IsOnGround;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsOnGround on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_JumpHorizonSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float ret = obj.JumpHorizonSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index JumpHorizonSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SetFlyHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float ret = obj.SetFlyHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SetFlyHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MinFlyHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float ret = obj.MinFlyHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinFlyHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MaxFlyHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float ret = obj.MaxFlyHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxFlyHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsFly(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool ret = obj.IsFly;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsFly on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CheckWater(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool ret = obj.CheckWater;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CheckWater on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WaterHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float ret = obj.WaterHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index WaterHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsInWater(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool ret = obj.IsInWater;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsInWater on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isWalkFree(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isWalkFree = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isWalkFree on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableQingGong(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableQingGong = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableQingGong on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_checkBuilding(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.checkBuilding = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index checkBuilding on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableMitsurugi(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableMitsurugi = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableMitsurugi on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_heightDifference(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			MoveableObject.heightDifference = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_mitsurugi_hight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.mitsurugi_hight = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mitsurugi_hight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_JumpHorizonSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.JumpHorizonSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index JumpHorizonSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SetFlyHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetFlyHeight = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SetFlyHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MinFlyHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MinFlyHeight = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinFlyHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MaxFlyHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MaxFlyHeight = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxFlyHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsFly(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsFly = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsFly on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CheckWater(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.CheckWater = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CheckWater on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_WaterHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			MoveableObject obj = (MoveableObject)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.WaterHeight = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index WaterHeight on a nil value");
		}
	}
}

