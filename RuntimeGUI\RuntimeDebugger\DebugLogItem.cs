﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DebugLogItem
{
	private readonly DateTime m_LogTime;
	private readonly LogType m_LogType;
	private readonly string m_LogMessage;
	private readonly string m_StackTrack;
	private readonly string m_FirstRowMessage;
	
	public DebugLogItem(LogType logType, string logMessage, string stackTrack)
	{
		m_LogTime = DateTime.Now;
		m_LogType = logType;
		m_LogMessage = logMessage;
		m_StackTrack = stackTrack;
		m_FirstRowMessage = logMessage.Split('\n')[0];
	}

	public DateTime LogTime
	{
		get
		{
			return m_LogTime;
		}
	}

	public LogType LogType
	{
		get
		{
			return m_LogType;
		}
	}

	public string LogMessage
	{
		get
		{
			return m_LogMessage;
		}
	}

	public string StackTrack
	{
		get
		{
			return m_StackTrack;
		}
	}

	public string FirstRowMessage
	{
		get
		{
			return m_FirstRowMessage;
		}
	}
}
