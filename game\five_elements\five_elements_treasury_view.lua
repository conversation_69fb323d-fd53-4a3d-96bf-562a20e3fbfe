FiveElementsTreasuryView = FiveElementsTreasuryView or BaseClass(SafeBaseView)

local DRAW_POOL_COUNT = 4 --奖池最大索引

function FiveElementsTreasuryView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")
    self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "five_elements_treasury")
    self.is_safe_area_adapter = true
    self.view_style = ViewStyle.Full
end

function FiveElementsTreasuryView:ReleaseCallBack()
    if self.item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
        self.item_data_change = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end
end

function FiveElementsTreasuryView:LoadCallBack()
    --self.cur_pool_seq = 1
    local bundle, asset = ResPath.GetRawImagesJPG("a2_wx_bg10")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
    self.node_list["title_view_name"].text.text = Language.FiveElements.ChouJiangViewName
    for i = 1, 2 do
        self.node_list["btn_draw_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickRecord, self, i))
        self.node_list["btn_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowDrawItemTips, self, i))
    end

    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true, show_bind_gold = true,
            show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    XUI.AddClickEventListener(self.node_list["btn_record"], BindTool.Bind1(self.OpenRecordView, self)) --抽奖日志
    XUI.AddClickEventListener(self.node_list["btn_reward_show"], BindTool.Bind1(self.OnClickRewardShow, self)) --奖励展示
    --XUI.AddClickEventListener(self.node_list["btn_pool_jump"], BindTool.Bind1(self.RewardJump, self)) --点击跳转奖池
    XUI.AddClickEventListener(self.node_list["btn_use_jump"], BindTool.Bind1(self.OnClickAutoJumpItem, self)) --自动使用奖池跳转道具
    XUI.AddClickEventListener(self.node_list["btn_icon_3"], BindTool.Bind1(self.ShowFlishItemTips, self))
    XUI.AddClickEventListener(self.node_list["skip_btn"], BindTool.Bind(self.AniOnClickJump, self)) --跳过动画
    XUI.AddClickEventListener(self.node_list["click_mask"], BindTool.Bind(self.OnClickMask, self)) --遮罩点击
    XUI.AddClickEventListener(self.node_list["gailv_btn"], BindTool.Bind(self.OnClickGaiLv, self)) --遮罩点击

    self.item_data_change = BindTool.Bind(self.OnItemChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)


    self.draw_pool_list = {}
    local draw_pool_info = FiveElementsWGData.Instance:GetDrawPoolInfo()
    local main_pool_index = FiveElementsWGData.Instance:GetMainPoolInfo()
    --local pool_info_cfg = FiveElementsWGData.Instance:GetPoolCfg()
    -- if draw_pool_info ~= nil or main_pool_index ~= nil then
    --     self.cur_pool_seq = draw_pool_info.reward_pool_seq or main_pool_index
    -- end

    for i = 0, DRAW_POOL_COUNT do
        self.draw_pool_list[i] = {}
        self.draw_pool_list[i].reward_pool = self.node_list["reward_pool_" .. i]
        self.draw_pool_list[i].effect_root = self.node_list["effect_root_" .. i]
    end


    self.ani_is_playing = false
    -- if FiveElementsWGData.Instance then
    --     FiveElementsWGData.Instance:SetAniIsplaying(self.ani_is_playing)
    -- end
end

function FiveElementsTreasuryView:OpenRecordView() --打开抽奖日志
    -- local is_playing = FiveElementsWGData.Instance:GetAniIsplaying()
    -- if is_playing then
    --     TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElementsTreasury.IsWorkIng)
    --     return
    -- end

    FiveElementsWGCtrl.Instance:OpenTreasuryRecordView()
end

function FiveElementsTreasuryView:OnClickRewardShow() --打开奖励展示
    -- local is_playing = FiveElementsWGData.Instance:GetAniIsplaying()
    -- if is_playing then
    --     TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElementsTreasury.IsWorkIng)
    --     return
    -- end

    FiveElementsWGCtrl.Instance:OpenTreasuryLibraryView()
end

-- function FiveElementsTreasuryView:RewardJump() --奖池跳转
--     local is_playing = FiveElementsWGData.Instance:GetAniIsplaying()
--     if is_playing then
--         TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElementsTreasury.IsWorkIng)
--         return
--     end

--     local jump_cfg = FiveElementsWGData.Instance:GetPoolJumpCfg()
--     local jump_count = 0
--     if jump_cfg.item_id then
--         jump_count = ItemWGData.Instance:GetItemNumInBagById(jump_cfg.item_id)
--     end

--     if jump_count < 1 then
--         TipWGCtrl.Instance:OpenItemTipGetWay({item_id = jump_cfg.item_id})
-- 		return
--     end

--     local cur_seq = FiveElementsWGData.Instance:GetCurPoolSeq()
--     if cur_seq >= jump_cfg.seq then
--         SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElementsTreasury.DrawPoolFlushError)
-- 		return
--     end

--     self:ShowAniHightLight(jump_cfg.seq)
--     FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_DRAW_JUMP, jump_cfg.item_id)
-- end

function FiveElementsTreasuryView:OnFlush(param_t)
    for k, v in pairs(param_t) do
        if k == "all" then
            self:FlushViewShow()
        elseif k == "play_ani" then --播放  动画
            self:PlayAnimation()
        end
    end
end

function FiveElementsTreasuryView:FlushViewShow()
    self:FlushDrawBtnShow() --抽奖按钮显示
    self:FlushAniStatus()
end

--抽奖请求
function FiveElementsTreasuryView:OnClickSend(param, draw_type)
    -- local is_playing = FiveElementsWGData.Instance:GetAniIsplaying()
    -- if is_playing then
    --     TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElementsTreasury.IsWorkIng)
    --     return
    -- end

    self.ani_is_playing = true
    FiveElementsWGData.Instance:CacheOrGetDrawIndex(draw_type)
    --FiveElementsWGData.Instance:SetAniIsplaying(self.ani_is_playing)
    local jump_cfg = FiveElementsWGData.Instance:GetPoolJumpCfg()
    local is_auto_item = FiveElementsWGData.Instance:GetAutoUseItem()
    local param_id = is_auto_item and jump_cfg.item_id or 0
    --发送协议
    FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_DRAW,
        param.mode, param_id)
end

function FiveElementsTreasuryView:PlayAnimation()
    -- local seq_list = FiveElementsWGData.Instance:GetResultSeq()
    -- if seq_list == nil then
        FiveElementsWGCtrl.Instance:OpenTreasuryRewardView()
        -- return
    -- end

    -- local btn_index = FiveElementsWGData.Instance:CacheOrGetDrawIndex()
    -- local ani_time = 4

    -- if self.is_jump_ani or btn_index == 1 then
    --     ani_time = 0
    -- end

    -- self.play_ani = GlobalTimerQuest:AddDelayTimer(function()
    --     self.node_list["click_mask"]:SetActive(false)
    --     GlobalTimerQuest:CancelQuest(self.play_ani)
    --     self.play_ani = nil
    --     self.ani_is_playing = false
    --     FiveElementsWGData.Instance:SetAniIsplaying(self.ani_is_playing)
    --     self:AniCancleHLQuest()
    --     self:ShowAniHightLight(seq_list[#seq_list])
    --     FiveElementsWGCtrl.Instance:OpenTreasuryRewardView()
    -- end, ani_time)

    -- if self.is_jump_ani or btn_index == 1 then
    --     return
    -- end

    -- self.node_list["click_mask"]:SetActive(true)

    -- local seq = 1
    -- self.show_hl_quest = GlobalTimerQuest:AddRunQuest(function()
    --     if seq <= #seq_list then
    --         self:ShowAniHightLight(seq_list[seq])
    --         seq = seq + 1
    --     end
    -- end, 0.4)
end

--跳过抽奖动画按钮
function FiveElementsTreasuryView:AniOnClickJump()
    FiveElementsWGData.Instance:SetJumpAni()
    self:FlushAniStatus()
end

--跳过动画按钮状态
function FiveElementsTreasuryView:FlushAniStatus()
    local is_jump_ani = FiveElementsWGData.Instance:GetJumpAni()
    self.node_list["jump_toggle"]:SetActive(is_jump_ani)
end

--自动使用道具按钮
function FiveElementsTreasuryView:OnClickAutoJumpItem()
    local jump_cfg = FiveElementsWGData.Instance:GetPoolJumpCfg()
    local jump_count = 0
    if jump_cfg.item_id then
        jump_count = ItemWGData.Instance:GetItemNumInBagById(jump_cfg.item_id)
    end

    local cur_state = FiveElementsWGData.Instance:GetAutoUseItem()
    if cur_state then
        FiveElementsWGData.Instance:SetAutoUseItem()
        self:FlushJumpItemStatus()
        return
    end

    if jump_count < 1 then
        --TipWGCtrl.Instance:OpenItemTipGetWay({item_id = jump_cfg.item_id})
        TipWGCtrl.Instance:OpenItem({ item_id = jump_cfg.item_id })
        return
    end

    FiveElementsWGData.Instance:SetAutoUseItem()
    self:FlushJumpItemStatus()
end

--自动使用道具按钮状态
function FiveElementsTreasuryView:FlushJumpItemStatus()
    self.item_jump = FiveElementsWGData.Instance:GetAutoUseItem()
    self.node_list["item_toggle"]:SetActive(self.item_jump)
end

function FiveElementsTreasuryView:AniCancleHLQuest()
    if self.show_hl_quest then
        GlobalTimerQuest:CancelQuest(self.show_hl_quest)
        self.show_hl_quest = nil
    end
end

-- 十连抽卡牌高亮
function FiveElementsTreasuryView:ShowAniHightLight(index)
    -- if index >= 0 and index <= DRAW_POOL_COUNT then
    --     for i = 0, DRAW_POOL_COUNT do
    --         if self.draw_pool_list[i] then
    --             --self.draw_pool_list[i].hl_seq:SetActive(i == index)
    --             if i == index then
    --                 self:PlayHightAni(self.draw_pool_list[i])
    --             end
    --         end
    --     end
    -- end
end

-- function FiveElementsTreasuryView:PlayHightAni(node_list)
--     node_list.reward_pool.transform:DOScale(0.8, 0.1)
--     node_list.reward_pool.transform:DOScale(1, 0.1)

--     local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_baoshi_xiangqian_01)
--     EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, node_list.effect_root.transform)
-- end

--抽奖
function FiveElementsTreasuryView:OnClickRecord(draw_type) --抽奖
    local cfg = FiveElementsWGData.Instance:GetTreasuryDrawConsumeCfg()
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)
    --检查道具数量
    if num >= cfg.cost_item_num then
        --发送协议
        self:OnClickSend(cfg, draw_type)
    else
        FiveElementsWGCtrl.Instance:ClickUseDrawItem(draw_type, function()
            self:OnClickDrawBuy(draw_type)
        end)
    end
end

--抽奖道具显示
function FiveElementsTreasuryView:FlushDrawBtnShow(is_flush_num) --刷新抽奖次数
    local cfg = FiveElementsWGData.Instance:GetTreasuryDrawConsumeCfg()
    if cfg == nil then
        return
    end

    local item_cfg
    local count = 0
    for i = 1, 2 do
        if cfg[i] then
            local item_id = cfg[i].cost_item_id
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not IsEmptyTable(item_cfg) then
                    --道具图标
                    self.node_list["btn_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
                end

                self.node_list["txt_buy_" .. i].text.text = string.format(Language.FiveElementsTreasury.BtnDrawDesc,
                    cfg[i].times)
                --折扣
                local is_zhekou = cfg[i].times ~= cfg[i].cost_item_num
                self.node_list["btn_discount_" .. i]:SetActive(is_zhekou)
                if is_zhekou then
                    self.node_list["txt_discount_" .. i].text.text = cfg[i].cost_item_num .. "折"
                end
            end
            --道具红点数量
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["btn_red_" .. i]:SetActive(count >= cfg[i].cost_item_num)
            local color = count >= cfg[i].cost_item_num and COLOR3B.BLUE_TITLE or COLOR3B.PINK
            local left_str = ToColorStr(count, color)
            self.node_list["btn_num_" .. i].text.text = left_str .. "/" .. cfg[i].cost_item_num
            self.node_list["btn_num_" .. i].text.color = Str2C3b(color)
        end
    end

    local jump_cfg = FiveElementsWGData.Instance:GetPoolJumpCfg()
    local jump_item_cfg
    local jump_count = 0
    if not is_flush_num then
        item_cfg = ItemWGData.Instance:GetItemConfig(jump_cfg.item_id)
        if not IsEmptyTable(item_cfg) then
            --道具图标
            self.node_list["btn_icon_3"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
        end
    end

    jump_count = ItemWGData.Instance:GetItemNumInBagById(jump_cfg.item_id)
    self.node_list["btn_red_3"]:SetActive(jump_count >= 1)
    local color = jump_count >= 1 and COLOR3B.BLUE_TITLE or COLOR3B.PINK
    local left_str = ToColorStr(jump_count, color)
    self.node_list["btn_num_3"].text.text = left_str .. "/" .. 1
    self.node_list["btn_num_3"].text.color = Str2C3b(color)
    if jump_count < 1 then
        local cur_state = FiveElementsWGData.Instance:GetAutoUseItem()
        if cur_state then
            FiveElementsWGData.Instance:SetAutoUseItem()
            self:FlushJumpItemStatus()
        end
    end
end

function FiveElementsTreasuryView:OnItemChange(item_id)
    local check_list = FiveElementsWGData.Instance:GetTreasuryItemDataChangeList()
    if check_list == nil then
        return
    end

    local jump_item = FiveElementsWGData.Instance:GetPoolJumpCfg()
    for i, v in pairs(check_list) do
        if v == item_id or jump_item.item_id == item_id then
            self:FlushDrawBtnShow(true)
            return
        end
    end
end

--抽奖物品tips弹窗
function FiveElementsTreasuryView:ShowDrawItemTips(item_type)
    local cfg = FiveElementsWGData.Instance:GetTreasuryDrawConsumeCfg()
    local item_id = cfg[item_type].cost_item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = item_id })
end

--奖池刷新道具tips
function FiveElementsTreasuryView:ShowFlishItemTips()
    local jump_cfg = FiveElementsWGData.Instance:GetPoolJumpCfg()
    TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = jump_cfg.item_id })
end

--点击购买
function FiveElementsTreasuryView:OnClickDrawBuy(draw_type)
    local cfg = FiveElementsWGData.Instance:GetTreasuryDrawConsumeCfg()
    local cur_cfg = cfg[draw_type]
    if cur_cfg == nil then
        return
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.cost_item_id)
    local consume = (cur_cfg.cost_gold / cur_cfg.cost_item_num) * (cur_cfg.cost_item_num - num)
    --检查仙玉
    local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
    --足够购买，不足弹窗
    if enough then
        --发送协议
        self:OnClickSend(cur_cfg, draw_type)
    else
        VipWGCtrl.Instance:OpenTipNoGold()
    end
end

function FiveElementsTreasuryView:OnClickMask()
    -- local is_playing = FiveElementsWGData.Instance:GetAniIsplaying()
    -- if is_playing then
    --     TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElementsTreasury.IsWorkIng)
    --     return
    -- end
end

function FiveElementsTreasuryView:OnClickGaiLv()
	local info = FiveElementsWGData.Instance:GetRandomGaiLvinfo()
	TipWGCtrl.Instance:OpenGaiLvShowView(info)
end