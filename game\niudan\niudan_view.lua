
NiuDanView = NiuDanView or BaseClass(SafeBaseView)

function NiuDanView:__init()
    self:SetMaskBg(true, true)
    self.view_name = GuideModuleName.NiuDanView
    self:AddViewResource(0, "uis/view/niudan_ui_prefab", "layout_niudan")
end

function NiuDanView:ReleaseCallBack()
    if self.fw_big_cell_list then
        self.fw_big_cell_list:DeleteMe()
        self.fw_big_cell_list = nil
    end

    if self.fw_zhenxi_cell_list then
        self.fw_zhenxi_cell_list:DeleteMe()
        self.fw_zhenxi_cell_list = nil
    end

    if self.fw_fanli_list then
        self.fw_fanli_list:DeleteMe()
        self.fw_fanli_list = nil
    end

    if self.niudan_count_down and CountDownManager.Instance:HasCountDown(self.niudan_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.niudan_count_down)
	end

    if self.fw_egg_list then
        for k, v in pairs(self.fw_egg_list) do
            v:DeleteMe()
        end
        self.fw_egg_list = nil
    end

    if self.item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
        self.item_data_change = nil
    end

    self.is_play_niudan_ani = false
end

function NiuDanView:LoadCallBack()
	self.node_list["btn_fw_record"].button:AddClickListener(BindTool.Bind1(self.OnClickFwsRecord, self))
	for i = 1, 3 do
		self.node_list["btn_fw_buy_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickFwsDraw, self, i))
        self.node_list["fw_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowNiuDanItemTips, self, i))
	end
    self:CreateFwsShowCell()
    self.node_list["niudan_btn_ignore_ani"].button:AddClickListener(BindTool.Bind(self.OnClickNiuDanIgnore, self))
	XUI.AddClickEventListener(self.node_list.probability_show_btn, BindTool.Bind(self.OpenGaiLvView, self))

    --self:LoadFireworksImages()
    self:LoginTimeCountDown() --活动倒计时

    if self.item_data_change == nil then
        self.item_data_change = BindTool.Bind(self.OnFwsItemChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
    end
end

-- function NiuDanView:LoadFireworksImages()
--     -- local leichong_rawimage_bundle, leichong_rawimage_asset = ResPath.GetFestivalRawImages("xyndgg")
--     -- self.node_list["title_img"].raw_image:LoadSprite(leichong_rawimage_bundle, leichong_rawimage_asset, function ()
--     --     self.node_list["title_img"].raw_image:SetNativeSize()
--     -- end)

--     local cfg = NiuDanWGData.Instance:GetGradeCfg()
--     --self.node_list.txt_fw_times.text.color = Str2C3b(cfg.left_text_color)
--     --self.node_list.niudan_top_txt.text.color = Str2C3b(cfg.left_text_color)
--     -- for i = 1, 3 do
--     --     self.node_list["fw_red_num_"..i].text.color = Str2C3b(cfg.down_text_color)
--     -- end
--     self.down_color = cfg.down_text_color
-- end

--跳过
function NiuDanView:OnClickNiuDanIgnore()
    local is_ignore = NiuDanWGData.Instance:GetSkipAnim() == 1
    NiuDanWGCtrl.Instance:SendReq(CSA_YANHUA_OP.ANIM_FLAG, is_ignore and 0 or 1)
end

--展示的格子
function NiuDanView:CreateFwsShowCell()
    self.fw_big_cell_list = AsyncListView.New(MergeFireworksShowRender,self.node_list["fw_big_cell_list"] )
    self.fw_zhenxi_cell_list = AsyncListView.New(MergeFireworksShowRender,self.node_list["fw_zhenxi_cell_list"])
    self.fw_fanli_list = AsyncListView.New(FestivalFireworksFanliRender, self.node_list["fw_leiji_list"])

    self.fw_egg_list = {}
    for i = 1, 8 do
        self.fw_egg_list[i] = FestivalFireworksEggRender.New(self.node_list["egg_" .. i])
    end
end


--更新日志和协议信息
function NiuDanView:OpenCallBack()
    --self:SetFireworksRuleTip()
    NiuDanWGCtrl.Instance:SendReq(CSA_YANHUA_OP.INFO)
    NiuDanWGCtrl.Instance:SendReq(CSA_YANHUA_OP.RECORD)
end

--参数刷新
function NiuDanView:OnFlush(param)
    for k, v in pairs(param) do
        if "all" == k then
            self:FlushFwsViewShow()
            if "record" == v[2] then
                self:FlushFwsRecordRed()
            elseif "play_ani" == v[2] then
                self:PlayNiuDanjiAni()
            elseif "reset_ani" == v[2] then
                self.is_play_niudan_ani = false
                self:SetNiuDanjiEnd()
            end
        end
    end
end

function NiuDanView:OnFwsItemChange(change_item_id)
    local check_list = NiuDanWGData.Instance:GetItemDataChangeList()
    for i, v in pairs(check_list) do
        if v == change_item_id then
            self:FlushFwsBtnShow(true)
            return
        end
    end
end

function NiuDanView:PlayNiuDanjiAni()
    local ignore_anim = NiuDanWGData.Instance:GetSkipAnim() == 1
    local btn_index = NiuDanWGData.Instance:CacheOrGetDrawIndex()
    if ignore_anim then
        return
    end

    self.is_play_niudan_ani = true
    self:SetNiuDanjiSpine(btn_index)
    self.play_niudan_ani = GlobalTimerQuest:AddDelayTimer(function()
        self.play_niudan_ani = nil
        self.is_play_niudan_ani = false
    end, 3) --滚动
end


-- 设置动画
function NiuDanView:SetNiuDanjiSpine(btn_index)
    if self.fw_egg_list == nil then
        return
    end
    
    if btn_index == 1 then
        local range_index = math.random(1, #self.fw_egg_list)
        self.fw_egg_list[range_index]:EggStartAni()
    elseif btn_index == 2 or btn_index == 3 then
        for i = 1, 8 do
            self.fw_egg_list[i]:EggStartAni()
        end
    end
end
-- 结束动画
function NiuDanView:SetNiuDanjiEnd()
    if self.fw_egg_list then
        for i = 1, 8 do
            self.fw_egg_list[i]:EggEndAni()
        end
    end
end

--刷新界面
function NiuDanView:FlushFwsViewShow()
    self:FlushIgnoreState()
    self:FlushFwsBtnShow()
    self:FlushFwsShowCell()
    --self:FlushFwsGuarantee()
    --扭蛋次数
    --local draw_times = NiuDanWGData.Instance:GetCurDrawTimes()
    --self.node_list["txt_fw_times"].text.text = string.format(Language.MergeFireworks.TxtTimes, draw_times)
end

function NiuDanView:FlushIgnoreState()
    local is_ignore = NiuDanWGData.Instance:GetSkipAnim() == 1
    self.node_list["niudan_ignore_img"]:SetActive(is_ignore)
end

--3个按钮刷新，传参数只刷新数量
function NiuDanView:FlushFwsBtnShow(is_flush_num)
    local cfg = NiuDanWGData.Instance:GetConsumeCfg()
    local item_cfg
    local count--, asset
    for i = 1, 3 do
        if cfg[i] then
            local item_id = cfg[i].yanhua_item.item_id
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not IsEmptyTable(item_cfg) then               
                    --道具图标
                    self.node_list["fw_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
                     --特效
                    --asset = BaseCell_Ui_Circle_Effect[item_cfg.zhengui_effect]
                end
                --按钮价格
                self.node_list["txt_fw_buy_" .. i].text.text = string.format(Language.MergeFireworks.TxtBuy, cfg[i].onekey_lotto_num)
                -- --折扣
                self.node_list["fw_discount_" .. i]:SetActive(cfg[i].discount_text ~= "")
                if cfg[i].discount_text ~= "" then
                    self.node_list["txt_fw_discount_" .. i].text.text = cfg[i].discount_text .. "折"
                end
            end
            --道具红点数量
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["fw_red_" .. i]:SetActive(true)
            self.node_list["niudan_red_" .. i]:SetActive(count >= cfg[i].yanhua_item.num)
            --self.down_color = self.down_color or COLOR3B.DEFAULT
            local color = count >= cfg[i].yanhua_item.num and COLOR3B.D_GREEN or COLOR3B.D_RED
            local left_str = ToColorStr(count, color) 
            self.node_list["fw_red_num_" .. i].text.text = left_str .."/".. cfg[i].yanhua_item.num
            self.node_list["fw_red_num_"..i].text.color = Str2C3b(color)
        end
    end
end

function NiuDanView:ShowNiuDanItemTips(item_type)
    local cfg = NiuDanWGData.Instance:GetConsumeCfg()
    local item_id = cfg[item_type].yanhua_item.item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id}) 
end

function NiuDanView:FlushFwsRecordRed()
	--大奖记录红点
    local read_time = RoleWGData.GetRolePlayerPrefsInt("Festival_Fireworks_record")
    local new_record_time = NiuDanWGData.Instance:GetRecordTime()
    self.node_list["fw_record_red"]:SetActive(new_record_time > read_time)
end

--展示格子刷新
function NiuDanView:FlushFwsShowCell()
    local show_cell_list1, show_cell_list2, baodi_list = NiuDanWGData.Instance:GetShowCellList()
    local zhenxi_list = NiuDanWGData.Instance:GetZhenXiRewardList()
    local fanli_list = NiuDanWGData.Instance:GetFanliList()
    self.fw_big_cell_list:SetDataList(show_cell_list1)
    self.fw_zhenxi_cell_list:SetDataList(zhenxi_list)
    self.fw_fanli_list:SetDataList(fanli_list)
    -- self.fw_mid_cell_list:SetDataList(show_cell_list2)
    -- self.fw_small_cell_list:SetDataList(baodi_list)

    local baodi_times = NiuDanWGData.Instance:GetBaoDiDrawTimes()
    local is_show_baodi = NiuDanWGData.Instance:GetIsShowBaodiTimes()
    local is_baodi = baodi_times == -1
    if baodi_times > 0 then
        self.node_list["baodi_txt"].text.text = string.format(Language.MergeFireworks.NiudanDrawTimes, baodi_times)
    end

    self.node_list["baodi_txt"]:SetActive(not is_baodi and is_show_baodi)
end

--活动时间倒计时
function NiuDanView:LoginTimeCountDown()
	self.niudan_count_down = "niudan_count_down"
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.FESTIVAL_ACT_YANHUA_SHENGDIAN_2)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list.niudan_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown(self.niudan_count_down, BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end

function NiuDanView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list.niudan_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function NiuDanView:OnComplete()
	self.node_list.niudan_time_label.text.text = Language.Activity.TianshenRoadLoginTime
end

--保底次数显示
-- function NiuDanView:FlushFwsGuarantee()
--     --local next_times = NiuDanWGData.Instance:GetNextBaodiTimes()
--     --self.node_list["fw_guarantee"]:SetActive(true)
--     --self.node_list["txt_fw_last_times"].text.text = next_times
-- end

--ruletip
function NiuDanView:SetFireworksRuleTip()
    local grade_cfg = NiuDanWGData.Instance:GetGradeCfg()
    if not IsEmptyTable(grade_cfg) then
        local desc = grade_cfg.tip_desc
        local title = grade_cfg.tip_title or Language.MergeFireworks.TipTitle
        local probility_str = NiuDanWGData.Instance:GetItemsProbility()
        desc = desc .. probility_str
        RuleTip.Instance:SetContent(desc, title)
    end
end

--打开记录
function NiuDanView:OnClickFwsRecord()
	NiuDanWGCtrl.Instance:OpenRecord()
end

--点击抽奖
function NiuDanView:OnClickFwsDraw(draw_type)
    if self.is_play_niudan_ani then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeFireworks.PlayAni)
        return
    end
	--check enough
	local cfg = NiuDanWGData.Instance:GetConsumeCfg()
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.yanhua_item.item_id)
	if num >= cfg.yanhua_item.num then
		--足够就关闭界面放特效
			--是否跳过动画
        NiuDanWGData.Instance:CacheOrGetDrawIndex(draw_type)
        NiuDanWGCtrl.Instance:SendReq(CSA_YANHUA_OP.BUY, cfg.onekey_lotto_num, 1)
	else
		--不足够买
        NiuDanWGCtrl.Instance:ClickUse(draw_type, function()
            self:OnClickFwsBuy(draw_type)
        end)
	end
end

--点击购买
function NiuDanView:OnClickFwsBuy(draw_type)
    local cfg = NiuDanWGData.Instance:GetConsumeCfg()
    local cur_cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.yanhua_item.item_id)
    local consume = cfg[1].consume_count * (cur_cfg.yanhua_item.num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
    if enough then
        NiuDanWGData.Instance:CacheOrGetDrawIndex(draw_type)
		NiuDanWGCtrl.Instance:SendReq(CSA_YANHUA_OP.BUY, cfg[draw_type].onekey_lotto_num)
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

function NiuDanView:OpenGaiLvView()
    local info = NiuDanWGData.Instance:GetGaiLvInfo()
    TipWGCtrl.Instance:OpenGaiLvShowView(info)
end

--------------------------------------------------------------------------------------------
FestivalFireworksFanliRender = FestivalFireworksFanliRender or BaseClass(BaseRender)
function FestivalFireworksFanliRender:__delete()
    
end

function FestivalFireworksFanliRender:LoadCallBack()
    self.node_list["btn_fanli"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))

    self.item_list_view = AsyncListView.New(ItemCell,self.node_list["fw_item_list"])
end

function FestivalFireworksFanliRender:ReleaseCallBack()
    if self.item_list_view then
		self.item_list_view:DeleteMe()
		self.item_list_view = nil
    end
end

function FestivalFireworksFanliRender:OnFlush()
    if not self.data then
        return
    end

    local data = self.data.data
    local draw_times = NiuDanWGData.Instance:GetCurDrawTimes()
    local color = draw_times >= data.lotto_num and COLOR3B.GREEN or COLOR3B.D_RED
    local str = draw_times .. "/" .. data.lotto_num
    local desc = ToColorStr(str, color)
    self.node_list["txt_times"].text.text = string.format(Language.MergeFireworks.TxtTimes, desc, data.lotto_num)
    local is_show = data.lotto_num <= draw_times and self.data.has_get ~= 1
    self.node_list["red"]:SetActive(is_show)
    self.node_list["is_get"]:SetActive(self.data.has_get == 1)
    self.node_list["btn_fanli"]:SetActive(self.data.has_get ~= 1)
    XUI.SetButtonEnabled(self.node_list["btn_fanli"], is_show)

    local reward_data = {}
	if data.reward_item then
		for i=0,#data.reward_item do
			table.insert(reward_data,data.reward_item[i])
		end
	end
	reward_data = SortDataByItemColor(reward_data)
	self.item_list_view:SetDataList(reward_data)
end

function FestivalFireworksFanliRender:OnClickGet()
    if not self.data then
        return
    end

    local draw_times = NiuDanWGData.Instance:GetCurDrawTimes()
    local data = self.data.data
    if draw_times >= data.lotto_num and self.data.has_get == 0 then
        NiuDanWGCtrl.Instance:SendReq(CSA_YANHUA_OP.FETCH, data.index)
    end
end

MergeFireworksShowRender = MergeFireworksShowRender or BaseClass(BaseRender) 

function MergeFireworksShowRender:LoadCallBack()
    self.show_item = ItemCell.New(self.node_list.pos)
end

function MergeFireworksShowRender:__delete()
    if self.show_item then
        self.show_item:DeleteMe()
    end
    self.show_item = nil
    self.item_id = 0
end

function MergeFireworksShowRender:SetData(data)
    self.data = data

    if not IsEmptyTable(self.data) and not IsEmptyTable(self.data.reward_item) then
        if self.item_id ~= self.data.reward_item.item_id then
            self.item_id = self.data.reward_item.item_id
            self.show_item:SetData(self.data.reward_item)
        end
    end
end

FestivalFireworksEggRender = FestivalFireworksEggRender or BaseClass(BaseRender)


function FestivalFireworksEggRender:EggStartAni()
    local hammer_tween1 = self.node_list["hammer"].canvas_group:DoAlpha(0, 1, 0.5)
    hammer_tween1:OnComplete(function ()
        local hammer_tween2 = self.node_list["hammer"].transform:DOLocalRotate(u3dpool.vec3(0, 0, 30), 0.5, DG.Tweening.RotateMode.Fast)
        hammer_tween2:SetEase(DG.Tweening.Ease.InOutBack)
        hammer_tween2:OnComplete(function ()
            -- self.node_list["egg_img_1"].canvas_group:DoAlpha(1, 0, 0.2)
            -- self.node_list["egg_img_2"].canvas_group:DoAlpha(0, 1, 0.2)
            self.node_list["egg_img_1"]:SetActive(false)
            self.node_list["egg_img_2"]:SetActive(true)
            self.node_list["hammer"].transform:DOLocalRotate(u3dpool.vec3(0, 0, 0), 0.5, DG.Tweening.RotateMode.Fast)
            self.node_list["hammer"].canvas_group:DoAlpha(1, 0, 0.5)
        end)
    end)
end

function FestivalFireworksEggRender:EggEndAni()
    self.node_list["hammer"].canvas_group.alpha = 0
    -- self.node_list["egg_img_1"].canvas_group.alpha = 1
    -- self.node_list["egg_img_2"].canvas_group.alpha = 0
    self.node_list["egg_img_1"]:SetActive(true)
    self.node_list["egg_img_2"]:SetActive(false)
end