function SwornView:InitSwornStartView()
    if not self.start_team_menber_list then
		self.start_team_menber_list = AsyncListView.New(SwornStartMenberItemRender, self.node_list.start_team_menber_list)
	end

    if not self.relieve_menber_list then
		self.relieve_menber_list = AsyncListView.New(SwornStartRelieveMenberItemRender, self.node_list.relieve_menber_list)
	end
    
    -- local sworn_cfg = SwornWGData.Instance:GetSwornOtherCfg()
    -- if not IsEmptyTable(sworn_cfg) then
    --     local item_cfg = ItemWGData.Instance:GetItemConfig(sworn_cfg.jieyi_item_id)

    --     if not IsEmptyTable(item_cfg) then
    --         local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    --         local num = sworn_cfg.jieyi_item_num
    --         self.node_list.sworn_start_cost_text.text.text = string.format(Language.Sworn.SwornStartCost, name, num)
    --     end
    -- end

    if not self.cost_item then
        self.cost_item = ItemCell.New(self.node_list.cost_item)
    end

    self.node_list.label_desc_text.text.text = Language.Sworn.LableDesc
    self.node_list.label_reward_text.text.text = Language.Sworn.LableReward
    self.show_relieve_panel = false

    XUI.AddClickEventListener(self.node_list.btn_start_sworn, BindTool.Bind(self.OnClickStartSwornBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_cancal_sworn, BindTool.Bind(self.OnClickCancalSwornBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_relieve_sworn, BindTool.Bind(self.OnClickRelieveBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_back_sworn, BindTool.Bind(self.OnClickBackBtn, self)) 
end

function SwornView:ShowSwornStartView()
    local is_sworn_now = SwornWGData.Instance:IsSwornNow()
    self.node_list.show_panel:SetActive(not is_sworn_now)
    self.node_list.wait_panel:SetActive(is_sworn_now)
end

function SwornView:SwornStartViewReleaseCallBack()
    if self.start_team_menber_list then
        self.start_team_menber_list:DeleteMe()
		self.start_team_menber_list = nil
	end

    if self.canca_sworn_alert then
        self.canca_sworn_alert:DeleteMe()
        self.canca_sworn_alert = nil
    end 
    
    if self.cost_item then
        self.cost_item:DeleteMe()
        self.cost_item = nil
    end

    if self.relieve_menber_list then
        self.relieve_menber_list:DeleteMe()
		self.relieve_menber_list = nil
	end
end

function SwornView:FlushSwornStartView()
    local sworn_state = SwornWGData.Instance:GetMySwornState()
    local no_sworn = sworn_state == SwornWGData.SWORN_TYPE.NOSWORN
    local has_sworn = sworn_state == SwornWGData.SWORN_TYPE.HAS_SWORN
    local sworning = sworn_state == SwornWGData.SWORN_TYPE.SWORNING

    self.node_list.show_panel:SetActive(no_sworn)
    self.node_list.wait_panel:SetActive(not no_sworn and not self.show_relieve_panel)
    self.node_list.relieve_panel:SetActive(has_sworn and self.show_relieve_panel)

    self.node_list.sworn_limit_time.text.text = ""
    self.node_list.sworn_start_desc.text.text = ""
    self.node_list.btn_relieve_sworn:SetActive(has_sworn)

    if not no_sworn then
        local num, data_list = SwornWGData.Instance:GetMyTeamMenberInfo()

        if not IsEmptyTable(data_list) and self.start_team_menber_list then
            self.start_team_menber_list:SetDataList(data_list)
        end

        if has_sworn then
            self:CleanRefreshTime()
            self.node_list.sworn_start_desc.text.text = Language.Sworn.SwornStartDesc
            self.node_list.btn_cancal_sworn_text.text.text = Language.Sworn.OutSwornBtn
            local relieve_remind = SwornWGData.Instance:GetSwornRelieveRemind() == 1
            self.node_list.btn_relieve_sworn_remind:SetActive(relieve_remind)
            self:FlushStartRelieveView()
        else
            self.node_list.btn_cancal_sworn_text.text.text = Language.Sworn.CancleSwornBtn
            self:FlushSwornLimitRefreshTime()
        end

    else
       self:FlushCostItem()
    end
end

function SwornView:FlushCostItem()
    local cost_cfg = SwornWGData.Instance:GetSwornOtherCfg()

    if not IsEmptyTable(cost_cfg) then
        local cost_item_id = cost_cfg.jieyi_item_id
        local cost_item_num = cost_cfg.jieyi_item_num
        local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
        local enough = has_num >= cost_item_num

        self.cost_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(has_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.cost_item:SetRightBottomColorText(right_text)
            self.cost_item:SetRightBottomTextVisible(true)
            self.cost_item:SetNeedItemGetWay(true)
        end)
    
        self.cost_item:SetData({item_id = cost_item_id, num = cost_item_num, is_bind = 0})
    end
end

function SwornView:OnClickStartSwornBtn()
    --增加cd处理
    local cannot_jieyi, cd_time = SwornWGData.Instance:GetJieYiCdTime()

    if cannot_jieyi then
        TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Sworn.JieYiCdTimeTip, TimeUtil.FormatSecondDHM2(cd_time)))
        return
    end

    local sworn_cfg = SwornWGData.Instance:GetSwornOtherCfg()
    local cost_item_id = sworn_cfg.jieyi_item_id
    local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

    if has_num >= sworn_cfg.jieyi_item_num then
        SwornWGCtrl.Instance:OpenSwornProtocolView()
    else
        local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
        local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Sworn.NoEnough, name))
        TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
    end
end

function SwornView:OnClickCancalSwornBtn()
    if not self.canca_sworn_alert then
        self.canca_sworn_alert = Alert.New()
    end

    self.canca_sworn_alert:SetOkFunc(function ()
        SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_LEAVE_TEAM)
	end)

    local sworn_state = SwornWGData.Instance:GetMySwornState()
    local out_sworn_str = string.format(Language.Sworn.OutSworn, SwornWGData.Instance:GetSwornCdTime())
    local lable = sworn_state == SwornWGData.SWORN_TYPE.SWORNING and Language.Sworn.CancleSworn or out_sworn_str
    self.canca_sworn_alert:SetLableString(lable)
    self.canca_sworn_alert:SetLableRectWidth(460)
    self.canca_sworn_alert:Open()
end

function SwornView:FlushSwornLimitRefreshTime()
	self:CleanRefreshTime()
	local interval = 1
    local time =  SwornWGData.Instance:GetSwornStartLimitTime()
    local TimeColor = "#E0FF62"
    self.node_list.sworn_limit_time.text.text = Language.Sworn.SwornLimitTime .. ToColorStr(TimeUtil.FormatSecondDHM2(time), TimeColor)

	self.refresh_time = CountDown.Instance:AddCountDown(time, interval,
	    function(elapse_time, total_time)
		    local refresh_time = total_time - elapse_time

		    if self.node_list.sworn_limit_time then
			    self.node_list.sworn_limit_time.text.text = Language.Sworn.SwornLimitTime .. ToColorStr(TimeUtil.FormatSecondDHM2(refresh_time), TimeColor)
		    end
	    end,
	    function()
            if self.node_list and self.node_list.sworn_limit_time then
                self.node_list.sworn_limit_time.text.text = ""
            end
	    end
    )
end

function SwornView:CleanRefreshTime()
    if self.refresh_time and CountDown.Instance:HasCountDown(self.refresh_time) then
        CountDown.Instance:RemoveCountDown(self.refresh_time)
        self.refresh_time = nil
    end
end

function SwornView:FlushStartRelieveView()
    local data_list = SwornWGData.Instance:GetRelieveMemberData()
    self.relieve_menber_list:SetDataList(data_list)
end

function SwornView:OnClickRelieveBtn()
    self:SetRelievePanelState(true)
end

function SwornView:OnClickBackBtn()
    self:SetRelievePanelState(false)
end

function SwornView:SetRelievePanelState(state)
    self.show_relieve_panel = state
    self.node_list.wait_panel:SetActive(not state)
    self.node_list.relieve_panel:SetActive(state)
end

--------------------------------------------结义Item----------------------------------------------------
SwornStartMenberItemRender = SwornStartMenberItemRender or BaseClass(BaseRender)
function SwornStartMenberItemRender:__init()
    if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["head_pos"])
	end

    self.role_avatar = RoleHeadCell.New(false)
end

function SwornStartMenberItemRender:__delete()
    if self.role_head_cell then
        self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

    if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
    end
end

function SwornStartMenberItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind(self.OnClickAddBtn, self))
    XUI.AddClickEventListener(self.node_list.head_pos, BindTool.Bind(self.OnClickHead, self))
end

function SwornStartMenberItemRender:OnFlush()
    if IsEmptyTable(self:GetData()) then
        return
    end

    local data = self:GetData()
    local active = data.uid > 0

    self.node_list["head_pos"]:SetActive(active)
    self.node_list.btn_add:SetActive(not active)
    self.node_list.name.text.text = active and data.name or ""
    self.node_list.prof_bg:SetActive(active)

    if active then
        local head_data = {fashion_photoframe = data.shizhuang_photoframe}
        head_data.role_id = data.uid
        head_data.prof = data.prof
        head_data.sex = data.sex
        head_data.is_show_main = true

        self.role_head_cell:SetImgBg(data.shizhuang_photoframe > 0)
        self.role_head_cell:SetData(head_data)
        self.node_list["prof_icon"].image:LoadSprite(ResPath.GetCommonImages(RoleWGData.GetProfIcon(self.data.prof, self.data.sex)))
    end
end

function SwornStartMenberItemRender:OnClickAddBtn()
    SwornWGCtrl.Instance:OpenSwornInviteView()
end

function SwornStartMenberItemRender:OnClickHead()
    if IsEmptyTable(self:GetData()) then
        return
    end

    local data = self:GetData()
    local active = data.uid > 0
    local is_mine = RoleWGData.Instance:GetOriginUid() == data.uid

    if active and not is_mine then
        if self.role_avatar then
            self.role_avatar:AddItems(Language.Menu.DeleteFriend)
            self.role_avatar:RemoveItems(Language.Menu.AddFriend)
            self.role_avatar:RemoveItems(Language.Menu.PrivateChat)
            self.role_avatar:SetIsFormPrivate(true)
            local role_info = {
                role_id = data.uid,
                role_name = data.name,
                prof = data.prof,
                sex = data.sex,
                is_online = 0 ~= data.is_online,
                plat_type = data.origin_plat_type,
                server_id = data.merge_server_id,
            }
            self.role_avatar:SetRoleInfo(role_info)
        end

        self.role_avatar:OpenMenu(nil, self:GetMenuNodePos(self:GetIndex() <= 3))
    end
end

function SwornStartMenberItemRender:GetMenuNodePos(is_right)
    local main_view = ViewManager.Instance:GetView(GuideModuleName.SwornView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end
    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.node_list.open_menu_pos.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y

    if is_right then
        x = x + 210
        y = y - 70
    else
        x = x - 210
        y = y - 70
    end

    return Vector2(x, y)
end


SwornStartRelieveMenberItemRender = SwornStartRelieveMenberItemRender or BaseClass(BaseRender)
function SwornStartRelieveMenberItemRender:__init()
    if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["head_pos"])
	end

    XUI.AddClickEventListener(self.node_list.btn_opera, BindTool.Bind(self.OnClickOperaBtn, self)) 
end

function SwornStartRelieveMenberItemRender:__delete()
    if self.role_head_cell then
        self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

    if self.relieve_tips then
        self.relieve_tips:DeleteMe()
        self.relieve_tips = nil
    end

    if self.vote_tips then
        self.vote_tips:DeleteMe()
        self.vote_tips = nil
    end
end

function SwornStartRelieveMenberItemRender:OnFlush()
    if IsEmptyTable(self:GetData()) then
        return
    end

    local data = self:GetData()
    local is_mine = RoleWGData.Instance:GetOriginUid() == data.uid

    local head_data = {fashion_photoframe = data.shizhuang_photoframe}
    head_data.role_id = data.uid
    head_data.prof = data.prof
    head_data.sex = data.sex
    head_data.is_show_main = true

    self.role_head_cell:SetImgBg(data.shizhuang_photoframe > 0)
    self.role_head_cell:SetData(head_data)
    self.node_list.cap.text.text = data.capability
    self.node_list.btn_opera:SetActive(not is_mine)

    local is_vis, level = RoleWGData.Instance:GetDianFengLevel(data.level)
    self.node_list.dianfeng_img:SetActive(is_vis)
    self.node_list.level_txt.text.text = level

    local status = ""
    local color_name = ""
	if 1 == data.is_online then --在线
		status = ToColorStr(Language.Common.OnLine, COLOR3B.DEFAULT_NUM)
		color_name = ToColorStr(data.name, COLOR3B.WHITE)
		self.role_head_cell:SetGray(false)
	else --离线
		local time_format = TimeWGCtrl.Instance:GetServerTime() - data.last_login_time
		status = ToColorStr(GuildWGData.FormatTime(time_format), COLOR3B.WHITE)
		color_name = ToColorStr(data.name, COLOR3B.WHITE)
		self.role_head_cell:SetGray(true)
	end
	self.node_list.state.text.text = status  --在线状态
    self.node_list.name.text.text = color_name

    if not is_mine then
        local opera_str = Language.Sworn.RelieveType1
        local vote_time, vote_uid, be_vote_uid = SwornWGData.Instance:GetJieYiTeamVoteInfo()
        if vote_time > 0 and vote_uid > 0 and be_vote_uid > 0 and data.uid == be_vote_uid then
            local vote_flag = SwornWGData.Instance:GetMyRelieveFlag()
            opera_str = vote_flag and Language.Sworn.RelieveType3 or Language.Sworn.RelieveType2
        end

        self.node_list.btn_opera_text.text.text = opera_str
    end
end

function SwornStartRelieveMenberItemRender:OnClickOperaBtn()
    if IsEmptyTable(self:GetData()) then
        return
    end

    local data = self:GetData()
    local vote_time, vote_uid, be_vote_uid = SwornWGData.Instance:GetJieYiTeamVoteInfo()
    local has_vote = vote_time > 0 and vote_uid > 0 and be_vote_uid > 0 
    local is_be_vote = be_vote_uid == data.uid
    local my_tem_active_member = SwornWGData.Instance:GetMyTeamMenberInfo()
    local current_time = TimeWGCtrl.Instance:GetServerTime()
    local is_cold_time = vote_time > 0 and vote_time > current_time and vote_uid <= 0 and be_vote_uid <= 0

    if my_tem_active_member >= 3 then
        if has_vote then
            if is_be_vote then
                local vote_flag = SwornWGData.Instance:GetMyRelieveFlag()

                if not vote_flag then
                    local vote_info = SwornWGData.Instance:GetJieyiBeVoteMemberInfo()

                    if not IsEmptyTable(vote_info) then
                        if not self.vote_tips then
                            self.vote_tips = Alert.New()
                            self.vote_tips:SetOkString(Language.Sworn.RelieveAgreeBtn)
                            self.vote_tips:SetCancelString(Language.Sworn.RelieveDisagreeBtn)
                        end

                        local str = string.format(Language.Sworn.RelieveOperaDesc, vote_info.name)
                        self.vote_tips:SetLableString(str)
                        self.vote_tips:SetOkFunc(function ()
                            SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_VOTE_RESULT, 1)    
                        end)
                        self.vote_tips:SetCancelFunc(function ()
                            SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_VOTE_RESULT, 0)    
                        end)
    
                        self.vote_tips:Open()
                    end
                else
                    TipWGCtrl.Instance:ShowSystemMsg(Language.Sworn.RelieveType3)
                end
            else
                TipWGCtrl.Instance:ShowSystemMsg(Language.Sworn.HasRelieveMenber)
            end
        else
            if is_cold_time then
                TipWGCtrl.Instance:ShowSystemMsg(Language.Sworn.RelieveColdTime)
            else
                if not self.relieve_tips then
                    self.relieve_tips = Alert.New()
                end

                local str = string.format(Language.Sworn.RelieveApplyDesc, data.name)
                self.relieve_tips:SetLableString(str)
                self.relieve_tips:SetOkFunc(function ()
                    SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_VOTE, data.uid)    
                end)

                self.relieve_tips:Open()
            end
        end
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.Sworn.RelieveMemberNotEnough)
    end
end