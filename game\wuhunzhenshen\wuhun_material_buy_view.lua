WuHunMaterialBuyView = WuHunMaterialBuyView or BaseClass(SafeBaseView)

function WuHunMaterialBuyView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/wuhunzhenshen_prefab", "layout_wuhun_material_buy")
end

function WuHunMaterialBuyView:LoadCallBack()
	self.cur_wh_data = nil
	self.buy_cfg = {}
	self.cur_buy_price = 0

	self.model_display = nil

	----[[模型展示
	if self.model_display == nil then
		self.model_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.model_display:SetRenderTexUI3DModel(display_data)
		-- self.model_display:SetUI3DModel(self.node_list["display_model"].transform,
		-- 	self.node_list["display_model"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.model_display)
	end

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuyBtn, self))
end

function WuHunMaterialBuyView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("wuhun_material_buy_down") then
		CountDownManager.Instance:RemoveCountDown("wuhun_material_buy_down")
	end

	if self.model_display then
		self.model_display:RemoveSoulFormation()
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	self.cur_wh_data = nil
	self.buy_cfg = {}

	self.curr_details_hunzhen_appid = nil
	self.model_display = nil
end

function WuHunMaterialBuyView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("wuhun_material_buy_down") then
		CountDownManager.Instance:RemoveCountDown("wuhun_material_buy_down")
	end
end

function WuHunMaterialBuyView:OnFlush(param_t, index)
	self.cur_wh_data = param_t.all
	if not self.cur_wh_data then
		return
	end

	self.buy_cfg = WuHunWGData.Instance:GetWuhunMaterialCfg(self.cur_wh_data.wuhun_id)
	if IsEmptyTable(self.buy_cfg) then
		return
	end

	self:SetModel()
	self:SetBuyBtn()

	self.reward_list:SetDataList(self.buy_cfg.reward_list)

	if not CountDownManager.Instance:HasCountDown("wuhun_material_buy_down") then
		self:LoginTimeCountDown()
	end
end

function WuHunMaterialBuyView:SetBuyBtn()
	--是否可以购买.
	local is_can_buy = WuHunWGData.Instance:CheckWuhunMaterialIsCanBuy(self.cur_wh_data.wuhun_id)

	self.node_list.price_group:SetActive(is_can_buy)
	self.node_list.nobuy_text:SetActive(not is_can_buy)
	self.node_list.description:SetActive(is_can_buy)
	XUI.SetButtonEnabled(self.node_list.buy_btn, is_can_buy)

	if not is_can_buy then
		return
	end

	--是否打折.
	local is_discount = WuHunWGData.Instance:CheckWuhunMaterialIsDiscount(self.cur_wh_data.wuhun_id)

	local original_num = self.buy_cfg.price
	self.cur_buy_price = self.buy_cfg.price

	if is_discount then
		local original_cfg = WuHunWGData.Instance:GetWuhunMaterialCfgByDis(self.cur_wh_data.wuhun_id, 0)
		if IsEmptyTable(original_cfg) then
			return
		end

		original_num = original_cfg.price
		local change_original_price = RoleWGData.GetPayMoneyStr(original_num)
		self.node_list.original_price.text.text = string.format(Language.WuHunMaterialBuy.BuyText, change_original_price)
	end

	local change_buy_price = RoleWGData.GetPayMoneyStr(self.cur_buy_price)

	self.node_list.buy_price.text.text = change_buy_price
	self.node_list.original_price:SetActive(is_discount)
	self.node_list.act_time:SetActive(is_discount)

	self.node_list.btn_description.text.text = self.buy_cfg.buy_desc
end

function WuHunMaterialBuyView:SetModel()
	if self.model_display == nil then
		return
	end

	local active = WuHunWGData.Instance:GetWuHunActiveCfg(self.cur_wh_data.wuhun_id)
	self.curr_wuhun_appeid = active and active.appe_image_id or 10114

	local bundle, asset = ResPath.GetWuHunModel(self.curr_wuhun_appeid)
	self.model_display:SetMainAsset(bundle, asset, function()
		self.model_display:PlayRoleAction(SceneObjAnimator.Rest1)
	end)

	local front_use_index = WuHunFrontWGData.Instance:GetWuHunFrontHuanHuaIndex(self.cur_wh_data.wuhun_id)
	local cfg = WuHunFrontWGData.Instance:GetSoulFormationAppimageCfg(self.cur_wh_data.wuhun_id, front_use_index)

	if cfg then
		if self.curr_details_hunzhen_appid ~= cfg.app_image_id then
			self.curr_details_hunzhen_appid = cfg.app_image_id
			self.model_display:SetSoulFormationResid(cfg.app_image_id, self.cur_wh_data.wuhun_id)
		end
	else
		self.curr_details_hunzhen_appid = nil
		self.model_display:RemoveSoulFormation()
	end
end

function WuHunMaterialBuyView:OnClickBuyBtn()
	if IsEmptyTable(self.buy_cfg) then
		return
	end

	RechargeWGCtrl.Instance:Recharge(self.cur_buy_price, self.buy_cfg.rmb_type, self.buy_cfg.rmb_seq)
end

function WuHunMaterialBuyView:SetWuHunMaterialBuyCountDown()
	if CountDownManager.Instance:HasCountDown("wuhun_material_buy_down") then
		CountDownManager.Instance:RemoveCountDown("wuhun_material_buy_down")
	end

	if not CountDownManager.Instance:HasCountDown("wuhun_material_buy_down") then
		self:LoginTimeCountDown()
	end
end

------------------------------------活动时间倒计时------------------------------------
function WuHunMaterialBuyView:LoginTimeCountDown()
	local invalid_time = WuHunWGData.Instance:GetWuhunMaterialTime(self.cur_wh_data.wuhun_id)
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.act_time_text.text.text = string.format(Language.WuHunMaterialBuy.ActTimer,
			TimeUtil.FormatSecondDHM6(invalid_time - TimeWGCtrl.Instance:GetServerTime()))

		CountDownManager.Instance:AddCountDown("wuhun_material_buy_down", BindTool.Bind1(self.UpdateCountDown, self),
			BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	end
end

function WuHunMaterialBuyView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list.act_time_text.text.text = string.format(Language.WuHunMaterialBuy.ActTimer,
			TimeUtil.FormatSecondDHM6(valid_time))
	end
end

function WuHunMaterialBuyView:OnComplete()
	self.node_list.act_time_text.text.text = ""
	self:Close()
end
