require("game/operation_activity/image_show/image_show_view")
require("game/operation_activity/image_show/image_show_wg_data")

ImageShowWGCtrl = ImageShowWGCtrl or BaseClass(BaseWGCtrl)

function ImageShowWGCtrl:__init()
	if ImageShowWGCtrl.Instance then
		ErrorLog("[ImageShowWGCtrl] Attemp to create a singleton twice !")
	end
	ImageShowWGCtrl.Instance = self
	self.image_show_data = ImageShowWGData.New()

	self:RegisterAllProtocols()

	OperationActivityWGCtrl.Instance:ListenHotUpdate(ImageShowWGData.ConfigPath, BindTool.Bind(self.OnHotUpdate, self))

end

function ImageShowWGCtrl:__delete()
	ImageShowWGCtrl.Instance = nil

	if self.image_show_data ~= nil then
		self.image_show_data:DeleteMe()
		self.image_show_data = nil
	end
end

function ImageShowWGCtrl:RegisterAllProtocols()

end



function ImageShowWGCtrl:OnHotUpdate()
	self.xianshi_miaosha_data:FlushXianShiMiaoShaCfg()
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_xianshi_miaosha, "xianshi_hot_update")
end

-----------------------------------------------------------------------------------------------------------------------------------