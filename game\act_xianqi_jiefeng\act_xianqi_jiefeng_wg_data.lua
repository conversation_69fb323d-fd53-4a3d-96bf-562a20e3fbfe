

ActXianQiJieFengWGData = ActXianQiJieFengWGData or BaseClass()
function ActXianQiJieFengWGData:__init()
	if ActXianQiJieFengWGData.Instance then
		ErrorLog("[ActXianQiJieFengWGData] Attemp to create a singleton twice !")
	end
	ActXianQiJieFengWGData.Instance = self
	self.theme_cfg_list = {}

	--仙器冲榜信息
	self.longhun_rank_log_list = {}
	self.longhun_rank_role_info = {}
	self.longhunrank_join_flag = {}
	self.longhunrank_join_reward_flag = {}

	RemindManager.Instance:Register(RemindName.XianQiJieFeng, BindTool.Bind(self.IsShowMainViewRedPoint, self))
	RemindManager.Instance:Register(RemindName.XianQiJieFeng_Login, BindTool.Bind(self.IsShowLoginRedPoint, self))

	RemindManager.Instance:Register(RemindName.XianQiJieFeng_<PERSON>hou<PERSON><PERSON>, BindTool.Bind(self.IsShowShouChongRedPoint, self))
	RemindManager.Instance:Register(RemindName.XianQiJieFeng_LeiChong, BindTool.Bind(self.IsShowLeiChongRedPoint, self))

	RemindManager.Instance:Register(RemindName.XianQiJieFeng_LaiXi, BindTool.Bind(self.IsShowLaiXiRedPoint, self))
	RemindManager.Instance:Register(RemindName.XianQiJieFeng_HaoLi, BindTool.Bind(self.IsShowHaoLiRedPoint, self))
	RemindManager.Instance:Register(RemindName.XianQiJieFeng_JuanXian, BindTool.Bind(self.IsShowJuanXianRedPoint, self))
	RemindManager.Instance:Register(RemindName.XianQiJieFeng_LongHun, BindTool.Bind(self.IsShowLongHunRedPoint, self))
	RemindManager.Instance:Register(RemindName.XianQiJieFeng_Cap, BindTool.Bind(self.IsShowRoleCapRedPoint, self))
	RemindManager.Instance:Register(RemindName.XianQiJieFeng_Cap2, BindTool.Bind(self.IsShowServerCapRedPoint, self))
	--仙器冲榜
	RemindManager.Instance:Register(RemindName.XianQiJieFeng_LongHunRank, BindTool.Bind(self.CheckLongHunRankRemind, self))

	local banzhan_all_cfg = ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto")
	self.long_hun_chong_bang_rank_cfgs = ListToMap(banzhan_all_cfg.longhun_chongbang_rank, "rank")
	self.long_hun_chong_bang_award_cfgs = ListToMap(banzhan_all_cfg.longhun_chongbang_reward, "ID")
	self:InitLongHunRankCfgList()
end

function ActXianQiJieFengWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_Login)

	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_ShouChong)
	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_LeiChong)

	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng)
	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_LaiXi)
	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_HaoLi)
	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_JuanXian)
	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_LongHun)
	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_Cap)
	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_Cap2)
	RemindManager.Instance:UnRegister(RemindName.XianQiJieFeng_LongHunRank)

	self.longhun_rank_cfg_list = nil
	ActXianQiJieFengWGData.Instance = nil
end

function ActXianQiJieFengWGData:GetLoginRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").login_reward
end

function ActXianQiJieFengWGData:GetActivityThemeCfgList()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").beizhan_theme_dec
end

function ActXianQiJieFengWGData:GetActivityThemeOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").beizhan_theme_other
end

function ActXianQiJieFengWGData:SetThemeCfgByTabIndex(tab_index, cfg)
	self.theme_cfg_list[tab_index] = cfg
end

function ActXianQiJieFengWGData:GetThemeCfgByTabIndex(tab_index)
	return self.theme_cfg_list[tab_index]
end

function ActXianQiJieFengWGData:GetActivityName(real_id)
	local cfg = self:GetActivityThemeCfgList()
	for k,v in ipairs(cfg) do
		if v.real_id == real_id then
			return v.active_name
		end
	end
	return ""
end

function ActXianQiJieFengWGData:GetActivityDefultCfg(real_id)
	local cfg = self:GetActivityThemeCfgList()
	for k,v in ipairs(cfg) do
		if v.real_id == real_id then
			return v
		end
	end
	return nil
end

--获取活动界面配置
function ActXianQiJieFengWGData:GetActivityThemeCfg(index)
	return self:GetThemeCfgByTabIndex(index)
end

--获取活动提示
function ActXianQiJieFengWGData:GetActivityTip(index)
	local cfg = self:GetThemeCfgByTabIndex(index)
	if cfg ~= nil then
		return cfg.tab_name, cfg.rule_desc
	end
	return nil,nil
end

------------------------------------  登录奖励 -------------------------------------------
-- 获取登录奖励配置
function ActXianQiJieFengWGData:GetLoginRewardCfgByDay(day)
	local cfg = self:GetLoginRewardCfg()
	for k,v in pairs(cfg) do
		if day == v.day_index then
			return v
		end
	end
end

function ActXianQiJieFengWGData:SetLoginRewardInfo(protocol)
	if self.LoginDayData == nil then
		self.LoginDayData = {}
	end
	self.LoginDayData.day_list = {}
	self.LoginDayData.activity_day_index = protocol.activity_day_index
	self.LoginDayData.len = 0
	local cfg = self:GetLoginRewardCfg()
	for k,v in pairs(cfg) do
		local data = {}
		data.index = v.day_index
		data.vip_level = v.vip_lv or 0
		data.common_gift_state = protocol.reward_state[v.day_index].common_gift_state 	--是否领取
		data.special_gift_state = protocol.reward_state[v.day_index].special_gift_state
		table.insert(self.LoginDayData.day_list,data)
		self.LoginDayData.len = self.LoginDayData.len + 1
	end
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_Login)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng)
end

function ActXianQiJieFengWGData:SetSelectDayItem(item, select_bg)
	self.cur_day_item_select = item
	self.cur_day_item_select_bg = select_bg
end

-- vip等级变化后端不推的
function ActXianQiJieFengWGData:FlushLoginRewardInfo()
	if self.LoginDayData then
		local vip_level = VipWGData.Instance:GetRoleVipLevel()
		local cur_day = self.LoginDayData.activity_day_index or 0
		local day_list = self.LoginDayData.day_list or {}
		for i=1,#day_list do
			if day_list[i].special_gift_state == 0 and cur_day >= day_list[i].index and
				day_list[i].vip_level > 0 and vip_level >= day_list[i].vip_level then
				day_list[i].special_gift_state = 1
			end
		end
		self.LoginDayData.day_list = day_list
	end
end

function ActXianQiJieFengWGData:GetLoginRewardInfo(day_index)
	if self.LoginDayData then
		return self.LoginDayData.day_list[day_index]
	end
end

function ActXianQiJieFengWGData:GetLoginDayData()
	return self.LoginDayData
end

function ActXianQiJieFengWGData:GetLoginDyaIndex()
	if self.LoginDayData == nil or self.LoginDayData.activity_day_index == nil then
		return 0
	end
	return self.LoginDayData.activity_day_index
end

function ActXianQiJieFengWGData:GetCurSelectDay()
	local day_index = self:GetFirstHasRewardDayIndex()
	if day_index == 0 and self.LoginDayData then
		if self:GetLoginRewardCfgByDay(self.LoginDayData.activity_day_index + 1)  then
			day_index = self.LoginDayData.activity_day_index + 1
		else
			day_index = self.LoginDayData.activity_day_index
		end
	end
	return day_index
end

function ActXianQiJieFengWGData:GetFirstHasRewardDayIndex()
	if self.LoginDayData ~= nil and self.LoginDayData.day_list ~= nil then
		local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
		for k,v in ipairs(self.LoginDayData.day_list) do
			local cfg = self:GetLoginRewardCfgByDay(v.index)
			if v.common_gift_state == ActivityRewardState.KLQ then
				return k
			end

			if v.special_gift_state == ActivityRewardState.KLQ then
				return k
			else
				if v.special_gift_state == ActivityRewardState.BKL and v.index <= self.LoginDayData.activity_day_index then
					if cfg.vip_lv ~= "" and cfg.vip_lv > 0 and cur_vip_level >= cfg.vip_lv then
						return k
					end
				end
			end
		end
	end
	return 0
end



function ActXianQiJieFengWGData:GetDyaItemSelect()
	return self.cur_day_item_select, self.cur_day_item_select_bg
end

-------------------------------充值---------------------------------
function ActXianQiJieFengWGData:GetShouChongRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").shouchong_reward
end

function ActXianQiJieFengWGData:GetLeiChongRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").leichonghaoli_reward
end

function ActXianQiJieFengWGData:SetShouChongInfo(protocol)
	if self.ShouChongData == nil then
		self.ShouChongData = {}
	end
	self.ShouChongData.gift_state = protocol.gift_state -- 首充奖励领取状态
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_ShouChong)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng)
end

function ActXianQiJieFengWGData:SetLeiChongInfo(protocol)
	if self.LeiChongData == nil then
		self.LeiChongData = {}
	end
	self.LeiChongData.cur_xianyu = protocol.cur_xianyu
	self.LeiChongData.cur_dangwei = protocol.cur_dangwei
	self.LeiChongData.dangwei_num = protocol.dangwei_num
	self.LeiChongData.dangwei_info = protocol.dangwei_info

	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_LeiChong)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng)
end

--获取累充奖励排行数据
function ActXianQiJieFengWGData:GetLeiChongRewardList()
	local list = {}
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		local data = {}
		data.cfg = v
		data.ID = v.ID
		data.receive_state = self:GetLeiChongReceiveState(v.ID)
		if data.receive_state == TianShenRoadRewardState.KLQ then
			data.sort = 0
		elseif data.receive_state == TianShenRoadRewardState.BKL then
			data.sort = 1
		else
			data.sort = 2
		end
		table.insert(list, data)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort", "ID"))
	return list
end

function ActXianQiJieFengWGData:GetLeiChongProgress(leichong_value)  --亲密度进度条
	local cur_progress = 0
	local cfg = self:GetLeiChongRewardCfg()
	local cur_leichong_value = tonumber(leichong_value)
	if next(cfg) == nil or leichong_value == nil then
		return cur_progress
	end
	local progress_list = {0.1, 0.3, 0.52, 0.74, 1}			--对应的进度条值

	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list
		local cur_need = cfg[seq] and cfg[seq].stage_value or 0
		local next_need = cfg[seq + 1] and cfg[seq + 1].stage_value or cfg[#cfg].stage_value
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		if leichong_value > cur_need and leichong_value <= next_need then
			cur_progress = (leichong_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif leichong_value > cfg[#cfg].stage_value then
			cur_progress = progress_list[length]
			break
		end
	end
	return cur_progress
end

-- 获取每日首充奖励配置
function ActXianQiJieFengWGData:GetShouChongRewardCfgByDay(day)
	local cfg = self:GetShouChongRewardCfg()
	for k,v in pairs(cfg) do
		if day == v.day_index then
			return v
		end
	end
	return nil
end

--首充领取状态
function ActXianQiJieFengWGData:GetShouChongGiftState()
	if self.ShouChongData ~= nil then
		return self.ShouChongData.gift_state
	end
	return 0
end

--当前充值仙玉
function ActXianQiJieFengWGData:GetOwnXianYu()
	if self.LeiChongData ~= nil then
		return self.LeiChongData.cur_xianyu
	end
	return 0
end

--获取当前档需要充值的仙玉
function ActXianQiJieFengWGData:GetNeedRechargeXianYU()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		if cur_xianyu < v.stage_value then
			local pre_stage_value = 0
			if i > 1 then
				pre_stage_value = cfg[i-1].stage_value
			end
			return v.stage_value - cur_xianyu, v.stage_value, pre_stage_value
		end
	end
	return 0, cfg[#cfg].stage_value, cfg[#cfg].stage_value
end

--是否已达成
function ActXianQiJieFengWGData:IsRechargeTargetFinish()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	return cur_xianyu >= cfg[#cfg].stage_value
end

--获取领取状态
function ActXianQiJieFengWGData:GetLeiChongReceiveState(index)
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil and self.LeiChongData.dangwei_info[index] ~= nil then
		return self.LeiChongData.dangwei_info[index]
	else
		return 0
	end
end

---------------------------------多倍 -------------------------------------

function ActXianQiJieFengWGData:GetDuoBeiCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").duobeijiangli
end

function ActXianQiJieFengWGData:SetDuoBeiInfo(protocol)
	self.duobei_data = protocol
	MainuiWGCtrl.Instance:FlushDuoBei()
end

function ActXianQiJieFengWGData:GetDuoBeiInfo()
	if self.duobei_data ~= nil then
		local cfg = self:GetDuoBeiCfg()
		local list = {}
		for k,v in pairs(cfg) do
			if v.day == self.duobei_data.day then
				local info = {}
				info.cfg = v
				info.cur_finish_num = 0
				if self.duobei_data.task_info[v.task_type] then
					info.cur_finish_num = self.duobei_data.task_info[v.task_type].cur_finish_num
				end
				table.insert(list, info)
			end
		end
		return list
	end
	return nil
end

--获取副本是否有多倍活动开启
function ActXianQiJieFengWGData:GetHasDuoBeiInCopy()
	for i=RATSDUOBEI_TASK.FANRENXIUXIAN, RATSDUOBEI_TASK.HAIDIFEIXU do
		if self:GetDuoBeiTimes(i) > 0 then
			return true
		end
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.WABAO) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANGUDIAN) > 0 then
		return true
	end

	return false
end

--获取魔王是否有boss开启
function ActXianQiJieFengWGData:GetHasDuoBeiInBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.SHIJIEMOWANG) > 0 then
		return true
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.MOWANGCHAOXUE) > 0 then
		return true
	end

	return false
end

--获取世界服是否有boss开启
function ActXianQiJieFengWGData:GetHasDuoBeiInWorldBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.HONGMENGSHENYU) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANSHENSHOU) > 0 then
		return true
	end

	return false
end

function ActXianQiJieFengWGData:GetDuoBeiTimes(task_type)
	if self.duobei_data ~= nil and self:GetActivityState(TabIndex.xianqi_jiefeng_duobei) then
		local cfg = self:GetDuoBeiCfg()
		if cfg ~= nil then
			for k,v in pairs(cfg) do
				if v.day == self.duobei_data.day and v.task_type == task_type then
					if FunOpen.Instance:GetFunIsOpened(v.module_name) then
						return v.reward_mult
					end
				end
			end
		end
	end
	return 0
end

function ActXianQiJieFengWGData:GetItemDuoBeiTimes(task_type , item_id)
	if self.duobei_data ~= nil and self:GetActivityState(TabIndex.xianqi_jiefeng_duobei) then
		local cfg = self:GetDuoBeiCfg()
		if not cfg then
			return 0
		end

		for k,v in pairs(cfg) do
			if v.day == self.duobei_data.day and v.task_type == task_type then
				for k2,v2 in pairs(v.reward_item) do
					if v2.item_id == item_id then
						return v.reward_mult
					end
				end
			end
		end
	end
	return 0
end

------------------------------------- 我要仙器 --------------------------------------------
function ActXianQiJieFengWGData:GetXQTaskCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").woyaolonghun_task
end

function ActXianQiJieFengWGData:GetXQGiftCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").woyaolonghun_libao
end

function ActXianQiJieFengWGData:GetXQRewardCfg()
	if self.lh_reward_cfg == nil then
		self.lh_reward_cfg = ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").woyaolonghun_stage_reward
	end
	return self.lh_reward_cfg
end

function ActXianQiJieFengWGData:GetXQInfo()
	return self.XQData
end

function ActXianQiJieFengWGData:GetXQTaskList()
	local list = {}
	local cfg = self:GetXQTaskCfg()
	if not cfg then
		return {}
	end
	for i,v in ipairs(cfg) do
		local item = {}
		item.ID = v.ID
		item.cfg = v
		item.receive_state = self:GetXQTaskState(v.ID)
		if item.receive_state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.receive_state == ActivityRewardState.BKL then
			item.sort = 1
		else
			item.sort = 2
		end
		table.insert(list, item)
	end

	if not IsEmptyTable(list) then
		table.sort(list, SortTools.KeyLowerSorter("sort","ID"))
	end
	return list
end

function ActXianQiJieFengWGData:GetXQTaskState(task_id)
	if self.XQData ~= nil then
		for k,v in pairs(self.XQData.task_list) do
			if v.task_id == task_id then
				return v.gift_state
			end
		end
	end
	return 0
end

function ActXianQiJieFengWGData:SetXQInfo(protocol)
	self.XQData = {}
	self.XQData.dangwei_jifen = protocol.dangwei_jifen
	self.XQData.buy_libao_cnt = protocol.buy_libao_cnt
	self.XQData.longhun_frag_num = protocol.longhun_frag_num
	self.XQData.longhun_hecheng_state = protocol.longhun_hecheng_state

	self.XQData.dangwei_state = protocol.dangwei_state
	self.XQData.task_list = protocol.task_list
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_LongHun)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng)
	-- print_error("FFFF+====== SetXQInfo", self.XQData)
end

function ActXianQiJieFengWGData:GetXQGiftIndex()
	if self.XQData ~= nil and self.XQData.buy_libao_cnt ~= nil then
		return self.XQData.buy_libao_cnt
	end
	return 0
end

--获取当前可以购买的礼包，全部买完展示最后一个
function ActXianQiJieFengWGData:GetCanBuyGiftData()
	local cfg = self:GetXQGiftCfg()
	if self.XQData.buy_libao_cnt < #cfg then
		return cfg[self.XQData.buy_libao_cnt + 1], false
	else
		return cfg[#cfg], true
	end
end

function ActXianQiJieFengWGData:GetDangweiScore()
	if self.XQData ~= nil then
		return self.XQData.dangwei_jifen
	end
	return 0
end

function ActXianQiJieFengWGData:GetXianQiTaskParamNum(index)
	index = index or 0
	index = index + 1

	local task_list = self.XQData and self.XQData.task_list or {}
	if task_list[index] then
		return task_list[index].task_param or 0
	end

	return 0
end

--获取最小未领取的阶段奖励
function ActXianQiJieFengWGData:GetMinBXReward()
	if self.XQData ~= nil and self.XQData.dangwei_state ~= nil then
		local reward_cfg = self:GetXQRewardCfg()
		for i=1,#reward_cfg do
			if self.XQData.dangwei_state[i] ~= nil and self.XQData.dangwei_state[i] ~=2 then
				return i
			end
		end
	end
	return 0
end

function ActXianQiJieFengWGData:IsLastBX(id)
	local reward_cfg = self:GetXQRewardCfg()
	if reward_cfg ~= nil then
		return id == #reward_cfg
	end
	return false
end

function ActXianQiJieFengWGData:IsNeedScroll(box_index)
	local reward_cfg = self:GetXQRewardCfg()
	if reward_cfg ~= nil then
		return (#reward_cfg - box_index) >= 5
	end
	return false
end

function ActXianQiJieFengWGData:GetBXState(id)
	if self.XQData ~= nil and self.XQData.dangwei_state ~= nil then
		return self.XQData.dangwei_state[id]
	end
	return 0
end

function ActXianQiJieFengWGData:GetBXTargetScore(id)
	local reward_cfg = self:GetXQRewardCfg()
	if reward_cfg ~= nil and reward_cfg[id] ~= nil then
		return reward_cfg[id].jifen
	end
	return 0
end

function ActXianQiJieFengWGData:GetXQSuiPianCfg()
	local scompose_cfg = ConfigManager.Instance:GetAutoConfig("compose_auto").compose_list
	local other_cfg = self:GetActivityThemeOtherCfg()
	if other_cfg ~= nil and scompose_cfg ~= nil then
		return scompose_cfg[other_cfg[1].lh_frag_hecheng_seq], other_cfg[1]
	end
	return nil
end

-------------------------------------- 刑天来袭（降临） --------------------------------------
function ActXianQiJieFengWGData:GetJiangLinRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi2_cfg_auto").reward_config
end

function ActXianQiJieFengWGData:GetJiangLinFlushTimeCfg()
	return ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi2_cfg_auto").refresh_time
end

function ActXianQiJieFengWGData:GetJiangLinOtherCfg(key)
	if key then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi2_cfg_auto").other
		if other_cfg and other_cfg[1] then
			return other_cfg[1][key]
		end
	end
	return ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi2_cfg_auto").other
end

function ActXianQiJieFengWGData:GetJiangLinMonster_cfg()
	return ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi2_cfg_auto").monster_refresh_cfg
end

function ActXianQiJieFengWGData:GetJingLinData()
	return self.JiangLinData
end

function ActXianQiJieFengWGData:SetXTLXInfo(protocol)
	if self.JiangLinData == nil then
		self.JiangLinData = {}
	end

	self.JiangLinData.world_level = protocol.world_lv
	self.JiangLinData.pass_times = protocol.pass_times
	self.JiangLinData.day_pass_times = protocol.day_pass_times
end

--刷新刑天来袭刷新数据
function ActXianQiJieFengWGData:SetXTLXFlushInfo(protocol)
	if self.JiangLinData == nil then
		self.JiangLinData = {}
	end

	self.JiangLinData.next_change_time = protocol.next_change_time
    self.JiangLinData.cur_status = protocol.cur_status
    self.JiangLinData.refresh_times = protocol.refresh_times
	self.JiangLinData.is_final_boss_skill = protocol.is_final_boss_skill
end

function ActXianQiJieFengWGData:SetXTLXFinishInfo(protocol)
	local finish_info = {}
	local reward_list = {}
	local partipate_list = protocol.partipate_list
	for i=1,#partipate_list do
		if partipate_list[i].item_id > 0 then
			reward_list[#reward_list + 1] = partipate_list[i]
		end
	end

	finish_info.partipate_list = reward_list
	finish_info.fall_item_list = protocol.fall_item_list

	self.xtlx_finish_info = finish_info
end

function ActXianQiJieFengWGData:GetXTLXFinishInfo()
	return self.xtlx_finish_info
end

function ActXianQiJieFengWGData:GetIsFinalBossSkill()
	return self.JiangLinData.is_final_boss_skill
end

function ActXianQiJieFengWGData:GetQuanMinBeiZhanIsClose()
	if self.JiangLinData and self.JiangLinData.is_final_boss_skill then
		return self.JiangLinData.is_final_boss_skill ~= 0
	end

	return true
end

function ActXianQiJieFengWGData:GetDayPassTimes()
	if self.JiangLinData ~= nil and self.JiangLinData.day_pass_times ~= nil then
		return self.JiangLinData.day_pass_times
	end
	return 0
end

function ActXianQiJieFengWGData:GetJiangLinReward()
	local cfg = nil
	if self.JiangLinData and self.JiangLinData.world_level then
		local reward_cfg = self:GetJiangLinRewardCfg()
		for k,v in pairs(reward_cfg) do
			if self.JiangLinData.world_level >= v.min_lv and self.JiangLinData.world_level <= v.max_lv and self.JiangLinData.day_pass_times >= v.do_times_limit then
				cfg = v --取最后一个符合的数据
			end
		end
	end
	return cfg
end

function ActXianQiJieFengWGData:XingTianLaiXiActivityState(state, next_time)
	if self.JiangLinData == nil then
		self.JiangLinData = {}
	end
	self.JiangLinData.activity_state = state
	self.JiangLinData.next_time = next_time

	if self:IsInXingTianLaiXiActivity() then
		ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
	end
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_LaiXi)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng)
end

--判断是否在活动开启时间内
function ActXianQiJieFengWGData:IsInXingTianLaiXiActivity()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG)
	if act_cfg then
		local role_level = RoleWGData.Instance:GetAttr("level")
		if role_level < act_cfg.level or role_level > act_cfg.level_max then
			return false
		end
	end

	if self.JiangLinData ~= nil and self.JiangLinData.activity_state ~= nil then
		if self.JiangLinData.activity_state == ACTIVITY_STATUS.OPEN then
			return true
		end
	end
	return false
end

function ActXianQiJieFengWGData:GetXTLXOpenTime()
	if self.time_pramstr == nil then
		self.time_pramstr = {}
		local cfg = self:GetJiangLinFlushTimeCfg()
		local index = 1
		if cfg ~= nil then
			for k,v in pairs(cfg) do
				local date = {}
				local arr = {}
				for i=1,4 do
					arr[i] = tonumber(string.sub(v.refresh_time,i,i))
				end
				date.hour = arr[1] * 10 + arr[2]
				date.min = arr[3] * 10 + arr[4]
				table.insert(self.time_pramstr, date)
			end
		end
	end

	return self.time_pramstr
end

--------------------------- 全民捐献 --------------------------------------

function ActXianQiJieFengWGData:GetQFJXDayRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").qfjx_server_day_reward
end

function ActXianQiJieFengWGData:GetQFRewardTypeCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").qfjx_reward_type
end

function ActXianQiJieFengWGData:GetQFRoleRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").qfjx_role_lj_reward
end

function ActXianQiJieFengWGData:GetQFRewardTypeCfgForIndex(index)
	local cfg = self:GetQFRewardTypeCfg()
	for i,v in ipairs(cfg) do
		if v.type == index then
			return v
		end
	end
	return nil
end

function ActXianQiJieFengWGData:GetQFJXDayRewardCfgMaxCount()
	local cfg = self:GetQFJXDayRewardCfg()
	if cfg ~= nil then
		return #cfg
	end
	return 0
end

function ActXianQiJieFengWGData:SetQuanFuJuanXianInfo(protocol)
	if self.JuanXianData == nil then
		self.JuanXianData = {}
	end
	self.JuanXianData.quanfu_juanxian_num = protocol.quanfu_juanxian_num        --全服捐献次数
	self.JuanXianData.role_juanxian_num = protocol.role_juanxian_num			--个人捐献次数
	self.JuanXianData.server_reward_state_list = protocol.server_reward_state_list
	self.JuanXianData.role_reward_state_list = protocol.role_reward_state_list
	self.JuanXianData.server_reward_time_list = protocol.server_reward_time_list

	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_JuanXian)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng)
end

--获取全服礼包奖励状态
function ActXianQiJieFengWGData:GetGiftState(index)
	if self.JuanXianData ~= nil and self.JuanXianData.server_reward_state_list ~= nil then
		return self.JuanXianData.server_reward_state_list[index]
	end
	return 0
end

--获取有多少个激活
function ActXianQiJieFengWGData:GetActiveNum()
	local max_count = #self:GetQFJXDayRewardCfg()
	if max_count > 0 then
		local active_num = 0
		if self.JuanXianData ~= nil and self.JuanXianData.server_reward_state_list ~= nil then
			for i,v in ipairs(self.JuanXianData.server_reward_state_list) do
				if v > 0 then
					active_num = active_num + 1
				end
			end
		end
		return active_num, max_count
	end

	return 0, 1
end

--获取全服奖励生效时间
function ActXianQiJieFengWGData:GetRewardTime(index)
	if self.JuanXianData ~= nil and self.JuanXianData.server_reward_time_list ~= nil then
		return self.JuanXianData.server_reward_time_list[index]
	end
	return 0
end

--个人奖励状态
function ActXianQiJieFengWGData:GetRoleRewardState(index)
	if self.JuanXianData ~= nil and self.JuanXianData.role_reward_state_list ~= nil then
		return self.JuanXianData.role_reward_state_list[index]
	end
	return 0
end

--获取全服捐献次数和个人捐献次数
function ActXianQiJieFengWGData:GetServerJuanXianCount()
	if self.JuanXianData ~= nil then
		return self.JuanXianData.quanfu_juanxian_num
	end
	return 0
end

--获取全服捐献次数和个人捐献次数
function ActXianQiJieFengWGData:GetRoleJuanXianCount()
	if self.JuanXianData ~= nil then
		return self.JuanXianData.role_juanxian_num
	end
	return 0
end

--获取当前节点对应的百分比
function ActXianQiJieFengWGData:IsActiveServerJXReward(index)
	local cfg = self:GetQFJXDayRewardCfg()
	local server_count = self:GetServerJuanXianCount()
	local need_count = 0
	local cur_count = 0
	if index == 1 then
		need_count = cfg[index].server_day_juanxian_num
		cur_count = server_count
	else
		need_count = cfg[index].server_day_juanxian_num - cfg[index-1].server_day_juanxian_num
		cur_count = server_count - cfg[index-1].server_day_juanxian_num
	end

	if cur_count > 0 then
		local rate = cur_count / need_count
		return rate > 1 and 1 or rate
	else
		return 0
	end
end
--------------------------------------------------------------

--------------------------- 备战好礼 ----------------------------------
function ActXianQiJieFengWGData:GetHaoLiCfg( ... )
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").beizhanhaoli
end

function ActXianQiJieFengWGData:SetBeiZhanHaoLiInfo(protocol)
	--print_error("----------------SetBeiZhanHaoLiInfo ------------------")
	if self.HaoLiData == nil then
		self.HaoLiData = {}
	end
	self.old_cur_day = self.HaoLiData.cur_day
	self.HaoLiData.cur_day = protocol.cur_day
	self.HaoLiData.buy_limit_type = protocol.buy_limit_type
	self.HaoLiData.libao_state = protocol.libao_statu
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_HaoLi)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng)
end

--获取页签主题
function ActXianQiJieFengWGData:GetHaoLiTabName()
	local cfg = self:GetHaoLiCfg()
	local tab_name_list = {}
	if cfg ~= nil then
		for i,v in ipairs(cfg) do
			if tab_name_list[v.theme] == nil then
				if self.HaoLiData ~= nil and v.theme <= self.HaoLiData.cur_day then
					tab_name_list[v.theme] = v.tab_name
				else
					tab_name_list[v.theme] = "????"
				end
			end
		end
	end
	return tab_name_list
end

function ActXianQiJieFengWGData:GetHaoLiDayNum()
	if self.HaoLiData ~= nil and self.HaoLiData.cur_day ~= nil then
		return self.HaoLiData.cur_day
	end
	return 0
end

function ActXianQiJieFengWGData:GetHaoLiOldCurDay()
	if self.old_cur_day ~= nil then
		return self.old_cur_day
	end
end

function ActXianQiJieFengWGData:GetHaoLigift(index)
	local cfg = self:GetHaoLiCfg()
	local list = {}
	if cfg ~= nil then
		for i,v in ipairs(cfg) do
			if v.theme == index then
				table.insert(list, v)
			end
		end
	end
	return list
end

function ActXianQiJieFengWGData:GetThemeCount()
	local cfg = self:GetHaoLiCfg()
	local list = {}
	local index = 0
	if cfg ~= nil then
		for i,v in ipairs(cfg) do
			if list[v.theme] == nil then
				list[v.theme] = 1
				index = index + 1
			end
		end
	end
	return index
end

--获取礼包购买数量
function ActXianQiJieFengWGData:GetBeiZanHaoliBuyCount(theme, gift_index)
	if self.HaoLiData ~= nil and self.HaoLiData.buy_limit_type then
		if self.HaoLiData.buy_limit_type[theme] ~= nil then
			if self.HaoLiData.buy_limit_type[theme][gift_index] ~= nil then
				return self.HaoLiData.buy_limit_type[theme][gift_index]
			end
		end
	end
	return 0
end

--获取可以购买的可购买的礼包页签
function ActXianQiJieFengWGData:GetCanBuyGiftTabIndex()
	local cfg = self:GetHaoLiCfg()
	local has_buy_times = 0
	if cfg ~= nil and self.HaoLiData ~= nil and self.HaoLiData.cur_day ~= nil then
		for i,v in ipairs(cfg) do
			if v.theme <= self.HaoLiData.cur_day then
				has_buy_times = self:GetBeiZanHaoliBuyCount(v.theme, v.gift_index)
				if has_buy_times < v.role_buy_limit then
					return TabIndex.xianqi_jiefeng_haoli + v.theme - 1
				end
			end
		end
	end
	return TabIndex.xianqi_jiefeng_haoli
end

-------------------------------------------战力比拼----------------------------------
function ActXianQiJieFengWGData:GetRoleCapRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").zlbp_role_reward
end

function ActXianQiJieFengWGData:GetServerCapRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").zlbp_server_reward
end

function ActXianQiJieFengWGData:GetKanJiaRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").zlbp_zhanli_dali
end

function ActXianQiJieFengWGData:GetBuffCfg()
	return ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").zlbp_ljzb_buffer
end

function ActXianQiJieFengWGData:SetZhanLiBiPinInfo(protocol)
	if self.CapData == nil then
		self.CapData = {}
	end
	self.CapData.server_cap = protocol.server_cap
	self.CapData.role_reward_state = protocol.role_reward_state
	self.CapData.server_reward_state = protocol.server_reward_state
	self.CapData.kanjia_reward_state = protocol.kanjia_reward_state

	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_Cap)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_Cap2)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng)
end

--获取全服战力
function ActXianQiJieFengWGData:GetServerCap()
	if self.CapData ~= nil and self.CapData.server_cap ~= nil then
		return self.CapData.server_cap
	end
	return 0
end

--获取个人战力奖励领取状态
function ActXianQiJieFengWGData:GetRoleCapRewardState(index)
	if self.CapData ~= nil then
		if self.CapData.role_reward_state ~= nil and self.CapData.role_reward_state[index] then
			return self.CapData.role_reward_state[index]
		end
	end
	return 0
end

--获取全服战力奖励领取状态
function ActXianQiJieFengWGData:GetServerCapRewardState(index)
	if self.CapData ~= nil then
		if self.CapData.server_reward_state ~= nil and self.CapData.server_reward_state[index] then
			return self.CapData.server_reward_state[index]
		end
	end
	return 0
end

--获取砍价奖励领取状态
function ActXianQiJieFengWGData:GetKanjiaRewardState(index)
	if self.CapData ~= nil then
		if self.CapData.kanjia_reward_state ~= nil and self.CapData.kanjia_reward_state[index] then
			return self.CapData.kanjia_reward_state[index]
		end
	end
	return 0
end

function ActXianQiJieFengWGData:GetKanjiaRewardStateByItemID(item_id)
	local cfg_list = self:GetKanJiaRewardCfg()
	if cfg_list then
		for i=1,#cfg_list do
			if cfg_list[i].libao_item_id == item_id then
				return self:GetKanjiaRewardState(i)
			end
		end
	end
	return 0
end

--获取个人战力奖励列表
function ActXianQiJieFengWGData:GetRoleCapList()
	local list = {}
	local cfg = self:GetRoleCapRewardCfg()
	for i,v in ipairs(cfg) do
		local item = {}
		item.cfg = v
		item.reward_id = v.reward_id
		item.state = self:GetRoleCapRewardState(v.reward_id)
		if item.state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.state == ActivityRewardState.BKL then
			item.sort = 1
		else
			item.sort = 2
		end
		table.insert(list, item)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort","reward_id"))
	return list
end

--获取全服战力奖励列表
function ActXianQiJieFengWGData:GetServerCapList()
	local list = {}
	local cfg = self:GetServerCapRewardCfg()
	for i,v in ipairs(cfg) do
		local item = {}
		item.cfg = v
		item.reward_id = v.reward_id
		item.state = self:GetServerCapRewardState(v.reward_id)
		if item.state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.state == ActivityRewardState.BKL then
			item.sort = 1
		else
			item.sort = 2
		end
		table.insert(list, item)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort","reward_id"))
	return list
end

--获取砍价列表
function ActXianQiJieFengWGData:GetKanJiaList()
	local list = {}
	local cfg = self:GetKanJiaRewardCfg()
	for i,v in ipairs(cfg) do
		local item = {}
		item.cfg = v
		item.index = i
		item.state = self:GetKanjiaRewardState(i)
		item.zhekou = BossAssistWGData.Instance:GetMainRoleGiftZheKou(v.libao_item_id)
		if item.state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.state == ActivityRewardState.BKL then
			item.sort = 1
		else
			item.sort = 2
		end
		table.insert(list, item)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort","index"))
	return list
end

--获取当前buff激活等级
function ActXianQiJieFengWGData:GetBuffLevel()
	local role_cap = RoleWGData.Instance:GetMainRoleCap()
	local buff_cfg = self:GetBuffCfg()
	for i,v in ipairs(buff_cfg) do
		if role_cap <= v.zhanli_need then
			return v.level
		end
	end
	return 0
end

function ActXianQiJieFengWGData:IsBuffFullLv(level)
	return level == #self:GetBuffCfg()
end

--获取当前礼包折扣
function ActXianQiJieFengWGData:GetGiftZheKou(gift_id)
	-- body
end

function ActXianQiJieFengWGData:SetGiftKanJiaDownTime(gift_id, end_time)
	if self.CapData == nil then
		self.CapData = {}
	end
	if self.CapData.DownTimeList == nil then
		self.CapData.DownTimeList = {}
	end
	self.CapData.DownTimeList[gift_id] = end_time
end

function ActXianQiJieFengWGData:GetGiftKanJiaDownTime(gift_id)
	if self.CapData ~= nil and self.CapData.DownTimeList ~= nil then
		return self.CapData.DownTimeList[gift_id]
	end
	return nil
end
----------------------------------------------------------
--获取活动是否开启
function ActXianQiJieFengWGData:GetActivityState(tab_index)
	local activity_type = 0
	if tab_index == TabIndex.xianqi_jiefeng_login then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DENGLUYOULI
	elseif tab_index == TabIndex.xianqi_jiefeng_shouchong then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_MEIRISHOUCHONG
	elseif tab_index == TabIndex.xianqi_jiefeng_leichong then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LEICHONGHAOLI
	elseif tab_index == TabIndex.xianqi_jiefeng_duobei then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DUOBEILAIXI
	elseif tab_index == TabIndex.xianqi_jiefeng_longhun then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_WOYAOLONGHUN
	elseif tab_index == TabIndex.xianqi_jiefeng_juanxian then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_QUANFUJUANXIAN
	elseif tab_index == TabIndex.xianqi_jiefeng_cap then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_ZHANLIBIPIN
	elseif tab_index == TabIndex.xianqi_jiefeng_haoli then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_BEIZHANHAOLI
	elseif tab_index == TabIndex.xianqi_jiefeng_laixi then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI
	elseif tab_index == TabIndex.xianqi_jiefeng_turntable then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_TURNTABLE
	elseif tab_index == TabIndex.xianqi_jiefeng_longhun_rank then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LONGHUNRANK--仙器冲榜
	end
	return ActivityWGData.Instance:GetActivityIsOpen(activity_type)
end

--获取活动的结束时间
function ActXianQiJieFengWGData:GetActivityInValidTime(tab_index)
	local activity_type = 0
	if tab_index == TabIndex.xianqi_jiefeng_login then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DENGLUYOULI
	elseif tab_index == TabIndex.xianqi_jiefeng_shouchong then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_MEIRISHOUCHONG
	elseif tab_index == TabIndex.xianqi_jiefeng_leichong then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LEICHONGHAOLI
	elseif tab_index == TabIndex.xianqi_jiefeng_duobei then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DUOBEILAIXI
	elseif tab_index == TabIndex.xianqi_jiefeng_longhun then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_WOYAOLONGHUN
	elseif tab_index == TabIndex.xianqi_jiefeng_juanxian then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_QUANFUJUANXIAN
	elseif tab_index == TabIndex.xianqi_jiefeng_cap then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_ZHANLIBIPIN
	elseif tab_index == TabIndex.xianqi_jiefeng_haoli then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_BEIZHANHAOLI
	elseif tab_index == TabIndex.xianqi_jiefeng_laixi then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI
	elseif tab_index == TabIndex.xianqi_jiefeng_longhun_rank then
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LONGHUNRANK--仙器冲榜
	else
		activity_type = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DALIAN
	end

	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(activity_type)
	if activity_data ~= nil then
		return activity_data.end_time, activity_data.start_time
	end
	return  0
end

function ActXianQiJieFengWGData:GetRemindNameMap()
	if self.remind_name_list == nil then
		self.remind_name_list = {}
		self.remind_name_list[10] = RemindName.XianQiJieFeng_Login
		self.remind_name_list[20] = RemindName.XianQiJieFeng_ShouChong
		self.remind_name_list[30] = RemindName.XianQiJieFeng_LeiChong
		self.remind_name_list[40] = RemindName.XianQiJieFeng_DuoBei
		self.remind_name_list[50] = RemindName.XianQiJieFeng_LongHun
		self.remind_name_list[60] = RemindName.XianQiJieFeng_JuanXian
		self.remind_name_list[71] = RemindName.XianQiJieFeng_Cap
		self.remind_name_list[72] = RemindName.XianQiJieFeng_Cap2
		self.remind_name_list[73] = RemindName.XianQiJieFeng_Cap3
		self.remind_name_list[81] = RemindName.XianQiJieFeng_HaoLi
		self.remind_name_list[82] = RemindName.XianQiJieFeng_HaoLi2
		self.remind_name_list[83] = RemindName.XianQiJieFeng_HaoLi3
		self.remind_name_list[84] = RemindName.XianQiJieFeng_HaoLi4
		self.remind_name_list[90] = RemindName.XianQiJieFeng_LaiXi
		self.remind_name_list[100] = RemindName.XianQiJieFeng_Turntable
		self.remind_name_list[110] = RemindName.XianQiJieFeng_LongHunRank--仙器冲榜红点
	end

	return self.remind_name_list
end

--获取活动的奖励数据
function ActXianQiJieFengWGData:GetActivityRewardState(tab_index)
	local state = 0
	if tab_index == TabIndex.xianqi_jiefeng_login then
		state = self:IsShowLoginRedPoint()
	elseif tab_index == TabIndex.xianqi_jiefeng_shouchong then
	elseif tab_index == TabIndex.xianqi_jiefeng_leichong then
	elseif tab_index == TabIndex.xianqi_jiefeng_duobei then
	elseif tab_index == TabIndex.xianqi_jiefeng_longhun then
		state = self:IsShowLongHunRedPoint()
	elseif tab_index == TabIndex.xianqi_jiefeng_juanxian then
		state = self:IsShowJuanXianRedPoint()
	elseif tab_index == TabIndex.xianqi_jiefeng_cap then
		state = self:IsShowRoleCapRedPoint() + self:IsShowServerCapRedPoint()
	elseif tab_index == TabIndex.xianqi_jiefeng_haoli then
		state = self:IsShowHaoLiRedPoint()
	elseif tab_index == TabIndex.xianqi_jiefeng_laixi then
		state = self:IsShowLaiXiRedPoint()
	elseif tab_index == TabIndex.xianqi_jiefeng_turntable then
		-- state = XQJFTurnTableData.Instance:IsShowTurnTableRed()
	end
	return  state
end

function ActXianQiJieFengWGData:IsActivityLastDay(tab_index)
	local end_time = self:GetActivityInValidTime(tab_index)
	local end_date = os.date("*t", end_time)
	local cur_date = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	if cur_date.month == end_date.month and cur_date.day == end_date.day then
		return true
	end
	return false
end

------------------------- 各活动红点数据-------------------------
-- 主界面红点提示
function ActXianQiJieFengWGData:IsShowMainViewRedPoint()
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLoginRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowShouChongRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLeiChongRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLaiXiRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowHaoLiRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowJuanXianRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLongHunRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowRoleCapRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowServerCapRedPoint() then
		return 1
	end

	-- if ShowRedPoint.SHOW_RED_POINT == XQJFTurnTableData.Instance:IsShowTurnTableRed() then
	-- 	return 1
	-- end

	return 0
end

function ActXianQiJieFengWGData:IsShowLoginRedPoint()
	if self.LoginDayData ~= nil and self.LoginDayData.day_list ~= nil then
		for k,v in ipairs(self.LoginDayData.day_list) do
			if v.common_gift_state == ActivityRewardState.KLQ or v.special_gift_state == ActivityRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end

function ActXianQiJieFengWGData:IsShowShouChongRedPoint()
	if self.ShouChongData ~= nil then
		if self.ShouChongData.gift_state == ActivityRewardState.KLQ then
			return 1
		end
	end
	return 0
end

function ActXianQiJieFengWGData:IsShowLeiChongRedPoint()
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil then
		for k,v in ipairs(self.LeiChongData.dangwei_info) do
			if v == ActivityRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end

function ActXianQiJieFengWGData:IsShowLaiXiRedPoint()
	if self.has_show_laixi_red_point == nil then
		if self:IsInXingTianLaiXiActivity() then
			return 1
		end
	end
	return 0
end

function ActXianQiJieFengWGData:SetLaiXiRedPoint()
	self.has_show_laixi_red_point = true
end

function ActXianQiJieFengWGData:IsShowHaoLiRedPoint()
	-- if self.HaoLiData ~= nil and self.HaoLiData.libao_state ~= nil then
	-- 	if self.HaoLiData.libao_state ~= ActivityRewardState.YLQ then
	-- 		return 1
	-- 	end
	-- end
	return 0
end

--全民捐献红点
function ActXianQiJieFengWGData:IsShowJuanXianRedPoint()
	if self.JuanXianData ~= nil then
		local cfg = self:GetQFJXDayRewardCfg()
		if self.JuanXianData.server_reward_state_list ~= nil then
			for k,v in ipairs(self.JuanXianData.server_reward_state_list) do
				if cfg[k] ~= nil and cfg[k].reward_type == 2 then  --只有礼包需要领取
					if v == ActivityRewardState.KLQ then
						return 1
					end
				end
			end
		end

		if self.JuanXianData.role_reward_state_list ~= nil then
			for k,v in ipairs(self.JuanXianData.role_reward_state_list) do
				if v == ActivityRewardState.KLQ then
					return 1
				end
			end
		end

		local other_cfg = self:GetActivityThemeOtherCfg()
		if other_cfg then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].juanxian_stuff_item_id)
			if item_num > 0 then
				return 1
			end
		end
	end
	return 0
end

--龙魂红点
function ActXianQiJieFengWGData:IsShowLongHunRedPoint()
	if self.XQData ~= nil and self.XQData.dangwei_state ~= nil and self.XQData.task_list ~= nil then
		for i,v in ipairs(self.XQData.dangwei_state) do
			if v == ActivityRewardState.KLQ then
				return 1
			end
		end

		for i,v in ipairs(self.XQData.task_list) do
			if v.gift_state == ActivityRewardState.KLQ then
				return 1
			end
		end

		--屏蔽合成红点
		-- local lhsp_cfg = self:GetXQSuiPianCfg()
		-- if 	lhsp_cfg ~= nil then
		-- 	local item_num = ItemWGData.Instance:GetItemNumInBagById(lhsp_cfg.stuff_id_1)
		-- 	if item_num >= lhsp_cfg.stuff_count_1 then
		--         return 1
		--     end
		-- end

	end
	return 0
end

--个人战力
function ActXianQiJieFengWGData:IsShowRoleCapRedPoint()
	if self.CapData ~= nil then
		if self.CapData.role_reward_state ~= nil then
			for k,v in ipairs(self.CapData.role_reward_state) do
				if v == ActivityRewardState.KLQ then
					return 1
				end
			end
		end
	end
	return 0
end

--全服战力
function ActXianQiJieFengWGData:IsShowServerCapRedPoint()
	-- if self.CapData ~= nil then
	-- 	if self.CapData.server_reward_state ~= nil then
	-- 		for k,v in ipairs(self.CapData.server_reward_state) do
	-- 			if v == ActivityRewardState.KLQ then
	-- 				return 1
	-- 			end
	-- 		end
	-- 	end
	-- end
	return 0
end

function ActXianQiJieFengWGData:CalcIrregularProgress(value)
	local cur_progress = 0
	local progress_list = {1/5, 2/5, 3/5, 4/5, 1}
	local max_jifen = self:GetMaxJiFen()
	for k,v in pairs(progress_list) do
		local cur_need = self:GetBXTargetScore(k)
		local next_cfg = self:GetBXTargetCfg(k + 1)
		local next_need = 0
		if IsEmptyTable(next_cfg) then
			next_need = max_jifen
		else
			next_need = next_cfg.jifen
		end
		local cur_value = progress_list[k] and progress_list[k] or 0
		local next_value = progress_list[k + 1] and progress_list[k + 1] or progress_list[#progress_list]

		if value >= cur_need and value <= next_need then
			cur_progress = (value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif value < cur_need then
			cur_progress =  value * cur_value / cur_need
			break
		elseif value >= max_jifen then
			cur_progress = progress_list[#progress_list]
			break
		end
	end
	return cur_progress
end

function ActXianQiJieFengWGData:GetMaxJiFen()
	local max_jifen = 0
	local cfg = self:GetXQRewardCfg()
	for k,v in pairs(cfg) do
		if v.jifen > max_jifen then
			max_jifen = v.jifen
		end
	end
	return max_jifen
end

function ActXianQiJieFengWGData:GetBXTargetCfg(id)
	local reward_cfg = self:GetXQRewardCfg()
	if reward_cfg ~= nil and reward_cfg[id] ~= nil then
		return reward_cfg[id]
	end
	return {}
end

--活动按钮开启
function ActXianQiJieFengWGData:OpenActivityIsOpen()
	local role_level = RoleWGData.Instance.role_vo.level
	local cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG)
	if cfg and cfg.level <= role_level then
		return true
	end

	return false
end

--仙器冲榜------- start -----------
--ActXianQiJieFengWGData.Instance:InitLongHunRankCfgList()
function ActXianQiJieFengWGData:InitLongHunRankCfgList()
	self.longhun_rank_cfg_list = {}
	local rank_cfgs = self.long_hun_chong_bang_rank_cfgs
	-- print_error("rank_cfgs ", rank_cfgs)
	if not IsEmptyTable(rank_cfgs) then
		for _, v in pairs(rank_cfgs) do
			if not IsEmptyTable(v) then
				local info = {
					id = -1,
					rank = v.rank,
					zhanli_cond = -1,
					min_zhanli = v.min_zhanli,
					reward_item = v.reward_item,
					condition_desc = v.condition_desc,
					rank_range = v.rank_range,
				}
				self.longhun_rank_cfg_list[#self.longhun_rank_cfg_list + 1] = info
			end
		end
	end

	local award_cfgs = self.long_hun_chong_bang_award_cfgs
	-- print_error("award_cfgs ", award_cfgs)
	if not IsEmptyTable(award_cfgs) then
		for _, v in pairs(award_cfgs) do
			if not IsEmptyTable(v) then
				local info = {
					id = v.ID,
					rank = -1,
					zhanli_cond = v.zhanli_cond,
					min_zhanli = -1,
					reward_item = v.reward_item,
					condition_desc = v.condition_desc,
					rank_range = "",
				}
				self.longhun_rank_cfg_list[#self.longhun_rank_cfg_list + 1] = info
			end
		end
	end
	table.sort(self.longhun_rank_cfg_list, SortTools.KeyLowerSorters("id", "rank"))
	-- for i, v in ipairs(self.longhun_rank_cfg_list) do
	-- 	print_error("vvvvvvvv = ", i, v.condition_desc, v)
	-- end
end

function ActXianQiJieFengWGData:GetXianQiRankCfgList()
	return self.longhun_rank_cfg_list
end

--仙器冲榜--排行榜列表信息
function ActXianQiJieFengWGData:SetLongHunRankLogInfo(protocol)
	self.longhun_rank_log_list = protocol.longhun_rank_log_list or {}
	SortTools.SortAsc(self.longhun_rank_log_list, "rand_id")
	for i, v in ipairs(self.longhun_rank_log_list) do
		v.sort_index = i
	end
	ActXianQiJieFengWGCtrl.Instance:FlushLongHunRankLogView()
end

function ActXianQiJieFengWGData:GetXianQiRankLogInfo()
	return self.longhun_rank_log_list
end

--仙器冲榜--活动相关信息
function ActXianQiJieFengWGData:SetLongHunRankRoleInfo(protocol)
	local info = protocol.longhun_rank_role_info
	self.longhun_rank_role_info = {}
	self.longhun_rank_role_info.rank = info.rank
	self.longhun_rank_role_info.zhanli = info.zhanli

	self.longhunrank_join_flag = bit:d2b(info.ra_longhunrank_join_flag)
	self.longhunrank_join_reward_flag = bit:d2b(info.ra_longhunrank_join_reward_flag)
	RemindManager.Instance:Fire(RemindName.XianQiJieFeng_LongHunRank)
	GlobalEventSystem:Fire(CHONGBANG_TIP_EVENT.ACT_CHECK,ChongBangTipWGData.ACT_TIP_TYPE.ACT,ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LONGHUNRANK)
end

function ActXianQiJieFengWGData:GetXianQiRankRoleInfo()
	return self.longhun_rank_role_info
end

--仙器冲榜 奖励达成标记
function ActXianQiJieFengWGData:GetXianQiRankJoinFlag(index)
	if not IsEmptyTable(self.longhunrank_join_flag) then
		-- print_error("FFF====111 奖励达成标记", 33 - index, index, self.longhunrank_join_flag[33 - index])
		return self.longhunrank_join_flag[33 - index] == 1
	end
	return false
end

--仙器冲榜 奖励领取标记
function ActXianQiJieFengWGData:GetXianQiRankJoinRewardFlag(index)
	if not IsEmptyTable(self.longhunrank_join_reward_flag) then
		-- print_error("FFF====222 奖励领取标记", 33 - index, index, self.longhunrank_join_reward_flag[33 - index])
		return self.longhunrank_join_reward_flag[33 - index] == 1
	end
	return false
end

function ActXianQiJieFengWGData:CheckLongHunRankRemind()
	local total_step = #self.long_hun_chong_bang_award_cfgs or 2
	for i = 1, total_step do
		local is_finish = self:GetXianQiRankJoinFlag(i)
		local is_get = self:GetXianQiRankJoinRewardFlag(i)
		if is_finish and not is_get then
			return 1, self.long_hun_chong_bang_award_cfgs[i].ID
		end
	end
	return 0
end
--仙器冲榜------- end -----------
