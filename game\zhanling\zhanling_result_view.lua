ZhanLingResultView = ZhanLingResultView or BaseClass(SafeBaseView)

function ZhanLingResultView:__init(view_name)
	self.view_name = "ZhanLingResultView"
	self.view_layer = UiLayer.Pop
	self.view_style = ViewStyle.Half
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
	self:AddViewResource(0, "uis/view/zhanling_ui_prefab", "layout_zhanling_result")
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function ZhanLingResultView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
	self:FlushActiveStatus()
end

function ZhanLingResultView:ReleaseCallBack()
	if self.noraml_reward_list then
		self.noraml_reward_list:DeleteMe()
		self.noraml_reward_list = nil
	end
	if self.special_reward_list then
		self.special_reward_list:DeleteMe()
		self.special_reward_list = nil
	end
	self:InitParam()
end

function ZhanLingResultView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "reward_info" then
			self.noraml_reward = v.reward_list
			self.special_reward = v.show_list
		end
	end
	self:RefreshView()
end

function ZhanLingResultView:InitParam()
	self.noraml_reward = {}
	self.special_reward = {}
end

function ZhanLingResultView:InitPanel()
	self.noraml_reward_list = AsyncListView.New(ItemCell, self.node_list.noraml_reward_list)
	self.special_reward_list = AsyncListView.New(ItemCell, self.node_list.special_reward_list)
end

function ZhanLingResultView:InitListener()
	XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.OnClickSureBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuyBtn, self))
end

function ZhanLingResultView:RefreshView()
	self:FlushRewardList()
	self:FlushActiveStatus()
end

function ZhanLingResultView:FlushActiveStatus()
	local is_active = ZhanLingWGData.Instance:GetIsActHighZhanLing()
	self.node_list.btn_buy:SetActive(not is_active)

	if is_active then
		RectTransform.SetAnchoredPositionXY(self.node_list.noraml_reward_list.rect, -11, -4)
		RectTransform.SetSizeDeltaXY(self.node_list.root_bg.rect, 1334, 300)
	end

	self.node_list.special_reward_list:SetActive(not is_active)
end

function ZhanLingResultView:FlushRewardList()
	local noraml_reward = self:CombineReward(self.noraml_reward)
	local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(noraml_reward)
	self.noraml_reward_list:SetDataList(reward_list)

	local is_show_reward = not IsEmptyTable(self.special_reward)
	if is_show_reward then
		local special_reward = self:CombineReward(self.special_reward)
		local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(special_reward)
		self.special_reward_list:SetDataList(reward_list)
	end

	self.node_list.special_reward_list:SetActive(is_show_reward)
	self.node_list.desc_lbl:SetActive(is_show_reward)
end

function ZhanLingResultView:CombineReward(reward_list)
	local combine_list = {}
	local item_id = 0
	for i=1,#reward_list do
		for j=0,#reward_list[i] do
			item_id = reward_list[i][j].item_id
			if not combine_list[item_id] then
				combine_list[item_id] = reward_list[i][j].num
			else
				combine_list[item_id] = combine_list[item_id] + reward_list[i][j].num
			end
		end
	end

	local temp_list = {}
	for item_id,num in pairs(combine_list) do
		temp_list[#temp_list + 1] = {item_id = item_id, num = num, is_bind = 1}
	end

	return temp_list
end

function ZhanLingResultView:OnClickSureBtn()
	self:Close()
end

function ZhanLingResultView:OnClickBuyBtn()
	self:Close()
	ZhanLingWGCtrl.Instance:OpenActZhanlingView()
end