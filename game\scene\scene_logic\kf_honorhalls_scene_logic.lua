KfHonorhallsSceneLogic = KfHonorhallsSceneLogic or BaseClass(CommonFbLogic)

function KfHonorhallsSceneLogic:__init()
	self.last_check_time = 0
end

function KfHonorhallsSceneLogic:__delete()
end

function KfHonorhallsSceneLogic:Update(now_time, elapse_time)
	CommonActivityLogic.Update(self, now_time, elapse_time)
end

function KfHonorhallsSceneLogic:Enter(old_scene_type, new_scene_type)
	local kfhh_ctrl = KuafuHonorhallWGCtrl.Instance
	kfhh_ctrl:FlushFollowView(0, "enter_scene")
	if old_scene_type == new_scene_type then
		self.is_show_enter_scene_tip = false
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
		return
    end

	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:AddMainUIFuPingChangeList(kfhh_ctrl:GetFollowView())
	ViewManager.Instance:AddMainUIRightTopChangeList(kfhh_ctrl:GetFollowView())
    kfhh_ctrl:OpenHonorhallsFollow()
    kfhh_ctrl:FlushRightCountView()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetToHonorhallRewardState(true)
	end)

	-- MainuiWGCtrl.Instance:SetTaskActive(false)
	MainuiWGCtrl.Instance:PlayTopButtonTween(false)

    local main_role = Scene.Instance:GetMainRole()
    local target_pos = Vector3(0,0,0)
    local target_dir = target_pos - main_role:GetRoot().transform.position
    local camera_type = ADJUST_CAMERA_TYPE.CAN_CHANGE
    local rotation = Quaternion.LookRotation(target_dir).eulerAngles
    Scene.Instance:ChangeCamera(camera_type, 24, rotation.y, 11)
end

function KfHonorhallsSceneLogic:Out(old_scene_type, new_scene_type)
	if old_scene_type == new_scene_type then
		return
	end

	local kfhh_ctrl = KuafuHonorhallWGCtrl.Instance
	MainuiWGCtrl.Instance:PlayTopButtonTween(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	MainuiWGCtrl.Instance:SetToHonorhallRewardState(false)

	kfhh_ctrl:SendCrossXiuluoTowerJoinReq(0)
    kfhh_ctrl:CloseHonorhallsFollow()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(kfhh_ctrl:GetFollowView())
	ViewManager.Instance:RemoveMainUIRightTopChangeList(kfhh_ctrl:GetFollowView())
	CommonFbLogic.Out(self)
end


--在进入下一层的时候 主角的头顶信息会提前刷新导致显示不全，所以主角创建完成时加个回调刷新
function KfHonorhallsSceneLogic:LogicOnMainRoleCreate()
	BaseFbLogic:FlushMainRoleDefaultDir()
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:ReloadUIName()
	end
end

-- 获取挂机打怪的敌人
function KfHonorhallsSceneLogic:GetGuajiCharacter()
	local is_need_stop = false
    local target_obj = self:GetMonster()
	if target_obj ~= nil then
        is_need_stop = true
		return target_obj, nil, is_need_stop
	end

	if target_obj == nil then
		target_obj, is_need_stop = self:GetNormalRole()
		return target_obj, nil, is_need_stop
	end
end

function KfHonorhallsSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()
		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function KfHonorhallsSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local obj = nil
	local is_stop = false
	local cur_dis = nil
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or not main_role:IsDeleted() then
		return obj, is_stop
	end

	local cur_x, cur_y = main_role:GetLogicPos()
	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if not v:IsDeleted() then
				local pos_x, pos_y = v:GetLogicPos()
				local dis = GameMath.GetDistance(cur_x, cur_y, pos_x, pos_y, false)
				if cur_dis == nil or cur_dis > dis then
					cur_dis = dis
					obj = v
				end
			end
		end
	end

	if obj ~= nil then
		is_stop = true
	end

	return obj, is_stop
end

function KfHonorhallsSceneLogic:GetGuajiPos()
    local target_x = nil
    local target_y = nil
    if target_x ~=nil and target_y ~= nil then
    	return target_x, target_y
    end

    local scene_id = Scene.Instance:GetSceneId()
	local layer_cfg = KuafuHonorhallWGData.Instance:GetLayerCfgBySceneId(scene_id)
	if layer_cfg and layer_cfg.layer >= KuafuHonorhallWGData.Instance:GetMaxLayer() then
    	GuajiWGCtrl.Instance:StopGuaji(false, true)
		GuajiWGCtrl.Instance:ResetMoveCache()
		MoveCache.SetEndType(MoveEndType.DoNothing)
    	return
    end

    local x, y = Scene.Instance:GetMainRole():GetLogicPos()
    local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()
    --其次判断是否怪物
    for k, v in pairs(obj_move_info_list) do
        local vo = v:GetVo()
        if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
            local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
            if self.x == nil and self.y == nil  then
                self.x , self.y = GuajiWGCtrl.Instance:GetGuiJiMonsterPos()
                return
           	elseif self.x ~= vo.pos_x or self.y ~= vo.pos_y then
           		target_x = vo.pos_x
                target_y = vo.pos_y
                self.x = target_x
                self.y = target_y
            	return target_x, target_y
           	end
        end
    end
end

function KfHonorhallsSceneLogic:GetRoleNameBoardText(role_vo)
	local role_kill = role_vo.special_param or 0
	local t = {}
	local role_id = RoleWGData.Instance.role_vo.role_id
	t.color = role_vo.role_id == role_id and COLOR3B.WHITE or COLOR3B.RED
	t.text = role_vo.name

	return t
end

-- 获取角色仙盟名
function KfHonorhallsSceneLogic:GetGuildNameBoardText(role_vo)
	local t = {}
	return t
end

function KfHonorhallsSceneLogic:IsRoleEnemy(target_obj, main_role)
	local status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if status and status.status == ACTIVITY_STATUS.STANDY then
		return false
	end
	return BaseSceneLogic.IsRoleEnemy(self, target_obj, main_role)
end

-- 是否是挂机打怪的敌人
function KfHonorhallsSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

function KfHonorhallsSceneLogic:GuaiJiRoleUpdate(now_time, elapse_time)
	self:SetGuaiJi(GUAI_JI_TYPE.MONSTER)
end

-- 获取挂机打怪的敌人
function KfHonorhallsSceneLogic:GetGuiJiMonsterEnemy()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local obj,dis = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
	if obj then
		return obj,dis
	else
		return Scene.Instance:SelectObjHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, SelectType.Enemy)
	end
end

function KfHonorhallsSceneLogic:OnClickHeadHandler(is_show)
	CrossServerSceneLogic.OnClickHeadHandler(self, is_show)
end

function KfHonorhallsSceneLogic:GetFbSceneMonsterCfg( monsters_list_cfg )
	for k,v in pairs(monsters_list_cfg) do
		local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v.id]
		v.name = cfg.name
		v.level = cfg.level
	end
	return monsters_list_cfg, #monsters_list_cfg
end

-- 此场景优先保证单位数量
function KfHonorhallsSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function KfHonorhallsSceneLogic:IsEnemyVisiblePriortiy()
	return true
end

-- 是否达到指定条件搜索全地图怪
function KfHonorhallsSceneLogic:ConditionScanMonster(vo)
	return true
end