CommonWindowSkillShowView = CommonWindowSkillShowView or BaseClass(CommonSkillShowView)
function CommonWindowSkillShowView:__init()
    -- self.view_layer = UiLayer.PopTop
    self.view_layer = UiLayer.Guide  -- 弹窗出来会卡在界面与模型之间
    self:SetMaskBg(true, true, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_skill_show_view")
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "skill_show_scene")
end

function CommonWindowSkillShowView:LoadCallBack()
	CommonSkillShowView.LoadCallBack(self)
	self:InitShower()
	self:InitEnemy()
end

--[[
	data = {
		skill_id = 0,
		level = 0,
		fazhen_id = -1,
		skill_halo_id = 0
		special_appearance = 0,
		tianshen_appearance_id = 0,
		not_loop_skill = true,
	}
]]
function CommonWindowSkillShowView:SetShowDataAndOpen(data)
	self.show_data = data

	if data then
		self.is_fazhen_skill = data.fazhen_id ~= nil and data.fazhen_id > -1
		self.is_halo_skill = data.skill_halo_id ~= nil and data.skill_halo_id > 0
	end

	self:Open()
end


function CommonWindowSkillShowView:OnFlush(param_t, index)
	self:InitShower()
	self:FlushSkillintro()
	self:ChangeEnemyShowPos()
end

-- 刷新技能简介
function CommonWindowSkillShowView:FlushSkillintro()
	if IsEmptyTable(self.show_data) then
		return
	end

	local skill_id = self.show_data.skill_id
	local skill_cfg, skill_name, skill_icon, skill_desc, ad_img, name, tips_str
	if self.is_fazhen_skill then
		skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(skill_id)
		local cfg = SupremeFieldsWGData.Instance:GetFootLightCfg(self.show_data.fazhen_id)
		if skill_cfg then
			skill_name = skill_cfg.skill_name
			skill_icon = skill_cfg.icon
			skill_desc = skill_cfg.skill_txt
		end

		if cfg then
			name = cfg.name
		end

		ad_img = "a2_skill_ad_" .. self.show_data.fazhen_id
		tips_str = string.format(Language.SupremeFields.Tips12, name)
	else
		skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(self.show_data.skill_id, self.show_data.level)
		if skill_cfg then
			skill_name = skill_cfg.skill_name
			local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_cfg.skill_id, self.show_data.level)
			if clien_skill_cfg then
				skill_icon = clien_skill_cfg.icon_resource
				skill_desc = clien_skill_cfg.description
			end
		end

		ad_img = "a2_skill_ad_" .. self.show_data.skill_id
		tips_str = string.format(Language.FiveElements.SkillTipStr, skill_name)
	end

	self.node_list["tips"].text.text = tips_str
	self.node_list["skill_name"].text.text = skill_name
	self.node_list["skill_des"].text.text = skill_desc

	local bundle, asset = ResPath.GetSkillIconById(skill_icon)
	self.node_list["skill_icon"].image:LoadSpriteAsync(bundle, asset, function ()
		self.node_list["skill_icon"].image:SetNativeSize()
    end)

	local bundle2, asset2 = ResPath.GetRawImagesPNG(ad_img)
	self.node_list["skill_ad"].raw_image:LoadSpriteAsync(bundle2, asset2, function ()
		self.node_list["skill_ad"].raw_image:SetNativeSize()
    end)
end