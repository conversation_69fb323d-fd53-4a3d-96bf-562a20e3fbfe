LandWarFbSceneLogic = LandWarFbSceneLogic or BaseClass(CommonFbLogic)

function LandWarFbSceneLogic:__init()
	self.update_elaspe_time = 0
end

function LandWarFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnter, self))

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.LandWarFbPersonView.PWFBName)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		--MainuiWGCtrl.Instance:SetToPositionalWaefareViewBtnState(true)
		LandWarFbPersonWGCtrl.Instance:OpenFBTaskView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	local view = LandWarFbPersonWGCtrl.Instance:GetFBTaskView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIRightTopChangeList(view)

	MainuiWGCtrl.Instance:SetMianUITargetPos(0, -50)
	self.update_elaspe_time = 0
end

function LandWarFbSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

    LandWarFbPersonWGCtrl.Instance:CloseFBTaskView()
    MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	MainuiWGCtrl.Instance:SetToPositionalWaefareViewBtnState(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	if self.role_enter_event then
		GlobalEventSystem:UnBind(self.role_enter_event)
		self.role_enter_event = nil
	end

	local view = LandWarFbPersonWGCtrl.Instance:GetFBTaskView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
	MainuiWGCtrl.Instance:SetMianUITargetPos(0, 0)
end

function LandWarFbSceneLogic:Update(now_time, elapse_time)
	BaseSceneLogic.Update(self, now_time, elapse_time)

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and main_role:IsDeleted() then
		return
	end

	if now_time > self.update_elaspe_time then
		-- 拾取
		local obj_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
		if not IsEmptyTable(obj_list) then
			local obj_id_list = {}

			for k, v in pairs(obj_list)do
				table.insert(obj_id_list, v:GetObjId())
			end

			if not IsEmptyTable(obj_id_list) then
				self.update_elaspe_time = now_time + 2
				GlobalTimerQuest:AddTimesTimer(function ()
					Scene.ScenePickItem(obj_id_list)
				end, 1, 1)
			end
		end
	end
end

function LandWarFbSceneLogic:OnRoleEnter(obj_id)

end