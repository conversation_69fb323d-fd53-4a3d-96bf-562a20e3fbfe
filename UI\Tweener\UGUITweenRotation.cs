﻿using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("UGUI/Tween/UGUI Tween Rotation")]
public class UGUITweenRotation : UGUITweener 
{
	public Vector3 from;
	public Vector3 to;
	public bool quaternionLerp = false;

	Transform mTrans;

	public Transform cachedTransform { get { if (mTrans == null) mTrans = transform; return mTrans; } }

	[System.Obsolete("Use 'value' instead")]
	public Quaternion rotation { get { return this.value; } set { this.value = value; } }

	public Quaternion value { get { return cachedTransform.localRotation; } set { cachedTransform.localRotation = value; } }

	protected override void OnUpdate (float factor, bool isFinished)
	{
		value = quaternionLerp ? Quaternion.Slerp(Quaternion.Euler(from), Quaternion.Euler(to), factor) :
			Quaternion.Euler(new Vector3(
				Mathf.Lerp(from.x, to.x, factor),
				Math<PERSON>.Lerp(from.y, to.y, factor),
				Mathf.Lerp(from.z, to.z, factor)
			));
	}
		
	static public UGUITweenRotation Begin (GameObject go, float duration, Quaternion rot)
	{
		UGUITweenRotation comp = UGUITweener.Begin<UGUITweenRotation>(go, duration);
		comp.from = comp.value.eulerAngles;
		comp.to = rot.eulerAngles;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	[ContextMenu("Set 'From' to current value")]
	public override void SetStartToCurrentValue () { from = value.eulerAngles; }

	[ContextMenu("Set 'To' to current value")]
	public override void SetEndToCurrentValue () { to = value.eulerAngles; }

	[ContextMenu("Assume value of 'From'")]
	void SetCurrentValueToStart () { value = Quaternion.Euler(from); }

	[ContextMenu("Assume value of 'To'")]
	void SetCurrentValueToEnd () { value = Quaternion.Euler(to); }

}
