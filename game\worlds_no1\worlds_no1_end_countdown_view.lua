-- 天下第一结束前倒计时面板
WorldsNO1EndCountdownView = WorldsNO1EndCountdownView or BaseClass(SafeBaseView)
function WorldsNO1EndCountdownView:__init()
    self.active_close = false
    
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self:SetMaskBg(false, false)
	self:AddViewResource(0, "uis/view/worlds_no1_ui_prefab", "layout_worlds_no1_end_countdown")
end

function WorldsNO1EndCountdownView:__delete()
end

function WorldsNO1EndCountdownView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("WorldsNO1EndCountdownView") then
		CountDownManager.Instance:RemoveCountDown("WorldsNO1EndCountdownView")
    end
    self:KillTweener()
end

function WorldsNO1EndCountdownView:LoadCallBack()
	if self.fill_tween then
        return
    end

    local jiesuan_time = WorldsNO1WGData.Instance:GetJiesuanTimestamp()
    self.total_time = jiesuan_time - TimeWGCtrl.Instance:GetServerTime()
    if self.total_time <= 0 or Scene.Instance:GetSceneType() ~= SceneType.WorldsNO1 then
        self:Close()
        return
    end

    self:KillTweener()
    self.node_list.yuanquan.image.fillAmount = 1
    self.node_list.tuowei_img.image.fillAmount = 1
    self.node_list.tuowei_img.rect.localRotation = Quaternion.Euler(0, 0, 225)
    self.fill_tween = self.node_list.yuanquan.image:DOFillAmount(0, self.total_time):OnUpdate(function()
        local value = self.node_list.yuanquan.image.fillAmount
        local rotate = - 360 * value + 225
        if rotate > 135 then
            self.node_list.tuowei_img.image.fillAmount = value * 4
        end
        self.node_list.tuowei_img.rect.localRotation = Quaternion.Euler(0, 0, - 360 * value + 225)
    end):OnComplete(function()
        self.fill_tween = nil
        self:Close()
    end)
    
	self.node_list["daojishi_text"].text.text = math.floor(self.total_time)
	CountDownManager.Instance:RemoveCountDown("WorldsNO1EndCountdownView")
	CountDownManager.Instance:AddCountDown("WorldsNO1EndCountdownView", BindTool.Bind1(self.UpdateCountDownTime, self), BindTool.Bind(self.Close, self), nil, self.total_time, 0.5)
end

function WorldsNO1EndCountdownView:UpdateCountDownTime(elapse_time, total_time)
	if not self.node_list["daojishi_text"] then
        self:Close()
		return
    end
    local last_time = math.floor(total_time - elapse_time)  
	self.node_list["daojishi_text"].text.text = last_time
    if last_time <= 0 then
        self:Close()
    end
end

function WorldsNO1EndCountdownView:OnFlush()

end

function WorldsNO1EndCountdownView:KillTweener()
    if self.fill_tween then
        self.fill_tween:Kill()
        self.fill_tween = nil
    end
end
