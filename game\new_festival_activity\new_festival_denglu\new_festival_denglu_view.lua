-- 新节日活动_登录有礼
local rotate_time = 5	--旋转动画时间
function NewFestivalActivityView:ReleaseCallBackDengLu()
	if CountDownManager.Instance:HasCountDown("dl_end_time") then
		CountDownManager.Instance:RemoveCountDown("dl_end_time")
	end

	if self.dl_dis_reward_list then
		for k, v in pairs(self.dl_dis_reward_list) do
			v:DeleteMe()
		end
		self.dl_dis_reward_list = nil
	end

	if self.tween then
        self.tween:Kill()
        self.tween = nil
    end
end

function NewFestivalActivityView:LoadIndexCallBackDengLu()
	self:LoadDengLuUi()
	if self.dl_dis_reward_list == nil then
		self.dl_dis_reward_list = {}
		for i = 1, NewFestivalDengLuWGData.SHOW_REWARD_POOL_COUNT do
			self.dl_dis_reward_list[i] = NewFesDengLuCell.New(self.node_list["dl_panel"]:FindObj("reward_pool_cell" .. i))
			self.dl_dis_reward_list[i]:SetIndex(i)
		end
	end

	-- self.node_list["dl_illustrate"].button:AddClickListener(BindTool.Bind(self.OnDLClickTip, self))
	XUI.AddClickEventListener(self.node_list.dl_raffle_btn, BindTool.Bind1(self.OnClickDLRaffle, self))
	XUI.AddClickEventListener(self.node_list["click_mask"], BindTool.Bind(self.OnClickMask, self)) --遮罩点击
	self:FlushDLEndTime()
end

function NewFestivalActivityView:FlushDLEndTime()
	local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NEW_FESACT_DENGLU)
	if CountDownManager.Instance:HasCountDown("dl_end_time") then
		CountDownManager.Instance:RemoveCountDown("dl_end_time")
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown("dl_end_time",
			BindTool.Bind(self.UpDLdateCountDown, self),
			BindTool.Bind(self.OnDLComplete, self),
			nil, time, 1)
	else
		self:OnDLComplete()
	end
end

function NewFestivalActivityView:UpDLdateCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	local dl_info_cfg = NewFestivalActivityWGData.Instance:GetNewFesActDLCfg()
	local time_part_color = dl_info_cfg and dl_info_cfg.time_part_color or COLOR3B.D_GREEN
	self.node_list["dl_activity_time"].text.text = string.format(Language.NewFestivalActivity.ActTime, time_part_color, time_str)
end

function NewFestivalActivityView:OnDLComplete()
	self.node_list.dl_activity_time.text.text = ""
	self:Close()
end

function NewFestivalActivityView:LoadDengLuUi()
	local dl_bg_bundle, dl_bg_asset = ResPath.GetNewFestivalRawImages("dl_yp")
	self.node_list["dl_bg"].raw_image:LoadSprite(dl_bg_bundle, dl_bg_asset, function ()
		self.node_list["dl_bg"].raw_image:SetNativeSize()
	end)

	local dl_title_bundle, dl_title_asset = ResPath.GetNewFestivalRawImages("dl_title")
	self.node_list["dl_title"].raw_image:LoadSprite(dl_title_bundle, dl_title_asset, function ()
		self.node_list["dl_title"].raw_image:SetNativeSize()
	end)

	local dl_illustrate_bg_bundle, dl_illustrate_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_dl_di3")
 	self.node_list["dl_illustrate_bg"].image:LoadSprite(dl_illustrate_bg_bundle, dl_illustrate_bg_asset)

	local dl_raffle_btn_bundle, dl_raffle_btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xyxc_btn_jjqy")
 	self.node_list["dl_raffle_btn"].image:LoadSprite(dl_raffle_btn_bundle, dl_raffle_btn_asset, function()
		self.node_list["dl_raffle_btn"].image:SetNativeSize()
	end)

	local dl_act_bg_bundle, dl_act_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_dl_di2")
 	self.node_list["dl_act_bg"].image:LoadSprite(dl_act_bg_bundle, dl_act_bg_asset, function()
		self.node_list["dl_act_bg"].image:SetNativeSize()
	end)

	local dl_info_cfg = NewFestivalActivityWGData.Instance:GetNewFesActDLCfg()
	self.node_list["dl_illustrate_text"].text.color = Str2C3b(dl_info_cfg.illustrate_color)
	self.node_list["dl_raffle_text"].text.color = Str2C3b(dl_info_cfg.raffle_color)
	self.node_list["dl_residue_num"].text.color = Str2C3b(dl_info_cfg.residue_color)
	self.node_list["dl_activity_time"].text.color = Str2C3b(dl_info_cfg.activity_color)
end

function NewFestivalActivityView:OnFlushDengLu()
	self:FlushDengLu()
end

function NewFestivalActivityView:FlushDengLu()
	local display_cfg = NewFestivalDengLuWGData.Instance:GetDisplaycfg()
	for k, v in ipairs(self.dl_dis_reward_list) do
		v:SetData(display_cfg[k])
	end

	local can_draw_num = NewFestivalDengLuWGData.Instance:GetCanDrawNum()
	self.node_list.dl_residue_num.text.text = string.format(Language.NewFestivalActivity.ResidueNum, can_draw_num)

	-- 抽奖按钮红点
	local is_draw = NewFestivalDengLuWGData.Instance:GetNewFestivalDLRemind()
	self.node_list.dl_remind:SetActive(is_draw ~= 0)
end

function NewFestivalActivityView:PlayAnimation()
	self.node_list["click_mask"]:SetActive(true)
	self.tween = DG.Tweening.DOTween.Sequence()
	local tween_rotate = self.node_list.parent_arrow.transform:DORotate(Vector3(0, 0, -360 * rotate_time),
	rotate_time, DG.Tweening.RotateMode.FastBeyond360)
	tween_rotate:SetEase(DG.Tweening.Ease.OutCubic)
	self.tween:Append(tween_rotate)
	self.tween:OnUpdate(function()
		local rotate_z = self.node_list.parent_arrow.transform.localEulerAngles.z
		local tween_idx = self:GetCurPointIndex(rotate_z)
		for i = 1, NewFestivalDengLuWGData.SHOW_REWARD_POOL_COUNT do
			self.dl_dis_reward_list[i]:ShowHlLight(i == tween_idx)
		end
	end)
	self.tween:OnComplete(function()
		self.node_list["click_mask"]:SetActive(false)
		self.tween:Kill()
		self.tween = nil
		for i = 1, NewFestivalDengLuWGData.SHOW_REWARD_POOL_COUNT do
			self.dl_dis_reward_list[i]:ShowHlLight(false)
		end
	end)
end

function NewFestivalActivityView:GetCurPointIndex(rotate_z)
	local rotate = 360 / NewFestivalDengLuWGData.SHOW_REWARD_POOL_COUNT
	return NewFestivalDengLuWGData.SHOW_REWARD_POOL_COUNT - math.floor((rotate_z / rotate))
end

function NewFestivalActivityView:OnClickDLRaffle()
	local can_draw_num = NewFestivalDengLuWGData.Instance:GetCanDrawNum()
	if can_draw_num > 0 then
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NEW_FESACT_DENGLU, OA_NEW_FES_ACT_DENGLU_OPERATE_TYPE.DRAW)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.NewFestivalActivity.NotEnoughNum)
	end
end

function NewFestivalActivityView:OnClickMask()
	TipWGCtrl.Instance:ShowSystemMsg(Language.NewFestivalActivity.IsPlayingTrun)
end

function NewFestivalActivityView:OnDLClickTip()
	RuleTip.Instance:SetContent(Language.NewFestivalActivity.DLRuleContent, Language.NewFestivalActivity.DLRuleTitle)
end

NewFesDengLuCell = NewFesDengLuCell or BaseClass(BaseRender)
function NewFesDengLuCell:__init()
	if not self.reward_item then
		self.reward_item = ItemCell.New(self.node_list["cell_pos"])
		self.reward_item:SetCellBgEnabled(false)
		self.reward_item:SetIsUseRoundQualityBg(true)
	end
end

function NewFesDengLuCell:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function NewFesDengLuCell:OnFlush()
	if not self.data then
		return
	end

	if self.reward_item then
		self.reward_item:SetData(self.data.reward_item[0])
	end

	local is_exhaust = NewFestivalDengLuWGData.Instance:IsExhaust(self.data.seq)
	self.reward_item:SetIconGrey(is_exhaust)
	self.reward_item:SetGraphicGreyCualityBg(is_exhaust)
	self.reward_item:SetEffectRootEnable(not is_exhaust)
end

function NewFesDengLuCell:ShowHlLight(state)
	self.node_list.hl_img:SetActive(state)
end