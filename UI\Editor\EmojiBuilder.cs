﻿using UnityEngine;
using UnityEditor;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;

public class EmojiBuilder
{
    private static string rootPath = Application.dataPath.Replace("Assets", "");
    private const string OutputPath = "Assets/Game/UIs/Emoji";
    private const string InputPath = "/EmojiInput";

    private static readonly Vector2[] AtlasSize = new Vector2[]{
        new Vector2(64,64),
        new Vector2(128,128),
        new Vector2(256,256),
        new Vector2(512,512),
        new Vector2(1024,1024),
        new Vector2(2048,2048)
    };

    struct EmojiInfo
    {
        public string key;
        public string x;
        public string y;
        public string size_x;
        public string size_y;
        public string isBig;
    }
    private const int EmojiSize = 64;//the size of emoji.

    [MenuItem("自定义工具/Build Emoji")]
    public static void BuildEmoji()
    {
        Dictionary<string, EmojiData> sourceLookup = new Dictionary<string, EmojiData>();
        string[] folders = Directory.GetDirectories(Application.dataPath + InputPath);
        for (int i = 0; i < folders.Length; ++i)
        {
            string folder = folders[i];
            string key = Path.GetFileNameWithoutExtension(folder);
            if (sourceLookup.ContainsKey(key))
            {
                Debug.LogError("重复的大表情ID: " + folder);
            }
            else
            {
                EmojiData emojiData;
                if (BuildEmojiData(StandradPathToAssetPath(folder), out emojiData))
                    sourceLookup.Add(key, emojiData);
            }
        }

        if (!Directory.Exists(OutputPath))
        {
            Directory.CreateDirectory(OutputPath);
        }

        Dictionary<string, EmojiInfo> emojiDic = new Dictionary<string, EmojiInfo>();

        int totalCount = 0;
        foreach (var value in sourceLookup.Values)
        {
            totalCount += value.count;
        }
        int totalArea = totalCount * EmojiSize * EmojiSize;
        if (totalArea > 2048 * 2048)
        {
            Debug.LogError("图集大于2048！！请删除部分图片");
            return;
        }

        Vector2 texSize = ComputeAtlasSize(totalArea);
        Texture2D newTex = new Texture2D((int)texSize.x, (int)texSize.y, TextureFormat.ARGB32, false);
        ResetTexture(newTex);
        Texture2D dataTex = new Texture2D((int)texSize.x / EmojiSize, (int)texSize.y / EmojiSize, TextureFormat.ARGB32, false);
        int x = 0;
        int y = 0;
        foreach (string key in sourceLookup.Keys)
        {
            bool isBig = false;
            // 1000 - 1999之间定义为大表情
            int number;
            if (int.TryParse(key, out number))
            {
                if (number >= 1000 && number <= 1999)
                {
                    isBig = true;
                }
            }

            EmojiData emojiData = sourceLookup[key];
            for (int index = 0; index < emojiData.count; index++)
            {
                string path = emojiData.paths[index];
                Texture2D asset = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                Color[] colors = asset.GetPixels(0);
                for (int i = 0; i < asset.width; i++)
                {
                    for (int j = 0; j < asset.height; j++)
                    {
                        newTex.SetPixel(x + i, y + j, colors[i + j * asset.width]);
                    }
                }
                int count = emojiData.count - 1;
                float r = 0, g = 0, b = 0, a = 0;
                a = (count & 8) > 0 ? 0.5f : 0;
                r = (count & 4) > 0 ? 0.5f : 0;
                g = (count & 2) > 0 ? 0.5f : 0;
                b = (count & 1) > 0 ? 0.5f : 0;

                dataTex.SetPixel(x / EmojiSize, y / EmojiSize, new Color(r, g, b, a));

                if (!emojiDic.ContainsKey(key))
                {
                    EmojiInfo info;
                    info.key = "[" + key + "]";
                    info.x = (x * 1.0f / texSize.x).ToString();
                    info.y = (y * 1.0f / texSize.y).ToString();
                    info.size_x = (EmojiSize / texSize.x).ToString();
                    info.size_y = (EmojiSize / texSize.y).ToString();
                    info.isBig = isBig ? "1" : "0";
                    emojiDic.Add(key, info);
                }

                x += EmojiSize;
                if (x + EmojiSize > texSize.x)
                {
                    x = 0;
                    y += EmojiSize;
                }
            }
        }

        byte[] bytes1 = newTex.EncodeToPNG();
        string outputfile1 = Path.Combine(OutputPath, "emoji_tex.png");
        File.WriteAllBytes(outputfile1, bytes1);

        byte[] bytes2 = dataTex.EncodeToPNG();
        string outputfile2 = Path.Combine(OutputPath, "emoji_data.png");
        File.WriteAllBytes(outputfile2, bytes2);
        string textPath = Path.Combine(OutputPath, "emoji.txt");
        using (StreamWriter sw = new StreamWriter(textPath, false))
        {
            sw.WriteLine("Name\tKey\tFrames\tX\tY\tSizeX\tSizeY\tIsBig");
            foreach (string key in emojiDic.Keys)
            {
                sw.WriteLine("{" + key + "}\t" + emojiDic[key].key + "\t" + sourceLookup[key].count + "\t" + emojiDic[key].x + "\t" + emojiDic[key].y + "\t" + emojiDic[key].size_x + "\t" + emojiDic[key].size_y + "\t" + emojiDic[key].isBig);
            }
            sw.Close();
        }
        AssetDatabase.Refresh();
        FormatTexture();
        BuildMaterial((int)texSize.x / EmojiSize);
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    private static Vector2 ComputeAtlasSize(int area)
    {
        for (int i = 0; i < AtlasSize.Length; i++)
        {
            if (area <= AtlasSize[i].x * AtlasSize[i].y)
            {
                return AtlasSize[i];
            }
        }
        return Vector2.zero;
    }

    private static void FormatTexture()
    {
        TextureImporter emojiTex = AssetImporter.GetAtPath(Path.Combine(OutputPath, "emoji_tex.png")) as TextureImporter;
        emojiTex.textureType = TextureImporterType.Default;
        emojiTex.filterMode = FilterMode.Bilinear;
        emojiTex.mipmapEnabled = false;
        emojiTex.sRGBTexture = true;
        emojiTex.alphaSource = TextureImporterAlphaSource.FromInput;
        emojiTex.alphaIsTransparency = true;
        emojiTex.textureCompression = TextureImporterCompression.Uncompressed;
        emojiTex.SaveAndReimport();

        TextureImporter emojiData = AssetImporter.GetAtPath(Path.Combine(OutputPath, "emoji_data.png")) as TextureImporter;
        emojiTex.textureType = TextureImporterType.Default;
        emojiData.filterMode = FilterMode.Point;
        emojiData.mipmapEnabled = false;
        emojiData.sRGBTexture = false;
        emojiData.alphaSource = TextureImporterAlphaSource.FromInput;
        emojiData.alphaIsTransparency = false;
        emojiData.textureCompression = TextureImporterCompression.Uncompressed;
        emojiData.SaveAndReimport();
    }

    private static void BuildMaterial(int emojiSize)
    {
        string materialPath = Path.Combine(OutputPath, "UGUIEmoji.mat");
        Material material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
        if (null == material)
        {
            material = new Material(Shader.Find("UI/EmojiFont"));
            AssetDatabase.CreateAsset(material, materialPath);
        }
        Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(Path.Combine(OutputPath, "emoji_tex.png"));
        Texture2D textureData = AssetDatabase.LoadAssetAtPath<Texture2D>(Path.Combine(OutputPath, "emoji_data.png"));
        material.SetTexture("_EmojiTex", texture);
        material.SetTexture("_EmojiDataTex", textureData);
        material.SetInt("_EmojiSize", emojiSize);
    }

    private static bool BuildEmojiData(string floderPath, out EmojiData emojiData)
    {
        emojiData = new EmojiData();
        string[] guids = AssetDatabase.FindAssets("t:Texture", new string[] { floderPath });
        if (guids.Length > 16)
        {
            Debug.LogError("emoji不能超过16张图片！" + floderPath);
            return false;
        }
        string[] paths = new string[guids.Length];
        for (int i = 0; i < guids.Length; i++)
        {
            var guid = guids[i];
            var path = AssetDatabase.GUIDToAssetPath(guid);
            var texture = AssetDatabase.LoadAssetAtPath(path, typeof(Texture2D)) as Texture2D;
            string fileName = Path.GetFileNameWithoutExtension(path);
            fileName = Regex.Replace(fileName, "-", "_", RegexOptions.IgnoreCase);
            string[] splits = fileName.Split('_');

            int id;
            if (!int.TryParse(splits[splits.Length - 1], out id))
            {
                Debug.LogError("名字格式不正确！" + fileName);
                return false;
            }
            if (id > paths.Length)
            {
                Debug.LogError("Index不连续，请检查！" + fileName);
                return false;
            }
            paths[id - 1] = path;
        }
        emojiData.count = guids.Length;
        emojiData.paths = paths;
        return true;
    }

    private static void ResetTexture(Texture2D texture)
    {
        for (int x = 0; x < texture.width; ++x)
        {
            for (int y = 0; y < texture.height; ++y)
            {
                texture.SetPixel(x, y, new Color(0, 0, 0, 0));
            }
        }
    }

    private struct EmojiData
    {
        public int count;
        public string[] paths;
    }

    private static string StandradPathToAssetPath(string standradPath)
    {
        standradPath = standradPath.Replace("\\", "/");
        return standradPath.Replace(rootPath, "");
    }
}
