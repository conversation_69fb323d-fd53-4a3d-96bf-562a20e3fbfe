local KELING_OPEN_NEED_COLOR = GameEnum.ITEM_COLOR_PURPLE
local KELING_OPEN_NEED_NUM = 8

MultiFunctionWGData = MultiFunctionWGData or BaseClass()

function MultiFunctionWGData:InitDaoHangCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("taoist_cfg_auto")
	self.daohang_other_cfg = cfg.other[1]
	self.daohang_keyin_gongming_cfg = cfg.carve_gongming
	self.daohang_qianghua_gongming_cfg = cfg.qianghua_gongming
	self.daohang_equip_cfg = cfg.equip
	self.daohang_rmb_buy_cfg = cfg.rmb_buy
	self.daohang_keling_hole_cfg = cfg.hole
	self.daohang_equip_type_cfg = cfg.equip_type
	self.daohang_attr_limit_cfg = cfg.attr_limit
	self.daohang_suit_item_cfg = cfg.suit_item
	self.daohang_fashion_skill_cfg = cfg.fashion_skill

	self.daohang_equip_suit_cfg = ListToMapList(cfg.equip_suit, "need_num", "quality")
	self.daohang_keling_gongming_cfg = ListToMapList(cfg.keling_gongming, "num")
	self.daohang_suit_four_cache = self.daohang_equip_suit_cfg[4]
	self.daohang_suit_eight_cache = self.daohang_equip_suit_cfg[8]
	self.daohang_keling_gongming_four_cfg = self.daohang_keling_gongming_cfg[4]
	self.daohang_keling_gongming_eight_cfg = self.daohang_keling_gongming_cfg[8]
	self.daohang_keling_gongming_twelve_cfg = self.daohang_keling_gongming_cfg[12]
	self.daohang_compos_cfg = ListToMap(cfg.comp, "big_type", "small_type")
	self.daohang_slot_enhance_cfg = ListToMap(cfg.slot_enhance, "slot", "level")
	self.daohang_slot_advance_cfg = ListToMap(cfg.slot_advance, "slot", "order")
	self.daohang_compose_type_cfg = ListToMap(cfg.comp_type, "big_type", "small_type")
	self.daohang_keling_stone = ListToMap(cfg.keling_stone, "item_id")
	self.daohang_keling_slot_stone = ListToMap(cfg.keling_stone, "slot", "level")
	self.daohang_slot_carve_cfg = ListToMap(cfg.slot_carve, "slot", "seq", "level")
	self.daohang_kaiguang_attr_cfg = ListToMap(cfg.kaiguang_attr, "slot", "slider_index")
	self.daohang_compos_stuff_cfg = ListToMap(cfg.comp, "stuff_id2")

	self.daohang_qianghua_remind = false
	self.daohang_qianghua_remind_cache = {}
	self.daohang_qianghua_resonance_level = 0
	self.daohang_qianghua_auto_up_state = false

	self.daohang_jinjie_remind = false
	self.daohang_jinjie_remind_cache = {}
	self.daohang_jinjie_auto_up_state = false

	self.daohang_keyin_all_level = 0
	self.daohang_keyin_remind = false
	self.daohang_keyin_remind_cache = {}
	self.daohang_keyin_resonance_cache = {}

	self.daohang_kaiguang_remind = false
	self.daohang_kaiguang_remind_cache = {}
	self.daohang_kaiguang_extracost_state = false

	self.inlay_tw_gongming = 0
	self.inlay_four_gongming = 0
	self.inlay_eight_gongming = 0

	self.keling_item_list = {}
	self.keling_store_cache = {}
	self.keling_store_cal_cache = {}
	self.keling_store_remind = false
	self.daohang_keling_need_cache = 0
	self.daohang_keling_one_key_data = {}
	self.daohang_keling_gongming_remind = false
	self.daohang_keling_gongming_num_cache = {}
	self.daohang_keling_gongming_data_cache = {}

	self.daohang_bag_grid_count = 0
	self.daohang_bag_grid_list = {}
	self.daohang_bag_one_key_cache = {}

	self.daohang_compose_cache = {}
	self.daohang_compose_remind = false
	self.daohang_compose_cache_remind = {}
	self.daohang_compose_equiped_cache = {}
	
	self.m_qu_id = 0
	self.rmb_buy_level = 0
	self.rmb_buy_flag = {}
	self.touch_rmb_time = 0
	self.slot_item_list = {}
	self.daohang_suit_cache = {}
	self.daohang_suit_color_cache = {}
	self.suit_id_eight_item = -1
	self.carve_gongming_level = 0
	self.daohang_equiped_equip = {}
	self.enhance_gongming_level = 0
	self.daohang_one_key_wear_datalist = {}
	self.touch_num = 0
	self.fumo_max = 0

	self.daohang_qianghua_item_cost_cache = {}
	self.daohang_jinjie_item_cost_cache = {}
	self.daohang_keyin_item_cost_cache = {}

    RemindManager.Instance:Register(RemindName.DaoHang_Suit, BindTool.Bind(self.GetDaoHangSuitRemind, self))
	RemindManager.Instance:Register(RemindName.DaoHang_KeYin, BindTool.Bind(self.GetDaoHangKeYinRemind, self))
	RemindManager.Instance:Register(RemindName.DaoHang_KeLing, BindTool.Bind(self.GetDaoHangKeLingRemind, self))
	RemindManager.Instance:Register(RemindName.DaoHang_JinJie, BindTool.Bind(self.GetDaoHangJinJieRemind, self))
    RemindManager.Instance:Register(RemindName.DaoHang_QiangHua, BindTool.Bind(self.GetDaoHangQiangHuaRemind, self))
	RemindManager.Instance:Register(RemindName.DaoHang_KaiGuang, BindTool.Bind(self.GetDaoHangKaiGuangRemind, self))
end

function MultiFunctionWGData:DeleteDaoHangData()
    RemindManager.Instance:UnRegister(RemindName.DaoHang_Suit)
	RemindManager.Instance:UnRegister(RemindName.DaoHang_KeYin)
	RemindManager.Instance:UnRegister(RemindName.DaoHang_KeLing)
	RemindManager.Instance:UnRegister(RemindName.DaoHang_JinJie)
    RemindManager.Instance:UnRegister(RemindName.DaoHang_QiangHua)
	RemindManager.Instance:UnRegister(RemindName.DaoHang_KaiGuang)
end

----------------------------------------remind_start-------------------------------------------
function MultiFunctionWGData:GetDaoHangSuitRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.DaoHang_Suit) then
		return 0
	end

	if self:GetDaoHangOneKeyWearDataList() > 0 then
		return 1
	end

	local _, is_get, can_get = self:GetDaoHangEquipSuitShowItemData()
	if not is_get and can_get then
		return 1
	end

	if self.daohang_compose_remind then
		return 1
	end

	if self:GetDaoHangHolySealRemind() == true then
		return 1
	end

    return 0
end

function MultiFunctionWGData:GetDaoHangKeLingRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.DaoHang_KeLing) then
		return 0
	end
	
	if self:CanOpenDaoHangKeLing() then
		return 0
	end

	if self.keling_store_remind then
		return 1
	end
	
	if not IsEmptyTable(self.daohang_keling_one_key_data) then
		return 1
	end

	if self.daohang_keling_gongming_remind then
		return 1
	end

    return 0
end

function MultiFunctionWGData:GetDaoHangQiangHuaRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.DaoHang_QiangHua) then
		return 0
	end

	if self:IsDaoHangNoWearEquip() then
		return 0
	end

	if self.daohang_qianghua_remind then
		return 1
	end

	if self:GetDaoHangQiangHuaResonanceRemind() then
		return 1
	end

    return 0
end

function MultiFunctionWGData:GetDaoHangJinJieRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.DaoHang_JinJie) then
		return 0
	end

	if self:IsDaoHangNoWearEquip() then
		return 0
	end

	if self.daohang_jinjie_remind then
		return 1
	end

    return 0
end

function MultiFunctionWGData:GetDaoHangKeYinRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.DaoHang_KeYin) then
		return 0
	end

	if self:IsDaoHangNoWearEquip() then
		return 0
	end

	if self.daohang_keyin_remind then
		return 1
	end

	if self:GetDaoHangKeYinResonanceRemind() then
		return 1
	end

    return 0
end

function MultiFunctionWGData:GetDaoHangKaiGuangRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.DaoHang_KaiGuang) then
		return 0
	end

	if self:IsDaoHangNoWearEquip() then
		return 0
	end

	if self.daohang_kaiguang_remind then
		return 1
	end

    return 0
end

-----------------------------------------remind_end--------------------------------------------

------------------------------------------Common_Get_Start-------------------------------------------
function MultiFunctionWGData:IsDaoHangEquip(item_id)
	return nil ~= self.daohang_equip_cfg[item_id]
end

function MultiFunctionWGData:GetDaoHangEquipByItemId(item_id)
	return self.daohang_equip_cfg[item_id]
end

function MultiFunctionWGData:GetDaoHangEquipTypeCfgBySlot(slot)
	return self.daohang_equip_type_cfg[slot]
end

function MultiFunctionWGData:GetDaoHangEquipTypeCfg()
	return self.daohang_equip_type_cfg
end

function MultiFunctionWGData:GetDaoHangCurRmbBuyCfg()
	return self.daohang_rmb_buy_cfg[self.rmb_buy_level] or {}
end

function MultiFunctionWGData:GetDaoHangSuitApparelItemCfg()
	return self.daohang_suit_item_cfg
end

function MultiFunctionWGData:GetDaoHangComposeTypeCfg()
	return self.daohang_compose_type_cfg
end

function MultiFunctionWGData:GetDaoHangComposeSmallItemInfo(index)
	return self.daohang_compose_type_cfg[index]
end

function MultiFunctionWGData:GetDaoHangKelingStoreCacheByType(type)
	return self.keling_store_cache[type]
end

function MultiFunctionWGData:GetDaoHangKelingItemDataList()
	return self.keling_item_list
end

function MultiFunctionWGData:GetDaoHangKelingItemDataBySlot(slot)
	return self.keling_item_list[slot]
end

function MultiFunctionWGData:GetDaoHangSlotItemData()
	return self.slot_item_list
end

function MultiFunctionWGData:GetDaoHangSlotItemDataBySlot(slot)
	return self.slot_item_list[slot]
end

function MultiFunctionWGData:GetDaoHangOneKeyBagCache(slot)
	return self.daohang_bag_one_key_cache[slot]
end

function MultiFunctionWGData:GetDaoHangBagSortDataList()
	return SortDataByItemColor(self.daohang_bag_grid_list)
end

function MultiFunctionWGData:GetDaoHangSlotEnhanceCfgBySlotAndLevel(slot, level)
	return (self.daohang_slot_enhance_cfg[slot] or {})[level] or {}
end

function MultiFunctionWGData:GetDaoHangSlotAdvanceCfgBySlotAndLevel(slot, order)
	return (self.daohang_slot_advance_cfg[slot] or {})[order] or {}
end

function MultiFunctionWGData:GetDaoHangOtherCfg()
	return self.daohang_other_cfg or {}
end

function MultiFunctionWGData:GetDaoHangBagDataList()
	return self.daohang_bag_grid_list
end

function MultiFunctionWGData:GetDaoHangComposeBigTypeRedmind(big_type)
	return (self.daohang_compose_cache_remind[big_type] or {}).is_remind or false
end

function MultiFunctionWGData:GetDaoHangComposeSmallTypeRedmind(big_type, small_type)
	return (((self.daohang_compose_cache_remind[big_type] or {}).child_list or {})[small_type] or {}).is_remind or false
end

function MultiFunctionWGData:GetDaoHangComposeItemCfg(big_type, small_type)
	return ((self.daohang_compos_cfg[big_type] or {})[small_type] or {})
end

function MultiFunctionWGData:GetDaoHangComposeStuffNum(item_id)
	return (self.daohang_compose_cache[item_id] or {}).num or 0
end

function MultiFunctionWGData:GetDaoHangCompRemind()
	return self.daohang_compose_remind
end

function MultiFunctionWGData:IsDaoHangKeLingStore(item_id)
	return nil ~= self.daohang_keling_stone[item_id]
end

function MultiFunctionWGData:GetDaoHangKeLingStoreCfg(item_id)
	return self.daohang_keling_stone[item_id]
end

function MultiFunctionWGData:CanOpenDaoHangKeLing()
	return self.daohang_keling_need_cache < KELING_OPEN_NEED_NUM
end

function MultiFunctionWGData:GetDaoHangKeLingOneKeyData()
	return self.daohang_keling_one_key_data
end

function MultiFunctionWGData:GetDaoHangKeLingTypeBySlot(slot)
	return (self.daohang_keling_hole_cfg[slot] or {}).type
end

function MultiFunctionWGData:GetDaoHangKelingInfo(slot)
	return self.keling_store_cal_cache[slot]
end

function MultiFunctionWGData:GetDaoHangOneKeyWearDataList()
	if IsEmptyTable(self.daohang_one_key_wear_datalist) then
		return 0, {}
	end
	
	return #self.daohang_one_key_wear_datalist, self.daohang_one_key_wear_datalist
end

function MultiFunctionWGData:GetDaoHangResonanceRemind()
	return self.daohang_keling_gongming_remind
end

function MultiFunctionWGData:GetDaoHangEquipedEquip()
	return self.daohang_equiped_equip
end

function MultiFunctionWGData:GetDaoHangQiangHuaEquipRemindByslot(slot)
	return self.daohang_qianghua_remind_cache[slot]
end

function MultiFunctionWGData:GetDaoHangJinJieEquipRemindByslot(slot)
	return self.daohang_jinjie_remind_cache[slot]
end

function MultiFunctionWGData:GetDaoHangQiangHuaAutoUpState()
	return self.daohang_qianghua_auto_up_state
end

function MultiFunctionWGData:SetDaoHangQiangHuaAutoUpState(state)
	self.daohang_qianghua_auto_up_state = state
end

function MultiFunctionWGData:GetDaoHangJinJieAutoUpState()
	return self.daohang_jinjie_auto_up_state
end

function MultiFunctionWGData:SetDaoHangJinJieAutoUpState(state)
	self.daohang_jinjie_auto_up_state = state
end

function MultiFunctionWGData:GetDaoHangKaiGuangCostState()
	return self.daohang_kaiguang_extracost_state
end

function MultiFunctionWGData:SetDaoHangKaiGuangCostState(state)
	self.daohang_kaiguang_extracost_state = state
end

function MultiFunctionWGData:GetDaoHangSlotCarveCfg(slot, seq, level)
	return ((self.daohang_slot_carve_cfg[slot] or {})[seq] or {})[level] or {}
end

function MultiFunctionWGData:GetDaoHangKeYinEquipRemindByslot(slot)
	return (self.daohang_keyin_remind_cache[slot] or {}).remind
end

function MultiFunctionWGData:GetDaoHangKeYinCellRemindByslotAndSeq(slot, seq)
	return ((self.daohang_keyin_remind_cache[slot] or {})[seq] or {}).remind
end

function MultiFunctionWGData:GetDaoHangKaiGuangAttrCfg(slot, seq)
	return (self.daohang_kaiguang_attr_cfg[slot] or {})[seq]
end

function MultiFunctionWGData:GetDaoHangKaiGuangEquipRemindByslot(slot)
	return self.daohang_kaiguang_remind_cache[slot]
end

function MultiFunctionWGData:GetDaoHangSuitCacheNum(quality, star_level)
	return (((self.daohang_suit_cache or {})[quality] or {})[star_level] or {}).num or 0
end

function MultiFunctionWGData:GetDaoHangSuitColorCacheNum(quality)
	return ((self.daohang_suit_color_cache or {})[quality] or {}).num or 0
end

function MultiFunctionWGData:GetDaoHangkeLingTypeDefaultStoreCfg(slot)
	return (self.daohang_keling_slot_stone[slot] or {})[1]
end

-- function MultiFunctionWGData:GetDaoHangKeYinResonanceData()
--     return self.daohang_keyin_resonance_cache
-- end

function MultiFunctionWGData:GetDaoHangKeLingResonanceData()
    return self.daohang_keling_gongming_data_cache
end

function MultiFunctionWGData:GetDaoHangQiangHuaResonanceCfg()
	return self.daohang_qianghua_gongming_cfg
end

function MultiFunctionWGData:GetDaoHangQiangHuaResonanceLevel()
	return self.daohang_qianghua_resonance_level
end

function MultiFunctionWGData:GetDaoHangKeYinResonanceLevel()
	return self.daohang_keyin_all_level
end

function MultiFunctionWGData:GetDaoHangKeYinResonanceCfg()
	return self.daohang_keyin_gongming_cfg
end

function MultiFunctionWGData:GetDaoHangFashionSkillCfg()
	return self.daohang_fashion_skill_cfg
end
------------------------------------------Common_Get_End---------------------------------------------

------------------------------------------Protocol_Set_Start-------------------------------------------
function MultiFunctionWGData:SetDaoHangBagInfo(protocol)
	self.daohang_bag_grid_count = protocol.grid_count
	self.daohang_bag_grid_list = protocol.grid_list

	self:CalculationDaoHangBagCache(self.daohang_bag_grid_list)
	self:CalDaoHangSuitOneKeyWearData()
	self:CalculationDaoHangComposeRemind()
	self:CalDaoHangKelingData()
end

function MultiFunctionWGData:SetDaoHangInfo(protocol)
	self.enhance_gongming_level = protocol.enhance_gongming_level
	self.carve_gongming_level = protocol.carve_gongming_level
	self.rmb_buy_level = protocol.rmb_buy_level
	self.keling_item_list = protocol.keling_item_list

	self.inlay_four_gongming = protocol.inlay_four_gongming
	self.inlay_eight_gongming = protocol.inlay_eight_gongming
	self.inlay_tw_gongming = protocol.inlay_tw_gongming

	self.slot_item_list = protocol.slot_item_list
	self.suit_id_eight_item = protocol.suit_id_eight_item
	self.touch_rmb_time = protocol.touch_rmb_time
	self.m_qu_id = protocol.m_qu_id
	self.rmb_buy_flag = bit:d2b_l2h(protocol.rmb_buy_flag, nil, true)
	self.touch_num = protocol.touch_num

	-- 初始化fumo_max， 不然存在红点问题
	self:GetPrivilegeNum()

	self:CalculationDaoHangEquipedEquip()
	self:CalDaoHangSuitOneKeyWearData()
	self:CalDaoHangKeLingResonanceNumData()
	self:CalDaoHangKelingOneKeyData()
	self:CalDaoHangKelingData()
end

function MultiFunctionWGData:UpdataDaoHangSlot(protocol)
	self.slot_item_list[protocol.slot] = protocol.slot_item
	self:CalDaoHangSuitOneKeyWearData()
	self:CalculationDaoHangEquipedEquip()
end

function MultiFunctionWGData:UpdateDaoHangHole(protocol)
	self.keling_item_list[protocol.hole] = protocol.inlay_item
	self:UpdateDaoHangKeLingRemind()
	self:CalDaoHangKeLingResonanceNumData()
end

function MultiFunctionWGData:UpdateDaoHangBagInfo(protocol)
	local new_data = protocol.grid_info
	local old_data = self.daohang_bag_grid_list[new_data.index]
	local no_old_data = IsEmptyTable(old_data)
	local is_add = false
	local add_num = 0

	if (no_old_data and new_data.item_id > 0 and new_data.num > 0) or (not no_old_data and old_data.item_id <= 0 and new_data.item_id > 0 and new_data.num > 0) then
		-- print_error("添加------------  " ,protocol.grid_info)
		self.daohang_bag_grid_list[new_data.index] = new_data
		is_add = true
		add_num = new_data.num
	elseif not no_old_data and old_data.item_id > 0 and old_data.num > 0 and new_data.item_id > 0 and new_data.num > 0  then
		-- print_error("修改------------  " ,protocol.grid_info)
		self.daohang_bag_grid_list[new_data.index] = new_data
		is_add = new_data.num > old_data.num
		add_num = new_data.num - old_data.num
	else
		-- print_error("删除------------  " ,protocol.grid_info)
		self.daohang_bag_grid_list[new_data.index] = nil
	end

	self:CalculationDaoHangBagCache(self.daohang_bag_grid_list)
	self:CalculationDaoHangComposeRemind()
	self:CalDaoHangSuitOneKeyWearData()

	return is_add, add_num
end

function MultiFunctionWGData:UpdataDaoHangKeLingRenonanceInfo(protocol)
	self.inlay_four_gongming = protocol.inlay_four_gongming
	self.inlay_eight_gongming = protocol.inlay_eight_gongming
	self.inlay_tw_gongming = protocol.inlay_tw_gongming

	self:CalDaoHangKeLingResonanceCache()
end

function MultiFunctionWGData:UpdateTaoistGongMing(protocol)
	local old_enhance_gongming_level = self.enhance_gongming_level
	local enhance_change = old_enhance_gongming_level ~= protocol.enhance_gongming_level

	self.enhance_gongming_level = protocol.enhance_gongming_level
	self.carve_gongming_level = protocol.carve_gongming_level
	return enhance_change
end
------------------------------------------Protocol_Set_End---------------------------------------------

---------------------------------------------------Cal_Start---------------------------------------------
function MultiFunctionWGData:CalculationDaoHangBagCache(bag_grid_list)
	local daohang_bag_one_key_cache = {}
	local daohang_compose_cache = {}

	if IsEmptyTable(bag_grid_list) then
		self.daohang_bag_one_key_cache = daohang_bag_one_key_cache
		self.daohang_compose_cache = daohang_compose_cache
		return
	end

	for k, v in pairs(bag_grid_list) do
		local equip_cfg = self:GetDaoHangEquipByItemId(v.item_id)
		if not IsEmptyTable(equip_cfg) then
			local _, equip_color = ItemWGData.Instance:GetItemColor(v.item_id)
			local data = {star_level = equip_cfg.star_level, color = equip_color, index = v.index, item_id = v.item_id}
			daohang_bag_one_key_cache[equip_cfg.slot] = daohang_bag_one_key_cache[equip_cfg.slot] or {}
			table.insert(daohang_bag_one_key_cache[equip_cfg.slot], data)

			daohang_compose_cache[v.item_id] = daohang_compose_cache[v.item_id] or {item_id = v.item_id, num = 0}
			daohang_compose_cache[v.item_id].num = daohang_compose_cache[v.item_id].num + v.num
		end
	end

	if not IsEmptyTable(daohang_bag_one_key_cache) then
		for i = 0, 7 do
			if not IsEmptyTable(daohang_bag_one_key_cache[i]) then
				table.sort(daohang_bag_one_key_cache[i], SortTools.KeyUpperSorters("color", "star_level"))
			end
		end
	end

	self.daohang_bag_one_key_cache = daohang_bag_one_key_cache
	self.daohang_compose_cache = daohang_compose_cache
end

function MultiFunctionWGData:CalDaoHangSuitOneKeyWearData()
	local data_list = {}

	for k, v in pairs(self:GetDaoHangEquipTypeCfg()) do
		local change_data = self:GetDaoHangOneKeyBagCache(v.slot)

		local best_quality_data = ((change_data or {})[1] or {})

		if not IsEmptyTable(best_quality_data) then
			if self:CanDaoHangSuitItemUp(best_quality_data.item_id) then
				table.insert(data_list, {item_id = best_quality_data.item_id, bag_index = best_quality_data.index, slot = v.slot})
			end
		end
	end

	self.daohang_one_key_wear_datalist = data_list
end

function MultiFunctionWGData:CalculationDaoHangEquipedEquip()
	local daohang_suit_cache = {}
	local daohang_suit_color_cache = {}
	local daohang_equiped_equip = {}
	local daohang_keling_need_cache = 0
	local daohang_compose_equiped_cache = {}

	local daohang_qianghua_remind = false
	local daohang_qianghua_remind_cache = {}
	local daohang_qianghua_resonance_level = 0

	local daohang_jinjie_remind = false
	local daohang_jinjie_remind_cache = {}

	local daohang_keyin_remind = false
	local daohang_keyin_remind_cache = {}

	local daohang_keyin_all_level = 0
	local daohang_kaiguang_remind = false
	local daohang_kaiguang_remind_cache = {}

	local daohang_qianghua_item_cost_cache = {}
	local daohang_jinjie_item_cost_cache = {}
	local daohang_keyin_item_cost_cache = {}

	for k, v in pairs(self:GetDaoHangEquipTypeCfg()) do
		local slot_info = self:GetDaoHangSlotItemDataBySlot(v.slot)

		if slot_info and slot_info.item_id > 0 then
			------------------------------item_id 改变监听缓存-----------------------------------
			local slot = slot_info.slot
			local cur_level_cfg = self:GetDaoHangSlotEnhanceCfgBySlotAndLevel(slot, slot_info.level)
			if not IsEmptyTable(cur_level_cfg) then
				daohang_qianghua_item_cost_cache[cur_level_cfg.cost_item_id] = cur_level_cfg.cost_item_id
			end

			local cur_grade_cfg = self:GetDaoHangSlotAdvanceCfgBySlotAndLevel(slot, slot_info.grade)
			if not IsEmptyTable(cur_grade_cfg) then
				daohang_jinjie_item_cost_cache[cur_grade_cfg.cost_item_id] = cur_grade_cfg.cost_item_id
			end

			for i = 1, 3 do
				local level = ((slot_info.carve_item_list or {})[i] or {}).level
				local cur_level_cfg = self:GetDaoHangSlotCarveCfg(slot, i, level)
	
				if not IsEmptyTable(cur_level_cfg) then
					daohang_keyin_item_cost_cache[cur_level_cfg.cost_item_id] = cur_level_cfg.cost_item_id
				end
			end
			------------------------------------------------------------------------------------------

			table.insert(daohang_equiped_equip, slot_info)
			daohang_compose_equiped_cache[slot_info.item_id] = daohang_compose_equiped_cache[slot_info.item_id] or {item_id = slot_info.item_id, num = 0}
			daohang_compose_equiped_cache[slot_info.item_id].num = daohang_compose_equiped_cache[slot_info.item_id].num + 1

			daohang_suit_cache[slot_info.color] = daohang_suit_cache[slot_info.color] or {}

			for i = slot_info.color, GameEnum.ITEM_COLOR_GREEN, -1 do
				daohang_suit_cache[i] = daohang_suit_cache[i] or {}
				daohang_suit_color_cache[i] = daohang_suit_color_cache[i] or {num = 0}
				daohang_suit_color_cache[i].num = daohang_suit_color_cache[i].num + 1

				local equip_cfg = self:GetDaoHangEquipByItemId(slot_info.item_id)

				for j = equip_cfg.star_level, 1, -1 do
					daohang_suit_cache[i][j] = daohang_suit_cache[i][j] or {num = 0}
					daohang_suit_cache[i][j].num = daohang_suit_cache[i][j].num + 1
				end
			end

			if slot_info.color >= KELING_OPEN_NEED_COLOR then
				daohang_keling_need_cache = daohang_keling_need_cache + 1
			end

			-- 强化
			local qianghua_cell_remind = self:GetDaoHangQiangHuaSlotRemind(v.slot)
			daohang_qianghua_remind_cache[slot_info.slot] = qianghua_cell_remind
			if qianghua_cell_remind then
				daohang_qianghua_remind = true
			end

			daohang_qianghua_resonance_level = daohang_qianghua_resonance_level + slot_info.level

			-- 进阶
			local jinjie_cell_remind = self:GetDaoHangJinJieSlotRemind(v.slot)
			daohang_jinjie_remind_cache[slot_info.slot] = jinjie_cell_remind
			if jinjie_cell_remind then
				daohang_jinjie_remind = true
			end

			--刻印
			local daohang_keyin_cell_remind = {}
			daohang_keyin_cell_remind.remind = false
			for i = 1, 3 do
				local remind = self:GetDaoHangKeYinSlotRemind(v.slot, i)
				daohang_keyin_cell_remind[i] = daohang_keyin_cell_remind[i] or {}
				daohang_keyin_cell_remind[i].remind = remind

				local level = ((slot_info.carve_item_list or {})[i] or {}).level
				daohang_keyin_all_level = daohang_keyin_all_level + level

				if remind then
					daohang_keyin_cell_remind.remind = true
					daohang_keyin_remind = true
				end
			end

			daohang_keyin_remind_cache[v.slot] = daohang_keyin_cell_remind

			-- 开光
			local kaiguang_cell_remind = self:GetDaoHangKaiGuangSlotRemind(v.slot)
			if kaiguang_cell_remind then
				daohang_kaiguang_remind = true
			end

			daohang_kaiguang_remind_cache[v.slot] = kaiguang_cell_remind
		end
	end

	self.daohang_suit_cache = daohang_suit_cache
	self.daohang_suit_color_cache = daohang_suit_color_cache
	self.daohang_compose_equiped_cache = daohang_compose_equiped_cache
	self.daohang_keling_need_cache = daohang_keling_need_cache
	self.daohang_equiped_equip = daohang_equiped_equip
	self.daohang_qianghua_resonance_level = daohang_qianghua_resonance_level

	self.daohang_qianghua_remind_cache = daohang_qianghua_remind_cache
	self.daohang_qianghua_remind = daohang_qianghua_remind

	self.daohang_jinjie_remind_cache = daohang_jinjie_remind_cache
	self.daohang_jinjie_remind = daohang_jinjie_remind

	self.daohang_keyin_remind = daohang_keyin_remind
	self.daohang_keyin_remind_cache = daohang_keyin_remind_cache
	self.daohang_keyin_all_level = daohang_keyin_all_level
	
	self.daohang_kaiguang_remind = daohang_kaiguang_remind
	self.daohang_kaiguang_remind_cache = daohang_kaiguang_remind_cache

	self.daohang_qianghua_item_cost_cache = daohang_qianghua_item_cost_cache
	self.daohang_jinjie_item_cost_cache = daohang_jinjie_item_cost_cache
	self.daohang_keyin_item_cost_cache = daohang_keyin_item_cost_cache
end

function MultiFunctionWGData:CalculationDaoHangQiangHuaRemind()
	local daohang_qianghua_remind = false
	local daohang_qianghua_remind_cache = {}
	
	for k, v in pairs(self:GetDaoHangEquipedEquip()) do
		local remind = self:GetDaoHangQiangHuaSlotRemind(v.slot)
		daohang_qianghua_remind_cache[v.slot] = remind

		if remind then
			daohang_qianghua_remind = true
		end
	end

	self.daohang_qianghua_remind_cache = daohang_qianghua_remind_cache
	self.daohang_qianghua_remind = daohang_qianghua_remind
end

function MultiFunctionWGData:CalculationDaoHangJinJieRemind()
	local daohang_jinjie_remind = false
	local daohang_jinjie_remind_cache = {}
	
	for k, v in pairs(self:GetDaoHangEquipedEquip()) do
		local remind = self:GetDaoHangJinJieSlotRemind(v.slot)
		daohang_jinjie_remind_cache[v.slot] = remind

		if remind then
			daohang_jinjie_remind = true
		end
	end

	self.daohang_jinjie_remind_cache = daohang_jinjie_remind_cache
	self.daohang_jinjie_remind = daohang_jinjie_remind
end

function MultiFunctionWGData:CalculationDaoHangKeYinRemind()
	local daohang_keyin_remind = false
	local daohang_keyin_remind_cache = {}
	
	for k, v in pairs(self:GetDaoHangEquipedEquip()) do
		local daohang_keyin_cell_remind = {}
		daohang_keyin_cell_remind.remind = false

		for i = 1, 3 do
			local remind = self:GetDaoHangKeYinSlotRemind(v.slot, i)
			daohang_keyin_cell_remind[i] = daohang_keyin_cell_remind[i] or {}
			daohang_keyin_cell_remind[i].remind = remind

			if remind then
				daohang_keyin_cell_remind.remind = true
				daohang_keyin_remind = true
			end
		end

		daohang_keyin_remind_cache[v.slot] = daohang_keyin_cell_remind
	end

	self.daohang_keyin_remind = daohang_keyin_remind
	self.daohang_keyin_remind_cache = daohang_keyin_remind_cache
end

function MultiFunctionWGData:CalculationDaoHangKaiGuangRemind()
	local daohang_kaiguang_remind = false
	local daohang_kaiguang_remind_cache = {}
	
	for k, v in pairs(self:GetDaoHangEquipedEquip()) do
		local kaiguang_cell_remind = self:GetDaoHangKaiGuangSlotRemind(v.slot)
		if kaiguang_cell_remind then
			daohang_kaiguang_remind = true
		end

		daohang_kaiguang_remind_cache[v.slot] = kaiguang_cell_remind
	end

	self.daohang_kaiguang_remind = daohang_kaiguang_remind
	self.daohang_kaiguang_remind_cache = daohang_kaiguang_remind_cache
end

function MultiFunctionWGData:IsDaoHangKaiGuangItem(change_item_id)
	local other_cfg = self:GetDaoHangOtherCfg()

    if not IsEmptyTable(other_cfg) then
		if change_item_id == other_cfg.kaiguang_item_id or change_item_id == other_cfg.luck_item_id then
			return true
		end
	end

	return false
end

function MultiFunctionWGData:GetDaoHangJinJieSlotRemind(slot)
	local remind = false
	local data_info = self:GetDaoHangSlotItemDataBySlot(slot)

	if not IsEmptyTable(data_info) then
		local grade = data_info.grade
		local next_grade_cfg = self:GetDaoHangSlotAdvanceCfgBySlotAndLevel(slot, grade + 1)

		if not IsEmptyTable(next_grade_cfg) then
			local cur_grade_cfg = self:GetDaoHangSlotAdvanceCfgBySlotAndLevel(slot, grade)
			local has_item_num = ItemWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
			local item_enough = cur_grade_cfg.cost_item_num <= has_item_num
			local role_bind_gold = GameVoManager.Instance:GetMainRoleVo().gold or 0
			local bind_gold_enough = cur_grade_cfg.cost_gold <= role_bind_gold

			remind = item_enough and bind_gold_enough
		end
	end

	return remind
end

function MultiFunctionWGData:GetDaoHangQiangHuaSlotRemind(slot)
	local remind = false
	local data_info = self:GetDaoHangSlotItemDataBySlot(slot)

	if not IsEmptyTable(data_info) then
		local level = data_info.level
		local next_level_cfg = self:GetDaoHangSlotEnhanceCfgBySlotAndLevel(slot, level + 1)

		if not IsEmptyTable(next_level_cfg) then
			local cur_level_cfg = self:GetDaoHangSlotEnhanceCfgBySlotAndLevel(slot, level)
			local has_item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
			local item_enough = cur_level_cfg.cost_item_num <= has_item_num
			local role_bind_gold = RoleWGData.Instance.role_info.coin or 0
			local bind_gold_enough = cur_level_cfg.cost_coin_num <= role_bind_gold

			remind = item_enough and bind_gold_enough
		end
	end

	return remind
end

function MultiFunctionWGData:GetDaoHangKeYinSlotRemind(slot, seq)
	local remind = false
	local data_info = self:GetDaoHangSlotItemDataBySlot(slot)

	if not IsEmptyTable(data_info) then
		local level = ((data_info.carve_item_list or {})[seq] or {}).level or 0
		local next_level_cfg = self:GetDaoHangSlotCarveCfg(slot, seq, level + 1)
		
		if not IsEmptyTable(next_level_cfg) then
			local cur_level_cfg = self:GetDaoHangSlotCarveCfg(slot, seq, level)
			local has_item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
			local item_enough = cur_level_cfg.cost_item_num <= has_item_num
			remind = item_enough
		end
	end

	return remind
end

function MultiFunctionWGData:GetDaoHangKaiGuangSlotRemind(slot)
    local other_cfg = self:GetDaoHangOtherCfg()
	local has_kaiguang_item = false
	local has_kaiguang_lucky_item= false

    if not IsEmptyTable(other_cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.kaiguang_item_id)
		local lucky_item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.luck_item_id)
		has_kaiguang_item = item_num >= 1
		has_kaiguang_lucky_item = lucky_item_num >= 1
	end

	local data_info = self:GetDaoHangSlotItemDataBySlot(slot)
	local remind = false

	local _, item_color = ItemWGData.Instance:GetItemColor(data_info.item_id)
    local can_kaiguang = item_color >= other_cfg.max_kaiguang_quality

	if can_kaiguang then
		if data_info.already_kaiguang then
			remind = has_kaiguang_item and has_kaiguang_lucky_item
		else
			remind = has_kaiguang_item
		end
	end

	return remind
end

function MultiFunctionWGData:IsDaoHangQiangHuaItem(change_item_id)
	-- for k, v in pairs(self:GetDaoHangEquipedEquip()) do
	-- 	local slot = v.slot
	-- 	local cur_level = v.level
	-- 	local cur_level_cfg = self:GetDaoHangSlotEnhanceCfgBySlotAndLevel(slot, cur_level)

	-- 	if not IsEmptyTable(cur_level_cfg) then
	-- 		if change_item_id == cur_level_cfg.cost_item_id then
	-- 			return true
	-- 		end
	-- 	end
	-- end

	-- return false
	
	return nil ~= self.daohang_qianghua_item_cost_cache[change_item_id]
end

function MultiFunctionWGData:IsDaoHangJinJieItem(change_item_id)
	-- for k, v in pairs(self:GetDaoHangEquipedEquip()) do
	-- 	local slot = v.slot
	-- 	local cur_grade = v.grade
	-- 	local cur_grade_cfg = self:GetDaoHangSlotAdvanceCfgBySlotAndLevel(slot, cur_grade)
		
	-- 	if not IsEmptyTable(cur_grade_cfg) then
	-- 		if change_item_id == cur_grade_cfg.cost_item_id then
	-- 			return true
	-- 		end
	-- 	end
	-- end

	-- return false

	return nil ~= self.daohang_jinjie_item_cost_cache[change_item_id]
end

function MultiFunctionWGData:IsDaoHangKeYinItem(change_item_id)
	-- for k, v in pairs(self:GetDaoHangEquipedEquip()) do
	-- 	local slot = v.slot

	-- 	for i = 1, 3 do
	-- 		local level = ((v.carve_item_list or {})[i] or {}).level
	-- 		local cur_level_cfg = self:GetDaoHangSlotCarveCfg(slot, i, level)

	-- 		if not IsEmptyTable(cur_level_cfg) then
	-- 			if change_item_id == cur_level_cfg.cost_item_id then
	-- 				return true
	-- 			end
	-- 		end
	-- 	end
	-- end

	-- return false

	return nil ~= self.daohang_keyin_item_cost_cache[change_item_id]
end

function MultiFunctionWGData:CalDaoHangKelingOneKeyData()
	local one_key_data_list = {}
	local data_list = ItemWGData.Instance:GetDaoHangEquipStoneList()

	if not IsEmptyTable(data_list) then
		for k, v in pairs(data_list) do
			local store_cfg = self:GetDaoHangKeLingStoreCfg(v.item_id)

			if not IsEmptyTable(store_cfg) then
				one_key_data_list[store_cfg.slot] = one_key_data_list[store_cfg.slot] or {}

				for i = 1, v.num do
					local one_key_data = {num = 1, bag_index = v.index, type = store_cfg.slot, item_id = v.item_id, level = store_cfg.level}
					table.insert(one_key_data_list[store_cfg.slot], one_key_data)
				end
			end
		end

		for k, v in pairs(one_key_data_list) do
			table.sort(v, SortTools.KeyUpperSorter("level"))  --KeyUpperSorter
		end
	end

	local daohang_keling_one_key_data = {}
	if IsEmptyTable(one_key_data_list) then
		self.daohang_keling_one_key_data = daohang_keling_one_key_data
		return
	end

    -- 优先空槽位，然后是有槽位
	local keling_item_list_info = self:GetDaoHangKelingItemDataList()
	if not IsEmptyTable(keling_item_list_info) then
		-- 空槽位
		for k, v in pairs(keling_item_list_info) do
			if v.is_open == 1 then
				local type = self:GetDaoHangKeLingTypeBySlot(v.slot)
				local stuff_cache_list = one_key_data_list[type]
				if not IsEmptyTable(stuff_cache_list) then
					if v.item_id <= 0 then
						local data = table.remove(stuff_cache_list, 1)
						data.hole = v.slot
						table.insert(daohang_keling_one_key_data, data)
					end
				end
			end
		end

		-- 替换位置
		for k, v in pairs(keling_item_list_info) do
			if v.is_open == 1 then
				local type = self:GetDaoHangKeLingTypeBySlot(v.slot)
				local stuff_cache_list = one_key_data_list[type]
				if not IsEmptyTable(stuff_cache_list) then
					local stuff_cache_data = stuff_cache_list[1]

					if v.item_id > 0 then
						local cur_cfg = self:GetDaoHangKeLingStoreCfg(v.item_id)
						local stuff_cfg = self:GetDaoHangKeLingStoreCfg(stuff_cache_data.item_id)

						if stuff_cfg.level > cur_cfg.level then
							local data = table.remove(stuff_cache_list, 1)
							data.hole = v.slot
							table.insert(daohang_keling_one_key_data, data)
						end
					end
				end
			end
		end
	end

	self.daohang_keling_one_key_data = daohang_keling_one_key_data
end

-- 计算宝石镶嵌替换数据
function MultiFunctionWGData:GetDaoHangKelingChangeDataList(slot, type)
	local can_change = false
	local change_data_list = {}

	local data_list = self:GetDaoHangKelingStoreCacheByType(type)
	if IsEmptyTable(data_list) then
		return can_change, change_data_list
	end

	local slot_info = self:GetDaoHangKelingItemDataBySlot(slot)
	if not IsEmptyTable(slot_info) then
		if slot_info.item_id > 0 then
			local store_cfg = self:GetDaoHangKeLingStoreCfg(slot_info.item_id)

			for i = 1, #data_list do
				local target_cfg = self:GetDaoHangKeLingStoreCfg(data_list[i].item_id)
				
				if target_cfg.level > store_cfg.level then
					can_change = true
					table.insert(change_data_list, data_list[i])
				end
			end
		else
			can_change = true
			change_data_list = data_list
		end
	end

	return can_change, change_data_list
end

-- 计算宝石升级数据
function MultiFunctionWGData:GetDaoHangKelingUpDataList(slot, type)
	local can_up = false
	local up_data_list = {}

	local data_list = self:GetDaoHangKelingStoreCacheByType(type)
	if IsEmptyTable(data_list) then
		return can_up, up_data_list
	end

	local slot_info = self:GetDaoHangKelingItemDataBySlot(slot)
	if not IsEmptyTable(slot_info) then
		local store_cfg = self:GetDaoHangKeLingStoreCfg(slot_info.item_id)
		if store_cfg.next_item_id <= 0 then
			return can_up, up_data_list
		end

		local target_cfg = self:GetDaoHangKeLingStoreCfg(store_cfg.next_item_id)
		local need_price = target_cfg.price
		local has_price = store_cfg.price

		for i = 1, #data_list do
			local cell_cfg = self:GetDaoHangKeLingStoreCfg(data_list[i].item_id)
			if cell_cfg.level <= store_cfg.level then
				has_price = has_price + cell_cfg.price * data_list[i].num

				table.insert(up_data_list, data_list[i])
				if need_price <= has_price then
					can_up = true
					return can_up, up_data_list
				end
			end
		end
	end

	if not can_up then
		up_data_list = {}
	end

	return can_up, up_data_list
end

function MultiFunctionWGData:GetDaoHangKeLingLevelAndAttr()
	local level = 0
	local attr_list = {}

	local all_attr_list = {}
	local function add_attr(attr_data)
		if IsEmptyTable(attr_data) then
			return
		end

		for i = 1, 5 do
			local attr_id = attr_data["attr_id" .. i] or 0
			local attr_value = attr_data["attr_value" .. i] or 0

			if attr_id > 0 then
				local add_value = all_attr_list[attr_id] and (all_attr_list[attr_id] + attr_value) or attr_value
				all_attr_list[attr_id] = add_value
			end
		end
	end

	local data_info = self:GetDaoHangKelingItemDataList()
	if not IsEmptyTable(data_info) then
		for k, v in pairs(data_info) do
			if v.item_id > 0 then
				local store_cfg = self:GetDaoHangKeLingStoreCfg(v.item_id)
				local store_level = store_cfg.level or 0
				level = level + store_level

				add_attr(store_cfg)
			end
		end
	end

	if IsEmptyTable(all_attr_list) then
		local other_cfg = self:GetDaoHangOtherCfg()
		local default_attr_str = other_cfg.kl_attr_display
		local t = Split(default_attr_str, "|")

		if not IsEmptyTable(t) then
			for i, v in ipairs(t) do
				all_attr_list[v] = 0
			end
		end
	end

	if not IsEmptyTable(all_attr_list) then
		for i, v in pairs(all_attr_list) do
			local data = {}
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(i))
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
			local value_str = AttributeMgr.PerAttrValue(attr_str, v)
			local attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			data.attr_name = attr_name
			data.value_str = value_str
			data.attr_sort = attr_sort
			table.insert(attr_list, data)
		end

		if not IsEmptyTable(attr_list) then
			table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
		end
	end

	return level, attr_list
end

function MultiFunctionWGData:UpdateDaoHangKeLingRemind()
	local keling_store_cache = {}
	local data_list = ItemWGData.Instance:GetDaoHangEquipStoneList()

	if not IsEmptyTable(data_list) then
		for k, v in pairs(data_list) do
			local store_cfg = self:GetDaoHangKeLingStoreCfg(v.item_id)
			if not IsEmptyTable(store_cfg) then
				local stuff_data = {knapsack_type = v.knapsack_type, num = v.num, index = v.index,
					is_bind = v.is_bind, item_id = v.item_id, type = store_cfg.slot, level = store_cfg.level}
				keling_store_cache[store_cfg.slot] = keling_store_cache[store_cfg.slot] or {}
				table.insert(keling_store_cache[store_cfg.slot], stuff_data)
			end
		end

		for k, v in pairs(keling_store_cache) do
			table.sort(v, SortTools.KeyUpperSorter("level"))  --KeyUpperSorter
		end
	end

	self.keling_store_cache = keling_store_cache

	self:CalDaoHangKelingData()
end

-- 计算宝石数据
function MultiFunctionWGData:CalDaoHangKelingData()
	local keling_store_remind = false
	local keling_store_cal_cache = {}
	
	for k, v in pairs(self.daohang_keling_hole_cfg) do
		local slot_info = self:GetDaoHangKelingItemDataBySlot(v.slot)

		if not IsEmptyTable(slot_info) then		
			local data = {}
			data.slot = v.slot
			data.item_id = slot_info.item_id
			local is_open = slot_info.is_open == 1
			local can_change, change_data_list = false, {}
			local can_up, up_store_list = false, {}

			if is_open then
				can_change, change_data_list = self:GetDaoHangKelingChangeDataList(v.slot, v.type)

				if slot_info.item_id > 0 then
					can_up, up_store_list = self:GetDaoHangKelingUpDataList(v.slot, v.type)
				end
			end

			data.can_change = can_change
			data.change_data_list = change_data_list
			data.can_up = can_up
			data.up_store_list = up_store_list

			if can_change or can_up then
				keling_store_remind = true
			end

			keling_store_cal_cache[v.slot] = data
		end
	end
 
	self.keling_store_remind = keling_store_remind
	self.keling_store_cal_cache = keling_store_cal_cache
	self:CalDaoHangKelingOneKeyData()
end

-- 品质quality优先 让然后星级star_level
function MultiFunctionWGData:GetDaoHangEquipSuitAttr()
	local suit_data = {}

	for i = 1, 2 do
		local target_suit_data = {}
		local cur_data = {}
		local next_data = {}

		local suit_num = 4 * i
		target_suit_data.num = suit_num

		local cache_data = suit_num == 4 and self.daohang_suit_four_cache or self.daohang_suit_eight_cache
		local color_data_cache = {}
		local default_data = {}

		-- 2022/12/28 策划要求 先计算颜色  颜色之后再算星级
		for k, v in pairs(cache_data) do
			local num = self:GetDaoHangSuitColorCacheNum(k)
			default_data = v

			if num < suit_num then
				break
			end

			default_data = {}
			color_data_cache = v
		end

		if IsEmptyTable(color_data_cache) then
			color_data_cache = default_data
		end

		for k, v in pairs(color_data_cache) do
			local num = self:GetDaoHangSuitCacheNum(v.quality, v.star_level)

			if num < v.need_num then
				next_data = v
				break
			end
	
			cur_data = v
		end

		if IsEmptyTable(next_data) and not IsEmptyTable(default_data) then
			for k, v in pairs(default_data) do
				next_data = v
				break
			end
		end

		local current_title = Language.Charm.DaoHangCurSuitTitle
		
		if not IsEmptyTable(cur_data) then
			local color = Language.Common.ColorName4[cur_data.quality]
			current_title = current_title .. ToColorStr(string.format(Language.Charm.DaoHangSuitQuality, color, cur_data.star_level), COLOR3B.D_GREEN)
		end
		
		target_suit_data.current_title = current_title

		local next_title = Language.Charm.DaoHangNextSuitTitle
		if not IsEmptyTable(next_data) then
			local color = Language.Common.ColorName4[next_data.quality]
			next_title = next_title .. ToColorStr(string.format(Language.Charm.DaoHangSuitQuality, color, next_data.star_level), COLOR3B.D_RED)
		end

		target_suit_data.next_title = next_title
		target_suit_data.max_act_suit = IsEmptyTable(next_data)
		target_suit_data.attr_list = self:MergeTwoAttrTable(cur_data, next_data, 5)
		table.insert(suit_data, target_suit_data)
	end

	return suit_data
end

function MultiFunctionWGData:MergeTwoAttrTable(cur_attr, add_attr, attr_len, need_diff)
	local attr_list = {}
	local attr_id_list = {}
	local need_diff = need_diff or false
	-- local attribute = AttributePool.AllocAttribute()

	local function cal_attr_data(attr_id, attr_value, is_add)
		if attr_id > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))

			if not attr_id_list[attr_id] then
				local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
				local value_str = AttributeMgr.PerAttrValue(attr_str, attr_value)
				local attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)

				local temp = {}
				temp.attr_id = attr_id     			               -- 属性ID
				temp.attr_name = attr_name                         -- 标准属性名
				temp.attr_value = not is_add and attr_value or 0   -- 属性值原值
				temp.value_str =  not is_add and value_str or 0    -- 转换后的属性值 string
				temp.add_attr_value = is_add and attr_value or 0   -- 属性值原值
				temp.add_value_str = is_add and value_str or ""    -- 转换后的属性值 string
				temp.attr_sort = attr_sort                         -- 属性排序值
				table.insert(attr_list, temp)
				attr_id_list[attr_id] = #attr_list
			else
				local index = attr_id_list[attr_id]

				if is_add then
					if need_diff then
						attr_list[index].add_attr_value = attr_list[index].add_attr_value + attr_value - attr_list[index].attr_value
					else
						attr_list[index].add_attr_value = attr_list[index].add_attr_value + attr_value
					end

					local value_str = AttributeMgr.PerAttrValue(attr_str, attr_list[index].add_attr_value)
					attr_list[index].add_value_str = value_str
				else
					attr_list[index].attr_value = attr_list[index].attr_value + attr_value
					local value_str = AttributeMgr.PerAttrValue(attr_str, attr_list[index].attr_value)
					attr_list[index].value_str = value_str
				end
			end

			-- if not is_add and attr_value > 0 and attribute[attr_str] then
			-- 	attribute[attr_str] = attribute[attr_str] + attr_value
			-- end
		end
	end

	if not IsEmptyTable(cur_attr) or not IsEmptyTable(add_attr) then
		for i = 1, attr_len do
			local attr_id = cur_attr["attr_id" .. i] or 0
			local attr_value = cur_attr["attr_value" .. i] or 0
			cal_attr_data(attr_id, attr_value, false)
	
			local next_attr_id = add_attr["attr_id" .. i] or 0
			local next_attr_value = add_attr["attr_value" .. i] or 0
			cal_attr_data(next_attr_id, next_attr_value, true)
		end
	end

	return attr_list
end

-- 策划说默认拿8件的配置
function MultiFunctionWGData:GetDaoHangEquipSuitShowItemData()
	local show_item_data = {}
	local is_get = false
	local can_receive = false

	local curent_show_id = self.suit_id_eight_item
	local suit_item_cfg = self:GetDaoHangSuitApparelItemCfg()

	if suit_item_cfg then
		local cfg_num = #suit_item_cfg

		if curent_show_id >= cfg_num then
			show_item_data = suit_item_cfg[cfg_num]
			is_get = true
		else
			local cfg = suit_item_cfg[curent_show_id + 1] or {}
			local num = self:GetDaoHangSuitCacheNum(cfg.quality or 0, cfg.star_level or 0)
			show_item_data = suit_item_cfg[curent_show_id + 1]
			can_receive = num >= 8
		end
	end

	return show_item_data, is_get, can_receive
end

function MultiFunctionWGData:GetDaoHangComposeItemCfg(big_type, small_type)
	return ((self.daohang_compos_cfg[big_type] or {})[small_type] or {})
end

function MultiFunctionWGData:CalculationDaoHangComposeRemind()
	self.daohang_compose_remind = false
	local big_type, small_type = 1, 1

	for k, v in pairs(self.daohang_compose_type_cfg) do
		big_type = k
		self.daohang_compose_cache_remind[big_type] = self.daohang_compose_cache_remind[big_type] or {big_type = big_type, is_remind = false, child_list = {}}
		self.daohang_compose_cache_remind[big_type].is_remind = false

		for i = 1, #v do
			small_type = v[i].small_type
			local cfg = self:GetDaoHangComposeItemCfg(big_type, small_type)

			if not IsEmptyTable(cfg) then
				local has_equip_num = (self.daohang_compose_cache[cfg.equip_id1] or {}).num or 0
				local equiped_num = (CultivationWGData.Instance.charm_compose_equiped_cache[cfg.equip_id1] or {}).num or 0
				local cost_equip_enough = (has_equip_num + equiped_num) >= cfg.equip_num1
				local cost_stuff_enough = true
				if cfg.stuff_id2 > 0 then
					local has_stuff_num = ItemWGData.Instance:GetItemNumInBagById(cfg.stuff_id2)
					cost_stuff_enough = has_stuff_num >= cfg.stuff_num2
				end

				local can_compose = cost_equip_enough and cost_stuff_enough
	
				if not CultivationWGData.Instance.charm_compose_stuff_cache[cfg.stuff_id2] then
					CultivationWGData.Instance.charm_compose_stuff_cache[cfg.stuff_id2] = cfg.stuff_id2
				end
	
				if can_compose then
					self.daohang_compose_cache_remind[big_type].is_remind = true
					self.daohang_compose_remind = true
				end
	
				self.daohang_compose_cache_remind[big_type].child_list[small_type] = self.daohang_compose_cache_remind[big_type].child_list[small_type] or {big_type = big_type, small_type = small_type ,is_remind = false}
				self.daohang_compose_cache_remind[big_type].child_list[small_type].is_remind = can_compose
			end
		end
	end
end

function MultiFunctionWGData:GetDaoHangComposeDefaultSelect(big_type, small_type)
	local target_big_type = big_type > 0 and big_type or 1
	local target_small_type = small_type > 0 and small_type or 1

	if self:GetDaoHangComposeSmallTypeRedmind(target_big_type, target_small_type) then
		return target_big_type, target_small_type
	end

	for k, v in pairs(self.daohang_compose_type_cfg) do
		if self:GetDaoHangComposeBigTypeRedmind(k) then
			for i = 1, #v do
				if self:GetDaoHangComposeSmallTypeRedmind(k, v[i].small_type) then
					return k, v[i].small_type
				end
			end
		end
	end
	
	return target_big_type, target_small_type
end

function MultiFunctionWGData:GetDaoHangComposeSmallTypeSelect(big_type)
	local small_type = 1
	local data_list = self:GetDaoHangComposeSmallItemInfo(big_type)

	for i = 1, #data_list do
		if self:GetDaoHangComposeSmallTypeRedmind(big_type, data_list[i].small_type) then
			return data_list[i].small_type
		end
	end

	return small_type
end

function MultiFunctionWGData:GetDaoHangSuitShowCfgAttrAndCap()
	local attr_list = {}
	local attr_id_list = {}
    local capability = 0
	local attribute = AttributePool.AllocAttribute()

	local function cal_attr_data(attr_id, attr_value)
		if attr_id > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))

			if not attr_id_list[attr_id] then
				local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
				local value_str = AttributeMgr.PerAttrValue(attr_str, attr_value)
				local attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)

				--新增
				local temp = {}
				temp.attr_id = attr_id     			-- 属性ID
				temp.attr_name = attr_name          -- 标准属性名
				temp.attr_value = attr_value        -- 属性值原值
				temp.value_str = value_str          -- 转换后的属性值 string
				temp.attr_sort = attr_sort          -- 属性排序值
				table.insert(attr_list, temp)
				attr_id_list[attr_id] = #attr_list
			else
				--修改
				local index = attr_id_list[attr_id]
				attr_list[index].attr_value = attr_list[index].attr_value + attr_value
				local value_str = AttributeMgr.PerAttrValue(attr_str, attr_list[index].attr_value)
				attr_list[index].value_str = value_str
			end

			if attr_value > 0 and attribute[attr_str] then
				attribute[attr_str] = attribute[attr_str] + attr_value
			end
		end
	end

	-- 策划要求必需显示所有属性, 哪怕值为0
	local other_cfg = self:GetDaoHangOtherCfg()
	local default_attr_str = other_cfg.attr_display
	if default_attr_str then
		local t = Split(default_attr_str, "|")

		if not IsEmptyTable(t) then
			for i, v in ipairs(t) do
				cal_attr_data(tonumber(v), 0)
			end
		end
	end

	-- 8件装备属性
	local suit_equip_list = self:GetDaoHangSlotItemData()
	if not IsEmptyTable(suit_equip_list) then
		for k, v in pairs(suit_equip_list) do
			if v.item_id > 0 then
				local equip_cfg = self:GetDaoHangEquipByItemId(v.item_id)

				if not IsEmptyTable(equip_cfg) then
					for i = 1, 5 do
						local attr_id = equip_cfg["attr_id" .. i]
						local attr_value = equip_cfg["attr_value" .. i]

						if attr_id > 0 then
							cal_attr_data(attr_id, attr_value)
						end
					end
				end
			end
		end
	end

	-- 策划要求将直购增加百分比展示出来，作为一个特殊属性展示，纯展示用
	local rmb_buy_cfg = self:GetDaoHangCurRmbBuyCfg()
	if not IsEmptyTable(rmb_buy_cfg) then
		local temp = {}
		temp.attr_id = 0
		temp.attr_name = other_cfg.sp_attr_txt
		temp.attr_value = rmb_buy_cfg.add
		temp.value_str = rmb_buy_cfg.add / 10 .. "%"
		temp.attr_sort = 999
		table.insert(attr_list, temp)
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	capability = AttributeMgr.GetCapability(attribute)
	return attr_list, capability
end

function MultiFunctionWGData:IsDaoHangNoWearEquip()
	local suit_equip_list = self:GetDaoHangSlotItemData()
	if IsEmptyTable(suit_equip_list) then return true end

	for k, v in pairs(suit_equip_list) do
		if v.item_id > 0 then
			return false
		end
	end

	return true
end

-- 规则 品质  星级  品质大于星级
function MultiFunctionWGData:CanDaoHangSuitItemUp(item_id)
	local cfg = self:GetDaoHangEquipByItemId(item_id)
	
	if IsEmptyTable(cfg) then
		return false
	else
		local current_data = self:GetDaoHangSlotItemDataBySlot(cfg.slot)
		if current_data.item_id <= 0 then
			return true
		end
 
		local current_data_cfg = self:GetDaoHangEquipByItemId(current_data.item_id)
		local _, item_color = ItemWGData.Instance:GetItemColor(item_id)
		local _, slot_color = ItemWGData.Instance:GetItemColor(current_data.item_id)
		return (item_color > slot_color) or (item_color == slot_color) and (cfg.star_level > current_data_cfg.star_level)
	end
end

-- 石头孔位数据缓存 
function MultiFunctionWGData:CalDaoHangKeLingResonanceNumData()
	local data_list = self:GetDaoHangKelingItemDataList()
	local daohang_keling_gongming_num_cache = {}

	if not IsEmptyTable(data_list) then
		for k, v in pairs(data_list) do
			if v.item_id > 0 then
				local store_cfg = self:GetDaoHangKeLingStoreCfg(v.item_id)
				if not IsEmptyTable(store_cfg) then
					local level = store_cfg.level
					for i = level, 1, -1 do
						daohang_keling_gongming_num_cache[i] = daohang_keling_gongming_num_cache[i] or 0
						daohang_keling_gongming_num_cache[i] = daohang_keling_gongming_num_cache[i] + 1
					end
				end
			end
		end
	end

	self.daohang_keling_gongming_num_cache = daohang_keling_gongming_num_cache

	self:CalDaoHangKeLingResonanceCache()
end

function MultiFunctionWGData:GetDaoHangKeLingGoMingNumByLevel(level)
	return self.daohang_keling_gongming_num_cache[level] or 0
end

function MultiFunctionWGData:CalDaoHangKeLingResonanceCache()
	local resonance_data = {}
	local daohang_keling_gongming_remind = false

	local function get_kelin_attr_data(data)
		local data_list = {}

		if data.equip_add_attack_per_name ~= "" and data.equip_add_attack_per > 0 then
			local t = Split(data.equip_add_attack_per_name, "|")
			for i, v in ipairs(t) do
				local value_str = data.equip_add_attack_per / 10 .. "%"
				table.insert(data_list, {attr_name = v, value_str = value_str})
			end
		end

		if data.equip_add_defense_per_name ~= "" and data.equip_add_defense_per > 0 then
			local t = Split(data.equip_add_defense_per_name, "|")
			for i, v in ipairs(t) do
				local value_str = data.equip_add_defense_per / 10 .. "%"
				table.insert(data_list, {attr_name = v, value_str = value_str})
			end
		end

		if data.equip_add_magic_per_name ~= "" and data.equip_add_magic_per > 0 then
			local t = Split(data.equip_add_magic_per_name, "|")
			for i, v in ipairs(t) do
				local value_str = data.equip_add_magic_per / 10 .. "%"
				table.insert(data_list, {attr_name = v, value_str = value_str})
			end
		end

		return data_list
	end

	local function get_kelin_gongming_data(data_list, level, num, callback)
		local cur_data = {}
		local next_data = {}

		for i = 1, #data_list do
			if level < data_list[i].level then
				next_data = data_list[i]
				break
			end
	
			cur_data = data_list[i]
		end

		local cur_level_desc = ""
		local no_cur_attr = IsEmptyTable(cur_data)
		local cur_attr_data = {}
		local no_cur_attr_desc = Language.Charm.DaoHangKeLingNoCurResonanceDesc

		if not no_cur_attr then
			cur_level_desc = string.format(Language.Charm.DaoHangKeLingResonanceCurTitle, num, cur_data.level)
			-- cur_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cur_data, "attr_id", "attr_value")
			cur_attr_data = get_kelin_attr_data(cur_data)
		end

		local next_level_desc = ""
		local no_next_attr = IsEmptyTable(next_data)
		local next_attr_data = {}
		local no_next_attr_desc = Language.Charm.DaoHangKeLingMaxResonanceDesc
		local remind = false
		if not no_next_attr then
			local has_num = self:GetDaoHangKeLingGoMingNumByLevel(next_data.level)
			local color = has_num >= num and COLOR3B.D_GREEN or COLOR3B.D_RED
			next_level_desc = string.format(Language.Charm.DaoHangKeLingResonanceNextTitle, num, next_data.level, color, has_num, num)
			-- next_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(next_data, "attr_id", "attr_value")
			next_attr_data = get_kelin_attr_data(next_data)
			remind = has_num >= num
		end

		local show_active_btn = true
		local is_max_level = no_next_attr

		if remind then
			daohang_keling_gongming_remind = true
		end

		local temp = self:GetCommonResonanceData()
		temp.top_title = Language.Charm.DaoHangKeLingResonanceTitle
		temp.cur_level_desc = cur_level_desc
		temp.no_cur_attr = no_cur_attr
		temp.no_cur_attr_desc = no_cur_attr_desc
		temp.cur_attr_data = cur_attr_data
		temp.next_level_desc = next_level_desc
		temp.no_next_attr = no_next_attr
		temp.no_next_attr_desc = no_next_attr_desc
		temp.next_attr_data = next_attr_data
		temp.show_active_btn = show_active_btn
		temp.remind = remind
		temp.is_max_level = is_max_level
		temp.click_callback = callback

		return temp
	end

	local four_resonance_data = get_kelin_gongming_data(self.daohang_keling_gongming_four_cfg, self.inlay_four_gongming, 4, function ()
		MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.TAOIST_INLAY_TYPE_GONGMING, 4)
	end)
	table.insert(resonance_data, four_resonance_data)

	local eight_resonance_data = get_kelin_gongming_data(self.daohang_keling_gongming_eight_cfg, self.inlay_eight_gongming, 8, function ()
		MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.TAOIST_INLAY_TYPE_GONGMING, 8)
	end)
	table.insert(resonance_data, eight_resonance_data)

	local tw_resonance_data = get_kelin_gongming_data(self.daohang_keling_gongming_twelve_cfg, self.inlay_tw_gongming, 12, function ()
		MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.TAOIST_INLAY_TYPE_GONGMING, 12)
	end)
	table.insert(resonance_data, tw_resonance_data)

	self.daohang_keling_gongming_data_cache = resonance_data
	self.daohang_keling_gongming_remind = daohang_keling_gongming_remind
end

function MultiFunctionWGData:GetCommonResonanceData()
    local resonance_data_left = 
	{
		top_title = "",
		cur_level_desc = "",
		no_cur_attr = false,
		no_cur_attr_desc = "",
		cur_attr_data = {},
		next_level_desc = "",
		no_next_attr = false,
		no_next_attr_desc = "",
		next_attr_data = {},
		show_active_btn = false,
		remind = false,
		is_max_level = false,
		click_callback = nil,
	}

    return resonance_data_left
end

function MultiFunctionWGData:GetDaoHangQiangHuaResonanceData()
	local resonance_data = {}

	local cur_data = {}
	local next_data = {}
	local data_cfg = self:GetDaoHangQiangHuaResonanceCfg()
	local attr_len = #data_cfg
	local qianghua_resonance_level = self:GetDaoHangQiangHuaResonanceLevel()

	for i = 1, attr_len do
		if self.enhance_gongming_level < data_cfg[i].level then
			next_data = data_cfg[i]
			break
		end

		cur_data = data_cfg[i]
	end

	local cur_level_desc = ""
	local no_cur_attr = IsEmptyTable(cur_data)
	local cur_attr_data = {}
	local no_cur_attr_desc = Language.Charm.DaoHangQiangHuaNoCurResonanceDesc

	if not no_cur_attr then
		cur_level_desc = string.format(Language.Charm.DaoHangQiangHuaResonanceCurTitle, cur_data.level)
		cur_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cur_data, "attr_id", "attr_value")
	end

	local next_level_desc = ""
	local no_next_attr = IsEmptyTable(next_data)
	local next_attr_data = {}
	local no_next_attr_desc = Language.Charm.DaoHangQiangHuaMaxResonanceDesc
	local remind = false
	if not no_next_attr then
		local color = qianghua_resonance_level >= next_data.level and COLOR3B.D_GREEN or COLOR3B.D_RED
		next_level_desc = string.format(Language.Charm.DaoHangQiangHuaResonanceNextTitle, next_data.level, color, qianghua_resonance_level, next_data.level)
		next_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(next_data, "attr_id", "attr_value")

		remind = qianghua_resonance_level >= next_data.level
	end

	local show_active_btn = true
	local is_max_level = no_next_attr

	local temp = self:GetCommonResonanceData()
	temp.top_title = Language.Charm.DaoHangQiangHuaResonanceTips
	temp.cur_level_desc = cur_level_desc
	temp.no_cur_attr = no_cur_attr
	temp.no_cur_attr_desc = no_cur_attr_desc
	temp.cur_attr_data = cur_attr_data
	temp.next_level_desc = next_level_desc
	temp.no_next_attr = no_next_attr
	temp.no_next_attr_desc = no_next_attr_desc
	temp.next_attr_data = next_attr_data
	temp.show_active_btn = show_active_btn
	temp.remind = remind
	temp.is_max_level = is_max_level
	temp.click_callback = function ()
		MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.ENHANCE_GONGMING)
	end
	
	table.insert(resonance_data, temp)
	return resonance_data
end

function MultiFunctionWGData:GetDaoHangKeYinResonanceData()
	local resonance_data = {}
	local cur_data = {}
	local next_data = {}
	local data_cfg = self:GetDaoHangKeYinResonanceCfg()
	local attr_len = #data_cfg
	local qianghua_resonance_level = self:GetDaoHangKeYinResonanceLevel()

	for i = 1, attr_len do
		if self.carve_gongming_level < data_cfg[i].level then
			next_data = data_cfg[i]
			break
		end

		cur_data = data_cfg[i]
	end

	local cur_level_desc = ""
	local no_cur_attr = IsEmptyTable(cur_data)
	local cur_attr_data = {}
	local no_cur_attr_desc = Language.Charm.DaoHangQiangHuaNoCurResonanceDesc

	if not no_cur_attr then
		cur_level_desc = string.format(Language.Charm.DaoHangQiangHuaResonanceCurTitle, cur_data.level)
		cur_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cur_data, "attr_id", "attr_value")
	end

	local next_level_desc = ""
	local no_next_attr = IsEmptyTable(next_data)
	local next_attr_data = {}
	local no_next_attr_desc = Language.Charm.DaoHangQiangHuaMaxResonanceDesc
	local remind = false
	if not no_next_attr then
		local color = qianghua_resonance_level >= next_data.level and COLOR3B.D_GREEN or COLOR3B.D_RED
		next_level_desc = string.format(Language.Charm.DaoHangQiangHuaResonanceNextTitle, next_data.level, color, qianghua_resonance_level, next_data.level)
		next_attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(next_data, "attr_id", "attr_value")

		remind = qianghua_resonance_level >= next_data.level
	end

	local show_active_btn = true
	local is_max_level = no_next_attr

	local temp = self:GetCommonResonanceData()
	temp.top_title = Language.Charm.DaoHangKeYinResonanceTips
	temp.cur_level_desc = cur_level_desc
	temp.no_cur_attr = no_cur_attr
	temp.no_cur_attr_desc = no_cur_attr_desc
	temp.cur_attr_data = cur_attr_data
	temp.next_level_desc = next_level_desc
	temp.no_next_attr = no_next_attr
	temp.no_next_attr_desc = no_next_attr_desc
	temp.next_attr_data = next_attr_data
	temp.show_active_btn = show_active_btn
	temp.remind = remind
	temp.is_max_level = is_max_level
	temp.click_callback = function ()
		MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.CARVE_GONGMING)
	end
	
	table.insert(resonance_data, temp)
	return resonance_data
end

function MultiFunctionWGData:GetDaoHangQianHuaSelect(cur_select_slot, cur_select_index)
	if cur_select_slot >= 0 then
		if self:GetDaoHangQiangHuaEquipRemindByslot(cur_select_slot) then
			return cur_select_index
		end
	end

	for k, v in pairs(self:GetDaoHangEquipedEquip()) do
		local remind = self:GetDaoHangQiangHuaEquipRemindByslot(v.slot)

		if remind then
			return k
		end
	end
	
	return cur_select_index
end

function MultiFunctionWGData:GetDaoHangQiangHuaResonanceRemind()
	local data_cfg = self:GetDaoHangQiangHuaResonanceCfg()

	for i = 1, #data_cfg do
		if self.enhance_gongming_level < data_cfg[i].level then
			if self:GetDaoHangQiangHuaResonanceLevel() >= data_cfg[i].level then
				return true
			end

			break
		end
	end

	return false
end

function MultiFunctionWGData:GetDaoHangKeYinResonanceRemind()
	local data_cfg = self:GetDaoHangKeYinResonanceCfg()

	for i = 1, #data_cfg do
		if self.carve_gongming_level < data_cfg[i].level then
			if self:GetDaoHangKeYinResonanceLevel() >= data_cfg[i].level then
				return true
			end

			break
		end
	end

	return false
end

function MultiFunctionWGData:GetDaoHangkaiGuangAttrLimitCfg(quality)
	return self.daohang_attr_limit_cfg[quality]
end

function MultiFunctionWGData:GetDaoHangKaiGuangAttrRange(quality, value)
	local range = -1
	local quality_cfg = self:GetDaoHangkaiGuangAttrLimitCfg(quality)
	if not IsEmptyTable(quality_cfg) then
		for i = 1, 5 do
			if value < quality_cfg["range_min" .. i] then
				break
			end

			range = i
		end
	end

	return range
end

function MultiFunctionWGData:GetDaoHangkaiGuangValueChangeDesc(slot)
	local cap_desc = ""
	local cur_cap = 0
	local next_cap = 0

	local data_info = self:GetDaoHangSlotItemDataBySlot(slot)
	local cur_attr = data_info.per_kaiguang_attr_value_list
	local change_value = data_info.per_kaiguang_attr_value_list_temp
	local quality_cfg = self:GetDaoHangkaiGuangAttrLimitCfg(data_info.color)

	local cur_attribute = AttributePool.AllocAttribute()
	local next_attribute = AttributePool.AllocAttribute()

	for i = 0, 4 do
		local attr_cfg = self:GetDaoHangKaiGuangAttrCfg(slot, i)
		local attr_id = attr_cfg.attr_id
		local attr_cur_value = attr_cfg.attr_val * (cur_attr[i] / quality_cfg.limit)
		local attr_next_value = attr_cfg.attr_val * ((cur_attr[i] + change_value[i]) / quality_cfg.limit)
		
		if attr_id and attr_id > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))

			if attr_cur_value > 0 and cur_attribute[attr_str] then
				cur_attribute[attr_str] = cur_attribute[attr_str] + attr_cur_value
			end
			
			if attr_next_value > 0 and next_attribute[attr_str] then
				next_attribute[attr_str] = next_attribute[attr_str] + attr_next_value
			end
		end
	end

	cur_cap = AttributeMgr.GetCapability(cur_attribute)
	next_cap = AttributeMgr.GetCapability(next_attribute)
	local is_add = next_cap > cur_cap
	local diff_value = math.floor(next_cap - cur_cap)
	local color = is_add and COLOR3B.GREEN or COLOR3B.RED
	local cap_value = (is_add and "+ " or "- ") .. math.abs(diff_value)
	cap_desc = ToColorStr(cap_value, color)
	
	return is_add, cap_desc
end

function MultiFunctionWGData:GetDaoHangFashionAllShowItem()
	local model_item_list = {}
	local suit_icon_list = {}
	local skill_data_list = {}
	local active_num = 0
	local normal_data_list = self:GetDaoHangSuitApparelItemCfg()

	for k, v in pairs(normal_data_list) do
		if v.item_id and v.item_id > 0 then
			model_item_list[v.item_id] = true

			local fashion_type = v.fashion_type
			local active = false
			
			if fashion_type == 1 then
				active = NewAppearanceWGData.Instance:GetFashionIsActByItemId(v.item_id)
			else
				active = NewAppearanceWGData.Instance:GetQiChongIsActByItemId(v.item_id)
			end

			table.insert(suit_icon_list, {active = active, name = v.name, icon = v.icon})
			
			if active then
				active_num = active_num + 1
			end
		end
	end

	local other_cfg = self:GetDaoHangOtherCfg()
	local special_item_id = other_cfg and other_cfg.rmb_fashion

	if special_item_id and special_item_id > 0 then
		model_item_list[special_item_id] = true

		local fashion_type = other_cfg.fashion_type
		local active = false
		
		if fashion_type == 1 then
			active = NewAppearanceWGData.Instance:GetFashionIsActByItemId(special_item_id)
		else
			active = NewAppearanceWGData.Instance:GetQiChongIsActByItemId(special_item_id)
		end

		table.insert(suit_icon_list, {active = active, name = other_cfg.rmb_fashion_name, icon = other_cfg.rmb_fashion_icon})

		if active then
			active_num = active_num + 1
		end
	end

	for i = 1, 3 do
		local target_item_id = other_cfg["rmb_fashion" .. i]

		if target_item_id and target_item_id > 0 then
			model_item_list[target_item_id] = true
			local fashion_type = other_cfg["fashion_type" .. i]
			local active = false
		
			if fashion_type == 1 then
				active = NewAppearanceWGData.Instance:GetFashionIsActByItemId(target_item_id)
			else
				active = NewAppearanceWGData.Instance:GetQiChongIsActByItemId(target_item_id)
			end

			table.insert(suit_icon_list, {active = active, name = other_cfg["rmb_fashion_name" .. i], icon = other_cfg["rmb_fashion_icon" .. i]})

			if active then
				active_num = active_num + 1
			end
		end
	end

	local skill_cfg = self:GetDaoHangFashionSkillCfg()
	for k, v in pairs(skill_cfg) do
		table.insert(skill_data_list, {fashion_num = v.fashion_num, fashion_icon = v.fashion_icon,
		 fashion_txt = v.fashion_txt, active_num = active_num})
	end

	return model_item_list, suit_icon_list, skill_data_list
end

function MultiFunctionWGData:IsDaoHangComposeItem(change_item_id)
	return (change_item_id > 0) and (nil ~= self.daohang_compos_stuff_cfg[change_item_id])
end


function MultiFunctionWGData:GetDaoHangKeLingCap()
	local cap = 0
	local data_list = self:GetDaoHangKelingItemDataList()
	if IsEmptyTable(data_list) then
		return cap
	end

	local base_attribute = AttributePool.AllocAttribute()
	for k, v in pairs(data_list) do
		if v.is_open and v.item_id > 0 then
			local store_cfg = self:GetDaoHangKeLingStoreCfg(v.item_id)

			if not IsEmptyTable(store_cfg) then
				base_attribute = base_attribute + self:GetAttrInfo(store_cfg)
			end
		end
	end

	-- local function get_kelin_gongming_data(data_list, level)
	-- 	local cur_data = {}

	-- 	for i = 1, #data_list do
	-- 		if level < data_list[i].level then
	-- 			break
	-- 		end
	
	-- 		cur_data = data_list[i]
	-- 	end

	-- 	return cur_data
	-- end

	-- local four_resonance_data = get_kelin_gongming_data(self.daohang_keling_gongming_four_cfg, self.inlay_four_gongming)
	-- if not IsEmptyTable(four_resonance_data) then
	-- 	base_attribute = base_attribute + self:GetAttrInfo(four_resonance_data)
	-- end

	-- local eight_resonance_data = get_kelin_gongming_data(self.daohang_keling_gongming_eight_cfg, self.inlay_eight_gongming)
	-- if not IsEmptyTable(eight_resonance_data) then
	-- 	base_attribute = base_attribute + self:GetAttrInfo(eight_resonance_data)
	-- end

	-- local tw_resonance_data = get_kelin_gongming_data(self.daohang_keling_gongming_twelve_cfg, self.inlay_tw_gongming)
	-- if not IsEmptyTable(tw_resonance_data) then
	-- 	base_attribute = base_attribute + self:GetAttrInfo(tw_resonance_data)
	-- end

	cap = cap + AttributeMgr.GetCapability(base_attribute)

	return cap
end

function MultiFunctionWGData:GetDaoHangQiangHuaCap()
	local cap = 0
	local data_list = self:GetDaoHangEquipedEquip()

	if IsEmptyTable(data_list) then
		return cap
	end

	-- cap = cap + self:GetAllEquipEquipBaseCap()

	local base_attribute = AttributePool.AllocAttribute()
	for k, v in pairs(data_list) do
		if v.level > 0 then
			local level_cfg = self:GetDaoHangSlotEnhanceCfgBySlotAndLevel(v.slot, v.level)
			if not IsEmptyTable(level_cfg) then
				base_attribute = base_attribute + self:GetAttrInfo(level_cfg)
			end
		end
	end

	local cur_data = {}
	local data_cfg = self:GetDaoHangQiangHuaResonanceCfg()
	local attr_len = #data_cfg

	for i = 1, attr_len do
		if self.enhance_gongming_level < data_cfg[i].level then
			break
		end

		cur_data = data_cfg[i]
	end

	if not IsEmptyTable(cur_data) then
		base_attribute = base_attribute + self:GetAttrInfo(cur_data)
	end

	cap = cap + AttributeMgr.GetCapability(base_attribute)
	
	return cap
end

function MultiFunctionWGData:GetDaoHangJinJieCap()
	local cap = 0
	local data_list = self:GetDaoHangEquipedEquip()
	if IsEmptyTable(data_list) then
		return cap
	end

	-- cap = cap + self:GetAllEquipEquipBaseCap()
	local base_attribute = AttributePool.AllocAttribute()
	for k, v in pairs(data_list) do
		if v.grade > 0 then
			local cur_grade_cfg =self:GetDaoHangSlotAdvanceCfgBySlotAndLevel(v.slot, v.grade)
			if not IsEmptyTable(cur_grade_cfg) then
				base_attribute = base_attribute + self:GetAttrInfo(cur_grade_cfg)
			end
		end
	end

	cap = cap + AttributeMgr.GetCapability(base_attribute)

	return cap
end

function MultiFunctionWGData:GetDaoHangKeYinCap()
	local cap = 0
	local data_list = self:GetDaoHangEquipedEquip()
	if IsEmptyTable(data_list) then
		return cap
	end

	-- cap = cap + self:GetAllEquipEquipBaseCap()
	local base_attribute = AttributePool.AllocAttribute()
	for k, v in pairs(data_list) do
		for i = 1, 3 do
			local level = ((v.carve_item_list or {})[i] or {}).level or 0
			if level > 0 then
				local cur_level_cfg = self:GetDaoHangSlotCarveCfg(v.slot, i, level)
				if not IsEmptyTable(cur_level_cfg) then
					base_attribute = base_attribute + self:GetAttrInfo(cur_level_cfg)
				end
			end
		end
	end

	local cur_data = {}
	local data_cfg = self:GetDaoHangKeYinResonanceCfg()
	local attr_len = #data_cfg
	for i = 1, attr_len do
		if self.carve_gongming_level < data_cfg[i].level then
			break
		end

		cur_data = data_cfg[i]
	end

	if not IsEmptyTable(cur_data) then
		base_attribute = base_attribute + self:GetAttrInfo(cur_data)
	end

	cap = cap + AttributeMgr.GetCapability(base_attribute)

	return cap
end

function MultiFunctionWGData:GetAttrInfo(data, data_len)
	local attr_list = {}
	local attr_id, attr_value = 0, 0
	local data_len = data_len or 5

	for i = 1, data_len do
		attr_id = data["attr_id" .. i]
		attr_value = data["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			attr_list[attr_str] = attr_value
		end
	end

	return attr_list
end

function MultiFunctionWGData:GetDaoHangKaiGuangCap()
	local cap = 0
	local data_list = self:GetDaoHangEquipedEquip()

	if IsEmptyTable(data_list) then
		return cap
	end

	-- cap = cap + self:GetAllEquipEquipBaseCap()
	local base_attribute = AttributePool.AllocAttribute()
	local attr_info_list = {}
	for k, v in pairs(data_list) do
		local quality_cfg = self:GetDaoHangkaiGuangAttrLimitCfg(v.color)
		local limit_value = quality_cfg.limit

		for i = 0, 4 do
			local attr_value = (v.per_kaiguang_attr_value_list or {})[i] or 0
			if attr_value > 0 then
				local attr_cfg = self:GetDaoHangKaiGuangAttrCfg(v.slot, i)

				if not IsEmptyTable(attr_cfg) then
					local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_cfg.attr_id))
					local value = attr_value / limit_value
					local attr_value = math.ceil(attr_cfg.attr_val * value)

					if base_attribute[attr_str] then
						base_attribute[attr_str] = base_attribute[attr_str] + attr_value
					end
				end
			end
		end
	end

	cap = cap + AttributeMgr.GetCapability(base_attribute)

	return cap
end

function MultiFunctionWGData:GetAllEquipEquipBaseCap()
	local cap = 0
	local data_list = self:GetDaoHangEquipedEquip()

	if IsEmptyTable(data_list) then
		return cap
	end

	for k, v in pairs(data_list) do
		cap = cap + self:GetDaoHangEquipCapByItemId(v.item_id)
	end

	return cap
end

function MultiFunctionWGData:GetDaoHangEquipCapByItemId(item_id)
	local _, cap = {}, 0
	if not self:IsDaoHangEquip(item_id) then
		return cap
	end

	local cfg = self:GetDaoHangEquipByItemId(item_id)
	_, cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cfg, "attr_id", "attr_value")

	return cap
end

function MultiFunctionWGData:GetDaoHangSuitShowApparelAct(type, item_id)
	if type == 1 then
		return NewAppearanceWGData.Instance:GetFashionIsActByItemId(item_id)
	else
		return NewAppearanceWGData.Instance:GetQiChongIsActByItemId(item_id)
	end 
end

function MultiFunctionWGData:GetDaoHangSuitShowApparelItemCfg()
	local data_list = {}

	for k, v in pairs(self:GetDaoHangSuitApparelItemCfg()) do
		local item_id = v.item_id
        local act = self:GetDaoHangSuitShowApparelAct(v.fashion_type, item_id)

		table.insert(data_list, {item_id = item_id, act = act})
	end

	local other_cfg = self:GetDaoHangOtherCfg()
	if other_cfg then
		if other_cfg.rmb_fashion then
			local target_item_id = other_cfg.rmb_fashion
			local act = self:GetDaoHangSuitShowApparelAct(other_cfg.fashion_type, target_item_id)
			table.insert(data_list, {item_id = target_item_id, act = act})
		end

		for i = 1, 3 do
			local apprance_item_id = other_cfg["rmb_fashion" ..i]
			local apprance_fashion_type = other_cfg["fashion_type" ..i]
			if apprance_item_id and apprance_item_id > 0 and apprance_fashion_type then
				local act = self:GetDaoHangSuitShowApparelAct(apprance_fashion_type, apprance_item_id)
				table.insert(data_list, {item_id = apprance_item_id, act = act})
			end
		end
	end

	return data_list
end
----------------------------------------------------Cal_End---------------------------------------------