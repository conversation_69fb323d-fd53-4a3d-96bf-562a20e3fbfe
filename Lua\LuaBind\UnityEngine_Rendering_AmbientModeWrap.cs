﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Rendering_AmbientModeWrap
{
	public static void Register(LuaState L)
	{
		L.BeginEnum(typeof(UnityEngine.Rendering.AmbientMode));
		<PERSON><PERSON>("Skybox", get_Skybox, null);
		<PERSON><PERSON>("Trilight", get_Trilight, null);
		<PERSON><PERSON>("Flat", get_Flat, null);
		<PERSON><PERSON>("Custom", get_Custom, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		<PERSON>.End<PERSON>();
		TypeTraits<UnityEngine.Rendering.AmbientMode>.Check = CheckType;
		StackTraits<UnityEngine.Rendering.AmbientMode>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.Rendering.AmbientMode arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.Rendering.AmbientMode), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Skybox(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Rendering.AmbientMode.Skybox);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Trilight(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Rendering.AmbientMode.Trilight);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Flat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Rendering.AmbientMode.Flat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Custom(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Rendering.AmbientMode.Custom);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.Rendering.AmbientMode o = (UnityEngine.Rendering.AmbientMode)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

