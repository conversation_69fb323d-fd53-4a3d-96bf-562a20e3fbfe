KFZhuXieFollowView = KFZhuXieFollowView or BaseClass(SafeBaseView)

KFZhuXieFollowView.TaskType ={
	Gather = 1,
	Monster = 2,
	Role = 3,
}

local TaskType_Role = 5

KFZhuXieFollowView.AutoAcceptTaskIndex = -1
function KFZhuXieFollowView:__init()
	self:AddViewResource(0, "uis/view/zhuxie_ui_prefab", "ZhuxiePanel")
	self.view_layer = UiLayer.MainUI
	self.act_state = nil
end

function KFZhuXieFollowView:ReleaseCallBack()
	if self.task_list_view then
		self.task_list_view:DeleteMe()
		self.task_list_view = nil
	end

	if self.hurt_list_view then
		self.hurt_list_view:DeleteMe()
		self.hurt_list_view = nil
	end

	if CountDownManager.Instance:HasCountDown(self.countdown_str) then
		CountDownManager.Instance:RemoveCountDown(self.countdown_str)
	end

	if CountDownManager.Instance:HasCountDown(self.zhuxie_act_countdown) then
		CountDownManager.Instance:RemoveCountDown(self.zhuxie_act_countdown)
	end
	if self.be_select_event then
		GlobalEventSystem:UnBind(self.be_select_event)
		self.be_select_event = nil
	end

	if self.arrow_click_event then
		GlobalEventSystem:UnBind(self.arrow_click_event)
		self.arrow_click_event = nil
	end

	if self.touch_move_event then
		GlobalEventSystem:UnBind(self.touch_move_event)
		self.touch_move_event = nil
	end

	if self.role_click_move_event then
		GlobalEventSystem:UnBind(self.role_click_move_event)
		self.role_click_move_event = nil
	end

	if self.rank_reward_list then
		for k,v in pairs(self.rank_reward_list) do
			v:DeleteMe()
		end
	end
	self.rank_reward_list = nil


	if self.boss_reward_list then
		for k,v in pairs(self.boss_reward_list) do
			v:DeleteMe()
		end
	end
	self.boss_reward_list = nil

	if self.zx_tween_sequence then
		self.zx_tween_sequence:Kill()
		self.zx_tween_sequence = nil
	end

	self:DeleteBossStone()
	self.is_load_succe = nil
	self.monster_cfg = nil
	self.is_show_one = nil
	self.is_show_two = nil

	if ActivityWGData.Instance then
		ActivityWGData.Instance:SetCurTaskid(nil)
	end

	self.act_state = nil
	self.boss_head_cache = nil
end

function KFZhuXieFollowView:LoadCallBack()
	self.is_load_succe = true
	self.cur_endtime = 0
	self.is_show_tip_1 = false
	self.is_show_tip_2 = false
	self.is_show_tip_3 = false
	-- XUI.AddClickEventListener(self.node_list["TaskButton"],BindTool.Bind(self.OnClickTask, self))
 --    XUI.AddClickEventListener(self.node_list["BossButton"],BindTool.Bind(self.OnClickBoss, self))

    self.node_list.Boss_Btn.button:AddClickListener(BindTool.Bind(self.OnClickGoAttackBoss, self))--去攻击boos
    self.node_list["TargetHp"].button:AddClickListener(BindTool.Bind(self.OnClickGoAttackBoss, self))
    self.node_list["flush_img"].button:AddClickListener(BindTool.Bind(self.OnClickGoAttackBoss, self))
    -- self.node_list["Btn_item_show"].button:AddClickListener(BindTool.Bind(self.OnClickOpenReward, self))
    XUI.AddClickEventListener(self.node_list.btn_reward, BindTool.Bind1(self.OnClickOpenRewardView,self))

    self.arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.TopPanelAniCallBack, self))
    self.touch_move_event = GlobalEventSystem:Bind(LayerEventType.TOUCH_MOVED, BindTool.Bind(self.StopAutoTask, self))
	self.be_select_event = GlobalEventSystem:Bind(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObjCallBack, self))
    self.role_click_move_event = GlobalEventSystem:Bind(LayerEventType.SCENE_CLICK_FLOOR, BindTool.Bind(self.StopAutoTask, self))

    self.task_list_view = AsyncListView.New(KFZhuXieFollowTask,self.node_list.TaskList)
	self.hurt_list_view = AsyncListView.New(KFZhuXieHurtRankView,self.node_list.rank_list_view)
	self.task_list_view:SetSelectCallBack(BindTool.Bind(self.TaskCellCallBack,self))
	self:FlushKFZhuXieFollowView()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

end

function KFZhuXieFollowView:StopAutoTask()
	ActivityWGData.Instance:SetCurTaskid(nil)
	ActivityWGCtrl.Instance:KFZhuXieFollowViewFlush(0, "select_change")
end

function KFZhuXieFollowView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "select_change" then
			local all_item = self.task_list_view:GetAllItems()
			for k,v in pairs(all_item) do
				v:AutoChange()
			end
		end
	end
end

function KFZhuXieFollowView:OnSelectObjCallBack(target_obj, select_type)
	local task_cfg = ActivityWGData.Instance:GetKFZhuXieTaskList()
	if target_obj:GetType() == SceneObjType.GatherObj then
		ActivityWGData.Instance:SetCurTaskid(1)
		ActivityWGCtrl.Instance:KFZhuXieFollowViewFlush(0, "select_change")
	elseif target_obj:GetType() == SceneObjType.Monster then
		for k,v in pairs(task_cfg) do
			if v.param_id == target_obj.vo.monster_id then
				ActivityWGData.Instance:SetCurTaskid(v.cfg.task_id)
				ActivityWGCtrl.Instance:KFZhuXieFollowViewFlush(0, "select_change")
			end
		end
	elseif target_obj:GetType() == SceneObjType.Role then
		ActivityWGData.Instance:SetCurTaskid(5)
		ActivityWGCtrl.Instance:KFZhuXieFollowViewFlush(0, "select_change")
	end
end

function KFZhuXieFollowView:TopPanelAniCallBack(is_on)
    if nil == self.node_list.right_parent then return end
    local max_move ,min_move = 351 , 61
    local move_vaule = is_on == true and max_move or min_move
    local tween = self.node_list.right_parent.rect:DOAnchorPosX(move_vaule, 1)
    tween:SetEase(DG.Tweening.Ease.OutBack)
    tween:OnUpdate(function()
        self.node_list.right_parent.canvas_group.alpha = ((max_move - min_move) - (self.node_list.right_parent.rect.anchoredPosition.x - min_move)) / (max_move - min_move)
    end)
end

function KFZhuXieFollowView:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KF_ZHUXIE and status == ACTIVITY_STATUS.OPEN then
		-- self:XuanZeTask()
	end
end

--boss刷新动画
function KFZhuXieFollowView:BossFlushAnimation()
	self.node_list["Boss_head"].transform.anchoredPosition = Vector2(-590,0)
	self.node_list["Boss_head"].transform.localScale = Vector3(2,2,2)
	local bossfluah_animator = self.node_list["Boss_head"].rect:DOScale(Vector3(1, 1, 1), 1)
	if self.zx_tween_sequence then
		self.zx_tween_sequence:Kill()
		self.zx_tween_sequence = nil
	end

	local tween_sequence = DG.Tweening.DOTween.Sequence()
	tween_sequence:Join(bossfluah_animator)
	GlobalTimerQuest:AddDelayTimer(function ()
		local boss_head_move_animator = self.node_list["Boss_head"].rect:DOAnchorPosX(0, 1)
		boss_head_move_animator:SetEase(DG.Tweening.Ease.Linear)
	end, 1.5)

	self.zx_tween_sequence = tween_sequence
end

function KFZhuXieFollowView:OnClickOpenReward()
	self:OnClickGoAttackBoss()
end

--自动点击未完成的任务
function KFZhuXieFollowView:OnClickTaskCell(task_id)
	if not task_id then
		return
	end
	local task_data_list = ActivityWGData.Instance:GetKFZhuXieTaskList()
	local task_flag = false
	for k,v in pairs(task_data_list) do
		if v.cfg.task_id == task_id and self.task_list_view then
			local task_info = ActivityWGData.Instance:GetKFZhuXieTaskInfoByTaskId(task_id)
			if task_info and task_info.is_fetched == 0 then
				task_flag = true
				self:OnCellData(v)
				return
			end
		end
	end

	if not task_flag then
		local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo() or {}
		local task_id = ActivityWGData.Instance:GetKFZhuXieANotCompleteTaskID()

		if boss_info and boss_info.boss_id and boss_info.boss_id > 0 then
			if boss_info.boss_cur_hp <= 0 and task_id ~= nil then
				GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
				GuajiCache.monster_id = boss_info.boss_id
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Monster)
			else
				local kill_role_task = ActivityWGData.Instance:GetKFZhuXieTaskInfoByTaskId(5)
				if kill_role_task and kill_role_task.is_fetched == 0 then
					self:FindRoleTarget()
				end
			end
		end
	end
end


--自动选择任务
function KFZhuXieFollowView:XuanZeTask()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ZHUXIE)
	if nil ~= activity_info then
		local flag = activity_info.status == ACTIVITY_STATUS.STANDY
		if flag then
			return
		end
	end

	-- 判断boss存在直接挂机
	local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo() or {}
	if IsEmptyTable(boss_info) then
		return
	end

	if boss_info.boss_id ~= nil and boss_info.boss_cur_hp > 0 then
		local scene_id = Scene.Instance:GetSceneId()
		local other_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").other[1]
		local pos_list = Split(other_cfg.boss_pos, ",")
		if other_cfg ~= nil and not IsEmptyTable(pos_list) then
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)

			MoveCache.SetEndType(MoveEndType.FightByMonsterId)
			MoveCache.param1 = boss_info.boss_id
			GuajiCache.monster_id = boss_info.boss_id
			local range = BossWGData.Instance:GetMonsterRangeByid(boss_info.boss_id)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_list[1], pos_list[2], range)
		end
		return
	end

	if self:CheckFallItem() then
		return
	end

	local task_id = ActivityWGData.Instance:GetCurTaskid()
	if task_id == nil then
		--获取一个没做完的任务id（不包括杀人任务）
		task_id = ActivityWGData.Instance:GetKFZhuXieANotCompleteTaskID()
		if task_id == nil then
			--杀人任务
			local kill_role_task = ActivityWGData.Instance:GetKFZhuXieTaskInfoByTaskId(5)
			if kill_role_task and kill_role_task.is_fetched == 0 then
				task_id = 5
			else
				--随便给一个任务（不要给采集）
				task_id = 3
			end

		end
		self:OnClickTaskCell(task_id)
		return
	else
		local task_info = ActivityWGData.Instance:GetKFZhuXieTaskInfoByTaskId(task_id) or {}
		local param_value = task_info.param_value or 0
		local max_value = task_info.cfg and task_info.cfg.param_max_value or 0
		local is_fetched = task_info.is_fetched or 0
		if (is_fetched == 1 and max_value <= param_value) or is_fetched == 1 then
			task_id = ActivityWGData.Instance:GetKFZhuXieANotCompleteTaskID()
			if task_id ~= nil then
				self:OnClickTaskCell(task_id)
			else
				local task_id = ActivityWGData.Instance:GetCurTaskid()
				task_id = task_id == 1 and 2 or task_id
				self:OnClickTaskCell(task_id)
			end
			return
		end
	end
end

function KFZhuXieFollowView:CheckFallItem()
	local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)

    if IsEmptyTable(fall_item_list) then
        return false
    end

	local main_role = Scene.Instance:GetMainRole()

	if not main_role then
		return false
	end

	if main_role then
        if main_role:IsRealDead() then
            return
        end
    end

	for k,v in pairs(fall_item_list) do
		if v:GetVo().owner_role_id <= 0 or v:GetVo().owner_role_id == main_role:GetRoleId()
            or (v:GetVo().lose_owner_time > 0 and v:GetVo().lose_owner_time <= TimeWGCtrl.Instance:GetServerTime()) then
            return true
        end
	end

	return false
end

function KFZhuXieFollowView:FlushKFZhuXieFollowView()
	self:FlushTaskListView()
	self:FlushBossListView()
	self:FlushBossInfo()
end

--刷新整个任务列表
function KFZhuXieFollowView:FlushTaskListView()
	if not self.is_load_succe then return end
	local task_data_list = ActivityWGData.Instance:GetKFZhuXieTaskList()
	if self.task_list_view then
		self.task_list_view:SetDataList(task_data_list)
		self.task_list_view:JumpToTop()
	end
	local completed_num, all_num = ActivityWGData.Instance:GetKfZhuXieCompleteTaskNum()
	local color = completed_num >= all_num and COLOR3B.RED or COLOR3B.WHITE
	-- self.node_list.task_btn_text.text.text = string.format(Language.ZhuXie.TaskBtnText,color,completed_num,all_num)
	-- self.node_list.HL_text.text.text = string.format(Language.ZhuXie.TaskBtnText,color,completed_num,all_num)
	-- self:AutoCompleteTasks()
	self:XuanZeTask()
	local drop_reward_cfg = ActivityWGData.Instance:GetKFZhuXieShowRewardCfg().item
	if not drop_reward_cfg then return end

	-- if not self.boss_reward_list then
	-- 	self.boss_reward_list = {}
	-- 	for k,v in pairs(drop_reward_cfg) do
	-- 		self.boss_reward_list[k] = ItemCell.New()
	-- 		self.boss_reward_list[k]:SetInstanceParent(self.node_list["boss_reward_list"])
	-- 		self.boss_reward_list[k]:SetData(v)
	-- 		self.boss_reward_list[k]:SetTopLeftFlag(true, ResPath.GetLoadingPath("a1_zjm_qbzhenxi"))
	-- 	end
	-- end
end

function KFZhuXieFollowView:AutoCompleteTasks()
	if not self.task_list_view then return end
	local items = self.task_list_view:GetAllItems()
	if items then
		for k,v in pairs(items) do
			v:AutoCompleteTask()
		end
	end
end

function KFZhuXieFollowView:FlushBossListView()
	if not self.is_load_succe then return end
	self.countdown_str = "KFZhuXieFollowBoss_0" --唯一标识
	self.zhuxie_act_countdown = "kf_zhuxie_act_countdown"
	self:ClearCD()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").other[1]
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[other_cfg.boss_id]
	if monster_cfg then
		self.monster_cfg = monster_cfg
		self.node_list.text_name.text.text = monster_cfg.name
	end
	local boss_info = ActivityWGData.Instance:GetKFZhuXieTaskInfo() or {}
	local activity_state = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ZHUXIE)

	self.node_list.right_parent:SetActive(activity_state )--and boss_info.next_boss_refresh_time < TimeWGCtrl.Instance:GetServerTime()
	if boss_info.boss_id ~= nil then
		local icon = ActivityWGData.Instance:GetKFZhuXieBossIconByid(boss_info.boss_id)
		if icon ~= nil then
			local asset_name,bundle_name = ResPath.GetBossIcon("wrod_boss_".. icon)
			self.node_list.boss_head_img.image:LoadSprite(asset_name,bundle_name,function ()
				self.node_list.boss_head_img.image:SetNativeSize()
			end)
		end
	end
	self:FlushBossCountDown()
	self:FlushActivityCountDown()

	if boss_info.boss_id and boss_info.boss_id ~= 0 then
		self:SetBossHp(boss_info.boss_cur_hp,boss_info.boss_max_hp)
		--self.node_list["flush_flag"]:SetActive(boss_info.boss_cur_hp > 0)
		--self.node_list.effect:SetActive(boss_info.boss_cur_hp > 0)
	end

	local rank_cfg = ActivityWGData.Instance:GetKFZhuXieShowRewardCfg().rank_item
	if not rank_cfg then return end

	if not self.rank_reward_list then
		self.rank_reward_list = {}
		for k,v in pairs(rank_cfg) do
			self.rank_reward_list[k] = ItemCell.New()
			-- local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			self.rank_reward_list[k]:SetInstanceParent(self.node_list["rank_reward_list"])
			self.rank_reward_list[k]:SetData(v)
			self.rank_reward_list[k]:SetTopLeftFlag(true, ResPath.GetLoadingPath("a3_ty_bq_z"))
		end
	end
end

function KFZhuXieFollowView:FlushBossCountDown()
	local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo() or {}
	self.node_list.boss_tip:SetActive(false)
	self.node_list.Btn_item_show:SetActive(false)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ZHUXIE)

	if activity_info.status == ACTIVITY_STATUS.OPEN and next(boss_info) and boss_info.next_boss_refresh_time and boss_info.next_boss_refresh_time > TimeWGCtrl.Instance:GetServerTime() then
		CountDownManager.Instance:AddCountDown(self.countdown_str, BindTool.Bind1(self.UpdateCallBack, self), BindTool.Bind1(self.CompleteCallBack, self), boss_info.next_boss_refresh_time, nil, 1)
		self:UpdateCallBack(0, boss_info.next_boss_refresh_time - TimeWGCtrl.Instance:GetServerTime())
		self.is_show_two = false
		self.is_show_one = false
	else
		self:ClearCD()
		local boss_hp = boss_info.boss_cur_hp or 0
		self.node_list.Btn_item_show:SetActive(boss_hp > 0)
		if boss_hp <= 0 then
			self:CreateBossStone()
		end
		self.node_list.text_desc.text.text = Language.TaskFollow.ZhuxieBoss
	end
end

function KFZhuXieFollowView:FlushActivityCountDown()
	local time
	--如果活动处于准备状态
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ZHUXIE)

	local is_need_guaji = false
	if self.act_state == nil and activity_info.status == ACTIVITY_STATUS.OPEN then
		is_need_guaji = true
	end

	if self.act_state ~= nil and self.act_state ~= activity_info.status and activity_info.status == ACTIVITY_STATUS.OPEN then
		is_need_guaji = true
	end

	self.act_state = activity_info.status

	if is_need_guaji then
		if not (GuajiCache.guaji_type == GuajiType.Auto) then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end

	self.node_list.text_activity_tip.text.text = ""
	if activity_info.status == ACTIVITY_STATUS.STANDY then
		self.node_list.activity_tips:SetActive(true)
		GuajiWGCtrl.Instance:StopGuaji()
		time = activity_info.next_time
		self.node_list.text_activity_tip.text.text = Language.ZhuXie.ActTips1
	else
		local boss_info = ActivityWGData.Instance:GetKFZhuXieTaskInfo() or {}
		local boss_hp = boss_info.boss_cur_hp or 0
		local next_boss_refresh_time = boss_info.next_boss_refresh_time or 0
		if boss_hp <= 0 and (next_boss_refresh_time < TimeWGCtrl.Instance:GetServerTime()) then
			self.node_list.activity_tips:SetActive(true)
			time = activity_info.next_time
			self.node_list.text_activity_tip.text.text = Language.ZhuXie.ActTips2
		end
	end
	self.node_list.activity_tips:SetActive(false)
	if CountDownManager.Instance:HasCountDown(self.zhuxie_act_countdown) then
		CountDownManager.Instance:RemoveCountDown(self.zhuxie_act_countdown)
	end
	if time then
		CountDownManager.Instance:AddCountDown(self.zhuxie_act_countdown, BindTool.Bind1(self.ActUpdateCallBack, self), BindTool.Bind1(self.ActCompleteCallBack, self), time, nil, 1)
		self:ActUpdateCallBack(0, time - TimeWGCtrl.Instance:GetServerTime())
	end
end

function KFZhuXieFollowView:ActCompleteCallBack()
	if CountDownManager.Instance:HasCountDown(self.zhuxie_act_countdown) then
		CountDownManager.Instance:RemoveCountDown(self.zhuxie_act_countdown)
	end
	self.node_list.activity_tips:SetActive(false)


end

--活动倒计时刷新
function KFZhuXieFollowView:ActUpdateCallBack(elapse_time, total_time)
	if not self:IsOpen() then return end
	elapse_time = math.floor(elapse_time)
	total_time = math.floor(total_time)
	self.node_list.activity_count_time.text.text = TimeUtil.FormatSecond(total_time - elapse_time, 2)
	self.node_list.activity_tips:SetActive(true)
end

--BOSS刷新倒计时
function KFZhuXieFollowView:UpdateCallBack(elapse_time, total_time)
	if not self:IsOpen() then return end

	elapse_time = math.floor(elapse_time)
	total_time = math.floor(total_time)

	local last_time = total_time - elapse_time
	local time_str = TimeUtil.FormatSecond(last_time, 2)

	--self.node_list["flush_flag"]:SetActive((last_time) <= 0)
	--self.node_list.effect:SetActive((last_time) <= 0)
	self.node_list.Btn_item_show:SetActive((last_time) <= 0)
	self:SetTargetActive((last_time) > 0, last_time)
	self.node_list.count_time.text.text = time_str
	self.node_list.time_show.text.text = time_str
	self.node_list.boss_tip:SetActive((last_time) > 0)
	-- self.node_list.right_parent:SetActive((last_time) <= 0)
	if (last_time) <= 0 then
		self:GetIsNeedGuaji()
		self:BossFlushAnimation()
	end
	--记录倒计时
	self.cur_endtime = last_time

end

function KFZhuXieFollowView:CompleteCallBack()
	self.node_list.BossButton.toggle.isOn = true
	self.node_list.TaskButton.toggle.isOn = false
	self:SetTargetActive(false)
end

function KFZhuXieFollowView:FlushBossInfo()
	if not self.is_load_succe then return end
	local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo()
	local hurt_rank_list = ActivityWGData.Instance:GetKFZhuXieHurtRankList()
	self.hurt_list_view:SetDataList(hurt_rank_list)
	if boss_info.boss_id and boss_info.boss_id ~= 0 then
    	local monster_info = BossWGData.Instance:GetMonsterInfo(boss_info.boss_id)
    	self.node_list["MonsterName"].text.text = monster_info.name
    	self:SetBossHp(boss_info.boss_cur_hp, boss_info.boss_max_hp)
    end
    local role_name = RoleWGData.Instance:GetRoleVo().role_name
    local server_id = GameVoManager.Instance:GetMainRoleVo().server_id
    role_name = string.format("%s_s%s",role_name,server_id)
    local rank_num, rank_info = ActivityWGData.Instance:GetKFZhuXieHurtRankInfoByName(role_name)
    if rank_info then
	    local percent = ActivityWGData.Instance:GetZhuXieProValue(rank_info.hurt)
		self.node_list.Progress.slider.value = percent
	else
		self.node_list.Progress.slider.value = 0
	end


    if not rank_num or not rank_info then
    	-- self.node_list.rank_num:SetActive(false)
    	-- self.node_list.rank_img:SetActive(false)
    	self.node_list.damage.text.text = "0"
    	--self.node_list.role_name.text.text = Language.GuildAnswer.NoRank1
    else
    	-- self.node_list.rank_num:SetActive(rank_num > 3)
    	-- self.node_list.rank_img:SetActive(rank_num <= 3)
    	-- if rank_num <= 3 then
    	-- 	self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonOthers("rank_num_"..rank_num))
    	-- end
    	self.node_list.rank_num.text.text = rank_num
    	--self.node_list.role_name.text.text = role_name
    	self.node_list.damage.text.text = rank_info.hurt
    end
end

function KFZhuXieFollowView:SetTargetActive(enable, last_time)
	-- self.node_list["time_show"]:SetActive(enable)
	self.node_list["flush_img"]:SetActive(enable)
	-- self.node_list["boss_hp"]:SetActive(not enable)
	XUI.SetButtonEnabled(self.node_list["Boss_Btn"], not enable)
	self:SetBossHeadVal(enable, last_time)
end

function KFZhuXieFollowView:SetBossHeadVal(enable, val)
	if self.boss_head_cache ~= enable or self.boss_head_cache then
		self.boss_head_cache = enable
		if self.boss_head_cache then
			if val == nil then
				return
			end
			local max_time = ActivityWGData.Instance:GetKFZhuXieBossRefreshTime()
			val = val/max_time
			if val < 0 then
				val = 0
			end
			if val > 1 then
				val = 1
			end
			self.node_list["boss_head_img"].image.material:SetFloat("_Progress", 1 - val)
		else
			self.node_list["boss_head_img"].image.material:SetFloat("_Progress", 1)
		end
	end
end

--怪物死亡
function KFZhuXieFollowView:SetTargetActiveTwo(enable)
	-- self.node_list["time_show"]:SetActive(enable)
	self.node_list["flush_img"]:SetActive(enable)
	-- self.node_list["boss_hp"]:SetActive(not enable)
	XUI.SetButtonEnabled(self.node_list["Boss_Btn"], enable)
end

function KFZhuXieFollowView:SetBossHp(hp_value, max_value)
	if hp_value <= 0 then hp_value = 0 end
	if max_value == 0 then max_value = 1 end
	local percent = hp_value / max_value
	 self:CalcHpPercent(percent)
    if percent <= 0 and self.cur_endtime <= 0 then
    	self:SetTargetActive(false)
		self.node_list.killed_flag:SetActive(true)
    	self:CreateBossStone()
    else
    	self.node_list.killed_flag:SetActive(false)
    	self:DeleteBossStone()
    end

    if percent > 0 and self.cur_endtime <= 0 then
    	if percent * 100 <= 10 then
    		if not self.is_show_tip_3 then
    			self.is_show_tip_3= true
    			-- 危险！首领血量低于10%，将进入狂暴，攻击力翻倍
    			self.node_list.hp_tip.image:LoadSprite(ResPath.GetF2ZhuXieIcon("zx_piaozi1"))
    			self.node_list.hp_tip:SetActive(true)
    			GlobalTimerQuest:AddDelayTimer(function ()
    				self.node_list.hp_tip:SetActive(false)
    			end,10)
    		end
    	elseif percent * 100 <= 40 then
    		if not self.is_show_tip_2 then
    			self.is_show_tip_2 = true
				-- 危险！首领血量低于40%，将释放技能“大杀四方”
    			self.node_list.hp_tip.image:LoadSprite(ResPath.GetF2ZhuXieIcon("zx_piaozi2"))
    			self.node_list.hp_tip:SetActive(true)
    			GlobalTimerQuest:AddDelayTimer(function ()
    				self.node_list.hp_tip:SetActive(false)
    			end,10)
    		end
    	elseif percent * 100 <= 70 then
    		if not self.is_show_tip_1 then
    			self.is_show_tip_1 = true
				-- 危险！首领血量低于70%，将释放技能“大杀四方”
    			self.node_list.hp_tip.image:LoadSprite(ResPath.GetF2ZhuXieIcon("zx_piaozi"))
    			self.node_list.hp_tip:SetActive(true)
    			GlobalTimerQuest:AddDelayTimer(function ()
    				self.node_list.hp_tip:SetActive(false)
    			end,10)
    		end
    	end
    	if self.is_first_enter then
    		self:SetTargetActive(false)
    		self.is_first_enter = false
    	end
    end

    self.node_list["MonsterHp"].slider.value = percent
end

function KFZhuXieFollowView:CalcHpPercent(percent)
	 if percent < 0.6 and percent > 0.3 and not self.is_show_one then
	 	self.is_show_one = true
	 	if self.monster_cfg then
	 		TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.XueLiangShengYu60, self.monster_cfg.name))
	 	end
	 elseif percent < 0.3 and percent > 0 and not self.is_show_two then
	 	self.is_show_two = true
	 	if self.monster_cfg then
	 		TipWGCtrl.Instance:ShowZCRuneMsg(string.format(Language.ZCRuneText.XueLiangShengYu30, self.monster_cfg.name))
	 	end
	 end
end

function KFZhuXieFollowView:ClearCD()
	if CountDownManager.Instance:HasCountDown(self.countdown_str) then
		CountDownManager.Instance:RemoveCountDown(self.countdown_str)
	end
end

function KFZhuXieFollowView:OnClickGoAttackBoss()--去攻击boos
	Scene.Instance:ClearAllOperate()
	self.node_list["flush_flag"]:SetActive(false)
	self.node_list.effect:SetActive(false)

	local other_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").other[1]

	local scene_id = Scene.Instance:GetSceneId()
	local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo() or {}
	local boss_obj = nil
	local pos_list = Split(other_cfg.boss_pos,",")
	if boss_info.boss_cur_hp <= 0 and not IsEmptyTable(pos_list) then
		GuajiWGCtrl.Instance:StopGuaji(false, true)
		GuajiWGCtrl.Instance:ResetMoveCache()
		MoveCache.SetEndType(MoveEndType.Auto)
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(BindTool.Bind1(self.GetIsNeedGuaji, self))
		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_list[1], pos_list[2],3)
		return
	end

	if boss_info.boss_id ~= nil and not IsEmptyTable(pos_list) then
		--当前选中的怪已经是目标的时候不在选中
		if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and
			GuajiCache.target_obj:IsBoss() and GuajiCache.target_obj.vo ~= nil and
			GuajiCache.target_obj.vo.monster_id ==  boss_info.boss_id then
			return
		end
		GuajiWGCtrl.Instance:ResetMoveCache()
		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		MoveCache.param1 = boss_info.boss_id
		GuajiCache.monster_id = boss_info.boss_id
		local range = BossWGData.Instance:GetMonsterRangeByid(boss_info.boss_id)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_list[1], pos_list[2], range)
	else
		if not IsEmptyTable(pos_list) then
			--当前选中的怪已经是目标的时候不在选中
			if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and
				GuajiCache.target_obj:IsBoss() and GuajiCache.target_obj.vo ~= nil and
				GuajiCache.target_obj.vo.monster_id ==  boss_info.boss_id then
				return
			end
			GuajiWGCtrl.Instance:ResetMoveCache()
			MoveCache.SetEndType(MoveEndType.Auto)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_list[1], pos_list[2],3)
		end
	end
end

function KFZhuXieFollowView:GetIsNeedGuaji()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	if x == nil then x = 0 end
	if y == nil then y = 0 end
	local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo() or {}
	local boss_cur_hp = boss_info.boss_cur_hp or 0
	if boss_info ~= nil and boss_cur_hp <= 0 then
		self:AutoCompleteTasks()
	end
end

--创建墓碑
function KFZhuXieFollowView:CreateBossStone()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ZHUXIE)
	if nil ~= activity_info then
		local flag = activity_info.status == ACTIVITY_STATUS.STANDY
		if flag then return end
	end
	local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo() or {}

	if nil == boss_info.boss_id or self.obj ~= nil then return end
	local other_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").other[1]
	local pos_list = Split(other_cfg.boss_pos,",")
	local stone_vo = GameVoManager.Instance:CreateVo(BossStoneVo)   -- 创建一个VO对象  参数为vo类
	stone_vo.pos_x = pos_list[1]
	stone_vo.pos_y = pos_list[2]
	stone_vo.boss_id = boss_info.boss_id
	stone_vo.obj_id = Scene.Instance:GetSceneClientId()
	self.obj = Scene.Instance:CreateObj(stone_vo, SceneObjType.BossStoneObj)   -- 创建一个场景对象 参数为 vo对象 和场景对象的类型
  	self.obj:SetBossStoneInfo(TimeWGCtrl.Instance:GetServerTime() + 10000)
end

function KFZhuXieFollowView:DeleteBossStone()
	if self.obj then
		self.obj:DeleteBossStone()
		self.obj = nil
	end
end

function KFZhuXieFollowView:OnCellData(data)
	if not data then return end
	local cfg = data.cfg
	if not cfg then return end
	local task_id

	local task_info = ActivityWGData.Instance:GetKFZhuXieTaskInfoByTaskId(cfg.task_id)

	if not task_info or (task_info and task_info.is_fetched == 1) then
		task_id = ActivityWGData.Instance:GetKFZhuXieANotCompleteTaskID()
	else
		task_id = cfg.task_id
	end

	if not task_id then
		GuajiWGCtrl.Instance:StopGuaji()
		return
	end

	GuajiWGCtrl.Instance:StopGuaji()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	ActivityWGData.Instance:SetCurTaskid(cfg.task_id)
	ActivityWGCtrl.Instance:KFZhuXieFollowViewFlush(0, "select_change")
	if cfg.task_type ~= KFZhuXieFollowView.TaskType.Role then
		self:AutoFindTarget(data)
	else
		self:FindRoleTarget()
	end
end

function KFZhuXieFollowView:TaskCellCallBack(cell)
	local data = cell:GetData()
	if not data then return end
	self:OnCellData(data)
end

function KFZhuXieFollowView:FindRoleTarget()
	local kill_role_task_info = ActivityWGData.Instance:GetKFZhuXieTaskInfoByTaskId(5)
	if not kill_role_task_info or not (kill_role_task_info.is_fetched == 0) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		GuajiWGCtrl.Instance:StopGuaji()
		local main_role = Scene.Instance:GetMainRole()

		if main_role then
			main_role:ChangeToCommonState()
		end
		return
	end

	GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

	if kill_role_task_info and kill_role_task_info.is_fetched == 0 then
		local x, y = Scene.Instance:GetMainRole():GetLogicPos()
		local enemy_obj = Scene.Instance:SelectObjHelper(SceneObjType.Role, x, y, 1000000, SelectType.Enemy)
		if nil ~= enemy_obj and not enemy_obj:IsDeleted() and Scene.Instance:IsEnemy(enemy_obj) then
			GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, enemy_obj, SceneTargetSelectType.SELECT)
		end
	end
end

-- 自动寻路到目标点,然后干活
function KFZhuXieFollowView:AutoFindTarget(data)
	if data == nil or next(data) == nil then return end
	local cfg = data.cfg
	if cfg == nil or next(cfg) == nil then return end


	local cur_select_target = SceneObj.select_obj	--当前若已选将不再随机选出--当前若已选将不再随机选出

	if nil ~= cur_select_target and nil ~= cur_select_target.vo and
		not cur_select_target:IsDeleted() then
		if cfg.event == SceneObjType.Monster and cur_select_target.vo.monster_id ~= cfg.param_id then
			cur_select_target = nil
		elseif cfg.event == SceneObjType.GatherObj and cur_select_target.vo.gather_id ~= cfg.param_id then
			cur_select_target = nil
		end
	end

	if nil == cur_select_target then
		if cfg.event == SceneObjType.Monster then
			cur_select_target = Scene.Instance:SelectMinDisMonster(cfg.param_id)	--选择最近的
		elseif cfg.event == SceneObjType.GatherObj then
			cur_select_target = Scene.Instance:SelectMinDisGather(cfg.param_id)	--选择最近的
		end
	end

	local target_obj = {}
	target_obj.x = cfg.x
	target_obj.y = cfg.y
	target_obj.scene = SceneType.KFZhuXieZhanChang
	if nil ~= cur_select_target and nil ~= cur_select_target.vo and not cur_select_target:IsDeleted() and cur_select_target:GetObjId() ~= nil then
		target_obj.obj = cur_select_target
		target_obj.obj_id = cur_select_target:GetObjId()
		target_obj.x, target_obj.y= cur_select_target:GetLogicPos()
		target_obj.id = cur_select_target.id
	end

	local gjc = GuajiWGCtrl.Instance
	gjc:StopGuaji()

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:ChangeToCommonState()
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		scene_logic:SetIsAutoTask(false)
	end

	local scene_id = Scene.Instance:GetSceneId()
	if cfg.task_type ~= KFZhuXieFollowView.TaskType.Gather then
		MoveCache.SetEndType(MoveEndType.Auto)
		gjc:MoveToPos(scene_id, target_obj.x, target_obj.y, 3)
	else--采集
		if scene_logic ~= nil then
			scene_logic:SetIsAutoTask(true)
		end

		MoveCache.SetEndType(MoveEndType.GatherById)
		MoveCache.param1 = cfg.param_id
		gjc:MoveToPos(scene_id, target_obj.x, target_obj.y, 3)
	end
end

function KFZhuXieFollowView:OnClickOpenRewardView()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ZhuXie or scene_type == SceneType.KFZhuXieZhanChang then
    	ActivityWGCtrl.Instance:OpenZhuXieRankView(scene_type == SceneType.KFZhuXieZhanChang)
    end
end
-------------KFZhuXieFollowTask-------------------

KFZhuXieFollowTask = KFZhuXieFollowTask or BaseClass(BaseRender)

function KFZhuXieFollowTask:__init()
	self.task_status = GameEnum.TASK_STATUS_NONE
	self.task_link_type = "common"
	-- self.cell_list = {}
	self:CreateChild()
	self.value = 0
end

function KFZhuXieFollowTask:__delete()
	if self.auto_task_timer then
		GlobalTimerQuest:CancelQuest(self.auto_task_timer)
		self.auto_task_timer = nil
	end

	if self.cell_list then
		self.cell_list:DeleteMe()
		self.cell_list = nil
	end

	self.start_guaji_event = nil
	self:ClearCountDown()
	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
	end
end

function KFZhuXieFollowTask:CreateChild()

	self.cell_list = ItemCell.New()
	self.cell_list:SetInstanceParent(self.node_list["item_num_1"])
	-- self.node_list.btn_task_bg.button:AddClickListener(BindTool.Bind1(self.OnClick,self))
end

function KFZhuXieFollowTask:AutoChange()
	if nil == self.data then
		return
	end

	local cur_task_id = ActivityWGData.Instance:GetCurTaskid()
	local flag = cur_task_id == self.data.cfg.task_id
	if self.data.is_fetched == 1 or self.data.cfg.task_id == TaskType_Role then
		flag = false
	end

	self.node_list.BtnAuto:SetActive(flag)
	self.node_list.Effect:SetActive(flag)
end

function KFZhuXieFollowTask:OnFlush()
	if not self.data then return end
	self.node_list.img_dacheng:SetActive(false)
	local gray_color = self:HasFetchReward() and COLOR3B.GRAY
	local reward_data = ActivityWGData.Instance:GetKFZhuxieTaskReward(self.data.cfg.task_id)
	self.cell_list:SetData(reward_data.item[0])
	self.node_list.double_flag:SetActive(self.data.is_double == 1)
	-- local task_info = ActivityWGData.Instance:GetOneZhuXieTaskInfo(self.data.task_id) or {}
	local param_value = self.data.param_value or 0
	local max_value = self.data.cfg.param_max_value or 0
	local is_fetched = self.data.is_fetched or 0
	local cur_task_id = ActivityWGData.Instance:GetCurTaskid()
	self:AutoChange()

	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
	end
	self.delay_time = GlobalTimerQuest:AddDelayTimer(function ()
		if self.data.cfg.task_type == KFZhuXieFollowView.TaskType.Gather and cur_task_id == self.data.cfg.task_id and param_value < max_value then
			ActivityWGCtrl.Instance:KFZhuXieAutoFindTarget(self.data)
		end
	end,0.1)

	self.value = param_value
	local status = ""
	if is_fetched == 1 then
		self.node_list.rich_desc.text.text = string.format(Language.ZhuXie.TaskQuantity1,self.data.cfg.task_name,param_value,max_value)
		self.node_list.img_dacheng:SetActive(true)

	elseif max_value <= param_value then
		self.node_list.rich_desc.text.text = string.format(Language.ZhuXie.TaskQuantity1,self.data.cfg.task_name,param_value,max_value)
		self.node_list.img_dacheng:SetActive(true)
	else
		status = Language.Task.task_status[2]--进行中
		self.node_list.rich_desc.text.text = string.format(Language.ZhuXie.TaskQuantity1,self.data.cfg.task_name,param_value,max_value)
	end
	local time = self.data.double_task_end_time - TimeWGCtrl.Instance:GetServerTime()
	self.node_list.double_flag:SetActive(self.data.is_double == 1 and time > 0)
	if self.data.is_double == 1 then
		if time > 0 then
			self:ClearCountDown()
			self:ChangeTime(0,time)
			self.count_down = CountDown.Instance:AddCountDown(time, 1, BindTool.Bind(self.ChangeTime, self))
		else
			self.node_list.rich_name.text.text = string.format(Language.ZhuXie.TaskState2,self.data.cfg.item_name,"")
		end
	else
		self:ClearCountDown()
		self.node_list.rich_time:SetActive(false)
		self.node_list.rich_name.text.text = string.format(Language.ZhuXie.TaskState1,self.data.cfg.item_name,status)
	end
	self:AutoCompleteTask()
end

function KFZhuXieFollowTask:ChangeTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	if self.node_list.rich_name then
		if time > 0 then
			self.node_list.rich_time:SetActive(true)
			self.node_list.rich_name.text.text = string.format(Language.ZhuXie.TaskState2,self.data.cfg.item_name,"")
       		self.node_list.rich_time.text.text = string.format(Language.ZhuXie.TaskState3,TimeUtil.FormatSecond(time,2))
       	else
       		self.node_list.rich_time:SetActive(false)
       		self.node_list.rich_name.text.text = string.format(Language.ZhuXie.TaskState2,self.data.cfg.item_name,"")
       	end
    end
end

function KFZhuXieFollowTask:ClearCountDown()
	self.node_list.double_flag:SetActive(false)
	if CountDown.Instance:HasCountDown(self.count_down) then
        CountDown.Instance:RemoveCountDown(self.count_down)
        self.count_down = nil
   	end
end

function KFZhuXieFollowTask:IsTaskCanComplete()
	if not self.data then return false end
	local param_value = self.data.param_value or 0
	local max_value = self.data.cfg.param_max_value or 0
	local is_fetched = self.data.is_fetched or 0
	if is_fetched == 0 and param_value >= max_value then
		return true
	end
	return false
end

function KFZhuXieFollowTask:HasFetchReward()
	if not self.data then return false end
	local is_fetched = self.data.is_fetched or 0
	return is_fetched ~= 0
end

function KFZhuXieFollowTask:AutoCompleteTask()
	if not self.data then return end
	if self:IsTaskCanComplete() then

		ActivityWGCtrl.Instance:SendZhuXieFetchTaskReward(self.data.cfg.task_id)
		ActivityWGCtrl:SendCSCrossZhuxieOperate(CROSS_ZHUXIE_OP_TYPE.CROSS_ZHUXIE_OP_TYPE_TASK_REWARD,self.data.cfg.task_id)
	end
end

---------------------------------------------------KFZhuXieHurtRankView-----------------------------------------------------
KFZhuXieHurtRankView = KFZhuXieHurtRankView or BaseClass(BaseRender)

function KFZhuXieHurtRankView:__init()
	self.node_list["attack_btn"].button:AddClickListener(BindTool.Bind1(self.OnClickAttackBtn, self))
end

function KFZhuXieHurtRankView:__delete()

end

function KFZhuXieHurtRankView:OnFlush()
	if not self.data then return end
	self.node_list.rank_num:SetActive(self.index > 3)
	self.node_list.rank_img:SetActive(self.index <= 3)
	if self.index <= 3 then
		self.node_list.rank_img.image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_"..self.index))
	end
	self.node_list.rank_num.text.text = self.index
	self.node_list.role_name.text.text = self.data.name
	self.node_list.damage.text.text = self.data.hurt
	local percent = ActivityWGData.Instance:GetZhuXieProValue(self.data.hurt)
	self.node_list.Progress.slider.value = percent

	local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo()

	if not boss_info or IsEmptyTable(boss_info) then
		return
	end

	local role_id = RoleWGData.Instance:GetOriginUid()
	self.node_list["attack_btn"]:SetActive(role_id ~= self.data.role_id and boss_info.boss_cur_hp > 0)
end

function KFZhuXieHurtRankView:OnClickAttackBtn()
	local main_role = Scene.Instance:GetMainRole()

	if not main_role or main_role:IsDeleted() or main_role:IsDead() and not main_role:GetVo() then
		return
	end

	local role_list = Scene.Instance:GetRoleList()

	if IsEmptyTable(role_list) then
		--对方不在攻击范围
		TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips1)
		return
	end

	for k,v in pairs(role_list) do
		if not v:IsDeleted() and not v:IsDead() and v.vo ~= nil and v.vo.origin_uid and v.vo.origin_uid > 0 then
			if self.data.role_id == v.vo.origin_uid then
				--对方是队友
				if Scene.Instance:IsFriend(v) then
					if v:IsInSafeArea() then
						--对方处于安全区
						TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips4)
					elseif main_role:GetVo().attack_mode == ATTACK_MODE.PEACE then
						--处于和平模式
						TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips3)
					elseif main_role:GetVo().attack_mode == ATTACK_MODE.GUILD then
						--处于盟友模式
						TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips2)

					end
					return
				end

				--对方是敌人
				if Scene.Instance:IsEnemy(v) then
					GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
					GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
					GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, v, SceneTargetSelectType.SELECT)
				end

				return
			end
		end
	end
	--对方不在攻击范围
	TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuXie.AttackTips1)
end
