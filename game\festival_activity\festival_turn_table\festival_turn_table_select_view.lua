local SelectTextOutLineColor = {
    [1] = "#ab5939",
    [2] = "#9d6d1a",
    [3] = "#8c45a1",
    [4] = "#4552a1",
}

FestivalTurnTableSelectView = FestivalTurnTableSelectView or BaseClass(SafeBaseView)
function FestivalTurnTableSelectView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
                        {sizeDelta = Vector2(940,586)})
	self:AddViewResource(0, "uis/view/festival_activity_ui/trun_table_prefab", "layout_fastival_select_view")
end

function FestivalTurnTableSelectView:__delete()

end

function FestivalTurnTableSelectView:ReleaseCallBack()

	if self.change_select_list then
		self.change_select_list:DeleteMe()
		self.change_select_list = nil
	end

end

function FestivalTurnTableSelectView:OpenCallBack()
end

-- function FestivalTurnTableSelectView:LoadTurnTableSelectUI()
-- 	self.node_list["big_bg"].raw_image:LoadSprite(ResPath.GetFestivalRawImages("hs_diban120"))
-- 	self.node_list["left_bg"].raw_image:LoadSprite(ResPath.GetFestivalRawImages("hs_biaotidi"))
--     self.node_list["name_image"].image:LoadSprite(ResPath.GetFestivalImages("hs_xuanzejiangl"))
--     self.node_list["close_btn"].image:LoadSprite(ResPath.GetFestivalImages("hs_close"))

-- 	self.node_list["big_bg"].raw_image:SetNativeSize()
-- 	self.node_list["left_bg"].raw_image:SetNativeSize()
--     self.node_list["name_image"].image:SetNativeSize()
--     self.node_list["close_btn"].image:SetNativeSize()
-- end

function FestivalTurnTableSelectView:LoadCallBack()
	--self:LoadTurnTableSelectUI()
	self.node_list.title_view_name.text.text = Language.ZhouYiYunCheng.SelectViewTitle
	self.change_select_list = AsyncListView.New(FestivalTableSelectBigCell, self.node_list.xianling_list)
    self.node_list["confirm"].button:AddClickListener(BindTool.Bind(self.OnClickConfirm,self))
end

function FestivalTurnTableSelectView:OnClickConfirm()
	local all_select_flag = FestivalTurnTableWGData.Instance:GetTempSelectList()
	for i = 1, 4 do
		local have_num,need_num = FestivalTurnTableWGData.Instance:GetHaoManyWeSelect(i)
		if have_num ~= need_num then
			TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.PleaseSelect)
			return
		end
	end

	local default_select = {}
	for i = 1 , 4 do
		default_select[i] = {}
		for q = 1, 32 do
			default_select[i][33-q] = all_select_flag[i][q] and 1 or 0
		end
	end

	FestivalTrunTableWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_CHOOSE_REWARD,bit:b2d(default_select[1]),bit:b2d(default_select[2]),bit:b2d(default_select[3]),bit:b2d(default_select[4]))
	self:Close()
end

function FestivalTurnTableSelectView:OnFlush()
	local all_data = FestivalTurnTableWGData.Instance:GetAllItemList()
	self.change_select_list:SetDataList(all_data)
end


FestivalTableSelectBigCell = FestivalTableSelectBigCell or BaseClass(BaseRender)

function FestivalTableSelectBigCell:__init()
	self.change_select_list = AsyncListView.New(FestivalTableSelectSmallCell, self.node_list.item_group)
	--self.node_list["bg"].image:LoadSprite(ResPath.GetFestivalImages("hs_liebiao"))
	
end

function FestivalTableSelectBigCell:__delete()
	if self.change_select_list then
		self.change_select_list:DeleteMe()
		self.change_select_list = nil
	end
end

function FestivalTableSelectBigCell:OnFlush()
	if not IsEmptyTable(self.data) then
		self.level = self.data[1].index1
		for k,v in pairs(self.data) do
			v.click_call_back = function ()
				self:FlushSelectNum()
			end
		end
		self.change_select_list:SetDataList(self.data)
		local bundle, asset = ResPath.GetNoPackPNG("a2_jlxz_sel_"..self.level)
 		self.node_list["img"].image:LoadSpriteAsync(bundle, asset,function ()
			self.node_list["img"].image:SetNativeSize()
		end)

		local out_line = self.node_list.level_num:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
		if SelectTextOutLineColor[self.level] then
			out_line.effectColor = Str2C3b(SelectTextOutLineColor[self.level])
		end
		self.node_list.level_num.text.text = Language.ZhouYiYunCheng.ReWaredLevel[self.level]

		self:FlushSelectNum()
	else

	end
end


function FestivalTableSelectBigCell:FlushSelectNum()
	local have_num,limite_num = FestivalTurnTableWGData.Instance:GetHaoManyWeSelect(self.level)
	local color = have_num == limite_num and COLOR3B.GREEN or COLOR3B.RED
	local str = "("..ToColorStr(have_num,color).."/"..limite_num..")"
	self.node_list["select_num"].text.text = str
end

FestivalTableSelectSmallCell = FestivalTableSelectSmallCell or BaseClass(BaseRender)

function FestivalTableSelectSmallCell:__init()
	self.item_cell = ItemCell.New(self.node_list["item_pos"])
	--self.item_cell:UseNewSelectEffect(true)
	self.node_list["click_area"].button:AddClickListener(BindTool.Bind(self.OnClickItem, self))
	self.node_list["select_area"].button:AddClickListener(BindTool.Bind(self.OnClickSelect, self))
end

function FestivalTableSelectSmallCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function FestivalTableSelectSmallCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local cell_data = {}
		cell_data.item_id = tonumber(self.data.item_id)
		cell_data.num = tonumber(self.data.num)
		cell_data.is_bind = tonumber(self.data.is_bind)
		self.item_cell:SetData(cell_data)
	else

	end
	self:FlushSelect()
end

function FestivalTableSelectSmallCell:OnClickSelect()
	local index1 = self.data.index1
	local index2 = self.data.index2
	local value = FestivalTurnTableWGData.Instance:TryToSelectOrUnSelectThis(index1, index2)
	if value then
		self:FlushSelect()
		if self.data.click_call_back then
			self.data.click_call_back()
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.IsSelectEnough)
	end
end

function FestivalTableSelectSmallCell:FlushSelect()
	local index1 = self.data.index1
	local index2 = self.data.index2
	local is_select = FestivalTurnTableWGData.Instance:GetItemIsBeenSelect(index1, index2)
	self.node_list["select_flag"]:SetActive(is_select)
	--self.item_cell:SetSelectEffect(is_select)
end

function FestivalTableSelectSmallCell:OnClickItem()
	local cell_data = {}
	cell_data.item_id = tonumber(self.data.item_id)
	cell_data.num = tonumber(self.data.num)
	cell_data.is_bind = tonumber(self.data.is_bind)
	TipWGCtrl.Instance:OpenItem(cell_data)
end