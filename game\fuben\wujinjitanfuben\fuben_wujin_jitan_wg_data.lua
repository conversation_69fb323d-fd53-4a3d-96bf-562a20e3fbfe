WuJinJiTanWGData = WuJinJiTanWGData or BaseClass()

WuJinJiTanWGData.WUJINJITAN_REQ_TYPE = {
	WUJINJITAN_REQ_TYPE_BUY_ONCE = 0,		-- 购买副本进入次数
	WUJINJITAN_REQ_TYPE_ALL_INFO = 1,		-- 副本所有信息
	WUJINJITAN_REQ_TYPE_ROLE_INFO = 2,		-- 角色信息

}

function WuJinJiTanWGData:__init()
	WuJinJiTanWGData.Instance = self
	self.wujin_jitan_all_data = {}
	self.wujin_jitan_role_data = {}

	self.enter_time = 0        --多人经验本进入次数
	self.buy_time = 0
	self.user_ticket_times = 0
	self.wujinjitan_cfg = ConfigManager.Instance:GetAutoConfig("wujinjitan_cfg_auto")
	self.linghunguangchang_cfg = ConfigManager.Instance:GetAutoConfig("linghunsquare_cfg_auto")

	self.linghunguangchang_enter_time = 0 	--灵魂广场次数
	self.linghunguangchang_buy_time = 0     --灵魂广场购买次数
	self.use_ticket_add_times = 0 			--消耗卷增加的次数

	self:InitMedicineItemConfig()

	self.monster_cfg = ListToMap(self.wujinjitan_cfg.monster, "wave", "seq")
	self.linghun_monster_cfg = ListToMap(self.linghunguangchang_cfg.monster, "wave", "seq")
	self.buy_times_cfg = ListToMap(self.wujinjitan_cfg.buy_times, "buy_times")
	local count = 0
	for i, v in pairs(self.wujinjitan_cfg.buy_times) do
		count = count + 1
	end
	self.max_buy_times = count
end

function WuJinJiTanWGData:__delete()

end

function WuJinJiTanWGData:SetWuJinJiTanAllInfo(protocol)
	self.wujin_jitan_all_data = protocol.data
end

function WuJinJiTanWGData:GetWuJinJiTanAllInfo()
	return self.wujin_jitan_all_data
end

function WuJinJiTanWGData:SetWuJinJiTanRoleInfo(protocol)
	self.wujin_jitan_role_data = protocol.data
end

function WuJinJiTanWGData:GetGuWuCount()
	return self.wujin_jitan_role_data.coin_guwu_count,self.wujin_jitan_role_data.gold_guwu_count
end

function WuJinJiTanWGData:GetWuJinJiTanRoleInfo()
	return self.wujin_jitan_role_data
end

function WuJinJiTanWGData:GetWuJinJiCfg()
	return self.wujinjitan_cfg
end

function WuJinJiTanWGData:SetTeamExpFbEnterTimes(enter_time)
	self.enter_time = enter_time
end

function WuJinJiTanWGData:GetTeamExpFbEnterTimes()
	return self.enter_time
end

function WuJinJiTanWGData:SetZhuZhanTimes(zhuzhan_times)
	self.zhuzhan_times = zhuzhan_times
end
function WuJinJiTanWGData:GetZhuZhanTimes()
	return self.zhuzhan_times
end

-- 已购买次数
function WuJinJiTanWGData:SetTeamFbBuyTimes(buy_time)
	self.buy_time = buy_time
end

function WuJinJiTanWGData:GetTeamFbBuyTimes()
	return self.buy_time
end

-- 炼狱卷使用次数
function WuJinJiTanWGData:SetTeamExpUseTicketTimes(user_time)
	self.user_ticket_times = user_time
end

function WuJinJiTanWGData:GetTeamExpUseTicketTimes()
	return self.user_ticket_times
end

-- 可购买次数
function WuJinJiTanWGData:GetTeamFbAllBuyTimes(vip_level, next_level)
	local max_level = VipWGData.Instance:GetMaxVIPLevel()
	if vip_level > max_level then
		vip_level = max_level
	end

	local vip_level_cfg = ConfigManager.Instance:GetAutoConfig("vip_auto").level[12]
	if next_level then
		if vip_level_cfg["param_" .. vip_level] <= 0 then
			for i = 1, max_level do
				if vip_level_cfg["param_" .. i] > 0 then
					return i, vip_level_cfg["param_" .. i]
				end
			end
		else
			return vip_level, vip_level_cfg["param_" .. vip_level]
		end
	else
		return vip_level_cfg["param_" .. vip_level]
	end
end

function WuJinJiTanWGData:GetTeamExpFbMonsterLevel()
	local refresh_level_cfg = ConfigManager.Instance:GetAutoConfig("wujinjitan_cfg_auto").refresh_level
	local member_list = SocietyWGData.Instance:GetTeamMemberList()
	local average_level = 0
	if 0 == #member_list then
		average_level = RoleWGData.Instance.role_vo.level
	else
		local total_level = 0
		for i,v in ipairs(member_list) do
			total_level = total_level + v.level
		end
		average_level = math.floor(total_level / #member_list)
	end

	return refresh_level_cfg[average_level].monster_level
end

function WuJinJiTanWGData:GetTeamExpFbExtraHurt()
	local monster_level = self:GetTeamExpFbMonsterLevel()
	local refresh_level_cfg = ConfigManager.Instance:GetAutoConfig("wujinjitan_cfg_auto").refresh_level
	for k,v in pairs(refresh_level_cfg) do
		if v.monster_level == monster_level then
			return v.extra_been_hurt
		end
	end
	return 0
end

function WuJinJiTanWGData:GetGuWuAddPer(count)
	if count > 5 then return end
	return self.wujinjitan_cfg.other[1].guwu_add_per / 100 * count
end

function WuJinJiTanWGData:GetAllTime()
	return self.wujinjitan_cfg.other[1].fb_continue_time_s
end

--function WuJinJiTanWGData:SetLingHunGuangChangFbEnterTimes(times)
--	self.linghunguangchang_enter_time = times
--end
--
--function WuJinJiTanWGData:GetLingHunGuangChangFbEnterTimes()
--	return self.linghunguangchang_enter_time
--end
--
--
--function WuJinJiTanWGData:SetLingHunGuangChangFbBuyTimes(times)
--	self.linghunguangchang_buy_time = times
--end
--
--function WuJinJiTanWGData:GetLingHunGuangChangBuyTimes()
--	return self.linghunguangchang_buy_time
--end

--function WuJinJiTanWGData:SetLingHunGuangChangUseTicketAddTimes(times)
--	self.use_ticket_add_times = times
--end
--
--function WuJinJiTanWGData:GetLingHunGuangChangUseTicketAddTimes()
--	return self.use_ticket_add_times
--end


function WuJinJiTanWGData:GetLingHunGuangChangLevelLimit()
	return self.linghunguangchang_cfg.other[1].open_level
end

function WuJinJiTanWGData:GetLingHunGuangChangCfg()
	return self.linghunguangchang_cfg
end

function WuJinJiTanWGData:GetTeamExpRedlimitLv()
	return self.linghunguangchang_cfg.other[1].limit_red_point
end

function WuJinJiTanWGData:GetTeamExpBossId()
	return self.linghunguangchang_cfg.other[1].boss_id_1
end

function WuJinJiTanWGData:IsLingHunGuangChang()
	local limit = self:GetLingHunGuangChangLevelLimit()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	return role_level >= limit
end

function WuJinJiTanWGData.GetOtherCfg()
	local cfg = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and WuJinJiTanWGData.Instance:GetLingHunGuangChangCfg().other[1] or WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
	return cfg
end

function WuJinJiTanWGData.GetFuBenType()
	return WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FUBEN_TYPE.LINGHUNGUANGCHANG or FUBEN_TYPE.FBCT_WUJINJITAN
end

function WuJinJiTanWGData.GetFBEnterTimes()
	return WuJinJiTanWGData.Instance:GetTeamExpFbEnterTimes()
end

function WuJinJiTanWGData.GetFBBuyTimes()
	return WuJinJiTanWGData.Instance:GetTeamFbBuyTimes()
end

function WuJinJiTanWGData.GetFBAddTimes()
	return WuJinJiTanWGData.Instance:GetTeamExpUseTicketTimes()
end

function WuJinJiTanWGData:InitMedicineItemConfig()
	if not self.medicine_item_id_list then
		self.medicine_item_id_list = {}
	end
	local str = self:GetWuJinJiCfg().other[1].exp_medicine
	local list = Split(str, "|")
	for i, v in ipairs(list) do
		table.insert(self.medicine_item_id_list, tonumber(v))
	end

	if not self.medicine_item_seq_list then
		self.medicine_item_seq_list = {}
	end
	local str = self:GetWuJinJiCfg().other[1].exp_medicicn_seq
	local list = Split(str, "|")
	for i, v in ipairs(list) do
		table.insert(self.medicine_item_seq_list, tonumber(v))
	end
end

function WuJinJiTanWGData:GetMedicineItemList()
	return self.medicine_item_id_list
end
function WuJinJiTanWGData:GetMedicineItemSeqList()
	return self.medicine_item_seq_list
end

--获取助战总奖励次数
function WuJinJiTanWGData:GetZhuZhanTotalRewardTimes()
	return self:GetWuJinJiCfg().other[1].help_reward_times
end
--获取助战声望
function WuJinJiTanWGData:GetZhuZhanShengWang()
	return self:GetWuJinJiCfg().other[1].help_reward_shengwang
end
--是否助战进入
function WuJinJiTanWGData:GetIsZhuZhan()
	return self.wujin_jitan_role_data.is_help == 1
end

function WuJinJiTanWGData:GetMonsterCfg(wave, seq)
	local is_linghun_guangchang = self:GetRecordIsLingHunGuangChang()
	if is_linghun_guangchang then
		if wave and seq and self.linghun_monster_cfg[wave] then
			if self.linghun_monster_cfg[wave][seq] then
				return self.linghun_monster_cfg[wave][seq]
			end
		end
	else
		if wave and seq and self.monster_cfg[wave] then
			if self.monster_cfg[wave][seq] then
				return self.monster_cfg[wave][seq]
			end
		end
	end
	return nil
end

function WuJinJiTanWGData:GetBossJumpPoint()
	local other_cfg = WuJinJiTanWGData.Instance:GetLingHunGuangChangCfg().other[1]
	return other_cfg.jumppos_x, other_cfg.jumppos_y
end

function WuJinJiTanWGData:SetRecordIsLingHunGuangChang(is_linghun_guangchang)
	self.is_linghun_guangchang_record = is_linghun_guangchang
end
function WuJinJiTanWGData:GetRecordIsLingHunGuangChang()
	return self.is_linghun_guangchang_record or false
end

function WuJinJiTanWGData:GetBuyTimesComsumeGold(will_buy_times)
	if self.buy_times_cfg[will_buy_times] then
		return self.buy_times_cfg[will_buy_times].price
	end
	if will_buy_times > self.max_buy_times then
		return self.buy_times_cfg[self.max_buy_times].price
	end
	return self.buy_times_cfg[1].price
end

function WuJinJiTanWGData:GetIsShowArrowVip()
	-- local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	-- local state = VipWGData.Instance:GetBaoZhuState()
	-- return role_vip_level < 4 or state ~= RechargeView.BaoZhuState.Type_3
	-- 宝珠屏蔽了

	local max = VipWGData.Instance:GetVipExpMaxAdd()
	local cur = VipWGData.Instance:GetCurExpAdd()
	if max > cur then
		return true
	end

	return false
end

function WuJinJiTanWGData:GetWuJinJiTanOtherCfg()
	return self.linghunguangchang_cfg.other[1] or {}
end