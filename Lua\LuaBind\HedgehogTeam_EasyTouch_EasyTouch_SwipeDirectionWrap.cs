﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class HedgehogTeam_EasyTouch_EasyTouch_SwipeDirectionWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection));
		<PERSON><PERSON>("None", get_None, null);
		<PERSON><PERSON>("Left", get_Left, null);
		<PERSON><PERSON>("Right", get_Right, null);
		<PERSON><PERSON>("Up", get_Up, null);
		<PERSON><PERSON>("Down", get_Down, null);
		<PERSON><PERSON>("UpLeft", get_UpLeft, null);
		<PERSON><PERSON>("UpRight", get_UpRight, null);
		<PERSON><PERSON>("DownLeft", get_DownLeft, null);
		<PERSON><PERSON>("DownRight", get_DownRight, null);
		<PERSON><PERSON>("Other", get_Other, null);
		<PERSON><PERSON>("All", get_All, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		<PERSON><PERSON>();
		TypeTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection>.Check = CheckType;
		StackTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection>.Push = Push;
	}

	static void Push(IntPtr L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_None(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.None);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Left(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.Left);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Right(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.Right);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Up(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.Up);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Down(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.Down);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UpLeft(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.UpLeft);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UpRight(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.UpRight);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DownLeft(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.DownLeft);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DownRight(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.DownRight);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Other(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.Other);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_All(IntPtr L)
	{
		ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection.All);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection o = (HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

