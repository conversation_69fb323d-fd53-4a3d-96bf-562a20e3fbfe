-- 跨服夺旗战场 3112

CrossFlagGrabbingBattleFieldSceneLogic = CrossFlagGrabbingBattleFieldSceneLogic or BaseClass(CommonFbLogic)

local CROSS_FLAG_CAMP_TYPE = {
	BLUE = 0,
	RED = 1,
	NEUTRAL = 2,
}

local CROSS_FLAG_AREA_EFFECT = {
	[0] = "fangxing_qiang_lan",
	[1] = "fangxing_qiang_hong",
	[2] = "fangxing_qiang_zhong",
	[3] = "fangxing_qiang_qiehuan",
}

function CrossFlagGrabbingBattleFieldSceneLogic:__init()
	self.role_creat_pos = {x = 0, y = 0}
	self.enter_point_id_cache = -1
end

function CrossFlagGrabbingBattleFieldSceneLogic:__delete()
	self:UnBindEvents()

	self.role_creat_pos = nil
	self.first_enter_auto_state_check = nil
	self.enter_point_id_cache = nil
	self.fgb_effect_cache = nil
	self:RemoveFGBSceneEffect()
end

function CrossFlagGrabbingBattleFieldSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

    self.lm_scene_enter_complete = true
	self.first_enter_auto_state_check = true
	self.enter_point_id_cache = -1

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()
		self.role_creat_pos = {x = x, y = y}
	end

	self.cf_role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.CFOnRoleEnterCallBack, self))
	self.robot_enter_event = GlobalEventSystem:Bind(SceneEventType.OBJ_ENTER_SHADOW, BindTool.Bind(self.OnRoBotEnterCallBack, self))
	self.main_role_pos_change = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE,BindTool.Bind(self.CheckFGBPointEnterOrOut,self))

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		MainuiWGCtrl.Instance:SetTaskContents(false)
		-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
		MainuiWGCtrl.Instance:SetShowTimeTextState(false)

		CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBRankView()
		CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBTaskView()
		CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBSceneTopView()
		CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBExplainView(true, false)
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	end)

	MainuiWGCtrl.Instance:SetMianUITargetPos(20, -50)

	local view = CrossFlagGrabbingBattleFieldWGCtrl.Instance:GetFGBTaskView()
	local rank_view = CrossFlagGrabbingBattleFieldWGCtrl.Instance:GetFGBRankView()
	local scene_top_view = CrossFlagGrabbingBattleFieldWGCtrl.Instance:GetFGBSceneTopView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIFuPingChangeList(rank_view)
	ViewManager.Instance:AddMainUIFuPingChangeList(scene_top_view)

	self:CreateFGBAreaEffect()
	self:UpdateRoleFGBInfo()
end

function CrossFlagGrabbingBattleFieldSceneLogic:CFOnRoleEnterCallBack(obj_id)
	local role = Scene.Instance:GetObj(obj_id)

	if role then
		local fgb_team_id = role:GetVo().special_param
		local follow_ui = role:GetFollowUi()
		follow_ui:SetRoleFGBTeamInfo(fgb_team_id)
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:OnRoBotEnterCallBack(obj_id)
	local role = Scene.Instance:GetObj(obj_id)

	if role then
		local fgb_team_id = role:GetVo().special_param
		local follow_ui = role:GetFollowUi()
		follow_ui:SetRoleFGBTeamInfo(fgb_team_id)
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:UpdateRoleFGBInfo()
	local role_list = Scene.Instance:GetRoleList()
	for i, v in pairs(role_list) do
		if not v:IsMainRole() then
			local fgb_team_id = v:GetVo().special_param
			local follow_ui = v:GetFollowUi()
			follow_ui:SetRoleFGBTeamInfo(fgb_team_id)
		end
	end

	local main_role = Scene.Instance:GetMainRole()

	if main_role then
		local team_id = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBMyCamp()
		local follow_ui = main_role:GetFollowUi()
		follow_ui:SetRoleFGBTeamInfo(team_id)
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:UnBindEvents()
	if self.cf_role_enter_event then
		GlobalEventSystem:UnBind(self.cf_role_enter_event)
		self.cf_role_enter_event = nil
	end

	if self.robot_enter_event then
		GlobalEventSystem:UnBind(self.robot_enter_event)
		self.robot_enter_event = nil
	end

	if self.main_role_pos_change then
        GlobalEventSystem:UnBind(self.main_role_pos_change)
        self.main_role_pos_change = nil
    end
end

function CrossFlagGrabbingBattleFieldSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	CrossFlagGrabbingBattleFieldWGCtrl.Instance:CloseFGBTaskView()
	CrossFlagGrabbingBattleFieldWGCtrl.Instance:CloseFGBRankView()
	CrossFlagGrabbingBattleFieldWGCtrl.Instance:CloseFGBSceneTopView()

	local view = CrossFlagGrabbingBattleFieldWGCtrl.Instance:GetFGBTaskView()
	local rank_view = CrossFlagGrabbingBattleFieldWGCtrl.Instance:GetFGBRankView()
	local scene_top_view = CrossFlagGrabbingBattleFieldWGCtrl.Instance:GetFGBSceneTopView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIFuPingChangeList(rank_view)
	ViewManager.Instance:RemoveMainUIFuPingChangeList(scene_top_view)
	MainuiWGCtrl.Instance:SetMianUITargetPos(0, 0)

	self:UnBindEvents()
	self.lm_scene_enter_complete = false
	self.role_wait_time = nil
	self.first_enter_auto_state_check = nil
	self.enter_point_id_cache = nil
	self.fgb_effect_cache = nil

	self:RemoveFGBSceneEffect()
end

function CrossFlagGrabbingBattleFieldSceneLogic:Update(now_time, elapse_time)
	if not self.lm_scene_enter_complete then
		return
	end

	BaseSceneLogic.Update(self, now_time, elapse_time)

	if self.first_enter_auto_state_check then
		self:CheckFGBFirstEnterAutoState(now_time, elapse_time)
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:CheckFGBPointEnterOrOut(x, y)
	local move_info_list = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendList()

	if self.enter_point_id_cache >= 0 then
		local contend_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendBySeq(self.enter_point_id_cache)

		if not IsEmptyTable(contend_cfg) then
			local pos_list = string.split(contend_cfg.pos, ",")
			local target_pos_x = pos_list[1]
			local target_pos_y = pos_list[2]

			if not self:SquareAreaDetection(x, y, target_pos_x, target_pos_y, contend_cfg.range) then
				self.enter_point_id_cache = -1
				TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.FlagGrabbingBattlefield.FGBOutPointDesc, contend_cfg.contend_name))
				CrossFlagGrabbingBattleFieldWGCtrl.Instance:OnOutFGBPoint(self.enter_point_id_cache)
			end
		end
	else
		for k, v in pairs(move_info_list) do
			local split_list = string.split(v.pos, ",")
			local pos_x = split_list[1]
			local pos_y = split_list[2]

			if self:SquareAreaDetection(x, y, pos_x, pos_y, v.range) and self.enter_point_id_cache ~= v.seq then
				self.enter_point_id_cache = v.seq
				TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.FlagGrabbingBattlefield.FGBEnterPointDesc, v.contend_name))
				CrossFlagGrabbingBattleFieldWGCtrl.Instance:OnEnterFGBPoint(v.seq)
			end
		end
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:SquareAreaDetection(pos_x, pos_y, center_x, center_y, range)
	return (pos_x >= (center_x - range)) and (pos_x <= (center_x + range)) and (pos_y >= (center_y - range)) and (pos_y <= (center_y + range))
end

-- 后端将棋子当作采集物处理， 前端屏蔽掉棋子采集
function CrossFlagGrabbingBattleFieldSceneLogic:GetIsCanGather(obj)
	if obj then
		local gather_type = obj:GetVo().special_gather_type
		if gather_type then
			return gather_type ~= SPECIAL_GATHER_TYPE.CROSS_FLAG_BATTLE_CONTEND
		end
	end

	return true
end

function CrossFlagGrabbingBattleFieldSceneLogic:CheckFGBFirstEnterAutoState(now_time, elapse_time)
	local main_role = Scene.Instance:GetMainRole()

	if main_role then
		if main_role:IsDeleted() then
			return
		end

		if self.role_wait_time == nil then
			self.role_wait_time = now_time
		end

		local x, y = main_role:GetLogicPos()
		if self.role_creat_pos.x ~= x or self.role_creat_pos.y ~= y or GuajiCache.guaji_type ~= GuajiType.None then
			self.first_enter_auto_state_check = false
		else
			if now_time - self.role_wait_time > 30 then
				self.role_wait_time = now_time
				self:MainRoleMoveToPoint()
				self.first_enter_auto_state_check = false
			end
		end
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:MainRoleMoveToPoint()
	local pos_x, pos_y = self:GetGuiJiAreaPos()

	local call_back = function ()
		-- 2023.02.08 策划说 得让玩家自己去寻找采集物，不去自动拾取
		-- local gather_obj = self:GetGatherObj()
		-- self:StartGather(gather_obj)

		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end

	local scene_id = Scene.Instance:GetSceneId()
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
	GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, 3, nil, nil, nil, call_back)
end

function CrossFlagGrabbingBattleFieldSceneLogic:StartGather(gather_obj)
	if gather_obj then
		GuajiWGCtrl.Instance:StopGuaji()
		local scene_id = Scene.Instance:GetSceneId()
		local x, y = gather_obj:GetLogicPos()
		local call_back = function ()
			if gather_obj then
				GuajiWGCtrl.Instance:OnSelectObj(gather_obj, SceneTargetSelectType.SCENE)
			end
		end

		GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, x, y, 3, nil, nil, nil, call_back)
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:GetGatherObj()
	local fall_obj_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
	if IsEmptyTable(fall_obj_list) then
		for k, v in pairs(fall_obj_list) do
			return v
		end
	end

	local gather_obj_list = Scene.Instance:GetObjListByType(SceneObjType.GatherObj)
	if IsEmptyTable(gather_obj_list) then
		for k, v in pairs(gather_obj_list) do
			return v
		end
	end

	return nil
end

function CrossFlagGrabbingBattleFieldSceneLogic:IsRoleEnemy(target_obj, main_role)
	if CrossFlagGrabbingBattleFieldWGCtrl.Instance:GetIsEnd() then
		return false
	end

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local target_obj_vo = target_obj:GetVo()

	if target_obj_vo.role_id == main_role_vo.role_id then
		return false
   	end

	local team_id = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBMyCamp()
	if target_obj_vo.special_param ~= team_id then
		return true
	end

  	return CommonFbLogic.IsRoleEnemy(self, target_obj, main_role)
end

function CrossFlagGrabbingBattleFieldSceneLogic:GetGuajiPos()
	local pos_x,pos_y = self:GetGuiJiAreaPos()
	return pos_x,pos_y
end

function CrossFlagGrabbingBattleFieldSceneLogic:GetGuiJiAreaPos()
    local target_distance = 1000 * 1000
    local target_x = 0
    local target_y = 0
    local x, y = Scene.Instance:GetMainRole():GetLogicPos()

    local move_info_list = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendList()
    for k, v in pairs(move_info_list) do
		local split_list = string.split(v.pos, ",")
		local pos_x = split_list[1]
		local pos_y = split_list[2]
        local not_block = not AStarFindWay:IsBlock(pos_x, pos_y)
		
		if not_block then
			local distance = GameMath.GetDistance(x, y, pos_x, pos_y, false)
			if distance < target_distance then
				target_x = pos_x
				target_y = pos_y
				target_distance = distance
			end
		end
    end

    return target_x, target_y
end

function CrossFlagGrabbingBattleFieldSceneLogic:CreateFGBAreaEffect()
	if not self.fgb_effect_list then
		self.fgb_effect_list = {}
	end

	local contend_list = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendList()
	if IsEmptyTable(contend_list) then
		return
	end

	local res_async_loader = AllocResAsyncLoader(self, "fgb_battle_effect_load" )
	if res_async_loader then
		res_async_loader:Load("uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "fgb_point_effect", nil,
		function(new_obj)
			for k, v in pairs(contend_list) do
				local pos_list = string.split(v.camp_effect_pos, ",")
				local pos_x, pos_y, pos_z = tonumber(pos_list[1]), tonumber(pos_list[2]), tonumber(pos_list[3])
				local obj = ResMgr:Instantiate(new_obj)
				obj.name = string.format(Language.FlagGrabbingBattlefield.FGBAreaEffectName, v.contend_name)
				obj.transform:SetParent(G_SceneObjLayer)
				obj:SetActive(false)
				obj.transform.localPosition = Vector3(pos_x, pos_y, pos_z)
				self.fgb_effect_list[v.seq] = obj
			end
		end)
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:RemoveFGBSceneEffect()
	if self.fgb_effect_list and #self.fgb_effect_list > 0 then
		for k,v in pairs(self.fgb_effect_list) do
			v:Destroy()
		end

		self.fgb_effect_list = nil
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:UpdateFGBEffect(old_contend_list, new_contend_list)
	local function get_area_ower(data_list, seq)
		local camp_ower = CROSS_FLAG_CAMP_TYPE.NEUTRAL
		local team_new_value0 = ((data_list[seq] or {}).camp_value_list or {})[0] or 0
		local team_new_value1 = ((data_list[seq] or {}).camp_value_list or {})[1] or 0

		if team_new_value0 == team_new_value1 then
			camp_ower = CROSS_FLAG_CAMP_TYPE.NEUTRAL
		else
			camp_ower = team_new_value0 > team_new_value1 and CROSS_FLAG_CAMP_TYPE.BLUE or CROSS_FLAG_CAMP_TYPE.RED
		end

		return camp_ower
	end

	local contend_list = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendList()
	if not IsEmptyTable(contend_list) then
		for k, v in pairs(contend_list) do
			local seq = v.seq
			local old_camp_ower = get_area_ower(old_contend_list, seq)
			local camp_ower = get_area_ower(new_contend_list, seq)
			self:UpdateFGBEffectBySeqAndType(seq, camp_ower)

			if old_camp_ower ~= camp_ower then
				self:PlayCartoomFGBEffect(seq, v)
			end
		end
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:UpdateFGBEffectBySeqAndType(seq, effect_type)
	if not self.fgb_effect_cache then
		self.fgb_effect_cache = {}
	end

	if nil ~= self.fgb_effect_cache[seq] and effect_type == self.fgb_effect_cache[seq] then
		return
	end

	if self.fgb_effect_list and self.fgb_effect_list[seq] and CROSS_FLAG_AREA_EFFECT[effect_type] then
		local bundle, asset = ResPath.GetEnvironmentZhanLingQuYuEffect(CROSS_FLAG_AREA_EFFECT[effect_type])
		local attach = self.fgb_effect_list[seq]:GetComponent(typeof(Game.GameObjectAttach))
		attach.BundleName = bundle
		attach.AssetName = asset
		self.fgb_effect_list[seq]:SetActive(false)
		self.fgb_effect_list[seq]:SetActive(true)
		self.fgb_effect_cache[seq] = effect_type
	end
end

function CrossFlagGrabbingBattleFieldSceneLogic:PlayCartoomFGBEffect(seq, data_info)
	local res_async_loader = AllocResAsyncLoader(self, "fgb_battle_effect_load" )

	if res_async_loader then
		res_async_loader:Load("uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "fgb_point_effect", nil,
		function(new_obj)
				local pos_list = string.split(data_info.camp_effect_pos,",")
				local pos_x, pos_y, pos_z = tonumber(pos_list[1]), tonumber(pos_list[2]), tonumber(pos_list[3])
				local obj = ResMgr:Instantiate(new_obj)
				obj.name = string.format(Language.FlagGrabbingBattlefield.FGBAreaFlushEffectName, data_info.contend_name)
				obj.transform:SetParent(G_SceneObjLayer)
				obj.transform.localPosition = Vector3(pos_x, pos_y, pos_z)
				local bundle, asset = ResPath.GetEnvironmentZhanLingQuYuEffect(CROSS_FLAG_AREA_EFFECT[3])
				local attach = obj:GetComponent(typeof(Game.GameObjectAttach))
				attach.BundleName = bundle
				attach.AssetName = asset
				obj:SetActive(false)
				obj:SetActive(true)

				GlobalTimerQuest:AddDelayTimer(function()
					if obj then
						obj:Destroy()
					end
				end, 3)
		end)
	end
end