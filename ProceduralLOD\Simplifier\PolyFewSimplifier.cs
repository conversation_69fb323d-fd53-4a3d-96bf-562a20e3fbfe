#if UNITY_EDITOR

using System;
using System.Collections;
using System.Collections.Generic;
using BrainFailProductions.PolyFew;
using BrainFailProductions.PolyFewRuntime;
using UnityEngine;
using Object = UnityEngine.Object;

namespace ProceduralLOD
{
    [Serializable]
    public class SimplifyDescriptor
    {
        public bool preserveUVSeamEdges = false;
        public bool preserveUVFoldoverEdges = false;
        public bool preserveBorderEdges = true;
        public bool useEdgeSort = true;
        public bool regardCurvature = true;
        public bool recalculateNormals = true;
        public bool isPreservationActive = true;
        public List<ToleranceSphere> toleranceSpheres;
    }

    [Serializable]
    public class PolyFewSimplifier : Simplifier
    {
        public SimplifyDescriptor descriptor = new SimplifyDescriptor();

        public override IEnumerator Simplify(GameObject toSimplify, float strength)
        {
            /*
            List<ToleranceSphere> preservationSpheres;
            if (descriptor.toleranceSpheres != null)
            {
                preservationSpheres = descriptor.toleranceSpheres;
            }
            else
            {
                preservationSpheres = new List<ToleranceSphere>();
            }
            
            var pair = UtilityServices.GetObjectMeshPairs(toSimplify, false, true);
            UtilityServices.dataContainer = new DataContainer();
            UtilityServices.dataContainer.preserveUVSeams = descriptor.preserveUVSeamEdges;
            UtilityServices.dataContainer.preserveUVSeams = descriptor.preserveUVSeamEdges;
            UtilityServices.dataContainer.preserveUVFoldover = descriptor.preserveUVFoldoverEdges;
            UtilityServices.dataContainer.preserveBorders = descriptor.preserveBorderEdges;
            UtilityServices.dataContainer.useEdgeSort = descriptor.useEdgeSort;
            UtilityServices.dataContainer.regardCurvature = descriptor.regardCurvature;
            UtilityServices.dataContainer.recalculateNormals = descriptor.recalculateNormals;


            bool finish = false;
            
            UtilityServices.SimplifyObjectDeep(pair, preservationSpheres, false, false, strength * 0.01f,
            OnEachMeshSimplified : (go, pair) =>
            {
                Renderer renderer = go.GetComponent<Renderer>();
                Mesh simplifiedMesh = pair.mesh;
                CommonUtility.SetMeshByRenderer(renderer, simplifiedMesh);
                finish = true;
                Debug.LogError(">>>>>>>>>>>>>>>>>>111111111");
            },
            OnError: (e) =>
            {
                Debug.LogError(e);
                finish = true;
            });

            // while (!finish)
            // {
            //     yield return null;
            // }
                Debug.LogError("######################");
            */

            List<PolyfewRuntime.PreservationSphere> preservationSpheres;
            if (descriptor.toleranceSpheres != null)
            {
                preservationSpheres = descriptor.toleranceSpheres.ConvertAll(sphere => { return new PolyfewRuntime.PreservationSphere(sphere.worldPosition, sphere.diameter, sphere.preservationStrength); });
            }
            else
            {
                preservationSpheres = new List<PolyfewRuntime.PreservationSphere>();
            }
            
            PolyfewRuntime.SimplificationOptions options = new PolyfewRuntime.SimplificationOptions()
            {
                simplificationStrength = strength,
                preserveUVSeamEdges = descriptor.preserveUVSeamEdges,
                preserveUVFoldoverEdges = descriptor.preserveUVFoldoverEdges,
                preserveBorderEdges = descriptor.preserveBorderEdges,
                useEdgeSort = descriptor.useEdgeSort,
                regardCurvature = descriptor.regardCurvature,
                regardPreservationSpheres = descriptor.isPreservationActive,
                preservationSpheres = preservationSpheres,
                recalculateNormals = descriptor.recalculateNormals,
            };
            
            Dictionary<Renderer, Mesh> r2m = new Dictionary<Renderer, Mesh>();
            foreach (var renderer in toSimplify.GetComponentsInChildren<Renderer>(true))
            {
                r2m.Add(renderer, CommonUtility.GetMeshByRenderer(renderer));
            }
            
            foreach (var pairs in PolyfewRuntime.SimplifyObjectDeep(toSimplify, options))
            {
                Renderer renderer = pairs.Key.GetComponent<Renderer>();
                Mesh simplifiedMesh = pairs.Value.mesh;
                CommonUtility.SetMeshByRenderer(renderer, simplifiedMesh);
            }

            yield return null;

        }

        
        public override void BeginModify(GameObject toModify, float strength)
        {
            base.BeginModify(toModify, strength);
            
            var polyfew = toModify.AddComponent<PolyFew>();
            var dataContainer = polyfew.dataContainer;
            dataContainer.reductionStrength = strength;
            dataContainer.considerChildren = true;
            dataContainer.preserveBorders = descriptor.preserveBorderEdges;
            dataContainer.preserveUVFoldover = descriptor.preserveUVFoldoverEdges;
            dataContainer.recalculateNormals = descriptor.recalculateNormals;
            dataContainer.useEdgeSort = descriptor.useEdgeSort;
            dataContainer.regardCurvature = descriptor.regardCurvature;
            dataContainer.preserveUVSeams = descriptor.preserveUVSeamEdges;
            dataContainer.isPreservationActive = descriptor.isPreservationActive;
            dataContainer.toleranceSpheres = descriptor.toleranceSpheres;
        }

        public override float EndModify()
        {
            PolyFew polyfew = toModify.GetComponent<PolyFew>();
            if (polyfew == null)
            {
                return 0;
            }
            
            descriptor.preserveBorderEdges = polyfew.dataContainer.preserveBorders;
            descriptor.preserveUVFoldoverEdges = polyfew.dataContainer.preserveUVFoldover;
            descriptor.recalculateNormals = polyfew.dataContainer.recalculateNormals;
            descriptor.useEdgeSort = polyfew.dataContainer.useEdgeSort;
            descriptor.preserveUVSeamEdges = polyfew.dataContainer.preserveUVSeams;
            descriptor.regardCurvature = polyfew.dataContainer.regardCurvature;
            descriptor.isPreservationActive = polyfew.dataContainer.isPreservationActive;
            descriptor.toleranceSpheres = polyfew.dataContainer.toleranceSpheres;
            float strength = polyfew.dataContainer.reductionStrength;

            return strength;
        }
    }
}
#endif