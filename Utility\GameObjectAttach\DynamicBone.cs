﻿using UnityEngine;
using System.Collections.Generic;

public enum RoleType
{
    <PERSON>, Woman,
}

[AddComponentMenu("Dynamic Bone/Dynamic Bone")]
public class DynamicBone : MonoBehaviour
{
    [Header("角色类型")]
    public RoleType CurRole = RoleType.Man;

    List<string> manBoneRoots = new List<string>()
    {
        //前裙摆
        "Bone085",
        "Bone086",
        "Bone087",
        "Bone104",
        "Bone088",

        //后裙摆
        "Bone090",
        "Bone091",
        "Bone092",
        "Bone105",
        "Bone093",

        //左裙摆
        "Bone035(mirrored)(mirrored)",
        "Bone036(mirrored)(mirrored)",
        "Bone037(mirrored)(mirrored)",
        "Bone038(mirrored)(mirrored)",

        //右裙摆
        "Bone035(mirrored)",
        "Bone036(mirrored)",
        "Bone037(mirrored)",
        "Bone038(mirrored)",

        //背部头发
        "Bone095",
        "Bone096",
        "Bone099",
        "Bone097",

        //马尾
        "Bone006",
        "Bone007",
        "Bone008",

        //马尾2
        "Bone078",
        "Bone079",
        "Bone080",
        "Bone081",

        //左翎尾
        "Bone117",
        "Bone118",
        "Bone119",
        "Bone120",
        "Bone121",

        //右翎尾
        "Bone117(mirrored)",
        "Bone118(mirrored)",
        "Bone119(mirrored)",
        "Bone120(mirrored)",
        "Bone121(mirrored)",

        //左手摆1
        "Bone026(mirrored)(mirrored)(mirrored)",
        "Bone027(mirrored)(mirrored)(mirrored)",
        "Bone028(mirrored)(mirrored)(mirrored)",
        "Bone071(mirrored)(mirrored)(mirrored)",

        //右手摆1
        "Bone026(mirrored)(mirrored)",
        "Bone027(mirrored)(mirrored)",
        "Bone028(mirrored)(mirrored)",
        "Bone071(mirrored)(mirrored)",

        //左手摆2
        "Bone073(mirrored)",
        "Bone074(mirrored)",
        "Bone075(mirrored)",

        //右手摆2
        "Bone073",
        "Bone074",
        "Bone075",

        //左手摆3
        "Bone010(mirrored)",
        "Bone011(mirrored)",
        "Bone012(mirrored)",
        "Bone013(mirrored)",
        "Bone014(mirrored)",

        //右手摆3
        "Bone010",
        "Bone011",
        "Bone012",
        "Bone013",
        "Bone014",
    };

    List<string> womanBoneRoots = new List<string>()
    {
        //前裙摆
        "Bone002",
        "Bone003",
        "Bone004",
        "Bone005",

        //后裙摆
        "Bone007",
        "Bone008",
        "Bone009",
        "Bone010",

        //左裙摆
        "Bone012(mirrored)",
        "Bone013(mirrored)",
        "Bone014(mirrored)",
        "Bone015(mirrored)",
        
        //右裙摆
        "Bone012",
        "Bone013",
        "Bone014",
        "Bone015",

        //背部头发
        "Bone032",
        "Bone033",
        "Bone034",

        //马尾
        "Bone037",
        "Bone038",
        "Bone039",
        "Bone040",
        "Bone041",

        //马尾2
        "Bone043",
        "Bone044",
        "Bone045",
        "Bone046",

        //马尾右
        "Bone052",
        "Bone053",
        "Bone054",
        "Bone055",
        "Bone056",

        //马尾左
        "Bone052(mirrored)",
        "Bone053(mirrored)",
        "Bone054(mirrored)",
        "Bone055(mirrored)",
        "Bone056(mirrored)",

        //左手摆1
        "Bone019(mirrored)",
        "Bone020(mirrored)",
        "Bone021(mirrored)",
        "Bone022(mirrored)",

        //右手摆1
        "Bone019",
        "Bone020",
        "Bone021",
        "Bone022",

        //左手摆2
        "Bone024(mirrored)",
        "Bone025(mirrored)",
        "Bone026(mirrored)",
        "Bone027(mirrored)",

        //右手摆2
        "Bone024",
        "Bone025",
        "Bone026",
        "Bone027",
        
        //左手摆3
        "Bone029(mirrored)",
        "Bone030(mirrored)",
        "Bone064(mirrored)",
        
        //右手摆3
        "Bone029",
        "Bone030",
        "Bone064",
    };

    List<string> manCJBoneRoots = new List<string>()
    {
    };

    List<string> womanCJBoneRoots = new List<string>()
    {
    };

    public enum FreezeAxis
    {
        None, X, Y, Z
    }

    public List<Transform> m_BoneRoots = new List<Transform>();

    [Header("更新频率（帧）")]
    public float m_UpdateRate = 60.0f;

    //阻尼，骨骼运动的快慢
    [Range(0, 1)]
    [Header("阻尼")]
    public float m_Damping;
    [Header("阻尼曲线")]
    private AnimationCurve m_DampingDistrib = new AnimationCurve();

    //弹性，用于将每个骨骼返回到原始方向的力
    [Range(0, 1)]
    [Header("弹性")]
    public float m_Elasticity;
    [Header("弹性曲线")]
    private AnimationCurve m_ElasticityDistrib = new AnimationCurve();

    //刚度，骨骼的原始方向会保留多少
    [Range(0, 1)]
    [Header("刚度")]
    public float m_Stiffness;
    [Header("刚度曲线")]
    private AnimationCurve m_StiffnessDistrib = new AnimationCurve();

    //惰性，在物理模拟中会忽略多少角色的位置变化
    [Range(0, 1)]
    [Header("惰性")]
    public float m_Inert;
    [Header("惰性曲线")]
    private AnimationCurve m_InertDistrib = new AnimationCurve();

    //球体或胶囊碰撞体的半径，受比例缩放影响
    [Header("骨骼点碰撞半径")]
    public float m_Radius;
    [Header("骨骼点碰撞半径曲线")]
    private AnimationCurve m_RadiusDistrib = new AnimationCurve();

    [Header("重力")]
    public Vector3 Gravity = Vector3.zero;

    [Header("风力")]
    public Vector3 Force = Vector3.zero;

    [Header("额外骨骼生成长度")]
    public float m_EndLength = 0;

    [Header("额外骨骼生成偏移")]
    public Vector3 m_EndOffset = Vector3.zero;

    [Header("Body碰撞体列表")]
    public List<DynamicBoneCollider> Colliders = new List<DynamicBoneCollider>();

    [Header("排除在物理模拟之外的骨骼节点")]
    public List<Transform> Exclusions = new List<Transform>();

    [Header("限制骨骼在指定平面上移动")]
    public FreezeAxis m_FreezeAxis = FreezeAxis.None;


    [Header("是否创角模型")]
    public bool isChuangJue =false;

    private bool m_DistantDisable = false;
    private Transform m_ReferenceObject = null;
    private float m_DistanceToObject = 20;

    Vector3 m_LocalGravity = Vector3.zero;
    Vector3 m_ObjectMove = Vector3.zero;
    Vector3 m_ObjectPrevPosition = Vector3.zero;

    float m_BoneTotalLength = 0;
    float m_ObjectScale = 1.0f;
    float m_Time = 0;
    float m_Weight = 1.0f;
    bool m_DistantDisabled = false;

    DynamicBoneCollider LThighDynamicBoneCollider = null;
    DynamicBoneCollider RThighDynamicBoneCollider = null;

    //骨骼节点
    class Particle
    {
        public Transform m_Transform = null;

        public int m_ParentIndex = -1;  //节点位置
        public float m_Damping = 0;     //阻尼
        public float m_Elasticity = 0;  //弹性
        public float m_Stiffness = 0;   //刚度
        public float m_Inert = 0;       //惰性
        public float m_Radius = 0;      //碰撞半径
        public float m_BoneLength = 0;  //

        public Vector3 m_Position = Vector3.zero;
        public Vector3 m_PrevPosition = Vector3.zero;
        public Vector3 m_EndOffset = Vector3.zero;
        public Vector3 m_InitLocalPosition = Vector3.zero;

        public Quaternion m_InitLocalRotation = Quaternion.identity;
    }

    //骨骼节点列表
    List<Particle> m_Particles = new List<Particle>();
    Dictionary<string, List<Particle>> m_ParticleDir = new Dictionary<string, List<Particle>>();

    void Start()
    {
        if (m_BoneRoots.Count == 0)
        {
            List<string> rootNames = CurRole == RoleType.Man ? manBoneRoots : womanBoneRoots;
            InitBoneRoots(rootNames);
        }
        InitBoneCollider();
        SetupParticles();
    }

    void OnEnable()
    {
        ResetParticlesPosition();
    }

    void Update()
    {
        if (m_Weight > 0 && !(m_DistantDisable && m_DistantDisabled))
            InitTransforms();
    }

    void LateUpdate()
    {
        if (m_DistantDisable)
            CheckDistance();

        if (m_Weight > 0 && !(m_DistantDisable && m_DistantDisabled))
            UpdateDynamicBones(Time.deltaTime);
    }

    void OnDisable()
    {
        InitTransforms();
    }

    void OnDestroy()
    {
        if (LThighDynamicBoneCollider != null)
        {
            Destroy(LThighDynamicBoneCollider);
            LThighDynamicBoneCollider = null;
        }
        if (RThighDynamicBoneCollider != null)
        {
            Destroy(RThighDynamicBoneCollider);
            RThighDynamicBoneCollider = null;
        }
        Colliders.Clear();
    }

    public void InitDynamicBone(RoleType roleType,
        float updateRate = 60,
        float damping = 0.87f,
        float elasticity = 0.12f,
        float stiffness = 0.48f,
        float inert = 0.6f,
        float radius = 0.05f,
        float gravityX = 0,
        float gravityY = 0,
        float gravityZ = 0,
        float forceX = 0,
        float forceY = 0,
        float forceZ = 0)
    {
        CurRole = roleType;
        this.m_UpdateRate = updateRate;
        this.m_Damping = damping;
        this.m_Elasticity = elasticity;
        this.m_Stiffness = stiffness;
        this.m_Inert = inert;
        this.m_Radius = radius;

        this.Gravity = new Vector3(gravityX, gravityY, gravityZ);
        this.Force = new Vector3(forceX, forceY, forceZ);

        m_BoneRoots.Clear();

        //if (isChuangJue)
        //    InitBoneRoots(CurRole == RoleType.Man ? manCJBoneRoots : womanCJBoneRoots);
        //else
        //    InitBoneRoots(CurRole == RoleType.Man ? manBoneRoots : womanBoneRoots);
        InitBoneRoots(CurRole == RoleType.Man ? manBoneRoots : womanBoneRoots);
        InitBoneCollider();
        SetupParticles();
    }

    void InitBoneRoots(List<string> rootNames)
    {
        Transform bip = this.transform.Find("root/Bip001");
        if (bip == null)
        {
            return;
        }

        Transform[] trans = this.transform.Find("root/Bip001").gameObject.GetComponentsInChildren<Transform>();
        foreach (Transform tr in trans)
        {
            if (rootNames.Contains(tr.name) && !m_BoneRoots.Contains(tr))
            {
                m_BoneRoots.Add(tr.transform);
            }
            if (rootNames.Count == m_BoneRoots.Count)
            {
                break;
            }
        }
    }

    void InitBoneCollider()
    {
        Transform LThigh = this.transform.Find("root/Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 L Thigh");
        if (LThigh)
        {
            LThighDynamicBoneCollider = LThigh.GetComponent<DynamicBoneCollider>();
            if (LThighDynamicBoneCollider == null)
            {
                LThighDynamicBoneCollider = LThigh.gameObject.AddComponent<DynamicBoneCollider>();
            }
            LThighDynamicBoneCollider.m_Center = CurRole == RoleType.Man ? new Vector3(-0.42f, -0.03f, 0.05f) : new Vector3(-0.22f, 0.06f, -0.02f);
            LThighDynamicBoneCollider.m_Radius = CurRole == RoleType.Man ? 0.18f : 0.12f;
            LThighDynamicBoneCollider.m_Height = CurRole == RoleType.Man ? 0.88f : 0.71f;
            Colliders.Add(LThighDynamicBoneCollider);
        }
        Transform RThigh = this.transform.Find("root/Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 R Thigh");
        if (RThigh)
        {
            RThighDynamicBoneCollider = RThigh.GetComponent<DynamicBoneCollider>();
            if (RThighDynamicBoneCollider == null)
            {
                RThighDynamicBoneCollider = RThigh.gameObject.AddComponent<DynamicBoneCollider>();
            }
            RThighDynamicBoneCollider.m_Center = CurRole == RoleType.Man ? new Vector3(-0.33f, -0.05f, 0) : new Vector3(-0.13f, -0.03f, 0);
            RThighDynamicBoneCollider.m_Radius = CurRole == RoleType.Man ? 0.1f : 0.12f;
            RThighDynamicBoneCollider.m_Height = CurRole == RoleType.Man ? 0.88f : 0.71f;
            Colliders.Add(RThighDynamicBoneCollider);
        }
    }

    void CheckDistance()
    {
        Transform rt = m_ReferenceObject;
        if (rt == null && Camera.main != null)
        {
            rt = Camera.main.transform;
        }

        if (rt != null)
        {
            float d = (rt.position - transform.position).sqrMagnitude;
            bool disable = d > m_DistanceToObject * m_DistanceToObject;
            if (disable != m_DistantDisabled)
            {
                if (!disable)
                    ResetParticlesPosition();
                m_DistantDisabled = disable;
            }
        }
    }

    void OnValidate()
    {
        m_UpdateRate = Mathf.Max(m_UpdateRate, 0);
        m_Damping = Mathf.Clamp01(m_Damping);
        m_Elasticity = Mathf.Clamp01(m_Elasticity);
        m_Stiffness = Mathf.Clamp01(m_Stiffness);
        m_Inert = Mathf.Clamp01(m_Inert);
        m_Radius = Mathf.Max(m_Radius, 0);

        if (Application.isEditor && Application.isPlaying)
        {
            InitTransforms();
            SetupParticles();
        }
    }

    void OnDrawGizmosSelected()
    {
        if (!enabled || m_BoneRoots.Count == 0)
            return;

        if (Application.isEditor && !Application.isPlaying && transform.hasChanged)
        {
            InitTransforms();
            SetupParticles();
        }

        Gizmos.color = Color.white;
        foreach (KeyValuePair<string, List<Particle>> dir in m_ParticleDir)
        {
            List<Particle> particles = dir.Value;
            for (int i = 0; i < particles.Count; ++i)
            {
                Particle p = particles[i];
                if (p.m_ParentIndex >= 0 && p.m_ParentIndex < particles.Count)
                {
                    Particle p0 = particles[p.m_ParentIndex];
                    Gizmos.DrawLine(p.m_Position, p0.m_Position);
                }
                if (p.m_Radius > 0)
                    Gizmos.DrawWireSphere(p.m_Position, p.m_Radius * m_ObjectScale);
            }
        }
    }

    public void SetWeight(float w)
    {
        if (m_Weight != w)
        {
            if (w == 0)
                InitTransforms();
            else if (m_Weight == 0)
                ResetParticlesPosition();

            m_Weight = w;
        }
    }

    public float GetWeight()
    {
        return m_Weight;
    }

    void UpdateDynamicBones(float t)
    {
        if (m_BoneRoots.Count == 0)
            return;

        m_ObjectScale = Mathf.Abs(transform.lossyScale.x);
        m_ObjectMove = transform.position - m_ObjectPrevPosition;
        m_ObjectPrevPosition = transform.position;

        int loop = 1;
        if (m_UpdateRate > 0)
        {
            float dt = 1.0f / m_UpdateRate;
            m_Time += t;
            loop = 0;

            while (m_Time >= dt)
            {
                m_Time -= dt;
                if (++loop >= 3)
                {
                    m_Time = 0;
                    break;
                }
            }
        }

        if (loop > 0)
        {
            for (int i = 0; i < loop; ++i)
            {
                UpdateParticles1();
                UpdateParticles2();
                m_ObjectMove = Vector3.zero;
            }
        }
        else
        {
            SkipUpdateParticles();
        }

        ApplyParticlesToTransforms();
    }

    void SetupParticles()
    {
        m_ParticleDir.Clear();
        if (m_BoneRoots.Count == 0)
            return;

        m_LocalGravity = m_BoneRoots[0].InverseTransformDirection(Gravity);
        m_ObjectScale = transform.lossyScale.x;
        m_ObjectPrevPosition = transform.position;
        m_ObjectMove = Vector3.zero;
        m_BoneTotalLength = 0;

        for (int i = 0; i < m_BoneRoots.Count; i++)
        {
            Transform tr = m_BoneRoots[i];
            if (tr == null)
                continue;

            List<Particle> p_list = AppendParticleList(tr, -1, 0);
            if (!m_ParticleDir.ContainsKey(tr.gameObject.name))
            {
                m_ParticleDir.Add(tr.gameObject.name, p_list);
            }
        }

        foreach (KeyValuePair<string, List<Particle>> dir in m_ParticleDir)
        {
            List<Particle> particles = dir.Value;
            for (int i = 0; i < particles.Count; ++i)
            {
                Particle p = particles[i];
                p.m_Damping = m_Damping;
                p.m_Elasticity = m_Elasticity;
                p.m_Stiffness = m_Stiffness;
                p.m_Inert = m_Inert;
                p.m_Radius = m_Radius;

                if (m_BoneTotalLength > 0)
                {
                    float a = p.m_BoneLength / m_BoneTotalLength;
                    if (m_DampingDistrib != null && m_DampingDistrib.keys.Length > 0)
                        p.m_Damping *= m_DampingDistrib.Evaluate(a);
                    if (m_ElasticityDistrib != null && m_ElasticityDistrib.keys.Length > 0)
                        p.m_Elasticity *= m_ElasticityDistrib.Evaluate(a);
                    if (m_StiffnessDistrib != null && m_StiffnessDistrib.keys.Length > 0)
                        p.m_Stiffness *= m_StiffnessDistrib.Evaluate(a);
                    if (m_InertDistrib != null && m_InertDistrib.keys.Length > 0)
                        p.m_Inert *= m_InertDistrib.Evaluate(a);
                    if (m_RadiusDistrib != null && m_RadiusDistrib.keys.Length > 0)
                        p.m_Radius *= m_RadiusDistrib.Evaluate(a);
                }

                p.m_Damping = Mathf.Clamp01(p.m_Damping);
                p.m_Elasticity = Mathf.Clamp01(p.m_Elasticity);
                p.m_Stiffness = Mathf.Clamp01(p.m_Stiffness);
                p.m_Inert = Mathf.Clamp01(p.m_Inert);
                p.m_Radius = Mathf.Max(p.m_Radius, 0);
            }
        }
    }

    List<Particle> AppendParticleList(Transform bone, int parentIndex, float boneLength)
    {
        List<Particle> m_Particles = m_ParticleDir.ContainsKey(bone.gameObject.name)
            ? m_ParticleDir[bone.gameObject.name] : new List<Particle>();

        AppendParticles(bone, parentIndex, boneLength, m_Particles);

        return m_Particles;
    }

    void AppendParticles(Transform bone, int parentIndex, float boneLength, List<Particle> particleList)
    {
        Particle p = new Particle();
        p.m_Transform = bone;
        p.m_ParentIndex = parentIndex;

        if (bone != null)
        {
            p.m_Position = p.m_PrevPosition = bone.position;
            p.m_InitLocalPosition = bone.localPosition;
            p.m_InitLocalRotation = bone.localRotation;
        }
        else 	// end bone
        {
            Transform pb = particleList[parentIndex].m_Transform;
            if (m_EndLength > 0)
            {
                Transform ppb = pb.parent;
                if (ppb != null)
                    p.m_EndOffset = pb.InverseTransformPoint((pb.position * 2 - ppb.position)) * m_EndLength;
                else
                    p.m_EndOffset = new Vector3(m_EndLength, 0, 0);
            }
            else
            {
                p.m_EndOffset = pb.InverseTransformPoint(transform.TransformDirection(m_EndOffset) + pb.position);
            }
            p.m_Position = p.m_PrevPosition = pb.TransformPoint(p.m_EndOffset);
        }

        if (parentIndex >= 0 && particleList[parentIndex] != null)
        {
            boneLength += (particleList[parentIndex].m_Transform.position - p.m_Position).magnitude;
            p.m_BoneLength = boneLength;
            m_BoneTotalLength = Mathf.Max(m_BoneTotalLength, boneLength);
        }

        int index = 0;
        if (particleList != null)
        {
            index = particleList.Count;
            particleList.Add(p);
        }

        if (bone != null)
        {
            for (int i = 0; i < bone.childCount; ++i)
            {
                bool exclude = false;
                if (Exclusions != null)
                {
                    for (int j = 0; j < Exclusions.Count; ++j)
                    {
                        Transform e = Exclusions[j];
                        if (e == bone.GetChild(i))
                        {
                            exclude = true;
                            break;
                        }
                    }
                }
                if (!exclude)
                    AppendParticles(bone.GetChild(i), index, boneLength, particleList);
            }

            if (bone.childCount == 0 && (m_EndLength > 0 || m_EndOffset != Vector3.zero))
                AppendParticles(null, index, boneLength, particleList);
        }
    }

    void InitTransforms()
    {
        if (m_ParticleDir.Count == 0)
            return;

        foreach (KeyValuePair<string, List<Particle>> dir in m_ParticleDir)
        {
            List<Particle> particles = dir.Value;
            for (int i = 0; i < particles.Count; ++i)
            {
                Particle p = particles[i];
                if (p.m_Transform != null)
                {
                    p.m_Transform.localPosition = p.m_InitLocalPosition;
                    p.m_Transform.localRotation = p.m_InitLocalRotation;
                }
            }
        }
    }

    void ResetParticlesPosition()
    {
        if (m_ParticleDir.Count == 0)
            return;

        foreach (KeyValuePair<string, List<Particle>> dir in m_ParticleDir)
        {
            List<Particle> particles = dir.Value;
            for (int i = 0; i < particles.Count; ++i)
            {
                Particle p = particles[i];
                if (p.m_Transform != null)
                {
                    p.m_Position = p.m_PrevPosition = p.m_Transform.position;
                }
                else    // end bone
                {
                    Transform pb = particles[p.m_ParentIndex].m_Transform;
                    p.m_Position = p.m_PrevPosition = pb.TransformPoint(p.m_EndOffset);
                }
            }
        }
        m_ObjectPrevPosition = transform.position;
    }

    void UpdateParticles1()
    {
        Vector3 force = Gravity;
        Vector3 fdir = Gravity.normalized;
        Vector3 rf = m_BoneRoots[0].TransformDirection(m_LocalGravity);
        Vector3 pf = fdir * Mathf.Max(Vector3.Dot(rf, fdir), 0);    // project current gravity to rest gravity
        force -= pf;    // remove projected gravity
        force = (force + Force) * m_ObjectScale;

        foreach (KeyValuePair<string, List<Particle>> dir in m_ParticleDir)
        {
            List<Particle> particles = dir.Value;

            for (int i = 0; i < particles.Count; ++i)
            {
                Particle p = particles[i];
                if (p.m_ParentIndex >= 0)
                {
                    // verlet integration
                    Vector3 v = p.m_Position - p.m_PrevPosition;
                    Vector3 rmove = m_ObjectMove * p.m_Inert;
                    p.m_PrevPosition = p.m_Position + rmove;
                    p.m_Position += v * (1 - p.m_Damping) + force + rmove;
                }
                else
                {
                    p.m_PrevPosition = p.m_Position;
                    p.m_Position = p.m_Transform.position;
                }
            }
        }
    }

    void UpdateParticles2()
    {
        Plane movePlane = new Plane();
        foreach (KeyValuePair<string, List<Particle>> dir in m_ParticleDir)
        {
            List<Particle> particles = dir.Value;
            for (int i = 1; i < particles.Count; ++i)
            {
                Particle p = particles[i];
                if (p.m_ParentIndex < 0 || p.m_ParentIndex >= particles.Count)
                    continue;
                Particle p0 = particles[p.m_ParentIndex];

                float restLen;
                if (p.m_Transform != null)
                    restLen = (p0.m_Transform.position - p.m_Transform.position).magnitude;
                else
                    restLen = p0.m_Transform.localToWorldMatrix.MultiplyVector(p.m_EndOffset).magnitude;

                // keep shape
                float stiffness = Mathf.Lerp(1.0f, p.m_Stiffness, m_Weight);
                if (stiffness > 0 || p.m_Elasticity > 0)
                {
                    Matrix4x4 m0 = p0.m_Transform.localToWorldMatrix;
                    m0.SetColumn(3, p0.m_Position);
                    Vector3 restPos;
                    if (p.m_Transform != null)
                        restPos = m0.MultiplyPoint3x4(p.m_Transform.localPosition);
                    else
                        restPos = m0.MultiplyPoint3x4(p.m_EndOffset);

                    Vector3 d = restPos - p.m_Position;
                    p.m_Position += d * p.m_Elasticity;

                    if (stiffness > 0)
                    {
                        d = restPos - p.m_Position;
                        float len = d.magnitude;
                        float maxlen = restLen * (1 - stiffness) * 2;
                        if (len > maxlen)
                            p.m_Position += d * ((len - maxlen) / len);
                    }
                }

                // collide
                if (Colliders != null)
                {
                    float particleRadius = p.m_Radius * m_ObjectScale;
                    for (int j = 0; j < Colliders.Count; ++j)
                    {
                        DynamicBoneCollider c = Colliders[j];
                        if (c != null && c.enabled)
                            c.Collide(ref p.m_Position, particleRadius);
                    }
                }

                // freeze axis, project to plane
                if (m_FreezeAxis != FreezeAxis.None)
                {
                    switch (m_FreezeAxis)
                    {
                        case FreezeAxis.X:
                            movePlane.SetNormalAndPosition(p0.m_Transform.right, p0.m_Position);
                            break;
                        case FreezeAxis.Y:
                            movePlane.SetNormalAndPosition(p0.m_Transform.up, p0.m_Position);
                            break;
                        case FreezeAxis.Z:
                            movePlane.SetNormalAndPosition(p0.m_Transform.forward, p0.m_Position);
                            break;
                    }
                    p.m_Position -= movePlane.normal * movePlane.GetDistanceToPoint(p.m_Position);
                }

                // keep length
                Vector3 dd = p0.m_Position - p.m_Position;
                float leng = dd.magnitude;
                if (leng > 0)
                {
                    p.m_Position += dd * ((leng - restLen) / leng);
                }
            }
        }
    }

    // only update stiffness and keep bone length
    void SkipUpdateParticles()
    {
        foreach (KeyValuePair<string, List<Particle>> dir in m_ParticleDir)
        {
            List<Particle> particles = dir.Value;
            for (int i = 0; i < particles.Count; ++i)
            {
                Particle p = particles[i];
                if (p.m_ParentIndex >= 0 && p.m_ParentIndex < particles.Count)
                {
                    p.m_PrevPosition += m_ObjectMove;
                    p.m_Position += m_ObjectMove;

                    Particle p0 = particles[p.m_ParentIndex];

                    float restLen;
                    if (p.m_Transform != null)
                        restLen = (p0.m_Transform.position - p.m_Transform.position).magnitude;
                    else
                        restLen = p0.m_Transform.localToWorldMatrix.MultiplyVector(p.m_EndOffset).magnitude;

                    // keep shape
                    float stiffness = Mathf.Lerp(1.0f, p.m_Stiffness, m_Weight);
                    if (stiffness > 0)
                    {
                        Matrix4x4 m0 = p0.m_Transform.localToWorldMatrix;
                        m0.SetColumn(3, p0.m_Position);
                        Vector3 restPos;
                        if (p.m_Transform != null)
                            restPos = m0.MultiplyPoint3x4(p.m_Transform.localPosition);
                        else
                            restPos = m0.MultiplyPoint3x4(p.m_EndOffset);

                        Vector3 d = restPos - p.m_Position;
                        float len = d.magnitude;
                        float maxlen = restLen * (1 - stiffness) * 2;
                        if (len > maxlen)
                            p.m_Position += d * ((len - maxlen) / len);
                    }

                    // keep length
                    Vector3 dd = p0.m_Position - p.m_Position;
                    float leng = dd.magnitude;
                    if (leng > 0)
                        p.m_Position += dd * ((leng - restLen) / leng);
                }
                else
                {
                    p.m_PrevPosition = p.m_Position;
                    p.m_Position = p.m_Transform.position;
                }
            }
        }
    }

    Vector3 MirrorVector(Vector3 v, Vector3 axis)
    {
        return v - axis * (Vector3.Dot(v, axis) * 2);
    }

    void ApplyParticlesToTransforms()
    {
        // detect negative scale
        Vector3 ax = Vector3.right;
        Vector3 ay = Vector3.up;
        Vector3 az = Vector3.forward;
        Vector3 ls = transform.localScale;
        bool nx = ls.x < 0;
        if (nx)
            ax = transform.right;
        bool ny = ls.y < 0;
        if (ny)
            ay = transform.up;
        bool nz = ls.z < 0;
        if (nz)
            az = transform.forward;
        Transform pt = transform.parent;
        if (pt != null)
        {
            ls = pt.localScale;
            if (!nx && ls.x < 0)
            {
                nx = true;
                ax = pt.right;
            }
            if (!ny && ls.y < 0)
            {
                ny = true;
                ay = pt.up;
            }
            if (!nz && ls.z < 0)
            {
                nz = true;
                az = pt.forward;
            }
        }

        foreach (KeyValuePair<string, List<Particle>> dir in m_ParticleDir)
        {
            List<Particle> particles = dir.Value;
            for (int i = 1; i < particles.Count; ++i)
            {
                Particle p = particles[i];
                if (p.m_ParentIndex < 0 || p.m_ParentIndex >= particles.Count)
                    continue;
                Particle p0 = particles[p.m_ParentIndex];

                if (p0.m_Transform.childCount <= 1)     // do not modify bone orientation if has more then one child
                {
                    Vector3 v;
                    if (p.m_Transform != null)
                        v = p.m_Transform.localPosition;
                    else
                        v = p.m_EndOffset;
                    Vector3 v2 = p.m_Position - p0.m_Position;
                    if (nx)
                        v2 = MirrorVector(v2, ax);
                    if (ny)
                        v2 = MirrorVector(v2, ay);
                    if (nz)
                        v2 = MirrorVector(v2, az);
                    Quaternion rot = Quaternion.FromToRotation(p0.m_Transform.TransformDirection(v), v2);
                    p0.m_Transform.rotation = rot * p0.m_Transform.rotation;
                }

                if (p.m_Transform != null)
                    p.m_Transform.position = p.m_Position;
            }
        }
    }

    #region Get/Set


    public float Damping
    {
        get { return m_Damping; }
        set { m_Damping = value; }
    }
    public AnimationCurve DampingDistrib
    {
        get { return m_DampingDistrib; }
        set { m_DampingDistrib = value; }
    }
    public float Elasticity
    {
        get { return m_Elasticity; }
        set { m_Elasticity = value; }
    }
    public AnimationCurve ElasticityDistrib
    {
        get { return m_ElasticityDistrib; }
        set { m_ElasticityDistrib = value; }
    }
    public float Stiffness
    {
        get { return m_Stiffness; }
        set { m_Stiffness = value; }
    }
    public AnimationCurve StiffnessDistrib
    {
        get { return m_StiffnessDistrib; }
        set { m_StiffnessDistrib = value; }
    }
    public float Inert
    {
        get { return m_Inert; }
        set { m_Inert = value; }
    }
    public AnimationCurve InertDistrib
    {
        get { return m_InertDistrib; }
        set { m_InertDistrib = value; }
    }
    public float Radius
    {
        get { return m_Radius; }
        set { m_Radius = value; }
    }
    public AnimationCurve RadiusDistrib
    {
        get { return m_RadiusDistrib; }
        set { m_RadiusDistrib = value; }
    }

    #endregion
}
