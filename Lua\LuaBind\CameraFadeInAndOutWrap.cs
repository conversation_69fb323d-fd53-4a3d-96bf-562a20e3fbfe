﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CameraFadeInAndOutWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(CameraFadeInAndOut), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("TriggerFade", TriggerFade);
		<PERSON><PERSON>unction("SetCameraDepth", SetCameraDepth);
		<PERSON><PERSON>unction("KillFadeTween", KillFadeTween);
		L.RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("shader", get_shader, set_shader);
		<PERSON><PERSON>("fadeProcess", get_fadeProcess, set_fadeProcess);
		L.Reg<PERSON>ar("time", get_time, set_time);
		L.Reg<PERSON>ar("Instance", get_Instance, set_Instance);
		L<PERSON>Class();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TriggerFade(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFadeInAndOut obj = (CameraFadeInAndOut)ToLua.CheckObject<CameraFadeInAndOut>(L, 1);
			obj.TriggerFade();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCameraDepth(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraFadeInAndOut obj = (CameraFadeInAndOut)ToLua.CheckObject<CameraFadeInAndOut>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetCameraDepth(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int KillFadeTween(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFadeInAndOut obj = (CameraFadeInAndOut)ToLua.CheckObject<CameraFadeInAndOut>(L, 1);
			obj.KillFadeTween();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFadeInAndOut obj = (CameraFadeInAndOut)o;
			UnityEngine.Shader ret = obj.shader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fadeProcess(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFadeInAndOut obj = (CameraFadeInAndOut)o;
			float ret = obj.fadeProcess;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeProcess on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_time(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFadeInAndOut obj = (CameraFadeInAndOut)o;
			float ret = obj.time;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index time on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Instance(IntPtr L)
	{
		try
		{
			ToLua.Push(L, CameraFadeInAndOut.Instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFadeInAndOut obj = (CameraFadeInAndOut)o;
			UnityEngine.Shader arg0 = (UnityEngine.Shader)ToLua.CheckObject(L, 2, typeof(UnityEngine.Shader));
			obj.shader = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fadeProcess(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFadeInAndOut obj = (CameraFadeInAndOut)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.fadeProcess = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeProcess on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_time(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFadeInAndOut obj = (CameraFadeInAndOut)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.time = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index time on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Instance(IntPtr L)
	{
		try
		{
			CameraFadeInAndOut arg0 = (CameraFadeInAndOut)ToLua.CheckObject<CameraFadeInAndOut>(L, 2);
			CameraFadeInAndOut.Instance = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

