ZCRunMsgRightView = ZCRunMsgRightView or BaseClass(SafeBaseView)

local INIT_POS = Vector2(-194, -235)
local END_POS = Vector2(258, -235)

function ZCRunMsgRightView:__init()
    self:AddViewResource(0, "uis/view/kuafu_yezhan<PERSON>_ui_prefab", "zc_run_msg_right_view")
end

function ZCRunMsgRightView:__delete()
end

function ZCRunMsgRightView:ReleaseCallBack()
    self.show_info = nil
    self.tween_alpha = nil
    self.tween_end_pos = nil
    self.tween_pos = nil
    self.load_callback = nil
    self.role_info = nil
end

function ZCRunMsgRightView:OpenCallBack()

end

function ZCRunMsgRightView:LoadCallBack()
    self.load_callback = true
    if self.need_show_tween then
        self.need_show_tween = nil
        self:ShowTween()
    end
end

function ZCRunMsgRightView:ShowIndexCallBack()
    --self:Flush()
end

function ZCRunMsgRightView:OnFlush()
    if not self.role_info then
        return
    end
    XUI.UpdateRoleHead(self.node_list["role_head"], self.node_list["custom_head"], self.role_info.killer_uid, self.role_info.killer_sex, nil, nil, true)
    self.node_list["name"].text.text = self.role_info.killer_name
    local param = self.role_info.param
    if self.role_info.broadcast_type == 0 then -- 0 击杀  1 终结
        self.node_list["desc1"].text.text = Language.YeZhanWangCheng.KillDesc1_1
        self.node_list["desc2"].text.text = string.format(Language.YeZhanWangCheng.KillDesc1_2, param)
        if param <= 10 then
            self.node_list["desc3"].text.text = Language.YeZhanWangCheng["KillDesc1_3_" .. param]
        else
            self.node_list["desc3"].text.text = Language.YeZhanWangCheng.KillDesc1_3_100
        end
    else
        self.node_list["desc1"].text.text = string.format(Language.YeZhanWangCheng.KillDesc2_1, self.role_info.target_name)
        self.node_list["desc2"].text.text = string.format(Language.YeZhanWangCheng.KillDesc2_2, self.role_info.param or "")
        self.node_list["desc3"].text.text = ""
    end
end

function ZCRunMsgRightView:Show(t_info)
    self.role_info = t_info
    self:Open()
    self:ShowTween()
end

function ZCRunMsgRightView:ShowTween()
    if not self.load_callback then
        self.need_show_tween = true
        return
    end
    if self.show_info then
        if self.tween_alpha then
            self.tween_alpha:Kill()
        end
        if self.tween_end_pos then
            self.tween_end_pos:Kill()
        end
        GlobalTimerQuest:CancelQuest(self.show_info)
        self.tween_pos = self.node_list["all_view"].rect:DOAnchorPosX(END_POS.x, 0.2)
        self.tween_pos:SetEase(DG.Tweening.Ease.Linear)
        self.tween_pos:OnComplete(function()
            self:StartTween()
            self.tween_pos = nil
        end)
    else
        self:StartTween()
    end

end

function ZCRunMsgRightView:StartTween()
    self:Flush()
    self.node_list["all_view"].rect.anchoredPosition = INIT_POS
    self.node_list["all_view"].canvas_group.alpha = 0

    self.tween_alpha = self.node_list["all_view"].canvas_group:DoAlpha(0,1,0.3)
    self.tween_alpha:SetEase(DG.Tweening.Ease.Linear)
    self.tween_alpha:OnComplete(function()
        self.tween_alpha = nil
    end)

    self.show_info = GlobalTimerQuest:AddDelayTimer(function()
        self.tween_end_pos = self.node_list["all_view"].rect:DOAnchorPosX(END_POS.x, 0.2)
        self.tween_end_pos:SetEase(DG.Tweening.Ease.Linear)
        self.tween_end_pos:OnComplete(function()
                self:Close()
                self.show_info = nil
        end)
    end, 3)
end