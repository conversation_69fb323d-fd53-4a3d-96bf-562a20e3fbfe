

function BiZuoView:InitWeekCalendarView()
    if not self.week_calendar_list then
        self.week_calendar_list = AsyncListView.New(BiZuoWeekCalendarRender, self.node_list["ph_week_calendar_list"])
    end
    local data = BiZuoWGData.Instance:GetWeekCalendarItemData(true)
    self.week_calendar_list:SetDataList(data)
    local ymd = os.date("%Y/%m/%d", os.time())
    self.node_list.ZL_date.text.text = ymd
end

function BiZuoView:DeleteWeekCalendarView()
    if self.week_calendar_list then
        self.week_calendar_list:DeleteMe()
        self.week_calendar_list = nil
    end
end

function BiZuoView:GetSomeDayOnWeek()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local cur_week_day = TimeUtil.FormatSecond3MYHM1(server_time)
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 1
    local temp_day = 3 - open_day
    if temp_day < 0 then
        return -1
    end
    local new_week_day = cur_week_day + temp_day
    if new_week_day > 7 then
        new_week_day = new_week_day - 7
    end
    return new_week_day
end


----------------------------------------------------------------------------
--BiZuoWeekCalendarRender
----------------------------------------------------------------------------
BiZuoWeekCalendarRender = BiZuoWeekCalendarRender or BaseClass(BaseRender)
function BiZuoWeekCalendarRender:__init()
    for i=1 , 7 do
       self.node_list["tip_btn_"..i].button:AddClickListener(BindTool.Bind(self.OnClickActIcon,self,i))
    end   
end

function BiZuoWeekCalendarRender:__delete()
end

function BiZuoWeekCalendarRender:OnFlush()
    if nil == self.data then
        return
    end
    local hour, minute = math.modf(self.data.activity_time / 100)
    minute = string.format("%0.2f",minute)
    if minute * 100 ~= 0 and minute * 100 > 10 then
        minute = minute * 100
    elseif minute * 100 ~= 0 and minute * 100 < 10 then
         minute = string.format("0%s",minute *100)
    else
        minute = "00"
    end
    -- minute = minute * 100 ~= 0 and minute * 100 or "00"
    local time_text = hour .. ":" .. minute
    self.node_list["lbl_activity_time"].text.text = (time_text)

    local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local updata_time = math.max(server_time, 0) -- 0点刷新时间
    local w_day = tonumber(os.date("%w", updata_time))
    local timer_list = Split(os.date("%X",server_time),":")
    local timer = (timer_list[1]*100 + timer_list[2])+timer_list[3]/100
    if 0 == w_day then w_day = 7 end

    local replace_name_list = {}
    if self.data.replace_name ~= "" then
        replace_name_list = Split(self.data.replace_name, "|")
    end

    for i = 1, 7 do
        local i_open_server_day = cur_openserver_day - (w_day - i)
        if i_open_server_day <= 0 then
            --策划需求:
            --当开服第一天是周六/周日时,本周一/周二显示将替换为下周展示替换(目前开服前三天才会进行固定替换)
            i_open_server_day = i_open_server_day + 7
        end
        self.activity_name = self.node_list["lbl_activity_name_" .. i]
        self.pos = self.activity_name.transform.localPosition
        local name_des = ""
        name_des = replace_name_list[i_open_server_day] or self.data["name" .. i]
        
        self.node_list["select_bg_" .. i]:SetActive(false)
        if w_day == i then
            self.node_list["lbl_activity_day_hl_" .. i]:SetActive(true)
            if self.data["close_time_"..i] ~= "" and timer >= self.data.activity_time and timer < self.data["close_time_"..i] then
                self.node_list["select_bg_" .. i]:SetActive(true)
            end
        else
            self.node_list["lbl_activity_day_hl_" .. i]:SetActive(false)
        end

        self.activity_name.text.text = name_des
    end

    if self.index % 2 ~= 1 then
        --self.node_list.img9_activity_item_bg.image:LoadSprite(ResPath.GetDailyTypeIcon("a3_rc_di_ri1"))
        self.node_list.img9_activity_item_bg:SetActive(true)
    else
        --self.node_list.img9_activity_item_bg.image:LoadSprite(ResPath.GetDailyTypeIcon("a3_rc_zldt2"))
        self.node_list.img9_activity_item_bg:SetActive(false)
    end
end

function  BiZuoWeekCalendarRender:OnClickActIcon(index)
    local str_name = self.node_list["lbl_activity_name_" .. index].text.text
    local tips_data = BiZuoWGData.Instance:GetWeekCalenderShowTipData(str_name)
    local is_special = false
    if tips_data then
        if self.data.replace_name and self.data.replace_name ~= "" then
            is_special = true
        end
        BiZuoWGCtrl.Instance.activity_desc_view:SetData(tips_data, self.data.replace_name, index)
        BiZuoWGCtrl.Instance.activity_desc_view:Open()
    end
end
