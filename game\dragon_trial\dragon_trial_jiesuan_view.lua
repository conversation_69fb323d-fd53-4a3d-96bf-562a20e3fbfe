DragonTrialJieSuanView = DragonTrialJieSuanView or BaseClass(SafeBaseView)
local CLOSE_WINDOWS_TIME = 5

function DragonTrialJieSuanView:__init()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/common_jiesuan_prefab", "layout_jiesuan_common")
	self.view_layer = UiLayer.Pop
end

function DragonTrialJieSuanView:__delete()
end

function DragonTrialJieSuanView:ReleaseCallBack()
	if self.reward_item_six_list then
		self.reward_item_six_list:DeleteMe()
		self.reward_item_six_list = nil
	end

	if self.reward_item_max_list then
		self.reward_item_max_list:DeleteMe()
		self.reward_item_max_list = nil
	end

	self.show_data = nil
end

function DragonTrialJieSuanView:CloseCallBack()
	self:ClearCloseTime()
end

function DragonTrialJieSuanView:SetShowData(show_data)
	self.show_data = show_data
end

function DragonTrialJieSuanView:LoadCallBack()
	if not self.reward_item_six_list then
		self.reward_item_six_list = AsyncListView.New(ItemCell, self.node_list.reward_item_six_list)
	end

	if not self.reward_item_max_list then
		self.reward_item_max_list = AsyncListView.New(ItemCell, self.node_list.reward_item_max_list)
	end

	XUI.AddClickEventListener(self.node_list.layout_equip, BindTool.Bind(self.OpenJump, self, 1)) -- 装备
	XUI.AddClickEventListener(self.node_list.layout_chongwu, BindTool.Bind(self.OpenJump, self, 2)) -- 宠物
	XUI.AddClickEventListener(self.node_list.layout_skill, BindTool.Bind(self.OpenJump, self, 3)) -- 技能
	XUI.AddClickEventListener(self.node_list.layout_shop, BindTool.Bind(self.OpenJump, self, 4)) -- 商城
	XUI.AddClickEventListener(self.node_list.layout_first_recharge, BindTool.Bind(self.OpenJump, self, 5)) -- 首冲

	XUI.AddClickEventListener(self.node_list.btn_left, BindTool.Bind1(self.ClickLeftBtn, self)) -- 操作第一个按钮
	XUI.AddClickEventListener(self.node_list.btn_right, BindTool.Bind1(self.ClickRightBtn, self)) -- 操作第二个按钮
end

function DragonTrialJieSuanView:OnFlush()
	if not self.show_data then
		return
	end

	self:FlushChallengeStatus()
	self.downtime_str = Language.GuildBattleRanked.AutoQuitFb
	self.node_list.btn_right:CustomSetActive(self.show_data.is_win)
	if self.show_data.is_win then
		self:FlushRewardList()
		self:FlushFastChanllenge()
	else
		self.downtime_str = Language.GuildBattleRanked.BattleRankedEndTime
	end

	self:FlushCloseTime()
end

-- 设置挑战状态
function DragonTrialJieSuanView:FlushChallengeStatus()
	self.node_list.lbl_fb_tips:CustomSetActive(self.show_data.is_win)
	self.node_list.victory:CustomSetActive(self.show_data.is_win)
	self.node_list.lose:CustomSetActive(not self.show_data.is_win)
	self.node_list.winner:CustomSetActive(self.show_data.is_win)
	self.node_list.loser:CustomSetActive(not self.show_data.is_win)
end

-- 设置挑战成功的奖励
function DragonTrialJieSuanView:FlushRewardList()
	local real_reward_item = {}

	if self.show_data.is_once then -- 只有一关的奖励
		local one_data = DragonTrialWGData.Instance:GetLevelMonsterCfg(self.show_data.seq)
		if one_data then
			if one_data.fixed_level_reward ~= 0 then
				local fix_level_data = {}
				DragonTrialWGData.Instance:SetFixLevelReward(fix_level_data, one_data)
				table.insert(real_reward_item, fix_level_data)
			end
			for _, reward_data in pairs(one_data.pass_reward_item) do
				if reward_data and reward_data.item_id then
					table.insert(real_reward_item, reward_data)
				end
			end
		end
	else -- 多关的奖励
		real_reward_item = DragonTrialWGData.Instance:GetJumpLevelRewardList(self.show_data.seq, self.show_data.start_seq)
	end

	local is_max = #real_reward_item > 6
	self.node_list.reward_item_six_list:CustomSetActive(not is_max)
	self.node_list.reward_item_max_list:CustomSetActive(is_max)

	if is_max then
		if self.reward_item_max_list then
			self.reward_item_max_list:SetDataList(real_reward_item)
		end
	else
		if self.reward_item_six_list then
			self.reward_item_six_list:SetDataList(real_reward_item)
		end
	end
end

-- 刷新一键直达挑战状态
function DragonTrialJieSuanView:FlushFastChanllenge()
	-- 获取能一键直达的关卡
	local seq, level_limit, need_need_cap, is_max = DragonTrialWGData.Instance:GetCurCanQuickFinishLevel(self.show_data.seq)

	-- 没有下一关或者没达到下一关的条件不给操作了
	self.node_list.btn_right:CustomSetActive(true)
	if seq ~= self.show_data.seq then	-- 不能一键直达
		seq = self.show_data.seq + 1
		local cfg = DragonTrialWGData.Instance:GetLevelMonsterCfg(seq)
		need_need_cap = cfg and cfg.need_cap or 0
	end

	if seq == self.show_data.seq then -- 相等表示下一关不可继续， 倒计时退出
		self.node_list.btn_right_text.text.text = Language.FuBenPanel.ContinueFight
		self.node_list.btn_right:CustomSetActive(false)

		if level_limit ~= 0 then
			self.node_list.lbl_fb_tips.text.text = string.format(Language.FuBen.FuBenSpecialTips3, ToColorStr(level_limit, COLOR3B.D_RED))
		else
			self.node_list.lbl_fb_tips.text.text = string.format(Language.FuBen.FuBenSpecialTips2, ToColorStr(need_need_cap, COLOR3B.D_RED))
		end

		if is_max then
			self.node_list.lbl_fb_tips.text.text = string.format(Language.FuBen.FuBenSpecialTips6, ToColorStr(level_limit, COLOR3B.D_RED))
			self.downtime_str = Language.GuildBattleRanked.BattleRankedEndTime
		end
	elseif seq - self.show_data.seq == 1 then -- 相差1个推荐继续
		self.node_list.lbl_fb_tips.text.text = string.format(Language.FuBen.FuBenSpecialTips4, ToColorStr(need_need_cap, COLOR3B.GREEN))
		self.node_list.btn_right_text.text.text = Language.FuBenPanel.ContinueFight
		self.downtime_str = Language.GuildBattleRanked.BattleRankedNextTime
	elseif seq - self.show_data.seq > 1 then -- 相差很多 一键直达
		self.node_list.lbl_fb_tips.text.text = string.format(Language.FuBen.FuBenSpecialTips5, ToColorStr(seq, COLOR3B.GREEN))
		self.node_list.btn_right_text.text.text = Language.FuBenPanel.QuickFight
		self.downtime_str = Language.GuildBattleRanked.BattleQuickTime
	end
end

-- 退出关卡
function DragonTrialJieSuanView:FlushCloseTime()
	self:ClearCloseTime()

	CountDownManager.Instance:AddCountDown("dragon_trial_fb_close_timer", BindTool.Bind1(self.UpdateCloseCountDownTime, self),
					BindTool.Bind1(self.CompleteCloseCountDownTime, self), nil, CLOSE_WINDOWS_TIME, 1)
end

-- 清除倒计时
function DragonTrialJieSuanView:ClearCloseTime()
	if CountDownManager.Instance:HasCountDown("dragon_trial_fb_close_timer") then
		CountDownManager.Instance:RemoveCountDown("dragon_trial_fb_close_timer")
	end
end

function DragonTrialJieSuanView:UpdateCloseCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		self.node_list["lbl_end_time"].text.text = string.format(self.downtime_str, math.floor(total_time - elapse_time))
	end
end

-- 倒计时结束执行一些操作
function DragonTrialJieSuanView:CompleteCloseCountDownTime()
	self:Close()

	if not self.show_data then
		return
	end

	-- 获取能一键直达的关卡
	if self.show_data.is_win then
		local seq, _, _, _ = DragonTrialWGData.Instance:GetCurCanQuickFinishLevel(self.show_data.seq)
		if seq ~= self.show_data.seq then	-- 不能一键直达
			seq = self.show_data.seq + 1
		end
	
		if seq == self.show_data.seq then -- 相等表示下一关不可继续， 倒计时退出
			FuBenWGCtrl.Instance:SendLeaveFB()
		elseif seq - self.show_data.seq == 1 then -- 相差1个推荐继续
			DragonTrialWGCtrl.Instance:SendDragonTrialOperate(DRAGON_TRIAL_OPERATE_TYPE.OA_DRAGON_TRIAL_OPERATE_TYPE_DARE)
		elseif seq - self.show_data.seq > 1 then -- 相差很多 一键直达
			DragonTrialWGCtrl.Instance:SendDragonTrialOperate(DRAGON_TRIAL_OPERATE_TYPE.OA_DRAGON_TRIAL_OPERATE_TYPE_AUTO_DARE)
		end
	else
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
end

--------------------------------------------------------------------------------------------
-- 离开副本
function DragonTrialJieSuanView:OpenJump(click_type)
	FuBenWGCtrl.Instance:LoseViewCilckLeaveFB(click_type)
	self:Close()
end

-- 左边按钮
function DragonTrialJieSuanView:ClickLeftBtn()
	FuBenWGCtrl.Instance:SendLeaveFB()
	self:Close()
end

-- 右边按钮
function DragonTrialJieSuanView:ClickRightBtn()
	self:Close()

	if not self.show_data then
		return
	end

	if self.show_data.is_win then
		-- 获取能一键直达的关卡
		local seq, _, _, _ = DragonTrialWGData.Instance:GetCurCanQuickFinishLevel(self.show_data.seq)
		if seq ~= self.show_data.seq then	-- 不能一键直达
			seq = self.show_data.seq + 1
		end

		if seq - self.show_data.seq == 1 then -- 相差1个推荐继续
			DragonTrialWGCtrl.Instance:SendDragonTrialOperate(DRAGON_TRIAL_OPERATE_TYPE.OA_DRAGON_TRIAL_OPERATE_TYPE_DARE)
		elseif seq - self.show_data.seq > 1 then -- 相差很多 一键直达
			DragonTrialWGCtrl.Instance:SendDragonTrialOperate(DRAGON_TRIAL_OPERATE_TYPE.OA_DRAGON_TRIAL_OPERATE_TYPE_AUTO_DARE)
		end
	else -- 失败继续挑战
		DragonTrialWGCtrl.Instance:SendDragonTrialOperate(DRAGON_TRIAL_OPERATE_TYPE.OA_DRAGON_TRIAL_OPERATE_TYPE_DARE)
	end
end
