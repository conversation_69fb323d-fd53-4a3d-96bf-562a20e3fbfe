require("game/new_xunbao/new_xunbao_view")
require("game/new_xunbao/new_xunbao_wg_data")
require("game/new_xunbao/new_xunbao_taobao")
require("game/new_xunbao/new_xunbao_taobao_cell_render")
require("game/new_xunbao/new_xunbao_taobao_record")
require("game/new_xunbao/new_xunbao_taobao_bag")
require("game/new_xunbao/new_xunbao_taobao_tip")
require("game/new_xunbao/new_xunbao_wengua")
require("game/new_xunbao/new_xunbao_wengua_pre_view")

-- 寻宝
NewXunbaoWGCtrl = NewXunbaoWGCtrl or BaseClass(BaseWGCtrl)

function NewXunbaoWGCtrl:__init()
	if NewXunbaoWGCtrl.Instance ~= nil then
		ErrorLog("[NewXunbaoWGCtrl] attempt to create singleton twice!")
		return
	end
	NewXunbaoWGCtrl.Instance = self
	self.xunbao_data = NewXunbaoWGData.New()
	self.xunbao_view = NewXunbaoView.New(GuideModuleName.XunBao)

	self.record_view = XunBaoRecordView.New(GuideModuleName.XunBaoRecord)
	self.tb_bag_view = XunBaoBag.New(GuideModuleName.XunBaoBag)
	self.tb_tip_view = XunbaoTipView.New()

	self.wengua_pre_view = WenGuaPreView.New(GuideModuleName.WenGuaPre)
	self.wengua_record = WenGuaRecordView.New(GuideModuleName.WenGuaRecord)

 	-- self:RegisterAllProtocols()

	self.wengua_result_list = {}
end

function NewXunbaoWGCtrl:__delete()
	self.wengua_record:DeleteMe()
	self.wengua_pre_view:DeleteMe()

	if nil ~= self.xunbao_view then
		self.xunbao_view:DeleteMe()
		self.xunbao_view = nil
	end

	if nil ~= self.xunbao_data then
		self.xunbao_data:DeleteMe()
		self.xunbao_data = nil
	end

	if nil ~= self.record_view then
		self.record_view:DeleteMe()
		self.record_view = nil
	end

	if nil ~= self.tb_bag_view then
		self.tb_bag_view:DeleteMe()
		self.tb_bag_view = nil
	end

	if nil ~= self.tb_tip_view then
		self.tb_tip_view:DeleteMe()
		self.tb_tip_view = nil
	end

	self.shake_co = nil
	self.wengua_result_list = nil
	NewXunbaoWGCtrl.Instance = nil
end

function NewXunbaoWGCtrl:Open(tab_index, param_t)
	self.xunbao_view:Open(tab_index)
end

function NewXunbaoWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSTaobaoOpera)
	self:RegisterProtocol(SCTaobaoBagInfo, "OnSCTaobaoBagInfo")
	self:RegisterProtocol(SCTaobaoBagChangeInfo, "OnSCTaobaoBagChangeInfo")
	self:RegisterProtocol(SCTaobaoDrawLayerInfo, "OnSCTaobaoDrawLayerInfo")
	self:RegisterProtocol(SCTaobaoDrawResult, "OnSCTaobaoDrawResult")
	self:RegisterProtocol(SCTaobaoDrawServerRecord, "OnSCTaobaoDrawServerRecord")


	self:RegisterProtocol(CSWenGuaOpera)
	self:RegisterProtocol(SCWenGuaInfo, "OnSCWenGuaInfo")
	self:RegisterProtocol(SCWenGuaPersonRecord, "OnSCWenGuaPersonRecord")
	self:RegisterProtocol(SCWenGuaServerRecord, "OnSCWenGuaServerRecord")
end
---------------------------------------------------------------------------------------
function NewXunbaoWGCtrl:OnSCTaobaoBagInfo(protocol)
	--print_error("bag", protocol)
	self.xunbao_data:SetLongBagInfo(protocol)
	RemindManager.Instance:Fire(RemindName.XunBao_XunBao)
	ViewManager.Instance:FlushView(GuideModuleName.XunBaoBag)
	if self.xunbao_view:IsOpen() then
		self.xunbao_view:FlushLongRemind()
	end
end

function NewXunbaoWGCtrl:OnSCTaobaoBagChangeInfo(protocol)
	--print_error("bagchange", protocol)
	self.xunbao_data:SetBagChangeInfo(protocol)

	local info = protocol.change_info
	local item_cfg = ItemWGData.Instance:GetItemConfig(info.item_id)
	if item_cfg then
		local str = string.format(Language.Bag.GetItemTxt, ToColorStr(item_cfg.name, GET_TIP_ITEM_COLOR[item_cfg.color]), info.num)
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	end

    ViewManager.Instance:FlushView(GuideModuleName.XunBaoBag)
	RemindManager.Instance:Fire(RemindName.XunBao_XunBao)
	if self.xunbao_view:IsOpen() then
		self.xunbao_view:FlushLongRemind()
	end
end

function NewXunbaoWGCtrl:OnSCTaobaoDrawLayerInfo(protocol)
	--print_error("layer", protocol.last_draw_index, protocol.layer, protocol.draw_id_list)
	local is_open = self.xunbao_view:IsOpen()
	if protocol.reason == 0 then
		local old_has_reward_id = self.xunbao_data:CheckHasRewardId(protocol.layer)
		self.xunbao_data:SetTBDrawLayerInfo(protocol)
		local cur_has_reward_id = self.xunbao_data:CheckHasRewardId(protocol.layer)
		if old_has_reward_id and not cur_has_reward_id then
			if is_open then
				ViewManager.Instance:FlushView(GuideModuleName.XunBao, nil, "draw_all")
			end
		end
		ViewManager.Instance:FlushView(GuideModuleName.XunBaoRecord, nil, XunBaoRecordView.STATE.SELF)
		RemindManager.Instance:Fire(RemindName.XunBao_XunBao)
		if not is_open then
			if self.xunbao_data:GetisDrawOutByLayer(protocol.layer) then
				self:SendTaoBaoOp(TAOBAO_OP_TYPE.RESET, protocol.layer)
			end
		end
	else
		self.xunbao_data:SetShuffleInfo(protocol)
		if is_open then
			ViewManager.Instance:FlushView(GuideModuleName.XunBao, nil, "shuffle")
		end
	end
end

function NewXunbaoWGCtrl:OnSCTaobaoDrawResult(protocol)
	--print_error("result", protocol)
	self.xunbao_data:SetDrawResult(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.XunBao, nil, "draw")
	RemindManager.Instance:Fire(RemindName.XunBao_XunBao)
end

function NewXunbaoWGCtrl:OnSCTaobaoDrawServerRecord(protocol)
	--print_error("record", protocol)
	self.xunbao_data:SetTBLongServerRecord(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.XunBaoRecord, nil, XunBaoRecordView.STATE.ALL)
end

 -- 寻宝操作
 function NewXunbaoWGCtrl:SendTaoBaoOp(opera, param1, param2, param3)
	 --print_error(opera, param1, param2, param3)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSTaobaoOpera)
 	protocol.op_type = opera
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol.param3 = param3 or 1
 	protocol:EncodeAndSend()
 end

function NewXunbaoWGCtrl:SetRecordViewLayer(layer)
	self.record_view:SetLayer(layer)
end

function NewXunbaoWGCtrl:ShakeRoot()
	local perform_cfg = self.xunbao_data:GetTBLongPerformanceCfg()
	local shake_frame_num = perform_cfg.shake_frame_num or 4
	local shake_pixel = perform_cfg.shake_pixel or 30
	local count = 0
	GlobalTimerQuest:AddTimesTimer(function()
		count = count + 1
		local rand_num_x = 0
		local rand_num_y = 0
		local rand_num_z = 0
		if count ~= shake_frame_num then
			rand_num_x = GameMath.Rand(-shake_pixel,shake_pixel)
			rand_num_y = GameMath.Rand(-shake_pixel,shake_pixel)
			rand_num_z = GameMath.Rand(-shake_pixel,shake_pixel)
		end
		self.xunbao_view.root_node_transform.anchoredPosition = Vector3(rand_num_x, rand_num_y, rand_num_z)
	end, 0, shake_frame_num)
end

function NewXunbaoWGCtrl:OpenXunBaoTip()
	self.tb_tip_view:Open()
end
---------------------------------------------------------------------------------
function NewXunbaoWGCtrl:OnSCWenGuaInfo(protocol)
	--print_error("info", protocol)
    local old_pool_id = self.xunbao_data:GetGuaCurPoolId()
    local old_num = self.xunbao_data:GetGuaBagNum()
    local new_num = self.xunbao_data:GetNewIdListNum(protocol.draw_id_list)
    self.xunbao_data:SetWenGuaIdInfo(protocol, new_num)

    local has_new_num = new_num - old_num
	local flush_t = {
		can_do_anim = has_new_num > 0 and self.first_gua_info ~= nil,
		old_pool_id = old_pool_id,
	}

	if has_new_num < 0 then--一键分解需要立即刷新
		if self.wengua_result_delay then
			GlobalTimerQuest:CancelQuest(self.wengua_result_delay)
			self.wengua_result_delay = nil
		end
		self.wengua_result_list = {}
		self:DoWenGuaIdInfo(protocol.draw_id_list)
		ViewManager.Instance:FlushView(GuideModuleName.XunBao, TabIndex.fuwen_xunbao)
	else
		--播放动画，缓存背包数据
		ViewManager.Instance:FlushView(GuideModuleName.XunBao, TabIndex.fuwen_xunbao, "gua_result", flush_t)
		self.first_gua_info = true
		RemindManager.Instance:Fire(RemindName.XunBao_FuWen)

		table.insert(self.wengua_result_list, __TableCopy(protocol.draw_id_list))
	end
end

function NewXunbaoWGCtrl:DoWenGuaList()
	local info_list = table.remove(self.wengua_result_list, 1)
	if info_list == nil then
		return
	end

    self:DoWenGuaIdInfo(info_list)
end

function NewXunbaoWGCtrl:DoWenGuaIdInfo(list)
	self.xunbao_data:SetWenGuaDrawIdInfo(list)
end

function NewXunbaoWGCtrl:OnSCWenGuaPersonRecord(protocol)
	--print_error("info", protocol)
	self.xunbao_data:SetWenGuaPersonRecord(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WenGuaRecord, nil, WenGuaRecordView.STATE.SELF)
end

function NewXunbaoWGCtrl:OnSCWenGuaServerRecord(protocol)
	--print_error("info", protocol)
	self.xunbao_data:SetWenGuaServerRecord(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WenGuaRecord, nil, WenGuaRecordView.STATE.ALL)
end

 -- 问卦操作
 function NewXunbaoWGCtrl:SendWenGuaOp(opera, param1, param2, param3)
	--print_error(opera, param1, param2, param3)
	if opera == WENGUA_OPERA_TYPE.TAKE then
	 if self:CheckBaGuaBag() then
		 SysMsgWGCtrl.Instance:ErrorRemind(Language.WenGua.NoBag)
		 return
	 end
	end
 	local protocol = ProtocolPool.Instance:GetProtocol(CSWenGuaOpera)
 	protocol.op_type = opera or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol.param3 = param3 or 0
 	protocol:EncodeAndSend()
 end

function NewXunbaoWGCtrl:CheckBaGuaBag(num)
	-- local bag_info = FuWenWGData.Instance:GetOrderBaGuaBagInfo(0)
	-- num = num or 0
	return false
end
