
--聊天拍一拍
function NewChatWindow:PYPReleaseCallBack()
	if self.pyp_btn_list then
		self.pyp_btn_list:DeleteMe()
		self.pyp_btn_list = nil
	end

	if self.pyp_vip_limit_alert then
		self.pyp_vip_limit_alert:DeleteMe()
		self.pyp_vip_limit_alert = nil
	end

	self.is_pyp_loaded = nil
	self.cur_pyp_index = nil
	self.cur_pyp_need_vip = nil
	self.cur_custom_info = nil
end

function NewChatWindow:PYPCloseCallBack()
	
end

function NewChatWindow:PYPLoadCallBack()
	if self.is_pyp_loaded then
		return
	end

	local btn_cfg = ChatWGData.Instance:GetPaiYiPaiCfg()
	if nil == self.pyp_btn_list then
		self.pyp_btn_list = AsyncBaseGrid.New()
		local bundle = "uis/view/chat_ui_prefab"
		local asset = "pyp_btn_cell"
		self.pyp_btn_list:CreateCells({col = 4, cell_count = #btn_cfg, list_view = self.node_list["pyp_cells_list"],
			assetBundle = bundle, assetName = asset, itemRender = PYPBtnCellRender})
		-- self.pyp_btn_list = AsyncListView.New(PYPBtnCellRender, self.node_list["pyp_cells_list"])
		self.pyp_btn_list:SetSelectCallBack(BindTool.Bind1(self.PYPOnBtnSelectCB, self))
		self.pyp_btn_list:SetStartZeroIndex(false)
	end

	
	self.pyp_btn_list:SetDataList(btn_cfg, 0)
	self.cur_pyp_index = 1
	self.cur_pyp_need_vip = 0
	self.cur_custom_info = ""

	XUI.AddClickEventListener(self.node_list["btn_pyp_set"], BindTool.Bind1(self.PYPOnInfoSettingConfirm, self))
	XUI.AddClickEventListener(self.node_list["btn_mask_act_msg"], BindTool.Bind1(self.PYPOnMaskChange, self))
	self.node_list["PYP_InputField"].input_field.onValueChanged:AddListener(BindTool.Bind(self.PYPOnInputChange, self))
	self.node_list["pyp_info_title"].text.text = Language.Chat.PYPSettingInfoTitle_1

	self.is_pyp_loaded = true
end

function NewChatWindow:PYPShowIndexCallBack()
	self:PYPFlushInfo()
end

function NewChatWindow:PYPFlushInfo()
	local pyp_setting_info = ChatWGData.Instance:GetPYPRoleSettingInfo()
	local default_index = pyp_setting_info.action_index and pyp_setting_info.action_index + 1 or 1
	local is_pingbi = ChatWGData.Instance:GetPYPIsPingbi()
	local touch_name = ChatWGData.Instance:GetPaiYiPaiNameByIndex(self.cur_pyp_index)
	local custom_des = ""
	self.pyp_btn_list:JumpToIndex(default_index)
	self.cur_pyp_index = default_index - 1

	if pyp_setting_info.is_set_flag == 1 then
		custom_des = pyp_setting_info.msg_buff
	end
	self.node_list["PYP_InputField"].input_field.text = custom_des
	-- self.node_list["pyp_info_title"].text.text = string.format(Language.Chat.PYPSettingInfoTitle, touch_name)
	self.node_list["img_mask_act_msg_flag"]:SetActive(is_pingbi)
end

--拍一拍按钮类型改变
function NewChatWindow:PYPOnBtnSelectCB(cell)
	if not cell then return end
	local cur_data = cell:GetData()
	if not cur_data then return end

	self.cur_pyp_index = cur_data.index
	self.cur_pyp_need_vip = cur_data.vip_level

	-- self.node_list["pyp_info_title"].text.text = string.format(Language.Chat.PYPSettingInfoTitle, cur_data.touch_name)

	self:CheckShowPYPVipLimitTip()
end

--确认设置
function NewChatWindow:PYPOnInfoSettingConfirm()
	local vip_limi = self:CheckShowPYPVipLimitTip()
	-- print_error("FFF===== 点击 确认设置", vip_limi, self.cur_pyp_index, self.cur_custom_info)
	if not vip_limi and self.cur_pyp_index and self.cur_custom_info then
		--有非法字符直接不让发
		if ChatFilter.Instance:IsIllegal(self.cur_custom_info, false)
			or ChatWGData.Instance:CheckStringHadBlank(self.cur_custom_info) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.PYPIllegalContent)
			return
		end

		local is_set_flag = self.cur_custom_info ~= "" and 1 or 0
		-- print_error("FFF====== 点击 确认设置 data", self.cur_pyp_index, is_set_flag, self.cur_custom_info)
		ChatWGCtrl.Instance:SendSetCustomInfo(HUDONG_TYPE.PYP, self.cur_pyp_index, is_set_flag, self.cur_custom_info)
	end
end

--勾选屏蔽
function NewChatWindow:PYPOnMaskChange()
	local active_state = self.node_list["img_mask_act_msg_flag"].gameObject.activeSelf
	self.node_list["img_mask_act_msg_flag"]:SetActive(not active_state)
	ChatWGData.Instance:SetPYPIsPingbi(not active_state)
end

function NewChatWindow:PYPOnInputChange()
	local input_des = self.node_list["PYP_InputField"].input_field.text
	self.cur_custom_info = input_des
end

function NewChatWindow:CheckShowPYPVipLimitTip()
	local my_vip_level = VipWGData.Instance:GetVipLevel()
	if my_vip_level >= self.cur_pyp_need_vip then
		return false
	end

	local up_level = VipWGData.Instance:GetVIPZeroBuyCfg("arrive_level") or 0
	if up_level > 0 then
		if not self.pyp_vip_limit_alert then
			self.pyp_vip_limit_alert = Alert.New()
		end
		local ok_fun = function()
			--打开VIP界面
			local tab_index = VipWGData.Instance:GetVIPZeorBuyIsOpenJump()
			ViewManager.Instance:Open(GuideModuleName.Vip, tab_index)
		end
		self.pyp_vip_limit_alert:SetLableString(Language.Chat.PYPVipLevelLimit)
		self.pyp_vip_limit_alert:SetOkString(Language.Chat.PYPVipLevelLimitOk)
		self.pyp_vip_limit_alert:SetCancelString(Language.Chat.PYPVipLevelLimitCancel)
		self.pyp_vip_limit_alert:SetOkFunc(ok_fun)
		self.pyp_vip_limit_alert:Open()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Vip.VIP6Error)
	end
	
	return true
end

--==================================PYPBtnCellRender === start

PYPBtnCellRender = PYPBtnCellRender or BaseClass(BaseRender)

function PYPBtnCellRender:__init()

end

function PYPBtnCellRender:__delete()
	
end

function PYPBtnCellRender:OnFlush()
	if not self.data then return end

	self.node_list["pyp_icon"].image:LoadSprite(ResPath.GetCommonImages("a3_lt_bq_" .. self.data.index + 1))
	self.node_list["pyp_icon"].image:SetNativeSize()
	self.node_list["pyp_name"].text.text = self.data.touch_name
	local is_vip_limi = self.data.vip_level > 0
	if is_vip_limi then
		--self.node_list["vip_limi"].image:LoadSprite(ResPath.GetVipIcon("vip" .. self.data.vip_level))
		self.node_list["vip_limi"].text.text = "v" .. self.data.vip_level
		self.node_list["vip_limi"]:SetActive(true)
	else
		self.node_list["vip_limi"]:SetActive(false)
	end
end

function PYPBtnCellRender:OnSelectChange(is_select)
	-- print_error("FFFFF======= self.data.index", self.data.index)
	if self.node_list["bg_hl"] then
		self.node_list["bg_hl"]:SetActive(is_select)
	end
end

--==================================PYPBtnCellRender === end
