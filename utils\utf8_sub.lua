UTFSub = UTFSub or BaseClass()

--截取中英混合的UTF8字符串，endIndex可缺省
function UTFSub.SubStringUTF8(str, startIndex, endIndex)
    if startIndex < 0 then
        startIndex = UTFSub.SubStringGetTotalIndex(str) + startIndex + 1;
    end

    if endIndex ~= nil and endIndex < 0 then
        endIndex = UTFSub.SubStringGetTotalIndex(str) + endIndex + 1;
    end

    if endIndex == nil then
        return string.sub(str, UTFSub.SubStringGetTrueIndex(str, startIndex));
    else
        return string.sub(str, UTFSub.SubStringGetTrueIndex(str, startIndex), UTFSub.SubStringGetTrueIndex(str, endIndex + 1) - 1);
    end
end

--获取中英混合UTF8字符串的真实字符数量
function UTFSub.SubStringGetTotalIndex(str)
    local curIndex = 0;
    local i = 1;
    local lastCount = 1;
    repeat
        lastCount = UTFSub.SubStringGetByteCount(str, i)
        i = i + lastCount;
        curIndex = curIndex + 1;
    until(lastCount == 0);
    return curIndex - 1;
end

function UTFSub.SubStringGetTrueIndex(str, index)
    local curIndex = 0;
    local i = 1;
    local lastCount = 1;
    repeat
        lastCount = UTFSub.SubStringGetByteCount(str, i)
        i = i + lastCount;
        curIndex = curIndex + 1;
    until(curIndex >= index);
    return i - lastCount;
end

--返回当前字符实际占用的字符数
function UTFSub.SubStringGetByteCount(str, index)
    local curByte = string.byte(str, index)
    local byteCount = 1;
    if curByte == nil then
        byteCount = 0
    elseif curByte > 0 and curByte <= 127 then
        byteCount = 1
    elseif curByte>=192 and curByte<=223 then
        byteCount = 2
    elseif curByte>=224 and curByte<=239 then
        byteCount = 3
    elseif curByte>=240 and curByte<=247 then
        byteCount = 4
    end
    return byteCount;
end

local function utf8len(input, limitCharByteCount)
    local len = #input
    local left = len
    local cnt = 0
    local bl_out_limit = false
    local arr = {0,0xc0,0xe0,0xf0,0xf8,0xfc} --utf8可变字节特性
    while left ~= 0 do
        local tmp = string.byte(input,-left)
        local i = 6
        while arr[i] do
            if tmp >= arr[i] then
                left = left - 1
                break
            end
            i = i - 1
        end
        if i > (limitCharByteCount or 3) then --大部分需要的字符都集中在3字节内,包括中文,超过3字节就直接过滤
            bl_out_limit = true
        end
        cnt = cnt + 1
    end
    return cnt,bl_out_limit
end

function UTFSub.IsFilterChar(str, fliterLimitCharCount)
    local _, bl_out_limit = utf8len(str, fliterLimitCharCount)
    return bl_out_limit
end

function UTFSub.IsFilterSymbolChar(str)
    local i = 1

    while true do
        local lastCount = UTFSub.SubStringGetByteCount(str, i)
        if lastCount == 0 then
            break
        end

        local curByte = string.byte(str, i)
        if not curByte then
            return false
        end

        -- Check for ASCII symbols
        if (curByte <= 47) or
           (curByte >= 58 and curByte <= 64) or
           (curByte >= 91 and curByte <= 96) or
           (curByte >= 123 and curByte <= 127) then
            return true
        end

        -- Check for non-ASCII symbols
        if lastCount == 3 then
            local tmp1 = string.byte(str, i)
            local tmp2 = string.byte(str, i + 1)
            local tmp3 = string.byte(str, i + 2)

            if (tmp1 < 228) or
               (tmp1 == 228 and tmp2 < 184) or
               (tmp1 == 228 and tmp2 == 184 and tmp3 < 128) or
               (tmp1 == 233 and tmp2 > 191) or
               (tmp1 == 233 and tmp2 == 191 and tmp3 > 191) then
                return true
            end
        end

        i = i + lastCount
    end

    return false
end