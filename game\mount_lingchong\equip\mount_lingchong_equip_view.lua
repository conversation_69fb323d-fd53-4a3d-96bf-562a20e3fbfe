--骑宠装备界面，按viewname不同分为坐骑装备和灵宠装备
MountLingChongEquipView = MountLingChongEquipView or BaseClass(SafeBaseView)

local OFFSET = 94 -- 两个界面模型位置的偏移值

MountLingChongEquipViewIndex = {
    [MOUNT_PET_EQUIP_TYPE.MOUNT] ={
        Bag = TabIndex.mount_equip_bag,
        Strength = TabIndex.mount_equip_strength,
        ShengPin = TabIndex.mount_equip_shengpin,
        UpStar = TabIndex.mount_equip_star,
        Suit = TabIndex.mount_equip_suit,
        Resolve = TabIndex.mount_equip_resolve,
    },
    [MOUNT_PET_EQUIP_TYPE.PET] = {
        Bag = TabIndex.pet_equip_bag,
        Strength = TabIndex.pet_equip_strength,
        ShengPin = TabIndex.pet_equip_shengpin,
        UpStar = TabIndex.pet_equip_star,
        Suit = TabIndex.pet_equip_suit,
        Resolve = TabIndex.pet_equip_resolve,
    },
    [MOUNT_PET_EQUIP_TYPE.HUAKUN] ={
        Bag      = TabIndex.huakun_equip_bag,
        Strength = TabIndex.huakun_equip_strength,
        ShengPin = TabIndex.huakun_equip_shengpin,
        UpStar   = TabIndex.huakun_equip_star,
        Suit     = TabIndex.huakun_equip_suit,
        Resolve = TabIndex.huakun_equip_resolve,
    },
}

MountLingChongEquipViewRightNode =
{
    [10] = {
        right_container = "bag_right_container",
        right_item_container = "bag_item_container",
    },
    [20] = {
        right_container = "strength_right_container",
        right_item_container = "strength_item_container",
    },
    [30] = {
        right_container = "shengpin_right_contanier",
        right_item_container = "sp_item_container",
    },
    [40] = {
        right_container = "right_star_container",
        right_item_container = "star_item_container",
    },
}

function MountLingChongEquipView:__init()
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
	self:SetMaskBg()

    if self.view_name == GuideModuleName.LingChongEquipView then
        self.remind_tab = {
            {RemindName.LingChongEquip_Bag}, {RemindName.LingChongEquip_Strength}, {RemindName.LingChongEquip_Shengpin},
            {RemindName.LingChongEquip_Star}, {RemindName.LingChongEquip_Suit}, {RemindName.LingChongEquip_Resolve}
        }
        self.cur_show_type = MOUNT_PET_EQUIP_TYPE.PET
    elseif self.view_name == GuideModuleName.MountEquipView then
        self.remind_tab = {
            {RemindName.MountEquip_Bag}, {RemindName.MountEquip_Strength}, {RemindName.MountEquip_Shengpin},
            {RemindName.MountEquip_Star}, {RemindName.MountEquip_Suit}, {RemindName.MountEquip_Resolve}
        }
        self.cur_show_type = MOUNT_PET_EQUIP_TYPE.MOUNT
    elseif self.view_name == GuideModuleName.HuaKunEquipView then
        self.remind_tab = {
            {RemindName.HuaKunEquip_Bag}, {RemindName.HuaKunEquip_Strength}, {RemindName.HuaKunEquip_Shengpin},
            {RemindName.HuaKunEquip_Star}, {RemindName.HuaKunEquip_Suit}, {RemindName.HuaKunEquip_Resolve}
        }
        self.cur_show_type = MOUNT_PET_EQUIP_TYPE.HUAKUN
    end
	self.tab_sub = {}
    local path_str = "uis/view/qichong_equip_ui_prefab"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].Bag, path_str, "layout_qichong_equip_left")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].Strength, path_str, "layout_qichong_equip_left")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].ShengPin, path_str, "layout_qichong_equip_left")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].UpStar, path_str, "layout_qichong_equip_left")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].Bag, path_str, "layout_qichong_bag")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].Strength, path_str, "layout_qichong_equip_strength")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].ShengPin, path_str, "layout_qichong_equip_shengpin")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].UpStar, path_str, "layout_qichong_equip_star")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].Suit , path_str, "layout_qichong_equip_suit")
    self:AddViewResource(MountLingChongEquipViewIndex[self.cur_show_type].Resolve , path_str, "layout_qichong_resolve")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
    self.default_index = MountLingChongEquipViewIndex[self.cur_show_type].Bag
    self.open_tween = nil
    self.close_tween = nil
end

function MountLingChongEquipView:ReleaseCallBack()
    if MountLingChongEquipWGData.Instance then
        MountLingChongEquipWGData.Instance:SaveShengpinSelectList({})
    end
    
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
    end
    -- if self.display_model then
    --     self.display_model:DeleteMe()
    --     self.display_model = nil
    -- end
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end
    --self.show_res_id = nil
    self.has_play_model_anim = nil
    if not IsEmptyTable(self.ph_equip_list) then
        for k,v in pairs(self.ph_equip_list) do
            v:DeleteMe()
        end
    end
    if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
    end
    if self.role_data_change_callback then
        RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
        self.role_data_change_callback = nil
    end

    if self.model_display_left then
		self.model_display_left:DeleteMe()
		self.model_display_left = nil
	end

    self.ph_equip_list = nil
    self.cur_select_part = nil
    self:DeleteBagView()
    self:DeleteStrengthView()
    self:DeleteSuitView()
    self:DeleteStarView()
    self:DeleteShengPinView()
    self:DeleteResolveView()
end

function MountLingChongEquipView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("mount_pet_equip")
        self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
        self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.SetMaskBtnsActive, self))
        --self.tabbar:Init(Language.MountPetEquip.TabGrop, self.tab_sub, "uis/view/qichong_equip_ui_prefab", nil,self.remind_tab, EquipVerticalItemRender)
        self.tabbar:Init(Language.MountPetEquip.TabGrop, self.tab_sub, nil, nil,self.remind_tab, EquipVerticalItemRender)
    end
    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
    FunOpen.Instance:RegsiterTabFunUi(self.view_name, self.tabbar)
    if not self.item_data_event then
        self.item_data_event = BindTool.Bind1(self.BagItemDataChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
    end
    self.role_data_change_callback = BindTool.Bind1(self.MoneyChangeEvent, self)
    RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"coin"})
    self.node_list.title_view_name.text.text = Language.MountPetEquip.StrNameTable[self.cur_show_type]
end

function MountLingChongEquipView:SetMaskBtnsActive()
    if self:IsOpen() and self:IsLoaded() then
        local ver_list = self.tabbar and self.tabbar:GetVerCellList()
        --是否穿戴装备
        local has_equip = MountLingChongEquipWGData.Instance:GetEquipInfoFirstPart(self.cur_show_type) ~= nil
        local limit_click = MountLingChongEquipWGData.Instance:GetEnterNingPingByQuality(self.cur_show_type)
        if not IsEmptyTable(ver_list) then
            for k, v in pairs(ver_list) do
                if k == 1 then
                    v:SetMaskBtnActive(false)
                elseif k == 6 then
                    v:ChangeLingChongTabbarLimit(not limit_click)
                else
                    v:SetMaskBtnActive(not has_equip)
                end
            end
        end
    end
end

function MountLingChongEquipView:MoneyChangeEvent()
    self:FlushStarStuffItem()
    self:FlushStrengthStuffItem()
    self:FlushQualityStuffItem()
    self:FlushEquipCellRemind()
end

function MountLingChongEquipView:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    self:FlushStarStuffItem()
    self:FlushStrengthStuffItem()
    self:FlushQualityStuffItem()
    self:FlushEquipCellRemind()
end

function MountLingChongEquipView:OpenCallBack()
    MountLingchongEquipWGCtrl.Instance:SendBaseEquipInfo()
end

function MountLingChongEquipView:LoadIndexCallBack(index)
    if index == MountLingChongEquipViewIndex[self.cur_show_type].Bag then
        self:InitBagView()
        self:InitLeftPanel()
    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].Strength then
        self:InitStrengthView()
        self:InitLeftPanel()
    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].ShengPin then
        self:InitLeftPanel()
        self:InitShengPinView()
    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].UpStar then
        self:InitStarView()
        self:InitLeftPanel()
    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].Suit  then
        self:InitSuitView()
    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].Resolve  then
        self:InitResolveView()
    end
end

function MountLingChongEquipView:ShowIndexCallBack(index)
    --self.show_res_id = nil
    local bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.NOT_HAVE_ROLEMODEL)
    if self:IsAutoStrength() then
		self:StopStrengthOperator()
	end
    if index == MountLingChongEquipViewIndex[self.cur_show_type].Bag then
        for k, v in pairs(self.ph_equip_list) do
            v:SetSelectEffect(false)
        end
    elseif index ~= MountLingChongEquipViewIndex[self.cur_show_type].Suit and index ~= MountLingChongEquipViewIndex[self.cur_show_type].Resolve then
        for k, v in pairs(self.ph_equip_list) do
            v:SetSelectEffect(false)
        end
        local data_part = MountLingChongEquipWGData.Instance:GetEquipInfoFirstPart(self.cur_show_type)
        self.cur_select_part = self.cur_select_part or data_part
        if self.cur_select_part then
            self.ph_equip_list[self.cur_select_part]:SetSelectEffect(true)
        end
    end
    if index ~= MountLingChongEquipViewIndex[self.cur_show_type].Suit then
        --self:PlayCommonTween()
    end

    if index == MountLingChongEquipViewIndex[self.cur_show_type].Bag then

    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].Strength then
        self:ShowStrengthCallBack()
    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].ShengPin then
        self:ShowShengPinCallBack()
    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].UpStar then
        self:ShowStarCallBack()
    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].Suit then
        self:ShowSuitCallBack()
    elseif index == MountLingChongEquipViewIndex[self.cur_show_type].Resolve then
        self:ShowResolveCallBack()
    end

    self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

    MountLingChongEquipWGData.Instance:SaveShengpinSelectList({})
    self:JumpToRemindCell()
end

function MountLingChongEquipView:IsCanJumpTo()
    if self.show_index ~= MountLingChongEquipViewIndex[self.cur_show_type].Suit then
        local show_remind_list = MountLingChongEquipWGData.Instance:GetEquipCellShowRemind(self.show_index, self.cur_show_type)
        local select_idx 
        for i = 0, 5 do
            if show_remind_list[i] then
                select_idx = i
                break
            end
        end
        if select_idx then
            return true
        end
    end
    return false
end

function MountLingChongEquipView:JumpToRemindCell()
    if self.show_index ~= MountLingChongEquipViewIndex[self.cur_show_type].Suit then
        local show_remind_list = MountLingChongEquipWGData.Instance:GetEquipCellShowRemind(self.show_index, self.cur_show_type)
        local select_idx 
        for i = 0, 5 do
            if show_remind_list[i] then
                select_idx = i
                break
            end
        end
        if select_idx then
            local cell = self.ph_equip_list[select_idx]
            self:OnClickEquipItem(cell)
        end
    end
end

function MountLingChongEquipView:PlayCommonTween()
    local tween_info = UITween_CONSTS.MountPetEquipSys.Common

    local right_node = MountLingChongEquipViewRightNode[self.show_index]
    local right_container = right_node.right_container
    local right_item_container = right_node.right_item_container
	UITween.CleanAllTween(self.view_name)
    UITween.FakeHideShow(self.node_list[right_item_container])

    RectTransform.SetAnchoredPositionXY(self.node_list.left_cells.rect, -585, -6)
    RectTransform.SetAnchoredPositionXY(self.node_list.right_cells.rect, 585, -6)
    RectTransform.SetAnchoredPositionXY(self.node_list[right_container].rect, 395, -15)
    RectTransform.SetAnchoredPositionXY(self.node_list.common_capa_container.rect, 20, -415)

    self.node_list.common_capa_container.rect:DOAnchorPos(Vector2(20, -283), tween_info.MoveTime)
    self.node_list.right_cells.rect:DOAnchorPos(Vector2(288, -6), tween_info.MoveTime)
    self.node_list.left_cells.rect:DOAnchorPos(Vector2(-282, -6), tween_info.MoveTime)

	self.node_list[right_container].rect:DOAnchorPos(Vector2(-155, -15), tween_info.MoveTime)
    ReDelayCall(self, function()
		UITween.AlphaShow(self.view_name, self.node_list[right_item_container], 0, tween_info.ToAlpha, tween_info.AlphaTime)
	end, tween_info.AlphaDelay, "mount_pet_equip_tween")
end

function MountLingChongEquipView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
            if index == MountLingChongEquipViewIndex[self.cur_show_type].Bag then
                self:FlushBagView()
                self:FlushLeftPanel()
            elseif index == MountLingChongEquipViewIndex[self.cur_show_type].Strength then
                self:FlushLeftPanel()
                self:FlushStrengthView()
            elseif index == MountLingChongEquipViewIndex[self.cur_show_type].ShengPin then
                self:FlushShengPinView()
                self:FlushLeftPanel()
            elseif index == MountLingChongEquipViewIndex[self.cur_show_type].UpStar then
                self:FlushStarView()
                self:FlushLeftPanel()
            elseif index == MountLingChongEquipViewIndex[self.cur_show_type].Suit  then
                self:FlushSuitView()
            elseif index == MountLingChongEquipViewIndex[self.cur_show_type].Resolve  then
                self:FlushResolveView()
            end
        elseif "flush_select" == k then
            self:FlushQualityStuffItem()
		end

	end
end

function MountLingChongEquipView:InitLeftPanel()
    -- for i = 0, 5 do
    --     local bundle, asset = ResPath.GetMountPetEquipPath(self.cur_show_type.."_"..i)
    --     self.node_list["equip_icon"..i].image:LoadSprite(bundle, asset)
    --     self.node_list["equip_icon"..i]:SetActive(true)
    -- end


    -- -- 初始化模型
    -- if not self.display_model then
    --     self.display_model = RoleModel.New()
    --     local display_data = {
    --         parent_node = self.node_list["model_display"],
    --         camera_type = MODEL_CAMERA_TYPE.BASE,
    --         -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
    --         rt_scale_type = ModelRTSCaleType.S,
    --         can_drag = true,
    --     }
        
    --     self.display_model:SetRenderTexUI3DModel(display_data)
    --     -- self.display_model:SetUI3DModel(self.node_list.model_display.transform, self.node_list.model_display.event_trigger_listener,
    --     --                     1, false, MODEL_CAMERA_TYPE.BASE)
    -- end

    self.equip_item_list = {}
    --初始化装备格子
    for i = 0, 5 do
        self.node_list["img_add_btn"..i].button:AddClickListener(BindTool.Bind(self.OnClickAddItem, self, i))--弹出tips
    end
    if not self.ph_equip_list then
        self.ph_equip_list = {}
        for i = 0, 5 do
            self.ph_equip_list[i] = MountPetEquipItem.New(self.node_list["ph_equip_cell"..i], self)
            self.ph_equip_list[i]:SetIndex(i)
            self.ph_equip_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickEquipItem, self))
        end
    end

    if not self.model_display_left then
		self.model_display_left = OperationActRender.New(self.node_list.model_display_left)
		self.model_display_left:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    self.node_list["rule_btn"].button:AddClickListener(BindTool.Bind(self.OnClickPlayDes, self))--弹出tips

    local model_cfg = MountLingChongEquipWGData.Instance:GetModelCfgByType(self.cur_show_type)
    self:FlushModel(self.model_display_left, self.node_list.model_display_left, model_cfg)
end

function MountLingChongEquipView:OnClickPlayDes()
    local role_tip = RuleTip.Instance
    local str_name = Language.MountPetEquip.NameStrTable[self.cur_show_type]
    local equip_part_name = Language.MountPetEquip.EquipPartNameStrTable[self.cur_show_type]
    local PlayDes = string.gsub(Language.MountPetEquip.PlayDes, "name", str_name)
    PlayDes = string.format(PlayDes, equip_part_name)
	if role_tip then
		role_tip:SetContent(PlayDes, Language.MountPetEquip.RuleTitle)
	end
end

function MountLingChongEquipView:OnClickEquipItem(item)
    if self.show_index == MountLingChongEquipViewIndex[self.cur_show_type].ShengPin or self.show_index == MountLingChongEquipViewIndex[self.cur_show_type].Strength or
    self.show_index == MountLingChongEquipViewIndex[self.cur_show_type].UpStar then
        for k, v in pairs(self.ph_equip_list) do
            v:SetSelectEffect(false)
        end
        item:SetSelectEffect(true)
        self.cur_select_part = item:GetIndex()
        if self.show_index == MountLingChongEquipViewIndex[self.cur_show_type].Strength then
            self:OnSelectEquipStrengthItemHandler()
        elseif self.show_index == MountLingChongEquipViewIndex[self.cur_show_type].ShengPin then
            self:OnSelectEquipQualityItemHandler()
        elseif self.show_index == MountLingChongEquipViewIndex[self.cur_show_type].UpStar then
            self:OnSelectEquipStarItemHandler()
        else
            self:Flush()
        end
    else
        for k, v in pairs(self.ph_equip_list) do
            v:SetSelectEffect(false)
        end
    end
end

function MountLingChongEquipView:GetCurSelectPart()
    return self.cur_select_part
end

-- function MountLingChongEquipView:FlushDisplayModel()
--     local temp_res_id, name, grade_num = MountLingChongEquipWGData.Instance:GetCurShowModelId(self.cur_show_type)
--     if self.show_res_id == temp_res_id then
--         return
--     end
--     self.show_res_id = temp_res_id
--     -- self.node_list.model_name.text.text = name
--     -- self.node_list.grade_num.text.text = NumberToChinaNumber(grade_num) .. Language.Common.Jie
--     local path = nil
--     if self.cur_show_type == MOUNT_PET_EQUIP_TYPE.PET then
-- 		path = ResPath.GetPetModel
-- 	elseif self.cur_show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
-- 		path = ResPath.GetMountModel
--     elseif self.cur_show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
--         path = ResPath.GetMountModel
--     end

--     -- local bundle, asset = path(temp_res_id)
-- 	-- if self.display_model and bundle ~= nil then
-- 	-- 	self.display_model:SetMainAsset(bundle,asset)
-- 	-- end

--     -- if not self.has_play_model_anim then
--     --     self.has_play_model_anim = true
--     --     if self.cur_show_type == MOUNT_PET_EQUIP_TYPE.PET then
--     --         self.display_model:PlaySoulAction()
--     --     elseif self.cur_show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
--     --         self.display_model:PlayMountAction(false)
--     --     end
--     -- end
-- end

function MountLingChongEquipView:FlushLeftPanel()
    --self:FlushDisplayModel()
    --self:FlushTuTeng()

    for k, v in pairs(self.ph_equip_list) do
        local data = MountLingChongEquipWGData.Instance:GetEquipInfoByPart(self.cur_show_type, k) or {}
        v:SetData(data)
    end
    self:FlushEquipCapability()
    self:FlushEquipCellRemind()
end

function MountLingChongEquipView:FlushTuTeng()
    -- local temp_res_id, name, grade_num = MountLingChongEquipWGData.Instance:GetCurShowModelId(self.cur_show_type)
    -- if self.show_res_id == temp_res_id then
    --     return
    -- end
    -- self.show_res_id = temp_res_id
    self.node_list.tuteng_zuoqi:SetActive(self.cur_show_type == MOUNT_PET_EQUIP_TYPE.PET)
    self.node_list.tuteng_chongwu:SetActive(self.cur_show_type == MOUNT_PET_EQUIP_TYPE.MOUNT)
    self.node_list.tuteng_huakun:SetActive(self.cur_show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN)
end

function MountLingChongEquipView:FlushModel(model_display, node, model_cfg, is_move)
	if IsEmptyTable(model_cfg) or not model_display or not node then
		return
	end

	-- 形象展示
	local display_data = {}
	display_data.should_ani = true
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = model_cfg.model_bundle_name
	display_data.asset_name = model_cfg.model_asset_name
	display_data.render_type = model_cfg.model_show_type - 1
	display_data.need_wp_tween = false
	display_data.hide_model_block = true
	-- display_data.model_click_func = function ()
    --     TipWGCtrl.Instance:OpenItem({item_id = model_cfg.model_show_itemid})
    -- end
    if model_cfg.model_pos and model_cfg.model_pos ~= "" then
		local pos_list = string.split(model_cfg.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if model_cfg.model_rot and model_cfg.model_rot ~= "" then
		local rot_list = string.split(model_cfg.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
		--display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_cfg.model_scale and model_cfg.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_cfg.model_scale
	end

    display_data.model_rt_type = ModelRTSCaleType.L
    model_display:SetData(display_data)

    local pos_x, pos_y, pos_z = 0, 0, 0
	if model_cfg.whole_display_pos and model_cfg.whole_display_pos ~= "" then
		local pos_list = string.split(model_cfg.whole_display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
        pos_z = tonumber(pos_list[3]) or pos_z
	end
    if is_move then
        pos_x = pos_x + OFFSET
    end
	RectTransform.SetAnchoredPosition3DXYZ(node.rect, pos_x, pos_y, pos_z)

	local rot_x, rot_y, rot_z = 0, 0, 0
	if model_cfg.whole_display_rot and model_cfg.whole_display_rot ~= "" then
		local rot_list = string.split(model_cfg.whole_display_rot, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z

		node.transform.localRotation = Quaternion.Euler(rot_x, rot_y, rot_z)
	end

	local scale = model_cfg["whole_display_scale"]
	if scale and scale ~= "" then
		Transform.SetLocalScaleXYZ(node.transform, scale, scale, scale)
	end
end

function MountLingChongEquipView:FlushEquipCapability()
    local capability = MountLingChongEquipWGData.Instance:GetEquipCapability(self.cur_show_type)
    self.node_list.equip_cap_value.text.text = capability
end

function MountLingChongEquipView:OnClickAddItem(idx)
    local item_id = MountLingChongEquipWGData.Instance:GetEquipTipsItemList(self.cur_show_type, idx)
    if item_id then
        local data = {}
        data.item_id = item_id
        local btn_callback_event = nil
        TipWGCtrl.Instance:OpenItem(data, nil, nil, nil, btn_callback_event)
    end
end

function MountLingChongEquipView:GetCurSelectData()
    return MountLingChongEquipWGData.Instance:GetEquipInfoByPart(self.cur_show_type, self.cur_select_part)
end

--刷新装备格子红点
function MountLingChongEquipView:FlushEquipCellRemind()
    local show_remind_list = MountLingChongEquipWGData.Instance:GetEquipCellShowRemind(self.show_index, self.cur_show_type)
    for i = 0, 5 do
        if self.node_list["remind_"..i] then
            self.node_list["remind_"..i]:SetActive(show_remind_list[i])
        end
    end
end


------------------------穿戴的装备----------------------------------------------------------------------
MountPetEquipItem = MountPetEquipItem or BaseClass(ItemCell)

function MountPetEquipItem:__init(instance, parent_view)
    self.parent_view = parent_view
    ItemCell.__init(self, instance)
end

function MountPetEquipItem:LoadCallBack()
    self.is_first = true
end

function MountPetEquipItem:__delete()
    self.parent_view = nil
end


function MountPetEquipItem:OnFlush()
    if IsEmptyTable(self.data) or self.data.item_id == nil or self.data.item_id == 0 then
        self:SetVisible(false)
    else
        self:SetVisible(true)
    end
    ItemCell.OnFlush(self)

    if self.is_first then
        --local bundle, asset = ResPath.GetF2CommonImages("item_select_2")
        local bundle, asset = ResPath.GetCommonImages("a3_ty_xz1")
        --local size = {width = 90, height = 90,}
        self:SetSelectEffectImageRes(bundle, asset)
        self:SetSelectEffect(self.parent_view.cur_select_part == self.index)
        self.is_first = false
    end

    if self.data.star_level then
        self:SetLeftTopImg(self.data.star_level)
    end
    if self.data.strengthen_level and self.data.strengthen_level > 0 then
        self:SetRightBottomTextVisible(true)
        self:SetRightBottomColorText("+" .. self.data.strengthen_level)
    else
        self:SetRightBottomColorText("")
    end
end

-- 点击格子
function MountPetEquipItem:OnClick()
	if self.tip_callback ~= nil then
		local is_black = self.tip_callback()
		if is_black == true then
			return
		end
	end

    if self.is_showtip then
        if self.parent_view and (self.parent_view:GetCurSelectPart() == self:GetIndex() or self.parent_view.show_index == 
        MountLingChongEquipViewIndex[self.parent_view.cur_show_type].Bag) then
            if self.data then
                local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
                if nil == item_cfg then return end
                TipWGData.Instance:SetDefShowBuyCount(self.show_buy_num or 1)
                TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_MOUNTEQUIP_EQUIP, nil)
            end
        end
    end
	BaseRender.OnClick(self)
end


-----------EquipVerticalItemRender-------------------------------------------
EquipVerticalItemRender = EquipVerticalItemRender or BaseClass(VerItemRender)

function EquipVerticalItemRender:SetMaskBtnActive(active)
    --self.node_list.mask_btn:SetActive(active)
    self:SetOtherBtn(active, BindTool.Bind(self.OnClickMaskBtn,self))
end

function EquipVerticalItemRender:LoadCallBack()
    --self.node_list.mask_btn.button:AddClickListener(BindTool.Bind(self.OnClickMaskBtn, self))--弹出tips
end

function EquipVerticalItemRender:OnClickMaskBtn()
    SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.EquipLimit)
end

function EquipVerticalItemRender:ChangeLingChongTabbarLimit(active)
	self:SetOtherBtn(active, BindTool.Bind(self.OnClickLimitSysMsg,self))
end

function EquipVerticalItemRender:OnClickLimitSysMsg()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.NingPingTabbarLimit)
end