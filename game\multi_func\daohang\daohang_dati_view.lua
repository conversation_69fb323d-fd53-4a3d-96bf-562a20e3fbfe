DaoHangDaTiView = DaoHangDaTiView or BaseClass(SafeBaseView)
function DaoHangDaTiView:__init()
	self:SetMaskBg()
	local daohang_bundle_name = "uis/view/multi_function_ui/daohang_prefab"
	self:AddViewResource(0, daohang_bundle_name, "daohang_dati")
	self.delay_call_flag = false
end

function DaoHangDaTiView:LoadCallBack()
	self.dati_index = MultiFunctionWGData.Instance:GetQuestionId() + 1
	self.flush_flag = true
	self:InitDati()
end

function DaoHangDaTiView:OpenCallBack()
	if self.delay_call_flag == true then
		self:InitDati()
	end
end

function DaoHangDaTiView:InitDati()
	self.data = MultiFunctionWGData.Instance:GetDaoHangDaTiCfg(self.dati_index)
	self.dati_num = MultiFunctionWGData.Instance:GetDaoHangDaTiNum()
	self.node_list["topic_desc"].text.text = self.data.qu_desc
	self.res_list = {}
	for i = 0, 3 do
		self.res_list[i] = DaoHangResult.New(self.node_list["answer_item" .. i])
		local res_data = {}
		res_data.res_desc = self.data["res_" .. i]
		res_data.res_id = i
		res_data.qu_id = self.data.qu_id
		self.res_list[i]:SetData(res_data)
	end
	self.node_list["topic_progress"].text.text = string.format(Language.Charm.DaoHangDaTiDesc, self.dati_index, self.dati_num)
	self.node_list["topic_title"].text.text = string.format(Language.Charm.DaoHangDaTiTitle, Language.Common.UpNum[self.dati_index])
	self.delay_call_flag = false
end

function DaoHangDaTiView:ReleaseCallBack()
	if self.res_list then
		for k, v in pairs(self.res_list) do
			v:DeleteMe()
		end
		self.res_list = nil
	end
	self.dati_info = nil
	self.data = nil
	self.data_num = nil
	self.dati_index = nil
end

function DaoHangDaTiView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "FlushView" then
			self:FlushView()
		end
	end
end

function DaoHangDaTiView:FlushView()
	self.dati_info = MultiFunctionWGData.Instance:GetDaoHangDaTiInfo()
	if self.dati_info.result and self.dati_info.result == 1 then -- 答错
		self.res_list[self.dati_info.choose]:SetResFalse()
	elseif self.dati_info.result and self.dati_info.result == 0 then -- 答对
		self.res_list[self.dati_info.choose]:SetResTrue()
		if self.dati_index ~= self.dati_num then
			self.dati_index = self.dati_index + 1
			self.delay_call_flag = true
			AddDelayCall(self, function()
				self:InitDati()
			end, 3)
		else
			MultiFunctionWGData.Instance:SetQuestionId(self.dati_index)
			self.flush_flag = false
			MultiFunctionWGCtrl.Instance:FlushHolySealView()
			AddDelayCall(self, function()
				self:Close()
			end, 3)
		end

	end
end

function DaoHangDaTiView:CloseCallBack()
	if self.flush_flag then
		MultiFunctionWGData.Instance:SetQuestionId(self.dati_index - 1)
	end
end


DaoHangResult = DaoHangResult or BaseClass(BaseRender)
function DaoHangResult:__init()
	XUI.AddClickEventListener(self.node_list["res_btn"], BindTool.Bind(self.OnClickDaTi, self))
end

function DaoHangResult:__delete()
end

function DaoHangResult:OnFlush()
	if self.data == nil then
		return
	end
	self.node_list["bg_green"]:SetActive(false)
	self.node_list["bg_hong"]:SetActive(false)
	self.node_list["answer_desc"].text.text = self.data.res_desc
end

function DaoHangResult:OnClickDaTi()
	MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.TAOIST_OPERATE_QUE, self.data.qu_id, self.data.res_id)
end

function DaoHangResult:SetResTrue()
	self.node_list["bg_green"]:SetActive(true)
end

function DaoHangResult:SetResFalse()
	self.node_list["bg_hong"]:SetActive(true)
end
