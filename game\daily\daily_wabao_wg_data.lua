DailyWaBaoWGData = DailyWaBaoWGData or BaseClass()

WABAO_MAP_WIDTH = 650
WABAO_MAP_HEIGHT = 391

WABAO_OPERA_TYPE = 
{
	START = 0, 			-- 开始
	DIG = 1, 			-- 挖
	QUICK_COMPLETE = 2, -- 快速完成 
	BUY_JOIN_TIMES = 3, -- 购买次数
	INFO = 4, 			-- 查询
	MAX = 5,
}

WABAO_STATUS = 
{
	DEFAULT = 1, 	--未开始
	DIG = 2, 		--挖宝中
}

function DailyWaBaoWGData:__init()
	if DailyWaBaoWGData.Instance then
		ErrorLog("[DailyWaBaoWGData]:Attempt to create singleton twice!")
	end
	DailyWaBaoWGData.Instance = self

	self.wabaovo = DailyWaBaoWGData.CreateWaBaoVo()
	self:InitDailyWaBaoData()
end

function DailyWaBaoWGData:__delete()
	DailyWaBaoWGData.Instance = nil
end

function DailyWaBaoWGData.CreateWaBaoVo()
	return 
	{
		complete_times = 0,
		buy_join_times = 0,
		reserve_sh = 0,
		baozang_scene_id = 0,
		baozang_pos_x = 0,
		baozang_pos_y = 0,

		status = WABAO_STATUS.DEFAULT,
		map_list = {}, 			--小地图块列表

		max_times = 0,
		free_times = 0,
		buy_times_cost = 0,
		quick_complete_cost = 0,

		min_cull_x = 0, 		--最小地图块
		min_cull_y = 0,
		max_cull_x = 0, 		--最大地图块
		max_cull_y = 0,

		big_map_w = 0, 			--大地图宽度
		big_map_h = 0,
		small_map_w = 0, 		--小地图宽度
		small_map_h = 0,

		word_x = 0, 			--世界坐标
		word_y = 0,
		box_x = 0, 				--宝箱在小地图位置
		box_y = 0,
	}
end

function DailyWaBaoWGData:GetWaBaoVo()
	return self.wabaovo
end

function DailyWaBaoWGData:InitDailyWaBaoData()
	local other_cfg =ConfigManager.Instance:GetAutoConfig("wabaoconfig_auto").other[1]
	self.wabaovo.free_times = other_cfg.free_times
	self.wabaovo.buy_times_cost = other_cfg.buy_times_cost
	self.wabaovo.quick_complete_cost = other_cfg.quick_complete_cost
end

function DailyWaBaoWGData:UpdateDailyWaBaoData()


end

function DailyWaBaoWGData:IsDigStatus()
	return self.wabaovo.status == WABAO_STATUS.DIG
end