-- 仙盟捐献
GuildJuanXianView = GuildJuanXianView or BaseClass(SafeBaseView)

function GuildJuanXianView:__init()
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_juanxian_view")
end

function GuildJuanXianView:LoadCallBack()
	self.node_list.view_close.button:AddClickListener(BindTool.Bind(self.OnCloseView, self)) --关闭窗口

	-- if not self.money_bar then
	-- 	self.money_bar = MoneyBar.New()
	-- 	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	-- 	local show_params = {
    --     	show_gold = true, show_bind_gold = true,
    --     	show_coin = true, show_silver_ticket = true,
    --     }
    --     self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	-- 	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	-- end
end

function GuildJuanXianView:ReleaseCallBack()
	if self.juanxain_item_list then
		self.juanxain_item_list:DeleteMe()
		self.juanxain_item_list = nil
	end
	
	-- if self.money_bar then
	-- 	self.money_bar:DeleteMe()
	-- 	self.money_bar = nil
	-- end
end

function GuildJuanXianView:ShowIndexCallBack()
	local contribute_cfg = GuildWGData.Instance:GetGuildContributeCfg()
	if nil == self.juanxain_item_list then
		self.juanxain_item_list = AsyncListView.New(JXListItemRender, self.node_list["ph_juanxian_list"])
	end
	self.node_list["btn_guild_juanxian_states"].button:AddClickListener(BindTool.Bind1(self.OnClickState, self))
	self.juanxain_item_list:SetDataList(contribute_cfg,0)
end

function GuildJuanXianView:OnFlushView()
	if self.juanxain_item_list then
		self.juanxain_item_list:RefreshActiveCellViews()
	end
end

function GuildJuanXianView:OnClickState()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.GuildJX.GuildJxTips)
		role_tip:SetContent(Language.GuildJX.GuildJxStateTips)
	end
end

function GuildJuanXianView:OnCloseView()
	ViewManager.Instance:Close(GuideModuleName.GuildJuanXian)
end

----------------JXListItemRender(捐献)-------------------
JXListItemRender = JXListItemRender or BaseClass(BaseRender)

function JXListItemRender:__delete()
	self.can_set = nil
	self.num = nil
end

function JXListItemRender:OnFlush()
	if nil == self.data then
		return
	end
	--物品捐献类型需要单独处理
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local gold = main_role_vo.gold or 0
	local bind_gold = main_role_vo.bind_gold or 0
	local cur_num = GuildWGData.Instance:GetCurJxNum(self.data.contribute_type)
	local color_num = COLOR3B.WHITE
	self.num = self.data.contribute_num
	self.node_list["tuijian_biaoqian"]:SetActive(false)
	if self.data.contribute_type == 1 then
		-- local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
		local coin = main_role_vo.coin or 0
		color_num = (self.data.contribute_num <= coin or cur_num <= 0) and "#1D4261" or COLOR3B.RED

		--RectTransform.SetAnchoredPositionXY(self.node_list.img_bg.rect, -5, 30)
	elseif self.data.contribute_type == 2 then
		self.node_list["tuijian_biaoqian"]:SetActive(true)  --绑玉显示可推荐
		color_num = (self.data.contribute_num <= bind_gold or cur_num <= 0) and "#1D4261" or COLOR3B.RED

		-- RectTransform.SetAnchoredPositionXY(self.node_list.img_bg.rect, -8, 44)
	elseif self.data.contribute_type == 3 then
		color_num = (self.data.contribute_num <= gold or cur_num <= 0) and "#1D4261" or COLOR3B.RED

		-- RectTransform.SetAnchoredPositionXY(self.node_list.img_bg.rect, -5, 83)
	end
	self.node_list.juanxian_num.text.text = ToColorStr(self.data.contribute_num, color_num)
	-- end
	
	self.node_list["desc_name"].text.text = self.data.title_name  --祈福类型名字
	self.node_list["btn_jx"]:SetActive(cur_num > 0)
	self.node_list["icon_yjx"]:SetActive(cur_num <= 0)
	if 1 == self.data.contribute_type then
		local is_show_red = GuildWGData.Instance:IsCanJuanXian() == 1
		self.node_list["juanxian_red"]:SetActive(is_show_red)
	end
	
	if nil == self.can_set then
		self:SetViewInfo()
	end
end

--这些数据只需要做一次设置，放loacallback会出现加载的时候数据还没过来，设置不上
function JXListItemRender:SetViewInfo()
	self.node_list.add_gongxian_tex.text.text = string.format(Language.GuildJX.NatureName1,CommonDataManager.NotConverExpExtend(self.data.add_longhun))
	self.node_list.add_guild_exp.text.text = string.format(Language.GuildJX.NatureName1,CommonDataManager.NotConverExpExtend(self.data.add_guild_exp))
	-- self.node_list.add_guildwage_tex.text.text = string.format(Language.GuildJX.NatureName2,CommonDataManager.NotConverExpExtend(self.data.reward_shengwang))
	self.node_list.btn_jx.button:AddClickListener(BindTool.Bind(self.OnClickJuanxianHandler, self))
	self.node_list.btn_icon.button:AddClickListener(BindTool.Bind(self.OnClickItemTips, self))
	local asset_name,bundle_name = ResPath.GetRawImagesPNG("a3_xm_dj".. self.data.contribute_type)
	self.node_list.img_bg.raw_image:LoadSprite(asset_name, bundle_name)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)

	local asset_name2,bundle_name2 = ResPath.GetXiaoHaoIcon(self.data.item_id)
	self.node_list.img_btnicon.image:LoadSprite(asset_name2,bundle_name2,function ()
		self.node_list.img_btnicon.image:SetNativeSize()
	end)
	
	self.can_set = 1
end

function JXListItemRender:OnClickJuanxianHandler()
	local cur_num = GuildWGData.Instance:GetCurJxNum(self.data.contribute_type)
	if cur_num <= 0 and self.data.contribute_maxnum > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildJX.NoCanJuanXianHint)
		return
	end
	-- if self.data.contribute_type == 1 and self.num == 0 then
	-- 	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
	-- 	TipWGCtrl.Instance:CloseOtherView(function ()
	-- 		GuildWGCtrl.Instance:CloseGuildJuanXinaView()
	-- 	end)
	-- 	return
	-- end
	GuildWGCtrl.Instance:SendAddGuildExpReq(self.data.contribute_type,self.num)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local gold = main_role_vo.gold or 0
	local coin = main_role_vo.coin or 0
	local bind_gold = main_role_vo.bind_gold or 0

	if self.data.contribute_type == 1 and self.data.contribute_num <= coin then
		local bundle_name, asset_name = ResPath.GetEffectUi("UI_juanxian_ok")
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect.transform)
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_juanxian, is_success = true, pos = Vector2(0, 0)})
	elseif self.data.contribute_type == 2 and self.data.contribute_num <= bind_gold then
		local bundle_name, asset_name = ResPath.GetEffectUi("UI_juanxian_ok")
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect.transform)
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_juanxian, is_success = true, pos = Vector2(0, 0)})
	elseif self.data.contribute_type == 3 and self.data.contribute_num <= gold then
		local bundle_name, asset_name = ResPath.GetEffectUi("UI_juanxian_ok")
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect.transform)
		-- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect["UI_JuanXian_"..self.data.contribute_type])
		-- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect.transform)
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_juanxian, is_success = true, pos = Vector2(0, 0)})
	end
end

function JXListItemRender:OnClickItemTips()
	TipWGCtrl.Instance:OpenItem({item_id = self.data.item_id},nil)
end