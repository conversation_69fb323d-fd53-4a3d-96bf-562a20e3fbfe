Monster = Monster or BaseClass(Character)

Monster.Count = 0

function Monster:__init(vo)
    Scene.Instance:SetMonsterJumpFlag(self:GetObjId(), MONSTER_JUMP_FLAG.WAIT_JUMP)
	Monster.Count = Monster.Count + 1
	self.update_num = 0
	self.elapse_time = 0

	self.obj_type = SceneObjType.Monster
	self.followui_class = MonsterFollow
	if DuckRaceWGData.Instance:IsDuckRaceMonster(vo.monster_id) then
		self.followui_class = DuckRaceFollow
	end
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.Monster
	self.need_calculate_priortiy = true

	self.res_id = 0
	self.head_id = 0
	self.general_resid = 0
	self.last_jump_time = 0
	self.boss_type = 0
	self.is_skill_reading = false
	self.magic_skill_id = nil
	self.magic_time_record = nil
	self.aoi_range = 0
	self.tianshen_boss_action_config = nil
	self.shadow_level = 2

	local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.vo.monster_id]
	if nil ~= cfg then
		self.vo.name = cfg.name
		self.res_id = cfg.resid
		self.general_resid = cfg.general_resid or 0
		self.head_id = cfg.small_icon
		self.obj_scale = cfg.scale
		self.dietype = cfg.dietype
		self.checks = cfg.checks
		self.aoi_range = cfg.aoi_range
		self.no_guanghuan = cfg.no_guanghuan == 1
		self.boss_type = cfg.boss_type2

		if self:IsTianShenBoss() then
			if TianShenBossActionConfig[self.res_id] ~= nil then
				self.tianshen_boss_action_config = TianShenBossActionConfig[self.res_id]
			end
		end
	end

	self.totem_name = self.vo.name or ""
	self.draw_obj.is_boss = self:IsBoss()
	self.tower_attack_range = nil
    self.record_time = self.now_time or 0
    self.can_bubble = false
    self.is_talk = false
	self.exist_time = 5
    self.interval = 10
    self:GetUpdateNumber()
    self.be_hit_call_back = nil

    self.xun_luo_status = OPERATION_TASK_CHAIN_MONSTER_XUNLUO_TYPE.NONE
    self.check_idle_ani = false

	self.die_event_list = {}
	self.monster_attack_cache = nil
end

function Monster:__delete()
	Monster.Count = Monster.Count - 1

	if self.time_coundown then
		GlobalTimerQuest:CancelQuest(self.time_coundown)
		self.time_coundown = nil
	end
	if self.tower_attack_range then
		self.tower_attack_range:DeleteMe()
		 self.tower_attack_range = nil
	end
    if self.bobble_timer_quest then
        GlobalTimerQuest:CancelQuest(self.bobble_timer_quest)
        self.bobble_timer_quest = nil
    end

    if self.fazhen_async_loader then
		self.fazhen_async_loader:DeleteMe()
		self.fazhen_async_loader = nil
	end

	if self.alert_eff_loader then
		self.alert_eff_loader:DeleteMe()
		self.alert_eff_loader = nil
    end
    
    if self.xuruo_eff_loader then
		self.xuruo_eff_loader:DeleteMe()
		self.xuruo_eff_loader = nil
	end

	GlobalTimerQuest:CancelQuest(self.rest_anim_delay_timer)
	self.rest_anim_delay_timer = nil
    
    GlobalTimerQuest:CancelQuest(self.delay_hide_bubble_timer)
    self.delay_hide_bubble_timer = nil
	self.be_hit_call_back = nil
	self.boss_scale = nil
	Scene.Instance:SetMonsterJumpFlag(self:GetObjId(), MONSTER_JUMP_FLAG.WAIT_JUMP)

	self.xun_luo_status = OPERATION_TASK_CHAIN_MONSTER_XUNLUO_TYPE.NONE
	self.check_idle_ani = false

	self.die_event_list = nil
	self.monster_attack_cache = nil

	GlobalTimerQuest:CancelQuest(self.play_action_back_fun)
	self.play_action_back_fun = nil

	if self.visib_delay ~= nil then
		GlobalTimerQuest:CancelQuest(self.visib_delay)
		self.visib_delay = nil
	end
end

local DecayMounstCount = 0
function Monster:DeleteDrawObj()
	if (not self:IsRealDead() and self.vo.monster_type ~= SCENE_MONSTER_TYPE.MONSTER_TYPE_CALL) or DecayMounstCount > 10 then
		Character.DeleteDrawObj(self)
		return
	end

	if nil ~= self.draw_obj then
		self.draw_obj:DeleteMe()
		self.draw_obj = nil
	end

	Scene.Instance:SetMonsterJumpFlag(self:GetObjId(), MONSTER_JUMP_FLAG.WAIT_JUMP)
end

function Monster:InitInfo()
	Character.InitInfo(self)
	self:GetFollowUi()
	self:ReloadUIName()

	if self:IsTianShenBoss() then
		self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Tianshen", self.res_id))
	elseif self:IsBoss() then
		self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Boss", self.res_id))
	else
		self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Monster", self.res_id))
	end
end

function Monster:UpdateMonsterIcon()
	local monster_id = self.vo.monster_id
	if self:IsDuckRaceMonster() then
		local duck_trait_cfg = DuckRaceWGData.Instance:GetDuckTraitCfgByMonsterId(monster_id)
		self:GetFollowUi():SetMonsterIconAsset(ResPath.GetDuckRaceImg("a2_ya_num_" .. duck_trait_cfg.duck_index))
	end
end

function Monster:ReloadSpecialImage()
	local scene_logic = Scene.Instance:GetSceneLogic()
	local is_show_special_image, asset, bundle = scene_logic:GetIsShowSpecialImage(self)
	self.follow_ui:SetSpecialImage(is_show_special_image, bundle, asset)

	local scene_type = Scene.Instance:GetSceneType()
	if SceneType.DABAO_BOSS == scene_type or SceneType.SG_BOSS == scene_type then
		local angry = BossWGData.Instance:GetBossAngry(self.vo.monster_id)
		angry = angry ~= 0 and angry or BossWGData.Instance:GetDabaoBossAngry(self.vo.monster_id, scene_type)
		local bundle_name, asset_name = ResPath.GetBossAngryIcon()
		self.follow_ui:SetBoxBossAngry(bundle_name, asset_name, angry)
	end
end

function Monster:DoAttack(skill_id, target_x, target_y, target_obj_id, target_type, awake_level, sub_zone)
    self:CancelPlayActionBackFun()
    local skill_cfg = SkillWGData.GetMonsterSkillConfig(skill_id)
    -- boss大招需要在读条结束前预先播放技能动作，但是多区域的大招需要在技能下发时才知道目标点，所以排除
    if skill_cfg and Monster.IsMaigc(skill_cfg.skill_action) and IS_SELF_POS_RANDETYPE[skill_cfg.RangeType] then
    	self.monster_attack_cache = nil
    	FightWGData.Instance:OnHitTrigger(self, Scene.Instance:GetObj(target_obj_id))
    else
    	if not self:IsDeleted() and (self.vo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_TIANSHEN or self.vo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_CALL) then
		    local part = self.draw_obj:GetPart(SceneObjPart.Main)
		    local part_obj = part:GetObj()
		    if part_obj == nil or IsNil(part_obj.gameObject) then
	       		self.monster_attack_cache = {
	    			skill_id = skill_id,
	    			target_x = target_x,
	    			target_y = target_y,
	    			target_obj_id = target_obj_id,
	    			target_type = target_type,
	    			awake_level = awake_level,
	    			sub_zone = sub_zone,
	    			load_t = {[SceneObjPart.Main] = false, [SceneObjPart.Weapon] = not (self:IsTianShenBoss() and self:IsWeaponOwnAnim()),},
	    		}
	    		return
		    end
    	end

    	self.monster_attack_cache = nil
    	Character.DoAttack(self, skill_id, target_x, target_y, target_obj_id, target_type, awake_level, sub_zone)
    end
end

function Monster:ReloadBuffList()
	if not self:IsBoss() then
		return
	end
	
	self:RemoveAllBuff(true)
    local buff_list = FightWGData.Instance:GetOtherRoleEffectList(self:GetObjId())
    if not IsEmptyTable(buff_list) then
        for k,v in pairs(buff_list) do
            self:AddBuff(v.buff_type, v.product_id)
        end
    end
end

function Monster:AddBuff(buff_type, product_id)
	Character.AddBuff(self, buff_type, product_id)
	local scene_id = Scene.Instance:GetSceneId()
	if buff_type == 41 and BossWGData.IsWorldBossScene(scene_id) then
		local scale = BossWGData.Instance:GetBossHuDunScale(self.vo.monster_id)
		self.buff_effect_list[buff_type]:SetScale({scale, scale, scale})

		--个人塔防
		if Scene.Instance:GetSceneType() == SceneType.TowerDefend then
			local other_cfg = ConfigManager.Instance:GetAutoConfig("towerdefend_auto").other[1]
			local life_tower_monster_id = other_cfg.life_tower_monster_id
			if self.vo.monster_id == life_tower_monster_id then
				self.buff_effect_list[buff_type]:SetScale({1.3, 1.3, 1.3})
			end
		end
	end
end

-- 重写CheckModleScale方法 避免放大缩小给限制了
function Monster:CheckModleScale()

end

-- Boss  Scale(诛邪再用)
function Monster:CheckBossModleScale(scale)
	self.boss_scale = scale
end

function Monster:GetIsLimitRotate()
	local is_limit = false
	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId() or 0
	if self.vo == nil or self.res_id == nil or self.vo.monster_id == nil then
		return true
	end

	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId() or 0
	if scene_type == SceneType.DailyTaskFb and self.res_id == 3030001 then --领土战防御塔
		is_limit = true
	elseif math.floor(self.res_id / 1000) == 2038 then --领土战防御塔
		if scene_type == SceneType.QunXianLuanDou then
			is_limit = true
		elseif scene_type == SceneType.ClashTerritory then
			--local size = ClashTerritoryData.Instance:GetTerritoryMonsterSide(self.vo.monster_id)
			-- if size then
			-- 	is_limit = true
			-- end
		end
	elseif scene_type == SceneType.GongChengZhan then
		is_limit = true
	elseif scene_type == SceneType.TowerDefend then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("towerdefendteam_auto").other[1]
		local life_tower_monster_id = other_cfg.life_tower_monster_id
		if self.vo.monster_id == life_tower_monster_id then
			is_limit = true
		end
	elseif scene_type == SceneType.MonsterSiegeFb and self.vo.monster_id == 50268 then
		is_limit = true
	elseif scene_type == SceneType.PET_FB or scene_type == SceneType.FakePetFb then
		is_limit = true
	end

	return is_limit
end

function Monster:InitAppearance()
	if self:IsTianShenBoss() then
		if self:IsWeaponOwnAnim() then
            local tianshen_cfg, is_huamo = TianShenWGData.Instance:GetImageModelByAppeId(self.res_id, false)
			local weapon_res_id = 0
			if is_huamo then
				weapon_res_id = tianshen_cfg ~= nil and tianshen_cfg.ts_res_id or 0
			else
				local shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(tianshen_cfg and tianshen_cfg.index)
				weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id or self.res_id
			end

			local bundle, asset = ResPath.GetTianShenShenQiPath(weapon_res_id)
			self:ChangeModel(SceneObjPart.Weapon, bundle, asset)
		end
	end

	local scene_type = Scene.Instance:GetSceneType()
	local scene_id = Scene.Instance:GetSceneId() or 0
	self.load_priority = 3
	local transform = self.draw_obj:GetRoot().transform

	if nil ~= self.boss_scale then
		transform.localScale = Vector3(self.boss_scale, self.boss_scale, self.boss_scale)
	elseif nil ~= self.obj_scale then
		transform.localScale = Vector3(self.obj_scale, self.obj_scale, self.obj_scale)
	end

	local bundle, asset = self:GetMonsterBundleAsset()
	if scene_type == SceneType.DailyTaskFb and self.res_id == 3030001 then --领土战防御塔
		self.draw_obj:Rotate(0, 52, 0)
		-- self:InitModel(ResPath.GetMonsterModel(self.res_id))
	elseif math.floor(self.res_id / 1000) == 2038 then --领土战防御塔
		if scene_type == SceneType.QunXianLuanDou then
			local qxld_cfg = ConfigManager.Instance:GetAutoConfig("qunxianlundouconfig_auto").other[1]
			self.draw_obj:Rotate(0, math.deg(math.atan2(qxld_cfg.town_direction_x - self.logic_pos.x, qxld_cfg.town_direction_y - self.logic_pos.y)) - 90, 0)
		elseif scene_type == SceneType.ClashTerritory then
			-- local size = ClashTerritoryData.Instance:GetTerritoryMonsterSide(self.vo.monster_id)
			-- if size then
			-- 	self.draw_obj:Rotate(0, size == 0 and 180 or 0, 0)
			-- end
			-- self.draw_obj:SetOffset(Vector3(0, -2, 0))
		end
		-- self:InitModel(ResPath.GetMonsterModel(self.res_id))
	elseif scene_type == SceneType.GongChengZhan then
		if self.vo.monster_id == ConfigManager.Instance:GetAutoConfig("gongchengzhan_auto").other[1].boss2_id then
			--or self.vo.monster_id == ConfigManager.Instance:GetAutoConfig("gongchengzhan_auto").other[1].boss2_1_id or self.vo.monster_id == ConfigManager.Instance:GetAutoConfig("gongchengzhan_auto").other[1].boss2_2_id or self.vo.monster_id == ConfigManager.Instance:GetAutoConfig("gongchengzhan_auto").other[1].boss2_3_id
			-- local lv = CityCombatData.Instance:GetShouGuildTotemLevel()
			-- local qizhi_res_id = GuildWGData.Instance:GetQiZhiResId(lv)
			-- self.head_id = GuildWGData.Instance:GetQiZhiHeadId(lv)
			-- self:InitModel(ResPath.GetQiZhiModel(qizhi_res_id))
			-- self.draw_obj.root.transform.rotation = Quaternion.identity
			self.draw_obj:Rotate(0, 270, 0)
		elseif self.vo.monster_id == ConfigManager.Instance:GetAutoConfig("gongchengzhan_auto").other[1].boss1_id then
			self.draw_obj:Rotate(0, 90, 0)
			-- self:InitModel(ResPath.GetMonsterModel(self.res_id))
		else
			--self.draw_obj:Rotate(0, math.random(0, 360), 0)
			self.draw_obj:Rotate(0, 275, 0)
			-- self:InitModel(ResPath.GetMonsterModel(self.res_id))
		end
	elseif scene_type == SceneType.GuideFb or
			scene_type == SceneType.MountStoryFb or
			scene_type == SceneType.WingStoryFb or
			scene_type == SceneType.XianNvStoryFb then

		if self.vo.monster_id == 7501 then
			local qizhi_res_id = GuildWGData.Instance:GetQiZhiResId(14)
			-- self:InitModel(ResPath.GetQiZhiModel(qizhi_res_id))
			bundle, asset = ResPath.GetQiZhiModel(qizhi_res_id)
		end
	elseif scene_type == SceneType.TowerDefend then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("towerdefendteam_auto").other[1]
		local life_tower_monster_id = other_cfg.life_tower_monster_id
		if self.vo.monster_id == life_tower_monster_id then
			self.draw_obj:Rotate(0, 220, 0)
		end
		-- self:InitModel(ResPath.GetMonsterModel(self.res_id))
	elseif scene_type == SceneType.MonsterSiegeFb and self.vo.monster_id == 50268 then
		self.draw_obj:Rotate(0, 0, 0)
	elseif scene_type == SceneType.FakePetFb then	--scene_type == SceneType.PET_FB or 
        self.draw_obj:Rotate(0, 0, 0)
	end

	self:InitModel(bundle, asset)
end

function Monster:InitModel(bundle, asset)
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic:HideMonster(self:GetVo()) then
		self:ForceSetVisible(false)
		self:ForceShadowVisible(false)
		return
	end

	local sync_anim_type = self:IsWeaponOwnAnim() and SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN or nil
	self:ChangeObjWaitSyncAnimType(sync_anim_type)
	self:ChangeModel(SceneObjPart.Main, bundle, asset)
	if self:CanPlayBianShenYanEffectOnInit() then
		self:PlayBianShenYanEffect()
	end

	--配置里面没有，写死算了
	self.record_time = self.record_time + self.interval
end

function Monster:InitEnd()
	Character.InitEnd(self)

	if MAGIC_SPECIAL_STATUS_TYPE.READING == self.vo.status_type then
		self:StartSkillReading(0)
	end
end

function Monster:SetDirectionByXY(x, y)
	if self.vo and math.floor(self.res_id / 1000) == 2038 then--and ClashTerritoryData.Instance:GetTerritoryMonsterSide(self.vo.monster_id) then
		return
	end

	if self.vo then
		if not BossWGData.Instance:GetMonsterRotable(self.vo.monster_id) then
			return
		end
	end

	Character.SetDirectionByXY(self, x, y)
end

function Monster:OnEnterScene()
	Character.OnEnterScene(self)
	self:ReloadSpecialImage()
	self:CreateShadow()
	self:UpdateMonsterIcon()
end

function Monster:OnClick()
	local need_load_select = self.select_effect == nil
	Character.OnClick(self)
	if not self:IsBoss() then
		self:ShowName()
		if need_load_select and self.select_effect then
        	local bundle, asset = ResPath.GetEnvironmentCommonEffect("eff_xuanzhong")
			self.select_effect:Load(bundle, asset, function(gameobj)
            	self.select_effect_gameobj = gameobj
				self:ChangeXuanZhongEffect()
		 	end)
		end
	end

	self:ChangeXuanZhongEffect()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.DefenseFb and self:IsTower() then
		if nil == self.tower_attack_range then
			self.tower_attack_range = AllocAsyncLoader(self, "tower_attack_range")
			self.tower_attack_range:SetParent(self.draw_obj:GetRoot().transform)
			self.tower_attack_range:SetIsUseObjPool(true)
			local b,a = ResPath.GetMiscEffect("yuanxinggongjifanwei")
    		self.tower_attack_range:Load(b,a,function ()
    			self.tower_attack_range:SetLocalPosition(Vector3(0, 0.1, 0))
    		end)

		end
		self.tower_attack_range:SetActive(true)
	end
end

function Monster:ChangeXuanZhongEffect()
	if not IsNil(self.select_effect_gameobj) then
		if self.no_guanghuan then
            --客服不要法阵要选中框，特殊处理
            if self:GetVo() and self:GetVo().monster_id == 63001 then
                self.select_effect_gameobj:SetActive(true)
            else
                self.select_effect_gameobj:SetActive(false)
            end
		else
			self.select_effect_gameobj.transform.localScale = Vector3.one
		end
	end
end

function Monster:CancelSelect()
	Character.CancelSelect(self)
	local scene_logic = Scene.Instance:GetSceneLogic()
	if not self:IsBoss() and not scene_logic:AlwaysShowMonsterName(self:GetMonsterId()) then
		self:HideName()
	end
end


function Monster:EnterStateDead()
	Character.EnterStateDead(self)

	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	if main_part then
		GlobalTimerQuest:AddDelayTimer(function ()
			main_part:PlayDissolveEffect()
		end, 1.5)
	end

	if self.gameobject_attach_handle then
		self.gameobject_attach_handle:ForceSetVisible(false)
	end
end

function Monster:IsMonster()
	return true
end

function Monster:IsBoss()
	return false
end

-- 判断是不是小鸭疾走里的怪物
function Monster:IsDuckRaceMonster()
	local monster_id = self.vo.monster_id
	return DuckRaceWGData.Instance:IsDuckRaceMonster(monster_id) 
end

function Monster:IsTower()
	if not self.vo then return false end
	local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.vo.monster_id]
	if not cfg then return false end
	return cfg.is_tower == GameEnum.IS_TOWER
end

function Monster:GetMonsterId()
	if self.vo then
		return self.vo.monster_id
	end

	return 0
end

function Monster:GetMonsterKey()
	if self.vo then
		return self.vo.monster_key
	end

	return 0
end

function Monster:GetMonsterHead()
	return self.head_id
end

function Monster:IsSkillReading()
	return self.is_skill_reading
end

function Monster:GetUpdateNumber()
	if self:IsBoss() then
		self.update_num = 0
	else
		self.update_num = math.ceil(Monster.Count / 5)
	end
end

function Monster:Update(now_time, elapse_time)
	self.update_num = self.update_num - 1
	self.elapse_time = self.elapse_time + elapse_time
	if self.update_num > 0 then
		return
	end

	elapse_time = self.elapse_time
	self.elapse_time = 0
	self:GetUpdateNumber()

	Character.Update(self, now_time, elapse_time)
	self:UpdateMagic(now_time, elapse_time)

    if nil ~= self.follow_ui and self.follow_ui.view then
    	local cfg = BossWGData.Instance:GetMonsterCfgByid(self.vo.monster_id)
        if now_time > self.record_time and self.is_talk == false and cfg and cfg.monster_words ~= "" then
            self.is_talk = true
            self:UpdataBubble(cfg.monster_words)
            self.follow_ui:ShowBubble()
            self.bobble_timer_quest = GlobalTimerQuest:AddDelayTimer(function()
                self.follow_ui:HideBubble()
                -- self.is_talk = false
                self.record_time = now_time + self.interval
            end,self.exist_time)
        end
    end
end

function Monster:ShowTextToBubble(str, time)
	if not self.is_talk then
	    self.is_talk = true
	    self:UpdataBubble(str)
	    self.follow_ui:ShowBubble()            
	    self.bobble_timer_quest = GlobalTimerQuest:AddDelayTimer(function()
	    	if self.follow_ui then
		        self.follow_ui:HideBubble()
		    end
		    self.is_talk = false
	    end, time)
	end
end

function Monster:UpdateMagic(now_time, elapse_time)
	if not self.is_skill_reading or not self.magic_skill_id then
		return
	end

	if self.magic_time_record and self.magic_time_record <= now_time then
		self.magic_time_record = nil
		local skill_cfg = SkillWGData.GetMonsterSkillConfig(self.magic_skill_id)
		if not skill_cfg then
			return
		end

		local part = self.draw_obj:GetPart(SceneObjPart.Main)
		local part_obj = part:GetObj()
		if not part_obj or IsNil(part_obj.gameObject) then
			return
		end

		local delay_time = 0
		if skill_cfg.delay_time ~= "" and skill_cfg.delay_time > 0 then
			delay_time = skill_cfg.delay_time / 1000
		end

		-- 因为magic_time_record在协议下发时已经做了计时处理，如果该技能delay_time大于0走到这里已经完成延时了
		if delay_time > 0 then
			self.magic_atk_time = now_time
		else
			local is_magic = Monster.IsMaigc(skill_cfg.skill_action)
			if not is_magic then
				self.magic_atk_time = now_time
			else
				local anim_name = skill_cfg.skill_action .. "_2"
				local anim_cfg = self:GetActionTimeRecord(anim_name)
				if anim_cfg ~= nil then
					part:CrossFade(anim_name, nil, 0)
	
					-- 非多区域大招都提前播放第三段动作
					local length_pre = part:GetClipLength(skill_cfg.skill_action .. "_1")
					if IS_SELF_POS_RANDETYPE[skill_cfg.RangeType] then
						local length
						local act_cfg = self:GetActionTimeRecord(skill_cfg.skill_action .. "_3")
						if act_cfg and act_cfg.hit_time then
							length = act_cfg.hit_time
						else
							length = part:GetClipLength(skill_cfg.skill_action .. "_3")
						end
	
						self.magic_atk_time = math.max(0, skill_cfg.ReadingTime - length_pre - length) + now_time
						-- self:CharacterAnimatorEvent(nil, nil, anim_name.."/begin")
					end
				else
					self.magic_atk_time = now_time
				end
			end
		end
	end

	if self.magic_atk_time and self.magic_atk_time <= now_time then
		self.magic_atk_time = nil
		self.attack_skill_id = self.magic_skill_id
		self:ChangeState(SceneObjState.Atk)
	end
end

function Monster:ClearMagicData()
	self.magic_skill_id = nil
	self.is_skill_reading = false
	self.magic_time_record = nil
	self.magic_atk_time = nil
end

function Monster:StartSkillReading(skill_id, param, target_obj_id, target_obj_pos_x, target_obj_pos_y)
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	local part_obj = part:GetObj()
	if part_obj == nil or IsNil(part_obj.gameObject) then
		return false
	end

	if skill_id ~= 0 and param ~= nil and param ~= 0 then
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.CROSS_TASK_CHAIN_BOSS then
			local str = self.vo ~= nil and self.vo.name or ""
			str = str .. Language.OpertionAcitvity.TaskChain.BossTip
			ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "common_slider", {slider_str = str, time = param + TimeWGCtrl.Instance:GetServerTime(), time_type = OPERATION_TASK_CHAIN_SCHEDULE_TIMER_TYPE.LEFT_SLIDER})
		end
	end

	local skill_cfg = SkillWGData.GetMonsterSkillConfig(skill_id)
	if skill_cfg == nil then
		return
	end

	local anim_name = "magic1_1"
	local delay_time = 0
	local is_magic = false
	delay_time = (skill_cfg.delay_time ~= "" and skill_cfg.delay_time > 0) and (skill_cfg.delay_time / 1000) or 0
	is_magic = Monster.IsMaigc(skill_cfg.skill_action)
	if is_magic then
		anim_name = skill_cfg.skill_action .. "_1"
	end

	self.attack_target_pos_x = target_obj_pos_x
	self.attack_target_pos_y = target_obj_pos_y
	self.magic_skill_id = skill_id
	self.is_skill_reading = true

	-- 策划提的骚需求，新逻辑如果配置表delay_time大于0时以此延迟时间为主，否则走以前的逻辑
	if delay_time > 0 then
		self.magic_time_record = delay_time + Status.NowTime
	else
		if is_magic then
			if self:GetActionTimeRecord(anim_name) then
				self.magic_time_record = self:GetActionTimeRecord(anim_name).time + Status.NowTime
			end
		else
			self.magic_time_record = skill_cfg.ReadingTime + Status.NowTime
		end
	end

	if is_magic then
		part:CrossFade(anim_name, nil, 0)
		-- 执行一下动画事件，不然没有特效
		self:CharacterAnimatorEvent(nil, nil, anim_name.."/begin")
		-- part_obj.animator:SetTrigger(anim_name)
	end

	return true
end

function Monster:OnPlayActionBackEnd()
    self:ChangeToCommonState()
end

function Monster:CancelPlayActionBackFun()
    if self.play_action_back_fun then
        GlobalTimerQuest:CancelQuest(self.play_action_back_fun)
    end
    self.play_action_back_fun = nil
end

function Monster:EndSkillReading(skill_id)
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	local part_obj = part:GetObj()
	if part_obj == nil or IsNil(part_obj.gameObject) then
		return false
	end

	local skill_cfg = SkillWGData.GetMonsterSkillConfig(skill_id)
	if nil ~= skill_cfg then
		local is_magic = ("magic1" == skill_cfg.skill_action or "magic2" == skill_cfg.skill_action)
		if is_magic then
			self:ClearMagicData()
			part_obj.animator:SetTrigger("boss_end")
		end
	end
end

function Monster.IsMaigc(skill_action)
	return "magic1" == skill_action or "magic2" == skill_action
			or "magic3" == skill_action or "magic4" == skill_action
end

function Monster:EnterStateAttack()
	if self:IsTianShenBoss() and self.tianshen_boss_action_config ~= nil then
		local anim_name = SceneObjAnimator.Atk1
		local skill_cfg = SkillWGData.GetMonsterSkillConfig(self.attack_skill_id)
		if skill_cfg == nil then
			local info_cfg = SkillWGData.Instance:GetSkillClientConfig(self.attack_skill_id)
			anim_name = SkillWGData.GetActionString(self.attack_skill_id, info_cfg.action, 1, 0)
		else
			anim_name = skill_cfg.skill_action
		end
		
		self.anim_name = anim_name
		self.tianshen_action_time_record = self.tianshen_boss_action_config[anim_name]
		self.is_move_over_pos = false
    	self:ClearActionData()

    	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	    local part_obj = part:GetObj()
	    if part_obj == nil or IsNil(part_obj.gameObject) then
	        return
	    end

		self:CrossAction(SceneObjPart.Main, anim_name)
	 	self:OnAnimatorBegin()
	else
		local anim_name = SceneObjAnimator.Atk1
		local skill_cfg = SkillWGData.GetMonsterSkillConfig(self.attack_skill_id)
		if nil ~= skill_cfg then
			local is_magic = Monster.IsMaigc(skill_cfg.skill_action)
			-- if self.is_skill_reading and is_magic then -- 正在读条中且是魔法技能，则是一个完整的（读条-聚气-释放）
			if is_magic then -- 正在读条中且是魔法技能，则是一个完整的（读条-聚气-释放）
				self:ClearMagicData()
				anim_name = skill_cfg.skill_action .. "_3"

			-- elseif not self.is_skill_reading and is_magic then -- 没在读条但收到魔法技能id，则处理成普攻
			-- 	anim_name = SceneObjAnimator.Atk1

			else
				anim_name = skill_cfg.skill_action
			end
		end

		Character.EnterStateAttack(self, anim_name)
	end
end

function Monster:QuitStateAttack(attack_skill_id)
	Character.QuitStateAttack(self, attack_skill_id)

	if self.vo ~= nil and self.vo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_TIANSHEN then
		if self.visib_delay ~= nil then
			GlobalTimerQuest:CancelQuest(self.visib_delay)
			self.visib_delay = nil
		end

		self.visib_delay = GlobalTimerQuest:AddDelayTimer(function ()
			if not self:IsDeleted() then
				self:ForceSetVisible(false)
			end
		end, 1)
	end
end

function Monster:CreateFollowUi()
	Character.CreateFollowUi(self, SceneObjType.Monster)
	if self.vo then
		self.follow_ui:SetHpPercent(self.vo.hp / self.vo.max_hp)
	end
end

function Monster:ShowName()
	local follow_ui = self:GetFollowUi()
	if nil == follow_ui then
		return
	end
	follow_ui:ShowName()
end

function Monster:HideName()
	local follow_ui = self:GetFollowUi()
	if nil == follow_ui then
		return
	end
	follow_ui:HideName()
end

function Monster:FollowUiShow()
	local follow_ui = self:GetFollowUi()
	if nil == follow_ui then
		return
	end
	follow_ui:Show()
end

function Monster:FollowUiHide()
	local follow_ui = self:GetFollowUi()
	if nil == follow_ui then
		return
	end
	follow_ui:Hide()
end

function Monster:OnDie()
	Character.OnDie(self)

	local part_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
	if self.fazhen_async_loader then
		self.fazhen_async_loader:DeleteMe()
		self.fazhen_async_loader = nil
	end

	if self.is_skill_reading then
		self:ClearMagicData()
		if nil ~= part_obj and nil ~= part_obj.actor_ctrl then
			part_obj.actor_ctrl:StopEffects()
		end
	end

	for _, v in pairs(self.die_event_list) do
		v()
	end
end

function Monster:NotifyDieEvent(key, func)
	if key and func then
		self.die_event_list[key] = func
	end
end

function Monster:UnNotifyDieEvent(key)
	if key then
		self.die_event_list[key] = nil
	end
end

function Monster:DoBeHit(deliverer, ...)
	Character.DoBeHit(self, deliverer, ...)
	if self.do_hit_call_back then
		self.do_hit_call_back(self.vo.hp, self.vo.max_hp, deliverer)
	end
end

function Monster:SetDoHitCallback(do_hit_call_back)
	self.do_hit_call_back = do_hit_call_back
end

-- 受击
function Monster:OnBeHit(real_blood, deliverer, skill_id)
	Character.OnBeHit(self, real_blood, deliverer, skill_id)
	
	--策划需求，受击就播闪光效果
	self:DoBeHitShowAction()

	if self:IsBoss() or self:IsTower() then return end
	if SkillWGData.GetSkillBigType(skill_id) == SKILL_BIG_TYPE.NORMAL then
		self:DoHurt()
	end
	
	if self.be_hit_call_back then
		self.be_hit_call_back(self.vo.hp, self.vo.max_hp, deliverer)
	end
end

function Monster:SetBeHitCallback(be_hit_call_back)
	self.be_hit_call_back = be_hit_call_back
end

function Monster:SetBubble(text)
	if nil ~= self.follow_ui and text then
		self.follow_ui:ChangeBubble(text)
	end
	if text then
		self.follow_ui:ShowBubble()
	else
		self.follow_ui:HideBubble()
	end
end

function Monster:SetShowBubbleDelayHide(text, delay_time)
    delay_time = delay_time or 3
	if nil ~= self.follow_ui and text then
        self.follow_ui:ChangeBubble(text)
        if self.delay_hide_bubble_timer then
            GlobalTimerQuest:CancelQuest(self.delay_hide_bubble_timer)
            self.delay_hide_bubble_timer = nil
        end
        self.delay_hide_bubble_timer = GlobalTimerQuest:AddDelayTimer(function()
            if not self:IsDeleted() then
                self:SetBubble()
            end
            GlobalTimerQuest:CancelQuest(self.delay_hide_bubble_timer)
            self.delay_hide_bubble_timer = nil
        end, delay_time)
	end
end

function Monster:FlushMonsterName()
	self.vo.name = self.totem_name

	self:ReloadUIName()
end

function Monster:ReloadUIName()
	if self.follow_ui ~= nil then
		local scene_logic = Scene.Instance:GetSceneLogic()
        local color_name = scene_logic:GetMonsterName(self)
		self.follow_ui:SetName(color_name, self)
	end
end

function Monster:SetMonsterName(name)
	if self.follow_ui ~= nil and name then
		self.follow_ui:SetName(name, self)
	end
end

function Monster:GetActionTimeRecord(anim_name)
	local atr = MonsterActionConfig[self.res_id]
	if self:IsTianShenBoss() then
		if TianShenBossActionConfig[self.res_id] ~= nil then
			atr = TianShenBossActionConfig[self.res_id][anim_name]
		end
	end

    if atr ~= nil then
        return atr[anim_name]
    end

    return MonsterActionConfig[0][anim_name]
end

function Monster:PlayAddHpEffect()
	GlobalEventSystem:Fire(ObjectEventType.HP_RECOVER,self)
end

function Monster:OnModelLoaded(part, obj)
	if self:IsDeleted() then
		return
	end

	Character.OnModelLoaded(self, part, obj)

	if self.monster_attack_cache ~= nil and self.monster_attack_cache.load_t ~= nil then
		if self.monster_attack_cache.load_t[part] ~= nil then
			self.monster_attack_cache.load_t[part] = true
		end
	end

	if part == SceneObjPart.Main then
	 	if obj then
	 		if not self.fazhen_async_loader then
	 			if self:IsBoss() and not self.no_guanghuan and not self:IsTower() then
			 		self.fazhen_async_loader = AllocAsyncLoader(self, "eff_boss_fazhen")
			 		self.fazhen_async_loader:SetIsUseObjPool(true)
					self.fazhen_async_loader:SetIsInQueueLoad(true)
					self.fazhen_async_loader:SetParent(obj.transform)
					local ass, name = ResPath.GetEnvironmentCommonEffect("eff_boss_fazhen")
					self.fazhen_async_loader:Load(ass, name, function(gameobj)
							if IsNil(gameobj) then
								return
							end
							if IsNil(obj.gameObject) then
								return
							end

							local lossyScale = obj.transform.lossyScale
							local lossy_x = lossyScale.x ~= 0 and lossyScale.x or 1
							local lossy_y = lossyScale.y ~= 0 and lossyScale.y or 1
							local lossy_z = lossyScale.z ~= 0 and lossyScale.z or 1
							gameobj.transform.localScale = Vector3(2.5 / lossy_x, 2.5 / lossy_y, 2.5 / lossy_z)
						end)
				end
			elseif not self:IsBoss() or self:IsTower() then
				self.fazhen_async_loader:DeleteMe()
				self.fazhen_async_loader = nil
			end

			-- 怪物出生动作
			if self.vo ~= nil and (self.vo.monster_type ~= SCENE_MONSTER_TYPE.MONSTER_TYPE_TIANSHEN and self.vo.monster_type ~= SCENE_MONSTER_TYPE.MONSTER_TYPE_CALL) then
				local draw_obj = self:GetDrawObj()
				if draw_obj ~= nil and self:GetVo() ~= nil and self:GetVo().hp == self:GetVo().max_hp then
					local part_obj = draw_obj:GetPart(SceneObjPart.Main)
					if part_obj:GetObj() and part_obj:GetObj().animator then
						local clip = part_obj:GetObj().animator:GetAnimationClip(SceneObjAnimator.D_Rest)
						if clip and not clip.empty then
							self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Rest)
							self:CharacterAnimatorEvent(nil, nil, SceneObjAnimator.Rest.."/begin")

							GlobalTimerQuest:CancelQuest(self.rest_anim_delay_timer)
							self.rest_anim_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
								if part_obj and part_obj:GetObj() and part_obj:GetIsCurrentAnimatorStateName(SceneObjAnimator.Rest) then
									self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle)
								end
								GlobalTimerQuest:CancelQuest(self.rest_anim_delay_timer)
								self.rest_anim_delay_timer = nil
							end, clip.length)
						end
					end
				end
			end

			--入场跳跃动作
			if self:GetVo() ~= nil and Scene.Instance:GetSceneLogic():CanMonsterDoJump(self:GetVo().special_param)  then
				if Scene.Instance:GetMonsterJumpFlag(self:GetObjId()) == MONSTER_JUMP_FLAG.WAIT_JUMP then
					self:DoJump()
					Scene.Instance:SetMonsterJumpFlag(self:GetObjId(), MONSTER_JUMP_FLAG.HAS_JUMP)
					return
				end
			end

			local scene_logic = Scene.Instance:GetSceneLogic()
			if scene_logic:AlwaysShowMonsterName(self:GetMonsterId()) then
				local follow_ui = self:GetFollowUi()
				if follow_ui then
					follow_ui:Show()
					self:ShowName()
				end
			end

			self:ShowAlertEff(self:GetXunLuoStatus() == OPERATION_TASK_CHAIN_MONSTER_XUNLUO_TYPE.XUNLUO, obj)
		end
	end

	self:CheckIsCanDoAttack()
	self:ReloadBuffList()
end

function Monster:CheckIsCanDoAttack()
	if self.monster_attack_cache ~= nil then
		for k,v in pairs (self.monster_attack_cache.load_t) do
			if not v then
				return
			end
		end

		self:DoAttack(self.monster_attack_cache.skill_id, self.monster_attack_cache.target_x, 
			self.monster_attack_cache.target_y, self.monster_attack_cache.target_obj_id, 
			self.monster_attack_cache.target_type, self.monster_attack_cache.awake_level, self.monster_attack_cache.sub_zone)

		self.monster_attack_cache = nil
	end
end

function Monster:DoJump()
	Character.DoJump(self)
	self.last_jump_time = Status.NowTime + 1
	self.need_jump = false

	local start_logic_x, start_logic_y = self:GetJumpStartPoint()
	if start_logic_x and start_logic_y then
		local logic_x, logic_y = self:GetLogicPos()
		local self_real_x, self_real_y = GameMapHelper.LogicToWorld(logic_x, logic_y)
		local real_x, real_y = GameMapHelper.LogicToWorld(start_logic_x, start_logic_y)
		local root = self:GetRoot()

		root.gameObject.transform:SetLocalPosition(real_x, root.transform.position.y, real_y)
		root.gameObject.transform:DOMove(Vector3(self_real_x, root.transform.position.y, self_real_y), 0.8):SetEase(DG.Tweening.Ease.InCubic):OnComplete(function()
			if self.cache_end_jummp_state and self.state_machine and not self.state_machine:IsInState(self.cache_end_jummp_state) then
				self.last_jump_time = 0
				self:ChangeState(self.cache_end_jummp_state)
				self.cache_end_jummp_state = nil
			end
		end)
	end
end

function Monster:IsJumping()
	return self.last_jump_time > Status.NowTime
end

function Monster:ChangeState(state)
    if self:IsJumping() then
		self.cache_end_jummp_state = state
        return
    end
   Character.ChangeState(self,state)
end

function Monster:GetJumpStartPoint()
	return Scene.Instance:GetSceneLogic():GetMonsterJumpLogicPos(self)
end

function Monster:UpdataBubble(text)
    if nil ~= self.follow_ui then
        self.follow_ui:ChangeBubble(text)
    end
end

function Monster:CalculatePriortiy()
	if SceneObj.select_obj == self then
		return SceneAppearPriority.High
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		if u3dpool.v2Length(u3dpool.v2Sub(main_role.logic_pos, self.logic_pos), false) <= 5 * 5 then
			return SceneAppearPriority.High
		else
			return SceneAppearPriority.Middle
		end
	end

	return SceneAppearPriority.Middle
end

function Monster:SetXunLuoStutes(value)
	self.xun_luo_status = value
	self:ShowAlertEff(self.xun_luo_status == OPERATION_TASK_CHAIN_MONSTER_XUNLUO_TYPE.XUNLUO)
	local follow_ui = self:GetFollowUi()
	if self.xun_luo_status == OPERATION_TASK_CHAIN_MONSTER_XUNLUO_TYPE.XUNLUO and follow_ui ~= nil then
		follow_ui:ForceSetVisible(true)
	end
end

function Monster:GetXunLuoStatus()
	return self.xun_luo_status
end

function Monster:CurRunAni()
	if self:GetXunLuoStatus() == OPERATION_TASK_CHAIN_MONSTER_XUNLUO_TYPE.PURSUE or self:GetXunLuoStatus() == OPERATION_TASK_CHAIN_MONSTER_XUNLUO_TYPE.BLACK then
		-- return SceneObjAnimator.Xun_Luo_Move
		return SceneObjAnimator.Move
	elseif self:IsDuckRaceMonster() then
		return SceneObjAnimator.Move --self:GetIsSwimming() and SceneObjAnimator.Run_YouYong or SceneObjAnimator.Move
	else
		return Character.CurRunAni(self)
	end
end

function Monster:SetIsCheckIdleAni(value)
	self.check_idle_ani = value
end

function Monster:CurIdleAni()
	local scene_type = Scene.Instance:GetSceneType()
	if self.check_idle_ani and scene_type == SceneType.CROSS_TASK_CHAIN_HUSONG then
		local info = OperationTaskChainWGData.Instance:GetHuSongInfo()
		if info ~= nil and info.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.FIGHT then
			return SceneObjAnimator.Monster_Idle_Show
		end
	end

	return Character.CurIdleAni(self)
end

function Monster:ShowXuruoEffect(is_show)
	if not is_show then
		if self.xuruo_eff_loader then
			self.xuruo_eff_loader:SetActive(false)
		end
		return
	end

    if self.xuruo_eff_loader == nil then
        local obj = self.draw_obj:GetRoot()
		if obj ~= nil and not IsNil(obj.transform) then
			self.xuruo_eff_loader = AllocAsyncLoader(self, "xuruo_eff_loader")
			self.xuruo_eff_loader:SetIsUseObjPool(true)
			self.xuruo_eff_loader:SetLoadPriority(ResLoadPriority.low)
			self.xuruo_eff_loader:SetParent(obj.transform)
			local ass, name = ResPath.GetEnvironmentEffect("BUFF_xuruo_01")
			self.xuruo_eff_loader:Load(ass, name, function(gameobj)
			if IsNil(gameobj) then
				return
			end
			if IsNil(obj.gameObject) then
				return
			end
			end)
		end
	else
		self.xuruo_eff_loader:SetActive(true)
	end
end

function Monster:ShowAlertEff(is_show, obj)
	if self.aoi_range <= 0 or not is_show then
		if self.alert_eff_loader then
			self.alert_eff_loader:SetActive(false)
		end

		return
	end

	if self.alert_eff_loader == nil then
		if obj == nil then
			local draw_obj = self:GetDrawObj()
			if draw_obj ~= nil then
				local part = draw_obj:GetPart(SceneObjPart.Main)
				if part ~= nil then
					obj = part:GetObj()
				end
			end

		end

		if obj ~= nil and not IsNil(obj.transform) then
			self.alert_eff_loader = AllocAsyncLoader(self, "alert_eff")
			self.alert_eff_loader:SetIsUseObjPool(true)
			self.alert_eff_loader:SetLoadPriority(ResLoadPriority.low)
			self.alert_eff_loader:SetParent(obj.transform)
			local ass, name = ResPath.GetMiscEffect("yujing_fanwei")
			self.alert_eff_loader:Load(ass, name, function(gameobj)
			if IsNil(gameobj) then
				return
			end
			if IsNil(obj.gameObject) then
				return
			end

			-- local localScale = obj.transform.localScale
			-- local r = math.ceil(localScale.x * 2.5)
			local r = self.aoi_range / Config.SCENE_TILE_WIDTH
			gameobj.transform.localScale = Vector3(r, r, r)
			local pos = gameobj.transform.localPosition
			gameobj.transform.localPosition = Vector3(pos.x, pos.y + 0.5, pos.z)

			-- gameobj.transform.localScale = Vector3(2.5 / lossyScale.x, 2.5 / lossyScale.y, 2.5 / lossyScale.z)
			end)
		end
	else
		self.alert_eff_loader:SetActive(true)
	end
end

function Monster:GetChecksModel()
	return self.checks
end

function Monster:UpdateStateAttack(elapse_time)
	if self.tianshen_action_time_record ~= nil then
		self.cur_action_time = self.cur_action_time + elapse_time
        if self.has_action_hit == false then
            -- 触发受击
            if self.tianshen_action_time_record.hit_time and self.cur_action_time >= self.tianshen_action_time_record.hit_time then
                self.has_action_hit = true
                self:OnAnimatorHit()
            end
        end

        --被打断处理
        if self.has_do_cut_down == false then
            if self.can_cut_down_time > 0 then
                if self.cur_action_time >= self.can_cut_down_time then
                    self:SetIsAtkPlaying(false)
                    self.has_do_cut_down = true
                end
            end
        end

        if self.state_machine:IsInState(SceneObjState.Atk) and self.has_action_end == false then
            -- 触发动作完成
            if self.cur_action_time >= self.tianshen_action_time_record.time then
                self.has_action_end = true
                self:OnAnimatorEnd()
            end
        end
    else
    	Character.UpdateStateAttack(self, elapse_time)
    end
end

function Monster:CharacterAnimatorEvent(param, state_info, anim_name)
	if self:IsTianShenBoss() then
	    local actor_trigger = self:GetActorTrigger()
	    if actor_trigger ~= nil then
	        local source = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()
	        local target = nil
	        if self.attack_target_obj and self.attack_target_obj.draw_obj then
	            target = self.attack_target_obj.draw_obj:GetRoot()
	        end
	        actor_trigger:OnAnimatorEvent(param, state_info, source, target, anim_name)
	    end
	else
		Character.CharacterAnimatorEvent(self, param, state_info, anim_name)
	end
end

function Monster:OnAnimatorBegin(param, state_info)
    if self:IsTianShenBoss() then
        self.dic = nil
	    self.cur_action_time = 0
	    self:SetIsAtkPlaying(true)
	    local anim_name = self.anim_name
	    self:CharacterAnimatorEvent(param, state_info, anim_name.."/begin")
	else
		Character.OnAnimatorBegin(self, param, state_info)
    end
end

local function PlayProjectileCallBack(cbdata)
    local self = cbdata[1]
    local attack_target_obj = cbdata[2]
    local attack_skill_id = cbdata[3]
    Character.ReleaseCBData(cbdata)

    if not self:IsDeleted() and attack_target_obj ~= nil then
        self:OnAttackHit(attack_skill_id, attack_target_obj)
    end
end

function Monster:OnAnimatorHit(param, state_info)
	if self:IsTianShenBoss() then
	    if self:IsDeleted() then
	        self.attack_skill_id = 0
	        return
	    end
	    if self:IsAtk() then
	        local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	        local obj = main_part:GetObj()
	        if nil == obj or IsNil(obj.gameObject) then
	            return
	        end

	        if self.attack_target_obj == nil then
	            return
	        end
	        local target_draw_obj = self.attack_target_obj.draw_obj
	        if target_draw_obj == nil then
	            return
	        end

	        self:CharacterAnimatorEvent(param, state_info, self.anim_name.."/hit")
	        if self.has_set_prefab_data then
	            --新实现
	            local root = target_draw_obj:GetRoot()
	            local hurt_point = target_draw_obj:GetAttachPoint(AttachPoint.Hurt)
	            local attack_skill_id = self.attack_skill_id
	            local attack_target_obj = self.attack_target_obj
	            local actor_ctrl = self:GetActorWGCtrl()
	            if actor_ctrl ~= nil then
	                local cbdata = Character.GetCBData()
	                cbdata[1] = self
	                cbdata[2] = attack_target_obj
	                cbdata[3] = attack_skill_id
	                actor_ctrl:PlayProjectile(obj, self.anim_name, root, hurt_point, PlayProjectileCallBack, cbdata)
	            end
	        else
	            local actor_ctrl = obj.actor_ctrl
	            if actor_ctrl ~= nil and
	                self.attack_target_obj ~= nil and
	                self.attack_target_obj.draw_obj ~= nil then
	                local root = self.attack_target_obj.draw_obj:GetRoot().transform
	                local hurt_point = self.attack_target_obj.draw_obj:GetAttachPoint(AttachPoint.Hurt)
	                local attack_skill_id = self.attack_skill_id
	                local attack_target_obj = self.attack_target_obj
	                local cbdata = Character.GetCBData()
	                cbdata[1] = self
	                cbdata[2] = attack_target_obj
	                cbdata[3] = attack_skill_id
	                actor_ctrl:PlayProjectile(self.anim_name, root, hurt_point, PlayProjectileCallBack, cbdata)
	            end
	        end
	    end
	else
		Character.OnAnimatorHit(self, param, state_info)
	end
end

function Monster:OnAnimatorEnd(param, state_info)
	if self:IsTianShenBoss() then
		self:SetIsAtkPlaying(false)
	    if self:IsAtk() then
	        self:AttackActionEndHandle()
	    end
	    self:CharacterAnimatorEvent(param, state_info, self.anim_name.."/end")
	else
		Character.OnAnimatorEnd(self, param, state_info)
	end
end

function Monster:AttackActionEndHandle()
	if self:IsTianShenBoss() then
		if self.tianshen_action_time_record ~= nil and self.tianshen_action_time_record.has_back then
			GlobalTimerQuest:CancelQuest(self.play_action_back_fun)
			self.play_action_back_fun = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnPlayActionBackEnd, self), self.tianshen_action_time_record.back_time)
			self:CrossAction(SceneObjPart.Main, self.anim_name.."_back")
		else
			self:OnPlayActionBackEnd()
		end
	else
		if self.action_time_record ~= nil and self.action_time_record.has_back then
	        self.play_action_back_fun = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.OnPlayActionBackEnd, self), self.action_time_record.back_time)
			self:CrossAction(SceneObjPart.Main, self.anim_name.."_back")
	    else
	        self:ChangeToCommonState()
	    end
	end
end

function Monster:IsTianShenBoss()
	return self.boss_type == ClientBossType.TSBoss or self.boss_type == ClientBossType.WeaponAniTSBoss
end

function Monster:IsNoSelectTianShenMonster()
	return self.vo ~= nil and self.vo.monster_type ~= nil and self.vo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_TIANSHEN
end

function Monster:IsWeaponOwnAnim()
	return self.boss_type == ClientBossType.WeaponAniTSBoss
end

function Monster:GetMonsterBundleAsset()
	local bundle, asset
	if self:IsTianShenBoss() then
		bundle, asset = ResPath.GetBianShenModel(self.res_id)
	else
		bundle, asset = ResPath.GetMonsterModel(self.res_id)
	end

	return bundle, asset
end

function Monster:FlushMonsterResId(res_id)
	self.res_id = res_id
	if self:IsTianShenBoss() then
		if TianShenBossActionConfig[self.res_id] ~= nil then
			self.tianshen_boss_action_config = TianShenBossActionConfig[self.res_id]
		end
	end

	if self.boss_scale then
		local transform = self.draw_obj:GetRoot().transform
		transform.localScale = Vector3(self.boss_scale, self.boss_scale, self.boss_scale)
	end

	local sync_anim_type = self:IsWeaponOwnAnim() and SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN or nil
	self:ChangeObjWaitSyncAnimType(sync_anim_type)
	local bundle, asset = self:GetMonsterBundleAsset()
	self:ChangeModel(SceneObjPart.Main, bundle, asset)
end

--怪物出生时,需要播放烟雾特效
function Monster:CanPlayBianShenYanEffectOnInit()
	return false
end

function Monster:PlayBianShenYanEffect()
	local eff_bundle, eff_asset_name = ResPath.GetEffect("effect_bianshen_yan_01")
    local draw_obj = self:GetDrawObj()
    if draw_obj ~= nil then
        EffectManager.Instance:PlayControlEffect(self, eff_bundle, eff_asset_name, draw_obj:GetPosition())
    end
end

function Monster:SetMonsterAimEffect(is_show)
	local follow_ui = self:GetFollowUi()
	if nil == follow_ui then
		return
	end

	local offset_info
	follow_ui:ShowAimEffect(is_show, offset_info)
end

function Monster:GetOwnerObjId()
	local obj_id = nil
	if not self:IsDeleted() and self.vo.is_has_owner >= 0 and self.vo.is_has_owner ~= COMMON_CONSTS.SERVER_INVAILD_OBJ_ID then
		obj_id = self.vo.is_has_owner
	end

	return obj_id
end

function Monster:IsTianShenCallMonster()
	return self.vo ~= nil and self.vo.monster_type ~= nil and self.vo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_CALL
end

function Monster:IsCanShowHead()
	return true
end