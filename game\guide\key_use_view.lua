--------------------------------------------------
--一键使用视图
--------------------------------------------------
KeyUseView = KeyUseView or BaseClass(SafeBaseView)

local ForceUseEquipLevel = 90 		-- 多少级之前强行穿装备
local ACTIVE_TEXT_LEVEL = 100 		-- 多少级之前才显示文本

function KeyUseView:__init()
	self.call_back = nil
	self.item_list = {}

	self.active_close = false
	self.view_name = "KeyUseView"
	self.calc_active_close_ui_volume = false

	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.ui_config = {"uis/view/guide_ui_prefab", "GuideUi"}
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third2_panel")
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "layout_auto_equip_view")
end

function KeyUseView:__delete()
	if nil ~= self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.item_name_txt = nil
	self.up_num = nil
end

function KeyUseView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("key_use_countdown") then
		CountDownManager.Instance:RemoveCountDown("key_use_countdown")
	end
	if nil ~= self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
	self.item_name_txt = nil
	self.up_num = nil
	self.change_panel_mark = nil
	self.third_panel_canvas = nil

	if self.slider_bg_tween then
		self.slider_bg_tween:Kill()
		self.slider_bg_tween = nil
	end
end

function KeyUseView:LoadCallBack()
	self.item_name_txt = self.node_list.item_name_txt
	self.cur_num  = 1
	self.node_list.lbl_num.text.text = self.cur_num
	self.change_panel_mark = false
	self.third_panel_canvas = nil

	self.item_cell = ItemCell.New(self.node_list.item)
	self.item_cell:SetItemTipFrom(ItemTip.FROM_KEY_USE_EQUIP)

	self.item_name_txt.text.text = ""
	XUI.AddClickEventListener(self.node_list.btn_key_use, BindTool.Bind2(self.ClickHandler, self, true))
	XUI.AddClickEventListener(self.node_list.btn_key_use2, BindTool.Bind2(self.ClickHandler, self, true))
	XUI.AddClickEventListener(self.node_list.btn_key_use3, BindTool.Bind2(self.ClickHandler, self, true))

	XUI.AddClickEventListener(self.node_list.ButtonUse3, BindTool.Bind2(self.ClickHandler, self, true))
	XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.Close, self, true))

	self.node_list.btn_clear_close_window:SetActive(true)
	XUI.AddClickEventListener(self.node_list.btn_clear_close_window, BindTool.Bind(self.ClearClose, self, true))
	
	self.save_third_pos = self.node_list["layout_commmon_third_root"].rect.anchoredPosition
	self.save_equip_pos = self.node_list["layout_auto_equip_root"].rect.anchoredPosition
end

-- 移除无效数据
function KeyUseView:RemoveInvaildItem()
	local tab = {}
	local index, add_flag = 1, false
	for i = 1, #self.item_list do
		local data = self.item_list[i]
		if data ~= nil then
			add_flag = false
			-- 山海经背包容错
			if ShanHaiJingWGData.Instance:GetTJResolveShowCfg(data.id) ~= nil then
				add_flag = true
			-- 圣装背包
			elseif FairyLandEquipmentWGData.Instance:IsHolyEquipByItemId(data.id) then
				local he_data = FairyLandEquipmentWGData.Instance:GetHolyEquipBagItem(data.index)
				if he_data ~= nil and he_data.item_id ~= nil and he_data.item_id == data.id
				and he_data.num ~= nil and he_data.num >= 1 then
					add_flag = true
				end
			-- 秘笈背包
			elseif CultivationWGData.Instance:GetIsEsotericaPartItem(data.id) then
				local he_data = CultivationWGData.Instance:GetEsotericaBagItem(data.index)
				if he_data ~= nil and he_data.item_id ~= nil and he_data.item_id == data.id
				and he_data.num ~= nil and he_data.num >= 1 then
					add_flag = true
				end
			--武魂背包
			elseif WuHunFrontWGData.Instance:GetItemDataByItemId(data.id) then
				local he_data = WuHunFrontWGData.Instance:GetWuHunBagItem(data.index)
				if he_data ~= nil and he_data.item_id ~= nil and he_data.item_id == data.id
				and he_data.num ~= nil and he_data.num >= 1 then
					add_flag = true
				end
			-- 梦灵背包
			elseif MengLingWGData.Instance:IsMengLingEquip(data.id) then
				local he_data = MengLingWGData.Instance:GetBagItemByIndex(data.index)
				if he_data ~= nil and he_data.item_id ~= nil and he_data.item_id == data.id
				and he_data.num ~= nil and he_data.num >= 1 then
					add_flag = true
				end
			--雷法背包
			elseif ThunderManaWGData.Instance:IsThunderManaEquip(data.id) then
				local he_data = ThunderManaWGData.Instance:GetThunderBagInfoByIndex(data.index)
				if he_data ~= nil and he_data.item_id ~= nil and he_data.item_id == data.id
				and he_data.num ~= nil and he_data.num >= 1 then
					add_flag = true
				end
			else
				local data_item = ItemWGData.Instance:GetGridData(data.index)
				if data_item ~= nil and data_item.item_id ~= nil
				and data_item.item_id == data.id and data_item.num ~= nil and data_item.num >= 1 then
					add_flag = true
				end
			end

			if add_flag then
				tab[index] = data
				index = index + 1
			end
		end
	end

	self.item_list = tab
end

function KeyUseView:AddItem(id, index, callback, is_end_callback)
	--离线挂机卡特殊拦截(满足20小时就不用弹出)
	if OfflineRestWGData.Instance:IsOfflineGuaJiCard(id) then
		local is_over_time = OfflineRestWGData.Instance:IsOverstep(id, 1)
		if is_over_time then
			return
		end
	end

    if index >= COMMON_CONSTS.MAX_BAG_COUNT and index < (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
    	return
   	end

	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(id)
	if not item_cfg then
		return
	end

	--新增未达到使用等级要求的物品、装备不提示快捷使用规则；需要达到使用等级要求后才会提示快捷使用
	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level < ItemWGData.Instance:GetEquipLimitLevel(item_cfg) then
		return
	end

	--装备 如果开启了自动吞噬则  紫色品质以下全部吞噬  不弹提示
	if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and RoleBagWGCtrl.Instance:GetIsAutoTunShi() and item_cfg.color <= GameEnum.ITEM_COLOR_PURPLE then
		return
	end

	-- 检查背包，避免在弹提示的时候因为某些原因这个道具在背包里变化了，导致逻辑有问题
	local data_item_id = ItemWGData.Instance:GetItemIdByIndex(index)
	local is_tujian = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(id) ~= nil
	if not is_tujian and (data_item_id == nil or data_item_id == 0) then
		return
	end

   	local has_item = false
   	local has_ticket = false
   	local ticket_key
	for k,v in pairs(self.item_list) do
		if v.id == id then
			has_item = true
		end

		if MoneyTicket[v.id] and MoneyTicket[id] then
			has_ticket = true
			if v.id < id then
				ticket_key = k
			end
		end
	end

	local xiaogui_info = EquipWGData.Instance:GetmpGuardInfo()
	for i = 1, 2 do
		local xiaogui_data = xiaogui_info.item_wrapper[i]
		if xiaogui_data and xiaogui_data.item_id and xiaogui_data.item_id > 0 then
			local compose_cfg = ComposeWGData.Instance:GetComposeCfgByProductId(xiaogui_data.item_id)
			if compose_cfg and (compose_cfg.stuff_id_3 == id or compose_cfg.stuff_id_2 == id or compose_cfg.stuff_id_1 == id) then
				return
			end
		end
	end

	-- 钱契检测(需要判断有无钱契红点)
	if has_ticket and ticket_key then  -- 是否可用高级钱契替换低级钱契
		self.item_list[ticket_key] = {id = id, index = index, sub_type = item_cfg.sub_type, click_callback = callback, is_end_callback = is_end_callback}
		self:UpdateShow()

		return
	-- 低级钱契判断
	elseif has_ticket and not ticket_key then
		return
	elseif MoneyTicket[id] then  -- 第一个钱契
		local exchange_flag = RoleBagWGData.Instance:GetYinPiaoExchangeRemind()
		if not exchange_flag then
			return
		end

		local open_view_flag = ViewManager.Instance:IsOpen(GuideModuleName.YinPiaoExchangeView)
		if open_view_flag then
			return
		end

		local open_flag = FunOpen.Instance:GetFunIsOpened(FunName.YinPiaoExchangePopView)
		if not open_flag then
			return
		end
	end

	-- 装备检测 同一部位 只显示最好的
	local change_index = -1
	if (item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT or ItemWGData.GetIsXiaogGui(id)) and not has_item then --or )
		local item_data_1 = ItemWGData.Instance:GetGridData(index)
		local item_cap_1 = EquipmentWGData.Instance:GetEquipPingfen(item_data_1)
		local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
		local cur_equip_body_seq = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)

		for i,v in ipairs(self.item_list) do
			-- 是否是同一肉身同一类型装备
			local target_item_cfg = ItemWGData.Instance:GetItemConfig(v.id)
			local target_equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(target_item_cfg)
			local target_equip_body_seq = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(target_equip_body_index)

			if target_equip_body_seq == cur_equip_body_seq and v.sub_type == item_cfg.sub_type and v.index ~= index then   -- 同部位
				-- 当前装备
				local item_data = ItemWGData.Instance:GetGridData(v.index)
				local item_cap_2 = EquipmentWGData.Instance:GetEquipPingfen(item_data)
				if item_cap_1 > item_cap_2 then
					change_index = i
				end

				has_item = true
				break
			end
		end

		if change_index >= 0 then
			self.item_list[change_index] = {id = id, index = index, sub_type = item_cfg.sub_type, click_callback = callback, is_end_callback = is_end_callback}
		end
	end

	if not has_item then
		self.item_list[#self.item_list + 1] = {id = id, index = index, sub_type = item_cfg.sub_type, click_callback = callback, is_end_callback = is_end_callback}
	end

	--检测武魂是否激活
	if item_cfg.use_type == Item_Use_Type.WUHUNZHENSHEN then
		local wuhun_cfg = WuHunWGData.Instance:GetWuhunResByItemId(item_cfg.id)
		local is_active = false

		if wuhun_cfg then
			is_active = WuHunWGData.Instance:IsWuHunActive(wuhun_cfg)
		end

		if is_active then
			return
		end
	end

	if not self:IsOpen() then
		self:RemoveInvaildItem()
		if not IsEmptyTable(self.item_list) then
			self:Open()
		end
	else
		self:UpdateShow()
	end
end

-- 圣装添加
function KeyUseView:AddHolyEquipItem(data)
    if data == nil or data.item_id == nil or data.item_id <= 0 then
        return
    end

    local item_id = data.item_id
	local index = data.index
    local part =  data.part
	local slot = data.slot
    local sub_type = GameEnum.E_TYPE_XIANJIE_EQUI
    local fle_data = FairyLandEquipmentWGData.Instance

    -- 检查背包，避免在弹提示的时候因为某些原因这个道具在背包里变化了
    local he_data = fle_data:GetHolyEquipBagItem(index)
    if he_data == nil or he_data.item_id == nil or he_data.item_id <= 0 then
        return
    end

    for k,v in pairs(self.item_list) do
		if v.id == item_id then
			return
		end
    end

    local is_add = true

    -- 装备检测 同一部位 只显示最好的
    for k,v in ipairs(self.item_list) do
        if v.sub_type == sub_type
		and v.slot and v.slot == slot
        and v.part and v.part == part
        and v.index ~= index then
            local item_cfg = ItemWGData.Instance:GetItemConfig(v.id)
            if item_cfg then
				if data.color > item_cfg.color then
	                self.item_list[k] = {id = item_id, index = index, sub_type = sub_type,
	                                    slot = slot, part = part,}
				end
				is_add = false
                break
            end
        end
    end

    if is_add then
        self.item_list[#self.item_list + 1] = {id = item_id, index = index, sub_type = sub_type,
												slot = slot, part = part,}
    end

    if not self:IsOpen() then
        self:RemoveInvaildItem()
        if not IsEmptyTable(self.item_list) then
            self:Open()
        end
    else
        self:UpdateShow()
    end
end

-- 秘笈添加
function KeyUseView:AddEsotericaItem(data)
    if data == nil or data.item_id == nil or data.item_id <= 0 then
        return
    end

    local item_id = data.item_id
	local index = data.index
	local slot = data.slot
    local part =  data.part
    local sub_type = 0

    -- 检查背包，避免在弹提示的时候因为某些原因这个道具在背包里变化了
    local he_data = CultivationWGData.Instance:GetEsotericaBagItem(index)
    if he_data == nil or he_data.item_id == nil or he_data.item_id <= 0 then
        return
    end

    for k,v in pairs(self.item_list) do
		if v.id == item_id then
			return
		end
    end

    local is_add = true

    -- 装备检测 同一部位 只显示最好的
    for k,v in ipairs(self.item_list) do
        if v.sub_type == sub_type
		and v.slot and v.slot == slot
        and v.part and v.part == part
        and v.index ~= index then
            local item_cfg = ItemWGData.Instance:GetItemConfig(v.id)
            if item_cfg then
				if data.color > item_cfg.color then
	                self.item_list[k] = {id = item_id, index = index, sub_type = sub_type,
	                                    slot = slot, part = part,}
				end
				is_add = false
                break
            end
        end
    end

    if is_add then
        self.item_list[#self.item_list + 1] = {id = item_id, index = index, sub_type = sub_type,
												slot = slot, part = part,}
    end

    if not self:IsOpen() then
        self:RemoveInvaildItem()
        if not IsEmptyTable(self.item_list) then
            self:Open()
        end
    else
        self:UpdateShow()
    end
end


-- 武魂添加
function KeyUseView:AddWuHunItem(data)
    if data == nil or data.item_id == nil or data.item_id <= 0 then
        return
    end

    local item_id = data.item_id
	local index = data.index
	local slot = data.slot
    local part =  data.part
    local sub_type = 0

    -- 检查背包，避免在弹提示的时候因为某些原因这个道具在背包里变化了
    local he_data = WuHunFrontWGData.Instance:GetWuHunBagItem(index)
    if he_data == nil or he_data.item_id == nil or he_data.item_id <= 0 then
        return
    end

    for k,v in pairs(self.item_list) do
		if v.id == item_id then
			return
		end
    end

    self.item_list[#self.item_list + 1] = {id = item_id, index = index, sub_type = sub_type,
												slot = slot, part = part,}

    if not self:IsOpen() then
        self:RemoveInvaildItem()
        if not IsEmptyTable(self.item_list) then
            self:Open()
        end
    else
        self:UpdateShow()
    end
end

-- 梦灵装备
function KeyUseView:AddMengLingItem(data)
	if data == nil or data.item_id == nil or data.item_id <= 0 then
        return
    end

    local item_id = data.item_id
	local index = data.index
	local slot = data.slot
    local part =  data.part
    local sub_type = 0

	self.item_list[#self.item_list + 1] = {id = item_id, index = index, sub_type = sub_type,
	slot = slot, part = part,}

	if not self:IsOpen() then
		self:RemoveInvaildItem()
		if not IsEmptyTable(self.item_list) then
			self:Open()
		end
	else
		self:UpdateShow()
	end
end


-- 雷法添加
function KeyUseView:AddThunderEquipItem(data)
    if data == nil or data.item_id == nil or data.item_id <= 0 then
        return
    end

    local item_id = data.item_id
	local index = data.index
    local part =  data.part
	local slot = data.slot
    local sub_type = 0

    -- 检查背包，避免在弹提示的时候因为某些原因这个道具在背包里变化了
    local he_data = ThunderManaWGData.Instance:GetThunderBagInfoByIndex(index)
    if he_data == nil or he_data.item_id == nil or he_data.item_id <= 0 then
        return
    end

    for k,v in pairs(self.item_list) do
		if v.id == item_id then
			return
		end
    end

	local is_better_wear = false
	local add_thunder_item_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(data.item_id)
	if add_thunder_item_cfg then
		local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(add_thunder_item_cfg.seq, add_thunder_item_cfg.part)
		if add_thunder_item_cfg.star > part_info.star then
			is_better_wear = true
		end
	end

	if not is_better_wear then
		return
	end

    local is_add = true

    -- 装备检测 同一部位 只显示最好的
    for k, v in ipairs(self.item_list) do
        if v.sub_type == sub_type
		and v.slot and v.slot == slot
        and v.part and v.part == part
        and v.index ~= index then
			local v_thunder_item_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(v.id)
			if v_thunder_item_cfg and add_thunder_item_cfg.star > v_thunder_item_cfg.star then
				self.item_list[k] = {id = item_id, index = index, sub_type = sub_type,
	                                    slot = slot, part = part,}
			end
			is_add = false
        	break
        end
    end

    if is_add then
        self.item_list[#self.item_list + 1] = {id = item_id, index = index, sub_type = sub_type,
												slot = slot, part = part,}
    end

    if not self:IsOpen() then
        self:RemoveInvaildItem()
        if not IsEmptyTable(self.item_list) then
            self:Open()
        end
    else
        self:UpdateShow()
    end
end
-- 关闭面板检查是否强行穿装备
function KeyUseView:CheckNeedForceUse()
	local keyuse_item_data = self.item_list[#self.item_list]
	if not keyuse_item_data then
		return
	end

	local item_id = keyuse_item_data.id
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg then
		return
	end

	if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then  				--装备类型
		if GetMainRoleLevel() <= ForceUseEquipLevel
		and item_cfg.sub_type ~= GameEnum.E_TYPE_SIXIANG
		and item_cfg.sub_type ~= GameEnum.E_TYPE_SIXIANG_BONE
		and item_cfg.sub_type ~= GameEnum.E_TYPE_XIANJIE_EQUI then
			self:ClickHandler(true)
		end
	end
end

function KeyUseView:AddKeyUseCountDown(id)
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(id)
	local is_equip = item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT
	local is_thunder_equip = ThunderManaWGData.Instance:IsThunderManaEquip(id)
	------------------------------------------------------------
	self.node_list.count_dowo_time_txt.text.text = ""
	self.node_list.count_dowo_time_txt:SetActive(false)
	local function update_fun(elapse_time, total_time)
		if is_equip and self.node_list ~= nil and self.node_list.count_dowo_time_txt ~= nil then
			--这个过程中可能会被吞噬
			if RoleBagWGCtrl.Instance:GetIsAutoTunShi() and item_cfg.color <= GameEnum.ITEM_COLOR_PURPLE then
				if CountDownManager.Instance:HasCountDown("key_use_countdown") then
					CountDownManager.Instance:RemoveCountDown("key_use_countdown")
				end
				self:Close()
			else
				self.node_list.count_dowo_time_txt.text.text = string.format(Language.Common.EquimentTip, math.ceil(total_time- elapse_time))
				self.node_list.count_dowo_time_txt:SetActive(true)
			end
		elseif is_thunder_equip then
			self.node_list.count_dowo_time_txt.text.text = string.format(Language.Common.EquimentTip, math.ceil(total_time- elapse_time))
			self.node_list.count_dowo_time_txt:SetActive(true)
		end
	end

	local function complete_fun()
		if CountDownManager.Instance:HasCountDown("key_use_countdown") then
			CountDownManager.Instance:RemoveCountDown("key_use_countdown")
		end

		if is_equip then
			self:ClickHandler(true)
		elseif is_thunder_equip then
			self:ClickHandler(true)
		else
			self:Close()
		end
	end

	if CountDownManager.Instance:HasCountDown("key_use_countdown") then
		CountDownManager.Instance:RemoveCountDown("key_use_countdown")
	end

	local wait_time = 4
	update_fun(0, wait_time)
	CountDownManager.Instance:AddCountDown("key_use_countdown", update_fun, complete_fun, nil, wait_time, 1)
	self:DoBgSliderTween(wait_time)
end

function KeyUseView:DoBgSliderTween(time)
	if self.slider_bg_tween then
		self.slider_bg_tween:Kill()
		self.slider_bg_tween = nil
	end

	if self.node_list.slider_bg then
		self.node_list.slider_bg.image.fillAmount = 0
		self.slider_bg_tween = self.node_list.slider_bg.image:DOFillAmount(1, time):SetEase(DG.Tweening.Ease.Linear)
	end
end

function KeyUseView:OnFlush(param_t)
	self:FlushView()
end

function KeyUseView:FlushView()
	if CountDownManager.Instance:HasCountDown("key_use_countdown") then
		CountDownManager.Instance:RemoveCountDown("key_use_countdown")
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	self.node_list.use_text:SetActive(role_level < ACTIVE_TEXT_LEVEL)

	self.item_name_txt = self.node_list.item_name_txt
	self.up_num = self.node_list.up_num
	local data = self.item_list[#self.item_list]
	if data == nil then
		self:Close()
		return
	end

	self.node_list.t1:SetActive(false)
	self.node_list.t2:SetActive(false)
	self.node_list.t3:SetActive(false)
	self.node_list.t4:SetActive(false)

	local item_id = data.id
	local bag_index = data.index
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)

	local function end_func()
		self.item_cell:ClearData()
		self.item_name_txt:SetActive(false)
		self.item_list[#self.item_list] = nil --清除无用的，继续下一个
		self:UpdateShow()
	end

	if item_cfg == nil then
		end_func()
		return
	end

	-- 是否为图鉴
	local is_tujian = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(item_id) ~= nil
	if is_tujian then
		local item_num = ShanHaiJingWGData.Instance:GetItemNumInBagById(item_id)
		if item_num <= 0 then
			end_func()
			return
		end
		
		self.item_cell:SetData({item_id = item_id,num = item_num})
		self.item_name_txt.text.text = item_cfg.name
		self.node_list.t2:SetActive(true)
		self:FlushNum(item_id, item_num)
		self:AddKeyUseCountDown(item_id)
		return
	end

	-- 是否为钱契
	if MoneyTicket[item_id] then
		local item_num = ShanHaiJingWGData.Instance:GetItemNumInBagById(item_id)
		self.item_cell:SetData({item_id = COMMON_CONSTS.EXPENSE_ITEM_36424})
		self.item_name_txt.text.text = Language.Bag.Exchange_Tip_4
		self.node_list.des_3.text.text = Language.Bag.Exchange_Tip_3
		self.node_list.t3:SetActive(true)
		return
	end

	local item_data
	local num = 0
	-- 圣装
	if item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then
		item_data = FairyLandEquipmentWGData.Instance:GetHolyEquipBagItem(bag_index)
		num = item_data and item_data.num or 0
	-- 秘笈
	elseif CultivationWGData.Instance:GetIsEsotericaPartItem(item_id) then
		item_data = CultivationWGData.Instance:GetEsotericaBagItem(bag_index)
		num = item_data and item_data.num or 0
	--武魂背包
	elseif WuHunFrontWGData.Instance:GetItemDataByItemId(item_id) and WuHunFrontWGData.Instance:GetIsFrontGemByItemId(item_id) then
		local cfg = WuHunFrontWGData.Instance:GetIsFrontGemByItemId(item_id)
		--武魂已经激活 不激活不快捷使用
		if cfg and WuHunWGData.Instance:GetWuHunIsActive(cfg.wuhun_id) then
			item_data = WuHunFrontWGData.Instance:GetWuHunBagItem(bag_index)
			num = item_data and item_data.num or 0
		end
	-- 梦灵背包
	elseif MengLingWGData.Instance:IsMengLingEquip(item_id) then
		item_data = MengLingWGData.Instance:GetBagItemByIndex(bag_index)
		num = item_data and item_data.num or 0
	--雷法背包
	elseif ThunderManaWGData.Instance:IsThunderManaEquip(item_id) then
		item_data = ThunderManaWGData.Instance:GetThunderBagInfoByIndex(bag_index)
		num = item_data and item_data.num or 0
	else
		item_data = ItemWGData.Instance:GetGridData(bag_index)
		num = ItemWGData.Instance:GetItemNumInBagByIndex(bag_index, item_id)
	end

	if item_data == nil or num <= 0 then
		end_func()
	end

	--检测武魂是否激活
	if item_cfg.use_type == Item_Use_Type.WUHUNZHENSHEN then
		local wuhun_cfg = WuHunWGData.Instance:GetWuhunResByItemId(item_cfg.id)
		local is_active = false

		if wuhun_cfg then
			is_active = WuHunWGData.Instance:IsWuHunActive(wuhun_cfg)
		end

		if is_active then
			end_func()
		end
	end

	self:AddKeyUseCountDown(item_id)
	if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then		--装备类型
		local show_yinji = item_cfg.sub_type ~= GameEnum.E_TYPE_SIXIANG
							and item_cfg.sub_type ~= GameEnum.E_TYPE_SIXIANG_BONE
							and item_cfg.sub_type ~= GameEnum.E_TYPE_XIANJIE_EQUI
							and item_cfg.sub_type ~= GameEnum.E_TYPE_XIANJIE_EQUI
		self.node_list.t1:SetActive(true)
	elseif item_type == GameEnum.ITEM_BIGTYPE_OTHER and item_cfg.is_tip_use == 1 then
		self.item_name_txt.text.text = Language.Guide.KeyuseBtnLable[3]
		self.node_list.t4:SetActive(true)
	elseif ThunderManaWGData.Instance:IsThunderManaEquip(item_id) then
		self.node_list.t1:SetActive(true)
	elseif MengLingWGData.Instance:IsMengLingEquip(item_id) then
		self.node_list.t4:SetActive(true)
	else
		self.node_list.t2:SetActive(true)
		self:FlushQuickUse(item_id)
	end

    self.item_cell:SetData(item_data)
	self.item_name_txt.text.text = item_cfg.name
	self.item_name_txt:SetActive(true)
	if ThunderManaWGData.Instance:IsThunderManaEquip(item_id) then
		self.item_cell:SetUpFlagIconVisible(true)
	end
	-- local is_better, sub = EquipWGData.Instance:GetIsBetterEquip(item_data)
	-- if sub == nil then
	-- 	self.up_num.text.text = EquipmentWGData.Instance:GetEquipPingfen(item_data)
	-- else
	-- 	self.up_num.text.text = sub
	-- end

	-- end_func()
end

function KeyUseView:UpdateShow()
	self:RemoveInvaildItem()
	self:Flush()
end

--有返回值,返回是否使用成功
function KeyUseView:ClickHandler(is_click)
	local keyuse_item_data = self.item_list[#self.item_list]
	if keyuse_item_data == nil then
		self:Close()
		return false
	end

	if nil ~= keyuse_item_data.is_end_callback and nil ~= keyuse_item_data.click_callback then  --时间结束后自动回调
		keyuse_item_data.click_callback(self)
	end

	if not is_click then
		self:Close()
		return false
	end

	local item_id = keyuse_item_data.id
	local bag_index = keyuse_item_data.index
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil or bag_index == -1 then
		self.item_list[#self.item_list] = nil
		self:UpdateShow()
		return false
	end

	-- 是否为图鉴
	local is_tujian = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(item_id) ~= nil
	if is_tujian then --图鉴
		-- 跳转到图鉴 设置跳转参数
		ShanHaiJingWGData.Instance:SetTJTips(false)
		ShanHaiJingWGData.Instance:SetJumpTuJianInfo(item_id)
		ViewManager.Instance:Open(GuideModuleName.ShanHaiJingView, nil, "all", {flush_jump_tujian = true})

	-- 称号
	elseif item_cfg.use_type == 3 then
		local title_id = item_cfg.param1
		local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
		if diy_cfg then
			TitleWGCtrl.Instance:OpenDiyTitleNameView(title_id)
		else
			RoleWGCtrl.Instance:OpenRoleTitleView(title_id)
		end

	-- 奇境	
	elseif item_cfg.use_type == Item_Use_Type.BACKGROUND then
		local back_id = item_cfg.param1
		RoleWGCtrl.Instance:OpenBackViewByUse(back_id)
	elseif item_cfg.use_type == Item_Use_Type.MECHA then
		local mecha_item_cfg = MechaWGData.Instance:GetMechaActiveItemCfgByItemId(item_id)
		
		if not IsEmptyTable(mecha_item_cfg) then
			MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.USE_ACTIVE_ITEM, mecha_item_cfg.seq, 1)
		end

	-- 钱契
	elseif MoneyTicket[item_id] then
		ViewManager.Instance:Open(GuideModuleName.YinPiaoExchangeView)

	-- 圣装
	elseif item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then
		local he_data = FairyLandEquipmentWGData.Instance:GetHolyEquipBagItem(bag_index)
		if he_data and he_data.item_id ~= nil and he_data.item_id > 0 then
			FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.PUT_ON, he_data.slot, bag_index)
		end

	-- 秘笈
	elseif CultivationWGData.Instance:GetIsEsotericaPartItem(item_id) then
		CultivationWGCtrl.Instance:JumpEsotericaView(keyuse_item_data.slot)

	-- 魂阵
	elseif WuHunFrontWGData.Instance:GetItemDataByItemId(item_id) then
		local cfg = WuHunFrontWGData.Instance:GetIsFrontGemByItemId(item_id) or {}
		WuHunWGCtrl.Instance:JumpWUHunFrontView(cfg.wuhun_id, cfg.soul_front_seq, cfg.soul_stone_seq)

	-- 进阶属性丹
	elseif NewAppearanceWGData.Instance:GetIsAdvancedSXD(item_id) then
		local add_num = ItemWGData.Instance:GetItemNumInBagByIndex(bag_index, item_id)
		local cfg = NewAppearanceWGData.Instance:GetAdvancedSXDCfgByItemId(item_id)
		if cfg then
			NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_SHUXINGDAN, cfg.type, cfg.slot_idx, add_num)
		end
	-- 坐骑属性丹
	elseif NewAppearanceWGData.Instance:GetIsMountSXD(item_id) then
		local add_num = ItemWGData.Instance:GetItemNumInBagByIndex(bag_index, item_id)
		local cfg = NewAppearanceWGData.Instance:GetMountSXDCfgByItemId(item_id)
		if cfg then
			NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_SHUXINGDAN, cfg.slot_idx, add_num)
		end
	-- 灵宠属性丹
	elseif NewAppearanceWGData.Instance:GetIsLingChongSXD(item_id) then
		local add_num = ItemWGData.Instance:GetItemNumInBagByIndex(bag_index, item_id)
		local cfg = NewAppearanceWGData.Instance:GetLingChongSXDCfgByItemId(item_id)
		if cfg then
			NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.USE_SHUXINGDAN, cfg.slot_idx, add_num)
		end
	elseif DragonTempleWGData.Instance:GetIsHatchPelletCostCfg(item_id) then
		DragonTempleWGCtrl.Instance:UseDanReq(item_id)
	elseif NewAppearanceWGData.Instance:IsAttrStoreSXD(item_id) and item_cfg.is_tip_use == 1 then
		local sxd_cfg, sxd_type, send_type = NewAppearanceWGData.Instance:GetAttrStoreSXDCfg(item_id)
		local sxd_data_list
		if sxd_type == NEWAPPEARANCE_SXD_TYPE.UpGrade then
			sxd_data_list = NewAppearanceWGData.Instance:GetAdvancedSXDExtendlist(send_type)
		elseif sxd_type == NEWAPPEARANCE_SXD_TYPE.Other then
			sxd_data_list = NewAppearanceWGData.Instance:GetQiChongSXDExtendlist(send_type)
		end

		local data = sxd_data_list[sxd_cfg.dan_index_1]
		NewAppearanceWGCtrl.Instance:OnClickAttributeStoneItem(data, true)
	elseif MengLingWGData.Instance:IsMengLingEquip(item_id) then
		ViewManager.Instance:Open(GuideModuleName.MengLingView, TabIndex.mengling_bag)
	elseif ThunderManaWGData.Instance:IsThunderManaEquip(item_id) then
		local thunder_item_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(item_id)
		if thunder_item_cfg then
			ThunderManaWGCtrl.Instance:SendCSThunderRequest(THUNDER_OPERATE_TYPE.PUT_EQUIP, thunder_item_cfg.seq, thunder_item_cfg.part, bag_index)
		end
	elseif item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then --装备类型
		local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)

		if equip_index ~= -1 then
			if ItemWGData.GetIsXiaogGui(item_id) then -- 小鬼特殊处理
				local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(item_id)
				equip_index = xiaogui_cfg.impguard_type - 1
			end

			local func2 = function()
				-- 使用装备通过动画去装备了，这里屏蔽先不删除免得以后加回来
				if EquipWGData.CheckIsTreasure(item_cfg.sub_type) == true then
					self:ReqUseEquip(bag_index, item_id, equip_index, item_cfg.need_gold)
				end

				if nil ~= keyuse_item_data.click_callback then
					BagWGCtrl.Instance:SendUseItem(bag_index, 1, equip_index, item_cfg.need_gold)
				else
					BagWGCtrl.Instance:SendUseItem(bag_index, 1, equip_index, item_cfg.need_gold)
				end
			end

			--印记装备替换处理
			local new_equip_data = ItemWGData.Instance:GetGridData(bag_index)
			-- local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
			local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
			local cur_equip_data = EquipWGData.Instance:GetGridData(equip_body_index)
			local is_need_alert, alert_content = NewYinJiJiChengWGData.Instance:CheckIsNeedAlertWhenEquipLess(cur_equip_data, new_equip_data)

			if is_need_alert then
				TipWGCtrl.Instance:OpenAlertTips(alert_content, func2)
			else
				func2()
			end
		end

	--被动使用类型
	elseif item_type == GameEnum.ITEM_BIGTYPE_OTHER and item_cfg.is_tip_use == 1 then
		-- 技能觉醒书
		if item_cfg.item_type ~= nil and item_cfg.item_type ~= "" and item_cfg.item_type == CLIENT_ITEM_TYPE.AWAKE_SKILL then
			local awake_data = SkillWGData.Instance:GetAwakeForItem()
			if awake_data == nil or awake_data[item_cfg.id] == nil then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Skill.IsNotCanAwake)
			else
				FunOpen.Instance:OpenViewNameByCfg(item_cfg.open_panel, item_cfg.id)
			end
		else
			if LongZhuWGData.Instance:IsLongZhuStuff(item_cfg.id) then
				ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.rolebag_longzhu, "select_long_zhu", {item_id = item_cfg.id})
			else
				FunOpen.Instance:OpenViewNameByCfg(item_cfg.open_panel, item_cfg.id)
			end
		end

	--批量使用
	elseif item_cfg.click_use == 2 then
		if OfflineRestWGData.Instance:IsOverstep(item_id, self.cur_num) then
			OfflineRestWGCtrl.Instance:OpenUserOfflineView(item_id)
			self:Close()
		else
			self:ReqUseItem(bag_index, item_id, self.cur_num, item_cfg.need_gold)
		end
	else
		self:ReqUseItem(bag_index, item_id, 1, item_cfg.need_gold)
	end

	self.item_list[#self.item_list] = nil
	self:UpdateShow()
	return true
end

function KeyUseView:GetGuideUI()
	if self.node_list.btn_key_use then
		return self.node_list.btn_key_use, BindTool.Bind(self.ClickHandler, self, true)
	end
end

function KeyUseView:Close(is_click_close)
	self:CheckNeedForceUse()
	local has_succ_use_flush = self:SetRoleExpDanAutoUse(is_click_close)--经验丹使用成功会执行数据移除刷新,防止下方又移除刷新了一遍

	if #self.item_list == 0 then
		SafeBaseView.Close(self)
		return
	end

	if not has_succ_use_flush then
		self.item_list[#self.item_list] = nil 		 --下一个
		self:UpdateShow()
	end
end

function KeyUseView:ClearClose()
	self.item_list = {}
	-- 直接清空列表，一键关闭
	self:Close(is_click_close)
end

function KeyUseView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("key_use_countdown") then
		CountDownManager.Instance:RemoveCountDown("key_use_countdown")
	end

	if self.item_list[#self.item_list] == nil then
		self.item_list = {}
		return
	end

	self.item_list[#self.item_list] = nil
	self.item_list = {}
	if FunctionGuide.Instance:GetIsGuide() then
		FunctionGuide.Instance:EndGuide()
	end
end

function KeyUseView:ReqUseItem(index, item_id, num, need_gold)
	if not ItemWGData.Instance:IsValidItem(index, item_id) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.KeyUseItemError)
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		return
	end

	if item_cfg.open_panel ~= "" then
		local t = Split(item_cfg.open_panel, "#")
		local tab_index = t[2]
		local is_tab_fun_open, tip = FunOpen.Instance:GetFunIsOpenedByTabName(tab_index, true)
		if is_tab_fun_open ~= true then --标签功能未开启
			SysMsgWGCtrl.Instance:ErrorRemind(tip)
			return
		end
	end

	if 1 <= num or (item_cfg.ignore_num ~= nil and item_cfg.ignore_num == 1) then
		if item_cfg.max_open_num and item_cfg.max_open_num  > 0 then
			local select_gift_data = ItemWGData.Instance:GetItemListInGift(item_id)
			local gift_item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
			if gift_item_cfg and gift_item_cfg.star_describe == 1 then
				select_gift_data = ItemWGData.Instance:GetGiftConfig(item_id)
			end

			FunctionChooseWGCtrl.Instance:SetSelectGifData(index, item_cfg.max_open_num, select_gift_data, item_id)
		else
			if not AppearanceWGData.GetIsAppearanceByItemId(item_id) then
				BagWGCtrl.Instance:SendUseItem(index, num, 0, need_gold)
			end
		end
		
		local is_preview_show_use_click = item_cfg.is_preview_show_use_click ~= nil and item_cfg.is_preview_show_use_click ~= "" and item_cfg.is_preview_show_use_click ~= 0
		if item_cfg.open_panel ~= "" and (not is_preview_show_use_click) then
			FunOpen.Instance:OpenViewNameByCfg(item_cfg.open_panel, item_id)
		end
	elseif num > 1 then
		OfflineRestWGCtrl.Instance:OpenUserOfflineView(item_id)
	end

	-- 如果开的不是神通面板则把神通面板关掉
	if not string.find(item_cfg.open_panel, GuideModuleName.TianShenShenTongView) then
		ViewManager.Instance:Close(GuideModuleName.TianShenShenTongView)
	end
end

function KeyUseView:ReqUseEquip(index, item_id, equip_index, need_gold)
	if not ItemWGData.Instance:IsValidItem(index, item_id) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.KeyUseItemError)
		return
	end

	BagWGCtrl.Instance:SendUseItem(index, 1, equip_index, need_gold)
end

function KeyUseView:OnClickSub()
	self.cur_num = (self.cur_num == 0 and self.num or self.cur_num) - 1
	self.cur_num = self.cur_num <= 1 and 1 or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
end

function KeyUseView:OnClickAdd()
	self.cur_num = (self.cur_num == 0 and self.num or self.cur_num) + 1
	self.cur_num = self.cur_num >= self.max and self.max or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
end

function KeyUseView:FlushQuickUse(item_id)
	local item_cfg, _ = ItemWGData.Instance:GetItemConfig(item_id)
	local item_data = ItemWGData.Instance:GetItem(item_id)

	local num = 0
	if self.item_list[#self.item_list] then
		local bag_index = self.item_list[#self.item_list].index
		num = ItemWGData.Instance:GetItemNumInBagByIndex(bag_index, item_id)
	end
	self:FlushNum(item_id, num)
end

function KeyUseView:FlushNum(item_id, item_num)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg.use_daytimes and item_cfg.use_daytimes ~= 0 then
		local can_user_num = item_cfg.use_daytimes - ItemWGData.Instance:GetItemUseTimes(item_id)
		self.max = item_num <= can_user_num and item_num or can_user_num
	else
		self.max = item_num
	end

	if self.default_num then
		self.num = self.default_num
		self.cur_num = self.num
		self.node_list.lbl_num.text.text = self.default_num
	else
		self.num = item_num
		self.cur_num = self.num
		self.node_list.lbl_num.text.text = self.max
	end
end

--设置人物经验丹自动使用
function KeyUseView:SetRoleExpDanAutoUse(is_click_close)
	if is_click_close then--点击X关闭的不自动使用
		return false
	end

	local keyuse_item_data = self.item_list[#self.item_list]
	if not keyuse_item_data then
		return false
	end

	local item_id = keyuse_item_data.id
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg then
		return false
	end

	if item_cfg and item_cfg.use_type == Item_Use_Type.ROLE_EXP_DAN then
		local is_use_succ = self:ClickHandler(true)
		return is_use_succ
	end

	return false
end
