require("game/hefu/hefu_wg_data")

HeFuWGCtrl = HeFuWGCtrl or BaseClass(BaseWGCtrl)
function HeFuWGCtrl:__init()
    if nil ~= HeFuWGCtrl.Instance then
        ErrorLog("[HeFuWGCtrl]:Attempt to create singleton twice!")
    end
    HeFuWGCtrl.Instance = self

    -- self.data = HeFuWGData.New()

    -- self:RegisterProtocol(SCRoleHeFuYuGao, "OnSCRoleHeFuYuGao")

    -- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))

    -- self.role_data_change_callback = BindTool.Bind(self.OnRoleDataChangeCallBack, self)
    -- RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"level"})
end

function HeFuWGCtrl:__delete()
    if self.role_data_change_callback then
        RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
        self.role_data_change_callback = nil
    end

    -- self.data:DeleteMe()
    -- self.data = nil

    HeFuWGCtrl.Instance = nil
end

function HeFuWGCtrl:OnSCRoleHeFuYuGao(protocol)
    --print_error("收到合服预告协议>>>>>>>>>>>>>", protocol)
    self.data:SetHeFuInfo(protocol)

    if self.data:HasFetchReward() then
        MainuiWGCtrl.Instance:FlushView(0, "HeFuNoticeBtnRelease")
        --注销相关监听事件
        if self.role_data_change_callback then
            RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
            self.role_data_change_callback = nil
        end
    else
        MainuiWGCtrl.Instance:FlushView(0, "HeFuNoticeBtnFlush")
    end

    RemindManager.Instance:Fire(RemindName.HeFuNotice)
end

local function RoleHeFuYuGaoOperate(operate_type)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRoleHeFuYuGao)
    protocol.operate_type = operate_type
    protocol:EncodeAndSend()
end

--请求合服信息
function HeFuWGCtrl:RequestInfo()
    --print_error("请求合服信息 >>>>>>>>>>>>>")
    RoleHeFuYuGaoOperate(ROLE_HEFU_YUGAO.ROLE_HEFU_YUGAO_DATA)
end

--请求领取合服奖励
function HeFuWGCtrl:SendFetchReward()
    --print_error("请求领取合服奖励 >>>>>>>>>>>>>")
    RoleHeFuYuGaoOperate(ROLE_HEFU_YUGAO.ROLE_HEFU_YUGAO_FETCH)
end

function HeFuWGCtrl:MainuiOpenCreate()
    self:RequestInfo()
end

function HeFuWGCtrl:OnRoleDataChangeCallBack(attr_name, value, old_value)
    if attr_name == "level" then
        MainuiWGCtrl.Instance:FlushView(0, "HeFuNoticeBtnFlush")
    end
end