SunRainbowView = SunRainbowView or BaseClass(SafeBaseView)

function SunRainbowView:__init()
    self:SetMaskBg()
	self.view_style = ViewStyle.Half
    self.default_index = TabIndex.sun_rainbow_lottery

	local bundle_name = "uis/view/sun_rainbow_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(TabIndex.sun_rainbow_lottery, bundle_name, "layout_sun_rainbow")
	self:AddViewResource(TabIndex.sun_rainbow_shop, bundle_name, "layout_grch_shop")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

    self.tab_sub = {}
	self.remind_tab = {
			{RemindName.SunRainbow},
            {nil},
		}
end

function SunRainbowView:__delete()

end

function SunRainbowView:LoadCallBack()
    self:InitTabbar()
    self:InitMoneyBar()

    local bundle, asset = ResPath.GetRawImagesPNG("a3_xfcj_bg")

    self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)
end

function SunRainbowView:InitTabbar()
	if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
        self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:Init(Language.SunRainbow.TabSub1, self.tab_sub, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
	end
end
function SunRainbowView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			-- show_coin = true,
			show_cangjin_score = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end


function SunRainbowView:ReleaseCallBack()
    self:ReleaseCallBack_Lottery()
    self:ReleaseCallBack_Shop()

    if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

    if self.money_bar then
        self.money_bar:DeleteMe()
		self.money_bar = nil
    end
end

function SunRainbowView:LoadIndexCallBack(index)
    if index == TabIndex.sun_rainbow_lottery then
        self:LoadCallBack_Lottery()
    elseif index == TabIndex.sun_rainbow_shop then
        self:LoadCallBack_Shop()
    end
end

function SunRainbowView:ShowIndexCallBack(index)
	if index == TabIndex.sun_rainbow_lottery then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.SunRainbowView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUANRI_CHANGHONG)
	end
end

function SunRainbowView:OnFlush(param_t, index)
    if index == TabIndex.sun_rainbow_lottery then
        self:OnFlush_Lottery()
    elseif index == TabIndex.sun_rainbow_shop then
        self:OnFlush_Shop()
    end
end
