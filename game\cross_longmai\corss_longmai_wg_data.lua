CrossLongMaiWGData = CrossLongMaiWGData or BaseClass()

function CrossLongMaiWGData:__init()
	if CrossLongMaiWGData.Instance then
		error("[CrossLongMaiWGData] Attempt to create singleton twice!")
		return
	end

	CrossLongMaiWGData.Instance = self

    -- 【配置】
    local cfg = ConfigManager.Instance:GetAutoConfig("cross_dragon_vein_auto")
    self.other_cfg = cfg.other[1]
    -- 龙脉
    self.boss_next_refresh_time = 0
    self.gather_next_refresh_time = 0
    self.boss_info_list = {}
    self.boss_gather_info_list = {}
    self.own_boss_list = {}
    self.refresh_boss_map_cfg = {}
    for k,v in ipairs(cfg.boss_refresh) do
        local pos_list = Split(v.refresh_pos, ",")
        self.refresh_boss_map_cfg[v.monster_id] = {pos_x = tonumber(pos_list[1]) or 0, pos_y = tonumber(pos_list[2]) or 0}
    end

    self.gather_map_cfg = ListToMap(cfg.gather, "gather_id")
    self.camera_view_cfg = ListToMap(cfg.camera_view, "scene_id", "server_index")
    self.dead_drop_cfg = cfg.dead_drop_item
    self.dead_drop_map_cfg = ListToMap(cfg.dead_drop_item, "item_id")

    -- 商店
    self.longmai_shop_id_cfg = ListToMap(cfg.shop, "id")
    self.longmai_shop_flush_cfg = ListToMap(cfg.shop_refresh_cost, "refresh_times")
    -- 玩法
    self.rule_tips_cfg = ListToMapList(cfg.rule_tips,"rule_perent_index")

    -- 【红点】
    -- 龙脉
    RemindManager.Instance:Register(RemindName.CrossLongMaiAct, BindTool.Bind(self.GetActRemind, self))
    -- 商店
end

function CrossLongMaiWGData:__delete()
    -- 龙脉
    RemindManager.Instance:UnRegister(RemindName.CrossLongMaiAct)
    -- 商店

    CrossLongMaiWGData.Instance = nil
end

function CrossLongMaiWGData:GetOtherCfg()
    return self.other_cfg
end

-- 龙脉
function CrossLongMaiWGData:GetMaxGatherNum()
    return self.other_cfg.max_gather_times or 0
end

function CrossLongMaiWGData:GetDisplayReward()
    return ConfigManager.Instance:GetAutoConfig("cross_dragon_vein_auto").reward_show[1]
end

function CrossLongMaiWGData:GetBossRefreshPoint(monster_id)
    return self.refresh_boss_map_cfg[monster_id]
end

function CrossLongMaiWGData:GetGatherCfg(gather_id)
    return self.gather_map_cfg[gather_id]
end

function CrossLongMaiWGData:SetSceneInfo(protocol)
    self.boss_next_refresh_time = protocol.boss_next_refresh_time
    self.gather_next_refresh_time = protocol.gather_next_refresh_time
    self.boss_info_list = protocol.boss_info_list
end

function CrossLongMaiWGData:SetBossGatherInfo(protocol)
    self.boss_gather_info_list = protocol.boss_gather_info_list
end

function CrossLongMaiWGData:SetOwnBossInfo(protocol)
    self.own_boss_list[protocol.monster_obj_id] = {own_obj_uuid = protocol.team_leader_uuid, own_name = protocol.owner_name}
end

function CrossLongMaiWGData:GetOwnBossInfo(monster_obj_id)
    return self.own_boss_list[monster_obj_id]
end

function CrossLongMaiWGData:CleanOwnBossInfo()
    self.own_boss_list = {}
end

function CrossLongMaiWGData:GetBossNextRefreshTime()
    return self.boss_next_refresh_time
end

function CrossLongMaiWGData:GetGatherNextRefreshTime()
    return self.gather_next_refresh_time
end

function CrossLongMaiWGData:GetBossInfoList()
    return self.boss_info_list
end

function CrossLongMaiWGData:GetBossGatherInfo(monster_id)
    return self.boss_gather_info_list[monster_id]
end

function CrossLongMaiWGData:SetGatherTimes(value)
    self.cur_gather_times = value
end

function CrossLongMaiWGData:GetGatherTimes()
    return self.cur_gather_times or self:GetMaxGatherNum()
end

function CrossLongMaiWGData:GetActRemind()
    local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_LONGMAI)
    if not act_is_open then
        return 0
    end

    local fun_is_open = FunOpen.Instance:GetFunIsOpened(FunName.CrossLongMaiView)
    if not fun_is_open then
        return 0
    end

    local times = self:GetGatherTimes()
    local max_times = self:GetMaxGatherNum()
    return times < max_times and 1 or 0
end

function CrossLongMaiWGData:SelectRandGatherObj()
    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil then
        return nil
    end

    local obj_list = {}
    local gather_obj_list = Scene.Instance:GetObjListByType(SceneObjType.GatherObj)

    for _, v in pairs(gather_obj_list) do
        if not v:IsDeleted() then
            local gather_cfg = self:GetGatherCfg(v:GetGatherId())
            if gather_cfg and gather_cfg.quality >= GameEnum.ITEM_COLOR_PURPLE then
                table.insert(obj_list, v)
            end
        end
    end

    if #obj_list > 0 then
        return obj_list[GameMath.Rand(1, #obj_list)]
    end

    return nil
end

-- 获得当前城池场景的摄像机朝向
function CrossLongMaiWGData:GetCameraDefaultAngle()
    -- 需要策划后续调整 目前直接把index写死成1
    local scene_id = Scene.Instance:GetSceneId()
	return (self.camera_view_cfg[scene_id] or {})[1]
end


-- 商店
function CrossLongMaiWGData:SetShopFlushTimes(times)
    self.shop_flush_time = times
end

--返回剩余免费刷新次数，总次数
function CrossLongMaiWGData:GetShopFlushTimesInfo()
    local flush_times = self.shop_flush_time or 0
    local free_fresh_count = self.other_cfg.shop_free_refresh_times - flush_times
    return free_fresh_count, flush_times
end

function CrossLongMaiWGData:SetShopInfo(protocol)
    self.longmai_shop_list_info = protocol.item_list
end

function CrossLongMaiWGData:GetShopInfo(protocol)
    return self.longmai_shop_list_info or {}
end

function CrossLongMaiWGData:GetShopByIdInfo(id)
    return self.longmai_shop_id_cfg[id] or {}
end

function CrossLongMaiWGData:GetShopInfoCfg()
    return self.longmai_shop_id_cfg
end

function CrossLongMaiWGData:GetShopFlushPrice(times)
    local refresh_cost = 0
    local refresh_times = times - self.other_cfg.shop_free_refresh_times
    for i,v in ipairs(self.longmai_shop_flush_cfg) do
        if refresh_times >= v.refresh_times then
            refresh_cost = v.refresh_cost
        else
            break
        end
    end

    return refresh_cost
end

function CrossLongMaiWGData:GetDeadDropCfg()
    return self.dead_drop_cfg
end

function CrossLongMaiWGData:GetIsShopStuff(item_id)
    return self.dead_drop_map_cfg[item_id] ~= nil
end



-- 玩法
function CrossLongMaiWGData:GetBattleRuleCfg()
    return self.rule_tips_cfg
end

function CrossLongMaiWGData:GetRuleByIndexCfg(index)
    return self.rule_tips_cfg[index] or {}
end









