﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GameRuntimeGUI
{
    string curUseSvnTime = "";
    string newSvnTime = "";
    string curWinExeSvnTime = "";
    string newWinExeSvnTime = "";

    public void OnGUI()
    {
        if (RuntimeGUIMgr.Instance.IsAndroidGM())
            return;

        if (UnityEngine.Time.frameCount % 5 != 0)
        {
            curUseSvnTime = RuntimeGUIMgr.Instance.GetCurUseSVNTime();
            newSvnTime = RuntimeGUIMgr.Instance.GetNewSVNTime();
            curWinExeSvnTime = RuntimeGUIMgr.Instance.GetCurWindowsExeSvnTime();
            newWinExeSvnTime = RuntimeGUIMgr.Instance.GetNewWindowsExeSvnTime();
        } 

        GUILayout.BeginVertical();
        GUILayout.Label(curUseSvnTime, RuntimeGUIStyle.GreenLabel);

        if (curUseSvnTime != newSvnTime)
        {
            GUILayout.Label(newSvnTime, RuntimeGUIStyle.RedLabel);
        }
        if (!string.IsNullOrEmpty(curWinExeSvnTime) && !string.IsNullOrEmpty(newWinExeSvnTime) && curWinExeSvnTime != newWinExeSvnTime)
        {
            GUILayout.Label("PC exe有更新，请关闭游戏更新SVN", RuntimeGUIStyle.RedLabel);
        }
        GUILayout.EndVertical();

        if (RuntimeGUIMgr.isShowGMGui)
        {
            if (GUI.Button(new Rect(600, 30, 60, 23), "GM调试"))
            {
                RuntimeViewMgr.Instance.OpenView(RuntimeViewName.GM);
            }
            if (GUI.Button(new Rect(600 + 60, 30, 60, 23), "工具箱"))
            {
                RuntimeViewMgr.Instance.OpenView(RuntimeViewName.TOOL);
            }
        }
    }
}
