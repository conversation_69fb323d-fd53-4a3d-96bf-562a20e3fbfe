local MEMBER_LIST_SELECT_LIST_HIGHT = 338
local LIST_SELECT_LIST_CELL_HIGHT = 48

-- 初始化成员列表界面
function GuildView:InitMemberView()
	self.member_view = self.node_list.layout_menber_list
	self:CreateMemberList()
	self:RegisterMemberEvent()
	self.member_data_list = nil
	self.member_name = ""
	self.member_index = 0
	--排序字段
	self.sort_member_nature = {
		[1] = {[1] = "level", [2] = "sort_index", [3] = "is_online", [4] = "last_login_time"},
		[2] = {[1] = "sort_index", [2] = "level", [3] = "is_online", [4] = "last_login_time"},
		[3] = {[1] = "capability", [2] = "sort_index", [3] = "is_online", [4] = "last_login_time"},
		[4] = {[1] = "total_gongxian", [2] = "sort_index", [3] = "is_online", [4] = "last_login_time"},
		[5] = {[1] = "is_online", [2] = "sort_index",[3] = "last_login_time", [4] = "capability"},--首次进入默认排序
	}
	self.can_sort = false
	self.is_first_enter = true
	self.node_list.closemember:SetActive(false)
	self.node_list.closemember.button:AddClickListener(BindTool.Bind(self.Close_member, self))
	self.node_list.select_menmber_btn.button:AddClickListener(BindTool.Bind(self.OpenSaiXuanBtn, self))

	self.icon_sort_list = {}
	self.is_up = {}
	for i=1,4 do
		self.is_up[i] = false
		self.node_list["btn_sort_"..i].button:AddClickListener(BindTool.Bind(self.OnclickSortMemberList, self,i))
		self.icon_sort_list[i] = self.node_list["icon_sort_"..i]
	end
	self.sort_index = 3 
	self:SetBtnIcon()
end

function GuildView:InItJump()
	self.is_jump = true
end

--帮派操作回调函数
function GuildView:SelectMemberItemCallBack(item)
	if not item or not item:GetData() then return end
    local data = item:GetData()
	-- self:Close_member()
	self.member_index = data[2]
	self.member_name = data[1]
	-- self:OnClikcListMenuItem(self.member_index,item)
	self:OnClikcListMenuItem(self.member_index)

end

function GuildView:DeleteMemberView()
	if self.member_list then
		self.member_list:DeleteMe()
		self.member_list = nil
	end

	if self.btnmember_list then
		self.btnmember_list:DeleteMe()
		self.btnmember_list = nil
	end
	self.item = nil
	self.sort_member_nature = nil
	self.can_sort = nil
	self.is_up = nil
	self.icon_sort_list = nil
	self.sort_index = nil
	self.is_first_enter = nil
end

function GuildView:Close_member()
	self.node_list.closemember:SetActive(false)
end

function GuildView:CloseView_member()
	if self.node_list.closemember ~= nil and self.node_list.closemember:GetActive() then
		self:Close_member()
	end
end

-- 创建成员列表控件
function GuildView:CreateMemberList()
	if nil == self.member_list then
		self.member_list = AsyncListView.New(GuildMemberItem, self.node_list["ph_member_list"])
		self.member_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectMemberListItemHandler, self))
		self.member_list:SetDefaultSelectIndex(nil)
	end
	if nil == self.btnmember_list then
		self.btnmember_list = AsyncListView.New(LBMemberListRender, self.node_list["ph_btn_list"])
		self.btnmember_list:SetSelectCallBack(BindTool.Bind1(self.SelectMemberItemCallBack, self))
		self.btnmember_list:SetDefaultSelectIndex(nil)
	end
end

-- 创建点击成员列表时弹出菜单
function GuildView:OpenMemberMenuView(item)
	self.node_list.closemember:SetActive(true)
	if nil == item or nil ~= self.list_menu_view then
		return
	end

	local post_list = GuildDataConst.GUILD_POST_AUTHORITY_LIST
	self:FlushMemberList(item)

end

--刷新列表
function GuildView:FlushMemberList(item)
    if item.data.uid == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
        self.member_data_list = Language.Guild.GuildMemberRobot
    else
        if RoleWGData.Instance.role_vo.guild_post == GUILD_POST.TUANGZHANG or RoleWGData.Instance.role_vo.guild_post == GUILD_POST.JiaMengZhu then --成员在线
            if item.data.is_online == 1 then
                if item.data.post == GUILD_POST.FU_TUANGZHANG then
                    if RoleWGData.Instance.role_vo.guild_post == GUILD_POST.JiaMengZhu then
                        self.member_data_list = Language.Guild.GuildMemberName1_1_1
                    else
                        self.member_data_list = Language.Guild.GuildMemberName1_1
                    end
                    
                elseif  item.data.post == GUILD_POST.ZHANG_LAO then
                    self.member_data_list = Language.Guild.GuildMemberName1_3
                elseif item.data.post == GUILD_POST.JINGYING then
                    self.member_data_list = Language.Guild.GuildMemberName1_4
                elseif item.data.post == GUILD_POST.HUFA then
                    self.member_data_list = Language.Guild.GuildMemberName1_5
                else
                    self.member_data_list = Language.Guild.GuildMemberName
                end
            else
                if item.data.post == GUILD_POST.FU_TUANGZHANG then
                    if RoleWGData.Instance.role_vo.guild_post == GUILD_POST.JiaMengZhu then
                        self.member_data_list = Language.Guild.GuildMemberName2_1_1
                    else
                        self.member_data_list = Language.Guild.GuildMemberName2_1
                    end
                    --self.member_data_list = Language.Guild.GuildMemberName2_1
                elseif  item.data.post == GUILD_POST.ZHANG_LAO then
                    self.member_data_list = Language.Guild.GuildMemberName2_3
                elseif item.data.post == GUILD_POST.JINGYING then
                    self.member_data_list = Language.Guild.GuildMemberName2_4
                elseif item.data.post == GUILD_POST.HUFA then
                    self.member_data_list = Language.Guild.GuildMemberName2_5
                elseif item.data.post == GUILD_POST.TUANGZHANG and RoleWGData.Instance.role_vo.guild_post == GUILD_POST.JiaMengZhu then
                    self.member_data_list = Language.Guild.GuildMemberName2_6
                else
                    self.member_data_list = Language.Guild.GuildMemberName2
                end
            end
        elseif RoleWGData.Instance.role_vo.guild_post == GUILD_POST.FU_TUANGZHANG then --成员在线
            
            if item.data.is_online == 1 then
                if item.data.post == GUILD_POST.FU_TUANGZHANG or item.data.post == GUILD_POST.TUANGZHANG then
                    self.member_data_list = Language.Guild.GuildMemberNameF1_1
                elseif  item.data.post == GUILD_POST.ZHANG_LAO then
                    self.member_data_list = Language.Guild.GuildMemberNameF1_3
                elseif item.data.post == GUILD_POST.JINGYING then
                    self.member_data_list = Language.Guild.GuildMemberNameF1_4
                elseif item.data.post == GUILD_POST.HUFA then
                    self.member_data_list = Language.Guild.GuildMemberNameF1_5
                else
                    self.member_data_list = Language.Guild.GuildMemberName
                end
            else
                if item.data.post == GUILD_POST.FU_TUANGZHANG or item.data.post == GUILD_POST.TUANGZHANG then
                    self.member_data_list = Language.Guild.GuildMemberNameF2_1
                elseif  item.data.post == GUILD_POST.ZHANG_LAO then
                    self.member_data_list = Language.Guild.GuildMemberNameF2_3
                elseif item.data.post == GUILD_POST.JINGYING then
                    self.member_data_list = Language.Guild.GuildMemberNameF2_4
                elseif item.data.post == GUILD_POST.HUFA then
                    self.member_data_list = Language.Guild.GuildMemberNameF2_5
                else
                    self.member_data_list = Language.Guild.GuildMemberNameF2
                end          
            end
        else
            if item.data.is_online == 1 then
                self.member_data_list = Language.Guild.GuildMemberName3
            else
                self.member_data_list = Language.Guild.GuildMemberName4
            end
        end
    end
	local count = #self.member_data_list
	self:SetMmberBtnSize(count)

	self.item = item
	self.btnmember_list:SetDataList(self.member_data_list,3)
	self.btnmember_list:JumpToTop()

end
-- 设置view大小尺寸
function GuildView:SetMmberBtnSize(count)
	local hight_1 = count * 51
	local hight = hight_1 
	self.node_list["img_bg"].rect.sizeDelta = Vector2(206, hight + 32)
	self.node_list["ph_btn_list"].rect.sizeDelta = Vector2(170, hight)

	-- self.node_list["img_bg"].transform.anchoredPosition = Vector2(360, -82)
	-- self.node_list["ph_btn_list"].transform.anchoredPosition = Vector2(359, -88)
end

-- 点击某个成员事件
function GuildView:OnSelectMemberListItemHandler(item)
	if nil == item then
		return
	end
	if nil == item.data then
		return
    end

	GuildWGData.Instance:SetGuildMemberSelectIndex(item:GetIndex())
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if nil == role_vo then
		return
	end
	if item.data.uid == role_vo.role_id then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.NotOperateSelf)
		return
	end
	self:OpenMemberMenuView(item)
end

-- 点击菜单按钮
function GuildView:OnClikcListMenuItem(index)
	local btn_text = GuildDataConst.GUILD_AUTHORITY_LIST[index]
	if not self.item or not self.item.data then return end
	local society_ctrl = SocietyWGCtrl.Instance
	self:Close_member()
    if btn_text == Language.Guild.LookInfo then
        if self.item.data.uid == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
            --机器人操作
            local data = GuildWGData.Instance:GetRobotBrowseCfgByRoleName(self.item.data.role_name)
            BrowseWGCtrl.Instance:BrowseRobotInfo(data)
            return
        end
		BrowseWGCtrl.Instance:OpenWithUid(self.item.data.uid,nil,nil,nil,1)
	elseif btn_text == Language.Guild.Private then
		-- 必须是好友才能私聊
		if nil == SocietyWGData.Instance:FindFriend(self.item.data.uid) then		-- 拒绝陌生私聊
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.IsFindFriend)
			return
		end
        GuildWGData.Instance:SetSendID(self.item.data.uid)
        local user_info = {}
		user_info.user_id = self.item.data.uid
		user_info.username = self.item.data.role_name
		user_info.sex = self.item.data.sex
		user_info.camp = 0
		user_info.prof = self.item.data.prof
		user_info.avatar_key_small = self.item.data.avatar_key_small
        user_info.level = self.item.data.level
		ChatWGCtrl.Instance:AddPrivateRequset(self.item.data.role_name, nil,self.item.data.uid, user_info)
    elseif btn_text == Language.Guild.AddFriend then
        if self.item.data.uid == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
            return
        end
		SocietyWGCtrl.Instance:IAddFriend(self.item.data.uid)
	elseif btn_text == Language.Guild.SendMail then
		SocietyWGCtrl.Instance:IOpenSendMail(self.item.data.role_name)
    elseif btn_text == Language.Guild.TeamInvite then
        if self.item.data.uid == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
            return
        end
		SocietyWGCtrl.Instance:ITeamInvite(self.item.data.uid,TEAM_INVITE_TYPE.GUILD)
    elseif btn_text == Language.Guild.SendFlowers then
        if self.item.data.uid == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
            return
        end
		FlowerWGCtrl.Instance:OpenSendFlowerView(self.item.data.uid, self.item.data.role_name,self.item.data.sex,self.item.data.prof)
	elseif btn_text == Language.Guild.Kickout then
		local pop_alert = self:GetPopAlert()
		pop_alert:Open()
		pop_alert:SetOkFunc(BindTool.Bind2(self.OnKickoutMemberHandler, self, self.item.data.uid))
		pop_alert:SetLableString(string.format(Language.Guild.KickoutMemberTip, self.item.data.role_name))
	elseif btn_text == Language.Guild.AppointPost then
		GuildWGCtrl.Instance:OpenAppointView(self.item.data)
	elseif btn_text == Language.Guild.TransferMengZhu then
		local pop_alert = self:GetPopAlert()
		pop_alert:Open()
		pop_alert:SetOkFunc(BindTool.Bind2(self.OnConfirmMengZhuHandler, self, self.item.data.uid))
		pop_alert:SetLableString(string.format(Language.Guild.ConfirmTransferMengZhuTip, self.item.data.role_name))
	elseif btn_text == Language.Guild.GuildZhangLao then
		GuildWGCtrl.Instance:SendGuildAppointReq(GuildDataConst.GUILDVO.guild_id, self.item.data.uid, GUILD_POST.ZHANG_LAO)
	elseif btn_text == Language.Guild.GuildFuMeng then
		GuildWGCtrl.Instance:SendGuildAppointReq(GuildDataConst.GUILDVO.guild_id, self.item.data.uid, GUILD_POST.FU_TUANGZHANG)
	elseif btn_text == Language.Guild.GuildChengYuan then
		GuildWGCtrl.Instance:SendGuildAppointReq(GuildDataConst.GUILDVO.guild_id, self.item.data.uid, GUILD_POST.CHENG_YUAN)
	elseif btn_text == Language.Guild.GuildHuFa then
		GuildWGCtrl.Instance:SendGuildAppointReq(GuildDataConst.GUILDVO.guild_id, self.item.data.uid, GUILD_POST.HUFA)
	elseif btn_text == Language.Guild.GuildJingYing then
		GuildWGCtrl.Instance:SendGuildAppointReq(GuildDataConst.GUILDVO.guild_id, self.item.data.uid, GUILD_POST.JINGYING)
	end
end

-- 注册成员界面事件
function GuildView:RegisterMemberEvent()

end

-- 踢出仙盟二次确认
function GuildView:OnKickoutMemberHandler(uid)
	if nil ~= uid then
		GuildWGCtrl.Instance:SendKickoutGuildReq(GuildDataConst.GUILDVO.guild_id, 1, {uid})
	end
end

-- 转让盟主二次确认
function GuildView:OnConfirmMengZhuHandler(uid)
	if nil ~= uid then
		GuildWGCtrl.Instance:SendGuildAppointReq(GuildDataConst.GUILDVO.guild_id, uid, GuildDataConst.GUILD_POST_ENUM[GuildDataConst.GUILD_POST_LIST[1]])
	end
end

-- 刷新成员列表数据源
function GuildView:FlushMemberListDatasource()
	local guildvo = GuildDataConst.GUILDVO
	if self.node_list.select_menmber_btn then
--		print_error(RoleWGData.Instance.role_vo.guild_post)
		self.node_list.select_menmber_btn:SetActive(RoleWGData.Instance.role_vo.guild_post == GUILD_POST.TUANGZHANG or RoleWGData.Instance.role_vo.guild_post == GUILD_POST.JiaMengZhu )	
	end
	local m_list = GuildDataConst.GUILD_MEMBER_LIST
	self.member_datasource = {}
	for i = 1, m_list.count do
        local item = m_list.list[i]
		local datasource = {uid = item.uid, role_name = item.role_name, level = item.level, sex = item.sex, prof = item.prof,
			post = item.post, vip_type = item.vip_type, vip_level = item.vip_level, is_online = item.is_online, join_time = item.join_time,
			last_login_time = item.last_login_time, gongxian = item.gongxian, total_gongxian = item.total_gongxian, capability = item.capability, photframe = item.photframe,
			sort_index = GuildDataConst.GUILD_POST_SORTINDEX_LIST[item.post], avatar_key_small = item.avatar_key_small}
		table.insert(self.member_datasource, datasource)
	end
	if nil ~= self.member_list then
		if self.can_sort then
			if self.is_up[self.sort_index] then
				local sort_member = self.sort_member_nature[self.sort_index]
				table.sort(self.member_datasource, SortTools.KeyUpperSorters(sort_member[1],sort_member[2],sort_member[3],sort_member[4]))
			else
				--战力排序做特殊处理 用默认排序
				if self.sort_index == 3 then
					table.sort(self.member_datasource, SortTools.KeyUpperSorters(self.sort_member_nature[5][1],self.sort_member_nature[5][2],self.sort_member_nature[5][4]))
				else
					table.sort(self.member_datasource, SortTools.KeyGuildMemberSorters(self.sort_member_nature[self.sort_index][1],self.sort_member_nature[self.sort_index][2],self.sort_member_nature[self.sort_index][3],self.sort_member_nature[self.sort_index][4]))
				end 
			end
		else
			-- table.sort(self.member_datasource, BindTool.Bind1(self.SortMemberList, self))
			--默认排序
			table.sort(self.member_datasource, SortTools.KeyUpperSorters(self.sort_member_nature[5][1],self.sort_member_nature[5][2],self.sort_member_nature[5][4]))
		end
		if self.is_jump then
			self.member_list:SetDataList(self.member_datasource,0)
			self.is_jump = false
		else
			self.member_list:SetDataList(self.member_datasource,3)
		end
		self.member_list:CancelSelect()
		-- if self.is_first_enter then
		-- 	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
		-- 	for k,v in pairs(self.member_datasource) do
		-- 		if role_id == v.uid then
		-- 			self:SetJumpLocal(k)
		-- 			break
		-- 		end
		-- 	end
		-- 	self.is_first_enter = false
		-- end
	end
	GuildWGCtrl.Instance:FlushSelectLimitPanel()
end
--一键清理
function GuildView:OpenSaiXuanBtn()
	 GuildWGCtrl.Instance:OpenSelectLimitPanel()
end
-- 排序成员列表图标
function GuildView:SetJumpLocal(index)
	if index <= 5 then
		self:SelectNowIndex11(index)
	else
		local huadong_xishu = 1 / (#self.member_datasource - 5)
		local huaodng = (index - 5) * huadong_xishu
		self.node_list.ph_member_list.scroller:ReloadData(huaodng)
		self:SelectNowIndex11(index)
	end

end

function GuildView:SelectNowIndex11(default_index)
  self.member_list:SelectIndex(default_index)
end

-- 排序成员列表图标
function GuildView:SetBtnIcon()
	-- for i=1,4 do
	if self.sort_index then
		for i=1,4 do
			self.icon_sort_list[i]:SetActive(i == self.sort_index)
		end
		-- local icon_name = self.is_up[self.sort_index] and "arrow_14" or "arrow_13"
		-- local asset_name,bundle_name = ResPath.GetCommonOthers(icon_name)
		-- 	self.icon_sort_list[self.sort_index].image:LoadSprite(asset_name,bundle_name,function ()
		-- 	self.icon_sort_list[self.sort_index].image:SetNativeSize()
		-- end)
		local icon_pos = self.is_up[self.sort_index] and Vector3(0, 0, 0) or Vector3(180, 0, 0)
		self.icon_sort_list[self.sort_index].transform.rotation = icon_pos
	else
		for i=1,4 do
			self.icon_sort_list[i]:SetActive(false)
		end
	end
	-- end
end
-- 点击排序
function GuildView:OnclickSortMemberList(sort_index)
	for i=1,4 do
		if i == sort_index then
			self.is_up[i] = not self.is_up[i]
		else
			self.is_up[i] = true
		end
	end
	self.can_sort = true
	self.sort_index = sort_index
	self:SetBtnIcon()
	--排序索引优先级别
	self:FlushMemberListDatasource()
end
-- 排序成员列表
function GuildView:SortMemberList(a, b)
	local a_post = GuildDataConst.GUILD_POST_AUTHORITY_LIST[a.post]
	local b_post = GuildDataConst.GUILD_POST_AUTHORITY_LIST[b.post]

	if nil == a_post or nil == b_post then
		return false
	end

	if a.is_online == b.is_online then
		if a.is_online == 0 then
			return a.last_login_time > b.last_login_time
		else
			if a_post.post_index == b_post.post_index then
				return a.capability > b.capability
			else
				return a_post.post_index < b_post.post_index
			end
		end
	else
		return a.is_online > b.is_online
	end
end

-- 刷新成员界面数据
function GuildView:OnFlushMember()
	self:FlushMemberListDatasource()
end

-- 销毁成员菜单界面
function GuildView:DestroyMemberMenuView()
	if nil ~= self.list_menu_view then
		if true == self.list_menu_view:IsPopup() then
			self.list_menu_view:Close()
		end
		self.list_menu_view:DeleteMe()
		self.list_menu_view = nil
	end
end



----------------CKCaoZuoListRender-------------------
LBMemberListRender = LBMemberListRender or BaseClass(BaseRender)

function LBMemberListRender:__init()

end

function LBMemberListRender:OnFlush()
	if self.data == nil then return end
	self.node_list.lbl_caozuo_name.text.text = self.data[1]
	if self.data[1] == Language.Guild.GuildShenQing then
		self.node_list.red_remind:SetActive(GuildWGData.Instance:GetIsHasGuildSetting())
	end
end





-----------------------------------------------------------------------------------------------
GuildMemberListSelectPanel = GuildMemberListSelectPanel or BaseClass(SafeBaseView)
--成员清理列表
function GuildMemberListSelectPanel:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
						{vector2 = Vector2(0, 0), sizeDelta = Vector2(814, 522)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_menber_list_select")
end

function GuildMemberListSelectPanel:LoadCallBack()
	local baseInfo = GuildWGData.Instance:GetMenmberSelectLimitCfg()
	self.node_list.title_view_name.text.text = Language.Guild.SelectDeleteMenmber
	XUI.AddClickEventListener(self.node_list.clear_btn, BindTool.Bind(self.ClearMenmber, self)) 
	XUI.AddClickEventListener(self.node_list.btn_Close, BindTool.Bind(self.BtnClose, self)) 
	for i=1,3 do
		XUI.AddClickEventListener(self.node_list["layout_select_limit_"..i], BindTool.Bind(self.SelectLimitEvent, self,i))
		XUI.AddClickEventListener(self.node_list["close_list_"..i], BindTool.Bind(self.ClostListSelect, self,i))
		self.node_list.img_branch_line_flag_down_1:SetActive(false)
	end
	self.select_limit_list_1 = AsyncListView.New(SelectMenmberListRender,self.node_list.btn_select_list_1)
	self.select_limit_list_1:SetSelectCallBack(BindTool.Bind1(self.SelectLimitBtnCallBack, self))
	self.select_limit_list_2 = AsyncListView.New(SelectMenmberListRender,self.node_list.btn_select_list_2)
	self.select_limit_list_2:SetSelectCallBack(BindTool.Bind1(self.SelectLimitBtnCallBack, self))
	self.select_limit_list_3 = AsyncListView.New(SelectMenmberListRender,self.node_list.btn_select_list_3)
	self.select_limit_list_3:SetSelectCallBack(BindTool.Bind1(self.SelectLimitBtnCallBack, self))

	local m_list = GuildDataConst.GUILD_MEMBER_LIST
	self.member_datasource = {}
	for i = 1, m_list.count do
		local item = m_list.list[i]
		local datasource = {uid = item.uid, role_name = item.role_name, level = item.level, sex = item.sex, prof = item.prof,
			post = item.post, vip_type = item.vip_type, vip_level = item.vip_level, is_online = item.is_online, join_time = item.join_time,
			last_login_time = item.last_login_time, gongxian = item.gongxian, total_gongxian = item.total_gongxian, capability = item.capability, photframe = item.photframe,
			sort_index = GuildDataConst.GUILD_POST_SORTINDEX_LIST[item.post],chongzhi_num = item.chongzhi_num}
			if item.post ~= GUILD_POST.TUANGZHANG and item.post ~= GUILD_POST.JiaMengZhu then
				table.insert( self.member_datasource, datasource)
			end
	end
	table.sort( self.member_datasource, SortTools.KeyLowerSorter("last_login_time"))
	table.sort( self.member_datasource, SortTools.KeyUpperSorters("sort_index","level","capability"))

	if nil == self.guild_menmber_list_view then
		self.guild_menmber_list_view = AsyncListView.New(MenmberListRender,self.node_list.ph_member_list)
	end
end

function GuildMemberListSelectPanel:ShowIndexCallBack()
	self:BtnClose()
	local baseInfo = GuildWGData.Instance:GetMenmberSelectLimitCfg()
	self.node_list["lbl_scene_line_name_1"].text.text = baseInfo[1].level
	self.node_list["lbl_scene_line_name_2"].text.text = baseInfo[1].capability_show
	self.node_list["lbl_scene_line_name_3"].text.text = baseInfo[1].time_show
	GuildWGData.Instance:SetSelectSelectDataLimit(1,baseInfo[1].level)
	GuildWGData.Instance:SetSelectSelectDataLimit(2,baseInfo[1].capability)
    GuildWGData.Instance:SetSelectSelectDataLimit(3,baseInfo[1].time)
    if self.select_limit_list_1:GetDataList() then
        self.select_limit_list_1:SelectIndex(1)
    end
    if self.select_limit_list_2:GetDataList() then
        self.select_limit_list_2:SelectIndex(1)
    end
    if self.select_limit_list_3:GetDataList() then
        self.select_limit_list_3:SelectIndex(1)
    end
	-- for i=1,3 do
	-- 	GuildWGData.Instance:SetSelectSelectDataLimit(i,nil)
	-- end

	GuildWGData.Instance:SetSelectSelectMenmberLimit({})
	self:Flush()
end

function GuildMemberListSelectPanel:SelectLimitBtnCallBack(item)
	local data = item.data
	GuildWGData.Instance:SetSelectSelectDataLimit(data.limit_index,data.limit_value)

	local limit_list = GuildWGData.Instance:GetSelectSelectDataLimit()
	local temp_list = {}
	local level = limit_list[1] or 0
	local capability = limit_list[2] or 0
	local sec = limit_list[3] or 0
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	for k,v in pairs(self.member_datasource) do
		if (v.level <= level or level == 0) and ((server_time - v.last_login_time) >= sec * 3600 or sec == 0) then--and (v.capability <= capability or capability == 0)  then
			table.insert(temp_list,v)
		end
	end

	self.node_list["lbl_scene_line_name_"..data.limit_index].text.text = data.name
	GuildWGData.Instance:SetSelectSelectMenmberLimit(temp_list)
	self:BtnClose()
	self:Flush()
end

function GuildMemberListSelectPanel:ClostListSelect(index)
	self.node_list["close_list_"..index]:SetActive(false)
	self.node_list["img_branch_line_flag_down_"..index]:SetActive(false)
	self.node_list["img_branch_line_flag_up_"..index]:SetActive(true)
end

function GuildMemberListSelectPanel:SelectLimitEvent(index)
	self.node_list.btn_Close:SetActive(true)
	for i=1, 3 do
		self.node_list["close_list_"..i]:SetActive(index == i)
		self.node_list["img_branch_line_flag_down_"..i]:SetActive(index == i)
		self.node_list["img_branch_line_flag_up_"..i]:SetActive(index ~= i)
	end
	local baseInfo = GuildWGData.Instance:GetMenmberSelectLimitCfg()
	local data_list = {}
	if index == 1 then
		for k,v in pairs(baseInfo) do
			local data = {}
			data.limit_value = v.level
			data.limit_index = index
			data.name = v.level
			table.insert(data_list,data)
		end
		self.select_limit_list_1:SetDataList(data_list)
	elseif index == 2 then
		
		for k,v in pairs(baseInfo) do
			local data = {}
			data.limit_value = v.capability
			data.limit_index = index
			data.name = v.capability_show
			table.insert(data_list,data)
		end
		self.select_limit_list_2:SetDataList(data_list)
	elseif index == 3 then
		for k,v in pairs(baseInfo) do
			local data = {}
			data.limit_value = v.time
			data.limit_index = index
			data.name = v.time_show
			table.insert(data_list,data)
		end
		self.select_limit_list_3:SetDataList(data_list)
	end
end

function GuildMemberListSelectPanel:BtnClose()
	self.node_list.btn_Close:SetActive(false)
	for i=1, 3 do
		-- self.node_list["btn_select_list_"..i]:SetActive(false)
		self.node_list["img_branch_line_flag_down_"..i]:SetActive(false)
		self.node_list["img_branch_line_flag_up_"..i]:SetActive(true)
		self.node_list["close_list_"..i]:CustomSetActive(false)
	end
end

function GuildMemberListSelectPanel:OnFlush()
	-- if self.node_list["ph_member_list"] then
	-- 	self.node_list["ph_member_list"].scroller:RefreshActiveCellViews()
	-- end
	-- local select_list =  GuildWGData.Instance:GetSelectSelectMenmberLimit()
	-- XUI.SetButtonEnabled(self.node_list.clear_btn, #select_list > 0)
	local limit_list = GuildWGData.Instance:GetSelectSelectDataLimit()
	local temp_list = {}
	local level = limit_list[1] or 0
	local capability = limit_list[2] or 0
	local sec = limit_list[3] or 0
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	--print_error(limit_list)
	for k,v in pairs(self.member_datasource) do
		if (v.level <= level or level == 0) and ((server_time - v.last_login_time) >= sec * 3600 or sec == 0) then--and (v.capability <= capability or capability == 0) then
		--	print_error(limit_list,v.level,v.capability)
			table.insert(temp_list,v)
		end
	end
	self.guild_menmber_list_view:SetDataList(temp_list)
	local show_count = math.ceil(LIST_SELECT_LIST_CELL_HIGHT / MEMBER_LIST_SELECT_LIST_HIGHT) 
	local select_list =  GuildWGData.Instance:GetSelectSelectMenmberLimit()
	XUI.SetButtonEnabled(self.node_list.clear_btn, #select_list > 0)
	self.node_list["no_list"]:SetActive(#temp_list <= 0)
end

function GuildMemberListSelectPanel:ClearMenmber()
	local select_list =  GuildWGData.Instance:GetSelectSelectMenmberLimit()
	if #select_list <= 0 then return end
	local kickout_list = {}
	for k,v in pairs(select_list) do
		table.insert(kickout_list,v.uid)
	end
	local function gotoHecheng()
		GuildWGCtrl.Instance:SendKickoutGuildReq(GuildDataConst.GUILDVO.guild_id, #select_list, kickout_list)
		GuildWGData.Instance:SetSelectSelectMenmberLimit({})
	end
	if nil == self.delete_menmber_alert then
		self.delete_menmber_alert = Alert.New()
	end
	self.delete_menmber_alert:SetOkFunc(gotoHecheng)
	self.delete_menmber_alert:SetLableString(string.format(Language.Guild.SelectDeleteMenmberAlert, select_list[1].role_name))
	self.delete_menmber_alert:Open()
end

function GuildMemberListSelectPanel:CompleteNextTime()
	self:Close()
end

function GuildMemberListSelectPanel:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("GuildAgentPanel") then
		CountDownManager.Instance:RemoveCountDown("GuildAgentPanel")
	end
	if self.select_limit_list_1 then
		self.select_limit_list_1:DeleteMe()
		self.select_limit_list_1 = nil
	end
	if self.select_limit_list_2 then
		self.select_limit_list_2:DeleteMe()
		self.select_limit_list_2 = nil
	end
	if self.select_limit_list_3 then
		self.select_limit_list_3:DeleteMe()
		self.select_limit_list_3 = nil
	end
	if self.guild_menmber_list_view then
		self.guild_menmber_list_view:DeleteMe()
		self.guild_menmber_list_view = nil
	end
	self.member_datasource = nil
	if self.delete_menmber_alert then
		self.delete_menmber_alert:DeleteMe()
		self.delete_menmber_alert = nil
	end
end


MenmberListRender = MenmberListRender or BaseClass(BaseRender)

function MenmberListRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.check_mark, BindTool.Bind(self.OnClickCheckMark, self)) 
end

function MenmberListRender:OnClickCheckMark()
	local vas = self.node_list.mark_select:GetActive()
	self.node_list.mark_select:SetActive(not vas)
	GuildWGData.Instance:AddOrSubMenmber(not vas,self.data)
	GuildWGCtrl.Instance:OpenSelectLimitPanel()
end

function MenmberListRender:ReleaseCallBack()
end

function MenmberListRender:OnFlush()
	if nil == self.data then return end
	local num = self.index % 2
	if(num == 0) then
		self.node_list["img9_bg"].image:LoadSprite(ResPath.GetCommonImages("a2_zudui_lbdi"))
	else
		self.node_list["img9_bg"].image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_5"))
	end

	self.node_list.lbl_name.text.text = self.data.role_name
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.fly_image:SetActive(is_vis)
	self.node_list.level:SetActive(not is_vis)
	if is_vis then
		self.node_list.lbl_level.text.text = role_level
	else
		self.node_list.level.text.text = role_level
	end
	--self.node_list.lbl_zhanli.text.text = CommonDataManager.ConverExpByThousand(self.data.capability)
	if self.data.is_online == 0 then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local hour_num = math.floor((server_time - self.data.last_login_time)/3600)
		if hour_num < 1 then
			 local min = math.floor((server_time - self.data.last_login_time)/60)
			self.node_list.lbl_zonggongxian.text.text = min..Language.Common.Minite
		elseif hour_num >= 24 then
			local day = math.floor(hour_num/24)
			self.node_list.lbl_zonggongxian.text.text = day ..Language.Common.Day
		else
			self.node_list.lbl_zonggongxian.text.text = hour_num ..Language.Common.Hour
		end
	else
		self.node_list.lbl_zonggongxian.text.text = Language.Common.OnLine
	end
	local post_list = GuildDataConst.GUILD_POST_AUTHORITY_LIST
	self.node_list.lbl_post.text.text = post_list[self.data.post].post
	local is_select =  GuildWGData.Instance:IsSelectMenmber(self.data.uid)
	self.node_list.mark_select:SetActive(is_select)
end


SelectMenmberListRender = SelectMenmberListRender or BaseClass(BaseRender)
function SelectMenmberListRender:__init()
	-- body
end

function SelectMenmberListRender:LoadCallBack()
	
end

function SelectMenmberListRender:ReleaseCallBack()
	-- body
end

function SelectMenmberListRender:OnFlush()
	if nil == self.data then return end
	self.node_list.Name.text.text = self.data.name
end

function SelectMenmberListRender:OnSelectChange(is_select)
	self.node_list.select_image:SetActive(is_select)
end