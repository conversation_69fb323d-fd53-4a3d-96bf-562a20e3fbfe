KuafuHonorhallShop = KuafuHonorhallShop or BaseClass(SafeBaseView)

function KuafuHonorhallShop:__init(view_name)
	-- self.view_name = "KuafuHonorhallShop"
	-- self.view_layer = UiLayer.Pop
	self.full_screen = true											-- 是否是全屏界面
	self.is_need_depth = true										-- 是否需要开启景深
	self.is_safe_area_adapter = true								-- 是否刘海屏适配
	-- self.view_style = ViewStyle.Full
	self.shop_type = 9
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/kuafu_honorhalls_ui_prefab", "layout_honorhalls_shop")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	-- self:SetMaskBg(true, true)
end

function KuafuHonorhallShop:LoadCallBack()
	self:InitPanel()
	-- self:InitListener()
end

function KuafuHonorhallShop:ReleaseCallBack()
	if self.grid_list then
		self.grid_list:DeleteMe()
		self.grid_list = nil
	end
	
    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self.open_is_preview = nil
	KuafuHonorhallShop:CleanActTimeDown()
end

function KuafuHonorhallShop:SetIsOpenPreview(is_preview)
	self.open_is_preview = is_preview
end

function KuafuHonorhallShop:OpenCallBack()
	ShopWGCtrl.Instance:SendReqShopItemInfo(self.shop_type)
end

function KuafuHonorhallShop:OnFlush(param_t)
	self:RefreshView()
end

function KuafuHonorhallShop:InitPanel()
	self.node_list.title_view_name.text.text = Language.Shop.ShenMiShangDian

	self.grid_list = AsyncBaseGrid.New()
	self.grid_list:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list.item_list,
			assetBundle = "uis/view/kuafu_honorhalls_ui_prefab", assetName = "cel_shop_item", itemRender = HonorhallShopItemRender})
	self.grid_list:SetStartZeroIndex(false)

	self.money_bar = MoneyBar.New()
	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	local other_cfg = KuafuHonorhallWGData.Instance:GetOtherCfg()
	self.node_list["shop_desc"].text.text = other_cfg.shop_desc

	local bundle, asset = ResPath.GetRawImagesPNG("a3_jy_bg")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function KuafuHonorhallShop:InitListener()
	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.Close, self))
end

function KuafuHonorhallShop:RefreshView()
	local data_list = ShopWGData.Instance:GetItemDataList(self.shop_type)
	local temp_list = {}

	if data_list then
		for i, v in ipairs(data_list) do
			local info = {}
			info.shop_data = v
			info.open_is_preview = self.open_is_preview

			table.insert(temp_list, info)
		end
	end

	self.grid_list:SetDataList(temp_list, 0)
	self.node_list.act_time_down:CustomSetActive(not self.open_is_preview)
	local act = ACTIVITY_TYPE.KF_HONORHALLS or nil
	local act_status = ActivityWGData.Instance:GetActivityStatuByType(act)

	if act_status and act_status.status == ACTIVITY_STATUS.OPEN then
		self:FlushActTimeCountDown(act_status.next_time)
	end
end


--- 活动结束倒计时
function KuafuHonorhallShop:CleanActTimeDown()
	if CountDownManager.Instance:HasCountDown("hit_hamster_act_time_down") then
		CountDownManager.Instance:RemoveCountDown("hit_hamster_act_time_down")
	end
end

function KuafuHonorhallShop:FlushActTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self:CleanActTimeDown()
		self.node_list["act_time_down"].text.text = string.format(Language.OpertionAcitvity.WateringFlowers.ActivityRemainTime, TimeUtil.FormatSecondDHM8(invalid_time - TimeWGCtrl.Instance:GetServerTime()))
		CountDownManager.Instance:AddCountDown("hit_hamster_act_time_down", BindTool.Bind1(self.UpdateActCountDown, self), BindTool.Bind1(self.OnActComplete, self), invalid_time, nil, 1)
	end
end

function KuafuHonorhallShop:UpdateActCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list["act_time_down"].text.text = string.format(Language.OpertionAcitvity.WateringFlowers.ActivityRemainTime, TimeUtil.FormatSecondDHM8(valid_time))
	end
end

function KuafuHonorhallShop:OnActComplete()
    self.node_list["act_time_down"].text.text = ""
    self:Close()
end

------------------------------------------------------------------------------

HonorhallShopItemRender = HonorhallShopItemRender or BaseClass(BaseRender)
function HonorhallShopItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function HonorhallShopItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["ph_item"])
	self.item_cell:SetClickCallBack(BindTool.Bind(self.OnClick, self))
	self.item_cell:SetIsShowTips(false)
end

function HonorhallShopItemRender:OnFlush()
	if nil == self.data then
		return
	end

	local shop_data = self.data.shop_data
	-- info.shop_data = v
	-- info.open_is_preview = self.open_is_preview
	local item_cfg = ItemWGData.Instance:GetItemConfig(shop_data.itemid)
	if nil == item_cfg then
		print_error("没有配置 " , shop_data.itemid)
		return
	end

	self.item_cell:SetData({item_id = shop_data.itemid, is_bind = shop_data.is_bind})
	self.item_cell:SetDefaultEff(true)
	self.item_cell:SetBindIconVisible(false)
	self.node_list.lbl_item_name.text.text = item_cfg.name

	local is_vip_limit, vip_limit_str, is_can_buy = ShopWGData.Instance:IsVipLimit(shop_data.seq)
	self.node_list["img_vip"]:SetActive(is_vip_limit)
	if is_vip_limit and vip_limit_str then
		self.node_list["text_vip"].text.text = vip_limit_str
	end

	self.node_list["img_shop_icon"]:SetActive(0 ~= shop_data.jiaobiao_icon)
	self.node_list.zhekou.text.text = shop_data.jiaobiao_text or ""

	---[[ 活动期间打折
	local discount = ShopWGData.Instance:GetActDiscountByItemSeq(shop_data.seq)
	if discount > 0 then
		self.node_list["img_shop_icon"]:SetActive(true)
		self.node_list.zhekou.text.text = string.format(Language.SmallGoal.Zhe, CommonDataManager.GetDaXie(discount))
	end
	--]]
 	
	local list = ShopWGData.Instance:ExplainComposeStr(shop_data.seq)
 	self.item_cell:SetRightBottomTextVisible(false)
	if list[1] then
		self.node_list["text_limit_bug"].text.text = list[1].str
	else
		self.node_list["text_limit_bug"].text.text = Language.Shop.NoLimitBuy
	end

	local is_can_cell, sell_str = ShopWGData.Instance:IsCanCell(shop_data.seq, true)
	is_can_cell = is_can_cell and (not self.data.open_is_preview)
	self.node_list["img_limit"]:SetActive(not is_can_cell)
	local str = self.data.open_is_preview and Language.Shop.HonorhallsShopLimit or Language.Shop.EmptyDes
	self.node_list.text_empty.text.text = str
	self.node_list["img_normal"]:SetActive(is_can_cell)
	self:FlushPrice()

	if not is_can_cell then
		self.node_list["layout_gold_show"]:CustomSetActive(false)
		self.node_list["layout_gold_old_show"]:CustomSetActive(false)
	end
end

function HonorhallShopItemRender:FlushPrice()
	local shop_data = self.data.shop_data
	local bundel, asset = ResPath.GetCommonIcon(Shop_Money[shop_data.price_type].url)
	local scale = Shop_Money[shop_data.price_type].scale
	local vector3 = Vector3(scale, scale, scale)
	local price = shop_data.price

	-- 活动期间打折
	local discount = ShopWGData.Instance:GetActDiscountByItemSeq(shop_data.seq)
	if discount > 0 then
		price = price * discount / 10
	end

	self.node_list["img_money2"].image:LoadSprite(bundel, asset, function ()
		self.node_list["img_money2"].image:SetNativeSize()
		self.node_list["img_money2"].transform.localScale = vector3
	end)
	self.node_list["img_old_money2"].image:LoadSprite(bundel, asset, function ()
		self.node_list["img_old_money2"].image:SetNativeSize()
		self.node_list["img_old_money2"].transform.localScale = vector3
	end)

	self.node_list["text_money2"].text.text = price
	self.node_list["text_old_money2"].text.text = price
	
	if 0 ~= shop_data.old_price or discount > 0 then
		self.node_list["text_money1"].text.text = discount > 0 and shop_data.price or shop_data.old_price
		self.node_list["layout_gold_old_show"]:SetActive(true)
		self.node_list["layout_gold_show"]:SetActive(false)
	else
		self.node_list["layout_gold_show"]:SetActive(true)
		self.node_list["layout_gold_old_show"]:SetActive(false)
	end
end

function HonorhallShopItemRender:OnClick()
	if nil == self.data then
		print_error("当前Item没有数据")
		return
	end

	local shop_data = self.data.shop_data
	BaseRender.OnClick(self)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(shop_data.itemid)
	if not item_cfg then
		print_error("the item don't exist:::", shop_data.itemid)
		return
	end

	local check_cond, back_str = ShopWGData.Instance:CheckItemCondition(shop_data.seq)
	local can_cell = true
	if check_cond then
		can_cell = ShopWGData.Instance:IsCanCell(shop_data.seq)
	end

	can_cell = can_cell and (not self.data.open_is_preview)
	local is_vip_limit = ShopWGData.Instance:CheckConditionState(shop_data.seq, 1, ConditionManager.Check_Type.Type103)
 	if not can_cell or (not check_cond and not is_vip_limit) then
 		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(shop_data.itemid)
 		if big_type == GameEnum.ITEM_BIGTYPE_EXPENSE then
 			TipWGCtrl.Instance:OpenItem({item_id = shop_data.itemid}, ItemTip.FROM_EQUIPBROWSE, nil)
 		else
 			local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(shop_data.seq)
			TipWGCtrl.Instance:OpenItem({item_id = shop_data.itemid, shop_type = shop_cfg.shop_type}, ItemTip.SHOP_BUY, nil)
		end
 		return
 	end

 	if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and not ItemWGData.GetIsXiaogGui(shop_data.itemid) then
 		local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(shop_data.seq)

 		local special_buy_info = {}
 		special_buy_info.buy_count = 1
 		special_buy_info.max_buy_count = ShopWGData.Instance:GetMaxBuyNum(shop_data.seq)
		special_buy_info.price = shop_cfg.price
		special_buy_info.price_type = shop_cfg.price_type
		special_buy_info.itemid = shop_data.itemid

		local buy_func = function (buy_count)
			local function ok_func()
				local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(shop_data.seq)
					--你策划要求仙玉>=100就弹多一个二次确认框
				-- local buy_count = self.base_tips:GetBuyCount()
				if buy_count * shop_cfg.price >= 100 and shop_cfg.price_type == Shop_Money_Type.Type1 then
					local name_str = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
					local item_id = item_cfg.id
					TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat1, shop_cfg.price * buy_count, name_str, buy_count), function ()
						ShopWGCtrl.Instance:SendShopBuy(item_id, buy_count, 0, 0, shop_data.seq)
					end)
				else
					ShopWGCtrl.Instance:SendShopBuy(item_cfg.id, buy_count, 0, 0, shop_data.seq)
				end
			end
			if not ShopWGData.Instance:CheckBangYuToBuy(shop_data.seq, buy_count) then
				TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
			else
				ok_func()
			end
		end

 		TipWGCtrl.Instance:OpenItem({item_id = shop_data.itemid, buy_limit = shop_data.buy_limit,special_buy_info = special_buy_info,special_buy_call_back = buy_func}, ItemTip.FROM_SHOP_EQUIP)
 		return
 	end

	local is_bind_gold = shop_data.consume_type == GameEnum.CONSUME_TYPE_BIND
	local num = ShopWGData.Instance:GetMaxBuyNum(shop_data.seq)
	local def_buy_count = self:GetDefaultBuyCount()
	ShopTip.Instance:SetData(item_cfg, shop_data.consume_type, GameEnum.SHOP, def_buy_count , shop_data.seq, shop_data.buy_limit, num)

	local view = ViewManager.Instance:GetView(GuideModuleName.Shop)
	if view then
		view:ClearItemIndex()
	end
end

function HonorhallShopItemRender:ResetUI()
	self.node_list["img_vip"]:SetActive(false)
	self.node_list["img_empty"]:SetActive(false)
 	self.node_list["layout_gold_show"]:SetActive(false)
 	self.node_list["img_shop_icon"]:SetActive(false)
end

--策划需求:特殊商品类型,购买时默认显示玩家当前最大可购买量
function HonorhallShopItemRender:GetDefaultBuyCount()
	local shop_data = self.data.shop_data
	local count = 1
	local cur_shop_data = ShopWGData.Instance:GetShopCfgSeq(shop_data.seq)
	if cur_shop_data then
		if cur_shop_data.shop_type == SHOP_BIG_TYPE.SHOP_TYPE_6 	--声望商店
		or cur_shop_data.shop_type == SHOP_BIG_TYPE.SHOP_TYPE_9 then--青云商店

			local temp_max_num = ShopWGData.Instance:GetMaxBuyNum(shop_data.seq)
			local my_cur_type_money = 0
			my_cur_type_money = RoleWGData.Instance:GetMoney(cur_shop_data.price_type)
			count = math.floor(my_cur_type_money / cur_shop_data.price)
			if temp_max_num < count then
				count = temp_max_num
			elseif count <= 0 then
				count = 1
			end

		end
	end
	return count
end