---仙盟仓库------
GuildCangKuWGData = GuildCangKuWGData or BaseClass()

function GuildCangKuWGData:__init()
	if GuildCangKuWGData.Instance then
		ErrorLog("[GuildCangKuWGData]:Attempt to create singleton twice!")
	end
	GuildCangKuWGData.Instance = self
	local guild_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto")
	self.exchange_cfg = ListToMap(guild_cfg.exchange,"id")
	self.storage_access_score_cfg = ListToMapList(guild_cfg.storage_access_score,"equip_order","color")
	self.is_admin = nil
	self.open_grid_count = 200
	self.guild_storge_score = 0
	self.bag_item_list = {}
	self.item_list = {}
	self.is_multi_select = false
end

function GuildCangKuWGData:__delete()
	-- print_error("-----------------------------------------------------")
	GuildCangKuWGData.Instance = nil
end

function GuildCangKuWGData:SetOpengridCount(open_grid_count)
	self.open_grid_count = open_grid_count
end

function GuildCangKuWGData:GetOpengridCount()
	return self.open_grid_count
end

function GuildCangKuWGData:SetStorgeScore(guild_storge_score)
	self.guild_storge_score = guild_storge_score
end

function GuildCangKuWGData:GetStorgeScore()
	return self.guild_storge_score
end

function GuildCangKuWGData:SetGuildStorgeList(item_list)
	self.bag_item_list = item_list
	self.item_list = self:ListSort(self.bag_item_list)
end
function GuildCangKuWGData:ListSort(item_list)
	if item_list == nil then
		return
	end
	local item_data_list = {}
	for k,v in pairs(item_list) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		v.order = item_cfg.order
		if not v.param.star_level then
			v.star_level = 0
		else
			v.star_level = v.param.star_level
		end
		if not item_cfg.color then
			v.color = 0
		else
			v.color = item_cfg.color
		end
		table.insert(item_data_list,v)
	end
	table.sort(item_data_list, SortTools.KeyUpperSorters("order","color","star_level") )
	return item_data_list
end
function GuildCangKuWGData:SetGuildStorgeCell(data)
	if data.item_id > 0 then
		self.bag_item_list[data.index] = data
	else
		self.bag_item_list[data.index] = nil
	end
	self.item_list = self:ListSort(self.bag_item_list)
end

function GuildCangKuWGData:GetItemListNum()
	return #self.item_list
end

function GuildCangKuWGData:GetItemList()
	return self.item_list
end

--获得仙盟仓库里的所有物品
--order 0 表示阶数不筛选
--color 0 表示品质不筛选
--star 0 表示星级不筛选
-- 星级查看的  做向上兼容  ：显示X星以上
-- 星级销毁的 做向下兼容：销毁X星以下
function GuildCangKuWGData:GetGuildStorgeItemDataList(is_prof,order,color,star,is_admin)
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	local prof = RoleWGData.Instance:GetRoleProf()
	local sex = mainrolevo.sex
	local data = {}
	local conver_list = self:GetGuildConvertItem()
	if is_admin then
		for k,v in pairs(conver_list) do
			table.insert(data,v.reward_item)
		end
	end

	for k,v in pairs(self.item_list) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		local is_limit_prof = not is_prof or prof == item_cfg.limit_prof or item_cfg.limit_prof == 5
		local is_limit_sex = not is_prof or sex == item_cfg.limit_sex or item_cfg.limit_sex == 2
		local star_level = v.param and v.param.star_level or 0
		local star_flag = false
		local color_flag = false
		local order_flag = false
		if item_cfg ~= nil and is_limit_prof and is_limit_sex then
			if is_admin then
				star_flag = star_level >= star
				color_flag = item_cfg.color == color
				order_flag = item_cfg.order == order
			else
				star_flag = star_level <= star
				color_flag = item_cfg.color <= color
				order_flag = item_cfg.order <= order
			end
			if (not order or 0 == order or order_flag) and
				(not color or 0 == color or color_flag) and
				(not star or 0 == star or star_flag) then
				table.insert(data,v)
			end
		end
		
	end
	return data
end


function GuildCangKuWGData:GetIsAdmin()
	return self.is_admin
end

function GuildCangKuWGData:SetIsAdmin(flag)
	self.is_admin = flag
end

--------------兑换---------------
function GuildCangKuWGData:GetIsConvertItem(item_id)
	for k,v in pairs(self.exchange_cfg) do
		if v.reward_item and v.reward_item.item_id == item_id then
			return v
		end
	end
end

function GuildCangKuWGData:GetGuildConvertItem()
	return self.exchange_cfg
end

function GuildCangKuWGData:GetGuildConvertItemById(id)
	return self.exchange_cfg[id]
end

--获取捐献的背包装备
--order 0 表示阶数不筛选
--color 0 表示品质不筛选
--打开的时候自动给他筛选显示3星以上的装备
function GuildCangKuWGData:GetEquipBagData(order, color_param)
	local color_list = Split(color_param, "#")
	local data = {}
	local bag_data = ItemWGData.Instance:GetBagItemDataList()
	for k,v in pairs(bag_data) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg and item_cfg.sub_type
		and (item_cfg.sub_type >= GameEnum.EQUIP_TYPE_TOUKUI and item_cfg.sub_type <= GameEnum.EQUIP_TYPE_XIANFU) then

			local star_level = v.param and v.param.star_level or 0
			local score = self:GetStorageAccessScore(item_cfg.order,item_cfg.color,star_level)

			for _, color in ipairs(color_list) do
				color = tonumber(color)
				if nil ~= item_cfg and v.is_bind == 0 and 
					(not order or 0 == order or item_cfg.order == order) and
					(star_level >= 3) and
					(not color or 0 == color or item_cfg.color == color) and score > 0 then
					table.insert(data,v)
				end
			end
		end
	end
	
	return data
end

function GuildCangKuWGData:GetStorageAccessScore(order,color,star_level)
	local score = 0
	star_level = star_level or 0
	if self.storage_access_score_cfg[order] and self.storage_access_score_cfg[order][color] then
		local cfg = self.storage_access_score_cfg[order][color][1]
		score = cfg["star"..star_level] or 0
	end
	return score
end

function GuildCangKuWGData:GetIsCanDonate(order, color, star_level)
	local score = self:GetStorageAccessScore(order, color, star_level)
	return score > 0
end

function GuildCangKuWGData:SetIsMultiSelect(flag)
	self.is_multi_select = flag
end

function GuildCangKuWGData:GetIsMultiSelect()
	return self.is_multi_select
end

-- 设置是否需要自动销毁
function GuildCangKuWGData:SetNeedAutoDestory(need)
	PlayerPrefsUtil.SetInt(self:AutoDestoryKey(), need and 0 or 1) 	--0表示需要自动销毁（ 默认勾选自动销毁）
end

function GuildCangKuWGData:GetNeedAutoDestory()
	return PlayerPrefsUtil.GetInt(self:AutoDestoryKey()) == 0
end

function GuildCangKuWGData:AutoDestoryKey()
	local role_uuid = RoleWGData.Instance:GetUUid()
	local role_id = role_uuid.temp_low .. role_uuid.temp_high
	return "GuildCangKuWGData" .. role_id
end