DragonTempleView = DragonTempleView or BaseClass(SafeBaseView)
function DragonTempleView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:SetMaskBg()

	local bundle_name = "uis/view/dragon_temple_ui_prefab"
	self:AddViewResource(0, bundle_name, "layout_dragon_temple_panel_down")
	self:AddViewResource(TabIndex.dragon_temp_longhun, bundle_name, "layout_dragon_temple_longshen") -- 龙神殿--龙神
	self:AddViewResource(TabIndex.dragon_temp_equip, bundle_name, "layout_dragon_temple_equip")   -- 龙神殿--装备
	self:AddViewResource(TabIndex.dragon_temp_hatch, bundle_name, "layout_dragon_temple_hatch")   -- 龙神殿--孵化
	self:AddViewResource(TabIndex.dragon_temp_draw, bundle_name, "layout_dragon_temple_mibao")    -- 龙神殿--秘宝
	self:AddViewResource(TabIndex.dragon_temp_rank, bundle_name, "layout_dragon_temple_rank")     -- 龙神殿--排行
	self:AddViewResource(0, bundle_name, "VerticalTabbar")
	self:AddViewResource(0, bundle_name, "layout_dragon_temple_panel_up")

	self.default_index = TabIndex.dragon_temp_longhun

	self.remind_tab = {
		{ RemindName.DragonTemple_All_LongShen },
		{ RemindName.DragonTemple_Equip },
		{ RemindName.DragonTemple_All_Hatch },
		{ RemindName.DragonTemple_Mibao },
		{ RemindName.DragonTemple_Rank },
	}

	self.tab_sub = {}

	self.raw_bg = {
		[TabIndex.dragon_temp_longhun] = "a2_lsd_bg_0",
		[TabIndex.dragon_temp_equip] = "a2_lsd_bg_0",
		[TabIndex.dragon_temp_hatch] = "a2_lsd_bg_1",
		[TabIndex.dragon_temp_draw] = "a2_lsd_bg_2",
		[TabIndex.dragon_temp_rank] = "a2_lsd_bg_4",
	}
end

function DragonTempleView:__delete()

end

function DragonTempleView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:ReleaseLongShen()
	self:ReleaseEquip()
	self:ReleaseHatch()
	self:ReleaseRank()
	self:ReleaseMiBao()
end

function DragonTempleView:OpenCallBack()

end

function DragonTempleView:CloseCallBack()

end

function DragonTempleView:LoadCallBack()
	if nil == self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("a2_dragon_temple_icon")
		self.tabbar:SetVerTabbarIconPath(ResPath.GetDragonTempleImg)
		self.tabbar:Init(Language.DragonTemple.TabGrop, nil, "uis/view/dragon_temple_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.DragonTempleView, self.tabbar)
	end

	XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnBtnTipClickHnadler, self))
end

function DragonTempleView:LoadIndexCallBack(index)
	if index == TabIndex.dragon_temp_longhun then
		self:LoadIndexCallBackLongShen()
	elseif index == TabIndex.dragon_temp_equip then
		self:LoadIndexCallBackEquip()
	elseif index == TabIndex.dragon_temp_hatch then
		self:LoadIndexCallBackHatch()
	elseif index == TabIndex.dragon_temp_draw then
		self:LoadIndexCallBackMiBao()
	elseif index == TabIndex.dragon_temp_rank then
		self:LoadIndexCallBackRank()
	end
end

function DragonTempleView:ShowIndexCallBack(index)
	if index == TabIndex.dragon_temp_longhun then
		self:ShowIndexCallBackLongShen()
	elseif index == TabIndex.dragon_temp_equip then
		self:ShowIndexCallBackEquip()
	elseif index == TabIndex.dragon_temp_hatch then
		self:ShowIndexCallBackHatch()
	elseif index == TabIndex.dragon_temp_draw then
		self:ShowIndexCallBackMiBao()
	elseif index == TabIndex.dragon_temp_rank then
		self:ShowIndexCallBackRank()
	end

	local bg_res = self.raw_bg[index] or self.raw_bg[TabIndex.dragon_temp_longhun]

	self.node_list["raw_big_bg"].raw_image:LoadSpriteAsync(ResPath.GetRawImagesJPG(bg_res))
end

function DragonTempleView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.dragon_temp_longhun then
				self:OnFlushLongShen(param_t, index)
			elseif index == TabIndex.dragon_temp_equip then
				self:OnFlushEquip(param_t, index)
			elseif index == TabIndex.dragon_temp_hatch then
				self:OnFlushHatch(param_t, index)
			elseif index == TabIndex.dragon_temp_draw then
				self:OnFlushMiBao()
			elseif index == TabIndex.dragon_temp_rank then
				self:OnFlushRank()
			end
		elseif k == "donate_reward" then
			self:FlushRankBomReward()
		elseif k == "flush_rank_longhun" then
			self:FlushRankModel()
		elseif k == "flush_rank_contribute" then
			self:FlushRankBonusPool()
		elseif k == "person_draw" then
			self:FlushMiBaoPersonRecordList()
		elseif k == "server_draw" then
			self:FlushMiBaoServerRecordList()
		elseif k == "hatch_item_change" then
			self:OnHatchItemChangeFlush()
		elseif k == "hatch_gold_change" then
			self:OnHatchGoldChangeFlush()
		end

		self:OnFlushOtherInfo()
	end
end

function DragonTempleView:OnFlushOtherInfo()
	local other_cfg = DragonTempleWGData.Instance:GetOtherCfg()
	local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.longhun_item)
	if not IsEmptyTable(item_cfg) then
		self.node_list["longhun_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	end

	local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
	local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
	local daily_longhun = DragonTempleWGData.Instance:GetDailyLongHunValue()
	local longhun_value = DragonTempleWGData.Instance:GetLongHunValue()
	if cur_level_cfg then
		self.node_list.longhun_get_today.text.text = daily_longhun .. "/" .. cur_level_cfg.daily_max_longhun
	end

	self.node_list.longhun_num.text.text = longhun_value
end

-- 使用特效
function DragonTempleView:PlayUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({
		effect_type = effect_name,
		is_success = true,
		pos = Vector2(0, 0),
		parent_node = self.node_list["play_effect_pos"]
	})
end

function DragonTempleView:OnBtnTipClickHnadler()
	if self:GetShowIndex() == TabIndex.dragon_temp_longhun then
		RuleTip.Instance:SetContent(Language.DragonTemple.LongHunTipsContent, Language.DragonTemple.LongHunTipsTitle)
	elseif self:GetShowIndex() == TabIndex.dragon_temp_equip then
		RuleTip.Instance:SetContent(Language.DragonTemple.EquipTipsContent, Language.DragonTemple.EquipTipsTitle)
	elseif self:GetShowIndex() == TabIndex.dragon_temp_hatch then
		RuleTip.Instance:SetContent(Language.DragonTemple.HatchTipsContent, Language.DragonTemple.HatchTipsTitle)
	elseif self:GetShowIndex() == TabIndex.dragon_temp_draw then
		RuleTip.Instance:SetContent(Language.DragonTemple.DrawTipsContent, Language.DragonTemple.DrawHunTipsTitle)
	elseif self:GetShowIndex() == TabIndex.dragon_temp_rank then
		RuleTip.Instance:SetContent(Language.DragonTemple.RankTipsContent, Language.DragonTemple.RankTipsTitle)
	end
end
