-- 已屏蔽
BaGuaShopSettingView = BaGuaShopSettingView or BaseClass(SafeBaseView)

function BaGuaShopSettingView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(740, 426)})
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_bagua_shop_set")
end

function BaGuaShopSettingView:__delete()
end

function BaGuaShopSettingView:ReleaseCallBack()

end

function BaGuaShopSettingView:LoadCallBack()
	self:SetViewName(Language.TianShen.BaGuaShopSetName)
	self.node_list["tips_btn"].button:AddClickListener(BindTool.Bind(self.OnClickTip,self))
	self.node_list["btn_confirm"].button:AddClickListener(BindTool.Bind(self.Close,self))
	
	for i=1,3 do
		self.node_list["select_btn"..i].button:AddClickListener(BindTool.Bind(self.OnClickSelect,self,i))
	end
end

function BaGuaShopSettingView:OnClickTip()
	RuleTip.Instance:SetContent(Language.TianShen.BaGuaShopSetRule, Language.TianShen.BaGuaShopSetTitle)
end

function BaGuaShopSettingView:ShowIndexCallBack()

end

function BaGuaShopSettingView:OnClickSelect(i)
	TianShenBaGuaWGData.Instance:SelectShopSetRule(i)
	self:Flush()
	TianShenWGCtrl.Instance:BaGuaShopSetChange()
end

function BaGuaShopSettingView:OnFlush()
	local set_data = TianShenBaGuaWGData.Instance:GetShopSetRule()
	for i=1,3 do
		self.node_list["select_img"..i]:SetActive(set_data[i])
	end
end