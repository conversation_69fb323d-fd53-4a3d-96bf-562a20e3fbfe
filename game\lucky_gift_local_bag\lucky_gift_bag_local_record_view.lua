LuckyGiftBagLocalRecordView = LuckyGiftBagLocalRecordView or BaseClass(SafeBaseView)

function LuckyGiftBagLocalRecordView:__init()
    self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "Lucky_gift_bag_local_record_view")
	self:SetMaskBg(true, true)
end

function LuckyGiftBagLocalRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function LuckyGiftBagLocalRecordView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.LuckyGiftBag.RewardListTitle
	if nil == self.record_list then
		self.record_list = AsyncListView.New(LuckyGiftBagLocalRecordRender, self.node_list["record_list"])
	end
end

function LuckyGiftBagLocalRecordView:OnFlush()
    local data_list = LuckyGiftBagLocalWgData.Instance:GetRateRecordList()
    local is_show_list = not IsEmptyTable(data_list)
    if is_show_list then
        self.record_list:SetDataList(data_list)
    end
    self.node_list.record_list:SetActive(is_show_list)
    self.node_list.no_invite:SetActive(not is_show_list)
end

-------------------------------------------------------------------------------------
LuckyGiftBagLocalRecordRender = LuckyGiftBagLocalRecordRender or BaseClass(BaseRender)

function LuckyGiftBagLocalRecordRender:OnFlush()
    if not self.data then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if not item_cfg then
        print_error('物品配置读取不到   item_id:',self.data.item_id, self.data)
        return
    end
    local color = ITEM_COLOR[item_cfg.color]
    --local str = string.format(Language.LuckyGiftBag.Record, self.data.name, ITEM_COLOR[item_cfg.color], item_cfg.name, self.data.num)
    --self.node_list["info"].text.text = str
    local str1 = string.format(Language.SiXiangCall.TxtRecord3, self.data.name)
    local name = string.format(Language.SiXiangCall.TxtRecord1_2, color, item_cfg.name)
    local num = string.format(Language.SiXiangCall.TxtRecord1_3, color, self.data.num or 1)
    self.node_list.desc.text.text = str1
    self.node_list.txt_btn.text.text = name
    self.node_list.num.text.text = num
end