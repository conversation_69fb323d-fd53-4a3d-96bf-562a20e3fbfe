function YanYuGeExchangeShopView:TZSDLoadCallBack()
    if not self.suit_tzsd_grid_list then
        self.suit_tzsd_grid_list = AsyncListView.New(YYGTZSDItemCellRender, self.node_list.suit_tzsd_grid_list)
        self.suit_tzsd_grid_list:SetSelectCallBack(BindTool.Bind(self.OnSelectTZSD<PERSON>ellH<PERSON><PERSON>, self))
    end

    if not self.suit_tzsd_reward_list then
        self.suit_tzsd_reward_list = AsyncListView.New(ItemCell, self.node_list.suit_tzsd_reward_list)
        self.suit_tzsd_reward_list:SetStartZeroIndex(true)
    end

    if not self.tzsd_model then
        self.tzsd_model = OperationActRender.New(self.node_list.tzsd_model_pos)
        self.tzsd_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    self.tzsd_select_suit_data = {}
    XUI.AddClickEventListener(self.node_list.btn_tzsd_buy, BindTool.Bind(self.OnClickTQSDBuy, self))
end

function YanYuGeExchangeShopView:TZSDShowIndexCallBack()

end

function YanYuGeExchangeShopView:TZSDReleaseCallBack()
    if self.suit_tzsd_grid_list then
        self.suit_tzsd_grid_list:DeleteMe()
        self.suit_tzsd_grid_list = nil
    end

    if self.suit_tzsd_reward_list then
        self.suit_tzsd_reward_list:DeleteMe()
        self.suit_tzsd_reward_list = nil
    end

    if self.tzsd_model then
        self.tzsd_model:DeleteMe()
        self.tzsd_model = nil
    end

    self.tzsd_select_suit_data = nil
end

function YanYuGeExchangeShopView:TZSDOnFlush(param_t)
    local show_data_list = YanYuGeWGData.Instance:GetShowTZSDDataList()
    self.suit_tzsd_grid_list:SetDataList(show_data_list)
    local jump_to_index = self.suit_tzsd_grid_list:GetSelectIndex() or 1
    self.suit_tzsd_grid_list:JumpToIndex(jump_to_index)
end

function YanYuGeExchangeShopView:OnSelectTZSDCellHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    local cfg = data.cfg

    self.tzsd_select_suit_data = data
    self.suit_tzsd_reward_list:SetDataList(cfg.reward_item)
    local limit_buy_times = cfg.buy_limit - data.buy_times
    self.node_list.btn_tzsd_buy:CustomSetActive(limit_buy_times > 0)

    if limit_buy_times > 0 then
        if cfg.buy_type == 1 then
            self.node_list.desc_tzsd_buy.tmp.text = RoleWGData.GetPayMoneyStr(cfg.price, cfg.rmb_type, cfg.rmb_seq)
        elseif cfg.buy_type == 2 then
            self.node_list.desc_tzsd_buy.tmp.text = string.format(Language.YanYuGe.ScoreStr, cfg.price)
        end

        self.node_list.desc_tzsd_buy_tip.tmp.text = string.format(Language.YanYuGe.TZSDLimitBuyTimeStr, COLOR3B.L_GREEN, limit_buy_times, cfg.buy_limit)

    else
        self.node_list.desc_tzsd_buy_tip.tmp.text = ""
    end

    self.node_list.flag_sell_out:CustomSetActive(limit_buy_times <= 0)

    local score = YanYuGeWGData.Instance:GetCurScore()
    -- self.node_list.btn_tzsd_buy_remind:CustomSetActive(limit_buy_times > 0 and cfg.buy_type == 2 and score >= cfg.price)

    self:FlushTZSDModel(cfg)
end

function YanYuGeExchangeShopView:FlushTZSDModel(model_cfg)
	local display_data = {}
	display_data.should_ani = true
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
		end
	end

	display_data.render_type = model_cfg.model_show_type -1
	display_data.model_rt_type = ModelRTSCaleType.L

	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rot_list = string.split(model_cfg.rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	display_data.model_adjust_root_local_scale = model_cfg.display_scale
	self.tzsd_model:SetData(display_data)
end

function YanYuGeExchangeShopView:OnClickTQSDBuy()
    if IsEmptyTable(self.tzsd_select_suit_data) then
        return
    end

    local cfg = self.tzsd_select_suit_data.cfg
    local cur_score = YanYuGeWGData.Instance:GetCurScore()

    if cfg.buy_type == 1 then
        RechargeWGCtrl.Instance:Recharge(cfg.price, cfg.rmb_type, cfg.rmb_seq)
        return
    elseif cfg.buy_type == 2 then
        if cur_score < cfg.price then
            TipWGCtrl.Instance:ShowSystemMsg(Language.YanYuGe.NoEnoughScore)
            RechargeWGCtrl.Instance:RemindRechargeByCangJinShangPuScoreNoEnough(cfg.price - cur_score)
            return
        end
    end

	TipWGCtrl.Instance:OpenAlertTips(string.format(Language.YanYuGe.TZSDPayRechargeScoreStr, cfg.price, cur_score), 
		function()
			YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.BUY_SUIT, cfg.seq, 1)
		end
	)
end

-------------------------------------YYGTZSDItemCellRender----------------------------------
YYGTZSDItemCellRender = YYGTZSDItemCellRender or BaseClass(BaseRender)

function YYGTZSDItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc_name.tmp.text = self.data.cfg.suit_name

    if self.data.cfg.suit_icon and "" ~= self.data.cfg.suit_icon then
        local bundle, asset = ResPath.GetYanYuGeImg(self.data.cfg.suit_icon)
        self.node_list.icon.image:LoadSprite(bundle, asset, function ()
            self.node_list.icon.image:SetNativeSize()
        end)
    end

    local cfg = self.data.cfg
    local limit_buy_times = cfg.buy_limit - self.data.buy_times
    local score = YanYuGeWGData.Instance:GetCurScore()
    -- self.node_list.remind:CustomSetActive(limit_buy_times > 0 and score >= cfg.price)
end

function YYGTZSDItemCellRender:OnSelectChange(is_select)
    self.node_list.select_bg:CustomSetActive(is_select)
end