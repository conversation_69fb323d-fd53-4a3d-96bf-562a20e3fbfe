﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

class LuaConsoleGUI : IDebugWindow
{
    private Vector2 m_ContentScroll;
    private string m_szContentText;
    private string m_szInputText;
    private TextAsset txtAsset;
    private List<string> m_TxtList = new List<string>();
    private int m_nCurTxtIndex = 0;
    private int oldKeyboardControl = 0;

    public void OnDraw()
    {
        m_szContentText = LuaConsole.szContent;
        //TextAsset newTxtAsset = GUILayout.allowSceneObjects(txtAsset, type(txtAsset));
        GUILayout.BeginVertical();

        m_ContentScroll = GUILayout.BeginScrollView(m_ContentScroll);

        //GUILayout.HelpBox(m_szContentText, MessageType.Info);
        m_szContentText = GUILayout.TextArea(m_szContentText);
        GUILayout.EndScrollView();

        m_szInputText = GUILayout.TextArea(m_szInputText, GUILayout.Height(100));
    
        if (GUILayout.Button("Send"))
        {
            if (m_szInputText != "")
            {
                m_TxtList.Add(m_szInputText);
                LuaConsole.szContent += ('\n' + m_szInputText);
                GameRoot.Instance.LuaConsoleGm(m_szInputText);
                m_szInputText = "";
                m_nCurTxtIndex = m_TxtList.Count;
                if (m_nCurTxtIndex < 0)
                    m_nCurTxtIndex = 0;
                this.uodateKeyBoard();
                m_ContentScroll = new Vector2(0, 9000000000); //保证滚动容器到最低
            }
        }

        if (GUILayout.Button("上一条"))
        {
            if (m_TxtList.Count > 0)
            {
                m_nCurTxtIndex--;
                if (m_nCurTxtIndex < 0)
                    m_nCurTxtIndex = 0;
                m_szInputText = m_TxtList[m_nCurTxtIndex];
                this.uodateKeyBoard();
            }

        }

        if (GUILayout.Button("下一条"))
        {
            if (m_TxtList.Count > 0)
            {
                m_nCurTxtIndex++;
                if (m_nCurTxtIndex > m_TxtList.Count - 1)
                    m_nCurTxtIndex = m_TxtList.Count - 1;

                m_szInputText = m_TxtList[m_nCurTxtIndex];
                this.uodateKeyBoard();
            }
        }


        if (GUILayout.Button("清除输出历史(清不定时手动点一下，unity不允许文本过长)"))
        {
            LuaConsole.szContent = "";
            this.uodateKeyBoard();
        }

        GUILayout.EndVertical();



    }

    private void uodateKeyBoard()
    {
        this.oldKeyboardControl = GUIUtility.keyboardControl;
        GUIUtility.keyboardControl = 0;
        //this.Repaint();
    }

    public void Init()
    {
        LuaConsole.szContent = "不要在这里输入指令，这是输出内容";
    }

    public void Destroy()
    {
    }

    public void OnEnter()
    {
    }

    public void OnLeave()
    {
    }
    
    public void Update()
    {
    }
}
