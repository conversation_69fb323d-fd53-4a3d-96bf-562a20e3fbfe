ActDiscountData = ActDiscountData or BaseClass()

function ActDiscountData:__init()
	if ActDiscountData.Instance ~= nil then
		ErrorLog("[ActDiscountData] Attemp to create a singleton twice !")
	end
	ActDiscountData.Instance = self
	RemindManager.Instance:Register(RemindName.ActDisCount, BindTool.Bind(self.SetMainViewRedPoint, self))
	self.discount_cfg = ConfigManager.Instance:GetAutoConfig("discountbuycfg_auto")
	self.day_change = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind1(self.DayChange, self))
end

function ActDiscountData:__delete()
	if self.day_change then
		GlobalEventSystem:UnBind(self.day_change)
		self.day_change = nil
	end
	
	RemindManager.Instance:UnRegister(RemindName.ActDisCount)
	ActDiscountData.Instance = nil
end

function ActDiscountData:DayChange()
	self.phase_cfg = nil
end

function ActDiscountData:GetLeftBarList()
	local role_level = RoleWGData.Instance.role_vo.level
	local time = TimeWGCtrl.Instance:GetServerTime()
	if not self.phase_cfg or self.level_cache ~= role_level or time > self.min_close_timestamp then
		self.phase_cfg = {}
		self.level_cache = role_level
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		for i,v in ipairs(self.discount_cfg.phase_cfg) do
			if role_level >= v.active_level then
				if open_day >= v.begin_open_day and open_day <= v.end_open_day then
					if self.phase_protocol_list[v.phase] and self.phase_protocol_list[v.phase].close_timestamp > time then
						table.insert(self.phase_cfg, v)
					end
				end
			else
				break
			end
		end
	end
	return self.phase_cfg
end

function ActDiscountData:GetSelfPhaseItemCfg()
	self.phase_item_cfg = ListToMapList(self.discount_cfg.item_cfg,"phase")
	table.insert(self.phase_item_cfg[0],1,self.discount_cfg.item_cfg[0])
end

function ActDiscountData:GetRightContentList( phase )
	if not self.phase_item_cfg then
		self:GetSelfPhaseItemCfg()
	end
	local can_buy_list = {}
	if self.phase_protocol_list[phase] then
		local list = self.phase_protocol_list[phase].buy_count_list
		local normal_list = {}
		local no_buy_list = {}
		for i,v in ipairs(self.phase_item_cfg[phase]) do
			if v.price == 0 then
				if list[i] == 0 then
					table.insert(can_buy_list,v)
				end
			else
				if v.buy_limit_count > list[i] then
					table.insert(normal_list,v)
				else
					table.insert(no_buy_list,v)
				end
			end
		end
		for i,v in ipairs(normal_list) do
			table.insert(can_buy_list,v)
		end
		for i,v in ipairs(no_buy_list) do
			table.insert(can_buy_list,v)
		end
	end
	return (not IsEmptyTable(can_buy_list)) and can_buy_list or self.phase_item_cfg[phase]
end

function ActDiscountData:GetRightContentInfo(phase)
	return self.phase_protocol_list[phase]
end

function ActDiscountData:GetCanBuyIt( phase, item_seq )
	local list1 = self.phase_protocol_list[phase]
	local list2 = self.phase_item_cfg[phase]
	if list1 and list2 then
		return list1.buy_count_list[item_seq + 1] ~= list2[item_seq + 1].buy_limit_count
	end
end

function ActDiscountData:SetDiscountInfo(protocol)
	self.phase_protocol_list = {}
	self.min_close_timestamp = 999999999999
	for i, v in ipairs(protocol.phase_list) do
		if v.close_timestamp > 0 then
			self.phase_protocol_list[i-1] = v
			if v.close_timestamp < self.min_close_timestamp then
				self.min_close_timestamp = v.close_timestamp
			end
		end
	end
	self:ShowMainUIBtn()
end

function ActDiscountData:ShowMainUIBtn()
	local list = self:GetLeftBarList()
	if IsEmptyTable(list) then
		-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ACTDISCOUNT,0)
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ACTDISCOUNT, ACTIVITY_STATUS.CLOSE)
		return
	end
	-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ACTDISCOUNT,1,function ()
	-- 	FunOpen.Instance:OpenViewByName(GuideModuleName.ActDiscount)
	-- 	return true
	-- end)
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ACTDISCOUNT, ACTIVITY_STATUS.OPEN)
end

function ActDiscountData:SetMainViewRedPoint()
	for i,v in ipairs(self.phase_cfg) do
		if self:ShowPhaseRed(v.phase) then
			return 1
		end
	end
	return 0
end

function ActDiscountData:ShowPhaseRed(phase)
	local list = self.phase_protocol_list[phase] and self.phase_protocol_list[phase].buy_count_list or {}
	if not self.phase_item_cfg then
		self:GetSelfPhaseItemCfg()
	end
	if not IsEmptyTable(list) then
		for i,v in ipairs(self.phase_item_cfg[phase]) do
			if v.price == 0 and list[i] == 0 then
				return true
			end
		end
	end
	return false
end

function ActDiscountData:GetPhaseNameByPhase( phase )
	for _, v in ipairs(self.discount_cfg.phase_cfg) do
		if v.phase == phase then
			return v.name
		end
	end
	return ""
end

--传闻显示的一折阶段
function ActDiscountData:SetPhaseIndex(phase)
	self.jump_phase_index = phase
end

function ActDiscountData:SetJumpIndex( phase )
	if phase then
		self.jump_phase_index_cache = phase
	else
		return self.jump_phase_index_cache
	end
end

--通过传闻跳转的一折阶段
function ActDiscountData:GetPhaseIndex()
	local list = self:GetLeftBarList()
	if self.jump_phase_index then
		for i,v in ipairs(list) do
			if v.phase == self.jump_phase_index then
				self.jump_phase_index = nil
				return i,#list
			end
		end
		self.jump_phase_index = nil
	end
	return 1
end

function ActDiscountData:GetVector3(str)
	if str ~= nil then
		local v3_num = string.split(str,"|")
		return Vector3(v3_num[1],v3_num[2],v3_num[3])
	end
end