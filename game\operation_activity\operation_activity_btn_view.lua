OperationActivityBtn = OperationActivityBtn or BaseClass(MainActivityBtn)

function OperationActivityBtn:__init()
	
end

function OperationActivityBtn:__delete()

end

function OperationActivityBtn:CreateBtnAsset(parent)
	self:LoadAsset("uis/view/operation_activity_bottom_ui_prefab", "operation_activity_btn", parent.transform)
end

--设置按钮名字
function OperationActivityBtn:SetBtnName(activity_name, activity_type)
	if IsEmptyTable(self.node_list) then
		return
    end
    
    local btn_name = ""
    local other_con_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_config_auto").main_btn_effect
    local open_type = OPERATION_ACTIVITY_OPEN_TYPE[activity_type] or 1
	local effect_cfg = other_con_cfg[open_type]
    if nil == effect_cfg then
		return
	end

    self.node_list.Name.text.text = effect_cfg.btn_name
end


