﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class AnimationCreator_Core_AnimationPlayerWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>ginClass(typeof(AnimationCreator.Core.AnimationPlayer), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("GetNodes", GetNodes);
		<PERSON><PERSON>unction("ClearAnimation", ClearAnimation);
		<PERSON><PERSON>unction("Play", Play);
		<PERSON><PERSON>Function("TryAddNode", TryAddNode);
		<PERSON><PERSON>RegFunction("TryRemoveNode", TryRemoveNode);
		<PERSON><PERSON>RegFunction("GetNodeByID", GetNodeByID);
		<PERSON><PERSON>Function("OnCompleteAnimationGroup", OnCompleteAnimationGroup);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Initialized", get_Initialized, null);
		<PERSON><PERSON>("CurrentSelectedID", get_CurrentSelectedID, set_CurrentSelectedID);
		<PERSON><PERSON>("OnPlayEvent", get_OnPlayEvent, set_OnPlayEvent);
		<PERSON><PERSON>("OnNodesChange", get_OnNodesChange, set_OnNodesChange);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNodes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject<AnimationCreator.Core.AnimationPlayer>(L, 1);
			System.Collections.Generic.List<AnimationCreator.Core.BaseNode> o = obj.GetNodes();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearAnimation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject<AnimationCreator.Core.AnimationPlayer>(L, 1);
			obj.ClearAnimation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject<AnimationCreator.Core.AnimationPlayer>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				obj.Play(arg0);
				return 0;
			}
			else if (count == 3)
			{
				AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject<AnimationCreator.Core.AnimationPlayer>(L, 1);
				string arg0 = ToLua.CheckString(L, 2);
				System.Action arg1 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 3);
				obj.Play(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: AnimationCreator.Core.AnimationPlayer.Play");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TryAddNode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject<AnimationCreator.Core.AnimationPlayer>(L, 1);
			AnimationCreator.Core.BaseNode arg0 = (AnimationCreator.Core.BaseNode)ToLua.CheckObject<AnimationCreator.Core.BaseNode>(L, 2);
			bool o = obj.TryAddNode(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TryRemoveNode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject<AnimationCreator.Core.AnimationPlayer>(L, 1);
			AnimationCreator.Core.BaseNode arg0 = (AnimationCreator.Core.BaseNode)ToLua.CheckObject<AnimationCreator.Core.BaseNode>(L, 2);
			bool o = obj.TryRemoveNode(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNodeByID(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject<AnimationCreator.Core.AnimationPlayer>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			AnimationCreator.Core.BaseNode arg1 = null;
			bool o = obj.GetNodeByID(arg0, out arg1);
			LuaDLL.lua_pushboolean(L, o);
			ToLua.PushObject(L, arg1);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnCompleteAnimationGroup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject<AnimationCreator.Core.AnimationPlayer>(L, 1);
			AnimationCreator.Nodes.AnimationGroupNode arg0 = (AnimationCreator.Nodes.AnimationGroupNode)ToLua.CheckObject<AnimationCreator.Nodes.AnimationGroupNode>(L, 2);
			obj.OnCompleteAnimationGroup(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Initialized(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)o;
			bool ret = obj.Initialized;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Initialized on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CurrentSelectedID(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)o;
			string ret = obj.CurrentSelectedID;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CurrentSelectedID on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnPlayEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<string>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OnNodesChange(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CurrentSelectedID(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.CurrentSelectedID = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CurrentSelectedID on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnPlayEvent(IntPtr L)
	{
		try
		{
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject(L, 1, typeof(AnimationCreator.Core.AnimationPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'AnimationCreator.Core.AnimationPlayer.OnPlayEvent' can only appear on the left hand side of += or -= when used outside of the type 'AnimationCreator.Core.AnimationPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<string> ev = (System.Action<string>)arg0.func;
				obj.OnPlayEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<string> ev = (System.Action<string>)arg0.func;
				obj.OnPlayEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OnNodesChange(IntPtr L)
	{
		try
		{
			AnimationCreator.Core.AnimationPlayer obj = (AnimationCreator.Core.AnimationPlayer)ToLua.CheckObject(L, 1, typeof(AnimationCreator.Core.AnimationPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'AnimationCreator.Core.AnimationPlayer.OnNodesChange' can only appear on the left hand side of += or -= when used outside of the type 'AnimationCreator.Core.AnimationPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.OnNodesChange += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.OnNodesChange -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

