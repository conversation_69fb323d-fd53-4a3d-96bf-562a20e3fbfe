BossRewardThsGivenView = BossRewardThsGivenView or BaseClass(SafeBaseView)

local BOSS_NAME = {
	[1] = "猎魔深渊",
	[2] = "魔界纵横",
	[3] = "天魔化境",
	[4] = "莽荒神兽",
}
local DownCountTime = 5

function BossRewardThsGivenView:__init()
	self.view_name = "BossRewardThsGivenView"
	self.view_layer = UiLayer.Pop

	self:AddViewResource(0, "uis/view/boss_xiezhu_ui_prefab", "layout_boss_thsgiven")
	self.cur_times = 0
	self.reward_thsgiven_data = {}
	self.running_timer = nil
end

function BossRewardThsGivenView:__delete()

end

function BossRewardThsGivenView:LoadCallBack()
	self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.ClickCancel, self))
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.ClickCancel, self))
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.ClickOk, self))
	self.node_list["add_friend_btn"].button:AddClickListener(BindTool.Bind(self.OnClickAddFriendBtn, self))
	self.node_list["ignore_open_btn"].button:AddClickListener(BindTool.Bind(self.OnIgnoreBtn, self))

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		-- self.reward_list:SetStartZeroIndex(true)
	end
end

function BossRewardThsGivenView:OpenCallBack()
	self.is_add_friend = 1
	self.add_friend_id = 0
end

function BossRewardThsGivenView:ReleaseCallBack()
	self:ClearTimer()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function BossRewardThsGivenView:OnFlush()
	self:FlushView()
end

function BossRewardThsGivenView:FlushView()
	if not IsEmptyTable(self.reward_thsgiven_data) then
		return
	end
	local reward_data = BossWGData.Instance:GetBossThsGivenRewardData()
	if not reward_data or IsEmptyTable(reward_data) then
		self:ClearTimer()
		self:Close()
		return
	end

	local is_ignore_view = BossWGData.Instance:GetIsIgnoreThankMsg()
	self.node_list.ignore_hook:SetActive(is_ignore_view > 0)

	self.reward_thsgiven_data = reward_data
	self.node_list["btn_cancel"]:SetActive(reward_data.is_send_me == 1)
	self.node_list["add_friend_btn"]:SetActive(reward_data.is_send_me == 1)
	self.node_list["add_friend_tips"]:SetActive(reward_data.is_send_me == 1)
	--self.node_list.title_tmp.text.text = reward_data.is_send_me == 0 and Language.BossAssist.ReceiveGift or Language.BossAssist.DaXieGift
	--self.node_list.title_img_ganxie:SetActive(reward_data.is_send_me == 0)
	--self.node_list.title_img_daxie:SetActive(reward_data.is_send_me == 1)
    BrowseWGCtrl.Instance:SendQueryRoleInfoReq(reward_data.user_id, function (user_info)
        if self.node_list and self.node_list.rich_dialog and not IsEmptyTable(user_info) then
            local boss_type_name = BOSS_NAME[reward_data.boss_type]
            local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(reward_data.boss_id)
            local boss_name = boss_cfg and boss_cfg.name or ""
            local ths_str = reward_data.is_send_me == 0
                    and string.format(Language.Boss.ThsGivenBossReward2, boss_name, user_info.role_name)
                    or string.format(Language.Boss.ThsGivenBossReward1, user_info.role_name, boss_name)
            self.node_list.rich_dialog.tmp.text = ths_str
			self.add_friend_id = user_info.role_id
        end
	end)

	if not IsEmptyTable(reward_data.item_ids) then
		self.reward_list:SetDataList(reward_data.item_ids)
	end

	if reward_data.is_send_me == 0 then
		self.node_list["down_count_text"].tmp.text = string.format(Language.BossAssist.AutoClose, DownCountTime)
		if not self.running_timer then
			self.running_timer = GlobalTimerQuest:AddTimesTimer(function()
				self.cur_times = self.cur_times + 1

				if not self:IsLoaded() or not self:IsOpen() then
					self:ClearTimer()
					return
				end
				self.node_list["down_count_text"].tmp.text = string.format(Language.BossAssist.AutoClose, tostring(DownCountTime - self.cur_times))
	            if self.cur_times >= DownCountTime then
	            	self.reward_thsgiven_data = {}
	                self:ClearTimer()
					self:FlushView()
				end
			end, 1, DownCountTime)
		end
	else
		self.node_list["down_count_text"].tmp.text = ""
		self:ClearTimer()
	end
end

function BossRewardThsGivenView:ClickOk()
	if IsEmptyTable(self.reward_thsgiven_data) or self.reward_thsgiven_data.is_send_me == 0 then
		self.reward_thsgiven_data = {}
		self:ClearTimer()
		self:FlushView()
		return
	end

	if self.reward_thsgiven_data.is_send_me == 1 then
		local data = self.reward_thsgiven_data
		BossWGCtrl.Instance:OnCSRefreshBossRewardGet(data.boss_type, data.boss_id, data.user_id,
													data.plat_type, data.item_ids, data.reward_id)
		self.reward_thsgiven_data = {}

		if self.add_friend_id > 0 and self.is_add_friend > 0 then
			SocietyWGCtrl.Instance:AddFriend(self.add_friend_id, 0)
		end

		self:ClearTimer()
		self:FlushView()
	end
end

function BossRewardThsGivenView:ClickCancel()
	self.reward_thsgiven_data = {}
	self:ClearTimer()
	self:FlushView()
end

function BossRewardThsGivenView:ClearTimer()
	self.cur_times = 0
	GlobalTimerQuest:CancelQuest(self.running_timer)
	self.running_timer = nil
end

function BossRewardThsGivenView:OnClickAddFriendBtn()
	self.is_add_friend = self.is_add_friend > 0 and 0 or 1
	self.node_list.hook:SetActive(self.is_add_friend == 1)
end

function BossRewardThsGivenView:OnIgnoreBtn()
	local save_value = BossWGData.Instance:GetIsIgnoreThankMsg()
	local is_ignore_view = save_value == 0 and 1 or 0
	self.node_list.ignore_hook:SetActive(is_ignore_view > 0)
	BossWGData.Instance:SetIsIgnoreThankMsg(is_ignore_view)
end