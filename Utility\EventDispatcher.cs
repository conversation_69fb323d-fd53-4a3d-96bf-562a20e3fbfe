﻿using UnityEngine;
using System.Collections.Generic;
using LuaInterface;
using Nirvana;

public class EventDispatcher : MonoBeh<PERSON>our
{
    public static EventDispatcher Instance;

    private List<Game.GameObjectAttach> enabledGameObjAttach = new List<Game.GameObjectAttach>(256);
    private List<Game.GameObjectAttach> disabledGameObjAttach = new List<Game.GameObjectAttach>(256);
    private List<int> destroyedGameObjAttach = new List<int>(256);

    private List<Game.LoadRawImage> enabledLoadRawImage = new List<Game.LoadRawImage>(256);
    private List<Game.LoadRawImage> disabledLoadRawImage = new List<Game.LoadRawImage>(256);
    private List<int> destroyedLoadRawImage = new List<int>(256);

    public LuaFunction EnableGameObjAttachFunc
    {
        get; set;
    }

    public LuaFunction DisableGameObjAttachFunc
    {
        get; set;
    }

    public LuaFunction DestroyGameObjAttachFunc
    {
        get; set;
    }

    public LuaFunction EnableLoadRawImageFunc
    {
        get; set;
    }

    public LuaFunction DisableLoadRawImageFunc
    {
        get; set;
    }

    public LuaFunction DestroyLoadRawImageFunc
    {
        get; set;
    }

    public LuaFunction ProjectileSingleEffectFunc
    {
        get; set;
    }

    public LuaFunction UIMouseClickEffectFunc
    {
        get; set;
    }

    private void Awake()
    {
        Instance = this;
    }

    private void OnDestroy()
    {
        Instance = null;
    }

    private void Update()
    {
        ToLuaProfile.AddProfileBeginSample("c:DisableGameObjAttachFunc");
        if (disabledGameObjAttach.Count > 0 && DisableGameObjAttachFunc != null)
        {
            DisableGameObjAttachFunc.Call(disabledGameObjAttach);
            disabledGameObjAttach.Clear();
        }
        ToLuaProfile.AddProfileEndSample();

        ToLuaProfile.AddProfileBeginSample("c:EnableGameObjAttachFunc");
        if (enabledGameObjAttach.Count > 0 && EnableGameObjAttachFunc != null)
        {
            EnableGameObjAttachFunc.Call(enabledGameObjAttach);
            enabledGameObjAttach.Clear();
        }
        ToLuaProfile.AddProfileEndSample();

        ToLuaProfile.AddProfileBeginSample("c:DestroyGameObjAttachFunc");
        if (destroyedGameObjAttach.Count > 0 && DestroyGameObjAttachFunc != null)
        {
            DestroyGameObjAttachFunc.Call(destroyedGameObjAttach);
            destroyedGameObjAttach.Clear();
        }
        ToLuaProfile.AddProfileEndSample();

        ToLuaProfile.AddProfileBeginSample("c:DisableLoadRawImageFunc");
        if (disabledLoadRawImage.Count > 0 && DisableLoadRawImageFunc != null)
        {
            DisableLoadRawImageFunc.Call(disabledLoadRawImage);
            disabledLoadRawImage.Clear();
        }
        ToLuaProfile.AddProfileEndSample();

        ToLuaProfile.AddProfileBeginSample("c:EnableLoadRawImageFunc");
        if (enabledLoadRawImage.Count > 0 && EnableLoadRawImageFunc != null)
        {
            EnableLoadRawImageFunc.Call(enabledLoadRawImage);
            enabledLoadRawImage.Clear();
        }
        ToLuaProfile.AddProfileEndSample();

        ToLuaProfile.AddProfileBeginSample("c:DestroyLoadRawImageFunc");
        if (destroyedLoadRawImage.Count > 0 && DestroyLoadRawImageFunc != null)
        {
            DestroyLoadRawImageFunc.Call(destroyedLoadRawImage);
            destroyedLoadRawImage.Clear();
        }
        ToLuaProfile.AddProfileEndSample();
    }

    public void OnGameObjAttachEnable(Game.GameObjectAttach gameObjAttach)
    {
        enabledGameObjAttach.Add(gameObjAttach);
    }

    public void OnGameObjAttachDisable(Game.GameObjectAttach gameObjAttach)
    {
        disabledGameObjAttach.Add(gameObjAttach);
    }

    public void OnGameObjAttachDestroyed(Game.GameObjectAttach gameObjAttach)
    {
        destroyedGameObjAttach.Add(gameObjAttach.GetInstanceID());
    }

    public void OnLoadRawImageEnable(Game.LoadRawImage loadRawImage)
    {
        enabledLoadRawImage.Add(loadRawImage);
    }

    public void OnLoadRawImageDisable(Game.LoadRawImage loadRawImage)
    {
        disabledLoadRawImage.Add(loadRawImage);
    }

    public void OnLoadRawImageDestroy(Game.LoadRawImage loadRawImage)
    {
        destroyedLoadRawImage.Add(loadRawImage.GetInstanceID());
    }

    public void OnProjectileSingleEffect(EffectControl hitEffect, Vector3 position, Quaternion rotation, bool hit_effect_with_rotation, Vector3 source_scale, int layer)
    {
        ProjectileSingleEffectFunc.Call(hitEffect, position, rotation, hit_effect_with_rotation, source_scale, layer);
    }

    public void OnUIMouseClickEffect(GameObject effectInstance, GameObject[] effects, Canvas canvas, Transform mouse_click_transform)
    {
        UIMouseClickEffectFunc.Call(effectInstance, effects, canvas, mouse_click_transform);
    }
}
