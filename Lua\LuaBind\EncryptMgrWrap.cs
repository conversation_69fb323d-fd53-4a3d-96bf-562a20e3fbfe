﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class EncryptMgrWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(EncryptMgr), typeof(System.Object));
		<PERSON><PERSON>Function("InitEncryptKey", InitEncryptKey);
		<PERSON><PERSON>("GetEncrypt<PERSON>ey", GetEncryptKey);
		<PERSON><PERSON>unction("GetStreamingEncryptKey", GetStreamingEncryptKey);
		<PERSON><PERSON>RegFunction("GetStreamingEncryptyString", GetStreamingEncryptyString);
		<PERSON><PERSON>unction("SetEncryptKey", SetEncryptKey);
		<PERSON><PERSON>unction("SetBase64EncryptKey", SetBase64EncryptKey);
		<PERSON><PERSON>Function("SaveCacheEncryptKeyFile", SaveCacheEncryptKeyFile);
		<PERSON><PERSON>unction("GetEncryptKeyLength", GetEncryptKeyLength);
		<PERSON><PERSON>("Get<PERSON><PERSON><PERSON>", Get<PERSON>cii);
		<PERSON><PERSON>ction("GetStreamingEncryptKeyLength", GetStreamingEncryptKeyLength);
		L.RegFunction("CalcEncryptKey", CalcEncryptKey);
		L.RegFunction("IsBase64EncryptCacheAsset", IsBase64EncryptCacheAsset);
		L.RegFunction("GetIsBase64EncryptCacheAsset", GetIsBase64EncryptCacheAsset);
		L.RegFunction("GetIsBase64EncryptStreamingAsset", GetIsBase64EncryptStreamingAsset);
		L.RegFunction("IsEncryptAsset", IsEncryptAsset);
		L.RegFunction("GetStreamingEncryptPath", GetStreamingEncryptPath);
		L.RegFunction("GetStreamingEncryptPath2Base64", GetStreamingEncryptPath2Base64);
		L.RegFunction("GetEncryptPath", GetEncryptPath);
		L.RegFunction("GetEncryptPath2Base64Editor", GetEncryptPath2Base64Editor);
		L.RegFunction("GetEncryptPath2Base64", GetEncryptPath2Base64);
		L.RegFunction("ConvertContentToBase64", ConvertContentToBase64);
		L.RegFunction("ConvertContentToDeBase64", ConvertContentToDeBase64);
		L.RegFunction("ReadEncryptBytes", ReadEncryptBytes);
		L.RegFunction("ReadStreamingEncryptFile", ReadStreamingEncryptFile);
		L.RegFunction("ReadEncryptFile", ReadEncryptFile);
		L.RegFunction("DecryptAssetBundle", DecryptAssetBundle);
		L.RegFunction("DecryptAgentAssets", DecryptAgentAssets);
		L.RegFunction("DecryptStr2Str", DecryptStr2Str);
		L.RegFunction("DecryptByte2Str", DecryptByte2Str);
		L.RegFunction("WriteFileToThePath", WriteFileToThePath);
		L.RegFunction("XORAndBase64Encode", XORAndBase64Encode);
		L.RegFunction("XORAndBase64Decode", XORAndBase64Decode);
		L.RegFunction("FromBase64String", FromBase64String);
		L.RegFunction("New", _CreateEncryptMgr);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("Base64KeyCode", get_Base64KeyCode, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateEncryptMgr(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				EncryptMgr obj = new EncryptMgr();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: EncryptMgr.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitEncryptKey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			EncryptMgr.InitEncryptKey();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEncryptKey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			byte[] o = EncryptMgr.GetEncryptKey();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetStreamingEncryptKey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			byte[] o = EncryptMgr.GetStreamingEncryptKey();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetStreamingEncryptyString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = EncryptMgr.GetStreamingEncryptyString();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEncryptKey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			EncryptMgr.SetEncryptKey(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetBase64EncryptKey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			EncryptMgr.SetBase64EncryptKey(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SaveCacheEncryptKeyFile(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			EncryptMgr.SaveCacheEncryptKeyFile();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEncryptKeyLength(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				int o = EncryptMgr.GetEncryptKeyLength();
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				int o = EncryptMgr.GetEncryptKeyLength(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: EncryptMgr.GetEncryptKeyLength");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAscii(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			byte[] o = EncryptMgr.GetAscii(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetStreamingEncryptKeyLength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = EncryptMgr.GetStreamingEncryptKeyLength();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CalcEncryptKey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			byte[] o = EncryptMgr.CalcEncryptKey(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsBase64EncryptCacheAsset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			EncryptMgr.IsBase64EncryptCacheAsset(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetIsBase64EncryptCacheAsset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = EncryptMgr.GetIsBase64EncryptCacheAsset();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetIsBase64EncryptStreamingAsset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = EncryptMgr.GetIsBase64EncryptStreamingAsset();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsEncryptAsset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = EncryptMgr.IsEncryptAsset();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetStreamingEncryptPath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.GetStreamingEncryptPath(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetStreamingEncryptPath2Base64(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.GetStreamingEncryptPath2Base64(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEncryptPath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.GetEncryptPath(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEncryptPath2Base64Editor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.GetEncryptPath2Base64Editor(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEncryptPath2Base64(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.GetEncryptPath2Base64(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ConvertContentToBase64(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.ConvertContentToBase64(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ConvertContentToDeBase64(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.ConvertContentToDeBase64(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ReadEncryptBytes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
			byte[] o = EncryptMgr.ReadEncryptBytes(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ReadStreamingEncryptFile(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.ReadStreamingEncryptFile(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ReadEncryptFile(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string o = EncryptMgr.ReadEncryptFile(arg0);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				byte[] arg1 = ToLua.CheckByteBuffer(L, 2);
				string o = EncryptMgr.ReadEncryptFile(arg0, arg1);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: EncryptMgr.ReadEncryptFile");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DecryptAssetBundle(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			bool o = EncryptMgr.DecryptAssetBundle(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DecryptAgentAssets(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.DecryptAgentAssets(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DecryptStr2Str(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.DecryptStr2Str(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DecryptByte2Str(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
			string o = EncryptMgr.DecryptByte2Str(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WriteFileToThePath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			bool o = EncryptMgr.WriteFileToThePath(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int XORAndBase64Encode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			string o = EncryptMgr.XORAndBase64Encode(arg0, arg1);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int XORAndBase64Decode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			string o = EncryptMgr.XORAndBase64Decode(arg0, arg1);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FromBase64String(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = EncryptMgr.FromBase64String(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Base64KeyCode(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, EncryptMgr.Base64KeyCode);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

