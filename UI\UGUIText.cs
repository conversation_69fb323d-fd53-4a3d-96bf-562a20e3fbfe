﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("UI/UGUIText", 10)]
public class UGUIText : Text, ILayoutElement
{
    internal static readonly ObjectPool<TextGenerator> s_TextGeneratorPool = new ObjectPool<TextGenerator>(l => l.Invalidate());
    private TextGenerator textCache;
    private TextGenerator textCacheForLayout;

    new public TextGenerator cachedTextGenerator
    {
        get { return textCache ?? (textCache = s_TextGeneratorPool.Get()); }
    }

    new public TextGenerator cachedTextGeneratorForLayout
    {
        get { return textCacheForLayout ?? (textCacheForLayout = s_TextGeneratorPool.Get()); }
    }

    protected override void OnDisable()
    {
        FontUpdateTracker.UntrackText(this);


        if (null != textCache)
        {
            s_TextGeneratorPool.Release(textCache);
            textCache = null;
        }

        if (null != textCacheForLayout)
        {
            s_TextGeneratorPool.Release(textCacheForLayout);
            textCacheForLayout = null;
        }

        base.OnDisable();
    }

    readonly UIVertex[] m_TempVerts = new UIVertex[4];
    protected override void OnPopulateMesh(VertexHelper toFill)
    {
        if (font == null)
            return;

        // We don't care if we the font Texture changes while we are doing our Update.
        // The end result of cachedTextGenerator will be valid for this instance.
        // Otherwise we can get issues like Case 619238.
        m_DisableFontTextureRebuiltCallback = true;

        Vector2 extents = rectTransform.rect.size;

        //cachedTextGenerator.Invalidate();
        var settings = GetGenerationSettings(extents);
        cachedTextGenerator.PopulateWithErrors(text, settings, gameObject);

        // Apply the offset to the vertices
        List<UIVertex> verts = ListPool<UIVertex>.Get();
        cachedTextGenerator.GetVertices(verts);
        float unitsPerPixel = 1 / pixelsPerUnit;
        //Last 4 verts are always a new line... (\n)
        int vertCount = verts.Count - 4;

        Vector2 roundingOffset = new Vector2(verts[0].position.x, verts[0].position.y) * unitsPerPixel;
        roundingOffset = PixelAdjustPoint(roundingOffset) - roundingOffset;
        toFill.Clear();
        if (roundingOffset != Vector2.zero)
        {
            Vector3 tempPos = Vector3.zero;
            for (int i = 0; i < vertCount; ++i)
            {
                var vert = verts[i];
                int tempVertsIndex = i & 3;
                // 当在一个四边形计算中，连续2个顶点都相同时，则认为不是一完整的字符
                // 处理<color='#ff5a00'>AA</color>这种产生多个多余顶点的情况
                if (tempVertsIndex == 0) tempPos = vert.position;
                else if (tempPos == vert.position) continue;

                m_TempVerts[tempVertsIndex] = vert;
                m_TempVerts[tempVertsIndex].position *= unitsPerPixel;
                m_TempVerts[tempVertsIndex].position.x += roundingOffset.x;
                m_TempVerts[tempVertsIndex].position.y += roundingOffset.y;
                if (tempVertsIndex == 3)
                    toFill.AddUIVertexQuad(m_TempVerts);
            }
        }
        else
        {
            Vector3 tempPos = Vector3.zero;
            for (int i = 0; i < vertCount; ++i)
            {
                var vert = verts[i];
                int tempVertsIndex = i & 3; // 0..1..2..3 代表四边形的4个顶点

                // 当在一个四边形计算中，连续2个顶点都相同时，则认为不是一完整的字符)
                // 处理<color='#ff5a00'>AA</color>这种产生多个多余顶点的情况
                if (tempVertsIndex == 0) tempPos = vert.position;
                else if (tempPos == vert.position) continue;

                m_TempVerts[tempVertsIndex] = vert;
                m_TempVerts[tempVertsIndex].position *= unitsPerPixel;
                if (tempVertsIndex == 3)
                    toFill.AddUIVertexQuad(m_TempVerts);
            }
        }

        ListPool<UIVertex>.Release(verts);

        m_DisableFontTextureRebuiltCallback = false;
    }
}
