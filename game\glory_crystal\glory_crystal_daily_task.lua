-- 天裳豪礼-每日任务
function GloryCrystalHaoLiView:ReleaseDailyTask()
	if self.daily_task_list then
		self.daily_task_list:DeleteMe()
		self.daily_task_list = nil
	end
end

function GloryCrystalHaoLiView:LoadDailyTaskCallBack()
	if not self.daily_task_list then
		self.daily_task_list = AsyncListView.New(GloryCrystalTaskCell, self.node_list.task_list_view)
	end

	self.node_list.task_tip_text.text.text = Language.GloryCrystal.DailyTaskTip
end

function GloryCrystalHaoLiView:FlushDailyTask()
	local show_list = GloryCrystalWGData.Instance:GetDailyTask()
	self.daily_task_list:SetDataList(show_list)
end

--------------------------------------- GloryCrystalTaskCell ------------------------------------
GloryCrystalTaskCell = GloryCrystalTaskCell or BaseClass(BaseRender)
function GloryCrystalTaskCell:ReleaseCallBack()
	if self.award_item_list then
		self.award_item_list:DeleteMe()
		self.award_item_list = nil
	end
end

function GloryCrystalTaskCell:LoadCallBack()
	if not self.award_item_list then
		self.award_item_list = AsyncListView.New(ItemCell, self.node_list.award_list_view)
		self.award_item_list:SetStartZeroIndex(true)
	end

	self:ChangeViewStyle()

	XUI.AddClickEventListener(self.node_list.btn_jump_view, BindTool.Bind(self.GoTo, self))
	XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.GetAward, self))
end

function GloryCrystalTaskCell:ChangeViewStyle()
	local cfg = GloryCrystalWGData.Instance:GetCurViewStyle()
	if not cfg then
		return
	end

	local bundle, asset = ResPath.GetRawImagesPNG("a3_tsxy_mrrw_dk_" .. cfg.color_index)
	if self.node_list.bg then
		self.node_list.bg.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.bg.raw_image:SetNativeSize()
		end)
	end
end

function GloryCrystalTaskCell:OnFlush()
	local data = self:GetData()
	local cfg = data.cfg
	self.award_item_list:SetDataList(cfg.reward_item)
	
	self.node_list.process.text.text = string.format("%s/%s", data.process, cfg.param1)
	self.node_list.task_desc.text.text = cfg.task_desc or ""

	local is_complete = data.process >= cfg.param1
	local is_finish = data.fetch_flag ~= 0
	self.node_list.btn_jump_view:CustomSetActive(not is_complete)
	self.node_list.btn_get:CustomSetActive(is_complete and not is_finish)
	self.node_list.is_finish:CustomSetActive(is_finish)
end

function GloryCrystalTaskCell:GoTo()
	local cfg = self.data.cfg
	if cfg.open_panel then
		ViewManager.Instance:OpenByCfg(cfg.open_panel)
	end
end

function GloryCrystalTaskCell:GetAward()
	GloryCrystalWGCtrl.Instance:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.DAILY_REWARD, self.data.seq)
end