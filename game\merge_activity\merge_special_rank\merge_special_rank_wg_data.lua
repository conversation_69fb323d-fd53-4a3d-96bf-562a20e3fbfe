MergeSpecialRankWGData = MergeSpecialRankWGData or BaseClass()

function MergeSpecialRankWGData:__init()
    if nil ~= MergeSpecialRankWGData.Instance then
        ErrorLog("[MergeSpecialRankWGData]:Attempt to create singleton twice!")
    end
    MergeSpecialRankWGData.Instance = self

    self.grade = -1

    self.gold_my_rank_value = 0
    self.gold_my_rank = 0
    --self.gold_is_end = 0
    self.gold_max_count = 0
    self.gold_data_list = {}

    self.silver_ticket_my_rank_value = 0
    self.silver_ticket_my_rank = 0
    --self.silver_ticket_is_end = 0
    self.silver_ticket_max_count = 0
    self.silver_ticket_data_list = {}

    self.is_end = -1

    self:InitConfig()
    self:CreateTipShowRankList()

    RemindManager.Instance:Register(RemindName.MergeActivity_SPRank, BindTool.Bind(self.IsShowSpecialRankRedPoint, self))
    MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_RANK, {[1] = MERGE_EVENT_TYPE.NONE},
    	BindTool.Bind(self.GetActIsOpen, self))
end

function MergeSpecialRankWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.MergeActivity_SPRank)

    MergeSpecialRankWGData.Instance = nil
end

function MergeSpecialRankWGData:InitConfig()
    local all_config = ConfigManager.Instance:GetAutoConfig("combine_server_activity_consume_gold_rand_cfg_auto")
    self.config_param_map = ListToMap(all_config.config_param, "grade")
    --self.reward_map
    self.gold_view_cfg = ListToMap(all_config.view_cfg, "grade")
    self:MakeGoldRewardData(all_config.reward)
    self.silver_ticket_config_param_map = {}
    self.silver_ticket_view_cfg = {}
end

function MergeSpecialRankWGData:MakeGoldRewardData(cfg)
    self.reward_map = {}

    for i, v in ipairs(cfg) do
        if not self.reward_map[v.grade] then
            self.reward_map[v.grade] = {}
        end

        local data = {}
        data.rank_hight = v.rank_hight
        data.rank_low = v.rank_low
        data.reach_value = v.reach_value
        data.item_list = {}
        for i = 0, #v.reward_item do
            table.insert(data.item_list, v.reward_item[i])
        end
        table.insert(self.reward_map[v.grade], data)
    end
end

function MergeSpecialRankWGData:GetSpecialRankReward()
    if not IsEmptyTable(self.reward_map) then
        return self.reward_map[self.grade]
    end
    return self.reward_map[self.grade]
end

function MergeSpecialRankWGData:SetInfo(protocol)
    self.grade = protocol.grade
    self.is_end = protocol.is_end
    if protocol.rank_type == MERGE_SPECITAL_RANK_OPERATE.GOLD_RANK_INFO then --仙玉榜
        self:MakeGoldData(protocol)
    end
end

function MergeSpecialRankWGData:GetGrade()
    return self.grade
end

function MergeSpecialRankWGData:RecordDuplicateGoldRank(rank_list)
    self.gold_dupilcate_rank_map = {}
    for i, v in ipairs(rank_list) do
        if not self.gold_dupilcate_rank_map[v.rank] then
            self.gold_dupilcate_rank_map[v.rank] = {}
        end

        table.insert(self.gold_dupilcate_rank_map[v.rank], v)
    end
end

function MergeSpecialRankWGData:MakeGoldData(protocol)
    local grade = protocol.grade
    local rank_list = protocol.rank_list
    self.gold_my_rank_value = protocol.my_rank_value            --自己的值
    self.gold_my_rank = protocol.my_rank                        --自己的排行
    --self.gold_is_end = protocol.is_end                          --是否结算了

    self.gold_data_list = {}                                    --数据列表
    if not self.config_param_map[grade] then
        print_error("MakeGoldData 不存在配置 >>> grade :", grade)
        return
    end

    local rank_max_count = self.config_param_map[grade].rank_show_num
    --记录重叠排名的rank_item
    self.gold_dupilcate_rank_map = {}
    for i, v in ipairs(rank_list) do
        if not self.gold_dupilcate_rank_map[v.rank] then
            self.gold_dupilcate_rank_map[v.rank] = {}
        end

        table.insert(self.gold_dupilcate_rank_map[v.rank], v)
    end

    for rank = 1, rank_max_count do
        if not self.sp_rank_list[rank] then
            self.sp_rank_list[rank] = self:CreateEmptyRankData(rank)
        end

        local rank_info = self.gold_dupilcate_rank_map[rank]
        local no_true_rank = IsEmptyTable(rank_info)
        self.sp_rank_list[rank].no_true_rank = no_true_rank
        self.sp_rank_list[rank].rank = rank
        if not no_true_rank then
            self.sp_rank_list[rank].user_name = rank_info[1].name
            self.sp_rank_list[rank].rank_value = rank_info[1].rank_value
        end
    end

    --构造排行列表
    local rank_index = 1
    for i = 1, rank_max_count do
        if rank_index > rank_max_count then
            break
        end
        local dupilcate_list = self.gold_dupilcate_rank_map[rank_index]
        if dupilcate_list and #dupilcate_list > 0 then
            for k = 1, #dupilcate_list do
                local rank_item = dupilcate_list[k]
                local data = self:CheckIndex(rank_item.rank, grade)
                if data then
                    rank_item.item_list = data.item_list
                    rank_item.is_end = protocol.is_end
                    rank_item.reach_value = data.reach_value

                    table.insert(self.gold_data_list, rank_item)
                    rank_index = rank_index + 1
                else
                    break
                end
            end
        else
            local data = self:CheckIndex(rank_index, grade)
            if data then
                local rank_item = {}
                rank_item.grade = data.grade
                rank_item.item_list = data.item_list
                rank_item.rank = rank_index
                rank_item.rank_value = 0
                rank_item.reach_value = data.reach_value
                rank_item.is_end = protocol.is_end
                rank_item.is_empty_value = true

                table.insert(self.gold_data_list, rank_item)
                rank_index = rank_index + 1
            else
                break
            end
        end
    end

    self.gold_max_count = rank_max_count                        --绑玉排行最大显示数量

    GlobalEventSystem:Fire(CHONGBANG_TIP_EVENT.ACT_CHECK,ChongBangTipWGData.ACT_TIP_TYPE.ACT,ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_RANK)
    ----最后最后一条数据构造。
    --local data = self:CheckIndex(rank_max_count + 1, grade)
    --if data then
    --    local item = {}
    --    item.grade = data.grade
    --    item.item_list = data.item_list
    --    item.rank = rank_max_count + 1
    --    item.rank_value = 0
    --    item.reach_value = data.reach_value
    --    item.is_end = protocol.is_end
    --    item.is_empty_value = true
    --    table.insert(self.gold_data_list, item)
    --end
end

function MergeSpecialRankWGData:CheckIndex(index, grade)
    local list = self.reward_map[grade]
    for i, v in ipairs(list) do
        if v.rank_hight <= index and index <= v.rank_low then
            return v
        end
    end

    return nil
end


function MergeSpecialRankWGData:CheckSilverTicketIndex(index, grade)
    -- local list = self.silver_ticket_reward_map[grade]
    -- for i, v in ipairs(list) do
    --     if v.rank_hight <= index and index <= v.rank_low then
    --         return v
    --     end
    -- end

    return nil
end

function MergeSpecialRankWGData:GetGoldDataList()
    return self.gold_data_list
end

function MergeSpecialRankWGData:GetSilverTicketDataList()
    return self.silver_ticket_data_list
end

function MergeSpecialRankWGData:GetIsEnd()
    return self.is_end and self.is_end == 1
end

--获取信息：自己名次、自己排行的值、是否结算。
function MergeSpecialRankWGData:GetGoldRankInfo()
    return self.gold_my_rank, self.gold_my_rank_value, self.gold_max_count, self.is_end
end
--获取信息：自己名次、自己排行的值、是否结算。
function MergeSpecialRankWGData:GetSilverTicketRankInfo()
    return self.silver_ticket_my_rank, self.silver_ticket_my_rank_value, self.silver_ticket_max_count, self.is_end
end

function MergeSpecialRankWGData:IsShowSpecialRankRedPoint()
    --还没结算 并且 今日没提醒过
    if self.is_end == 0 and not self:GetTodayIsReminded() then
        return 1
    end

    return 0
end

function MergeSpecialRankWGData:GetRankList()
    return self.sp_rank_list
end

--本地保存今天提醒状态
function MergeSpecialRankWGData:CacheTodayIsReminded()
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local name = uid .."MergeSpecialRankDataRemind"
	PlayerPrefsUtil.SetString(name, cur_day)
end

--今日是否提醒过
function MergeSpecialRankWGData:GetTodayIsReminded()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local name = uid.."MergeSpecialRankDataRemind"
	local remind_day = PlayerPrefsUtil.GetString(name) or 0
	return tonumber(remind_day) == cur_day
end

function MergeSpecialRankWGData:GetActIsOpen()
    --
    if self.grade == -1 then
        return false
    end
    return true
end

function MergeSpecialRankWGData:GetGoldViewCfg()
    return self.gold_view_cfg[self.grade]
end
function MergeSpecialRankWGData:GetSilverTicketViewCfg()
    return self.silver_ticket_view_cfg[self.grade]
end


function MergeSpecialRankWGData:GetGoldConfigParamCfg()
    return self.config_param_map[self.grade]
end
function MergeSpecialRankWGData:GetSilverTicketConfigParamCfg()
    return self.silver_ticket_config_param_map[self.grade]
end

function MergeSpecialRankWGData:CreateTipShowRankList()
    self.sp_rank_list = {}
    local rank_max_count = 30
    for rank = 1, rank_max_count do
        self.sp_rank_list[rank] = self:CreateEmptyRankData(rank)
    end
end

function MergeSpecialRankWGData:CreateEmptyRankData(rank)
    return {no_true_rank = true, rank = rank, user_name = "", rank_value = 0}
end