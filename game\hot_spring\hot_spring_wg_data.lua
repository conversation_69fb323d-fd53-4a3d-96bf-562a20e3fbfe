HotSpringWGData = HotSpringWGData or BaseClass()
HotSpringWGData.XUEQIU_SKILL = 100001
HotSpringWGData.SHUIQIU_SKILL = 100002
function HotSpringWGData:__init()
	if HotSpringWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[HotSpringWGData] Attemp to create a singleton twice !")
	end
	HotSpringWGData.Instance = self

	self.total_score = 0
	self.got_score = 0
	self.play_times = 0
	self.last_monster_list = {}
	self.monster_list= {}
	self.cur_game_end_time = 0
	self.effect_queue_size = 0
	self.effect_queue_cur_index = 0
	self.effect_callback_queue = {}
	self.is_in_playing_effect = false

	self.monster_list_queue = {}

	self.check_pos_list_t = {{1,2,3}, {4,5,6}, {7,8,9}, {1,4,7}, {2,5,8}, {3,6,9}, {1,5,9}, {3,5,7}}
	self.check_elimination_effect_t = {
		{index = 2, rotation_angle = -30},
		{index = 5, rotation_angle = -30},
		{index = 8, rotation_angle = -30},
		{index = 4, rotation_angle = 30},
		{index = 5, rotation_angle = 30},
		{index = 6, rotation_angle = 30},
		{index = 5, rotation_angle = 0},
		{index = 5, rotation_angle = 90},
	}


	self:ClearHotSpringCache()

	self.is_cancel_shuangxiu = -1
	self.shuangxiu_all_list = {}
	self.cross_hotspring_auto = ConfigManager.Instance:GetAutoConfig("cross_hotspring_auto")
	self.hotspring_auto = ConfigManager.Instance:GetAutoConfig("hotspring_auto")

	self.bubble_po_shui_list = ListToMap(self.hotspring_auto.bubble_content, "index")
	local count = 0
	for i, v in pairs(self.hotspring_auto.bubble_content) do
		count = count + 1
	end
	self.bubble_po_shui_count = count
end

function HotSpringWGData:ClearHotSpringCache()
	self.snow_times = 0								-- 雪人次数
	self.intimidation_times = 0						-- 恐吓次数
	self.snow_cd_time = 0							-- 雪人CD时间
	self.intimidation_cd_time = 0					-- 恐吓CD时间
	self.total_exp = 0								-- 总共获取经验
	self.anmo_cd_time = 0
	self.anmo_times = 0
end

function HotSpringWGData:__delete()
	HotSpringWGData.Instance = nil
end

function HotSpringWGData:PushMonsterList(monster_list)
	table.insert(self.monster_list_queue, monster_list)
end

function HotSpringWGData:PopMonsterList()
	return table.remove(self.monster_list_queue, 1)
end


function HotSpringWGData:SetHotspringPlayerInfo(protocol)
	self.snow_times = protocol.snow_times or 0
	self.intimidation_times = protocol.intimidation_times or 0
	self.snow_cd_time = protocol.snow_cd_time or 0
	self.intimidation_cd_time = protocol.intimidation_cd_time or 0
	self.total_exp = protocol.total_exp or 0
	self.gather_times = protocol.gather_times or 0
	self.anmo_cd_time = protocol.anmo_cd_time or 0
	self.anmo_times = protocol.anmo_times or 0
end

--获取采集相关信息
function HotSpringWGData:GetGatherInfo()
	return self.gather_times or 0
end

--采集物刷新时间  get set
function HotSpringWGData:GatherRefreshRemainTime( time )
	if nil == time then
		return self.gather_refresh_remain_time or 0
	end
	self.gather_refresh_remain_time = time
end

--按摩次数，恐吓次数，按摩cd，恐吓cd
function HotSpringWGData:GetHotspringPlayerInfo()
	return self.anmo_times, self.intimidation_times, self.anmo_cd_time, self.intimidation_cd_time, self.total_exp
end

function HotSpringWGData:GetHotspringTotalExp()
	return self.total_exp
end


-- function HotSpringWGData:SetHotspringPlayerInfo(protocol)
-- 	self.total_score = protocol.total_score or 0									-- 玩家拥有的总积分
-- 	self.got_score = protocol.got_score or 0 										-- 这次活动获取的积分
-- 	self.play_times = protocol.play_times or 0 										-- 这次活动已经参与三消游戏次数
-- 	self.cur_game_end_time = protocol.cur_game_end_time or 0	 					-- 当前次游戏结束时间
-- end

-- function HotSpringWGData:GetHotspringPlayerInfo()
-- 	return self.total_score, self.got_score, self.play_times
-- end

function HotSpringWGData:GetEndTime()
	return self.cur_game_end_time
end

function HotSpringWGData:SetHotspringMonsterInfo(protocol)
	self.monster_list = protocol.monster_list or {}
	self:PushMonsterList(self.monster_list)
end

function HotSpringWGData:GetHotspringMonsterList()
	return self.monster_list
end


function HotSpringWGData:GetMonsterCfg()
	return ConfigManager.Instance:GetAutoConfig("hotspring_auto").monster
end

function HotSpringWGData:GetEffectList(monster_list)
	local effect_pos_info = {}
	if nil ~= next(monster_list) then
		for k,pos_t in pairs(self.check_pos_list_t) do
			if monster_list[pos_t[1]] == monster_list[pos_t[2]]
					and monster_list[pos_t[2]] == monster_list[pos_t[3]] then
				table.insert(effect_pos_info, self.check_elimination_effect_t[k])
			end
		end
	end
	return effect_pos_info
end


function HotSpringWGData:GetVipLevelCfg(auth_type)
	local vip_level = ConfigManager.Instance:GetAutoConfig("vip_auto").level

	for k,v in pairs(vip_level) do
		if v.auth_type == auth_type then
			return v
		end
	end
end

function HotSpringWGData:GetCrossHotSpringOtherCfg()
	return self.cross_hotspring_auto.other[1] or {}
end

function HotSpringWGData:GetHotSpringOtherCfg()
	return self.hotspring_auto.other[1] or {}
end

function HotSpringWGData:GetIsRoleLevelLimit()
	local level_limit = (self:GetCrossHotSpringOtherCfg() and self:GetCrossHotSpringOtherCfg().level_lim) and self:GetCrossHotSpringOtherCfg().level_lim or 0
	if level_limit == 0 then
		return true
	end

	return RoleWGData.Instance:GetRoleLevel() <= level_limit
end

function HotSpringWGData:GetHotSpringRewardCfg()
	local other_cfg = self.hotspring_auto.other[1]
	local reward_list = {}
	-- if RoleWGData.Instance.role_vo.level >= other_cfg.normal_reward_open_level then
		reward_list = other_cfg.actiion_reward_item
	-- else
	-- 	reward_list = other_cfg.actiion_low_reward_item
	-- end
	return reward_list
end

function HotSpringWGData:SetHotspringShuangxiuInfo(protocol)
	-- self:SetShuangXiuState(protocol.is_cancel_shuangxiu, protocol.shuangxiu_list)
	self:SetShuangXiuAllList(protocol.is_cancel_shuangxiu, protocol.shuangxiu_list)
end

function HotSpringWGData:SetShuangXiuState(is_cancel_shuangxiu, shuangxiu_list)
	if is_cancel_shuangxiu == 0 then
		for k, v in pairs(shuangxiu_list) do
			local role_host = Scene.Instance:GetObjectByObjId(v.role_obj_id1)
			if role_host then
				-- role_host:DoStand()
				role_host:ShuangXiuState(SHUANGXIU_TYPE.IS_SHUANGXIU)
				role_host:ShuangXiuIdentity(1)
				role_host:SetShuangxiuPartner(v.role_obj_id2)
				role_host:UpdateBoat()
			end
			local role_parnert = Scene.Instance:GetObjectByObjId(v.role_obj_id2)

			if role_parnert then
				-- role_parnert:DoStand()
				role_parnert:ShuangXiuState(SHUANGXIU_TYPE.IS_SHUANGXIU)
				role_parnert:ShuangXiuIdentity(0)
				role_parnert:SetShuangxiuPartner(v.role_obj_id1)
				role_parnert:UpdateBoat()
			end
		end
	else
		for k, v in pairs(shuangxiu_list) do
			local role_host = Scene.Instance:GetObjectByObjId(v.role_obj_id1)

			if role_host then
				role_host:ShuangXiuState(SHUANGXIU_TYPE.NO_SHUANGXIU)
				role_host:ShuangXiuIdentity(-1)
			end
			local role_parnert = Scene.Instance:GetObjectByObjId(v.role_obj_id2)
			if role_parnert then
				role_host:ShuangXiuState(SHUANGXIU_TYPE.NO_SHUANGXIU)
				role_host:ShuangXiuIdentity(-1)
			end
		end
	end
end

function HotSpringWGData:SetShuangXiuAllList(is_cancel_shuangxiu, shuangxiu_list)
	--if is_cancel_shuangxiu == 0 then
	--	for k, v in pairs(shuangxiu_list) do
	--		table.insert(self.shuangxiu_all_list, v)
	--	end
	--else
	--	local delete_list = {}
	--	for _, t1 in pairs(shuangxiu_list) do
	--		for k,t2 in pairs(self.shuangxiu_all_list) do
	--			if (t1.role_obj_id1 == t2.role_obj_id1 and t1.role_obj_id2 == t2.role_obj_id2) or
	--				 (t1.role_obj_id1 == t2.role_obj_id2 and t1.role_obj_id2 == t2.role_obj_id1) then
	--				table.insert(delete_list, k)
	--			end
	--		end
    --
	--		for k,v in pairs(delete_list) do
	--			self.shuangxiu_all_list[v] = nil
	--		end
	--	end
	--end

	local delete_list = {}
	for _, t1 in pairs(shuangxiu_list) do
		if t1.action_type == HOTSPRING_BOAT_TYPE.SHUANGXIU then
			table.insert(self.shuangxiu_all_list, t1)
		elseif t1.action_type == HOTSPRING_BOAT_TYPE.ANMO then
			table.insert(self.shuangxiu_all_list, t1)
		elseif t1.action_type == HOTSPRING_BOAT_TYPE.NONE then
			--table.insert(self.shuangxiu_all_list, v)
			for k,t2 in pairs(self.shuangxiu_all_list) do
				if (t1.role_obj_id1 == t2.role_obj_id1 and t1.role_obj_id2 == t2.role_obj_id2) or
					 (t1.role_obj_id1 == t2.role_obj_id2 and t1.role_obj_id2 == t2.role_obj_id1) then
					table.insert(delete_list, k)
				end
			end
		end
	end
	for k,v in pairs(delete_list) do
		self.shuangxiu_all_list[v] = nil
	end
end

function HotSpringWGData:GetShuangXiuAllList()
	return self.shuangxiu_all_list
end

function HotSpringWGData:ClearShuangXiuAllList()
	self.shuangxiu_all_list = {}
end

--获取双修某个角色的双修信息, action_type = 1 双修， action_type = 2 按摩
function HotSpringWGData:GetShuangXiuRoleInfoById(role_obj_id)
	for k, v in pairs(self.shuangxiu_all_list) do
		if v.role_obj_id1 == role_obj_id or v.role_obj_id2 == role_obj_id then
			if v.role_obj_id1 == role_obj_id then
				return v
			else
				local tmp = v.role_obj_id2
				v.role_obj_id2 = v.role_obj_id1
				v.role_obj_id1 = tmp
				return v
			end
		end
	end
	return nil
end

--是否开启跨服温泉
function HotSpringWGData:IsKuaFuHotSping()
	local other_cfg = self:GetCrossHotSpringOtherCfg()
	local open_server_day = TimeWGCtrl.Instance:GetServerTime()
	local open_day_limit = other_cfg.openday_limit
	local level_limit = other_cfg.open_level
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	return role_level >= level_limit and open_server_day > open_day_limit
end

function HotSpringWGData:GetAddCrossDay()
	local other_cfg = self:GetCrossHotSpringOtherCfg()
	return other_cfg.openday_limit
end

function HotSpringWGData:GetAddCrossLevel()
	local other_cfg = self:GetCrossHotSpringOtherCfg()
	return other_cfg.open_level
end

function HotSpringWGData:GetRamdomBePoShuiStr()
	local random_idnex = math.random(1, self.bubble_po_shui_count)
	if self.bubble_po_shui_list[random_idnex] then
		return self.bubble_po_shui_list[random_idnex].bubble_text
	end
	return nil
end

function HotSpringWGData:GetbVipExePercent()
	local is_vip = VipWGData.Instance:IsVip()
	if is_vip then
		local vip_level = VipWGData.Instance:GetRoleVipLevel()
		local cfg = ConfigManager.Instance:GetAutoConfig("vip_auto").level
		if cfg and cfg[8] then
			local param = cfg[8]["param_"..vip_level]
			if param then
				return string.format("%s%%", math.floor(param/100))
			end
		end
	end

	return "0%"
end


--获取最大得经验加成值
function HotSpringWGData:GetVipExpMaxAdd()
	if self.max_exp_add_per_value then
		return self.max_exp_add_per_value
	end
	local cfg = VipWGData.Instance:GetVipSpecPermissions(8)
	local max_level = VipWGData.Instance:GetMaxVIPLevel()
	local exp_add_buff = 0
	for i = 1, max_level do
		if exp_add_buff < cfg["param_".. i - 1] then
			exp_add_buff = cfg["param_".. i - 1]
		end
	end
	self.max_exp_add_per_value = exp_add_buff
	return exp_add_buff
end

--设置当前操作类型 双修，按摩，泼热水, 采集 0-2
function HotSpringWGData:SetCurOperType(index)
	self.current_opera_type = index
end

function HotSpringWGData:GetCurOperType()
	return self.current_opera_type 
end