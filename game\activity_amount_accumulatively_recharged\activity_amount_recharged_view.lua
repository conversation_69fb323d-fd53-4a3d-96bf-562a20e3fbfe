ActivityAmountRechargedView = ActivityAmountRechargedView or BaseClass(SafeBaseView)

function ActivityAmountRechargedView:__init()
    self.view_layer = UiLayer.Normal

    self:SetMaskBg()

    self:AddViewResource(0, "uis/view/activity_amount_recharged_ui_prefab", "layout_amount_recharge_rank")
end

function ActivityAmountRechargedView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("amount_recharged_rank_down") then
        CountDownManager.Instance:RemoveCountDown("amount_recharged_rank_down")
    end

    if self.reward_rank_list then
        self.reward_rank_list:DeleteMe()
        self.reward_rank_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end
end

function ActivityAmountRechargedView:OpenCallBack()
    --请求信息的协议
    ActivityAmountRechargedWGCtrl.Instance:SendAmountRechargedRank(CROSS_CHONGZHI_RANK_OPERATE_TYPE.RANK_INFO)
end

function ActivityAmountRechargedView:LoadCallBack()
    --查看排行榜
    self.node_list["btn_rank"].button:AddClickListener(BindTool.Bind(self.OnClickRankView, self))

    --退出按钮
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))
    --奖励列表
    self.reward_rank_list = AsyncListView.New(RechargeRankRender, self.node_list["day_list"])

    --提示按钮
    self.node_list["btn_prompt"].button:AddClickListener(BindTool.Bind(self.PromptBtnClick, self))

    --活动时间文本的显示
    self.node_list["time_text"].text.text = Language.AmountRecharged.RechargeActTxt


    self:LoginTimeCountDown()
end

--
function ActivityAmountRechargedView:PromptBtnClick()
    local role_tip = RuleTip.Instance

    role_tip:SetContent(Language.AmountRecharged.RechargeRuleContent, Language.AmountRecharged.RechargeRankRule)
end

--计时器
function ActivityAmountRechargedView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
    .CROSS_CHANNEL_ACTIVITY_TYPE_CHONGZHI_RANK)
    if activity_data ~= nil then
        local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list["amount_down_time"].text.text = TimeUtil.FormatSecondDHM2(invalid_time -
            TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("amount_recharged_rank_down",
                BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
    end
end

function ActivityAmountRechargedView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.node_list["amount_down_time"].text.text = TimeUtil.FormatSecondDHM2(valid_time)
    end
end

function ActivityAmountRechargedView:OnComplete()
    self.node_list["amount_down_time"].text.text = TimeUtil.FormatSecondDHM2(0)
    self:Close()
end

function ActivityAmountRechargedView:OnFlush(param_t)
    for k, v in pairs(param_t) do
        self:FlushViewShow()
        if "rank_list" == k then
            self:LoadModel()
        end
    end
end

function ActivityAmountRechargedView:FlushViewShow()
    --获取自己的上榜情况
    local my_rank_data = ActivityAmountRechargedWGData.Instance:GetMyRankData()
    --获取到自己的累计金额
    local my_rank_value = ActivityAmountRechargedWGData.Instance:GetRankValue()
    --拿到配置表信息
    local recharged_rank_list = ActivityAmountRechargedWGData.Instance:GetRechargedRankCfg()
    if recharged_rank_list then
        self.reward_rank_list:SetDataList(recharged_rank_list)
    end

    --更新排名
    if my_rank_data > 0 then
        self.node_list["my_rank"].text.text = string.format(Language.AmountRecharged.RechargeRanking, my_rank_data)
    else
        self.node_list["my_rank"].text.text = Language.AmountRecharged.RankWeiShangBang
    end

    --更新累计金额
    self.node_list["my_total"].text.text = my_rank_value

    --活动说明
end

function ActivityAmountRechargedView:LoadModel()
    --获取到榜一的信息
    local model_data = ActivityAmountRechargedWGData.Instance:GetTopRankInfo()

    if model_data[1] == nil then
        return
    end

    if model_data[1].is_rank then
        --self.node_list["name"].text.text = Language.AmountRecharged.XuWeiYiDai
        self.node_list["norank_name"]:SetActive(true)
        self.node_list["wu_menber"]:SetActive(true)
        self.node_list["display"]:SetActive(false)
    else
        self.node_list["wu_menber"]:SetActive(false)
        self.node_list["display"]:SetActive(true)
        self.node_list["norank_name"]:SetActive(false)

        local flush_fun = function(protocol)
            if not self.node_list then
                return
            end
            if nil == self.model_display then
                self.model_display = RoleModel.New()
                local display_data = {
                    parent_node = self.node_list["display"],
                    camera_type = MODEL_CAMERA_TYPE.BASE,
                    -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
                    rt_scale_type = ModelRTSCaleType.M,
                    can_drag = true,
                }
                
                self.model_display:SetRenderTexUI3DModel(display_data)
                -- self.model_display:SetUI3DModel(self.node_list["display"].transform,
                --     self.node_list["display"].event_trigger_listener,
                --     1, false, MODEL_CAMERA_TYPE.BASE)
            end

            if self.model_display then
                local ignore_table = { ignore_wing = true, ignore_jianzhen = true, ignore_halo = true,
                    ignore_shouhuan = true, ignore_tail = true, ignore_waist = true }
                self.model_display:SetModelResInfo(protocol, ignore_table)
            end

            local name_data = Split(model_data[1].name, "_")
            local user_name = "[" .. name_data[2] .. "]" .. name_data[1]
            self.node_list["name"].text.text = user_name
        end
        BrowseWGCtrl.Instance:BrowRoelInfo(model_data[1].uuid.temp_low, flush_fun)
    end
end

function ActivityAmountRechargedView:OnClickRankView()
    ActivityAmountRechargedWGCtrl.Instance:OpenAmountRankView()
end

---------------列表格子
RechargeRankRender = RechargeRankRender or BaseClass(BaseRender)

function RechargeRankRender:LoadCallBack()
    self.item_cell_list = {}
end

function RechargeRankRender:__delete()
    for _, v in pairs(self.item_cell_list) do
        v:DeleteMe()
    end
    self.item_cell_list = nil
end

function RechargeRankRender:OnFlush()
    if self.data == nil then
        return
    end

    local desc = ""
    if self.data.min_rank == 1 then
        desc = string.format(Language.AmountRecharged.RechargeRanking, self.data.min_rank)
    else
        desc = string.format(Language.AmountRecharged.RechargeRankTitle2, self.data.min_rank, self.data.max_rank)
    end

    self.node_list["rank_title"].text.text = string.format(Language.AmountRecharged.RechargeRankTitle1, desc,
        self.data.reach_value)

    local item_list = self.item_cell_list
    if #self.data.reward_item > #item_list then
        local cell_parent = self.node_list["reward_group"]
        for i = 0, #self.data.reward_item do
            item_list[i] = item_list[i] or ItemCell.New(cell_parent)
        end
        self.item_list = item_list
    end

    for i = 0, #item_list do
        if self.data.reward_item[i] then
            item_list[i]:SetData(self.data.reward_item[i])
            item_list[i]:SetActive(true)
        else
            item_list[i]:SetActive(false)
        end
    end
end
