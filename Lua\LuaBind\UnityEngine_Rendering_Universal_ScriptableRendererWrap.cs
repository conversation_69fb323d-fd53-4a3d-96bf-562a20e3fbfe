﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Rendering_Universal_ScriptableRendererWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Rendering.Universal.ScriptableRenderer), typeof(System.Object));
		<PERSON><PERSON>RegFunction("SupportedCameraStackingTypes", SupportedCameraStackingTypes);
		<PERSON><PERSON>RegFunction("SupportsCameraStackingType", SupportsCameraStackingType);
		L.RegFunction("SetCameraMatrices", SetCameraMatrices);
		L.RegFunction("GetNestBuffer4Dest", GetNestBuffer4Dest);
		<PERSON><PERSON>RegFunction("GetCurDestBuffder", GetCurDestBuffder);
		<PERSON>.RegFunction("Dispose", Dispose);
		<PERSON>.RegFunction("ConfigureCameraTarget", ConfigureCameraTarget);
		<PERSON><PERSON>unction("Setup", Setup);
		<PERSON><PERSON>RegFunction("SetupLights", SetupLights);
		<PERSON><PERSON>RegFunction("SetupCullingParameters", SetupCullingParameters);
		<PERSON><PERSON>RegFunction("FinishRendering", FinishRendering);
		L.RegFunction("Execute", Execute);
		L.RegFunction("EnqueuePass", EnqueuePass);
		L.RegFunction("GameSwapColorBuffer", GameSwapColorBuffer);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("cameraColorTarget", get_cameraColorTarget, null);
		L.RegVar("cameraDepthTarget", get_cameraDepthTarget, null);
		L.RegVar("supportedRenderingFeatures", get_supportedRenderingFeatures, set_supportedRenderingFeatures);
		L.RegVar("unsupportedGraphicsDeviceTypes", get_unsupportedGraphicsDeviceTypes, set_unsupportedGraphicsDeviceTypes);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SupportedCameraStackingTypes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			int o = obj.SupportedCameraStackingTypes();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SupportsCameraStackingType(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.Universal.CameraRenderType arg0 = (UnityEngine.Rendering.Universal.CameraRenderType)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.Universal.CameraRenderType));
			bool o = obj.SupportsCameraStackingType(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCameraMatrices(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 1);
			UnityEngine.Rendering.Universal.CameraData arg1 = StackTraits<UnityEngine.Rendering.Universal.CameraData>.Check(L, 2);
			bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
			UnityEngine.Rendering.Universal.ScriptableRenderer.SetCameraMatrices(arg0, ref arg1, arg2);
			ToLua.PushValue(L, arg1);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNestBuffer4Dest(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			UnityEngine.Rendering.RenderTargetIdentifier o = obj.GetNestBuffer4Dest(arg0);
			ToLua.PushValue(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCurDestBuffder(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			UnityEngine.Rendering.RenderTargetIdentifier o = obj.GetCurDestBuffder(arg0);
			ToLua.PushValue(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Dispose(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			obj.Dispose();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ConfigureCameraTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.RenderTargetIdentifier arg0 = StackTraits<UnityEngine.Rendering.RenderTargetIdentifier>.Check(L, 2);
			UnityEngine.Rendering.RenderTargetIdentifier arg1 = StackTraits<UnityEngine.Rendering.RenderTargetIdentifier>.Check(L, 3);
			obj.ConfigureCameraTarget(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Setup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.ScriptableRenderContext arg0 = StackTraits<UnityEngine.Rendering.ScriptableRenderContext>.Check(L, 2);
			UnityEngine.Rendering.Universal.RenderingData arg1 = StackTraits<UnityEngine.Rendering.Universal.RenderingData>.Check(L, 3);
			obj.Setup(arg0, ref arg1);
			ToLua.PushValue(L, arg1);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetupLights(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.ScriptableRenderContext arg0 = StackTraits<UnityEngine.Rendering.ScriptableRenderContext>.Check(L, 2);
			UnityEngine.Rendering.Universal.RenderingData arg1 = StackTraits<UnityEngine.Rendering.Universal.RenderingData>.Check(L, 3);
			obj.SetupLights(arg0, ref arg1);
			ToLua.PushValue(L, arg1);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetupCullingParameters(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.ScriptableCullingParameters arg0 = StackTraits<UnityEngine.Rendering.ScriptableCullingParameters>.Check(L, 2);
			UnityEngine.Rendering.Universal.CameraData arg1 = StackTraits<UnityEngine.Rendering.Universal.CameraData>.Check(L, 3);
			obj.SetupCullingParameters(ref arg0, ref arg1);
			ToLua.PushValue(L, arg0);
			ToLua.PushValue(L, arg1);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FinishRendering(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			obj.FinishRendering(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Execute(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.ScriptableRenderContext arg0 = StackTraits<UnityEngine.Rendering.ScriptableRenderContext>.Check(L, 2);
			UnityEngine.Rendering.Universal.RenderingData arg1 = StackTraits<UnityEngine.Rendering.Universal.RenderingData>.Check(L, 3);
			obj.Execute(arg0, ref arg1);
			ToLua.PushValue(L, arg1);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EnqueuePass(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderPass arg0 = (UnityEngine.Rendering.Universal.ScriptableRenderPass)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderPass>(L, 2);
			obj.EnqueuePass(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GameSwapColorBuffer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer>(L, 1);
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			obj.GameSwapColorBuffer(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cameraColorTarget(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)o;
			UnityEngine.Rendering.RenderTargetIdentifier ret = obj.cameraColorTarget;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cameraColorTarget on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cameraDepthTarget(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)o;
			UnityEngine.Rendering.RenderTargetIdentifier ret = obj.cameraDepthTarget;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cameraDepthTarget on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportedRenderingFeatures(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)o;
			UnityEngine.Rendering.Universal.ScriptableRenderer.RenderingFeatures ret = obj.supportedRenderingFeatures;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportedRenderingFeatures on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_unsupportedGraphicsDeviceTypes(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)o;
			UnityEngine.Rendering.GraphicsDeviceType[] ret = obj.unsupportedGraphicsDeviceTypes;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index unsupportedGraphicsDeviceTypes on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_supportedRenderingFeatures(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)o;
			UnityEngine.Rendering.Universal.ScriptableRenderer.RenderingFeatures arg0 = (UnityEngine.Rendering.Universal.ScriptableRenderer.RenderingFeatures)ToLua.CheckObject<UnityEngine.Rendering.Universal.ScriptableRenderer.RenderingFeatures>(L, 2);
			obj.supportedRenderingFeatures = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportedRenderingFeatures on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_unsupportedGraphicsDeviceTypes(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.ScriptableRenderer obj = (UnityEngine.Rendering.Universal.ScriptableRenderer)o;
			UnityEngine.Rendering.GraphicsDeviceType[] arg0 = ToLua.CheckStructArray<UnityEngine.Rendering.GraphicsDeviceType>(L, 2);
			obj.unsupportedGraphicsDeviceTypes = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index unsupportedGraphicsDeviceTypes on a nil value");
		}
	}
}

