﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class DayNight_DayNightFeatureWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(DayNight.DayNightFeature), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("SetMoment", SetMoment);
		<PERSON><PERSON>unction("NextMoment", NextMoment);
		<PERSON><PERSON>unction("AutoPlay", AutoPlay);
		<PERSON><PERSON>ction("StopAutoPlay", StopAutoPlay);
		L<PERSON>RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON>.<PERSON>ar("dayNightData", get_dayNightData, set_dayNightData);
		<PERSON>.<PERSON>("translateDuration", get_translateDuration, set_translateDuration);
		<PERSON><PERSON>("smoothTranslate", get_smoothTranslate, set_smoothTranslate);
		<PERSON><PERSON>("currentMomentIndex", get_currentMomentIndex, set_currentMomentIndex);
		<PERSON><PERSON>("dayNightTarget", get_dayNightTarget, set_dayNightTarget);
		<PERSON><PERSON>("SelectdMomentIndex", get_SelectdMomentIndex, set_SelectdMomentIndex);
		L.RegVar("AutoPlaying", get_AutoPlaying, null);
		L.RegVar("Translating", get_Translating, null);
		L.RegVar("AutoPlayFrequency", get_AutoPlayFrequency, set_AutoPlayFrequency);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMoment(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				DayNight.DayNightFeature obj = (DayNight.DayNightFeature)ToLua.CheckObject<DayNight.DayNightFeature>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				obj.SetMoment(arg0);
				return 0;
			}
			else if (count == 3)
			{
				DayNight.DayNightFeature obj = (DayNight.DayNightFeature)ToLua.CheckObject<DayNight.DayNightFeature>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.SetMoment(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DayNight.DayNightFeature.SetMoment");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int NextMoment(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)ToLua.CheckObject<DayNight.DayNightFeature>(L, 1);
			obj.NextMoment();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AutoPlay(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				DayNight.DayNightFeature obj = (DayNight.DayNightFeature)ToLua.CheckObject<DayNight.DayNightFeature>(L, 1);
				obj.AutoPlay();
				return 0;
			}
			else if (count == 2)
			{
				DayNight.DayNightFeature obj = (DayNight.DayNightFeature)ToLua.CheckObject<DayNight.DayNightFeature>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				obj.AutoPlay(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DayNight.DayNightFeature.AutoPlay");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopAutoPlay(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)ToLua.CheckObject<DayNight.DayNightFeature>(L, 1);
			obj.StopAutoPlay();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_dayNightData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			DayNight.DayNightData ret = obj.dayNightData;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index dayNightData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_translateDuration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			float ret = obj.translateDuration;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index translateDuration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_smoothTranslate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			bool ret = obj.smoothTranslate;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index smoothTranslate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_currentMomentIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			int ret = obj.currentMomentIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index currentMomentIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_dayNightTarget(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			DayNight.DayNightTarget ret = obj.dayNightTarget;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index dayNightTarget on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SelectdMomentIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			int ret = obj.SelectdMomentIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SelectdMomentIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoPlaying(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			bool ret = obj.AutoPlaying;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoPlaying on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Translating(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			bool ret = obj.Translating;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Translating on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoPlayFrequency(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			float ret = obj.AutoPlayFrequency;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoPlayFrequency on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_dayNightData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			DayNight.DayNightData arg0 = (DayNight.DayNightData)ToLua.CheckObject<DayNight.DayNightData>(L, 2);
			obj.dayNightData = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index dayNightData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_translateDuration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.translateDuration = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index translateDuration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_smoothTranslate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.smoothTranslate = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index smoothTranslate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_currentMomentIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.currentMomentIndex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index currentMomentIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_dayNightTarget(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			DayNight.DayNightTarget arg0 = StackTraits<DayNight.DayNightTarget>.Check(L, 2);
			obj.dayNightTarget = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index dayNightTarget on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SelectdMomentIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SelectdMomentIndex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SelectdMomentIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AutoPlayFrequency(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			DayNight.DayNightFeature obj = (DayNight.DayNightFeature)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.AutoPlayFrequency = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoPlayFrequency on a nil value");
		}
	}
}

