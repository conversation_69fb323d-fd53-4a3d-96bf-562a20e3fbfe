BZTurnTableWGData = BZTurnTableWGData or BaseClass()

BZTurnTableWGData.DIF_ANGLE = 20
BZTurnTableWGData.MAX_RANK = 3
BZTurnTableWGData.MAX_LAYER = 3

function BZTurnTableWGData:__init()
	if BZTurnTableWGData.Instance then
		ErrorLog("[BZTurnTableWGData] Attemp to create a singleton twice !")
	end
	self.angle_list = {}
	self.result = {}
	self.layer_info = {}
	self.reward_list = {}
	self.record = {}
	self.near_record_time = 0
	BZTurnTableWGData.Instance = self

	self.act_cfg = ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto")
	self.gear_cfg = ListToMap(self.act_cfg.tunvlang_layer, "layer")
	self.week_cfg = ListToMap(self.act_cfg.tunvlang_week, "layer", "round_count")
	self.reward_cfg = ListToMap(self.act_cfg.tunvlang_reward, "reward_id")
	self.cost_cfg = ListToMapList(self.act_cfg.tunvlang_cost, "layer")
	self.record_cfg = ListToMap(self.act_cfg.tunvlang_record, "prop_id")

	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_Turntable, BindTool.Bind(self.IsShowTurnTableRed, self))
	self:RegisterRewardRemindInBag(RemindName.QuanMinBeiZhan_Turntable)
end

function BZTurnTableWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_Turntable)

	BZTurnTableWGData.Instance = nil
end

function BZTurnTableWGData:RegisterRewardRemindInBag(remind_name)
    local map = {}

	local len = #self.gear_cfg
	for i = 1, len do
		local draw_info = self:GetTurnTableCurDrawInfoByLayer(i) 
		if draw_info then
			map[draw_info.draw_consume_item_id] = true
		end
	end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function BZTurnTableWGData:GetTurnTableIsOpen()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACT_BZ_TURNTABLE) then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cfg = QuanMinBeiZhanWGData.Instance:GetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_turntable)

	return cfg and role_level >= cfg.open_level
end

function BZTurnTableWGData:ClearActCache(act_type, status)
	if act_type ~= ACTIVITY_TYPE.RAND_ACT_BZ_TURNTABLE then
		return
	end

	self.layer_list = nil

	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid
	PlayerPrefsUtil.SetInt("bz_turntable" .. "_ONCE" .. role_id, 0)

	if status then
		local layer_list = self:GetCurActLayerList()
		for i, v in pairs(layer_list) do
			QuanMinBeiZhanWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.LAYER_INFO, v)
		end
	end
end

function BZTurnTableWGData:GetCurActLayerList()
	if not self.layer_list then
		self.layer_list = {}
		local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.RAND_ACT_BZ_TURNTABLE)
		for i, v in pairs(self.act_cfg.tunvlang_config_param) do
			if open_day >= v.start_server_day and open_day <= v.end_server_day then
				local t = Split(v.layer, "|")
				for i, v in ipairs(t) do
					table.insert(self.layer_list, tonumber(v))
				end
				break
			end
		end
	end
	return self.layer_list
end

function BZTurnTableWGData:GetLayerCfgByLayerNum(layer_num)
	return self.gear_cfg[layer_num] or {}
end

function BZTurnTableWGData:GetWeekCfgByLayerNum(layer_num, round_count)
	return self.week_cfg[layer_num] and self.week_cfg[layer_num][round_count] or {}
end

function BZTurnTableWGData:GetTurnTableRewardListByLayer(layer_num)
	--获取对应层数的奖励列表
	if not self.reward_list[layer_num] and self.layer_info[layer_num] then
		local reward_list = self.layer_info[layer_num].draw_reward_id_list or {}
		for i = #reward_list,1,-1 do
			if reward_list[i] == 0 then
				table.remove(reward_list, i)
			else
				break
			end
		end
		self.reward_list[layer_num] = reward_list
	end
	return self.reward_list[layer_num] or {}
end

function BZTurnTableWGData:GetTurnTableRewardIsShowByLayerAndIndex(layer_num, index)
	--根据层数和id判断这个奖励有没有抽取
	local flag = self.layer_info[layer_num] and self.layer_info[layer_num].draw_result_flag or 0
    return bit:_and(flag, bit:_lshift(1, index-1)) == 0
end

function BZTurnTableWGData:GetTurnTableRewardInfoById(id)
	--根据id取奖励信息
	return self.reward_cfg[id]
end

function BZTurnTableWGData:GetTurnTableRoundTimeByLayer(layer_num)
	--根据层数取得当前的剩余时间和当前round
	--周期刷新问题从服务器拿
	local layer_info = self.layer_info[layer_num] or {}
	if layer_info.reward_round_id and layer_info.last_refresh_time then
		local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
		local round_time = layer_cfg.round_continue_time_h or 0
		local round_time_list = string.split(round_time, ",")
		round_time = tonumber(round_time_list[layer_info.reward_round_id + 1]) or 0

		local next_time = layer_info.last_refresh_time + round_time * 3600 - TimeWGCtrl.Instance:GetServerTime()
		return next_time, layer_info.reward_round_id
	end
	return 0, -1
end

function BZTurnTableWGData:GetTurnTableIsDrawEmptyByLayer(layer_num)
	--根据层数判断当前层有没有抽空
	local reward_list = self:GetTurnTableRewardListByLayer(layer_num)
	local num = self:GetLayerRewardNumByLayer(layer_num)
	return #reward_list <= num
end

function BZTurnTableWGData:GetLayerRewardNumByLayer(layer_num)
	local flag = self.layer_info[layer_num] and self.layer_info[layer_num].draw_result_flag or 0
    return bit:d2b1n(flag)
end

function BZTurnTableWGData:GetTurnTableAngleByLayerAndRank(layer, rank)
	--根据层数和圈数取当前圈的角度
	layer = layer or 1
	return self.angle_list[layer * BZTurnTableWGData.MAX_LAYER + rank]
end

function BZTurnTableWGData:CacheTurnTableAngleByLayerAndRank(layer, rank, angle)
	self.angle_list[layer * BZTurnTableWGData.MAX_LAYER + rank] = angle
end

function BZTurnTableWGData:GetTurnTableRecordByItemId(prop_id)
	return self.record_cfg and self.record_cfg[prop_id]
end


function BZTurnTableWGData:GetTurnTableCurDrawInfoByLayer(layer_num)
	--根据层数从协议拿当前的抽奖信息
	if not self.cost_cfg[layer_num] then
		return nil
	end

	local t = {}
	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	t.conmsum_xianyu = layer_cfg.conmsum_xianyu
	t.draw_consume_item_id = layer_cfg.draw_consume_item_id
	local draw_num = self:GetLayerRewardNumByLayer(layer_num)
	draw_num = draw_num + 1
	for i, v in pairs(self.cost_cfg[layer_num]) do
		if draw_num >= v.lower_limit_of_extraction and draw_num <= v.extract_upper_limit then
			t.draw_consume_item_count = v.draw_consume_item_count
			break
		end
	end
	return t
end

function BZTurnTableWGData:GetAllDrawInfo(layer_num)
	local cfg = self.cost_cfg[layer_num]
	if not cfg then
		return nil
	end

	local t = {}
	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	t.draw_consume_item_id = layer_cfg.draw_consume_item_id
	local draw_num = self:GetLayerRewardNumByLayer(layer_num)
	draw_num = draw_num + 1
	t.draw_consume_item_count = 0
	local times
	for i, v in pairs(cfg) do
		if draw_num < v.lower_limit_of_extraction then
			times = v.extract_upper_limit - v.lower_limit_of_extraction + 1
			t.draw_consume_item_count = t.draw_consume_item_count + v.draw_consume_item_count
		elseif draw_num >= v.lower_limit_of_extraction and draw_num <= v.extract_upper_limit then
			times = v.extract_upper_limit - draw_num + 1
			t.draw_consume_item_count = t.draw_consume_item_count + times * v.draw_consume_item_count
		end
	end
	return t
end

function BZTurnTableWGData:GetRemainDrawTimesByLayer(layer_num)
	--根据层数判断当前还有多少个奖励
	local reward_list = self:GetTurnTableRewardListByLayer(layer_num)
	local num = self:GetLayerRewardNumByLayer(layer_num)
	return #reward_list - num
end

function BZTurnTableWGData:CheckAngleHasReward(layer_num, angle, rank)
	--获取当前的奖励列表
	local reward_list = self:GetTurnTableRewardListByLayer(layer_num)
	for i, v in pairs(reward_list) do
		if self:GetTurnTableRewardIsShowByLayerAndIndex(layer_num, i) then
			local cfg = self:GetTurnTableRewardInfoById(v)
			--筛选rank，筛选是否领取
			if cfg and cfg.rank == rank then
				local rot_angle = Vector2.Angle(self.turntable_win_vector2, Vector2(cfg.x, cfg.y))
				local direct = (cfg.x * self.turntable_win_vector2.y - cfg.y * self.turntable_win_vector2.x)
				if direct > 0 then--顺时针角度
					rot_angle = 360 - rot_angle
				end

				local dif_angle = math.abs(rot_angle - angle)%360
				local tolerance = rot_angle - angle > 0 and -1 or 1
				--遍历是否相似角度
				if dif_angle <= BZTurnTableWGData.DIF_ANGLE or (360 - dif_angle) <= BZTurnTableWGData.DIF_ANGLE then
					local rand_angle = GameMath.Rand(30,45)
					tolerance = tolerance * (rand_angle - dif_angle)
					return tolerance
				end
			end
		end
	end
	return 0
end

function BZTurnTableWGData:GetCircleRotNum()
	--其他表的3个圈的圈数和持续时间
	local cricle_num = {3,4,5}
	local rot_time = 6
	return cricle_num, rot_time
end

function BZTurnTableWGData:CheckCanResetByLayer(layer_num)
	--大奖已被抽取
	local big_reward_exist = self:GetBigRewardExistByLayer(layer_num)
	if big_reward_exist then
		return false
	end
	--剩余奖励数量

	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	if layer_cfg.is_auto_refresh == 0 then
		return false
	end

	local remains = self:GetRemainDrawTimesByLayer(layer_num)
	if remains > layer_cfg.remaining_incentive_limit then
		return false
	end
	--本周期存在后续奖池
	return self:CheckHasNextPool(layer_num)
end

function BZTurnTableWGData:CheckHasNextRound(layer_num)
	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	local cur_round_count = self:GetCurRoundByLayer(layer_num) + 1
	local round_list = Split(layer_cfg.round_count, ",")
	local max_round_count = #round_list

	return cur_round_count < max_round_count
end

function BZTurnTableWGData:CheckHasNextPool(layer_num)
	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	local cur_round_count = self:GetCurRoundByLayer(layer_num)
	local round_list = Split(layer_cfg.round_count, ",")
	local max_round_count = #round_list

	local cur_pool_count = self:GetCurPoolByLayer(layer_num) + 1
	local reward_pool_id = self:GetWeekCfgByLayerNum(layer_num, cur_round_count).reward_pool_id
	if not reward_pool_id then
		return false
	end

	local pool_list = Split(reward_pool_id, ",")
	local max_pool_count = #pool_list

	return cur_pool_count < max_pool_count
end

function BZTurnTableWGData:GetBigRewardExistByLayer(layer_num)
    local list = self:GetTurnTableRewardListByLayer(layer_num)
    local cfg
    for i, v in pairs(list) do
        cfg = self:GetTurnTableRewardInfoById(v)
		if cfg.is_best == 1 then
			local is_show = self:GetTurnTableRewardIsShowByLayerAndIndex(layer_num, i)
			return is_show
		end
    end
    return false
end

function BZTurnTableWGData:GetNoticeDelayTime()
	local time
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	if PlayerPrefsUtil.GetInt("bz_turntable" .. "_ANIM" .. role_id) == 1 then
		time = 0
	else
		local _
		_, time = self:GetCircleRotNum()
	end
	time = time + 1--滑落时间
	return time
end

function BZTurnTableWGData:IsShowTurnTableRed()
	local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACT_BZ_TURNTABLE)
	if not act_is_open then
		return 0
	end

	--刷新下一周期了，给一次红点，这个要服务器下发
	local layer_list = BZTurnTableWGData.Instance:GetCurActLayerList()
	for i,v in ipairs(layer_list) do
		if self:IsShowRedByLayer(v) then
			return 1
		end
	end	

	return 0
end

function BZTurnTableWGData:IsShowRedByLayer(layer)
	--可重置了有个红点
	if self:CheckCanResetByLayer(layer) then
		return true
	end

	--检测道具够了有个红点（当前挡位道具抽完了？？）
	if self:GetTurnTableIsDrawEmptyByLayer(layer) then
		return false
	end

	local cur_draw_info = self:GetTurnTableCurDrawInfoByLayer(layer)
	if cur_draw_info.draw_consume_item_count == nil then
		return false
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	if num >= cur_draw_info.draw_consume_item_count then
		return true
	end

	-- if RoleWGData.Instance:GetRoleVo().gold >= cur_draw_info.conmsum_xianyu * cur_draw_info.draw_consume_item_count then
	-- 	return true
	-- end

	return false
end

function BZTurnTableWGData:GetTurnTableCurDrawResultByLayer(layer_num)
	if self.result.layer ~= layer_num then
		return {}
	end

	local reward_list = self:GetTurnTableRewardListByLayer(layer_num)
	local reward_id = reward_list[self.result.slot + 1]

	return self:GetTurnTableRewardInfoById(reward_id)
end

function BZTurnTableWGData:CalNewRecordNum()
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	if self.near_record_time ~= 0 then
		local count = 0
		for i, v in pairs(self.record) do
			if v.timestamp > self.near_record_time and role_id ~= v.uid then
				count = count + 1
			end
		end
		self:SetNewRecordCount(count)
	end
end

function BZTurnTableWGData:SetNewRecordCount(count)
	self.new_record_count = count
end

function BZTurnTableWGData:GetNewRecordCount()
	return self.new_record_count or 0
end

function BZTurnTableWGData:ClearRecordCount()
	self.near_record_time = TimeWGCtrl.Instance:GetServerTime()
	self.new_record_count = 0
end

function BZTurnTableWGData:GetRecordInfo()
	return self.record
end

function BZTurnTableWGData:GetCurPoolByLayer(layer_num)
	return self.layer_info[layer_num] and self.layer_info[layer_num].reward_pool_id or 0
end

function BZTurnTableWGData:GetCurRoundByLayer(layer_num)
	return self.layer_info[layer_num] and self.layer_info[layer_num].reward_round_id or 0
end

function BZTurnTableWGData:GetLayerInfo(layer_num)
	return self.layer_info[layer_num] or {}
end

function BZTurnTableWGData:SetLayerInfo(protocol)
	self.layer_info[protocol.layer] = protocol.layer_info
	self.reward_list[protocol.layer] = nil
	for i = 1, BZTurnTableWGData.MAX_RANK do
		if self.angle_list[protocol.layer * BZTurnTableWGData.MAX_LAYER + i] == nil then
			self.angle_list[protocol.layer * BZTurnTableWGData.MAX_LAYER + i] = protocol.layer_info.dirs[i]
		end
	end
end

function BZTurnTableWGData:SetRecordInfo(protocol)
	--筛选数目
	self.record = protocol.record_list
	self:CalNewRecordNum()
end

function BZTurnTableWGData:SetResultInfo(protocol)
	self.result.layer = protocol.layer
    self.result.slot = protocol.hit_slot
end

function BZTurnTableWGData:SetWinVector2(v2)
	self.turntable_win_vector2 = v2
end