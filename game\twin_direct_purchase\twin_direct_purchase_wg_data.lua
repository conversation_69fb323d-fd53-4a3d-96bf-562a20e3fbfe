TwinDirectPurchaseData = TwinDirectPurchaseData or BaseClass()

function TwinDirectPurchaseData:__init()
	if TwinDirectPurchaseData.Instance then
		error("[TwinDirectPurchaseData] Attempt to create singleton twice!")
		return
	end

    TwinDirectPurchaseData.Instance = self

    self:InitParam()
    self:InitConfig()
end

function TwinDirectPurchaseData:__delete()
    TwinDirectPurchaseData.Instance = nil
end

function TwinDirectPurchaseData:InitParam()
    self.grade = -1
    self.rmb_buy_flag_list = {}
end

function TwinDirectPurchaseData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_avatar_rmb_buy_auto")
    self.shop_cfg = ListToMap(cfg.shop, "grade", "rmb_buy_flag")
    self.color_cfg = cfg.color_cfg
end

function TwinDirectPurchaseData:SetAllInfo(protocol)
    self.grade = protocol.grade
    self.rmb_buy_flag_list = protocol.rmb_buy_flag
end

function TwinDirectPurchaseData:GetCurGrade()
    return self.grade
end

function TwinDirectPurchaseData:GetCurGradeCfg()
    return self.shop_cfg[self.grade] or {}
end

function TwinDirectPurchaseData:GetCurGradeCfgBySeq(seq)
    return (self.shop_cfg[self.grade] or {})[seq]
end

function TwinDirectPurchaseData:GetCurGradeBuyFlag(seq)
    return self.rmb_buy_flag_list[seq] or 0
end

function TwinDirectPurchaseData:GetCurGradeHasCanBuy()
    local twin_cfg = self:GetCurGradeCfg()
    if not IsEmptyTable(twin_cfg) then
        for k, v in pairs(twin_cfg) do
            local buy_times = self:GetCurGradeBuyFlag(v.rmb_buy_flag)
            if v.buy_times > buy_times then
                return false
            end
        end
    end

    return true
end

function TwinDirectPurchaseData:GetGiftShowList()
    local show_list = {}

    local cfg_list = self:GetCurGradeCfg()
    for k, v in pairs(cfg_list) do
        local buy_times = self:GetCurGradeBuyFlag(v.rmb_buy_flag)
        if v.pre_seq == -1 then
            if buy_times < v.buy_times then
                table.insert(show_list, v)
            end
        else
            local pre_cfg = cfg_list[v.pre_seq]
            local pre_buy_times = self:GetCurGradeBuyFlag(v.pre_seq)
            if buy_times < v.buy_times and pre_buy_times >= pre_cfg.buy_times then
                table.insert(show_list, v)
            end
        end
    end

    return show_list
end

--获取配色配置.
function TwinDirectPurchaseData:GetColorCfgByColorType(color_type)
    return self.color_cfg[color_type]
end