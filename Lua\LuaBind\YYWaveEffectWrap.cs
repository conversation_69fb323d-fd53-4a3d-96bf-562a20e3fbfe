﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class YYWaveEffectWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(YYWaveEffect), typeof(PostEffectBase));
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>un<PERSON>("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("waveStrength", get_waveStrength, set_waveStrength);
		<PERSON><PERSON>("totalFactor", get_totalFactor, set_totalFactor);
		<PERSON>.<PERSON>ar("waveWith", get_waveWith, set_waveWith);
		<PERSON><PERSON>lass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_waveStrength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYWaveEffect obj = (YYWaveEffect)o;
			float ret = obj.waveStrength;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index waveStrength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_totalFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYWaveEffect obj = (YYWaveEffect)o;
			float ret = obj.totalFactor;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index totalFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_waveWith(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYWaveEffect obj = (YYWaveEffect)o;
			float ret = obj.waveWith;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index waveWith on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_waveStrength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYWaveEffect obj = (YYWaveEffect)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.waveStrength = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index waveStrength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_totalFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYWaveEffect obj = (YYWaveEffect)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.totalFactor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index totalFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_waveWith(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			YYWaveEffect obj = (YYWaveEffect)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.waveWith = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index waveWith on a nil value");
		}
	}
}

