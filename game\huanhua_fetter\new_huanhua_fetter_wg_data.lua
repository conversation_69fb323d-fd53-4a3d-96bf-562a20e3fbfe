NewHuanHuaFetterWGData = NewHuanHuaFetterWGData or BaseClass()

function NewHuanHuaFetterWGData:__init()
	if NewHuanHuaFetterWGData.Instance ~= nil then
		<PERSON>rror<PERSON>og("[NewHuanHuaFetterWGData] attempt to create singleton twice!")
		return
	end

	NewHuanHuaFetterWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("huanhua_fetter_cfg_auto")
    self.toggle_show_cfg = ListToMap(cfg.toggle_show, "big_type", "small_type")
    self.theme_cfg = ListToMap(cfg.theme, "big_type", "small_type")
	self.theme_seq_cfg = ListToMap(cfg.theme, "seq")
	self.activation_cfg = ListToMap(cfg.activation, "seq", "part")
	self.reward_cfg = ListToMap(cfg.reward, "seq")
	self.activation_type_cfg = ListToMap(cfg.activation, "type", "param1", "param2")
	self.other_cfg = cfg.other[1]
	self.attr_cfg = ListToMap(cfg.attr, "seq", "num")

    self.suit_list = {}
	self.fetter_reward_info_list = {}
	self.reward_cfg_cache = {}
	self:InitCfg()
	
	RemindManager.Instance:Register(RemindName.NewHuanHuaFetterView, BindTool.Bind(self.GetHuanHuaFetterRemind, self))
end

function NewHuanHuaFetterWGData:__delete()
    NewHuanHuaFetterWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.NewHuanHuaFetterView)
end

----------------------------------------------------remind_start--------------------------------------------------------
function NewHuanHuaFetterWGData:GetHuanHuaFetterRemind()
	local data_list = self:GetTotalToggleShowBigTypeDataList()

	if not IsEmptyTable(data_list) then
		for k, v in pairs(data_list) do
			if self:GetFetterBigTypeRemind(k) then
				return 1
			end
		end
	end

	return 0
end

-- 可领取  可激活套装
function NewHuanHuaFetterWGData:GetFetterBigTypeRemind(big_type)
	if not self:IsCanShowHuanHuaFetterType(big_type) then
		return false	
	end

	local target_data_list = NewHuanHuaFetterWGData.Instance:GetToggleShowBigTypeDataList(big_type)

	if not IsEmptyTable(target_data_list) then
		for k, v in pairs(target_data_list) do
			if self:GetFetterSmallTypeRemind(big_type, v.small_type) then
				return true
			end
		end
	end

	return false
end

-- 具体小类型红点
function NewHuanHuaFetterWGData:GetFetterSmallTypeRemind(big_type, small_type)
	if not self:IsCanShowTheme(big_type, small_type) then
		return false
	end

	local theme_cfg = self:GetThemeCfgByType(big_type, small_type)

	if theme_cfg then
		-- 奖励能够领取
		if self:IsCanGetReward(theme_cfg.reward_seq) then
			return true
		end

		-- 能激活套装
		if self:IsHasCanActiveSuitCfg(theme_cfg.seq) then
			return true
		end
	end

	return false
end
------------------------------------------------------remind_end-------------------------------------------------------

------------------------------------------------protocol_start---------------------------------------------------
function NewHuanHuaFetterWGData:SetSCHuanhuaFetterRewardItemInfo(protocol)
    self.fetter_reward_info_list = protocol.fetter_reward_info_list
end

function NewHuanHuaFetterWGData:SCHuanhuaFetterRewardItemUpdate(protocol)
	self.fetter_reward_info_list[protocol.seq] = protocol.fetter_reward_info
end

function NewHuanHuaFetterWGData:SetAllSuitStateInfo(protocol)
	self.suit_list = protocol.suit_list
end

function NewHuanHuaFetterWGData:SetSingleSuitStateInfo(protocol)
	self.suit_list[protocol.update_suit] = protocol.part_list
end

function NewHuanHuaFetterWGData:GetHuanhuaFetterRewardItemData(seq)
	return self.fetter_reward_info_list[seq]
end

-- 1.表示在系统中已经激活   2  表示在套装中已经激活 + 1
function NewHuanHuaFetterWGData:GetSuitPartStateByData(seq, part)
	return (self.suit_list[seq] or {})[part]
end

function NewHuanHuaFetterWGData:IsGetRewardCfg(seq)
	return ((self.fetter_reward_info_list[seq] or {}).is_fetch or 0) == 1
end
-------------------------------------------------protocol_end----------------------------------------------------

------------------------------------------------cfg_get_start---------------------------------------------------
function NewHuanHuaFetterWGData:InitCfg()
	local reward_cfg_cache = {}
	for k, v in pairs(self.reward_cfg) do
		reward_cfg_cache[v.seq] = reward_cfg_cache[v.seq] or {}
		local active_limit_data = string.split(v.active_limit, "|")

		if not IsEmptyTable(active_limit_data) then
			for i, u in pairs(active_limit_data) do
				local item_data = string.split(u, ",")

				local param1 = tonumber(item_data[1])
				local param2 = tonumber(item_data[2])
				table.insert(reward_cfg_cache[v.seq], {type = v.type, param1 = param1, param2 = param2})
			end
		end
	end

	self.reward_cfg_cache = reward_cfg_cache
end

function NewHuanHuaFetterWGData:GetThemeBigTypeDataList(big_type)
	return self.theme_cfg[big_type]
end

function NewHuanHuaFetterWGData:GetThemeCfgByType(big_type, small_type)
	return (self.theme_cfg[big_type] or {})[small_type]
end

function NewHuanHuaFetterWGData:GetToggleShowBigTypeDataList(big_type)
	return self.toggle_show_cfg[big_type]
end

function NewHuanHuaFetterWGData:GetTotalToggleShowBigTypeDataList()
	return self.toggle_show_cfg
end

function NewHuanHuaFetterWGData:GetActivationDataListCfgBySeq(seq)
	return self.activation_cfg[seq]
end

function NewHuanHuaFetterWGData:GetRewardCfgBySeq(seq)
	return self.reward_cfg[seq]
end

function NewHuanHuaFetterWGData:GetRewardCfgCacheBySeq(seq)
	return self.reward_cfg_cache[seq]
end

function NewHuanHuaFetterWGData:GetActivationTypeCfg(type, param1, param2)
	return ((self.activation_type_cfg[type] or {})[param1] or {})[param2]
end

function NewHuanHuaFetterWGData:GetThemeSeqCfgBySeq(seq)
	return self.theme_seq_cfg[seq]
end

function NewHuanHuaFetterWGData:GetOtherCfg()
	return self.other_cfg
end

function NewHuanHuaFetterWGData:GetAttrDataListCfg(seq)
	return self.attr_cfg[seq]
end
------------------------------------------------cfg_get_end---------------------------------------------------

------------------------------------------------cal_start---------------------------------------------------
function NewHuanHuaFetterWGData:IsCanShowHuanHuaFetterType(type)
	local data_list = self:GetThemeBigTypeDataList(type)

	if IsEmptyTable(data_list) then
		return false
	end

	for k, v in pairs(data_list) do
		if self:IsCanShowTheme(v.big_type, v.small_type) then
			return true
		end
	end

	return false
end

-- function NewHuanHuaFetterWGData:IsCanShowVerTabIndex(tabindex)
-- 	local data_list = self:GetThemeBigTypeDataList(math.floor(tabindex / 10))

-- 	if IsEmptyTable(data_list) then
-- 		return false
-- 	end

-- 	for k, v in pairs(data_list) do
-- 		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
-- 		local role_level = RoleWGData.Instance:GetRoleLevel()

-- 		local can_open = false
-- 		if v.condition_type == 1 then
-- 			can_open = open_day >= v.skynumber_show and role_level >= v.level_show
-- 		elseif v.condition_type == 2 then
-- 			can_open = open_day >= v.skynumber_show or role_level >= v.level_show
-- 		end

-- 		if can_open then
-- 			return true
-- 		end
-- 	end

-- 	return false
-- end

function NewHuanHuaFetterWGData:GetActivationDataListCfgByType(big_type, small_type)
	local theme_cfg = self:GetThemeCfgByType(big_type, small_type)
	local data_list = {}
	local big_reward_data = {}

	if theme_cfg then
		local target_data_list = self:GetActivationDataListCfgBySeq(theme_cfg.seq)

		if not IsEmptyTable(target_data_list) then
			for k, v in pairs(target_data_list) do
				if v.is_reward_item ~= 1 then
					table.insert(data_list, v)
				else
					big_reward_data = v
				end
			end
		end
	end

	return data_list, big_reward_data
end

function NewHuanHuaFetterWGData:GetActItemId(data)
	local act_id = 0
	if IsEmptyTable(data) then
		return act_id
	end

	local cfg

	if data.type == WARDROBE_PART_TYPE.FASHION then
		cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		act_id = NewAppearanceWGData.Instance:GetFashionItemId(data.param1, data.param2)
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then
		cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		act_id = cfg and cfg.active_item_id or 0
	-- elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then
	-- 	cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
	-- 	act_id = cfg and cfg.active_item_id or 0
	-- elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then
	-- 	cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
	-- 	act_id = cfg and cfg.active_need_item_id or 0
	-- elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then
	-- 	cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
	-- 	act_id = cfg and cfg.item_id or 0
	end

	return act_id, cfg
end

-- function NewHuanHuaFetterWGData:IsActItemIsHuanHua(data)
-- 	local is_huanhua = false
-- 	if IsEmptyTable(data) then
-- 		return is_huanhua
-- 	end

-- 	-- 魂息 法宝
-- 	if data.type == WARDROBE_PART_TYPE.FASHION then
-- 		local used_index = NewAppearanceWGData.Instance:GetFashionUseIndex(data.param1)
-- 		local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(data.param1, data.param2)
-- 		is_huanhua = is_act and data.param2 == used_index
-- 	elseif data.type == WARDROBE_PART_TYPE.MOUNT then
-- 		local cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
-- 		local all_info = NewAppearanceWGData.Instance:GetQiChongInfo(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
-- 		if cfg and all_info then
-- 			is_huanhua = cfg.appe_image_id == all_info.used_imageid
-- 		end
-- 	end

-- 	return is_huanhua
-- end

-- 能否激活特殊奖励
function NewHuanHuaFetterWGData:IsCanGetReward(seq)
	if self:IsGetRewardCfg(seq) then
		return false
	end

	local active_data_list = self:GetRewardCfgCacheBySeq(seq)

	if not IsEmptyTable(active_data_list) then
		local act_num = 0

		for k, v in pairs(active_data_list) do
			local act_cfg = self:GetActivationTypeCfg(v.type, v.param1, v.param2)

			if act_cfg then
				local state = self:GetSuitPartStateByData(act_cfg.seq, act_cfg.part)
				if state == REWARD_STATE_TYPE.CAN_FETCH or state == REWARD_STATE_TYPE.FINISH then
					act_num = act_num + 1
				end
			end
		end


		if act_num == #active_data_list and act_num > 0 then
			return true
		end
	end

	return false
end

-- 获取套装已经激活的件数
-- REWARD_STATE_TYPE.FINISH      -- 已经激活  套装激活数量+ 1   已经激活了套装标记 
-- REWARD_STATE_TYPE.CAN_FETCH   -- 已完成 表示在系统中已经激活 道具已经激活  但是未激活套装标记
function NewHuanHuaFetterWGData:GetHuanHuaFetterActiveNum(seq)
	-- 转成reward_seq
	local theme_cfg = self:GetThemeSeqCfgBySeq(seq)
	local active_num = 0
	local complete_num = 0

	if IsEmptyTable(theme_cfg) then
		return active_num, complete_num		
	end
	
	local active_data_list = self:GetActivationDataListCfgBySeq(theme_cfg.reward_seq)

	if not IsEmptyTable(active_data_list) then
		for k, v in pairs(active_data_list) do
			local act_cfg = self:GetActivationTypeCfg(v.type, v.param1, v.param2)

			if act_cfg then
				local state = self:GetSuitPartStateByData(act_cfg.seq, act_cfg.part)

				if state == REWARD_STATE_TYPE.FINISH then
					active_num = active_num + 1
					complete_num = complete_num + 1
				elseif state == REWARD_STATE_TYPE.CAN_FETCH then
					complete_num = complete_num + 1
				end
			end
		end
	end

	return active_num, complete_num
end

-- 是否存在能激活的套装   大坑(每个部位是需要玩家主动去请求激活的 然后根据激活的部位数量 后端自动激活套装)
function NewHuanHuaFetterWGData:IsHasCanActiveSuitCfg(seq)
	local attr_data_list = self:GetAttrDataListCfg(seq)
    local active_num, complete_num = self:GetHuanHuaFetterActiveNum(seq)
    local active_data = {}

    for k, v in pairs(attr_data_list) do
        if v.num <= complete_num and active_num < v.num then
            active_data = v
            break
        end
    end

	return not IsEmptyTable(active_data), active_data
end

function NewHuanHuaFetterWGData:IsCanShowTheme(big_type, small_type)
	local theme_cfg = self:GetThemeCfgByType(big_type, small_type)

	if not IsEmptyTable(theme_cfg) then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local role_level = RoleWGData.Instance:GetRoleLevel()

		local can_open = false
		if theme_cfg.condition_type == 1 then
			can_open = open_day >= theme_cfg.skynumber_show and role_level >= theme_cfg.level_show
		elseif theme_cfg.condition_type == 2 then
			can_open = open_day >= theme_cfg.skynumber_show or role_level >= theme_cfg.level_show
		end

		if can_open then
			return true
		end
	end

	return false
end

-------------------------------------------------cal_end----------------------------------------------------