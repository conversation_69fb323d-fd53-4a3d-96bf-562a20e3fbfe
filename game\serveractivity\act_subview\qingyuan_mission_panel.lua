QingYuanMissionView = QingYuanMissionView or BaseClass(SafeBaseView)

function QingYuanMissionView:__init()
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_qingyuan_mission")
    self:SetMaskBg(true)
end

function QingYuanMissionView:LoadCallBack()
	self.qy_task_list = AsyncListView.New(QingYuanMissionRender, self.node_list["task_list"])

    self.qy_task_reward_list = {}
    for i = 1, 5 do
        local render = QingYuanMissionRewardItem.New(self.node_list["reward_item_" .. i])
        render:SetIndex(i)
        render:SetClickCallBack(BindTool.Bind(self.OpenCityInfoScoreRewardView, self))
        self.qy_task_reward_list[i] = render
    end
end

function QingYuanMissionView:ReleaseCallBack()
    if self.qy_task_reward_list then
        for i = 1, 5 do
            self.qy_task_reward_list[i]:DeleteMe()
            self.qy_task_reward_list[i] = nil
        end
        self.qy_task_reward_list = nil
    end

    if self.qy_task_list then
        self.qy_task_list:DeleteMe()
        self.qy_task_list = nil
    end
end

function QingYuanMissionView:OnFlush()
    local qy_task_list_info = ActivePerfertQingrenWGData.Instance:GetLoveCityListInfo()
    self.qy_task_list:SetDataList(qy_task_list_info)

    local _, loving_city_process_reward = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianTaskCfg()
    local server_info = ActivePerfertQingrenWGData.Instance:GetServerLoveCityInfo()
    local need_process = ActivePerfertQingrenWGData.Instance:GetCurLoverProgress(loving_city_process_reward, server_info.total_process)
	self.node_list.qingyuan_mission_slider.slider.value = need_process

    local num_flag = bit:d2b(server_info.process_reward_flag)
    for i = 1, 5 do
        local temp_data = {}
        temp_data.need_process = loving_city_process_reward[i].need_process
        temp_data.is_get = num_flag[32 - i] == 0
        temp_data.total_process = server_info.total_process
        self.qy_task_reward_list[i]:SetData(temp_data)
    end

    self.node_list.score_text.text.text = server_info.total_process
end

function QingYuanMissionView:OpenCityInfoScoreRewardView()
    ServerActivityWGCtrl.Instance:OpenCityInfoRewardView()
end

---------------------
