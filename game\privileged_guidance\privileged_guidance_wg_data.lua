PrivilegedGuidanceWGData = PrivilegedGuidanceWGData or BaseClass()
PrivilegedGuidanceType = {
    Guidance_Type_1 = 0,    --周卡       周卡特权 尊享周卡  30 
    Guidance_Type_2 = 1,    --龙神月卡   返利特权   198 有插值
    Guidance_Type_3 = 2,    --至尊月卡   返利特权  98  
    Guidance_Type_4 = 3,    --龙神宝玺
    Guidance_Type_5 = 4,    --龙神宝珠
    Guidance_Type_6 = 5,    --龙神秒杀     
    Guidance_Type_7 = 6,    --328守护直购
    Guidance_Type_8 = 7,    --奴龙高手    
    Guidance_Type_9 = 8,    --再爆一次
    Guidance_Type_10 = 9,   --攒福特权

    Guidance_Type_11 = 10,    --周卡  周卡特权  超级周卡  
    Guidance_Type_12 = 11,    --周卡  周卡特权  6000档位   
}

function PrivilegedGuidanceWGData:__init()
    if PrivilegedGuidanceWGData.Instance then
        error("[PrivilegedGuidanceWGData] Attempt to create singleton twice!")
        return
    end

    PrivilegedGuidanceWGData.Instance = self
    local cfg = ConfigManager.Instance:GetAutoConfig("privileged_guidance_cfg_auto")
    self.privileged_sub_type_cfg = ListToMapList(cfg.privileged, "sub_type")
    self.advertisement_type_cfg = ListToMapList(cfg.advertisement, "type")
    self.privileged_type_cfg = ListToMap(cfg.privileged, "sub_type", "type")
    self.privileged_cfg = cfg.privileged
    RemindManager.Instance:Register(RemindName.PrivilegedGuidance, BindTool.Bind(self.AllGuidanceRemindBySubType, self))
end

function PrivilegedGuidanceWGData:__delete()
    PrivilegedGuidanceWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.PrivilegedGuidance)
end

function PrivilegedGuidanceWGData:GetItemListsBySub(sub_type)
    local cfg = self.privileged_sub_type_cfg[sub_type] or {}
    local datas = {}
    for k,v in pairs(cfg) do
        local list = {}
        if self:GetActIsOpen(v.sub_type, v.type) then
            local _, _, _, _, sort_index = self:GetRenderCanBuy(v.type)
            list.cfg = v
            list.sort_index = sort_index
            table.insert(datas, list)
        end
    end

    table.sort(datas, SortTools.KeyLowerSorters("sort_index"))
    return datas
end

function PrivilegedGuidanceWGData:GetActIsOpen(sub_type, type)
    local fun_open = false
    if type == PrivilegedGuidanceType.Guidance_Type_1 then
        local can_show = RechargeWGData.Instance:IsShowWeekCardGrade(0)
        fun_open = can_show and FunOpen.Instance:GetFunIsOpened(FunName.RechargeWeekcard)
    elseif type == PrivilegedGuidanceType.Guidance_Type_2 then
        fun_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeMonthcard)
    elseif type == PrivilegedGuidanceType.Guidance_Type_3 then
        -- fun_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeMonthcard)
        local mc_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
        local mc_can_get_reward, mc_state = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, mc_info.cur_day)
        local is_show = mc_state == INVEST_CARD_TIPS.NoInvest
        fun_open = is_show and FunOpen.Instance:GetFunIsOpened(FunName.RechargeMonthcard)
    elseif type == PrivilegedGuidanceType.Guidance_Type_4 then
        fun_open = FunOpen.Instance:GetFunIsOpened(FunName.SuperDragonSeal)
    elseif type == PrivilegedGuidanceType.Guidance_Type_5 then
        fun_open = FunOpen.Instance:GetFunIsOpened(FunName.DragonKingToken)
    elseif type == PrivilegedGuidanceType.Guidance_Type_6 then
        fun_open = FunOpen.Instance:GetFunIsOpened(FunName.dragon_temp_longhun)
    elseif type == PrivilegedGuidanceType.Guidance_Type_7 then
        fun_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ACT_PIERRE_DIRECT_PURCHASE)
    elseif type == PrivilegedGuidanceType.Guidance_Type_8 then
        fun_open = FunOpen.Instance:GetFunIsOpened(FunName.dragon_temp_hatch)
    elseif type == PrivilegedGuidanceType.Guidance_Type_9 then
        fun_open = FunOpen.Instance:GetFunIsOpened(FunName.Boss)
    elseif type == PrivilegedGuidanceType.Guidance_Type_10 then
        fun_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeReservecard)
    elseif type == PrivilegedGuidanceType.Guidance_Type_11 then
        local can_show = RechargeWGData.Instance:IsShowWeekCardGrade(1)
        fun_open = can_show and FunOpen.Instance:GetFunIsOpened(FunName.RechargeWeekcard)
    elseif type == PrivilegedGuidanceType.Guidance_Type_12 then
        local can_show = RechargeWGData.Instance:IsShowWeekCardGrade(2)
        fun_open = can_show and FunOpen.Instance:GetFunIsOpened(FunName.RechargeWeekcard)
    end

    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local cfg = self.privileged_type_cfg[sub_type][type] or {}
    local is_show = false
    if cfg.is_default_show == 1 and fun_open then
        if cfg.condition_type == 2 then
            is_show = cur_day >= cfg.skynumber_show and role_level >= cfg.level_show and cur_day <= cfg.end_display_day
        else
            is_show = cur_day >= cfg.skynumber_show or role_level >= cfg.level_show and cur_day <= cfg.end_display_day
        end
    end

    return is_show
end

function PrivilegedGuidanceWGData:IsCanUp(longxi_type)
    local cost_cfg = LongXiWGData.Instance:GetLongXiLevelInfoByType(longxi_type)
    if not IsEmptyTable(cost_cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_cfg.cost_item_id)
        return item_num >= cost_cfg.cost_item_num
    end

    return false
end

function PrivilegedGuidanceWGData:GetRenderCanBuy(type)
    local buy_btn_show, reward_btn_show, is_grey = false, false, false
    local sort_index = 0
    local btn_str = Language.PrivilegedGuidance.LinQu
    if type == PrivilegedGuidanceType.Guidance_Type_1 then
        local cur_wc_grade = RechargeWGData.Instance:GetWeekCardGrade()
        cur_wc_grade = cur_wc_grade < 0 and 1 or cur_wc_grade + 1
        local other_cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(cur_wc_grade)
        local day_num = other_cfg and other_cfg.week_card_day or 0
        local cur_day = RechargeWGData.Instance:GetWeekCardCurrentDay()
        local has_week_card = RechargeWGData.Instance:IsHasWeekCard()
        local get_flag = RechargeWGData.Instance:GetWeekCardRewardFlag(cur_day)
        local can_get_reward = has_week_card and cur_day <= day_num and not get_flag
        buy_btn_show = not has_week_card --or not can_get_reward
        reward_btn_show = has_week_card --and can_get_reward
        sort_index = 2
        is_grey = not can_get_reward
        btn_str = can_get_reward and Language.PrivilegedGuidance.WeiLing or Language.PrivilegedGuidance.YiLing
    elseif type == PrivilegedGuidanceType.Guidance_Type_2 then
        local can_active = RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.MonthCard)
        local card_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
        local can_get_reward = false
        if card_info and card_info.cur_day then
            can_get_reward = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, card_info.cur_day)
        end
        
        buy_btn_show = can_active
        reward_btn_show = not buy_btn_show
        if can_active or can_get_reward then
            sort_index = 7
        else
            sort_index = 999
        end

        is_grey = not can_get_reward
        btn_str = can_get_reward and Language.PrivilegedGuidance.WeiLing or Language.PrivilegedGuidance.YiLing
    elseif type == PrivilegedGuidanceType.Guidance_Type_3 then
        local can_active = RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
        local card_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.StorehouseCard)
        local can_get_reward = false
        if card_info and card_info.cur_day then
            can_get_reward = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.StorehouseCard, card_info.cur_day)
        end

        buy_btn_show = can_active
        reward_btn_show = not buy_btn_show
        if can_active or can_get_reward then
            sort_index = 3
        else
            sort_index = 999
        end

        is_grey = not can_get_reward
        btn_str = can_get_reward and Language.PrivilegedGuidance.WeiLing or Language.PrivilegedGuidance.YiLing
    elseif type == PrivilegedGuidanceType.Guidance_Type_4 then
        local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
        if not IsEmptyTable(data) then
            buy_btn_show = data.is_active == 0
            sort_index = data.is_active == 0 and 4 or 999
        else
            buy_btn_show = false
            sort_index = 999
        end 
          
        local is_can_up = self:IsCanUp(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
        local is_up = LongXiWGData.Instance:GetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
        reward_btn_show = not buy_btn_show
        btn_str = Language.PrivilegedGuidance.SellOut--is_up and Language.PrivilegedGuidance.Stop or Language.PrivilegedGuidance.OneKeyUp
        is_grey = not buy_btn_show--is_max_level or not is_can_up
    elseif type == PrivilegedGuidanceType.Guidance_Type_5 then
        local is_can_up = self:IsCanUp(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
        local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
        local is_up = LongXiWGData.Instance:GetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
        if not IsEmptyTable(data) then
            buy_btn_show = data.is_active == 0
            sort_index = data.is_active == 0 and 5 or 999
        else
            buy_btn_show = false
            sort_index = 999
        end   

        reward_btn_show = not buy_btn_show
        is_grey = not buy_btn_show--is_max_level or not is_can_up
        btn_str = Language.PrivilegedGuidance.SellOut--is_up and Language.PrivilegedGuidance.Stop or Language.PrivilegedGuidance.OneKeyUp
    elseif type == PrivilegedGuidanceType.Guidance_Type_6 then
        local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
        local next_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level + 1)
        local is_max_level = IsEmptyTable(next_level_cfg)
        buy_btn_show = not is_max_level
        is_grey = not buy_btn_show
        -- btn_str = is_max_level and Language.PrivilegedGuidance.SellOut or Language.PrivilegedGuidance.Buy
        -- buy_btn_show = longshen_level == 0
        sort_index = buy_btn_show and 6 or 999
        reward_btn_show = not buy_btn_show
        btn_str = is_max_level and Language.PrivilegedGuidance.SellOut or Language.PrivilegedGuidance.Buy
        -- is_grey = IsEmptyTable(next_level_cfg)
    elseif type == PrivilegedGuidanceType.Guidance_Type_7 then
        local no_buy = PierreDirectPurchaseWGData.Instance:GetBuyTypeAllNotBuy(1)
        buy_btn_show = no_buy
        reward_btn_show = not buy_btn_show
        sort_index = no_buy and 8 or 999
        is_grey = not no_buy
        btn_str = no_buy and Language.PrivilegedGuidance.Buy or Language.PrivilegedGuidance.SellOut
    elseif type == PrivilegedGuidanceType.Guidance_Type_8 then
        local has_privilege = DragonTempleWGData.Instance:IsHatchHasPrivilege()
        local can_get_daily_reward = DragonTempleWGData.Instance:CanGetHatchDailyReward()
        buy_btn_show = not has_privilege
        reward_btn_show = not buy_btn_show
        if not has_privilege or can_get_daily_reward then
            sort_index = 9
        else
            sort_index = 999
        end

        is_grey = not can_get_daily_reward
        btn_str = can_get_daily_reward and Language.PrivilegedGuidance.WeiLing or Language.PrivilegedGuidance.YiLing
    elseif type == PrivilegedGuidanceType.Guidance_Type_9 then
        local cur_level ,max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
        local next_level = cur_level >= max_level and max_level or cur_level + 1
        local next_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(next_level)
        if cur_level >= max_level then
            buy_btn_show = false
        else
            buy_btn_show = true
        end

        sort_index = buy_btn_show and 10 or 999
        reward_btn_show = false--not buy_btn_show
        is_grey = not buy_btn_show
        btn_str = buy_btn_show and Language.PrivilegedGuidance.Buy or Language.PrivilegedGuidance.SellOut
    elseif type == PrivilegedGuidanceType.Guidance_Type_10 then
        local cur_level = RechargeWGData.Instance:GetZFCurLevel()
        local max_level = RechargeWGData.Instance:GetZFMaxLevel()
        if cur_level >= max_level then
            is_grey = true
            buy_btn_show = false
        else
            buy_btn_show = true
        end

        sort_index = buy_btn_show and 1 or 999
        reward_btn_show = not buy_btn_show
        is_grey = not buy_btn_show
        btn_str = buy_btn_show and Language.PrivilegedGuidance.Buy or Language.PrivilegedGuidance.SellOut
    elseif type == PrivilegedGuidanceType.Guidance_Type_11 then
        local cur_week_card_type = 2
        local other_cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(cur_week_card_type)
		local cur_grade = RechargeWGData.Instance:GetWeekCardGrade()
		local is_has_week_card = RechargeWGData.Instance:IsHasWeekCard()
        local cur_day = RechargeWGData.Instance:GetWeekCardCurrentDay()
        local day_num = other_cfg and other_cfg.week_card_day or 0
        local get_flag = RechargeWGData.Instance:GetWeekCardRewardFlag(cur_day)
        local is_show = (cur_week_card_type - 1 > cur_grade and is_has_week_card) or not is_has_week_card
        local can_get_reward = (cur_grade == cur_week_card_type - 1) and is_has_week_card and cur_day <= day_num and not get_flag

        buy_btn_show = is_show
        reward_btn_show = is_has_week_card and cur_week_card_type - 1 == cur_grade --and can_get_reward

        sort_index = 2
        is_grey = not can_get_reward
        btn_str = can_get_reward and Language.PrivilegedGuidance.WeiLing or Language.PrivilegedGuidance.YiLing
    elseif type == PrivilegedGuidanceType.Guidance_Type_12 then 
        local cur_week_card_type = 3
        local other_cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(cur_week_card_type)
		local cur_grade = RechargeWGData.Instance:GetWeekCardGrade()
		local is_has_week_card = RechargeWGData.Instance:IsHasWeekCard()
        local cur_day = RechargeWGData.Instance:GetWeekCardCurrentDay()
        local day_num = other_cfg and other_cfg.week_card_day or 0
        local get_flag = RechargeWGData.Instance:GetWeekCardRewardFlag(cur_day)
        local is_show = (cur_week_card_type - 1 > cur_grade and is_has_week_card) or not is_has_week_card
        local can_get_reward = (cur_grade == cur_week_card_type - 1) and is_has_week_card and cur_day <= day_num and not get_flag

        buy_btn_show = is_show
        reward_btn_show = is_has_week_card and cur_week_card_type - 1 == cur_grade --and can_get_reward

        sort_index = 2
        is_grey = not can_get_reward
        btn_str = can_get_reward and Language.PrivilegedGuidance.WeiLing or Language.PrivilegedGuidance.YiLing
    end

    return buy_btn_show, reward_btn_show, is_grey, btn_str, sort_index
end

function PrivilegedGuidanceWGData:GetNeedGold(type)
    local cost_num, rmb_type, rmb_seq
    local is_rmb = false
    if type == PrivilegedGuidanceType.Guidance_Type_1 then
        local cur_wc_grade = RechargeWGData.Instance:GetWeekCardGrade()
        cur_wc_grade = cur_wc_grade < 0 and 1 or cur_wc_grade + 1
        local cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(cur_wc_grade)
        if cfg then
            rmb_seq = cfg.rmb_seq
            cost_num = cfg.week_card_price
            rmb_type = cfg.rmb_type
            is_rmb = true
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_2 then
        local mc_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
        local mc_can_get_reward, mc_state = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, mc_info.cur_day)
    
        local sc_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.StorehouseCard)
        local sc_can_get_reward, sc_state = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.StorehouseCard, sc_info.cur_day)
    
        if mc_state == INVEST_CARD_TIPS.NoInvest and sc_state ~= INVEST_CARD_TIPS.NoInvest then
            rmb_seq = INVEST_CARD_TYPE.SpreadMonthCard
        else
            rmb_seq = INVEST_CARD_TYPE.MonthCard
        end

	    -- 根据显示的月卡类型显示
	    local month_card_cfg = RechargeWGData.Instance:GetTZCardCfg(rmb_seq)
	    cost_num = month_card_cfg and month_card_cfg.need_rmb or 0
	    rmb_type = month_card_cfg and month_card_cfg.rmb_type or 0
        is_rmb = true

        -- local month_card_cfg = RechargeWGData.Instance:GetTZCardCfg(INVEST_CARD_TYPE.MonthCard)
        -- cost_num = month_card_cfg and month_card_cfg.need_rmb or 0
        -- rmb_type = month_card_cfg and month_card_cfg.rmb_type or 0
        -- rmb_seq = INVEST_CARD_TYPE.MonthCard
        -- is_rmb = true
    elseif type == PrivilegedGuidanceType.Guidance_Type_3 then
        local storehouse_cfg = RechargeWGData.Instance:GetTZCardCfg(INVEST_CARD_TYPE.StorehouseCard)
        cost_num = storehouse_cfg and storehouse_cfg.need_rmb or 0
        rmb_type = storehouse_cfg and storehouse_cfg.rmb_type or 0
        rmb_seq = INVEST_CARD_TYPE.StorehouseCard
        is_rmb = true
    elseif type == PrivilegedGuidanceType.Guidance_Type_4 then
        local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
        cost_num = base_data.price
        rmb_type = base_data.rmb_type 
        rmb_seq = base_data.rmb_seq
        is_rmb = true
    elseif type == PrivilegedGuidanceType.Guidance_Type_5 then
        local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
        cost_num = base_data.price
        rmb_type = base_data.rmb_type
        rmb_seq = base_data.rmb_seq
        is_rmb = true
    elseif type == PrivilegedGuidanceType.Guidance_Type_6 then
        local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
        local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
        if IsEmptyTable(cur_level_cfg) then
            return
        end

        is_rmb = true
        rmb_type = cur_level_cfg.rmb_type
        rmb_seq = cur_level_cfg.rmb_seq
        cost_num = cur_level_cfg.price
    elseif type == PrivilegedGuidanceType.Guidance_Type_7 then
        local buy_type = 1
        local data = PierreDirectPurchaseWGData.Instance:GetDirectPurchaseCfgByType(buy_type)
        if not IsEmptyTable(data) then
            cost_num = data.buy_all_price
            rmb_type = data.rmb_type
            rmb_seq = data.buy_type
            is_rmb = true
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_8 then
        local cfg = DragonTempleWGData.Instance:GetOtherCfg()
        if not IsEmptyTable(cfg) then
            cost_num = cfg.hatch_price
            rmb_type = cfg.hatch_rmb_type
            rmb_seq = cfg.hatch_rmb_seq
            is_rmb = true
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_9 then
        local is_active = BossPrivilegeWGData.Instance:GetBossPrivilegeIsActive() --是否激活过
        if not is_active then --激活
            local times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(1)
            cost_num = times_list.condition_value
            is_rmb = false
        else
            local cur_level ,max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
            local next_level = cur_level >= max_level and max_level or cur_level + 1
            local next_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(next_level)
            if cur_level >= max_level then
                return
            end

            if next_times_list.condition_type == 1 then
                cost_num = next_times_list.condition_value
                is_rmb = false
            elseif next_times_list.condition_type == 2 then
                rmb_type = next_times_list.rmb_type
                rmb_seq = next_times_list.rmb_seq
                cost_num = next_times_list.condition_value
                is_rmb = true
            end
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_10 then
        local cfg = RechargeWGData.Instance:GetZFCurBuyCfg()
        if not IsEmptyTable(cfg) then
            rmb_seq = cfg.rmb_seq
            cost_num = cfg.rmb
            rmb_type = cfg.rmb_type
            is_rmb = true
        end

    elseif type == PrivilegedGuidanceType.Guidance_Type_11 then
        local cur_week_card_type = 2
        local cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(cur_week_card_type)
        if cfg then
            rmb_seq = cfg.rmb_seq
            cost_num = cfg.week_card_price
            rmb_type = cfg.rmb_type
            is_rmb = true
        end
    elseif type == PrivilegedGuidanceType.Guidance_Type_12 then 
        local cur_week_card_type = 3
        local cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(cur_week_card_type)
        if cfg then
            rmb_seq = cfg.rmb_seq
            cost_num = cfg.week_card_price
            rmb_type = cfg.rmb_type
            is_rmb = true
        end
    end

    local price = 0
    if is_rmb then
        price = RoleWGData.GetPayMoneyStr(cost_num, rmb_type, rmb_seq)
    else
        price = cost_num
    end
    
    return price, is_rmb
end

function PrivilegedGuidanceWGData:GetAdvertisementList(type)
    return self.advertisement_type_cfg[type] or {}
end

function PrivilegedGuidanceWGData:GuidanceRemindBySubType(sub_type)
    local cfg = self:GetItemListsBySub(sub_type)
    for k,v in pairs(cfg) do
        local _, is_show_reward, is_grey = self:GetRenderCanBuy(v.type)
        if not is_grey and is_show_reward then
            return true
        end
    end
    
    return false
end

function PrivilegedGuidanceWGData:AllGuidanceRemindBySubType()
    for k,v in pairs(self.privileged_cfg) do
        local _, is_show_reward, is_grey = self:GetRenderCanBuy(v.type)
        if not is_grey and is_show_reward then
            return 1
        end
    end

    return 0
end