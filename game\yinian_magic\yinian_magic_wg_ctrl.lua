require("game/yinian_magic/yinian_magic_view")
require("game/yinian_magic/yinian_magic_wg_data")
require("game/yinian_magic/yinian_magic_select_view")
require("game/yinian_magic/yinian_magic_zhenqi_view")
require("game/yinian_magic/yinian_magic_daily_buy")
require("game/yinian_magic/yinian_magic_purchase")
require("game/yinian_magic/yinian_magic_tips_view")

YinianMagicWGCtrl = YinianMagicWGCtrl or BaseClass(BaseWGCtrl)

function YinianMagicWGCtrl:__init()
	if YinianMagicWGCtrl.Instance then
		print_error("[YinianMagicWGCtrl]:Attempt to create singleton twice!")
	end

	YinianMagicWGCtrl.Instance = self
    self.data = YinianMagicWGData.New()
    self.view = YinianMagicView.New(GuideModuleName.YinianMagicView)
	self.select_view = YinianMagicSelectView.New()
    self.zhenqi_view = YinianMagicZhenQitView.New()
    self.daily_buy_view = YiNianMagicDailyBuyView.New()
    self.purchase_view = YiNianMagicPurchaseView.New()
    self.tips_view = YinianMagicTipsView.New()

    self:RegisterAllProtocols()
end

function YinianMagicWGCtrl:__delete()
    if self.data then
        self.data:DeleteMe()
	    self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
	    self.view = nil
    end

	if self.select_view then
        self.select_view:DeleteMe()
	    self.select_view = nil
    end

	if self.zhenqi_view then
        self.zhenqi_view:DeleteMe()
	    self.zhenqi_view = nil
    end

    if self.daily_buy_view then
        self.daily_buy_view:DeleteMe()
	    self.daily_buy_view = nil
    end

    if self.purchase_view then
        self.purchase_view:DeleteMe()
	    self.purchase_view = nil
    end
    if self.tips_view then
        self.tips_view:DeleteMe()
	    self.tips_view = nil
    end

    YinianMagicWGCtrl.Instance = nil
end

function YinianMagicWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSGodOrDemonOperate)
	self:RegisterProtocol(SCGodOrDemonInfo, "OnSCGodOrDemonInfo")
    self:RegisterProtocol(SCGodOrDemonTaskInfo, "OnSCGodOrDemonTaskInfo")
    self:RegisterProtocol(SCGodOrDemonTaskUpdate, "OnSCGodOrDemonTaskUpdate")
end

function YinianMagicWGCtrl:SendYinianMagicRequest(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGodOrDemonOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
    --print_error("SendYinianMagicRequest",protocol)
end

function YinianMagicWGCtrl:OnSCGodOrDemonInfo(protocol)
	--print_error("===all_info====",protocol)
    local old_data_type = self.data:GetCurSelcetType()
    local reward_list = {}
    local change_type = false
    if old_data_type == 0 and protocol.type ~= old_data_type then
        local type_cfg = self.data:GetTypeCfgByType(protocol.type)
        if not IsEmptyTable(type_cfg) then
            for k, v in pairs(type_cfg.choose_reward_item) do
                table.insert(reward_list, v)
            end
        end

        change_type = true
    end

    self.data:SetAllGodOrDemonInfo(protocol)

    --选择类型后需要打开界面
    if change_type then
        ViewManager.Instance:Open(GuideModuleName.YinianMagicView)
    end

    ViewManager.Instance:FlushView(GuideModuleName.YinianMagicView)
    self:FlushBuyView()
    if self.purchase_view:IsOpen() then
		self.purchase_view:Flush()
	end

    if self.zhenqi_view:IsOpen() then
		self.zhenqi_view:Flush()
	end

    RemindManager.Instance:Fire(RemindName.YinianMagic)
end

function YinianMagicWGCtrl:OnSCGodOrDemonTaskInfo(protocol)
	--print_error("===Takinfo====",protocol)
    self.data:SetAllTaskInfo(protocol)
    RemindManager.Instance:Fire(RemindName.YinianMagic)
end

function YinianMagicWGCtrl:OnSCGodOrDemonTaskUpdate(protocol)
	--print_error("==single====",protocol)
    self.data:SetSingleTaskInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.YinianMagicView, nil, "flush_task")
    RemindManager.Instance:Fire(RemindName.YinianMagic)
end

function YinianMagicWGCtrl:OpenSelectView(suit_seq)
	if self.select_view then
        self.select_view:Open()
    end
end

function YinianMagicWGCtrl:CloseSelectView()
	if self.select_view:IsOpen() then
        self.select_view:Close()
    end
end

function YinianMagicWGCtrl:OpenZhenQiView(suit_seq)
	if self.zhenqi_view then
        self.zhenqi_view:Open()
    end
end

function YinianMagicWGCtrl:OpenBuyView(seal_data)
	self.daily_buy_view:SetDataAndOpen(seal_data)
end

function YinianMagicWGCtrl:FlushBuyView()
	if self.daily_buy_view:IsOpen() then
		self.daily_buy_view:Flush()
	end
end

function YinianMagicWGCtrl:OnUpLevelResult(result, level)
	if 1 == result then
		self.view:PlayUseEffect(UIEffectName.s_shengji)
        ViewManager.Instance:FlushView(GuideModuleName.YinianMagicView, nil, "all", {jump_index = level})
	end
end

function YinianMagicWGCtrl:OnUpGradeResult(result, grade)
	if 0 == result then
		self.zhenqi_view:StopGradeOperator()
	elseif 1 == result then
        local select_type = YinianMagicWGData.Instance:GetCurSelcetType()
        local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, grade) 
        local cur_modle_type = cur_cfg and cur_cfg.modle_type or 0
        local last_cfg = YinianMagicWGData.Instance:GetGradeCfg(select_type, grade - 1)
        local last_modle_type = last_cfg and last_cfg.modle_type or 0
        if last_modle_type == cur_modle_type then
            if self.zhenqi_view:IsAutoUpGrade() then
                self.zhenqi_view:AutoUpGradeUpOnce()
            end
        else
            local show_model_type_cfg = YinianMagicWGData.Instance:GetModelTypeCfg(select_type, cur_modle_type)

            self.zhenqi_view:StopGradeOperator()
            self:OpenGetNewView(show_model_type_cfg.appe_image_id or 0)
            YinianMagicWGCtrl.Instance:SendYinianMagicRequest(YINIAN_MAGIC_OPERATE_TYPE.USE_GRADE, show_model_type_cfg.min_grade or 1)
        end
        
        self.zhenqi_view:PlayEffect(UIEffectName.s_shengjie)
	end
end

function YinianMagicWGCtrl:OpenPurchaseView()
	if self.purchase_view then
        self.purchase_view:Open()
    end
end

function YinianMagicWGCtrl:OpenTipsView(select_type)
	if self.tips_view then
        self.tips_view:FlushTipsView(select_type)
        if not self.tips_view:IsOpen() then
            self.tips_view:Open()
        end
    end
end

function YinianMagicWGCtrl:OpenGetNewView(appe_image_id, select_type)
	local protocol = {appe_image_id = appe_image_id, appe_type = ROLE_APPE_TYPE.GOD_OR_DEMON}
	AppearanceWGCtrl.Instance:OnGetNewAppearance(protocol)
end

function YinianMagicWGCtrl:PlayFlyEffect()
    -- if self.view then
    --     self.view:PlayFlyEffect()
    -- end
end