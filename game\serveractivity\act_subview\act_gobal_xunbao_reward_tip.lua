ActGobalXunbaoRewardTipNew = ActGobalXunbaoRewardTipNew or BaseClass(SafeBaseView)

function ActGobalXunbaoRewardTipNew:__init()
	self:AddViewResource(0, "uis/view/act_subview_ui_prefab", "layout_act_gobal_xunbao_reward_tip")
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
end

function ActGobalXunbaoRewardTipNew:__delete()
	self.data = nil
end

function ActGobalXunbaoRewardTipNew:ReleaseCallBack()
	self.data = nil
end

function ActGobalXunbaoRewardTipNew:LoadCallBack()
	self:Flush()
end

function ActGobalXunbaoRewardTipNew:OnFlush()
	if self.data then
		local data = self.data
		self.node_list.icon.image:LoadSprite(ResPath.GetF2MainUIImage(self.data.pic_name))
		self.node_list.name.text.text = Language.XunbaoIconName[data.pic_name]
		self.node_list.desc.text.text = Language.Common.ActDesc .. Language.CelebrationDesc[data.pic_name]
		self.node_list.require.text.text = Language.Common.ActCelebrationDesc .. data.need_progress_var
	end
end

function ActGobalXunbaoRewardTipNew:SetContent(data)
	self.data = data
	self:Flush()
end
