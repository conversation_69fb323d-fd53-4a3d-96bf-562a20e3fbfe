local MaxChapter = 12

FuBenLayerView = FuBenLayerView or BaseClass(SafeBaseView)

function FuBenLayerView:__init()
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_cengshu")
end

function FuBenLayerView:__delete()
    if self.timer_quest then
        GlobalTimerQuest:CancelQuest(self.timer_quest)
        self.timer_quest = nil
    end
end

function FuBenLayerView:ReleaseCallBack()
    if self.timer_quest then
        GlobalTimerQuest:CancelQuest(self.timer_quest)
        self.timer_quest = nil
    end
end

function FuBenLayerView:ShowIndexCallBack(index)
    local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(self.layer_value)
    if not cfg then
        self:Close()
        return
    end
    local value = cfg.chapter % MaxChapter
    local calc_chapter = value == 0 and MaxChapter or value
    local cur_leve = FuBenPanelWGData.Instance:GetPassLevel()
    self.node_list["img_guaiwu_num"].text.text = (cur_leve + 1)


    if self.timer_quest then
        GlobalTimerQuest:CancelQuest(self.timer_quest)
        self.timer_quest = nil
    end
    self.timer_quest = GlobalTimerQuest:AddDelayTimer(function()
        self:Close()
    end, 3)
end

function FuBenLayerView:SetFBLayer(layer)
    self.layer_value = layer
end

--function FuBenLayerView:OnFlush(param_t)
--    print_error("FuBenLayerView", param_t)
--
--end