ZhuoGuiFuBenLogic = ZhuoGuiFuBenLogic or BaseClass(CommonFbLogic)

function ZhuoGuiFuBenLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function ZhuoGuiFuBenLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function ZhuoGuiFuBenLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	local effect_bundle, effect_asset
	if RoleWGData.Instance:GetRoleProf() == GameEnum.ROLE_PROF_4 then
		effect_bundle, effect_asset = ResPath.GetEffectUi("UI_effect_miwu1")
	else
		effect_bundle, effect_asset = ResPath.GetEffectUi("UI_effect_miwu2")
	end

	local param_t = {is_show = true, effect_bundle = effect_bundle, effect_asset = effect_asset}
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.FuBenPanel.RuleTitle[TabIndex.fubenpanel_zhuogui])
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		MainuiWGCtrl.Instance:SetUnderMainUIEffectInfo(param_t)

        Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.NO_CAN_CHANGE, 31, 1.5, 12)
        FuBenPanelWGCtrl.Instance:OpenZhuoGuiFubenSceneView()
	end)

    self:StartDoEvent()
end

function ZhuoGuiFuBenLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

	MainuiWGCtrl.Instance:SetUnderMainUIEffectInfo()
    FuBenPanelWGCtrl.Instance:CloseZhuoGuiFubenSceneView()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
end

function ZhuoGuiFuBenLogic:StartDoEvent()
    FuBenPanelWGCtrl.Instance:FindZhuoGuiNpc()
end