HideGoldShopBuyView = HideGoldShopBuyView or BaseClass(SafeBaseView)
function HideGoldShopBuyView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/hidegold_shop_ui_prefab", "layout_hidegold_shop_view")
end

function HideGoldShopBuyView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_flush_shop"], BindTool.Bind(self.OnClickFlushShop, self))
    XUI.AddClickEventListener(self.node_list["btn_go_recharge"], BindTool.Bind(self.OnClickGoRechargeBtn, self))

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end


    if self.shop_item_list == nil then
        local bundle = "uis/view/hidegold_shop_ui_prefab"
		local asset = "hidegold_shop_item_cell"
        self.shop_item_list = AsyncBaseGrid.New()
        self.shop_item_list:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list["shop_item_grid"],
			assetBundle = bundle, assetName = asset, itemRender = HideGoldShopBuyItemRender})
        self.shop_item_list:SetStartZeroIndex(false)
    end
end

function HideGoldShopBuyView:ReleaseCallBack()
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.shop_item_list then
        self.shop_item_list:DeleteMe()
        self.shop_item_list = nil
    end

    if self.shop_model_display then
        self.shop_model_display:DeleteMe()
        self.shop_model_display = nil
    end
end

function HideGoldShopBuyView:OpenCallBack()
    HideGoldShopWGCtrl.Instance:ReqActivityHideGoldShopInfo(OA_HIDE_GOLD_SHOP_OPERATE_TYPE.INFO)
end

function HideGoldShopBuyView:OnClickFlushShop()
    local flush_cfg = HideGoldShopWGData.Instance:GetRefreshCfg()
    if flush_cfg == nil then
        return
    end

    local enough = RoleWGData.Instance:GetIsEnoughUseGold(flush_cfg.refresh_cost_gold)
    if enough then
        HideGoldShopWGCtrl.Instance:ReqActivityHideGoldShopInfo(OA_HIDE_GOLD_SHOP_OPERATE_TYPE.REFRESH)
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

function HideGoldShopBuyView:OnClickGoRechargeBtn()
    ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
end

function HideGoldShopBuyView:OnFlush()
    local flush_cfg = HideGoldShopWGData.Instance:GetRefreshCfg()
    if flush_cfg then
        self.node_list["flush_gold"].text.text = flush_cfg.refresh_cost_gold
    end

    local cur_score = HideGoldShopWGData.Instance:GetCurScore()
    self.node_list["cur_score"].text.text = string.format(Language.HideGoldShop.CurScore, cur_score)

    local shop_list = HideGoldShopWGData.Instance:GetShopShowList()
    --print_error(shop_list)
    if shop_list then
        self.shop_item_list:SetDataList(shop_list)
    end

    self:LoadShopModel()
end

function HideGoldShopBuyView:LoadShopModel()
    if self.shop_model_display == nil then
		self.shop_model_display = OperationActRender.New(self.node_list.shop_model_root)
        self.shop_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
 
        local show_model_id_list = HideGoldShopWGData.Instance:GetShopShowModelId()
        if show_model_id_list then
            local data = {}
            if show_model_id_list ~= 0 and show_model_id_list ~= "" then
                local split_list = string.split(show_model_id_list, "|")
                if #split_list > 1 then
                    local list = {}
                    for k, v in pairs(split_list) do
                        list[tonumber(v)] = true
                    end
                    data.model_item_id_list = list
                else
                    data.item_id = show_model_id_list
                end
            end
            data.should_ani = true
            data.item_id = show_model_id_list
            data.render_type = 0
            self.shop_model_display:SetData(data)
        end
    else
        self.shop_model_display:ActModelPlayLastAction()
	end

    local model_flush_cfg = HideGoldShopWGData.Instance:GetRefreshCfg()
    if model_flush_cfg then
        local pos_x, pos_y = 0, 0
        if  model_flush_cfg.shop_model_display_pos and model_flush_cfg.shop_model_display_pos ~= "" then
            local pos_list = string.split(model_flush_cfg.shop_model_display_pos, "|")
            pos_x = tonumber(pos_list[1]) or pos_x
            pos_y = tonumber(pos_list[2]) or pos_y
        end
    
        local rot_x, rot_y, rot_z = 0, 0, 0
        if  model_flush_cfg.shop_model_display_rotation and model_flush_cfg.shop_model_display_rotation ~= "" then
            local rot_list = string.split(model_flush_cfg.shop_model_display_rotation, "|")
            rot_x = tonumber(rot_list[1]) or rot_x
            rot_y = tonumber(rot_list[2]) or rot_y
            rot_z = tonumber(rot_list[3]) or rot_z
        end
    
        RectTransform.SetAnchoredPositionXY(self.node_list.shop_model_root.rect, pos_x, pos_y)
        self.node_list.shop_model_root.rect.rotation = Quaternion.Euler(rot_x, rot_y, rot_z)
    
        local scale = model_flush_cfg.shop_model_display_scale
        scale = (scale and scale ~= "" and scale > 0) and scale or 1
        Transform.SetLocalScaleXYZ(self.node_list.shop_model_root.transform, scale, scale, scale)
    end
end


HideGoldShopBuyItemRender = HideGoldShopBuyItemRender or BaseClass(BaseRender)
function HideGoldShopBuyItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuy, self))

    self.item_cell = ItemCell.New(self.node_list["item_pos"])
end

function HideGoldShopBuyItemRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function HideGoldShopBuyItemRender:OnFlush()
    if self.data == nil then
        return
    end

    self.item_cell:SetData(self.data.item)
    self.node_list["score_text"].text.text = string.format(Language.HideGoldShop.ShopScore, self.data.need_score)

    local item_name = ItemWGData.Instance:GetItemName(self.data.item.item_id) or ""
    self.node_list["item_name"].text.text = item_name

    local is_limit = HideGoldShopWGData.Instance:GetIsLimitShop(self.data.seq)

    if is_limit then
        local already_buy_times = HideGoldShopWGData.Instance:GetShopLimitTimes(self.data.seq)
        local times = self.data.times_limit - already_buy_times
        self.node_list["limit_times"].text.text = string.format(Language.HideGoldShop.LimitTimes2, times)
        self.node_list["limit_times_root"]:SetActive(times > 0)
        self.node_list["is_buy_sold"]:SetActive(times < 1)
        XUI.SetButtonEnabled(self.node_list["btn_buy"], times > 0)
        XUI.SetGraphicGrey(self.node_list["bg"], times < 1)

        if times < 1 then
            self.item_cell:SetGraphicGreyCualityBg(true)
            self.item_cell:SetDefaultEff(false)
        else
            self.item_cell:SetGraphicGreyCualityBg(false)
        end
    else
        self.node_list["limit_times_root"]:SetActive(false)
        self.node_list["is_buy_sold"]:SetActive(false)
        XUI.SetButtonEnabled(self.node_list["btn_buy"], true)
        XUI.SetGraphicGrey(self.node_list["bg"], false)
        self.item_cell:SetGraphicGreyCualityBg(false)
    end
end

function HideGoldShopBuyItemRender:OnClickBuy()
    if self.data == nil then
        return
    end

    local cur_score = HideGoldShopWGData.Instance:GetCurScore()
    if cur_score < self.data.need_score then
        TipWGCtrl.Instance:ShowSystemMsg(Language.HideGoldShop.BuyError)
        return
    end

    local function func()
        HideGoldShopWGCtrl.Instance:ReqActivityHideGoldShopInfo(OA_HIDE_GOLD_SHOP_OPERATE_TYPE.BUY, self.data.seq)
    end

    HideGoldShopWGCtrl.Instance:OpenBuyShopItemTips(func)
end