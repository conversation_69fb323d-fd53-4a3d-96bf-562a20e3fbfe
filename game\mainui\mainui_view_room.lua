---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by 123.
--- DateTime: 2019/10/12 18:23
--- Desc: 主界面房间（任务栏组队按钮下的信息）
MainUIViewRoom = MainUIViewRoom or BaseClass(BaseRender)

function MainUIViewRoom:__init()
    self.room_menber_list = AsyncListView.New(MainRoomMemberRender, self.node_list["RoomList"])
end
function MainUIViewRoom:__delete()
    if self.room_menber_list then
        self.room_menber_list:DeleteMe()
        self.room_menber_list = nil
    end
end

function MainUIViewRoom:LoadCallBack()
end

function MainUIViewRoom:Flush()
    BaseRender.Flush(self)
end

function MainUIViewRoom:OnFlush()
    local room_menber_list = NewTeamWGData.Instance:GetRoomMemberList()
    if self.node_list["RoomList"].gameObject.activeInHierarchy then
        self.room_menber_list:SetDataList(room_menber_list, 3)
    end
    local member_real_count = #room_menber_list
    local count = 0
    for i = 1, 3 do
        local flag = i <= member_real_count and  room_menber_list[i] and room_menber_list[i].online_type == 1
        self.node_list["ImgPerson" .. i]:SetActive(flag)
        if flag then
            count = count + 1
        end
    end
    local exp_add = NewTeamWGData.Instance:GetRoomExpAdd() * 10
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.LingHunGuangChang  --两个组队副本特殊处理满队伍加成
        or scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
        exp_add = 20
    end
    self.node_list.ExpText.text.text = Language.NewTeam.AddExpTeam .. exp_add .. "%"
end


MainRoomMemberRender = MainRoomMemberRender or BaseClass(BaseRender)

function MainRoomMemberRender:__init()
    self.role_name = self.node_list["TextName"]
    self.level_text = self.node_list["TextLevel"]
    self.MenberState = self.node_list["TextMenberState"]
    XUI.AddClickEventListener(self.node_list["BtnClick"], BindTool.Bind(self.OnClickItem, self))
end

function MainRoomMemberRender:__delete()
    self.role_name = nil
    self.level_text = nil
    self.MenberState = nil
end

function MainRoomMemberRender:LoadCallBack()

end

function MainRoomMemberRender:OnFlush()
    if not self.data or not next(self.data) then return end

    self.node_list["NormalState"]:CustomSetActive(true)
    self.node_list["EmptyState"]:CustomSetActive(false)

    local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
    local is_me = self.data.role_original_id == my_uid
    --if is_me then
   -- local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
    self.node_list.level_icon:SetActive(is_vis)
    if is_vis then
        self.node_list["TextLevel"].text.text = level
    else
        self.node_list["TextLevel"].text.text = string.format(Language.Common.Level1, level)
    end
    --local str = string.format(Language.NewTeam.PTLevel, role_level)
        --EmojiTextUtil.ParseRichText(self.node_list["TextLevel"].emoji_text, str, 18, COLOR3B.D_ORANGE)
    --else
     --   local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)  
        --local str = string.format(Language.NewTeam.PTLevel, self.data.level)
        --EmojiTextUtil.ParseRichText(self.node_list["TextLevel"].emoji_text, str, 18, COLOR3B.D_ORANGE)
   -- end
    self.role_name.text.text = ToColorStr(self.data.name, COLOR3B.D_ORANGE)

    --目前房间只有两种情况 ：离线 和 附近
    if self.data.online_type == 0 then
        self.MenberState.text.text = ToColorStr(Language.NewTeam.OffLine, '#717070')
    elseif self.data.online_type == 1 then
        self.MenberState.text.text = ToColorStr(Language.NewTeam.Near, '#7cffb7')
    end
    if self.node_list["btn_quit_team"] then
        self.node_list["btn_quit_team"]:SetActive(false)
    end
end

function MainRoomMemberRender:OnClickItem()
    if self.data.is_robert and self.data.is_robert == 1 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.RobertCannotCheckInfo)
        return
    end

    if self.data.role_original_id == RoleWGData.Instance:InCrossGetOriginUid() then
        return
    end

    local items, callback_param = self:GetItems()
    UiInstanceMgr.Instance:OpenCustomMenu(items
                                            , self:GetPos(self.node_list["point"])
                                            , BindTool.Bind(self.OnClickMenuCallback, self)
                                            , callback_param
                                            , nil
                                            , nil
                                            , nil
                                            , self.data)
end

function MainRoomMemberRender:GetItems()
    local items = {Language.NewTeam.MenuBrouse}
    local callback_param = {}
    local index = 1
    if nil == SocietyWGData.Instance:FindFriend(self.data.role_original_id) and not UserVo.IsCrossServer(self.data.role_original_id) then
        table.insert(items, Language.NewTeam.MenuAddFriend)
        callback_param.has_add_friend = true
        index = index + 1
        callback_param.add_friend_index = index
    elseif SocietyWGData.Instance:GetIsTeamLeader() == 0 and self.data.is_leader == 1 then
        table.insert(items, Language.NewTeam.ApplyToBeTeamLeader)
        callback_param.apply_to_leader = true
        index = index + 1
        callback_param.apply_to_leader_index = index
    end
    return items, callback_param
end

function MainRoomMemberRender:GetPos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end
    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y
    return Vector2(x, y)
end

function MainRoomMemberRender:OnClickMenuCallback(index, sender, callback_param)
    --print_error(index, sender, callback_param)
    if 1 == index then                                                                          --查看信息
        BrowseWGCtrl.Instance:OpenWithUid(self.data.role_original_id)
	elseif callback_param.has_add_friend and  callback_param.add_friend_index == index then     --加为好友
        SocietyWGCtrl.Instance:IAddFriend(self.data.role_original_id)
	elseif callback_param.is_leader and  callback_param.turn_leader_index == index then         --转移队长
        SocietyWGCtrl.Instance:SendChangeTeamLeader(self.data.role_original_id)
	elseif callback_param.is_leader and  callback_param.quit_team_index == index then           --请离队伍
        SocietyWGCtrl.Instance:SendKickOutOfTeam(self.data.role_original_id)
    elseif callback_param.apply_to_leader and callback_param.apply_to_leader_index == index then    --申请队长
        SocietyWGCtrl.Instance:SendReqMemChangeTeamLeader()
	end
end