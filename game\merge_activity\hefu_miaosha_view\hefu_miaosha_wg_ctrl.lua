require("game/merge_activity/hefu_miaosha_view/hefu_miaosha_wg_data")
require("game/merge_activity/hefu_miaosha_view/miaosha_batch_buy_view")

HeFuMiaoShaWGCtrl = HeFuMiaoShaWGCtrl or BaseClass(BaseWGCtrl)

function HeFuMiaoShaWGCtrl:__init()
	if HeFuMiaoShaWGCtrl.Instance then
		ErrorLog("[HeFuMiaoShaWGCtrl] Attemp to create a singleton twice !")
	end
	HeFuMiaoShaWGCtrl.Instance = self
    self.hefu_miaosha_data = HeFuMiaoshaWGData.New()
    self.miaosha_batch_view = MiaoshaBatchView.New()

	self:RegisterAllProtocols()

	self.refresh_remind_flag_list = {}
end

function HeFuMiaoShaWGCtrl:__delete()
	HeFuMiaoShaWGCtrl.Instance = nil

	if self.hefu_miaosha_data ~= nil then
		self.hefu_miaosha_data:DeleteMe()
		self.hefu_miaosha_data = nil
	end
    if self.miaosha_batch_view ~= nil then
		self.miaosha_batch_view:DeleteMe()
		self.miaosha_batch_view = nil
	end
end

function HeFuMiaoShaWGCtrl:RegisterAllProtocols()
	-----------------------------------限时秒杀协议------------------------------------
	self:RegisterProtocol(SCHeFuTimedSpikeInfo, "OnSCHeFuTimedSpikeInfo")
	self:RegisterProtocol(SCHeFuTimedSpikeItemUpdate, "OnSCHeFuTimedSpikeItemUpdate")
	self:RegisterProtocol(SCHeFuTimedSpikeQuotaUpdate, "OnSCHeFuTimedSpikeQuotaUpdate")
	self:RegisterProtocol(SCHeFuTimedSpikeBuyTimesInfo, "OnSCHeFuTimedSpikeBuyTimesInfo")
	self:RegisterProtocol(SCTimedSpikeNewTagInfo, "OnSCTimedSpikeNewTagInfo")--10420（所有的秒杀活动）限时秒杀新字标记
	-----------------------------------------------------------------------------------
end

-----------------------------------------------------------限时秒杀----------------------------------------------------------------

--10090// 合服活动-限时秒杀信息
function HeFuMiaoShaWGCtrl:OnSCHeFuTimedSpikeInfo(protocol)
	self.hefu_miaosha_data:SetSCHeFuTimedSpikeInfo(protocol)

	--8.1 策划说把秒杀提醒屏蔽
	-- self:CreateOpenMiaoShaTipsCountDown()

	-- self:CretaeRemindDelayFunc(protocol.type)

	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2128)
	RemindManager.Instance:Fire(RemindName.HeFu_MiaoSha)
end

--10091// 合服活动-限时秒杀商品更新
function HeFuMiaoShaWGCtrl:OnSCHeFuTimedSpikeItemUpdate(protocol)
	self.hefu_miaosha_data:SetSCHeFuTimedSpikeItemUpdate(protocol)
	--8.1 策划说把秒杀提醒屏蔽
	-- self:CreateOpenMiaoShaTipsCountDown()
	-- self:CretaeRemindDelayFunc(protocol.type)

	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2128)
	RemindManager.Instance:Fire(RemindName.HeFu_MiaoSha)
end

--10092// 合服活动-限时秒杀额度奖励更新
function HeFuMiaoShaWGCtrl:OnSCHeFuTimedSpikeQuotaUpdate(protocol)
	self.hefu_miaosha_data:SetSCHeFuTimedSpikeQuotaUpdate(protocol)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2128)
	RemindManager.Instance:Fire(RemindName.HeFu_MiaoSha)
end

--10093,// 合服活动-限时秒杀购买次数更新
function HeFuMiaoShaWGCtrl:OnSCHeFuTimedSpikeBuyTimesInfo(protocol)
	self.hefu_miaosha_data:SetSCHeFuTimedSpikeBuyTimesInfo(protocol)

    MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2128)
	RemindManager.Instance:Fire(RemindName.HeFu_MiaoSha)
end

--创建红点延迟，用于红点显示
function HeFuMiaoShaWGCtrl:CretaeRemindDelayFunc(type)
	if self.refresh_remind_flag_list[type] then
		GlobalTimerQuest:CancelQuest(self.refresh_remind_flag_list[type])
		self.refresh_remind_flag_list[type] = nil
	end
	local refresh_time_list = self.hefu_miaosha_data:GetRefreshTimeList()

	if refresh_time_list[type] == nil or refresh_time_list[type] <= 0 then
		return
	end

	--延迟到刷新时间把红点标记设置为true
	if self.refresh_remind_flag_list[type] == nil then
		self.refresh_remind_flag_list[type] = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.DelayRefreshRemindFlag, self, type), refresh_time_list[type] - 1)
	end
end


function HeFuMiaoShaWGCtrl:DelayRefreshRemindFlag(type)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	PlayerPrefsUtil.SetInt("refresh_remind_flag" .. type .. role_id, 1)
end

--延迟剩余30秒内打开提示框
function HeFuMiaoShaWGCtrl:CreateOpenMiaoShaTipsCountDown()
	if self.hefu_miaosha_tips_quset then
		GlobalTimerQuest:CancelQuest(self.hefu_miaosha_tips_quset)
		self.hefu_miaosha_tips_quset = nil
	end

	local time

	local refresh_time_list = self.hefu_miaosha_data:GetRefreshTimeList()
	--拿到最小的时间进行计时
	for i=1,3 do
		if refresh_time_list[i] ~= nil and refresh_time_list[i] > 0 then
			if time == nil then
				time = refresh_time_list[i]
			elseif time > refresh_time_list[i] then
				time = refresh_time_list[i]
			end
		end
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	-- --是否勾选今日不在展示提示框 :1(勾选) 0(未勾选)
	-- local value = PlayerPrefsUtil.GetInt("hefu_miaosha_tips_" .. role_id .. cur_day)
	--是否勾选界面内的提醒选项 :0(勾选) 1(未勾选)
	local value1 = PlayerPrefsUtil.GetInt("hefu_miaosha_toggle_" .. role_id)
	if value1 == 1 then --or value == 1 then
		return
	end

	if time == nil or time <= 0 then
		return
	end

	local temp_time = 0
	local count_down_time = 0

	if time <= 30 then
		temp_time = 0
		count_down_time = time
	else
		temp_time = time - 30
		count_down_time = 30
	end

	--延迟剩余30秒内打开提示框
	if self.hefu_miaosha_tips_quset == nil then
		self.hefu_miaosha_tips_quset = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OpenHeFuMiaoshaTips, self, count_down_time), temp_time)
	end
end

--打开限时秒杀提示框
function HeFuMiaoShaWGCtrl:OpenHeFuMiaoshaTips(time)
	if CgManager.Instance:IsCgIng() then
		return
	end
	local state = MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE)

	if not state then
		return
	end

	local yes_callback = function ()
		--打开限时秒杀界面
		MergeActivityWGCtrl.Instance:Open(TabIndex.merge_activity_2128)
	end

	if XianShiMiaoShaWGCtrl.Instance.xianshi_miaosha_tips and not XianShiMiaoShaWGCtrl.Instance.xianshi_miaosha_tips:IsOpen() and not OperationActivityWGCtrl.Instance:GetViewIsOpen() then
		XianShiMiaoShaWGCtrl.Instance.xianshi_miaosha_tips:SetData(Language.HeFuMiaoShaDesc.MiaoShaTips, time, yes_callback)
		XianShiMiaoShaWGCtrl.Instance.xianshi_miaosha_tips:Open()
	end
end
-----------------------------------------------------------------------------------------------------------------------------------
--10420（所有的秒杀活动）限时秒杀新字标记
function HeFuMiaoShaWGCtrl:OnSCTimedSpikeNewTagInfo(protocol)
	if protocol.activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE then
		self.hefu_miaosha_data:SetCTimedSpikeNewTagInfo(protocol)
		MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2128)
		GlobalEventSystem:Fire(ACTIVITY_BTN_EVENT.MERGE_ACT_BTN_CHANGE)
	elseif protocol.activity_type == ACTIVITY_TYPE.GOD_MIAO_SHA then
		TianshenRoadWGData.Instance:SetCTimedSpikeNewTagInfo(protocol)
		TianshenRoadWGCtrl.Instance:FlushTianShenMiaoShaView()
		GlobalEventSystem:Fire(ACTIVITY_BTN_EVENT.TIANSHEN_ROAD_BTN_CHANGE)
	elseif protocol.activity_type == ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA then
		XianshiMiaoshaWGData.Instance:SetCTimedSpikeNewTagInfo(protocol)
		OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_xianshi_miaosha)
		GlobalEventSystem:Fire(ACTIVITY_BTN_EVENT.OPERATION_ACTIVITY_BTN_CHANGE)
	end
end

--批量购买界面
function HeFuMiaoShaWGCtrl:OpenBatchBuyView(item_id, num, show_num, max, buy_func)
    self.miaosha_batch_view:SetData(item_id, num, show_num, max, buy_func)
end