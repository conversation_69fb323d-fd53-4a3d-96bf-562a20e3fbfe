require("game/special_draw/special_draw_wg_data")
require("game/special_draw/special_draw_trun_table")
require("game/special_draw/special_draw_reward")
SpecialActivityWGCtrl = SpecialActivityWGCtrl or BaseClass(BaseWGCtrl)
function SpecialActivityWGCtrl:__init()
	if SpecialActivityWGCtrl.Instance then
		ErrorLog("[SpecialActivityWGCtrl] Attemp to create a singleton twice !")
	end
	SpecialActivityWGCtrl.Instance = self

    self.data = SpecialActivityWGData.New()
	self.trun_table_view = SpecialDrawTrunTable.New(GuideModuleName.SpecialDrawTrunTable)
    self.draw_reward = SpecialDrawReward.New() --恭喜获得

    self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
    self:RegisterAllProtocols()
end

function SpecialActivityWGCtrl:__delete()
	SpecialActivityWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.trun_table_view then
		self.trun_table_view:DeleteMe()
		self.trun_table_view = nil
	end

    if self.trun_table_reward then
		self.trun_table_reward:DeleteMe()
		self.trun_table_reward = nil
	end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

--请求信息
function SpecialActivityWGCtrl:SendDrawReq(act_type, opera_type, param1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
    --print_error(act_type, opera_type, param1)
	protocol.rand_activity_type = act_type
	protocol.opera_type = opera_type
	protocol.param_1 = param1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function SpecialActivityWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOASpecialDrawInfo, "OnSCOASpecialDrawInfo")
	self:RegisterProtocol(SCOASpecialDrawResult, "OnSCOASpecialDrawResult")
end

function SpecialActivityWGCtrl:OnSCOASpecialDrawInfo(protocol)
	--print_error("抽奖信息", protocol)
	self.data:SetAllInfo(protocol)
	self:FlushViewByAct(protocol.rand_activity_type)
end

local SpecialDrawRemind = {
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1] = RemindName.SpecialDrawTrunTable,
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW2] = RemindName.SpecialDrawSecretCrystal,
}

function SpecialActivityWGCtrl:FlushViewByAct(act_type)
	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1 then
		if self.trun_table_view:IsOpen() then
			self.trun_table_view:Flush()
		end
	end

	RemindManager.Instance:Fire(SpecialDrawRemind[act_type])
end

--结果信息
function SpecialActivityWGCtrl:OnSCOASpecialDrawResult(protocol)
	--print_error("抽奖结果信息", protocol)
	self.data:SetResultInfo(protocol)
	
	if self.trun_table_view:IsOpen() then
		self.trun_table_view:Flush(0, "play_ani")
	end
end

--恭喜获得界面
function SpecialActivityWGCtrl:OpenRewardView(act_type)
	local data_list = self.data:GetResultInfo()
    self.draw_reward:SetData(data_list.record_list, act_type)
    self.draw_reward:Open()
end

--使用道具并弹窗
function SpecialActivityWGCtrl:ClickUseDrawItem(act_type, index, func)
	local cfg = SpecialActivityWGData.Instance:GetDrawConsumeCfg(act_type)
	local mode_cfg = SpecialActivityWGData.Instance:GetDrawItem(act_type)
	local cur_cfg = cfg[index]
	if cur_cfg == nil then
		return
	end
	
	local num = ItemWGData.Instance:GetItemNumInBagById(mode_cfg.cost_item_id)
	--不足弹窗
	if num < cur_cfg.cost_item_num then
		 if not self.alert then
			 self.alert = Alert.New()
		 end
		 self.alert:ClearCheckHook()
		 self.alert:SetShowCheckBox(true, "special_draw2")
		 self.alert:SetCheckBoxDefaultSelect(false)
		 local item_cfg = ItemWGData.Instance:GetItemConfig(mode_cfg.cost_item_id)
		 local name = ""
		 if item_cfg ~= nil then
			 name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		 end

		 local cost = mode_cfg.cost_gold * (cur_cfg.cost_item_num - num)
		 local str = string.format(Language.SpecialDraw.DrawCostStr, name, cost)
		 self.alert:SetLableString(str)
		 self.alert:SetOkFunc(func)
		 self.alert:Open()
	else
		--使用
		func()
	end
 
 end

function SpecialActivityWGCtrl:OnDayChange()
    if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1) then
		self:SendDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1, OA_SPECIAL_DRAW_OPERATE_TYPE.INFO)
	end
end