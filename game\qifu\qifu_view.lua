QiFuView = QiFuView or BaseClass(SafeBaseView)
function QiFuView:__init()
	self.view_cache_time = 0
	self.view_name = GuideModuleName.QIFU
	self.view_style = ViewStyle.Full
	self.is_align_right = true						-- 是否向右对齐
	self.is_safe_area_adapter = true
	self:SetMaskBg()

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/qifu_ui_prefab", "VerticalTabbar")
	self:AddViewResource(TabIndex.qifu_qf, "uis/view/qifu_ui_prefab", "layout_qifu_wish")
	self:AddViewResource(TabIndex.qifu_hmwd, "uis/view/qifu_ui_prefab", "layout_hongmeng_view")
	self:AddViewResource(TabIndex.qifu_yunshi, "uis/view/qifu_ui_prefab", "layout_yunshi_view")
	self:AddViewResource(TabIndex.qifu_getengrgy, "uis/view/qifu_ui_prefab", "layout_qifu_getenergy")
	self:AddViewResource(0, "uis/view/qifu_ui_prefab", "layout_qifu_exp")
	self:AddViewResource(0, "uis/view/qifu_ui_prefab", "layout_qf_top_panel")
end


function QiFuView:InitTabbar()
	local remind_tab = {
		{RemindName.WelfareQiFu,},
		{RemindName.HongMengWuDao},
		{RemindName.QiFuYunShi},  --SCVipBossXianLiInfo
		{RemindName.QiFuGetEnergy},
	}
	if not self.tabbar then
	    self.tabbar = Tabbar.New(self.node_list)
	    local is_show_lingli = BossWGData.Instance:GetIsShowLingLi()
	    local tab_grop = is_show_lingli > 0 and Language.QiFu.TabGrop2 or Language.QiFu.TabGrop
	    self.tabbar:Init(tab_grop, nil, "uis/view/qifu_ui_prefab", nil, remind_tab)
	    self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	    self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabInfo, self))
    end
    FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.QIFU, self.tabbar)
end

function QiFuView:LoadCallBack()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	--监听经验属性改变
	self.player_data_change_callback = BindTool.Bind1(self.PlayerDataChangeCallback, self)
	RoleWGData.Instance:NotifyAttrChange(self.player_data_change_callback, {"exp", "max_exp"})

	
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.QIFU, self.get_guide_ui_event)

	-- -- 创建提示框
	self:CreateAlert()

	self:InitTabbar()

	RemindManager.Instance:Fire(RemindName.HongMengWuDao)--鸿蒙悟道
	RemindManager.Instance:Fire(RemindName.QiFuGetEnergy)
end

function QiFuView:LoadIndexCallBack(index)
	if index == TabIndex.qifu_qf then
		self:InitWelfareQiFuView()
	elseif index == TabIndex.qifu_hmwd then
		self:InitHongMengView()
	elseif index == TabIndex.qifu_yunshi then
		self:InitYunShiView()
	elseif index == TabIndex.qifu_getengrgy then
		self:InitGetEnergyView()
	end
	if self.node_list.btn_close_window then
		XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.CloseWindows, self))
	end

end

function QiFuView:CloseCallBack()
	local show_index = self:GetShowIndex()
	if show_index == TabIndex.qifu_yunshi then
		self:YunShiCloseCallBack()
	end
	self.is_flush_exp_show = false
end

function QiFuView:CloseWindows()
		-- if self.node_list.cat_glow then
		-- 	self.node_list.cat_glow:SetActive(false)
		-- end
		self:Close()
end

function QiFuView:SetTabInfo()
	if self.tabbar then
		--local is_show = QiFuWGData.Instance:GetBtnIsShow()
		local is_show = FunOpen.Instance:GetFunIsOpenedByTabName("qifu_hmwd")
		local is_show_2 = FunOpen.Instance:GetFunIsOpenedByTabName("qifu_yunshi")
		self.tabbar:SetVerToggleVisble(TabIndex.qifu_hmwd, is_show)
		self.tabbar:SetVerToggleVisble(TabIndex.qifu_yunshi, is_show_2)
	end
	-- self:SetTabbar()
end

function QiFuView:SetBg(index)
	local key = math.ceil(index / 10)
	local bundle, asset = ResPath.GetRawImagesPNG("a3_qf_bg_1")
	if key == 2 then
		bundle, asset = ResPath.GetRawImagesPNG("a3_qf_bg_2")
	elseif key == 4 then
		bundle, asset = ResPath.GetRawImagesJPG("a3_qf_bg_4")
	end
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function QiFuView:SetTabbar()
	if not self.tabbar then
		return
	end
	local ver_cell_list = self.tabbar:GetVerCellList()
	if nil == ver_cell_list then
		return
	end
	local is_show = QiFuWGData.Instance:GetBtnIsShow()
	-- for k,v in pairs(ver_cell_list) do
	-- 	if is_show then
	-- 		if k*10 == TabIndex.qifu_qf then
	-- 			-- v:SetNoShowElement(false)
	-- 			-- v:SetVerTextSize(28)
	-- 			v:SetHandOrTailShow(true, true, false)
	-- 		elseif k*10 == TabIndex.qifu_hmwd then
	-- 			-- v:SetNoShowElement(true)
	-- 			-- v:SetVerTextSize(26)
	-- 			v:SetHandOrTailShow(false, false, true)
	-- 		end
	-- 	else
	-- 		if k*10 == TabIndex.qifu_qf then
	-- 			-- v:SetNoShowElement(true)
	-- 			-- v:SetVerTextSize(28)
	-- 			v:SetHandOrTailShow(true, false, true)
	-- 		elseif k*10 == TabIndex.qifu_hmwd then
	-- 			-- v:SetNoShowElement(true)
	-- 			-- v:SetVerTextSize(26)
	-- 			v:SetHandOrTailShow(false, false, false)
	-- 		end
	-- 	end
	-- end
end

function QiFuView:ShowIndexCallBack(index, loaded_times)
	if index == TabIndex.qifu_hmwd then
		RankWGCtrl.Instance:SendRankReq(RankKind.Person,PersonRankType.Level)   -- 請求排行榜信息
	end
	self:SetTabInfo()
	self:SetBg(index)
	self:Flush(index)
	self:ClearChouQianTimer(true)

	if not self.is_flush_exp_show then
		self.is_flush_exp_show = true
		self:ChangeExpSlider()
	end
end

function QiFuView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	RoleWGData.Instance:UnNotifyAttrChange(self.player_data_change_callback)

	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.QIFU, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	self:DelWelfareQiFuView()
	self:DelHongMengView()
	self:DelYunShiView()
	self:DelGetEnergyView()
end


function QiFuView:OnFlush(param_t, index)
	if index == TabIndex.qifu_qf then
		self:FlushWelfareQiFuView()
	elseif index == TabIndex.qifu_hmwd then
		self:FlushHongMengView()
	elseif index == TabIndex.qifu_yunshi then
		self:FlushYunShiView()
	elseif index == TabIndex.qifu_getengrgy then
		self:FlushGetEnergyView()
	end
end

function QiFuView:PlayerDataChangeCallback(attr_name, value, old_value)
	if attr_name == "exp" or attr_name == "max_exp" then
		self:ChangeExpSlider()
	end
end

function QiFuView:ChangeExpSlider()
	if not self.node_list["qifu_exp_slider"] or not self.node_list["qifu_exp_effect"] then
		return
	end
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local cur_max_exp = RoleWGData.Instance.GetRoleExpCfgByLv(vo.level).exp
	local slider_value = vo.exp / cur_max_exp
	self.node_list["qifu_exp_slider"].slider.value = slider_value
	self.node_list["qifu_exp_effect"]:SetActive(false)
	self.node_list["qifu_exp_effect"]:SetActive(true)
end

function QiFuView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "qiantong_img" then
		return self.node_list.guide_effect_root, BindTool.Bind(self.OnChouQianBtn, self)
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end
--------------------------------------------------------------

