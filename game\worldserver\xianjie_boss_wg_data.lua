--因为BossData太长，因此把仙界boss相关的分出来

XianJieBossWGData = XianJieBossWGData or BaseClass()
XianJieBossWGData.MaxChipIndex = 7
function XianJieBossWGData:__init()
	if XianJieBossWGData.Instance then
		error("[XianJieBossWGData] Attempt to create singleton twice!")
		return
	end
	XianJieBossWGData.Instance = self
    self.xianjie_boss_auto = ConfigManager.Instance:GetAutoConfig("cross_fairyland_boss_auto")
    self.xianjie_other_cfg = self.xianjie_boss_auto.other[1]
    self.xianjie_layer_cfg = self.xianjie_boss_auto.layer
    self.xianjie_layer_list = ListToMapList(self.xianjie_boss_auto.layer, "slot_limit")
    self.xianjie_boss_list = ListToMapList(self.xianjie_boss_auto.monster, "layer")
    self.xianjie_boss_layer_cfg = self.xianjie_boss_auto.monster
    self:InitBossCfg()
    self.server_info_list = {}
    RemindManager.Instance:Register(RemindName.WorldServer_Xianjie, BindTool.Bind(self.GetXianjieRed, self))
    self:RegisterXianJieBossRemindInBag(RemindName.WorldServer_Xianjie)
end

function XianJieBossWGData:__delete()
	XianJieBossWGData.Instance = nil
    self.server_info_list = {}
    RemindManager.Instance:UnRegister(RemindName.WorldServer_Xianjie)
    if self.time_delay_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.time_delay_timer)
        self.time_delay_timer = nil
    end
end

function XianJieBossWGData:GetLayerListBySlot(slot_limit)
    return self.xianjie_layer_list[slot_limit] or {}
end

function XianJieBossWGData:GetLayerListNum()
    return #self.xianjie_layer_list
end

function XianJieBossWGData:GetBossListByLayer(layer)
    return self.boss_list_layer[layer] or {}
end

function XianJieBossWGData:GetXianjieRed()
    local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.XianJieBoss)
    if not is_open then
        return 0
    end
    local remain, total = self:GetXianjieEnterInfo()
    local cfg = self:GetOtherCfg()
    local can_enter = self:GetIsCanEnterWithTime()
    if can_enter then
        if remain > 0 then
            return 1
        elseif cfg and cfg.enter_cost_item_id then
            local num = ItemWGData.Instance:GetItemNumInBagById(cfg.enter_cost_item_id)
            if num >= cfg.enter_cost_item_num then
                return 1
            end
        end
    end 
    return 0 
end

function XianJieBossWGData:InitBossCfg()
    self.boss_id_list = {}
    self.boss_list_layer = {}
    local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list
    for k, v in ipairs(self.xianjie_boss_layer_cfg) do
        if not self.boss_list_layer[v.layer] then
            self.boss_list_layer[v.layer] = {}
        end
        local temp = {}
        temp.monster_id = v.monster_id
        temp.boss_id = v.monster_id
        temp.layer = v.layer
        local pos = v.monster_pos
        local pos_tb = Split(pos,",")
        temp.x_pos = tonumber(pos_tb[1])
        temp.y_pos = tonumber(pos_tb[2])
        temp.relive_time = v.relive_time
        temp.drop_owner_add_tired = v.drop_owner_add_tired
        temp.drop_item_list = v.drop_item_list
        temp.reward_tips_des = v.reward_tips_des
        temp.boss_index = v.boss_index
        temp.bottom_color = v.bottom_color

        local boss_cfg = monster_cfg[v.monster_id]
        if boss_cfg == nil then
            print_error("配置有问题，取不到boss配置，bossid =", v.monster_id)
        else
            temp.boss_name = boss_cfg.name
            temp.boss_level = boss_cfg.level
            temp.big_icon = boss_cfg.small_icon
            temp.resid = boss_cfg.resid
            temp.scale = temp.scale or 1
        end
        
        local layer_cfg = self:GetBossLayerCfgByLayer(v.layer)
        temp.layer = layer_cfg.layer
        temp.scene_id = layer_cfg.scene_id
        temp.role_level_limit = layer_cfg.role_level_limit
        temp.need_role_level = layer_cfg.role_level_limit
        temp.vip_level_limit = layer_cfg.vip_level_limit
        temp.fairyland_equip_level_limit = layer_cfg.fairyland_equip_level_limit
        temp.slot_limit = layer_cfg.slot_limit
        temp.slot_name = layer_cfg.slot_name
        temp.slot_page_limit = layer_cfg.slot_page_limit
        temp.group_num = layer_cfg.group_num
        temp.shop_seq = layer_cfg.shop_seq

        temp.type = BossWGData.MonsterType.Boss
        self.boss_id_list[temp.monster_id] = temp
        BossWGData.Instance:AddToNewCrossBossList(temp.monster_id, temp)
        table.insert(self.boss_list_layer[v.layer], temp) 
    end
end

function XianJieBossWGData:GetBossAngry(boss_id)
    local cfg = self.boss_id_list[boss_id]
    return cfg and cfg.drop_owner_add_tired or 0
end


function XianJieBossWGData:GetBossListlayer()
    return self.boss_list_layer
end

function XianJieBossWGData:GetOtherCfg()
    return self.xianjie_other_cfg
end

function XianJieBossWGData:RegisterXianJieBossRemindInBag(remind_name)
    local cfg = self:GetOtherCfg()
    if cfg and cfg.enter_cost_item_id then
        local item_id_list = {cfg.enter_cost_item_id}
        BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
    end
end


function XianJieBossWGData:GetReliveDiscountPercent()
    return self.xianjie_other_cfg.be_kill_monster_hurt_dec_per
end

--界面倒序
function XianJieBossWGData:GetBossListlayerForView()
    local list = {}
    for k, v in pairs(self.boss_list_layer) do
        local temp = {}
        temp.index = k
        temp.boss_list = v
        table.insert(list, temp)
    end

 --    local temp_list = {}
	-- local length = #list
	-- for k, v in pairs(list) do
	-- 	temp_list[length - k + 1] = v
	-- end
    return list
end

--一层的boss列表
function XianJieBossWGData:GetBossListlayerByLayer(layer)
    return self.boss_list_layer and self.boss_list_layer[layer]
end

function XianJieBossWGData:GetBossCfgById(boss_id)
    local temp = self.boss_id_list[boss_id]
    if temp == nil then
        return --场景类型判断不安全，加判空
    end
    local server_info = self:GetBossInfoById(boss_id)
    temp.next_refresh_time = server_info and server_info.next_refresh_time or 0
    temp.is_concern = server_info and server_info.is_concern or 0
    return temp
end

function XianJieBossWGData:GetBossLayerCfgBySceneId(scene_id)
    for k, v in pairs(self.xianjie_layer_cfg) do
        if scene_id == v.scene_id then
            return v
        end
    end
end

function XianJieBossWGData:GetBossListBySceneId(scene_id)
    if not self.xianjie_boss_scene_list then
        self.xianjie_boss_scene_list = {}
    end
	if not self.xianjie_boss_scene_list[scene_id] then
		self.xianjie_boss_scene_list[scene_id] = {}
		for i, v in pairs(self.boss_id_list) do
			if v.scene_id == scene_id then
				table.insert(self.xianjie_boss_scene_list[scene_id], v)
			end
        end
        table.sort(self.xianjie_boss_scene_list[scene_id], SortTools.KeyLowerSorter("boss_level"))
	end
	return self.xianjie_boss_scene_list[scene_id]
end

function XianJieBossWGData:GetBossLayerCfgByLayer(layer)
    for k, v in pairs(self.xianjie_layer_cfg) do
        if layer == v.layer then
            return v
        end
    end
end

function XianJieBossWGData:GetBossLayerCfg()
    return self.xianjie_layer_cfg
end

function XianJieBossWGData:SaveBossInfo(protocol)
    if not self.server_info_list then
        self.server_info_list = {}
    end
    local info_list = protocol.boss_info_list
    for k, v in pairs(info_list) do
        self.server_info_list[v.boss_id] = v
    end
    -- self:NeedJudgeTimer()
end

function XianJieBossWGData:GetBossInfoById(boss_id)
    return self.server_info_list[boss_id]
end

function XianJieBossWGData:SaveBossInfoUpdate(protocol)
    if not self.server_info_list then
        self.server_info_list = {}
    end
    if not self.server_info_list[protocol.boss_id] then
        self.server_info_list[protocol.boss_id] = {}
    end
    self.server_info_list[protocol.boss_id].next_refresh_time = protocol.next_refresh_time
    self.server_info_list[protocol.boss_id].is_concern = protocol.is_concern
end

function XianJieBossWGData:SaveBossDropHistory(protocol)
    self.drop_history_item_list = protocol.drop_history_item_list
end

function XianJieBossWGData:AddBossDropHistory(protocol)
    if not self.drop_history_item_list then
        self.drop_history_item_list = {}
    end
    local temp = protocol.drop_history_item
    temp.index = #self.drop_history_item_list + 1
    table.insert(self.drop_history_item_list, temp)
end

function XianJieBossWGData:GetBossDropHistory()
    return self.drop_history_item_list or {}
end

function XianJieBossWGData:GetBossDefaultIndex()
    return 1
end

--界面显示倒序
function XianJieBossWGData:GetBossLayerDefaultIndex()
    local cur_slot, cur_page = self:GetCurSlotAndPage()
    for i = #self.boss_list_layer, 1, -1 do
        local cfg = self:GetBossLayerCfgByLayer(i)
        if cur_slot == cfg.slot_limit and cur_page == cfg.slot_page_limit then
            return cfg.layer
        end
    end
    return 1
end

--获取当前可进入的最大层数
function XianJieBossWGData:GetMaxLayer()
    local layer = 1
    local rolelv = RoleWGData.Instance:GetRoleLevel()
    for i = #self.xianjie_layer_cfg, 1, -1 do
        local cfg = self:GetBossLayerCfgByLayer(i)
        if cfg.role_level_limit <= rolelv then
            layer = i
            break
        end
    end
    return layer
end

function XianJieBossWGData:SetXianJieTiredValue(protocol)
	self.tired_value = protocol.tired
    self.kick_out_time = protocol.kick_out_time
end


function XianJieBossWGData:SaveCurEnemyInfo(enemy_info)
    self.cur_enemy_info = enemy_info
end

function XianJieBossWGData:GetCurEnemyInfo()
	return self.cur_enemy_info
end

function XianJieBossWGData:ClearCurEnemyInfo()
    self.cur_enemy_info = nil
end

function XianJieBossWGData:GetXianJieTiredValue()
	return self.tired_value, self.kick_out_time
end

function XianJieBossWGData:SetXianJieEnter(times)
	self.enter_times = times
end

--返回剩余进入次数，总次数
function XianJieBossWGData:GetXianjieEnterInfo()
    local enter_times = self.enter_times or 0
    local vip_times = VipWGData.Instance:GetVipSpecPermissionsValue(VIP_LEVEL_AUTH_TYPE.VAT_CROSS_FAIRYLAND_BOSS_ENTER_TIMES)
    local cfg_times = tonumber(vip_times) or 0
    return cfg_times - enter_times, cfg_times
end

function XianJieBossWGData:GetMonsterChallengeTime(need_ret)
    local other_cfg = self.xianjie_other_cfg
    if need_ret or not self.end_chal_time then
        self.end_chal_time = TimeUtil.FormatCfgTimestamp(other_cfg.wait_summary_time)
        self.begin_chal_time = TimeUtil.FormatCfgTimestamp(other_cfg.open_time)
    end
    return self.end_chal_time, self.begin_chal_time
end

function XianJieBossWGData:GetMonsterChallengeEndTime()
    local cur_end_time = self:GetMonsterChallengeTime()
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    if cur_time < cur_end_time then
        return cur_end_time
    else
        return cur_end_time + 86400
    end
end

function XianJieBossWGData:GetIsCanEnterWithTime()
    return true
    --[[
    local cur_end_time, cur_open_time = self:GetMonsterChallengeTime()
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    local zero_begin_time = TimeWGCtrl.Instance:NowDayTimeStart(cur_time)
    local zero_end_time = TimeWGCtrl.Instance:NowDayTimeEnd(cur_time)
    local can_enter
    if cur_time >= zero_begin_time and cur_time < cur_end_time then
        can_enter = true
    elseif cur_time >= cur_end_time and cur_time < cur_open_time then
        can_enter = false
    elseif cur_time >= cur_open_time and cur_time < zero_end_time then
        can_enter = true
    end
    return can_enter
    ]]
end

function XianJieBossWGData:NeedJudgeTimer()
    if self.time_delay_timer ~= nil then
        return
    end
    local cur_end_time, cur_open_time = self:GetMonsterChallengeTime()
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    local zero_begin_time = TimeWGCtrl.Instance:NowDayTimeStart(cur_time)
    local zero_end_time = TimeWGCtrl.Instance:NowDayTimeEnd(cur_time)
    local time_delay
    if cur_time >= zero_begin_time and cur_time < cur_end_time then
        time_delay = cur_end_time - cur_time
    elseif cur_time >= cur_end_time and cur_time < cur_open_time then
        time_delay = cur_open_time - cur_time
    elseif cur_time >= cur_open_time and cur_time < zero_end_time then
        time_delay = zero_end_time - cur_time
    end

    --print_error("打印信息非报错>>>>>>>>>>>>>>>>>>time_delay",time_delay, "cur_time", cur_time, cur_end_time, zero_begin_time)
    if time_delay ~= nil then
		self.time_delay_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.ChallengStateChange, self), time_delay)
	end
end

function XianJieBossWGData:ChallengStateChange()
    if self.time_delay_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.time_delay_timer)
        self.time_delay_timer = nil
    end
    self:GetMonsterChallengeTime(true)
    ViewManager.Instance:FlushView(GuideModuleName.WorldServer, TabIndex.xianjie_boss)
    RemindManager.Instance:Fire(RemindName.WorldServer_Xianjie)
    -- self:NeedJudgeTimer()
end

function XianJieBossWGData:SaveBaseInfo(protocol)
    self.open_xianjie_time = protocol.open_time
end

function XianJieBossWGData:GetEquipActiveInfo(slot, page)
    local list = {}
    for k = 0, XianJieBossWGData.MaxChipIndex do
        local is_active = FairyLandEquipmentWGData.Instance:GetPagePartAct(slot, page, k)
        list[k] = is_active
    end
	return list
end

function XianJieBossWGData:GetCurSlotAndPage()
    local slot, page = FairyLandEquipmentWGData.Instance:GetGodBookCurMaxShow()
	return slot, page
end

-- need_short是否显示缩写
function XianJieBossWGData:GetPageStr(cur_page, need_short)
    local str_page = FairyLandEquipmentWGData.Instance:GetGodBookCfg(cur_page)
    if not need_short then
        return str_page and str_page.page_name or cur_page
    else
        return str_page and str_page.page_s_name or cur_page
    end
end

function XianJieBossWGData:GetCurPageIsOpen(slot_limit, slot_page_limit)
    local cur_slot, cur_page = self:GetCurSlotAndPage()
    local stage_can_enter
    if cur_slot > slot_limit then
        stage_can_enter = true
    elseif cur_slot == slot_limit then
        stage_can_enter = cur_page >= slot_page_limit
    else
        stage_can_enter = false
    end
    return stage_can_enter
end

--返回是否需要显示仙界装备
function XianJieBossWGData:GetIsShowXianJieEquip(xianjie_boss_equip, role)
    if IsEmptyTable(xianjie_boss_equip) then
        return false
    end

    local other_vo = role and role:GetVo()
    local is_can_attack = BossWGCtrl.Instance:GetXianJieSceneIsCanAttack(other_vo)
    local is_team_member = CrossTeamWGData.Instance:GetTargetIsTeamMember(other_vo.uuid)
    if not is_can_attack or is_team_member then
        return false
    end

    local slot, page = self:GetCurSlotAndPage() -- 自身当前的神体，书页
    if xianjie_boss_equip.slot ~= slot or xianjie_boss_equip.page ~= page then --他人当前神体，书页比自身高或者低
        return false
    end
    local list = FairyLandEquipmentWGData.Instance:GetPageAllPartActList(slot, page)
    local other_list = xianjie_boss_equip.active_tb
    --self:TestPrintInfo(list, other_list)
    for k = 0, XianJieBossWGData.MaxChipIndex do
        if list[32 - k] and list[32 - k] == 0 and other_list[k] == 1 then
            local cfg = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByData(slot, page, k)
            return true, cfg.item_id
        end
    end
    return false
end

-- function XianJieBossWGData:TestPrintInfo(list, other_list)
--     local str = ""
--     for k = 0, XianJieBossWGData.MaxChipIndex do
--         str = str.."   ".. string.format("第%s个部位，我：%s, 对方：%s",k, list[32 - k], other_list[k]) 
--     end
--     print_error(">>>>>>>TestPrintInfo>>>>>>>", str)
-- end

function XianJieBossWGData:SaveXianjieEnemyInfo(protocol)
    if not self.xianjie_enemy_info_list then
        self.xianjie_enemy_info_list = {}
    end
    self.xianjie_enemy_info_list = protocol.record_enemy_list
end

function XianJieBossWGData:AddXianjieEnemyInfo(protocol)
    if not self.xianjie_enemy_info_list then
        self.xianjie_enemy_info_list = {}
    end
    local is_exist = false
    local item = protocol.add_item
    for k, v in pairs(self.xianjie_enemy_info_list) do
        if v.uuid == item.uuid then
            is_exist = true
            self.xianjie_enemy_info_list[k] = item
            break
        end
    end
    if not is_exist then
        table.insert(self.xianjie_enemy_info_list, item)
    end
end

function XianJieBossWGData:RemoveXianjieEnemyInfo(protocol)
    if not IsEmptyTable(self.xianjie_enemy_info_list) then
        for k, v in pairs(self.xianjie_enemy_info_list) do
            if v.uuid == protocol.remove_uuid then
                v = nil
                table.remove(self.xianjie_enemy_info_list, k)
            end
        end
    end
end

function XianJieBossWGData:ClearXianJieEnemyInfo()
    self.xianjie_enemy_info_list = {}
end

function XianJieBossWGData:GetXianJieEnemyInfo()
    return self.xianjie_enemy_info_list
end

function XianJieBossWGData:GetCanPickItemById(id)
    local xianjie_item_cfg = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByItemId(id)
    if xianjie_item_cfg then
        local cur_slot, cur_page = self:GetCurSlotAndPage()
        if cur_slot == xianjie_item_cfg.slot and cur_page == xianjie_item_cfg.page then
            local is_active = FairyLandEquipmentWGData.Instance:GetPagePartAct(xianjie_item_cfg.slot, xianjie_item_cfg.page, xianjie_item_cfg.part)
            if is_active then
                return false
            else
                return true
            end
        else
            return false
        end
    else
        return true
    end
    return false
end

function XianJieBossWGData:GetIsShowAreaIndexImg(area_index)
    local scene_type = Scene.Instance and Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.XianJie_Boss then
        return false
    end

    local main_role = Scene.Instance:GetMainRole()
    if not main_role then
        return false
    end

    -- local my_attack_mode = main_role and main_role:GetVo().attack_mode
    -- if my_attack_mode == ATTACK_MODE.AREA then
    --     return true
    -- end
    local my_area = main_role and main_role:GetVo().area_index
    if my_area ~= area_index then
        return true
    end
    
    return false
end

function XianJieBossWGData:GetCurPageCanActive(slot, page)
    local cur_slot, cur_page = XianJieBossWGData.Instance:GetCurSlotAndPage()
    slot = slot or cur_slot
    page = page or cur_page
    local act_page_remind = FairyLandEquipmentWGData.Instance:GetPageActRemindWithoutNew(slot, page)
    return act_page_remind
end