require("game/yibenwanli/yibenwanli_view")
require("game/yibenwanli/yibenwanli_wg_data")
require("game/yibenwanli/yibenwanli_daily_gift_view")
require("game/yibenwanli/yibenwanli_lianchong_gift_view")
require("game/yibenwanli/yibenwanli_yueka_view")

YiBenWanLiWGCtrl = YiBenWanLiWGCtrl or BaseClass(BaseWGCtrl)

function YiBenWanLiWGCtrl:__init()

	if YiBenWanLiWGCtrl.Instance then
		print_error("[YiBenWanLiWGCtrl] attempt to create singleton twice!")
		return
	end

	YiBenWanLiWGCtrl.Instance = self

	self.view = YiBenWanLiView.New(GuideModuleName.YiBenWanLiView)
	self.data = YiBenWanLiWGData.New()

	self:RegisterAllProtocols()
end

function YiBenWanLiWGCtrl:__delete()
	YiBenWanLiWGCtrl.Instance = nil
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
end

function YiBenWanLiWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSYiBenWanLiReq)
	self:RegisterProtocol(SCYiBenWanLiInfo, "OnSCYiBenWanLiInfo")
end

function YiBenWanLiWGCtrl:OnSCYiBenWanLiInfo(protocol)
	self.data:SetYiBenWanLiPortocol(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.YiBenWanLiView)
	RemindManager.Instance:Fire(RemindName.YiBenWanLi)
	RemindManager.Instance:Fire(RemindName.YiBenWanLi_DailyGift)
	RemindManager.Instance:Fire(RemindName.YiBenWanLi_LianChongGift)
	local activity_status = protocol.open_flag == 1 and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.YIBWNWANLI, activity_status,0,nil,nil,RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL)
end

function YiBenWanLiWGCtrl:SendYiBenWanLiWGCtrl(op_type,param)
	local protocol = ProtocolPool.Instance:GetProtocol(CSYiBenWanLiReq)
	protocol.op_type = op_type or 0
	protocol.param = param or 0
	protocol:EncodeAndSend()
end
