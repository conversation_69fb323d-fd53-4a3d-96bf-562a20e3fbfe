VipFreeRender = VipFreeRender or BaseClass(BaseRender)

function VipFreeRender:__init(instance)
	self.card_seq = nil
	self.save_activity_card_seq = -1
	local bundle_name, asset_name = "uis/view/main_ui_prefab", "tip_free_panel"
	self:LoadAsset(bundle_name, asset_name, instance.transform)
end

function VipFreeRender:__delete()
	self.card_seq = nil
	self.save_activity_card_seq = nil
end

function VipFreeRender:LoadCallBack()
	self.card_seq = nil
	self.save_activity_card_seq = -1

	if self.node_list.click_btn then
		self.node_list.click_btn.button:AddClickListener(BindTool.Bind(self.OnClickGetBtn, self))
	end
	if not self.scene_change_complete_event then
		self.scene_change_complete_event = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneChangeComplete, self))
	end
	self.view.transform:SetSiblingIndex(0)
end

function VipFreeRender:ReleaseCallBack()
	self:StopCanGetImgTween()
	if self.scene_change_complete_event then
		GlobalEventSystem:UnBind(self.scene_change_complete_event)
		self.scene_change_complete_event = nil
	end
	CountDownManager.Instance:RemoveCountDown("vip_free_tips")
end

function VipFreeRender:OnFlush()
	local has_free_card, card_seq = VipWGData.Instance:CheckHasFreeCard()
	CountDownManager.Instance:RemoveCountDown("vip_free_tips")
	if not has_free_card or not card_seq then
		self:SetVisible(false)
		MainuiWGCtrl.Instance:ChangeVipBtnShow(true)
		return
	end
	MainuiWGCtrl.Instance:ChangeVipBtnShow(true)

	self:StopCanGetImgTween()
	self:SetCanGetEffect(false)
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()

	self:OnSceneChangeComplete()

	self.card_seq = card_seq
	local card_cfg = VipWGData.Instance:GetVipCardCfg(card_seq)
	if card_cfg.active_condition == VipWGData.VipCardActiveType.online then
		local online_time_s = TimeWGCtrl.Instance:GetOnlineTimes()
		local time = card_cfg.active_value * 60 - online_time_s
		time = time > 0 and time or 0
		if time > 0 then
			self.node_list.time_label.text.text = TimeUtil.FormatSecondDHM9(time)
			self:StarCountDownTime(time)
		else
			self.node_list.time_label.text.text = Language.Vip.VipTips_15
			self:SetCanGetEffect(true)
		end
		self.node_list.time_bg:SetActive(true)
	elseif card_cfg.active_condition == VipWGData.VipCardActiveType.day then
		local login_day_count = ServerActivityWGData.Instance:GetTotalLoginDays()
		if login_day_count >= card_cfg.active_value then
			self.node_list.time_label.text.text = Language.Vip.VipTips_15
			self:SetCanGetEffect(true)
		else
			local now_time = TimeWGCtrl.Instance:GetServerTime()
			local end_time = TimeWGCtrl.Instance:NowDayTimeEnd(now_time)
			local time = end_time - now_time
			self.node_list.time_label.text.text = TimeUtil.FormatSecondDHM9(time)
			self:StarCountDownTime(time)
		end
		self.node_list.time_bg:SetActive(true)
	else
		self.node_list.time_label.text.text = ""
		self.node_list.time_bg:SetActive(false)
	end
end

function VipFreeRender:PlayCanGetImgTween()
	if not self.tween_yoyo then
		self:StopCanGetImgTween()
		local sequence = DG.Tweening.DOTween.Sequence()
		UITween.ShakeAnimi(self.node_list.vip_img.transform, sequence)
		self.tween_yoyo = sequence
	else
		self.tween_yoyo:Restart()
	end
end

function VipFreeRender:StopCanGetImgTween()
	if self.tween_yoyo then
		self.tween_yoyo:Kill(true)
		self.tween_yoyo = nil
    end
end

function VipFreeRender:OnSceneChangeComplete(old_scene_type, new_scene_type)
	if self.node_list and self.node_list["content"] then
		local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
		local is_open = VipWGData.Instance:CheckHasFreeCard()
		if fb_scene_cfg and fb_scene_cfg.is_show_free_vip == 0 or not is_open then
			self:SetVisible(false)
		else
			self:SetVisible(true)
		end
	end
end

function VipFreeRender:StarCountDownTime(total_time)
	CountDownManager.Instance:AddCountDown("vip_free_tips", function (time, total_time)
			self.node_list.time_label.text.text = TimeUtil.FormatSecondDHM6(total_time - time)
		end, function ()
			self.node_list.time_label.text.text = Language.Vip.VipTips_15
			self:SetCanGetEffect(true)
			RemindManager.Instance:Fire(RemindName.Vip_Col)
		end, nil, total_time + 1, 1)
end

function VipFreeRender:OnClickGetBtn()
	VipWGCtrl.Instance:OpenVipTrialPanel()
end

function VipFreeRender:SetCanGetEffect(can_get)
	self.node_list.red_point:SetActive(can_get)
	-- 时间到了直接弹出面板
	if can_get and (self.card_seq >= 0 and self.card_seq <= 2) and self.save_activity_card_seq ~= self.card_seq then
		if not ViewManager.Instance:IsOpen(GuideModuleName.NormalGuideView) then
			self:OnClickGetBtn()
		end
		self.save_activity_card_seq = self.card_seq
	end
end