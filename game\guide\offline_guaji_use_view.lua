
--------------------------------------------------
--一键使用视图
--------------------------------------------------
OfflineGuJiUseView = OfflineGuJiUseView or BaseClass(SafeBaseView)

local ACTIVE_TEXT_LEVEL = 100 		-- 多少级之前才显示文本

function OfflineGuJiUseView:__init()
	if OfflineGuJiUseView.Instance then
		ErrorLog("[OfflineGuJiUseView] Attemp to create a singleton twice !")
	end

	self.view_layer = UiLayer.Pop
	self.calc_active_close_ui_volume = false

	self.ui_config = {"uis/view/guide_ui_prefab", "GuideUi"}
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third4_panel")
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "layout_guaji_use")
end

function OfflineGuJiUseView:__delete()

end

function OfflineGuJiUseView:ReleaseCallBack()
	if nil ~= self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function OfflineGuJiUseView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_look, BindTool.Bind(self.OnClickLook, self))
	XUI.AddClickEventListener(self.node_list.btn_close_windows, BindTool.Bind1(self.Close, self))
	if self.item_cell == nil then
		self.item_cell = ItemCell.New(self.node_list.item)
	end
	--先不做判断因为这个时候背包物品还没下发判断不出来
	-- local num1 = ItemWGData.Instance:GetItemNumInBagById(SPECIAL_GUAJICARD.IDONE)
	-- local num2 = ItemWGData.Instance:GetItemNumInBagById(SPECIAL_GUAJICARD.IDTWO)
	-- if num1 <=0 and num2 <= 0 then
		self.item_cell:SetData({item_id = SPECIAL_GUAJICARD.IDTWO})
		local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(SPECIAL_GUAJICARD.IDTWO)
		if item_cfg then
			self.node_list.item_name_txt.text.text = item_cfg.name
		end
	-- elseif num1 > 0 then
	-- 	self.item_cell:SetData({item_id = SPECIAL_GUAJICARD.IDONE,num = num1})
	-- 	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(SPECIAL_GUAJICARD.IDONE)
	-- 	if item_cfg then
	-- 		self.node_list.item_name_txt.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	-- 	end
	-- elseif num2 > 0 then
	-- 	self.item_cell:SetData({item_id = SPECIAL_GUAJICARD.IDTWO,num = num2})
	-- 	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(SPECIAL_GUAJICARD.IDTWO)
	-- 	if item_cfg then
	-- 		self.node_list.item_name_txt.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	-- 	end
	-- end

	local remain_time = OfflineRestWGData.Instance:GetRemainOfflineRestTime()
	if remain_time == 0 then
		self.node_list.Offline_time.text.text = ToColorStr(string.format(Language.Common.RemainTime1, "00:00:00"), COLOR3B.RED)
	else--if remain_time <= 4 * 3600 then
		self.node_list.Offline_time.text.text = ToColorStr(string.format(Language.Common.RemainTime1, TimeUtil.FormatSecond(remain_time)), COLOR3B.L_GREEN)
	end
end

function OfflineGuJiUseView:OnFlush()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	self.node_list.use_text:SetActive(role_level < ACTIVE_TEXT_LEVEL)
end

function OfflineGuJiUseView:OnClickLook()
	local num1 = ItemWGData.Instance:GetItemNumInBagById(SPECIAL_GUAJICARD.IDONE)
	local num2 = ItemWGData.Instance:GetItemNumInBagById(SPECIAL_GUAJICARD.IDTWO)
	if num1 <= 0 and num2 <= 0 then
		-- local tab_index = 50 --绑玉商店特殊处理 ShopWGData.Instance:GetItemSellTabIndex(SPECIAL_GUAJICARD.IDTWO)
		-- ViewManager.Instance:Open(GuideModuleName.Shop, tab_index, "select_item_id", {item_id = SPECIAL_GUAJICARD.IDTWO})
		local tab_index = ShopWGData.Instance:GetShopTabNumIndex(SPECIAL_GUAJICARD.IDTWO)
		ViewManager.Instance:Open(GuideModuleName.Market, tab_index)
	else
		SettingWGData.Instance:SetViewOpenForm(1)
		FunOpen.Instance:OpenViewByName(GuideModuleName.Setting, "setting_guaji")
	end
	FunctionGuide.Instance:CloseOfflineGuaJiLook()
end
