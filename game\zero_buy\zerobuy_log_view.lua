ZeroBuyLogView = ZeroBuyLogView or BaseClass(SafeBaseView)

function ZeroBuyLogView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/zero_buy_ui_prefab", "zerobuy_log_view")
	self:SetMaskBg()
end
local monty_type = {
	[1] = "icon_coin_xianyu",
	[2] = "icon_coin_xianyu_bind",
	[3] = "icon_yuanbao",
	[4] = "icon_coin",

}

local YILINGQU = 4 --领取
local YIGOUMAI = 3 	--购买

function ZeroBuyLogView:__delete()

end

function ZeroBuyLogView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ZeroBuy.TitleName
	self:SetSecondView(Vector2(597,434))
	self.list_view = AsyncListView.New(ZeroBuyLogCell,self.node_list.list_view)
	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.OnClickClose,self))
end

-- 切换标签调用
function ZeroBuyLogView:ShowIndexCallBack()
	self:Flush()
end

function ZeroBuyLogView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
	end
end

function ZeroBuyLogView:OnFlush()
	local log_list = LayoutZeroBuyWGData.Instance:GetZeroBuyLogList()
	if not log_list then return end
	self.list_view:SetDataList(log_list)
end

-- 关闭前调用
function ZeroBuyLogView:CloseCallBack()
	-- override
end

function ZeroBuyLogView:OnClickClose()
	self:Close()
end

---------------------------------------------------------------------------------------------------

ZeroBuyLogCell = ZeroBuyLogCell or BaseClass(BaseRender)

function ZeroBuyLogCell:__init()
	-- body
end

function ZeroBuyLogCell:__delete()
	-- body
end

function ZeroBuyLogCell:OnFlush()
	if not self.data then return end
	self.node_list.bg:SetActive(self.index % 2 ~= 0)
	self.node_list.name.text.text = ToColorStr(string.format("【%s】",self.data.role_name),COLOR3B.PURPLE)
	self.node_list.desc.text.text = self.data.fetch_state == YIGOUMAI and Language.ZeroBuy.DescText2 or Language.ZeroBuy.DescText1
	self.node_list.icon:SetActive(self.data.fetch_state == YILINGQU)
	if self.data.fetch_state == YIGOUMAI then
		self.node_list.item_name.text.text = string.format(Language.ZeroBuy.SpecialBtnDesc3, COLOR3B.ORANGE, self.data.cfg.name, self.data.cfg.item_name)
	else
		self.node_list.item_name.text.text = ToColorStr(self.data.buy_count.."!", "#72eba9")
	end

	self.node_list.icon.image:LoadSprite(ResPath.GetCommonIcon(monty_type[self.data.money_type]))
	self.node_list.icon.image:SetNativeSize()
end