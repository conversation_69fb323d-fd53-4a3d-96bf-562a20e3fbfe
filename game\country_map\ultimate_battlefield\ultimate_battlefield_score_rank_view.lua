UltimateBattlefieldScoreRankView = UltimateBattlefieldScoreRankView or BaseClass(SafeBaseView)

function UltimateBattlefieldScoreRankView:__init()
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_rank_view")
end

function UltimateBattlefieldScoreRankView:LoadCallBack()
    if not self.fgb_rank_list then
		self.fgb_rank_list = AsyncListView.New(UltimateBattlefieldScoreRankRender, self.node_list.fgb_rank_list)
        self.fgb_rank_list:SetStartZeroIndex(false)
	end

    if not self.my_rank_item then
        self.my_rank_item = UltimateBattlefieldScoreRankRender.New(self.node_list.fgb_my_rank)
    end
end

function UltimateBattlefieldScoreRankView:ReleaseCallBack()
	if self.fgb_rank_list then
		self.fgb_rank_list:DeleteMe()
		self.fgb_rank_list = nil
	end

    if self.my_rank_item then
        self.my_rank_item:DeleteMe()
        self.my_rank_item = nil
    end
end

function UltimateBattlefieldScoreRankView:OnFlush()
    local rank_list, my_rank_data = UltimateBattlefieldWGData.Instance:GetCurrRankInfoList()
    local has_rank_list = not IsEmptyTable(rank_list)
    local has_my_rank_data = not IsEmptyTable(my_rank_data)

    self.node_list.no_rank_list_flag:CustomSetActive(not has_rank_list)
    self.node_list.fgb_rank_list:CustomSetActive(has_rank_list)
    self.node_list.no_my_rank_flag:CustomSetActive(not has_my_rank_data)

    if has_rank_list then
        self.fgb_rank_list:SetDataList(rank_list)
    end

    if has_my_rank_data then
        self.my_rank_item:SetData(my_rank_data)
    end

    self.node_list.fgb_my_rank:CustomSetActive(has_my_rank_data)
end

-----------------------------------UltimateBattlefieldScoreRankRender----------------------------------
UltimateBattlefieldScoreRankRender = UltimateBattlefieldScoreRankRender or BaseClass(BaseRender)

function UltimateBattlefieldScoreRankRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local server_id = self.data.usid.temp_low
    local name_str = string.format("[s%s]%s", server_id, self.data.name)
    self.node_list.role_name.text.text = name_str

    local is_top_3 = self.data.rank <= 3
    self.node_list.rank_num_image:CustomSetActive(is_top_3)

    local color_bundle, color_asset = "", ""
    self.node_list.bg:SetActive(self.data.rank % 2 == 0)
    self.node_list.color_bg:SetActive(is_top_3)
    local rank_text = ""
	if not is_top_3 then
        rank_text = self.data.rank
	else
        color_bundle, color_asset = ResPath.GetCrossFGBPathImg("a2_xt_pm0" .. self.data.rank)
        self.node_list.rank_num_image.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank))
	end

    self.node_list.rank_text:CustomSetActive(not is_top_3)
    self.node_list.rank_text.text.text = rank_text
    self.node_list.color_bg.image:LoadSprite(color_bundle, color_asset)
    self.node_list.role_score.text.text = self.data.score
    local cfg = UltimateBattlefieldWGData.Instance:GetCampCfgBySeq(self.data.camp)
    local color = self.data.camp == 0 and "#377ace" or "#bc3147"
    local str = cfg and cfg.camp_name or ""
    self.node_list.role_team_name.text.text = ToColorStr(str, color)
end