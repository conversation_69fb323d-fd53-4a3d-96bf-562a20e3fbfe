CharacterFollow = CharacterFollow or BaseClass(FollowUi)

function CharacterFollow:__init()
	self.title_list = {}
	self.charactor_title_loader_list = {}

    self.is_show_title = true
    self.is_marry_couple = false
	self.is_show_special_title = false
	self.special_title = 0
	self.has_guild = false
	self.has_lover = false
	self.has_servername = false
	self.banzhuan_color = -1
	self.citan_color = -1
end

function CharacterFollow:__delete()
	self:RemoveTitleList()

	if self.title_handle then
		ReuseableHandleManager.Instance:ReleaseShieldHandle(self.title_handle)
		self.title_handle = nil
	end
end

function CharacterFollow:Create(obj_type)
	FollowUi.Create(self, obj_type or 0)
end

function CharacterFollow:OnRootCreateCompleteCallback(gameobj)
	FollowUi.OnRootCreateCompleteCallback(self, gameobj)
	self.hpbar:CreateRootNode(self.follow_hp_prefab_name, self.obj_type, self.node_list["Follow"].transform)
end

function CharacterFollow:ClearTitle(index, is_delete)
	if index ~= nil then
		if self.charactor_title_loader_list[index] ~= nil then
			self.charactor_title_loader_list[index]:Destroy()
		end
	else
		for k,v in pairs(self.charactor_title_loader_list) do
			if v ~= nil then
				v:Destroy()
			end
		end
	end
end

function CharacterFollow:UpdateSetTitle()
	self.namebar:SetHasTitle(false)
	if nil == self.title_info or self:IsNil() then
		self:ClearTitle()
		return
	end

	local index = self.title_info.index
	local title_id = self.title_info.title_id

	if title_id == nil or title_id == 0 then
		self:ClearTitle()
		return
	end

	local asset_bundle, asset_name = ResPath.GetTitleModel(title_id)
	if not asset_bundle or not asset_name then
		return
	end

	if self.charactor_title_loader_list[index] == nil and self.node_list["Follow"] and not IsNil(self.node_list["Follow"].transform) then
		self.charactor_title_loader_list[index] = AllocAsyncLoader(self, "charactor_title_loader" .. index)
		self.charactor_title_loader_list[index]:SetIsUseObjPool(true)
		self.charactor_title_loader_list[index]:SetIsInQueueLoad(true)
		self.charactor_title_loader_list[index]:SetParent(self.node_list["Follow"].transform)
	end

	if self.charactor_title_loader_list[index] == nil then
		return
	end

	if nil == self.title_handle then
		local shield_type = ShieldObjType.OthersFollowTitle
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.ZhuXie or scene_type == SceneType.KFZhuXieZhanChang then
			shield_type = ShieldObjType.DontShieldFollowTitle
		elseif self.obj_type == SceneObjType.Statue then
			shield_type = ShieldObjType.Statue
		elseif self.is_main_role then
			shield_type = ShieldObjType.MainRoleFollowTitle
		end

		self.title_handle = ReuseableHandleManager.Instance:GetShieldHandle(FollowTitleHandle, self, shield_type)
		self.title_handle:CreateShieldHandle()
	end

	self.charactor_title_loader_list[index]:Load(asset_bundle, asset_name, BindTool.Bind3(self.OnTitleLoadComplete, self, index, title_id))
end

function CharacterFollow:OnTitleLoadComplete(index, title_id, obj)
	if IsNil(obj) or self:IsNil() then
		self:ClearTitle()
		return
	end

	self.namebar:SetHasTitle(self.is_show_title)

	self.title_list[index] = U3DObject(obj)

	if nil ~= self.title_list[index] then
		local the_follow = self.node_list["Follow"].transform
		local tran = self.title_list[index].gameObject.transform
		self.title_list[index].gameObject:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta =
			Vector2(self.title_list[index].gameObject:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta.x,tran:Find("Title").gameObject:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta.y)
		tran:SetParent(the_follow, false)

        local space = self:GetTitleOffset()
        self.off_y =  self.off_y or 0
		self.title_list[index].gameObject.transform:SetLocalPosition(0, space +  self.off_y, 0)

		if self.scale then
			self.title_list[index].gameObject.transform:SetLocalScale(self.scale[1], self.scale[2], self.scale[3])
		end

		-- 有些沙雕玩法 会改变玩家称号id，出场景才恢复
		self:UpdateDiyTitleName()
		local switch = self:IsNeedVisible(index)
		self.title_list[index].gameObject:SetActive(switch)
	end
end

-- 自定义称号
function CharacterFollow:UpdateDiyTitleName()
	if nil == self.vo or nil == self.title_info or self:IsNil() then
		return
	end

	local selected_title = self.vo.used_title_list
	if not selected_title then
		return
	end

	local index = self.title_info.index
	local title_id = self.title_info.title_id
	local cur_title_id = selected_title[1]
	if cur_title_id ~= title_id then
		return
	end

	local diy_title_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
	if not diy_title_cfg then
		return
	end

	local diy_title_name = self.vo.used_diy_title_name
	local u3d_obj = self.title_list[index]
	if u3d_obj == nil or IsNil(u3d_obj.transform) or not diy_title_name or diy_title_name == "" then
		return
	end

	local text_obj = u3d_obj.transform:Find("Text")
	if text_obj == nil or IsNil(text_obj.gameObject) then
		return
	end

	local text_component = text_obj.gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
	if text_component == nil then
		return
	end

	text_component.text = diy_title_name
end

--设置是当前结婚者
function CharacterFollow:SetIsMarryCouple(is_marry)
    self.is_marry_couple = is_marry
end

function CharacterFollow:GetTitleOffset(title_id)
	local space = 80
	if Scene.Instance:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN and
			KuafuYeZhanWangChengWGData.Instance:CheckActIsStart() then
		if self.has_servername then
			space = space + 27
		end
	else
		local guide_name = self:GetGuildName()
		local lover_name = self:GetLoverName()

		if guide_name ~= nil and guide_name ~= "" then
			space = space + 27
		end

		if lover_name ~= nil and lover_name ~= "" then
			space = space + 27
		end

		if self:GetServerInfo() ~= nil and self:GetServerInfo() ~= "" and self:GetServerInfoActive() then
			space = space + 27
		end
		
		if self:GetHasEternalNightEquipData() then
			space = space + 36
		end
	end

	return space
end

function CharacterFollow:SetLocalScale(scale)
	self.scale = scale
	for k,v in pairs(self.title_list) do
		v.gameObject.transform:SetLocalScale(self.scale[1], self.scale[2], self.scale[3])
	end
end

function CharacterFollow:CreateTitleEffect(vo)
	if nil == vo then
		return
	end

	self.vo = vo
	self.has_guild = vo.guild_id > 0
	self.has_lover = vo.lover_name and vo.lover_name ~= ""
	self.has_servername = vo.origin_server_id ~= 0 and vo.plat_name ~= ""

	local selected_title = self.vo.used_title_list
	if not selected_title then
		return
	end
	--[[
	table.sort(selected_title, function(x, y)
			local a = TitleWGData.GetTitleConfig(x)
			local b = TitleWGData.GetTitleConfig(y)
			if a ~= nil and b ~= nil then
				return a.title_show_level < b.title_show_level
			end
		end)

	for i = 1, 4 do
		self:SetTitle(i, selected_title[i] or 0)
	end
	--]]

	if self.is_show_special_title then
		self:SetTitle(0, self.special_title)
	else
		self:SetTitle(1, selected_title[1] or 0)
	end
end

function CharacterFollow:AchieveTitleFilter(selected_title, chengjiu_title_level)
	local chengjiu_title = 10000 + chengjiu_title_level - 1
	if chengjiu_title < 10000 then
		return
	end
	table.insert(selected_title, 1, chengjiu_title)
end

function CharacterFollow:RemoveTitleList()
	if self.charactor_title_loader_list then
		for k,v in pairs(self.charactor_title_loader_list) do
			if v ~= nil then
				v:DeleteMe()
			end
		end
	end

	self.charactor_title_loader_list = {}

	self.title_list = {}
end

function CharacterFollow:SetTitleVisible(is_visible)
	self.is_show_title = is_visible
	self.namebar:SetHasTitle(is_visible)
	for k,v in pairs(self.title_list) do
		if v and not IsNil(v.gameObject) then
			local switch = self:IsNeedVisible(k)
			v.gameObject:SetActive(switch)
		end
	end
end

function CharacterFollow:ChangeSpecailTitle(res_id)
	if not res_id or res_id == 0 then
		self.is_show_special_title = false
		self.special_title = 0
	else
		self.is_show_special_title = true
		self.special_title = res_id
		self:SetTitle(0, res_id)
	end
end

function CharacterFollow:IsNeedVisible(index)
	if index == 0 then
		if self.is_show_special_title then
			return true
		end
	else
		if self.is_show_title and not self.is_show_special_title then
			return true
		end
	end
	return false
end

function CharacterFollow:GetTitleObj()
	return self.title_list
end

function CharacterFollow:UpdateTitleOffY(off_y)
    local space = self:GetTitleOffset()
    self.off_y = off_y or 0
	for k,v in pairs(self.title_list) do
		if not IsNil(v.gameObject) and v.gameObject.transform then
			v.gameObject.transform:SetLocalPosition(0, space + self.off_y, 0)
		end
	end
end

---------------------------------------FollowTitleHandle-----------------------------------

FollowTitleHandle = FollowTitleHandle or BaseClass(ReuseableShieldHandle)
function FollowTitleHandle:__init(follow_ui, shield_obj_type)
	self:Init(follow_ui, shield_obj_type)
end

function FollowTitleHandle:__delete()
	self:Clear()
end

function FollowTitleHandle:Init(follow_ui, shield_obj_type)
	self.follow_ui = follow_ui
	self.shield_obj_type = shield_obj_type
end

function FollowTitleHandle:Clear()
	self.follow_ui = nil
end

function FollowTitleHandle:VisibleChanged(visible)
	if self.follow_ui then
		self.follow_ui:SetTitleVisible(visible)
	end
end
