FuBenNewPlayerView = FuBenNewPlayerView or BaseClass(SafeBaseView)

function FuBenNewPlayerView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_newplayer_fuben_info")
	self.view_cache_time = 1
	self.open_tween = nil
	self.close_tween = nil
	self.tips_str = nil
	self.tips_node = nil
end

function FuBenNewPlayerView:__delete()

end

function FuBenNewPlayerView:ReleaseCallBack()
	if self.info then
		self.info:DeleteMe()
		self.info = nil
	end
	self.is_init = false
	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
	end

	self.tips_str = nil
	self.tips_node = nil
	self.time_down_callback = nil
	self.fb_cfg = nil
	self.fb_specal_operate = nil
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.FBNewPlayerView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
	self:CleanTimeDown()
end

function FuBenNewPlayerView:LoadCallBack()
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.FBNewPlayerView, self.get_guide_ui_event)
end

function FuBenNewPlayerView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	self.info = NewPlayerFubenList.New(self.node_list["root_fuben_info"])
	self.info:Flush()
	self:SetParent()

	self.is_init = true
	-- self:Flush()
	XUI.AddClickEventListener(self.node_list.bianshen_show_skill,BindTool.Bind(self.OnClickTianShenBianshen, self))
end

function FuBenNewPlayerView:CanActiveClose()
	return false
end

function FuBenNewPlayerView:SetParent()
	if self.info then
		local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
		self.info:SetInstanceParent(parent)
		self.info:AnchorPosition(0, 0)
		MainuiWGCtrl.Instance:SetTaskPanel(false,152,-179)
	end
end

function FuBenNewPlayerView:OpenCallBack()
	self.fb_specal_operate = nil
	
	if self:IsLoaded() then
		self:SetParent()
	end
end

function FuBenNewPlayerView:CloseCallBack()
    if self.info then
		self.info:SetInstanceParent(self.node_list["layout_fuben"])
	end
end

function FuBenNewPlayerView:ShowIndexCallBack(index)
	-- self:Flush()
end

function FuBenNewPlayerView:OnFlush(param_list, index)
	if not self.is_init then
		return
	end
	self.info:Flush()
	self:FlushSpecialOperate()
end

-- 新手副本特殊操作
function FuBenNewPlayerView:FlushSpecialOperate()
	if self.fb_specal_operate then
		return
	end

	local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
	if not fuben_data or not next(fuben_data) then return end
	self.fb_cfg = FuBenWGData.Instance:GetNewPlayerFbCfg(fuben_data.fb_type)

	if self.fb_cfg == nil or self.fb_cfg.temp_tianshen_index == -1 then
		return
	end

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if (not main_role_vo) or (main_role_vo.special_appearance ~= 0) then
		return
	end

	self.node_list.bianshen_show_root:CustomSetActive(self.fb_cfg.temp_tianshen_index ~= -1)

	if self.fb_cfg.temp_tianshen_index ~= -1 then
		self:FlushSpecialTianshenOperate(self.fb_cfg.temp_tianshen_index)
	end

	self.fb_specal_operate = true
end

-- 副本天神特殊操作
function FuBenNewPlayerView:FlushSpecialTianshenOperate(image_index)
	local bianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(image_index)
	if not bianshen_cfg then
		return
	end

	self.tips_str = Language.FuBenPanel.NewPlayerTianshen
	self.tips_node = self.node_list.bianshen_show_time_down
	self.time_down_callback = BindTool.Bind(self.OnClickTianShenBianshen, self)
	self:FlushTimeCountDown(8)
end

-----------------活动时间倒计时-------------------
function FuBenNewPlayerView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("new_player_choose_time_down") then
		CountDownManager.Instance:RemoveCountDown("new_player_choose_time_down")
	end
end

function FuBenNewPlayerView:FlushTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > 0 then
		self:CleanTimeDown()
		self:UpdateNodeRoot(invalid_time)
		CountDownManager.Instance:AddCountDown("new_player_choose_time_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), nil, invalid_time, 1)
	end
end

function FuBenNewPlayerView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self:UpdateNodeRoot(valid_time)
	end
end

function FuBenNewPlayerView:UpdateNodeRoot(num)
	local temp_num = math.ceil(num)
	if self.tips_node then
		self.tips_node.text.text = string.format(self.tips_str, temp_num)
	end
end

function FuBenNewPlayerView:OnComplete()
	if self.time_down_callback then
		self.time_down_callback()
		self.time_down_callback = nil
	end
end

--请求模拟天神变身
function FuBenNewPlayerView:OnClickTianShenBianshen()
	--直接让玩家变身
	if self.fb_cfg == nil or self.fb_cfg.temp_tianshen_index == -1 then
		return
	end

	self.node_list.bianshen_show_root:CustomSetActive(false)

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if (not main_role_vo) or (main_role_vo.special_appearance ~= 0) then
		return
	end

	FunctionGuide.Instance:StartNextStep()
	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type15, self.fb_cfg.temp_tianshen_index)
end

-- 引导模拟天神变身
function FuBenNewPlayerView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.ZhuXianBianShen then
		return self.node_list.bianshen_show_skill, BindTool.Bind(self.OnClickTianShenBianshen, self)
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

---新手副本展示列表信息-------------------------------------------------------
NewPlayerFubenList = NewPlayerFubenList or BaseClass(BaseRender)
function NewPlayerFubenList:__init()
	self.task_cfg = nil
	self.max_count = 0
	self.cut_count = 0
	self.is_init = false
	self.reward_list = {}
	if not self.global_event then
		self.global_event = GlobalEventSystem:Bind(MainUIEventType.SETMAINTASK, BindTool.Bind(self.UpdateTaskProgress, self))
	end
	XUI.AddClickEventListener(self.node_list["rich_fuben_progress"], BindTool.Bind(self.DoTask, self))
end

function NewPlayerFubenList:__delete()
	if self.global_event then
		GlobalEventSystem:UnBind(self.global_event)
		self.global_event = nil
	end

	for k,v in pairs(self.reward_list) do
		v:DeleteMe()
	end
	self.reward_list = {}

	self.is_pass = false
	self.fb_cfg = nil
	self.task_cfg = nil
	self.is_init = false
end

function NewPlayerFubenList:AnchorPosition( x,y )
	self.view.rect.anchoredPosition = Vector2(x,y)
end

function NewPlayerFubenList:UpdateTaskProgress()
	local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
	if not fuben_data or not next(fuben_data) then return end
	self.cut_count = fuben_data.kill_monster_count
	self:UpdateRichProgress()
end

function NewPlayerFubenList:OnFlush()
	local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
	if not fuben_data or not next(fuben_data) then return end
	self.fb_cfg = FuBenWGData.Instance:GetNewPlayerFbCfg(fuben_data.fb_type)
	if fuben_data.is_pass == 1 then
		self.is_pass = true
	end
	self.cut_count = fuben_data.kill_monster_count
	self.max_count = FuBenWGData.Instance:GetNewPlayerFbMonsteCount(fuben_data.fb_type)

	if self.fb_cfg then
		self.node_list["rich_fuben_explain"].text.text = self.fb_cfg.fuben_desc or "" --副本说明
	end

	self.task_cfg = self:GetNewPlayerAccpetedTaskCfg(self.fb_cfg.fb_type)

	if self.fb_cfg ~= nil and not self.is_init then
		self:UpdateRichProgress()
		self:FlushRewardList()

		self.is_init = true
	end
end

function NewPlayerFubenList:UpdateRichProgress()
	if self.node_list and self.node_list["rich_fuben_progress"] and self.fb_cfg then
		local text = self:GetDesc(self.fb_cfg.fb_type)
		local color 
		if self.cut_count >= self.max_count or self.is_pass then
			color = COLOR3B.D_GREEN
		else
			color = COLOR3B.D_RED
		end

		local text1 = ToColorStr(self.cut_count .. "/" .. self.max_count, color)
		text = XmlUtil.RelaceTagContent(text, "per", text1)
		self.node_list["rich_fuben_progress"].text.text = text
	end
end

function NewPlayerFubenList:FlushRewardList()
	local reward_cfg = self.fb_cfg.reward_list
	self.node_list["reward_list"].scroll_rect.enabled = false

	if reward_cfg and reward_cfg ~= "" and not IsEmptyTable(reward_cfg) then
		local reward_cfg_list = {}

		for i = 0, #reward_cfg do
			table.insert(reward_cfg_list, reward_cfg[i])
		end
		if self.task_cfg and self.task_cfg.task_type == GameEnum.TASK_TYPE_RI then
			local num = DailyWGData.Instance:GetShangJinExpReward()
			if num > 0 then
				table.insert(reward_cfg_list, {item_id = SpecialItemId.Exp, num = num})
			end
		end

		for k,v in ipairs(reward_cfg_list) do
			if self.reward_list[k] == nil then
				self.reward_list[k] = ItemCell.New(self.node_list["reward_list"])
			end
			self.reward_list[k]:SetData(v)
		end

		self.node_list["reward_bg"]:SetActive(true)
		self.node_list["desc_panel"]:SetActive(false)
	else
		self.node_list["reward_bg"]:SetActive(false)
		self.node_list["desc_panel"]:SetActive(true)
	end
end

function NewPlayerFubenList:DoTask()
	local task_desc = MainuiWGData.Instance:GetTaskDesc()
	if nil ~= task_desc then
		MainuiWGCtrl.Instance:DoTask(task_desc.task_id, task_desc.task_status)
	end
end

function NewPlayerFubenList:GetNewPlayerAccpetedTaskCfg(fb_type)
	local accpeted_task_list = TaskWGData.Instance:GetTaskAcceptedInfoList()
	for task_id, task_info in pairs(accpeted_task_list) do
		local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
		-- 检查是否是通关新手副本任务
		if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 and task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_42 then
			if task_cfg.c_param3 == fb_type then
				return task_cfg
			end
		end
	end
	return nil
end

function NewPlayerFubenList:GetDesc(fb_type)
	if not fb_type then
		return ""
	end
	if self.task_cfg == nil then
		self.task_cfg = self:GetNewPlayerAccpetedTaskCfg(fb_type)
	end
	return self.task_cfg and self.task_cfg.progress_desc or ""
end