require("game/homes/homes_view")

HomesWGCtrl = HomesWGCtrl or BaseClass(BaseWGCtrl)
function HomesWGCtrl:__init()
    if HomesWGCtrl.Instance then
        ErrorLog("[HomesWGCtrl]:Attempt to create singleton twice!")
    end
    HomesWGCtrl.Instance = self
    self.view = HomesView.New(GuideModuleName.HomesView)
end

function HomesWGCtrl:__delete()
    HomesWGCtrl.Instance = nil
    self.view:DeleteMe()
    self.view = nil
end

function HomesWGCtrl:FlushHomesView()
    TryDelayCall(self, function ()
         ViewManager.Instance:FlushView(GuideModuleName.HomesView)
    end, 0.5, "flush_view_delay")
end