AlchemySpeedUpView = AlchemySpeedUpView or BaseClass(SafeBaseView)
function AlchemySpeedUpView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 32), sizeDelta = Vector2(760, 380)})
	self:AddViewResource(0, "uis/view/country_map_ui/alchemy_prefab", "layout_alchemy_speed_up")
end

function AlchemySpeedUpView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind(self.Close, self))

    self.node_list["title_view_name"].text.text = Language.CountryAlchemy.TItleName4
    self.node_list["rule_desc"].text.text = Language.CountryAlchemy.SpeedUpDesc

    if self.speed_up_list == nil then
        self.speed_up_list = AsyncListView.New(AlchemySpeedUpCell, self.node_list["speed_up_list"])
    end

end

function AlchemySpeedUpView:ReleaseCallBack()
    if self.speed_up_list then
        self.speed_up_list:DeleteMe()
        self.speed_up_list = nil
    end
end

function AlchemySpeedUpView:OnFlush(param_t)
    for k, v in pairs(param_t) do
		if k == "all" then
			if v.speed_seq then
				AlchemyWGData.Instance:SetCurDanzaoSeq(v.speed_seq)
			end
		end
	end

    local expedite_list = AlchemyWGData.Instance:GetExpediteCfg()
    if expedite_list ~= nil then
        self.speed_up_list:SetDataList(expedite_list)
    end
end


AlchemySpeedUpCell = AlchemySpeedUpCell or BaseClass(BaseRender)
function AlchemySpeedUpCell:__init()
    self.speed_up_item = ItemCell.New(self.node_list["speed_item_pos"])
    XUI.AddClickEventListener(self.node_list["btn_speed_up"], BindTool.Bind(self.OnClickExpedite, self))
end

function AlchemySpeedUpCell:__delete()
    if self.speed_up_item then
        self.speed_up_item:DeleteMe()
	    self.speed_up_item = nil
    end
end

function AlchemySpeedUpCell:OnFlush()
    if not self.data then
		return 
	end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg ~= nil then
        self.node_list["speed_item_name"].text.text = item_cfg.name
        self.node_list["btn_time_text"].text.text = string.format(Language.CountryAlchemy.ExpediteTime, TimeUtil.FormatSecond(self.data.reduce_time))
    end
    
    if self.data.item_id == 26050 then
        self.node_list["btn_time_text"].text.text = Language.CountryAlchemy.ExpediteAccomplish
    end

    local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
        local color = has_num > 0 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
        local str = has_num .. "/" .. 1
        self.speed_up_item:SetFlushCallBack(function ()
            self.speed_up_item:SetRightBottomColorText(str, color)
            self.speed_up_item:SetRightBottomTextVisible(true)
        end)
    self.speed_up_item:SetData({item_id = self.data.item_id})
end

function AlchemySpeedUpCell:OnClickExpedite()
    local cur_danzao_seq = AlchemyWGData.Instance:GetCurDanzaoSeq()
    if not self.data  and (not cur_danzao_seq) then
		return
	end

    local grow_time = 0
    local danzao_data = AlchemyWGData.Instance:GetDanzaoTimeInfo(cur_danzao_seq)
    if danzao_data then
        grow_time = AlchemyWGData.Instance:GetGrowTimeComposeBySeq(danzao_data.compos_seq, danzao_data.start_compos_time)
    end

    local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
    if grow_time > 0 then
        if has_num > 0 then
            AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.COMPOS_EXPEDITE, cur_danzao_seq, self.data.item_id)
            AlchemyWGCtrl.Instance:AlchemyExpeditePlayEffect(cur_danzao_seq)
        else
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.CountryAlchemy.ExpediteError)
    end

end
