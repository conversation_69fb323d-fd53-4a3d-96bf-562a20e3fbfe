KuafuOnevoneWGData = KuafuOnevoneWGData or BaseClass()
function KuafuOnevoneWGData:__init()
	if KuafuOnevoneWGData.Instance then
		ErrorLog("[KuafuOnevoneWGData] attempt to create singleton twice!")
		return
	end
	KuafuOnevoneWGData.Instance =self
	self.kf1v1_info = {}
	self.kf_1v1_show_rank = {}
	self.macth_info = {
		result = 0,
		match_end_left_time = 0,
	}
	self.oppo_info = {}
	self.scene_user_list = {}
	self.kf1v1_news ={}
	self.join_time_reward_flag = {}
	self.jifen_reward_flag = {}
	self.curr_activity_add_score = {}
	self.enemy = {}
	self.come_from_kf_scene = false
	self.scene_status = -1
	self.scene_next_time = -1
	self.fight_start_timestmap = -1								-- 当前状态倒计时

	local config = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto")
end

function KuafuOnevoneWGData:__delete()
	KuafuOnevoneWGData.Instance = nil
end

-- 设置跨服1V1基本信息
function KuafuOnevoneWGData:Set1V1Info(info)
	self.kf1v1_info.cross_score_1v1 = info.cross_score_1v1
	self.kf1v1_info.cross_day_join_1v1_count = info.cross_day_join_1v1_count
	self.kf1v1_info.cross_1v1_curr_activity_add_score = info.cross_1v1_curr_activity_add_score
	self.kf1v1_info.today_buy_times = info.today_buy_times
	self.kf1v1_info.cross_lvl_total_join_times = info.cross_lvl_total_join_times
	self.kf1v1_info.cross_1v1_total_win_times = info.cross_1v1_total_win_times
	self.kf1v1_info.cross_1v1_dur_win_times = info.cross_1v1_dur_win_times
	self.kf1v1_info.cross_1v1_gongxun = info.cross_1v1_gongxun
	self.kf1v1_info.cur_season  = info.cur_season
	self.join_time_reward_flag = bit:d2b(info.cross_1v1_join_time_reward_flag)
	self.cross_1v1_fetch_score_reward_flag = bit:d2b(info.cross_1v1_fetch_score_reward_flag)
	self.kf1v1_info.cross_1v1_have_ring = info.cross_1v1_have_ring
	self.kf1v1_info.today_reward_count = info.today_reward_count
	self.cross_1v1_use_ring = info.cross_1v1_use_ring
end

-- 获取跨服1V1基本信息
function KuafuOnevoneWGData:Get1V1Info()
	return self.kf1v1_info
end
function KuafuOnevoneWGData:GetRoleHasUseRing()
	return  self.cross_1v1_use_ring or {},self.kf1v1_info.cross_1v1_have_ring or {}
end
function KuafuOnevoneWGData:HaveBetterRingSelect()
	local have_ring_cfg_data = {}
	local use_ring_cfg_data = {}

	local temp_ring_base_cfg 
	if nil == self.kf1v1_info.cross_1v1_have_ring or IsEmptyTable(self.cross_1v1_use_ring) then 
		return 0
	end
	for k,v in pairs(self.kf1v1_info.cross_1v1_have_ring) do
		if v >= 38 then
			temp_ring_base_cfg = TableCopy(self:GetWZringAttributeCfg(self.kf1v1_info.cur_season,v - 38))
			temp_ring_base_cfg.select_index = k
			table.insert(have_ring_cfg_data,temp_ring_base_cfg)
		end
	end
	if #have_ring_cfg_data < 1 then return 0 end
	--print_error(have_ring_cfg_data)
	table.sort(have_ring_cfg_data,SortTools.KeyUpperSorter("zhan_li"))
	local cap_1,cap_2 = self:GetUseRingCfg()
	for k,v in pairs(have_ring_cfg_data) do
		if (v.zhan_li > cap_1 and v.select_index ~= self.cross_1v1_use_ring[1] and v.select_index ~= self.cross_1v1_use_ring[2]) 
			or (v.zhan_li > cap_2 and v.select_index ~= self.cross_1v1_use_ring[2]and v.select_index ~= self.cross_1v1_use_ring[1]) then
--			print_error(have_ring_cfg_data,self.kf1v1_info.cross_1v1_have_ring)
			return 1
		end

	end
	return 0
end
function KuafuOnevoneWGData:GetRingSelectIndex()
	local have_ring_cfg_data = {}
	local use_ring_cfg_data = {}
	local temp_ring_base_cfg 
	--print_error(self.kf1v1_info.cross_1v1_have_ring)
	for k,v in pairs(self.kf1v1_info.cross_1v1_have_ring) do
		if v >= 38 then
			temp_ring_base_cfg = TableCopy(self:GetWZringAttributeCfg(self.kf1v1_info.cur_season,v - 38))
			temp_ring_base_cfg.select_index = k
			table.insert(have_ring_cfg_data,temp_ring_base_cfg)
		end
	end
	if #have_ring_cfg_data < 1 then return 0 end
	table.sort(have_ring_cfg_data,SortTools.KeyUpperSorter("zhan_li"))
	local cap_1,cap_2 = self:GetUseRingCfg()
	for k,v in pairs(have_ring_cfg_data) do
		--print_error(v.zhan_li , cap_1 ,cap_2, v.select_index , self.cross_1v1_use_ring[1] ,self.cross_1v1_use_ring[2])
		if (v.zhan_li > cap_1 and v.select_index ~= self.cross_1v1_use_ring[1] and v.select_index ~= self.cross_1v1_use_ring[2]) 
			or (v.zhan_li > cap_2 and v.select_index ~= self.cross_1v1_use_ring[2]and v.select_index ~= self.cross_1v1_use_ring[1]) then
			return v.select_index
		end

	end
	-- if #have_ring_cfg_data > 0 and have_ring_cfg_data[1].zhan_li > cap_1 and have_ring_cfg_data[1].zhan_li > cap_2 then
	-- 	print_error( have_ring_cfg_data[1].zhan_li,cap_1,cap_2)
	-- 	return have_ring_cfg_data[1].select_index
	-- end
	return 0
end
function KuafuOnevoneWGData:GetUseRingCfg()
	
	local use_1_season, use_2_season = 0, 0
	for k,v in pairs(self.kf1v1_info.cross_1v1_have_ring) do
		if k == self.cross_1v1_use_ring[1] then
			use_1_season = v
		elseif k == self.cross_1v1_use_ring[2] then
			use_2_season = v
		end
	end
	local cap_1 = self:GetWZringAttributeCfg(self.cross_1v1_use_ring[1],use_1_season)
	local cap_2 = self:GetWZringAttributeCfg(self.cross_1v1_use_ring[2],use_2_season)
--	print_error(self.cross_1v1_use_ring,self.kf1v1_info.cross_1v1_have_ring)
	return IsEmptyTable(cap_1) and 0 or cap_1.zhan_li,IsEmptyTable(cap_2) and 0 or cap_2.zhan_li,cap_1,cap_2
end
function KuafuOnevoneWGData:GetWZringSelectCfg(index)
	if self.cross_1v1_use_ring  == nil then 
		return  
	end
	for k,v in ipairs(self.cross_1v1_use_ring) do
		if v == index  then
			return true
		end
	end
	return false

end

function KuafuOnevoneWGData:GetWinRate()
	local kf_info = self:Get1V1Info()
	if nil == kf_info.cross_1v1_total_win_times then
		return 
	end
	if kf_info.cross_1v1_total_win_times > 0 then
		local num = math.ceil((kf_info.cross_1v1_total_win_times / kf_info.cross_lvl_total_join_times)*100)
		if num > 100 then
			num = 100
		elseif num < 0 then
			num = 0
		end
	 	return  num
	else
		return 0
	end
end
-- 获取跨服1V1总积分
function KuafuOnevoneWGData:GetInfoGongXun()
		return self.kf1v1_info.cross_1v1_gongxun
end
function KuafuOnevoneWGData:Get1V1InfoJiFen()
	return self.kf1v1_info.cross_score_1v1
end

function KuafuOnevoneWGData:GetJionTimesRewardIsGet(index)
	return self.join_time_reward_flag[33- index]
end

function KuafuOnevoneWGData:GetGongXunRewardIsGet(index)

	return 1 --self.cross_1v1_fetch_score_reward_flag[32 - index]
end

-- 设置跨服1V1展示排行
function KuafuOnevoneWGData:Set1V1ShowRank(info)
	self.kf_1v1_show_rank = {}
	for i=1,5 do
		if info[i] then
			local vo = GameVoManager.Instance:CreateVo(RoleVo)
			vo.plat_type = info[i].plat_type
			vo.role_id = info[i].role_id
			local plat_type = GameVoManager.Instance:GetUserVo()
			local plat = ""
			if RoleWGData.Instance.role_vo.plat_type ~= vo.plat_type then
				plat = Language.Common.WaiYu .. "_"
			end
			vo.name = plat .. info[i].name .. "_s" .. info[i].oppo_server_id
			vo.level = info[i].level
			vo.prof = info[i].prof
			vo.sex = info[i].sex
			vo.score = info[i].score
			vo.max_dur_win_count = info[i].max_dur_win_count
			vo.win_percent = info[i].win_percent
			vo.capability = info[i].capability
			vo.appearance = info[i].appearance
			self.kf_1v1_show_rank[i] = vo
		end
	end
end

-- 获取跨服1V1展示排行
function KuafuOnevoneWGData:Get1V1ShowRank()
	return self.kf_1v1_show_rank
end

-- 设置跨服1V1匹配信息
function KuafuOnevoneWGData:Set1V1MacthInfo(info)
	self.macth_info.result = info.result
	self.macth_info.match_end_left_time = TimeWGCtrl.Instance:GetServerTime() + info.match_end_left_time
end

-- 获取跨服1V1匹配信息
function KuafuOnevoneWGData:Get1V1MacthInfo()
	return self.macth_info
end

-- 设置跨服1V1对手信息
function KuafuOnevoneWGData:SetOppoInfo(info)
	self.oppo_info = info
end

-- 获取跨服1V1对手信息
function KuafuOnevoneWGData:GetOppoInfo()
	return self.oppo_info
end

-- 获取跨服1V1对手信息
function KuafuOnevoneWGData:GetSceneUser()
	self.scene_user_list = {}
	self.scene_user_list[1] = RoleWGData.Instance.role_vo
	self.scene_user_list[2] = self.oppo_info
	return self.scene_user_list
end

function KuafuOnevoneWGData:ClearKf1V1News()
	self.kf1v1_news = {}
end

function KuafuOnevoneWGData:AddKf1V1News(name, result, score)
	local vo = {}
	vo.name = name
	vo.result = result
	vo.jifen = score or KuafuOnevoneWGData.ShowCfg().match_fail_score
	table.insert(self.kf1v1_news, vo)
	if #self.kf1v1_news > 5 then
		table.remove(self.kf1v1_news, 1)
	end
end

function KuafuOnevoneWGData:GetKf1V1News()
	return self.kf1v1_news
end

function KuafuOnevoneWGData.ShowCfg()
	return ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").show_cfg[1]
end

function KuafuOnevoneWGData.GetMaxJionTimes()
	local cfg =  ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").other
	return cfg[1].free_times
end

function KuafuOnevoneWGData:SetComeFromScene(bool_value)
	self.come_from_kf_scene = bool_value
end

function KuafuOnevoneWGData:GetComeFromScene()
	return self.come_from_kf_scene
end
--获取参与次数奖励
function KuafuOnevoneWGData:GetJoinTimesReward(join_times)
	if join_times == nil then return end
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").join_times_reward
	local num = #cfg
	for i = 1, #cfg do
		if join_times >= cfg[i].jion_times and self:GetJionTimesRewardIsGet(cfg[i].seq) == 0 then
			num = i
			break
		end
		if join_times < cfg[i].jion_times then
			num = i
			break
		end
	end
	return cfg[num]
end
--获取积分达标奖励
function KuafuOnevoneWGData:GetJiFenReward()
	local cfg =  ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").score_reward
	local reward_tab = {}
	for k, v in ipairs(cfg) do
		reward_tab[k] = {}
		reward_tab[k].seq = v.seq
		reward_tab[k].need_score = v.need_score
		reward_tab[k].reward_item = v.reward_item
		reward_tab[k].curr_score = self.kf1v1_info.cross_1v1_gongxun
	end
	return reward_tab
end

function KuafuOnevoneWGData:RemindKFOnevOne()
	local cfg = KuafuOnevoneWGData.Instance:GetJiFenReward()
	for k,v in ipairs(cfg) do
		local remind = self:GetGongXunRewardIsGet(v.seq)
		if v.curr_score> v.need_score and remind == 0  then
			return  1
		end
	end
	return  0
end

--获取排行榜排名奖励
function KuafuOnevoneWGData.GetRankingReward()
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").rank_reward
	local other_cfg =  ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").other[1]
	local ranking_tab = {}
	local num = 0
	for i, v in ipairs(cfg) do
		ranking_tab[i] = {}

		-- 配置  min_rank_pos 从0开始
		ranking_tab[i].min_rank_pos = v.min_rank_pos + 1
		ranking_tab[i].max_rank_pos = v.max_rank_pos + 1
		ranking_tab[i].reward_item = v.reward_item
		num = i
	end
	ranking_tab[num + 1] = {}
	ranking_tab[num + 1].is_canyu = true
	ranking_tab[num + 1].reward_item = other_cfg.join_item
	return ranking_tab
end

function KuafuOnevoneWGData:GetdwRankReward(season)
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").gxshow_cfg
	local data = {}
	local should_select_season = season
	if season > 12 then
		should_select_season = 12 - season % 2
	end
	for k,v in ipairs(cfg) do
		if v.season == should_select_season then
			data[v.grade] = {}
			data[v.grade].seq = v.seq
			data[v.grade].grade = v.grade
			data[v.grade].score = v.score
			data[v.grade].name = v.name
			data[v.grade].reward_item = v.reward_item
		end
	end
	return data
end


function KuafuOnevoneWGData:GetMyRewardRank()
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").rank_reward
	local rank_list = RankWGData.Instance:GetRankData(RankKind.Cross, CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_1V1_SCORE)
	local my_jifen = self:Get1V1InfoJiFen()
	local nb_rank_num = 1
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()

	local function getRankDataByNum(num)
		for _, rank_data in ipairs(cfg) do
			-- 配置  min_rank_pos 从0开始
			if num <= rank_data.max_rank_pos + 1 then
				return rank_data
			end
		end
	end

	local rank_data = nil
	for k,v in ipairs(rank_list)do
		for up = 1, 100 do
			rank_data = getRankDataByNum(nb_rank_num)
			if rank_data ~= nil then
				if v.rank_value >= rank_data.score_limit then
					nb_rank_num = nb_rank_num + 1
					break
				else
					nb_rank_num = rank_data.max_rank_pos + 1 + 1
				end
			end
			up = up + 1
		end
		if v.user_id == main_role_vo.role_id then
			return nb_rank_num - 1
		end
	end
	return ""
end
--获取购买花费
function KuafuOnevoneWGData:GetKFOneVOneOtherCfg()
	local cfg =  ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").other[1]
	return cfg
end

--获取购买花费
function KuafuOnevoneWGData.GetBuyTimeCost()
	local cfg =  ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").other[1]
	return cfg.buy_time_cost
end

function KuafuOnevoneWGData:GetGongXunReward(index)
	local cfg  = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").other[1]
	return index > 0 and cfg.winner_score or cfg.loser_score
end
--获取比赛结果获得物品
function KuafuOnevoneWGData.GetFinishGetItem(result)
	local cfg =  ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").other[1]
	local tab = {}
	tab.get_exp = result == 0 and cfg.loser_exp_per or cfg.winner_exp_per
	tab.get_item = result == 0 and cfg.loser_item or cfg.winner_item
	tab.get_jifen = result == 0 and cfg.loser_score or cfg.winner_score
	return tab
end
--获取比赛结经验
function KuafuOnevoneWGData.GetFinishGetExp()
	local role_level = RoleWGData.Instance.role_vo.level
	local cfg =  ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").reward_exp
	return cfg[role_level].reward_exp
end

function KuafuOnevoneWGData:GetGridNum()
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").grade_score
	return #cfg,cfg
end

function KuafuOnevoneWGData:GetRewardBaseCell(jifen)
	local item_cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").grade_score
	if jifen and jifen > 0 then
		for k = #item_cfg,1, -1 do
			if item_cfg[k].score <= jifen then
				return item_cfg[k]
			end
		end
	end
	return item_cfg[1]
end

function KuafuOnevoneWGData:SetProgLevel(grade)
	local item_cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").grade_score
	if grade < #item_cfg - 1 then
		for k = #item_cfg,1, -1 do
			if item_cfg[k].grade == grade + 1 then
				return item_cfg[k]
			end
		end
	end
	return item_cfg[#item_cfg]

end
function KuafuOnevoneWGData:GetRewardCfg()
	local reward_cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").join_times_reward
	local role  = RoleWGData.Instance.role_vo
	return reward_cfg
end
function KuafuOnevoneWGData:GetRanKjifencfg()
	local cfg  =  ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").other[1]
	local list_cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").grade_score
	for  k,v in ipairs(list_cfg)do
		if v.grade == cfg.rank_reward_min_grade then
			return v.score
		end
	end
	return  0
end

function KuafuOnevoneWGData:GetBigRankScore()
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").grade_score
	local big_rank = {}
	for i=1,#cfg,5 do
		local list = {}
		list.low_score = cfg[i].score
		list.up_score = cfg[i + 5] and cfg[i + 5].score - 1
		list.name = string.gsub(cfg[i].name,"V","")
		list.pk_type = "onevone"
		table.insert(big_rank, 1, list)
	end
	return big_rank
end

function KuafuOnevoneWGData:GetRankList(tab)
	local data_list  = {}
	local score = self:GetRanKjifencfg()
	for k,v in ipairs(tab)do
		if v.rank_value >= score then
			table.insert(data_list, v)
		end
	end
	return data_list
end

function KuafuOnevoneWGData:GetKFRingCfg()
	local ring_cfg =  ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").xndex
	return ring_cfg
end

function KuafuOnevoneWGData:GetWZRingCfg()
	self.cfg_list = self:GetKFRingCfg()
	local data_lits  = self:Get1V1Info()
	self.data_cfg  = {}
	if data_lits.cur_season <= 1 then
		table.insert(self.data_cfg,{})
	end
--print_error(data_lits.cur_season)
	local cur_season = data_lits.cur_season
	if cur_season <= 12 then
		for k,v in ipairs(self.cfg_list) do
			if v.seq <= data_lits.cur_season + 1 then
				local data = {}
				local data_flag  = data_lits.cross_1v1_have_ring[k]
				data.index = v.seq
				data.flag = data_flag - 38 --段位一共有38级  0-37表示获得戒子没有领取或者邮件附件未领取  但是要展示出来   38-75 表示已激活 
				data.show_get_ring_index = -1
				if data_flag < 38 and data_flag >= 0 then
					data.show_get_ring_index = data_flag
				end
				data.cur_season = data_lits.cur_season
				data.pic  = v.img_pic
				data.particle_index = k
				data.type = "cross_1v1_auto"
				table.insert(self.data_cfg, data)
			end
		end
	else
		for i = 1,cur_season do
			if i <= 12 then
				--if self.cfg_list[i].seq <= i + 1 then
					local data = {}
					local data_flag  = data_lits.cross_1v1_have_ring[i]
					data.index = self.cfg_list[i].seq
					--data.flag = data_flag
					data.flag = data_flag - 38 --段位一共有38级  0-37表示获得戒子没有领取或者邮件附件未领取  但是要展示出来   38-75 表示已激活 
					data.show_get_ring_index = -1
					if data_flag < 38 and data_flag >= 0 then
						data.show_get_ring_index = data_flag
					end
					data.cur_season = data_lits.cur_season
					data.pic  = self.cfg_list[i].img_pic
					data.particle_index = i
					data.type = "cross_1v1_auto"
					table.insert(self.data_cfg, data)
				--end
			else
				local should_select_season = 12 - i % 2
				--if self.cfg_list[i].seq <= i + 1 then
					local data = {}
					local data_flag  = data_lits.cross_1v1_have_ring[i]
					data.index = i
					--data.flag = data_flag
					data.flag = data_flag - 38 --段位一共有38级  0-37表示获得戒子没有领取或者邮件附件未领取  但是要展示出来   38-75 表示已激活 
					data.show_get_ring_index = -1
					if data_flag < 38 and data_flag >= 0 then
						data.show_get_ring_index = data_flag
					end
					data.cur_season = data_lits.cur_season
					data.pic  = self.cfg_list[should_select_season].img_pic
					data.particle_index = should_select_season
					data.type = "cross_1v1_auto"
					table.insert(self.data_cfg, data)
				--end

			end

		end
	end
	-- for k,v in ipairs(self.cfg_list) do
	-- 	if v.seq <= data_lits.cur_season + 1 then
	-- 		local data = {}
	-- 		data_flag  = data_lits.cross_1v1_have_ring[k]
	-- 		data.index = v.seq
	-- 		data.flag = data_flag
	-- 		data.cur_season = data_lits.cur_season
	-- 		data.pic  = v.img_pic
	-- 		data.type = "cross_1v1_auto"
	-- 		table.insert(self.data_cfg, data)
	-- 	end
	-- end
	return self.data_cfg
end

function KuafuOnevoneWGData:GetWZringAttributeCfg(index,grade,is_max)

	local ring_att = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto").season_ring
	if is_max then
		local curseason_max_cfg = {}
		for k,v in ipairs(ring_att) do
			if v.season == index  then
				curseason_max_cfg = v
			end
		end
		return curseason_max_cfg
	else
		for k,v in ipairs(ring_att) do
			if v.season == index and v.grade == grade then
				return v
			end
		end
	end
	
	return  {}
end

function KuafuOnevoneWGData:SetMatchingEnemySex(info)
	self.enemy.name = info.oppo_name
	self.enemy.level = info.level
	self.enemy.sever = info.oppo_sever_id
	self.enemy.sex = info.sex
	-- self.enemy.head_icon = info.head_icon
	self.enemy.prof = info.prof
	self.enemy.photo_frame = info.photo_frame
	self.enemy.role_id = info.role_id
	self.enemy.capability = info.capability
	self.enemy.oppo_score = info.oppo_score
end

function KuafuOnevoneWGData:GetMatchingEnemySex()
	return self.enemy
end

function KuafuOnevoneWGData:ClearEnemyInfo()
	self.enemy = {}
end

function KuafuOnevoneWGData:GetCurOneVOneSeason()
	if not self.kf1v1_info then return end
	return self.kf1v1_info.cur_season
end
function KuafuOnevoneWGData:SetIsAutoMatch(is_need)
	self.prepare_match = is_need
end
function KuafuOnevoneWGData:GetIsAutoMatch()
	return self.prepare_match
end