-- Y-运营活动-扶苏海棠.xls
local item_table={
[1]={item_id=47384,num=1,is_bind=1},
[2]={item_id=26409,num=10,is_bind=1},
[3]={item_id=47385,num=1,is_bind=1},
[4]={item_id=26444,num=20,is_bind=1},
[5]={item_id=47386,num=1,is_bind=1},
[6]={item_id=45017,num=5,is_bind=1},
[7]={item_id=47387,num=1,is_bind=1},
[8]={item_id=26409,num=20,is_bind=1},
[9]={item_id=47388,num=1,is_bind=1},
[10]={item_id=22753,num=1,is_bind=1},
[11]={item_id=26455,num=1,is_bind=1},
[12]={item_id=47389,num=1,is_bind=1},
[13]={item_id=26409,num=50,is_bind=1},
[14]={item_id=48118,num=2,is_bind=1},
[15]={item_id=47390,num=1,is_bind=1},
[16]={item_id=48118,num=3,is_bind=1},
[17]={item_id=26459,num=1,is_bind=1},
[18]={item_id=47391,num=1,is_bind=1},
[19]={item_id=26462,num=1,is_bind=1},
[20]={item_id=26464,num=1,is_bind=1},
[21]={item_id=47480,num=1,is_bind=1},
[22]={item_id=29627,num=10,is_bind=1},
[23]={item_id=47481,num=1,is_bind=1},
[24]={item_id=47482,num=1,is_bind=1},
[25]={item_id=29627,num=15,is_bind=1},
[26]={item_id=47483,num=1,is_bind=1},
[27]={item_id=29627,num=20,is_bind=1},
[28]={item_id=47484,num=1,is_bind=1},
[29]={item_id=47485,num=1,is_bind=1},
[30]={item_id=29627,num=50,is_bind=1},
[31]={item_id=47486,num=1,is_bind=1},
[32]={item_id=47487,num=1,is_bind=1},
[33]={item_id=39989,num=5,is_bind=1},
[34]={item_id=26437,num=1,is_bind=1},
[35]={item_id=56316,num=5,is_bind=1},
[36]={item_id=26444,num=1,is_bind=1},
[37]={item_id=28450,num=5,is_bind=1},
[38]={item_id=28665,num=5,is_bind=1},
[39]={item_id=39991,num=5,is_bind=1},
[40]={item_id=56317,num=10,is_bind=1},
[41]={item_id=28666,num=10,is_bind=1},
[42]={item_id=22753,num=2,is_bind=1},
[43]={item_id=28451,num=5,is_bind=1},
[44]={item_id=26460,num=1,is_bind=1},
[45]={item_id=28451,num=10,is_bind=1},
[46]={item_id=26570,num=1,is_bind=1},
[47]={item_id=26178,num=3,is_bind=1},
[48]={item_id=26178,num=6,is_bind=1},
[49]={item_id=26178,num=8,is_bind=1},
[50]={item_id=26178,num=12,is_bind=1},
[51]={item_id=26178,num=14,is_bind=1},
[52]={item_id=26178,num=26,is_bind=1},
[53]={item_id=26178,num=4,is_bind=1},
[54]={item_id=26178,num=7,is_bind=1},
[55]={item_id=26178,num=13,is_bind=1},
[56]={item_id=26178,num=9,is_bind=1},
[57]={item_id=26178,num=18,is_bind=1},
[58]={item_id=26178,num=27,is_bind=1},
[59]={item_id=26178,num=16,is_bind=1},
[60]={item_id=26178,num=20,is_bind=1},
[61]={item_id=26178,num=40,is_bind=1},
[62]={item_id=26178,num=60,is_bind=1},
[63]={item_id=26178,num=21,is_bind=1},
[64]={item_id=26178,num=54,is_bind=1},
[65]={item_id=26178,num=81,is_bind=1},
[66]={item_id=26178,num=24,is_bind=1},
[67]={item_id=26178,num=36,is_bind=1},
[68]={item_id=26450,num=1,is_bind=1},
[69]={item_id=26178,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=4,end_day=9,grade=2,},
{start_day=10,end_day=999,grade=3,}
},

open_day_meta_table_map={
},
reward={
{item=item_table[1],},
{step=2,item=item_table[2],},
{step=3,item=item_table[3],},
{step=4,item=item_table[4],},
{step=5,item=item_table[5],},
{step=6,item=item_table[6],},
{step=7,item=item_table[7],},
{step=8,item=item_table[8],},
{step=9,},
{step=10,item=item_table[9],},
{step=11,item=item_table[10],},
{step=12,item=item_table[11],},
{step=13,item=item_table[12],},
{step=14,item=item_table[13],},
{step=15,item=item_table[14],},
{step=16,item=item_table[15],},
{step=17,item=item_table[16],},
{step=18,item=item_table[17],},
{step=19,item=item_table[18],},
{step=20,item=item_table[19],},
{step=21,item=item_table[20],},
{grade=2,item=item_table[21],},
{grade=2,item=item_table[22],},
{grade=2,item=item_table[23],},
{grade=2,},
{grade=2,item=item_table[24],},
{grade=2,item=item_table[25],},
{grade=2,item=item_table[26],},
{grade=2,item=item_table[27],},
{grade=2,},
{grade=2,item=item_table[28],},
{grade=2,},
{grade=2,},
{grade=2,item=item_table[29],},
{grade=2,item=item_table[30],},
{grade=2,},
{grade=2,item=item_table[31],},
{grade=2,},
{grade=2,},
{grade=2,item=item_table[32],},
{grade=2,},
{grade=2,},
{grade=3,item=item_table[33],},
{step=2,item=item_table[34],},
{step=3,item=item_table[35],},
{step=4,item=item_table[36],},
{step=5,item=item_table[37],},
{grade=3,item=item_table[38],},
{grade=3,item=item_table[39],},
{grade=3,item=item_table[40],},
{grade=3,},
{grade=3,item=item_table[41],},
{grade=3,item=item_table[42],},
{grade=3,},
{step=13,item=item_table[43],},
{step=14,item=item_table[44],},
{grade=3,},
{step=16,item=item_table[45],},
{grade=3,},
{grade=3,},
{step=19,item=item_table[46],},
{grade=3,},
{grade=3,}
},

reward_meta_table_map={
[51]=9,	-- depth:1
[30]=51,	-- depth:2
[44]=43,	-- depth:1
[45]=43,	-- depth:1
[46]=43,	-- depth:1
[47]=43,	-- depth:1
[48]=6,	-- depth:1
[49]=7,	-- depth:1
[50]=8,	-- depth:1
[54]=12,	-- depth:1
[53]=11,	-- depth:1
[55]=43,	-- depth:1
[57]=15,	-- depth:1
[58]=43,	-- depth:1
[59]=17,	-- depth:1
[60]=18,	-- depth:1
[61]=43,	-- depth:1
[52]=10,	-- depth:1
[56]=43,	-- depth:1
[32]=11,	-- depth:1
[41]=20,	-- depth:1
[23]=2,	-- depth:1
[24]=3,	-- depth:1
[25]=4,	-- depth:1
[26]=5,	-- depth:1
[27]=6,	-- depth:1
[28]=7,	-- depth:1
[29]=8,	-- depth:1
[31]=10,	-- depth:1
[62]=41,	-- depth:2
[33]=54,	-- depth:2
[34]=13,	-- depth:1
[35]=14,	-- depth:1
[36]=57,	-- depth:2
[37]=16,	-- depth:1
[38]=59,	-- depth:2
[39]=60,	-- depth:2
[40]=19,	-- depth:1
[42]=21,	-- depth:1
[63]=42,	-- depth:2
},
consume={
{},
{step=2,cost_item_num=3,},
{step=3,cost_item_num=6,},
{step=4,cost_item_num=10,},
{step=5,cost_item_num=15,},
{step=6,cost_item_num=20,},
{step=7,cost_item_num=30,},
{step=8,cost_item_num=40,},
{step=9,cost_item_num=50,},
{step=10,cost_item_num=60,},
{step=11,cost_item_num=70,},
{step=12,cost_item_num=80,},
{step=13,cost_item_num=95,},
{step=14,cost_item_num=110,},
{step=15,cost_item_num=130,},
{step=16,cost_item_num=150,},
{step=17,cost_item_num=170,},
{step=18,cost_item_num=190,},
{step=19,cost_item_num=210,},
{step=20,cost_item_num=230,},
{step=21,cost_item_num=250,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,}
},

consume_meta_table_map={
[45]=3,	-- depth:1
[46]=4,	-- depth:1
[47]=5,	-- depth:1
[48]=6,	-- depth:1
[49]=7,	-- depth:1
[50]=8,	-- depth:1
[51]=9,	-- depth:1
[52]=10,	-- depth:1
[54]=12,	-- depth:1
[55]=13,	-- depth:1
[56]=14,	-- depth:1
[58]=16,	-- depth:1
[59]=17,	-- depth:1
[60]=18,	-- depth:1
[61]=19,	-- depth:1
[44]=2,	-- depth:1
[53]=11,	-- depth:1
[57]=15,	-- depth:1
[32]=53,	-- depth:2
[41]=20,	-- depth:1
[23]=44,	-- depth:2
[24]=45,	-- depth:2
[25]=46,	-- depth:2
[26]=47,	-- depth:2
[27]=48,	-- depth:2
[28]=49,	-- depth:2
[29]=50,	-- depth:2
[30]=51,	-- depth:2
[42]=21,	-- depth:1
[31]=52,	-- depth:2
[33]=54,	-- depth:2
[34]=55,	-- depth:2
[35]=56,	-- depth:2
[36]=57,	-- depth:2
[37]=58,	-- depth:2
[38]=59,	-- depth:2
[39]=60,	-- depth:2
[40]=61,	-- depth:2
[62]=41,	-- depth:2
[63]=42,	-- depth:2
},
task={
{task_type=1,param1=30,des="获得活跃点",target=30,open_panel="bizuo#bizuo_bizuo",},
{task_id=2,task_type=1,param1=50,des="获得活跃点",open_panel="bizuo#bizuo_bizuo",},
{task_id=3,param1=90,target=90,},
{task_id=4,param1=120,target=120,},
{task_id=5,param1=160,target=160,},
{task_id=6,param1=200,target=200,},
{task_id=7,task_type=2,item_list={[0]=item_table[47]},des="充值任意金额",target=1,open_panel="vip#recharge_cz",},
{task_id=8,task_type=3,des="每日登录",target=1,open_panel="",},
{task_id=9,task_type=4,param1=500,target=500,},
{task_id=10,task_type=4,param1=1000,target=1000,},
{task_id=11,task_type=4,param1=1500,target=1500,},
{task_id=12,task_type=4,param1=2000,target=2000,},
{task_id=13,task_type=4,param1=2500,target=2500,},
{task_id=14,task_type=4,param1=3000,target=3000,},
{task_id=15,task_type=4,param1=3500,target=3500,},
{task_id=16,task_type=4,param1=4000,target=4000,},
{task_id=17,task_type=4,param1=4500,target=4500,},
{task_id=18,task_type=4,param1=5000,target=5000,},
{task_id=19,param1=3280,item_list={[0]=item_table[48]},target=3280,},
{task_id=20,task_type=5,param1=6480,item_list={[0]=item_table[49]},des="累计充值灵玉",target=6480,open_panel="vip#recharge_cz",},
{task_id=21,param1=10000,item_list={[0]=item_table[50]},target=10000,},
{task_id=22,param1=20000,item_list={[0]=item_table[51]},target=20000,},
{task_id=23,param1=30000,item_list={[0]=item_table[52]},target=30000,},
{task_id=24,param1=3280,item_list={[0]=item_table[47]},target=3280,},
{task_id=25,param1=6480,item_list={[0]=item_table[53]},target=6480,},
{task_id=26,param1=10000,item_list={[0]=item_table[48]},target=10000,},
{task_id=27,param1=20000,item_list={[0]=item_table[54]},target=20000,},
{task_id=28,task_type=6,param1=30000,item_list={[0]=item_table[55]},des="累计消费灵玉",target=30000,open_panel="shop#Tab_Shop30",},
{task_id=29,param2=10,item_list={[0]=item_table[53]},target=10,},
{task_id=30,param2=50,item_list={[0]=item_table[54]},des="天地至宝寻宝",open_panel="TreasureHunt#treasurehunt_equip",},
{task_id=31,param2=100,item_list={[0]=item_table[56]},target=100,},
{task_id=32,param2=200,item_list={[0]=item_table[57]},target=200,},
{task_id=33,param2=350,item_list={[0]=item_table[58]},target=350,},
{task_id=34,param2=10,item_list={[0]=item_table[49]},target=10,},
{task_id=35,param1=2,param2=50,item_list={[0]=item_table[59]},des="沧溟珍宝寻宝",open_panel="TreasureHunt#treasurehunt_dianfeng",},
{task_id=36,param2=100,item_list={[0]=item_table[60]},target=100,},
{task_id=37,param2=200,item_list={[0]=item_table[61]},target=200,},
{task_id=38,param2=350,item_list={[0]=item_table[62]},target=350,},
{task_id=39,param2=10,item_list={[0]=item_table[50]},target=10,},
{task_id=40,param1=3,param2=50,item_list={[0]=item_table[63]},des="洪荒异宝寻宝",open_panel="TreasureHunt#treasurehunt_zhizun",},
{task_id=41,param2=100,item_list={[0]=item_table[58]},target=100,},
{task_id=42,param2=200,item_list={[0]=item_table[64]},target=200,},
{task_id=43,param2=350,item_list={[0]=item_table[65]},target=350,},
{task_id=44,param1=10,target=10,},
{task_id=45,param1=20,item_list={[0]=item_table[50]},target=20,},
{task_id=46,param1=40,target=40,},
{task_id=47,task_type=8,param1=60,item_list={[0]=item_table[66]},des="战纹秘宝寻宝",target=60,open_panel="TreasureHunt#treasurehunt_fuwen",},
{task_id=48,param1=90,item_list={[0]=item_table[67]},target=90,},
{grade=2,task_id=49,},
{grade=2,task_id=50,},
{grade=2,task_id=51,},
{grade=2,task_id=52,},
{grade=2,task_id=53,},
{grade=2,task_id=54,},
{grade=2,task_id=55,},
{grade=2,task_id=56,},
{grade=2,task_id=57,},
{grade=2,task_id=58,},
{grade=2,task_id=59,},
{grade=2,task_id=60,},
{grade=2,task_id=61,},
{grade=2,task_id=62,},
{grade=2,task_id=63,},
{grade=2,task_id=64,},
{grade=2,task_id=65,},
{grade=2,task_id=66,},
{grade=2,task_id=67,},
{grade=2,task_id=68,},
{grade=2,task_id=69,},
{grade=2,task_id=70,},
{grade=2,task_id=71,},
{grade=2,task_id=72,},
{grade=2,task_id=73,},
{grade=2,task_id=74,},
{grade=2,task_id=75,},
{grade=2,task_id=76,},
{grade=2,task_id=77,},
{grade=2,task_id=78,},
{grade=2,task_id=79,},
{grade=2,task_id=80,},
{grade=2,task_id=81,},
{grade=2,task_id=82,},
{grade=2,task_id=83,},
{grade=2,task_id=84,},
{grade=2,task_id=85,},
{grade=2,task_id=86,},
{grade=2,task_id=87,},
{grade=2,task_id=88,},
{grade=2,task_id=89,},
{grade=2,task_id=90,},
{grade=2,task_id=91,},
{grade=2,task_id=92,},
{grade=2,task_id=93,},
{grade=2,task_id=94,},
{grade=2,task_id=95,},
{grade=2,task_id=96,},
{grade=3,task_id=97,},
{grade=3,task_id=98,},
{grade=3,task_id=99,},
{grade=3,task_id=100,},
{grade=3,task_id=101,},
{grade=3,task_id=102,},
{grade=3,task_id=103,},
{grade=3,task_id=104,},
{grade=3,task_id=105,},
{grade=3,task_id=106,},
{grade=3,task_id=107,},
{grade=3,task_id=108,},
{grade=3,task_id=109,},
{grade=3,task_id=110,},
{grade=3,task_id=111,},
{grade=3,task_id=112,},
{grade=3,task_id=113,},
{grade=3,task_id=114,},
{grade=3,task_id=115,},
{grade=3,task_id=116,},
{grade=3,task_id=117,},
{grade=3,task_id=118,},
{grade=3,task_id=119,},
{grade=3,task_id=120,},
{grade=3,task_id=121,},
{grade=3,task_id=122,},
{grade=3,task_id=123,},
{grade=3,task_id=124,},
{grade=3,task_id=125,},
{grade=3,task_id=126,},
{grade=3,task_id=127,},
{grade=3,task_id=128,},
{grade=3,task_id=129,},
{grade=3,task_id=130,},
{grade=3,task_id=131,},
{grade=3,task_id=132,},
{grade=3,task_id=133,},
{grade=3,task_id=134,},
{grade=3,task_id=135,},
{grade=3,task_id=136,},
{grade=3,task_id=137,},
{grade=3,task_id=138,},
{grade=3,task_id=139,},
{grade=3,task_id=140,},
{grade=3,task_id=141,},
{grade=3,task_id=142,},
{grade=3,task_id=143,},
{grade=3,task_id=144,}
},

task_meta_table_map={
[66]=18,	-- depth:1
[65]=17,	-- depth:1
[64]=16,	-- depth:1
[63]=15,	-- depth:1
[62]=14,	-- depth:1
[105]=9,	-- depth:1
[61]=13,	-- depth:1
[60]=12,	-- depth:1
[106]=10,	-- depth:1
[59]=11,	-- depth:1
[108]=12,	-- depth:1
[109]=13,	-- depth:1
[110]=14,	-- depth:1
[111]=15,	-- depth:1
[112]=16,	-- depth:1
[113]=17,	-- depth:1
[114]=18,	-- depth:1
[107]=11,	-- depth:1
[57]=9,	-- depth:1
[58]=10,	-- depth:1
[32]=30,	-- depth:1
[126]=30,	-- depth:1
[104]=8,	-- depth:1
[78]=30,	-- depth:1
[31]=30,	-- depth:1
[6]=2,	-- depth:1
[5]=2,	-- depth:1
[4]=2,	-- depth:1
[3]=2,	-- depth:1
[29]=30,	-- depth:1
[56]=8,	-- depth:1
[50]=2,	-- depth:1
[98]=2,	-- depth:1
[33]=30,	-- depth:1
[49]=1,	-- depth:1
[77]=29,	-- depth:2
[79]=31,	-- depth:2
[80]=32,	-- depth:2
[81]=33,	-- depth:2
[21]=20,	-- depth:1
[83]=35,	-- depth:1
[131]=35,	-- depth:1
[34]=35,	-- depth:1
[129]=33,	-- depth:2
[128]=32,	-- depth:2
[88]=40,	-- depth:1
[127]=31,	-- depth:2
[125]=29,	-- depth:2
[103]=7,	-- depth:1
[102]=6,	-- depth:2
[101]=5,	-- depth:2
[100]=4,	-- depth:2
[99]=3,	-- depth:2
[19]=20,	-- depth:1
[36]=35,	-- depth:1
[136]=40,	-- depth:1
[37]=35,	-- depth:1
[51]=3,	-- depth:2
[52]=4,	-- depth:2
[53]=5,	-- depth:2
[54]=6,	-- depth:2
[55]=7,	-- depth:1
[46]=47,	-- depth:1
[45]=47,	-- depth:1
[44]=45,	-- depth:2
[43]=40,	-- depth:1
[27]=28,	-- depth:1
[26]=28,	-- depth:1
[25]=28,	-- depth:1
[24]=28,	-- depth:1
[97]=49,	-- depth:2
[22]=20,	-- depth:1
[42]=40,	-- depth:1
[41]=40,	-- depth:1
[39]=40,	-- depth:1
[38]=35,	-- depth:1
[48]=47,	-- depth:1
[23]=20,	-- depth:1
[135]=39,	-- depth:2
[121]=25,	-- depth:2
[119]=23,	-- depth:2
[142]=46,	-- depth:2
[141]=45,	-- depth:2
[140]=44,	-- depth:3
[139]=43,	-- depth:2
[138]=42,	-- depth:2
[120]=24,	-- depth:2
[137]=41,	-- depth:2
[133]=37,	-- depth:2
[132]=36,	-- depth:2
[130]=34,	-- depth:2
[124]=28,	-- depth:1
[123]=27,	-- depth:2
[122]=26,	-- depth:2
[134]=38,	-- depth:2
[118]=22,	-- depth:2
[72]=24,	-- depth:2
[116]=20,	-- depth:1
[67]=19,	-- depth:2
[68]=20,	-- depth:1
[69]=21,	-- depth:2
[70]=22,	-- depth:2
[71]=23,	-- depth:2
[143]=47,	-- depth:1
[73]=25,	-- depth:2
[74]=26,	-- depth:2
[75]=27,	-- depth:2
[76]=28,	-- depth:1
[82]=34,	-- depth:2
[117]=21,	-- depth:2
[84]=36,	-- depth:2
[86]=38,	-- depth:2
[87]=39,	-- depth:2
[89]=41,	-- depth:2
[90]=42,	-- depth:2
[91]=43,	-- depth:2
[92]=44,	-- depth:3
[93]=45,	-- depth:2
[94]=46,	-- depth:2
[95]=47,	-- depth:1
[96]=48,	-- depth:2
[115]=19,	-- depth:2
[85]=37,	-- depth:2
[144]=48,	-- depth:2
},
item_random_desc={
{item_id=47384,},
{number=2,item_id=26409,},
{number=3,item_id=47385,},
{number=4,item_id=26444,random_count=14.54,},
{number=5,item_id=47386,random_count=9.09,},
{number=6,item_id=45017,random_count=7.27,},
{number=7,item_id=47387,random_count=5.45,},
{number=8,random_count=3.64,},
{number=9,item_id=26450,random_count=1.82,},
{number=10,item_id=47388,random_count=1.45,},
{number=11,item_id=22753,random_count=0.91,},
{number=12,item_id=26455,random_count=0.55,},
{number=13,item_id=47389,random_count=0.36,},
{number=14,random_count=0.18,},
{number=15,random_count=0.09,},
{number=16,item_id=47390,random_count=0.05,},
{number=17,random_count=0.04,},
{number=18,item_id=26459,random_count=0.02,},
{number=19,item_id=47391,},
{number=20,item_id=26462,},
{number=21,item_id=26464,random_count=0.01,},
{grade=2,item_id=47480,},
{grade=2,item_id=29627,},
{grade=2,item_id=47481,},
{grade=2,},
{grade=2,item_id=47482,},
{grade=2,item_id=29627,},
{grade=2,item_id=47483,},
{number=8,random_count=3.64,},
{grade=2,},
{grade=2,item_id=47484,},
{grade=2,},
{grade=2,},
{grade=2,item_id=47485,},
{number=14,random_count=0.18,},
{grade=2,},
{grade=2,item_id=47486,},
{grade=2,},
{grade=2,},
{number=19,item_id=47487,},
{grade=2,},
{grade=2,},
{grade=3,item_id=39989,},
{number=2,item_id=26437,},
{number=3,item_id=56316,},
{grade=3,},
{grade=3,item_id=28450,},
{grade=3,item_id=28665,},
{grade=3,item_id=39991,},
{grade=3,item_id=56317,},
{grade=3,},
{grade=3,item_id=28666,},
{grade=3,},
{grade=3,},
{grade=3,item_id=28451,},
{grade=3,item_id=26460,},
{grade=3,},
{grade=3,item_id=28451,},
{grade=3,},
{grade=3,},
{grade=3,item_id=26570,},
{grade=3,},
{grade=3,}
},

item_random_desc_meta_table_map={
[38]=17,	-- depth:1
[36]=15,	-- depth:1
[57]=36,	-- depth:2
[59]=38,	-- depth:2
[44]=43,	-- depth:1
[20]=21,	-- depth:1
[19]=21,	-- depth:1
[24]=3,	-- depth:1
[23]=2,	-- depth:1
[14]=2,	-- depth:1
[8]=2,	-- depth:1
[45]=43,	-- depth:1
[46]=4,	-- depth:1
[47]=5,	-- depth:1
[48]=6,	-- depth:1
[49]=7,	-- depth:1
[50]=8,	-- depth:2
[61]=19,	-- depth:2
[51]=9,	-- depth:1
[52]=10,	-- depth:1
[53]=11,	-- depth:1
[55]=13,	-- depth:1
[56]=14,	-- depth:2
[58]=16,	-- depth:1
[60]=18,	-- depth:1
[54]=12,	-- depth:1
[32]=53,	-- depth:2
[41]=20,	-- depth:2
[25]=46,	-- depth:2
[26]=5,	-- depth:1
[27]=6,	-- depth:1
[28]=7,	-- depth:1
[29]=27,	-- depth:2
[30]=51,	-- depth:2
[42]=21,	-- depth:1
[31]=10,	-- depth:1
[33]=54,	-- depth:2
[34]=13,	-- depth:1
[35]=27,	-- depth:2
[37]=16,	-- depth:1
[39]=60,	-- depth:2
[40]=42,	-- depth:2
[62]=41,	-- depth:3
[63]=42,	-- depth:2
},
other_default_table={cost_item_id=26178,cost_gold=80,},

open_day_default_table={start_day=1,end_day=3,grade=1,},

reward_default_table={grade=1,step=1,item=item_table[68],is_show=1,},

consume_default_table={grade=1,step=1,cost_item_num=1,},

task_default_table={grade=1,task_id=1,task_type=7,param1=1,param2=0,item_list={[0]=item_table[69]},des="夺仙宝图收集仙灵气",target=50,open_panel="counrty_map_task_view",},

item_random_desc_default_table={grade=1,number=1,item_id=48118,random_count=18.18,is_rare=0,}

}

