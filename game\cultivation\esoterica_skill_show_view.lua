EsotericaSkillShowView = EsotericaSkillShowView or BaseClass(SafeBaseView)

function EsotericaSkillShowView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)

    local common_path = "uis/view/common_panel_prefab"
    local view_bundle = "uis/view/cultivation_ui_prefab"
    
    self:AddViewResource(0, common_path, "layout_a3_common_panel")
    self:AddViewResource(0, view_bundle, "layout_esoterica_skill_show_view")
    self:AddViewResource(0, common_path, "layout_a3_common_top_panel")
end

function EsotericaSkillShowView:LoadCallBack()

    self.cur_select_es_index = -1
    if not self.esoterica_show_list then
        self.esoterica_show_list = AsyncListView.New(SkillShowEsotericaRender, self.node_list["es_list"])
        self.esoterica_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectEsotericaCallBack, self))
    end

    if not self.skill_pre_view then
        self.skill_pre_view = SkillPreview.New(self.node_list.skill_pre_root, self.node_list.skill_pre_rawimage) 
        self.skill_pre_view:SetPreviewPlayEndCb(BindTool.Bind1(self.PreviewPlayEnd, self))
    end

end

function EsotericaSkillShowView:ReleaseCallBack()
    self.cur_select_es_index = -1
    self.cur_select_skill_data = nil

    if self.esoterica_show_list then
        self.esoterica_show_list:DeleteMe()
        self.esoterica_show_list = nil
    end

    if self.skill_pre_view then
		self.skill_pre_view:DeleteMe()
		self.skill_pre_view = nil
	end
end

--[[
	data = {
        slot = -1,
	}
]]
function EsotericaSkillShowView:SetShowData(data)
	self.show_data = data
end

function EsotericaSkillShowView:ShowIndexCallBack()
    -- self:ResetCameraFieldOfView()
end

-- 列表选择返回
function EsotericaSkillShowView:OnSelectEsotericaCallBack(item)
    if self:IsLimitClick() then
        return
    end

	if nil == item or nil == item.data then
		return
	end

    local data = item.data
    local slot = data.slot

    if self.cur_select_es_index == slot then
        return
    end

    self.cur_select_es_index = slot

    local cfg = CultivationWGData.Instance:GetEsotericaCfg(slot)
    self.cur_select_skill_data = cfg.skill_id

    -- 刷新技能格子
    
    if cfg then
        self:FlushSkillMessage()
        self:FlushPlaySkill()
        -- self:FlushSkillInfo(cfg.skill_id, 1, cfg.name, cfg.esoterica_des)
    end
end





function EsotericaSkillShowView:IsLimitClick(no_tips)
    if not self.skill_play_timestemp then
        return false
    end
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp
    if not no_tips and is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end

-- 技能预览结束回调
function EsotericaSkillShowView:PreviewPlayEnd()
    self:RemovePreSkillDelayTimer()

	self.show_pre_skill_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
		if not self.cur_select_skill_data then
			return
		end

		if self.skill_pre_view and self.skill_pre_view:GetPreviewIsLoaded() then
			self.skill_pre_view:PlaySkill(self.cur_select_skill_data)
		end
	end, 4)
end

--移除回调
function EsotericaSkillShowView:RemovePreSkillDelayTimer()
    if self.show_pre_skill_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_pre_skill_delay_timer)
        self.show_pre_skill_delay_timer = nil
    end
end

function EsotericaSkillShowView:OnFlush()
    if not self.show_data then
        return
    end

    local es_list_cfg = CultivationWGData.Instance:GetEsotericaShowList()
    if IsEmptyTable(es_list_cfg) then
        return
    end

    if self.esoterica_show_list then
        self.esoterica_show_list:SetDataList(es_list_cfg)
    end

    local show_slot = self.show_data.slot
    if self.cur_select_es_index ~= show_slot then
        local jump_index = 1
        for k,v in pairs(es_list_cfg) do
            if show_slot == v.slot then
                jump_index = k
                break
            end
        end

        self.esoterica_show_list:JumpToIndex(jump_index)
    end
end


-- 刷新人物模型
function EsotericaSkillShowView:FlushPlaySkill()
    if self.skill_pre_view:GetPreviewIsLoaded() then
		self.skill_pre_view:PlaySkill(self.cur_select_skill_data)
	else
		self.skill_pre_view:SetPreviewLoadCb(function()
			self.skill_pre_view:PlaySkill(self.cur_select_skill_data)
		end)
	end
end

-- 刷新技能详情
function EsotericaSkillShowView:FlushSkillMessage()
    if not self.cur_select_es_index then
        return
    end

    local es_cfg = CultivationWGData.Instance:GetEsotericaCfg(self.cur_select_es_index)

    local skill_id = es_cfg.skill_id
    local skill_level = 1
    self.node_list.skill_lv.text.text = string.format(Language.Rank.Level, ToColorStr(skill_level, COLOR3B.GREEN)) 

    local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
    if skill_cfg then
        self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.icon_resource))
    end

	if es_cfg then
        self.node_list.skill_name.text.text = es_cfg.name
        self.node_list.skill_cd.text.text = string.format(Language.Skill.SkillCd, es_cfg.cd_time)
    else
        self.node_list.skill_name.text.text = Language.Esoterica.SkillName
        self.node_list.skill_cd.text.text = ""
	end

    self.node_list.skill_desc.text.text = SkillWGData.Instance:GetSKillDescBySkillId(skill_id)
end












--===================================================================
SkillShowEsotericaRender = SkillShowEsotericaRender or BaseClass(BaseRender)
function SkillShowEsotericaRender:OnFlush()
	if not self.data then
		return
	end

    -- self.node_list["name"].text.text = self.data.name



    -- local bundle = "uis/view/cultivation_ui/images_atlas"
    -- local asset = "a3_xj_icon" .. self.data.slot
    -- self.node_list["icon"].image:LoadSprite(bundle, asset, function()
    --     self.node_list["icon"].image:SetNativeSize()
    -- end)

    self.node_list.text_level.text.text = self.data.level
    self.node_list.name.text.text = self.data.name

    local bundle, asset = ResPath.GetCultivationImg("a3_xf_yq" .. self.data.img_id)
    self.node_list.bg.image:LoadSprite(bundle, asset, function ()
        self.node_list["bg"].image:SetNativeSize()
    end)

    -- self.node_list["act_flag"]:SetActive(self.data.level > 0)
    if self.data.skill_label and self.data.skill_label ~= "" then
        self.node_list.label_bg:SetActive(true)
		local split_list = string.split(self.data.skill_label, "|")
        if split_list[1] then
            local label1 = split_list[1]:sub(1, 3)
		    local label2 = split_list[1]:sub(4, 6)
            self.node_list["label"].text.text = string.format(Language.Esoterica.LabelDesc, label2, label1)
        end
    else
        self.node_list.label_bg:SetActive(false)
	end

    self.node_list.lock_flag:SetActive(false)
end

function SkillShowEsotericaRender:OnSelectChange(is_select)
    self.node_list["select"]:SetActive(is_select)
end