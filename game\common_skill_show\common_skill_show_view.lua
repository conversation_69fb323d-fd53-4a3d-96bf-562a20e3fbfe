-- 基类
CommonSkillShowView = CommonSkillShowView or BaseClass(SafeBaseView)
local default_fight_camera_field = 22

function CommonSkillShowView:__init()
	self.skill_play_timestemp = 0
	self.normal_attack_play_timestemp = 0
	self.is_fazhen_skill = false
	self.is_halo_skill = false
	self.is_shower_loaded = false
	self.is_enemy_loaded = false
	self.special_skill_effect_loader = {}
	self.buff_effect_loader = {}
	self.buff_effect_load_delaytimer = {}
	self.urpcamera_component = nil
	self.summon_obj_list = {}
	self.line_effect_cache = {}
	self.is_all_summon_obj_loaded = false
end

function CommonSkillShowView:ReleaseCallBack()
	Runner.Instance:RemoveRunObj(self)
	self:KillMoveTweener()
	self:ClearShowCameraTween()
	self:CleanSummonObj()
	self.show_data = nil
	self.show_appearance_data = nil
	self.general_attack_list = nil
	self.general_attack_index = nil
	self.urpcamera_component = nil

	if self.shower_obj then
		self.shower_obj:DeleteMe()
		self.is_shower_loaded = false
		self.shower_obj = nil
	end

	if self.enemy_obj then
		self.enemy_obj:DeleteMe()
		self.is_enemy_loaded = false
		self.enemy_obj = nil
	end
end

function CommonSkillShowView:OpenCallBack()
	self:TrySetCamera()
end

function CommonSkillShowView:CloseCallBack()
	self.skill_play_timestemp = 0
	self.normal_attack_play_timestemp = 0
	self:ClearDelayDoFuncTimer()
	self:ClearDelayLoopPlaySkillAttackTimer()
	self:ClearDelaySimulateGeneralAttackOpera()
	self:ClearDelaySimulateSpecialSkillOpera()
	self:ClearTimesHurtTimer()
	self:ClearSpeclaiSkillEffect()
	self:ClearBuffEffect()
	self:ClearShowCameraTween()
	self:TryResetCamera()
	self:CleanDelayDieCDTimer()
	self:CleanDelayReliveCDTimer()
	self:ClearLineEffect()
	self:ClearSkillInfo()
end

function CommonSkillShowView:LoadCallBack()
	self.origin_enemy_pos = self.node_list["enemy_pos"].transform.localPosition
	self.origin_shower_pos = self.node_list["shower_pos"].transform.localPosition
	Runner.Instance:AddRunObj(self, 8)
end

-- 初始化 木桩怪
function CommonSkillShowView:InitEnemy()
	if nil ~= self.enemy_obj then
		return
	end

	self.is_enemy_loaded = false
	local enemy_vo = RoleVo.New()
	enemy_vo.name = ""
	enemy_vo.level = 1
	enemy_vo.sex = 1
	enemy_vo.prof = 1
	enemy_vo.max_hp = 10
	enemy_vo.hp = 10
	self.enemy_obj = SkillShowerMonster.New(enemy_vo)
	local draw_obj = self.enemy_obj:GetDrawObj()
	draw_obj:SetScale(1, 1, 1)
	self.enemy_obj:SetModelLoadCallback(function()
		self.is_enemy_loaded = true
		self:TryPlaySkillAttack()
	end)

	local draw_obj_trans = draw_obj:GetTransfrom()
	draw_obj_trans:SetParent(self.node_list["enemy_pos"].transform)
	draw_obj_trans.localPosition = u3dpool.vec3(0, 0, 0)
	draw_obj_trans.localRotation = Quaternion.Euler(0, 0, 0)
end

-- 修改木桩怪 展示位置
function CommonSkillShowView:ChangeEnemyShowPos()
	local enemy_show_pos
	if self.is_fazhen_skill or self.is_halo_skill then
		enemy_show_pos = u3dpool.vec3(0, 0, 0)
	end

	local pos = enemy_show_pos or self.origin_enemy_pos
	Transform.SetLocalPosition(self.node_list["enemy_pos"].transform, pos)
end

-- 初始化 展示者
function CommonSkillShowView:InitShower(show_data)
	show_data = show_data or self.show_data
	if IsEmptyTable(show_data) then
		return
	end

	if nil ~= self.shower_obj then
		self.shower_obj:DeleteMe()
	end

	local sa_data = self.show_appearance_data or {}
	self.is_shower_loaded = false
	local role_vo = RoleWGData.Instance:GetRoleVo()
	local shower_vo = RoleVo.New()
	shower_vo.name = ""
	shower_vo.level = 1
	shower_vo.sex = role_vo.sex or 1
	shower_vo.prof = role_vo.prof or 1
	shower_vo.max_hp = 10
	shower_vo.hp = 10
	shower_vo.shenwu_appeid = sa_data.shenwu_appeid or role_vo.shenwu_appeid
	shower_vo.appearance = ProtocolStruct.RoleAppearance()
	shower_vo.appearance.fashion_body = sa_data.fashion_body or role_vo.appearance.fashion_body
	shower_vo.appearance.fashion_wuqi = role_vo.appearance.fashion_wuqi
	shower_vo.appearance.fashion_guanghuan = sa_data.fashion_guanghuan or 0
	shower_vo.jianzhen_appeid = sa_data.jianzhen_appeid or 0
	shower_vo.wing_appeid = sa_data.wing_appeid or 0
	-- shower_vo.fabao_appeid = sa_data.fabao_appeid or 0

	shower_vo.appearance.fazhen_id = show_data.fazhen_id or -1
	shower_vo.appearance.skill_halo_id = show_data.skill_halo_id or 0
	shower_vo.appearance_param = show_data.tianshen_appearance_id or 0
	shower_vo.special_appearance = show_data.special_appearance or 0
	shower_vo.appearance_param_extend = show_data.appearance_param_extend or {}

	shower_vo.mount_appeid = show_data.mount_appeid or 0
	shower_vo.fight_mount_skill_level = show_data.fight_mount_skill_level or 0
	shower_vo.wuhun_id = show_data.wuhun_id or 0
	shower_vo.wuhun_lv = show_data.wuhun_lv or 0
	shower_vo.beast_id = show_data.beast_id or {}

	local shower_obj = SkillShower.New(shower_vo, nil, self.node_list["shower_pos"].transform)
	self.shower_obj = shower_obj
	shower_obj:SetEnemyPos(self.node_list["enemy_pos"].gameObject)
	shower_obj:SetWuHunCreateCallBack(BindTool.Bind(self.WuHunCreateCallBack, self))
	shower_obj:SetBeastCreateCallBack(BindTool.Bind(self.BeastCreateCallBack, self))
	shower_obj:SetModelLoadCallback(function()
		self.is_shower_loaded = true
		self:TryPlaySkillAttack()
	end)

	shower_obj:SetAttackHitCallback(BindTool.Bind(self.AttackHitCallback, self))

	local draw_obj = shower_obj:GetDrawObj()
	draw_obj:SetScale(1, 1, 1)
	local draw_obj_trans = draw_obj:GetTransfrom()
	draw_obj_trans:SetParent(self.node_list["shower_pos"].transform)
	draw_obj_trans.localPosition = u3dpool.vec3(0, 0, 0)
	draw_obj_trans.localRotation = Quaternion.Euler(0, 0, 0)
end

-- 初始化 召唤物
function CommonSkillShowView:CreateSummonObj(index, data)
	if IsEmptyTable(data) then
		return
	end

	local role_vo = RoleWGData.Instance:GetRoleVo()
	local sunnon_vo = RoleVo.New()
	sunnon_vo.name = ""
	sunnon_vo.level = 1
	sunnon_vo.sex = role_vo.sex or 1
	sunnon_vo.prof = role_vo.prof or 1
	sunnon_vo.max_hp = 10
	sunnon_vo.hp = 10
	sunnon_vo.monster_id = data.monster_id

	local shower_trans = self.node_list["shower_pos"].transform
	local enemy_trans = self.node_list["enemy_pos"].gameObject
	local sunnon_obj = SkillShower.New(sunnon_vo, nil, shower_trans)
	sunnon_obj:SetEnemyPos(enemy_trans)
	sunnon_obj:SetModelLoadCallback(function()
		if self.summon_obj_list[index] then
			self.summon_obj_list[index].is_loaded = true
		end

		self:CheckSummonAllLoadComplete()
	end)

	local draw_obj = sunnon_obj:GetDrawObj()
	draw_obj:SetScale(1, 1, 1)
	local draw_obj_trans = draw_obj:GetTransfrom()
	draw_obj_trans:SetParent(shower_trans)
	-- 相对位置
	draw_obj_trans.localPosition = u3dpool.vec3(data.rel_pos_x, 0, data.rel_pos_y)
	-- 朝向
	draw_obj_trans.localRotation = Quaternion.Euler(0, 0, 0)
	if data.look_dir == 0 then
		local shower_obj_trans
		if self.shower_obj then
			local obj = self.shower_obj:GetDrawObj()
			if obj then
				shower_obj_trans = obj:GetTransfrom()
			end
		end

		if shower_obj_trans then
			draw_obj_trans:LookAt(shower_obj_trans)
		end
	else
		local enemy_obj_trans
		if self.enemy_obj then
			local obj = self.enemy_obj:GetDrawObj()
			if obj then
				enemy_obj_trans = obj:GetTransfrom()
			end
		end

		if enemy_obj_trans then
			draw_obj_trans:LookAt(enemy_obj_trans)
		end
	end

	return sunnon_obj
end

function CommonSkillShowView:CleanSummonObj()
	for k, v in pairs(self.summon_obj_list) do
		v.obj:DeleteMe()
	end

	self.summon_obj_list = {}
end

-- 模拟召唤
function CommonSkillShowView:SimulateSummonObj()
	self:CleanSummonObj()
	self.is_all_summon_obj_loaded = false
	local summon_obj_list = CommonSkillShowData.Instance:GetCommonSkillSummonObjList(self.show_data.skill_id,
		self.show_data.level)
	for k, v in ipairs(summon_obj_list) do
		local data = {}
		data.obj = self:CreateSummonObj(k, v)
		data.alive_time = v.alive_time
		table.insert(self.summon_obj_list, data)
	end
end

function CommonSkillShowView:CheckSummonAllLoadComplete()
	for k, v in pairs(self.summon_obj_list) do
		if not v.is_loaded then
			return
		end
	end

	self.is_all_summon_obj_loaded = true
	self:OnAllSummonLoaded()
end

function CommonSkillShowView:OnAllSummonLoaded()
end

-- 清除循环执行技能展示
function CommonSkillShowView:ClearDelayLoopPlaySkillAttackTimer()
	if self.delay_loop_skill_attack_timer then
		GlobalTimerQuest:CancelQuest(self.delay_loop_skill_attack_timer)
		self.delay_loop_skill_attack_timer = nil
	end
end

-- 播放技能
function CommonSkillShowView:TryPlaySkillAttack()
	if not self.is_shower_loaded or not self.is_enemy_loaded then
		return
	end

	if IsEmptyTable(self.show_data) then
		return
	end

	-- print_error("-----播放技能---")
	self:ClearDelayLoopPlaySkillAttackTimer()
	self:ClearDelayDoFuncTimer()
	self:ClearSpeclaiSkillEffect()
	self:ClearBuffEffect()

	local loop_time = 12
	if self.is_fazhen_skill then
		self:SimulateTimesHurt()
	elseif self.is_halo_skill then
		self:DelayDoFunc(3)
	end

	if self.show_data.not_loop_skill then
		return
	end

	self.delay_loop_skill_attack_timer = GlobalTimerQuest:AddTimesTimer(function()
		self:TryPlaySkillAttack()
	end, loop_time, 1)
end

function CommonSkillShowView:ClearDelayDoFuncTimer()
	if self.delay_do_func_timer then
		GlobalTimerQuest:CancelQuest(self.delay_do_func_timer)
		self.delay_do_func_timer = nil
	end
end

-- 延迟执行方法
function CommonSkillShowView:DelayDoFunc(time)
	self.delay_do_func_timer = GlobalTimerQuest:AddTimesTimer(function()
		if self.is_halo_skill then
			self:TryDoGeneralAttack()
			-- self:DelaySimulateSpecialSkillOpera(4)
		end
	end, time or 0, 1)
end

-- 普攻展示
function CommonSkillShowView:TryDoGeneralAttack()
	self.general_attack_list = CommonSkillShowData.GetRoleNormalSkillList()
	self.general_attack_index = 1

	-- print_error("----general_attack_list---", self.general_attack_list)
	self:SimulateGeneralAttackOpera()
end

function CommonSkillShowView:ClearDelaySimulateGeneralAttackOpera()
	if self.general_attack_timer then
		GlobalTimerQuest:CancelQuest(self.general_attack_timer)
		self.general_attack_timer = nil
	end
end

-- 单个普攻
function CommonSkillShowView:SimulateSingleGeneralAttackOpera(skill_id, attack_index)
	if self.shower_obj then
		self.shower_obj:SetAttackIndex(attack_index)
		self.shower_obj:DoAttackForNoTarget(skill_id, 0)

		local wuhun_obj = self:GetWuHunObj()
		if wuhun_obj then
			wuhun_obj:DoAttackForNoTarget(skill_id, 0)
		end

		if self.shower_obj and self.shower_obj.shaungsheng_tianshen_obj then
			self.shower_obj.shaungsheng_tianshen_obj:SetAttackIndex(attack_index)
			self.shower_obj.shaungsheng_tianshen_obj:DoAttackForNoTarget(skill_id, 0)
		end

		-- local anim_name = SkillWGData.GetSkillActionStr(SceneObjType.SkillShower, skill_id, attack_index)
		-- local action_config = self.shower_obj:GetActionTimeRecord(anim_name)
		-- -- print_error("---单个普攻----", skill_id, attack_index, anim_name, action_config)
		-- if action_config then
		-- 	self:SimulateTimesHurt({action_config.hit_time})
		-- end
	end
end

-- 获取技能展示时间
function CommonSkillShowView:GetRoleSkillTime(skill_id, attack_index)
	if self.shower_obj then
		local anim_name = SkillWGData.GetSkillActionStr(SceneObjType.SkillShower, skill_id, attack_index)
		local action_config = self.shower_obj:GetActionTimeRecord(anim_name)
		if action_config then
			local before_skill_cfg = SkillWGData.Instance:GetBeforeSkillCfg(skill_id)
			if before_skill_cfg and before_skill_cfg.skill_type == FRONT_SKILL_TYPE.CHARGE then
				-- 加个1s播冲刺 + 停留
				return action_config.time + 1
			end

			return action_config.time
		end
	end
	return 0
end

-- 模拟四段普攻
function CommonSkillShowView:SimulateGeneralAttackOpera()
	self:ClearDelaySimulateGeneralAttackOpera()
	if IsEmptyTable(self.general_attack_list) then
		return
	end

	local data = self.general_attack_list[self.general_attack_index] or {}
	local skill_id = data.skill_id
	if not skill_id then
		if self.is_halo_skill then
			self:DelaySimulateSpecialSkillOpera(1)
		end

		self:SimulateGeneralAttackOperaComplete()
		return
	end

	self.general_attack_index = self.general_attack_index + 1
	self:SimulateSingleGeneralAttackOpera(skill_id, self.general_attack_index)
	local do_next_action_time = self:GetRoleSkillTime(skill_id, self.general_attack_index)

	if do_next_action_time > 0 then
		self.general_attack_timer = GlobalTimerQuest:AddTimesTimer(function()
			self:SimulateGeneralAttackOpera()
		end, do_next_action_time, 1)
	end
end

function CommonSkillShowView:ClearDelaySimulateSpecialSkillOpera()
	if self.delay_fanzhen_skill_timer then
		GlobalTimerQuest:CancelQuest(self.delay_fanzhen_skill_timer)
		self.delay_fanzhen_skill_timer = nil
	end
end

-- 模拟四段普攻 完成
function CommonSkillShowView:SimulateGeneralAttackOperaComplete()
end

-- 延迟模拟技能操作
function CommonSkillShowView:DelaySimulateSpecialSkillOpera(delay_time)
	-- print_error("----DelaySimulateSpecialSkillOpera----", delay_time)
	self:ClearDelaySimulateSpecialSkillOpera()

	delay_time = delay_time or 0

	if delay_time > 0 then
		self.delay_fanzhen_skill_timer = GlobalTimerQuest:AddTimesTimer(function()
			self:SimulateSpecialSkillOpera()
		end, delay_time, 1)
	else
		self:SimulateSpecialSkillOpera()
	end
end

-- 模拟技能操作
function CommonSkillShowView:SimulateSpecialSkillOpera()
	if IsEmptyTable(self.show_data) then
		return
	end

	-- print_error("---SimulateSpecialSkillOpera---", self.show_data.skill_id, self.show_data.level)
	local bundle, asset, loader_name, alive_time, parent_root, callback
	if self.is_fazhen_skill then
		bundle = "effects2/prefab/footlight_prefab"
		asset = string.format("FootLight_%d_skill", self.show_data.fazhen_id)
		loader_name = "FootLight_skill"
		parent_root = self.node_list["shower_pos"].transform
		alive_time = 5
	else
		local skill_data = CommonSkillShowData.Instance:GetCommonSkillData(self.show_data.skill_id, self.show_data.level)
		if not IsEmptyTable(skill_data) then
			parent_root = skill_data.target_type == 0 and self.node_list["shower_pos"].transform or
			self.node_list["enemy_pos"].gameObject.transform
			alive_time = skill_data.alive_time
			bundle = skill_data.effect_bundle
			asset = skill_data.effect_asset
			loader_name = "skill_show_special_loader"
			self:SimulateTimesHurt(skill_data.times_hurt_table)
		end
	end

	-- print_error("---SimulateSpecialSkillOpera---", bundle, asset, alive_time)
	if bundle and asset then
		self:AddSpecialSkillEffect(bundle, asset, loader_name, alive_time, parent_root, callback)
	end

	self:SimulateSkillBuff()
	PlaySkillFloatWordWGCtrl.Instance:SetSkillShowWord(self.show_data.skill_id)
	self.shower_obj:DoAttackForNoTarget(self.show_data.skill_id, 0)

	if self.shower_obj and self.shower_obj.shaungsheng_tianshen_obj then
		self.shower_obj.shaungsheng_tianshen_obj:DoAttackForNoTarget(self.show_data.skill_id, 0)
	end
	self:DoShowerSpecialAction()
end

function CommonSkillShowView:AttackHitCallback(skill_id)
	-- print_error("----攻击返回----", skill_id)
	if self.enemy_obj then
		self.enemy_obj:DoHurt()
	end
end

-- 模拟多段伤害计时器清除
function CommonSkillShowView:ClearTimesHurtTimer()
	if self.times_hurt_timer and CountDown.Instance:HasCountDown(self.times_hurt_timer) then
		CountDown.Instance:RemoveCountDown(self.times_hurt_timer)
		self.times_hurt_timer = nil
	end
end

-- 多段伤害每一下的时间间隔
-- key 技能id、自定义
local times_hurt_table = {
	["fazhen"] = { 1.3, 1.3, 1.3 },
}
-- 模拟多段伤害
function CommonSkillShowView:SimulateTimesHurt(times_table)
	if IsEmptyTable(self.show_data) then
		return
	end

	-- local skill_id = self.show_data.skill_id
	self:ClearTimesHurtTimer()

	if self.is_fazhen_skill then
		times_table = times_hurt_table["fazhen"]
	end

	-- print_error("---SimulateTimesHurt---", times_table)
	if not times_table then
		return
	end

	local total_time, cur_time, cur_count = 0, 0, 1
	cur_time = times_table[1]
	for k, v in ipairs(times_table) do
		total_time = total_time + v
	end

	self.times_hurt_timer = CountDown.Instance:AddCountDown(total_time, 0.1,
		function(elapse_time, total_time)
			if elapse_time > cur_time then
				cur_count = cur_count + 1
				cur_time = times_table[cur_count] and (times_table[cur_count] + cur_time) or total_time
				if self.enemy_obj then
					self.enemy_obj:DoHurt()
				end
			end
		end,
		-- 完成func
		function()
			if self.enemy_obj then
				self.enemy_obj:DoHurt()
			end

			if self.is_fazhen_skill then
				self:DelaySimulateSpecialSkillOpera(2)
			end
		end
	)
end

-- 清除技能特效
function CommonSkillShowView:ClearSpeclaiSkillEffect()
	for k, v in pairs(self.special_skill_effect_loader) do
		v:Destroy()
	end

	self.special_skill_effect_loader = {}
end

-- 添加技能特效
function CommonSkillShowView:AddSpecialSkillEffect(bundle, asset, loader_name, alive_time, parent_root, callback)
	if not alive_time or not parent_root then
		return
	end

	local effect_loader = AllocAsyncLoader(self, loader_name)
	effect_loader:SetObjAliveTime(alive_time)
	effect_loader:SetIsUseObjPool(true)
	effect_loader:SetParent(parent_root)
	effect_loader:Load(bundle, asset, function(obj)
		if IsNil(obj) then
			return
		end

		-- obj.transform.localPosition = Vector3(0, 0, 0)
		-- obj.transform.localRotation = Quaternion.Euler(0, parent_root.localEulerAngles.y, 0)
		-- obj.transform.localScale = Vector3(1, 1, 1)
		if callback then
			callback(obj)
		end
	end)

	table.insert(self.special_skill_effect_loader, effect_loader)
end

-- 模拟技能附带的BUff
function CommonSkillShowView:SimulateSkillBuff()
	if IsEmptyTable(self.show_data) then
		return
	end

	local buff_list
	if self.is_fazhen_skill then
		buff_list = CommonSkillShowData.Instance:GetFaZhenSkillBuffList(self.show_data.skill_id)
	else
		buff_list = CommonSkillShowData.Instance:GetCommonSkillBuffList(self.show_data.skill_id, self.show_data.level)
	end

	if IsEmptyTable(buff_list) then
		return
	end

	-- print_error("----模拟技能附带的BUff----", #buff_list, buff_list)
	local buff_load_func = function(buff_info)
		local buff_effect_cfg
		if buff_info.buff_id and buff_info.buff_id ~= 0 then
			local buff_cfg = SkillWGData.Instance:GetBuffCfgById(buff_info.buff_id)
			buff_effect_cfg = FightWGData.Instance:GetBuffEffectCfg(buff_cfg.sub_type, buff_info.skill_id)
		else
			buff_effect_cfg = FightWGData.Instance:GetBuffEffectCfg(buff_info.buff_type)
		end

		local bundle, asset
		if buff_effect_cfg then
			bundle, asset = buff_effect_cfg.bundle, buff_effect_cfg.asset
		end

		-- print_error("----buff_load_func-----", buff_effect_cfg == nil, bundle, asset)
		if not bundle or not asset or bundle == "" or asset == "" then
			return
		end

		-- print_error("----buff_attach_point-----", buff_effect_cfg.attach_index, AttachPoint.BuffMiddle)
		local buff_attach_point = buff_effect_cfg.attach_index ~= "" and buff_effect_cfg.attach_index or
		AttachPoint.BuffMiddle
		local parent_root, loader_name
		if buff_info.target_type == 0 then
			if self.shower_obj then
				parent_root = self.shower_obj:GetDrawObj():GetAttachPoint(buff_attach_point)
				loader_name = "shower_buff_effect_loader_"
			end
		else
			if self.enemy_obj then
				parent_root = self.enemy_obj:GetDrawObj():GetAttachPoint(buff_attach_point)
				loader_name = "emeny_buff_effect_loader_"
			end
		end

		self:AddBuffEffect(bundle, asset, loader_name, buff_info.alive_time, parent_root)
	end

	for k, buff_info in pairs(buff_list) do
		if buff_info.delay_play_time > 0 then
			local delay_timer = GlobalTimerQuest:AddDelayTimer(function()
				buff_load_func(buff_info)

				if CommonSkillShowData.GetIsAttackHurtBuff(buff_info.buff_type) and self.enemy_obj then
					self.enemy_obj:DoHurt()
				end
			end, buff_info.delay_play_time)
			self.buff_effect_load_delaytimer[#self.buff_effect_load_delaytimer + 1] = delay_timer
		else
			if CommonSkillShowData.GetIsAttackHurtBuff(buff_info.buff_type) and self.enemy_obj then
				self.enemy_obj:DoHurt()
			end
			buff_load_func(buff_info)
		end

		-- 死亡释放技能
		if buff_info.buff_type == BUFF_TYPE.EBT_ROLE_DIE_EFFECT then
			self:CreateDieEffect(buff_info)
			-- 每隔几秒定时触发概率buff
		elseif buff_info.buff_type == BUFF_TYPE.EBT_TIMER_OUT_ADD_EFFECT then
			self:CreateTimerOutAddEffect(buff_info)
			-- 创建连线
		elseif buff_info.buff_type == BUFF_TYPE.EBT_ATTR_LINE then
			self:CreateLineEffect(buff_info)
		end
	end
end

-- 清除技能特效
function CommonSkillShowView:ClearBuffEffect()
	for k, v in pairs(self.buff_effect_load_delaytimer) do
		GlobalTimerQuest:CancelQuest(v)
	end

	self.buff_effect_load_delaytimer = {}

	for k, v in pairs(self.buff_effect_loader) do
		v:Destroy()
	end

	self.buff_effect_loader = {}
end

-- 添加技能特效
function CommonSkillShowView:AddBuffEffect(bundle, asset, loader_name, alive_time, parent_root, callback)
	-- print_error("---AddBuffEffect--", bundle, asset, loader_name, alive_time, not parent_root)
	if not parent_root then
		return
	end

	local effect_loader = AllocAsyncLoader(self, loader_name .. (#self.buff_effect_loader + 1))
	effect_loader:SetObjAliveTime(alive_time)
	effect_loader:SetIsUseObjPool(true)
	effect_loader:SetParent(parent_root)
	effect_loader:Load(bundle, asset, function(obj)
		if IsNil(obj) then
			return
		end

		if callback then
			callback(obj)
		end
	end)

	table.insert(self.buff_effect_loader, effect_loader)
end

function CommonSkillShowView:InitSkillInfo()
	self.message_root_tween = self.node_list.skill_desc_root:GetComponent(typeof(UGUITweenPosition))
	XUI.AddClickEventListener(self.node_list.bag_forward_button,
		BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, false))
	XUI.AddClickEventListener(self.node_list.bag_reverse_button,
		BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, true))
end

--技能描述展示按钮.
function CommonSkillShowView:PlayBeastsMessagePositionTween(is_forward)
	self.node_list.bag_forward_button:CustomSetActive(is_forward)
	self.node_list.bag_reverse_button:CustomSetActive(not is_forward)

	if not IsNil(self.message_root_tween) then
		if is_forward then
			self.message_root_tween:PlayForward()
		else
			self.message_root_tween:PlayReverse()
		end
	end
end

--刷新技能描述.
function CommonSkillShowView:FlushSkillInfo(skill_id, skill_level, skill_name, skill_desc)
	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	if client_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
	end

	self.node_list.skill_nane.text.text = skill_name
	self.node_list.skill_desc.text.text = skill_desc
end

--释放技能描述动画.
function CommonSkillShowView:ClearSkillInfo()
	if not IsNil(self.message_root_tween) then
		self.message_root_tween = nil
	end
end

--========================= 摄像机
-- 全屏的技能界面 需要重新设置主摄像机
function CommonSkillShowView:TrySetCamera()
	if self.is_set_view_camera then
		return
	end

	if not self:IsLoaded() then
		return
	end

	self.urpcamera_component = self.node_list.Camera:GetComponent(typeof(URPCamera)) or nil
	if not IsNil(self.urpcamera_component) then
		self.is_set_view_camera = true
		Scene.Instance:CtrlFollowCameraEnabled(false)

		local snap_shot_bg = GameObject.Find("GameRoot/UILayer/SnapShotBackground")
		if not IsNil(snap_shot_bg) then
			snap_shot_bg:SetActive(false)
		end

		self.urpcamera_component:ChangeCameraToBase()
		-- NonsupportScreenShot = true
		-- NonsupportChangeCamera = true
		ViewManager.Instance:AddCanInactiveViewList(self.view_name)
	end
end

-- 恢复主摄像机
function CommonSkillShowView:TryResetCamera()
	self.is_set_view_camera = nil
	-- NonsupportScreenShot = false
	-- NonsupportChangeCamera = false
	local snap_shot_bg = GameObject.Find("GameRoot/UILayer/SnapShotBackground")
	if not IsNil(snap_shot_bg) then
		snap_shot_bg:SetActive(true)
	end

	ViewManager.Instance:RemoveCanInactiveViewList()
end

function CommonSkillShowView:ClearShowCameraTween()
	if self.camera_fade_sequence ~= nil then
		self.camera_fade_sequence:Kill()
		self.camera_fade_sequence = nil
	end
end

-- 摄像机视场恢复为默认值
function CommonSkillShowView:ResetCameraFieldOfView()
	self.cur_camera_field_of_view = default_fight_camera_field
	self.node_list.Camera.camera.fieldOfView = default_fight_camera_field
end

-- 摄像机移动
function CommonSkillShowView:DoShowCameraMove(fov_value, callback)
	fov_value = fov_value < default_fight_camera_field and default_fight_camera_field or fov_value
	if fov_value == self.cur_camera_field_of_view then
		return
	end

	self.cur_camera_field_of_view = fov_value

	-- self.is_camera_moving = true
	self:ClearShowCameraTween()

	local duration = 0.8
	self.camera_fade_sequence = DG.Tweening.DOTween.Sequence()
	self.camera_fade_sequence:Join(self.node_list.Camera.camera:DOFieldOfView(fov_value, duration):SetEase(DG.Tweening
	.Ease.OutCubic))
	self.camera_fade_sequence:OnComplete(function()
		if callback then
			callback()
		end

		-- self.is_camera_moving = false
	end)
end

-- 根据技能配置设置视场 和 模型位置
function CommonSkillShowView:ChangeViewDisplay(skill_id, camera_field)
	local show_cfg = CommonSkillShowData.Instance:GetSkillViewShowParams(skill_id)
	local fight_camera_field = camera_field or default_fight_camera_field
	local shower_pos_x = 0
	local enemy_pos_x = -7
	if show_cfg then
		if show_cfg.camera_field ~= "" then
			fight_camera_field = show_cfg.camera_field
		end

		if show_cfg.shower_pos_x ~= "" then
			shower_pos_x = show_cfg.shower_pos_x
		end

		if show_cfg.enemy_pos_x ~= "" then
			enemy_pos_x = show_cfg.enemy_pos_x
		end
	end

	Transform.SetLocalPosition(self.node_list["shower_pos"].transform, u3dpool.vec3(shower_pos_x, 0, 0))
	Transform.SetLocalPosition(self.node_list["enemy_pos"].transform, u3dpool.vec3(enemy_pos_x, 0, 0))
	self.origin_enemy_pos = self.node_list["enemy_pos"].transform.localPosition
	self.origin_shower_pos = self.node_list["shower_pos"].transform.localPosition
	self:DoShowCameraMove(fight_camera_field)
end

-- 武魂obj
function CommonSkillShowView:GetWuHunObj()
	if not self.shower_obj then
		return
	end

	return self.shower_obj:GetWuHun()
end

-- 武魂obj创建回调
function CommonSkillShowView:WuHunCreateCallBack()
	if not self.shower_obj then
		return
	end

	local wuhun_obj = self.shower_obj:GetWuHun()
	if wuhun_obj then
		local draw_obj = wuhun_obj:GetDrawObj()
		local draw_obj_trans = draw_obj:GetTransfrom()
		draw_obj_trans:SetParent(self.node_list["shower_pos"].gameObject.transform)
		draw_obj_trans.localPosition = u3dpool.vec3(0, 0, 0)
		draw_obj_trans.localRotation = Quaternion.Euler(0, 0, 0)
		draw_obj:SetScale(1, 1, 1)
	end
end

-- 创建驭兽回调
function CommonSkillShowView:BeastCreateCallBack(beast_obj, battle_index)
	if beast_obj then
		local pos = beast_obj:GetFollowTargetPos()
		local draw_obj = beast_obj:GetDrawObj()
		local draw_obj_trans = draw_obj:GetTransfrom()
		draw_obj_trans:SetParent(self.node_list["shower_pos"].gameObject.transform)
		draw_obj_trans.localPosition = u3dpool.vec3(pos.y, 0, pos.x)
		draw_obj_trans.localRotation = Quaternion.Euler(0, 0, 0)
		draw_obj:SetScale(1, 1, 1)
	end
end

function CommonSkillShowView:Update(now_time, elapse_time)
	-- 连线更新
	for k, v in pairs(self.line_effect_cache) do
		self:UpdateLineEffect(k)
	end
end

-- 死亡释放技能
function CommonSkillShowView:CreateDieEffect(buff_info)
	self:CleanDelayDieCDTimer()
	self:CleanDelayReliveCDTimer()

	self.delay_die_timer = GlobalTimerQuest:AddDelayTimer(function()
		if self.enemy_obj then
			self.enemy_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.Die)
		end

		self:EnemyDieDoSkill(buff_info)
	end, 2.5)

	self.delay_relive_timer = GlobalTimerQuest:AddDelayTimer(function()
		if self.enemy_obj then
			self.enemy_obj:ChangeToCommonState()
		end
	end, 6)
end

function CommonSkillShowView:EnemyDieDoSkill(buff_info)
	if not buff_info then return end
	local buff_cfg = SkillWGData.Instance:GetBuffCfgById(buff_info.buff_id)
	if not buff_cfg then return end

	self:SimulateEnemyDoSceneSkillOpera(buff_cfg.param0)
end

-- 模拟木桩怪释放技能操作
function CommonSkillShowView:SimulateEnemyDoSceneSkillOpera(skill_id)
	local bundle, asset, loader_name, alive_time, parent_root, callback
	local skill_data = CommonSkillShowData.Instance:GetCommonSkillData(skill_id, 1)
	if IsEmptyTable(skill_data) then
		return
	end

	parent_root = skill_data.target_type == 0 and self.node_list["enemy_pos"].transform or
	self.node_list["shower_pos"].gameObject.transform
	alive_time = skill_data.alive_time
	bundle = skill_data.effect_bundle
	asset = skill_data.effect_asset
	loader_name = "skill_show_special_loader2"

	if bundle and asset then
		self:AddSpecialSkillEffect(bundle, asset, loader_name, alive_time, parent_root, callback)
	end
end

-- 延迟死亡
function CommonSkillShowView:CleanDelayDieCDTimer()
	if self.delay_die_timer then
		GlobalTimerQuest:CancelQuest(self.delay_die_timer)
		self.delay_die_timer = nil
	end
end

-- 延迟复活
function CommonSkillShowView:CleanDelayReliveCDTimer()
	if self.delay_relive_timer then
		GlobalTimerQuest:CancelQuest(self.delay_relive_timer)
		self.delay_relive_timer = nil
	end
end

-- 每隔几秒定时触发概率buff
-- buff配置里param0=万分比概率 param1=buff_id  param2=1s
function CommonSkillShowView:CreateTimerOutAddEffect(buff_info)
	if not buff_info then return end
	local buff_cfg = SkillWGData.Instance:GetBuffCfgById(buff_info.buff_id)
	if not buff_cfg then return end
	local new_buff_cfg = SkillWGData.Instance:GetBuffCfgById(buff_cfg.param1)
	if not new_buff_cfg then return end

	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	self.enemy_obj:AddBuff(new_buff_cfg.sub_type, nil, cur_time + buff_info.alive_time)
end

-- 创建连线
function CommonSkillShowView:CreateLineEffect(buff_info)
	if not buff_info then
		return
	end

	self:ClearLineEffect()

	local data_index = #self.line_effect_cache + 1

	local bundle, asset
	if buff_info.buff_type == BUFF_TYPE.EBT_ATTR_LINE then
		bundle = "effects2/prefab/dingzhi_prefab"
		asset = "dingzhi_attack06_xian"
	end

	local loader = AllocAsyncLoader(self, "skill_show_line_effect_loader")
	local end_time = buff_info.alive_time
	local delay_time = 0
	loader:SetIsUseObjPool(true)
	loader:SetObjAliveTime(end_time)
	loader:SetParent(self.node_list["war_node"].transform)
	local link_data = {}
	link_data.loader = loader
	link_data.is_finish = false
	link_data.side1_obj = self.shower_obj
	link_data.side2_obj = self.enemy_obj
	self.line_effect_cache[data_index] = link_data

	loader:Load(bundle, asset, function(gameobj)
		local load_info = self.line_effect_cache[data_index]
		if load_info == nil then
			if not IsNil(gameobj) then
				ResPoolMgr:Release(gameobj)
			end
			return
		end

		if (load_info.end_time ~= nil and load_info.end_time <= Status.NowTime)
			or (load_info.side1_obj == nil or load_info.side2_obj == nil) then
			if not IsNil(gameobj) then
				ResPoolMgr:Release(gameobj)
			end

			self.line_effect_cache[data_index] = nil
			return
		end

		local line_render = gameobj:GetComponent(typeof(UnityEngine.LineRenderer))
		if IsNil(line_render) or not self.shower_obj or not self.enemy_obj then
			if not IsNil(gameobj) then
				ResPoolMgr:Release(gameobj)
			end

			self.line_effect_cache[data_index] = nil
			return
		end

		gameobj.transform.localPosition = Vector3(0, 0, 0)
		load_info.obj = gameobj
		load_info.is_finish = true
		load_info.line_render = line_render
		load_info.end_time = Status.NowTime + end_time
		load_info.delay_time = Status.NowTime + delay_time
		gameobj:SetActive(delay_time <= 0)
	end)
end

function CommonSkillShowView:UpdateLineEffect(index)
	if not self.line_effect_cache[index] then
		return
	end

	local link_info = self.line_effect_cache[index]
	if not link_info.is_finish then
		return
	end

	if (link_info.side1_obj == nil or link_info.side2_obj == nil) then
		self:ClearSingleLineEffect(index)
		return
	end

	if link_info.obj == nil or IsNil(link_info.obj) == nil then
		self:ClearSingleLineEffect(index)
		return
	end

	if link_info.delay_time == nil then
		self:ClearSingleLineEffect(index)
		return
	end

	if link_info.delay_time > Status.NowTime then
		return
	end

	if link_info.end_time == nil or link_info.end_time <= Status.NowTime then
		self:ClearSingleLineEffect(index)
		return
	end

	if link_info.line_render == nil or IsNil(link_info.line_render) then
		self:ClearSingleLineEffect(index)
		return
	end

	local side1_draw_obj = link_info.side1_obj:GetDrawObj()
	if side1_draw_obj == nil then
		return
	end

	local side2_draw_obj = link_info.side2_obj:GetDrawObj()
	if side2_draw_obj == nil then
		return
	end

	local pos1
	local point = side1_draw_obj:GetAttachPoint(AttachPoint.BuffMiddle)
	if point then
		pos1 = self.node_list["war_node"].transform:InverseTransformPoint(point.position)
	else
		pos1 = side1_draw_obj:GetRootPosition()
	end

	link_info.obj:SetActive(true)
	local line_renderer = link_info.line_render
	line_renderer:SetPosition(0, Vector3(pos1.x, pos1.y, pos1.z))

	local pos2
	point = side2_draw_obj:GetAttachPoint(AttachPoint.BuffMiddle)
	if point then
		pos2 = self.node_list["war_node"].transform:InverseTransformPoint(point.position)
	else
		pos2 = side2_draw_obj:GetRootPosition()
	end

	line_renderer:SetPosition(1, Vector3(pos2.x, pos2.y, pos2.z))
end

-- 清除连线
function CommonSkillShowView:ClearLineEffect()
	for k, v in pairs(self.line_effect_cache) do
		if v.loader ~= nil then
			v.loader:DeleteMe()
		end
	end

	self.line_effect_cache = {}
end

function CommonSkillShowView:ClearSingleLineEffect(index)
	if not self.line_effect_cache[index] then
		return
	end

	self.line_effect_cache[index].loader:DeleteMe()
	self.line_effect_cache[index] = nil
end

function CommonSkillShowView:KillMoveTweener()
	if self.shower_move_tweener then
		self.shower_move_tweener:Kill()
		self.shower_move_tweener = nil
	end

	if self.delay_reset_shower_pos_timer then
		GlobalTimerQuest:CancelQuest(self.delay_reset_shower_pos_timer)
		self.delay_reset_shower_pos_timer = nil
	end
end

-- 后面再做细分
function CommonSkillShowView:DoShowerSpecialAction()
	if IsEmptyTable(self.show_data) then
		return
	end

	self:KillMoveTweener()
	-- 冲锋到怪前
	local before_skill_cfg = SkillWGData.Instance:GetBeforeSkillCfg(self.show_data.skill_id)
	if before_skill_cfg and before_skill_cfg.skill_type == FRONT_SKILL_TYPE.CHARGE then
		if self.shower_obj and self.enemy_obj then
			local shower_pos_trans = self.node_list["shower_pos"].transform
			local charge_time = 0.5

			self.shower_move_tweener = shower_pos_trans:DOLocalMove(
			Vector3(self.origin_enemy_pos.x + 1, self.origin_enemy_pos.y, self.origin_enemy_pos.z), charge_time)
			self.delay_reset_shower_pos_timer = GlobalTimerQuest:AddDelayTimer(function()
				if self.node_list["shower_pos"] then
					self.node_list["shower_pos"].transform.localPosition = self.origin_shower_pos
				end
			end, charge_time + 1)
		end
	end
end

--===================================================================
local SkillAnchoredPos = {
	-- 正常的 4个技能位置
	{
		-- Vector3(-118, 122, 0),	-- 普攻
		Vector3(-89, -94, 0),
		Vector3(-103, -12, 0),
		Vector3(-67, 62, 0),
		Vector3(6, 101, 0),
	},
	-- 变天神后 3个技能的位置
	{
		-- Vector3(-118, 122, 0),	-- 普攻
		Vector3(-89, -94, 0),
		Vector3(-94, 32, 0),
		Vector3(6, 101, 0),
		Vector3(-67, 62, 0),
	},
}

SkillShowSkillRender = SkillShowSkillRender or BaseClass(BaseRender)
function SkillShowSkillRender:__init()
	self.change_skill_btn_pos = true
end

function SkillShowSkillRender:SetNeedChangeSkillBtnPos(bool)
	self.change_skill_btn_pos = bool
end

function SkillShowSkillRender:OnFlush()
	if not self.data or not self.data.skill_id or self.data.skill_id <= 0 then
		self.view:CustomSetActive(false)
		return
	end

	self.view:CustomSetActive(true)
	local tianshen_client_cfg = SkillWGData.Instance:GetSkillClientConfig(self.data.skill_id, self.data.skill_level)
	if tianshen_client_cfg then
		local bundel, asset = ResPath.GetSkillIconById(tianshen_client_cfg.icon_resource)
		self.node_list.icon.image:LoadSprite(bundel, asset)
	end

	if self.node_list.skill_name and self.data.skill_name then
		self.node_list.skill_name.text.text = self.data.skill_name
	end

	if self.change_skill_btn_pos then
		local skill_pos
		local skill_type = tianshen_client_cfg and tianshen_client_cfg.skill_type
		if skill_type and (skill_type == SKILL_TYPE.SKILL_3 or skill_type == SKILL_TYPE.SKILL_4) then
			skill_pos = SkillAnchoredPos[2][self.index]
		else
			skill_pos = SkillAnchoredPos[1][self.index]
		end

		self:SetAnchoredPosition(skill_pos.x, skill_pos.y)
	end
end

function SkillShowSkillRender:SetSkillBtnCD(time, total_time)
	local num_time = tonumber(time)
	if not num_time or num_time <= 0 or not total_time or total_time == 0 then
		self.node_list.cd_mark:CustomSetActive(false)
		self.node_list.cd_text:CustomSetActive(false)
		return
	end

	self.node_list.cd_mark:CustomSetActive(true)
	self.node_list.cd_text:CustomSetActive(true)
	self.node_list.cd_mark.image.fillAmount = num_time / total_time
	self.node_list.cd_text.text.text = num_time
end
