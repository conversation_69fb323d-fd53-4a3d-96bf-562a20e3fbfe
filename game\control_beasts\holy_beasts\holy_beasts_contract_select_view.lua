HolyBeastsContractSelectView = HolyBeastsContractSelectView or BaseClass(SafeBaseView)

function HolyBeastsContractSelectView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -28), sizeDelta = Vector2(812, 524)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_contract_select")
end

function HolyBeastsContractSelectView:__delete()

end

function HolyBeastsContractSelectView:SetDataAndOpen(holy_beast_data)
	self.holy_beast_data = holy_beast_data
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function HolyBeastsContractSelectView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName16

	if nil == self.select_beasts_list then
		self.select_beasts_list = AsyncBaseGrid.New()
		self.select_beasts_list:CreateCells({
				col = 5, 
				change_cells_num = 1, 
				list_view = self.node_list["contract_beasts_list"],
				assetBundle = "uis/view/control_beasts_ui_prefab",
				assetName = "beasts_contract_item", 
				itemRender = BeastsContractSelectItem
			})
		self.select_beasts_list:SetStartZeroIndex(false)
        self.select_beasts_list:SetSelectCallBack(BindTool.Bind(self.SelectBeastsCallBack, self))
	end

	if not self.holy_beast_item then
		self.holy_beast_item = BeastsContractItem.New(self.node_list["holy_beast_item"])
	end

	if not self.target_beast_item then
		self.target_beast_item = BeastsContractItem.New(self.node_list["target_beast_item"])
	end

	XUI.AddClickEventListener(self.node_list["btn_beasts_contract"], BindTool.Bind(self.OnClickContractBtn, self))
end

function HolyBeastsContractSelectView:CloseCallBack()
	self.holy_beast_data = nil
	self.select_beast_data = nil
	self.select_beast_index = nil

	self.select_beasts_list:CancleAllSelectCell()
end

function HolyBeastsContractSelectView:ReleaseCallBack()
	if self.select_beasts_list then
		self.select_beasts_list:DeleteMe()
		self.select_beasts_list = nil
	end

	if self.holy_beast_item then
		self.holy_beast_item:DeleteMe()
		self.holy_beast_item = nil
	end

	if self.target_beast_item then
		self.target_beast_item:DeleteMe()
		self.target_beast_item = nil
	end

	if self.contract_alert then
		self.contract_alert:DeleteMe()
		self.contract_alert = nil
	end
end

function HolyBeastsContractSelectView:OnFlush(param_t)
	if not self.holy_beast_data then
		return
	end

	self.node_list["txt_select_tip"].tmp.text = Language.ContralBeasts.HolyBeastContractSelectTip
	self.node_list["txt_tips_1"].tmp.text = Language.ContralBeasts.HolyBeastContractTip1
	self.node_list["txt_tips_2"].tmp.text = Language.ContralBeasts.HolyBeastContractTip2

	self.holy_beast_item:SetData(self.holy_beast_data.beast_data)

	local data_list = ControlBeastsWGData.Instance:GetCanContractList(self.holy_beast_data.beast_type)
	local need_jump = false
	if self.holy_beast_data.link_bag_id ~= -1 then
		local cur_linked_beast = ControlBeastsWGData.Instance:GetBeastDataById(self.holy_beast_data.link_bag_id)
		table.insert(data_list, 1, cur_linked_beast)
		need_jump = true
	end
	self.select_beasts_list:SetDataList(data_list)
	if need_jump and #data_list > 0 then
		self.select_beasts_list:JumpToIndexAndSelect(1)
	else
		self:FlushSelect()
	end
end

function HolyBeastsContractSelectView:FlushSelect()
	if not self.holy_beast_data then
		return
	end

	local is_selected = self.select_beast_data ~= nil
	self.target_beast_item:SetVisible(is_selected)
	self.node_list["no_beast_select"]:SetActive(not is_selected)
	XUI.SetButtonEnabled(self.node_list["btn_beasts_contract"], is_selected)
	if self.select_beast_data then
		self.target_beast_item:SetData(self.select_beast_data)
		local is_linked_target = self.select_beast_data.bag_id == self.holy_beast_data.link_bag_id
		local btn_str = is_linked_target and Language.ContralBeasts.UnContract or Language.ContralBeasts.Contract
		self.node_list["btn_text"].tmp.text = btn_str
	else
		self.node_list["btn_text"].tmp.text = Language.ContralBeasts.Contract
	end
end

function HolyBeastsContractSelectView:SelectBeastsCallBack(beast_cell)
	if not beast_cell.data or beast_cell.index == self.select_beast_index then
		return 
	end

	self.select_beast_index = beast_cell.index
	self.select_beast_data = beast_cell.data
	self:FlushSelect()
end

function HolyBeastsContractSelectView:OnClickContractBtn()
	if not self.holy_beast_data or not self.select_beast_data then
		return
	end

	local is_linked = self.select_beast_data.bag_id == self.holy_beast_data.link_bag_id
	if is_linked then
		ControlBeastsWGCtrl.Instance:SendOperateTypeHolyBeastLink(self.holy_beast_data.bag_id - 1, -1)
		self:Close()
	else
		local func = function ()
			ControlBeastsWGCtrl.Instance:SendOperateTypeHolyBeastLink(self.holy_beast_data.bag_id - 1, self.select_beast_data.bag_id - 1)
			self:Close()
		end

		local holy_beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.holy_beast_data.item_id)
		local holy_beast_name = holy_beast_cfg and holy_beast_cfg.beast_name or ""
		local contract_beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.select_beast_data.item_id)
		local contract_beast_name = contract_beast_cfg and contract_beast_cfg.beast_name or ""
	
		local str = string.format(Language.ContralBeasts.GolyBeastContractAlert, holy_beast_name, contract_beast_name)
		if self.select_beast_data.server_data.stand_by_slot ~= -1 then
			str = str .. string.format(Language.ContralBeasts.GolyBeastContractAlert2, holy_beast_name, contract_beast_name)
		end
		if not self.contract_alert then
			self.contract_alert = Alert.New()
		end
		self.contract_alert:SetLableRectWidth(600)
		self.contract_alert:SetLableString(str)
		self.contract_alert:SetOkFunc(func)
		self.contract_alert:Open()
	end
end

-------------------------------------------------
-- 链接幻兽item
-------------------------------------------------
BeastsContractItem = BeastsContractItem or BaseClass(BaseRender)
function BeastsContractItem:LoadCallBack()
	if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
		self.beast_item:SetIsShowTips(false)
	end
end

function BeastsContractItem:ReleaseCallBack()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end
end

function BeastsContractItem:OnFlush()
	if not self.data then
		return
	end
	self.beast_item:SetData(self.data)
	local server_data = self.data.server_data
	if not self.data.is_egg and server_data and (not self.data.is_preview) then
		self.beast_item:SetBeastBattleTypeIcon(server_data.stand_by_slot)
		self.beast_item:SetBeastLevel(server_data.beast_level)
		self.node_list["txt_beast_cap"].tmp.text = self.data.cap_value-- string.format(Language.ContralBeasts.HolyBeastCap, cap)
	end
end

-------------------------------------------------
-- 链接幻兽选择item
-------------------------------------------------
BeastsContractSelectItem = BeastsContractSelectItem or BaseClass(BaseRender)
function BeastsContractSelectItem:LoadCallBack()
	if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
		self.beast_item:SetIsShowTips(false)
		self.beast_item:SetClickCallBack(BindTool.Bind(self.OnClick, self))
	end
end

function BeastsContractSelectItem:ReleaseCallBack()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end
end

function BeastsContractSelectItem:OnFlush()
	if not self.data then
		return
	end
	self.beast_item:SetData(self.data)
	local server_data = self.data.server_data
	if not self.data.is_egg and server_data and (not self.data.is_preview) then
		self.beast_item:SetBeastBattleTypeIcon(server_data.stand_by_slot)
		self.beast_item:SetBeastLevel(server_data.beast_level)
	end

	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.server_data.beast_id)
	self.node_list["txt_beast_name"].tmp.text = beast_cfg and beast_cfg.beast_name or ""
	self.node_list["is_contracted_tag"]:SetActive(server_data.holy_spirit_link_index ~= -1)
end

function BeastsContractSelectItem:OnClick()
	if (not self.data) or (not self.data.server_data) then
		return
	end
	local server_data = self.data.server_data
	if server_data.beast_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip9)
		return
	end
	BaseRender.OnClick(self)
end

-- 设置是否选中
function BeastsContractSelectItem:SetSelect(is_select, item_call_back)
	local bundle, asset = ResPath.GetCommon("a3_ty_xz1")
	self.beast_item:SetSelectSpEffectImageRes(bundle, asset)
	self.beast_item:SetSelectEffectSp(is_select)	
end