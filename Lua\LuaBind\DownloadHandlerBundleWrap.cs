﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class DownloadHandlerBundleWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(DownloadHandlerBundle), typeof(UnityEngine.Networking.DownloadHandlerScript));
		<PERSON><PERSON>unction("OnD<PERSON>roy", OnDestroy);
		<PERSON><PERSON>("SetPriority", SetPriority);
		L<PERSON>unction("New", _CreateDownloadHandlerBundle);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateDownloadHandlerBundle(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				string arg0 = ToLua.CheckString(L, 1);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
				System.Action<bool,int> arg2 = (System.Action<bool,int>)ToLua.CheckDelegate<System.Action<bool,int>>(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				DownloadHandlerBundle obj = new DownloadHandlerBundle(arg0, arg1, arg2, arg3);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 5)
			{
				string arg0 = ToLua.CheckString(L, 1);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
				System.Action<bool,int> arg2 = (System.Action<bool,int>)ToLua.CheckDelegate<System.Action<bool,int>>(L, 3);
				int arg3 = (int)LuaDLL.luaL_checknumber(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				DownloadHandlerBundle obj = new DownloadHandlerBundle(arg0, arg1, arg2, arg3, arg4);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: DownloadHandlerBundle.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDestroy(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			DownloadHandlerBundle obj = (DownloadHandlerBundle)ToLua.CheckObject<DownloadHandlerBundle>(L, 1);
			obj.OnDestroy();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPriority(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			DownloadHandlerBundle obj = (DownloadHandlerBundle)ToLua.CheckObject<DownloadHandlerBundle>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetPriority(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

