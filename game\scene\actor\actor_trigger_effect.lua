local TypeEffectControl = typeof(EffectControl)
local TypeQualityControlActive = typeof(QualityControlActive)

ActorTriggerEffectloaderHandle = ActorTriggerEffectloaderHandle or BaseClass()

function ActorTriggerEffectloaderHandle:__init()
end

function ActorTriggerEffectloaderHandle:__delete()
end

local actor_trigger_effect_loader_handle = ActorTriggerEffectloaderHandle.New()

ActorTriggerEffect = ActorTriggerEffect or BaseClass(ActorTriggerBase)
ActorTriggerEffect._class_type = true

function ActorTriggerEffect:__init()
	self.anima_name = nil
	self.transform = nil
	self.enabled = true
	self.target = nil
	self.is_modeify_mask = false
	self.effect_data = nil
	self.is_use_low_quality = false
	self.ui3d_model = nil
	self.effect_ctrl = nil
end

function ActorTriggerEffect:InitData(anima_name, is_modeify_mask, is_use_low_quality)
	self.anima_name = anima_name
	self.transform = nil
	self.enabled = true
	self.target = nil
	self.is_modeify_mask = is_modeify_mask
	self.effect_data = nil
	self.is_use_low_quality = is_use_low_quality
end

function ActorTriggerEffect:Reset()
	if self.__gameobj_loaders then
		ReleaseGameobjLoaders(self)
	end

	if self.__res_loaders then
		ReleaseResLoaders(self)
	end

	self.effect_data = nil
	self.transform = nil
	self.target = nil
	self.ui3d_model = nil
	self.effect_custom_scale = nil
	self.target_effect_custom_scale = nil
	self.effect_custom_rotation = nil
	self.effect_ctrl = nil
	ActorTriggerBase.Reset(self)
end

function ActorTriggerEffect:__delete()
	self.effect_data = nil
	self.is_modeify_mask = nil
	self.ui3d_model = nil
	self.effect_async_loader = nil
	self.effect_custom_scale = nil
	self.target_effect_custom_scale = nil
	self.effect_custom_rotation = nil
	self.effect_ctrl = nil
end

-- 初始化预制体保存的配置数据(单个)
function ActorTriggerEffect:Init(effect_data)
	self.effect_data = effect_data
	self.delay = effect_data.triggerDelay
end

function ActorTriggerEffect:SetUI3dModel(ui3d_model)
	self.ui3d_model = ui3d_model
end

-- get/set
function ActorTriggerEffect:Enalbed(value)
	if value == nil then
		return self.enabled
	end
	self.enabled = value
end

function ActorTriggerEffect:OnEventTriggered(source, target, stateInfo)
	if self.enabled then
		self:OnEventTriggeredImpl(source, target, stateInfo)
	end
end

local ui3d_layer = UnityEngine.LayerMask.NameToLayer("UI3D")
local default_layer = UnityEngine.LayerMask.NameToLayer("Default")
local TriggerEffCount = 0
local ScaleVector = Vector3(1, 1, 1)

local function EffectLoadCallBack(obj, cbdata)
	local self = cbdata[1]
	local async_loader = cbdata[2]
	local reference = cbdata[3]
	local deliverer = cbdata[4]
	local effect_data = cbdata[5]
	local reference_node = cbdata[6]
	local stateInfo = cbdata[7]
	local effect_count = cbdata[8]
	ActorWGCtrl.ReleaseCBData(cbdata)

	if IsNil(obj) then
		return
	end

	if self.is_modeify_mask then
		if obj then
			obj.gameObject:SetLayerRecursively(ui3d_layer)
		end
	end

	local effect = obj:GetComponent(TypeEffectControl)
	if effect == nil then
		async_loader:Destroy()
		return
	end
	self.effect_ctrl = effect

	local control_active = obj:GetComponent(TypeQualityControlActive)
	if nil ~= control_active then
		-- 其它玩家技能特效强制使用倒数第二档
		if self.is_use_low_quality then
			control_active:SetOverrideLevel(2)
		else
			control_active:ResetOverrideLevel()
		end
	end

	local is_skill_shower = stateInfo ~= nil and stateInfo.is_skill_shower or false
	--reference 释放者
	if not IsNil(reference) and not IsNil(reference_node) then
		if effect_data.isAttach then
			--特效要附着在释放者根节点上
			effect.transform:SetParent(reference)
			if effect_data.isRotation then
				local reference_position = Transform.GetPositionOnce(reference)
				local direction = nil
				if not IsNil(deliverer) then
					local deliverer_position = Transform.GetPositionOnce(deliverer)
					direction = u3dpool.v3Sub(reference_position, deliverer_position)
					direction.y = 0
					if u3dpool.v3Length(direction, false) <= 1e-6 then
						local deliverer_forward = Transform.GetForwardOnce(deliverer)
						direction = u3dpool.v3Sub(u3dpool.v3Mul(deliverer_forward, 1000), deliverer_position)
						direction.y = 0
					end
					effect.transform.position = deliverer_position
				else
					-- deliverer为nil时，使用reference的前方向
					local reference_forward = Transform.GetForwardOnce(reference)
					direction = u3dpool.v3Mul(reference_forward, 1)
					direction.y = 0
					effect.transform.position = reference_position
				end
				effect.transform.rotation = u3dpool.LookRotation(direction)
			else
				effect.transform.localPosition = u3dpool.vec3(0, 0, 0)
				effect.transform.localRotation = Quaternion.identity
			end

			if not effect_data.ignoreParentScale then
				local reference_localScale_val = reference.localScale.x
				local reference_node_localScale_val = reference_node.localScale.x
				local show_scale_val = reference_localScale_val * reference_node_localScale_val
				effect.transform.localScale = Vector3(show_scale_val, show_scale_val, show_scale_val)
			else
				effect.transform.localScale = ScaleVector
			end
		else
			local deliverer_position = nil
			local deliverer_forward = nil
			local reference_position = Transform.GetPositionOnce(reference)
			local reference_forward = Transform.GetForwardOnce(reference)
			local d_pos = reference_position -- 默认使用reference位置
			
			-- 当deliverer不为nil时，使用deliverer的位置和朝向
			if not IsNil(deliverer) then
				deliverer_position = Transform.GetPositionOnce(deliverer)
				deliverer_forward = Transform.GetForwardOnce(deliverer)
				d_pos = deliverer_position
			else
				-- deliverer为nil时，使用reference的位置和朝向
				deliverer_position = reference_position
				deliverer_forward = reference_forward
			end
			
			if effect_data.playerAtTarget then
				if not IsNil(deliverer) then
					effect.transform:SetParent(deliverer)
					effect.transform.position = Transform.GetPositionOnce(deliverer)
				elseif stateInfo and stateInfo.dir_pos then
					effect.transform.position = u3dpool.vec3(stateInfo.dir_pos.x, d_pos.y, stateInfo.dir_pos.z)
				else
					effect.transform.position = u3dpool.v3Add(d_pos, u3dpool.v3Mul(deliverer_forward, 4))
				end
			else
				local pos = deliverer_position
				-- if is_skill_shower then
				-- 	pos = reference_position
				-- end

				if stateInfo and stateInfo.off_vec then
					pos = u3dpool.v3Add(pos, stateInfo.off_vec)
				elseif stateInfo and stateInfo.play_pos then
					pos = stateInfo.play_pos
				end
				effect.transform.position = pos
			end

			if effect_data.playerAtTarget == false and effect_data.isRotation then
				local my_pos = d_pos
				local forward_pos = deliverer_forward
				if is_skill_shower then
					my_pos = reference_position
					forward_pos = reference_forward
				end

				local direction = nil
				direction = u3dpool.v3Sub(u3dpool.v3Add(my_pos, u3dpool.v3Mul(forward_pos, 5)), my_pos)

				if stateInfo ~= nil and stateInfo.no_actor then
					if stateInfo and stateInfo.dir_pos then
						direction = u3dpool.v3Sub(stateInfo.dir_pos, my_pos)
					elseif stateInfo and stateInfo.play_pos then
						direction = u3dpool.v3Sub(stateInfo.play_pos, my_pos)
					end
				else
					if stateInfo and stateInfo.skill_dir then
						direction = u3dpool.v3Sub(stateInfo.skill_dir, my_pos)
					elseif stateInfo and stateInfo.play_pos then
						direction = u3dpool.v3Sub(stateInfo.play_pos, my_pos)
					elseif stateInfo and stateInfo.dir_pos then
						direction = u3dpool.v3Sub(stateInfo.dir_pos, my_pos)
					end
				end

				direction.y = 0
				if direction.x == 0 and direction.z == 0 then
					direction.x = 0.1
				end

				if u3dpool.v3Length(direction, false) <= 1e-6 then
					direction = u3dpool.v3Sub(u3dpool.v3Mul(forward_pos, 1000), my_pos)
					direction.y = 0
				end
				if direction.x == 0 and direction.z == 0 then
					direction.x = 0.1
				end

				local r = u3dpool.LookRotation(direction)
				if r ~= nil then
					effect.transform.rotation = r
				end
			end

			if effect_data.playerAtPos then
				if stateInfo and stateInfo.dir_pos then
					local effect_position = Transform.GetPositionOnce(effect.transform)
					effect.transform.position = u3dpool.vec3(stateInfo.dir_pos.x, effect_position.y, stateInfo.dir_pos.z)
				elseif stateInfo and stateInfo.dir_pos == nil and stateInfo.is_skill_shower and not IsNil(deliverer) then
					effect.transform.position = Transform.GetPositionOnce(deliverer)
				end
			end

			local eff_scale = nil


			if self.effect_custom_scale and not effect_data.playerAtTarget then
				eff_scale = self.effect_custom_scale
			elseif self.target_effect_custom_scale then
				eff_scale = self.target_effect_custom_scale
			else
				local reference_localScale_val = reference.localScale.x
				local reference_node_localScale_val = reference_node.localScale.x
				local show_scale_val = reference_localScale_val * reference_node_localScale_val
				eff_scale = Vector3(show_scale_val, show_scale_val, show_scale_val)
			end

			if not effect_data.ignoreParentScale then
				effect.transform.localScale = eff_scale
			else
				effect.transform.localScale = ScaleVector
			end
		end
	end

	--坐标偏移
	if effect_data.isUseCustomTransform then
		local local_pos = effect.transform.localPosition
		effect.transform.localPosition = u3dpool.vec3(local_pos.x + effect_data.offsetPosX , local_pos.y + effect_data.offsetPosY , local_pos.z + effect_data.offsetPosZ)
	end

	if not effect_data.isAttach and self.effect_custom_rotation then
		effect.transform.localRotation = self.effect_custom_rotation
	end

	local ui3d_model = self.ui3d_model
	if ui3d_model and not IsNil(ui3d_model) then
		ui3d_model:OnAddGameobject(obj)
	end
	effect:Reset()

	local is_modeify_mask = self.is_modeify_mask
	effect:WaitFinsh(function()
		if is_modeify_mask then
			if obj and not IsNil(obj.gameObject) then
				obj.gameObject:SetLayerRecursively(default_layer)
			end
		end

		if ui3d_model and not IsNil(ui3d_model) then
			ui3d_model:OnRemoveGameObject(obj)
		end

		if control_active and not IsNil(control_active) then
			-- 还原技能特效品质
			if self.is_use_low_quality then
				control_active:ResetOverrideLevel()
			end
		end

		async_loader:Destroy()
	end)

	effect.enabled = true
	effect:Play()
end

function ActorTriggerEffect:GetEffectBundleAndAssetName(effect_data, stateInfo)
	local bundle_name, asset_name = nil, nil
	if not effect_data then
		return nil, nil
	end

	if effect_data.effectAsset.BundleName and effect_data.effectAsset.AssetName then
		return effect_data.effectAsset.BundleName, effect_data.effectAsset.AssetName
	end

	if effect_data.effectArray then
		local seq = (stateInfo and stateInfo.awake_skill_level) and stateInfo.awake_skill_level + 1 or 1
		if effect_data.effectArray[seq] then
			bundle_name = effect_data.effectArray[seq].effectAsset.BundleName
			asset_name = effect_data.effectArray[seq].effectAsset.AssetName
		end
	end

	return bundle_name, asset_name
end

function ActorTriggerEffect:OnEventTriggeredImpl(source, target, stateInfo)
	local effect_data = self.effect_data

	local bundle_name, asset_name = self:GetEffectBundleAndAssetName(effect_data, stateInfo)
	if bundle_name == nil or asset_name == nil then
		return
	end

	-- Find the reference node.
	local reference = nil 	--释放者
	local deliverer = nil 	--被攻击目标
	local reference_node = nil
	if effect_data.referenceNodeHierarchyPath ~= nil and effect_data.referenceNodeHierarchyPath ~= "" and source ~= nil then
		reference_node = source.transform:Find(effect_data.referenceNodeHierarchyPath)
		reference = reference_node
	end

	if reference_node == nil and source ~= nil then
		if source.transform.parent ~= nil then
			reference = source.transform.parent.transform
		else
			reference = source.transform
		end
		reference_node = source.transform.transform
	end

	if effect_data.playerAtTarget then
		if target ~= nil then
			deliverer = target.transform
		else
			--技能特效在目标点释放，目标点为空时（后面会处理这种情况下的技能特效位置）
			deliverer = nil
		end
		reference = reference
	else
		deliverer = reference
		if stateInfo ~= nil and stateInfo.is_skill_shower then
			deliverer = target and target.transform or nil
		end
	end

	-- 只有reference为nil时才拦截，允许deliverer为nil
	if reference == nil then
		return
	end

	TriggerEffCount = (TriggerEffCount + 1) % 20

	local async_loader = AllocAsyncLoader(actor_trigger_effect_loader_handle, "actor_trigger_effect" .. TriggerEffCount)
	if async_loader then
		self.effect_async_loader = async_loader
		async_loader:SetIsUseObjPool(true)
		async_loader:SetIsOptimizeEffect(false)
		async_loader:SetParent(G_EffectLayer)
		local time = 5

		if effect_data.triggerFreeDelay and 0 < effect_data.triggerFreeDelay then
			time = effect_data.triggerFreeDelay
		end
		
		async_loader:SetObjAliveTime(time) --防止永久存在
		local cbdata = ActorWGCtrl.GetCBData()
		cbdata[1] = self
		cbdata[2] = async_loader
		cbdata[3] = reference
		cbdata[4] = deliverer
		cbdata[5] = effect_data
		cbdata[6] = reference_node
		cbdata[7] = stateInfo
		cbdata[8] = TriggerEffCount
		async_loader:Load(bundle_name, asset_name, EffectLoadCallBack, cbdata)
	end
end

function ActorTriggerEffect:RemoveEffects()
	local async_loader = self.effect_async_loader
	if not async_loader then
		return
	end

	local obj = async_loader:GetGameObj()
	if obj and not IsNil(obj.gameObject) then
		if self.ui3d_model and not IsNil(self.ui3d_model) then
			self.ui3d_model:OnRemoveGameObject(obj)
		end
	end

	async_loader:Destroy()
end

ActorTriggerEffect.ActorTriggerEffectList = {}
function ActorTriggerEffect.GetActorTriggerEffect()
    local trigger_effect = table.remove(ActorTriggerEffect.ActorTriggerEffectList)
    if nil == trigger_effect then
        trigger_effect = ActorTriggerEffect.New()
    end

    return trigger_effect
end

function ActorTriggerEffect.Release(trigger_effect)
	if #ActorTriggerEffect.ActorTriggerEffectList < 100 then
	    trigger_effect:Reset()
	    table.insert(ActorTriggerEffect.ActorTriggerEffectList, trigger_effect)
	else
		trigger_effect:DeleteMe()
	end
end

function ActorTriggerEffect:SetEffectCustomScale(scale)
	self.effect_custom_scale = scale
end

function ActorTriggerEffect:SetTargetEffectCustomScale(scale)
	self.target_effect_custom_scale = scale
end

function ActorTriggerEffect:SetEffectCustomRotation(rotation)
	self.effect_custom_rotation = rotation
end

function ActorTriggerEffect:StopPlay()
	if not IsNil(self.effect_ctrl) then
		self.effect_ctrl:Stop()
	end
end