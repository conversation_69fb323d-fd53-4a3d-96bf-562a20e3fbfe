KfAllDayMapSceneLogic = KfAllDayMapSceneLogic or BaseClass(CrossServerSceneLogic)

function KfAllDayMapSceneLogic:__init()
end

function KfAllDayMapSceneLogic:__delete()

end

-- 进入场景
function KfAllDayMapSceneLogic:Enter(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenWGCtrl.Instance:OpenTaskFollow()
	-- XuiBaseView.CloseAllView()

end

function KfAllDayMapSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	-- FuBenWGCtrl.Instance:UpdataTaskFollow()
end

function KfAllDayMapSceneLogic:Out()
	CommonFbLogic.Out(self)
	-- UiInstanceMgr.Instance:CloseRewardAction()
	FuBenWGCtrl.Instance:CloseTaskFollow()
end

-- function KfAllDayMapSceneLogic:IsR<PERSON>Enemy(target_obj, main_role)
-- 	return true
-- end