------------------------------------------------------
--引导

--剧情说明：
--1.用||号代表多个演员和对应的动作，用#代表多个参数 用&&代表1个演员具有n个动作
--如actor = "1#monster||2#monster"  action = "born#44#212#1#100||born#33#212#1#100"
--代表分别在指定位置生成id为1和2的怪物类型演员
--actor = "1#monster||2#monster",  action = "move#30#228#200||move#32#228#200",
--代表id为1，2的怪物分别移动到指定位置
--actor = "0#combat", action = "change_blood#999#1#154#1#-200&&change_blood#999#2#154#1#-200",
--2个战斗包。
--动作参数参照动作表
------------------------------------------------------
GuideTriggerType = {
	AcceptTask = 1,									--接受任务后
	CommitTask = 2,									--提交任务后
	LevelUp = 3,									--升级后
	OutFb = 4,										--退出副本后
	Born = 5,										--主角出生时
	Opening = 6,									--开场动画
	ClickUi = 7,									--点击Ui触发
	CanCommintTask = 8,								--达到可提交任务时
	FirstEnterFb = 9,								--第一次进入副本
	ZhuanZhi = 10,									--转职后
	FuBenPanelResult = 11,							--面板打开触发
	BuyItem = 12,									--购买物品
	GuideEnd = 13,									--引导结束
    EnterScene = 14, 								--进入某个场景
    TypeXianJieBoss = 15,                           --仙界boss特殊处理
    OpenSeverDay = 16,								--开服天数功能开启
    TypeTransFer = 17,								--转职功能特殊处理
    KFAndRoleLevelUp = 18,							--跨服等级和角色等级都要达到 trigger_param为角色等级 cross_gs_level为跨服等级
    TypeShenJi = 19,								--神机功能特殊处理
    ChildFunOpen = 20,								--需要子功能其中一个开启才显示
    XiuXianShiLianSkillCanActive = 21, 				--修仙试炼技能可激活的时候触发
    XiuXianClickCapConditionGoBtn = 22, 			--修仙试炼点击战力条件任务的前往按钮触发
    BossNoticeOpen = 24,							--boss提醒
    DayAndSpecialCondition = 25,                    --系统预告
	LevelAndViplevel = 26,                          --人物等级  并且 vip等级达到
	RechargeVal_Day_Level = 27,						--充值数（trigger_param）、天数（trigger_param2）、等级（trigger_param3）
	LandWarFbPersonStageOne = 28,                   -- 阵地战个人副本引导触发
	LandWarFbPersonStageTwo = 29,                   -- 阵地战个人副本引导触发
	LandWarFbPersonStageThree = 30,                 -- 阵地战个人副本引导触发
	LandWarFbPersonStageFour = 31,                  -- 阵地战个人副本引导触发
	MergeFireworksFirstEnter = 32,					-- 合服活动-陨落星辰首次进入触发
	AcceptTask_NoAuto = 33,							-- 接受任务后(非自动)
	OpenViewOnTimes = 34,							-- 当第几次打开界面
	ArtifactViewActive = 35,						-- 激活某个双休引导
}

GuideStepType = {
	Arrow = 1,										--箭头指引
	AutoOpenView = 2,								--自动打开面板	参数 面板名字（参考GuideModuleName）
	AutoCloseView = 3,								--自动关闭面板  参数 面板名字（参考GuideModuleName）
	OpenView = 4,									--打开界面
	DragUi = 5,										--拖动ui			参数 (从uiname1拖到uiname2)
	Introduce = 6,									--美女介绍      参数 无（注：介绍内容放在arrowtip字段,应策划要求）
	GirlGuide = 7,									--美女指引
	FindUi = 8,										--查找ui
	CloseAllView = 9,								--关闭所有界面
	CloseModuleView = 10,							--关闭某模块面板
	SpriteGuide = 11,								--精灵指引
	TextGuide = 12,									--纯文本指引
	WaitCloseModuleView = 13,						--等待面板关闭
	SpriteAnger = 14,								--精灵生气
	SpriteClap = 15,								--精灵拍手
	SpriteExpect = 16,								--精灵期待
	ChangeCamera = 17,								--调整摄像机视角
	MoveTo = 18,								    --移动
	Husong = 19,								    --直接开始护送任务
	SprintMoveTo = 20,								--冲刺移动
	CloseMenu = 21,								    --关闭副屏
	OpenNovicePopDialog = 22,						--打开提示
}

FunOpenType = {
	Visible = 1,									--直接可视
	Fly = 2,										--飞行出现
	TabOpen = 3,									--选项卡开启方式（即标签栏里开始某一项，后面的功能都得自适应）
	FlyTabOpen = 4,									--飞行出现 + 选项卡开启方式 open_param为：标签索引#所在模块#主属主功能
	OpenView = 5,									--弹出面板
	Born = 6,										--生长（如用于场景上对象的出现）
	FlyToUI = 7,									--飞行出现,指定目标ui
	OpenModel = 8,									--弹出模型
	OpenTipView = 9,								--弹出界面（但是不飞行）
}

--功能所在的位置 1:右上, 2:左下, 3:其他
FunWithType = {
	Up = 1,
	Down = 2,
	Other = 3,
}

--模块名字,可用于引导中指定所属大模块，也可用来作为打开模块的名字
GuideModuleName = {
	--G21 功能引导用到的 --------------------------------------------------------
	SceneLoading           = "scene_loading",					-- 加载
	MainUIView             = "main_view",						--主界面
	Bag                    = "bag_view",						--角色背包
	RoleBagMeltingEnterView = "RoleBagMeltingEnterView",		-- 熔炼入口
	RoleBagViewMeltingView = "rolebag_view_metling_view",		--背包一键装备熔炼
	OpenServerMenu         = "open_server_menu",				--开服活动菜单
	TianShuView            = "tian_shu_view",					--天书界面
	TaskDialog             = "TaskDialog",						-- 任务对话面板
	ReconnectView          = "ReconnectView",					--断线重连
	SkillView              = "skill_view",                      --技能
	skill_zhudong		   = "skill_zhudong",					-- 主动技能
	skill_beidong		   = "skill_beidong",					-- 被动技能
	skill_talent		   = "skill_talent",					-- 天赋技能
	SkillReset             = "skill_reset",                     --技能重置
	RoleView               = "role_view",						--角色
	RoleBranchView 		   = "RoleBranchView",					-- 角色-万象
	SkyCurtain		   	   = "SkyCurtain",						--天幕（原奇境）
	ItemTip                = "item_tip",						--物品tips
	KeyEquipment           = "key_equipment",					--一键装备
	Equipment              = "equipment",						--装备
	EquipOld               = "equip_old",						--原装备
	ShenEquip              = "shen_equip",						--神装
	Mount                  = "mount",							--坐骑
	ShengWang              = "shengwang",						--声望
	Appearance             = "appearance",						--外观
	WZring                 = "wangzhering",						--王者之戒
	Wing                   = "wing",							--羽翼
	Guild                  = "guild",							--仙盟
	GuildPass              = "guild_pass",					    --仙盟传功
	Exchange               = "exchange",						--兑换
	Society                = "society",							--社交
	SocietySendGoods       = "SocietySendGoods",     		    --社交赠送物品
	ShenShouStrengthenView   = "ShenShouStrengthenView", 			--生肖背包
	ShouhunBag             = "ShouhunBag",						--兽魂背包
	ShenShouQiangHua       = "ShenShouQiangHua",				--神兵强化
	CombineSociety         = "combine_society",					--综合社交
	MainCommonUnfold       = "main_common_unfold",				--主界面中的综合展开后
	Husong                 = "husong",							--护送
	Flower                 = "Flower",							--送花
	XunBao                 = "xunBao",							--寻宝  旧的
	XunBaoRecord           = "xunbao_record",					--寻宝记录 旧的
	XunBaoBag              = "xunbao_bag",						--寻宝背包 旧的
    XunBaoGaiLv            = "xunBao_gailv",                    -- 旧的

    TreasureHunt           = "TreasureHunt",					--寻宝  新的
	TreasureHuntRecord     = "treasurehunt_record",				--寻宝记录 新的
	TreasureHuntBag        = "treasurehunt_bag",				--寻宝背包 新的
	TreasureHuntStoreView  = "TreasureHuntStoreView",			--新寻宝积分商城 新的
	TreasureXuKongLieFengView = "TreasureXuKongLieFengView",    -- 虚空裂缝

	Alert = "Alert",                               				-- 通用弹窗
	ConfirmAlert = "ConfirmAlert",                              -- 通用弹窗
	NumKeypad = "NumKeypad",                                    -- 数字键盘

	CommonWindowSkillShowView = "CommonWindowSkillShowView",	-- 技能窗口展示
	SiXiangCallView			= "SiXiangCallView",				-- 四象召唤
	SiXiangYuGaoView		= "SiXiangYuGaoView",				-- 四象预告
	SiXiangCallRecord       = "SiXiangCallRecord",				-- 四象召唤记录界面
	SiXiangExchange         = "SiXiangExchange",			    -- 四象召唤兑换界面
	SiXiangReward         	= "SiXiangReward",			    	-- 四象召唤奖励界面
	SiXiangRewardPond		= "SiXiangRewardPond",				-- 四象召唤奖池界面
	SiXiangProUp			= "SiXiang_ProbabilityUpView",		-- 四象特典概率提升
	SiXiangShiLian			= "SiXiang_ShiLianView",			-- 四象十连
	SiXiangZhenXuan 		= "SiXiang_ZhenXuanView",			-- 四象甄选
	SiXiangTeHui			= "SiXiang_TeHuiZhaoHuanView",		-- 四象特惠
	XianQiProUp				= "XianQi_ProbabilityUpView",		-- 仙器特典概率提升
	XianQiShiLian			= "XianQi_ShiLianView",				-- 仙器十连
	XianQiZhenXuan 			= "XianQi_ZhenXuanView",			-- 仙器甄选
	XianQiTeHui				= "XianQi_TeHuiZhaoHuanView",		-- 仙器特惠
	XianQiReward         	= "XianQiReward",			    	-- 仙器奖励界面

	CapabilityContrastView = "CapabilityContrastView",			-- 战力对比
	VersionsAdvanceNoticeView = "VersionsAdvanceNoticeView",	-- 版本预告
	TianShenSkillShowView  = "TianShenSkillShowView",			-- 天神技能展示
	BossMaBiSkillShowView = "BossMaBiSkillShowView",            -- boss麻痹技能展示
	FightMountSkillShowView = "FightMountSkillShowView",		-- 战斗坐骑技能展示
	WuHunSkillShowView = "WuHunSkillShowView",					-- 武魂技能展示
	CustomizedSkillShowView = "CustomizedSkillShowView",		-- 定制技能展示
	TianShenHeJiSkillShowView = "TianShenHeJiSkillShowView",	-- 天神合击技能展示
	BeastGroupSkillShowView = "BeastGroupSkillShowView",		-- 驭兽合击技能展示
	SupremeFieldsSkillShowView = "SupremeFieldsSkillShowView",	-- 独尊领域技能展示
	SkillBreakPurchaseShowView = "SkillBreakPurchaseShowView",	-- 技能突破直购技能展示
	SpiritSkillShowView = "SpiritSkillShowView",				-- 修为变身技能展示

	BossPrivilegeView 		= "BossPrivilegeView",				--激活Boss再爆一次特权
	BossNewPrivilegeView 	= "BossNewPrivilegeView",			--Boss特权，再爆一次特权、boss麻痹、Boss自动寻找特权
	BossGodWarView 			= "BossGodWarView",					--Boss自动寻找特权
	BossZhanLingView 		= "BossZhanLingView",				--Boss战令
	WardrobeView			= "WardrobeView",					-- 衣橱
	HmGodView               = "HmGodView",                      -- 鸿蒙神藏
	GuiXuDreamView          = "GuiXuDreamView",                 -- 归墟梦演
	HolyDarkWeaponEnter		= "HolyDarkWeaponEnter",			-- 圣器暗器入口
	HolyWeapon              = "HolyWeapon",                     -- 圣器
	DarkWeapon              = "DarkWeapon",                     -- 暗器
	ArtifactView            = "ArtifactView",                   -- 双修(仙魔神器)
	ArtifactSelectView		= "ArtifactSelectView",				-- 双修（选择出战）
	--ArtifactAffectionView	= "ArtifactAffectionView",			-- 双修-好感度
	ArtifactTravelView		= "ArtifactTravelView",				-- 双修-同游
	--ArtifactBattleView      = "ArtifactBattleView",             -- 双修出战
	DragonTempleView        = "DragonTempleView",               --龙神殿
	DragonTempleUpgradeView = "DragonTempleUpgradeView",        --龙神殿秒杀
	DragonTempleLevelBuyView = "DragonTempleLevelBuyView",        --龙神秒杀
	DragonTemplePrivilegeView = "DragonTemplePrivilegeView",    --御龙高手
	DragonTempleWildBuyView = "DragonTempleWildBuyView",        -- 龙战于野
	HuanHuaFetterView       = "HuanHuaFetterView",              -- 幻化羁绊
	NewHuanHuaFetterView   = "NewHuanHuaFetterView",               -- 幻化羁绊   
	EquipTargetView        = "rolebag_Target",             		--装备目标
	EquipTargetTipsView    = "EquipTargetTipsView",             --装备目标Tips
	EquipmentBagResloveView = "EquipmentBagResloveView",        -- 装备目标分解
	EquipTargetComposeView = "EquipTargetComposeView",        -- 装备收集 合成说明
	YinianMagicView         = "YinianMagicView",                -- 一念神魔

	WenGuaPre              = "WenGuaPre",						--问卦极品预览
	WenGuaRecord           = "WenGuaRecord",					--问卦记录
	Task                   = "task",							--任务(用于链接里配置会寻路过去)
	TaskShangJinView	   = "TaskShangJinView",				--赏金任务
	NPC                    = "npc",								--npc(用于链接里配置会寻路过去)
	TaskDice			   = "taskdice",						--骰子任务
	GuajiMap               = "guajimap",						--挂机（用于链接里配置会飞到计算得到的挂机地图）
	Marry                  = "marry",							--结婚
	MarryPropose           = "marry_propose",					--求婚小窗口
	MarryProposeBack       = "marry_proposeback",				--被求婚小窗口
	MarryGetNew            = "marry_getnew",				        --被求婚小窗口
	MarryFriend            = "marry_friend",					--邀请异性好友加入副本
	MarryNearbySingle      = "marry_nearbysingle",				--附近异性玩家
	MarryExplain           = "marry_explain",					--婚宴流程
	MarryXunYuan           = "marry_xunyuan",					--寻缘界面
	MarryXunYuanFaBu       = "marry_xunyuan_fabu",				--寻缘发布界面
	MarryTiQin             = "marry_tiqin",						--提亲界面
	MarryYuYue             = "marry_yuyue",						--婚宴预约
	MarryPaiQi             = "marry_paiqi",						--婚宴排期
	MarryBeiYao             = "marry_beiyao",						--婚宴被邀
	MarryInvite            = "marry_invite",					--婚宴邀请
	MarryDanMu             = "marry_danmu",						--婚宴弹幕
	MarryHunYanTask        = "marry_hunyan_task",				--婚宴任务栏
	MarryIntimacy          = "marry_intimacy",					--婚宴亲密度小界面
	MarryFbTaskView        = "marry_fb_task",					--副本内任务栏
	MarryFbChoose          = "marry_fb_choose",					--副本结束选择材料界面
	MarryTitleView         = "marry_title_view",				--结婚称号显示界面
	MarryFB                = "marryfb",							--结婚副本
	MarryBaoXiaView        = "MarryBaoXiaView",                 -- 结婚宝匣
	GuradInvalidTimeView   = "GuradInvalidTimeView", 			--守护过期界面
	GuradShwoView          = "GuradShwoView",  					--小鬼显示界面
	GuardComposeView       = "GuardComposeView",  				--小鬼合成界面
	ZhanShenDian           = "zhanshendian",					--战神殿
	ActIvityHall           = "activity_hall",					--活动大厅
	ActIvityHallMinPanel   = "activity_hall_min_panel",			--活动大厅
	ActivityMainPanel      = "activity_main_panel",      		--所有活动的面板
	CanJiaHunYan           = "canjiahunyan",					--参加婚宴(副本)
	ShenJiang              = "shenjiang",						--神将
	JingLing               = "jingling",						--精灵
	ZhuanSheng             = "zhuansheng",						--转生
	GodAndDemons           = "GodAndDemons",					--转生 神魔
	GodAndDemonsTips       = "GodAndDemonsTips",				--转生 神魔 确认弹窗
	TransferSuccess        = "TransferSuccess",                 --转职成功
	Boss                   = "boss",							--boss
	BossFake               = "boss_fake",						--假boss
	BossAssist             = "boss_assist",						--boss协助
    Boss_ShengYu_FollowUi  = "boss_shengyu_follow_ui",
    OAFishRecord           = "OAFishRecord",					--锦鲤记录
    OAFishFanLiView        = "OAFishFanLiView",					--锦鲤奖励
	OATipsGetRewardView    = "OATipsGetRewardView",				--锦鲤奖励
	BossAssistHelp  	   = "BossAssistHelp",					--boss协助者tip
	BossHMSYTip			   = "BossHMSYTip",						--鸿蒙神域Tip
	BossDropRecord		   = "BossDropRecord",					--鸿蒙神域掉落记录
	BossKillRecordView	   = "BossKillRecordView",			    --boss击杀记录
	BossRewardThsGivenView = "BossRewardThsGivenView",			--boss击杀获得掉落感谢弹窗
	QuickUse		   	   = "QuickUse",						--boss重生道具快速使用
	Drugstore              = "drugstore",						--药店
	Storage                = "storage",							--仓库
	Fabao                  = "fabao",							--法宝
	PaTaFb                 = "pata_fb",							--通天塔
    MysteriousShop         = "mysterious_shop",					--神秘商店
    FortuneCatView         = "FortuneCatView",                  --招财猫
    RebateActivityView     = "RebateActivityView",              --返利活动
	Meridian               = "meridian",						--修炼
	Master                 = "master",							--师徒
	TeJie                  = "tejie",							--特戒
	FuBenPanel             = "fubenpanel",						--副本
	FuBenPanelBuyCountInfo = "FuBenPanelBuyCountInfo",			--副本购买次数信息面板
	BiZuo                  = "bizuo",							--每日必做（日常）
	QuintupleExp           = "quintuple_exp",					--五倍经验
	MiJing                 = "mijing",							--秘境
	Welkin                 = "welkin",							--天仙阁
	Wujinjitan             = "wujinjitan",						--无尽祭坛
	RechargeVipTyView      = "rechargeviptyview",				--VIP体验卡
	FirstRechargeTips      = "firstrechargetips",				--首充浮窗
	SecondRechargeTips      = "secondrechargetips",				--二充浮窗
	VipFourBuyTips      	= "vipfourbuytips",					--V4浮窗
	TitleAddView           = "titleaddview",					--新称号展示界面
	ActTitleAddView        = "acttitleaddview",					--开宗立派新称号展示界面
	GuildBossTaskView       = "guildbosstaskview",				--仙盟boss
	GuildBossTipsView       = "guildbosstipsview",				--仙盟boss提示
	GuildBossRewardView       = "GuildBossRewardView",				--仙盟boss骰子奖励
	NormalGuideView        = "normal_guide_view",				--功能引导
	GuideDisableClickView        = "guide_disable_click_view",	--引导拦截点击界面
	CameraBubbleView       	= "CameraBubbleView",				--相机气泡框
	FieldHeadPanel			= "FieldHeadPanel",					--竞技1v1
	FieldFinish				= "FieldFinish",					--竞技结算

	YinPiaoExchangeView = "YinPiaoExchangeView",		-- 银票兑换
	TestView             = "TestView",				-- 测试面板
	ChatView             = "ChatView", 				--聊天面板
	Agent                = "AgentView",				-- 登陆SDK
	Login                = "LoginView",				-- 登录
	LoginShowRoleAppearanceView = "LoginShowRoleAppearanceView", -- 时装展示
	select_server	 	 = "LoginSelectServer",		-- 选择服务器
	LoginNoticView		 = "LoginNoticView",		-- 登录公告
	MainPlayer           = "MainPlayer",
	GuildView            = "GuildView",				-- 公会
	SelectCameraModeView = "SelectCameraModeView",  -- 选择摄像机模式界面
	StoryView            = "StoryView",				-- 剧情
	StoryEntranceView    = "StoryEntranceView",		-- 剧情入口面板
	LineView             = "LineView",				-- 分线面板
	TipsDisconnectedView = "TipsDisconnectedView",	--断线重连

	PierreDirectPurchaseView = "PierreDirectPurchaseView",		-- 臻品直购
	DIYDrawOneView = "DIYDrawOneView",						-- DIY1抽奖
	DIYDrawTwoView = "DIYDrawTwoView",						-- DIY2抽奖

    CrossTeamView            = "CrossTeamView",					-- 跨服组队面板
	NewTeamView              = "NewTeamView",					-- 组队面板
	NewTeamInviteView        = "NewTeamInviteView",				-- 组队邀请面板
	NewTeamQuickView         = "NewTeamQuickView",				-- 快速组队面板
	NewTeamPrepareView       = "NewTeamPrepareView",			-- 组队进入副本
	NewTeamBaoMingEnterView  = "NewTeamBaoMingEnterView", 		-- 组队报名进入界面
	TeamPrepareView   		 = "TeamPrepareView", 				-- 组队准备进入界面
	TeamMatchView 			 = "TeamMatchView", 				-- 匹配计时界面
	QuickInviteTeamView		 = "QuickInviteTeamView",			-- 快速加入队伍弹窗
	QuickInviteMatch		 = "QuickInviteMatch",			    -- 快速匹配弹窗

	EquipmentView            = "EquipmentView",					--锻造
	EquipmentMarkView        = "EquipmentMarkView",             --装备印记
	EquipmentShenPinAttrTip  = "equipment_shenpinattr",			--锻造-升品弹出
	EquipmentSelectBaoShi    = "equipment_select_baoshi",		--选中宝石
	EquipmentSelectYinJi     = "equipment_select_yinji",		--选中印记
	EquipmentSelectLingYu    = "equipment_select_lingyu",		--选中灵玉
	EquipmentLingYuUpgradeView    = "EquipmentLingYuUpgradeView",		--灵玉升级
	EquipmentUpLevelBagView  = "equipment_uplevel_bag_view",
	BaptizeAdditonView		 = "equipment_baptize_additon_view",
	EquipmentRongLianTipView = "equipment_ronglian_tip_view",
	EquipmentSuitTipView     = "equipment_suit_tip_view",
	FuBenPanelView           = "FuBenPanelView",				--副本
	SecretRecordView         = "SecretRecordView",    		    -- 紫云秘录
	ExpEfficiencyView        = 'ExpEfficiencyView',    		    -- 经验效率
	SpecialKeypadView        = "SpecialKeypadView", 			--特殊数字键盘
	FuWenView                = "FuWenView", 					--铭纹
	FuWenZongLanView         = "FuWenZongLanView", 				--铭纹总览
	FuWenZongLanAttrView     = "FuWenZongLanAttrView", 			--铭纹属性总览
	FuWenResultView          = "FuWenResultView", 				--铭纹寻宝结果(\开箱结果)
	FuWenBagView             = "FuWenBagView", 					--铭纹背包(全部)
	FuWenEquipBagView        = "FuWenEquipBagView",  			--铭纹装备背包
	ZhenYanOpenView          = "ZhenYanOpenView",  			--铭纹装备背包
	EquipmentSuitZongLanView = "EquipmentSuitZongLanView",		--套装属性总览

	RebateReel               = "rebate_reel",					--返利卷轴
	FubenReel                = "fuben_reel",					--副本卷轴
	FuncReel                 = "func_reel",						--功能卷轴
	ActOpenServerView        = "open_server_activity",			-- 开服活动
	ActOpenServertwoView     = "open_server_compete",			-- 开服比拼
	PerfertQingren           = "PerfertQingren", 				-- 完美情人
	ServerActivityTabView 	 = "serveractivity_tab_view",		--F2开服活动界面
	XianMengZhengBaView		 = "xianmeng_zhengba_view",			--仙盟争霸弹窗
	KfActivityView 			 = "kf_activity_view",				--F2第二个开服活动界面
	KfRewardActivityView 	 = "kf_activity_reward_view",		--开服活动奖励弹窗
	JingYanYu                = "jingyanyu",						--经验玉
	NoticeTwo                = "notice_two",					--功能预告2
	NoticeThree              = "notice_three",					--功能预告3
	Award                    = "award",							--活跃度奖励
	HunYan                   = "hunyan",						--婚宴
	Setting                  = "setting",						--设置
	Rank                     = "rank",							--排行
	Market                   = "market",						--集市

	DuckRace 				 = "duck_race", 					--小鸭疾走
	DuckRaceFight 			 = "duck_race_fight", 				--小鸭疾走场景面板
	DuckDialog 				 = "duck_dialog", 					--小鸭对话
	DuckRaceResultsVoteView  = "DuckRaceResultsVoteView", 		--轮次结束后的投票面板
	BarrageView 			 = "barrage_view", 					--弹幕面板

	Vip                      = "vip",							--vip
	VipTip                   = "vip_tip",						--vip_tip
	VipTipXuFei              = "vip_tip_xufei",                 --vip续费
	VipShowWordView          = 'VipShowWordView',
	VipTiYanView        	  = 'VipTiYanView',
	VipActiveView        	  = 'VipActiveView',
	VipUpView        	      = 'VipUpView',
	VipInitialView           =   "VipInitialView",

	ChangeRolesView				= "ChangeRolesView",			-- 换角

	Shop                     = "shop",							--市场
	PrivilegeShop            = "privilegeshop",					--特权市场
	Jinyinta                 = "jinyinta",						--金银塔
	Chongzhiniuegg           = "chongzhiniuegg",				--充值扭蛋
	HightRebate              = "hight_rebate",                  --高倍返利
	Huanlezadan              = "huanlezadan",
	HuanlezadanRank          = "huanlezadan_rank",
	Daily                    = "daily",							--日常
	DayActivity              = "day_activity",					--新活动
	Activity                 = "activity",						--活动
	OpenActivity             = "open_activity",					--开服活动(菜单按钮)
	OpenServer               = "open_server",				     --开服活动
	FanliActivity            = "fl_activity",					--返利活动
	CombineActivity          = "combine_activity",				--合服活动
	FcActivity               = "fc_activity",					--封测活动
	BanBenActivity           = "banben_activity",				--版本活动
	FirstCharge              = "first_recharge",				--首充
	UpdateAffiche            = "updateaffiche",					--更新公告
	LingYaoMaiHuo			 = "lingyaomaihuo",					--灵妖卖货
	TeamExpFBMedicineView 	 = "TeamExpFBMedicineView",			--经验本经验效率
	FBNewPlayerView 		 = "FBNewPlayerView",				--新手副本窗口

	Welfare             = "welfare",							--福利
	SevenDay             = "sevenday",							--十五天登录
	WorldRedPaper       = "worldredpaper",                      --世界红包
	ActMaze             = "act_maze",							--迷宫寻宝
	Want                = "want",								--小助手（我要。。）
	ContinueLevelup     = "continuelevelup",					--继续升级界面
	Chat                = "chat",								--聊天
	CommonActivity      = "common_activity",					--通用活动界面
	ActJjc              = "act_jjc",							--斗法封神
	ActJjc_yingxiong    = "act_jjc_yingxiong",					--英雄榜
	ActJjc_yulan_reward = "act_jjc_yulan_reward",				--英雄榜奖励预览
	KeyAddFriend        = "keyaddfriend",						--一键添加好友面板
	Recharge            = "recharge",							--充值界面
	Dati                = "act_answer",							--Dati
	ManyTowerFB         = "manytowerfb",						--多人副本（多人塔防)
	LuckRoller          = "luck_roller",						--转盘
	MonthCard           = "month_card",
	Carnival            = "carnival",							--七日狂欢
	GoldReversion       = "gold_reversion",						--百倍返利
	XianMengZhan        = "xian_meng_zhan",						--仙盟战
	SuperGift           = "super_gift",							--消费引导
	Map                 = "map",								--地图界面
	RoleChangePro       = "role_change_prof",					--转职业界面
	RechargeAgain       = "recharge_again",						--再充
	RechargeThird       = "recharge_third",						--三充
	DailyRecharge       = "daily_recharge",						--每日累充
	DailyConsume        = "daily_consume",						--每日累消
	DailyTotalRecharge  = "daily_total_recharge",				--累充
	ActivityBipin       = "activity_bipin",						--比拼活动
	InvestPlan          = "invest_plan",						--投资计划
	AdvancedSys         = "advanced_sys",						--进阶系统
	AdvancedRank        = "advanced_rank",						--进阶排行榜
	TransferProfPerson  = "transfer_prof_person",				--转职任务副本
	TransferProfTeam    = "transfer_prof_team",					--转职组队副本
	GongChengShop       = "gong_cheng_shop",					--攻城战商店界面
	AgentPromote        = "agent_promote",						--平台推广
	SuperVip            = "super_vip",							--至尊VIP
	JingHua             = "jinghua",							-- 精华
	ChongzhiLottery     = "chongzhiLottery",					--充值扭蛋
	YaoShouPlaza        = "yaoshouplaza",						--妖兽广场
	LongMai             = "longmai",							-- 龙脉
	ZhuZaiShenDian      ="zhuzaishendianview",						--主宰神殿
	QIFU                = "qifu",								--祈福
	JingJie             = "jingjie",							--境界
	------------------------------------------------------------
	PigTreasure          = "pig_treasure",					--金猪探宝
	TreasureLoft         = "treasure_loft",					--珍宝阁
	LingYuanHaoLi        = "LingYuanHaoLi", 				--0元豪礼
	TimeLimitFeedback    = "timeLimitFeedback",				--限时回馈
	MiJingTaoBao         = "mijingtaobao",					--秘境淘宝
	MiJingTaoBaoTwo      = "mijingtaobaotwo",				--秘境淘宝2
	PleaseDrawCard       = "please_draw_card",				--陛下请翻牌
	CollectBless         = "collect_bless",					--翻牌集福
	PerfectLover         = "perfect_lover",					--完美情人
	LotteryTree          = "lottery_tree",					--摇钱树
	LotteryTreeTwo       = "lottery_tree_two",				--摇钱树2
	SingleRechargeOne    = "single_recharge_one",			--我要充值1,现改成 冲战新星1
	SingleRechargeTwo    = "single_recharge_two",			--我要充值2,现改成 我要冲战2
	SingleRechargeThree  = "single_recharge_three",			--我要充值3,现改成 急速冲战3
	SingleRechargeFour   = "single_recharge_four",			--我要充值4,现改成 战榜提名4
	ConsumerScore        = "consumer_score",				--消费积分
	New_Three_Suit       = "new_three_suit",         	    --新三件套
	Single_Recharge      = "single_recharge",               --新单笔充值
	Mine                 = "mine",							--翡翠矿场
	MoLong               = "molong",						--龙行天下
	ContinuouslyRecharge = "continuously_recharge",			--正版连续充值2086
	RebateRecharge       = "rebate_recharge",				--充值返利
	TOTAL_CHONGZHI       = "total_chongzhi",				--累计充值
	TOTAL_CONSUME        = "total_consume",					--累计消费
	-- Rush_Recharge_One = "RushRechargeone",			--冲战新星
	-- Rush_Recharge_Two = "RushRechargetwo",	   		--我要冲战
	-- Rush_Recharge_Three = "RushRechargethree",		--急速冲战
	-- Rush_Recharge_Four = "RushRechargefour",			--战榜提名

	FunnyShoot                        = "funny_shoot",				-- 趣味射门
	DailyLove                         = "daily_love",				-- 每日一爱
	SingleRebate                      = "single_rebate",			-- 单笔返利
	SuperTransmit                     = "super_transmit",			-- 超级传送
	RandActivityHall                  = "rand_activityhall",		-- 活动收缩
	FriendsBlessing                   = "friends_blessing",			-- 好友祝福
	FriendsReturn                     = "friends_return",			-- 好友回赠
	Blessing                          = "blessing",					-- 祝福
	FragmentExchange                  = "fragment_exchange",		-- 碎片兑换
	ConsumeDiscount                   = "consume_discount",			-- 连消特惠
	PlantingTree                      = "planting_tree",			-- 植树
	Fanfanzhuan                       = "fanfanzhuan",				-- 翻翻转
	Fishpond                          = "fishpond",					-- 灵池
	RepeatRecharge                    = "repeat_recharge",			-- 循环消费
	SpeedRecharge                     = "speed_recharge",			-- 至尊充值排行
	RechargeReturnReward              = "recharge_return_reward",	-- 充值狂返元宝
	RunningMan                        = "running_man",				-- 奔跑吧仙友
	KuafuRecharge                     = "kuafu_recharge",			-- 跨服排行榜
	KuafuConsume                      = "kuafu_consume",			-- 跨服消费榜
	WeGetMarried                      = "wegetmarried",				-- 咱们结婚吧
	MammonBless                       = "mammonbless",				-- 财神赐福
	KuafuOneVsN                       = "kuafuonevsn",				-- 跨服1vn
	KuafuHonorhalls                   = "kuafuhonorhalls",			-- 跨服修罗塔
	KuafuYeZhanWangCheng              = "kuafuYeZhanWangCheng",  	-- 夜战王城
	MagicUtensil                      = "magic_utensil",			-- 魔器
	TeShuFace                         = "teshu_face",				-- 特殊表情
	CangBaoGe                         = "cangbaoge",				-- 藏宝阁
	TeamFb                            = "newteamfb",				-- 跨服组队本
	GongCheng                         = "gongcheng",				-- 攻城战
	KuafuOneVsOne                     = "kuafuonevsone",			-- 跨服1v1
	KuafuPVP                          = "kuafupvp",					-- 跨服pvp
	Answer                            = "answer",                   -- 答题
	SevendayInvestplan                = "sevenday_investplan",		-- 七天投资计划
	TianJi                            = "tianji",					-- 天机
	GoldMember                        = "gold_member",				-- 黄金会员
	Immortal                          = "Immortal",					-- 仙尊卡
	MysteryHouse                      = "mystery_house",			-- 神秘小屋
	TreasureMap                       = "treasure_map",				-- 藏宝图
	MingHun                           = "ming_hun",					-- 命魂
	FantasyFashion                    = "fantasy_fashion",			-- 幻装商店
	MysteryMerchant                   = "mystery_merchant",			-- 神秘商人
	ChatWinView                       = "chat_win_view",			-- 个性聊天
	KuafuGuildBattle                  = "kuafu_guild_battle",		-- 跨服帮派战
	WorldServer                       = "WorldServer",							--世界服

	PositionalWarfareZhanLingView	  = "PositionalWarfareZhanLingView",	-- 阵地战 - 论剑战令

	ShanHaiJingView					  = "ShanHaiJingView",						-- 万图谱(原山海经)
	ShanHaiJingZuHeView				  = "ShanHaiJingZuHeView",						-- 万图谱(原山海经)
	SHJFenjieView				      = "SHJFenjieView",						-- 山海经
	SHJAttrView				     	  = "SHJAttrView",						-- 山海经
	SHJSkillTipView				      = "SHJSkillTipView",						-- 山海经
	ShanHaiJingLSCView				  = "ShanHaiJingLSCView",					-- 山海经-千秋绝艳图

	MarryParty                        = "marry_party",         	 			    -- 婚宴
	WeddingDeMand                     = "wedding_demand",          			 	-- 索要请柬
	InfiniteHell                      = "infinite_hell",          				-- 无尽神狱
	FuWen                             = "fuwen",								-- 铭纹
	ShenShou                          = "shenshou",                 	 	    -- 神兽
	Achievement                       = "achievement",              	    	-- 成就
	ShenShouBianShenAppearance        = "shenshou_bianshen_appearanceview",		-- 变身幻装界面
	BianShenSuitTip                   = "bianshen_suit_tip",					-- 变身套装tips
	TransFerSkillTip                  = "transfer_skill_tip",     				-- 转职技能
	TransFerDemon                     = "transfer_demon",     					-- 转职技能
	NewTeam                           = "new_team",                        		-- 组队
	Fishing                           = "fishing",								-- 钓鱼
	ChangeHeadView                    = "change_head_view",						-- 换头像界面
	TitleBackGroundView               = "title_background_view",				-- 称号与背景
	SkyRedenvelopes                   = "sky_redenvelopes",        --天降红包
	GuildAnswer                       = "guildanswer",             -- 帮派答题
	GuildJiuSheDaoFB                  = "guildjiushedaofb",        -- 帮派副本
	GuildPopInfoView                  = "guide_pop_info",			--帮派信息
	LieKunSingleOutPutTips            = "LieKunSingleOutPutTips",	-- 个人伤害排名
	LieKunGuideOutPutTips             = "LieKunGuideOutPutTips",	-- 帮派伤害排名
	OpenServerRank                    = "open_server_rank",            -- 开服活动排行榜
	CallBoss                          = "call_boss",         					-- boss召唤
	FubenWinView                      = "fuben_win_view",				-- 副本胜利界面
	FuBenNextView                     = "fuben_next_view",				-- 天仙阁副本胜利面板
	Compose                           = "other_compose",							--炼炉
	ComposeStuffListView			  = "ComposeStuffListView",
	ComposeTicket                     = "compose_ticket",					--合成门票
	ChargeRepayment2                  = "charge_repayment2",				-- 充值回馈
	HappyConsume                      = "rand_happy_consume",					--消费欢乐颂
	HappyRecharge                     = "rand_happy_recharge",				--充值欢乐颂
	RechargeGift                      = "act_recharge_gift",                 --充值好礼
	RechargeGift2                     = "act_recharge_gift2",                 --充值好礼2
	TianJianCaiShen                   = "tianjian_caishen",			--天降财神
	LuckBuy                           = "LuckBuy",					--幸运云购
	LimitedShop                       = "limited_shop",				--限时特卖
	FlowerRank                        = "flower_rank",				--情人鲜花榜
	WishingPool                       = "wishing_pool",				--许愿池
	TreasureAct                       = "Treasure_act",				--全民鉴宝
	ConsumeRankThree                  = "cunsume_rank_three",		--消费排行
	LuckyRandom                       = "LuckyRandom",				--幸运云购
	TeHuiShop                         = "tehui_shop",				-- 特惠秒杀
	Secrefash                         = "Secrefash",				-- 炫装特卖
	Browse                            = "browse",					-- 查看信息
	AppearanceGetNew                  = "appearance_get_new",		--获得新的物品：坐骑，灵宠，羽翼，法宝等...
	AppearanceRemoveView              = "AppearanceRemoveView",     -- 外观过期
	AppearanceMount                   = "appearance_mount",			--幻装-坐骑界面：通过其他按钮打开时的界面，该界面只显示坐骑-灵宠-战骑
	Unlock                            = "unlock",					-- 锁屏
	ExpAdditionView                   = "ExpAdditionView",			-- 经验加成
	OpenFunFlyView                    = "OpenFunFlyView", 				-- 开启新功能
	TipsGetRewardView                 = "TipsGetRewardView", 			-- 获取奖励提示
	ItemTipView                       = "ItemTipView", 					-- 装备tips
	ItemGoodsTipView                  = "ItemGoodsTipView", 			-- 物品tips
	DisplayItemTipView                = "DisplayItemTipView", 			-- 形象tips
	TipsEneterCommonSceneView         = "TipsEneterCommonSceneView",	-- 进入普通副本
	TipsShowChapterView               = "TipsShowChapterView",  		-- 章节tip
	TipsShowStoryTextView             = "TipsShowStoryTextView",  		-- 任务剧情文字tip
	MasterEnvelopeView 				  = "MasterEnvelopeView",			-- 徒儿亲启
	GuideSpriteBorn 				  = "GuideSpriteBorn",				-- 引导精灵出现
	GodGetRewardNewView				  = "GodGetRewardNewView",
	----骑宠进阶-----
	MountLingChongView                   = "MountLingChongView",     		  -- 骑宠界面
	MountLingChongDisplayBox             = "MountLingChongDisplayBox",
    LingChongEquipView                   = "LingChongEquipView",           -- 灵宠装备界面
    MountEquipView                       = "MountEquipView",               -- 坐骑装备界面
    HuaKunEquipView                      = "HuaKunEquipView",              -- 化鲲装备界面
    LingChongQuickSpView                 = "LingChongQuickSpView",         -- 灵宠装备快捷升品界面
    MountEquipQuickSpView                = "MountEquipQuickSpView",        -- 坐骑装备快捷升品界面
    HuaKunEquipQuickSpView               = "HuaKunEquipQuickSpView",       -- 化鲲装备快捷升品界面
	MountLingChongChooseBtnView          = 'MountLingChongChooseBtnView',
	MountLingChongEquipBagFuHunSkillView = "MountLingChongEquipBagFuHunSkillView",
	-----------------

	------- 新形象 ---------
	NewAppearanceWGView					= "NewAppearanceWGView",			-- 新形象
	NewAppearanceHaloWGView				= "NewAppearanceHaloWGView",		-- 新形象（光环）
	-----
	new_appearance_upgrade_mount = "new_appearance_upgrade_mount",	-- 进阶 - 坐骑
	new_appearance_upgrade_wing = "new_appearance_upgrade_wing",	-- 进阶 - 羽翼
	new_appearance_upgrade_fabao = "new_appearance_upgrade_fabao",	-- 进阶 - 法宝

	------- 新商店 ---------
	--NewShopWGView					= "NewShopWGView",			-- 新商店

	------- 独尊领域 ---------
	SupremeFieldsWGView					= "SupremeFieldsWGView",			-- 独尊领域
	SupremeFieldsActView					= "SupremeFieldsActView",			-- 独尊领域活动

	----战魂-----
	FightSoulView = "FightSoulView",
	FightSoulExpPoolView = "FightSoulExpPoolView",

	-----------------

	----仙界装备-----
	FairyLandPlaneView = "FairyLandPlaneView",
	FairyLandRuleView = "FairyLandRuleView",
	FairyLandEquipmentView = "FairyLandEquipmentView",

	EvolvePopEquipBag = "EvolvePopEquipBag",			--仙界装备 锻造 进化 装备背包
	EvolveStarMasterView = "EvolveStarMasterView",		--仙界装备 全身星级加成

	GodBodyDuJieView = "GodBodyDuJieView",				-- 神体 渡劫界面
	-----------------

	DiZangRedPackView 		 = "DiZangRedPackView",			-- 地藏红包
	OnlineRewardView		 = "OnlineRewardView",			-- 在线奖励
	FuhuoView                = "FuhuoView",
	FuhuoNoBtnView           = "FuhuoNoBtnView",
	FuhuoEquipFbView         = "FuhuoEquipFbView",
	BeKilledView             = "BeKilledView",
	--BeKilledInVIPView        = "BeKilledInVIPView",
	BeKilledOutView          = "BeKilledOutView",
	Fireworks                = "fireworks",					-- 烟花庆典
	KfOneVOneMatch           = "kuafuonevonematch",			--跨服1v1匹配
	PvPRank                  = "pvp_rank",					--跨服PvP排行
	PvPSeasonReward          = "pvp_season_reward",			--跨服PvP赛季奖励
	PvPGongXunReward         = "pvp_gongxun_reward",		--跨服PvP功勋奖励
	TaskConsumeView          = "TaskConsumeView",
	TaskEnterFbTipsView      = "TaskEnterFbTipsView",
	TaskDailyRewardTip       = "TaskDailyRewardTip",
	TaskGuildRewardTip       = "TaskGuildRewardTip",
	TaskDailyBatchTips       = "TaskDailyBatchTips",
	OpenKeyAddFriend         = "open_keyaddfriend",			--打开一键加好友(社交模块)
	SocietyAddReqView        = "SocietyAddReqView", 		-- 好友申请
	SocietyTeamInviteReq     = "SocietyTeamInviteReq", 		-- 组队邀请
	SocietyEnemyRecordView 	 = "SocietyEnemyRecordView", 	-- 仇人记录
	Marry_XunYouView         = "Marry_XunYouView", 			-- 巡游view
	MarryFingerPrintView     = "MarryFingerPrintView",  	-- 按手印
	Marry_DeMandView         = "marry_demandview", 			-- 索要请帖
	MarryBaiTangView         = "marry_baitang_view", 		-- 是否同意拜堂
	CollectBlessView         = 'CollectBlessView',          -- 翻牌集福
	JinyintaView             = 'JinyintaView',				-- 天天向上
	Zhenbaoge                = "zhenbaoge_view", 			-- 珍宝阁
	VipExperience            = "vip_experience", 			-- vip体验
	ChooseGiftView           = "choose_gift_view", 			-- 自选
	BatchUseChooseGiftView   = "BatchUseChooseGiftView", 	-- 自选的批量获取
	CrazyMoneyTreeView       = "CrazyMoneyTreeView", 		-- 疯狂摇钱树
	ActDisCount              = "ActDisCount",				-- 一折抢购
	EveryDayOneLove          = "EveryDayOneLoveView",			-- 每日一爱
	RechargeReturnRewardView = "RechargeReturnRewardView", 	-- 狂返元宝
	ActDiscount              = "ActDiscount",				-- 一折抢购
	ProfessWallView          = "ProfessWallView", 			-- 表白墙界面
	ChooseProfessView        = "ChooseProfessView", 		-- 我要表白
	ProfessRankView          = "ProfessRankView", 			-- 表白排行
	ProfessRemindView        = "ProfessRemindView", 		-- 屏蔽表白特效
	ProfessSelectFriendView  = "ProfessSelectFriendView" ,	-- 选择表白对象界面
	GuaJiExpView             = "GuaJiExpView",
	FlowerEffectView         = "floser_effect_view", 		-- 送花特效
	WorshipView              = "WorshipView", 				-- 膜拜
	BossFightView            = "boss_fight_view",			-- boss乱斗
	BossFightRewardView      = "boss_fight_reward_view",	-- boss乱斗奖励
	BossFightSceneView       = "boss_fight_scene_view",		-- boss乱斗场景界面
	YunbiaoView              = "YunbiaoView",				-- 护送
	YuBiaoJieSuan			 = "YuBiaoJieSuan",				-- 护送结算
	YuBiaoQuestion			 = "YuBiaoQuestion",				-- 护送答题
	MonsterTip               = "monster_tip",				-- 怪物信息
	MarryBlessingView        = "MarryBlessingView", 		-- 结婚祝福
	SpecialGiftBag           = "SpecialGiftBag",			-- 特惠礼包
	KFYunGou                 = "KuaFuYunGouView", 			-- 跨服云购
	KFYunGouReward           = "KuaFuYunGouViewReward",		-- 跨服云购获奖界面
	KFYunGouResultView       = "KFYunGouResultView",		-- 跨服云购购买结果界面
	XinFuTeMai               = "XinFuTeMai", 				-- 新服特卖
	SmallGoal                = "SmallGoal",					-- 小目标
	DayDayFanLiView          = "DayDayFanLi",				-- 天天返利
	KuaFuMiaoSha             = "KuaFuMiaoSha", 				-- 跨服秒杀
	KuaFuMiaoShaPreView      = "KuaFuMiaoShaPreView", 		-- 跨服秒杀下期预览
	ActivePlate              = "ActivePlate", 				-- 活跃转盘
	OpenServerJuanXian		 = "OpenServerJuanXian",		-- 开服捐献
	OpenServerZhenLong		 = "OpenServerZhenLong",		-- 真龙送礼
	MustBuy                  = "MustBuy", 					-- 超值必买
	MustBuyTip               = "MustBuyTip", 				-- 超值必买Tip
	ZCResultView             = "ZCResultView", 				-- 战场结算界面
	ZCRankView             	 = "ZCRankView", 				-- 战场排名界面
	ZCStageView              = "ZCStageView", 				-- 战场切换局数界面
	BossHunterRank			 = "BossHunterRank",			-- Boss猎人排行

	TianShenView			 = "TianShenView",				-- 天神
	TianShenHuamoView		 = "TianShenHuamoView",			-- 天神化魔
	TianShenShuangShengView  = "TianShenShuangShengView",	-- 双生天神 
	TianShenShenTongView     = "TianShenShenTongView", 		-- 天神神通
	TianShenActivationView	 = "TianShenActivationView",	-- 天神激活面板
	TianShenSelectView 		 = "TianShenSelectView", 		-- 天神选择面板
    TianShenFightAlert 		 = "TianShenFightAlert",		-- 天神出战弹框
    TianShenJuexing 		 = "TianShenJuexing",		    -- 天神觉醒
    LimitedTimeOffer         = "LimitedTimeOffer",		    -- 特惠直购
    ActivityLimitBuyView     = "ActivityLimitBuyView",      -- 限时直购
	OdysseyPurchaseView		 = "OdysseyPurchaseView",		-- 西游直购
    XiuXianShiLian 		     = "XiuXianShiLian",		    -- 修仙试炼
	ShenShiQiangHuaView = "ShenShiQiangHuaView", 					-- 天神-神饰-强化
	TianShenBaGuaShop = "TianShenBaGuaShop",						-- 天神八卦商店
	TianShenJiBanView = "TianShenJiBanView",
	WuXingTip				 = "WuXingTip",							-- 五行克制tip

	TianShenBaoXia = "TianShenBaoXia",						-- 天神宝匣
	TianShenLingHeView = "TianShenLingHeView",				-- 天神灵核

	GodGetReward			 = "GodGetReward",			-- 天神夺宝
	GodGetRewardRecord		 = "god_get_reward_record",		-- 天神夺宝
	MarryNotice				 = "marry_notice",				--结婚预告
	guild_task 				 = "guild_task",				--仙盟建设
	GuildFbGuWuBtnView 		 = "GuildFbGuWuBtnView", 		-- 仙盟试炼鼓舞按钮界面
	GuildFbGuWuView 		 = "GuildFbGuWuView", 			-- 仙盟试炼鼓舞界面
	GuildFbShouHuJieSuanView = "GuildFbShouHuJieSuanView", 	-- 仙盟试炼结算界面
	XiuZhenRoadView 		 = "XiuZhenRoadView",			--修真之路
	FanRenXiuZhenTask 		 = "FanRenXiuZhenTask",			--凡人修真task
	FanRenXiuZhenRewardView 		 = "FanRenXiuZhenRewardView",			--凡人修真奖励
	FanrenxiuzhenRewardPreview = "FanrenxiuzhenRewardPreview",  -- 凡人修真奖励总览
	FuBenPanelTianShenBuyView = "FuBenPanelTianShenBuyView",--天神购买次数面板
	FuBenPanelTianShenSaoDangView = "FuBenPanelTianShenSaoDangView", --天神扫荡面板
	TianShenLogicView = "TianShenLogicView", 				--天神扫荡面板
	BootyBayFBReadyView = "BootyBayFBReadyView",			--藏宝湾组队准备界面
	BootyBayFBView = "BootyBayFBView",						--藏宝湾单人副本内的信息界面
	TeamBootyBayFBView = "TeamBootyBayFBView",				--藏宝湾组队副本内的信息界面
	BootyBaySceneFollowView = "BootyBaySceneFollowView",	--藏宝湾场景信息界面
	BootyBayRewardTips = "BootyBayRewardTips",				--藏宝湾组队准备信息界面
	BootybayTeamBaoMingView = "BootybayTeamBaoMingView",	--藏宝湾组队报名信息界面
	BootyBayPreView = "BootyBayPreView",					--藏宝湾奖励预览界面
	FuBenPanelBaGuaMiZhenSaoDangView = "FuBenPanelBaGuaMiZhenSaoDangView",--八卦迷阵扫荡界面
	FuBenPanelBaGuaMiZhenBuyView = "FuBenPanelBaGuaMiZhenBuyView",--八卦迷阵买次数界面

	BaGuaMiZhenTaskView = "BaGuaMiZhenTaskView",			--八卦迷阵task信息界面
	ExpPoolView = "ExpPoolView",  							--经验池界面
	TianShenRoadPanel = "TianShenRoadPanel",				-- 诸神庆典(天神之路)
	TianshenRoadJigsawTask = "TianshenRoadJigsawTask",		-- 诸神庆典(天神之路)-拼图任务
	ManHuangGuDianFirstPassView = "ManHuangGuDianFirstPassView", 	--蛮荒古殿 首通奖励界面
	ManHuangGuDianBuyView = "ManHuangGuDianBuyView", 				--蛮荒古殿 购买次数界面
	ManHuangGuDianLogicView = "ManHuangGuDianLogicView", 				--蛮荒古殿 场景任务栏界面
	FuBenPopDialogView = "FuBenPopDialogView",				-- 副本引导弹窗
	TIANSHEN_JIANLI = "tianshen_jianlin",					-- 天神降临预告
	FirstRechargeView = "FirstRechargeView",				-- 首充
	QuanMinBeiZhanView = "quanmin_beizhan",					-- 全民备战

	ActXianQiJieFengView = "act_xianqi_jiefeng",			--仙器解封

	EveryDayRechargeView = "EveryDayRechargeView",			--每日充值
	ZhanLingView = "ZhanLingView",							-- 战令

	YuanGuXianDianLogicView = "YuanGuXianDianLogicView", 	--远古仙殿场景任务栏UI

	LoverPkView = "LoverPkView", 							--仙侣PK场景界面
	LoverPkMsgView = "LoverPkMsgView",                      --仙侣Pk信息界面 
	LoverPKBlessingView = "LoverPKBlessingView",            -- 仙侣PK预热送花

	KF3v3View = "KF3v3View",								--3V3主界面(含有页签：跨服3v3、战队信息)
	KF3V3MatchView = "KF3V3MatchView", 						--3V3匹配界面
	KF3V3PrepareLogicView = "KF3V3PrepareLogicView", 		--3V3准备场景任务栏界面
	KF3V3LogicView = "KF3V3LogicView", 						--3V3场景界面
	KF3V3KillInfoView = "KF3V3KillInfoView", 				--3V3击杀界面

	KF3V3ZhanDuiView = "KF3V3ZhanDuiView", 					--3V3战队界面
	KF3V3SeasonRewardView = "KF3V3SeasonRewardView", 		--3V3战队奖励界面
	KF3V3ZhanDuiRankView = "KF3V3ZhanDuiRankView", 			--3V3战队排名界面
	ZhanDuiBeInviteView = "ZhanDuiBeInviteView", 			--3V3战队_被邀请界面
	ZhanDuiCreateView = "ZhanDuiCreateView", 				--3V3战队_创建界面
	ZhanDuiJoinView = "ZhanDuiJoinView", 					--3V3战队_加入界面
	ZhanDuiZhanLingView = "ZhanDuiZhanLingView", 			--3V3战队_战令界面
	ZhanDuiApplyView = "ZhanDuiApplyView", 					--3V3战队_申请加入战队界面
	ZhanDuiInviteView = "ZhanDuiInviteView", 				--3V3战队_邀请别人加入战队界面
	ZhanDuiChangeNoticeView = "ZhanDuiChangeNoticeView", 	--3V3战队_修改公告界面
	ZhanDuiChangeNameView = "ZhanDuiChangeNameView", 		--3V3战队_修改战队名字界面
	LayoutZeroBuyView = "LayoutZeroBuyView",				--零元购
	YiBenWanLiView = "YiBenWanLiView",						--一本万利
	GuideMoWang = "GuideMoWang",							--魔王引导
	TipsSystemView = "TipsSystemView", 						--提示飘字

	GuildShowHuRankView = "GuildShowHuRankView", 			-- 仙盟守护在副本中排行界面
	FuBenMiJingView = "FuBenMiJingView", 					-- 仙盟守护在副本界面
	XunYouOpenHintView = "xunyou_hint_view",					--巡游提示
	GuildJuanXian = "GuildJuanXian",						--仙盟捐献
	GuildActivityShouHuRank = "GuildActivityShouHuRank",	--仙盟守护排名
	GuildActivityBossRank = "GuildActivityBossRank",		--仙盟boss排名
	GuildActivityDaTiRank = "GuildActivityDaTiRank",		--仙盟答题排名
	GuildWarDuiZhengView = "GuildWarDuiZhengView",			--仙盟争霸对阵表
	GuildWarInviteView = "GuildWarInviteView",				--仙盟争霸资格赛
	GuildWarInviteResultView = "GuildWarInviteResultView",	--仙盟争霸资格赛结果
	GuildWarRuleView = "GuildWarRuleView",					--仙盟争霸规则
	GuildInviteSceneView = "GuildInviteSceneView",			--仙盟争霸资格赛场景界面
	GuildWarFeiPeiView = "GuildWarFeiPeiView",				--仙盟争霸分配奖励界面
	GuildRenameView = "GuildRenameView",					--仙盟改名界面
	GuildWarZhanBaoView = "GuildWarZhanBaoView",			--仙盟争霸战报
	GuildActivityView = "GuildActivityView",				--仙盟活动

	MingWenView = "MingWenView",							--F2铭文系统
	MingWenBagView = "MingWenBagView",						--F2铭文背包
	MingWenLibraryView = "MingWenLibraryView",				--F2铭文总览
	LongHunView = "LongHunView",							--F2龙魂

	OpenningView = "OpenningView", 							-- 开启仙途
	CalendarView = "CalendarView", 							-- 周历
	CalendarNoticeView = "CalendarNoticeView", 				-- 周历活动预告
	OpenningActNoticeView = "OpenningActNoticeView", 		-- 开启中活动提醒
	BossOfferReward = "BossOfferReward",                     --Boss悬赏
	OpenServerAssistView = "OpenServerAssistView",			-- 开服助力
	XianLingGuZhen = "XianLingGuZhen",						-- F2灵犀六芒图
	ZhouYiYunCheng = "ZhouYiYunCheng",						-- F2周一运程
	FengShenBang = "FengShenBang",							-- F2封神榜
	QunXiongZhuLu = "QunXiongZhuLu",						-- F2群雄逐鹿
	LimitTimeGift = "limit_time_gift",						-- F2限时礼包
	LimitTimeGiftPurchase = "LimitTimeGiftPurchase",		-- 限时直购礼包
	GuDaoSceneFollowView = "GuDaoSceneFollowView",			-- 孤岛激战副本
	ShiTuXiuLi = "ShiTuXiuLi",
	NovicePopDialogView = "NovicePopDialogView", 			-- 新手弹出对话
	SceneScreenEffectView = "SceneScreenEffectView", 		-- 场景特定区域屏幕特效
	EternalNightEquipView = "EternalNightEquipView",		-- 永夜之巅 装备界面
	EternalNightFuHuoView = "EternalNightFuHuoView",		-- 永夜之巅 复活
	EternalNightRankView = "EternalNightRankView",			-- 永夜之巅 排行
	EternalNightEliminateRankView = "EternalNightEliminateRankView",	-- 永夜之巅 决赛阶段排行
	EternalNightResultView = "EternalNightResultView",		-- 永夜之巅 结算
	EternalNightRewardView = "EternalNightRewardView",		-- 永夜之巅 奖励
	EternalNightTaskView = "EternalNightTaskView",			-- 永夜之巅 场景界面
	EternalNightMapView = "EternalNightMapView",			-- 永夜之巅 地图

	FirstTSGetNewView 				  = "FirstTSGetNewView",			-- 第一次获得天神的界面
	KuafuBossMH = "worserv_boss_mh",						-- 世界Boss 蛮荒神兽

	CountryMapMapView = "counrty_map_map_view", 			-- 版图界面
	CountryMapActView = "country_map_act_view", 				-- 国家版图-活动

	CrossLongMaiView = "CrossLongMaiView",						-- 跨服龙脉

	AlchemyView = "AlchemyView",								-- 炼丹房
	AlchemyComposeView = "AlchemyComposeView",                  -- 炼丹合成
	AlchemyNormalArrayView = "AlchemyNormalArrayView",          -- 炼丹  法阵提升
	AlchemyFurnaceView = "AlchemyFurnaceView",                  -- 炼丹  炼丹炉提升
	AlchemySpeedUpView = "AlchemySpeedUpView",					-- 炼丹  加速炼丹

	PrivilegedGuidanceView = "PrivilegedGuidanceView",					--特权引导

	AntiFraudCartoonView = "anti_fraud_cartoon_view", 		-- 防骗漫画

	NeedGoldGiftView = "need_gold_gift_view", 				-- 特退礼包面板

	LongMaiShop  = "longmai_shop", -- 龙脉商店

	SubPackageView = "subpackage_view", 					--分包下载
	OperationActivityView = "operation_activity_view",		--动态运营活动
	OperationTaskChainEntranceView = "operation_task_chain_entrance_view", 	--任务链任务入口界面
	OperationTaskChainRewardView = "operation_task_chain_reward_view", --任务链进度奖励界面
	OperationTaskChainFbInfoView = "operation_task_chain_fb_info_view", --任务链场景信息界面
	OperationTaskChainAcceptView = "operation_task_chain_accept_view", --任务链场景任务接受
	OperationTaskChainScheduleView = "operation_task_chain_schedule_view", --任务链任务状态小界面
	OperationTaskChainResultView = "operation_task_chain_result_view",		--任务链结算界面
	OperationTaskChainSkillView = "operation_task_chain_skill_view",		--任务链技能小面板
	OperationTaskChainThinkView = "operation_task_chain_think_view",		--任务链活动结束感谢面板
	OperationTaskChainFuHuoView = "operation_task_chain_fuhuo_view",		--任务链运送躲避失败界面
	OperationTaskView = "operation_task_chain_view",						-- 任务链主界面


    MergeActivityView = "merge_activity_view",		--合服活动

	--跨服1V1 新
	KFOneVOneEndView = "KFOneVOneEndView",
	KFOneVOneHeadView = "KFOneVOneHeadView",
	KFOneVOneJingCaiView = "KFOneVOneJingCaiView",
	KFOneVOneMatchingView = "KFOneVOneMatchingView",
	KFOneVOneDuiZhenView = "KFOneVOneDuiZhenView",
	KFOneVOneRewardView = "KFOneVOneRewardView",
	KFOneVOneSeasonReward = "KFOneVOneSeasonReward",
	KFOneVOneTaskView = "KFOneVOneTaskView",
	KFOneVOneResultView = "KFOneVOneResultView",

	ScreenShotView = "ScreenShotView",						--准备截屏
	SnapShotView = "SnapShotView", 							--截屏结果

	--运势结果
	QifuYunShiResultView = "QifuYunShiResultView",
	QifuYunShiFirstView = "QifuYunShiFirstView",

	--每日宝箱
	BaoXiangCodeView = "BaoXiangCodeView",
	BaoXiangRewardYuLianView = "BaoXiangRewardYuLianView",
	BaoXiangFlushRequestView = "BaoXiangFlushRequestView",
	BaoXiangFlushView = "BaoXiangFlushView",
	BaoXiangGetResultView = "BaoXiangGetResultView",
	BaoXiangGetRewardView = "BaoXiangGetRewardView",
	BaoXiangRefreshAlert = "BaoXiangRefreshAlert",
	BaoXiangThankTips = "BaoXiangThankTips",
	FeedBackView = "FeedBackView",--玩家反馈

	-- 龙珠
	LongZhuView = "LongZhuView",
	MainUILongZhuSkill = "MainUILongZhuSkill",

	--暗器系统
    HiddenWeaponView = "shenji_view",                  --暗器软甲
	HiddenWeaponEquipSelectView = "HiddenWeaponEquipSelectView",    --暗器软甲材料选择
	HWDetailSkillTip = "HWDetailSkillTip",    --技能tip
	HWEquipSkillTip = "HWEquipSkillTip",    --技能tip

	ShenJiXianShiView = "ShenJiXianShiView", 				--神机现世
	ShenJiSpecialSaleView1 = "ShenJiSpecialSaleView1", 		--神机特卖1
	ShenJiSpecialSaleView2 = "ShenJiSpecialSaleView2", 		--神机特卖2
	ShenJiOpenAnimView = "ShenJiOpenAnimView",				--神机动画

	ChongBangTipView = "ChongBangTipView",	--冲榜通用tips

	TianShen3v3View = "tianshen_3v3_view", 									-- 天神3v3
	TianShen3v3RuleView = "tianshen_3v3_rule_view",							-- 天神3v3规则面板
	TianShen3v3RewardView = "tianshen_3v3_reward_view",						-- 天神3v3奖励预览面板
	TianShen3v3PrepareSceneView = "tianshen_3v3_prepare_scene_view",		-- 天神3v3准备场景面板
	TianShen3v3ReadyView = "tianshen_3v3_ready_view", 						-- 天神3v3准备面板
	TianShen3v3SceneView = "tianshen_3v3_scene_view", 						-- 天神3v3战斗场景面板
	TianShen3v3EndView = "tianshen_3v3_end_view", 							-- 天神3v3结算面板
	TianShen3v3CountdownView = "tianshen_3v3_countdown_view", 				-- 天神3v3倒计时
	TianShen3v3EndCountdownView = "tianshen_3v3_end_countdown_view", 		-- 天神3v3战斗结束倒计时
	TianShen3v3ReliveView = "tianshen_3v3_relive_view", 					-- 天神3v3复活面板
	TianShen3v3SeasonRankView = "tianshen_3v3_season_rank_view", 			-- 天神3v3赛季排名面板

	WorldsNO1View = "worlds_no1_view", 										-- 天下第一
	WorldsNO1RewardView = "worlds_no1_reward_view", 						-- 天下第一奖励面板
	WorldsNO1TimetableView = "worlds_no1_timetable_view", 					-- 天下第一对战场次面板
	WorldsNO1SceneView = "worlds_no1_scene_view",		 					-- 天下第一准备和战斗场景面板
	WorldsNO1CountdownView = "worlds_no1_countdown_view", 					-- 天下第一战斗开始倒计时面板
	WorldsNO1ObservationView = "worlds_no1_observation_view", 				-- 天下第一观战面板
	WorldsNO1FuhuoView = "worlds_no1_fuhuo_view", 							-- 天下第一复活面板
	WorldsNO1EndCountdownView = "worlds_no1_end_countdown_view", 			-- 天下第一结束前倒计时

	--节日活动定义
    FestivalActivityView = "FestivalActivityView",		--节日活动
	FestivalNoticeView = "FestivalNoticeView", 				--节日预告

	GuildBattleRankedView = "GuildBattleRankedView",

	--boss协助 新 （现在策划的需求只针对蛮荒boss）
	BossXiezhuListView = "BossXiezhuListView",
	BossXiezhuThanksView = "BossXiezhuThanksView",
	BossXiezhuInfoView = "BossXiezhuInfoView",
	BossXiezhuRuleView = "BossXiezhuRuleView",

	XianyuTrunTableView = "XianyuTrunTableView",		--仙玉转盘  活动
	YiYuanHaoLiView = "YiYuanHaoLiView",	--一元豪礼

	WingLinZhiSkillView			 = "WingLinZhiSkillView",			-- 灵智技能  羽翼
	FaBaoLinZhiSkillView			 = "FaBaoLinZhiSkillView",			-- 灵智技能 法宝
	ShenBingLinZhiSkillView			 = "ShenBingLinZhiSkillView",			-- 灵智技能  神兵
	JianZhenLinZhiSkillView			 = "JianZhenLinZhiSkillView",			-- 灵智技能   剑灵
	LinZhiAchView			 = "LinZhiAchView",			-- 灵智技能
	LingZhiSkillTips			 = "LingZhiSkillTips",			-- 灵智技能
	LingZhiQiHunTips			 = "LingZhiQiHunTips",			-- 灵智技能
	LingZhiZhiGouTips			 = "LingZhiZhiGouTips",			-- 灵智技能
	YinJiTurnTableView = "YinJiTurnTableView",

	MainUiMultiSkillSelectView = "MainUiMultiSkillSelectView", 		-- 天神切换技能小界面
	IllegalityNoticeView = "IllegalityNoticeView",                  -- 移速违规警告界面

	GuildHeBingShow = "GuildHeBingShow", 	--仙盟合并列表界面
	OfflineRestView = "OfflineRestView",	--打坐奖励
	ExperienceFbView = "ExperienceFbView",	--历练副本
	MonthCardProvolegeView = "MonthCardProvolegeView", --月卡特权界面

	SingleReChargeView = "SingleReChargeView",     --单笔充值
	LuckyGiftBagView = "LuckyGiftBagView",					-- 幸运大礼包主界面
	LuckyGiftBagLocalView = "LuckyGiftBagLocalView",		-- 幸运大礼包主界面(本服)
	LuckyGiftBagThroughTrainView = "LuckyGiftBagThroughTrainView",   --神秘商店
	LuckyGiftBagLocalThroughTrainView = "LuckyGiftBagLocalThroughTrainView",   --神秘商店 (本服)
	NiuDanView = "NiuDanView",               --幸运砸蛋
	TianShenLingHeDrawView = "TianShenLingHeDrawView",		-- 天神灵核抽奖
	MarryDiscountView = "MarryDiscountView",				-- 结婚折扣
	ShenJiTianCiView = "ShenJiTianCiView",					-- 神技天赐
	CrossFlowerRankView = "CrossFlowerRankView",			-- 跨服鲜花榜
	CatExploreView = "CatExploreView",                      -- 小猫探险
	ActicityFoldFunView = "ActicityFoldFunView",            -- 活动折叠
	PlaySkillFloatWordView = "PlaySkillFloatWordView",      -- 技能飘字
	FlopDrawView = "FlopDrawView",                          -- 小猫探险2
	ChainDrawView = "ChainDrawView",                        -- 一线牵
	ChaoticPurchaseView = "ChaoticPurchaseView",            -- 洪荒直购
	TianShenLiLianView = "TianShenLiLianView",				-- 天神历练
	FiveElementsView = "FiveElementsView",                  -- 五行系统
	FiveElementsTreasuryView = "FiveElementsTreasuryView",	-- 五行宝库
	RebateExtinctGiftView = "RebateExtinctGiftView",		-- 返利-绝版赠礼
	RebateFireWorksView = "RebateFireWorksView",			-- 返利-烟花抽奖
	RebateDiscountView = "RebateDiscountView",				-- 返利-充值立减
	PremiumGiftView = "PremiumGiftView",                    -- 超值赠礼
	HongHuangGoodCoremonyView = "HongHuangGoodCoremonyView",-- 洪荒豪礼
	ChaoticVipSpecialView = "ChaoticVipSpecialView",        -- vip特典
	DownLoadWebView = "DownLoadWebView",                    -- 下载有礼
	HelpRankView = "HelpRankView",                          -- 冲榜助力
	TianShenPurchaseView = "TianShenPurchaseView",          -- 天神直购
	GodPchaseView = "GodPchaseView",                        -- 神藏直购
	HongHuangClassicView = "HongHuangClassicView",          -- 洪荒特典
	GloryCrystalView = "GloryCrystalView",                  -- 天裳仙衣
	GloryCrystalExchangeShopView = "GloryCrystalExchangeShopView",                -- 天裳仙衣兑换商店
	SystemForceLongShenView = "SystemForceLongShenView",    -- 系统预告——龙神降临
	WeiWoDunZunView = "WeiWoDunZunView",                    -- 唯我独尊
	ChessBoardTreasueView = "ChessBoardTreasueView",		-- 棋盘寻宝
	CustomActionView = "CustomActionView",					-- 自定义动作
	CustomActionWindowView = "CustomActionWindowView",		-- 自定义动作主界面弹窗
	HomesView = "HomesView",                                -- 家园
	SwornView = "SwornView",                                -- 结义金兰
	sworn_start = "sworn_start",         					-- 结义主界面
	sworn_apply = "sworn_apply",        					-- 结义申请
	sworn_imprint = "sworn_imprint",       					-- 结义金兰
	sworn_suit = "sworn_suit",		  						-- 结义装备-套装
	sworn_upstar = "sworn_upstar",		  					-- 结义装备-升星
	sworn_uplevel = "sworn_uplevel",		  				-- 结义装备-升级
	GodOfWealthView = "GodOfWealthView",                    -- 喜迎财神
	CashPointView = "CashPointView",                        -- 现金点
	RechargeVolumeView = "recharge_volume",                 -- 充值卷
	RechargeVolumeLimitView = "RechargeVolumeLimitView",    -- 充值卷任务
	RechargeVolumeRewardView = "RechargeVolumeRewardView",  -- 充值卷提取奖励
	GoddessBlessingView = "GoddessBlessingView",            -- 女神祝福
	LongXiView = "LongXiView",            					-- 受命于天
	-- SuperDragonSealView = "SuperDragonSealView",            -- 至尊玉玺
	-- DragonKingTokenView = "DragonKingTokenView",            -- 龙王令牌
	LevelRechargeView = "LevelRechargeView",                -- 等级直购
	SystemCapRankSwornView = "SystemCapRankSwornView",		-- 五行冲榜
	SystemCapRankShenJiView = "SystemCapRankShenJiView",	-- 神技冲榜
	SystemCapRankLingHeView = "SystemCapRankLingHeView",	-- 灵核冲榜
	SystemCapRankShengQiView = "SystemCapRankShengQiView",	-- 圣器冲榜
	SystemCapRankAnQiView = "SystemCapRankAnQiView",		-- 暗器冲榜
	SystemCapRankZhuHunView = "SystemCapRankZhuHunView",	-- 铸魂冲榜
	SystemCapRankLongShenView = "SystemCapRankLongShenView",-- 龙神冲榜
	SystemCapRankYuLingView = "SystemCapRankYuLingView",	-- 御灵冲榜
	SystemCapRankWuHunView = "SystemCapRankWuHunView",		-- 武魂冲榜
	SystemCapRankYuShouView = "SystemCapRankYuShouView",	-- 驭兽冲榜
	ShiTianSuitView = "ShiTianSuitView",					-- 弑天套装
	XuYuanFreshPoolView = "XuYuanFreshPoolView",			-- 许愿仙池
	JiangShanRuHuaView = "JiangShanRuHuaView",				-- 江山如画
	TodaySpecialView = "TodaySpecialView",					-- 限时直购2
	SuperPurchaseView = "SuperPurchaseView",				-- 限时直购3
	CheapShopPurchaseView = "CheapShopPurchaseView",        -- 特惠卖场
	HammerPlanView = "HammerPlanView",                      -- 暴揍策划
	OneDayRechargeView = "OneDayRechargeView",				-- 每日累充2
	--AccumulativeLoginView = "AccumulativeLoginView",		-- 累计登录
	FireWorksDrawSecondView = "FireWorksDrawSecondView",	-- 烟花抽奖2
	SpecialDrawTrunTable = "SpecialDrawTrunTable",			-- 特殊抽奖 -幸运转盘
	HideGoldMainView = "HideGoldMainView",					-- 藏金商铺
	PrivilegeBuyView = "PrivilegeBuyView",					-- 特权直购
	DragonSecretView = "DragonSecretView", 					-- 神龙密藏
	MultiFunctionView = "MultiFunctionView",                                -- 符咒
	AmountRechargedView = "AmountRechargedView",			-- 跨服充值榜
	CrossConsumeView = "CrossConsumeView",					-- 跨服消费榜
	CollectionWordView = "CollectionWordView",				-- 天师集字兑换商店
	TianShiGodownHillView = "TianShiGodownHillView",		-- 天师下山
	CangMingChaseView = "CangMingChaseView",                -- 沧溟直购
	GoldStoneView= "GoldStoneView",                			-- 金石之言
	InterludePopDialogView = "InterludePopDialogView",		-- 剧情黑幕
	BossMustFallPrivilegeView = "BossMustFallPrivilegeView",-- boss必爆
	HolyBeastCallView = "HolyBeastCallView",                -- 圣兽召唤
	WuHunView = "WuHunView",								-- 武魂
	WuHunGemFrontView = "WuHunGemFrontView",                --武魂宝石
	WuHunSelectBaoShiView = "WuHunSelectBaoShiView",		--武魂选择宝石
	WuHunBaoShiUpgradeView = "WuHunBaoShiUpgradeView",		--武魂升级宝石
	WuHunFrontPropertyTip = "WuHunFrontPropertyTip",        --武魂属性升级窗口
	WuHunMaterialBuyView = "WuHunMaterialBuyView",			-- 武魂材料直购.
	WuHunPrerogativeView = "WuHunPrerogativeView",			-- 武魂特权.
	WuHunCultivateView = "WuHunCultivateView",				-- 武魂培养
	WuHunSkillView = "WuHunSkillView",						-- 武魂技能镶嵌
	NewFightMountView = "NewFightMountView",                -- 新战斗坐骑
	RoleCharmNoticeView = "RoleCharmNoticeView",            -- 角色魅力榜
	CultivationView = "CultivationView",                    -- 修为
	CultivationPrivilegeView = "CultivationPrivilegeView",  -- 修为特权
	CultivationPreviewView = "CultivationPreviewView",      -- 境界预览
	EsotericaView = "EsotericaView",      					-- 仙法
	EsotericaDetailView = "EsotericaDetailView",      		-- 仙法详情
	CultivationBuffView = "CultivationBuffView",            -- 境界效率加成
	EsotericaPartView = "EsotericaPartView",                -- 仙技提升
	AngerSkillView = "AngerSkillView",  					-- 怒气技能
	AngerFashionView = "AngerFashionView",  				-- 怒气变身外观
	JudgmentView = "JudgmentView",  					    -- 渡劫
	JudgmentReadyView = "JudgmentReadyView",  				-- 渡劫准备
	CharmComposeView = "CharmComposeView",			        -- 天道石 合成
	RoleCharmRewardPreviewView = "RoleCharmRewardPreviewView",            -- 角色魅力榜奖励展示
	HolyHeavenlyDomainView = "HolyHeavenlyDomainView",      -- 圣天神域
	HolyHeavenlyDomainCampView = "HolyHeavenlyDomainCampView",-- 圣天神域阵营
	HolyHeavenlyDomainMapView = "HolyHeavenlyDomainMapView", --圣天神域战场
	HolyHeavenlyDomainPrivilegeView = "HolyHeavenlyDomainPrivilegeView", --圣天神域特权
	HolyHeavenlyDomainShopView = "HolyHeavenlyDomainShopView",    --圣天神域商店
	YiJianZhaoCaiView = "YiJianZhaoCaiView",				-- 一键招财
	CustomizedSuitView = "CustomizedSuitView",				-- 定制套装
	ShenNuTianZhuView = "ShenNuTianZhuView",				-- 神怒天诛
	BoundlessJoyView = "BoundlessJoyView",                  -- 长乐未央
	MechaView = "MechaView",                                -- 机甲系统
	MechaSkillShowView = "MechaSkillShowView",              -- 机甲技能展示
	ControlBeastsView = "ControlBeastsView",				-- 驭兽系统
	ControlBeastsCultivateWGView = "ControlBeastsCultivateWGView",-- 驭兽修炼
	ControlBeastsKingView = "ControlBeastsKingView",		-- 驭兽兽王
	ControlBeastsContractWGView = "ControlBeastsContractWGView",		-- 驭兽抽奖
	ControlBeastsPrizeDrawWGView = "ControlBeastsPrizeDrawWGView",		-- 驭兽抽奖(新)
	ControlBeastsDrawPrepareView = "ControlBeastsDrawPrepareView",		-- 驭兽抽奖(特效窗口)
	ControlBeastsBattleSelectView = "ControlBeastsBattleSelectView",	-- 驭兽出战选取
	ControlBeastsSkinView = "ControlBeastsSkinView",		-- 幻兽皮肤
	ControlBeastsDrawResultView = "ControlBeastsDrawResultView",-- 幻兽抽奖
	HolyBeastsView = "HolyBeastsView",						-- 创世圣兽
	HolyBeastsContractSelectView = "HolyBeastsContractSelectView",						-- 创世圣兽
	CangJinShopView = "CangJinShopView",                    -- 藏金商铺（功能）    
	CangJinExchangeView = "CangJinExchangeView",            -- 藏金商铺（兑换)
	CangJinExchangeAttr = "CangJinExchangeAttr",            -- 藏金商铺 --属性定制
	CangJinExchangeShopPreview = "CangJinExchangeShopPreview", --藏金商铺商品预览
	LifeTimeOfLoveView = "LifeTimeOfLoveView",				-- 一生所爱
	TreasurePalaceView = "TreasurePalaceView",				-- 臻宝殿
	CustomizedRumorsView = "CustomizedRumorsView",          -- 定制传闻
	MoRanXuanYuanView = "MoRanXuanYuanView",                -- 墨染轩辕
	PanaceaFurnaceView = "PanaceaFurnaceView",				-- 灵丹宝炉
	ShiTianSuitStrengthenView = "ShiTianSuitStrengthenView",	-- 灵皇套装养成
	VientianeTianyinView = "VientianeTianyinView",			-- 万象天引
	ReikiSeedView = "ReikiSeedView",						-- 灵气之种
	UltimateBattlefieldView = "UltimateBattlefieldView", 	--终极战场界面（未实现只是做一个判断）
	UltimateBattlefieldReward = "UltimateBattlefieldReward", --终极战场 --奖励界面
	TwinDirectPurchase = "TwinDirectPurchase",				-- 双生直购
	SunRainbowView = "SunRainbowView",						-- 贯日长虹
	SunShopView = "SunShopView",							-- 贯日积分商店
	EverythingUnderTheSun = "EverythingUnderTheSun",		-- 森罗万象
	KFCapRankView = "KFCapRankView",                        -- 跨服提战榜
	BFCapRankView = "BFCapRankView",                        -- 本服提战榜
	CapabilityUpGiftView = "CapabilityUpGiftView",			-- 战力飙升礼包
	TianxianPavilionView = "TianxianPavilionView",			-- 天仙宝阁
	KFAttributeStoneRankView = "KFAttributeStoneRankView",	-- 跨服属性宝石榜
	DiscountPurchaseView = "DiscountPurchaseView",			-- 一折弹窗礼包
	JingHuaShuiYueView = "JingHuaShuiYueView",				-- 镜花水月
	CrossMarryRankView = "CrossMarryRankView",				-- 跨服仙侣排行榜
	SwornRechargeView = "SwornRechargeView",				-- 跨服结义充值榜
	LingYuActiveView = "LingYuActiveView",					-- 超级锦鲤活动
	MysteryBoxView = "MysteryBoxView",						-- 盲盒卡包活动
	StrangeCatalogView = "strange_catalog",					-- 奇闻异录
	PositionalWarfareView = "PositionalWarfareView",        -- 阵地战
	LandWarFbPersonView = "LandWarFbPersonView",            -- 阵地战(单人副本)
	LandWarFbPersonBossInfoView = "LandWarFbPersonBossInfoView",  -- 阵地战Boss信息(单人副本)
	AssignmentView = "AssignmentView",						-- 委托任务
	EsotericaSkillShow = "EsotericaSkillShow",              -- 获得仙修
	CultivationGetView = "CultivationGetView",              -- 仙技技能预览
	MostVenerableView = "MostVenerableView",				-- 最强仙尊活动
	SkillBreakPurchase = "SkillBreakPurchase",				-- 技能突破直购
	MengLingView = "MengLingView",                          -- 梦灵
	HitHamsterView = "HitHamsterView",						-- 打地鼠（活动）
	GuildAnswerStartTimer = "GuildAnswerStartTimer",        --仙盟答题
	ConquestWarView = "ConquestWarView",    				-- 征战
	LongYunZhanLingTaskView = "LongYunZhanLingTaskView",	-- 龙云战令任务
	LongYunZhanLingView = "LongYunZhanLingView",			-- 龙云仙契 - 九霄仙令
	CapabilityWelfare = "CapabilityWelfare",				-- 战力福利
	OpenServerInvestView = "OpenServerInvestView",			-- 开服投资
	LordEveryDayShopView = "LordEveryDayShopView",          -- 魔王仙藏 领主每日商店
	AuctionTipsView = "AuctionTipsView",          			-- 拍卖弹窗
	TripleRechargeView = "TripleRechargeView",				-- 三倍充值
	FashionExchangeShopView = "FashionExchangeShopView",	--时装兑换商店
	PhantomDreamlandView = "PhantomDreamlandView",			-- 幻梦秘境
	WorldTreasureView = "WorldTreasureView",                -- 天财地宝
	PursuitGameView = "PursuitGameView", 					-- 天财地宝-迷影寻踪游戏界面
	HaoliWanzhangView = "HaoliWanzhangView",                -- 运营活动 豪礼万丈
	TotalRechargeGiftView = "TotalRechargeGiftView",		-- 累充豪礼
	HaoLiWanZhang3View = "HaoLiWanZhang3View",				-- 豪礼万丈3(绝版套装)
	NewFestivalActivityView = "NewFestivalActivityView",	-- 新节日活动
	ArenaTianTi = "arena_tianti",                    -- 天梯争霸
	BOSSInvasionView = "BOSSInvasionView",                  -- boss入侵
	BillionSubsidy = "BillionSubsidy",						-- 百亿补贴
	BillionSubsidyBuyTip = "BillionSubsidyBuyTip",			-- 百亿补贴 -购买弹窗
	BillionSubsidyActiveVip = "BillionSubsidyActiveVip",	-- 百亿补贴 -激活VIP页面
	CrossAirWarView = "CrossAirWarView",					-- 跨服空战
	RedPacketRainView = "RedPacketRainView",                  -- 跨服红包天降
	RedPacketRainSendView = "RedPacketRainSendView",                  -- 跨服红包天降 发红包界面
	RedPacketRainSendAddActivityView = "RedPacketRainSendAddActivityView",                  -- 跨服红包天降 开启红包活动界面.
	RedPacketRainInfoView = "RedPacketRainInfoView",                  -- 跨服红包天降 左侧信息界面
	CrossAirPopDialogView = "CrossAirPopDialogView",		-- 跨服空战，小提示
	NewAppearanceDyeView = "NewAppearanceDyeView",	-- 时装染色	
	RoleDiyAppearanceView = "RoleDiyAppearanceView",        -- 捏脸
	DressingRoleDiyView = "DressingRoleDiyView",            -- 易容
	MainUIStrongMenuView = "MainUIStrongMenuView",	-- 主界面 变强菜单
	DujieView = "DujieView",						-- 渡劫界面
	DujieOperateView = "DujieOperateView",					-- 渡劫操作界面
	DujieInviteView = "DujieInviteView",					-- 渡劫邀请界面
	DujieReceiveView = "DujieReceiveView",                  -- 渡劫接受邀请弹窗
	DujieResultSuccessView = "DujieResultSuccessView",		-- 渡劫成功界面
	DujieResultFailView = "DujieResultFailView",		-- 渡劫成功界面
	
	DujieBodyView = "DujieBodyView",						-- 渡劫-灵根
	XiuWeiView = "XiuWeiView",									-- 修为
	RechargeRankView = "RechargeRankView",			-- 本服充值榜
	ConsumeRankView = "ConsumeRankView",			-- 本服消费榜

	YanYuGeEntranceView = "YanYuGeEntranceView",    -- 烟雨阁入口
	YanYuGeWLTZTaskView = "YanYuGeWLTZTaskView",    -- 烟雨阁未来投资任务
	YanYuGePrivilegeView = "YanYuGePrivilegeView",    -- 烟雨阁特权
	YanYuGeExchangeShopView = "YanYuGeExchangeShopView",    -- 烟雨阁兑换商店
	YanYuGeNobleView = "YanYuGeNobleView",                  -- 烟雨贵族
	ThunderManaSelectView = "ThunderManaSelectView",        -- 雷法-总界面
	ShadyThunderView = "ShadyThunderView",                  -- 雷法-阴
	SunThunderView = "SunThunderView",                      -- 雷法-阳
	LifeIndulgenceView ="LifeIndulgenceView",               -- 终身特惠
	HundredEquipView = "HundredEquipView",                  -- 百倍爆装
	HundredEquipRatio = "HundredEquipRatio",                -- 黄金龙巢
	HundredLogicView = "HundredLogicView", 					-- 百倍爆装副本界面
	HundredEquipTip = "HundredEquipTip", 					-- 深渊王权

	HonorhallsView = "HonorhallsView",                    --永夜幻都
	ShenyuanBossHurtView = "ShenyuanBossHurtView",        --深渊boss
	PrivilegeCollectionView = "PrivilegeCollectionView",  -- 特权合集
	ControlBeastsDesposeView = "ControlBeastsDesposeView", -- 幻兽分解

	PositionalWarfareSceneView = "PositionalWarfareSceneView", -- 阵地战战斗界面
	PositionalWarfareMallView = "PositionalWarfareMallView", -- 武斗商店
	
	DragonTrialView = "DragonTrialView",					-- 龙神试炼
	DragonTrialTaskView = "DragonTrialTaskView",			-- 龙神试炼-战斗任务
	DragonTrialJieSuanView = "DragonTrialJieSuanView",		-- 龙神试炼-结算

	GameAssistant = "GameAssistant",						-- 游戏小助手
	HalfPurchaseView = "HalfPurchaseView",					-- 五折直购卡
	CrossTreasureView = "CrossTreasureView",				-- 跨服藏宝

	GloryCrystalHaoLiView = "GloryCrystalHaoLiView",		-- 天裳仙衣-天裳豪礼
	VipServiceWindowView = "VipServiceWindowView",	-- 客服

	NewFestivalActivityMainView = "NewFestivalActivityMainView",		-- 节日活动主界面
	XunYuanFreshPoolPopView = "XunYuanFreshPoolPopView",	-- 毒敌覆世(原许愿仙池)拍脸图
	XunYuanFreshPoolPopView2 = "XunYuanFreshPoolPopView2",	-- 毒敌覆世(原许愿仙池)拍脸图,小白
	XunYuanFreshPoolShop = "XunYuanFreshPoolShop",			-- 毒敌覆世(原许愿仙池)内置商店
	

	DailyFirstRechargeView = "DailyFirstRechargeView",		-- 每日首充
	OneSwordFrostbiteView = "OneSwordFrostbiteView",		-- 一剑霜寒
	OneSwordFrostbiteTaskView = "OneSwordFrostbiteTaskView",		-- 一剑霜寒-任务面板
	
	-- AppearanceSoulActView = "AppearanceSoulActView",		-- 外观器魂激活
	-- AppearanceSoulTrainView = "AppearanceSoulTrainView",	-- 器魂培养
	-- SoulUpTaskView = "SoulUpTaskView",						-- 器魂培养任务弹窗
	
	TipsNoItemView = "TipsNoItemView",						-- 道具不足弹窗，带跳转
	DiZangRedpacketPop = "DiZangRedpacketPop", 				-- 地藏红包提示弹窗
}

--二级界面名字。是主模块界面里的一个子界面。
--可用于打开功能模块面板时，再打开某个子界面
SubViewName = {
	CreateGuild = "create_guild",					--创建仙盟界面（属于仙盟）
	GuildJianXian = "guild_jianxian",				--仙盟捐献（属于仙盟）
	SmallLabaSend = "small_labasend",				--小喇叭发送界面（属于聊天）
	BigLabaSend = "big_labasend",					--大喇叭发送界面（属于聊天）
	QuickTeam = "quick_team",						--快速组队界面（属于社交）
	ReName = "re_name",								--更换名称(属于人物)
	ReNameGuild = "re_g_name",						--更换仙盟名称
	Qiuhun = "qiuhun",								--求婚界面（属于结婚）
	TiQin = "tiqin",								--提亲界面（属于结婚）
	Explain = "explain",							--结婚说明（属于结婚）
	BaiShi = "baishi",								--拜师界面（属于师徒）
	RoleTrace = "role_trace",						--追踪令
	RoleChangeCamp = "role_cahnge_camp",			--换阵营
	OneKeyAddFriend = "one_key_add_friend",			--一键添加(社交)
	ReRoleIcon = "re_role_icon",					--更换头像界面
}

--一个个小ui名字，用于引导中指向某个ui
GuideUIName = {
	-- G21 功能引导用到的 --------------------------------
	MainUIBag = "main_ui_bag",						--主界面-功能按钮
	BagOneKeySell = "bag_one_key_sell",				--背包-一键出售
	BagOneKeySellConfirm = "bag_one_key_sell_confirm",--背包-一键出售-确定
	BagAutoMelting = "bag_auto_melting",			--背包-一键熔炼
	BagAutoMeltingConfirm = "bag_auto_melting_confirm",	--背包-一键熔炼-确定
	MeltingShaiXuanBtn = "melting_shaixuanbtn",		--背包-一键熔炼-筛选按钮
	OpenServerAct = "open_server_act",				--主界面-开服活动
	MainuiJumpButton = "MainuiJumpButton",			--主界面-跳跃按钮
	MainuiLandingButton = "MainuiLandingButton",	--主界面-跳跃落地按钮
	JumpButtonUp = "JumpButtonUp",					--主界面-跳跃冲刺按钮
	BtnPortraitLabel = "BtnPortraitLabel",			--主界面-角色头像
	RoleUpJingjie = "RoleUpJingjie",				--提升境界按钮
	RoleJingjie = "RoleJingjie",					--角色-境界按钮
	TianShuBtn = "tian_shu_btn",					--开服活动-天书按钮
	TianShuViewGet = "tian_shu_view_get",			--天书界面-领取按钮
	FuBenWelKinStart = "fuben_welkin_start",		--副本天仙阁-开始挑战
	FuBenExpOneRole = "fuben_exp_one_role",			--副本经验--单人进入
	FuBenExpMoreRole = "btn_matching",			--副本经验--多人进入
	TeamPrepareBtn = "team_prepare_btn",			--副本经验--准备按钮
	FuBenEnterYGXDAlone = "fuben_enter_ysxd_alone",	--远古仙殿--单人进入
	FuBenEnterYGXDMore = "btn_enter_hte",	--远古仙殿--多人进入
	FuBenEnterHDFXMore = "btn_enter_hdfx",			--海底废墟（装备本）--多人进入
	FuBenPetStart = "fuben_pet_start",				--副本宠物本--进入
	FuBenTongbiStart = "fuben_tongbi_start",		--副本铜币本--进入
	FuBenTianShenEnter = "fuben_tianshen_enter",	--副本天神本--进入
	FuBenFRXZBox = "fuben_frxz_box",				--副本凡人修真宝箱
	FRXZRewardPanel = "frxz_reward_panel",			--副本凡人修真奖励界面
	FuWenXiangQianOne = "fuwen_xiang_qian_one",		--铭纹镶嵌 第一个孔
	FuWenXiangQianBag = "fuwen_xiang_qian_bag",		--铭纹镶嵌 背包
	FuWenXiangQianUp = "fuwen_xiang_qian_up",		--铭纹镶嵌 升级
	FuWenFenjieBtn = "fuwen_fenjie_btn",			--铭纹分解 分解
	FuWenBagOne = "fuwen_bag_one",					--铭纹背包 第一个格子
	WorldBossFatigue = "world_boss_fatigue",		--世界boss 疲劳显示
	WorldBossList = "world_boss_list",				--世界boss列表 第几只
	WorldBossEnter = "world_boss_enter",			--世界boss 进入
	PersonBossEnter = "person_boss_enter",			--个人boss 进入
	SettingGuaJiRonglian = "setting_guaji_ronglian",		--设置挂机自动熔炼
	SettingAddTime = "setting_add_time",			--设置界面加挂机时间按钮
	MainuiSetting = "setting",						--主界面设置按钮
	MainuiMenu = "MainuiMenu",						-- 主界面菜单按钮
	MainuiGuaji = "mianui_guaji",					-- 挂机按钮
	CloseBtn = "close_btn",							--关闭按钮
	OpenLingchongDevour = "open_lingchong_devour",	--宠物进阶
	LingchongDevourBtn = "lingchong_devour_btn",	--灵宠吞噬按钮
	AlertEnter = "alert_enter",						--确认框确定按钮
	EndPanelGoOn = "end_panel_go_on",				--结束界面继续挑战
	TaskShangJinGoOn = "task_shangjin_go_on",		--赏金任务接取按钮
	ComposeTipText = "compose_tip_text",			--合成提示文字
	ComposeAddOnekey = "compose_add_one_key",		--合成一键添加
	BtnSkillUpLevel = "btn_skill_uplevel",			-- 技能升级
	BtnSkillUpLeveling = "btn_skill_upleveling",	-- 技能自动升级
	MarketBagCell = "market_bag_cell",				-- 市场背包格子
	MarketBuyPanel = "market_buy_panel",			-- 市场购买界面
    BtnBootybayGoto = "btn_bootybay_goto",          -- 天帝陵前往挖宝
    BtnXiuzhenRoadShowWuxing = "xiuzhenroad_show_wuxing",          -- 修真路显示五行

	Tab = "tab",									--标签，参数：第几个索引（1开始）
	EquipCell = "equip_cell",						--装备格子，参数：装备类型
	BagCellById = "bag_cell_by_id",					--背包格子, 参数：通过物品id取得
	BagCellByType = "bag_cell_by_type",				--背包格子, 参数：通过物品类型取得
	GoldExchangeBtn		   = "gold_exchange_btn",					--背包元宝兑换
	BagGoldExchange		   = "bag_gold_exchange",					--元宝兑换按钮
	TakeonBtn = "takeon_btn",						--穿戴按钮(如在一键装备模块里)
	BuyBtn = "buy_btn",								--购买按钮
	VipTryBtn = "viptrybtn",						--VIP体验按钮
	VipCheckBtn = "vip_chec_kbtn",					--VIP前往查看
	FlyShoesGuide = "flyshoesguide",				--小飞鞋引导
	AddTitleBtn = "addtitlebtn",					--领取新称号


	EquipmentIntensifyList = "intensifylist_1",		--锻造界面强化列表
	EquipmentIntensifyBtn = "equipment_btn",		--立即强化按钮
	-- AutoEquipmentIntensifyBtn = "auto_equipment_btn",--自动强化按钮
	BtnEquipQhAuto = "btn_equip_qh_auto",           --自动强化按钮
	
	GuideFunBtn = "function_btn",					--功能按钮

	FuBenPanelJiangHuBtn = "fb_jianghu_btn",		--江湖副本按钮
	--FuBenPanelMaterialBtn = "fb_material_btn",		--进阶副本按钮
	FuBenPanelExpBtn = "fb_exp_btn",				--经验副本按钮
	FuBenPanelJuQingBtn = "fb_jq_btn",				--剧情副本按钮

	Field1v1SelectBtn = "select_third",				--竞技1v1按钮
	Field1v1Finish = "field1v1_finish",				--竞技场结算面板
	Field1v1RolePanel = "field1v1_rolepanel",		--竞技场选人界面
	KF3V3GotoBtn = "kf3v3_goto_btn",				--跨服3v3前往加入按钮
	KF3V3ZhanDuiBtn = "kf3v3_zhandui_btn",			--跨服3v3战队按钮
	NpcDialogLayoutBtn = "npc_dialog_layout_btn",	--npc对话按钮

	MeiRiBiZuoBtn = "BtnBiZuoView",					--每日必做

	MainUINuQiSkillBtn = "main_skill_nq",			--主界面怒气技能按钮

	GuildOneKeyBtn = "one_key",						--帮派一键申请

	ShrinkButton = "ShrinkButton",					--主界面右上角缩放按钮
	MainUIRoleHead = "main_ui_role_head",			--主角面-人物头像
	MainUIRoleBtn = "main_ui_role_btn",				--主界面-人物按钮
	MainUIEquipment = "main_ui_equipment_btn",		--主界面-装备按钮
	MainUIMount = "main_ui_mount_btn",				--主界面-坐骑按钮
	MainUIWing = "main_ui_wing_btn",				--主界面-羽翼按钮
	MainUIGuild = "main_ui_guild_btn",				--主界面-仙盟按钮
	MainUISociety = "main_ui_society_btn",			--主界面-社交按钮
	MainUIExchange = "main_ui_exchange",			--主界面-兑换按钮
	MainUISupply = "main_ui_supply_btn",			--主界面-补给按钮
	MainUIDayActivity = "main_ui_day_activity_btn",	--主界面-活动按钮
	MainUIBanBenActivity = "main_ui_banben_activity_btn",	--主界面-版本活动按钮
	MainUIDaily = "main_ui_daily_btn",				--主界面-日常按钮
	MainUIXunBao = "main_ui_xunbao_btn",			--主界面-寻宝按钮
	MainUIMarry = "main_ui_marry_btn",				--主界面-情缘按钮
	MainUIWelfare = "BtnFuLiView",					--主界面-福利按钮
	MainUIShop = "main_ui_shop_btn",				--主界面-商城按钮
	MainUINuqi = "main_ui_nuqi_btn",				--主界面-怒气按钮
	MainUIBoss = "BtnBossView",						--主界面-boss按钮
	MainUIJjc = "main_ui_jjc_btn",					--主界面-竞技场按钮
	MainUIJingLing = "main_ui_jingling_btn",		--主界面-精灵按钮
	MainUIPet = "main_ui_pet_btn",					--主界面-宠物按钮
	MainUIFabao = "main_ui_fabao_btn",				--主界面-法宝按钮
	MainUIAppearance = "main_ui_appearance_btn",	--主界面-时装按钮
	MainUICard = "main_ui_card_btn",				--主界面-卡牌按钮
	MainUIPaTaFb = "main_ui_patafb_btn",			--主界面-爬塔按钮
	-- MainUISkillBtn = "main_ui_skill_btn",			--主界面-技能按钮 参数：第几个索引（从1开始）
	MainUIFunc = "main_ui_func_btn",				--主界面-功能按钮
	BtnActJjcView = "BtnActJjcView", 				--主界面-竞技场
	BtnFPActJJC = "BtnFPActJJC", 				--主界面-天梯(竞技场)
	BtnQiFuView = "BtnQiFuView",                 --主界面-祈福
	BtnCultivationRealm = "BtnCultivationRealm",     -- 主界面幻兽
	
	ShopFunOpenItem = "shop_fun_open_item",			--商城功能开启物品item
	ShopCelShopItem = "cel_shop_item",			--商城功能限购物品item

	MainUIZS = "main_ui_zhuansheng_btn",			--主界面-转生按钮
	MainUIRecharge = "main_ui_recharge_btn",		--主界面-充值按钮
	MainUIInvest = "main_ui_invest_btn",			--主界面-投资计划按钮
	MainUIActivity = "main_ui_activity_btn",		--主界面-返利活动按钮
	ButtonZuoqi = "ButtonZuoqi",					--主界面-上坐骑按钮
	MainUiTianShenSkillS = "mainui_tianshen_skills", --主界面-天神技能大按钮
	MainUiTianShenSkill = "mainui_tianshen_skill",	--主界面-天神技能大按钮


	SkillItem = "skill_item",						--技能item，参数：第几个索引（从1开始）
	SkillLearnBtn = "skill_learn_btn",				--技能学习按钮

	ZhuanShengBtn = "zhuan_sheng_btn",					--转生按钮

	EquipLeveupBtn = "equip_levelup_btn",			--装备升级按钮 （在装备模块）
	EquipStrengthBtn = "equip_strength_btn",		--装备强化按钮（在装备模块）
	EquipUpQualityBtn = "equip_up_quality_btn",		--装备提升品质按钮（在装备模块）
	EquipActStoneBtn = "equip_actstone_btn",		--装备激活宝石按钮（在装备模块）
	StoneSelect = "stone_select",					--选择宝石（在副本模块）参数：第几个索引（从1开始）

	FishShopBtn = "fish_shop_btn",					--鱼塘进入商店按钮
	FishShopCell = "fish_shop_cell",				--鱼商店中的鱼格子
	FishShopFishingPool = "fish_shop_Fishing_pool",	--鱼商店中的养鱼的池塘区域

	MountLevelupBtn = "mount_levelup_btn",			--坐骑升级按钮（在坐骑模块）
	MountUpgradeBtn = "mount_upgrade_btn",			--坐骑进阶按钮（在坐骑模块）

	WingJingHuaBtn = "wing_jinghua_btn",			--羽翼进化按钮（在翅膀模块）

	FubenSelect = "fuben_select",					--选择副本按钮（在副本模块）参数：第几个索引（从1开始）
	FubenGoinBtn = "fuben_goin_btn",				--进入副本按钮（在副本模块）
	FubenStarAniView= "fuben_star_ani_view",		--副本星级面板
	FuBenStarView = "FuBenStarView",				--副本星级面板(新的，后面会删掉 FubenStarAniView)

	ItemTipBtn = "item_tip_btn", 					-- 物品tip按钮（参数index）

	KeyUseBtn = "key_use_btn",						-- 一键使用按钮

	XunBaoChuJi = "xunbao_chuji",					--初始寻宝按钮(在寻宝模块)
	XunBaoGaoJi = "xunbao_gaoji",					--高级寻宝按钮(在寻宝模块)
	XunBaoZhiJun = "xunbao_zhijun",					--至尊寻宝按钮(在寻宝模块)
	XunBaoKeyGet = "xunbao_keyget",					--寻宝一键取出(在寻宝模块)
	XunBaoStorge = "xunbao_storge",					--打开寻宝仓库(在寻宝模块)

	JingLingUpBtn = "jingling_up_btn",					--精灵升级按钮
	JingLingFightBtn = "jingling_fight_btn",			--精灵出战按钮
	JingLingBag = "jingling_bag",						--精灵背包
	JingLingBagCell = "jingling_bag_cell",				--精灵背包格子
	JingLingLie = "jingling_lie",						--精灵猎取
	JingLingSlot = "jingling_slot",					--精灵装备部位
	JingLingFind1 = "jingling_find1",					--精灵寻宝一次
	JinglingKeyGet = "jingling_keyget",				--精灵一键取出

	WelfareLvBtn = "welfare_lv_btn",				--福利等级奖励按钮

	ShopStrengthenIndex = "shop_strengthen_index",	--商城强化石
	ShopBuyBtn = "shop_buy_btn",					--商城购买图标

	ExchangeItem = "exchange_item",					--兑换item，参数：第几个索引（从1开始） (在兑换模块)
	ExchangeItemBtn = "exchange_item_btn",			--兑换物品按钮（在兑换模块）

	HusongRefreshBtn = "husong_refresh_btn",		--刷新按钮（在护送模块）
	HusongGoBtn = "husong_go_btn",					--护送按钮（在护送模块）

	DailyTaskGoBtn = "dailytask_go_btn",			--前往日常任务目标按钮（在日常任务模块）
	DailyJuQingCell = "daily_juqing_cell",			--剧情本选关
	DailyJuQingEnterBtn = "daily_jq_enter_btn",		--剧情本进入副本按钮

	GuildItem = "guild_item",						--仙盟item。参数:第几个索引（从1开始） (在仙盟列表模块)
	JoinGuildBtn = "join_guild_btn",				--加入仙盟 (在加入仙盟模块)
	GuildActItem = "guild_act_item",				--仙盟活动item

	OpenKeyAddFriend = "open_keyaddfriend",			--打开一键加好友(社交模块)
	KeyAddFriend = "keyaddfriend",					--一键加好友(社交模块)

	FaBaoActBtn = "fabao_act_btn",					--法宝激活召唤按钮

	PetActBtn = "pet_act_btn",						--宠物激活按钮

	UpJingmaitBtn = "up_jingmai_btn",				--经脉提升按钮

	EquipFbEnterBtn = "equipfb_enter_btn",			--装备副本进入按钮

	AppearanceJinjieBtn = "appearance_jinjie_btn",	--羽翼进阶按钮

	EquipTargetAct = "equip_target_act",			--装备目标激活按钮
	EquipTargetSkill = "equip_target_skill",		--装备目标技能栏
	EquipTargetAttr = "equip_target_attr",			--装备目标属性栏


	MainTask = "main_task",							--主线任务
	EquipmentStrength = "equipment_strength",		-- 锻造强化按钮
	ShiTuXiuLi = "ShiTuXiuLi",

	HuSongFlush = "husong_flush", 					-- 护送一键刷新
	StartHuSong = "start_husong", 					-- 开始护送
	
	XiuXianBtn = "xiuxian_btn", 					-- 主界面任务栏修仙试炼按钮
	BianQiang = "bian_qiang", 						-- 主界面变强按钮

	MainTaskBtn = "main_task_btn",					-- 主线任务按钮触发boss跳转
	BossEnterBtn = "boss_enter_btn",				-- 进入boss按钮
	BossNoticeCell = "boss_notice_cell",			-- 选择击杀boss

	YiNianMagic = "yinian_magic",                   -- 一念神魔

	LilianGoAssign = "lilian_go_assign",            -- 前往委托按钮
	AssignTaskStar = "assign_task_star",      		-- 任务星级介绍
	AssignTaskCost = "assign_task_cost",      		-- 时间跟消耗历练值介绍
	AssignTaskCondition = "assign_task_condition",  -- 任务条件
	AssignOneKey = "assign_one_key",  				-- 点击一键派遣
	AssignStart = "assign_start",  					-- 点击开始
	AssignGetReward = "assign_get_reward",  		-- 领取奖励
	AssignClose = "close_assign",  					-- 关闭委托界面
	LilianClose = "close_lilian",  					-- 关闭历练界面
	ZhuXianBianShen ="bianshen_show_skill",			-- 主线天神变身按钮
	UseBeastsSkill = "beast_skill_01",				-- 使用幻兽技能
	TriggerFirstTaskGuide = "first_task_guide",		-- 引导当前第一个任务
	TriggerTaskActJjc = "first_task_act_jjc_guide",	-- 引导当前第一个任务到竞技场

	QTEView = "QTEView",                           -- QTE
	
	BeastsDraw = "operate_ten_btn",					-- 幻兽抽奖
	BiZuoDaliyTask = "BiZuoDaliyTask",				-- 日常-任务
	BiZuoDaliyVerTab = "BiZuoDaliyVerTab",			-- 日常-标签
	WangQiUseClick = "wangqi_skill_trigger",		-- 主界面使用望气
	BtnShowTask = "btn_show_task",					-- 拼图任务入口
	BtnLingQu = "btn_lingqu",						-- 拼图任务领取
	Jigsaw_1 = "bx_list_item_1",					-- 第一块拼图
	Jigsaw_2 = "bx_list_item_2",					-- 第二块拼图
	VerticalTabbarCell_Activity = "VerticalTabbarCell_Activity",	-- 标签
	BtnCloseWindow = "btn_close_window",			-- 关闭按钮
	JigsawAwardItem = "jigsaw_award_item",			-- 进度奖励

	BillionSubsidyDCListFirst = "BillionSubsidyDCListFirst",	--处理引导时百亿补贴优惠券列表
	BillionSubsidyDCList = "BillionSubsidyDCList",	--百亿补贴优惠券列表
	BillionSubsidyVerTab = "BillionSubsidyVerTab",	--百亿补贴大页签
	BillionSubsidyTenList = "BillionSubsidyTenList",	--百亿补贴-百亿补贴商店商品列表
	BillionSubsidyBuyTipList = "BillionSubsidyBuyTipList",	--百亿补贴购买弹窗列表
	BillionSubsidyActiveVipHigh = "BillionSubsidyActiveVipHigh",--百亿补贴购买VIP界面

	OneSwordFrostbiteTaskList = "OneSwordFrostbiteTaskList",--一剑霜寒任务列表
	OneSwordFrostbiteBoxList = "OneSwordFrostbiteBoxList",--一剑霜寒宝箱列表
}

--功能名字，用于功能开启。
--下面不一一列出游戏中所有功能，只提供大模块。
--若需要判断某模块下的标签功能是否开启，可结合TabIndex用Funopen中的GetFunIsOpenedByTabName()
FunName = {
    Role = "role",									--人物
	Bag = "bag",									--角色背包
	RoleView = "role_view",							--人物
	BagView = "bag_view",							--背包
	Equipment = "equipment",						--装备
	PetView = "PetView",							--宠物
	EquipmentBaoShiJL = "equipment_baoshi_jl",
	JinJIeHuanZhuang = "jinjiehuanzhuang", 			--进阶幻装（灵宠坐骑战骑）用作标签功能开启
	SkillView = "skill_view",						--技能
	skill_zhudong = "skill_zhudong",				-- 主动技能
	skill_beidong = "skill_beidong",				-- 被动技能
	skill_talent = "skill_talent",					-- 天赋技能
	ShenEquip = "shen_equip",						--神装
	EquipOld = "equip_old",							--原装备
	EquipFuling = "equip_fuling",					--装备附灵
	Mount = "mount",								--坐骑
    ShengWang = "shengwang",						--声望
    LingChongEquipView = "LingChongEquipView",
    MountEquipView = "MountEquipView",
    HuaKunEquipView = "HuaKunEquipView",			--化鲲装备
	Appearance = "appearance",						--外观
	Wing = "wing",									--翅膀
	Guild = "guild",								--仙盟
	Exchange = "exchange",							--兑换
	Society = "society",							--社交
	CombineSociety = "combine_society",				--综合社交
	Welfare = "welfare",							--福利
	ActMaze = "act_maze",							--迷宫寻宝
	DayActivity = "day_activity",					--活动
	ActJjc = "act_jjc",								--斗法封神
	ActIvityHall = "activity_hall",					--活动大厅
	Daily = "daily",								--日常
	Other = "other",								--综合
	ShouChong = "first_recharge",					--首充入口
	first_recharge_shouchong = "first_recharge_shouchong",	--首充--首充
	first_recharge_zhigou = "first_recharge_zhigou",		--首充--直购
	ActivityMainPanel = "activity_main_panel",      --所有活动的面板
	ActvityIcon = "activity",						--活动图标
	OpenActivity = "open_activity",					--开服活动(菜单按钮)
	OpenServer = "open_server",				     		--开服活动
	BanBenActivity = "banben_activity",             --版本活动
	CombineActivity = "combine_activity",			--合服活动
	FanliActivity = "fl_activity",					--返利活动
	Actvity = "activity_",							--活动
	XunBao = "xunbao",								--寻宝
	WenXinRemind = "wenxin_remind",					--温馨提示
	Supply = "supply",								--补给
	JiuHui = "jiuhui",								--酒会
	WardrobeView = "WardrobeView",					--衣橱
	HmGodView = "HmGodView",                        -- 鸿蒙神藏
	GuiXuDreamView = "GuiXuDreamView",              -- 归墟梦演
	GoldStoneView = "GoldStoneView",				-- 金石玉言直购
	HolyDarkWeaponEnter = "HolyDarkWeaponEnter",	-- 圣器暗器入口
	HolyWeapon = "HolyWeapon",                      -- 圣器
	DarkWeapon = "DarkWeapon",                      -- 暗器
	ArtifactView = "ArtifactView",                  -- 仙魔神器
	ArtifactAffection = "ArtifactAffection",		-- 双修-好感度
	YinianMagicView = "YinianMagicView",            -- 一念神魔
	DragonTempleView = "DragonTempleView",          -- 龙神殿
	dragon_temp_longhun = "dragon_temp_longhun",    -- 龙神殿--龙神
	dragon_temp_equip = "dragon_temp_equip",        -- 龙神殿--装备
	dragon_temp_hatch = "dragon_temp_hatch",        -- 龙神殿--孵化
	dragon_temp_draw = "dragon_temp_draw",          -- 龙神殿--秘宝
	dragon_temp_rank = "dragon_temp_rank",          -- 龙神殿--排行
	-----
	HuanHuaFetterView = "HuanHuaFetterView",               -- 幻化羁绊
	NewHuanHuaFetterView   = "NewHuanHuaFetterView",       -- 幻化羁绊
	huanhua_fetter_waiguan ="huanhua_fetter_waiguan",      -- 幻化羁绊-- 外观
	huanhua_fetter_lingchong = "huanhua_fetter_lingchong", -- 幻化羁绊-- 灵宠
	huanhua_fetter_mount = "huanhua_fetter_mount",         -- 幻化羁绊-- 坐骑
	huanhua_fetter_kun = "huanhua_fetter_kun",             -- 幻化羁绊-- 鲲

	new_huanhua_fetter_zuoqi = "new_huanhua_fetter_zuoqi",-- 幻化羁绊  坐骑
	new_huanhua_fetter_lingqi = "new_huanhua_fetter_lingqi",-- 幻化羁绊 灵气
	new_huanhua_fetter_fabao = "new_huanhua_fetter_lingqi",-- 幻化羁绊 法宝

	--Cornucopia = "cornucopia",							--聚宝盆
	ShenJiang = "shenjiang",						--神将
	JingLing  = "jingling",							--精灵
	BigHorn = "big_horn",							--大喇叭
	ZhuanSheng = "zhuansheng",							--转生
	Boss = "boss",									--boss
	BossFake = "boss_fake",							--假boss
	Drugstore = "drugstore",						--药店
	Storage = "storage",							--仓库
	Fabao = "fabao",								--法宝

	TianShenView = "TianShenView",					-- 天神
	TianshenHuanhuaJinjie ="tianshen_huanhua_jinjie",						-- 天神幻化进阶
	ShanHaiJingView = "ShanHaiJingView",			-- 山海经
	ShanHaiJingLSCView = "ShanHaiJingLSCView",		-- 山海经--千秋绝艳图

	BiZuo = "bizuo",								--每日必做
	MiJing = "mijing",								--秘境
	Welkin = "welkin",								--天仙阁
	Wujinjitan = "wujinjitan",						--无尽祭坛
	KuafuYeZhanWangCheng = "kuafuYeZhanWangCheng",  --夜战王城
	-----------
	RebateReel = "rebate_reel",						--返利卷轴
	FubenReel = "fuben_reel",						--副本卷轴
	FuncReel = "func_reel",							--功能卷轴
	---------
	JingYanYu = "jingyanyu",						--经验玉

	NoticeTwo = "notice_two",						--功能预告2
	NoticeThree = "notice_three",					--功能预告3
	OpenServerMenu = "open_server_menu",					--开服活动菜单

	HideTask = "hide_task",							--隐藏任务
	Shileding = "shileding",						--屏蔽别人
	NearPlayer = "near_player",						--附近玩家
	Attack = "attack",								--攻击模式
	TeamIcon= "teamicon",							--组队图标
	Shop = "shop",									--商城

	RoleChangePro = "role_change_prof",				--转换职业界面

	Husong = "husong",								--护送
	Marry = "marry",									--结婚模块
	Divorce = "divorce",							--离婚
	MarryFB = "marryfb",							--结婚副本
	Recharge = "recharge",							--充值界面
	RechargeUi = "rechargeui",						--主界面充值ui
	Jinyinta = "jinyinta",							--金银塔
	Chongzhiniuegg = "chongzhiniuegg",				--充值扭蛋
	HightRebate = "hight_rebate",                   --高倍返利
	Huanlezadan = "huanlezadan",
	HuanlezadanRank = "huanlezadan_rank",
	OtherSetting = "other_setting",					--设置
	Rank = "rank",						--排行
	Compose = "other_compose",					--炼炉
	ComposeTicket = "compose_ticket",				--合成门票
	Market = "market",					--市场
	ProfessWallView = "ProfessWallView",			--表白墙
	MysteriousShop = "mysterious_shop",				--神秘商店
	CampPanel = "camp_panel",						--阵营模块
	Meridian = "meridian",							--修炼
	Master = "master",								--师徒
	TeJie = "tejie",							--特戒
	FuBenPanel = "fubenpanel",						--副本
	FuBenPanelExp = "fubenpanel_exp",				--经验副本


	QuintupleExp = "quintuple_exp",					--五倍经验

	GuildJianXian = "guild_jianxian",				--仙盟捐献
	GuildShenshou = "guild_shenshou",				--仙盟神兽

	DailyTask = "daily_task",						--日常任务
	HusongTask = "husong_task",						--护送任务
	guild_task = "guild_task",						--仙盟任务

	AutoOnMount = "auto_on_mount",					--自动上坐骑
	Direction = "decoration_",						--场景装饰物（后面sceneid_decorationid)
	Gather = "gather_",								--采集物（后面sceneid_gatherid)
	Npc = "npc_",									--npc(后面npcid)

	ZhanShenDian = "zhanshendian",					--战神殿
	Award = "award",								--活跃度奖励
	UpdateAffiche = "updateaffiche",				--更新公告
	UpdateAffichezonghe = "updateaffichezonghe",	--更新公告(综合里显示图标)
	ManyTower = "manytower",						--多人塔防
	PaTaFb = "pata_fb",								--通天塔
    TianShenJuexing = "TianShenJuexing",		  -- 天神觉醒
    XiuXianShiLian = "XiuXianShiLian",		        -- 修仙试炼
	Carnival = "carnival",								--七日狂欢
	GoldReversion = "gold_reversion",					--百倍返利
	SuperGift = "super_gift",						--消费引导
	Map = "map",									--地图显示
	RechargeAgain = "recharge_again",					--再充
	RechargeThird = "recharge_third",					--三充
	DailyConsume = "daily_consume",					--每日累消
	DailyTotalRecharge = "daily_total_recharge",		--累充
	ActivityBipin = "activity_bipin",					--比拼活动
	InvestPlan = "invest_plan",						--投资计划
	VIPTouZi = "recharge_month_card",				-- VIP投资

	AgentPromote = "agent_promote",					--平台推广
	SuperVip = "super_vip",							--至尊VIP
	JingHua = "jinghua",								--精华
	YaoShouPlaza = "yaoshouplaza",					--妖兽广场
	---------------------------------------------------------
	ZhuZaiShenDian = "zhuzaishendianview",				--主宰神殿
	QIFU = "qifu",
	-----------------------------------------------------------
	PigTreasure = "pig_treasure",					--金猪探宝
	TreasureLoft = "treasure_loft",					--珍宝阁
	TimeLimitFeedback = "timeLimitFeedback",		--限时回馈
	MiJingTaoBao = "mijingtaobao",					--秘境淘宝
	PleaseDrawCard = "please_draw_card",			--陛下请翻牌
	CollectBless = "collect_bless",			--翻牌集福
	PerfectLover = "perfect_lover",			--完美情人
	LotteryTree = "lottery_tree",					--摇钱树
	ConsumerScore = "consumer_score",				--消费积分
	New_Three_Suit = "new_three_suit",              --新三件套
	Single_Recharge = "single_recharge",              --新单笔充值
	Mine = "mine",									--翡翠矿场
	MoLong = "molong",								--龙行天下
	ContinuouslyRecharge = "continuously_recharge",			--正版连续充值2086
	RebateRecharge = "rebate_recharge",											--充值返利
	TOTAL_CHONGZHI = "total_chongzhi",											--累计充值
	TOTAL_CONSUME = "total_consume",											--累计消费
	FunnyShoot = "funny_shoot",						--趣味射门
	DailyLove = "daily_love",						--每日一爱
	SingleRebate = "single_rebate",					--单笔返利
	SuperTransmit = "super_transmit",				--超级传送
	RandActivityHall = "rand_activityhall",			--活动收缩
	FriendsBlessing = "friends_blessing",			--好友祝福
	FriendsReturn = "friends_return",				--好友回赠
	Blessing = "blessing",							--祝福
	FragmentExchange = "fragment_exchange",			--碎片兑换
	ConsumeDiscount = "consume_discount",			--连消特惠
	PlantingTree = "planting_tree",					--植树
	Fanfanzhuan = "fanfanzhuan",					--翻翻转
	Fishpond = "fishpond",							--灵池
	RepeatRecharge = "repeat_recharge",				--循环消费
	SpeedRecharge = "speed_recharge",				--至尊充值排行
	RechargeReturnReward = "recharge_return_reward",--充值狂返元宝
	RunningMan = "running_man",						--奔跑吧仙友
	KuafuRecharge = "kuafu_recharge",				--跨服排行榜
	KuafuConsume = "kuafu_consume",					--跨服消费榜
	WeGetMarried = "wegetmarried",					--咱们结婚吧
	KuafuOneVsN = "kuafuonevsn",						--跨服1vn
	MagicUtensil = "magic_utensil",					--魔器
	TeShuFace = "teshu_face",						--特殊表情
	CangBaoGe = "cangbaoge",						--藏宝阁
	TeamFb = "newteamfb",							--跨服组队本
	GongCheng = "gongcheng",						--攻城战
	KuafuOneVsOne = "kuafuonevsone",				--跨服1v1
	KfOneVOneMatch = "kuafuonevonematch",			--跨服1v1匹配
	PvPRank = "pvp_rank",							--跨服PvP排行
	PvPSeasonReward = "pvp_season_reward",			--跨服PvP赛季奖励
	PvPGongXunReward = "pvp_gongxun_reward",		--跨服PvP功勋奖励
	Answer = "answer",                              --答题
	SevendayInvestplan = "sevenday_investplan",		--七天投资计划
	TianJi = "tianji",								--天机
	GoldMember = "gold_member",						--黄金会员
	SingleRechargeOne = "single_recharge_one",		--我要充值1,现改成 冲战新星1
	SingleRechargeTwo = "single_recharge_two",		--我要充值2,现改成 我要冲战2
	SingleRechargeThree = "single_recharge_three",	--我要充值3,现改成 急速冲战3
	SingleRechargeFour = "single_recharge_four",	--我要充值4,现改成 战榜提名4
	Immortal = "Immortal",							--仙尊卡
	MysteryHouse = "mystery_house",					--神秘小屋
	MingHun = "ming_hun",							--命魂
	KuafuGuildBattle = "kuafu_guild_battle",	--跨服帮派战
	WorldServer = "worldserver", 					--世界服

	FuWen = "fuwen",								-- 铭纹
	ShenShou = "shenshou",                          -- 神兽
	Achievement = "achievement",                    -- 成就

    TreasureHunt = "TreasureHunt",                  --新寻宝

	NewTeam = "new_team",                           -- 组队
	Fishing = "fishing",							--钓鱼
	SkyRedenvelopes = "sky_redenvelopes",           --天降红包
	GuildAnswer = "guildanswer",                    -- 帮派答题
	GuildJiuSheDaoFB = "guildjiushedaofb",                    -- 帮派副本
	OpenServerRank = "open_server_rank",            -- 开服活动排行榜
	ExpPool = "exp_pool",                           -- 经验池
	CallBoss = "call_boss",           					-- boss召唤
	KuafuBoss = "worserv_boss_mh",                       -- 跨服boss
	BossTujian =  "boss_tujian",					-- boss图鉴
	ChargeRepayment2 = "charge_repayment2",		    -- 充值回馈
	BossVip = "boss_vip",							--boss之家
	boss_world = "boss_world", 						--世界魔王
	PersonalBoss = "boss_personal",                 --个人boss
	DaBaoBoss = "boss_dabao",						--打宝boss
	ShengYuBoss = "boss_shengyu", 					--圣域boss
	YuLuoBoss = "worserv_boss_gdzc", 				--陨落boss
	ShangGuBoss = "worserv_boss_sgyj", 				--上古boss
    HMSYBoss = "worserv_boss_hmsy",					--鸿蒙神域
    ShenYuanBoss = "world_new_shenyuan_boss",		--深渊魔王
    XianJieBoss = "xianjie_boss",                   --仙界boss
	EveryDayRechargeBoss = "worserv_everyday_recharge_boss",-- 每日真充boss
	MiJingBoss = "boss_mijing", 					--秘境boss
	GuildBoss = "guild_boss", 						--帮派boss
	GuildWar = "guild_War", 						--帮派争霸
	GuildShouHu = "guild_shouhu", 					--帮派守护
	GuildDaTi = "guild_dati", 						--帮派答题
	KF1V1 = "arena_kf1v1", 							--跨服1V1
	KFPVP = "arena_kf3v3", 								--跨服PVP（3V3）
	RechargeVip = "recharge_vip",					--充值vip界面
	RechargeKingVip = "recharge_king_vip",			--王者特权
	RechargeTQTZ = "recharge_tqtz",					--充值特权投资界面
	RechargeLeichong = "recharge_leichong",			--VIP-累充
	RechargeXiaofei = "recharge_xiaofei",			--VIP-消费
	RechargeMonthcard = "recharge_month_card",		--充值月卡投资界面
	RechargeWeekcard = "recharge_week_card",   		--充值月卡投资
	RechargeReservecard = "recharge_reserve_card",	-- 攒福特权界面
	FubenpanelEquip = "fubenpanel_equip",           --多人装备本
	HappyConsume = "rand_happy_consume",				--消费欢乐颂
	HappyRecharge = "rand_happy_recharge",			--充值欢乐颂
	RechargeGift = "act_recharge_gift",             --充值好礼
	RechargeGift2 = "act_recharge_gift2",                 --充值好礼2

	XUNBAOQIBAO = "equipxunbao_xunbao",				--装备寻宝-奇宝
	XUNBAOZHENBAO = "equipxunbao_zhenbao",			--装备寻宝-稀宝
	XUNBAODIANFENG = "equipxunbao_dianfeng",        --装备寻宝-巅峰
	LuckBuy = "luck_buy",			--幸运云购
	LimitedShop = "limited_shop",					--限时特卖
	ZhanLing = "zhanling",							--战令
	CountryMapMapView = "counrty_map_map_view", 			-- 版图界面
	CountryMapActView = "country_map_act_view",
	EquipTargetView  = "rolebag_Target",					--装备目标
	MainEquipTargetView  = "main_ui_Target",					--装备目标
	FightSoulView = "FightSoulView",				-- 四象
	FairyLandPlaneView = "FairyLandPlaneView", -- 元神位面
	FairyLandRuleView = "FairyLandRuleView", -- 元神法则
	FairyLandEquipmentView = "FairyLandEquipmentView", -- 仙界装备
	InfiniteHell = "infinite_hell",                 -- 无尽神狱
	FubenpanelEquipHigh = "fubenpanel_equip_high",-- 副本-高级多人装备
	FubenpanelManHuangGuDian = "fubenpanel_manhuanggudian",-- 副本-蛮荒古殿
	FubenpanelCopper = "fubenpanel_copper", 		--副本铜币本
	FubenpanelBaGuaMiZhen = "baguamizhenview", 		--副本八卦迷阵
	FubenpanelTianShen = "fubenpanel_tianshen", 	--副本天神
	FubenpanelPet = "fubenpanel_pet", 				--副本守护圣灵（宠物本）
	fubenpanel_zhuogui = "fubenpanel_zhuogui",
	fubenpanel_equip_high = "fubenpanel_equip_high",
	fubenpanel_control_beasts = "fubenpanel_control_beasts",
	fubenpanel_beauty = "fubenpanel_beauty",
	fubenpanel_wuhun = "fubenpanel_wuhun",
	fubenpanel_rune_tower = "fubenpanel_rune_tower",
	fubenpanel_bootybay = "fubenpanel_bootybay",
	FubenpanelWelkin = "fubenpanel_welkin", 		--副本凡人修真
	FlowerRank = "flower_rank",						--情人鲜花榜
	ConsumeRankThree = "cunsume_rank_three",					--消费排行
	DailyRecharge = "daily_recharge",					--每日累充
	TeHuiShop = "tehui_shop",						-- 特惠秒杀
	TitleBackGroundView = "title_background_view",				-- 称号与背景
	Browse = "browse",								--查看信息

	ActOpenServertwo = "open_server_compete",		-- 开服比拼
	ActOpenServer = "open_server_activity",			-- 开服活动
	ActOpenServerScroll = "open_server_scroll",		-- 上古卷轴
	ActOpenServerInvest = "open_server_invest",		-- 投资计划
	ActOpenServerRecharge = "open_server_recharge",	-- 七天累充
	ActOpenServerLanding = "open_server_landing",	-- 七天登陆

	other_compose_eq_hecheng_lingchong = "other_compose_eq_hecheng_lingchong",  -- 骑宠合成
	other_compose_eq_hecheng_xiaogui = "other_compose_eq_hecheng_xiaogui",		-- 小鬼合成
	rolebag_longhun = 'rolebag_longhun',			-- 龙脉
	welfare_kuanghuan = 'welfare_kuanghuan',
	welfare_hide = "welfare_hide",
	welfare_qifu = "welfare_qifu",
	qifu_yunshi = "qifu_yunshi",				--运势
	welfare_online_reward = "welfare_online_reward",
	welfare_upgrade = "welfare_upgrade",   -- 等级礼包
	welfare_vipgift = "welfare_vipgift",   -- vip礼包

	VipExperience = "vip_experience", -- vip体验
	ShenShouBianShenAppearance = "shenshou_bianshen_appearanceview",

	rank_zhanli = 'rank_zhanli',               --战力榜
	rank_level = 'rank_level',
	rank_meili = 'rank_meili',
	rank_xianmeng = 'rank_xianmeng',
	rank_lingchong = 'rank_lingchong',
	rank_yuyi = 'rank_yuyi',
	rank_zuoqi = 'rank_zuoqi',
	rank_fabao = 'rank_fabao',
	rank_shenbing = 'rank_shenbing',
	rank_lingqi = 'rank_lingqi',
	rank_guaji = 'rank_guaji',
	rank_linggong = 'rank_linggong',
	rank_bianshen = 'rank_bianshen',
	rank_equip = 'rank_equip',
	rank_jinjie = 'rank_jinjie',
	rank_fanrenxiuzhen = 'rank_fanrenxiuzhen',
	SpecialGiftBag = "SpecialGiftBag",
	rank_jianzhen = "rank_jianzhen",
	rank_baby = "rank_baby",
	rank_xianqi = 'rank_xianqi',--仙器榜
	rank_competition = "rank_competition",
	rank_dujie = "rank_dujie",
	rank_beast = "rank_beast",
	TianShenLingHeView = "TianShenLingHeView",				-- 天神灵核
	tianshen_activation = "tianshen_activation",			--天神激活（幻化）
	tianshen_battle = "tianshen_battle", 			--天神出战
	tianshen_shenshi = "tianshen_shenshi",			--天神神饰
	tianshen_heji = "tianshen_heji",                -- 天神合击
	tianshen_linghe_uplevel = "tianshen_linghe_uplevel",	--天神灵核升级
	tianshen_linghe_reslove = "tianshen_linghe_reslove",	--天神灵核分解
	tianshen_linghe_compose = "tianshen_linghe_compose",	--天神灵核融合
	tianshen_shenQi = "tianshen_shenQi",				-- 天神神器
	tianshen_shentong = "tianshen_shentong",			--天神神通
	tianshen_bagua = "tianshen_bagua",					--天神八卦牌
	MarryNotice	 = "marry_notice",						--结婚预告
	XiuZhenRoadView = "XiuZhenRoadView",				--修真之路
	FanRenXiuZhenRewardView = "FanRenXiuZhenRewardView",--凡人修真奖励
	FanrenxiuzhenRewardPreview = "FanrenxiuzhenRewardPreview",  -- 凡人修真奖励总览
	ExpPoolView = "ExpPoolView", 						--经验池
	guild_skill = "guild_skill",
	baguamizhenview = "baguamizhenview",           		--八卦迷阵
	TianShenRoadPanel = "TianShenRoadPanel",			-- 天神之路

	fubenpanel_bagua = "fubenpanel_bagua",								--副本-八卦迷阵
	arena_field1v1 = "arena_field1v1",									--竞技场
	market_jisou_list = "market_jisou_list", 							-- 市场：我的上架
	market_buy = "market_buy",											-- 市场：世界拍卖

	role_refining = "role_refining",									-- 丹道
	qifu_qf = "qifu_qf",												-- 祈福
	society_friend = "society_friend",									-- 好友

	YinPiaoExchangeView = "yuanbao_exchange",							-- 元宝兑换
	YinPiaoExchangePopView = "yuanbao_exchange_popup",		-- 银票兑换弹窗
	recharge_week_buy = "recharge_week_buy",							-- 每周必买
	long_hun_xiangqian = "long_hun_xiangqian", 							-- 龙魂附身
	long_hun_uplevel = "long_hun_uplevel",							  	  -- 龙魂升级
	long_hun_flyup = "long_hun_flyup",								  	  -- 龙魂飞升
	MingWenView = "MingWenView",										--F2铭文面板
	ming_wen_xiang_qian ="ming_wen_xiang_qian",
	ming_wen_fen_jie = "ming_wen_fen_jie",
	ming_wen_dui_huan = "ming_wen_dui_huan",
	ming_wen_he_cheng = "ming_wen_he_cheng",
	LongHunView = "LongHunView",										--F2龙魂面板
	long_hun_xiang_qian = "long_hun_xiang_qian",
	--long_hun_he_cheng = "long_hun_he_cheng",
	ShiTuXiuLi = "ShiTuXiuLi",
	CalendarView = "CalendarView", 										-- 主界面周历
	NewTeamView = "NewTeamView",										-- 组队面板
	RoleSit = "role_sit",												-- 打坐

	operation_act_ctn_recharge = "operation_act_ctn_recharge", 			--运营活动_连续充值
	operation_act_fengzheng = "operation_act_fengzheng", 			    --运营活动_风筝夺宝
	operation_act_task_chain = "operation_act_task_chain",				--运营活动_任务链
	operation_act_xianshi_miaosha = "operation_act_xianshi_miaosha",	--运营活动_限时秒杀
	operation_act_first_recharge = "operation_act_first_recharge",		--运营活动_每日首冲
	operation_act_total_recharge = "operation_act_total_recharge",		--运营活动_限时累充
	operation_act_mowang_youli = "operation_act_mowang_youli", 			--运营活动_魔王有礼
	operation_act_watering_flowers = "operation_act_watering_flowers",	--运营活动_种花浇水
	operation_act_login_reward = "operation_act_login_reward",			--运营活动_登录有礼
	operation_act_mowu_jianglin = "operation_act_mowu_jianglin",		--运营活动_魔物降临
	operation_act_quanfu_juanxian = "operation_act_quanfu_juanxian", 	--运营活动_全服捐献
	operation_act_duobei = "operation_act_duobei",						--运营活动_多倍
	operation_act_chushen = "operation_act_chushen",					--运营活动_天才厨神
	operation_act_image_show = "operation_act_image_show",				--运营活动_形象展示
	operation_act_recharge_rank = "operation_act_recharge_rank",		--运营活动_充值排行

	jiucengyaota = "funbenpanel_yaota",							    --九层妖塔

	market_cross_server = "market_cross_server",			-- 市场-拍卖-跨服拍卖
	market_country_auction = "market_country_auction",		-- 市场-拍卖-国家拍卖
	market_guild_auction = "market_guild_auction",			-- 市场-拍卖-仙盟拍卖
	market_my_auction = "market_my_auction",				-- 市场-拍卖-我的竞拍
	market_auction_record = "market_auction_record",		-- 市场-拍卖-拍卖纪录

	tianshen_baoxia_box = "tianshen_baoxia_box",
	ScreenShotView = "ScreenShotView", 				--风景拍照

	CountryMapActSecret = "country_map_secret_area",        -- 国家秘境
	CountryMapAlchemy = "CountryMapAlchemy",                -- 国家炼丹
	CrossYangLongSi = "cross_yanglongsi",                   -- 跨服养龙寺
	YangLongSiHurtView = "YangLongSiHurtView",              -- 养龙寺伤害面板
	
	SiXiangCallView = "SiXiangCallView",					-- 四象召唤
	CapabilityContrastView = "CapabilityContrastView",		-- 战力对比
	FeedBackView = "feed_back_view",						--玩家反馈
	LongZhuView = "LongZhuView",
	MainUILongZhuSkill = "MainUILongZhuSkill",
	ChangeHeadView = "change_head_view",

	ShenJi = "ShenJi", 										--神机系统（暗器软甲）
	ShenJiNotice = "ShenJiNotice",							--神机预告
	--sixiang_call_xqzl = "sixiang_call_xqzl",					--神机百炼--页签
	sixiang_call_sx = "sixiang_call_sx",					--四象召唤--页签

	YiYuanHaoLiView = "YiYuanHaoLiView",					--一元豪礼
	XianyuTrunTableView = "XianyuTrunTableView",			--仙玉转盘
	WingLinZhiSkillView			 = "WingLinZhiSkillView",	-- 灵智技能  羽翼
	FaBaoLinZhiSkillView			 = "FaBaoLinZhiSkillView",				-- 灵智技能 法宝
	ShenBingLinZhiSkillView			 = "ShenBingLinZhiSkillView",			-- 灵智技能  神兵
	JianZhenLinZhiSkillView			 = "JianZhenLinZhiSkillView",			-- 灵智技能   剑灵
	EveryDayYinJiTurnTable 			= "everyday_recharge_yingji",			--印记转盘
	WorldsNO1View = "worlds_no1_view", 						-- 天下第一
	GuildHeBing = "guild_hebing",							-- 仙盟合并
	EquipmentMark = "equipment_yingji",                     -- 装备刻印
	TianShenLingHeDrawView = "TianShenLingHeDrawView",		-- 天神灵核抽奖
	ShenJiTianCiView = "ShenJiTianCiView",					-- 神技天赐
	CrossLongMaiView = "CrossLongMaiView",					-- 跨服龙脉
	ChaoticPurchaseView = "ChaoticPurchaseView",            -- 洪荒直购
	TianShenLiLianView = "TianShenLiLianView",				-- 天神历练
	FiveElementsTreasuryView = "FiveElementsTreasuryView",	-- 五行宝库
	HongHuangGoodCoremonyView = "HongHuangGoodCoremonyView",-- 洪荒豪礼
	ChaoticVipSpecialView = "ChaoticVipSpecialView",        -- vip特典
	HongHuangClassicView = "HongHuangClassicView",			-- 洪荒特典
	SystemForceLongShenView = "SystemForceLongShenView",    -- 系统预告——龙神降临
	FiveElementsView = "FiveElementsView",                  -- 五行
	five_elements_overview = "five_elements_overview",      -- 五行总览
	five_elements_knapsack = "five_elements_knapsack",      -- 五行天命
	five_elements_talent   = "five_elements_talent",        -- 五行天赋
	five_elements_cangming = "five_elements_cangming",      -- 五行沧溟
	SupremeFieldsWGView = "SupremeFieldsWGView",			-- 独尊领域
	HomesView = "HomesView",                                -- 家园
	SwornView = "SwornView",                                -- 结义金兰
	SwornEquipAttrView = "SwornEquipAttrView",				-- 结义装备套装激活
	GodOfWealthView = "GodOfWealthView",                    -- 喜迎财神
	CashPointView = "cash_point",                           -- 现金点
	RechargeVolume = "recharge_volume",                     -- 充值卷
	LongXiView = "LongXiView",								-- 受命于天
	SuperDragonSeal = "super_dragon_seal",					-- 龙玺
	DragonKingToken = "dragon_king_token",                  -- 令牌
	ShiTianSuitView = "ShiTianSuitView",					-- 弑天套装
	SeasonPrivilegeShop = "season_privilege_shop",			-- 季卡特权商店
	SupPrivilegeShop = "sup_privilege_shop",				-- 至尊特权商店
	PrivilegeShop = "privilegeshop",				-- 至尊特权商店
	PrivilegedGuidanceView = "PrivilegedGuidanceView",			-- 特权引导
	MultiFunctionView = "MultiFunctionView",                                -- 符咒
	--AccumulativeLoginView = "AccumulativeLoginView",		-- 累计登录
	CharmHolySeal = "charm_holy_seal",                      -- 符咒装备
	CharmLingZhu = "charm_lingzhu",                         -- 符咒灵珠
	RoleBranchView = "RoleBranchView",						-- 角色-万象
	sky_curtain = "sky_curtain",							-- 奇境
	custom_action = "custom_action",						-- 自定义动作 - 动作
	TianShenHuamoView = "TianShenHuamoView",				-- 天神化魔
	TianShenShuangShengView = "TianShenShuangShengView",	-- 天神双生
	BossMustFallPrivilegeView = "BossMustFallPrivilegeView",-- boss必爆
	WuHunView = "WuHunView",                                -- 武魂
	WuHunCultivateView = "WuHunCultivateView",				-- 武魂培养
	HolyHeavenlyDomainView = "HolyHeavenlyDomainView",
	NewFightMountView = "NewFightMountView",                -- 新战斗坐骑
	CustomizedSuitView = "CustomizedSuitView",				-- 定制技能
	BoundlessJoyView = "BoundlessJoyView",                  -- 长乐未央
	ControlBeastsView = "ControlBeastsView",			-- 驭兽系统
	BeastsBattle = "BeastsBattle",							-- 驭兽系统--出战
	BeastsCulture = "BeastsCulture",						-- 驭兽系统--培养
	BeastsRefining = "BeastsRefining",						-- 驭兽系统--炼根
	BeastsStable = "BeastsStable",							-- 驭兽系统--闲厩
	BeastsCompose = "BeastsCompose",						-- 驭兽系统--合成
	BeastsKing = "BeastsKing",								-- 驭兽系统--兽王
	BeastsContract = "BeastsContract",						-- 驭兽系统--奇遇
	beasts_alchemy = "beasts_alchemy",						-- 驭兽系统--内丹
	ControlBeastsContractWGView = "ControlBeastsContractWGView",	-- 驭兽抽奖
	HolyBeastsView = "HolyBeastsView",						-- 创世圣兽
	CustomizedRumorsView = "CustomizedRumorsView",          -- 定制传闻
	TreasurePalace = "TreasurePalace",                    	-- 臻宝殿
	CangJinShopView = "CangJinShopView",                    -- 藏金商铺（功能）
	CangJinExchangeView = "CangJinExchangeView",            -- 藏金商铺（兑换）
	cangjin_exchange_suit = "cangjin_exchange_suit",        -- 藏金商铺（兑换—---套装)
	cangjin_exchange_tequan = "cangjin_exchange_tequan",    -- 藏金商铺（兑换—---特权)
	cangjin_exchange_shop = "cangjin_exchange_shop",        -- 藏金商铺（兑换—---商铺)
	cangjin_exchange_privilege = "cangjin_exchange_privilege",-- 藏金商铺 特权商店
	cangjin_exchange_exchange = "cangjin_exchange_exchange", --藏金商铺 限时兑换
	MechaView = "MechaView",								-- 机甲
	mecha_fighter_plane = "mecha_fighter_plane",            --机甲战机                  
	mecha_wing = "mecha_wing",            					--机甲机翼   
	mecha_weapon = "mecha_weapon",            				--机甲武器    
	mecha_to_fight = "mecha_to_fight",            			--机甲出战    
	mecha_equip = "mecha_equip",            				--机甲装备
	esoterica = "esoterica",								--秘笈
	EverythingUnderTheSun = "EverythingUnderTheSun",		-- 森罗万象
	SXBX_xianyi = "sxbs_xianyi",                            -- 仙翼属性宝石
	SXBX_FaBao = "sxbs_fabao",                              -- 法宝属性宝石
	SXBX_ShenWu = "sxbs_shenwu",                            -- 神武属性宝石
	SXBX_BeiShi = "sxbs_beishi",                            -- 背饰属性宝石
	SXBX_Mount = "sxbs_mount",                              -- 灵宠属性宝石
	SXBX_Pet = "sxbs_pet",                                  -- 坐骑属性宝石
	JingHuaShuiYue = "JingHuaShuiYue",						-- 镜花水月
	StrangeCatalogView = "strange_catalog",					-- 奇闻异录
	wuhun_tower = "wuhun_tower",							-- 武魂塔
	wuhun_front = "wuhun_front",							-- 武魂魂阵
	CultivationView = "CultivationView",					-- 境界
	AssignmentView = "AssignmentView",						-- 委托任务
	EsotericaSkillShow = "EsotericaSkillShow",              -- 仙技技能预览
	MengLingView = "MengLingView",                          -- 梦灵
	PositionalWarfareView = "PositionalWarfareView",        -- 阵地战
	LandWarFbPersonView = "LandWarFbPersonView",            -- 阵地战(单人副本)
	ConquestWarView = "ConquestWarView",    				-- 征战
	LongYunZhanLingTaskView = "LongYunZhanLingTaskView",	-- 龙云战令
	LordEveryDayShopView = "LordEveryDayShopView",          -- 领主每日商店 魔王仙藏
	FashionExchangeShopView = "FashionExchangeShopView",	-- 时装兑换商店
	WorldTreasureView = "WorldTreasureView",                --天财地宝
	WorldTreasurePursuitTaskView = "WorldTreasurePursuitTaskView", -- 天财地宝-迷影寻踪任务
	tcdb_login_gift = "tcdb_login_gift",
	tcdb_first_recharge = "tcdb_first_recharge",
	tcdb_total_recharge = "tcdb_total_recharge",
	tcdb_jigsaw = "tcdb_jigsaw",
	tcdb_flash_sale = "tcdb_flash_sale",
	tcdb_flash_sale1 = "tcdb_flash_sale1",
	tcdb_flash_sale2 = "tcdb_flash_sale2",
	tcdb_jianglin = "tcdb_jianglin",
	BossNewPrivilegeView = "BossNewPrivilegeView",			--Boss特权，再爆一次特权、boss麻痹、Boss自动寻找特权
	boss_mabi_skill = "boss_mabi_skill",					--Boss-猎魔宝典.
	boss_godwar = "boss_godwar",							--Boss-战神特权.
	boss_zaibaoyici = "boss_zaibaoyici",					--Boss-再爆一次.
	boss_kill_every = "boss_kill_every",					--Boss-秒杀.
	NewAppearanceUpgradeLingChong = "new_appearance_upgrade_lingchong",
	SecretRecordView = "SecretRecordView",    		    	-- 紫云秘录
	ArenaTianTi = "arena_tianti",                          -- 天梯争霸
	BillionSubsidy = "BillionSubsidy",						--百亿补贴.
	billion_subsidy_dailygift = "billion_subsidy_dailygift",--百亿补贴.-礼包
	billion_subsidy_rapidly = "billion_subsidy_rapidly",	--百亿补贴.-秒杀
	--NewAppearanceWaiGuanLingChong = "new_appearance_waiguan_lingchong",		-- 珍稀外观 - 神武
	NewAppearanceLingChongUpstar = "new_appearance_lingchong_upstar",		-- 外观-灵剑升星
	NewAppearanceLingChongUpgrade = "new_appearance_lingchong_upgrade",		-- 外观-灵剑升阶
	CrossAirWarView = "CrossAirWarView",					-- 跨服空战
	BossEntrance = "boss_entrance",							-- Boss入口
	NewAppearanceDyeView = "NewAppearanceDyeView",			-- 时装染色	
	RoleDiyAppearanceView = "RoleDiyAppearanceView",        -- 捏脸
	DressingRoleDiyView = "DressingRoleDiyView",            -- 易容
	ControlBeastsPrizeDrawWGView = "ControlBeastsPrizeDrawWGView",	
	DujieView = "DujieView",                                -- 渡劫
	XiuWei = "XiuWei",										-- 修为
	LifeIndulgenceView = "LifeIndulgenceView",				-- 终身特惠
	HundredEquipView = "HundredEquipView",                  -- 百倍爆装
	HundredEquipRatio = "HundredEquipRatio",                  -- 黄金龙巢
	YanYuGeEntranceView = "YanYuGeEntranceView",            -- 烟雨阁 活动
	YanYuGePrivilegeView = "YanYuGePrivilegeView",          -- 烟雨特权
	YanYuGeExchangeShopView = "YanYuGeExchangeShopView",    -- 烟雨阁兑换商店
	YanYuGeNobleView = "YanYuGeNobleView",                  -- 赞助特权
	YanYuGePrivilegeNZTQ = "YanYuGePrivilegeNZTQ",          -- 烟雨阁 - 哪吒特权 
	YanYuGePrivilegeWLTZ = "YanYuGePrivilegeWLTZ",          -- 烟雨阁 - 未来投资 
	YanYuGePrivilegeYYTQ = "YanYuGePrivilegeYYTQ",          -- 烟雨阁 - 烟雨特权 
	YanYuGePrivilegeZZTQ = "YanYuGePrivilegeZZTQ",          -- 烟雨阁 - 赞助特权
	YanYuGeShopTZSD = "YanYuGeShopTZSD",                    -- 烟雨阁 - 投资商店
	YanYuGeShopNWSD = "YanYuGeShopNWSD",					-- 烟雨阁 - 女娲商店
	ThunderManaSelectView = "ThunderManaSelectView",        -- 雷法-总界面
	ShadyThunderView = "ShadyThunderView",                  -- 雷法-阴
	SunThunderView = "SunThunderView",                      -- 雷法-阳
	NewAppearanceHaloWGView	= "NewAppearanceHaloWGView",		-- 新形象（光环）

	NewAppearanceWGView = "NewAppearanceWGView",				-- 时装
	new_appearance_upgrade_mount = "new_appearance_upgrade_mount",	-- 时装- 进阶 - 坐骑
	new_appearance_upgrade_wing = "new_appearance_upgrade_wing",	-- 时装- 进阶 - 羽翼
	new_appearance_upgrade_fabao = "new_appearance_upgrade_fabao",	-- 时装- 进阶 - 法宝

	ExpGuideWGView = "ExpGuideWGView",						-- 升级引导
	EveryDayRechargeView = "EveryDayRechargeView",			-- 每日充值.
	everyday_recharge_leichong = "everyday_recharge_leichong",	--累充--累充.
	everyday_recharge_zhigou = "everyday_recharge_zhigou",		--累充--直购.
	everyday_recharge_xiaofei = "everyday_recharge_xiaofei",	--累充--消费.
	everyday_recharge_rapidly = "everyday_recharge_rapidly",	--累充--秒杀.
	everyday_recharge_lianchong = "everyday_recharge_lianchong",--累充--连充.
	everyday_recharge_dailygift = "everyday_recharge_dailygift",--累充--每日礼包.
	TreasurehuntBoss = "treasurehunt_boss",					-- 寻宝boss
	ConquestWarKFKZ = "conquest_war_kfkz",					-- 跨服空战界面
	PrivilegeCollectionView = "PrivilegeCollectionView",    -- 特权合集
	WangqiView = "WangqiView",								-- 望气按钮
	DragonTrialView = "DragonTrialView",					-- 龙神试炼

	GameAssistant = "GameAssistant",						-- 游戏小助手
	HalfPurchaseView = "HalfPurchaseView",				-- 五折直购卡

	PrivilegeCollectionSQCY = "PrivilegeCollectionSQCY",	-- 圣器残页
	RechargeVolumeView = "recharge_volume",                 -- 充值卷
	RechargeVolumeRewardView = "RechargeVolumeRewardView",  -- 充值卷提取奖励
	SevenDay             = "sevenday",							--十五天登录
	CrossTreasureView = "CrossTreasureView",				-- 跨服藏宝

	pri_col_shtq = "pri_col_shtq",          -- 特权 - 守护特权
	treasurehunt_thunder = "treasurehunt_thunder", --雷法抽奖
	
	-- AppearanceSoulUp = "AppearanceSoulUp",					-- 外观器魂开启条件
}-- FunName end

--标签名字。注意下面的名字不能更改，配置表里会用到。代表票签几
--首位是tabbar索引，每个模块段之间+10。确保下面数字唯一
-- math.floor(tonumber(tab_index) / 1000)得到tabbar用到的索引。
-- 可用SetTabIndex动态改变
TabIndex = {
	skill_zhudong = 11,						--技能-主动技能
	skill_awake = 12,						--技能-觉醒
	--skill_upgrade = 20,						--技能-进阶
	skill_beidong = 20,						--技能-被动技能
	skill_talent = 30,						--技能-天赋

	luxury_gift = 10,						--开服助力-登录豪礼
	high_point = 20,						--开服助力-开服嗨点
	gudao_jizhan = 30,						--开服助力-孤岛激战

	zl_main_reward = 10,					-- 战令-战令奖励
	zl_task_panel = 20,						-- 战令-战令任务
	zl_shop_panel = 30,						-- 战令-战令商城

	role_intro = 10,						--人物-总览
	role_refining = 20,						--人物-丹道
	role_change = 30,						--人物-换角
	jingjie = 40,							--境界
	jingmai = 50,							--经脉

	equipment_strength      = 11,				--锻造-强化
	equipment_xilian        = 12,				--锻造-洗炼
	equipment_shengpin      = 13,				--装备升品
	--equipment_yingji		= 14,				--装备印记

	equipment_baoshi        = 21,				--锻造-宝石
	equipment_baoshi_jl     = 22,				--锻造-宝石-精炼
	equipment_lingyu	    = 23,				--锻造-灵玉

	equipment_suit 			= 30,				--套装
	-- equipment_suit_zhumo 	= 31,				--锻造-卓越
	-- equipment_suit_one      = 32,				--锻造-完美
	-- equipment_suit_two      = 33,				--锻造-史诗

	equipment_zhuanhua      = 40,           -- 转化

	equipment_imperial_spirit_set           = 51,           -- 御灵镶嵌
	equipment_imperial_spirit_strengtn      = 52,           -- 御灵强化

	equipment_zhushen = 61,						--锻造-铸神
	equipment_zhushentai = 62,					--锻造-铸神台

	equipment_suit_eq		= 11,				--装备
	equipment_suit_sp		= 12,				--饰品

	EquipmentTarget				= 10,		-- 装备目标
	EquipmentIntegration		= 20,		-- 装备集成

	huanzhuang_lingchong = 10,				-- 灵宠-幻装
	huanzhuang_mount = 20,					-- 坐骑-幻装

	guild_info = 10,					--仙盟-信息
	guild_member = 21,					--仙盟-成员
	guild_guildlist = 22,				--仙盟-列表
	guild_history = 23,					--仙盟-历史记录
	guild_wage = 31,					--仙盟工资
	guild_redpacket = 32,				--仙盟-红包
	guild_baoxiang = 33,				--每日宝箱
	guild_cangku = 34,					--仙盟-仓库
	guild_skill = 35,					--仙盟技能
	guild_shop = 36,					--仙盟商店
	guild_sign = 37,					--仙盟签到
	guild_active_task = 38,				--仙盟活跃任务
	guild_act = 40,						--仙盟-活动
	guild_boss = 41,						--仙盟-boss活动
	guild_dati = 42, 						--仙盟-答题
	guild_shouhu = 43,						--仙盟-守护
	guild_War = 50,						--仙盟-新仙盟战
	guild_assign = 61,					--仙盟-雇佣上架
	guild_my_assign = 62,				--仙盟-我的雇佣

	

	society_mail = 20,					--社交-邮件
	society_friend = 10,					--社交-好友
	master_info = 31,					--师徒-信息
	master_worship = 32,					--师徒-供奉
	society_enemy = 40,					--社交-仇人
	society_buyu= 50,					--社交-黑名单
	--society_tab_b= 50,					--社交-捕鱼
	-- society_temp = 40114,					--社交-组队

	master_wuxingshenjiang = 52,			--师徒-五行神将
	master_fb = 53,						--师徒-副本
	master_boss = 54,					--师徒-boss
	master_equip = 55,					--师徒-装备
	master_wash = 56,					--师徒-鉴定
	master_recycle = 57,				--师徒-熔炼

	season_privilege_shop = 10,					--季卡特权商城
	sup_privilege_shop = 20,					--至尊特权商城

	setting_screen = 10,					--设置-设置
	setting_guaji = 20,						--设置-挂机
	--welfare_libao = 30,						--设置-兑换
	setting_contect = 30,					--设置-GM

	welfare_qiandao = 10,					--福利-每月签到
	-- welfare_kuanghuan = 60,					--福利-七日狂欢
	-- welfare_qiandao = 11,
	-- welfare_hide = 13,					--隐藏福利
	-- welfare_zaixian = 12,				--福利-在线礼包
	-- welfare_friend_req = 13,				--福利-好友畅玩(邀请)
	--welfare_online_reward = 20,			--福利-在线礼包
	welfare_wekqiandao = 20,             --福利-周签到
	welfare_upgrade = 30,				--福利-冲级
	welfare_vipgift = 40,				--vip礼包
	-- welfare_gonggao = 50,				--福利-更新公告(从单独界面移到福利面板里)
	welfare_libao = 50,					--福利-领取礼包
	welfare_phone_bind = 60,				--福利-手机绑定

	world_paper = 11,					--世界红包
	--welfare_qifu = 10,					 --祈福
	marry_jiehun       = 10,                -- 结婚
	marry_profess_wall = 20,            	-- 表白墙
	marry_hunjie       = 30,				-- 情缘信物
	marry_baby         = 40,				-- 宝宝
	-- marry_baoxia       = 50,                -- 宝匣
	-- marry_flowers      = 50,				-- 赠花
	marry_fb           = 50,				-- 副本

	-- marry_dianchun     = 70,            	-- 点唇

	flower_upgrade     = 10,                -- 花圃
	flower_send        = 20,                -- 赠花

	--祈福和鸿蒙感悟
	qifu_qf = 10,
	qifu_hmwd = 20,
	qifu_yunshi = 30,						-- 运势
	qifu_getengrgy = 40,                    -- 获取仙力

	profess_wall_select_friend = 11, 		-- 表白选择对象
	profess_wall_select_friend2 = 12, 		-- 表白选择对象
	send_flower_obj = 13,					--选择赠送鲜花的对象

	profess_wall_xunyuan     = 10, 			-- 寻缘
	profess_wall_all         = 20, 			-- 表白
	
	profess_wall_to        	 = 20,			-- 对我表白
	profess_wall_from        = 30,			-- 我的表白
	profess_wall_rank_male   = 40,			-- 男神榜
	profess_wall_rank_female = 50,			-- 女神榜

	shop_limit = 11,					--商城-每周限购
	shop_moyu = 12,						--商城-绑定
	shop_daoju = 13,					--商城-道具
	shop_fashion = 14,					--商城-时装
	shop_hot = 15,						--商城-元宝
	shop_honor = 16,					--商城-荣誉
	shop_shengwang = 17,                --商城-声望
	shop_chivalrous = 18,               --商城-侠义值

	market_buy = 21,						-- 市场-购买
	market_sell = 22,						-- 市场-出售
	market_record = 23,						-- 市场-销售记录

	market_cross_server    = 31,				-- 市场-拍卖-跨服拍卖
	market_country_auction = 32,			-- 市场-拍卖-国家拍卖
	market_guild_auction   = 33,				-- 市场-拍卖-仙盟拍卖
	market_my_auction      = 34,					-- 市场-拍卖-我的竞拍
	market_auction_record  = 35,				-- 市场-拍卖-拍卖纪录

	duck_race_bet = 10, 					-- 下注
	duck_race_results = 20, 				-- 结算

	equipxunbao_xunbao = 10,				--真龙寻宝
	fuwen_xunbao = 20,						--铭纹寻宝
	xuanshi_xunbao = 30,					--玄狮寻宝

	--equipxunbao_dianfeng = 30,            	--装备寻宝-巅峰
	--equipxunbao_zhenbao = 40,				--装备寻宝-稀宝
	--equipxunbao_wushuang = 50,				--装备寻宝-无双(封神)
	----equipxunbao_exchange = 60,			--装备寻宝-兑换
	--equipxunbao_all_log = 61,				--装备寻宝-全服-纪录
	--equipxunbao_my_log = 62,				--装备寻宝-个人-纪录

    treasurehunt_fuwen = 20,				--符文寻宝 新
	treasurehunt_equip = 10,                --装备寻宝 新
	treasurehunt_dianfeng = 30,				--巅峰寻宝 新
	treasurehunt_zhizun = 40,				--至尊寻宝 新
	treasurehunt_thunder = 50,              --雷法寻宝


	sixiang_call_sx = 10,					--四象召唤 四象

	--sixiang_call_xqzl = 20,					--仙器真炼

	fight_soul_train = 10,					--四象 养成
	fight_soul_compose = 20,				--四象 融合
	fight_soul_bone = 30,					--四象 魂骨
	fight_soul_cuizhuo = 40,				--四象 淬琢

	--fairy_land_eq_god_book = 10,			--仙界装备 天书
	fairy_land_eq_god_body = 11,			--仙界装备 神体
	fairy_land_eq_holy_equip = 12,			--仙界装备 圣装
	fl_eq_forge_strengthen = 13,			--仙界装备 锻造 强化
	fl_eq_forge_evolve = 14,				--仙界装备 锻造 进化
	fl_eq_forge_upquality = 15,				--仙界装备 锻造 升品

    boss_world = 30,						--世界boss
	boss_personal = 20,                  	--个人boss
	boss_vip = 10,							--VIPboss
	boss_dabao = 40,						--打宝地图
	boss_drop_record = 50,                	--世界boss掉落

	boss_shengyu = 60,						--圣域boss
	boss_mijing = 70,						--秘境boss
	kuafu_drop_record = 80,               	--跨服boss掉落

	boss_tujian  = 27,						--boss图鉴


	sheng_qi_equip = 10,                    --圣器

	rolebag_bag_all = 10,					--背包-背包
	rolebag_bag_equip = 12,					--背包-装备
	rolebag_bag_other = 13,					--背包-其他
	rolebag_stuff = 20,            	    	--背包-材料仓库
	rolebag_storge = 30,					--背包-仓库
	-- rolebag_Target = 40,					--背包-装备目标
	rolebag_Integration = 40,				--背包-装备集成
	rolebag_longzhu = 50,				    --背包-星宿降临 星陨怒兵
	-- rolebag_equip_transsex = 40,            --背包-装备性别转换

	----------------副本
	fubenpanel_single = 10,					-- 副本-单人
	fubenpanel_team = 20,					-- 副本-组队

	fubenpanel_welkin = 11,					-- 副本-天仙阁
	fubenpanel_copper = 15,					-- 副本-铜币
	fubenpanel_pet = 13,					-- 副本-宠物本
	fubenpanel_bagua = 14,					-- 副本-八卦迷阵
	fubenpanel_exp = 12,					-- 副本-组队-经验
	fubenpanel_zhuogui = 16,				-- 副本-捉鬼

	fubenpanel_equip_high = 21,				-- 副本-组队-海底废墟-高级多人装备
	fubenpanel_beauty = 22,                 -- 副本-女神
	fubenpanel_control_beasts = 23,         -- 副本-幻兽
	fubenpanel_wuhun = 24,                  -- 副本-武魂
	fubenpanel_rune_tower = 25,             -- 副本-符文塔
	fubenpanel_bootybay = 26,				-- 副本-藏宝湾
	fubenpanel_tianshen = 27,               -- 副本 原天神副本 封神台

	lover_pk_qcdj = 10,						-- 仙侣pk倾城对决
	lover_pk_fszz = 20,						-- 仙侣pk封神之战

	lover_pk_msg_jbjl = 10,                 -- 仙侣PK金榜奖励
	lover_pk_msg_fsjl = 20,                 -- 仙侣PK封神奖励
	lover_pk_msg_dzb = 30,                  -- 仙侣pk对战表

	arena_enter = 10,						-- 战场
	--arena_field1v1 = 20,					-- 竞技场
	arena_1v1rank = 24,						-- 1v1排行
	arena_tianti = 20,						-- 天梯争霸
	arena_kf1v1 = 30,						-- 跨服1v1
	arena_1v1jfreward = 32,					-- 1v1积分奖励
	arena_1v1paragraph = 33,				-- 1v1段位
	worlds_no1 = 40,						-- 天下第一
	tianshen_3v3 = 50,						-- 天神3V3

	arena_kf3v3 = 60,						-- 跨服3v3
	arena_3v3jfreward = 62,					-- 3v3积分奖励
	arena_3v3paragraph = 63,				-- 3v3段位
	arena_3v3rank = 64,						-- 3v3排行
	kf_guild_battle = 70,					-- 跨服帮派战

	tianshen_3v3_season_rank = 10, 			-- 天神3v3赛季排名面板本赛季标签

	kfonevone_ring = 10,					--王者之戒
	kfpvp_lingpai = 20,						--王者令牌

	bizuo_bizuo = 10,                     	-- 每日必做
	--bizuo_cross_server_pro = 20,			-- 跨服进度
	bizuo_act_hall = 20,                  	-- 活动大厅
	bizuo_cultivation = 30,					-- 修为
	get_stronger = 40,				  		-- 我要变强
	welfare_dailyfind = 50,				  	-- 奖励找回
	bizuo_time_table = 60,                 	-- 活动时间表

	recharge_cz = 10,                   	-- 充值
	recharge_zerobuy = 20,					-- 0元购
	recharge_vip = 30,						-- vip
	recharge_king_vip = 40,					-- 王者特权
	recharge_month_card = 51,				-- 月卡
	recharge_week_card = 52,				-- 周卡
	recharge_reserve_card = 53,				-- 攒福特权
	recharge_tqtz = 60,                 	-- 特权投资
	recharge_leichong = 70,                	-- 累充
	recharge_xiaofei = 80,           		-- 消费
	recharge_week_buy = 90,                	-- 每周必买
	recharge_week_privilege = 100,           -- 特权周卡


	recharge_tzjh = 80,                 	-- 投资计划
	recharge_zztz = 90,                 	-- 至尊投资
	recharge_jbp = 100,                  	-- 聚宝盆
	recharge_bosstz = 110,                  -- Boss投资
	recharge_sevenday = 120,             	-- 七天充值(开服累充)

	fuwen_xiangqian = 11,                 -- 铭纹镶嵌
	fuwen_fenjie = 12,                    -- 铭纹分解
	--fuwen_xunbao = 13,                    -- 铭纹寻宝
	fuwen_duihuan = 13,                   -- 铭纹兑换
	-- fuwen_hecheng = 14,                    -- 铭纹合成
	long_hun_xiangqian = 21,			  -- 龙魂附身
	long_hun_uplevel = 22,			  	  -- 龙魂升级
	long_hun_flyup = 23,			  	  -- 龙魂飞升

	-- shenshou_shenshou = 10,               -- 神兽
	shenshou_tianbing = 20,               -- 天兵
	shenshou_qilin = 30,					-- 麒麟
	shenshou_shengxiao = 40,               -- 生肖
	shenshou_bianshen = 50,				  -- 变身
	shenshou_shenzhuang = 60,				-- 天王神装
	--shenshou_qianghua = 20,               -- 强化

	achievement_achieve_totle = 10,		--成就总览
	achievement_achieve = 20,			--成就

	shj_shouhunzhuzhan = 10,
	shj_shouhunqianghua = 12,

	team_pingtai = 10, 			-- 组队平台
	team_my_team = 20, 			-- 我的队伍
	team_near = 40, 			-- 附近队伍
	team_clone = 30,            -- 分身            

	other_compose_eq_hecheng_one = 11,         -- 苍穹装备合成 -- 男性合成
	other_compose_eq_hecheng_two = 12,         -- 离歌装备合成 -- 女性合成
	other_compose_eq_hecheng_three = 13,       -- 饰品合成
	other_compose_eq_hecheng_shenshou = 14,    -- 神兽装备合成
	-- other_compose_eq_hecheng_tianshen = 15,    -- 天神神饰合成
	-- other_compose_eq_hecheng_lingchong = 16,   -- 灵宠坐骑合成

	-- other_compose_shengpin = 21,			   -- 仙器 升品
	--other_compose_zhenlian = 22,			   -- 仙器 真炼
	other_compose_shengjie = 21,			   -- 仙器 升阶
	other_compose_shengxing = 22,			   -- 仙器 升星

	other_compose_tianshen = 31,				-- 合成 天神
	other_compose_xiaogui = 32,					-- 合成 小鬼
	other_compose_longhu = 33,					-- 合成 龙魂
	other_compose_shenbing = 34,				-- 合成 神兵
	other_compose_beast = 35,					-- 合成 驭兽

	other_compose_taozhuang = 41,				-- 合成 套装
	--other_compose_shitian = 42,					-- 合成 弑天套装合成
	other_compose_daoju = 42,					-- 合成 道具
	other_xianqi_stuff = 43,					-- 仙器材料
	other_compose_back = 44,					-- 合成 奇境

	bianshen_appearance = 10,			--变身幻装
	bianshen_chongzhu = 20,				--变声重铸

	local_map = 10, 				--本地地图
	global_map = 20,				--世界地图

	worserv_boss_mh = 20,						--蛮荒神兽
	world_new_shenyuan_boss = 10,				--新加深渊boss
    xianjie_boss = 30,						    --仙界boss
	worserv_everyday_recharge_boss = 40,        --每日真充boss
	worserv_boss_hmsy = 50,						--鸿蒙
    worserv_boss_sgyj = 60,						--上古遗迹


	--worserv_boss_record = 50,					--跨服boss掉落记录
	worserv_gcz = 52,						--攻城战

	tianshen_activation = 10,		--天神激活
	tianshen_shenshi = 20,			--天神神饰
	tianshen_linghe_uplevel = 30,	--天神灵核升级
	tianshen_shenQi = 40,			--天神神器
	tianshen_bagua = 50,			--天神八卦牌
	tianshen_temple = 60,			--天神神殿

	shenshi_qianghua = 11,			--天神神饰强化
	shenshi_jicheng = 12, 			--天神神饰继承
	shenshi_taozhuang = 13,			--天神神饰套装

	-- tianshen_linghe_uplevel = 10,
	-- tianshen_linghe_reslove = 20,
	-- tianshen_linghe_compose = 30,

	--=============== 新形象 ================
	-- new_appearance_upgrade = 10,					-- 进阶
	new_appearance_upgrade_mount = 11,				-- 进阶 - 坐骑
	new_appearance_upgrade_wing = 12,				-- 进阶 - 羽翼
	new_appearance_upgrade_fabao = 13,				-- 进阶 - 法宝
	new_appearance_upgrade_shenbing = 14,			-- 进阶 - 神武
	new_appearance_upgrade_jianzhen = 15,			-- 进阶 - 背饰
	new_appearance_upgrade_lingchong = 16,			-- 进阶 - 灵宠

	--[[	全部移到新衣橱
	new_appearance_waiguan = 20,					-- 珍稀外观
	new_appearance_waiguan_body = 21,				-- 珍稀外观	- 时装
	new_appearance_waiguan_weapon = 22,				-- 珍稀外观 - 武器
	new_appearance_waiguan_halo = 23,				-- 珍稀外观 - 光环
	new_appearance_waiguan_wing = 24,				-- 珍稀外观 - 羽翼
	new_appearance_waiguan_fabao = 25,				-- 珍稀外观 - 法宝
	new_appearance_waiguan_shenbing = 26,			-- 珍稀外观 - 神武
	new_appearance_waiguan_jianzhen = 27,			-- 珍稀外观 - 背饰

	new_appearance_zhuangban = 20,					-- 装扮
	new_appearance_zhuangban_mask = 21,				-- 装扮 - 脸饰
	new_appearance_zhuangban_belt = 22,				-- 装扮 - 腰饰
	new_appearance_zhuangban_weiba = 23,			-- 装扮 - 尾巴

	]]
	new_appearance_zhuangban_shouhuan = 21,			-- 装扮 - 手环
	new_appearance_zhuangban_foot = 22,				-- 装扮 - 足迹
	new_appearance_zhuangban_photoframe = 23,		-- 装扮 - 相框
	new_appearance_zhuangban_bubble = 24,			-- 装扮 - 气泡

	-- new_appearance_lingchong = 30,					-- 灵宠
	new_appearance_lingchong_upstar = 31,			-- 灵宠 - 升星
	new_appearance_lingchong_upgrade = 32,			-- 灵宠 - 进阶
	new_appearance_lingchong_hualing = 33,			-- 灵宠 - 化灵

	-- new_appearance_mount = 40,						-- 坐骑
	new_appearance_mount_upstar = 41,				-- 坐骑 - 升星
	new_appearance_mount_upgrade = 42,				-- 坐骑 - 进阶
	new_appearance_mount_hualing = 43,				-- 坐骑 - 化灵

	-- new_appearance_kun = 50,						-- 仙鲲
	new_appearance_kun_upstar = 51,					-- 仙鲲 - 升星
	new_appearance_kun_upgrade = 52,				-- 仙鲲	- 进阶
	new_appearance_kun_hualing = 53,                -- 仙鲲 - 化灵

	new_appearance_multi_mount = 60,                -- 双人坐骑

	-- new_appearance_wardrobe = 70,					-- 衣橱


	--=============== 新形象 end ================


	jin_chiyou_shengge   = 11,			--蚩尤神格
	jin_chiyou_xinyun    = 12,			--蚩尤幸运
	jin_xingtian_shengge = 21,			--刑天神格
	jin_xingtian_xinyun  = 22,			--刑天幸运
	jin_nvwa_shengge     = 31,			--女娲神格
	jin_nvwa_xinyun      = 32,			--女娲幸运
	jin_xuanyuan_shengge = 41,			--轩辕神格
	jin_xuanyuan_xinyun  = 42,			--轩辕幸运
	jin_yandi_shengge    = 51,			--炎帝神格
	jin_yandi_xinyun     = 52,			--炎帝幸运
	-- jin_huangdi_shengge  = 61,			--黄帝神格
	-- jin_huangdi_xinyun   = 62,			--黄帝幸运


	tianshenroad_login	= 10,		    --登录有礼、多倍副本
	tianshenroad_first_recharge	= 20,	--每日充值
	tianshenroad_total_recharge	= 30,	--累计充值
	tianshenroad_shenqi	= 40,		    --神器
	tianshenroad_chongbang	= 50,		--冲榜
	tianshenroad_jianglin	= 60,		--天神降临
	tianshenroad_mowang	= 70,	        --魔王有礼
	-- tianshenroad_duobei	= 80,		    --多倍来袭
	tianshenroad_xianshi_miaosha = 80,  --限时秒杀

	paihangbang = 10,					-- 本服/本国排行榜
	cross_rank = 20,					-- 跨服/跨国国排行榜

	zhuzaishendian = 10,				-- 主宰神殿

	kf_pvp_3v3info = 10,				-- 跨服3v3_3v3信息

	kf_pvp_zhandui_info = 10,			-- 跨服3v3_战队信息
	--kf_pvp_season_rank_reward = 20,	-- 3V3赛季排行奖励
	kf_pvp_zhandui_rank_cur = 20,		-- 3V3本赛季排名
	kf_pvp_season_duanwei_reward = 30,	-- 3V3赛季段位奖励

	--kf_pvp_zhandui_rank_last = 20,		-- 3V3上一赛季排名

	tianshen_3v3_grade_reward = 10, 	-- 天神3V3赛季奖励
	tianshen_3v3_rank_reward = 20, 		-- 天神3V3排名奖励

	worlds_no1_single_match_reward = 10,-- 天下第一单场奖励
	worlds_no1_season_reward = 20, 		-- 天下第一赛季奖励

    mount_equip_bag = 10,             --坐骑装备_背包
    mount_equip_strength = 20,        --坐骑装备_强化
    mount_equip_shengpin = 30,        --坐骑装备_升品
    mount_equip_star = 40,            --坐骑装备_升星
    mount_equip_suit = 50,            --坐骑装备_套装收集
	mount_equip_resolve = 60,         --坐骑装备_凝品

    pet_equip_bag = 10,             --灵宠装备_背包
    pet_equip_strength = 20,        --灵宠装备_强化
    pet_equip_shengpin = 30,        --灵宠装备_升品
    pet_equip_star = 40,            --灵宠装备_升星
    pet_equip_suit = 50,            --灵宠装备_套装收集
	pet_equip_resolve = 60,         --灵宠装备_凝品

    huakun_equip_bag = 10,             --化鲲装备_背包
    huakun_equip_strength = 20,        --化鲲装备_强化
    huakun_equip_shengpin = 30,        --化鲲装备_升品
    huakun_equip_star = 40,            --化鲲装备_升星
    huakun_equip_suit = 50,            --化鲲装备_套装收集
	huakun_equip_resolve = 60,         --化鲲装备_凝品

	quanmin_beizhan_login	= 10,		-- 备战登录
	quanmin_beizhan_shouchong = 20,		-- 备战首充
	quanmin_beizhan_leichong = 30,		-- 备战累充
	quanmin_beizhan_duobei = 40,		-- 备战多倍
	quanmin_beizhan_longhun = 50,		-- 备战龙魂
	quanmin_beizhan_juanxian = 60,		-- 备战捐献
	quanmin_beizhan_cap	= 71,			-- 个人战力
	quanmin_beizhan_cap2 = 72,			-- 全服战力
	quanmin_beizhan_cap3 = 73,			-- 砍价战力
	quanmin_beizhan_haoli = 81,			-- 备战好礼tab1
	quanmin_beizhan_haoli2 = 82,		-- 备战好礼tab2
	quanmin_beizhan_haoli3 = 83,		-- 备战好礼tab3
	quanmin_beizhan_haoli4 = 84,		-- 备战好礼tab4
	quanmin_beizhan_laixi = 90,			-- 备战来袭
	quanmin_beizhan_turntable = 100,		-- 备战兔女郎
	quanmin_beizhan_longhun_rank = 100,			-- 龙魂冲榜

	-----------------仙器解封系列活动------------------
	xianqi_jiefeng_login	= 10,		-- 备战登录
	xianqi_jiefeng_shouchong = 20,		-- 备战首充
	xianqi_jiefeng_leichong = 30,		-- 备战累充
	xianqi_jiefeng_duobei = 40,			-- 备战多倍
	xianqi_jiefeng_longhun = 50,		-- 备战龙魂
	xianqi_jiefeng_juanxian = 60,		-- 备战捐献
	xianqi_jiefeng_cap	= 71,			-- 个人战力
	xianqi_jiefeng_cap2 = 72,			-- 全服战力
	xianqi_jiefeng_cap3 = 73,			-- 砍价战力
	xianqi_jiefeng_haoli = 81,			-- 备战好礼tab1
	xianqi_jiefeng_haoli2 = 82,			-- 备战好礼tab2
	xianqi_jiefeng_haoli3 = 83,			-- 备战好礼tab3
	xianqi_jiefeng_haoli4 = 84,			-- 备战好礼tab4
	xianqi_jiefeng_laixi = 90,			-- 备战来袭
	xianqi_jiefeng_turntable = 100,		-- 备战兔女郎
	xianqi_jiefeng_longhun_rank = 110,	-- 仙器冲榜

	daily_gift = 10,					-- 一本万利--每日礼包
	lianchong_gift = 20,				-- 一本万利--连充大礼
	yueka = 30,							-- 一本万利--月卡

	boss_xiezhu = 11,					-- boss协助
	kajia_xiezhu = 12,					-- 砍价协助

	ming_wen_xiang_qian = 10,			-- 铭文镶嵌
	ming_wen_fen_jie = 20,				-- 铭文分解
	ming_wen_dui_huan = 30,				-- 铭文兑换
	ming_wen_he_cheng = 40,				-- 铭文合成

	long_hun_xiang_qian = 10,			-- 龙魂镶嵌
	--long_hun_he_cheng = 20,				-- 龙魂合成

	qi_jing_bei_jing = 20,				-- 奇境背景

	cheng_hao = 10,						-- 称号
	sky_curtain = 20,					-- 天幕
	custom_action = 30,					-- 自定义动作 - 动作

	act_bipin = 10,						-- 开服比拼
	

	act_kf_gift = 10,					-- 开服礼包
	act_xianmeng_fengbang = 20,			-- 仙盟封榜
	act_kaizonglipai = 30,				-- 开宗立派
	act_boss_lieren = 40,				-- Boss猎人
	--act_xianmeng_zhengba = 50,			-- 仙盟争霸
	act_kf_jizi	= 50,					-- 开服集字
	act_kf_bysf = 60,				-- 比翼双飞（整合完美情人和全场热恋）
    act_lovers = 61,					-- 完美情人
    city_love = 62,					    -- 全城热恋
    profess_love = 63,					-- 爱的表白(已屏蔽)
	act_highpoint = 70,					-- 开服嗨点
	act_sevenday_recharge = 80,			-- 七日累充
	server_collect_card = 90,			-- 开服集卡
	--password_redpacket = 100,			-- 口令红包

	country_map_secret_area = 10, 		-- 国家版图-活动-国家秘境
	country_map_yanglongsi = 20, 		-- 国家版图-活动-养龙寺


	operation_act_recharge_rank = 10,		--动态运营活动，充值排行
	operation_act_first_recharge = 20,		--动态运营活动，每日首充
	operation_act_xianshi_miaosha = 30,		--动态运营活动，限时秒杀
	operation_act_task_chain = 40,		    --动态运营活动，任务链
	operation_act_total_recharge = 50,		--动态运营活动，限时累充
	operation_act_mowang_youli = 60, 		--动态运营活动，魔王有礼
	operation_act_mowu_jianglin = 70,		--动态运营活动，魔物降临
	operation_act_duobei = 80,				--动态运营活动，多倍有礼
    operation_act_turntable = 90,			--动态运营活动，兔女郎宝藏
    operation_act_login_reward = 100,		--动态运营活动，登录有礼
    operation_act_quanfu_juanxian = 110,	--动态运营活动，全服捐献
	operation_act_watering_flowers = 120,	--动态运营活动，种花浇水
	operation_act_fish = 130,				--动态运营活动，辛运锦鲤
	operation_act_happy_kuanghuan = 140,	--动态运营活动，幸福狂欢
	operation_act_ctn_recharge = 150,		--动态运营活动，连充豪礼
	operation_act_fengzheng = 160,		    --动态运营活动，风筝夺宝
	operation_act_chushen = 170,			--动态运营活动，天才厨神
	operation_act_exchange = 180,			--动态运营活动，兑换商店

    shenji_equip_detail = 1,         -- 神机装备详情
	shenji_equip_upgrade = 2,         -- 神机装备升级
    shenji_equip_compose = 3,       -- 神机装备打造

    first_recharge_shouchong = 10, 	--首充--首充
    first_recharge_zhigou = 20,		--首充--直购

	everyday_recharge_rapidly = 10,		--累充--秒杀
	everyday_recharge_dailygift = 20,   --累充--每日礼包
	everyday_recharge_leichong = 30, 	--累充--累充
	everyday_recharge_lianchong = 40,		--累充--连充
	everyday_recharge_yingji = 50,		--累充--印记
	everyday_recharge_zhigou = 60,		--累充--直购
	everyday_recharge_xiaofei = 70,     --累充--消费

	lingzhi_ql_skill = 10,		-- 器灵神技
	lingzhi_ql_up = 20,			-- 器灵修炼

	holy_dark_seal = 10,        -- 圣暗器封印
	holy_dark_equip_bag = 21,   -- 圣暗器背包
	holy_dark_equip_skill = 22, -- 圣暗器技能
	holy_dark_qianghua = 30,    -- 圣暗器强化
	holy_dark_upstar = 40,      -- 圣暗器升星
	holy_dark_jinjie = 50,      -- 圣暗器进阶

	five_elements_overview = 10,      -- 五行总览
	five_elements_knapsack = 20,      -- 五行总览
	five_elements_talent   = 30,      -- 五行总览
	five_elements_cangming = 40,      -- 五行沧溟

	sworn_start = 10,         -- 结义主界面
	sworn_apply = 20,         -- 结义申请
	sworn_task = 30,          -- 结义悬赏
	sworn_taoyuan = 40,       -- 结义桃园
	sworn_imprint = 50,       -- 结义金兰
	sworn_suit = 61,		  -- 结义装备-套装
	sworn_upstar = 62,		  -- 结义装备-升星
	sworn_uplevel = 63,		  -- 结义装备-升级

	dragon_temp_longhun = 10, -- 龙神殿--龙神
	dragon_temp_equip = 20,   -- 龙神殿--装备
	dragon_temp_hatch = 30,   -- 龙神殿--孵化
	dragon_temp_draw = 40,    -- 龙神殿--秘宝
	dragon_temp_rank = 50,    -- 龙神殿--排行

	huanhua_fetter_waiguan = 10,   -- 幻化羁绊-- 外观
	huanhua_fetter_lingchong = 20, -- 幻化羁绊-- 灵宠
	huanhua_fetter_mount = 30,     -- 幻化羁绊-- 坐骑
	huanhua_fetter_kun = 40,       -- 幻化羁绊-- 鲲

	new_huanhua_fetter_zuoqi = 10,
	new_huanhua_fetter_lingqi = 20,
	new_huanhua_fetter_fabao = 30,

	--charm_holy_seal = 11,     -- 符咒装备
	--charm_lingzhu = 12,       -- 符咒灵珠

	daohang_suit = 11,         -- 道行套装
	daohang_keling = 12,       -- 道行刻灵
	daohang_qianghua = 13,     -- 道行强化
	daohang_jinjie = 14,       -- 道行进阶
	daohang_keyin = 15,        -- 道行刻印
	daohang_kaiguang = 16,     -- 道行开光

	holy_heavenly_domain_details = 10,     --圣天神域详情
	holy_heavenly_domain_rank = 20,        --圣天神域名人堂


	country_map_actshow = 10,  			   -- 活动列表
	country_map_ultimate_battlefield = 20, -- 终极战场

	conquest_war_zxzc = 10,							-- 诛仙战场
	honorhalls_tower = 20,							-- 永夜幻都
	conquest_war_tjmc = 30,							-- 天玑迷城
	country_map_flag_grabbing_battlefield = 40,		-- 冠绝征战
	boss_invasion = 50,                             -- 烛龙来袭
	conquest_war_kfkz = 60,                         -- 跨服空战
	yanglongsi = 70,                                -- 龙宫(养龙寺)


	ultimate_battlefield_camp = 10, -- 终极战场 -- 阵营奖励
	ultimate_battlefield_score = 20, -- 终极战场 -- 积分奖励

	ultimate_battlefield_talent_lower = 10, -- 终极战场 -- 低级天赋
	ultimate_battlefield_talent_uper = 20, -- 终极战场 -- 高级天赋

	ultimate_battlefield_guess_history_one = 10, -- 终极战场 -- 竞猜记录第一回合
	ultimate_battlefield_guess_history_two = 20, -- 终极战场 -- 竞猜记录第二回合
	ultimate_battlefield_guess_history_three = 30, -- 终极战场 -- 竞猜记录第三回合
	ultimate_battlefield_guess_history_four = 40, -- 终极战场 -- 竞猜记录第四回合
	ultimate_battlefield_guess_history_five = 50, -- 终极战场 -- 竞猜记录第五回合

	wuhun_details = 10,			--武魂详情
	wuhun_tower = 20,			--武魂塔
	wuhun_front = 30,			--武魂魂阵

	-- cultivation = 10,			-- 境界
	--esoterica = 10,					-- 秘笈
	tiandao_stone = 10,         	-- 天道石
	anger_skill = 20,         		-- 爆气飞升
	equip_target = 30,         		-- 装备收集
	cross_air_war = 40,         	-- 跨服空战


	beasts_culture = 10,		-- 驭兽--幻兽养成
	beasts_battle = 20,			-- 驭兽--幻兽出战
	beasts_alchemy = 30,			-- 驭兽--幻兽丹药
	-- beasts_refining = 30,		-- 驭兽--幻兽星辰灵域
	-- beasts_contract = 40,		-- 驭兽--幻兽奇遇
	-- beasts_stable = 50,			-- 驭兽--幻兽孵化
	beasts_compose = 40,			-- 驭兽--幻兽合成
	beasts_book = 50,				-- 驭兽--幻兽图鉴
	-- beasts_depose = 70,			-- 驭兽--幻兽献祭

	beasts_prize_draw_type_1 = 10,				-- 驭兽--幻兽抽奖类型1
	beasts_prize_draw_type_2 = 20,				-- 驭兽--幻兽抽奖类型2
	beasts_prize_draw_type_3 = 30,				-- 驭兽--幻兽抽奖类型3

	beasts_alchemy_strengthen = 10,			-- 驭兽--幻兽丹药--强化
	beasts_alchemy_succinct = 20,			-- 驭兽--幻兽丹药--洗练
	beasts_alchemy_inherit = 30,			-- 驭兽--幻兽丹药--传承
	beasts_alchemy_break = 40,				-- 驭兽--幻兽丹药--分解

	holy_beasts_contract = 10,					-- 契书
	holy_beasts_spirit = 20,					-- 圣魂

	treasure_palace_enter_reward = 20,			-- 臻宝殿-进游戏福利
	treasure_palace_rebate = 10,				-- 臻宝殿-每日累充
	treasure_palace_everyday_recharge = 30,		-- 臻宝殿-每日累充
	treasure_palace_lifetime_recharge = 40,		-- 臻宝殿-终生累充
	treasure_palace_gift = 50,					-- 臻宝殿-每日累充

	mecha_fighter_plane = 10,   -- 战机
	mecha_wing          = 20,   -- 机翼
	mecha_weapon        = 30,   -- 武器
	mecha_to_fight      = 40,   -- 上阵
	mecha_equip         = 50,   -- 装备

	customized_rumors = 10,     	   -- 传闻
	customized_rumors_broadcast = 20,  -- 广播

	cangjin_exchange_privilege = 10, 	-- 藏金商铺 特权商店
	cangjin_exchange_shop = 20,  		-- 藏金商铺 积分兑换
	cangjin_exchange_suit = 30,  		-- 藏金商铺 套装
	cangjin_exchange_exchange = 40,  	-- 藏金商铺 限时兑换
	cangjin_exchange_tequan = 50,  		-- 藏金商铺 特权

	most_venerable = 10,			-- 最强仙尊
	cat_explore = 10,			-- 小猫探险 御剑修行

	skill_break_purchase = 10,			-- 技能突破直购

	sun_rainbow_lottery = 10,			-- 万象玄生 抽奖
	sun_rainbow_shop = 20,			-- 万象玄生 积分兑换

	dragon_king_token = 10,             -- 龙神之灵
	super_dragon_seal = 20,				-- 免伤守护


	shitian_suit_strengthen = 10,		-- 灵皇套装-强化
	shitian_suit_gemstone = 20,			-- 灵皇套装-镶嵌
	shitian_suit_infuse_soul = 30,		-- 灵皇套装-入灵

	mengling_bag = 10,         -- 梦灵背包
	mengling_strong = 20,      -- 梦灵强化
	mengling_suit = 30,        -- 梦灵套装
	mengling_compose = 40,     -- 梦灵合成

	boss_mabi_skill = 10,				--Boss-猎魔宝典.
	boss_godwar = 20,					--Boss-战神特权.
	boss_zaibaoyici = 30,				--Boss-再爆一次.
	boss_kill_every = 40,				--Boss-秒杀.

	jiangshan_ruhua = 10,				-- 江山如画

	god_of_wealth = 10,					--招财进宝
	god_of_wealth_lucky_star = 20,					--锦盒寻宝

	fashion_exchange_shop_low1 = 11,		--时装兑换商店-低级1.
	fashion_exchange_shop_low2 = 12,		--时装兑换商店-低级2.
	fashion_exchange_shop_low3 = 13,		--时装兑换商店-低级3.
	fashion_exchange_shop_low4 = 14,		--时装兑换商店-低级4.
	fashion_exchange_shop_low5 = 15,		--时装兑换商店-低级5.
	
	fashion_exchange_yanyu = 21,		--烟雨商店

	fashion_exchange_shop_high1 = 31,		--时装兑换商店-高级1.
	fashion_exchange_shop_high2 = 32,		--时装兑换商店-高级2.
	fashion_exchange_shop_high3 = 33,		--时装兑换商店-高级3.
	fashion_exchange_shop_high4 = 34,		--时装兑换商店-高级4.
	fashion_exchange_shop_high5 = 35,		--时装兑换商店-高级5.

	fashion_exchange_shop_rare1 = 41,		--时装兑换商店-稀有1.
	fashion_exchange_shop_rare2 = 42,		--时装兑换商店-稀有2.
	fashion_exchange_shop_rare3 = 43,		--时装兑换商店-稀有3.
	fashion_exchange_shop_rare4 = 44,		--时装兑换商店-稀有4.
	fashion_exchange_shop_rare5 = 45,		--时装兑换商店-稀有5.
	
	fashion_exchange_shop_mid1 = 51,		--时装兑换商店-中级1.
	fashion_exchange_shop_mid2 = 52,		--时装兑换商店-中级2.
	fashion_exchange_shop_mid3 = 53,		--时装兑换商店-中级3.
	fashion_exchange_shop_mid4 = 54,		--时装兑换商店-中级4.
	fashion_exchange_shop_mid5 = 55,		--时装兑换商店-中级5.

	act_bipin_normal = 10,					-- 冲榜排行
	act_bipin_purchase = 20,				-- 超爽直购
	act_daily_recharge = 30,				-- 每日累充
	act_equip_cloud_buy = 40,				-- 装备云购
	act_equip_lottery = 50,					-- 装备抽奖

	tcdb_first_page = 10,				-- 通天降临-首页*
	tcdb_login_gift = 20,				-- 天财地宝-登录有礼
	tcdb_first_recharge = 30,			-- 天财地宝-首充送礼
	tcdb_flowing = 40,					-- 通天降临-川流不息*
	tcdb_raise_star_gift = 50,			-- 天财地宝-升星赠礼*
	tcdb_together = 60,					-- 通天降临-携手同行*
	tcdb_jigsaw = 70,					-- 天财地宝-拼图领奖
	tcdb_flash_sale = 80,				-- 天财地宝-限时抢购
	tcdb_flash_sale1 = 81,				-- 天财地宝-幻兽召唤
	tcdb_flash_sale2 = 82,				-- 天财地宝-材料商店
	tcdb_total_recharge = 90,			-- 天财地宝-累计充值*
	tcdb_jianglin = 100,				-- 天财地宝-试炼副本
	tcdb_mowang = 110,				    -- 天财地宝-幻境降魔(掉落)
	tcdb_pursuit = 120,				    -- 通天降临-迷影寻踪*
	tcdb_scheme = 130,					-- 通天降临-奕者谋定*
	tcdb_premium_shop = 140,			-- 通天降临-专享商店*


	recharge_volume_exchange = 10,		-- 代金券兑换
	recharge_volume_reward   = 20,		-- 代金券领取奖励

	xlgz_qmhb = 10,
	xlgz_qmlj = 20,

	billion_subsidy_bybt = 11,				--百亿补贴-百亿补贴.
	billion_subsidy_xdzk = 12,				--百亿补贴-限定折扣.
	billion_subsidy_fyms = 13,				--百亿补贴-付1买3.
	billion_subsidy_jkjzc = 14,				--百亿补贴-九块九专场.
	billion_subsidy_lysd = 15,				--百亿补贴-灵玉商店.
	billion_subsidy_dezg = 20,				--百亿补贴-大额直购.
	billion_subsidy_vip = 30,				--百亿补贴-会员专场.
	billion_subsidy_drpt = 40,				--百亿补贴-多人拼团.
	billion_subsidy_mrsy = 50,				--百亿补贴-每日试用.
	billion_subsidy_dailygift = 61,			--百亿补贴-礼包.
	billion_subsidy_rapidly = 62,			--百亿补贴-秒杀.
	billion_subsidy_yiyuan = 70,			--百亿补贴-一元福利.

	artifact_uplevel = 10,					--双修(仙魔神器)-升级
	artifact_upstar = 20,					--双修(仙魔神器)-升星
	artifact_affection = 30,				--双修(仙魔神器)-好感
	artifact_battle = 40,					--双修(仙魔神器)-出战

	glory_crystal_exchange_shop_1 = 10,		--天裳仙衣兑换商店1.
	glory_crystal_exchange_shop_2 = 20,		--天裳仙衣兑换商店2.

	yanyuge_privilege_wltz = 10,            -- 烟雨阁未来投资
	yanyuge_privilege_yytq = 20,            -- 烟雨阁烟雨特权
	yanyuge_privilege_nztq = 30,            -- 烟雨阁哪吒特权
	yanyuge_privilege_zztq = 40,            -- 烟雨阁赞助特权

	yanyuge_shop_nwsd = 10,                 -- 烟雨阁女娲商店
	yanyuge_shop_tzsd = 20,                 -- 烟雨阁套装商店

	wardrobe_suit = 10,						-- 衣橱--套装
	wardrobe_fashion = 20,					-- 衣橱--时装
	wardrobe_jewelry = 30,					-- 衣橱--饰品
	wardrobe_effect = 40,					-- 衣橱--特效

	pri_col_zztq = 10,                      -- 特权合集 - 至尊特权
	pri_col_nstq = 20,                      -- 特权合集 - 女神特权
	pri_col_shtq = 30,                      -- 特权合集 - 守护特权
	pri_col_zjtq = 40,                      -- 特权合集 - 终极特权
	pri_col_sqcy = 50,                      -- 特权合集 - 圣器残页

	assistant_tongpiao  	= 10,			-- 通票指南
	assistant_today_act 	= 20,			-- 今日玩法
	assistant_today_must	= 30,			-- 灵玉必买
	assistant_hot_buy  		= 40,			-- 热门直购
	-- assistant_fashion  		= 50,			-- 上架外观
	assistant_want_lingyu	= 50,			-- 我要灵玉
	assistant_want_exp  	= 60,			-- 我要经验
	assistant_want_equip  	= 70,			-- 我要装备

	glory_crystal_daily_task = 10,			-- 天裳仙衣-每日任务
	glory_crystal_purchase = 20,			-- 天裳仙衣-直购礼包
	glory_crystal_acc_recharge = 30,		-- 天裳仙衣-累计充值

	-- -- 器魂
	-- new_appearance_soul_uplevel = 10,				-- 器魂 - 升级
	-- new_appearance_soul_skill 	= 20,				-- 器魂 - 技能
}

ModuleNameToFunName =
{
	[GuideModuleName.HomesView]					= FunName.HomesView,
	[GuideModuleName.Exchange]					= FunName.Exchange,
	[GuideModuleName.Husong]					= FunName.Husong,
	[GuideModuleName.Equipment]					= FunName.Equipment,
	[GuideModuleName.ShenEquip]					= FunName.ShenEquip,
	[GuideModuleName.RoleView]					= FunName.Role,
	[GuideModuleName.Bag]						= FunName.Bag,
	[GuideModuleName.FuBenPanel]				= FunName.FuBenPanel,
	[GuideModuleName.Mount]						= FunName.Mount,
	[GuideModuleName.ShengWang]					= FunName.ShengWang,
	[GuideModuleName.Guild]						= FunName.Guild,
	[GuideModuleName.Appearance]				= FunName.Appearance,
	[GuideModuleName.Society]					= FunName.Society,
	[GuideModuleName.CombineSociety]			= FunName.CombineSociety,
	[GuideModuleName.Wing]						= FunName.Wing,
	[GuideModuleName.XunBao]					= FunName.XunBao,
	[GuideModuleName.Marry]						= FunName.Marry,
	[GuideModuleName.MarryFB]					= FunName.MarryFB,
	[GuideModuleName.ZhanShenDian]				= FunName.ZhanShenDian,
	[GuideModuleName.Setting]					= FunName.OtherSetting,
	[GuideModuleName.Rank]						= FunName.Rank,
	[GuideModuleName.Compose]					= FunName.Compose,
	[GuideModuleName.Market]					= FunName.Market,
	[GuideModuleName.ProfessWallView]			= FunName.ProfessWallView,
	[GuideModuleName.Welfare]					= FunName.Welfare,
	[GuideModuleName.WorldRedPaper]				= FunName.WorldRedPaper,
	[GuideModuleName.ActMaze]					= FunName.ActMaze,
	[GuideModuleName.DayActivity]				= FunName.DayActivity,
	[GuideModuleName.PrivilegeShop]				= FunName.PrivilegeShop,
	[GuideModuleName.LongXiView]				= FunName.LongXiView,
	-- [GuideModuleName.SuperDragonSealView]		= FunName.SuperDragonSeal,
	[GuideModuleName.Daily]						= FunName.Daily,
	[GuideModuleName.FirstCharge]				= FunName.ShouChong,
	[GuideModuleName.RechargeAgain]				= FunName.RechargeAgain,
	[GuideModuleName.RechargeThird]				= FunName.RechargeThird,
	[GuideModuleName.InvestPlan]				= FunName.InvestPlan,
	[GuideModuleName.Activity]					= FunName.ActvityIcon,
	[GuideModuleName.Recharge]					= FunName.Recharge,
	[GuideModuleName.ActJjc]					= FunName.ActJjc,
	[GuideModuleName.ActJjc_yingxiong]			= FunName.ActJjc_yingxiong,
	[GuideModuleName.ActIvityHall]				= FunName.ActIvityHall,
    [GuideModuleName.Carnival]					= FunName.Carnival,
    [GuideModuleName.TianShenJuexing]			= FunName.TianShenJuexing,
    [GuideModuleName.XiuXianShiLian]			= FunName.XiuXianShiLian,
	[GuideModuleName.GoldReversion]				= FunName.GoldReversion,
	[GuideModuleName.WorldServer]				= FunName.WorldServer,
	[GuideModuleName.SuperGift]					= FunName.SuperGift,
	[GuideModuleName.Map]						= FunName.Map,
	[GuideModuleName.ShenJiang]					= FunName.ShenJiang,
	[GuideModuleName.Award]						= FunName.Award,
	[GuideModuleName.ZhuanSheng]				= FunName.ZhuanSheng,
	[GuideModuleName.JingLing]					= FunName.JingLing,
	[GuideModuleName.Boss]						= FunName.Boss,
	[GuideModuleName.BossFake]					= FunName.BossFake,
	[GuideModuleName.Drugstore]					= FunName.Drugstore,
	[GuideModuleName.Storage]					= FunName.Storage,
	[GuideModuleName.TeJie]						= FunName.TeJie,
	[GuideModuleName.Fabao]						= FunName.Fabao,
	[GuideModuleName.JingHua]					= FunName.JingHua,
	[GuideModuleName.DailyRecharge]				= FunName.DailyRecharge,
	[GuideModuleName.DailyConsume]				= FunName.DailyConsume,
	[GuideModuleName.MysteriousShop]			= FunName.MysteriousShop,
	[GuideModuleName.Shop]						= FunName.Shop,
	[GuideModuleName.Meridian]					= FunName.Meridian,
	[GuideModuleName.Master]					= FunName.Master,
	[GuideModuleName.Jinyinta]					= FunName.Jinyinta,
	[GuideModuleName.Chongzhiniuegg]			= FunName.Chongzhiniuegg,
	[GuideModuleName.ActivityBipin]				= FunName.ActivityBipin,
	[GuideModuleName.TreasureLoft]				= FunName.TreasureLoft,
	[GuideModuleName.PigTreasure]				= FunName.PigTreasure,
	[GuideModuleName.TimeLimitFeedback]			= FunName.TimeLimitFeedback,
	[GuideModuleName.MiJingTaoBao]				= FunName.MiJingTaoBao,
	[GuideModuleName.MiJingTaoBaoTwo]			= FunName.MiJingTaoBaoTwo,
	[GuideModuleName.PleaseDrawCard]			= FunName.PleaseDrawCard,
	[GuideModuleName.CollectBless]				= FunName.CollectBless,
	[GuideModuleName.PerfectLover]				= FunName.PerfectLover,
	[GuideModuleName.LotteryTree]				= FunName.LotteryTree,
	[GuideModuleName.LotteryTreeTwo]			= FunName.LotteryTreeTwo,
	[GuideModuleName.SingleRechargeFour]		= FunName.SingleRechargeFour,
	[GuideModuleName.ConsumerScore]				= FunName.ConsumerScore,
	[GuideModuleName.New_Three_Suit]			= FunName.New_Three_Suit,
	[GuideModuleName.Single_Recharge]			= FunName.Single_Recharge,
	[GuideModuleName.Mine]						= FunName.Mine,
	[GuideModuleName.NoticeTwo]					= FunName.NoticeTwo,
	[GuideModuleName.NoticeThree]				= FunName.NoticeThree,
	[GuideModuleName.OpenServerMenu]			= FunName.OpenServerMenu,
	[GuideModuleName.MoLong]					= FunName.MoLong,
	[GuideModuleName.ContinuouslyRecharge]		= FunName.ContinuouslyRecharge,
	[GuideModuleName.RebateRecharge]			= FunName.RebateRecharge,
	[GuideModuleName.TOTAL_CHONGZHI]			= FunName.TOTAL_CHONGZHI,
	[GuideModuleName.TOTAL_CONSUME]				= FunName.TOTAL_CONSUME,
	[GuideModuleName.FunnyShoot]				= FunName.FunnyShoot,
	[GuideModuleName.DailyLove]					= FunName.DailyLove,
	[GuideModuleName.SingleRebate]				= FunName.SingleRebate,
	[GuideModuleName.SuperTransmit]				= FunName.SuperTransmit,
	[GuideModuleName.RandActivityHall]			= FunName.RandActivityHall,
	[GuideModuleName.FriendsBlessing]			= FunName.FriendsBlessing,
	[GuideModuleName.FriendsReturn]				= FunName.FriendsReturn,
	[GuideModuleName.Blessing]					= FunName.Blessing,
	[GuideModuleName.FragmentExchange]			= FunName.FragmentExchange,
	[GuideModuleName.ConsumeDiscount]			= FunName.ConsumeDiscount,
	[GuideModuleName.PlantingTree]				= FunName.PlantingTree,
	[GuideModuleName.Fanfanzhuan]				= FunName.Fanfanzhuan,
	[GuideModuleName.Fishpond]					= FunName.Fishpond,
	[GuideModuleName.RepeatRecharge]			= FunName.RepeatRecharge,
	[GuideModuleName.FlowerRank]				= FunName.FlowerRank,
	[GuideModuleName.KuafuYeZhanWangCheng]		= FunName.KuafuYeZhanWangCheng,
	[GuideModuleName.SpeedRecharge]				= FunName.SpeedRecharge,
	[GuideModuleName.RechargeReturnReward]		= FunName.RechargeReturnReward,
	[GuideModuleName.RunningMan]				= FunName.RunningMan,
	[GuideModuleName.KuafuRecharge]				= FunName.KuafuRecharge,
	[GuideModuleName.KuafuConsume]				= FunName.KuafuConsume,
	[GuideModuleName.WeGetMarried]				= FunName.WeGetMarried,
	[GuideModuleName.KuafuOneVsN]				= FunName.KuafuOneVsN,
	[GuideModuleName.Huanlezadan]				= FunName.Huanlezadan,
	[GuideModuleName.HuanlezadanRank]			= FunName.HuanlezadanRank,
	[GuideModuleName.MagicUtensil]				= FunName.MagicUtensil,
	[GuideModuleName.TeShuFace]					= FunName.TeShuFace,
	[GuideModuleName.BiZuo]						= FunName.BiZuo,
	[GuideModuleName.MiJing]					= FunName.MiJing,
	[GuideModuleName.Welkin]					= FunName.Welkin,
	[GuideModuleName.Wujinjitan]				= FunName.Wujinjitan,
	[GuideModuleName.JingYanYu]					= FunName.JingYanYu,
	[GuideModuleName.RebateReel]				= FunName.RebateReel,
	[GuideModuleName.FubenReel]					= FunName.FubenReel,
	[GuideModuleName.FuncReel]					= FunName.FuncReel,
	[GuideModuleName.GongCheng]					= FunName.GongCheng,
	[GuideModuleName.KuafuOneVsOne]				= FunName.KuafuOneVsOne,
	[GuideModuleName.Answer]					= FunName.Answer,
	[GuideModuleName.TeamFb]					= FunName.TeamFb,
	[GuideModuleName.CangBaoGe]					= FunName.CangBaoGe,
	[GuideModuleName.SevendayInvestplan]		= FunName.SevendayInvestplan,
	[GuideModuleName.TianJi]					= FunName.TianJi,
	[GuideModuleName.GoldMember]				= FunName.GoldMember,
	[GuideModuleName.Immortal]					= FunName.Immortal,
	[GuideModuleName.MingHun]					= FunName.MingHun,
	[GuideModuleName.SingleRechargeOne]			= FunName.SingleRechargeOne,
	[GuideModuleName.SingleRechargeTwo]			= FunName.SingleRechargeTwo,
	[GuideModuleName.SingleRechargeThree]		= FunName.SingleRechargeThree,
	[GuideModuleName.SingleRechargeFour]		= FunName.SingleRechargeFour,
	[GuideModuleName.MysteryHouse]				= FunName.MysteryHouse,
	[GuideModuleName.FuWen]						= FunName.FuWen,
	[GuideModuleName.ShenShou]					= FunName.ShenShou,
	[GuideModuleName.Achievement]				= FunName.Achievement,

	[GuideModuleName.QIFU]						= FunName.QIFU,
	[GuideModuleName.SevenDay]					= FunName.SevenDay,
	[GuideModuleName.ZhuZaiShenDian]			= FunName.ZhuZaiShenDian,
    [GuideModuleName.LingChongEquipView]		= FunName.LingChongEquipView,
    [GuideModuleName.MountEquipView]		    = FunName.MountEquipView,
    [GuideModuleName.HuaKunEquipView]		    = FunName.HuaKunEquipView,

	[GuideModuleName.NewTeam]					= FunName.NewTeam,
	[GuideModuleName.ShanHaiJingView]			= FunName.ShanHaiJingView,
	[GuideModuleName.ShanHaiJingLSCView]		= FunName.ShanHaiJingLSCView,

    [GuideModuleName.TreasureHunt]				= FunName.TreasureHunt,
	[GuideModuleName.Fishing]					= FunName.Fishing,
	[GuideModuleName.GuildAnswer]				= FunName.GuildAnswer,
	[GuideModuleName.GuildJiuSheDaoFB]			= FunName.GuildJiuSheDaoFB,
	[GuideModuleName.OpenServerRank]			= FunName.OpenServerRank,
	[GuideModuleName.CallBoss]					= FunName.CallBoss,
	[GuideModuleName.OpenServer]				= FunName.OpenServer,
	[GuideModuleName.ComposeTicket]				= FunName.ComposeTicket,
	[GuideModuleName.ChargeRepayment2]			= FunName.ChargeRepayment2,
	[GuideModuleName.HappyConsume]				= FunName.HappyConsume,
	[GuideModuleName.HappyRecharge]				= FunName.HappyRecharge,
	[GuideModuleName.RechargeGift]				= FunName.RechargeGift,
	[GuideModuleName.RechargeGift2]				= FunName.RechargeGift2,
	[GuideModuleName.FuWen]						= FunName.FuWen,
	[GuideModuleName.LuckBuy]					= FunName.LuckBuy,
	[GuideModuleName.LimitedShop]				= FunName.LimitedShop,
	[GuideModuleName.WishingPool]				= FunName.WishingPool,
	[GuideModuleName.TreasureAct]				= FunName.TreasureAct,
	[GuideModuleName.ConsumeRankThree]			= FunName.ConsumeRankThree,
	[GuideModuleName.LuckyRandom]				= FunName.LuckyRandom,
	[GuideModuleName.TeHuiShop]					= FunName.TeHuiShop,
	[GuideModuleName.Unlock]					= FunName.Unlock,
	[GuideModuleName.Secrefash]					= FunName.Secrefash,
	[GuideModuleName.SkillView]					= FunName.SkillView,
	[GuideModuleName.ActivityMainPanel]			= FunName.ActivityMainPanel,
	[GuideModuleName.VipExperience]				= FunName.VipExperience,
	[GuideModuleName.ShenShouBianShenAppearance]= FunName.ShenShouBianShenAppearance,
	[GuideModuleName.SpecialGiftBag]			= FunName.SpecialGiftBag,
	[GuideModuleName.MarryNotice]				= FunName.MarryNotice,
	[GuideModuleName.guild_task]				= FunName.guild_task,
	[GuideModuleName.XiuZhenRoadView]			= FunName.XiuZhenRoadView,
	[GuideModuleName.FanRenXiuZhenRewardView]	= FunName.FanRenXiuZhenRewardView,
	[GuideModuleName.FanrenxiuzhenRewardPreview]	= FunName.FanrenxiuzhenRewardPreview,
	[GuideModuleName.ZhanLingView]				= FunName.ZhanLing,
	[GuideModuleName.CountryMapMapView]			= FunName.CountryMapMapView,
	[GuideModuleName.CountryMapActView]			= FunName.CountryMapActView,
	[GuideModuleName.EquipTargetView]			= FunName.EquipTargetView,
	[GuideModuleName.TianShenView]			    = FunName.TianShenView,
	[GuideModuleName.TianShenShenTongView] 		= FunName.tianshen_shentong,
	[GuideModuleName.ExpPoolView]			    = FunName.ExpPoolView,
	[GuideModuleName.YinPiaoExchangeView]		= FunName.YinPiaoExchangeView,
	[GuideModuleName.MingWenView] 				= FunName.MingWenView,
	[GuideModuleName.SupremeFieldsWGView] 		= FunName.SupremeFieldsWGView,
	[GuideModuleName.LongHunView] 				= FunName.LongHunView,
	[GuideModuleName.ShiTuXiuLi] 				= FunName.ShiTuXiuLi,
	[GuideModuleName.NewTeamView] 				= FunName.NewTeamView,
	[GuideModuleName.FightSoulView] 			= FunName.FightSoulView,
	[GuideModuleName.FairyLandPlaneView] 		= FunName.FairyLandPlaneView,
	[GuideModuleName.FairyLandRuleView] 		= FunName.FairyLandRuleView,
	[GuideModuleName.FairyLandEquipmentView] 	= FunName.FairyLandEquipmentView,
	[GuideModuleName.SiXiangCallView]			= FunName.SiXiangCallView,
	[GuideModuleName.CapabilityContrastView]	= FunName.CapabilityContrastView,
	[GuideModuleName.FeedBackView]				= FunName.FeedBackView,
	[GuideModuleName.ChangeHeadView]			= FunName.ChangeHeadView,
	[GuideModuleName.ShenJiXianShiView]			= FunName.ShenJiNotice,
	[GuideModuleName.HiddenWeaponView]		 	= FunName.ShenJi,
	[GuideModuleName.XianyuTrunTableView]= FunName.XianyuTrunTableView,
	[GuideModuleName.WingLinZhiSkillView]		= FunName.WingLinZhiSkillView,
	[GuideModuleName.FaBaoLinZhiSkillView]		= FunName.FaBaoLinZhiSkillView,
	[GuideModuleName.ShenBingLinZhiSkillView]		= FunName.ShenBingLinZhiSkillView,
	[GuideModuleName.JianZhenLinZhiSkillView]		= FunName.JianZhenLinZhiSkillView,
	[GuideModuleName.WorldsNO1View]				= FunName.WorldsNO1View,
	[GuideModuleName.TianShenLingHeDrawView]	= FunName.TianShenLingHeDrawView,
	[GuideModuleName.ShenJiTianCiView]			= FunName.ShenJiTianCiView,
	[GuideModuleName.CrossLongMaiView]			= FunName.CrossLongMaiView,
	[GuideModuleName.ChaoticPurchaseView]			= FunName.ChaoticPurchaseView,
	[GuideModuleName.TianShenLiLianView]			= FunName.TianShenLiLianView,
	[GuideModuleName.FiveElementsTreasuryView]	= FunName.FiveElementsTreasuryView,
	[GuideModuleName.HongHuangGoodCoremonyView]	= FunName.HongHuangGoodCoremonyView,
	[GuideModuleName.ChaoticVipSpecialView]	    = FunName.ChaoticVipSpecialView,
	[GuideModuleName.SystemForceLongShenView]   = FunName.SystemForceLongShenView,
	[GuideModuleName.HongHuangClassicView]	    = FunName.HongHuangClassicView,
	[GuideModuleName.FiveElementsView]          = FunName.FiveElementsView,
	[GuideModuleName.SwornView]                 = FunName.SwornView,
	[GuideModuleName.HolyDarkWeaponEnter]		= FunName.HolyDarkWeaponEnter,
	[GuideModuleName.HolyWeapon]                = FunName.HolyWeapon,
	[GuideModuleName.DarkWeapon]                = FunName.DarkWeapon,
	[GuideModuleName.ArtifactView]              = FunName.ArtifactView,
	[GuideModuleName.YinianMagicView]              = FunName.YinianMagicView,
	[GuideModuleName.DragonTempleView]          = FunName.DragonTempleView,
	[GuideModuleName.HuanHuaFetterView]         = FunName.HuanHuaFetterView,
	[GuideModuleName.NewHuanHuaFetterView]      = FunName.NewHuanHuaFetterView,
	[GuideModuleName.ShiTianSuitView]			= FunName.ShiTianSuitView,
	[GuideModuleName.MultiFunctionView]           = FunName.MultiFunctionView,
	[GuideModuleName.GuiXuDreamView]            = FunName.GuiXuDreamView,
	[GuideModuleName.BossMustFallPrivilegeView]	= FunName.BossMustFallPrivilegeView,
	[GuideModuleName.WuHunView]                 = FunName.WuHunView,
	[GuideModuleName.WuHunCultivateView]        = FunName.WuHunCultivateView,
	[GuideModuleName.HolyHeavenlyDomainView]    = FunName.HolyHeavenlyDomainView,
	[GuideModuleName.NewFightMountView]         = FunName.NewFightMountView,
	[GuideModuleName.CustomizedSuitView]		= FunName.CustomizedSuitView,
	[GuideModuleName.BoundlessJoyView]			= FunName.BoundlessJoyView,
	[GuideModuleName.ControlBeastsView]			= FunName.ControlBeastsView,
	[GuideModuleName.ControlBeastsContractWGView]= FunName.ControlBeastsContractWGView,
	[GuideModuleName.HolyBeastsView]			= FunName.HolyBeastsView,
	[GuideModuleName.CustomizedRumorsView] = FunName.CustomizedRumorsView,
	[GuideModuleName.CangJinShopView]           = FunName.CangJinShopView,
	[GuideModuleName.CangJinExchangeView]       = FunName.CangJinExchangeView,
	[GuideModuleName.MechaView]                 = FunName.MechaView,
	[GuideModuleName.EverythingUnderTheSun]     = FunName.EverythingUnderTheSun,
	[GuideModuleName.JingHuaShuiYueView]     	= FunName.JingHuaShuiYue,
	[GuideModuleName.StrangeCatalogView]		= FunName.StrangeCatalogView,
	[GuideModuleName.CultivationView]		    = FunName.CultivationView,
	[GuideModuleName.AssignmentView]			= FunName.AssignmentView,
	[GuideModuleName.EsotericaSkillShow]		= FunName.EsotericaSkillShow,
	[GuideModuleName.MengLingView]              = FunName.MengLingView,
	[GuideModuleName.PositionalWarfareView]     = FunName.PositionalWarfareView,        -- 阵地战
	[GuideModuleName.LandWarFbPersonView]       = FunName.LandWarFbPersonView,        -- 阵地战
	[GuideModuleName.ConquestWarView]   		= FunName.ConquestWarView,
	[GuideModuleName.LongYunZhanLingTaskView]   = FunName.LongYunZhanLingTaskView,
	[GuideModuleName.LordEveryDayShopView]   = FunName.LordEveryDayShopView, --魔王仙藏
	[GuideModuleName.FashionExchangeShopView]   = FunName.FashionExchangeShopView,			--时装兑换商店
	[GuideModuleName.WorldTreasureView]      = FunName.WorldTreasureView,
	[GuideModuleName.BossNewPrivilegeView]      = FunName.BossNewPrivilegeView,
	[GuideModuleName.SecretRecordView]          = FunName.SecretRecordView,
	[GuideModuleName.BillionSubsidy]			= FunName.BillionSubsidy,				--百亿补贴
	[GuideModuleName.RoleDiyAppearanceView]		= FunName.RoleDiyAppearanceView,
	[GuideModuleName.DressingRoleDiyView]		= FunName.DressingRoleDiyView,
	[GuideModuleName.ControlBeastsPrizeDrawWGView]	= FunName.ControlBeastsPrizeDrawWGView,				--幻兽奇遇
	[GuideModuleName.HundredEquipView] = FunName.HundredEquipView,						-- 百倍爆装
	[GuideModuleName.HundredEquipRatio] = FunName.HundredEquipRatio,					-- 黄金龙巢
	[GuideModuleName.YanYuGePrivilegeView]	        = FunName.YanYuGePrivilegeView,
	[GuideModuleName.YanYuGeExchangeShopView]	    = FunName.YanYuGeExchangeShopView,
	[GuideModuleName.YanYuGeNobleView]	            = FunName.YanYuGeNobleView,
	[GuideModuleName.ThunderManaSelectView]	        = FunName.ThunderManaSelectView,
	[GuideModuleName.ShadyThunderView]	            = FunName.ShadyThunderView,
	[GuideModuleName.SunThunderView]	            = FunName.SunThunderView,
	[GuideModuleName.NewAppearanceHaloWGView]	    = FunName.NewAppearanceHaloWGView,
	[GuideModuleName.NewAppearanceWGView]			= FunName.NewAppearanceWGView,
	[GuideModuleName.EveryDayRechargeView]			= FunName.EveryDayRechargeView,
	[GuideModuleName.PrivilegeCollectionView]		= FunName.PrivilegeCollectionView,
	[GuideModuleName.WardrobeView]					= FunName.WardrobeView,
	[GuideModuleName.GameAssistant]					= FunName.GameAssistant,
	[GuideModuleName.HalfPurchaseView]				= FunName.HalfPurchaseView,
	[GuideModuleName.RechargeVolumeView]			= FunName.RechargeVolumeView,
	[GuideModuleName.RechargeVolumeRewardView]		= FunName.RechargeVolumeRewardView,
	[GuideModuleName.CashPointView]					= FunName.CashPointView,				--现金点
}

ActivityModuleName = {
	[GuideModuleName.Huanlezadan] = ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG,
	[GuideModuleName.TreasureAct] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NATIONAL_TREASURE,
	[GuideModuleName.WishingPool] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL,
	[GuideModuleName.Fireworks] = ACTIVITY_TYPE.RAND_ACTIVITY_FIREWORKS,
	[GuideModuleName.LuckyRandom] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CLOUD_BUY,
	[GuideModuleName.LuckBuy] = ACTIVITY_TYPE.LUCKYBUY,
	[GuideModuleName.LayoutZeroBuyView] = ACTIVITY_TYPE.ZEROBUY,
}

--打开界面要播放的音效
ViewAudioUrl =
{
	["YeQian"] = "YeQian",								--页签
	[GuideModuleName.Shop] = "OpenShop",
	[GuideModuleName.Market] = "OpenMarket",
	[GuideModuleName.Vip] = "OpenChongZhi",
	[GuideModuleName.Equipment] = "OpenDuanZao",
	[GuideModuleName.Boss] = "OpenBoss",
}

IgnoreAudioUrlView =
{
	[GuideModuleName.Login] = 1,
	[GuideModuleName.Agent] = 1,
	-- [GuideModuleName.] = 1,
}

-- 界面点击上报映射
--[[
	[view_name] ~= nil 上报大界面的打开关闭 
	[view_name] ： type(val) == "number" and val == 0, 不自动上报，若要上报活动点击、标签点击在各自界面内特殊处理
	[view_name] ： type(val) == "number" and val > 0, 上报点击活动埋点事件
	[view_name] : type(val) == "table", 上报点击界面标签埋点事件
]]
VIEW_CLICK_REPORT_MAP = {
	[GuideModuleName.ActivityLimitBuyView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY,
	[GuideModuleName.PrivilegeBuyView] = ACTIVITY_TYPE.PRIVILEGEBUY,
	[GuideModuleName.CatExploreView] = 0,
	[GuideModuleName.ChessBoardTreasueView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHESSBOARD,
	[GuideModuleName.DIYDrawOneView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW,
	[GuideModuleName.DIYDrawTwoView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW,
	[GuideModuleName.FlopDrawView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE2,
	[GuideModuleName.LimitedTimeOffer] = ACTIVITY_TYPE.LIMITEDTIMEOFFER,
	[GuideModuleName.ShenNuTianZhuView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SHENNUTIANZHU,
	[GuideModuleName.SuperPurchaseView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY3,
	[GuideModuleName.SystemCapRankAnQiView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_SQAN,
	[GuideModuleName.SystemCapRankShengQiView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SYSTEM_SQGUANG,
	[GuideModuleName.TianShenPurchaseView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY,
	[GuideModuleName.GloryCrystalView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GLORY_CRYSTAL,
	[GuideModuleName.SunRainbowView] = 0,
	[GuideModuleName.XuYuanFreshPoolView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3,
	[GuideModuleName.TodaySpecialView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY2,
	[GuideModuleName.XianLingGuZhen] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN,
	[GuideModuleName.PremiumGiftView] = ACTIVITY_TYPE.PREMIUM_GIFT,
	[GuideModuleName.RebateExtinctGiftView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT,
	[GuideModuleName.TripleRechargeView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TRIPLE_RECHARGE,
	[GuideModuleName.HaoliWanzhangView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG,
	[GuideModuleName.TotalRechargeGiftView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT,
	[GuideModuleName.HaoLiWanZhang3View] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG3,
	[GuideModuleName.FlopDrawView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE2,
	[GuideModuleName.LuckyGiftBagLocalView] = ACTIVITY_TYPE.CROSS_ACTIVITY_LUCKYGIFTBAGLOCAL,
	[GuideModuleName.GodGetRewardNewView] = ACTIVITY_TYPE.GOD_XUNBAO,
	[GuideModuleName.JiangShanRuHuaView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_JIANG_SHAN_RU_HUA,
	[GuideModuleName.SupremeFieldsActView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN,
	[GuideModuleName.PanaceaFurnaceView] = ACTIVITY_TYPE.RAND_ACTIVITY_PANACEA_FURNACE,
	[GuideModuleName.FortuneCatView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT,
	[GuideModuleName.GodPchaseView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY,
	[GuideModuleName.MustBuy] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY,
	[GuideModuleName.HelpRankView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK,
	[GuideModuleName.RebateActivityView] = 0,
	[GuideModuleName.ServerActivityTabView] = 0,
	[GuideModuleName.OperationActivityView] = 0,
	[GuideModuleName.MergeActivityView] = 0,
	[GuideModuleName.MarryDiscountView] = ACTIVITY_TYPE.MARRY_DISCOUNT,
	[GuideModuleName.LifeTimeOfLoveView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_A_LIFELONG_LOVE_TASK,
	[GuideModuleName.OdysseyPurchaseView] = ACTIVITY_TYPE.RAND_ACTIVIYY_TYPE_OA_ODYSSEYRMB,
	[GuideModuleName.ChessBoardTreasueView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHESSBOARD,
	[GuideModuleName.OneSwordFrostbiteView] = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SWORD_FROSTBITE,

	[GuideModuleName.GoldStoneView] = 0,
	[GuideModuleName.EveryDayRechargeView] = {
		[TabIndex.everyday_recharge_rapidly] = true, [TabIndex.everyday_recharge_dailygift] = true, [TabIndex.everyday_recharge_leichong] = true,
		[TabIndex.everyday_recharge_lianchong] = true, [TabIndex.everyday_recharge_yingji] = true, [TabIndex.everyday_recharge_zhigou] = true,
		[TabIndex.everyday_recharge_xiaofei] = true
	},
	[GuideModuleName.ZhanLingView] = {["all"] = true},
	[GuideModuleName.PositionalWarfareZhanLingView] = 0,
	[GuideModuleName.BossZhanLingView] = 0,
	[GuideModuleName.LongYunZhanLingView] = 0,
	[GuideModuleName.LayoutZeroBuyView] = 0,
	[GuideModuleName.OpenServerInvestView] = 0,
	[GuideModuleName.LimitTimeGiftPurchase] = 0,
	[GuideModuleName.PrivilegeCollectionView] = {
		[TabIndex.pri_col_zztq] = true, [TabIndex.pri_col_nstq] = true, [TabIndex.pri_col_zjtq] = true, [TabIndex.pri_col_shtq] = true
	},
	[GuideModuleName.YanYuGeNobleView] = 0,
	[GuideModuleName.YanYuGeExchangeShopView] = 0,
	[GuideModuleName.YanYuGePrivilegeView] = 0,
	[GuideModuleName.HundredEquipTip] = 0,
	[GuideModuleName.GodOfWealthView] = 0,
	[GuideModuleName.LimitTimeGift] = 0,
	[GuideModuleName.ProfessWallView] = 0,
	[GuideModuleName.LongYunZhanLingTaskView] = 0,
	[GuideModuleName.LongYunZhanLingView] = 0,
	[GuideModuleName.BillionSubsidy] = {
		[TabIndex.billion_subsidy_bybt] = true, [TabIndex.billion_subsidy_xdzk] = true, [TabIndex.billion_subsidy_fyms] = true,
		[TabIndex.billion_subsidy_jkjzc] = true, [TabIndex.billion_subsidy_lysd] = true, [TabIndex.billion_subsidy_dezg] = true,
		[TabIndex.billion_subsidy_vip] = true, [TabIndex.billion_subsidy_drpt] = true, [TabIndex.billion_subsidy_mrsy] = true,
		[TabIndex.billion_subsidy_dailygift] = true, [TabIndex.billion_subsidy_rapidly] = true,
	},
	[GuideModuleName.Vip] = {
		[TabIndex.recharge_cz] = true, [TabIndex.recharge_zerobuy] = true, [TabIndex.recharge_vip] = true,
		[TabIndex.recharge_king_vip] = true, [TabIndex.recharge_month_card] = true, [TabIndex.recharge_week_card] = true,
		[TabIndex.recharge_reserve_card] = true, [TabIndex.recharge_tqtz] = true, [TabIndex.recharge_leichong] = true,
		[TabIndex.recharge_xiaofei] = true, [TabIndex.recharge_week_buy] = true, [TabIndex.recharge_week_privilege] = true,
	},
	[GuideModuleName.BossNewPrivilegeView] = {
		[TabIndex.boss_mabi_skill] = true, [TabIndex.boss_godwar] = true, [TabIndex.boss_zaibaoyici] = true,
		[TabIndex.boss_kill_every] = true
	},
	[GuideModuleName.TreasureHunt] = {
		[TabIndex.treasurehunt_fuwen] = true, [TabIndex.treasurehunt_equip] = true, [TabIndex.treasurehunt_dianfeng] = true,
		[TabIndex.treasurehunt_zhizun] = true, [TabIndex.treasurehunt_thunder] = true,
	},
	[GuideModuleName.WorldTreasureView] = {
		[TabIndex.tcdb_first_page] = true, [TabIndex.tcdb_login_gift] = true, [TabIndex.tcdb_first_recharge] = true,
		[TabIndex.tcdb_flowing] = true, [TabIndex.tcdb_raise_star_gift] = true, [TabIndex.tcdb_together] = true,
		[TabIndex.tcdb_jigsaw] = true, [TabIndex.tcdb_flash_sale] = true, [TabIndex.tcdb_flash_sale1] = true,
		[TabIndex.tcdb_flash_sale2] = true, [TabIndex.tcdb_total_recharge] = true, [TabIndex.tcdb_jianglin] = true,
		[TabIndex.tcdb_mowang] = true, [TabIndex.tcdb_pursuit] = true, [TabIndex.tcdb_scheme] = true,
		[TabIndex.tcdb_premium_shop] = true,
	},
}

-- 动态设置页签索引
-- 有些系统页签是动态变化的，要这个方法设置
function SetTabIndex(key, value)
	TabIndex[key] = value
end
