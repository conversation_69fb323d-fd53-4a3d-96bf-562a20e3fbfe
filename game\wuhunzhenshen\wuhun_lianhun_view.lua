WuHunLianHunView = WuHunLianHunView or BaseClass(SafeBaseView)

function WuHunLianHunView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self.view_name = "WuHunLianHunView"
	local bundle_name = "uis/view/wuhunzhenshen_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
		{ vector2 = Vector2(0, 0), sizeDelta = Vector2(948, 594) })
	self:AddViewResource(0, bundle_name, "layout_tianshen_wuhun_lianhun")
end

function WuHunLianHunView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.WuHunZhenShen.WuHunLianHun

	self.cur_select_wh_data = nil

	if self.wuhun_li_list == nil then
		self.wuhun_li_list = {}
		for i = 1, 5 do
			local attr_obj = self.node_list["wuhun_li_list"]:FindObj(string.format("wuhun_li_%d", i))
			if attr_obj then
				local cell = TianShenWuHunLiRender.New(attr_obj)
				cell:SetIndex(i)
				self.wuhun_li_list[i] = cell
			end
		end
	end

	if self.wuhun_lianhun_item == nil then
		self.wuhun_lianhun_item = ItemCell.New(self.node_list.wuhun_lianhun_item)
		self.wuhun_lianhun_item:SetShowCualityBg(false)
		self.wuhun_lianhun_item:SetCellBgEnabled(false)
	end

	if not self.lianhua_attr_list_view then
		self.lianhua_attr_list_view = AsyncListView.New(CommonAddAttrRender, self.node_list["lianhua_attr_list_view"])
	end

	XUI.AddClickEventListener(self.node_list.wuhun_btn_lianhun_operate, BindTool.Bind2(self.WuHunOnLianHunOperate, self))
end

function WuHunLianHunView:ReleaseCallBack()
	if self.wuhun_li_list and #self.wuhun_li_list > 0 then
		for _, wuhun_li_cell in ipairs(self.wuhun_li_list) do
			wuhun_li_cell:DeleteMe()
			wuhun_li_cell = nil
		end

		self.wuhun_li_list = nil
	end

	if self.wuhun_lianhun_item then
		self.wuhun_lianhun_item:DeleteMe()
		self.wuhun_lianhun_item = nil
	end

	if self.lianhua_attr_list_view then
		self.lianhua_attr_list_view:DeleteMe()
		self.lianhua_attr_list_view = nil
	end

	self.cur_select_wh_data = nil
end

function WuHunLianHunView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "LianHun" then
			self.cur_select_wh_data = v
		end
	end

	self:WuHunRefreshLianHun()
	self:WuHunShowLianHunItem()
end

function WuHunLianHunView:WuHunRefreshLianHun()
	if not self.cur_select_wh_data then
		return
	end

	-- 服务器的下标从0开始的
	local purgatory_cfg_next = WuHunWGData.Instance:GetWuHunPurgatoryCfg(self.cur_select_wh_data.wuhun_id,
		self.cur_select_wh_data.order + 1)

	self.node_list.lianhun_flag_red:CustomSetActive(purgatory_cfg_next == nil)
	self.node_list.expend:CustomSetActive(purgatory_cfg_next ~= nil)
	self.node_list.wuhun_lianhun_item:CustomSetActive(purgatory_cfg_next ~= nil)
	self.node_list.lianhun_limit_tip:CustomSetActive(purgatory_cfg_next ~= nil)
	self.node_list.wuhun_btn_lianhun_operate:CustomSetActive(purgatory_cfg_next ~= nil)
	self.node_list.lianhun_remind:CustomSetActive(self.cur_select_wh_data.purgatory_up)

	if purgatory_cfg_next then
		local need_level = purgatory_cfg_next.need_level

		if need_level then
			local color = self.cur_select_wh_data.level >= need_level and ITEM_NUM_COLOR.ENOUGH or
				ITEM_NUM_COLOR.NOT_ENOUGH
			local str = string.format(Language.WuHunZhenShen.WuHunColorStr, color, self.cur_select_wh_data.level,
				need_level)
			self.node_list.lianhun_limit_tip.text.text = string.format(Language.WuHunZhenShen.WuHunLianHunText, str)
		end
	end

	local wuhun_li_cfg = WuHunWGData.Instance:GetWuHunLiCfg(self.cur_select_wh_data.wh_type)
	if wuhun_li_cfg and self.wuhun_li_list then
		for order, wuhun_li_cell in ipairs(self.wuhun_li_list) do
			local offset = (self.cur_select_wh_data.order % 5)
			wuhun_li_cell:SetCurSelect(offset + 1 == order)
			wuhun_li_cell:SetClickCallBack(BindTool.Bind(self.WuHunLiClickCallback, self, wuhun_li_cell, order))
			wuhun_li_cell:SetNormalImage(wuhun_li_cfg.wh_bead)
			wuhun_li_cell:SetLockChange(order > offset)
		end

		local li_attr_2_data = WuHunWGData.Instance:GetLianHunAttrData(self.cur_select_wh_data.wuhun_id,
			self.cur_select_wh_data.order, wuhun_li_cfg.wh_name)

		self.lianhua_attr_list_view:SetDataList(li_attr_2_data)

		local bundle, asset = ResPath.GetWuHunZhenShenImage(wuhun_li_cfg.wh_sign)
		self.node_list.item_image.image:LoadSprite(bundle, asset)
	end

	local order_value = math.floor(self.cur_select_wh_data.order / 5)
	local num_txt = order_value + 1
	local floor_txt = purgatory_cfg_next == nil and Language.WuHunZhenShen.WuHunLianHunText2 or
		string.format(Language.WuHunZhenShen.WuHunLianHunText1, num_txt)
	self.node_list.text_lianhun_order.text.text = floor_txt
end

function WuHunLianHunView:WuHunShowLianHunItem()
	if not self.cur_select_wh_data then
		return
	end

	local purgatory_cfg_next = WuHunWGData.Instance:GetWuHunPurgatoryCfg(self.cur_select_wh_data.wuhun_id,
		self.cur_select_wh_data.order + 1)
	if purgatory_cfg_next == nil then
		return
	end

	local item_count = ItemWGData.Instance:GetItemNumInBagById(purgatory_cfg_next.item)

	self.wuhun_lianhun_item:SetData({ item_id = purgatory_cfg_next.item })
	self.wuhun_lianhun_item:SetDefaultEff(false)

	if purgatory_cfg_next.item_num then
		local str = string.format("%s/%s", item_count, purgatory_cfg_next.item_num)
		local color = item_count >= purgatory_cfg_next.item_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
		self.node_list.expend_item_num.text.text = ToColorStr(str, color)
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(purgatory_cfg_next.item)

	if item_cfg and item_cfg.is_display_role and item_cfg.is_display_role <= 0 then
		self.wuhun_lianhun_item:SetIsShowTips(false)

		self.wuhun_lianhun_item:SetClickCallBack(function()
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = purgatory_cfg_next.item })
		end)
	else
		self.wuhun_lianhun_item:SetClickCallBack(nil)
		self.wuhun_lianhun_item:SetIsShowTips(true)
	end
end

--物品变化
function WuHunLianHunView:ItemChangeFlush()
	self:WuHunShowLianHunItem()
end

-- 武魂炼魂升级按钮(需要突破时为突破按钮)
function WuHunLianHunView:WuHunOnLianHunOperate()
	if not self.cur_select_wh_data then
		return
	end

	-- 服务器的下标从0开始的
	local purgatory_cfg = WuHunWGData.Instance:GetWuHunPurgatoryCfg(self.cur_select_wh_data.wuhun_id,
		self.cur_select_wh_data.order + 1)

	if purgatory_cfg then
		local need_level = purgatory_cfg.need_level

		if need_level and self.cur_select_wh_data.level >= need_level then
			local num = ItemWGData.Instance:GetItemNumInBagById(purgatory_cfg.item)
			if num < purgatory_cfg.item_num then
				TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = purgatory_cfg.item })
				return
			end
			WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_LIAN_HUA,
				self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.order + 1)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunLianHunError)
		end
	end
end

function WuHunLianHunView:WuHunLiClickCallback(wuhun_li_cell, order)
	if not self.cur_select_wh_data then
		return
	end

	if self.cur_select_wh_data.order + 1 ~= order then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunLianHunError2)
	end
end

--===================================================================
TianShenWuHunLiRender = TianShenWuHunLiRender or BaseClass(BaseRender)
function TianShenWuHunLiRender:__delete()
	self.move_tween_original_x = nil
	self.move_tween_original_y = nil
	self:KillMoveTween()
end

function TianShenWuHunLiRender:SetCurSelect(is_select)
	self.node_list["select"]:SetActive(is_select)
end

function TianShenWuHunLiRender:SetNormalImage(wuhun_li_name)
	self.node_list["normal"].image:LoadSprite(ResPath.GetWuHunZhenShenImage(wuhun_li_name))
end

function TianShenWuHunLiRender:SetLockChange(is_lock)
	XUI.SetGraphicGrey(self.node_list["normal"], is_lock)
	self:ResetOriginalPos()
	self:KillMoveTween()

	if not is_lock then
		self:AddMoveTween()
	end
end

function TianShenWuHunLiRender:KillMoveTween()
	if self.move_tweener then
		self.move_tweener:Kill()
		self.move_tweener = nil
	end
end

function TianShenWuHunLiRender:LoadCallBack()
	local node = self.view
	self.move_tween_original_x = node.rect.anchoredPosition.x
	self.move_tween_original_y = node.rect.anchoredPosition.y
end

function TianShenWuHunLiRender:ResetOriginalPos()
	local node = self.view
	RectTransform.SetAnchoredPositionXY(node.rect, self.move_tween_original_x, self.move_tween_original_y)
end

function TianShenWuHunLiRender:AddMoveTween()
	local tween_time = 0.8
	local node = self.view
	local offset_y = self.index % 2 == 0 and 20 or -20
	if node then
		RectTransform.SetAnchoredPositionXY(node.rect, self.move_tween_original_x,
			self.move_tween_original_y + (offset_y / 2))
		self.move_tweener = node.rect:DOAnchorPosY(self.move_tween_original_y + ((offset_y / 2) * -1), tween_time)
		self.move_tweener:SetEase(DG.Tweening.Ease.InOutSine)
		self.move_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

--===================================================================
TianShenWuHunLiAddAttrRender = TianShenWuHunLiAddAttrRender or BaseClass(CommonAddAttrRender)
function TianShenWuHunLiAddAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	self.node_list.attr_name.text.text = self.data.attr_name
	self.node_list.attr_value.text.text = self.data.cur_value

	self.node_list.arrow:SetActive(true)
	self.node_list.add_value:SetActive(true)
	self.node_list.arrow.image.enabled = true

	if self.data.add_value and self.data.add_value > 0 then
		self.node_list.add_value.text.text = self.data.add_value
	else
		if self.real_hide_next then
			self.node_list.arrow:SetActive(false)
			self.node_list.add_value:SetActive(false)
		else
			self.node_list.arrow.image.enabled = false
			self.node_list.add_value.text.text = ""
		end
	end

	self.view:SetActive(true)
end
