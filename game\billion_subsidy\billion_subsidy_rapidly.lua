--每日累充--秒杀
function BillionSubsidyView:InitRapidlyRechargeView()
	XUI.AddClickEventListener(self.node_list.rapidly_buy_btn, BindTool.Bind1(self.OnClickRapidlyBuyBtn, self))
	XUI.AddClickEventListener(self.node_list.rapidly_buy_all_btn, BindTool.Bind1(self.OnClickRapidlyBuyAllBtn, self))
	XUI.AddClickEventListener(self.node_list.rapidly_free_box_btn, BindTool.Bind1(self.OnClickRapidlyFreeBoxBtn, self))
	XUI.AddClickEventListener(self.node_list.rapidly_sup_reward_get_btn,
		BindTool.Bind1(self.OnClickRapidlySupRewardGetBtn, self))
	XUI.AddClickEventListener(self.node_list.rapidly_record_btn,
		BindTool.Bind1(self.OnClickRapidlyOpenRecordViewBtn, self))
	XUI.AddClickEventListener(self.node_list.rapidly_up_vip, BindTool.Bind1(self.OnClickRapidlyJumpViewBtn, self))


	if not self.rapidly_gift_list then
		self.rapidly_gift_list = {}
		local count = self.node_list.rapidly_gift_list.transform.childCount
		for i = 1, count do
			self.rapidly_gift_list[i] = BillionRapidlyGiftItem.New(self.node_list.rapidly_gift_list:FindObj("rapidly_gift_" .. i))
		end
	end

	if not self.rapidly_reward_list then
		self.rapidly_reward_list = AsyncBaseGrid.New()
		self.rapidly_reward_list:SetStartZeroIndex(true)
		self.rapidly_reward_list:CreateCells({
			col = 2,
			list_view = self.node_list["rapidly_sup_reward_list"],
			itemRender = ItemCell,
			change_cells_num = 1,
		})
	end
end

function BillionSubsidyView:ReleaseRapidlyRechargeView()
	if self.rapidly_gift_list then
		for key, value in pairs(self.rapidly_gift_list) do
			value:DeleteMe()
			value = nil
		end
		self.rapidly_gift_list = nil
	end

	if self.rapidly_reward_list then
		self.rapidly_reward_list:DeleteMe()
		self.rapidly_reward_list = nil
	end

	if self.rapidly_daily_gift_box_tween then
		self.rapidly_daily_gift_box_tween:Kill()
		self.rapidly_daily_gift_box_tween = nil
	end
end

function BillionSubsidyView:FlushRapidlyRechargeView()
	self:FlushRapidlyGiftList()

	local can_get_day = ServerActivityWGData.Instance:CheckCanGetEveryDayRapidlyGiftDay()
	self.node_list.rapidly_up_vip:SetActive(can_get_day == 0)
	self.node_list.rapidly_buy_btn_text:SetActive(can_get_day == 0)
	self.node_list.rapidly_buy_btn_text2:SetActive(can_get_day == 0)
	self.node_list.rapidly_buy_btn_text3:SetActive(can_get_day > 0)
	self.node_list.rapidly_not_buy_img:SetActive(can_get_day > 0)
	self.node_list.rapidly_buy_all_btn:SetActive(ServerActivityWGData.Instance:CanOneKeyBuyEveryDayRapidlyGift())

	for i = 1, 2 do
		local one_key_cfg = ServerActivityWGData.Instance:GetRapidlyGiftOneKey(i - 1)
		if not one_key_cfg then
			return
		end

		local text_name = i == 1 and "rapidly_buy_all_btn_text" or "rapidly_buy_btn_text"

		self.node_list[text_name].text.text = RoleWGData.GetPayMoneyStr(one_key_cfg.price)
		self.node_list[text_name .. 2].text.text = one_key_cfg.name

		self.node_list["rapidly_up_vip_text" .. i].text.text = one_key_cfg.tip
	end

	if can_get_day > 0 then
		local str = can_get_day > 10 and Language.EverydayRecharge.MRSX or string.format(Language.EverydayRecharge.RapidlyCanGetDayText, can_get_day)
		self.node_list.rapidly_buy_btn_text3.text.text = str
	else
		self.node_list.rapidly_buy_btn_text2.text.text = Language.EverydayRecharge.RapidlyCanGetDayText2
	end

	local is_show_free = ServerActivityWGData.Instance:CanGetEveryDayRapidlyGiftFreeBox()
	self.node_list.rapidly_free_box_btn_remind:SetActive(is_show_free)
	self.node_list.rapidly_free_box_btn:SetActive(is_show_free)

	if is_show_free then
		if self.rapidly_daily_gift_box_tween then
			self.rapidly_daily_gift_box_tween:Restart()
		else
			if self.rapidly_daily_gift_box_tween then
				self.rapidly_daily_gift_box_tween:Kill()
				self.rapidly_daily_gift_box_tween = nil
			end

			self.rapidly_daily_gift_box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.rapidly_free_box_icon.transform, self.rapidly_daily_gift_box_tween, 1)
		end
	elseif self.rapidly_daily_gift_box_tween then
		self.rapidly_daily_gift_box_tween:Pause()
		self.node_list.rapidly_free_box_icon.transform.localRotation = Quaternion.identity
	end

	local can_get_sup = ServerActivityWGData.Instance:CanGetEveryDayRapidlyGiftSuperReward()
	self.node_list.rapidly_sup_reward_get_btn:SetActive(can_get_sup)
	self.node_list.rapidly_sup_reward_get_btn_remind:SetActive(can_get_sup)
end

function BillionSubsidyView:FlushRapidlyGiftList()
	local rapidly_cfg = ServerActivityWGData.Instance:GetRapidlyGiftCfg()
	if not rapidly_cfg then
		return
	end

	for key, value in pairs(self.rapidly_gift_list) do
		if rapidly_cfg[key - 1] then
			value:SetIndex(key - 1)
			value:SetData(rapidly_cfg[key - 1])
		end
	end

	local reward_list = ServerActivityWGData.Instance:GetLeiChongOtherCfg("gift_item")

	if not IsEmptyTable(reward_list) then
		self.rapidly_reward_list:SetDataList(reward_list)
		-- self.rapidly_reward_list:SetRefreshCallback(function(item_cell, cell_index)
		-- 	if item_cell then
		-- 		local gift_info = ServerActivityWGData.Instance:GetEveryDayRapidlyGiftInfo()
		-- 		if not IsEmptyTable(gift_info) and gift_info.super_reward_flag then
		-- 			item_cell:SetLingQuVisible(gift_info.super_reward_flag == 1)
		-- 		end
		-- 	end
		-- end)
	end
end

function BillionSubsidyView:OnClickRapidlyBuyBtn()
	if ServerActivityWGData.Instance:CheckCanGetEveryDayRapidlyGiftDay() == 0 then
		local one_key_cfg = ServerActivityWGData.Instance:GetRapidlyGiftOneKey(1)
		if not one_key_cfg then
			return
		end

		RechargeWGCtrl.Instance:Recharge(one_key_cfg.price, one_key_cfg.rmb_type, one_key_cfg.rmb_seq)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.EverydayRecharge.YGM)
	end
end

function BillionSubsidyView:OnClickRapidlyBuyAllBtn()
	local one_key_cfg = ServerActivityWGData.Instance:GetRapidlyGiftOneKey(0)
	if not one_key_cfg then
		return
	end

	RechargeWGCtrl.Instance:Recharge(one_key_cfg.price, one_key_cfg.rmb_type, one_key_cfg.rmb_seq)
end

function BillionSubsidyView:OnClickRapidlyFreeBoxBtn()
	ServerActivityWGCtrl.Instance:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_GET_LEIJI_CHONGZHI_FREE_REWARD, 0)
end

function BillionSubsidyView:OnClickRapidlySupRewardGetBtn()
	ServerActivityWGCtrl.Instance:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_GET_LEIJI_CHONGZHI_FREE_REWARD, 1)
end

function BillionSubsidyView:OnClickRapidlyOpenRecordViewBtn()
	ServerActivityWGCtrl.Instance:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_GET_RAPIDLY_REWARD_RECORD_INFO)
	ServerActivityWGCtrl.Instance:OpenEveryDayRapidlyGiftRecordTip()
end

function BillionSubsidyView:OnClickRapidlyJumpViewBtn()
	local one_key_cfg = ServerActivityWGData.Instance:GetRapidlyGiftOneKey(1)
	if not one_key_cfg then
		return
	end

	FunOpen.Instance:OpenViewNameByCfg(one_key_cfg.go_param)
end

---------------------------------------BillionRapidlyGiftItem----------------------------------
BillionRapidlyGiftItem = BillionRapidlyGiftItem or BaseClass(BaseRender)

function BillionRapidlyGiftItem:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.list_cell)
		self.reward_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.icon, BindTool.Bind(self.OnClickOpenItemTips, self))
	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuyBtn, self))
end

function BillionRapidlyGiftItem:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function BillionRapidlyGiftItem:OnFlush()
	if not self.data then
		return
	end

	self.node_list.name.text.text = self.data.item_name
	self.node_list.pro_get_text.text.text = self.data.describe
	local old_price = RoleWGData.GetPayMoneyStr(self.data.dummy_price)
	self.node_list.old_price.text.text = string.format( Language.EverydayRecharge.OldSpend2, old_price)

	local is_have = ServerActivityWGData.Instance:CheckCanGetEveryDayRapidlyGiftDay() > 0
	local can_buy = ServerActivityWGData.Instance:CanBuyEveryDayRapidlyGiftById(self.index)

	self.node_list.have_flag:SetActive(not can_buy)
	self.node_list.buy_btn:SetActive(can_buy)

	if can_buy then
		local str = ""
		if is_have then
			str = Language.EverydayRecharge.KLQ
		else
			str = RoleWGData.GetPayMoneyStr(self.data.price)
		end
		self.node_list.buy_btn_text.text.text = str
	end

	if not IsEmptyTable(self.data.reward_item) then
		self.reward_list:SetDataList(self.data.reward_item)
		self.reward_list:SetRefreshCallback(function(item_cell, cell_index)
			if item_cell then
				item_cell:SetLingQuVisible(not can_buy)
			end
		end)
	end
end

function BillionRapidlyGiftItem:OnClickOpenItemTips()
	local data_list =
	{
		view_type = RewardShowViewType.Normal,
		reward_item_list = self.data.reward_preview_item
	}
	RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
end

function BillionRapidlyGiftItem:OnClickBuyBtn()
	local is_have = ServerActivityWGData.Instance:CheckCanGetEveryDayRapidlyGiftDay() > 0
	local can_buy = ServerActivityWGData.Instance:CanBuyEveryDayRapidlyGiftById(self.index)
	if can_buy then
		if is_have then
			ServerActivityWGCtrl.Instance:SendEveryDayRecharge(
				EVERYDAY_RECHARGE_TYPR.OPER_TYPE_GET_LEIJI_CHONGZHI_RAPIDLY_REWARD, self.index)
		else
			RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.EverydayRecharge.YGM)
	end
end

---------------------------------------BillionRapidlyGiftItem----------------------------------
