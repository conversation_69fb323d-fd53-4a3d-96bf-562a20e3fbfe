------------------------------------------------------------
--师傅来信
------------------------------------------------------------
MasterEnvelopeView = MasterEnvelopeView or BaseClass(SafeBaseView)

function MasterEnvelopeView:__init()
	self:SetMaskBg(true, true, false, BindTool.Bind(self.OnClickEnvelope, self))
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "master_envelope")
	self.view_cache_time = 0
	self.is_read = false
	-- self.open_tween = nil
	-- self.close_tween = nil
end

function MasterEnvelopeView:__delete()

end

function MasterEnvelopeView:ReleaseCallBack()
	self:CancelQuest()
	if self.model then
		self.model:DeleteMe()
		self.model = nil
	end
end

function MasterEnvelopeView:CloseCallBack()
	TaskWGCtrl.Instance:UpdateTaskPanelShow()
end

function MasterEnvelopeView:LoadCallBack()

end

function MasterEnvelopeView:OnFlush(params)
	for k,v in pairs(params) do
		if v.ui_param then
			self.node_list.envelope_dec1:SetActive(v.ui_param == 1)
			self.node_list.envelope_dec2:SetActive(v.ui_param == 2)
			self.node_list.bag:SetActive(v.ui_param == 1)
			if v.ui_param == 1 and not self.model then
				self.model= RoleModel.New()
				local display_data = {
					parent_node = self.node_list["bag"],
					camera_type = MODEL_CAMERA_TYPE.BASE,
					-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
					rt_scale_type = ModelRTSCaleType.M,
					can_drag = true,
				}

				self.model:SetRenderTexUI3DModel(display_data)
				self.model:SetMainAsset(ResPath.GetNpcModel(2004001))
				self.model:CustomDisplayPositionAndRotation(Vector3(0.4, 0, 0), Quaternion.Euler(0, 192, 0), Vector3(4, 4, 4))
			end
		end
	end
end

function MasterEnvelopeView:ShowIndexCallBack()
	TaskWGCtrl.Instance:UpdateTaskPanelShow()
	if not self.next_handle_timer then
		self.node_list.fly_btn_zhuanzhi:SetActive(false)
		self.node_list.btn_zhuanzhi:SetActive(true)
		self.is_read = false
		self.is_closing  = false
		self.next_handle_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self.next_handle_timer = nil
			self:OnClickEnvelope()
		end, 5)
	end
end

function MasterEnvelopeView:OnClickEnvelope()
	if self.is_closing then
		return
	end
	self:CancelQuest()
	if self.is_read then
		self.is_closing  = true
		self.node_list.panel.transform:DOScale(Vector3(0.8, 0.8, 0.8), 0.5)
		self.node_list.master.transform:DOLocalMove(Vector2(79, 27), 0.5)
		self.node_list.envelope.transform:DOLocalMove(Vector2(-1, -21), 0.5)
		self.node_list.envelope_paper.canvas_group:DoAlpha(1, 0, 0.5)
		self.node_list.envelope_dec.canvas_group:DoAlpha(1, 0, 0.5)
		if self.node_list.btn_zhuanzhi:GetActive() and ViewManager.Instance:IsOpen(GuideModuleName.MainUIView) then
			self.node_list.fly_btn_zhuanzhi:SetActive(true)
			self.node_list.btn_zhuanzhi:SetActive(false)
			local mainui_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
			local task_view = mainui_view:GetOneTask(GameEnum.TASK_TYPE_ZHUAN) or mainui_view:GetOneTask(GameEnum.TASK_TYPE_ZHU)
			if task_view then
				self.node_list.fly_btn_zhuanzhi.transform:DOMove(task_view.transform.position, 0.5)
			end
		end
		self.next_handle_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self.next_handle_timer = nil
			self:Close()
		end, 0.5)
	else
		self.is_read = true
		self.node_list.master.transform:DOLocalMove(Vector2(250, 27), 0.5)
		self.node_list.envelope.transform:DOLocalMove(Vector2(-88, -21), 0.5)
		self.node_list.envelope_paper.canvas_group:DoAlpha(0, 1, 0.5)
		self.node_list.envelope_dec.canvas_group:DoAlpha(0, 1, 0.5)
		self.next_handle_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self.next_handle_timer = nil
			self:OnClickEnvelope()
		end, 10)
	end
end

function MasterEnvelopeView:CancelQuest()
	if self.next_handle_timer then
		GlobalTimerQuest:CancelQuest(self.next_handle_timer)
		self.next_handle_timer = nil
	end
end