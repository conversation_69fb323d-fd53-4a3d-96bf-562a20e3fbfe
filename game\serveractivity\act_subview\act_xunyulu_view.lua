ActXunYuLuView = ActXunYuLuView or BaseClass(SafeBaseView)

function ActXunYuLuView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_xunyulu_reward")
end

function ActXunYuLuView:__delete()

end

function ActXunYuLuView:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function ActXunYuLuView:LoadCallBack()
	-- self:FlushView()
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_OPEN_SERVER_XUNYULU, XUNYULU_OPERA_TYPE.YPE_REQ_INFO)
end

function ActXunYuLuView:OnFlush(param_t, index)
	self:FlushView()
end

function ActXunYuLuView:FlushView()
	if nil == self.reward_list_view then
		self.reward_list_view = AsyncListView.New(XunYuLuItemRender, self.node_list["ph_item_list"])
	end

	if self.reward_list_view then
		local data_list = ServerActivityWGData.Instance:GetXunYuLuCfgList()
		if data_list ~= nil then
			self.reward_list_view:SetDataList(data_list)
		end
	end
end


XunYuLuItemRender = XunYuLuItemRender or BaseClass(BaseRender)
function XunYuLuItemRender:__init()
	self.node_list.btn_lq.button:AddClickListener(BindTool.Bind(self.OnBtnLingQuClickHandler, self))
	self.node_list.head_bg.button:AddClickListener(BindTool.Bind(self.OnBtnHeadBgClickHandler, self))	
end

function XunYuLuItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function XunYuLuItemRender:LoadCallBack()
end

function XunYuLuItemRender:OnFlush()
	if not self.data then 
		return 
	end
	
	local data = self.data.cfg
	self.node_list.btn_lq:SetActive(false)
	self.node_list.btn_ylq:SetActive(false)
	self.node_list.btn_wdc:SetActive(false)

	self.node_list.task_name.text.text = data.name
	self.node_list.desc.text.text = data.des


	local bundle, asset = ResPath.GetBiZuo(data.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset)

	if self.cell == nil then
		self.cell = ItemCell.New(self.node_list["reward"])
	end

	if data.reward_item then
		self.cell:SetData(data.reward_item[0])
	end

	local info = ServerActivityWGData.Instance:GetXunYuLuTaskInfo(data.id)
	if info then
		local color = info.times >= data.join_times and "#009621" or "#ff0000"
		self.node_list.jindu_txt.text.text = ToColorStr(string.format("(%s/%s)", info.times, data.join_times), color)

		if info.status == ActivityRewardState2.WLQ and info.times >= data.join_times then
			self.node_list.btn_lq:SetActive(true)
		elseif info.status == ActivityRewardState2.YLQ then
			self.node_list.btn_ylq:SetActive(true)
		else
			self.node_list.btn_wdc:SetActive(true)
		end
	end
end

function XunYuLuItemRender:OnBtnLingQuClickHandler()
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_OPEN_SERVER_XUNYULU, XUNYULU_OPERA_TYPE.TYPE_REWARD, self.data.cfg.id)
end

function XunYuLuItemRender:OnBtnHeadBgClickHandler()
	local data = BiZuoWGData.Instance:GetActData(self.data.cfg.activity_id)
	if data then
		BiZuoWGCtrl.Instance:TaskInfoViewShow(data)
	end
end